// lib: , url: package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart

// class id: 1049393, size: 0x8
class :: {
}

// class id: 4568, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeProductSkusScreen extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1360348, size: 0x64
    // 0x1360348: EnterFrame
    //     0x1360348: stp             fp, lr, [SP, #-0x10]!
    //     0x136034c: mov             fp, SP
    // 0x1360350: AllocStack(0x18)
    //     0x1360350: sub             SP, SP, #0x18
    // 0x1360354: SetupParameters(ExchangeProductSkusScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1360354: stur            x1, [fp, #-8]
    //     0x1360358: stur            x2, [fp, #-0x10]
    // 0x136035c: r1 = 2
    //     0x136035c: movz            x1, #0x2
    // 0x1360360: r0 = AllocateContext()
    //     0x1360360: bl              #0x16f6108  ; AllocateContextStub
    // 0x1360364: mov             x1, x0
    // 0x1360368: ldur            x0, [fp, #-8]
    // 0x136036c: stur            x1, [fp, #-0x18]
    // 0x1360370: StoreField: r1->field_f = r0
    //     0x1360370: stur            w0, [x1, #0xf]
    // 0x1360374: ldur            x0, [fp, #-0x10]
    // 0x1360378: StoreField: r1->field_13 = r0
    //     0x1360378: stur            w0, [x1, #0x13]
    // 0x136037c: r0 = Obx()
    //     0x136037c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1360380: ldur            x2, [fp, #-0x18]
    // 0x1360384: r1 = Function '<anonymous closure>':.
    //     0x1360384: add             x1, PP, #0x40, lsl #12  ; [pp+0x40790] AnonymousClosure: (0x13603ac), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::bottomNavigationBar (0x1360348)
    //     0x1360388: ldr             x1, [x1, #0x790]
    // 0x136038c: stur            x0, [fp, #-8]
    // 0x1360390: r0 = AllocateClosure()
    //     0x1360390: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360394: mov             x1, x0
    // 0x1360398: ldur            x0, [fp, #-8]
    // 0x136039c: StoreField: r0->field_b = r1
    //     0x136039c: stur            w1, [x0, #0xb]
    // 0x13603a0: LeaveFrame
    //     0x13603a0: mov             SP, fp
    //     0x13603a4: ldp             fp, lr, [SP], #0x10
    // 0x13603a8: ret
    //     0x13603a8: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x13603ac, size: 0x2d0
    // 0x13603ac: EnterFrame
    //     0x13603ac: stp             fp, lr, [SP, #-0x10]!
    //     0x13603b0: mov             fp, SP
    // 0x13603b4: AllocStack(0x40)
    //     0x13603b4: sub             SP, SP, #0x40
    // 0x13603b8: SetupParameters()
    //     0x13603b8: ldr             x0, [fp, #0x10]
    //     0x13603bc: ldur            w2, [x0, #0x17]
    //     0x13603c0: add             x2, x2, HEAP, lsl #32
    //     0x13603c4: stur            x2, [fp, #-8]
    // 0x13603c8: CheckStackOverflow
    //     0x13603c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13603cc: cmp             SP, x16
    //     0x13603d0: b.ls            #0x1360674
    // 0x13603d4: r16 = <EdgeInsets>
    //     0x13603d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13603d8: ldr             x16, [x16, #0xda0]
    // 0x13603dc: r30 = Instance_EdgeInsets
    //     0x13603dc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13603e0: ldr             lr, [lr, #0x1f0]
    // 0x13603e4: stp             lr, x16, [SP]
    // 0x13603e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13603e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13603ec: r0 = all()
    //     0x13603ec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13603f0: ldur            x2, [fp, #-8]
    // 0x13603f4: stur            x0, [fp, #-0x10]
    // 0x13603f8: LoadField: r1 = r2->field_f
    //     0x13603f8: ldur            w1, [x2, #0xf]
    // 0x13603fc: DecompressPointer r1
    //     0x13603fc: add             x1, x1, HEAP, lsl #32
    // 0x1360400: r0 = controller()
    //     0x1360400: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360404: LoadField: r1 = r0->field_5b
    //     0x1360404: ldur            w1, [x0, #0x5b]
    // 0x1360408: DecompressPointer r1
    //     0x1360408: add             x1, x1, HEAP, lsl #32
    // 0x136040c: r0 = RxStringExt.isNotEmpty()
    //     0x136040c: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1360410: tbnz            w0, #4, #0x1360434
    // 0x1360414: ldur            x2, [fp, #-8]
    // 0x1360418: LoadField: r1 = r2->field_13
    //     0x1360418: ldur            w1, [x2, #0x13]
    // 0x136041c: DecompressPointer r1
    //     0x136041c: add             x1, x1, HEAP, lsl #32
    // 0x1360420: r0 = of()
    //     0x1360420: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1360424: LoadField: r1 = r0->field_5b
    //     0x1360424: ldur            w1, [x0, #0x5b]
    // 0x1360428: DecompressPointer r1
    //     0x1360428: add             x1, x1, HEAP, lsl #32
    // 0x136042c: mov             x0, x1
    // 0x1360430: b               #0x1360440
    // 0x1360434: r1 = Instance_Color
    //     0x1360434: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1360438: d0 = 0.400000
    //     0x1360438: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x136043c: r0 = withOpacity()
    //     0x136043c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1360440: ldur            x2, [fp, #-8]
    // 0x1360444: r16 = <Color>
    //     0x1360444: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1360448: ldr             x16, [x16, #0xf80]
    // 0x136044c: stp             x0, x16, [SP]
    // 0x1360450: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1360450: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1360454: r0 = all()
    //     0x1360454: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1360458: ldur            x2, [fp, #-8]
    // 0x136045c: stur            x0, [fp, #-0x18]
    // 0x1360460: LoadField: r1 = r2->field_f
    //     0x1360460: ldur            w1, [x2, #0xf]
    // 0x1360464: DecompressPointer r1
    //     0x1360464: add             x1, x1, HEAP, lsl #32
    // 0x1360468: r0 = controller()
    //     0x1360468: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136046c: LoadField: r1 = r0->field_5b
    //     0x136046c: ldur            w1, [x0, #0x5b]
    // 0x1360470: DecompressPointer r1
    //     0x1360470: add             x1, x1, HEAP, lsl #32
    // 0x1360474: r0 = RxStringExt.isNotEmpty()
    //     0x1360474: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1360478: tbnz            w0, #4, #0x136049c
    // 0x136047c: ldur            x2, [fp, #-8]
    // 0x1360480: LoadField: r1 = r2->field_13
    //     0x1360480: ldur            w1, [x2, #0x13]
    // 0x1360484: DecompressPointer r1
    //     0x1360484: add             x1, x1, HEAP, lsl #32
    // 0x1360488: r0 = of()
    //     0x1360488: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x136048c: LoadField: r1 = r0->field_5b
    //     0x136048c: ldur            w1, [x0, #0x5b]
    // 0x1360490: DecompressPointer r1
    //     0x1360490: add             x1, x1, HEAP, lsl #32
    // 0x1360494: mov             x3, x1
    // 0x1360498: b               #0x13604ac
    // 0x136049c: r1 = Instance_Color
    //     0x136049c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13604a0: d0 = 0.400000
    //     0x13604a0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x13604a4: r0 = withOpacity()
    //     0x13604a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13604a8: mov             x3, x0
    // 0x13604ac: ldur            x2, [fp, #-8]
    // 0x13604b0: ldur            x1, [fp, #-0x10]
    // 0x13604b4: ldur            x0, [fp, #-0x18]
    // 0x13604b8: r16 = <Color>
    //     0x13604b8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13604bc: ldr             x16, [x16, #0xf80]
    // 0x13604c0: stp             x3, x16, [SP]
    // 0x13604c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13604c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13604c8: r0 = all()
    //     0x13604c8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13604cc: stur            x0, [fp, #-0x20]
    // 0x13604d0: r16 = <RoundedRectangleBorder>
    //     0x13604d0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13604d4: ldr             x16, [x16, #0xf78]
    // 0x13604d8: r30 = Instance_RoundedRectangleBorder
    //     0x13604d8: add             lr, PP, #0x40, lsl #12  ; [pp+0x406d0] Obj!RoundedRectangleBorder@d5acc1
    //     0x13604dc: ldr             lr, [lr, #0x6d0]
    // 0x13604e0: stp             lr, x16, [SP]
    // 0x13604e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13604e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13604e8: r0 = all()
    //     0x13604e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13604ec: stur            x0, [fp, #-0x28]
    // 0x13604f0: r0 = ButtonStyle()
    //     0x13604f0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13604f4: mov             x1, x0
    // 0x13604f8: ldur            x0, [fp, #-0x20]
    // 0x13604fc: stur            x1, [fp, #-0x30]
    // 0x1360500: StoreField: r1->field_b = r0
    //     0x1360500: stur            w0, [x1, #0xb]
    // 0x1360504: ldur            x0, [fp, #-0x18]
    // 0x1360508: StoreField: r1->field_f = r0
    //     0x1360508: stur            w0, [x1, #0xf]
    // 0x136050c: ldur            x0, [fp, #-0x10]
    // 0x1360510: StoreField: r1->field_23 = r0
    //     0x1360510: stur            w0, [x1, #0x23]
    // 0x1360514: ldur            x0, [fp, #-0x28]
    // 0x1360518: StoreField: r1->field_43 = r0
    //     0x1360518: stur            w0, [x1, #0x43]
    // 0x136051c: r0 = TextButtonThemeData()
    //     0x136051c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1360520: mov             x2, x0
    // 0x1360524: ldur            x0, [fp, #-0x30]
    // 0x1360528: stur            x2, [fp, #-0x10]
    // 0x136052c: StoreField: r2->field_7 = r0
    //     0x136052c: stur            w0, [x2, #7]
    // 0x1360530: ldur            x0, [fp, #-8]
    // 0x1360534: LoadField: r1 = r0->field_f
    //     0x1360534: ldur            w1, [x0, #0xf]
    // 0x1360538: DecompressPointer r1
    //     0x1360538: add             x1, x1, HEAP, lsl #32
    // 0x136053c: r0 = controller()
    //     0x136053c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360540: LoadField: r1 = r0->field_5b
    //     0x1360540: ldur            w1, [x0, #0x5b]
    // 0x1360544: DecompressPointer r1
    //     0x1360544: add             x1, x1, HEAP, lsl #32
    // 0x1360548: r0 = RxStringExt.isNotEmpty()
    //     0x1360548: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x136054c: tbnz            w0, #4, #0x1360568
    // 0x1360550: ldur            x2, [fp, #-8]
    // 0x1360554: r1 = Function '<anonymous closure>':.
    //     0x1360554: add             x1, PP, #0x40, lsl #12  ; [pp+0x40798] AnonymousClosure: (0x136067c), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::bottomNavigationBar (0x1360348)
    //     0x1360558: ldr             x1, [x1, #0x798]
    // 0x136055c: r0 = AllocateClosure()
    //     0x136055c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360560: mov             x2, x0
    // 0x1360564: b               #0x136056c
    // 0x1360568: r2 = Null
    //     0x1360568: mov             x2, NULL
    // 0x136056c: ldur            x1, [fp, #-8]
    // 0x1360570: ldur            x0, [fp, #-0x10]
    // 0x1360574: stur            x2, [fp, #-0x18]
    // 0x1360578: LoadField: r3 = r1->field_13
    //     0x1360578: ldur            w3, [x1, #0x13]
    // 0x136057c: DecompressPointer r3
    //     0x136057c: add             x3, x3, HEAP, lsl #32
    // 0x1360580: mov             x1, x3
    // 0x1360584: r0 = of()
    //     0x1360584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1360588: LoadField: r1 = r0->field_87
    //     0x1360588: ldur            w1, [x0, #0x87]
    // 0x136058c: DecompressPointer r1
    //     0x136058c: add             x1, x1, HEAP, lsl #32
    // 0x1360590: LoadField: r0 = r1->field_2b
    //     0x1360590: ldur            w0, [x1, #0x2b]
    // 0x1360594: DecompressPointer r0
    //     0x1360594: add             x0, x0, HEAP, lsl #32
    // 0x1360598: r16 = Instance_Color
    //     0x1360598: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x136059c: r30 = 16.000000
    //     0x136059c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x13605a0: ldr             lr, [lr, #0x188]
    // 0x13605a4: stp             lr, x16, [SP]
    // 0x13605a8: mov             x1, x0
    // 0x13605ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13605ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13605b0: ldr             x4, [x4, #0x9b8]
    // 0x13605b4: r0 = copyWith()
    //     0x13605b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13605b8: stur            x0, [fp, #-8]
    // 0x13605bc: r0 = Text()
    //     0x13605bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13605c0: mov             x1, x0
    // 0x13605c4: r0 = "Next"
    //     0x13605c4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x13605c8: ldr             x0, [x0, #0x3e8]
    // 0x13605cc: stur            x1, [fp, #-0x20]
    // 0x13605d0: StoreField: r1->field_b = r0
    //     0x13605d0: stur            w0, [x1, #0xb]
    // 0x13605d4: ldur            x0, [fp, #-8]
    // 0x13605d8: StoreField: r1->field_13 = r0
    //     0x13605d8: stur            w0, [x1, #0x13]
    // 0x13605dc: r0 = TextButton()
    //     0x13605dc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13605e0: mov             x1, x0
    // 0x13605e4: ldur            x0, [fp, #-0x18]
    // 0x13605e8: stur            x1, [fp, #-8]
    // 0x13605ec: StoreField: r1->field_b = r0
    //     0x13605ec: stur            w0, [x1, #0xb]
    // 0x13605f0: r0 = false
    //     0x13605f0: add             x0, NULL, #0x30  ; false
    // 0x13605f4: StoreField: r1->field_27 = r0
    //     0x13605f4: stur            w0, [x1, #0x27]
    // 0x13605f8: r0 = true
    //     0x13605f8: add             x0, NULL, #0x20  ; true
    // 0x13605fc: StoreField: r1->field_2f = r0
    //     0x13605fc: stur            w0, [x1, #0x2f]
    // 0x1360600: ldur            x0, [fp, #-0x20]
    // 0x1360604: StoreField: r1->field_37 = r0
    //     0x1360604: stur            w0, [x1, #0x37]
    // 0x1360608: r0 = TextButtonTheme()
    //     0x1360608: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x136060c: mov             x1, x0
    // 0x1360610: ldur            x0, [fp, #-0x10]
    // 0x1360614: stur            x1, [fp, #-0x18]
    // 0x1360618: StoreField: r1->field_f = r0
    //     0x1360618: stur            w0, [x1, #0xf]
    // 0x136061c: ldur            x0, [fp, #-8]
    // 0x1360620: StoreField: r1->field_b = r0
    //     0x1360620: stur            w0, [x1, #0xb]
    // 0x1360624: r0 = SizedBox()
    //     0x1360624: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1360628: mov             x1, x0
    // 0x136062c: r0 = inf
    //     0x136062c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1360630: ldr             x0, [x0, #0x9f8]
    // 0x1360634: stur            x1, [fp, #-8]
    // 0x1360638: StoreField: r1->field_f = r0
    //     0x1360638: stur            w0, [x1, #0xf]
    // 0x136063c: r0 = 48.000000
    //     0x136063c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x1360640: ldr             x0, [x0, #0xad8]
    // 0x1360644: StoreField: r1->field_13 = r0
    //     0x1360644: stur            w0, [x1, #0x13]
    // 0x1360648: ldur            x0, [fp, #-0x18]
    // 0x136064c: StoreField: r1->field_b = r0
    //     0x136064c: stur            w0, [x1, #0xb]
    // 0x1360650: r0 = Padding()
    //     0x1360650: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1360654: r1 = Instance_EdgeInsets
    //     0x1360654: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1360658: ldr             x1, [x1, #0x1f0]
    // 0x136065c: StoreField: r0->field_f = r1
    //     0x136065c: stur            w1, [x0, #0xf]
    // 0x1360660: ldur            x1, [fp, #-8]
    // 0x1360664: StoreField: r0->field_b = r1
    //     0x1360664: stur            w1, [x0, #0xb]
    // 0x1360668: LeaveFrame
    //     0x1360668: mov             SP, fp
    //     0x136066c: ldp             fp, lr, [SP], #0x10
    // 0x1360670: ret
    //     0x1360670: ret             
    // 0x1360674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1360674: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1360678: b               #0x13603d4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x136067c, size: 0x308
    // 0x136067c: EnterFrame
    //     0x136067c: stp             fp, lr, [SP, #-0x10]!
    //     0x1360680: mov             fp, SP
    // 0x1360684: AllocStack(0x28)
    //     0x1360684: sub             SP, SP, #0x28
    // 0x1360688: SetupParameters()
    //     0x1360688: ldr             x0, [fp, #0x10]
    //     0x136068c: ldur            w2, [x0, #0x17]
    //     0x1360690: add             x2, x2, HEAP, lsl #32
    //     0x1360694: stur            x2, [fp, #-8]
    // 0x1360698: CheckStackOverflow
    //     0x1360698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x136069c: cmp             SP, x16
    //     0x13606a0: b.ls            #0x136097c
    // 0x13606a4: LoadField: r1 = r2->field_f
    //     0x13606a4: ldur            w1, [x2, #0xf]
    // 0x13606a8: DecompressPointer r1
    //     0x13606a8: add             x1, x1, HEAP, lsl #32
    // 0x13606ac: r0 = controller()
    //     0x13606ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13606b0: mov             x1, x0
    // 0x13606b4: r2 = "Next"
    //     0x13606b4: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x13606b8: ldr             x2, [x2, #0x3e8]
    // 0x13606bc: r3 = "return_exchange_next"
    //     0x13606bc: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f60] "return_exchange_next"
    //     0x13606c0: ldr             x3, [x3, #0xf60]
    // 0x13606c4: r0 = ctaPostEvent()
    //     0x13606c4: bl              #0x1318484  ; [package:customer_app/app/presentation/controllers/exchange/exchange_same_product_controller.dart] ExchangeSameProductController::ctaPostEvent
    // 0x13606c8: ldur            x0, [fp, #-8]
    // 0x13606cc: LoadField: r1 = r0->field_f
    //     0x13606cc: ldur            w1, [x0, #0xf]
    // 0x13606d0: DecompressPointer r1
    //     0x13606d0: add             x1, x1, HEAP, lsl #32
    // 0x13606d4: r0 = controller()
    //     0x13606d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13606d8: LoadField: r1 = r0->field_4b
    //     0x13606d8: ldur            w1, [x0, #0x4b]
    // 0x13606dc: DecompressPointer r1
    //     0x13606dc: add             x1, x1, HEAP, lsl #32
    // 0x13606e0: r0 = value()
    //     0x13606e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13606e4: LoadField: r1 = r0->field_7
    //     0x13606e4: ldur            w1, [x0, #7]
    // 0x13606e8: DecompressPointer r1
    //     0x13606e8: add             x1, x1, HEAP, lsl #32
    // 0x13606ec: cmp             w1, #0x190
    // 0x13606f0: b.ne            #0x13608f8
    // 0x13606f4: ldur            x0, [fp, #-8]
    // 0x13606f8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13606f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13606fc: ldr             x0, [x0, #0x1c80]
    //     0x1360700: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1360704: cmp             w0, w16
    //     0x1360708: b.ne            #0x1360714
    //     0x136070c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1360710: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1360714: r1 = Null
    //     0x1360714: mov             x1, NULL
    // 0x1360718: r2 = 20
    //     0x1360718: movz            x2, #0x14
    // 0x136071c: r0 = AllocateArray()
    //     0x136071c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1360720: stur            x0, [fp, #-0x10]
    // 0x1360724: r16 = "order_id"
    //     0x1360724: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x1360728: ldr             x16, [x16, #0xa38]
    // 0x136072c: StoreField: r0->field_f = r16
    //     0x136072c: stur            w16, [x0, #0xf]
    // 0x1360730: ldur            x2, [fp, #-8]
    // 0x1360734: LoadField: r1 = r2->field_f
    //     0x1360734: ldur            w1, [x2, #0xf]
    // 0x1360738: DecompressPointer r1
    //     0x1360738: add             x1, x1, HEAP, lsl #32
    // 0x136073c: r0 = controller()
    //     0x136073c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360740: LoadField: r1 = r0->field_6f
    //     0x1360740: ldur            w1, [x0, #0x6f]
    // 0x1360744: DecompressPointer r1
    //     0x1360744: add             x1, x1, HEAP, lsl #32
    // 0x1360748: r0 = value()
    //     0x1360748: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x136074c: ldur            x1, [fp, #-0x10]
    // 0x1360750: ArrayStore: r1[1] = r0  ; List_4
    //     0x1360750: add             x25, x1, #0x13
    //     0x1360754: str             w0, [x25]
    //     0x1360758: tbz             w0, #0, #0x1360774
    //     0x136075c: ldurb           w16, [x1, #-1]
    //     0x1360760: ldurb           w17, [x0, #-1]
    //     0x1360764: and             x16, x17, x16, lsr #2
    //     0x1360768: tst             x16, HEAP, lsr #32
    //     0x136076c: b.eq            #0x1360774
    //     0x1360770: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1360774: ldur            x0, [fp, #-0x10]
    // 0x1360778: r16 = "charge"
    //     0x1360778: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b28] "charge"
    //     0x136077c: ldr             x16, [x16, #0xb28]
    // 0x1360780: ArrayStore: r0[0] = r16  ; List_4
    //     0x1360780: stur            w16, [x0, #0x17]
    // 0x1360784: ldur            x2, [fp, #-8]
    // 0x1360788: LoadField: r1 = r2->field_f
    //     0x1360788: ldur            w1, [x2, #0xf]
    // 0x136078c: DecompressPointer r1
    //     0x136078c: add             x1, x1, HEAP, lsl #32
    // 0x1360790: r0 = controller()
    //     0x1360790: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360794: LoadField: r1 = r0->field_67
    //     0x1360794: ldur            w1, [x0, #0x67]
    // 0x1360798: DecompressPointer r1
    //     0x1360798: add             x1, x1, HEAP, lsl #32
    // 0x136079c: mov             x0, x1
    // 0x13607a0: ldur            x1, [fp, #-0x10]
    // 0x13607a4: ArrayStore: r1[3] = r0  ; List_4
    //     0x13607a4: add             x25, x1, #0x1b
    //     0x13607a8: str             w0, [x25]
    //     0x13607ac: tbz             w0, #0, #0x13607c8
    //     0x13607b0: ldurb           w16, [x1, #-1]
    //     0x13607b4: ldurb           w17, [x0, #-1]
    //     0x13607b8: and             x16, x17, x16, lsr #2
    //     0x13607bc: tst             x16, HEAP, lsr #32
    //     0x13607c0: b.eq            #0x13607c8
    //     0x13607c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13607c8: ldur            x0, [fp, #-0x10]
    // 0x13607cc: r16 = "type"
    //     0x13607cc: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x13607d0: StoreField: r0->field_1f = r16
    //     0x13607d0: stur            w16, [x0, #0x1f]
    // 0x13607d4: ldur            x2, [fp, #-8]
    // 0x13607d8: LoadField: r1 = r2->field_f
    //     0x13607d8: ldur            w1, [x2, #0xf]
    // 0x13607dc: DecompressPointer r1
    //     0x13607dc: add             x1, x1, HEAP, lsl #32
    // 0x13607e0: r0 = controller()
    //     0x13607e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13607e4: LoadField: r1 = r0->field_6b
    //     0x13607e4: ldur            w1, [x0, #0x6b]
    // 0x13607e8: DecompressPointer r1
    //     0x13607e8: add             x1, x1, HEAP, lsl #32
    // 0x13607ec: mov             x0, x1
    // 0x13607f0: ldur            x1, [fp, #-0x10]
    // 0x13607f4: ArrayStore: r1[5] = r0  ; List_4
    //     0x13607f4: add             x25, x1, #0x23
    //     0x13607f8: str             w0, [x25]
    //     0x13607fc: tbz             w0, #0, #0x1360818
    //     0x1360800: ldurb           w16, [x1, #-1]
    //     0x1360804: ldurb           w17, [x0, #-1]
    //     0x1360808: and             x16, x17, x16, lsr #2
    //     0x136080c: tst             x16, HEAP, lsr #32
    //     0x1360810: b.eq            #0x1360818
    //     0x1360814: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1360818: ldur            x0, [fp, #-0x10]
    // 0x136081c: r16 = "sku_short_id"
    //     0x136081c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a0] "sku_short_id"
    //     0x1360820: ldr             x16, [x16, #0x4a0]
    // 0x1360824: StoreField: r0->field_27 = r16
    //     0x1360824: stur            w16, [x0, #0x27]
    // 0x1360828: ldur            x2, [fp, #-8]
    // 0x136082c: LoadField: r1 = r2->field_f
    //     0x136082c: ldur            w1, [x2, #0xf]
    // 0x1360830: DecompressPointer r1
    //     0x1360830: add             x1, x1, HEAP, lsl #32
    // 0x1360834: r0 = controller()
    //     0x1360834: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360838: LoadField: r1 = r0->field_5b
    //     0x1360838: ldur            w1, [x0, #0x5b]
    // 0x136083c: DecompressPointer r1
    //     0x136083c: add             x1, x1, HEAP, lsl #32
    // 0x1360840: r0 = value()
    //     0x1360840: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360844: ldur            x1, [fp, #-0x10]
    // 0x1360848: ArrayStore: r1[7] = r0  ; List_4
    //     0x1360848: add             x25, x1, #0x2b
    //     0x136084c: str             w0, [x25]
    //     0x1360850: tbz             w0, #0, #0x136086c
    //     0x1360854: ldurb           w16, [x1, #-1]
    //     0x1360858: ldurb           w17, [x0, #-1]
    //     0x136085c: and             x16, x17, x16, lsr #2
    //     0x1360860: tst             x16, HEAP, lsr #32
    //     0x1360864: b.eq            #0x136086c
    //     0x1360868: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x136086c: ldur            x0, [fp, #-0x10]
    // 0x1360870: r16 = "customer_product_skus_id"
    //     0x1360870: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cd8] "customer_product_skus_id"
    //     0x1360874: ldr             x16, [x16, #0xcd8]
    // 0x1360878: StoreField: r0->field_2f = r16
    //     0x1360878: stur            w16, [x0, #0x2f]
    // 0x136087c: ldur            x1, [fp, #-8]
    // 0x1360880: LoadField: r2 = r1->field_f
    //     0x1360880: ldur            w2, [x1, #0xf]
    // 0x1360884: DecompressPointer r2
    //     0x1360884: add             x2, x2, HEAP, lsl #32
    // 0x1360888: mov             x1, x2
    // 0x136088c: r0 = controller()
    //     0x136088c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360890: LoadField: r1 = r0->field_5f
    //     0x1360890: ldur            w1, [x0, #0x5f]
    // 0x1360894: DecompressPointer r1
    //     0x1360894: add             x1, x1, HEAP, lsl #32
    // 0x1360898: mov             x0, x1
    // 0x136089c: ldur            x1, [fp, #-0x10]
    // 0x13608a0: ArrayStore: r1[9] = r0  ; List_4
    //     0x13608a0: add             x25, x1, #0x33
    //     0x13608a4: str             w0, [x25]
    //     0x13608a8: tbz             w0, #0, #0x13608c4
    //     0x13608ac: ldurb           w16, [x1, #-1]
    //     0x13608b0: ldurb           w17, [x0, #-1]
    //     0x13608b4: and             x16, x17, x16, lsr #2
    //     0x13608b8: tst             x16, HEAP, lsr #32
    //     0x13608bc: b.eq            #0x13608c4
    //     0x13608c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13608c4: r16 = <String, Object?>
    //     0x13608c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x13608c8: ldr             x16, [x16, #0xc28]
    // 0x13608cc: ldur            lr, [fp, #-0x10]
    // 0x13608d0: stp             lr, x16, [SP]
    // 0x13608d4: r0 = Map._fromLiteral()
    //     0x13608d4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13608d8: r16 = "/return-order"
    //     0x13608d8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x13608dc: ldr             x16, [x16, #0x8b8]
    // 0x13608e0: stp             x16, NULL, [SP, #8]
    // 0x13608e4: str             x0, [SP]
    // 0x13608e8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13608e8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13608ec: ldr             x4, [x4, #0x438]
    // 0x13608f0: r0 = GetNavigation.toNamed()
    //     0x13608f0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13608f4: b               #0x136096c
    // 0x13608f8: ldur            x1, [fp, #-8]
    // 0x13608fc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13608fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1360900: ldr             x0, [x0, #0x1c80]
    //     0x1360904: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1360908: cmp             w0, w16
    //     0x136090c: b.ne            #0x1360918
    //     0x1360910: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1360914: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1360918: ldur            x0, [fp, #-8]
    // 0x136091c: LoadField: r1 = r0->field_f
    //     0x136091c: ldur            w1, [x0, #0xf]
    // 0x1360920: DecompressPointer r1
    //     0x1360920: add             x1, x1, HEAP, lsl #32
    // 0x1360924: r0 = controller()
    //     0x1360924: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360928: LoadField: r1 = r0->field_6f
    //     0x1360928: ldur            w1, [x0, #0x6f]
    // 0x136092c: DecompressPointer r1
    //     0x136092c: add             x1, x1, HEAP, lsl #32
    // 0x1360930: r0 = value()
    //     0x1360930: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360934: cmp             w0, NULL
    // 0x1360938: b.ne            #0x1360940
    // 0x136093c: r0 = ""
    //     0x136093c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1360940: stur            x0, [fp, #-8]
    // 0x1360944: r0 = OrderItemModel()
    //     0x1360944: bl              #0x925de8  ; AllocateOrderItemModelStub -> OrderItemModel (size=0x14)
    // 0x1360948: mov             x1, x0
    // 0x136094c: ldur            x0, [fp, #-8]
    // 0x1360950: StoreField: r1->field_7 = r0
    //     0x1360950: stur            w0, [x1, #7]
    // 0x1360954: r0 = ""
    //     0x1360954: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1360958: StoreField: r1->field_b = r0
    //     0x1360958: stur            w0, [x1, #0xb]
    // 0x136095c: StoreField: r1->field_f = r0
    //     0x136095c: stur            w0, [x1, #0xf]
    // 0x1360960: stp             x1, NULL, [SP]
    // 0x1360964: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1360964: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1360968: r0 = GetNavigation.offAllNamed()
    //     0x1360968: bl              #0xbd1e0c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offAllNamed
    // 0x136096c: r0 = Null
    //     0x136096c: mov             x0, NULL
    // 0x1360970: LeaveFrame
    //     0x1360970: mov             SP, fp
    //     0x1360974: ldp             fp, lr, [SP], #0x10
    // 0x1360978: ret
    //     0x1360978: ret             
    // 0x136097c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x136097c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1360980: b               #0x13606a4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x149ca1c, size: 0x17c
    // 0x149ca1c: EnterFrame
    //     0x149ca1c: stp             fp, lr, [SP, #-0x10]!
    //     0x149ca20: mov             fp, SP
    // 0x149ca24: AllocStack(0x28)
    //     0x149ca24: sub             SP, SP, #0x28
    // 0x149ca28: SetupParameters()
    //     0x149ca28: ldr             x0, [fp, #0x10]
    //     0x149ca2c: ldur            w2, [x0, #0x17]
    //     0x149ca30: add             x2, x2, HEAP, lsl #32
    //     0x149ca34: stur            x2, [fp, #-8]
    // 0x149ca38: CheckStackOverflow
    //     0x149ca38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x149ca3c: cmp             SP, x16
    //     0x149ca40: b.ls            #0x149cb90
    // 0x149ca44: LoadField: r1 = r2->field_f
    //     0x149ca44: ldur            w1, [x2, #0xf]
    // 0x149ca48: DecompressPointer r1
    //     0x149ca48: add             x1, x1, HEAP, lsl #32
    // 0x149ca4c: r0 = controller()
    //     0x149ca4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149ca50: mov             x1, x0
    // 0x149ca54: r2 = "Exchange with new product"
    //     0x149ca54: add             x2, PP, #0x40, lsl #12  ; [pp+0x407e0] "Exchange with new product"
    //     0x149ca58: ldr             x2, [x2, #0x7e0]
    // 0x149ca5c: r3 = "return_exchange_with_different_product"
    //     0x149ca5c: add             x3, PP, #0x38, lsl #12  ; [pp+0x380f8] "return_exchange_with_different_product"
    //     0x149ca60: ldr             x3, [x3, #0xf8]
    // 0x149ca64: r0 = ctaPostEvent()
    //     0x149ca64: bl              #0x1318484  ; [package:customer_app/app/presentation/controllers/exchange/exchange_same_product_controller.dart] ExchangeSameProductController::ctaPostEvent
    // 0x149ca68: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x149ca68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x149ca6c: ldr             x0, [x0, #0x1c80]
    //     0x149ca70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x149ca74: cmp             w0, w16
    //     0x149ca78: b.ne            #0x149ca84
    //     0x149ca7c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x149ca80: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x149ca84: r1 = Null
    //     0x149ca84: mov             x1, NULL
    // 0x149ca88: r2 = 12
    //     0x149ca88: movz            x2, #0xc
    // 0x149ca8c: r0 = AllocateArray()
    //     0x149ca8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149ca90: stur            x0, [fp, #-0x10]
    // 0x149ca94: r16 = "order_id"
    //     0x149ca94: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x149ca98: ldr             x16, [x16, #0xa38]
    // 0x149ca9c: StoreField: r0->field_f = r16
    //     0x149ca9c: stur            w16, [x0, #0xf]
    // 0x149caa0: ldur            x2, [fp, #-8]
    // 0x149caa4: LoadField: r1 = r2->field_f
    //     0x149caa4: ldur            w1, [x2, #0xf]
    // 0x149caa8: DecompressPointer r1
    //     0x149caa8: add             x1, x1, HEAP, lsl #32
    // 0x149caac: r0 = controller()
    //     0x149caac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cab0: LoadField: r1 = r0->field_6f
    //     0x149cab0: ldur            w1, [x0, #0x6f]
    // 0x149cab4: DecompressPointer r1
    //     0x149cab4: add             x1, x1, HEAP, lsl #32
    // 0x149cab8: r0 = value()
    //     0x149cab8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149cabc: ldur            x1, [fp, #-0x10]
    // 0x149cac0: ArrayStore: r1[1] = r0  ; List_4
    //     0x149cac0: add             x25, x1, #0x13
    //     0x149cac4: str             w0, [x25]
    //     0x149cac8: tbz             w0, #0, #0x149cae4
    //     0x149cacc: ldurb           w16, [x1, #-1]
    //     0x149cad0: ldurb           w17, [x0, #-1]
    //     0x149cad4: and             x16, x17, x16, lsr #2
    //     0x149cad8: tst             x16, HEAP, lsr #32
    //     0x149cadc: b.eq            #0x149cae4
    //     0x149cae0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149cae4: ldur            x0, [fp, #-0x10]
    // 0x149cae8: r16 = "charge"
    //     0x149cae8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b28] "charge"
    //     0x149caec: ldr             x16, [x16, #0xb28]
    // 0x149caf0: ArrayStore: r0[0] = r16  ; List_4
    //     0x149caf0: stur            w16, [x0, #0x17]
    // 0x149caf4: ldur            x1, [fp, #-8]
    // 0x149caf8: LoadField: r2 = r1->field_f
    //     0x149caf8: ldur            w2, [x1, #0xf]
    // 0x149cafc: DecompressPointer r2
    //     0x149cafc: add             x2, x2, HEAP, lsl #32
    // 0x149cb00: mov             x1, x2
    // 0x149cb04: r0 = controller()
    //     0x149cb04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cb08: LoadField: r1 = r0->field_67
    //     0x149cb08: ldur            w1, [x0, #0x67]
    // 0x149cb0c: DecompressPointer r1
    //     0x149cb0c: add             x1, x1, HEAP, lsl #32
    // 0x149cb10: mov             x0, x1
    // 0x149cb14: ldur            x1, [fp, #-0x10]
    // 0x149cb18: ArrayStore: r1[3] = r0  ; List_4
    //     0x149cb18: add             x25, x1, #0x1b
    //     0x149cb1c: str             w0, [x25]
    //     0x149cb20: tbz             w0, #0, #0x149cb3c
    //     0x149cb24: ldurb           w16, [x1, #-1]
    //     0x149cb28: ldurb           w17, [x0, #-1]
    //     0x149cb2c: and             x16, x17, x16, lsr #2
    //     0x149cb30: tst             x16, HEAP, lsr #32
    //     0x149cb34: b.eq            #0x149cb3c
    //     0x149cb38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149cb3c: ldur            x0, [fp, #-0x10]
    // 0x149cb40: r16 = "type"
    //     0x149cb40: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x149cb44: StoreField: r0->field_1f = r16
    //     0x149cb44: stur            w16, [x0, #0x1f]
    // 0x149cb48: r16 = "replace-new"
    //     0x149cb48: add             x16, PP, #0x32, lsl #12  ; [pp+0x32ad8] "replace-new"
    //     0x149cb4c: ldr             x16, [x16, #0xad8]
    // 0x149cb50: StoreField: r0->field_23 = r16
    //     0x149cb50: stur            w16, [x0, #0x23]
    // 0x149cb54: r16 = <String, Object?>
    //     0x149cb54: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x149cb58: ldr             x16, [x16, #0xc28]
    // 0x149cb5c: stp             x0, x16, [SP]
    // 0x149cb60: r0 = Map._fromLiteral()
    //     0x149cb60: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x149cb64: r16 = "/return-order"
    //     0x149cb64: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x149cb68: ldr             x16, [x16, #0x8b8]
    // 0x149cb6c: stp             x16, NULL, [SP, #8]
    // 0x149cb70: str             x0, [SP]
    // 0x149cb74: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x149cb74: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x149cb78: ldr             x4, [x4, #0x438]
    // 0x149cb7c: r0 = GetNavigation.offAndToNamed()
    //     0x149cb7c: bl              #0x8f4af0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offAndToNamed
    // 0x149cb80: r0 = Null
    //     0x149cb80: mov             x0, NULL
    // 0x149cb84: LeaveFrame
    //     0x149cb84: mov             SP, fp
    //     0x149cb88: ldp             fp, lr, [SP], #0x10
    // 0x149cb8c: ret
    //     0x149cb8c: ret             
    // 0x149cb90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x149cb90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x149cb94: b               #0x149ca44
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x149cb98, size: 0x17a8
    // 0x149cb98: EnterFrame
    //     0x149cb98: stp             fp, lr, [SP, #-0x10]!
    //     0x149cb9c: mov             fp, SP
    // 0x149cba0: AllocStack(0xb0)
    //     0x149cba0: sub             SP, SP, #0xb0
    // 0x149cba4: SetupParameters()
    //     0x149cba4: ldr             x0, [fp, #0x10]
    //     0x149cba8: ldur            w2, [x0, #0x17]
    //     0x149cbac: add             x2, x2, HEAP, lsl #32
    //     0x149cbb0: stur            x2, [fp, #-8]
    // 0x149cbb4: CheckStackOverflow
    //     0x149cbb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x149cbb8: cmp             SP, x16
    //     0x149cbbc: b.ls            #0x149e30c
    // 0x149cbc0: r0 = Radius()
    //     0x149cbc0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x149cbc4: d0 = 20.000000
    //     0x149cbc4: fmov            d0, #20.00000000
    // 0x149cbc8: stur            x0, [fp, #-0x10]
    // 0x149cbcc: StoreField: r0->field_7 = d0
    //     0x149cbcc: stur            d0, [x0, #7]
    // 0x149cbd0: StoreField: r0->field_f = d0
    //     0x149cbd0: stur            d0, [x0, #0xf]
    // 0x149cbd4: r0 = BorderRadius()
    //     0x149cbd4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x149cbd8: mov             x3, x0
    // 0x149cbdc: ldur            x0, [fp, #-0x10]
    // 0x149cbe0: stur            x3, [fp, #-0x18]
    // 0x149cbe4: StoreField: r3->field_7 = r0
    //     0x149cbe4: stur            w0, [x3, #7]
    // 0x149cbe8: StoreField: r3->field_b = r0
    //     0x149cbe8: stur            w0, [x3, #0xb]
    // 0x149cbec: StoreField: r3->field_f = r0
    //     0x149cbec: stur            w0, [x3, #0xf]
    // 0x149cbf0: StoreField: r3->field_13 = r0
    //     0x149cbf0: stur            w0, [x3, #0x13]
    // 0x149cbf4: r1 = Null
    //     0x149cbf4: mov             x1, NULL
    // 0x149cbf8: r2 = Instance_Color
    //     0x149cbf8: ldr             x2, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x149cbfc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x149cbfc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x149cc00: r0 = Border.all()
    //     0x149cc00: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x149cc04: stur            x0, [fp, #-0x10]
    // 0x149cc08: r0 = BoxDecoration()
    //     0x149cc08: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x149cc0c: mov             x1, x0
    // 0x149cc10: r0 = Instance_Color
    //     0x149cc10: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x149cc14: stur            x1, [fp, #-0x20]
    // 0x149cc18: StoreField: r1->field_7 = r0
    //     0x149cc18: stur            w0, [x1, #7]
    // 0x149cc1c: ldur            x2, [fp, #-0x10]
    // 0x149cc20: StoreField: r1->field_f = r2
    //     0x149cc20: stur            w2, [x1, #0xf]
    // 0x149cc24: ldur            x2, [fp, #-0x18]
    // 0x149cc28: StoreField: r1->field_13 = r2
    //     0x149cc28: stur            w2, [x1, #0x13]
    // 0x149cc2c: r2 = Instance_BoxShape
    //     0x149cc2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x149cc30: ldr             x2, [x2, #0x80]
    // 0x149cc34: StoreField: r1->field_23 = r2
    //     0x149cc34: stur            w2, [x1, #0x23]
    // 0x149cc38: r0 = Radius()
    //     0x149cc38: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x149cc3c: d0 = 20.000000
    //     0x149cc3c: fmov            d0, #20.00000000
    // 0x149cc40: stur            x0, [fp, #-0x10]
    // 0x149cc44: StoreField: r0->field_7 = d0
    //     0x149cc44: stur            d0, [x0, #7]
    // 0x149cc48: StoreField: r0->field_f = d0
    //     0x149cc48: stur            d0, [x0, #0xf]
    // 0x149cc4c: r0 = BorderRadius()
    //     0x149cc4c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x149cc50: mov             x2, x0
    // 0x149cc54: ldur            x0, [fp, #-0x10]
    // 0x149cc58: stur            x2, [fp, #-0x18]
    // 0x149cc5c: StoreField: r2->field_7 = r0
    //     0x149cc5c: stur            w0, [x2, #7]
    // 0x149cc60: StoreField: r2->field_b = r0
    //     0x149cc60: stur            w0, [x2, #0xb]
    // 0x149cc64: StoreField: r2->field_f = r0
    //     0x149cc64: stur            w0, [x2, #0xf]
    // 0x149cc68: StoreField: r2->field_13 = r0
    //     0x149cc68: stur            w0, [x2, #0x13]
    // 0x149cc6c: ldur            x0, [fp, #-8]
    // 0x149cc70: LoadField: r1 = r0->field_f
    //     0x149cc70: ldur            w1, [x0, #0xf]
    // 0x149cc74: DecompressPointer r1
    //     0x149cc74: add             x1, x1, HEAP, lsl #32
    // 0x149cc78: r0 = controller()
    //     0x149cc78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cc7c: LoadField: r1 = r0->field_4b
    //     0x149cc7c: ldur            w1, [x0, #0x4b]
    // 0x149cc80: DecompressPointer r1
    //     0x149cc80: add             x1, x1, HEAP, lsl #32
    // 0x149cc84: r0 = value()
    //     0x149cc84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149cc88: LoadField: r1 = r0->field_b
    //     0x149cc88: ldur            w1, [x0, #0xb]
    // 0x149cc8c: DecompressPointer r1
    //     0x149cc8c: add             x1, x1, HEAP, lsl #32
    // 0x149cc90: cmp             w1, NULL
    // 0x149cc94: b.ne            #0x149cca0
    // 0x149cc98: r0 = Null
    //     0x149cc98: mov             x0, NULL
    // 0x149cc9c: b               #0x149ccc4
    // 0x149cca0: LoadField: r0 = r1->field_f
    //     0x149cca0: ldur            w0, [x1, #0xf]
    // 0x149cca4: DecompressPointer r0
    //     0x149cca4: add             x0, x0, HEAP, lsl #32
    // 0x149cca8: cmp             w0, NULL
    // 0x149ccac: b.ne            #0x149ccb8
    // 0x149ccb0: r0 = Null
    //     0x149ccb0: mov             x0, NULL
    // 0x149ccb4: b               #0x149ccc4
    // 0x149ccb8: LoadField: r1 = r0->field_7
    //     0x149ccb8: ldur            w1, [x0, #7]
    // 0x149ccbc: DecompressPointer r1
    //     0x149ccbc: add             x1, x1, HEAP, lsl #32
    // 0x149ccc0: mov             x0, x1
    // 0x149ccc4: cmp             w0, NULL
    // 0x149ccc8: b.ne            #0x149ccd4
    // 0x149cccc: r4 = ""
    //     0x149cccc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149ccd0: b               #0x149ccd8
    // 0x149ccd4: mov             x4, x0
    // 0x149ccd8: ldur            x3, [fp, #-8]
    // 0x149ccdc: ldur            x0, [fp, #-0x18]
    // 0x149cce0: stur            x4, [fp, #-0x10]
    // 0x149cce4: r1 = Function '<anonymous closure>':.
    //     0x149cce4: add             x1, PP, #0x40, lsl #12  ; [pp+0x407a8] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x149cce8: ldr             x1, [x1, #0x7a8]
    // 0x149ccec: r2 = Null
    //     0x149ccec: mov             x2, NULL
    // 0x149ccf0: r0 = AllocateClosure()
    //     0x149ccf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149ccf4: r1 = Function '<anonymous closure>':.
    //     0x149ccf4: add             x1, PP, #0x40, lsl #12  ; [pp+0x407b0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x149ccf8: ldr             x1, [x1, #0x7b0]
    // 0x149ccfc: r2 = Null
    //     0x149ccfc: mov             x2, NULL
    // 0x149cd00: stur            x0, [fp, #-0x28]
    // 0x149cd04: r0 = AllocateClosure()
    //     0x149cd04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149cd08: stur            x0, [fp, #-0x30]
    // 0x149cd0c: r0 = CachedNetworkImage()
    //     0x149cd0c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x149cd10: stur            x0, [fp, #-0x38]
    // 0x149cd14: r16 = 80.000000
    //     0x149cd14: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x149cd18: ldr             x16, [x16, #0x2f8]
    // 0x149cd1c: r30 = 80.000000
    //     0x149cd1c: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x149cd20: ldr             lr, [lr, #0x2f8]
    // 0x149cd24: stp             lr, x16, [SP, #0x18]
    // 0x149cd28: r16 = Instance_BoxFit
    //     0x149cd28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x149cd2c: ldr             x16, [x16, #0x118]
    // 0x149cd30: ldur            lr, [fp, #-0x28]
    // 0x149cd34: stp             lr, x16, [SP, #8]
    // 0x149cd38: ldur            x16, [fp, #-0x30]
    // 0x149cd3c: str             x16, [SP]
    // 0x149cd40: mov             x1, x0
    // 0x149cd44: ldur            x2, [fp, #-0x10]
    // 0x149cd48: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x149cd48: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x149cd4c: ldr             x4, [x4, #0xc28]
    // 0x149cd50: r0 = CachedNetworkImage()
    //     0x149cd50: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x149cd54: r0 = ClipRRect()
    //     0x149cd54: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x149cd58: mov             x2, x0
    // 0x149cd5c: ldur            x0, [fp, #-0x18]
    // 0x149cd60: stur            x2, [fp, #-0x10]
    // 0x149cd64: StoreField: r2->field_f = r0
    //     0x149cd64: stur            w0, [x2, #0xf]
    // 0x149cd68: r0 = Instance_Clip
    //     0x149cd68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x149cd6c: ldr             x0, [x0, #0x138]
    // 0x149cd70: ArrayStore: r2[0] = r0  ; List_4
    //     0x149cd70: stur            w0, [x2, #0x17]
    // 0x149cd74: ldur            x0, [fp, #-0x38]
    // 0x149cd78: StoreField: r2->field_b = r0
    //     0x149cd78: stur            w0, [x2, #0xb]
    // 0x149cd7c: ldur            x0, [fp, #-8]
    // 0x149cd80: LoadField: r1 = r0->field_13
    //     0x149cd80: ldur            w1, [x0, #0x13]
    // 0x149cd84: DecompressPointer r1
    //     0x149cd84: add             x1, x1, HEAP, lsl #32
    // 0x149cd88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x149cd88: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x149cd8c: r0 = _of()
    //     0x149cd8c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x149cd90: LoadField: r1 = r0->field_7
    //     0x149cd90: ldur            w1, [x0, #7]
    // 0x149cd94: DecompressPointer r1
    //     0x149cd94: add             x1, x1, HEAP, lsl #32
    // 0x149cd98: LoadField: d0 = r1->field_7
    //     0x149cd98: ldur            d0, [x1, #7]
    // 0x149cd9c: d1 = 0.500000
    //     0x149cd9c: fmov            d1, #0.50000000
    // 0x149cda0: fmul            d2, d0, d1
    // 0x149cda4: ldur            x2, [fp, #-8]
    // 0x149cda8: stur            d2, [fp, #-0x88]
    // 0x149cdac: LoadField: r1 = r2->field_f
    //     0x149cdac: ldur            w1, [x2, #0xf]
    // 0x149cdb0: DecompressPointer r1
    //     0x149cdb0: add             x1, x1, HEAP, lsl #32
    // 0x149cdb4: r0 = controller()
    //     0x149cdb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cdb8: LoadField: r1 = r0->field_4b
    //     0x149cdb8: ldur            w1, [x0, #0x4b]
    // 0x149cdbc: DecompressPointer r1
    //     0x149cdbc: add             x1, x1, HEAP, lsl #32
    // 0x149cdc0: r0 = value()
    //     0x149cdc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149cdc4: LoadField: r1 = r0->field_b
    //     0x149cdc4: ldur            w1, [x0, #0xb]
    // 0x149cdc8: DecompressPointer r1
    //     0x149cdc8: add             x1, x1, HEAP, lsl #32
    // 0x149cdcc: cmp             w1, NULL
    // 0x149cdd0: b.ne            #0x149cddc
    // 0x149cdd4: r0 = Null
    //     0x149cdd4: mov             x0, NULL
    // 0x149cdd8: b               #0x149ce00
    // 0x149cddc: LoadField: r0 = r1->field_f
    //     0x149cddc: ldur            w0, [x1, #0xf]
    // 0x149cde0: DecompressPointer r0
    //     0x149cde0: add             x0, x0, HEAP, lsl #32
    // 0x149cde4: cmp             w0, NULL
    // 0x149cde8: b.ne            #0x149cdf4
    // 0x149cdec: r0 = Null
    //     0x149cdec: mov             x0, NULL
    // 0x149cdf0: b               #0x149ce00
    // 0x149cdf4: LoadField: r1 = r0->field_b
    //     0x149cdf4: ldur            w1, [x0, #0xb]
    // 0x149cdf8: DecompressPointer r1
    //     0x149cdf8: add             x1, x1, HEAP, lsl #32
    // 0x149cdfc: mov             x0, x1
    // 0x149ce00: cmp             w0, NULL
    // 0x149ce04: b.ne            #0x149ce0c
    // 0x149ce08: r0 = ""
    //     0x149ce08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149ce0c: ldur            x2, [fp, #-8]
    // 0x149ce10: stur            x0, [fp, #-0x18]
    // 0x149ce14: LoadField: r1 = r2->field_13
    //     0x149ce14: ldur            w1, [x2, #0x13]
    // 0x149ce18: DecompressPointer r1
    //     0x149ce18: add             x1, x1, HEAP, lsl #32
    // 0x149ce1c: r0 = of()
    //     0x149ce1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149ce20: LoadField: r1 = r0->field_87
    //     0x149ce20: ldur            w1, [x0, #0x87]
    // 0x149ce24: DecompressPointer r1
    //     0x149ce24: add             x1, x1, HEAP, lsl #32
    // 0x149ce28: LoadField: r0 = r1->field_2b
    //     0x149ce28: ldur            w0, [x1, #0x2b]
    // 0x149ce2c: DecompressPointer r0
    //     0x149ce2c: add             x0, x0, HEAP, lsl #32
    // 0x149ce30: stur            x0, [fp, #-0x28]
    // 0x149ce34: r1 = Instance_Color
    //     0x149ce34: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149ce38: d0 = 0.700000
    //     0x149ce38: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149ce3c: ldr             d0, [x17, #0xf48]
    // 0x149ce40: r0 = withOpacity()
    //     0x149ce40: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149ce44: r16 = 12.000000
    //     0x149ce44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149ce48: ldr             x16, [x16, #0x9e8]
    // 0x149ce4c: stp             x16, x0, [SP]
    // 0x149ce50: ldur            x1, [fp, #-0x28]
    // 0x149ce54: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x149ce54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x149ce58: ldr             x4, [x4, #0x9b8]
    // 0x149ce5c: r0 = copyWith()
    //     0x149ce5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149ce60: stur            x0, [fp, #-0x28]
    // 0x149ce64: r0 = Text()
    //     0x149ce64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149ce68: mov             x2, x0
    // 0x149ce6c: ldur            x0, [fp, #-0x18]
    // 0x149ce70: stur            x2, [fp, #-0x30]
    // 0x149ce74: StoreField: r2->field_b = r0
    //     0x149ce74: stur            w0, [x2, #0xb]
    // 0x149ce78: ldur            x0, [fp, #-0x28]
    // 0x149ce7c: StoreField: r2->field_13 = r0
    //     0x149ce7c: stur            w0, [x2, #0x13]
    // 0x149ce80: r0 = Instance_TextOverflow
    //     0x149ce80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x149ce84: ldr             x0, [x0, #0xe10]
    // 0x149ce88: StoreField: r2->field_2b = r0
    //     0x149ce88: stur            w0, [x2, #0x2b]
    // 0x149ce8c: r0 = 6
    //     0x149ce8c: movz            x0, #0x6
    // 0x149ce90: StoreField: r2->field_37 = r0
    //     0x149ce90: stur            w0, [x2, #0x37]
    // 0x149ce94: ldur            x3, [fp, #-8]
    // 0x149ce98: LoadField: r1 = r3->field_f
    //     0x149ce98: ldur            w1, [x3, #0xf]
    // 0x149ce9c: DecompressPointer r1
    //     0x149ce9c: add             x1, x1, HEAP, lsl #32
    // 0x149cea0: r0 = controller()
    //     0x149cea0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cea4: LoadField: r1 = r0->field_4b
    //     0x149cea4: ldur            w1, [x0, #0x4b]
    // 0x149cea8: DecompressPointer r1
    //     0x149cea8: add             x1, x1, HEAP, lsl #32
    // 0x149ceac: r0 = value()
    //     0x149ceac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149ceb0: LoadField: r1 = r0->field_b
    //     0x149ceb0: ldur            w1, [x0, #0xb]
    // 0x149ceb4: DecompressPointer r1
    //     0x149ceb4: add             x1, x1, HEAP, lsl #32
    // 0x149ceb8: cmp             w1, NULL
    // 0x149cebc: b.ne            #0x149cec8
    // 0x149cec0: r0 = Null
    //     0x149cec0: mov             x0, NULL
    // 0x149cec4: b               #0x149ceec
    // 0x149cec8: LoadField: r0 = r1->field_f
    //     0x149cec8: ldur            w0, [x1, #0xf]
    // 0x149cecc: DecompressPointer r0
    //     0x149cecc: add             x0, x0, HEAP, lsl #32
    // 0x149ced0: cmp             w0, NULL
    // 0x149ced4: b.ne            #0x149cee0
    // 0x149ced8: r0 = Null
    //     0x149ced8: mov             x0, NULL
    // 0x149cedc: b               #0x149ceec
    // 0x149cee0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x149cee0: ldur            w1, [x0, #0x17]
    // 0x149cee4: DecompressPointer r1
    //     0x149cee4: add             x1, x1, HEAP, lsl #32
    // 0x149cee8: mov             x0, x1
    // 0x149ceec: r1 = LoadClassIdInstr(r0)
    //     0x149ceec: ldur            x1, [x0, #-1]
    //     0x149cef0: ubfx            x1, x1, #0xc, #0x14
    // 0x149cef4: r16 = "size"
    //     0x149cef4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x149cef8: ldr             x16, [x16, #0x9c0]
    // 0x149cefc: stp             x16, x0, [SP]
    // 0x149cf00: mov             x0, x1
    // 0x149cf04: mov             lr, x0
    // 0x149cf08: ldr             lr, [x21, lr, lsl #3]
    // 0x149cf0c: blr             lr
    // 0x149cf10: tbnz            w0, #4, #0x149d104
    // 0x149cf14: ldur            x0, [fp, #-8]
    // 0x149cf18: r1 = Null
    //     0x149cf18: mov             x1, NULL
    // 0x149cf1c: r2 = 8
    //     0x149cf1c: movz            x2, #0x8
    // 0x149cf20: r0 = AllocateArray()
    //     0x149cf20: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149cf24: stur            x0, [fp, #-0x18]
    // 0x149cf28: r16 = "Size: "
    //     0x149cf28: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x149cf2c: ldr             x16, [x16, #0xf00]
    // 0x149cf30: StoreField: r0->field_f = r16
    //     0x149cf30: stur            w16, [x0, #0xf]
    // 0x149cf34: ldur            x2, [fp, #-8]
    // 0x149cf38: LoadField: r1 = r2->field_f
    //     0x149cf38: ldur            w1, [x2, #0xf]
    // 0x149cf3c: DecompressPointer r1
    //     0x149cf3c: add             x1, x1, HEAP, lsl #32
    // 0x149cf40: r0 = controller()
    //     0x149cf40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cf44: LoadField: r1 = r0->field_4b
    //     0x149cf44: ldur            w1, [x0, #0x4b]
    // 0x149cf48: DecompressPointer r1
    //     0x149cf48: add             x1, x1, HEAP, lsl #32
    // 0x149cf4c: r0 = value()
    //     0x149cf4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149cf50: LoadField: r1 = r0->field_b
    //     0x149cf50: ldur            w1, [x0, #0xb]
    // 0x149cf54: DecompressPointer r1
    //     0x149cf54: add             x1, x1, HEAP, lsl #32
    // 0x149cf58: cmp             w1, NULL
    // 0x149cf5c: b.ne            #0x149cf68
    // 0x149cf60: r0 = Null
    //     0x149cf60: mov             x0, NULL
    // 0x149cf64: b               #0x149cf8c
    // 0x149cf68: LoadField: r0 = r1->field_f
    //     0x149cf68: ldur            w0, [x1, #0xf]
    // 0x149cf6c: DecompressPointer r0
    //     0x149cf6c: add             x0, x0, HEAP, lsl #32
    // 0x149cf70: cmp             w0, NULL
    // 0x149cf74: b.ne            #0x149cf80
    // 0x149cf78: r0 = Null
    //     0x149cf78: mov             x0, NULL
    // 0x149cf7c: b               #0x149cf8c
    // 0x149cf80: LoadField: r1 = r0->field_f
    //     0x149cf80: ldur            w1, [x0, #0xf]
    // 0x149cf84: DecompressPointer r1
    //     0x149cf84: add             x1, x1, HEAP, lsl #32
    // 0x149cf88: mov             x0, x1
    // 0x149cf8c: cmp             w0, NULL
    // 0x149cf90: b.ne            #0x149cf98
    // 0x149cf94: r0 = ""
    //     0x149cf94: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149cf98: ldur            x3, [fp, #-8]
    // 0x149cf9c: ldur            x2, [fp, #-0x18]
    // 0x149cfa0: mov             x1, x2
    // 0x149cfa4: ArrayStore: r1[1] = r0  ; List_4
    //     0x149cfa4: add             x25, x1, #0x13
    //     0x149cfa8: str             w0, [x25]
    //     0x149cfac: tbz             w0, #0, #0x149cfc8
    //     0x149cfb0: ldurb           w16, [x1, #-1]
    //     0x149cfb4: ldurb           w17, [x0, #-1]
    //     0x149cfb8: and             x16, x17, x16, lsr #2
    //     0x149cfbc: tst             x16, HEAP, lsr #32
    //     0x149cfc0: b.eq            #0x149cfc8
    //     0x149cfc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149cfc8: r16 = " / Qty: "
    //     0x149cfc8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x149cfcc: ldr             x16, [x16, #0x760]
    // 0x149cfd0: ArrayStore: r2[0] = r16  ; List_4
    //     0x149cfd0: stur            w16, [x2, #0x17]
    // 0x149cfd4: LoadField: r1 = r3->field_f
    //     0x149cfd4: ldur            w1, [x3, #0xf]
    // 0x149cfd8: DecompressPointer r1
    //     0x149cfd8: add             x1, x1, HEAP, lsl #32
    // 0x149cfdc: r0 = controller()
    //     0x149cfdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149cfe0: LoadField: r1 = r0->field_4b
    //     0x149cfe0: ldur            w1, [x0, #0x4b]
    // 0x149cfe4: DecompressPointer r1
    //     0x149cfe4: add             x1, x1, HEAP, lsl #32
    // 0x149cfe8: r0 = value()
    //     0x149cfe8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149cfec: LoadField: r1 = r0->field_b
    //     0x149cfec: ldur            w1, [x0, #0xb]
    // 0x149cff0: DecompressPointer r1
    //     0x149cff0: add             x1, x1, HEAP, lsl #32
    // 0x149cff4: cmp             w1, NULL
    // 0x149cff8: b.ne            #0x149d004
    // 0x149cffc: r0 = Null
    //     0x149cffc: mov             x0, NULL
    // 0x149d000: b               #0x149d04c
    // 0x149d004: LoadField: r0 = r1->field_f
    //     0x149d004: ldur            w0, [x1, #0xf]
    // 0x149d008: DecompressPointer r0
    //     0x149d008: add             x0, x0, HEAP, lsl #32
    // 0x149d00c: cmp             w0, NULL
    // 0x149d010: b.ne            #0x149d01c
    // 0x149d014: r0 = Null
    //     0x149d014: mov             x0, NULL
    // 0x149d018: b               #0x149d04c
    // 0x149d01c: LoadField: r1 = r0->field_13
    //     0x149d01c: ldur            w1, [x0, #0x13]
    // 0x149d020: DecompressPointer r1
    //     0x149d020: add             x1, x1, HEAP, lsl #32
    // 0x149d024: r0 = 60
    //     0x149d024: movz            x0, #0x3c
    // 0x149d028: branchIfSmi(r1, 0x149d034)
    //     0x149d028: tbz             w1, #0, #0x149d034
    // 0x149d02c: r0 = LoadClassIdInstr(r1)
    //     0x149d02c: ldur            x0, [x1, #-1]
    //     0x149d030: ubfx            x0, x0, #0xc, #0x14
    // 0x149d034: str             x1, [SP]
    // 0x149d038: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x149d038: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x149d03c: r0 = GDT[cid_x0 + 0x2700]()
    //     0x149d03c: movz            x17, #0x2700
    //     0x149d040: add             lr, x0, x17
    //     0x149d044: ldr             lr, [x21, lr, lsl #3]
    //     0x149d048: blr             lr
    // 0x149d04c: cmp             w0, NULL
    // 0x149d050: b.ne            #0x149d058
    // 0x149d054: r0 = ""
    //     0x149d054: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149d058: ldur            x2, [fp, #-8]
    // 0x149d05c: ldur            x1, [fp, #-0x18]
    // 0x149d060: ArrayStore: r1[3] = r0  ; List_4
    //     0x149d060: add             x25, x1, #0x1b
    //     0x149d064: str             w0, [x25]
    //     0x149d068: tbz             w0, #0, #0x149d084
    //     0x149d06c: ldurb           w16, [x1, #-1]
    //     0x149d070: ldurb           w17, [x0, #-1]
    //     0x149d074: and             x16, x17, x16, lsr #2
    //     0x149d078: tst             x16, HEAP, lsr #32
    //     0x149d07c: b.eq            #0x149d084
    //     0x149d080: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149d084: ldur            x16, [fp, #-0x18]
    // 0x149d088: str             x16, [SP]
    // 0x149d08c: r0 = _interpolate()
    //     0x149d08c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x149d090: ldur            x2, [fp, #-8]
    // 0x149d094: stur            x0, [fp, #-0x18]
    // 0x149d098: LoadField: r1 = r2->field_13
    //     0x149d098: ldur            w1, [x2, #0x13]
    // 0x149d09c: DecompressPointer r1
    //     0x149d09c: add             x1, x1, HEAP, lsl #32
    // 0x149d0a0: r0 = of()
    //     0x149d0a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149d0a4: LoadField: r1 = r0->field_87
    //     0x149d0a4: ldur            w1, [x0, #0x87]
    // 0x149d0a8: DecompressPointer r1
    //     0x149d0a8: add             x1, x1, HEAP, lsl #32
    // 0x149d0ac: LoadField: r0 = r1->field_2b
    //     0x149d0ac: ldur            w0, [x1, #0x2b]
    // 0x149d0b0: DecompressPointer r0
    //     0x149d0b0: add             x0, x0, HEAP, lsl #32
    // 0x149d0b4: stur            x0, [fp, #-0x28]
    // 0x149d0b8: r1 = Instance_Color
    //     0x149d0b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149d0bc: d0 = 0.400000
    //     0x149d0bc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x149d0c0: r0 = withOpacity()
    //     0x149d0c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149d0c4: r16 = 12.000000
    //     0x149d0c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149d0c8: ldr             x16, [x16, #0x9e8]
    // 0x149d0cc: stp             x0, x16, [SP]
    // 0x149d0d0: ldur            x1, [fp, #-0x28]
    // 0x149d0d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x149d0d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x149d0d8: ldr             x4, [x4, #0xaa0]
    // 0x149d0dc: r0 = copyWith()
    //     0x149d0dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149d0e0: stur            x0, [fp, #-0x28]
    // 0x149d0e4: r0 = Text()
    //     0x149d0e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149d0e8: mov             x1, x0
    // 0x149d0ec: ldur            x0, [fp, #-0x18]
    // 0x149d0f0: StoreField: r1->field_b = r0
    //     0x149d0f0: stur            w0, [x1, #0xb]
    // 0x149d0f4: ldur            x0, [fp, #-0x28]
    // 0x149d0f8: StoreField: r1->field_13 = r0
    //     0x149d0f8: stur            w0, [x1, #0x13]
    // 0x149d0fc: mov             x0, x1
    // 0x149d100: b               #0x149d2f0
    // 0x149d104: ldur            x0, [fp, #-8]
    // 0x149d108: r1 = Null
    //     0x149d108: mov             x1, NULL
    // 0x149d10c: r2 = 8
    //     0x149d10c: movz            x2, #0x8
    // 0x149d110: r0 = AllocateArray()
    //     0x149d110: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149d114: stur            x0, [fp, #-0x18]
    // 0x149d118: r16 = "Variant: "
    //     0x149d118: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x149d11c: ldr             x16, [x16, #0xf08]
    // 0x149d120: StoreField: r0->field_f = r16
    //     0x149d120: stur            w16, [x0, #0xf]
    // 0x149d124: ldur            x2, [fp, #-8]
    // 0x149d128: LoadField: r1 = r2->field_f
    //     0x149d128: ldur            w1, [x2, #0xf]
    // 0x149d12c: DecompressPointer r1
    //     0x149d12c: add             x1, x1, HEAP, lsl #32
    // 0x149d130: r0 = controller()
    //     0x149d130: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149d134: LoadField: r1 = r0->field_4b
    //     0x149d134: ldur            w1, [x0, #0x4b]
    // 0x149d138: DecompressPointer r1
    //     0x149d138: add             x1, x1, HEAP, lsl #32
    // 0x149d13c: r0 = value()
    //     0x149d13c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149d140: LoadField: r1 = r0->field_b
    //     0x149d140: ldur            w1, [x0, #0xb]
    // 0x149d144: DecompressPointer r1
    //     0x149d144: add             x1, x1, HEAP, lsl #32
    // 0x149d148: cmp             w1, NULL
    // 0x149d14c: b.ne            #0x149d158
    // 0x149d150: r0 = Null
    //     0x149d150: mov             x0, NULL
    // 0x149d154: b               #0x149d17c
    // 0x149d158: LoadField: r0 = r1->field_f
    //     0x149d158: ldur            w0, [x1, #0xf]
    // 0x149d15c: DecompressPointer r0
    //     0x149d15c: add             x0, x0, HEAP, lsl #32
    // 0x149d160: cmp             w0, NULL
    // 0x149d164: b.ne            #0x149d170
    // 0x149d168: r0 = Null
    //     0x149d168: mov             x0, NULL
    // 0x149d16c: b               #0x149d17c
    // 0x149d170: LoadField: r1 = r0->field_f
    //     0x149d170: ldur            w1, [x0, #0xf]
    // 0x149d174: DecompressPointer r1
    //     0x149d174: add             x1, x1, HEAP, lsl #32
    // 0x149d178: mov             x0, x1
    // 0x149d17c: cmp             w0, NULL
    // 0x149d180: b.ne            #0x149d188
    // 0x149d184: r0 = ""
    //     0x149d184: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149d188: ldur            x3, [fp, #-8]
    // 0x149d18c: ldur            x2, [fp, #-0x18]
    // 0x149d190: mov             x1, x2
    // 0x149d194: ArrayStore: r1[1] = r0  ; List_4
    //     0x149d194: add             x25, x1, #0x13
    //     0x149d198: str             w0, [x25]
    //     0x149d19c: tbz             w0, #0, #0x149d1b8
    //     0x149d1a0: ldurb           w16, [x1, #-1]
    //     0x149d1a4: ldurb           w17, [x0, #-1]
    //     0x149d1a8: and             x16, x17, x16, lsr #2
    //     0x149d1ac: tst             x16, HEAP, lsr #32
    //     0x149d1b0: b.eq            #0x149d1b8
    //     0x149d1b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149d1b8: r16 = " / Qty: "
    //     0x149d1b8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x149d1bc: ldr             x16, [x16, #0x760]
    // 0x149d1c0: ArrayStore: r2[0] = r16  ; List_4
    //     0x149d1c0: stur            w16, [x2, #0x17]
    // 0x149d1c4: LoadField: r1 = r3->field_f
    //     0x149d1c4: ldur            w1, [x3, #0xf]
    // 0x149d1c8: DecompressPointer r1
    //     0x149d1c8: add             x1, x1, HEAP, lsl #32
    // 0x149d1cc: r0 = controller()
    //     0x149d1cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149d1d0: LoadField: r1 = r0->field_4b
    //     0x149d1d0: ldur            w1, [x0, #0x4b]
    // 0x149d1d4: DecompressPointer r1
    //     0x149d1d4: add             x1, x1, HEAP, lsl #32
    // 0x149d1d8: r0 = value()
    //     0x149d1d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149d1dc: LoadField: r1 = r0->field_b
    //     0x149d1dc: ldur            w1, [x0, #0xb]
    // 0x149d1e0: DecompressPointer r1
    //     0x149d1e0: add             x1, x1, HEAP, lsl #32
    // 0x149d1e4: cmp             w1, NULL
    // 0x149d1e8: b.ne            #0x149d1f4
    // 0x149d1ec: r0 = Null
    //     0x149d1ec: mov             x0, NULL
    // 0x149d1f0: b               #0x149d23c
    // 0x149d1f4: LoadField: r0 = r1->field_f
    //     0x149d1f4: ldur            w0, [x1, #0xf]
    // 0x149d1f8: DecompressPointer r0
    //     0x149d1f8: add             x0, x0, HEAP, lsl #32
    // 0x149d1fc: cmp             w0, NULL
    // 0x149d200: b.ne            #0x149d20c
    // 0x149d204: r0 = Null
    //     0x149d204: mov             x0, NULL
    // 0x149d208: b               #0x149d23c
    // 0x149d20c: LoadField: r1 = r0->field_13
    //     0x149d20c: ldur            w1, [x0, #0x13]
    // 0x149d210: DecompressPointer r1
    //     0x149d210: add             x1, x1, HEAP, lsl #32
    // 0x149d214: r0 = 60
    //     0x149d214: movz            x0, #0x3c
    // 0x149d218: branchIfSmi(r1, 0x149d224)
    //     0x149d218: tbz             w1, #0, #0x149d224
    // 0x149d21c: r0 = LoadClassIdInstr(r1)
    //     0x149d21c: ldur            x0, [x1, #-1]
    //     0x149d220: ubfx            x0, x0, #0xc, #0x14
    // 0x149d224: str             x1, [SP]
    // 0x149d228: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x149d228: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x149d22c: r0 = GDT[cid_x0 + 0x2700]()
    //     0x149d22c: movz            x17, #0x2700
    //     0x149d230: add             lr, x0, x17
    //     0x149d234: ldr             lr, [x21, lr, lsl #3]
    //     0x149d238: blr             lr
    // 0x149d23c: cmp             w0, NULL
    // 0x149d240: b.ne            #0x149d248
    // 0x149d244: r0 = ""
    //     0x149d244: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149d248: ldur            x2, [fp, #-8]
    // 0x149d24c: ldur            x1, [fp, #-0x18]
    // 0x149d250: ArrayStore: r1[3] = r0  ; List_4
    //     0x149d250: add             x25, x1, #0x1b
    //     0x149d254: str             w0, [x25]
    //     0x149d258: tbz             w0, #0, #0x149d274
    //     0x149d25c: ldurb           w16, [x1, #-1]
    //     0x149d260: ldurb           w17, [x0, #-1]
    //     0x149d264: and             x16, x17, x16, lsr #2
    //     0x149d268: tst             x16, HEAP, lsr #32
    //     0x149d26c: b.eq            #0x149d274
    //     0x149d270: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149d274: ldur            x16, [fp, #-0x18]
    // 0x149d278: str             x16, [SP]
    // 0x149d27c: r0 = _interpolate()
    //     0x149d27c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x149d280: ldur            x2, [fp, #-8]
    // 0x149d284: stur            x0, [fp, #-0x18]
    // 0x149d288: LoadField: r1 = r2->field_13
    //     0x149d288: ldur            w1, [x2, #0x13]
    // 0x149d28c: DecompressPointer r1
    //     0x149d28c: add             x1, x1, HEAP, lsl #32
    // 0x149d290: r0 = of()
    //     0x149d290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149d294: LoadField: r1 = r0->field_87
    //     0x149d294: ldur            w1, [x0, #0x87]
    // 0x149d298: DecompressPointer r1
    //     0x149d298: add             x1, x1, HEAP, lsl #32
    // 0x149d29c: LoadField: r0 = r1->field_2b
    //     0x149d29c: ldur            w0, [x1, #0x2b]
    // 0x149d2a0: DecompressPointer r0
    //     0x149d2a0: add             x0, x0, HEAP, lsl #32
    // 0x149d2a4: stur            x0, [fp, #-0x28]
    // 0x149d2a8: r1 = Instance_Color
    //     0x149d2a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149d2ac: d0 = 0.400000
    //     0x149d2ac: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x149d2b0: r0 = withOpacity()
    //     0x149d2b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149d2b4: r16 = 12.000000
    //     0x149d2b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149d2b8: ldr             x16, [x16, #0x9e8]
    // 0x149d2bc: stp             x0, x16, [SP]
    // 0x149d2c0: ldur            x1, [fp, #-0x28]
    // 0x149d2c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x149d2c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x149d2c8: ldr             x4, [x4, #0xaa0]
    // 0x149d2cc: r0 = copyWith()
    //     0x149d2cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149d2d0: stur            x0, [fp, #-0x28]
    // 0x149d2d4: r0 = Text()
    //     0x149d2d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149d2d8: mov             x1, x0
    // 0x149d2dc: ldur            x0, [fp, #-0x18]
    // 0x149d2e0: StoreField: r1->field_b = r0
    //     0x149d2e0: stur            w0, [x1, #0xb]
    // 0x149d2e4: ldur            x0, [fp, #-0x28]
    // 0x149d2e8: StoreField: r1->field_13 = r0
    //     0x149d2e8: stur            w0, [x1, #0x13]
    // 0x149d2ec: mov             x0, x1
    // 0x149d2f0: ldur            x2, [fp, #-8]
    // 0x149d2f4: stur            x0, [fp, #-0x18]
    // 0x149d2f8: r0 = Padding()
    //     0x149d2f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149d2fc: mov             x2, x0
    // 0x149d300: r0 = Instance_EdgeInsets
    //     0x149d300: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x149d304: ldr             x0, [x0, #0x770]
    // 0x149d308: stur            x2, [fp, #-0x28]
    // 0x149d30c: StoreField: r2->field_f = r0
    //     0x149d30c: stur            w0, [x2, #0xf]
    // 0x149d310: ldur            x1, [fp, #-0x18]
    // 0x149d314: StoreField: r2->field_b = r1
    //     0x149d314: stur            w1, [x2, #0xb]
    // 0x149d318: ldur            x3, [fp, #-8]
    // 0x149d31c: LoadField: r1 = r3->field_f
    //     0x149d31c: ldur            w1, [x3, #0xf]
    // 0x149d320: DecompressPointer r1
    //     0x149d320: add             x1, x1, HEAP, lsl #32
    // 0x149d324: r0 = controller()
    //     0x149d324: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149d328: LoadField: r1 = r0->field_4b
    //     0x149d328: ldur            w1, [x0, #0x4b]
    // 0x149d32c: DecompressPointer r1
    //     0x149d32c: add             x1, x1, HEAP, lsl #32
    // 0x149d330: r0 = value()
    //     0x149d330: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149d334: LoadField: r1 = r0->field_b
    //     0x149d334: ldur            w1, [x0, #0xb]
    // 0x149d338: DecompressPointer r1
    //     0x149d338: add             x1, x1, HEAP, lsl #32
    // 0x149d33c: cmp             w1, NULL
    // 0x149d340: b.ne            #0x149d34c
    // 0x149d344: r0 = Null
    //     0x149d344: mov             x0, NULL
    // 0x149d348: b               #0x149d384
    // 0x149d34c: LoadField: r0 = r1->field_f
    //     0x149d34c: ldur            w0, [x1, #0xf]
    // 0x149d350: DecompressPointer r0
    //     0x149d350: add             x0, x0, HEAP, lsl #32
    // 0x149d354: cmp             w0, NULL
    // 0x149d358: b.ne            #0x149d364
    // 0x149d35c: r0 = Null
    //     0x149d35c: mov             x0, NULL
    // 0x149d360: b               #0x149d384
    // 0x149d364: LoadField: r1 = r0->field_1b
    //     0x149d364: ldur            w1, [x0, #0x1b]
    // 0x149d368: DecompressPointer r1
    //     0x149d368: add             x1, x1, HEAP, lsl #32
    // 0x149d36c: cmp             w1, NULL
    // 0x149d370: b.ne            #0x149d37c
    // 0x149d374: r0 = Null
    //     0x149d374: mov             x0, NULL
    // 0x149d378: b               #0x149d384
    // 0x149d37c: LoadField: r0 = r1->field_7
    //     0x149d37c: ldur            w0, [x1, #7]
    // 0x149d380: DecompressPointer r0
    //     0x149d380: add             x0, x0, HEAP, lsl #32
    // 0x149d384: cmp             w0, NULL
    // 0x149d388: b.ne            #0x149d394
    // 0x149d38c: r5 = ""
    //     0x149d38c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149d390: b               #0x149d398
    // 0x149d394: mov             x5, x0
    // 0x149d398: ldur            x2, [fp, #-8]
    // 0x149d39c: ldur            x4, [fp, #-0x10]
    // 0x149d3a0: ldur            d0, [fp, #-0x88]
    // 0x149d3a4: ldur            x3, [fp, #-0x30]
    // 0x149d3a8: ldur            x0, [fp, #-0x28]
    // 0x149d3ac: stur            x5, [fp, #-0x18]
    // 0x149d3b0: LoadField: r1 = r2->field_13
    //     0x149d3b0: ldur            w1, [x2, #0x13]
    // 0x149d3b4: DecompressPointer r1
    //     0x149d3b4: add             x1, x1, HEAP, lsl #32
    // 0x149d3b8: r0 = of()
    //     0x149d3b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149d3bc: LoadField: r1 = r0->field_87
    //     0x149d3bc: ldur            w1, [x0, #0x87]
    // 0x149d3c0: DecompressPointer r1
    //     0x149d3c0: add             x1, x1, HEAP, lsl #32
    // 0x149d3c4: LoadField: r0 = r1->field_2b
    //     0x149d3c4: ldur            w0, [x1, #0x2b]
    // 0x149d3c8: DecompressPointer r0
    //     0x149d3c8: add             x0, x0, HEAP, lsl #32
    // 0x149d3cc: stur            x0, [fp, #-0x38]
    // 0x149d3d0: r1 = Instance_Color
    //     0x149d3d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149d3d4: d0 = 0.700000
    //     0x149d3d4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149d3d8: ldr             d0, [x17, #0xf48]
    // 0x149d3dc: r0 = withOpacity()
    //     0x149d3dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149d3e0: r16 = 12.000000
    //     0x149d3e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149d3e4: ldr             x16, [x16, #0x9e8]
    // 0x149d3e8: stp             x0, x16, [SP]
    // 0x149d3ec: ldur            x1, [fp, #-0x38]
    // 0x149d3f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x149d3f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x149d3f4: ldr             x4, [x4, #0xaa0]
    // 0x149d3f8: r0 = copyWith()
    //     0x149d3f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149d3fc: stur            x0, [fp, #-0x38]
    // 0x149d400: r0 = Text()
    //     0x149d400: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149d404: mov             x1, x0
    // 0x149d408: ldur            x0, [fp, #-0x18]
    // 0x149d40c: stur            x1, [fp, #-0x40]
    // 0x149d410: StoreField: r1->field_b = r0
    //     0x149d410: stur            w0, [x1, #0xb]
    // 0x149d414: ldur            x0, [fp, #-0x38]
    // 0x149d418: StoreField: r1->field_13 = r0
    //     0x149d418: stur            w0, [x1, #0x13]
    // 0x149d41c: r0 = Padding()
    //     0x149d41c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149d420: mov             x3, x0
    // 0x149d424: r0 = Instance_EdgeInsets
    //     0x149d424: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x149d428: ldr             x0, [x0, #0x770]
    // 0x149d42c: stur            x3, [fp, #-0x18]
    // 0x149d430: StoreField: r3->field_f = r0
    //     0x149d430: stur            w0, [x3, #0xf]
    // 0x149d434: ldur            x0, [fp, #-0x40]
    // 0x149d438: StoreField: r3->field_b = r0
    //     0x149d438: stur            w0, [x3, #0xb]
    // 0x149d43c: r1 = Null
    //     0x149d43c: mov             x1, NULL
    // 0x149d440: r2 = 6
    //     0x149d440: movz            x2, #0x6
    // 0x149d444: r0 = AllocateArray()
    //     0x149d444: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149d448: mov             x2, x0
    // 0x149d44c: ldur            x0, [fp, #-0x30]
    // 0x149d450: stur            x2, [fp, #-0x38]
    // 0x149d454: StoreField: r2->field_f = r0
    //     0x149d454: stur            w0, [x2, #0xf]
    // 0x149d458: ldur            x0, [fp, #-0x28]
    // 0x149d45c: StoreField: r2->field_13 = r0
    //     0x149d45c: stur            w0, [x2, #0x13]
    // 0x149d460: ldur            x0, [fp, #-0x18]
    // 0x149d464: ArrayStore: r2[0] = r0  ; List_4
    //     0x149d464: stur            w0, [x2, #0x17]
    // 0x149d468: r1 = <Widget>
    //     0x149d468: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x149d46c: r0 = AllocateGrowableArray()
    //     0x149d46c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x149d470: mov             x1, x0
    // 0x149d474: ldur            x0, [fp, #-0x38]
    // 0x149d478: stur            x1, [fp, #-0x18]
    // 0x149d47c: StoreField: r1->field_f = r0
    //     0x149d47c: stur            w0, [x1, #0xf]
    // 0x149d480: r2 = 6
    //     0x149d480: movz            x2, #0x6
    // 0x149d484: StoreField: r1->field_b = r2
    //     0x149d484: stur            w2, [x1, #0xb]
    // 0x149d488: r0 = Column()
    //     0x149d488: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x149d48c: mov             x1, x0
    // 0x149d490: r0 = Instance_Axis
    //     0x149d490: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x149d494: stur            x1, [fp, #-0x28]
    // 0x149d498: StoreField: r1->field_f = r0
    //     0x149d498: stur            w0, [x1, #0xf]
    // 0x149d49c: r2 = Instance_MainAxisAlignment
    //     0x149d49c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x149d4a0: ldr             x2, [x2, #0xa08]
    // 0x149d4a4: StoreField: r1->field_13 = r2
    //     0x149d4a4: stur            w2, [x1, #0x13]
    // 0x149d4a8: r3 = Instance_MainAxisSize
    //     0x149d4a8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x149d4ac: ldr             x3, [x3, #0xdd0]
    // 0x149d4b0: ArrayStore: r1[0] = r3  ; List_4
    //     0x149d4b0: stur            w3, [x1, #0x17]
    // 0x149d4b4: r3 = Instance_CrossAxisAlignment
    //     0x149d4b4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x149d4b8: ldr             x3, [x3, #0x890]
    // 0x149d4bc: StoreField: r1->field_1b = r3
    //     0x149d4bc: stur            w3, [x1, #0x1b]
    // 0x149d4c0: r4 = Instance_VerticalDirection
    //     0x149d4c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x149d4c4: ldr             x4, [x4, #0xa20]
    // 0x149d4c8: StoreField: r1->field_23 = r4
    //     0x149d4c8: stur            w4, [x1, #0x23]
    // 0x149d4cc: r5 = Instance_Clip
    //     0x149d4cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x149d4d0: ldr             x5, [x5, #0x38]
    // 0x149d4d4: StoreField: r1->field_2b = r5
    //     0x149d4d4: stur            w5, [x1, #0x2b]
    // 0x149d4d8: StoreField: r1->field_2f = rZR
    //     0x149d4d8: stur            xzr, [x1, #0x2f]
    // 0x149d4dc: ldur            x6, [fp, #-0x18]
    // 0x149d4e0: StoreField: r1->field_b = r6
    //     0x149d4e0: stur            w6, [x1, #0xb]
    // 0x149d4e4: ldur            d0, [fp, #-0x88]
    // 0x149d4e8: r6 = inline_Allocate_Double()
    //     0x149d4e8: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x149d4ec: add             x6, x6, #0x10
    //     0x149d4f0: cmp             x7, x6
    //     0x149d4f4: b.ls            #0x149e314
    //     0x149d4f8: str             x6, [THR, #0x50]  ; THR::top
    //     0x149d4fc: sub             x6, x6, #0xf
    //     0x149d500: movz            x7, #0xe15c
    //     0x149d504: movk            x7, #0x3, lsl #16
    //     0x149d508: stur            x7, [x6, #-1]
    // 0x149d50c: StoreField: r6->field_7 = d0
    //     0x149d50c: stur            d0, [x6, #7]
    // 0x149d510: stur            x6, [fp, #-0x18]
    // 0x149d514: r0 = SizedBox()
    //     0x149d514: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x149d518: mov             x3, x0
    // 0x149d51c: ldur            x0, [fp, #-0x18]
    // 0x149d520: stur            x3, [fp, #-0x30]
    // 0x149d524: StoreField: r3->field_f = r0
    //     0x149d524: stur            w0, [x3, #0xf]
    // 0x149d528: ldur            x0, [fp, #-0x28]
    // 0x149d52c: StoreField: r3->field_b = r0
    //     0x149d52c: stur            w0, [x3, #0xb]
    // 0x149d530: r1 = Null
    //     0x149d530: mov             x1, NULL
    // 0x149d534: r2 = 6
    //     0x149d534: movz            x2, #0x6
    // 0x149d538: r0 = AllocateArray()
    //     0x149d538: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149d53c: mov             x2, x0
    // 0x149d540: ldur            x0, [fp, #-0x10]
    // 0x149d544: stur            x2, [fp, #-0x18]
    // 0x149d548: StoreField: r2->field_f = r0
    //     0x149d548: stur            w0, [x2, #0xf]
    // 0x149d54c: r16 = Instance_SizedBox
    //     0x149d54c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x149d550: ldr             x16, [x16, #0x998]
    // 0x149d554: StoreField: r2->field_13 = r16
    //     0x149d554: stur            w16, [x2, #0x13]
    // 0x149d558: ldur            x0, [fp, #-0x30]
    // 0x149d55c: ArrayStore: r2[0] = r0  ; List_4
    //     0x149d55c: stur            w0, [x2, #0x17]
    // 0x149d560: r1 = <Widget>
    //     0x149d560: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x149d564: r0 = AllocateGrowableArray()
    //     0x149d564: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x149d568: mov             x1, x0
    // 0x149d56c: ldur            x0, [fp, #-0x18]
    // 0x149d570: stur            x1, [fp, #-0x10]
    // 0x149d574: StoreField: r1->field_f = r0
    //     0x149d574: stur            w0, [x1, #0xf]
    // 0x149d578: r0 = 6
    //     0x149d578: movz            x0, #0x6
    // 0x149d57c: StoreField: r1->field_b = r0
    //     0x149d57c: stur            w0, [x1, #0xb]
    // 0x149d580: r0 = Row()
    //     0x149d580: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x149d584: mov             x1, x0
    // 0x149d588: r0 = Instance_Axis
    //     0x149d588: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x149d58c: stur            x1, [fp, #-0x18]
    // 0x149d590: StoreField: r1->field_f = r0
    //     0x149d590: stur            w0, [x1, #0xf]
    // 0x149d594: r0 = Instance_MainAxisAlignment
    //     0x149d594: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x149d598: ldr             x0, [x0, #0xa08]
    // 0x149d59c: StoreField: r1->field_13 = r0
    //     0x149d59c: stur            w0, [x1, #0x13]
    // 0x149d5a0: r2 = Instance_MainAxisSize
    //     0x149d5a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x149d5a4: ldr             x2, [x2, #0xa10]
    // 0x149d5a8: ArrayStore: r1[0] = r2  ; List_4
    //     0x149d5a8: stur            w2, [x1, #0x17]
    // 0x149d5ac: r3 = Instance_CrossAxisAlignment
    //     0x149d5ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x149d5b0: ldr             x3, [x3, #0xa18]
    // 0x149d5b4: StoreField: r1->field_1b = r3
    //     0x149d5b4: stur            w3, [x1, #0x1b]
    // 0x149d5b8: r3 = Instance_VerticalDirection
    //     0x149d5b8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x149d5bc: ldr             x3, [x3, #0xa20]
    // 0x149d5c0: StoreField: r1->field_23 = r3
    //     0x149d5c0: stur            w3, [x1, #0x23]
    // 0x149d5c4: r4 = Instance_Clip
    //     0x149d5c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x149d5c8: ldr             x4, [x4, #0x38]
    // 0x149d5cc: StoreField: r1->field_2b = r4
    //     0x149d5cc: stur            w4, [x1, #0x2b]
    // 0x149d5d0: StoreField: r1->field_2f = rZR
    //     0x149d5d0: stur            xzr, [x1, #0x2f]
    // 0x149d5d4: ldur            x5, [fp, #-0x10]
    // 0x149d5d8: StoreField: r1->field_b = r5
    //     0x149d5d8: stur            w5, [x1, #0xb]
    // 0x149d5dc: r0 = Padding()
    //     0x149d5dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149d5e0: mov             x1, x0
    // 0x149d5e4: r0 = Instance_EdgeInsets
    //     0x149d5e4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x149d5e8: ldr             x0, [x0, #0xf98]
    // 0x149d5ec: stur            x1, [fp, #-0x10]
    // 0x149d5f0: StoreField: r1->field_f = r0
    //     0x149d5f0: stur            w0, [x1, #0xf]
    // 0x149d5f4: ldur            x0, [fp, #-0x18]
    // 0x149d5f8: StoreField: r1->field_b = r0
    //     0x149d5f8: stur            w0, [x1, #0xb]
    // 0x149d5fc: r0 = Container()
    //     0x149d5fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x149d600: stur            x0, [fp, #-0x18]
    // 0x149d604: ldur            x16, [fp, #-0x20]
    // 0x149d608: ldur            lr, [fp, #-0x10]
    // 0x149d60c: stp             lr, x16, [SP]
    // 0x149d610: mov             x1, x0
    // 0x149d614: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x149d614: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x149d618: ldr             x4, [x4, #0x88]
    // 0x149d61c: r0 = Container()
    //     0x149d61c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x149d620: r0 = Padding()
    //     0x149d620: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149d624: mov             x1, x0
    // 0x149d628: r0 = Instance_EdgeInsets
    //     0x149d628: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x149d62c: ldr             x0, [x0, #0x240]
    // 0x149d630: stur            x1, [fp, #-0x10]
    // 0x149d634: StoreField: r1->field_f = r0
    //     0x149d634: stur            w0, [x1, #0xf]
    // 0x149d638: ldur            x0, [fp, #-0x18]
    // 0x149d63c: StoreField: r1->field_b = r0
    //     0x149d63c: stur            w0, [x1, #0xb]
    // 0x149d640: r0 = Radius()
    //     0x149d640: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x149d644: d0 = 20.000000
    //     0x149d644: fmov            d0, #20.00000000
    // 0x149d648: stur            x0, [fp, #-0x18]
    // 0x149d64c: StoreField: r0->field_7 = d0
    //     0x149d64c: stur            d0, [x0, #7]
    // 0x149d650: StoreField: r0->field_f = d0
    //     0x149d650: stur            d0, [x0, #0xf]
    // 0x149d654: r0 = BorderRadius()
    //     0x149d654: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x149d658: mov             x1, x0
    // 0x149d65c: ldur            x0, [fp, #-0x18]
    // 0x149d660: stur            x1, [fp, #-0x20]
    // 0x149d664: StoreField: r1->field_7 = r0
    //     0x149d664: stur            w0, [x1, #7]
    // 0x149d668: StoreField: r1->field_b = r0
    //     0x149d668: stur            w0, [x1, #0xb]
    // 0x149d66c: StoreField: r1->field_f = r0
    //     0x149d66c: stur            w0, [x1, #0xf]
    // 0x149d670: StoreField: r1->field_13 = r0
    //     0x149d670: stur            w0, [x1, #0x13]
    // 0x149d674: r0 = BoxDecoration()
    //     0x149d674: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x149d678: mov             x2, x0
    // 0x149d67c: r0 = Instance_Color
    //     0x149d67c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x149d680: stur            x2, [fp, #-0x18]
    // 0x149d684: StoreField: r2->field_7 = r0
    //     0x149d684: stur            w0, [x2, #7]
    // 0x149d688: ldur            x0, [fp, #-0x20]
    // 0x149d68c: StoreField: r2->field_13 = r0
    //     0x149d68c: stur            w0, [x2, #0x13]
    // 0x149d690: r0 = Instance_BoxShape
    //     0x149d690: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x149d694: ldr             x0, [x0, #0x80]
    // 0x149d698: StoreField: r2->field_23 = r0
    //     0x149d698: stur            w0, [x2, #0x23]
    // 0x149d69c: ldur            x3, [fp, #-8]
    // 0x149d6a0: LoadField: r1 = r3->field_13
    //     0x149d6a0: ldur            w1, [x3, #0x13]
    // 0x149d6a4: DecompressPointer r1
    //     0x149d6a4: add             x1, x1, HEAP, lsl #32
    // 0x149d6a8: r0 = of()
    //     0x149d6a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149d6ac: LoadField: r1 = r0->field_87
    //     0x149d6ac: ldur            w1, [x0, #0x87]
    // 0x149d6b0: DecompressPointer r1
    //     0x149d6b0: add             x1, x1, HEAP, lsl #32
    // 0x149d6b4: LoadField: r0 = r1->field_7
    //     0x149d6b4: ldur            w0, [x1, #7]
    // 0x149d6b8: DecompressPointer r0
    //     0x149d6b8: add             x0, x0, HEAP, lsl #32
    // 0x149d6bc: r16 = Instance_Color
    //     0x149d6bc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149d6c0: r30 = 21.000000
    //     0x149d6c0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x149d6c4: ldr             lr, [lr, #0x9b0]
    // 0x149d6c8: stp             lr, x16, [SP]
    // 0x149d6cc: mov             x1, x0
    // 0x149d6d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x149d6d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x149d6d4: ldr             x4, [x4, #0x9b8]
    // 0x149d6d8: r0 = copyWith()
    //     0x149d6d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149d6dc: stur            x0, [fp, #-0x20]
    // 0x149d6e0: r0 = Text()
    //     0x149d6e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149d6e4: mov             x2, x0
    // 0x149d6e8: r0 = "Replacement order details"
    //     0x149d6e8: add             x0, PP, #0x38, lsl #12  ; [pp+0x380a8] "Replacement order details"
    //     0x149d6ec: ldr             x0, [x0, #0xa8]
    // 0x149d6f0: stur            x2, [fp, #-0x28]
    // 0x149d6f4: StoreField: r2->field_b = r0
    //     0x149d6f4: stur            w0, [x2, #0xb]
    // 0x149d6f8: ldur            x0, [fp, #-0x20]
    // 0x149d6fc: StoreField: r2->field_13 = r0
    //     0x149d6fc: stur            w0, [x2, #0x13]
    // 0x149d700: ldur            x0, [fp, #-8]
    // 0x149d704: LoadField: r1 = r0->field_f
    //     0x149d704: ldur            w1, [x0, #0xf]
    // 0x149d708: DecompressPointer r1
    //     0x149d708: add             x1, x1, HEAP, lsl #32
    // 0x149d70c: r0 = controller()
    //     0x149d70c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149d710: LoadField: r1 = r0->field_4b
    //     0x149d710: ldur            w1, [x0, #0x4b]
    // 0x149d714: DecompressPointer r1
    //     0x149d714: add             x1, x1, HEAP, lsl #32
    // 0x149d718: r0 = value()
    //     0x149d718: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149d71c: LoadField: r1 = r0->field_b
    //     0x149d71c: ldur            w1, [x0, #0xb]
    // 0x149d720: DecompressPointer r1
    //     0x149d720: add             x1, x1, HEAP, lsl #32
    // 0x149d724: cmp             w1, NULL
    // 0x149d728: b.ne            #0x149d734
    // 0x149d72c: r3 = Null
    //     0x149d72c: mov             x3, NULL
    // 0x149d730: b               #0x149d75c
    // 0x149d734: LoadField: r0 = r1->field_2f
    //     0x149d734: ldur            w0, [x1, #0x2f]
    // 0x149d738: DecompressPointer r0
    //     0x149d738: add             x0, x0, HEAP, lsl #32
    // 0x149d73c: cmp             w0, NULL
    // 0x149d740: b.ne            #0x149d74c
    // 0x149d744: r0 = Null
    //     0x149d744: mov             x0, NULL
    // 0x149d748: b               #0x149d758
    // 0x149d74c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x149d74c: ldur            w1, [x0, #0x17]
    // 0x149d750: DecompressPointer r1
    //     0x149d750: add             x1, x1, HEAP, lsl #32
    // 0x149d754: mov             x0, x1
    // 0x149d758: mov             x3, x0
    // 0x149d75c: ldur            x0, [fp, #-8]
    // 0x149d760: stur            x3, [fp, #-0x20]
    // 0x149d764: r1 = Null
    //     0x149d764: mov             x1, NULL
    // 0x149d768: r2 = 4
    //     0x149d768: movz            x2, #0x4
    // 0x149d76c: r0 = AllocateArray()
    //     0x149d76c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149d770: mov             x1, x0
    // 0x149d774: ldur            x0, [fp, #-0x20]
    // 0x149d778: StoreField: r1->field_f = r0
    //     0x149d778: stur            w0, [x1, #0xf]
    // 0x149d77c: r16 = " :"
    //     0x149d77c: add             x16, PP, #0x40, lsl #12  ; [pp+0x407b8] " :"
    //     0x149d780: ldr             x16, [x16, #0x7b8]
    // 0x149d784: StoreField: r1->field_13 = r16
    //     0x149d784: stur            w16, [x1, #0x13]
    // 0x149d788: str             x1, [SP]
    // 0x149d78c: r0 = _interpolate()
    //     0x149d78c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x149d790: mov             x1, x0
    // 0x149d794: r0 = capitalizeFirstWord()
    //     0x149d794: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x149d798: ldur            x2, [fp, #-8]
    // 0x149d79c: stur            x0, [fp, #-0x20]
    // 0x149d7a0: LoadField: r1 = r2->field_13
    //     0x149d7a0: ldur            w1, [x2, #0x13]
    // 0x149d7a4: DecompressPointer r1
    //     0x149d7a4: add             x1, x1, HEAP, lsl #32
    // 0x149d7a8: r0 = of()
    //     0x149d7a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149d7ac: LoadField: r1 = r0->field_87
    //     0x149d7ac: ldur            w1, [x0, #0x87]
    // 0x149d7b0: DecompressPointer r1
    //     0x149d7b0: add             x1, x1, HEAP, lsl #32
    // 0x149d7b4: LoadField: r0 = r1->field_7
    //     0x149d7b4: ldur            w0, [x1, #7]
    // 0x149d7b8: DecompressPointer r0
    //     0x149d7b8: add             x0, x0, HEAP, lsl #32
    // 0x149d7bc: stur            x0, [fp, #-0x30]
    // 0x149d7c0: r1 = Instance_Color
    //     0x149d7c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149d7c4: d0 = 0.700000
    //     0x149d7c4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149d7c8: ldr             d0, [x17, #0xf48]
    // 0x149d7cc: r0 = withOpacity()
    //     0x149d7cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149d7d0: r16 = 14.000000
    //     0x149d7d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x149d7d4: ldr             x16, [x16, #0x1d8]
    // 0x149d7d8: stp             x0, x16, [SP]
    // 0x149d7dc: ldur            x1, [fp, #-0x30]
    // 0x149d7e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x149d7e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x149d7e4: ldr             x4, [x4, #0xaa0]
    // 0x149d7e8: r0 = copyWith()
    //     0x149d7e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149d7ec: stur            x0, [fp, #-0x30]
    // 0x149d7f0: r0 = Text()
    //     0x149d7f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149d7f4: mov             x1, x0
    // 0x149d7f8: ldur            x0, [fp, #-0x20]
    // 0x149d7fc: stur            x1, [fp, #-0x38]
    // 0x149d800: StoreField: r1->field_b = r0
    //     0x149d800: stur            w0, [x1, #0xb]
    // 0x149d804: ldur            x0, [fp, #-0x30]
    // 0x149d808: StoreField: r1->field_13 = r0
    //     0x149d808: stur            w0, [x1, #0x13]
    // 0x149d80c: r0 = Padding()
    //     0x149d80c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149d810: mov             x2, x0
    // 0x149d814: r0 = Instance_EdgeInsets
    //     0x149d814: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0x149d818: ldr             x0, [x0, #0x100]
    // 0x149d81c: stur            x2, [fp, #-0x20]
    // 0x149d820: StoreField: r2->field_f = r0
    //     0x149d820: stur            w0, [x2, #0xf]
    // 0x149d824: ldur            x0, [fp, #-0x38]
    // 0x149d828: StoreField: r2->field_b = r0
    //     0x149d828: stur            w0, [x2, #0xb]
    // 0x149d82c: ldur            x0, [fp, #-8]
    // 0x149d830: LoadField: r1 = r0->field_f
    //     0x149d830: ldur            w1, [x0, #0xf]
    // 0x149d834: DecompressPointer r1
    //     0x149d834: add             x1, x1, HEAP, lsl #32
    // 0x149d838: r0 = controller()
    //     0x149d838: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149d83c: LoadField: r1 = r0->field_4b
    //     0x149d83c: ldur            w1, [x0, #0x4b]
    // 0x149d840: DecompressPointer r1
    //     0x149d840: add             x1, x1, HEAP, lsl #32
    // 0x149d844: r0 = value()
    //     0x149d844: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149d848: LoadField: r1 = r0->field_b
    //     0x149d848: ldur            w1, [x0, #0xb]
    // 0x149d84c: DecompressPointer r1
    //     0x149d84c: add             x1, x1, HEAP, lsl #32
    // 0x149d850: cmp             w1, NULL
    // 0x149d854: b.ne            #0x149d860
    // 0x149d858: r5 = Null
    //     0x149d858: mov             x5, NULL
    // 0x149d85c: b               #0x149d888
    // 0x149d860: LoadField: r0 = r1->field_2f
    //     0x149d860: ldur            w0, [x1, #0x2f]
    // 0x149d864: DecompressPointer r0
    //     0x149d864: add             x0, x0, HEAP, lsl #32
    // 0x149d868: cmp             w0, NULL
    // 0x149d86c: b.ne            #0x149d878
    // 0x149d870: r0 = Null
    //     0x149d870: mov             x0, NULL
    // 0x149d874: b               #0x149d884
    // 0x149d878: LoadField: r1 = r0->field_b
    //     0x149d878: ldur            w1, [x0, #0xb]
    // 0x149d87c: DecompressPointer r1
    //     0x149d87c: add             x1, x1, HEAP, lsl #32
    // 0x149d880: LoadField: r0 = r1->field_b
    //     0x149d880: ldur            w0, [x1, #0xb]
    // 0x149d884: mov             x5, x0
    // 0x149d888: ldur            x0, [fp, #-8]
    // 0x149d88c: mov             x2, x0
    // 0x149d890: stur            x5, [fp, #-0x30]
    // 0x149d894: r1 = Function '<anonymous closure>':.
    //     0x149d894: add             x1, PP, #0x40, lsl #12  ; [pp+0x407c0] AnonymousClosure: (0x149e458), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x14e3e60)
    //     0x149d898: ldr             x1, [x1, #0x7c0]
    // 0x149d89c: r0 = AllocateClosure()
    //     0x149d89c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149d8a0: stur            x0, [fp, #-0x38]
    // 0x149d8a4: r0 = GridView()
    //     0x149d8a4: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0x149d8a8: mov             x1, x0
    // 0x149d8ac: ldur            x3, [fp, #-0x38]
    // 0x149d8b0: ldur            x5, [fp, #-0x30]
    // 0x149d8b4: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0x149d8b4: add             x2, PP, #0x38, lsl #12  ; [pp+0x380b8] Obj!SliverGridDelegateWithFixedCrossAxisCount@d564e1
    //     0x149d8b8: ldr             x2, [x2, #0xb8]
    // 0x149d8bc: stur            x0, [fp, #-0x30]
    // 0x149d8c0: r0 = GridView.builder()
    //     0x149d8c0: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0x149d8c4: r0 = SizedBox()
    //     0x149d8c4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x149d8c8: mov             x1, x0
    // 0x149d8cc: r0 = 100.000000
    //     0x149d8cc: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x149d8d0: stur            x1, [fp, #-0x38]
    // 0x149d8d4: StoreField: r1->field_13 = r0
    //     0x149d8d4: stur            w0, [x1, #0x13]
    // 0x149d8d8: ldur            x0, [fp, #-0x30]
    // 0x149d8dc: StoreField: r1->field_b = r0
    //     0x149d8dc: stur            w0, [x1, #0xb]
    // 0x149d8e0: r0 = Padding()
    //     0x149d8e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149d8e4: mov             x2, x0
    // 0x149d8e8: r0 = Instance_EdgeInsets
    //     0x149d8e8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x149d8ec: ldr             x0, [x0, #0x868]
    // 0x149d8f0: stur            x2, [fp, #-0x30]
    // 0x149d8f4: StoreField: r2->field_f = r0
    //     0x149d8f4: stur            w0, [x2, #0xf]
    // 0x149d8f8: ldur            x0, [fp, #-0x38]
    // 0x149d8fc: StoreField: r2->field_b = r0
    //     0x149d8fc: stur            w0, [x2, #0xb]
    // 0x149d900: ldur            x0, [fp, #-8]
    // 0x149d904: LoadField: r1 = r0->field_f
    //     0x149d904: ldur            w1, [x0, #0xf]
    // 0x149d908: DecompressPointer r1
    //     0x149d908: add             x1, x1, HEAP, lsl #32
    // 0x149d90c: r0 = controller()
    //     0x149d90c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149d910: LoadField: r1 = r0->field_4b
    //     0x149d910: ldur            w1, [x0, #0x4b]
    // 0x149d914: DecompressPointer r1
    //     0x149d914: add             x1, x1, HEAP, lsl #32
    // 0x149d918: r0 = value()
    //     0x149d918: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149d91c: LoadField: r1 = r0->field_b
    //     0x149d91c: ldur            w1, [x0, #0xb]
    // 0x149d920: DecompressPointer r1
    //     0x149d920: add             x1, x1, HEAP, lsl #32
    // 0x149d924: cmp             w1, NULL
    // 0x149d928: b.ne            #0x149d934
    // 0x149d92c: r0 = Null
    //     0x149d92c: mov             x0, NULL
    // 0x149d930: b               #0x149d97c
    // 0x149d934: LoadField: r0 = r1->field_2f
    //     0x149d934: ldur            w0, [x1, #0x2f]
    // 0x149d938: DecompressPointer r0
    //     0x149d938: add             x0, x0, HEAP, lsl #32
    // 0x149d93c: cmp             w0, NULL
    // 0x149d940: b.ne            #0x149d94c
    // 0x149d944: r0 = Null
    //     0x149d944: mov             x0, NULL
    // 0x149d948: b               #0x149d97c
    // 0x149d94c: LoadField: r1 = r0->field_f
    //     0x149d94c: ldur            w1, [x0, #0xf]
    // 0x149d950: DecompressPointer r1
    //     0x149d950: add             x1, x1, HEAP, lsl #32
    // 0x149d954: cmp             w1, NULL
    // 0x149d958: b.ne            #0x149d964
    // 0x149d95c: r0 = Null
    //     0x149d95c: mov             x0, NULL
    // 0x149d960: b               #0x149d97c
    // 0x149d964: LoadField: r0 = r1->field_7
    //     0x149d964: ldur            w0, [x1, #7]
    // 0x149d968: cbnz            w0, #0x149d974
    // 0x149d96c: r1 = false
    //     0x149d96c: add             x1, NULL, #0x30  ; false
    // 0x149d970: b               #0x149d978
    // 0x149d974: r1 = true
    //     0x149d974: add             x1, NULL, #0x20  ; true
    // 0x149d978: mov             x0, x1
    // 0x149d97c: cmp             w0, NULL
    // 0x149d980: b.ne            #0x149d988
    // 0x149d984: r0 = false
    //     0x149d984: add             x0, NULL, #0x30  ; false
    // 0x149d988: ldur            x2, [fp, #-8]
    // 0x149d98c: stur            x0, [fp, #-0x38]
    // 0x149d990: LoadField: r1 = r2->field_13
    //     0x149d990: ldur            w1, [x2, #0x13]
    // 0x149d994: DecompressPointer r1
    //     0x149d994: add             x1, x1, HEAP, lsl #32
    // 0x149d998: r0 = of()
    //     0x149d998: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149d99c: LoadField: r1 = r0->field_87
    //     0x149d99c: ldur            w1, [x0, #0x87]
    // 0x149d9a0: DecompressPointer r1
    //     0x149d9a0: add             x1, x1, HEAP, lsl #32
    // 0x149d9a4: LoadField: r0 = r1->field_2b
    //     0x149d9a4: ldur            w0, [x1, #0x2b]
    // 0x149d9a8: DecompressPointer r0
    //     0x149d9a8: add             x0, x0, HEAP, lsl #32
    // 0x149d9ac: stur            x0, [fp, #-0x40]
    // 0x149d9b0: r1 = Instance_Color
    //     0x149d9b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149d9b4: d0 = 0.700000
    //     0x149d9b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149d9b8: ldr             d0, [x17, #0xf48]
    // 0x149d9bc: r0 = withOpacity()
    //     0x149d9bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149d9c0: r16 = 12.000000
    //     0x149d9c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149d9c4: ldr             x16, [x16, #0x9e8]
    // 0x149d9c8: stp             x16, x0, [SP, #8]
    // 0x149d9cc: r16 = Instance_TextDecoration
    //     0x149d9cc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0x149d9d0: ldr             x16, [x16, #0x10]
    // 0x149d9d4: str             x16, [SP]
    // 0x149d9d8: ldur            x1, [fp, #-0x40]
    // 0x149d9dc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0x149d9dc: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0x149d9e0: ldr             x4, [x4, #0x7c8]
    // 0x149d9e4: r0 = copyWith()
    //     0x149d9e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149d9e8: stur            x0, [fp, #-0x40]
    // 0x149d9ec: r0 = Text()
    //     0x149d9ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149d9f0: mov             x1, x0
    // 0x149d9f4: r0 = "View Size Chart"
    //     0x149d9f4: add             x0, PP, #0x40, lsl #12  ; [pp+0x407d0] "View Size Chart"
    //     0x149d9f8: ldr             x0, [x0, #0x7d0]
    // 0x149d9fc: stur            x1, [fp, #-0x48]
    // 0x149da00: StoreField: r1->field_b = r0
    //     0x149da00: stur            w0, [x1, #0xb]
    // 0x149da04: ldur            x0, [fp, #-0x40]
    // 0x149da08: StoreField: r1->field_13 = r0
    //     0x149da08: stur            w0, [x1, #0x13]
    // 0x149da0c: r0 = InkWell()
    //     0x149da0c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x149da10: mov             x3, x0
    // 0x149da14: ldur            x0, [fp, #-0x48]
    // 0x149da18: stur            x3, [fp, #-0x40]
    // 0x149da1c: StoreField: r3->field_b = r0
    //     0x149da1c: stur            w0, [x3, #0xb]
    // 0x149da20: ldur            x2, [fp, #-8]
    // 0x149da24: r1 = Function '<anonymous closure>':.
    //     0x149da24: add             x1, PP, #0x40, lsl #12  ; [pp+0x407d8] AnonymousClosure: (0x149e340), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x14e3e60)
    //     0x149da28: ldr             x1, [x1, #0x7d8]
    // 0x149da2c: r0 = AllocateClosure()
    //     0x149da2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149da30: mov             x1, x0
    // 0x149da34: ldur            x0, [fp, #-0x40]
    // 0x149da38: StoreField: r0->field_f = r1
    //     0x149da38: stur            w1, [x0, #0xf]
    // 0x149da3c: r1 = true
    //     0x149da3c: add             x1, NULL, #0x20  ; true
    // 0x149da40: StoreField: r0->field_43 = r1
    //     0x149da40: stur            w1, [x0, #0x43]
    // 0x149da44: r2 = Instance_BoxShape
    //     0x149da44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x149da48: ldr             x2, [x2, #0x80]
    // 0x149da4c: StoreField: r0->field_47 = r2
    //     0x149da4c: stur            w2, [x0, #0x47]
    // 0x149da50: StoreField: r0->field_6f = r1
    //     0x149da50: stur            w1, [x0, #0x6f]
    // 0x149da54: r2 = false
    //     0x149da54: add             x2, NULL, #0x30  ; false
    // 0x149da58: StoreField: r0->field_73 = r2
    //     0x149da58: stur            w2, [x0, #0x73]
    // 0x149da5c: StoreField: r0->field_83 = r1
    //     0x149da5c: stur            w1, [x0, #0x83]
    // 0x149da60: StoreField: r0->field_7b = r2
    //     0x149da60: stur            w2, [x0, #0x7b]
    // 0x149da64: r0 = Center()
    //     0x149da64: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x149da68: mov             x1, x0
    // 0x149da6c: r0 = Instance_Alignment
    //     0x149da6c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x149da70: ldr             x0, [x0, #0xb10]
    // 0x149da74: stur            x1, [fp, #-0x48]
    // 0x149da78: StoreField: r1->field_f = r0
    //     0x149da78: stur            w0, [x1, #0xf]
    // 0x149da7c: ldur            x0, [fp, #-0x40]
    // 0x149da80: StoreField: r1->field_b = r0
    //     0x149da80: stur            w0, [x1, #0xb]
    // 0x149da84: r0 = Visibility()
    //     0x149da84: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x149da88: mov             x2, x0
    // 0x149da8c: ldur            x0, [fp, #-0x48]
    // 0x149da90: stur            x2, [fp, #-0x40]
    // 0x149da94: StoreField: r2->field_b = r0
    //     0x149da94: stur            w0, [x2, #0xb]
    // 0x149da98: r0 = Instance_SizedBox
    //     0x149da98: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x149da9c: StoreField: r2->field_f = r0
    //     0x149da9c: stur            w0, [x2, #0xf]
    // 0x149daa0: ldur            x1, [fp, #-0x38]
    // 0x149daa4: StoreField: r2->field_13 = r1
    //     0x149daa4: stur            w1, [x2, #0x13]
    // 0x149daa8: r3 = false
    //     0x149daa8: add             x3, NULL, #0x30  ; false
    // 0x149daac: ArrayStore: r2[0] = r3  ; List_4
    //     0x149daac: stur            w3, [x2, #0x17]
    // 0x149dab0: StoreField: r2->field_1b = r3
    //     0x149dab0: stur            w3, [x2, #0x1b]
    // 0x149dab4: StoreField: r2->field_1f = r3
    //     0x149dab4: stur            w3, [x2, #0x1f]
    // 0x149dab8: StoreField: r2->field_23 = r3
    //     0x149dab8: stur            w3, [x2, #0x23]
    // 0x149dabc: StoreField: r2->field_27 = r3
    //     0x149dabc: stur            w3, [x2, #0x27]
    // 0x149dac0: StoreField: r2->field_2b = r3
    //     0x149dac0: stur            w3, [x2, #0x2b]
    // 0x149dac4: ldur            x4, [fp, #-8]
    // 0x149dac8: LoadField: r1 = r4->field_f
    //     0x149dac8: ldur            w1, [x4, #0xf]
    // 0x149dacc: DecompressPointer r1
    //     0x149dacc: add             x1, x1, HEAP, lsl #32
    // 0x149dad0: r0 = controller()
    //     0x149dad0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149dad4: LoadField: r1 = r0->field_4b
    //     0x149dad4: ldur            w1, [x0, #0x4b]
    // 0x149dad8: DecompressPointer r1
    //     0x149dad8: add             x1, x1, HEAP, lsl #32
    // 0x149dadc: r0 = value()
    //     0x149dadc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149dae0: LoadField: r1 = r0->field_b
    //     0x149dae0: ldur            w1, [x0, #0xb]
    // 0x149dae4: DecompressPointer r1
    //     0x149dae4: add             x1, x1, HEAP, lsl #32
    // 0x149dae8: cmp             w1, NULL
    // 0x149daec: b.ne            #0x149daf8
    // 0x149daf0: r0 = Null
    //     0x149daf0: mov             x0, NULL
    // 0x149daf4: b               #0x149db30
    // 0x149daf8: LoadField: r0 = r1->field_2f
    //     0x149daf8: ldur            w0, [x1, #0x2f]
    // 0x149dafc: DecompressPointer r0
    //     0x149dafc: add             x0, x0, HEAP, lsl #32
    // 0x149db00: cmp             w0, NULL
    // 0x149db04: b.ne            #0x149db10
    // 0x149db08: r0 = Null
    //     0x149db08: mov             x0, NULL
    // 0x149db0c: b               #0x149db30
    // 0x149db10: LoadField: r1 = r0->field_13
    //     0x149db10: ldur            w1, [x0, #0x13]
    // 0x149db14: DecompressPointer r1
    //     0x149db14: add             x1, x1, HEAP, lsl #32
    // 0x149db18: LoadField: r0 = r1->field_b
    //     0x149db18: ldur            w0, [x1, #0xb]
    // 0x149db1c: cbnz            w0, #0x149db28
    // 0x149db20: r1 = false
    //     0x149db20: add             x1, NULL, #0x30  ; false
    // 0x149db24: b               #0x149db2c
    // 0x149db28: r1 = true
    //     0x149db28: add             x1, NULL, #0x20  ; true
    // 0x149db2c: mov             x0, x1
    // 0x149db30: cmp             w0, NULL
    // 0x149db34: b.ne            #0x149db40
    // 0x149db38: r7 = false
    //     0x149db38: add             x7, NULL, #0x30  ; false
    // 0x149db3c: b               #0x149db44
    // 0x149db40: mov             x7, x0
    // 0x149db44: ldur            x2, [fp, #-8]
    // 0x149db48: ldur            x6, [fp, #-0x10]
    // 0x149db4c: ldur            x5, [fp, #-0x28]
    // 0x149db50: ldur            x4, [fp, #-0x20]
    // 0x149db54: ldur            x3, [fp, #-0x30]
    // 0x149db58: ldur            x0, [fp, #-0x40]
    // 0x149db5c: stur            x7, [fp, #-0x38]
    // 0x149db60: LoadField: r1 = r2->field_13
    //     0x149db60: ldur            w1, [x2, #0x13]
    // 0x149db64: DecompressPointer r1
    //     0x149db64: add             x1, x1, HEAP, lsl #32
    // 0x149db68: r0 = of()
    //     0x149db68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149db6c: LoadField: r1 = r0->field_87
    //     0x149db6c: ldur            w1, [x0, #0x87]
    // 0x149db70: DecompressPointer r1
    //     0x149db70: add             x1, x1, HEAP, lsl #32
    // 0x149db74: LoadField: r0 = r1->field_7
    //     0x149db74: ldur            w0, [x1, #7]
    // 0x149db78: DecompressPointer r0
    //     0x149db78: add             x0, x0, HEAP, lsl #32
    // 0x149db7c: stur            x0, [fp, #-0x48]
    // 0x149db80: r1 = Instance_Color
    //     0x149db80: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149db84: d0 = 0.700000
    //     0x149db84: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149db88: ldr             d0, [x17, #0xf48]
    // 0x149db8c: r0 = withOpacity()
    //     0x149db8c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149db90: r16 = 14.000000
    //     0x149db90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x149db94: ldr             x16, [x16, #0x1d8]
    // 0x149db98: stp             x16, x0, [SP]
    // 0x149db9c: ldur            x1, [fp, #-0x48]
    // 0x149dba0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x149dba0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x149dba4: ldr             x4, [x4, #0x9b8]
    // 0x149dba8: r0 = copyWith()
    //     0x149dba8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149dbac: stur            x0, [fp, #-0x48]
    // 0x149dbb0: r0 = TextSpan()
    //     0x149dbb0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x149dbb4: mov             x3, x0
    // 0x149dbb8: r0 = "Size with different prices"
    //     0x149dbb8: add             x0, PP, #0x38, lsl #12  ; [pp+0x380d0] "Size with different prices"
    //     0x149dbbc: ldr             x0, [x0, #0xd0]
    // 0x149dbc0: stur            x3, [fp, #-0x50]
    // 0x149dbc4: StoreField: r3->field_b = r0
    //     0x149dbc4: stur            w0, [x3, #0xb]
    // 0x149dbc8: r0 = Instance__DeferringMouseCursor
    //     0x149dbc8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x149dbcc: ArrayStore: r3[0] = r0  ; List_4
    //     0x149dbcc: stur            w0, [x3, #0x17]
    // 0x149dbd0: ldur            x1, [fp, #-0x48]
    // 0x149dbd4: StoreField: r3->field_7 = r1
    //     0x149dbd4: stur            w1, [x3, #7]
    // 0x149dbd8: r1 = Null
    //     0x149dbd8: mov             x1, NULL
    // 0x149dbdc: r2 = 2
    //     0x149dbdc: movz            x2, #0x2
    // 0x149dbe0: r0 = AllocateArray()
    //     0x149dbe0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149dbe4: mov             x2, x0
    // 0x149dbe8: ldur            x0, [fp, #-0x50]
    // 0x149dbec: stur            x2, [fp, #-0x48]
    // 0x149dbf0: StoreField: r2->field_f = r0
    //     0x149dbf0: stur            w0, [x2, #0xf]
    // 0x149dbf4: r1 = <InlineSpan>
    //     0x149dbf4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x149dbf8: ldr             x1, [x1, #0xe40]
    // 0x149dbfc: r0 = AllocateGrowableArray()
    //     0x149dbfc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x149dc00: mov             x1, x0
    // 0x149dc04: ldur            x0, [fp, #-0x48]
    // 0x149dc08: stur            x1, [fp, #-0x50]
    // 0x149dc0c: StoreField: r1->field_f = r0
    //     0x149dc0c: stur            w0, [x1, #0xf]
    // 0x149dc10: r0 = 2
    //     0x149dc10: movz            x0, #0x2
    // 0x149dc14: StoreField: r1->field_b = r0
    //     0x149dc14: stur            w0, [x1, #0xb]
    // 0x149dc18: r0 = TextSpan()
    //     0x149dc18: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x149dc1c: mov             x1, x0
    // 0x149dc20: ldur            x0, [fp, #-0x50]
    // 0x149dc24: stur            x1, [fp, #-0x48]
    // 0x149dc28: StoreField: r1->field_f = r0
    //     0x149dc28: stur            w0, [x1, #0xf]
    // 0x149dc2c: r0 = Instance__DeferringMouseCursor
    //     0x149dc2c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x149dc30: ArrayStore: r1[0] = r0  ; List_4
    //     0x149dc30: stur            w0, [x1, #0x17]
    // 0x149dc34: r0 = RichText()
    //     0x149dc34: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x149dc38: mov             x1, x0
    // 0x149dc3c: ldur            x2, [fp, #-0x48]
    // 0x149dc40: stur            x0, [fp, #-0x48]
    // 0x149dc44: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x149dc44: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x149dc48: r0 = RichText()
    //     0x149dc48: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x149dc4c: ldur            x2, [fp, #-8]
    // 0x149dc50: LoadField: r1 = r2->field_13
    //     0x149dc50: ldur            w1, [x2, #0x13]
    // 0x149dc54: DecompressPointer r1
    //     0x149dc54: add             x1, x1, HEAP, lsl #32
    // 0x149dc58: r0 = of()
    //     0x149dc58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149dc5c: LoadField: r1 = r0->field_87
    //     0x149dc5c: ldur            w1, [x0, #0x87]
    // 0x149dc60: DecompressPointer r1
    //     0x149dc60: add             x1, x1, HEAP, lsl #32
    // 0x149dc64: LoadField: r0 = r1->field_2b
    //     0x149dc64: ldur            w0, [x1, #0x2b]
    // 0x149dc68: DecompressPointer r0
    //     0x149dc68: add             x0, x0, HEAP, lsl #32
    // 0x149dc6c: stur            x0, [fp, #-0x50]
    // 0x149dc70: r1 = Instance_Color
    //     0x149dc70: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149dc74: d0 = 0.400000
    //     0x149dc74: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x149dc78: r0 = withOpacity()
    //     0x149dc78: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149dc7c: r16 = 12.000000
    //     0x149dc7c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149dc80: ldr             x16, [x16, #0x9e8]
    // 0x149dc84: stp             x16, x0, [SP]
    // 0x149dc88: ldur            x1, [fp, #-0x50]
    // 0x149dc8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x149dc8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x149dc90: ldr             x4, [x4, #0x9b8]
    // 0x149dc94: r0 = copyWith()
    //     0x149dc94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149dc98: stur            x0, [fp, #-0x50]
    // 0x149dc9c: r0 = Text()
    //     0x149dc9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149dca0: mov             x2, x0
    // 0x149dca4: r0 = "Price of some variants/sizes of this product doesn’t match with the previously ordered product. Tap on the below button if you want to exchange with a different priced variant/size."
    //     0x149dca4: add             x0, PP, #0x38, lsl #12  ; [pp+0x380d8] "Price of some variants/sizes of this product doesn’t match with the previously ordered product. Tap on the below button if you want to exchange with a different priced variant/size."
    //     0x149dca8: ldr             x0, [x0, #0xd8]
    // 0x149dcac: stur            x2, [fp, #-0x58]
    // 0x149dcb0: StoreField: r2->field_b = r0
    //     0x149dcb0: stur            w0, [x2, #0xb]
    // 0x149dcb4: ldur            x0, [fp, #-0x50]
    // 0x149dcb8: StoreField: r2->field_13 = r0
    //     0x149dcb8: stur            w0, [x2, #0x13]
    // 0x149dcbc: ldur            x0, [fp, #-8]
    // 0x149dcc0: LoadField: r1 = r0->field_f
    //     0x149dcc0: ldur            w1, [x0, #0xf]
    // 0x149dcc4: DecompressPointer r1
    //     0x149dcc4: add             x1, x1, HEAP, lsl #32
    // 0x149dcc8: r0 = controller()
    //     0x149dcc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149dccc: LoadField: r1 = r0->field_57
    //     0x149dccc: ldur            w1, [x0, #0x57]
    // 0x149dcd0: DecompressPointer r1
    //     0x149dcd0: add             x1, x1, HEAP, lsl #32
    // 0x149dcd4: LoadField: r0 = r1->field_b
    //     0x149dcd4: ldur            w0, [x1, #0xb]
    // 0x149dcd8: cbnz            w0, #0x149dce4
    // 0x149dcdc: r3 = false
    //     0x149dcdc: add             x3, NULL, #0x30  ; false
    // 0x149dce0: b               #0x149dce8
    // 0x149dce4: r3 = true
    //     0x149dce4: add             x3, NULL, #0x20  ; true
    // 0x149dce8: stur            x3, [fp, #-0x50]
    // 0x149dcec: r1 = Null
    //     0x149dcec: mov             x1, NULL
    // 0x149dcf0: r2 = 4
    //     0x149dcf0: movz            x2, #0x4
    // 0x149dcf4: r0 = AllocateArray()
    //     0x149dcf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149dcf8: stur            x0, [fp, #-0x60]
    // 0x149dcfc: r16 = "Available sizes: "
    //     0x149dcfc: add             x16, PP, #0x38, lsl #12  ; [pp+0x380e0] "Available sizes: "
    //     0x149dd00: ldr             x16, [x16, #0xe0]
    // 0x149dd04: StoreField: r0->field_f = r16
    //     0x149dd04: stur            w16, [x0, #0xf]
    // 0x149dd08: ldur            x2, [fp, #-8]
    // 0x149dd0c: LoadField: r1 = r2->field_f
    //     0x149dd0c: ldur            w1, [x2, #0xf]
    // 0x149dd10: DecompressPointer r1
    //     0x149dd10: add             x1, x1, HEAP, lsl #32
    // 0x149dd14: r0 = controller()
    //     0x149dd14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149dd18: LoadField: r1 = r0->field_57
    //     0x149dd18: ldur            w1, [x0, #0x57]
    // 0x149dd1c: DecompressPointer r1
    //     0x149dd1c: add             x1, x1, HEAP, lsl #32
    // 0x149dd20: r16 = ", "
    //     0x149dd20: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x149dd24: str             x16, [SP]
    // 0x149dd28: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x149dd28: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x149dd2c: r0 = join()
    //     0x149dd2c: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0x149dd30: ldur            x1, [fp, #-0x60]
    // 0x149dd34: ArrayStore: r1[1] = r0  ; List_4
    //     0x149dd34: add             x25, x1, #0x13
    //     0x149dd38: str             w0, [x25]
    //     0x149dd3c: tbz             w0, #0, #0x149dd58
    //     0x149dd40: ldurb           w16, [x1, #-1]
    //     0x149dd44: ldurb           w17, [x0, #-1]
    //     0x149dd48: and             x16, x17, x16, lsr #2
    //     0x149dd4c: tst             x16, HEAP, lsr #32
    //     0x149dd50: b.eq            #0x149dd58
    //     0x149dd54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x149dd58: ldur            x16, [fp, #-0x60]
    // 0x149dd5c: str             x16, [SP]
    // 0x149dd60: r0 = _interpolate()
    //     0x149dd60: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x149dd64: ldur            x2, [fp, #-8]
    // 0x149dd68: stur            x0, [fp, #-0x60]
    // 0x149dd6c: LoadField: r1 = r2->field_13
    //     0x149dd6c: ldur            w1, [x2, #0x13]
    // 0x149dd70: DecompressPointer r1
    //     0x149dd70: add             x1, x1, HEAP, lsl #32
    // 0x149dd74: r0 = of()
    //     0x149dd74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149dd78: LoadField: r1 = r0->field_87
    //     0x149dd78: ldur            w1, [x0, #0x87]
    // 0x149dd7c: DecompressPointer r1
    //     0x149dd7c: add             x1, x1, HEAP, lsl #32
    // 0x149dd80: LoadField: r0 = r1->field_2b
    //     0x149dd80: ldur            w0, [x1, #0x2b]
    // 0x149dd84: DecompressPointer r0
    //     0x149dd84: add             x0, x0, HEAP, lsl #32
    // 0x149dd88: stur            x0, [fp, #-0x68]
    // 0x149dd8c: r1 = Instance_Color
    //     0x149dd8c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149dd90: d0 = 0.700000
    //     0x149dd90: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149dd94: ldr             d0, [x17, #0xf48]
    // 0x149dd98: r0 = withOpacity()
    //     0x149dd98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149dd9c: r16 = 12.000000
    //     0x149dd9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x149dda0: ldr             x16, [x16, #0x9e8]
    // 0x149dda4: stp             x16, x0, [SP]
    // 0x149dda8: ldur            x1, [fp, #-0x68]
    // 0x149ddac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x149ddac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x149ddb0: ldr             x4, [x4, #0x9b8]
    // 0x149ddb4: r0 = copyWith()
    //     0x149ddb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149ddb8: stur            x0, [fp, #-0x68]
    // 0x149ddbc: r0 = Text()
    //     0x149ddbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149ddc0: mov             x1, x0
    // 0x149ddc4: ldur            x0, [fp, #-0x60]
    // 0x149ddc8: stur            x1, [fp, #-0x70]
    // 0x149ddcc: StoreField: r1->field_b = r0
    //     0x149ddcc: stur            w0, [x1, #0xb]
    // 0x149ddd0: ldur            x0, [fp, #-0x68]
    // 0x149ddd4: StoreField: r1->field_13 = r0
    //     0x149ddd4: stur            w0, [x1, #0x13]
    // 0x149ddd8: r0 = Visibility()
    //     0x149ddd8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x149dddc: mov             x1, x0
    // 0x149dde0: ldur            x0, [fp, #-0x70]
    // 0x149dde4: stur            x1, [fp, #-0x60]
    // 0x149dde8: StoreField: r1->field_b = r0
    //     0x149dde8: stur            w0, [x1, #0xb]
    // 0x149ddec: r0 = Instance_SizedBox
    //     0x149ddec: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x149ddf0: StoreField: r1->field_f = r0
    //     0x149ddf0: stur            w0, [x1, #0xf]
    // 0x149ddf4: ldur            x2, [fp, #-0x50]
    // 0x149ddf8: StoreField: r1->field_13 = r2
    //     0x149ddf8: stur            w2, [x1, #0x13]
    // 0x149ddfc: r2 = false
    //     0x149ddfc: add             x2, NULL, #0x30  ; false
    // 0x149de00: ArrayStore: r1[0] = r2  ; List_4
    //     0x149de00: stur            w2, [x1, #0x17]
    // 0x149de04: StoreField: r1->field_1b = r2
    //     0x149de04: stur            w2, [x1, #0x1b]
    // 0x149de08: StoreField: r1->field_1f = r2
    //     0x149de08: stur            w2, [x1, #0x1f]
    // 0x149de0c: StoreField: r1->field_23 = r2
    //     0x149de0c: stur            w2, [x1, #0x23]
    // 0x149de10: StoreField: r1->field_27 = r2
    //     0x149de10: stur            w2, [x1, #0x27]
    // 0x149de14: StoreField: r1->field_2b = r2
    //     0x149de14: stur            w2, [x1, #0x2b]
    // 0x149de18: r16 = <EdgeInsets>
    //     0x149de18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x149de1c: ldr             x16, [x16, #0xda0]
    // 0x149de20: r30 = Instance_EdgeInsets
    //     0x149de20: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x149de24: ldr             lr, [lr, #0x1f0]
    // 0x149de28: stp             lr, x16, [SP]
    // 0x149de2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149de2c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149de30: r0 = all()
    //     0x149de30: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149de34: ldur            x2, [fp, #-8]
    // 0x149de38: stur            x0, [fp, #-0x50]
    // 0x149de3c: LoadField: r1 = r2->field_13
    //     0x149de3c: ldur            w1, [x2, #0x13]
    // 0x149de40: DecompressPointer r1
    //     0x149de40: add             x1, x1, HEAP, lsl #32
    // 0x149de44: r0 = of()
    //     0x149de44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149de48: LoadField: r1 = r0->field_5b
    //     0x149de48: ldur            w1, [x0, #0x5b]
    // 0x149de4c: DecompressPointer r1
    //     0x149de4c: add             x1, x1, HEAP, lsl #32
    // 0x149de50: r16 = <Color>
    //     0x149de50: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x149de54: ldr             x16, [x16, #0xf80]
    // 0x149de58: stp             x1, x16, [SP]
    // 0x149de5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149de5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149de60: r0 = all()
    //     0x149de60: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149de64: ldur            x2, [fp, #-8]
    // 0x149de68: stur            x0, [fp, #-0x68]
    // 0x149de6c: LoadField: r1 = r2->field_13
    //     0x149de6c: ldur            w1, [x2, #0x13]
    // 0x149de70: DecompressPointer r1
    //     0x149de70: add             x1, x1, HEAP, lsl #32
    // 0x149de74: r0 = of()
    //     0x149de74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149de78: LoadField: r1 = r0->field_5b
    //     0x149de78: ldur            w1, [x0, #0x5b]
    // 0x149de7c: DecompressPointer r1
    //     0x149de7c: add             x1, x1, HEAP, lsl #32
    // 0x149de80: r16 = <Color>
    //     0x149de80: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x149de84: ldr             x16, [x16, #0xf80]
    // 0x149de88: stp             x1, x16, [SP]
    // 0x149de8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149de8c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149de90: r0 = all()
    //     0x149de90: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149de94: stur            x0, [fp, #-0x70]
    // 0x149de98: r16 = <RoundedRectangleBorder>
    //     0x149de98: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x149de9c: ldr             x16, [x16, #0xf78]
    // 0x149dea0: r30 = Instance_RoundedRectangleBorder
    //     0x149dea0: add             lr, PP, #0x40, lsl #12  ; [pp+0x406d0] Obj!RoundedRectangleBorder@d5acc1
    //     0x149dea4: ldr             lr, [lr, #0x6d0]
    // 0x149dea8: stp             lr, x16, [SP]
    // 0x149deac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149deac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149deb0: r0 = all()
    //     0x149deb0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149deb4: stur            x0, [fp, #-0x78]
    // 0x149deb8: r0 = ButtonStyle()
    //     0x149deb8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x149debc: mov             x1, x0
    // 0x149dec0: ldur            x0, [fp, #-0x70]
    // 0x149dec4: stur            x1, [fp, #-0x80]
    // 0x149dec8: StoreField: r1->field_b = r0
    //     0x149dec8: stur            w0, [x1, #0xb]
    // 0x149decc: ldur            x0, [fp, #-0x68]
    // 0x149ded0: StoreField: r1->field_f = r0
    //     0x149ded0: stur            w0, [x1, #0xf]
    // 0x149ded4: ldur            x0, [fp, #-0x50]
    // 0x149ded8: StoreField: r1->field_23 = r0
    //     0x149ded8: stur            w0, [x1, #0x23]
    // 0x149dedc: ldur            x0, [fp, #-0x78]
    // 0x149dee0: StoreField: r1->field_43 = r0
    //     0x149dee0: stur            w0, [x1, #0x43]
    // 0x149dee4: r0 = TextButtonThemeData()
    //     0x149dee4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x149dee8: mov             x2, x0
    // 0x149deec: ldur            x0, [fp, #-0x80]
    // 0x149def0: stur            x2, [fp, #-0x50]
    // 0x149def4: StoreField: r2->field_7 = r0
    //     0x149def4: stur            w0, [x2, #7]
    // 0x149def8: ldur            x0, [fp, #-8]
    // 0x149defc: LoadField: r1 = r0->field_13
    //     0x149defc: ldur            w1, [x0, #0x13]
    // 0x149df00: DecompressPointer r1
    //     0x149df00: add             x1, x1, HEAP, lsl #32
    // 0x149df04: r0 = of()
    //     0x149df04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149df08: LoadField: r1 = r0->field_87
    //     0x149df08: ldur            w1, [x0, #0x87]
    // 0x149df0c: DecompressPointer r1
    //     0x149df0c: add             x1, x1, HEAP, lsl #32
    // 0x149df10: LoadField: r0 = r1->field_2b
    //     0x149df10: ldur            w0, [x1, #0x2b]
    // 0x149df14: DecompressPointer r0
    //     0x149df14: add             x0, x0, HEAP, lsl #32
    // 0x149df18: r16 = Instance_Color
    //     0x149df18: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x149df1c: str             x16, [SP]
    // 0x149df20: mov             x1, x0
    // 0x149df24: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x149df24: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x149df28: ldr             x4, [x4, #0xf40]
    // 0x149df2c: r0 = copyWith()
    //     0x149df2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149df30: stur            x0, [fp, #-0x68]
    // 0x149df34: r0 = Text()
    //     0x149df34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149df38: mov             x3, x0
    // 0x149df3c: r0 = "Exchange with new product"
    //     0x149df3c: add             x0, PP, #0x40, lsl #12  ; [pp+0x407e0] "Exchange with new product"
    //     0x149df40: ldr             x0, [x0, #0x7e0]
    // 0x149df44: stur            x3, [fp, #-0x70]
    // 0x149df48: StoreField: r3->field_b = r0
    //     0x149df48: stur            w0, [x3, #0xb]
    // 0x149df4c: ldur            x0, [fp, #-0x68]
    // 0x149df50: StoreField: r3->field_13 = r0
    //     0x149df50: stur            w0, [x3, #0x13]
    // 0x149df54: ldur            x2, [fp, #-8]
    // 0x149df58: r1 = Function '<anonymous closure>':.
    //     0x149df58: add             x1, PP, #0x40, lsl #12  ; [pp+0x407e8] AnonymousClosure: (0x149ca1c), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x14e3e60)
    //     0x149df5c: ldr             x1, [x1, #0x7e8]
    // 0x149df60: r0 = AllocateClosure()
    //     0x149df60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149df64: stur            x0, [fp, #-8]
    // 0x149df68: r0 = TextButton()
    //     0x149df68: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x149df6c: mov             x1, x0
    // 0x149df70: ldur            x0, [fp, #-8]
    // 0x149df74: stur            x1, [fp, #-0x68]
    // 0x149df78: StoreField: r1->field_b = r0
    //     0x149df78: stur            w0, [x1, #0xb]
    // 0x149df7c: r0 = false
    //     0x149df7c: add             x0, NULL, #0x30  ; false
    // 0x149df80: StoreField: r1->field_27 = r0
    //     0x149df80: stur            w0, [x1, #0x27]
    // 0x149df84: r2 = true
    //     0x149df84: add             x2, NULL, #0x20  ; true
    // 0x149df88: StoreField: r1->field_2f = r2
    //     0x149df88: stur            w2, [x1, #0x2f]
    // 0x149df8c: ldur            x2, [fp, #-0x70]
    // 0x149df90: StoreField: r1->field_37 = r2
    //     0x149df90: stur            w2, [x1, #0x37]
    // 0x149df94: r0 = TextButtonTheme()
    //     0x149df94: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x149df98: mov             x1, x0
    // 0x149df9c: ldur            x0, [fp, #-0x50]
    // 0x149dfa0: stur            x1, [fp, #-8]
    // 0x149dfa4: StoreField: r1->field_f = r0
    //     0x149dfa4: stur            w0, [x1, #0xf]
    // 0x149dfa8: ldur            x0, [fp, #-0x68]
    // 0x149dfac: StoreField: r1->field_b = r0
    //     0x149dfac: stur            w0, [x1, #0xb]
    // 0x149dfb0: r0 = SizedBox()
    //     0x149dfb0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x149dfb4: mov             x3, x0
    // 0x149dfb8: r0 = inf
    //     0x149dfb8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x149dfbc: ldr             x0, [x0, #0x9f8]
    // 0x149dfc0: stur            x3, [fp, #-0x50]
    // 0x149dfc4: StoreField: r3->field_f = r0
    //     0x149dfc4: stur            w0, [x3, #0xf]
    // 0x149dfc8: r0 = 48.000000
    //     0x149dfc8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x149dfcc: ldr             x0, [x0, #0xad8]
    // 0x149dfd0: StoreField: r3->field_13 = r0
    //     0x149dfd0: stur            w0, [x3, #0x13]
    // 0x149dfd4: ldur            x0, [fp, #-8]
    // 0x149dfd8: StoreField: r3->field_b = r0
    //     0x149dfd8: stur            w0, [x3, #0xb]
    // 0x149dfdc: r1 = Null
    //     0x149dfdc: mov             x1, NULL
    // 0x149dfe0: r2 = 14
    //     0x149dfe0: movz            x2, #0xe
    // 0x149dfe4: r0 = AllocateArray()
    //     0x149dfe4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149dfe8: mov             x2, x0
    // 0x149dfec: ldur            x0, [fp, #-0x48]
    // 0x149dff0: stur            x2, [fp, #-8]
    // 0x149dff4: StoreField: r2->field_f = r0
    //     0x149dff4: stur            w0, [x2, #0xf]
    // 0x149dff8: r16 = Instance_SizedBox
    //     0x149dff8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x149dffc: ldr             x16, [x16, #0xc70]
    // 0x149e000: StoreField: r2->field_13 = r16
    //     0x149e000: stur            w16, [x2, #0x13]
    // 0x149e004: ldur            x0, [fp, #-0x58]
    // 0x149e008: ArrayStore: r2[0] = r0  ; List_4
    //     0x149e008: stur            w0, [x2, #0x17]
    // 0x149e00c: r16 = Instance_SizedBox
    //     0x149e00c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x149e010: ldr             x16, [x16, #0x8f0]
    // 0x149e014: StoreField: r2->field_1b = r16
    //     0x149e014: stur            w16, [x2, #0x1b]
    // 0x149e018: ldur            x0, [fp, #-0x60]
    // 0x149e01c: StoreField: r2->field_1f = r0
    //     0x149e01c: stur            w0, [x2, #0x1f]
    // 0x149e020: r16 = Instance_SizedBox
    //     0x149e020: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x149e024: ldr             x16, [x16, #0x8f0]
    // 0x149e028: StoreField: r2->field_23 = r16
    //     0x149e028: stur            w16, [x2, #0x23]
    // 0x149e02c: ldur            x0, [fp, #-0x50]
    // 0x149e030: StoreField: r2->field_27 = r0
    //     0x149e030: stur            w0, [x2, #0x27]
    // 0x149e034: r1 = <Widget>
    //     0x149e034: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x149e038: r0 = AllocateGrowableArray()
    //     0x149e038: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x149e03c: mov             x1, x0
    // 0x149e040: ldur            x0, [fp, #-8]
    // 0x149e044: stur            x1, [fp, #-0x48]
    // 0x149e048: StoreField: r1->field_f = r0
    //     0x149e048: stur            w0, [x1, #0xf]
    // 0x149e04c: r0 = 14
    //     0x149e04c: movz            x0, #0xe
    // 0x149e050: StoreField: r1->field_b = r0
    //     0x149e050: stur            w0, [x1, #0xb]
    // 0x149e054: r0 = Column()
    //     0x149e054: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x149e058: mov             x1, x0
    // 0x149e05c: r0 = Instance_Axis
    //     0x149e05c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x149e060: stur            x1, [fp, #-8]
    // 0x149e064: StoreField: r1->field_f = r0
    //     0x149e064: stur            w0, [x1, #0xf]
    // 0x149e068: r2 = Instance_MainAxisAlignment
    //     0x149e068: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x149e06c: ldr             x2, [x2, #0xa08]
    // 0x149e070: StoreField: r1->field_13 = r2
    //     0x149e070: stur            w2, [x1, #0x13]
    // 0x149e074: r3 = Instance_MainAxisSize
    //     0x149e074: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x149e078: ldr             x3, [x3, #0xa10]
    // 0x149e07c: ArrayStore: r1[0] = r3  ; List_4
    //     0x149e07c: stur            w3, [x1, #0x17]
    // 0x149e080: r4 = Instance_CrossAxisAlignment
    //     0x149e080: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x149e084: ldr             x4, [x4, #0x890]
    // 0x149e088: StoreField: r1->field_1b = r4
    //     0x149e088: stur            w4, [x1, #0x1b]
    // 0x149e08c: r5 = Instance_VerticalDirection
    //     0x149e08c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x149e090: ldr             x5, [x5, #0xa20]
    // 0x149e094: StoreField: r1->field_23 = r5
    //     0x149e094: stur            w5, [x1, #0x23]
    // 0x149e098: r6 = Instance_Clip
    //     0x149e098: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x149e09c: ldr             x6, [x6, #0x38]
    // 0x149e0a0: StoreField: r1->field_2b = r6
    //     0x149e0a0: stur            w6, [x1, #0x2b]
    // 0x149e0a4: StoreField: r1->field_2f = rZR
    //     0x149e0a4: stur            xzr, [x1, #0x2f]
    // 0x149e0a8: ldur            x7, [fp, #-0x48]
    // 0x149e0ac: StoreField: r1->field_b = r7
    //     0x149e0ac: stur            w7, [x1, #0xb]
    // 0x149e0b0: r0 = Visibility()
    //     0x149e0b0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x149e0b4: mov             x3, x0
    // 0x149e0b8: ldur            x0, [fp, #-8]
    // 0x149e0bc: stur            x3, [fp, #-0x48]
    // 0x149e0c0: StoreField: r3->field_b = r0
    //     0x149e0c0: stur            w0, [x3, #0xb]
    // 0x149e0c4: r0 = Instance_SizedBox
    //     0x149e0c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x149e0c8: StoreField: r3->field_f = r0
    //     0x149e0c8: stur            w0, [x3, #0xf]
    // 0x149e0cc: ldur            x0, [fp, #-0x38]
    // 0x149e0d0: StoreField: r3->field_13 = r0
    //     0x149e0d0: stur            w0, [x3, #0x13]
    // 0x149e0d4: r0 = false
    //     0x149e0d4: add             x0, NULL, #0x30  ; false
    // 0x149e0d8: ArrayStore: r3[0] = r0  ; List_4
    //     0x149e0d8: stur            w0, [x3, #0x17]
    // 0x149e0dc: StoreField: r3->field_1b = r0
    //     0x149e0dc: stur            w0, [x3, #0x1b]
    // 0x149e0e0: StoreField: r3->field_1f = r0
    //     0x149e0e0: stur            w0, [x3, #0x1f]
    // 0x149e0e4: StoreField: r3->field_23 = r0
    //     0x149e0e4: stur            w0, [x3, #0x23]
    // 0x149e0e8: StoreField: r3->field_27 = r0
    //     0x149e0e8: stur            w0, [x3, #0x27]
    // 0x149e0ec: StoreField: r3->field_2b = r0
    //     0x149e0ec: stur            w0, [x3, #0x2b]
    // 0x149e0f0: r1 = Null
    //     0x149e0f0: mov             x1, NULL
    // 0x149e0f4: r2 = 12
    //     0x149e0f4: movz            x2, #0xc
    // 0x149e0f8: r0 = AllocateArray()
    //     0x149e0f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149e0fc: mov             x2, x0
    // 0x149e100: ldur            x0, [fp, #-0x28]
    // 0x149e104: stur            x2, [fp, #-8]
    // 0x149e108: StoreField: r2->field_f = r0
    //     0x149e108: stur            w0, [x2, #0xf]
    // 0x149e10c: ldur            x0, [fp, #-0x20]
    // 0x149e110: StoreField: r2->field_13 = r0
    //     0x149e110: stur            w0, [x2, #0x13]
    // 0x149e114: ldur            x0, [fp, #-0x30]
    // 0x149e118: ArrayStore: r2[0] = r0  ; List_4
    //     0x149e118: stur            w0, [x2, #0x17]
    // 0x149e11c: ldur            x0, [fp, #-0x40]
    // 0x149e120: StoreField: r2->field_1b = r0
    //     0x149e120: stur            w0, [x2, #0x1b]
    // 0x149e124: r16 = Instance_SizedBox
    //     0x149e124: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x149e128: ldr             x16, [x16, #0x9f0]
    // 0x149e12c: StoreField: r2->field_1f = r16
    //     0x149e12c: stur            w16, [x2, #0x1f]
    // 0x149e130: ldur            x0, [fp, #-0x48]
    // 0x149e134: StoreField: r2->field_23 = r0
    //     0x149e134: stur            w0, [x2, #0x23]
    // 0x149e138: r1 = <Widget>
    //     0x149e138: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x149e13c: r0 = AllocateGrowableArray()
    //     0x149e13c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x149e140: mov             x1, x0
    // 0x149e144: ldur            x0, [fp, #-8]
    // 0x149e148: stur            x1, [fp, #-0x20]
    // 0x149e14c: StoreField: r1->field_f = r0
    //     0x149e14c: stur            w0, [x1, #0xf]
    // 0x149e150: r0 = 12
    //     0x149e150: movz            x0, #0xc
    // 0x149e154: StoreField: r1->field_b = r0
    //     0x149e154: stur            w0, [x1, #0xb]
    // 0x149e158: r0 = Column()
    //     0x149e158: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x149e15c: mov             x1, x0
    // 0x149e160: r0 = Instance_Axis
    //     0x149e160: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x149e164: stur            x1, [fp, #-8]
    // 0x149e168: StoreField: r1->field_f = r0
    //     0x149e168: stur            w0, [x1, #0xf]
    // 0x149e16c: r2 = Instance_MainAxisAlignment
    //     0x149e16c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x149e170: ldr             x2, [x2, #0xa08]
    // 0x149e174: StoreField: r1->field_13 = r2
    //     0x149e174: stur            w2, [x1, #0x13]
    // 0x149e178: r3 = Instance_MainAxisSize
    //     0x149e178: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x149e17c: ldr             x3, [x3, #0xa10]
    // 0x149e180: ArrayStore: r1[0] = r3  ; List_4
    //     0x149e180: stur            w3, [x1, #0x17]
    // 0x149e184: r4 = Instance_CrossAxisAlignment
    //     0x149e184: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x149e188: ldr             x4, [x4, #0x890]
    // 0x149e18c: StoreField: r1->field_1b = r4
    //     0x149e18c: stur            w4, [x1, #0x1b]
    // 0x149e190: r5 = Instance_VerticalDirection
    //     0x149e190: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x149e194: ldr             x5, [x5, #0xa20]
    // 0x149e198: StoreField: r1->field_23 = r5
    //     0x149e198: stur            w5, [x1, #0x23]
    // 0x149e19c: r6 = Instance_Clip
    //     0x149e19c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x149e1a0: ldr             x6, [x6, #0x38]
    // 0x149e1a4: StoreField: r1->field_2b = r6
    //     0x149e1a4: stur            w6, [x1, #0x2b]
    // 0x149e1a8: StoreField: r1->field_2f = rZR
    //     0x149e1a8: stur            xzr, [x1, #0x2f]
    // 0x149e1ac: ldur            x7, [fp, #-0x20]
    // 0x149e1b0: StoreField: r1->field_b = r7
    //     0x149e1b0: stur            w7, [x1, #0xb]
    // 0x149e1b4: r0 = Container()
    //     0x149e1b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x149e1b8: stur            x0, [fp, #-0x20]
    // 0x149e1bc: ldur            x16, [fp, #-0x18]
    // 0x149e1c0: r30 = Instance_EdgeInsets
    //     0x149e1c0: add             lr, PP, #0x40, lsl #12  ; [pp+0x40740] Obj!EdgeInsets@d59f61
    //     0x149e1c4: ldr             lr, [lr, #0x740]
    // 0x149e1c8: stp             lr, x16, [SP, #8]
    // 0x149e1cc: ldur            x16, [fp, #-8]
    // 0x149e1d0: str             x16, [SP]
    // 0x149e1d4: mov             x1, x0
    // 0x149e1d8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x149e1d8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x149e1dc: ldr             x4, [x4, #0xb40]
    // 0x149e1e0: r0 = Container()
    //     0x149e1e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x149e1e4: r1 = Null
    //     0x149e1e4: mov             x1, NULL
    // 0x149e1e8: r2 = 4
    //     0x149e1e8: movz            x2, #0x4
    // 0x149e1ec: r0 = AllocateArray()
    //     0x149e1ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x149e1f0: mov             x2, x0
    // 0x149e1f4: ldur            x0, [fp, #-0x10]
    // 0x149e1f8: stur            x2, [fp, #-8]
    // 0x149e1fc: StoreField: r2->field_f = r0
    //     0x149e1fc: stur            w0, [x2, #0xf]
    // 0x149e200: ldur            x0, [fp, #-0x20]
    // 0x149e204: StoreField: r2->field_13 = r0
    //     0x149e204: stur            w0, [x2, #0x13]
    // 0x149e208: r1 = <Widget>
    //     0x149e208: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x149e20c: r0 = AllocateGrowableArray()
    //     0x149e20c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x149e210: mov             x1, x0
    // 0x149e214: ldur            x0, [fp, #-8]
    // 0x149e218: stur            x1, [fp, #-0x10]
    // 0x149e21c: StoreField: r1->field_f = r0
    //     0x149e21c: stur            w0, [x1, #0xf]
    // 0x149e220: r0 = 4
    //     0x149e220: movz            x0, #0x4
    // 0x149e224: StoreField: r1->field_b = r0
    //     0x149e224: stur            w0, [x1, #0xb]
    // 0x149e228: r0 = Column()
    //     0x149e228: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x149e22c: mov             x1, x0
    // 0x149e230: r0 = Instance_Axis
    //     0x149e230: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x149e234: stur            x1, [fp, #-8]
    // 0x149e238: StoreField: r1->field_f = r0
    //     0x149e238: stur            w0, [x1, #0xf]
    // 0x149e23c: r2 = Instance_MainAxisAlignment
    //     0x149e23c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x149e240: ldr             x2, [x2, #0xa08]
    // 0x149e244: StoreField: r1->field_13 = r2
    //     0x149e244: stur            w2, [x1, #0x13]
    // 0x149e248: r2 = Instance_MainAxisSize
    //     0x149e248: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x149e24c: ldr             x2, [x2, #0xa10]
    // 0x149e250: ArrayStore: r1[0] = r2  ; List_4
    //     0x149e250: stur            w2, [x1, #0x17]
    // 0x149e254: r2 = Instance_CrossAxisAlignment
    //     0x149e254: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x149e258: ldr             x2, [x2, #0x890]
    // 0x149e25c: StoreField: r1->field_1b = r2
    //     0x149e25c: stur            w2, [x1, #0x1b]
    // 0x149e260: r2 = Instance_VerticalDirection
    //     0x149e260: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x149e264: ldr             x2, [x2, #0xa20]
    // 0x149e268: StoreField: r1->field_23 = r2
    //     0x149e268: stur            w2, [x1, #0x23]
    // 0x149e26c: r2 = Instance_Clip
    //     0x149e26c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x149e270: ldr             x2, [x2, #0x38]
    // 0x149e274: StoreField: r1->field_2b = r2
    //     0x149e274: stur            w2, [x1, #0x2b]
    // 0x149e278: StoreField: r1->field_2f = rZR
    //     0x149e278: stur            xzr, [x1, #0x2f]
    // 0x149e27c: ldur            x2, [fp, #-0x10]
    // 0x149e280: StoreField: r1->field_b = r2
    //     0x149e280: stur            w2, [x1, #0xb]
    // 0x149e284: r0 = Padding()
    //     0x149e284: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x149e288: mov             x1, x0
    // 0x149e28c: r0 = Instance_EdgeInsets
    //     0x149e28c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x149e290: ldr             x0, [x0, #0x1f0]
    // 0x149e294: stur            x1, [fp, #-0x10]
    // 0x149e298: StoreField: r1->field_f = r0
    //     0x149e298: stur            w0, [x1, #0xf]
    // 0x149e29c: ldur            x0, [fp, #-8]
    // 0x149e2a0: StoreField: r1->field_b = r0
    //     0x149e2a0: stur            w0, [x1, #0xb]
    // 0x149e2a4: r0 = ColoredBox()
    //     0x149e2a4: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x149e2a8: mov             x1, x0
    // 0x149e2ac: r0 = Instance_Color
    //     0x149e2ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x149e2b0: ldr             x0, [x0, #0x90]
    // 0x149e2b4: stur            x1, [fp, #-8]
    // 0x149e2b8: StoreField: r1->field_f = r0
    //     0x149e2b8: stur            w0, [x1, #0xf]
    // 0x149e2bc: ldur            x0, [fp, #-0x10]
    // 0x149e2c0: StoreField: r1->field_b = r0
    //     0x149e2c0: stur            w0, [x1, #0xb]
    // 0x149e2c4: r0 = SingleChildScrollView()
    //     0x149e2c4: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x149e2c8: r1 = Instance_Axis
    //     0x149e2c8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x149e2cc: StoreField: r0->field_b = r1
    //     0x149e2cc: stur            w1, [x0, #0xb]
    // 0x149e2d0: r1 = false
    //     0x149e2d0: add             x1, NULL, #0x30  ; false
    // 0x149e2d4: StoreField: r0->field_f = r1
    //     0x149e2d4: stur            w1, [x0, #0xf]
    // 0x149e2d8: ldur            x1, [fp, #-8]
    // 0x149e2dc: StoreField: r0->field_23 = r1
    //     0x149e2dc: stur            w1, [x0, #0x23]
    // 0x149e2e0: r1 = Instance_DragStartBehavior
    //     0x149e2e0: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x149e2e4: StoreField: r0->field_27 = r1
    //     0x149e2e4: stur            w1, [x0, #0x27]
    // 0x149e2e8: r1 = Instance_Clip
    //     0x149e2e8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x149e2ec: ldr             x1, [x1, #0x7e0]
    // 0x149e2f0: StoreField: r0->field_2b = r1
    //     0x149e2f0: stur            w1, [x0, #0x2b]
    // 0x149e2f4: r1 = Instance_HitTestBehavior
    //     0x149e2f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x149e2f8: ldr             x1, [x1, #0x288]
    // 0x149e2fc: StoreField: r0->field_2f = r1
    //     0x149e2fc: stur            w1, [x0, #0x2f]
    // 0x149e300: LeaveFrame
    //     0x149e300: mov             SP, fp
    //     0x149e304: ldp             fp, lr, [SP], #0x10
    // 0x149e308: ret
    //     0x149e308: ret             
    // 0x149e30c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x149e30c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x149e310: b               #0x149cbc0
    // 0x149e314: SaveReg d0
    //     0x149e314: str             q0, [SP, #-0x10]!
    // 0x149e318: stp             x4, x5, [SP, #-0x10]!
    // 0x149e31c: stp             x2, x3, [SP, #-0x10]!
    // 0x149e320: stp             x0, x1, [SP, #-0x10]!
    // 0x149e324: r0 = AllocateDouble()
    //     0x149e324: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x149e328: mov             x6, x0
    // 0x149e32c: ldp             x0, x1, [SP], #0x10
    // 0x149e330: ldp             x2, x3, [SP], #0x10
    // 0x149e334: ldp             x4, x5, [SP], #0x10
    // 0x149e338: RestoreReg d0
    //     0x149e338: ldr             q0, [SP], #0x10
    // 0x149e33c: b               #0x149d50c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x149e340, size: 0x78
    // 0x149e340: EnterFrame
    //     0x149e340: stp             fp, lr, [SP, #-0x10]!
    //     0x149e344: mov             fp, SP
    // 0x149e348: AllocStack(0x18)
    //     0x149e348: sub             SP, SP, #0x18
    // 0x149e34c: SetupParameters()
    //     0x149e34c: ldr             x0, [fp, #0x10]
    //     0x149e350: ldur            w2, [x0, #0x17]
    //     0x149e354: add             x2, x2, HEAP, lsl #32
    //     0x149e358: stur            x2, [fp, #-8]
    // 0x149e35c: CheckStackOverflow
    //     0x149e35c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x149e360: cmp             SP, x16
    //     0x149e364: b.ls            #0x149e3b0
    // 0x149e368: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x149e368: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x149e36c: ldr             x0, [x0, #0x1c80]
    //     0x149e370: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x149e374: cmp             w0, w16
    //     0x149e378: b.ne            #0x149e384
    //     0x149e37c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x149e380: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x149e384: ldur            x2, [fp, #-8]
    // 0x149e388: r1 = Function '<anonymous closure>':.
    //     0x149e388: add             x1, PP, #0x40, lsl #12  ; [pp+0x407f0] AnonymousClosure: (0x149e3b8), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x14e3e60)
    //     0x149e38c: ldr             x1, [x1, #0x7f0]
    // 0x149e390: r0 = AllocateClosure()
    //     0x149e390: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149e394: stp             x0, NULL, [SP]
    // 0x149e398: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e398: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e39c: r0 = GetNavigation.to()
    //     0x149e39c: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0x149e3a0: r0 = Null
    //     0x149e3a0: mov             x0, NULL
    // 0x149e3a4: LeaveFrame
    //     0x149e3a4: mov             SP, fp
    //     0x149e3a8: ldp             fp, lr, [SP], #0x10
    // 0x149e3ac: ret
    //     0x149e3ac: ret             
    // 0x149e3b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x149e3b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x149e3b4: b               #0x149e368
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0x149e3b8, size: 0xa0
    // 0x149e3b8: EnterFrame
    //     0x149e3b8: stp             fp, lr, [SP, #-0x10]!
    //     0x149e3bc: mov             fp, SP
    // 0x149e3c0: AllocStack(0x8)
    //     0x149e3c0: sub             SP, SP, #8
    // 0x149e3c4: SetupParameters()
    //     0x149e3c4: ldr             x0, [fp, #0x10]
    //     0x149e3c8: ldur            w1, [x0, #0x17]
    //     0x149e3cc: add             x1, x1, HEAP, lsl #32
    // 0x149e3d0: CheckStackOverflow
    //     0x149e3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x149e3d4: cmp             SP, x16
    //     0x149e3d8: b.ls            #0x149e450
    // 0x149e3dc: LoadField: r0 = r1->field_f
    //     0x149e3dc: ldur            w0, [x1, #0xf]
    // 0x149e3e0: DecompressPointer r0
    //     0x149e3e0: add             x0, x0, HEAP, lsl #32
    // 0x149e3e4: mov             x1, x0
    // 0x149e3e8: r0 = controller()
    //     0x149e3e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149e3ec: LoadField: r1 = r0->field_4b
    //     0x149e3ec: ldur            w1, [x0, #0x4b]
    // 0x149e3f0: DecompressPointer r1
    //     0x149e3f0: add             x1, x1, HEAP, lsl #32
    // 0x149e3f4: r0 = value()
    //     0x149e3f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149e3f8: LoadField: r1 = r0->field_b
    //     0x149e3f8: ldur            w1, [x0, #0xb]
    // 0x149e3fc: DecompressPointer r1
    //     0x149e3fc: add             x1, x1, HEAP, lsl #32
    // 0x149e400: cmp             w1, NULL
    // 0x149e404: b.ne            #0x149e410
    // 0x149e408: r0 = Null
    //     0x149e408: mov             x0, NULL
    // 0x149e40c: b               #0x149e434
    // 0x149e410: LoadField: r0 = r1->field_2f
    //     0x149e410: ldur            w0, [x1, #0x2f]
    // 0x149e414: DecompressPointer r0
    //     0x149e414: add             x0, x0, HEAP, lsl #32
    // 0x149e418: cmp             w0, NULL
    // 0x149e41c: b.ne            #0x149e428
    // 0x149e420: r0 = Null
    //     0x149e420: mov             x0, NULL
    // 0x149e424: b               #0x149e434
    // 0x149e428: LoadField: r1 = r0->field_f
    //     0x149e428: ldur            w1, [x0, #0xf]
    // 0x149e42c: DecompressPointer r1
    //     0x149e42c: add             x1, x1, HEAP, lsl #32
    // 0x149e430: mov             x0, x1
    // 0x149e434: stur            x0, [fp, #-8]
    // 0x149e438: r0 = ViewSizeChart()
    //     0x149e438: bl              #0xa1d360  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0x149e43c: ldur            x1, [fp, #-8]
    // 0x149e440: StoreField: r0->field_b = r1
    //     0x149e440: stur            w1, [x0, #0xb]
    // 0x149e444: LeaveFrame
    //     0x149e444: mov             SP, fp
    //     0x149e448: ldp             fp, lr, [SP], #0x10
    // 0x149e44c: ret
    //     0x149e44c: ret             
    // 0x149e450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x149e450: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x149e454: b               #0x149e3dc
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x149e458, size: 0x74
    // 0x149e458: EnterFrame
    //     0x149e458: stp             fp, lr, [SP, #-0x10]!
    //     0x149e45c: mov             fp, SP
    // 0x149e460: AllocStack(0x10)
    //     0x149e460: sub             SP, SP, #0x10
    // 0x149e464: SetupParameters()
    //     0x149e464: ldr             x0, [fp, #0x20]
    //     0x149e468: ldur            w1, [x0, #0x17]
    //     0x149e46c: add             x1, x1, HEAP, lsl #32
    //     0x149e470: stur            x1, [fp, #-8]
    // 0x149e474: r1 = 2
    //     0x149e474: movz            x1, #0x2
    // 0x149e478: r0 = AllocateContext()
    //     0x149e478: bl              #0x16f6108  ; AllocateContextStub
    // 0x149e47c: mov             x1, x0
    // 0x149e480: ldur            x0, [fp, #-8]
    // 0x149e484: stur            x1, [fp, #-0x10]
    // 0x149e488: StoreField: r1->field_b = r0
    //     0x149e488: stur            w0, [x1, #0xb]
    // 0x149e48c: ldr             x0, [fp, #0x18]
    // 0x149e490: StoreField: r1->field_f = r0
    //     0x149e490: stur            w0, [x1, #0xf]
    // 0x149e494: ldr             x0, [fp, #0x10]
    // 0x149e498: StoreField: r1->field_13 = r0
    //     0x149e498: stur            w0, [x1, #0x13]
    // 0x149e49c: r0 = Obx()
    //     0x149e49c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x149e4a0: ldur            x2, [fp, #-0x10]
    // 0x149e4a4: r1 = Function '<anonymous closure>':.
    //     0x149e4a4: add             x1, PP, #0x40, lsl #12  ; [pp+0x407f8] AnonymousClosure: (0x149e4cc), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x14e3e60)
    //     0x149e4a8: ldr             x1, [x1, #0x7f8]
    // 0x149e4ac: stur            x0, [fp, #-8]
    // 0x149e4b0: r0 = AllocateClosure()
    //     0x149e4b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149e4b4: mov             x1, x0
    // 0x149e4b8: ldur            x0, [fp, #-8]
    // 0x149e4bc: StoreField: r0->field_b = r1
    //     0x149e4bc: stur            w1, [x0, #0xb]
    // 0x149e4c0: LeaveFrame
    //     0x149e4c0: mov             SP, fp
    //     0x149e4c4: ldp             fp, lr, [SP], #0x10
    // 0x149e4c8: ret
    //     0x149e4c8: ret             
  }
  [closure] TextButtonTheme <anonymous closure>(dynamic) {
    // ** addr: 0x149e4cc, size: 0x434
    // 0x149e4cc: EnterFrame
    //     0x149e4cc: stp             fp, lr, [SP, #-0x10]!
    //     0x149e4d0: mov             fp, SP
    // 0x149e4d4: AllocStack(0x40)
    //     0x149e4d4: sub             SP, SP, #0x40
    // 0x149e4d8: SetupParameters()
    //     0x149e4d8: ldr             x0, [fp, #0x10]
    //     0x149e4dc: ldur            w2, [x0, #0x17]
    //     0x149e4e0: add             x2, x2, HEAP, lsl #32
    //     0x149e4e4: stur            x2, [fp, #-0x10]
    // 0x149e4e8: CheckStackOverflow
    //     0x149e4e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x149e4ec: cmp             SP, x16
    //     0x149e4f0: b.ls            #0x149e8f0
    // 0x149e4f4: LoadField: r0 = r2->field_b
    //     0x149e4f4: ldur            w0, [x2, #0xb]
    // 0x149e4f8: DecompressPointer r0
    //     0x149e4f8: add             x0, x0, HEAP, lsl #32
    // 0x149e4fc: stur            x0, [fp, #-8]
    // 0x149e500: LoadField: r1 = r0->field_f
    //     0x149e500: ldur            w1, [x0, #0xf]
    // 0x149e504: DecompressPointer r1
    //     0x149e504: add             x1, x1, HEAP, lsl #32
    // 0x149e508: r0 = controller()
    //     0x149e508: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149e50c: LoadField: r2 = r0->field_5b
    //     0x149e50c: ldur            w2, [x0, #0x5b]
    // 0x149e510: DecompressPointer r2
    //     0x149e510: add             x2, x2, HEAP, lsl #32
    // 0x149e514: ldur            x0, [fp, #-8]
    // 0x149e518: stur            x2, [fp, #-0x18]
    // 0x149e51c: LoadField: r1 = r0->field_f
    //     0x149e51c: ldur            w1, [x0, #0xf]
    // 0x149e520: DecompressPointer r1
    //     0x149e520: add             x1, x1, HEAP, lsl #32
    // 0x149e524: r0 = controller()
    //     0x149e524: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149e528: LoadField: r1 = r0->field_4b
    //     0x149e528: ldur            w1, [x0, #0x4b]
    // 0x149e52c: DecompressPointer r1
    //     0x149e52c: add             x1, x1, HEAP, lsl #32
    // 0x149e530: r0 = value()
    //     0x149e530: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149e534: LoadField: r1 = r0->field_b
    //     0x149e534: ldur            w1, [x0, #0xb]
    // 0x149e538: DecompressPointer r1
    //     0x149e538: add             x1, x1, HEAP, lsl #32
    // 0x149e53c: cmp             w1, NULL
    // 0x149e540: b.ne            #0x149e550
    // 0x149e544: ldur            x3, [fp, #-0x10]
    // 0x149e548: r0 = Null
    //     0x149e548: mov             x0, NULL
    // 0x149e54c: b               #0x149e5bc
    // 0x149e550: LoadField: r0 = r1->field_2f
    //     0x149e550: ldur            w0, [x1, #0x2f]
    // 0x149e554: DecompressPointer r0
    //     0x149e554: add             x0, x0, HEAP, lsl #32
    // 0x149e558: cmp             w0, NULL
    // 0x149e55c: b.ne            #0x149e56c
    // 0x149e560: ldur            x3, [fp, #-0x10]
    // 0x149e564: r0 = Null
    //     0x149e564: mov             x0, NULL
    // 0x149e568: b               #0x149e5bc
    // 0x149e56c: ldur            x3, [fp, #-0x10]
    // 0x149e570: LoadField: r2 = r0->field_b
    //     0x149e570: ldur            w2, [x0, #0xb]
    // 0x149e574: DecompressPointer r2
    //     0x149e574: add             x2, x2, HEAP, lsl #32
    // 0x149e578: LoadField: r0 = r3->field_13
    //     0x149e578: ldur            w0, [x3, #0x13]
    // 0x149e57c: DecompressPointer r0
    //     0x149e57c: add             x0, x0, HEAP, lsl #32
    // 0x149e580: LoadField: r1 = r2->field_b
    //     0x149e580: ldur            w1, [x2, #0xb]
    // 0x149e584: r4 = LoadInt32Instr(r0)
    //     0x149e584: sbfx            x4, x0, #1, #0x1f
    //     0x149e588: tbz             w0, #0, #0x149e590
    //     0x149e58c: ldur            x4, [x0, #7]
    // 0x149e590: r0 = LoadInt32Instr(r1)
    //     0x149e590: sbfx            x0, x1, #1, #0x1f
    // 0x149e594: mov             x1, x4
    // 0x149e598: cmp             x1, x0
    // 0x149e59c: b.hs            #0x149e8f8
    // 0x149e5a0: LoadField: r0 = r2->field_f
    //     0x149e5a0: ldur            w0, [x2, #0xf]
    // 0x149e5a4: DecompressPointer r0
    //     0x149e5a4: add             x0, x0, HEAP, lsl #32
    // 0x149e5a8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x149e5a8: add             x16, x0, x4, lsl #2
    //     0x149e5ac: ldur            w1, [x16, #0xf]
    // 0x149e5b0: DecompressPointer r1
    //     0x149e5b0: add             x1, x1, HEAP, lsl #32
    // 0x149e5b4: LoadField: r0 = r1->field_7
    //     0x149e5b4: ldur            w0, [x1, #7]
    // 0x149e5b8: DecompressPointer r0
    //     0x149e5b8: add             x0, x0, HEAP, lsl #32
    // 0x149e5bc: cmp             w0, NULL
    // 0x149e5c0: b.ne            #0x149e5cc
    // 0x149e5c4: r2 = ""
    //     0x149e5c4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x149e5c8: b               #0x149e5d0
    // 0x149e5cc: mov             x2, x0
    // 0x149e5d0: ldur            x1, [fp, #-0x18]
    // 0x149e5d4: r0 = RxStringExt.contains()
    //     0x149e5d4: bl              #0x13cb554  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.contains
    // 0x149e5d8: tbnz            w0, #4, #0x149e690
    // 0x149e5dc: r16 = <EdgeInsets>
    //     0x149e5dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x149e5e0: ldr             x16, [x16, #0xda0]
    // 0x149e5e4: r30 = Instance_EdgeInsets
    //     0x149e5e4: add             lr, PP, #0x40, lsl #12  ; [pp+0x40800] Obj!EdgeInsets@d59b11
    //     0x149e5e8: ldr             lr, [lr, #0x800]
    // 0x149e5ec: stp             lr, x16, [SP]
    // 0x149e5f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e5f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e5f4: r0 = all()
    //     0x149e5f4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e5f8: r1 = Instance_Color
    //     0x149e5f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149e5fc: d0 = 0.050000
    //     0x149e5fc: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x149e600: stur            x0, [fp, #-0x18]
    // 0x149e604: r0 = withOpacity()
    //     0x149e604: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149e608: r16 = <Color>
    //     0x149e608: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x149e60c: ldr             x16, [x16, #0xf80]
    // 0x149e610: stp             x0, x16, [SP]
    // 0x149e614: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e614: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e618: r0 = all()
    //     0x149e618: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e61c: r1 = Instance_Color
    //     0x149e61c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149e620: d0 = 0.050000
    //     0x149e620: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x149e624: stur            x0, [fp, #-0x20]
    // 0x149e628: r0 = withOpacity()
    //     0x149e628: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149e62c: r16 = <Color>
    //     0x149e62c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x149e630: ldr             x16, [x16, #0xf80]
    // 0x149e634: stp             x0, x16, [SP]
    // 0x149e638: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e638: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e63c: r0 = all()
    //     0x149e63c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e640: stur            x0, [fp, #-0x28]
    // 0x149e644: r16 = <RoundedRectangleBorder>
    //     0x149e644: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x149e648: ldr             x16, [x16, #0xf78]
    // 0x149e64c: r30 = Instance_RoundedRectangleBorder
    //     0x149e64c: add             lr, PP, #0x40, lsl #12  ; [pp+0x40808] Obj!RoundedRectangleBorder@d5ad01
    //     0x149e650: ldr             lr, [lr, #0x808]
    // 0x149e654: stp             lr, x16, [SP]
    // 0x149e658: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e658: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e65c: r0 = all()
    //     0x149e65c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e660: stur            x0, [fp, #-0x30]
    // 0x149e664: r0 = ButtonStyle()
    //     0x149e664: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x149e668: mov             x1, x0
    // 0x149e66c: ldur            x0, [fp, #-0x28]
    // 0x149e670: StoreField: r1->field_b = r0
    //     0x149e670: stur            w0, [x1, #0xb]
    // 0x149e674: ldur            x0, [fp, #-0x20]
    // 0x149e678: StoreField: r1->field_f = r0
    //     0x149e678: stur            w0, [x1, #0xf]
    // 0x149e67c: ldur            x0, [fp, #-0x18]
    // 0x149e680: StoreField: r1->field_23 = r0
    //     0x149e680: stur            w0, [x1, #0x23]
    // 0x149e684: ldur            x0, [fp, #-0x30]
    // 0x149e688: StoreField: r1->field_43 = r0
    //     0x149e688: stur            w0, [x1, #0x43]
    // 0x149e68c: b               #0x149e740
    // 0x149e690: r16 = <EdgeInsets>
    //     0x149e690: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x149e694: ldr             x16, [x16, #0xda0]
    // 0x149e698: r30 = Instance_EdgeInsets
    //     0x149e698: add             lr, PP, #0x40, lsl #12  ; [pp+0x40800] Obj!EdgeInsets@d59b11
    //     0x149e69c: ldr             lr, [lr, #0x800]
    // 0x149e6a0: stp             lr, x16, [SP]
    // 0x149e6a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e6a4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e6a8: r0 = all()
    //     0x149e6a8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e6ac: r1 = Instance_Color
    //     0x149e6ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149e6b0: d0 = 0.050000
    //     0x149e6b0: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x149e6b4: stur            x0, [fp, #-0x18]
    // 0x149e6b8: r0 = withOpacity()
    //     0x149e6b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149e6bc: r16 = <Color>
    //     0x149e6bc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x149e6c0: ldr             x16, [x16, #0xf80]
    // 0x149e6c4: stp             x0, x16, [SP]
    // 0x149e6c8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e6c8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e6cc: r0 = all()
    //     0x149e6cc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e6d0: r1 = Instance_Color
    //     0x149e6d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149e6d4: d0 = 0.050000
    //     0x149e6d4: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x149e6d8: stur            x0, [fp, #-0x20]
    // 0x149e6dc: r0 = withOpacity()
    //     0x149e6dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149e6e0: r16 = <Color>
    //     0x149e6e0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x149e6e4: ldr             x16, [x16, #0xf80]
    // 0x149e6e8: stp             x0, x16, [SP]
    // 0x149e6ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e6ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e6f0: r0 = all()
    //     0x149e6f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e6f4: stur            x0, [fp, #-0x28]
    // 0x149e6f8: r16 = <RoundedRectangleBorder>
    //     0x149e6f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x149e6fc: ldr             x16, [x16, #0xf78]
    // 0x149e700: r30 = Instance_RoundedRectangleBorder
    //     0x149e700: add             lr, PP, #0x40, lsl #12  ; [pp+0x406d0] Obj!RoundedRectangleBorder@d5acc1
    //     0x149e704: ldr             lr, [lr, #0x6d0]
    // 0x149e708: stp             lr, x16, [SP]
    // 0x149e70c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x149e70c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x149e710: r0 = all()
    //     0x149e710: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x149e714: stur            x0, [fp, #-0x30]
    // 0x149e718: r0 = ButtonStyle()
    //     0x149e718: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x149e71c: mov             x1, x0
    // 0x149e720: ldur            x0, [fp, #-0x28]
    // 0x149e724: StoreField: r1->field_b = r0
    //     0x149e724: stur            w0, [x1, #0xb]
    // 0x149e728: ldur            x0, [fp, #-0x20]
    // 0x149e72c: StoreField: r1->field_f = r0
    //     0x149e72c: stur            w0, [x1, #0xf]
    // 0x149e730: ldur            x0, [fp, #-0x18]
    // 0x149e734: StoreField: r1->field_23 = r0
    //     0x149e734: stur            w0, [x1, #0x23]
    // 0x149e738: ldur            x0, [fp, #-0x30]
    // 0x149e73c: StoreField: r1->field_43 = r0
    //     0x149e73c: stur            w0, [x1, #0x43]
    // 0x149e740: ldur            x0, [fp, #-8]
    // 0x149e744: stur            x1, [fp, #-0x18]
    // 0x149e748: r0 = TextButtonThemeData()
    //     0x149e748: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x149e74c: mov             x2, x0
    // 0x149e750: ldur            x0, [fp, #-0x18]
    // 0x149e754: stur            x2, [fp, #-0x20]
    // 0x149e758: StoreField: r2->field_7 = r0
    //     0x149e758: stur            w0, [x2, #7]
    // 0x149e75c: ldur            x0, [fp, #-8]
    // 0x149e760: LoadField: r1 = r0->field_f
    //     0x149e760: ldur            w1, [x0, #0xf]
    // 0x149e764: DecompressPointer r1
    //     0x149e764: add             x1, x1, HEAP, lsl #32
    // 0x149e768: r0 = controller()
    //     0x149e768: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x149e76c: LoadField: r1 = r0->field_4b
    //     0x149e76c: ldur            w1, [x0, #0x4b]
    // 0x149e770: DecompressPointer r1
    //     0x149e770: add             x1, x1, HEAP, lsl #32
    // 0x149e774: r0 = value()
    //     0x149e774: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x149e778: LoadField: r1 = r0->field_b
    //     0x149e778: ldur            w1, [x0, #0xb]
    // 0x149e77c: DecompressPointer r1
    //     0x149e77c: add             x1, x1, HEAP, lsl #32
    // 0x149e780: cmp             w1, NULL
    // 0x149e784: b.ne            #0x149e794
    // 0x149e788: ldur            x2, [fp, #-0x10]
    // 0x149e78c: r1 = Null
    //     0x149e78c: mov             x1, NULL
    // 0x149e790: b               #0x149e804
    // 0x149e794: LoadField: r0 = r1->field_2f
    //     0x149e794: ldur            w0, [x1, #0x2f]
    // 0x149e798: DecompressPointer r0
    //     0x149e798: add             x0, x0, HEAP, lsl #32
    // 0x149e79c: cmp             w0, NULL
    // 0x149e7a0: b.ne            #0x149e7b0
    // 0x149e7a4: ldur            x2, [fp, #-0x10]
    // 0x149e7a8: r0 = Null
    //     0x149e7a8: mov             x0, NULL
    // 0x149e7ac: b               #0x149e800
    // 0x149e7b0: ldur            x2, [fp, #-0x10]
    // 0x149e7b4: LoadField: r3 = r0->field_b
    //     0x149e7b4: ldur            w3, [x0, #0xb]
    // 0x149e7b8: DecompressPointer r3
    //     0x149e7b8: add             x3, x3, HEAP, lsl #32
    // 0x149e7bc: LoadField: r0 = r2->field_13
    //     0x149e7bc: ldur            w0, [x2, #0x13]
    // 0x149e7c0: DecompressPointer r0
    //     0x149e7c0: add             x0, x0, HEAP, lsl #32
    // 0x149e7c4: LoadField: r1 = r3->field_b
    //     0x149e7c4: ldur            w1, [x3, #0xb]
    // 0x149e7c8: r4 = LoadInt32Instr(r0)
    //     0x149e7c8: sbfx            x4, x0, #1, #0x1f
    //     0x149e7cc: tbz             w0, #0, #0x149e7d4
    //     0x149e7d0: ldur            x4, [x0, #7]
    // 0x149e7d4: r0 = LoadInt32Instr(r1)
    //     0x149e7d4: sbfx            x0, x1, #1, #0x1f
    // 0x149e7d8: mov             x1, x4
    // 0x149e7dc: cmp             x1, x0
    // 0x149e7e0: b.hs            #0x149e8fc
    // 0x149e7e4: LoadField: r0 = r3->field_f
    //     0x149e7e4: ldur            w0, [x3, #0xf]
    // 0x149e7e8: DecompressPointer r0
    //     0x149e7e8: add             x0, x0, HEAP, lsl #32
    // 0x149e7ec: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x149e7ec: add             x16, x0, x4, lsl #2
    //     0x149e7f0: ldur            w1, [x16, #0xf]
    // 0x149e7f4: DecompressPointer r1
    //     0x149e7f4: add             x1, x1, HEAP, lsl #32
    // 0x149e7f8: LoadField: r0 = r1->field_f
    //     0x149e7f8: ldur            w0, [x1, #0xf]
    // 0x149e7fc: DecompressPointer r0
    //     0x149e7fc: add             x0, x0, HEAP, lsl #32
    // 0x149e800: mov             x1, x0
    // 0x149e804: ldur            x0, [fp, #-0x20]
    // 0x149e808: str             x1, [SP]
    // 0x149e80c: r0 = _interpolateSingle()
    //     0x149e80c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x149e810: ldur            x2, [fp, #-0x10]
    // 0x149e814: stur            x0, [fp, #-8]
    // 0x149e818: LoadField: r1 = r2->field_f
    //     0x149e818: ldur            w1, [x2, #0xf]
    // 0x149e81c: DecompressPointer r1
    //     0x149e81c: add             x1, x1, HEAP, lsl #32
    // 0x149e820: r0 = of()
    //     0x149e820: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x149e824: LoadField: r1 = r0->field_87
    //     0x149e824: ldur            w1, [x0, #0x87]
    // 0x149e828: DecompressPointer r1
    //     0x149e828: add             x1, x1, HEAP, lsl #32
    // 0x149e82c: LoadField: r0 = r1->field_2b
    //     0x149e82c: ldur            w0, [x1, #0x2b]
    // 0x149e830: DecompressPointer r0
    //     0x149e830: add             x0, x0, HEAP, lsl #32
    // 0x149e834: stur            x0, [fp, #-0x18]
    // 0x149e838: r1 = Instance_Color
    //     0x149e838: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x149e83c: d0 = 0.700000
    //     0x149e83c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x149e840: ldr             d0, [x17, #0xf48]
    // 0x149e844: r0 = withOpacity()
    //     0x149e844: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x149e848: r16 = 14.000000
    //     0x149e848: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x149e84c: ldr             x16, [x16, #0x1d8]
    // 0x149e850: stp             x16, x0, [SP]
    // 0x149e854: ldur            x1, [fp, #-0x18]
    // 0x149e858: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x149e858: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x149e85c: ldr             x4, [x4, #0x9b8]
    // 0x149e860: r0 = copyWith()
    //     0x149e860: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x149e864: stur            x0, [fp, #-0x18]
    // 0x149e868: r0 = Text()
    //     0x149e868: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x149e86c: mov             x3, x0
    // 0x149e870: ldur            x0, [fp, #-8]
    // 0x149e874: stur            x3, [fp, #-0x28]
    // 0x149e878: StoreField: r3->field_b = r0
    //     0x149e878: stur            w0, [x3, #0xb]
    // 0x149e87c: ldur            x0, [fp, #-0x18]
    // 0x149e880: StoreField: r3->field_13 = r0
    //     0x149e880: stur            w0, [x3, #0x13]
    // 0x149e884: r0 = Instance_TextOverflow
    //     0x149e884: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x149e888: ldr             x0, [x0, #0xe10]
    // 0x149e88c: StoreField: r3->field_2b = r0
    //     0x149e88c: stur            w0, [x3, #0x2b]
    // 0x149e890: ldur            x2, [fp, #-0x10]
    // 0x149e894: r1 = Function '<anonymous closure>':.
    //     0x149e894: add             x1, PP, #0x40, lsl #12  ; [pp+0x40810] AnonymousClosure: (0x13cb5b4), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x149e898: ldr             x1, [x1, #0x810]
    // 0x149e89c: r0 = AllocateClosure()
    //     0x149e89c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x149e8a0: stur            x0, [fp, #-8]
    // 0x149e8a4: r0 = TextButton()
    //     0x149e8a4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x149e8a8: mov             x1, x0
    // 0x149e8ac: ldur            x0, [fp, #-8]
    // 0x149e8b0: stur            x1, [fp, #-0x10]
    // 0x149e8b4: StoreField: r1->field_b = r0
    //     0x149e8b4: stur            w0, [x1, #0xb]
    // 0x149e8b8: r0 = false
    //     0x149e8b8: add             x0, NULL, #0x30  ; false
    // 0x149e8bc: StoreField: r1->field_27 = r0
    //     0x149e8bc: stur            w0, [x1, #0x27]
    // 0x149e8c0: r0 = true
    //     0x149e8c0: add             x0, NULL, #0x20  ; true
    // 0x149e8c4: StoreField: r1->field_2f = r0
    //     0x149e8c4: stur            w0, [x1, #0x2f]
    // 0x149e8c8: ldur            x0, [fp, #-0x28]
    // 0x149e8cc: StoreField: r1->field_37 = r0
    //     0x149e8cc: stur            w0, [x1, #0x37]
    // 0x149e8d0: r0 = TextButtonTheme()
    //     0x149e8d0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x149e8d4: ldur            x1, [fp, #-0x20]
    // 0x149e8d8: StoreField: r0->field_f = r1
    //     0x149e8d8: stur            w1, [x0, #0xf]
    // 0x149e8dc: ldur            x1, [fp, #-0x10]
    // 0x149e8e0: StoreField: r0->field_b = r1
    //     0x149e8e0: stur            w1, [x0, #0xb]
    // 0x149e8e4: LeaveFrame
    //     0x149e8e4: mov             SP, fp
    //     0x149e8e8: ldp             fp, lr, [SP], #0x10
    // 0x149e8ec: ret
    //     0x149e8ec: ret             
    // 0x149e8f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x149e8f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x149e8f4: b               #0x149e4f4
    // 0x149e8f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x149e8f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x149e8fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x149e8fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ body(/* No info */) {
    // ** addr: 0x14e3e60, size: 0x64
    // 0x14e3e60: EnterFrame
    //     0x14e3e60: stp             fp, lr, [SP, #-0x10]!
    //     0x14e3e64: mov             fp, SP
    // 0x14e3e68: AllocStack(0x18)
    //     0x14e3e68: sub             SP, SP, #0x18
    // 0x14e3e6c: SetupParameters(ExchangeProductSkusScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14e3e6c: stur            x1, [fp, #-8]
    //     0x14e3e70: stur            x2, [fp, #-0x10]
    // 0x14e3e74: r1 = 2
    //     0x14e3e74: movz            x1, #0x2
    // 0x14e3e78: r0 = AllocateContext()
    //     0x14e3e78: bl              #0x16f6108  ; AllocateContextStub
    // 0x14e3e7c: mov             x1, x0
    // 0x14e3e80: ldur            x0, [fp, #-8]
    // 0x14e3e84: stur            x1, [fp, #-0x18]
    // 0x14e3e88: StoreField: r1->field_f = r0
    //     0x14e3e88: stur            w0, [x1, #0xf]
    // 0x14e3e8c: ldur            x0, [fp, #-0x10]
    // 0x14e3e90: StoreField: r1->field_13 = r0
    //     0x14e3e90: stur            w0, [x1, #0x13]
    // 0x14e3e94: r0 = Obx()
    //     0x14e3e94: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14e3e98: ldur            x2, [fp, #-0x18]
    // 0x14e3e9c: r1 = Function '<anonymous closure>':.
    //     0x14e3e9c: add             x1, PP, #0x40, lsl #12  ; [pp+0x407a0] AnonymousClosure: (0x149cb98), in [package:customer_app/app/presentation/views/glass/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x14e3e60)
    //     0x14e3ea0: ldr             x1, [x1, #0x7a0]
    // 0x14e3ea4: stur            x0, [fp, #-8]
    // 0x14e3ea8: r0 = AllocateClosure()
    //     0x14e3ea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e3eac: mov             x1, x0
    // 0x14e3eb0: ldur            x0, [fp, #-8]
    // 0x14e3eb4: StoreField: r0->field_b = r1
    //     0x14e3eb4: stur            w1, [x0, #0xb]
    // 0x14e3eb8: LeaveFrame
    //     0x14e3eb8: mov             SP, fp
    //     0x14e3ebc: ldp             fp, lr, [SP], #0x10
    // 0x14e3ec0: ret
    //     0x14e3ec0: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e1d14, size: 0x18c
    // 0x15e1d14: EnterFrame
    //     0x15e1d14: stp             fp, lr, [SP, #-0x10]!
    //     0x15e1d18: mov             fp, SP
    // 0x15e1d1c: AllocStack(0x28)
    //     0x15e1d1c: sub             SP, SP, #0x28
    // 0x15e1d20: SetupParameters(ExchangeProductSkusScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e1d20: mov             x0, x1
    //     0x15e1d24: stur            x1, [fp, #-8]
    //     0x15e1d28: mov             x1, x2
    //     0x15e1d2c: stur            x2, [fp, #-0x10]
    // 0x15e1d30: CheckStackOverflow
    //     0x15e1d30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1d34: cmp             SP, x16
    //     0x15e1d38: b.ls            #0x15e1e98
    // 0x15e1d3c: r1 = 2
    //     0x15e1d3c: movz            x1, #0x2
    // 0x15e1d40: r0 = AllocateContext()
    //     0x15e1d40: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e1d44: mov             x1, x0
    // 0x15e1d48: ldur            x0, [fp, #-8]
    // 0x15e1d4c: stur            x1, [fp, #-0x18]
    // 0x15e1d50: StoreField: r1->field_f = r0
    //     0x15e1d50: stur            w0, [x1, #0xf]
    // 0x15e1d54: ldur            x0, [fp, #-0x10]
    // 0x15e1d58: StoreField: r1->field_13 = r0
    //     0x15e1d58: stur            w0, [x1, #0x13]
    // 0x15e1d5c: r0 = Obx()
    //     0x15e1d5c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e1d60: ldur            x2, [fp, #-0x18]
    // 0x15e1d64: r1 = Function '<anonymous closure>':.
    //     0x15e1d64: add             x1, PP, #0x40, lsl #12  ; [pp+0x40818] AnonymousClosure: (0x15d03f0), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::appBar (0x15e9f10)
    //     0x15e1d68: ldr             x1, [x1, #0x818]
    // 0x15e1d6c: stur            x0, [fp, #-8]
    // 0x15e1d70: r0 = AllocateClosure()
    //     0x15e1d70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1d74: mov             x1, x0
    // 0x15e1d78: ldur            x0, [fp, #-8]
    // 0x15e1d7c: StoreField: r0->field_b = r1
    //     0x15e1d7c: stur            w1, [x0, #0xb]
    // 0x15e1d80: ldur            x1, [fp, #-0x10]
    // 0x15e1d84: r0 = of()
    //     0x15e1d84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e1d88: LoadField: r1 = r0->field_5b
    //     0x15e1d88: ldur            w1, [x0, #0x5b]
    // 0x15e1d8c: DecompressPointer r1
    //     0x15e1d8c: add             x1, x1, HEAP, lsl #32
    // 0x15e1d90: stur            x1, [fp, #-0x10]
    // 0x15e1d94: r0 = ColorFilter()
    //     0x15e1d94: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e1d98: mov             x1, x0
    // 0x15e1d9c: ldur            x0, [fp, #-0x10]
    // 0x15e1da0: stur            x1, [fp, #-0x20]
    // 0x15e1da4: StoreField: r1->field_7 = r0
    //     0x15e1da4: stur            w0, [x1, #7]
    // 0x15e1da8: r0 = Instance_BlendMode
    //     0x15e1da8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e1dac: ldr             x0, [x0, #0xb30]
    // 0x15e1db0: StoreField: r1->field_b = r0
    //     0x15e1db0: stur            w0, [x1, #0xb]
    // 0x15e1db4: r0 = 1
    //     0x15e1db4: movz            x0, #0x1
    // 0x15e1db8: StoreField: r1->field_13 = r0
    //     0x15e1db8: stur            x0, [x1, #0x13]
    // 0x15e1dbc: r0 = SvgPicture()
    //     0x15e1dbc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1dc0: stur            x0, [fp, #-0x10]
    // 0x15e1dc4: ldur            x16, [fp, #-0x20]
    // 0x15e1dc8: str             x16, [SP]
    // 0x15e1dcc: mov             x1, x0
    // 0x15e1dd0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e1dd0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e1dd4: ldr             x2, [x2, #0xa40]
    // 0x15e1dd8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e1dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e1ddc: ldr             x4, [x4, #0xa38]
    // 0x15e1de0: r0 = SvgPicture.asset()
    //     0x15e1de0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e1de4: r0 = Align()
    //     0x15e1de4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e1de8: mov             x1, x0
    // 0x15e1dec: r0 = Instance_Alignment
    //     0x15e1dec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1df0: ldr             x0, [x0, #0xb10]
    // 0x15e1df4: stur            x1, [fp, #-0x20]
    // 0x15e1df8: StoreField: r1->field_f = r0
    //     0x15e1df8: stur            w0, [x1, #0xf]
    // 0x15e1dfc: r0 = 1.000000
    //     0x15e1dfc: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e1e00: StoreField: r1->field_13 = r0
    //     0x15e1e00: stur            w0, [x1, #0x13]
    // 0x15e1e04: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e1e04: stur            w0, [x1, #0x17]
    // 0x15e1e08: ldur            x0, [fp, #-0x10]
    // 0x15e1e0c: StoreField: r1->field_b = r0
    //     0x15e1e0c: stur            w0, [x1, #0xb]
    // 0x15e1e10: r0 = InkWell()
    //     0x15e1e10: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e1e14: mov             x3, x0
    // 0x15e1e18: ldur            x0, [fp, #-0x20]
    // 0x15e1e1c: stur            x3, [fp, #-0x10]
    // 0x15e1e20: StoreField: r3->field_b = r0
    //     0x15e1e20: stur            w0, [x3, #0xb]
    // 0x15e1e24: ldur            x2, [fp, #-0x18]
    // 0x15e1e28: r1 = Function '<anonymous closure>':.
    //     0x15e1e28: add             x1, PP, #0x40, lsl #12  ; [pp+0x40820] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e1e2c: ldr             x1, [x1, #0x820]
    // 0x15e1e30: r0 = AllocateClosure()
    //     0x15e1e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1e34: ldur            x2, [fp, #-0x10]
    // 0x15e1e38: StoreField: r2->field_f = r0
    //     0x15e1e38: stur            w0, [x2, #0xf]
    // 0x15e1e3c: r0 = true
    //     0x15e1e3c: add             x0, NULL, #0x20  ; true
    // 0x15e1e40: StoreField: r2->field_43 = r0
    //     0x15e1e40: stur            w0, [x2, #0x43]
    // 0x15e1e44: r1 = Instance_BoxShape
    //     0x15e1e44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e1e48: ldr             x1, [x1, #0x80]
    // 0x15e1e4c: StoreField: r2->field_47 = r1
    //     0x15e1e4c: stur            w1, [x2, #0x47]
    // 0x15e1e50: StoreField: r2->field_6f = r0
    //     0x15e1e50: stur            w0, [x2, #0x6f]
    // 0x15e1e54: r1 = false
    //     0x15e1e54: add             x1, NULL, #0x30  ; false
    // 0x15e1e58: StoreField: r2->field_73 = r1
    //     0x15e1e58: stur            w1, [x2, #0x73]
    // 0x15e1e5c: StoreField: r2->field_83 = r0
    //     0x15e1e5c: stur            w0, [x2, #0x83]
    // 0x15e1e60: StoreField: r2->field_7b = r1
    //     0x15e1e60: stur            w1, [x2, #0x7b]
    // 0x15e1e64: r0 = AppBar()
    //     0x15e1e64: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e1e68: stur            x0, [fp, #-0x18]
    // 0x15e1e6c: ldur            x16, [fp, #-8]
    // 0x15e1e70: str             x16, [SP]
    // 0x15e1e74: mov             x1, x0
    // 0x15e1e78: ldur            x2, [fp, #-0x10]
    // 0x15e1e7c: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e1e7c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e1e80: ldr             x4, [x4, #0xf00]
    // 0x15e1e84: r0 = AppBar()
    //     0x15e1e84: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e1e88: ldur            x0, [fp, #-0x18]
    // 0x15e1e8c: LeaveFrame
    //     0x15e1e8c: mov             SP, fp
    //     0x15e1e90: ldp             fp, lr, [SP], #0x10
    // 0x15e1e94: ret
    //     0x15e1e94: ret             
    // 0x15e1e98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e1e98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e1e9c: b               #0x15e1d3c
  }
}
