// lib: , url: package:customer_app/app/presentation/views/glass/customization/customized_view.dart

// class id: 1049389, size: 0x8
class :: {
}

// class id: 3344, size: 0x14, field offset: 0x14
class _CustomizedViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb5ae3c, size: 0x5fc
    // 0xb5ae3c: EnterFrame
    //     0xb5ae3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5ae40: mov             fp, SP
    // 0xb5ae44: AllocStack(0x68)
    //     0xb5ae44: sub             SP, SP, #0x68
    // 0xb5ae48: SetupParameters(_CustomizedViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb5ae48: mov             x0, x1
    //     0xb5ae4c: stur            x1, [fp, #-8]
    //     0xb5ae50: mov             x1, x2
    //     0xb5ae54: stur            x2, [fp, #-0x10]
    // 0xb5ae58: CheckStackOverflow
    //     0xb5ae58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5ae5c: cmp             SP, x16
    //     0xb5ae60: b.ls            #0xb5b424
    // 0xb5ae64: r1 = 1
    //     0xb5ae64: movz            x1, #0x1
    // 0xb5ae68: r0 = AllocateContext()
    //     0xb5ae68: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5ae6c: mov             x2, x0
    // 0xb5ae70: ldur            x0, [fp, #-8]
    // 0xb5ae74: stur            x2, [fp, #-0x18]
    // 0xb5ae78: StoreField: r2->field_f = r0
    //     0xb5ae78: stur            w0, [x2, #0xf]
    // 0xb5ae7c: ldur            x1, [fp, #-0x10]
    // 0xb5ae80: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5ae80: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5ae84: r0 = _of()
    //     0xb5ae84: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb5ae88: LoadField: r1 = r0->field_23
    //     0xb5ae88: ldur            w1, [x0, #0x23]
    // 0xb5ae8c: DecompressPointer r1
    //     0xb5ae8c: add             x1, x1, HEAP, lsl #32
    // 0xb5ae90: LoadField: d0 = r1->field_1f
    //     0xb5ae90: ldur            d0, [x1, #0x1f]
    // 0xb5ae94: stur            d0, [fp, #-0x50]
    // 0xb5ae98: r0 = EdgeInsets()
    //     0xb5ae98: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb5ae9c: stur            x0, [fp, #-0x20]
    // 0xb5aea0: StoreField: r0->field_7 = rZR
    //     0xb5aea0: stur            xzr, [x0, #7]
    // 0xb5aea4: StoreField: r0->field_f = rZR
    //     0xb5aea4: stur            xzr, [x0, #0xf]
    // 0xb5aea8: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb5aea8: stur            xzr, [x0, #0x17]
    // 0xb5aeac: ldur            d0, [fp, #-0x50]
    // 0xb5aeb0: StoreField: r0->field_1f = d0
    //     0xb5aeb0: stur            d0, [x0, #0x1f]
    // 0xb5aeb4: ldur            x1, [fp, #-8]
    // 0xb5aeb8: LoadField: r2 = r1->field_b
    //     0xb5aeb8: ldur            w2, [x1, #0xb]
    // 0xb5aebc: DecompressPointer r2
    //     0xb5aebc: add             x2, x2, HEAP, lsl #32
    // 0xb5aec0: cmp             w2, NULL
    // 0xb5aec4: b.eq            #0xb5b42c
    // 0xb5aec8: LoadField: r3 = r2->field_b
    //     0xb5aec8: ldur            w3, [x2, #0xb]
    // 0xb5aecc: DecompressPointer r3
    //     0xb5aecc: add             x3, x3, HEAP, lsl #32
    // 0xb5aed0: LoadField: r2 = r3->field_b
    //     0xb5aed0: ldur            w2, [x3, #0xb]
    // 0xb5aed4: DecompressPointer r2
    //     0xb5aed4: add             x2, x2, HEAP, lsl #32
    // 0xb5aed8: cmp             w2, NULL
    // 0xb5aedc: b.ne            #0xb5aee8
    // 0xb5aee0: r2 = Null
    //     0xb5aee0: mov             x2, NULL
    // 0xb5aee4: b               #0xb5aef4
    // 0xb5aee8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb5aee8: ldur            w3, [x2, #0x17]
    // 0xb5aeec: DecompressPointer r3
    //     0xb5aeec: add             x3, x3, HEAP, lsl #32
    // 0xb5aef0: mov             x2, x3
    // 0xb5aef4: str             x2, [SP]
    // 0xb5aef8: r0 = _interpolateSingle()
    //     0xb5aef8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb5aefc: ldur            x1, [fp, #-0x10]
    // 0xb5af00: stur            x0, [fp, #-0x28]
    // 0xb5af04: r0 = of()
    //     0xb5af04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5af08: LoadField: r1 = r0->field_87
    //     0xb5af08: ldur            w1, [x0, #0x87]
    // 0xb5af0c: DecompressPointer r1
    //     0xb5af0c: add             x1, x1, HEAP, lsl #32
    // 0xb5af10: LoadField: r0 = r1->field_7
    //     0xb5af10: ldur            w0, [x1, #7]
    // 0xb5af14: DecompressPointer r0
    //     0xb5af14: add             x0, x0, HEAP, lsl #32
    // 0xb5af18: r16 = 16.000000
    //     0xb5af18: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5af1c: ldr             x16, [x16, #0x188]
    // 0xb5af20: r30 = Instance_Color
    //     0xb5af20: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5af24: stp             lr, x16, [SP]
    // 0xb5af28: mov             x1, x0
    // 0xb5af2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5af2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5af30: ldr             x4, [x4, #0xaa0]
    // 0xb5af34: r0 = copyWith()
    //     0xb5af34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5af38: stur            x0, [fp, #-0x30]
    // 0xb5af3c: r0 = Text()
    //     0xb5af3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5af40: mov             x2, x0
    // 0xb5af44: ldur            x0, [fp, #-0x28]
    // 0xb5af48: stur            x2, [fp, #-0x38]
    // 0xb5af4c: StoreField: r2->field_b = r0
    //     0xb5af4c: stur            w0, [x2, #0xb]
    // 0xb5af50: ldur            x0, [fp, #-0x30]
    // 0xb5af54: StoreField: r2->field_13 = r0
    //     0xb5af54: stur            w0, [x2, #0x13]
    // 0xb5af58: r1 = <FlexParentData>
    //     0xb5af58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5af5c: ldr             x1, [x1, #0xe00]
    // 0xb5af60: r0 = Expanded()
    //     0xb5af60: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb5af64: mov             x2, x0
    // 0xb5af68: r0 = 1
    //     0xb5af68: movz            x0, #0x1
    // 0xb5af6c: stur            x2, [fp, #-0x28]
    // 0xb5af70: StoreField: r2->field_13 = r0
    //     0xb5af70: stur            x0, [x2, #0x13]
    // 0xb5af74: r1 = Instance_FlexFit
    //     0xb5af74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb5af78: ldr             x1, [x1, #0xe08]
    // 0xb5af7c: StoreField: r2->field_1b = r1
    //     0xb5af7c: stur            w1, [x2, #0x1b]
    // 0xb5af80: ldur            x1, [fp, #-0x38]
    // 0xb5af84: StoreField: r2->field_b = r1
    //     0xb5af84: stur            w1, [x2, #0xb]
    // 0xb5af88: ldur            x1, [fp, #-0x10]
    // 0xb5af8c: r0 = of()
    //     0xb5af8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5af90: LoadField: r1 = r0->field_5b
    //     0xb5af90: ldur            w1, [x0, #0x5b]
    // 0xb5af94: DecompressPointer r1
    //     0xb5af94: add             x1, x1, HEAP, lsl #32
    // 0xb5af98: stur            x1, [fp, #-0x10]
    // 0xb5af9c: r0 = ColorFilter()
    //     0xb5af9c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb5afa0: mov             x1, x0
    // 0xb5afa4: ldur            x0, [fp, #-0x10]
    // 0xb5afa8: stur            x1, [fp, #-0x30]
    // 0xb5afac: StoreField: r1->field_7 = r0
    //     0xb5afac: stur            w0, [x1, #7]
    // 0xb5afb0: r0 = Instance_BlendMode
    //     0xb5afb0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb5afb4: ldr             x0, [x0, #0xb30]
    // 0xb5afb8: StoreField: r1->field_b = r0
    //     0xb5afb8: stur            w0, [x1, #0xb]
    // 0xb5afbc: r0 = 1
    //     0xb5afbc: movz            x0, #0x1
    // 0xb5afc0: StoreField: r1->field_13 = r0
    //     0xb5afc0: stur            x0, [x1, #0x13]
    // 0xb5afc4: r0 = SvgPicture()
    //     0xb5afc4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb5afc8: stur            x0, [fp, #-0x10]
    // 0xb5afcc: ldur            x16, [fp, #-0x30]
    // 0xb5afd0: str             x16, [SP]
    // 0xb5afd4: mov             x1, x0
    // 0xb5afd8: r2 = "assets/images/x.svg"
    //     0xb5afd8: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb5afdc: ldr             x2, [x2, #0x5e8]
    // 0xb5afe0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb5afe0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb5afe4: ldr             x4, [x4, #0xa38]
    // 0xb5afe8: r0 = SvgPicture.asset()
    //     0xb5afe8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb5afec: r0 = InkWell()
    //     0xb5afec: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb5aff0: mov             x3, x0
    // 0xb5aff4: ldur            x0, [fp, #-0x10]
    // 0xb5aff8: stur            x3, [fp, #-0x30]
    // 0xb5affc: StoreField: r3->field_b = r0
    //     0xb5affc: stur            w0, [x3, #0xb]
    // 0xb5b000: r1 = Function '<anonymous closure>':.
    //     0xb5b000: add             x1, PP, #0x56, lsl #12  ; [pp+0x56088] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5b004: ldr             x1, [x1, #0x88]
    // 0xb5b008: r2 = Null
    //     0xb5b008: mov             x2, NULL
    // 0xb5b00c: r0 = AllocateClosure()
    //     0xb5b00c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b010: mov             x1, x0
    // 0xb5b014: ldur            x0, [fp, #-0x30]
    // 0xb5b018: StoreField: r0->field_f = r1
    //     0xb5b018: stur            w1, [x0, #0xf]
    // 0xb5b01c: r1 = true
    //     0xb5b01c: add             x1, NULL, #0x20  ; true
    // 0xb5b020: StoreField: r0->field_43 = r1
    //     0xb5b020: stur            w1, [x0, #0x43]
    // 0xb5b024: r2 = Instance_BoxShape
    //     0xb5b024: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5b028: ldr             x2, [x2, #0x80]
    // 0xb5b02c: StoreField: r0->field_47 = r2
    //     0xb5b02c: stur            w2, [x0, #0x47]
    // 0xb5b030: StoreField: r0->field_6f = r1
    //     0xb5b030: stur            w1, [x0, #0x6f]
    // 0xb5b034: r3 = false
    //     0xb5b034: add             x3, NULL, #0x30  ; false
    // 0xb5b038: StoreField: r0->field_73 = r3
    //     0xb5b038: stur            w3, [x0, #0x73]
    // 0xb5b03c: StoreField: r0->field_83 = r1
    //     0xb5b03c: stur            w1, [x0, #0x83]
    // 0xb5b040: StoreField: r0->field_7b = r3
    //     0xb5b040: stur            w3, [x0, #0x7b]
    // 0xb5b044: r1 = Null
    //     0xb5b044: mov             x1, NULL
    // 0xb5b048: r2 = 6
    //     0xb5b048: movz            x2, #0x6
    // 0xb5b04c: r0 = AllocateArray()
    //     0xb5b04c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5b050: mov             x2, x0
    // 0xb5b054: ldur            x0, [fp, #-0x28]
    // 0xb5b058: stur            x2, [fp, #-0x10]
    // 0xb5b05c: StoreField: r2->field_f = r0
    //     0xb5b05c: stur            w0, [x2, #0xf]
    // 0xb5b060: r16 = Instance_SizedBox
    //     0xb5b060: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb5b064: ldr             x16, [x16, #0xb20]
    // 0xb5b068: StoreField: r2->field_13 = r16
    //     0xb5b068: stur            w16, [x2, #0x13]
    // 0xb5b06c: ldur            x0, [fp, #-0x30]
    // 0xb5b070: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5b070: stur            w0, [x2, #0x17]
    // 0xb5b074: r1 = <Widget>
    //     0xb5b074: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5b078: r0 = AllocateGrowableArray()
    //     0xb5b078: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5b07c: mov             x1, x0
    // 0xb5b080: ldur            x0, [fp, #-0x10]
    // 0xb5b084: stur            x1, [fp, #-0x28]
    // 0xb5b088: StoreField: r1->field_f = r0
    //     0xb5b088: stur            w0, [x1, #0xf]
    // 0xb5b08c: r2 = 6
    //     0xb5b08c: movz            x2, #0x6
    // 0xb5b090: StoreField: r1->field_b = r2
    //     0xb5b090: stur            w2, [x1, #0xb]
    // 0xb5b094: r0 = Row()
    //     0xb5b094: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5b098: mov             x1, x0
    // 0xb5b09c: r0 = Instance_Axis
    //     0xb5b09c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5b0a0: stur            x1, [fp, #-0x10]
    // 0xb5b0a4: StoreField: r1->field_f = r0
    //     0xb5b0a4: stur            w0, [x1, #0xf]
    // 0xb5b0a8: r0 = Instance_MainAxisAlignment
    //     0xb5b0a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5b0ac: ldr             x0, [x0, #0xa08]
    // 0xb5b0b0: StoreField: r1->field_13 = r0
    //     0xb5b0b0: stur            w0, [x1, #0x13]
    // 0xb5b0b4: r2 = Instance_MainAxisSize
    //     0xb5b0b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5b0b8: ldr             x2, [x2, #0xa10]
    // 0xb5b0bc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb5b0bc: stur            w2, [x1, #0x17]
    // 0xb5b0c0: r3 = Instance_CrossAxisAlignment
    //     0xb5b0c0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5b0c4: ldr             x3, [x3, #0x890]
    // 0xb5b0c8: StoreField: r1->field_1b = r3
    //     0xb5b0c8: stur            w3, [x1, #0x1b]
    // 0xb5b0cc: r4 = Instance_VerticalDirection
    //     0xb5b0cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5b0d0: ldr             x4, [x4, #0xa20]
    // 0xb5b0d4: StoreField: r1->field_23 = r4
    //     0xb5b0d4: stur            w4, [x1, #0x23]
    // 0xb5b0d8: r5 = Instance_Clip
    //     0xb5b0d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5b0dc: ldr             x5, [x5, #0x38]
    // 0xb5b0e0: StoreField: r1->field_2b = r5
    //     0xb5b0e0: stur            w5, [x1, #0x2b]
    // 0xb5b0e4: StoreField: r1->field_2f = rZR
    //     0xb5b0e4: stur            xzr, [x1, #0x2f]
    // 0xb5b0e8: ldur            x6, [fp, #-0x28]
    // 0xb5b0ec: StoreField: r1->field_b = r6
    //     0xb5b0ec: stur            w6, [x1, #0xb]
    // 0xb5b0f0: r0 = Container()
    //     0xb5b0f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5b0f4: stur            x0, [fp, #-0x28]
    // 0xb5b0f8: r16 = Instance_Color
    //     0xb5b0f8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb5b0fc: r30 = Instance_EdgeInsets
    //     0xb5b0fc: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xb5b100: ldr             lr, [lr, #0x560]
    // 0xb5b104: stp             lr, x16, [SP, #8]
    // 0xb5b108: ldur            x16, [fp, #-0x10]
    // 0xb5b10c: str             x16, [SP]
    // 0xb5b110: mov             x1, x0
    // 0xb5b114: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, padding, 0x2, null]
    //     0xb5b114: add             x4, PP, #0x45, lsl #12  ; [pp+0x45c40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "padding", 0x2, Null]
    //     0xb5b118: ldr             x4, [x4, #0xc40]
    // 0xb5b11c: r0 = Container()
    //     0xb5b11c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5b120: ldur            x0, [fp, #-8]
    // 0xb5b124: LoadField: r1 = r0->field_b
    //     0xb5b124: ldur            w1, [x0, #0xb]
    // 0xb5b128: DecompressPointer r1
    //     0xb5b128: add             x1, x1, HEAP, lsl #32
    // 0xb5b12c: cmp             w1, NULL
    // 0xb5b130: b.eq            #0xb5b430
    // 0xb5b134: LoadField: r2 = r1->field_b
    //     0xb5b134: ldur            w2, [x1, #0xb]
    // 0xb5b138: DecompressPointer r2
    //     0xb5b138: add             x2, x2, HEAP, lsl #32
    // 0xb5b13c: LoadField: r1 = r2->field_b
    //     0xb5b13c: ldur            w1, [x2, #0xb]
    // 0xb5b140: DecompressPointer r1
    //     0xb5b140: add             x1, x1, HEAP, lsl #32
    // 0xb5b144: cmp             w1, NULL
    // 0xb5b148: b.ne            #0xb5b154
    // 0xb5b14c: r2 = Null
    //     0xb5b14c: mov             x2, NULL
    // 0xb5b150: b               #0xb5b19c
    // 0xb5b154: LoadField: r2 = r1->field_1b
    //     0xb5b154: ldur            w2, [x1, #0x1b]
    // 0xb5b158: DecompressPointer r2
    //     0xb5b158: add             x2, x2, HEAP, lsl #32
    // 0xb5b15c: cmp             w2, NULL
    // 0xb5b160: b.ne            #0xb5b16c
    // 0xb5b164: r2 = Null
    //     0xb5b164: mov             x2, NULL
    // 0xb5b168: b               #0xb5b19c
    // 0xb5b16c: LoadField: r3 = r2->field_7
    //     0xb5b16c: ldur            w3, [x2, #7]
    // 0xb5b170: DecompressPointer r3
    //     0xb5b170: add             x3, x3, HEAP, lsl #32
    // 0xb5b174: cmp             w3, NULL
    // 0xb5b178: b.ne            #0xb5b184
    // 0xb5b17c: r2 = Null
    //     0xb5b17c: mov             x2, NULL
    // 0xb5b180: b               #0xb5b19c
    // 0xb5b184: LoadField: r2 = r3->field_7
    //     0xb5b184: ldur            w2, [x3, #7]
    // 0xb5b188: cbnz            w2, #0xb5b194
    // 0xb5b18c: r3 = false
    //     0xb5b18c: add             x3, NULL, #0x30  ; false
    // 0xb5b190: b               #0xb5b198
    // 0xb5b194: r3 = true
    //     0xb5b194: add             x3, NULL, #0x20  ; true
    // 0xb5b198: mov             x2, x3
    // 0xb5b19c: cmp             w2, NULL
    // 0xb5b1a0: b.ne            #0xb5b1ac
    // 0xb5b1a4: r3 = false
    //     0xb5b1a4: add             x3, NULL, #0x30  ; false
    // 0xb5b1a8: b               #0xb5b1b0
    // 0xb5b1ac: mov             x3, x2
    // 0xb5b1b0: stur            x3, [fp, #-0x30]
    // 0xb5b1b4: cmp             w1, NULL
    // 0xb5b1b8: b.ne            #0xb5b1c4
    // 0xb5b1bc: r1 = Null
    //     0xb5b1bc: mov             x1, NULL
    // 0xb5b1c0: b               #0xb5b1e4
    // 0xb5b1c4: LoadField: r2 = r1->field_1b
    //     0xb5b1c4: ldur            w2, [x1, #0x1b]
    // 0xb5b1c8: DecompressPointer r2
    //     0xb5b1c8: add             x2, x2, HEAP, lsl #32
    // 0xb5b1cc: cmp             w2, NULL
    // 0xb5b1d0: b.ne            #0xb5b1dc
    // 0xb5b1d4: r1 = Null
    //     0xb5b1d4: mov             x1, NULL
    // 0xb5b1d8: b               #0xb5b1e4
    // 0xb5b1dc: LoadField: r1 = r2->field_7
    //     0xb5b1dc: ldur            w1, [x2, #7]
    // 0xb5b1e0: DecompressPointer r1
    //     0xb5b1e0: add             x1, x1, HEAP, lsl #32
    // 0xb5b1e4: cmp             w1, NULL
    // 0xb5b1e8: b.ne            #0xb5b1f4
    // 0xb5b1ec: r4 = ""
    //     0xb5b1ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5b1f0: b               #0xb5b1f8
    // 0xb5b1f4: mov             x4, x1
    // 0xb5b1f8: stur            x4, [fp, #-0x10]
    // 0xb5b1fc: r1 = Function '<anonymous closure>':.
    //     0xb5b1fc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56090] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb5b200: ldr             x1, [x1, #0x90]
    // 0xb5b204: r2 = Null
    //     0xb5b204: mov             x2, NULL
    // 0xb5b208: r0 = AllocateClosure()
    //     0xb5b208: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b20c: r1 = Function '<anonymous closure>':.
    //     0xb5b20c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56098] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5b210: ldr             x1, [x1, #0x98]
    // 0xb5b214: r2 = Null
    //     0xb5b214: mov             x2, NULL
    // 0xb5b218: stur            x0, [fp, #-0x38]
    // 0xb5b21c: r0 = AllocateClosure()
    //     0xb5b21c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b220: stur            x0, [fp, #-0x40]
    // 0xb5b224: r0 = CachedNetworkImage()
    //     0xb5b224: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb5b228: stur            x0, [fp, #-0x48]
    // 0xb5b22c: ldur            x16, [fp, #-0x38]
    // 0xb5b230: ldur            lr, [fp, #-0x40]
    // 0xb5b234: stp             lr, x16, [SP]
    // 0xb5b238: mov             x1, x0
    // 0xb5b23c: ldur            x2, [fp, #-0x10]
    // 0xb5b240: r4 = const [0, 0x4, 0x2, 0x2, errorWidget, 0x3, progressIndicatorBuilder, 0x2, null]
    //     0xb5b240: add             x4, PP, #0x55, lsl #12  ; [pp+0x55af8] List(9) [0, 0x4, 0x2, 0x2, "errorWidget", 0x3, "progressIndicatorBuilder", 0x2, Null]
    //     0xb5b244: ldr             x4, [x4, #0xaf8]
    // 0xb5b248: r0 = CachedNetworkImage()
    //     0xb5b248: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb5b24c: r0 = Padding()
    //     0xb5b24c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5b250: mov             x1, x0
    // 0xb5b254: r0 = Instance_EdgeInsets
    //     0xb5b254: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5b258: ldr             x0, [x0, #0x1f0]
    // 0xb5b25c: stur            x1, [fp, #-0x10]
    // 0xb5b260: StoreField: r1->field_f = r0
    //     0xb5b260: stur            w0, [x1, #0xf]
    // 0xb5b264: ldur            x0, [fp, #-0x48]
    // 0xb5b268: StoreField: r1->field_b = r0
    //     0xb5b268: stur            w0, [x1, #0xb]
    // 0xb5b26c: r0 = Visibility()
    //     0xb5b26c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb5b270: mov             x3, x0
    // 0xb5b274: ldur            x0, [fp, #-0x10]
    // 0xb5b278: stur            x3, [fp, #-0x38]
    // 0xb5b27c: StoreField: r3->field_b = r0
    //     0xb5b27c: stur            w0, [x3, #0xb]
    // 0xb5b280: r0 = Instance_SizedBox
    //     0xb5b280: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb5b284: StoreField: r3->field_f = r0
    //     0xb5b284: stur            w0, [x3, #0xf]
    // 0xb5b288: ldur            x0, [fp, #-0x30]
    // 0xb5b28c: StoreField: r3->field_13 = r0
    //     0xb5b28c: stur            w0, [x3, #0x13]
    // 0xb5b290: r0 = false
    //     0xb5b290: add             x0, NULL, #0x30  ; false
    // 0xb5b294: ArrayStore: r3[0] = r0  ; List_4
    //     0xb5b294: stur            w0, [x3, #0x17]
    // 0xb5b298: StoreField: r3->field_1b = r0
    //     0xb5b298: stur            w0, [x3, #0x1b]
    // 0xb5b29c: StoreField: r3->field_1f = r0
    //     0xb5b29c: stur            w0, [x3, #0x1f]
    // 0xb5b2a0: StoreField: r3->field_23 = r0
    //     0xb5b2a0: stur            w0, [x3, #0x23]
    // 0xb5b2a4: StoreField: r3->field_27 = r0
    //     0xb5b2a4: stur            w0, [x3, #0x27]
    // 0xb5b2a8: StoreField: r3->field_2b = r0
    //     0xb5b2a8: stur            w0, [x3, #0x2b]
    // 0xb5b2ac: ldur            x0, [fp, #-8]
    // 0xb5b2b0: LoadField: r1 = r0->field_b
    //     0xb5b2b0: ldur            w1, [x0, #0xb]
    // 0xb5b2b4: DecompressPointer r1
    //     0xb5b2b4: add             x1, x1, HEAP, lsl #32
    // 0xb5b2b8: cmp             w1, NULL
    // 0xb5b2bc: b.eq            #0xb5b434
    // 0xb5b2c0: LoadField: r0 = r1->field_b
    //     0xb5b2c0: ldur            w0, [x1, #0xb]
    // 0xb5b2c4: DecompressPointer r0
    //     0xb5b2c4: add             x0, x0, HEAP, lsl #32
    // 0xb5b2c8: LoadField: r1 = r0->field_b
    //     0xb5b2c8: ldur            w1, [x0, #0xb]
    // 0xb5b2cc: DecompressPointer r1
    //     0xb5b2cc: add             x1, x1, HEAP, lsl #32
    // 0xb5b2d0: cmp             w1, NULL
    // 0xb5b2d4: b.ne            #0xb5b2e0
    // 0xb5b2d8: r5 = Null
    //     0xb5b2d8: mov             x5, NULL
    // 0xb5b2dc: b               #0xb5b304
    // 0xb5b2e0: LoadField: r0 = r1->field_f
    //     0xb5b2e0: ldur            w0, [x1, #0xf]
    // 0xb5b2e4: DecompressPointer r0
    //     0xb5b2e4: add             x0, x0, HEAP, lsl #32
    // 0xb5b2e8: cmp             w0, NULL
    // 0xb5b2ec: b.ne            #0xb5b2f8
    // 0xb5b2f0: r0 = Null
    //     0xb5b2f0: mov             x0, NULL
    // 0xb5b2f4: b               #0xb5b300
    // 0xb5b2f8: LoadField: r1 = r0->field_b
    //     0xb5b2f8: ldur            w1, [x0, #0xb]
    // 0xb5b2fc: mov             x0, x1
    // 0xb5b300: mov             x5, x0
    // 0xb5b304: ldur            x4, [fp, #-0x20]
    // 0xb5b308: ldur            x0, [fp, #-0x28]
    // 0xb5b30c: ldur            x2, [fp, #-0x18]
    // 0xb5b310: stur            x5, [fp, #-8]
    // 0xb5b314: r1 = Function '<anonymous closure>':.
    //     0xb5b314: add             x1, PP, #0x56, lsl #12  ; [pp+0x560a0] AnonymousClosure: (0xb5b458), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b318: ldr             x1, [x1, #0xa0]
    // 0xb5b31c: r0 = AllocateClosure()
    //     0xb5b31c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b320: stur            x0, [fp, #-0x10]
    // 0xb5b324: r0 = ListView()
    //     0xb5b324: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb5b328: stur            x0, [fp, #-0x18]
    // 0xb5b32c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb5b32c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb5b330: ldr             x16, [x16, #0x1c8]
    // 0xb5b334: r30 = Instance_Axis
    //     0xb5b334: ldr             lr, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5b338: stp             lr, x16, [SP, #8]
    // 0xb5b33c: r16 = true
    //     0xb5b33c: add             x16, NULL, #0x20  ; true
    // 0xb5b340: str             x16, [SP]
    // 0xb5b344: mov             x1, x0
    // 0xb5b348: ldur            x2, [fp, #-0x10]
    // 0xb5b34c: ldur            x3, [fp, #-8]
    // 0xb5b350: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x3, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0xb5b350: add             x4, PP, #0x34, lsl #12  ; [pp+0x346d8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0xb5b354: ldr             x4, [x4, #0x6d8]
    // 0xb5b358: r0 = ListView.builder()
    //     0xb5b358: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb5b35c: r1 = Null
    //     0xb5b35c: mov             x1, NULL
    // 0xb5b360: r2 = 6
    //     0xb5b360: movz            x2, #0x6
    // 0xb5b364: r0 = AllocateArray()
    //     0xb5b364: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5b368: mov             x2, x0
    // 0xb5b36c: ldur            x0, [fp, #-0x28]
    // 0xb5b370: stur            x2, [fp, #-8]
    // 0xb5b374: StoreField: r2->field_f = r0
    //     0xb5b374: stur            w0, [x2, #0xf]
    // 0xb5b378: ldur            x0, [fp, #-0x38]
    // 0xb5b37c: StoreField: r2->field_13 = r0
    //     0xb5b37c: stur            w0, [x2, #0x13]
    // 0xb5b380: ldur            x0, [fp, #-0x18]
    // 0xb5b384: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5b384: stur            w0, [x2, #0x17]
    // 0xb5b388: r1 = <Widget>
    //     0xb5b388: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5b38c: r0 = AllocateGrowableArray()
    //     0xb5b38c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5b390: mov             x1, x0
    // 0xb5b394: ldur            x0, [fp, #-8]
    // 0xb5b398: stur            x1, [fp, #-0x10]
    // 0xb5b39c: StoreField: r1->field_f = r0
    //     0xb5b39c: stur            w0, [x1, #0xf]
    // 0xb5b3a0: r0 = 6
    //     0xb5b3a0: movz            x0, #0x6
    // 0xb5b3a4: StoreField: r1->field_b = r0
    //     0xb5b3a4: stur            w0, [x1, #0xb]
    // 0xb5b3a8: r0 = Column()
    //     0xb5b3a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5b3ac: mov             x1, x0
    // 0xb5b3b0: r0 = Instance_Axis
    //     0xb5b3b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5b3b4: stur            x1, [fp, #-8]
    // 0xb5b3b8: StoreField: r1->field_f = r0
    //     0xb5b3b8: stur            w0, [x1, #0xf]
    // 0xb5b3bc: r0 = Instance_MainAxisAlignment
    //     0xb5b3bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5b3c0: ldr             x0, [x0, #0xa08]
    // 0xb5b3c4: StoreField: r1->field_13 = r0
    //     0xb5b3c4: stur            w0, [x1, #0x13]
    // 0xb5b3c8: r0 = Instance_MainAxisSize
    //     0xb5b3c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5b3cc: ldr             x0, [x0, #0xa10]
    // 0xb5b3d0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5b3d0: stur            w0, [x1, #0x17]
    // 0xb5b3d4: r0 = Instance_CrossAxisAlignment
    //     0xb5b3d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5b3d8: ldr             x0, [x0, #0x890]
    // 0xb5b3dc: StoreField: r1->field_1b = r0
    //     0xb5b3dc: stur            w0, [x1, #0x1b]
    // 0xb5b3e0: r0 = Instance_VerticalDirection
    //     0xb5b3e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5b3e4: ldr             x0, [x0, #0xa20]
    // 0xb5b3e8: StoreField: r1->field_23 = r0
    //     0xb5b3e8: stur            w0, [x1, #0x23]
    // 0xb5b3ec: r0 = Instance_Clip
    //     0xb5b3ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5b3f0: ldr             x0, [x0, #0x38]
    // 0xb5b3f4: StoreField: r1->field_2b = r0
    //     0xb5b3f4: stur            w0, [x1, #0x2b]
    // 0xb5b3f8: StoreField: r1->field_2f = rZR
    //     0xb5b3f8: stur            xzr, [x1, #0x2f]
    // 0xb5b3fc: ldur            x0, [fp, #-0x10]
    // 0xb5b400: StoreField: r1->field_b = r0
    //     0xb5b400: stur            w0, [x1, #0xb]
    // 0xb5b404: r0 = Padding()
    //     0xb5b404: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5b408: ldur            x1, [fp, #-0x20]
    // 0xb5b40c: StoreField: r0->field_f = r1
    //     0xb5b40c: stur            w1, [x0, #0xf]
    // 0xb5b410: ldur            x1, [fp, #-8]
    // 0xb5b414: StoreField: r0->field_b = r1
    //     0xb5b414: stur            w1, [x0, #0xb]
    // 0xb5b418: LeaveFrame
    //     0xb5b418: mov             SP, fp
    //     0xb5b41c: ldp             fp, lr, [SP], #0x10
    // 0xb5b420: ret
    //     0xb5b420: ret             
    // 0xb5b424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5b424: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5b428: b               #0xb5ae64
    // 0xb5b42c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5b42c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5b430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5b430: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5b434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5b434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb5b458, size: 0x79c
    // 0xb5b458: EnterFrame
    //     0xb5b458: stp             fp, lr, [SP, #-0x10]!
    //     0xb5b45c: mov             fp, SP
    // 0xb5b460: AllocStack(0x28)
    //     0xb5b460: sub             SP, SP, #0x28
    // 0xb5b464: SetupParameters()
    //     0xb5b464: ldr             x0, [fp, #0x20]
    //     0xb5b468: ldur            w2, [x0, #0x17]
    //     0xb5b46c: add             x2, x2, HEAP, lsl #32
    //     0xb5b470: stur            x2, [fp, #-8]
    // 0xb5b474: CheckStackOverflow
    //     0xb5b474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5b478: cmp             SP, x16
    //     0xb5b47c: b.ls            #0xb5bbbc
    // 0xb5b480: r0 = Container()
    //     0xb5b480: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5b484: mov             x1, x0
    // 0xb5b488: stur            x0, [fp, #-0x10]
    // 0xb5b48c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5b48c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5b490: r0 = Container()
    //     0xb5b490: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5b494: ldur            x2, [fp, #-8]
    // 0xb5b498: LoadField: r0 = r2->field_f
    //     0xb5b498: ldur            w0, [x2, #0xf]
    // 0xb5b49c: DecompressPointer r0
    //     0xb5b49c: add             x0, x0, HEAP, lsl #32
    // 0xb5b4a0: LoadField: r3 = r0->field_b
    //     0xb5b4a0: ldur            w3, [x0, #0xb]
    // 0xb5b4a4: DecompressPointer r3
    //     0xb5b4a4: add             x3, x3, HEAP, lsl #32
    // 0xb5b4a8: cmp             w3, NULL
    // 0xb5b4ac: b.eq            #0xb5bbc4
    // 0xb5b4b0: LoadField: r0 = r3->field_b
    //     0xb5b4b0: ldur            w0, [x3, #0xb]
    // 0xb5b4b4: DecompressPointer r0
    //     0xb5b4b4: add             x0, x0, HEAP, lsl #32
    // 0xb5b4b8: LoadField: r4 = r0->field_b
    //     0xb5b4b8: ldur            w4, [x0, #0xb]
    // 0xb5b4bc: DecompressPointer r4
    //     0xb5b4bc: add             x4, x4, HEAP, lsl #32
    // 0xb5b4c0: cmp             w4, NULL
    // 0xb5b4c4: b.ne            #0xb5b4d0
    // 0xb5b4c8: ldr             x6, [fp, #0x10]
    // 0xb5b4cc: b               #0xb5b618
    // 0xb5b4d0: LoadField: r5 = r4->field_f
    //     0xb5b4d0: ldur            w5, [x4, #0xf]
    // 0xb5b4d4: DecompressPointer r5
    //     0xb5b4d4: add             x5, x5, HEAP, lsl #32
    // 0xb5b4d8: cmp             w5, NULL
    // 0xb5b4dc: b.ne            #0xb5b4e8
    // 0xb5b4e0: ldr             x6, [fp, #0x10]
    // 0xb5b4e4: b               #0xb5b618
    // 0xb5b4e8: ldr             x6, [fp, #0x10]
    // 0xb5b4ec: LoadField: r0 = r5->field_b
    //     0xb5b4ec: ldur            w0, [x5, #0xb]
    // 0xb5b4f0: r7 = LoadInt32Instr(r6)
    //     0xb5b4f0: sbfx            x7, x6, #1, #0x1f
    //     0xb5b4f4: tbz             w6, #0, #0xb5b4fc
    //     0xb5b4f8: ldur            x7, [x6, #7]
    // 0xb5b4fc: r1 = LoadInt32Instr(r0)
    //     0xb5b4fc: sbfx            x1, x0, #1, #0x1f
    // 0xb5b500: mov             x0, x1
    // 0xb5b504: mov             x1, x7
    // 0xb5b508: cmp             x1, x0
    // 0xb5b50c: b.hs            #0xb5bbc8
    // 0xb5b510: LoadField: r0 = r5->field_f
    //     0xb5b510: ldur            w0, [x5, #0xf]
    // 0xb5b514: DecompressPointer r0
    //     0xb5b514: add             x0, x0, HEAP, lsl #32
    // 0xb5b518: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb5b518: add             x16, x0, x7, lsl #2
    //     0xb5b51c: ldur            w1, [x16, #0xf]
    // 0xb5b520: DecompressPointer r1
    //     0xb5b520: add             x1, x1, HEAP, lsl #32
    // 0xb5b524: LoadField: r0 = r1->field_b
    //     0xb5b524: ldur            w0, [x1, #0xb]
    // 0xb5b528: DecompressPointer r0
    //     0xb5b528: add             x0, x0, HEAP, lsl #32
    // 0xb5b52c: r16 = Instance_CustomisationType
    //     0xb5b52c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xb5b530: ldr             x16, [x16, #0x680]
    // 0xb5b534: cmp             w0, w16
    // 0xb5b538: b.ne            #0xb5b618
    // 0xb5b53c: cmp             w4, NULL
    // 0xb5b540: b.ne            #0xb5b54c
    // 0xb5b544: r0 = Null
    //     0xb5b544: mov             x0, NULL
    // 0xb5b548: b               #0xb5b594
    // 0xb5b54c: LoadField: r5 = r4->field_f
    //     0xb5b54c: ldur            w5, [x4, #0xf]
    // 0xb5b550: DecompressPointer r5
    //     0xb5b550: add             x5, x5, HEAP, lsl #32
    // 0xb5b554: cmp             w5, NULL
    // 0xb5b558: b.ne            #0xb5b564
    // 0xb5b55c: r0 = Null
    //     0xb5b55c: mov             x0, NULL
    // 0xb5b560: b               #0xb5b594
    // 0xb5b564: LoadField: r0 = r5->field_b
    //     0xb5b564: ldur            w0, [x5, #0xb]
    // 0xb5b568: r1 = LoadInt32Instr(r0)
    //     0xb5b568: sbfx            x1, x0, #1, #0x1f
    // 0xb5b56c: mov             x0, x1
    // 0xb5b570: mov             x1, x7
    // 0xb5b574: cmp             x1, x0
    // 0xb5b578: b.hs            #0xb5bbcc
    // 0xb5b57c: LoadField: r0 = r5->field_f
    //     0xb5b57c: ldur            w0, [x5, #0xf]
    // 0xb5b580: DecompressPointer r0
    //     0xb5b580: add             x0, x0, HEAP, lsl #32
    // 0xb5b584: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb5b584: add             x16, x0, x7, lsl #2
    //     0xb5b588: ldur            w1, [x16, #0xf]
    // 0xb5b58c: DecompressPointer r1
    //     0xb5b58c: add             x1, x1, HEAP, lsl #32
    // 0xb5b590: mov             x0, x1
    // 0xb5b594: stur            x0, [fp, #-0x20]
    // 0xb5b598: LoadField: r1 = r3->field_f
    //     0xb5b598: ldur            w1, [x3, #0xf]
    // 0xb5b59c: DecompressPointer r1
    //     0xb5b59c: add             x1, x1, HEAP, lsl #32
    // 0xb5b5a0: stur            x1, [fp, #-0x18]
    // 0xb5b5a4: r0 = CustomisationText()
    //     0xb5b5a4: bl              #0xb5bc24  ; AllocateCustomisationTextStub -> CustomisationText (size=0x20)
    // 0xb5b5a8: mov             x3, x0
    // 0xb5b5ac: ldur            x0, [fp, #-0x20]
    // 0xb5b5b0: stur            x3, [fp, #-0x28]
    // 0xb5b5b4: StoreField: r3->field_b = r0
    //     0xb5b5b4: stur            w0, [x3, #0xb]
    // 0xb5b5b8: ldur            x0, [fp, #-0x18]
    // 0xb5b5bc: StoreField: r3->field_f = r0
    //     0xb5b5bc: stur            w0, [x3, #0xf]
    // 0xb5b5c0: ldur            x2, [fp, #-8]
    // 0xb5b5c4: r1 = Function '<anonymous closure>':.
    //     0xb5b5c4: add             x1, PP, #0x56, lsl #12  ; [pp+0x560a8] AnonymousClosure: (0xb5c304), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b5c8: ldr             x1, [x1, #0xa8]
    // 0xb5b5cc: r0 = AllocateClosure()
    //     0xb5b5cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b5d0: mov             x1, x0
    // 0xb5b5d4: ldur            x0, [fp, #-0x28]
    // 0xb5b5d8: StoreField: r0->field_13 = r1
    //     0xb5b5d8: stur            w1, [x0, #0x13]
    // 0xb5b5dc: ldur            x2, [fp, #-8]
    // 0xb5b5e0: r1 = Function '<anonymous closure>':.
    //     0xb5b5e0: add             x1, PP, #0x56, lsl #12  ; [pp+0x560b0] AnonymousClosure: (0xb5c274), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b5e4: ldr             x1, [x1, #0xb0]
    // 0xb5b5e8: r0 = AllocateClosure()
    //     0xb5b5e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b5ec: mov             x1, x0
    // 0xb5b5f0: ldur            x0, [fp, #-0x28]
    // 0xb5b5f4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5b5f4: stur            w1, [x0, #0x17]
    // 0xb5b5f8: ldur            x2, [fp, #-8]
    // 0xb5b5fc: r1 = Function '<anonymous closure>':.
    //     0xb5b5fc: add             x1, PP, #0x56, lsl #12  ; [pp+0x560b8] AnonymousClosure: (0xb5c1ec), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b600: ldr             x1, [x1, #0xb8]
    // 0xb5b604: r0 = AllocateClosure()
    //     0xb5b604: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b608: mov             x1, x0
    // 0xb5b60c: ldur            x0, [fp, #-0x28]
    // 0xb5b610: StoreField: r0->field_1b = r1
    //     0xb5b610: stur            w1, [x0, #0x1b]
    // 0xb5b614: b               #0xb5bbb0
    // 0xb5b618: cmp             w4, NULL
    // 0xb5b61c: b.eq            #0xb5b760
    // 0xb5b620: LoadField: r2 = r4->field_f
    //     0xb5b620: ldur            w2, [x4, #0xf]
    // 0xb5b624: DecompressPointer r2
    //     0xb5b624: add             x2, x2, HEAP, lsl #32
    // 0xb5b628: cmp             w2, NULL
    // 0xb5b62c: b.eq            #0xb5b760
    // 0xb5b630: LoadField: r0 = r2->field_b
    //     0xb5b630: ldur            w0, [x2, #0xb]
    // 0xb5b634: r5 = LoadInt32Instr(r6)
    //     0xb5b634: sbfx            x5, x6, #1, #0x1f
    //     0xb5b638: tbz             w6, #0, #0xb5b640
    //     0xb5b63c: ldur            x5, [x6, #7]
    // 0xb5b640: r1 = LoadInt32Instr(r0)
    //     0xb5b640: sbfx            x1, x0, #1, #0x1f
    // 0xb5b644: mov             x0, x1
    // 0xb5b648: mov             x1, x5
    // 0xb5b64c: cmp             x1, x0
    // 0xb5b650: b.hs            #0xb5bbd0
    // 0xb5b654: LoadField: r0 = r2->field_f
    //     0xb5b654: ldur            w0, [x2, #0xf]
    // 0xb5b658: DecompressPointer r0
    //     0xb5b658: add             x0, x0, HEAP, lsl #32
    // 0xb5b65c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b65c: add             x16, x0, x5, lsl #2
    //     0xb5b660: ldur            w1, [x16, #0xf]
    // 0xb5b664: DecompressPointer r1
    //     0xb5b664: add             x1, x1, HEAP, lsl #32
    // 0xb5b668: LoadField: r0 = r1->field_b
    //     0xb5b668: ldur            w0, [x1, #0xb]
    // 0xb5b66c: DecompressPointer r0
    //     0xb5b66c: add             x0, x0, HEAP, lsl #32
    // 0xb5b670: r16 = Instance_CustomisationType
    //     0xb5b670: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xb5b674: ldr             x16, [x16, #0x690]
    // 0xb5b678: cmp             w0, w16
    // 0xb5b67c: b.ne            #0xb5b760
    // 0xb5b680: cmp             w4, NULL
    // 0xb5b684: b.ne            #0xb5b690
    // 0xb5b688: r0 = Null
    //     0xb5b688: mov             x0, NULL
    // 0xb5b68c: b               #0xb5b6d8
    // 0xb5b690: LoadField: r2 = r4->field_f
    //     0xb5b690: ldur            w2, [x4, #0xf]
    // 0xb5b694: DecompressPointer r2
    //     0xb5b694: add             x2, x2, HEAP, lsl #32
    // 0xb5b698: cmp             w2, NULL
    // 0xb5b69c: b.ne            #0xb5b6a8
    // 0xb5b6a0: r0 = Null
    //     0xb5b6a0: mov             x0, NULL
    // 0xb5b6a4: b               #0xb5b6d8
    // 0xb5b6a8: LoadField: r0 = r2->field_b
    //     0xb5b6a8: ldur            w0, [x2, #0xb]
    // 0xb5b6ac: r1 = LoadInt32Instr(r0)
    //     0xb5b6ac: sbfx            x1, x0, #1, #0x1f
    // 0xb5b6b0: mov             x0, x1
    // 0xb5b6b4: mov             x1, x5
    // 0xb5b6b8: cmp             x1, x0
    // 0xb5b6bc: b.hs            #0xb5bbd4
    // 0xb5b6c0: LoadField: r0 = r2->field_f
    //     0xb5b6c0: ldur            w0, [x2, #0xf]
    // 0xb5b6c4: DecompressPointer r0
    //     0xb5b6c4: add             x0, x0, HEAP, lsl #32
    // 0xb5b6c8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b6c8: add             x16, x0, x5, lsl #2
    //     0xb5b6cc: ldur            w1, [x16, #0xf]
    // 0xb5b6d0: DecompressPointer r1
    //     0xb5b6d0: add             x1, x1, HEAP, lsl #32
    // 0xb5b6d4: mov             x0, x1
    // 0xb5b6d8: stur            x0, [fp, #-0x20]
    // 0xb5b6dc: LoadField: r1 = r3->field_f
    //     0xb5b6dc: ldur            w1, [x3, #0xf]
    // 0xb5b6e0: DecompressPointer r1
    //     0xb5b6e0: add             x1, x1, HEAP, lsl #32
    // 0xb5b6e4: stur            x1, [fp, #-0x18]
    // 0xb5b6e8: r0 = CustomisationNumber()
    //     0xb5b6e8: bl              #0xb5bc18  ; AllocateCustomisationNumberStub -> CustomisationNumber (size=0x20)
    // 0xb5b6ec: mov             x3, x0
    // 0xb5b6f0: ldur            x0, [fp, #-0x20]
    // 0xb5b6f4: stur            x3, [fp, #-0x28]
    // 0xb5b6f8: StoreField: r3->field_b = r0
    //     0xb5b6f8: stur            w0, [x3, #0xb]
    // 0xb5b6fc: ldur            x0, [fp, #-0x18]
    // 0xb5b700: StoreField: r3->field_f = r0
    //     0xb5b700: stur            w0, [x3, #0xf]
    // 0xb5b704: ldur            x2, [fp, #-8]
    // 0xb5b708: r1 = Function '<anonymous closure>':.
    //     0xb5b708: add             x1, PP, #0x56, lsl #12  ; [pp+0x560c0] AnonymousClosure: (0xb5c168), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b70c: ldr             x1, [x1, #0xc0]
    // 0xb5b710: r0 = AllocateClosure()
    //     0xb5b710: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b714: mov             x1, x0
    // 0xb5b718: ldur            x0, [fp, #-0x28]
    // 0xb5b71c: StoreField: r0->field_13 = r1
    //     0xb5b71c: stur            w1, [x0, #0x13]
    // 0xb5b720: ldur            x2, [fp, #-8]
    // 0xb5b724: r1 = Function '<anonymous closure>':.
    //     0xb5b724: add             x1, PP, #0x56, lsl #12  ; [pp+0x560c8] AnonymousClosure: (0xb5c0d8), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b728: ldr             x1, [x1, #0xc8]
    // 0xb5b72c: r0 = AllocateClosure()
    //     0xb5b72c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b730: mov             x1, x0
    // 0xb5b734: ldur            x0, [fp, #-0x28]
    // 0xb5b738: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5b738: stur            w1, [x0, #0x17]
    // 0xb5b73c: ldur            x2, [fp, #-8]
    // 0xb5b740: r1 = Function '<anonymous closure>':.
    //     0xb5b740: add             x1, PP, #0x56, lsl #12  ; [pp+0x560d0] AnonymousClosure: (0xb5c050), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b744: ldr             x1, [x1, #0xd0]
    // 0xb5b748: r0 = AllocateClosure()
    //     0xb5b748: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b74c: mov             x1, x0
    // 0xb5b750: ldur            x0, [fp, #-0x28]
    // 0xb5b754: StoreField: r0->field_1b = r1
    //     0xb5b754: stur            w1, [x0, #0x1b]
    // 0xb5b758: mov             x1, x0
    // 0xb5b75c: b               #0xb5bbac
    // 0xb5b760: cmp             w4, NULL
    // 0xb5b764: b.eq            #0xb5b93c
    // 0xb5b768: LoadField: r2 = r4->field_f
    //     0xb5b768: ldur            w2, [x4, #0xf]
    // 0xb5b76c: DecompressPointer r2
    //     0xb5b76c: add             x2, x2, HEAP, lsl #32
    // 0xb5b770: cmp             w2, NULL
    // 0xb5b774: b.eq            #0xb5b93c
    // 0xb5b778: LoadField: r0 = r2->field_b
    //     0xb5b778: ldur            w0, [x2, #0xb]
    // 0xb5b77c: r5 = LoadInt32Instr(r6)
    //     0xb5b77c: sbfx            x5, x6, #1, #0x1f
    //     0xb5b780: tbz             w6, #0, #0xb5b788
    //     0xb5b784: ldur            x5, [x6, #7]
    // 0xb5b788: r1 = LoadInt32Instr(r0)
    //     0xb5b788: sbfx            x1, x0, #1, #0x1f
    // 0xb5b78c: mov             x0, x1
    // 0xb5b790: mov             x1, x5
    // 0xb5b794: cmp             x1, x0
    // 0xb5b798: b.hs            #0xb5bbd8
    // 0xb5b79c: LoadField: r0 = r2->field_f
    //     0xb5b79c: ldur            w0, [x2, #0xf]
    // 0xb5b7a0: DecompressPointer r0
    //     0xb5b7a0: add             x0, x0, HEAP, lsl #32
    // 0xb5b7a4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b7a4: add             x16, x0, x5, lsl #2
    //     0xb5b7a8: ldur            w1, [x16, #0xf]
    // 0xb5b7ac: DecompressPointer r1
    //     0xb5b7ac: add             x1, x1, HEAP, lsl #32
    // 0xb5b7b0: LoadField: r0 = r1->field_b
    //     0xb5b7b0: ldur            w0, [x1, #0xb]
    // 0xb5b7b4: DecompressPointer r0
    //     0xb5b7b4: add             x0, x0, HEAP, lsl #32
    // 0xb5b7b8: r16 = Instance_CustomisationType
    //     0xb5b7b8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xb5b7bc: ldr             x16, [x16, #0x660]
    // 0xb5b7c0: cmp             w0, w16
    // 0xb5b7c4: b.ne            #0xb5b93c
    // 0xb5b7c8: cmp             w4, NULL
    // 0xb5b7cc: b.ne            #0xb5b7d8
    // 0xb5b7d0: r0 = Null
    //     0xb5b7d0: mov             x0, NULL
    // 0xb5b7d4: b               #0xb5b848
    // 0xb5b7d8: LoadField: r2 = r4->field_f
    //     0xb5b7d8: ldur            w2, [x4, #0xf]
    // 0xb5b7dc: DecompressPointer r2
    //     0xb5b7dc: add             x2, x2, HEAP, lsl #32
    // 0xb5b7e0: cmp             w2, NULL
    // 0xb5b7e4: b.ne            #0xb5b7f0
    // 0xb5b7e8: r0 = Null
    //     0xb5b7e8: mov             x0, NULL
    // 0xb5b7ec: b               #0xb5b848
    // 0xb5b7f0: LoadField: r0 = r2->field_b
    //     0xb5b7f0: ldur            w0, [x2, #0xb]
    // 0xb5b7f4: r1 = LoadInt32Instr(r0)
    //     0xb5b7f4: sbfx            x1, x0, #1, #0x1f
    // 0xb5b7f8: mov             x0, x1
    // 0xb5b7fc: mov             x1, x5
    // 0xb5b800: cmp             x1, x0
    // 0xb5b804: b.hs            #0xb5bbdc
    // 0xb5b808: LoadField: r0 = r2->field_f
    //     0xb5b808: ldur            w0, [x2, #0xf]
    // 0xb5b80c: DecompressPointer r0
    //     0xb5b80c: add             x0, x0, HEAP, lsl #32
    // 0xb5b810: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b810: add             x16, x0, x5, lsl #2
    //     0xb5b814: ldur            w1, [x16, #0xf]
    // 0xb5b818: DecompressPointer r1
    //     0xb5b818: add             x1, x1, HEAP, lsl #32
    // 0xb5b81c: LoadField: r0 = r1->field_2f
    //     0xb5b81c: ldur            w0, [x1, #0x2f]
    // 0xb5b820: DecompressPointer r0
    //     0xb5b820: add             x0, x0, HEAP, lsl #32
    // 0xb5b824: cmp             w0, NULL
    // 0xb5b828: b.ne            #0xb5b834
    // 0xb5b82c: r0 = Null
    //     0xb5b82c: mov             x0, NULL
    // 0xb5b830: b               #0xb5b848
    // 0xb5b834: LoadField: r1 = r0->field_b
    //     0xb5b834: ldur            w1, [x0, #0xb]
    // 0xb5b838: cbnz            w1, #0xb5b844
    // 0xb5b83c: r0 = false
    //     0xb5b83c: add             x0, NULL, #0x30  ; false
    // 0xb5b840: b               #0xb5b848
    // 0xb5b844: r0 = true
    //     0xb5b844: add             x0, NULL, #0x20  ; true
    // 0xb5b848: cmp             w0, NULL
    // 0xb5b84c: b.eq            #0xb5b91c
    // 0xb5b850: tbnz            w0, #4, #0xb5b91c
    // 0xb5b854: cmp             w4, NULL
    // 0xb5b858: b.ne            #0xb5b864
    // 0xb5b85c: r0 = Null
    //     0xb5b85c: mov             x0, NULL
    // 0xb5b860: b               #0xb5b8ac
    // 0xb5b864: LoadField: r2 = r4->field_f
    //     0xb5b864: ldur            w2, [x4, #0xf]
    // 0xb5b868: DecompressPointer r2
    //     0xb5b868: add             x2, x2, HEAP, lsl #32
    // 0xb5b86c: cmp             w2, NULL
    // 0xb5b870: b.ne            #0xb5b87c
    // 0xb5b874: r0 = Null
    //     0xb5b874: mov             x0, NULL
    // 0xb5b878: b               #0xb5b8ac
    // 0xb5b87c: LoadField: r0 = r2->field_b
    //     0xb5b87c: ldur            w0, [x2, #0xb]
    // 0xb5b880: r1 = LoadInt32Instr(r0)
    //     0xb5b880: sbfx            x1, x0, #1, #0x1f
    // 0xb5b884: mov             x0, x1
    // 0xb5b888: mov             x1, x5
    // 0xb5b88c: cmp             x1, x0
    // 0xb5b890: b.hs            #0xb5bbe0
    // 0xb5b894: LoadField: r0 = r2->field_f
    //     0xb5b894: ldur            w0, [x2, #0xf]
    // 0xb5b898: DecompressPointer r0
    //     0xb5b898: add             x0, x0, HEAP, lsl #32
    // 0xb5b89c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b89c: add             x16, x0, x5, lsl #2
    //     0xb5b8a0: ldur            w1, [x16, #0xf]
    // 0xb5b8a4: DecompressPointer r1
    //     0xb5b8a4: add             x1, x1, HEAP, lsl #32
    // 0xb5b8a8: mov             x0, x1
    // 0xb5b8ac: stur            x0, [fp, #-0x18]
    // 0xb5b8b0: r0 = CustomizationMultiSelect()
    //     0xb5b8b0: bl              #0xb5bc0c  ; AllocateCustomizationMultiSelectStub -> CustomizationMultiSelect (size=0x1c)
    // 0xb5b8b4: mov             x3, x0
    // 0xb5b8b8: ldur            x0, [fp, #-0x18]
    // 0xb5b8bc: stur            x3, [fp, #-0x20]
    // 0xb5b8c0: StoreField: r3->field_b = r0
    //     0xb5b8c0: stur            w0, [x3, #0xb]
    // 0xb5b8c4: ldur            x2, [fp, #-8]
    // 0xb5b8c8: r1 = Function '<anonymous closure>':.
    //     0xb5b8c8: add             x1, PP, #0x56, lsl #12  ; [pp+0x560d8] AnonymousClosure: (0xb5bfd4), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b8cc: ldr             x1, [x1, #0xd8]
    // 0xb5b8d0: r0 = AllocateClosure()
    //     0xb5b8d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b8d4: mov             x1, x0
    // 0xb5b8d8: ldur            x0, [fp, #-0x20]
    // 0xb5b8dc: StoreField: r0->field_f = r1
    //     0xb5b8dc: stur            w1, [x0, #0xf]
    // 0xb5b8e0: ldur            x2, [fp, #-8]
    // 0xb5b8e4: r1 = Function '<anonymous closure>':.
    //     0xb5b8e4: add             x1, PP, #0x56, lsl #12  ; [pp+0x560e0] AnonymousClosure: (0xb5bf50), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b8e8: ldr             x1, [x1, #0xe0]
    // 0xb5b8ec: r0 = AllocateClosure()
    //     0xb5b8ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b8f0: mov             x1, x0
    // 0xb5b8f4: ldur            x0, [fp, #-0x20]
    // 0xb5b8f8: StoreField: r0->field_13 = r1
    //     0xb5b8f8: stur            w1, [x0, #0x13]
    // 0xb5b8fc: ldur            x2, [fp, #-8]
    // 0xb5b900: r1 = Function '<anonymous closure>':.
    //     0xb5b900: add             x1, PP, #0x56, lsl #12  ; [pp+0x560e8] AnonymousClosure: (0xb5bec0), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5b904: ldr             x1, [x1, #0xe8]
    // 0xb5b908: r0 = AllocateClosure()
    //     0xb5b908: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5b90c: mov             x1, x0
    // 0xb5b910: ldur            x0, [fp, #-0x20]
    // 0xb5b914: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5b914: stur            w1, [x0, #0x17]
    // 0xb5b918: b               #0xb5b934
    // 0xb5b91c: r0 = Container()
    //     0xb5b91c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5b920: mov             x1, x0
    // 0xb5b924: stur            x0, [fp, #-0x18]
    // 0xb5b928: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5b928: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5b92c: r0 = Container()
    //     0xb5b92c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5b930: ldur            x0, [fp, #-0x18]
    // 0xb5b934: mov             x1, x0
    // 0xb5b938: b               #0xb5bbac
    // 0xb5b93c: cmp             w4, NULL
    // 0xb5b940: b.eq            #0xb5ba84
    // 0xb5b944: LoadField: r2 = r4->field_f
    //     0xb5b944: ldur            w2, [x4, #0xf]
    // 0xb5b948: DecompressPointer r2
    //     0xb5b948: add             x2, x2, HEAP, lsl #32
    // 0xb5b94c: cmp             w2, NULL
    // 0xb5b950: b.eq            #0xb5ba84
    // 0xb5b954: LoadField: r0 = r2->field_b
    //     0xb5b954: ldur            w0, [x2, #0xb]
    // 0xb5b958: r5 = LoadInt32Instr(r6)
    //     0xb5b958: sbfx            x5, x6, #1, #0x1f
    //     0xb5b95c: tbz             w6, #0, #0xb5b964
    //     0xb5b960: ldur            x5, [x6, #7]
    // 0xb5b964: r1 = LoadInt32Instr(r0)
    //     0xb5b964: sbfx            x1, x0, #1, #0x1f
    // 0xb5b968: mov             x0, x1
    // 0xb5b96c: mov             x1, x5
    // 0xb5b970: cmp             x1, x0
    // 0xb5b974: b.hs            #0xb5bbe4
    // 0xb5b978: LoadField: r0 = r2->field_f
    //     0xb5b978: ldur            w0, [x2, #0xf]
    // 0xb5b97c: DecompressPointer r0
    //     0xb5b97c: add             x0, x0, HEAP, lsl #32
    // 0xb5b980: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b980: add             x16, x0, x5, lsl #2
    //     0xb5b984: ldur            w1, [x16, #0xf]
    // 0xb5b988: DecompressPointer r1
    //     0xb5b988: add             x1, x1, HEAP, lsl #32
    // 0xb5b98c: LoadField: r0 = r1->field_b
    //     0xb5b98c: ldur            w0, [x1, #0xb]
    // 0xb5b990: DecompressPointer r0
    //     0xb5b990: add             x0, x0, HEAP, lsl #32
    // 0xb5b994: r16 = Instance_CustomisationType
    //     0xb5b994: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xb5b998: ldr             x16, [x16, #0x670]
    // 0xb5b99c: cmp             w0, w16
    // 0xb5b9a0: b.ne            #0xb5ba84
    // 0xb5b9a4: cmp             w4, NULL
    // 0xb5b9a8: b.ne            #0xb5b9b4
    // 0xb5b9ac: r0 = Null
    //     0xb5b9ac: mov             x0, NULL
    // 0xb5b9b0: b               #0xb5b9fc
    // 0xb5b9b4: LoadField: r2 = r4->field_f
    //     0xb5b9b4: ldur            w2, [x4, #0xf]
    // 0xb5b9b8: DecompressPointer r2
    //     0xb5b9b8: add             x2, x2, HEAP, lsl #32
    // 0xb5b9bc: cmp             w2, NULL
    // 0xb5b9c0: b.ne            #0xb5b9cc
    // 0xb5b9c4: r0 = Null
    //     0xb5b9c4: mov             x0, NULL
    // 0xb5b9c8: b               #0xb5b9fc
    // 0xb5b9cc: LoadField: r0 = r2->field_b
    //     0xb5b9cc: ldur            w0, [x2, #0xb]
    // 0xb5b9d0: r1 = LoadInt32Instr(r0)
    //     0xb5b9d0: sbfx            x1, x0, #1, #0x1f
    // 0xb5b9d4: mov             x0, x1
    // 0xb5b9d8: mov             x1, x5
    // 0xb5b9dc: cmp             x1, x0
    // 0xb5b9e0: b.hs            #0xb5bbe8
    // 0xb5b9e4: LoadField: r0 = r2->field_f
    //     0xb5b9e4: ldur            w0, [x2, #0xf]
    // 0xb5b9e8: DecompressPointer r0
    //     0xb5b9e8: add             x0, x0, HEAP, lsl #32
    // 0xb5b9ec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5b9ec: add             x16, x0, x5, lsl #2
    //     0xb5b9f0: ldur            w1, [x16, #0xf]
    // 0xb5b9f4: DecompressPointer r1
    //     0xb5b9f4: add             x1, x1, HEAP, lsl #32
    // 0xb5b9f8: mov             x0, x1
    // 0xb5b9fc: stur            x0, [fp, #-0x20]
    // 0xb5ba00: LoadField: r1 = r3->field_f
    //     0xb5ba00: ldur            w1, [x3, #0xf]
    // 0xb5ba04: DecompressPointer r1
    //     0xb5ba04: add             x1, x1, HEAP, lsl #32
    // 0xb5ba08: stur            x1, [fp, #-0x18]
    // 0xb5ba0c: r0 = CustomizationSingleSelect()
    //     0xb5ba0c: bl              #0xb5bc00  ; AllocateCustomizationSingleSelectStub -> CustomizationSingleSelect (size=0x20)
    // 0xb5ba10: mov             x3, x0
    // 0xb5ba14: ldur            x0, [fp, #-0x20]
    // 0xb5ba18: stur            x3, [fp, #-0x28]
    // 0xb5ba1c: StoreField: r3->field_b = r0
    //     0xb5ba1c: stur            w0, [x3, #0xb]
    // 0xb5ba20: ldur            x0, [fp, #-0x18]
    // 0xb5ba24: StoreField: r3->field_f = r0
    //     0xb5ba24: stur            w0, [x3, #0xf]
    // 0xb5ba28: ldur            x2, [fp, #-8]
    // 0xb5ba2c: r1 = Function '<anonymous closure>':.
    //     0xb5ba2c: add             x1, PP, #0x56, lsl #12  ; [pp+0x560f0] AnonymousClosure: (0xb5be3c), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5ba30: ldr             x1, [x1, #0xf0]
    // 0xb5ba34: r0 = AllocateClosure()
    //     0xb5ba34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ba38: mov             x1, x0
    // 0xb5ba3c: ldur            x0, [fp, #-0x28]
    // 0xb5ba40: StoreField: r0->field_13 = r1
    //     0xb5ba40: stur            w1, [x0, #0x13]
    // 0xb5ba44: ldur            x2, [fp, #-8]
    // 0xb5ba48: r1 = Function '<anonymous closure>':.
    //     0xb5ba48: add             x1, PP, #0x56, lsl #12  ; [pp+0x560f8] AnonymousClosure: (0xb5bdc0), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5ba4c: ldr             x1, [x1, #0xf8]
    // 0xb5ba50: r0 = AllocateClosure()
    //     0xb5ba50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ba54: mov             x1, x0
    // 0xb5ba58: ldur            x0, [fp, #-0x28]
    // 0xb5ba5c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5ba5c: stur            w1, [x0, #0x17]
    // 0xb5ba60: ldur            x2, [fp, #-8]
    // 0xb5ba64: r1 = Function '<anonymous closure>':.
    //     0xb5ba64: add             x1, PP, #0x56, lsl #12  ; [pp+0x56100] AnonymousClosure: (0xb5bd38), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5ba68: ldr             x1, [x1, #0x100]
    // 0xb5ba6c: r0 = AllocateClosure()
    //     0xb5ba6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ba70: mov             x1, x0
    // 0xb5ba74: ldur            x0, [fp, #-0x28]
    // 0xb5ba78: StoreField: r0->field_1b = r1
    //     0xb5ba78: stur            w1, [x0, #0x1b]
    // 0xb5ba7c: mov             x1, x0
    // 0xb5ba80: b               #0xb5bbac
    // 0xb5ba84: cmp             w4, NULL
    // 0xb5ba88: b.eq            #0xb5bba8
    // 0xb5ba8c: LoadField: r2 = r4->field_f
    //     0xb5ba8c: ldur            w2, [x4, #0xf]
    // 0xb5ba90: DecompressPointer r2
    //     0xb5ba90: add             x2, x2, HEAP, lsl #32
    // 0xb5ba94: cmp             w2, NULL
    // 0xb5ba98: b.eq            #0xb5bba8
    // 0xb5ba9c: LoadField: r0 = r2->field_b
    //     0xb5ba9c: ldur            w0, [x2, #0xb]
    // 0xb5baa0: r5 = LoadInt32Instr(r6)
    //     0xb5baa0: sbfx            x5, x6, #1, #0x1f
    //     0xb5baa4: tbz             w6, #0, #0xb5baac
    //     0xb5baa8: ldur            x5, [x6, #7]
    // 0xb5baac: r1 = LoadInt32Instr(r0)
    //     0xb5baac: sbfx            x1, x0, #1, #0x1f
    // 0xb5bab0: mov             x0, x1
    // 0xb5bab4: mov             x1, x5
    // 0xb5bab8: cmp             x1, x0
    // 0xb5babc: b.hs            #0xb5bbec
    // 0xb5bac0: LoadField: r0 = r2->field_f
    //     0xb5bac0: ldur            w0, [x2, #0xf]
    // 0xb5bac4: DecompressPointer r0
    //     0xb5bac4: add             x0, x0, HEAP, lsl #32
    // 0xb5bac8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5bac8: add             x16, x0, x5, lsl #2
    //     0xb5bacc: ldur            w1, [x16, #0xf]
    // 0xb5bad0: DecompressPointer r1
    //     0xb5bad0: add             x1, x1, HEAP, lsl #32
    // 0xb5bad4: LoadField: r0 = r1->field_b
    //     0xb5bad4: ldur            w0, [x1, #0xb]
    // 0xb5bad8: DecompressPointer r0
    //     0xb5bad8: add             x0, x0, HEAP, lsl #32
    // 0xb5badc: r16 = Instance_CustomisationType
    //     0xb5badc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xb5bae0: ldr             x16, [x16, #0x650]
    // 0xb5bae4: cmp             w0, w16
    // 0xb5bae8: b.ne            #0xb5bba8
    // 0xb5baec: cmp             w4, NULL
    // 0xb5baf0: b.ne            #0xb5bafc
    // 0xb5baf4: r0 = Null
    //     0xb5baf4: mov             x0, NULL
    // 0xb5baf8: b               #0xb5bb44
    // 0xb5bafc: LoadField: r2 = r4->field_f
    //     0xb5bafc: ldur            w2, [x4, #0xf]
    // 0xb5bb00: DecompressPointer r2
    //     0xb5bb00: add             x2, x2, HEAP, lsl #32
    // 0xb5bb04: cmp             w2, NULL
    // 0xb5bb08: b.ne            #0xb5bb14
    // 0xb5bb0c: r0 = Null
    //     0xb5bb0c: mov             x0, NULL
    // 0xb5bb10: b               #0xb5bb44
    // 0xb5bb14: LoadField: r0 = r2->field_b
    //     0xb5bb14: ldur            w0, [x2, #0xb]
    // 0xb5bb18: r1 = LoadInt32Instr(r0)
    //     0xb5bb18: sbfx            x1, x0, #1, #0x1f
    // 0xb5bb1c: mov             x0, x1
    // 0xb5bb20: mov             x1, x5
    // 0xb5bb24: cmp             x1, x0
    // 0xb5bb28: b.hs            #0xb5bbf0
    // 0xb5bb2c: LoadField: r0 = r2->field_f
    //     0xb5bb2c: ldur            w0, [x2, #0xf]
    // 0xb5bb30: DecompressPointer r0
    //     0xb5bb30: add             x0, x0, HEAP, lsl #32
    // 0xb5bb34: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5bb34: add             x16, x0, x5, lsl #2
    //     0xb5bb38: ldur            w1, [x16, #0xf]
    // 0xb5bb3c: DecompressPointer r1
    //     0xb5bb3c: add             x1, x1, HEAP, lsl #32
    // 0xb5bb40: mov             x0, x1
    // 0xb5bb44: stur            x0, [fp, #-0x20]
    // 0xb5bb48: LoadField: r1 = r3->field_f
    //     0xb5bb48: ldur            w1, [x3, #0xf]
    // 0xb5bb4c: DecompressPointer r1
    //     0xb5bb4c: add             x1, x1, HEAP, lsl #32
    // 0xb5bb50: stur            x1, [fp, #-0x18]
    // 0xb5bb54: r0 = CameraPicker()
    //     0xb5bb54: bl              #0xb5bbf4  ; AllocateCameraPickerStub -> CameraPicker (size=0x1c)
    // 0xb5bb58: mov             x3, x0
    // 0xb5bb5c: ldur            x0, [fp, #-0x20]
    // 0xb5bb60: stur            x3, [fp, #-0x28]
    // 0xb5bb64: StoreField: r3->field_b = r0
    //     0xb5bb64: stur            w0, [x3, #0xb]
    // 0xb5bb68: ldur            x0, [fp, #-0x18]
    // 0xb5bb6c: StoreField: r3->field_f = r0
    //     0xb5bb6c: stur            w0, [x3, #0xf]
    // 0xb5bb70: ldur            x2, [fp, #-8]
    // 0xb5bb74: r1 = Function '<anonymous closure>':.
    //     0xb5bb74: add             x1, PP, #0x56, lsl #12  ; [pp+0x56108] AnonymousClosure: (0xb5bcb4), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5bb78: ldr             x1, [x1, #0x108]
    // 0xb5bb7c: r0 = AllocateClosure()
    //     0xb5bb7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5bb80: mov             x1, x0
    // 0xb5bb84: ldur            x0, [fp, #-0x28]
    // 0xb5bb88: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5bb88: stur            w1, [x0, #0x17]
    // 0xb5bb8c: ldur            x2, [fp, #-8]
    // 0xb5bb90: r1 = Function '<anonymous closure>':.
    //     0xb5bb90: add             x1, PP, #0x56, lsl #12  ; [pp+0x56110] AnonymousClosure: (0xb5bc30), in [package:customer_app/app/presentation/views/glass/customization/customized_view.dart] _CustomizedViewState::build (0xb5ae3c)
    //     0xb5bb94: ldr             x1, [x1, #0x110]
    // 0xb5bb98: r0 = AllocateClosure()
    //     0xb5bb98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5bb9c: ldur            x1, [fp, #-0x28]
    // 0xb5bba0: StoreField: r1->field_13 = r0
    //     0xb5bba0: stur            w0, [x1, #0x13]
    // 0xb5bba4: b               #0xb5bbac
    // 0xb5bba8: ldur            x1, [fp, #-0x10]
    // 0xb5bbac: mov             x0, x1
    // 0xb5bbb0: LeaveFrame
    //     0xb5bbb0: mov             SP, fp
    //     0xb5bbb4: ldp             fp, lr, [SP], #0x10
    // 0xb5bbb8: ret
    //     0xb5bbb8: ret             
    // 0xb5bbbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5bbbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5bbc0: b               #0xb5b480
    // 0xb5bbc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bbc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5bbc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbc8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbd8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbdc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbe0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbe4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbe4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbe8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbe8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5bbf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5bbf0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>) {
    // ** addr: 0xb5bc30, size: 0x84
    // 0xb5bc30: EnterFrame
    //     0xb5bc30: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bc34: mov             fp, SP
    // 0xb5bc38: AllocStack(0x18)
    //     0xb5bc38: sub             SP, SP, #0x18
    // 0xb5bc3c: SetupParameters()
    //     0xb5bc3c: ldr             x0, [fp, #0x20]
    //     0xb5bc40: ldur            w1, [x0, #0x17]
    //     0xb5bc44: add             x1, x1, HEAP, lsl #32
    // 0xb5bc48: CheckStackOverflow
    //     0xb5bc48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bc4c: cmp             SP, x16
    //     0xb5bc50: b.ls            #0xb5bca8
    // 0xb5bc54: LoadField: r0 = r1->field_f
    //     0xb5bc54: ldur            w0, [x1, #0xf]
    // 0xb5bc58: DecompressPointer r0
    //     0xb5bc58: add             x0, x0, HEAP, lsl #32
    // 0xb5bc5c: LoadField: r1 = r0->field_b
    //     0xb5bc5c: ldur            w1, [x0, #0xb]
    // 0xb5bc60: DecompressPointer r1
    //     0xb5bc60: add             x1, x1, HEAP, lsl #32
    // 0xb5bc64: cmp             w1, NULL
    // 0xb5bc68: b.eq            #0xb5bcb0
    // 0xb5bc6c: LoadField: r0 = r1->field_13
    //     0xb5bc6c: ldur            w0, [x1, #0x13]
    // 0xb5bc70: DecompressPointer r0
    //     0xb5bc70: add             x0, x0, HEAP, lsl #32
    // 0xb5bc74: ldr             x16, [fp, #0x18]
    // 0xb5bc78: stp             x16, x0, [SP, #8]
    // 0xb5bc7c: ldr             x16, [fp, #0x10]
    // 0xb5bc80: str             x16, [SP]
    // 0xb5bc84: r4 = 0
    //     0xb5bc84: movz            x4, #0
    // 0xb5bc88: ldr             x0, [SP, #0x10]
    // 0xb5bc8c: r5 = UnlinkedCall_0x613b5c
    //     0xb5bc8c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56118] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5bc90: ldp             x5, lr, [x16, #0x118]
    // 0xb5bc94: blr             lr
    // 0xb5bc98: r0 = Null
    //     0xb5bc98: mov             x0, NULL
    // 0xb5bc9c: LeaveFrame
    //     0xb5bc9c: mov             SP, fp
    //     0xb5bca0: ldp             fp, lr, [SP], #0x10
    // 0xb5bca4: ret
    //     0xb5bca4: ret             
    // 0xb5bca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5bca8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5bcac: b               #0xb5bc54
    // 0xb5bcb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bcb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0xb5bcb4, size: 0x84
    // 0xb5bcb4: EnterFrame
    //     0xb5bcb4: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bcb8: mov             fp, SP
    // 0xb5bcbc: AllocStack(0x18)
    //     0xb5bcbc: sub             SP, SP, #0x18
    // 0xb5bcc0: SetupParameters()
    //     0xb5bcc0: ldr             x0, [fp, #0x20]
    //     0xb5bcc4: ldur            w1, [x0, #0x17]
    //     0xb5bcc8: add             x1, x1, HEAP, lsl #32
    // 0xb5bccc: CheckStackOverflow
    //     0xb5bccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bcd0: cmp             SP, x16
    //     0xb5bcd4: b.ls            #0xb5bd2c
    // 0xb5bcd8: LoadField: r0 = r1->field_f
    //     0xb5bcd8: ldur            w0, [x1, #0xf]
    // 0xb5bcdc: DecompressPointer r0
    //     0xb5bcdc: add             x0, x0, HEAP, lsl #32
    // 0xb5bce0: LoadField: r1 = r0->field_b
    //     0xb5bce0: ldur            w1, [x0, #0xb]
    // 0xb5bce4: DecompressPointer r1
    //     0xb5bce4: add             x1, x1, HEAP, lsl #32
    // 0xb5bce8: cmp             w1, NULL
    // 0xb5bcec: b.eq            #0xb5bd34
    // 0xb5bcf0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5bcf0: ldur            w0, [x1, #0x17]
    // 0xb5bcf4: DecompressPointer r0
    //     0xb5bcf4: add             x0, x0, HEAP, lsl #32
    // 0xb5bcf8: ldr             x16, [fp, #0x18]
    // 0xb5bcfc: stp             x16, x0, [SP, #8]
    // 0xb5bd00: ldr             x16, [fp, #0x10]
    // 0xb5bd04: str             x16, [SP]
    // 0xb5bd08: r4 = 0
    //     0xb5bd08: movz            x4, #0
    // 0xb5bd0c: ldr             x0, [SP, #0x10]
    // 0xb5bd10: r5 = UnlinkedCall_0x613b5c
    //     0xb5bd10: add             x16, PP, #0x56, lsl #12  ; [pp+0x56128] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5bd14: ldp             x5, lr, [x16, #0x128]
    // 0xb5bd18: blr             lr
    // 0xb5bd1c: r0 = Null
    //     0xb5bd1c: mov             x0, NULL
    // 0xb5bd20: LeaveFrame
    //     0xb5bd20: mov             SP, fp
    //     0xb5bd24: ldp             fp, lr, [SP], #0x10
    // 0xb5bd28: ret
    //     0xb5bd28: ret             
    // 0xb5bd2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5bd2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5bd30: b               #0xb5bcd8
    // 0xb5bd34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bd34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, String, List<ProductCustomisation>) {
    // ** addr: 0xb5bd38, size: 0x88
    // 0xb5bd38: EnterFrame
    //     0xb5bd38: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bd3c: mov             fp, SP
    // 0xb5bd40: AllocStack(0x20)
    //     0xb5bd40: sub             SP, SP, #0x20
    // 0xb5bd44: SetupParameters()
    //     0xb5bd44: ldr             x0, [fp, #0x28]
    //     0xb5bd48: ldur            w1, [x0, #0x17]
    //     0xb5bd4c: add             x1, x1, HEAP, lsl #32
    // 0xb5bd50: CheckStackOverflow
    //     0xb5bd50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bd54: cmp             SP, x16
    //     0xb5bd58: b.ls            #0xb5bdb4
    // 0xb5bd5c: LoadField: r0 = r1->field_f
    //     0xb5bd5c: ldur            w0, [x1, #0xf]
    // 0xb5bd60: DecompressPointer r0
    //     0xb5bd60: add             x0, x0, HEAP, lsl #32
    // 0xb5bd64: LoadField: r1 = r0->field_b
    //     0xb5bd64: ldur            w1, [x0, #0xb]
    // 0xb5bd68: DecompressPointer r1
    //     0xb5bd68: add             x1, x1, HEAP, lsl #32
    // 0xb5bd6c: cmp             w1, NULL
    // 0xb5bd70: b.eq            #0xb5bdbc
    // 0xb5bd74: LoadField: r0 = r1->field_47
    //     0xb5bd74: ldur            w0, [x1, #0x47]
    // 0xb5bd78: DecompressPointer r0
    //     0xb5bd78: add             x0, x0, HEAP, lsl #32
    // 0xb5bd7c: ldr             x16, [fp, #0x20]
    // 0xb5bd80: stp             x16, x0, [SP, #0x10]
    // 0xb5bd84: ldr             x16, [fp, #0x18]
    // 0xb5bd88: ldr             lr, [fp, #0x10]
    // 0xb5bd8c: stp             lr, x16, [SP]
    // 0xb5bd90: r4 = 0
    //     0xb5bd90: movz            x4, #0
    // 0xb5bd94: ldr             x0, [SP, #0x18]
    // 0xb5bd98: r5 = UnlinkedCall_0x613b5c
    //     0xb5bd98: add             x16, PP, #0x56, lsl #12  ; [pp+0x56138] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5bd9c: ldp             x5, lr, [x16, #0x138]
    // 0xb5bda0: blr             lr
    // 0xb5bda4: r0 = Null
    //     0xb5bda4: mov             x0, NULL
    // 0xb5bda8: LeaveFrame
    //     0xb5bda8: mov             SP, fp
    //     0xb5bdac: ldp             fp, lr, [SP], #0x10
    // 0xb5bdb0: ret
    //     0xb5bdb0: ret             
    // 0xb5bdb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5bdb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5bdb8: b               #0xb5bd5c
    // 0xb5bdbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bdbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0xb5bdc0, size: 0x7c
    // 0xb5bdc0: EnterFrame
    //     0xb5bdc0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bdc4: mov             fp, SP
    // 0xb5bdc8: AllocStack(0x10)
    //     0xb5bdc8: sub             SP, SP, #0x10
    // 0xb5bdcc: SetupParameters()
    //     0xb5bdcc: ldr             x0, [fp, #0x18]
    //     0xb5bdd0: ldur            w1, [x0, #0x17]
    //     0xb5bdd4: add             x1, x1, HEAP, lsl #32
    // 0xb5bdd8: CheckStackOverflow
    //     0xb5bdd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bddc: cmp             SP, x16
    //     0xb5bde0: b.ls            #0xb5be30
    // 0xb5bde4: LoadField: r0 = r1->field_f
    //     0xb5bde4: ldur            w0, [x1, #0xf]
    // 0xb5bde8: DecompressPointer r0
    //     0xb5bde8: add             x0, x0, HEAP, lsl #32
    // 0xb5bdec: LoadField: r1 = r0->field_b
    //     0xb5bdec: ldur            w1, [x0, #0xb]
    // 0xb5bdf0: DecompressPointer r1
    //     0xb5bdf0: add             x1, x1, HEAP, lsl #32
    // 0xb5bdf4: cmp             w1, NULL
    // 0xb5bdf8: b.eq            #0xb5be38
    // 0xb5bdfc: LoadField: r0 = r1->field_43
    //     0xb5bdfc: ldur            w0, [x1, #0x43]
    // 0xb5be00: DecompressPointer r0
    //     0xb5be00: add             x0, x0, HEAP, lsl #32
    // 0xb5be04: ldr             x16, [fp, #0x10]
    // 0xb5be08: stp             x16, x0, [SP]
    // 0xb5be0c: r4 = 0
    //     0xb5be0c: movz            x4, #0
    // 0xb5be10: ldr             x0, [SP, #8]
    // 0xb5be14: r5 = UnlinkedCall_0x613b5c
    //     0xb5be14: add             x16, PP, #0x56, lsl #12  ; [pp+0x56148] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5be18: ldp             x5, lr, [x16, #0x148]
    // 0xb5be1c: blr             lr
    // 0xb5be20: r0 = Null
    //     0xb5be20: mov             x0, NULL
    // 0xb5be24: LeaveFrame
    //     0xb5be24: mov             SP, fp
    //     0xb5be28: ldp             fp, lr, [SP], #0x10
    // 0xb5be2c: ret
    //     0xb5be2c: ret             
    // 0xb5be30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5be30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5be34: b               #0xb5bde4
    // 0xb5be38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5be38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool, int) {
    // ** addr: 0xb5be3c, size: 0x84
    // 0xb5be3c: EnterFrame
    //     0xb5be3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5be40: mov             fp, SP
    // 0xb5be44: AllocStack(0x18)
    //     0xb5be44: sub             SP, SP, #0x18
    // 0xb5be48: SetupParameters()
    //     0xb5be48: ldr             x0, [fp, #0x20]
    //     0xb5be4c: ldur            w1, [x0, #0x17]
    //     0xb5be50: add             x1, x1, HEAP, lsl #32
    // 0xb5be54: CheckStackOverflow
    //     0xb5be54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5be58: cmp             SP, x16
    //     0xb5be5c: b.ls            #0xb5beb4
    // 0xb5be60: LoadField: r0 = r1->field_f
    //     0xb5be60: ldur            w0, [x1, #0xf]
    // 0xb5be64: DecompressPointer r0
    //     0xb5be64: add             x0, x0, HEAP, lsl #32
    // 0xb5be68: LoadField: r1 = r0->field_b
    //     0xb5be68: ldur            w1, [x0, #0xb]
    // 0xb5be6c: DecompressPointer r1
    //     0xb5be6c: add             x1, x1, HEAP, lsl #32
    // 0xb5be70: cmp             w1, NULL
    // 0xb5be74: b.eq            #0xb5bebc
    // 0xb5be78: LoadField: r0 = r1->field_3f
    //     0xb5be78: ldur            w0, [x1, #0x3f]
    // 0xb5be7c: DecompressPointer r0
    //     0xb5be7c: add             x0, x0, HEAP, lsl #32
    // 0xb5be80: ldr             x16, [fp, #0x18]
    // 0xb5be84: stp             x16, x0, [SP, #8]
    // 0xb5be88: ldr             x16, [fp, #0x10]
    // 0xb5be8c: str             x16, [SP]
    // 0xb5be90: r4 = 0
    //     0xb5be90: movz            x4, #0
    // 0xb5be94: ldr             x0, [SP, #0x10]
    // 0xb5be98: r5 = UnlinkedCall_0x613b5c
    //     0xb5be98: add             x16, PP, #0x56, lsl #12  ; [pp+0x56158] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5be9c: ldp             x5, lr, [x16, #0x158]
    // 0xb5bea0: blr             lr
    // 0xb5bea4: r0 = Null
    //     0xb5bea4: mov             x0, NULL
    // 0xb5bea8: LeaveFrame
    //     0xb5bea8: mov             SP, fp
    //     0xb5beac: ldp             fp, lr, [SP], #0x10
    // 0xb5beb0: ret
    //     0xb5beb0: ret             
    // 0xb5beb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5beb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5beb8: b               #0xb5be60
    // 0xb5bebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, bool, bool, CustomerResponse) {
    // ** addr: 0xb5bec0, size: 0x90
    // 0xb5bec0: EnterFrame
    //     0xb5bec0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bec4: mov             fp, SP
    // 0xb5bec8: AllocStack(0x28)
    //     0xb5bec8: sub             SP, SP, #0x28
    // 0xb5becc: SetupParameters()
    //     0xb5becc: ldr             x0, [fp, #0x30]
    //     0xb5bed0: ldur            w1, [x0, #0x17]
    //     0xb5bed4: add             x1, x1, HEAP, lsl #32
    // 0xb5bed8: CheckStackOverflow
    //     0xb5bed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bedc: cmp             SP, x16
    //     0xb5bee0: b.ls            #0xb5bf44
    // 0xb5bee4: LoadField: r0 = r1->field_f
    //     0xb5bee4: ldur            w0, [x1, #0xf]
    // 0xb5bee8: DecompressPointer r0
    //     0xb5bee8: add             x0, x0, HEAP, lsl #32
    // 0xb5beec: LoadField: r1 = r0->field_b
    //     0xb5beec: ldur            w1, [x0, #0xb]
    // 0xb5bef0: DecompressPointer r1
    //     0xb5bef0: add             x1, x1, HEAP, lsl #32
    // 0xb5bef4: cmp             w1, NULL
    // 0xb5bef8: b.eq            #0xb5bf4c
    // 0xb5befc: LoadField: r0 = r1->field_3b
    //     0xb5befc: ldur            w0, [x1, #0x3b]
    // 0xb5bf00: DecompressPointer r0
    //     0xb5bf00: add             x0, x0, HEAP, lsl #32
    // 0xb5bf04: ldr             x16, [fp, #0x28]
    // 0xb5bf08: stp             x16, x0, [SP, #0x18]
    // 0xb5bf0c: ldr             x16, [fp, #0x20]
    // 0xb5bf10: ldr             lr, [fp, #0x18]
    // 0xb5bf14: stp             lr, x16, [SP, #8]
    // 0xb5bf18: ldr             x16, [fp, #0x10]
    // 0xb5bf1c: str             x16, [SP]
    // 0xb5bf20: r4 = 0
    //     0xb5bf20: movz            x4, #0
    // 0xb5bf24: ldr             x0, [SP, #0x20]
    // 0xb5bf28: r5 = UnlinkedCall_0x613b5c
    //     0xb5bf28: add             x16, PP, #0x56, lsl #12  ; [pp+0x56168] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5bf2c: ldp             x5, lr, [x16, #0x168]
    // 0xb5bf30: blr             lr
    // 0xb5bf34: r0 = Null
    //     0xb5bf34: mov             x0, NULL
    // 0xb5bf38: LeaveFrame
    //     0xb5bf38: mov             SP, fp
    //     0xb5bf3c: ldp             fp, lr, [SP], #0x10
    // 0xb5bf40: ret
    //     0xb5bf40: ret             
    // 0xb5bf44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5bf44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5bf48: b               #0xb5bee4
    // 0xb5bf4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bf4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, CustomerResponse) {
    // ** addr: 0xb5bf50, size: 0x84
    // 0xb5bf50: EnterFrame
    //     0xb5bf50: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bf54: mov             fp, SP
    // 0xb5bf58: AllocStack(0x18)
    //     0xb5bf58: sub             SP, SP, #0x18
    // 0xb5bf5c: SetupParameters()
    //     0xb5bf5c: ldr             x0, [fp, #0x20]
    //     0xb5bf60: ldur            w1, [x0, #0x17]
    //     0xb5bf64: add             x1, x1, HEAP, lsl #32
    // 0xb5bf68: CheckStackOverflow
    //     0xb5bf68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bf6c: cmp             SP, x16
    //     0xb5bf70: b.ls            #0xb5bfc8
    // 0xb5bf74: LoadField: r0 = r1->field_f
    //     0xb5bf74: ldur            w0, [x1, #0xf]
    // 0xb5bf78: DecompressPointer r0
    //     0xb5bf78: add             x0, x0, HEAP, lsl #32
    // 0xb5bf7c: LoadField: r1 = r0->field_b
    //     0xb5bf7c: ldur            w1, [x0, #0xb]
    // 0xb5bf80: DecompressPointer r1
    //     0xb5bf80: add             x1, x1, HEAP, lsl #32
    // 0xb5bf84: cmp             w1, NULL
    // 0xb5bf88: b.eq            #0xb5bfd0
    // 0xb5bf8c: LoadField: r0 = r1->field_37
    //     0xb5bf8c: ldur            w0, [x1, #0x37]
    // 0xb5bf90: DecompressPointer r0
    //     0xb5bf90: add             x0, x0, HEAP, lsl #32
    // 0xb5bf94: ldr             x16, [fp, #0x18]
    // 0xb5bf98: stp             x16, x0, [SP, #8]
    // 0xb5bf9c: ldr             x16, [fp, #0x10]
    // 0xb5bfa0: str             x16, [SP]
    // 0xb5bfa4: r4 = 0
    //     0xb5bfa4: movz            x4, #0
    // 0xb5bfa8: ldr             x0, [SP, #0x10]
    // 0xb5bfac: r5 = UnlinkedCall_0x613b5c
    //     0xb5bfac: add             x16, PP, #0x56, lsl #12  ; [pp+0x56178] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5bfb0: ldp             x5, lr, [x16, #0x178]
    // 0xb5bfb4: blr             lr
    // 0xb5bfb8: r0 = Null
    //     0xb5bfb8: mov             x0, NULL
    // 0xb5bfbc: LeaveFrame
    //     0xb5bfbc: mov             SP, fp
    //     0xb5bfc0: ldp             fp, lr, [SP], #0x10
    // 0xb5bfc4: ret
    //     0xb5bfc4: ret             
    // 0xb5bfc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5bfc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5bfcc: b               #0xb5bf74
    // 0xb5bfd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5bfd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0xb5bfd4, size: 0x7c
    // 0xb5bfd4: EnterFrame
    //     0xb5bfd4: stp             fp, lr, [SP, #-0x10]!
    //     0xb5bfd8: mov             fp, SP
    // 0xb5bfdc: AllocStack(0x10)
    //     0xb5bfdc: sub             SP, SP, #0x10
    // 0xb5bfe0: SetupParameters()
    //     0xb5bfe0: ldr             x0, [fp, #0x18]
    //     0xb5bfe4: ldur            w1, [x0, #0x17]
    //     0xb5bfe8: add             x1, x1, HEAP, lsl #32
    // 0xb5bfec: CheckStackOverflow
    //     0xb5bfec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5bff0: cmp             SP, x16
    //     0xb5bff4: b.ls            #0xb5c044
    // 0xb5bff8: LoadField: r0 = r1->field_f
    //     0xb5bff8: ldur            w0, [x1, #0xf]
    // 0xb5bffc: DecompressPointer r0
    //     0xb5bffc: add             x0, x0, HEAP, lsl #32
    // 0xb5c000: LoadField: r1 = r0->field_b
    //     0xb5c000: ldur            w1, [x0, #0xb]
    // 0xb5c004: DecompressPointer r1
    //     0xb5c004: add             x1, x1, HEAP, lsl #32
    // 0xb5c008: cmp             w1, NULL
    // 0xb5c00c: b.eq            #0xb5c04c
    // 0xb5c010: LoadField: r0 = r1->field_33
    //     0xb5c010: ldur            w0, [x1, #0x33]
    // 0xb5c014: DecompressPointer r0
    //     0xb5c014: add             x0, x0, HEAP, lsl #32
    // 0xb5c018: ldr             x16, [fp, #0x10]
    // 0xb5c01c: stp             x16, x0, [SP]
    // 0xb5c020: r4 = 0
    //     0xb5c020: movz            x4, #0
    // 0xb5c024: ldr             x0, [SP, #8]
    // 0xb5c028: r5 = UnlinkedCall_0x613b5c
    //     0xb5c028: add             x16, PP, #0x56, lsl #12  ; [pp+0x56188] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c02c: ldp             x5, lr, [x16, #0x188]
    // 0xb5c030: blr             lr
    // 0xb5c034: r0 = Null
    //     0xb5c034: mov             x0, NULL
    // 0xb5c038: LeaveFrame
    //     0xb5c038: mov             SP, fp
    //     0xb5c03c: ldp             fp, lr, [SP], #0x10
    // 0xb5c040: ret
    //     0xb5c040: ret             
    // 0xb5c044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c048: b               #0xb5bff8
    // 0xb5c04c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c04c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xb5c050, size: 0x88
    // 0xb5c050: EnterFrame
    //     0xb5c050: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c054: mov             fp, SP
    // 0xb5c058: AllocStack(0x20)
    //     0xb5c058: sub             SP, SP, #0x20
    // 0xb5c05c: SetupParameters()
    //     0xb5c05c: ldr             x0, [fp, #0x28]
    //     0xb5c060: ldur            w1, [x0, #0x17]
    //     0xb5c064: add             x1, x1, HEAP, lsl #32
    // 0xb5c068: CheckStackOverflow
    //     0xb5c068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c06c: cmp             SP, x16
    //     0xb5c070: b.ls            #0xb5c0cc
    // 0xb5c074: LoadField: r0 = r1->field_f
    //     0xb5c074: ldur            w0, [x1, #0xf]
    // 0xb5c078: DecompressPointer r0
    //     0xb5c078: add             x0, x0, HEAP, lsl #32
    // 0xb5c07c: LoadField: r1 = r0->field_b
    //     0xb5c07c: ldur            w1, [x0, #0xb]
    // 0xb5c080: DecompressPointer r1
    //     0xb5c080: add             x1, x1, HEAP, lsl #32
    // 0xb5c084: cmp             w1, NULL
    // 0xb5c088: b.eq            #0xb5c0d4
    // 0xb5c08c: LoadField: r0 = r1->field_2f
    //     0xb5c08c: ldur            w0, [x1, #0x2f]
    // 0xb5c090: DecompressPointer r0
    //     0xb5c090: add             x0, x0, HEAP, lsl #32
    // 0xb5c094: ldr             x16, [fp, #0x20]
    // 0xb5c098: stp             x16, x0, [SP, #0x10]
    // 0xb5c09c: ldr             x16, [fp, #0x18]
    // 0xb5c0a0: ldr             lr, [fp, #0x10]
    // 0xb5c0a4: stp             lr, x16, [SP]
    // 0xb5c0a8: r4 = 0
    //     0xb5c0a8: movz            x4, #0
    // 0xb5c0ac: ldr             x0, [SP, #0x18]
    // 0xb5c0b0: r5 = UnlinkedCall_0x613b5c
    //     0xb5c0b0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56198] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c0b4: ldp             x5, lr, [x16, #0x198]
    // 0xb5c0b8: blr             lr
    // 0xb5c0bc: r0 = Null
    //     0xb5c0bc: mov             x0, NULL
    // 0xb5c0c0: LeaveFrame
    //     0xb5c0c0: mov             SP, fp
    //     0xb5c0c4: ldp             fp, lr, [SP], #0x10
    // 0xb5c0c8: ret
    //     0xb5c0c8: ret             
    // 0xb5c0cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c0cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c0d0: b               #0xb5c074
    // 0xb5c0d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c0d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xb5c0d8, size: 0x90
    // 0xb5c0d8: EnterFrame
    //     0xb5c0d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c0dc: mov             fp, SP
    // 0xb5c0e0: AllocStack(0x28)
    //     0xb5c0e0: sub             SP, SP, #0x28
    // 0xb5c0e4: SetupParameters()
    //     0xb5c0e4: ldr             x0, [fp, #0x30]
    //     0xb5c0e8: ldur            w1, [x0, #0x17]
    //     0xb5c0ec: add             x1, x1, HEAP, lsl #32
    // 0xb5c0f0: CheckStackOverflow
    //     0xb5c0f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c0f4: cmp             SP, x16
    //     0xb5c0f8: b.ls            #0xb5c15c
    // 0xb5c0fc: LoadField: r0 = r1->field_f
    //     0xb5c0fc: ldur            w0, [x1, #0xf]
    // 0xb5c100: DecompressPointer r0
    //     0xb5c100: add             x0, x0, HEAP, lsl #32
    // 0xb5c104: LoadField: r1 = r0->field_b
    //     0xb5c104: ldur            w1, [x0, #0xb]
    // 0xb5c108: DecompressPointer r1
    //     0xb5c108: add             x1, x1, HEAP, lsl #32
    // 0xb5c10c: cmp             w1, NULL
    // 0xb5c110: b.eq            #0xb5c164
    // 0xb5c114: LoadField: r0 = r1->field_2b
    //     0xb5c114: ldur            w0, [x1, #0x2b]
    // 0xb5c118: DecompressPointer r0
    //     0xb5c118: add             x0, x0, HEAP, lsl #32
    // 0xb5c11c: ldr             x16, [fp, #0x28]
    // 0xb5c120: stp             x16, x0, [SP, #0x18]
    // 0xb5c124: ldr             x16, [fp, #0x20]
    // 0xb5c128: ldr             lr, [fp, #0x18]
    // 0xb5c12c: stp             lr, x16, [SP, #8]
    // 0xb5c130: ldr             x16, [fp, #0x10]
    // 0xb5c134: str             x16, [SP]
    // 0xb5c138: r4 = 0
    //     0xb5c138: movz            x4, #0
    // 0xb5c13c: ldr             x0, [SP, #0x20]
    // 0xb5c140: r5 = UnlinkedCall_0x613b5c
    //     0xb5c140: add             x16, PP, #0x56, lsl #12  ; [pp+0x561a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c144: ldp             x5, lr, [x16, #0x1a8]
    // 0xb5c148: blr             lr
    // 0xb5c14c: r0 = Null
    //     0xb5c14c: mov             x0, NULL
    // 0xb5c150: LeaveFrame
    //     0xb5c150: mov             SP, fp
    //     0xb5c154: ldp             fp, lr, [SP], #0x10
    // 0xb5c158: ret
    //     0xb5c158: ret             
    // 0xb5c15c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c15c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c160: b               #0xb5c0fc
    // 0xb5c164: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c164: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0xb5c168, size: 0x84
    // 0xb5c168: EnterFrame
    //     0xb5c168: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c16c: mov             fp, SP
    // 0xb5c170: AllocStack(0x18)
    //     0xb5c170: sub             SP, SP, #0x18
    // 0xb5c174: SetupParameters()
    //     0xb5c174: ldr             x0, [fp, #0x20]
    //     0xb5c178: ldur            w1, [x0, #0x17]
    //     0xb5c17c: add             x1, x1, HEAP, lsl #32
    // 0xb5c180: CheckStackOverflow
    //     0xb5c180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c184: cmp             SP, x16
    //     0xb5c188: b.ls            #0xb5c1e0
    // 0xb5c18c: LoadField: r0 = r1->field_f
    //     0xb5c18c: ldur            w0, [x1, #0xf]
    // 0xb5c190: DecompressPointer r0
    //     0xb5c190: add             x0, x0, HEAP, lsl #32
    // 0xb5c194: LoadField: r1 = r0->field_b
    //     0xb5c194: ldur            w1, [x0, #0xb]
    // 0xb5c198: DecompressPointer r1
    //     0xb5c198: add             x1, x1, HEAP, lsl #32
    // 0xb5c19c: cmp             w1, NULL
    // 0xb5c1a0: b.eq            #0xb5c1e8
    // 0xb5c1a4: LoadField: r0 = r1->field_27
    //     0xb5c1a4: ldur            w0, [x1, #0x27]
    // 0xb5c1a8: DecompressPointer r0
    //     0xb5c1a8: add             x0, x0, HEAP, lsl #32
    // 0xb5c1ac: ldr             x16, [fp, #0x18]
    // 0xb5c1b0: stp             x16, x0, [SP, #8]
    // 0xb5c1b4: ldr             x16, [fp, #0x10]
    // 0xb5c1b8: str             x16, [SP]
    // 0xb5c1bc: r4 = 0
    //     0xb5c1bc: movz            x4, #0
    // 0xb5c1c0: ldr             x0, [SP, #0x10]
    // 0xb5c1c4: r5 = UnlinkedCall_0x613b5c
    //     0xb5c1c4: add             x16, PP, #0x56, lsl #12  ; [pp+0x561b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c1c8: ldp             x5, lr, [x16, #0x1b8]
    // 0xb5c1cc: blr             lr
    // 0xb5c1d0: r0 = Null
    //     0xb5c1d0: mov             x0, NULL
    // 0xb5c1d4: LeaveFrame
    //     0xb5c1d4: mov             SP, fp
    //     0xb5c1d8: ldp             fp, lr, [SP], #0x10
    // 0xb5c1dc: ret
    //     0xb5c1dc: ret             
    // 0xb5c1e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c1e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c1e4: b               #0xb5c18c
    // 0xb5c1e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c1e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xb5c1ec, size: 0x88
    // 0xb5c1ec: EnterFrame
    //     0xb5c1ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c1f0: mov             fp, SP
    // 0xb5c1f4: AllocStack(0x20)
    //     0xb5c1f4: sub             SP, SP, #0x20
    // 0xb5c1f8: SetupParameters()
    //     0xb5c1f8: ldr             x0, [fp, #0x28]
    //     0xb5c1fc: ldur            w1, [x0, #0x17]
    //     0xb5c200: add             x1, x1, HEAP, lsl #32
    // 0xb5c204: CheckStackOverflow
    //     0xb5c204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c208: cmp             SP, x16
    //     0xb5c20c: b.ls            #0xb5c268
    // 0xb5c210: LoadField: r0 = r1->field_f
    //     0xb5c210: ldur            w0, [x1, #0xf]
    // 0xb5c214: DecompressPointer r0
    //     0xb5c214: add             x0, x0, HEAP, lsl #32
    // 0xb5c218: LoadField: r1 = r0->field_b
    //     0xb5c218: ldur            w1, [x0, #0xb]
    // 0xb5c21c: DecompressPointer r1
    //     0xb5c21c: add             x1, x1, HEAP, lsl #32
    // 0xb5c220: cmp             w1, NULL
    // 0xb5c224: b.eq            #0xb5c270
    // 0xb5c228: LoadField: r0 = r1->field_23
    //     0xb5c228: ldur            w0, [x1, #0x23]
    // 0xb5c22c: DecompressPointer r0
    //     0xb5c22c: add             x0, x0, HEAP, lsl #32
    // 0xb5c230: ldr             x16, [fp, #0x20]
    // 0xb5c234: stp             x16, x0, [SP, #0x10]
    // 0xb5c238: ldr             x16, [fp, #0x18]
    // 0xb5c23c: ldr             lr, [fp, #0x10]
    // 0xb5c240: stp             lr, x16, [SP]
    // 0xb5c244: r4 = 0
    //     0xb5c244: movz            x4, #0
    // 0xb5c248: ldr             x0, [SP, #0x18]
    // 0xb5c24c: r5 = UnlinkedCall_0x613b5c
    //     0xb5c24c: add             x16, PP, #0x56, lsl #12  ; [pp+0x561c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c250: ldp             x5, lr, [x16, #0x1c8]
    // 0xb5c254: blr             lr
    // 0xb5c258: r0 = Null
    //     0xb5c258: mov             x0, NULL
    // 0xb5c25c: LeaveFrame
    //     0xb5c25c: mov             SP, fp
    //     0xb5c260: ldp             fp, lr, [SP], #0x10
    // 0xb5c264: ret
    //     0xb5c264: ret             
    // 0xb5c268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c268: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c26c: b               #0xb5c210
    // 0xb5c270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c270: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xb5c274, size: 0x90
    // 0xb5c274: EnterFrame
    //     0xb5c274: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c278: mov             fp, SP
    // 0xb5c27c: AllocStack(0x28)
    //     0xb5c27c: sub             SP, SP, #0x28
    // 0xb5c280: SetupParameters()
    //     0xb5c280: ldr             x0, [fp, #0x30]
    //     0xb5c284: ldur            w1, [x0, #0x17]
    //     0xb5c288: add             x1, x1, HEAP, lsl #32
    // 0xb5c28c: CheckStackOverflow
    //     0xb5c28c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c290: cmp             SP, x16
    //     0xb5c294: b.ls            #0xb5c2f8
    // 0xb5c298: LoadField: r0 = r1->field_f
    //     0xb5c298: ldur            w0, [x1, #0xf]
    // 0xb5c29c: DecompressPointer r0
    //     0xb5c29c: add             x0, x0, HEAP, lsl #32
    // 0xb5c2a0: LoadField: r1 = r0->field_b
    //     0xb5c2a0: ldur            w1, [x0, #0xb]
    // 0xb5c2a4: DecompressPointer r1
    //     0xb5c2a4: add             x1, x1, HEAP, lsl #32
    // 0xb5c2a8: cmp             w1, NULL
    // 0xb5c2ac: b.eq            #0xb5c300
    // 0xb5c2b0: LoadField: r0 = r1->field_1f
    //     0xb5c2b0: ldur            w0, [x1, #0x1f]
    // 0xb5c2b4: DecompressPointer r0
    //     0xb5c2b4: add             x0, x0, HEAP, lsl #32
    // 0xb5c2b8: ldr             x16, [fp, #0x28]
    // 0xb5c2bc: stp             x16, x0, [SP, #0x18]
    // 0xb5c2c0: ldr             x16, [fp, #0x20]
    // 0xb5c2c4: ldr             lr, [fp, #0x18]
    // 0xb5c2c8: stp             lr, x16, [SP, #8]
    // 0xb5c2cc: ldr             x16, [fp, #0x10]
    // 0xb5c2d0: str             x16, [SP]
    // 0xb5c2d4: r4 = 0
    //     0xb5c2d4: movz            x4, #0
    // 0xb5c2d8: ldr             x0, [SP, #0x20]
    // 0xb5c2dc: r5 = UnlinkedCall_0x613b5c
    //     0xb5c2dc: add             x16, PP, #0x56, lsl #12  ; [pp+0x561d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c2e0: ldp             x5, lr, [x16, #0x1d8]
    // 0xb5c2e4: blr             lr
    // 0xb5c2e8: r0 = Null
    //     0xb5c2e8: mov             x0, NULL
    // 0xb5c2ec: LeaveFrame
    //     0xb5c2ec: mov             SP, fp
    //     0xb5c2f0: ldp             fp, lr, [SP], #0x10
    // 0xb5c2f4: ret
    //     0xb5c2f4: ret             
    // 0xb5c2f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c2f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c2fc: b               #0xb5c298
    // 0xb5c300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0xb5c304, size: 0x84
    // 0xb5c304: EnterFrame
    //     0xb5c304: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c308: mov             fp, SP
    // 0xb5c30c: AllocStack(0x18)
    //     0xb5c30c: sub             SP, SP, #0x18
    // 0xb5c310: SetupParameters()
    //     0xb5c310: ldr             x0, [fp, #0x20]
    //     0xb5c314: ldur            w1, [x0, #0x17]
    //     0xb5c318: add             x1, x1, HEAP, lsl #32
    // 0xb5c31c: CheckStackOverflow
    //     0xb5c31c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c320: cmp             SP, x16
    //     0xb5c324: b.ls            #0xb5c37c
    // 0xb5c328: LoadField: r0 = r1->field_f
    //     0xb5c328: ldur            w0, [x1, #0xf]
    // 0xb5c32c: DecompressPointer r0
    //     0xb5c32c: add             x0, x0, HEAP, lsl #32
    // 0xb5c330: LoadField: r1 = r0->field_b
    //     0xb5c330: ldur            w1, [x0, #0xb]
    // 0xb5c334: DecompressPointer r1
    //     0xb5c334: add             x1, x1, HEAP, lsl #32
    // 0xb5c338: cmp             w1, NULL
    // 0xb5c33c: b.eq            #0xb5c384
    // 0xb5c340: LoadField: r0 = r1->field_1b
    //     0xb5c340: ldur            w0, [x1, #0x1b]
    // 0xb5c344: DecompressPointer r0
    //     0xb5c344: add             x0, x0, HEAP, lsl #32
    // 0xb5c348: ldr             x16, [fp, #0x18]
    // 0xb5c34c: stp             x16, x0, [SP, #8]
    // 0xb5c350: ldr             x16, [fp, #0x10]
    // 0xb5c354: str             x16, [SP]
    // 0xb5c358: r4 = 0
    //     0xb5c358: movz            x4, #0
    // 0xb5c35c: ldr             x0, [SP, #0x10]
    // 0xb5c360: r5 = UnlinkedCall_0x613b5c
    //     0xb5c360: add             x16, PP, #0x56, lsl #12  ; [pp+0x561e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5c364: ldp             x5, lr, [x16, #0x1e8]
    // 0xb5c368: blr             lr
    // 0xb5c36c: r0 = Null
    //     0xb5c36c: mov             x0, NULL
    // 0xb5c370: LeaveFrame
    //     0xb5c370: mov             SP, fp
    //     0xb5c374: ldp             fp, lr, [SP], #0x10
    // 0xb5c378: ret
    //     0xb5c378: ret             
    // 0xb5c37c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5c37c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5c380: b               #0xb5c328
    // 0xb5c384: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5c384: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4084, size: 0x4c, field offset: 0xc
//   const constructor, 
class CustomizedView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f184, size: 0x24
    // 0xc7f184: EnterFrame
    //     0xc7f184: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f188: mov             fp, SP
    // 0xc7f18c: mov             x0, x1
    // 0xc7f190: r1 = <CustomizedView>
    //     0xc7f190: add             x1, PP, #0x48, lsl #12  ; [pp+0x48868] TypeArguments: <CustomizedView>
    //     0xc7f194: ldr             x1, [x1, #0x868]
    // 0xc7f198: r0 = _CustomizedViewState()
    //     0xc7f198: bl              #0xc7f1a8  ; Allocate_CustomizedViewStateStub -> _CustomizedViewState (size=0x14)
    // 0xc7f19c: LeaveFrame
    //     0xc7f19c: mov             SP, fp
    //     0xc7f1a0: ldp             fp, lr, [SP], #0x10
    // 0xc7f1a4: ret
    //     0xc7f1a4: ret             
  }
}
