// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/bag_single_select.dart

// class id: 1049221, size: 0x8
class :: {
}

// class id: 3470, size: 0x1c, field offset: 0x14
class _BagSingleSelectState extends State<dynamic> {

  late String value; // offset: 0x14
  late String customisedValue; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xab4664, size: 0x8ac
    // 0xab4664: EnterFrame
    //     0xab4664: stp             fp, lr, [SP, #-0x10]!
    //     0xab4668: mov             fp, SP
    // 0xab466c: AllocStack(0x50)
    //     0xab466c: sub             SP, SP, #0x50
    // 0xab4670: SetupParameters(_BagSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xab4670: mov             x0, x1
    //     0xab4674: stur            x1, [fp, #-8]
    //     0xab4678: mov             x1, x2
    //     0xab467c: stur            x2, [fp, #-0x10]
    // 0xab4680: CheckStackOverflow
    //     0xab4680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4684: cmp             SP, x16
    //     0xab4688: b.ls            #0xab4edc
    // 0xab468c: LoadField: r2 = r0->field_b
    //     0xab468c: ldur            w2, [x0, #0xb]
    // 0xab4690: DecompressPointer r2
    //     0xab4690: add             x2, x2, HEAP, lsl #32
    // 0xab4694: cmp             w2, NULL
    // 0xab4698: b.eq            #0xab4ee4
    // 0xab469c: LoadField: r3 = r2->field_b
    //     0xab469c: ldur            w3, [x2, #0xb]
    // 0xab46a0: DecompressPointer r3
    //     0xab46a0: add             x3, x3, HEAP, lsl #32
    // 0xab46a4: cmp             w3, NULL
    // 0xab46a8: b.ne            #0xab46b4
    // 0xab46ac: r2 = Null
    //     0xab46ac: mov             x2, NULL
    // 0xab46b0: b               #0xab46e0
    // 0xab46b4: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xab46b4: ldur            w2, [x3, #0x17]
    // 0xab46b8: DecompressPointer r2
    //     0xab46b8: add             x2, x2, HEAP, lsl #32
    // 0xab46bc: cmp             w2, NULL
    // 0xab46c0: b.ne            #0xab46cc
    // 0xab46c4: r2 = Null
    //     0xab46c4: mov             x2, NULL
    // 0xab46c8: b               #0xab46e0
    // 0xab46cc: LoadField: r3 = r2->field_7
    //     0xab46cc: ldur            w3, [x2, #7]
    // 0xab46d0: cbnz            w3, #0xab46dc
    // 0xab46d4: r2 = false
    //     0xab46d4: add             x2, NULL, #0x30  ; false
    // 0xab46d8: b               #0xab46e0
    // 0xab46dc: r2 = true
    //     0xab46dc: add             x2, NULL, #0x20  ; true
    // 0xab46e0: cmp             w2, NULL
    // 0xab46e4: b.ne            #0xab4730
    // 0xab46e8: mov             x3, x0
    // 0xab46ec: r4 = 6
    //     0xab46ec: movz            x4, #0x6
    // 0xab46f0: r2 = 4
    //     0xab46f0: movz            x2, #0x4
    // 0xab46f4: r7 = Instance_CrossAxisAlignment
    //     0xab46f4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab46f8: ldr             x7, [x7, #0xa18]
    // 0xab46fc: r5 = Instance_MainAxisAlignment
    //     0xab46fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab4700: ldr             x5, [x5, #0xa08]
    // 0xab4704: r6 = Instance_MainAxisSize
    //     0xab4704: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab4708: ldr             x6, [x6, #0xa10]
    // 0xab470c: r1 = Instance_Axis
    //     0xab470c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab4710: r8 = Instance_VerticalDirection
    //     0xab4710: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab4714: ldr             x8, [x8, #0xa20]
    // 0xab4718: r0 = Instance__DeferringMouseCursor
    //     0xab4718: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab471c: r9 = Instance_Clip
    //     0xab471c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab4720: ldr             x9, [x9, #0x38]
    // 0xab4724: d1 = 0.500000
    //     0xab4724: fmov            d1, #0.50000000
    // 0xab4728: d0 = inf
    //     0xab4728: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab472c: b               #0xab4b28
    // 0xab4730: tbnz            w2, #4, #0xab4ae4
    // 0xab4734: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xab4734: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab4738: ldr             x0, [x0, #0x1c80]
    //     0xab473c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab4740: cmp             w0, w16
    //     0xab4744: b.ne            #0xab4750
    //     0xab4748: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xab474c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xab4750: r0 = GetNavigation.size()
    //     0xab4750: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xab4754: LoadField: d0 = r0->field_7
    //     0xab4754: ldur            d0, [x0, #7]
    // 0xab4758: d1 = 0.500000
    //     0xab4758: fmov            d1, #0.50000000
    // 0xab475c: fmul            d2, d0, d1
    // 0xab4760: stur            d2, [fp, #-0x40]
    // 0xab4764: r0 = BoxConstraints()
    //     0xab4764: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xab4768: stur            x0, [fp, #-0x20]
    // 0xab476c: StoreField: r0->field_7 = rZR
    //     0xab476c: stur            xzr, [x0, #7]
    // 0xab4770: ldur            d0, [fp, #-0x40]
    // 0xab4774: StoreField: r0->field_f = d0
    //     0xab4774: stur            d0, [x0, #0xf]
    // 0xab4778: ArrayStore: r0[0] = rZR  ; List_8
    //     0xab4778: stur            xzr, [x0, #0x17]
    // 0xab477c: d0 = inf
    //     0xab477c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab4780: StoreField: r0->field_1f = d0
    //     0xab4780: stur            d0, [x0, #0x1f]
    // 0xab4784: ldur            x2, [fp, #-8]
    // 0xab4788: LoadField: r1 = r2->field_b
    //     0xab4788: ldur            w1, [x2, #0xb]
    // 0xab478c: DecompressPointer r1
    //     0xab478c: add             x1, x1, HEAP, lsl #32
    // 0xab4790: cmp             w1, NULL
    // 0xab4794: b.eq            #0xab4ee8
    // 0xab4798: LoadField: r3 = r1->field_b
    //     0xab4798: ldur            w3, [x1, #0xb]
    // 0xab479c: DecompressPointer r3
    //     0xab479c: add             x3, x3, HEAP, lsl #32
    // 0xab47a0: cmp             w3, NULL
    // 0xab47a4: b.ne            #0xab47b0
    // 0xab47a8: r1 = Null
    //     0xab47a8: mov             x1, NULL
    // 0xab47ac: b               #0xab47b8
    // 0xab47b0: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xab47b0: ldur            w1, [x3, #0x17]
    // 0xab47b4: DecompressPointer r1
    //     0xab47b4: add             x1, x1, HEAP, lsl #32
    // 0xab47b8: cmp             w1, NULL
    // 0xab47bc: b.ne            #0xab47c8
    // 0xab47c0: r3 = ""
    //     0xab47c0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab47c4: b               #0xab47cc
    // 0xab47c8: mov             x3, x1
    // 0xab47cc: ldur            x1, [fp, #-0x10]
    // 0xab47d0: stur            x3, [fp, #-0x18]
    // 0xab47d4: r0 = of()
    //     0xab47d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab47d8: LoadField: r1 = r0->field_87
    //     0xab47d8: ldur            w1, [x0, #0x87]
    // 0xab47dc: DecompressPointer r1
    //     0xab47dc: add             x1, x1, HEAP, lsl #32
    // 0xab47e0: LoadField: r0 = r1->field_7
    //     0xab47e0: ldur            w0, [x1, #7]
    // 0xab47e4: DecompressPointer r0
    //     0xab47e4: add             x0, x0, HEAP, lsl #32
    // 0xab47e8: r16 = 16.000000
    //     0xab47e8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab47ec: ldr             x16, [x16, #0x188]
    // 0xab47f0: r30 = Instance_Color
    //     0xab47f0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab47f4: stp             lr, x16, [SP]
    // 0xab47f8: mov             x1, x0
    // 0xab47fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab47fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab4800: ldr             x4, [x4, #0xaa0]
    // 0xab4804: r0 = copyWith()
    //     0xab4804: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab4808: stur            x0, [fp, #-0x28]
    // 0xab480c: r0 = TextSpan()
    //     0xab480c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab4810: mov             x3, x0
    // 0xab4814: ldur            x0, [fp, #-0x18]
    // 0xab4818: stur            x3, [fp, #-0x30]
    // 0xab481c: StoreField: r3->field_b = r0
    //     0xab481c: stur            w0, [x3, #0xb]
    // 0xab4820: r0 = Instance__DeferringMouseCursor
    //     0xab4820: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4824: ArrayStore: r3[0] = r0  ; List_4
    //     0xab4824: stur            w0, [x3, #0x17]
    // 0xab4828: ldur            x1, [fp, #-0x28]
    // 0xab482c: StoreField: r3->field_7 = r1
    //     0xab482c: stur            w1, [x3, #7]
    // 0xab4830: r1 = Null
    //     0xab4830: mov             x1, NULL
    // 0xab4834: r2 = 6
    //     0xab4834: movz            x2, #0x6
    // 0xab4838: r0 = AllocateArray()
    //     0xab4838: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab483c: r16 = " : "
    //     0xab483c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xab4840: ldr             x16, [x16, #0x680]
    // 0xab4844: StoreField: r0->field_f = r16
    //     0xab4844: stur            w16, [x0, #0xf]
    // 0xab4848: ldur            x1, [fp, #-8]
    // 0xab484c: LoadField: r2 = r1->field_13
    //     0xab484c: ldur            w2, [x1, #0x13]
    // 0xab4850: DecompressPointer r2
    //     0xab4850: add             x2, x2, HEAP, lsl #32
    // 0xab4854: r16 = Sentinel
    //     0xab4854: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab4858: cmp             w2, w16
    // 0xab485c: b.eq            #0xab4eec
    // 0xab4860: StoreField: r0->field_13 = r2
    //     0xab4860: stur            w2, [x0, #0x13]
    // 0xab4864: r16 = " "
    //     0xab4864: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xab4868: ArrayStore: r0[0] = r16  ; List_4
    //     0xab4868: stur            w16, [x0, #0x17]
    // 0xab486c: str             x0, [SP]
    // 0xab4870: r0 = _interpolate()
    //     0xab4870: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab4874: ldur            x1, [fp, #-0x10]
    // 0xab4878: stur            x0, [fp, #-0x18]
    // 0xab487c: r0 = of()
    //     0xab487c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab4880: LoadField: r1 = r0->field_87
    //     0xab4880: ldur            w1, [x0, #0x87]
    // 0xab4884: DecompressPointer r1
    //     0xab4884: add             x1, x1, HEAP, lsl #32
    // 0xab4888: LoadField: r0 = r1->field_2b
    //     0xab4888: ldur            w0, [x1, #0x2b]
    // 0xab488c: DecompressPointer r0
    //     0xab488c: add             x0, x0, HEAP, lsl #32
    // 0xab4890: r16 = 14.000000
    //     0xab4890: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab4894: ldr             x16, [x16, #0x1d8]
    // 0xab4898: r30 = Instance_Color
    //     0xab4898: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab489c: stp             lr, x16, [SP]
    // 0xab48a0: mov             x1, x0
    // 0xab48a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab48a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab48a8: ldr             x4, [x4, #0xaa0]
    // 0xab48ac: r0 = copyWith()
    //     0xab48ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab48b0: stur            x0, [fp, #-0x28]
    // 0xab48b4: r0 = TextSpan()
    //     0xab48b4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab48b8: mov             x3, x0
    // 0xab48bc: ldur            x0, [fp, #-0x18]
    // 0xab48c0: stur            x3, [fp, #-0x38]
    // 0xab48c4: StoreField: r3->field_b = r0
    //     0xab48c4: stur            w0, [x3, #0xb]
    // 0xab48c8: r0 = Instance__DeferringMouseCursor
    //     0xab48c8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab48cc: ArrayStore: r3[0] = r0  ; List_4
    //     0xab48cc: stur            w0, [x3, #0x17]
    // 0xab48d0: ldur            x1, [fp, #-0x28]
    // 0xab48d4: StoreField: r3->field_7 = r1
    //     0xab48d4: stur            w1, [x3, #7]
    // 0xab48d8: r1 = Null
    //     0xab48d8: mov             x1, NULL
    // 0xab48dc: r2 = 4
    //     0xab48dc: movz            x2, #0x4
    // 0xab48e0: r0 = AllocateArray()
    //     0xab48e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab48e4: mov             x2, x0
    // 0xab48e8: ldur            x0, [fp, #-0x30]
    // 0xab48ec: stur            x2, [fp, #-0x18]
    // 0xab48f0: StoreField: r2->field_f = r0
    //     0xab48f0: stur            w0, [x2, #0xf]
    // 0xab48f4: ldur            x0, [fp, #-0x38]
    // 0xab48f8: StoreField: r2->field_13 = r0
    //     0xab48f8: stur            w0, [x2, #0x13]
    // 0xab48fc: r1 = <InlineSpan>
    //     0xab48fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xab4900: ldr             x1, [x1, #0xe40]
    // 0xab4904: r0 = AllocateGrowableArray()
    //     0xab4904: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab4908: mov             x1, x0
    // 0xab490c: ldur            x0, [fp, #-0x18]
    // 0xab4910: stur            x1, [fp, #-0x28]
    // 0xab4914: StoreField: r1->field_f = r0
    //     0xab4914: stur            w0, [x1, #0xf]
    // 0xab4918: r2 = 4
    //     0xab4918: movz            x2, #0x4
    // 0xab491c: StoreField: r1->field_b = r2
    //     0xab491c: stur            w2, [x1, #0xb]
    // 0xab4920: r0 = TextSpan()
    //     0xab4920: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab4924: mov             x1, x0
    // 0xab4928: ldur            x0, [fp, #-0x28]
    // 0xab492c: stur            x1, [fp, #-0x18]
    // 0xab4930: StoreField: r1->field_f = r0
    //     0xab4930: stur            w0, [x1, #0xf]
    // 0xab4934: r0 = Instance__DeferringMouseCursor
    //     0xab4934: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4938: ArrayStore: r1[0] = r0  ; List_4
    //     0xab4938: stur            w0, [x1, #0x17]
    // 0xab493c: r0 = RichText()
    //     0xab493c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xab4940: mov             x1, x0
    // 0xab4944: ldur            x2, [fp, #-0x18]
    // 0xab4948: stur            x0, [fp, #-0x18]
    // 0xab494c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab494c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab4950: r0 = RichText()
    //     0xab4950: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xab4954: r0 = ConstrainedBox()
    //     0xab4954: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xab4958: mov             x1, x0
    // 0xab495c: ldur            x0, [fp, #-0x20]
    // 0xab4960: stur            x1, [fp, #-0x28]
    // 0xab4964: StoreField: r1->field_f = r0
    //     0xab4964: stur            w0, [x1, #0xf]
    // 0xab4968: ldur            x0, [fp, #-0x18]
    // 0xab496c: StoreField: r1->field_b = r0
    //     0xab496c: stur            w0, [x1, #0xb]
    // 0xab4970: ldur            x3, [fp, #-8]
    // 0xab4974: LoadField: r0 = r3->field_b
    //     0xab4974: ldur            w0, [x3, #0xb]
    // 0xab4978: DecompressPointer r0
    //     0xab4978: add             x0, x0, HEAP, lsl #32
    // 0xab497c: cmp             w0, NULL
    // 0xab4980: b.eq            #0xab4ef8
    // 0xab4984: LoadField: r2 = r0->field_b
    //     0xab4984: ldur            w2, [x0, #0xb]
    // 0xab4988: DecompressPointer r2
    //     0xab4988: add             x2, x2, HEAP, lsl #32
    // 0xab498c: cmp             w2, NULL
    // 0xab4990: b.ne            #0xab499c
    // 0xab4994: r0 = Null
    //     0xab4994: mov             x0, NULL
    // 0xab4998: b               #0xab49c8
    // 0xab499c: LoadField: r0 = r2->field_2b
    //     0xab499c: ldur            w0, [x2, #0x2b]
    // 0xab49a0: DecompressPointer r0
    //     0xab49a0: add             x0, x0, HEAP, lsl #32
    // 0xab49a4: r2 = LoadClassIdInstr(r0)
    //     0xab49a4: ldur            x2, [x0, #-1]
    //     0xab49a8: ubfx            x2, x2, #0xc, #0x14
    // 0xab49ac: str             x0, [SP]
    // 0xab49b0: mov             x0, x2
    // 0xab49b4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xab49b4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xab49b8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xab49b8: movz            x17, #0x2700
    //     0xab49bc: add             lr, x0, x17
    //     0xab49c0: ldr             lr, [x21, lr, lsl #3]
    //     0xab49c4: blr             lr
    // 0xab49c8: cmp             w0, NULL
    // 0xab49cc: b.ne            #0xab49d8
    // 0xab49d0: r2 = ""
    //     0xab49d0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab49d4: b               #0xab49dc
    // 0xab49d8: mov             x2, x0
    // 0xab49dc: ldur            x0, [fp, #-0x28]
    // 0xab49e0: ldur            x1, [fp, #-0x10]
    // 0xab49e4: stur            x2, [fp, #-0x18]
    // 0xab49e8: r0 = of()
    //     0xab49e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab49ec: LoadField: r1 = r0->field_87
    //     0xab49ec: ldur            w1, [x0, #0x87]
    // 0xab49f0: DecompressPointer r1
    //     0xab49f0: add             x1, x1, HEAP, lsl #32
    // 0xab49f4: LoadField: r0 = r1->field_2b
    //     0xab49f4: ldur            w0, [x1, #0x2b]
    // 0xab49f8: DecompressPointer r0
    //     0xab49f8: add             x0, x0, HEAP, lsl #32
    // 0xab49fc: r16 = 14.000000
    //     0xab49fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab4a00: ldr             x16, [x16, #0x1d8]
    // 0xab4a04: r30 = Instance_Color
    //     0xab4a04: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab4a08: stp             lr, x16, [SP]
    // 0xab4a0c: mov             x1, x0
    // 0xab4a10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab4a10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab4a14: ldr             x4, [x4, #0xaa0]
    // 0xab4a18: r0 = copyWith()
    //     0xab4a18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab4a1c: stur            x0, [fp, #-0x20]
    // 0xab4a20: r0 = Text()
    //     0xab4a20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab4a24: mov             x3, x0
    // 0xab4a28: ldur            x0, [fp, #-0x18]
    // 0xab4a2c: stur            x3, [fp, #-0x30]
    // 0xab4a30: StoreField: r3->field_b = r0
    //     0xab4a30: stur            w0, [x3, #0xb]
    // 0xab4a34: ldur            x0, [fp, #-0x20]
    // 0xab4a38: StoreField: r3->field_13 = r0
    //     0xab4a38: stur            w0, [x3, #0x13]
    // 0xab4a3c: r1 = Null
    //     0xab4a3c: mov             x1, NULL
    // 0xab4a40: r2 = 6
    //     0xab4a40: movz            x2, #0x6
    // 0xab4a44: r0 = AllocateArray()
    //     0xab4a44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab4a48: mov             x2, x0
    // 0xab4a4c: ldur            x0, [fp, #-0x28]
    // 0xab4a50: stur            x2, [fp, #-0x18]
    // 0xab4a54: StoreField: r2->field_f = r0
    //     0xab4a54: stur            w0, [x2, #0xf]
    // 0xab4a58: r16 = Instance_Spacer
    //     0xab4a58: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab4a5c: ldr             x16, [x16, #0xf0]
    // 0xab4a60: StoreField: r2->field_13 = r16
    //     0xab4a60: stur            w16, [x2, #0x13]
    // 0xab4a64: ldur            x0, [fp, #-0x30]
    // 0xab4a68: ArrayStore: r2[0] = r0  ; List_4
    //     0xab4a68: stur            w0, [x2, #0x17]
    // 0xab4a6c: r1 = <Widget>
    //     0xab4a6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab4a70: r0 = AllocateGrowableArray()
    //     0xab4a70: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab4a74: mov             x1, x0
    // 0xab4a78: ldur            x0, [fp, #-0x18]
    // 0xab4a7c: stur            x1, [fp, #-0x20]
    // 0xab4a80: StoreField: r1->field_f = r0
    //     0xab4a80: stur            w0, [x1, #0xf]
    // 0xab4a84: r4 = 6
    //     0xab4a84: movz            x4, #0x6
    // 0xab4a88: StoreField: r1->field_b = r4
    //     0xab4a88: stur            w4, [x1, #0xb]
    // 0xab4a8c: r0 = Row()
    //     0xab4a8c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab4a90: r1 = Instance_Axis
    //     0xab4a90: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab4a94: StoreField: r0->field_f = r1
    //     0xab4a94: stur            w1, [x0, #0xf]
    // 0xab4a98: r5 = Instance_MainAxisAlignment
    //     0xab4a98: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab4a9c: ldr             x5, [x5, #0xa08]
    // 0xab4aa0: StoreField: r0->field_13 = r5
    //     0xab4aa0: stur            w5, [x0, #0x13]
    // 0xab4aa4: r6 = Instance_MainAxisSize
    //     0xab4aa4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab4aa8: ldr             x6, [x6, #0xa10]
    // 0xab4aac: ArrayStore: r0[0] = r6  ; List_4
    //     0xab4aac: stur            w6, [x0, #0x17]
    // 0xab4ab0: r7 = Instance_CrossAxisAlignment
    //     0xab4ab0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab4ab4: ldr             x7, [x7, #0xa18]
    // 0xab4ab8: StoreField: r0->field_1b = r7
    //     0xab4ab8: stur            w7, [x0, #0x1b]
    // 0xab4abc: r8 = Instance_VerticalDirection
    //     0xab4abc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab4ac0: ldr             x8, [x8, #0xa20]
    // 0xab4ac4: StoreField: r0->field_23 = r8
    //     0xab4ac4: stur            w8, [x0, #0x23]
    // 0xab4ac8: r9 = Instance_Clip
    //     0xab4ac8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab4acc: ldr             x9, [x9, #0x38]
    // 0xab4ad0: StoreField: r0->field_2b = r9
    //     0xab4ad0: stur            w9, [x0, #0x2b]
    // 0xab4ad4: StoreField: r0->field_2f = rZR
    //     0xab4ad4: stur            xzr, [x0, #0x2f]
    // 0xab4ad8: ldur            x1, [fp, #-0x20]
    // 0xab4adc: StoreField: r0->field_b = r1
    //     0xab4adc: stur            w1, [x0, #0xb]
    // 0xab4ae0: b               #0xab4ed0
    // 0xab4ae4: mov             x3, x0
    // 0xab4ae8: r4 = 6
    //     0xab4ae8: movz            x4, #0x6
    // 0xab4aec: r2 = 4
    //     0xab4aec: movz            x2, #0x4
    // 0xab4af0: r7 = Instance_CrossAxisAlignment
    //     0xab4af0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab4af4: ldr             x7, [x7, #0xa18]
    // 0xab4af8: r5 = Instance_MainAxisAlignment
    //     0xab4af8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab4afc: ldr             x5, [x5, #0xa08]
    // 0xab4b00: r6 = Instance_MainAxisSize
    //     0xab4b00: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab4b04: ldr             x6, [x6, #0xa10]
    // 0xab4b08: r1 = Instance_Axis
    //     0xab4b08: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab4b0c: r8 = Instance_VerticalDirection
    //     0xab4b0c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab4b10: ldr             x8, [x8, #0xa20]
    // 0xab4b14: r0 = Instance__DeferringMouseCursor
    //     0xab4b14: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4b18: r9 = Instance_Clip
    //     0xab4b18: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab4b1c: ldr             x9, [x9, #0x38]
    // 0xab4b20: d1 = 0.500000
    //     0xab4b20: fmov            d1, #0.50000000
    // 0xab4b24: d0 = inf
    //     0xab4b24: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab4b28: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xab4b28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab4b2c: ldr             x0, [x0, #0x1c80]
    //     0xab4b30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab4b34: cmp             w0, w16
    //     0xab4b38: b.ne            #0xab4b44
    //     0xab4b3c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xab4b40: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xab4b44: r0 = GetNavigation.size()
    //     0xab4b44: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xab4b48: LoadField: d0 = r0->field_7
    //     0xab4b48: ldur            d0, [x0, #7]
    // 0xab4b4c: d1 = 0.500000
    //     0xab4b4c: fmov            d1, #0.50000000
    // 0xab4b50: fmul            d2, d0, d1
    // 0xab4b54: stur            d2, [fp, #-0x40]
    // 0xab4b58: r0 = BoxConstraints()
    //     0xab4b58: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xab4b5c: stur            x0, [fp, #-0x20]
    // 0xab4b60: StoreField: r0->field_7 = rZR
    //     0xab4b60: stur            xzr, [x0, #7]
    // 0xab4b64: ldur            d0, [fp, #-0x40]
    // 0xab4b68: StoreField: r0->field_f = d0
    //     0xab4b68: stur            d0, [x0, #0xf]
    // 0xab4b6c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xab4b6c: stur            xzr, [x0, #0x17]
    // 0xab4b70: d0 = inf
    //     0xab4b70: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab4b74: StoreField: r0->field_1f = d0
    //     0xab4b74: stur            d0, [x0, #0x1f]
    // 0xab4b78: ldur            x2, [fp, #-8]
    // 0xab4b7c: LoadField: r1 = r2->field_b
    //     0xab4b7c: ldur            w1, [x2, #0xb]
    // 0xab4b80: DecompressPointer r1
    //     0xab4b80: add             x1, x1, HEAP, lsl #32
    // 0xab4b84: cmp             w1, NULL
    // 0xab4b88: b.eq            #0xab4efc
    // 0xab4b8c: LoadField: r3 = r1->field_f
    //     0xab4b8c: ldur            w3, [x1, #0xf]
    // 0xab4b90: DecompressPointer r3
    //     0xab4b90: add             x3, x3, HEAP, lsl #32
    // 0xab4b94: cmp             w3, NULL
    // 0xab4b98: b.ne            #0xab4ba4
    // 0xab4b9c: r1 = Null
    //     0xab4b9c: mov             x1, NULL
    // 0xab4ba0: b               #0xab4bac
    // 0xab4ba4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xab4ba4: ldur            w1, [x3, #0x17]
    // 0xab4ba8: DecompressPointer r1
    //     0xab4ba8: add             x1, x1, HEAP, lsl #32
    // 0xab4bac: cmp             w1, NULL
    // 0xab4bb0: b.ne            #0xab4bbc
    // 0xab4bb4: r3 = ""
    //     0xab4bb4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab4bb8: b               #0xab4bc0
    // 0xab4bbc: mov             x3, x1
    // 0xab4bc0: ldur            x1, [fp, #-0x10]
    // 0xab4bc4: stur            x3, [fp, #-0x18]
    // 0xab4bc8: r0 = of()
    //     0xab4bc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab4bcc: LoadField: r1 = r0->field_87
    //     0xab4bcc: ldur            w1, [x0, #0x87]
    // 0xab4bd0: DecompressPointer r1
    //     0xab4bd0: add             x1, x1, HEAP, lsl #32
    // 0xab4bd4: LoadField: r0 = r1->field_7
    //     0xab4bd4: ldur            w0, [x1, #7]
    // 0xab4bd8: DecompressPointer r0
    //     0xab4bd8: add             x0, x0, HEAP, lsl #32
    // 0xab4bdc: r16 = 16.000000
    //     0xab4bdc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab4be0: ldr             x16, [x16, #0x188]
    // 0xab4be4: r30 = Instance_Color
    //     0xab4be4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab4be8: stp             lr, x16, [SP]
    // 0xab4bec: mov             x1, x0
    // 0xab4bf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab4bf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab4bf4: ldr             x4, [x4, #0xaa0]
    // 0xab4bf8: r0 = copyWith()
    //     0xab4bf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab4bfc: stur            x0, [fp, #-0x28]
    // 0xab4c00: r0 = TextSpan()
    //     0xab4c00: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab4c04: mov             x3, x0
    // 0xab4c08: ldur            x0, [fp, #-0x18]
    // 0xab4c0c: stur            x3, [fp, #-0x30]
    // 0xab4c10: StoreField: r3->field_b = r0
    //     0xab4c10: stur            w0, [x3, #0xb]
    // 0xab4c14: r0 = Instance__DeferringMouseCursor
    //     0xab4c14: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4c18: ArrayStore: r3[0] = r0  ; List_4
    //     0xab4c18: stur            w0, [x3, #0x17]
    // 0xab4c1c: ldur            x1, [fp, #-0x28]
    // 0xab4c20: StoreField: r3->field_7 = r1
    //     0xab4c20: stur            w1, [x3, #7]
    // 0xab4c24: r1 = Null
    //     0xab4c24: mov             x1, NULL
    // 0xab4c28: r2 = 6
    //     0xab4c28: movz            x2, #0x6
    // 0xab4c2c: r0 = AllocateArray()
    //     0xab4c2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab4c30: r16 = " : "
    //     0xab4c30: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xab4c34: ldr             x16, [x16, #0x680]
    // 0xab4c38: StoreField: r0->field_f = r16
    //     0xab4c38: stur            w16, [x0, #0xf]
    // 0xab4c3c: ldur            x1, [fp, #-8]
    // 0xab4c40: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xab4c40: ldur            w2, [x1, #0x17]
    // 0xab4c44: DecompressPointer r2
    //     0xab4c44: add             x2, x2, HEAP, lsl #32
    // 0xab4c48: r16 = Sentinel
    //     0xab4c48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab4c4c: cmp             w2, w16
    // 0xab4c50: b.eq            #0xab4f00
    // 0xab4c54: StoreField: r0->field_13 = r2
    //     0xab4c54: stur            w2, [x0, #0x13]
    // 0xab4c58: r16 = " "
    //     0xab4c58: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xab4c5c: ArrayStore: r0[0] = r16  ; List_4
    //     0xab4c5c: stur            w16, [x0, #0x17]
    // 0xab4c60: str             x0, [SP]
    // 0xab4c64: r0 = _interpolate()
    //     0xab4c64: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab4c68: ldur            x1, [fp, #-0x10]
    // 0xab4c6c: stur            x0, [fp, #-0x18]
    // 0xab4c70: r0 = of()
    //     0xab4c70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab4c74: LoadField: r1 = r0->field_87
    //     0xab4c74: ldur            w1, [x0, #0x87]
    // 0xab4c78: DecompressPointer r1
    //     0xab4c78: add             x1, x1, HEAP, lsl #32
    // 0xab4c7c: LoadField: r0 = r1->field_2b
    //     0xab4c7c: ldur            w0, [x1, #0x2b]
    // 0xab4c80: DecompressPointer r0
    //     0xab4c80: add             x0, x0, HEAP, lsl #32
    // 0xab4c84: r16 = 14.000000
    //     0xab4c84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab4c88: ldr             x16, [x16, #0x1d8]
    // 0xab4c8c: r30 = Instance_Color
    //     0xab4c8c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab4c90: stp             lr, x16, [SP]
    // 0xab4c94: mov             x1, x0
    // 0xab4c98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab4c98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab4c9c: ldr             x4, [x4, #0xaa0]
    // 0xab4ca0: r0 = copyWith()
    //     0xab4ca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab4ca4: stur            x0, [fp, #-0x28]
    // 0xab4ca8: r0 = TextSpan()
    //     0xab4ca8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab4cac: mov             x3, x0
    // 0xab4cb0: ldur            x0, [fp, #-0x18]
    // 0xab4cb4: stur            x3, [fp, #-0x38]
    // 0xab4cb8: StoreField: r3->field_b = r0
    //     0xab4cb8: stur            w0, [x3, #0xb]
    // 0xab4cbc: r0 = Instance__DeferringMouseCursor
    //     0xab4cbc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4cc0: ArrayStore: r3[0] = r0  ; List_4
    //     0xab4cc0: stur            w0, [x3, #0x17]
    // 0xab4cc4: ldur            x1, [fp, #-0x28]
    // 0xab4cc8: StoreField: r3->field_7 = r1
    //     0xab4cc8: stur            w1, [x3, #7]
    // 0xab4ccc: r1 = Null
    //     0xab4ccc: mov             x1, NULL
    // 0xab4cd0: r2 = 4
    //     0xab4cd0: movz            x2, #0x4
    // 0xab4cd4: r0 = AllocateArray()
    //     0xab4cd4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab4cd8: mov             x2, x0
    // 0xab4cdc: ldur            x0, [fp, #-0x30]
    // 0xab4ce0: stur            x2, [fp, #-0x18]
    // 0xab4ce4: StoreField: r2->field_f = r0
    //     0xab4ce4: stur            w0, [x2, #0xf]
    // 0xab4ce8: ldur            x0, [fp, #-0x38]
    // 0xab4cec: StoreField: r2->field_13 = r0
    //     0xab4cec: stur            w0, [x2, #0x13]
    // 0xab4cf0: r1 = <InlineSpan>
    //     0xab4cf0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xab4cf4: ldr             x1, [x1, #0xe40]
    // 0xab4cf8: r0 = AllocateGrowableArray()
    //     0xab4cf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab4cfc: mov             x1, x0
    // 0xab4d00: ldur            x0, [fp, #-0x18]
    // 0xab4d04: stur            x1, [fp, #-0x28]
    // 0xab4d08: StoreField: r1->field_f = r0
    //     0xab4d08: stur            w0, [x1, #0xf]
    // 0xab4d0c: r0 = 4
    //     0xab4d0c: movz            x0, #0x4
    // 0xab4d10: StoreField: r1->field_b = r0
    //     0xab4d10: stur            w0, [x1, #0xb]
    // 0xab4d14: r0 = TextSpan()
    //     0xab4d14: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab4d18: mov             x1, x0
    // 0xab4d1c: ldur            x0, [fp, #-0x28]
    // 0xab4d20: stur            x1, [fp, #-0x18]
    // 0xab4d24: StoreField: r1->field_f = r0
    //     0xab4d24: stur            w0, [x1, #0xf]
    // 0xab4d28: r0 = Instance__DeferringMouseCursor
    //     0xab4d28: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4d2c: ArrayStore: r1[0] = r0  ; List_4
    //     0xab4d2c: stur            w0, [x1, #0x17]
    // 0xab4d30: r0 = RichText()
    //     0xab4d30: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xab4d34: mov             x1, x0
    // 0xab4d38: ldur            x2, [fp, #-0x18]
    // 0xab4d3c: stur            x0, [fp, #-0x18]
    // 0xab4d40: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab4d40: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab4d44: r0 = RichText()
    //     0xab4d44: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xab4d48: r0 = ConstrainedBox()
    //     0xab4d48: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xab4d4c: mov             x1, x0
    // 0xab4d50: ldur            x0, [fp, #-0x20]
    // 0xab4d54: stur            x1, [fp, #-0x28]
    // 0xab4d58: StoreField: r1->field_f = r0
    //     0xab4d58: stur            w0, [x1, #0xf]
    // 0xab4d5c: ldur            x0, [fp, #-0x18]
    // 0xab4d60: StoreField: r1->field_b = r0
    //     0xab4d60: stur            w0, [x1, #0xb]
    // 0xab4d64: ldur            x0, [fp, #-8]
    // 0xab4d68: LoadField: r2 = r0->field_b
    //     0xab4d68: ldur            w2, [x0, #0xb]
    // 0xab4d6c: DecompressPointer r2
    //     0xab4d6c: add             x2, x2, HEAP, lsl #32
    // 0xab4d70: cmp             w2, NULL
    // 0xab4d74: b.eq            #0xab4f0c
    // 0xab4d78: LoadField: r0 = r2->field_f
    //     0xab4d78: ldur            w0, [x2, #0xf]
    // 0xab4d7c: DecompressPointer r0
    //     0xab4d7c: add             x0, x0, HEAP, lsl #32
    // 0xab4d80: cmp             w0, NULL
    // 0xab4d84: b.ne            #0xab4d90
    // 0xab4d88: r0 = Null
    //     0xab4d88: mov             x0, NULL
    // 0xab4d8c: b               #0xab4db8
    // 0xab4d90: LoadField: r2 = r0->field_2b
    //     0xab4d90: ldur            w2, [x0, #0x2b]
    // 0xab4d94: DecompressPointer r2
    //     0xab4d94: add             x2, x2, HEAP, lsl #32
    // 0xab4d98: r0 = LoadClassIdInstr(r2)
    //     0xab4d98: ldur            x0, [x2, #-1]
    //     0xab4d9c: ubfx            x0, x0, #0xc, #0x14
    // 0xab4da0: str             x2, [SP]
    // 0xab4da4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xab4da4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xab4da8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xab4da8: movz            x17, #0x2700
    //     0xab4dac: add             lr, x0, x17
    //     0xab4db0: ldr             lr, [x21, lr, lsl #3]
    //     0xab4db4: blr             lr
    // 0xab4db8: cmp             w0, NULL
    // 0xab4dbc: b.ne            #0xab4dc8
    // 0xab4dc0: r2 = ""
    //     0xab4dc0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab4dc4: b               #0xab4dcc
    // 0xab4dc8: mov             x2, x0
    // 0xab4dcc: ldur            x0, [fp, #-0x28]
    // 0xab4dd0: ldur            x1, [fp, #-0x10]
    // 0xab4dd4: stur            x2, [fp, #-8]
    // 0xab4dd8: r0 = of()
    //     0xab4dd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab4ddc: LoadField: r1 = r0->field_87
    //     0xab4ddc: ldur            w1, [x0, #0x87]
    // 0xab4de0: DecompressPointer r1
    //     0xab4de0: add             x1, x1, HEAP, lsl #32
    // 0xab4de4: LoadField: r0 = r1->field_2b
    //     0xab4de4: ldur            w0, [x1, #0x2b]
    // 0xab4de8: DecompressPointer r0
    //     0xab4de8: add             x0, x0, HEAP, lsl #32
    // 0xab4dec: r16 = 14.000000
    //     0xab4dec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab4df0: ldr             x16, [x16, #0x1d8]
    // 0xab4df4: r30 = Instance_Color
    //     0xab4df4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab4df8: stp             lr, x16, [SP]
    // 0xab4dfc: mov             x1, x0
    // 0xab4e00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab4e00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab4e04: ldr             x4, [x4, #0xaa0]
    // 0xab4e08: r0 = copyWith()
    //     0xab4e08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab4e0c: stur            x0, [fp, #-0x10]
    // 0xab4e10: r0 = Text()
    //     0xab4e10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab4e14: mov             x3, x0
    // 0xab4e18: ldur            x0, [fp, #-8]
    // 0xab4e1c: stur            x3, [fp, #-0x18]
    // 0xab4e20: StoreField: r3->field_b = r0
    //     0xab4e20: stur            w0, [x3, #0xb]
    // 0xab4e24: ldur            x0, [fp, #-0x10]
    // 0xab4e28: StoreField: r3->field_13 = r0
    //     0xab4e28: stur            w0, [x3, #0x13]
    // 0xab4e2c: r1 = Null
    //     0xab4e2c: mov             x1, NULL
    // 0xab4e30: r2 = 6
    //     0xab4e30: movz            x2, #0x6
    // 0xab4e34: r0 = AllocateArray()
    //     0xab4e34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab4e38: mov             x2, x0
    // 0xab4e3c: ldur            x0, [fp, #-0x28]
    // 0xab4e40: stur            x2, [fp, #-8]
    // 0xab4e44: StoreField: r2->field_f = r0
    //     0xab4e44: stur            w0, [x2, #0xf]
    // 0xab4e48: r16 = Instance_Spacer
    //     0xab4e48: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab4e4c: ldr             x16, [x16, #0xf0]
    // 0xab4e50: StoreField: r2->field_13 = r16
    //     0xab4e50: stur            w16, [x2, #0x13]
    // 0xab4e54: ldur            x0, [fp, #-0x18]
    // 0xab4e58: ArrayStore: r2[0] = r0  ; List_4
    //     0xab4e58: stur            w0, [x2, #0x17]
    // 0xab4e5c: r1 = <Widget>
    //     0xab4e5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab4e60: r0 = AllocateGrowableArray()
    //     0xab4e60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab4e64: mov             x1, x0
    // 0xab4e68: ldur            x0, [fp, #-8]
    // 0xab4e6c: stur            x1, [fp, #-0x10]
    // 0xab4e70: StoreField: r1->field_f = r0
    //     0xab4e70: stur            w0, [x1, #0xf]
    // 0xab4e74: r0 = 6
    //     0xab4e74: movz            x0, #0x6
    // 0xab4e78: StoreField: r1->field_b = r0
    //     0xab4e78: stur            w0, [x1, #0xb]
    // 0xab4e7c: r0 = Row()
    //     0xab4e7c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab4e80: r1 = Instance_Axis
    //     0xab4e80: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab4e84: StoreField: r0->field_f = r1
    //     0xab4e84: stur            w1, [x0, #0xf]
    // 0xab4e88: r1 = Instance_MainAxisAlignment
    //     0xab4e88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab4e8c: ldr             x1, [x1, #0xa08]
    // 0xab4e90: StoreField: r0->field_13 = r1
    //     0xab4e90: stur            w1, [x0, #0x13]
    // 0xab4e94: r1 = Instance_MainAxisSize
    //     0xab4e94: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab4e98: ldr             x1, [x1, #0xa10]
    // 0xab4e9c: ArrayStore: r0[0] = r1  ; List_4
    //     0xab4e9c: stur            w1, [x0, #0x17]
    // 0xab4ea0: r1 = Instance_CrossAxisAlignment
    //     0xab4ea0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab4ea4: ldr             x1, [x1, #0xa18]
    // 0xab4ea8: StoreField: r0->field_1b = r1
    //     0xab4ea8: stur            w1, [x0, #0x1b]
    // 0xab4eac: r1 = Instance_VerticalDirection
    //     0xab4eac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab4eb0: ldr             x1, [x1, #0xa20]
    // 0xab4eb4: StoreField: r0->field_23 = r1
    //     0xab4eb4: stur            w1, [x0, #0x23]
    // 0xab4eb8: r1 = Instance_Clip
    //     0xab4eb8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab4ebc: ldr             x1, [x1, #0x38]
    // 0xab4ec0: StoreField: r0->field_2b = r1
    //     0xab4ec0: stur            w1, [x0, #0x2b]
    // 0xab4ec4: StoreField: r0->field_2f = rZR
    //     0xab4ec4: stur            xzr, [x0, #0x2f]
    // 0xab4ec8: ldur            x1, [fp, #-0x10]
    // 0xab4ecc: StoreField: r0->field_b = r1
    //     0xab4ecc: stur            w1, [x0, #0xb]
    // 0xab4ed0: LeaveFrame
    //     0xab4ed0: mov             SP, fp
    //     0xab4ed4: ldp             fp, lr, [SP], #0x10
    // 0xab4ed8: ret
    //     0xab4ed8: ret             
    // 0xab4edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4edc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4ee0: b               #0xab468c
    // 0xab4ee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab4ee4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab4ee8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab4ee8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab4eec: r9 = value
    //     0xab4eec: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6aff8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xab4ef0: ldr             x9, [x9, #0xff8]
    // 0xab4ef4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xab4ef4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xab4ef8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab4ef8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab4efc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab4efc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab4f00: r9 = customisedValue
    //     0xab4f00: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b000] Field <<EMAIL>>: late (offset: 0x18)
    //     0xab4f04: ldr             x9, [x9]
    // 0xab4f08: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xab4f08: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xab4f0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab4f0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4203, size: 0x14, field offset: 0xc
//   const constructor, 
class BagSingleSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7cef0, size: 0x30
    // 0xc7cef0: EnterFrame
    //     0xc7cef0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7cef4: mov             fp, SP
    // 0xc7cef8: mov             x0, x1
    // 0xc7cefc: r1 = <BagSingleSelect>
    //     0xc7cefc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f78] TypeArguments: <BagSingleSelect>
    //     0xc7cf00: ldr             x1, [x1, #0xf78]
    // 0xc7cf04: r0 = _BagSingleSelectState()
    //     0xc7cf04: bl              #0xc7cf20  ; Allocate_BagSingleSelectStateStub -> _BagSingleSelectState (size=0x1c)
    // 0xc7cf08: r1 = Sentinel
    //     0xc7cf08: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7cf0c: StoreField: r0->field_13 = r1
    //     0xc7cf0c: stur            w1, [x0, #0x13]
    // 0xc7cf10: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7cf10: stur            w1, [x0, #0x17]
    // 0xc7cf14: LeaveFrame
    //     0xc7cf14: mov             SP, fp
    //     0xc7cf18: ldp             fp, lr, [SP], #0x10
    // 0xc7cf1c: ret
    //     0xc7cf1c: ret             
  }
}
