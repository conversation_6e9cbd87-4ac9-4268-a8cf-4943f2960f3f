// lib: , url: package:customer_app/app/presentation/views/glass/bag/offers_list_widget.dart

// class id: 1049350, size: 0x8
class :: {
}

// class id: 3375, size: 0x14, field offset: 0x14
class _OffersListWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb389ec, size: 0x4e4
    // 0xb389ec: EnterFrame
    //     0xb389ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb389f0: mov             fp, SP
    // 0xb389f4: AllocStack(0x50)
    //     0xb389f4: sub             SP, SP, #0x50
    // 0xb389f8: SetupParameters(_OffersListWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb389f8: mov             x0, x1
    //     0xb389fc: stur            x1, [fp, #-8]
    //     0xb38a00: mov             x1, x2
    //     0xb38a04: stur            x2, [fp, #-0x10]
    // 0xb38a08: CheckStackOverflow
    //     0xb38a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb38a0c: cmp             SP, x16
    //     0xb38a10: b.ls            #0xb38ec0
    // 0xb38a14: r1 = 1
    //     0xb38a14: movz            x1, #0x1
    // 0xb38a18: r0 = AllocateContext()
    //     0xb38a18: bl              #0x16f6108  ; AllocateContextStub
    // 0xb38a1c: mov             x1, x0
    // 0xb38a20: ldur            x0, [fp, #-8]
    // 0xb38a24: stur            x1, [fp, #-0x30]
    // 0xb38a28: StoreField: r1->field_f = r0
    //     0xb38a28: stur            w0, [x1, #0xf]
    // 0xb38a2c: LoadField: r2 = r0->field_b
    //     0xb38a2c: ldur            w2, [x0, #0xb]
    // 0xb38a30: DecompressPointer r2
    //     0xb38a30: add             x2, x2, HEAP, lsl #32
    // 0xb38a34: stur            x2, [fp, #-0x28]
    // 0xb38a38: cmp             w2, NULL
    // 0xb38a3c: b.eq            #0xb38ec8
    // 0xb38a40: LoadField: r3 = r2->field_23
    //     0xb38a40: ldur            w3, [x2, #0x23]
    // 0xb38a44: DecompressPointer r3
    //     0xb38a44: add             x3, x3, HEAP, lsl #32
    // 0xb38a48: stur            x3, [fp, #-0x20]
    // 0xb38a4c: LoadField: r4 = r2->field_b
    //     0xb38a4c: ldur            w4, [x2, #0xb]
    // 0xb38a50: DecompressPointer r4
    //     0xb38a50: add             x4, x4, HEAP, lsl #32
    // 0xb38a54: cmp             w4, NULL
    // 0xb38a58: b.ne            #0xb38a64
    // 0xb38a5c: r4 = Null
    //     0xb38a5c: mov             x4, NULL
    // 0xb38a60: b               #0xb38a70
    // 0xb38a64: LoadField: r5 = r4->field_f
    //     0xb38a64: ldur            w5, [x4, #0xf]
    // 0xb38a68: DecompressPointer r5
    //     0xb38a68: add             x5, x5, HEAP, lsl #32
    // 0xb38a6c: LoadField: r4 = r5->field_b
    //     0xb38a6c: ldur            w4, [x5, #0xb]
    // 0xb38a70: stur            x4, [fp, #-0x18]
    // 0xb38a74: LoadField: r5 = r2->field_27
    //     0xb38a74: ldur            w5, [x2, #0x27]
    // 0xb38a78: DecompressPointer r5
    //     0xb38a78: add             x5, x5, HEAP, lsl #32
    // 0xb38a7c: r16 = "false"
    //     0xb38a7c: add             x16, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0xb38a80: ldr             x16, [x16, #0xed8]
    // 0xb38a84: stp             x16, x5, [SP]
    // 0xb38a88: r0 = ==()
    //     0xb38a88: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb38a8c: tbnz            w0, #4, #0xb38a98
    // 0xb38a90: r4 = false
    //     0xb38a90: add             x4, NULL, #0x30  ; false
    // 0xb38a94: b               #0xb38a9c
    // 0xb38a98: r4 = true
    //     0xb38a98: add             x4, NULL, #0x20  ; true
    // 0xb38a9c: ldur            x0, [fp, #-8]
    // 0xb38aa0: ldur            x2, [fp, #-0x20]
    // 0xb38aa4: ldur            x3, [fp, #-0x18]
    // 0xb38aa8: ldur            x1, [fp, #-0x28]
    // 0xb38aac: stur            x4, [fp, #-0x38]
    // 0xb38ab0: r0 = EventData()
    //     0xb38ab0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb38ab4: mov             x1, x0
    // 0xb38ab8: ldur            x0, [fp, #-0x20]
    // 0xb38abc: stur            x1, [fp, #-0x40]
    // 0xb38ac0: StoreField: r1->field_87 = r0
    //     0xb38ac0: stur            w0, [x1, #0x87]
    // 0xb38ac4: ldur            x0, [fp, #-0x18]
    // 0xb38ac8: StoreField: r1->field_9b = r0
    //     0xb38ac8: stur            w0, [x1, #0x9b]
    // 0xb38acc: ldur            x0, [fp, #-0x38]
    // 0xb38ad0: StoreField: r1->field_9f = r0
    //     0xb38ad0: stur            w0, [x1, #0x9f]
    // 0xb38ad4: r0 = EventsRequest()
    //     0xb38ad4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb38ad8: mov             x1, x0
    // 0xb38adc: r0 = "offer_clicked"
    //     0xb38adc: add             x0, PP, #0x54, lsl #12  ; [pp+0x54b58] "offer_clicked"
    //     0xb38ae0: ldr             x0, [x0, #0xb58]
    // 0xb38ae4: StoreField: r1->field_7 = r0
    //     0xb38ae4: stur            w0, [x1, #7]
    // 0xb38ae8: ldur            x0, [fp, #-0x40]
    // 0xb38aec: StoreField: r1->field_b = r0
    //     0xb38aec: stur            w0, [x1, #0xb]
    // 0xb38af0: ldur            x0, [fp, #-0x28]
    // 0xb38af4: LoadField: r2 = r0->field_1f
    //     0xb38af4: ldur            w2, [x0, #0x1f]
    // 0xb38af8: DecompressPointer r2
    //     0xb38af8: add             x2, x2, HEAP, lsl #32
    // 0xb38afc: stp             x1, x2, [SP]
    // 0xb38b00: r4 = 0
    //     0xb38b00: movz            x4, #0
    // 0xb38b04: ldr             x0, [SP, #8]
    // 0xb38b08: r5 = UnlinkedCall_0x613b5c
    //     0xb38b08: add             x16, PP, #0x57, lsl #12  ; [pp+0x57198] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb38b0c: ldp             x5, lr, [x16, #0x198]
    // 0xb38b10: blr             lr
    // 0xb38b14: ldur            x1, [fp, #-0x10]
    // 0xb38b18: r0 = of()
    //     0xb38b18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb38b1c: LoadField: r1 = r0->field_87
    //     0xb38b1c: ldur            w1, [x0, #0x87]
    // 0xb38b20: DecompressPointer r1
    //     0xb38b20: add             x1, x1, HEAP, lsl #32
    // 0xb38b24: LoadField: r0 = r1->field_23
    //     0xb38b24: ldur            w0, [x1, #0x23]
    // 0xb38b28: DecompressPointer r0
    //     0xb38b28: add             x0, x0, HEAP, lsl #32
    // 0xb38b2c: r16 = Instance_Color
    //     0xb38b2c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb38b30: r30 = 21.000000
    //     0xb38b30: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb38b34: ldr             lr, [lr, #0x9b0]
    // 0xb38b38: stp             lr, x16, [SP]
    // 0xb38b3c: mov             x1, x0
    // 0xb38b40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb38b40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb38b44: ldr             x4, [x4, #0x9b8]
    // 0xb38b48: r0 = copyWith()
    //     0xb38b48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb38b4c: stur            x0, [fp, #-0x10]
    // 0xb38b50: r0 = Text()
    //     0xb38b50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb38b54: mov             x1, x0
    // 0xb38b58: r0 = "All Offers & Coupons"
    //     0xb38b58: add             x0, PP, #0x57, lsl #12  ; [pp+0x571a8] "All Offers & Coupons"
    //     0xb38b5c: ldr             x0, [x0, #0x1a8]
    // 0xb38b60: stur            x1, [fp, #-0x18]
    // 0xb38b64: StoreField: r1->field_b = r0
    //     0xb38b64: stur            w0, [x1, #0xb]
    // 0xb38b68: ldur            x0, [fp, #-0x10]
    // 0xb38b6c: StoreField: r1->field_13 = r0
    //     0xb38b6c: stur            w0, [x1, #0x13]
    // 0xb38b70: r0 = Padding()
    //     0xb38b70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb38b74: mov             x2, x0
    // 0xb38b78: r0 = Instance_EdgeInsets
    //     0xb38b78: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb38b7c: ldr             x0, [x0, #0xf70]
    // 0xb38b80: stur            x2, [fp, #-0x10]
    // 0xb38b84: StoreField: r2->field_f = r0
    //     0xb38b84: stur            w0, [x2, #0xf]
    // 0xb38b88: ldur            x0, [fp, #-0x18]
    // 0xb38b8c: StoreField: r2->field_b = r0
    //     0xb38b8c: stur            w0, [x2, #0xb]
    // 0xb38b90: r1 = <FlexParentData>
    //     0xb38b90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb38b94: ldr             x1, [x1, #0xe00]
    // 0xb38b98: r0 = Expanded()
    //     0xb38b98: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb38b9c: stur            x0, [fp, #-0x18]
    // 0xb38ba0: StoreField: r0->field_13 = rZR
    //     0xb38ba0: stur            xzr, [x0, #0x13]
    // 0xb38ba4: r1 = Instance_FlexFit
    //     0xb38ba4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb38ba8: ldr             x1, [x1, #0xe08]
    // 0xb38bac: StoreField: r0->field_1b = r1
    //     0xb38bac: stur            w1, [x0, #0x1b]
    // 0xb38bb0: ldur            x2, [fp, #-0x10]
    // 0xb38bb4: StoreField: r0->field_b = r2
    //     0xb38bb4: stur            w2, [x0, #0xb]
    // 0xb38bb8: r0 = InkWell()
    //     0xb38bb8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb38bbc: mov             x3, x0
    // 0xb38bc0: r0 = Instance_Icon
    //     0xb38bc0: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb38bc4: ldr             x0, [x0, #0x2b8]
    // 0xb38bc8: stur            x3, [fp, #-0x10]
    // 0xb38bcc: StoreField: r3->field_b = r0
    //     0xb38bcc: stur            w0, [x3, #0xb]
    // 0xb38bd0: r1 = Function '<anonymous closure>':.
    //     0xb38bd0: add             x1, PP, #0x57, lsl #12  ; [pp+0x571b0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb38bd4: ldr             x1, [x1, #0x1b0]
    // 0xb38bd8: r2 = Null
    //     0xb38bd8: mov             x2, NULL
    // 0xb38bdc: r0 = AllocateClosure()
    //     0xb38bdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb38be0: mov             x1, x0
    // 0xb38be4: ldur            x0, [fp, #-0x10]
    // 0xb38be8: StoreField: r0->field_f = r1
    //     0xb38be8: stur            w1, [x0, #0xf]
    // 0xb38bec: r1 = true
    //     0xb38bec: add             x1, NULL, #0x20  ; true
    // 0xb38bf0: StoreField: r0->field_43 = r1
    //     0xb38bf0: stur            w1, [x0, #0x43]
    // 0xb38bf4: r2 = Instance_BoxShape
    //     0xb38bf4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb38bf8: ldr             x2, [x2, #0x80]
    // 0xb38bfc: StoreField: r0->field_47 = r2
    //     0xb38bfc: stur            w2, [x0, #0x47]
    // 0xb38c00: StoreField: r0->field_6f = r1
    //     0xb38c00: stur            w1, [x0, #0x6f]
    // 0xb38c04: r2 = false
    //     0xb38c04: add             x2, NULL, #0x30  ; false
    // 0xb38c08: StoreField: r0->field_73 = r2
    //     0xb38c08: stur            w2, [x0, #0x73]
    // 0xb38c0c: StoreField: r0->field_83 = r1
    //     0xb38c0c: stur            w1, [x0, #0x83]
    // 0xb38c10: StoreField: r0->field_7b = r2
    //     0xb38c10: stur            w2, [x0, #0x7b]
    // 0xb38c14: r0 = Padding()
    //     0xb38c14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb38c18: mov             x2, x0
    // 0xb38c1c: r0 = Instance_EdgeInsets
    //     0xb38c1c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xb38c20: ldr             x0, [x0, #0x268]
    // 0xb38c24: stur            x2, [fp, #-0x20]
    // 0xb38c28: StoreField: r2->field_f = r0
    //     0xb38c28: stur            w0, [x2, #0xf]
    // 0xb38c2c: ldur            x0, [fp, #-0x10]
    // 0xb38c30: StoreField: r2->field_b = r0
    //     0xb38c30: stur            w0, [x2, #0xb]
    // 0xb38c34: r1 = <FlexParentData>
    //     0xb38c34: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb38c38: ldr             x1, [x1, #0xe00]
    // 0xb38c3c: r0 = Expanded()
    //     0xb38c3c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb38c40: stur            x0, [fp, #-0x10]
    // 0xb38c44: StoreField: r0->field_13 = rZR
    //     0xb38c44: stur            xzr, [x0, #0x13]
    // 0xb38c48: r1 = Instance_FlexFit
    //     0xb38c48: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb38c4c: ldr             x1, [x1, #0xe08]
    // 0xb38c50: StoreField: r0->field_1b = r1
    //     0xb38c50: stur            w1, [x0, #0x1b]
    // 0xb38c54: ldur            x1, [fp, #-0x20]
    // 0xb38c58: StoreField: r0->field_b = r1
    //     0xb38c58: stur            w1, [x0, #0xb]
    // 0xb38c5c: r1 = Null
    //     0xb38c5c: mov             x1, NULL
    // 0xb38c60: r2 = 6
    //     0xb38c60: movz            x2, #0x6
    // 0xb38c64: r0 = AllocateArray()
    //     0xb38c64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb38c68: mov             x2, x0
    // 0xb38c6c: ldur            x0, [fp, #-0x18]
    // 0xb38c70: stur            x2, [fp, #-0x20]
    // 0xb38c74: StoreField: r2->field_f = r0
    //     0xb38c74: stur            w0, [x2, #0xf]
    // 0xb38c78: r16 = Instance_Spacer
    //     0xb38c78: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb38c7c: ldr             x16, [x16, #0xf0]
    // 0xb38c80: StoreField: r2->field_13 = r16
    //     0xb38c80: stur            w16, [x2, #0x13]
    // 0xb38c84: ldur            x0, [fp, #-0x10]
    // 0xb38c88: ArrayStore: r2[0] = r0  ; List_4
    //     0xb38c88: stur            w0, [x2, #0x17]
    // 0xb38c8c: r1 = <Widget>
    //     0xb38c8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb38c90: r0 = AllocateGrowableArray()
    //     0xb38c90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb38c94: mov             x1, x0
    // 0xb38c98: ldur            x0, [fp, #-0x20]
    // 0xb38c9c: stur            x1, [fp, #-0x10]
    // 0xb38ca0: StoreField: r1->field_f = r0
    //     0xb38ca0: stur            w0, [x1, #0xf]
    // 0xb38ca4: r0 = 6
    //     0xb38ca4: movz            x0, #0x6
    // 0xb38ca8: StoreField: r1->field_b = r0
    //     0xb38ca8: stur            w0, [x1, #0xb]
    // 0xb38cac: r0 = Row()
    //     0xb38cac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb38cb0: mov             x3, x0
    // 0xb38cb4: r0 = Instance_Axis
    //     0xb38cb4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb38cb8: stur            x3, [fp, #-0x18]
    // 0xb38cbc: StoreField: r3->field_f = r0
    //     0xb38cbc: stur            w0, [x3, #0xf]
    // 0xb38cc0: r0 = Instance_MainAxisAlignment
    //     0xb38cc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb38cc4: ldr             x0, [x0, #0xa08]
    // 0xb38cc8: StoreField: r3->field_13 = r0
    //     0xb38cc8: stur            w0, [x3, #0x13]
    // 0xb38ccc: r1 = Instance_MainAxisSize
    //     0xb38ccc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb38cd0: ldr             x1, [x1, #0xa10]
    // 0xb38cd4: ArrayStore: r3[0] = r1  ; List_4
    //     0xb38cd4: stur            w1, [x3, #0x17]
    // 0xb38cd8: r4 = Instance_CrossAxisAlignment
    //     0xb38cd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb38cdc: ldr             x4, [x4, #0xa18]
    // 0xb38ce0: StoreField: r3->field_1b = r4
    //     0xb38ce0: stur            w4, [x3, #0x1b]
    // 0xb38ce4: r5 = Instance_VerticalDirection
    //     0xb38ce4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb38ce8: ldr             x5, [x5, #0xa20]
    // 0xb38cec: StoreField: r3->field_23 = r5
    //     0xb38cec: stur            w5, [x3, #0x23]
    // 0xb38cf0: r6 = Instance_Clip
    //     0xb38cf0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb38cf4: ldr             x6, [x6, #0x38]
    // 0xb38cf8: StoreField: r3->field_2b = r6
    //     0xb38cf8: stur            w6, [x3, #0x2b]
    // 0xb38cfc: StoreField: r3->field_2f = rZR
    //     0xb38cfc: stur            xzr, [x3, #0x2f]
    // 0xb38d00: ldur            x1, [fp, #-0x10]
    // 0xb38d04: StoreField: r3->field_b = r1
    //     0xb38d04: stur            w1, [x3, #0xb]
    // 0xb38d08: ldur            x1, [fp, #-8]
    // 0xb38d0c: LoadField: r2 = r1->field_b
    //     0xb38d0c: ldur            w2, [x1, #0xb]
    // 0xb38d10: DecompressPointer r2
    //     0xb38d10: add             x2, x2, HEAP, lsl #32
    // 0xb38d14: cmp             w2, NULL
    // 0xb38d18: b.eq            #0xb38ecc
    // 0xb38d1c: LoadField: r1 = r2->field_b
    //     0xb38d1c: ldur            w1, [x2, #0xb]
    // 0xb38d20: DecompressPointer r1
    //     0xb38d20: add             x1, x1, HEAP, lsl #32
    // 0xb38d24: cmp             w1, NULL
    // 0xb38d28: b.ne            #0xb38d34
    // 0xb38d2c: r7 = Null
    //     0xb38d2c: mov             x7, NULL
    // 0xb38d30: b               #0xb38d44
    // 0xb38d34: LoadField: r2 = r1->field_f
    //     0xb38d34: ldur            w2, [x1, #0xf]
    // 0xb38d38: DecompressPointer r2
    //     0xb38d38: add             x2, x2, HEAP, lsl #32
    // 0xb38d3c: LoadField: r1 = r2->field_b
    //     0xb38d3c: ldur            w1, [x2, #0xb]
    // 0xb38d40: mov             x7, x1
    // 0xb38d44: ldur            x2, [fp, #-0x30]
    // 0xb38d48: stur            x7, [fp, #-8]
    // 0xb38d4c: r1 = Function '<anonymous closure>':.
    //     0xb38d4c: add             x1, PP, #0x57, lsl #12  ; [pp+0x571b8] AnonymousClosure: (0xb38ef4), in [package:customer_app/app/presentation/views/glass/bag/offers_list_widget.dart] _OffersListWidgetState::build (0xb389ec)
    //     0xb38d50: ldr             x1, [x1, #0x1b8]
    // 0xb38d54: r0 = AllocateClosure()
    //     0xb38d54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb38d58: stur            x0, [fp, #-0x10]
    // 0xb38d5c: r0 = ListView()
    //     0xb38d5c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb38d60: stur            x0, [fp, #-0x20]
    // 0xb38d64: r16 = true
    //     0xb38d64: add             x16, NULL, #0x20  ; true
    // 0xb38d68: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb38d68: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb38d6c: ldr             lr, [lr, #0x1c8]
    // 0xb38d70: stp             lr, x16, [SP]
    // 0xb38d74: mov             x1, x0
    // 0xb38d78: ldur            x2, [fp, #-0x10]
    // 0xb38d7c: ldur            x3, [fp, #-8]
    // 0xb38d80: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb38d80: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb38d84: ldr             x4, [x4, #8]
    // 0xb38d88: r0 = ListView.builder()
    //     0xb38d88: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb38d8c: r1 = Null
    //     0xb38d8c: mov             x1, NULL
    // 0xb38d90: r2 = 8
    //     0xb38d90: movz            x2, #0x8
    // 0xb38d94: r0 = AllocateArray()
    //     0xb38d94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb38d98: stur            x0, [fp, #-8]
    // 0xb38d9c: r16 = Instance_SizedBox
    //     0xb38d9c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb38da0: ldr             x16, [x16, #0x578]
    // 0xb38da4: StoreField: r0->field_f = r16
    //     0xb38da4: stur            w16, [x0, #0xf]
    // 0xb38da8: ldur            x1, [fp, #-0x18]
    // 0xb38dac: StoreField: r0->field_13 = r1
    //     0xb38dac: stur            w1, [x0, #0x13]
    // 0xb38db0: r16 = Instance_SizedBox
    //     0xb38db0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb38db4: ldr             x16, [x16, #0x578]
    // 0xb38db8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb38db8: stur            w16, [x0, #0x17]
    // 0xb38dbc: ldur            x1, [fp, #-0x20]
    // 0xb38dc0: StoreField: r0->field_1b = r1
    //     0xb38dc0: stur            w1, [x0, #0x1b]
    // 0xb38dc4: r1 = <Widget>
    //     0xb38dc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb38dc8: r0 = AllocateGrowableArray()
    //     0xb38dc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb38dcc: mov             x1, x0
    // 0xb38dd0: ldur            x0, [fp, #-8]
    // 0xb38dd4: stur            x1, [fp, #-0x10]
    // 0xb38dd8: StoreField: r1->field_f = r0
    //     0xb38dd8: stur            w0, [x1, #0xf]
    // 0xb38ddc: r0 = 8
    //     0xb38ddc: movz            x0, #0x8
    // 0xb38de0: StoreField: r1->field_b = r0
    //     0xb38de0: stur            w0, [x1, #0xb]
    // 0xb38de4: r0 = Column()
    //     0xb38de4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb38de8: mov             x1, x0
    // 0xb38dec: r0 = Instance_Axis
    //     0xb38dec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb38df0: stur            x1, [fp, #-8]
    // 0xb38df4: StoreField: r1->field_f = r0
    //     0xb38df4: stur            w0, [x1, #0xf]
    // 0xb38df8: r2 = Instance_MainAxisAlignment
    //     0xb38df8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb38dfc: ldr             x2, [x2, #0xa08]
    // 0xb38e00: StoreField: r1->field_13 = r2
    //     0xb38e00: stur            w2, [x1, #0x13]
    // 0xb38e04: r2 = Instance_MainAxisSize
    //     0xb38e04: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb38e08: ldr             x2, [x2, #0xdd0]
    // 0xb38e0c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb38e0c: stur            w2, [x1, #0x17]
    // 0xb38e10: r2 = Instance_CrossAxisAlignment
    //     0xb38e10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb38e14: ldr             x2, [x2, #0xa18]
    // 0xb38e18: StoreField: r1->field_1b = r2
    //     0xb38e18: stur            w2, [x1, #0x1b]
    // 0xb38e1c: r2 = Instance_VerticalDirection
    //     0xb38e1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb38e20: ldr             x2, [x2, #0xa20]
    // 0xb38e24: StoreField: r1->field_23 = r2
    //     0xb38e24: stur            w2, [x1, #0x23]
    // 0xb38e28: r2 = Instance_Clip
    //     0xb38e28: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb38e2c: ldr             x2, [x2, #0x38]
    // 0xb38e30: StoreField: r1->field_2b = r2
    //     0xb38e30: stur            w2, [x1, #0x2b]
    // 0xb38e34: StoreField: r1->field_2f = rZR
    //     0xb38e34: stur            xzr, [x1, #0x2f]
    // 0xb38e38: ldur            x2, [fp, #-0x10]
    // 0xb38e3c: StoreField: r1->field_b = r2
    //     0xb38e3c: stur            w2, [x1, #0xb]
    // 0xb38e40: r0 = SingleChildScrollView()
    //     0xb38e40: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb38e44: mov             x1, x0
    // 0xb38e48: r0 = Instance_Axis
    //     0xb38e48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb38e4c: stur            x1, [fp, #-0x10]
    // 0xb38e50: StoreField: r1->field_b = r0
    //     0xb38e50: stur            w0, [x1, #0xb]
    // 0xb38e54: r0 = false
    //     0xb38e54: add             x0, NULL, #0x30  ; false
    // 0xb38e58: StoreField: r1->field_f = r0
    //     0xb38e58: stur            w0, [x1, #0xf]
    // 0xb38e5c: ldur            x2, [fp, #-8]
    // 0xb38e60: StoreField: r1->field_23 = r2
    //     0xb38e60: stur            w2, [x1, #0x23]
    // 0xb38e64: r2 = Instance_DragStartBehavior
    //     0xb38e64: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb38e68: StoreField: r1->field_27 = r2
    //     0xb38e68: stur            w2, [x1, #0x27]
    // 0xb38e6c: r2 = Instance_Clip
    //     0xb38e6c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb38e70: ldr             x2, [x2, #0x7e0]
    // 0xb38e74: StoreField: r1->field_2b = r2
    //     0xb38e74: stur            w2, [x1, #0x2b]
    // 0xb38e78: r2 = Instance_HitTestBehavior
    //     0xb38e78: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb38e7c: ldr             x2, [x2, #0x288]
    // 0xb38e80: StoreField: r1->field_2f = r2
    //     0xb38e80: stur            w2, [x1, #0x2f]
    // 0xb38e84: r0 = SafeArea()
    //     0xb38e84: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb38e88: r1 = true
    //     0xb38e88: add             x1, NULL, #0x20  ; true
    // 0xb38e8c: StoreField: r0->field_b = r1
    //     0xb38e8c: stur            w1, [x0, #0xb]
    // 0xb38e90: StoreField: r0->field_f = r1
    //     0xb38e90: stur            w1, [x0, #0xf]
    // 0xb38e94: StoreField: r0->field_13 = r1
    //     0xb38e94: stur            w1, [x0, #0x13]
    // 0xb38e98: ArrayStore: r0[0] = r1  ; List_4
    //     0xb38e98: stur            w1, [x0, #0x17]
    // 0xb38e9c: r1 = Instance_EdgeInsets
    //     0xb38e9c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb38ea0: StoreField: r0->field_1b = r1
    //     0xb38ea0: stur            w1, [x0, #0x1b]
    // 0xb38ea4: r1 = false
    //     0xb38ea4: add             x1, NULL, #0x30  ; false
    // 0xb38ea8: StoreField: r0->field_1f = r1
    //     0xb38ea8: stur            w1, [x0, #0x1f]
    // 0xb38eac: ldur            x1, [fp, #-0x10]
    // 0xb38eb0: StoreField: r0->field_23 = r1
    //     0xb38eb0: stur            w1, [x0, #0x23]
    // 0xb38eb4: LeaveFrame
    //     0xb38eb4: mov             SP, fp
    //     0xb38eb8: ldp             fp, lr, [SP], #0x10
    // 0xb38ebc: ret
    //     0xb38ebc: ret             
    // 0xb38ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb38ec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb38ec4: b               #0xb38a14
    // 0xb38ec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb38ec8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb38ecc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb38ecc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb38ef4, size: 0x954
    // 0xb38ef4: EnterFrame
    //     0xb38ef4: stp             fp, lr, [SP, #-0x10]!
    //     0xb38ef8: mov             fp, SP
    // 0xb38efc: AllocStack(0x60)
    //     0xb38efc: sub             SP, SP, #0x60
    // 0xb38f00: SetupParameters()
    //     0xb38f00: ldr             x0, [fp, #0x20]
    //     0xb38f04: ldur            w1, [x0, #0x17]
    //     0xb38f08: add             x1, x1, HEAP, lsl #32
    //     0xb38f0c: stur            x1, [fp, #-8]
    // 0xb38f10: CheckStackOverflow
    //     0xb38f10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb38f14: cmp             SP, x16
    //     0xb38f18: b.ls            #0xb397f0
    // 0xb38f1c: r0 = Container()
    //     0xb38f1c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb38f20: mov             x1, x0
    // 0xb38f24: stur            x0, [fp, #-0x10]
    // 0xb38f28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb38f28: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb38f2c: r0 = Container()
    //     0xb38f2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb38f30: ldur            x2, [fp, #-8]
    // 0xb38f34: LoadField: r0 = r2->field_f
    //     0xb38f34: ldur            w0, [x2, #0xf]
    // 0xb38f38: DecompressPointer r0
    //     0xb38f38: add             x0, x0, HEAP, lsl #32
    // 0xb38f3c: LoadField: r1 = r0->field_b
    //     0xb38f3c: ldur            w1, [x0, #0xb]
    // 0xb38f40: DecompressPointer r1
    //     0xb38f40: add             x1, x1, HEAP, lsl #32
    // 0xb38f44: cmp             w1, NULL
    // 0xb38f48: b.eq            #0xb397f8
    // 0xb38f4c: LoadField: r0 = r1->field_b
    //     0xb38f4c: ldur            w0, [x1, #0xb]
    // 0xb38f50: DecompressPointer r0
    //     0xb38f50: add             x0, x0, HEAP, lsl #32
    // 0xb38f54: cmp             w0, NULL
    // 0xb38f58: b.ne            #0xb38f68
    // 0xb38f5c: ldr             x3, [fp, #0x10]
    // 0xb38f60: r0 = Null
    //     0xb38f60: mov             x0, NULL
    // 0xb38f64: b               #0xb38fb4
    // 0xb38f68: ldr             x3, [fp, #0x10]
    // 0xb38f6c: LoadField: r4 = r0->field_f
    //     0xb38f6c: ldur            w4, [x0, #0xf]
    // 0xb38f70: DecompressPointer r4
    //     0xb38f70: add             x4, x4, HEAP, lsl #32
    // 0xb38f74: LoadField: r0 = r4->field_b
    //     0xb38f74: ldur            w0, [x4, #0xb]
    // 0xb38f78: r5 = LoadInt32Instr(r3)
    //     0xb38f78: sbfx            x5, x3, #1, #0x1f
    //     0xb38f7c: tbz             w3, #0, #0xb38f84
    //     0xb38f80: ldur            x5, [x3, #7]
    // 0xb38f84: r1 = LoadInt32Instr(r0)
    //     0xb38f84: sbfx            x1, x0, #1, #0x1f
    // 0xb38f88: mov             x0, x1
    // 0xb38f8c: mov             x1, x5
    // 0xb38f90: cmp             x1, x0
    // 0xb38f94: b.hs            #0xb397fc
    // 0xb38f98: LoadField: r0 = r4->field_f
    //     0xb38f98: ldur            w0, [x4, #0xf]
    // 0xb38f9c: DecompressPointer r0
    //     0xb38f9c: add             x0, x0, HEAP, lsl #32
    // 0xb38fa0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb38fa0: add             x16, x0, x5, lsl #2
    //     0xb38fa4: ldur            w1, [x16, #0xf]
    // 0xb38fa8: DecompressPointer r1
    //     0xb38fa8: add             x1, x1, HEAP, lsl #32
    // 0xb38fac: LoadField: r0 = r1->field_7
    //     0xb38fac: ldur            w0, [x1, #7]
    // 0xb38fb0: DecompressPointer r0
    //     0xb38fb0: add             x0, x0, HEAP, lsl #32
    // 0xb38fb4: r1 = LoadClassIdInstr(r0)
    //     0xb38fb4: ldur            x1, [x0, #-1]
    //     0xb38fb8: ubfx            x1, x1, #0xc, #0x14
    // 0xb38fbc: r16 = "special_offers"
    //     0xb38fbc: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c0] "special_offers"
    //     0xb38fc0: ldr             x16, [x16, #0x1c0]
    // 0xb38fc4: stp             x16, x0, [SP]
    // 0xb38fc8: mov             x0, x1
    // 0xb38fcc: mov             lr, x0
    // 0xb38fd0: ldr             lr, [x21, lr, lsl #3]
    // 0xb38fd4: blr             lr
    // 0xb38fd8: tbnz            w0, #4, #0xb39158
    // 0xb38fdc: ldur            x2, [fp, #-8]
    // 0xb38fe0: LoadField: r0 = r2->field_f
    //     0xb38fe0: ldur            w0, [x2, #0xf]
    // 0xb38fe4: DecompressPointer r0
    //     0xb38fe4: add             x0, x0, HEAP, lsl #32
    // 0xb38fe8: LoadField: r3 = r0->field_b
    //     0xb38fe8: ldur            w3, [x0, #0xb]
    // 0xb38fec: DecompressPointer r3
    //     0xb38fec: add             x3, x3, HEAP, lsl #32
    // 0xb38ff0: cmp             w3, NULL
    // 0xb38ff4: b.eq            #0xb39800
    // 0xb38ff8: LoadField: r4 = r3->field_b
    //     0xb38ff8: ldur            w4, [x3, #0xb]
    // 0xb38ffc: DecompressPointer r4
    //     0xb38ffc: add             x4, x4, HEAP, lsl #32
    // 0xb39000: cmp             w4, NULL
    // 0xb39004: b.ne            #0xb39014
    // 0xb39008: ldr             x5, [fp, #0x10]
    // 0xb3900c: r6 = Null
    //     0xb3900c: mov             x6, NULL
    // 0xb39010: b               #0xb39064
    // 0xb39014: ldr             x5, [fp, #0x10]
    // 0xb39018: LoadField: r6 = r4->field_f
    //     0xb39018: ldur            w6, [x4, #0xf]
    // 0xb3901c: DecompressPointer r6
    //     0xb3901c: add             x6, x6, HEAP, lsl #32
    // 0xb39020: LoadField: r0 = r6->field_b
    //     0xb39020: ldur            w0, [x6, #0xb]
    // 0xb39024: r7 = LoadInt32Instr(r5)
    //     0xb39024: sbfx            x7, x5, #1, #0x1f
    //     0xb39028: tbz             w5, #0, #0xb39030
    //     0xb3902c: ldur            x7, [x5, #7]
    // 0xb39030: r1 = LoadInt32Instr(r0)
    //     0xb39030: sbfx            x1, x0, #1, #0x1f
    // 0xb39034: mov             x0, x1
    // 0xb39038: mov             x1, x7
    // 0xb3903c: cmp             x1, x0
    // 0xb39040: b.hs            #0xb39804
    // 0xb39044: LoadField: r0 = r6->field_f
    //     0xb39044: ldur            w0, [x6, #0xf]
    // 0xb39048: DecompressPointer r0
    //     0xb39048: add             x0, x0, HEAP, lsl #32
    // 0xb3904c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb3904c: add             x16, x0, x7, lsl #2
    //     0xb39050: ldur            w1, [x16, #0xf]
    // 0xb39054: DecompressPointer r1
    //     0xb39054: add             x1, x1, HEAP, lsl #32
    // 0xb39058: LoadField: r0 = r1->field_7
    //     0xb39058: ldur            w0, [x1, #7]
    // 0xb3905c: DecompressPointer r0
    //     0xb3905c: add             x0, x0, HEAP, lsl #32
    // 0xb39060: mov             x6, x0
    // 0xb39064: stur            x6, [fp, #-0x50]
    // 0xb39068: cmp             w4, NULL
    // 0xb3906c: b.ne            #0xb39078
    // 0xb39070: r0 = Null
    //     0xb39070: mov             x0, NULL
    // 0xb39074: b               #0xb390bc
    // 0xb39078: LoadField: r7 = r4->field_f
    //     0xb39078: ldur            w7, [x4, #0xf]
    // 0xb3907c: DecompressPointer r7
    //     0xb3907c: add             x7, x7, HEAP, lsl #32
    // 0xb39080: LoadField: r0 = r7->field_b
    //     0xb39080: ldur            w0, [x7, #0xb]
    // 0xb39084: r4 = LoadInt32Instr(r5)
    //     0xb39084: sbfx            x4, x5, #1, #0x1f
    //     0xb39088: tbz             w5, #0, #0xb39090
    //     0xb3908c: ldur            x4, [x5, #7]
    // 0xb39090: r1 = LoadInt32Instr(r0)
    //     0xb39090: sbfx            x1, x0, #1, #0x1f
    // 0xb39094: mov             x0, x1
    // 0xb39098: mov             x1, x4
    // 0xb3909c: cmp             x1, x0
    // 0xb390a0: b.hs            #0xb39808
    // 0xb390a4: LoadField: r0 = r7->field_f
    //     0xb390a4: ldur            w0, [x7, #0xf]
    // 0xb390a8: DecompressPointer r0
    //     0xb390a8: add             x0, x0, HEAP, lsl #32
    // 0xb390ac: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb390ac: add             x16, x0, x4, lsl #2
    //     0xb390b0: ldur            w1, [x16, #0xf]
    // 0xb390b4: DecompressPointer r1
    //     0xb390b4: add             x1, x1, HEAP, lsl #32
    // 0xb390b8: mov             x0, x1
    // 0xb390bc: stur            x0, [fp, #-0x48]
    // 0xb390c0: LoadField: r1 = r3->field_f
    //     0xb390c0: ldur            w1, [x3, #0xf]
    // 0xb390c4: DecompressPointer r1
    //     0xb390c4: add             x1, x1, HEAP, lsl #32
    // 0xb390c8: stur            x1, [fp, #-0x40]
    // 0xb390cc: LoadField: r4 = r3->field_13
    //     0xb390cc: ldur            w4, [x3, #0x13]
    // 0xb390d0: DecompressPointer r4
    //     0xb390d0: add             x4, x4, HEAP, lsl #32
    // 0xb390d4: stur            x4, [fp, #-0x38]
    // 0xb390d8: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xb390d8: ldur            w7, [x3, #0x17]
    // 0xb390dc: DecompressPointer r7
    //     0xb390dc: add             x7, x7, HEAP, lsl #32
    // 0xb390e0: stur            x7, [fp, #-0x30]
    // 0xb390e4: LoadField: r8 = r3->field_1b
    //     0xb390e4: ldur            w8, [x3, #0x1b]
    // 0xb390e8: DecompressPointer r8
    //     0xb390e8: add             x8, x8, HEAP, lsl #32
    // 0xb390ec: stur            x8, [fp, #-0x28]
    // 0xb390f0: LoadField: r9 = r3->field_2b
    //     0xb390f0: ldur            w9, [x3, #0x2b]
    // 0xb390f4: DecompressPointer r9
    //     0xb390f4: add             x9, x9, HEAP, lsl #32
    // 0xb390f8: stur            x9, [fp, #-0x20]
    // 0xb390fc: LoadField: r10 = r3->field_2f
    //     0xb390fc: ldur            w10, [x3, #0x2f]
    // 0xb39100: DecompressPointer r10
    //     0xb39100: add             x10, x10, HEAP, lsl #32
    // 0xb39104: stur            x10, [fp, #-0x18]
    // 0xb39108: r0 = OffersContentItemView()
    //     0xb39108: bl              #0xb39848  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xb3910c: mov             x1, x0
    // 0xb39110: ldur            x0, [fp, #-0x50]
    // 0xb39114: StoreField: r1->field_b = r0
    //     0xb39114: stur            w0, [x1, #0xb]
    // 0xb39118: ldur            x0, [fp, #-0x48]
    // 0xb3911c: StoreField: r1->field_f = r0
    //     0xb3911c: stur            w0, [x1, #0xf]
    // 0xb39120: ldur            x0, [fp, #-0x40]
    // 0xb39124: StoreField: r1->field_13 = r0
    //     0xb39124: stur            w0, [x1, #0x13]
    // 0xb39128: ldur            x0, [fp, #-0x38]
    // 0xb3912c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3912c: stur            w0, [x1, #0x17]
    // 0xb39130: ldur            x0, [fp, #-0x30]
    // 0xb39134: StoreField: r1->field_1b = r0
    //     0xb39134: stur            w0, [x1, #0x1b]
    // 0xb39138: ldur            x0, [fp, #-0x28]
    // 0xb3913c: StoreField: r1->field_1f = r0
    //     0xb3913c: stur            w0, [x1, #0x1f]
    // 0xb39140: ldur            x0, [fp, #-0x20]
    // 0xb39144: StoreField: r1->field_23 = r0
    //     0xb39144: stur            w0, [x1, #0x23]
    // 0xb39148: ldur            x0, [fp, #-0x18]
    // 0xb3914c: StoreField: r1->field_27 = r0
    //     0xb3914c: stur            w0, [x1, #0x27]
    // 0xb39150: mov             x3, x1
    // 0xb39154: b               #0xb3915c
    // 0xb39158: ldur            x3, [fp, #-0x10]
    // 0xb3915c: ldur            x2, [fp, #-8]
    // 0xb39160: stur            x3, [fp, #-0x10]
    // 0xb39164: LoadField: r0 = r2->field_f
    //     0xb39164: ldur            w0, [x2, #0xf]
    // 0xb39168: DecompressPointer r0
    //     0xb39168: add             x0, x0, HEAP, lsl #32
    // 0xb3916c: LoadField: r1 = r0->field_b
    //     0xb3916c: ldur            w1, [x0, #0xb]
    // 0xb39170: DecompressPointer r1
    //     0xb39170: add             x1, x1, HEAP, lsl #32
    // 0xb39174: cmp             w1, NULL
    // 0xb39178: b.eq            #0xb3980c
    // 0xb3917c: LoadField: r0 = r1->field_b
    //     0xb3917c: ldur            w0, [x1, #0xb]
    // 0xb39180: DecompressPointer r0
    //     0xb39180: add             x0, x0, HEAP, lsl #32
    // 0xb39184: cmp             w0, NULL
    // 0xb39188: b.ne            #0xb39198
    // 0xb3918c: ldr             x4, [fp, #0x10]
    // 0xb39190: r0 = Null
    //     0xb39190: mov             x0, NULL
    // 0xb39194: b               #0xb391e4
    // 0xb39198: ldr             x4, [fp, #0x10]
    // 0xb3919c: LoadField: r5 = r0->field_f
    //     0xb3919c: ldur            w5, [x0, #0xf]
    // 0xb391a0: DecompressPointer r5
    //     0xb391a0: add             x5, x5, HEAP, lsl #32
    // 0xb391a4: LoadField: r0 = r5->field_b
    //     0xb391a4: ldur            w0, [x5, #0xb]
    // 0xb391a8: r6 = LoadInt32Instr(r4)
    //     0xb391a8: sbfx            x6, x4, #1, #0x1f
    //     0xb391ac: tbz             w4, #0, #0xb391b4
    //     0xb391b0: ldur            x6, [x4, #7]
    // 0xb391b4: r1 = LoadInt32Instr(r0)
    //     0xb391b4: sbfx            x1, x0, #1, #0x1f
    // 0xb391b8: mov             x0, x1
    // 0xb391bc: mov             x1, x6
    // 0xb391c0: cmp             x1, x0
    // 0xb391c4: b.hs            #0xb39810
    // 0xb391c8: LoadField: r0 = r5->field_f
    //     0xb391c8: ldur            w0, [x5, #0xf]
    // 0xb391cc: DecompressPointer r0
    //     0xb391cc: add             x0, x0, HEAP, lsl #32
    // 0xb391d0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb391d0: add             x16, x0, x6, lsl #2
    //     0xb391d4: ldur            w1, [x16, #0xf]
    // 0xb391d8: DecompressPointer r1
    //     0xb391d8: add             x1, x1, HEAP, lsl #32
    // 0xb391dc: LoadField: r0 = r1->field_7
    //     0xb391dc: ldur            w0, [x1, #7]
    // 0xb391e0: DecompressPointer r0
    //     0xb391e0: add             x0, x0, HEAP, lsl #32
    // 0xb391e4: r1 = LoadClassIdInstr(r0)
    //     0xb391e4: ldur            x1, [x0, #-1]
    //     0xb391e8: ubfx            x1, x1, #0xc, #0x14
    // 0xb391ec: r16 = "coupons"
    //     0xb391ec: add             x16, PP, #0x25, lsl #12  ; [pp+0x25128] "coupons"
    //     0xb391f0: ldr             x16, [x16, #0x128]
    // 0xb391f4: stp             x16, x0, [SP]
    // 0xb391f8: mov             x0, x1
    // 0xb391fc: mov             lr, x0
    // 0xb39200: ldr             lr, [x21, lr, lsl #3]
    // 0xb39204: blr             lr
    // 0xb39208: tbnz            w0, #4, #0xb39388
    // 0xb3920c: ldur            x2, [fp, #-8]
    // 0xb39210: LoadField: r0 = r2->field_f
    //     0xb39210: ldur            w0, [x2, #0xf]
    // 0xb39214: DecompressPointer r0
    //     0xb39214: add             x0, x0, HEAP, lsl #32
    // 0xb39218: LoadField: r3 = r0->field_b
    //     0xb39218: ldur            w3, [x0, #0xb]
    // 0xb3921c: DecompressPointer r3
    //     0xb3921c: add             x3, x3, HEAP, lsl #32
    // 0xb39220: cmp             w3, NULL
    // 0xb39224: b.eq            #0xb39814
    // 0xb39228: LoadField: r4 = r3->field_b
    //     0xb39228: ldur            w4, [x3, #0xb]
    // 0xb3922c: DecompressPointer r4
    //     0xb3922c: add             x4, x4, HEAP, lsl #32
    // 0xb39230: cmp             w4, NULL
    // 0xb39234: b.ne            #0xb39244
    // 0xb39238: ldr             x5, [fp, #0x10]
    // 0xb3923c: r6 = Null
    //     0xb3923c: mov             x6, NULL
    // 0xb39240: b               #0xb39294
    // 0xb39244: ldr             x5, [fp, #0x10]
    // 0xb39248: LoadField: r6 = r4->field_f
    //     0xb39248: ldur            w6, [x4, #0xf]
    // 0xb3924c: DecompressPointer r6
    //     0xb3924c: add             x6, x6, HEAP, lsl #32
    // 0xb39250: LoadField: r0 = r6->field_b
    //     0xb39250: ldur            w0, [x6, #0xb]
    // 0xb39254: r7 = LoadInt32Instr(r5)
    //     0xb39254: sbfx            x7, x5, #1, #0x1f
    //     0xb39258: tbz             w5, #0, #0xb39260
    //     0xb3925c: ldur            x7, [x5, #7]
    // 0xb39260: r1 = LoadInt32Instr(r0)
    //     0xb39260: sbfx            x1, x0, #1, #0x1f
    // 0xb39264: mov             x0, x1
    // 0xb39268: mov             x1, x7
    // 0xb3926c: cmp             x1, x0
    // 0xb39270: b.hs            #0xb39818
    // 0xb39274: LoadField: r0 = r6->field_f
    //     0xb39274: ldur            w0, [x6, #0xf]
    // 0xb39278: DecompressPointer r0
    //     0xb39278: add             x0, x0, HEAP, lsl #32
    // 0xb3927c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb3927c: add             x16, x0, x7, lsl #2
    //     0xb39280: ldur            w1, [x16, #0xf]
    // 0xb39284: DecompressPointer r1
    //     0xb39284: add             x1, x1, HEAP, lsl #32
    // 0xb39288: LoadField: r0 = r1->field_7
    //     0xb39288: ldur            w0, [x1, #7]
    // 0xb3928c: DecompressPointer r0
    //     0xb3928c: add             x0, x0, HEAP, lsl #32
    // 0xb39290: mov             x6, x0
    // 0xb39294: stur            x6, [fp, #-0x50]
    // 0xb39298: cmp             w4, NULL
    // 0xb3929c: b.ne            #0xb392a8
    // 0xb392a0: r0 = Null
    //     0xb392a0: mov             x0, NULL
    // 0xb392a4: b               #0xb392ec
    // 0xb392a8: LoadField: r7 = r4->field_f
    //     0xb392a8: ldur            w7, [x4, #0xf]
    // 0xb392ac: DecompressPointer r7
    //     0xb392ac: add             x7, x7, HEAP, lsl #32
    // 0xb392b0: LoadField: r0 = r7->field_b
    //     0xb392b0: ldur            w0, [x7, #0xb]
    // 0xb392b4: r4 = LoadInt32Instr(r5)
    //     0xb392b4: sbfx            x4, x5, #1, #0x1f
    //     0xb392b8: tbz             w5, #0, #0xb392c0
    //     0xb392bc: ldur            x4, [x5, #7]
    // 0xb392c0: r1 = LoadInt32Instr(r0)
    //     0xb392c0: sbfx            x1, x0, #1, #0x1f
    // 0xb392c4: mov             x0, x1
    // 0xb392c8: mov             x1, x4
    // 0xb392cc: cmp             x1, x0
    // 0xb392d0: b.hs            #0xb3981c
    // 0xb392d4: LoadField: r0 = r7->field_f
    //     0xb392d4: ldur            w0, [x7, #0xf]
    // 0xb392d8: DecompressPointer r0
    //     0xb392d8: add             x0, x0, HEAP, lsl #32
    // 0xb392dc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb392dc: add             x16, x0, x4, lsl #2
    //     0xb392e0: ldur            w1, [x16, #0xf]
    // 0xb392e4: DecompressPointer r1
    //     0xb392e4: add             x1, x1, HEAP, lsl #32
    // 0xb392e8: mov             x0, x1
    // 0xb392ec: stur            x0, [fp, #-0x48]
    // 0xb392f0: LoadField: r1 = r3->field_f
    //     0xb392f0: ldur            w1, [x3, #0xf]
    // 0xb392f4: DecompressPointer r1
    //     0xb392f4: add             x1, x1, HEAP, lsl #32
    // 0xb392f8: stur            x1, [fp, #-0x40]
    // 0xb392fc: LoadField: r4 = r3->field_13
    //     0xb392fc: ldur            w4, [x3, #0x13]
    // 0xb39300: DecompressPointer r4
    //     0xb39300: add             x4, x4, HEAP, lsl #32
    // 0xb39304: stur            x4, [fp, #-0x38]
    // 0xb39308: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xb39308: ldur            w7, [x3, #0x17]
    // 0xb3930c: DecompressPointer r7
    //     0xb3930c: add             x7, x7, HEAP, lsl #32
    // 0xb39310: stur            x7, [fp, #-0x30]
    // 0xb39314: LoadField: r8 = r3->field_1b
    //     0xb39314: ldur            w8, [x3, #0x1b]
    // 0xb39318: DecompressPointer r8
    //     0xb39318: add             x8, x8, HEAP, lsl #32
    // 0xb3931c: stur            x8, [fp, #-0x28]
    // 0xb39320: LoadField: r9 = r3->field_2b
    //     0xb39320: ldur            w9, [x3, #0x2b]
    // 0xb39324: DecompressPointer r9
    //     0xb39324: add             x9, x9, HEAP, lsl #32
    // 0xb39328: stur            x9, [fp, #-0x20]
    // 0xb3932c: LoadField: r10 = r3->field_2f
    //     0xb3932c: ldur            w10, [x3, #0x2f]
    // 0xb39330: DecompressPointer r10
    //     0xb39330: add             x10, x10, HEAP, lsl #32
    // 0xb39334: stur            x10, [fp, #-0x18]
    // 0xb39338: r0 = OffersContentItemView()
    //     0xb39338: bl              #0xb39848  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xb3933c: mov             x1, x0
    // 0xb39340: ldur            x0, [fp, #-0x50]
    // 0xb39344: StoreField: r1->field_b = r0
    //     0xb39344: stur            w0, [x1, #0xb]
    // 0xb39348: ldur            x0, [fp, #-0x48]
    // 0xb3934c: StoreField: r1->field_f = r0
    //     0xb3934c: stur            w0, [x1, #0xf]
    // 0xb39350: ldur            x0, [fp, #-0x40]
    // 0xb39354: StoreField: r1->field_13 = r0
    //     0xb39354: stur            w0, [x1, #0x13]
    // 0xb39358: ldur            x0, [fp, #-0x38]
    // 0xb3935c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3935c: stur            w0, [x1, #0x17]
    // 0xb39360: ldur            x0, [fp, #-0x30]
    // 0xb39364: StoreField: r1->field_1b = r0
    //     0xb39364: stur            w0, [x1, #0x1b]
    // 0xb39368: ldur            x0, [fp, #-0x28]
    // 0xb3936c: StoreField: r1->field_1f = r0
    //     0xb3936c: stur            w0, [x1, #0x1f]
    // 0xb39370: ldur            x0, [fp, #-0x20]
    // 0xb39374: StoreField: r1->field_23 = r0
    //     0xb39374: stur            w0, [x1, #0x23]
    // 0xb39378: ldur            x0, [fp, #-0x18]
    // 0xb3937c: StoreField: r1->field_27 = r0
    //     0xb3937c: stur            w0, [x1, #0x27]
    // 0xb39380: mov             x3, x1
    // 0xb39384: b               #0xb3938c
    // 0xb39388: ldur            x3, [fp, #-0x10]
    // 0xb3938c: ldur            x2, [fp, #-8]
    // 0xb39390: stur            x3, [fp, #-0x10]
    // 0xb39394: LoadField: r0 = r2->field_f
    //     0xb39394: ldur            w0, [x2, #0xf]
    // 0xb39398: DecompressPointer r0
    //     0xb39398: add             x0, x0, HEAP, lsl #32
    // 0xb3939c: LoadField: r1 = r0->field_b
    //     0xb3939c: ldur            w1, [x0, #0xb]
    // 0xb393a0: DecompressPointer r1
    //     0xb393a0: add             x1, x1, HEAP, lsl #32
    // 0xb393a4: cmp             w1, NULL
    // 0xb393a8: b.eq            #0xb39820
    // 0xb393ac: LoadField: r0 = r1->field_b
    //     0xb393ac: ldur            w0, [x1, #0xb]
    // 0xb393b0: DecompressPointer r0
    //     0xb393b0: add             x0, x0, HEAP, lsl #32
    // 0xb393b4: cmp             w0, NULL
    // 0xb393b8: b.ne            #0xb393c8
    // 0xb393bc: ldr             x4, [fp, #0x10]
    // 0xb393c0: r0 = Null
    //     0xb393c0: mov             x0, NULL
    // 0xb393c4: b               #0xb39414
    // 0xb393c8: ldr             x4, [fp, #0x10]
    // 0xb393cc: LoadField: r5 = r0->field_f
    //     0xb393cc: ldur            w5, [x0, #0xf]
    // 0xb393d0: DecompressPointer r5
    //     0xb393d0: add             x5, x5, HEAP, lsl #32
    // 0xb393d4: LoadField: r0 = r5->field_b
    //     0xb393d4: ldur            w0, [x5, #0xb]
    // 0xb393d8: r6 = LoadInt32Instr(r4)
    //     0xb393d8: sbfx            x6, x4, #1, #0x1f
    //     0xb393dc: tbz             w4, #0, #0xb393e4
    //     0xb393e0: ldur            x6, [x4, #7]
    // 0xb393e4: r1 = LoadInt32Instr(r0)
    //     0xb393e4: sbfx            x1, x0, #1, #0x1f
    // 0xb393e8: mov             x0, x1
    // 0xb393ec: mov             x1, x6
    // 0xb393f0: cmp             x1, x0
    // 0xb393f4: b.hs            #0xb39824
    // 0xb393f8: LoadField: r0 = r5->field_f
    //     0xb393f8: ldur            w0, [x5, #0xf]
    // 0xb393fc: DecompressPointer r0
    //     0xb393fc: add             x0, x0, HEAP, lsl #32
    // 0xb39400: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb39400: add             x16, x0, x6, lsl #2
    //     0xb39404: ldur            w1, [x16, #0xf]
    // 0xb39408: DecompressPointer r1
    //     0xb39408: add             x1, x1, HEAP, lsl #32
    // 0xb3940c: LoadField: r0 = r1->field_7
    //     0xb3940c: ldur            w0, [x1, #7]
    // 0xb39410: DecompressPointer r0
    //     0xb39410: add             x0, x0, HEAP, lsl #32
    // 0xb39414: r1 = LoadClassIdInstr(r0)
    //     0xb39414: ldur            x1, [x0, #-1]
    //     0xb39418: ubfx            x1, x1, #0xc, #0x14
    // 0xb3941c: r16 = "checkout_offers"
    //     0xb3941c: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xb39420: ldr             x16, [x16, #0x1c8]
    // 0xb39424: stp             x16, x0, [SP]
    // 0xb39428: mov             x0, x1
    // 0xb3942c: mov             lr, x0
    // 0xb39430: ldr             lr, [x21, lr, lsl #3]
    // 0xb39434: blr             lr
    // 0xb39438: tbnz            w0, #4, #0xb395b8
    // 0xb3943c: ldur            x2, [fp, #-8]
    // 0xb39440: LoadField: r0 = r2->field_f
    //     0xb39440: ldur            w0, [x2, #0xf]
    // 0xb39444: DecompressPointer r0
    //     0xb39444: add             x0, x0, HEAP, lsl #32
    // 0xb39448: LoadField: r3 = r0->field_b
    //     0xb39448: ldur            w3, [x0, #0xb]
    // 0xb3944c: DecompressPointer r3
    //     0xb3944c: add             x3, x3, HEAP, lsl #32
    // 0xb39450: cmp             w3, NULL
    // 0xb39454: b.eq            #0xb39828
    // 0xb39458: LoadField: r4 = r3->field_b
    //     0xb39458: ldur            w4, [x3, #0xb]
    // 0xb3945c: DecompressPointer r4
    //     0xb3945c: add             x4, x4, HEAP, lsl #32
    // 0xb39460: cmp             w4, NULL
    // 0xb39464: b.ne            #0xb39474
    // 0xb39468: ldr             x5, [fp, #0x10]
    // 0xb3946c: r6 = Null
    //     0xb3946c: mov             x6, NULL
    // 0xb39470: b               #0xb394c4
    // 0xb39474: ldr             x5, [fp, #0x10]
    // 0xb39478: LoadField: r6 = r4->field_f
    //     0xb39478: ldur            w6, [x4, #0xf]
    // 0xb3947c: DecompressPointer r6
    //     0xb3947c: add             x6, x6, HEAP, lsl #32
    // 0xb39480: LoadField: r0 = r6->field_b
    //     0xb39480: ldur            w0, [x6, #0xb]
    // 0xb39484: r7 = LoadInt32Instr(r5)
    //     0xb39484: sbfx            x7, x5, #1, #0x1f
    //     0xb39488: tbz             w5, #0, #0xb39490
    //     0xb3948c: ldur            x7, [x5, #7]
    // 0xb39490: r1 = LoadInt32Instr(r0)
    //     0xb39490: sbfx            x1, x0, #1, #0x1f
    // 0xb39494: mov             x0, x1
    // 0xb39498: mov             x1, x7
    // 0xb3949c: cmp             x1, x0
    // 0xb394a0: b.hs            #0xb3982c
    // 0xb394a4: LoadField: r0 = r6->field_f
    //     0xb394a4: ldur            w0, [x6, #0xf]
    // 0xb394a8: DecompressPointer r0
    //     0xb394a8: add             x0, x0, HEAP, lsl #32
    // 0xb394ac: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb394ac: add             x16, x0, x7, lsl #2
    //     0xb394b0: ldur            w1, [x16, #0xf]
    // 0xb394b4: DecompressPointer r1
    //     0xb394b4: add             x1, x1, HEAP, lsl #32
    // 0xb394b8: LoadField: r0 = r1->field_7
    //     0xb394b8: ldur            w0, [x1, #7]
    // 0xb394bc: DecompressPointer r0
    //     0xb394bc: add             x0, x0, HEAP, lsl #32
    // 0xb394c0: mov             x6, x0
    // 0xb394c4: stur            x6, [fp, #-0x50]
    // 0xb394c8: cmp             w4, NULL
    // 0xb394cc: b.ne            #0xb394d8
    // 0xb394d0: r0 = Null
    //     0xb394d0: mov             x0, NULL
    // 0xb394d4: b               #0xb3951c
    // 0xb394d8: LoadField: r7 = r4->field_f
    //     0xb394d8: ldur            w7, [x4, #0xf]
    // 0xb394dc: DecompressPointer r7
    //     0xb394dc: add             x7, x7, HEAP, lsl #32
    // 0xb394e0: LoadField: r0 = r7->field_b
    //     0xb394e0: ldur            w0, [x7, #0xb]
    // 0xb394e4: r4 = LoadInt32Instr(r5)
    //     0xb394e4: sbfx            x4, x5, #1, #0x1f
    //     0xb394e8: tbz             w5, #0, #0xb394f0
    //     0xb394ec: ldur            x4, [x5, #7]
    // 0xb394f0: r1 = LoadInt32Instr(r0)
    //     0xb394f0: sbfx            x1, x0, #1, #0x1f
    // 0xb394f4: mov             x0, x1
    // 0xb394f8: mov             x1, x4
    // 0xb394fc: cmp             x1, x0
    // 0xb39500: b.hs            #0xb39830
    // 0xb39504: LoadField: r0 = r7->field_f
    //     0xb39504: ldur            w0, [x7, #0xf]
    // 0xb39508: DecompressPointer r0
    //     0xb39508: add             x0, x0, HEAP, lsl #32
    // 0xb3950c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb3950c: add             x16, x0, x4, lsl #2
    //     0xb39510: ldur            w1, [x16, #0xf]
    // 0xb39514: DecompressPointer r1
    //     0xb39514: add             x1, x1, HEAP, lsl #32
    // 0xb39518: mov             x0, x1
    // 0xb3951c: stur            x0, [fp, #-0x48]
    // 0xb39520: LoadField: r1 = r3->field_f
    //     0xb39520: ldur            w1, [x3, #0xf]
    // 0xb39524: DecompressPointer r1
    //     0xb39524: add             x1, x1, HEAP, lsl #32
    // 0xb39528: stur            x1, [fp, #-0x40]
    // 0xb3952c: LoadField: r4 = r3->field_13
    //     0xb3952c: ldur            w4, [x3, #0x13]
    // 0xb39530: DecompressPointer r4
    //     0xb39530: add             x4, x4, HEAP, lsl #32
    // 0xb39534: stur            x4, [fp, #-0x38]
    // 0xb39538: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xb39538: ldur            w7, [x3, #0x17]
    // 0xb3953c: DecompressPointer r7
    //     0xb3953c: add             x7, x7, HEAP, lsl #32
    // 0xb39540: stur            x7, [fp, #-0x30]
    // 0xb39544: LoadField: r8 = r3->field_1b
    //     0xb39544: ldur            w8, [x3, #0x1b]
    // 0xb39548: DecompressPointer r8
    //     0xb39548: add             x8, x8, HEAP, lsl #32
    // 0xb3954c: stur            x8, [fp, #-0x28]
    // 0xb39550: LoadField: r9 = r3->field_2b
    //     0xb39550: ldur            w9, [x3, #0x2b]
    // 0xb39554: DecompressPointer r9
    //     0xb39554: add             x9, x9, HEAP, lsl #32
    // 0xb39558: stur            x9, [fp, #-0x20]
    // 0xb3955c: LoadField: r10 = r3->field_2f
    //     0xb3955c: ldur            w10, [x3, #0x2f]
    // 0xb39560: DecompressPointer r10
    //     0xb39560: add             x10, x10, HEAP, lsl #32
    // 0xb39564: stur            x10, [fp, #-0x18]
    // 0xb39568: r0 = OffersContentItemView()
    //     0xb39568: bl              #0xb39848  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xb3956c: mov             x1, x0
    // 0xb39570: ldur            x0, [fp, #-0x50]
    // 0xb39574: StoreField: r1->field_b = r0
    //     0xb39574: stur            w0, [x1, #0xb]
    // 0xb39578: ldur            x0, [fp, #-0x48]
    // 0xb3957c: StoreField: r1->field_f = r0
    //     0xb3957c: stur            w0, [x1, #0xf]
    // 0xb39580: ldur            x0, [fp, #-0x40]
    // 0xb39584: StoreField: r1->field_13 = r0
    //     0xb39584: stur            w0, [x1, #0x13]
    // 0xb39588: ldur            x0, [fp, #-0x38]
    // 0xb3958c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3958c: stur            w0, [x1, #0x17]
    // 0xb39590: ldur            x0, [fp, #-0x30]
    // 0xb39594: StoreField: r1->field_1b = r0
    //     0xb39594: stur            w0, [x1, #0x1b]
    // 0xb39598: ldur            x0, [fp, #-0x28]
    // 0xb3959c: StoreField: r1->field_1f = r0
    //     0xb3959c: stur            w0, [x1, #0x1f]
    // 0xb395a0: ldur            x0, [fp, #-0x20]
    // 0xb395a4: StoreField: r1->field_23 = r0
    //     0xb395a4: stur            w0, [x1, #0x23]
    // 0xb395a8: ldur            x0, [fp, #-0x18]
    // 0xb395ac: StoreField: r1->field_27 = r0
    //     0xb395ac: stur            w0, [x1, #0x27]
    // 0xb395b0: mov             x3, x1
    // 0xb395b4: b               #0xb395bc
    // 0xb395b8: ldur            x3, [fp, #-0x10]
    // 0xb395bc: ldur            x2, [fp, #-8]
    // 0xb395c0: stur            x3, [fp, #-0x10]
    // 0xb395c4: LoadField: r0 = r2->field_f
    //     0xb395c4: ldur            w0, [x2, #0xf]
    // 0xb395c8: DecompressPointer r0
    //     0xb395c8: add             x0, x0, HEAP, lsl #32
    // 0xb395cc: LoadField: r1 = r0->field_b
    //     0xb395cc: ldur            w1, [x0, #0xb]
    // 0xb395d0: DecompressPointer r1
    //     0xb395d0: add             x1, x1, HEAP, lsl #32
    // 0xb395d4: cmp             w1, NULL
    // 0xb395d8: b.eq            #0xb39834
    // 0xb395dc: LoadField: r0 = r1->field_b
    //     0xb395dc: ldur            w0, [x1, #0xb]
    // 0xb395e0: DecompressPointer r0
    //     0xb395e0: add             x0, x0, HEAP, lsl #32
    // 0xb395e4: cmp             w0, NULL
    // 0xb395e8: b.ne            #0xb395f8
    // 0xb395ec: ldr             x4, [fp, #0x10]
    // 0xb395f0: r0 = Null
    //     0xb395f0: mov             x0, NULL
    // 0xb395f4: b               #0xb39644
    // 0xb395f8: ldr             x4, [fp, #0x10]
    // 0xb395fc: LoadField: r5 = r0->field_f
    //     0xb395fc: ldur            w5, [x0, #0xf]
    // 0xb39600: DecompressPointer r5
    //     0xb39600: add             x5, x5, HEAP, lsl #32
    // 0xb39604: LoadField: r0 = r5->field_b
    //     0xb39604: ldur            w0, [x5, #0xb]
    // 0xb39608: r6 = LoadInt32Instr(r4)
    //     0xb39608: sbfx            x6, x4, #1, #0x1f
    //     0xb3960c: tbz             w4, #0, #0xb39614
    //     0xb39610: ldur            x6, [x4, #7]
    // 0xb39614: r1 = LoadInt32Instr(r0)
    //     0xb39614: sbfx            x1, x0, #1, #0x1f
    // 0xb39618: mov             x0, x1
    // 0xb3961c: mov             x1, x6
    // 0xb39620: cmp             x1, x0
    // 0xb39624: b.hs            #0xb39838
    // 0xb39628: LoadField: r0 = r5->field_f
    //     0xb39628: ldur            w0, [x5, #0xf]
    // 0xb3962c: DecompressPointer r0
    //     0xb3962c: add             x0, x0, HEAP, lsl #32
    // 0xb39630: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb39630: add             x16, x0, x6, lsl #2
    //     0xb39634: ldur            w1, [x16, #0xf]
    // 0xb39638: DecompressPointer r1
    //     0xb39638: add             x1, x1, HEAP, lsl #32
    // 0xb3963c: LoadField: r0 = r1->field_7
    //     0xb3963c: ldur            w0, [x1, #7]
    // 0xb39640: DecompressPointer r0
    //     0xb39640: add             x0, x0, HEAP, lsl #32
    // 0xb39644: r1 = LoadClassIdInstr(r0)
    //     0xb39644: ldur            x1, [x0, #-1]
    //     0xb39648: ubfx            x1, x1, #0xc, #0x14
    // 0xb3964c: r16 = "other_offers"
    //     0xb3964c: add             x16, PP, #0x57, lsl #12  ; [pp+0x571d0] "other_offers"
    //     0xb39650: ldr             x16, [x16, #0x1d0]
    // 0xb39654: stp             x16, x0, [SP]
    // 0xb39658: mov             x0, x1
    // 0xb3965c: mov             lr, x0
    // 0xb39660: ldr             lr, [x21, lr, lsl #3]
    // 0xb39664: blr             lr
    // 0xb39668: tbnz            w0, #4, #0xb397e0
    // 0xb3966c: ldur            x0, [fp, #-8]
    // 0xb39670: LoadField: r1 = r0->field_f
    //     0xb39670: ldur            w1, [x0, #0xf]
    // 0xb39674: DecompressPointer r1
    //     0xb39674: add             x1, x1, HEAP, lsl #32
    // 0xb39678: LoadField: r2 = r1->field_b
    //     0xb39678: ldur            w2, [x1, #0xb]
    // 0xb3967c: DecompressPointer r2
    //     0xb3967c: add             x2, x2, HEAP, lsl #32
    // 0xb39680: cmp             w2, NULL
    // 0xb39684: b.eq            #0xb3983c
    // 0xb39688: LoadField: r3 = r2->field_b
    //     0xb39688: ldur            w3, [x2, #0xb]
    // 0xb3968c: DecompressPointer r3
    //     0xb3968c: add             x3, x3, HEAP, lsl #32
    // 0xb39690: cmp             w3, NULL
    // 0xb39694: b.ne            #0xb396a4
    // 0xb39698: ldr             x4, [fp, #0x10]
    // 0xb3969c: r5 = Null
    //     0xb3969c: mov             x5, NULL
    // 0xb396a0: b               #0xb396f4
    // 0xb396a4: ldr             x4, [fp, #0x10]
    // 0xb396a8: LoadField: r5 = r3->field_f
    //     0xb396a8: ldur            w5, [x3, #0xf]
    // 0xb396ac: DecompressPointer r5
    //     0xb396ac: add             x5, x5, HEAP, lsl #32
    // 0xb396b0: LoadField: r0 = r5->field_b
    //     0xb396b0: ldur            w0, [x5, #0xb]
    // 0xb396b4: r6 = LoadInt32Instr(r4)
    //     0xb396b4: sbfx            x6, x4, #1, #0x1f
    //     0xb396b8: tbz             w4, #0, #0xb396c0
    //     0xb396bc: ldur            x6, [x4, #7]
    // 0xb396c0: r1 = LoadInt32Instr(r0)
    //     0xb396c0: sbfx            x1, x0, #1, #0x1f
    // 0xb396c4: mov             x0, x1
    // 0xb396c8: mov             x1, x6
    // 0xb396cc: cmp             x1, x0
    // 0xb396d0: b.hs            #0xb39840
    // 0xb396d4: LoadField: r0 = r5->field_f
    //     0xb396d4: ldur            w0, [x5, #0xf]
    // 0xb396d8: DecompressPointer r0
    //     0xb396d8: add             x0, x0, HEAP, lsl #32
    // 0xb396dc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb396dc: add             x16, x0, x6, lsl #2
    //     0xb396e0: ldur            w1, [x16, #0xf]
    // 0xb396e4: DecompressPointer r1
    //     0xb396e4: add             x1, x1, HEAP, lsl #32
    // 0xb396e8: LoadField: r0 = r1->field_7
    //     0xb396e8: ldur            w0, [x1, #7]
    // 0xb396ec: DecompressPointer r0
    //     0xb396ec: add             x0, x0, HEAP, lsl #32
    // 0xb396f0: mov             x5, x0
    // 0xb396f4: stur            x5, [fp, #-0x48]
    // 0xb396f8: cmp             w3, NULL
    // 0xb396fc: b.ne            #0xb39708
    // 0xb39700: r0 = Null
    //     0xb39700: mov             x0, NULL
    // 0xb39704: b               #0xb3974c
    // 0xb39708: LoadField: r6 = r3->field_f
    //     0xb39708: ldur            w6, [x3, #0xf]
    // 0xb3970c: DecompressPointer r6
    //     0xb3970c: add             x6, x6, HEAP, lsl #32
    // 0xb39710: LoadField: r0 = r6->field_b
    //     0xb39710: ldur            w0, [x6, #0xb]
    // 0xb39714: r3 = LoadInt32Instr(r4)
    //     0xb39714: sbfx            x3, x4, #1, #0x1f
    //     0xb39718: tbz             w4, #0, #0xb39720
    //     0xb3971c: ldur            x3, [x4, #7]
    // 0xb39720: r1 = LoadInt32Instr(r0)
    //     0xb39720: sbfx            x1, x0, #1, #0x1f
    // 0xb39724: mov             x0, x1
    // 0xb39728: mov             x1, x3
    // 0xb3972c: cmp             x1, x0
    // 0xb39730: b.hs            #0xb39844
    // 0xb39734: LoadField: r0 = r6->field_f
    //     0xb39734: ldur            w0, [x6, #0xf]
    // 0xb39738: DecompressPointer r0
    //     0xb39738: add             x0, x0, HEAP, lsl #32
    // 0xb3973c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb3973c: add             x16, x0, x3, lsl #2
    //     0xb39740: ldur            w1, [x16, #0xf]
    // 0xb39744: DecompressPointer r1
    //     0xb39744: add             x1, x1, HEAP, lsl #32
    // 0xb39748: mov             x0, x1
    // 0xb3974c: stur            x0, [fp, #-0x40]
    // 0xb39750: LoadField: r1 = r2->field_f
    //     0xb39750: ldur            w1, [x2, #0xf]
    // 0xb39754: DecompressPointer r1
    //     0xb39754: add             x1, x1, HEAP, lsl #32
    // 0xb39758: stur            x1, [fp, #-0x38]
    // 0xb3975c: LoadField: r3 = r2->field_13
    //     0xb3975c: ldur            w3, [x2, #0x13]
    // 0xb39760: DecompressPointer r3
    //     0xb39760: add             x3, x3, HEAP, lsl #32
    // 0xb39764: stur            x3, [fp, #-0x30]
    // 0xb39768: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb39768: ldur            w4, [x2, #0x17]
    // 0xb3976c: DecompressPointer r4
    //     0xb3976c: add             x4, x4, HEAP, lsl #32
    // 0xb39770: stur            x4, [fp, #-0x28]
    // 0xb39774: LoadField: r6 = r2->field_1b
    //     0xb39774: ldur            w6, [x2, #0x1b]
    // 0xb39778: DecompressPointer r6
    //     0xb39778: add             x6, x6, HEAP, lsl #32
    // 0xb3977c: stur            x6, [fp, #-0x20]
    // 0xb39780: LoadField: r7 = r2->field_2b
    //     0xb39780: ldur            w7, [x2, #0x2b]
    // 0xb39784: DecompressPointer r7
    //     0xb39784: add             x7, x7, HEAP, lsl #32
    // 0xb39788: stur            x7, [fp, #-0x18]
    // 0xb3978c: LoadField: r8 = r2->field_2f
    //     0xb3978c: ldur            w8, [x2, #0x2f]
    // 0xb39790: DecompressPointer r8
    //     0xb39790: add             x8, x8, HEAP, lsl #32
    // 0xb39794: stur            x8, [fp, #-8]
    // 0xb39798: r0 = OffersContentItemView()
    //     0xb39798: bl              #0xb39848  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xb3979c: ldur            x1, [fp, #-0x48]
    // 0xb397a0: StoreField: r0->field_b = r1
    //     0xb397a0: stur            w1, [x0, #0xb]
    // 0xb397a4: ldur            x1, [fp, #-0x40]
    // 0xb397a8: StoreField: r0->field_f = r1
    //     0xb397a8: stur            w1, [x0, #0xf]
    // 0xb397ac: ldur            x1, [fp, #-0x38]
    // 0xb397b0: StoreField: r0->field_13 = r1
    //     0xb397b0: stur            w1, [x0, #0x13]
    // 0xb397b4: ldur            x1, [fp, #-0x30]
    // 0xb397b8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb397b8: stur            w1, [x0, #0x17]
    // 0xb397bc: ldur            x1, [fp, #-0x28]
    // 0xb397c0: StoreField: r0->field_1b = r1
    //     0xb397c0: stur            w1, [x0, #0x1b]
    // 0xb397c4: ldur            x1, [fp, #-0x20]
    // 0xb397c8: StoreField: r0->field_1f = r1
    //     0xb397c8: stur            w1, [x0, #0x1f]
    // 0xb397cc: ldur            x1, [fp, #-0x18]
    // 0xb397d0: StoreField: r0->field_23 = r1
    //     0xb397d0: stur            w1, [x0, #0x23]
    // 0xb397d4: ldur            x1, [fp, #-8]
    // 0xb397d8: StoreField: r0->field_27 = r1
    //     0xb397d8: stur            w1, [x0, #0x27]
    // 0xb397dc: b               #0xb397e4
    // 0xb397e0: ldur            x0, [fp, #-0x10]
    // 0xb397e4: LeaveFrame
    //     0xb397e4: mov             SP, fp
    //     0xb397e8: ldp             fp, lr, [SP], #0x10
    // 0xb397ec: ret
    //     0xb397ec: ret             
    // 0xb397f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb397f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb397f4: b               #0xb38f1c
    // 0xb397f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb397f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb397fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb397fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb39800: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb39804: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39804: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39808: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39808: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3980c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3980c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb39810: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39810: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39814: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb39814: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb39818: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39818: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3981c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3981c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39820: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb39820: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb39824: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39824: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39828: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb39828: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3982c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3982c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39830: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39834: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb39834: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb39838: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39838: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3983c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3983c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb39840: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39840: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb39844: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb39844: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4113, size: 0x34, field offset: 0xc
//   const constructor, 
class OffersListWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e7d8, size: 0x24
    // 0xc7e7d8: EnterFrame
    //     0xc7e7d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e7dc: mov             fp, SP
    // 0xc7e7e0: mov             x0, x1
    // 0xc7e7e4: r1 = <OffersListWidget>
    //     0xc7e7e4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a90] TypeArguments: <OffersListWidget>
    //     0xc7e7e8: ldr             x1, [x1, #0xa90]
    // 0xc7e7ec: r0 = _OffersListWidgetState()
    //     0xc7e7ec: bl              #0xc7e7fc  ; Allocate_OffersListWidgetStateStub -> _OffersListWidgetState (size=0x14)
    // 0xc7e7f0: LeaveFrame
    //     0xc7e7f0: mov             SP, fp
    //     0xc7e7f4: ldp             fp, lr, [SP], #0x10
    // 0xc7e7f8: ret
    //     0xc7e7f8: ret             
  }
}
