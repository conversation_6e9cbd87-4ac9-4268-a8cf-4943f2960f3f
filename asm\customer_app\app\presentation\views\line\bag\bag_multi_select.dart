// lib: , url: package:customer_app/app/presentation/views/line/bag/bag_multi_select.dart

// class id: 1049464, size: 0x8
class :: {
}

// class id: 3293, size: 0x18, field offset: 0x14
class _BagMultiSelectState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x945414, size: 0x3a4
    // 0x945414: EnterFrame
    //     0x945414: stp             fp, lr, [SP, #-0x10]!
    //     0x945418: mov             fp, SP
    // 0x94541c: AllocStack(0x40)
    //     0x94541c: sub             SP, SP, #0x40
    // 0x945420: SetupParameters(_BagMultiSelectState this /* r1 => r1, fp-0x8 */)
    //     0x945420: stur            x1, [fp, #-8]
    // 0x945424: CheckStackOverflow
    //     0x945424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945428: cmp             SP, x16
    //     0x94542c: b.ls            #0x945798
    // 0x945430: LoadField: r0 = r1->field_b
    //     0x945430: ldur            w0, [x1, #0xb]
    // 0x945434: DecompressPointer r0
    //     0x945434: add             x0, x0, HEAP, lsl #32
    // 0x945438: cmp             w0, NULL
    // 0x94543c: b.eq            #0x9457a0
    // 0x945440: LoadField: r2 = r0->field_b
    //     0x945440: ldur            w2, [x0, #0xb]
    // 0x945444: DecompressPointer r2
    //     0x945444: add             x2, x2, HEAP, lsl #32
    // 0x945448: cmp             w2, NULL
    // 0x94544c: b.ne            #0x945458
    // 0x945450: r0 = Null
    //     0x945450: mov             x0, NULL
    // 0x945454: b               #0x945460
    // 0x945458: LoadField: r0 = r2->field_23
    //     0x945458: ldur            w0, [x2, #0x23]
    // 0x94545c: DecompressPointer r0
    //     0x94545c: add             x0, x0, HEAP, lsl #32
    // 0x945460: cmp             w0, NULL
    // 0x945464: b.ne            #0x9454ac
    // 0x945468: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x945468: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94546c: ldr             x0, [x0]
    //     0x945470: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x945474: cmp             w0, w16
    //     0x945478: b.ne            #0x945484
    //     0x94547c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x945480: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x945484: r1 = <CustomerResponse>
    //     0x945484: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0x945488: ldr             x1, [x1, #0x5a8]
    // 0x94548c: stur            x0, [fp, #-0x10]
    // 0x945490: r0 = AllocateGrowableArray()
    //     0x945490: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x945494: mov             x1, x0
    // 0x945498: ldur            x0, [fp, #-0x10]
    // 0x94549c: StoreField: r1->field_f = r0
    //     0x94549c: stur            w0, [x1, #0xf]
    // 0x9454a0: StoreField: r1->field_b = rZR
    //     0x9454a0: stur            wzr, [x1, #0xb]
    // 0x9454a4: mov             x3, x1
    // 0x9454a8: b               #0x9454b0
    // 0x9454ac: mov             x3, x0
    // 0x9454b0: stur            x3, [fp, #-0x30]
    // 0x9454b4: LoadField: r4 = r3->field_7
    //     0x9454b4: ldur            w4, [x3, #7]
    // 0x9454b8: DecompressPointer r4
    //     0x9454b8: add             x4, x4, HEAP, lsl #32
    // 0x9454bc: stur            x4, [fp, #-0x28]
    // 0x9454c0: LoadField: r0 = r3->field_b
    //     0x9454c0: ldur            w0, [x3, #0xb]
    // 0x9454c4: r5 = LoadInt32Instr(r0)
    //     0x9454c4: sbfx            x5, x0, #1, #0x1f
    // 0x9454c8: stur            x5, [fp, #-0x20]
    // 0x9454cc: r0 = 0
    //     0x9454cc: movz            x0, #0
    // 0x9454d0: ldur            x6, [fp, #-8]
    // 0x9454d4: CheckStackOverflow
    //     0x9454d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9454d8: cmp             SP, x16
    //     0x9454dc: b.ls            #0x9457a4
    // 0x9454e0: LoadField: r1 = r3->field_b
    //     0x9454e0: ldur            w1, [x3, #0xb]
    // 0x9454e4: r2 = LoadInt32Instr(r1)
    //     0x9454e4: sbfx            x2, x1, #1, #0x1f
    // 0x9454e8: cmp             x5, x2
    // 0x9454ec: b.ne            #0x945778
    // 0x9454f0: cmp             x0, x2
    // 0x9454f4: b.ge            #0x945604
    // 0x9454f8: LoadField: r1 = r3->field_f
    //     0x9454f8: ldur            w1, [x3, #0xf]
    // 0x9454fc: DecompressPointer r1
    //     0x9454fc: add             x1, x1, HEAP, lsl #32
    // 0x945500: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0x945500: add             x16, x1, x0, lsl #2
    //     0x945504: ldur            w7, [x16, #0xf]
    // 0x945508: DecompressPointer r7
    //     0x945508: add             x7, x7, HEAP, lsl #32
    // 0x94550c: stur            x7, [fp, #-0x10]
    // 0x945510: add             x8, x0, #1
    // 0x945514: stur            x8, [fp, #-0x18]
    // 0x945518: cmp             w7, NULL
    // 0x94551c: b.ne            #0x945550
    // 0x945520: mov             x0, x7
    // 0x945524: mov             x2, x4
    // 0x945528: r1 = Null
    //     0x945528: mov             x1, NULL
    // 0x94552c: cmp             w2, NULL
    // 0x945530: b.eq            #0x945550
    // 0x945534: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x945534: ldur            w4, [x2, #0x17]
    // 0x945538: DecompressPointer r4
    //     0x945538: add             x4, x4, HEAP, lsl #32
    // 0x94553c: r8 = X0
    //     0x94553c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x945540: LoadField: r9 = r4->field_7
    //     0x945540: ldur            x9, [x4, #7]
    // 0x945544: r3 = Null
    //     0x945544: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a6a8] Null
    //     0x945548: ldr             x3, [x3, #0x6a8]
    // 0x94554c: blr             x9
    // 0x945550: ldur            x0, [fp, #-8]
    // 0x945554: ldur            x1, [fp, #-0x10]
    // 0x945558: LoadField: r2 = r0->field_13
    //     0x945558: ldur            w2, [x0, #0x13]
    // 0x94555c: DecompressPointer r2
    //     0x94555c: add             x2, x2, HEAP, lsl #32
    // 0x945560: stur            x2, [fp, #-0x40]
    // 0x945564: LoadField: r3 = r1->field_b
    //     0x945564: ldur            w3, [x1, #0xb]
    // 0x945568: DecompressPointer r3
    //     0x945568: add             x3, x3, HEAP, lsl #32
    // 0x94556c: cmp             w3, NULL
    // 0x945570: b.ne            #0x945578
    // 0x945574: r3 = ""
    //     0x945574: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x945578: stur            x3, [fp, #-0x10]
    // 0x94557c: LoadField: r1 = r2->field_b
    //     0x94557c: ldur            w1, [x2, #0xb]
    // 0x945580: LoadField: r4 = r2->field_f
    //     0x945580: ldur            w4, [x2, #0xf]
    // 0x945584: DecompressPointer r4
    //     0x945584: add             x4, x4, HEAP, lsl #32
    // 0x945588: LoadField: r5 = r4->field_b
    //     0x945588: ldur            w5, [x4, #0xb]
    // 0x94558c: r4 = LoadInt32Instr(r1)
    //     0x94558c: sbfx            x4, x1, #1, #0x1f
    // 0x945590: stur            x4, [fp, #-0x38]
    // 0x945594: r1 = LoadInt32Instr(r5)
    //     0x945594: sbfx            x1, x5, #1, #0x1f
    // 0x945598: cmp             x4, x1
    // 0x94559c: b.ne            #0x9455a8
    // 0x9455a0: mov             x1, x2
    // 0x9455a4: r0 = _growToNextCapacity()
    //     0x9455a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9455a8: ldur            x0, [fp, #-0x40]
    // 0x9455ac: ldur            x2, [fp, #-0x38]
    // 0x9455b0: add             x1, x2, #1
    // 0x9455b4: lsl             x3, x1, #1
    // 0x9455b8: StoreField: r0->field_b = r3
    //     0x9455b8: stur            w3, [x0, #0xb]
    // 0x9455bc: LoadField: r1 = r0->field_f
    //     0x9455bc: ldur            w1, [x0, #0xf]
    // 0x9455c0: DecompressPointer r1
    //     0x9455c0: add             x1, x1, HEAP, lsl #32
    // 0x9455c4: ldur            x0, [fp, #-0x10]
    // 0x9455c8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x9455c8: add             x25, x1, x2, lsl #2
    //     0x9455cc: add             x25, x25, #0xf
    //     0x9455d0: str             w0, [x25]
    //     0x9455d4: tbz             w0, #0, #0x9455f0
    //     0x9455d8: ldurb           w16, [x1, #-1]
    //     0x9455dc: ldurb           w17, [x0, #-1]
    //     0x9455e0: and             x16, x17, x16, lsr #2
    //     0x9455e4: tst             x16, HEAP, lsr #32
    //     0x9455e8: b.eq            #0x9455f0
    //     0x9455ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9455f0: ldur            x0, [fp, #-0x18]
    // 0x9455f4: ldur            x3, [fp, #-0x30]
    // 0x9455f8: ldur            x4, [fp, #-0x28]
    // 0x9455fc: ldur            x5, [fp, #-0x20]
    // 0x945600: b               #0x9454d0
    // 0x945604: mov             x0, x6
    // 0x945608: LoadField: r1 = r0->field_b
    //     0x945608: ldur            w1, [x0, #0xb]
    // 0x94560c: DecompressPointer r1
    //     0x94560c: add             x1, x1, HEAP, lsl #32
    // 0x945610: cmp             w1, NULL
    // 0x945614: b.eq            #0x9457ac
    // 0x945618: LoadField: r2 = r1->field_f
    //     0x945618: ldur            w2, [x1, #0xf]
    // 0x94561c: DecompressPointer r2
    //     0x94561c: add             x2, x2, HEAP, lsl #32
    // 0x945620: cmp             w2, NULL
    // 0x945624: b.ne            #0x945630
    // 0x945628: r1 = Null
    //     0x945628: mov             x1, NULL
    // 0x94562c: b               #0x945638
    // 0x945630: LoadField: r1 = r2->field_23
    //     0x945630: ldur            w1, [x2, #0x23]
    // 0x945634: DecompressPointer r1
    //     0x945634: add             x1, x1, HEAP, lsl #32
    // 0x945638: cmp             w1, NULL
    // 0x94563c: b.ne            #0x945654
    // 0x945640: r1 = <CustomerResponse>
    //     0x945640: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0x945644: ldr             x1, [x1, #0x5a8]
    // 0x945648: r2 = 0
    //     0x945648: movz            x2, #0
    // 0x94564c: r0 = AllocateArray()
    //     0x94564c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x945650: mov             x1, x0
    // 0x945654: r0 = LoadClassIdInstr(r1)
    //     0x945654: ldur            x0, [x1, #-1]
    //     0x945658: ubfx            x0, x0, #0xc, #0x14
    // 0x94565c: r0 = GDT[cid_x0 + 0xc907]()
    //     0x94565c: movz            x17, #0xc907
    //     0x945660: add             lr, x0, x17
    //     0x945664: ldr             lr, [x21, lr, lsl #3]
    //     0x945668: blr             lr
    // 0x94566c: mov             x2, x0
    // 0x945670: stur            x2, [fp, #-0x10]
    // 0x945674: ldur            x3, [fp, #-8]
    // 0x945678: CheckStackOverflow
    //     0x945678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94567c: cmp             SP, x16
    //     0x945680: b.ls            #0x9457b0
    // 0x945684: r0 = LoadClassIdInstr(r2)
    //     0x945684: ldur            x0, [x2, #-1]
    //     0x945688: ubfx            x0, x0, #0xc, #0x14
    // 0x94568c: mov             x1, x2
    // 0x945690: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x945690: add             lr, x0, #0x5ea
    //     0x945694: ldr             lr, [x21, lr, lsl #3]
    //     0x945698: blr             lr
    // 0x94569c: tbnz            w0, #4, #0x945768
    // 0x9456a0: ldur            x3, [fp, #-8]
    // 0x9456a4: ldur            x2, [fp, #-0x10]
    // 0x9456a8: r0 = LoadClassIdInstr(r2)
    //     0x9456a8: ldur            x0, [x2, #-1]
    //     0x9456ac: ubfx            x0, x0, #0xc, #0x14
    // 0x9456b0: mov             x1, x2
    // 0x9456b4: r0 = GDT[cid_x0 + 0x655]()
    //     0x9456b4: add             lr, x0, #0x655
    //     0x9456b8: ldr             lr, [x21, lr, lsl #3]
    //     0x9456bc: blr             lr
    // 0x9456c0: mov             x1, x0
    // 0x9456c4: ldur            x0, [fp, #-8]
    // 0x9456c8: LoadField: r2 = r0->field_13
    //     0x9456c8: ldur            w2, [x0, #0x13]
    // 0x9456cc: DecompressPointer r2
    //     0x9456cc: add             x2, x2, HEAP, lsl #32
    // 0x9456d0: stur            x2, [fp, #-0x40]
    // 0x9456d4: LoadField: r3 = r1->field_b
    //     0x9456d4: ldur            w3, [x1, #0xb]
    // 0x9456d8: DecompressPointer r3
    //     0x9456d8: add             x3, x3, HEAP, lsl #32
    // 0x9456dc: cmp             w3, NULL
    // 0x9456e0: b.ne            #0x9456e8
    // 0x9456e4: r3 = ""
    //     0x9456e4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9456e8: stur            x3, [fp, #-0x28]
    // 0x9456ec: LoadField: r1 = r2->field_b
    //     0x9456ec: ldur            w1, [x2, #0xb]
    // 0x9456f0: LoadField: r4 = r2->field_f
    //     0x9456f0: ldur            w4, [x2, #0xf]
    // 0x9456f4: DecompressPointer r4
    //     0x9456f4: add             x4, x4, HEAP, lsl #32
    // 0x9456f8: LoadField: r5 = r4->field_b
    //     0x9456f8: ldur            w5, [x4, #0xb]
    // 0x9456fc: r4 = LoadInt32Instr(r1)
    //     0x9456fc: sbfx            x4, x1, #1, #0x1f
    // 0x945700: stur            x4, [fp, #-0x18]
    // 0x945704: r1 = LoadInt32Instr(r5)
    //     0x945704: sbfx            x1, x5, #1, #0x1f
    // 0x945708: cmp             x4, x1
    // 0x94570c: b.ne            #0x945718
    // 0x945710: mov             x1, x2
    // 0x945714: r0 = _growToNextCapacity()
    //     0x945714: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945718: ldur            x0, [fp, #-0x40]
    // 0x94571c: ldur            x2, [fp, #-0x18]
    // 0x945720: add             x1, x2, #1
    // 0x945724: lsl             x3, x1, #1
    // 0x945728: StoreField: r0->field_b = r3
    //     0x945728: stur            w3, [x0, #0xb]
    // 0x94572c: LoadField: r1 = r0->field_f
    //     0x94572c: ldur            w1, [x0, #0xf]
    // 0x945730: DecompressPointer r1
    //     0x945730: add             x1, x1, HEAP, lsl #32
    // 0x945734: ldur            x0, [fp, #-0x28]
    // 0x945738: ArrayStore: r1[r2] = r0  ; List_4
    //     0x945738: add             x25, x1, x2, lsl #2
    //     0x94573c: add             x25, x25, #0xf
    //     0x945740: str             w0, [x25]
    //     0x945744: tbz             w0, #0, #0x945760
    //     0x945748: ldurb           w16, [x1, #-1]
    //     0x94574c: ldurb           w17, [x0, #-1]
    //     0x945750: and             x16, x17, x16, lsr #2
    //     0x945754: tst             x16, HEAP, lsr #32
    //     0x945758: b.eq            #0x945760
    //     0x94575c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945760: ldur            x2, [fp, #-0x10]
    // 0x945764: b               #0x945674
    // 0x945768: r0 = Null
    //     0x945768: mov             x0, NULL
    // 0x94576c: LeaveFrame
    //     0x94576c: mov             SP, fp
    //     0x945770: ldp             fp, lr, [SP], #0x10
    // 0x945774: ret
    //     0x945774: ret             
    // 0x945778: mov             x0, x3
    // 0x94577c: r0 = ConcurrentModificationError()
    //     0x94577c: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x945780: mov             x1, x0
    // 0x945784: ldur            x0, [fp, #-0x30]
    // 0x945788: StoreField: r1->field_b = r0
    //     0x945788: stur            w0, [x1, #0xb]
    // 0x94578c: mov             x0, x1
    // 0x945790: r0 = Throw()
    //     0x945790: bl              #0x16f5420  ; ThrowStub
    // 0x945794: brk             #0
    // 0x945798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945798: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94579c: b               #0x945430
    // 0x9457a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9457a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9457a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9457a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9457a8: b               #0x9454e0
    // 0x9457ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9457ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9457b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9457b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9457b4: b               #0x945684
  }
  _ build(/* No info */) {
    // ** addr: 0xba1f5c, size: 0x944
    // 0xba1f5c: EnterFrame
    //     0xba1f5c: stp             fp, lr, [SP, #-0x10]!
    //     0xba1f60: mov             fp, SP
    // 0xba1f64: AllocStack(0x50)
    //     0xba1f64: sub             SP, SP, #0x50
    // 0xba1f68: SetupParameters(_BagMultiSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba1f68: mov             x0, x1
    //     0xba1f6c: stur            x1, [fp, #-8]
    //     0xba1f70: mov             x1, x2
    //     0xba1f74: stur            x2, [fp, #-0x10]
    // 0xba1f78: CheckStackOverflow
    //     0xba1f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba1f7c: cmp             SP, x16
    //     0xba1f80: b.ls            #0xba2884
    // 0xba1f84: LoadField: r2 = r0->field_b
    //     0xba1f84: ldur            w2, [x0, #0xb]
    // 0xba1f88: DecompressPointer r2
    //     0xba1f88: add             x2, x2, HEAP, lsl #32
    // 0xba1f8c: cmp             w2, NULL
    // 0xba1f90: b.eq            #0xba288c
    // 0xba1f94: LoadField: r3 = r2->field_b
    //     0xba1f94: ldur            w3, [x2, #0xb]
    // 0xba1f98: DecompressPointer r3
    //     0xba1f98: add             x3, x3, HEAP, lsl #32
    // 0xba1f9c: cmp             w3, NULL
    // 0xba1fa0: b.ne            #0xba1fac
    // 0xba1fa4: r2 = Null
    //     0xba1fa4: mov             x2, NULL
    // 0xba1fa8: b               #0xba1fd8
    // 0xba1fac: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xba1fac: ldur            w2, [x3, #0x17]
    // 0xba1fb0: DecompressPointer r2
    //     0xba1fb0: add             x2, x2, HEAP, lsl #32
    // 0xba1fb4: cmp             w2, NULL
    // 0xba1fb8: b.ne            #0xba1fc4
    // 0xba1fbc: r2 = Null
    //     0xba1fbc: mov             x2, NULL
    // 0xba1fc0: b               #0xba1fd8
    // 0xba1fc4: LoadField: r3 = r2->field_7
    //     0xba1fc4: ldur            w3, [x2, #7]
    // 0xba1fc8: cbnz            w3, #0xba1fd4
    // 0xba1fcc: r2 = false
    //     0xba1fcc: add             x2, NULL, #0x30  ; false
    // 0xba1fd0: b               #0xba1fd8
    // 0xba1fd4: r2 = true
    //     0xba1fd4: add             x2, NULL, #0x20  ; true
    // 0xba1fd8: cmp             w2, NULL
    // 0xba1fdc: b.ne            #0xba2028
    // 0xba1fe0: mov             x3, x0
    // 0xba1fe4: r2 = 4
    //     0xba1fe4: movz            x2, #0x4
    // 0xba1fe8: r4 = 6
    //     0xba1fe8: movz            x4, #0x6
    // 0xba1fec: r7 = Instance_CrossAxisAlignment
    //     0xba1fec: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba1ff0: ldr             x7, [x7, #0xa18]
    // 0xba1ff4: r5 = Instance_MainAxisAlignment
    //     0xba1ff4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba1ff8: ldr             x5, [x5, #0xa08]
    // 0xba1ffc: r6 = Instance_MainAxisSize
    //     0xba1ffc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba2000: ldr             x6, [x6, #0xa10]
    // 0xba2004: r1 = Instance_Axis
    //     0xba2004: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba2008: r8 = Instance_VerticalDirection
    //     0xba2008: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba200c: ldr             x8, [x8, #0xa20]
    // 0xba2010: r0 = Instance__DeferringMouseCursor
    //     0xba2010: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2014: r9 = Instance_Clip
    //     0xba2014: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba2018: ldr             x9, [x9, #0x38]
    // 0xba201c: d1 = 0.500000
    //     0xba201c: fmov            d1, #0.50000000
    // 0xba2020: d0 = inf
    //     0xba2020: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba2024: b               #0xba2478
    // 0xba2028: tbnz            w2, #4, #0xba2434
    // 0xba202c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba202c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba2030: ldr             x0, [x0, #0x1c80]
    //     0xba2034: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba2038: cmp             w0, w16
    //     0xba203c: b.ne            #0xba2048
    //     0xba2040: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba2044: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba2048: r0 = GetNavigation.size()
    //     0xba2048: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba204c: LoadField: d0 = r0->field_7
    //     0xba204c: ldur            d0, [x0, #7]
    // 0xba2050: d1 = 0.500000
    //     0xba2050: fmov            d1, #0.50000000
    // 0xba2054: fmul            d2, d0, d1
    // 0xba2058: stur            d2, [fp, #-0x40]
    // 0xba205c: r0 = BoxConstraints()
    //     0xba205c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba2060: stur            x0, [fp, #-0x20]
    // 0xba2064: StoreField: r0->field_7 = rZR
    //     0xba2064: stur            xzr, [x0, #7]
    // 0xba2068: ldur            d0, [fp, #-0x40]
    // 0xba206c: StoreField: r0->field_f = d0
    //     0xba206c: stur            d0, [x0, #0xf]
    // 0xba2070: ArrayStore: r0[0] = rZR  ; List_8
    //     0xba2070: stur            xzr, [x0, #0x17]
    // 0xba2074: d0 = inf
    //     0xba2074: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba2078: StoreField: r0->field_1f = d0
    //     0xba2078: stur            d0, [x0, #0x1f]
    // 0xba207c: ldur            x2, [fp, #-8]
    // 0xba2080: LoadField: r1 = r2->field_b
    //     0xba2080: ldur            w1, [x2, #0xb]
    // 0xba2084: DecompressPointer r1
    //     0xba2084: add             x1, x1, HEAP, lsl #32
    // 0xba2088: cmp             w1, NULL
    // 0xba208c: b.eq            #0xba2890
    // 0xba2090: LoadField: r3 = r1->field_b
    //     0xba2090: ldur            w3, [x1, #0xb]
    // 0xba2094: DecompressPointer r3
    //     0xba2094: add             x3, x3, HEAP, lsl #32
    // 0xba2098: cmp             w3, NULL
    // 0xba209c: b.ne            #0xba20a8
    // 0xba20a0: r1 = Null
    //     0xba20a0: mov             x1, NULL
    // 0xba20a4: b               #0xba20b0
    // 0xba20a8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xba20a8: ldur            w1, [x3, #0x17]
    // 0xba20ac: DecompressPointer r1
    //     0xba20ac: add             x1, x1, HEAP, lsl #32
    // 0xba20b0: cmp             w1, NULL
    // 0xba20b4: b.ne            #0xba20c0
    // 0xba20b8: r3 = ""
    //     0xba20b8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba20bc: b               #0xba20c4
    // 0xba20c0: mov             x3, x1
    // 0xba20c4: ldur            x1, [fp, #-0x10]
    // 0xba20c8: stur            x3, [fp, #-0x18]
    // 0xba20cc: r0 = of()
    //     0xba20cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba20d0: LoadField: r1 = r0->field_87
    //     0xba20d0: ldur            w1, [x0, #0x87]
    // 0xba20d4: DecompressPointer r1
    //     0xba20d4: add             x1, x1, HEAP, lsl #32
    // 0xba20d8: LoadField: r0 = r1->field_2b
    //     0xba20d8: ldur            w0, [x1, #0x2b]
    // 0xba20dc: DecompressPointer r0
    //     0xba20dc: add             x0, x0, HEAP, lsl #32
    // 0xba20e0: stur            x0, [fp, #-0x28]
    // 0xba20e4: r1 = Instance_Color
    //     0xba20e4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba20e8: d0 = 0.700000
    //     0xba20e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba20ec: ldr             d0, [x17, #0xf48]
    // 0xba20f0: r0 = withOpacity()
    //     0xba20f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba20f4: r16 = 12.000000
    //     0xba20f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba20f8: ldr             x16, [x16, #0x9e8]
    // 0xba20fc: stp             x0, x16, [SP]
    // 0xba2100: ldur            x1, [fp, #-0x28]
    // 0xba2104: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2104: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2108: ldr             x4, [x4, #0xaa0]
    // 0xba210c: r0 = copyWith()
    //     0xba210c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2110: stur            x0, [fp, #-0x28]
    // 0xba2114: r0 = TextSpan()
    //     0xba2114: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2118: mov             x3, x0
    // 0xba211c: ldur            x0, [fp, #-0x18]
    // 0xba2120: stur            x3, [fp, #-0x30]
    // 0xba2124: StoreField: r3->field_b = r0
    //     0xba2124: stur            w0, [x3, #0xb]
    // 0xba2128: r0 = Instance__DeferringMouseCursor
    //     0xba2128: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba212c: ArrayStore: r3[0] = r0  ; List_4
    //     0xba212c: stur            w0, [x3, #0x17]
    // 0xba2130: ldur            x1, [fp, #-0x28]
    // 0xba2134: StoreField: r3->field_7 = r1
    //     0xba2134: stur            w1, [x3, #7]
    // 0xba2138: r1 = Null
    //     0xba2138: mov             x1, NULL
    // 0xba213c: r2 = 4
    //     0xba213c: movz            x2, #0x4
    // 0xba2140: r0 = AllocateArray()
    //     0xba2140: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2144: stur            x0, [fp, #-0x18]
    // 0xba2148: r16 = " : "
    //     0xba2148: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xba214c: ldr             x16, [x16, #0x680]
    // 0xba2150: StoreField: r0->field_f = r16
    //     0xba2150: stur            w16, [x0, #0xf]
    // 0xba2154: ldur            x2, [fp, #-8]
    // 0xba2158: LoadField: r1 = r2->field_13
    //     0xba2158: ldur            w1, [x2, #0x13]
    // 0xba215c: DecompressPointer r1
    //     0xba215c: add             x1, x1, HEAP, lsl #32
    // 0xba2160: r16 = ", "
    //     0xba2160: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xba2164: str             x16, [SP]
    // 0xba2168: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xba2168: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xba216c: r0 = join()
    //     0xba216c: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0xba2170: ldur            x1, [fp, #-0x18]
    // 0xba2174: ArrayStore: r1[1] = r0  ; List_4
    //     0xba2174: add             x25, x1, #0x13
    //     0xba2178: str             w0, [x25]
    //     0xba217c: tbz             w0, #0, #0xba2198
    //     0xba2180: ldurb           w16, [x1, #-1]
    //     0xba2184: ldurb           w17, [x0, #-1]
    //     0xba2188: and             x16, x17, x16, lsr #2
    //     0xba218c: tst             x16, HEAP, lsr #32
    //     0xba2190: b.eq            #0xba2198
    //     0xba2194: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba2198: ldur            x16, [fp, #-0x18]
    // 0xba219c: str             x16, [SP]
    // 0xba21a0: r0 = _interpolate()
    //     0xba21a0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba21a4: ldur            x1, [fp, #-0x10]
    // 0xba21a8: stur            x0, [fp, #-0x18]
    // 0xba21ac: r0 = of()
    //     0xba21ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba21b0: LoadField: r1 = r0->field_87
    //     0xba21b0: ldur            w1, [x0, #0x87]
    // 0xba21b4: DecompressPointer r1
    //     0xba21b4: add             x1, x1, HEAP, lsl #32
    // 0xba21b8: LoadField: r0 = r1->field_7
    //     0xba21b8: ldur            w0, [x1, #7]
    // 0xba21bc: DecompressPointer r0
    //     0xba21bc: add             x0, x0, HEAP, lsl #32
    // 0xba21c0: stur            x0, [fp, #-0x28]
    // 0xba21c4: r1 = Instance_Color
    //     0xba21c4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba21c8: d0 = 0.700000
    //     0xba21c8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba21cc: ldr             d0, [x17, #0xf48]
    // 0xba21d0: r0 = withOpacity()
    //     0xba21d0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba21d4: r16 = 12.000000
    //     0xba21d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba21d8: ldr             x16, [x16, #0x9e8]
    // 0xba21dc: stp             x0, x16, [SP]
    // 0xba21e0: ldur            x1, [fp, #-0x28]
    // 0xba21e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba21e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba21e8: ldr             x4, [x4, #0xaa0]
    // 0xba21ec: r0 = copyWith()
    //     0xba21ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba21f0: stur            x0, [fp, #-0x28]
    // 0xba21f4: r0 = TextSpan()
    //     0xba21f4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba21f8: mov             x3, x0
    // 0xba21fc: ldur            x0, [fp, #-0x18]
    // 0xba2200: stur            x3, [fp, #-0x38]
    // 0xba2204: StoreField: r3->field_b = r0
    //     0xba2204: stur            w0, [x3, #0xb]
    // 0xba2208: r0 = Instance__DeferringMouseCursor
    //     0xba2208: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba220c: ArrayStore: r3[0] = r0  ; List_4
    //     0xba220c: stur            w0, [x3, #0x17]
    // 0xba2210: ldur            x1, [fp, #-0x28]
    // 0xba2214: StoreField: r3->field_7 = r1
    //     0xba2214: stur            w1, [x3, #7]
    // 0xba2218: r1 = Null
    //     0xba2218: mov             x1, NULL
    // 0xba221c: r2 = 4
    //     0xba221c: movz            x2, #0x4
    // 0xba2220: r0 = AllocateArray()
    //     0xba2220: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2224: mov             x2, x0
    // 0xba2228: ldur            x0, [fp, #-0x30]
    // 0xba222c: stur            x2, [fp, #-0x18]
    // 0xba2230: StoreField: r2->field_f = r0
    //     0xba2230: stur            w0, [x2, #0xf]
    // 0xba2234: ldur            x0, [fp, #-0x38]
    // 0xba2238: StoreField: r2->field_13 = r0
    //     0xba2238: stur            w0, [x2, #0x13]
    // 0xba223c: r1 = <InlineSpan>
    //     0xba223c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xba2240: ldr             x1, [x1, #0xe40]
    // 0xba2244: r0 = AllocateGrowableArray()
    //     0xba2244: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba2248: mov             x1, x0
    // 0xba224c: ldur            x0, [fp, #-0x18]
    // 0xba2250: stur            x1, [fp, #-0x28]
    // 0xba2254: StoreField: r1->field_f = r0
    //     0xba2254: stur            w0, [x1, #0xf]
    // 0xba2258: r2 = 4
    //     0xba2258: movz            x2, #0x4
    // 0xba225c: StoreField: r1->field_b = r2
    //     0xba225c: stur            w2, [x1, #0xb]
    // 0xba2260: r0 = TextSpan()
    //     0xba2260: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2264: mov             x1, x0
    // 0xba2268: ldur            x0, [fp, #-0x28]
    // 0xba226c: stur            x1, [fp, #-0x18]
    // 0xba2270: StoreField: r1->field_f = r0
    //     0xba2270: stur            w0, [x1, #0xf]
    // 0xba2274: r0 = Instance__DeferringMouseCursor
    //     0xba2274: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2278: ArrayStore: r1[0] = r0  ; List_4
    //     0xba2278: stur            w0, [x1, #0x17]
    // 0xba227c: r0 = RichText()
    //     0xba227c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba2280: mov             x1, x0
    // 0xba2284: ldur            x2, [fp, #-0x18]
    // 0xba2288: stur            x0, [fp, #-0x18]
    // 0xba228c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba228c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba2290: r0 = RichText()
    //     0xba2290: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba2294: r0 = ConstrainedBox()
    //     0xba2294: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba2298: mov             x1, x0
    // 0xba229c: ldur            x0, [fp, #-0x20]
    // 0xba22a0: stur            x1, [fp, #-0x28]
    // 0xba22a4: StoreField: r1->field_f = r0
    //     0xba22a4: stur            w0, [x1, #0xf]
    // 0xba22a8: ldur            x0, [fp, #-0x18]
    // 0xba22ac: StoreField: r1->field_b = r0
    //     0xba22ac: stur            w0, [x1, #0xb]
    // 0xba22b0: ldur            x3, [fp, #-8]
    // 0xba22b4: LoadField: r0 = r3->field_b
    //     0xba22b4: ldur            w0, [x3, #0xb]
    // 0xba22b8: DecompressPointer r0
    //     0xba22b8: add             x0, x0, HEAP, lsl #32
    // 0xba22bc: cmp             w0, NULL
    // 0xba22c0: b.eq            #0xba2894
    // 0xba22c4: LoadField: r2 = r0->field_b
    //     0xba22c4: ldur            w2, [x0, #0xb]
    // 0xba22c8: DecompressPointer r2
    //     0xba22c8: add             x2, x2, HEAP, lsl #32
    // 0xba22cc: cmp             w2, NULL
    // 0xba22d0: b.ne            #0xba22dc
    // 0xba22d4: r0 = Null
    //     0xba22d4: mov             x0, NULL
    // 0xba22d8: b               #0xba2308
    // 0xba22dc: LoadField: r0 = r2->field_2b
    //     0xba22dc: ldur            w0, [x2, #0x2b]
    // 0xba22e0: DecompressPointer r0
    //     0xba22e0: add             x0, x0, HEAP, lsl #32
    // 0xba22e4: r2 = LoadClassIdInstr(r0)
    //     0xba22e4: ldur            x2, [x0, #-1]
    //     0xba22e8: ubfx            x2, x2, #0xc, #0x14
    // 0xba22ec: str             x0, [SP]
    // 0xba22f0: mov             x0, x2
    // 0xba22f4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba22f4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba22f8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba22f8: movz            x17, #0x2700
    //     0xba22fc: add             lr, x0, x17
    //     0xba2300: ldr             lr, [x21, lr, lsl #3]
    //     0xba2304: blr             lr
    // 0xba2308: cmp             w0, NULL
    // 0xba230c: b.ne            #0xba2318
    // 0xba2310: r2 = " "
    //     0xba2310: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba2314: b               #0xba231c
    // 0xba2318: mov             x2, x0
    // 0xba231c: ldur            x0, [fp, #-0x28]
    // 0xba2320: ldur            x1, [fp, #-0x10]
    // 0xba2324: stur            x2, [fp, #-0x18]
    // 0xba2328: r0 = of()
    //     0xba2328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba232c: LoadField: r1 = r0->field_87
    //     0xba232c: ldur            w1, [x0, #0x87]
    // 0xba2330: DecompressPointer r1
    //     0xba2330: add             x1, x1, HEAP, lsl #32
    // 0xba2334: LoadField: r0 = r1->field_2b
    //     0xba2334: ldur            w0, [x1, #0x2b]
    // 0xba2338: DecompressPointer r0
    //     0xba2338: add             x0, x0, HEAP, lsl #32
    // 0xba233c: stur            x0, [fp, #-0x20]
    // 0xba2340: r1 = Instance_Color
    //     0xba2340: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2344: d0 = 0.700000
    //     0xba2344: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2348: ldr             d0, [x17, #0xf48]
    // 0xba234c: r0 = withOpacity()
    //     0xba234c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2350: r16 = 12.000000
    //     0xba2350: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2354: ldr             x16, [x16, #0x9e8]
    // 0xba2358: stp             x0, x16, [SP]
    // 0xba235c: ldur            x1, [fp, #-0x20]
    // 0xba2360: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2360: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2364: ldr             x4, [x4, #0xaa0]
    // 0xba2368: r0 = copyWith()
    //     0xba2368: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba236c: stur            x0, [fp, #-0x20]
    // 0xba2370: r0 = Text()
    //     0xba2370: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba2374: mov             x3, x0
    // 0xba2378: ldur            x0, [fp, #-0x18]
    // 0xba237c: stur            x3, [fp, #-0x30]
    // 0xba2380: StoreField: r3->field_b = r0
    //     0xba2380: stur            w0, [x3, #0xb]
    // 0xba2384: ldur            x0, [fp, #-0x20]
    // 0xba2388: StoreField: r3->field_13 = r0
    //     0xba2388: stur            w0, [x3, #0x13]
    // 0xba238c: r1 = Null
    //     0xba238c: mov             x1, NULL
    // 0xba2390: r2 = 6
    //     0xba2390: movz            x2, #0x6
    // 0xba2394: r0 = AllocateArray()
    //     0xba2394: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2398: mov             x2, x0
    // 0xba239c: ldur            x0, [fp, #-0x28]
    // 0xba23a0: stur            x2, [fp, #-0x18]
    // 0xba23a4: StoreField: r2->field_f = r0
    //     0xba23a4: stur            w0, [x2, #0xf]
    // 0xba23a8: r16 = Instance_Spacer
    //     0xba23a8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba23ac: ldr             x16, [x16, #0xf0]
    // 0xba23b0: StoreField: r2->field_13 = r16
    //     0xba23b0: stur            w16, [x2, #0x13]
    // 0xba23b4: ldur            x0, [fp, #-0x30]
    // 0xba23b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xba23b8: stur            w0, [x2, #0x17]
    // 0xba23bc: r1 = <Widget>
    //     0xba23bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba23c0: r0 = AllocateGrowableArray()
    //     0xba23c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba23c4: mov             x1, x0
    // 0xba23c8: ldur            x0, [fp, #-0x18]
    // 0xba23cc: stur            x1, [fp, #-0x20]
    // 0xba23d0: StoreField: r1->field_f = r0
    //     0xba23d0: stur            w0, [x1, #0xf]
    // 0xba23d4: r4 = 6
    //     0xba23d4: movz            x4, #0x6
    // 0xba23d8: StoreField: r1->field_b = r4
    //     0xba23d8: stur            w4, [x1, #0xb]
    // 0xba23dc: r0 = Row()
    //     0xba23dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba23e0: r1 = Instance_Axis
    //     0xba23e0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba23e4: StoreField: r0->field_f = r1
    //     0xba23e4: stur            w1, [x0, #0xf]
    // 0xba23e8: r5 = Instance_MainAxisAlignment
    //     0xba23e8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba23ec: ldr             x5, [x5, #0xa08]
    // 0xba23f0: StoreField: r0->field_13 = r5
    //     0xba23f0: stur            w5, [x0, #0x13]
    // 0xba23f4: r6 = Instance_MainAxisSize
    //     0xba23f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba23f8: ldr             x6, [x6, #0xa10]
    // 0xba23fc: ArrayStore: r0[0] = r6  ; List_4
    //     0xba23fc: stur            w6, [x0, #0x17]
    // 0xba2400: r7 = Instance_CrossAxisAlignment
    //     0xba2400: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba2404: ldr             x7, [x7, #0xa18]
    // 0xba2408: StoreField: r0->field_1b = r7
    //     0xba2408: stur            w7, [x0, #0x1b]
    // 0xba240c: r8 = Instance_VerticalDirection
    //     0xba240c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba2410: ldr             x8, [x8, #0xa20]
    // 0xba2414: StoreField: r0->field_23 = r8
    //     0xba2414: stur            w8, [x0, #0x23]
    // 0xba2418: r9 = Instance_Clip
    //     0xba2418: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba241c: ldr             x9, [x9, #0x38]
    // 0xba2420: StoreField: r0->field_2b = r9
    //     0xba2420: stur            w9, [x0, #0x2b]
    // 0xba2424: StoreField: r0->field_2f = rZR
    //     0xba2424: stur            xzr, [x0, #0x2f]
    // 0xba2428: ldur            x1, [fp, #-0x20]
    // 0xba242c: StoreField: r0->field_b = r1
    //     0xba242c: stur            w1, [x0, #0xb]
    // 0xba2430: b               #0xba2878
    // 0xba2434: mov             x3, x0
    // 0xba2438: r2 = 4
    //     0xba2438: movz            x2, #0x4
    // 0xba243c: r4 = 6
    //     0xba243c: movz            x4, #0x6
    // 0xba2440: r7 = Instance_CrossAxisAlignment
    //     0xba2440: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba2444: ldr             x7, [x7, #0xa18]
    // 0xba2448: r5 = Instance_MainAxisAlignment
    //     0xba2448: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba244c: ldr             x5, [x5, #0xa08]
    // 0xba2450: r6 = Instance_MainAxisSize
    //     0xba2450: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba2454: ldr             x6, [x6, #0xa10]
    // 0xba2458: r1 = Instance_Axis
    //     0xba2458: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba245c: r8 = Instance_VerticalDirection
    //     0xba245c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba2460: ldr             x8, [x8, #0xa20]
    // 0xba2464: r0 = Instance__DeferringMouseCursor
    //     0xba2464: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2468: r9 = Instance_Clip
    //     0xba2468: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba246c: ldr             x9, [x9, #0x38]
    // 0xba2470: d1 = 0.500000
    //     0xba2470: fmov            d1, #0.50000000
    // 0xba2474: d0 = inf
    //     0xba2474: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba2478: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba2478: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba247c: ldr             x0, [x0, #0x1c80]
    //     0xba2480: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba2484: cmp             w0, w16
    //     0xba2488: b.ne            #0xba2494
    //     0xba248c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba2490: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba2494: r0 = GetNavigation.size()
    //     0xba2494: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba2498: LoadField: d0 = r0->field_7
    //     0xba2498: ldur            d0, [x0, #7]
    // 0xba249c: d1 = 0.500000
    //     0xba249c: fmov            d1, #0.50000000
    // 0xba24a0: fmul            d2, d0, d1
    // 0xba24a4: stur            d2, [fp, #-0x40]
    // 0xba24a8: r0 = BoxConstraints()
    //     0xba24a8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba24ac: stur            x0, [fp, #-0x20]
    // 0xba24b0: StoreField: r0->field_7 = rZR
    //     0xba24b0: stur            xzr, [x0, #7]
    // 0xba24b4: ldur            d0, [fp, #-0x40]
    // 0xba24b8: StoreField: r0->field_f = d0
    //     0xba24b8: stur            d0, [x0, #0xf]
    // 0xba24bc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xba24bc: stur            xzr, [x0, #0x17]
    // 0xba24c0: d0 = inf
    //     0xba24c0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba24c4: StoreField: r0->field_1f = d0
    //     0xba24c4: stur            d0, [x0, #0x1f]
    // 0xba24c8: ldur            x2, [fp, #-8]
    // 0xba24cc: LoadField: r1 = r2->field_b
    //     0xba24cc: ldur            w1, [x2, #0xb]
    // 0xba24d0: DecompressPointer r1
    //     0xba24d0: add             x1, x1, HEAP, lsl #32
    // 0xba24d4: cmp             w1, NULL
    // 0xba24d8: b.eq            #0xba2898
    // 0xba24dc: LoadField: r3 = r1->field_f
    //     0xba24dc: ldur            w3, [x1, #0xf]
    // 0xba24e0: DecompressPointer r3
    //     0xba24e0: add             x3, x3, HEAP, lsl #32
    // 0xba24e4: cmp             w3, NULL
    // 0xba24e8: b.ne            #0xba24f4
    // 0xba24ec: r1 = Null
    //     0xba24ec: mov             x1, NULL
    // 0xba24f0: b               #0xba24fc
    // 0xba24f4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xba24f4: ldur            w1, [x3, #0x17]
    // 0xba24f8: DecompressPointer r1
    //     0xba24f8: add             x1, x1, HEAP, lsl #32
    // 0xba24fc: cmp             w1, NULL
    // 0xba2500: b.ne            #0xba250c
    // 0xba2504: r3 = ""
    //     0xba2504: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba2508: b               #0xba2510
    // 0xba250c: mov             x3, x1
    // 0xba2510: ldur            x1, [fp, #-0x10]
    // 0xba2514: stur            x3, [fp, #-0x18]
    // 0xba2518: r0 = of()
    //     0xba2518: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba251c: LoadField: r1 = r0->field_87
    //     0xba251c: ldur            w1, [x0, #0x87]
    // 0xba2520: DecompressPointer r1
    //     0xba2520: add             x1, x1, HEAP, lsl #32
    // 0xba2524: LoadField: r0 = r1->field_2b
    //     0xba2524: ldur            w0, [x1, #0x2b]
    // 0xba2528: DecompressPointer r0
    //     0xba2528: add             x0, x0, HEAP, lsl #32
    // 0xba252c: stur            x0, [fp, #-0x28]
    // 0xba2530: r1 = Instance_Color
    //     0xba2530: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2534: d0 = 0.700000
    //     0xba2534: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2538: ldr             d0, [x17, #0xf48]
    // 0xba253c: r0 = withOpacity()
    //     0xba253c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2540: r16 = 12.000000
    //     0xba2540: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2544: ldr             x16, [x16, #0x9e8]
    // 0xba2548: stp             x0, x16, [SP]
    // 0xba254c: ldur            x1, [fp, #-0x28]
    // 0xba2550: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2550: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2554: ldr             x4, [x4, #0xaa0]
    // 0xba2558: r0 = copyWith()
    //     0xba2558: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba255c: stur            x0, [fp, #-0x28]
    // 0xba2560: r0 = TextSpan()
    //     0xba2560: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2564: mov             x3, x0
    // 0xba2568: ldur            x0, [fp, #-0x18]
    // 0xba256c: stur            x3, [fp, #-0x30]
    // 0xba2570: StoreField: r3->field_b = r0
    //     0xba2570: stur            w0, [x3, #0xb]
    // 0xba2574: r0 = Instance__DeferringMouseCursor
    //     0xba2574: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2578: ArrayStore: r3[0] = r0  ; List_4
    //     0xba2578: stur            w0, [x3, #0x17]
    // 0xba257c: ldur            x1, [fp, #-0x28]
    // 0xba2580: StoreField: r3->field_7 = r1
    //     0xba2580: stur            w1, [x3, #7]
    // 0xba2584: r1 = Null
    //     0xba2584: mov             x1, NULL
    // 0xba2588: r2 = 4
    //     0xba2588: movz            x2, #0x4
    // 0xba258c: r0 = AllocateArray()
    //     0xba258c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2590: stur            x0, [fp, #-0x18]
    // 0xba2594: r16 = " : "
    //     0xba2594: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xba2598: ldr             x16, [x16, #0x680]
    // 0xba259c: StoreField: r0->field_f = r16
    //     0xba259c: stur            w16, [x0, #0xf]
    // 0xba25a0: ldur            x2, [fp, #-8]
    // 0xba25a4: LoadField: r1 = r2->field_13
    //     0xba25a4: ldur            w1, [x2, #0x13]
    // 0xba25a8: DecompressPointer r1
    //     0xba25a8: add             x1, x1, HEAP, lsl #32
    // 0xba25ac: r16 = ", "
    //     0xba25ac: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xba25b0: str             x16, [SP]
    // 0xba25b4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xba25b4: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xba25b8: r0 = join()
    //     0xba25b8: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0xba25bc: ldur            x1, [fp, #-0x18]
    // 0xba25c0: ArrayStore: r1[1] = r0  ; List_4
    //     0xba25c0: add             x25, x1, #0x13
    //     0xba25c4: str             w0, [x25]
    //     0xba25c8: tbz             w0, #0, #0xba25e4
    //     0xba25cc: ldurb           w16, [x1, #-1]
    //     0xba25d0: ldurb           w17, [x0, #-1]
    //     0xba25d4: and             x16, x17, x16, lsr #2
    //     0xba25d8: tst             x16, HEAP, lsr #32
    //     0xba25dc: b.eq            #0xba25e4
    //     0xba25e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba25e4: ldur            x16, [fp, #-0x18]
    // 0xba25e8: str             x16, [SP]
    // 0xba25ec: r0 = _interpolate()
    //     0xba25ec: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba25f0: ldur            x1, [fp, #-0x10]
    // 0xba25f4: stur            x0, [fp, #-0x18]
    // 0xba25f8: r0 = of()
    //     0xba25f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba25fc: LoadField: r1 = r0->field_87
    //     0xba25fc: ldur            w1, [x0, #0x87]
    // 0xba2600: DecompressPointer r1
    //     0xba2600: add             x1, x1, HEAP, lsl #32
    // 0xba2604: LoadField: r0 = r1->field_7
    //     0xba2604: ldur            w0, [x1, #7]
    // 0xba2608: DecompressPointer r0
    //     0xba2608: add             x0, x0, HEAP, lsl #32
    // 0xba260c: stur            x0, [fp, #-0x28]
    // 0xba2610: r1 = Instance_Color
    //     0xba2610: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2614: d0 = 0.700000
    //     0xba2614: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2618: ldr             d0, [x17, #0xf48]
    // 0xba261c: r0 = withOpacity()
    //     0xba261c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2620: r16 = 12.000000
    //     0xba2620: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2624: ldr             x16, [x16, #0x9e8]
    // 0xba2628: stp             x0, x16, [SP]
    // 0xba262c: ldur            x1, [fp, #-0x28]
    // 0xba2630: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2634: ldr             x4, [x4, #0xaa0]
    // 0xba2638: r0 = copyWith()
    //     0xba2638: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba263c: stur            x0, [fp, #-0x28]
    // 0xba2640: r0 = TextSpan()
    //     0xba2640: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2644: mov             x3, x0
    // 0xba2648: ldur            x0, [fp, #-0x18]
    // 0xba264c: stur            x3, [fp, #-0x38]
    // 0xba2650: StoreField: r3->field_b = r0
    //     0xba2650: stur            w0, [x3, #0xb]
    // 0xba2654: r0 = Instance__DeferringMouseCursor
    //     0xba2654: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2658: ArrayStore: r3[0] = r0  ; List_4
    //     0xba2658: stur            w0, [x3, #0x17]
    // 0xba265c: ldur            x1, [fp, #-0x28]
    // 0xba2660: StoreField: r3->field_7 = r1
    //     0xba2660: stur            w1, [x3, #7]
    // 0xba2664: r1 = Null
    //     0xba2664: mov             x1, NULL
    // 0xba2668: r2 = 4
    //     0xba2668: movz            x2, #0x4
    // 0xba266c: r0 = AllocateArray()
    //     0xba266c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2670: mov             x2, x0
    // 0xba2674: ldur            x0, [fp, #-0x30]
    // 0xba2678: stur            x2, [fp, #-0x18]
    // 0xba267c: StoreField: r2->field_f = r0
    //     0xba267c: stur            w0, [x2, #0xf]
    // 0xba2680: ldur            x0, [fp, #-0x38]
    // 0xba2684: StoreField: r2->field_13 = r0
    //     0xba2684: stur            w0, [x2, #0x13]
    // 0xba2688: r1 = <InlineSpan>
    //     0xba2688: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xba268c: ldr             x1, [x1, #0xe40]
    // 0xba2690: r0 = AllocateGrowableArray()
    //     0xba2690: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba2694: mov             x1, x0
    // 0xba2698: ldur            x0, [fp, #-0x18]
    // 0xba269c: stur            x1, [fp, #-0x28]
    // 0xba26a0: StoreField: r1->field_f = r0
    //     0xba26a0: stur            w0, [x1, #0xf]
    // 0xba26a4: r0 = 4
    //     0xba26a4: movz            x0, #0x4
    // 0xba26a8: StoreField: r1->field_b = r0
    //     0xba26a8: stur            w0, [x1, #0xb]
    // 0xba26ac: r0 = TextSpan()
    //     0xba26ac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba26b0: mov             x1, x0
    // 0xba26b4: ldur            x0, [fp, #-0x28]
    // 0xba26b8: stur            x1, [fp, #-0x18]
    // 0xba26bc: StoreField: r1->field_f = r0
    //     0xba26bc: stur            w0, [x1, #0xf]
    // 0xba26c0: r0 = Instance__DeferringMouseCursor
    //     0xba26c0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba26c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xba26c4: stur            w0, [x1, #0x17]
    // 0xba26c8: r0 = RichText()
    //     0xba26c8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba26cc: mov             x1, x0
    // 0xba26d0: ldur            x2, [fp, #-0x18]
    // 0xba26d4: stur            x0, [fp, #-0x18]
    // 0xba26d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba26d8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba26dc: r0 = RichText()
    //     0xba26dc: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba26e0: r0 = ConstrainedBox()
    //     0xba26e0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba26e4: mov             x1, x0
    // 0xba26e8: ldur            x0, [fp, #-0x20]
    // 0xba26ec: stur            x1, [fp, #-0x28]
    // 0xba26f0: StoreField: r1->field_f = r0
    //     0xba26f0: stur            w0, [x1, #0xf]
    // 0xba26f4: ldur            x0, [fp, #-0x18]
    // 0xba26f8: StoreField: r1->field_b = r0
    //     0xba26f8: stur            w0, [x1, #0xb]
    // 0xba26fc: ldur            x0, [fp, #-8]
    // 0xba2700: LoadField: r2 = r0->field_b
    //     0xba2700: ldur            w2, [x0, #0xb]
    // 0xba2704: DecompressPointer r2
    //     0xba2704: add             x2, x2, HEAP, lsl #32
    // 0xba2708: cmp             w2, NULL
    // 0xba270c: b.eq            #0xba289c
    // 0xba2710: LoadField: r0 = r2->field_f
    //     0xba2710: ldur            w0, [x2, #0xf]
    // 0xba2714: DecompressPointer r0
    //     0xba2714: add             x0, x0, HEAP, lsl #32
    // 0xba2718: cmp             w0, NULL
    // 0xba271c: b.ne            #0xba2728
    // 0xba2720: r0 = Null
    //     0xba2720: mov             x0, NULL
    // 0xba2724: b               #0xba2750
    // 0xba2728: LoadField: r2 = r0->field_2b
    //     0xba2728: ldur            w2, [x0, #0x2b]
    // 0xba272c: DecompressPointer r2
    //     0xba272c: add             x2, x2, HEAP, lsl #32
    // 0xba2730: r0 = LoadClassIdInstr(r2)
    //     0xba2730: ldur            x0, [x2, #-1]
    //     0xba2734: ubfx            x0, x0, #0xc, #0x14
    // 0xba2738: str             x2, [SP]
    // 0xba273c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba273c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba2740: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba2740: movz            x17, #0x2700
    //     0xba2744: add             lr, x0, x17
    //     0xba2748: ldr             lr, [x21, lr, lsl #3]
    //     0xba274c: blr             lr
    // 0xba2750: cmp             w0, NULL
    // 0xba2754: b.ne            #0xba2760
    // 0xba2758: r2 = " "
    //     0xba2758: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba275c: b               #0xba2764
    // 0xba2760: mov             x2, x0
    // 0xba2764: ldur            x0, [fp, #-0x28]
    // 0xba2768: ldur            x1, [fp, #-0x10]
    // 0xba276c: stur            x2, [fp, #-8]
    // 0xba2770: r0 = of()
    //     0xba2770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba2774: LoadField: r1 = r0->field_87
    //     0xba2774: ldur            w1, [x0, #0x87]
    // 0xba2778: DecompressPointer r1
    //     0xba2778: add             x1, x1, HEAP, lsl #32
    // 0xba277c: LoadField: r0 = r1->field_2b
    //     0xba277c: ldur            w0, [x1, #0x2b]
    // 0xba2780: DecompressPointer r0
    //     0xba2780: add             x0, x0, HEAP, lsl #32
    // 0xba2784: stur            x0, [fp, #-0x10]
    // 0xba2788: r1 = Instance_Color
    //     0xba2788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba278c: d0 = 0.700000
    //     0xba278c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2790: ldr             d0, [x17, #0xf48]
    // 0xba2794: r0 = withOpacity()
    //     0xba2794: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2798: r16 = 12.000000
    //     0xba2798: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba279c: ldr             x16, [x16, #0x9e8]
    // 0xba27a0: stp             x0, x16, [SP]
    // 0xba27a4: ldur            x1, [fp, #-0x10]
    // 0xba27a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba27a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba27ac: ldr             x4, [x4, #0xaa0]
    // 0xba27b0: r0 = copyWith()
    //     0xba27b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba27b4: stur            x0, [fp, #-0x10]
    // 0xba27b8: r0 = Text()
    //     0xba27b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba27bc: mov             x3, x0
    // 0xba27c0: ldur            x0, [fp, #-8]
    // 0xba27c4: stur            x3, [fp, #-0x18]
    // 0xba27c8: StoreField: r3->field_b = r0
    //     0xba27c8: stur            w0, [x3, #0xb]
    // 0xba27cc: ldur            x0, [fp, #-0x10]
    // 0xba27d0: StoreField: r3->field_13 = r0
    //     0xba27d0: stur            w0, [x3, #0x13]
    // 0xba27d4: r1 = Null
    //     0xba27d4: mov             x1, NULL
    // 0xba27d8: r2 = 6
    //     0xba27d8: movz            x2, #0x6
    // 0xba27dc: r0 = AllocateArray()
    //     0xba27dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba27e0: mov             x2, x0
    // 0xba27e4: ldur            x0, [fp, #-0x28]
    // 0xba27e8: stur            x2, [fp, #-8]
    // 0xba27ec: StoreField: r2->field_f = r0
    //     0xba27ec: stur            w0, [x2, #0xf]
    // 0xba27f0: r16 = Instance_Spacer
    //     0xba27f0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba27f4: ldr             x16, [x16, #0xf0]
    // 0xba27f8: StoreField: r2->field_13 = r16
    //     0xba27f8: stur            w16, [x2, #0x13]
    // 0xba27fc: ldur            x0, [fp, #-0x18]
    // 0xba2800: ArrayStore: r2[0] = r0  ; List_4
    //     0xba2800: stur            w0, [x2, #0x17]
    // 0xba2804: r1 = <Widget>
    //     0xba2804: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba2808: r0 = AllocateGrowableArray()
    //     0xba2808: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba280c: mov             x1, x0
    // 0xba2810: ldur            x0, [fp, #-8]
    // 0xba2814: stur            x1, [fp, #-0x10]
    // 0xba2818: StoreField: r1->field_f = r0
    //     0xba2818: stur            w0, [x1, #0xf]
    // 0xba281c: r0 = 6
    //     0xba281c: movz            x0, #0x6
    // 0xba2820: StoreField: r1->field_b = r0
    //     0xba2820: stur            w0, [x1, #0xb]
    // 0xba2824: r0 = Row()
    //     0xba2824: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba2828: r1 = Instance_Axis
    //     0xba2828: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba282c: StoreField: r0->field_f = r1
    //     0xba282c: stur            w1, [x0, #0xf]
    // 0xba2830: r1 = Instance_MainAxisAlignment
    //     0xba2830: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba2834: ldr             x1, [x1, #0xa08]
    // 0xba2838: StoreField: r0->field_13 = r1
    //     0xba2838: stur            w1, [x0, #0x13]
    // 0xba283c: r1 = Instance_MainAxisSize
    //     0xba283c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba2840: ldr             x1, [x1, #0xa10]
    // 0xba2844: ArrayStore: r0[0] = r1  ; List_4
    //     0xba2844: stur            w1, [x0, #0x17]
    // 0xba2848: r1 = Instance_CrossAxisAlignment
    //     0xba2848: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba284c: ldr             x1, [x1, #0xa18]
    // 0xba2850: StoreField: r0->field_1b = r1
    //     0xba2850: stur            w1, [x0, #0x1b]
    // 0xba2854: r1 = Instance_VerticalDirection
    //     0xba2854: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba2858: ldr             x1, [x1, #0xa20]
    // 0xba285c: StoreField: r0->field_23 = r1
    //     0xba285c: stur            w1, [x0, #0x23]
    // 0xba2860: r1 = Instance_Clip
    //     0xba2860: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba2864: ldr             x1, [x1, #0x38]
    // 0xba2868: StoreField: r0->field_2b = r1
    //     0xba2868: stur            w1, [x0, #0x2b]
    // 0xba286c: StoreField: r0->field_2f = rZR
    //     0xba286c: stur            xzr, [x0, #0x2f]
    // 0xba2870: ldur            x1, [fp, #-0x10]
    // 0xba2874: StoreField: r0->field_b = r1
    //     0xba2874: stur            w1, [x0, #0xb]
    // 0xba2878: LeaveFrame
    //     0xba2878: mov             SP, fp
    //     0xba287c: ldp             fp, lr, [SP], #0x10
    // 0xba2880: ret
    //     0xba2880: ret             
    // 0xba2884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba2884: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba2888: b               #0xba1f84
    // 0xba288c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba288c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba2890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba2890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba2894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba2894: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba2898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba2898: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba289c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba289c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4036, size: 0x14, field offset: 0xc
//   const constructor, 
class BagMultiSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fe88, size: 0x7c
    // 0xc7fe88: EnterFrame
    //     0xc7fe88: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fe8c: mov             fp, SP
    // 0xc7fe90: AllocStack(0x10)
    //     0xc7fe90: sub             SP, SP, #0x10
    // 0xc7fe94: CheckStackOverflow
    //     0xc7fe94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fe98: cmp             SP, x16
    //     0xc7fe9c: b.ls            #0xc7fefc
    // 0xc7fea0: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc7fea0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7fea4: ldr             x0, [x0]
    //     0xc7fea8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7feac: cmp             w0, w16
    //     0xc7feb0: b.ne            #0xc7febc
    //     0xc7feb4: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc7feb8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7febc: r1 = <String>
    //     0xc7febc: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xc7fec0: stur            x0, [fp, #-8]
    // 0xc7fec4: r0 = AllocateGrowableArray()
    //     0xc7fec4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc7fec8: mov             x2, x0
    // 0xc7fecc: ldur            x0, [fp, #-8]
    // 0xc7fed0: stur            x2, [fp, #-0x10]
    // 0xc7fed4: StoreField: r2->field_f = r0
    //     0xc7fed4: stur            w0, [x2, #0xf]
    // 0xc7fed8: StoreField: r2->field_b = rZR
    //     0xc7fed8: stur            wzr, [x2, #0xb]
    // 0xc7fedc: r1 = <BagMultiSelect>
    //     0xc7fedc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d30] TypeArguments: <BagMultiSelect>
    //     0xc7fee0: ldr             x1, [x1, #0xd30]
    // 0xc7fee4: r0 = _BagMultiSelectState()
    //     0xc7fee4: bl              #0xc7ff04  ; Allocate_BagMultiSelectStateStub -> _BagMultiSelectState (size=0x18)
    // 0xc7fee8: ldur            x1, [fp, #-0x10]
    // 0xc7feec: StoreField: r0->field_13 = r1
    //     0xc7feec: stur            w1, [x0, #0x13]
    // 0xc7fef0: LeaveFrame
    //     0xc7fef0: mov             SP, fp
    //     0xc7fef4: ldp             fp, lr, [SP], #0x10
    // 0xc7fef8: ret
    //     0xc7fef8: ret             
    // 0xc7fefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fefc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7ff00: b               #0xc7fea0
  }
}
