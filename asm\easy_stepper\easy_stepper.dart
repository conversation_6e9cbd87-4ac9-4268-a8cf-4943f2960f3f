// lib: easy_stepper, url: package:easy_stepper/easy_stepper.dart

// class id: 1049624, size: 0x8
class :: {
}

// class id: 3205, size: 0x24, field offset: 0x14
class _EasyStepperState extends State<dynamic> {

  late EdgeInsetsGeometry _padding; // offset: 0x20
  late LineStyle lineStyle; // offset: 0x1c
  late int _selectedIndex; // offset: 0x18

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x807aa0, size: 0xd4
    // 0x807aa0: EnterFrame
    //     0x807aa0: stp             fp, lr, [SP, #-0x10]!
    //     0x807aa4: mov             fp, SP
    // 0x807aa8: AllocStack(0x10)
    //     0x807aa8: sub             SP, SP, #0x10
    // 0x807aac: SetupParameters(_EasyStepperState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x807aac: mov             x0, x2
    //     0x807ab0: mov             x4, x1
    //     0x807ab4: mov             x3, x2
    //     0x807ab8: stur            x1, [fp, #-8]
    //     0x807abc: stur            x2, [fp, #-0x10]
    // 0x807ac0: r2 = Null
    //     0x807ac0: mov             x2, NULL
    // 0x807ac4: r1 = Null
    //     0x807ac4: mov             x1, NULL
    // 0x807ac8: r4 = 60
    //     0x807ac8: movz            x4, #0x3c
    // 0x807acc: branchIfSmi(r0, 0x807ad8)
    //     0x807acc: tbz             w0, #0, #0x807ad8
    // 0x807ad0: r4 = LoadClassIdInstr(r0)
    //     0x807ad0: ldur            x4, [x0, #-1]
    //     0x807ad4: ubfx            x4, x4, #0xc, #0x14
    // 0x807ad8: cmp             x4, #0xf71
    // 0x807adc: b.eq            #0x807af4
    // 0x807ae0: r8 = EasyStepper
    //     0x807ae0: add             x8, PP, #0x6b, lsl #12  ; [pp+0x6b458] Type: EasyStepper
    //     0x807ae4: ldr             x8, [x8, #0x458]
    // 0x807ae8: r3 = Null
    //     0x807ae8: add             x3, PP, #0x6b, lsl #12  ; [pp+0x6b460] Null
    //     0x807aec: ldr             x3, [x3, #0x460]
    // 0x807af0: r0 = EasyStepper()
    //     0x807af0: bl              #0x807b74  ; IsType_EasyStepper_Stub
    // 0x807af4: ldur            x3, [fp, #-8]
    // 0x807af8: LoadField: r2 = r3->field_7
    //     0x807af8: ldur            w2, [x3, #7]
    // 0x807afc: DecompressPointer r2
    //     0x807afc: add             x2, x2, HEAP, lsl #32
    // 0x807b00: ldur            x0, [fp, #-0x10]
    // 0x807b04: r1 = Null
    //     0x807b04: mov             x1, NULL
    // 0x807b08: cmp             w2, NULL
    // 0x807b0c: b.eq            #0x807b30
    // 0x807b10: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x807b10: ldur            w4, [x2, #0x17]
    // 0x807b14: DecompressPointer r4
    //     0x807b14: add             x4, x4, HEAP, lsl #32
    // 0x807b18: r8 = X0 bound StatefulWidget
    //     0x807b18: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x807b1c: ldr             x8, [x8, #0x7a0]
    // 0x807b20: LoadField: r9 = r4->field_7
    //     0x807b20: ldur            x9, [x4, #7]
    // 0x807b24: r3 = Null
    //     0x807b24: add             x3, PP, #0x6b, lsl #12  ; [pp+0x6b470] Null
    //     0x807b28: ldr             x3, [x3, #0x470]
    // 0x807b2c: blr             x9
    // 0x807b30: ldur            x1, [fp, #-8]
    // 0x807b34: LoadField: r2 = r1->field_b
    //     0x807b34: ldur            w2, [x1, #0xb]
    // 0x807b38: DecompressPointer r2
    //     0x807b38: add             x2, x2, HEAP, lsl #32
    // 0x807b3c: cmp             w2, NULL
    // 0x807b40: b.eq            #0x807b70
    // 0x807b44: LoadField: r3 = r2->field_b
    //     0x807b44: ldur            w3, [x2, #0xb]
    // 0x807b48: DecompressPointer r3
    //     0x807b48: add             x3, x3, HEAP, lsl #32
    // 0x807b4c: LoadField: r2 = r3->field_b
    //     0x807b4c: ldur            w2, [x3, #0xb]
    // 0x807b50: r3 = LoadInt32Instr(r2)
    //     0x807b50: sbfx            x3, x2, #1, #0x1f
    // 0x807b54: cmp             x3, #0
    // 0x807b58: b.le            #0x807b60
    // 0x807b5c: ArrayStore: r1[0] = rZR  ; List_4
    //     0x807b5c: stur            wzr, [x1, #0x17]
    // 0x807b60: r0 = Null
    //     0x807b60: mov             x0, NULL
    // 0x807b64: LeaveFrame
    //     0x807b64: mov             SP, fp
    //     0x807b68: ldp             fp, lr, [SP], #0x10
    // 0x807b6c: ret
    //     0x807b6c: ret             
    // 0x807b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x807b70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x94b878, size: 0x20c
    // 0x94b878: EnterFrame
    //     0x94b878: stp             fp, lr, [SP, #-0x10]!
    //     0x94b87c: mov             fp, SP
    // 0x94b880: AllocStack(0x10)
    //     0x94b880: sub             SP, SP, #0x10
    // 0x94b884: SetupParameters(_EasyStepperState this /* r1 => r1, fp-0x8 */)
    //     0x94b884: stur            x1, [fp, #-8]
    // 0x94b888: CheckStackOverflow
    //     0x94b888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b88c: cmp             SP, x16
    //     0x94b890: b.ls            #0x94ba6c
    // 0x94b894: LoadField: r0 = r1->field_b
    //     0x94b894: ldur            w0, [x1, #0xb]
    // 0x94b898: DecompressPointer r0
    //     0x94b898: add             x0, x0, HEAP, lsl #32
    // 0x94b89c: cmp             w0, NULL
    // 0x94b8a0: b.eq            #0x94ba74
    // 0x94b8a4: LoadField: r2 = r0->field_c3
    //     0x94b8a4: ldur            w2, [x0, #0xc3]
    // 0x94b8a8: DecompressPointer r2
    //     0x94b8a8: add             x2, x2, HEAP, lsl #32
    // 0x94b8ac: mov             x0, x2
    // 0x94b8b0: StoreField: r1->field_1b = r0
    //     0x94b8b0: stur            w0, [x1, #0x1b]
    //     0x94b8b4: ldurb           w16, [x1, #-1]
    //     0x94b8b8: ldurb           w17, [x0, #-1]
    //     0x94b8bc: and             x16, x17, x16, lsr #2
    //     0x94b8c0: tst             x16, HEAP, lsr #32
    //     0x94b8c4: b.eq            #0x94b8cc
    //     0x94b8c8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94b8cc: ArrayStore: r1[0] = rZR  ; List_4
    //     0x94b8cc: stur            wzr, [x1, #0x17]
    // 0x94b8d0: r0 = ScrollController()
    //     0x94b8d0: bl              #0x675ac8  ; AllocateScrollControllerStub -> ScrollController (size=0x40)
    // 0x94b8d4: mov             x1, x0
    // 0x94b8d8: stur            x0, [fp, #-0x10]
    // 0x94b8dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94b8dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94b8e0: r0 = ScrollController()
    //     0x94b8e0: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94b8e4: ldur            x0, [fp, #-0x10]
    // 0x94b8e8: ldur            x3, [fp, #-8]
    // 0x94b8ec: StoreField: r3->field_13 = r0
    //     0x94b8ec: stur            w0, [x3, #0x13]
    //     0x94b8f0: ldurb           w16, [x3, #-1]
    //     0x94b8f4: ldurb           w17, [x0, #-1]
    //     0x94b8f8: and             x16, x17, x16, lsr #2
    //     0x94b8fc: tst             x16, HEAP, lsr #32
    //     0x94b900: b.eq            #0x94b908
    //     0x94b904: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x94b908: r0 = Instance_EdgeInsetsDirectional
    //     0x94b908: add             x0, PP, #0x6b, lsl #12  ; [pp+0x6b480] Obj!EdgeInsetsDirectional@d569c1
    //     0x94b90c: ldr             x0, [x0, #0x480]
    // 0x94b910: StoreField: r3->field_1f = r0
    //     0x94b910: stur            w0, [x3, #0x1f]
    // 0x94b914: LoadField: r0 = r3->field_b
    //     0x94b914: ldur            w0, [x3, #0xb]
    // 0x94b918: DecompressPointer r0
    //     0x94b918: add             x0, x0, HEAP, lsl #32
    // 0x94b91c: cmp             w0, NULL
    // 0x94b920: b.eq            #0x94ba78
    // 0x94b924: LoadField: r4 = r0->field_b
    //     0x94b924: ldur            w4, [x0, #0xb]
    // 0x94b928: DecompressPointer r4
    //     0x94b928: add             x4, x4, HEAP, lsl #32
    // 0x94b92c: stur            x4, [fp, #-0x10]
    // 0x94b930: r1 = Function '<anonymous closure>':.
    //     0x94b930: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b488] Function: [dart:core] Object::_simpleInstanceOfFalse (0x16f47dc)
    //     0x94b934: ldr             x1, [x1, #0x488]
    // 0x94b938: r2 = Null
    //     0x94b938: mov             x2, NULL
    // 0x94b93c: r0 = AllocateClosure()
    //     0x94b93c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94b940: ldur            x1, [fp, #-0x10]
    // 0x94b944: mov             x2, x0
    // 0x94b948: r0 = any()
    //     0x94b948: bl              #0x7d624c  ; [dart:collection] ListBase::any
    // 0x94b94c: ldur            x0, [fp, #-8]
    // 0x94b950: LoadField: r1 = r0->field_b
    //     0x94b950: ldur            w1, [x0, #0xb]
    // 0x94b954: DecompressPointer r1
    //     0x94b954: add             x1, x1, HEAP, lsl #32
    // 0x94b958: cmp             w1, NULL
    // 0x94b95c: b.eq            #0x94ba7c
    // 0x94b960: LoadField: r1 = r0->field_1f
    //     0x94b960: ldur            w1, [x0, #0x1f]
    // 0x94b964: DecompressPointer r1
    //     0x94b964: add             x1, x1, HEAP, lsl #32
    // 0x94b968: r2 = LoadClassIdInstr(r1)
    //     0x94b968: ldur            x2, [x1, #-1]
    //     0x94b96c: ubfx            x2, x2, #0xc, #0x14
    // 0x94b970: cmp             x2, #0x896
    // 0x94b974: b.ne            #0x94b98c
    // 0x94b978: r2 = Instance_EdgeInsetsDirectional
    //     0x94b978: add             x2, PP, #0x6b, lsl #12  ; [pp+0x6b490] Obj!EdgeInsetsDirectional@d56991
    //     0x94b97c: ldr             x2, [x2, #0x490]
    // 0x94b980: r0 = +()
    //     0x94b980: bl              #0x7424dc  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsDirectional::+
    // 0x94b984: mov             x2, x0
    // 0x94b988: b               #0x94b9c8
    // 0x94b98c: cmp             x2, #0x897
    // 0x94b990: b.ne            #0x94b9a8
    // 0x94b994: r2 = Instance_EdgeInsetsDirectional
    //     0x94b994: add             x2, PP, #0x6b, lsl #12  ; [pp+0x6b490] Obj!EdgeInsetsDirectional@d56991
    //     0x94b998: ldr             x2, [x2, #0x490]
    // 0x94b99c: r0 = add()
    //     0x94b99c: bl              #0x16bcb2c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::add
    // 0x94b9a0: mov             x2, x0
    // 0x94b9a4: b               #0x94b9c8
    // 0x94b9a8: r0 = LoadClassIdInstr(r1)
    //     0x94b9a8: ldur            x0, [x1, #-1]
    //     0x94b9ac: ubfx            x0, x0, #0xc, #0x14
    // 0x94b9b0: r2 = Instance_EdgeInsetsDirectional
    //     0x94b9b0: add             x2, PP, #0x6b, lsl #12  ; [pp+0x6b490] Obj!EdgeInsetsDirectional@d56991
    //     0x94b9b4: ldr             x2, [x2, #0x490]
    // 0x94b9b8: r0 = GDT[cid_x0 + -0xfaa]()
    //     0x94b9b8: sub             lr, x0, #0xfaa
    //     0x94b9bc: ldr             lr, [x21, lr, lsl #3]
    //     0x94b9c0: blr             lr
    // 0x94b9c4: mov             x2, x0
    // 0x94b9c8: ldur            x1, [fp, #-8]
    // 0x94b9cc: mov             x0, x2
    // 0x94b9d0: StoreField: r1->field_1f = r0
    //     0x94b9d0: stur            w0, [x1, #0x1f]
    //     0x94b9d4: ldurb           w16, [x1, #-1]
    //     0x94b9d8: ldurb           w17, [x0, #-1]
    //     0x94b9dc: and             x16, x17, x16, lsr #2
    //     0x94b9e0: tst             x16, HEAP, lsr #32
    //     0x94b9e4: b.eq            #0x94b9ec
    //     0x94b9e8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94b9ec: LoadField: r0 = r1->field_b
    //     0x94b9ec: ldur            w0, [x1, #0xb]
    // 0x94b9f0: DecompressPointer r0
    //     0x94b9f0: add             x0, x0, HEAP, lsl #32
    // 0x94b9f4: cmp             w0, NULL
    // 0x94b9f8: b.eq            #0x94ba80
    // 0x94b9fc: r0 = LoadClassIdInstr(r2)
    //     0x94b9fc: ldur            x0, [x2, #-1]
    //     0x94ba00: ubfx            x0, x0, #0xc, #0x14
    // 0x94ba04: cmp             x0, #0x896
    // 0x94ba08: b.ne            #0x94ba20
    // 0x94ba0c: mov             x1, x2
    // 0x94ba10: r2 = Instance_EdgeInsetsDirectional
    //     0x94ba10: add             x2, PP, #0x54, lsl #12  ; [pp+0x54810] Obj!EdgeInsetsDirectional@d569f1
    //     0x94ba14: ldr             x2, [x2, #0x810]
    // 0x94ba18: r0 = +()
    //     0x94ba18: bl              #0x7424dc  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsDirectional::+
    // 0x94ba1c: b               #0x94ba5c
    // 0x94ba20: cmp             x0, #0x897
    // 0x94ba24: b.ne            #0x94ba3c
    // 0x94ba28: mov             x1, x2
    // 0x94ba2c: r2 = Instance_EdgeInsetsDirectional
    //     0x94ba2c: add             x2, PP, #0x54, lsl #12  ; [pp+0x54810] Obj!EdgeInsetsDirectional@d569f1
    //     0x94ba30: ldr             x2, [x2, #0x810]
    // 0x94ba34: r0 = add()
    //     0x94ba34: bl              #0x16bcb2c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::add
    // 0x94ba38: b               #0x94ba5c
    // 0x94ba3c: r0 = LoadClassIdInstr(r2)
    //     0x94ba3c: ldur            x0, [x2, #-1]
    //     0x94ba40: ubfx            x0, x0, #0xc, #0x14
    // 0x94ba44: mov             x1, x2
    // 0x94ba48: r2 = Instance_EdgeInsetsDirectional
    //     0x94ba48: add             x2, PP, #0x54, lsl #12  ; [pp+0x54810] Obj!EdgeInsetsDirectional@d569f1
    //     0x94ba4c: ldr             x2, [x2, #0x810]
    // 0x94ba50: r0 = GDT[cid_x0 + -0xfaa]()
    //     0x94ba50: sub             lr, x0, #0xfaa
    //     0x94ba54: ldr             lr, [x21, lr, lsl #3]
    //     0x94ba58: blr             lr
    // 0x94ba5c: r0 = Null
    //     0x94ba5c: mov             x0, NULL
    // 0x94ba60: LeaveFrame
    //     0x94ba60: mov             SP, fp
    //     0x94ba64: ldp             fp, lr, [SP], #0x10
    // 0x94ba68: ret
    //     0x94ba68: ret             
    // 0x94ba6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94ba6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94ba70: b               #0x94b894
    // 0x94ba74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94ba74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94ba78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94ba78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94ba7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94ba7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94ba80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94ba80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc182c8, size: 0x2dc
    // 0xc182c8: EnterFrame
    //     0xc182c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc182cc: mov             fp, SP
    // 0xc182d0: AllocStack(0x28)
    //     0xc182d0: sub             SP, SP, #0x28
    // 0xc182d4: SetupParameters(_EasyStepperState this /* r1 => r3, fp-0x18 */)
    //     0xc182d4: mov             x3, x1
    //     0xc182d8: stur            x1, [fp, #-0x18]
    // 0xc182dc: CheckStackOverflow
    //     0xc182dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc182e0: cmp             SP, x16
    //     0xc182e4: b.ls            #0xc18584
    // 0xc182e8: LoadField: r0 = r3->field_b
    //     0xc182e8: ldur            w0, [x3, #0xb]
    // 0xc182ec: DecompressPointer r0
    //     0xc182ec: add             x0, x0, HEAP, lsl #32
    // 0xc182f0: cmp             w0, NULL
    // 0xc182f4: b.eq            #0xc1858c
    // 0xc182f8: LoadField: r1 = r0->field_c3
    //     0xc182f8: ldur            w1, [x0, #0xc3]
    // 0xc182fc: DecompressPointer r1
    //     0xc182fc: add             x1, x1, HEAP, lsl #32
    // 0xc18300: mov             x0, x1
    // 0xc18304: StoreField: r3->field_1b = r0
    //     0xc18304: stur            w0, [x3, #0x1b]
    //     0xc18308: ldurb           w16, [x3, #-1]
    //     0xc1830c: ldurb           w17, [x0, #-1]
    //     0xc18310: and             x16, x17, x16, lsr #2
    //     0xc18314: tst             x16, HEAP, lsr #32
    //     0xc18318: b.eq            #0xc18320
    //     0xc1831c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc18320: r0 = LoadStaticField(0x878)
    //     0xc18320: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc18324: ldr             x0, [x0, #0x10f0]
    // 0xc18328: cmp             w0, NULL
    // 0xc1832c: b.eq            #0xc18590
    // 0xc18330: LoadField: r4 = r0->field_53
    //     0xc18330: ldur            w4, [x0, #0x53]
    // 0xc18334: DecompressPointer r4
    //     0xc18334: add             x4, x4, HEAP, lsl #32
    // 0xc18338: stur            x4, [fp, #-0x10]
    // 0xc1833c: LoadField: r0 = r4->field_7
    //     0xc1833c: ldur            w0, [x4, #7]
    // 0xc18340: DecompressPointer r0
    //     0xc18340: add             x0, x0, HEAP, lsl #32
    // 0xc18344: mov             x2, x3
    // 0xc18348: stur            x0, [fp, #-8]
    // 0xc1834c: r1 = Function '_afterLayout@1307264635':.
    //     0xc1834c: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b3f0] AnonymousClosure: (0xc19090), in [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_afterLayout (0xc190cc)
    //     0xc18350: ldr             x1, [x1, #0x3f0]
    // 0xc18354: r0 = AllocateClosure()
    //     0xc18354: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc18358: ldur            x2, [fp, #-8]
    // 0xc1835c: mov             x3, x0
    // 0xc18360: r1 = Null
    //     0xc18360: mov             x1, NULL
    // 0xc18364: stur            x3, [fp, #-8]
    // 0xc18368: cmp             w2, NULL
    // 0xc1836c: b.eq            #0xc1838c
    // 0xc18370: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc18370: ldur            w4, [x2, #0x17]
    // 0xc18374: DecompressPointer r4
    //     0xc18374: add             x4, x4, HEAP, lsl #32
    // 0xc18378: r8 = X0
    //     0xc18378: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xc1837c: LoadField: r9 = r4->field_7
    //     0xc1837c: ldur            x9, [x4, #7]
    // 0xc18380: r3 = Null
    //     0xc18380: add             x3, PP, #0x6b, lsl #12  ; [pp+0x6b3f8] Null
    //     0xc18384: ldr             x3, [x3, #0x3f8]
    // 0xc18388: blr             x9
    // 0xc1838c: ldur            x0, [fp, #-0x10]
    // 0xc18390: LoadField: r1 = r0->field_b
    //     0xc18390: ldur            w1, [x0, #0xb]
    // 0xc18394: LoadField: r2 = r0->field_f
    //     0xc18394: ldur            w2, [x0, #0xf]
    // 0xc18398: DecompressPointer r2
    //     0xc18398: add             x2, x2, HEAP, lsl #32
    // 0xc1839c: LoadField: r3 = r2->field_b
    //     0xc1839c: ldur            w3, [x2, #0xb]
    // 0xc183a0: r2 = LoadInt32Instr(r1)
    //     0xc183a0: sbfx            x2, x1, #1, #0x1f
    // 0xc183a4: stur            x2, [fp, #-0x20]
    // 0xc183a8: r1 = LoadInt32Instr(r3)
    //     0xc183a8: sbfx            x1, x3, #1, #0x1f
    // 0xc183ac: cmp             x2, x1
    // 0xc183b0: b.ne            #0xc183bc
    // 0xc183b4: mov             x1, x0
    // 0xc183b8: r0 = _growToNextCapacity()
    //     0xc183b8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc183bc: ldur            x3, [fp, #-0x18]
    // 0xc183c0: ldur            x0, [fp, #-0x10]
    // 0xc183c4: ldur            x2, [fp, #-0x20]
    // 0xc183c8: add             x1, x2, #1
    // 0xc183cc: lsl             x4, x1, #1
    // 0xc183d0: StoreField: r0->field_b = r4
    //     0xc183d0: stur            w4, [x0, #0xb]
    // 0xc183d4: LoadField: r1 = r0->field_f
    //     0xc183d4: ldur            w1, [x0, #0xf]
    // 0xc183d8: DecompressPointer r1
    //     0xc183d8: add             x1, x1, HEAP, lsl #32
    // 0xc183dc: ldur            x0, [fp, #-8]
    // 0xc183e0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc183e0: add             x25, x1, x2, lsl #2
    //     0xc183e4: add             x25, x25, #0xf
    //     0xc183e8: str             w0, [x25]
    //     0xc183ec: tbz             w0, #0, #0xc18408
    //     0xc183f0: ldurb           w16, [x1, #-1]
    //     0xc183f4: ldurb           w17, [x0, #-1]
    //     0xc183f8: and             x16, x17, x16, lsr #2
    //     0xc183fc: tst             x16, HEAP, lsr #32
    //     0xc18400: b.eq            #0xc18408
    //     0xc18404: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc18408: LoadField: r0 = r3->field_b
    //     0xc18408: ldur            w0, [x3, #0xb]
    // 0xc1840c: DecompressPointer r0
    //     0xc1840c: add             x0, x0, HEAP, lsl #32
    // 0xc18410: cmp             w0, NULL
    // 0xc18414: b.eq            #0xc18594
    // 0xc18418: LoadField: r0 = r3->field_13
    //     0xc18418: ldur            w0, [x3, #0x13]
    // 0xc1841c: DecompressPointer r0
    //     0xc1841c: add             x0, x0, HEAP, lsl #32
    // 0xc18420: stur            x0, [fp, #-0x10]
    // 0xc18424: LoadField: r2 = r3->field_1f
    //     0xc18424: ldur            w2, [x3, #0x1f]
    // 0xc18428: DecompressPointer r2
    //     0xc18428: add             x2, x2, HEAP, lsl #32
    // 0xc1842c: r16 = Sentinel
    //     0xc1842c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18430: cmp             w2, w16
    // 0xc18434: b.eq            #0xc18598
    // 0xc18438: mov             x1, x3
    // 0xc1843c: stur            x2, [fp, #-8]
    // 0xc18440: r0 = _buildEasySteps()
    //     0xc18440: bl              #0xc185bc  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildEasySteps
    // 0xc18444: stur            x0, [fp, #-0x18]
    // 0xc18448: r0 = Row()
    //     0xc18448: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc1844c: mov             x1, x0
    // 0xc18450: r0 = Instance_Axis
    //     0xc18450: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc18454: stur            x1, [fp, #-0x28]
    // 0xc18458: StoreField: r1->field_f = r0
    //     0xc18458: stur            w0, [x1, #0xf]
    // 0xc1845c: r2 = Instance_MainAxisAlignment
    //     0xc1845c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc18460: ldr             x2, [x2, #0xa08]
    // 0xc18464: StoreField: r1->field_13 = r2
    //     0xc18464: stur            w2, [x1, #0x13]
    // 0xc18468: r2 = Instance_MainAxisSize
    //     0xc18468: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc1846c: ldr             x2, [x2, #0xdd0]
    // 0xc18470: ArrayStore: r1[0] = r2  ; List_4
    //     0xc18470: stur            w2, [x1, #0x17]
    // 0xc18474: r2 = Instance_CrossAxisAlignment
    //     0xc18474: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc18478: ldr             x2, [x2, #0xa18]
    // 0xc1847c: StoreField: r1->field_1b = r2
    //     0xc1847c: stur            w2, [x1, #0x1b]
    // 0xc18480: r2 = Instance_VerticalDirection
    //     0xc18480: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc18484: ldr             x2, [x2, #0xa20]
    // 0xc18488: StoreField: r1->field_23 = r2
    //     0xc18488: stur            w2, [x1, #0x23]
    // 0xc1848c: r2 = Instance_Clip
    //     0xc1848c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc18490: ldr             x2, [x2, #0x38]
    // 0xc18494: StoreField: r1->field_2b = r2
    //     0xc18494: stur            w2, [x1, #0x2b]
    // 0xc18498: StoreField: r1->field_2f = rZR
    //     0xc18498: stur            xzr, [x1, #0x2f]
    // 0xc1849c: ldur            x2, [fp, #-0x18]
    // 0xc184a0: StoreField: r1->field_b = r2
    //     0xc184a0: stur            w2, [x1, #0xb]
    // 0xc184a4: r0 = SingleChildScrollView()
    //     0xc184a4: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xc184a8: mov             x3, x0
    // 0xc184ac: r0 = Instance_Axis
    //     0xc184ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc184b0: stur            x3, [fp, #-0x18]
    // 0xc184b4: StoreField: r3->field_b = r0
    //     0xc184b4: stur            w0, [x3, #0xb]
    // 0xc184b8: r0 = false
    //     0xc184b8: add             x0, NULL, #0x30  ; false
    // 0xc184bc: StoreField: r3->field_f = r0
    //     0xc184bc: stur            w0, [x3, #0xf]
    // 0xc184c0: ldur            x0, [fp, #-8]
    // 0xc184c4: StoreField: r3->field_13 = r0
    //     0xc184c4: stur            w0, [x3, #0x13]
    // 0xc184c8: r0 = Instance_ClampingScrollPhysics
    //     0xc184c8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d58] Obj!ClampingScrollPhysics@d558d1
    //     0xc184cc: ldr             x0, [x0, #0xd58]
    // 0xc184d0: StoreField: r3->field_1f = r0
    //     0xc184d0: stur            w0, [x3, #0x1f]
    // 0xc184d4: ldur            x0, [fp, #-0x10]
    // 0xc184d8: ArrayStore: r3[0] = r0  ; List_4
    //     0xc184d8: stur            w0, [x3, #0x17]
    // 0xc184dc: ldur            x0, [fp, #-0x28]
    // 0xc184e0: StoreField: r3->field_23 = r0
    //     0xc184e0: stur            w0, [x3, #0x23]
    // 0xc184e4: r0 = Instance_DragStartBehavior
    //     0xc184e4: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xc184e8: StoreField: r3->field_27 = r0
    //     0xc184e8: stur            w0, [x3, #0x27]
    // 0xc184ec: r0 = Instance_Clip
    //     0xc184ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc184f0: ldr             x0, [x0, #0x7e0]
    // 0xc184f4: StoreField: r3->field_2b = r0
    //     0xc184f4: stur            w0, [x3, #0x2b]
    // 0xc184f8: r0 = Instance_HitTestBehavior
    //     0xc184f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xc184fc: ldr             x0, [x0, #0x288]
    // 0xc18500: StoreField: r3->field_2f = r0
    //     0xc18500: stur            w0, [x3, #0x2f]
    // 0xc18504: r1 = Function '<anonymous closure>':.
    //     0xc18504: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b408] AnonymousClosure: (0xc1904c), in [package:easy_stepper/easy_stepper.dart] _EasyStepperState::build (0xc182c8)
    //     0xc18508: ldr             x1, [x1, #0x408]
    // 0xc1850c: r2 = Null
    //     0xc1850c: mov             x2, NULL
    // 0xc18510: r0 = AllocateClosure()
    //     0xc18510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc18514: r1 = <OverscrollIndicatorNotification>
    //     0xc18514: add             x1, PP, #0x65, lsl #12  ; [pp+0x656c0] TypeArguments: <OverscrollIndicatorNotification>
    //     0xc18518: ldr             x1, [x1, #0x6c0]
    // 0xc1851c: stur            x0, [fp, #-8]
    // 0xc18520: r0 = NotificationListener()
    //     0xc18520: bl              #0x81c03c  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xc18524: mov             x1, x0
    // 0xc18528: ldur            x0, [fp, #-8]
    // 0xc1852c: stur            x1, [fp, #-0x10]
    // 0xc18530: StoreField: r1->field_13 = r0
    //     0xc18530: stur            w0, [x1, #0x13]
    // 0xc18534: ldur            x0, [fp, #-0x18]
    // 0xc18538: StoreField: r1->field_b = r0
    //     0xc18538: stur            w0, [x1, #0xb]
    // 0xc1853c: r0 = Align()
    //     0xc1853c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc18540: mov             x1, x0
    // 0xc18544: r0 = Instance_Alignment
    //     0xc18544: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xc18548: ldr             x0, [x0, #0xf98]
    // 0xc1854c: stur            x1, [fp, #-8]
    // 0xc18550: StoreField: r1->field_f = r0
    //     0xc18550: stur            w0, [x1, #0xf]
    // 0xc18554: ldur            x0, [fp, #-0x10]
    // 0xc18558: StoreField: r1->field_b = r0
    //     0xc18558: stur            w0, [x1, #0xb]
    // 0xc1855c: r0 = CustomScrollBehavior()
    //     0xc1855c: bl              #0xc185b0  ; AllocateCustomScrollBehaviorStub -> CustomScrollBehavior (size=0x8)
    // 0xc18560: stur            x0, [fp, #-0x10]
    // 0xc18564: r0 = ScrollConfiguration()
    //     0xc18564: bl              #0xc185a4  ; AllocateScrollConfigurationStub -> ScrollConfiguration (size=0x14)
    // 0xc18568: ldur            x1, [fp, #-0x10]
    // 0xc1856c: StoreField: r0->field_f = r1
    //     0xc1856c: stur            w1, [x0, #0xf]
    // 0xc18570: ldur            x1, [fp, #-8]
    // 0xc18574: StoreField: r0->field_b = r1
    //     0xc18574: stur            w1, [x0, #0xb]
    // 0xc18578: LeaveFrame
    //     0xc18578: mov             SP, fp
    //     0xc1857c: ldp             fp, lr, [SP], #0x10
    // 0xc18580: ret
    //     0xc18580: ret             
    // 0xc18584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18584: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18588: b               #0xc182e8
    // 0xc1858c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1858c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18590: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18598: r9 = _padding
    //     0xc18598: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b410] Field <_EasyStepperState@1307264635._padding@1307264635>: late (offset: 0x20)
    //     0xc1859c: ldr             x9, [x9, #0x410]
    // 0xc185a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc185a0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildEasySteps(/* No info */) {
    // ** addr: 0xc185bc, size: 0x17c
    // 0xc185bc: EnterFrame
    //     0xc185bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc185c0: mov             fp, SP
    // 0xc185c4: AllocStack(0x40)
    //     0xc185c4: sub             SP, SP, #0x40
    // 0xc185c8: SetupParameters(_EasyStepperState this /* r1 => r1, fp-0x8 */)
    //     0xc185c8: stur            x1, [fp, #-8]
    // 0xc185cc: CheckStackOverflow
    //     0xc185cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc185d0: cmp             SP, x16
    //     0xc185d4: b.ls            #0xc18724
    // 0xc185d8: r1 = 1
    //     0xc185d8: movz            x1, #0x1
    // 0xc185dc: r0 = AllocateContext()
    //     0xc185dc: bl              #0x16f6108  ; AllocateContextStub
    // 0xc185e0: mov             x1, x0
    // 0xc185e4: ldur            x0, [fp, #-8]
    // 0xc185e8: StoreField: r1->field_f = r0
    //     0xc185e8: stur            w0, [x1, #0xf]
    // 0xc185ec: LoadField: r2 = r0->field_b
    //     0xc185ec: ldur            w2, [x0, #0xb]
    // 0xc185f0: DecompressPointer r2
    //     0xc185f0: add             x2, x2, HEAP, lsl #32
    // 0xc185f4: cmp             w2, NULL
    // 0xc185f8: b.eq            #0xc1872c
    // 0xc185fc: LoadField: r0 = r2->field_b
    //     0xc185fc: ldur            w0, [x2, #0xb]
    // 0xc18600: DecompressPointer r0
    //     0xc18600: add             x0, x0, HEAP, lsl #32
    // 0xc18604: LoadField: r3 = r0->field_b
    //     0xc18604: ldur            w3, [x0, #0xb]
    // 0xc18608: mov             x2, x1
    // 0xc1860c: stur            x3, [fp, #-8]
    // 0xc18610: r1 = Function '<anonymous closure>':.
    //     0xc18610: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b428] AnonymousClosure: (0xc18738), in [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildEasySteps (0xc185bc)
    //     0xc18614: ldr             x1, [x1, #0x428]
    // 0xc18618: r0 = AllocateClosure()
    //     0xc18618: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1861c: mov             x3, x0
    // 0xc18620: ldur            x0, [fp, #-8]
    // 0xc18624: stur            x3, [fp, #-0x10]
    // 0xc18628: r2 = LoadInt32Instr(r0)
    //     0xc18628: sbfx            x2, x0, #1, #0x1f
    // 0xc1862c: r1 = <Widget>
    //     0xc1862c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc18630: r0 = _GrowableList()
    //     0xc18630: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc18634: mov             x1, x0
    // 0xc18638: stur            x1, [fp, #-0x28]
    // 0xc1863c: LoadField: r0 = r1->field_b
    //     0xc1863c: ldur            w0, [x1, #0xb]
    // 0xc18640: r2 = LoadInt32Instr(r0)
    //     0xc18640: sbfx            x2, x0, #1, #0x1f
    // 0xc18644: stur            x2, [fp, #-0x20]
    // 0xc18648: LoadField: r3 = r1->field_f
    //     0xc18648: ldur            w3, [x1, #0xf]
    // 0xc1864c: DecompressPointer r3
    //     0xc1864c: add             x3, x3, HEAP, lsl #32
    // 0xc18650: stur            x3, [fp, #-8]
    // 0xc18654: r4 = 0
    //     0xc18654: movz            x4, #0
    // 0xc18658: stur            x4, [fp, #-0x18]
    // 0xc1865c: CheckStackOverflow
    //     0xc1865c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18660: cmp             SP, x16
    //     0xc18664: b.ls            #0xc18730
    // 0xc18668: cmp             x4, x2
    // 0xc1866c: b.ge            #0xc18714
    // 0xc18670: lsl             x0, x4, #1
    // 0xc18674: ldur            x16, [fp, #-0x10]
    // 0xc18678: stp             x0, x16, [SP]
    // 0xc1867c: ldur            x0, [fp, #-0x10]
    // 0xc18680: ClosureCall
    //     0xc18680: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xc18684: ldur            x2, [x0, #0x1f]
    //     0xc18688: blr             x2
    // 0xc1868c: mov             x3, x0
    // 0xc18690: r2 = Null
    //     0xc18690: mov             x2, NULL
    // 0xc18694: r1 = Null
    //     0xc18694: mov             x1, NULL
    // 0xc18698: stur            x3, [fp, #-0x30]
    // 0xc1869c: r4 = 60
    //     0xc1869c: movz            x4, #0x3c
    // 0xc186a0: branchIfSmi(r0, 0xc186ac)
    //     0xc186a0: tbz             w0, #0, #0xc186ac
    // 0xc186a4: r4 = LoadClassIdInstr(r0)
    //     0xc186a4: ldur            x4, [x0, #-1]
    //     0xc186a8: ubfx            x4, x4, #0xc, #0x14
    // 0xc186ac: sub             x4, x4, #0xe60
    // 0xc186b0: cmp             x4, #0x464
    // 0xc186b4: b.ls            #0xc186cc
    // 0xc186b8: r8 = Widget
    //     0xc186b8: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xc186bc: ldr             x8, [x8, #0xe68]
    // 0xc186c0: r3 = Null
    //     0xc186c0: add             x3, PP, #0x6b, lsl #12  ; [pp+0x6b430] Null
    //     0xc186c4: ldr             x3, [x3, #0x430]
    // 0xc186c8: r0 = Widget()
    //     0xc186c8: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xc186cc: ldur            x1, [fp, #-8]
    // 0xc186d0: ldur            x0, [fp, #-0x30]
    // 0xc186d4: ldur            x2, [fp, #-0x18]
    // 0xc186d8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc186d8: add             x25, x1, x2, lsl #2
    //     0xc186dc: add             x25, x25, #0xf
    //     0xc186e0: str             w0, [x25]
    //     0xc186e4: tbz             w0, #0, #0xc18700
    //     0xc186e8: ldurb           w16, [x1, #-1]
    //     0xc186ec: ldurb           w17, [x0, #-1]
    //     0xc186f0: and             x16, x17, x16, lsr #2
    //     0xc186f4: tst             x16, HEAP, lsr #32
    //     0xc186f8: b.eq            #0xc18700
    //     0xc186fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc18700: add             x4, x2, #1
    // 0xc18704: ldur            x1, [fp, #-0x28]
    // 0xc18708: ldur            x3, [fp, #-8]
    // 0xc1870c: ldur            x2, [fp, #-0x20]
    // 0xc18710: b               #0xc18658
    // 0xc18714: ldur            x0, [fp, #-0x28]
    // 0xc18718: LeaveFrame
    //     0xc18718: mov             SP, fp
    //     0xc1871c: ldp             fp, lr, [SP], #0x10
    // 0xc18720: ret
    //     0xc18720: ret             
    // 0xc18724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18724: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18728: b               #0xc185d8
    // 0xc1872c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1872c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18730: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18734: b               #0xc18668
  }
  [closure] Flex <anonymous closure>(dynamic, int) {
    // ** addr: 0xc18738, size: 0x130
    // 0xc18738: EnterFrame
    //     0xc18738: stp             fp, lr, [SP, #-0x10]!
    //     0xc1873c: mov             fp, SP
    // 0xc18740: AllocStack(0x20)
    //     0xc18740: sub             SP, SP, #0x20
    // 0xc18744: SetupParameters()
    //     0xc18744: ldr             x0, [fp, #0x18]
    //     0xc18748: ldur            w3, [x0, #0x17]
    //     0xc1874c: add             x3, x3, HEAP, lsl #32
    //     0xc18750: stur            x3, [fp, #-0x10]
    // 0xc18754: CheckStackOverflow
    //     0xc18754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18758: cmp             SP, x16
    //     0xc1875c: b.ls            #0xc1885c
    // 0xc18760: LoadField: r1 = r3->field_f
    //     0xc18760: ldur            w1, [x3, #0xf]
    // 0xc18764: DecompressPointer r1
    //     0xc18764: add             x1, x1, HEAP, lsl #32
    // 0xc18768: LoadField: r0 = r1->field_b
    //     0xc18768: ldur            w0, [x1, #0xb]
    // 0xc1876c: DecompressPointer r0
    //     0xc1876c: add             x0, x0, HEAP, lsl #32
    // 0xc18770: cmp             w0, NULL
    // 0xc18774: b.eq            #0xc18864
    // 0xc18778: ldr             x0, [fp, #0x10]
    // 0xc1877c: r4 = LoadInt32Instr(r0)
    //     0xc1877c: sbfx            x4, x0, #1, #0x1f
    //     0xc18780: tbz             w0, #0, #0xc18788
    //     0xc18784: ldur            x4, [x0, #7]
    // 0xc18788: mov             x2, x4
    // 0xc1878c: stur            x4, [fp, #-8]
    // 0xc18790: r0 = _buildStep()
    //     0xc18790: bl              #0xc18ca0  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildStep
    // 0xc18794: mov             x4, x0
    // 0xc18798: ldur            x0, [fp, #-0x10]
    // 0xc1879c: stur            x4, [fp, #-0x18]
    // 0xc187a0: LoadField: r1 = r0->field_f
    //     0xc187a0: ldur            w1, [x0, #0xf]
    // 0xc187a4: DecompressPointer r1
    //     0xc187a4: add             x1, x1, HEAP, lsl #32
    // 0xc187a8: ldur            x2, [fp, #-8]
    // 0xc187ac: r3 = Instance_Axis
    //     0xc187ac: ldr             x3, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc187b0: r0 = _buildLine()
    //     0xc187b0: bl              #0xc18868  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildLine
    // 0xc187b4: r1 = Null
    //     0xc187b4: mov             x1, NULL
    // 0xc187b8: r2 = 4
    //     0xc187b8: movz            x2, #0x4
    // 0xc187bc: stur            x0, [fp, #-0x10]
    // 0xc187c0: r0 = AllocateArray()
    //     0xc187c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc187c4: mov             x2, x0
    // 0xc187c8: ldur            x0, [fp, #-0x18]
    // 0xc187cc: stur            x2, [fp, #-0x20]
    // 0xc187d0: StoreField: r2->field_f = r0
    //     0xc187d0: stur            w0, [x2, #0xf]
    // 0xc187d4: ldur            x0, [fp, #-0x10]
    // 0xc187d8: StoreField: r2->field_13 = r0
    //     0xc187d8: stur            w0, [x2, #0x13]
    // 0xc187dc: r1 = <Widget>
    //     0xc187dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc187e0: r0 = AllocateGrowableArray()
    //     0xc187e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc187e4: mov             x1, x0
    // 0xc187e8: ldur            x0, [fp, #-0x20]
    // 0xc187ec: stur            x1, [fp, #-0x10]
    // 0xc187f0: StoreField: r1->field_f = r0
    //     0xc187f0: stur            w0, [x1, #0xf]
    // 0xc187f4: r0 = 4
    //     0xc187f4: movz            x0, #0x4
    // 0xc187f8: StoreField: r1->field_b = r0
    //     0xc187f8: stur            w0, [x1, #0xb]
    // 0xc187fc: r0 = Row()
    //     0xc187fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc18800: r1 = Instance_Axis
    //     0xc18800: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc18804: StoreField: r0->field_f = r1
    //     0xc18804: stur            w1, [x0, #0xf]
    // 0xc18808: r1 = Instance_MainAxisAlignment
    //     0xc18808: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc1880c: ldr             x1, [x1, #0xa08]
    // 0xc18810: StoreField: r0->field_13 = r1
    //     0xc18810: stur            w1, [x0, #0x13]
    // 0xc18814: r1 = Instance_MainAxisSize
    //     0xc18814: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc18818: ldr             x1, [x1, #0xa10]
    // 0xc1881c: ArrayStore: r0[0] = r1  ; List_4
    //     0xc1881c: stur            w1, [x0, #0x17]
    // 0xc18820: r1 = Instance_CrossAxisAlignment
    //     0xc18820: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc18824: ldr             x1, [x1, #0x890]
    // 0xc18828: StoreField: r0->field_1b = r1
    //     0xc18828: stur            w1, [x0, #0x1b]
    // 0xc1882c: r1 = Instance_VerticalDirection
    //     0xc1882c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc18830: ldr             x1, [x1, #0xa20]
    // 0xc18834: StoreField: r0->field_23 = r1
    //     0xc18834: stur            w1, [x0, #0x23]
    // 0xc18838: r1 = Instance_Clip
    //     0xc18838: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc1883c: ldr             x1, [x1, #0x38]
    // 0xc18840: StoreField: r0->field_2b = r1
    //     0xc18840: stur            w1, [x0, #0x2b]
    // 0xc18844: StoreField: r0->field_2f = rZR
    //     0xc18844: stur            xzr, [x0, #0x2f]
    // 0xc18848: ldur            x1, [fp, #-0x10]
    // 0xc1884c: StoreField: r0->field_b = r1
    //     0xc1884c: stur            w1, [x0, #0xb]
    // 0xc18850: LeaveFrame
    //     0xc18850: mov             SP, fp
    //     0xc18854: ldp             fp, lr, [SP], #0x10
    // 0xc18858: ret
    //     0xc18858: ret             
    // 0xc1885c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1885c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18860: b               #0xc18760
    // 0xc18864: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18864: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildLine(/* No info */) {
    // ** addr: 0xc18868, size: 0x218
    // 0xc18868: EnterFrame
    //     0xc18868: stp             fp, lr, [SP, #-0x10]!
    //     0xc1886c: mov             fp, SP
    // 0xc18870: AllocStack(0x40)
    //     0xc18870: sub             SP, SP, #0x40
    // 0xc18874: SetupParameters(_EasyStepperState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xc18874: stur            x1, [fp, #-8]
    //     0xc18878: stur            x2, [fp, #-0x10]
    //     0xc1887c: stur            x3, [fp, #-0x18]
    // 0xc18880: CheckStackOverflow
    //     0xc18880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18884: cmp             SP, x16
    //     0xc18888: b.ls            #0xc18a54
    // 0xc1888c: LoadField: r0 = r1->field_b
    //     0xc1888c: ldur            w0, [x1, #0xb]
    // 0xc18890: DecompressPointer r0
    //     0xc18890: add             x0, x0, HEAP, lsl #32
    // 0xc18894: cmp             w0, NULL
    // 0xc18898: b.eq            #0xc18a5c
    // 0xc1889c: LoadField: r4 = r0->field_b
    //     0xc1889c: ldur            w4, [x0, #0xb]
    // 0xc188a0: DecompressPointer r4
    //     0xc188a0: add             x4, x4, HEAP, lsl #32
    // 0xc188a4: LoadField: r0 = r4->field_b
    //     0xc188a4: ldur            w0, [x4, #0xb]
    // 0xc188a8: r4 = LoadInt32Instr(r0)
    //     0xc188a8: sbfx            x4, x0, #1, #0x1f
    // 0xc188ac: sub             x0, x4, #1
    // 0xc188b0: cmp             x2, x0
    // 0xc188b4: b.ge            #0xc18a40
    // 0xc188b8: r16 = Instance_Axis
    //     0xc188b8: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc188bc: cmp             w3, w16
    // 0xc188c0: b.ne            #0xc188e0
    // 0xc188c4: LoadField: r0 = r1->field_1b
    //     0xc188c4: ldur            w0, [x1, #0x1b]
    // 0xc188c8: DecompressPointer r0
    //     0xc188c8: add             x0, x0, HEAP, lsl #32
    // 0xc188cc: r16 = Sentinel
    //     0xc188cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc188d0: cmp             w0, w16
    // 0xc188d4: b.eq            #0xc18a60
    // 0xc188d8: d0 = 9.000000
    //     0xc188d8: fmov            d0, #9.00000000
    // 0xc188dc: b               #0xc188e4
    // 0xc188e0: d0 = 0.000000
    //     0xc188e0: eor             v0.16b, v0.16b, v0.16b
    // 0xc188e4: stur            d0, [fp, #-0x40]
    // 0xc188e8: r16 = Instance_Axis
    //     0xc188e8: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc188ec: cmp             w3, w16
    // 0xc188f0: b.ne            #0xc188fc
    // 0xc188f4: d1 = 10.000000
    //     0xc188f4: fmov            d1, #10.00000000
    // 0xc188f8: b               #0xc18900
    // 0xc188fc: d1 = 0.000000
    //     0xc188fc: eor             v1.16b, v1.16b, v1.16b
    // 0xc18900: stur            d1, [fp, #-0x38]
    // 0xc18904: r0 = EdgeInsets()
    //     0xc18904: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xc18908: stur            x0, [fp, #-0x20]
    // 0xc1890c: StoreField: r0->field_7 = rZR
    //     0xc1890c: stur            xzr, [x0, #7]
    // 0xc18910: ldur            d0, [fp, #-0x40]
    // 0xc18914: StoreField: r0->field_f = d0
    //     0xc18914: stur            d0, [x0, #0xf]
    // 0xc18918: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc18918: stur            xzr, [x0, #0x17]
    // 0xc1891c: ldur            d0, [fp, #-0x38]
    // 0xc18920: StoreField: r0->field_1f = d0
    //     0xc18920: stur            d0, [x0, #0x1f]
    // 0xc18924: ldur            x4, [fp, #-8]
    // 0xc18928: LoadField: r1 = r4->field_1b
    //     0xc18928: ldur            w1, [x4, #0x1b]
    // 0xc1892c: DecompressPointer r1
    //     0xc1892c: add             x1, x1, HEAP, lsl #32
    // 0xc18930: r16 = Sentinel
    //     0xc18930: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18934: cmp             w1, w16
    // 0xc18938: b.eq            #0xc18a6c
    // 0xc1893c: mov             x1, x4
    // 0xc18940: ldur            x2, [fp, #-0x10]
    // 0xc18944: ldur            x3, [fp, #-0x18]
    // 0xc18948: r0 = _buildBaseLine()
    //     0xc18948: bl              #0xc18a80  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildBaseLine
    // 0xc1894c: stur            x0, [fp, #-0x28]
    // 0xc18950: r0 = Padding()
    //     0xc18950: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc18954: mov             x3, x0
    // 0xc18958: ldur            x0, [fp, #-0x20]
    // 0xc1895c: stur            x3, [fp, #-0x30]
    // 0xc18960: StoreField: r3->field_f = r0
    //     0xc18960: stur            w0, [x3, #0xf]
    // 0xc18964: ldur            x0, [fp, #-0x28]
    // 0xc18968: StoreField: r3->field_b = r0
    //     0xc18968: stur            w0, [x3, #0xb]
    // 0xc1896c: r1 = Null
    //     0xc1896c: mov             x1, NULL
    // 0xc18970: r2 = 2
    //     0xc18970: movz            x2, #0x2
    // 0xc18974: r0 = AllocateArray()
    //     0xc18974: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc18978: mov             x2, x0
    // 0xc1897c: ldur            x0, [fp, #-0x30]
    // 0xc18980: stur            x2, [fp, #-0x20]
    // 0xc18984: StoreField: r2->field_f = r0
    //     0xc18984: stur            w0, [x2, #0xf]
    // 0xc18988: r1 = <Widget>
    //     0xc18988: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc1898c: r0 = AllocateGrowableArray()
    //     0xc1898c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc18990: mov             x2, x0
    // 0xc18994: ldur            x0, [fp, #-0x20]
    // 0xc18998: stur            x2, [fp, #-0x28]
    // 0xc1899c: StoreField: r2->field_f = r0
    //     0xc1899c: stur            w0, [x2, #0xf]
    // 0xc189a0: r0 = 2
    //     0xc189a0: movz            x0, #0x2
    // 0xc189a4: StoreField: r2->field_b = r0
    //     0xc189a4: stur            w0, [x2, #0xb]
    // 0xc189a8: ldur            x0, [fp, #-0x18]
    // 0xc189ac: r16 = Instance_Axis
    //     0xc189ac: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc189b0: cmp             w0, w16
    // 0xc189b4: b.ne            #0xc189e8
    // 0xc189b8: ldur            x0, [fp, #-8]
    // 0xc189bc: LoadField: r1 = r0->field_b
    //     0xc189bc: ldur            w1, [x0, #0xb]
    // 0xc189c0: DecompressPointer r1
    //     0xc189c0: add             x1, x1, HEAP, lsl #32
    // 0xc189c4: cmp             w1, NULL
    // 0xc189c8: b.eq            #0xc18a78
    // 0xc189cc: LoadField: r0 = r1->field_b
    //     0xc189cc: ldur            w0, [x1, #0xb]
    // 0xc189d0: DecompressPointer r0
    //     0xc189d0: add             x0, x0, HEAP, lsl #32
    // 0xc189d4: LoadField: r1 = r0->field_b
    //     0xc189d4: ldur            w1, [x0, #0xb]
    // 0xc189d8: r0 = LoadInt32Instr(r1)
    //     0xc189d8: sbfx            x0, x1, #1, #0x1f
    // 0xc189dc: ldur            x1, [fp, #-0x10]
    // 0xc189e0: cmp             x1, x0
    // 0xc189e4: b.hs            #0xc18a7c
    // 0xc189e8: r0 = Column()
    //     0xc189e8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc189ec: r1 = Instance_Axis
    //     0xc189ec: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc189f0: StoreField: r0->field_f = r1
    //     0xc189f0: stur            w1, [x0, #0xf]
    // 0xc189f4: r1 = Instance_MainAxisAlignment
    //     0xc189f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc189f8: ldr             x1, [x1, #0xa08]
    // 0xc189fc: StoreField: r0->field_13 = r1
    //     0xc189fc: stur            w1, [x0, #0x13]
    // 0xc18a00: r1 = Instance_MainAxisSize
    //     0xc18a00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc18a04: ldr             x1, [x1, #0xa10]
    // 0xc18a08: ArrayStore: r0[0] = r1  ; List_4
    //     0xc18a08: stur            w1, [x0, #0x17]
    // 0xc18a0c: r1 = Instance_CrossAxisAlignment
    //     0xc18a0c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc18a10: ldr             x1, [x1, #0xa18]
    // 0xc18a14: StoreField: r0->field_1b = r1
    //     0xc18a14: stur            w1, [x0, #0x1b]
    // 0xc18a18: r1 = Instance_VerticalDirection
    //     0xc18a18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc18a1c: ldr             x1, [x1, #0xa20]
    // 0xc18a20: StoreField: r0->field_23 = r1
    //     0xc18a20: stur            w1, [x0, #0x23]
    // 0xc18a24: r1 = Instance_Clip
    //     0xc18a24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc18a28: ldr             x1, [x1, #0x38]
    // 0xc18a2c: StoreField: r0->field_2b = r1
    //     0xc18a2c: stur            w1, [x0, #0x2b]
    // 0xc18a30: StoreField: r0->field_2f = rZR
    //     0xc18a30: stur            xzr, [x0, #0x2f]
    // 0xc18a34: ldur            x1, [fp, #-0x28]
    // 0xc18a38: StoreField: r0->field_b = r1
    //     0xc18a38: stur            w1, [x0, #0xb]
    // 0xc18a3c: b               #0xc18a48
    // 0xc18a40: r0 = Instance_Offstage
    //     0xc18a40: add             x0, PP, #0x6b, lsl #12  ; [pp+0x6b440] Obj!Offstage@d67ce1
    //     0xc18a44: ldr             x0, [x0, #0x440]
    // 0xc18a48: LeaveFrame
    //     0xc18a48: mov             SP, fp
    //     0xc18a4c: ldp             fp, lr, [SP], #0x10
    // 0xc18a50: ret
    //     0xc18a50: ret             
    // 0xc18a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18a54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18a58: b               #0xc1888c
    // 0xc18a5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18a5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18a60: r9 = lineStyle
    //     0xc18a60: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18a64: ldr             x9, [x9, #0x418]
    // 0xc18a68: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18a68: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18a6c: r9 = lineStyle
    //     0xc18a6c: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18a70: ldr             x9, [x9, #0x418]
    // 0xc18a74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18a74: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18a78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18a78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18a7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18a7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildBaseLine(/* No info */) {
    // ** addr: 0xc18a80, size: 0xe0
    // 0xc18a80: EnterFrame
    //     0xc18a80: stp             fp, lr, [SP, #-0x10]!
    //     0xc18a84: mov             fp, SP
    // 0xc18a88: AllocStack(0x28)
    //     0xc18a88: sub             SP, SP, #0x28
    // 0xc18a8c: SetupParameters(_EasyStepperState this /* r1 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc18a8c: mov             x0, x1
    //     0xc18a90: stur            x1, [fp, #-8]
    //     0xc18a94: stur            x3, [fp, #-0x10]
    // 0xc18a98: CheckStackOverflow
    //     0xc18a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18a9c: cmp             SP, x16
    //     0xc18aa0: b.ls            #0xc18b48
    // 0xc18aa4: LoadField: r1 = r0->field_1b
    //     0xc18aa4: ldur            w1, [x0, #0x1b]
    // 0xc18aa8: DecompressPointer r1
    //     0xc18aa8: add             x1, x1, HEAP, lsl #32
    // 0xc18aac: r16 = Sentinel
    //     0xc18aac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18ab0: cmp             w1, w16
    // 0xc18ab4: b.eq            #0xc18b50
    // 0xc18ab8: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xc18ab8: ldur            d0, [x1, #0x17]
    // 0xc18abc: mov             x1, x0
    // 0xc18ac0: stur            d0, [fp, #-0x20]
    // 0xc18ac4: r0 = _getLineColor()
    //     0xc18ac4: bl              #0xc18b6c  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_getLineColor
    // 0xc18ac8: mov             x1, x0
    // 0xc18acc: ldur            x0, [fp, #-8]
    // 0xc18ad0: stur            x1, [fp, #-0x18]
    // 0xc18ad4: LoadField: r2 = r0->field_1b
    //     0xc18ad4: ldur            w2, [x0, #0x1b]
    // 0xc18ad8: DecompressPointer r2
    //     0xc18ad8: add             x2, x2, HEAP, lsl #32
    // 0xc18adc: LoadField: d0 = r2->field_2f
    //     0xc18adc: ldur            d0, [x2, #0x2f]
    // 0xc18ae0: stur            d0, [fp, #-0x28]
    // 0xc18ae4: LoadField: r3 = r0->field_b
    //     0xc18ae4: ldur            w3, [x0, #0xb]
    // 0xc18ae8: DecompressPointer r3
    //     0xc18ae8: add             x3, x3, HEAP, lsl #32
    // 0xc18aec: cmp             w3, NULL
    // 0xc18af0: b.eq            #0xc18b5c
    // 0xc18af4: LoadField: r0 = r2->field_37
    //     0xc18af4: ldur            w0, [x2, #0x37]
    // 0xc18af8: DecompressPointer r0
    //     0xc18af8: add             x0, x0, HEAP, lsl #32
    // 0xc18afc: stur            x0, [fp, #-8]
    // 0xc18b00: r0 = EasyLine()
    //     0xc18b00: bl              #0xc18b60  ; AllocateEasyLineStub -> EasyLine (size=0x38)
    // 0xc18b04: ldur            d0, [fp, #-0x20]
    // 0xc18b08: StoreField: r0->field_b = d0
    //     0xc18b08: stur            d0, [x0, #0xb]
    // 0xc18b0c: ldur            x1, [fp, #-0x18]
    // 0xc18b10: StoreField: r0->field_1b = r1
    //     0xc18b10: stur            w1, [x0, #0x1b]
    // 0xc18b14: d0 = 1.000000
    //     0xc18b14: fmov            d0, #1.00000000
    // 0xc18b18: StoreField: r0->field_1f = d0
    //     0xc18b18: stur            d0, [x0, #0x1f]
    // 0xc18b1c: ldur            d0, [fp, #-0x28]
    // 0xc18b20: StoreField: r0->field_27 = d0
    //     0xc18b20: stur            d0, [x0, #0x27]
    // 0xc18b24: d0 = 4.000000
    //     0xc18b24: fmov            d0, #4.00000000
    // 0xc18b28: StoreField: r0->field_13 = d0
    //     0xc18b28: stur            d0, [x0, #0x13]
    // 0xc18b2c: ldur            x1, [fp, #-8]
    // 0xc18b30: StoreField: r0->field_2f = r1
    //     0xc18b30: stur            w1, [x0, #0x2f]
    // 0xc18b34: ldur            x1, [fp, #-0x10]
    // 0xc18b38: StoreField: r0->field_33 = r1
    //     0xc18b38: stur            w1, [x0, #0x33]
    // 0xc18b3c: LeaveFrame
    //     0xc18b3c: mov             SP, fp
    //     0xc18b40: ldp             fp, lr, [SP], #0x10
    // 0xc18b44: ret
    //     0xc18b44: ret             
    // 0xc18b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18b48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18b4c: b               #0xc18aa4
    // 0xc18b50: r9 = lineStyle
    //     0xc18b50: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18b54: ldr             x9, [x9, #0x418]
    // 0xc18b58: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18b58: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18b5c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc18b5c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _getLineColor(/* No info */) {
    // ** addr: 0xc18b6c, size: 0x134
    // 0xc18b6c: EnterFrame
    //     0xc18b6c: stp             fp, lr, [SP, #-0x10]!
    //     0xc18b70: mov             fp, SP
    // 0xc18b74: CheckStackOverflow
    //     0xc18b74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18b78: cmp             SP, x16
    //     0xc18b7c: b.ls            #0xc18c60
    // 0xc18b80: LoadField: r0 = r1->field_b
    //     0xc18b80: ldur            w0, [x1, #0xb]
    // 0xc18b84: DecompressPointer r0
    //     0xc18b84: add             x0, x0, HEAP, lsl #32
    // 0xc18b88: cmp             w0, NULL
    // 0xc18b8c: b.eq            #0xc18c68
    // 0xc18b90: cbnz            x2, #0xc18bb8
    // 0xc18b94: LoadField: r0 = r1->field_1b
    //     0xc18b94: ldur            w0, [x1, #0x1b]
    // 0xc18b98: DecompressPointer r0
    //     0xc18b98: add             x0, x0, HEAP, lsl #32
    // 0xc18b9c: r16 = Sentinel
    //     0xc18b9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18ba0: cmp             w0, w16
    // 0xc18ba4: b.eq            #0xc18c6c
    // 0xc18ba8: LoadField: r2 = r0->field_f
    //     0xc18ba8: ldur            w2, [x0, #0xf]
    // 0xc18bac: DecompressPointer r2
    //     0xc18bac: add             x2, x2, HEAP, lsl #32
    // 0xc18bb0: mov             x0, x2
    // 0xc18bb4: b               #0xc18c00
    // 0xc18bb8: cmp             x2, #0
    // 0xc18bbc: b.le            #0xc18be4
    // 0xc18bc0: LoadField: r0 = r1->field_1b
    //     0xc18bc0: ldur            w0, [x1, #0x1b]
    // 0xc18bc4: DecompressPointer r0
    //     0xc18bc4: add             x0, x0, HEAP, lsl #32
    // 0xc18bc8: r16 = Sentinel
    //     0xc18bc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18bcc: cmp             w0, w16
    // 0xc18bd0: b.eq            #0xc18c78
    // 0xc18bd4: LoadField: r2 = r0->field_b
    //     0xc18bd4: ldur            w2, [x0, #0xb]
    // 0xc18bd8: DecompressPointer r2
    //     0xc18bd8: add             x2, x2, HEAP, lsl #32
    // 0xc18bdc: mov             x0, x2
    // 0xc18be0: b               #0xc18c00
    // 0xc18be4: tbz             x2, #0x3f, #0xc18bfc
    // 0xc18be8: LoadField: r0 = r1->field_1b
    //     0xc18be8: ldur            w0, [x1, #0x1b]
    // 0xc18bec: DecompressPointer r0
    //     0xc18bec: add             x0, x0, HEAP, lsl #32
    // 0xc18bf0: r16 = Sentinel
    //     0xc18bf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18bf4: cmp             w0, w16
    // 0xc18bf8: b.eq            #0xc18c84
    // 0xc18bfc: r0 = Null
    //     0xc18bfc: mov             x0, NULL
    // 0xc18c00: cmp             w0, NULL
    // 0xc18c04: b.ne            #0xc18c20
    // 0xc18c08: LoadField: r0 = r1->field_1b
    //     0xc18c08: ldur            w0, [x1, #0x1b]
    // 0xc18c0c: DecompressPointer r0
    //     0xc18c0c: add             x0, x0, HEAP, lsl #32
    // 0xc18c10: r16 = Sentinel
    //     0xc18c10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18c14: cmp             w0, w16
    // 0xc18c18: b.eq            #0xc18c90
    // 0xc18c1c: r0 = Null
    //     0xc18c1c: mov             x0, NULL
    // 0xc18c20: cmp             w0, NULL
    // 0xc18c24: b.ne            #0xc18c54
    // 0xc18c28: LoadField: r0 = r1->field_f
    //     0xc18c28: ldur            w0, [x1, #0xf]
    // 0xc18c2c: DecompressPointer r0
    //     0xc18c2c: add             x0, x0, HEAP, lsl #32
    // 0xc18c30: cmp             w0, NULL
    // 0xc18c34: b.eq            #0xc18c9c
    // 0xc18c38: mov             x1, x0
    // 0xc18c3c: r0 = of()
    //     0xc18c3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc18c40: LoadField: r1 = r0->field_3f
    //     0xc18c40: ldur            w1, [x0, #0x3f]
    // 0xc18c44: DecompressPointer r1
    //     0xc18c44: add             x1, x1, HEAP, lsl #32
    // 0xc18c48: LoadField: r2 = r1->field_b
    //     0xc18c48: ldur            w2, [x1, #0xb]
    // 0xc18c4c: DecompressPointer r2
    //     0xc18c4c: add             x2, x2, HEAP, lsl #32
    // 0xc18c50: mov             x0, x2
    // 0xc18c54: LeaveFrame
    //     0xc18c54: mov             SP, fp
    //     0xc18c58: ldp             fp, lr, [SP], #0x10
    // 0xc18c5c: ret
    //     0xc18c5c: ret             
    // 0xc18c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18c60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18c64: b               #0xc18b80
    // 0xc18c68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18c68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18c6c: r9 = lineStyle
    //     0xc18c6c: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18c70: ldr             x9, [x9, #0x418]
    // 0xc18c74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18c74: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18c78: r9 = lineStyle
    //     0xc18c78: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18c7c: ldr             x9, [x9, #0x418]
    // 0xc18c80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18c80: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18c84: r9 = lineStyle
    //     0xc18c84: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18c88: ldr             x9, [x9, #0x418]
    // 0xc18c8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18c8c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18c90: r9 = lineStyle
    //     0xc18c90: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18c94: ldr             x9, [x9, #0x418]
    // 0xc18c98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18c98: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18c9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18c9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStep(/* No info */) {
    // ** addr: 0xc18ca0, size: 0x1f4
    // 0xc18ca0: EnterFrame
    //     0xc18ca0: stp             fp, lr, [SP, #-0x10]!
    //     0xc18ca4: mov             fp, SP
    // 0xc18ca8: AllocStack(0x40)
    //     0xc18ca8: sub             SP, SP, #0x40
    // 0xc18cac: SetupParameters(_EasyStepperState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc18cac: mov             x0, x1
    //     0xc18cb0: stur            x1, [fp, #-8]
    //     0xc18cb4: mov             x1, x2
    //     0xc18cb8: stur            x2, [fp, #-0x10]
    // 0xc18cbc: CheckStackOverflow
    //     0xc18cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18cc0: cmp             SP, x16
    //     0xc18cc4: b.ls            #0xc18e70
    // 0xc18cc8: r1 = 2
    //     0xc18cc8: movz            x1, #0x2
    // 0xc18ccc: r0 = AllocateContext()
    //     0xc18ccc: bl              #0x16f6108  ; AllocateContextStub
    // 0xc18cd0: mov             x4, x0
    // 0xc18cd4: ldur            x3, [fp, #-8]
    // 0xc18cd8: stur            x4, [fp, #-0x30]
    // 0xc18cdc: StoreField: r4->field_f = r3
    //     0xc18cdc: stur            w3, [x4, #0xf]
    // 0xc18ce0: ldur            x5, [fp, #-0x10]
    // 0xc18ce4: r0 = BoxInt64Instr(r5)
    //     0xc18ce4: sbfiz           x0, x5, #1, #0x1f
    //     0xc18ce8: cmp             x5, x0, asr #1
    //     0xc18cec: b.eq            #0xc18cf8
    //     0xc18cf0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc18cf4: stur            x5, [x0, #7]
    // 0xc18cf8: StoreField: r4->field_13 = r0
    //     0xc18cf8: stur            w0, [x4, #0x13]
    // 0xc18cfc: LoadField: r0 = r3->field_b
    //     0xc18cfc: ldur            w0, [x3, #0xb]
    // 0xc18d00: DecompressPointer r0
    //     0xc18d00: add             x0, x0, HEAP, lsl #32
    // 0xc18d04: cmp             w0, NULL
    // 0xc18d08: b.eq            #0xc18e78
    // 0xc18d0c: LoadField: r2 = r0->field_b
    //     0xc18d0c: ldur            w2, [x0, #0xb]
    // 0xc18d10: DecompressPointer r2
    //     0xc18d10: add             x2, x2, HEAP, lsl #32
    // 0xc18d14: LoadField: r0 = r2->field_b
    //     0xc18d14: ldur            w0, [x2, #0xb]
    // 0xc18d18: r1 = LoadInt32Instr(r0)
    //     0xc18d18: sbfx            x1, x0, #1, #0x1f
    // 0xc18d1c: mov             x0, x1
    // 0xc18d20: mov             x1, x5
    // 0xc18d24: cmp             x1, x0
    // 0xc18d28: b.hs            #0xc18e7c
    // 0xc18d2c: LoadField: r0 = r2->field_f
    //     0xc18d2c: ldur            w0, [x2, #0xf]
    // 0xc18d30: DecompressPointer r0
    //     0xc18d30: add             x0, x0, HEAP, lsl #32
    // 0xc18d34: ArrayLoad: r6 = r0[r5]  ; Unknown_4
    //     0xc18d34: add             x16, x0, x5, lsl #2
    //     0xc18d38: ldur            w6, [x16, #0xf]
    // 0xc18d3c: DecompressPointer r6
    //     0xc18d3c: add             x6, x6, HEAP, lsl #32
    // 0xc18d40: stur            x6, [fp, #-0x28]
    // 0xc18d44: cbz             x5, #0xc18d50
    // 0xc18d48: r0 = false
    //     0xc18d48: add             x0, NULL, #0x30  ; false
    // 0xc18d4c: b               #0xc18d54
    // 0xc18d50: r0 = true
    //     0xc18d50: add             x0, NULL, #0x20  ; true
    // 0xc18d54: stur            x0, [fp, #-0x20]
    // 0xc18d58: tbnz            x5, #0x3f, #0xc18d64
    // 0xc18d5c: r7 = false
    //     0xc18d5c: add             x7, NULL, #0x30  ; false
    // 0xc18d60: b               #0xc18d68
    // 0xc18d64: r7 = true
    //     0xc18d64: add             x7, NULL, #0x20  ; true
    // 0xc18d68: stur            x7, [fp, #-0x18]
    // 0xc18d6c: d1 = 8.000000
    //     0xc18d6c: fmov            d1, #8.00000000
    // 0xc18d70: d0 = 0.000000
    //     0xc18d70: eor             v0.16b, v0.16b, v0.16b
    // 0xc18d74: fmax            v2.2d, v1.2d, v0.2d
    // 0xc18d78: mov             x1, x3
    // 0xc18d7c: mov             x2, x5
    // 0xc18d80: stur            d2, [fp, #-0x38]
    // 0xc18d84: r0 = _handleBorderType()
    //     0xc18d84: bl              #0xc18ea0  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_handleBorderType
    // 0xc18d88: ldur            x0, [fp, #-8]
    // 0xc18d8c: LoadField: r1 = r0->field_b
    //     0xc18d8c: ldur            w1, [x0, #0xb]
    // 0xc18d90: DecompressPointer r1
    //     0xc18d90: add             x1, x1, HEAP, lsl #32
    // 0xc18d94: cmp             w1, NULL
    // 0xc18d98: b.eq            #0xc18e80
    // 0xc18d9c: LoadField: r2 = r0->field_1b
    //     0xc18d9c: ldur            w2, [x0, #0x1b]
    // 0xc18da0: DecompressPointer r2
    //     0xc18da0: add             x2, x2, HEAP, lsl #32
    // 0xc18da4: r16 = Sentinel
    //     0xc18da4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc18da8: cmp             w2, w16
    // 0xc18dac: b.eq            #0xc18e84
    // 0xc18db0: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xc18db0: ldur            d0, [x2, #0x17]
    // 0xc18db4: stur            d0, [fp, #-0x40]
    // 0xc18db8: LoadField: r0 = r1->field_b
    //     0xc18db8: ldur            w0, [x1, #0xb]
    // 0xc18dbc: DecompressPointer r0
    //     0xc18dbc: add             x0, x0, HEAP, lsl #32
    // 0xc18dc0: LoadField: r1 = r0->field_b
    //     0xc18dc0: ldur            w1, [x0, #0xb]
    // 0xc18dc4: r0 = LoadInt32Instr(r1)
    //     0xc18dc4: sbfx            x0, x1, #1, #0x1f
    // 0xc18dc8: ldur            x1, [fp, #-0x10]
    // 0xc18dcc: cmp             x1, x0
    // 0xc18dd0: b.hs            #0xc18e90
    // 0xc18dd4: r0 = BaseStep()
    //     0xc18dd4: bl              #0xc18e94  ; AllocateBaseStepStub -> BaseStep (size=0x68)
    // 0xc18dd8: mov             x3, x0
    // 0xc18ddc: ldur            x0, [fp, #-0x28]
    // 0xc18de0: stur            x3, [fp, #-8]
    // 0xc18de4: StoreField: r3->field_b = r0
    //     0xc18de4: stur            w0, [x3, #0xb]
    // 0xc18de8: ldur            x0, [fp, #-0x20]
    // 0xc18dec: StoreField: r3->field_f = r0
    //     0xc18dec: stur            w0, [x3, #0xf]
    // 0xc18df0: ldur            x0, [fp, #-0x18]
    // 0xc18df4: StoreField: r3->field_13 = r0
    //     0xc18df4: stur            w0, [x3, #0x13]
    // 0xc18df8: r0 = false
    //     0xc18df8: add             x0, NULL, #0x30  ; false
    // 0xc18dfc: ArrayStore: r3[0] = r0  ; List_4
    //     0xc18dfc: stur            w0, [x3, #0x17]
    // 0xc18e00: ldur            x2, [fp, #-0x30]
    // 0xc18e04: r1 = Function '<anonymous closure>':.
    //     0xc18e04: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b448] AnonymousClosure: (0xc18efc), in [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildStep (0xc18ca0)
    //     0xc18e08: ldr             x1, [x1, #0x448]
    // 0xc18e0c: r0 = AllocateClosure()
    //     0xc18e0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc18e10: mov             x1, x0
    // 0xc18e14: ldur            x0, [fp, #-8]
    // 0xc18e18: StoreField: r0->field_1b = r1
    //     0xc18e18: stur            w1, [x0, #0x1b]
    // 0xc18e1c: r1 = true
    //     0xc18e1c: add             x1, NULL, #0x20  ; true
    // 0xc18e20: StoreField: r0->field_27 = r1
    //     0xc18e20: stur            w1, [x0, #0x27]
    // 0xc18e24: d0 = 10.000000
    //     0xc18e24: fmov            d0, #10.00000000
    // 0xc18e28: StoreField: r0->field_1f = d0
    //     0xc18e28: stur            d0, [x0, #0x1f]
    // 0xc18e2c: ldur            d0, [fp, #-0x38]
    // 0xc18e30: StoreField: r0->field_3b = d0
    //     0xc18e30: stur            d0, [x0, #0x3b]
    // 0xc18e34: r2 = Instance_StepShape
    //     0xc18e34: add             x2, PP, #0x54, lsl #12  ; [pp+0x54818] Obj!StepShape@d75061
    //     0xc18e38: ldr             x2, [x2, #0x818]
    // 0xc18e3c: StoreField: r0->field_43 = r2
    //     0xc18e3c: stur            w2, [x0, #0x43]
    // 0xc18e40: r2 = false
    //     0xc18e40: add             x2, NULL, #0x30  ; false
    // 0xc18e44: StoreField: r0->field_4b = r2
    //     0xc18e44: stur            w2, [x0, #0x4b]
    // 0xc18e48: StoreField: r0->field_4f = r2
    //     0xc18e48: stur            w2, [x0, #0x4f]
    // 0xc18e4c: ldur            d0, [fp, #-0x40]
    // 0xc18e50: StoreField: r0->field_53 = d0
    //     0xc18e50: stur            d0, [x0, #0x53]
    // 0xc18e54: StoreField: r0->field_5b = r1
    //     0xc18e54: stur            w1, [x0, #0x5b]
    // 0xc18e58: r2 = Instance_Axis
    //     0xc18e58: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc18e5c: StoreField: r0->field_63 = r2
    //     0xc18e5c: stur            w2, [x0, #0x63]
    // 0xc18e60: StoreField: r0->field_5f = r1
    //     0xc18e60: stur            w1, [x0, #0x5f]
    // 0xc18e64: LeaveFrame
    //     0xc18e64: mov             SP, fp
    //     0xc18e68: ldp             fp, lr, [SP], #0x10
    // 0xc18e6c: ret
    //     0xc18e6c: ret             
    // 0xc18e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18e70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18e74: b               #0xc18cc8
    // 0xc18e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18e78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18e7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18e7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18e80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18e84: r9 = lineStyle
    //     0xc18e84: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc18e88: ldr             x9, [x9, #0x418]
    // 0xc18e8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc18e8c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc18e90: r0 = RangeErrorSharedWithFPURegs()
    //     0xc18e90: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
  }
  _ _handleBorderType(/* No info */) {
    // ** addr: 0xc18ea0, size: 0x5c
    // 0xc18ea0: LoadField: r3 = r1->field_b
    //     0xc18ea0: ldur            w3, [x1, #0xb]
    // 0xc18ea4: DecompressPointer r3
    //     0xc18ea4: add             x3, x3, HEAP, lsl #32
    // 0xc18ea8: cmp             w3, NULL
    // 0xc18eac: b.eq            #0xc18ef0
    // 0xc18eb0: cbnz            x2, #0xc18ec0
    // 0xc18eb4: r0 = Instance_BorderType
    //     0xc18eb4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54820] Obj!BorderType@d75041
    //     0xc18eb8: ldr             x0, [x0, #0x820]
    // 0xc18ebc: ret
    //     0xc18ebc: ret             
    // 0xc18ec0: cmp             x2, #0
    // 0xc18ec4: b.le            #0xc18ed4
    // 0xc18ec8: r0 = Instance_BorderType
    //     0xc18ec8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54820] Obj!BorderType@d75041
    //     0xc18ecc: ldr             x0, [x0, #0x820]
    // 0xc18ed0: ret
    //     0xc18ed0: ret             
    // 0xc18ed4: tbz             x2, #0x3f, #0xc18ee4
    // 0xc18ed8: r0 = Instance_BorderType
    //     0xc18ed8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54820] Obj!BorderType@d75041
    //     0xc18edc: ldr             x0, [x0, #0x820]
    // 0xc18ee0: ret
    //     0xc18ee0: ret             
    // 0xc18ee4: r0 = Instance_BorderType
    //     0xc18ee4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54820] Obj!BorderType@d75041
    //     0xc18ee8: ldr             x0, [x0, #0x820]
    // 0xc18eec: ret
    //     0xc18eec: ret             
    // 0xc18ef0: EnterFrame
    //     0xc18ef0: stp             fp, lr, [SP, #-0x10]!
    //     0xc18ef4: mov             fp, SP
    // 0xc18ef8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18ef8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc18efc, size: 0xa4
    // 0xc18efc: EnterFrame
    //     0xc18efc: stp             fp, lr, [SP, #-0x10]!
    //     0xc18f00: mov             fp, SP
    // 0xc18f04: AllocStack(0x8)
    //     0xc18f04: sub             SP, SP, #8
    // 0xc18f08: SetupParameters()
    //     0xc18f08: ldr             x0, [fp, #0x10]
    //     0xc18f0c: ldur            w2, [x0, #0x17]
    //     0xc18f10: add             x2, x2, HEAP, lsl #32
    // 0xc18f14: CheckStackOverflow
    //     0xc18f14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18f18: cmp             SP, x16
    //     0xc18f1c: b.ls            #0xc18f90
    // 0xc18f20: LoadField: r3 = r2->field_f
    //     0xc18f20: ldur            w3, [x2, #0xf]
    // 0xc18f24: DecompressPointer r3
    //     0xc18f24: add             x3, x3, HEAP, lsl #32
    // 0xc18f28: stur            x3, [fp, #-8]
    // 0xc18f2c: LoadField: r0 = r3->field_b
    //     0xc18f2c: ldur            w0, [x3, #0xb]
    // 0xc18f30: DecompressPointer r0
    //     0xc18f30: add             x0, x0, HEAP, lsl #32
    // 0xc18f34: cmp             w0, NULL
    // 0xc18f38: b.eq            #0xc18f98
    // 0xc18f3c: LoadField: r1 = r0->field_b
    //     0xc18f3c: ldur            w1, [x0, #0xb]
    // 0xc18f40: DecompressPointer r1
    //     0xc18f40: add             x1, x1, HEAP, lsl #32
    // 0xc18f44: LoadField: r0 = r2->field_13
    //     0xc18f44: ldur            w0, [x2, #0x13]
    // 0xc18f48: DecompressPointer r0
    //     0xc18f48: add             x0, x0, HEAP, lsl #32
    // 0xc18f4c: LoadField: r4 = r1->field_b
    //     0xc18f4c: ldur            w4, [x1, #0xb]
    // 0xc18f50: r1 = LoadInt32Instr(r0)
    //     0xc18f50: sbfx            x1, x0, #1, #0x1f
    //     0xc18f54: tbz             w0, #0, #0xc18f5c
    //     0xc18f58: ldur            x1, [x0, #7]
    // 0xc18f5c: r0 = LoadInt32Instr(r4)
    //     0xc18f5c: sbfx            x0, x4, #1, #0x1f
    // 0xc18f60: cmp             x1, x0
    // 0xc18f64: b.hs            #0xc18f9c
    // 0xc18f68: r1 = Function '<anonymous closure>':.
    //     0xc18f68: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b450] AnonymousClosure: (0xc18fa0), in [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_buildStep (0xc18ca0)
    //     0xc18f6c: ldr             x1, [x1, #0x450]
    // 0xc18f70: r0 = AllocateClosure()
    //     0xc18f70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc18f74: ldur            x1, [fp, #-8]
    // 0xc18f78: mov             x2, x0
    // 0xc18f7c: r0 = setState()
    //     0xc18f7c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc18f80: r0 = Null
    //     0xc18f80: mov             x0, NULL
    // 0xc18f84: LeaveFrame
    //     0xc18f84: mov             SP, fp
    //     0xc18f88: ldp             fp, lr, [SP], #0x10
    // 0xc18f8c: ret
    //     0xc18f8c: ret             
    // 0xc18f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18f90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18f94: b               #0xc18f20
    // 0xc18f98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18f98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18f9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18f9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc18fa0, size: 0xac
    // 0xc18fa0: EnterFrame
    //     0xc18fa0: stp             fp, lr, [SP, #-0x10]!
    //     0xc18fa4: mov             fp, SP
    // 0xc18fa8: AllocStack(0x10)
    //     0xc18fa8: sub             SP, SP, #0x10
    // 0xc18fac: SetupParameters()
    //     0xc18fac: ldr             x0, [fp, #0x10]
    //     0xc18fb0: ldur            w1, [x0, #0x17]
    //     0xc18fb4: add             x1, x1, HEAP, lsl #32
    // 0xc18fb8: CheckStackOverflow
    //     0xc18fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18fbc: cmp             SP, x16
    //     0xc18fc0: b.ls            #0xc1903c
    // 0xc18fc4: LoadField: r2 = r1->field_f
    //     0xc18fc4: ldur            w2, [x1, #0xf]
    // 0xc18fc8: DecompressPointer r2
    //     0xc18fc8: add             x2, x2, HEAP, lsl #32
    // 0xc18fcc: LoadField: r3 = r1->field_13
    //     0xc18fcc: ldur            w3, [x1, #0x13]
    // 0xc18fd0: DecompressPointer r3
    //     0xc18fd0: add             x3, x3, HEAP, lsl #32
    // 0xc18fd4: mov             x0, x3
    // 0xc18fd8: ArrayStore: r2[0] = r0  ; List_4
    //     0xc18fd8: stur            w0, [x2, #0x17]
    //     0xc18fdc: tbz             w0, #0, #0xc18ff8
    //     0xc18fe0: ldurb           w16, [x2, #-1]
    //     0xc18fe4: ldurb           w17, [x0, #-1]
    //     0xc18fe8: and             x16, x17, x16, lsr #2
    //     0xc18fec: tst             x16, HEAP, lsr #32
    //     0xc18ff0: b.eq            #0xc18ff8
    //     0xc18ff4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc18ff8: LoadField: r0 = r2->field_b
    //     0xc18ff8: ldur            w0, [x2, #0xb]
    // 0xc18ffc: DecompressPointer r0
    //     0xc18ffc: add             x0, x0, HEAP, lsl #32
    // 0xc19000: cmp             w0, NULL
    // 0xc19004: b.eq            #0xc19044
    // 0xc19008: LoadField: r1 = r0->field_13
    //     0xc19008: ldur            w1, [x0, #0x13]
    // 0xc1900c: DecompressPointer r1
    //     0xc1900c: add             x1, x1, HEAP, lsl #32
    // 0xc19010: cmp             w1, NULL
    // 0xc19014: b.eq            #0xc19048
    // 0xc19018: stp             x3, x1, [SP]
    // 0xc1901c: mov             x0, x1
    // 0xc19020: ClosureCall
    //     0xc19020: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xc19024: ldur            x2, [x0, #0x1f]
    //     0xc19028: blr             x2
    // 0xc1902c: r0 = Null
    //     0xc1902c: mov             x0, NULL
    // 0xc19030: LeaveFrame
    //     0xc19030: mov             SP, fp
    //     0xc19034: ldp             fp, lr, [SP], #0x10
    // 0xc19038: ret
    //     0xc19038: ret             
    // 0xc1903c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1903c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19040: b               #0xc18fc4
    // 0xc19044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc19044: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc19048: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc19048: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, OverscrollIndicatorNotification) {
    // ** addr: 0xc1904c, size: 0x34
    // 0xc1904c: EnterFrame
    //     0xc1904c: stp             fp, lr, [SP, #-0x10]!
    //     0xc19050: mov             fp, SP
    // 0xc19054: CheckStackOverflow
    //     0xc19054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc19058: cmp             SP, x16
    //     0xc1905c: b.ls            #0xc19078
    // 0xc19060: ldr             x1, [fp, #0x10]
    // 0xc19064: r0 = _doRestore()
    //     0xc19064: bl              #0xc19080  ; [package:flutter/src/material/text_field.dart] __TextFieldState&State&RestorationMixin::_doRestore
    // 0xc19068: r0 = false
    //     0xc19068: add             x0, NULL, #0x30  ; false
    // 0xc1906c: LeaveFrame
    //     0xc1906c: mov             SP, fp
    //     0xc19070: ldp             fp, lr, [SP], #0x10
    // 0xc19074: ret
    //     0xc19074: ret             
    // 0xc19078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19078: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1907c: b               #0xc19060
  }
  [closure] void _afterLayout(dynamic, dynamic) {
    // ** addr: 0xc19090, size: 0x3c
    // 0xc19090: EnterFrame
    //     0xc19090: stp             fp, lr, [SP, #-0x10]!
    //     0xc19094: mov             fp, SP
    // 0xc19098: ldr             x0, [fp, #0x18]
    // 0xc1909c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc1909c: ldur            w1, [x0, #0x17]
    // 0xc190a0: DecompressPointer r1
    //     0xc190a0: add             x1, x1, HEAP, lsl #32
    // 0xc190a4: CheckStackOverflow
    //     0xc190a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc190a8: cmp             SP, x16
    //     0xc190ac: b.ls            #0xc190c4
    // 0xc190b0: ldr             x2, [fp, #0x10]
    // 0xc190b4: r0 = _afterLayout()
    //     0xc190b4: bl              #0xc190cc  ; [package:easy_stepper/easy_stepper.dart] _EasyStepperState::_afterLayout
    // 0xc190b8: LeaveFrame
    //     0xc190b8: mov             SP, fp
    //     0xc190bc: ldp             fp, lr, [SP], #0x10
    // 0xc190c0: ret
    //     0xc190c0: ret             
    // 0xc190c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc190c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc190c8: b               #0xc190b0
  }
  _ _afterLayout(/* No info */) {
    // ** addr: 0xc190cc, size: 0x120
    // 0xc190cc: EnterFrame
    //     0xc190cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc190d0: mov             fp, SP
    // 0xc190d4: AllocStack(0x10)
    //     0xc190d4: sub             SP, SP, #0x10
    // 0xc190d8: SetupParameters(_EasyStepperState this /* r1 => r0, fp-0x10 */)
    //     0xc190d8: mov             x0, x1
    //     0xc190dc: stur            x1, [fp, #-0x10]
    // 0xc190e0: CheckStackOverflow
    //     0xc190e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc190e4: cmp             SP, x16
    //     0xc190e8: b.ls            #0xc191bc
    // 0xc190ec: r4 = 0
    //     0xc190ec: movz            x4, #0
    // 0xc190f0: d1 = 28.000000
    //     0xc190f0: fmov            d1, #28.00000000
    // 0xc190f4: stur            x4, [fp, #-8]
    // 0xc190f8: CheckStackOverflow
    //     0xc190f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc190fc: cmp             SP, x16
    //     0xc19100: b.ls            #0xc191c4
    // 0xc19104: LoadField: r1 = r0->field_b
    //     0xc19104: ldur            w1, [x0, #0xb]
    // 0xc19108: DecompressPointer r1
    //     0xc19108: add             x1, x1, HEAP, lsl #32
    // 0xc1910c: cmp             w1, NULL
    // 0xc19110: b.eq            #0xc191cc
    // 0xc19114: LoadField: r2 = r1->field_b
    //     0xc19114: ldur            w2, [x1, #0xb]
    // 0xc19118: DecompressPointer r2
    //     0xc19118: add             x2, x2, HEAP, lsl #32
    // 0xc1911c: LoadField: r1 = r2->field_b
    //     0xc1911c: ldur            w1, [x2, #0xb]
    // 0xc19120: r2 = LoadInt32Instr(r1)
    //     0xc19120: sbfx            x2, x1, #1, #0x1f
    // 0xc19124: cmp             x4, x2
    // 0xc19128: b.ge            #0xc191ac
    // 0xc1912c: LoadField: r1 = r0->field_13
    //     0xc1912c: ldur            w1, [x0, #0x13]
    // 0xc19130: DecompressPointer r1
    //     0xc19130: add             x1, x1, HEAP, lsl #32
    // 0xc19134: cmp             w1, NULL
    // 0xc19138: b.eq            #0xc191d0
    // 0xc1913c: LoadField: r2 = r0->field_1b
    //     0xc1913c: ldur            w2, [x0, #0x1b]
    // 0xc19140: DecompressPointer r2
    //     0xc19140: add             x2, x2, HEAP, lsl #32
    // 0xc19144: r16 = Sentinel
    //     0xc19144: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc19148: cmp             w2, w16
    // 0xc1914c: b.eq            #0xc191d4
    // 0xc19150: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xc19150: ldur            d0, [x2, #0x17]
    // 0xc19154: fadd            d2, d0, d1
    // 0xc19158: scvtf           d0, x4
    // 0xc1915c: fmul            d3, d0, d2
    // 0xc19160: mov             v0.16b, v3.16b
    // 0xc19164: r2 = Instance__Linear
    //     0xc19164: ldr             x2, [PP, #0x4cf8]  ; [pp+0x4cf8] Obj!_Linear@d5bb01
    // 0xc19168: r3 = Instance_Duration
    //     0xc19168: ldr             x3, [PP, #0xa68]  ; [pp+0xa68] Obj!Duration@d776d1
    // 0xc1916c: r0 = animateTo()
    //     0xc1916c: bl              #0x66be98  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::animateTo
    // 0xc19170: ldur            x1, [fp, #-0x10]
    // 0xc19174: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc19174: ldur            w2, [x1, #0x17]
    // 0xc19178: DecompressPointer r2
    //     0xc19178: add             x2, x2, HEAP, lsl #32
    // 0xc1917c: r16 = Sentinel
    //     0xc1917c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc19180: cmp             w2, w16
    // 0xc19184: b.eq            #0xc191e0
    // 0xc19188: r3 = LoadInt32Instr(r2)
    //     0xc19188: sbfx            x3, x2, #1, #0x1f
    //     0xc1918c: tbz             w2, #0, #0xc19194
    //     0xc19190: ldur            x3, [x2, #7]
    // 0xc19194: ldur            x2, [fp, #-8]
    // 0xc19198: cmp             x3, x2
    // 0xc1919c: b.eq            #0xc191ac
    // 0xc191a0: add             x4, x2, #1
    // 0xc191a4: mov             x0, x1
    // 0xc191a8: b               #0xc190f0
    // 0xc191ac: r0 = Null
    //     0xc191ac: mov             x0, NULL
    // 0xc191b0: LeaveFrame
    //     0xc191b0: mov             SP, fp
    //     0xc191b4: ldp             fp, lr, [SP], #0x10
    // 0xc191b8: ret
    //     0xc191b8: ret             
    // 0xc191bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc191bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc191c0: b               #0xc190ec
    // 0xc191c4: r0 = StackOverflowSharedWithFPURegs()
    //     0xc191c4: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0xc191c8: b               #0xc19104
    // 0xc191cc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc191cc: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc191d0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc191d0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc191d4: r9 = lineStyle
    //     0xc191d4: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b418] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xc191d8: ldr             x9, [x9, #0x418]
    // 0xc191dc: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xc191dc: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xc191e0: r9 = _selectedIndex
    //     0xc191e0: add             x9, PP, #0x6b, lsl #12  ; [pp+0x6b420] Field <_EasyStepperState@1307264635._selectedIndex@1307264635>: late (offset: 0x18)
    //     0xc191e4: ldr             x9, [x9, #0x420]
    // 0xc191e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc191e8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc886f4, size: 0x48
    // 0xc886f4: EnterFrame
    //     0xc886f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc886f8: mov             fp, SP
    // 0xc886fc: CheckStackOverflow
    //     0xc886fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88700: cmp             SP, x16
    //     0xc88704: b.ls            #0xc88730
    // 0xc88708: LoadField: r0 = r1->field_13
    //     0xc88708: ldur            w0, [x1, #0x13]
    // 0xc8870c: DecompressPointer r0
    //     0xc8870c: add             x0, x0, HEAP, lsl #32
    // 0xc88710: cmp             w0, NULL
    // 0xc88714: b.eq            #0xc88738
    // 0xc88718: mov             x1, x0
    // 0xc8871c: r0 = dispose()
    //     0xc8871c: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc88720: r0 = Null
    //     0xc88720: mov             x0, NULL
    // 0xc88724: LeaveFrame
    //     0xc88724: mov             SP, fp
    //     0xc88728: ldp             fp, lr, [SP], #0x10
    // 0xc8872c: ret
    //     0xc8872c: ret             
    // 0xc88730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88730: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88734: b               #0xc88708
    // 0xc88738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc88738: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3953, size: 0xc8, field offset: 0xc
//   const constructor, 
class EasyStepper extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc815ac, size: 0x34
    // 0xc815ac: EnterFrame
    //     0xc815ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc815b0: mov             fp, SP
    // 0xc815b4: mov             x0, x1
    // 0xc815b8: r1 = <EasyStepper>
    //     0xc815b8: add             x1, PP, #0x62, lsl #12  ; [pp+0x620c8] TypeArguments: <EasyStepper>
    //     0xc815bc: ldr             x1, [x1, #0xc8]
    // 0xc815c0: r0 = _EasyStepperState()
    //     0xc815c0: bl              #0xc815e0  ; Allocate_EasyStepperStateStub -> _EasyStepperState (size=0x24)
    // 0xc815c4: r1 = Sentinel
    //     0xc815c4: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc815c8: ArrayStore: r0[0] = r1  ; List_4
    //     0xc815c8: stur            w1, [x0, #0x17]
    // 0xc815cc: StoreField: r0->field_1b = r1
    //     0xc815cc: stur            w1, [x0, #0x1b]
    // 0xc815d0: StoreField: r0->field_1f = r1
    //     0xc815d0: stur            w1, [x0, #0x1f]
    // 0xc815d4: LeaveFrame
    //     0xc815d4: mov             SP, fp
    //     0xc815d8: ldp             fp, lr, [SP], #0x10
    // 0xc815dc: ret
    //     0xc815dc: ret             
  }
}
