// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart

// class id: 1049484, size: 0x8
class :: {
}

// class id: 3281, size: 0x68, field offset: 0x14
class _ChangeAddressBottomSheetState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9040b4, size: 0x24
    // 0x9040b4: r1 = true
    //     0x9040b4: add             x1, NULL, #0x20  ; true
    // 0x9040b8: ldr             x2, [SP]
    // 0x9040bc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9040bc: ldur            w3, [x2, #0x17]
    // 0x9040c0: DecompressPointer r3
    //     0x9040c0: add             x3, x3, HEAP, lsl #32
    // 0x9040c4: LoadField: r2 = r3->field_f
    //     0x9040c4: ldur            w2, [x3, #0xf]
    // 0x9040c8: DecompressPointer r2
    //     0x9040c8: add             x2, x2, HEAP, lsl #32
    // 0x9040cc: StoreField: r2->field_4b = r1
    //     0x9040cc: stur            w1, [x2, #0x4b]
    // 0x9040d0: r0 = Null
    //     0x9040d0: mov             x0, NULL
    // 0x9040d4: ret
    //     0x9040d4: ret             
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9040d8, size: 0x60
    // 0x9040d8: EnterFrame
    //     0x9040d8: stp             fp, lr, [SP, #-0x10]!
    //     0x9040dc: mov             fp, SP
    // 0x9040e0: AllocStack(0x8)
    //     0x9040e0: sub             SP, SP, #8
    // 0x9040e4: SetupParameters()
    //     0x9040e4: ldr             x0, [fp, #0x18]
    //     0x9040e8: ldur            w2, [x0, #0x17]
    //     0x9040ec: add             x2, x2, HEAP, lsl #32
    // 0x9040f0: CheckStackOverflow
    //     0x9040f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9040f4: cmp             SP, x16
    //     0x9040f8: b.ls            #0x904130
    // 0x9040fc: LoadField: r0 = r2->field_f
    //     0x9040fc: ldur            w0, [x2, #0xf]
    // 0x904100: DecompressPointer r0
    //     0x904100: add             x0, x0, HEAP, lsl #32
    // 0x904104: stur            x0, [fp, #-8]
    // 0x904108: r1 = Function '<anonymous closure>':.
    //     0x904108: add             x1, PP, #0x54, lsl #12  ; [pp+0x549d0] AnonymousClosure: (0x9040b4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::initState (0x945cec)
    //     0x90410c: ldr             x1, [x1, #0x9d0]
    // 0x904110: r0 = AllocateClosure()
    //     0x904110: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x904114: ldur            x1, [fp, #-8]
    // 0x904118: mov             x2, x0
    // 0x90411c: r0 = setState()
    //     0x90411c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x904120: r0 = Null
    //     0x904120: mov             x0, NULL
    // 0x904124: LeaveFrame
    //     0x904124: mov             SP, fp
    //     0x904128: ldp             fp, lr, [SP], #0x10
    // 0x90412c: ret
    //     0x90412c: ret             
    // 0x904130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x904130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x904134: b               #0x9040fc
  }
  _ initState(/* No info */) {
    // ** addr: 0x945cec, size: 0x300
    // 0x945cec: EnterFrame
    //     0x945cec: stp             fp, lr, [SP, #-0x10]!
    //     0x945cf0: mov             fp, SP
    // 0x945cf4: AllocStack(0x28)
    //     0x945cf4: sub             SP, SP, #0x28
    // 0x945cf8: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x945cf8: stur            x1, [fp, #-8]
    // 0x945cfc: CheckStackOverflow
    //     0x945cfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945d00: cmp             SP, x16
    //     0x945d04: b.ls            #0x945fd4
    // 0x945d08: r1 = 1
    //     0x945d08: movz            x1, #0x1
    // 0x945d0c: r0 = AllocateContext()
    //     0x945d0c: bl              #0x16f6108  ; AllocateContextStub
    // 0x945d10: mov             x4, x0
    // 0x945d14: ldur            x3, [fp, #-8]
    // 0x945d18: stur            x4, [fp, #-0x10]
    // 0x945d1c: StoreField: r4->field_f = r3
    //     0x945d1c: stur            w3, [x4, #0xf]
    // 0x945d20: LoadField: r0 = r3->field_b
    //     0x945d20: ldur            w0, [x3, #0xb]
    // 0x945d24: DecompressPointer r0
    //     0x945d24: add             x0, x0, HEAP, lsl #32
    // 0x945d28: cmp             w0, NULL
    // 0x945d2c: b.eq            #0x945fdc
    // 0x945d30: LoadField: r1 = r0->field_1f
    //     0x945d30: ldur            w1, [x0, #0x1f]
    // 0x945d34: DecompressPointer r1
    //     0x945d34: add             x1, x1, HEAP, lsl #32
    // 0x945d38: LoadField: r2 = r1->field_1b
    //     0x945d38: ldur            w2, [x1, #0x1b]
    // 0x945d3c: DecompressPointer r2
    //     0x945d3c: add             x2, x2, HEAP, lsl #32
    // 0x945d40: cmp             w2, NULL
    // 0x945d44: b.ne            #0x945d50
    // 0x945d48: r0 = Null
    //     0x945d48: mov             x0, NULL
    // 0x945d4c: b               #0x945d68
    // 0x945d50: LoadField: r0 = r2->field_b
    //     0x945d50: ldur            w0, [x2, #0xb]
    // 0x945d54: cbnz            w0, #0x945d60
    // 0x945d58: r1 = false
    //     0x945d58: add             x1, NULL, #0x30  ; false
    // 0x945d5c: b               #0x945d64
    // 0x945d60: r1 = true
    //     0x945d60: add             x1, NULL, #0x20  ; true
    // 0x945d64: mov             x0, x1
    // 0x945d68: cmp             w0, NULL
    // 0x945d6c: b.eq            #0x945ec0
    // 0x945d70: tbnz            w0, #4, #0x945ec0
    // 0x945d74: cmp             w2, NULL
    // 0x945d78: b.ne            #0x945d84
    // 0x945d7c: r2 = Null
    //     0x945d7c: mov             x2, NULL
    // 0x945d80: b               #0x945db0
    // 0x945d84: LoadField: r0 = r2->field_b
    //     0x945d84: ldur            w0, [x2, #0xb]
    // 0x945d88: r1 = LoadInt32Instr(r0)
    //     0x945d88: sbfx            x1, x0, #1, #0x1f
    // 0x945d8c: mov             x0, x1
    // 0x945d90: r1 = 0
    //     0x945d90: movz            x1, #0
    // 0x945d94: cmp             x1, x0
    // 0x945d98: b.hs            #0x945fe0
    // 0x945d9c: LoadField: r0 = r2->field_f
    //     0x945d9c: ldur            w0, [x2, #0xf]
    // 0x945da0: DecompressPointer r0
    //     0x945da0: add             x0, x0, HEAP, lsl #32
    // 0x945da4: LoadField: r1 = r0->field_f
    //     0x945da4: ldur            w1, [x0, #0xf]
    // 0x945da8: DecompressPointer r1
    //     0x945da8: add             x1, x1, HEAP, lsl #32
    // 0x945dac: mov             x2, x1
    // 0x945db0: mov             x1, x3
    // 0x945db4: r0 = setUpAddress()
    //     0x945db4: bl              #0x945fec  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::setUpAddress
    // 0x945db8: r0 = LoadStaticField(0x878)
    //     0x945db8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x945dbc: ldr             x0, [x0, #0x10f0]
    // 0x945dc0: cmp             w0, NULL
    // 0x945dc4: b.eq            #0x945fe4
    // 0x945dc8: LoadField: r3 = r0->field_53
    //     0x945dc8: ldur            w3, [x0, #0x53]
    // 0x945dcc: DecompressPointer r3
    //     0x945dcc: add             x3, x3, HEAP, lsl #32
    // 0x945dd0: stur            x3, [fp, #-0x20]
    // 0x945dd4: LoadField: r0 = r3->field_7
    //     0x945dd4: ldur            w0, [x3, #7]
    // 0x945dd8: DecompressPointer r0
    //     0x945dd8: add             x0, x0, HEAP, lsl #32
    // 0x945ddc: ldur            x2, [fp, #-0x10]
    // 0x945de0: stur            x0, [fp, #-0x18]
    // 0x945de4: r1 = Function '<anonymous closure>':.
    //     0x945de4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54990] AnonymousClosure: (0x9040d8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::initState (0x945cec)
    //     0x945de8: ldr             x1, [x1, #0x990]
    // 0x945dec: r0 = AllocateClosure()
    //     0x945dec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x945df0: ldur            x2, [fp, #-0x18]
    // 0x945df4: mov             x3, x0
    // 0x945df8: r1 = Null
    //     0x945df8: mov             x1, NULL
    // 0x945dfc: stur            x3, [fp, #-0x18]
    // 0x945e00: cmp             w2, NULL
    // 0x945e04: b.eq            #0x945e24
    // 0x945e08: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x945e08: ldur            w4, [x2, #0x17]
    // 0x945e0c: DecompressPointer r4
    //     0x945e0c: add             x4, x4, HEAP, lsl #32
    // 0x945e10: r8 = X0
    //     0x945e10: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x945e14: LoadField: r9 = r4->field_7
    //     0x945e14: ldur            x9, [x4, #7]
    // 0x945e18: r3 = Null
    //     0x945e18: add             x3, PP, #0x54, lsl #12  ; [pp+0x54998] Null
    //     0x945e1c: ldr             x3, [x3, #0x998]
    // 0x945e20: blr             x9
    // 0x945e24: ldur            x0, [fp, #-0x20]
    // 0x945e28: LoadField: r1 = r0->field_b
    //     0x945e28: ldur            w1, [x0, #0xb]
    // 0x945e2c: LoadField: r2 = r0->field_f
    //     0x945e2c: ldur            w2, [x0, #0xf]
    // 0x945e30: DecompressPointer r2
    //     0x945e30: add             x2, x2, HEAP, lsl #32
    // 0x945e34: LoadField: r3 = r2->field_b
    //     0x945e34: ldur            w3, [x2, #0xb]
    // 0x945e38: r2 = LoadInt32Instr(r1)
    //     0x945e38: sbfx            x2, x1, #1, #0x1f
    // 0x945e3c: stur            x2, [fp, #-0x28]
    // 0x945e40: r1 = LoadInt32Instr(r3)
    //     0x945e40: sbfx            x1, x3, #1, #0x1f
    // 0x945e44: cmp             x2, x1
    // 0x945e48: b.ne            #0x945e54
    // 0x945e4c: mov             x1, x0
    // 0x945e50: r0 = _growToNextCapacity()
    //     0x945e50: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945e54: ldur            x3, [fp, #-8]
    // 0x945e58: ldur            x0, [fp, #-0x20]
    // 0x945e5c: ldur            x2, [fp, #-0x28]
    // 0x945e60: r4 = true
    //     0x945e60: add             x4, NULL, #0x20  ; true
    // 0x945e64: add             x1, x2, #1
    // 0x945e68: lsl             x5, x1, #1
    // 0x945e6c: StoreField: r0->field_b = r5
    //     0x945e6c: stur            w5, [x0, #0xb]
    // 0x945e70: LoadField: r1 = r0->field_f
    //     0x945e70: ldur            w1, [x0, #0xf]
    // 0x945e74: DecompressPointer r1
    //     0x945e74: add             x1, x1, HEAP, lsl #32
    // 0x945e78: ldur            x0, [fp, #-0x18]
    // 0x945e7c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x945e7c: add             x25, x1, x2, lsl #2
    //     0x945e80: add             x25, x25, #0xf
    //     0x945e84: str             w0, [x25]
    //     0x945e88: tbz             w0, #0, #0x945ea4
    //     0x945e8c: ldurb           w16, [x1, #-1]
    //     0x945e90: ldurb           w17, [x0, #-1]
    //     0x945e94: and             x16, x17, x16, lsr #2
    //     0x945e98: tst             x16, HEAP, lsr #32
    //     0x945e9c: b.eq            #0x945ea4
    //     0x945ea0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945ea4: StoreField: r3->field_4f = r4
    //     0x945ea4: stur            w4, [x3, #0x4f]
    // 0x945ea8: StoreField: r3->field_53 = r4
    //     0x945ea8: stur            w4, [x3, #0x53]
    // 0x945eac: StoreField: r3->field_57 = r4
    //     0x945eac: stur            w4, [x3, #0x57]
    // 0x945eb0: StoreField: r3->field_5b = r4
    //     0x945eb0: stur            w4, [x3, #0x5b]
    // 0x945eb4: StoreField: r3->field_5f = r4
    //     0x945eb4: stur            w4, [x3, #0x5f]
    // 0x945eb8: StoreField: r3->field_63 = r4
    //     0x945eb8: stur            w4, [x3, #0x63]
    // 0x945ebc: b               #0x945fc4
    // 0x945ec0: r0 = LoadStaticField(0x878)
    //     0x945ec0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x945ec4: ldr             x0, [x0, #0x10f0]
    // 0x945ec8: cmp             w0, NULL
    // 0x945ecc: b.eq            #0x945fe8
    // 0x945ed0: LoadField: r4 = r0->field_53
    //     0x945ed0: ldur            w4, [x0, #0x53]
    // 0x945ed4: DecompressPointer r4
    //     0x945ed4: add             x4, x4, HEAP, lsl #32
    // 0x945ed8: stur            x4, [fp, #-0x20]
    // 0x945edc: LoadField: r0 = r4->field_7
    //     0x945edc: ldur            w0, [x4, #7]
    // 0x945ee0: DecompressPointer r0
    //     0x945ee0: add             x0, x0, HEAP, lsl #32
    // 0x945ee4: ldur            x2, [fp, #-0x10]
    // 0x945ee8: stur            x0, [fp, #-0x18]
    // 0x945eec: r1 = Function '<anonymous closure>':.
    //     0x945eec: add             x1, PP, #0x54, lsl #12  ; [pp+0x549a8] AnonymousClosure: (0x946ab4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::initState (0x945cec)
    //     0x945ef0: ldr             x1, [x1, #0x9a8]
    // 0x945ef4: r0 = AllocateClosure()
    //     0x945ef4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x945ef8: ldur            x2, [fp, #-0x18]
    // 0x945efc: mov             x3, x0
    // 0x945f00: r1 = Null
    //     0x945f00: mov             x1, NULL
    // 0x945f04: stur            x3, [fp, #-0x10]
    // 0x945f08: cmp             w2, NULL
    // 0x945f0c: b.eq            #0x945f2c
    // 0x945f10: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x945f10: ldur            w4, [x2, #0x17]
    // 0x945f14: DecompressPointer r4
    //     0x945f14: add             x4, x4, HEAP, lsl #32
    // 0x945f18: r8 = X0
    //     0x945f18: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x945f1c: LoadField: r9 = r4->field_7
    //     0x945f1c: ldur            x9, [x4, #7]
    // 0x945f20: r3 = Null
    //     0x945f20: add             x3, PP, #0x54, lsl #12  ; [pp+0x549b0] Null
    //     0x945f24: ldr             x3, [x3, #0x9b0]
    // 0x945f28: blr             x9
    // 0x945f2c: ldur            x0, [fp, #-0x20]
    // 0x945f30: LoadField: r1 = r0->field_b
    //     0x945f30: ldur            w1, [x0, #0xb]
    // 0x945f34: LoadField: r2 = r0->field_f
    //     0x945f34: ldur            w2, [x0, #0xf]
    // 0x945f38: DecompressPointer r2
    //     0x945f38: add             x2, x2, HEAP, lsl #32
    // 0x945f3c: LoadField: r3 = r2->field_b
    //     0x945f3c: ldur            w3, [x2, #0xb]
    // 0x945f40: r2 = LoadInt32Instr(r1)
    //     0x945f40: sbfx            x2, x1, #1, #0x1f
    // 0x945f44: stur            x2, [fp, #-0x28]
    // 0x945f48: r1 = LoadInt32Instr(r3)
    //     0x945f48: sbfx            x1, x3, #1, #0x1f
    // 0x945f4c: cmp             x2, x1
    // 0x945f50: b.ne            #0x945f5c
    // 0x945f54: mov             x1, x0
    // 0x945f58: r0 = _growToNextCapacity()
    //     0x945f58: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945f5c: ldur            x4, [fp, #-8]
    // 0x945f60: ldur            x2, [fp, #-0x20]
    // 0x945f64: ldur            x3, [fp, #-0x28]
    // 0x945f68: r5 = false
    //     0x945f68: add             x5, NULL, #0x30  ; false
    // 0x945f6c: add             x6, x3, #1
    // 0x945f70: lsl             x7, x6, #1
    // 0x945f74: StoreField: r2->field_b = r7
    //     0x945f74: stur            w7, [x2, #0xb]
    // 0x945f78: LoadField: r1 = r2->field_f
    //     0x945f78: ldur            w1, [x2, #0xf]
    // 0x945f7c: DecompressPointer r1
    //     0x945f7c: add             x1, x1, HEAP, lsl #32
    // 0x945f80: ldur            x0, [fp, #-0x10]
    // 0x945f84: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945f84: add             x25, x1, x3, lsl #2
    //     0x945f88: add             x25, x25, #0xf
    //     0x945f8c: str             w0, [x25]
    //     0x945f90: tbz             w0, #0, #0x945fac
    //     0x945f94: ldurb           w16, [x1, #-1]
    //     0x945f98: ldurb           w17, [x0, #-1]
    //     0x945f9c: and             x16, x17, x16, lsr #2
    //     0x945fa0: tst             x16, HEAP, lsr #32
    //     0x945fa4: b.eq            #0x945fac
    //     0x945fa8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945fac: StoreField: r4->field_4f = r5
    //     0x945fac: stur            w5, [x4, #0x4f]
    // 0x945fb0: StoreField: r4->field_53 = r5
    //     0x945fb0: stur            w5, [x4, #0x53]
    // 0x945fb4: StoreField: r4->field_57 = r5
    //     0x945fb4: stur            w5, [x4, #0x57]
    // 0x945fb8: StoreField: r4->field_5b = r5
    //     0x945fb8: stur            w5, [x4, #0x5b]
    // 0x945fbc: StoreField: r4->field_5f = r5
    //     0x945fbc: stur            w5, [x4, #0x5f]
    // 0x945fc0: StoreField: r4->field_63 = r5
    //     0x945fc0: stur            w5, [x4, #0x63]
    // 0x945fc4: r0 = Null
    //     0x945fc4: mov             x0, NULL
    // 0x945fc8: LeaveFrame
    //     0x945fc8: mov             SP, fp
    //     0x945fcc: ldp             fp, lr, [SP], #0x10
    // 0x945fd0: ret
    //     0x945fd0: ret             
    // 0x945fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945fd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945fd8: b               #0x945d08
    // 0x945fdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945fdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945fe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x945fe0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x945fe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945fe4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945fe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945fe8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setUpAddress(/* No info */) {
    // ** addr: 0x945fec, size: 0x788
    // 0x945fec: EnterFrame
    //     0x945fec: stp             fp, lr, [SP, #-0x10]!
    //     0x945ff0: mov             fp, SP
    // 0x945ff4: AllocStack(0x48)
    //     0x945ff4: sub             SP, SP, #0x48
    // 0x945ff8: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x945ff8: stur            x1, [fp, #-8]
    //     0x945ffc: stur            x2, [fp, #-0x10]
    // 0x946000: CheckStackOverflow
    //     0x946000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x946004: cmp             SP, x16
    //     0x946008: b.ls            #0x94675c
    // 0x94600c: r1 = 1
    //     0x94600c: movz            x1, #0x1
    // 0x946010: r0 = AllocateContext()
    //     0x946010: bl              #0x16f6108  ; AllocateContextStub
    // 0x946014: mov             x1, x0
    // 0x946018: ldur            x0, [fp, #-8]
    // 0x94601c: stur            x1, [fp, #-0x30]
    // 0x946020: StoreField: r1->field_f = r0
    //     0x946020: stur            w0, [x1, #0xf]
    // 0x946024: LoadField: r2 = r0->field_3f
    //     0x946024: ldur            w2, [x0, #0x3f]
    // 0x946028: DecompressPointer r2
    //     0x946028: add             x2, x2, HEAP, lsl #32
    // 0x94602c: ldur            x3, [fp, #-0x10]
    // 0x946030: stur            x2, [fp, #-0x28]
    // 0x946034: cmp             w3, NULL
    // 0x946038: b.ne            #0x946044
    // 0x94603c: r4 = Null
    //     0x94603c: mov             x4, NULL
    // 0x946040: b               #0x94604c
    // 0x946044: LoadField: r4 = r3->field_1b
    //     0x946044: ldur            w4, [x3, #0x1b]
    // 0x946048: DecompressPointer r4
    //     0x946048: add             x4, x4, HEAP, lsl #32
    // 0x94604c: cmp             w4, NULL
    // 0x946050: b.ne            #0x946058
    // 0x946054: r4 = ""
    //     0x946054: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946058: stur            x4, [fp, #-0x20]
    // 0x94605c: cmp             w3, NULL
    // 0x946060: b.ne            #0x94606c
    // 0x946064: r5 = Null
    //     0x946064: mov             x5, NULL
    // 0x946068: b               #0x94608c
    // 0x94606c: LoadField: r5 = r3->field_1b
    //     0x94606c: ldur            w5, [x3, #0x1b]
    // 0x946070: DecompressPointer r5
    //     0x946070: add             x5, x5, HEAP, lsl #32
    // 0x946074: cmp             w5, NULL
    // 0x946078: b.ne            #0x946084
    // 0x94607c: r5 = Null
    //     0x94607c: mov             x5, NULL
    // 0x946080: b               #0x94608c
    // 0x946084: LoadField: r6 = r5->field_7
    //     0x946084: ldur            w6, [x5, #7]
    // 0x946088: mov             x5, x6
    // 0x94608c: cmp             w5, NULL
    // 0x946090: b.ne            #0x94609c
    // 0x946094: r5 = 0
    //     0x946094: movz            x5, #0
    // 0x946098: b               #0x9460a4
    // 0x94609c: r6 = LoadInt32Instr(r5)
    //     0x94609c: sbfx            x6, x5, #1, #0x1f
    // 0x9460a0: mov             x5, x6
    // 0x9460a4: stur            x5, [fp, #-0x18]
    // 0x9460a8: r0 = TextSelection()
    //     0x9460a8: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x9460ac: mov             x1, x0
    // 0x9460b0: ldur            x0, [fp, #-0x18]
    // 0x9460b4: stur            x1, [fp, #-0x38]
    // 0x9460b8: ArrayStore: r1[0] = r0  ; List_8
    //     0x9460b8: stur            x0, [x1, #0x17]
    // 0x9460bc: StoreField: r1->field_1f = r0
    //     0x9460bc: stur            x0, [x1, #0x1f]
    // 0x9460c0: r2 = Instance_TextAffinity
    //     0x9460c0: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x9460c4: StoreField: r1->field_27 = r2
    //     0x9460c4: stur            w2, [x1, #0x27]
    // 0x9460c8: r3 = false
    //     0x9460c8: add             x3, NULL, #0x30  ; false
    // 0x9460cc: StoreField: r1->field_2b = r3
    //     0x9460cc: stur            w3, [x1, #0x2b]
    // 0x9460d0: StoreField: r1->field_7 = r0
    //     0x9460d0: stur            x0, [x1, #7]
    // 0x9460d4: StoreField: r1->field_f = r0
    //     0x9460d4: stur            x0, [x1, #0xf]
    // 0x9460d8: r0 = TextEditingValue()
    //     0x9460d8: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x9460dc: mov             x1, x0
    // 0x9460e0: ldur            x0, [fp, #-0x20]
    // 0x9460e4: StoreField: r1->field_7 = r0
    //     0x9460e4: stur            w0, [x1, #7]
    // 0x9460e8: ldur            x0, [fp, #-0x38]
    // 0x9460ec: StoreField: r1->field_b = r0
    //     0x9460ec: stur            w0, [x1, #0xb]
    // 0x9460f0: r0 = Instance_TextRange
    //     0x9460f0: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x9460f4: StoreField: r1->field_f = r0
    //     0x9460f4: stur            w0, [x1, #0xf]
    // 0x9460f8: mov             x2, x1
    // 0x9460fc: ldur            x1, [fp, #-0x28]
    // 0x946100: r0 = value=()
    //     0x946100: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x946104: ldur            x0, [fp, #-8]
    // 0x946108: LoadField: r1 = r0->field_3f
    //     0x946108: ldur            w1, [x0, #0x3f]
    // 0x94610c: DecompressPointer r1
    //     0x94610c: add             x1, x1, HEAP, lsl #32
    // 0x946110: LoadField: r2 = r1->field_27
    //     0x946110: ldur            w2, [x1, #0x27]
    // 0x946114: DecompressPointer r2
    //     0x946114: add             x2, x2, HEAP, lsl #32
    // 0x946118: LoadField: r1 = r2->field_7
    //     0x946118: ldur            w1, [x2, #7]
    // 0x94611c: DecompressPointer r1
    //     0x94611c: add             x1, x1, HEAP, lsl #32
    // 0x946120: LoadField: r2 = r1->field_7
    //     0x946120: ldur            w2, [x1, #7]
    // 0x946124: cmp             w2, #0xc
    // 0x946128: b.ne            #0x946160
    // 0x94612c: LoadField: r2 = r0->field_b
    //     0x94612c: ldur            w2, [x0, #0xb]
    // 0x946130: DecompressPointer r2
    //     0x946130: add             x2, x2, HEAP, lsl #32
    // 0x946134: cmp             w2, NULL
    // 0x946138: b.eq            #0x946764
    // 0x94613c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x94613c: ldur            w3, [x2, #0x17]
    // 0x946140: DecompressPointer r3
    //     0x946140: add             x3, x3, HEAP, lsl #32
    // 0x946144: stp             x1, x3, [SP]
    // 0x946148: r4 = 0
    //     0x946148: movz            x4, #0
    // 0x94614c: ldr             x0, [SP, #8]
    // 0x946150: r16 = UnlinkedCall_0x613b5c
    //     0x946150: add             x16, PP, #0x54, lsl #12  ; [pp+0x549d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x946154: add             x16, x16, #0x9d8
    // 0x946158: ldp             x5, lr, [x16]
    // 0x94615c: blr             lr
    // 0x946160: ldur            x0, [fp, #-8]
    // 0x946164: ldur            x1, [fp, #-0x10]
    // 0x946168: LoadField: r2 = r0->field_3b
    //     0x946168: ldur            w2, [x0, #0x3b]
    // 0x94616c: DecompressPointer r2
    //     0x94616c: add             x2, x2, HEAP, lsl #32
    // 0x946170: stur            x2, [fp, #-0x28]
    // 0x946174: cmp             w1, NULL
    // 0x946178: b.ne            #0x946184
    // 0x94617c: r3 = Null
    //     0x94617c: mov             x3, NULL
    // 0x946180: b               #0x94618c
    // 0x946184: LoadField: r3 = r1->field_2b
    //     0x946184: ldur            w3, [x1, #0x2b]
    // 0x946188: DecompressPointer r3
    //     0x946188: add             x3, x3, HEAP, lsl #32
    // 0x94618c: cmp             w3, NULL
    // 0x946190: b.ne            #0x946198
    // 0x946194: r3 = ""
    //     0x946194: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946198: stur            x3, [fp, #-0x20]
    // 0x94619c: cmp             w1, NULL
    // 0x9461a0: b.ne            #0x9461ac
    // 0x9461a4: r4 = Null
    //     0x9461a4: mov             x4, NULL
    // 0x9461a8: b               #0x9461cc
    // 0x9461ac: LoadField: r4 = r1->field_2b
    //     0x9461ac: ldur            w4, [x1, #0x2b]
    // 0x9461b0: DecompressPointer r4
    //     0x9461b0: add             x4, x4, HEAP, lsl #32
    // 0x9461b4: cmp             w4, NULL
    // 0x9461b8: b.ne            #0x9461c4
    // 0x9461bc: r4 = Null
    //     0x9461bc: mov             x4, NULL
    // 0x9461c0: b               #0x9461cc
    // 0x9461c4: LoadField: r5 = r4->field_7
    //     0x9461c4: ldur            w5, [x4, #7]
    // 0x9461c8: mov             x4, x5
    // 0x9461cc: cmp             w4, NULL
    // 0x9461d0: b.ne            #0x9461dc
    // 0x9461d4: r4 = 0
    //     0x9461d4: movz            x4, #0
    // 0x9461d8: b               #0x9461e4
    // 0x9461dc: r5 = LoadInt32Instr(r4)
    //     0x9461dc: sbfx            x5, x4, #1, #0x1f
    // 0x9461e0: mov             x4, x5
    // 0x9461e4: stur            x4, [fp, #-0x18]
    // 0x9461e8: r0 = TextSelection()
    //     0x9461e8: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x9461ec: mov             x1, x0
    // 0x9461f0: ldur            x0, [fp, #-0x18]
    // 0x9461f4: stur            x1, [fp, #-0x38]
    // 0x9461f8: ArrayStore: r1[0] = r0  ; List_8
    //     0x9461f8: stur            x0, [x1, #0x17]
    // 0x9461fc: StoreField: r1->field_1f = r0
    //     0x9461fc: stur            x0, [x1, #0x1f]
    // 0x946200: r2 = Instance_TextAffinity
    //     0x946200: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x946204: StoreField: r1->field_27 = r2
    //     0x946204: stur            w2, [x1, #0x27]
    // 0x946208: r3 = false
    //     0x946208: add             x3, NULL, #0x30  ; false
    // 0x94620c: StoreField: r1->field_2b = r3
    //     0x94620c: stur            w3, [x1, #0x2b]
    // 0x946210: StoreField: r1->field_7 = r0
    //     0x946210: stur            x0, [x1, #7]
    // 0x946214: StoreField: r1->field_f = r0
    //     0x946214: stur            x0, [x1, #0xf]
    // 0x946218: r0 = TextEditingValue()
    //     0x946218: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x94621c: mov             x1, x0
    // 0x946220: ldur            x0, [fp, #-0x20]
    // 0x946224: StoreField: r1->field_7 = r0
    //     0x946224: stur            w0, [x1, #7]
    // 0x946228: ldur            x0, [fp, #-0x38]
    // 0x94622c: StoreField: r1->field_b = r0
    //     0x94622c: stur            w0, [x1, #0xb]
    // 0x946230: r0 = Instance_TextRange
    //     0x946230: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x946234: StoreField: r1->field_f = r0
    //     0x946234: stur            w0, [x1, #0xf]
    // 0x946238: mov             x2, x1
    // 0x94623c: ldur            x1, [fp, #-0x28]
    // 0x946240: r0 = value=()
    //     0x946240: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x946244: ldur            x0, [fp, #-8]
    // 0x946248: LoadField: r1 = r0->field_47
    //     0x946248: ldur            w1, [x0, #0x47]
    // 0x94624c: DecompressPointer r1
    //     0x94624c: add             x1, x1, HEAP, lsl #32
    // 0x946250: ldur            x2, [fp, #-0x10]
    // 0x946254: stur            x1, [fp, #-0x28]
    // 0x946258: cmp             w2, NULL
    // 0x94625c: b.ne            #0x946268
    // 0x946260: r3 = Null
    //     0x946260: mov             x3, NULL
    // 0x946264: b               #0x946270
    // 0x946268: LoadField: r3 = r2->field_13
    //     0x946268: ldur            w3, [x2, #0x13]
    // 0x94626c: DecompressPointer r3
    //     0x94626c: add             x3, x3, HEAP, lsl #32
    // 0x946270: cmp             w3, NULL
    // 0x946274: b.ne            #0x94627c
    // 0x946278: r3 = ""
    //     0x946278: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94627c: stur            x3, [fp, #-0x20]
    // 0x946280: cmp             w2, NULL
    // 0x946284: b.ne            #0x946290
    // 0x946288: r4 = Null
    //     0x946288: mov             x4, NULL
    // 0x94628c: b               #0x9462b0
    // 0x946290: LoadField: r4 = r2->field_13
    //     0x946290: ldur            w4, [x2, #0x13]
    // 0x946294: DecompressPointer r4
    //     0x946294: add             x4, x4, HEAP, lsl #32
    // 0x946298: cmp             w4, NULL
    // 0x94629c: b.ne            #0x9462a8
    // 0x9462a0: r4 = Null
    //     0x9462a0: mov             x4, NULL
    // 0x9462a4: b               #0x9462b0
    // 0x9462a8: LoadField: r5 = r4->field_7
    //     0x9462a8: ldur            w5, [x4, #7]
    // 0x9462ac: mov             x4, x5
    // 0x9462b0: cmp             w4, NULL
    // 0x9462b4: b.ne            #0x9462c0
    // 0x9462b8: r4 = 0
    //     0x9462b8: movz            x4, #0
    // 0x9462bc: b               #0x9462c8
    // 0x9462c0: r5 = LoadInt32Instr(r4)
    //     0x9462c0: sbfx            x5, x4, #1, #0x1f
    // 0x9462c4: mov             x4, x5
    // 0x9462c8: stur            x4, [fp, #-0x18]
    // 0x9462cc: r0 = TextSelection()
    //     0x9462cc: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x9462d0: mov             x1, x0
    // 0x9462d4: ldur            x0, [fp, #-0x18]
    // 0x9462d8: stur            x1, [fp, #-0x38]
    // 0x9462dc: ArrayStore: r1[0] = r0  ; List_8
    //     0x9462dc: stur            x0, [x1, #0x17]
    // 0x9462e0: StoreField: r1->field_1f = r0
    //     0x9462e0: stur            x0, [x1, #0x1f]
    // 0x9462e4: r2 = Instance_TextAffinity
    //     0x9462e4: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x9462e8: StoreField: r1->field_27 = r2
    //     0x9462e8: stur            w2, [x1, #0x27]
    // 0x9462ec: r3 = false
    //     0x9462ec: add             x3, NULL, #0x30  ; false
    // 0x9462f0: StoreField: r1->field_2b = r3
    //     0x9462f0: stur            w3, [x1, #0x2b]
    // 0x9462f4: StoreField: r1->field_7 = r0
    //     0x9462f4: stur            x0, [x1, #7]
    // 0x9462f8: StoreField: r1->field_f = r0
    //     0x9462f8: stur            x0, [x1, #0xf]
    // 0x9462fc: r0 = TextEditingValue()
    //     0x9462fc: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x946300: mov             x1, x0
    // 0x946304: ldur            x0, [fp, #-0x20]
    // 0x946308: StoreField: r1->field_7 = r0
    //     0x946308: stur            w0, [x1, #7]
    // 0x94630c: ldur            x0, [fp, #-0x38]
    // 0x946310: StoreField: r1->field_b = r0
    //     0x946310: stur            w0, [x1, #0xb]
    // 0x946314: r0 = Instance_TextRange
    //     0x946314: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x946318: StoreField: r1->field_f = r0
    //     0x946318: stur            w0, [x1, #0xf]
    // 0x94631c: mov             x2, x1
    // 0x946320: ldur            x1, [fp, #-0x28]
    // 0x946324: r0 = value=()
    //     0x946324: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x946328: ldur            x0, [fp, #-8]
    // 0x94632c: LoadField: r1 = r0->field_37
    //     0x94632c: ldur            w1, [x0, #0x37]
    // 0x946330: DecompressPointer r1
    //     0x946330: add             x1, x1, HEAP, lsl #32
    // 0x946334: ldur            x2, [fp, #-0x10]
    // 0x946338: stur            x1, [fp, #-0x28]
    // 0x94633c: cmp             w2, NULL
    // 0x946340: b.ne            #0x94634c
    // 0x946344: r3 = Null
    //     0x946344: mov             x3, NULL
    // 0x946348: b               #0x946354
    // 0x94634c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x94634c: ldur            w3, [x2, #0x17]
    // 0x946350: DecompressPointer r3
    //     0x946350: add             x3, x3, HEAP, lsl #32
    // 0x946354: cmp             w3, NULL
    // 0x946358: b.ne            #0x946360
    // 0x94635c: r3 = ""
    //     0x94635c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946360: stur            x3, [fp, #-0x20]
    // 0x946364: cmp             w2, NULL
    // 0x946368: b.ne            #0x946374
    // 0x94636c: r4 = Null
    //     0x94636c: mov             x4, NULL
    // 0x946370: b               #0x946394
    // 0x946374: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x946374: ldur            w4, [x2, #0x17]
    // 0x946378: DecompressPointer r4
    //     0x946378: add             x4, x4, HEAP, lsl #32
    // 0x94637c: cmp             w4, NULL
    // 0x946380: b.ne            #0x94638c
    // 0x946384: r4 = Null
    //     0x946384: mov             x4, NULL
    // 0x946388: b               #0x946394
    // 0x94638c: LoadField: r5 = r4->field_7
    //     0x94638c: ldur            w5, [x4, #7]
    // 0x946390: mov             x4, x5
    // 0x946394: cmp             w4, NULL
    // 0x946398: b.ne            #0x9463a4
    // 0x94639c: r4 = 0
    //     0x94639c: movz            x4, #0
    // 0x9463a0: b               #0x9463ac
    // 0x9463a4: r5 = LoadInt32Instr(r4)
    //     0x9463a4: sbfx            x5, x4, #1, #0x1f
    // 0x9463a8: mov             x4, x5
    // 0x9463ac: stur            x4, [fp, #-0x18]
    // 0x9463b0: r0 = TextSelection()
    //     0x9463b0: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x9463b4: mov             x1, x0
    // 0x9463b8: ldur            x0, [fp, #-0x18]
    // 0x9463bc: stur            x1, [fp, #-0x38]
    // 0x9463c0: ArrayStore: r1[0] = r0  ; List_8
    //     0x9463c0: stur            x0, [x1, #0x17]
    // 0x9463c4: StoreField: r1->field_1f = r0
    //     0x9463c4: stur            x0, [x1, #0x1f]
    // 0x9463c8: r2 = Instance_TextAffinity
    //     0x9463c8: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x9463cc: StoreField: r1->field_27 = r2
    //     0x9463cc: stur            w2, [x1, #0x27]
    // 0x9463d0: r3 = false
    //     0x9463d0: add             x3, NULL, #0x30  ; false
    // 0x9463d4: StoreField: r1->field_2b = r3
    //     0x9463d4: stur            w3, [x1, #0x2b]
    // 0x9463d8: StoreField: r1->field_7 = r0
    //     0x9463d8: stur            x0, [x1, #7]
    // 0x9463dc: StoreField: r1->field_f = r0
    //     0x9463dc: stur            x0, [x1, #0xf]
    // 0x9463e0: r0 = TextEditingValue()
    //     0x9463e0: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x9463e4: mov             x1, x0
    // 0x9463e8: ldur            x0, [fp, #-0x20]
    // 0x9463ec: StoreField: r1->field_7 = r0
    //     0x9463ec: stur            w0, [x1, #7]
    // 0x9463f0: ldur            x0, [fp, #-0x38]
    // 0x9463f4: StoreField: r1->field_b = r0
    //     0x9463f4: stur            w0, [x1, #0xb]
    // 0x9463f8: r0 = Instance_TextRange
    //     0x9463f8: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x9463fc: StoreField: r1->field_f = r0
    //     0x9463fc: stur            w0, [x1, #0xf]
    // 0x946400: mov             x2, x1
    // 0x946404: ldur            x1, [fp, #-0x28]
    // 0x946408: r0 = value=()
    //     0x946408: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x94640c: ldur            x0, [fp, #-8]
    // 0x946410: LoadField: r1 = r0->field_43
    //     0x946410: ldur            w1, [x0, #0x43]
    // 0x946414: DecompressPointer r1
    //     0x946414: add             x1, x1, HEAP, lsl #32
    // 0x946418: ldur            x2, [fp, #-0x10]
    // 0x94641c: stur            x1, [fp, #-0x28]
    // 0x946420: cmp             w2, NULL
    // 0x946424: b.ne            #0x946430
    // 0x946428: r3 = Null
    //     0x946428: mov             x3, NULL
    // 0x94642c: b               #0x946438
    // 0x946430: LoadField: r3 = r2->field_2f
    //     0x946430: ldur            w3, [x2, #0x2f]
    // 0x946434: DecompressPointer r3
    //     0x946434: add             x3, x3, HEAP, lsl #32
    // 0x946438: cmp             w3, NULL
    // 0x94643c: b.ne            #0x946444
    // 0x946440: r3 = ""
    //     0x946440: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946444: stur            x3, [fp, #-0x20]
    // 0x946448: cmp             w2, NULL
    // 0x94644c: b.ne            #0x946458
    // 0x946450: r2 = Null
    //     0x946450: mov             x2, NULL
    // 0x946454: b               #0x946474
    // 0x946458: LoadField: r4 = r2->field_2f
    //     0x946458: ldur            w4, [x2, #0x2f]
    // 0x94645c: DecompressPointer r4
    //     0x94645c: add             x4, x4, HEAP, lsl #32
    // 0x946460: cmp             w4, NULL
    // 0x946464: b.ne            #0x946470
    // 0x946468: r2 = Null
    //     0x946468: mov             x2, NULL
    // 0x94646c: b               #0x946474
    // 0x946470: LoadField: r2 = r4->field_7
    //     0x946470: ldur            w2, [x4, #7]
    // 0x946474: cmp             w2, NULL
    // 0x946478: b.ne            #0x946484
    // 0x94647c: r2 = 0
    //     0x94647c: movz            x2, #0
    // 0x946480: b               #0x94648c
    // 0x946484: r4 = LoadInt32Instr(r2)
    //     0x946484: sbfx            x4, x2, #1, #0x1f
    // 0x946488: mov             x2, x4
    // 0x94648c: stur            x2, [fp, #-0x18]
    // 0x946490: r0 = TextSelection()
    //     0x946490: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x946494: mov             x1, x0
    // 0x946498: ldur            x0, [fp, #-0x18]
    // 0x94649c: stur            x1, [fp, #-0x10]
    // 0x9464a0: ArrayStore: r1[0] = r0  ; List_8
    //     0x9464a0: stur            x0, [x1, #0x17]
    // 0x9464a4: StoreField: r1->field_1f = r0
    //     0x9464a4: stur            x0, [x1, #0x1f]
    // 0x9464a8: r2 = Instance_TextAffinity
    //     0x9464a8: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x9464ac: StoreField: r1->field_27 = r2
    //     0x9464ac: stur            w2, [x1, #0x27]
    // 0x9464b0: r3 = false
    //     0x9464b0: add             x3, NULL, #0x30  ; false
    // 0x9464b4: StoreField: r1->field_2b = r3
    //     0x9464b4: stur            w3, [x1, #0x2b]
    // 0x9464b8: StoreField: r1->field_7 = r0
    //     0x9464b8: stur            x0, [x1, #7]
    // 0x9464bc: StoreField: r1->field_f = r0
    //     0x9464bc: stur            x0, [x1, #0xf]
    // 0x9464c0: r0 = TextEditingValue()
    //     0x9464c0: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x9464c4: mov             x1, x0
    // 0x9464c8: ldur            x0, [fp, #-0x20]
    // 0x9464cc: StoreField: r1->field_7 = r0
    //     0x9464cc: stur            w0, [x1, #7]
    // 0x9464d0: ldur            x0, [fp, #-0x10]
    // 0x9464d4: StoreField: r1->field_b = r0
    //     0x9464d4: stur            w0, [x1, #0xb]
    // 0x9464d8: r0 = Instance_TextRange
    //     0x9464d8: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x9464dc: StoreField: r1->field_f = r0
    //     0x9464dc: stur            w0, [x1, #0xf]
    // 0x9464e0: mov             x2, x1
    // 0x9464e4: ldur            x1, [fp, #-0x28]
    // 0x9464e8: r0 = value=()
    //     0x9464e8: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9464ec: ldur            x0, [fp, #-8]
    // 0x9464f0: LoadField: r3 = r0->field_33
    //     0x9464f0: ldur            w3, [x0, #0x33]
    // 0x9464f4: DecompressPointer r3
    //     0x9464f4: add             x3, x3, HEAP, lsl #32
    // 0x9464f8: stur            x3, [fp, #-0x28]
    // 0x9464fc: LoadField: r1 = r0->field_b
    //     0x9464fc: ldur            w1, [x0, #0xb]
    // 0x946500: DecompressPointer r1
    //     0x946500: add             x1, x1, HEAP, lsl #32
    // 0x946504: cmp             w1, NULL
    // 0x946508: b.eq            #0x946768
    // 0x94650c: LoadField: r4 = r1->field_1f
    //     0x94650c: ldur            w4, [x1, #0x1f]
    // 0x946510: DecompressPointer r4
    //     0x946510: add             x4, x4, HEAP, lsl #32
    // 0x946514: stur            x4, [fp, #-0x20]
    // 0x946518: LoadField: r1 = r4->field_b
    //     0x946518: ldur            w1, [x4, #0xb]
    // 0x94651c: DecompressPointer r1
    //     0x94651c: add             x1, x1, HEAP, lsl #32
    // 0x946520: cmp             w1, NULL
    // 0x946524: b.ne            #0x946530
    // 0x946528: r5 = ""
    //     0x946528: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94652c: b               #0x946534
    // 0x946530: mov             x5, x1
    // 0x946534: stur            x5, [fp, #-0x10]
    // 0x946538: r1 = Null
    //     0x946538: mov             x1, NULL
    // 0x94653c: r2 = 6
    //     0x94653c: movz            x2, #0x6
    // 0x946540: r0 = AllocateArray()
    //     0x946540: bl              #0x16f7198  ; AllocateArrayStub
    // 0x946544: mov             x1, x0
    // 0x946548: ldur            x0, [fp, #-0x10]
    // 0x94654c: StoreField: r1->field_f = r0
    //     0x94654c: stur            w0, [x1, #0xf]
    // 0x946550: r16 = " "
    //     0x946550: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x946554: StoreField: r1->field_13 = r16
    //     0x946554: stur            w16, [x1, #0x13]
    // 0x946558: ldur            x0, [fp, #-0x20]
    // 0x94655c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x94655c: ldur            w2, [x0, #0x17]
    // 0x946560: DecompressPointer r2
    //     0x946560: add             x2, x2, HEAP, lsl #32
    // 0x946564: cmp             w2, NULL
    // 0x946568: b.ne            #0x946570
    // 0x94656c: r2 = ""
    //     0x94656c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946570: ldur            x0, [fp, #-8]
    // 0x946574: ArrayStore: r1[0] = r2  ; List_4
    //     0x946574: stur            w2, [x1, #0x17]
    // 0x946578: str             x1, [SP]
    // 0x94657c: r0 = _interpolate()
    //     0x94657c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x946580: mov             x1, x0
    // 0x946584: ldur            x0, [fp, #-8]
    // 0x946588: stur            x1, [fp, #-0x10]
    // 0x94658c: LoadField: r2 = r0->field_b
    //     0x94658c: ldur            w2, [x0, #0xb]
    // 0x946590: DecompressPointer r2
    //     0x946590: add             x2, x2, HEAP, lsl #32
    // 0x946594: cmp             w2, NULL
    // 0x946598: b.eq            #0x94676c
    // 0x94659c: LoadField: r0 = r2->field_1f
    //     0x94659c: ldur            w0, [x2, #0x1f]
    // 0x9465a0: DecompressPointer r0
    //     0x9465a0: add             x0, x0, HEAP, lsl #32
    // 0x9465a4: LoadField: r2 = r0->field_b
    //     0x9465a4: ldur            w2, [x0, #0xb]
    // 0x9465a8: DecompressPointer r2
    //     0x9465a8: add             x2, x2, HEAP, lsl #32
    // 0x9465ac: cmp             w2, NULL
    // 0x9465b0: b.ne            #0x9465bc
    // 0x9465b4: r2 = Null
    //     0x9465b4: mov             x2, NULL
    // 0x9465b8: b               #0x9465c4
    // 0x9465bc: LoadField: r3 = r2->field_7
    //     0x9465bc: ldur            w3, [x2, #7]
    // 0x9465c0: mov             x2, x3
    // 0x9465c4: cmp             w2, NULL
    // 0x9465c8: b.ne            #0x946604
    // 0x9465cc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9465cc: ldur            w2, [x0, #0x17]
    // 0x9465d0: DecompressPointer r2
    //     0x9465d0: add             x2, x2, HEAP, lsl #32
    // 0x9465d4: cmp             w2, NULL
    // 0x9465d8: b.ne            #0x9465e4
    // 0x9465dc: r0 = Null
    //     0x9465dc: mov             x0, NULL
    // 0x9465e0: b               #0x9465e8
    // 0x9465e4: LoadField: r0 = r2->field_7
    //     0x9465e4: ldur            w0, [x2, #7]
    // 0x9465e8: cmp             w0, NULL
    // 0x9465ec: b.ne            #0x9465f8
    // 0x9465f0: r0 = 0
    //     0x9465f0: movz            x0, #0
    // 0x9465f4: b               #0x946608
    // 0x9465f8: r2 = LoadInt32Instr(r0)
    //     0x9465f8: sbfx            x2, x0, #1, #0x1f
    // 0x9465fc: mov             x0, x2
    // 0x946600: b               #0x946608
    // 0x946604: r0 = LoadInt32Instr(r2)
    //     0x946604: sbfx            x0, x2, #1, #0x1f
    // 0x946608: stur            x0, [fp, #-0x18]
    // 0x94660c: r0 = TextSelection()
    //     0x94660c: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x946610: mov             x1, x0
    // 0x946614: ldur            x0, [fp, #-0x18]
    // 0x946618: stur            x1, [fp, #-8]
    // 0x94661c: ArrayStore: r1[0] = r0  ; List_8
    //     0x94661c: stur            x0, [x1, #0x17]
    // 0x946620: StoreField: r1->field_1f = r0
    //     0x946620: stur            x0, [x1, #0x1f]
    // 0x946624: r2 = Instance_TextAffinity
    //     0x946624: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x946628: StoreField: r1->field_27 = r2
    //     0x946628: stur            w2, [x1, #0x27]
    // 0x94662c: r2 = false
    //     0x94662c: add             x2, NULL, #0x30  ; false
    // 0x946630: StoreField: r1->field_2b = r2
    //     0x946630: stur            w2, [x1, #0x2b]
    // 0x946634: StoreField: r1->field_7 = r0
    //     0x946634: stur            x0, [x1, #7]
    // 0x946638: StoreField: r1->field_f = r0
    //     0x946638: stur            x0, [x1, #0xf]
    // 0x94663c: r0 = TextEditingValue()
    //     0x94663c: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x946640: mov             x1, x0
    // 0x946644: ldur            x0, [fp, #-0x10]
    // 0x946648: StoreField: r1->field_7 = r0
    //     0x946648: stur            w0, [x1, #7]
    // 0x94664c: ldur            x0, [fp, #-8]
    // 0x946650: StoreField: r1->field_b = r0
    //     0x946650: stur            w0, [x1, #0xb]
    // 0x946654: r0 = Instance_TextRange
    //     0x946654: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x946658: StoreField: r1->field_f = r0
    //     0x946658: stur            w0, [x1, #0xf]
    // 0x94665c: mov             x2, x1
    // 0x946660: ldur            x1, [fp, #-0x28]
    // 0x946664: r0 = value=()
    //     0x946664: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x946668: r0 = LoadStaticField(0x878)
    //     0x946668: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94666c: ldr             x0, [x0, #0x10f0]
    // 0x946670: cmp             w0, NULL
    // 0x946674: b.eq            #0x946770
    // 0x946678: LoadField: r3 = r0->field_53
    //     0x946678: ldur            w3, [x0, #0x53]
    // 0x94667c: DecompressPointer r3
    //     0x94667c: add             x3, x3, HEAP, lsl #32
    // 0x946680: stur            x3, [fp, #-0x10]
    // 0x946684: LoadField: r0 = r3->field_7
    //     0x946684: ldur            w0, [x3, #7]
    // 0x946688: DecompressPointer r0
    //     0x946688: add             x0, x0, HEAP, lsl #32
    // 0x94668c: ldur            x2, [fp, #-0x30]
    // 0x946690: stur            x0, [fp, #-8]
    // 0x946694: r1 = Function '<anonymous closure>':.
    //     0x946694: add             x1, PP, #0x54, lsl #12  ; [pp+0x549e8] AnonymousClosure: (0x946774), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::setUpAddress (0x945fec)
    //     0x946698: ldr             x1, [x1, #0x9e8]
    // 0x94669c: r0 = AllocateClosure()
    //     0x94669c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9466a0: ldur            x2, [fp, #-8]
    // 0x9466a4: mov             x3, x0
    // 0x9466a8: r1 = Null
    //     0x9466a8: mov             x1, NULL
    // 0x9466ac: stur            x3, [fp, #-8]
    // 0x9466b0: cmp             w2, NULL
    // 0x9466b4: b.eq            #0x9466d4
    // 0x9466b8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9466b8: ldur            w4, [x2, #0x17]
    // 0x9466bc: DecompressPointer r4
    //     0x9466bc: add             x4, x4, HEAP, lsl #32
    // 0x9466c0: r8 = X0
    //     0x9466c0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x9466c4: LoadField: r9 = r4->field_7
    //     0x9466c4: ldur            x9, [x4, #7]
    // 0x9466c8: r3 = Null
    //     0x9466c8: add             x3, PP, #0x54, lsl #12  ; [pp+0x549f0] Null
    //     0x9466cc: ldr             x3, [x3, #0x9f0]
    // 0x9466d0: blr             x9
    // 0x9466d4: ldur            x0, [fp, #-0x10]
    // 0x9466d8: LoadField: r1 = r0->field_b
    //     0x9466d8: ldur            w1, [x0, #0xb]
    // 0x9466dc: LoadField: r2 = r0->field_f
    //     0x9466dc: ldur            w2, [x0, #0xf]
    // 0x9466e0: DecompressPointer r2
    //     0x9466e0: add             x2, x2, HEAP, lsl #32
    // 0x9466e4: LoadField: r3 = r2->field_b
    //     0x9466e4: ldur            w3, [x2, #0xb]
    // 0x9466e8: r2 = LoadInt32Instr(r1)
    //     0x9466e8: sbfx            x2, x1, #1, #0x1f
    // 0x9466ec: stur            x2, [fp, #-0x18]
    // 0x9466f0: r1 = LoadInt32Instr(r3)
    //     0x9466f0: sbfx            x1, x3, #1, #0x1f
    // 0x9466f4: cmp             x2, x1
    // 0x9466f8: b.ne            #0x946704
    // 0x9466fc: mov             x1, x0
    // 0x946700: r0 = _growToNextCapacity()
    //     0x946700: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x946704: ldur            x2, [fp, #-0x10]
    // 0x946708: ldur            x3, [fp, #-0x18]
    // 0x94670c: add             x4, x3, #1
    // 0x946710: lsl             x5, x4, #1
    // 0x946714: StoreField: r2->field_b = r5
    //     0x946714: stur            w5, [x2, #0xb]
    // 0x946718: LoadField: r1 = r2->field_f
    //     0x946718: ldur            w1, [x2, #0xf]
    // 0x94671c: DecompressPointer r1
    //     0x94671c: add             x1, x1, HEAP, lsl #32
    // 0x946720: ldur            x0, [fp, #-8]
    // 0x946724: ArrayStore: r1[r3] = r0  ; List_4
    //     0x946724: add             x25, x1, x3, lsl #2
    //     0x946728: add             x25, x25, #0xf
    //     0x94672c: str             w0, [x25]
    //     0x946730: tbz             w0, #0, #0x94674c
    //     0x946734: ldurb           w16, [x1, #-1]
    //     0x946738: ldurb           w17, [x0, #-1]
    //     0x94673c: and             x16, x17, x16, lsr #2
    //     0x946740: tst             x16, HEAP, lsr #32
    //     0x946744: b.eq            #0x94674c
    //     0x946748: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94674c: r0 = Null
    //     0x94674c: mov             x0, NULL
    // 0x946750: LeaveFrame
    //     0x946750: mov             SP, fp
    //     0x946754: ldp             fp, lr, [SP], #0x10
    // 0x946758: ret
    //     0x946758: ret             
    // 0x94675c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94675c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x946760: b               #0x94600c
    // 0x946764: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x946764: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x946768: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x946768: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94676c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94676c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x946770: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x946770: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x946774, size: 0x340
    // 0x946774: EnterFrame
    //     0x946774: stp             fp, lr, [SP, #-0x10]!
    //     0x946778: mov             fp, SP
    // 0x94677c: AllocStack(0x58)
    //     0x94677c: sub             SP, SP, #0x58
    // 0x946780: SetupParameters()
    //     0x946780: ldr             x0, [fp, #0x18]
    //     0x946784: ldur            w3, [x0, #0x17]
    //     0x946788: add             x3, x3, HEAP, lsl #32
    //     0x94678c: stur            x3, [fp, #-0x20]
    // 0x946790: CheckStackOverflow
    //     0x946790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x946794: cmp             SP, x16
    //     0x946798: b.ls            #0x946a90
    // 0x94679c: LoadField: r0 = r3->field_f
    //     0x94679c: ldur            w0, [x3, #0xf]
    // 0x9467a0: DecompressPointer r0
    //     0x9467a0: add             x0, x0, HEAP, lsl #32
    // 0x9467a4: LoadField: r4 = r0->field_b
    //     0x9467a4: ldur            w4, [x0, #0xb]
    // 0x9467a8: DecompressPointer r4
    //     0x9467a8: add             x4, x4, HEAP, lsl #32
    // 0x9467ac: stur            x4, [fp, #-0x18]
    // 0x9467b0: cmp             w4, NULL
    // 0x9467b4: b.eq            #0x946a98
    // 0x9467b8: LoadField: r0 = r4->field_1f
    //     0x9467b8: ldur            w0, [x4, #0x1f]
    // 0x9467bc: DecompressPointer r0
    //     0x9467bc: add             x0, x0, HEAP, lsl #32
    // 0x9467c0: stur            x0, [fp, #-0x10]
    // 0x9467c4: LoadField: r1 = r0->field_b
    //     0x9467c4: ldur            w1, [x0, #0xb]
    // 0x9467c8: DecompressPointer r1
    //     0x9467c8: add             x1, x1, HEAP, lsl #32
    // 0x9467cc: cmp             w1, NULL
    // 0x9467d0: b.ne            #0x9467dc
    // 0x9467d4: r5 = ""
    //     0x9467d4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9467d8: b               #0x9467e0
    // 0x9467dc: mov             x5, x1
    // 0x9467e0: stur            x5, [fp, #-8]
    // 0x9467e4: r1 = Null
    //     0x9467e4: mov             x1, NULL
    // 0x9467e8: r2 = 6
    //     0x9467e8: movz            x2, #0x6
    // 0x9467ec: r0 = AllocateArray()
    //     0x9467ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9467f0: mov             x1, x0
    // 0x9467f4: ldur            x0, [fp, #-8]
    // 0x9467f8: StoreField: r1->field_f = r0
    //     0x9467f8: stur            w0, [x1, #0xf]
    // 0x9467fc: r16 = " "
    //     0x9467fc: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x946800: StoreField: r1->field_13 = r16
    //     0x946800: stur            w16, [x1, #0x13]
    // 0x946804: ldur            x0, [fp, #-0x10]
    // 0x946808: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x946808: ldur            w2, [x0, #0x17]
    // 0x94680c: DecompressPointer r2
    //     0x94680c: add             x2, x2, HEAP, lsl #32
    // 0x946810: cmp             w2, NULL
    // 0x946814: b.ne            #0x94681c
    // 0x946818: r2 = ""
    //     0x946818: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94681c: ldur            x0, [fp, #-0x20]
    // 0x946820: ArrayStore: r1[0] = r2  ; List_4
    //     0x946820: stur            w2, [x1, #0x17]
    // 0x946824: str             x1, [SP]
    // 0x946828: r0 = _interpolate()
    //     0x946828: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x94682c: mov             x2, x0
    // 0x946830: ldur            x0, [fp, #-0x20]
    // 0x946834: LoadField: r1 = r0->field_f
    //     0x946834: ldur            w1, [x0, #0xf]
    // 0x946838: DecompressPointer r1
    //     0x946838: add             x1, x1, HEAP, lsl #32
    // 0x94683c: LoadField: r0 = r1->field_b
    //     0x94683c: ldur            w0, [x1, #0xb]
    // 0x946840: DecompressPointer r0
    //     0x946840: add             x0, x0, HEAP, lsl #32
    // 0x946844: cmp             w0, NULL
    // 0x946848: b.eq            #0x946a9c
    // 0x94684c: LoadField: r1 = r0->field_1f
    //     0x94684c: ldur            w1, [x0, #0x1f]
    // 0x946850: DecompressPointer r1
    //     0x946850: add             x1, x1, HEAP, lsl #32
    // 0x946854: LoadField: r3 = r1->field_1b
    //     0x946854: ldur            w3, [x1, #0x1b]
    // 0x946858: DecompressPointer r3
    //     0x946858: add             x3, x3, HEAP, lsl #32
    // 0x94685c: cmp             w3, NULL
    // 0x946860: b.ne            #0x94686c
    // 0x946864: r0 = Null
    //     0x946864: mov             x0, NULL
    // 0x946868: b               #0x9468ac
    // 0x94686c: LoadField: r0 = r3->field_b
    //     0x94686c: ldur            w0, [x3, #0xb]
    // 0x946870: r1 = LoadInt32Instr(r0)
    //     0x946870: sbfx            x1, x0, #1, #0x1f
    // 0x946874: mov             x0, x1
    // 0x946878: r1 = 0
    //     0x946878: movz            x1, #0
    // 0x94687c: cmp             x1, x0
    // 0x946880: b.hs            #0x946aa0
    // 0x946884: LoadField: r0 = r3->field_f
    //     0x946884: ldur            w0, [x3, #0xf]
    // 0x946888: DecompressPointer r0
    //     0x946888: add             x0, x0, HEAP, lsl #32
    // 0x94688c: LoadField: r1 = r0->field_f
    //     0x94688c: ldur            w1, [x0, #0xf]
    // 0x946890: DecompressPointer r1
    //     0x946890: add             x1, x1, HEAP, lsl #32
    // 0x946894: cmp             w1, NULL
    // 0x946898: b.ne            #0x9468a4
    // 0x94689c: r0 = Null
    //     0x94689c: mov             x0, NULL
    // 0x9468a0: b               #0x9468ac
    // 0x9468a4: LoadField: r0 = r1->field_1b
    //     0x9468a4: ldur            w0, [x1, #0x1b]
    // 0x9468a8: DecompressPointer r0
    //     0x9468a8: add             x0, x0, HEAP, lsl #32
    // 0x9468ac: cmp             w0, NULL
    // 0x9468b0: b.ne            #0x9468bc
    // 0x9468b4: r4 = ""
    //     0x9468b4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9468b8: b               #0x9468c0
    // 0x9468bc: mov             x4, x0
    // 0x9468c0: cmp             w3, NULL
    // 0x9468c4: b.ne            #0x9468d0
    // 0x9468c8: r0 = Null
    //     0x9468c8: mov             x0, NULL
    // 0x9468cc: b               #0x946910
    // 0x9468d0: LoadField: r0 = r3->field_b
    //     0x9468d0: ldur            w0, [x3, #0xb]
    // 0x9468d4: r1 = LoadInt32Instr(r0)
    //     0x9468d4: sbfx            x1, x0, #1, #0x1f
    // 0x9468d8: mov             x0, x1
    // 0x9468dc: r1 = 0
    //     0x9468dc: movz            x1, #0
    // 0x9468e0: cmp             x1, x0
    // 0x9468e4: b.hs            #0x946aa4
    // 0x9468e8: LoadField: r0 = r3->field_f
    //     0x9468e8: ldur            w0, [x3, #0xf]
    // 0x9468ec: DecompressPointer r0
    //     0x9468ec: add             x0, x0, HEAP, lsl #32
    // 0x9468f0: LoadField: r1 = r0->field_f
    //     0x9468f0: ldur            w1, [x0, #0xf]
    // 0x9468f4: DecompressPointer r1
    //     0x9468f4: add             x1, x1, HEAP, lsl #32
    // 0x9468f8: cmp             w1, NULL
    // 0x9468fc: b.ne            #0x946908
    // 0x946900: r0 = Null
    //     0x946900: mov             x0, NULL
    // 0x946904: b               #0x946910
    // 0x946908: LoadField: r0 = r1->field_2b
    //     0x946908: ldur            w0, [x1, #0x2b]
    // 0x94690c: DecompressPointer r0
    //     0x94690c: add             x0, x0, HEAP, lsl #32
    // 0x946910: cmp             w0, NULL
    // 0x946914: b.ne            #0x946920
    // 0x946918: r5 = ""
    //     0x946918: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94691c: b               #0x946924
    // 0x946920: mov             x5, x0
    // 0x946924: cmp             w3, NULL
    // 0x946928: b.ne            #0x946934
    // 0x94692c: r0 = Null
    //     0x94692c: mov             x0, NULL
    // 0x946930: b               #0x946974
    // 0x946934: LoadField: r0 = r3->field_b
    //     0x946934: ldur            w0, [x3, #0xb]
    // 0x946938: r1 = LoadInt32Instr(r0)
    //     0x946938: sbfx            x1, x0, #1, #0x1f
    // 0x94693c: mov             x0, x1
    // 0x946940: r1 = 0
    //     0x946940: movz            x1, #0
    // 0x946944: cmp             x1, x0
    // 0x946948: b.hs            #0x946aa8
    // 0x94694c: LoadField: r0 = r3->field_f
    //     0x94694c: ldur            w0, [x3, #0xf]
    // 0x946950: DecompressPointer r0
    //     0x946950: add             x0, x0, HEAP, lsl #32
    // 0x946954: LoadField: r1 = r0->field_f
    //     0x946954: ldur            w1, [x0, #0xf]
    // 0x946958: DecompressPointer r1
    //     0x946958: add             x1, x1, HEAP, lsl #32
    // 0x94695c: cmp             w1, NULL
    // 0x946960: b.ne            #0x94696c
    // 0x946964: r0 = Null
    //     0x946964: mov             x0, NULL
    // 0x946968: b               #0x946974
    // 0x94696c: LoadField: r0 = r1->field_13
    //     0x94696c: ldur            w0, [x1, #0x13]
    // 0x946970: DecompressPointer r0
    //     0x946970: add             x0, x0, HEAP, lsl #32
    // 0x946974: cmp             w0, NULL
    // 0x946978: b.ne            #0x946984
    // 0x94697c: r6 = ""
    //     0x94697c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946980: b               #0x946988
    // 0x946984: mov             x6, x0
    // 0x946988: cmp             w3, NULL
    // 0x94698c: b.ne            #0x946998
    // 0x946990: r0 = Null
    //     0x946990: mov             x0, NULL
    // 0x946994: b               #0x9469d8
    // 0x946998: LoadField: r0 = r3->field_b
    //     0x946998: ldur            w0, [x3, #0xb]
    // 0x94699c: r1 = LoadInt32Instr(r0)
    //     0x94699c: sbfx            x1, x0, #1, #0x1f
    // 0x9469a0: mov             x0, x1
    // 0x9469a4: r1 = 0
    //     0x9469a4: movz            x1, #0
    // 0x9469a8: cmp             x1, x0
    // 0x9469ac: b.hs            #0x946aac
    // 0x9469b0: LoadField: r0 = r3->field_f
    //     0x9469b0: ldur            w0, [x3, #0xf]
    // 0x9469b4: DecompressPointer r0
    //     0x9469b4: add             x0, x0, HEAP, lsl #32
    // 0x9469b8: LoadField: r1 = r0->field_f
    //     0x9469b8: ldur            w1, [x0, #0xf]
    // 0x9469bc: DecompressPointer r1
    //     0x9469bc: add             x1, x1, HEAP, lsl #32
    // 0x9469c0: cmp             w1, NULL
    // 0x9469c4: b.ne            #0x9469d0
    // 0x9469c8: r0 = Null
    //     0x9469c8: mov             x0, NULL
    // 0x9469cc: b               #0x9469d8
    // 0x9469d0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9469d0: ldur            w0, [x1, #0x17]
    // 0x9469d4: DecompressPointer r0
    //     0x9469d4: add             x0, x0, HEAP, lsl #32
    // 0x9469d8: cmp             w0, NULL
    // 0x9469dc: b.ne            #0x9469e8
    // 0x9469e0: r7 = ""
    //     0x9469e0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9469e4: b               #0x9469ec
    // 0x9469e8: mov             x7, x0
    // 0x9469ec: cmp             w3, NULL
    // 0x9469f0: b.ne            #0x9469fc
    // 0x9469f4: r0 = Null
    //     0x9469f4: mov             x0, NULL
    // 0x9469f8: b               #0x946a3c
    // 0x9469fc: LoadField: r0 = r3->field_b
    //     0x9469fc: ldur            w0, [x3, #0xb]
    // 0x946a00: r1 = LoadInt32Instr(r0)
    //     0x946a00: sbfx            x1, x0, #1, #0x1f
    // 0x946a04: mov             x0, x1
    // 0x946a08: r1 = 0
    //     0x946a08: movz            x1, #0
    // 0x946a0c: cmp             x1, x0
    // 0x946a10: b.hs            #0x946ab0
    // 0x946a14: LoadField: r0 = r3->field_f
    //     0x946a14: ldur            w0, [x3, #0xf]
    // 0x946a18: DecompressPointer r0
    //     0x946a18: add             x0, x0, HEAP, lsl #32
    // 0x946a1c: LoadField: r1 = r0->field_f
    //     0x946a1c: ldur            w1, [x0, #0xf]
    // 0x946a20: DecompressPointer r1
    //     0x946a20: add             x1, x1, HEAP, lsl #32
    // 0x946a24: cmp             w1, NULL
    // 0x946a28: b.ne            #0x946a34
    // 0x946a2c: r0 = Null
    //     0x946a2c: mov             x0, NULL
    // 0x946a30: b               #0x946a3c
    // 0x946a34: LoadField: r0 = r1->field_2f
    //     0x946a34: ldur            w0, [x1, #0x2f]
    // 0x946a38: DecompressPointer r0
    //     0x946a38: add             x0, x0, HEAP, lsl #32
    // 0x946a3c: cmp             w0, NULL
    // 0x946a40: b.ne            #0x946a4c
    // 0x946a44: r1 = ""
    //     0x946a44: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946a48: b               #0x946a50
    // 0x946a4c: mov             x1, x0
    // 0x946a50: ldur            x0, [fp, #-0x18]
    // 0x946a54: LoadField: r3 = r0->field_f
    //     0x946a54: ldur            w3, [x0, #0xf]
    // 0x946a58: DecompressPointer r3
    //     0x946a58: add             x3, x3, HEAP, lsl #32
    // 0x946a5c: stp             x2, x3, [SP, #0x28]
    // 0x946a60: stp             x5, x4, [SP, #0x18]
    // 0x946a64: stp             x7, x6, [SP, #8]
    // 0x946a68: str             x1, [SP]
    // 0x946a6c: r4 = 0
    //     0x946a6c: movz            x4, #0
    // 0x946a70: ldr             x0, [SP, #0x30]
    // 0x946a74: r16 = UnlinkedCall_0x613b5c
    //     0x946a74: add             x16, PP, #0x54, lsl #12  ; [pp+0x54a00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x946a78: add             x16, x16, #0xa00
    // 0x946a7c: ldp             x5, lr, [x16]
    // 0x946a80: blr             lr
    // 0x946a84: LeaveFrame
    //     0x946a84: mov             SP, fp
    //     0x946a88: ldp             fp, lr, [SP], #0x10
    // 0x946a8c: ret
    //     0x946a8c: ret             
    // 0x946a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x946a90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x946a94: b               #0x94679c
    // 0x946a98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x946a98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x946a9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x946a9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x946aa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x946aa0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x946aa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x946aa4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x946aa8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x946aa8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x946aac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x946aac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x946ab0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x946ab0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x946ab4, size: 0x9c
    // 0x946ab4: EnterFrame
    //     0x946ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x946ab8: mov             fp, SP
    // 0x946abc: AllocStack(0x38)
    //     0x946abc: sub             SP, SP, #0x38
    // 0x946ac0: SetupParameters()
    //     0x946ac0: ldr             x0, [fp, #0x18]
    //     0x946ac4: ldur            w1, [x0, #0x17]
    //     0x946ac8: add             x1, x1, HEAP, lsl #32
    // 0x946acc: CheckStackOverflow
    //     0x946acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x946ad0: cmp             SP, x16
    //     0x946ad4: b.ls            #0x946b44
    // 0x946ad8: LoadField: r0 = r1->field_f
    //     0x946ad8: ldur            w0, [x1, #0xf]
    // 0x946adc: DecompressPointer r0
    //     0x946adc: add             x0, x0, HEAP, lsl #32
    // 0x946ae0: LoadField: r1 = r0->field_b
    //     0x946ae0: ldur            w1, [x0, #0xb]
    // 0x946ae4: DecompressPointer r1
    //     0x946ae4: add             x1, x1, HEAP, lsl #32
    // 0x946ae8: cmp             w1, NULL
    // 0x946aec: b.eq            #0x946b4c
    // 0x946af0: LoadField: r0 = r1->field_f
    //     0x946af0: ldur            w0, [x1, #0xf]
    // 0x946af4: DecompressPointer r0
    //     0x946af4: add             x0, x0, HEAP, lsl #32
    // 0x946af8: r16 = ""
    //     0x946af8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946afc: stp             x16, x0, [SP, #0x28]
    // 0x946b00: r16 = ""
    //     0x946b00: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946b04: r30 = ""
    //     0x946b04: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946b08: stp             lr, x16, [SP, #0x18]
    // 0x946b0c: r16 = ""
    //     0x946b0c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946b10: r30 = ""
    //     0x946b10: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946b14: stp             lr, x16, [SP, #8]
    // 0x946b18: r16 = ""
    //     0x946b18: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x946b1c: str             x16, [SP]
    // 0x946b20: r4 = 0
    //     0x946b20: movz            x4, #0
    // 0x946b24: ldr             x0, [SP, #0x30]
    // 0x946b28: r16 = UnlinkedCall_0x613b5c
    //     0x946b28: add             x16, PP, #0x54, lsl #12  ; [pp+0x549c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x946b2c: add             x16, x16, #0x9c0
    // 0x946b30: ldp             x5, lr, [x16]
    // 0x946b34: blr             lr
    // 0x946b38: LeaveFrame
    //     0x946b38: mov             SP, fp
    //     0x946b3c: ldp             fp, lr, [SP], #0x10
    // 0x946b40: ret
    //     0x946b40: ret             
    // 0x946b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x946b44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x946b48: b               #0x946ad8
    // 0x946b4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x946b4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa015e0, size: 0x2a8
    // 0xa015e0: EnterFrame
    //     0xa015e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa015e4: mov             fp, SP
    // 0xa015e8: AllocStack(0x18)
    //     0xa015e8: sub             SP, SP, #0x18
    // 0xa015ec: SetupParameters()
    //     0xa015ec: ldr             x0, [fp, #0x10]
    //     0xa015f0: ldur            w3, [x0, #0x17]
    //     0xa015f4: add             x3, x3, HEAP, lsl #32
    //     0xa015f8: stur            x3, [fp, #-0x10]
    // 0xa015fc: CheckStackOverflow
    //     0xa015fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa01600: cmp             SP, x16
    //     0xa01604: b.ls            #0xa0187c
    // 0xa01608: LoadField: r0 = r3->field_f
    //     0xa01608: ldur            w0, [x3, #0xf]
    // 0xa0160c: DecompressPointer r0
    //     0xa0160c: add             x0, x0, HEAP, lsl #32
    // 0xa01610: stur            x0, [fp, #-8]
    // 0xa01614: LoadField: r1 = r0->field_33
    //     0xa01614: ldur            w1, [x0, #0x33]
    // 0xa01618: DecompressPointer r1
    //     0xa01618: add             x1, x1, HEAP, lsl #32
    // 0xa0161c: LoadField: r2 = r1->field_27
    //     0xa0161c: ldur            w2, [x1, #0x27]
    // 0xa01620: DecompressPointer r2
    //     0xa01620: add             x2, x2, HEAP, lsl #32
    // 0xa01624: LoadField: r1 = r2->field_7
    //     0xa01624: ldur            w1, [x2, #7]
    // 0xa01628: DecompressPointer r1
    //     0xa01628: add             x1, x1, HEAP, lsl #32
    // 0xa0162c: LoadField: r4 = r1->field_7
    //     0xa0162c: ldur            w4, [x1, #7]
    // 0xa01630: cbz             w4, #0xa01860
    // 0xa01634: LoadField: r1 = r2->field_7
    //     0xa01634: ldur            w1, [x2, #7]
    // 0xa01638: DecompressPointer r1
    //     0xa01638: add             x1, x1, HEAP, lsl #32
    // 0xa0163c: LoadField: r2 = r1->field_7
    //     0xa0163c: ldur            w2, [x1, #7]
    // 0xa01640: r1 = LoadInt32Instr(r2)
    //     0xa01640: sbfx            x1, x2, #1, #0x1f
    // 0xa01644: cmp             x1, #2
    // 0xa01648: b.lt            #0xa01860
    // 0xa0164c: LoadField: r1 = r0->field_3f
    //     0xa0164c: ldur            w1, [x0, #0x3f]
    // 0xa01650: DecompressPointer r1
    //     0xa01650: add             x1, x1, HEAP, lsl #32
    // 0xa01654: LoadField: r2 = r1->field_27
    //     0xa01654: ldur            w2, [x1, #0x27]
    // 0xa01658: DecompressPointer r2
    //     0xa01658: add             x2, x2, HEAP, lsl #32
    // 0xa0165c: LoadField: r1 = r2->field_7
    //     0xa0165c: ldur            w1, [x2, #7]
    // 0xa01660: DecompressPointer r1
    //     0xa01660: add             x1, x1, HEAP, lsl #32
    // 0xa01664: LoadField: r4 = r1->field_7
    //     0xa01664: ldur            w4, [x1, #7]
    // 0xa01668: cbz             w4, #0xa01860
    // 0xa0166c: LoadField: r1 = r0->field_3b
    //     0xa0166c: ldur            w1, [x0, #0x3b]
    // 0xa01670: DecompressPointer r1
    //     0xa01670: add             x1, x1, HEAP, lsl #32
    // 0xa01674: LoadField: r4 = r1->field_27
    //     0xa01674: ldur            w4, [x1, #0x27]
    // 0xa01678: DecompressPointer r4
    //     0xa01678: add             x4, x4, HEAP, lsl #32
    // 0xa0167c: LoadField: r1 = r4->field_7
    //     0xa0167c: ldur            w1, [x4, #7]
    // 0xa01680: DecompressPointer r1
    //     0xa01680: add             x1, x1, HEAP, lsl #32
    // 0xa01684: LoadField: r4 = r1->field_7
    //     0xa01684: ldur            w4, [x1, #7]
    // 0xa01688: cbz             w4, #0xa01860
    // 0xa0168c: LoadField: r1 = r0->field_47
    //     0xa0168c: ldur            w1, [x0, #0x47]
    // 0xa01690: DecompressPointer r1
    //     0xa01690: add             x1, x1, HEAP, lsl #32
    // 0xa01694: LoadField: r4 = r1->field_27
    //     0xa01694: ldur            w4, [x1, #0x27]
    // 0xa01698: DecompressPointer r4
    //     0xa01698: add             x4, x4, HEAP, lsl #32
    // 0xa0169c: LoadField: r1 = r4->field_7
    //     0xa0169c: ldur            w1, [x4, #7]
    // 0xa016a0: DecompressPointer r1
    //     0xa016a0: add             x1, x1, HEAP, lsl #32
    // 0xa016a4: LoadField: r4 = r1->field_7
    //     0xa016a4: ldur            w4, [x1, #7]
    // 0xa016a8: cbz             w4, #0xa01860
    // 0xa016ac: LoadField: r1 = r2->field_7
    //     0xa016ac: ldur            w1, [x2, #7]
    // 0xa016b0: DecompressPointer r1
    //     0xa016b0: add             x1, x1, HEAP, lsl #32
    // 0xa016b4: LoadField: r2 = r1->field_7
    //     0xa016b4: ldur            w2, [x1, #7]
    // 0xa016b8: cmp             w2, #0xc
    // 0xa016bc: b.ne            #0xa01860
    // 0xa016c0: LoadField: r1 = r0->field_b
    //     0xa016c0: ldur            w1, [x0, #0xb]
    // 0xa016c4: DecompressPointer r1
    //     0xa016c4: add             x1, x1, HEAP, lsl #32
    // 0xa016c8: cmp             w1, NULL
    // 0xa016cc: b.eq            #0xa01884
    // 0xa016d0: LoadField: r2 = r1->field_13
    //     0xa016d0: ldur            w2, [x1, #0x13]
    // 0xa016d4: DecompressPointer r2
    //     0xa016d4: add             x2, x2, HEAP, lsl #32
    // 0xa016d8: LoadField: r1 = r2->field_b
    //     0xa016d8: ldur            w1, [x2, #0xb]
    // 0xa016dc: DecompressPointer r1
    //     0xa016dc: add             x1, x1, HEAP, lsl #32
    // 0xa016e0: cmp             w1, NULL
    // 0xa016e4: b.ne            #0xa016f0
    // 0xa016e8: r1 = Null
    //     0xa016e8: mov             x1, NULL
    // 0xa016ec: b               #0xa01720
    // 0xa016f0: LoadField: r2 = r1->field_13
    //     0xa016f0: ldur            w2, [x1, #0x13]
    // 0xa016f4: DecompressPointer r2
    //     0xa016f4: add             x2, x2, HEAP, lsl #32
    // 0xa016f8: cmp             w2, NULL
    // 0xa016fc: b.ne            #0xa01708
    // 0xa01700: r1 = Null
    //     0xa01700: mov             x1, NULL
    // 0xa01704: b               #0xa01720
    // 0xa01708: LoadField: r1 = r2->field_7
    //     0xa01708: ldur            w1, [x2, #7]
    // 0xa0170c: cbnz            w1, #0xa01718
    // 0xa01710: r2 = false
    //     0xa01710: add             x2, NULL, #0x30  ; false
    // 0xa01714: b               #0xa0171c
    // 0xa01718: r2 = true
    //     0xa01718: add             x2, NULL, #0x20  ; true
    // 0xa0171c: mov             x1, x2
    // 0xa01720: cmp             w1, NULL
    // 0xa01724: b.eq            #0xa01860
    // 0xa01728: tbnz            w1, #4, #0xa01860
    // 0xa0172c: LoadField: r1 = r0->field_37
    //     0xa0172c: ldur            w1, [x0, #0x37]
    // 0xa01730: DecompressPointer r1
    //     0xa01730: add             x1, x1, HEAP, lsl #32
    // 0xa01734: LoadField: r2 = r1->field_27
    //     0xa01734: ldur            w2, [x1, #0x27]
    // 0xa01738: DecompressPointer r2
    //     0xa01738: add             x2, x2, HEAP, lsl #32
    // 0xa0173c: LoadField: r1 = r2->field_7
    //     0xa0173c: ldur            w1, [x2, #7]
    // 0xa01740: DecompressPointer r1
    //     0xa01740: add             x1, x1, HEAP, lsl #32
    // 0xa01744: LoadField: r4 = r1->field_7
    //     0xa01744: ldur            w4, [x1, #7]
    // 0xa01748: r1 = LoadInt32Instr(r4)
    //     0xa01748: sbfx            x1, x4, #1, #0x1f
    // 0xa0174c: cmp             x1, #5
    // 0xa01750: b.ge            #0xa01764
    // 0xa01754: LoadField: r1 = r2->field_7
    //     0xa01754: ldur            w1, [x2, #7]
    // 0xa01758: DecompressPointer r1
    //     0xa01758: add             x1, x1, HEAP, lsl #32
    // 0xa0175c: LoadField: r2 = r1->field_7
    //     0xa0175c: ldur            w2, [x1, #7]
    // 0xa01760: cbnz            w2, #0xa01860
    // 0xa01764: LoadField: r1 = r0->field_43
    //     0xa01764: ldur            w1, [x0, #0x43]
    // 0xa01768: DecompressPointer r1
    //     0xa01768: add             x1, x1, HEAP, lsl #32
    // 0xa0176c: LoadField: r2 = r1->field_27
    //     0xa0176c: ldur            w2, [x1, #0x27]
    // 0xa01770: DecompressPointer r2
    //     0xa01770: add             x2, x2, HEAP, lsl #32
    // 0xa01774: LoadField: r1 = r2->field_7
    //     0xa01774: ldur            w1, [x2, #7]
    // 0xa01778: DecompressPointer r1
    //     0xa01778: add             x1, x1, HEAP, lsl #32
    // 0xa0177c: LoadField: r4 = r1->field_7
    //     0xa0177c: ldur            w4, [x1, #7]
    // 0xa01780: cbz             w4, #0xa017f8
    // 0xa01784: LoadField: r1 = r2->field_7
    //     0xa01784: ldur            w1, [x2, #7]
    // 0xa01788: DecompressPointer r1
    //     0xa01788: add             x1, x1, HEAP, lsl #32
    // 0xa0178c: r16 = 2
    //     0xa0178c: movz            x16, #0x2
    // 0xa01790: str             x16, [SP]
    // 0xa01794: r2 = 0
    //     0xa01794: movz            x2, #0
    // 0xa01798: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xa01798: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xa0179c: r0 = substring()
    //     0xa0179c: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xa017a0: mov             x1, x0
    // 0xa017a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa017a4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa017a8: r0 = parse()
    //     0xa017a8: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xa017ac: cmp             x0, #6
    // 0xa017b0: b.lt            #0xa017f0
    // 0xa017b4: ldur            x1, [fp, #-0x10]
    // 0xa017b8: LoadField: r2 = r1->field_f
    //     0xa017b8: ldur            w2, [x1, #0xf]
    // 0xa017bc: DecompressPointer r2
    //     0xa017bc: add             x2, x2, HEAP, lsl #32
    // 0xa017c0: LoadField: r3 = r2->field_43
    //     0xa017c0: ldur            w3, [x2, #0x43]
    // 0xa017c4: DecompressPointer r3
    //     0xa017c4: add             x3, x3, HEAP, lsl #32
    // 0xa017c8: LoadField: r4 = r3->field_27
    //     0xa017c8: ldur            w4, [x3, #0x27]
    // 0xa017cc: DecompressPointer r4
    //     0xa017cc: add             x4, x4, HEAP, lsl #32
    // 0xa017d0: LoadField: r3 = r4->field_7
    //     0xa017d0: ldur            w3, [x4, #7]
    // 0xa017d4: DecompressPointer r3
    //     0xa017d4: add             x3, x3, HEAP, lsl #32
    // 0xa017d8: LoadField: r4 = r3->field_7
    //     0xa017d8: ldur            w4, [x3, #7]
    // 0xa017dc: r3 = LoadInt32Instr(r4)
    //     0xa017dc: sbfx            x3, x4, #1, #0x1f
    // 0xa017e0: cmp             x3, #0xa
    // 0xa017e4: b.lt            #0xa017fc
    // 0xa017e8: mov             x1, x2
    // 0xa017ec: b               #0xa01828
    // 0xa017f0: ldur            x1, [fp, #-0x10]
    // 0xa017f4: b               #0xa017fc
    // 0xa017f8: mov             x1, x3
    // 0xa017fc: LoadField: r2 = r1->field_f
    //     0xa017fc: ldur            w2, [x1, #0xf]
    // 0xa01800: DecompressPointer r2
    //     0xa01800: add             x2, x2, HEAP, lsl #32
    // 0xa01804: LoadField: r1 = r2->field_43
    //     0xa01804: ldur            w1, [x2, #0x43]
    // 0xa01808: DecompressPointer r1
    //     0xa01808: add             x1, x1, HEAP, lsl #32
    // 0xa0180c: LoadField: r3 = r1->field_27
    //     0xa0180c: ldur            w3, [x1, #0x27]
    // 0xa01810: DecompressPointer r3
    //     0xa01810: add             x3, x3, HEAP, lsl #32
    // 0xa01814: LoadField: r1 = r3->field_7
    //     0xa01814: ldur            w1, [x3, #7]
    // 0xa01818: DecompressPointer r1
    //     0xa01818: add             x1, x1, HEAP, lsl #32
    // 0xa0181c: LoadField: r3 = r1->field_7
    //     0xa0181c: ldur            w3, [x1, #7]
    // 0xa01820: cbnz            w3, #0xa01860
    // 0xa01824: mov             x1, x2
    // 0xa01828: LoadField: r2 = r1->field_47
    //     0xa01828: ldur            w2, [x1, #0x47]
    // 0xa0182c: DecompressPointer r2
    //     0xa0182c: add             x2, x2, HEAP, lsl #32
    // 0xa01830: LoadField: r1 = r2->field_27
    //     0xa01830: ldur            w1, [x2, #0x27]
    // 0xa01834: DecompressPointer r1
    //     0xa01834: add             x1, x1, HEAP, lsl #32
    // 0xa01838: LoadField: r2 = r1->field_7
    //     0xa01838: ldur            w2, [x1, #7]
    // 0xa0183c: DecompressPointer r2
    //     0xa0183c: add             x2, x2, HEAP, lsl #32
    // 0xa01840: LoadField: r1 = r2->field_7
    //     0xa01840: ldur            w1, [x2, #7]
    // 0xa01844: r2 = LoadInt32Instr(r1)
    //     0xa01844: sbfx            x2, x1, #1, #0x1f
    // 0xa01848: cmp             x2, #0x14
    // 0xa0184c: r16 = true
    //     0xa0184c: add             x16, NULL, #0x20  ; true
    // 0xa01850: r17 = false
    //     0xa01850: add             x17, NULL, #0x30  ; false
    // 0xa01854: csel            x1, x16, x17, ge
    // 0xa01858: mov             x2, x1
    // 0xa0185c: b               #0xa01864
    // 0xa01860: r2 = false
    //     0xa01860: add             x2, NULL, #0x30  ; false
    // 0xa01864: ldur            x1, [fp, #-8]
    // 0xa01868: StoreField: r1->field_4b = r2
    //     0xa01868: stur            w2, [x1, #0x4b]
    // 0xa0186c: r0 = Null
    //     0xa0186c: mov             x0, NULL
    // 0xa01870: LeaveFrame
    //     0xa01870: mov             SP, fp
    //     0xa01874: ldp             fp, lr, [SP], #0x10
    // 0xa01878: ret
    //     0xa01878: ret             
    // 0xa0187c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0187c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa01880: b               #0xa01608
    // 0xa01884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa01884: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xa01888, size: 0x60
    // 0xa01888: EnterFrame
    //     0xa01888: stp             fp, lr, [SP, #-0x10]!
    //     0xa0188c: mov             fp, SP
    // 0xa01890: AllocStack(0x8)
    //     0xa01890: sub             SP, SP, #8
    // 0xa01894: SetupParameters()
    //     0xa01894: ldr             x0, [fp, #0x18]
    //     0xa01898: ldur            w2, [x0, #0x17]
    //     0xa0189c: add             x2, x2, HEAP, lsl #32
    // 0xa018a0: CheckStackOverflow
    //     0xa018a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa018a4: cmp             SP, x16
    //     0xa018a8: b.ls            #0xa018e0
    // 0xa018ac: LoadField: r0 = r2->field_f
    //     0xa018ac: ldur            w0, [x2, #0xf]
    // 0xa018b0: DecompressPointer r0
    //     0xa018b0: add             x0, x0, HEAP, lsl #32
    // 0xa018b4: stur            x0, [fp, #-8]
    // 0xa018b8: r1 = Function '<anonymous closure>':.
    //     0xa018b8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54938] AnonymousClosure: (0xa015e0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress (0xa018e8)
    //     0xa018bc: ldr             x1, [x1, #0x938]
    // 0xa018c0: r0 = AllocateClosure()
    //     0xa018c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa018c4: ldur            x1, [fp, #-8]
    // 0xa018c8: mov             x2, x0
    // 0xa018cc: r0 = setState()
    //     0xa018cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa018d0: r0 = Null
    //     0xa018d0: mov             x0, NULL
    // 0xa018d4: LeaveFrame
    //     0xa018d4: mov             SP, fp
    //     0xa018d8: ldp             fp, lr, [SP], #0x10
    // 0xa018dc: ret
    //     0xa018dc: ret             
    // 0xa018e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa018e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa018e4: b               #0xa018ac
  }
  _ validateAddress(/* No info */) {
    // ** addr: 0xa018e8, size: 0x130
    // 0xa018e8: EnterFrame
    //     0xa018e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa018ec: mov             fp, SP
    // 0xa018f0: AllocStack(0x18)
    //     0xa018f0: sub             SP, SP, #0x18
    // 0xa018f4: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xa018f4: stur            x1, [fp, #-8]
    // 0xa018f8: CheckStackOverflow
    //     0xa018f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa018fc: cmp             SP, x16
    //     0xa01900: b.ls            #0xa01a0c
    // 0xa01904: r1 = 1
    //     0xa01904: movz            x1, #0x1
    // 0xa01908: r0 = AllocateContext()
    //     0xa01908: bl              #0x16f6108  ; AllocateContextStub
    // 0xa0190c: mov             x1, x0
    // 0xa01910: ldur            x0, [fp, #-8]
    // 0xa01914: StoreField: r1->field_f = r0
    //     0xa01914: stur            w0, [x1, #0xf]
    // 0xa01918: r0 = LoadStaticField(0x878)
    //     0xa01918: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa0191c: ldr             x0, [x0, #0x10f0]
    // 0xa01920: cmp             w0, NULL
    // 0xa01924: b.eq            #0xa01a14
    // 0xa01928: LoadField: r3 = r0->field_53
    //     0xa01928: ldur            w3, [x0, #0x53]
    // 0xa0192c: DecompressPointer r3
    //     0xa0192c: add             x3, x3, HEAP, lsl #32
    // 0xa01930: stur            x3, [fp, #-0x10]
    // 0xa01934: LoadField: r0 = r3->field_7
    //     0xa01934: ldur            w0, [x3, #7]
    // 0xa01938: DecompressPointer r0
    //     0xa01938: add             x0, x0, HEAP, lsl #32
    // 0xa0193c: mov             x2, x1
    // 0xa01940: stur            x0, [fp, #-8]
    // 0xa01944: r1 = Function '<anonymous closure>':.
    //     0xa01944: add             x1, PP, #0x54, lsl #12  ; [pp+0x54920] AnonymousClosure: (0xa01888), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress (0xa018e8)
    //     0xa01948: ldr             x1, [x1, #0x920]
    // 0xa0194c: r0 = AllocateClosure()
    //     0xa0194c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa01950: ldur            x2, [fp, #-8]
    // 0xa01954: mov             x3, x0
    // 0xa01958: r1 = Null
    //     0xa01958: mov             x1, NULL
    // 0xa0195c: stur            x3, [fp, #-8]
    // 0xa01960: cmp             w2, NULL
    // 0xa01964: b.eq            #0xa01984
    // 0xa01968: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa01968: ldur            w4, [x2, #0x17]
    // 0xa0196c: DecompressPointer r4
    //     0xa0196c: add             x4, x4, HEAP, lsl #32
    // 0xa01970: r8 = X0
    //     0xa01970: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xa01974: LoadField: r9 = r4->field_7
    //     0xa01974: ldur            x9, [x4, #7]
    // 0xa01978: r3 = Null
    //     0xa01978: add             x3, PP, #0x54, lsl #12  ; [pp+0x54928] Null
    //     0xa0197c: ldr             x3, [x3, #0x928]
    // 0xa01980: blr             x9
    // 0xa01984: ldur            x0, [fp, #-0x10]
    // 0xa01988: LoadField: r1 = r0->field_b
    //     0xa01988: ldur            w1, [x0, #0xb]
    // 0xa0198c: LoadField: r2 = r0->field_f
    //     0xa0198c: ldur            w2, [x0, #0xf]
    // 0xa01990: DecompressPointer r2
    //     0xa01990: add             x2, x2, HEAP, lsl #32
    // 0xa01994: LoadField: r3 = r2->field_b
    //     0xa01994: ldur            w3, [x2, #0xb]
    // 0xa01998: r2 = LoadInt32Instr(r1)
    //     0xa01998: sbfx            x2, x1, #1, #0x1f
    // 0xa0199c: stur            x2, [fp, #-0x18]
    // 0xa019a0: r1 = LoadInt32Instr(r3)
    //     0xa019a0: sbfx            x1, x3, #1, #0x1f
    // 0xa019a4: cmp             x2, x1
    // 0xa019a8: b.ne            #0xa019b4
    // 0xa019ac: mov             x1, x0
    // 0xa019b0: r0 = _growToNextCapacity()
    //     0xa019b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa019b4: ldur            x2, [fp, #-0x10]
    // 0xa019b8: ldur            x3, [fp, #-0x18]
    // 0xa019bc: add             x4, x3, #1
    // 0xa019c0: lsl             x5, x4, #1
    // 0xa019c4: StoreField: r2->field_b = r5
    //     0xa019c4: stur            w5, [x2, #0xb]
    // 0xa019c8: LoadField: r1 = r2->field_f
    //     0xa019c8: ldur            w1, [x2, #0xf]
    // 0xa019cc: DecompressPointer r1
    //     0xa019cc: add             x1, x1, HEAP, lsl #32
    // 0xa019d0: ldur            x0, [fp, #-8]
    // 0xa019d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa019d4: add             x25, x1, x3, lsl #2
    //     0xa019d8: add             x25, x25, #0xf
    //     0xa019dc: str             w0, [x25]
    //     0xa019e0: tbz             w0, #0, #0xa019fc
    //     0xa019e4: ldurb           w16, [x1, #-1]
    //     0xa019e8: ldurb           w17, [x0, #-1]
    //     0xa019ec: and             x16, x17, x16, lsr #2
    //     0xa019f0: tst             x16, HEAP, lsr #32
    //     0xa019f4: b.eq            #0xa019fc
    //     0xa019f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa019fc: r0 = Null
    //     0xa019fc: mov             x0, NULL
    // 0xa01a00: LeaveFrame
    //     0xa01a00: mov             SP, fp
    //     0xa01a04: ldp             fp, lr, [SP], #0x10
    // 0xa01a08: ret
    //     0xa01a08: ret             
    // 0xa01a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa01a0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa01a10: b               #0xa01904
    // 0xa01a14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa01a14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa02688, size: 0x24
    // 0xa02688: r1 = true
    //     0xa02688: add             x1, NULL, #0x20  ; true
    // 0xa0268c: ldr             x2, [SP]
    // 0xa02690: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa02690: ldur            w3, [x2, #0x17]
    // 0xa02694: DecompressPointer r3
    //     0xa02694: add             x3, x3, HEAP, lsl #32
    // 0xa02698: LoadField: r2 = r3->field_f
    //     0xa02698: ldur            w2, [x3, #0xf]
    // 0xa0269c: DecompressPointer r2
    //     0xa0269c: add             x2, x2, HEAP, lsl #32
    // 0xa026a0: StoreField: r2->field_4f = r1
    //     0xa026a0: stur            w1, [x2, #0x4f]
    // 0xa026a4: r0 = Null
    //     0xa026a4: mov             x0, NULL
    // 0xa026a8: ret
    //     0xa026a8: ret             
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa026ac, size: 0x78
    // 0xa026ac: EnterFrame
    //     0xa026ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa026b0: mov             fp, SP
    // 0xa026b4: AllocStack(0x10)
    //     0xa026b4: sub             SP, SP, #0x10
    // 0xa026b8: SetupParameters()
    //     0xa026b8: ldr             x0, [fp, #0x18]
    //     0xa026bc: ldur            w3, [x0, #0x17]
    //     0xa026c0: add             x3, x3, HEAP, lsl #32
    //     0xa026c4: stur            x3, [fp, #-0x10]
    // 0xa026c8: CheckStackOverflow
    //     0xa026c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa026cc: cmp             SP, x16
    //     0xa026d0: b.ls            #0xa0271c
    // 0xa026d4: LoadField: r0 = r3->field_f
    //     0xa026d4: ldur            w0, [x3, #0xf]
    // 0xa026d8: DecompressPointer r0
    //     0xa026d8: add             x0, x0, HEAP, lsl #32
    // 0xa026dc: mov             x2, x3
    // 0xa026e0: stur            x0, [fp, #-8]
    // 0xa026e4: r1 = Function '<anonymous closure>':.
    //     0xa026e4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54988] AnonymousClosure: (0xa02688), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xa026e8: ldr             x1, [x1, #0x988]
    // 0xa026ec: r0 = AllocateClosure()
    //     0xa026ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa026f0: ldur            x1, [fp, #-8]
    // 0xa026f4: mov             x2, x0
    // 0xa026f8: r0 = setState()
    //     0xa026f8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa026fc: ldur            x0, [fp, #-0x10]
    // 0xa02700: LoadField: r1 = r0->field_f
    //     0xa02700: ldur            w1, [x0, #0xf]
    // 0xa02704: DecompressPointer r1
    //     0xa02704: add             x1, x1, HEAP, lsl #32
    // 0xa02708: r0 = validateAddress()
    //     0xa02708: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xa0270c: r0 = Null
    //     0xa0270c: mov             x0, NULL
    // 0xa02710: LeaveFrame
    //     0xa02710: mov             SP, fp
    //     0xa02714: ldp             fp, lr, [SP], #0x10
    // 0xa02718: ret
    //     0xa02718: ret             
    // 0xa0271c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0271c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa02720: b               #0xa026d4
  }
  _ build(/* No info */) {
    // ** addr: 0xbaddd0, size: 0x2968
    // 0xbaddd0: EnterFrame
    //     0xbaddd0: stp             fp, lr, [SP, #-0x10]!
    //     0xbaddd4: mov             fp, SP
    // 0xbaddd8: AllocStack(0xe8)
    //     0xbaddd8: sub             SP, SP, #0xe8
    // 0xbadddc: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbadddc: stur            x1, [fp, #-8]
    //     0xbadde0: mov             x16, x2
    //     0xbadde4: mov             x2, x1
    //     0xbadde8: mov             x1, x16
    //     0xbaddec: stur            x1, [fp, #-0x10]
    // 0xbaddf0: CheckStackOverflow
    //     0xbaddf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaddf4: cmp             SP, x16
    //     0xbaddf8: b.ls            #0xbb0704
    // 0xbaddfc: r1 = 1
    //     0xbaddfc: movz            x1, #0x1
    // 0xbade00: r0 = AllocateContext()
    //     0xbade00: bl              #0x16f6108  ; AllocateContextStub
    // 0xbade04: ldur            x2, [fp, #-8]
    // 0xbade08: stur            x0, [fp, #-0x18]
    // 0xbade0c: StoreField: r0->field_f = r2
    //     0xbade0c: stur            w2, [x0, #0xf]
    // 0xbade10: ldur            x1, [fp, #-0x10]
    // 0xbade14: r0 = of()
    //     0xbade14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbade18: LoadField: r1 = r0->field_87
    //     0xbade18: ldur            w1, [x0, #0x87]
    // 0xbade1c: DecompressPointer r1
    //     0xbade1c: add             x1, x1, HEAP, lsl #32
    // 0xbade20: LoadField: r0 = r1->field_7
    //     0xbade20: ldur            w0, [x1, #7]
    // 0xbade24: DecompressPointer r0
    //     0xbade24: add             x0, x0, HEAP, lsl #32
    // 0xbade28: stur            x0, [fp, #-0x20]
    // 0xbade2c: r1 = Instance_Color
    //     0xbade2c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbade30: d0 = 0.700000
    //     0xbade30: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbade34: ldr             d0, [x17, #0xf48]
    // 0xbade38: r0 = withOpacity()
    //     0xbade38: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbade3c: r16 = 14.000000
    //     0xbade3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbade40: ldr             x16, [x16, #0x1d8]
    // 0xbade44: stp             x0, x16, [SP]
    // 0xbade48: ldur            x1, [fp, #-0x20]
    // 0xbade4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbade4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbade50: ldr             x4, [x4, #0xaa0]
    // 0xbade54: r0 = copyWith()
    //     0xbade54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbade58: stur            x0, [fp, #-0x20]
    // 0xbade5c: r0 = Text()
    //     0xbade5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbade60: mov             x2, x0
    // 0xbade64: r0 = "Change Delivery Address"
    //     0xbade64: add             x0, PP, #0x54, lsl #12  ; [pp+0x54870] "Change Delivery Address"
    //     0xbade68: ldr             x0, [x0, #0x870]
    // 0xbade6c: stur            x2, [fp, #-0x28]
    // 0xbade70: StoreField: r2->field_b = r0
    //     0xbade70: stur            w0, [x2, #0xb]
    // 0xbade74: ldur            x0, [fp, #-0x20]
    // 0xbade78: StoreField: r2->field_13 = r0
    //     0xbade78: stur            w0, [x2, #0x13]
    // 0xbade7c: ldur            x1, [fp, #-0x10]
    // 0xbade80: r0 = of()
    //     0xbade80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbade84: LoadField: r1 = r0->field_5b
    //     0xbade84: ldur            w1, [x0, #0x5b]
    // 0xbade88: DecompressPointer r1
    //     0xbade88: add             x1, x1, HEAP, lsl #32
    // 0xbade8c: stur            x1, [fp, #-0x20]
    // 0xbade90: r0 = ColorFilter()
    //     0xbade90: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbade94: mov             x1, x0
    // 0xbade98: ldur            x0, [fp, #-0x20]
    // 0xbade9c: stur            x1, [fp, #-0x30]
    // 0xbadea0: StoreField: r1->field_7 = r0
    //     0xbadea0: stur            w0, [x1, #7]
    // 0xbadea4: r0 = Instance_BlendMode
    //     0xbadea4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbadea8: ldr             x0, [x0, #0xb30]
    // 0xbadeac: StoreField: r1->field_b = r0
    //     0xbadeac: stur            w0, [x1, #0xb]
    // 0xbadeb0: r0 = 1
    //     0xbadeb0: movz            x0, #0x1
    // 0xbadeb4: StoreField: r1->field_13 = r0
    //     0xbadeb4: stur            x0, [x1, #0x13]
    // 0xbadeb8: r0 = SvgPicture()
    //     0xbadeb8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadebc: stur            x0, [fp, #-0x20]
    // 0xbadec0: ldur            x16, [fp, #-0x30]
    // 0xbadec4: str             x16, [SP]
    // 0xbadec8: mov             x1, x0
    // 0xbadecc: r2 = "assets/images/x.svg"
    //     0xbadecc: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbaded0: ldr             x2, [x2, #0x5e8]
    // 0xbaded4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbaded4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbaded8: ldr             x4, [x4, #0xa38]
    // 0xbadedc: r0 = SvgPicture.asset()
    //     0xbadedc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadee0: r0 = InkWell()
    //     0xbadee0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbadee4: mov             x3, x0
    // 0xbadee8: ldur            x0, [fp, #-0x20]
    // 0xbadeec: stur            x3, [fp, #-0x30]
    // 0xbadef0: StoreField: r3->field_b = r0
    //     0xbadef0: stur            w0, [x3, #0xb]
    // 0xbadef4: r1 = Function '<anonymous closure>':.
    //     0xbadef4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54878] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbadef8: ldr             x1, [x1, #0x878]
    // 0xbadefc: r2 = Null
    //     0xbadefc: mov             x2, NULL
    // 0xbadf00: r0 = AllocateClosure()
    //     0xbadf00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbadf04: mov             x1, x0
    // 0xbadf08: ldur            x0, [fp, #-0x30]
    // 0xbadf0c: StoreField: r0->field_f = r1
    //     0xbadf0c: stur            w1, [x0, #0xf]
    // 0xbadf10: r3 = true
    //     0xbadf10: add             x3, NULL, #0x20  ; true
    // 0xbadf14: StoreField: r0->field_43 = r3
    //     0xbadf14: stur            w3, [x0, #0x43]
    // 0xbadf18: r1 = Instance_BoxShape
    //     0xbadf18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbadf1c: ldr             x1, [x1, #0x80]
    // 0xbadf20: StoreField: r0->field_47 = r1
    //     0xbadf20: stur            w1, [x0, #0x47]
    // 0xbadf24: StoreField: r0->field_6f = r3
    //     0xbadf24: stur            w3, [x0, #0x6f]
    // 0xbadf28: r4 = false
    //     0xbadf28: add             x4, NULL, #0x30  ; false
    // 0xbadf2c: StoreField: r0->field_73 = r4
    //     0xbadf2c: stur            w4, [x0, #0x73]
    // 0xbadf30: StoreField: r0->field_83 = r3
    //     0xbadf30: stur            w3, [x0, #0x83]
    // 0xbadf34: StoreField: r0->field_7b = r4
    //     0xbadf34: stur            w4, [x0, #0x7b]
    // 0xbadf38: r1 = Null
    //     0xbadf38: mov             x1, NULL
    // 0xbadf3c: r2 = 4
    //     0xbadf3c: movz            x2, #0x4
    // 0xbadf40: r0 = AllocateArray()
    //     0xbadf40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbadf44: mov             x2, x0
    // 0xbadf48: ldur            x0, [fp, #-0x28]
    // 0xbadf4c: stur            x2, [fp, #-0x20]
    // 0xbadf50: StoreField: r2->field_f = r0
    //     0xbadf50: stur            w0, [x2, #0xf]
    // 0xbadf54: ldur            x0, [fp, #-0x30]
    // 0xbadf58: StoreField: r2->field_13 = r0
    //     0xbadf58: stur            w0, [x2, #0x13]
    // 0xbadf5c: r1 = <Widget>
    //     0xbadf5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbadf60: r0 = AllocateGrowableArray()
    //     0xbadf60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbadf64: mov             x1, x0
    // 0xbadf68: ldur            x0, [fp, #-0x20]
    // 0xbadf6c: stur            x1, [fp, #-0x28]
    // 0xbadf70: StoreField: r1->field_f = r0
    //     0xbadf70: stur            w0, [x1, #0xf]
    // 0xbadf74: r2 = 4
    //     0xbadf74: movz            x2, #0x4
    // 0xbadf78: StoreField: r1->field_b = r2
    //     0xbadf78: stur            w2, [x1, #0xb]
    // 0xbadf7c: r0 = Row()
    //     0xbadf7c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbadf80: mov             x1, x0
    // 0xbadf84: r0 = Instance_Axis
    //     0xbadf84: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbadf88: stur            x1, [fp, #-0x20]
    // 0xbadf8c: StoreField: r1->field_f = r0
    //     0xbadf8c: stur            w0, [x1, #0xf]
    // 0xbadf90: r2 = Instance_MainAxisAlignment
    //     0xbadf90: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbadf94: ldr             x2, [x2, #0xa8]
    // 0xbadf98: StoreField: r1->field_13 = r2
    //     0xbadf98: stur            w2, [x1, #0x13]
    // 0xbadf9c: r2 = Instance_MainAxisSize
    //     0xbadf9c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbadfa0: ldr             x2, [x2, #0xa10]
    // 0xbadfa4: ArrayStore: r1[0] = r2  ; List_4
    //     0xbadfa4: stur            w2, [x1, #0x17]
    // 0xbadfa8: r3 = Instance_CrossAxisAlignment
    //     0xbadfa8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbadfac: ldr             x3, [x3, #0xa18]
    // 0xbadfb0: StoreField: r1->field_1b = r3
    //     0xbadfb0: stur            w3, [x1, #0x1b]
    // 0xbadfb4: r4 = Instance_VerticalDirection
    //     0xbadfb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbadfb8: ldr             x4, [x4, #0xa20]
    // 0xbadfbc: StoreField: r1->field_23 = r4
    //     0xbadfbc: stur            w4, [x1, #0x23]
    // 0xbadfc0: r5 = Instance_Clip
    //     0xbadfc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbadfc4: ldr             x5, [x5, #0x38]
    // 0xbadfc8: StoreField: r1->field_2b = r5
    //     0xbadfc8: stur            w5, [x1, #0x2b]
    // 0xbadfcc: StoreField: r1->field_2f = rZR
    //     0xbadfcc: stur            xzr, [x1, #0x2f]
    // 0xbadfd0: ldur            x6, [fp, #-0x28]
    // 0xbadfd4: StoreField: r1->field_b = r6
    //     0xbadfd4: stur            w6, [x1, #0xb]
    // 0xbadfd8: r0 = Padding()
    //     0xbadfd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbadfdc: mov             x1, x0
    // 0xbadfe0: r0 = Instance_EdgeInsets
    //     0xbadfe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xbadfe4: ldr             x0, [x0, #0xd0]
    // 0xbadfe8: stur            x1, [fp, #-0x28]
    // 0xbadfec: StoreField: r1->field_f = r0
    //     0xbadfec: stur            w0, [x1, #0xf]
    // 0xbadff0: ldur            x0, [fp, #-0x20]
    // 0xbadff4: StoreField: r1->field_b = r0
    //     0xbadff4: stur            w0, [x1, #0xb]
    // 0xbadff8: ldur            x2, [fp, #-8]
    // 0xbadffc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbadffc: ldur            w0, [x2, #0x17]
    // 0xbae000: DecompressPointer r0
    //     0xbae000: add             x0, x0, HEAP, lsl #32
    // 0xbae004: stur            x0, [fp, #-0x20]
    // 0xbae008: r16 = "[a-zA-Z ]"
    //     0xbae008: add             x16, PP, #0x54, lsl #12  ; [pp+0x54040] "[a-zA-Z ]"
    //     0xbae00c: ldr             x16, [x16, #0x40]
    // 0xbae010: stp             x16, NULL, [SP, #0x20]
    // 0xbae014: r16 = false
    //     0xbae014: add             x16, NULL, #0x30  ; false
    // 0xbae018: r30 = true
    //     0xbae018: add             lr, NULL, #0x20  ; true
    // 0xbae01c: stp             lr, x16, [SP, #0x10]
    // 0xbae020: r16 = false
    //     0xbae020: add             x16, NULL, #0x30  ; false
    // 0xbae024: r30 = false
    //     0xbae024: add             lr, NULL, #0x30  ; false
    // 0xbae028: stp             lr, x16, [SP]
    // 0xbae02c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbae02c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbae030: r0 = _RegExp()
    //     0xbae030: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbae034: stur            x0, [fp, #-0x30]
    // 0xbae038: r0 = FilteringTextInputFormatter()
    //     0xbae038: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbae03c: mov             x1, x0
    // 0xbae040: ldur            x0, [fp, #-0x30]
    // 0xbae044: stur            x1, [fp, #-0x38]
    // 0xbae048: StoreField: r1->field_b = r0
    //     0xbae048: stur            w0, [x1, #0xb]
    // 0xbae04c: r0 = true
    //     0xbae04c: add             x0, NULL, #0x20  ; true
    // 0xbae050: StoreField: r1->field_7 = r0
    //     0xbae050: stur            w0, [x1, #7]
    // 0xbae054: r2 = ""
    //     0xbae054: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbae058: StoreField: r1->field_f = r2
    //     0xbae058: stur            w2, [x1, #0xf]
    // 0xbae05c: r0 = LengthLimitingTextInputFormatter()
    //     0xbae05c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbae060: mov             x3, x0
    // 0xbae064: r0 = 180
    //     0xbae064: movz            x0, #0xb4
    // 0xbae068: stur            x3, [fp, #-0x30]
    // 0xbae06c: StoreField: r3->field_7 = r0
    //     0xbae06c: stur            w0, [x3, #7]
    // 0xbae070: r1 = Null
    //     0xbae070: mov             x1, NULL
    // 0xbae074: r2 = 4
    //     0xbae074: movz            x2, #0x4
    // 0xbae078: r0 = AllocateArray()
    //     0xbae078: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbae07c: mov             x2, x0
    // 0xbae080: ldur            x0, [fp, #-0x38]
    // 0xbae084: stur            x2, [fp, #-0x40]
    // 0xbae088: StoreField: r2->field_f = r0
    //     0xbae088: stur            w0, [x2, #0xf]
    // 0xbae08c: ldur            x0, [fp, #-0x30]
    // 0xbae090: StoreField: r2->field_13 = r0
    //     0xbae090: stur            w0, [x2, #0x13]
    // 0xbae094: r1 = <TextInputFormatter>
    //     0xbae094: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbae098: ldr             x1, [x1, #0x7b0]
    // 0xbae09c: r0 = AllocateGrowableArray()
    //     0xbae09c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbae0a0: mov             x2, x0
    // 0xbae0a4: ldur            x0, [fp, #-0x40]
    // 0xbae0a8: stur            x2, [fp, #-0x30]
    // 0xbae0ac: StoreField: r2->field_f = r0
    //     0xbae0ac: stur            w0, [x2, #0xf]
    // 0xbae0b0: r0 = 4
    //     0xbae0b0: movz            x0, #0x4
    // 0xbae0b4: StoreField: r2->field_b = r0
    //     0xbae0b4: stur            w0, [x2, #0xb]
    // 0xbae0b8: ldur            x1, [fp, #-0x10]
    // 0xbae0bc: r0 = of()
    //     0xbae0bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae0c0: LoadField: r1 = r0->field_87
    //     0xbae0c0: ldur            w1, [x0, #0x87]
    // 0xbae0c4: DecompressPointer r1
    //     0xbae0c4: add             x1, x1, HEAP, lsl #32
    // 0xbae0c8: LoadField: r0 = r1->field_2b
    //     0xbae0c8: ldur            w0, [x1, #0x2b]
    // 0xbae0cc: DecompressPointer r0
    //     0xbae0cc: add             x0, x0, HEAP, lsl #32
    // 0xbae0d0: ldur            x1, [fp, #-0x10]
    // 0xbae0d4: stur            x0, [fp, #-0x38]
    // 0xbae0d8: r0 = of()
    //     0xbae0d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae0dc: LoadField: r1 = r0->field_5b
    //     0xbae0dc: ldur            w1, [x0, #0x5b]
    // 0xbae0e0: DecompressPointer r1
    //     0xbae0e0: add             x1, x1, HEAP, lsl #32
    // 0xbae0e4: r16 = 14.000000
    //     0xbae0e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbae0e8: ldr             x16, [x16, #0x1d8]
    // 0xbae0ec: stp             x1, x16, [SP]
    // 0xbae0f0: ldur            x1, [fp, #-0x38]
    // 0xbae0f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae0f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae0f8: ldr             x4, [x4, #0xaa0]
    // 0xbae0fc: r0 = copyWith()
    //     0xbae0fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae100: ldur            x2, [fp, #-8]
    // 0xbae104: stur            x0, [fp, #-0x40]
    // 0xbae108: LoadField: r3 = r2->field_33
    //     0xbae108: ldur            w3, [x2, #0x33]
    // 0xbae10c: DecompressPointer r3
    //     0xbae10c: add             x3, x3, HEAP, lsl #32
    // 0xbae110: ldur            x1, [fp, #-0x10]
    // 0xbae114: stur            x3, [fp, #-0x38]
    // 0xbae118: r0 = getTextFormFieldInputDecoration()
    //     0xbae118: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbae11c: ldur            x1, [fp, #-0x10]
    // 0xbae120: stur            x0, [fp, #-0x48]
    // 0xbae124: r0 = of()
    //     0xbae124: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae128: LoadField: r1 = r0->field_5b
    //     0xbae128: ldur            w1, [x0, #0x5b]
    // 0xbae12c: DecompressPointer r1
    //     0xbae12c: add             x1, x1, HEAP, lsl #32
    // 0xbae130: stur            x1, [fp, #-0x50]
    // 0xbae134: r0 = BorderSide()
    //     0xbae134: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbae138: mov             x1, x0
    // 0xbae13c: ldur            x0, [fp, #-0x50]
    // 0xbae140: stur            x1, [fp, #-0x58]
    // 0xbae144: StoreField: r1->field_7 = r0
    //     0xbae144: stur            w0, [x1, #7]
    // 0xbae148: d0 = 1.000000
    //     0xbae148: fmov            d0, #1.00000000
    // 0xbae14c: StoreField: r1->field_b = d0
    //     0xbae14c: stur            d0, [x1, #0xb]
    // 0xbae150: r0 = Instance_BorderStyle
    //     0xbae150: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbae154: ldr             x0, [x0, #0xf68]
    // 0xbae158: StoreField: r1->field_13 = r0
    //     0xbae158: stur            w0, [x1, #0x13]
    // 0xbae15c: d1 = -1.000000
    //     0xbae15c: fmov            d1, #-1.00000000
    // 0xbae160: ArrayStore: r1[0] = d1  ; List_8
    //     0xbae160: stur            d1, [x1, #0x17]
    // 0xbae164: r0 = OutlineInputBorder()
    //     0xbae164: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbae168: mov             x2, x0
    // 0xbae16c: r0 = Instance_BorderRadius
    //     0xbae16c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbae170: ldr             x0, [x0, #0xf70]
    // 0xbae174: stur            x2, [fp, #-0x50]
    // 0xbae178: StoreField: r2->field_13 = r0
    //     0xbae178: stur            w0, [x2, #0x13]
    // 0xbae17c: d0 = 4.000000
    //     0xbae17c: fmov            d0, #4.00000000
    // 0xbae180: StoreField: r2->field_b = d0
    //     0xbae180: stur            d0, [x2, #0xb]
    // 0xbae184: ldur            x1, [fp, #-0x58]
    // 0xbae188: StoreField: r2->field_7 = r1
    //     0xbae188: stur            w1, [x2, #7]
    // 0xbae18c: ldur            x1, [fp, #-0x10]
    // 0xbae190: r0 = of()
    //     0xbae190: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae194: LoadField: r1 = r0->field_87
    //     0xbae194: ldur            w1, [x0, #0x87]
    // 0xbae198: DecompressPointer r1
    //     0xbae198: add             x1, x1, HEAP, lsl #32
    // 0xbae19c: LoadField: r0 = r1->field_2b
    //     0xbae19c: ldur            w0, [x1, #0x2b]
    // 0xbae1a0: DecompressPointer r0
    //     0xbae1a0: add             x0, x0, HEAP, lsl #32
    // 0xbae1a4: stur            x0, [fp, #-0x58]
    // 0xbae1a8: r1 = Instance_Color
    //     0xbae1a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbae1ac: d0 = 0.400000
    //     0xbae1ac: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbae1b0: r0 = withOpacity()
    //     0xbae1b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbae1b4: r16 = 14.000000
    //     0xbae1b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbae1b8: ldr             x16, [x16, #0x1d8]
    // 0xbae1bc: stp             x0, x16, [SP]
    // 0xbae1c0: ldur            x1, [fp, #-0x58]
    // 0xbae1c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae1c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae1c8: ldr             x4, [x4, #0xaa0]
    // 0xbae1cc: r0 = copyWith()
    //     0xbae1cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae1d0: ldur            x1, [fp, #-0x10]
    // 0xbae1d4: stur            x0, [fp, #-0x58]
    // 0xbae1d8: r0 = of()
    //     0xbae1d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae1dc: LoadField: r1 = r0->field_87
    //     0xbae1dc: ldur            w1, [x0, #0x87]
    // 0xbae1e0: DecompressPointer r1
    //     0xbae1e0: add             x1, x1, HEAP, lsl #32
    // 0xbae1e4: LoadField: r0 = r1->field_2b
    //     0xbae1e4: ldur            w0, [x1, #0x2b]
    // 0xbae1e8: DecompressPointer r0
    //     0xbae1e8: add             x0, x0, HEAP, lsl #32
    // 0xbae1ec: r16 = 12.000000
    //     0xbae1ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbae1f0: ldr             x16, [x16, #0x9e8]
    // 0xbae1f4: r30 = Instance_MaterialColor
    //     0xbae1f4: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbae1f8: ldr             lr, [lr, #0x180]
    // 0xbae1fc: stp             lr, x16, [SP]
    // 0xbae200: mov             x1, x0
    // 0xbae204: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae204: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae208: ldr             x4, [x4, #0xaa0]
    // 0xbae20c: r0 = copyWith()
    //     0xbae20c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae210: ldur            x2, [fp, #-8]
    // 0xbae214: stur            x0, [fp, #-0x70]
    // 0xbae218: LoadField: r1 = r2->field_4f
    //     0xbae218: ldur            w1, [x2, #0x4f]
    // 0xbae21c: DecompressPointer r1
    //     0xbae21c: add             x1, x1, HEAP, lsl #32
    // 0xbae220: tbnz            w1, #4, #0xbae2ac
    // 0xbae224: LoadField: r1 = r2->field_33
    //     0xbae224: ldur            w1, [x2, #0x33]
    // 0xbae228: DecompressPointer r1
    //     0xbae228: add             x1, x1, HEAP, lsl #32
    // 0xbae22c: LoadField: r3 = r1->field_27
    //     0xbae22c: ldur            w3, [x1, #0x27]
    // 0xbae230: DecompressPointer r3
    //     0xbae230: add             x3, x3, HEAP, lsl #32
    // 0xbae234: LoadField: r1 = r3->field_7
    //     0xbae234: ldur            w1, [x3, #7]
    // 0xbae238: DecompressPointer r1
    //     0xbae238: add             x1, x1, HEAP, lsl #32
    // 0xbae23c: LoadField: r3 = r1->field_7
    //     0xbae23c: ldur            w3, [x1, #7]
    // 0xbae240: cbz             w3, #0xbae25c
    // 0xbae244: r1 = LoadInt32Instr(r3)
    //     0xbae244: sbfx            x1, x3, #1, #0x1f
    // 0xbae248: cmp             x1, #1
    // 0xbae24c: b.le            #0xbae25c
    // 0xbae250: r1 = Instance_IconData
    //     0xbae250: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbae254: ldr             x1, [x1, #0x130]
    // 0xbae258: b               #0xbae264
    // 0xbae25c: r1 = Instance_IconData
    //     0xbae25c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbae260: ldr             x1, [x1, #0x138]
    // 0xbae264: stur            x1, [fp, #-0x68]
    // 0xbae268: cbz             w3, #0xbae284
    // 0xbae26c: r4 = LoadInt32Instr(r3)
    //     0xbae26c: sbfx            x4, x3, #1, #0x1f
    // 0xbae270: cmp             x4, #1
    // 0xbae274: b.le            #0xbae284
    // 0xbae278: r3 = Instance_Color
    //     0xbae278: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbae27c: ldr             x3, [x3, #0x858]
    // 0xbae280: b               #0xbae28c
    // 0xbae284: r3 = Instance_Color
    //     0xbae284: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbae288: ldr             x3, [x3, #0x50]
    // 0xbae28c: stur            x3, [fp, #-0x60]
    // 0xbae290: r0 = Icon()
    //     0xbae290: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbae294: mov             x1, x0
    // 0xbae298: ldur            x0, [fp, #-0x68]
    // 0xbae29c: StoreField: r1->field_b = r0
    //     0xbae29c: stur            w0, [x1, #0xb]
    // 0xbae2a0: ldur            x0, [fp, #-0x60]
    // 0xbae2a4: StoreField: r1->field_23 = r0
    //     0xbae2a4: stur            w0, [x1, #0x23]
    // 0xbae2a8: b               #0xbae2b0
    // 0xbae2ac: r1 = Null
    //     0xbae2ac: mov             x1, NULL
    // 0xbae2b0: ldur            x2, [fp, #-8]
    // 0xbae2b4: ldur            x0, [fp, #-0x20]
    // 0xbae2b8: ldur            x16, [fp, #-0x50]
    // 0xbae2bc: r30 = Instance_EdgeInsets
    //     0xbae2bc: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbae2c0: ldr             lr, [lr, #0xc40]
    // 0xbae2c4: stp             lr, x16, [SP, #0x20]
    // 0xbae2c8: r16 = "Full Name*"
    //     0xbae2c8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54048] "Full Name*"
    //     0xbae2cc: ldr             x16, [x16, #0x48]
    // 0xbae2d0: ldur            lr, [fp, #-0x58]
    // 0xbae2d4: stp             lr, x16, [SP, #0x10]
    // 0xbae2d8: ldur            x16, [fp, #-0x70]
    // 0xbae2dc: stp             x1, x16, [SP]
    // 0xbae2e0: ldur            x1, [fp, #-0x48]
    // 0xbae2e4: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x6, null]
    //     0xbae2e4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54050] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x6, Null]
    //     0xbae2e8: ldr             x4, [x4, #0x50]
    // 0xbae2ec: r0 = copyWith()
    //     0xbae2ec: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbae2f0: ldur            x2, [fp, #-8]
    // 0xbae2f4: r1 = Function '_validateCustomerNameNumber@1659256992':.
    //     0xbae2f4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54880] AnonymousClosure: (0xbb10fc), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateCustomerNameNumber (0xa02760)
    //     0xbae2f8: ldr             x1, [x1, #0x880]
    // 0xbae2fc: stur            x0, [fp, #-0x48]
    // 0xbae300: r0 = AllocateClosure()
    //     0xbae300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbae304: ldur            x2, [fp, #-0x18]
    // 0xbae308: r1 = Function '<anonymous closure>':.
    //     0xbae308: add             x1, PP, #0x54, lsl #12  ; [pp+0x54888] AnonymousClosure: (0xa026ac), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbae30c: ldr             x1, [x1, #0x888]
    // 0xbae310: stur            x0, [fp, #-0x50]
    // 0xbae314: r0 = AllocateClosure()
    //     0xbae314: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbae318: r1 = <String>
    //     0xbae318: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbae31c: stur            x0, [fp, #-0x58]
    // 0xbae320: r0 = TextFormField()
    //     0xbae320: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbae324: stur            x0, [fp, #-0x60]
    // 0xbae328: ldur            x16, [fp, #-0x50]
    // 0xbae32c: r30 = true
    //     0xbae32c: add             lr, NULL, #0x20  ; true
    // 0xbae330: stp             lr, x16, [SP, #0x40]
    // 0xbae334: r16 = true
    //     0xbae334: add             x16, NULL, #0x20  ; true
    // 0xbae338: r30 = Instance_AutovalidateMode
    //     0xbae338: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbae33c: ldr             lr, [lr, #0x7e8]
    // 0xbae340: stp             lr, x16, [SP, #0x30]
    // 0xbae344: ldur            x16, [fp, #-0x30]
    // 0xbae348: ldur            lr, [fp, #-0x40]
    // 0xbae34c: stp             lr, x16, [SP, #0x20]
    // 0xbae350: r16 = Instance_TextInputType
    //     0xbae350: add             x16, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xbae354: ldr             x16, [x16, #0x68]
    // 0xbae358: r30 = 2
    //     0xbae358: movz            lr, #0x2
    // 0xbae35c: stp             lr, x16, [SP, #0x10]
    // 0xbae360: ldur            x16, [fp, #-0x38]
    // 0xbae364: ldur            lr, [fp, #-0x58]
    // 0xbae368: stp             lr, x16, [SP]
    // 0xbae36c: mov             x1, x0
    // 0xbae370: ldur            x2, [fp, #-0x48]
    // 0xbae374: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x8, maxLines, 0x9, onChanged, 0xb, style, 0x7, validator, 0x2, null]
    //     0xbae374: add             x4, PP, #0x54, lsl #12  ; [pp+0x54070] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x8, "maxLines", 0x9, "onChanged", 0xb, "style", 0x7, "validator", 0x2, Null]
    //     0xbae378: ldr             x4, [x4, #0x70]
    // 0xbae37c: r0 = TextFormField()
    //     0xbae37c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbae380: r0 = Form()
    //     0xbae380: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbae384: mov             x1, x0
    // 0xbae388: ldur            x0, [fp, #-0x60]
    // 0xbae38c: stur            x1, [fp, #-0x30]
    // 0xbae390: StoreField: r1->field_b = r0
    //     0xbae390: stur            w0, [x1, #0xb]
    // 0xbae394: r0 = Instance_AutovalidateMode
    //     0xbae394: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbae398: ldr             x0, [x0, #0x800]
    // 0xbae39c: StoreField: r1->field_23 = r0
    //     0xbae39c: stur            w0, [x1, #0x23]
    // 0xbae3a0: ldur            x2, [fp, #-0x20]
    // 0xbae3a4: StoreField: r1->field_7 = r2
    //     0xbae3a4: stur            w2, [x1, #7]
    // 0xbae3a8: r0 = Padding()
    //     0xbae3a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbae3ac: mov             x1, x0
    // 0xbae3b0: r0 = Instance_EdgeInsets
    //     0xbae3b0: add             x0, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xbae3b4: ldr             x0, [x0, #0x620]
    // 0xbae3b8: stur            x1, [fp, #-0x38]
    // 0xbae3bc: StoreField: r1->field_f = r0
    //     0xbae3bc: stur            w0, [x1, #0xf]
    // 0xbae3c0: ldur            x0, [fp, #-0x30]
    // 0xbae3c4: StoreField: r1->field_b = r0
    //     0xbae3c4: stur            w0, [x1, #0xb]
    // 0xbae3c8: ldur            x2, [fp, #-8]
    // 0xbae3cc: LoadField: r0 = r2->field_13
    //     0xbae3cc: ldur            w0, [x2, #0x13]
    // 0xbae3d0: DecompressPointer r0
    //     0xbae3d0: add             x0, x0, HEAP, lsl #32
    // 0xbae3d4: stur            x0, [fp, #-0x20]
    // 0xbae3d8: r0 = InitLateStaticField(0xa94) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::singleLineFormatter
    //     0xbae3d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbae3dc: ldr             x0, [x0, #0x1528]
    //     0xbae3e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbae3e4: cmp             w0, w16
    //     0xbae3e8: b.ne            #0xbae3f8
    //     0xbae3ec: add             x2, PP, #0x54, lsl #12  ; [pp+0x54078] Field <FilteringTextInputFormatter.singleLineFormatter>: static late final (offset: 0xa94)
    //     0xbae3f0: ldr             x2, [x2, #0x78]
    //     0xbae3f4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbae3f8: stur            x0, [fp, #-0x30]
    // 0xbae3fc: r0 = LengthLimitingTextInputFormatter()
    //     0xbae3fc: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbae400: mov             x3, x0
    // 0xbae404: r0 = 240
    //     0xbae404: movz            x0, #0xf0
    // 0xbae408: stur            x3, [fp, #-0x40]
    // 0xbae40c: StoreField: r3->field_7 = r0
    //     0xbae40c: stur            w0, [x3, #7]
    // 0xbae410: r1 = Null
    //     0xbae410: mov             x1, NULL
    // 0xbae414: r2 = 4
    //     0xbae414: movz            x2, #0x4
    // 0xbae418: r0 = AllocateArray()
    //     0xbae418: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbae41c: mov             x2, x0
    // 0xbae420: ldur            x0, [fp, #-0x30]
    // 0xbae424: stur            x2, [fp, #-0x48]
    // 0xbae428: StoreField: r2->field_f = r0
    //     0xbae428: stur            w0, [x2, #0xf]
    // 0xbae42c: ldur            x0, [fp, #-0x40]
    // 0xbae430: StoreField: r2->field_13 = r0
    //     0xbae430: stur            w0, [x2, #0x13]
    // 0xbae434: r1 = <TextInputFormatter>
    //     0xbae434: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbae438: ldr             x1, [x1, #0x7b0]
    // 0xbae43c: r0 = AllocateGrowableArray()
    //     0xbae43c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbae440: mov             x2, x0
    // 0xbae444: ldur            x0, [fp, #-0x48]
    // 0xbae448: stur            x2, [fp, #-0x40]
    // 0xbae44c: StoreField: r2->field_f = r0
    //     0xbae44c: stur            w0, [x2, #0xf]
    // 0xbae450: r0 = 4
    //     0xbae450: movz            x0, #0x4
    // 0xbae454: StoreField: r2->field_b = r0
    //     0xbae454: stur            w0, [x2, #0xb]
    // 0xbae458: ldur            x3, [fp, #-8]
    // 0xbae45c: LoadField: r4 = r3->field_3b
    //     0xbae45c: ldur            w4, [x3, #0x3b]
    // 0xbae460: DecompressPointer r4
    //     0xbae460: add             x4, x4, HEAP, lsl #32
    // 0xbae464: ldur            x1, [fp, #-0x10]
    // 0xbae468: stur            x4, [fp, #-0x30]
    // 0xbae46c: r0 = of()
    //     0xbae46c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae470: LoadField: r1 = r0->field_87
    //     0xbae470: ldur            w1, [x0, #0x87]
    // 0xbae474: DecompressPointer r1
    //     0xbae474: add             x1, x1, HEAP, lsl #32
    // 0xbae478: LoadField: r0 = r1->field_2b
    //     0xbae478: ldur            w0, [x1, #0x2b]
    // 0xbae47c: DecompressPointer r0
    //     0xbae47c: add             x0, x0, HEAP, lsl #32
    // 0xbae480: ldur            x1, [fp, #-0x10]
    // 0xbae484: stur            x0, [fp, #-0x48]
    // 0xbae488: r0 = of()
    //     0xbae488: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae48c: LoadField: r1 = r0->field_5b
    //     0xbae48c: ldur            w1, [x0, #0x5b]
    // 0xbae490: DecompressPointer r1
    //     0xbae490: add             x1, x1, HEAP, lsl #32
    // 0xbae494: r16 = 14.000000
    //     0xbae494: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbae498: ldr             x16, [x16, #0x1d8]
    // 0xbae49c: stp             x1, x16, [SP]
    // 0xbae4a0: ldur            x1, [fp, #-0x48]
    // 0xbae4a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae4a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae4a8: ldr             x4, [x4, #0xaa0]
    // 0xbae4ac: r0 = copyWith()
    //     0xbae4ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae4b0: ldur            x1, [fp, #-0x10]
    // 0xbae4b4: stur            x0, [fp, #-0x48]
    // 0xbae4b8: r0 = getTextFormFieldInputDecoration()
    //     0xbae4b8: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbae4bc: ldur            x1, [fp, #-0x10]
    // 0xbae4c0: stur            x0, [fp, #-0x50]
    // 0xbae4c4: r0 = of()
    //     0xbae4c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae4c8: LoadField: r1 = r0->field_5b
    //     0xbae4c8: ldur            w1, [x0, #0x5b]
    // 0xbae4cc: DecompressPointer r1
    //     0xbae4cc: add             x1, x1, HEAP, lsl #32
    // 0xbae4d0: stur            x1, [fp, #-0x58]
    // 0xbae4d4: r0 = BorderSide()
    //     0xbae4d4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbae4d8: mov             x1, x0
    // 0xbae4dc: ldur            x0, [fp, #-0x58]
    // 0xbae4e0: stur            x1, [fp, #-0x60]
    // 0xbae4e4: StoreField: r1->field_7 = r0
    //     0xbae4e4: stur            w0, [x1, #7]
    // 0xbae4e8: d0 = 1.000000
    //     0xbae4e8: fmov            d0, #1.00000000
    // 0xbae4ec: StoreField: r1->field_b = d0
    //     0xbae4ec: stur            d0, [x1, #0xb]
    // 0xbae4f0: r0 = Instance_BorderStyle
    //     0xbae4f0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbae4f4: ldr             x0, [x0, #0xf68]
    // 0xbae4f8: StoreField: r1->field_13 = r0
    //     0xbae4f8: stur            w0, [x1, #0x13]
    // 0xbae4fc: d1 = -1.000000
    //     0xbae4fc: fmov            d1, #-1.00000000
    // 0xbae500: ArrayStore: r1[0] = d1  ; List_8
    //     0xbae500: stur            d1, [x1, #0x17]
    // 0xbae504: r0 = OutlineInputBorder()
    //     0xbae504: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbae508: mov             x2, x0
    // 0xbae50c: r0 = Instance_BorderRadius
    //     0xbae50c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbae510: ldr             x0, [x0, #0xf70]
    // 0xbae514: stur            x2, [fp, #-0x58]
    // 0xbae518: StoreField: r2->field_13 = r0
    //     0xbae518: stur            w0, [x2, #0x13]
    // 0xbae51c: d0 = 4.000000
    //     0xbae51c: fmov            d0, #4.00000000
    // 0xbae520: StoreField: r2->field_b = d0
    //     0xbae520: stur            d0, [x2, #0xb]
    // 0xbae524: ldur            x1, [fp, #-0x60]
    // 0xbae528: StoreField: r2->field_7 = r1
    //     0xbae528: stur            w1, [x2, #7]
    // 0xbae52c: ldur            x1, [fp, #-0x10]
    // 0xbae530: r0 = of()
    //     0xbae530: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae534: LoadField: r1 = r0->field_87
    //     0xbae534: ldur            w1, [x0, #0x87]
    // 0xbae538: DecompressPointer r1
    //     0xbae538: add             x1, x1, HEAP, lsl #32
    // 0xbae53c: LoadField: r0 = r1->field_2b
    //     0xbae53c: ldur            w0, [x1, #0x2b]
    // 0xbae540: DecompressPointer r0
    //     0xbae540: add             x0, x0, HEAP, lsl #32
    // 0xbae544: stur            x0, [fp, #-0x60]
    // 0xbae548: r1 = Instance_Color
    //     0xbae548: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbae54c: d0 = 0.400000
    //     0xbae54c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbae550: r0 = withOpacity()
    //     0xbae550: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbae554: r16 = 14.000000
    //     0xbae554: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbae558: ldr             x16, [x16, #0x1d8]
    // 0xbae55c: stp             x0, x16, [SP]
    // 0xbae560: ldur            x1, [fp, #-0x60]
    // 0xbae564: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae564: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae568: ldr             x4, [x4, #0xaa0]
    // 0xbae56c: r0 = copyWith()
    //     0xbae56c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae570: ldur            x1, [fp, #-0x10]
    // 0xbae574: stur            x0, [fp, #-0x60]
    // 0xbae578: r0 = of()
    //     0xbae578: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae57c: LoadField: r1 = r0->field_87
    //     0xbae57c: ldur            w1, [x0, #0x87]
    // 0xbae580: DecompressPointer r1
    //     0xbae580: add             x1, x1, HEAP, lsl #32
    // 0xbae584: LoadField: r0 = r1->field_2b
    //     0xbae584: ldur            w0, [x1, #0x2b]
    // 0xbae588: DecompressPointer r0
    //     0xbae588: add             x0, x0, HEAP, lsl #32
    // 0xbae58c: r16 = 12.000000
    //     0xbae58c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbae590: ldr             x16, [x16, #0x9e8]
    // 0xbae594: r30 = Instance_MaterialColor
    //     0xbae594: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbae598: ldr             lr, [lr, #0x180]
    // 0xbae59c: stp             lr, x16, [SP]
    // 0xbae5a0: mov             x1, x0
    // 0xbae5a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae5a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae5a8: ldr             x4, [x4, #0xaa0]
    // 0xbae5ac: r0 = copyWith()
    //     0xbae5ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae5b0: ldur            x2, [fp, #-8]
    // 0xbae5b4: stur            x0, [fp, #-0x78]
    // 0xbae5b8: LoadField: r1 = r2->field_53
    //     0xbae5b8: ldur            w1, [x2, #0x53]
    // 0xbae5bc: DecompressPointer r1
    //     0xbae5bc: add             x1, x1, HEAP, lsl #32
    // 0xbae5c0: tbnz            w1, #4, #0xbae634
    // 0xbae5c4: LoadField: r1 = r2->field_3b
    //     0xbae5c4: ldur            w1, [x2, #0x3b]
    // 0xbae5c8: DecompressPointer r1
    //     0xbae5c8: add             x1, x1, HEAP, lsl #32
    // 0xbae5cc: LoadField: r3 = r1->field_27
    //     0xbae5cc: ldur            w3, [x1, #0x27]
    // 0xbae5d0: DecompressPointer r3
    //     0xbae5d0: add             x3, x3, HEAP, lsl #32
    // 0xbae5d4: LoadField: r1 = r3->field_7
    //     0xbae5d4: ldur            w1, [x3, #7]
    // 0xbae5d8: DecompressPointer r1
    //     0xbae5d8: add             x1, x1, HEAP, lsl #32
    // 0xbae5dc: LoadField: r3 = r1->field_7
    //     0xbae5dc: ldur            w3, [x1, #7]
    // 0xbae5e0: cbz             w3, #0xbae5f0
    // 0xbae5e4: r1 = Instance_IconData
    //     0xbae5e4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbae5e8: ldr             x1, [x1, #0x130]
    // 0xbae5ec: b               #0xbae5f8
    // 0xbae5f0: r1 = Instance_IconData
    //     0xbae5f0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbae5f4: ldr             x1, [x1, #0x138]
    // 0xbae5f8: stur            x1, [fp, #-0x70]
    // 0xbae5fc: cbz             w3, #0xbae60c
    // 0xbae600: r3 = Instance_Color
    //     0xbae600: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbae604: ldr             x3, [x3, #0x858]
    // 0xbae608: b               #0xbae614
    // 0xbae60c: r3 = Instance_Color
    //     0xbae60c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbae610: ldr             x3, [x3, #0x50]
    // 0xbae614: stur            x3, [fp, #-0x68]
    // 0xbae618: r0 = Icon()
    //     0xbae618: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbae61c: mov             x1, x0
    // 0xbae620: ldur            x0, [fp, #-0x70]
    // 0xbae624: StoreField: r1->field_b = r0
    //     0xbae624: stur            w0, [x1, #0xb]
    // 0xbae628: ldur            x0, [fp, #-0x68]
    // 0xbae62c: StoreField: r1->field_23 = r0
    //     0xbae62c: stur            w0, [x1, #0x23]
    // 0xbae630: b               #0xbae638
    // 0xbae634: r1 = Null
    //     0xbae634: mov             x1, NULL
    // 0xbae638: ldur            x2, [fp, #-8]
    // 0xbae63c: ldur            x0, [fp, #-0x20]
    // 0xbae640: ldur            x16, [fp, #-0x58]
    // 0xbae644: r30 = "House No./ Building Name*"
    //     0xbae644: add             lr, PP, #0x54, lsl #12  ; [pp+0x54080] "House No./ Building Name*"
    //     0xbae648: ldr             lr, [lr, #0x80]
    // 0xbae64c: stp             lr, x16, [SP, #0x20]
    // 0xbae650: ldur            x16, [fp, #-0x60]
    // 0xbae654: r30 = 4
    //     0xbae654: movz            lr, #0x4
    // 0xbae658: stp             lr, x16, [SP, #0x10]
    // 0xbae65c: ldur            x16, [fp, #-0x78]
    // 0xbae660: stp             x1, x16, [SP]
    // 0xbae664: ldur            x1, [fp, #-0x50]
    // 0xbae668: r4 = const [0, 0x7, 0x6, 0x1, errorMaxLines, 0x4, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x6, null]
    //     0xbae668: add             x4, PP, #0x54, lsl #12  ; [pp+0x54088] List(17) [0, 0x7, 0x6, 0x1, "errorMaxLines", 0x4, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x6, Null]
    //     0xbae66c: ldr             x4, [x4, #0x88]
    // 0xbae670: r0 = copyWith()
    //     0xbae670: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbae674: ldur            x2, [fp, #-8]
    // 0xbae678: r1 = Function '_validateHouseNumber@1659256992':.
    //     0xbae678: add             x1, PP, #0x54, lsl #12  ; [pp+0x54890] AnonymousClosure: (0xbb10c0), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateHouseNumber (0xa025b4)
    //     0xbae67c: ldr             x1, [x1, #0x890]
    // 0xbae680: stur            x0, [fp, #-0x50]
    // 0xbae684: r0 = AllocateClosure()
    //     0xbae684: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbae688: ldur            x2, [fp, #-0x18]
    // 0xbae68c: r1 = Function '<anonymous closure>':.
    //     0xbae68c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54898] AnonymousClosure: (0xbb1048), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbae690: ldr             x1, [x1, #0x898]
    // 0xbae694: stur            x0, [fp, #-0x58]
    // 0xbae698: r0 = AllocateClosure()
    //     0xbae698: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbae69c: r1 = <String>
    //     0xbae69c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbae6a0: stur            x0, [fp, #-0x60]
    // 0xbae6a4: r0 = TextFormField()
    //     0xbae6a4: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbae6a8: stur            x0, [fp, #-0x68]
    // 0xbae6ac: ldur            x16, [fp, #-0x58]
    // 0xbae6b0: r30 = Instance_AutovalidateMode
    //     0xbae6b0: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbae6b4: ldr             lr, [lr, #0x7e8]
    // 0xbae6b8: stp             lr, x16, [SP, #0x40]
    // 0xbae6bc: r16 = true
    //     0xbae6bc: add             x16, NULL, #0x20  ; true
    // 0xbae6c0: r30 = true
    //     0xbae6c0: add             lr, NULL, #0x20  ; true
    // 0xbae6c4: stp             lr, x16, [SP, #0x30]
    // 0xbae6c8: ldur            x16, [fp, #-0x40]
    // 0xbae6cc: r30 = Instance_TextInputType
    //     0xbae6cc: add             lr, PP, #0x33, lsl #12  ; [pp+0x33bb8] Obj!TextInputType@d55b41
    //     0xbae6d0: ldr             lr, [lr, #0xbb8]
    // 0xbae6d4: stp             lr, x16, [SP, #0x20]
    // 0xbae6d8: ldur            x16, [fp, #-0x30]
    // 0xbae6dc: r30 = 2
    //     0xbae6dc: movz            lr, #0x2
    // 0xbae6e0: stp             lr, x16, [SP, #0x10]
    // 0xbae6e4: ldur            x16, [fp, #-0x48]
    // 0xbae6e8: ldur            lr, [fp, #-0x60]
    // 0xbae6ec: stp             lr, x16, [SP]
    // 0xbae6f0: mov             x1, x0
    // 0xbae6f4: ldur            x2, [fp, #-0x50]
    // 0xbae6f8: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, onChanged, 0xb, style, 0xa, validator, 0x2, null]
    //     0xbae6f8: add             x4, PP, #0x54, lsl #12  ; [pp+0x540a0] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "onChanged", 0xb, "style", 0xa, "validator", 0x2, Null]
    //     0xbae6fc: ldr             x4, [x4, #0xa0]
    // 0xbae700: r0 = TextFormField()
    //     0xbae700: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbae704: r0 = Form()
    //     0xbae704: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbae708: mov             x1, x0
    // 0xbae70c: ldur            x0, [fp, #-0x68]
    // 0xbae710: stur            x1, [fp, #-0x30]
    // 0xbae714: StoreField: r1->field_b = r0
    //     0xbae714: stur            w0, [x1, #0xb]
    // 0xbae718: r0 = Instance_AutovalidateMode
    //     0xbae718: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbae71c: ldr             x0, [x0, #0x800]
    // 0xbae720: StoreField: r1->field_23 = r0
    //     0xbae720: stur            w0, [x1, #0x23]
    // 0xbae724: ldur            x2, [fp, #-0x20]
    // 0xbae728: StoreField: r1->field_7 = r2
    //     0xbae728: stur            w2, [x1, #7]
    // 0xbae72c: r0 = Padding()
    //     0xbae72c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbae730: mov             x4, x0
    // 0xbae734: r3 = Instance_EdgeInsets
    //     0xbae734: add             x3, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbae738: ldr             x3, [x3, #0x858]
    // 0xbae73c: stur            x4, [fp, #-0x40]
    // 0xbae740: StoreField: r4->field_f = r3
    //     0xbae740: stur            w3, [x4, #0xf]
    // 0xbae744: ldur            x0, [fp, #-0x30]
    // 0xbae748: StoreField: r4->field_b = r0
    //     0xbae748: stur            w0, [x4, #0xb]
    // 0xbae74c: ldur            x5, [fp, #-8]
    // 0xbae750: LoadField: r6 = r5->field_2f
    //     0xbae750: ldur            w6, [x5, #0x2f]
    // 0xbae754: DecompressPointer r6
    //     0xbae754: add             x6, x6, HEAP, lsl #32
    // 0xbae758: stur            x6, [fp, #-0x20]
    // 0xbae75c: LoadField: r0 = r5->field_b
    //     0xbae75c: ldur            w0, [x5, #0xb]
    // 0xbae760: DecompressPointer r0
    //     0xbae760: add             x0, x0, HEAP, lsl #32
    // 0xbae764: cmp             w0, NULL
    // 0xbae768: b.eq            #0xbb070c
    // 0xbae76c: LoadField: r1 = r0->field_b
    //     0xbae76c: ldur            w1, [x0, #0xb]
    // 0xbae770: DecompressPointer r1
    //     0xbae770: add             x1, x1, HEAP, lsl #32
    // 0xbae774: LoadField: r0 = r1->field_f
    //     0xbae774: ldur            w0, [x1, #0xf]
    // 0xbae778: DecompressPointer r0
    //     0xbae778: add             x0, x0, HEAP, lsl #32
    // 0xbae77c: cmp             w0, NULL
    // 0xbae780: b.ne            #0xbae78c
    // 0xbae784: r0 = Null
    //     0xbae784: mov             x0, NULL
    // 0xbae788: b               #0xbae7b8
    // 0xbae78c: r1 = LoadClassIdInstr(r0)
    //     0xbae78c: ldur            x1, [x0, #-1]
    //     0xbae790: ubfx            x1, x1, #0xc, #0x14
    // 0xbae794: mov             x16, x0
    // 0xbae798: mov             x0, x1
    // 0xbae79c: mov             x1, x16
    // 0xbae7a0: r2 = "landmark"
    //     0xbae7a0: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xbae7a4: ldr             x2, [x2, #0x930]
    // 0xbae7a8: r0 = GDT[cid_x0 + 0xe437]()
    //     0xbae7a8: movz            x17, #0xe437
    //     0xbae7ac: add             lr, x0, x17
    //     0xbae7b0: ldr             lr, [x21, lr, lsl #3]
    //     0xbae7b4: blr             lr
    // 0xbae7b8: cmp             w0, NULL
    // 0xbae7bc: b.eq            #0xbae7cc
    // 0xbae7c0: tbnz            w0, #4, #0xbae7cc
    // 0xbae7c4: r0 = 250
    //     0xbae7c4: movz            x0, #0xfa
    // 0xbae7c8: b               #0xbae7d0
    // 0xbae7cc: r0 = 370
    //     0xbae7cc: movz            x0, #0x172
    // 0xbae7d0: ldur            x2, [fp, #-8]
    // 0xbae7d4: lsl             x1, x0, #1
    // 0xbae7d8: stur            x1, [fp, #-0x30]
    // 0xbae7dc: r0 = LengthLimitingTextInputFormatter()
    //     0xbae7dc: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbae7e0: mov             x1, x0
    // 0xbae7e4: ldur            x0, [fp, #-0x30]
    // 0xbae7e8: stur            x1, [fp, #-0x48]
    // 0xbae7ec: StoreField: r1->field_7 = r0
    //     0xbae7ec: stur            w0, [x1, #7]
    // 0xbae7f0: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbae7f0: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbae7f4: ldr             x16, [x16, #0xa8]
    // 0xbae7f8: stp             x16, NULL, [SP, #0x20]
    // 0xbae7fc: r16 = false
    //     0xbae7fc: add             x16, NULL, #0x30  ; false
    // 0xbae800: r30 = true
    //     0xbae800: add             lr, NULL, #0x20  ; true
    // 0xbae804: stp             lr, x16, [SP, #0x10]
    // 0xbae808: r16 = false
    //     0xbae808: add             x16, NULL, #0x30  ; false
    // 0xbae80c: r30 = false
    //     0xbae80c: add             lr, NULL, #0x30  ; false
    // 0xbae810: stp             lr, x16, [SP]
    // 0xbae814: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbae814: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbae818: r0 = _RegExp()
    //     0xbae818: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbae81c: stur            x0, [fp, #-0x30]
    // 0xbae820: r0 = FilteringTextInputFormatter()
    //     0xbae820: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbae824: mov             x3, x0
    // 0xbae828: ldur            x0, [fp, #-0x30]
    // 0xbae82c: stur            x3, [fp, #-0x50]
    // 0xbae830: StoreField: r3->field_b = r0
    //     0xbae830: stur            w0, [x3, #0xb]
    // 0xbae834: r0 = true
    //     0xbae834: add             x0, NULL, #0x20  ; true
    // 0xbae838: StoreField: r3->field_7 = r0
    //     0xbae838: stur            w0, [x3, #7]
    // 0xbae83c: r4 = ""
    //     0xbae83c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbae840: StoreField: r3->field_f = r4
    //     0xbae840: stur            w4, [x3, #0xf]
    // 0xbae844: r1 = Null
    //     0xbae844: mov             x1, NULL
    // 0xbae848: r2 = 4
    //     0xbae848: movz            x2, #0x4
    // 0xbae84c: r0 = AllocateArray()
    //     0xbae84c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbae850: mov             x2, x0
    // 0xbae854: ldur            x0, [fp, #-0x48]
    // 0xbae858: stur            x2, [fp, #-0x30]
    // 0xbae85c: StoreField: r2->field_f = r0
    //     0xbae85c: stur            w0, [x2, #0xf]
    // 0xbae860: ldur            x0, [fp, #-0x50]
    // 0xbae864: StoreField: r2->field_13 = r0
    //     0xbae864: stur            w0, [x2, #0x13]
    // 0xbae868: r1 = <TextInputFormatter>
    //     0xbae868: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbae86c: ldr             x1, [x1, #0x7b0]
    // 0xbae870: r0 = AllocateGrowableArray()
    //     0xbae870: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbae874: mov             x2, x0
    // 0xbae878: ldur            x0, [fp, #-0x30]
    // 0xbae87c: stur            x2, [fp, #-0x48]
    // 0xbae880: StoreField: r2->field_f = r0
    //     0xbae880: stur            w0, [x2, #0xf]
    // 0xbae884: r0 = 4
    //     0xbae884: movz            x0, #0x4
    // 0xbae888: StoreField: r2->field_b = r0
    //     0xbae888: stur            w0, [x2, #0xb]
    // 0xbae88c: ldur            x1, [fp, #-0x10]
    // 0xbae890: r0 = of()
    //     0xbae890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae894: LoadField: r1 = r0->field_87
    //     0xbae894: ldur            w1, [x0, #0x87]
    // 0xbae898: DecompressPointer r1
    //     0xbae898: add             x1, x1, HEAP, lsl #32
    // 0xbae89c: LoadField: r0 = r1->field_2b
    //     0xbae89c: ldur            w0, [x1, #0x2b]
    // 0xbae8a0: DecompressPointer r0
    //     0xbae8a0: add             x0, x0, HEAP, lsl #32
    // 0xbae8a4: ldur            x1, [fp, #-0x10]
    // 0xbae8a8: stur            x0, [fp, #-0x30]
    // 0xbae8ac: r0 = of()
    //     0xbae8ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae8b0: LoadField: r1 = r0->field_5b
    //     0xbae8b0: ldur            w1, [x0, #0x5b]
    // 0xbae8b4: DecompressPointer r1
    //     0xbae8b4: add             x1, x1, HEAP, lsl #32
    // 0xbae8b8: r16 = 14.000000
    //     0xbae8b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbae8bc: ldr             x16, [x16, #0x1d8]
    // 0xbae8c0: stp             x1, x16, [SP]
    // 0xbae8c4: ldur            x1, [fp, #-0x30]
    // 0xbae8c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae8c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae8cc: ldr             x4, [x4, #0xaa0]
    // 0xbae8d0: r0 = copyWith()
    //     0xbae8d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae8d4: ldur            x2, [fp, #-8]
    // 0xbae8d8: stur            x0, [fp, #-0x50]
    // 0xbae8dc: LoadField: r3 = r2->field_47
    //     0xbae8dc: ldur            w3, [x2, #0x47]
    // 0xbae8e0: DecompressPointer r3
    //     0xbae8e0: add             x3, x3, HEAP, lsl #32
    // 0xbae8e4: ldur            x1, [fp, #-0x10]
    // 0xbae8e8: stur            x3, [fp, #-0x30]
    // 0xbae8ec: r0 = getTextFormFieldInputDecoration()
    //     0xbae8ec: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbae8f0: ldur            x1, [fp, #-0x10]
    // 0xbae8f4: stur            x0, [fp, #-0x58]
    // 0xbae8f8: r0 = of()
    //     0xbae8f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae8fc: LoadField: r1 = r0->field_5b
    //     0xbae8fc: ldur            w1, [x0, #0x5b]
    // 0xbae900: DecompressPointer r1
    //     0xbae900: add             x1, x1, HEAP, lsl #32
    // 0xbae904: stur            x1, [fp, #-0x60]
    // 0xbae908: r0 = BorderSide()
    //     0xbae908: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbae90c: mov             x1, x0
    // 0xbae910: ldur            x0, [fp, #-0x60]
    // 0xbae914: stur            x1, [fp, #-0x68]
    // 0xbae918: StoreField: r1->field_7 = r0
    //     0xbae918: stur            w0, [x1, #7]
    // 0xbae91c: d0 = 1.000000
    //     0xbae91c: fmov            d0, #1.00000000
    // 0xbae920: StoreField: r1->field_b = d0
    //     0xbae920: stur            d0, [x1, #0xb]
    // 0xbae924: r0 = Instance_BorderStyle
    //     0xbae924: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbae928: ldr             x0, [x0, #0xf68]
    // 0xbae92c: StoreField: r1->field_13 = r0
    //     0xbae92c: stur            w0, [x1, #0x13]
    // 0xbae930: d1 = -1.000000
    //     0xbae930: fmov            d1, #-1.00000000
    // 0xbae934: ArrayStore: r1[0] = d1  ; List_8
    //     0xbae934: stur            d1, [x1, #0x17]
    // 0xbae938: r0 = OutlineInputBorder()
    //     0xbae938: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbae93c: mov             x2, x0
    // 0xbae940: r0 = Instance_BorderRadius
    //     0xbae940: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbae944: ldr             x0, [x0, #0xf70]
    // 0xbae948: stur            x2, [fp, #-0x60]
    // 0xbae94c: StoreField: r2->field_13 = r0
    //     0xbae94c: stur            w0, [x2, #0x13]
    // 0xbae950: d0 = 4.000000
    //     0xbae950: fmov            d0, #4.00000000
    // 0xbae954: StoreField: r2->field_b = d0
    //     0xbae954: stur            d0, [x2, #0xb]
    // 0xbae958: ldur            x1, [fp, #-0x68]
    // 0xbae95c: StoreField: r2->field_7 = r1
    //     0xbae95c: stur            w1, [x2, #7]
    // 0xbae960: ldur            x1, [fp, #-0x10]
    // 0xbae964: r0 = of()
    //     0xbae964: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae968: LoadField: r1 = r0->field_87
    //     0xbae968: ldur            w1, [x0, #0x87]
    // 0xbae96c: DecompressPointer r1
    //     0xbae96c: add             x1, x1, HEAP, lsl #32
    // 0xbae970: LoadField: r0 = r1->field_2b
    //     0xbae970: ldur            w0, [x1, #0x2b]
    // 0xbae974: DecompressPointer r0
    //     0xbae974: add             x0, x0, HEAP, lsl #32
    // 0xbae978: stur            x0, [fp, #-0x68]
    // 0xbae97c: r1 = Instance_Color
    //     0xbae97c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbae980: d0 = 0.400000
    //     0xbae980: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbae984: r0 = withOpacity()
    //     0xbae984: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbae988: r16 = 14.000000
    //     0xbae988: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbae98c: ldr             x16, [x16, #0x1d8]
    // 0xbae990: stp             x0, x16, [SP]
    // 0xbae994: ldur            x1, [fp, #-0x68]
    // 0xbae998: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae998: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae99c: ldr             x4, [x4, #0xaa0]
    // 0xbae9a0: r0 = copyWith()
    //     0xbae9a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae9a4: ldur            x1, [fp, #-0x10]
    // 0xbae9a8: stur            x0, [fp, #-0x68]
    // 0xbae9ac: r0 = of()
    //     0xbae9ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbae9b0: LoadField: r1 = r0->field_87
    //     0xbae9b0: ldur            w1, [x0, #0x87]
    // 0xbae9b4: DecompressPointer r1
    //     0xbae9b4: add             x1, x1, HEAP, lsl #32
    // 0xbae9b8: LoadField: r0 = r1->field_2b
    //     0xbae9b8: ldur            w0, [x1, #0x2b]
    // 0xbae9bc: DecompressPointer r0
    //     0xbae9bc: add             x0, x0, HEAP, lsl #32
    // 0xbae9c0: r16 = 12.000000
    //     0xbae9c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbae9c4: ldr             x16, [x16, #0x9e8]
    // 0xbae9c8: r30 = Instance_MaterialColor
    //     0xbae9c8: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbae9cc: ldr             lr, [lr, #0x180]
    // 0xbae9d0: stp             lr, x16, [SP]
    // 0xbae9d4: mov             x1, x0
    // 0xbae9d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbae9d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbae9dc: ldr             x4, [x4, #0xaa0]
    // 0xbae9e0: r0 = copyWith()
    //     0xbae9e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbae9e4: mov             x1, x0
    // 0xbae9e8: ldur            x2, [fp, #-8]
    // 0xbae9ec: stur            x1, [fp, #-0x70]
    // 0xbae9f0: LoadField: r0 = r2->field_57
    //     0xbae9f0: ldur            w0, [x2, #0x57]
    // 0xbae9f4: DecompressPointer r0
    //     0xbae9f4: add             x0, x0, HEAP, lsl #32
    // 0xbae9f8: tbnz            w0, #4, #0xbaeb24
    // 0xbae9fc: LoadField: r0 = r2->field_47
    //     0xbae9fc: ldur            w0, [x2, #0x47]
    // 0xbaea00: DecompressPointer r0
    //     0xbaea00: add             x0, x0, HEAP, lsl #32
    // 0xbaea04: LoadField: r3 = r0->field_27
    //     0xbaea04: ldur            w3, [x0, #0x27]
    // 0xbaea08: DecompressPointer r3
    //     0xbaea08: add             x3, x3, HEAP, lsl #32
    // 0xbaea0c: LoadField: r0 = r3->field_7
    //     0xbaea0c: ldur            w0, [x3, #7]
    // 0xbaea10: DecompressPointer r0
    //     0xbaea10: add             x0, x0, HEAP, lsl #32
    // 0xbaea14: r3 = LoadClassIdInstr(r0)
    //     0xbaea14: ldur            x3, [x0, #-1]
    //     0xbaea18: ubfx            x3, x3, #0xc, #0x14
    // 0xbaea1c: r16 = ""
    //     0xbaea1c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaea20: stp             x16, x0, [SP]
    // 0xbaea24: mov             x0, x3
    // 0xbaea28: mov             lr, x0
    // 0xbaea2c: ldr             lr, [x21, lr, lsl #3]
    // 0xbaea30: blr             lr
    // 0xbaea34: tbz             w0, #4, #0xbaea70
    // 0xbaea38: ldur            x2, [fp, #-8]
    // 0xbaea3c: LoadField: r0 = r2->field_47
    //     0xbaea3c: ldur            w0, [x2, #0x47]
    // 0xbaea40: DecompressPointer r0
    //     0xbaea40: add             x0, x0, HEAP, lsl #32
    // 0xbaea44: LoadField: r1 = r0->field_27
    //     0xbaea44: ldur            w1, [x0, #0x27]
    // 0xbaea48: DecompressPointer r1
    //     0xbaea48: add             x1, x1, HEAP, lsl #32
    // 0xbaea4c: LoadField: r0 = r1->field_7
    //     0xbaea4c: ldur            w0, [x1, #7]
    // 0xbaea50: DecompressPointer r0
    //     0xbaea50: add             x0, x0, HEAP, lsl #32
    // 0xbaea54: LoadField: r1 = r0->field_7
    //     0xbaea54: ldur            w1, [x0, #7]
    // 0xbaea58: r0 = LoadInt32Instr(r1)
    //     0xbaea58: sbfx            x0, x1, #1, #0x1f
    // 0xbaea5c: cmp             x0, #0x14
    // 0xbaea60: b.lt            #0xbaea74
    // 0xbaea64: r1 = Instance_IconData
    //     0xbaea64: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbaea68: ldr             x1, [x1, #0x130]
    // 0xbaea6c: b               #0xbaea7c
    // 0xbaea70: ldur            x2, [fp, #-8]
    // 0xbaea74: r1 = Instance_IconData
    //     0xbaea74: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbaea78: ldr             x1, [x1, #0x138]
    // 0xbaea7c: stur            x1, [fp, #-0x78]
    // 0xbaea80: LoadField: r0 = r2->field_47
    //     0xbaea80: ldur            w0, [x2, #0x47]
    // 0xbaea84: DecompressPointer r0
    //     0xbaea84: add             x0, x0, HEAP, lsl #32
    // 0xbaea88: LoadField: r3 = r0->field_27
    //     0xbaea88: ldur            w3, [x0, #0x27]
    // 0xbaea8c: DecompressPointer r3
    //     0xbaea8c: add             x3, x3, HEAP, lsl #32
    // 0xbaea90: LoadField: r0 = r3->field_7
    //     0xbaea90: ldur            w0, [x3, #7]
    // 0xbaea94: DecompressPointer r0
    //     0xbaea94: add             x0, x0, HEAP, lsl #32
    // 0xbaea98: r3 = LoadClassIdInstr(r0)
    //     0xbaea98: ldur            x3, [x0, #-1]
    //     0xbaea9c: ubfx            x3, x3, #0xc, #0x14
    // 0xbaeaa0: r16 = ""
    //     0xbaeaa0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaeaa4: stp             x16, x0, [SP]
    // 0xbaeaa8: mov             x0, x3
    // 0xbaeaac: mov             lr, x0
    // 0xbaeab0: ldr             lr, [x21, lr, lsl #3]
    // 0xbaeab4: blr             lr
    // 0xbaeab8: tbz             w0, #4, #0xbaeaf4
    // 0xbaeabc: ldur            x2, [fp, #-8]
    // 0xbaeac0: LoadField: r0 = r2->field_47
    //     0xbaeac0: ldur            w0, [x2, #0x47]
    // 0xbaeac4: DecompressPointer r0
    //     0xbaeac4: add             x0, x0, HEAP, lsl #32
    // 0xbaeac8: LoadField: r1 = r0->field_27
    //     0xbaeac8: ldur            w1, [x0, #0x27]
    // 0xbaeacc: DecompressPointer r1
    //     0xbaeacc: add             x1, x1, HEAP, lsl #32
    // 0xbaead0: LoadField: r0 = r1->field_7
    //     0xbaead0: ldur            w0, [x1, #7]
    // 0xbaead4: DecompressPointer r0
    //     0xbaead4: add             x0, x0, HEAP, lsl #32
    // 0xbaead8: LoadField: r1 = r0->field_7
    //     0xbaead8: ldur            w1, [x0, #7]
    // 0xbaeadc: r0 = LoadInt32Instr(r1)
    //     0xbaeadc: sbfx            x0, x1, #1, #0x1f
    // 0xbaeae0: cmp             x0, #0x14
    // 0xbaeae4: b.lt            #0xbaeaf8
    // 0xbaeae8: r1 = Instance_Color
    //     0xbaeae8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaeaec: ldr             x1, [x1, #0x858]
    // 0xbaeaf0: b               #0xbaeb00
    // 0xbaeaf4: ldur            x2, [fp, #-8]
    // 0xbaeaf8: r1 = Instance_Color
    //     0xbaeaf8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbaeafc: ldr             x1, [x1, #0x50]
    // 0xbaeb00: ldur            x0, [fp, #-0x78]
    // 0xbaeb04: stur            x1, [fp, #-0x80]
    // 0xbaeb08: r0 = Icon()
    //     0xbaeb08: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbaeb0c: mov             x1, x0
    // 0xbaeb10: ldur            x0, [fp, #-0x78]
    // 0xbaeb14: StoreField: r1->field_b = r0
    //     0xbaeb14: stur            w0, [x1, #0xb]
    // 0xbaeb18: ldur            x0, [fp, #-0x80]
    // 0xbaeb1c: StoreField: r1->field_23 = r0
    //     0xbaeb1c: stur            w0, [x1, #0x23]
    // 0xbaeb20: b               #0xbaeb28
    // 0xbaeb24: r1 = Null
    //     0xbaeb24: mov             x1, NULL
    // 0xbaeb28: ldur            x2, [fp, #-8]
    // 0xbaeb2c: ldur            x0, [fp, #-0x20]
    // 0xbaeb30: ldur            x16, [fp, #-0x60]
    // 0xbaeb34: r30 = "Road Name / Area / Colony*"
    //     0xbaeb34: add             lr, PP, #0x54, lsl #12  ; [pp+0x540b0] "Road Name / Area / Colony*"
    //     0xbaeb38: ldr             lr, [lr, #0xb0]
    // 0xbaeb3c: stp             lr, x16, [SP, #0x18]
    // 0xbaeb40: ldur            x16, [fp, #-0x68]
    // 0xbaeb44: ldur            lr, [fp, #-0x70]
    // 0xbaeb48: stp             lr, x16, [SP, #8]
    // 0xbaeb4c: str             x1, [SP]
    // 0xbaeb50: ldur            x1, [fp, #-0x58]
    // 0xbaeb54: r4 = const [0, 0x6, 0x5, 0x1, errorStyle, 0x4, focusedBorder, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x5, null]
    //     0xbaeb54: add             x4, PP, #0x54, lsl #12  ; [pp+0x540b8] List(15) [0, 0x6, 0x5, 0x1, "errorStyle", 0x4, "focusedBorder", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x5, Null]
    //     0xbaeb58: ldr             x4, [x4, #0xb8]
    // 0xbaeb5c: r0 = copyWith()
    //     0xbaeb5c: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbaeb60: ldur            x2, [fp, #-8]
    // 0xbaeb64: r1 = Function '_validateAddress@1659256992':.
    //     0xbaeb64: add             x1, PP, #0x54, lsl #12  ; [pp+0x548a0] AnonymousClosure: (0xbb100c), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress (0xa023b0)
    //     0xbaeb68: ldr             x1, [x1, #0x8a0]
    // 0xbaeb6c: stur            x0, [fp, #-0x58]
    // 0xbaeb70: r0 = AllocateClosure()
    //     0xbaeb70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaeb74: ldur            x2, [fp, #-0x18]
    // 0xbaeb78: r1 = Function '<anonymous closure>':.
    //     0xbaeb78: add             x1, PP, #0x54, lsl #12  ; [pp+0x548a8] AnonymousClosure: (0xbb0f94), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbaeb7c: ldr             x1, [x1, #0x8a8]
    // 0xbaeb80: stur            x0, [fp, #-0x60]
    // 0xbaeb84: r0 = AllocateClosure()
    //     0xbaeb84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaeb88: r1 = <String>
    //     0xbaeb88: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbaeb8c: stur            x0, [fp, #-0x68]
    // 0xbaeb90: r0 = TextFormField()
    //     0xbaeb90: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbaeb94: stur            x0, [fp, #-0x70]
    // 0xbaeb98: ldur            x16, [fp, #-0x60]
    // 0xbaeb9c: r30 = Instance_AutovalidateMode
    //     0xbaeb9c: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbaeba0: ldr             lr, [lr, #0x7e8]
    // 0xbaeba4: stp             lr, x16, [SP, #0x48]
    // 0xbaeba8: r16 = true
    //     0xbaeba8: add             x16, NULL, #0x20  ; true
    // 0xbaebac: r30 = true
    //     0xbaebac: add             lr, NULL, #0x20  ; true
    // 0xbaebb0: stp             lr, x16, [SP, #0x38]
    // 0xbaebb4: ldur            x16, [fp, #-0x48]
    // 0xbaebb8: r30 = Instance_TextInputType
    //     0xbaebb8: add             lr, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0xbaebbc: ldr             lr, [lr, #0x7f0]
    // 0xbaebc0: stp             lr, x16, [SP, #0x28]
    // 0xbaebc4: r16 = 2
    //     0xbaebc4: movz            x16, #0x2
    // 0xbaebc8: r30 = 4
    //     0xbaebc8: movz            lr, #0x4
    // 0xbaebcc: stp             lr, x16, [SP, #0x18]
    // 0xbaebd0: ldur            x16, [fp, #-0x50]
    // 0xbaebd4: ldur            lr, [fp, #-0x30]
    // 0xbaebd8: stp             lr, x16, [SP, #8]
    // 0xbaebdc: ldur            x16, [fp, #-0x68]
    // 0xbaebe0: str             x16, [SP]
    // 0xbaebe4: mov             x1, x0
    // 0xbaebe8: ldur            x2, [fp, #-0x58]
    // 0xbaebec: r4 = const [0, 0xd, 0xb, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0xb, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, minLines, 0x8, onChanged, 0xc, style, 0xa, validator, 0x2, null]
    //     0xbaebec: add             x4, PP, #0x54, lsl #12  ; [pp+0x548b0] List(27) [0, 0xd, 0xb, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0xb, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "minLines", 0x8, "onChanged", 0xc, "style", 0xa, "validator", 0x2, Null]
    //     0xbaebf0: ldr             x4, [x4, #0x8b0]
    // 0xbaebf4: r0 = TextFormField()
    //     0xbaebf4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbaebf8: r0 = Form()
    //     0xbaebf8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbaebfc: mov             x1, x0
    // 0xbaec00: ldur            x0, [fp, #-0x70]
    // 0xbaec04: stur            x1, [fp, #-0x30]
    // 0xbaec08: StoreField: r1->field_b = r0
    //     0xbaec08: stur            w0, [x1, #0xb]
    // 0xbaec0c: r0 = Instance_AutovalidateMode
    //     0xbaec0c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbaec10: ldr             x0, [x0, #0x800]
    // 0xbaec14: StoreField: r1->field_23 = r0
    //     0xbaec14: stur            w0, [x1, #0x23]
    // 0xbaec18: ldur            x2, [fp, #-0x20]
    // 0xbaec1c: StoreField: r1->field_7 = r2
    //     0xbaec1c: stur            w2, [x1, #7]
    // 0xbaec20: r0 = Padding()
    //     0xbaec20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbaec24: mov             x1, x0
    // 0xbaec28: r0 = Instance_EdgeInsets
    //     0xbaec28: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbaec2c: ldr             x0, [x0, #0x858]
    // 0xbaec30: stur            x1, [fp, #-0x48]
    // 0xbaec34: StoreField: r1->field_f = r0
    //     0xbaec34: stur            w0, [x1, #0xf]
    // 0xbaec38: ldur            x2, [fp, #-0x30]
    // 0xbaec3c: StoreField: r1->field_b = r2
    //     0xbaec3c: stur            w2, [x1, #0xb]
    // 0xbaec40: ldur            x2, [fp, #-8]
    // 0xbaec44: LoadField: r3 = r2->field_1f
    //     0xbaec44: ldur            w3, [x2, #0x1f]
    // 0xbaec48: DecompressPointer r3
    //     0xbaec48: add             x3, x3, HEAP, lsl #32
    // 0xbaec4c: stur            x3, [fp, #-0x20]
    // 0xbaec50: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbaec50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbaec54: ldr             x0, [x0, #0x1530]
    //     0xbaec58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbaec5c: cmp             w0, w16
    //     0xbaec60: b.ne            #0xbaec70
    //     0xbaec64: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbaec68: ldr             x2, [x2, #0x120]
    //     0xbaec6c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbaec70: stur            x0, [fp, #-0x30]
    // 0xbaec74: r16 = "[0-9]"
    //     0xbaec74: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xbaec78: ldr             x16, [x16, #0x128]
    // 0xbaec7c: stp             x16, NULL, [SP, #0x20]
    // 0xbaec80: r16 = false
    //     0xbaec80: add             x16, NULL, #0x30  ; false
    // 0xbaec84: r30 = true
    //     0xbaec84: add             lr, NULL, #0x20  ; true
    // 0xbaec88: stp             lr, x16, [SP, #0x10]
    // 0xbaec8c: r16 = false
    //     0xbaec8c: add             x16, NULL, #0x30  ; false
    // 0xbaec90: r30 = false
    //     0xbaec90: add             lr, NULL, #0x30  ; false
    // 0xbaec94: stp             lr, x16, [SP]
    // 0xbaec98: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbaec98: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbaec9c: r0 = _RegExp()
    //     0xbaec9c: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbaeca0: stur            x0, [fp, #-0x50]
    // 0xbaeca4: r0 = FilteringTextInputFormatter()
    //     0xbaeca4: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbaeca8: mov             x1, x0
    // 0xbaecac: ldur            x0, [fp, #-0x50]
    // 0xbaecb0: stur            x1, [fp, #-0x58]
    // 0xbaecb4: StoreField: r1->field_b = r0
    //     0xbaecb4: stur            w0, [x1, #0xb]
    // 0xbaecb8: r0 = true
    //     0xbaecb8: add             x0, NULL, #0x20  ; true
    // 0xbaecbc: StoreField: r1->field_7 = r0
    //     0xbaecbc: stur            w0, [x1, #7]
    // 0xbaecc0: r2 = ""
    //     0xbaecc0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaecc4: StoreField: r1->field_f = r2
    //     0xbaecc4: stur            w2, [x1, #0xf]
    // 0xbaecc8: r0 = LengthLimitingTextInputFormatter()
    //     0xbaecc8: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbaeccc: mov             x3, x0
    // 0xbaecd0: r0 = 12
    //     0xbaecd0: movz            x0, #0xc
    // 0xbaecd4: stur            x3, [fp, #-0x50]
    // 0xbaecd8: StoreField: r3->field_7 = r0
    //     0xbaecd8: stur            w0, [x3, #7]
    // 0xbaecdc: r1 = Null
    //     0xbaecdc: mov             x1, NULL
    // 0xbaece0: r2 = 6
    //     0xbaece0: movz            x2, #0x6
    // 0xbaece4: r0 = AllocateArray()
    //     0xbaece4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaece8: mov             x2, x0
    // 0xbaecec: ldur            x0, [fp, #-0x30]
    // 0xbaecf0: stur            x2, [fp, #-0x60]
    // 0xbaecf4: StoreField: r2->field_f = r0
    //     0xbaecf4: stur            w0, [x2, #0xf]
    // 0xbaecf8: ldur            x1, [fp, #-0x58]
    // 0xbaecfc: StoreField: r2->field_13 = r1
    //     0xbaecfc: stur            w1, [x2, #0x13]
    // 0xbaed00: ldur            x1, [fp, #-0x50]
    // 0xbaed04: ArrayStore: r2[0] = r1  ; List_4
    //     0xbaed04: stur            w1, [x2, #0x17]
    // 0xbaed08: r1 = <TextInputFormatter>
    //     0xbaed08: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbaed0c: ldr             x1, [x1, #0x7b0]
    // 0xbaed10: r0 = AllocateGrowableArray()
    //     0xbaed10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaed14: mov             x2, x0
    // 0xbaed18: ldur            x0, [fp, #-0x60]
    // 0xbaed1c: stur            x2, [fp, #-0x58]
    // 0xbaed20: StoreField: r2->field_f = r0
    //     0xbaed20: stur            w0, [x2, #0xf]
    // 0xbaed24: r0 = 6
    //     0xbaed24: movz            x0, #0x6
    // 0xbaed28: StoreField: r2->field_b = r0
    //     0xbaed28: stur            w0, [x2, #0xb]
    // 0xbaed2c: ldur            x3, [fp, #-8]
    // 0xbaed30: LoadField: r4 = r3->field_3f
    //     0xbaed30: ldur            w4, [x3, #0x3f]
    // 0xbaed34: DecompressPointer r4
    //     0xbaed34: add             x4, x4, HEAP, lsl #32
    // 0xbaed38: ldur            x1, [fp, #-0x10]
    // 0xbaed3c: stur            x4, [fp, #-0x50]
    // 0xbaed40: r0 = of()
    //     0xbaed40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaed44: LoadField: r1 = r0->field_87
    //     0xbaed44: ldur            w1, [x0, #0x87]
    // 0xbaed48: DecompressPointer r1
    //     0xbaed48: add             x1, x1, HEAP, lsl #32
    // 0xbaed4c: LoadField: r0 = r1->field_2b
    //     0xbaed4c: ldur            w0, [x1, #0x2b]
    // 0xbaed50: DecompressPointer r0
    //     0xbaed50: add             x0, x0, HEAP, lsl #32
    // 0xbaed54: ldur            x1, [fp, #-0x10]
    // 0xbaed58: stur            x0, [fp, #-0x60]
    // 0xbaed5c: r0 = of()
    //     0xbaed5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaed60: LoadField: r1 = r0->field_5b
    //     0xbaed60: ldur            w1, [x0, #0x5b]
    // 0xbaed64: DecompressPointer r1
    //     0xbaed64: add             x1, x1, HEAP, lsl #32
    // 0xbaed68: r16 = 14.000000
    //     0xbaed68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaed6c: ldr             x16, [x16, #0x1d8]
    // 0xbaed70: stp             x1, x16, [SP]
    // 0xbaed74: ldur            x1, [fp, #-0x60]
    // 0xbaed78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaed78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaed7c: ldr             x4, [x4, #0xaa0]
    // 0xbaed80: r0 = copyWith()
    //     0xbaed80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaed84: ldur            x1, [fp, #-0x10]
    // 0xbaed88: stur            x0, [fp, #-0x60]
    // 0xbaed8c: r0 = getTextFormFieldInputDecoration()
    //     0xbaed8c: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbaed90: ldur            x1, [fp, #-0x10]
    // 0xbaed94: stur            x0, [fp, #-0x68]
    // 0xbaed98: r0 = of()
    //     0xbaed98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaed9c: LoadField: r1 = r0->field_5b
    //     0xbaed9c: ldur            w1, [x0, #0x5b]
    // 0xbaeda0: DecompressPointer r1
    //     0xbaeda0: add             x1, x1, HEAP, lsl #32
    // 0xbaeda4: stur            x1, [fp, #-0x70]
    // 0xbaeda8: r0 = BorderSide()
    //     0xbaeda8: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbaedac: mov             x1, x0
    // 0xbaedb0: ldur            x0, [fp, #-0x70]
    // 0xbaedb4: stur            x1, [fp, #-0x78]
    // 0xbaedb8: StoreField: r1->field_7 = r0
    //     0xbaedb8: stur            w0, [x1, #7]
    // 0xbaedbc: d0 = 1.000000
    //     0xbaedbc: fmov            d0, #1.00000000
    // 0xbaedc0: StoreField: r1->field_b = d0
    //     0xbaedc0: stur            d0, [x1, #0xb]
    // 0xbaedc4: r0 = Instance_BorderStyle
    //     0xbaedc4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbaedc8: ldr             x0, [x0, #0xf68]
    // 0xbaedcc: StoreField: r1->field_13 = r0
    //     0xbaedcc: stur            w0, [x1, #0x13]
    // 0xbaedd0: d1 = -1.000000
    //     0xbaedd0: fmov            d1, #-1.00000000
    // 0xbaedd4: ArrayStore: r1[0] = d1  ; List_8
    //     0xbaedd4: stur            d1, [x1, #0x17]
    // 0xbaedd8: r0 = OutlineInputBorder()
    //     0xbaedd8: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbaeddc: mov             x2, x0
    // 0xbaede0: r0 = Instance_BorderRadius
    //     0xbaede0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbaede4: ldr             x0, [x0, #0xf70]
    // 0xbaede8: stur            x2, [fp, #-0x70]
    // 0xbaedec: StoreField: r2->field_13 = r0
    //     0xbaedec: stur            w0, [x2, #0x13]
    // 0xbaedf0: d0 = 4.000000
    //     0xbaedf0: fmov            d0, #4.00000000
    // 0xbaedf4: StoreField: r2->field_b = d0
    //     0xbaedf4: stur            d0, [x2, #0xb]
    // 0xbaedf8: ldur            x1, [fp, #-0x78]
    // 0xbaedfc: StoreField: r2->field_7 = r1
    //     0xbaedfc: stur            w1, [x2, #7]
    // 0xbaee00: ldur            x1, [fp, #-0x10]
    // 0xbaee04: r0 = of()
    //     0xbaee04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaee08: LoadField: r1 = r0->field_87
    //     0xbaee08: ldur            w1, [x0, #0x87]
    // 0xbaee0c: DecompressPointer r1
    //     0xbaee0c: add             x1, x1, HEAP, lsl #32
    // 0xbaee10: LoadField: r0 = r1->field_2b
    //     0xbaee10: ldur            w0, [x1, #0x2b]
    // 0xbaee14: DecompressPointer r0
    //     0xbaee14: add             x0, x0, HEAP, lsl #32
    // 0xbaee18: stur            x0, [fp, #-0x78]
    // 0xbaee1c: r1 = Instance_Color
    //     0xbaee1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaee20: d0 = 0.400000
    //     0xbaee20: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbaee24: r0 = withOpacity()
    //     0xbaee24: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaee28: r16 = 14.000000
    //     0xbaee28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaee2c: ldr             x16, [x16, #0x1d8]
    // 0xbaee30: stp             x0, x16, [SP]
    // 0xbaee34: ldur            x1, [fp, #-0x78]
    // 0xbaee38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaee38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaee3c: ldr             x4, [x4, #0xaa0]
    // 0xbaee40: r0 = copyWith()
    //     0xbaee40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaee44: ldur            x1, [fp, #-0x10]
    // 0xbaee48: stur            x0, [fp, #-0x78]
    // 0xbaee4c: r0 = of()
    //     0xbaee4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaee50: LoadField: r1 = r0->field_87
    //     0xbaee50: ldur            w1, [x0, #0x87]
    // 0xbaee54: DecompressPointer r1
    //     0xbaee54: add             x1, x1, HEAP, lsl #32
    // 0xbaee58: LoadField: r0 = r1->field_2b
    //     0xbaee58: ldur            w0, [x1, #0x2b]
    // 0xbaee5c: DecompressPointer r0
    //     0xbaee5c: add             x0, x0, HEAP, lsl #32
    // 0xbaee60: r16 = 12.000000
    //     0xbaee60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbaee64: ldr             x16, [x16, #0x9e8]
    // 0xbaee68: r30 = Instance_MaterialColor
    //     0xbaee68: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbaee6c: ldr             lr, [lr, #0x180]
    // 0xbaee70: stp             lr, x16, [SP]
    // 0xbaee74: mov             x1, x0
    // 0xbaee78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaee78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaee7c: ldr             x4, [x4, #0xaa0]
    // 0xbaee80: r0 = copyWith()
    //     0xbaee80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaee84: ldur            x2, [fp, #-8]
    // 0xbaee88: stur            x0, [fp, #-0x90]
    // 0xbaee8c: LoadField: r1 = r2->field_5b
    //     0xbaee8c: ldur            w1, [x2, #0x5b]
    // 0xbaee90: DecompressPointer r1
    //     0xbaee90: add             x1, x1, HEAP, lsl #32
    // 0xbaee94: tbnz            w1, #4, #0xbaefe8
    // 0xbaee98: LoadField: r1 = r2->field_3f
    //     0xbaee98: ldur            w1, [x2, #0x3f]
    // 0xbaee9c: DecompressPointer r1
    //     0xbaee9c: add             x1, x1, HEAP, lsl #32
    // 0xbaeea0: LoadField: r3 = r1->field_27
    //     0xbaeea0: ldur            w3, [x1, #0x27]
    // 0xbaeea4: DecompressPointer r3
    //     0xbaeea4: add             x3, x3, HEAP, lsl #32
    // 0xbaeea8: LoadField: r1 = r3->field_7
    //     0xbaeea8: ldur            w1, [x3, #7]
    // 0xbaeeac: DecompressPointer r1
    //     0xbaeeac: add             x1, x1, HEAP, lsl #32
    // 0xbaeeb0: LoadField: r3 = r1->field_7
    //     0xbaeeb0: ldur            w3, [x1, #7]
    // 0xbaeeb4: cmp             w3, #0xc
    // 0xbaeeb8: b.ne            #0xbaef34
    // 0xbaeebc: LoadField: r1 = r2->field_b
    //     0xbaeebc: ldur            w1, [x2, #0xb]
    // 0xbaeec0: DecompressPointer r1
    //     0xbaeec0: add             x1, x1, HEAP, lsl #32
    // 0xbaeec4: cmp             w1, NULL
    // 0xbaeec8: b.eq            #0xbb0710
    // 0xbaeecc: LoadField: r4 = r1->field_13
    //     0xbaeecc: ldur            w4, [x1, #0x13]
    // 0xbaeed0: DecompressPointer r4
    //     0xbaeed0: add             x4, x4, HEAP, lsl #32
    // 0xbaeed4: LoadField: r1 = r4->field_b
    //     0xbaeed4: ldur            w1, [x4, #0xb]
    // 0xbaeed8: DecompressPointer r1
    //     0xbaeed8: add             x1, x1, HEAP, lsl #32
    // 0xbaeedc: cmp             w1, NULL
    // 0xbaeee0: b.ne            #0xbaeeec
    // 0xbaeee4: r1 = Null
    //     0xbaeee4: mov             x1, NULL
    // 0xbaeee8: b               #0xbaef1c
    // 0xbaeeec: LoadField: r4 = r1->field_13
    //     0xbaeeec: ldur            w4, [x1, #0x13]
    // 0xbaeef0: DecompressPointer r4
    //     0xbaeef0: add             x4, x4, HEAP, lsl #32
    // 0xbaeef4: cmp             w4, NULL
    // 0xbaeef8: b.ne            #0xbaef04
    // 0xbaeefc: r1 = Null
    //     0xbaeefc: mov             x1, NULL
    // 0xbaef00: b               #0xbaef1c
    // 0xbaef04: LoadField: r1 = r4->field_7
    //     0xbaef04: ldur            w1, [x4, #7]
    // 0xbaef08: cbnz            w1, #0xbaef14
    // 0xbaef0c: r4 = false
    //     0xbaef0c: add             x4, NULL, #0x30  ; false
    // 0xbaef10: b               #0xbaef18
    // 0xbaef14: r4 = true
    //     0xbaef14: add             x4, NULL, #0x20  ; true
    // 0xbaef18: mov             x1, x4
    // 0xbaef1c: cmp             w1, NULL
    // 0xbaef20: b.eq            #0xbaef34
    // 0xbaef24: tbnz            w1, #4, #0xbaef34
    // 0xbaef28: r1 = Instance_IconData
    //     0xbaef28: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbaef2c: ldr             x1, [x1, #0x130]
    // 0xbaef30: b               #0xbaef3c
    // 0xbaef34: r1 = Instance_IconData
    //     0xbaef34: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbaef38: ldr             x1, [x1, #0x138]
    // 0xbaef3c: stur            x1, [fp, #-0x88]
    // 0xbaef40: cmp             w3, #0xc
    // 0xbaef44: b.ne            #0xbaefc0
    // 0xbaef48: LoadField: r3 = r2->field_b
    //     0xbaef48: ldur            w3, [x2, #0xb]
    // 0xbaef4c: DecompressPointer r3
    //     0xbaef4c: add             x3, x3, HEAP, lsl #32
    // 0xbaef50: cmp             w3, NULL
    // 0xbaef54: b.eq            #0xbb0714
    // 0xbaef58: LoadField: r4 = r3->field_13
    //     0xbaef58: ldur            w4, [x3, #0x13]
    // 0xbaef5c: DecompressPointer r4
    //     0xbaef5c: add             x4, x4, HEAP, lsl #32
    // 0xbaef60: LoadField: r3 = r4->field_b
    //     0xbaef60: ldur            w3, [x4, #0xb]
    // 0xbaef64: DecompressPointer r3
    //     0xbaef64: add             x3, x3, HEAP, lsl #32
    // 0xbaef68: cmp             w3, NULL
    // 0xbaef6c: b.ne            #0xbaef78
    // 0xbaef70: r3 = Null
    //     0xbaef70: mov             x3, NULL
    // 0xbaef74: b               #0xbaefa8
    // 0xbaef78: LoadField: r4 = r3->field_13
    //     0xbaef78: ldur            w4, [x3, #0x13]
    // 0xbaef7c: DecompressPointer r4
    //     0xbaef7c: add             x4, x4, HEAP, lsl #32
    // 0xbaef80: cmp             w4, NULL
    // 0xbaef84: b.ne            #0xbaef90
    // 0xbaef88: r3 = Null
    //     0xbaef88: mov             x3, NULL
    // 0xbaef8c: b               #0xbaefa8
    // 0xbaef90: LoadField: r3 = r4->field_7
    //     0xbaef90: ldur            w3, [x4, #7]
    // 0xbaef94: cbnz            w3, #0xbaefa0
    // 0xbaef98: r4 = false
    //     0xbaef98: add             x4, NULL, #0x30  ; false
    // 0xbaef9c: b               #0xbaefa4
    // 0xbaefa0: r4 = true
    //     0xbaefa0: add             x4, NULL, #0x20  ; true
    // 0xbaefa4: mov             x3, x4
    // 0xbaefa8: cmp             w3, NULL
    // 0xbaefac: b.eq            #0xbaefc0
    // 0xbaefb0: tbnz            w3, #4, #0xbaefc0
    // 0xbaefb4: r3 = Instance_Color
    //     0xbaefb4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaefb8: ldr             x3, [x3, #0x858]
    // 0xbaefbc: b               #0xbaefc8
    // 0xbaefc0: r3 = Instance_Color
    //     0xbaefc0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbaefc4: ldr             x3, [x3, #0x50]
    // 0xbaefc8: stur            x3, [fp, #-0x80]
    // 0xbaefcc: r0 = Icon()
    //     0xbaefcc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbaefd0: mov             x1, x0
    // 0xbaefd4: ldur            x0, [fp, #-0x88]
    // 0xbaefd8: StoreField: r1->field_b = r0
    //     0xbaefd8: stur            w0, [x1, #0xb]
    // 0xbaefdc: ldur            x0, [fp, #-0x80]
    // 0xbaefe0: StoreField: r1->field_23 = r0
    //     0xbaefe0: stur            w0, [x1, #0x23]
    // 0xbaefe4: b               #0xbaefec
    // 0xbaefe8: r1 = Null
    //     0xbaefe8: mov             x1, NULL
    // 0xbaefec: ldur            x2, [fp, #-8]
    // 0xbaeff0: ldur            x0, [fp, #-0x20]
    // 0xbaeff4: ldur            x16, [fp, #-0x70]
    // 0xbaeff8: r30 = "Pincode*"
    //     0xbaeff8: add             lr, PP, #0x54, lsl #12  ; [pp+0x540d8] "Pincode*"
    //     0xbaeffc: ldr             lr, [lr, #0xd8]
    // 0xbaf000: stp             lr, x16, [SP, #0x20]
    // 0xbaf004: ldur            x16, [fp, #-0x78]
    // 0xbaf008: r30 = 4
    //     0xbaf008: movz            lr, #0x4
    // 0xbaf00c: stp             lr, x16, [SP, #0x10]
    // 0xbaf010: ldur            x16, [fp, #-0x90]
    // 0xbaf014: stp             x1, x16, [SP]
    // 0xbaf018: ldur            x1, [fp, #-0x68]
    // 0xbaf01c: r4 = const [0, 0x7, 0x6, 0x1, errorMaxLines, 0x4, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x6, null]
    //     0xbaf01c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54088] List(17) [0, 0x7, 0x6, 0x1, "errorMaxLines", 0x4, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x6, Null]
    //     0xbaf020: ldr             x4, [x4, #0x88]
    // 0xbaf024: r0 = copyWith()
    //     0xbaf024: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbaf028: ldur            x2, [fp, #-8]
    // 0xbaf02c: r1 = Function '_validatePinCode@1659256992':.
    //     0xbaf02c: add             x1, PP, #0x54, lsl #12  ; [pp+0x548b8] AnonymousClosure: (0xbb0dfc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validatePinCode (0xbb0e38)
    //     0xbaf030: ldr             x1, [x1, #0x8b8]
    // 0xbaf034: stur            x0, [fp, #-0x68]
    // 0xbaf038: r0 = AllocateClosure()
    //     0xbaf038: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaf03c: ldur            x2, [fp, #-0x18]
    // 0xbaf040: r1 = Function '<anonymous closure>':.
    //     0xbaf040: add             x1, PP, #0x54, lsl #12  ; [pp+0x548c0] AnonymousClosure: (0xbb0ca0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbaf044: ldr             x1, [x1, #0x8c0]
    // 0xbaf048: stur            x0, [fp, #-0x70]
    // 0xbaf04c: r0 = AllocateClosure()
    //     0xbaf04c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaf050: r1 = <String>
    //     0xbaf050: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbaf054: stur            x0, [fp, #-0x78]
    // 0xbaf058: r0 = TextFormField()
    //     0xbaf058: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbaf05c: stur            x0, [fp, #-0x80]
    // 0xbaf060: ldur            x16, [fp, #-0x70]
    // 0xbaf064: r30 = Instance_AutovalidateMode
    //     0xbaf064: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbaf068: ldr             lr, [lr, #0x7e8]
    // 0xbaf06c: stp             lr, x16, [SP, #0x40]
    // 0xbaf070: r16 = true
    //     0xbaf070: add             x16, NULL, #0x20  ; true
    // 0xbaf074: r30 = true
    //     0xbaf074: add             lr, NULL, #0x20  ; true
    // 0xbaf078: stp             lr, x16, [SP, #0x30]
    // 0xbaf07c: ldur            x16, [fp, #-0x58]
    // 0xbaf080: r30 = Instance_TextInputType
    //     0xbaf080: add             lr, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbaf084: ldr             lr, [lr, #0x1a0]
    // 0xbaf088: stp             lr, x16, [SP, #0x20]
    // 0xbaf08c: ldur            x16, [fp, #-0x50]
    // 0xbaf090: r30 = 2
    //     0xbaf090: movz            lr, #0x2
    // 0xbaf094: stp             lr, x16, [SP, #0x10]
    // 0xbaf098: ldur            x16, [fp, #-0x60]
    // 0xbaf09c: ldur            lr, [fp, #-0x78]
    // 0xbaf0a0: stp             lr, x16, [SP]
    // 0xbaf0a4: mov             x1, x0
    // 0xbaf0a8: ldur            x2, [fp, #-0x68]
    // 0xbaf0ac: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, onChanged, 0xb, style, 0xa, validator, 0x2, null]
    //     0xbaf0ac: add             x4, PP, #0x54, lsl #12  ; [pp+0x540a0] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "onChanged", 0xb, "style", 0xa, "validator", 0x2, Null]
    //     0xbaf0b0: ldr             x4, [x4, #0xa0]
    // 0xbaf0b4: r0 = TextFormField()
    //     0xbaf0b4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbaf0b8: r0 = Form()
    //     0xbaf0b8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbaf0bc: mov             x1, x0
    // 0xbaf0c0: ldur            x0, [fp, #-0x80]
    // 0xbaf0c4: stur            x1, [fp, #-0x50]
    // 0xbaf0c8: StoreField: r1->field_b = r0
    //     0xbaf0c8: stur            w0, [x1, #0xb]
    // 0xbaf0cc: r0 = Instance_AutovalidateMode
    //     0xbaf0cc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbaf0d0: ldr             x0, [x0, #0x800]
    // 0xbaf0d4: StoreField: r1->field_23 = r0
    //     0xbaf0d4: stur            w0, [x1, #0x23]
    // 0xbaf0d8: ldur            x2, [fp, #-0x20]
    // 0xbaf0dc: StoreField: r1->field_7 = r2
    //     0xbaf0dc: stur            w2, [x1, #7]
    // 0xbaf0e0: r0 = Padding()
    //     0xbaf0e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbaf0e4: mov             x2, x0
    // 0xbaf0e8: r0 = Instance_EdgeInsets
    //     0xbaf0e8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbaf0ec: ldr             x0, [x0, #0x858]
    // 0xbaf0f0: stur            x2, [fp, #-0x58]
    // 0xbaf0f4: StoreField: r2->field_f = r0
    //     0xbaf0f4: stur            w0, [x2, #0xf]
    // 0xbaf0f8: ldur            x0, [fp, #-0x50]
    // 0xbaf0fc: StoreField: r2->field_b = r0
    //     0xbaf0fc: stur            w0, [x2, #0xb]
    // 0xbaf100: ldur            x0, [fp, #-8]
    // 0xbaf104: LoadField: r3 = r0->field_23
    //     0xbaf104: ldur            w3, [x0, #0x23]
    // 0xbaf108: DecompressPointer r3
    //     0xbaf108: add             x3, x3, HEAP, lsl #32
    // 0xbaf10c: stur            x3, [fp, #-0x50]
    // 0xbaf110: LoadField: r1 = r0->field_3f
    //     0xbaf110: ldur            w1, [x0, #0x3f]
    // 0xbaf114: DecompressPointer r1
    //     0xbaf114: add             x1, x1, HEAP, lsl #32
    // 0xbaf118: LoadField: r4 = r1->field_27
    //     0xbaf118: ldur            w4, [x1, #0x27]
    // 0xbaf11c: DecompressPointer r4
    //     0xbaf11c: add             x4, x4, HEAP, lsl #32
    // 0xbaf120: LoadField: r1 = r4->field_7
    //     0xbaf120: ldur            w1, [x4, #7]
    // 0xbaf124: DecompressPointer r1
    //     0xbaf124: add             x1, x1, HEAP, lsl #32
    // 0xbaf128: LoadField: r4 = r1->field_7
    //     0xbaf128: ldur            w4, [x1, #7]
    // 0xbaf12c: cmp             w4, #0xc
    // 0xbaf130: b.ne            #0xbaf178
    // 0xbaf134: LoadField: r1 = r0->field_b
    //     0xbaf134: ldur            w1, [x0, #0xb]
    // 0xbaf138: DecompressPointer r1
    //     0xbaf138: add             x1, x1, HEAP, lsl #32
    // 0xbaf13c: cmp             w1, NULL
    // 0xbaf140: b.eq            #0xbb0718
    // 0xbaf144: LoadField: r4 = r1->field_13
    //     0xbaf144: ldur            w4, [x1, #0x13]
    // 0xbaf148: DecompressPointer r4
    //     0xbaf148: add             x4, x4, HEAP, lsl #32
    // 0xbaf14c: LoadField: r1 = r4->field_b
    //     0xbaf14c: ldur            w1, [x4, #0xb]
    // 0xbaf150: DecompressPointer r1
    //     0xbaf150: add             x1, x1, HEAP, lsl #32
    // 0xbaf154: cmp             w1, NULL
    // 0xbaf158: b.ne            #0xbaf164
    // 0xbaf15c: r1 = Null
    //     0xbaf15c: mov             x1, NULL
    // 0xbaf160: b               #0xbaf170
    // 0xbaf164: LoadField: r4 = r1->field_13
    //     0xbaf164: ldur            w4, [x1, #0x13]
    // 0xbaf168: DecompressPointer r4
    //     0xbaf168: add             x4, x4, HEAP, lsl #32
    // 0xbaf16c: mov             x1, x4
    // 0xbaf170: mov             x4, x1
    // 0xbaf174: b               #0xbaf17c
    // 0xbaf178: r4 = ""
    //     0xbaf178: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaf17c: stur            x4, [fp, #-0x20]
    // 0xbaf180: r1 = <TextEditingValue>
    //     0xbaf180: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xbaf184: r0 = TextEditingController()
    //     0xbaf184: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xbaf188: stur            x0, [fp, #-0x60]
    // 0xbaf18c: ldur            x16, [fp, #-0x20]
    // 0xbaf190: str             x16, [SP]
    // 0xbaf194: mov             x1, x0
    // 0xbaf198: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xbaf198: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xbaf19c: ldr             x4, [x4, #0xc40]
    // 0xbaf1a0: r0 = TextEditingController()
    //     0xbaf1a0: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xbaf1a4: ldur            x1, [fp, #-0x10]
    // 0xbaf1a8: r0 = of()
    //     0xbaf1a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaf1ac: LoadField: r1 = r0->field_87
    //     0xbaf1ac: ldur            w1, [x0, #0x87]
    // 0xbaf1b0: DecompressPointer r1
    //     0xbaf1b0: add             x1, x1, HEAP, lsl #32
    // 0xbaf1b4: LoadField: r0 = r1->field_2b
    //     0xbaf1b4: ldur            w0, [x1, #0x2b]
    // 0xbaf1b8: DecompressPointer r0
    //     0xbaf1b8: add             x0, x0, HEAP, lsl #32
    // 0xbaf1bc: ldur            x1, [fp, #-0x10]
    // 0xbaf1c0: stur            x0, [fp, #-0x20]
    // 0xbaf1c4: r0 = of()
    //     0xbaf1c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaf1c8: LoadField: r1 = r0->field_5b
    //     0xbaf1c8: ldur            w1, [x0, #0x5b]
    // 0xbaf1cc: DecompressPointer r1
    //     0xbaf1cc: add             x1, x1, HEAP, lsl #32
    // 0xbaf1d0: r16 = 14.000000
    //     0xbaf1d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaf1d4: ldr             x16, [x16, #0x1d8]
    // 0xbaf1d8: stp             x1, x16, [SP]
    // 0xbaf1dc: ldur            x1, [fp, #-0x20]
    // 0xbaf1e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaf1e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaf1e4: ldr             x4, [x4, #0xaa0]
    // 0xbaf1e8: r0 = copyWith()
    //     0xbaf1e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaf1ec: ldur            x1, [fp, #-0x10]
    // 0xbaf1f0: stur            x0, [fp, #-0x20]
    // 0xbaf1f4: r0 = getTextFormFieldInputDecoration()
    //     0xbaf1f4: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbaf1f8: r1 = Instance_Color
    //     0xbaf1f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaf1fc: d0 = 0.100000
    //     0xbaf1fc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbaf200: stur            x0, [fp, #-0x68]
    // 0xbaf204: r0 = withOpacity()
    //     0xbaf204: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaf208: ldur            x1, [fp, #-0x10]
    // 0xbaf20c: stur            x0, [fp, #-0x70]
    // 0xbaf210: r0 = of()
    //     0xbaf210: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaf214: LoadField: r1 = r0->field_87
    //     0xbaf214: ldur            w1, [x0, #0x87]
    // 0xbaf218: DecompressPointer r1
    //     0xbaf218: add             x1, x1, HEAP, lsl #32
    // 0xbaf21c: LoadField: r0 = r1->field_2b
    //     0xbaf21c: ldur            w0, [x1, #0x2b]
    // 0xbaf220: DecompressPointer r0
    //     0xbaf220: add             x0, x0, HEAP, lsl #32
    // 0xbaf224: stur            x0, [fp, #-0x78]
    // 0xbaf228: r1 = Instance_Color
    //     0xbaf228: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaf22c: d0 = 0.400000
    //     0xbaf22c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbaf230: r0 = withOpacity()
    //     0xbaf230: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaf234: r16 = 14.000000
    //     0xbaf234: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaf238: ldr             x16, [x16, #0x1d8]
    // 0xbaf23c: stp             x0, x16, [SP]
    // 0xbaf240: ldur            x1, [fp, #-0x78]
    // 0xbaf244: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaf244: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaf248: ldr             x4, [x4, #0xaa0]
    // 0xbaf24c: r0 = copyWith()
    //     0xbaf24c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaf250: ldur            x2, [fp, #-8]
    // 0xbaf254: stur            x0, [fp, #-0x88]
    // 0xbaf258: LoadField: r1 = r2->field_5b
    //     0xbaf258: ldur            w1, [x2, #0x5b]
    // 0xbaf25c: DecompressPointer r1
    //     0xbaf25c: add             x1, x1, HEAP, lsl #32
    // 0xbaf260: tbnz            w1, #4, #0xbaf3b4
    // 0xbaf264: LoadField: r1 = r2->field_3f
    //     0xbaf264: ldur            w1, [x2, #0x3f]
    // 0xbaf268: DecompressPointer r1
    //     0xbaf268: add             x1, x1, HEAP, lsl #32
    // 0xbaf26c: LoadField: r3 = r1->field_27
    //     0xbaf26c: ldur            w3, [x1, #0x27]
    // 0xbaf270: DecompressPointer r3
    //     0xbaf270: add             x3, x3, HEAP, lsl #32
    // 0xbaf274: LoadField: r1 = r3->field_7
    //     0xbaf274: ldur            w1, [x3, #7]
    // 0xbaf278: DecompressPointer r1
    //     0xbaf278: add             x1, x1, HEAP, lsl #32
    // 0xbaf27c: LoadField: r3 = r1->field_7
    //     0xbaf27c: ldur            w3, [x1, #7]
    // 0xbaf280: cmp             w3, #0xc
    // 0xbaf284: b.ne            #0xbaf300
    // 0xbaf288: LoadField: r1 = r2->field_b
    //     0xbaf288: ldur            w1, [x2, #0xb]
    // 0xbaf28c: DecompressPointer r1
    //     0xbaf28c: add             x1, x1, HEAP, lsl #32
    // 0xbaf290: cmp             w1, NULL
    // 0xbaf294: b.eq            #0xbb071c
    // 0xbaf298: LoadField: r4 = r1->field_13
    //     0xbaf298: ldur            w4, [x1, #0x13]
    // 0xbaf29c: DecompressPointer r4
    //     0xbaf29c: add             x4, x4, HEAP, lsl #32
    // 0xbaf2a0: LoadField: r1 = r4->field_b
    //     0xbaf2a0: ldur            w1, [x4, #0xb]
    // 0xbaf2a4: DecompressPointer r1
    //     0xbaf2a4: add             x1, x1, HEAP, lsl #32
    // 0xbaf2a8: cmp             w1, NULL
    // 0xbaf2ac: b.ne            #0xbaf2b8
    // 0xbaf2b0: r1 = Null
    //     0xbaf2b0: mov             x1, NULL
    // 0xbaf2b4: b               #0xbaf2e8
    // 0xbaf2b8: LoadField: r4 = r1->field_13
    //     0xbaf2b8: ldur            w4, [x1, #0x13]
    // 0xbaf2bc: DecompressPointer r4
    //     0xbaf2bc: add             x4, x4, HEAP, lsl #32
    // 0xbaf2c0: cmp             w4, NULL
    // 0xbaf2c4: b.ne            #0xbaf2d0
    // 0xbaf2c8: r1 = Null
    //     0xbaf2c8: mov             x1, NULL
    // 0xbaf2cc: b               #0xbaf2e8
    // 0xbaf2d0: LoadField: r1 = r4->field_7
    //     0xbaf2d0: ldur            w1, [x4, #7]
    // 0xbaf2d4: cbnz            w1, #0xbaf2e0
    // 0xbaf2d8: r4 = false
    //     0xbaf2d8: add             x4, NULL, #0x30  ; false
    // 0xbaf2dc: b               #0xbaf2e4
    // 0xbaf2e0: r4 = true
    //     0xbaf2e0: add             x4, NULL, #0x20  ; true
    // 0xbaf2e4: mov             x1, x4
    // 0xbaf2e8: cmp             w1, NULL
    // 0xbaf2ec: b.eq            #0xbaf300
    // 0xbaf2f0: tbnz            w1, #4, #0xbaf300
    // 0xbaf2f4: r1 = Instance_IconData
    //     0xbaf2f4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbaf2f8: ldr             x1, [x1, #0x130]
    // 0xbaf2fc: b               #0xbaf308
    // 0xbaf300: r1 = Instance_IconData
    //     0xbaf300: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbaf304: ldr             x1, [x1, #0x138]
    // 0xbaf308: stur            x1, [fp, #-0x80]
    // 0xbaf30c: cmp             w3, #0xc
    // 0xbaf310: b.ne            #0xbaf38c
    // 0xbaf314: LoadField: r3 = r2->field_b
    //     0xbaf314: ldur            w3, [x2, #0xb]
    // 0xbaf318: DecompressPointer r3
    //     0xbaf318: add             x3, x3, HEAP, lsl #32
    // 0xbaf31c: cmp             w3, NULL
    // 0xbaf320: b.eq            #0xbb0720
    // 0xbaf324: LoadField: r4 = r3->field_13
    //     0xbaf324: ldur            w4, [x3, #0x13]
    // 0xbaf328: DecompressPointer r4
    //     0xbaf328: add             x4, x4, HEAP, lsl #32
    // 0xbaf32c: LoadField: r3 = r4->field_b
    //     0xbaf32c: ldur            w3, [x4, #0xb]
    // 0xbaf330: DecompressPointer r3
    //     0xbaf330: add             x3, x3, HEAP, lsl #32
    // 0xbaf334: cmp             w3, NULL
    // 0xbaf338: b.ne            #0xbaf344
    // 0xbaf33c: r3 = Null
    //     0xbaf33c: mov             x3, NULL
    // 0xbaf340: b               #0xbaf374
    // 0xbaf344: LoadField: r4 = r3->field_13
    //     0xbaf344: ldur            w4, [x3, #0x13]
    // 0xbaf348: DecompressPointer r4
    //     0xbaf348: add             x4, x4, HEAP, lsl #32
    // 0xbaf34c: cmp             w4, NULL
    // 0xbaf350: b.ne            #0xbaf35c
    // 0xbaf354: r3 = Null
    //     0xbaf354: mov             x3, NULL
    // 0xbaf358: b               #0xbaf374
    // 0xbaf35c: LoadField: r3 = r4->field_7
    //     0xbaf35c: ldur            w3, [x4, #7]
    // 0xbaf360: cbnz            w3, #0xbaf36c
    // 0xbaf364: r4 = false
    //     0xbaf364: add             x4, NULL, #0x30  ; false
    // 0xbaf368: b               #0xbaf370
    // 0xbaf36c: r4 = true
    //     0xbaf36c: add             x4, NULL, #0x20  ; true
    // 0xbaf370: mov             x3, x4
    // 0xbaf374: cmp             w3, NULL
    // 0xbaf378: b.eq            #0xbaf38c
    // 0xbaf37c: tbnz            w3, #4, #0xbaf38c
    // 0xbaf380: r3 = Instance_Color
    //     0xbaf380: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaf384: ldr             x3, [x3, #0x858]
    // 0xbaf388: b               #0xbaf394
    // 0xbaf38c: r3 = Instance_Color
    //     0xbaf38c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbaf390: ldr             x3, [x3, #0x50]
    // 0xbaf394: stur            x3, [fp, #-0x78]
    // 0xbaf398: r0 = Icon()
    //     0xbaf398: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbaf39c: mov             x1, x0
    // 0xbaf3a0: ldur            x0, [fp, #-0x80]
    // 0xbaf3a4: StoreField: r1->field_b = r0
    //     0xbaf3a4: stur            w0, [x1, #0xb]
    // 0xbaf3a8: ldur            x0, [fp, #-0x78]
    // 0xbaf3ac: StoreField: r1->field_23 = r0
    //     0xbaf3ac: stur            w0, [x1, #0x23]
    // 0xbaf3b0: b               #0xbaf3b8
    // 0xbaf3b4: r1 = Null
    //     0xbaf3b4: mov             x1, NULL
    // 0xbaf3b8: ldur            x2, [fp, #-8]
    // 0xbaf3bc: ldur            x0, [fp, #-0x50]
    // 0xbaf3c0: ldur            x16, [fp, #-0x70]
    // 0xbaf3c4: r30 = "City*"
    //     0xbaf3c4: add             lr, PP, #0x54, lsl #12  ; [pp+0x540f0] "City*"
    //     0xbaf3c8: ldr             lr, [lr, #0xf0]
    // 0xbaf3cc: stp             lr, x16, [SP, #0x10]
    // 0xbaf3d0: ldur            x16, [fp, #-0x88]
    // 0xbaf3d4: stp             x1, x16, [SP]
    // 0xbaf3d8: ldur            x1, [fp, #-0x68]
    // 0xbaf3dc: r4 = const [0, 0x5, 0x4, 0x1, fillColor, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x4, null]
    //     0xbaf3dc: add             x4, PP, #0x54, lsl #12  ; [pp+0x548c8] List(13) [0, 0x5, 0x4, 0x1, "fillColor", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x4, Null]
    //     0xbaf3e0: ldr             x4, [x4, #0x8c8]
    // 0xbaf3e4: r0 = copyWith()
    //     0xbaf3e4: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbaf3e8: r1 = <String>
    //     0xbaf3e8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbaf3ec: stur            x0, [fp, #-0x68]
    // 0xbaf3f0: r0 = TextFormField()
    //     0xbaf3f0: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbaf3f4: stur            x0, [fp, #-0x70]
    // 0xbaf3f8: r16 = true
    //     0xbaf3f8: add             x16, NULL, #0x20  ; true
    // 0xbaf3fc: r30 = true
    //     0xbaf3fc: add             lr, NULL, #0x20  ; true
    // 0xbaf400: stp             lr, x16, [SP, #0x10]
    // 0xbaf404: ldur            x16, [fp, #-0x60]
    // 0xbaf408: ldur            lr, [fp, #-0x20]
    // 0xbaf40c: stp             lr, x16, [SP]
    // 0xbaf410: mov             x1, x0
    // 0xbaf414: ldur            x2, [fp, #-0x68]
    // 0xbaf418: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xbaf418: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xbaf41c: ldr             x4, [x4, #0x100]
    // 0xbaf420: r0 = TextFormField()
    //     0xbaf420: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbaf424: r0 = Form()
    //     0xbaf424: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbaf428: mov             x2, x0
    // 0xbaf42c: ldur            x0, [fp, #-0x70]
    // 0xbaf430: stur            x2, [fp, #-0x20]
    // 0xbaf434: StoreField: r2->field_b = r0
    //     0xbaf434: stur            w0, [x2, #0xb]
    // 0xbaf438: r0 = Instance_AutovalidateMode
    //     0xbaf438: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbaf43c: ldr             x0, [x0, #0x800]
    // 0xbaf440: StoreField: r2->field_23 = r0
    //     0xbaf440: stur            w0, [x2, #0x23]
    // 0xbaf444: ldur            x1, [fp, #-0x50]
    // 0xbaf448: StoreField: r2->field_7 = r1
    //     0xbaf448: stur            w1, [x2, #7]
    // 0xbaf44c: r1 = <FlexParentData>
    //     0xbaf44c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbaf450: ldr             x1, [x1, #0xe00]
    // 0xbaf454: r0 = Flexible()
    //     0xbaf454: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbaf458: mov             x2, x0
    // 0xbaf45c: r0 = 1
    //     0xbaf45c: movz            x0, #0x1
    // 0xbaf460: stur            x2, [fp, #-0x60]
    // 0xbaf464: StoreField: r2->field_13 = r0
    //     0xbaf464: stur            x0, [x2, #0x13]
    // 0xbaf468: r3 = Instance_FlexFit
    //     0xbaf468: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbaf46c: ldr             x3, [x3, #0xe08]
    // 0xbaf470: StoreField: r2->field_1b = r3
    //     0xbaf470: stur            w3, [x2, #0x1b]
    // 0xbaf474: ldur            x1, [fp, #-0x20]
    // 0xbaf478: StoreField: r2->field_b = r1
    //     0xbaf478: stur            w1, [x2, #0xb]
    // 0xbaf47c: ldur            x4, [fp, #-8]
    // 0xbaf480: LoadField: r5 = r4->field_27
    //     0xbaf480: ldur            w5, [x4, #0x27]
    // 0xbaf484: DecompressPointer r5
    //     0xbaf484: add             x5, x5, HEAP, lsl #32
    // 0xbaf488: stur            x5, [fp, #-0x50]
    // 0xbaf48c: LoadField: r1 = r4->field_3f
    //     0xbaf48c: ldur            w1, [x4, #0x3f]
    // 0xbaf490: DecompressPointer r1
    //     0xbaf490: add             x1, x1, HEAP, lsl #32
    // 0xbaf494: LoadField: r6 = r1->field_27
    //     0xbaf494: ldur            w6, [x1, #0x27]
    // 0xbaf498: DecompressPointer r6
    //     0xbaf498: add             x6, x6, HEAP, lsl #32
    // 0xbaf49c: LoadField: r1 = r6->field_7
    //     0xbaf49c: ldur            w1, [x6, #7]
    // 0xbaf4a0: DecompressPointer r1
    //     0xbaf4a0: add             x1, x1, HEAP, lsl #32
    // 0xbaf4a4: LoadField: r6 = r1->field_7
    //     0xbaf4a4: ldur            w6, [x1, #7]
    // 0xbaf4a8: cmp             w6, #0xc
    // 0xbaf4ac: b.ne            #0xbaf4f4
    // 0xbaf4b0: LoadField: r1 = r4->field_b
    //     0xbaf4b0: ldur            w1, [x4, #0xb]
    // 0xbaf4b4: DecompressPointer r1
    //     0xbaf4b4: add             x1, x1, HEAP, lsl #32
    // 0xbaf4b8: cmp             w1, NULL
    // 0xbaf4bc: b.eq            #0xbb0724
    // 0xbaf4c0: LoadField: r6 = r1->field_13
    //     0xbaf4c0: ldur            w6, [x1, #0x13]
    // 0xbaf4c4: DecompressPointer r6
    //     0xbaf4c4: add             x6, x6, HEAP, lsl #32
    // 0xbaf4c8: LoadField: r1 = r6->field_b
    //     0xbaf4c8: ldur            w1, [x6, #0xb]
    // 0xbaf4cc: DecompressPointer r1
    //     0xbaf4cc: add             x1, x1, HEAP, lsl #32
    // 0xbaf4d0: cmp             w1, NULL
    // 0xbaf4d4: b.ne            #0xbaf4e0
    // 0xbaf4d8: r1 = Null
    //     0xbaf4d8: mov             x1, NULL
    // 0xbaf4dc: b               #0xbaf4ec
    // 0xbaf4e0: LoadField: r6 = r1->field_f
    //     0xbaf4e0: ldur            w6, [x1, #0xf]
    // 0xbaf4e4: DecompressPointer r6
    //     0xbaf4e4: add             x6, x6, HEAP, lsl #32
    // 0xbaf4e8: mov             x1, x6
    // 0xbaf4ec: mov             x6, x1
    // 0xbaf4f0: b               #0xbaf4f8
    // 0xbaf4f4: r6 = ""
    //     0xbaf4f4: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaf4f8: stur            x6, [fp, #-0x20]
    // 0xbaf4fc: r1 = <TextEditingValue>
    //     0xbaf4fc: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xbaf500: r0 = TextEditingController()
    //     0xbaf500: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xbaf504: stur            x0, [fp, #-0x68]
    // 0xbaf508: ldur            x16, [fp, #-0x20]
    // 0xbaf50c: str             x16, [SP]
    // 0xbaf510: mov             x1, x0
    // 0xbaf514: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xbaf514: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xbaf518: ldr             x4, [x4, #0xc40]
    // 0xbaf51c: r0 = TextEditingController()
    //     0xbaf51c: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xbaf520: ldur            x1, [fp, #-0x10]
    // 0xbaf524: r0 = of()
    //     0xbaf524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaf528: LoadField: r1 = r0->field_87
    //     0xbaf528: ldur            w1, [x0, #0x87]
    // 0xbaf52c: DecompressPointer r1
    //     0xbaf52c: add             x1, x1, HEAP, lsl #32
    // 0xbaf530: LoadField: r0 = r1->field_2b
    //     0xbaf530: ldur            w0, [x1, #0x2b]
    // 0xbaf534: DecompressPointer r0
    //     0xbaf534: add             x0, x0, HEAP, lsl #32
    // 0xbaf538: ldur            x1, [fp, #-0x10]
    // 0xbaf53c: stur            x0, [fp, #-0x20]
    // 0xbaf540: r0 = of()
    //     0xbaf540: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaf544: LoadField: r1 = r0->field_5b
    //     0xbaf544: ldur            w1, [x0, #0x5b]
    // 0xbaf548: DecompressPointer r1
    //     0xbaf548: add             x1, x1, HEAP, lsl #32
    // 0xbaf54c: r16 = 14.000000
    //     0xbaf54c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaf550: ldr             x16, [x16, #0x1d8]
    // 0xbaf554: stp             x1, x16, [SP]
    // 0xbaf558: ldur            x1, [fp, #-0x20]
    // 0xbaf55c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaf55c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaf560: ldr             x4, [x4, #0xaa0]
    // 0xbaf564: r0 = copyWith()
    //     0xbaf564: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaf568: ldur            x1, [fp, #-0x10]
    // 0xbaf56c: stur            x0, [fp, #-0x20]
    // 0xbaf570: r0 = getTextFormFieldInputDecoration()
    //     0xbaf570: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbaf574: r1 = Instance_Color
    //     0xbaf574: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaf578: d0 = 0.100000
    //     0xbaf578: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbaf57c: stur            x0, [fp, #-0x70]
    // 0xbaf580: r0 = withOpacity()
    //     0xbaf580: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaf584: ldur            x1, [fp, #-0x10]
    // 0xbaf588: stur            x0, [fp, #-0x78]
    // 0xbaf58c: r0 = of()
    //     0xbaf58c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaf590: LoadField: r1 = r0->field_87
    //     0xbaf590: ldur            w1, [x0, #0x87]
    // 0xbaf594: DecompressPointer r1
    //     0xbaf594: add             x1, x1, HEAP, lsl #32
    // 0xbaf598: LoadField: r0 = r1->field_2b
    //     0xbaf598: ldur            w0, [x1, #0x2b]
    // 0xbaf59c: DecompressPointer r0
    //     0xbaf59c: add             x0, x0, HEAP, lsl #32
    // 0xbaf5a0: stur            x0, [fp, #-0x80]
    // 0xbaf5a4: r1 = Instance_Color
    //     0xbaf5a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaf5a8: d0 = 0.400000
    //     0xbaf5a8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbaf5ac: r0 = withOpacity()
    //     0xbaf5ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaf5b0: r16 = 14.000000
    //     0xbaf5b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaf5b4: ldr             x16, [x16, #0x1d8]
    // 0xbaf5b8: stp             x0, x16, [SP]
    // 0xbaf5bc: ldur            x1, [fp, #-0x80]
    // 0xbaf5c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaf5c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaf5c4: ldr             x4, [x4, #0xaa0]
    // 0xbaf5c8: r0 = copyWith()
    //     0xbaf5c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaf5cc: ldur            x2, [fp, #-8]
    // 0xbaf5d0: stur            x0, [fp, #-0x90]
    // 0xbaf5d4: LoadField: r1 = r2->field_5b
    //     0xbaf5d4: ldur            w1, [x2, #0x5b]
    // 0xbaf5d8: DecompressPointer r1
    //     0xbaf5d8: add             x1, x1, HEAP, lsl #32
    // 0xbaf5dc: tbnz            w1, #4, #0xbaf730
    // 0xbaf5e0: LoadField: r1 = r2->field_3f
    //     0xbaf5e0: ldur            w1, [x2, #0x3f]
    // 0xbaf5e4: DecompressPointer r1
    //     0xbaf5e4: add             x1, x1, HEAP, lsl #32
    // 0xbaf5e8: LoadField: r3 = r1->field_27
    //     0xbaf5e8: ldur            w3, [x1, #0x27]
    // 0xbaf5ec: DecompressPointer r3
    //     0xbaf5ec: add             x3, x3, HEAP, lsl #32
    // 0xbaf5f0: LoadField: r1 = r3->field_7
    //     0xbaf5f0: ldur            w1, [x3, #7]
    // 0xbaf5f4: DecompressPointer r1
    //     0xbaf5f4: add             x1, x1, HEAP, lsl #32
    // 0xbaf5f8: LoadField: r3 = r1->field_7
    //     0xbaf5f8: ldur            w3, [x1, #7]
    // 0xbaf5fc: cmp             w3, #0xc
    // 0xbaf600: b.ne            #0xbaf67c
    // 0xbaf604: LoadField: r1 = r2->field_b
    //     0xbaf604: ldur            w1, [x2, #0xb]
    // 0xbaf608: DecompressPointer r1
    //     0xbaf608: add             x1, x1, HEAP, lsl #32
    // 0xbaf60c: cmp             w1, NULL
    // 0xbaf610: b.eq            #0xbb0728
    // 0xbaf614: LoadField: r4 = r1->field_13
    //     0xbaf614: ldur            w4, [x1, #0x13]
    // 0xbaf618: DecompressPointer r4
    //     0xbaf618: add             x4, x4, HEAP, lsl #32
    // 0xbaf61c: LoadField: r1 = r4->field_b
    //     0xbaf61c: ldur            w1, [x4, #0xb]
    // 0xbaf620: DecompressPointer r1
    //     0xbaf620: add             x1, x1, HEAP, lsl #32
    // 0xbaf624: cmp             w1, NULL
    // 0xbaf628: b.ne            #0xbaf634
    // 0xbaf62c: r1 = Null
    //     0xbaf62c: mov             x1, NULL
    // 0xbaf630: b               #0xbaf664
    // 0xbaf634: LoadField: r4 = r1->field_13
    //     0xbaf634: ldur            w4, [x1, #0x13]
    // 0xbaf638: DecompressPointer r4
    //     0xbaf638: add             x4, x4, HEAP, lsl #32
    // 0xbaf63c: cmp             w4, NULL
    // 0xbaf640: b.ne            #0xbaf64c
    // 0xbaf644: r1 = Null
    //     0xbaf644: mov             x1, NULL
    // 0xbaf648: b               #0xbaf664
    // 0xbaf64c: LoadField: r1 = r4->field_7
    //     0xbaf64c: ldur            w1, [x4, #7]
    // 0xbaf650: cbnz            w1, #0xbaf65c
    // 0xbaf654: r4 = false
    //     0xbaf654: add             x4, NULL, #0x30  ; false
    // 0xbaf658: b               #0xbaf660
    // 0xbaf65c: r4 = true
    //     0xbaf65c: add             x4, NULL, #0x20  ; true
    // 0xbaf660: mov             x1, x4
    // 0xbaf664: cmp             w1, NULL
    // 0xbaf668: b.eq            #0xbaf67c
    // 0xbaf66c: tbnz            w1, #4, #0xbaf67c
    // 0xbaf670: r1 = Instance_IconData
    //     0xbaf670: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbaf674: ldr             x1, [x1, #0x130]
    // 0xbaf678: b               #0xbaf684
    // 0xbaf67c: r1 = Instance_IconData
    //     0xbaf67c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbaf680: ldr             x1, [x1, #0x138]
    // 0xbaf684: stur            x1, [fp, #-0x88]
    // 0xbaf688: cmp             w3, #0xc
    // 0xbaf68c: b.ne            #0xbaf708
    // 0xbaf690: LoadField: r3 = r2->field_b
    //     0xbaf690: ldur            w3, [x2, #0xb]
    // 0xbaf694: DecompressPointer r3
    //     0xbaf694: add             x3, x3, HEAP, lsl #32
    // 0xbaf698: cmp             w3, NULL
    // 0xbaf69c: b.eq            #0xbb072c
    // 0xbaf6a0: LoadField: r4 = r3->field_13
    //     0xbaf6a0: ldur            w4, [x3, #0x13]
    // 0xbaf6a4: DecompressPointer r4
    //     0xbaf6a4: add             x4, x4, HEAP, lsl #32
    // 0xbaf6a8: LoadField: r3 = r4->field_b
    //     0xbaf6a8: ldur            w3, [x4, #0xb]
    // 0xbaf6ac: DecompressPointer r3
    //     0xbaf6ac: add             x3, x3, HEAP, lsl #32
    // 0xbaf6b0: cmp             w3, NULL
    // 0xbaf6b4: b.ne            #0xbaf6c0
    // 0xbaf6b8: r3 = Null
    //     0xbaf6b8: mov             x3, NULL
    // 0xbaf6bc: b               #0xbaf6f0
    // 0xbaf6c0: LoadField: r4 = r3->field_f
    //     0xbaf6c0: ldur            w4, [x3, #0xf]
    // 0xbaf6c4: DecompressPointer r4
    //     0xbaf6c4: add             x4, x4, HEAP, lsl #32
    // 0xbaf6c8: cmp             w4, NULL
    // 0xbaf6cc: b.ne            #0xbaf6d8
    // 0xbaf6d0: r3 = Null
    //     0xbaf6d0: mov             x3, NULL
    // 0xbaf6d4: b               #0xbaf6f0
    // 0xbaf6d8: LoadField: r3 = r4->field_7
    //     0xbaf6d8: ldur            w3, [x4, #7]
    // 0xbaf6dc: cbnz            w3, #0xbaf6e8
    // 0xbaf6e0: r4 = false
    //     0xbaf6e0: add             x4, NULL, #0x30  ; false
    // 0xbaf6e4: b               #0xbaf6ec
    // 0xbaf6e8: r4 = true
    //     0xbaf6e8: add             x4, NULL, #0x20  ; true
    // 0xbaf6ec: mov             x3, x4
    // 0xbaf6f0: cmp             w3, NULL
    // 0xbaf6f4: b.eq            #0xbaf708
    // 0xbaf6f8: tbnz            w3, #4, #0xbaf708
    // 0xbaf6fc: r3 = Instance_Color
    //     0xbaf6fc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaf700: ldr             x3, [x3, #0x858]
    // 0xbaf704: b               #0xbaf710
    // 0xbaf708: r3 = Instance_Color
    //     0xbaf708: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbaf70c: ldr             x3, [x3, #0x50]
    // 0xbaf710: stur            x3, [fp, #-0x80]
    // 0xbaf714: r0 = Icon()
    //     0xbaf714: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbaf718: mov             x1, x0
    // 0xbaf71c: ldur            x0, [fp, #-0x88]
    // 0xbaf720: StoreField: r1->field_b = r0
    //     0xbaf720: stur            w0, [x1, #0xb]
    // 0xbaf724: ldur            x0, [fp, #-0x80]
    // 0xbaf728: StoreField: r1->field_23 = r0
    //     0xbaf728: stur            w0, [x1, #0x23]
    // 0xbaf72c: b               #0xbaf734
    // 0xbaf730: r1 = Null
    //     0xbaf730: mov             x1, NULL
    // 0xbaf734: ldur            x2, [fp, #-8]
    // 0xbaf738: ldur            x7, [fp, #-0x38]
    // 0xbaf73c: ldur            x6, [fp, #-0x40]
    // 0xbaf740: ldur            x5, [fp, #-0x48]
    // 0xbaf744: ldur            x4, [fp, #-0x58]
    // 0xbaf748: ldur            x0, [fp, #-0x60]
    // 0xbaf74c: ldur            x3, [fp, #-0x50]
    // 0xbaf750: ldur            x16, [fp, #-0x78]
    // 0xbaf754: r30 = "State*"
    //     0xbaf754: add             lr, PP, #0x54, lsl #12  ; [pp+0x54108] "State*"
    //     0xbaf758: ldr             lr, [lr, #0x108]
    // 0xbaf75c: stp             lr, x16, [SP, #0x10]
    // 0xbaf760: ldur            x16, [fp, #-0x90]
    // 0xbaf764: stp             x1, x16, [SP]
    // 0xbaf768: ldur            x1, [fp, #-0x70]
    // 0xbaf76c: r4 = const [0, 0x5, 0x4, 0x1, fillColor, 0x1, labelStyle, 0x3, labelText, 0x2, suffixIcon, 0x4, null]
    //     0xbaf76c: add             x4, PP, #0x54, lsl #12  ; [pp+0x548c8] List(13) [0, 0x5, 0x4, 0x1, "fillColor", 0x1, "labelStyle", 0x3, "labelText", 0x2, "suffixIcon", 0x4, Null]
    //     0xbaf770: ldr             x4, [x4, #0x8c8]
    // 0xbaf774: r0 = copyWith()
    //     0xbaf774: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbaf778: r1 = <String>
    //     0xbaf778: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbaf77c: stur            x0, [fp, #-0x70]
    // 0xbaf780: r0 = TextFormField()
    //     0xbaf780: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbaf784: stur            x0, [fp, #-0x78]
    // 0xbaf788: r16 = true
    //     0xbaf788: add             x16, NULL, #0x20  ; true
    // 0xbaf78c: r30 = true
    //     0xbaf78c: add             lr, NULL, #0x20  ; true
    // 0xbaf790: stp             lr, x16, [SP, #0x10]
    // 0xbaf794: ldur            x16, [fp, #-0x68]
    // 0xbaf798: ldur            lr, [fp, #-0x20]
    // 0xbaf79c: stp             lr, x16, [SP]
    // 0xbaf7a0: mov             x1, x0
    // 0xbaf7a4: ldur            x2, [fp, #-0x70]
    // 0xbaf7a8: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xbaf7a8: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xbaf7ac: ldr             x4, [x4, #0x100]
    // 0xbaf7b0: r0 = TextFormField()
    //     0xbaf7b0: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbaf7b4: r0 = Form()
    //     0xbaf7b4: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbaf7b8: mov             x2, x0
    // 0xbaf7bc: ldur            x0, [fp, #-0x78]
    // 0xbaf7c0: stur            x2, [fp, #-0x20]
    // 0xbaf7c4: StoreField: r2->field_b = r0
    //     0xbaf7c4: stur            w0, [x2, #0xb]
    // 0xbaf7c8: r0 = Instance_AutovalidateMode
    //     0xbaf7c8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbaf7cc: ldr             x0, [x0, #0x800]
    // 0xbaf7d0: StoreField: r2->field_23 = r0
    //     0xbaf7d0: stur            w0, [x2, #0x23]
    // 0xbaf7d4: ldur            x1, [fp, #-0x50]
    // 0xbaf7d8: StoreField: r2->field_7 = r1
    //     0xbaf7d8: stur            w1, [x2, #7]
    // 0xbaf7dc: r1 = <FlexParentData>
    //     0xbaf7dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbaf7e0: ldr             x1, [x1, #0xe00]
    // 0xbaf7e4: r0 = Flexible()
    //     0xbaf7e4: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbaf7e8: mov             x3, x0
    // 0xbaf7ec: r0 = 1
    //     0xbaf7ec: movz            x0, #0x1
    // 0xbaf7f0: stur            x3, [fp, #-0x50]
    // 0xbaf7f4: StoreField: r3->field_13 = r0
    //     0xbaf7f4: stur            x0, [x3, #0x13]
    // 0xbaf7f8: r0 = Instance_FlexFit
    //     0xbaf7f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbaf7fc: ldr             x0, [x0, #0xe08]
    // 0xbaf800: StoreField: r3->field_1b = r0
    //     0xbaf800: stur            w0, [x3, #0x1b]
    // 0xbaf804: ldur            x0, [fp, #-0x20]
    // 0xbaf808: StoreField: r3->field_b = r0
    //     0xbaf808: stur            w0, [x3, #0xb]
    // 0xbaf80c: r1 = Null
    //     0xbaf80c: mov             x1, NULL
    // 0xbaf810: r2 = 6
    //     0xbaf810: movz            x2, #0x6
    // 0xbaf814: r0 = AllocateArray()
    //     0xbaf814: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaf818: mov             x2, x0
    // 0xbaf81c: ldur            x0, [fp, #-0x60]
    // 0xbaf820: stur            x2, [fp, #-0x20]
    // 0xbaf824: StoreField: r2->field_f = r0
    //     0xbaf824: stur            w0, [x2, #0xf]
    // 0xbaf828: r16 = Instance_SizedBox
    //     0xbaf828: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xbaf82c: ldr             x16, [x16, #0x998]
    // 0xbaf830: StoreField: r2->field_13 = r16
    //     0xbaf830: stur            w16, [x2, #0x13]
    // 0xbaf834: ldur            x0, [fp, #-0x50]
    // 0xbaf838: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaf838: stur            w0, [x2, #0x17]
    // 0xbaf83c: r1 = <Widget>
    //     0xbaf83c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaf840: r0 = AllocateGrowableArray()
    //     0xbaf840: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaf844: mov             x1, x0
    // 0xbaf848: ldur            x0, [fp, #-0x20]
    // 0xbaf84c: stur            x1, [fp, #-0x50]
    // 0xbaf850: StoreField: r1->field_f = r0
    //     0xbaf850: stur            w0, [x1, #0xf]
    // 0xbaf854: r2 = 6
    //     0xbaf854: movz            x2, #0x6
    // 0xbaf858: StoreField: r1->field_b = r2
    //     0xbaf858: stur            w2, [x1, #0xb]
    // 0xbaf85c: r0 = Row()
    //     0xbaf85c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbaf860: mov             x1, x0
    // 0xbaf864: r0 = Instance_Axis
    //     0xbaf864: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbaf868: stur            x1, [fp, #-0x20]
    // 0xbaf86c: StoreField: r1->field_f = r0
    //     0xbaf86c: stur            w0, [x1, #0xf]
    // 0xbaf870: r2 = Instance_MainAxisAlignment
    //     0xbaf870: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbaf874: ldr             x2, [x2, #0xd10]
    // 0xbaf878: StoreField: r1->field_13 = r2
    //     0xbaf878: stur            w2, [x1, #0x13]
    // 0xbaf87c: r2 = Instance_MainAxisSize
    //     0xbaf87c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaf880: ldr             x2, [x2, #0xa10]
    // 0xbaf884: ArrayStore: r1[0] = r2  ; List_4
    //     0xbaf884: stur            w2, [x1, #0x17]
    // 0xbaf888: r3 = Instance_CrossAxisAlignment
    //     0xbaf888: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbaf88c: ldr             x3, [x3, #0x890]
    // 0xbaf890: StoreField: r1->field_1b = r3
    //     0xbaf890: stur            w3, [x1, #0x1b]
    // 0xbaf894: r3 = Instance_VerticalDirection
    //     0xbaf894: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaf898: ldr             x3, [x3, #0xa20]
    // 0xbaf89c: StoreField: r1->field_23 = r3
    //     0xbaf89c: stur            w3, [x1, #0x23]
    // 0xbaf8a0: r4 = Instance_Clip
    //     0xbaf8a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaf8a4: ldr             x4, [x4, #0x38]
    // 0xbaf8a8: StoreField: r1->field_2b = r4
    //     0xbaf8a8: stur            w4, [x1, #0x2b]
    // 0xbaf8ac: StoreField: r1->field_2f = rZR
    //     0xbaf8ac: stur            xzr, [x1, #0x2f]
    // 0xbaf8b0: ldur            x5, [fp, #-0x50]
    // 0xbaf8b4: StoreField: r1->field_b = r5
    //     0xbaf8b4: stur            w5, [x1, #0xb]
    // 0xbaf8b8: r0 = Container()
    //     0xbaf8b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbaf8bc: stur            x0, [fp, #-0x50]
    // 0xbaf8c0: r16 = Instance_EdgeInsets
    //     0xbaf8c0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbaf8c4: ldr             x16, [x16, #0x858]
    // 0xbaf8c8: ldur            lr, [fp, #-0x20]
    // 0xbaf8cc: stp             lr, x16, [SP]
    // 0xbaf8d0: mov             x1, x0
    // 0xbaf8d4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0xbaf8d4: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0xbaf8d8: ldr             x4, [x4, #0x400]
    // 0xbaf8dc: r0 = Container()
    //     0xbaf8dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbaf8e0: r1 = Null
    //     0xbaf8e0: mov             x1, NULL
    // 0xbaf8e4: r2 = 10
    //     0xbaf8e4: movz            x2, #0xa
    // 0xbaf8e8: r0 = AllocateArray()
    //     0xbaf8e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaf8ec: mov             x2, x0
    // 0xbaf8f0: ldur            x0, [fp, #-0x38]
    // 0xbaf8f4: stur            x2, [fp, #-0x20]
    // 0xbaf8f8: StoreField: r2->field_f = r0
    //     0xbaf8f8: stur            w0, [x2, #0xf]
    // 0xbaf8fc: ldur            x0, [fp, #-0x40]
    // 0xbaf900: StoreField: r2->field_13 = r0
    //     0xbaf900: stur            w0, [x2, #0x13]
    // 0xbaf904: ldur            x0, [fp, #-0x48]
    // 0xbaf908: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaf908: stur            w0, [x2, #0x17]
    // 0xbaf90c: ldur            x0, [fp, #-0x58]
    // 0xbaf910: StoreField: r2->field_1b = r0
    //     0xbaf910: stur            w0, [x2, #0x1b]
    // 0xbaf914: ldur            x0, [fp, #-0x50]
    // 0xbaf918: StoreField: r2->field_1f = r0
    //     0xbaf918: stur            w0, [x2, #0x1f]
    // 0xbaf91c: r1 = <Widget>
    //     0xbaf91c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaf920: r0 = AllocateGrowableArray()
    //     0xbaf920: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaf924: mov             x1, x0
    // 0xbaf928: ldur            x0, [fp, #-0x20]
    // 0xbaf92c: stur            x1, [fp, #-0x38]
    // 0xbaf930: StoreField: r1->field_f = r0
    //     0xbaf930: stur            w0, [x1, #0xf]
    // 0xbaf934: r0 = 10
    //     0xbaf934: movz            x0, #0xa
    // 0xbaf938: StoreField: r1->field_b = r0
    //     0xbaf938: stur            w0, [x1, #0xb]
    // 0xbaf93c: r0 = Column()
    //     0xbaf93c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbaf940: mov             x1, x0
    // 0xbaf944: r0 = Instance_Axis
    //     0xbaf944: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbaf948: stur            x1, [fp, #-0x20]
    // 0xbaf94c: StoreField: r1->field_f = r0
    //     0xbaf94c: stur            w0, [x1, #0xf]
    // 0xbaf950: r2 = Instance_MainAxisAlignment
    //     0xbaf950: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbaf954: ldr             x2, [x2, #0xa08]
    // 0xbaf958: StoreField: r1->field_13 = r2
    //     0xbaf958: stur            w2, [x1, #0x13]
    // 0xbaf95c: r2 = Instance_MainAxisSize
    //     0xbaf95c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaf960: ldr             x2, [x2, #0xa10]
    // 0xbaf964: ArrayStore: r1[0] = r2  ; List_4
    //     0xbaf964: stur            w2, [x1, #0x17]
    // 0xbaf968: r2 = Instance_CrossAxisAlignment
    //     0xbaf968: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbaf96c: ldr             x2, [x2, #0xa18]
    // 0xbaf970: StoreField: r1->field_1b = r2
    //     0xbaf970: stur            w2, [x1, #0x1b]
    // 0xbaf974: r2 = Instance_VerticalDirection
    //     0xbaf974: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaf978: ldr             x2, [x2, #0xa20]
    // 0xbaf97c: StoreField: r1->field_23 = r2
    //     0xbaf97c: stur            w2, [x1, #0x23]
    // 0xbaf980: r3 = Instance_Clip
    //     0xbaf980: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaf984: ldr             x3, [x3, #0x38]
    // 0xbaf988: StoreField: r1->field_2b = r3
    //     0xbaf988: stur            w3, [x1, #0x2b]
    // 0xbaf98c: StoreField: r1->field_2f = rZR
    //     0xbaf98c: stur            xzr, [x1, #0x2f]
    // 0xbaf990: ldur            x4, [fp, #-0x38]
    // 0xbaf994: StoreField: r1->field_b = r4
    //     0xbaf994: stur            w4, [x1, #0xb]
    // 0xbaf998: r0 = Padding()
    //     0xbaf998: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbaf99c: mov             x4, x0
    // 0xbaf9a0: r3 = Instance_EdgeInsets
    //     0xbaf9a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbaf9a4: ldr             x3, [x3, #0x668]
    // 0xbaf9a8: stur            x4, [fp, #-0x38]
    // 0xbaf9ac: StoreField: r4->field_f = r3
    //     0xbaf9ac: stur            w3, [x4, #0xf]
    // 0xbaf9b0: ldur            x0, [fp, #-0x20]
    // 0xbaf9b4: StoreField: r4->field_b = r0
    //     0xbaf9b4: stur            w0, [x4, #0xb]
    // 0xbaf9b8: ldur            x5, [fp, #-8]
    // 0xbaf9bc: LoadField: r0 = r5->field_b
    //     0xbaf9bc: ldur            w0, [x5, #0xb]
    // 0xbaf9c0: DecompressPointer r0
    //     0xbaf9c0: add             x0, x0, HEAP, lsl #32
    // 0xbaf9c4: cmp             w0, NULL
    // 0xbaf9c8: b.eq            #0xbb0730
    // 0xbaf9cc: LoadField: r1 = r0->field_b
    //     0xbaf9cc: ldur            w1, [x0, #0xb]
    // 0xbaf9d0: DecompressPointer r1
    //     0xbaf9d0: add             x1, x1, HEAP, lsl #32
    // 0xbaf9d4: LoadField: r0 = r1->field_f
    //     0xbaf9d4: ldur            w0, [x1, #0xf]
    // 0xbaf9d8: DecompressPointer r0
    //     0xbaf9d8: add             x0, x0, HEAP, lsl #32
    // 0xbaf9dc: cmp             w0, NULL
    // 0xbaf9e0: b.ne            #0xbaf9ec
    // 0xbaf9e4: r0 = Null
    //     0xbaf9e4: mov             x0, NULL
    // 0xbaf9e8: b               #0xbafa18
    // 0xbaf9ec: r1 = LoadClassIdInstr(r0)
    //     0xbaf9ec: ldur            x1, [x0, #-1]
    //     0xbaf9f0: ubfx            x1, x1, #0xc, #0x14
    // 0xbaf9f4: mov             x16, x0
    // 0xbaf9f8: mov             x0, x1
    // 0xbaf9fc: mov             x1, x16
    // 0xbafa00: r2 = "landmark"
    //     0xbafa00: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xbafa04: ldr             x2, [x2, #0x930]
    // 0xbafa08: r0 = GDT[cid_x0 + 0xe437]()
    //     0xbafa08: movz            x17, #0xe437
    //     0xbafa0c: add             lr, x0, x17
    //     0xbafa10: ldr             lr, [x21, lr, lsl #3]
    //     0xbafa14: blr             lr
    // 0xbafa18: cmp             w0, NULL
    // 0xbafa1c: b.ne            #0xbafa24
    // 0xbafa20: r0 = false
    //     0xbafa20: add             x0, NULL, #0x30  ; false
    // 0xbafa24: ldur            x2, [fp, #-8]
    // 0xbafa28: stur            x0, [fp, #-0x40]
    // 0xbafa2c: LoadField: r1 = r2->field_1b
    //     0xbafa2c: ldur            w1, [x2, #0x1b]
    // 0xbafa30: DecompressPointer r1
    //     0xbafa30: add             x1, x1, HEAP, lsl #32
    // 0xbafa34: stur            x1, [fp, #-0x20]
    // 0xbafa38: r0 = LengthLimitingTextInputFormatter()
    //     0xbafa38: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbafa3c: mov             x1, x0
    // 0xbafa40: r0 = 240
    //     0xbafa40: movz            x0, #0xf0
    // 0xbafa44: stur            x1, [fp, #-0x48]
    // 0xbafa48: StoreField: r1->field_7 = r0
    //     0xbafa48: stur            w0, [x1, #7]
    // 0xbafa4c: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbafa4c: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xbafa50: ldr             x16, [x16, #0xa8]
    // 0xbafa54: stp             x16, NULL, [SP, #0x20]
    // 0xbafa58: r16 = false
    //     0xbafa58: add             x16, NULL, #0x30  ; false
    // 0xbafa5c: r30 = true
    //     0xbafa5c: add             lr, NULL, #0x20  ; true
    // 0xbafa60: stp             lr, x16, [SP, #0x10]
    // 0xbafa64: r16 = false
    //     0xbafa64: add             x16, NULL, #0x30  ; false
    // 0xbafa68: r30 = false
    //     0xbafa68: add             lr, NULL, #0x30  ; false
    // 0xbafa6c: stp             lr, x16, [SP]
    // 0xbafa70: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbafa70: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbafa74: r0 = _RegExp()
    //     0xbafa74: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbafa78: stur            x0, [fp, #-0x50]
    // 0xbafa7c: r0 = FilteringTextInputFormatter()
    //     0xbafa7c: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbafa80: mov             x3, x0
    // 0xbafa84: ldur            x0, [fp, #-0x50]
    // 0xbafa88: stur            x3, [fp, #-0x58]
    // 0xbafa8c: StoreField: r3->field_b = r0
    //     0xbafa8c: stur            w0, [x3, #0xb]
    // 0xbafa90: r0 = true
    //     0xbafa90: add             x0, NULL, #0x20  ; true
    // 0xbafa94: StoreField: r3->field_7 = r0
    //     0xbafa94: stur            w0, [x3, #7]
    // 0xbafa98: r4 = ""
    //     0xbafa98: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbafa9c: StoreField: r3->field_f = r4
    //     0xbafa9c: stur            w4, [x3, #0xf]
    // 0xbafaa0: r1 = Null
    //     0xbafaa0: mov             x1, NULL
    // 0xbafaa4: r2 = 4
    //     0xbafaa4: movz            x2, #0x4
    // 0xbafaa8: r0 = AllocateArray()
    //     0xbafaa8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbafaac: mov             x2, x0
    // 0xbafab0: ldur            x0, [fp, #-0x48]
    // 0xbafab4: stur            x2, [fp, #-0x50]
    // 0xbafab8: StoreField: r2->field_f = r0
    //     0xbafab8: stur            w0, [x2, #0xf]
    // 0xbafabc: ldur            x0, [fp, #-0x58]
    // 0xbafac0: StoreField: r2->field_13 = r0
    //     0xbafac0: stur            w0, [x2, #0x13]
    // 0xbafac4: r1 = <TextInputFormatter>
    //     0xbafac4: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbafac8: ldr             x1, [x1, #0x7b0]
    // 0xbafacc: r0 = AllocateGrowableArray()
    //     0xbafacc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbafad0: mov             x2, x0
    // 0xbafad4: ldur            x0, [fp, #-0x50]
    // 0xbafad8: stur            x2, [fp, #-0x58]
    // 0xbafadc: StoreField: r2->field_f = r0
    //     0xbafadc: stur            w0, [x2, #0xf]
    // 0xbafae0: r0 = 4
    //     0xbafae0: movz            x0, #0x4
    // 0xbafae4: StoreField: r2->field_b = r0
    //     0xbafae4: stur            w0, [x2, #0xb]
    // 0xbafae8: ldur            x0, [fp, #-8]
    // 0xbafaec: LoadField: r3 = r0->field_37
    //     0xbafaec: ldur            w3, [x0, #0x37]
    // 0xbafaf0: DecompressPointer r3
    //     0xbafaf0: add             x3, x3, HEAP, lsl #32
    // 0xbafaf4: ldur            x1, [fp, #-0x10]
    // 0xbafaf8: stur            x3, [fp, #-0x48]
    // 0xbafafc: r0 = of()
    //     0xbafafc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbafb00: LoadField: r1 = r0->field_87
    //     0xbafb00: ldur            w1, [x0, #0x87]
    // 0xbafb04: DecompressPointer r1
    //     0xbafb04: add             x1, x1, HEAP, lsl #32
    // 0xbafb08: LoadField: r0 = r1->field_2b
    //     0xbafb08: ldur            w0, [x1, #0x2b]
    // 0xbafb0c: DecompressPointer r0
    //     0xbafb0c: add             x0, x0, HEAP, lsl #32
    // 0xbafb10: ldur            x1, [fp, #-0x10]
    // 0xbafb14: stur            x0, [fp, #-0x50]
    // 0xbafb18: r0 = of()
    //     0xbafb18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbafb1c: LoadField: r1 = r0->field_5b
    //     0xbafb1c: ldur            w1, [x0, #0x5b]
    // 0xbafb20: DecompressPointer r1
    //     0xbafb20: add             x1, x1, HEAP, lsl #32
    // 0xbafb24: r16 = 14.000000
    //     0xbafb24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbafb28: ldr             x16, [x16, #0x1d8]
    // 0xbafb2c: stp             x1, x16, [SP]
    // 0xbafb30: ldur            x1, [fp, #-0x50]
    // 0xbafb34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbafb34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbafb38: ldr             x4, [x4, #0xaa0]
    // 0xbafb3c: r0 = copyWith()
    //     0xbafb3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbafb40: ldur            x1, [fp, #-0x10]
    // 0xbafb44: stur            x0, [fp, #-0x50]
    // 0xbafb48: r0 = getTextFormFieldInputDecoration()
    //     0xbafb48: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbafb4c: ldur            x1, [fp, #-0x10]
    // 0xbafb50: stur            x0, [fp, #-0x60]
    // 0xbafb54: r0 = of()
    //     0xbafb54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbafb58: LoadField: r1 = r0->field_5b
    //     0xbafb58: ldur            w1, [x0, #0x5b]
    // 0xbafb5c: DecompressPointer r1
    //     0xbafb5c: add             x1, x1, HEAP, lsl #32
    // 0xbafb60: stur            x1, [fp, #-0x68]
    // 0xbafb64: r0 = BorderSide()
    //     0xbafb64: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbafb68: mov             x1, x0
    // 0xbafb6c: ldur            x0, [fp, #-0x68]
    // 0xbafb70: stur            x1, [fp, #-0x70]
    // 0xbafb74: StoreField: r1->field_7 = r0
    //     0xbafb74: stur            w0, [x1, #7]
    // 0xbafb78: d0 = 1.000000
    //     0xbafb78: fmov            d0, #1.00000000
    // 0xbafb7c: StoreField: r1->field_b = d0
    //     0xbafb7c: stur            d0, [x1, #0xb]
    // 0xbafb80: r0 = Instance_BorderStyle
    //     0xbafb80: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbafb84: ldr             x0, [x0, #0xf68]
    // 0xbafb88: StoreField: r1->field_13 = r0
    //     0xbafb88: stur            w0, [x1, #0x13]
    // 0xbafb8c: d1 = -1.000000
    //     0xbafb8c: fmov            d1, #-1.00000000
    // 0xbafb90: ArrayStore: r1[0] = d1  ; List_8
    //     0xbafb90: stur            d1, [x1, #0x17]
    // 0xbafb94: r0 = OutlineInputBorder()
    //     0xbafb94: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbafb98: mov             x2, x0
    // 0xbafb9c: r0 = Instance_BorderRadius
    //     0xbafb9c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbafba0: ldr             x0, [x0, #0xf70]
    // 0xbafba4: stur            x2, [fp, #-0x78]
    // 0xbafba8: StoreField: r2->field_13 = r0
    //     0xbafba8: stur            w0, [x2, #0x13]
    // 0xbafbac: d0 = 4.000000
    //     0xbafbac: fmov            d0, #4.00000000
    // 0xbafbb0: StoreField: r2->field_b = d0
    //     0xbafbb0: stur            d0, [x2, #0xb]
    // 0xbafbb4: ldur            x1, [fp, #-0x70]
    // 0xbafbb8: StoreField: r2->field_7 = r1
    //     0xbafbb8: stur            w1, [x2, #7]
    // 0xbafbbc: ldur            x3, [fp, #-8]
    // 0xbafbc0: LoadField: r1 = r3->field_37
    //     0xbafbc0: ldur            w1, [x3, #0x37]
    // 0xbafbc4: DecompressPointer r1
    //     0xbafbc4: add             x1, x1, HEAP, lsl #32
    // 0xbafbc8: LoadField: r4 = r1->field_27
    //     0xbafbc8: ldur            w4, [x1, #0x27]
    // 0xbafbcc: DecompressPointer r4
    //     0xbafbcc: add             x4, x4, HEAP, lsl #32
    // 0xbafbd0: LoadField: r1 = r4->field_7
    //     0xbafbd0: ldur            w1, [x4, #7]
    // 0xbafbd4: DecompressPointer r1
    //     0xbafbd4: add             x1, x1, HEAP, lsl #32
    // 0xbafbd8: LoadField: r4 = r1->field_7
    //     0xbafbd8: ldur            w4, [x1, #7]
    // 0xbafbdc: cbnz            w4, #0xbafbec
    // 0xbafbe0: r4 = "Landmark"
    //     0xbafbe0: add             x4, PP, #0x54, lsl #12  ; [pp+0x54118] "Landmark"
    //     0xbafbe4: ldr             x4, [x4, #0x118]
    // 0xbafbe8: b               #0xbafbf4
    // 0xbafbec: r4 = "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xbafbec: add             x4, PP, #0x54, lsl #12  ; [pp+0x54120] "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xbafbf0: ldr             x4, [x4, #0x120]
    // 0xbafbf4: ldur            x1, [fp, #-0x10]
    // 0xbafbf8: stur            x4, [fp, #-0x68]
    // 0xbafbfc: r0 = of()
    //     0xbafbfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbafc00: LoadField: r1 = r0->field_87
    //     0xbafc00: ldur            w1, [x0, #0x87]
    // 0xbafc04: DecompressPointer r1
    //     0xbafc04: add             x1, x1, HEAP, lsl #32
    // 0xbafc08: LoadField: r0 = r1->field_2b
    //     0xbafc08: ldur            w0, [x1, #0x2b]
    // 0xbafc0c: DecompressPointer r0
    //     0xbafc0c: add             x0, x0, HEAP, lsl #32
    // 0xbafc10: stur            x0, [fp, #-0x70]
    // 0xbafc14: r1 = Instance_Color
    //     0xbafc14: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbafc18: d0 = 0.400000
    //     0xbafc18: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbafc1c: r0 = withOpacity()
    //     0xbafc1c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbafc20: r16 = 12.000000
    //     0xbafc20: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbafc24: ldr             x16, [x16, #0x9e8]
    // 0xbafc28: stp             x0, x16, [SP]
    // 0xbafc2c: ldur            x1, [fp, #-0x70]
    // 0xbafc30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbafc30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbafc34: ldr             x4, [x4, #0xaa0]
    // 0xbafc38: r0 = copyWith()
    //     0xbafc38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbafc3c: ldur            x1, [fp, #-0x10]
    // 0xbafc40: stur            x0, [fp, #-0x70]
    // 0xbafc44: r0 = of()
    //     0xbafc44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbafc48: LoadField: r1 = r0->field_87
    //     0xbafc48: ldur            w1, [x0, #0x87]
    // 0xbafc4c: DecompressPointer r1
    //     0xbafc4c: add             x1, x1, HEAP, lsl #32
    // 0xbafc50: LoadField: r0 = r1->field_2b
    //     0xbafc50: ldur            w0, [x1, #0x2b]
    // 0xbafc54: DecompressPointer r0
    //     0xbafc54: add             x0, x0, HEAP, lsl #32
    // 0xbafc58: r16 = 12.000000
    //     0xbafc58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbafc5c: ldr             x16, [x16, #0x9e8]
    // 0xbafc60: r30 = Instance_MaterialColor
    //     0xbafc60: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbafc64: ldr             lr, [lr, #0x180]
    // 0xbafc68: stp             lr, x16, [SP]
    // 0xbafc6c: mov             x1, x0
    // 0xbafc70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbafc70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbafc74: ldr             x4, [x4, #0xaa0]
    // 0xbafc78: r0 = copyWith()
    //     0xbafc78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbafc7c: ldur            x2, [fp, #-8]
    // 0xbafc80: stur            x0, [fp, #-0x90]
    // 0xbafc84: LoadField: r1 = r2->field_5f
    //     0xbafc84: ldur            w1, [x2, #0x5f]
    // 0xbafc88: DecompressPointer r1
    //     0xbafc88: add             x1, x1, HEAP, lsl #32
    // 0xbafc8c: tbnz            w1, #4, #0xbafd24
    // 0xbafc90: LoadField: r1 = r2->field_37
    //     0xbafc90: ldur            w1, [x2, #0x37]
    // 0xbafc94: DecompressPointer r1
    //     0xbafc94: add             x1, x1, HEAP, lsl #32
    // 0xbafc98: LoadField: r3 = r1->field_27
    //     0xbafc98: ldur            w3, [x1, #0x27]
    // 0xbafc9c: DecompressPointer r3
    //     0xbafc9c: add             x3, x3, HEAP, lsl #32
    // 0xbafca0: LoadField: r1 = r3->field_7
    //     0xbafca0: ldur            w1, [x3, #7]
    // 0xbafca4: DecompressPointer r1
    //     0xbafca4: add             x1, x1, HEAP, lsl #32
    // 0xbafca8: LoadField: r3 = r1->field_7
    //     0xbafca8: ldur            w3, [x1, #7]
    // 0xbafcac: cbz             w3, #0xbafcc8
    // 0xbafcb0: r1 = LoadInt32Instr(r3)
    //     0xbafcb0: sbfx            x1, x3, #1, #0x1f
    // 0xbafcb4: cmp             x1, #5
    // 0xbafcb8: b.lt            #0xbafcc8
    // 0xbafcbc: r1 = Instance_IconData
    //     0xbafcbc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbafcc0: ldr             x1, [x1, #0x130]
    // 0xbafcc4: b               #0xbafcdc
    // 0xbafcc8: cbz             w3, #0xbafcd8
    // 0xbafccc: r1 = Instance_IconData
    //     0xbafccc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbafcd0: ldr             x1, [x1, #0x138]
    // 0xbafcd4: b               #0xbafcdc
    // 0xbafcd8: r1 = Null
    //     0xbafcd8: mov             x1, NULL
    // 0xbafcdc: stur            x1, [fp, #-0x88]
    // 0xbafce0: cbz             w3, #0xbafcfc
    // 0xbafce4: r4 = LoadInt32Instr(r3)
    //     0xbafce4: sbfx            x4, x3, #1, #0x1f
    // 0xbafce8: cmp             x4, #5
    // 0xbafcec: b.lt            #0xbafcfc
    // 0xbafcf0: r3 = Instance_Color
    //     0xbafcf0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbafcf4: ldr             x3, [x3, #0x858]
    // 0xbafcf8: b               #0xbafd04
    // 0xbafcfc: r3 = Instance_Color
    //     0xbafcfc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbafd00: ldr             x3, [x3, #0x50]
    // 0xbafd04: stur            x3, [fp, #-0x80]
    // 0xbafd08: r0 = Icon()
    //     0xbafd08: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbafd0c: mov             x1, x0
    // 0xbafd10: ldur            x0, [fp, #-0x88]
    // 0xbafd14: StoreField: r1->field_b = r0
    //     0xbafd14: stur            w0, [x1, #0xb]
    // 0xbafd18: ldur            x0, [fp, #-0x80]
    // 0xbafd1c: StoreField: r1->field_23 = r0
    //     0xbafd1c: stur            w0, [x1, #0x23]
    // 0xbafd20: b               #0xbafd28
    // 0xbafd24: r1 = Null
    //     0xbafd24: mov             x1, NULL
    // 0xbafd28: ldur            x2, [fp, #-8]
    // 0xbafd2c: ldur            x0, [fp, #-0x40]
    // 0xbafd30: ldur            x3, [fp, #-0x20]
    // 0xbafd34: ldur            x16, [fp, #-0x78]
    // 0xbafd38: r30 = Instance_EdgeInsets
    //     0xbafd38: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbafd3c: ldr             lr, [lr, #0xc40]
    // 0xbafd40: stp             lr, x16, [SP, #0x20]
    // 0xbafd44: ldur            x16, [fp, #-0x68]
    // 0xbafd48: ldur            lr, [fp, #-0x70]
    // 0xbafd4c: stp             lr, x16, [SP, #0x10]
    // 0xbafd50: ldur            x16, [fp, #-0x90]
    // 0xbafd54: stp             x1, x16, [SP]
    // 0xbafd58: ldur            x1, [fp, #-0x60]
    // 0xbafd5c: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x6, null]
    //     0xbafd5c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54050] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x6, Null]
    //     0xbafd60: ldr             x4, [x4, #0x50]
    // 0xbafd64: r0 = copyWith()
    //     0xbafd64: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbafd68: ldur            x2, [fp, #-8]
    // 0xbafd6c: r1 = Function '_validateLandmark@1659256992':.
    //     0xbafd6c: add             x1, PP, #0x54, lsl #12  ; [pp+0x548d0] AnonymousClosure: (0xbb0c64), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateLandmark (0xa01e20)
    //     0xbafd70: ldr             x1, [x1, #0x8d0]
    // 0xbafd74: stur            x0, [fp, #-0x60]
    // 0xbafd78: r0 = AllocateClosure()
    //     0xbafd78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbafd7c: ldur            x2, [fp, #-0x18]
    // 0xbafd80: r1 = Function '<anonymous closure>':.
    //     0xbafd80: add             x1, PP, #0x54, lsl #12  ; [pp+0x548d8] AnonymousClosure: (0xbb0bec), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbafd84: ldr             x1, [x1, #0x8d8]
    // 0xbafd88: stur            x0, [fp, #-0x68]
    // 0xbafd8c: r0 = AllocateClosure()
    //     0xbafd8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbafd90: r1 = <String>
    //     0xbafd90: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbafd94: stur            x0, [fp, #-0x70]
    // 0xbafd98: r0 = TextFormField()
    //     0xbafd98: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbafd9c: stur            x0, [fp, #-0x78]
    // 0xbafda0: ldur            x16, [fp, #-0x68]
    // 0xbafda4: r30 = true
    //     0xbafda4: add             lr, NULL, #0x20  ; true
    // 0xbafda8: stp             lr, x16, [SP, #0x38]
    // 0xbafdac: r16 = Instance_AutovalidateMode
    //     0xbafdac: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbafdb0: ldr             x16, [x16, #0x7e8]
    // 0xbafdb4: ldur            lr, [fp, #-0x58]
    // 0xbafdb8: stp             lr, x16, [SP, #0x28]
    // 0xbafdbc: r16 = Instance_TextInputType
    //     0xbafdbc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xbafdc0: ldr             x16, [x16, #0x68]
    // 0xbafdc4: r30 = 2
    //     0xbafdc4: movz            lr, #0x2
    // 0xbafdc8: stp             lr, x16, [SP, #0x18]
    // 0xbafdcc: ldur            x16, [fp, #-0x48]
    // 0xbafdd0: ldur            lr, [fp, #-0x50]
    // 0xbafdd4: stp             lr, x16, [SP, #8]
    // 0xbafdd8: ldur            x16, [fp, #-0x70]
    // 0xbafddc: str             x16, [SP]
    // 0xbafde0: mov             x1, x0
    // 0xbafde4: ldur            x2, [fp, #-0x60]
    // 0xbafde8: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x8, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x7, onChanged, 0xa, style, 0x9, validator, 0x2, null]
    //     0xbafde8: add             x4, PP, #0x54, lsl #12  ; [pp+0x54138] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x8, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x7, "onChanged", 0xa, "style", 0x9, "validator", 0x2, Null]
    //     0xbafdec: ldr             x4, [x4, #0x138]
    // 0xbafdf0: r0 = TextFormField()
    //     0xbafdf0: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbafdf4: r0 = Form()
    //     0xbafdf4: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbafdf8: mov             x1, x0
    // 0xbafdfc: ldur            x0, [fp, #-0x78]
    // 0xbafe00: stur            x1, [fp, #-0x48]
    // 0xbafe04: StoreField: r1->field_b = r0
    //     0xbafe04: stur            w0, [x1, #0xb]
    // 0xbafe08: r0 = Instance_AutovalidateMode
    //     0xbafe08: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbafe0c: ldr             x0, [x0, #0x800]
    // 0xbafe10: StoreField: r1->field_23 = r0
    //     0xbafe10: stur            w0, [x1, #0x23]
    // 0xbafe14: ldur            x2, [fp, #-0x20]
    // 0xbafe18: StoreField: r1->field_7 = r2
    //     0xbafe18: stur            w2, [x1, #7]
    // 0xbafe1c: r0 = Padding()
    //     0xbafe1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbafe20: mov             x1, x0
    // 0xbafe24: r0 = Instance_EdgeInsets
    //     0xbafe24: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be0] Obj!EdgeInsets@d57da1
    //     0xbafe28: ldr             x0, [x0, #0xbe0]
    // 0xbafe2c: stur            x1, [fp, #-0x20]
    // 0xbafe30: StoreField: r1->field_f = r0
    //     0xbafe30: stur            w0, [x1, #0xf]
    // 0xbafe34: ldur            x0, [fp, #-0x48]
    // 0xbafe38: StoreField: r1->field_b = r0
    //     0xbafe38: stur            w0, [x1, #0xb]
    // 0xbafe3c: r0 = Visibility()
    //     0xbafe3c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbafe40: mov             x3, x0
    // 0xbafe44: ldur            x0, [fp, #-0x20]
    // 0xbafe48: stur            x3, [fp, #-0x48]
    // 0xbafe4c: StoreField: r3->field_b = r0
    //     0xbafe4c: stur            w0, [x3, #0xb]
    // 0xbafe50: r4 = Instance_SizedBox
    //     0xbafe50: ldr             x4, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbafe54: StoreField: r3->field_f = r4
    //     0xbafe54: stur            w4, [x3, #0xf]
    // 0xbafe58: ldur            x0, [fp, #-0x40]
    // 0xbafe5c: StoreField: r3->field_13 = r0
    //     0xbafe5c: stur            w0, [x3, #0x13]
    // 0xbafe60: r5 = false
    //     0xbafe60: add             x5, NULL, #0x30  ; false
    // 0xbafe64: ArrayStore: r3[0] = r5  ; List_4
    //     0xbafe64: stur            w5, [x3, #0x17]
    // 0xbafe68: StoreField: r3->field_1b = r5
    //     0xbafe68: stur            w5, [x3, #0x1b]
    // 0xbafe6c: StoreField: r3->field_1f = r5
    //     0xbafe6c: stur            w5, [x3, #0x1f]
    // 0xbafe70: StoreField: r3->field_23 = r5
    //     0xbafe70: stur            w5, [x3, #0x23]
    // 0xbafe74: StoreField: r3->field_27 = r5
    //     0xbafe74: stur            w5, [x3, #0x27]
    // 0xbafe78: StoreField: r3->field_2b = r5
    //     0xbafe78: stur            w5, [x3, #0x2b]
    // 0xbafe7c: ldur            x6, [fp, #-8]
    // 0xbafe80: LoadField: r0 = r6->field_b
    //     0xbafe80: ldur            w0, [x6, #0xb]
    // 0xbafe84: DecompressPointer r0
    //     0xbafe84: add             x0, x0, HEAP, lsl #32
    // 0xbafe88: cmp             w0, NULL
    // 0xbafe8c: b.eq            #0xbb0734
    // 0xbafe90: LoadField: r1 = r0->field_b
    //     0xbafe90: ldur            w1, [x0, #0xb]
    // 0xbafe94: DecompressPointer r1
    //     0xbafe94: add             x1, x1, HEAP, lsl #32
    // 0xbafe98: LoadField: r0 = r1->field_f
    //     0xbafe98: ldur            w0, [x1, #0xf]
    // 0xbafe9c: DecompressPointer r0
    //     0xbafe9c: add             x0, x0, HEAP, lsl #32
    // 0xbafea0: cmp             w0, NULL
    // 0xbafea4: b.ne            #0xbafeb0
    // 0xbafea8: r0 = Null
    //     0xbafea8: mov             x0, NULL
    // 0xbafeac: b               #0xbafedc
    // 0xbafeb0: r1 = LoadClassIdInstr(r0)
    //     0xbafeb0: ldur            x1, [x0, #-1]
    //     0xbafeb4: ubfx            x1, x1, #0xc, #0x14
    // 0xbafeb8: mov             x16, x0
    // 0xbafebc: mov             x0, x1
    // 0xbafec0: mov             x1, x16
    // 0xbafec4: r2 = "alternate_contact_number"
    //     0xbafec4: add             x2, PP, #0x54, lsl #12  ; [pp+0x54140] "alternate_contact_number"
    //     0xbafec8: ldr             x2, [x2, #0x140]
    // 0xbafecc: r0 = GDT[cid_x0 + 0xe437]()
    //     0xbafecc: movz            x17, #0xe437
    //     0xbafed0: add             lr, x0, x17
    //     0xbafed4: ldr             lr, [x21, lr, lsl #3]
    //     0xbafed8: blr             lr
    // 0xbafedc: cmp             w0, NULL
    // 0xbafee0: b.ne            #0xbafeec
    // 0xbafee4: r1 = false
    //     0xbafee4: add             x1, NULL, #0x30  ; false
    // 0xbafee8: b               #0xbafef0
    // 0xbafeec: mov             x1, x0
    // 0xbafef0: ldur            x2, [fp, #-8]
    // 0xbafef4: ldur            x0, [fp, #-0x30]
    // 0xbafef8: stur            x1, [fp, #-0x40]
    // 0xbafefc: LoadField: r3 = r2->field_2b
    //     0xbafefc: ldur            w3, [x2, #0x2b]
    // 0xbaff00: DecompressPointer r3
    //     0xbaff00: add             x3, x3, HEAP, lsl #32
    // 0xbaff04: stur            x3, [fp, #-0x20]
    // 0xbaff08: r16 = "[0-9]"
    //     0xbaff08: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xbaff0c: ldr             x16, [x16, #0x128]
    // 0xbaff10: stp             x16, NULL, [SP, #0x20]
    // 0xbaff14: r16 = false
    //     0xbaff14: add             x16, NULL, #0x30  ; false
    // 0xbaff18: r30 = true
    //     0xbaff18: add             lr, NULL, #0x20  ; true
    // 0xbaff1c: stp             lr, x16, [SP, #0x10]
    // 0xbaff20: r16 = false
    //     0xbaff20: add             x16, NULL, #0x30  ; false
    // 0xbaff24: r30 = false
    //     0xbaff24: add             lr, NULL, #0x30  ; false
    // 0xbaff28: stp             lr, x16, [SP]
    // 0xbaff2c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbaff2c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbaff30: r0 = _RegExp()
    //     0xbaff30: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbaff34: stur            x0, [fp, #-0x50]
    // 0xbaff38: r0 = FilteringTextInputFormatter()
    //     0xbaff38: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbaff3c: mov             x1, x0
    // 0xbaff40: ldur            x0, [fp, #-0x50]
    // 0xbaff44: stur            x1, [fp, #-0x58]
    // 0xbaff48: StoreField: r1->field_b = r0
    //     0xbaff48: stur            w0, [x1, #0xb]
    // 0xbaff4c: r0 = true
    //     0xbaff4c: add             x0, NULL, #0x20  ; true
    // 0xbaff50: StoreField: r1->field_7 = r0
    //     0xbaff50: stur            w0, [x1, #7]
    // 0xbaff54: r2 = ""
    //     0xbaff54: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaff58: StoreField: r1->field_f = r2
    //     0xbaff58: stur            w2, [x1, #0xf]
    // 0xbaff5c: r0 = LengthLimitingTextInputFormatter()
    //     0xbaff5c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbaff60: mov             x3, x0
    // 0xbaff64: r0 = 20
    //     0xbaff64: movz            x0, #0x14
    // 0xbaff68: stur            x3, [fp, #-0x50]
    // 0xbaff6c: StoreField: r3->field_7 = r0
    //     0xbaff6c: stur            w0, [x3, #7]
    // 0xbaff70: r1 = Null
    //     0xbaff70: mov             x1, NULL
    // 0xbaff74: r2 = 6
    //     0xbaff74: movz            x2, #0x6
    // 0xbaff78: r0 = AllocateArray()
    //     0xbaff78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaff7c: mov             x2, x0
    // 0xbaff80: ldur            x0, [fp, #-0x30]
    // 0xbaff84: stur            x2, [fp, #-0x60]
    // 0xbaff88: StoreField: r2->field_f = r0
    //     0xbaff88: stur            w0, [x2, #0xf]
    // 0xbaff8c: ldur            x0, [fp, #-0x58]
    // 0xbaff90: StoreField: r2->field_13 = r0
    //     0xbaff90: stur            w0, [x2, #0x13]
    // 0xbaff94: ldur            x0, [fp, #-0x50]
    // 0xbaff98: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaff98: stur            w0, [x2, #0x17]
    // 0xbaff9c: r1 = <TextInputFormatter>
    //     0xbaff9c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbaffa0: ldr             x1, [x1, #0x7b0]
    // 0xbaffa4: r0 = AllocateGrowableArray()
    //     0xbaffa4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaffa8: mov             x2, x0
    // 0xbaffac: ldur            x0, [fp, #-0x60]
    // 0xbaffb0: stur            x2, [fp, #-0x50]
    // 0xbaffb4: StoreField: r2->field_f = r0
    //     0xbaffb4: stur            w0, [x2, #0xf]
    // 0xbaffb8: r0 = 6
    //     0xbaffb8: movz            x0, #0x6
    // 0xbaffbc: StoreField: r2->field_b = r0
    //     0xbaffbc: stur            w0, [x2, #0xb]
    // 0xbaffc0: ldur            x0, [fp, #-8]
    // 0xbaffc4: LoadField: r3 = r0->field_43
    //     0xbaffc4: ldur            w3, [x0, #0x43]
    // 0xbaffc8: DecompressPointer r3
    //     0xbaffc8: add             x3, x3, HEAP, lsl #32
    // 0xbaffcc: ldur            x1, [fp, #-0x10]
    // 0xbaffd0: stur            x3, [fp, #-0x30]
    // 0xbaffd4: r0 = of()
    //     0xbaffd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaffd8: LoadField: r1 = r0->field_87
    //     0xbaffd8: ldur            w1, [x0, #0x87]
    // 0xbaffdc: DecompressPointer r1
    //     0xbaffdc: add             x1, x1, HEAP, lsl #32
    // 0xbaffe0: LoadField: r0 = r1->field_2b
    //     0xbaffe0: ldur            w0, [x1, #0x2b]
    // 0xbaffe4: DecompressPointer r0
    //     0xbaffe4: add             x0, x0, HEAP, lsl #32
    // 0xbaffe8: ldur            x1, [fp, #-0x10]
    // 0xbaffec: stur            x0, [fp, #-0x58]
    // 0xbafff0: r0 = of()
    //     0xbafff0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbafff4: LoadField: r1 = r0->field_5b
    //     0xbafff4: ldur            w1, [x0, #0x5b]
    // 0xbafff8: DecompressPointer r1
    //     0xbafff8: add             x1, x1, HEAP, lsl #32
    // 0xbafffc: r16 = 14.000000
    //     0xbafffc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb0000: ldr             x16, [x16, #0x1d8]
    // 0xbb0004: stp             x1, x16, [SP]
    // 0xbb0008: ldur            x1, [fp, #-0x58]
    // 0xbb000c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb000c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb0010: ldr             x4, [x4, #0xaa0]
    // 0xbb0014: r0 = copyWith()
    //     0xbb0014: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb0018: ldur            x1, [fp, #-0x10]
    // 0xbb001c: stur            x0, [fp, #-0x58]
    // 0xbb0020: r0 = getTextFormFieldInputDecoration()
    //     0xbb0020: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb0024: ldur            x1, [fp, #-0x10]
    // 0xbb0028: stur            x0, [fp, #-0x60]
    // 0xbb002c: r0 = of()
    //     0xbb002c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb0030: LoadField: r1 = r0->field_5b
    //     0xbb0030: ldur            w1, [x0, #0x5b]
    // 0xbb0034: DecompressPointer r1
    //     0xbb0034: add             x1, x1, HEAP, lsl #32
    // 0xbb0038: stur            x1, [fp, #-0x68]
    // 0xbb003c: r0 = BorderSide()
    //     0xbb003c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb0040: mov             x1, x0
    // 0xbb0044: ldur            x0, [fp, #-0x68]
    // 0xbb0048: stur            x1, [fp, #-0x70]
    // 0xbb004c: StoreField: r1->field_7 = r0
    //     0xbb004c: stur            w0, [x1, #7]
    // 0xbb0050: d0 = 1.000000
    //     0xbb0050: fmov            d0, #1.00000000
    // 0xbb0054: StoreField: r1->field_b = d0
    //     0xbb0054: stur            d0, [x1, #0xb]
    // 0xbb0058: r0 = Instance_BorderStyle
    //     0xbb0058: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb005c: ldr             x0, [x0, #0xf68]
    // 0xbb0060: StoreField: r1->field_13 = r0
    //     0xbb0060: stur            w0, [x1, #0x13]
    // 0xbb0064: d0 = -1.000000
    //     0xbb0064: fmov            d0, #-1.00000000
    // 0xbb0068: ArrayStore: r1[0] = d0  ; List_8
    //     0xbb0068: stur            d0, [x1, #0x17]
    // 0xbb006c: r0 = OutlineInputBorder()
    //     0xbb006c: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb0070: mov             x2, x0
    // 0xbb0074: r0 = Instance_BorderRadius
    //     0xbb0074: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb0078: ldr             x0, [x0, #0xf70]
    // 0xbb007c: stur            x2, [fp, #-0x68]
    // 0xbb0080: StoreField: r2->field_13 = r0
    //     0xbb0080: stur            w0, [x2, #0x13]
    // 0xbb0084: d0 = 4.000000
    //     0xbb0084: fmov            d0, #4.00000000
    // 0xbb0088: StoreField: r2->field_b = d0
    //     0xbb0088: stur            d0, [x2, #0xb]
    // 0xbb008c: ldur            x0, [fp, #-0x70]
    // 0xbb0090: StoreField: r2->field_7 = r0
    //     0xbb0090: stur            w0, [x2, #7]
    // 0xbb0094: ldur            x1, [fp, #-0x10]
    // 0xbb0098: r0 = of()
    //     0xbb0098: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb009c: LoadField: r1 = r0->field_87
    //     0xbb009c: ldur            w1, [x0, #0x87]
    // 0xbb00a0: DecompressPointer r1
    //     0xbb00a0: add             x1, x1, HEAP, lsl #32
    // 0xbb00a4: LoadField: r0 = r1->field_2b
    //     0xbb00a4: ldur            w0, [x1, #0x2b]
    // 0xbb00a8: DecompressPointer r0
    //     0xbb00a8: add             x0, x0, HEAP, lsl #32
    // 0xbb00ac: stur            x0, [fp, #-0x70]
    // 0xbb00b0: r1 = Instance_Color
    //     0xbb00b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb00b4: d0 = 0.400000
    //     0xbb00b4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb00b8: r0 = withOpacity()
    //     0xbb00b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb00bc: r16 = 14.000000
    //     0xbb00bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb00c0: ldr             x16, [x16, #0x1d8]
    // 0xbb00c4: stp             x0, x16, [SP]
    // 0xbb00c8: ldur            x1, [fp, #-0x70]
    // 0xbb00cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb00cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb00d0: ldr             x4, [x4, #0xaa0]
    // 0xbb00d4: r0 = copyWith()
    //     0xbb00d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb00d8: ldur            x1, [fp, #-0x10]
    // 0xbb00dc: stur            x0, [fp, #-0x70]
    // 0xbb00e0: r0 = of()
    //     0xbb00e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb00e4: LoadField: r1 = r0->field_87
    //     0xbb00e4: ldur            w1, [x0, #0x87]
    // 0xbb00e8: DecompressPointer r1
    //     0xbb00e8: add             x1, x1, HEAP, lsl #32
    // 0xbb00ec: LoadField: r0 = r1->field_2b
    //     0xbb00ec: ldur            w0, [x1, #0x2b]
    // 0xbb00f0: DecompressPointer r0
    //     0xbb00f0: add             x0, x0, HEAP, lsl #32
    // 0xbb00f4: r16 = 12.000000
    //     0xbb00f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb00f8: ldr             x16, [x16, #0x9e8]
    // 0xbb00fc: r30 = Instance_MaterialColor
    //     0xbb00fc: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbb0100: ldr             lr, [lr, #0x180]
    // 0xbb0104: stp             lr, x16, [SP]
    // 0xbb0108: mov             x1, x0
    // 0xbb010c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb010c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb0110: ldr             x4, [x4, #0xaa0]
    // 0xbb0114: r0 = copyWith()
    //     0xbb0114: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb0118: mov             x3, x0
    // 0xbb011c: ldur            x0, [fp, #-8]
    // 0xbb0120: stur            x3, [fp, #-0x78]
    // 0xbb0124: LoadField: r1 = r0->field_63
    //     0xbb0124: ldur            w1, [x0, #0x63]
    // 0xbb0128: DecompressPointer r1
    //     0xbb0128: add             x1, x1, HEAP, lsl #32
    // 0xbb012c: tbnz            w1, #4, #0xbb0254
    // 0xbb0130: LoadField: r1 = r0->field_43
    //     0xbb0130: ldur            w1, [x0, #0x43]
    // 0xbb0134: DecompressPointer r1
    //     0xbb0134: add             x1, x1, HEAP, lsl #32
    // 0xbb0138: LoadField: r2 = r1->field_27
    //     0xbb0138: ldur            w2, [x1, #0x27]
    // 0xbb013c: DecompressPointer r2
    //     0xbb013c: add             x2, x2, HEAP, lsl #32
    // 0xbb0140: LoadField: r1 = r2->field_7
    //     0xbb0140: ldur            w1, [x2, #7]
    // 0xbb0144: DecompressPointer r1
    //     0xbb0144: add             x1, x1, HEAP, lsl #32
    // 0xbb0148: LoadField: r2 = r1->field_7
    //     0xbb0148: ldur            w2, [x1, #7]
    // 0xbb014c: cbz             w2, #0xbb0190
    // 0xbb0150: cmp             w2, #0x14
    // 0xbb0154: b.ne            #0xbb0190
    // 0xbb0158: r16 = 2
    //     0xbb0158: movz            x16, #0x2
    // 0xbb015c: str             x16, [SP]
    // 0xbb0160: r2 = 0
    //     0xbb0160: movz            x2, #0
    // 0xbb0164: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbb0164: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbb0168: r0 = substring()
    //     0xbb0168: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xbb016c: mov             x1, x0
    // 0xbb0170: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb0170: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb0174: r0 = parse()
    //     0xbb0174: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xbb0178: cmp             x0, #6
    // 0xbb017c: b.lt            #0xbb0190
    // 0xbb0180: ldur            x0, [fp, #-8]
    // 0xbb0184: r3 = Instance_IconData
    //     0xbb0184: add             x3, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb0188: ldr             x3, [x3, #0x130]
    // 0xbb018c: b               #0xbb01c8
    // 0xbb0190: ldur            x0, [fp, #-8]
    // 0xbb0194: LoadField: r1 = r0->field_43
    //     0xbb0194: ldur            w1, [x0, #0x43]
    // 0xbb0198: DecompressPointer r1
    //     0xbb0198: add             x1, x1, HEAP, lsl #32
    // 0xbb019c: LoadField: r2 = r1->field_27
    //     0xbb019c: ldur            w2, [x1, #0x27]
    // 0xbb01a0: DecompressPointer r2
    //     0xbb01a0: add             x2, x2, HEAP, lsl #32
    // 0xbb01a4: LoadField: r1 = r2->field_7
    //     0xbb01a4: ldur            w1, [x2, #7]
    // 0xbb01a8: DecompressPointer r1
    //     0xbb01a8: add             x1, x1, HEAP, lsl #32
    // 0xbb01ac: LoadField: r2 = r1->field_7
    //     0xbb01ac: ldur            w2, [x1, #7]
    // 0xbb01b0: cbz             w2, #0xbb01c0
    // 0xbb01b4: r1 = Instance_IconData
    //     0xbb01b4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb01b8: ldr             x1, [x1, #0x138]
    // 0xbb01bc: b               #0xbb01c4
    // 0xbb01c0: r1 = Null
    //     0xbb01c0: mov             x1, NULL
    // 0xbb01c4: mov             x3, x1
    // 0xbb01c8: stur            x3, [fp, #-0x80]
    // 0xbb01cc: LoadField: r1 = r0->field_43
    //     0xbb01cc: ldur            w1, [x0, #0x43]
    // 0xbb01d0: DecompressPointer r1
    //     0xbb01d0: add             x1, x1, HEAP, lsl #32
    // 0xbb01d4: LoadField: r2 = r1->field_27
    //     0xbb01d4: ldur            w2, [x1, #0x27]
    // 0xbb01d8: DecompressPointer r2
    //     0xbb01d8: add             x2, x2, HEAP, lsl #32
    // 0xbb01dc: LoadField: r1 = r2->field_7
    //     0xbb01dc: ldur            w1, [x2, #7]
    // 0xbb01e0: DecompressPointer r1
    //     0xbb01e0: add             x1, x1, HEAP, lsl #32
    // 0xbb01e4: LoadField: r2 = r1->field_7
    //     0xbb01e4: ldur            w2, [x1, #7]
    // 0xbb01e8: cbz             w2, #0xbb0228
    // 0xbb01ec: cmp             w2, #0x14
    // 0xbb01f0: b.ne            #0xbb0228
    // 0xbb01f4: r16 = 2
    //     0xbb01f4: movz            x16, #0x2
    // 0xbb01f8: str             x16, [SP]
    // 0xbb01fc: r2 = 0
    //     0xbb01fc: movz            x2, #0
    // 0xbb0200: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbb0200: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbb0204: r0 = substring()
    //     0xbb0204: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xbb0208: mov             x1, x0
    // 0xbb020c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb020c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb0210: r0 = parse()
    //     0xbb0210: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xbb0214: cmp             x0, #6
    // 0xbb0218: b.lt            #0xbb0228
    // 0xbb021c: r1 = Instance_Color
    //     0xbb021c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb0220: ldr             x1, [x1, #0x858]
    // 0xbb0224: b               #0xbb0230
    // 0xbb0228: r1 = Instance_Color
    //     0xbb0228: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb022c: ldr             x1, [x1, #0x50]
    // 0xbb0230: ldur            x0, [fp, #-0x80]
    // 0xbb0234: stur            x1, [fp, #-0x88]
    // 0xbb0238: r0 = Icon()
    //     0xbb0238: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb023c: mov             x1, x0
    // 0xbb0240: ldur            x0, [fp, #-0x80]
    // 0xbb0244: StoreField: r1->field_b = r0
    //     0xbb0244: stur            w0, [x1, #0xb]
    // 0xbb0248: ldur            x0, [fp, #-0x88]
    // 0xbb024c: StoreField: r1->field_23 = r0
    //     0xbb024c: stur            w0, [x1, #0x23]
    // 0xbb0250: b               #0xbb0258
    // 0xbb0254: r1 = Null
    //     0xbb0254: mov             x1, NULL
    // 0xbb0258: ldur            x2, [fp, #-8]
    // 0xbb025c: ldur            x0, [fp, #-0x40]
    // 0xbb0260: ldur            x3, [fp, #-0x20]
    // 0xbb0264: ldur            x16, [fp, #-0x68]
    // 0xbb0268: r30 = Instance_EdgeInsets
    //     0xbb0268: add             lr, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbb026c: ldr             lr, [lr, #0xc40]
    // 0xbb0270: stp             lr, x16, [SP, #0x20]
    // 0xbb0274: r16 = "Alternate Ph No"
    //     0xbb0274: add             x16, PP, #0x54, lsl #12  ; [pp+0x54148] "Alternate Ph No"
    //     0xbb0278: ldr             x16, [x16, #0x148]
    // 0xbb027c: ldur            lr, [fp, #-0x70]
    // 0xbb0280: stp             lr, x16, [SP, #0x10]
    // 0xbb0284: ldur            x16, [fp, #-0x78]
    // 0xbb0288: stp             x1, x16, [SP]
    // 0xbb028c: ldur            x1, [fp, #-0x60]
    // 0xbb0290: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x5, focusedBorder, 0x1, labelStyle, 0x4, labelText, 0x3, suffixIcon, 0x6, null]
    //     0xbb0290: add             x4, PP, #0x54, lsl #12  ; [pp+0x54050] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x5, "focusedBorder", 0x1, "labelStyle", 0x4, "labelText", 0x3, "suffixIcon", 0x6, Null]
    //     0xbb0294: ldr             x4, [x4, #0x50]
    // 0xbb0298: r0 = copyWith()
    //     0xbb0298: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb029c: ldur            x2, [fp, #-8]
    // 0xbb02a0: r1 = Function '_validateAlternateNo@1659256992':.
    //     0xbb02a0: add             x1, PP, #0x54, lsl #12  ; [pp+0x548e0] AnonymousClosure: (0xbb0bb0), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAlternateNo (0xa01af0)
    //     0xbb02a4: ldr             x1, [x1, #0x8e0]
    // 0xbb02a8: stur            x0, [fp, #-0x60]
    // 0xbb02ac: r0 = AllocateClosure()
    //     0xbb02ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb02b0: ldur            x2, [fp, #-0x18]
    // 0xbb02b4: r1 = Function '<anonymous closure>':.
    //     0xbb02b4: add             x1, PP, #0x54, lsl #12  ; [pp+0x548e8] AnonymousClosure: (0xbb0b38), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbb02b8: ldr             x1, [x1, #0x8e8]
    // 0xbb02bc: stur            x0, [fp, #-0x68]
    // 0xbb02c0: r0 = AllocateClosure()
    //     0xbb02c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb02c4: r1 = <String>
    //     0xbb02c4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb02c8: stur            x0, [fp, #-0x70]
    // 0xbb02cc: r0 = TextFormField()
    //     0xbb02cc: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb02d0: stur            x0, [fp, #-0x78]
    // 0xbb02d4: ldur            x16, [fp, #-0x68]
    // 0xbb02d8: r30 = true
    //     0xbb02d8: add             lr, NULL, #0x20  ; true
    // 0xbb02dc: stp             lr, x16, [SP, #0x38]
    // 0xbb02e0: r16 = Instance_AutovalidateMode
    //     0xbb02e0: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb02e4: ldr             x16, [x16, #0x7e8]
    // 0xbb02e8: ldur            lr, [fp, #-0x50]
    // 0xbb02ec: stp             lr, x16, [SP, #0x28]
    // 0xbb02f0: r16 = Instance_TextInputType
    //     0xbb02f0: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbb02f4: ldr             x16, [x16, #0x1a0]
    // 0xbb02f8: r30 = 2
    //     0xbb02f8: movz            lr, #0x2
    // 0xbb02fc: stp             lr, x16, [SP, #0x18]
    // 0xbb0300: ldur            x16, [fp, #-0x30]
    // 0xbb0304: ldur            lr, [fp, #-0x58]
    // 0xbb0308: stp             lr, x16, [SP, #8]
    // 0xbb030c: ldur            x16, [fp, #-0x70]
    // 0xbb0310: str             x16, [SP]
    // 0xbb0314: mov             x1, x0
    // 0xbb0318: ldur            x2, [fp, #-0x60]
    // 0xbb031c: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x8, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x7, onChanged, 0xa, style, 0x9, validator, 0x2, null]
    //     0xbb031c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54138] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x8, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x7, "onChanged", 0xa, "style", 0x9, "validator", 0x2, Null]
    //     0xbb0320: ldr             x4, [x4, #0x138]
    // 0xbb0324: r0 = TextFormField()
    //     0xbb0324: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb0328: r0 = Form()
    //     0xbb0328: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb032c: mov             x1, x0
    // 0xbb0330: ldur            x0, [fp, #-0x78]
    // 0xbb0334: stur            x1, [fp, #-0x30]
    // 0xbb0338: StoreField: r1->field_b = r0
    //     0xbb0338: stur            w0, [x1, #0xb]
    // 0xbb033c: r0 = Instance_AutovalidateMode
    //     0xbb033c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb0340: ldr             x0, [x0, #0x800]
    // 0xbb0344: StoreField: r1->field_23 = r0
    //     0xbb0344: stur            w0, [x1, #0x23]
    // 0xbb0348: ldur            x0, [fp, #-0x20]
    // 0xbb034c: StoreField: r1->field_7 = r0
    //     0xbb034c: stur            w0, [x1, #7]
    // 0xbb0350: r0 = Padding()
    //     0xbb0350: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb0354: mov             x1, x0
    // 0xbb0358: r0 = Instance_EdgeInsets
    //     0xbb0358: add             x0, PP, #0x42, lsl #12  ; [pp+0x427d8] Obj!EdgeInsets@d58971
    //     0xbb035c: ldr             x0, [x0, #0x7d8]
    // 0xbb0360: stur            x1, [fp, #-0x20]
    // 0xbb0364: StoreField: r1->field_f = r0
    //     0xbb0364: stur            w0, [x1, #0xf]
    // 0xbb0368: ldur            x0, [fp, #-0x30]
    // 0xbb036c: StoreField: r1->field_b = r0
    //     0xbb036c: stur            w0, [x1, #0xb]
    // 0xbb0370: r0 = Visibility()
    //     0xbb0370: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb0374: mov             x2, x0
    // 0xbb0378: ldur            x0, [fp, #-0x20]
    // 0xbb037c: stur            x2, [fp, #-0x30]
    // 0xbb0380: StoreField: r2->field_b = r0
    //     0xbb0380: stur            w0, [x2, #0xb]
    // 0xbb0384: r0 = Instance_SizedBox
    //     0xbb0384: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb0388: StoreField: r2->field_f = r0
    //     0xbb0388: stur            w0, [x2, #0xf]
    // 0xbb038c: ldur            x0, [fp, #-0x40]
    // 0xbb0390: StoreField: r2->field_13 = r0
    //     0xbb0390: stur            w0, [x2, #0x13]
    // 0xbb0394: r0 = false
    //     0xbb0394: add             x0, NULL, #0x30  ; false
    // 0xbb0398: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb0398: stur            w0, [x2, #0x17]
    // 0xbb039c: StoreField: r2->field_1b = r0
    //     0xbb039c: stur            w0, [x2, #0x1b]
    // 0xbb03a0: StoreField: r2->field_1f = r0
    //     0xbb03a0: stur            w0, [x2, #0x1f]
    // 0xbb03a4: StoreField: r2->field_23 = r0
    //     0xbb03a4: stur            w0, [x2, #0x23]
    // 0xbb03a8: StoreField: r2->field_27 = r0
    //     0xbb03a8: stur            w0, [x2, #0x27]
    // 0xbb03ac: StoreField: r2->field_2b = r0
    //     0xbb03ac: stur            w0, [x2, #0x2b]
    // 0xbb03b0: ldur            x1, [fp, #-0x10]
    // 0xbb03b4: r0 = of()
    //     0xbb03b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb03b8: LoadField: r1 = r0->field_5b
    //     0xbb03b8: ldur            w1, [x0, #0x5b]
    // 0xbb03bc: DecompressPointer r1
    //     0xbb03bc: add             x1, x1, HEAP, lsl #32
    // 0xbb03c0: stur            x1, [fp, #-0x20]
    // 0xbb03c4: r0 = Divider()
    //     0xbb03c4: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbb03c8: mov             x3, x0
    // 0xbb03cc: ldur            x0, [fp, #-0x20]
    // 0xbb03d0: stur            x3, [fp, #-0x40]
    // 0xbb03d4: StoreField: r3->field_1f = r0
    //     0xbb03d4: stur            w0, [x3, #0x1f]
    // 0xbb03d8: ldur            x0, [fp, #-8]
    // 0xbb03dc: LoadField: r1 = r0->field_4b
    //     0xbb03dc: ldur            w1, [x0, #0x4b]
    // 0xbb03e0: DecompressPointer r1
    //     0xbb03e0: add             x1, x1, HEAP, lsl #32
    // 0xbb03e4: tbnz            w1, #4, #0xbb0400
    // 0xbb03e8: ldur            x2, [fp, #-0x18]
    // 0xbb03ec: r1 = Function '<anonymous closure>':.
    //     0xbb03ec: add             x1, PP, #0x54, lsl #12  ; [pp+0x548f0] AnonymousClosure: (0xbb09b0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::build (0xbaddd0)
    //     0xbb03f0: ldr             x1, [x1, #0x8f0]
    // 0xbb03f4: r0 = AllocateClosure()
    //     0xbb03f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb03f8: mov             x1, x0
    // 0xbb03fc: b               #0xbb0404
    // 0xbb0400: r1 = Null
    //     0xbb0400: mov             x1, NULL
    // 0xbb0404: ldur            x0, [fp, #-8]
    // 0xbb0408: stur            x1, [fp, #-0x18]
    // 0xbb040c: r16 = <EdgeInsets>
    //     0xbb040c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbb0410: ldr             x16, [x16, #0xda0]
    // 0xbb0414: r30 = Instance_EdgeInsets
    //     0xbb0414: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbb0418: ldr             lr, [lr, #0x1f0]
    // 0xbb041c: stp             lr, x16, [SP]
    // 0xbb0420: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb0420: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb0424: r0 = all()
    //     0xbb0424: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb0428: ldur            x1, [fp, #-0x10]
    // 0xbb042c: stur            x0, [fp, #-0x20]
    // 0xbb0430: r0 = of()
    //     0xbb0430: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb0434: LoadField: r1 = r0->field_5b
    //     0xbb0434: ldur            w1, [x0, #0x5b]
    // 0xbb0438: DecompressPointer r1
    //     0xbb0438: add             x1, x1, HEAP, lsl #32
    // 0xbb043c: r16 = <Color>
    //     0xbb043c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbb0440: ldr             x16, [x16, #0xf80]
    // 0xbb0444: stp             x1, x16, [SP]
    // 0xbb0448: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb0448: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb044c: r0 = all()
    //     0xbb044c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb0450: mov             x2, x0
    // 0xbb0454: ldur            x0, [fp, #-8]
    // 0xbb0458: stur            x2, [fp, #-0x50]
    // 0xbb045c: LoadField: r1 = r0->field_4b
    //     0xbb045c: ldur            w1, [x0, #0x4b]
    // 0xbb0460: DecompressPointer r1
    //     0xbb0460: add             x1, x1, HEAP, lsl #32
    // 0xbb0464: tbnz            w1, #4, #0xbb0480
    // 0xbb0468: ldur            x1, [fp, #-0x10]
    // 0xbb046c: r0 = of()
    //     0xbb046c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb0470: LoadField: r1 = r0->field_5b
    //     0xbb0470: ldur            w1, [x0, #0x5b]
    // 0xbb0474: DecompressPointer r1
    //     0xbb0474: add             x1, x1, HEAP, lsl #32
    // 0xbb0478: mov             x8, x1
    // 0xbb047c: b               #0xbb04ac
    // 0xbb0480: ldur            x1, [fp, #-0x10]
    // 0xbb0484: r0 = of()
    //     0xbb0484: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb0488: LoadField: r1 = r0->field_5b
    //     0xbb0488: ldur            w1, [x0, #0x5b]
    // 0xbb048c: DecompressPointer r1
    //     0xbb048c: add             x1, x1, HEAP, lsl #32
    // 0xbb0490: r0 = LoadClassIdInstr(r1)
    //     0xbb0490: ldur            x0, [x1, #-1]
    //     0xbb0494: ubfx            x0, x0, #0xc, #0x14
    // 0xbb0498: d0 = 0.100000
    //     0xbb0498: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbb049c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb049c: sub             lr, x0, #0xffa
    //     0xbb04a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbb04a4: blr             lr
    // 0xbb04a8: mov             x8, x0
    // 0xbb04ac: ldur            x7, [fp, #-0x28]
    // 0xbb04b0: ldur            x6, [fp, #-0x38]
    // 0xbb04b4: ldur            x5, [fp, #-0x48]
    // 0xbb04b8: ldur            x4, [fp, #-0x30]
    // 0xbb04bc: ldur            x3, [fp, #-0x40]
    // 0xbb04c0: ldur            x2, [fp, #-0x18]
    // 0xbb04c4: ldur            x1, [fp, #-0x20]
    // 0xbb04c8: ldur            x0, [fp, #-0x50]
    // 0xbb04cc: r16 = <Color>
    //     0xbb04cc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbb04d0: ldr             x16, [x16, #0xf80]
    // 0xbb04d4: stp             x8, x16, [SP]
    // 0xbb04d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb04d8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb04dc: r0 = all()
    //     0xbb04dc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb04e0: stur            x0, [fp, #-8]
    // 0xbb04e4: r16 = <RoundedRectangleBorder>
    //     0xbb04e4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbb04e8: ldr             x16, [x16, #0xf78]
    // 0xbb04ec: r30 = Instance_RoundedRectangleBorder
    //     0xbb04ec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbb04f0: ldr             lr, [lr, #0xd68]
    // 0xbb04f4: stp             lr, x16, [SP]
    // 0xbb04f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb04f8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb04fc: r0 = all()
    //     0xbb04fc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbb0500: stur            x0, [fp, #-0x58]
    // 0xbb0504: r0 = ButtonStyle()
    //     0xbb0504: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbb0508: mov             x2, x0
    // 0xbb050c: ldur            x0, [fp, #-8]
    // 0xbb0510: stur            x2, [fp, #-0x60]
    // 0xbb0514: StoreField: r2->field_b = r0
    //     0xbb0514: stur            w0, [x2, #0xb]
    // 0xbb0518: ldur            x0, [fp, #-0x50]
    // 0xbb051c: StoreField: r2->field_f = r0
    //     0xbb051c: stur            w0, [x2, #0xf]
    // 0xbb0520: ldur            x0, [fp, #-0x20]
    // 0xbb0524: StoreField: r2->field_23 = r0
    //     0xbb0524: stur            w0, [x2, #0x23]
    // 0xbb0528: ldur            x0, [fp, #-0x58]
    // 0xbb052c: StoreField: r2->field_43 = r0
    //     0xbb052c: stur            w0, [x2, #0x43]
    // 0xbb0530: ldur            x1, [fp, #-0x10]
    // 0xbb0534: r0 = of()
    //     0xbb0534: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb0538: LoadField: r1 = r0->field_87
    //     0xbb0538: ldur            w1, [x0, #0x87]
    // 0xbb053c: DecompressPointer r1
    //     0xbb053c: add             x1, x1, HEAP, lsl #32
    // 0xbb0540: LoadField: r0 = r1->field_7
    //     0xbb0540: ldur            w0, [x1, #7]
    // 0xbb0544: DecompressPointer r0
    //     0xbb0544: add             x0, x0, HEAP, lsl #32
    // 0xbb0548: r16 = 16.000000
    //     0xbb0548: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbb054c: ldr             x16, [x16, #0x188]
    // 0xbb0550: r30 = Instance_Color
    //     0xbb0550: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbb0554: stp             lr, x16, [SP]
    // 0xbb0558: mov             x1, x0
    // 0xbb055c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb055c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb0560: ldr             x4, [x4, #0xaa0]
    // 0xbb0564: r0 = copyWith()
    //     0xbb0564: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb0568: stur            x0, [fp, #-8]
    // 0xbb056c: r0 = Text()
    //     0xbb056c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb0570: mov             x1, x0
    // 0xbb0574: r0 = "CONFIRM"
    //     0xbb0574: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e98] "CONFIRM"
    //     0xbb0578: ldr             x0, [x0, #0xe98]
    // 0xbb057c: stur            x1, [fp, #-0x10]
    // 0xbb0580: StoreField: r1->field_b = r0
    //     0xbb0580: stur            w0, [x1, #0xb]
    // 0xbb0584: ldur            x0, [fp, #-8]
    // 0xbb0588: StoreField: r1->field_13 = r0
    //     0xbb0588: stur            w0, [x1, #0x13]
    // 0xbb058c: r0 = TextButton()
    //     0xbb058c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbb0590: mov             x1, x0
    // 0xbb0594: ldur            x0, [fp, #-0x18]
    // 0xbb0598: stur            x1, [fp, #-8]
    // 0xbb059c: StoreField: r1->field_b = r0
    //     0xbb059c: stur            w0, [x1, #0xb]
    // 0xbb05a0: ldur            x0, [fp, #-0x60]
    // 0xbb05a4: StoreField: r1->field_1b = r0
    //     0xbb05a4: stur            w0, [x1, #0x1b]
    // 0xbb05a8: r0 = false
    //     0xbb05a8: add             x0, NULL, #0x30  ; false
    // 0xbb05ac: StoreField: r1->field_27 = r0
    //     0xbb05ac: stur            w0, [x1, #0x27]
    // 0xbb05b0: r2 = true
    //     0xbb05b0: add             x2, NULL, #0x20  ; true
    // 0xbb05b4: StoreField: r1->field_2f = r2
    //     0xbb05b4: stur            w2, [x1, #0x2f]
    // 0xbb05b8: ldur            x2, [fp, #-0x10]
    // 0xbb05bc: StoreField: r1->field_37 = r2
    //     0xbb05bc: stur            w2, [x1, #0x37]
    // 0xbb05c0: r0 = SizedBox()
    //     0xbb05c0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb05c4: mov             x1, x0
    // 0xbb05c8: r0 = inf
    //     0xbb05c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbb05cc: ldr             x0, [x0, #0x9f8]
    // 0xbb05d0: stur            x1, [fp, #-0x10]
    // 0xbb05d4: StoreField: r1->field_f = r0
    //     0xbb05d4: stur            w0, [x1, #0xf]
    // 0xbb05d8: ldur            x0, [fp, #-8]
    // 0xbb05dc: StoreField: r1->field_b = r0
    //     0xbb05dc: stur            w0, [x1, #0xb]
    // 0xbb05e0: r0 = Padding()
    //     0xbb05e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb05e4: mov             x3, x0
    // 0xbb05e8: r0 = Instance_EdgeInsets
    //     0xbb05e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbb05ec: ldr             x0, [x0, #0x668]
    // 0xbb05f0: stur            x3, [fp, #-8]
    // 0xbb05f4: StoreField: r3->field_f = r0
    //     0xbb05f4: stur            w0, [x3, #0xf]
    // 0xbb05f8: ldur            x0, [fp, #-0x10]
    // 0xbb05fc: StoreField: r3->field_b = r0
    //     0xbb05fc: stur            w0, [x3, #0xb]
    // 0xbb0600: r1 = Null
    //     0xbb0600: mov             x1, NULL
    // 0xbb0604: r2 = 12
    //     0xbb0604: movz            x2, #0xc
    // 0xbb0608: r0 = AllocateArray()
    //     0xbb0608: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb060c: mov             x2, x0
    // 0xbb0610: ldur            x0, [fp, #-0x28]
    // 0xbb0614: stur            x2, [fp, #-0x10]
    // 0xbb0618: StoreField: r2->field_f = r0
    //     0xbb0618: stur            w0, [x2, #0xf]
    // 0xbb061c: ldur            x0, [fp, #-0x38]
    // 0xbb0620: StoreField: r2->field_13 = r0
    //     0xbb0620: stur            w0, [x2, #0x13]
    // 0xbb0624: ldur            x0, [fp, #-0x48]
    // 0xbb0628: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb0628: stur            w0, [x2, #0x17]
    // 0xbb062c: ldur            x0, [fp, #-0x30]
    // 0xbb0630: StoreField: r2->field_1b = r0
    //     0xbb0630: stur            w0, [x2, #0x1b]
    // 0xbb0634: ldur            x0, [fp, #-0x40]
    // 0xbb0638: StoreField: r2->field_1f = r0
    //     0xbb0638: stur            w0, [x2, #0x1f]
    // 0xbb063c: ldur            x0, [fp, #-8]
    // 0xbb0640: StoreField: r2->field_23 = r0
    //     0xbb0640: stur            w0, [x2, #0x23]
    // 0xbb0644: r1 = <Widget>
    //     0xbb0644: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb0648: r0 = AllocateGrowableArray()
    //     0xbb0648: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb064c: mov             x1, x0
    // 0xbb0650: ldur            x0, [fp, #-0x10]
    // 0xbb0654: stur            x1, [fp, #-8]
    // 0xbb0658: StoreField: r1->field_f = r0
    //     0xbb0658: stur            w0, [x1, #0xf]
    // 0xbb065c: r0 = 12
    //     0xbb065c: movz            x0, #0xc
    // 0xbb0660: StoreField: r1->field_b = r0
    //     0xbb0660: stur            w0, [x1, #0xb]
    // 0xbb0664: r0 = Wrap()
    //     0xbb0664: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xbb0668: mov             x1, x0
    // 0xbb066c: r0 = Instance_Axis
    //     0xbb066c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb0670: stur            x1, [fp, #-0x10]
    // 0xbb0674: StoreField: r1->field_f = r0
    //     0xbb0674: stur            w0, [x1, #0xf]
    // 0xbb0678: r0 = Instance_WrapAlignment
    //     0xbb0678: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xbb067c: ldr             x0, [x0, #0x6e8]
    // 0xbb0680: StoreField: r1->field_13 = r0
    //     0xbb0680: stur            w0, [x1, #0x13]
    // 0xbb0684: ArrayStore: r1[0] = rZR  ; List_8
    //     0xbb0684: stur            xzr, [x1, #0x17]
    // 0xbb0688: StoreField: r1->field_1f = r0
    //     0xbb0688: stur            w0, [x1, #0x1f]
    // 0xbb068c: StoreField: r1->field_23 = rZR
    //     0xbb068c: stur            xzr, [x1, #0x23]
    // 0xbb0690: r0 = Instance_WrapCrossAlignment
    //     0xbb0690: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xbb0694: ldr             x0, [x0, #0x6f0]
    // 0xbb0698: StoreField: r1->field_2b = r0
    //     0xbb0698: stur            w0, [x1, #0x2b]
    // 0xbb069c: r0 = Instance_VerticalDirection
    //     0xbb069c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb06a0: ldr             x0, [x0, #0xa20]
    // 0xbb06a4: StoreField: r1->field_33 = r0
    //     0xbb06a4: stur            w0, [x1, #0x33]
    // 0xbb06a8: r0 = Instance_Clip
    //     0xbb06a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb06ac: ldr             x0, [x0, #0x38]
    // 0xbb06b0: StoreField: r1->field_37 = r0
    //     0xbb06b0: stur            w0, [x1, #0x37]
    // 0xbb06b4: ldur            x0, [fp, #-8]
    // 0xbb06b8: StoreField: r1->field_b = r0
    //     0xbb06b8: stur            w0, [x1, #0xb]
    // 0xbb06bc: r0 = SingleChildScrollView()
    //     0xbb06bc: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbb06c0: r1 = Instance_Axis
    //     0xbb06c0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb06c4: StoreField: r0->field_b = r1
    //     0xbb06c4: stur            w1, [x0, #0xb]
    // 0xbb06c8: r1 = false
    //     0xbb06c8: add             x1, NULL, #0x30  ; false
    // 0xbb06cc: StoreField: r0->field_f = r1
    //     0xbb06cc: stur            w1, [x0, #0xf]
    // 0xbb06d0: ldur            x1, [fp, #-0x10]
    // 0xbb06d4: StoreField: r0->field_23 = r1
    //     0xbb06d4: stur            w1, [x0, #0x23]
    // 0xbb06d8: r1 = Instance_DragStartBehavior
    //     0xbb06d8: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xbb06dc: StoreField: r0->field_27 = r1
    //     0xbb06dc: stur            w1, [x0, #0x27]
    // 0xbb06e0: r1 = Instance_Clip
    //     0xbb06e0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbb06e4: ldr             x1, [x1, #0x7e0]
    // 0xbb06e8: StoreField: r0->field_2b = r1
    //     0xbb06e8: stur            w1, [x0, #0x2b]
    // 0xbb06ec: r1 = Instance_HitTestBehavior
    //     0xbb06ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xbb06f0: ldr             x1, [x1, #0x288]
    // 0xbb06f4: StoreField: r0->field_2f = r1
    //     0xbb06f4: stur            w1, [x0, #0x2f]
    // 0xbb06f8: LeaveFrame
    //     0xbb06f8: mov             SP, fp
    //     0xbb06fc: ldp             fp, lr, [SP], #0x10
    // 0xbb0700: ret
    //     0xbb0700: ret             
    // 0xbb0704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0704: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0708: b               #0xbaddfc
    // 0xbb070c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb070c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0714: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0718: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb071c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb071c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0720: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0720: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0724: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0724: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0728: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb072c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb072c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0730: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0734: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0734: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb09b0, size: 0x188
    // 0xbb09b0: EnterFrame
    //     0xbb09b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb09b4: mov             fp, SP
    // 0xbb09b8: AllocStack(0x40)
    //     0xbb09b8: sub             SP, SP, #0x40
    // 0xbb09bc: SetupParameters()
    //     0xbb09bc: ldr             x0, [fp, #0x10]
    //     0xbb09c0: ldur            w1, [x0, #0x17]
    //     0xbb09c4: add             x1, x1, HEAP, lsl #32
    //     0xbb09c8: stur            x1, [fp, #-8]
    // 0xbb09cc: CheckStackOverflow
    //     0xbb09cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb09d0: cmp             SP, x16
    //     0xbb09d4: b.ls            #0xbb0b28
    // 0xbb09d8: LoadField: r0 = r1->field_f
    //     0xbb09d8: ldur            w0, [x1, #0xf]
    // 0xbb09dc: DecompressPointer r0
    //     0xbb09dc: add             x0, x0, HEAP, lsl #32
    // 0xbb09e0: LoadField: r2 = r0->field_b
    //     0xbb09e0: ldur            w2, [x0, #0xb]
    // 0xbb09e4: DecompressPointer r2
    //     0xbb09e4: add             x2, x2, HEAP, lsl #32
    // 0xbb09e8: cmp             w2, NULL
    // 0xbb09ec: b.eq            #0xbb0b30
    // 0xbb09f0: LoadField: r3 = r0->field_33
    //     0xbb09f0: ldur            w3, [x0, #0x33]
    // 0xbb09f4: DecompressPointer r3
    //     0xbb09f4: add             x3, x3, HEAP, lsl #32
    // 0xbb09f8: LoadField: r4 = r3->field_27
    //     0xbb09f8: ldur            w4, [x3, #0x27]
    // 0xbb09fc: DecompressPointer r4
    //     0xbb09fc: add             x4, x4, HEAP, lsl #32
    // 0xbb0a00: LoadField: r3 = r4->field_7
    //     0xbb0a00: ldur            w3, [x4, #7]
    // 0xbb0a04: DecompressPointer r3
    //     0xbb0a04: add             x3, x3, HEAP, lsl #32
    // 0xbb0a08: LoadField: r4 = r0->field_3f
    //     0xbb0a08: ldur            w4, [x0, #0x3f]
    // 0xbb0a0c: DecompressPointer r4
    //     0xbb0a0c: add             x4, x4, HEAP, lsl #32
    // 0xbb0a10: LoadField: r5 = r4->field_27
    //     0xbb0a10: ldur            w5, [x4, #0x27]
    // 0xbb0a14: DecompressPointer r5
    //     0xbb0a14: add             x5, x5, HEAP, lsl #32
    // 0xbb0a18: LoadField: r4 = r5->field_7
    //     0xbb0a18: ldur            w4, [x5, #7]
    // 0xbb0a1c: DecompressPointer r4
    //     0xbb0a1c: add             x4, x4, HEAP, lsl #32
    // 0xbb0a20: LoadField: r5 = r0->field_3b
    //     0xbb0a20: ldur            w5, [x0, #0x3b]
    // 0xbb0a24: DecompressPointer r5
    //     0xbb0a24: add             x5, x5, HEAP, lsl #32
    // 0xbb0a28: LoadField: r6 = r5->field_27
    //     0xbb0a28: ldur            w6, [x5, #0x27]
    // 0xbb0a2c: DecompressPointer r6
    //     0xbb0a2c: add             x6, x6, HEAP, lsl #32
    // 0xbb0a30: LoadField: r5 = r6->field_7
    //     0xbb0a30: ldur            w5, [x6, #7]
    // 0xbb0a34: DecompressPointer r5
    //     0xbb0a34: add             x5, x5, HEAP, lsl #32
    // 0xbb0a38: LoadField: r6 = r0->field_47
    //     0xbb0a38: ldur            w6, [x0, #0x47]
    // 0xbb0a3c: DecompressPointer r6
    //     0xbb0a3c: add             x6, x6, HEAP, lsl #32
    // 0xbb0a40: LoadField: r7 = r6->field_27
    //     0xbb0a40: ldur            w7, [x6, #0x27]
    // 0xbb0a44: DecompressPointer r7
    //     0xbb0a44: add             x7, x7, HEAP, lsl #32
    // 0xbb0a48: LoadField: r6 = r7->field_7
    //     0xbb0a48: ldur            w6, [x7, #7]
    // 0xbb0a4c: DecompressPointer r6
    //     0xbb0a4c: add             x6, x6, HEAP, lsl #32
    // 0xbb0a50: LoadField: r7 = r0->field_37
    //     0xbb0a50: ldur            w7, [x0, #0x37]
    // 0xbb0a54: DecompressPointer r7
    //     0xbb0a54: add             x7, x7, HEAP, lsl #32
    // 0xbb0a58: LoadField: r8 = r7->field_27
    //     0xbb0a58: ldur            w8, [x7, #0x27]
    // 0xbb0a5c: DecompressPointer r8
    //     0xbb0a5c: add             x8, x8, HEAP, lsl #32
    // 0xbb0a60: LoadField: r7 = r8->field_7
    //     0xbb0a60: ldur            w7, [x8, #7]
    // 0xbb0a64: DecompressPointer r7
    //     0xbb0a64: add             x7, x7, HEAP, lsl #32
    // 0xbb0a68: LoadField: r8 = r0->field_43
    //     0xbb0a68: ldur            w8, [x0, #0x43]
    // 0xbb0a6c: DecompressPointer r8
    //     0xbb0a6c: add             x8, x8, HEAP, lsl #32
    // 0xbb0a70: LoadField: r0 = r8->field_27
    //     0xbb0a70: ldur            w0, [x8, #0x27]
    // 0xbb0a74: DecompressPointer r0
    //     0xbb0a74: add             x0, x0, HEAP, lsl #32
    // 0xbb0a78: LoadField: r8 = r0->field_7
    //     0xbb0a78: ldur            w8, [x0, #7]
    // 0xbb0a7c: DecompressPointer r8
    //     0xbb0a7c: add             x8, x8, HEAP, lsl #32
    // 0xbb0a80: LoadField: r0 = r2->field_f
    //     0xbb0a80: ldur            w0, [x2, #0xf]
    // 0xbb0a84: DecompressPointer r0
    //     0xbb0a84: add             x0, x0, HEAP, lsl #32
    // 0xbb0a88: stp             x3, x0, [SP, #0x28]
    // 0xbb0a8c: stp             x5, x4, [SP, #0x18]
    // 0xbb0a90: stp             x7, x6, [SP, #8]
    // 0xbb0a94: str             x8, [SP]
    // 0xbb0a98: r4 = 0
    //     0xbb0a98: movz            x4, #0
    // 0xbb0a9c: ldr             x0, [SP, #0x30]
    // 0xbb0aa0: r16 = UnlinkedCall_0x613b5c
    //     0xbb0aa0: add             x16, PP, #0x54, lsl #12  ; [pp+0x548f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb0aa4: add             x16, x16, #0x8f8
    // 0xbb0aa8: ldp             x5, lr, [x16]
    // 0xbb0aac: blr             lr
    // 0xbb0ab0: ldur            x0, [fp, #-8]
    // 0xbb0ab4: LoadField: r1 = r0->field_f
    //     0xbb0ab4: ldur            w1, [x0, #0xf]
    // 0xbb0ab8: DecompressPointer r1
    //     0xbb0ab8: add             x1, x1, HEAP, lsl #32
    // 0xbb0abc: LoadField: r0 = r1->field_b
    //     0xbb0abc: ldur            w0, [x1, #0xb]
    // 0xbb0ac0: DecompressPointer r0
    //     0xbb0ac0: add             x0, x0, HEAP, lsl #32
    // 0xbb0ac4: cmp             w0, NULL
    // 0xbb0ac8: b.eq            #0xbb0b34
    // 0xbb0acc: LoadField: r1 = r0->field_23
    //     0xbb0acc: ldur            w1, [x0, #0x23]
    // 0xbb0ad0: DecompressPointer r1
    //     0xbb0ad0: add             x1, x1, HEAP, lsl #32
    // 0xbb0ad4: str             x1, [SP]
    // 0xbb0ad8: r4 = 0
    //     0xbb0ad8: movz            x4, #0
    // 0xbb0adc: ldr             x0, [SP]
    // 0xbb0ae0: r16 = UnlinkedCall_0x613b5c
    //     0xbb0ae0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54908] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb0ae4: add             x16, x16, #0x908
    // 0xbb0ae8: ldp             x5, lr, [x16]
    // 0xbb0aec: blr             lr
    // 0xbb0af0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbb0af0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb0af4: ldr             x0, [x0, #0x1c80]
    //     0xbb0af8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb0afc: cmp             w0, w16
    //     0xbb0b00: b.ne            #0xbb0b0c
    //     0xbb0b04: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbb0b08: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb0b0c: str             NULL, [SP]
    // 0xbb0b10: r4 = const [0x1, 0, 0, 0, null]
    //     0xbb0b10: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbb0b14: r0 = GetNavigation.back()
    //     0xbb0b14: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbb0b18: r0 = Null
    //     0xbb0b18: mov             x0, NULL
    // 0xbb0b1c: LeaveFrame
    //     0xbb0b1c: mov             SP, fp
    //     0xbb0b20: ldp             fp, lr, [SP], #0x10
    // 0xbb0b24: ret
    //     0xbb0b24: ret             
    // 0xbb0b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0b28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0b2c: b               #0xbb09d8
    // 0xbb0b30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0b30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0b34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0b34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbb0b38, size: 0x78
    // 0xbb0b38: EnterFrame
    //     0xbb0b38: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0b3c: mov             fp, SP
    // 0xbb0b40: AllocStack(0x10)
    //     0xbb0b40: sub             SP, SP, #0x10
    // 0xbb0b44: SetupParameters()
    //     0xbb0b44: ldr             x0, [fp, #0x18]
    //     0xbb0b48: ldur            w3, [x0, #0x17]
    //     0xbb0b4c: add             x3, x3, HEAP, lsl #32
    //     0xbb0b50: stur            x3, [fp, #-0x10]
    // 0xbb0b54: CheckStackOverflow
    //     0xbb0b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0b58: cmp             SP, x16
    //     0xbb0b5c: b.ls            #0xbb0ba8
    // 0xbb0b60: LoadField: r0 = r3->field_f
    //     0xbb0b60: ldur            w0, [x3, #0xf]
    // 0xbb0b64: DecompressPointer r0
    //     0xbb0b64: add             x0, x0, HEAP, lsl #32
    // 0xbb0b68: mov             x2, x3
    // 0xbb0b6c: stur            x0, [fp, #-8]
    // 0xbb0b70: r1 = Function '<anonymous closure>':.
    //     0xbb0b70: add             x1, PP, #0x54, lsl #12  ; [pp+0x54918] AnonymousClosure: (0xa01a18), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb0b74: ldr             x1, [x1, #0x918]
    // 0xbb0b78: r0 = AllocateClosure()
    //     0xbb0b78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb0b7c: ldur            x1, [fp, #-8]
    // 0xbb0b80: mov             x2, x0
    // 0xbb0b84: r0 = setState()
    //     0xbb0b84: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb0b88: ldur            x0, [fp, #-0x10]
    // 0xbb0b8c: LoadField: r1 = r0->field_f
    //     0xbb0b8c: ldur            w1, [x0, #0xf]
    // 0xbb0b90: DecompressPointer r1
    //     0xbb0b90: add             x1, x1, HEAP, lsl #32
    // 0xbb0b94: r0 = validateAddress()
    //     0xbb0b94: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb0b98: r0 = Null
    //     0xbb0b98: mov             x0, NULL
    // 0xbb0b9c: LeaveFrame
    //     0xbb0b9c: mov             SP, fp
    //     0xbb0ba0: ldp             fp, lr, [SP], #0x10
    // 0xbb0ba4: ret
    //     0xbb0ba4: ret             
    // 0xbb0ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0ba8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0bac: b               #0xbb0b60
  }
  [closure] String? _validateAlternateNo(dynamic, String?) {
    // ** addr: 0xbb0bb0, size: 0x3c
    // 0xbb0bb0: EnterFrame
    //     0xbb0bb0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0bb4: mov             fp, SP
    // 0xbb0bb8: ldr             x0, [fp, #0x18]
    // 0xbb0bbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb0bbc: ldur            w1, [x0, #0x17]
    // 0xbb0bc0: DecompressPointer r1
    //     0xbb0bc0: add             x1, x1, HEAP, lsl #32
    // 0xbb0bc4: CheckStackOverflow
    //     0xbb0bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0bc8: cmp             SP, x16
    //     0xbb0bcc: b.ls            #0xbb0be4
    // 0xbb0bd0: ldr             x2, [fp, #0x10]
    // 0xbb0bd4: r0 = _validateAlternateNo()
    //     0xbb0bd4: bl              #0xa01af0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAlternateNo
    // 0xbb0bd8: LeaveFrame
    //     0xbb0bd8: mov             SP, fp
    //     0xbb0bdc: ldp             fp, lr, [SP], #0x10
    // 0xbb0be0: ret
    //     0xbb0be0: ret             
    // 0xbb0be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0be4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0be8: b               #0xbb0bd0
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbb0bec, size: 0x78
    // 0xbb0bec: EnterFrame
    //     0xbb0bec: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0bf0: mov             fp, SP
    // 0xbb0bf4: AllocStack(0x10)
    //     0xbb0bf4: sub             SP, SP, #0x10
    // 0xbb0bf8: SetupParameters()
    //     0xbb0bf8: ldr             x0, [fp, #0x18]
    //     0xbb0bfc: ldur            w3, [x0, #0x17]
    //     0xbb0c00: add             x3, x3, HEAP, lsl #32
    //     0xbb0c04: stur            x3, [fp, #-0x10]
    // 0xbb0c08: CheckStackOverflow
    //     0xbb0c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0c0c: cmp             SP, x16
    //     0xbb0c10: b.ls            #0xbb0c5c
    // 0xbb0c14: LoadField: r0 = r3->field_f
    //     0xbb0c14: ldur            w0, [x3, #0xf]
    // 0xbb0c18: DecompressPointer r0
    //     0xbb0c18: add             x0, x0, HEAP, lsl #32
    // 0xbb0c1c: mov             x2, x3
    // 0xbb0c20: stur            x0, [fp, #-8]
    // 0xbb0c24: r1 = Function '<anonymous closure>':.
    //     0xbb0c24: add             x1, PP, #0x54, lsl #12  ; [pp+0x54948] AnonymousClosure: (0xa01cc4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb0c28: ldr             x1, [x1, #0x948]
    // 0xbb0c2c: r0 = AllocateClosure()
    //     0xbb0c2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb0c30: ldur            x1, [fp, #-8]
    // 0xbb0c34: mov             x2, x0
    // 0xbb0c38: r0 = setState()
    //     0xbb0c38: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb0c3c: ldur            x0, [fp, #-0x10]
    // 0xbb0c40: LoadField: r1 = r0->field_f
    //     0xbb0c40: ldur            w1, [x0, #0xf]
    // 0xbb0c44: DecompressPointer r1
    //     0xbb0c44: add             x1, x1, HEAP, lsl #32
    // 0xbb0c48: r0 = validateAddress()
    //     0xbb0c48: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb0c4c: r0 = Null
    //     0xbb0c4c: mov             x0, NULL
    // 0xbb0c50: LeaveFrame
    //     0xbb0c50: mov             SP, fp
    //     0xbb0c54: ldp             fp, lr, [SP], #0x10
    // 0xbb0c58: ret
    //     0xbb0c58: ret             
    // 0xbb0c5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0c5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0c60: b               #0xbb0c14
  }
  [closure] String? _validateLandmark(dynamic, String?) {
    // ** addr: 0xbb0c64, size: 0x3c
    // 0xbb0c64: EnterFrame
    //     0xbb0c64: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0c68: mov             fp, SP
    // 0xbb0c6c: ldr             x0, [fp, #0x18]
    // 0xbb0c70: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb0c70: ldur            w1, [x0, #0x17]
    // 0xbb0c74: DecompressPointer r1
    //     0xbb0c74: add             x1, x1, HEAP, lsl #32
    // 0xbb0c78: CheckStackOverflow
    //     0xbb0c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0c7c: cmp             SP, x16
    //     0xbb0c80: b.ls            #0xbb0c98
    // 0xbb0c84: ldr             x2, [fp, #0x10]
    // 0xbb0c88: r0 = _validateLandmark()
    //     0xbb0c88: bl              #0xa01e20  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateLandmark
    // 0xbb0c8c: LeaveFrame
    //     0xbb0c8c: mov             SP, fp
    //     0xbb0c90: ldp             fp, lr, [SP], #0x10
    // 0xbb0c94: ret
    //     0xbb0c94: ret             
    // 0xbb0c98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0c98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0c9c: b               #0xbb0c84
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbb0ca0, size: 0x15c
    // 0xbb0ca0: EnterFrame
    //     0xbb0ca0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0ca4: mov             fp, SP
    // 0xbb0ca8: AllocStack(0x20)
    //     0xbb0ca8: sub             SP, SP, #0x20
    // 0xbb0cac: SetupParameters()
    //     0xbb0cac: ldr             x0, [fp, #0x18]
    //     0xbb0cb0: ldur            w3, [x0, #0x17]
    //     0xbb0cb4: add             x3, x3, HEAP, lsl #32
    //     0xbb0cb8: stur            x3, [fp, #-0x10]
    // 0xbb0cbc: CheckStackOverflow
    //     0xbb0cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0cc0: cmp             SP, x16
    //     0xbb0cc4: b.ls            #0xbb0dec
    // 0xbb0cc8: LoadField: r0 = r3->field_f
    //     0xbb0cc8: ldur            w0, [x3, #0xf]
    // 0xbb0ccc: DecompressPointer r0
    //     0xbb0ccc: add             x0, x0, HEAP, lsl #32
    // 0xbb0cd0: mov             x2, x3
    // 0xbb0cd4: stur            x0, [fp, #-8]
    // 0xbb0cd8: r1 = Function '<anonymous closure>':.
    //     0xbb0cd8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54950] AnonymousClosure: (0xa0202c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb0cdc: ldr             x1, [x1, #0x950]
    // 0xbb0ce0: r0 = AllocateClosure()
    //     0xbb0ce0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb0ce4: ldur            x1, [fp, #-8]
    // 0xbb0ce8: mov             x2, x0
    // 0xbb0cec: r0 = setState()
    //     0xbb0cec: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb0cf0: ldr             x0, [fp, #0x10]
    // 0xbb0cf4: cmp             w0, NULL
    // 0xbb0cf8: b.ne            #0xbb0d04
    // 0xbb0cfc: r1 = Null
    //     0xbb0cfc: mov             x1, NULL
    // 0xbb0d00: b               #0xbb0d08
    // 0xbb0d04: LoadField: r1 = r0->field_7
    //     0xbb0d04: ldur            w1, [x0, #7]
    // 0xbb0d08: cmp             w1, NULL
    // 0xbb0d0c: b.ne            #0xbb0d18
    // 0xbb0d10: r1 = 0
    //     0xbb0d10: movz            x1, #0
    // 0xbb0d14: b               #0xbb0d20
    // 0xbb0d18: r2 = LoadInt32Instr(r1)
    //     0xbb0d18: sbfx            x2, x1, #1, #0x1f
    // 0xbb0d1c: mov             x1, x2
    // 0xbb0d20: cmp             x1, #5
    // 0xbb0d24: b.le            #0xbb0dcc
    // 0xbb0d28: ldur            x1, [fp, #-0x10]
    // 0xbb0d2c: LoadField: r2 = r1->field_f
    //     0xbb0d2c: ldur            w2, [x1, #0xf]
    // 0xbb0d30: DecompressPointer r2
    //     0xbb0d30: add             x2, x2, HEAP, lsl #32
    // 0xbb0d34: LoadField: r3 = r2->field_b
    //     0xbb0d34: ldur            w3, [x2, #0xb]
    // 0xbb0d38: DecompressPointer r3
    //     0xbb0d38: add             x3, x3, HEAP, lsl #32
    // 0xbb0d3c: cmp             w3, NULL
    // 0xbb0d40: b.eq            #0xbb0df4
    // 0xbb0d44: cmp             w0, NULL
    // 0xbb0d48: b.ne            #0xbb0d54
    // 0xbb0d4c: r2 = ""
    //     0xbb0d4c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb0d50: b               #0xbb0d58
    // 0xbb0d54: mov             x2, x0
    // 0xbb0d58: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xbb0d58: ldur            w4, [x3, #0x17]
    // 0xbb0d5c: DecompressPointer r4
    //     0xbb0d5c: add             x4, x4, HEAP, lsl #32
    // 0xbb0d60: stp             x2, x4, [SP]
    // 0xbb0d64: r4 = 0
    //     0xbb0d64: movz            x4, #0
    // 0xbb0d68: ldr             x0, [SP, #8]
    // 0xbb0d6c: r16 = UnlinkedCall_0x613b5c
    //     0xbb0d6c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54958] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb0d70: add             x16, x16, #0x958
    // 0xbb0d74: ldp             x5, lr, [x16]
    // 0xbb0d78: blr             lr
    // 0xbb0d7c: ldur            x0, [fp, #-0x10]
    // 0xbb0d80: LoadField: r1 = r0->field_f
    //     0xbb0d80: ldur            w1, [x0, #0xf]
    // 0xbb0d84: DecompressPointer r1
    //     0xbb0d84: add             x1, x1, HEAP, lsl #32
    // 0xbb0d88: LoadField: r2 = r1->field_b
    //     0xbb0d88: ldur            w2, [x1, #0xb]
    // 0xbb0d8c: DecompressPointer r2
    //     0xbb0d8c: add             x2, x2, HEAP, lsl #32
    // 0xbb0d90: cmp             w2, NULL
    // 0xbb0d94: b.eq            #0xbb0df8
    // 0xbb0d98: ldr             x1, [fp, #0x10]
    // 0xbb0d9c: cmp             w1, NULL
    // 0xbb0da0: b.ne            #0xbb0da8
    // 0xbb0da4: r1 = ""
    //     0xbb0da4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb0da8: LoadField: r3 = r2->field_1b
    //     0xbb0da8: ldur            w3, [x2, #0x1b]
    // 0xbb0dac: DecompressPointer r3
    //     0xbb0dac: add             x3, x3, HEAP, lsl #32
    // 0xbb0db0: stp             x1, x3, [SP]
    // 0xbb0db4: r4 = 0
    //     0xbb0db4: movz            x4, #0
    // 0xbb0db8: ldr             x0, [SP, #8]
    // 0xbb0dbc: r16 = UnlinkedCall_0x613b5c
    //     0xbb0dbc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54968] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb0dc0: add             x16, x16, #0x968
    // 0xbb0dc4: ldp             x5, lr, [x16]
    // 0xbb0dc8: blr             lr
    // 0xbb0dcc: ldur            x0, [fp, #-0x10]
    // 0xbb0dd0: LoadField: r1 = r0->field_f
    //     0xbb0dd0: ldur            w1, [x0, #0xf]
    // 0xbb0dd4: DecompressPointer r1
    //     0xbb0dd4: add             x1, x1, HEAP, lsl #32
    // 0xbb0dd8: r0 = validateAddress()
    //     0xbb0dd8: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb0ddc: r0 = Null
    //     0xbb0ddc: mov             x0, NULL
    // 0xbb0de0: LeaveFrame
    //     0xbb0de0: mov             SP, fp
    //     0xbb0de4: ldp             fp, lr, [SP], #0x10
    // 0xbb0de8: ret
    //     0xbb0de8: ret             
    // 0xbb0dec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0dec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0df0: b               #0xbb0cc8
    // 0xbb0df4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0df4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb0df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0df8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] String? _validatePinCode(dynamic, String?) {
    // ** addr: 0xbb0dfc, size: 0x3c
    // 0xbb0dfc: EnterFrame
    //     0xbb0dfc: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0e00: mov             fp, SP
    // 0xbb0e04: ldr             x0, [fp, #0x18]
    // 0xbb0e08: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb0e08: ldur            w1, [x0, #0x17]
    // 0xbb0e0c: DecompressPointer r1
    //     0xbb0e0c: add             x1, x1, HEAP, lsl #32
    // 0xbb0e10: CheckStackOverflow
    //     0xbb0e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0e14: cmp             SP, x16
    //     0xbb0e18: b.ls            #0xbb0e30
    // 0xbb0e1c: ldr             x2, [fp, #0x10]
    // 0xbb0e20: r0 = _validatePinCode()
    //     0xbb0e20: bl              #0xbb0e38  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validatePinCode
    // 0xbb0e24: LeaveFrame
    //     0xbb0e24: mov             SP, fp
    //     0xbb0e28: ldp             fp, lr, [SP], #0x10
    // 0xbb0e2c: ret
    //     0xbb0e2c: ret             
    // 0xbb0e30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0e30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0e34: b               #0xbb0e1c
  }
  _ _validatePinCode(/* No info */) {
    // ** addr: 0xbb0e38, size: 0x15c
    // 0xbb0e38: EnterFrame
    //     0xbb0e38: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0e3c: mov             fp, SP
    // 0xbb0e40: AllocStack(0x10)
    //     0xbb0e40: sub             SP, SP, #0x10
    // 0xbb0e44: SetupParameters(_ChangeAddressBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb0e44: mov             x0, x1
    //     0xbb0e48: stur            x1, [fp, #-8]
    //     0xbb0e4c: stur            x2, [fp, #-0x10]
    // 0xbb0e50: CheckStackOverflow
    //     0xbb0e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0e54: cmp             SP, x16
    //     0xbb0e58: b.ls            #0xbb0f88
    // 0xbb0e5c: cmp             w2, NULL
    // 0xbb0e60: b.ne            #0xbb0e6c
    // 0xbb0e64: r1 = ""
    //     0xbb0e64: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb0e68: b               #0xbb0e70
    // 0xbb0e6c: mov             x1, x2
    // 0xbb0e70: r0 = checkEmpty()
    //     0xbb0e70: bl              #0x8fc4b0  ; [package:customer_app/app/core/utils/utils.dart] Utils::checkEmpty
    // 0xbb0e74: tbnz            w0, #4, #0xbb0e8c
    // 0xbb0e78: r0 = "Pincode field is required"
    //     0xbb0e78: add             x0, PP, #0x54, lsl #12  ; [pp+0x541e8] "Pincode field is required"
    //     0xbb0e7c: ldr             x0, [x0, #0x1e8]
    // 0xbb0e80: LeaveFrame
    //     0xbb0e80: mov             SP, fp
    //     0xbb0e84: ldp             fp, lr, [SP], #0x10
    // 0xbb0e88: ret
    //     0xbb0e88: ret             
    // 0xbb0e8c: ldur            x0, [fp, #-0x10]
    // 0xbb0e90: cmp             w0, NULL
    // 0xbb0e94: b.ne            #0xbb0ea0
    // 0xbb0e98: r0 = Null
    //     0xbb0e98: mov             x0, NULL
    // 0xbb0e9c: b               #0xbb0ea8
    // 0xbb0ea0: LoadField: r1 = r0->field_7
    //     0xbb0ea0: ldur            w1, [x0, #7]
    // 0xbb0ea4: mov             x0, x1
    // 0xbb0ea8: cmp             w0, NULL
    // 0xbb0eac: b.ne            #0xbb0eb8
    // 0xbb0eb0: r0 = 0
    //     0xbb0eb0: movz            x0, #0
    // 0xbb0eb4: b               #0xbb0ec0
    // 0xbb0eb8: r1 = LoadInt32Instr(r0)
    //     0xbb0eb8: sbfx            x1, x0, #1, #0x1f
    // 0xbb0ebc: mov             x0, x1
    // 0xbb0ec0: cmp             x0, #6
    // 0xbb0ec4: b.ge            #0xbb0edc
    // 0xbb0ec8: r0 = "Please enter 6 digit pincode"
    //     0xbb0ec8: add             x0, PP, #0x54, lsl #12  ; [pp+0x541f0] "Please enter 6 digit pincode"
    //     0xbb0ecc: ldr             x0, [x0, #0x1f0]
    // 0xbb0ed0: LeaveFrame
    //     0xbb0ed0: mov             SP, fp
    //     0xbb0ed4: ldp             fp, lr, [SP], #0x10
    // 0xbb0ed8: ret
    //     0xbb0ed8: ret             
    // 0xbb0edc: ldur            x1, [fp, #-8]
    // 0xbb0ee0: LoadField: r0 = r1->field_b
    //     0xbb0ee0: ldur            w0, [x1, #0xb]
    // 0xbb0ee4: DecompressPointer r0
    //     0xbb0ee4: add             x0, x0, HEAP, lsl #32
    // 0xbb0ee8: cmp             w0, NULL
    // 0xbb0eec: b.eq            #0xbb0f90
    // 0xbb0ef0: LoadField: r2 = r0->field_13
    //     0xbb0ef0: ldur            w2, [x0, #0x13]
    // 0xbb0ef4: DecompressPointer r2
    //     0xbb0ef4: add             x2, x2, HEAP, lsl #32
    // 0xbb0ef8: LoadField: r0 = r2->field_b
    //     0xbb0ef8: ldur            w0, [x2, #0xb]
    // 0xbb0efc: DecompressPointer r0
    //     0xbb0efc: add             x0, x0, HEAP, lsl #32
    // 0xbb0f00: cmp             w0, NULL
    // 0xbb0f04: b.eq            #0xbb0f78
    // 0xbb0f08: LoadField: r2 = r0->field_13
    //     0xbb0f08: ldur            w2, [x0, #0x13]
    // 0xbb0f0c: DecompressPointer r2
    //     0xbb0f0c: add             x2, x2, HEAP, lsl #32
    // 0xbb0f10: cmp             w2, NULL
    // 0xbb0f14: b.eq            #0xbb0f4c
    // 0xbb0f18: cmp             w2, NULL
    // 0xbb0f1c: b.ne            #0xbb0f28
    // 0xbb0f20: r0 = Null
    //     0xbb0f20: mov             x0, NULL
    // 0xbb0f24: b               #0xbb0f40
    // 0xbb0f28: LoadField: r0 = r2->field_7
    //     0xbb0f28: ldur            w0, [x2, #7]
    // 0xbb0f2c: cbz             w0, #0xbb0f38
    // 0xbb0f30: r2 = false
    //     0xbb0f30: add             x2, NULL, #0x30  ; false
    // 0xbb0f34: b               #0xbb0f3c
    // 0xbb0f38: r2 = true
    //     0xbb0f38: add             x2, NULL, #0x20  ; true
    // 0xbb0f3c: mov             x0, x2
    // 0xbb0f40: cmp             w0, NULL
    // 0xbb0f44: b.eq            #0xbb0f64
    // 0xbb0f48: tbnz            w0, #4, #0xbb0f64
    // 0xbb0f4c: r0 = validateAddress()
    //     0xbb0f4c: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb0f50: r0 = "Provided Pincode is not serviceable"
    //     0xbb0f50: add             x0, PP, #0x54, lsl #12  ; [pp+0x541f8] "Provided Pincode is not serviceable"
    //     0xbb0f54: ldr             x0, [x0, #0x1f8]
    // 0xbb0f58: LeaveFrame
    //     0xbb0f58: mov             SP, fp
    //     0xbb0f5c: ldp             fp, lr, [SP], #0x10
    // 0xbb0f60: ret
    //     0xbb0f60: ret             
    // 0xbb0f64: r0 = validateAddress()
    //     0xbb0f64: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb0f68: r0 = Null
    //     0xbb0f68: mov             x0, NULL
    // 0xbb0f6c: LeaveFrame
    //     0xbb0f6c: mov             SP, fp
    //     0xbb0f70: ldp             fp, lr, [SP], #0x10
    // 0xbb0f74: ret
    //     0xbb0f74: ret             
    // 0xbb0f78: r0 = Null
    //     0xbb0f78: mov             x0, NULL
    // 0xbb0f7c: LeaveFrame
    //     0xbb0f7c: mov             SP, fp
    //     0xbb0f80: ldp             fp, lr, [SP], #0x10
    // 0xbb0f84: ret
    //     0xbb0f84: ret             
    // 0xbb0f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb0f88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb0f8c: b               #0xbb0e5c
    // 0xbb0f90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb0f90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbb0f94, size: 0x78
    // 0xbb0f94: EnterFrame
    //     0xbb0f94: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0f98: mov             fp, SP
    // 0xbb0f9c: AllocStack(0x10)
    //     0xbb0f9c: sub             SP, SP, #0x10
    // 0xbb0fa0: SetupParameters()
    //     0xbb0fa0: ldr             x0, [fp, #0x18]
    //     0xbb0fa4: ldur            w3, [x0, #0x17]
    //     0xbb0fa8: add             x3, x3, HEAP, lsl #32
    //     0xbb0fac: stur            x3, [fp, #-0x10]
    // 0xbb0fb0: CheckStackOverflow
    //     0xbb0fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0fb4: cmp             SP, x16
    //     0xbb0fb8: b.ls            #0xbb1004
    // 0xbb0fbc: LoadField: r0 = r3->field_f
    //     0xbb0fbc: ldur            w0, [x3, #0xf]
    // 0xbb0fc0: DecompressPointer r0
    //     0xbb0fc0: add             x0, x0, HEAP, lsl #32
    // 0xbb0fc4: mov             x2, x3
    // 0xbb0fc8: stur            x0, [fp, #-8]
    // 0xbb0fcc: r1 = Function '<anonymous closure>':.
    //     0xbb0fcc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54978] AnonymousClosure: (0xa022d8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb0fd0: ldr             x1, [x1, #0x978]
    // 0xbb0fd4: r0 = AllocateClosure()
    //     0xbb0fd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb0fd8: ldur            x1, [fp, #-8]
    // 0xbb0fdc: mov             x2, x0
    // 0xbb0fe0: r0 = setState()
    //     0xbb0fe0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb0fe4: ldur            x0, [fp, #-0x10]
    // 0xbb0fe8: LoadField: r1 = r0->field_f
    //     0xbb0fe8: ldur            w1, [x0, #0xf]
    // 0xbb0fec: DecompressPointer r1
    //     0xbb0fec: add             x1, x1, HEAP, lsl #32
    // 0xbb0ff0: r0 = validateAddress()
    //     0xbb0ff0: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb0ff4: r0 = Null
    //     0xbb0ff4: mov             x0, NULL
    // 0xbb0ff8: LeaveFrame
    //     0xbb0ff8: mov             SP, fp
    //     0xbb0ffc: ldp             fp, lr, [SP], #0x10
    // 0xbb1000: ret
    //     0xbb1000: ret             
    // 0xbb1004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb1004: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb1008: b               #0xbb0fbc
  }
  [closure] String? _validateAddress(dynamic, String?) {
    // ** addr: 0xbb100c, size: 0x3c
    // 0xbb100c: EnterFrame
    //     0xbb100c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb1010: mov             fp, SP
    // 0xbb1014: ldr             x0, [fp, #0x18]
    // 0xbb1018: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb1018: ldur            w1, [x0, #0x17]
    // 0xbb101c: DecompressPointer r1
    //     0xbb101c: add             x1, x1, HEAP, lsl #32
    // 0xbb1020: CheckStackOverflow
    //     0xbb1020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb1024: cmp             SP, x16
    //     0xbb1028: b.ls            #0xbb1040
    // 0xbb102c: ldr             x2, [fp, #0x10]
    // 0xbb1030: r0 = _validateAddress()
    //     0xbb1030: bl              #0xa023b0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress
    // 0xbb1034: LeaveFrame
    //     0xbb1034: mov             SP, fp
    //     0xbb1038: ldp             fp, lr, [SP], #0x10
    // 0xbb103c: ret
    //     0xbb103c: ret             
    // 0xbb1040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb1040: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb1044: b               #0xbb102c
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbb1048, size: 0x78
    // 0xbb1048: EnterFrame
    //     0xbb1048: stp             fp, lr, [SP, #-0x10]!
    //     0xbb104c: mov             fp, SP
    // 0xbb1050: AllocStack(0x10)
    //     0xbb1050: sub             SP, SP, #0x10
    // 0xbb1054: SetupParameters()
    //     0xbb1054: ldr             x0, [fp, #0x18]
    //     0xbb1058: ldur            w3, [x0, #0x17]
    //     0xbb105c: add             x3, x3, HEAP, lsl #32
    //     0xbb1060: stur            x3, [fp, #-0x10]
    // 0xbb1064: CheckStackOverflow
    //     0xbb1064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb1068: cmp             SP, x16
    //     0xbb106c: b.ls            #0xbb10b8
    // 0xbb1070: LoadField: r0 = r3->field_f
    //     0xbb1070: ldur            w0, [x3, #0xf]
    // 0xbb1074: DecompressPointer r0
    //     0xbb1074: add             x0, x0, HEAP, lsl #32
    // 0xbb1078: mov             x2, x3
    // 0xbb107c: stur            x0, [fp, #-8]
    // 0xbb1080: r1 = Function '<anonymous closure>':.
    //     0xbb1080: add             x1, PP, #0x54, lsl #12  ; [pp+0x54980] AnonymousClosure: (0xa024dc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xbb1084: ldr             x1, [x1, #0x980]
    // 0xbb1088: r0 = AllocateClosure()
    //     0xbb1088: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb108c: ldur            x1, [fp, #-8]
    // 0xbb1090: mov             x2, x0
    // 0xbb1094: r0 = setState()
    //     0xbb1094: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb1098: ldur            x0, [fp, #-0x10]
    // 0xbb109c: LoadField: r1 = r0->field_f
    //     0xbb109c: ldur            w1, [x0, #0xf]
    // 0xbb10a0: DecompressPointer r1
    //     0xbb10a0: add             x1, x1, HEAP, lsl #32
    // 0xbb10a4: r0 = validateAddress()
    //     0xbb10a4: bl              #0xa018e8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::validateAddress
    // 0xbb10a8: r0 = Null
    //     0xbb10a8: mov             x0, NULL
    // 0xbb10ac: LeaveFrame
    //     0xbb10ac: mov             SP, fp
    //     0xbb10b0: ldp             fp, lr, [SP], #0x10
    // 0xbb10b4: ret
    //     0xbb10b4: ret             
    // 0xbb10b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb10b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb10bc: b               #0xbb1070
  }
  [closure] String? _validateHouseNumber(dynamic, String?) {
    // ** addr: 0xbb10c0, size: 0x3c
    // 0xbb10c0: EnterFrame
    //     0xbb10c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb10c4: mov             fp, SP
    // 0xbb10c8: ldr             x0, [fp, #0x18]
    // 0xbb10cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb10cc: ldur            w1, [x0, #0x17]
    // 0xbb10d0: DecompressPointer r1
    //     0xbb10d0: add             x1, x1, HEAP, lsl #32
    // 0xbb10d4: CheckStackOverflow
    //     0xbb10d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb10d8: cmp             SP, x16
    //     0xbb10dc: b.ls            #0xbb10f4
    // 0xbb10e0: ldr             x2, [fp, #0x10]
    // 0xbb10e4: r0 = _validateHouseNumber()
    //     0xbb10e4: bl              #0xa025b4  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateHouseNumber
    // 0xbb10e8: LeaveFrame
    //     0xbb10e8: mov             SP, fp
    //     0xbb10ec: ldp             fp, lr, [SP], #0x10
    // 0xbb10f0: ret
    //     0xbb10f0: ret             
    // 0xbb10f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb10f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb10f8: b               #0xbb10e0
  }
  [closure] String? _validateCustomerNameNumber(dynamic, String?) {
    // ** addr: 0xbb10fc, size: 0x3c
    // 0xbb10fc: EnterFrame
    //     0xbb10fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbb1100: mov             fp, SP
    // 0xbb1104: ldr             x0, [fp, #0x18]
    // 0xbb1108: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb1108: ldur            w1, [x0, #0x17]
    // 0xbb110c: DecompressPointer r1
    //     0xbb110c: add             x1, x1, HEAP, lsl #32
    // 0xbb1110: CheckStackOverflow
    //     0xbb1110: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb1114: cmp             SP, x16
    //     0xbb1118: b.ls            #0xbb1130
    // 0xbb111c: ldr             x2, [fp, #0x10]
    // 0xbb1120: r0 = _validateCustomerNameNumber()
    //     0xbb1120: bl              #0xa02760  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateCustomerNameNumber
    // 0xbb1124: LeaveFrame
    //     0xbb1124: mov             SP, fp
    //     0xbb1128: ldp             fp, lr, [SP], #0x10
    // 0xbb112c: ret
    //     0xbb112c: ret             
    // 0xbb1130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb1130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb1134: b               #0xbb111c
  }
}

// class id: 4024, size: 0x28, field offset: 0xc
class ChangeAddressBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80140, size: 0x48
    // 0xc80140: EnterFrame
    //     0xc80140: stp             fp, lr, [SP, #-0x10]!
    //     0xc80144: mov             fp, SP
    // 0xc80148: AllocStack(0x8)
    //     0xc80148: sub             SP, SP, #8
    // 0xc8014c: CheckStackOverflow
    //     0xc8014c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80150: cmp             SP, x16
    //     0xc80154: b.ls            #0xc80180
    // 0xc80158: r1 = <ChangeAddressBottomSheet>
    //     0xc80158: add             x1, PP, #0x48, lsl #12  ; [pp+0x486b0] TypeArguments: <ChangeAddressBottomSheet>
    //     0xc8015c: ldr             x1, [x1, #0x6b0]
    // 0xc80160: r0 = _ChangeAddressBottomSheetState()
    //     0xc80160: bl              #0xc80188  ; Allocate_ChangeAddressBottomSheetStateStub -> _ChangeAddressBottomSheetState (size=0x68)
    // 0xc80164: mov             x1, x0
    // 0xc80168: stur            x0, [fp, #-8]
    // 0xc8016c: r0 = _ChangeAddressBottomSheetState()
    //     0xc8016c: bl              #0xc7a510  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_ChangeAddressBottomSheetState
    // 0xc80170: ldur            x0, [fp, #-8]
    // 0xc80174: LeaveFrame
    //     0xc80174: mov             SP, fp
    //     0xc80178: ldp             fp, lr, [SP], #0x10
    // 0xc8017c: ret
    //     0xc8017c: ret             
    // 0xc80180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80180: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80184: b               #0xc80158
  }
}
