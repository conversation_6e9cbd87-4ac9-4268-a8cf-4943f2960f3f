// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_info_widget.dart

// class id: 1049491, size: 0x8
class :: {
}

// class id: 3273, size: 0x14, field offset: 0x14
class _LineShowDeliveryInfoWidgetState extends State<dynamic> {

  [closure] Entities <anonymous closure>(dynamic) {
    // ** addr: 0xa0befc, size: 0x18
    // 0xa0befc: EnterFrame
    //     0xa0befc: stp             fp, lr, [SP, #-0x10]!
    //     0xa0bf00: mov             fp, SP
    // 0xa0bf04: r0 = Entities()
    //     0xa0bf04: bl              #0xa0bf34  ; AllocateEntitiesStub -> Entities (size=0x14)
    // 0xa0bf08: LeaveFrame
    //     0xa0bf08: mov             SP, fp
    //     0xa0bf0c: ldp             fp, lr, [SP], #0x10
    // 0xa0bf10: ret
    //     0xa0bf10: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Entities) {
    // ** addr: 0xa0bf40, size: 0x58
    // 0xa0bf40: EnterFrame
    //     0xa0bf40: stp             fp, lr, [SP, #-0x10]!
    //     0xa0bf44: mov             fp, SP
    // 0xa0bf48: AllocStack(0x10)
    //     0xa0bf48: sub             SP, SP, #0x10
    // 0xa0bf4c: CheckStackOverflow
    //     0xa0bf4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0bf50: cmp             SP, x16
    //     0xa0bf54: b.ls            #0xa0bf90
    // 0xa0bf58: ldr             x0, [fp, #0x10]
    // 0xa0bf5c: LoadField: r1 = r0->field_7
    //     0xa0bf5c: ldur            w1, [x0, #7]
    // 0xa0bf60: DecompressPointer r1
    //     0xa0bf60: add             x1, x1, HEAP, lsl #32
    // 0xa0bf64: r0 = LoadClassIdInstr(r1)
    //     0xa0bf64: ldur            x0, [x1, #-1]
    //     0xa0bf68: ubfx            x0, x0, #0xc, #0x14
    // 0xa0bf6c: r16 = "Delivery fee"
    //     0xa0bf6c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3cff0] "Delivery fee"
    //     0xa0bf70: ldr             x16, [x16, #0xff0]
    // 0xa0bf74: stp             x16, x1, [SP]
    // 0xa0bf78: mov             lr, x0
    // 0xa0bf7c: ldr             lr, [x21, lr, lsl #3]
    // 0xa0bf80: blr             lr
    // 0xa0bf84: LeaveFrame
    //     0xa0bf84: mov             SP, fp
    //     0xa0bf88: ldp             fp, lr, [SP], #0x10
    // 0xa0bf8c: ret
    //     0xa0bf8c: ret             
    // 0xa0bf90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0bf90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0bf94: b               #0xa0bf58
  }
  _ build(/* No info */) {
    // ** addr: 0xbb7f38, size: 0x5c0
    // 0xbb7f38: EnterFrame
    //     0xbb7f38: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7f3c: mov             fp, SP
    // 0xbb7f40: AllocStack(0x58)
    //     0xbb7f40: sub             SP, SP, #0x58
    // 0xbb7f44: SetupParameters(_LineShowDeliveryInfoWidgetState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xbb7f44: mov             x3, x1
    //     0xbb7f48: mov             x0, x2
    //     0xbb7f4c: stur            x1, [fp, #-0x10]
    //     0xbb7f50: stur            x2, [fp, #-0x18]
    // 0xbb7f54: CheckStackOverflow
    //     0xbb7f54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7f58: cmp             SP, x16
    //     0xbb7f5c: b.ls            #0xbb84cc
    // 0xbb7f60: LoadField: r1 = r3->field_b
    //     0xbb7f60: ldur            w1, [x3, #0xb]
    // 0xbb7f64: DecompressPointer r1
    //     0xbb7f64: add             x1, x1, HEAP, lsl #32
    // 0xbb7f68: cmp             w1, NULL
    // 0xbb7f6c: b.eq            #0xbb84d4
    // 0xbb7f70: LoadField: r2 = r1->field_b
    //     0xbb7f70: ldur            w2, [x1, #0xb]
    // 0xbb7f74: DecompressPointer r2
    //     0xbb7f74: add             x2, x2, HEAP, lsl #32
    // 0xbb7f78: LoadField: r1 = r2->field_b
    //     0xbb7f78: ldur            w1, [x2, #0xb]
    // 0xbb7f7c: DecompressPointer r1
    //     0xbb7f7c: add             x1, x1, HEAP, lsl #32
    // 0xbb7f80: cmp             w1, NULL
    // 0xbb7f84: b.ne            #0xbb7f90
    // 0xbb7f88: r0 = Null
    //     0xbb7f88: mov             x0, NULL
    // 0xbb7f8c: b               #0xbb8000
    // 0xbb7f90: LoadField: r2 = r1->field_1b
    //     0xbb7f90: ldur            w2, [x1, #0x1b]
    // 0xbb7f94: DecompressPointer r2
    //     0xbb7f94: add             x2, x2, HEAP, lsl #32
    // 0xbb7f98: cmp             w2, NULL
    // 0xbb7f9c: b.ne            #0xbb7fa8
    // 0xbb7fa0: r0 = Null
    //     0xbb7fa0: mov             x0, NULL
    // 0xbb7fa4: b               #0xbb8000
    // 0xbb7fa8: LoadField: r4 = r2->field_f
    //     0xbb7fa8: ldur            w4, [x2, #0xf]
    // 0xbb7fac: DecompressPointer r4
    //     0xbb7fac: add             x4, x4, HEAP, lsl #32
    // 0xbb7fb0: stur            x4, [fp, #-8]
    // 0xbb7fb4: cmp             w4, NULL
    // 0xbb7fb8: b.ne            #0xbb7fc4
    // 0xbb7fbc: r0 = Null
    //     0xbb7fbc: mov             x0, NULL
    // 0xbb7fc0: b               #0xbb8000
    // 0xbb7fc4: r1 = Function '<anonymous closure>':.
    //     0xbb7fc4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a5a8] AnonymousClosure: (0xa0bf40), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_info_widget.dart] _LineShowDeliveryInfoWidgetState::build (0xbb7f38)
    //     0xbb7fc8: ldr             x1, [x1, #0x5a8]
    // 0xbb7fcc: r2 = Null
    //     0xbb7fcc: mov             x2, NULL
    // 0xbb7fd0: r0 = AllocateClosure()
    //     0xbb7fd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb7fd4: r1 = Function '<anonymous closure>':.
    //     0xbb7fd4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a5b0] AnonymousClosure: (0xa0befc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_info_widget.dart] _LineShowDeliveryInfoWidgetState::build (0xbb7f38)
    //     0xbb7fd8: ldr             x1, [x1, #0x5b0]
    // 0xbb7fdc: r2 = Null
    //     0xbb7fdc: mov             x2, NULL
    // 0xbb7fe0: stur            x0, [fp, #-0x20]
    // 0xbb7fe4: r0 = AllocateClosure()
    //     0xbb7fe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb7fe8: str             x0, [SP]
    // 0xbb7fec: ldur            x1, [fp, #-8]
    // 0xbb7ff0: ldur            x2, [fp, #-0x20]
    // 0xbb7ff4: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xbb7ff4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xbb7ff8: ldr             x4, [x4, #0xb48]
    // 0xbb7ffc: r0 = firstWhere()
    //     0xbb7ffc: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xbb8000: stur            x0, [fp, #-0x20]
    // 0xbb8004: cmp             w0, NULL
    // 0xbb8008: b.ne            #0xbb8014
    // 0xbb800c: r1 = Null
    //     0xbb800c: mov             x1, NULL
    // 0xbb8010: b               #0xbb8040
    // 0xbb8014: LoadField: r1 = r0->field_f
    //     0xbb8014: ldur            w1, [x0, #0xf]
    // 0xbb8018: DecompressPointer r1
    //     0xbb8018: add             x1, x1, HEAP, lsl #32
    // 0xbb801c: cmp             w1, NULL
    // 0xbb8020: b.ne            #0xbb802c
    // 0xbb8024: r1 = Null
    //     0xbb8024: mov             x1, NULL
    // 0xbb8028: b               #0xbb8040
    // 0xbb802c: LoadField: r2 = r1->field_7
    //     0xbb802c: ldur            w2, [x1, #7]
    // 0xbb8030: cbnz            w2, #0xbb803c
    // 0xbb8034: r1 = false
    //     0xbb8034: add             x1, NULL, #0x30  ; false
    // 0xbb8038: b               #0xbb8040
    // 0xbb803c: r1 = true
    //     0xbb803c: add             x1, NULL, #0x20  ; true
    // 0xbb8040: cmp             w1, NULL
    // 0xbb8044: b.eq            #0xbb8058
    // 0xbb8048: tbnz            w1, #4, #0xbb8058
    // 0xbb804c: ldur            x1, [fp, #-0x10]
    // 0xbb8050: r2 = true
    //     0xbb8050: add             x2, NULL, #0x20  ; true
    // 0xbb8054: b               #0xbb80dc
    // 0xbb8058: ldur            x1, [fp, #-0x10]
    // 0xbb805c: LoadField: r2 = r1->field_b
    //     0xbb805c: ldur            w2, [x1, #0xb]
    // 0xbb8060: DecompressPointer r2
    //     0xbb8060: add             x2, x2, HEAP, lsl #32
    // 0xbb8064: cmp             w2, NULL
    // 0xbb8068: b.eq            #0xbb84d8
    // 0xbb806c: LoadField: r3 = r2->field_b
    //     0xbb806c: ldur            w3, [x2, #0xb]
    // 0xbb8070: DecompressPointer r3
    //     0xbb8070: add             x3, x3, HEAP, lsl #32
    // 0xbb8074: LoadField: r2 = r3->field_b
    //     0xbb8074: ldur            w2, [x3, #0xb]
    // 0xbb8078: DecompressPointer r2
    //     0xbb8078: add             x2, x2, HEAP, lsl #32
    // 0xbb807c: cmp             w2, NULL
    // 0xbb8080: b.ne            #0xbb808c
    // 0xbb8084: r2 = Null
    //     0xbb8084: mov             x2, NULL
    // 0xbb8088: b               #0xbb80d0
    // 0xbb808c: LoadField: r3 = r2->field_1b
    //     0xbb808c: ldur            w3, [x2, #0x1b]
    // 0xbb8090: DecompressPointer r3
    //     0xbb8090: add             x3, x3, HEAP, lsl #32
    // 0xbb8094: cmp             w3, NULL
    // 0xbb8098: b.ne            #0xbb80a4
    // 0xbb809c: r2 = Null
    //     0xbb809c: mov             x2, NULL
    // 0xbb80a0: b               #0xbb80d0
    // 0xbb80a4: LoadField: r2 = r3->field_b
    //     0xbb80a4: ldur            w2, [x3, #0xb]
    // 0xbb80a8: DecompressPointer r2
    //     0xbb80a8: add             x2, x2, HEAP, lsl #32
    // 0xbb80ac: cmp             w2, NULL
    // 0xbb80b0: b.ne            #0xbb80bc
    // 0xbb80b4: r2 = Null
    //     0xbb80b4: mov             x2, NULL
    // 0xbb80b8: b               #0xbb80d0
    // 0xbb80bc: LoadField: r3 = r2->field_7
    //     0xbb80bc: ldur            w3, [x2, #7]
    // 0xbb80c0: cbnz            w3, #0xbb80cc
    // 0xbb80c4: r2 = false
    //     0xbb80c4: add             x2, NULL, #0x30  ; false
    // 0xbb80c8: b               #0xbb80d0
    // 0xbb80cc: r2 = true
    //     0xbb80cc: add             x2, NULL, #0x20  ; true
    // 0xbb80d0: cmp             w2, NULL
    // 0xbb80d4: b.ne            #0xbb80dc
    // 0xbb80d8: r2 = false
    //     0xbb80d8: add             x2, NULL, #0x30  ; false
    // 0xbb80dc: stur            x2, [fp, #-8]
    // 0xbb80e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbb80e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb80e4: ldr             x0, [x0, #0x1c80]
    //     0xbb80e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb80ec: cmp             w0, w16
    //     0xbb80f0: b.ne            #0xbb80fc
    //     0xbb80f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbb80f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb80fc: r0 = GetNavigation.size()
    //     0xbb80fc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbb8100: LoadField: d1 = r0->field_7
    //     0xbb8100: ldur            d1, [x0, #7]
    // 0xbb8104: stur            d1, [fp, #-0x38]
    // 0xbb8108: r1 = Instance_Color
    //     0xbb8108: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0xbb810c: ldr             x1, [x1, #0xb18]
    // 0xbb8110: d0 = 0.080000
    //     0xbb8110: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xbb8114: ldr             d0, [x17, #0x798]
    // 0xbb8118: r0 = withOpacity()
    //     0xbb8118: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb811c: stur            x0, [fp, #-0x28]
    // 0xbb8120: r0 = BoxDecoration()
    //     0xbb8120: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbb8124: mov             x1, x0
    // 0xbb8128: ldur            x0, [fp, #-0x28]
    // 0xbb812c: stur            x1, [fp, #-0x30]
    // 0xbb8130: StoreField: r1->field_7 = r0
    //     0xbb8130: stur            w0, [x1, #7]
    // 0xbb8134: r0 = Instance_BoxShape
    //     0xbb8134: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb8138: ldr             x0, [x0, #0x80]
    // 0xbb813c: StoreField: r1->field_23 = r0
    //     0xbb813c: stur            w0, [x1, #0x23]
    // 0xbb8140: r0 = SvgPicture()
    //     0xbb8140: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbb8144: stur            x0, [fp, #-0x28]
    // 0xbb8148: r16 = Instance_BoxFit
    //     0xbb8148: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbb814c: ldr             x16, [x16, #0xb18]
    // 0xbb8150: r30 = 24.000000
    //     0xbb8150: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbb8154: ldr             lr, [lr, #0xba8]
    // 0xbb8158: stp             lr, x16, [SP, #8]
    // 0xbb815c: r16 = 24.000000
    //     0xbb815c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbb8160: ldr             x16, [x16, #0xba8]
    // 0xbb8164: str             x16, [SP]
    // 0xbb8168: mov             x1, x0
    // 0xbb816c: r2 = "assets/images/delivery_text_discount.svg"
    //     0xbb816c: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5b8] "assets/images/delivery_text_discount.svg"
    //     0xbb8170: ldr             x2, [x2, #0x5b8]
    // 0xbb8174: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xbb8174: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xbb8178: ldr             x4, [x4, #0x8e0]
    // 0xbb817c: r0 = SvgPicture.asset()
    //     0xbb817c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbb8180: ldur            x0, [fp, #-0x20]
    // 0xbb8184: cmp             w0, NULL
    // 0xbb8188: b.ne            #0xbb8194
    // 0xbb818c: r1 = Null
    //     0xbb818c: mov             x1, NULL
    // 0xbb8190: b               #0xbb81c0
    // 0xbb8194: LoadField: r1 = r0->field_f
    //     0xbb8194: ldur            w1, [x0, #0xf]
    // 0xbb8198: DecompressPointer r1
    //     0xbb8198: add             x1, x1, HEAP, lsl #32
    // 0xbb819c: cmp             w1, NULL
    // 0xbb81a0: b.ne            #0xbb81ac
    // 0xbb81a4: r1 = Null
    //     0xbb81a4: mov             x1, NULL
    // 0xbb81a8: b               #0xbb81c0
    // 0xbb81ac: LoadField: r2 = r1->field_7
    //     0xbb81ac: ldur            w2, [x1, #7]
    // 0xbb81b0: cbnz            w2, #0xbb81bc
    // 0xbb81b4: r1 = false
    //     0xbb81b4: add             x1, NULL, #0x30  ; false
    // 0xbb81b8: b               #0xbb81c0
    // 0xbb81bc: r1 = true
    //     0xbb81bc: add             x1, NULL, #0x20  ; true
    // 0xbb81c0: cmp             w1, NULL
    // 0xbb81c4: b.eq            #0xbb81f8
    // 0xbb81c8: tbnz            w1, #4, #0xbb81f8
    // 0xbb81cc: cmp             w0, NULL
    // 0xbb81d0: b.ne            #0xbb81dc
    // 0xbb81d4: r0 = Null
    //     0xbb81d4: mov             x0, NULL
    // 0xbb81d8: b               #0xbb81e8
    // 0xbb81dc: LoadField: r1 = r0->field_f
    //     0xbb81dc: ldur            w1, [x0, #0xf]
    // 0xbb81e0: DecompressPointer r1
    //     0xbb81e0: add             x1, x1, HEAP, lsl #32
    // 0xbb81e4: mov             x0, x1
    // 0xbb81e8: str             x0, [SP]
    // 0xbb81ec: r0 = _interpolateSingle()
    //     0xbb81ec: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbb81f0: mov             x3, x0
    // 0xbb81f4: b               #0xbb825c
    // 0xbb81f8: ldur            x0, [fp, #-0x10]
    // 0xbb81fc: LoadField: r1 = r0->field_b
    //     0xbb81fc: ldur            w1, [x0, #0xb]
    // 0xbb8200: DecompressPointer r1
    //     0xbb8200: add             x1, x1, HEAP, lsl #32
    // 0xbb8204: cmp             w1, NULL
    // 0xbb8208: b.eq            #0xbb84dc
    // 0xbb820c: LoadField: r0 = r1->field_b
    //     0xbb820c: ldur            w0, [x1, #0xb]
    // 0xbb8210: DecompressPointer r0
    //     0xbb8210: add             x0, x0, HEAP, lsl #32
    // 0xbb8214: LoadField: r1 = r0->field_b
    //     0xbb8214: ldur            w1, [x0, #0xb]
    // 0xbb8218: DecompressPointer r1
    //     0xbb8218: add             x1, x1, HEAP, lsl #32
    // 0xbb821c: cmp             w1, NULL
    // 0xbb8220: b.ne            #0xbb822c
    // 0xbb8224: r0 = Null
    //     0xbb8224: mov             x0, NULL
    // 0xbb8228: b               #0xbb8250
    // 0xbb822c: LoadField: r0 = r1->field_1b
    //     0xbb822c: ldur            w0, [x1, #0x1b]
    // 0xbb8230: DecompressPointer r0
    //     0xbb8230: add             x0, x0, HEAP, lsl #32
    // 0xbb8234: cmp             w0, NULL
    // 0xbb8238: b.ne            #0xbb8244
    // 0xbb823c: r0 = Null
    //     0xbb823c: mov             x0, NULL
    // 0xbb8240: b               #0xbb8250
    // 0xbb8244: LoadField: r1 = r0->field_b
    //     0xbb8244: ldur            w1, [x0, #0xb]
    // 0xbb8248: DecompressPointer r1
    //     0xbb8248: add             x1, x1, HEAP, lsl #32
    // 0xbb824c: mov             x0, x1
    // 0xbb8250: str             x0, [SP]
    // 0xbb8254: r0 = _interpolateSingle()
    //     0xbb8254: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbb8258: mov             x3, x0
    // 0xbb825c: ldur            x2, [fp, #-8]
    // 0xbb8260: ldur            x0, [fp, #-0x28]
    // 0xbb8264: ldur            d0, [fp, #-0x38]
    // 0xbb8268: ldur            x1, [fp, #-0x18]
    // 0xbb826c: stur            x3, [fp, #-0x10]
    // 0xbb8270: r0 = of()
    //     0xbb8270: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb8274: LoadField: r1 = r0->field_87
    //     0xbb8274: ldur            w1, [x0, #0x87]
    // 0xbb8278: DecompressPointer r1
    //     0xbb8278: add             x1, x1, HEAP, lsl #32
    // 0xbb827c: LoadField: r0 = r1->field_2b
    //     0xbb827c: ldur            w0, [x1, #0x2b]
    // 0xbb8280: DecompressPointer r0
    //     0xbb8280: add             x0, x0, HEAP, lsl #32
    // 0xbb8284: r16 = Instance_Color
    //     0xbb8284: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb8288: ldr             x16, [x16, #0x858]
    // 0xbb828c: r30 = 14.000000
    //     0xbb828c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb8290: ldr             lr, [lr, #0x1d8]
    // 0xbb8294: stp             lr, x16, [SP]
    // 0xbb8298: mov             x1, x0
    // 0xbb829c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb829c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb82a0: ldr             x4, [x4, #0x9b8]
    // 0xbb82a4: r0 = copyWith()
    //     0xbb82a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb82a8: stur            x0, [fp, #-0x18]
    // 0xbb82ac: r0 = Text()
    //     0xbb82ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb82b0: mov             x1, x0
    // 0xbb82b4: ldur            x0, [fp, #-0x10]
    // 0xbb82b8: stur            x1, [fp, #-0x20]
    // 0xbb82bc: StoreField: r1->field_b = r0
    //     0xbb82bc: stur            w0, [x1, #0xb]
    // 0xbb82c0: ldur            x0, [fp, #-0x18]
    // 0xbb82c4: StoreField: r1->field_13 = r0
    //     0xbb82c4: stur            w0, [x1, #0x13]
    // 0xbb82c8: r0 = Instance_TextAlign
    //     0xbb82c8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbb82cc: StoreField: r1->field_1b = r0
    //     0xbb82cc: stur            w0, [x1, #0x1b]
    // 0xbb82d0: r0 = WidgetSpan()
    //     0xbb82d0: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xbb82d4: mov             x3, x0
    // 0xbb82d8: ldur            x0, [fp, #-0x20]
    // 0xbb82dc: stur            x3, [fp, #-0x10]
    // 0xbb82e0: StoreField: r3->field_13 = r0
    //     0xbb82e0: stur            w0, [x3, #0x13]
    // 0xbb82e4: r0 = Instance_PlaceholderAlignment
    //     0xbb82e4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46930] Obj!PlaceholderAlignment@d76421
    //     0xbb82e8: ldr             x0, [x0, #0x930]
    // 0xbb82ec: StoreField: r3->field_b = r0
    //     0xbb82ec: stur            w0, [x3, #0xb]
    // 0xbb82f0: r1 = Null
    //     0xbb82f0: mov             x1, NULL
    // 0xbb82f4: r2 = 2
    //     0xbb82f4: movz            x2, #0x2
    // 0xbb82f8: r0 = AllocateArray()
    //     0xbb82f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb82fc: mov             x2, x0
    // 0xbb8300: ldur            x0, [fp, #-0x10]
    // 0xbb8304: stur            x2, [fp, #-0x18]
    // 0xbb8308: StoreField: r2->field_f = r0
    //     0xbb8308: stur            w0, [x2, #0xf]
    // 0xbb830c: r1 = <InlineSpan>
    //     0xbb830c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbb8310: ldr             x1, [x1, #0xe40]
    // 0xbb8314: r0 = AllocateGrowableArray()
    //     0xbb8314: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb8318: mov             x1, x0
    // 0xbb831c: ldur            x0, [fp, #-0x18]
    // 0xbb8320: stur            x1, [fp, #-0x10]
    // 0xbb8324: StoreField: r1->field_f = r0
    //     0xbb8324: stur            w0, [x1, #0xf]
    // 0xbb8328: r0 = 2
    //     0xbb8328: movz            x0, #0x2
    // 0xbb832c: StoreField: r1->field_b = r0
    //     0xbb832c: stur            w0, [x1, #0xb]
    // 0xbb8330: r0 = TextSpan()
    //     0xbb8330: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbb8334: mov             x1, x0
    // 0xbb8338: ldur            x0, [fp, #-0x10]
    // 0xbb833c: stur            x1, [fp, #-0x18]
    // 0xbb8340: StoreField: r1->field_f = r0
    //     0xbb8340: stur            w0, [x1, #0xf]
    // 0xbb8344: r0 = Instance__DeferringMouseCursor
    //     0xbb8344: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbb8348: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb8348: stur            w0, [x1, #0x17]
    // 0xbb834c: r0 = RichText()
    //     0xbb834c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbb8350: mov             x1, x0
    // 0xbb8354: ldur            x2, [fp, #-0x18]
    // 0xbb8358: stur            x0, [fp, #-0x10]
    // 0xbb835c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb835c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb8360: r0 = RichText()
    //     0xbb8360: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbb8364: r0 = Padding()
    //     0xbb8364: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb8368: mov             x3, x0
    // 0xbb836c: r0 = Instance_EdgeInsets
    //     0xbb836c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbb8370: ldr             x0, [x0, #0xa78]
    // 0xbb8374: stur            x3, [fp, #-0x18]
    // 0xbb8378: StoreField: r3->field_f = r0
    //     0xbb8378: stur            w0, [x3, #0xf]
    // 0xbb837c: ldur            x0, [fp, #-0x10]
    // 0xbb8380: StoreField: r3->field_b = r0
    //     0xbb8380: stur            w0, [x3, #0xb]
    // 0xbb8384: r1 = Null
    //     0xbb8384: mov             x1, NULL
    // 0xbb8388: r2 = 4
    //     0xbb8388: movz            x2, #0x4
    // 0xbb838c: r0 = AllocateArray()
    //     0xbb838c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb8390: mov             x2, x0
    // 0xbb8394: ldur            x0, [fp, #-0x28]
    // 0xbb8398: stur            x2, [fp, #-0x10]
    // 0xbb839c: StoreField: r2->field_f = r0
    //     0xbb839c: stur            w0, [x2, #0xf]
    // 0xbb83a0: ldur            x0, [fp, #-0x18]
    // 0xbb83a4: StoreField: r2->field_13 = r0
    //     0xbb83a4: stur            w0, [x2, #0x13]
    // 0xbb83a8: r1 = <Widget>
    //     0xbb83a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb83ac: r0 = AllocateGrowableArray()
    //     0xbb83ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb83b0: mov             x1, x0
    // 0xbb83b4: ldur            x0, [fp, #-0x10]
    // 0xbb83b8: stur            x1, [fp, #-0x18]
    // 0xbb83bc: StoreField: r1->field_f = r0
    //     0xbb83bc: stur            w0, [x1, #0xf]
    // 0xbb83c0: r0 = 4
    //     0xbb83c0: movz            x0, #0x4
    // 0xbb83c4: StoreField: r1->field_b = r0
    //     0xbb83c4: stur            w0, [x1, #0xb]
    // 0xbb83c8: r0 = Row()
    //     0xbb83c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb83cc: mov             x1, x0
    // 0xbb83d0: r0 = Instance_Axis
    //     0xbb83d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb83d4: stur            x1, [fp, #-0x20]
    // 0xbb83d8: StoreField: r1->field_f = r0
    //     0xbb83d8: stur            w0, [x1, #0xf]
    // 0xbb83dc: r0 = Instance_MainAxisAlignment
    //     0xbb83dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbb83e0: ldr             x0, [x0, #0xab0]
    // 0xbb83e4: StoreField: r1->field_13 = r0
    //     0xbb83e4: stur            w0, [x1, #0x13]
    // 0xbb83e8: r0 = Instance_MainAxisSize
    //     0xbb83e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb83ec: ldr             x0, [x0, #0xa10]
    // 0xbb83f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb83f0: stur            w0, [x1, #0x17]
    // 0xbb83f4: r0 = Instance_CrossAxisAlignment
    //     0xbb83f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb83f8: ldr             x0, [x0, #0xa18]
    // 0xbb83fc: StoreField: r1->field_1b = r0
    //     0xbb83fc: stur            w0, [x1, #0x1b]
    // 0xbb8400: r0 = Instance_VerticalDirection
    //     0xbb8400: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb8404: ldr             x0, [x0, #0xa20]
    // 0xbb8408: StoreField: r1->field_23 = r0
    //     0xbb8408: stur            w0, [x1, #0x23]
    // 0xbb840c: r0 = Instance_Clip
    //     0xbb840c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb8410: ldr             x0, [x0, #0x38]
    // 0xbb8414: StoreField: r1->field_2b = r0
    //     0xbb8414: stur            w0, [x1, #0x2b]
    // 0xbb8418: StoreField: r1->field_2f = rZR
    //     0xbb8418: stur            xzr, [x1, #0x2f]
    // 0xbb841c: ldur            x0, [fp, #-0x18]
    // 0xbb8420: StoreField: r1->field_b = r0
    //     0xbb8420: stur            w0, [x1, #0xb]
    // 0xbb8424: ldur            d0, [fp, #-0x38]
    // 0xbb8428: r0 = inline_Allocate_Double()
    //     0xbb8428: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbb842c: add             x0, x0, #0x10
    //     0xbb8430: cmp             x2, x0
    //     0xbb8434: b.ls            #0xbb84e0
    //     0xbb8438: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb843c: sub             x0, x0, #0xf
    //     0xbb8440: movz            x2, #0xe15c
    //     0xbb8444: movk            x2, #0x3, lsl #16
    //     0xbb8448: stur            x2, [x0, #-1]
    // 0xbb844c: StoreField: r0->field_7 = d0
    //     0xbb844c: stur            d0, [x0, #7]
    // 0xbb8450: stur            x0, [fp, #-0x10]
    // 0xbb8454: r0 = Container()
    //     0xbb8454: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb8458: stur            x0, [fp, #-0x18]
    // 0xbb845c: r16 = 44.000000
    //     0xbb845c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbb8460: ldr             x16, [x16, #0xad8]
    // 0xbb8464: ldur            lr, [fp, #-0x10]
    // 0xbb8468: stp             lr, x16, [SP, #0x10]
    // 0xbb846c: ldur            x16, [fp, #-0x30]
    // 0xbb8470: ldur            lr, [fp, #-0x20]
    // 0xbb8474: stp             lr, x16, [SP]
    // 0xbb8478: mov             x1, x0
    // 0xbb847c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xbb847c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xbb8480: ldr             x4, [x4, #0x8c0]
    // 0xbb8484: r0 = Container()
    //     0xbb8484: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb8488: r0 = Visibility()
    //     0xbb8488: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb848c: ldur            x1, [fp, #-0x18]
    // 0xbb8490: StoreField: r0->field_b = r1
    //     0xbb8490: stur            w1, [x0, #0xb]
    // 0xbb8494: r1 = Instance_SizedBox
    //     0xbb8494: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb8498: StoreField: r0->field_f = r1
    //     0xbb8498: stur            w1, [x0, #0xf]
    // 0xbb849c: ldur            x1, [fp, #-8]
    // 0xbb84a0: StoreField: r0->field_13 = r1
    //     0xbb84a0: stur            w1, [x0, #0x13]
    // 0xbb84a4: r1 = false
    //     0xbb84a4: add             x1, NULL, #0x30  ; false
    // 0xbb84a8: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb84a8: stur            w1, [x0, #0x17]
    // 0xbb84ac: StoreField: r0->field_1b = r1
    //     0xbb84ac: stur            w1, [x0, #0x1b]
    // 0xbb84b0: StoreField: r0->field_1f = r1
    //     0xbb84b0: stur            w1, [x0, #0x1f]
    // 0xbb84b4: StoreField: r0->field_23 = r1
    //     0xbb84b4: stur            w1, [x0, #0x23]
    // 0xbb84b8: StoreField: r0->field_27 = r1
    //     0xbb84b8: stur            w1, [x0, #0x27]
    // 0xbb84bc: StoreField: r0->field_2b = r1
    //     0xbb84bc: stur            w1, [x0, #0x2b]
    // 0xbb84c0: LeaveFrame
    //     0xbb84c0: mov             SP, fp
    //     0xbb84c4: ldp             fp, lr, [SP], #0x10
    // 0xbb84c8: ret
    //     0xbb84c8: ret             
    // 0xbb84cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb84cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb84d0: b               #0xbb7f60
    // 0xbb84d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb84d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb84d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb84d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb84dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb84dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb84e0: SaveReg d0
    //     0xbb84e0: str             q0, [SP, #-0x10]!
    // 0xbb84e4: SaveReg r1
    //     0xbb84e4: str             x1, [SP, #-8]!
    // 0xbb84e8: r0 = AllocateDouble()
    //     0xbb84e8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbb84ec: RestoreReg r1
    //     0xbb84ec: ldr             x1, [SP], #8
    // 0xbb84f0: RestoreReg d0
    //     0xbb84f0: ldr             q0, [SP], #0x10
    // 0xbb84f4: b               #0xbb844c
  }
}

// class id: 4017, size: 0x10, field offset: 0xc
//   const constructor, 
class DeliveryInfoWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8035c, size: 0x24
    // 0xc8035c: EnterFrame
    //     0xc8035c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80360: mov             fp, SP
    // 0xc80364: mov             x0, x1
    // 0xc80368: r1 = <DeliveryInfoWidget>
    //     0xc80368: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d10] TypeArguments: <DeliveryInfoWidget>
    //     0xc8036c: ldr             x1, [x1, #0xd10]
    // 0xc80370: r0 = _LineShowDeliveryInfoWidgetState()
    //     0xc80370: bl              #0xc80380  ; Allocate_LineShowDeliveryInfoWidgetStateStub -> _LineShowDeliveryInfoWidgetState (size=0x14)
    // 0xc80374: LeaveFrame
    //     0xc80374: mov             SP, fp
    //     0xc80378: ldp             fp, lr, [SP], #0x10
    // 0xc8037c: ret
    //     0xc8037c: ret             
  }
}
