// lib: , url: package:customer_app/app/presentation/views/basic/testimonials/testimonials_view.dart

// class id: 1049216, size: 0x8
class :: {
}

// class id: 4615, size: 0x14, field offset: 0x14
//   const constructor, 
class TestimonialsView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x1479ecc, size: 0x10c
    // 0x1479ecc: EnterFrame
    //     0x1479ecc: stp             fp, lr, [SP, #-0x10]!
    //     0x1479ed0: mov             fp, SP
    // 0x1479ed4: AllocStack(0x18)
    //     0x1479ed4: sub             SP, SP, #0x18
    // 0x1479ed8: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1479ed8: stur            x1, [fp, #-8]
    //     0x1479edc: stur            x2, [fp, #-0x10]
    // 0x1479ee0: CheckStackOverflow
    //     0x1479ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479ee4: cmp             SP, x16
    //     0x1479ee8: b.ls            #0x1479fd0
    // 0x1479eec: r1 = 2
    //     0x1479eec: movz            x1, #0x2
    // 0x1479ef0: r0 = AllocateContext()
    //     0x1479ef0: bl              #0x16f6108  ; AllocateContextStub
    // 0x1479ef4: mov             x1, x0
    // 0x1479ef8: ldur            x0, [fp, #-8]
    // 0x1479efc: stur            x1, [fp, #-0x18]
    // 0x1479f00: StoreField: r1->field_f = r0
    //     0x1479f00: stur            w0, [x1, #0xf]
    // 0x1479f04: ldur            x0, [fp, #-0x10]
    // 0x1479f08: StoreField: r1->field_13 = r0
    //     0x1479f08: stur            w0, [x1, #0x13]
    // 0x1479f0c: r0 = Obx()
    //     0x1479f0c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1479f10: ldur            x2, [fp, #-0x18]
    // 0x1479f14: r1 = Function '<anonymous closure>':.
    //     0x1479f14: add             x1, PP, #0x44, lsl #12  ; [pp+0x449a0] AnonymousClosure: (0x147beac), in [package:customer_app/app/presentation/views/basic/testimonials/testimonials_view.dart] TestimonialsView::body (0x1479ecc)
    //     0x1479f18: ldr             x1, [x1, #0x9a0]
    // 0x1479f1c: stur            x0, [fp, #-8]
    // 0x1479f20: r0 = AllocateClosure()
    //     0x1479f20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479f24: mov             x1, x0
    // 0x1479f28: ldur            x0, [fp, #-8]
    // 0x1479f2c: StoreField: r0->field_b = r1
    //     0x1479f2c: stur            w1, [x0, #0xb]
    // 0x1479f30: r0 = Padding()
    //     0x1479f30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1479f34: mov             x3, x0
    // 0x1479f38: r0 = Instance_EdgeInsets
    //     0x1479f38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1479f3c: ldr             x0, [x0, #0x1f0]
    // 0x1479f40: stur            x3, [fp, #-0x10]
    // 0x1479f44: StoreField: r3->field_f = r0
    //     0x1479f44: stur            w0, [x3, #0xf]
    // 0x1479f48: ldur            x0, [fp, #-8]
    // 0x1479f4c: StoreField: r3->field_b = r0
    //     0x1479f4c: stur            w0, [x3, #0xb]
    // 0x1479f50: ldur            x2, [fp, #-0x18]
    // 0x1479f54: r1 = Function '<anonymous closure>':.
    //     0x1479f54: add             x1, PP, #0x44, lsl #12  ; [pp+0x449a8] AnonymousClosure: (0x147bdfc), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x1479f58: ldr             x1, [x1, #0x9a8]
    // 0x1479f5c: r0 = AllocateClosure()
    //     0x1479f5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479f60: ldur            x2, [fp, #-0x18]
    // 0x1479f64: r1 = Function '<anonymous closure>':.
    //     0x1479f64: add             x1, PP, #0x44, lsl #12  ; [pp+0x449b0] AnonymousClosure: (0x147a04c), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x1479f68: ldr             x1, [x1, #0x9b0]
    // 0x1479f6c: stur            x0, [fp, #-8]
    // 0x1479f70: r0 = AllocateClosure()
    //     0x1479f70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479f74: stur            x0, [fp, #-0x18]
    // 0x1479f78: r0 = PagingView()
    //     0x1479f78: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x1479f7c: mov             x1, x0
    // 0x1479f80: ldur            x2, [fp, #-0x10]
    // 0x1479f84: ldur            x3, [fp, #-0x18]
    // 0x1479f88: ldur            x5, [fp, #-8]
    // 0x1479f8c: stur            x0, [fp, #-8]
    // 0x1479f90: r0 = PagingView()
    //     0x1479f90: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x1479f94: r0 = WillPopScope()
    //     0x1479f94: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1479f98: mov             x3, x0
    // 0x1479f9c: ldur            x0, [fp, #-8]
    // 0x1479fa0: stur            x3, [fp, #-0x10]
    // 0x1479fa4: StoreField: r3->field_b = r0
    //     0x1479fa4: stur            w0, [x3, #0xb]
    // 0x1479fa8: r1 = Function '<anonymous closure>':.
    //     0x1479fa8: add             x1, PP, #0x44, lsl #12  ; [pp+0x449b8] AnonymousClosure: (0x1479fd8), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x1479fac: ldr             x1, [x1, #0x9b8]
    // 0x1479fb0: r2 = Null
    //     0x1479fb0: mov             x2, NULL
    // 0x1479fb4: r0 = AllocateClosure()
    //     0x1479fb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479fb8: mov             x1, x0
    // 0x1479fbc: ldur            x0, [fp, #-0x10]
    // 0x1479fc0: StoreField: r0->field_f = r1
    //     0x1479fc0: stur            w1, [x0, #0xf]
    // 0x1479fc4: LeaveFrame
    //     0x1479fc4: mov             SP, fp
    //     0x1479fc8: ldp             fp, lr, [SP], #0x10
    // 0x1479fcc: ret
    //     0x1479fcc: ret             
    // 0x1479fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1479fd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479fd4: b               #0x1479eec
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x147beac, size: 0x610
    // 0x147beac: EnterFrame
    //     0x147beac: stp             fp, lr, [SP, #-0x10]!
    //     0x147beb0: mov             fp, SP
    // 0x147beb4: AllocStack(0x50)
    //     0x147beb4: sub             SP, SP, #0x50
    // 0x147beb8: SetupParameters()
    //     0x147beb8: ldr             x0, [fp, #0x10]
    //     0x147bebc: ldur            w2, [x0, #0x17]
    //     0x147bec0: add             x2, x2, HEAP, lsl #32
    //     0x147bec4: stur            x2, [fp, #-8]
    // 0x147bec8: CheckStackOverflow
    //     0x147bec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147becc: cmp             SP, x16
    //     0x147bed0: b.ls            #0x147c4b4
    // 0x147bed4: LoadField: r1 = r2->field_f
    //     0x147bed4: ldur            w1, [x2, #0xf]
    // 0x147bed8: DecompressPointer r1
    //     0x147bed8: add             x1, x1, HEAP, lsl #32
    // 0x147bedc: r0 = controller()
    //     0x147bedc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147bee0: LoadField: r1 = r0->field_53
    //     0x147bee0: ldur            w1, [x0, #0x53]
    // 0x147bee4: DecompressPointer r1
    //     0x147bee4: add             x1, x1, HEAP, lsl #32
    // 0x147bee8: r0 = value()
    //     0x147bee8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147beec: LoadField: r1 = r0->field_b
    //     0x147beec: ldur            w1, [x0, #0xb]
    // 0x147bef0: DecompressPointer r1
    //     0x147bef0: add             x1, x1, HEAP, lsl #32
    // 0x147bef4: cmp             w1, NULL
    // 0x147bef8: b.ne            #0x147bf08
    // 0x147befc: r0 = Instance_Center
    //     0x147befc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9a8] Obj!Center@d68361
    //     0x147bf00: ldr             x0, [x0, #0x9a8]
    // 0x147bf04: b               #0x147c4a8
    // 0x147bf08: ldur            x0, [fp, #-8]
    // 0x147bf0c: LoadField: r1 = r0->field_13
    //     0x147bf0c: ldur            w1, [x0, #0x13]
    // 0x147bf10: DecompressPointer r1
    //     0x147bf10: add             x1, x1, HEAP, lsl #32
    // 0x147bf14: r0 = of()
    //     0x147bf14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147bf18: LoadField: r1 = r0->field_87
    //     0x147bf18: ldur            w1, [x0, #0x87]
    // 0x147bf1c: DecompressPointer r1
    //     0x147bf1c: add             x1, x1, HEAP, lsl #32
    // 0x147bf20: LoadField: r0 = r1->field_7
    //     0x147bf20: ldur            w0, [x1, #7]
    // 0x147bf24: DecompressPointer r0
    //     0x147bf24: add             x0, x0, HEAP, lsl #32
    // 0x147bf28: r16 = Instance_Color
    //     0x147bf28: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x147bf2c: r30 = 21.000000
    //     0x147bf2c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x147bf30: ldr             lr, [lr, #0x9b0]
    // 0x147bf34: stp             lr, x16, [SP]
    // 0x147bf38: mov             x1, x0
    // 0x147bf3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x147bf3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x147bf40: ldr             x4, [x4, #0x9b8]
    // 0x147bf44: r0 = copyWith()
    //     0x147bf44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x147bf48: stur            x0, [fp, #-0x10]
    // 0x147bf4c: r0 = Text()
    //     0x147bf4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x147bf50: mov             x2, x0
    // 0x147bf54: r0 = "Customers Love Us"
    //     0x147bf54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c0] "Customers Love Us"
    //     0x147bf58: ldr             x0, [x0, #0x9c0]
    // 0x147bf5c: stur            x2, [fp, #-0x18]
    // 0x147bf60: StoreField: r2->field_b = r0
    //     0x147bf60: stur            w0, [x2, #0xb]
    // 0x147bf64: ldur            x0, [fp, #-0x10]
    // 0x147bf68: StoreField: r2->field_13 = r0
    //     0x147bf68: stur            w0, [x2, #0x13]
    // 0x147bf6c: ldur            x0, [fp, #-8]
    // 0x147bf70: LoadField: r1 = r0->field_13
    //     0x147bf70: ldur            w1, [x0, #0x13]
    // 0x147bf74: DecompressPointer r1
    //     0x147bf74: add             x1, x1, HEAP, lsl #32
    // 0x147bf78: r0 = of()
    //     0x147bf78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147bf7c: LoadField: r1 = r0->field_87
    //     0x147bf7c: ldur            w1, [x0, #0x87]
    // 0x147bf80: DecompressPointer r1
    //     0x147bf80: add             x1, x1, HEAP, lsl #32
    // 0x147bf84: LoadField: r0 = r1->field_7
    //     0x147bf84: ldur            w0, [x1, #7]
    // 0x147bf88: DecompressPointer r0
    //     0x147bf88: add             x0, x0, HEAP, lsl #32
    // 0x147bf8c: ldur            x2, [fp, #-8]
    // 0x147bf90: stur            x0, [fp, #-0x10]
    // 0x147bf94: LoadField: r1 = r2->field_13
    //     0x147bf94: ldur            w1, [x2, #0x13]
    // 0x147bf98: DecompressPointer r1
    //     0x147bf98: add             x1, x1, HEAP, lsl #32
    // 0x147bf9c: r0 = of()
    //     0x147bf9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147bfa0: LoadField: r1 = r0->field_5b
    //     0x147bfa0: ldur            w1, [x0, #0x5b]
    // 0x147bfa4: DecompressPointer r1
    //     0x147bfa4: add             x1, x1, HEAP, lsl #32
    // 0x147bfa8: r16 = 21.000000
    //     0x147bfa8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x147bfac: ldr             x16, [x16, #0x9b0]
    // 0x147bfb0: stp             x16, x1, [SP]
    // 0x147bfb4: ldur            x1, [fp, #-0x10]
    // 0x147bfb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x147bfb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x147bfbc: ldr             x4, [x4, #0x9b8]
    // 0x147bfc0: r0 = copyWith()
    //     0x147bfc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x147bfc4: stur            x0, [fp, #-0x10]
    // 0x147bfc8: r0 = Text()
    //     0x147bfc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x147bfcc: mov             x2, x0
    // 0x147bfd0: r0 = "—"
    //     0x147bfd0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c8] "—"
    //     0x147bfd4: ldr             x0, [x0, #0x9c8]
    // 0x147bfd8: stur            x2, [fp, #-0x20]
    // 0x147bfdc: StoreField: r2->field_b = r0
    //     0x147bfdc: stur            w0, [x2, #0xb]
    // 0x147bfe0: ldur            x0, [fp, #-0x10]
    // 0x147bfe4: StoreField: r2->field_13 = r0
    //     0x147bfe4: stur            w0, [x2, #0x13]
    // 0x147bfe8: r1 = Instance_Color
    //     0x147bfe8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x147bfec: d0 = 0.100000
    //     0x147bfec: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x147bff0: r0 = withOpacity()
    //     0x147bff0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x147bff4: stur            x0, [fp, #-0x10]
    // 0x147bff8: r0 = Divider()
    //     0x147bff8: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x147bffc: mov             x3, x0
    // 0x147c000: ldur            x0, [fp, #-0x10]
    // 0x147c004: stur            x3, [fp, #-0x28]
    // 0x147c008: StoreField: r3->field_1f = r0
    //     0x147c008: stur            w0, [x3, #0x1f]
    // 0x147c00c: r1 = Null
    //     0x147c00c: mov             x1, NULL
    // 0x147c010: r2 = 10
    //     0x147c010: movz            x2, #0xa
    // 0x147c014: r0 = AllocateArray()
    //     0x147c014: bl              #0x16f7198  ; AllocateArrayStub
    // 0x147c018: stur            x0, [fp, #-0x10]
    // 0x147c01c: r16 = "Showing "
    //     0x147c01c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d0] "Showing "
    //     0x147c020: ldr             x16, [x16, #0x9d0]
    // 0x147c024: StoreField: r0->field_f = r16
    //     0x147c024: stur            w16, [x0, #0xf]
    // 0x147c028: ldur            x2, [fp, #-8]
    // 0x147c02c: LoadField: r1 = r2->field_f
    //     0x147c02c: ldur            w1, [x2, #0xf]
    // 0x147c030: DecompressPointer r1
    //     0x147c030: add             x1, x1, HEAP, lsl #32
    // 0x147c034: r0 = controller()
    //     0x147c034: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c038: LoadField: r1 = r0->field_57
    //     0x147c038: ldur            w1, [x0, #0x57]
    // 0x147c03c: DecompressPointer r1
    //     0x147c03c: add             x1, x1, HEAP, lsl #32
    // 0x147c040: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x147c040: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x147c044: r0 = toList()
    //     0x147c044: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x147c048: LoadField: r1 = r0->field_b
    //     0x147c048: ldur            w1, [x0, #0xb]
    // 0x147c04c: ldur            x0, [fp, #-0x10]
    // 0x147c050: StoreField: r0->field_13 = r1
    //     0x147c050: stur            w1, [x0, #0x13]
    // 0x147c054: r16 = " out of "
    //     0x147c054: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d8] " out of "
    //     0x147c058: ldr             x16, [x16, #0x9d8]
    // 0x147c05c: ArrayStore: r0[0] = r16  ; List_4
    //     0x147c05c: stur            w16, [x0, #0x17]
    // 0x147c060: ldur            x2, [fp, #-8]
    // 0x147c064: LoadField: r1 = r2->field_f
    //     0x147c064: ldur            w1, [x2, #0xf]
    // 0x147c068: DecompressPointer r1
    //     0x147c068: add             x1, x1, HEAP, lsl #32
    // 0x147c06c: r0 = controller()
    //     0x147c06c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c070: LoadField: r1 = r0->field_53
    //     0x147c070: ldur            w1, [x0, #0x53]
    // 0x147c074: DecompressPointer r1
    //     0x147c074: add             x1, x1, HEAP, lsl #32
    // 0x147c078: r0 = value()
    //     0x147c078: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c07c: LoadField: r1 = r0->field_b
    //     0x147c07c: ldur            w1, [x0, #0xb]
    // 0x147c080: DecompressPointer r1
    //     0x147c080: add             x1, x1, HEAP, lsl #32
    // 0x147c084: cmp             w1, NULL
    // 0x147c088: b.ne            #0x147c094
    // 0x147c08c: r0 = Null
    //     0x147c08c: mov             x0, NULL
    // 0x147c090: b               #0x147c0b8
    // 0x147c094: LoadField: r0 = r1->field_b
    //     0x147c094: ldur            w0, [x1, #0xb]
    // 0x147c098: DecompressPointer r0
    //     0x147c098: add             x0, x0, HEAP, lsl #32
    // 0x147c09c: cmp             w0, NULL
    // 0x147c0a0: b.ne            #0x147c0ac
    // 0x147c0a4: r0 = Null
    //     0x147c0a4: mov             x0, NULL
    // 0x147c0a8: b               #0x147c0b8
    // 0x147c0ac: LoadField: r1 = r0->field_f
    //     0x147c0ac: ldur            w1, [x0, #0xf]
    // 0x147c0b0: DecompressPointer r1
    //     0x147c0b0: add             x1, x1, HEAP, lsl #32
    // 0x147c0b4: mov             x0, x1
    // 0x147c0b8: ldur            x3, [fp, #-8]
    // 0x147c0bc: ldur            x6, [fp, #-0x18]
    // 0x147c0c0: ldur            x5, [fp, #-0x20]
    // 0x147c0c4: ldur            x4, [fp, #-0x28]
    // 0x147c0c8: ldur            x2, [fp, #-0x10]
    // 0x147c0cc: mov             x1, x2
    // 0x147c0d0: ArrayStore: r1[3] = r0  ; List_4
    //     0x147c0d0: add             x25, x1, #0x1b
    //     0x147c0d4: str             w0, [x25]
    //     0x147c0d8: tbz             w0, #0, #0x147c0f4
    //     0x147c0dc: ldurb           w16, [x1, #-1]
    //     0x147c0e0: ldurb           w17, [x0, #-1]
    //     0x147c0e4: and             x16, x17, x16, lsr #2
    //     0x147c0e8: tst             x16, HEAP, lsr #32
    //     0x147c0ec: b.eq            #0x147c0f4
    //     0x147c0f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x147c0f4: r16 = " testimonials"
    //     0x147c0f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e0] " testimonials"
    //     0x147c0f8: ldr             x16, [x16, #0x9e0]
    // 0x147c0fc: StoreField: r2->field_1f = r16
    //     0x147c0fc: stur            w16, [x2, #0x1f]
    // 0x147c100: str             x2, [SP]
    // 0x147c104: r0 = _interpolate()
    //     0x147c104: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x147c108: mov             x2, x0
    // 0x147c10c: ldur            x0, [fp, #-8]
    // 0x147c110: stur            x2, [fp, #-0x10]
    // 0x147c114: LoadField: r1 = r0->field_13
    //     0x147c114: ldur            w1, [x0, #0x13]
    // 0x147c118: DecompressPointer r1
    //     0x147c118: add             x1, x1, HEAP, lsl #32
    // 0x147c11c: r0 = of()
    //     0x147c11c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147c120: LoadField: r1 = r0->field_87
    //     0x147c120: ldur            w1, [x0, #0x87]
    // 0x147c124: DecompressPointer r1
    //     0x147c124: add             x1, x1, HEAP, lsl #32
    // 0x147c128: LoadField: r0 = r1->field_2b
    //     0x147c128: ldur            w0, [x1, #0x2b]
    // 0x147c12c: DecompressPointer r0
    //     0x147c12c: add             x0, x0, HEAP, lsl #32
    // 0x147c130: stur            x0, [fp, #-0x30]
    // 0x147c134: r1 = Instance_Color
    //     0x147c134: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x147c138: d0 = 0.700000
    //     0x147c138: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x147c13c: ldr             d0, [x17, #0xf48]
    // 0x147c140: r0 = withOpacity()
    //     0x147c140: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x147c144: r16 = 12.000000
    //     0x147c144: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x147c148: ldr             x16, [x16, #0x9e8]
    // 0x147c14c: stp             x16, x0, [SP]
    // 0x147c150: ldur            x1, [fp, #-0x30]
    // 0x147c154: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x147c154: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x147c158: ldr             x4, [x4, #0x9b8]
    // 0x147c15c: r0 = copyWith()
    //     0x147c15c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x147c160: stur            x0, [fp, #-0x30]
    // 0x147c164: r0 = Text()
    //     0x147c164: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x147c168: mov             x3, x0
    // 0x147c16c: ldur            x0, [fp, #-0x10]
    // 0x147c170: stur            x3, [fp, #-0x38]
    // 0x147c174: StoreField: r3->field_b = r0
    //     0x147c174: stur            w0, [x3, #0xb]
    // 0x147c178: ldur            x0, [fp, #-0x30]
    // 0x147c17c: StoreField: r3->field_13 = r0
    //     0x147c17c: stur            w0, [x3, #0x13]
    // 0x147c180: r1 = Null
    //     0x147c180: mov             x1, NULL
    // 0x147c184: r2 = 10
    //     0x147c184: movz            x2, #0xa
    // 0x147c188: r0 = AllocateArray()
    //     0x147c188: bl              #0x16f7198  ; AllocateArrayStub
    // 0x147c18c: mov             x2, x0
    // 0x147c190: ldur            x0, [fp, #-0x18]
    // 0x147c194: stur            x2, [fp, #-0x10]
    // 0x147c198: StoreField: r2->field_f = r0
    //     0x147c198: stur            w0, [x2, #0xf]
    // 0x147c19c: ldur            x0, [fp, #-0x20]
    // 0x147c1a0: StoreField: r2->field_13 = r0
    //     0x147c1a0: stur            w0, [x2, #0x13]
    // 0x147c1a4: ldur            x0, [fp, #-0x28]
    // 0x147c1a8: ArrayStore: r2[0] = r0  ; List_4
    //     0x147c1a8: stur            w0, [x2, #0x17]
    // 0x147c1ac: ldur            x0, [fp, #-0x38]
    // 0x147c1b0: StoreField: r2->field_1b = r0
    //     0x147c1b0: stur            w0, [x2, #0x1b]
    // 0x147c1b4: r16 = Instance_SizedBox
    //     0x147c1b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x147c1b8: ldr             x16, [x16, #0x9f0]
    // 0x147c1bc: StoreField: r2->field_1f = r16
    //     0x147c1bc: stur            w16, [x2, #0x1f]
    // 0x147c1c0: r1 = <Widget>
    //     0x147c1c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x147c1c4: r0 = AllocateGrowableArray()
    //     0x147c1c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x147c1c8: mov             x2, x0
    // 0x147c1cc: ldur            x0, [fp, #-0x10]
    // 0x147c1d0: stur            x2, [fp, #-0x18]
    // 0x147c1d4: StoreField: r2->field_f = r0
    //     0x147c1d4: stur            w0, [x2, #0xf]
    // 0x147c1d8: r0 = 10
    //     0x147c1d8: movz            x0, #0xa
    // 0x147c1dc: StoreField: r2->field_b = r0
    //     0x147c1dc: stur            w0, [x2, #0xb]
    // 0x147c1e0: ldur            x0, [fp, #-8]
    // 0x147c1e4: LoadField: r1 = r0->field_f
    //     0x147c1e4: ldur            w1, [x0, #0xf]
    // 0x147c1e8: DecompressPointer r1
    //     0x147c1e8: add             x1, x1, HEAP, lsl #32
    // 0x147c1ec: r0 = controller()
    //     0x147c1ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c1f0: LoadField: r1 = r0->field_67
    //     0x147c1f0: ldur            w1, [x0, #0x67]
    // 0x147c1f4: DecompressPointer r1
    //     0x147c1f4: add             x1, x1, HEAP, lsl #32
    // 0x147c1f8: r0 = LoadClassIdInstr(r1)
    //     0x147c1f8: ldur            x0, [x1, #-1]
    //     0x147c1fc: ubfx            x0, x0, #0xc, #0x14
    // 0x147c200: r16 = "testimonial"
    //     0x147c200: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f8] "testimonial"
    //     0x147c204: ldr             x16, [x16, #0x9f8]
    // 0x147c208: stp             x16, x1, [SP]
    // 0x147c20c: mov             lr, x0
    // 0x147c210: ldr             lr, [x21, lr, lsl #3]
    // 0x147c214: blr             lr
    // 0x147c218: tbnz            w0, #4, #0x147c344
    // 0x147c21c: ldur            x2, [fp, #-8]
    // 0x147c220: ldur            x0, [fp, #-0x18]
    // 0x147c224: LoadField: r1 = r2->field_f
    //     0x147c224: ldur            w1, [x2, #0xf]
    // 0x147c228: DecompressPointer r1
    //     0x147c228: add             x1, x1, HEAP, lsl #32
    // 0x147c22c: r0 = controller()
    //     0x147c22c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c230: LoadField: r1 = r0->field_57
    //     0x147c230: ldur            w1, [x0, #0x57]
    // 0x147c234: DecompressPointer r1
    //     0x147c234: add             x1, x1, HEAP, lsl #32
    // 0x147c238: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x147c238: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x147c23c: r0 = toList()
    //     0x147c23c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x147c240: r1 = Instance_Color
    //     0x147c240: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x147c244: d0 = 0.100000
    //     0x147c244: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x147c248: stur            x0, [fp, #-0x10]
    // 0x147c24c: r0 = withOpacity()
    //     0x147c24c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x147c250: mov             x2, x0
    // 0x147c254: r1 = Null
    //     0x147c254: mov             x1, NULL
    // 0x147c258: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x147c258: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x147c25c: r0 = Border.all()
    //     0x147c25c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x147c260: stur            x0, [fp, #-0x20]
    // 0x147c264: r0 = Radius()
    //     0x147c264: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x147c268: d0 = 12.000000
    //     0x147c268: fmov            d0, #12.00000000
    // 0x147c26c: stur            x0, [fp, #-0x28]
    // 0x147c270: StoreField: r0->field_7 = d0
    //     0x147c270: stur            d0, [x0, #7]
    // 0x147c274: StoreField: r0->field_f = d0
    //     0x147c274: stur            d0, [x0, #0xf]
    // 0x147c278: r0 = BorderRadius()
    //     0x147c278: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x147c27c: mov             x1, x0
    // 0x147c280: ldur            x0, [fp, #-0x28]
    // 0x147c284: stur            x1, [fp, #-0x30]
    // 0x147c288: StoreField: r1->field_7 = r0
    //     0x147c288: stur            w0, [x1, #7]
    // 0x147c28c: StoreField: r1->field_b = r0
    //     0x147c28c: stur            w0, [x1, #0xb]
    // 0x147c290: StoreField: r1->field_f = r0
    //     0x147c290: stur            w0, [x1, #0xf]
    // 0x147c294: StoreField: r1->field_13 = r0
    //     0x147c294: stur            w0, [x1, #0x13]
    // 0x147c298: r0 = TestimonialViewAll()
    //     0x147c298: bl              #0x147c4c8  ; AllocateTestimonialViewAllStub -> TestimonialViewAll (size=0x1c)
    // 0x147c29c: mov             x2, x0
    // 0x147c2a0: ldur            x0, [fp, #-0x10]
    // 0x147c2a4: stur            x2, [fp, #-0x28]
    // 0x147c2a8: StoreField: r2->field_b = r0
    //     0x147c2a8: stur            w0, [x2, #0xb]
    // 0x147c2ac: ldur            x0, [fp, #-0x30]
    // 0x147c2b0: StoreField: r2->field_f = r0
    //     0x147c2b0: stur            w0, [x2, #0xf]
    // 0x147c2b4: ldur            x0, [fp, #-0x20]
    // 0x147c2b8: StoreField: r2->field_13 = r0
    //     0x147c2b8: stur            w0, [x2, #0x13]
    // 0x147c2bc: r0 = Instance_Color
    //     0x147c2bc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x147c2c0: ldr             x0, [x0, #0xf88]
    // 0x147c2c4: ArrayStore: r2[0] = r0  ; List_4
    //     0x147c2c4: stur            w0, [x2, #0x17]
    // 0x147c2c8: ldur            x0, [fp, #-0x18]
    // 0x147c2cc: LoadField: r1 = r0->field_b
    //     0x147c2cc: ldur            w1, [x0, #0xb]
    // 0x147c2d0: LoadField: r3 = r0->field_f
    //     0x147c2d0: ldur            w3, [x0, #0xf]
    // 0x147c2d4: DecompressPointer r3
    //     0x147c2d4: add             x3, x3, HEAP, lsl #32
    // 0x147c2d8: LoadField: r4 = r3->field_b
    //     0x147c2d8: ldur            w4, [x3, #0xb]
    // 0x147c2dc: r3 = LoadInt32Instr(r1)
    //     0x147c2dc: sbfx            x3, x1, #1, #0x1f
    // 0x147c2e0: stur            x3, [fp, #-0x40]
    // 0x147c2e4: r1 = LoadInt32Instr(r4)
    //     0x147c2e4: sbfx            x1, x4, #1, #0x1f
    // 0x147c2e8: cmp             x3, x1
    // 0x147c2ec: b.ne            #0x147c2f8
    // 0x147c2f0: mov             x1, x0
    // 0x147c2f4: r0 = _growToNextCapacity()
    //     0x147c2f4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x147c2f8: ldur            x2, [fp, #-0x18]
    // 0x147c2fc: ldur            x3, [fp, #-0x40]
    // 0x147c300: add             x0, x3, #1
    // 0x147c304: lsl             x1, x0, #1
    // 0x147c308: StoreField: r2->field_b = r1
    //     0x147c308: stur            w1, [x2, #0xb]
    // 0x147c30c: LoadField: r1 = r2->field_f
    //     0x147c30c: ldur            w1, [x2, #0xf]
    // 0x147c310: DecompressPointer r1
    //     0x147c310: add             x1, x1, HEAP, lsl #32
    // 0x147c314: ldur            x0, [fp, #-0x28]
    // 0x147c318: ArrayStore: r1[r3] = r0  ; List_4
    //     0x147c318: add             x25, x1, x3, lsl #2
    //     0x147c31c: add             x25, x25, #0xf
    //     0x147c320: str             w0, [x25]
    //     0x147c324: tbz             w0, #0, #0x147c340
    //     0x147c328: ldurb           w16, [x1, #-1]
    //     0x147c32c: ldurb           w17, [x0, #-1]
    //     0x147c330: and             x16, x17, x16, lsr #2
    //     0x147c334: tst             x16, HEAP, lsr #32
    //     0x147c338: b.eq            #0x147c340
    //     0x147c33c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x147c340: b               #0x147c348
    // 0x147c344: ldur            x2, [fp, #-0x18]
    // 0x147c348: ldur            x0, [fp, #-8]
    // 0x147c34c: LoadField: r1 = r0->field_f
    //     0x147c34c: ldur            w1, [x0, #0xf]
    // 0x147c350: DecompressPointer r1
    //     0x147c350: add             x1, x1, HEAP, lsl #32
    // 0x147c354: r0 = controller()
    //     0x147c354: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c358: LoadField: r1 = r0->field_67
    //     0x147c358: ldur            w1, [x0, #0x67]
    // 0x147c35c: DecompressPointer r1
    //     0x147c35c: add             x1, x1, HEAP, lsl #32
    // 0x147c360: r0 = LoadClassIdInstr(r1)
    //     0x147c360: ldur            x0, [x1, #-1]
    //     0x147c364: ubfx            x0, x0, #0xc, #0x14
    // 0x147c368: r16 = "product_testimonial"
    //     0x147c368: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea00] "product_testimonial"
    //     0x147c36c: ldr             x16, [x16, #0xa00]
    // 0x147c370: stp             x16, x1, [SP]
    // 0x147c374: mov             lr, x0
    // 0x147c378: ldr             lr, [x21, lr, lsl #3]
    // 0x147c37c: blr             lr
    // 0x147c380: tbnz            w0, #4, #0x147c450
    // 0x147c384: ldur            x1, [fp, #-8]
    // 0x147c388: ldur            x0, [fp, #-0x18]
    // 0x147c38c: LoadField: r2 = r1->field_f
    //     0x147c38c: ldur            w2, [x1, #0xf]
    // 0x147c390: DecompressPointer r2
    //     0x147c390: add             x2, x2, HEAP, lsl #32
    // 0x147c394: mov             x1, x2
    // 0x147c398: r0 = controller()
    //     0x147c398: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c39c: LoadField: r1 = r0->field_57
    //     0x147c39c: ldur            w1, [x0, #0x57]
    // 0x147c3a0: DecompressPointer r1
    //     0x147c3a0: add             x1, x1, HEAP, lsl #32
    // 0x147c3a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x147c3a4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x147c3a8: r0 = toList()
    //     0x147c3a8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x147c3ac: stur            x0, [fp, #-8]
    // 0x147c3b0: r0 = ProductTestimonialViewAll()
    //     0x147c3b0: bl              #0x147c4bc  ; AllocateProductTestimonialViewAllStub -> ProductTestimonialViewAll (size=0x1c)
    // 0x147c3b4: mov             x2, x0
    // 0x147c3b8: ldur            x0, [fp, #-8]
    // 0x147c3bc: stur            x2, [fp, #-0x10]
    // 0x147c3c0: StoreField: r2->field_b = r0
    //     0x147c3c0: stur            w0, [x2, #0xb]
    // 0x147c3c4: d0 = 12.000000
    //     0x147c3c4: fmov            d0, #12.00000000
    // 0x147c3c8: StoreField: r2->field_f = d0
    //     0x147c3c8: stur            d0, [x2, #0xf]
    // 0x147c3cc: r0 = Instance_Color
    //     0x147c3cc: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x147c3d0: ArrayStore: r2[0] = r0  ; List_4
    //     0x147c3d0: stur            w0, [x2, #0x17]
    // 0x147c3d4: ldur            x0, [fp, #-0x18]
    // 0x147c3d8: LoadField: r1 = r0->field_b
    //     0x147c3d8: ldur            w1, [x0, #0xb]
    // 0x147c3dc: LoadField: r3 = r0->field_f
    //     0x147c3dc: ldur            w3, [x0, #0xf]
    // 0x147c3e0: DecompressPointer r3
    //     0x147c3e0: add             x3, x3, HEAP, lsl #32
    // 0x147c3e4: LoadField: r4 = r3->field_b
    //     0x147c3e4: ldur            w4, [x3, #0xb]
    // 0x147c3e8: r3 = LoadInt32Instr(r1)
    //     0x147c3e8: sbfx            x3, x1, #1, #0x1f
    // 0x147c3ec: stur            x3, [fp, #-0x40]
    // 0x147c3f0: r1 = LoadInt32Instr(r4)
    //     0x147c3f0: sbfx            x1, x4, #1, #0x1f
    // 0x147c3f4: cmp             x3, x1
    // 0x147c3f8: b.ne            #0x147c404
    // 0x147c3fc: mov             x1, x0
    // 0x147c400: r0 = _growToNextCapacity()
    //     0x147c400: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x147c404: ldur            x2, [fp, #-0x18]
    // 0x147c408: ldur            x3, [fp, #-0x40]
    // 0x147c40c: add             x0, x3, #1
    // 0x147c410: lsl             x1, x0, #1
    // 0x147c414: StoreField: r2->field_b = r1
    //     0x147c414: stur            w1, [x2, #0xb]
    // 0x147c418: LoadField: r1 = r2->field_f
    //     0x147c418: ldur            w1, [x2, #0xf]
    // 0x147c41c: DecompressPointer r1
    //     0x147c41c: add             x1, x1, HEAP, lsl #32
    // 0x147c420: ldur            x0, [fp, #-0x10]
    // 0x147c424: ArrayStore: r1[r3] = r0  ; List_4
    //     0x147c424: add             x25, x1, x3, lsl #2
    //     0x147c428: add             x25, x25, #0xf
    //     0x147c42c: str             w0, [x25]
    //     0x147c430: tbz             w0, #0, #0x147c44c
    //     0x147c434: ldurb           w16, [x1, #-1]
    //     0x147c438: ldurb           w17, [x0, #-1]
    //     0x147c43c: and             x16, x17, x16, lsr #2
    //     0x147c440: tst             x16, HEAP, lsr #32
    //     0x147c444: b.eq            #0x147c44c
    //     0x147c448: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x147c44c: b               #0x147c454
    // 0x147c450: ldur            x2, [fp, #-0x18]
    // 0x147c454: r0 = Column()
    //     0x147c454: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x147c458: r1 = Instance_Axis
    //     0x147c458: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x147c45c: StoreField: r0->field_f = r1
    //     0x147c45c: stur            w1, [x0, #0xf]
    // 0x147c460: r1 = Instance_MainAxisAlignment
    //     0x147c460: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x147c464: ldr             x1, [x1, #0xa08]
    // 0x147c468: StoreField: r0->field_13 = r1
    //     0x147c468: stur            w1, [x0, #0x13]
    // 0x147c46c: r1 = Instance_MainAxisSize
    //     0x147c46c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x147c470: ldr             x1, [x1, #0xa10]
    // 0x147c474: ArrayStore: r0[0] = r1  ; List_4
    //     0x147c474: stur            w1, [x0, #0x17]
    // 0x147c478: r1 = Instance_CrossAxisAlignment
    //     0x147c478: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x147c47c: ldr             x1, [x1, #0xa18]
    // 0x147c480: StoreField: r0->field_1b = r1
    //     0x147c480: stur            w1, [x0, #0x1b]
    // 0x147c484: r1 = Instance_VerticalDirection
    //     0x147c484: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x147c488: ldr             x1, [x1, #0xa20]
    // 0x147c48c: StoreField: r0->field_23 = r1
    //     0x147c48c: stur            w1, [x0, #0x23]
    // 0x147c490: r1 = Instance_Clip
    //     0x147c490: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x147c494: ldr             x1, [x1, #0x38]
    // 0x147c498: StoreField: r0->field_2b = r1
    //     0x147c498: stur            w1, [x0, #0x2b]
    // 0x147c49c: StoreField: r0->field_2f = rZR
    //     0x147c49c: stur            xzr, [x0, #0x2f]
    // 0x147c4a0: ldur            x1, [fp, #-0x18]
    // 0x147c4a4: StoreField: r0->field_b = r1
    //     0x147c4a4: stur            w1, [x0, #0xb]
    // 0x147c4a8: LeaveFrame
    //     0x147c4a8: mov             SP, fp
    //     0x147c4ac: ldp             fp, lr, [SP], #0x10
    // 0x147c4b0: ret
    //     0x147c4b0: ret             
    // 0x147c4b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147c4b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147c4b8: b               #0x147bed4
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d79c0, size: 0x2b4
    // 0x15d79c0: EnterFrame
    //     0x15d79c0: stp             fp, lr, [SP, #-0x10]!
    //     0x15d79c4: mov             fp, SP
    // 0x15d79c8: AllocStack(0x30)
    //     0x15d79c8: sub             SP, SP, #0x30
    // 0x15d79cc: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15d79cc: stur            x1, [fp, #-8]
    //     0x15d79d0: stur            x2, [fp, #-0x10]
    // 0x15d79d4: CheckStackOverflow
    //     0x15d79d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d79d8: cmp             SP, x16
    //     0x15d79dc: b.ls            #0x15d7c6c
    // 0x15d79e0: r1 = 2
    //     0x15d79e0: movz            x1, #0x2
    // 0x15d79e4: r0 = AllocateContext()
    //     0x15d79e4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d79e8: ldur            x1, [fp, #-8]
    // 0x15d79ec: stur            x0, [fp, #-0x18]
    // 0x15d79f0: StoreField: r0->field_f = r1
    //     0x15d79f0: stur            w1, [x0, #0xf]
    // 0x15d79f4: ldur            x2, [fp, #-0x10]
    // 0x15d79f8: StoreField: r0->field_13 = r2
    //     0x15d79f8: stur            w2, [x0, #0x13]
    // 0x15d79fc: r0 = Obx()
    //     0x15d79fc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d7a00: ldur            x2, [fp, #-0x18]
    // 0x15d7a04: r1 = Function '<anonymous closure>':.
    //     0x15d7a04: add             x1, PP, #0x44, lsl #12  ; [pp+0x449c0] AnonymousClosure: (0x15cfe38), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_screen.dart] ExchangeCheckoutScreen::appBar (0x15e9d84)
    //     0x15d7a08: ldr             x1, [x1, #0x9c0]
    // 0x15d7a0c: stur            x0, [fp, #-0x10]
    // 0x15d7a10: r0 = AllocateClosure()
    //     0x15d7a10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d7a14: mov             x1, x0
    // 0x15d7a18: ldur            x0, [fp, #-0x10]
    // 0x15d7a1c: StoreField: r0->field_b = r1
    //     0x15d7a1c: stur            w1, [x0, #0xb]
    // 0x15d7a20: ldur            x1, [fp, #-8]
    // 0x15d7a24: r0 = controller()
    //     0x15d7a24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7a28: LoadField: r1 = r0->field_6b
    //     0x15d7a28: ldur            w1, [x0, #0x6b]
    // 0x15d7a2c: DecompressPointer r1
    //     0x15d7a2c: add             x1, x1, HEAP, lsl #32
    // 0x15d7a30: r0 = value()
    //     0x15d7a30: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7a34: tbnz            w0, #4, #0x15d7acc
    // 0x15d7a38: ldur            x2, [fp, #-0x18]
    // 0x15d7a3c: LoadField: r1 = r2->field_13
    //     0x15d7a3c: ldur            w1, [x2, #0x13]
    // 0x15d7a40: DecompressPointer r1
    //     0x15d7a40: add             x1, x1, HEAP, lsl #32
    // 0x15d7a44: r0 = of()
    //     0x15d7a44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7a48: LoadField: r1 = r0->field_5b
    //     0x15d7a48: ldur            w1, [x0, #0x5b]
    // 0x15d7a4c: DecompressPointer r1
    //     0x15d7a4c: add             x1, x1, HEAP, lsl #32
    // 0x15d7a50: stur            x1, [fp, #-8]
    // 0x15d7a54: r0 = ColorFilter()
    //     0x15d7a54: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d7a58: mov             x1, x0
    // 0x15d7a5c: ldur            x0, [fp, #-8]
    // 0x15d7a60: stur            x1, [fp, #-0x20]
    // 0x15d7a64: StoreField: r1->field_7 = r0
    //     0x15d7a64: stur            w0, [x1, #7]
    // 0x15d7a68: r0 = Instance_BlendMode
    //     0x15d7a68: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d7a6c: ldr             x0, [x0, #0xb30]
    // 0x15d7a70: StoreField: r1->field_b = r0
    //     0x15d7a70: stur            w0, [x1, #0xb]
    // 0x15d7a74: r2 = 1
    //     0x15d7a74: movz            x2, #0x1
    // 0x15d7a78: StoreField: r1->field_13 = r2
    //     0x15d7a78: stur            x2, [x1, #0x13]
    // 0x15d7a7c: r0 = SvgPicture()
    //     0x15d7a7c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d7a80: stur            x0, [fp, #-8]
    // 0x15d7a84: ldur            x16, [fp, #-0x20]
    // 0x15d7a88: str             x16, [SP]
    // 0x15d7a8c: mov             x1, x0
    // 0x15d7a90: r2 = "assets/images/search.svg"
    //     0x15d7a90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15d7a94: ldr             x2, [x2, #0xa30]
    // 0x15d7a98: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d7a98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d7a9c: ldr             x4, [x4, #0xa38]
    // 0x15d7aa0: r0 = SvgPicture.asset()
    //     0x15d7aa0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d7aa4: r0 = Align()
    //     0x15d7aa4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d7aa8: r3 = Instance_Alignment
    //     0x15d7aa8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d7aac: ldr             x3, [x3, #0xb10]
    // 0x15d7ab0: StoreField: r0->field_f = r3
    //     0x15d7ab0: stur            w3, [x0, #0xf]
    // 0x15d7ab4: r4 = 1.000000
    //     0x15d7ab4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d7ab8: StoreField: r0->field_13 = r4
    //     0x15d7ab8: stur            w4, [x0, #0x13]
    // 0x15d7abc: ArrayStore: r0[0] = r4  ; List_4
    //     0x15d7abc: stur            w4, [x0, #0x17]
    // 0x15d7ac0: ldur            x1, [fp, #-8]
    // 0x15d7ac4: StoreField: r0->field_b = r1
    //     0x15d7ac4: stur            w1, [x0, #0xb]
    // 0x15d7ac8: b               #0x15d7b7c
    // 0x15d7acc: ldur            x5, [fp, #-0x18]
    // 0x15d7ad0: r4 = 1.000000
    //     0x15d7ad0: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d7ad4: r0 = Instance_BlendMode
    //     0x15d7ad4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d7ad8: ldr             x0, [x0, #0xb30]
    // 0x15d7adc: r3 = Instance_Alignment
    //     0x15d7adc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d7ae0: ldr             x3, [x3, #0xb10]
    // 0x15d7ae4: r2 = 1
    //     0x15d7ae4: movz            x2, #0x1
    // 0x15d7ae8: LoadField: r1 = r5->field_13
    //     0x15d7ae8: ldur            w1, [x5, #0x13]
    // 0x15d7aec: DecompressPointer r1
    //     0x15d7aec: add             x1, x1, HEAP, lsl #32
    // 0x15d7af0: r0 = of()
    //     0x15d7af0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7af4: LoadField: r1 = r0->field_5b
    //     0x15d7af4: ldur            w1, [x0, #0x5b]
    // 0x15d7af8: DecompressPointer r1
    //     0x15d7af8: add             x1, x1, HEAP, lsl #32
    // 0x15d7afc: stur            x1, [fp, #-8]
    // 0x15d7b00: r0 = ColorFilter()
    //     0x15d7b00: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d7b04: mov             x1, x0
    // 0x15d7b08: ldur            x0, [fp, #-8]
    // 0x15d7b0c: stur            x1, [fp, #-0x20]
    // 0x15d7b10: StoreField: r1->field_7 = r0
    //     0x15d7b10: stur            w0, [x1, #7]
    // 0x15d7b14: r0 = Instance_BlendMode
    //     0x15d7b14: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d7b18: ldr             x0, [x0, #0xb30]
    // 0x15d7b1c: StoreField: r1->field_b = r0
    //     0x15d7b1c: stur            w0, [x1, #0xb]
    // 0x15d7b20: r0 = 1
    //     0x15d7b20: movz            x0, #0x1
    // 0x15d7b24: StoreField: r1->field_13 = r0
    //     0x15d7b24: stur            x0, [x1, #0x13]
    // 0x15d7b28: r0 = SvgPicture()
    //     0x15d7b28: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d7b2c: stur            x0, [fp, #-8]
    // 0x15d7b30: ldur            x16, [fp, #-0x20]
    // 0x15d7b34: str             x16, [SP]
    // 0x15d7b38: mov             x1, x0
    // 0x15d7b3c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d7b3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d7b40: ldr             x2, [x2, #0xa40]
    // 0x15d7b44: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d7b44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d7b48: ldr             x4, [x4, #0xa38]
    // 0x15d7b4c: r0 = SvgPicture.asset()
    //     0x15d7b4c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d7b50: r0 = Align()
    //     0x15d7b50: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d7b54: mov             x1, x0
    // 0x15d7b58: r0 = Instance_Alignment
    //     0x15d7b58: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d7b5c: ldr             x0, [x0, #0xb10]
    // 0x15d7b60: StoreField: r1->field_f = r0
    //     0x15d7b60: stur            w0, [x1, #0xf]
    // 0x15d7b64: r0 = 1.000000
    //     0x15d7b64: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d7b68: StoreField: r1->field_13 = r0
    //     0x15d7b68: stur            w0, [x1, #0x13]
    // 0x15d7b6c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d7b6c: stur            w0, [x1, #0x17]
    // 0x15d7b70: ldur            x0, [fp, #-8]
    // 0x15d7b74: StoreField: r1->field_b = r0
    //     0x15d7b74: stur            w0, [x1, #0xb]
    // 0x15d7b78: mov             x0, x1
    // 0x15d7b7c: stur            x0, [fp, #-8]
    // 0x15d7b80: r0 = InkWell()
    //     0x15d7b80: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d7b84: mov             x3, x0
    // 0x15d7b88: ldur            x0, [fp, #-8]
    // 0x15d7b8c: stur            x3, [fp, #-0x20]
    // 0x15d7b90: StoreField: r3->field_b = r0
    //     0x15d7b90: stur            w0, [x3, #0xb]
    // 0x15d7b94: ldur            x2, [fp, #-0x18]
    // 0x15d7b98: r1 = Function '<anonymous closure>':.
    //     0x15d7b98: add             x1, PP, #0x44, lsl #12  ; [pp+0x449c8] AnonymousClosure: (0x15d8310), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15d7b9c: ldr             x1, [x1, #0x9c8]
    // 0x15d7ba0: r0 = AllocateClosure()
    //     0x15d7ba0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d7ba4: ldur            x2, [fp, #-0x20]
    // 0x15d7ba8: StoreField: r2->field_f = r0
    //     0x15d7ba8: stur            w0, [x2, #0xf]
    // 0x15d7bac: r0 = true
    //     0x15d7bac: add             x0, NULL, #0x20  ; true
    // 0x15d7bb0: StoreField: r2->field_43 = r0
    //     0x15d7bb0: stur            w0, [x2, #0x43]
    // 0x15d7bb4: r1 = Instance_BoxShape
    //     0x15d7bb4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d7bb8: ldr             x1, [x1, #0x80]
    // 0x15d7bbc: StoreField: r2->field_47 = r1
    //     0x15d7bbc: stur            w1, [x2, #0x47]
    // 0x15d7bc0: StoreField: r2->field_6f = r0
    //     0x15d7bc0: stur            w0, [x2, #0x6f]
    // 0x15d7bc4: r1 = false
    //     0x15d7bc4: add             x1, NULL, #0x30  ; false
    // 0x15d7bc8: StoreField: r2->field_73 = r1
    //     0x15d7bc8: stur            w1, [x2, #0x73]
    // 0x15d7bcc: StoreField: r2->field_83 = r0
    //     0x15d7bcc: stur            w0, [x2, #0x83]
    // 0x15d7bd0: StoreField: r2->field_7b = r1
    //     0x15d7bd0: stur            w1, [x2, #0x7b]
    // 0x15d7bd4: r0 = Obx()
    //     0x15d7bd4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d7bd8: ldur            x2, [fp, #-0x18]
    // 0x15d7bdc: r1 = Function '<anonymous closure>':.
    //     0x15d7bdc: add             x1, PP, #0x44, lsl #12  ; [pp+0x449d0] AnonymousClosure: (0x15d7c74), in [package:customer_app/app/presentation/views/basic/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15d79c0)
    //     0x15d7be0: ldr             x1, [x1, #0x9d0]
    // 0x15d7be4: stur            x0, [fp, #-8]
    // 0x15d7be8: r0 = AllocateClosure()
    //     0x15d7be8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d7bec: mov             x1, x0
    // 0x15d7bf0: ldur            x0, [fp, #-8]
    // 0x15d7bf4: StoreField: r0->field_b = r1
    //     0x15d7bf4: stur            w1, [x0, #0xb]
    // 0x15d7bf8: r1 = Null
    //     0x15d7bf8: mov             x1, NULL
    // 0x15d7bfc: r2 = 2
    //     0x15d7bfc: movz            x2, #0x2
    // 0x15d7c00: r0 = AllocateArray()
    //     0x15d7c00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d7c04: mov             x2, x0
    // 0x15d7c08: ldur            x0, [fp, #-8]
    // 0x15d7c0c: stur            x2, [fp, #-0x18]
    // 0x15d7c10: StoreField: r2->field_f = r0
    //     0x15d7c10: stur            w0, [x2, #0xf]
    // 0x15d7c14: r1 = <Widget>
    //     0x15d7c14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15d7c18: r0 = AllocateGrowableArray()
    //     0x15d7c18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15d7c1c: mov             x1, x0
    // 0x15d7c20: ldur            x0, [fp, #-0x18]
    // 0x15d7c24: stur            x1, [fp, #-8]
    // 0x15d7c28: StoreField: r1->field_f = r0
    //     0x15d7c28: stur            w0, [x1, #0xf]
    // 0x15d7c2c: r0 = 2
    //     0x15d7c2c: movz            x0, #0x2
    // 0x15d7c30: StoreField: r1->field_b = r0
    //     0x15d7c30: stur            w0, [x1, #0xb]
    // 0x15d7c34: r0 = AppBar()
    //     0x15d7c34: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d7c38: stur            x0, [fp, #-0x18]
    // 0x15d7c3c: ldur            x16, [fp, #-0x10]
    // 0x15d7c40: ldur            lr, [fp, #-8]
    // 0x15d7c44: stp             lr, x16, [SP]
    // 0x15d7c48: mov             x1, x0
    // 0x15d7c4c: ldur            x2, [fp, #-0x20]
    // 0x15d7c50: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15d7c50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15d7c54: ldr             x4, [x4, #0xa58]
    // 0x15d7c58: r0 = AppBar()
    //     0x15d7c58: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d7c5c: ldur            x0, [fp, #-0x18]
    // 0x15d7c60: LeaveFrame
    //     0x15d7c60: mov             SP, fp
    //     0x15d7c64: ldp             fp, lr, [SP], #0x10
    // 0x15d7c68: ret
    //     0x15d7c68: ret             
    // 0x15d7c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d7c6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d7c70: b               #0x15d79e0
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15d7c74, size: 0x304
    // 0x15d7c74: EnterFrame
    //     0x15d7c74: stp             fp, lr, [SP, #-0x10]!
    //     0x15d7c78: mov             fp, SP
    // 0x15d7c7c: AllocStack(0x58)
    //     0x15d7c7c: sub             SP, SP, #0x58
    // 0x15d7c80: SetupParameters()
    //     0x15d7c80: ldr             x0, [fp, #0x10]
    //     0x15d7c84: ldur            w2, [x0, #0x17]
    //     0x15d7c88: add             x2, x2, HEAP, lsl #32
    //     0x15d7c8c: stur            x2, [fp, #-8]
    // 0x15d7c90: CheckStackOverflow
    //     0x15d7c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d7c94: cmp             SP, x16
    //     0x15d7c98: b.ls            #0x15d7f70
    // 0x15d7c9c: LoadField: r1 = r2->field_f
    //     0x15d7c9c: ldur            w1, [x2, #0xf]
    // 0x15d7ca0: DecompressPointer r1
    //     0x15d7ca0: add             x1, x1, HEAP, lsl #32
    // 0x15d7ca4: r0 = controller()
    //     0x15d7ca4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7ca8: LoadField: r1 = r0->field_5b
    //     0x15d7ca8: ldur            w1, [x0, #0x5b]
    // 0x15d7cac: DecompressPointer r1
    //     0x15d7cac: add             x1, x1, HEAP, lsl #32
    // 0x15d7cb0: r0 = value()
    //     0x15d7cb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7cb4: LoadField: r1 = r0->field_1f
    //     0x15d7cb4: ldur            w1, [x0, #0x1f]
    // 0x15d7cb8: DecompressPointer r1
    //     0x15d7cb8: add             x1, x1, HEAP, lsl #32
    // 0x15d7cbc: cmp             w1, NULL
    // 0x15d7cc0: b.ne            #0x15d7ccc
    // 0x15d7cc4: r0 = Null
    //     0x15d7cc4: mov             x0, NULL
    // 0x15d7cc8: b               #0x15d7cd4
    // 0x15d7ccc: LoadField: r0 = r1->field_7
    //     0x15d7ccc: ldur            w0, [x1, #7]
    // 0x15d7cd0: DecompressPointer r0
    //     0x15d7cd0: add             x0, x0, HEAP, lsl #32
    // 0x15d7cd4: cmp             w0, NULL
    // 0x15d7cd8: b.ne            #0x15d7ce4
    // 0x15d7cdc: r0 = false
    //     0x15d7cdc: add             x0, NULL, #0x30  ; false
    // 0x15d7ce0: b               #0x15d7ed8
    // 0x15d7ce4: tbnz            w0, #4, #0x15d7ed4
    // 0x15d7ce8: ldur            x0, [fp, #-8]
    // 0x15d7cec: LoadField: r1 = r0->field_f
    //     0x15d7cec: ldur            w1, [x0, #0xf]
    // 0x15d7cf0: DecompressPointer r1
    //     0x15d7cf0: add             x1, x1, HEAP, lsl #32
    // 0x15d7cf4: r0 = controller()
    //     0x15d7cf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7cf8: LoadField: r1 = r0->field_6f
    //     0x15d7cf8: ldur            w1, [x0, #0x6f]
    // 0x15d7cfc: DecompressPointer r1
    //     0x15d7cfc: add             x1, x1, HEAP, lsl #32
    // 0x15d7d00: r0 = value()
    //     0x15d7d00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7d04: mov             x2, x0
    // 0x15d7d08: ldur            x0, [fp, #-8]
    // 0x15d7d0c: stur            x2, [fp, #-0x10]
    // 0x15d7d10: LoadField: r1 = r0->field_13
    //     0x15d7d10: ldur            w1, [x0, #0x13]
    // 0x15d7d14: DecompressPointer r1
    //     0x15d7d14: add             x1, x1, HEAP, lsl #32
    // 0x15d7d18: r0 = of()
    //     0x15d7d18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7d1c: LoadField: r2 = r0->field_5b
    //     0x15d7d1c: ldur            w2, [x0, #0x5b]
    // 0x15d7d20: DecompressPointer r2
    //     0x15d7d20: add             x2, x2, HEAP, lsl #32
    // 0x15d7d24: ldur            x0, [fp, #-8]
    // 0x15d7d28: stur            x2, [fp, #-0x18]
    // 0x15d7d2c: LoadField: r1 = r0->field_f
    //     0x15d7d2c: ldur            w1, [x0, #0xf]
    // 0x15d7d30: DecompressPointer r1
    //     0x15d7d30: add             x1, x1, HEAP, lsl #32
    // 0x15d7d34: r0 = controller()
    //     0x15d7d34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7d38: LoadField: r1 = r0->field_73
    //     0x15d7d38: ldur            w1, [x0, #0x73]
    // 0x15d7d3c: DecompressPointer r1
    //     0x15d7d3c: add             x1, x1, HEAP, lsl #32
    // 0x15d7d40: r0 = value()
    //     0x15d7d40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7d44: cmp             w0, NULL
    // 0x15d7d48: r16 = true
    //     0x15d7d48: add             x16, NULL, #0x20  ; true
    // 0x15d7d4c: r17 = false
    //     0x15d7d4c: add             x17, NULL, #0x30  ; false
    // 0x15d7d50: csel            x2, x16, x17, ne
    // 0x15d7d54: ldur            x0, [fp, #-8]
    // 0x15d7d58: stur            x2, [fp, #-0x20]
    // 0x15d7d5c: LoadField: r1 = r0->field_f
    //     0x15d7d5c: ldur            w1, [x0, #0xf]
    // 0x15d7d60: DecompressPointer r1
    //     0x15d7d60: add             x1, x1, HEAP, lsl #32
    // 0x15d7d64: r0 = controller()
    //     0x15d7d64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d7d68: LoadField: r1 = r0->field_73
    //     0x15d7d68: ldur            w1, [x0, #0x73]
    // 0x15d7d6c: DecompressPointer r1
    //     0x15d7d6c: add             x1, x1, HEAP, lsl #32
    // 0x15d7d70: r0 = value()
    //     0x15d7d70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d7d74: str             x0, [SP]
    // 0x15d7d78: r0 = _interpolateSingle()
    //     0x15d7d78: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15d7d7c: mov             x2, x0
    // 0x15d7d80: ldur            x0, [fp, #-8]
    // 0x15d7d84: stur            x2, [fp, #-0x28]
    // 0x15d7d88: LoadField: r1 = r0->field_13
    //     0x15d7d88: ldur            w1, [x0, #0x13]
    // 0x15d7d8c: DecompressPointer r1
    //     0x15d7d8c: add             x1, x1, HEAP, lsl #32
    // 0x15d7d90: r0 = of()
    //     0x15d7d90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7d94: LoadField: r1 = r0->field_87
    //     0x15d7d94: ldur            w1, [x0, #0x87]
    // 0x15d7d98: DecompressPointer r1
    //     0x15d7d98: add             x1, x1, HEAP, lsl #32
    // 0x15d7d9c: LoadField: r0 = r1->field_27
    //     0x15d7d9c: ldur            w0, [x1, #0x27]
    // 0x15d7da0: DecompressPointer r0
    //     0x15d7da0: add             x0, x0, HEAP, lsl #32
    // 0x15d7da4: r16 = Instance_Color
    //     0x15d7da4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15d7da8: str             x16, [SP]
    // 0x15d7dac: mov             x1, x0
    // 0x15d7db0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15d7db0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15d7db4: ldr             x4, [x4, #0xf40]
    // 0x15d7db8: r0 = copyWith()
    //     0x15d7db8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d7dbc: stur            x0, [fp, #-0x30]
    // 0x15d7dc0: r0 = Text()
    //     0x15d7dc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d7dc4: mov             x2, x0
    // 0x15d7dc8: ldur            x0, [fp, #-0x28]
    // 0x15d7dcc: stur            x2, [fp, #-0x38]
    // 0x15d7dd0: StoreField: r2->field_b = r0
    //     0x15d7dd0: stur            w0, [x2, #0xb]
    // 0x15d7dd4: ldur            x0, [fp, #-0x30]
    // 0x15d7dd8: StoreField: r2->field_13 = r0
    //     0x15d7dd8: stur            w0, [x2, #0x13]
    // 0x15d7ddc: ldur            x0, [fp, #-8]
    // 0x15d7de0: LoadField: r1 = r0->field_13
    //     0x15d7de0: ldur            w1, [x0, #0x13]
    // 0x15d7de4: DecompressPointer r1
    //     0x15d7de4: add             x1, x1, HEAP, lsl #32
    // 0x15d7de8: r0 = of()
    //     0x15d7de8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7dec: LoadField: r1 = r0->field_5b
    //     0x15d7dec: ldur            w1, [x0, #0x5b]
    // 0x15d7df0: DecompressPointer r1
    //     0x15d7df0: add             x1, x1, HEAP, lsl #32
    // 0x15d7df4: stur            x1, [fp, #-8]
    // 0x15d7df8: r0 = ColorFilter()
    //     0x15d7df8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d7dfc: mov             x1, x0
    // 0x15d7e00: ldur            x0, [fp, #-8]
    // 0x15d7e04: stur            x1, [fp, #-0x28]
    // 0x15d7e08: StoreField: r1->field_7 = r0
    //     0x15d7e08: stur            w0, [x1, #7]
    // 0x15d7e0c: r0 = Instance_BlendMode
    //     0x15d7e0c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d7e10: ldr             x0, [x0, #0xb30]
    // 0x15d7e14: StoreField: r1->field_b = r0
    //     0x15d7e14: stur            w0, [x1, #0xb]
    // 0x15d7e18: r0 = 1
    //     0x15d7e18: movz            x0, #0x1
    // 0x15d7e1c: StoreField: r1->field_13 = r0
    //     0x15d7e1c: stur            x0, [x1, #0x13]
    // 0x15d7e20: r0 = SvgPicture()
    //     0x15d7e20: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d7e24: stur            x0, [fp, #-8]
    // 0x15d7e28: r16 = Instance_BoxFit
    //     0x15d7e28: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d7e2c: ldr             x16, [x16, #0xb18]
    // 0x15d7e30: r30 = 24.000000
    //     0x15d7e30: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d7e34: ldr             lr, [lr, #0xba8]
    // 0x15d7e38: stp             lr, x16, [SP, #0x10]
    // 0x15d7e3c: r16 = 24.000000
    //     0x15d7e3c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d7e40: ldr             x16, [x16, #0xba8]
    // 0x15d7e44: ldur            lr, [fp, #-0x28]
    // 0x15d7e48: stp             lr, x16, [SP]
    // 0x15d7e4c: mov             x1, x0
    // 0x15d7e50: r2 = "assets/images/shopping_bag.svg"
    //     0x15d7e50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15d7e54: ldr             x2, [x2, #0xa60]
    // 0x15d7e58: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d7e58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d7e5c: ldr             x4, [x4, #0xa68]
    // 0x15d7e60: r0 = SvgPicture.asset()
    //     0x15d7e60: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d7e64: r0 = Badge()
    //     0x15d7e64: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15d7e68: mov             x1, x0
    // 0x15d7e6c: ldur            x0, [fp, #-0x18]
    // 0x15d7e70: stur            x1, [fp, #-0x28]
    // 0x15d7e74: StoreField: r1->field_b = r0
    //     0x15d7e74: stur            w0, [x1, #0xb]
    // 0x15d7e78: ldur            x0, [fp, #-0x38]
    // 0x15d7e7c: StoreField: r1->field_27 = r0
    //     0x15d7e7c: stur            w0, [x1, #0x27]
    // 0x15d7e80: ldur            x0, [fp, #-0x20]
    // 0x15d7e84: StoreField: r1->field_2b = r0
    //     0x15d7e84: stur            w0, [x1, #0x2b]
    // 0x15d7e88: ldur            x0, [fp, #-8]
    // 0x15d7e8c: StoreField: r1->field_2f = r0
    //     0x15d7e8c: stur            w0, [x1, #0x2f]
    // 0x15d7e90: r0 = Visibility()
    //     0x15d7e90: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d7e94: mov             x1, x0
    // 0x15d7e98: ldur            x0, [fp, #-0x28]
    // 0x15d7e9c: StoreField: r1->field_b = r0
    //     0x15d7e9c: stur            w0, [x1, #0xb]
    // 0x15d7ea0: r0 = Instance_SizedBox
    //     0x15d7ea0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d7ea4: StoreField: r1->field_f = r0
    //     0x15d7ea4: stur            w0, [x1, #0xf]
    // 0x15d7ea8: ldur            x0, [fp, #-0x10]
    // 0x15d7eac: StoreField: r1->field_13 = r0
    //     0x15d7eac: stur            w0, [x1, #0x13]
    // 0x15d7eb0: r0 = false
    //     0x15d7eb0: add             x0, NULL, #0x30  ; false
    // 0x15d7eb4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d7eb4: stur            w0, [x1, #0x17]
    // 0x15d7eb8: StoreField: r1->field_1b = r0
    //     0x15d7eb8: stur            w0, [x1, #0x1b]
    // 0x15d7ebc: StoreField: r1->field_1f = r0
    //     0x15d7ebc: stur            w0, [x1, #0x1f]
    // 0x15d7ec0: StoreField: r1->field_23 = r0
    //     0x15d7ec0: stur            w0, [x1, #0x23]
    // 0x15d7ec4: StoreField: r1->field_27 = r0
    //     0x15d7ec4: stur            w0, [x1, #0x27]
    // 0x15d7ec8: StoreField: r1->field_2b = r0
    //     0x15d7ec8: stur            w0, [x1, #0x2b]
    // 0x15d7ecc: mov             x0, x1
    // 0x15d7ed0: b               #0x15d7ef0
    // 0x15d7ed4: r0 = false
    //     0x15d7ed4: add             x0, NULL, #0x30  ; false
    // 0x15d7ed8: r0 = Container()
    //     0x15d7ed8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15d7edc: mov             x1, x0
    // 0x15d7ee0: stur            x0, [fp, #-8]
    // 0x15d7ee4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15d7ee4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15d7ee8: r0 = Container()
    //     0x15d7ee8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15d7eec: ldur            x0, [fp, #-8]
    // 0x15d7ef0: stur            x0, [fp, #-8]
    // 0x15d7ef4: r0 = InkWell()
    //     0x15d7ef4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d7ef8: mov             x3, x0
    // 0x15d7efc: ldur            x0, [fp, #-8]
    // 0x15d7f00: stur            x3, [fp, #-0x10]
    // 0x15d7f04: StoreField: r3->field_b = r0
    //     0x15d7f04: stur            w0, [x3, #0xb]
    // 0x15d7f08: r1 = Function '<anonymous closure>':.
    //     0x15d7f08: add             x1, PP, #0x44, lsl #12  ; [pp+0x449d8] AnonymousClosure: (0x15d7f78), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15d7f0c: ldr             x1, [x1, #0x9d8]
    // 0x15d7f10: r2 = Null
    //     0x15d7f10: mov             x2, NULL
    // 0x15d7f14: r0 = AllocateClosure()
    //     0x15d7f14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d7f18: mov             x1, x0
    // 0x15d7f1c: ldur            x0, [fp, #-0x10]
    // 0x15d7f20: StoreField: r0->field_f = r1
    //     0x15d7f20: stur            w1, [x0, #0xf]
    // 0x15d7f24: r1 = true
    //     0x15d7f24: add             x1, NULL, #0x20  ; true
    // 0x15d7f28: StoreField: r0->field_43 = r1
    //     0x15d7f28: stur            w1, [x0, #0x43]
    // 0x15d7f2c: r2 = Instance_BoxShape
    //     0x15d7f2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d7f30: ldr             x2, [x2, #0x80]
    // 0x15d7f34: StoreField: r0->field_47 = r2
    //     0x15d7f34: stur            w2, [x0, #0x47]
    // 0x15d7f38: StoreField: r0->field_6f = r1
    //     0x15d7f38: stur            w1, [x0, #0x6f]
    // 0x15d7f3c: r2 = false
    //     0x15d7f3c: add             x2, NULL, #0x30  ; false
    // 0x15d7f40: StoreField: r0->field_73 = r2
    //     0x15d7f40: stur            w2, [x0, #0x73]
    // 0x15d7f44: StoreField: r0->field_83 = r1
    //     0x15d7f44: stur            w1, [x0, #0x83]
    // 0x15d7f48: StoreField: r0->field_7b = r2
    //     0x15d7f48: stur            w2, [x0, #0x7b]
    // 0x15d7f4c: r0 = Padding()
    //     0x15d7f4c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15d7f50: r1 = Instance_EdgeInsets
    //     0x15d7f50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15d7f54: ldr             x1, [x1, #0xa78]
    // 0x15d7f58: StoreField: r0->field_f = r1
    //     0x15d7f58: stur            w1, [x0, #0xf]
    // 0x15d7f5c: ldur            x1, [fp, #-0x10]
    // 0x15d7f60: StoreField: r0->field_b = r1
    //     0x15d7f60: stur            w1, [x0, #0xb]
    // 0x15d7f64: LeaveFrame
    //     0x15d7f64: mov             SP, fp
    //     0x15d7f68: ldp             fp, lr, [SP], #0x10
    // 0x15d7f6c: ret
    //     0x15d7f6c: ret             
    // 0x15d7f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d7f70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d7f74: b               #0x15d7c9c
  }
}
