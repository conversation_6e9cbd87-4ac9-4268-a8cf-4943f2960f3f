// lib: , url: package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart

// class id: 1049427, size: 0x8
class :: {
}

// class id: 4556, size: 0x14, field offset: 0x14
//   const constructor, 
class ReturnOrderWithProofView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1362c9c, size: 0x64
    // 0x1362c9c: EnterFrame
    //     0x1362c9c: stp             fp, lr, [SP, #-0x10]!
    //     0x1362ca0: mov             fp, SP
    // 0x1362ca4: AllocStack(0x18)
    //     0x1362ca4: sub             SP, SP, #0x18
    // 0x1362ca8: SetupParameters(ReturnOrderWithProofView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1362ca8: stur            x1, [fp, #-8]
    //     0x1362cac: stur            x2, [fp, #-0x10]
    // 0x1362cb0: r1 = 2
    //     0x1362cb0: movz            x1, #0x2
    // 0x1362cb4: r0 = AllocateContext()
    //     0x1362cb4: bl              #0x16f6108  ; AllocateContextStub
    // 0x1362cb8: mov             x1, x0
    // 0x1362cbc: ldur            x0, [fp, #-8]
    // 0x1362cc0: stur            x1, [fp, #-0x18]
    // 0x1362cc4: StoreField: r1->field_f = r0
    //     0x1362cc4: stur            w0, [x1, #0xf]
    // 0x1362cc8: ldur            x0, [fp, #-0x10]
    // 0x1362ccc: StoreField: r1->field_13 = r0
    //     0x1362ccc: stur            w0, [x1, #0x13]
    // 0x1362cd0: r0 = Obx()
    //     0x1362cd0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1362cd4: ldur            x2, [fp, #-0x18]
    // 0x1362cd8: r1 = Function '<anonymous closure>':.
    //     0x1362cd8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f2e0] AnonymousClosure: (0x1362d00), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::bottomNavigationBar (0x1362c9c)
    //     0x1362cdc: ldr             x1, [x1, #0x2e0]
    // 0x1362ce0: stur            x0, [fp, #-8]
    // 0x1362ce4: r0 = AllocateClosure()
    //     0x1362ce4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1362ce8: mov             x1, x0
    // 0x1362cec: ldur            x0, [fp, #-8]
    // 0x1362cf0: StoreField: r0->field_b = r1
    //     0x1362cf0: stur            w1, [x0, #0xb]
    // 0x1362cf4: LeaveFrame
    //     0x1362cf4: mov             SP, fp
    //     0x1362cf8: ldp             fp, lr, [SP], #0x10
    // 0x1362cfc: ret
    //     0x1362cfc: ret             
  }
  [closure] Container <anonymous closure>(dynamic) {
    // ** addr: 0x1362d00, size: 0xd94
    // 0x1362d00: EnterFrame
    //     0x1362d00: stp             fp, lr, [SP, #-0x10]!
    //     0x1362d04: mov             fp, SP
    // 0x1362d08: AllocStack(0x70)
    //     0x1362d08: sub             SP, SP, #0x70
    // 0x1362d0c: SetupParameters()
    //     0x1362d0c: ldr             x0, [fp, #0x10]
    //     0x1362d10: ldur            w2, [x0, #0x17]
    //     0x1362d14: add             x2, x2, HEAP, lsl #32
    //     0x1362d18: stur            x2, [fp, #-8]
    // 0x1362d1c: CheckStackOverflow
    //     0x1362d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1362d20: cmp             SP, x16
    //     0x1362d24: b.ls            #0x1363a74
    // 0x1362d28: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1362d28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1362d2c: ldr             x0, [x0, #0x1c80]
    //     0x1362d30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1362d34: cmp             w0, w16
    //     0x1362d38: b.ne            #0x1362d44
    //     0x1362d3c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1362d40: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1362d44: r0 = GetNavigation.size()
    //     0x1362d44: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1362d48: LoadField: d0 = r0->field_f
    //     0x1362d48: ldur            d0, [x0, #0xf]
    // 0x1362d4c: d1 = 0.170000
    //     0x1362d4c: add             x17, PP, #0x33, lsl #12  ; [pp+0x33f10] IMM: double(0.17) from 0x3fc5c28f5c28f5c3
    //     0x1362d50: ldr             d1, [x17, #0xf10]
    // 0x1362d54: fmul            d2, d0, d1
    // 0x1362d58: stur            d2, [fp, #-0x50]
    // 0x1362d5c: r1 = _ConstMap len:11
    //     0x1362d5c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x1362d60: ldr             x1, [x1, #0xc28]
    // 0x1362d64: r2 = 8
    //     0x1362d64: movz            x2, #0x8
    // 0x1362d68: r0 = []()
    //     0x1362d68: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1362d6c: stur            x0, [fp, #-0x10]
    // 0x1362d70: r0 = BoxDecoration()
    //     0x1362d70: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1362d74: mov             x2, x0
    // 0x1362d78: r0 = Instance_Color
    //     0x1362d78: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1362d7c: stur            x2, [fp, #-0x18]
    // 0x1362d80: StoreField: r2->field_7 = r0
    //     0x1362d80: stur            w0, [x2, #7]
    // 0x1362d84: ldur            x0, [fp, #-0x10]
    // 0x1362d88: ArrayStore: r2[0] = r0  ; List_4
    //     0x1362d88: stur            w0, [x2, #0x17]
    // 0x1362d8c: r0 = Instance_BoxShape
    //     0x1362d8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1362d90: ldr             x0, [x0, #0x80]
    // 0x1362d94: StoreField: r2->field_23 = r0
    //     0x1362d94: stur            w0, [x2, #0x23]
    // 0x1362d98: ldur            x3, [fp, #-8]
    // 0x1362d9c: LoadField: r1 = r3->field_f
    //     0x1362d9c: ldur            w1, [x3, #0xf]
    // 0x1362da0: DecompressPointer r1
    //     0x1362da0: add             x1, x1, HEAP, lsl #32
    // 0x1362da4: r0 = controller()
    //     0x1362da4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1362da8: LoadField: r1 = r0->field_6b
    //     0x1362da8: ldur            w1, [x0, #0x6b]
    // 0x1362dac: DecompressPointer r1
    //     0x1362dac: add             x1, x1, HEAP, lsl #32
    // 0x1362db0: r0 = value()
    //     0x1362db0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1362db4: cmp             w0, NULL
    // 0x1362db8: b.eq            #0x1362dc0
    // 0x1362dbc: tbz             w0, #4, #0x1362dcc
    // 0x1362dc0: r0 = Instance_IconData
    //     0x1362dc0: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c30] Obj!IconData@d55461
    //     0x1362dc4: ldr             x0, [x0, #0xc30]
    // 0x1362dc8: b               #0x1362dd4
    // 0x1362dcc: r0 = Instance_IconData
    //     0x1362dcc: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!IconData@d55481
    //     0x1362dd0: ldr             x0, [x0, #0xc38]
    // 0x1362dd4: ldur            x2, [fp, #-8]
    // 0x1362dd8: stur            x0, [fp, #-0x10]
    // 0x1362ddc: r0 = Icon()
    //     0x1362ddc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1362de0: mov             x1, x0
    // 0x1362de4: ldur            x0, [fp, #-0x10]
    // 0x1362de8: stur            x1, [fp, #-0x20]
    // 0x1362dec: StoreField: r1->field_b = r0
    //     0x1362dec: stur            w0, [x1, #0xb]
    // 0x1362df0: r0 = GetNavigation.size()
    //     0x1362df0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1362df4: LoadField: d0 = r0->field_7
    //     0x1362df4: ldur            d0, [x0, #7]
    // 0x1362df8: d1 = 0.800000
    //     0x1362df8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x1362dfc: ldr             d1, [x17, #0xb28]
    // 0x1362e00: fmul            d2, d0, d1
    // 0x1362e04: stur            d2, [fp, #-0x58]
    // 0x1362e08: r0 = BoxConstraints()
    //     0x1362e08: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1362e0c: stur            x0, [fp, #-0x10]
    // 0x1362e10: StoreField: r0->field_7 = rZR
    //     0x1362e10: stur            xzr, [x0, #7]
    // 0x1362e14: ldur            d0, [fp, #-0x58]
    // 0x1362e18: StoreField: r0->field_f = d0
    //     0x1362e18: stur            d0, [x0, #0xf]
    // 0x1362e1c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1362e1c: stur            xzr, [x0, #0x17]
    // 0x1362e20: d0 = inf
    //     0x1362e20: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1362e24: StoreField: r0->field_1f = d0
    //     0x1362e24: stur            d0, [x0, #0x1f]
    // 0x1362e28: ldur            x2, [fp, #-8]
    // 0x1362e2c: LoadField: r1 = r2->field_f
    //     0x1362e2c: ldur            w1, [x2, #0xf]
    // 0x1362e30: DecompressPointer r1
    //     0x1362e30: add             x1, x1, HEAP, lsl #32
    // 0x1362e34: r0 = controller()
    //     0x1362e34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1362e38: LoadField: r1 = r0->field_57
    //     0x1362e38: ldur            w1, [x0, #0x57]
    // 0x1362e3c: DecompressPointer r1
    //     0x1362e3c: add             x1, x1, HEAP, lsl #32
    // 0x1362e40: r0 = value()
    //     0x1362e40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1362e44: cmp             w0, NULL
    // 0x1362e48: b.ne            #0x1362e54
    // 0x1362e4c: r0 = Null
    //     0x1362e4c: mov             x0, NULL
    // 0x1362e50: b               #0x1362e60
    // 0x1362e54: LoadField: r1 = r0->field_1f
    //     0x1362e54: ldur            w1, [x0, #0x1f]
    // 0x1362e58: DecompressPointer r1
    //     0x1362e58: add             x1, x1, HEAP, lsl #32
    // 0x1362e5c: mov             x0, x1
    // 0x1362e60: cmp             w0, NULL
    // 0x1362e64: b.ne            #0x1362e6c
    // 0x1362e68: r0 = ""
    //     0x1362e68: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1362e6c: ldur            x2, [fp, #-8]
    // 0x1362e70: stur            x0, [fp, #-0x28]
    // 0x1362e74: LoadField: r1 = r2->field_13
    //     0x1362e74: ldur            w1, [x2, #0x13]
    // 0x1362e78: DecompressPointer r1
    //     0x1362e78: add             x1, x1, HEAP, lsl #32
    // 0x1362e7c: r0 = of()
    //     0x1362e7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1362e80: LoadField: r1 = r0->field_87
    //     0x1362e80: ldur            w1, [x0, #0x87]
    // 0x1362e84: DecompressPointer r1
    //     0x1362e84: add             x1, x1, HEAP, lsl #32
    // 0x1362e88: LoadField: r0 = r1->field_33
    //     0x1362e88: ldur            w0, [x1, #0x33]
    // 0x1362e8c: DecompressPointer r0
    //     0x1362e8c: add             x0, x0, HEAP, lsl #32
    // 0x1362e90: cmp             w0, NULL
    // 0x1362e94: b.ne            #0x1362ea0
    // 0x1362e98: r4 = Null
    //     0x1362e98: mov             x4, NULL
    // 0x1362e9c: b               #0x1362ec0
    // 0x1362ea0: r16 = 10.000000
    //     0x1362ea0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x1362ea4: r30 = Instance_Color
    //     0x1362ea4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1362ea8: stp             lr, x16, [SP]
    // 0x1362eac: mov             x1, x0
    // 0x1362eb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1362eb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1362eb4: ldr             x4, [x4, #0xaa0]
    // 0x1362eb8: r0 = copyWith()
    //     0x1362eb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1362ebc: mov             x4, x0
    // 0x1362ec0: ldur            x2, [fp, #-8]
    // 0x1362ec4: ldur            x3, [fp, #-0x20]
    // 0x1362ec8: ldur            x1, [fp, #-0x10]
    // 0x1362ecc: ldur            x0, [fp, #-0x28]
    // 0x1362ed0: stur            x4, [fp, #-0x30]
    // 0x1362ed4: r0 = Text()
    //     0x1362ed4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1362ed8: mov             x1, x0
    // 0x1362edc: ldur            x0, [fp, #-0x28]
    // 0x1362ee0: stur            x1, [fp, #-0x38]
    // 0x1362ee4: StoreField: r1->field_b = r0
    //     0x1362ee4: stur            w0, [x1, #0xb]
    // 0x1362ee8: ldur            x0, [fp, #-0x30]
    // 0x1362eec: StoreField: r1->field_13 = r0
    //     0x1362eec: stur            w0, [x1, #0x13]
    // 0x1362ef0: r0 = ConstrainedBox()
    //     0x1362ef0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1362ef4: mov             x1, x0
    // 0x1362ef8: ldur            x0, [fp, #-0x10]
    // 0x1362efc: stur            x1, [fp, #-0x28]
    // 0x1362f00: StoreField: r1->field_f = r0
    //     0x1362f00: stur            w0, [x1, #0xf]
    // 0x1362f04: ldur            x0, [fp, #-0x38]
    // 0x1362f08: StoreField: r1->field_b = r0
    //     0x1362f08: stur            w0, [x1, #0xb]
    // 0x1362f0c: r0 = Padding()
    //     0x1362f0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1362f10: mov             x3, x0
    // 0x1362f14: r0 = Instance_EdgeInsets
    //     0x1362f14: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x1362f18: ldr             x0, [x0, #0xc40]
    // 0x1362f1c: stur            x3, [fp, #-0x10]
    // 0x1362f20: StoreField: r3->field_f = r0
    //     0x1362f20: stur            w0, [x3, #0xf]
    // 0x1362f24: ldur            x0, [fp, #-0x28]
    // 0x1362f28: StoreField: r3->field_b = r0
    //     0x1362f28: stur            w0, [x3, #0xb]
    // 0x1362f2c: r1 = Null
    //     0x1362f2c: mov             x1, NULL
    // 0x1362f30: r2 = 4
    //     0x1362f30: movz            x2, #0x4
    // 0x1362f34: r0 = AllocateArray()
    //     0x1362f34: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1362f38: mov             x2, x0
    // 0x1362f3c: ldur            x0, [fp, #-0x20]
    // 0x1362f40: stur            x2, [fp, #-0x28]
    // 0x1362f44: StoreField: r2->field_f = r0
    //     0x1362f44: stur            w0, [x2, #0xf]
    // 0x1362f48: ldur            x0, [fp, #-0x10]
    // 0x1362f4c: StoreField: r2->field_13 = r0
    //     0x1362f4c: stur            w0, [x2, #0x13]
    // 0x1362f50: r1 = <Widget>
    //     0x1362f50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1362f54: r0 = AllocateGrowableArray()
    //     0x1362f54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1362f58: mov             x1, x0
    // 0x1362f5c: ldur            x0, [fp, #-0x28]
    // 0x1362f60: stur            x1, [fp, #-0x10]
    // 0x1362f64: StoreField: r1->field_f = r0
    //     0x1362f64: stur            w0, [x1, #0xf]
    // 0x1362f68: r2 = 4
    //     0x1362f68: movz            x2, #0x4
    // 0x1362f6c: StoreField: r1->field_b = r2
    //     0x1362f6c: stur            w2, [x1, #0xb]
    // 0x1362f70: r0 = Row()
    //     0x1362f70: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1362f74: mov             x1, x0
    // 0x1362f78: r0 = Instance_Axis
    //     0x1362f78: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1362f7c: stur            x1, [fp, #-0x20]
    // 0x1362f80: StoreField: r1->field_f = r0
    //     0x1362f80: stur            w0, [x1, #0xf]
    // 0x1362f84: r2 = Instance_MainAxisAlignment
    //     0x1362f84: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1362f88: ldr             x2, [x2, #0xa08]
    // 0x1362f8c: StoreField: r1->field_13 = r2
    //     0x1362f8c: stur            w2, [x1, #0x13]
    // 0x1362f90: r3 = Instance_MainAxisSize
    //     0x1362f90: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1362f94: ldr             x3, [x3, #0xa10]
    // 0x1362f98: ArrayStore: r1[0] = r3  ; List_4
    //     0x1362f98: stur            w3, [x1, #0x17]
    // 0x1362f9c: r4 = Instance_CrossAxisAlignment
    //     0x1362f9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1362fa0: ldr             x4, [x4, #0xa18]
    // 0x1362fa4: StoreField: r1->field_1b = r4
    //     0x1362fa4: stur            w4, [x1, #0x1b]
    // 0x1362fa8: r5 = Instance_VerticalDirection
    //     0x1362fa8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1362fac: ldr             x5, [x5, #0xa20]
    // 0x1362fb0: StoreField: r1->field_23 = r5
    //     0x1362fb0: stur            w5, [x1, #0x23]
    // 0x1362fb4: r6 = Instance_Clip
    //     0x1362fb4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1362fb8: ldr             x6, [x6, #0x38]
    // 0x1362fbc: StoreField: r1->field_2b = r6
    //     0x1362fbc: stur            w6, [x1, #0x2b]
    // 0x1362fc0: StoreField: r1->field_2f = rZR
    //     0x1362fc0: stur            xzr, [x1, #0x2f]
    // 0x1362fc4: ldur            x7, [fp, #-0x10]
    // 0x1362fc8: StoreField: r1->field_b = r7
    //     0x1362fc8: stur            w7, [x1, #0xb]
    // 0x1362fcc: r0 = Padding()
    //     0x1362fcc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1362fd0: mov             x1, x0
    // 0x1362fd4: r0 = Instance_EdgeInsets
    //     0x1362fd4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x1362fd8: ldr             x0, [x0, #0x868]
    // 0x1362fdc: stur            x1, [fp, #-0x10]
    // 0x1362fe0: StoreField: r1->field_f = r0
    //     0x1362fe0: stur            w0, [x1, #0xf]
    // 0x1362fe4: ldur            x0, [fp, #-0x20]
    // 0x1362fe8: StoreField: r1->field_b = r0
    //     0x1362fe8: stur            w0, [x1, #0xb]
    // 0x1362fec: r0 = InkWell()
    //     0x1362fec: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1362ff0: mov             x3, x0
    // 0x1362ff4: ldur            x0, [fp, #-0x10]
    // 0x1362ff8: stur            x3, [fp, #-0x20]
    // 0x1362ffc: StoreField: r3->field_b = r0
    //     0x1362ffc: stur            w0, [x3, #0xb]
    // 0x1363000: ldur            x2, [fp, #-8]
    // 0x1363004: r1 = Function '<anonymous closure>':.
    //     0x1363004: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f2e8] AnonymousClosure: (0x13304b8), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::bottomNavigationBar (0x136a000)
    //     0x1363008: ldr             x1, [x1, #0x2e8]
    // 0x136300c: r0 = AllocateClosure()
    //     0x136300c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1363010: mov             x1, x0
    // 0x1363014: ldur            x0, [fp, #-0x20]
    // 0x1363018: StoreField: r0->field_f = r1
    //     0x1363018: stur            w1, [x0, #0xf]
    // 0x136301c: r2 = true
    //     0x136301c: add             x2, NULL, #0x20  ; true
    // 0x1363020: StoreField: r0->field_43 = r2
    //     0x1363020: stur            w2, [x0, #0x43]
    // 0x1363024: r1 = Instance_BoxShape
    //     0x1363024: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1363028: ldr             x1, [x1, #0x80]
    // 0x136302c: StoreField: r0->field_47 = r1
    //     0x136302c: stur            w1, [x0, #0x47]
    // 0x1363030: StoreField: r0->field_6f = r2
    //     0x1363030: stur            w2, [x0, #0x6f]
    // 0x1363034: r3 = false
    //     0x1363034: add             x3, NULL, #0x30  ; false
    // 0x1363038: StoreField: r0->field_73 = r3
    //     0x1363038: stur            w3, [x0, #0x73]
    // 0x136303c: StoreField: r0->field_83 = r2
    //     0x136303c: stur            w2, [x0, #0x83]
    // 0x1363040: StoreField: r0->field_7b = r3
    //     0x1363040: stur            w3, [x0, #0x7b]
    // 0x1363044: ldur            x4, [fp, #-8]
    // 0x1363048: LoadField: r1 = r4->field_f
    //     0x1363048: ldur            w1, [x4, #0xf]
    // 0x136304c: DecompressPointer r1
    //     0x136304c: add             x1, x1, HEAP, lsl #32
    // 0x1363050: r0 = controller()
    //     0x1363050: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363054: LoadField: r1 = r0->field_57
    //     0x1363054: ldur            w1, [x0, #0x57]
    // 0x1363058: DecompressPointer r1
    //     0x1363058: add             x1, x1, HEAP, lsl #32
    // 0x136305c: r0 = value()
    //     0x136305c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363060: cmp             w0, NULL
    // 0x1363064: b.ne            #0x1363070
    // 0x1363068: r0 = Null
    //     0x1363068: mov             x0, NULL
    // 0x136306c: b               #0x1363090
    // 0x1363070: LoadField: r1 = r0->field_b
    //     0x1363070: ldur            w1, [x0, #0xb]
    // 0x1363074: DecompressPointer r1
    //     0x1363074: add             x1, x1, HEAP, lsl #32
    // 0x1363078: cmp             w1, NULL
    // 0x136307c: b.ne            #0x1363088
    // 0x1363080: r0 = Null
    //     0x1363080: mov             x0, NULL
    // 0x1363084: b               #0x1363090
    // 0x1363088: LoadField: r0 = r1->field_b
    //     0x1363088: ldur            w0, [x1, #0xb]
    // 0x136308c: DecompressPointer r0
    //     0x136308c: add             x0, x0, HEAP, lsl #32
    // 0x1363090: ldur            x2, [fp, #-8]
    // 0x1363094: cbnz            w0, #0x13630a0
    // 0x1363098: r3 = false
    //     0x1363098: add             x3, NULL, #0x30  ; false
    // 0x136309c: b               #0x13630a4
    // 0x13630a0: r3 = true
    //     0x13630a0: add             x3, NULL, #0x20  ; true
    // 0x13630a4: stur            x3, [fp, #-0x10]
    // 0x13630a8: LoadField: r1 = r2->field_f
    //     0x13630a8: ldur            w1, [x2, #0xf]
    // 0x13630ac: DecompressPointer r1
    //     0x13630ac: add             x1, x1, HEAP, lsl #32
    // 0x13630b0: r0 = controller()
    //     0x13630b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13630b4: LoadField: r1 = r0->field_57
    //     0x13630b4: ldur            w1, [x0, #0x57]
    // 0x13630b8: DecompressPointer r1
    //     0x13630b8: add             x1, x1, HEAP, lsl #32
    // 0x13630bc: r0 = value()
    //     0x13630bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13630c0: cmp             w0, NULL
    // 0x13630c4: b.ne            #0x13630d0
    // 0x13630c8: r0 = Null
    //     0x13630c8: mov             x0, NULL
    // 0x13630cc: b               #0x13630f0
    // 0x13630d0: LoadField: r1 = r0->field_33
    //     0x13630d0: ldur            w1, [x0, #0x33]
    // 0x13630d4: DecompressPointer r1
    //     0x13630d4: add             x1, x1, HEAP, lsl #32
    // 0x13630d8: cmp             w1, NULL
    // 0x13630dc: b.ne            #0x13630e8
    // 0x13630e0: r0 = Null
    //     0x13630e0: mov             x0, NULL
    // 0x13630e4: b               #0x13630f0
    // 0x13630e8: LoadField: r0 = r1->field_2b
    //     0x13630e8: ldur            w0, [x1, #0x2b]
    // 0x13630ec: DecompressPointer r0
    //     0x13630ec: add             x0, x0, HEAP, lsl #32
    // 0x13630f0: cmp             w0, NULL
    // 0x13630f4: b.ne            #0x13630fc
    // 0x13630f8: r0 = ""
    //     0x13630f8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13630fc: ldur            x2, [fp, #-8]
    // 0x1363100: stur            x0, [fp, #-0x28]
    // 0x1363104: LoadField: r1 = r2->field_13
    //     0x1363104: ldur            w1, [x2, #0x13]
    // 0x1363108: DecompressPointer r1
    //     0x1363108: add             x1, x1, HEAP, lsl #32
    // 0x136310c: r0 = of()
    //     0x136310c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1363110: LoadField: r1 = r0->field_87
    //     0x1363110: ldur            w1, [x0, #0x87]
    // 0x1363114: DecompressPointer r1
    //     0x1363114: add             x1, x1, HEAP, lsl #32
    // 0x1363118: LoadField: r0 = r1->field_7
    //     0x1363118: ldur            w0, [x1, #7]
    // 0x136311c: DecompressPointer r0
    //     0x136311c: add             x0, x0, HEAP, lsl #32
    // 0x1363120: stur            x0, [fp, #-0x30]
    // 0x1363124: r1 = Instance_Color
    //     0x1363124: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1363128: d0 = 0.400000
    //     0x1363128: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x136312c: r0 = withOpacity()
    //     0x136312c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1363130: r16 = 14.000000
    //     0x1363130: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1363134: ldr             x16, [x16, #0x1d8]
    // 0x1363138: stp             x0, x16, [SP]
    // 0x136313c: ldur            x1, [fp, #-0x30]
    // 0x1363140: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1363140: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1363144: ldr             x4, [x4, #0xaa0]
    // 0x1363148: r0 = copyWith()
    //     0x1363148: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x136314c: stur            x0, [fp, #-0x30]
    // 0x1363150: r0 = Text()
    //     0x1363150: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1363154: mov             x1, x0
    // 0x1363158: ldur            x0, [fp, #-0x28]
    // 0x136315c: stur            x1, [fp, #-0x38]
    // 0x1363160: StoreField: r1->field_b = r0
    //     0x1363160: stur            w0, [x1, #0xb]
    // 0x1363164: ldur            x0, [fp, #-0x30]
    // 0x1363168: StoreField: r1->field_13 = r0
    //     0x1363168: stur            w0, [x1, #0x13]
    // 0x136316c: r0 = Padding()
    //     0x136316c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1363170: mov             x2, x0
    // 0x1363174: r0 = Instance_EdgeInsets
    //     0x1363174: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c50] Obj!EdgeInsets@d59541
    //     0x1363178: ldr             x0, [x0, #0xc50]
    // 0x136317c: stur            x2, [fp, #-0x28]
    // 0x1363180: StoreField: r2->field_f = r0
    //     0x1363180: stur            w0, [x2, #0xf]
    // 0x1363184: ldur            x0, [fp, #-0x38]
    // 0x1363188: StoreField: r2->field_b = r0
    //     0x1363188: stur            w0, [x2, #0xb]
    // 0x136318c: ldur            x0, [fp, #-8]
    // 0x1363190: LoadField: r1 = r0->field_f
    //     0x1363190: ldur            w1, [x0, #0xf]
    // 0x1363194: DecompressPointer r1
    //     0x1363194: add             x1, x1, HEAP, lsl #32
    // 0x1363198: r0 = controller()
    //     0x1363198: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136319c: LoadField: r1 = r0->field_57
    //     0x136319c: ldur            w1, [x0, #0x57]
    // 0x13631a0: DecompressPointer r1
    //     0x13631a0: add             x1, x1, HEAP, lsl #32
    // 0x13631a4: r0 = value()
    //     0x13631a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13631a8: cmp             w0, NULL
    // 0x13631ac: b.ne            #0x13631b8
    // 0x13631b0: r0 = Null
    //     0x13631b0: mov             x0, NULL
    // 0x13631b4: b               #0x13631d8
    // 0x13631b8: LoadField: r1 = r0->field_b
    //     0x13631b8: ldur            w1, [x0, #0xb]
    // 0x13631bc: DecompressPointer r1
    //     0x13631bc: add             x1, x1, HEAP, lsl #32
    // 0x13631c0: cmp             w1, NULL
    // 0x13631c4: b.ne            #0x13631d0
    // 0x13631c8: r0 = Null
    //     0x13631c8: mov             x0, NULL
    // 0x13631cc: b               #0x13631d8
    // 0x13631d0: LoadField: r0 = r1->field_7
    //     0x13631d0: ldur            w0, [x1, #7]
    // 0x13631d4: DecompressPointer r0
    //     0x13631d4: add             x0, x0, HEAP, lsl #32
    // 0x13631d8: cmp             w0, NULL
    // 0x13631dc: b.ne            #0x13631e8
    // 0x13631e0: r4 = ""
    //     0x13631e0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13631e4: b               #0x13631ec
    // 0x13631e8: mov             x4, x0
    // 0x13631ec: ldur            x2, [fp, #-8]
    // 0x13631f0: ldur            x0, [fp, #-0x28]
    // 0x13631f4: ldur            x3, [fp, #-0x10]
    // 0x13631f8: stur            x4, [fp, #-0x30]
    // 0x13631fc: LoadField: r1 = r2->field_13
    //     0x13631fc: ldur            w1, [x2, #0x13]
    // 0x1363200: DecompressPointer r1
    //     0x1363200: add             x1, x1, HEAP, lsl #32
    // 0x1363204: r0 = of()
    //     0x1363204: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1363208: LoadField: r1 = r0->field_87
    //     0x1363208: ldur            w1, [x0, #0x87]
    // 0x136320c: DecompressPointer r1
    //     0x136320c: add             x1, x1, HEAP, lsl #32
    // 0x1363210: LoadField: r0 = r1->field_7
    //     0x1363210: ldur            w0, [x1, #7]
    // 0x1363214: DecompressPointer r0
    //     0x1363214: add             x0, x0, HEAP, lsl #32
    // 0x1363218: r16 = 16.000000
    //     0x1363218: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x136321c: ldr             x16, [x16, #0x188]
    // 0x1363220: r30 = Instance_Color
    //     0x1363220: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1363224: stp             lr, x16, [SP]
    // 0x1363228: mov             x1, x0
    // 0x136322c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x136322c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1363230: ldr             x4, [x4, #0xaa0]
    // 0x1363234: r0 = copyWith()
    //     0x1363234: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1363238: stur            x0, [fp, #-0x38]
    // 0x136323c: r0 = Text()
    //     0x136323c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1363240: mov             x3, x0
    // 0x1363244: ldur            x0, [fp, #-0x30]
    // 0x1363248: stur            x3, [fp, #-0x40]
    // 0x136324c: StoreField: r3->field_b = r0
    //     0x136324c: stur            w0, [x3, #0xb]
    // 0x1363250: ldur            x0, [fp, #-0x38]
    // 0x1363254: StoreField: r3->field_13 = r0
    //     0x1363254: stur            w0, [x3, #0x13]
    // 0x1363258: r1 = Null
    //     0x1363258: mov             x1, NULL
    // 0x136325c: r2 = 4
    //     0x136325c: movz            x2, #0x4
    // 0x1363260: r0 = AllocateArray()
    //     0x1363260: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1363264: mov             x2, x0
    // 0x1363268: ldur            x0, [fp, #-0x28]
    // 0x136326c: stur            x2, [fp, #-0x30]
    // 0x1363270: StoreField: r2->field_f = r0
    //     0x1363270: stur            w0, [x2, #0xf]
    // 0x1363274: ldur            x0, [fp, #-0x40]
    // 0x1363278: StoreField: r2->field_13 = r0
    //     0x1363278: stur            w0, [x2, #0x13]
    // 0x136327c: r1 = <Widget>
    //     0x136327c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1363280: r0 = AllocateGrowableArray()
    //     0x1363280: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1363284: mov             x1, x0
    // 0x1363288: ldur            x0, [fp, #-0x30]
    // 0x136328c: stur            x1, [fp, #-0x28]
    // 0x1363290: StoreField: r1->field_f = r0
    //     0x1363290: stur            w0, [x1, #0xf]
    // 0x1363294: r2 = 4
    //     0x1363294: movz            x2, #0x4
    // 0x1363298: StoreField: r1->field_b = r2
    //     0x1363298: stur            w2, [x1, #0xb]
    // 0x136329c: r0 = Column()
    //     0x136329c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13632a0: mov             x1, x0
    // 0x13632a4: r0 = Instance_Axis
    //     0x13632a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13632a8: stur            x1, [fp, #-0x30]
    // 0x13632ac: StoreField: r1->field_f = r0
    //     0x13632ac: stur            w0, [x1, #0xf]
    // 0x13632b0: r2 = Instance_MainAxisAlignment
    //     0x13632b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13632b4: ldr             x2, [x2, #0xa08]
    // 0x13632b8: StoreField: r1->field_13 = r2
    //     0x13632b8: stur            w2, [x1, #0x13]
    // 0x13632bc: r3 = Instance_MainAxisSize
    //     0x13632bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13632c0: ldr             x3, [x3, #0xa10]
    // 0x13632c4: ArrayStore: r1[0] = r3  ; List_4
    //     0x13632c4: stur            w3, [x1, #0x17]
    // 0x13632c8: r4 = Instance_CrossAxisAlignment
    //     0x13632c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13632cc: ldr             x4, [x4, #0x890]
    // 0x13632d0: StoreField: r1->field_1b = r4
    //     0x13632d0: stur            w4, [x1, #0x1b]
    // 0x13632d4: r4 = Instance_VerticalDirection
    //     0x13632d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13632d8: ldr             x4, [x4, #0xa20]
    // 0x13632dc: StoreField: r1->field_23 = r4
    //     0x13632dc: stur            w4, [x1, #0x23]
    // 0x13632e0: r5 = Instance_Clip
    //     0x13632e0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13632e4: ldr             x5, [x5, #0x38]
    // 0x13632e8: StoreField: r1->field_2b = r5
    //     0x13632e8: stur            w5, [x1, #0x2b]
    // 0x13632ec: StoreField: r1->field_2f = rZR
    //     0x13632ec: stur            xzr, [x1, #0x2f]
    // 0x13632f0: ldur            x6, [fp, #-0x28]
    // 0x13632f4: StoreField: r1->field_b = r6
    //     0x13632f4: stur            w6, [x1, #0xb]
    // 0x13632f8: r0 = Visibility()
    //     0x13632f8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13632fc: mov             x2, x0
    // 0x1363300: ldur            x0, [fp, #-0x30]
    // 0x1363304: stur            x2, [fp, #-0x28]
    // 0x1363308: StoreField: r2->field_b = r0
    //     0x1363308: stur            w0, [x2, #0xb]
    // 0x136330c: r0 = Instance_SizedBox
    //     0x136330c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1363310: StoreField: r2->field_f = r0
    //     0x1363310: stur            w0, [x2, #0xf]
    // 0x1363314: ldur            x0, [fp, #-0x10]
    // 0x1363318: StoreField: r2->field_13 = r0
    //     0x1363318: stur            w0, [x2, #0x13]
    // 0x136331c: r0 = false
    //     0x136331c: add             x0, NULL, #0x30  ; false
    // 0x1363320: ArrayStore: r2[0] = r0  ; List_4
    //     0x1363320: stur            w0, [x2, #0x17]
    // 0x1363324: StoreField: r2->field_1b = r0
    //     0x1363324: stur            w0, [x2, #0x1b]
    // 0x1363328: StoreField: r2->field_1f = r0
    //     0x1363328: stur            w0, [x2, #0x1f]
    // 0x136332c: StoreField: r2->field_23 = r0
    //     0x136332c: stur            w0, [x2, #0x23]
    // 0x1363330: StoreField: r2->field_27 = r0
    //     0x1363330: stur            w0, [x2, #0x27]
    // 0x1363334: StoreField: r2->field_2b = r0
    //     0x1363334: stur            w0, [x2, #0x2b]
    // 0x1363338: ldur            x3, [fp, #-8]
    // 0x136333c: LoadField: r1 = r3->field_f
    //     0x136333c: ldur            w1, [x3, #0xf]
    // 0x1363340: DecompressPointer r1
    //     0x1363340: add             x1, x1, HEAP, lsl #32
    // 0x1363344: r0 = controller()
    //     0x1363344: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363348: LoadField: r1 = r0->field_57
    //     0x1363348: ldur            w1, [x0, #0x57]
    // 0x136334c: DecompressPointer r1
    //     0x136334c: add             x1, x1, HEAP, lsl #32
    // 0x1363350: r0 = value()
    //     0x1363350: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363354: cmp             w0, NULL
    // 0x1363358: b.eq            #0x1363378
    // 0x136335c: LoadField: r1 = r0->field_b
    //     0x136335c: ldur            w1, [x0, #0xb]
    // 0x1363360: DecompressPointer r1
    //     0x1363360: add             x1, x1, HEAP, lsl #32
    // 0x1363364: cmp             w1, NULL
    // 0x1363368: b.eq            #0x1363378
    // 0x136336c: LoadField: r0 = r1->field_b
    //     0x136336c: ldur            w0, [x1, #0xb]
    // 0x1363370: DecompressPointer r0
    //     0x1363370: add             x0, x0, HEAP, lsl #32
    // 0x1363374: cbz             w0, #0x1363380
    // 0x1363378: r0 = 0
    //     0x1363378: movz            x0, #0
    // 0x136337c: b               #0x1363384
    // 0x1363380: r0 = 1
    //     0x1363380: movz            x0, #0x1
    // 0x1363384: ldur            x2, [fp, #-8]
    // 0x1363388: stur            x0, [fp, #-0x48]
    // 0x136338c: r16 = <EdgeInsets>
    //     0x136338c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1363390: ldr             x16, [x16, #0xda0]
    // 0x1363394: r30 = Instance_EdgeInsets
    //     0x1363394: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0x1363398: ldr             lr, [lr, #0x28]
    // 0x136339c: stp             lr, x16, [SP]
    // 0x13633a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13633a0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13633a4: r0 = all()
    //     0x13633a4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13633a8: ldur            x2, [fp, #-8]
    // 0x13633ac: stur            x0, [fp, #-0x10]
    // 0x13633b0: LoadField: r1 = r2->field_f
    //     0x13633b0: ldur            w1, [x2, #0xf]
    // 0x13633b4: DecompressPointer r1
    //     0x13633b4: add             x1, x1, HEAP, lsl #32
    // 0x13633b8: r0 = controller()
    //     0x13633b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13633bc: LoadField: r1 = r0->field_57
    //     0x13633bc: ldur            w1, [x0, #0x57]
    // 0x13633c0: DecompressPointer r1
    //     0x13633c0: add             x1, x1, HEAP, lsl #32
    // 0x13633c4: r0 = value()
    //     0x13633c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13633c8: cmp             w0, NULL
    // 0x13633cc: b.eq            #0x1363520
    // 0x13633d0: LoadField: r1 = r0->field_2b
    //     0x13633d0: ldur            w1, [x0, #0x2b]
    // 0x13633d4: DecompressPointer r1
    //     0x13633d4: add             x1, x1, HEAP, lsl #32
    // 0x13633d8: LoadField: r0 = r1->field_b
    //     0x13633d8: ldur            w0, [x1, #0xb]
    // 0x13633dc: cmp             w0, #4
    // 0x13633e0: b.ne            #0x1363520
    // 0x13633e4: ldur            x2, [fp, #-8]
    // 0x13633e8: LoadField: r1 = r2->field_f
    //     0x13633e8: ldur            w1, [x2, #0xf]
    // 0x13633ec: DecompressPointer r1
    //     0x13633ec: add             x1, x1, HEAP, lsl #32
    // 0x13633f0: r0 = controller()
    //     0x13633f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13633f4: LoadField: r1 = r0->field_6b
    //     0x13633f4: ldur            w1, [x0, #0x6b]
    // 0x13633f8: DecompressPointer r1
    //     0x13633f8: add             x1, x1, HEAP, lsl #32
    // 0x13633fc: r0 = value()
    //     0x13633fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363400: cmp             w0, NULL
    // 0x1363404: b.eq            #0x136350c
    // 0x1363408: tbnz            w0, #4, #0x136350c
    // 0x136340c: ldur            x2, [fp, #-8]
    // 0x1363410: LoadField: r1 = r2->field_f
    //     0x1363410: ldur            w1, [x2, #0xf]
    // 0x1363414: DecompressPointer r1
    //     0x1363414: add             x1, x1, HEAP, lsl #32
    // 0x1363418: r0 = controller()
    //     0x1363418: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136341c: LoadField: r1 = r0->field_6f
    //     0x136341c: ldur            w1, [x0, #0x6f]
    // 0x1363420: DecompressPointer r1
    //     0x1363420: add             x1, x1, HEAP, lsl #32
    // 0x1363424: r0 = value()
    //     0x1363424: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1363428: r1 = LoadClassIdInstr(r0)
    //     0x1363428: ldur            x1, [x0, #-1]
    //     0x136342c: ubfx            x1, x1, #0xc, #0x14
    // 0x1363430: str             x0, [SP]
    // 0x1363434: mov             x0, x1
    // 0x1363438: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1363438: movz            x17, #0xc898
    //     0x136343c: add             lr, x0, x17
    //     0x1363440: ldr             lr, [x21, lr, lsl #3]
    //     0x1363444: blr             lr
    // 0x1363448: cbz             w0, #0x136350c
    // 0x136344c: ldur            x2, [fp, #-8]
    // 0x1363450: LoadField: r1 = r2->field_f
    //     0x1363450: ldur            w1, [x2, #0xf]
    // 0x1363454: DecompressPointer r1
    //     0x1363454: add             x1, x1, HEAP, lsl #32
    // 0x1363458: r0 = controller()
    //     0x1363458: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136345c: LoadField: r1 = r0->field_9b
    //     0x136345c: ldur            w1, [x0, #0x9b]
    // 0x1363460: DecompressPointer r1
    //     0x1363460: add             x1, x1, HEAP, lsl #32
    // 0x1363464: r0 = value()
    //     0x1363464: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363468: tbnz            w0, #4, #0x136350c
    // 0x136346c: ldur            x2, [fp, #-8]
    // 0x1363470: LoadField: r1 = r2->field_f
    //     0x1363470: ldur            w1, [x2, #0xf]
    // 0x1363474: DecompressPointer r1
    //     0x1363474: add             x1, x1, HEAP, lsl #32
    // 0x1363478: r0 = controller()
    //     0x1363478: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136347c: LoadField: r1 = r0->field_77
    //     0x136347c: ldur            w1, [x0, #0x77]
    // 0x1363480: DecompressPointer r1
    //     0x1363480: add             x1, x1, HEAP, lsl #32
    // 0x1363484: r0 = value()
    //     0x1363484: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1363488: r1 = LoadClassIdInstr(r0)
    //     0x1363488: ldur            x1, [x0, #-1]
    //     0x136348c: ubfx            x1, x1, #0xc, #0x14
    // 0x1363490: str             x0, [SP]
    // 0x1363494: mov             x0, x1
    // 0x1363498: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1363498: movz            x17, #0xc898
    //     0x136349c: add             lr, x0, x17
    //     0x13634a0: ldr             lr, [x21, lr, lsl #3]
    //     0x13634a4: blr             lr
    // 0x13634a8: cbz             w0, #0x136350c
    // 0x13634ac: ldur            x2, [fp, #-8]
    // 0x13634b0: LoadField: r1 = r2->field_f
    //     0x13634b0: ldur            w1, [x2, #0xf]
    // 0x13634b4: DecompressPointer r1
    //     0x13634b4: add             x1, x1, HEAP, lsl #32
    // 0x13634b8: r0 = controller()
    //     0x13634b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13634bc: LoadField: r1 = r0->field_97
    //     0x13634bc: ldur            w1, [x0, #0x97]
    // 0x13634c0: DecompressPointer r1
    //     0x13634c0: add             x1, x1, HEAP, lsl #32
    // 0x13634c4: r0 = value()
    //     0x13634c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13634c8: tbnz            w0, #4, #0x136350c
    // 0x13634cc: ldur            x2, [fp, #-8]
    // 0x13634d0: LoadField: r1 = r2->field_f
    //     0x13634d0: ldur            w1, [x2, #0xf]
    // 0x13634d4: DecompressPointer r1
    //     0x13634d4: add             x1, x1, HEAP, lsl #32
    // 0x13634d8: r0 = controller()
    //     0x13634d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13634dc: LoadField: r1 = r0->field_93
    //     0x13634dc: ldur            w1, [x0, #0x93]
    // 0x13634e0: DecompressPointer r1
    //     0x13634e0: add             x1, x1, HEAP, lsl #32
    // 0x13634e4: r0 = value()
    //     0x13634e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13634e8: tbnz            w0, #4, #0x136350c
    // 0x13634ec: ldur            x2, [fp, #-8]
    // 0x13634f0: LoadField: r1 = r2->field_13
    //     0x13634f0: ldur            w1, [x2, #0x13]
    // 0x13634f4: DecompressPointer r1
    //     0x13634f4: add             x1, x1, HEAP, lsl #32
    // 0x13634f8: r0 = of()
    //     0x13634f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13634fc: LoadField: r1 = r0->field_5b
    //     0x13634fc: ldur            w1, [x0, #0x5b]
    // 0x1363500: DecompressPointer r1
    //     0x1363500: add             x1, x1, HEAP, lsl #32
    // 0x1363504: mov             x0, x1
    // 0x1363508: b               #0x1363518
    // 0x136350c: r1 = Instance_Color
    //     0x136350c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1363510: d0 = 0.400000
    //     0x1363510: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1363514: r0 = withOpacity()
    //     0x1363514: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1363518: mov             x1, x0
    // 0x136351c: b               #0x1363658
    // 0x1363520: ldur            x2, [fp, #-8]
    // 0x1363524: LoadField: r1 = r2->field_f
    //     0x1363524: ldur            w1, [x2, #0xf]
    // 0x1363528: DecompressPointer r1
    //     0x1363528: add             x1, x1, HEAP, lsl #32
    // 0x136352c: r0 = controller()
    //     0x136352c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363530: LoadField: r1 = r0->field_6b
    //     0x1363530: ldur            w1, [x0, #0x6b]
    // 0x1363534: DecompressPointer r1
    //     0x1363534: add             x1, x1, HEAP, lsl #32
    // 0x1363538: r0 = value()
    //     0x1363538: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x136353c: cmp             w0, NULL
    // 0x1363540: b.eq            #0x1363648
    // 0x1363544: tbnz            w0, #4, #0x1363648
    // 0x1363548: ldur            x2, [fp, #-8]
    // 0x136354c: LoadField: r1 = r2->field_f
    //     0x136354c: ldur            w1, [x2, #0xf]
    // 0x1363550: DecompressPointer r1
    //     0x1363550: add             x1, x1, HEAP, lsl #32
    // 0x1363554: r0 = controller()
    //     0x1363554: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363558: LoadField: r1 = r0->field_6f
    //     0x1363558: ldur            w1, [x0, #0x6f]
    // 0x136355c: DecompressPointer r1
    //     0x136355c: add             x1, x1, HEAP, lsl #32
    // 0x1363560: r0 = value()
    //     0x1363560: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1363564: r1 = LoadClassIdInstr(r0)
    //     0x1363564: ldur            x1, [x0, #-1]
    //     0x1363568: ubfx            x1, x1, #0xc, #0x14
    // 0x136356c: str             x0, [SP]
    // 0x1363570: mov             x0, x1
    // 0x1363574: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1363574: movz            x17, #0xc898
    //     0x1363578: add             lr, x0, x17
    //     0x136357c: ldr             lr, [x21, lr, lsl #3]
    //     0x1363580: blr             lr
    // 0x1363584: cbz             w0, #0x13635a8
    // 0x1363588: ldur            x2, [fp, #-8]
    // 0x136358c: LoadField: r1 = r2->field_f
    //     0x136358c: ldur            w1, [x2, #0xf]
    // 0x1363590: DecompressPointer r1
    //     0x1363590: add             x1, x1, HEAP, lsl #32
    // 0x1363594: r0 = controller()
    //     0x1363594: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363598: LoadField: r1 = r0->field_9b
    //     0x1363598: ldur            w1, [x0, #0x9b]
    // 0x136359c: DecompressPointer r1
    //     0x136359c: add             x1, x1, HEAP, lsl #32
    // 0x13635a0: r0 = value()
    //     0x13635a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13635a4: tbz             w0, #4, #0x1363608
    // 0x13635a8: ldur            x2, [fp, #-8]
    // 0x13635ac: LoadField: r1 = r2->field_f
    //     0x13635ac: ldur            w1, [x2, #0xf]
    // 0x13635b0: DecompressPointer r1
    //     0x13635b0: add             x1, x1, HEAP, lsl #32
    // 0x13635b4: r0 = controller()
    //     0x13635b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13635b8: LoadField: r1 = r0->field_77
    //     0x13635b8: ldur            w1, [x0, #0x77]
    // 0x13635bc: DecompressPointer r1
    //     0x13635bc: add             x1, x1, HEAP, lsl #32
    // 0x13635c0: r0 = value()
    //     0x13635c0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x13635c4: r1 = LoadClassIdInstr(r0)
    //     0x13635c4: ldur            x1, [x0, #-1]
    //     0x13635c8: ubfx            x1, x1, #0xc, #0x14
    // 0x13635cc: str             x0, [SP]
    // 0x13635d0: mov             x0, x1
    // 0x13635d4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x13635d4: movz            x17, #0xc898
    //     0x13635d8: add             lr, x0, x17
    //     0x13635dc: ldr             lr, [x21, lr, lsl #3]
    //     0x13635e0: blr             lr
    // 0x13635e4: cbz             w0, #0x1363648
    // 0x13635e8: ldur            x2, [fp, #-8]
    // 0x13635ec: LoadField: r1 = r2->field_f
    //     0x13635ec: ldur            w1, [x2, #0xf]
    // 0x13635f0: DecompressPointer r1
    //     0x13635f0: add             x1, x1, HEAP, lsl #32
    // 0x13635f4: r0 = controller()
    //     0x13635f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13635f8: LoadField: r1 = r0->field_97
    //     0x13635f8: ldur            w1, [x0, #0x97]
    // 0x13635fc: DecompressPointer r1
    //     0x13635fc: add             x1, x1, HEAP, lsl #32
    // 0x1363600: r0 = value()
    //     0x1363600: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363604: tbnz            w0, #4, #0x1363648
    // 0x1363608: ldur            x2, [fp, #-8]
    // 0x136360c: LoadField: r1 = r2->field_f
    //     0x136360c: ldur            w1, [x2, #0xf]
    // 0x1363610: DecompressPointer r1
    //     0x1363610: add             x1, x1, HEAP, lsl #32
    // 0x1363614: r0 = controller()
    //     0x1363614: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363618: LoadField: r1 = r0->field_93
    //     0x1363618: ldur            w1, [x0, #0x93]
    // 0x136361c: DecompressPointer r1
    //     0x136361c: add             x1, x1, HEAP, lsl #32
    // 0x1363620: r0 = value()
    //     0x1363620: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363624: tbnz            w0, #4, #0x1363648
    // 0x1363628: ldur            x2, [fp, #-8]
    // 0x136362c: LoadField: r1 = r2->field_13
    //     0x136362c: ldur            w1, [x2, #0x13]
    // 0x1363630: DecompressPointer r1
    //     0x1363630: add             x1, x1, HEAP, lsl #32
    // 0x1363634: r0 = of()
    //     0x1363634: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1363638: LoadField: r1 = r0->field_5b
    //     0x1363638: ldur            w1, [x0, #0x5b]
    // 0x136363c: DecompressPointer r1
    //     0x136363c: add             x1, x1, HEAP, lsl #32
    // 0x1363640: mov             x0, x1
    // 0x1363644: b               #0x1363654
    // 0x1363648: r1 = Instance_Color
    //     0x1363648: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x136364c: d0 = 0.400000
    //     0x136364c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1363650: r0 = withOpacity()
    //     0x1363650: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1363654: mov             x1, x0
    // 0x1363658: ldur            x2, [fp, #-8]
    // 0x136365c: ldur            x0, [fp, #-0x10]
    // 0x1363660: r16 = <Color>
    //     0x1363660: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1363664: ldr             x16, [x16, #0xf80]
    // 0x1363668: stp             x1, x16, [SP]
    // 0x136366c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x136366c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1363670: r0 = all()
    //     0x1363670: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1363674: stur            x0, [fp, #-0x30]
    // 0x1363678: r0 = Radius()
    //     0x1363678: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x136367c: d0 = 30.000000
    //     0x136367c: fmov            d0, #30.00000000
    // 0x1363680: stur            x0, [fp, #-0x38]
    // 0x1363684: StoreField: r0->field_7 = d0
    //     0x1363684: stur            d0, [x0, #7]
    // 0x1363688: StoreField: r0->field_f = d0
    //     0x1363688: stur            d0, [x0, #0xf]
    // 0x136368c: r0 = BorderRadius()
    //     0x136368c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1363690: mov             x1, x0
    // 0x1363694: ldur            x0, [fp, #-0x38]
    // 0x1363698: stur            x1, [fp, #-0x40]
    // 0x136369c: StoreField: r1->field_7 = r0
    //     0x136369c: stur            w0, [x1, #7]
    // 0x13636a0: StoreField: r1->field_b = r0
    //     0x13636a0: stur            w0, [x1, #0xb]
    // 0x13636a4: StoreField: r1->field_f = r0
    //     0x13636a4: stur            w0, [x1, #0xf]
    // 0x13636a8: StoreField: r1->field_13 = r0
    //     0x13636a8: stur            w0, [x1, #0x13]
    // 0x13636ac: r0 = RoundedRectangleBorder()
    //     0x13636ac: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x13636b0: mov             x1, x0
    // 0x13636b4: ldur            x0, [fp, #-0x40]
    // 0x13636b8: StoreField: r1->field_b = r0
    //     0x13636b8: stur            w0, [x1, #0xb]
    // 0x13636bc: r0 = Instance_BorderSide
    //     0x13636bc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x13636c0: ldr             x0, [x0, #0xe20]
    // 0x13636c4: StoreField: r1->field_7 = r0
    //     0x13636c4: stur            w0, [x1, #7]
    // 0x13636c8: r16 = <RoundedRectangleBorder>
    //     0x13636c8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13636cc: ldr             x16, [x16, #0xf78]
    // 0x13636d0: stp             x1, x16, [SP]
    // 0x13636d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13636d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13636d8: r0 = all()
    //     0x13636d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13636dc: stur            x0, [fp, #-0x38]
    // 0x13636e0: r0 = ButtonStyle()
    //     0x13636e0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13636e4: mov             x1, x0
    // 0x13636e8: ldur            x0, [fp, #-0x30]
    // 0x13636ec: stur            x1, [fp, #-0x40]
    // 0x13636f0: StoreField: r1->field_b = r0
    //     0x13636f0: stur            w0, [x1, #0xb]
    // 0x13636f4: ldur            x0, [fp, #-0x10]
    // 0x13636f8: StoreField: r1->field_23 = r0
    //     0x13636f8: stur            w0, [x1, #0x23]
    // 0x13636fc: ldur            x0, [fp, #-0x38]
    // 0x1363700: StoreField: r1->field_43 = r0
    //     0x1363700: stur            w0, [x1, #0x43]
    // 0x1363704: r0 = TextButtonThemeData()
    //     0x1363704: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1363708: mov             x2, x0
    // 0x136370c: ldur            x0, [fp, #-0x40]
    // 0x1363710: stur            x2, [fp, #-0x10]
    // 0x1363714: StoreField: r2->field_7 = r0
    //     0x1363714: stur            w0, [x2, #7]
    // 0x1363718: ldur            x0, [fp, #-8]
    // 0x136371c: LoadField: r1 = r0->field_f
    //     0x136371c: ldur            w1, [x0, #0xf]
    // 0x1363720: DecompressPointer r1
    //     0x1363720: add             x1, x1, HEAP, lsl #32
    // 0x1363724: r0 = controller()
    //     0x1363724: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1363728: LoadField: r1 = r0->field_57
    //     0x1363728: ldur            w1, [x0, #0x57]
    // 0x136372c: DecompressPointer r1
    //     0x136372c: add             x1, x1, HEAP, lsl #32
    // 0x1363730: r0 = value()
    //     0x1363730: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1363734: cmp             w0, NULL
    // 0x1363738: b.ne            #0x1363744
    // 0x136373c: r0 = Null
    //     0x136373c: mov             x0, NULL
    // 0x1363740: b               #0x1363764
    // 0x1363744: LoadField: r1 = r0->field_33
    //     0x1363744: ldur            w1, [x0, #0x33]
    // 0x1363748: DecompressPointer r1
    //     0x1363748: add             x1, x1, HEAP, lsl #32
    // 0x136374c: cmp             w1, NULL
    // 0x1363750: b.ne            #0x136375c
    // 0x1363754: r0 = Null
    //     0x1363754: mov             x0, NULL
    // 0x1363758: b               #0x1363764
    // 0x136375c: LoadField: r0 = r1->field_27
    //     0x136375c: ldur            w0, [x1, #0x27]
    // 0x1363760: DecompressPointer r0
    //     0x1363760: add             x0, x0, HEAP, lsl #32
    // 0x1363764: cmp             w0, NULL
    // 0x1363768: b.ne            #0x1363770
    // 0x136376c: r0 = ""
    //     0x136376c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1363770: ldur            x2, [fp, #-8]
    // 0x1363774: ldur            d0, [fp, #-0x50]
    // 0x1363778: ldur            x5, [fp, #-0x20]
    // 0x136377c: ldur            x4, [fp, #-0x28]
    // 0x1363780: ldur            x3, [fp, #-0x48]
    // 0x1363784: ldur            x1, [fp, #-0x10]
    // 0x1363788: r6 = LoadClassIdInstr(r0)
    //     0x1363788: ldur            x6, [x0, #-1]
    //     0x136378c: ubfx            x6, x6, #0xc, #0x14
    // 0x1363790: str             x0, [SP]
    // 0x1363794: mov             x0, x6
    // 0x1363798: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1363798: sub             lr, x0, #0xffa
    //     0x136379c: ldr             lr, [x21, lr, lsl #3]
    //     0x13637a0: blr             lr
    // 0x13637a4: mov             x1, x0
    // 0x13637a8: r0 = capitalizeFirstWord()
    //     0x13637a8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x13637ac: ldur            x2, [fp, #-8]
    // 0x13637b0: stur            x0, [fp, #-0x30]
    // 0x13637b4: LoadField: r1 = r2->field_13
    //     0x13637b4: ldur            w1, [x2, #0x13]
    // 0x13637b8: DecompressPointer r1
    //     0x13637b8: add             x1, x1, HEAP, lsl #32
    // 0x13637bc: r0 = of()
    //     0x13637bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13637c0: LoadField: r1 = r0->field_87
    //     0x13637c0: ldur            w1, [x0, #0x87]
    // 0x13637c4: DecompressPointer r1
    //     0x13637c4: add             x1, x1, HEAP, lsl #32
    // 0x13637c8: LoadField: r0 = r1->field_7
    //     0x13637c8: ldur            w0, [x1, #7]
    // 0x13637cc: DecompressPointer r0
    //     0x13637cc: add             x0, x0, HEAP, lsl #32
    // 0x13637d0: r16 = Instance_Color
    //     0x13637d0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13637d4: r30 = 16.000000
    //     0x13637d4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x13637d8: ldr             lr, [lr, #0x188]
    // 0x13637dc: stp             lr, x16, [SP]
    // 0x13637e0: mov             x1, x0
    // 0x13637e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13637e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13637e8: ldr             x4, [x4, #0x9b8]
    // 0x13637ec: r0 = copyWith()
    //     0x13637ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13637f0: stur            x0, [fp, #-0x38]
    // 0x13637f4: r0 = Text()
    //     0x13637f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13637f8: mov             x3, x0
    // 0x13637fc: ldur            x0, [fp, #-0x30]
    // 0x1363800: stur            x3, [fp, #-0x40]
    // 0x1363804: StoreField: r3->field_b = r0
    //     0x1363804: stur            w0, [x3, #0xb]
    // 0x1363808: ldur            x0, [fp, #-0x38]
    // 0x136380c: StoreField: r3->field_13 = r0
    //     0x136380c: stur            w0, [x3, #0x13]
    // 0x1363810: ldur            x2, [fp, #-8]
    // 0x1363814: r1 = Function '<anonymous closure>':.
    //     0x1363814: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f2f0] AnonymousClosure: (0x132e2d4), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::bottomNavigationBar (0x136a000)
    //     0x1363818: ldr             x1, [x1, #0x2f0]
    // 0x136381c: r0 = AllocateClosure()
    //     0x136381c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1363820: stur            x0, [fp, #-8]
    // 0x1363824: r0 = TextButton()
    //     0x1363824: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1363828: mov             x1, x0
    // 0x136382c: ldur            x0, [fp, #-8]
    // 0x1363830: stur            x1, [fp, #-0x30]
    // 0x1363834: StoreField: r1->field_b = r0
    //     0x1363834: stur            w0, [x1, #0xb]
    // 0x1363838: r0 = false
    //     0x1363838: add             x0, NULL, #0x30  ; false
    // 0x136383c: StoreField: r1->field_27 = r0
    //     0x136383c: stur            w0, [x1, #0x27]
    // 0x1363840: r0 = true
    //     0x1363840: add             x0, NULL, #0x20  ; true
    // 0x1363844: StoreField: r1->field_2f = r0
    //     0x1363844: stur            w0, [x1, #0x2f]
    // 0x1363848: ldur            x0, [fp, #-0x40]
    // 0x136384c: StoreField: r1->field_37 = r0
    //     0x136384c: stur            w0, [x1, #0x37]
    // 0x1363850: r0 = TextButtonTheme()
    //     0x1363850: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1363854: mov             x2, x0
    // 0x1363858: ldur            x0, [fp, #-0x10]
    // 0x136385c: stur            x2, [fp, #-8]
    // 0x1363860: StoreField: r2->field_f = r0
    //     0x1363860: stur            w0, [x2, #0xf]
    // 0x1363864: ldur            x0, [fp, #-0x30]
    // 0x1363868: StoreField: r2->field_b = r0
    //     0x1363868: stur            w0, [x2, #0xb]
    // 0x136386c: r1 = <FlexParentData>
    //     0x136386c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1363870: ldr             x1, [x1, #0xe00]
    // 0x1363874: r0 = Expanded()
    //     0x1363874: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1363878: mov             x3, x0
    // 0x136387c: ldur            x0, [fp, #-0x48]
    // 0x1363880: stur            x3, [fp, #-0x10]
    // 0x1363884: StoreField: r3->field_13 = r0
    //     0x1363884: stur            x0, [x3, #0x13]
    // 0x1363888: r0 = Instance_FlexFit
    //     0x1363888: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x136388c: ldr             x0, [x0, #0xe08]
    // 0x1363890: StoreField: r3->field_1b = r0
    //     0x1363890: stur            w0, [x3, #0x1b]
    // 0x1363894: ldur            x0, [fp, #-8]
    // 0x1363898: StoreField: r3->field_b = r0
    //     0x1363898: stur            w0, [x3, #0xb]
    // 0x136389c: r1 = Null
    //     0x136389c: mov             x1, NULL
    // 0x13638a0: r2 = 4
    //     0x13638a0: movz            x2, #0x4
    // 0x13638a4: r0 = AllocateArray()
    //     0x13638a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13638a8: mov             x2, x0
    // 0x13638ac: ldur            x0, [fp, #-0x28]
    // 0x13638b0: stur            x2, [fp, #-8]
    // 0x13638b4: StoreField: r2->field_f = r0
    //     0x13638b4: stur            w0, [x2, #0xf]
    // 0x13638b8: ldur            x0, [fp, #-0x10]
    // 0x13638bc: StoreField: r2->field_13 = r0
    //     0x13638bc: stur            w0, [x2, #0x13]
    // 0x13638c0: r1 = <Widget>
    //     0x13638c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13638c4: r0 = AllocateGrowableArray()
    //     0x13638c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13638c8: mov             x1, x0
    // 0x13638cc: ldur            x0, [fp, #-8]
    // 0x13638d0: stur            x1, [fp, #-0x10]
    // 0x13638d4: StoreField: r1->field_f = r0
    //     0x13638d4: stur            w0, [x1, #0xf]
    // 0x13638d8: r0 = 4
    //     0x13638d8: movz            x0, #0x4
    // 0x13638dc: StoreField: r1->field_b = r0
    //     0x13638dc: stur            w0, [x1, #0xb]
    // 0x13638e0: r0 = Row()
    //     0x13638e0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13638e4: mov             x3, x0
    // 0x13638e8: r0 = Instance_Axis
    //     0x13638e8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13638ec: stur            x3, [fp, #-8]
    // 0x13638f0: StoreField: r3->field_f = r0
    //     0x13638f0: stur            w0, [x3, #0xf]
    // 0x13638f4: r0 = Instance_MainAxisAlignment
    //     0x13638f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x13638f8: ldr             x0, [x0, #0xa8]
    // 0x13638fc: StoreField: r3->field_13 = r0
    //     0x13638fc: stur            w0, [x3, #0x13]
    // 0x1363900: r0 = Instance_MainAxisSize
    //     0x1363900: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1363904: ldr             x0, [x0, #0xa10]
    // 0x1363908: ArrayStore: r3[0] = r0  ; List_4
    //     0x1363908: stur            w0, [x3, #0x17]
    // 0x136390c: r1 = Instance_CrossAxisAlignment
    //     0x136390c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x1363910: ldr             x1, [x1, #0xc68]
    // 0x1363914: StoreField: r3->field_1b = r1
    //     0x1363914: stur            w1, [x3, #0x1b]
    // 0x1363918: r4 = Instance_VerticalDirection
    //     0x1363918: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x136391c: ldr             x4, [x4, #0xa20]
    // 0x1363920: StoreField: r3->field_23 = r4
    //     0x1363920: stur            w4, [x3, #0x23]
    // 0x1363924: r5 = Instance_Clip
    //     0x1363924: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1363928: ldr             x5, [x5, #0x38]
    // 0x136392c: StoreField: r3->field_2b = r5
    //     0x136392c: stur            w5, [x3, #0x2b]
    // 0x1363930: StoreField: r3->field_2f = rZR
    //     0x1363930: stur            xzr, [x3, #0x2f]
    // 0x1363934: ldur            x1, [fp, #-0x10]
    // 0x1363938: StoreField: r3->field_b = r1
    //     0x1363938: stur            w1, [x3, #0xb]
    // 0x136393c: r1 = Null
    //     0x136393c: mov             x1, NULL
    // 0x1363940: r2 = 6
    //     0x1363940: movz            x2, #0x6
    // 0x1363944: r0 = AllocateArray()
    //     0x1363944: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1363948: mov             x2, x0
    // 0x136394c: ldur            x0, [fp, #-0x20]
    // 0x1363950: stur            x2, [fp, #-0x10]
    // 0x1363954: StoreField: r2->field_f = r0
    //     0x1363954: stur            w0, [x2, #0xf]
    // 0x1363958: r16 = Instance_SizedBox
    //     0x1363958: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x136395c: ldr             x16, [x16, #0xc70]
    // 0x1363960: StoreField: r2->field_13 = r16
    //     0x1363960: stur            w16, [x2, #0x13]
    // 0x1363964: ldur            x0, [fp, #-8]
    // 0x1363968: ArrayStore: r2[0] = r0  ; List_4
    //     0x1363968: stur            w0, [x2, #0x17]
    // 0x136396c: r1 = <Widget>
    //     0x136396c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1363970: r0 = AllocateGrowableArray()
    //     0x1363970: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1363974: mov             x1, x0
    // 0x1363978: ldur            x0, [fp, #-0x10]
    // 0x136397c: stur            x1, [fp, #-8]
    // 0x1363980: StoreField: r1->field_f = r0
    //     0x1363980: stur            w0, [x1, #0xf]
    // 0x1363984: r0 = 6
    //     0x1363984: movz            x0, #0x6
    // 0x1363988: StoreField: r1->field_b = r0
    //     0x1363988: stur            w0, [x1, #0xb]
    // 0x136398c: r0 = Column()
    //     0x136398c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1363990: mov             x1, x0
    // 0x1363994: r0 = Instance_Axis
    //     0x1363994: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1363998: stur            x1, [fp, #-0x10]
    // 0x136399c: StoreField: r1->field_f = r0
    //     0x136399c: stur            w0, [x1, #0xf]
    // 0x13639a0: r0 = Instance_MainAxisAlignment
    //     0x13639a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13639a4: ldr             x0, [x0, #0xa08]
    // 0x13639a8: StoreField: r1->field_13 = r0
    //     0x13639a8: stur            w0, [x1, #0x13]
    // 0x13639ac: r0 = Instance_MainAxisSize
    //     0x13639ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13639b0: ldr             x0, [x0, #0xa10]
    // 0x13639b4: ArrayStore: r1[0] = r0  ; List_4
    //     0x13639b4: stur            w0, [x1, #0x17]
    // 0x13639b8: r0 = Instance_CrossAxisAlignment
    //     0x13639b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13639bc: ldr             x0, [x0, #0xa18]
    // 0x13639c0: StoreField: r1->field_1b = r0
    //     0x13639c0: stur            w0, [x1, #0x1b]
    // 0x13639c4: r0 = Instance_VerticalDirection
    //     0x13639c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13639c8: ldr             x0, [x0, #0xa20]
    // 0x13639cc: StoreField: r1->field_23 = r0
    //     0x13639cc: stur            w0, [x1, #0x23]
    // 0x13639d0: r0 = Instance_Clip
    //     0x13639d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13639d4: ldr             x0, [x0, #0x38]
    // 0x13639d8: StoreField: r1->field_2b = r0
    //     0x13639d8: stur            w0, [x1, #0x2b]
    // 0x13639dc: StoreField: r1->field_2f = rZR
    //     0x13639dc: stur            xzr, [x1, #0x2f]
    // 0x13639e0: ldur            x0, [fp, #-8]
    // 0x13639e4: StoreField: r1->field_b = r0
    //     0x13639e4: stur            w0, [x1, #0xb]
    // 0x13639e8: r0 = Padding()
    //     0x13639e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13639ec: mov             x1, x0
    // 0x13639f0: r0 = Instance_EdgeInsets
    //     0x13639f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13639f4: ldr             x0, [x0, #0x1f0]
    // 0x13639f8: stur            x1, [fp, #-0x20]
    // 0x13639fc: StoreField: r1->field_f = r0
    //     0x13639fc: stur            w0, [x1, #0xf]
    // 0x1363a00: ldur            x0, [fp, #-0x10]
    // 0x1363a04: StoreField: r1->field_b = r0
    //     0x1363a04: stur            w0, [x1, #0xb]
    // 0x1363a08: ldur            d0, [fp, #-0x50]
    // 0x1363a0c: r0 = inline_Allocate_Double()
    //     0x1363a0c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x1363a10: add             x0, x0, #0x10
    //     0x1363a14: cmp             x2, x0
    //     0x1363a18: b.ls            #0x1363a7c
    //     0x1363a1c: str             x0, [THR, #0x50]  ; THR::top
    //     0x1363a20: sub             x0, x0, #0xf
    //     0x1363a24: movz            x2, #0xe15c
    //     0x1363a28: movk            x2, #0x3, lsl #16
    //     0x1363a2c: stur            x2, [x0, #-1]
    // 0x1363a30: StoreField: r0->field_7 = d0
    //     0x1363a30: stur            d0, [x0, #7]
    // 0x1363a34: stur            x0, [fp, #-8]
    // 0x1363a38: r0 = Container()
    //     0x1363a38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1363a3c: stur            x0, [fp, #-0x10]
    // 0x1363a40: ldur            x16, [fp, #-8]
    // 0x1363a44: ldur            lr, [fp, #-0x18]
    // 0x1363a48: stp             lr, x16, [SP, #8]
    // 0x1363a4c: ldur            x16, [fp, #-0x20]
    // 0x1363a50: str             x16, [SP]
    // 0x1363a54: mov             x1, x0
    // 0x1363a58: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x1363a58: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x1363a5c: ldr             x4, [x4, #0xc78]
    // 0x1363a60: r0 = Container()
    //     0x1363a60: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1363a64: ldur            x0, [fp, #-0x10]
    // 0x1363a68: LeaveFrame
    //     0x1363a68: mov             SP, fp
    //     0x1363a6c: ldp             fp, lr, [SP], #0x10
    // 0x1363a70: ret
    //     0x1363a70: ret             
    // 0x1363a74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1363a74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1363a78: b               #0x1362d28
    // 0x1363a7c: SaveReg d0
    //     0x1363a7c: str             q0, [SP, #-0x10]!
    // 0x1363a80: SaveReg r1
    //     0x1363a80: str             x1, [SP, #-8]!
    // 0x1363a84: r0 = AllocateDouble()
    //     0x1363a84: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1363a88: RestoreReg r1
    //     0x1363a88: ldr             x1, [SP], #8
    // 0x1363a8c: RestoreReg d0
    //     0x1363a8c: ldr             q0, [SP], #0x10
    // 0x1363a90: b               #0x1363a30
  }
  _ body(/* No info */) {
    // ** addr: 0x14f8554, size: 0x110
    // 0x14f8554: EnterFrame
    //     0x14f8554: stp             fp, lr, [SP, #-0x10]!
    //     0x14f8558: mov             fp, SP
    // 0x14f855c: AllocStack(0x38)
    //     0x14f855c: sub             SP, SP, #0x38
    // 0x14f8560: SetupParameters(ReturnOrderWithProofView this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x14f8560: stur            x1, [fp, #-8]
    //     0x14f8564: mov             x16, x2
    //     0x14f8568: mov             x2, x1
    //     0x14f856c: mov             x1, x16
    //     0x14f8570: stur            x1, [fp, #-0x10]
    // 0x14f8574: CheckStackOverflow
    //     0x14f8574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f8578: cmp             SP, x16
    //     0x14f857c: b.ls            #0x14f865c
    // 0x14f8580: r1 = 2
    //     0x14f8580: movz            x1, #0x2
    // 0x14f8584: r0 = AllocateContext()
    //     0x14f8584: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f8588: ldur            x2, [fp, #-8]
    // 0x14f858c: stur            x0, [fp, #-0x18]
    // 0x14f8590: StoreField: r0->field_f = r2
    //     0x14f8590: stur            w2, [x0, #0xf]
    // 0x14f8594: ldur            x1, [fp, #-0x10]
    // 0x14f8598: StoreField: r0->field_13 = r1
    //     0x14f8598: stur            w1, [x0, #0x13]
    // 0x14f859c: r0 = of()
    //     0x14f859c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f85a0: LoadField: r1 = r0->field_5b
    //     0x14f85a0: ldur            w1, [x0, #0x5b]
    // 0x14f85a4: DecompressPointer r1
    //     0x14f85a4: add             x1, x1, HEAP, lsl #32
    // 0x14f85a8: r0 = LoadClassIdInstr(r1)
    //     0x14f85a8: ldur            x0, [x1, #-1]
    //     0x14f85ac: ubfx            x0, x0, #0xc, #0x14
    // 0x14f85b0: d0 = 0.030000
    //     0x14f85b0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14f85b4: ldr             d0, [x17, #0x238]
    // 0x14f85b8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14f85b8: sub             lr, x0, #0xffa
    //     0x14f85bc: ldr             lr, [x21, lr, lsl #3]
    //     0x14f85c0: blr             lr
    // 0x14f85c4: stur            x0, [fp, #-0x10]
    // 0x14f85c8: r0 = Obx()
    //     0x14f85c8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14f85cc: ldur            x2, [fp, #-0x18]
    // 0x14f85d0: r1 = Function '<anonymous closure>':.
    //     0x14f85d0: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f2f8] AnonymousClosure: (0x14f869c), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x14f8554)
    //     0x14f85d4: ldr             x1, [x1, #0x2f8]
    // 0x14f85d8: stur            x0, [fp, #-0x18]
    // 0x14f85dc: r0 = AllocateClosure()
    //     0x14f85dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f85e0: mov             x1, x0
    // 0x14f85e4: ldur            x0, [fp, #-0x18]
    // 0x14f85e8: StoreField: r0->field_b = r1
    //     0x14f85e8: stur            w1, [x0, #0xb]
    // 0x14f85ec: r0 = WillPopScope()
    //     0x14f85ec: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14f85f0: mov             x3, x0
    // 0x14f85f4: ldur            x0, [fp, #-0x18]
    // 0x14f85f8: stur            x3, [fp, #-0x20]
    // 0x14f85fc: StoreField: r3->field_b = r0
    //     0x14f85fc: stur            w0, [x3, #0xb]
    // 0x14f8600: ldur            x2, [fp, #-8]
    // 0x14f8604: r1 = Function 'onBackPress':.
    //     0x14f8604: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f300] AnonymousClosure: (0x14f8664), in [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress (0x1441560)
    //     0x14f8608: ldr             x1, [x1, #0x300]
    // 0x14f860c: r0 = AllocateClosure()
    //     0x14f860c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f8610: mov             x1, x0
    // 0x14f8614: ldur            x0, [fp, #-0x20]
    // 0x14f8618: StoreField: r0->field_f = r1
    //     0x14f8618: stur            w1, [x0, #0xf]
    // 0x14f861c: r0 = Container()
    //     0x14f861c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14f8620: stur            x0, [fp, #-8]
    // 0x14f8624: ldur            x16, [fp, #-0x10]
    // 0x14f8628: r30 = inf
    //     0x14f8628: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14f862c: ldr             lr, [lr, #0x9f8]
    // 0x14f8630: stp             lr, x16, [SP, #8]
    // 0x14f8634: ldur            x16, [fp, #-0x20]
    // 0x14f8638: str             x16, [SP]
    // 0x14f863c: mov             x1, x0
    // 0x14f8640: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, height, 0x2, null]
    //     0x14f8640: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f308] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "height", 0x2, Null]
    //     0x14f8644: ldr             x4, [x4, #0x308]
    // 0x14f8648: r0 = Container()
    //     0x14f8648: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14f864c: ldur            x0, [fp, #-8]
    // 0x14f8650: LeaveFrame
    //     0x14f8650: mov             SP, fp
    //     0x14f8654: ldp             fp, lr, [SP], #0x10
    // 0x14f8658: ret
    //     0x14f8658: ret             
    // 0x14f865c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f865c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f8660: b               #0x14f8580
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14f8664, size: 0x38
    // 0x14f8664: EnterFrame
    //     0x14f8664: stp             fp, lr, [SP, #-0x10]!
    //     0x14f8668: mov             fp, SP
    // 0x14f866c: ldr             x0, [fp, #0x10]
    // 0x14f8670: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f8670: ldur            w1, [x0, #0x17]
    // 0x14f8674: DecompressPointer r1
    //     0x14f8674: add             x1, x1, HEAP, lsl #32
    // 0x14f8678: CheckStackOverflow
    //     0x14f8678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f867c: cmp             SP, x16
    //     0x14f8680: b.ls            #0x14f8694
    // 0x14f8684: r0 = onBackPress()
    //     0x14f8684: bl              #0x1441560  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress
    // 0x14f8688: LeaveFrame
    //     0x14f8688: mov             SP, fp
    //     0x14f868c: ldp             fp, lr, [SP], #0x10
    // 0x14f8690: ret
    //     0x14f8690: ret             
    // 0x14f8694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f8694: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f8698: b               #0x14f8684
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14f869c, size: 0x1cb0
    // 0x14f869c: EnterFrame
    //     0x14f869c: stp             fp, lr, [SP, #-0x10]!
    //     0x14f86a0: mov             fp, SP
    // 0x14f86a4: AllocStack(0xc8)
    //     0x14f86a4: sub             SP, SP, #0xc8
    // 0x14f86a8: SetupParameters()
    //     0x14f86a8: ldr             x0, [fp, #0x10]
    //     0x14f86ac: ldur            w2, [x0, #0x17]
    //     0x14f86b0: add             x2, x2, HEAP, lsl #32
    //     0x14f86b4: stur            x2, [fp, #-8]
    // 0x14f86b8: CheckStackOverflow
    //     0x14f86b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f86bc: cmp             SP, x16
    //     0x14f86c0: b.ls            #0x14fa344
    // 0x14f86c4: LoadField: r1 = r2->field_f
    //     0x14f86c4: ldur            w1, [x2, #0xf]
    // 0x14f86c8: DecompressPointer r1
    //     0x14f86c8: add             x1, x1, HEAP, lsl #32
    // 0x14f86cc: r0 = controller()
    //     0x14f86cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f86d0: LoadField: r1 = r0->field_57
    //     0x14f86d0: ldur            w1, [x0, #0x57]
    // 0x14f86d4: DecompressPointer r1
    //     0x14f86d4: add             x1, x1, HEAP, lsl #32
    // 0x14f86d8: r0 = value()
    //     0x14f86d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f86dc: cmp             w0, NULL
    // 0x14f86e0: b.eq            #0x14fa320
    // 0x14f86e4: ldur            x2, [fp, #-8]
    // 0x14f86e8: r0 = Radius()
    //     0x14f86e8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f86ec: d0 = 15.000000
    //     0x14f86ec: fmov            d0, #15.00000000
    // 0x14f86f0: stur            x0, [fp, #-0x10]
    // 0x14f86f4: StoreField: r0->field_7 = d0
    //     0x14f86f4: stur            d0, [x0, #7]
    // 0x14f86f8: StoreField: r0->field_f = d0
    //     0x14f86f8: stur            d0, [x0, #0xf]
    // 0x14f86fc: r0 = BorderRadius()
    //     0x14f86fc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f8700: mov             x1, x0
    // 0x14f8704: ldur            x0, [fp, #-0x10]
    // 0x14f8708: stur            x1, [fp, #-0x18]
    // 0x14f870c: StoreField: r1->field_7 = r0
    //     0x14f870c: stur            w0, [x1, #7]
    // 0x14f8710: StoreField: r1->field_b = r0
    //     0x14f8710: stur            w0, [x1, #0xb]
    // 0x14f8714: StoreField: r1->field_f = r0
    //     0x14f8714: stur            w0, [x1, #0xf]
    // 0x14f8718: StoreField: r1->field_13 = r0
    //     0x14f8718: stur            w0, [x1, #0x13]
    // 0x14f871c: r0 = RoundedRectangleBorder()
    //     0x14f871c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14f8720: mov             x1, x0
    // 0x14f8724: ldur            x0, [fp, #-0x18]
    // 0x14f8728: stur            x1, [fp, #-0x10]
    // 0x14f872c: StoreField: r1->field_b = r0
    //     0x14f872c: stur            w0, [x1, #0xb]
    // 0x14f8730: r0 = Instance_BorderSide
    //     0x14f8730: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14f8734: ldr             x0, [x0, #0xe20]
    // 0x14f8738: StoreField: r1->field_7 = r0
    //     0x14f8738: stur            w0, [x1, #7]
    // 0x14f873c: r0 = Radius()
    //     0x14f873c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f8740: d0 = 12.000000
    //     0x14f8740: fmov            d0, #12.00000000
    // 0x14f8744: stur            x0, [fp, #-0x18]
    // 0x14f8748: StoreField: r0->field_7 = d0
    //     0x14f8748: stur            d0, [x0, #7]
    // 0x14f874c: StoreField: r0->field_f = d0
    //     0x14f874c: stur            d0, [x0, #0xf]
    // 0x14f8750: r0 = BorderRadius()
    //     0x14f8750: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f8754: mov             x2, x0
    // 0x14f8758: ldur            x0, [fp, #-0x18]
    // 0x14f875c: stur            x2, [fp, #-0x20]
    // 0x14f8760: StoreField: r2->field_7 = r0
    //     0x14f8760: stur            w0, [x2, #7]
    // 0x14f8764: StoreField: r2->field_b = r0
    //     0x14f8764: stur            w0, [x2, #0xb]
    // 0x14f8768: StoreField: r2->field_f = r0
    //     0x14f8768: stur            w0, [x2, #0xf]
    // 0x14f876c: StoreField: r2->field_13 = r0
    //     0x14f876c: stur            w0, [x2, #0x13]
    // 0x14f8770: ldur            x0, [fp, #-8]
    // 0x14f8774: LoadField: r1 = r0->field_f
    //     0x14f8774: ldur            w1, [x0, #0xf]
    // 0x14f8778: DecompressPointer r1
    //     0x14f8778: add             x1, x1, HEAP, lsl #32
    // 0x14f877c: r0 = controller()
    //     0x14f877c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8780: LoadField: r1 = r0->field_57
    //     0x14f8780: ldur            w1, [x0, #0x57]
    // 0x14f8784: DecompressPointer r1
    //     0x14f8784: add             x1, x1, HEAP, lsl #32
    // 0x14f8788: r0 = value()
    //     0x14f8788: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f878c: cmp             w0, NULL
    // 0x14f8790: b.ne            #0x14f879c
    // 0x14f8794: r0 = Null
    //     0x14f8794: mov             x0, NULL
    // 0x14f8798: b               #0x14f87bc
    // 0x14f879c: LoadField: r1 = r0->field_f
    //     0x14f879c: ldur            w1, [x0, #0xf]
    // 0x14f87a0: DecompressPointer r1
    //     0x14f87a0: add             x1, x1, HEAP, lsl #32
    // 0x14f87a4: cmp             w1, NULL
    // 0x14f87a8: b.ne            #0x14f87b4
    // 0x14f87ac: r0 = Null
    //     0x14f87ac: mov             x0, NULL
    // 0x14f87b0: b               #0x14f87bc
    // 0x14f87b4: LoadField: r0 = r1->field_7
    //     0x14f87b4: ldur            w0, [x1, #7]
    // 0x14f87b8: DecompressPointer r0
    //     0x14f87b8: add             x0, x0, HEAP, lsl #32
    // 0x14f87bc: cmp             w0, NULL
    // 0x14f87c0: b.ne            #0x14f87cc
    // 0x14f87c4: r4 = ""
    //     0x14f87c4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f87c8: b               #0x14f87d0
    // 0x14f87cc: mov             x4, x0
    // 0x14f87d0: ldur            x3, [fp, #-8]
    // 0x14f87d4: ldur            x0, [fp, #-0x20]
    // 0x14f87d8: stur            x4, [fp, #-0x18]
    // 0x14f87dc: r1 = Function '<anonymous closure>':.
    //     0x14f87dc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f310] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14f87e0: ldr             x1, [x1, #0x310]
    // 0x14f87e4: r2 = Null
    //     0x14f87e4: mov             x2, NULL
    // 0x14f87e8: r0 = AllocateClosure()
    //     0x14f87e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f87ec: r1 = Function '<anonymous closure>':.
    //     0x14f87ec: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f318] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14f87f0: ldr             x1, [x1, #0x318]
    // 0x14f87f4: r2 = Null
    //     0x14f87f4: mov             x2, NULL
    // 0x14f87f8: stur            x0, [fp, #-0x28]
    // 0x14f87fc: r0 = AllocateClosure()
    //     0x14f87fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f8800: stur            x0, [fp, #-0x30]
    // 0x14f8804: r0 = CachedNetworkImage()
    //     0x14f8804: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14f8808: stur            x0, [fp, #-0x38]
    // 0x14f880c: r16 = 65.000000
    //     0x14f880c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0x14f8810: ldr             x16, [x16, #0xd28]
    // 0x14f8814: r30 = 65.000000
    //     0x14f8814: add             lr, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0x14f8818: ldr             lr, [lr, #0xd28]
    // 0x14f881c: stp             lr, x16, [SP, #0x18]
    // 0x14f8820: r16 = Instance_BoxFit
    //     0x14f8820: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14f8824: ldr             x16, [x16, #0x118]
    // 0x14f8828: ldur            lr, [fp, #-0x28]
    // 0x14f882c: stp             lr, x16, [SP, #8]
    // 0x14f8830: ldur            x16, [fp, #-0x30]
    // 0x14f8834: str             x16, [SP]
    // 0x14f8838: mov             x1, x0
    // 0x14f883c: ldur            x2, [fp, #-0x18]
    // 0x14f8840: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14f8840: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14f8844: ldr             x4, [x4, #0xc28]
    // 0x14f8848: r0 = CachedNetworkImage()
    //     0x14f8848: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14f884c: r0 = ClipRRect()
    //     0x14f884c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14f8850: mov             x2, x0
    // 0x14f8854: ldur            x0, [fp, #-0x20]
    // 0x14f8858: stur            x2, [fp, #-0x18]
    // 0x14f885c: StoreField: r2->field_f = r0
    //     0x14f885c: stur            w0, [x2, #0xf]
    // 0x14f8860: r0 = Instance_Clip
    //     0x14f8860: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14f8864: ldr             x0, [x0, #0x138]
    // 0x14f8868: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f8868: stur            w0, [x2, #0x17]
    // 0x14f886c: ldur            x0, [fp, #-0x38]
    // 0x14f8870: StoreField: r2->field_b = r0
    //     0x14f8870: stur            w0, [x2, #0xb]
    // 0x14f8874: ldur            x0, [fp, #-8]
    // 0x14f8878: LoadField: r1 = r0->field_f
    //     0x14f8878: ldur            w1, [x0, #0xf]
    // 0x14f887c: DecompressPointer r1
    //     0x14f887c: add             x1, x1, HEAP, lsl #32
    // 0x14f8880: r0 = controller()
    //     0x14f8880: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8884: LoadField: r1 = r0->field_57
    //     0x14f8884: ldur            w1, [x0, #0x57]
    // 0x14f8888: DecompressPointer r1
    //     0x14f8888: add             x1, x1, HEAP, lsl #32
    // 0x14f888c: r0 = value()
    //     0x14f888c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f8890: cmp             w0, NULL
    // 0x14f8894: b.ne            #0x14f88a0
    // 0x14f8898: r0 = Null
    //     0x14f8898: mov             x0, NULL
    // 0x14f889c: b               #0x14f88c0
    // 0x14f88a0: LoadField: r1 = r0->field_f
    //     0x14f88a0: ldur            w1, [x0, #0xf]
    // 0x14f88a4: DecompressPointer r1
    //     0x14f88a4: add             x1, x1, HEAP, lsl #32
    // 0x14f88a8: cmp             w1, NULL
    // 0x14f88ac: b.ne            #0x14f88b8
    // 0x14f88b0: r0 = Null
    //     0x14f88b0: mov             x0, NULL
    // 0x14f88b4: b               #0x14f88c0
    // 0x14f88b8: LoadField: r0 = r1->field_b
    //     0x14f88b8: ldur            w0, [x1, #0xb]
    // 0x14f88bc: DecompressPointer r0
    //     0x14f88bc: add             x0, x0, HEAP, lsl #32
    // 0x14f88c0: cmp             w0, NULL
    // 0x14f88c4: b.ne            #0x14f88d0
    // 0x14f88c8: r1 = ""
    //     0x14f88c8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f88cc: b               #0x14f88d4
    // 0x14f88d0: mov             x1, x0
    // 0x14f88d4: ldur            x2, [fp, #-8]
    // 0x14f88d8: r0 = capitalizeFirstWord()
    //     0x14f88d8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14f88dc: ldur            x2, [fp, #-8]
    // 0x14f88e0: stur            x0, [fp, #-0x20]
    // 0x14f88e4: LoadField: r1 = r2->field_13
    //     0x14f88e4: ldur            w1, [x2, #0x13]
    // 0x14f88e8: DecompressPointer r1
    //     0x14f88e8: add             x1, x1, HEAP, lsl #32
    // 0x14f88ec: r0 = of()
    //     0x14f88ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f88f0: LoadField: r1 = r0->field_87
    //     0x14f88f0: ldur            w1, [x0, #0x87]
    // 0x14f88f4: DecompressPointer r1
    //     0x14f88f4: add             x1, x1, HEAP, lsl #32
    // 0x14f88f8: LoadField: r0 = r1->field_33
    //     0x14f88f8: ldur            w0, [x1, #0x33]
    // 0x14f88fc: DecompressPointer r0
    //     0x14f88fc: add             x0, x0, HEAP, lsl #32
    // 0x14f8900: stur            x0, [fp, #-0x28]
    // 0x14f8904: cmp             w0, NULL
    // 0x14f8908: b.ne            #0x14f8914
    // 0x14f890c: r1 = Null
    //     0x14f890c: mov             x1, NULL
    // 0x14f8910: b               #0x14f8944
    // 0x14f8914: r1 = Instance_Color
    //     0x14f8914: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f8918: d0 = 0.700000
    //     0x14f8918: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f891c: ldr             d0, [x17, #0xf48]
    // 0x14f8920: r0 = withOpacity()
    //     0x14f8920: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f8924: r16 = 12.000000
    //     0x14f8924: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f8928: ldr             x16, [x16, #0x9e8]
    // 0x14f892c: stp             x16, x0, [SP]
    // 0x14f8930: ldur            x1, [fp, #-0x28]
    // 0x14f8934: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14f8934: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14f8938: ldr             x4, [x4, #0x9b8]
    // 0x14f893c: r0 = copyWith()
    //     0x14f893c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f8940: mov             x1, x0
    // 0x14f8944: ldur            x2, [fp, #-8]
    // 0x14f8948: ldur            x0, [fp, #-0x20]
    // 0x14f894c: stur            x1, [fp, #-0x28]
    // 0x14f8950: r0 = Text()
    //     0x14f8950: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f8954: mov             x2, x0
    // 0x14f8958: ldur            x0, [fp, #-0x20]
    // 0x14f895c: stur            x2, [fp, #-0x30]
    // 0x14f8960: StoreField: r2->field_b = r0
    //     0x14f8960: stur            w0, [x2, #0xb]
    // 0x14f8964: ldur            x0, [fp, #-0x28]
    // 0x14f8968: StoreField: r2->field_13 = r0
    //     0x14f8968: stur            w0, [x2, #0x13]
    // 0x14f896c: r0 = Instance_TextOverflow
    //     0x14f896c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14f8970: ldr             x0, [x0, #0xe10]
    // 0x14f8974: StoreField: r2->field_2b = r0
    //     0x14f8974: stur            w0, [x2, #0x2b]
    // 0x14f8978: r3 = 4
    //     0x14f8978: movz            x3, #0x4
    // 0x14f897c: StoreField: r2->field_37 = r3
    //     0x14f897c: stur            w3, [x2, #0x37]
    // 0x14f8980: ldur            x4, [fp, #-8]
    // 0x14f8984: LoadField: r1 = r4->field_f
    //     0x14f8984: ldur            w1, [x4, #0xf]
    // 0x14f8988: DecompressPointer r1
    //     0x14f8988: add             x1, x1, HEAP, lsl #32
    // 0x14f898c: r0 = controller()
    //     0x14f898c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8990: LoadField: r1 = r0->field_57
    //     0x14f8990: ldur            w1, [x0, #0x57]
    // 0x14f8994: DecompressPointer r1
    //     0x14f8994: add             x1, x1, HEAP, lsl #32
    // 0x14f8998: r0 = value()
    //     0x14f8998: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f899c: cmp             w0, NULL
    // 0x14f89a0: b.ne            #0x14f89ac
    // 0x14f89a4: r0 = Null
    //     0x14f89a4: mov             x0, NULL
    // 0x14f89a8: b               #0x14f89cc
    // 0x14f89ac: LoadField: r1 = r0->field_f
    //     0x14f89ac: ldur            w1, [x0, #0xf]
    // 0x14f89b0: DecompressPointer r1
    //     0x14f89b0: add             x1, x1, HEAP, lsl #32
    // 0x14f89b4: cmp             w1, NULL
    // 0x14f89b8: b.ne            #0x14f89c4
    // 0x14f89bc: r0 = Null
    //     0x14f89bc: mov             x0, NULL
    // 0x14f89c0: b               #0x14f89cc
    // 0x14f89c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14f89c4: ldur            w0, [x1, #0x17]
    // 0x14f89c8: DecompressPointer r0
    //     0x14f89c8: add             x0, x0, HEAP, lsl #32
    // 0x14f89cc: r1 = LoadClassIdInstr(r0)
    //     0x14f89cc: ldur            x1, [x0, #-1]
    //     0x14f89d0: ubfx            x1, x1, #0xc, #0x14
    // 0x14f89d4: r16 = "size"
    //     0x14f89d4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x14f89d8: ldr             x16, [x16, #0x9c0]
    // 0x14f89dc: stp             x16, x0, [SP]
    // 0x14f89e0: mov             x0, x1
    // 0x14f89e4: mov             lr, x0
    // 0x14f89e8: ldr             lr, [x21, lr, lsl #3]
    // 0x14f89ec: blr             lr
    // 0x14f89f0: tbnz            w0, #4, #0x14f8b54
    // 0x14f89f4: ldur            x0, [fp, #-8]
    // 0x14f89f8: r1 = Null
    //     0x14f89f8: mov             x1, NULL
    // 0x14f89fc: r2 = 8
    //     0x14f89fc: movz            x2, #0x8
    // 0x14f8a00: r0 = AllocateArray()
    //     0x14f8a00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f8a04: stur            x0, [fp, #-0x20]
    // 0x14f8a08: r16 = "Size :  "
    //     0x14f8a08: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0x14f8a0c: ldr             x16, [x16, #0x758]
    // 0x14f8a10: StoreField: r0->field_f = r16
    //     0x14f8a10: stur            w16, [x0, #0xf]
    // 0x14f8a14: ldur            x2, [fp, #-8]
    // 0x14f8a18: LoadField: r1 = r2->field_f
    //     0x14f8a18: ldur            w1, [x2, #0xf]
    // 0x14f8a1c: DecompressPointer r1
    //     0x14f8a1c: add             x1, x1, HEAP, lsl #32
    // 0x14f8a20: r0 = controller()
    //     0x14f8a20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8a24: LoadField: r1 = r0->field_57
    //     0x14f8a24: ldur            w1, [x0, #0x57]
    // 0x14f8a28: DecompressPointer r1
    //     0x14f8a28: add             x1, x1, HEAP, lsl #32
    // 0x14f8a2c: r0 = value()
    //     0x14f8a2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f8a30: cmp             w0, NULL
    // 0x14f8a34: b.ne            #0x14f8a40
    // 0x14f8a38: r0 = Null
    //     0x14f8a38: mov             x0, NULL
    // 0x14f8a3c: b               #0x14f8a60
    // 0x14f8a40: LoadField: r1 = r0->field_f
    //     0x14f8a40: ldur            w1, [x0, #0xf]
    // 0x14f8a44: DecompressPointer r1
    //     0x14f8a44: add             x1, x1, HEAP, lsl #32
    // 0x14f8a48: cmp             w1, NULL
    // 0x14f8a4c: b.ne            #0x14f8a58
    // 0x14f8a50: r0 = Null
    //     0x14f8a50: mov             x0, NULL
    // 0x14f8a54: b               #0x14f8a60
    // 0x14f8a58: LoadField: r0 = r1->field_f
    //     0x14f8a58: ldur            w0, [x1, #0xf]
    // 0x14f8a5c: DecompressPointer r0
    //     0x14f8a5c: add             x0, x0, HEAP, lsl #32
    // 0x14f8a60: ldur            x3, [fp, #-8]
    // 0x14f8a64: ldur            x2, [fp, #-0x20]
    // 0x14f8a68: mov             x1, x2
    // 0x14f8a6c: ArrayStore: r1[1] = r0  ; List_4
    //     0x14f8a6c: add             x25, x1, #0x13
    //     0x14f8a70: str             w0, [x25]
    //     0x14f8a74: tbz             w0, #0, #0x14f8a90
    //     0x14f8a78: ldurb           w16, [x1, #-1]
    //     0x14f8a7c: ldurb           w17, [x0, #-1]
    //     0x14f8a80: and             x16, x17, x16, lsr #2
    //     0x14f8a84: tst             x16, HEAP, lsr #32
    //     0x14f8a88: b.eq            #0x14f8a90
    //     0x14f8a8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14f8a90: r16 = " / Qty: "
    //     0x14f8a90: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x14f8a94: ldr             x16, [x16, #0x760]
    // 0x14f8a98: ArrayStore: r2[0] = r16  ; List_4
    //     0x14f8a98: stur            w16, [x2, #0x17]
    // 0x14f8a9c: LoadField: r1 = r3->field_f
    //     0x14f8a9c: ldur            w1, [x3, #0xf]
    // 0x14f8aa0: DecompressPointer r1
    //     0x14f8aa0: add             x1, x1, HEAP, lsl #32
    // 0x14f8aa4: r0 = controller()
    //     0x14f8aa4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8aa8: LoadField: r1 = r0->field_57
    //     0x14f8aa8: ldur            w1, [x0, #0x57]
    // 0x14f8aac: DecompressPointer r1
    //     0x14f8aac: add             x1, x1, HEAP, lsl #32
    // 0x14f8ab0: r0 = value()
    //     0x14f8ab0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f8ab4: cmp             w0, NULL
    // 0x14f8ab8: b.ne            #0x14f8ac4
    // 0x14f8abc: r0 = Null
    //     0x14f8abc: mov             x0, NULL
    // 0x14f8ac0: b               #0x14f8b10
    // 0x14f8ac4: LoadField: r1 = r0->field_f
    //     0x14f8ac4: ldur            w1, [x0, #0xf]
    // 0x14f8ac8: DecompressPointer r1
    //     0x14f8ac8: add             x1, x1, HEAP, lsl #32
    // 0x14f8acc: cmp             w1, NULL
    // 0x14f8ad0: b.ne            #0x14f8adc
    // 0x14f8ad4: r0 = Null
    //     0x14f8ad4: mov             x0, NULL
    // 0x14f8ad8: b               #0x14f8b10
    // 0x14f8adc: LoadField: r0 = r1->field_13
    //     0x14f8adc: ldur            w0, [x1, #0x13]
    // 0x14f8ae0: DecompressPointer r0
    //     0x14f8ae0: add             x0, x0, HEAP, lsl #32
    // 0x14f8ae4: r1 = 60
    //     0x14f8ae4: movz            x1, #0x3c
    // 0x14f8ae8: branchIfSmi(r0, 0x14f8af4)
    //     0x14f8ae8: tbz             w0, #0, #0x14f8af4
    // 0x14f8aec: r1 = LoadClassIdInstr(r0)
    //     0x14f8aec: ldur            x1, [x0, #-1]
    //     0x14f8af0: ubfx            x1, x1, #0xc, #0x14
    // 0x14f8af4: str             x0, [SP]
    // 0x14f8af8: mov             x0, x1
    // 0x14f8afc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x14f8afc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x14f8b00: r0 = GDT[cid_x0 + 0x2700]()
    //     0x14f8b00: movz            x17, #0x2700
    //     0x14f8b04: add             lr, x0, x17
    //     0x14f8b08: ldr             lr, [x21, lr, lsl #3]
    //     0x14f8b0c: blr             lr
    // 0x14f8b10: cmp             w0, NULL
    // 0x14f8b14: b.ne            #0x14f8b1c
    // 0x14f8b18: r0 = ""
    //     0x14f8b18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f8b1c: ldur            x1, [fp, #-0x20]
    // 0x14f8b20: ArrayStore: r1[3] = r0  ; List_4
    //     0x14f8b20: add             x25, x1, #0x1b
    //     0x14f8b24: str             w0, [x25]
    //     0x14f8b28: tbz             w0, #0, #0x14f8b44
    //     0x14f8b2c: ldurb           w16, [x1, #-1]
    //     0x14f8b30: ldurb           w17, [x0, #-1]
    //     0x14f8b34: and             x16, x17, x16, lsr #2
    //     0x14f8b38: tst             x16, HEAP, lsr #32
    //     0x14f8b3c: b.eq            #0x14f8b44
    //     0x14f8b40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14f8b44: ldur            x16, [fp, #-0x20]
    // 0x14f8b48: str             x16, [SP]
    // 0x14f8b4c: r0 = _interpolate()
    //     0x14f8b4c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14f8b50: b               #0x14f8cb0
    // 0x14f8b54: ldur            x0, [fp, #-8]
    // 0x14f8b58: r1 = Null
    //     0x14f8b58: mov             x1, NULL
    // 0x14f8b5c: r2 = 8
    //     0x14f8b5c: movz            x2, #0x8
    // 0x14f8b60: r0 = AllocateArray()
    //     0x14f8b60: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f8b64: stur            x0, [fp, #-0x20]
    // 0x14f8b68: r16 = "Variant : "
    //     0x14f8b68: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0x14f8b6c: ldr             x16, [x16, #0x768]
    // 0x14f8b70: StoreField: r0->field_f = r16
    //     0x14f8b70: stur            w16, [x0, #0xf]
    // 0x14f8b74: ldur            x2, [fp, #-8]
    // 0x14f8b78: LoadField: r1 = r2->field_f
    //     0x14f8b78: ldur            w1, [x2, #0xf]
    // 0x14f8b7c: DecompressPointer r1
    //     0x14f8b7c: add             x1, x1, HEAP, lsl #32
    // 0x14f8b80: r0 = controller()
    //     0x14f8b80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8b84: LoadField: r1 = r0->field_57
    //     0x14f8b84: ldur            w1, [x0, #0x57]
    // 0x14f8b88: DecompressPointer r1
    //     0x14f8b88: add             x1, x1, HEAP, lsl #32
    // 0x14f8b8c: r0 = value()
    //     0x14f8b8c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f8b90: cmp             w0, NULL
    // 0x14f8b94: b.ne            #0x14f8ba0
    // 0x14f8b98: r0 = Null
    //     0x14f8b98: mov             x0, NULL
    // 0x14f8b9c: b               #0x14f8bc0
    // 0x14f8ba0: LoadField: r1 = r0->field_f
    //     0x14f8ba0: ldur            w1, [x0, #0xf]
    // 0x14f8ba4: DecompressPointer r1
    //     0x14f8ba4: add             x1, x1, HEAP, lsl #32
    // 0x14f8ba8: cmp             w1, NULL
    // 0x14f8bac: b.ne            #0x14f8bb8
    // 0x14f8bb0: r0 = Null
    //     0x14f8bb0: mov             x0, NULL
    // 0x14f8bb4: b               #0x14f8bc0
    // 0x14f8bb8: LoadField: r0 = r1->field_f
    //     0x14f8bb8: ldur            w0, [x1, #0xf]
    // 0x14f8bbc: DecompressPointer r0
    //     0x14f8bbc: add             x0, x0, HEAP, lsl #32
    // 0x14f8bc0: ldur            x3, [fp, #-8]
    // 0x14f8bc4: ldur            x2, [fp, #-0x20]
    // 0x14f8bc8: mov             x1, x2
    // 0x14f8bcc: ArrayStore: r1[1] = r0  ; List_4
    //     0x14f8bcc: add             x25, x1, #0x13
    //     0x14f8bd0: str             w0, [x25]
    //     0x14f8bd4: tbz             w0, #0, #0x14f8bf0
    //     0x14f8bd8: ldurb           w16, [x1, #-1]
    //     0x14f8bdc: ldurb           w17, [x0, #-1]
    //     0x14f8be0: and             x16, x17, x16, lsr #2
    //     0x14f8be4: tst             x16, HEAP, lsr #32
    //     0x14f8be8: b.eq            #0x14f8bf0
    //     0x14f8bec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14f8bf0: r16 = " / Qty: "
    //     0x14f8bf0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x14f8bf4: ldr             x16, [x16, #0x760]
    // 0x14f8bf8: ArrayStore: r2[0] = r16  ; List_4
    //     0x14f8bf8: stur            w16, [x2, #0x17]
    // 0x14f8bfc: LoadField: r1 = r3->field_f
    //     0x14f8bfc: ldur            w1, [x3, #0xf]
    // 0x14f8c00: DecompressPointer r1
    //     0x14f8c00: add             x1, x1, HEAP, lsl #32
    // 0x14f8c04: r0 = controller()
    //     0x14f8c04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8c08: LoadField: r1 = r0->field_57
    //     0x14f8c08: ldur            w1, [x0, #0x57]
    // 0x14f8c0c: DecompressPointer r1
    //     0x14f8c0c: add             x1, x1, HEAP, lsl #32
    // 0x14f8c10: r0 = value()
    //     0x14f8c10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f8c14: cmp             w0, NULL
    // 0x14f8c18: b.ne            #0x14f8c24
    // 0x14f8c1c: r0 = Null
    //     0x14f8c1c: mov             x0, NULL
    // 0x14f8c20: b               #0x14f8c70
    // 0x14f8c24: LoadField: r1 = r0->field_f
    //     0x14f8c24: ldur            w1, [x0, #0xf]
    // 0x14f8c28: DecompressPointer r1
    //     0x14f8c28: add             x1, x1, HEAP, lsl #32
    // 0x14f8c2c: cmp             w1, NULL
    // 0x14f8c30: b.ne            #0x14f8c3c
    // 0x14f8c34: r0 = Null
    //     0x14f8c34: mov             x0, NULL
    // 0x14f8c38: b               #0x14f8c70
    // 0x14f8c3c: LoadField: r0 = r1->field_13
    //     0x14f8c3c: ldur            w0, [x1, #0x13]
    // 0x14f8c40: DecompressPointer r0
    //     0x14f8c40: add             x0, x0, HEAP, lsl #32
    // 0x14f8c44: r1 = 60
    //     0x14f8c44: movz            x1, #0x3c
    // 0x14f8c48: branchIfSmi(r0, 0x14f8c54)
    //     0x14f8c48: tbz             w0, #0, #0x14f8c54
    // 0x14f8c4c: r1 = LoadClassIdInstr(r0)
    //     0x14f8c4c: ldur            x1, [x0, #-1]
    //     0x14f8c50: ubfx            x1, x1, #0xc, #0x14
    // 0x14f8c54: str             x0, [SP]
    // 0x14f8c58: mov             x0, x1
    // 0x14f8c5c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x14f8c5c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x14f8c60: r0 = GDT[cid_x0 + 0x2700]()
    //     0x14f8c60: movz            x17, #0x2700
    //     0x14f8c64: add             lr, x0, x17
    //     0x14f8c68: ldr             lr, [x21, lr, lsl #3]
    //     0x14f8c6c: blr             lr
    // 0x14f8c70: cmp             w0, NULL
    // 0x14f8c74: b.ne            #0x14f8c7c
    // 0x14f8c78: r0 = ""
    //     0x14f8c78: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f8c7c: ldur            x1, [fp, #-0x20]
    // 0x14f8c80: ArrayStore: r1[3] = r0  ; List_4
    //     0x14f8c80: add             x25, x1, #0x1b
    //     0x14f8c84: str             w0, [x25]
    //     0x14f8c88: tbz             w0, #0, #0x14f8ca4
    //     0x14f8c8c: ldurb           w16, [x1, #-1]
    //     0x14f8c90: ldurb           w17, [x0, #-1]
    //     0x14f8c94: and             x16, x17, x16, lsr #2
    //     0x14f8c98: tst             x16, HEAP, lsr #32
    //     0x14f8c9c: b.eq            #0x14f8ca4
    //     0x14f8ca0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14f8ca4: ldur            x16, [fp, #-0x20]
    // 0x14f8ca8: str             x16, [SP]
    // 0x14f8cac: r0 = _interpolate()
    //     0x14f8cac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14f8cb0: ldur            x2, [fp, #-8]
    // 0x14f8cb4: stur            x0, [fp, #-0x20]
    // 0x14f8cb8: LoadField: r1 = r2->field_13
    //     0x14f8cb8: ldur            w1, [x2, #0x13]
    // 0x14f8cbc: DecompressPointer r1
    //     0x14f8cbc: add             x1, x1, HEAP, lsl #32
    // 0x14f8cc0: r0 = of()
    //     0x14f8cc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f8cc4: LoadField: r1 = r0->field_87
    //     0x14f8cc4: ldur            w1, [x0, #0x87]
    // 0x14f8cc8: DecompressPointer r1
    //     0x14f8cc8: add             x1, x1, HEAP, lsl #32
    // 0x14f8ccc: LoadField: r0 = r1->field_33
    //     0x14f8ccc: ldur            w0, [x1, #0x33]
    // 0x14f8cd0: DecompressPointer r0
    //     0x14f8cd0: add             x0, x0, HEAP, lsl #32
    // 0x14f8cd4: stur            x0, [fp, #-0x28]
    // 0x14f8cd8: cmp             w0, NULL
    // 0x14f8cdc: b.ne            #0x14f8ce8
    // 0x14f8ce0: r1 = Null
    //     0x14f8ce0: mov             x1, NULL
    // 0x14f8ce4: b               #0x14f8d14
    // 0x14f8ce8: r1 = Instance_Color
    //     0x14f8ce8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f8cec: d0 = 0.400000
    //     0x14f8cec: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14f8cf0: r0 = withOpacity()
    //     0x14f8cf0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f8cf4: r16 = 12.000000
    //     0x14f8cf4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f8cf8: ldr             x16, [x16, #0x9e8]
    // 0x14f8cfc: stp             x0, x16, [SP]
    // 0x14f8d00: ldur            x1, [fp, #-0x28]
    // 0x14f8d04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f8d04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f8d08: ldr             x4, [x4, #0xaa0]
    // 0x14f8d0c: r0 = copyWith()
    //     0x14f8d0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f8d10: mov             x1, x0
    // 0x14f8d14: ldur            x2, [fp, #-8]
    // 0x14f8d18: ldur            x0, [fp, #-0x20]
    // 0x14f8d1c: stur            x1, [fp, #-0x28]
    // 0x14f8d20: r0 = Text()
    //     0x14f8d20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f8d24: mov             x1, x0
    // 0x14f8d28: ldur            x0, [fp, #-0x20]
    // 0x14f8d2c: stur            x1, [fp, #-0x38]
    // 0x14f8d30: StoreField: r1->field_b = r0
    //     0x14f8d30: stur            w0, [x1, #0xb]
    // 0x14f8d34: ldur            x0, [fp, #-0x28]
    // 0x14f8d38: StoreField: r1->field_13 = r0
    //     0x14f8d38: stur            w0, [x1, #0x13]
    // 0x14f8d3c: r0 = Padding()
    //     0x14f8d3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f8d40: mov             x2, x0
    // 0x14f8d44: r0 = Instance_EdgeInsets
    //     0x14f8d44: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x14f8d48: ldr             x0, [x0, #0x668]
    // 0x14f8d4c: stur            x2, [fp, #-0x20]
    // 0x14f8d50: StoreField: r2->field_f = r0
    //     0x14f8d50: stur            w0, [x2, #0xf]
    // 0x14f8d54: ldur            x0, [fp, #-0x38]
    // 0x14f8d58: StoreField: r2->field_b = r0
    //     0x14f8d58: stur            w0, [x2, #0xb]
    // 0x14f8d5c: ldur            x0, [fp, #-8]
    // 0x14f8d60: LoadField: r1 = r0->field_f
    //     0x14f8d60: ldur            w1, [x0, #0xf]
    // 0x14f8d64: DecompressPointer r1
    //     0x14f8d64: add             x1, x1, HEAP, lsl #32
    // 0x14f8d68: r0 = controller()
    //     0x14f8d68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f8d6c: LoadField: r1 = r0->field_57
    //     0x14f8d6c: ldur            w1, [x0, #0x57]
    // 0x14f8d70: DecompressPointer r1
    //     0x14f8d70: add             x1, x1, HEAP, lsl #32
    // 0x14f8d74: r0 = value()
    //     0x14f8d74: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f8d78: cmp             w0, NULL
    // 0x14f8d7c: b.ne            #0x14f8d88
    // 0x14f8d80: r0 = Null
    //     0x14f8d80: mov             x0, NULL
    // 0x14f8d84: b               #0x14f8dc4
    // 0x14f8d88: LoadField: r1 = r0->field_f
    //     0x14f8d88: ldur            w1, [x0, #0xf]
    // 0x14f8d8c: DecompressPointer r1
    //     0x14f8d8c: add             x1, x1, HEAP, lsl #32
    // 0x14f8d90: cmp             w1, NULL
    // 0x14f8d94: b.ne            #0x14f8da0
    // 0x14f8d98: r0 = Null
    //     0x14f8d98: mov             x0, NULL
    // 0x14f8d9c: b               #0x14f8dc4
    // 0x14f8da0: LoadField: r0 = r1->field_1b
    //     0x14f8da0: ldur            w0, [x1, #0x1b]
    // 0x14f8da4: DecompressPointer r0
    //     0x14f8da4: add             x0, x0, HEAP, lsl #32
    // 0x14f8da8: cmp             w0, NULL
    // 0x14f8dac: b.ne            #0x14f8db8
    // 0x14f8db0: r0 = Null
    //     0x14f8db0: mov             x0, NULL
    // 0x14f8db4: b               #0x14f8dc4
    // 0x14f8db8: LoadField: r1 = r0->field_7
    //     0x14f8db8: ldur            w1, [x0, #7]
    // 0x14f8dbc: DecompressPointer r1
    //     0x14f8dbc: add             x1, x1, HEAP, lsl #32
    // 0x14f8dc0: mov             x0, x1
    // 0x14f8dc4: cmp             w0, NULL
    // 0x14f8dc8: b.ne            #0x14f8dd0
    // 0x14f8dcc: r0 = ""
    //     0x14f8dcc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f8dd0: ldur            x2, [fp, #-8]
    // 0x14f8dd4: stur            x0, [fp, #-0x28]
    // 0x14f8dd8: LoadField: r1 = r2->field_13
    //     0x14f8dd8: ldur            w1, [x2, #0x13]
    // 0x14f8ddc: DecompressPointer r1
    //     0x14f8ddc: add             x1, x1, HEAP, lsl #32
    // 0x14f8de0: r0 = of()
    //     0x14f8de0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f8de4: LoadField: r1 = r0->field_87
    //     0x14f8de4: ldur            w1, [x0, #0x87]
    // 0x14f8de8: DecompressPointer r1
    //     0x14f8de8: add             x1, x1, HEAP, lsl #32
    // 0x14f8dec: LoadField: r0 = r1->field_33
    //     0x14f8dec: ldur            w0, [x1, #0x33]
    // 0x14f8df0: DecompressPointer r0
    //     0x14f8df0: add             x0, x0, HEAP, lsl #32
    // 0x14f8df4: stur            x0, [fp, #-0x38]
    // 0x14f8df8: cmp             w0, NULL
    // 0x14f8dfc: b.ne            #0x14f8e08
    // 0x14f8e00: r6 = Null
    //     0x14f8e00: mov             x6, NULL
    // 0x14f8e04: b               #0x14f8e38
    // 0x14f8e08: r1 = Instance_Color
    //     0x14f8e08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f8e0c: d0 = 0.700000
    //     0x14f8e0c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f8e10: ldr             d0, [x17, #0xf48]
    // 0x14f8e14: r0 = withOpacity()
    //     0x14f8e14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f8e18: r16 = 12.000000
    //     0x14f8e18: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f8e1c: ldr             x16, [x16, #0x9e8]
    // 0x14f8e20: stp             x16, x0, [SP]
    // 0x14f8e24: ldur            x1, [fp, #-0x38]
    // 0x14f8e28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14f8e28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14f8e2c: ldr             x4, [x4, #0x9b8]
    // 0x14f8e30: r0 = copyWith()
    //     0x14f8e30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f8e34: mov             x6, x0
    // 0x14f8e38: ldur            x2, [fp, #-8]
    // 0x14f8e3c: ldur            x5, [fp, #-0x10]
    // 0x14f8e40: ldur            x4, [fp, #-0x18]
    // 0x14f8e44: ldur            x3, [fp, #-0x30]
    // 0x14f8e48: ldur            x1, [fp, #-0x20]
    // 0x14f8e4c: ldur            x0, [fp, #-0x28]
    // 0x14f8e50: stur            x6, [fp, #-0x38]
    // 0x14f8e54: r0 = Text()
    //     0x14f8e54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f8e58: mov             x3, x0
    // 0x14f8e5c: ldur            x0, [fp, #-0x28]
    // 0x14f8e60: stur            x3, [fp, #-0x40]
    // 0x14f8e64: StoreField: r3->field_b = r0
    //     0x14f8e64: stur            w0, [x3, #0xb]
    // 0x14f8e68: ldur            x0, [fp, #-0x38]
    // 0x14f8e6c: StoreField: r3->field_13 = r0
    //     0x14f8e6c: stur            w0, [x3, #0x13]
    // 0x14f8e70: r1 = Null
    //     0x14f8e70: mov             x1, NULL
    // 0x14f8e74: r2 = 8
    //     0x14f8e74: movz            x2, #0x8
    // 0x14f8e78: r0 = AllocateArray()
    //     0x14f8e78: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f8e7c: mov             x2, x0
    // 0x14f8e80: ldur            x0, [fp, #-0x30]
    // 0x14f8e84: stur            x2, [fp, #-0x28]
    // 0x14f8e88: StoreField: r2->field_f = r0
    //     0x14f8e88: stur            w0, [x2, #0xf]
    // 0x14f8e8c: ldur            x0, [fp, #-0x20]
    // 0x14f8e90: StoreField: r2->field_13 = r0
    //     0x14f8e90: stur            w0, [x2, #0x13]
    // 0x14f8e94: r16 = Instance_SizedBox
    //     0x14f8e94: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14f8e98: ldr             x16, [x16, #0xc70]
    // 0x14f8e9c: ArrayStore: r2[0] = r16  ; List_4
    //     0x14f8e9c: stur            w16, [x2, #0x17]
    // 0x14f8ea0: ldur            x0, [fp, #-0x40]
    // 0x14f8ea4: StoreField: r2->field_1b = r0
    //     0x14f8ea4: stur            w0, [x2, #0x1b]
    // 0x14f8ea8: r1 = <Widget>
    //     0x14f8ea8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14f8eac: r0 = AllocateGrowableArray()
    //     0x14f8eac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f8eb0: mov             x1, x0
    // 0x14f8eb4: ldur            x0, [fp, #-0x28]
    // 0x14f8eb8: stur            x1, [fp, #-0x20]
    // 0x14f8ebc: StoreField: r1->field_f = r0
    //     0x14f8ebc: stur            w0, [x1, #0xf]
    // 0x14f8ec0: r2 = 8
    //     0x14f8ec0: movz            x2, #0x8
    // 0x14f8ec4: StoreField: r1->field_b = r2
    //     0x14f8ec4: stur            w2, [x1, #0xb]
    // 0x14f8ec8: r0 = Column()
    //     0x14f8ec8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14f8ecc: mov             x2, x0
    // 0x14f8ed0: r0 = Instance_Axis
    //     0x14f8ed0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14f8ed4: stur            x2, [fp, #-0x28]
    // 0x14f8ed8: StoreField: r2->field_f = r0
    //     0x14f8ed8: stur            w0, [x2, #0xf]
    // 0x14f8edc: r1 = Instance_MainAxisAlignment
    //     0x14f8edc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14f8ee0: ldr             x1, [x1, #0xa8]
    // 0x14f8ee4: StoreField: r2->field_13 = r1
    //     0x14f8ee4: stur            w1, [x2, #0x13]
    // 0x14f8ee8: r3 = Instance_MainAxisSize
    //     0x14f8ee8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14f8eec: ldr             x3, [x3, #0xdd0]
    // 0x14f8ef0: ArrayStore: r2[0] = r3  ; List_4
    //     0x14f8ef0: stur            w3, [x2, #0x17]
    // 0x14f8ef4: r4 = Instance_CrossAxisAlignment
    //     0x14f8ef4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14f8ef8: ldr             x4, [x4, #0x890]
    // 0x14f8efc: StoreField: r2->field_1b = r4
    //     0x14f8efc: stur            w4, [x2, #0x1b]
    // 0x14f8f00: r5 = Instance_VerticalDirection
    //     0x14f8f00: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14f8f04: ldr             x5, [x5, #0xa20]
    // 0x14f8f08: StoreField: r2->field_23 = r5
    //     0x14f8f08: stur            w5, [x2, #0x23]
    // 0x14f8f0c: r6 = Instance_Clip
    //     0x14f8f0c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14f8f10: ldr             x6, [x6, #0x38]
    // 0x14f8f14: StoreField: r2->field_2b = r6
    //     0x14f8f14: stur            w6, [x2, #0x2b]
    // 0x14f8f18: StoreField: r2->field_2f = rZR
    //     0x14f8f18: stur            xzr, [x2, #0x2f]
    // 0x14f8f1c: ldur            x1, [fp, #-0x20]
    // 0x14f8f20: StoreField: r2->field_b = r1
    //     0x14f8f20: stur            w1, [x2, #0xb]
    // 0x14f8f24: r1 = <FlexParentData>
    //     0x14f8f24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14f8f28: ldr             x1, [x1, #0xe00]
    // 0x14f8f2c: r0 = Expanded()
    //     0x14f8f2c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14f8f30: mov             x3, x0
    // 0x14f8f34: r0 = 1
    //     0x14f8f34: movz            x0, #0x1
    // 0x14f8f38: stur            x3, [fp, #-0x20]
    // 0x14f8f3c: StoreField: r3->field_13 = r0
    //     0x14f8f3c: stur            x0, [x3, #0x13]
    // 0x14f8f40: r4 = Instance_FlexFit
    //     0x14f8f40: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14f8f44: ldr             x4, [x4, #0xe08]
    // 0x14f8f48: StoreField: r3->field_1b = r4
    //     0x14f8f48: stur            w4, [x3, #0x1b]
    // 0x14f8f4c: ldur            x1, [fp, #-0x28]
    // 0x14f8f50: StoreField: r3->field_b = r1
    //     0x14f8f50: stur            w1, [x3, #0xb]
    // 0x14f8f54: r1 = Null
    //     0x14f8f54: mov             x1, NULL
    // 0x14f8f58: r2 = 6
    //     0x14f8f58: movz            x2, #0x6
    // 0x14f8f5c: r0 = AllocateArray()
    //     0x14f8f5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f8f60: mov             x2, x0
    // 0x14f8f64: ldur            x0, [fp, #-0x18]
    // 0x14f8f68: stur            x2, [fp, #-0x28]
    // 0x14f8f6c: StoreField: r2->field_f = r0
    //     0x14f8f6c: stur            w0, [x2, #0xf]
    // 0x14f8f70: r16 = Instance_SizedBox
    //     0x14f8f70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14f8f74: ldr             x16, [x16, #0xb20]
    // 0x14f8f78: StoreField: r2->field_13 = r16
    //     0x14f8f78: stur            w16, [x2, #0x13]
    // 0x14f8f7c: ldur            x0, [fp, #-0x20]
    // 0x14f8f80: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f8f80: stur            w0, [x2, #0x17]
    // 0x14f8f84: r1 = <Widget>
    //     0x14f8f84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14f8f88: r0 = AllocateGrowableArray()
    //     0x14f8f88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f8f8c: mov             x1, x0
    // 0x14f8f90: ldur            x0, [fp, #-0x28]
    // 0x14f8f94: stur            x1, [fp, #-0x18]
    // 0x14f8f98: StoreField: r1->field_f = r0
    //     0x14f8f98: stur            w0, [x1, #0xf]
    // 0x14f8f9c: r2 = 6
    //     0x14f8f9c: movz            x2, #0x6
    // 0x14f8fa0: StoreField: r1->field_b = r2
    //     0x14f8fa0: stur            w2, [x1, #0xb]
    // 0x14f8fa4: r0 = Row()
    //     0x14f8fa4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14f8fa8: mov             x3, x0
    // 0x14f8fac: r0 = Instance_Axis
    //     0x14f8fac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14f8fb0: stur            x3, [fp, #-0x20]
    // 0x14f8fb4: StoreField: r3->field_f = r0
    //     0x14f8fb4: stur            w0, [x3, #0xf]
    // 0x14f8fb8: r4 = Instance_MainAxisAlignment
    //     0x14f8fb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14f8fbc: ldr             x4, [x4, #0xa08]
    // 0x14f8fc0: StoreField: r3->field_13 = r4
    //     0x14f8fc0: stur            w4, [x3, #0x13]
    // 0x14f8fc4: r5 = Instance_MainAxisSize
    //     0x14f8fc4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14f8fc8: ldr             x5, [x5, #0xa10]
    // 0x14f8fcc: ArrayStore: r3[0] = r5  ; List_4
    //     0x14f8fcc: stur            w5, [x3, #0x17]
    // 0x14f8fd0: r6 = Instance_CrossAxisAlignment
    //     0x14f8fd0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14f8fd4: ldr             x6, [x6, #0xa18]
    // 0x14f8fd8: StoreField: r3->field_1b = r6
    //     0x14f8fd8: stur            w6, [x3, #0x1b]
    // 0x14f8fdc: r7 = Instance_VerticalDirection
    //     0x14f8fdc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14f8fe0: ldr             x7, [x7, #0xa20]
    // 0x14f8fe4: StoreField: r3->field_23 = r7
    //     0x14f8fe4: stur            w7, [x3, #0x23]
    // 0x14f8fe8: r8 = Instance_Clip
    //     0x14f8fe8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14f8fec: ldr             x8, [x8, #0x38]
    // 0x14f8ff0: StoreField: r3->field_2b = r8
    //     0x14f8ff0: stur            w8, [x3, #0x2b]
    // 0x14f8ff4: StoreField: r3->field_2f = rZR
    //     0x14f8ff4: stur            xzr, [x3, #0x2f]
    // 0x14f8ff8: ldur            x1, [fp, #-0x18]
    // 0x14f8ffc: StoreField: r3->field_b = r1
    //     0x14f8ffc: stur            w1, [x3, #0xb]
    // 0x14f9000: r1 = Null
    //     0x14f9000: mov             x1, NULL
    // 0x14f9004: r2 = 2
    //     0x14f9004: movz            x2, #0x2
    // 0x14f9008: r0 = AllocateArray()
    //     0x14f9008: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f900c: mov             x2, x0
    // 0x14f9010: ldur            x0, [fp, #-0x20]
    // 0x14f9014: stur            x2, [fp, #-0x18]
    // 0x14f9018: StoreField: r2->field_f = r0
    //     0x14f9018: stur            w0, [x2, #0xf]
    // 0x14f901c: r1 = <Widget>
    //     0x14f901c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14f9020: r0 = AllocateGrowableArray()
    //     0x14f9020: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f9024: mov             x1, x0
    // 0x14f9028: ldur            x0, [fp, #-0x18]
    // 0x14f902c: stur            x1, [fp, #-0x20]
    // 0x14f9030: StoreField: r1->field_f = r0
    //     0x14f9030: stur            w0, [x1, #0xf]
    // 0x14f9034: r2 = 2
    //     0x14f9034: movz            x2, #0x2
    // 0x14f9038: StoreField: r1->field_b = r2
    //     0x14f9038: stur            w2, [x1, #0xb]
    // 0x14f903c: r0 = Column()
    //     0x14f903c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14f9040: mov             x1, x0
    // 0x14f9044: r0 = Instance_Axis
    //     0x14f9044: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14f9048: stur            x1, [fp, #-0x18]
    // 0x14f904c: StoreField: r1->field_f = r0
    //     0x14f904c: stur            w0, [x1, #0xf]
    // 0x14f9050: r2 = Instance_MainAxisAlignment
    //     0x14f9050: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14f9054: ldr             x2, [x2, #0xa08]
    // 0x14f9058: StoreField: r1->field_13 = r2
    //     0x14f9058: stur            w2, [x1, #0x13]
    // 0x14f905c: r3 = Instance_MainAxisSize
    //     0x14f905c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14f9060: ldr             x3, [x3, #0xa10]
    // 0x14f9064: ArrayStore: r1[0] = r3  ; List_4
    //     0x14f9064: stur            w3, [x1, #0x17]
    // 0x14f9068: r4 = Instance_CrossAxisAlignment
    //     0x14f9068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14f906c: ldr             x4, [x4, #0xa18]
    // 0x14f9070: StoreField: r1->field_1b = r4
    //     0x14f9070: stur            w4, [x1, #0x1b]
    // 0x14f9074: r5 = Instance_VerticalDirection
    //     0x14f9074: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14f9078: ldr             x5, [x5, #0xa20]
    // 0x14f907c: StoreField: r1->field_23 = r5
    //     0x14f907c: stur            w5, [x1, #0x23]
    // 0x14f9080: r6 = Instance_Clip
    //     0x14f9080: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14f9084: ldr             x6, [x6, #0x38]
    // 0x14f9088: StoreField: r1->field_2b = r6
    //     0x14f9088: stur            w6, [x1, #0x2b]
    // 0x14f908c: StoreField: r1->field_2f = rZR
    //     0x14f908c: stur            xzr, [x1, #0x2f]
    // 0x14f9090: ldur            x7, [fp, #-0x20]
    // 0x14f9094: StoreField: r1->field_b = r7
    //     0x14f9094: stur            w7, [x1, #0xb]
    // 0x14f9098: r0 = Padding()
    //     0x14f9098: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f909c: mov             x1, x0
    // 0x14f90a0: r0 = Instance_EdgeInsets
    //     0x14f90a0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x14f90a4: ldr             x0, [x0, #0xf98]
    // 0x14f90a8: stur            x1, [fp, #-0x20]
    // 0x14f90ac: StoreField: r1->field_f = r0
    //     0x14f90ac: stur            w0, [x1, #0xf]
    // 0x14f90b0: ldur            x0, [fp, #-0x18]
    // 0x14f90b4: StoreField: r1->field_b = r0
    //     0x14f90b4: stur            w0, [x1, #0xb]
    // 0x14f90b8: r0 = Card()
    //     0x14f90b8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14f90bc: mov             x1, x0
    // 0x14f90c0: r0 = 0.000000
    //     0x14f90c0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14f90c4: stur            x1, [fp, #-0x18]
    // 0x14f90c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14f90c8: stur            w0, [x1, #0x17]
    // 0x14f90cc: ldur            x2, [fp, #-0x10]
    // 0x14f90d0: StoreField: r1->field_1b = r2
    //     0x14f90d0: stur            w2, [x1, #0x1b]
    // 0x14f90d4: r2 = true
    //     0x14f90d4: add             x2, NULL, #0x20  ; true
    // 0x14f90d8: StoreField: r1->field_1f = r2
    //     0x14f90d8: stur            w2, [x1, #0x1f]
    // 0x14f90dc: ldur            x3, [fp, #-0x20]
    // 0x14f90e0: StoreField: r1->field_2f = r3
    //     0x14f90e0: stur            w3, [x1, #0x2f]
    // 0x14f90e4: StoreField: r1->field_2b = r2
    //     0x14f90e4: stur            w2, [x1, #0x2b]
    // 0x14f90e8: r3 = Instance__CardVariant
    //     0x14f90e8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14f90ec: ldr             x3, [x3, #0xa68]
    // 0x14f90f0: StoreField: r1->field_33 = r3
    //     0x14f90f0: stur            w3, [x1, #0x33]
    // 0x14f90f4: r0 = Radius()
    //     0x14f90f4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f90f8: d0 = 15.000000
    //     0x14f90f8: fmov            d0, #15.00000000
    // 0x14f90fc: stur            x0, [fp, #-0x10]
    // 0x14f9100: StoreField: r0->field_7 = d0
    //     0x14f9100: stur            d0, [x0, #7]
    // 0x14f9104: StoreField: r0->field_f = d0
    //     0x14f9104: stur            d0, [x0, #0xf]
    // 0x14f9108: r0 = BorderRadius()
    //     0x14f9108: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f910c: mov             x1, x0
    // 0x14f9110: ldur            x0, [fp, #-0x10]
    // 0x14f9114: stur            x1, [fp, #-0x20]
    // 0x14f9118: StoreField: r1->field_7 = r0
    //     0x14f9118: stur            w0, [x1, #7]
    // 0x14f911c: StoreField: r1->field_b = r0
    //     0x14f911c: stur            w0, [x1, #0xb]
    // 0x14f9120: StoreField: r1->field_f = r0
    //     0x14f9120: stur            w0, [x1, #0xf]
    // 0x14f9124: StoreField: r1->field_13 = r0
    //     0x14f9124: stur            w0, [x1, #0x13]
    // 0x14f9128: r0 = RoundedRectangleBorder()
    //     0x14f9128: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14f912c: mov             x2, x0
    // 0x14f9130: ldur            x0, [fp, #-0x20]
    // 0x14f9134: stur            x2, [fp, #-0x10]
    // 0x14f9138: StoreField: r2->field_b = r0
    //     0x14f9138: stur            w0, [x2, #0xb]
    // 0x14f913c: r0 = Instance_BorderSide
    //     0x14f913c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14f9140: ldr             x0, [x0, #0xe20]
    // 0x14f9144: StoreField: r2->field_7 = r0
    //     0x14f9144: stur            w0, [x2, #7]
    // 0x14f9148: ldur            x3, [fp, #-8]
    // 0x14f914c: LoadField: r1 = r3->field_13
    //     0x14f914c: ldur            w1, [x3, #0x13]
    // 0x14f9150: DecompressPointer r1
    //     0x14f9150: add             x1, x1, HEAP, lsl #32
    // 0x14f9154: r0 = of()
    //     0x14f9154: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9158: LoadField: r1 = r0->field_87
    //     0x14f9158: ldur            w1, [x0, #0x87]
    // 0x14f915c: DecompressPointer r1
    //     0x14f915c: add             x1, x1, HEAP, lsl #32
    // 0x14f9160: LoadField: r0 = r1->field_7
    //     0x14f9160: ldur            w0, [x1, #7]
    // 0x14f9164: DecompressPointer r0
    //     0x14f9164: add             x0, x0, HEAP, lsl #32
    // 0x14f9168: r16 = 16.000000
    //     0x14f9168: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14f916c: ldr             x16, [x16, #0x188]
    // 0x14f9170: r30 = Instance_Color
    //     0x14f9170: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f9174: stp             lr, x16, [SP]
    // 0x14f9178: mov             x1, x0
    // 0x14f917c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f917c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f9180: ldr             x4, [x4, #0xaa0]
    // 0x14f9184: r0 = copyWith()
    //     0x14f9184: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9188: stur            x0, [fp, #-0x20]
    // 0x14f918c: r0 = Text()
    //     0x14f918c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9190: mov             x2, x0
    // 0x14f9194: r0 = "Share Proofs"
    //     0x14f9194: add             x0, PP, #0x33, lsl #12  ; [pp+0x33780] "Share Proofs"
    //     0x14f9198: ldr             x0, [x0, #0x780]
    // 0x14f919c: stur            x2, [fp, #-0x28]
    // 0x14f91a0: StoreField: r2->field_b = r0
    //     0x14f91a0: stur            w0, [x2, #0xb]
    // 0x14f91a4: ldur            x0, [fp, #-0x20]
    // 0x14f91a8: StoreField: r2->field_13 = r0
    //     0x14f91a8: stur            w0, [x2, #0x13]
    // 0x14f91ac: ldur            x0, [fp, #-8]
    // 0x14f91b0: LoadField: r1 = r0->field_f
    //     0x14f91b0: ldur            w1, [x0, #0xf]
    // 0x14f91b4: DecompressPointer r1
    //     0x14f91b4: add             x1, x1, HEAP, lsl #32
    // 0x14f91b8: r0 = controller()
    //     0x14f91b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f91bc: LoadField: r1 = r0->field_57
    //     0x14f91bc: ldur            w1, [x0, #0x57]
    // 0x14f91c0: DecompressPointer r1
    //     0x14f91c0: add             x1, x1, HEAP, lsl #32
    // 0x14f91c4: r0 = value()
    //     0x14f91c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f91c8: cmp             w0, NULL
    // 0x14f91cc: b.ne            #0x14f91d8
    // 0x14f91d0: r0 = Null
    //     0x14f91d0: mov             x0, NULL
    // 0x14f91d4: b               #0x14f91e4
    // 0x14f91d8: LoadField: r1 = r0->field_27
    //     0x14f91d8: ldur            w1, [x0, #0x27]
    // 0x14f91dc: DecompressPointer r1
    //     0x14f91dc: add             x1, x1, HEAP, lsl #32
    // 0x14f91e0: mov             x0, x1
    // 0x14f91e4: cmp             w0, NULL
    // 0x14f91e8: b.ne            #0x14f91f0
    // 0x14f91ec: r0 = ""
    //     0x14f91ec: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f91f0: ldur            x2, [fp, #-8]
    // 0x14f91f4: stur            x0, [fp, #-0x20]
    // 0x14f91f8: LoadField: r1 = r2->field_13
    //     0x14f91f8: ldur            w1, [x2, #0x13]
    // 0x14f91fc: DecompressPointer r1
    //     0x14f91fc: add             x1, x1, HEAP, lsl #32
    // 0x14f9200: r0 = of()
    //     0x14f9200: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9204: LoadField: r1 = r0->field_87
    //     0x14f9204: ldur            w1, [x0, #0x87]
    // 0x14f9208: DecompressPointer r1
    //     0x14f9208: add             x1, x1, HEAP, lsl #32
    // 0x14f920c: LoadField: r0 = r1->field_2b
    //     0x14f920c: ldur            w0, [x1, #0x2b]
    // 0x14f9210: DecompressPointer r0
    //     0x14f9210: add             x0, x0, HEAP, lsl #32
    // 0x14f9214: stur            x0, [fp, #-0x30]
    // 0x14f9218: r1 = Instance_Color
    //     0x14f9218: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f921c: d0 = 0.400000
    //     0x14f921c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14f9220: r0 = withOpacity()
    //     0x14f9220: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f9224: r16 = 12.000000
    //     0x14f9224: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f9228: ldr             x16, [x16, #0x9e8]
    // 0x14f922c: stp             x16, x0, [SP]
    // 0x14f9230: ldur            x1, [fp, #-0x30]
    // 0x14f9234: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14f9234: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14f9238: ldr             x4, [x4, #0x9b8]
    // 0x14f923c: r0 = copyWith()
    //     0x14f923c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9240: stur            x0, [fp, #-0x30]
    // 0x14f9244: r0 = Text()
    //     0x14f9244: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9248: mov             x1, x0
    // 0x14f924c: ldur            x0, [fp, #-0x20]
    // 0x14f9250: stur            x1, [fp, #-0x38]
    // 0x14f9254: StoreField: r1->field_b = r0
    //     0x14f9254: stur            w0, [x1, #0xb]
    // 0x14f9258: ldur            x0, [fp, #-0x30]
    // 0x14f925c: StoreField: r1->field_13 = r0
    //     0x14f925c: stur            w0, [x1, #0x13]
    // 0x14f9260: r0 = Padding()
    //     0x14f9260: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f9264: mov             x2, x0
    // 0x14f9268: r0 = Instance_EdgeInsets
    //     0x14f9268: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f320] Obj!EdgeInsets@d5a021
    //     0x14f926c: ldr             x0, [x0, #0x320]
    // 0x14f9270: stur            x2, [fp, #-0x20]
    // 0x14f9274: StoreField: r2->field_f = r0
    //     0x14f9274: stur            w0, [x2, #0xf]
    // 0x14f9278: ldur            x0, [fp, #-0x38]
    // 0x14f927c: StoreField: r2->field_b = r0
    //     0x14f927c: stur            w0, [x2, #0xb]
    // 0x14f9280: ldur            x0, [fp, #-8]
    // 0x14f9284: LoadField: r1 = r0->field_f
    //     0x14f9284: ldur            w1, [x0, #0xf]
    // 0x14f9288: DecompressPointer r1
    //     0x14f9288: add             x1, x1, HEAP, lsl #32
    // 0x14f928c: r0 = controller()
    //     0x14f928c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f9290: LoadField: r1 = r0->field_57
    //     0x14f9290: ldur            w1, [x0, #0x57]
    // 0x14f9294: DecompressPointer r1
    //     0x14f9294: add             x1, x1, HEAP, lsl #32
    // 0x14f9298: r0 = value()
    //     0x14f9298: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f929c: cmp             w0, NULL
    // 0x14f92a0: b.ne            #0x14f92ac
    // 0x14f92a4: r6 = Null
    //     0x14f92a4: mov             x6, NULL
    // 0x14f92a8: b               #0x14f92bc
    // 0x14f92ac: LoadField: r1 = r0->field_2b
    //     0x14f92ac: ldur            w1, [x0, #0x2b]
    // 0x14f92b0: DecompressPointer r1
    //     0x14f92b0: add             x1, x1, HEAP, lsl #32
    // 0x14f92b4: LoadField: r0 = r1->field_b
    //     0x14f92b4: ldur            w0, [x1, #0xb]
    // 0x14f92b8: mov             x6, x0
    // 0x14f92bc: ldur            x3, [fp, #-8]
    // 0x14f92c0: ldur            x5, [fp, #-0x10]
    // 0x14f92c4: ldur            x4, [fp, #-0x28]
    // 0x14f92c8: ldur            x0, [fp, #-0x20]
    // 0x14f92cc: mov             x2, x3
    // 0x14f92d0: stur            x6, [fp, #-0x30]
    // 0x14f92d4: r1 = Function '<anonymous closure>':.
    //     0x14f92d4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f328] AnonymousClosure: (0x14fa34c), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x14f8554)
    //     0x14f92d8: ldr             x1, [x1, #0x328]
    // 0x14f92dc: r0 = AllocateClosure()
    //     0x14f92dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f92e0: stur            x0, [fp, #-0x38]
    // 0x14f92e4: r0 = ListView()
    //     0x14f92e4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14f92e8: stur            x0, [fp, #-0x40]
    // 0x14f92ec: r16 = Instance_NeverScrollableScrollPhysics
    //     0x14f92ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14f92f0: ldr             x16, [x16, #0x1c8]
    // 0x14f92f4: r30 = true
    //     0x14f92f4: add             lr, NULL, #0x20  ; true
    // 0x14f92f8: stp             lr, x16, [SP, #8]
    // 0x14f92fc: r16 = true
    //     0x14f92fc: add             x16, NULL, #0x20  ; true
    // 0x14f9300: str             x16, [SP]
    // 0x14f9304: mov             x1, x0
    // 0x14f9308: ldur            x2, [fp, #-0x38]
    // 0x14f930c: ldur            x3, [fp, #-0x30]
    // 0x14f9310: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x3, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x14f9310: add             x4, PP, #0x32, lsl #12  ; [pp+0x32058] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x3, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14f9314: ldr             x4, [x4, #0x58]
    // 0x14f9318: r0 = ListView.builder()
    //     0x14f9318: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14f931c: ldur            x2, [fp, #-8]
    // 0x14f9320: LoadField: r1 = r2->field_13
    //     0x14f9320: ldur            w1, [x2, #0x13]
    // 0x14f9324: DecompressPointer r1
    //     0x14f9324: add             x1, x1, HEAP, lsl #32
    // 0x14f9328: r0 = of()
    //     0x14f9328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f932c: LoadField: r1 = r0->field_87
    //     0x14f932c: ldur            w1, [x0, #0x87]
    // 0x14f9330: DecompressPointer r1
    //     0x14f9330: add             x1, x1, HEAP, lsl #32
    // 0x14f9334: LoadField: r0 = r1->field_7
    //     0x14f9334: ldur            w0, [x1, #7]
    // 0x14f9338: DecompressPointer r0
    //     0x14f9338: add             x0, x0, HEAP, lsl #32
    // 0x14f933c: r16 = 14.000000
    //     0x14f933c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14f9340: ldr             x16, [x16, #0x1d8]
    // 0x14f9344: str             x16, [SP]
    // 0x14f9348: mov             x1, x0
    // 0x14f934c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14f934c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14f9350: ldr             x4, [x4, #0x798]
    // 0x14f9354: r0 = copyWith()
    //     0x14f9354: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9358: stur            x0, [fp, #-0x30]
    // 0x14f935c: r0 = TextSpan()
    //     0x14f935c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14f9360: mov             x2, x0
    // 0x14f9364: r0 = "Remark"
    //     0x14f9364: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a0] "Remark"
    //     0x14f9368: ldr             x0, [x0, #0x7a0]
    // 0x14f936c: stur            x2, [fp, #-0x38]
    // 0x14f9370: StoreField: r2->field_b = r0
    //     0x14f9370: stur            w0, [x2, #0xb]
    // 0x14f9374: r0 = Instance__DeferringMouseCursor
    //     0x14f9374: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14f9378: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f9378: stur            w0, [x2, #0x17]
    // 0x14f937c: ldur            x1, [fp, #-0x30]
    // 0x14f9380: StoreField: r2->field_7 = r1
    //     0x14f9380: stur            w1, [x2, #7]
    // 0x14f9384: ldur            x3, [fp, #-8]
    // 0x14f9388: LoadField: r1 = r3->field_13
    //     0x14f9388: ldur            w1, [x3, #0x13]
    // 0x14f938c: DecompressPointer r1
    //     0x14f938c: add             x1, x1, HEAP, lsl #32
    // 0x14f9390: r0 = of()
    //     0x14f9390: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9394: LoadField: r1 = r0->field_87
    //     0x14f9394: ldur            w1, [x0, #0x87]
    // 0x14f9398: DecompressPointer r1
    //     0x14f9398: add             x1, x1, HEAP, lsl #32
    // 0x14f939c: LoadField: r0 = r1->field_2b
    //     0x14f939c: ldur            w0, [x1, #0x2b]
    // 0x14f93a0: DecompressPointer r0
    //     0x14f93a0: add             x0, x0, HEAP, lsl #32
    // 0x14f93a4: r16 = 14.000000
    //     0x14f93a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14f93a8: ldr             x16, [x16, #0x1d8]
    // 0x14f93ac: r30 = Instance_Color
    //     0x14f93ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14f93b0: ldr             lr, [lr, #0x50]
    // 0x14f93b4: stp             lr, x16, [SP]
    // 0x14f93b8: mov             x1, x0
    // 0x14f93bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f93bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f93c0: ldr             x4, [x4, #0xaa0]
    // 0x14f93c4: r0 = copyWith()
    //     0x14f93c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f93c8: stur            x0, [fp, #-0x30]
    // 0x14f93cc: r0 = TextSpan()
    //     0x14f93cc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14f93d0: mov             x3, x0
    // 0x14f93d4: r0 = "*"
    //     0x14f93d4: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "*"
    //     0x14f93d8: ldr             x0, [x0, #0x7a8]
    // 0x14f93dc: stur            x3, [fp, #-0x48]
    // 0x14f93e0: StoreField: r3->field_b = r0
    //     0x14f93e0: stur            w0, [x3, #0xb]
    // 0x14f93e4: r0 = Instance__DeferringMouseCursor
    //     0x14f93e4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14f93e8: ArrayStore: r3[0] = r0  ; List_4
    //     0x14f93e8: stur            w0, [x3, #0x17]
    // 0x14f93ec: ldur            x1, [fp, #-0x30]
    // 0x14f93f0: StoreField: r3->field_7 = r1
    //     0x14f93f0: stur            w1, [x3, #7]
    // 0x14f93f4: r1 = Null
    //     0x14f93f4: mov             x1, NULL
    // 0x14f93f8: r2 = 4
    //     0x14f93f8: movz            x2, #0x4
    // 0x14f93fc: r0 = AllocateArray()
    //     0x14f93fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f9400: mov             x2, x0
    // 0x14f9404: ldur            x0, [fp, #-0x38]
    // 0x14f9408: stur            x2, [fp, #-0x30]
    // 0x14f940c: StoreField: r2->field_f = r0
    //     0x14f940c: stur            w0, [x2, #0xf]
    // 0x14f9410: ldur            x0, [fp, #-0x48]
    // 0x14f9414: StoreField: r2->field_13 = r0
    //     0x14f9414: stur            w0, [x2, #0x13]
    // 0x14f9418: r1 = <InlineSpan>
    //     0x14f9418: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14f941c: ldr             x1, [x1, #0xe40]
    // 0x14f9420: r0 = AllocateGrowableArray()
    //     0x14f9420: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f9424: mov             x1, x0
    // 0x14f9428: ldur            x0, [fp, #-0x30]
    // 0x14f942c: stur            x1, [fp, #-0x38]
    // 0x14f9430: StoreField: r1->field_f = r0
    //     0x14f9430: stur            w0, [x1, #0xf]
    // 0x14f9434: r2 = 4
    //     0x14f9434: movz            x2, #0x4
    // 0x14f9438: StoreField: r1->field_b = r2
    //     0x14f9438: stur            w2, [x1, #0xb]
    // 0x14f943c: r0 = TextSpan()
    //     0x14f943c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14f9440: mov             x1, x0
    // 0x14f9444: ldur            x0, [fp, #-0x38]
    // 0x14f9448: stur            x1, [fp, #-0x30]
    // 0x14f944c: StoreField: r1->field_f = r0
    //     0x14f944c: stur            w0, [x1, #0xf]
    // 0x14f9450: r0 = Instance__DeferringMouseCursor
    //     0x14f9450: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14f9454: ArrayStore: r1[0] = r0  ; List_4
    //     0x14f9454: stur            w0, [x1, #0x17]
    // 0x14f9458: r0 = RichText()
    //     0x14f9458: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14f945c: mov             x1, x0
    // 0x14f9460: ldur            x2, [fp, #-0x30]
    // 0x14f9464: stur            x0, [fp, #-0x30]
    // 0x14f9468: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14f9468: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14f946c: r0 = RichText()
    //     0x14f946c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14f9470: ldur            x2, [fp, #-8]
    // 0x14f9474: LoadField: r1 = r2->field_f
    //     0x14f9474: ldur            w1, [x2, #0xf]
    // 0x14f9478: DecompressPointer r1
    //     0x14f9478: add             x1, x1, HEAP, lsl #32
    // 0x14f947c: r0 = controller()
    //     0x14f947c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f9480: LoadField: r2 = r0->field_4f
    //     0x14f9480: ldur            w2, [x0, #0x4f]
    // 0x14f9484: DecompressPointer r2
    //     0x14f9484: add             x2, x2, HEAP, lsl #32
    // 0x14f9488: ldur            x0, [fp, #-8]
    // 0x14f948c: stur            x2, [fp, #-0x38]
    // 0x14f9490: LoadField: r1 = r0->field_f
    //     0x14f9490: ldur            w1, [x0, #0xf]
    // 0x14f9494: DecompressPointer r1
    //     0x14f9494: add             x1, x1, HEAP, lsl #32
    // 0x14f9498: r0 = controller()
    //     0x14f9498: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f949c: stur            x0, [fp, #-0x48]
    // 0x14f94a0: r0 = LengthLimitingTextInputFormatter()
    //     0x14f94a0: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14f94a4: mov             x3, x0
    // 0x14f94a8: r0 = 300
    //     0x14f94a8: movz            x0, #0x12c
    // 0x14f94ac: stur            x3, [fp, #-0x50]
    // 0x14f94b0: StoreField: r3->field_7 = r0
    //     0x14f94b0: stur            w0, [x3, #7]
    // 0x14f94b4: r1 = Null
    //     0x14f94b4: mov             x1, NULL
    // 0x14f94b8: r2 = 2
    //     0x14f94b8: movz            x2, #0x2
    // 0x14f94bc: r0 = AllocateArray()
    //     0x14f94bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f94c0: mov             x2, x0
    // 0x14f94c4: ldur            x0, [fp, #-0x50]
    // 0x14f94c8: stur            x2, [fp, #-0x58]
    // 0x14f94cc: StoreField: r2->field_f = r0
    //     0x14f94cc: stur            w0, [x2, #0xf]
    // 0x14f94d0: r1 = <TextInputFormatter>
    //     0x14f94d0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14f94d4: ldr             x1, [x1, #0x7b0]
    // 0x14f94d8: r0 = AllocateGrowableArray()
    //     0x14f94d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f94dc: mov             x2, x0
    // 0x14f94e0: ldur            x0, [fp, #-0x58]
    // 0x14f94e4: stur            x2, [fp, #-0x50]
    // 0x14f94e8: StoreField: r2->field_f = r0
    //     0x14f94e8: stur            w0, [x2, #0xf]
    // 0x14f94ec: r0 = 2
    //     0x14f94ec: movz            x0, #0x2
    // 0x14f94f0: StoreField: r2->field_b = r0
    //     0x14f94f0: stur            w0, [x2, #0xb]
    // 0x14f94f4: ldur            x0, [fp, #-8]
    // 0x14f94f8: LoadField: r1 = r0->field_13
    //     0x14f94f8: ldur            w1, [x0, #0x13]
    // 0x14f94fc: DecompressPointer r1
    //     0x14f94fc: add             x1, x1, HEAP, lsl #32
    // 0x14f9500: r0 = of()
    //     0x14f9500: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9504: LoadField: r1 = r0->field_87
    //     0x14f9504: ldur            w1, [x0, #0x87]
    // 0x14f9508: DecompressPointer r1
    //     0x14f9508: add             x1, x1, HEAP, lsl #32
    // 0x14f950c: LoadField: r0 = r1->field_2b
    //     0x14f950c: ldur            w0, [x1, #0x2b]
    // 0x14f9510: DecompressPointer r0
    //     0x14f9510: add             x0, x0, HEAP, lsl #32
    // 0x14f9514: ldur            x2, [fp, #-8]
    // 0x14f9518: stur            x0, [fp, #-0x58]
    // 0x14f951c: LoadField: r1 = r2->field_13
    //     0x14f951c: ldur            w1, [x2, #0x13]
    // 0x14f9520: DecompressPointer r1
    //     0x14f9520: add             x1, x1, HEAP, lsl #32
    // 0x14f9524: r0 = of()
    //     0x14f9524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9528: LoadField: r1 = r0->field_5b
    //     0x14f9528: ldur            w1, [x0, #0x5b]
    // 0x14f952c: DecompressPointer r1
    //     0x14f952c: add             x1, x1, HEAP, lsl #32
    // 0x14f9530: r16 = 12.000000
    //     0x14f9530: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f9534: ldr             x16, [x16, #0x9e8]
    // 0x14f9538: stp             x16, x1, [SP]
    // 0x14f953c: ldur            x1, [fp, #-0x58]
    // 0x14f9540: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14f9540: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14f9544: ldr             x4, [x4, #0x9b8]
    // 0x14f9548: r0 = copyWith()
    //     0x14f9548: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f954c: ldur            x2, [fp, #-8]
    // 0x14f9550: stur            x0, [fp, #-0x58]
    // 0x14f9554: LoadField: r1 = r2->field_f
    //     0x14f9554: ldur            w1, [x2, #0xf]
    // 0x14f9558: DecompressPointer r1
    //     0x14f9558: add             x1, x1, HEAP, lsl #32
    // 0x14f955c: r0 = controller()
    //     0x14f955c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f9560: LoadField: r2 = r0->field_53
    //     0x14f9560: ldur            w2, [x0, #0x53]
    // 0x14f9564: DecompressPointer r2
    //     0x14f9564: add             x2, x2, HEAP, lsl #32
    // 0x14f9568: ldur            x0, [fp, #-8]
    // 0x14f956c: stur            x2, [fp, #-0x60]
    // 0x14f9570: LoadField: r1 = r0->field_13
    //     0x14f9570: ldur            w1, [x0, #0x13]
    // 0x14f9574: DecompressPointer r1
    //     0x14f9574: add             x1, x1, HEAP, lsl #32
    // 0x14f9578: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0x14f9578: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0x14f957c: ldur            x2, [fp, #-8]
    // 0x14f9580: stur            x0, [fp, #-0x68]
    // 0x14f9584: LoadField: r1 = r2->field_13
    //     0x14f9584: ldur            w1, [x2, #0x13]
    // 0x14f9588: DecompressPointer r1
    //     0x14f9588: add             x1, x1, HEAP, lsl #32
    // 0x14f958c: r0 = of()
    //     0x14f958c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9590: LoadField: r1 = r0->field_87
    //     0x14f9590: ldur            w1, [x0, #0x87]
    // 0x14f9594: DecompressPointer r1
    //     0x14f9594: add             x1, x1, HEAP, lsl #32
    // 0x14f9598: LoadField: r0 = r1->field_2b
    //     0x14f9598: ldur            w0, [x1, #0x2b]
    // 0x14f959c: DecompressPointer r0
    //     0x14f959c: add             x0, x0, HEAP, lsl #32
    // 0x14f95a0: stur            x0, [fp, #-0x70]
    // 0x14f95a4: r1 = Instance_Color
    //     0x14f95a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f95a8: d0 = 0.400000
    //     0x14f95a8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14f95ac: r0 = withOpacity()
    //     0x14f95ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f95b0: r16 = 12.000000
    //     0x14f95b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f95b4: ldr             x16, [x16, #0x9e8]
    // 0x14f95b8: stp             x16, x0, [SP]
    // 0x14f95bc: ldur            x1, [fp, #-0x70]
    // 0x14f95c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14f95c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14f95c4: ldr             x4, [x4, #0x9b8]
    // 0x14f95c8: r0 = copyWith()
    //     0x14f95c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f95cc: ldur            x2, [fp, #-8]
    // 0x14f95d0: stur            x0, [fp, #-0x70]
    // 0x14f95d4: LoadField: r1 = r2->field_13
    //     0x14f95d4: ldur            w1, [x2, #0x13]
    // 0x14f95d8: DecompressPointer r1
    //     0x14f95d8: add             x1, x1, HEAP, lsl #32
    // 0x14f95dc: r0 = of()
    //     0x14f95dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f95e0: LoadField: r1 = r0->field_87
    //     0x14f95e0: ldur            w1, [x0, #0x87]
    // 0x14f95e4: DecompressPointer r1
    //     0x14f95e4: add             x1, x1, HEAP, lsl #32
    // 0x14f95e8: LoadField: r0 = r1->field_2b
    //     0x14f95e8: ldur            w0, [x1, #0x2b]
    // 0x14f95ec: DecompressPointer r0
    //     0x14f95ec: add             x0, x0, HEAP, lsl #32
    // 0x14f95f0: r16 = Instance_Color
    //     0x14f95f0: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x14f95f4: ldr             x16, [x16, #0x7c0]
    // 0x14f95f8: r30 = 12.000000
    //     0x14f95f8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f95fc: ldr             lr, [lr, #0x9e8]
    // 0x14f9600: stp             lr, x16, [SP]
    // 0x14f9604: mov             x1, x0
    // 0x14f9608: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14f9608: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14f960c: ldr             x4, [x4, #0x9b8]
    // 0x14f9610: r0 = copyWith()
    //     0x14f9610: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9614: r16 = "Describe your issue in detail"
    //     0x14f9614: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c8] "Describe your issue in detail"
    //     0x14f9618: ldr             x16, [x16, #0x7c8]
    // 0x14f961c: ldur            lr, [fp, #-0x70]
    // 0x14f9620: stp             lr, x16, [SP, #8]
    // 0x14f9624: str             x0, [SP]
    // 0x14f9628: ldur            x1, [fp, #-0x68]
    // 0x14f962c: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0x14f962c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0x14f9630: ldr             x4, [x4, #0xfe8]
    // 0x14f9634: r0 = copyWith()
    //     0x14f9634: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x14f9638: ldur            x2, [fp, #-0x48]
    // 0x14f963c: r1 = Function 'validateRemark':.
    //     0x14f963c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337d8] AnonymousClosure: (0x1449034), in [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::validateRemark (0x1447158)
    //     0x14f9640: ldr             x1, [x1, #0x7d8]
    // 0x14f9644: stur            x0, [fp, #-0x48]
    // 0x14f9648: r0 = AllocateClosure()
    //     0x14f9648: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f964c: ldur            x2, [fp, #-8]
    // 0x14f9650: r1 = Function '<anonymous closure>':.
    //     0x14f9650: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f330] AnonymousClosure: (0x14454e0), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x1509194)
    //     0x14f9654: ldr             x1, [x1, #0x330]
    // 0x14f9658: stur            x0, [fp, #-0x68]
    // 0x14f965c: r0 = AllocateClosure()
    //     0x14f965c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f9660: r1 = <String>
    //     0x14f9660: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14f9664: stur            x0, [fp, #-0x70]
    // 0x14f9668: r0 = TextFormField()
    //     0x14f9668: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x14f966c: stur            x0, [fp, #-0x78]
    // 0x14f9670: ldur            x16, [fp, #-0x68]
    // 0x14f9674: r30 = false
    //     0x14f9674: add             lr, NULL, #0x30  ; false
    // 0x14f9678: stp             lr, x16, [SP, #0x40]
    // 0x14f967c: r16 = Instance_AutovalidateMode
    //     0x14f967c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14f9680: ldr             x16, [x16, #0x7e8]
    // 0x14f9684: ldur            lr, [fp, #-0x50]
    // 0x14f9688: stp             lr, x16, [SP, #0x30]
    // 0x14f968c: r16 = Instance_TextInputType
    //     0x14f968c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x14f9690: ldr             x16, [x16, #0x7f0]
    // 0x14f9694: r30 = 6
    //     0x14f9694: movz            lr, #0x6
    // 0x14f9698: stp             lr, x16, [SP, #0x20]
    // 0x14f969c: r16 = 10
    //     0x14f969c: movz            x16, #0xa
    // 0x14f96a0: ldur            lr, [fp, #-0x58]
    // 0x14f96a4: stp             lr, x16, [SP, #0x10]
    // 0x14f96a8: ldur            x16, [fp, #-0x60]
    // 0x14f96ac: ldur            lr, [fp, #-0x70]
    // 0x14f96b0: stp             lr, x16, [SP]
    // 0x14f96b4: mov             x1, x0
    // 0x14f96b8: ldur            x2, [fp, #-0x48]
    // 0x14f96bc: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x4, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, validator, 0x2, null]
    //     0x14f96bc: add             x4, PP, #0x33, lsl #12  ; [pp+0x337f8] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x4, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "validator", 0x2, Null]
    //     0x14f96c0: ldr             x4, [x4, #0x7f8]
    // 0x14f96c4: r0 = TextFormField()
    //     0x14f96c4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14f96c8: r0 = Form()
    //     0x14f96c8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14f96cc: mov             x1, x0
    // 0x14f96d0: ldur            x0, [fp, #-0x78]
    // 0x14f96d4: stur            x1, [fp, #-0x48]
    // 0x14f96d8: StoreField: r1->field_b = r0
    //     0x14f96d8: stur            w0, [x1, #0xb]
    // 0x14f96dc: r0 = Instance_AutovalidateMode
    //     0x14f96dc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14f96e0: ldr             x0, [x0, #0x800]
    // 0x14f96e4: StoreField: r1->field_23 = r0
    //     0x14f96e4: stur            w0, [x1, #0x23]
    // 0x14f96e8: ldur            x0, [fp, #-0x38]
    // 0x14f96ec: StoreField: r1->field_7 = r0
    //     0x14f96ec: stur            w0, [x1, #7]
    // 0x14f96f0: r0 = Padding()
    //     0x14f96f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f96f4: mov             x3, x0
    // 0x14f96f8: r0 = Instance_EdgeInsets
    //     0x14f96f8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x14f96fc: ldr             x0, [x0, #0x868]
    // 0x14f9700: stur            x3, [fp, #-0x38]
    // 0x14f9704: StoreField: r3->field_f = r0
    //     0x14f9704: stur            w0, [x3, #0xf]
    // 0x14f9708: ldur            x0, [fp, #-0x48]
    // 0x14f970c: StoreField: r3->field_b = r0
    //     0x14f970c: stur            w0, [x3, #0xb]
    // 0x14f9710: r1 = Null
    //     0x14f9710: mov             x1, NULL
    // 0x14f9714: r2 = 10
    //     0x14f9714: movz            x2, #0xa
    // 0x14f9718: r0 = AllocateArray()
    //     0x14f9718: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f971c: mov             x2, x0
    // 0x14f9720: ldur            x0, [fp, #-0x28]
    // 0x14f9724: stur            x2, [fp, #-0x48]
    // 0x14f9728: StoreField: r2->field_f = r0
    //     0x14f9728: stur            w0, [x2, #0xf]
    // 0x14f972c: ldur            x0, [fp, #-0x20]
    // 0x14f9730: StoreField: r2->field_13 = r0
    //     0x14f9730: stur            w0, [x2, #0x13]
    // 0x14f9734: ldur            x0, [fp, #-0x40]
    // 0x14f9738: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f9738: stur            w0, [x2, #0x17]
    // 0x14f973c: ldur            x0, [fp, #-0x30]
    // 0x14f9740: StoreField: r2->field_1b = r0
    //     0x14f9740: stur            w0, [x2, #0x1b]
    // 0x14f9744: ldur            x0, [fp, #-0x38]
    // 0x14f9748: StoreField: r2->field_1f = r0
    //     0x14f9748: stur            w0, [x2, #0x1f]
    // 0x14f974c: r1 = <Widget>
    //     0x14f974c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14f9750: r0 = AllocateGrowableArray()
    //     0x14f9750: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f9754: mov             x1, x0
    // 0x14f9758: ldur            x0, [fp, #-0x48]
    // 0x14f975c: stur            x1, [fp, #-0x20]
    // 0x14f9760: StoreField: r1->field_f = r0
    //     0x14f9760: stur            w0, [x1, #0xf]
    // 0x14f9764: r0 = 10
    //     0x14f9764: movz            x0, #0xa
    // 0x14f9768: StoreField: r1->field_b = r0
    //     0x14f9768: stur            w0, [x1, #0xb]
    // 0x14f976c: r0 = Column()
    //     0x14f976c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14f9770: mov             x1, x0
    // 0x14f9774: r0 = Instance_Axis
    //     0x14f9774: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14f9778: stur            x1, [fp, #-0x28]
    // 0x14f977c: StoreField: r1->field_f = r0
    //     0x14f977c: stur            w0, [x1, #0xf]
    // 0x14f9780: r2 = Instance_MainAxisAlignment
    //     0x14f9780: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14f9784: ldr             x2, [x2, #0xa08]
    // 0x14f9788: StoreField: r1->field_13 = r2
    //     0x14f9788: stur            w2, [x1, #0x13]
    // 0x14f978c: r3 = Instance_MainAxisSize
    //     0x14f978c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14f9790: ldr             x3, [x3, #0xa10]
    // 0x14f9794: ArrayStore: r1[0] = r3  ; List_4
    //     0x14f9794: stur            w3, [x1, #0x17]
    // 0x14f9798: r4 = Instance_CrossAxisAlignment
    //     0x14f9798: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14f979c: ldr             x4, [x4, #0x890]
    // 0x14f97a0: StoreField: r1->field_1b = r4
    //     0x14f97a0: stur            w4, [x1, #0x1b]
    // 0x14f97a4: r5 = Instance_VerticalDirection
    //     0x14f97a4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14f97a8: ldr             x5, [x5, #0xa20]
    // 0x14f97ac: StoreField: r1->field_23 = r5
    //     0x14f97ac: stur            w5, [x1, #0x23]
    // 0x14f97b0: r6 = Instance_Clip
    //     0x14f97b0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14f97b4: ldr             x6, [x6, #0x38]
    // 0x14f97b8: StoreField: r1->field_2b = r6
    //     0x14f97b8: stur            w6, [x1, #0x2b]
    // 0x14f97bc: StoreField: r1->field_2f = rZR
    //     0x14f97bc: stur            xzr, [x1, #0x2f]
    // 0x14f97c0: ldur            x7, [fp, #-0x20]
    // 0x14f97c4: StoreField: r1->field_b = r7
    //     0x14f97c4: stur            w7, [x1, #0xb]
    // 0x14f97c8: r0 = Padding()
    //     0x14f97c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f97cc: mov             x1, x0
    // 0x14f97d0: r0 = Instance_EdgeInsets
    //     0x14f97d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14f97d4: ldr             x0, [x0, #0x1f0]
    // 0x14f97d8: stur            x1, [fp, #-0x20]
    // 0x14f97dc: StoreField: r1->field_f = r0
    //     0x14f97dc: stur            w0, [x1, #0xf]
    // 0x14f97e0: ldur            x2, [fp, #-0x28]
    // 0x14f97e4: StoreField: r1->field_b = r2
    //     0x14f97e4: stur            w2, [x1, #0xb]
    // 0x14f97e8: r0 = Card()
    //     0x14f97e8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14f97ec: mov             x2, x0
    // 0x14f97f0: r0 = 0.000000
    //     0x14f97f0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14f97f4: stur            x2, [fp, #-0x28]
    // 0x14f97f8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f97f8: stur            w0, [x2, #0x17]
    // 0x14f97fc: ldur            x1, [fp, #-0x10]
    // 0x14f9800: StoreField: r2->field_1b = r1
    //     0x14f9800: stur            w1, [x2, #0x1b]
    // 0x14f9804: r3 = true
    //     0x14f9804: add             x3, NULL, #0x20  ; true
    // 0x14f9808: StoreField: r2->field_1f = r3
    //     0x14f9808: stur            w3, [x2, #0x1f]
    // 0x14f980c: ldur            x1, [fp, #-0x20]
    // 0x14f9810: StoreField: r2->field_2f = r1
    //     0x14f9810: stur            w1, [x2, #0x2f]
    // 0x14f9814: StoreField: r2->field_2b = r3
    //     0x14f9814: stur            w3, [x2, #0x2b]
    // 0x14f9818: r4 = Instance__CardVariant
    //     0x14f9818: add             x4, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14f981c: ldr             x4, [x4, #0xa68]
    // 0x14f9820: StoreField: r2->field_33 = r4
    //     0x14f9820: stur            w4, [x2, #0x33]
    // 0x14f9824: ldur            x5, [fp, #-8]
    // 0x14f9828: LoadField: r1 = r5->field_f
    //     0x14f9828: ldur            w1, [x5, #0xf]
    // 0x14f982c: DecompressPointer r1
    //     0x14f982c: add             x1, x1, HEAP, lsl #32
    // 0x14f9830: r0 = controller()
    //     0x14f9830: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f9834: LoadField: r1 = r0->field_57
    //     0x14f9834: ldur            w1, [x0, #0x57]
    // 0x14f9838: DecompressPointer r1
    //     0x14f9838: add             x1, x1, HEAP, lsl #32
    // 0x14f983c: r0 = value()
    //     0x14f983c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f9840: cmp             w0, NULL
    // 0x14f9844: b.ne            #0x14f9850
    // 0x14f9848: r0 = Null
    //     0x14f9848: mov             x0, NULL
    // 0x14f984c: b               #0x14f985c
    // 0x14f9850: LoadField: r1 = r0->field_23
    //     0x14f9850: ldur            w1, [x0, #0x23]
    // 0x14f9854: DecompressPointer r1
    //     0x14f9854: add             x1, x1, HEAP, lsl #32
    // 0x14f9858: mov             x0, x1
    // 0x14f985c: cmp             w0, NULL
    // 0x14f9860: b.ne            #0x14f986c
    // 0x14f9864: r1 = false
    //     0x14f9864: add             x1, NULL, #0x30  ; false
    // 0x14f9868: b               #0x14f9870
    // 0x14f986c: mov             x1, x0
    // 0x14f9870: ldur            x0, [fp, #-8]
    // 0x14f9874: stur            x1, [fp, #-0x10]
    // 0x14f9878: r0 = Radius()
    //     0x14f9878: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f987c: d0 = 20.000000
    //     0x14f987c: fmov            d0, #20.00000000
    // 0x14f9880: stur            x0, [fp, #-0x20]
    // 0x14f9884: StoreField: r0->field_7 = d0
    //     0x14f9884: stur            d0, [x0, #7]
    // 0x14f9888: StoreField: r0->field_f = d0
    //     0x14f9888: stur            d0, [x0, #0xf]
    // 0x14f988c: r0 = BorderRadius()
    //     0x14f988c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f9890: mov             x1, x0
    // 0x14f9894: ldur            x0, [fp, #-0x20]
    // 0x14f9898: stur            x1, [fp, #-0x30]
    // 0x14f989c: StoreField: r1->field_7 = r0
    //     0x14f989c: stur            w0, [x1, #7]
    // 0x14f98a0: StoreField: r1->field_b = r0
    //     0x14f98a0: stur            w0, [x1, #0xb]
    // 0x14f98a4: StoreField: r1->field_f = r0
    //     0x14f98a4: stur            w0, [x1, #0xf]
    // 0x14f98a8: StoreField: r1->field_13 = r0
    //     0x14f98a8: stur            w0, [x1, #0x13]
    // 0x14f98ac: r0 = RoundedRectangleBorder()
    //     0x14f98ac: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14f98b0: mov             x2, x0
    // 0x14f98b4: ldur            x0, [fp, #-0x30]
    // 0x14f98b8: stur            x2, [fp, #-0x20]
    // 0x14f98bc: StoreField: r2->field_b = r0
    //     0x14f98bc: stur            w0, [x2, #0xb]
    // 0x14f98c0: r0 = Instance_BorderSide
    //     0x14f98c0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14f98c4: ldr             x0, [x0, #0xe20]
    // 0x14f98c8: StoreField: r2->field_7 = r0
    //     0x14f98c8: stur            w0, [x2, #7]
    // 0x14f98cc: ldur            x3, [fp, #-8]
    // 0x14f98d0: LoadField: r1 = r3->field_f
    //     0x14f98d0: ldur            w1, [x3, #0xf]
    // 0x14f98d4: DecompressPointer r1
    //     0x14f98d4: add             x1, x1, HEAP, lsl #32
    // 0x14f98d8: r0 = controller()
    //     0x14f98d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f98dc: LoadField: r1 = r0->field_57
    //     0x14f98dc: ldur            w1, [x0, #0x57]
    // 0x14f98e0: DecompressPointer r1
    //     0x14f98e0: add             x1, x1, HEAP, lsl #32
    // 0x14f98e4: r0 = value()
    //     0x14f98e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f98e8: cmp             w0, NULL
    // 0x14f98ec: b.ne            #0x14f98f8
    // 0x14f98f0: r0 = Null
    //     0x14f98f0: mov             x0, NULL
    // 0x14f98f4: b               #0x14f9918
    // 0x14f98f8: LoadField: r1 = r0->field_13
    //     0x14f98f8: ldur            w1, [x0, #0x13]
    // 0x14f98fc: DecompressPointer r1
    //     0x14f98fc: add             x1, x1, HEAP, lsl #32
    // 0x14f9900: cmp             w1, NULL
    // 0x14f9904: b.ne            #0x14f9910
    // 0x14f9908: r0 = Null
    //     0x14f9908: mov             x0, NULL
    // 0x14f990c: b               #0x14f9918
    // 0x14f9910: LoadField: r0 = r1->field_7
    //     0x14f9910: ldur            w0, [x1, #7]
    // 0x14f9914: DecompressPointer r0
    //     0x14f9914: add             x0, x0, HEAP, lsl #32
    // 0x14f9918: cmp             w0, NULL
    // 0x14f991c: b.ne            #0x14f9928
    // 0x14f9920: r2 = ""
    //     0x14f9920: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f9924: b               #0x14f992c
    // 0x14f9928: mov             x2, x0
    // 0x14f992c: ldur            x0, [fp, #-8]
    // 0x14f9930: stur            x2, [fp, #-0x30]
    // 0x14f9934: LoadField: r1 = r0->field_13
    //     0x14f9934: ldur            w1, [x0, #0x13]
    // 0x14f9938: DecompressPointer r1
    //     0x14f9938: add             x1, x1, HEAP, lsl #32
    // 0x14f993c: r0 = of()
    //     0x14f993c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9940: LoadField: r1 = r0->field_87
    //     0x14f9940: ldur            w1, [x0, #0x87]
    // 0x14f9944: DecompressPointer r1
    //     0x14f9944: add             x1, x1, HEAP, lsl #32
    // 0x14f9948: LoadField: r0 = r1->field_7
    //     0x14f9948: ldur            w0, [x1, #7]
    // 0x14f994c: DecompressPointer r0
    //     0x14f994c: add             x0, x0, HEAP, lsl #32
    // 0x14f9950: stur            x0, [fp, #-0x38]
    // 0x14f9954: r1 = Instance_Color
    //     0x14f9954: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f9958: d0 = 0.700000
    //     0x14f9958: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f995c: ldr             d0, [x17, #0xf48]
    // 0x14f9960: r0 = withOpacity()
    //     0x14f9960: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f9964: r16 = 14.000000
    //     0x14f9964: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14f9968: ldr             x16, [x16, #0x1d8]
    // 0x14f996c: stp             x0, x16, [SP]
    // 0x14f9970: ldur            x1, [fp, #-0x38]
    // 0x14f9974: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f9974: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f9978: ldr             x4, [x4, #0xaa0]
    // 0x14f997c: r0 = copyWith()
    //     0x14f997c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9980: stur            x0, [fp, #-0x38]
    // 0x14f9984: r0 = Text()
    //     0x14f9984: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9988: mov             x1, x0
    // 0x14f998c: ldur            x0, [fp, #-0x30]
    // 0x14f9990: stur            x1, [fp, #-0x40]
    // 0x14f9994: StoreField: r1->field_b = r0
    //     0x14f9994: stur            w0, [x1, #0xb]
    // 0x14f9998: ldur            x0, [fp, #-0x38]
    // 0x14f999c: StoreField: r1->field_13 = r0
    //     0x14f999c: stur            w0, [x1, #0x13]
    // 0x14f99a0: r0 = Padding()
    //     0x14f99a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f99a4: mov             x2, x0
    // 0x14f99a8: r0 = Instance_EdgeInsets
    //     0x14f99a8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33808] Obj!EdgeInsets@d592d1
    //     0x14f99ac: ldr             x0, [x0, #0x808]
    // 0x14f99b0: stur            x2, [fp, #-0x30]
    // 0x14f99b4: StoreField: r2->field_f = r0
    //     0x14f99b4: stur            w0, [x2, #0xf]
    // 0x14f99b8: ldur            x0, [fp, #-0x40]
    // 0x14f99bc: StoreField: r2->field_b = r0
    //     0x14f99bc: stur            w0, [x2, #0xb]
    // 0x14f99c0: ldur            x0, [fp, #-8]
    // 0x14f99c4: LoadField: r1 = r0->field_f
    //     0x14f99c4: ldur            w1, [x0, #0xf]
    // 0x14f99c8: DecompressPointer r1
    //     0x14f99c8: add             x1, x1, HEAP, lsl #32
    // 0x14f99cc: r0 = controller()
    //     0x14f99cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f99d0: LoadField: r1 = r0->field_57
    //     0x14f99d0: ldur            w1, [x0, #0x57]
    // 0x14f99d4: DecompressPointer r1
    //     0x14f99d4: add             x1, x1, HEAP, lsl #32
    // 0x14f99d8: r0 = value()
    //     0x14f99d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f99dc: cmp             w0, NULL
    // 0x14f99e0: b.ne            #0x14f99ec
    // 0x14f99e4: r0 = Null
    //     0x14f99e4: mov             x0, NULL
    // 0x14f99e8: b               #0x14f9a0c
    // 0x14f99ec: LoadField: r1 = r0->field_13
    //     0x14f99ec: ldur            w1, [x0, #0x13]
    // 0x14f99f0: DecompressPointer r1
    //     0x14f99f0: add             x1, x1, HEAP, lsl #32
    // 0x14f99f4: cmp             w1, NULL
    // 0x14f99f8: b.ne            #0x14f9a04
    // 0x14f99fc: r0 = Null
    //     0x14f99fc: mov             x0, NULL
    // 0x14f9a00: b               #0x14f9a0c
    // 0x14f9a04: LoadField: r0 = r1->field_b
    //     0x14f9a04: ldur            w0, [x1, #0xb]
    // 0x14f9a08: DecompressPointer r0
    //     0x14f9a08: add             x0, x0, HEAP, lsl #32
    // 0x14f9a0c: cmp             w0, NULL
    // 0x14f9a10: b.ne            #0x14f9a1c
    // 0x14f9a14: r2 = ""
    //     0x14f9a14: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f9a18: b               #0x14f9a20
    // 0x14f9a1c: mov             x2, x0
    // 0x14f9a20: ldur            x0, [fp, #-8]
    // 0x14f9a24: stur            x2, [fp, #-0x38]
    // 0x14f9a28: LoadField: r1 = r0->field_13
    //     0x14f9a28: ldur            w1, [x0, #0x13]
    // 0x14f9a2c: DecompressPointer r1
    //     0x14f9a2c: add             x1, x1, HEAP, lsl #32
    // 0x14f9a30: r0 = of()
    //     0x14f9a30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9a34: LoadField: r1 = r0->field_87
    //     0x14f9a34: ldur            w1, [x0, #0x87]
    // 0x14f9a38: DecompressPointer r1
    //     0x14f9a38: add             x1, x1, HEAP, lsl #32
    // 0x14f9a3c: LoadField: r0 = r1->field_2f
    //     0x14f9a3c: ldur            w0, [x1, #0x2f]
    // 0x14f9a40: DecompressPointer r0
    //     0x14f9a40: add             x0, x0, HEAP, lsl #32
    // 0x14f9a44: stur            x0, [fp, #-0x40]
    // 0x14f9a48: cmp             w0, NULL
    // 0x14f9a4c: b.ne            #0x14f9a58
    // 0x14f9a50: r2 = Null
    //     0x14f9a50: mov             x2, NULL
    // 0x14f9a54: b               #0x14f9a88
    // 0x14f9a58: r1 = Instance_Color
    //     0x14f9a58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f9a5c: d0 = 0.700000
    //     0x14f9a5c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f9a60: ldr             d0, [x17, #0xf48]
    // 0x14f9a64: r0 = withOpacity()
    //     0x14f9a64: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f9a68: r16 = 14.000000
    //     0x14f9a68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14f9a6c: ldr             x16, [x16, #0x1d8]
    // 0x14f9a70: stp             x0, x16, [SP]
    // 0x14f9a74: ldur            x1, [fp, #-0x40]
    // 0x14f9a78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f9a78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f9a7c: ldr             x4, [x4, #0xaa0]
    // 0x14f9a80: r0 = copyWith()
    //     0x14f9a80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9a84: mov             x2, x0
    // 0x14f9a88: ldur            x0, [fp, #-8]
    // 0x14f9a8c: ldur            x1, [fp, #-0x38]
    // 0x14f9a90: stur            x2, [fp, #-0x40]
    // 0x14f9a94: r0 = Text()
    //     0x14f9a94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9a98: mov             x1, x0
    // 0x14f9a9c: ldur            x0, [fp, #-0x38]
    // 0x14f9aa0: stur            x1, [fp, #-0x48]
    // 0x14f9aa4: StoreField: r1->field_b = r0
    //     0x14f9aa4: stur            w0, [x1, #0xb]
    // 0x14f9aa8: ldur            x0, [fp, #-0x40]
    // 0x14f9aac: StoreField: r1->field_13 = r0
    //     0x14f9aac: stur            w0, [x1, #0x13]
    // 0x14f9ab0: r0 = Padding()
    //     0x14f9ab0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f9ab4: mov             x2, x0
    // 0x14f9ab8: r0 = Instance_EdgeInsets
    //     0x14f9ab8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33810] Obj!EdgeInsets@d592a1
    //     0x14f9abc: ldr             x0, [x0, #0x810]
    // 0x14f9ac0: stur            x2, [fp, #-0x38]
    // 0x14f9ac4: StoreField: r2->field_f = r0
    //     0x14f9ac4: stur            w0, [x2, #0xf]
    // 0x14f9ac8: ldur            x0, [fp, #-0x48]
    // 0x14f9acc: StoreField: r2->field_b = r0
    //     0x14f9acc: stur            w0, [x2, #0xb]
    // 0x14f9ad0: ldur            x0, [fp, #-8]
    // 0x14f9ad4: LoadField: r1 = r0->field_13
    //     0x14f9ad4: ldur            w1, [x0, #0x13]
    // 0x14f9ad8: DecompressPointer r1
    //     0x14f9ad8: add             x1, x1, HEAP, lsl #32
    // 0x14f9adc: r0 = of()
    //     0x14f9adc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9ae0: LoadField: r1 = r0->field_5b
    //     0x14f9ae0: ldur            w1, [x0, #0x5b]
    // 0x14f9ae4: DecompressPointer r1
    //     0x14f9ae4: add             x1, x1, HEAP, lsl #32
    // 0x14f9ae8: r0 = LoadClassIdInstr(r1)
    //     0x14f9ae8: ldur            x0, [x1, #-1]
    //     0x14f9aec: ubfx            x0, x0, #0xc, #0x14
    // 0x14f9af0: d0 = 0.100000
    //     0x14f9af0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14f9af4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14f9af4: sub             lr, x0, #0xffa
    //     0x14f9af8: ldr             lr, [x21, lr, lsl #3]
    //     0x14f9afc: blr             lr
    // 0x14f9b00: stur            x0, [fp, #-0x40]
    // 0x14f9b04: r0 = Divider()
    //     0x14f9b04: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x14f9b08: mov             x1, x0
    // 0x14f9b0c: ldur            x0, [fp, #-0x40]
    // 0x14f9b10: stur            x1, [fp, #-0x48]
    // 0x14f9b14: StoreField: r1->field_1f = r0
    //     0x14f9b14: stur            w0, [x1, #0x1f]
    // 0x14f9b18: r0 = SvgPicture()
    //     0x14f9b18: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14f9b1c: stur            x0, [fp, #-0x40]
    // 0x14f9b20: r16 = Instance_BoxFit
    //     0x14f9b20: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x14f9b24: ldr             x16, [x16, #0xb18]
    // 0x14f9b28: r30 = Instance_ColorFilter
    //     0x14f9b28: add             lr, PP, #0x33, lsl #12  ; [pp+0x33818] Obj!ColorFilter@d69801
    //     0x14f9b2c: ldr             lr, [lr, #0x818]
    // 0x14f9b30: stp             lr, x16, [SP]
    // 0x14f9b34: mov             x1, x0
    // 0x14f9b38: r2 = "assets/images/reason_glasses_icon.svg"
    //     0x14f9b38: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f338] "assets/images/reason_glasses_icon.svg"
    //     0x14f9b3c: ldr             x2, [x2, #0x338]
    // 0x14f9b40: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14f9b40: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14f9b44: ldr             x4, [x4, #0x820]
    // 0x14f9b48: r0 = SvgPicture.asset()
    //     0x14f9b48: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14f9b4c: ldur            x0, [fp, #-8]
    // 0x14f9b50: LoadField: r1 = r0->field_f
    //     0x14f9b50: ldur            w1, [x0, #0xf]
    // 0x14f9b54: DecompressPointer r1
    //     0x14f9b54: add             x1, x1, HEAP, lsl #32
    // 0x14f9b58: r0 = controller()
    //     0x14f9b58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f9b5c: LoadField: r1 = r0->field_57
    //     0x14f9b5c: ldur            w1, [x0, #0x57]
    // 0x14f9b60: DecompressPointer r1
    //     0x14f9b60: add             x1, x1, HEAP, lsl #32
    // 0x14f9b64: r0 = value()
    //     0x14f9b64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f9b68: cmp             w0, NULL
    // 0x14f9b6c: b.ne            #0x14f9b78
    // 0x14f9b70: r0 = Null
    //     0x14f9b70: mov             x0, NULL
    // 0x14f9b74: b               #0x14f9b84
    // 0x14f9b78: LoadField: r1 = r0->field_1b
    //     0x14f9b78: ldur            w1, [x0, #0x1b]
    // 0x14f9b7c: DecompressPointer r1
    //     0x14f9b7c: add             x1, x1, HEAP, lsl #32
    // 0x14f9b80: mov             x0, x1
    // 0x14f9b84: cmp             w0, NULL
    // 0x14f9b88: b.ne            #0x14f9b94
    // 0x14f9b8c: r2 = ""
    //     0x14f9b8c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f9b90: b               #0x14f9b98
    // 0x14f9b94: mov             x2, x0
    // 0x14f9b98: ldur            x0, [fp, #-8]
    // 0x14f9b9c: stur            x2, [fp, #-0x50]
    // 0x14f9ba0: LoadField: r1 = r0->field_13
    //     0x14f9ba0: ldur            w1, [x0, #0x13]
    // 0x14f9ba4: DecompressPointer r1
    //     0x14f9ba4: add             x1, x1, HEAP, lsl #32
    // 0x14f9ba8: r0 = of()
    //     0x14f9ba8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9bac: LoadField: r1 = r0->field_87
    //     0x14f9bac: ldur            w1, [x0, #0x87]
    // 0x14f9bb0: DecompressPointer r1
    //     0x14f9bb0: add             x1, x1, HEAP, lsl #32
    // 0x14f9bb4: LoadField: r0 = r1->field_33
    //     0x14f9bb4: ldur            w0, [x1, #0x33]
    // 0x14f9bb8: DecompressPointer r0
    //     0x14f9bb8: add             x0, x0, HEAP, lsl #32
    // 0x14f9bbc: stur            x0, [fp, #-0x58]
    // 0x14f9bc0: cmp             w0, NULL
    // 0x14f9bc4: b.ne            #0x14f9bd0
    // 0x14f9bc8: r7 = Null
    //     0x14f9bc8: mov             x7, NULL
    // 0x14f9bcc: b               #0x14f9c00
    // 0x14f9bd0: r1 = Instance_Color
    //     0x14f9bd0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f9bd4: d0 = 0.700000
    //     0x14f9bd4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f9bd8: ldr             d0, [x17, #0xf48]
    // 0x14f9bdc: r0 = withOpacity()
    //     0x14f9bdc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f9be0: r16 = 12.000000
    //     0x14f9be0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f9be4: ldr             x16, [x16, #0x9e8]
    // 0x14f9be8: stp             x0, x16, [SP]
    // 0x14f9bec: ldur            x1, [fp, #-0x58]
    // 0x14f9bf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f9bf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f9bf4: ldr             x4, [x4, #0xaa0]
    // 0x14f9bf8: r0 = copyWith()
    //     0x14f9bf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9bfc: mov             x7, x0
    // 0x14f9c00: ldur            x0, [fp, #-8]
    // 0x14f9c04: ldur            x6, [fp, #-0x20]
    // 0x14f9c08: ldur            x5, [fp, #-0x30]
    // 0x14f9c0c: ldur            x4, [fp, #-0x38]
    // 0x14f9c10: ldur            x3, [fp, #-0x48]
    // 0x14f9c14: ldur            x2, [fp, #-0x40]
    // 0x14f9c18: ldur            x1, [fp, #-0x50]
    // 0x14f9c1c: stur            x7, [fp, #-0x58]
    // 0x14f9c20: r0 = Text()
    //     0x14f9c20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9c24: mov             x2, x0
    // 0x14f9c28: ldur            x0, [fp, #-0x50]
    // 0x14f9c2c: stur            x2, [fp, #-0x60]
    // 0x14f9c30: StoreField: r2->field_b = r0
    //     0x14f9c30: stur            w0, [x2, #0xb]
    // 0x14f9c34: ldur            x0, [fp, #-0x58]
    // 0x14f9c38: StoreField: r2->field_13 = r0
    //     0x14f9c38: stur            w0, [x2, #0x13]
    // 0x14f9c3c: r0 = Instance_TextOverflow
    //     0x14f9c3c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14f9c40: ldr             x0, [x0, #0xe10]
    // 0x14f9c44: StoreField: r2->field_2b = r0
    //     0x14f9c44: stur            w0, [x2, #0x2b]
    // 0x14f9c48: r0 = 4
    //     0x14f9c48: movz            x0, #0x4
    // 0x14f9c4c: StoreField: r2->field_37 = r0
    //     0x14f9c4c: stur            w0, [x2, #0x37]
    // 0x14f9c50: r1 = <FlexParentData>
    //     0x14f9c50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14f9c54: ldr             x1, [x1, #0xe00]
    // 0x14f9c58: r0 = Expanded()
    //     0x14f9c58: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14f9c5c: mov             x3, x0
    // 0x14f9c60: r0 = 1
    //     0x14f9c60: movz            x0, #0x1
    // 0x14f9c64: stur            x3, [fp, #-0x50]
    // 0x14f9c68: StoreField: r3->field_13 = r0
    //     0x14f9c68: stur            x0, [x3, #0x13]
    // 0x14f9c6c: r0 = Instance_FlexFit
    //     0x14f9c6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14f9c70: ldr             x0, [x0, #0xe08]
    // 0x14f9c74: StoreField: r3->field_1b = r0
    //     0x14f9c74: stur            w0, [x3, #0x1b]
    // 0x14f9c78: ldur            x0, [fp, #-0x60]
    // 0x14f9c7c: StoreField: r3->field_b = r0
    //     0x14f9c7c: stur            w0, [x3, #0xb]
    // 0x14f9c80: r1 = Null
    //     0x14f9c80: mov             x1, NULL
    // 0x14f9c84: r2 = 6
    //     0x14f9c84: movz            x2, #0x6
    // 0x14f9c88: r0 = AllocateArray()
    //     0x14f9c88: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f9c8c: mov             x2, x0
    // 0x14f9c90: ldur            x0, [fp, #-0x40]
    // 0x14f9c94: stur            x2, [fp, #-0x58]
    // 0x14f9c98: StoreField: r2->field_f = r0
    //     0x14f9c98: stur            w0, [x2, #0xf]
    // 0x14f9c9c: r16 = Instance_SizedBox
    //     0x14f9c9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14f9ca0: ldr             x16, [x16, #0xb20]
    // 0x14f9ca4: StoreField: r2->field_13 = r16
    //     0x14f9ca4: stur            w16, [x2, #0x13]
    // 0x14f9ca8: ldur            x0, [fp, #-0x50]
    // 0x14f9cac: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f9cac: stur            w0, [x2, #0x17]
    // 0x14f9cb0: r1 = <Widget>
    //     0x14f9cb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14f9cb4: r0 = AllocateGrowableArray()
    //     0x14f9cb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f9cb8: mov             x1, x0
    // 0x14f9cbc: ldur            x0, [fp, #-0x58]
    // 0x14f9cc0: stur            x1, [fp, #-0x40]
    // 0x14f9cc4: StoreField: r1->field_f = r0
    //     0x14f9cc4: stur            w0, [x1, #0xf]
    // 0x14f9cc8: r0 = 6
    //     0x14f9cc8: movz            x0, #0x6
    // 0x14f9ccc: StoreField: r1->field_b = r0
    //     0x14f9ccc: stur            w0, [x1, #0xb]
    // 0x14f9cd0: r0 = Row()
    //     0x14f9cd0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14f9cd4: mov             x1, x0
    // 0x14f9cd8: r0 = Instance_Axis
    //     0x14f9cd8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14f9cdc: stur            x1, [fp, #-0x50]
    // 0x14f9ce0: StoreField: r1->field_f = r0
    //     0x14f9ce0: stur            w0, [x1, #0xf]
    // 0x14f9ce4: r0 = Instance_MainAxisAlignment
    //     0x14f9ce4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14f9ce8: ldr             x0, [x0, #0xa08]
    // 0x14f9cec: StoreField: r1->field_13 = r0
    //     0x14f9cec: stur            w0, [x1, #0x13]
    // 0x14f9cf0: r2 = Instance_MainAxisSize
    //     0x14f9cf0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14f9cf4: ldr             x2, [x2, #0xa10]
    // 0x14f9cf8: ArrayStore: r1[0] = r2  ; List_4
    //     0x14f9cf8: stur            w2, [x1, #0x17]
    // 0x14f9cfc: r3 = Instance_CrossAxisAlignment
    //     0x14f9cfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14f9d00: ldr             x3, [x3, #0xa18]
    // 0x14f9d04: StoreField: r1->field_1b = r3
    //     0x14f9d04: stur            w3, [x1, #0x1b]
    // 0x14f9d08: r3 = Instance_VerticalDirection
    //     0x14f9d08: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14f9d0c: ldr             x3, [x3, #0xa20]
    // 0x14f9d10: StoreField: r1->field_23 = r3
    //     0x14f9d10: stur            w3, [x1, #0x23]
    // 0x14f9d14: r4 = Instance_Clip
    //     0x14f9d14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14f9d18: ldr             x4, [x4, #0x38]
    // 0x14f9d1c: StoreField: r1->field_2b = r4
    //     0x14f9d1c: stur            w4, [x1, #0x2b]
    // 0x14f9d20: StoreField: r1->field_2f = rZR
    //     0x14f9d20: stur            xzr, [x1, #0x2f]
    // 0x14f9d24: ldur            x5, [fp, #-0x40]
    // 0x14f9d28: StoreField: r1->field_b = r5
    //     0x14f9d28: stur            w5, [x1, #0xb]
    // 0x14f9d2c: r0 = Padding()
    //     0x14f9d2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14f9d30: mov             x3, x0
    // 0x14f9d34: r0 = Instance_EdgeInsets
    //     0x14f9d34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14f9d38: ldr             x0, [x0, #0x980]
    // 0x14f9d3c: stur            x3, [fp, #-0x40]
    // 0x14f9d40: StoreField: r3->field_f = r0
    //     0x14f9d40: stur            w0, [x3, #0xf]
    // 0x14f9d44: ldur            x0, [fp, #-0x50]
    // 0x14f9d48: StoreField: r3->field_b = r0
    //     0x14f9d48: stur            w0, [x3, #0xb]
    // 0x14f9d4c: r1 = Null
    //     0x14f9d4c: mov             x1, NULL
    // 0x14f9d50: r2 = 8
    //     0x14f9d50: movz            x2, #0x8
    // 0x14f9d54: r0 = AllocateArray()
    //     0x14f9d54: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14f9d58: mov             x2, x0
    // 0x14f9d5c: ldur            x0, [fp, #-0x30]
    // 0x14f9d60: stur            x2, [fp, #-0x50]
    // 0x14f9d64: StoreField: r2->field_f = r0
    //     0x14f9d64: stur            w0, [x2, #0xf]
    // 0x14f9d68: ldur            x0, [fp, #-0x38]
    // 0x14f9d6c: StoreField: r2->field_13 = r0
    //     0x14f9d6c: stur            w0, [x2, #0x13]
    // 0x14f9d70: ldur            x0, [fp, #-0x48]
    // 0x14f9d74: ArrayStore: r2[0] = r0  ; List_4
    //     0x14f9d74: stur            w0, [x2, #0x17]
    // 0x14f9d78: ldur            x0, [fp, #-0x40]
    // 0x14f9d7c: StoreField: r2->field_1b = r0
    //     0x14f9d7c: stur            w0, [x2, #0x1b]
    // 0x14f9d80: r1 = <Widget>
    //     0x14f9d80: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14f9d84: r0 = AllocateGrowableArray()
    //     0x14f9d84: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14f9d88: mov             x1, x0
    // 0x14f9d8c: ldur            x0, [fp, #-0x50]
    // 0x14f9d90: stur            x1, [fp, #-0x30]
    // 0x14f9d94: StoreField: r1->field_f = r0
    //     0x14f9d94: stur            w0, [x1, #0xf]
    // 0x14f9d98: r2 = 8
    //     0x14f9d98: movz            x2, #0x8
    // 0x14f9d9c: StoreField: r1->field_b = r2
    //     0x14f9d9c: stur            w2, [x1, #0xb]
    // 0x14f9da0: r0 = Column()
    //     0x14f9da0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14f9da4: mov             x1, x0
    // 0x14f9da8: r0 = Instance_Axis
    //     0x14f9da8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14f9dac: stur            x1, [fp, #-0x38]
    // 0x14f9db0: StoreField: r1->field_f = r0
    //     0x14f9db0: stur            w0, [x1, #0xf]
    // 0x14f9db4: r2 = Instance_MainAxisAlignment
    //     0x14f9db4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14f9db8: ldr             x2, [x2, #0xa08]
    // 0x14f9dbc: StoreField: r1->field_13 = r2
    //     0x14f9dbc: stur            w2, [x1, #0x13]
    // 0x14f9dc0: r3 = Instance_MainAxisSize
    //     0x14f9dc0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14f9dc4: ldr             x3, [x3, #0xa10]
    // 0x14f9dc8: ArrayStore: r1[0] = r3  ; List_4
    //     0x14f9dc8: stur            w3, [x1, #0x17]
    // 0x14f9dcc: r4 = Instance_CrossAxisAlignment
    //     0x14f9dcc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14f9dd0: ldr             x4, [x4, #0x890]
    // 0x14f9dd4: StoreField: r1->field_1b = r4
    //     0x14f9dd4: stur            w4, [x1, #0x1b]
    // 0x14f9dd8: r5 = Instance_VerticalDirection
    //     0x14f9dd8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14f9ddc: ldr             x5, [x5, #0xa20]
    // 0x14f9de0: StoreField: r1->field_23 = r5
    //     0x14f9de0: stur            w5, [x1, #0x23]
    // 0x14f9de4: r6 = Instance_Clip
    //     0x14f9de4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14f9de8: ldr             x6, [x6, #0x38]
    // 0x14f9dec: StoreField: r1->field_2b = r6
    //     0x14f9dec: stur            w6, [x1, #0x2b]
    // 0x14f9df0: StoreField: r1->field_2f = rZR
    //     0x14f9df0: stur            xzr, [x1, #0x2f]
    // 0x14f9df4: ldur            x7, [fp, #-0x30]
    // 0x14f9df8: StoreField: r1->field_b = r7
    //     0x14f9df8: stur            w7, [x1, #0xb]
    // 0x14f9dfc: r0 = Card()
    //     0x14f9dfc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14f9e00: mov             x1, x0
    // 0x14f9e04: r0 = 0.000000
    //     0x14f9e04: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14f9e08: stur            x1, [fp, #-0x30]
    // 0x14f9e0c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14f9e0c: stur            w0, [x1, #0x17]
    // 0x14f9e10: ldur            x2, [fp, #-0x20]
    // 0x14f9e14: StoreField: r1->field_1b = r2
    //     0x14f9e14: stur            w2, [x1, #0x1b]
    // 0x14f9e18: r2 = true
    //     0x14f9e18: add             x2, NULL, #0x20  ; true
    // 0x14f9e1c: StoreField: r1->field_1f = r2
    //     0x14f9e1c: stur            w2, [x1, #0x1f]
    // 0x14f9e20: r3 = Instance_EdgeInsets
    //     0x14f9e20: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f340] Obj!EdgeInsets@d58161
    //     0x14f9e24: ldr             x3, [x3, #0x340]
    // 0x14f9e28: StoreField: r1->field_27 = r3
    //     0x14f9e28: stur            w3, [x1, #0x27]
    // 0x14f9e2c: ldur            x3, [fp, #-0x38]
    // 0x14f9e30: StoreField: r1->field_2f = r3
    //     0x14f9e30: stur            w3, [x1, #0x2f]
    // 0x14f9e34: StoreField: r1->field_2b = r2
    //     0x14f9e34: stur            w2, [x1, #0x2b]
    // 0x14f9e38: r3 = Instance__CardVariant
    //     0x14f9e38: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14f9e3c: ldr             x3, [x3, #0xa68]
    // 0x14f9e40: StoreField: r1->field_33 = r3
    //     0x14f9e40: stur            w3, [x1, #0x33]
    // 0x14f9e44: r0 = Radius()
    //     0x14f9e44: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14f9e48: d0 = 20.000000
    //     0x14f9e48: fmov            d0, #20.00000000
    // 0x14f9e4c: stur            x0, [fp, #-0x20]
    // 0x14f9e50: StoreField: r0->field_7 = d0
    //     0x14f9e50: stur            d0, [x0, #7]
    // 0x14f9e54: StoreField: r0->field_f = d0
    //     0x14f9e54: stur            d0, [x0, #0xf]
    // 0x14f9e58: r0 = BorderRadius()
    //     0x14f9e58: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14f9e5c: mov             x1, x0
    // 0x14f9e60: ldur            x0, [fp, #-0x20]
    // 0x14f9e64: stur            x1, [fp, #-0x38]
    // 0x14f9e68: StoreField: r1->field_7 = r0
    //     0x14f9e68: stur            w0, [x1, #7]
    // 0x14f9e6c: StoreField: r1->field_b = r0
    //     0x14f9e6c: stur            w0, [x1, #0xb]
    // 0x14f9e70: StoreField: r1->field_f = r0
    //     0x14f9e70: stur            w0, [x1, #0xf]
    // 0x14f9e74: StoreField: r1->field_13 = r0
    //     0x14f9e74: stur            w0, [x1, #0x13]
    // 0x14f9e78: r0 = RoundedRectangleBorder()
    //     0x14f9e78: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14f9e7c: mov             x2, x0
    // 0x14f9e80: ldur            x0, [fp, #-0x38]
    // 0x14f9e84: stur            x2, [fp, #-0x20]
    // 0x14f9e88: StoreField: r2->field_b = r0
    //     0x14f9e88: stur            w0, [x2, #0xb]
    // 0x14f9e8c: r0 = Instance_BorderSide
    //     0x14f9e8c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14f9e90: ldr             x0, [x0, #0xe20]
    // 0x14f9e94: StoreField: r2->field_7 = r0
    //     0x14f9e94: stur            w0, [x2, #7]
    // 0x14f9e98: ldur            x0, [fp, #-8]
    // 0x14f9e9c: LoadField: r1 = r0->field_13
    //     0x14f9e9c: ldur            w1, [x0, #0x13]
    // 0x14f9ea0: DecompressPointer r1
    //     0x14f9ea0: add             x1, x1, HEAP, lsl #32
    // 0x14f9ea4: r0 = of()
    //     0x14f9ea4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9ea8: LoadField: r1 = r0->field_87
    //     0x14f9ea8: ldur            w1, [x0, #0x87]
    // 0x14f9eac: DecompressPointer r1
    //     0x14f9eac: add             x1, x1, HEAP, lsl #32
    // 0x14f9eb0: LoadField: r0 = r1->field_7
    //     0x14f9eb0: ldur            w0, [x1, #7]
    // 0x14f9eb4: DecompressPointer r0
    //     0x14f9eb4: add             x0, x0, HEAP, lsl #32
    // 0x14f9eb8: stur            x0, [fp, #-0x38]
    // 0x14f9ebc: r1 = Instance_Color
    //     0x14f9ebc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f9ec0: d0 = 0.700000
    //     0x14f9ec0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f9ec4: ldr             d0, [x17, #0xf48]
    // 0x14f9ec8: r0 = withOpacity()
    //     0x14f9ec8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f9ecc: r16 = 14.000000
    //     0x14f9ecc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14f9ed0: ldr             x16, [x16, #0x1d8]
    // 0x14f9ed4: stp             x0, x16, [SP]
    // 0x14f9ed8: ldur            x1, [fp, #-0x38]
    // 0x14f9edc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f9edc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f9ee0: ldr             x4, [x4, #0xaa0]
    // 0x14f9ee4: r0 = copyWith()
    //     0x14f9ee4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9ee8: stur            x0, [fp, #-0x38]
    // 0x14f9eec: r0 = Text()
    //     0x14f9eec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9ef0: mov             x2, x0
    // 0x14f9ef4: r0 = "Note:"
    //     0x14f9ef4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33838] "Note:"
    //     0x14f9ef8: ldr             x0, [x0, #0x838]
    // 0x14f9efc: stur            x2, [fp, #-0x40]
    // 0x14f9f00: StoreField: r2->field_b = r0
    //     0x14f9f00: stur            w0, [x2, #0xb]
    // 0x14f9f04: ldur            x0, [fp, #-0x38]
    // 0x14f9f08: StoreField: r2->field_13 = r0
    //     0x14f9f08: stur            w0, [x2, #0x13]
    // 0x14f9f0c: ldur            x0, [fp, #-8]
    // 0x14f9f10: LoadField: r1 = r0->field_f
    //     0x14f9f10: ldur            w1, [x0, #0xf]
    // 0x14f9f14: DecompressPointer r1
    //     0x14f9f14: add             x1, x1, HEAP, lsl #32
    // 0x14f9f18: r0 = controller()
    //     0x14f9f18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14f9f1c: LoadField: r1 = r0->field_57
    //     0x14f9f1c: ldur            w1, [x0, #0x57]
    // 0x14f9f20: DecompressPointer r1
    //     0x14f9f20: add             x1, x1, HEAP, lsl #32
    // 0x14f9f24: r0 = value()
    //     0x14f9f24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14f9f28: cmp             w0, NULL
    // 0x14f9f2c: b.ne            #0x14f9f38
    // 0x14f9f30: r0 = Null
    //     0x14f9f30: mov             x0, NULL
    // 0x14f9f34: b               #0x14f9f44
    // 0x14f9f38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f9f38: ldur            w1, [x0, #0x17]
    // 0x14f9f3c: DecompressPointer r1
    //     0x14f9f3c: add             x1, x1, HEAP, lsl #32
    // 0x14f9f40: mov             x0, x1
    // 0x14f9f44: cmp             w0, NULL
    // 0x14f9f48: b.ne            #0x14f9f54
    // 0x14f9f4c: r2 = ""
    //     0x14f9f4c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14f9f50: b               #0x14f9f58
    // 0x14f9f54: mov             x2, x0
    // 0x14f9f58: ldur            x0, [fp, #-8]
    // 0x14f9f5c: stur            x2, [fp, #-0x38]
    // 0x14f9f60: LoadField: r1 = r0->field_13
    //     0x14f9f60: ldur            w1, [x0, #0x13]
    // 0x14f9f64: DecompressPointer r1
    //     0x14f9f64: add             x1, x1, HEAP, lsl #32
    // 0x14f9f68: r0 = of()
    //     0x14f9f68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f9f6c: LoadField: r1 = r0->field_87
    //     0x14f9f6c: ldur            w1, [x0, #0x87]
    // 0x14f9f70: DecompressPointer r1
    //     0x14f9f70: add             x1, x1, HEAP, lsl #32
    // 0x14f9f74: LoadField: r0 = r1->field_33
    //     0x14f9f74: ldur            w0, [x1, #0x33]
    // 0x14f9f78: DecompressPointer r0
    //     0x14f9f78: add             x0, x0, HEAP, lsl #32
    // 0x14f9f7c: stur            x0, [fp, #-8]
    // 0x14f9f80: cmp             w0, NULL
    // 0x14f9f84: b.ne            #0x14f9f90
    // 0x14f9f88: r7 = Null
    //     0x14f9f88: mov             x7, NULL
    // 0x14f9f8c: b               #0x14f9fc0
    // 0x14f9f90: r1 = Instance_Color
    //     0x14f9f90: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14f9f94: d0 = 0.700000
    //     0x14f9f94: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14f9f98: ldr             d0, [x17, #0xf48]
    // 0x14f9f9c: r0 = withOpacity()
    //     0x14f9f9c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14f9fa0: r16 = 12.000000
    //     0x14f9fa0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14f9fa4: ldr             x16, [x16, #0x9e8]
    // 0x14f9fa8: stp             x0, x16, [SP]
    // 0x14f9fac: ldur            x1, [fp, #-8]
    // 0x14f9fb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14f9fb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14f9fb4: ldr             x4, [x4, #0xaa0]
    // 0x14f9fb8: r0 = copyWith()
    //     0x14f9fb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14f9fbc: mov             x7, x0
    // 0x14f9fc0: ldur            x6, [fp, #-0x18]
    // 0x14f9fc4: ldur            x5, [fp, #-0x28]
    // 0x14f9fc8: ldur            x4, [fp, #-0x10]
    // 0x14f9fcc: ldur            x3, [fp, #-0x30]
    // 0x14f9fd0: ldur            x2, [fp, #-0x20]
    // 0x14f9fd4: ldur            x1, [fp, #-0x40]
    // 0x14f9fd8: ldur            x0, [fp, #-0x38]
    // 0x14f9fdc: stur            x7, [fp, #-8]
    // 0x14f9fe0: r0 = Text()
    //     0x14f9fe0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14f9fe4: mov             x1, x0
    // 0x14f9fe8: ldur            x0, [fp, #-0x38]
    // 0x14f9fec: stur            x1, [fp, #-0x48]
    // 0x14f9ff0: StoreField: r1->field_b = r0
    //     0x14f9ff0: stur            w0, [x1, #0xb]
    // 0x14f9ff4: ldur            x0, [fp, #-8]
    // 0x14f9ff8: StoreField: r1->field_13 = r0
    //     0x14f9ff8: stur            w0, [x1, #0x13]
    // 0x14f9ffc: r0 = Padding()
    //     0x14f9ffc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fa000: mov             x3, x0
    // 0x14fa004: r0 = Instance_EdgeInsets
    //     0x14fa004: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f348] Obj!EdgeInsets@d59271
    //     0x14fa008: ldr             x0, [x0, #0x348]
    // 0x14fa00c: stur            x3, [fp, #-8]
    // 0x14fa010: StoreField: r3->field_f = r0
    //     0x14fa010: stur            w0, [x3, #0xf]
    // 0x14fa014: ldur            x0, [fp, #-0x48]
    // 0x14fa018: StoreField: r3->field_b = r0
    //     0x14fa018: stur            w0, [x3, #0xb]
    // 0x14fa01c: r1 = Null
    //     0x14fa01c: mov             x1, NULL
    // 0x14fa020: r2 = 4
    //     0x14fa020: movz            x2, #0x4
    // 0x14fa024: r0 = AllocateArray()
    //     0x14fa024: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fa028: mov             x2, x0
    // 0x14fa02c: ldur            x0, [fp, #-0x40]
    // 0x14fa030: stur            x2, [fp, #-0x38]
    // 0x14fa034: StoreField: r2->field_f = r0
    //     0x14fa034: stur            w0, [x2, #0xf]
    // 0x14fa038: ldur            x0, [fp, #-8]
    // 0x14fa03c: StoreField: r2->field_13 = r0
    //     0x14fa03c: stur            w0, [x2, #0x13]
    // 0x14fa040: r1 = <Widget>
    //     0x14fa040: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fa044: r0 = AllocateGrowableArray()
    //     0x14fa044: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fa048: mov             x1, x0
    // 0x14fa04c: ldur            x0, [fp, #-0x38]
    // 0x14fa050: stur            x1, [fp, #-8]
    // 0x14fa054: StoreField: r1->field_f = r0
    //     0x14fa054: stur            w0, [x1, #0xf]
    // 0x14fa058: r2 = 4
    //     0x14fa058: movz            x2, #0x4
    // 0x14fa05c: StoreField: r1->field_b = r2
    //     0x14fa05c: stur            w2, [x1, #0xb]
    // 0x14fa060: r0 = Column()
    //     0x14fa060: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fa064: mov             x1, x0
    // 0x14fa068: r0 = Instance_Axis
    //     0x14fa068: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fa06c: stur            x1, [fp, #-0x38]
    // 0x14fa070: StoreField: r1->field_f = r0
    //     0x14fa070: stur            w0, [x1, #0xf]
    // 0x14fa074: r2 = Instance_MainAxisAlignment
    //     0x14fa074: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fa078: ldr             x2, [x2, #0xa08]
    // 0x14fa07c: StoreField: r1->field_13 = r2
    //     0x14fa07c: stur            w2, [x1, #0x13]
    // 0x14fa080: r3 = Instance_MainAxisSize
    //     0x14fa080: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fa084: ldr             x3, [x3, #0xa10]
    // 0x14fa088: ArrayStore: r1[0] = r3  ; List_4
    //     0x14fa088: stur            w3, [x1, #0x17]
    // 0x14fa08c: r4 = Instance_CrossAxisAlignment
    //     0x14fa08c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14fa090: ldr             x4, [x4, #0x890]
    // 0x14fa094: StoreField: r1->field_1b = r4
    //     0x14fa094: stur            w4, [x1, #0x1b]
    // 0x14fa098: r5 = Instance_VerticalDirection
    //     0x14fa098: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fa09c: ldr             x5, [x5, #0xa20]
    // 0x14fa0a0: StoreField: r1->field_23 = r5
    //     0x14fa0a0: stur            w5, [x1, #0x23]
    // 0x14fa0a4: r6 = Instance_Clip
    //     0x14fa0a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fa0a8: ldr             x6, [x6, #0x38]
    // 0x14fa0ac: StoreField: r1->field_2b = r6
    //     0x14fa0ac: stur            w6, [x1, #0x2b]
    // 0x14fa0b0: StoreField: r1->field_2f = rZR
    //     0x14fa0b0: stur            xzr, [x1, #0x2f]
    // 0x14fa0b4: ldur            x7, [fp, #-8]
    // 0x14fa0b8: StoreField: r1->field_b = r7
    //     0x14fa0b8: stur            w7, [x1, #0xb]
    // 0x14fa0bc: r0 = Padding()
    //     0x14fa0bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fa0c0: mov             x1, x0
    // 0x14fa0c4: r0 = Instance_EdgeInsets
    //     0x14fa0c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14fa0c8: ldr             x0, [x0, #0x1f0]
    // 0x14fa0cc: stur            x1, [fp, #-8]
    // 0x14fa0d0: StoreField: r1->field_f = r0
    //     0x14fa0d0: stur            w0, [x1, #0xf]
    // 0x14fa0d4: ldur            x2, [fp, #-0x38]
    // 0x14fa0d8: StoreField: r1->field_b = r2
    //     0x14fa0d8: stur            w2, [x1, #0xb]
    // 0x14fa0dc: r0 = Card()
    //     0x14fa0dc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14fa0e0: mov             x3, x0
    // 0x14fa0e4: r0 = 0.000000
    //     0x14fa0e4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14fa0e8: stur            x3, [fp, #-0x38]
    // 0x14fa0ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x14fa0ec: stur            w0, [x3, #0x17]
    // 0x14fa0f0: ldur            x0, [fp, #-0x20]
    // 0x14fa0f4: StoreField: r3->field_1b = r0
    //     0x14fa0f4: stur            w0, [x3, #0x1b]
    // 0x14fa0f8: r0 = true
    //     0x14fa0f8: add             x0, NULL, #0x20  ; true
    // 0x14fa0fc: StoreField: r3->field_1f = r0
    //     0x14fa0fc: stur            w0, [x3, #0x1f]
    // 0x14fa100: r1 = Instance_EdgeInsets
    //     0x14fa100: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x14fa104: ldr             x1, [x1, #0xb0]
    // 0x14fa108: StoreField: r3->field_27 = r1
    //     0x14fa108: stur            w1, [x3, #0x27]
    // 0x14fa10c: ldur            x1, [fp, #-8]
    // 0x14fa110: StoreField: r3->field_2f = r1
    //     0x14fa110: stur            w1, [x3, #0x2f]
    // 0x14fa114: StoreField: r3->field_2b = r0
    //     0x14fa114: stur            w0, [x3, #0x2b]
    // 0x14fa118: r0 = Instance__CardVariant
    //     0x14fa118: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14fa11c: ldr             x0, [x0, #0xa68]
    // 0x14fa120: StoreField: r3->field_33 = r0
    //     0x14fa120: stur            w0, [x3, #0x33]
    // 0x14fa124: r1 = Null
    //     0x14fa124: mov             x1, NULL
    // 0x14fa128: r2 = 4
    //     0x14fa128: movz            x2, #0x4
    // 0x14fa12c: r0 = AllocateArray()
    //     0x14fa12c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fa130: mov             x2, x0
    // 0x14fa134: ldur            x0, [fp, #-0x30]
    // 0x14fa138: stur            x2, [fp, #-8]
    // 0x14fa13c: StoreField: r2->field_f = r0
    //     0x14fa13c: stur            w0, [x2, #0xf]
    // 0x14fa140: ldur            x0, [fp, #-0x38]
    // 0x14fa144: StoreField: r2->field_13 = r0
    //     0x14fa144: stur            w0, [x2, #0x13]
    // 0x14fa148: r1 = <Widget>
    //     0x14fa148: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fa14c: r0 = AllocateGrowableArray()
    //     0x14fa14c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fa150: mov             x1, x0
    // 0x14fa154: ldur            x0, [fp, #-8]
    // 0x14fa158: stur            x1, [fp, #-0x20]
    // 0x14fa15c: StoreField: r1->field_f = r0
    //     0x14fa15c: stur            w0, [x1, #0xf]
    // 0x14fa160: r0 = 4
    //     0x14fa160: movz            x0, #0x4
    // 0x14fa164: StoreField: r1->field_b = r0
    //     0x14fa164: stur            w0, [x1, #0xb]
    // 0x14fa168: r0 = Column()
    //     0x14fa168: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fa16c: mov             x1, x0
    // 0x14fa170: r0 = Instance_Axis
    //     0x14fa170: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fa174: stur            x1, [fp, #-8]
    // 0x14fa178: StoreField: r1->field_f = r0
    //     0x14fa178: stur            w0, [x1, #0xf]
    // 0x14fa17c: r2 = Instance_MainAxisAlignment
    //     0x14fa17c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fa180: ldr             x2, [x2, #0xa08]
    // 0x14fa184: StoreField: r1->field_13 = r2
    //     0x14fa184: stur            w2, [x1, #0x13]
    // 0x14fa188: r3 = Instance_MainAxisSize
    //     0x14fa188: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14fa18c: ldr             x3, [x3, #0xdd0]
    // 0x14fa190: ArrayStore: r1[0] = r3  ; List_4
    //     0x14fa190: stur            w3, [x1, #0x17]
    // 0x14fa194: r3 = Instance_CrossAxisAlignment
    //     0x14fa194: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14fa198: ldr             x3, [x3, #0x890]
    // 0x14fa19c: StoreField: r1->field_1b = r3
    //     0x14fa19c: stur            w3, [x1, #0x1b]
    // 0x14fa1a0: r4 = Instance_VerticalDirection
    //     0x14fa1a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fa1a4: ldr             x4, [x4, #0xa20]
    // 0x14fa1a8: StoreField: r1->field_23 = r4
    //     0x14fa1a8: stur            w4, [x1, #0x23]
    // 0x14fa1ac: r5 = Instance_Clip
    //     0x14fa1ac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fa1b0: ldr             x5, [x5, #0x38]
    // 0x14fa1b4: StoreField: r1->field_2b = r5
    //     0x14fa1b4: stur            w5, [x1, #0x2b]
    // 0x14fa1b8: StoreField: r1->field_2f = rZR
    //     0x14fa1b8: stur            xzr, [x1, #0x2f]
    // 0x14fa1bc: ldur            x6, [fp, #-0x20]
    // 0x14fa1c0: StoreField: r1->field_b = r6
    //     0x14fa1c0: stur            w6, [x1, #0xb]
    // 0x14fa1c4: r0 = Visibility()
    //     0x14fa1c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14fa1c8: mov             x3, x0
    // 0x14fa1cc: ldur            x0, [fp, #-8]
    // 0x14fa1d0: stur            x3, [fp, #-0x20]
    // 0x14fa1d4: StoreField: r3->field_b = r0
    //     0x14fa1d4: stur            w0, [x3, #0xb]
    // 0x14fa1d8: r0 = Instance_SizedBox
    //     0x14fa1d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14fa1dc: StoreField: r3->field_f = r0
    //     0x14fa1dc: stur            w0, [x3, #0xf]
    // 0x14fa1e0: ldur            x0, [fp, #-0x10]
    // 0x14fa1e4: StoreField: r3->field_13 = r0
    //     0x14fa1e4: stur            w0, [x3, #0x13]
    // 0x14fa1e8: r0 = false
    //     0x14fa1e8: add             x0, NULL, #0x30  ; false
    // 0x14fa1ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x14fa1ec: stur            w0, [x3, #0x17]
    // 0x14fa1f0: StoreField: r3->field_1b = r0
    //     0x14fa1f0: stur            w0, [x3, #0x1b]
    // 0x14fa1f4: StoreField: r3->field_1f = r0
    //     0x14fa1f4: stur            w0, [x3, #0x1f]
    // 0x14fa1f8: StoreField: r3->field_23 = r0
    //     0x14fa1f8: stur            w0, [x3, #0x23]
    // 0x14fa1fc: StoreField: r3->field_27 = r0
    //     0x14fa1fc: stur            w0, [x3, #0x27]
    // 0x14fa200: StoreField: r3->field_2b = r0
    //     0x14fa200: stur            w0, [x3, #0x2b]
    // 0x14fa204: r1 = Null
    //     0x14fa204: mov             x1, NULL
    // 0x14fa208: r2 = 8
    //     0x14fa208: movz            x2, #0x8
    // 0x14fa20c: r0 = AllocateArray()
    //     0x14fa20c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fa210: mov             x2, x0
    // 0x14fa214: ldur            x0, [fp, #-0x18]
    // 0x14fa218: stur            x2, [fp, #-8]
    // 0x14fa21c: StoreField: r2->field_f = r0
    //     0x14fa21c: stur            w0, [x2, #0xf]
    // 0x14fa220: r16 = Instance_SizedBox
    //     0x14fa220: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x14fa224: ldr             x16, [x16, #0x8b8]
    // 0x14fa228: StoreField: r2->field_13 = r16
    //     0x14fa228: stur            w16, [x2, #0x13]
    // 0x14fa22c: ldur            x0, [fp, #-0x28]
    // 0x14fa230: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fa230: stur            w0, [x2, #0x17]
    // 0x14fa234: ldur            x0, [fp, #-0x20]
    // 0x14fa238: StoreField: r2->field_1b = r0
    //     0x14fa238: stur            w0, [x2, #0x1b]
    // 0x14fa23c: r1 = <Widget>
    //     0x14fa23c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fa240: r0 = AllocateGrowableArray()
    //     0x14fa240: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fa244: mov             x1, x0
    // 0x14fa248: ldur            x0, [fp, #-8]
    // 0x14fa24c: stur            x1, [fp, #-0x10]
    // 0x14fa250: StoreField: r1->field_f = r0
    //     0x14fa250: stur            w0, [x1, #0xf]
    // 0x14fa254: r0 = 8
    //     0x14fa254: movz            x0, #0x8
    // 0x14fa258: StoreField: r1->field_b = r0
    //     0x14fa258: stur            w0, [x1, #0xb]
    // 0x14fa25c: r0 = Column()
    //     0x14fa25c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fa260: mov             x1, x0
    // 0x14fa264: r0 = Instance_Axis
    //     0x14fa264: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fa268: stur            x1, [fp, #-8]
    // 0x14fa26c: StoreField: r1->field_f = r0
    //     0x14fa26c: stur            w0, [x1, #0xf]
    // 0x14fa270: r2 = Instance_MainAxisAlignment
    //     0x14fa270: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fa274: ldr             x2, [x2, #0xa08]
    // 0x14fa278: StoreField: r1->field_13 = r2
    //     0x14fa278: stur            w2, [x1, #0x13]
    // 0x14fa27c: r2 = Instance_MainAxisSize
    //     0x14fa27c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fa280: ldr             x2, [x2, #0xa10]
    // 0x14fa284: ArrayStore: r1[0] = r2  ; List_4
    //     0x14fa284: stur            w2, [x1, #0x17]
    // 0x14fa288: r2 = Instance_CrossAxisAlignment
    //     0x14fa288: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14fa28c: ldr             x2, [x2, #0x890]
    // 0x14fa290: StoreField: r1->field_1b = r2
    //     0x14fa290: stur            w2, [x1, #0x1b]
    // 0x14fa294: r2 = Instance_VerticalDirection
    //     0x14fa294: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fa298: ldr             x2, [x2, #0xa20]
    // 0x14fa29c: StoreField: r1->field_23 = r2
    //     0x14fa29c: stur            w2, [x1, #0x23]
    // 0x14fa2a0: r2 = Instance_Clip
    //     0x14fa2a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fa2a4: ldr             x2, [x2, #0x38]
    // 0x14fa2a8: StoreField: r1->field_2b = r2
    //     0x14fa2a8: stur            w2, [x1, #0x2b]
    // 0x14fa2ac: StoreField: r1->field_2f = rZR
    //     0x14fa2ac: stur            xzr, [x1, #0x2f]
    // 0x14fa2b0: ldur            x2, [fp, #-0x10]
    // 0x14fa2b4: StoreField: r1->field_b = r2
    //     0x14fa2b4: stur            w2, [x1, #0xb]
    // 0x14fa2b8: r0 = Padding()
    //     0x14fa2b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fa2bc: mov             x1, x0
    // 0x14fa2c0: r0 = Instance_EdgeInsets
    //     0x14fa2c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14fa2c4: ldr             x0, [x0, #0x1f0]
    // 0x14fa2c8: stur            x1, [fp, #-0x10]
    // 0x14fa2cc: StoreField: r1->field_f = r0
    //     0x14fa2cc: stur            w0, [x1, #0xf]
    // 0x14fa2d0: ldur            x0, [fp, #-8]
    // 0x14fa2d4: StoreField: r1->field_b = r0
    //     0x14fa2d4: stur            w0, [x1, #0xb]
    // 0x14fa2d8: r0 = SingleChildScrollView()
    //     0x14fa2d8: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14fa2dc: mov             x1, x0
    // 0x14fa2e0: r0 = Instance_Axis
    //     0x14fa2e0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fa2e4: StoreField: r1->field_b = r0
    //     0x14fa2e4: stur            w0, [x1, #0xb]
    // 0x14fa2e8: r0 = false
    //     0x14fa2e8: add             x0, NULL, #0x30  ; false
    // 0x14fa2ec: StoreField: r1->field_f = r0
    //     0x14fa2ec: stur            w0, [x1, #0xf]
    // 0x14fa2f0: ldur            x0, [fp, #-0x10]
    // 0x14fa2f4: StoreField: r1->field_23 = r0
    //     0x14fa2f4: stur            w0, [x1, #0x23]
    // 0x14fa2f8: r0 = Instance_DragStartBehavior
    //     0x14fa2f8: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14fa2fc: StoreField: r1->field_27 = r0
    //     0x14fa2fc: stur            w0, [x1, #0x27]
    // 0x14fa300: r0 = Instance_Clip
    //     0x14fa300: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14fa304: ldr             x0, [x0, #0x7e0]
    // 0x14fa308: StoreField: r1->field_2b = r0
    //     0x14fa308: stur            w0, [x1, #0x2b]
    // 0x14fa30c: r0 = Instance_HitTestBehavior
    //     0x14fa30c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14fa310: ldr             x0, [x0, #0x288]
    // 0x14fa314: StoreField: r1->field_2f = r0
    //     0x14fa314: stur            w0, [x1, #0x2f]
    // 0x14fa318: mov             x0, x1
    // 0x14fa31c: b               #0x14fa338
    // 0x14fa320: r0 = Container()
    //     0x14fa320: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fa324: mov             x1, x0
    // 0x14fa328: stur            x0, [fp, #-8]
    // 0x14fa32c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fa32c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fa330: r0 = Container()
    //     0x14fa330: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14fa334: ldur            x0, [fp, #-8]
    // 0x14fa338: LeaveFrame
    //     0x14fa338: mov             SP, fp
    //     0x14fa33c: ldp             fp, lr, [SP], #0x10
    // 0x14fa340: ret
    //     0x14fa340: ret             
    // 0x14fa344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fa344: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fa348: b               #0x14f86c4
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14fa34c, size: 0x74
    // 0x14fa34c: EnterFrame
    //     0x14fa34c: stp             fp, lr, [SP, #-0x10]!
    //     0x14fa350: mov             fp, SP
    // 0x14fa354: AllocStack(0x10)
    //     0x14fa354: sub             SP, SP, #0x10
    // 0x14fa358: SetupParameters()
    //     0x14fa358: ldr             x0, [fp, #0x20]
    //     0x14fa35c: ldur            w1, [x0, #0x17]
    //     0x14fa360: add             x1, x1, HEAP, lsl #32
    //     0x14fa364: stur            x1, [fp, #-8]
    // 0x14fa368: r1 = 2
    //     0x14fa368: movz            x1, #0x2
    // 0x14fa36c: r0 = AllocateContext()
    //     0x14fa36c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fa370: mov             x1, x0
    // 0x14fa374: ldur            x0, [fp, #-8]
    // 0x14fa378: stur            x1, [fp, #-0x10]
    // 0x14fa37c: StoreField: r1->field_b = r0
    //     0x14fa37c: stur            w0, [x1, #0xb]
    // 0x14fa380: ldr             x0, [fp, #0x18]
    // 0x14fa384: StoreField: r1->field_f = r0
    //     0x14fa384: stur            w0, [x1, #0xf]
    // 0x14fa388: ldr             x0, [fp, #0x10]
    // 0x14fa38c: StoreField: r1->field_13 = r0
    //     0x14fa38c: stur            w0, [x1, #0x13]
    // 0x14fa390: r0 = Obx()
    //     0x14fa390: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fa394: ldur            x2, [fp, #-0x10]
    // 0x14fa398: r1 = Function '<anonymous closure>':.
    //     0x14fa398: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f350] AnonymousClosure: (0x14fa3c0), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x14f8554)
    //     0x14fa39c: ldr             x1, [x1, #0x350]
    // 0x14fa3a0: stur            x0, [fp, #-8]
    // 0x14fa3a4: r0 = AllocateClosure()
    //     0x14fa3a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fa3a8: mov             x1, x0
    // 0x14fa3ac: ldur            x0, [fp, #-8]
    // 0x14fa3b0: StoreField: r0->field_b = r1
    //     0x14fa3b0: stur            w1, [x0, #0xb]
    // 0x14fa3b4: LeaveFrame
    //     0x14fa3b4: mov             SP, fp
    //     0x14fa3b8: ldp             fp, lr, [SP], #0x10
    // 0x14fa3bc: ret
    //     0x14fa3bc: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14fa3c0, size: 0xdc
    // 0x14fa3c0: EnterFrame
    //     0x14fa3c0: stp             fp, lr, [SP, #-0x10]!
    //     0x14fa3c4: mov             fp, SP
    // 0x14fa3c8: AllocStack(0x18)
    //     0x14fa3c8: sub             SP, SP, #0x18
    // 0x14fa3cc: SetupParameters()
    //     0x14fa3cc: ldr             x0, [fp, #0x10]
    //     0x14fa3d0: ldur            w2, [x0, #0x17]
    //     0x14fa3d4: add             x2, x2, HEAP, lsl #32
    //     0x14fa3d8: stur            x2, [fp, #-0x18]
    // 0x14fa3dc: CheckStackOverflow
    //     0x14fa3dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fa3e0: cmp             SP, x16
    //     0x14fa3e4: b.ls            #0x14fa490
    // 0x14fa3e8: LoadField: r0 = r2->field_b
    //     0x14fa3e8: ldur            w0, [x2, #0xb]
    // 0x14fa3ec: DecompressPointer r0
    //     0x14fa3ec: add             x0, x0, HEAP, lsl #32
    // 0x14fa3f0: LoadField: r3 = r0->field_f
    //     0x14fa3f0: ldur            w3, [x0, #0xf]
    // 0x14fa3f4: DecompressPointer r3
    //     0x14fa3f4: add             x3, x3, HEAP, lsl #32
    // 0x14fa3f8: stur            x3, [fp, #-0x10]
    // 0x14fa3fc: LoadField: r0 = r2->field_f
    //     0x14fa3fc: ldur            w0, [x2, #0xf]
    // 0x14fa400: DecompressPointer r0
    //     0x14fa400: add             x0, x0, HEAP, lsl #32
    // 0x14fa404: mov             x1, x3
    // 0x14fa408: stur            x0, [fp, #-8]
    // 0x14fa40c: r0 = controller()
    //     0x14fa40c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fa410: LoadField: r1 = r0->field_57
    //     0x14fa410: ldur            w1, [x0, #0x57]
    // 0x14fa414: DecompressPointer r1
    //     0x14fa414: add             x1, x1, HEAP, lsl #32
    // 0x14fa418: r0 = value()
    //     0x14fa418: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fa41c: cmp             w0, NULL
    // 0x14fa420: b.ne            #0x14fa42c
    // 0x14fa424: r3 = Null
    //     0x14fa424: mov             x3, NULL
    // 0x14fa428: b               #0x14fa478
    // 0x14fa42c: ldur            x1, [fp, #-0x18]
    // 0x14fa430: LoadField: r2 = r0->field_2b
    //     0x14fa430: ldur            w2, [x0, #0x2b]
    // 0x14fa434: DecompressPointer r2
    //     0x14fa434: add             x2, x2, HEAP, lsl #32
    // 0x14fa438: LoadField: r0 = r1->field_13
    //     0x14fa438: ldur            w0, [x1, #0x13]
    // 0x14fa43c: DecompressPointer r0
    //     0x14fa43c: add             x0, x0, HEAP, lsl #32
    // 0x14fa440: LoadField: r1 = r2->field_b
    //     0x14fa440: ldur            w1, [x2, #0xb]
    // 0x14fa444: r3 = LoadInt32Instr(r0)
    //     0x14fa444: sbfx            x3, x0, #1, #0x1f
    //     0x14fa448: tbz             w0, #0, #0x14fa450
    //     0x14fa44c: ldur            x3, [x0, #7]
    // 0x14fa450: r0 = LoadInt32Instr(r1)
    //     0x14fa450: sbfx            x0, x1, #1, #0x1f
    // 0x14fa454: mov             x1, x3
    // 0x14fa458: cmp             x1, x0
    // 0x14fa45c: b.hs            #0x14fa498
    // 0x14fa460: LoadField: r0 = r2->field_f
    //     0x14fa460: ldur            w0, [x2, #0xf]
    // 0x14fa464: DecompressPointer r0
    //     0x14fa464: add             x0, x0, HEAP, lsl #32
    // 0x14fa468: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14fa468: add             x16, x0, x3, lsl #2
    //     0x14fa46c: ldur            w1, [x16, #0xf]
    // 0x14fa470: DecompressPointer r1
    //     0x14fa470: add             x1, x1, HEAP, lsl #32
    // 0x14fa474: mov             x3, x1
    // 0x14fa478: ldur            x1, [fp, #-0x10]
    // 0x14fa47c: ldur            x2, [fp, #-8]
    // 0x14fa480: r0 = proofFieldWidget()
    //     0x14fa480: bl              #0x14fa49c  ; [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget
    // 0x14fa484: LeaveFrame
    //     0x14fa484: mov             SP, fp
    //     0x14fa488: ldp             fp, lr, [SP], #0x10
    // 0x14fa48c: ret
    //     0x14fa48c: ret             
    // 0x14fa490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fa490: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fa494: b               #0x14fa3e8
    // 0x14fa498: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14fa498: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ proofFieldWidget(/* No info */) {
    // ** addr: 0x14fa49c, size: 0xd48
    // 0x14fa49c: EnterFrame
    //     0x14fa49c: stp             fp, lr, [SP, #-0x10]!
    //     0x14fa4a0: mov             fp, SP
    // 0x14fa4a4: AllocStack(0x58)
    //     0x14fa4a4: sub             SP, SP, #0x58
    // 0x14fa4a8: SetupParameters(ReturnOrderWithProofView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x14fa4a8: stur            x1, [fp, #-8]
    //     0x14fa4ac: stur            x2, [fp, #-0x10]
    //     0x14fa4b0: stur            x3, [fp, #-0x18]
    // 0x14fa4b4: CheckStackOverflow
    //     0x14fa4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fa4b8: cmp             SP, x16
    //     0x14fa4bc: b.ls            #0x14fb1dc
    // 0x14fa4c0: r1 = 3
    //     0x14fa4c0: movz            x1, #0x3
    // 0x14fa4c4: r0 = AllocateContext()
    //     0x14fa4c4: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fa4c8: mov             x2, x0
    // 0x14fa4cc: ldur            x1, [fp, #-8]
    // 0x14fa4d0: stur            x2, [fp, #-0x20]
    // 0x14fa4d4: StoreField: r2->field_f = r1
    //     0x14fa4d4: stur            w1, [x2, #0xf]
    // 0x14fa4d8: ldur            x0, [fp, #-0x10]
    // 0x14fa4dc: StoreField: r2->field_13 = r0
    //     0x14fa4dc: stur            w0, [x2, #0x13]
    // 0x14fa4e0: ldur            x0, [fp, #-0x18]
    // 0x14fa4e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fa4e4: stur            w0, [x2, #0x17]
    // 0x14fa4e8: cmp             w0, NULL
    // 0x14fa4ec: b.ne            #0x14fa4f8
    // 0x14fa4f0: r0 = Null
    //     0x14fa4f0: mov             x0, NULL
    // 0x14fa4f4: b               #0x14fa504
    // 0x14fa4f8: LoadField: r3 = r0->field_7
    //     0x14fa4f8: ldur            w3, [x0, #7]
    // 0x14fa4fc: DecompressPointer r3
    //     0x14fa4fc: add             x3, x3, HEAP, lsl #32
    // 0x14fa500: mov             x0, x3
    // 0x14fa504: r3 = LoadClassIdInstr(r0)
    //     0x14fa504: ldur            x3, [x0, #-1]
    //     0x14fa508: ubfx            x3, x3, #0xc, #0x14
    // 0x14fa50c: r16 = "unboxing_videos"
    //     0x14fa50c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "unboxing_videos"
    //     0x14fa510: ldr             x16, [x16, #0x860]
    // 0x14fa514: stp             x16, x0, [SP]
    // 0x14fa518: mov             x0, x3
    // 0x14fa51c: mov             lr, x0
    // 0x14fa520: ldr             lr, [x21, lr, lsl #3]
    // 0x14fa524: blr             lr
    // 0x14fa528: tbnz            w0, #4, #0x14fa558
    // 0x14fa52c: ldur            x2, [fp, #-0x20]
    // 0x14fa530: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14fa530: ldur            w0, [x2, #0x17]
    // 0x14fa534: DecompressPointer r0
    //     0x14fa534: add             x0, x0, HEAP, lsl #32
    // 0x14fa538: cmp             w0, NULL
    // 0x14fa53c: b.ne            #0x14fa548
    // 0x14fa540: r0 = Null
    //     0x14fa540: mov             x0, NULL
    // 0x14fa544: b               #0x14fa580
    // 0x14fa548: LoadField: r1 = r0->field_b
    //     0x14fa548: ldur            w1, [x0, #0xb]
    // 0x14fa54c: DecompressPointer r1
    //     0x14fa54c: add             x1, x1, HEAP, lsl #32
    // 0x14fa550: mov             x0, x1
    // 0x14fa554: b               #0x14fa580
    // 0x14fa558: ldur            x2, [fp, #-0x20]
    // 0x14fa55c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14fa55c: ldur            w0, [x2, #0x17]
    // 0x14fa560: DecompressPointer r0
    //     0x14fa560: add             x0, x0, HEAP, lsl #32
    // 0x14fa564: cmp             w0, NULL
    // 0x14fa568: b.ne            #0x14fa574
    // 0x14fa56c: r0 = Null
    //     0x14fa56c: mov             x0, NULL
    // 0x14fa570: b               #0x14fa580
    // 0x14fa574: LoadField: r1 = r0->field_b
    //     0x14fa574: ldur            w1, [x0, #0xb]
    // 0x14fa578: DecompressPointer r1
    //     0x14fa578: add             x1, x1, HEAP, lsl #32
    // 0x14fa57c: mov             x0, x1
    // 0x14fa580: stur            x0, [fp, #-0x10]
    // 0x14fa584: LoadField: r1 = r2->field_13
    //     0x14fa584: ldur            w1, [x2, #0x13]
    // 0x14fa588: DecompressPointer r1
    //     0x14fa588: add             x1, x1, HEAP, lsl #32
    // 0x14fa58c: r0 = of()
    //     0x14fa58c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fa590: LoadField: r1 = r0->field_87
    //     0x14fa590: ldur            w1, [x0, #0x87]
    // 0x14fa594: DecompressPointer r1
    //     0x14fa594: add             x1, x1, HEAP, lsl #32
    // 0x14fa598: LoadField: r0 = r1->field_7
    //     0x14fa598: ldur            w0, [x1, #7]
    // 0x14fa59c: DecompressPointer r0
    //     0x14fa59c: add             x0, x0, HEAP, lsl #32
    // 0x14fa5a0: stur            x0, [fp, #-0x18]
    // 0x14fa5a4: r1 = Instance_Color
    //     0x14fa5a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fa5a8: d0 = 0.700000
    //     0x14fa5a8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14fa5ac: ldr             d0, [x17, #0xf48]
    // 0x14fa5b0: r0 = withOpacity()
    //     0x14fa5b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fa5b4: r16 = 12.000000
    //     0x14fa5b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14fa5b8: ldr             x16, [x16, #0x9e8]
    // 0x14fa5bc: stp             x0, x16, [SP]
    // 0x14fa5c0: ldur            x1, [fp, #-0x18]
    // 0x14fa5c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14fa5c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14fa5c8: ldr             x4, [x4, #0xaa0]
    // 0x14fa5cc: r0 = copyWith()
    //     0x14fa5cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fa5d0: stur            x0, [fp, #-0x18]
    // 0x14fa5d4: r0 = TextSpan()
    //     0x14fa5d4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14fa5d8: mov             x2, x0
    // 0x14fa5dc: ldur            x0, [fp, #-0x10]
    // 0x14fa5e0: stur            x2, [fp, #-0x28]
    // 0x14fa5e4: StoreField: r2->field_b = r0
    //     0x14fa5e4: stur            w0, [x2, #0xb]
    // 0x14fa5e8: r0 = Instance__DeferringMouseCursor
    //     0x14fa5e8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14fa5ec: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fa5ec: stur            w0, [x2, #0x17]
    // 0x14fa5f0: ldur            x1, [fp, #-0x18]
    // 0x14fa5f4: StoreField: r2->field_7 = r1
    //     0x14fa5f4: stur            w1, [x2, #7]
    // 0x14fa5f8: ldur            x3, [fp, #-0x20]
    // 0x14fa5fc: LoadField: r1 = r3->field_13
    //     0x14fa5fc: ldur            w1, [x3, #0x13]
    // 0x14fa600: DecompressPointer r1
    //     0x14fa600: add             x1, x1, HEAP, lsl #32
    // 0x14fa604: r0 = of()
    //     0x14fa604: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fa608: LoadField: r1 = r0->field_87
    //     0x14fa608: ldur            w1, [x0, #0x87]
    // 0x14fa60c: DecompressPointer r1
    //     0x14fa60c: add             x1, x1, HEAP, lsl #32
    // 0x14fa610: LoadField: r0 = r1->field_7
    //     0x14fa610: ldur            w0, [x1, #7]
    // 0x14fa614: DecompressPointer r0
    //     0x14fa614: add             x0, x0, HEAP, lsl #32
    // 0x14fa618: r16 = 14.000000
    //     0x14fa618: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14fa61c: ldr             x16, [x16, #0x1d8]
    // 0x14fa620: r30 = Instance_Color
    //     0x14fa620: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14fa624: ldr             lr, [lr, #0x50]
    // 0x14fa628: stp             lr, x16, [SP]
    // 0x14fa62c: mov             x1, x0
    // 0x14fa630: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14fa630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14fa634: ldr             x4, [x4, #0xaa0]
    // 0x14fa638: r0 = copyWith()
    //     0x14fa638: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fa63c: stur            x0, [fp, #-0x10]
    // 0x14fa640: r0 = TextSpan()
    //     0x14fa640: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14fa644: mov             x3, x0
    // 0x14fa648: r0 = "*"
    //     0x14fa648: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "*"
    //     0x14fa64c: ldr             x0, [x0, #0x7a8]
    // 0x14fa650: stur            x3, [fp, #-0x18]
    // 0x14fa654: StoreField: r3->field_b = r0
    //     0x14fa654: stur            w0, [x3, #0xb]
    // 0x14fa658: r0 = Instance__DeferringMouseCursor
    //     0x14fa658: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14fa65c: ArrayStore: r3[0] = r0  ; List_4
    //     0x14fa65c: stur            w0, [x3, #0x17]
    // 0x14fa660: ldur            x1, [fp, #-0x10]
    // 0x14fa664: StoreField: r3->field_7 = r1
    //     0x14fa664: stur            w1, [x3, #7]
    // 0x14fa668: r1 = Null
    //     0x14fa668: mov             x1, NULL
    // 0x14fa66c: r2 = 4
    //     0x14fa66c: movz            x2, #0x4
    // 0x14fa670: r0 = AllocateArray()
    //     0x14fa670: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fa674: mov             x2, x0
    // 0x14fa678: ldur            x0, [fp, #-0x28]
    // 0x14fa67c: stur            x2, [fp, #-0x10]
    // 0x14fa680: StoreField: r2->field_f = r0
    //     0x14fa680: stur            w0, [x2, #0xf]
    // 0x14fa684: ldur            x0, [fp, #-0x18]
    // 0x14fa688: StoreField: r2->field_13 = r0
    //     0x14fa688: stur            w0, [x2, #0x13]
    // 0x14fa68c: r1 = <InlineSpan>
    //     0x14fa68c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14fa690: ldr             x1, [x1, #0xe40]
    // 0x14fa694: r0 = AllocateGrowableArray()
    //     0x14fa694: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fa698: mov             x1, x0
    // 0x14fa69c: ldur            x0, [fp, #-0x10]
    // 0x14fa6a0: stur            x1, [fp, #-0x18]
    // 0x14fa6a4: StoreField: r1->field_f = r0
    //     0x14fa6a4: stur            w0, [x1, #0xf]
    // 0x14fa6a8: r2 = 4
    //     0x14fa6a8: movz            x2, #0x4
    // 0x14fa6ac: StoreField: r1->field_b = r2
    //     0x14fa6ac: stur            w2, [x1, #0xb]
    // 0x14fa6b0: r0 = TextSpan()
    //     0x14fa6b0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14fa6b4: mov             x1, x0
    // 0x14fa6b8: ldur            x0, [fp, #-0x18]
    // 0x14fa6bc: stur            x1, [fp, #-0x10]
    // 0x14fa6c0: StoreField: r1->field_f = r0
    //     0x14fa6c0: stur            w0, [x1, #0xf]
    // 0x14fa6c4: r0 = Instance__DeferringMouseCursor
    //     0x14fa6c4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14fa6c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14fa6c8: stur            w0, [x1, #0x17]
    // 0x14fa6cc: r0 = RichText()
    //     0x14fa6cc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14fa6d0: mov             x1, x0
    // 0x14fa6d4: ldur            x2, [fp, #-0x10]
    // 0x14fa6d8: stur            x0, [fp, #-0x10]
    // 0x14fa6dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14fa6dc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14fa6e0: r0 = RichText()
    //     0x14fa6e0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14fa6e4: ldur            x2, [fp, #-0x20]
    // 0x14fa6e8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14fa6e8: ldur            w0, [x2, #0x17]
    // 0x14fa6ec: DecompressPointer r0
    //     0x14fa6ec: add             x0, x0, HEAP, lsl #32
    // 0x14fa6f0: cmp             w0, NULL
    // 0x14fa6f4: b.ne            #0x14fa700
    // 0x14fa6f8: r0 = Null
    //     0x14fa6f8: mov             x0, NULL
    // 0x14fa6fc: b               #0x14fa70c
    // 0x14fa700: LoadField: r1 = r0->field_7
    //     0x14fa700: ldur            w1, [x0, #7]
    // 0x14fa704: DecompressPointer r1
    //     0x14fa704: add             x1, x1, HEAP, lsl #32
    // 0x14fa708: mov             x0, x1
    // 0x14fa70c: r1 = LoadClassIdInstr(r0)
    //     0x14fa70c: ldur            x1, [x0, #-1]
    //     0x14fa710: ubfx            x1, x1, #0xc, #0x14
    // 0x14fa714: r16 = "unboxing_videos"
    //     0x14fa714: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "unboxing_videos"
    //     0x14fa718: ldr             x16, [x16, #0x860]
    // 0x14fa71c: stp             x16, x0, [SP]
    // 0x14fa720: mov             x0, x1
    // 0x14fa724: mov             lr, x0
    // 0x14fa728: ldr             lr, [x21, lr, lsl #3]
    // 0x14fa72c: blr             lr
    // 0x14fa730: tbnz            w0, #4, #0x14fa770
    // 0x14fa734: ldur            x2, [fp, #-0x20]
    // 0x14fa738: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14fa738: ldur            w0, [x2, #0x17]
    // 0x14fa73c: DecompressPointer r0
    //     0x14fa73c: add             x0, x0, HEAP, lsl #32
    // 0x14fa740: cmp             w0, NULL
    // 0x14fa744: b.ne            #0x14fa750
    // 0x14fa748: r0 = Null
    //     0x14fa748: mov             x0, NULL
    // 0x14fa74c: b               #0x14fa75c
    // 0x14fa750: LoadField: r1 = r0->field_f
    //     0x14fa750: ldur            w1, [x0, #0xf]
    // 0x14fa754: DecompressPointer r1
    //     0x14fa754: add             x1, x1, HEAP, lsl #32
    // 0x14fa758: mov             x0, x1
    // 0x14fa75c: cmp             w0, NULL
    // 0x14fa760: b.ne            #0x14fa768
    // 0x14fa764: r0 = ""
    //     0x14fa764: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14fa768: mov             x3, x0
    // 0x14fa76c: b               #0x14fa7a8
    // 0x14fa770: ldur            x2, [fp, #-0x20]
    // 0x14fa774: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14fa774: ldur            w0, [x2, #0x17]
    // 0x14fa778: DecompressPointer r0
    //     0x14fa778: add             x0, x0, HEAP, lsl #32
    // 0x14fa77c: cmp             w0, NULL
    // 0x14fa780: b.ne            #0x14fa78c
    // 0x14fa784: r0 = Null
    //     0x14fa784: mov             x0, NULL
    // 0x14fa788: b               #0x14fa798
    // 0x14fa78c: LoadField: r1 = r0->field_f
    //     0x14fa78c: ldur            w1, [x0, #0xf]
    // 0x14fa790: DecompressPointer r1
    //     0x14fa790: add             x1, x1, HEAP, lsl #32
    // 0x14fa794: mov             x0, x1
    // 0x14fa798: cmp             w0, NULL
    // 0x14fa79c: b.ne            #0x14fa7a4
    // 0x14fa7a0: r0 = ""
    //     0x14fa7a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14fa7a4: mov             x3, x0
    // 0x14fa7a8: ldur            x0, [fp, #-0x10]
    // 0x14fa7ac: stur            x3, [fp, #-0x18]
    // 0x14fa7b0: LoadField: r1 = r2->field_13
    //     0x14fa7b0: ldur            w1, [x2, #0x13]
    // 0x14fa7b4: DecompressPointer r1
    //     0x14fa7b4: add             x1, x1, HEAP, lsl #32
    // 0x14fa7b8: r0 = of()
    //     0x14fa7b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fa7bc: LoadField: r1 = r0->field_87
    //     0x14fa7bc: ldur            w1, [x0, #0x87]
    // 0x14fa7c0: DecompressPointer r1
    //     0x14fa7c0: add             x1, x1, HEAP, lsl #32
    // 0x14fa7c4: LoadField: r0 = r1->field_2b
    //     0x14fa7c4: ldur            w0, [x1, #0x2b]
    // 0x14fa7c8: DecompressPointer r0
    //     0x14fa7c8: add             x0, x0, HEAP, lsl #32
    // 0x14fa7cc: stur            x0, [fp, #-0x28]
    // 0x14fa7d0: r1 = Instance_Color
    //     0x14fa7d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fa7d4: d0 = 0.400000
    //     0x14fa7d4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14fa7d8: r0 = withOpacity()
    //     0x14fa7d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fa7dc: r16 = 12.000000
    //     0x14fa7dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14fa7e0: ldr             x16, [x16, #0x9e8]
    // 0x14fa7e4: stp             x0, x16, [SP]
    // 0x14fa7e8: ldur            x1, [fp, #-0x28]
    // 0x14fa7ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14fa7ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14fa7f0: ldr             x4, [x4, #0xaa0]
    // 0x14fa7f4: r0 = copyWith()
    //     0x14fa7f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fa7f8: stur            x0, [fp, #-0x28]
    // 0x14fa7fc: r0 = Text()
    //     0x14fa7fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14fa800: mov             x1, x0
    // 0x14fa804: ldur            x0, [fp, #-0x18]
    // 0x14fa808: stur            x1, [fp, #-0x30]
    // 0x14fa80c: StoreField: r1->field_b = r0
    //     0x14fa80c: stur            w0, [x1, #0xb]
    // 0x14fa810: ldur            x0, [fp, #-0x28]
    // 0x14fa814: StoreField: r1->field_13 = r0
    //     0x14fa814: stur            w0, [x1, #0x13]
    // 0x14fa818: r0 = Padding()
    //     0x14fa818: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fa81c: mov             x3, x0
    // 0x14fa820: r0 = Instance_EdgeInsets
    //     0x14fa820: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x14fa824: ldr             x0, [x0, #0x770]
    // 0x14fa828: stur            x3, [fp, #-0x18]
    // 0x14fa82c: StoreField: r3->field_f = r0
    //     0x14fa82c: stur            w0, [x3, #0xf]
    // 0x14fa830: ldur            x0, [fp, #-0x30]
    // 0x14fa834: StoreField: r3->field_b = r0
    //     0x14fa834: stur            w0, [x3, #0xb]
    // 0x14fa838: r1 = Null
    //     0x14fa838: mov             x1, NULL
    // 0x14fa83c: r2 = 4
    //     0x14fa83c: movz            x2, #0x4
    // 0x14fa840: r0 = AllocateArray()
    //     0x14fa840: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fa844: mov             x2, x0
    // 0x14fa848: ldur            x0, [fp, #-0x10]
    // 0x14fa84c: stur            x2, [fp, #-0x28]
    // 0x14fa850: StoreField: r2->field_f = r0
    //     0x14fa850: stur            w0, [x2, #0xf]
    // 0x14fa854: ldur            x0, [fp, #-0x18]
    // 0x14fa858: StoreField: r2->field_13 = r0
    //     0x14fa858: stur            w0, [x2, #0x13]
    // 0x14fa85c: r1 = <Widget>
    //     0x14fa85c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fa860: r0 = AllocateGrowableArray()
    //     0x14fa860: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fa864: mov             x1, x0
    // 0x14fa868: ldur            x0, [fp, #-0x28]
    // 0x14fa86c: stur            x1, [fp, #-0x10]
    // 0x14fa870: StoreField: r1->field_f = r0
    //     0x14fa870: stur            w0, [x1, #0xf]
    // 0x14fa874: r0 = 4
    //     0x14fa874: movz            x0, #0x4
    // 0x14fa878: StoreField: r1->field_b = r0
    //     0x14fa878: stur            w0, [x1, #0xb]
    // 0x14fa87c: ldur            x2, [fp, #-0x20]
    // 0x14fa880: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14fa880: ldur            w0, [x2, #0x17]
    // 0x14fa884: DecompressPointer r0
    //     0x14fa884: add             x0, x0, HEAP, lsl #32
    // 0x14fa888: cmp             w0, NULL
    // 0x14fa88c: b.ne            #0x14fa898
    // 0x14fa890: r0 = Null
    //     0x14fa890: mov             x0, NULL
    // 0x14fa894: b               #0x14fa8a4
    // 0x14fa898: LoadField: r3 = r0->field_7
    //     0x14fa898: ldur            w3, [x0, #7]
    // 0x14fa89c: DecompressPointer r3
    //     0x14fa89c: add             x3, x3, HEAP, lsl #32
    // 0x14fa8a0: mov             x0, x3
    // 0x14fa8a4: r3 = LoadClassIdInstr(r0)
    //     0x14fa8a4: ldur            x3, [x0, #-1]
    //     0x14fa8a8: ubfx            x3, x3, #0xc, #0x14
    // 0x14fa8ac: r16 = "unboxing_videos"
    //     0x14fa8ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "unboxing_videos"
    //     0x14fa8b0: ldr             x16, [x16, #0x860]
    // 0x14fa8b4: stp             x16, x0, [SP]
    // 0x14fa8b8: mov             x0, x3
    // 0x14fa8bc: mov             lr, x0
    // 0x14fa8c0: ldr             lr, [x21, lr, lsl #3]
    // 0x14fa8c4: blr             lr
    // 0x14fa8c8: tbnz            w0, #4, #0x14fad04
    // 0x14fa8cc: ldur            x1, [fp, #-8]
    // 0x14fa8d0: r0 = controller()
    //     0x14fa8d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fa8d4: LoadField: r1 = r0->field_73
    //     0x14fa8d4: ldur            w1, [x0, #0x73]
    // 0x14fa8d8: DecompressPointer r1
    //     0x14fa8d8: add             x1, x1, HEAP, lsl #32
    // 0x14fa8dc: r0 = value()
    //     0x14fa8dc: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fa8e0: r1 = LoadClassIdInstr(r0)
    //     0x14fa8e0: ldur            x1, [x0, #-1]
    //     0x14fa8e4: ubfx            x1, x1, #0xc, #0x14
    // 0x14fa8e8: str             x0, [SP]
    // 0x14fa8ec: mov             x0, x1
    // 0x14fa8f0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fa8f0: movz            x17, #0xc898
    //     0x14fa8f4: add             lr, x0, x17
    //     0x14fa8f8: ldr             lr, [x21, lr, lsl #3]
    //     0x14fa8fc: blr             lr
    // 0x14fa900: cbnz            w0, #0x14faab4
    // 0x14fa904: ldur            x2, [fp, #-0x20]
    // 0x14fa908: r0 = Radius()
    //     0x14fa908: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fa90c: d0 = 12.000000
    //     0x14fa90c: fmov            d0, #12.00000000
    // 0x14fa910: stur            x0, [fp, #-0x18]
    // 0x14fa914: StoreField: r0->field_7 = d0
    //     0x14fa914: stur            d0, [x0, #7]
    // 0x14fa918: StoreField: r0->field_f = d0
    //     0x14fa918: stur            d0, [x0, #0xf]
    // 0x14fa91c: r0 = BorderRadius()
    //     0x14fa91c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fa920: mov             x2, x0
    // 0x14fa924: ldur            x0, [fp, #-0x18]
    // 0x14fa928: stur            x2, [fp, #-0x28]
    // 0x14fa92c: StoreField: r2->field_7 = r0
    //     0x14fa92c: stur            w0, [x2, #7]
    // 0x14fa930: StoreField: r2->field_b = r0
    //     0x14fa930: stur            w0, [x2, #0xb]
    // 0x14fa934: StoreField: r2->field_f = r0
    //     0x14fa934: stur            w0, [x2, #0xf]
    // 0x14fa938: StoreField: r2->field_13 = r0
    //     0x14fa938: stur            w0, [x2, #0x13]
    // 0x14fa93c: r1 = Instance_Color
    //     0x14fa93c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fa940: d0 = 0.100000
    //     0x14fa940: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14fa944: r0 = withOpacity()
    //     0x14fa944: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fa948: r16 = 1.000000
    //     0x14fa948: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14fa94c: str             x16, [SP]
    // 0x14fa950: mov             x2, x0
    // 0x14fa954: r1 = Null
    //     0x14fa954: mov             x1, NULL
    // 0x14fa958: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14fa958: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14fa95c: ldr             x4, [x4, #0x108]
    // 0x14fa960: r0 = Border.all()
    //     0x14fa960: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14fa964: stur            x0, [fp, #-0x18]
    // 0x14fa968: r0 = BoxDecoration()
    //     0x14fa968: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14fa96c: mov             x2, x0
    // 0x14fa970: ldur            x0, [fp, #-0x18]
    // 0x14fa974: stur            x2, [fp, #-0x30]
    // 0x14fa978: StoreField: r2->field_f = r0
    //     0x14fa978: stur            w0, [x2, #0xf]
    // 0x14fa97c: ldur            x0, [fp, #-0x28]
    // 0x14fa980: StoreField: r2->field_13 = r0
    //     0x14fa980: stur            w0, [x2, #0x13]
    // 0x14fa984: r0 = Instance_BoxShape
    //     0x14fa984: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fa988: ldr             x0, [x0, #0x80]
    // 0x14fa98c: StoreField: r2->field_23 = r0
    //     0x14fa98c: stur            w0, [x2, #0x23]
    // 0x14fa990: ldur            x3, [fp, #-0x20]
    // 0x14fa994: LoadField: r1 = r3->field_13
    //     0x14fa994: ldur            w1, [x3, #0x13]
    // 0x14fa998: DecompressPointer r1
    //     0x14fa998: add             x1, x1, HEAP, lsl #32
    // 0x14fa99c: r0 = of()
    //     0x14fa99c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fa9a0: LoadField: r1 = r0->field_5b
    //     0x14fa9a0: ldur            w1, [x0, #0x5b]
    // 0x14fa9a4: DecompressPointer r1
    //     0x14fa9a4: add             x1, x1, HEAP, lsl #32
    // 0x14fa9a8: stur            x1, [fp, #-0x18]
    // 0x14fa9ac: r0 = ColorFilter()
    //     0x14fa9ac: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14fa9b0: mov             x1, x0
    // 0x14fa9b4: ldur            x0, [fp, #-0x18]
    // 0x14fa9b8: stur            x1, [fp, #-0x28]
    // 0x14fa9bc: StoreField: r1->field_7 = r0
    //     0x14fa9bc: stur            w0, [x1, #7]
    // 0x14fa9c0: r0 = Instance_BlendMode
    //     0x14fa9c0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14fa9c4: ldr             x0, [x0, #0xb30]
    // 0x14fa9c8: StoreField: r1->field_b = r0
    //     0x14fa9c8: stur            w0, [x1, #0xb]
    // 0x14fa9cc: r2 = 1
    //     0x14fa9cc: movz            x2, #0x1
    // 0x14fa9d0: StoreField: r1->field_13 = r2
    //     0x14fa9d0: stur            x2, [x1, #0x13]
    // 0x14fa9d4: r0 = SvgPicture()
    //     0x14fa9d4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14fa9d8: stur            x0, [fp, #-0x18]
    // 0x14fa9dc: r16 = Instance_BoxFit
    //     0x14fa9dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14fa9e0: ldr             x16, [x16, #0x118]
    // 0x14fa9e4: ldur            lr, [fp, #-0x28]
    // 0x14fa9e8: stp             lr, x16, [SP]
    // 0x14fa9ec: mov             x1, x0
    // 0x14fa9f0: r2 = "assets/images/image_bg.svg"
    //     0x14fa9f0: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14fa9f4: ldr             x2, [x2, #0x868]
    // 0x14fa9f8: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14fa9f8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14fa9fc: ldr             x4, [x4, #0x820]
    // 0x14faa00: r0 = SvgPicture.asset()
    //     0x14faa00: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14faa04: r0 = Center()
    //     0x14faa04: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14faa08: r3 = Instance_Alignment
    //     0x14faa08: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14faa0c: ldr             x3, [x3, #0xb10]
    // 0x14faa10: stur            x0, [fp, #-0x28]
    // 0x14faa14: StoreField: r0->field_f = r3
    //     0x14faa14: stur            w3, [x0, #0xf]
    // 0x14faa18: ldur            x1, [fp, #-0x18]
    // 0x14faa1c: StoreField: r0->field_b = r1
    //     0x14faa1c: stur            w1, [x0, #0xb]
    // 0x14faa20: r0 = Container()
    //     0x14faa20: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14faa24: stur            x0, [fp, #-0x18]
    // 0x14faa28: r16 = inf
    //     0x14faa28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14faa2c: ldr             x16, [x16, #0x9f8]
    // 0x14faa30: r30 = 60.000000
    //     0x14faa30: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14faa34: ldr             lr, [lr, #0x110]
    // 0x14faa38: stp             lr, x16, [SP, #0x10]
    // 0x14faa3c: ldur            x16, [fp, #-0x30]
    // 0x14faa40: ldur            lr, [fp, #-0x28]
    // 0x14faa44: stp             lr, x16, [SP]
    // 0x14faa48: mov             x1, x0
    // 0x14faa4c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14faa4c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14faa50: ldr             x4, [x4, #0x870]
    // 0x14faa54: r0 = Container()
    //     0x14faa54: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14faa58: r0 = InkWell()
    //     0x14faa58: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14faa5c: mov             x3, x0
    // 0x14faa60: ldur            x0, [fp, #-0x18]
    // 0x14faa64: stur            x3, [fp, #-0x28]
    // 0x14faa68: StoreField: r3->field_b = r0
    //     0x14faa68: stur            w0, [x3, #0xb]
    // 0x14faa6c: ldur            x2, [fp, #-0x20]
    // 0x14faa70: r1 = Function '<anonymous closure>':.
    //     0x14faa70: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f358] AnonymousClosure: (0x9a826c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14faa74: ldr             x1, [x1, #0x358]
    // 0x14faa78: r0 = AllocateClosure()
    //     0x14faa78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14faa7c: mov             x1, x0
    // 0x14faa80: ldur            x0, [fp, #-0x28]
    // 0x14faa84: StoreField: r0->field_f = r1
    //     0x14faa84: stur            w1, [x0, #0xf]
    // 0x14faa88: r4 = true
    //     0x14faa88: add             x4, NULL, #0x20  ; true
    // 0x14faa8c: StoreField: r0->field_43 = r4
    //     0x14faa8c: stur            w4, [x0, #0x43]
    // 0x14faa90: r5 = Instance_BoxShape
    //     0x14faa90: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14faa94: ldr             x5, [x5, #0x80]
    // 0x14faa98: StoreField: r0->field_47 = r5
    //     0x14faa98: stur            w5, [x0, #0x47]
    // 0x14faa9c: StoreField: r0->field_6f = r4
    //     0x14faa9c: stur            w4, [x0, #0x6f]
    // 0x14faaa0: r6 = false
    //     0x14faaa0: add             x6, NULL, #0x30  ; false
    // 0x14faaa4: StoreField: r0->field_73 = r6
    //     0x14faaa4: stur            w6, [x0, #0x73]
    // 0x14faaa8: StoreField: r0->field_83 = r4
    //     0x14faaa8: stur            w4, [x0, #0x83]
    // 0x14faaac: StoreField: r0->field_7b = r6
    //     0x14faaac: stur            w6, [x0, #0x7b]
    // 0x14faab0: b               #0x14fac44
    // 0x14faab4: ldur            x2, [fp, #-0x20]
    // 0x14faab8: ldur            x1, [fp, #-8]
    // 0x14faabc: r0 = controller()
    //     0x14faabc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14faac0: LoadField: r1 = r0->field_73
    //     0x14faac0: ldur            w1, [x0, #0x73]
    // 0x14faac4: DecompressPointer r1
    //     0x14faac4: add             x1, x1, HEAP, lsl #32
    // 0x14faac8: r0 = value()
    //     0x14faac8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14faacc: r1 = LoadClassIdInstr(r0)
    //     0x14faacc: ldur            x1, [x0, #-1]
    //     0x14faad0: ubfx            x1, x1, #0xc, #0x14
    // 0x14faad4: str             x0, [SP]
    // 0x14faad8: mov             x0, x1
    // 0x14faadc: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14faadc: movz            x17, #0xc898
    //     0x14faae0: add             lr, x0, x17
    //     0x14faae4: ldr             lr, [x21, lr, lsl #3]
    //     0x14faae8: blr             lr
    // 0x14faaec: ldur            x2, [fp, #-0x20]
    // 0x14faaf0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14faaf0: ldur            w1, [x2, #0x17]
    // 0x14faaf4: DecompressPointer r1
    //     0x14faaf4: add             x1, x1, HEAP, lsl #32
    // 0x14faaf8: cmp             w1, NULL
    // 0x14faafc: b.ne            #0x14fab08
    // 0x14fab00: r1 = Null
    //     0x14fab00: mov             x1, NULL
    // 0x14fab04: b               #0x14fab14
    // 0x14fab08: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x14fab08: ldur            w3, [x1, #0x17]
    // 0x14fab0c: DecompressPointer r3
    //     0x14fab0c: add             x3, x3, HEAP, lsl #32
    // 0x14fab10: mov             x1, x3
    // 0x14fab14: cmp             w1, NULL
    // 0x14fab18: b.ne            #0x14fab24
    // 0x14fab1c: r1 = 0
    //     0x14fab1c: movz            x1, #0
    // 0x14fab20: b               #0x14fab34
    // 0x14fab24: r3 = LoadInt32Instr(r1)
    //     0x14fab24: sbfx            x3, x1, #1, #0x1f
    //     0x14fab28: tbz             w1, #0, #0x14fab30
    //     0x14fab2c: ldur            x3, [x1, #7]
    // 0x14fab30: mov             x1, x3
    // 0x14fab34: r3 = LoadInt32Instr(r0)
    //     0x14fab34: sbfx            x3, x0, #1, #0x1f
    //     0x14fab38: tbz             w0, #0, #0x14fab40
    //     0x14fab3c: ldur            x3, [x0, #7]
    // 0x14fab40: cmp             x3, x1
    // 0x14fab44: b.ge            #0x14fab94
    // 0x14fab48: ldur            x1, [fp, #-8]
    // 0x14fab4c: r0 = controller()
    //     0x14fab4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fab50: LoadField: r1 = r0->field_73
    //     0x14fab50: ldur            w1, [x0, #0x73]
    // 0x14fab54: DecompressPointer r1
    //     0x14fab54: add             x1, x1, HEAP, lsl #32
    // 0x14fab58: r0 = value()
    //     0x14fab58: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fab5c: r1 = LoadClassIdInstr(r0)
    //     0x14fab5c: ldur            x1, [x0, #-1]
    //     0x14fab60: ubfx            x1, x1, #0xc, #0x14
    // 0x14fab64: str             x0, [SP]
    // 0x14fab68: mov             x0, x1
    // 0x14fab6c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fab6c: movz            x17, #0xc898
    //     0x14fab70: add             lr, x0, x17
    //     0x14fab74: ldr             lr, [x21, lr, lsl #3]
    //     0x14fab78: blr             lr
    // 0x14fab7c: r1 = LoadInt32Instr(r0)
    //     0x14fab7c: sbfx            x1, x0, #1, #0x1f
    //     0x14fab80: tbz             w0, #0, #0x14fab88
    //     0x14fab84: ldur            x1, [x0, #7]
    // 0x14fab88: add             x0, x1, #1
    // 0x14fab8c: mov             x3, x0
    // 0x14fab90: b               #0x14fabd8
    // 0x14fab94: ldur            x1, [fp, #-8]
    // 0x14fab98: r0 = controller()
    //     0x14fab98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fab9c: LoadField: r1 = r0->field_73
    //     0x14fab9c: ldur            w1, [x0, #0x73]
    // 0x14faba0: DecompressPointer r1
    //     0x14faba0: add             x1, x1, HEAP, lsl #32
    // 0x14faba4: r0 = value()
    //     0x14faba4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14faba8: r1 = LoadClassIdInstr(r0)
    //     0x14faba8: ldur            x1, [x0, #-1]
    //     0x14fabac: ubfx            x1, x1, #0xc, #0x14
    // 0x14fabb0: str             x0, [SP]
    // 0x14fabb4: mov             x0, x1
    // 0x14fabb8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fabb8: movz            x17, #0xc898
    //     0x14fabbc: add             lr, x0, x17
    //     0x14fabc0: ldr             lr, [x21, lr, lsl #3]
    //     0x14fabc4: blr             lr
    // 0x14fabc8: r1 = LoadInt32Instr(r0)
    //     0x14fabc8: sbfx            x1, x0, #1, #0x1f
    //     0x14fabcc: tbz             w0, #0, #0x14fabd4
    //     0x14fabd0: ldur            x1, [x0, #7]
    // 0x14fabd4: mov             x3, x1
    // 0x14fabd8: stur            x3, [fp, #-0x38]
    // 0x14fabdc: r1 = Function '<anonymous closure>':.
    //     0x14fabdc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f360] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14fabe0: ldr             x1, [x1, #0x360]
    // 0x14fabe4: r2 = Null
    //     0x14fabe4: mov             x2, NULL
    // 0x14fabe8: r0 = AllocateClosure()
    //     0x14fabe8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fabec: ldur            x2, [fp, #-0x20]
    // 0x14fabf0: r1 = Function '<anonymous closure>':.
    //     0x14fabf0: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f368] AnonymousClosure: (0x14fba18), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14fa49c)
    //     0x14fabf4: ldr             x1, [x1, #0x368]
    // 0x14fabf8: stur            x0, [fp, #-0x18]
    // 0x14fabfc: r0 = AllocateClosure()
    //     0x14fabfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fac00: stur            x0, [fp, #-0x28]
    // 0x14fac04: r0 = ListView()
    //     0x14fac04: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14fac08: stur            x0, [fp, #-0x30]
    // 0x14fac0c: r16 = Instance_BouncingScrollPhysics
    //     0x14fac0c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x14fac10: ldr             x16, [x16, #0x890]
    // 0x14fac14: r30 = true
    //     0x14fac14: add             lr, NULL, #0x20  ; true
    // 0x14fac18: stp             lr, x16, [SP, #8]
    // 0x14fac1c: r16 = Instance_Axis
    //     0x14fac1c: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14fac20: str             x16, [SP]
    // 0x14fac24: mov             x1, x0
    // 0x14fac28: ldur            x2, [fp, #-0x28]
    // 0x14fac2c: ldur            x3, [fp, #-0x38]
    // 0x14fac30: ldur            x5, [fp, #-0x18]
    // 0x14fac34: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x6, shrinkWrap, 0x5, null]
    //     0x14fac34: add             x4, PP, #0x33, lsl #12  ; [pp+0x33898] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x6, "shrinkWrap", 0x5, Null]
    //     0x14fac38: ldr             x4, [x4, #0x898]
    // 0x14fac3c: r0 = ListView.separated()
    //     0x14fac3c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14fac40: ldur            x0, [fp, #-0x30]
    // 0x14fac44: ldur            x1, [fp, #-0x10]
    // 0x14fac48: stur            x0, [fp, #-0x18]
    // 0x14fac4c: r0 = SizedBox()
    //     0x14fac4c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14fac50: r7 = 60.000000
    //     0x14fac50: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fac54: ldr             x7, [x7, #0x110]
    // 0x14fac58: stur            x0, [fp, #-0x28]
    // 0x14fac5c: StoreField: r0->field_13 = r7
    //     0x14fac5c: stur            w7, [x0, #0x13]
    // 0x14fac60: ldur            x1, [fp, #-0x18]
    // 0x14fac64: StoreField: r0->field_b = r1
    //     0x14fac64: stur            w1, [x0, #0xb]
    // 0x14fac68: r0 = Padding()
    //     0x14fac68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fac6c: r8 = Instance_EdgeInsets
    //     0x14fac6c: add             x8, PP, #0x33, lsl #12  ; [pp+0x338a0] Obj!EdgeInsets@d575f1
    //     0x14fac70: ldr             x8, [x8, #0x8a0]
    // 0x14fac74: stur            x0, [fp, #-0x18]
    // 0x14fac78: StoreField: r0->field_f = r8
    //     0x14fac78: stur            w8, [x0, #0xf]
    // 0x14fac7c: ldur            x1, [fp, #-0x28]
    // 0x14fac80: StoreField: r0->field_b = r1
    //     0x14fac80: stur            w1, [x0, #0xb]
    // 0x14fac84: ldur            x2, [fp, #-0x10]
    // 0x14fac88: LoadField: r1 = r2->field_b
    //     0x14fac88: ldur            w1, [x2, #0xb]
    // 0x14fac8c: LoadField: r3 = r2->field_f
    //     0x14fac8c: ldur            w3, [x2, #0xf]
    // 0x14fac90: DecompressPointer r3
    //     0x14fac90: add             x3, x3, HEAP, lsl #32
    // 0x14fac94: LoadField: r4 = r3->field_b
    //     0x14fac94: ldur            w4, [x3, #0xb]
    // 0x14fac98: r3 = LoadInt32Instr(r1)
    //     0x14fac98: sbfx            x3, x1, #1, #0x1f
    // 0x14fac9c: stur            x3, [fp, #-0x38]
    // 0x14faca0: r1 = LoadInt32Instr(r4)
    //     0x14faca0: sbfx            x1, x4, #1, #0x1f
    // 0x14faca4: cmp             x3, x1
    // 0x14faca8: b.ne            #0x14facb4
    // 0x14facac: mov             x1, x2
    // 0x14facb0: r0 = _growToNextCapacity()
    //     0x14facb0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14facb4: ldur            x9, [fp, #-0x10]
    // 0x14facb8: ldur            x2, [fp, #-0x38]
    // 0x14facbc: add             x0, x2, #1
    // 0x14facc0: lsl             x1, x0, #1
    // 0x14facc4: StoreField: r9->field_b = r1
    //     0x14facc4: stur            w1, [x9, #0xb]
    // 0x14facc8: LoadField: r1 = r9->field_f
    //     0x14facc8: ldur            w1, [x9, #0xf]
    // 0x14faccc: DecompressPointer r1
    //     0x14faccc: add             x1, x1, HEAP, lsl #32
    // 0x14facd0: ldur            x0, [fp, #-0x18]
    // 0x14facd4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x14facd4: add             x25, x1, x2, lsl #2
    //     0x14facd8: add             x25, x25, #0xf
    //     0x14facdc: str             w0, [x25]
    //     0x14face0: tbz             w0, #0, #0x14facfc
    //     0x14face4: ldurb           w16, [x1, #-1]
    //     0x14face8: ldurb           w17, [x0, #-1]
    //     0x14facec: and             x16, x17, x16, lsr #2
    //     0x14facf0: tst             x16, HEAP, lsr #32
    //     0x14facf4: b.eq            #0x14facfc
    //     0x14facf8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14facfc: mov             x2, x9
    // 0x14fad00: b               #0x14fb17c
    // 0x14fad04: ldur            x9, [fp, #-0x10]
    // 0x14fad08: r4 = true
    //     0x14fad08: add             x4, NULL, #0x20  ; true
    // 0x14fad0c: r7 = 60.000000
    //     0x14fad0c: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fad10: ldr             x7, [x7, #0x110]
    // 0x14fad14: r8 = Instance_EdgeInsets
    //     0x14fad14: add             x8, PP, #0x33, lsl #12  ; [pp+0x338a0] Obj!EdgeInsets@d575f1
    //     0x14fad18: ldr             x8, [x8, #0x8a0]
    // 0x14fad1c: r5 = Instance_BoxShape
    //     0x14fad1c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fad20: ldr             x5, [x5, #0x80]
    // 0x14fad24: r0 = Instance_BlendMode
    //     0x14fad24: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14fad28: ldr             x0, [x0, #0xb30]
    // 0x14fad2c: r6 = false
    //     0x14fad2c: add             x6, NULL, #0x30  ; false
    // 0x14fad30: r3 = Instance_Alignment
    //     0x14fad30: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14fad34: ldr             x3, [x3, #0xb10]
    // 0x14fad38: d0 = 12.000000
    //     0x14fad38: fmov            d0, #12.00000000
    // 0x14fad3c: r2 = 1
    //     0x14fad3c: movz            x2, #0x1
    // 0x14fad40: ldur            x1, [fp, #-8]
    // 0x14fad44: r0 = controller()
    //     0x14fad44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fad48: LoadField: r1 = r0->field_7b
    //     0x14fad48: ldur            w1, [x0, #0x7b]
    // 0x14fad4c: DecompressPointer r1
    //     0x14fad4c: add             x1, x1, HEAP, lsl #32
    // 0x14fad50: r0 = value()
    //     0x14fad50: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fad54: r1 = LoadClassIdInstr(r0)
    //     0x14fad54: ldur            x1, [x0, #-1]
    //     0x14fad58: ubfx            x1, x1, #0xc, #0x14
    // 0x14fad5c: str             x0, [SP]
    // 0x14fad60: mov             x0, x1
    // 0x14fad64: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fad64: movz            x17, #0xc898
    //     0x14fad68: add             lr, x0, x17
    //     0x14fad6c: ldr             lr, [x21, lr, lsl #3]
    //     0x14fad70: blr             lr
    // 0x14fad74: cbnz            w0, #0x14faf2c
    // 0x14fad78: ldur            x2, [fp, #-0x20]
    // 0x14fad7c: r0 = Radius()
    //     0x14fad7c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fad80: d0 = 12.000000
    //     0x14fad80: fmov            d0, #12.00000000
    // 0x14fad84: stur            x0, [fp, #-0x18]
    // 0x14fad88: StoreField: r0->field_7 = d0
    //     0x14fad88: stur            d0, [x0, #7]
    // 0x14fad8c: StoreField: r0->field_f = d0
    //     0x14fad8c: stur            d0, [x0, #0xf]
    // 0x14fad90: r0 = BorderRadius()
    //     0x14fad90: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fad94: mov             x2, x0
    // 0x14fad98: ldur            x0, [fp, #-0x18]
    // 0x14fad9c: stur            x2, [fp, #-0x28]
    // 0x14fada0: StoreField: r2->field_7 = r0
    //     0x14fada0: stur            w0, [x2, #7]
    // 0x14fada4: StoreField: r2->field_b = r0
    //     0x14fada4: stur            w0, [x2, #0xb]
    // 0x14fada8: StoreField: r2->field_f = r0
    //     0x14fada8: stur            w0, [x2, #0xf]
    // 0x14fadac: StoreField: r2->field_13 = r0
    //     0x14fadac: stur            w0, [x2, #0x13]
    // 0x14fadb0: r1 = Instance_Color
    //     0x14fadb0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fadb4: d0 = 0.100000
    //     0x14fadb4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14fadb8: r0 = withOpacity()
    //     0x14fadb8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fadbc: r16 = 1.000000
    //     0x14fadbc: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14fadc0: str             x16, [SP]
    // 0x14fadc4: mov             x2, x0
    // 0x14fadc8: r1 = Null
    //     0x14fadc8: mov             x1, NULL
    // 0x14fadcc: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14fadcc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14fadd0: ldr             x4, [x4, #0x108]
    // 0x14fadd4: r0 = Border.all()
    //     0x14fadd4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14fadd8: stur            x0, [fp, #-0x18]
    // 0x14faddc: r0 = BoxDecoration()
    //     0x14faddc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14fade0: mov             x2, x0
    // 0x14fade4: ldur            x0, [fp, #-0x18]
    // 0x14fade8: stur            x2, [fp, #-0x30]
    // 0x14fadec: StoreField: r2->field_f = r0
    //     0x14fadec: stur            w0, [x2, #0xf]
    // 0x14fadf0: ldur            x0, [fp, #-0x28]
    // 0x14fadf4: StoreField: r2->field_13 = r0
    //     0x14fadf4: stur            w0, [x2, #0x13]
    // 0x14fadf8: r0 = Instance_BoxShape
    //     0x14fadf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fadfc: ldr             x0, [x0, #0x80]
    // 0x14fae00: StoreField: r2->field_23 = r0
    //     0x14fae00: stur            w0, [x2, #0x23]
    // 0x14fae04: ldur            x3, [fp, #-0x20]
    // 0x14fae08: LoadField: r1 = r3->field_13
    //     0x14fae08: ldur            w1, [x3, #0x13]
    // 0x14fae0c: DecompressPointer r1
    //     0x14fae0c: add             x1, x1, HEAP, lsl #32
    // 0x14fae10: r0 = of()
    //     0x14fae10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fae14: LoadField: r1 = r0->field_5b
    //     0x14fae14: ldur            w1, [x0, #0x5b]
    // 0x14fae18: DecompressPointer r1
    //     0x14fae18: add             x1, x1, HEAP, lsl #32
    // 0x14fae1c: stur            x1, [fp, #-0x18]
    // 0x14fae20: r0 = ColorFilter()
    //     0x14fae20: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14fae24: mov             x1, x0
    // 0x14fae28: ldur            x0, [fp, #-0x18]
    // 0x14fae2c: stur            x1, [fp, #-0x28]
    // 0x14fae30: StoreField: r1->field_7 = r0
    //     0x14fae30: stur            w0, [x1, #7]
    // 0x14fae34: r0 = Instance_BlendMode
    //     0x14fae34: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14fae38: ldr             x0, [x0, #0xb30]
    // 0x14fae3c: StoreField: r1->field_b = r0
    //     0x14fae3c: stur            w0, [x1, #0xb]
    // 0x14fae40: r0 = 1
    //     0x14fae40: movz            x0, #0x1
    // 0x14fae44: StoreField: r1->field_13 = r0
    //     0x14fae44: stur            x0, [x1, #0x13]
    // 0x14fae48: r0 = SvgPicture()
    //     0x14fae48: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14fae4c: stur            x0, [fp, #-0x18]
    // 0x14fae50: r16 = Instance_BoxFit
    //     0x14fae50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14fae54: ldr             x16, [x16, #0x118]
    // 0x14fae58: ldur            lr, [fp, #-0x28]
    // 0x14fae5c: stp             lr, x16, [SP]
    // 0x14fae60: mov             x1, x0
    // 0x14fae64: r2 = "assets/images/image_bg.svg"
    //     0x14fae64: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14fae68: ldr             x2, [x2, #0x868]
    // 0x14fae6c: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14fae6c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14fae70: ldr             x4, [x4, #0x820]
    // 0x14fae74: r0 = SvgPicture.asset()
    //     0x14fae74: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14fae78: r0 = Center()
    //     0x14fae78: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14fae7c: mov             x1, x0
    // 0x14fae80: r0 = Instance_Alignment
    //     0x14fae80: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14fae84: ldr             x0, [x0, #0xb10]
    // 0x14fae88: stur            x1, [fp, #-0x28]
    // 0x14fae8c: StoreField: r1->field_f = r0
    //     0x14fae8c: stur            w0, [x1, #0xf]
    // 0x14fae90: ldur            x0, [fp, #-0x18]
    // 0x14fae94: StoreField: r1->field_b = r0
    //     0x14fae94: stur            w0, [x1, #0xb]
    // 0x14fae98: r0 = Container()
    //     0x14fae98: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fae9c: stur            x0, [fp, #-0x18]
    // 0x14faea0: r16 = inf
    //     0x14faea0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14faea4: ldr             x16, [x16, #0x9f8]
    // 0x14faea8: r30 = 60.000000
    //     0x14faea8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14faeac: ldr             lr, [lr, #0x110]
    // 0x14faeb0: stp             lr, x16, [SP, #0x10]
    // 0x14faeb4: ldur            x16, [fp, #-0x30]
    // 0x14faeb8: ldur            lr, [fp, #-0x28]
    // 0x14faebc: stp             lr, x16, [SP]
    // 0x14faec0: mov             x1, x0
    // 0x14faec4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14faec4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14faec8: ldr             x4, [x4, #0x870]
    // 0x14faecc: r0 = Container()
    //     0x14faecc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14faed0: r0 = InkWell()
    //     0x14faed0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14faed4: mov             x3, x0
    // 0x14faed8: ldur            x0, [fp, #-0x18]
    // 0x14faedc: stur            x3, [fp, #-0x28]
    // 0x14faee0: StoreField: r3->field_b = r0
    //     0x14faee0: stur            w0, [x3, #0xb]
    // 0x14faee4: ldur            x2, [fp, #-0x20]
    // 0x14faee8: r1 = Function '<anonymous closure>':.
    //     0x14faee8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f370] AnonymousClosure: (0x9a7888), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14faeec: ldr             x1, [x1, #0x370]
    // 0x14faef0: r0 = AllocateClosure()
    //     0x14faef0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14faef4: mov             x1, x0
    // 0x14faef8: ldur            x0, [fp, #-0x28]
    // 0x14faefc: StoreField: r0->field_f = r1
    //     0x14faefc: stur            w1, [x0, #0xf]
    // 0x14faf00: r1 = true
    //     0x14faf00: add             x1, NULL, #0x20  ; true
    // 0x14faf04: StoreField: r0->field_43 = r1
    //     0x14faf04: stur            w1, [x0, #0x43]
    // 0x14faf08: r2 = Instance_BoxShape
    //     0x14faf08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14faf0c: ldr             x2, [x2, #0x80]
    // 0x14faf10: StoreField: r0->field_47 = r2
    //     0x14faf10: stur            w2, [x0, #0x47]
    // 0x14faf14: StoreField: r0->field_6f = r1
    //     0x14faf14: stur            w1, [x0, #0x6f]
    // 0x14faf18: r2 = false
    //     0x14faf18: add             x2, NULL, #0x30  ; false
    // 0x14faf1c: StoreField: r0->field_73 = r2
    //     0x14faf1c: stur            w2, [x0, #0x73]
    // 0x14faf20: StoreField: r0->field_83 = r1
    //     0x14faf20: stur            w1, [x0, #0x83]
    // 0x14faf24: StoreField: r0->field_7b = r2
    //     0x14faf24: stur            w2, [x0, #0x7b]
    // 0x14faf28: b               #0x14fb0bc
    // 0x14faf2c: ldur            x2, [fp, #-0x20]
    // 0x14faf30: ldur            x1, [fp, #-8]
    // 0x14faf34: r0 = controller()
    //     0x14faf34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14faf38: LoadField: r1 = r0->field_7b
    //     0x14faf38: ldur            w1, [x0, #0x7b]
    // 0x14faf3c: DecompressPointer r1
    //     0x14faf3c: add             x1, x1, HEAP, lsl #32
    // 0x14faf40: r0 = value()
    //     0x14faf40: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14faf44: r1 = LoadClassIdInstr(r0)
    //     0x14faf44: ldur            x1, [x0, #-1]
    //     0x14faf48: ubfx            x1, x1, #0xc, #0x14
    // 0x14faf4c: str             x0, [SP]
    // 0x14faf50: mov             x0, x1
    // 0x14faf54: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14faf54: movz            x17, #0xc898
    //     0x14faf58: add             lr, x0, x17
    //     0x14faf5c: ldr             lr, [x21, lr, lsl #3]
    //     0x14faf60: blr             lr
    // 0x14faf64: ldur            x2, [fp, #-0x20]
    // 0x14faf68: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14faf68: ldur            w1, [x2, #0x17]
    // 0x14faf6c: DecompressPointer r1
    //     0x14faf6c: add             x1, x1, HEAP, lsl #32
    // 0x14faf70: cmp             w1, NULL
    // 0x14faf74: b.ne            #0x14faf80
    // 0x14faf78: r1 = Null
    //     0x14faf78: mov             x1, NULL
    // 0x14faf7c: b               #0x14faf8c
    // 0x14faf80: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x14faf80: ldur            w3, [x1, #0x17]
    // 0x14faf84: DecompressPointer r3
    //     0x14faf84: add             x3, x3, HEAP, lsl #32
    // 0x14faf88: mov             x1, x3
    // 0x14faf8c: cmp             w1, NULL
    // 0x14faf90: b.ne            #0x14faf9c
    // 0x14faf94: r1 = 0
    //     0x14faf94: movz            x1, #0
    // 0x14faf98: b               #0x14fafac
    // 0x14faf9c: r3 = LoadInt32Instr(r1)
    //     0x14faf9c: sbfx            x3, x1, #1, #0x1f
    //     0x14fafa0: tbz             w1, #0, #0x14fafa8
    //     0x14fafa4: ldur            x3, [x1, #7]
    // 0x14fafa8: mov             x1, x3
    // 0x14fafac: r3 = LoadInt32Instr(r0)
    //     0x14fafac: sbfx            x3, x0, #1, #0x1f
    //     0x14fafb0: tbz             w0, #0, #0x14fafb8
    //     0x14fafb4: ldur            x3, [x0, #7]
    // 0x14fafb8: cmp             x3, x1
    // 0x14fafbc: b.ge            #0x14fb00c
    // 0x14fafc0: ldur            x1, [fp, #-8]
    // 0x14fafc4: r0 = controller()
    //     0x14fafc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fafc8: LoadField: r1 = r0->field_7b
    //     0x14fafc8: ldur            w1, [x0, #0x7b]
    // 0x14fafcc: DecompressPointer r1
    //     0x14fafcc: add             x1, x1, HEAP, lsl #32
    // 0x14fafd0: r0 = value()
    //     0x14fafd0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fafd4: r1 = LoadClassIdInstr(r0)
    //     0x14fafd4: ldur            x1, [x0, #-1]
    //     0x14fafd8: ubfx            x1, x1, #0xc, #0x14
    // 0x14fafdc: str             x0, [SP]
    // 0x14fafe0: mov             x0, x1
    // 0x14fafe4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fafe4: movz            x17, #0xc898
    //     0x14fafe8: add             lr, x0, x17
    //     0x14fafec: ldr             lr, [x21, lr, lsl #3]
    //     0x14faff0: blr             lr
    // 0x14faff4: r1 = LoadInt32Instr(r0)
    //     0x14faff4: sbfx            x1, x0, #1, #0x1f
    //     0x14faff8: tbz             w0, #0, #0x14fb000
    //     0x14faffc: ldur            x1, [x0, #7]
    // 0x14fb000: add             x0, x1, #1
    // 0x14fb004: mov             x3, x0
    // 0x14fb008: b               #0x14fb050
    // 0x14fb00c: ldur            x1, [fp, #-8]
    // 0x14fb010: r0 = controller()
    //     0x14fb010: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb014: LoadField: r1 = r0->field_7b
    //     0x14fb014: ldur            w1, [x0, #0x7b]
    // 0x14fb018: DecompressPointer r1
    //     0x14fb018: add             x1, x1, HEAP, lsl #32
    // 0x14fb01c: r0 = value()
    //     0x14fb01c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fb020: r1 = LoadClassIdInstr(r0)
    //     0x14fb020: ldur            x1, [x0, #-1]
    //     0x14fb024: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb028: str             x0, [SP]
    // 0x14fb02c: mov             x0, x1
    // 0x14fb030: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fb030: movz            x17, #0xc898
    //     0x14fb034: add             lr, x0, x17
    //     0x14fb038: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb03c: blr             lr
    // 0x14fb040: r1 = LoadInt32Instr(r0)
    //     0x14fb040: sbfx            x1, x0, #1, #0x1f
    //     0x14fb044: tbz             w0, #0, #0x14fb04c
    //     0x14fb048: ldur            x1, [x0, #7]
    // 0x14fb04c: mov             x3, x1
    // 0x14fb050: stur            x3, [fp, #-0x38]
    // 0x14fb054: r1 = Function '<anonymous closure>':.
    //     0x14fb054: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f378] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14fb058: ldr             x1, [x1, #0x378]
    // 0x14fb05c: r2 = Null
    //     0x14fb05c: mov             x2, NULL
    // 0x14fb060: r0 = AllocateClosure()
    //     0x14fb060: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fb064: ldur            x2, [fp, #-0x20]
    // 0x14fb068: r1 = Function '<anonymous closure>':.
    //     0x14fb068: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f380] AnonymousClosure: (0x14fb1e4), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14fa49c)
    //     0x14fb06c: ldr             x1, [x1, #0x380]
    // 0x14fb070: stur            x0, [fp, #-8]
    // 0x14fb074: r0 = AllocateClosure()
    //     0x14fb074: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fb078: stur            x0, [fp, #-0x18]
    // 0x14fb07c: r0 = ListView()
    //     0x14fb07c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14fb080: stur            x0, [fp, #-0x20]
    // 0x14fb084: r16 = Instance_BouncingScrollPhysics
    //     0x14fb084: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x14fb088: ldr             x16, [x16, #0x890]
    // 0x14fb08c: r30 = true
    //     0x14fb08c: add             lr, NULL, #0x20  ; true
    // 0x14fb090: stp             lr, x16, [SP, #8]
    // 0x14fb094: r16 = Instance_Axis
    //     0x14fb094: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14fb098: str             x16, [SP]
    // 0x14fb09c: mov             x1, x0
    // 0x14fb0a0: ldur            x2, [fp, #-0x18]
    // 0x14fb0a4: ldur            x3, [fp, #-0x38]
    // 0x14fb0a8: ldur            x5, [fp, #-8]
    // 0x14fb0ac: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x6, shrinkWrap, 0x5, null]
    //     0x14fb0ac: add             x4, PP, #0x33, lsl #12  ; [pp+0x33898] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x6, "shrinkWrap", 0x5, Null]
    //     0x14fb0b0: ldr             x4, [x4, #0x898]
    // 0x14fb0b4: r0 = ListView.separated()
    //     0x14fb0b4: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14fb0b8: ldur            x0, [fp, #-0x20]
    // 0x14fb0bc: ldur            x1, [fp, #-0x10]
    // 0x14fb0c0: stur            x0, [fp, #-8]
    // 0x14fb0c4: r0 = SizedBox()
    //     0x14fb0c4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14fb0c8: mov             x1, x0
    // 0x14fb0cc: r0 = 60.000000
    //     0x14fb0cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fb0d0: ldr             x0, [x0, #0x110]
    // 0x14fb0d4: stur            x1, [fp, #-0x18]
    // 0x14fb0d8: StoreField: r1->field_13 = r0
    //     0x14fb0d8: stur            w0, [x1, #0x13]
    // 0x14fb0dc: ldur            x0, [fp, #-8]
    // 0x14fb0e0: StoreField: r1->field_b = r0
    //     0x14fb0e0: stur            w0, [x1, #0xb]
    // 0x14fb0e4: r0 = Padding()
    //     0x14fb0e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fb0e8: mov             x2, x0
    // 0x14fb0ec: r0 = Instance_EdgeInsets
    //     0x14fb0ec: add             x0, PP, #0x33, lsl #12  ; [pp+0x338a0] Obj!EdgeInsets@d575f1
    //     0x14fb0f0: ldr             x0, [x0, #0x8a0]
    // 0x14fb0f4: stur            x2, [fp, #-8]
    // 0x14fb0f8: StoreField: r2->field_f = r0
    //     0x14fb0f8: stur            w0, [x2, #0xf]
    // 0x14fb0fc: ldur            x0, [fp, #-0x18]
    // 0x14fb100: StoreField: r2->field_b = r0
    //     0x14fb100: stur            w0, [x2, #0xb]
    // 0x14fb104: ldur            x0, [fp, #-0x10]
    // 0x14fb108: LoadField: r1 = r0->field_b
    //     0x14fb108: ldur            w1, [x0, #0xb]
    // 0x14fb10c: LoadField: r3 = r0->field_f
    //     0x14fb10c: ldur            w3, [x0, #0xf]
    // 0x14fb110: DecompressPointer r3
    //     0x14fb110: add             x3, x3, HEAP, lsl #32
    // 0x14fb114: LoadField: r4 = r3->field_b
    //     0x14fb114: ldur            w4, [x3, #0xb]
    // 0x14fb118: r3 = LoadInt32Instr(r1)
    //     0x14fb118: sbfx            x3, x1, #1, #0x1f
    // 0x14fb11c: stur            x3, [fp, #-0x38]
    // 0x14fb120: r1 = LoadInt32Instr(r4)
    //     0x14fb120: sbfx            x1, x4, #1, #0x1f
    // 0x14fb124: cmp             x3, x1
    // 0x14fb128: b.ne            #0x14fb134
    // 0x14fb12c: mov             x1, x0
    // 0x14fb130: r0 = _growToNextCapacity()
    //     0x14fb130: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14fb134: ldur            x2, [fp, #-0x10]
    // 0x14fb138: ldur            x3, [fp, #-0x38]
    // 0x14fb13c: add             x0, x3, #1
    // 0x14fb140: lsl             x1, x0, #1
    // 0x14fb144: StoreField: r2->field_b = r1
    //     0x14fb144: stur            w1, [x2, #0xb]
    // 0x14fb148: LoadField: r1 = r2->field_f
    //     0x14fb148: ldur            w1, [x2, #0xf]
    // 0x14fb14c: DecompressPointer r1
    //     0x14fb14c: add             x1, x1, HEAP, lsl #32
    // 0x14fb150: ldur            x0, [fp, #-8]
    // 0x14fb154: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14fb154: add             x25, x1, x3, lsl #2
    //     0x14fb158: add             x25, x25, #0xf
    //     0x14fb15c: str             w0, [x25]
    //     0x14fb160: tbz             w0, #0, #0x14fb17c
    //     0x14fb164: ldurb           w16, [x1, #-1]
    //     0x14fb168: ldurb           w17, [x0, #-1]
    //     0x14fb16c: and             x16, x17, x16, lsr #2
    //     0x14fb170: tst             x16, HEAP, lsr #32
    //     0x14fb174: b.eq            #0x14fb17c
    //     0x14fb178: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14fb17c: r0 = Column()
    //     0x14fb17c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fb180: r1 = Instance_Axis
    //     0x14fb180: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fb184: StoreField: r0->field_f = r1
    //     0x14fb184: stur            w1, [x0, #0xf]
    // 0x14fb188: r1 = Instance_MainAxisAlignment
    //     0x14fb188: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fb18c: ldr             x1, [x1, #0xa08]
    // 0x14fb190: StoreField: r0->field_13 = r1
    //     0x14fb190: stur            w1, [x0, #0x13]
    // 0x14fb194: r1 = Instance_MainAxisSize
    //     0x14fb194: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fb198: ldr             x1, [x1, #0xa10]
    // 0x14fb19c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14fb19c: stur            w1, [x0, #0x17]
    // 0x14fb1a0: r1 = Instance_CrossAxisAlignment
    //     0x14fb1a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14fb1a4: ldr             x1, [x1, #0x890]
    // 0x14fb1a8: StoreField: r0->field_1b = r1
    //     0x14fb1a8: stur            w1, [x0, #0x1b]
    // 0x14fb1ac: r1 = Instance_VerticalDirection
    //     0x14fb1ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fb1b0: ldr             x1, [x1, #0xa20]
    // 0x14fb1b4: StoreField: r0->field_23 = r1
    //     0x14fb1b4: stur            w1, [x0, #0x23]
    // 0x14fb1b8: r1 = Instance_Clip
    //     0x14fb1b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fb1bc: ldr             x1, [x1, #0x38]
    // 0x14fb1c0: StoreField: r0->field_2b = r1
    //     0x14fb1c0: stur            w1, [x0, #0x2b]
    // 0x14fb1c4: StoreField: r0->field_2f = rZR
    //     0x14fb1c4: stur            xzr, [x0, #0x2f]
    // 0x14fb1c8: ldur            x1, [fp, #-0x10]
    // 0x14fb1cc: StoreField: r0->field_b = r1
    //     0x14fb1cc: stur            w1, [x0, #0xb]
    // 0x14fb1d0: LeaveFrame
    //     0x14fb1d0: mov             SP, fp
    //     0x14fb1d4: ldp             fp, lr, [SP], #0x10
    // 0x14fb1d8: ret
    //     0x14fb1d8: ret             
    // 0x14fb1dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fb1dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fb1e0: b               #0x14fa4c0
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14fb1e4, size: 0x518
    // 0x14fb1e4: EnterFrame
    //     0x14fb1e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14fb1e8: mov             fp, SP
    // 0x14fb1ec: AllocStack(0x48)
    //     0x14fb1ec: sub             SP, SP, #0x48
    // 0x14fb1f0: SetupParameters()
    //     0x14fb1f0: ldr             x0, [fp, #0x20]
    //     0x14fb1f4: ldur            w1, [x0, #0x17]
    //     0x14fb1f8: add             x1, x1, HEAP, lsl #32
    //     0x14fb1fc: stur            x1, [fp, #-8]
    // 0x14fb200: CheckStackOverflow
    //     0x14fb200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fb204: cmp             SP, x16
    //     0x14fb208: b.ls            #0x14fb6f4
    // 0x14fb20c: r1 = 1
    //     0x14fb20c: movz            x1, #0x1
    // 0x14fb210: r0 = AllocateContext()
    //     0x14fb210: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fb214: mov             x2, x0
    // 0x14fb218: ldur            x0, [fp, #-8]
    // 0x14fb21c: stur            x2, [fp, #-0x10]
    // 0x14fb220: StoreField: r2->field_b = r0
    //     0x14fb220: stur            w0, [x2, #0xb]
    // 0x14fb224: ldr             x3, [fp, #0x10]
    // 0x14fb228: StoreField: r2->field_f = r3
    //     0x14fb228: stur            w3, [x2, #0xf]
    // 0x14fb22c: LoadField: r1 = r0->field_f
    //     0x14fb22c: ldur            w1, [x0, #0xf]
    // 0x14fb230: DecompressPointer r1
    //     0x14fb230: add             x1, x1, HEAP, lsl #32
    // 0x14fb234: r0 = controller()
    //     0x14fb234: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb238: LoadField: r1 = r0->field_7b
    //     0x14fb238: ldur            w1, [x0, #0x7b]
    // 0x14fb23c: DecompressPointer r1
    //     0x14fb23c: add             x1, x1, HEAP, lsl #32
    // 0x14fb240: r0 = value()
    //     0x14fb240: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fb244: r1 = LoadClassIdInstr(r0)
    //     0x14fb244: ldur            x1, [x0, #-1]
    //     0x14fb248: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb24c: str             x0, [SP]
    // 0x14fb250: mov             x0, x1
    // 0x14fb254: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fb254: movz            x17, #0xc898
    //     0x14fb258: add             lr, x0, x17
    //     0x14fb25c: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb260: blr             lr
    // 0x14fb264: mov             x1, x0
    // 0x14fb268: ldr             x0, [fp, #0x10]
    // 0x14fb26c: r2 = LoadInt32Instr(r0)
    //     0x14fb26c: sbfx            x2, x0, #1, #0x1f
    //     0x14fb270: tbz             w0, #0, #0x14fb278
    //     0x14fb274: ldur            x2, [x0, #7]
    // 0x14fb278: r0 = LoadInt32Instr(r1)
    //     0x14fb278: sbfx            x0, x1, #1, #0x1f
    //     0x14fb27c: tbz             w1, #0, #0x14fb284
    //     0x14fb280: ldur            x0, [x1, #7]
    // 0x14fb284: cmp             x2, x0
    // 0x14fb288: b.ne            #0x14fb4e0
    // 0x14fb28c: ldur            x0, [fp, #-8]
    // 0x14fb290: LoadField: r1 = r0->field_f
    //     0x14fb290: ldur            w1, [x0, #0xf]
    // 0x14fb294: DecompressPointer r1
    //     0x14fb294: add             x1, x1, HEAP, lsl #32
    // 0x14fb298: r0 = controller()
    //     0x14fb298: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb29c: LoadField: r1 = r0->field_7b
    //     0x14fb29c: ldur            w1, [x0, #0x7b]
    // 0x14fb2a0: DecompressPointer r1
    //     0x14fb2a0: add             x1, x1, HEAP, lsl #32
    // 0x14fb2a4: r0 = value()
    //     0x14fb2a4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fb2a8: r1 = LoadClassIdInstr(r0)
    //     0x14fb2a8: ldur            x1, [x0, #-1]
    //     0x14fb2ac: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb2b0: str             x0, [SP]
    // 0x14fb2b4: mov             x0, x1
    // 0x14fb2b8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fb2b8: movz            x17, #0xc898
    //     0x14fb2bc: add             lr, x0, x17
    //     0x14fb2c0: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb2c4: blr             lr
    // 0x14fb2c8: mov             x1, x0
    // 0x14fb2cc: ldur            x0, [fp, #-8]
    // 0x14fb2d0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14fb2d0: ldur            w2, [x0, #0x17]
    // 0x14fb2d4: DecompressPointer r2
    //     0x14fb2d4: add             x2, x2, HEAP, lsl #32
    // 0x14fb2d8: cmp             w2, NULL
    // 0x14fb2dc: b.ne            #0x14fb2e8
    // 0x14fb2e0: r2 = Null
    //     0x14fb2e0: mov             x2, NULL
    // 0x14fb2e4: b               #0x14fb2f4
    // 0x14fb2e8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14fb2e8: ldur            w3, [x2, #0x17]
    // 0x14fb2ec: DecompressPointer r3
    //     0x14fb2ec: add             x3, x3, HEAP, lsl #32
    // 0x14fb2f0: mov             x2, x3
    // 0x14fb2f4: cmp             w2, NULL
    // 0x14fb2f8: b.ne            #0x14fb304
    // 0x14fb2fc: r2 = 0
    //     0x14fb2fc: movz            x2, #0
    // 0x14fb300: b               #0x14fb314
    // 0x14fb304: r3 = LoadInt32Instr(r2)
    //     0x14fb304: sbfx            x3, x2, #1, #0x1f
    //     0x14fb308: tbz             w2, #0, #0x14fb310
    //     0x14fb30c: ldur            x3, [x2, #7]
    // 0x14fb310: mov             x2, x3
    // 0x14fb314: r3 = LoadInt32Instr(r1)
    //     0x14fb314: sbfx            x3, x1, #1, #0x1f
    //     0x14fb318: tbz             w1, #0, #0x14fb320
    //     0x14fb31c: ldur            x3, [x1, #7]
    // 0x14fb320: cmp             x3, x2
    // 0x14fb324: b.ge            #0x14fb4d8
    // 0x14fb328: r0 = Radius()
    //     0x14fb328: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fb32c: d0 = 12.000000
    //     0x14fb32c: fmov            d0, #12.00000000
    // 0x14fb330: stur            x0, [fp, #-0x18]
    // 0x14fb334: StoreField: r0->field_7 = d0
    //     0x14fb334: stur            d0, [x0, #7]
    // 0x14fb338: StoreField: r0->field_f = d0
    //     0x14fb338: stur            d0, [x0, #0xf]
    // 0x14fb33c: r0 = BorderRadius()
    //     0x14fb33c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fb340: mov             x2, x0
    // 0x14fb344: ldur            x0, [fp, #-0x18]
    // 0x14fb348: stur            x2, [fp, #-0x20]
    // 0x14fb34c: StoreField: r2->field_7 = r0
    //     0x14fb34c: stur            w0, [x2, #7]
    // 0x14fb350: StoreField: r2->field_b = r0
    //     0x14fb350: stur            w0, [x2, #0xb]
    // 0x14fb354: StoreField: r2->field_f = r0
    //     0x14fb354: stur            w0, [x2, #0xf]
    // 0x14fb358: StoreField: r2->field_13 = r0
    //     0x14fb358: stur            w0, [x2, #0x13]
    // 0x14fb35c: r1 = Instance_Color
    //     0x14fb35c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fb360: d0 = 0.100000
    //     0x14fb360: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14fb364: r0 = withOpacity()
    //     0x14fb364: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fb368: r16 = 1.000000
    //     0x14fb368: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14fb36c: str             x16, [SP]
    // 0x14fb370: mov             x2, x0
    // 0x14fb374: r1 = Null
    //     0x14fb374: mov             x1, NULL
    // 0x14fb378: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14fb378: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14fb37c: ldr             x4, [x4, #0x108]
    // 0x14fb380: r0 = Border.all()
    //     0x14fb380: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14fb384: stur            x0, [fp, #-0x18]
    // 0x14fb388: r0 = BoxDecoration()
    //     0x14fb388: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14fb38c: mov             x2, x0
    // 0x14fb390: ldur            x0, [fp, #-0x18]
    // 0x14fb394: stur            x2, [fp, #-0x28]
    // 0x14fb398: StoreField: r2->field_f = r0
    //     0x14fb398: stur            w0, [x2, #0xf]
    // 0x14fb39c: ldur            x0, [fp, #-0x20]
    // 0x14fb3a0: StoreField: r2->field_13 = r0
    //     0x14fb3a0: stur            w0, [x2, #0x13]
    // 0x14fb3a4: r0 = Instance_BoxShape
    //     0x14fb3a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fb3a8: ldr             x0, [x0, #0x80]
    // 0x14fb3ac: StoreField: r2->field_23 = r0
    //     0x14fb3ac: stur            w0, [x2, #0x23]
    // 0x14fb3b0: ldr             x1, [fp, #0x18]
    // 0x14fb3b4: r0 = of()
    //     0x14fb3b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fb3b8: LoadField: r1 = r0->field_5b
    //     0x14fb3b8: ldur            w1, [x0, #0x5b]
    // 0x14fb3bc: DecompressPointer r1
    //     0x14fb3bc: add             x1, x1, HEAP, lsl #32
    // 0x14fb3c0: stur            x1, [fp, #-0x18]
    // 0x14fb3c4: r0 = ColorFilter()
    //     0x14fb3c4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14fb3c8: mov             x1, x0
    // 0x14fb3cc: ldur            x0, [fp, #-0x18]
    // 0x14fb3d0: stur            x1, [fp, #-0x20]
    // 0x14fb3d4: StoreField: r1->field_7 = r0
    //     0x14fb3d4: stur            w0, [x1, #7]
    // 0x14fb3d8: r0 = Instance_BlendMode
    //     0x14fb3d8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14fb3dc: ldr             x0, [x0, #0xb30]
    // 0x14fb3e0: StoreField: r1->field_b = r0
    //     0x14fb3e0: stur            w0, [x1, #0xb]
    // 0x14fb3e4: r0 = 1
    //     0x14fb3e4: movz            x0, #0x1
    // 0x14fb3e8: StoreField: r1->field_13 = r0
    //     0x14fb3e8: stur            x0, [x1, #0x13]
    // 0x14fb3ec: r0 = SvgPicture()
    //     0x14fb3ec: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14fb3f0: stur            x0, [fp, #-0x18]
    // 0x14fb3f4: r16 = Instance_BoxFit
    //     0x14fb3f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14fb3f8: ldr             x16, [x16, #0x118]
    // 0x14fb3fc: ldur            lr, [fp, #-0x20]
    // 0x14fb400: stp             lr, x16, [SP]
    // 0x14fb404: mov             x1, x0
    // 0x14fb408: r2 = "assets/images/image_bg.svg"
    //     0x14fb408: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14fb40c: ldr             x2, [x2, #0x868]
    // 0x14fb410: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14fb410: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14fb414: ldr             x4, [x4, #0x820]
    // 0x14fb418: r0 = SvgPicture.asset()
    //     0x14fb418: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14fb41c: r0 = Center()
    //     0x14fb41c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14fb420: mov             x1, x0
    // 0x14fb424: r0 = Instance_Alignment
    //     0x14fb424: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14fb428: ldr             x0, [x0, #0xb10]
    // 0x14fb42c: stur            x1, [fp, #-0x20]
    // 0x14fb430: StoreField: r1->field_f = r0
    //     0x14fb430: stur            w0, [x1, #0xf]
    // 0x14fb434: ldur            x0, [fp, #-0x18]
    // 0x14fb438: StoreField: r1->field_b = r0
    //     0x14fb438: stur            w0, [x1, #0xb]
    // 0x14fb43c: r0 = Container()
    //     0x14fb43c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fb440: stur            x0, [fp, #-0x18]
    // 0x14fb444: r16 = 60.000000
    //     0x14fb444: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fb448: ldr             x16, [x16, #0x110]
    // 0x14fb44c: r30 = 60.000000
    //     0x14fb44c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fb450: ldr             lr, [lr, #0x110]
    // 0x14fb454: stp             lr, x16, [SP, #0x10]
    // 0x14fb458: ldur            x16, [fp, #-0x28]
    // 0x14fb45c: ldur            lr, [fp, #-0x20]
    // 0x14fb460: stp             lr, x16, [SP]
    // 0x14fb464: mov             x1, x0
    // 0x14fb468: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x14fb468: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x14fb46c: ldr             x4, [x4, #0x8c0]
    // 0x14fb470: r0 = Container()
    //     0x14fb470: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14fb474: r0 = InkWell()
    //     0x14fb474: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14fb478: mov             x3, x0
    // 0x14fb47c: ldur            x0, [fp, #-0x18]
    // 0x14fb480: stur            x3, [fp, #-0x20]
    // 0x14fb484: StoreField: r3->field_b = r0
    //     0x14fb484: stur            w0, [x3, #0xb]
    // 0x14fb488: ldur            x2, [fp, #-0x10]
    // 0x14fb48c: r1 = Function '<anonymous closure>':.
    //     0x14fb48c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f388] AnonymousClosure: (0x9a5e48), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14fb490: ldr             x1, [x1, #0x388]
    // 0x14fb494: r0 = AllocateClosure()
    //     0x14fb494: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fb498: mov             x1, x0
    // 0x14fb49c: ldur            x0, [fp, #-0x20]
    // 0x14fb4a0: StoreField: r0->field_f = r1
    //     0x14fb4a0: stur            w1, [x0, #0xf]
    // 0x14fb4a4: r1 = true
    //     0x14fb4a4: add             x1, NULL, #0x20  ; true
    // 0x14fb4a8: StoreField: r0->field_43 = r1
    //     0x14fb4a8: stur            w1, [x0, #0x43]
    // 0x14fb4ac: r2 = Instance_BoxShape
    //     0x14fb4ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fb4b0: ldr             x2, [x2, #0x80]
    // 0x14fb4b4: StoreField: r0->field_47 = r2
    //     0x14fb4b4: stur            w2, [x0, #0x47]
    // 0x14fb4b8: StoreField: r0->field_6f = r1
    //     0x14fb4b8: stur            w1, [x0, #0x6f]
    // 0x14fb4bc: r2 = false
    //     0x14fb4bc: add             x2, NULL, #0x30  ; false
    // 0x14fb4c0: StoreField: r0->field_73 = r2
    //     0x14fb4c0: stur            w2, [x0, #0x73]
    // 0x14fb4c4: StoreField: r0->field_83 = r1
    //     0x14fb4c4: stur            w1, [x0, #0x83]
    // 0x14fb4c8: StoreField: r0->field_7b = r2
    //     0x14fb4c8: stur            w2, [x0, #0x7b]
    // 0x14fb4cc: LeaveFrame
    //     0x14fb4cc: mov             SP, fp
    //     0x14fb4d0: ldp             fp, lr, [SP], #0x10
    // 0x14fb4d4: ret
    //     0x14fb4d4: ret             
    // 0x14fb4d8: d0 = 12.000000
    //     0x14fb4d8: fmov            d0, #12.00000000
    // 0x14fb4dc: b               #0x14fb4e8
    // 0x14fb4e0: ldur            x0, [fp, #-8]
    // 0x14fb4e4: d0 = 12.000000
    //     0x14fb4e4: fmov            d0, #12.00000000
    // 0x14fb4e8: ldur            x2, [fp, #-0x10]
    // 0x14fb4ec: r0 = Radius()
    //     0x14fb4ec: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fb4f0: d0 = 12.000000
    //     0x14fb4f0: fmov            d0, #12.00000000
    // 0x14fb4f4: stur            x0, [fp, #-0x18]
    // 0x14fb4f8: StoreField: r0->field_7 = d0
    //     0x14fb4f8: stur            d0, [x0, #7]
    // 0x14fb4fc: StoreField: r0->field_f = d0
    //     0x14fb4fc: stur            d0, [x0, #0xf]
    // 0x14fb500: r0 = BorderRadius()
    //     0x14fb500: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fb504: mov             x2, x0
    // 0x14fb508: ldur            x0, [fp, #-0x18]
    // 0x14fb50c: stur            x2, [fp, #-0x20]
    // 0x14fb510: StoreField: r2->field_7 = r0
    //     0x14fb510: stur            w0, [x2, #7]
    // 0x14fb514: StoreField: r2->field_b = r0
    //     0x14fb514: stur            w0, [x2, #0xb]
    // 0x14fb518: StoreField: r2->field_f = r0
    //     0x14fb518: stur            w0, [x2, #0xf]
    // 0x14fb51c: StoreField: r2->field_13 = r0
    //     0x14fb51c: stur            w0, [x2, #0x13]
    // 0x14fb520: ldur            x0, [fp, #-8]
    // 0x14fb524: LoadField: r1 = r0->field_f
    //     0x14fb524: ldur            w1, [x0, #0xf]
    // 0x14fb528: DecompressPointer r1
    //     0x14fb528: add             x1, x1, HEAP, lsl #32
    // 0x14fb52c: r0 = controller()
    //     0x14fb52c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb530: LoadField: r1 = r0->field_7b
    //     0x14fb530: ldur            w1, [x0, #0x7b]
    // 0x14fb534: DecompressPointer r1
    //     0x14fb534: add             x1, x1, HEAP, lsl #32
    // 0x14fb538: ldur            x2, [fp, #-0x10]
    // 0x14fb53c: LoadField: r0 = r2->field_f
    //     0x14fb53c: ldur            w0, [x2, #0xf]
    // 0x14fb540: DecompressPointer r0
    //     0x14fb540: add             x0, x0, HEAP, lsl #32
    // 0x14fb544: stur            x0, [fp, #-8]
    // 0x14fb548: r0 = value()
    //     0x14fb548: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fb54c: r1 = LoadClassIdInstr(r0)
    //     0x14fb54c: ldur            x1, [x0, #-1]
    //     0x14fb550: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb554: ldur            x16, [fp, #-8]
    // 0x14fb558: stp             x16, x0, [SP]
    // 0x14fb55c: mov             x0, x1
    // 0x14fb560: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14fb560: sub             lr, x0, #0xb7
    //     0x14fb564: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb568: blr             lr
    // 0x14fb56c: stur            x0, [fp, #-8]
    // 0x14fb570: r0 = Image()
    //     0x14fb570: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x14fb574: mov             x1, x0
    // 0x14fb578: ldur            x2, [fp, #-8]
    // 0x14fb57c: d0 = 60.000000
    //     0x14fb57c: ldr             d0, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14fb580: d1 = 60.000000
    //     0x14fb580: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14fb584: stur            x0, [fp, #-8]
    // 0x14fb588: r0 = Image.memory()
    //     0x14fb588: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x14fb58c: r0 = ClipRRect()
    //     0x14fb58c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14fb590: mov             x2, x0
    // 0x14fb594: ldur            x0, [fp, #-0x20]
    // 0x14fb598: stur            x2, [fp, #-0x18]
    // 0x14fb59c: StoreField: r2->field_f = r0
    //     0x14fb59c: stur            w0, [x2, #0xf]
    // 0x14fb5a0: r0 = Instance_Clip
    //     0x14fb5a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14fb5a4: ldr             x0, [x0, #0x138]
    // 0x14fb5a8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fb5a8: stur            w0, [x2, #0x17]
    // 0x14fb5ac: ldur            x0, [fp, #-8]
    // 0x14fb5b0: StoreField: r2->field_b = r0
    //     0x14fb5b0: stur            w0, [x2, #0xb]
    // 0x14fb5b4: ldr             x1, [fp, #0x18]
    // 0x14fb5b8: r0 = of()
    //     0x14fb5b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fb5bc: LoadField: r2 = r0->field_5b
    //     0x14fb5bc: ldur            w2, [x0, #0x5b]
    // 0x14fb5c0: DecompressPointer r2
    //     0x14fb5c0: add             x2, x2, HEAP, lsl #32
    // 0x14fb5c4: r16 = 1.000000
    //     0x14fb5c4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14fb5c8: str             x16, [SP]
    // 0x14fb5cc: r1 = Null
    //     0x14fb5cc: mov             x1, NULL
    // 0x14fb5d0: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14fb5d0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14fb5d4: ldr             x4, [x4, #0x108]
    // 0x14fb5d8: r0 = Border.all()
    //     0x14fb5d8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14fb5dc: stur            x0, [fp, #-8]
    // 0x14fb5e0: r0 = BoxDecoration()
    //     0x14fb5e0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14fb5e4: mov             x1, x0
    // 0x14fb5e8: ldur            x0, [fp, #-8]
    // 0x14fb5ec: stur            x1, [fp, #-0x20]
    // 0x14fb5f0: StoreField: r1->field_f = r0
    //     0x14fb5f0: stur            w0, [x1, #0xf]
    // 0x14fb5f4: r0 = Instance_BoxShape
    //     0x14fb5f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14fb5f8: ldr             x0, [x0, #0x970]
    // 0x14fb5fc: StoreField: r1->field_23 = r0
    //     0x14fb5fc: stur            w0, [x1, #0x23]
    // 0x14fb600: r0 = Container()
    //     0x14fb600: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fb604: stur            x0, [fp, #-8]
    // 0x14fb608: ldur            x16, [fp, #-0x20]
    // 0x14fb60c: r30 = Instance_Icon
    //     0x14fb60c: add             lr, PP, #0x33, lsl #12  ; [pp+0x338d0] Obj!Icon@d65db1
    //     0x14fb610: ldr             lr, [lr, #0x8d0]
    // 0x14fb614: stp             lr, x16, [SP]
    // 0x14fb618: mov             x1, x0
    // 0x14fb61c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14fb61c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14fb620: ldr             x4, [x4, #0x88]
    // 0x14fb624: r0 = Container()
    //     0x14fb624: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14fb628: r0 = GestureDetector()
    //     0x14fb628: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14fb62c: ldur            x2, [fp, #-0x10]
    // 0x14fb630: r1 = Function '<anonymous closure>':.
    //     0x14fb630: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f390] AnonymousClosure: (0x14fb6fc), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14fa49c)
    //     0x14fb634: ldr             x1, [x1, #0x390]
    // 0x14fb638: stur            x0, [fp, #-0x10]
    // 0x14fb63c: r0 = AllocateClosure()
    //     0x14fb63c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fb640: ldur            x16, [fp, #-8]
    // 0x14fb644: stp             x16, x0, [SP]
    // 0x14fb648: ldur            x1, [fp, #-0x10]
    // 0x14fb64c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x14fb64c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x14fb650: ldr             x4, [x4, #0xaf0]
    // 0x14fb654: r0 = GestureDetector()
    //     0x14fb654: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14fb658: r1 = <StackParentData>
    //     0x14fb658: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14fb65c: ldr             x1, [x1, #0x8e0]
    // 0x14fb660: r0 = Positioned()
    //     0x14fb660: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14fb664: mov             x3, x0
    // 0x14fb668: ldur            x0, [fp, #-0x10]
    // 0x14fb66c: stur            x3, [fp, #-8]
    // 0x14fb670: StoreField: r3->field_b = r0
    //     0x14fb670: stur            w0, [x3, #0xb]
    // 0x14fb674: r1 = Null
    //     0x14fb674: mov             x1, NULL
    // 0x14fb678: r2 = 4
    //     0x14fb678: movz            x2, #0x4
    // 0x14fb67c: r0 = AllocateArray()
    //     0x14fb67c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fb680: mov             x2, x0
    // 0x14fb684: ldur            x0, [fp, #-0x18]
    // 0x14fb688: stur            x2, [fp, #-0x10]
    // 0x14fb68c: StoreField: r2->field_f = r0
    //     0x14fb68c: stur            w0, [x2, #0xf]
    // 0x14fb690: ldur            x0, [fp, #-8]
    // 0x14fb694: StoreField: r2->field_13 = r0
    //     0x14fb694: stur            w0, [x2, #0x13]
    // 0x14fb698: r1 = <Widget>
    //     0x14fb698: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fb69c: r0 = AllocateGrowableArray()
    //     0x14fb69c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fb6a0: mov             x1, x0
    // 0x14fb6a4: ldur            x0, [fp, #-0x10]
    // 0x14fb6a8: stur            x1, [fp, #-8]
    // 0x14fb6ac: StoreField: r1->field_f = r0
    //     0x14fb6ac: stur            w0, [x1, #0xf]
    // 0x14fb6b0: r0 = 4
    //     0x14fb6b0: movz            x0, #0x4
    // 0x14fb6b4: StoreField: r1->field_b = r0
    //     0x14fb6b4: stur            w0, [x1, #0xb]
    // 0x14fb6b8: r0 = Stack()
    //     0x14fb6b8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14fb6bc: r1 = Instance_Alignment
    //     0x14fb6bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14fb6c0: ldr             x1, [x1, #0x950]
    // 0x14fb6c4: StoreField: r0->field_f = r1
    //     0x14fb6c4: stur            w1, [x0, #0xf]
    // 0x14fb6c8: r1 = Instance_StackFit
    //     0x14fb6c8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14fb6cc: ldr             x1, [x1, #0xfa8]
    // 0x14fb6d0: ArrayStore: r0[0] = r1  ; List_4
    //     0x14fb6d0: stur            w1, [x0, #0x17]
    // 0x14fb6d4: r1 = Instance_Clip
    //     0x14fb6d4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14fb6d8: ldr             x1, [x1, #0x7e0]
    // 0x14fb6dc: StoreField: r0->field_1b = r1
    //     0x14fb6dc: stur            w1, [x0, #0x1b]
    // 0x14fb6e0: ldur            x1, [fp, #-8]
    // 0x14fb6e4: StoreField: r0->field_b = r1
    //     0x14fb6e4: stur            w1, [x0, #0xb]
    // 0x14fb6e8: LeaveFrame
    //     0x14fb6e8: mov             SP, fp
    //     0x14fb6ec: ldp             fp, lr, [SP], #0x10
    // 0x14fb6f0: ret
    //     0x14fb6f0: ret             
    // 0x14fb6f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fb6f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fb6f8: b               #0x14fb20c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14fb6fc, size: 0x31c
    // 0x14fb6fc: EnterFrame
    //     0x14fb6fc: stp             fp, lr, [SP, #-0x10]!
    //     0x14fb700: mov             fp, SP
    // 0x14fb704: AllocStack(0x38)
    //     0x14fb704: sub             SP, SP, #0x38
    // 0x14fb708: SetupParameters(ReturnOrderWithProofView this /* r1 */)
    //     0x14fb708: stur            NULL, [fp, #-8]
    //     0x14fb70c: movz            x0, #0
    //     0x14fb710: add             x1, fp, w0, sxtw #2
    //     0x14fb714: ldr             x1, [x1, #0x10]
    //     0x14fb718: ldur            w2, [x1, #0x17]
    //     0x14fb71c: add             x2, x2, HEAP, lsl #32
    //     0x14fb720: stur            x2, [fp, #-0x10]
    // 0x14fb724: CheckStackOverflow
    //     0x14fb724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fb728: cmp             SP, x16
    //     0x14fb72c: b.ls            #0x14fba0c
    // 0x14fb730: InitAsync() -> Future<void?>
    //     0x14fb730: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14fb734: bl              #0x6326e0  ; InitAsyncStub
    // 0x14fb738: ldur            x0, [fp, #-0x10]
    // 0x14fb73c: LoadField: r2 = r0->field_b
    //     0x14fb73c: ldur            w2, [x0, #0xb]
    // 0x14fb740: DecompressPointer r2
    //     0x14fb740: add             x2, x2, HEAP, lsl #32
    // 0x14fb744: stur            x2, [fp, #-0x18]
    // 0x14fb748: LoadField: r1 = r2->field_f
    //     0x14fb748: ldur            w1, [x2, #0xf]
    // 0x14fb74c: DecompressPointer r1
    //     0x14fb74c: add             x1, x1, HEAP, lsl #32
    // 0x14fb750: r0 = controller()
    //     0x14fb750: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb754: LoadField: r1 = r0->field_77
    //     0x14fb754: ldur            w1, [x0, #0x77]
    // 0x14fb758: DecompressPointer r1
    //     0x14fb758: add             x1, x1, HEAP, lsl #32
    // 0x14fb75c: ldur            x0, [fp, #-0x10]
    // 0x14fb760: LoadField: r2 = r0->field_f
    //     0x14fb760: ldur            w2, [x0, #0xf]
    // 0x14fb764: DecompressPointer r2
    //     0x14fb764: add             x2, x2, HEAP, lsl #32
    // 0x14fb768: stur            x2, [fp, #-0x20]
    // 0x14fb76c: r0 = value()
    //     0x14fb76c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fb770: r1 = LoadClassIdInstr(r0)
    //     0x14fb770: ldur            x1, [x0, #-1]
    //     0x14fb774: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb778: ldur            x16, [fp, #-0x20]
    // 0x14fb77c: stp             x16, x0, [SP]
    // 0x14fb780: mov             x0, x1
    // 0x14fb784: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14fb784: sub             lr, x0, #0xb7
    //     0x14fb788: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb78c: blr             lr
    // 0x14fb790: r1 = LoadClassIdInstr(r0)
    //     0x14fb790: ldur            x1, [x0, #-1]
    //     0x14fb794: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb798: mov             x16, x0
    // 0x14fb79c: mov             x0, x1
    // 0x14fb7a0: mov             x1, x16
    // 0x14fb7a4: r0 = GDT[cid_x0 + -0xe96]()
    //     0x14fb7a4: sub             lr, x0, #0xe96
    //     0x14fb7a8: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb7ac: blr             lr
    // 0x14fb7b0: mov             x1, x0
    // 0x14fb7b4: r0 = lookupMimeType()
    //     0x14fb7b4: bl              #0x8ab24c  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0x14fb7b8: cmp             w0, NULL
    // 0x14fb7bc: b.ne            #0x14fb7c8
    // 0x14fb7c0: r2 = Null
    //     0x14fb7c0: mov             x2, NULL
    // 0x14fb7c4: b               #0x14fb7f0
    // 0x14fb7c8: r1 = LoadClassIdInstr(r0)
    //     0x14fb7c8: ldur            x1, [x0, #-1]
    //     0x14fb7cc: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb7d0: mov             x16, x0
    // 0x14fb7d4: mov             x0, x1
    // 0x14fb7d8: mov             x1, x16
    // 0x14fb7dc: r2 = "/"
    //     0x14fb7dc: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x14fb7e0: r0 = GDT[cid_x0 + -0xffc]()
    //     0x14fb7e0: sub             lr, x0, #0xffc
    //     0x14fb7e4: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb7e8: blr             lr
    // 0x14fb7ec: mov             x2, x0
    // 0x14fb7f0: cmp             w2, NULL
    // 0x14fb7f4: b.ne            #0x14fb800
    // 0x14fb7f8: r0 = Null
    //     0x14fb7f8: mov             x0, NULL
    // 0x14fb7fc: b               #0x14fb82c
    // 0x14fb800: LoadField: r0 = r2->field_b
    //     0x14fb800: ldur            w0, [x2, #0xb]
    // 0x14fb804: r1 = LoadInt32Instr(r0)
    //     0x14fb804: sbfx            x1, x0, #1, #0x1f
    // 0x14fb808: mov             x0, x1
    // 0x14fb80c: r1 = 0
    //     0x14fb80c: movz            x1, #0
    // 0x14fb810: cmp             x1, x0
    // 0x14fb814: b.hs            #0x14fba14
    // 0x14fb818: LoadField: r0 = r2->field_f
    //     0x14fb818: ldur            w0, [x2, #0xf]
    // 0x14fb81c: DecompressPointer r0
    //     0x14fb81c: add             x0, x0, HEAP, lsl #32
    // 0x14fb820: LoadField: r1 = r0->field_f
    //     0x14fb820: ldur            w1, [x0, #0xf]
    // 0x14fb824: DecompressPointer r1
    //     0x14fb824: add             x1, x1, HEAP, lsl #32
    // 0x14fb828: mov             x0, x1
    // 0x14fb82c: r1 = LoadClassIdInstr(r0)
    //     0x14fb82c: ldur            x1, [x0, #-1]
    //     0x14fb830: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb834: r16 = "video"
    //     0x14fb834: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x14fb838: ldr             x16, [x16, #0xb50]
    // 0x14fb83c: stp             x16, x0, [SP]
    // 0x14fb840: mov             x0, x1
    // 0x14fb844: mov             lr, x0
    // 0x14fb848: ldr             lr, [x21, lr, lsl #3]
    // 0x14fb84c: blr             lr
    // 0x14fb850: tbnz            w0, #4, #0x14fb874
    // 0x14fb854: ldur            x0, [fp, #-0x18]
    // 0x14fb858: LoadField: r1 = r0->field_f
    //     0x14fb858: ldur            w1, [x0, #0xf]
    // 0x14fb85c: DecompressPointer r1
    //     0x14fb85c: add             x1, x1, HEAP, lsl #32
    // 0x14fb860: r0 = controller()
    //     0x14fb860: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb864: LoadField: r1 = r0->field_87
    //     0x14fb864: ldur            x1, [x0, #0x87]
    // 0x14fb868: sub             x2, x1, #1
    // 0x14fb86c: StoreField: r0->field_87 = r2
    //     0x14fb86c: stur            x2, [x0, #0x87]
    // 0x14fb870: b               #0x14fb890
    // 0x14fb874: ldur            x0, [fp, #-0x18]
    // 0x14fb878: LoadField: r1 = r0->field_f
    //     0x14fb878: ldur            w1, [x0, #0xf]
    // 0x14fb87c: DecompressPointer r1
    //     0x14fb87c: add             x1, x1, HEAP, lsl #32
    // 0x14fb880: r0 = controller()
    //     0x14fb880: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb884: LoadField: r1 = r0->field_7f
    //     0x14fb884: ldur            x1, [x0, #0x7f]
    // 0x14fb888: sub             x2, x1, #1
    // 0x14fb88c: StoreField: r0->field_7f = r2
    //     0x14fb88c: stur            x2, [x0, #0x7f]
    // 0x14fb890: ldur            x2, [fp, #-0x10]
    // 0x14fb894: ldur            x0, [fp, #-0x18]
    // 0x14fb898: LoadField: r1 = r0->field_f
    //     0x14fb898: ldur            w1, [x0, #0xf]
    // 0x14fb89c: DecompressPointer r1
    //     0x14fb89c: add             x1, x1, HEAP, lsl #32
    // 0x14fb8a0: r0 = controller()
    //     0x14fb8a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb8a4: mov             x2, x0
    // 0x14fb8a8: ldur            x0, [fp, #-0x18]
    // 0x14fb8ac: stur            x2, [fp, #-0x20]
    // 0x14fb8b0: LoadField: r1 = r0->field_f
    //     0x14fb8b0: ldur            w1, [x0, #0xf]
    // 0x14fb8b4: DecompressPointer r1
    //     0x14fb8b4: add             x1, x1, HEAP, lsl #32
    // 0x14fb8b8: r0 = controller()
    //     0x14fb8b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb8bc: LoadField: r1 = r0->field_77
    //     0x14fb8bc: ldur            w1, [x0, #0x77]
    // 0x14fb8c0: DecompressPointer r1
    //     0x14fb8c0: add             x1, x1, HEAP, lsl #32
    // 0x14fb8c4: ldur            x0, [fp, #-0x10]
    // 0x14fb8c8: LoadField: r2 = r0->field_f
    //     0x14fb8c8: ldur            w2, [x0, #0xf]
    // 0x14fb8cc: DecompressPointer r2
    //     0x14fb8cc: add             x2, x2, HEAP, lsl #32
    // 0x14fb8d0: stur            x2, [fp, #-0x28]
    // 0x14fb8d4: r0 = value()
    //     0x14fb8d4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fb8d8: r1 = LoadClassIdInstr(r0)
    //     0x14fb8d8: ldur            x1, [x0, #-1]
    //     0x14fb8dc: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb8e0: ldur            x16, [fp, #-0x28]
    // 0x14fb8e4: stp             x16, x0, [SP]
    // 0x14fb8e8: mov             x0, x1
    // 0x14fb8ec: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14fb8ec: sub             lr, x0, #0xb7
    //     0x14fb8f0: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb8f4: blr             lr
    // 0x14fb8f8: r1 = LoadClassIdInstr(r0)
    //     0x14fb8f8: ldur            x1, [x0, #-1]
    //     0x14fb8fc: ubfx            x1, x1, #0xc, #0x14
    // 0x14fb900: mov             x16, x0
    // 0x14fb904: mov             x0, x1
    // 0x14fb908: mov             x1, x16
    // 0x14fb90c: r0 = GDT[cid_x0 + -0xe96]()
    //     0x14fb90c: sub             lr, x0, #0xe96
    //     0x14fb910: ldr             lr, [x21, lr, lsl #3]
    //     0x14fb914: blr             lr
    // 0x14fb918: ldur            x1, [fp, #-0x20]
    // 0x14fb91c: mov             x2, x0
    // 0x14fb920: r0 = getFileSize()
    //     0x14fb920: bl              #0x9a577c  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::getFileSize
    // 0x14fb924: mov             x1, x0
    // 0x14fb928: stur            x1, [fp, #-0x20]
    // 0x14fb92c: r0 = Await()
    //     0x14fb92c: bl              #0x63248c  ; AwaitStub
    // 0x14fb930: mov             x3, x0
    // 0x14fb934: r2 = Null
    //     0x14fb934: mov             x2, NULL
    // 0x14fb938: r1 = Null
    //     0x14fb938: mov             x1, NULL
    // 0x14fb93c: stur            x3, [fp, #-0x20]
    // 0x14fb940: branchIfSmi(r0, 0x14fb968)
    //     0x14fb940: tbz             w0, #0, #0x14fb968
    // 0x14fb944: r4 = LoadClassIdInstr(r0)
    //     0x14fb944: ldur            x4, [x0, #-1]
    //     0x14fb948: ubfx            x4, x4, #0xc, #0x14
    // 0x14fb94c: sub             x4, x4, #0x3c
    // 0x14fb950: cmp             x4, #1
    // 0x14fb954: b.ls            #0x14fb968
    // 0x14fb958: r8 = int
    //     0x14fb958: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14fb95c: r3 = Null
    //     0x14fb95c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f398] Null
    //     0x14fb960: ldr             x3, [x3, #0x398]
    // 0x14fb964: r0 = int()
    //     0x14fb964: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14fb968: ldur            x0, [fp, #-0x18]
    // 0x14fb96c: LoadField: r1 = r0->field_f
    //     0x14fb96c: ldur            w1, [x0, #0xf]
    // 0x14fb970: DecompressPointer r1
    //     0x14fb970: add             x1, x1, HEAP, lsl #32
    // 0x14fb974: r0 = controller()
    //     0x14fb974: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb978: LoadField: r1 = r0->field_cf
    //     0x14fb978: ldur            x1, [x0, #0xcf]
    // 0x14fb97c: ldur            x2, [fp, #-0x20]
    // 0x14fb980: r3 = LoadInt32Instr(r2)
    //     0x14fb980: sbfx            x3, x2, #1, #0x1f
    //     0x14fb984: tbz             w2, #0, #0x14fb98c
    //     0x14fb988: ldur            x3, [x2, #7]
    // 0x14fb98c: sub             x2, x1, x3
    // 0x14fb990: StoreField: r0->field_cf = r2
    //     0x14fb990: stur            x2, [x0, #0xcf]
    // 0x14fb994: ldur            x0, [fp, #-0x18]
    // 0x14fb998: LoadField: r1 = r0->field_f
    //     0x14fb998: ldur            w1, [x0, #0xf]
    // 0x14fb99c: DecompressPointer r1
    //     0x14fb99c: add             x1, x1, HEAP, lsl #32
    // 0x14fb9a0: r0 = controller()
    //     0x14fb9a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb9a4: LoadField: r1 = r0->field_77
    //     0x14fb9a4: ldur            w1, [x0, #0x77]
    // 0x14fb9a8: DecompressPointer r1
    //     0x14fb9a8: add             x1, x1, HEAP, lsl #32
    // 0x14fb9ac: ldur            x0, [fp, #-0x10]
    // 0x14fb9b0: LoadField: r2 = r0->field_f
    //     0x14fb9b0: ldur            w2, [x0, #0xf]
    // 0x14fb9b4: DecompressPointer r2
    //     0x14fb9b4: add             x2, x2, HEAP, lsl #32
    // 0x14fb9b8: r3 = LoadInt32Instr(r2)
    //     0x14fb9b8: sbfx            x3, x2, #1, #0x1f
    //     0x14fb9bc: tbz             w2, #0, #0x14fb9c4
    //     0x14fb9c0: ldur            x3, [x2, #7]
    // 0x14fb9c4: mov             x2, x3
    // 0x14fb9c8: r0 = removeAt()
    //     0x14fb9c8: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14fb9cc: ldur            x0, [fp, #-0x18]
    // 0x14fb9d0: LoadField: r1 = r0->field_f
    //     0x14fb9d0: ldur            w1, [x0, #0xf]
    // 0x14fb9d4: DecompressPointer r1
    //     0x14fb9d4: add             x1, x1, HEAP, lsl #32
    // 0x14fb9d8: r0 = controller()
    //     0x14fb9d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fb9dc: LoadField: r1 = r0->field_7b
    //     0x14fb9dc: ldur            w1, [x0, #0x7b]
    // 0x14fb9e0: DecompressPointer r1
    //     0x14fb9e0: add             x1, x1, HEAP, lsl #32
    // 0x14fb9e4: ldur            x0, [fp, #-0x10]
    // 0x14fb9e8: LoadField: r2 = r0->field_f
    //     0x14fb9e8: ldur            w2, [x0, #0xf]
    // 0x14fb9ec: DecompressPointer r2
    //     0x14fb9ec: add             x2, x2, HEAP, lsl #32
    // 0x14fb9f0: r0 = LoadInt32Instr(r2)
    //     0x14fb9f0: sbfx            x0, x2, #1, #0x1f
    //     0x14fb9f4: tbz             w2, #0, #0x14fb9fc
    //     0x14fb9f8: ldur            x0, [x2, #7]
    // 0x14fb9fc: mov             x2, x0
    // 0x14fba00: r0 = removeAt()
    //     0x14fba00: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14fba04: r0 = Null
    //     0x14fba04: mov             x0, NULL
    // 0x14fba08: r0 = ReturnAsyncNotFuture()
    //     0x14fba08: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14fba0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fba0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fba10: b               #0x14fb730
    // 0x14fba14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14fba14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14fba18, size: 0x518
    // 0x14fba18: EnterFrame
    //     0x14fba18: stp             fp, lr, [SP, #-0x10]!
    //     0x14fba1c: mov             fp, SP
    // 0x14fba20: AllocStack(0x48)
    //     0x14fba20: sub             SP, SP, #0x48
    // 0x14fba24: SetupParameters()
    //     0x14fba24: ldr             x0, [fp, #0x20]
    //     0x14fba28: ldur            w1, [x0, #0x17]
    //     0x14fba2c: add             x1, x1, HEAP, lsl #32
    //     0x14fba30: stur            x1, [fp, #-8]
    // 0x14fba34: CheckStackOverflow
    //     0x14fba34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fba38: cmp             SP, x16
    //     0x14fba3c: b.ls            #0x14fbf28
    // 0x14fba40: r1 = 1
    //     0x14fba40: movz            x1, #0x1
    // 0x14fba44: r0 = AllocateContext()
    //     0x14fba44: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fba48: mov             x2, x0
    // 0x14fba4c: ldur            x0, [fp, #-8]
    // 0x14fba50: stur            x2, [fp, #-0x10]
    // 0x14fba54: StoreField: r2->field_b = r0
    //     0x14fba54: stur            w0, [x2, #0xb]
    // 0x14fba58: ldr             x3, [fp, #0x10]
    // 0x14fba5c: StoreField: r2->field_f = r3
    //     0x14fba5c: stur            w3, [x2, #0xf]
    // 0x14fba60: LoadField: r1 = r0->field_f
    //     0x14fba60: ldur            w1, [x0, #0xf]
    // 0x14fba64: DecompressPointer r1
    //     0x14fba64: add             x1, x1, HEAP, lsl #32
    // 0x14fba68: r0 = controller()
    //     0x14fba68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fba6c: LoadField: r1 = r0->field_73
    //     0x14fba6c: ldur            w1, [x0, #0x73]
    // 0x14fba70: DecompressPointer r1
    //     0x14fba70: add             x1, x1, HEAP, lsl #32
    // 0x14fba74: r0 = value()
    //     0x14fba74: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fba78: r1 = LoadClassIdInstr(r0)
    //     0x14fba78: ldur            x1, [x0, #-1]
    //     0x14fba7c: ubfx            x1, x1, #0xc, #0x14
    // 0x14fba80: str             x0, [SP]
    // 0x14fba84: mov             x0, x1
    // 0x14fba88: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fba88: movz            x17, #0xc898
    //     0x14fba8c: add             lr, x0, x17
    //     0x14fba90: ldr             lr, [x21, lr, lsl #3]
    //     0x14fba94: blr             lr
    // 0x14fba98: mov             x1, x0
    // 0x14fba9c: ldr             x0, [fp, #0x10]
    // 0x14fbaa0: r2 = LoadInt32Instr(r0)
    //     0x14fbaa0: sbfx            x2, x0, #1, #0x1f
    //     0x14fbaa4: tbz             w0, #0, #0x14fbaac
    //     0x14fbaa8: ldur            x2, [x0, #7]
    // 0x14fbaac: r0 = LoadInt32Instr(r1)
    //     0x14fbaac: sbfx            x0, x1, #1, #0x1f
    //     0x14fbab0: tbz             w1, #0, #0x14fbab8
    //     0x14fbab4: ldur            x0, [x1, #7]
    // 0x14fbab8: cmp             x2, x0
    // 0x14fbabc: b.ne            #0x14fbd14
    // 0x14fbac0: ldur            x0, [fp, #-8]
    // 0x14fbac4: LoadField: r1 = r0->field_f
    //     0x14fbac4: ldur            w1, [x0, #0xf]
    // 0x14fbac8: DecompressPointer r1
    //     0x14fbac8: add             x1, x1, HEAP, lsl #32
    // 0x14fbacc: r0 = controller()
    //     0x14fbacc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fbad0: LoadField: r1 = r0->field_73
    //     0x14fbad0: ldur            w1, [x0, #0x73]
    // 0x14fbad4: DecompressPointer r1
    //     0x14fbad4: add             x1, x1, HEAP, lsl #32
    // 0x14fbad8: r0 = value()
    //     0x14fbad8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fbadc: r1 = LoadClassIdInstr(r0)
    //     0x14fbadc: ldur            x1, [x0, #-1]
    //     0x14fbae0: ubfx            x1, x1, #0xc, #0x14
    // 0x14fbae4: str             x0, [SP]
    // 0x14fbae8: mov             x0, x1
    // 0x14fbaec: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fbaec: movz            x17, #0xc898
    //     0x14fbaf0: add             lr, x0, x17
    //     0x14fbaf4: ldr             lr, [x21, lr, lsl #3]
    //     0x14fbaf8: blr             lr
    // 0x14fbafc: mov             x1, x0
    // 0x14fbb00: ldur            x0, [fp, #-8]
    // 0x14fbb04: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14fbb04: ldur            w2, [x0, #0x17]
    // 0x14fbb08: DecompressPointer r2
    //     0x14fbb08: add             x2, x2, HEAP, lsl #32
    // 0x14fbb0c: cmp             w2, NULL
    // 0x14fbb10: b.ne            #0x14fbb1c
    // 0x14fbb14: r2 = Null
    //     0x14fbb14: mov             x2, NULL
    // 0x14fbb18: b               #0x14fbb28
    // 0x14fbb1c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14fbb1c: ldur            w3, [x2, #0x17]
    // 0x14fbb20: DecompressPointer r3
    //     0x14fbb20: add             x3, x3, HEAP, lsl #32
    // 0x14fbb24: mov             x2, x3
    // 0x14fbb28: cmp             w2, NULL
    // 0x14fbb2c: b.ne            #0x14fbb38
    // 0x14fbb30: r2 = 0
    //     0x14fbb30: movz            x2, #0
    // 0x14fbb34: b               #0x14fbb48
    // 0x14fbb38: r3 = LoadInt32Instr(r2)
    //     0x14fbb38: sbfx            x3, x2, #1, #0x1f
    //     0x14fbb3c: tbz             w2, #0, #0x14fbb44
    //     0x14fbb40: ldur            x3, [x2, #7]
    // 0x14fbb44: mov             x2, x3
    // 0x14fbb48: r3 = LoadInt32Instr(r1)
    //     0x14fbb48: sbfx            x3, x1, #1, #0x1f
    //     0x14fbb4c: tbz             w1, #0, #0x14fbb54
    //     0x14fbb50: ldur            x3, [x1, #7]
    // 0x14fbb54: cmp             x3, x2
    // 0x14fbb58: b.ge            #0x14fbd0c
    // 0x14fbb5c: r0 = Radius()
    //     0x14fbb5c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fbb60: d0 = 12.000000
    //     0x14fbb60: fmov            d0, #12.00000000
    // 0x14fbb64: stur            x0, [fp, #-0x18]
    // 0x14fbb68: StoreField: r0->field_7 = d0
    //     0x14fbb68: stur            d0, [x0, #7]
    // 0x14fbb6c: StoreField: r0->field_f = d0
    //     0x14fbb6c: stur            d0, [x0, #0xf]
    // 0x14fbb70: r0 = BorderRadius()
    //     0x14fbb70: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fbb74: mov             x2, x0
    // 0x14fbb78: ldur            x0, [fp, #-0x18]
    // 0x14fbb7c: stur            x2, [fp, #-0x20]
    // 0x14fbb80: StoreField: r2->field_7 = r0
    //     0x14fbb80: stur            w0, [x2, #7]
    // 0x14fbb84: StoreField: r2->field_b = r0
    //     0x14fbb84: stur            w0, [x2, #0xb]
    // 0x14fbb88: StoreField: r2->field_f = r0
    //     0x14fbb88: stur            w0, [x2, #0xf]
    // 0x14fbb8c: StoreField: r2->field_13 = r0
    //     0x14fbb8c: stur            w0, [x2, #0x13]
    // 0x14fbb90: r1 = Instance_Color
    //     0x14fbb90: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fbb94: d0 = 0.100000
    //     0x14fbb94: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14fbb98: r0 = withOpacity()
    //     0x14fbb98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fbb9c: r16 = 1.000000
    //     0x14fbb9c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14fbba0: str             x16, [SP]
    // 0x14fbba4: mov             x2, x0
    // 0x14fbba8: r1 = Null
    //     0x14fbba8: mov             x1, NULL
    // 0x14fbbac: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14fbbac: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14fbbb0: ldr             x4, [x4, #0x108]
    // 0x14fbbb4: r0 = Border.all()
    //     0x14fbbb4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14fbbb8: stur            x0, [fp, #-0x18]
    // 0x14fbbbc: r0 = BoxDecoration()
    //     0x14fbbbc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14fbbc0: mov             x2, x0
    // 0x14fbbc4: ldur            x0, [fp, #-0x18]
    // 0x14fbbc8: stur            x2, [fp, #-0x28]
    // 0x14fbbcc: StoreField: r2->field_f = r0
    //     0x14fbbcc: stur            w0, [x2, #0xf]
    // 0x14fbbd0: ldur            x0, [fp, #-0x20]
    // 0x14fbbd4: StoreField: r2->field_13 = r0
    //     0x14fbbd4: stur            w0, [x2, #0x13]
    // 0x14fbbd8: r0 = Instance_BoxShape
    //     0x14fbbd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fbbdc: ldr             x0, [x0, #0x80]
    // 0x14fbbe0: StoreField: r2->field_23 = r0
    //     0x14fbbe0: stur            w0, [x2, #0x23]
    // 0x14fbbe4: ldr             x1, [fp, #0x18]
    // 0x14fbbe8: r0 = of()
    //     0x14fbbe8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fbbec: LoadField: r1 = r0->field_5b
    //     0x14fbbec: ldur            w1, [x0, #0x5b]
    // 0x14fbbf0: DecompressPointer r1
    //     0x14fbbf0: add             x1, x1, HEAP, lsl #32
    // 0x14fbbf4: stur            x1, [fp, #-0x18]
    // 0x14fbbf8: r0 = ColorFilter()
    //     0x14fbbf8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14fbbfc: mov             x1, x0
    // 0x14fbc00: ldur            x0, [fp, #-0x18]
    // 0x14fbc04: stur            x1, [fp, #-0x20]
    // 0x14fbc08: StoreField: r1->field_7 = r0
    //     0x14fbc08: stur            w0, [x1, #7]
    // 0x14fbc0c: r0 = Instance_BlendMode
    //     0x14fbc0c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14fbc10: ldr             x0, [x0, #0xb30]
    // 0x14fbc14: StoreField: r1->field_b = r0
    //     0x14fbc14: stur            w0, [x1, #0xb]
    // 0x14fbc18: r0 = 1
    //     0x14fbc18: movz            x0, #0x1
    // 0x14fbc1c: StoreField: r1->field_13 = r0
    //     0x14fbc1c: stur            x0, [x1, #0x13]
    // 0x14fbc20: r0 = SvgPicture()
    //     0x14fbc20: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14fbc24: stur            x0, [fp, #-0x18]
    // 0x14fbc28: r16 = Instance_BoxFit
    //     0x14fbc28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14fbc2c: ldr             x16, [x16, #0x118]
    // 0x14fbc30: ldur            lr, [fp, #-0x20]
    // 0x14fbc34: stp             lr, x16, [SP]
    // 0x14fbc38: mov             x1, x0
    // 0x14fbc3c: r2 = "assets/images/image_bg.svg"
    //     0x14fbc3c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14fbc40: ldr             x2, [x2, #0x868]
    // 0x14fbc44: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14fbc44: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14fbc48: ldr             x4, [x4, #0x820]
    // 0x14fbc4c: r0 = SvgPicture.asset()
    //     0x14fbc4c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14fbc50: r0 = Center()
    //     0x14fbc50: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14fbc54: mov             x1, x0
    // 0x14fbc58: r0 = Instance_Alignment
    //     0x14fbc58: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14fbc5c: ldr             x0, [x0, #0xb10]
    // 0x14fbc60: stur            x1, [fp, #-0x20]
    // 0x14fbc64: StoreField: r1->field_f = r0
    //     0x14fbc64: stur            w0, [x1, #0xf]
    // 0x14fbc68: ldur            x0, [fp, #-0x18]
    // 0x14fbc6c: StoreField: r1->field_b = r0
    //     0x14fbc6c: stur            w0, [x1, #0xb]
    // 0x14fbc70: r0 = Container()
    //     0x14fbc70: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fbc74: stur            x0, [fp, #-0x18]
    // 0x14fbc78: r16 = 60.000000
    //     0x14fbc78: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fbc7c: ldr             x16, [x16, #0x110]
    // 0x14fbc80: r30 = 60.000000
    //     0x14fbc80: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fbc84: ldr             lr, [lr, #0x110]
    // 0x14fbc88: stp             lr, x16, [SP, #0x10]
    // 0x14fbc8c: ldur            x16, [fp, #-0x28]
    // 0x14fbc90: ldur            lr, [fp, #-0x20]
    // 0x14fbc94: stp             lr, x16, [SP]
    // 0x14fbc98: mov             x1, x0
    // 0x14fbc9c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x14fbc9c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x14fbca0: ldr             x4, [x4, #0x8c0]
    // 0x14fbca4: r0 = Container()
    //     0x14fbca4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14fbca8: r0 = InkWell()
    //     0x14fbca8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14fbcac: mov             x3, x0
    // 0x14fbcb0: ldur            x0, [fp, #-0x18]
    // 0x14fbcb4: stur            x3, [fp, #-0x20]
    // 0x14fbcb8: StoreField: r3->field_b = r0
    //     0x14fbcb8: stur            w0, [x3, #0xb]
    // 0x14fbcbc: ldur            x2, [fp, #-0x10]
    // 0x14fbcc0: r1 = Function '<anonymous closure>':.
    //     0x14fbcc0: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3a8] AnonymousClosure: (0x9a80e0), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14fbcc4: ldr             x1, [x1, #0x3a8]
    // 0x14fbcc8: r0 = AllocateClosure()
    //     0x14fbcc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fbccc: mov             x1, x0
    // 0x14fbcd0: ldur            x0, [fp, #-0x20]
    // 0x14fbcd4: StoreField: r0->field_f = r1
    //     0x14fbcd4: stur            w1, [x0, #0xf]
    // 0x14fbcd8: r1 = true
    //     0x14fbcd8: add             x1, NULL, #0x20  ; true
    // 0x14fbcdc: StoreField: r0->field_43 = r1
    //     0x14fbcdc: stur            w1, [x0, #0x43]
    // 0x14fbce0: r2 = Instance_BoxShape
    //     0x14fbce0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fbce4: ldr             x2, [x2, #0x80]
    // 0x14fbce8: StoreField: r0->field_47 = r2
    //     0x14fbce8: stur            w2, [x0, #0x47]
    // 0x14fbcec: StoreField: r0->field_6f = r1
    //     0x14fbcec: stur            w1, [x0, #0x6f]
    // 0x14fbcf0: r2 = false
    //     0x14fbcf0: add             x2, NULL, #0x30  ; false
    // 0x14fbcf4: StoreField: r0->field_73 = r2
    //     0x14fbcf4: stur            w2, [x0, #0x73]
    // 0x14fbcf8: StoreField: r0->field_83 = r1
    //     0x14fbcf8: stur            w1, [x0, #0x83]
    // 0x14fbcfc: StoreField: r0->field_7b = r2
    //     0x14fbcfc: stur            w2, [x0, #0x7b]
    // 0x14fbd00: LeaveFrame
    //     0x14fbd00: mov             SP, fp
    //     0x14fbd04: ldp             fp, lr, [SP], #0x10
    // 0x14fbd08: ret
    //     0x14fbd08: ret             
    // 0x14fbd0c: d0 = 12.000000
    //     0x14fbd0c: fmov            d0, #12.00000000
    // 0x14fbd10: b               #0x14fbd1c
    // 0x14fbd14: ldur            x0, [fp, #-8]
    // 0x14fbd18: d0 = 12.000000
    //     0x14fbd18: fmov            d0, #12.00000000
    // 0x14fbd1c: ldur            x2, [fp, #-0x10]
    // 0x14fbd20: r0 = Radius()
    //     0x14fbd20: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fbd24: d0 = 12.000000
    //     0x14fbd24: fmov            d0, #12.00000000
    // 0x14fbd28: stur            x0, [fp, #-0x18]
    // 0x14fbd2c: StoreField: r0->field_7 = d0
    //     0x14fbd2c: stur            d0, [x0, #7]
    // 0x14fbd30: StoreField: r0->field_f = d0
    //     0x14fbd30: stur            d0, [x0, #0xf]
    // 0x14fbd34: r0 = BorderRadius()
    //     0x14fbd34: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fbd38: mov             x2, x0
    // 0x14fbd3c: ldur            x0, [fp, #-0x18]
    // 0x14fbd40: stur            x2, [fp, #-0x20]
    // 0x14fbd44: StoreField: r2->field_7 = r0
    //     0x14fbd44: stur            w0, [x2, #7]
    // 0x14fbd48: StoreField: r2->field_b = r0
    //     0x14fbd48: stur            w0, [x2, #0xb]
    // 0x14fbd4c: StoreField: r2->field_f = r0
    //     0x14fbd4c: stur            w0, [x2, #0xf]
    // 0x14fbd50: StoreField: r2->field_13 = r0
    //     0x14fbd50: stur            w0, [x2, #0x13]
    // 0x14fbd54: ldur            x0, [fp, #-8]
    // 0x14fbd58: LoadField: r1 = r0->field_f
    //     0x14fbd58: ldur            w1, [x0, #0xf]
    // 0x14fbd5c: DecompressPointer r1
    //     0x14fbd5c: add             x1, x1, HEAP, lsl #32
    // 0x14fbd60: r0 = controller()
    //     0x14fbd60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fbd64: LoadField: r1 = r0->field_73
    //     0x14fbd64: ldur            w1, [x0, #0x73]
    // 0x14fbd68: DecompressPointer r1
    //     0x14fbd68: add             x1, x1, HEAP, lsl #32
    // 0x14fbd6c: ldur            x2, [fp, #-0x10]
    // 0x14fbd70: LoadField: r0 = r2->field_f
    //     0x14fbd70: ldur            w0, [x2, #0xf]
    // 0x14fbd74: DecompressPointer r0
    //     0x14fbd74: add             x0, x0, HEAP, lsl #32
    // 0x14fbd78: stur            x0, [fp, #-8]
    // 0x14fbd7c: r0 = value()
    //     0x14fbd7c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fbd80: r1 = LoadClassIdInstr(r0)
    //     0x14fbd80: ldur            x1, [x0, #-1]
    //     0x14fbd84: ubfx            x1, x1, #0xc, #0x14
    // 0x14fbd88: ldur            x16, [fp, #-8]
    // 0x14fbd8c: stp             x16, x0, [SP]
    // 0x14fbd90: mov             x0, x1
    // 0x14fbd94: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14fbd94: sub             lr, x0, #0xb7
    //     0x14fbd98: ldr             lr, [x21, lr, lsl #3]
    //     0x14fbd9c: blr             lr
    // 0x14fbda0: stur            x0, [fp, #-8]
    // 0x14fbda4: r0 = Image()
    //     0x14fbda4: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x14fbda8: mov             x1, x0
    // 0x14fbdac: ldur            x2, [fp, #-8]
    // 0x14fbdb0: d0 = 60.000000
    //     0x14fbdb0: ldr             d0, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14fbdb4: d1 = 60.000000
    //     0x14fbdb4: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14fbdb8: stur            x0, [fp, #-8]
    // 0x14fbdbc: r0 = Image.memory()
    //     0x14fbdbc: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x14fbdc0: r0 = ClipRRect()
    //     0x14fbdc0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14fbdc4: mov             x2, x0
    // 0x14fbdc8: ldur            x0, [fp, #-0x20]
    // 0x14fbdcc: stur            x2, [fp, #-0x18]
    // 0x14fbdd0: StoreField: r2->field_f = r0
    //     0x14fbdd0: stur            w0, [x2, #0xf]
    // 0x14fbdd4: r0 = Instance_Clip
    //     0x14fbdd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14fbdd8: ldr             x0, [x0, #0x138]
    // 0x14fbddc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fbddc: stur            w0, [x2, #0x17]
    // 0x14fbde0: ldur            x0, [fp, #-8]
    // 0x14fbde4: StoreField: r2->field_b = r0
    //     0x14fbde4: stur            w0, [x2, #0xb]
    // 0x14fbde8: ldr             x1, [fp, #0x18]
    // 0x14fbdec: r0 = of()
    //     0x14fbdec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fbdf0: LoadField: r2 = r0->field_5b
    //     0x14fbdf0: ldur            w2, [x0, #0x5b]
    // 0x14fbdf4: DecompressPointer r2
    //     0x14fbdf4: add             x2, x2, HEAP, lsl #32
    // 0x14fbdf8: r16 = 1.000000
    //     0x14fbdf8: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14fbdfc: str             x16, [SP]
    // 0x14fbe00: r1 = Null
    //     0x14fbe00: mov             x1, NULL
    // 0x14fbe04: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14fbe04: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14fbe08: ldr             x4, [x4, #0x108]
    // 0x14fbe0c: r0 = Border.all()
    //     0x14fbe0c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14fbe10: stur            x0, [fp, #-8]
    // 0x14fbe14: r0 = BoxDecoration()
    //     0x14fbe14: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14fbe18: mov             x1, x0
    // 0x14fbe1c: ldur            x0, [fp, #-8]
    // 0x14fbe20: stur            x1, [fp, #-0x20]
    // 0x14fbe24: StoreField: r1->field_f = r0
    //     0x14fbe24: stur            w0, [x1, #0xf]
    // 0x14fbe28: r0 = Instance_BoxShape
    //     0x14fbe28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14fbe2c: ldr             x0, [x0, #0x970]
    // 0x14fbe30: StoreField: r1->field_23 = r0
    //     0x14fbe30: stur            w0, [x1, #0x23]
    // 0x14fbe34: r0 = Container()
    //     0x14fbe34: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fbe38: stur            x0, [fp, #-8]
    // 0x14fbe3c: ldur            x16, [fp, #-0x20]
    // 0x14fbe40: r30 = Instance_Icon
    //     0x14fbe40: add             lr, PP, #0x33, lsl #12  ; [pp+0x338d0] Obj!Icon@d65db1
    //     0x14fbe44: ldr             lr, [lr, #0x8d0]
    // 0x14fbe48: stp             lr, x16, [SP]
    // 0x14fbe4c: mov             x1, x0
    // 0x14fbe50: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14fbe50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14fbe54: ldr             x4, [x4, #0x88]
    // 0x14fbe58: r0 = Container()
    //     0x14fbe58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14fbe5c: r0 = GestureDetector()
    //     0x14fbe5c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14fbe60: ldur            x2, [fp, #-0x10]
    // 0x14fbe64: r1 = Function '<anonymous closure>':.
    //     0x14fbe64: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3b0] AnonymousClosure: (0x14fbf30), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14fa49c)
    //     0x14fbe68: ldr             x1, [x1, #0x3b0]
    // 0x14fbe6c: stur            x0, [fp, #-0x10]
    // 0x14fbe70: r0 = AllocateClosure()
    //     0x14fbe70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fbe74: ldur            x16, [fp, #-8]
    // 0x14fbe78: stp             x16, x0, [SP]
    // 0x14fbe7c: ldur            x1, [fp, #-0x10]
    // 0x14fbe80: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x14fbe80: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x14fbe84: ldr             x4, [x4, #0xaf0]
    // 0x14fbe88: r0 = GestureDetector()
    //     0x14fbe88: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14fbe8c: r1 = <StackParentData>
    //     0x14fbe8c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14fbe90: ldr             x1, [x1, #0x8e0]
    // 0x14fbe94: r0 = Positioned()
    //     0x14fbe94: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14fbe98: mov             x3, x0
    // 0x14fbe9c: ldur            x0, [fp, #-0x10]
    // 0x14fbea0: stur            x3, [fp, #-8]
    // 0x14fbea4: StoreField: r3->field_b = r0
    //     0x14fbea4: stur            w0, [x3, #0xb]
    // 0x14fbea8: r1 = Null
    //     0x14fbea8: mov             x1, NULL
    // 0x14fbeac: r2 = 4
    //     0x14fbeac: movz            x2, #0x4
    // 0x14fbeb0: r0 = AllocateArray()
    //     0x14fbeb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fbeb4: mov             x2, x0
    // 0x14fbeb8: ldur            x0, [fp, #-0x18]
    // 0x14fbebc: stur            x2, [fp, #-0x10]
    // 0x14fbec0: StoreField: r2->field_f = r0
    //     0x14fbec0: stur            w0, [x2, #0xf]
    // 0x14fbec4: ldur            x0, [fp, #-8]
    // 0x14fbec8: StoreField: r2->field_13 = r0
    //     0x14fbec8: stur            w0, [x2, #0x13]
    // 0x14fbecc: r1 = <Widget>
    //     0x14fbecc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fbed0: r0 = AllocateGrowableArray()
    //     0x14fbed0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fbed4: mov             x1, x0
    // 0x14fbed8: ldur            x0, [fp, #-0x10]
    // 0x14fbedc: stur            x1, [fp, #-8]
    // 0x14fbee0: StoreField: r1->field_f = r0
    //     0x14fbee0: stur            w0, [x1, #0xf]
    // 0x14fbee4: r0 = 4
    //     0x14fbee4: movz            x0, #0x4
    // 0x14fbee8: StoreField: r1->field_b = r0
    //     0x14fbee8: stur            w0, [x1, #0xb]
    // 0x14fbeec: r0 = Stack()
    //     0x14fbeec: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14fbef0: r1 = Instance_Alignment
    //     0x14fbef0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14fbef4: ldr             x1, [x1, #0x950]
    // 0x14fbef8: StoreField: r0->field_f = r1
    //     0x14fbef8: stur            w1, [x0, #0xf]
    // 0x14fbefc: r1 = Instance_StackFit
    //     0x14fbefc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14fbf00: ldr             x1, [x1, #0xfa8]
    // 0x14fbf04: ArrayStore: r0[0] = r1  ; List_4
    //     0x14fbf04: stur            w1, [x0, #0x17]
    // 0x14fbf08: r1 = Instance_Clip
    //     0x14fbf08: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14fbf0c: ldr             x1, [x1, #0x7e0]
    // 0x14fbf10: StoreField: r0->field_1b = r1
    //     0x14fbf10: stur            w1, [x0, #0x1b]
    // 0x14fbf14: ldur            x1, [fp, #-8]
    // 0x14fbf18: StoreField: r0->field_b = r1
    //     0x14fbf18: stur            w1, [x0, #0xb]
    // 0x14fbf1c: LeaveFrame
    //     0x14fbf1c: mov             SP, fp
    //     0x14fbf20: ldp             fp, lr, [SP], #0x10
    // 0x14fbf24: ret
    //     0x14fbf24: ret             
    // 0x14fbf28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fbf28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fbf2c: b               #0x14fba40
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14fbf30, size: 0x228
    // 0x14fbf30: EnterFrame
    //     0x14fbf30: stp             fp, lr, [SP, #-0x10]!
    //     0x14fbf34: mov             fp, SP
    // 0x14fbf38: AllocStack(0x38)
    //     0x14fbf38: sub             SP, SP, #0x38
    // 0x14fbf3c: SetupParameters(ReturnOrderWithProofView this /* r1 */)
    //     0x14fbf3c: stur            NULL, [fp, #-8]
    //     0x14fbf40: movz            x0, #0
    //     0x14fbf44: add             x1, fp, w0, sxtw #2
    //     0x14fbf48: ldr             x1, [x1, #0x10]
    //     0x14fbf4c: ldur            w2, [x1, #0x17]
    //     0x14fbf50: add             x2, x2, HEAP, lsl #32
    //     0x14fbf54: stur            x2, [fp, #-0x10]
    // 0x14fbf58: CheckStackOverflow
    //     0x14fbf58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fbf5c: cmp             SP, x16
    //     0x14fbf60: b.ls            #0x14fc150
    // 0x14fbf64: InitAsync() -> Future<void?>
    //     0x14fbf64: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14fbf68: bl              #0x6326e0  ; InitAsyncStub
    // 0x14fbf6c: ldur            x0, [fp, #-0x10]
    // 0x14fbf70: LoadField: r2 = r0->field_b
    //     0x14fbf70: ldur            w2, [x0, #0xb]
    // 0x14fbf74: DecompressPointer r2
    //     0x14fbf74: add             x2, x2, HEAP, lsl #32
    // 0x14fbf78: stur            x2, [fp, #-0x18]
    // 0x14fbf7c: LoadField: r1 = r2->field_f
    //     0x14fbf7c: ldur            w1, [x2, #0xf]
    // 0x14fbf80: DecompressPointer r1
    //     0x14fbf80: add             x1, x1, HEAP, lsl #32
    // 0x14fbf84: r0 = controller()
    //     0x14fbf84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fbf88: mov             x2, x0
    // 0x14fbf8c: ldur            x0, [fp, #-0x18]
    // 0x14fbf90: stur            x2, [fp, #-0x20]
    // 0x14fbf94: LoadField: r1 = r0->field_f
    //     0x14fbf94: ldur            w1, [x0, #0xf]
    // 0x14fbf98: DecompressPointer r1
    //     0x14fbf98: add             x1, x1, HEAP, lsl #32
    // 0x14fbf9c: r0 = controller()
    //     0x14fbf9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fbfa0: LoadField: r1 = r0->field_6f
    //     0x14fbfa0: ldur            w1, [x0, #0x6f]
    // 0x14fbfa4: DecompressPointer r1
    //     0x14fbfa4: add             x1, x1, HEAP, lsl #32
    // 0x14fbfa8: ldur            x0, [fp, #-0x10]
    // 0x14fbfac: LoadField: r2 = r0->field_f
    //     0x14fbfac: ldur            w2, [x0, #0xf]
    // 0x14fbfb0: DecompressPointer r2
    //     0x14fbfb0: add             x2, x2, HEAP, lsl #32
    // 0x14fbfb4: stur            x2, [fp, #-0x28]
    // 0x14fbfb8: r0 = value()
    //     0x14fbfb8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fbfbc: r1 = LoadClassIdInstr(r0)
    //     0x14fbfbc: ldur            x1, [x0, #-1]
    //     0x14fbfc0: ubfx            x1, x1, #0xc, #0x14
    // 0x14fbfc4: ldur            x16, [fp, #-0x28]
    // 0x14fbfc8: stp             x16, x0, [SP]
    // 0x14fbfcc: mov             x0, x1
    // 0x14fbfd0: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14fbfd0: sub             lr, x0, #0xb7
    //     0x14fbfd4: ldr             lr, [x21, lr, lsl #3]
    //     0x14fbfd8: blr             lr
    // 0x14fbfdc: r1 = LoadClassIdInstr(r0)
    //     0x14fbfdc: ldur            x1, [x0, #-1]
    //     0x14fbfe0: ubfx            x1, x1, #0xc, #0x14
    // 0x14fbfe4: mov             x16, x0
    // 0x14fbfe8: mov             x0, x1
    // 0x14fbfec: mov             x1, x16
    // 0x14fbff0: r0 = GDT[cid_x0 + -0xe96]()
    //     0x14fbff0: sub             lr, x0, #0xe96
    //     0x14fbff4: ldr             lr, [x21, lr, lsl #3]
    //     0x14fbff8: blr             lr
    // 0x14fbffc: ldur            x1, [fp, #-0x20]
    // 0x14fc000: mov             x2, x0
    // 0x14fc004: r0 = getFileSize()
    //     0x14fc004: bl              #0x9a577c  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::getFileSize
    // 0x14fc008: mov             x1, x0
    // 0x14fc00c: stur            x1, [fp, #-0x20]
    // 0x14fc010: r0 = Await()
    //     0x14fc010: bl              #0x63248c  ; AwaitStub
    // 0x14fc014: mov             x3, x0
    // 0x14fc018: r2 = Null
    //     0x14fc018: mov             x2, NULL
    // 0x14fc01c: r1 = Null
    //     0x14fc01c: mov             x1, NULL
    // 0x14fc020: stur            x3, [fp, #-0x20]
    // 0x14fc024: branchIfSmi(r0, 0x14fc04c)
    //     0x14fc024: tbz             w0, #0, #0x14fc04c
    // 0x14fc028: r4 = LoadClassIdInstr(r0)
    //     0x14fc028: ldur            x4, [x0, #-1]
    //     0x14fc02c: ubfx            x4, x4, #0xc, #0x14
    // 0x14fc030: sub             x4, x4, #0x3c
    // 0x14fc034: cmp             x4, #1
    // 0x14fc038: b.ls            #0x14fc04c
    // 0x14fc03c: r8 = int
    //     0x14fc03c: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14fc040: r3 = Null
    //     0x14fc040: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f3b8] Null
    //     0x14fc044: ldr             x3, [x3, #0x3b8]
    // 0x14fc048: r0 = int()
    //     0x14fc048: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14fc04c: ldur            x0, [fp, #-0x18]
    // 0x14fc050: LoadField: r1 = r0->field_f
    //     0x14fc050: ldur            w1, [x0, #0xf]
    // 0x14fc054: DecompressPointer r1
    //     0x14fc054: add             x1, x1, HEAP, lsl #32
    // 0x14fc058: r0 = controller()
    //     0x14fc058: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc05c: LoadField: r1 = r0->field_c7
    //     0x14fc05c: ldur            x1, [x0, #0xc7]
    // 0x14fc060: ldur            x2, [fp, #-0x20]
    // 0x14fc064: r3 = LoadInt32Instr(r2)
    //     0x14fc064: sbfx            x3, x2, #1, #0x1f
    //     0x14fc068: tbz             w2, #0, #0x14fc070
    //     0x14fc06c: ldur            x3, [x2, #7]
    // 0x14fc070: sub             x2, x1, x3
    // 0x14fc074: StoreField: r0->field_c7 = r2
    //     0x14fc074: stur            x2, [x0, #0xc7]
    // 0x14fc078: ldur            x0, [fp, #-0x18]
    // 0x14fc07c: LoadField: r1 = r0->field_f
    //     0x14fc07c: ldur            w1, [x0, #0xf]
    // 0x14fc080: DecompressPointer r1
    //     0x14fc080: add             x1, x1, HEAP, lsl #32
    // 0x14fc084: r0 = controller()
    //     0x14fc084: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc088: LoadField: r1 = r0->field_6f
    //     0x14fc088: ldur            w1, [x0, #0x6f]
    // 0x14fc08c: DecompressPointer r1
    //     0x14fc08c: add             x1, x1, HEAP, lsl #32
    // 0x14fc090: ldur            x0, [fp, #-0x10]
    // 0x14fc094: LoadField: r2 = r0->field_f
    //     0x14fc094: ldur            w2, [x0, #0xf]
    // 0x14fc098: DecompressPointer r2
    //     0x14fc098: add             x2, x2, HEAP, lsl #32
    // 0x14fc09c: r3 = LoadInt32Instr(r2)
    //     0x14fc09c: sbfx            x3, x2, #1, #0x1f
    //     0x14fc0a0: tbz             w2, #0, #0x14fc0a8
    //     0x14fc0a4: ldur            x3, [x2, #7]
    // 0x14fc0a8: mov             x2, x3
    // 0x14fc0ac: r0 = removeAt()
    //     0x14fc0ac: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14fc0b0: ldur            x0, [fp, #-0x18]
    // 0x14fc0b4: LoadField: r1 = r0->field_f
    //     0x14fc0b4: ldur            w1, [x0, #0xf]
    // 0x14fc0b8: DecompressPointer r1
    //     0x14fc0b8: add             x1, x1, HEAP, lsl #32
    // 0x14fc0bc: r0 = controller()
    //     0x14fc0bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc0c0: LoadField: r1 = r0->field_73
    //     0x14fc0c0: ldur            w1, [x0, #0x73]
    // 0x14fc0c4: DecompressPointer r1
    //     0x14fc0c4: add             x1, x1, HEAP, lsl #32
    // 0x14fc0c8: ldur            x0, [fp, #-0x10]
    // 0x14fc0cc: LoadField: r2 = r0->field_f
    //     0x14fc0cc: ldur            w2, [x0, #0xf]
    // 0x14fc0d0: DecompressPointer r2
    //     0x14fc0d0: add             x2, x2, HEAP, lsl #32
    // 0x14fc0d4: r0 = LoadInt32Instr(r2)
    //     0x14fc0d4: sbfx            x0, x2, #1, #0x1f
    //     0x14fc0d8: tbz             w2, #0, #0x14fc0e0
    //     0x14fc0dc: ldur            x0, [x2, #7]
    // 0x14fc0e0: mov             x2, x0
    // 0x14fc0e4: r0 = removeAt()
    //     0x14fc0e4: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14fc0e8: ldur            x0, [fp, #-0x18]
    // 0x14fc0ec: LoadField: r1 = r0->field_f
    //     0x14fc0ec: ldur            w1, [x0, #0xf]
    // 0x14fc0f0: DecompressPointer r1
    //     0x14fc0f0: add             x1, x1, HEAP, lsl #32
    // 0x14fc0f4: r0 = controller()
    //     0x14fc0f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc0f8: LoadField: r1 = r0->field_73
    //     0x14fc0f8: ldur            w1, [x0, #0x73]
    // 0x14fc0fc: DecompressPointer r1
    //     0x14fc0fc: add             x1, x1, HEAP, lsl #32
    // 0x14fc100: r0 = value()
    //     0x14fc100: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14fc104: r1 = LoadClassIdInstr(r0)
    //     0x14fc104: ldur            x1, [x0, #-1]
    //     0x14fc108: ubfx            x1, x1, #0xc, #0x14
    // 0x14fc10c: str             x0, [SP]
    // 0x14fc110: mov             x0, x1
    // 0x14fc114: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14fc114: movz            x17, #0xc898
    //     0x14fc118: add             lr, x0, x17
    //     0x14fc11c: ldr             lr, [x21, lr, lsl #3]
    //     0x14fc120: blr             lr
    // 0x14fc124: cbnz            w0, #0x14fc148
    // 0x14fc128: ldur            x0, [fp, #-0x18]
    // 0x14fc12c: LoadField: r1 = r0->field_f
    //     0x14fc12c: ldur            w1, [x0, #0xf]
    // 0x14fc130: DecompressPointer r1
    //     0x14fc130: add             x1, x1, HEAP, lsl #32
    // 0x14fc134: r0 = controller()
    //     0x14fc134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc138: LoadField: r1 = r0->field_9b
    //     0x14fc138: ldur            w1, [x0, #0x9b]
    // 0x14fc13c: DecompressPointer r1
    //     0x14fc13c: add             x1, x1, HEAP, lsl #32
    // 0x14fc140: r2 = false
    //     0x14fc140: add             x2, NULL, #0x30  ; false
    // 0x14fc144: r0 = value=()
    //     0x14fc144: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14fc148: r0 = Null
    //     0x14fc148: mov             x0, NULL
    // 0x14fc14c: r0 = ReturnAsyncNotFuture()
    //     0x14fc14c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14fc150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fc150: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fc154: b               #0x14fbf64
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e4844, size: 0x18c
    // 0x15e4844: EnterFrame
    //     0x15e4844: stp             fp, lr, [SP, #-0x10]!
    //     0x15e4848: mov             fp, SP
    // 0x15e484c: AllocStack(0x28)
    //     0x15e484c: sub             SP, SP, #0x28
    // 0x15e4850: SetupParameters(ReturnOrderWithProofView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e4850: mov             x0, x1
    //     0x15e4854: stur            x1, [fp, #-8]
    //     0x15e4858: mov             x1, x2
    //     0x15e485c: stur            x2, [fp, #-0x10]
    // 0x15e4860: CheckStackOverflow
    //     0x15e4860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e4864: cmp             SP, x16
    //     0x15e4868: b.ls            #0x15e49c8
    // 0x15e486c: r1 = 2
    //     0x15e486c: movz            x1, #0x2
    // 0x15e4870: r0 = AllocateContext()
    //     0x15e4870: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e4874: mov             x1, x0
    // 0x15e4878: ldur            x0, [fp, #-8]
    // 0x15e487c: stur            x1, [fp, #-0x18]
    // 0x15e4880: StoreField: r1->field_f = r0
    //     0x15e4880: stur            w0, [x1, #0xf]
    // 0x15e4884: ldur            x0, [fp, #-0x10]
    // 0x15e4888: StoreField: r1->field_13 = r0
    //     0x15e4888: stur            w0, [x1, #0x13]
    // 0x15e488c: r0 = Obx()
    //     0x15e488c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e4890: ldur            x2, [fp, #-0x18]
    // 0x15e4894: r1 = Function '<anonymous closure>':.
    //     0x15e4894: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3c8] AnonymousClosure: (0x15d5454), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e4898: ldr             x1, [x1, #0x3c8]
    // 0x15e489c: stur            x0, [fp, #-8]
    // 0x15e48a0: r0 = AllocateClosure()
    //     0x15e48a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e48a4: mov             x1, x0
    // 0x15e48a8: ldur            x0, [fp, #-8]
    // 0x15e48ac: StoreField: r0->field_b = r1
    //     0x15e48ac: stur            w1, [x0, #0xb]
    // 0x15e48b0: ldur            x1, [fp, #-0x10]
    // 0x15e48b4: r0 = of()
    //     0x15e48b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e48b8: LoadField: r1 = r0->field_5b
    //     0x15e48b8: ldur            w1, [x0, #0x5b]
    // 0x15e48bc: DecompressPointer r1
    //     0x15e48bc: add             x1, x1, HEAP, lsl #32
    // 0x15e48c0: stur            x1, [fp, #-0x10]
    // 0x15e48c4: r0 = ColorFilter()
    //     0x15e48c4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e48c8: mov             x1, x0
    // 0x15e48cc: ldur            x0, [fp, #-0x10]
    // 0x15e48d0: stur            x1, [fp, #-0x20]
    // 0x15e48d4: StoreField: r1->field_7 = r0
    //     0x15e48d4: stur            w0, [x1, #7]
    // 0x15e48d8: r0 = Instance_BlendMode
    //     0x15e48d8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e48dc: ldr             x0, [x0, #0xb30]
    // 0x15e48e0: StoreField: r1->field_b = r0
    //     0x15e48e0: stur            w0, [x1, #0xb]
    // 0x15e48e4: r0 = 1
    //     0x15e48e4: movz            x0, #0x1
    // 0x15e48e8: StoreField: r1->field_13 = r0
    //     0x15e48e8: stur            x0, [x1, #0x13]
    // 0x15e48ec: r0 = SvgPicture()
    //     0x15e48ec: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e48f0: stur            x0, [fp, #-0x10]
    // 0x15e48f4: ldur            x16, [fp, #-0x20]
    // 0x15e48f8: str             x16, [SP]
    // 0x15e48fc: mov             x1, x0
    // 0x15e4900: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e4900: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e4904: ldr             x2, [x2, #0xa40]
    // 0x15e4908: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e4908: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e490c: ldr             x4, [x4, #0xa38]
    // 0x15e4910: r0 = SvgPicture.asset()
    //     0x15e4910: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e4914: r0 = Align()
    //     0x15e4914: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e4918: mov             x1, x0
    // 0x15e491c: r0 = Instance_Alignment
    //     0x15e491c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e4920: ldr             x0, [x0, #0xb10]
    // 0x15e4924: stur            x1, [fp, #-0x20]
    // 0x15e4928: StoreField: r1->field_f = r0
    //     0x15e4928: stur            w0, [x1, #0xf]
    // 0x15e492c: r0 = 1.000000
    //     0x15e492c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e4930: StoreField: r1->field_13 = r0
    //     0x15e4930: stur            w0, [x1, #0x13]
    // 0x15e4934: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e4934: stur            w0, [x1, #0x17]
    // 0x15e4938: ldur            x0, [fp, #-0x10]
    // 0x15e493c: StoreField: r1->field_b = r0
    //     0x15e493c: stur            w0, [x1, #0xb]
    // 0x15e4940: r0 = InkWell()
    //     0x15e4940: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e4944: mov             x3, x0
    // 0x15e4948: ldur            x0, [fp, #-0x20]
    // 0x15e494c: stur            x3, [fp, #-0x10]
    // 0x15e4950: StoreField: r3->field_b = r0
    //     0x15e4950: stur            w0, [x3, #0xb]
    // 0x15e4954: ldur            x2, [fp, #-0x18]
    // 0x15e4958: r1 = Function '<anonymous closure>':.
    //     0x15e4958: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3d0] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e495c: ldr             x1, [x1, #0x3d0]
    // 0x15e4960: r0 = AllocateClosure()
    //     0x15e4960: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e4964: ldur            x2, [fp, #-0x10]
    // 0x15e4968: StoreField: r2->field_f = r0
    //     0x15e4968: stur            w0, [x2, #0xf]
    // 0x15e496c: r0 = true
    //     0x15e496c: add             x0, NULL, #0x20  ; true
    // 0x15e4970: StoreField: r2->field_43 = r0
    //     0x15e4970: stur            w0, [x2, #0x43]
    // 0x15e4974: r1 = Instance_BoxShape
    //     0x15e4974: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e4978: ldr             x1, [x1, #0x80]
    // 0x15e497c: StoreField: r2->field_47 = r1
    //     0x15e497c: stur            w1, [x2, #0x47]
    // 0x15e4980: StoreField: r2->field_6f = r0
    //     0x15e4980: stur            w0, [x2, #0x6f]
    // 0x15e4984: r1 = false
    //     0x15e4984: add             x1, NULL, #0x30  ; false
    // 0x15e4988: StoreField: r2->field_73 = r1
    //     0x15e4988: stur            w1, [x2, #0x73]
    // 0x15e498c: StoreField: r2->field_83 = r0
    //     0x15e498c: stur            w0, [x2, #0x83]
    // 0x15e4990: StoreField: r2->field_7b = r1
    //     0x15e4990: stur            w1, [x2, #0x7b]
    // 0x15e4994: r0 = AppBar()
    //     0x15e4994: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e4998: stur            x0, [fp, #-0x18]
    // 0x15e499c: ldur            x16, [fp, #-8]
    // 0x15e49a0: str             x16, [SP]
    // 0x15e49a4: mov             x1, x0
    // 0x15e49a8: ldur            x2, [fp, #-0x10]
    // 0x15e49ac: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e49ac: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e49b0: ldr             x4, [x4, #0xf00]
    // 0x15e49b4: r0 = AppBar()
    //     0x15e49b4: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e49b8: ldur            x0, [fp, #-0x18]
    // 0x15e49bc: LeaveFrame
    //     0x15e49bc: mov             SP, fp
    //     0x15e49c0: ldp             fp, lr, [SP], #0x10
    // 0x15e49c4: ret
    //     0x15e49c4: ret             
    // 0x15e49c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e49c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e49cc: b               #0x15e486c
  }
}
