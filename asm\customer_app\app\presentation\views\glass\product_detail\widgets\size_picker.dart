// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/size_picker.dart

// class id: 1049449, size: 0x8
class :: {
}

// class id: 3303, size: 0x1c, field offset: 0x14
class _SizePickerState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb9387c, size: 0x7dc
    // 0xb9387c: EnterFrame
    //     0xb9387c: stp             fp, lr, [SP, #-0x10]!
    //     0xb93880: mov             fp, SP
    // 0xb93884: AllocStack(0x58)
    //     0xb93884: sub             SP, SP, #0x58
    // 0xb93888: SetupParameters(_SizePickerState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb93888: stur            x1, [fp, #-8]
    //     0xb9388c: stur            x2, [fp, #-0x10]
    // 0xb93890: CheckStackOverflow
    //     0xb93890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb93894: cmp             SP, x16
    //     0xb93898: b.ls            #0xb94034
    // 0xb9389c: r1 = 2
    //     0xb9389c: movz            x1, #0x2
    // 0xb938a0: r0 = AllocateContext()
    //     0xb938a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb938a4: mov             x1, x0
    // 0xb938a8: ldur            x0, [fp, #-8]
    // 0xb938ac: stur            x1, [fp, #-0x18]
    // 0xb938b0: StoreField: r1->field_f = r0
    //     0xb938b0: stur            w0, [x1, #0xf]
    // 0xb938b4: ldur            x2, [fp, #-0x10]
    // 0xb938b8: StoreField: r1->field_13 = r2
    //     0xb938b8: stur            w2, [x1, #0x13]
    // 0xb938bc: LoadField: r2 = r0->field_b
    //     0xb938bc: ldur            w2, [x0, #0xb]
    // 0xb938c0: DecompressPointer r2
    //     0xb938c0: add             x2, x2, HEAP, lsl #32
    // 0xb938c4: cmp             w2, NULL
    // 0xb938c8: b.eq            #0xb9403c
    // 0xb938cc: LoadField: r3 = r2->field_b
    //     0xb938cc: ldur            w3, [x2, #0xb]
    // 0xb938d0: DecompressPointer r3
    //     0xb938d0: add             x3, x3, HEAP, lsl #32
    // 0xb938d4: LoadField: r2 = r3->field_1b
    //     0xb938d4: ldur            w2, [x3, #0x1b]
    // 0xb938d8: DecompressPointer r2
    //     0xb938d8: add             x2, x2, HEAP, lsl #32
    // 0xb938dc: cmp             w2, NULL
    // 0xb938e0: b.ne            #0xb938ec
    // 0xb938e4: r2 = Null
    //     0xb938e4: mov             x2, NULL
    // 0xb938e8: b               #0xb93944
    // 0xb938ec: r16 = <WidgetEntity?>
    //     0xb938ec: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e28] TypeArguments: <WidgetEntity?>
    //     0xb938f0: ldr             x16, [x16, #0xe28]
    // 0xb938f4: stp             x2, x16, [SP]
    // 0xb938f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb938f8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb938fc: r0 = cast()
    //     0xb938fc: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0xb93900: ldur            x2, [fp, #-0x18]
    // 0xb93904: r1 = Function '<anonymous closure>':.
    //     0xb93904: add             x1, PP, #0x55, lsl #12  ; [pp+0x55298] AnonymousClosure: (0xa9d774), in [package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xc0ec90)
    //     0xb93908: ldr             x1, [x1, #0x298]
    // 0xb9390c: stur            x0, [fp, #-0x10]
    // 0xb93910: r0 = AllocateClosure()
    //     0xb93910: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb93914: r1 = Function '<anonymous closure>':.
    //     0xb93914: add             x1, PP, #0x55, lsl #12  ; [pp+0x552a0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb93918: ldr             x1, [x1, #0x2a0]
    // 0xb9391c: r2 = Null
    //     0xb9391c: mov             x2, NULL
    // 0xb93920: stur            x0, [fp, #-0x20]
    // 0xb93924: r0 = AllocateClosure()
    //     0xb93924: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb93928: str             x0, [SP]
    // 0xb9392c: ldur            x1, [fp, #-0x10]
    // 0xb93930: ldur            x2, [fp, #-0x20]
    // 0xb93934: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb93934: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb93938: ldr             x4, [x4, #0xb48]
    // 0xb9393c: r0 = firstWhere()
    //     0xb9393c: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0xb93940: mov             x2, x0
    // 0xb93944: cmp             w2, NULL
    // 0xb93948: b.eq            #0xb939d8
    // 0xb9394c: ldur            x0, [fp, #-8]
    // 0xb93950: LoadField: r1 = r0->field_b
    //     0xb93950: ldur            w1, [x0, #0xb]
    // 0xb93954: DecompressPointer r1
    //     0xb93954: add             x1, x1, HEAP, lsl #32
    // 0xb93958: cmp             w1, NULL
    // 0xb9395c: b.eq            #0xb94040
    // 0xb93960: LoadField: r3 = r1->field_b
    //     0xb93960: ldur            w3, [x1, #0xb]
    // 0xb93964: DecompressPointer r3
    //     0xb93964: add             x3, x3, HEAP, lsl #32
    // 0xb93968: LoadField: r1 = r3->field_1b
    //     0xb93968: ldur            w1, [x3, #0x1b]
    // 0xb9396c: DecompressPointer r1
    //     0xb9396c: add             x1, x1, HEAP, lsl #32
    // 0xb93970: cmp             w1, NULL
    // 0xb93974: b.ne            #0xb93984
    // 0xb93978: mov             x2, x0
    // 0xb9397c: r1 = Null
    //     0xb9397c: mov             x1, NULL
    // 0xb93980: b               #0xb939ac
    // 0xb93984: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb93984: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb93988: r0 = indexOf()
    //     0xb93988: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xb9398c: mov             x2, x0
    // 0xb93990: r0 = BoxInt64Instr(r2)
    //     0xb93990: sbfiz           x0, x2, #1, #0x1f
    //     0xb93994: cmp             x2, x0, asr #1
    //     0xb93998: b.eq            #0xb939a4
    //     0xb9399c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb939a0: stur            x2, [x0, #7]
    // 0xb939a4: mov             x1, x0
    // 0xb939a8: ldur            x2, [fp, #-8]
    // 0xb939ac: mov             x0, x1
    // 0xb939b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb939b0: stur            w0, [x2, #0x17]
    //     0xb939b4: tbz             w0, #0, #0xb939d0
    //     0xb939b8: ldurb           w16, [x2, #-1]
    //     0xb939bc: ldurb           w17, [x0, #-1]
    //     0xb939c0: and             x16, x17, x16, lsr #2
    //     0xb939c4: tst             x16, HEAP, lsr #32
    //     0xb939c8: b.eq            #0xb939d0
    //     0xb939cc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb939d0: mov             x0, x1
    // 0xb939d4: b               #0xb939e4
    // 0xb939d8: ldur            x2, [fp, #-8]
    // 0xb939dc: ArrayStore: r2[0] = rZR  ; List_4
    //     0xb939dc: stur            wzr, [x2, #0x17]
    // 0xb939e0: r0 = 0
    //     0xb939e0: movz            x0, #0
    // 0xb939e4: LoadField: r1 = r2->field_b
    //     0xb939e4: ldur            w1, [x2, #0xb]
    // 0xb939e8: DecompressPointer r1
    //     0xb939e8: add             x1, x1, HEAP, lsl #32
    // 0xb939ec: cmp             w1, NULL
    // 0xb939f0: b.eq            #0xb94044
    // 0xb939f4: LoadField: r3 = r1->field_b
    //     0xb939f4: ldur            w3, [x1, #0xb]
    // 0xb939f8: DecompressPointer r3
    //     0xb939f8: add             x3, x3, HEAP, lsl #32
    // 0xb939fc: LoadField: r4 = r3->field_1b
    //     0xb939fc: ldur            w4, [x3, #0x1b]
    // 0xb93a00: DecompressPointer r4
    //     0xb93a00: add             x4, x4, HEAP, lsl #32
    // 0xb93a04: cmp             w4, NULL
    // 0xb93a08: b.ne            #0xb93a14
    // 0xb93a0c: r0 = Null
    //     0xb93a0c: mov             x0, NULL
    // 0xb93a10: b               #0xb93a64
    // 0xb93a14: cmp             w0, NULL
    // 0xb93a18: b.ne            #0xb93a24
    // 0xb93a1c: r3 = 0
    //     0xb93a1c: movz            x3, #0
    // 0xb93a20: b               #0xb93a34
    // 0xb93a24: r1 = LoadInt32Instr(r0)
    //     0xb93a24: sbfx            x1, x0, #1, #0x1f
    //     0xb93a28: tbz             w0, #0, #0xb93a30
    //     0xb93a2c: ldur            x1, [x0, #7]
    // 0xb93a30: mov             x3, x1
    // 0xb93a34: LoadField: r0 = r4->field_b
    //     0xb93a34: ldur            w0, [x4, #0xb]
    // 0xb93a38: r1 = LoadInt32Instr(r0)
    //     0xb93a38: sbfx            x1, x0, #1, #0x1f
    // 0xb93a3c: mov             x0, x1
    // 0xb93a40: mov             x1, x3
    // 0xb93a44: cmp             x1, x0
    // 0xb93a48: b.hs            #0xb94048
    // 0xb93a4c: LoadField: r0 = r4->field_f
    //     0xb93a4c: ldur            w0, [x4, #0xf]
    // 0xb93a50: DecompressPointer r0
    //     0xb93a50: add             x0, x0, HEAP, lsl #32
    // 0xb93a54: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb93a54: add             x16, x0, x3, lsl #2
    //     0xb93a58: ldur            w1, [x16, #0xf]
    // 0xb93a5c: DecompressPointer r1
    //     0xb93a5c: add             x1, x1, HEAP, lsl #32
    // 0xb93a60: mov             x0, x1
    // 0xb93a64: cmp             w0, NULL
    // 0xb93a68: b.ne            #0xb93a70
    // 0xb93a6c: r0 = WidgetEntity()
    //     0xb93a6c: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xb93a70: ldur            x3, [fp, #-8]
    // 0xb93a74: StoreField: r3->field_13 = r0
    //     0xb93a74: stur            w0, [x3, #0x13]
    //     0xb93a78: ldurb           w16, [x3, #-1]
    //     0xb93a7c: ldurb           w17, [x0, #-1]
    //     0xb93a80: and             x16, x17, x16, lsr #2
    //     0xb93a84: tst             x16, HEAP, lsr #32
    //     0xb93a88: b.eq            #0xb93a90
    //     0xb93a8c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb93a90: r1 = <Widget>
    //     0xb93a90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb93a94: r2 = 0
    //     0xb93a94: movz            x2, #0
    // 0xb93a98: r0 = _GrowableList()
    //     0xb93a98: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb93a9c: mov             x2, x0
    // 0xb93aa0: ldur            x1, [fp, #-8]
    // 0xb93aa4: stur            x2, [fp, #-0x10]
    // 0xb93aa8: LoadField: r0 = r1->field_b
    //     0xb93aa8: ldur            w0, [x1, #0xb]
    // 0xb93aac: DecompressPointer r0
    //     0xb93aac: add             x0, x0, HEAP, lsl #32
    // 0xb93ab0: cmp             w0, NULL
    // 0xb93ab4: b.eq            #0xb9404c
    // 0xb93ab8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb93ab8: ldur            w3, [x0, #0x17]
    // 0xb93abc: DecompressPointer r3
    //     0xb93abc: add             x3, x3, HEAP, lsl #32
    // 0xb93ac0: r0 = LoadClassIdInstr(r3)
    //     0xb93ac0: ldur            x0, [x3, #-1]
    //     0xb93ac4: ubfx            x0, x0, #0xc, #0x14
    // 0xb93ac8: r16 = "size"
    //     0xb93ac8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb93acc: ldr             x16, [x16, #0x9c0]
    // 0xb93ad0: stp             x16, x3, [SP]
    // 0xb93ad4: mov             lr, x0
    // 0xb93ad8: ldr             lr, [x21, lr, lsl #3]
    // 0xb93adc: blr             lr
    // 0xb93ae0: tbnz            w0, #4, #0xb93bd4
    // 0xb93ae4: ldur            x2, [fp, #-0x18]
    // 0xb93ae8: ldur            x0, [fp, #-0x10]
    // 0xb93aec: LoadField: r1 = r2->field_13
    //     0xb93aec: ldur            w1, [x2, #0x13]
    // 0xb93af0: DecompressPointer r1
    //     0xb93af0: add             x1, x1, HEAP, lsl #32
    // 0xb93af4: r0 = of()
    //     0xb93af4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb93af8: LoadField: r1 = r0->field_87
    //     0xb93af8: ldur            w1, [x0, #0x87]
    // 0xb93afc: DecompressPointer r1
    //     0xb93afc: add             x1, x1, HEAP, lsl #32
    // 0xb93b00: LoadField: r0 = r1->field_7
    //     0xb93b00: ldur            w0, [x1, #7]
    // 0xb93b04: DecompressPointer r0
    //     0xb93b04: add             x0, x0, HEAP, lsl #32
    // 0xb93b08: r16 = 12.000000
    //     0xb93b08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb93b0c: ldr             x16, [x16, #0x9e8]
    // 0xb93b10: r30 = Instance_Color
    //     0xb93b10: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb93b14: stp             lr, x16, [SP]
    // 0xb93b18: mov             x1, x0
    // 0xb93b1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb93b1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb93b20: ldr             x4, [x4, #0xaa0]
    // 0xb93b24: r0 = copyWith()
    //     0xb93b24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb93b28: stur            x0, [fp, #-0x20]
    // 0xb93b2c: r0 = Text()
    //     0xb93b2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb93b30: mov             x2, x0
    // 0xb93b34: r0 = "Select Size"
    //     0xb93b34: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xb93b38: ldr             x0, [x0, #0x370]
    // 0xb93b3c: stur            x2, [fp, #-0x30]
    // 0xb93b40: StoreField: r2->field_b = r0
    //     0xb93b40: stur            w0, [x2, #0xb]
    // 0xb93b44: ldur            x0, [fp, #-0x20]
    // 0xb93b48: StoreField: r2->field_13 = r0
    //     0xb93b48: stur            w0, [x2, #0x13]
    // 0xb93b4c: ldur            x0, [fp, #-0x10]
    // 0xb93b50: LoadField: r1 = r0->field_b
    //     0xb93b50: ldur            w1, [x0, #0xb]
    // 0xb93b54: LoadField: r3 = r0->field_f
    //     0xb93b54: ldur            w3, [x0, #0xf]
    // 0xb93b58: DecompressPointer r3
    //     0xb93b58: add             x3, x3, HEAP, lsl #32
    // 0xb93b5c: LoadField: r4 = r3->field_b
    //     0xb93b5c: ldur            w4, [x3, #0xb]
    // 0xb93b60: r3 = LoadInt32Instr(r1)
    //     0xb93b60: sbfx            x3, x1, #1, #0x1f
    // 0xb93b64: stur            x3, [fp, #-0x28]
    // 0xb93b68: r1 = LoadInt32Instr(r4)
    //     0xb93b68: sbfx            x1, x4, #1, #0x1f
    // 0xb93b6c: cmp             x3, x1
    // 0xb93b70: b.ne            #0xb93b7c
    // 0xb93b74: mov             x1, x0
    // 0xb93b78: r0 = _growToNextCapacity()
    //     0xb93b78: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93b7c: ldur            x2, [fp, #-0x10]
    // 0xb93b80: ldur            x3, [fp, #-0x28]
    // 0xb93b84: add             x4, x3, #1
    // 0xb93b88: lsl             x0, x4, #1
    // 0xb93b8c: StoreField: r2->field_b = r0
    //     0xb93b8c: stur            w0, [x2, #0xb]
    // 0xb93b90: LoadField: r5 = r2->field_f
    //     0xb93b90: ldur            w5, [x2, #0xf]
    // 0xb93b94: DecompressPointer r5
    //     0xb93b94: add             x5, x5, HEAP, lsl #32
    // 0xb93b98: mov             x1, x5
    // 0xb93b9c: ldur            x0, [fp, #-0x30]
    // 0xb93ba0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb93ba0: add             x25, x1, x3, lsl #2
    //     0xb93ba4: add             x25, x25, #0xf
    //     0xb93ba8: str             w0, [x25]
    //     0xb93bac: tbz             w0, #0, #0xb93bc8
    //     0xb93bb0: ldurb           w16, [x1, #-1]
    //     0xb93bb4: ldurb           w17, [x0, #-1]
    //     0xb93bb8: and             x16, x17, x16, lsr #2
    //     0xb93bbc: tst             x16, HEAP, lsr #32
    //     0xb93bc0: b.eq            #0xb93bc8
    //     0xb93bc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb93bc8: mov             x3, x4
    // 0xb93bcc: mov             x0, x5
    // 0xb93bd0: b               #0xb93cc0
    // 0xb93bd4: ldur            x0, [fp, #-0x18]
    // 0xb93bd8: ldur            x2, [fp, #-0x10]
    // 0xb93bdc: LoadField: r1 = r0->field_13
    //     0xb93bdc: ldur            w1, [x0, #0x13]
    // 0xb93be0: DecompressPointer r1
    //     0xb93be0: add             x1, x1, HEAP, lsl #32
    // 0xb93be4: r0 = of()
    //     0xb93be4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb93be8: LoadField: r1 = r0->field_87
    //     0xb93be8: ldur            w1, [x0, #0x87]
    // 0xb93bec: DecompressPointer r1
    //     0xb93bec: add             x1, x1, HEAP, lsl #32
    // 0xb93bf0: LoadField: r0 = r1->field_7
    //     0xb93bf0: ldur            w0, [x1, #7]
    // 0xb93bf4: DecompressPointer r0
    //     0xb93bf4: add             x0, x0, HEAP, lsl #32
    // 0xb93bf8: r16 = 12.000000
    //     0xb93bf8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb93bfc: ldr             x16, [x16, #0x9e8]
    // 0xb93c00: r30 = Instance_Color
    //     0xb93c00: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb93c04: stp             lr, x16, [SP]
    // 0xb93c08: mov             x1, x0
    // 0xb93c0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb93c0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb93c10: ldr             x4, [x4, #0xaa0]
    // 0xb93c14: r0 = copyWith()
    //     0xb93c14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb93c18: stur            x0, [fp, #-0x20]
    // 0xb93c1c: r0 = Text()
    //     0xb93c1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb93c20: mov             x2, x0
    // 0xb93c24: r0 = "Select Variant"
    //     0xb93c24: add             x0, PP, #0x52, lsl #12  ; [pp+0x52378] "Select Variant"
    //     0xb93c28: ldr             x0, [x0, #0x378]
    // 0xb93c2c: stur            x2, [fp, #-0x30]
    // 0xb93c30: StoreField: r2->field_b = r0
    //     0xb93c30: stur            w0, [x2, #0xb]
    // 0xb93c34: ldur            x0, [fp, #-0x20]
    // 0xb93c38: StoreField: r2->field_13 = r0
    //     0xb93c38: stur            w0, [x2, #0x13]
    // 0xb93c3c: ldur            x0, [fp, #-0x10]
    // 0xb93c40: LoadField: r1 = r0->field_b
    //     0xb93c40: ldur            w1, [x0, #0xb]
    // 0xb93c44: LoadField: r3 = r0->field_f
    //     0xb93c44: ldur            w3, [x0, #0xf]
    // 0xb93c48: DecompressPointer r3
    //     0xb93c48: add             x3, x3, HEAP, lsl #32
    // 0xb93c4c: LoadField: r4 = r3->field_b
    //     0xb93c4c: ldur            w4, [x3, #0xb]
    // 0xb93c50: r3 = LoadInt32Instr(r1)
    //     0xb93c50: sbfx            x3, x1, #1, #0x1f
    // 0xb93c54: stur            x3, [fp, #-0x28]
    // 0xb93c58: r1 = LoadInt32Instr(r4)
    //     0xb93c58: sbfx            x1, x4, #1, #0x1f
    // 0xb93c5c: cmp             x3, x1
    // 0xb93c60: b.ne            #0xb93c6c
    // 0xb93c64: mov             x1, x0
    // 0xb93c68: r0 = _growToNextCapacity()
    //     0xb93c68: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93c6c: ldur            x2, [fp, #-0x10]
    // 0xb93c70: ldur            x3, [fp, #-0x28]
    // 0xb93c74: add             x4, x3, #1
    // 0xb93c78: lsl             x0, x4, #1
    // 0xb93c7c: StoreField: r2->field_b = r0
    //     0xb93c7c: stur            w0, [x2, #0xb]
    // 0xb93c80: LoadField: r5 = r2->field_f
    //     0xb93c80: ldur            w5, [x2, #0xf]
    // 0xb93c84: DecompressPointer r5
    //     0xb93c84: add             x5, x5, HEAP, lsl #32
    // 0xb93c88: mov             x1, x5
    // 0xb93c8c: ldur            x0, [fp, #-0x30]
    // 0xb93c90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb93c90: add             x25, x1, x3, lsl #2
    //     0xb93c94: add             x25, x25, #0xf
    //     0xb93c98: str             w0, [x25]
    //     0xb93c9c: tbz             w0, #0, #0xb93cb8
    //     0xb93ca0: ldurb           w16, [x1, #-1]
    //     0xb93ca4: ldurb           w17, [x0, #-1]
    //     0xb93ca8: and             x16, x17, x16, lsr #2
    //     0xb93cac: tst             x16, HEAP, lsr #32
    //     0xb93cb0: b.eq            #0xb93cb8
    //     0xb93cb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb93cb8: mov             x3, x4
    // 0xb93cbc: mov             x0, x5
    // 0xb93cc0: stur            x3, [fp, #-0x28]
    // 0xb93cc4: LoadField: r1 = r0->field_b
    //     0xb93cc4: ldur            w1, [x0, #0xb]
    // 0xb93cc8: r0 = LoadInt32Instr(r1)
    //     0xb93cc8: sbfx            x0, x1, #1, #0x1f
    // 0xb93ccc: cmp             x3, x0
    // 0xb93cd0: b.ne            #0xb93cdc
    // 0xb93cd4: mov             x1, x2
    // 0xb93cd8: r0 = _growToNextCapacity()
    //     0xb93cd8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93cdc: ldur            x5, [fp, #-8]
    // 0xb93ce0: ldur            x4, [fp, #-0x18]
    // 0xb93ce4: ldur            x2, [fp, #-0x10]
    // 0xb93ce8: ldur            x3, [fp, #-0x28]
    // 0xb93cec: add             x0, x3, #1
    // 0xb93cf0: lsl             x1, x0, #1
    // 0xb93cf4: StoreField: r2->field_b = r1
    //     0xb93cf4: stur            w1, [x2, #0xb]
    // 0xb93cf8: mov             x1, x3
    // 0xb93cfc: cmp             x1, x0
    // 0xb93d00: b.hs            #0xb94050
    // 0xb93d04: LoadField: r0 = r2->field_f
    //     0xb93d04: ldur            w0, [x2, #0xf]
    // 0xb93d08: DecompressPointer r0
    //     0xb93d08: add             x0, x0, HEAP, lsl #32
    // 0xb93d0c: add             x1, x0, x3, lsl #2
    // 0xb93d10: r16 = Instance_SizedBox
    //     0xb93d10: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb93d14: ldr             x16, [x16, #0x328]
    // 0xb93d18: StoreField: r1->field_f = r16
    //     0xb93d18: stur            w16, [x1, #0xf]
    // 0xb93d1c: LoadField: r1 = r4->field_13
    //     0xb93d1c: ldur            w1, [x4, #0x13]
    // 0xb93d20: DecompressPointer r1
    //     0xb93d20: add             x1, x1, HEAP, lsl #32
    // 0xb93d24: r0 = of()
    //     0xb93d24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb93d28: LoadField: r1 = r0->field_5b
    //     0xb93d28: ldur            w1, [x0, #0x5b]
    // 0xb93d2c: DecompressPointer r1
    //     0xb93d2c: add             x1, x1, HEAP, lsl #32
    // 0xb93d30: r0 = LoadClassIdInstr(r1)
    //     0xb93d30: ldur            x0, [x1, #-1]
    //     0xb93d34: ubfx            x0, x0, #0xc, #0x14
    // 0xb93d38: d0 = 0.030000
    //     0xb93d38: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb93d3c: ldr             d0, [x17, #0x238]
    // 0xb93d40: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb93d40: sub             lr, x0, #0xffa
    //     0xb93d44: ldr             lr, [x21, lr, lsl #3]
    //     0xb93d48: blr             lr
    // 0xb93d4c: stur            x0, [fp, #-0x20]
    // 0xb93d50: r0 = Radius()
    //     0xb93d50: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb93d54: d0 = 30.000000
    //     0xb93d54: fmov            d0, #30.00000000
    // 0xb93d58: stur            x0, [fp, #-0x30]
    // 0xb93d5c: StoreField: r0->field_7 = d0
    //     0xb93d5c: stur            d0, [x0, #7]
    // 0xb93d60: StoreField: r0->field_f = d0
    //     0xb93d60: stur            d0, [x0, #0xf]
    // 0xb93d64: r0 = BorderRadius()
    //     0xb93d64: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb93d68: mov             x1, x0
    // 0xb93d6c: ldur            x0, [fp, #-0x30]
    // 0xb93d70: stur            x1, [fp, #-0x38]
    // 0xb93d74: StoreField: r1->field_7 = r0
    //     0xb93d74: stur            w0, [x1, #7]
    // 0xb93d78: StoreField: r1->field_b = r0
    //     0xb93d78: stur            w0, [x1, #0xb]
    // 0xb93d7c: StoreField: r1->field_f = r0
    //     0xb93d7c: stur            w0, [x1, #0xf]
    // 0xb93d80: StoreField: r1->field_13 = r0
    //     0xb93d80: stur            w0, [x1, #0x13]
    // 0xb93d84: r0 = BoxDecoration()
    //     0xb93d84: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb93d88: mov             x2, x0
    // 0xb93d8c: ldur            x0, [fp, #-0x20]
    // 0xb93d90: stur            x2, [fp, #-0x30]
    // 0xb93d94: StoreField: r2->field_7 = r0
    //     0xb93d94: stur            w0, [x2, #7]
    // 0xb93d98: ldur            x0, [fp, #-0x38]
    // 0xb93d9c: StoreField: r2->field_13 = r0
    //     0xb93d9c: stur            w0, [x2, #0x13]
    // 0xb93da0: r0 = Instance_BoxShape
    //     0xb93da0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb93da4: ldr             x0, [x0, #0x80]
    // 0xb93da8: StoreField: r2->field_23 = r0
    //     0xb93da8: stur            w0, [x2, #0x23]
    // 0xb93dac: ldur            x0, [fp, #-8]
    // 0xb93db0: LoadField: r5 = r0->field_13
    //     0xb93db0: ldur            w5, [x0, #0x13]
    // 0xb93db4: DecompressPointer r5
    //     0xb93db4: add             x5, x5, HEAP, lsl #32
    // 0xb93db8: ldur            x3, [fp, #-0x18]
    // 0xb93dbc: stur            x5, [fp, #-0x20]
    // 0xb93dc0: LoadField: r1 = r3->field_13
    //     0xb93dc0: ldur            w1, [x3, #0x13]
    // 0xb93dc4: DecompressPointer r1
    //     0xb93dc4: add             x1, x1, HEAP, lsl #32
    // 0xb93dc8: r0 = of()
    //     0xb93dc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb93dcc: LoadField: r3 = r0->field_5b
    //     0xb93dcc: ldur            w3, [x0, #0x5b]
    // 0xb93dd0: DecompressPointer r3
    //     0xb93dd0: add             x3, x3, HEAP, lsl #32
    // 0xb93dd4: ldur            x0, [fp, #-8]
    // 0xb93dd8: stur            x3, [fp, #-0x38]
    // 0xb93ddc: LoadField: r1 = r0->field_b
    //     0xb93ddc: ldur            w1, [x0, #0xb]
    // 0xb93de0: DecompressPointer r1
    //     0xb93de0: add             x1, x1, HEAP, lsl #32
    // 0xb93de4: cmp             w1, NULL
    // 0xb93de8: b.eq            #0xb94054
    // 0xb93dec: LoadField: r0 = r1->field_b
    //     0xb93dec: ldur            w0, [x1, #0xb]
    // 0xb93df0: DecompressPointer r0
    //     0xb93df0: add             x0, x0, HEAP, lsl #32
    // 0xb93df4: LoadField: r4 = r0->field_1b
    //     0xb93df4: ldur            w4, [x0, #0x1b]
    // 0xb93df8: DecompressPointer r4
    //     0xb93df8: add             x4, x4, HEAP, lsl #32
    // 0xb93dfc: stur            x4, [fp, #-8]
    // 0xb93e00: cmp             w4, NULL
    // 0xb93e04: b.ne            #0xb93e10
    // 0xb93e08: r3 = Null
    //     0xb93e08: mov             x3, NULL
    // 0xb93e0c: b               #0xb93e4c
    // 0xb93e10: ldur            x2, [fp, #-0x18]
    // 0xb93e14: r1 = Function '<anonymous closure>':.
    //     0xb93e14: add             x1, PP, #0x55, lsl #12  ; [pp+0x552a8] AnonymousClosure: (0xb9428c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xc0ec90)
    //     0xb93e18: ldr             x1, [x1, #0x2a8]
    // 0xb93e1c: r0 = AllocateClosure()
    //     0xb93e1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb93e20: r16 = <DropdownMenuItem<WidgetEntity>>
    //     0xb93e20: add             x16, PP, #0x52, lsl #12  ; [pp+0x52388] TypeArguments: <DropdownMenuItem<WidgetEntity>>
    //     0xb93e24: ldr             x16, [x16, #0x388]
    // 0xb93e28: ldur            lr, [fp, #-8]
    // 0xb93e2c: stp             lr, x16, [SP, #8]
    // 0xb93e30: str             x0, [SP]
    // 0xb93e34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb93e34: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb93e38: r0 = map()
    //     0xb93e38: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xb93e3c: mov             x1, x0
    // 0xb93e40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb93e40: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb93e44: r0 = toList()
    //     0xb93e44: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xb93e48: mov             x3, x0
    // 0xb93e4c: ldur            x0, [fp, #-0x10]
    // 0xb93e50: ldur            x2, [fp, #-0x18]
    // 0xb93e54: stur            x3, [fp, #-8]
    // 0xb93e58: r1 = Function '<anonymous closure>':.
    //     0xb93e58: add             x1, PP, #0x55, lsl #12  ; [pp+0x552b0] AnonymousClosure: (0xb94078), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xb9387c)
    //     0xb93e5c: ldr             x1, [x1, #0x2b0]
    // 0xb93e60: r0 = AllocateClosure()
    //     0xb93e60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb93e64: r1 = <WidgetEntity>
    //     0xb93e64: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0xb93e68: ldr             x1, [x1, #0x878]
    // 0xb93e6c: stur            x0, [fp, #-0x18]
    // 0xb93e70: r0 = DropdownButton()
    //     0xb93e70: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xb93e74: stur            x0, [fp, #-0x40]
    // 0xb93e78: r16 = Instance_Icon
    //     0xb93e78: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e530] Obj!Icon@d66771
    //     0xb93e7c: ldr             x16, [x16, #0x530]
    // 0xb93e80: ldur            lr, [fp, #-0x38]
    // 0xb93e84: stp             lr, x16, [SP]
    // 0xb93e88: mov             x1, x0
    // 0xb93e8c: ldur            x2, [fp, #-8]
    // 0xb93e90: ldur            x3, [fp, #-0x18]
    // 0xb93e94: ldur            x5, [fp, #-0x20]
    // 0xb93e98: r4 = const [0, 0x6, 0x2, 0x4, icon, 0x4, iconEnabledColor, 0x5, null]
    //     0xb93e98: add             x4, PP, #0x55, lsl #12  ; [pp+0x552b8] List(9) [0, 0x6, 0x2, 0x4, "icon", 0x4, "iconEnabledColor", 0x5, Null]
    //     0xb93e9c: ldr             x4, [x4, #0x2b8]
    // 0xb93ea0: r0 = DropdownButton()
    //     0xb93ea0: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xb93ea4: r0 = DropdownButtonHideUnderline()
    //     0xb93ea4: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xb93ea8: mov             x1, x0
    // 0xb93eac: ldur            x0, [fp, #-0x40]
    // 0xb93eb0: stur            x1, [fp, #-8]
    // 0xb93eb4: StoreField: r1->field_b = r0
    //     0xb93eb4: stur            w0, [x1, #0xb]
    // 0xb93eb8: r0 = Container()
    //     0xb93eb8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb93ebc: stur            x0, [fp, #-0x18]
    // 0xb93ec0: r16 = Instance_Alignment
    //     0xb93ec0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb93ec4: ldr             x16, [x16, #0xb10]
    // 0xb93ec8: r30 = 44.000000
    //     0xb93ec8: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb93ecc: ldr             lr, [lr, #0xad8]
    // 0xb93ed0: stp             lr, x16, [SP, #8]
    // 0xb93ed4: ldur            x16, [fp, #-8]
    // 0xb93ed8: str             x16, [SP]
    // 0xb93edc: mov             x1, x0
    // 0xb93ee0: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, height, 0x2, null]
    //     0xb93ee0: add             x4, PP, #0x52, lsl #12  ; [pp+0x523a0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "height", 0x2, Null]
    //     0xb93ee4: ldr             x4, [x4, #0x3a0]
    // 0xb93ee8: r0 = Container()
    //     0xb93ee8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb93eec: r0 = Padding()
    //     0xb93eec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb93ef0: mov             x1, x0
    // 0xb93ef4: r0 = Instance_EdgeInsets
    //     0xb93ef4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xb93ef8: ldr             x0, [x0, #0xc10]
    // 0xb93efc: stur            x1, [fp, #-8]
    // 0xb93f00: StoreField: r1->field_f = r0
    //     0xb93f00: stur            w0, [x1, #0xf]
    // 0xb93f04: ldur            x0, [fp, #-0x18]
    // 0xb93f08: StoreField: r1->field_b = r0
    //     0xb93f08: stur            w0, [x1, #0xb]
    // 0xb93f0c: r0 = Container()
    //     0xb93f0c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb93f10: stur            x0, [fp, #-0x18]
    // 0xb93f14: r16 = Instance_AlignmentDirectional
    //     0xb93f14: add             x16, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xb93f18: ldr             x16, [x16, #0x3a8]
    // 0xb93f1c: ldur            lr, [fp, #-0x30]
    // 0xb93f20: stp             lr, x16, [SP, #8]
    // 0xb93f24: ldur            x16, [fp, #-8]
    // 0xb93f28: str             x16, [SP]
    // 0xb93f2c: mov             x1, x0
    // 0xb93f30: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, decoration, 0x2, null]
    //     0xb93f30: add             x4, PP, #0x52, lsl #12  ; [pp+0x523b0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "decoration", 0x2, Null]
    //     0xb93f34: ldr             x4, [x4, #0x3b0]
    // 0xb93f38: r0 = Container()
    //     0xb93f38: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb93f3c: ldur            x0, [fp, #-0x10]
    // 0xb93f40: LoadField: r1 = r0->field_b
    //     0xb93f40: ldur            w1, [x0, #0xb]
    // 0xb93f44: LoadField: r2 = r0->field_f
    //     0xb93f44: ldur            w2, [x0, #0xf]
    // 0xb93f48: DecompressPointer r2
    //     0xb93f48: add             x2, x2, HEAP, lsl #32
    // 0xb93f4c: LoadField: r3 = r2->field_b
    //     0xb93f4c: ldur            w3, [x2, #0xb]
    // 0xb93f50: r2 = LoadInt32Instr(r1)
    //     0xb93f50: sbfx            x2, x1, #1, #0x1f
    // 0xb93f54: stur            x2, [fp, #-0x28]
    // 0xb93f58: r1 = LoadInt32Instr(r3)
    //     0xb93f58: sbfx            x1, x3, #1, #0x1f
    // 0xb93f5c: cmp             x2, x1
    // 0xb93f60: b.ne            #0xb93f6c
    // 0xb93f64: mov             x1, x0
    // 0xb93f68: r0 = _growToNextCapacity()
    //     0xb93f68: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93f6c: ldur            x2, [fp, #-0x10]
    // 0xb93f70: ldur            x3, [fp, #-0x28]
    // 0xb93f74: add             x0, x3, #1
    // 0xb93f78: lsl             x1, x0, #1
    // 0xb93f7c: StoreField: r2->field_b = r1
    //     0xb93f7c: stur            w1, [x2, #0xb]
    // 0xb93f80: LoadField: r1 = r2->field_f
    //     0xb93f80: ldur            w1, [x2, #0xf]
    // 0xb93f84: DecompressPointer r1
    //     0xb93f84: add             x1, x1, HEAP, lsl #32
    // 0xb93f88: ldur            x0, [fp, #-0x18]
    // 0xb93f8c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb93f8c: add             x25, x1, x3, lsl #2
    //     0xb93f90: add             x25, x25, #0xf
    //     0xb93f94: str             w0, [x25]
    //     0xb93f98: tbz             w0, #0, #0xb93fb4
    //     0xb93f9c: ldurb           w16, [x1, #-1]
    //     0xb93fa0: ldurb           w17, [x0, #-1]
    //     0xb93fa4: and             x16, x17, x16, lsr #2
    //     0xb93fa8: tst             x16, HEAP, lsr #32
    //     0xb93fac: b.eq            #0xb93fb4
    //     0xb93fb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb93fb4: r0 = Column()
    //     0xb93fb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb93fb8: mov             x1, x0
    // 0xb93fbc: r0 = Instance_Axis
    //     0xb93fbc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb93fc0: stur            x1, [fp, #-8]
    // 0xb93fc4: StoreField: r1->field_f = r0
    //     0xb93fc4: stur            w0, [x1, #0xf]
    // 0xb93fc8: r0 = Instance_MainAxisAlignment
    //     0xb93fc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb93fcc: ldr             x0, [x0, #0xa08]
    // 0xb93fd0: StoreField: r1->field_13 = r0
    //     0xb93fd0: stur            w0, [x1, #0x13]
    // 0xb93fd4: r0 = Instance_MainAxisSize
    //     0xb93fd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb93fd8: ldr             x0, [x0, #0xa10]
    // 0xb93fdc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb93fdc: stur            w0, [x1, #0x17]
    // 0xb93fe0: r0 = Instance_CrossAxisAlignment
    //     0xb93fe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb93fe4: ldr             x0, [x0, #0x890]
    // 0xb93fe8: StoreField: r1->field_1b = r0
    //     0xb93fe8: stur            w0, [x1, #0x1b]
    // 0xb93fec: r0 = Instance_VerticalDirection
    //     0xb93fec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb93ff0: ldr             x0, [x0, #0xa20]
    // 0xb93ff4: StoreField: r1->field_23 = r0
    //     0xb93ff4: stur            w0, [x1, #0x23]
    // 0xb93ff8: r0 = Instance_Clip
    //     0xb93ff8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb93ffc: ldr             x0, [x0, #0x38]
    // 0xb94000: StoreField: r1->field_2b = r0
    //     0xb94000: stur            w0, [x1, #0x2b]
    // 0xb94004: StoreField: r1->field_2f = rZR
    //     0xb94004: stur            xzr, [x1, #0x2f]
    // 0xb94008: ldur            x0, [fp, #-0x10]
    // 0xb9400c: StoreField: r1->field_b = r0
    //     0xb9400c: stur            w0, [x1, #0xb]
    // 0xb94010: r0 = Padding()
    //     0xb94010: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb94014: r1 = Instance_EdgeInsets
    //     0xb94014: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb94018: ldr             x1, [x1, #0x240]
    // 0xb9401c: StoreField: r0->field_f = r1
    //     0xb9401c: stur            w1, [x0, #0xf]
    // 0xb94020: ldur            x1, [fp, #-8]
    // 0xb94024: StoreField: r0->field_b = r1
    //     0xb94024: stur            w1, [x0, #0xb]
    // 0xb94028: LeaveFrame
    //     0xb94028: mov             SP, fp
    //     0xb9402c: ldp             fp, lr, [SP], #0x10
    // 0xb94030: ret
    //     0xb94030: ret             
    // 0xb94034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94038: b               #0xb9389c
    // 0xb9403c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9403c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb94040: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94040: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb94044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94044: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb94048: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb94048: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb9404c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9404c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb94050: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb94050: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb94054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94054: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, WidgetEntity?) {
    // ** addr: 0xb94078, size: 0x84
    // 0xb94078: EnterFrame
    //     0xb94078: stp             fp, lr, [SP, #-0x10]!
    //     0xb9407c: mov             fp, SP
    // 0xb94080: AllocStack(0x10)
    //     0xb94080: sub             SP, SP, #0x10
    // 0xb94084: SetupParameters()
    //     0xb94084: ldr             x0, [fp, #0x18]
    //     0xb94088: ldur            w1, [x0, #0x17]
    //     0xb9408c: add             x1, x1, HEAP, lsl #32
    //     0xb94090: stur            x1, [fp, #-8]
    // 0xb94094: CheckStackOverflow
    //     0xb94094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94098: cmp             SP, x16
    //     0xb9409c: b.ls            #0xb940f4
    // 0xb940a0: r1 = 1
    //     0xb940a0: movz            x1, #0x1
    // 0xb940a4: r0 = AllocateContext()
    //     0xb940a4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb940a8: mov             x1, x0
    // 0xb940ac: ldur            x0, [fp, #-8]
    // 0xb940b0: StoreField: r1->field_b = r0
    //     0xb940b0: stur            w0, [x1, #0xb]
    // 0xb940b4: ldr             x2, [fp, #0x10]
    // 0xb940b8: StoreField: r1->field_f = r2
    //     0xb940b8: stur            w2, [x1, #0xf]
    // 0xb940bc: LoadField: r3 = r0->field_f
    //     0xb940bc: ldur            w3, [x0, #0xf]
    // 0xb940c0: DecompressPointer r3
    //     0xb940c0: add             x3, x3, HEAP, lsl #32
    // 0xb940c4: mov             x2, x1
    // 0xb940c8: stur            x3, [fp, #-0x10]
    // 0xb940cc: r1 = Function '<anonymous closure>':.
    //     0xb940cc: add             x1, PP, #0x55, lsl #12  ; [pp+0x552c0] AnonymousClosure: (0xb940fc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xb9387c)
    //     0xb940d0: ldr             x1, [x1, #0x2c0]
    // 0xb940d4: r0 = AllocateClosure()
    //     0xb940d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb940d8: ldur            x1, [fp, #-0x10]
    // 0xb940dc: mov             x2, x0
    // 0xb940e0: r0 = setState()
    //     0xb940e0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb940e4: r0 = Null
    //     0xb940e4: mov             x0, NULL
    // 0xb940e8: LeaveFrame
    //     0xb940e8: mov             SP, fp
    //     0xb940ec: ldp             fp, lr, [SP], #0x10
    // 0xb940f0: ret
    //     0xb940f0: ret             
    // 0xb940f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb940f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb940f8: b               #0xb940a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb940fc, size: 0x190
    // 0xb940fc: EnterFrame
    //     0xb940fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb94100: mov             fp, SP
    // 0xb94104: AllocStack(0x30)
    //     0xb94104: sub             SP, SP, #0x30
    // 0xb94108: SetupParameters()
    //     0xb94108: ldr             x0, [fp, #0x10]
    //     0xb9410c: ldur            w1, [x0, #0x17]
    //     0xb94110: add             x1, x1, HEAP, lsl #32
    //     0xb94114: stur            x1, [fp, #-0x20]
    // 0xb94118: CheckStackOverflow
    //     0xb94118: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9411c: cmp             SP, x16
    //     0xb94120: b.ls            #0xb9427c
    // 0xb94124: LoadField: r2 = r1->field_b
    //     0xb94124: ldur            w2, [x1, #0xb]
    // 0xb94128: DecompressPointer r2
    //     0xb94128: add             x2, x2, HEAP, lsl #32
    // 0xb9412c: stur            x2, [fp, #-0x18]
    // 0xb94130: LoadField: r3 = r2->field_f
    //     0xb94130: ldur            w3, [x2, #0xf]
    // 0xb94134: DecompressPointer r3
    //     0xb94134: add             x3, x3, HEAP, lsl #32
    // 0xb94138: stur            x3, [fp, #-0x10]
    // 0xb9413c: LoadField: r4 = r1->field_f
    //     0xb9413c: ldur            w4, [x1, #0xf]
    // 0xb94140: DecompressPointer r4
    //     0xb94140: add             x4, x4, HEAP, lsl #32
    // 0xb94144: mov             x0, x4
    // 0xb94148: StoreField: r3->field_13 = r0
    //     0xb94148: stur            w0, [x3, #0x13]
    //     0xb9414c: ldurb           w16, [x3, #-1]
    //     0xb94150: ldurb           w17, [x0, #-1]
    //     0xb94154: and             x16, x17, x16, lsr #2
    //     0xb94158: tst             x16, HEAP, lsr #32
    //     0xb9415c: b.eq            #0xb94164
    //     0xb94160: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb94164: LoadField: r0 = r3->field_b
    //     0xb94164: ldur            w0, [x3, #0xb]
    // 0xb94168: DecompressPointer r0
    //     0xb94168: add             x0, x0, HEAP, lsl #32
    // 0xb9416c: cmp             w0, NULL
    // 0xb94170: b.eq            #0xb94284
    // 0xb94174: LoadField: r5 = r0->field_b
    //     0xb94174: ldur            w5, [x0, #0xb]
    // 0xb94178: DecompressPointer r5
    //     0xb94178: add             x5, x5, HEAP, lsl #32
    // 0xb9417c: LoadField: r0 = r5->field_1b
    //     0xb9417c: ldur            w0, [x5, #0x1b]
    // 0xb94180: DecompressPointer r0
    //     0xb94180: add             x0, x0, HEAP, lsl #32
    // 0xb94184: stur            x0, [fp, #-8]
    // 0xb94188: cmp             w0, NULL
    // 0xb9418c: b.ne            #0xb94198
    // 0xb94190: r0 = Null
    //     0xb94190: mov             x0, NULL
    // 0xb94194: b               #0xb941e0
    // 0xb94198: cmp             w4, NULL
    // 0xb9419c: b.ne            #0xb941ac
    // 0xb941a0: r0 = WidgetEntity()
    //     0xb941a0: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xb941a4: mov             x2, x0
    // 0xb941a8: b               #0xb941b0
    // 0xb941ac: mov             x2, x4
    // 0xb941b0: ldur            x1, [fp, #-8]
    // 0xb941b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb941b4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb941b8: r0 = indexOf()
    //     0xb941b8: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xb941bc: mov             x2, x0
    // 0xb941c0: r0 = BoxInt64Instr(r2)
    //     0xb941c0: sbfiz           x0, x2, #1, #0x1f
    //     0xb941c4: cmp             x2, x0, asr #1
    //     0xb941c8: b.eq            #0xb941d4
    //     0xb941cc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb941d0: stur            x2, [x0, #7]
    // 0xb941d4: ldur            x1, [fp, #-0x20]
    // 0xb941d8: ldur            x2, [fp, #-0x18]
    // 0xb941dc: ldur            x3, [fp, #-0x10]
    // 0xb941e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb941e0: stur            w0, [x3, #0x17]
    //     0xb941e4: tbz             w0, #0, #0xb94200
    //     0xb941e8: ldurb           w16, [x3, #-1]
    //     0xb941ec: ldurb           w17, [x0, #-1]
    //     0xb941f0: and             x16, x17, x16, lsr #2
    //     0xb941f4: tst             x16, HEAP, lsr #32
    //     0xb941f8: b.eq            #0xb94200
    //     0xb941fc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb94200: LoadField: r0 = r2->field_f
    //     0xb94200: ldur            w0, [x2, #0xf]
    // 0xb94204: DecompressPointer r0
    //     0xb94204: add             x0, x0, HEAP, lsl #32
    // 0xb94208: LoadField: r2 = r0->field_b
    //     0xb94208: ldur            w2, [x0, #0xb]
    // 0xb9420c: DecompressPointer r2
    //     0xb9420c: add             x2, x2, HEAP, lsl #32
    // 0xb94210: cmp             w2, NULL
    // 0xb94214: b.eq            #0xb94288
    // 0xb94218: LoadField: r0 = r1->field_f
    //     0xb94218: ldur            w0, [x1, #0xf]
    // 0xb9421c: DecompressPointer r0
    //     0xb9421c: add             x0, x0, HEAP, lsl #32
    // 0xb94220: cmp             w0, NULL
    // 0xb94224: b.ne            #0xb94230
    // 0xb94228: r0 = Null
    //     0xb94228: mov             x0, NULL
    // 0xb9422c: b               #0xb9423c
    // 0xb94230: LoadField: r1 = r0->field_37
    //     0xb94230: ldur            w1, [x0, #0x37]
    // 0xb94234: DecompressPointer r1
    //     0xb94234: add             x1, x1, HEAP, lsl #32
    // 0xb94238: mov             x0, x1
    // 0xb9423c: cmp             w0, NULL
    // 0xb94240: b.ne            #0xb94248
    // 0xb94244: r0 = ""
    //     0xb94244: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb94248: LoadField: r1 = r2->field_f
    //     0xb94248: ldur            w1, [x2, #0xf]
    // 0xb9424c: DecompressPointer r1
    //     0xb9424c: add             x1, x1, HEAP, lsl #32
    // 0xb94250: stp             x0, x1, [SP]
    // 0xb94254: r4 = 0
    //     0xb94254: movz            x4, #0
    // 0xb94258: ldr             x0, [SP, #8]
    // 0xb9425c: r16 = UnlinkedCall_0x613b5c
    //     0xb9425c: add             x16, PP, #0x55, lsl #12  ; [pp+0x552c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb94260: add             x16, x16, #0x2c8
    // 0xb94264: ldp             x5, lr, [x16]
    // 0xb94268: blr             lr
    // 0xb9426c: r0 = Null
    //     0xb9426c: mov             x0, NULL
    // 0xb94270: LeaveFrame
    //     0xb94270: mov             SP, fp
    //     0xb94274: ldp             fp, lr, [SP], #0x10
    // 0xb94278: ret
    //     0xb94278: ret             
    // 0xb9427c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9427c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94280: b               #0xb94124
    // 0xb94284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94284: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb94288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94288: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4046, size: 0x1c, field offset: 0xc
//   const constructor, 
class SizePicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fb94, size: 0x28
    // 0xc7fb94: EnterFrame
    //     0xc7fb94: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fb98: mov             fp, SP
    // 0xc7fb9c: mov             x0, x1
    // 0xc7fba0: r1 = <SizePicker>
    //     0xc7fba0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48728] TypeArguments: <SizePicker>
    //     0xc7fba4: ldr             x1, [x1, #0x728]
    // 0xc7fba8: r0 = _SizePickerState()
    //     0xc7fba8: bl              #0xc7fbbc  ; Allocate_SizePickerStateStub -> _SizePickerState (size=0x1c)
    // 0xc7fbac: ArrayStore: r0[0] = rZR  ; List_4
    //     0xc7fbac: stur            wzr, [x0, #0x17]
    // 0xc7fbb0: LeaveFrame
    //     0xc7fbb0: mov             SP, fp
    //     0xc7fbb4: ldp             fp, lr, [SP], #0x10
    // 0xc7fbb8: ret
    //     0xc7fbb8: ret             
  }
}
