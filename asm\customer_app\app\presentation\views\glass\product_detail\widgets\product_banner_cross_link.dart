// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart

// class id: 1049433, size: 0x8
class :: {
}

// class id: 3318, size: 0x14, field offset: 0x14
class _ProductBannerCrossLinkState extends State<dynamic> {

  [closure] CachedNetworkImage <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0xa422dc, size: 0x4c
    // 0xa422dc: EnterFrame
    //     0xa422dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa422e0: mov             fp, SP
    // 0xa422e4: AllocStack(0x8)
    //     0xa422e4: sub             SP, SP, #8
    // 0xa422e8: CheckStackOverflow
    //     0xa422e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa422ec: cmp             SP, x16
    //     0xa422f0: b.ls            #0xa42320
    // 0xa422f4: r0 = CachedNetworkImage()
    //     0xa422f4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa422f8: mov             x1, x0
    // 0xa422fc: r2 = "https://d1311wbk6unapo.cloudfront.net/NushopWebsiteAsset/image_placeholder_2.png"
    //     0xa422fc: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c58] "https://d1311wbk6unapo.cloudfront.net/NushopWebsiteAsset/image_placeholder_2.png"
    //     0xa42300: ldr             x2, [x2, #0xc58]
    // 0xa42304: stur            x0, [fp, #-8]
    // 0xa42308: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa42308: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4230c: r0 = CachedNetworkImage()
    //     0xa4230c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa42310: ldur            x0, [fp, #-8]
    // 0xa42314: LeaveFrame
    //     0xa42314: mov             SP, fp
    //     0xa42318: ldp             fp, lr, [SP], #0x10
    // 0xa4231c: ret
    //     0xa4231c: ret             
    // 0xa42320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42320: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42324: b               #0xa422f4
  }
  _ bannerSlider(/* No info */) {
    // ** addr: 0xa42328, size: 0xc70
    // 0xa42328: EnterFrame
    //     0xa42328: stp             fp, lr, [SP, #-0x10]!
    //     0xa4232c: mov             fp, SP
    // 0xa42330: AllocStack(0x78)
    //     0xa42330: sub             SP, SP, #0x78
    // 0xa42334: SetupParameters(_ProductBannerCrossLinkState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa42334: stur            x1, [fp, #-8]
    //     0xa42338: stur            x2, [fp, #-0x10]
    //     0xa4233c: stur            x3, [fp, #-0x18]
    // 0xa42340: CheckStackOverflow
    //     0xa42340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42344: cmp             SP, x16
    //     0xa42348: b.ls            #0xa42f3c
    // 0xa4234c: r1 = 2
    //     0xa4234c: movz            x1, #0x2
    // 0xa42350: r0 = AllocateContext()
    //     0xa42350: bl              #0x16f6108  ; AllocateContextStub
    // 0xa42354: mov             x3, x0
    // 0xa42358: ldur            x2, [fp, #-8]
    // 0xa4235c: stur            x3, [fp, #-0x20]
    // 0xa42360: StoreField: r3->field_f = r2
    //     0xa42360: stur            w2, [x3, #0xf]
    // 0xa42364: ldur            x4, [fp, #-0x18]
    // 0xa42368: r0 = BoxInt64Instr(r4)
    //     0xa42368: sbfiz           x0, x4, #1, #0x1f
    //     0xa4236c: cmp             x4, x0, asr #1
    //     0xa42370: b.eq            #0xa4237c
    //     0xa42374: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa42378: stur            x4, [x0, #7]
    // 0xa4237c: StoreField: r3->field_13 = r0
    //     0xa4237c: stur            w0, [x3, #0x13]
    // 0xa42380: ldur            x1, [fp, #-0x10]
    // 0xa42384: r4 = LoadClassIdInstr(r1)
    //     0xa42384: ldur            x4, [x1, #-1]
    //     0xa42388: ubfx            x4, x4, #0xc, #0x14
    // 0xa4238c: stp             x0, x1, [SP]
    // 0xa42390: mov             x0, x4
    // 0xa42394: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa42394: sub             lr, x0, #0xb7
    //     0xa42398: ldr             lr, [x21, lr, lsl #3]
    //     0xa4239c: blr             lr
    // 0xa423a0: r17 = 299
    //     0xa423a0: movz            x17, #0x12b
    // 0xa423a4: ldr             w1, [x0, x17]
    // 0xa423a8: DecompressPointer r1
    //     0xa423a8: add             x1, x1, HEAP, lsl #32
    // 0xa423ac: cmp             w1, NULL
    // 0xa423b0: b.ne            #0xa423e8
    // 0xa423b4: ldur            x3, [fp, #-8]
    // 0xa423b8: ldur            x5, [fp, #-0x10]
    // 0xa423bc: ldur            x4, [fp, #-0x20]
    // 0xa423c0: r6 = Instance_Alignment
    //     0xa423c0: add             x6, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xa423c4: ldr             x6, [x6, #0xcb0]
    // 0xa423c8: r2 = 4
    //     0xa423c8: movz            x2, #0x4
    // 0xa423cc: r8 = Instance_Clip
    //     0xa423cc: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa423d0: ldr             x8, [x8, #0x7e0]
    // 0xa423d4: r7 = Instance_StackFit
    //     0xa423d4: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa423d8: ldr             x7, [x7, #0xfa8]
    // 0xa423dc: r0 = Instance_Clip
    //     0xa423dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa423e0: ldr             x0, [x0, #0x138]
    // 0xa423e4: b               #0xa42764
    // 0xa423e8: tbnz            w1, #4, #0xa42734
    // 0xa423ec: ldur            x0, [fp, #-8]
    // 0xa423f0: ldur            x3, [fp, #-0x10]
    // 0xa423f4: ldur            x2, [fp, #-0x20]
    // 0xa423f8: LoadField: r1 = r0->field_b
    //     0xa423f8: ldur            w1, [x0, #0xb]
    // 0xa423fc: DecompressPointer r1
    //     0xa423fc: add             x1, x1, HEAP, lsl #32
    // 0xa42400: cmp             w1, NULL
    // 0xa42404: b.eq            #0xa42f44
    // 0xa42408: LoadField: r4 = r1->field_2f
    //     0xa42408: ldur            w4, [x1, #0x2f]
    // 0xa4240c: DecompressPointer r4
    //     0xa4240c: add             x4, x4, HEAP, lsl #32
    // 0xa42410: stur            x4, [fp, #-0x28]
    // 0xa42414: LoadField: r1 = r0->field_f
    //     0xa42414: ldur            w1, [x0, #0xf]
    // 0xa42418: DecompressPointer r1
    //     0xa42418: add             x1, x1, HEAP, lsl #32
    // 0xa4241c: cmp             w1, NULL
    // 0xa42420: b.eq            #0xa42f48
    // 0xa42424: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa42424: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa42428: r0 = _of()
    //     0xa42428: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa4242c: LoadField: r1 = r0->field_7
    //     0xa4242c: ldur            w1, [x0, #7]
    // 0xa42430: DecompressPointer r1
    //     0xa42430: add             x1, x1, HEAP, lsl #32
    // 0xa42434: LoadField: d0 = r1->field_7
    //     0xa42434: ldur            d0, [x1, #7]
    // 0xa42438: ldur            x1, [fp, #-8]
    // 0xa4243c: stur            d0, [fp, #-0x60]
    // 0xa42440: LoadField: r0 = r1->field_b
    //     0xa42440: ldur            w0, [x1, #0xb]
    // 0xa42444: DecompressPointer r0
    //     0xa42444: add             x0, x0, HEAP, lsl #32
    // 0xa42448: cmp             w0, NULL
    // 0xa4244c: b.eq            #0xa42f4c
    // 0xa42450: LoadField: r2 = r0->field_33
    //     0xa42450: ldur            w2, [x0, #0x33]
    // 0xa42454: DecompressPointer r2
    //     0xa42454: add             x2, x2, HEAP, lsl #32
    // 0xa42458: ldur            x3, [fp, #-0x20]
    // 0xa4245c: stur            x2, [fp, #-0x30]
    // 0xa42460: LoadField: r0 = r3->field_13
    //     0xa42460: ldur            w0, [x3, #0x13]
    // 0xa42464: DecompressPointer r0
    //     0xa42464: add             x0, x0, HEAP, lsl #32
    // 0xa42468: ldur            x4, [fp, #-0x10]
    // 0xa4246c: r5 = LoadClassIdInstr(r4)
    //     0xa4246c: ldur            x5, [x4, #-1]
    //     0xa42470: ubfx            x5, x5, #0xc, #0x14
    // 0xa42474: stp             x0, x4, [SP]
    // 0xa42478: mov             x0, x5
    // 0xa4247c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4247c: sub             lr, x0, #0xb7
    //     0xa42480: ldr             lr, [x21, lr, lsl #3]
    //     0xa42484: blr             lr
    // 0xa42488: LoadField: r1 = r0->field_13
    //     0xa42488: ldur            w1, [x0, #0x13]
    // 0xa4248c: DecompressPointer r1
    //     0xa4248c: add             x1, x1, HEAP, lsl #32
    // 0xa42490: cmp             w1, NULL
    // 0xa42494: b.ne            #0xa424a0
    // 0xa42498: r5 = ""
    //     0xa42498: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4249c: b               #0xa424a4
    // 0xa424a0: mov             x5, x1
    // 0xa424a4: ldur            x4, [fp, #-0x10]
    // 0xa424a8: ldur            x3, [fp, #-0x20]
    // 0xa424ac: ldur            x0, [fp, #-0x30]
    // 0xa424b0: ldur            d0, [fp, #-0x60]
    // 0xa424b4: stur            x5, [fp, #-0x38]
    // 0xa424b8: r1 = Function '<anonymous closure>':.
    //     0xa424b8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55860] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa424bc: ldr             x1, [x1, #0x860]
    // 0xa424c0: r2 = Null
    //     0xa424c0: mov             x2, NULL
    // 0xa424c4: r0 = AllocateClosure()
    //     0xa424c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa424c8: r1 = Function '<anonymous closure>':.
    //     0xa424c8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55868] AnonymousClosure: (0xa422dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xa424cc: ldr             x1, [x1, #0x868]
    // 0xa424d0: r2 = Null
    //     0xa424d0: mov             x2, NULL
    // 0xa424d4: stur            x0, [fp, #-0x40]
    // 0xa424d8: r0 = AllocateClosure()
    //     0xa424d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa424dc: stur            x0, [fp, #-0x48]
    // 0xa424e0: r0 = CachedNetworkImage()
    //     0xa424e0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa424e4: stur            x0, [fp, #-0x50]
    // 0xa424e8: r16 = Instance_BoxFit
    //     0xa424e8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa424ec: ldr             x16, [x16, #0x118]
    // 0xa424f0: ldur            lr, [fp, #-0x40]
    // 0xa424f4: stp             lr, x16, [SP, #8]
    // 0xa424f8: ldur            x16, [fp, #-0x48]
    // 0xa424fc: str             x16, [SP]
    // 0xa42500: mov             x1, x0
    // 0xa42504: ldur            x2, [fp, #-0x38]
    // 0xa42508: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xa42508: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xa4250c: ldr             x4, [x4, #0x638]
    // 0xa42510: r0 = CachedNetworkImage()
    //     0xa42510: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa42514: r0 = ClipRRect()
    //     0xa42514: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa42518: mov             x1, x0
    // 0xa4251c: ldur            x0, [fp, #-0x30]
    // 0xa42520: stur            x1, [fp, #-0x38]
    // 0xa42524: StoreField: r1->field_f = r0
    //     0xa42524: stur            w0, [x1, #0xf]
    // 0xa42528: r0 = Instance_Clip
    //     0xa42528: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa4252c: ldr             x0, [x0, #0x138]
    // 0xa42530: ArrayStore: r1[0] = r0  ; List_4
    //     0xa42530: stur            w0, [x1, #0x17]
    // 0xa42534: ldur            x0, [fp, #-0x50]
    // 0xa42538: StoreField: r1->field_b = r0
    //     0xa42538: stur            w0, [x1, #0xb]
    // 0xa4253c: ldur            d0, [fp, #-0x60]
    // 0xa42540: r0 = inline_Allocate_Double()
    //     0xa42540: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa42544: add             x0, x0, #0x10
    //     0xa42548: cmp             x2, x0
    //     0xa4254c: b.ls            #0xa42f50
    //     0xa42550: str             x0, [THR, #0x50]  ; THR::top
    //     0xa42554: sub             x0, x0, #0xf
    //     0xa42558: movz            x2, #0xe15c
    //     0xa4255c: movk            x2, #0x3, lsl #16
    //     0xa42560: stur            x2, [x0, #-1]
    // 0xa42564: StoreField: r0->field_7 = d0
    //     0xa42564: stur            d0, [x0, #7]
    // 0xa42568: stur            x0, [fp, #-0x30]
    // 0xa4256c: r0 = Container()
    //     0xa4256c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa42570: stur            x0, [fp, #-0x40]
    // 0xa42574: ldur            x16, [fp, #-0x28]
    // 0xa42578: ldur            lr, [fp, #-0x30]
    // 0xa4257c: stp             lr, x16, [SP, #8]
    // 0xa42580: ldur            x16, [fp, #-0x38]
    // 0xa42584: str             x16, [SP]
    // 0xa42588: mov             x1, x0
    // 0xa4258c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xa4258c: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xa42590: ldr             x4, [x4, #0x1b8]
    // 0xa42594: r0 = Container()
    //     0xa42594: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa42598: ldur            x2, [fp, #-0x20]
    // 0xa4259c: LoadField: r0 = r2->field_13
    //     0xa4259c: ldur            w0, [x2, #0x13]
    // 0xa425a0: DecompressPointer r0
    //     0xa425a0: add             x0, x0, HEAP, lsl #32
    // 0xa425a4: ldur            x1, [fp, #-0x10]
    // 0xa425a8: r3 = LoadClassIdInstr(r1)
    //     0xa425a8: ldur            x3, [x1, #-1]
    //     0xa425ac: ubfx            x3, x3, #0xc, #0x14
    // 0xa425b0: stp             x0, x1, [SP]
    // 0xa425b4: mov             x0, x3
    // 0xa425b8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa425b8: sub             lr, x0, #0xb7
    //     0xa425bc: ldr             lr, [x21, lr, lsl #3]
    //     0xa425c0: blr             lr
    // 0xa425c4: LoadField: r1 = r0->field_7
    //     0xa425c4: ldur            w1, [x0, #7]
    // 0xa425c8: DecompressPointer r1
    //     0xa425c8: add             x1, x1, HEAP, lsl #32
    // 0xa425cc: cmp             w1, NULL
    // 0xa425d0: b.ne            #0xa425dc
    // 0xa425d4: r4 = ""
    //     0xa425d4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa425d8: b               #0xa425e0
    // 0xa425dc: mov             x4, x1
    // 0xa425e0: ldur            x3, [fp, #-8]
    // 0xa425e4: ldur            x0, [fp, #-0x10]
    // 0xa425e8: ldur            x2, [fp, #-0x20]
    // 0xa425ec: stur            x4, [fp, #-0x28]
    // 0xa425f0: LoadField: r1 = r3->field_f
    //     0xa425f0: ldur            w1, [x3, #0xf]
    // 0xa425f4: DecompressPointer r1
    //     0xa425f4: add             x1, x1, HEAP, lsl #32
    // 0xa425f8: cmp             w1, NULL
    // 0xa425fc: b.eq            #0xa42f68
    // 0xa42600: r0 = of()
    //     0xa42600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa42604: LoadField: r1 = r0->field_87
    //     0xa42604: ldur            w1, [x0, #0x87]
    // 0xa42608: DecompressPointer r1
    //     0xa42608: add             x1, x1, HEAP, lsl #32
    // 0xa4260c: LoadField: r2 = r1->field_7
    //     0xa4260c: ldur            w2, [x1, #7]
    // 0xa42610: DecompressPointer r2
    //     0xa42610: add             x2, x2, HEAP, lsl #32
    // 0xa42614: ldur            x4, [fp, #-0x20]
    // 0xa42618: stur            x2, [fp, #-0x30]
    // 0xa4261c: LoadField: r0 = r4->field_13
    //     0xa4261c: ldur            w0, [x4, #0x13]
    // 0xa42620: DecompressPointer r0
    //     0xa42620: add             x0, x0, HEAP, lsl #32
    // 0xa42624: ldur            x5, [fp, #-0x10]
    // 0xa42628: r1 = LoadClassIdInstr(r5)
    //     0xa42628: ldur            x1, [x5, #-1]
    //     0xa4262c: ubfx            x1, x1, #0xc, #0x14
    // 0xa42630: stp             x0, x5, [SP]
    // 0xa42634: mov             x0, x1
    // 0xa42638: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa42638: sub             lr, x0, #0xb7
    //     0xa4263c: ldr             lr, [x21, lr, lsl #3]
    //     0xa42640: blr             lr
    // 0xa42644: r17 = 307
    //     0xa42644: movz            x17, #0x133
    // 0xa42648: ldr             w1, [x0, x17]
    // 0xa4264c: DecompressPointer r1
    //     0xa4264c: add             x1, x1, HEAP, lsl #32
    // 0xa42650: cmp             w1, NULL
    // 0xa42654: b.ne            #0xa42660
    // 0xa42658: r0 = Null
    //     0xa42658: mov             x0, NULL
    // 0xa4265c: b               #0xa42664
    // 0xa42660: r0 = ColorExtension.toColor()
    //     0xa42660: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0xa42664: cmp             w0, NULL
    // 0xa42668: b.ne            #0xa42674
    // 0xa4266c: r1 = Instance_Color
    //     0xa4266c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa42670: b               #0xa42678
    // 0xa42674: mov             x1, x0
    // 0xa42678: ldur            x2, [fp, #-0x40]
    // 0xa4267c: ldur            x0, [fp, #-0x28]
    // 0xa42680: r16 = 16.000000
    //     0xa42680: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa42684: ldr             x16, [x16, #0x188]
    // 0xa42688: stp             x16, x1, [SP]
    // 0xa4268c: ldur            x1, [fp, #-0x30]
    // 0xa42690: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa42690: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa42694: ldr             x4, [x4, #0x9b8]
    // 0xa42698: r0 = copyWith()
    //     0xa42698: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4269c: stur            x0, [fp, #-0x30]
    // 0xa426a0: r0 = Text()
    //     0xa426a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa426a4: mov             x3, x0
    // 0xa426a8: ldur            x0, [fp, #-0x28]
    // 0xa426ac: stur            x3, [fp, #-0x38]
    // 0xa426b0: StoreField: r3->field_b = r0
    //     0xa426b0: stur            w0, [x3, #0xb]
    // 0xa426b4: ldur            x0, [fp, #-0x30]
    // 0xa426b8: StoreField: r3->field_13 = r0
    //     0xa426b8: stur            w0, [x3, #0x13]
    // 0xa426bc: r1 = Null
    //     0xa426bc: mov             x1, NULL
    // 0xa426c0: r2 = 4
    //     0xa426c0: movz            x2, #0x4
    // 0xa426c4: r0 = AllocateArray()
    //     0xa426c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa426c8: mov             x2, x0
    // 0xa426cc: ldur            x0, [fp, #-0x40]
    // 0xa426d0: stur            x2, [fp, #-0x28]
    // 0xa426d4: StoreField: r2->field_f = r0
    //     0xa426d4: stur            w0, [x2, #0xf]
    // 0xa426d8: ldur            x0, [fp, #-0x38]
    // 0xa426dc: StoreField: r2->field_13 = r0
    //     0xa426dc: stur            w0, [x2, #0x13]
    // 0xa426e0: r1 = <Widget>
    //     0xa426e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa426e4: r0 = AllocateGrowableArray()
    //     0xa426e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa426e8: mov             x1, x0
    // 0xa426ec: ldur            x0, [fp, #-0x28]
    // 0xa426f0: stur            x1, [fp, #-0x30]
    // 0xa426f4: StoreField: r1->field_f = r0
    //     0xa426f4: stur            w0, [x1, #0xf]
    // 0xa426f8: r2 = 4
    //     0xa426f8: movz            x2, #0x4
    // 0xa426fc: StoreField: r1->field_b = r2
    //     0xa426fc: stur            w2, [x1, #0xb]
    // 0xa42700: r0 = Stack()
    //     0xa42700: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa42704: r6 = Instance_Alignment
    //     0xa42704: add             x6, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xa42708: ldr             x6, [x6, #0xcb0]
    // 0xa4270c: StoreField: r0->field_f = r6
    //     0xa4270c: stur            w6, [x0, #0xf]
    // 0xa42710: r7 = Instance_StackFit
    //     0xa42710: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa42714: ldr             x7, [x7, #0xfa8]
    // 0xa42718: ArrayStore: r0[0] = r7  ; List_4
    //     0xa42718: stur            w7, [x0, #0x17]
    // 0xa4271c: r8 = Instance_Clip
    //     0xa4271c: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa42720: ldr             x8, [x8, #0x7e0]
    // 0xa42724: StoreField: r0->field_1b = r8
    //     0xa42724: stur            w8, [x0, #0x1b]
    // 0xa42728: ldur            x1, [fp, #-0x30]
    // 0xa4272c: StoreField: r0->field_b = r1
    //     0xa4272c: stur            w1, [x0, #0xb]
    // 0xa42730: b               #0xa42f30
    // 0xa42734: ldur            x3, [fp, #-8]
    // 0xa42738: ldur            x5, [fp, #-0x10]
    // 0xa4273c: ldur            x4, [fp, #-0x20]
    // 0xa42740: r6 = Instance_Alignment
    //     0xa42740: add             x6, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xa42744: ldr             x6, [x6, #0xcb0]
    // 0xa42748: r2 = 4
    //     0xa42748: movz            x2, #0x4
    // 0xa4274c: r8 = Instance_Clip
    //     0xa4274c: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa42750: ldr             x8, [x8, #0x7e0]
    // 0xa42754: r7 = Instance_StackFit
    //     0xa42754: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa42758: ldr             x7, [x7, #0xfa8]
    // 0xa4275c: r0 = Instance_Clip
    //     0xa4275c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa42760: ldr             x0, [x0, #0x138]
    // 0xa42764: LoadField: r1 = r3->field_f
    //     0xa42764: ldur            w1, [x3, #0xf]
    // 0xa42768: DecompressPointer r1
    //     0xa42768: add             x1, x1, HEAP, lsl #32
    // 0xa4276c: cmp             w1, NULL
    // 0xa42770: b.eq            #0xa42f6c
    // 0xa42774: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa42774: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa42778: r0 = _of()
    //     0xa42778: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa4277c: LoadField: r1 = r0->field_7
    //     0xa4277c: ldur            w1, [x0, #7]
    // 0xa42780: DecompressPointer r1
    //     0xa42780: add             x1, x1, HEAP, lsl #32
    // 0xa42784: LoadField: d0 = r1->field_7
    //     0xa42784: ldur            d0, [x1, #7]
    // 0xa42788: ldur            x1, [fp, #-8]
    // 0xa4278c: stur            d0, [fp, #-0x60]
    // 0xa42790: LoadField: r0 = r1->field_b
    //     0xa42790: ldur            w0, [x1, #0xb]
    // 0xa42794: DecompressPointer r0
    //     0xa42794: add             x0, x0, HEAP, lsl #32
    // 0xa42798: cmp             w0, NULL
    // 0xa4279c: b.eq            #0xa42f70
    // 0xa427a0: LoadField: r2 = r0->field_2f
    //     0xa427a0: ldur            w2, [x0, #0x2f]
    // 0xa427a4: DecompressPointer r2
    //     0xa427a4: add             x2, x2, HEAP, lsl #32
    // 0xa427a8: stur            x2, [fp, #-0x30]
    // 0xa427ac: LoadField: r3 = r0->field_33
    //     0xa427ac: ldur            w3, [x0, #0x33]
    // 0xa427b0: DecompressPointer r3
    //     0xa427b0: add             x3, x3, HEAP, lsl #32
    // 0xa427b4: ldur            x4, [fp, #-0x20]
    // 0xa427b8: stur            x3, [fp, #-0x28]
    // 0xa427bc: LoadField: r0 = r4->field_13
    //     0xa427bc: ldur            w0, [x4, #0x13]
    // 0xa427c0: DecompressPointer r0
    //     0xa427c0: add             x0, x0, HEAP, lsl #32
    // 0xa427c4: ldur            x5, [fp, #-0x10]
    // 0xa427c8: r6 = LoadClassIdInstr(r5)
    //     0xa427c8: ldur            x6, [x5, #-1]
    //     0xa427cc: ubfx            x6, x6, #0xc, #0x14
    // 0xa427d0: stp             x0, x5, [SP]
    // 0xa427d4: mov             x0, x6
    // 0xa427d8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa427d8: sub             lr, x0, #0xb7
    //     0xa427dc: ldr             lr, [x21, lr, lsl #3]
    //     0xa427e0: blr             lr
    // 0xa427e4: LoadField: r1 = r0->field_13
    //     0xa427e4: ldur            w1, [x0, #0x13]
    // 0xa427e8: DecompressPointer r1
    //     0xa427e8: add             x1, x1, HEAP, lsl #32
    // 0xa427ec: cmp             w1, NULL
    // 0xa427f0: b.ne            #0xa427fc
    // 0xa427f4: r5 = ""
    //     0xa427f4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa427f8: b               #0xa42800
    // 0xa427fc: mov             x5, x1
    // 0xa42800: ldur            x4, [fp, #-0x10]
    // 0xa42804: ldur            x3, [fp, #-0x20]
    // 0xa42808: ldur            x0, [fp, #-0x28]
    // 0xa4280c: ldur            d0, [fp, #-0x60]
    // 0xa42810: stur            x5, [fp, #-0x38]
    // 0xa42814: r1 = Function '<anonymous closure>':.
    //     0xa42814: add             x1, PP, #0x55, lsl #12  ; [pp+0x55870] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa42818: ldr             x1, [x1, #0x870]
    // 0xa4281c: r2 = Null
    //     0xa4281c: mov             x2, NULL
    // 0xa42820: r0 = AllocateClosure()
    //     0xa42820: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa42824: r1 = Function '<anonymous closure>':.
    //     0xa42824: add             x1, PP, #0x55, lsl #12  ; [pp+0x55878] AnonymousClosure: (0xa422dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xa42828: ldr             x1, [x1, #0x878]
    // 0xa4282c: r2 = Null
    //     0xa4282c: mov             x2, NULL
    // 0xa42830: stur            x0, [fp, #-0x40]
    // 0xa42834: r0 = AllocateClosure()
    //     0xa42834: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa42838: stur            x0, [fp, #-0x48]
    // 0xa4283c: r0 = CachedNetworkImage()
    //     0xa4283c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa42840: stur            x0, [fp, #-0x50]
    // 0xa42844: r16 = Instance_BoxFit
    //     0xa42844: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa42848: ldr             x16, [x16, #0x118]
    // 0xa4284c: ldur            lr, [fp, #-0x40]
    // 0xa42850: stp             lr, x16, [SP, #8]
    // 0xa42854: ldur            x16, [fp, #-0x48]
    // 0xa42858: str             x16, [SP]
    // 0xa4285c: mov             x1, x0
    // 0xa42860: ldur            x2, [fp, #-0x38]
    // 0xa42864: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xa42864: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xa42868: ldr             x4, [x4, #0x638]
    // 0xa4286c: r0 = CachedNetworkImage()
    //     0xa4286c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa42870: r0 = ClipRRect()
    //     0xa42870: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa42874: mov             x1, x0
    // 0xa42878: ldur            x0, [fp, #-0x28]
    // 0xa4287c: stur            x1, [fp, #-0x38]
    // 0xa42880: StoreField: r1->field_f = r0
    //     0xa42880: stur            w0, [x1, #0xf]
    // 0xa42884: r0 = Instance_Clip
    //     0xa42884: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa42888: ldr             x0, [x0, #0x138]
    // 0xa4288c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4288c: stur            w0, [x1, #0x17]
    // 0xa42890: ldur            x0, [fp, #-0x50]
    // 0xa42894: StoreField: r1->field_b = r0
    //     0xa42894: stur            w0, [x1, #0xb]
    // 0xa42898: ldur            d0, [fp, #-0x60]
    // 0xa4289c: r0 = inline_Allocate_Double()
    //     0xa4289c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa428a0: add             x0, x0, #0x10
    //     0xa428a4: cmp             x2, x0
    //     0xa428a8: b.ls            #0xa42f74
    //     0xa428ac: str             x0, [THR, #0x50]  ; THR::top
    //     0xa428b0: sub             x0, x0, #0xf
    //     0xa428b4: movz            x2, #0xe15c
    //     0xa428b8: movk            x2, #0x3, lsl #16
    //     0xa428bc: stur            x2, [x0, #-1]
    // 0xa428c0: StoreField: r0->field_7 = d0
    //     0xa428c0: stur            d0, [x0, #7]
    // 0xa428c4: stur            x0, [fp, #-0x28]
    // 0xa428c8: r0 = Container()
    //     0xa428c8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa428cc: stur            x0, [fp, #-0x40]
    // 0xa428d0: ldur            x16, [fp, #-0x28]
    // 0xa428d4: ldur            lr, [fp, #-0x30]
    // 0xa428d8: stp             lr, x16, [SP, #8]
    // 0xa428dc: ldur            x16, [fp, #-0x38]
    // 0xa428e0: str             x16, [SP]
    // 0xa428e4: mov             x1, x0
    // 0xa428e8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xa428e8: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xa428ec: ldr             x4, [x4, #0x628]
    // 0xa428f0: r0 = Container()
    //     0xa428f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa428f4: ldur            x2, [fp, #-0x20]
    // 0xa428f8: LoadField: r0 = r2->field_13
    //     0xa428f8: ldur            w0, [x2, #0x13]
    // 0xa428fc: DecompressPointer r0
    //     0xa428fc: add             x0, x0, HEAP, lsl #32
    // 0xa42900: ldur            x1, [fp, #-0x10]
    // 0xa42904: r3 = LoadClassIdInstr(r1)
    //     0xa42904: ldur            x3, [x1, #-1]
    //     0xa42908: ubfx            x3, x3, #0xc, #0x14
    // 0xa4290c: stp             x0, x1, [SP]
    // 0xa42910: mov             x0, x3
    // 0xa42914: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa42914: sub             lr, x0, #0xb7
    //     0xa42918: ldr             lr, [x21, lr, lsl #3]
    //     0xa4291c: blr             lr
    // 0xa42920: LoadField: r1 = r0->field_7
    //     0xa42920: ldur            w1, [x0, #7]
    // 0xa42924: DecompressPointer r1
    //     0xa42924: add             x1, x1, HEAP, lsl #32
    // 0xa42928: cmp             w1, NULL
    // 0xa4292c: b.ne            #0xa42938
    // 0xa42930: r0 = Null
    //     0xa42930: mov             x0, NULL
    // 0xa42934: b               #0xa42950
    // 0xa42938: LoadField: r0 = r1->field_7
    //     0xa42938: ldur            w0, [x1, #7]
    // 0xa4293c: cbnz            w0, #0xa42948
    // 0xa42940: r1 = false
    //     0xa42940: add             x1, NULL, #0x30  ; false
    // 0xa42944: b               #0xa4294c
    // 0xa42948: r1 = true
    //     0xa42948: add             x1, NULL, #0x20  ; true
    // 0xa4294c: mov             x0, x1
    // 0xa42950: cmp             w0, NULL
    // 0xa42954: b.ne            #0xa42960
    // 0xa42958: r3 = false
    //     0xa42958: add             x3, NULL, #0x30  ; false
    // 0xa4295c: b               #0xa42964
    // 0xa42960: mov             x3, x0
    // 0xa42964: ldur            x1, [fp, #-0x10]
    // 0xa42968: ldur            x2, [fp, #-0x20]
    // 0xa4296c: stur            x3, [fp, #-0x28]
    // 0xa42970: LoadField: r0 = r2->field_13
    //     0xa42970: ldur            w0, [x2, #0x13]
    // 0xa42974: DecompressPointer r0
    //     0xa42974: add             x0, x0, HEAP, lsl #32
    // 0xa42978: r4 = LoadClassIdInstr(r1)
    //     0xa42978: ldur            x4, [x1, #-1]
    //     0xa4297c: ubfx            x4, x4, #0xc, #0x14
    // 0xa42980: stp             x0, x1, [SP]
    // 0xa42984: mov             x0, x4
    // 0xa42988: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa42988: sub             lr, x0, #0xb7
    //     0xa4298c: ldr             lr, [x21, lr, lsl #3]
    //     0xa42990: blr             lr
    // 0xa42994: LoadField: r1 = r0->field_7
    //     0xa42994: ldur            w1, [x0, #7]
    // 0xa42998: DecompressPointer r1
    //     0xa42998: add             x1, x1, HEAP, lsl #32
    // 0xa4299c: cmp             w1, NULL
    // 0xa429a0: b.ne            #0xa429ac
    // 0xa429a4: r4 = ""
    //     0xa429a4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa429a8: b               #0xa429b0
    // 0xa429ac: mov             x4, x1
    // 0xa429b0: ldur            x3, [fp, #-8]
    // 0xa429b4: ldur            x0, [fp, #-0x10]
    // 0xa429b8: ldur            x2, [fp, #-0x20]
    // 0xa429bc: stur            x4, [fp, #-0x30]
    // 0xa429c0: LoadField: r1 = r3->field_f
    //     0xa429c0: ldur            w1, [x3, #0xf]
    // 0xa429c4: DecompressPointer r1
    //     0xa429c4: add             x1, x1, HEAP, lsl #32
    // 0xa429c8: cmp             w1, NULL
    // 0xa429cc: b.eq            #0xa42f8c
    // 0xa429d0: r0 = of()
    //     0xa429d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa429d4: LoadField: r1 = r0->field_87
    //     0xa429d4: ldur            w1, [x0, #0x87]
    // 0xa429d8: DecompressPointer r1
    //     0xa429d8: add             x1, x1, HEAP, lsl #32
    // 0xa429dc: LoadField: r0 = r1->field_27
    //     0xa429dc: ldur            w0, [x1, #0x27]
    // 0xa429e0: DecompressPointer r0
    //     0xa429e0: add             x0, x0, HEAP, lsl #32
    // 0xa429e4: r16 = 21.000000
    //     0xa429e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa429e8: ldr             x16, [x16, #0x9b0]
    // 0xa429ec: r30 = Instance_Color
    //     0xa429ec: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa429f0: stp             lr, x16, [SP]
    // 0xa429f4: mov             x1, x0
    // 0xa429f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa429f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa429fc: ldr             x4, [x4, #0xaa0]
    // 0xa42a00: r0 = copyWith()
    //     0xa42a00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa42a04: stur            x0, [fp, #-0x38]
    // 0xa42a08: r0 = Text()
    //     0xa42a08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa42a0c: mov             x1, x0
    // 0xa42a10: ldur            x0, [fp, #-0x30]
    // 0xa42a14: stur            x1, [fp, #-0x48]
    // 0xa42a18: StoreField: r1->field_b = r0
    //     0xa42a18: stur            w0, [x1, #0xb]
    // 0xa42a1c: ldur            x0, [fp, #-0x38]
    // 0xa42a20: StoreField: r1->field_13 = r0
    //     0xa42a20: stur            w0, [x1, #0x13]
    // 0xa42a24: r0 = Padding()
    //     0xa42a24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa42a28: mov             x1, x0
    // 0xa42a2c: r0 = Instance_EdgeInsets
    //     0xa42a2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f078] Obj!EdgeInsets@d571a1
    //     0xa42a30: ldr             x0, [x0, #0x78]
    // 0xa42a34: stur            x1, [fp, #-0x30]
    // 0xa42a38: StoreField: r1->field_f = r0
    //     0xa42a38: stur            w0, [x1, #0xf]
    // 0xa42a3c: ldur            x0, [fp, #-0x48]
    // 0xa42a40: StoreField: r1->field_b = r0
    //     0xa42a40: stur            w0, [x1, #0xb]
    // 0xa42a44: ldur            x2, [fp, #-0x20]
    // 0xa42a48: LoadField: r0 = r2->field_13
    //     0xa42a48: ldur            w0, [x2, #0x13]
    // 0xa42a4c: DecompressPointer r0
    //     0xa42a4c: add             x0, x0, HEAP, lsl #32
    // 0xa42a50: ldur            x3, [fp, #-0x10]
    // 0xa42a54: r4 = LoadClassIdInstr(r3)
    //     0xa42a54: ldur            x4, [x3, #-1]
    //     0xa42a58: ubfx            x4, x4, #0xc, #0x14
    // 0xa42a5c: stp             x0, x3, [SP]
    // 0xa42a60: mov             x0, x4
    // 0xa42a64: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa42a64: sub             lr, x0, #0xb7
    //     0xa42a68: ldr             lr, [x21, lr, lsl #3]
    //     0xa42a6c: blr             lr
    // 0xa42a70: LoadField: r1 = r0->field_f
    //     0xa42a70: ldur            w1, [x0, #0xf]
    // 0xa42a74: DecompressPointer r1
    //     0xa42a74: add             x1, x1, HEAP, lsl #32
    // 0xa42a78: cmp             w1, NULL
    // 0xa42a7c: b.ne            #0xa42a88
    // 0xa42a80: r0 = Null
    //     0xa42a80: mov             x0, NULL
    // 0xa42a84: b               #0xa42aa0
    // 0xa42a88: LoadField: r0 = r1->field_7
    //     0xa42a88: ldur            w0, [x1, #7]
    // 0xa42a8c: cbnz            w0, #0xa42a98
    // 0xa42a90: r1 = false
    //     0xa42a90: add             x1, NULL, #0x30  ; false
    // 0xa42a94: b               #0xa42a9c
    // 0xa42a98: r1 = true
    //     0xa42a98: add             x1, NULL, #0x20  ; true
    // 0xa42a9c: mov             x0, x1
    // 0xa42aa0: cmp             w0, NULL
    // 0xa42aa4: b.ne            #0xa42ab0
    // 0xa42aa8: r1 = false
    //     0xa42aa8: add             x1, NULL, #0x30  ; false
    // 0xa42aac: b               #0xa42ab4
    // 0xa42ab0: mov             x1, x0
    // 0xa42ab4: ldur            x0, [fp, #-0x10]
    // 0xa42ab8: ldur            x2, [fp, #-0x20]
    // 0xa42abc: stur            x1, [fp, #-0x38]
    // 0xa42ac0: r16 = <Color>
    //     0xa42ac0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa42ac4: ldr             x16, [x16, #0xf80]
    // 0xa42ac8: r30 = Instance_Color
    //     0xa42ac8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa42acc: stp             lr, x16, [SP]
    // 0xa42ad0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa42ad0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa42ad4: r0 = all()
    //     0xa42ad4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa42ad8: stur            x0, [fp, #-0x48]
    // 0xa42adc: r0 = Radius()
    //     0xa42adc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa42ae0: d0 = 20.000000
    //     0xa42ae0: fmov            d0, #20.00000000
    // 0xa42ae4: stur            x0, [fp, #-0x50]
    // 0xa42ae8: StoreField: r0->field_7 = d0
    //     0xa42ae8: stur            d0, [x0, #7]
    // 0xa42aec: StoreField: r0->field_f = d0
    //     0xa42aec: stur            d0, [x0, #0xf]
    // 0xa42af0: r0 = BorderRadius()
    //     0xa42af0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa42af4: mov             x1, x0
    // 0xa42af8: ldur            x0, [fp, #-0x50]
    // 0xa42afc: stur            x1, [fp, #-0x58]
    // 0xa42b00: StoreField: r1->field_7 = r0
    //     0xa42b00: stur            w0, [x1, #7]
    // 0xa42b04: StoreField: r1->field_b = r0
    //     0xa42b04: stur            w0, [x1, #0xb]
    // 0xa42b08: StoreField: r1->field_f = r0
    //     0xa42b08: stur            w0, [x1, #0xf]
    // 0xa42b0c: StoreField: r1->field_13 = r0
    //     0xa42b0c: stur            w0, [x1, #0x13]
    // 0xa42b10: r0 = RoundedRectangleBorder()
    //     0xa42b10: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa42b14: mov             x1, x0
    // 0xa42b18: ldur            x0, [fp, #-0x58]
    // 0xa42b1c: StoreField: r1->field_b = r0
    //     0xa42b1c: stur            w0, [x1, #0xb]
    // 0xa42b20: r0 = Instance_BorderSide
    //     0xa42b20: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa42b24: ldr             x0, [x0, #0xe20]
    // 0xa42b28: StoreField: r1->field_7 = r0
    //     0xa42b28: stur            w0, [x1, #7]
    // 0xa42b2c: r16 = <RoundedRectangleBorder>
    //     0xa42b2c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa42b30: ldr             x16, [x16, #0xf78]
    // 0xa42b34: stp             x1, x16, [SP]
    // 0xa42b38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa42b38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa42b3c: r0 = all()
    //     0xa42b3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa42b40: stur            x0, [fp, #-0x50]
    // 0xa42b44: r0 = ButtonStyle()
    //     0xa42b44: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa42b48: mov             x1, x0
    // 0xa42b4c: ldur            x0, [fp, #-0x48]
    // 0xa42b50: stur            x1, [fp, #-0x58]
    // 0xa42b54: StoreField: r1->field_b = r0
    //     0xa42b54: stur            w0, [x1, #0xb]
    // 0xa42b58: ldur            x0, [fp, #-0x50]
    // 0xa42b5c: StoreField: r1->field_43 = r0
    //     0xa42b5c: stur            w0, [x1, #0x43]
    // 0xa42b60: r0 = TextButtonThemeData()
    //     0xa42b60: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa42b64: mov             x1, x0
    // 0xa42b68: ldur            x0, [fp, #-0x58]
    // 0xa42b6c: stur            x1, [fp, #-0x48]
    // 0xa42b70: StoreField: r1->field_7 = r0
    //     0xa42b70: stur            w0, [x1, #7]
    // 0xa42b74: ldur            x2, [fp, #-0x20]
    // 0xa42b78: LoadField: r0 = r2->field_13
    //     0xa42b78: ldur            w0, [x2, #0x13]
    // 0xa42b7c: DecompressPointer r0
    //     0xa42b7c: add             x0, x0, HEAP, lsl #32
    // 0xa42b80: ldur            x3, [fp, #-0x10]
    // 0xa42b84: r4 = LoadClassIdInstr(r3)
    //     0xa42b84: ldur            x4, [x3, #-1]
    //     0xa42b88: ubfx            x4, x4, #0xc, #0x14
    // 0xa42b8c: stp             x0, x3, [SP]
    // 0xa42b90: mov             x0, x4
    // 0xa42b94: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa42b94: sub             lr, x0, #0xb7
    //     0xa42b98: ldr             lr, [x21, lr, lsl #3]
    //     0xa42b9c: blr             lr
    // 0xa42ba0: LoadField: r1 = r0->field_f
    //     0xa42ba0: ldur            w1, [x0, #0xf]
    // 0xa42ba4: DecompressPointer r1
    //     0xa42ba4: add             x1, x1, HEAP, lsl #32
    // 0xa42ba8: cmp             w1, NULL
    // 0xa42bac: b.ne            #0xa42bb8
    // 0xa42bb0: r7 = ""
    //     0xa42bb0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa42bb4: b               #0xa42bbc
    // 0xa42bb8: mov             x7, x1
    // 0xa42bbc: ldur            x4, [fp, #-8]
    // 0xa42bc0: ldur            x6, [fp, #-0x40]
    // 0xa42bc4: ldur            x5, [fp, #-0x28]
    // 0xa42bc8: ldur            x3, [fp, #-0x30]
    // 0xa42bcc: ldur            x2, [fp, #-0x38]
    // 0xa42bd0: ldur            x0, [fp, #-0x48]
    // 0xa42bd4: stur            x7, [fp, #-0x10]
    // 0xa42bd8: LoadField: r1 = r4->field_f
    //     0xa42bd8: ldur            w1, [x4, #0xf]
    // 0xa42bdc: DecompressPointer r1
    //     0xa42bdc: add             x1, x1, HEAP, lsl #32
    // 0xa42be0: cmp             w1, NULL
    // 0xa42be4: b.eq            #0xa42f90
    // 0xa42be8: r0 = of()
    //     0xa42be8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa42bec: LoadField: r1 = r0->field_87
    //     0xa42bec: ldur            w1, [x0, #0x87]
    // 0xa42bf0: DecompressPointer r1
    //     0xa42bf0: add             x1, x1, HEAP, lsl #32
    // 0xa42bf4: LoadField: r0 = r1->field_7
    //     0xa42bf4: ldur            w0, [x1, #7]
    // 0xa42bf8: DecompressPointer r0
    //     0xa42bf8: add             x0, x0, HEAP, lsl #32
    // 0xa42bfc: ldur            x1, [fp, #-8]
    // 0xa42c00: stur            x0, [fp, #-0x50]
    // 0xa42c04: LoadField: r2 = r1->field_f
    //     0xa42c04: ldur            w2, [x1, #0xf]
    // 0xa42c08: DecompressPointer r2
    //     0xa42c08: add             x2, x2, HEAP, lsl #32
    // 0xa42c0c: cmp             w2, NULL
    // 0xa42c10: b.eq            #0xa42f94
    // 0xa42c14: mov             x1, x2
    // 0xa42c18: r0 = of()
    //     0xa42c18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa42c1c: LoadField: r1 = r0->field_5b
    //     0xa42c1c: ldur            w1, [x0, #0x5b]
    // 0xa42c20: DecompressPointer r1
    //     0xa42c20: add             x1, x1, HEAP, lsl #32
    // 0xa42c24: r16 = 16.000000
    //     0xa42c24: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa42c28: ldr             x16, [x16, #0x188]
    // 0xa42c2c: stp             x16, x1, [SP]
    // 0xa42c30: ldur            x1, [fp, #-0x50]
    // 0xa42c34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa42c34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa42c38: ldr             x4, [x4, #0x9b8]
    // 0xa42c3c: r0 = copyWith()
    //     0xa42c3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa42c40: stur            x0, [fp, #-8]
    // 0xa42c44: r0 = Text()
    //     0xa42c44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa42c48: mov             x1, x0
    // 0xa42c4c: ldur            x0, [fp, #-0x10]
    // 0xa42c50: stur            x1, [fp, #-0x50]
    // 0xa42c54: StoreField: r1->field_b = r0
    //     0xa42c54: stur            w0, [x1, #0xb]
    // 0xa42c58: ldur            x0, [fp, #-8]
    // 0xa42c5c: StoreField: r1->field_13 = r0
    //     0xa42c5c: stur            w0, [x1, #0x13]
    // 0xa42c60: r0 = Padding()
    //     0xa42c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa42c64: mov             x3, x0
    // 0xa42c68: r0 = Instance_EdgeInsets
    //     0xa42c68: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xa42c6c: ldr             x0, [x0, #0xc10]
    // 0xa42c70: stur            x3, [fp, #-8]
    // 0xa42c74: StoreField: r3->field_f = r0
    //     0xa42c74: stur            w0, [x3, #0xf]
    // 0xa42c78: ldur            x0, [fp, #-0x50]
    // 0xa42c7c: StoreField: r3->field_b = r0
    //     0xa42c7c: stur            w0, [x3, #0xb]
    // 0xa42c80: ldur            x2, [fp, #-0x20]
    // 0xa42c84: r1 = Function '<anonymous closure>':.
    //     0xa42c84: add             x1, PP, #0x55, lsl #12  ; [pp+0x55880] AnonymousClosure: (0xa42f98), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xa42c88: ldr             x1, [x1, #0x880]
    // 0xa42c8c: r0 = AllocateClosure()
    //     0xa42c8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa42c90: stur            x0, [fp, #-0x10]
    // 0xa42c94: r0 = TextButton()
    //     0xa42c94: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa42c98: mov             x1, x0
    // 0xa42c9c: ldur            x0, [fp, #-0x10]
    // 0xa42ca0: stur            x1, [fp, #-0x20]
    // 0xa42ca4: StoreField: r1->field_b = r0
    //     0xa42ca4: stur            w0, [x1, #0xb]
    // 0xa42ca8: r0 = false
    //     0xa42ca8: add             x0, NULL, #0x30  ; false
    // 0xa42cac: StoreField: r1->field_27 = r0
    //     0xa42cac: stur            w0, [x1, #0x27]
    // 0xa42cb0: r2 = true
    //     0xa42cb0: add             x2, NULL, #0x20  ; true
    // 0xa42cb4: StoreField: r1->field_2f = r2
    //     0xa42cb4: stur            w2, [x1, #0x2f]
    // 0xa42cb8: ldur            x2, [fp, #-8]
    // 0xa42cbc: StoreField: r1->field_37 = r2
    //     0xa42cbc: stur            w2, [x1, #0x37]
    // 0xa42cc0: r0 = TextButtonTheme()
    //     0xa42cc0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa42cc4: mov             x1, x0
    // 0xa42cc8: ldur            x0, [fp, #-0x48]
    // 0xa42ccc: stur            x1, [fp, #-8]
    // 0xa42cd0: StoreField: r1->field_f = r0
    //     0xa42cd0: stur            w0, [x1, #0xf]
    // 0xa42cd4: ldur            x0, [fp, #-0x20]
    // 0xa42cd8: StoreField: r1->field_b = r0
    //     0xa42cd8: stur            w0, [x1, #0xb]
    // 0xa42cdc: r0 = Visibility()
    //     0xa42cdc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa42ce0: mov             x3, x0
    // 0xa42ce4: ldur            x0, [fp, #-8]
    // 0xa42ce8: stur            x3, [fp, #-0x10]
    // 0xa42cec: StoreField: r3->field_b = r0
    //     0xa42cec: stur            w0, [x3, #0xb]
    // 0xa42cf0: r0 = Instance_SizedBox
    //     0xa42cf0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa42cf4: StoreField: r3->field_f = r0
    //     0xa42cf4: stur            w0, [x3, #0xf]
    // 0xa42cf8: ldur            x1, [fp, #-0x38]
    // 0xa42cfc: StoreField: r3->field_13 = r1
    //     0xa42cfc: stur            w1, [x3, #0x13]
    // 0xa42d00: r4 = false
    //     0xa42d00: add             x4, NULL, #0x30  ; false
    // 0xa42d04: ArrayStore: r3[0] = r4  ; List_4
    //     0xa42d04: stur            w4, [x3, #0x17]
    // 0xa42d08: StoreField: r3->field_1b = r4
    //     0xa42d08: stur            w4, [x3, #0x1b]
    // 0xa42d0c: StoreField: r3->field_1f = r4
    //     0xa42d0c: stur            w4, [x3, #0x1f]
    // 0xa42d10: StoreField: r3->field_23 = r4
    //     0xa42d10: stur            w4, [x3, #0x23]
    // 0xa42d14: StoreField: r3->field_27 = r4
    //     0xa42d14: stur            w4, [x3, #0x27]
    // 0xa42d18: StoreField: r3->field_2b = r4
    //     0xa42d18: stur            w4, [x3, #0x2b]
    // 0xa42d1c: r1 = Null
    //     0xa42d1c: mov             x1, NULL
    // 0xa42d20: r2 = 4
    //     0xa42d20: movz            x2, #0x4
    // 0xa42d24: r0 = AllocateArray()
    //     0xa42d24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa42d28: mov             x2, x0
    // 0xa42d2c: ldur            x0, [fp, #-0x30]
    // 0xa42d30: stur            x2, [fp, #-8]
    // 0xa42d34: StoreField: r2->field_f = r0
    //     0xa42d34: stur            w0, [x2, #0xf]
    // 0xa42d38: ldur            x0, [fp, #-0x10]
    // 0xa42d3c: StoreField: r2->field_13 = r0
    //     0xa42d3c: stur            w0, [x2, #0x13]
    // 0xa42d40: r1 = <Widget>
    //     0xa42d40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa42d44: r0 = AllocateGrowableArray()
    //     0xa42d44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa42d48: mov             x1, x0
    // 0xa42d4c: ldur            x0, [fp, #-8]
    // 0xa42d50: stur            x1, [fp, #-0x10]
    // 0xa42d54: StoreField: r1->field_f = r0
    //     0xa42d54: stur            w0, [x1, #0xf]
    // 0xa42d58: r2 = 4
    //     0xa42d58: movz            x2, #0x4
    // 0xa42d5c: StoreField: r1->field_b = r2
    //     0xa42d5c: stur            w2, [x1, #0xb]
    // 0xa42d60: r0 = Column()
    //     0xa42d60: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa42d64: mov             x3, x0
    // 0xa42d68: r0 = Instance_Axis
    //     0xa42d68: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa42d6c: stur            x3, [fp, #-8]
    // 0xa42d70: StoreField: r3->field_f = r0
    //     0xa42d70: stur            w0, [x3, #0xf]
    // 0xa42d74: r0 = Instance_MainAxisAlignment
    //     0xa42d74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xa42d78: ldr             x0, [x0, #0xab0]
    // 0xa42d7c: StoreField: r3->field_13 = r0
    //     0xa42d7c: stur            w0, [x3, #0x13]
    // 0xa42d80: r0 = Instance_MainAxisSize
    //     0xa42d80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa42d84: ldr             x0, [x0, #0xa10]
    // 0xa42d88: ArrayStore: r3[0] = r0  ; List_4
    //     0xa42d88: stur            w0, [x3, #0x17]
    // 0xa42d8c: r0 = Instance_CrossAxisAlignment
    //     0xa42d8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa42d90: ldr             x0, [x0, #0xa18]
    // 0xa42d94: StoreField: r3->field_1b = r0
    //     0xa42d94: stur            w0, [x3, #0x1b]
    // 0xa42d98: r0 = Instance_VerticalDirection
    //     0xa42d98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa42d9c: ldr             x0, [x0, #0xa20]
    // 0xa42da0: StoreField: r3->field_23 = r0
    //     0xa42da0: stur            w0, [x3, #0x23]
    // 0xa42da4: r0 = Instance_Clip
    //     0xa42da4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa42da8: ldr             x0, [x0, #0x38]
    // 0xa42dac: StoreField: r3->field_2b = r0
    //     0xa42dac: stur            w0, [x3, #0x2b]
    // 0xa42db0: StoreField: r3->field_2f = rZR
    //     0xa42db0: stur            xzr, [x3, #0x2f]
    // 0xa42db4: ldur            x0, [fp, #-0x10]
    // 0xa42db8: StoreField: r3->field_b = r0
    //     0xa42db8: stur            w0, [x3, #0xb]
    // 0xa42dbc: r1 = Null
    //     0xa42dbc: mov             x1, NULL
    // 0xa42dc0: r2 = 2
    //     0xa42dc0: movz            x2, #0x2
    // 0xa42dc4: r0 = AllocateArray()
    //     0xa42dc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa42dc8: mov             x2, x0
    // 0xa42dcc: ldur            x0, [fp, #-8]
    // 0xa42dd0: stur            x2, [fp, #-0x10]
    // 0xa42dd4: StoreField: r2->field_f = r0
    //     0xa42dd4: stur            w0, [x2, #0xf]
    // 0xa42dd8: r1 = <Widget>
    //     0xa42dd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa42ddc: r0 = AllocateGrowableArray()
    //     0xa42ddc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa42de0: mov             x1, x0
    // 0xa42de4: ldur            x0, [fp, #-0x10]
    // 0xa42de8: stur            x1, [fp, #-8]
    // 0xa42dec: StoreField: r1->field_f = r0
    //     0xa42dec: stur            w0, [x1, #0xf]
    // 0xa42df0: r0 = 2
    //     0xa42df0: movz            x0, #0x2
    // 0xa42df4: StoreField: r1->field_b = r0
    //     0xa42df4: stur            w0, [x1, #0xb]
    // 0xa42df8: r0 = Stack()
    //     0xa42df8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa42dfc: mov             x1, x0
    // 0xa42e00: r0 = Instance_Alignment
    //     0xa42e00: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xa42e04: ldr             x0, [x0, #0x5b8]
    // 0xa42e08: stur            x1, [fp, #-0x10]
    // 0xa42e0c: StoreField: r1->field_f = r0
    //     0xa42e0c: stur            w0, [x1, #0xf]
    // 0xa42e10: r0 = Instance_StackFit
    //     0xa42e10: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa42e14: ldr             x0, [x0, #0xfa8]
    // 0xa42e18: ArrayStore: r1[0] = r0  ; List_4
    //     0xa42e18: stur            w0, [x1, #0x17]
    // 0xa42e1c: r2 = Instance_Clip
    //     0xa42e1c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa42e20: ldr             x2, [x2, #0x7e0]
    // 0xa42e24: StoreField: r1->field_1b = r2
    //     0xa42e24: stur            w2, [x1, #0x1b]
    // 0xa42e28: ldur            x3, [fp, #-8]
    // 0xa42e2c: StoreField: r1->field_b = r3
    //     0xa42e2c: stur            w3, [x1, #0xb]
    // 0xa42e30: r0 = SizedBox()
    //     0xa42e30: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa42e34: mov             x1, x0
    // 0xa42e38: r0 = 210.000000
    //     0xa42e38: add             x0, PP, #0x55, lsl #12  ; [pp+0x55888] 210
    //     0xa42e3c: ldr             x0, [x0, #0x888]
    // 0xa42e40: stur            x1, [fp, #-8]
    // 0xa42e44: StoreField: r1->field_f = r0
    //     0xa42e44: stur            w0, [x1, #0xf]
    // 0xa42e48: r0 = 110.000000
    //     0xa42e48: add             x0, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xa42e4c: ldr             x0, [x0, #0x770]
    // 0xa42e50: StoreField: r1->field_13 = r0
    //     0xa42e50: stur            w0, [x1, #0x13]
    // 0xa42e54: ldur            x0, [fp, #-0x10]
    // 0xa42e58: StoreField: r1->field_b = r0
    //     0xa42e58: stur            w0, [x1, #0xb]
    // 0xa42e5c: r0 = Padding()
    //     0xa42e5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa42e60: mov             x1, x0
    // 0xa42e64: r0 = Instance_EdgeInsets
    //     0xa42e64: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xa42e68: ldr             x0, [x0, #0x240]
    // 0xa42e6c: stur            x1, [fp, #-0x10]
    // 0xa42e70: StoreField: r1->field_f = r0
    //     0xa42e70: stur            w0, [x1, #0xf]
    // 0xa42e74: ldur            x0, [fp, #-8]
    // 0xa42e78: StoreField: r1->field_b = r0
    //     0xa42e78: stur            w0, [x1, #0xb]
    // 0xa42e7c: r0 = Visibility()
    //     0xa42e7c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa42e80: mov             x3, x0
    // 0xa42e84: ldur            x0, [fp, #-0x10]
    // 0xa42e88: stur            x3, [fp, #-8]
    // 0xa42e8c: StoreField: r3->field_b = r0
    //     0xa42e8c: stur            w0, [x3, #0xb]
    // 0xa42e90: r0 = Instance_SizedBox
    //     0xa42e90: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa42e94: StoreField: r3->field_f = r0
    //     0xa42e94: stur            w0, [x3, #0xf]
    // 0xa42e98: ldur            x0, [fp, #-0x28]
    // 0xa42e9c: StoreField: r3->field_13 = r0
    //     0xa42e9c: stur            w0, [x3, #0x13]
    // 0xa42ea0: r0 = false
    //     0xa42ea0: add             x0, NULL, #0x30  ; false
    // 0xa42ea4: ArrayStore: r3[0] = r0  ; List_4
    //     0xa42ea4: stur            w0, [x3, #0x17]
    // 0xa42ea8: StoreField: r3->field_1b = r0
    //     0xa42ea8: stur            w0, [x3, #0x1b]
    // 0xa42eac: StoreField: r3->field_1f = r0
    //     0xa42eac: stur            w0, [x3, #0x1f]
    // 0xa42eb0: StoreField: r3->field_23 = r0
    //     0xa42eb0: stur            w0, [x3, #0x23]
    // 0xa42eb4: StoreField: r3->field_27 = r0
    //     0xa42eb4: stur            w0, [x3, #0x27]
    // 0xa42eb8: StoreField: r3->field_2b = r0
    //     0xa42eb8: stur            w0, [x3, #0x2b]
    // 0xa42ebc: r1 = Null
    //     0xa42ebc: mov             x1, NULL
    // 0xa42ec0: r2 = 4
    //     0xa42ec0: movz            x2, #0x4
    // 0xa42ec4: r0 = AllocateArray()
    //     0xa42ec4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa42ec8: mov             x2, x0
    // 0xa42ecc: ldur            x0, [fp, #-0x40]
    // 0xa42ed0: stur            x2, [fp, #-0x10]
    // 0xa42ed4: StoreField: r2->field_f = r0
    //     0xa42ed4: stur            w0, [x2, #0xf]
    // 0xa42ed8: ldur            x0, [fp, #-8]
    // 0xa42edc: StoreField: r2->field_13 = r0
    //     0xa42edc: stur            w0, [x2, #0x13]
    // 0xa42ee0: r1 = <Widget>
    //     0xa42ee0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa42ee4: r0 = AllocateGrowableArray()
    //     0xa42ee4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa42ee8: mov             x1, x0
    // 0xa42eec: ldur            x0, [fp, #-0x10]
    // 0xa42ef0: stur            x1, [fp, #-8]
    // 0xa42ef4: StoreField: r1->field_f = r0
    //     0xa42ef4: stur            w0, [x1, #0xf]
    // 0xa42ef8: r0 = 4
    //     0xa42ef8: movz            x0, #0x4
    // 0xa42efc: StoreField: r1->field_b = r0
    //     0xa42efc: stur            w0, [x1, #0xb]
    // 0xa42f00: r0 = Stack()
    //     0xa42f00: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa42f04: r1 = Instance_Alignment
    //     0xa42f04: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xa42f08: ldr             x1, [x1, #0xcb0]
    // 0xa42f0c: StoreField: r0->field_f = r1
    //     0xa42f0c: stur            w1, [x0, #0xf]
    // 0xa42f10: r1 = Instance_StackFit
    //     0xa42f10: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa42f14: ldr             x1, [x1, #0xfa8]
    // 0xa42f18: ArrayStore: r0[0] = r1  ; List_4
    //     0xa42f18: stur            w1, [x0, #0x17]
    // 0xa42f1c: r1 = Instance_Clip
    //     0xa42f1c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa42f20: ldr             x1, [x1, #0x7e0]
    // 0xa42f24: StoreField: r0->field_1b = r1
    //     0xa42f24: stur            w1, [x0, #0x1b]
    // 0xa42f28: ldur            x1, [fp, #-8]
    // 0xa42f2c: StoreField: r0->field_b = r1
    //     0xa42f2c: stur            w1, [x0, #0xb]
    // 0xa42f30: LeaveFrame
    //     0xa42f30: mov             SP, fp
    //     0xa42f34: ldp             fp, lr, [SP], #0x10
    // 0xa42f38: ret
    //     0xa42f38: ret             
    // 0xa42f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42f3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42f40: b               #0xa4234c
    // 0xa42f44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa42f48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa42f4c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa42f4c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa42f50: SaveReg d0
    //     0xa42f50: str             q0, [SP, #-0x10]!
    // 0xa42f54: SaveReg r1
    //     0xa42f54: str             x1, [SP, #-8]!
    // 0xa42f58: r0 = AllocateDouble()
    //     0xa42f58: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa42f5c: RestoreReg r1
    //     0xa42f5c: ldr             x1, [SP], #8
    // 0xa42f60: RestoreReg d0
    //     0xa42f60: ldr             q0, [SP], #0x10
    // 0xa42f64: b               #0xa42564
    // 0xa42f68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa42f6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa42f70: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa42f70: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa42f74: SaveReg d0
    //     0xa42f74: str             q0, [SP, #-0x10]!
    // 0xa42f78: SaveReg r1
    //     0xa42f78: str             x1, [SP, #-8]!
    // 0xa42f7c: r0 = AllocateDouble()
    //     0xa42f7c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa42f80: RestoreReg r1
    //     0xa42f80: ldr             x1, [SP], #8
    // 0xa42f84: RestoreReg d0
    //     0xa42f84: ldr             q0, [SP], #0x10
    // 0xa42f88: b               #0xa428c0
    // 0xa42f8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa42f90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa42f94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42f94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa42f98, size: 0xe0
    // 0xa42f98: EnterFrame
    //     0xa42f98: stp             fp, lr, [SP, #-0x10]!
    //     0xa42f9c: mov             fp, SP
    // 0xa42fa0: AllocStack(0x20)
    //     0xa42fa0: sub             SP, SP, #0x20
    // 0xa42fa4: SetupParameters()
    //     0xa42fa4: ldr             x0, [fp, #0x10]
    //     0xa42fa8: ldur            w1, [x0, #0x17]
    //     0xa42fac: add             x1, x1, HEAP, lsl #32
    // 0xa42fb0: CheckStackOverflow
    //     0xa42fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42fb4: cmp             SP, x16
    //     0xa42fb8: b.ls            #0xa43068
    // 0xa42fbc: LoadField: r0 = r1->field_f
    //     0xa42fbc: ldur            w0, [x1, #0xf]
    // 0xa42fc0: DecompressPointer r0
    //     0xa42fc0: add             x0, x0, HEAP, lsl #32
    // 0xa42fc4: LoadField: r2 = r0->field_b
    //     0xa42fc4: ldur            w2, [x0, #0xb]
    // 0xa42fc8: DecompressPointer r2
    //     0xa42fc8: add             x2, x2, HEAP, lsl #32
    // 0xa42fcc: cmp             w2, NULL
    // 0xa42fd0: b.eq            #0xa43070
    // 0xa42fd4: LoadField: r3 = r2->field_b
    //     0xa42fd4: ldur            w3, [x2, #0xb]
    // 0xa42fd8: DecompressPointer r3
    //     0xa42fd8: add             x3, x3, HEAP, lsl #32
    // 0xa42fdc: LoadField: r0 = r1->field_13
    //     0xa42fdc: ldur            w0, [x1, #0x13]
    // 0xa42fe0: DecompressPointer r0
    //     0xa42fe0: add             x0, x0, HEAP, lsl #32
    // 0xa42fe4: LoadField: r1 = r3->field_b
    //     0xa42fe4: ldur            w1, [x3, #0xb]
    // 0xa42fe8: r4 = LoadInt32Instr(r0)
    //     0xa42fe8: sbfx            x4, x0, #1, #0x1f
    //     0xa42fec: tbz             w0, #0, #0xa42ff4
    //     0xa42ff0: ldur            x4, [x0, #7]
    // 0xa42ff4: r0 = LoadInt32Instr(r1)
    //     0xa42ff4: sbfx            x0, x1, #1, #0x1f
    // 0xa42ff8: mov             x1, x4
    // 0xa42ffc: cmp             x1, x0
    // 0xa43000: b.hs            #0xa43074
    // 0xa43004: LoadField: r0 = r3->field_f
    //     0xa43004: ldur            w0, [x3, #0xf]
    // 0xa43008: DecompressPointer r0
    //     0xa43008: add             x0, x0, HEAP, lsl #32
    // 0xa4300c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa4300c: add             x16, x0, x4, lsl #2
    //     0xa43010: ldur            w1, [x16, #0xf]
    // 0xa43014: DecompressPointer r1
    //     0xa43014: add             x1, x1, HEAP, lsl #32
    // 0xa43018: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa43018: ldur            w0, [x1, #0x17]
    // 0xa4301c: DecompressPointer r0
    //     0xa4301c: add             x0, x0, HEAP, lsl #32
    // 0xa43020: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa43020: ldur            w1, [x2, #0x17]
    // 0xa43024: DecompressPointer r1
    //     0xa43024: add             x1, x1, HEAP, lsl #32
    // 0xa43028: LoadField: r3 = r2->field_23
    //     0xa43028: ldur            w3, [x2, #0x23]
    // 0xa4302c: DecompressPointer r3
    //     0xa4302c: add             x3, x3, HEAP, lsl #32
    // 0xa43030: stp             x0, x3, [SP, #0x10]
    // 0xa43034: r16 = "product_page"
    //     0xa43034: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa43038: ldr             x16, [x16, #0x480]
    // 0xa4303c: stp             x1, x16, [SP]
    // 0xa43040: r4 = 0
    //     0xa43040: movz            x4, #0
    // 0xa43044: ldr             x0, [SP, #0x18]
    // 0xa43048: r16 = UnlinkedCall_0x613b5c
    //     0xa43048: add             x16, PP, #0x55, lsl #12  ; [pp+0x55890] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4304c: add             x16, x16, #0x890
    // 0xa43050: ldp             x5, lr, [x16]
    // 0xa43054: blr             lr
    // 0xa43058: r0 = Null
    //     0xa43058: mov             x0, NULL
    // 0xa4305c: LeaveFrame
    //     0xa4305c: mov             SP, fp
    //     0xa43060: ldp             fp, lr, [SP], #0x10
    // 0xa43064: ret
    //     0xa43064: ret             
    // 0xa43068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43068: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4306c: b               #0xa42fbc
    // 0xa43070: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa43070: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa43074: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa43074: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb80588, size: 0x16c
    // 0xb80588: EnterFrame
    //     0xb80588: stp             fp, lr, [SP, #-0x10]!
    //     0xb8058c: mov             fp, SP
    // 0xb80590: AllocStack(0x40)
    //     0xb80590: sub             SP, SP, #0x40
    // 0xb80594: SetupParameters(_ProductBannerCrossLinkState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb80594: mov             x0, x1
    //     0xb80598: stur            x1, [fp, #-8]
    //     0xb8059c: mov             x1, x2
    //     0xb805a0: stur            x2, [fp, #-0x10]
    // 0xb805a4: CheckStackOverflow
    //     0xb805a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb805a8: cmp             SP, x16
    //     0xb805ac: b.ls            #0xb806e8
    // 0xb805b0: r1 = 1
    //     0xb805b0: movz            x1, #0x1
    // 0xb805b4: r0 = AllocateContext()
    //     0xb805b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb805b8: mov             x2, x0
    // 0xb805bc: ldur            x0, [fp, #-8]
    // 0xb805c0: stur            x2, [fp, #-0x38]
    // 0xb805c4: StoreField: r2->field_f = r0
    //     0xb805c4: stur            w0, [x2, #0xf]
    // 0xb805c8: LoadField: r1 = r0->field_b
    //     0xb805c8: ldur            w1, [x0, #0xb]
    // 0xb805cc: DecompressPointer r1
    //     0xb805cc: add             x1, x1, HEAP, lsl #32
    // 0xb805d0: cmp             w1, NULL
    // 0xb805d4: b.eq            #0xb806f0
    // 0xb805d8: LoadField: r0 = r1->field_b
    //     0xb805d8: ldur            w0, [x1, #0xb]
    // 0xb805dc: DecompressPointer r0
    //     0xb805dc: add             x0, x0, HEAP, lsl #32
    // 0xb805e0: stur            x0, [fp, #-0x30]
    // 0xb805e4: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb805e4: ldur            w3, [x1, #0x17]
    // 0xb805e8: DecompressPointer r3
    //     0xb805e8: add             x3, x3, HEAP, lsl #32
    // 0xb805ec: stur            x3, [fp, #-0x28]
    // 0xb805f0: LoadField: r4 = r1->field_13
    //     0xb805f0: ldur            w4, [x1, #0x13]
    // 0xb805f4: DecompressPointer r4
    //     0xb805f4: add             x4, x4, HEAP, lsl #32
    // 0xb805f8: stur            x4, [fp, #-0x20]
    // 0xb805fc: LoadField: r5 = r1->field_f
    //     0xb805fc: ldur            w5, [x1, #0xf]
    // 0xb80600: DecompressPointer r5
    //     0xb80600: add             x5, x5, HEAP, lsl #32
    // 0xb80604: stur            x5, [fp, #-0x18]
    // 0xb80608: LoadField: r6 = r1->field_27
    //     0xb80608: ldur            w6, [x1, #0x27]
    // 0xb8060c: DecompressPointer r6
    //     0xb8060c: add             x6, x6, HEAP, lsl #32
    // 0xb80610: ldur            x1, [fp, #-0x10]
    // 0xb80614: stur            x6, [fp, #-8]
    // 0xb80618: r0 = of()
    //     0xb80618: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8061c: LoadField: r1 = r0->field_5b
    //     0xb8061c: ldur            w1, [x0, #0x5b]
    // 0xb80620: DecompressPointer r1
    //     0xb80620: add             x1, x1, HEAP, lsl #32
    // 0xb80624: stur            x1, [fp, #-0x10]
    // 0xb80628: r0 = ProductBannerCrossWidget()
    //     0xb80628: bl              #0xa79dc0  ; AllocateProductBannerCrossWidgetStub -> ProductBannerCrossWidget (size=0x54)
    // 0xb8062c: d0 = 200.000000
    //     0xb8062c: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xb80630: ldr             d0, [x17, #0x360]
    // 0xb80634: stur            x0, [fp, #-0x40]
    // 0xb80638: StoreField: r0->field_b = d0
    //     0xb80638: stur            d0, [x0, #0xb]
    // 0xb8063c: r1 = true
    //     0xb8063c: add             x1, NULL, #0x20  ; true
    // 0xb80640: StoreField: r0->field_13 = r1
    //     0xb80640: stur            w1, [x0, #0x13]
    // 0xb80644: r1 = Instance_Duration
    //     0xb80644: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0xb80648: ldr             x1, [x1, #0xbd8]
    // 0xb8064c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8064c: stur            w1, [x0, #0x17]
    // 0xb80650: ldur            x1, [fp, #-0x10]
    // 0xb80654: StoreField: r0->field_1b = r1
    //     0xb80654: stur            w1, [x0, #0x1b]
    // 0xb80658: r1 = Instance_Color
    //     0xb80658: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb8065c: ldr             x1, [x1, #0x90]
    // 0xb80660: StoreField: r0->field_1f = r1
    //     0xb80660: stur            w1, [x0, #0x1f]
    // 0xb80664: d0 = 48.000000
    //     0xb80664: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0xb80668: StoreField: r0->field_27 = d0
    //     0xb80668: stur            d0, [x0, #0x27]
    // 0xb8066c: ldur            x1, [fp, #-0x30]
    // 0xb80670: StoreField: r0->field_2f = r1
    //     0xb80670: stur            w1, [x0, #0x2f]
    // 0xb80674: ldur            x2, [fp, #-0x38]
    // 0xb80678: r1 = Function '<anonymous closure>':.
    //     0xb80678: add             x1, PP, #0x55, lsl #12  ; [pp+0x55840] AnonymousClosure: (0xb80774), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::build (0xb80588)
    //     0xb8067c: ldr             x1, [x1, #0x840]
    // 0xb80680: r0 = AllocateClosure()
    //     0xb80680: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb80684: mov             x1, x0
    // 0xb80688: ldur            x0, [fp, #-0x40]
    // 0xb8068c: StoreField: r0->field_33 = r1
    //     0xb8068c: stur            w1, [x0, #0x33]
    // 0xb80690: r1 = "product_page"
    //     0xb80690: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb80694: ldr             x1, [x1, #0x480]
    // 0xb80698: StoreField: r0->field_37 = r1
    //     0xb80698: stur            w1, [x0, #0x37]
    // 0xb8069c: ldur            x2, [fp, #-0x28]
    // 0xb806a0: StoreField: r0->field_3b = r2
    //     0xb806a0: stur            w2, [x0, #0x3b]
    // 0xb806a4: StoreField: r0->field_3f = r1
    //     0xb806a4: stur            w1, [x0, #0x3f]
    // 0xb806a8: ldur            x1, [fp, #-0x20]
    // 0xb806ac: StoreField: r0->field_43 = r1
    //     0xb806ac: stur            w1, [x0, #0x43]
    // 0xb806b0: ldur            x1, [fp, #-0x18]
    // 0xb806b4: StoreField: r0->field_47 = r1
    //     0xb806b4: stur            w1, [x0, #0x47]
    // 0xb806b8: ldur            x1, [fp, #-8]
    // 0xb806bc: StoreField: r0->field_4b = r1
    //     0xb806bc: stur            w1, [x0, #0x4b]
    // 0xb806c0: ldur            x2, [fp, #-0x38]
    // 0xb806c4: r1 = Function '<anonymous closure>':.
    //     0xb806c4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55848] AnonymousClosure: (0xb806f4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::build (0xb80588)
    //     0xb806c8: ldr             x1, [x1, #0x848]
    // 0xb806cc: r0 = AllocateClosure()
    //     0xb806cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb806d0: mov             x1, x0
    // 0xb806d4: ldur            x0, [fp, #-0x40]
    // 0xb806d8: StoreField: r0->field_4f = r1
    //     0xb806d8: stur            w1, [x0, #0x4f]
    // 0xb806dc: LeaveFrame
    //     0xb806dc: mov             SP, fp
    //     0xb806e0: ldp             fp, lr, [SP], #0x10
    // 0xb806e4: ret
    //     0xb806e4: ret             
    // 0xb806e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb806e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb806ec: b               #0xb805b0
    // 0xb806f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb806f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, dynamic, int) {
    // ** addr: 0xb806f4, size: 0x80
    // 0xb806f4: EnterFrame
    //     0xb806f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb806f8: mov             fp, SP
    // 0xb806fc: AllocStack(0x8)
    //     0xb806fc: sub             SP, SP, #8
    // 0xb80700: SetupParameters()
    //     0xb80700: ldr             x0, [fp, #0x20]
    //     0xb80704: ldur            w1, [x0, #0x17]
    //     0xb80708: add             x1, x1, HEAP, lsl #32
    // 0xb8070c: CheckStackOverflow
    //     0xb8070c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb80710: cmp             SP, x16
    //     0xb80714: b.ls            #0xb8076c
    // 0xb80718: LoadField: r3 = r1->field_f
    //     0xb80718: ldur            w3, [x1, #0xf]
    // 0xb8071c: DecompressPointer r3
    //     0xb8071c: add             x3, x3, HEAP, lsl #32
    // 0xb80720: ldr             x0, [fp, #0x18]
    // 0xb80724: stur            x3, [fp, #-8]
    // 0xb80728: r2 = Null
    //     0xb80728: mov             x2, NULL
    // 0xb8072c: r1 = Null
    //     0xb8072c: mov             x1, NULL
    // 0xb80730: r8 = List<WidgetEntity>
    //     0xb80730: add             x8, PP, #0x52, lsl #12  ; [pp+0x52bf0] Type: List<WidgetEntity>
    //     0xb80734: ldr             x8, [x8, #0xbf0]
    // 0xb80738: r3 = Null
    //     0xb80738: add             x3, PP, #0x55, lsl #12  ; [pp+0x55850] Null
    //     0xb8073c: ldr             x3, [x3, #0x850]
    // 0xb80740: r0 = List<WidgetEntity>()
    //     0xb80740: bl              #0xa7aae4  ; IsType_List<WidgetEntity>_Stub
    // 0xb80744: ldr             x0, [fp, #0x10]
    // 0xb80748: r3 = LoadInt32Instr(r0)
    //     0xb80748: sbfx            x3, x0, #1, #0x1f
    //     0xb8074c: tbz             w0, #0, #0xb80754
    //     0xb80750: ldur            x3, [x0, #7]
    // 0xb80754: ldur            x1, [fp, #-8]
    // 0xb80758: ldr             x2, [fp, #0x18]
    // 0xb8075c: r0 = bannerSlider()
    //     0xb8075c: bl              #0xa42328  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider
    // 0xb80760: LeaveFrame
    //     0xb80760: mov             SP, fp
    //     0xb80764: ldp             fp, lr, [SP], #0x10
    // 0xb80768: ret
    //     0xb80768: ret             
    // 0xb8076c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8076c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb80770: b               #0xb80718
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String) {
    // ** addr: 0xb80774, size: 0xa4
    // 0xb80774: EnterFrame
    //     0xb80774: stp             fp, lr, [SP, #-0x10]!
    //     0xb80778: mov             fp, SP
    // 0xb8077c: AllocStack(0x40)
    //     0xb8077c: sub             SP, SP, #0x40
    // 0xb80780: SetupParameters()
    //     0xb80780: ldr             x0, [fp, #0x48]
    //     0xb80784: ldur            w1, [x0, #0x17]
    //     0xb80788: add             x1, x1, HEAP, lsl #32
    // 0xb8078c: CheckStackOverflow
    //     0xb8078c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb80790: cmp             SP, x16
    //     0xb80794: b.ls            #0xb8080c
    // 0xb80798: LoadField: r0 = r1->field_f
    //     0xb80798: ldur            w0, [x1, #0xf]
    // 0xb8079c: DecompressPointer r0
    //     0xb8079c: add             x0, x0, HEAP, lsl #32
    // 0xb807a0: LoadField: r1 = r0->field_b
    //     0xb807a0: ldur            w1, [x0, #0xb]
    // 0xb807a4: DecompressPointer r1
    //     0xb807a4: add             x1, x1, HEAP, lsl #32
    // 0xb807a8: cmp             w1, NULL
    // 0xb807ac: b.eq            #0xb80814
    // 0xb807b0: LoadField: r0 = r1->field_1f
    //     0xb807b0: ldur            w0, [x1, #0x1f]
    // 0xb807b4: DecompressPointer r0
    //     0xb807b4: add             x0, x0, HEAP, lsl #32
    // 0xb807b8: ldr             x16, [fp, #0x40]
    // 0xb807bc: stp             x16, x0, [SP, #0x30]
    // 0xb807c0: ldr             x16, [fp, #0x38]
    // 0xb807c4: ldr             lr, [fp, #0x30]
    // 0xb807c8: stp             lr, x16, [SP, #0x20]
    // 0xb807cc: ldr             x16, [fp, #0x28]
    // 0xb807d0: ldr             lr, [fp, #0x20]
    // 0xb807d4: stp             lr, x16, [SP, #0x10]
    // 0xb807d8: ldr             x16, [fp, #0x18]
    // 0xb807dc: ldr             lr, [fp, #0x10]
    // 0xb807e0: stp             lr, x16, [SP]
    // 0xb807e4: r4 = 0
    //     0xb807e4: movz            x4, #0
    // 0xb807e8: ldr             x0, [SP, #0x38]
    // 0xb807ec: r16 = UnlinkedCall_0x613b5c
    //     0xb807ec: add             x16, PP, #0x55, lsl #12  ; [pp+0x558a0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb807f0: add             x16, x16, #0x8a0
    // 0xb807f4: ldp             x5, lr, [x16]
    // 0xb807f8: blr             lr
    // 0xb807fc: r0 = Null
    //     0xb807fc: mov             x0, NULL
    // 0xb80800: LeaveFrame
    //     0xb80800: mov             SP, fp
    //     0xb80804: ldp             fp, lr, [SP], #0x10
    // 0xb80808: ret
    //     0xb80808: ret             
    // 0xb8080c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8080c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb80810: b               #0xb80798
    // 0xb80814: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80814: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4060, size: 0x38, field offset: 0xc
//   const constructor, 
class ProductBannerCrossLink extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f784, size: 0x24
    // 0xc7f784: EnterFrame
    //     0xc7f784: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f788: mov             fp, SP
    // 0xc7f78c: mov             x0, x1
    // 0xc7f790: r1 = <ProductBannerCrossLink>
    //     0xc7f790: add             x1, PP, #0x48, lsl #12  ; [pp+0x487a0] TypeArguments: <ProductBannerCrossLink>
    //     0xc7f794: ldr             x1, [x1, #0x7a0]
    // 0xc7f798: r0 = _ProductBannerCrossLinkState()
    //     0xc7f798: bl              #0xc7f7a8  ; Allocate_ProductBannerCrossLinkStateStub -> _ProductBannerCrossLinkState (size=0x14)
    // 0xc7f79c: LeaveFrame
    //     0xc7f79c: mov             SP, fp
    //     0xc7f7a0: ldp             fp, lr, [SP], #0x10
    // 0xc7f7a4: ret
    //     0xc7f7a4: ret             
  }
}
