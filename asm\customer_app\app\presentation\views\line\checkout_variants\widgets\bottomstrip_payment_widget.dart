// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/bottomstrip_payment_widget.dart

// class id: 1049483, size: 0x8
class :: {
}

// class id: 3282, size: 0x14, field offset: 0x14
class BottomStripPaymentStripState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x903138, size: 0x74
    // 0x903138: EnterFrame
    //     0x903138: stp             fp, lr, [SP, #-0x10]!
    //     0x90313c: mov             fp, SP
    // 0x903140: AllocStack(0x10)
    //     0x903140: sub             SP, SP, #0x10
    // 0x903144: SetupParameters()
    //     0x903144: ldr             x0, [fp, #0x18]
    //     0x903148: ldur            w1, [x0, #0x17]
    //     0x90314c: add             x1, x1, HEAP, lsl #32
    // 0x903150: CheckStackOverflow
    //     0x903150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x903154: cmp             SP, x16
    //     0x903158: b.ls            #0x9031a0
    // 0x90315c: LoadField: r0 = r1->field_f
    //     0x90315c: ldur            w0, [x1, #0xf]
    // 0x903160: DecompressPointer r0
    //     0x903160: add             x0, x0, HEAP, lsl #32
    // 0x903164: LoadField: r1 = r0->field_b
    //     0x903164: ldur            w1, [x0, #0xb]
    // 0x903168: DecompressPointer r1
    //     0x903168: add             x1, x1, HEAP, lsl #32
    // 0x90316c: cmp             w1, NULL
    // 0x903170: b.eq            #0x9031a8
    // 0x903174: LoadField: r0 = r1->field_b
    //     0x903174: ldur            w0, [x1, #0xb]
    // 0x903178: DecompressPointer r0
    //     0x903178: add             x0, x0, HEAP, lsl #32
    // 0x90317c: r16 = true
    //     0x90317c: add             x16, NULL, #0x20  ; true
    // 0x903180: stp             x16, x0, [SP]
    // 0x903184: ClosureCall
    //     0x903184: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x903188: ldur            x2, [x0, #0x1f]
    //     0x90318c: blr             x2
    // 0x903190: r0 = Null
    //     0x903190: mov             x0, NULL
    // 0x903194: LeaveFrame
    //     0x903194: mov             SP, fp
    //     0x903198: ldp             fp, lr, [SP], #0x10
    // 0x90319c: ret
    //     0x90319c: ret             
    // 0x9031a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9031a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9031a4: b               #0x90315c
    // 0x9031a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9031a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x945ba8, size: 0x144
    // 0x945ba8: EnterFrame
    //     0x945ba8: stp             fp, lr, [SP, #-0x10]!
    //     0x945bac: mov             fp, SP
    // 0x945bb0: AllocStack(0x18)
    //     0x945bb0: sub             SP, SP, #0x18
    // 0x945bb4: SetupParameters(BottomStripPaymentStripState this /* r1 => r1, fp-0x8 */)
    //     0x945bb4: stur            x1, [fp, #-8]
    // 0x945bb8: CheckStackOverflow
    //     0x945bb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945bbc: cmp             SP, x16
    //     0x945bc0: b.ls            #0x945cdc
    // 0x945bc4: r1 = 1
    //     0x945bc4: movz            x1, #0x1
    // 0x945bc8: r0 = AllocateContext()
    //     0x945bc8: bl              #0x16f6108  ; AllocateContextStub
    // 0x945bcc: mov             x1, x0
    // 0x945bd0: ldur            x0, [fp, #-8]
    // 0x945bd4: StoreField: r1->field_f = r0
    //     0x945bd4: stur            w0, [x1, #0xf]
    // 0x945bd8: LoadField: r2 = r0->field_b
    //     0x945bd8: ldur            w2, [x0, #0xb]
    // 0x945bdc: DecompressPointer r2
    //     0x945bdc: add             x2, x2, HEAP, lsl #32
    // 0x945be0: cmp             w2, NULL
    // 0x945be4: b.eq            #0x945ce4
    // 0x945be8: r0 = LoadStaticField(0x878)
    //     0x945be8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x945bec: ldr             x0, [x0, #0x10f0]
    // 0x945bf0: cmp             w0, NULL
    // 0x945bf4: b.eq            #0x945ce8
    // 0x945bf8: LoadField: r3 = r0->field_53
    //     0x945bf8: ldur            w3, [x0, #0x53]
    // 0x945bfc: DecompressPointer r3
    //     0x945bfc: add             x3, x3, HEAP, lsl #32
    // 0x945c00: stur            x3, [fp, #-0x10]
    // 0x945c04: LoadField: r0 = r3->field_7
    //     0x945c04: ldur            w0, [x3, #7]
    // 0x945c08: DecompressPointer r0
    //     0x945c08: add             x0, x0, HEAP, lsl #32
    // 0x945c0c: mov             x2, x1
    // 0x945c10: stur            x0, [fp, #-8]
    // 0x945c14: r1 = Function '<anonymous closure>':.
    //     0x945c14: add             x1, PP, #0x53, lsl #12  ; [pp+0x53e38] AnonymousClosure: (0x903138), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bottomstrip_payment_widget.dart] BottomStripPaymentStripState::initState (0x945ba8)
    //     0x945c18: ldr             x1, [x1, #0xe38]
    // 0x945c1c: r0 = AllocateClosure()
    //     0x945c1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x945c20: ldur            x2, [fp, #-8]
    // 0x945c24: mov             x3, x0
    // 0x945c28: r1 = Null
    //     0x945c28: mov             x1, NULL
    // 0x945c2c: stur            x3, [fp, #-8]
    // 0x945c30: cmp             w2, NULL
    // 0x945c34: b.eq            #0x945c54
    // 0x945c38: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x945c38: ldur            w4, [x2, #0x17]
    // 0x945c3c: DecompressPointer r4
    //     0x945c3c: add             x4, x4, HEAP, lsl #32
    // 0x945c40: r8 = X0
    //     0x945c40: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x945c44: LoadField: r9 = r4->field_7
    //     0x945c44: ldur            x9, [x4, #7]
    // 0x945c48: r3 = Null
    //     0x945c48: add             x3, PP, #0x53, lsl #12  ; [pp+0x53e40] Null
    //     0x945c4c: ldr             x3, [x3, #0xe40]
    // 0x945c50: blr             x9
    // 0x945c54: ldur            x0, [fp, #-0x10]
    // 0x945c58: LoadField: r1 = r0->field_b
    //     0x945c58: ldur            w1, [x0, #0xb]
    // 0x945c5c: LoadField: r2 = r0->field_f
    //     0x945c5c: ldur            w2, [x0, #0xf]
    // 0x945c60: DecompressPointer r2
    //     0x945c60: add             x2, x2, HEAP, lsl #32
    // 0x945c64: LoadField: r3 = r2->field_b
    //     0x945c64: ldur            w3, [x2, #0xb]
    // 0x945c68: r2 = LoadInt32Instr(r1)
    //     0x945c68: sbfx            x2, x1, #1, #0x1f
    // 0x945c6c: stur            x2, [fp, #-0x18]
    // 0x945c70: r1 = LoadInt32Instr(r3)
    //     0x945c70: sbfx            x1, x3, #1, #0x1f
    // 0x945c74: cmp             x2, x1
    // 0x945c78: b.ne            #0x945c84
    // 0x945c7c: mov             x1, x0
    // 0x945c80: r0 = _growToNextCapacity()
    //     0x945c80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945c84: ldur            x2, [fp, #-0x10]
    // 0x945c88: ldur            x3, [fp, #-0x18]
    // 0x945c8c: add             x4, x3, #1
    // 0x945c90: lsl             x5, x4, #1
    // 0x945c94: StoreField: r2->field_b = r5
    //     0x945c94: stur            w5, [x2, #0xb]
    // 0x945c98: LoadField: r1 = r2->field_f
    //     0x945c98: ldur            w1, [x2, #0xf]
    // 0x945c9c: DecompressPointer r1
    //     0x945c9c: add             x1, x1, HEAP, lsl #32
    // 0x945ca0: ldur            x0, [fp, #-8]
    // 0x945ca4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945ca4: add             x25, x1, x3, lsl #2
    //     0x945ca8: add             x25, x25, #0xf
    //     0x945cac: str             w0, [x25]
    //     0x945cb0: tbz             w0, #0, #0x945ccc
    //     0x945cb4: ldurb           w16, [x1, #-1]
    //     0x945cb8: ldurb           w17, [x0, #-1]
    //     0x945cbc: and             x16, x17, x16, lsr #2
    //     0x945cc0: tst             x16, HEAP, lsr #32
    //     0x945cc4: b.eq            #0x945ccc
    //     0x945cc8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945ccc: r0 = Null
    //     0x945ccc: mov             x0, NULL
    // 0x945cd0: LeaveFrame
    //     0x945cd0: mov             SP, fp
    //     0x945cd4: ldp             fp, lr, [SP], #0x10
    // 0x945cd8: ret
    //     0x945cd8: ret             
    // 0x945cdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945cdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945ce0: b               #0x945bc4
    // 0x945ce4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945ce4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945ce8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945ce8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbadbb0, size: 0x220
    // 0xbadbb0: EnterFrame
    //     0xbadbb0: stp             fp, lr, [SP, #-0x10]!
    //     0xbadbb4: mov             fp, SP
    // 0xbadbb8: AllocStack(0x48)
    //     0xbadbb8: sub             SP, SP, #0x48
    // 0xbadbbc: CheckStackOverflow
    //     0xbadbbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbadbc0: cmp             SP, x16
    //     0xbadbc4: b.ls            #0xbaddc8
    // 0xbadbc8: r0 = SvgPicture()
    //     0xbadbc8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadbcc: stur            x0, [fp, #-8]
    // 0xbadbd0: r16 = Instance_BoxFit
    //     0xbadbd0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadbd4: ldr             x16, [x16, #0xb18]
    // 0xbadbd8: str             x16, [SP]
    // 0xbadbdc: mov             x1, x0
    // 0xbadbe0: r2 = "assets/images/icons/phonepe.svg"
    //     0xbadbe0: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e00] "assets/images/icons/phonepe.svg"
    //     0xbadbe4: ldr             x2, [x2, #0xe00]
    // 0xbadbe8: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadbe8: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadbec: ldr             x4, [x4, #0xb0]
    // 0xbadbf0: r0 = SvgPicture.asset()
    //     0xbadbf0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadbf4: r0 = SvgPicture()
    //     0xbadbf4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadbf8: stur            x0, [fp, #-0x10]
    // 0xbadbfc: r16 = Instance_BoxFit
    //     0xbadbfc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadc00: ldr             x16, [x16, #0xb18]
    // 0xbadc04: str             x16, [SP]
    // 0xbadc08: mov             x1, x0
    // 0xbadc0c: r2 = "assets/images/icons/google-pay.svg"
    //     0xbadc0c: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e08] "assets/images/icons/google-pay.svg"
    //     0xbadc10: ldr             x2, [x2, #0xe08]
    // 0xbadc14: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadc14: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadc18: ldr             x4, [x4, #0xb0]
    // 0xbadc1c: r0 = SvgPicture.asset()
    //     0xbadc1c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadc20: r0 = SvgPicture()
    //     0xbadc20: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadc24: stur            x0, [fp, #-0x18]
    // 0xbadc28: r16 = Instance_BoxFit
    //     0xbadc28: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadc2c: ldr             x16, [x16, #0xb18]
    // 0xbadc30: str             x16, [SP]
    // 0xbadc34: mov             x1, x0
    // 0xbadc38: r2 = "assets/images/icons/paytm.svg"
    //     0xbadc38: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e10] "assets/images/icons/paytm.svg"
    //     0xbadc3c: ldr             x2, [x2, #0xe10]
    // 0xbadc40: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadc40: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadc44: ldr             x4, [x4, #0xb0]
    // 0xbadc48: r0 = SvgPicture.asset()
    //     0xbadc48: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadc4c: r0 = SvgPicture()
    //     0xbadc4c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadc50: stur            x0, [fp, #-0x20]
    // 0xbadc54: r16 = Instance_BoxFit
    //     0xbadc54: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadc58: ldr             x16, [x16, #0xb18]
    // 0xbadc5c: str             x16, [SP]
    // 0xbadc60: mov             x1, x0
    // 0xbadc64: r2 = "assets/images/icons/upi.svg"
    //     0xbadc64: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e18] "assets/images/icons/upi.svg"
    //     0xbadc68: ldr             x2, [x2, #0xe18]
    // 0xbadc6c: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadc6c: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadc70: ldr             x4, [x4, #0xb0]
    // 0xbadc74: r0 = SvgPicture.asset()
    //     0xbadc74: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadc78: r0 = SvgPicture()
    //     0xbadc78: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadc7c: stur            x0, [fp, #-0x28]
    // 0xbadc80: r16 = Instance_BoxFit
    //     0xbadc80: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadc84: ldr             x16, [x16, #0xb18]
    // 0xbadc88: str             x16, [SP]
    // 0xbadc8c: mov             x1, x0
    // 0xbadc90: r2 = "assets/images/icons/rupay.svg"
    //     0xbadc90: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e20] "assets/images/icons/rupay.svg"
    //     0xbadc94: ldr             x2, [x2, #0xe20]
    // 0xbadc98: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadc98: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadc9c: ldr             x4, [x4, #0xb0]
    // 0xbadca0: r0 = SvgPicture.asset()
    //     0xbadca0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadca4: r0 = SvgPicture()
    //     0xbadca4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadca8: stur            x0, [fp, #-0x30]
    // 0xbadcac: r16 = Instance_BoxFit
    //     0xbadcac: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadcb0: ldr             x16, [x16, #0xb18]
    // 0xbadcb4: str             x16, [SP]
    // 0xbadcb8: mov             x1, x0
    // 0xbadcbc: r2 = "assets/images/icons/mastercard.svg"
    //     0xbadcbc: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e28] "assets/images/icons/mastercard.svg"
    //     0xbadcc0: ldr             x2, [x2, #0xe28]
    // 0xbadcc4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadcc4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadcc8: ldr             x4, [x4, #0xb0]
    // 0xbadccc: r0 = SvgPicture.asset()
    //     0xbadccc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadcd0: r0 = SvgPicture()
    //     0xbadcd0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbadcd4: stur            x0, [fp, #-0x38]
    // 0xbadcd8: r16 = Instance_BoxFit
    //     0xbadcd8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbadcdc: ldr             x16, [x16, #0xb18]
    // 0xbadce0: str             x16, [SP]
    // 0xbadce4: mov             x1, x0
    // 0xbadce8: r2 = "assets/images/icons/visa.svg"
    //     0xbadce8: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e30] "assets/images/icons/visa.svg"
    //     0xbadcec: ldr             x2, [x2, #0xe30]
    // 0xbadcf0: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbadcf0: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbadcf4: ldr             x4, [x4, #0xb0]
    // 0xbadcf8: r0 = SvgPicture.asset()
    //     0xbadcf8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbadcfc: r1 = Null
    //     0xbadcfc: mov             x1, NULL
    // 0xbadd00: r2 = 14
    //     0xbadd00: movz            x2, #0xe
    // 0xbadd04: r0 = AllocateArray()
    //     0xbadd04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbadd08: mov             x2, x0
    // 0xbadd0c: ldur            x0, [fp, #-8]
    // 0xbadd10: stur            x2, [fp, #-0x40]
    // 0xbadd14: StoreField: r2->field_f = r0
    //     0xbadd14: stur            w0, [x2, #0xf]
    // 0xbadd18: ldur            x0, [fp, #-0x10]
    // 0xbadd1c: StoreField: r2->field_13 = r0
    //     0xbadd1c: stur            w0, [x2, #0x13]
    // 0xbadd20: ldur            x0, [fp, #-0x18]
    // 0xbadd24: ArrayStore: r2[0] = r0  ; List_4
    //     0xbadd24: stur            w0, [x2, #0x17]
    // 0xbadd28: ldur            x0, [fp, #-0x20]
    // 0xbadd2c: StoreField: r2->field_1b = r0
    //     0xbadd2c: stur            w0, [x2, #0x1b]
    // 0xbadd30: ldur            x0, [fp, #-0x28]
    // 0xbadd34: StoreField: r2->field_1f = r0
    //     0xbadd34: stur            w0, [x2, #0x1f]
    // 0xbadd38: ldur            x0, [fp, #-0x30]
    // 0xbadd3c: StoreField: r2->field_23 = r0
    //     0xbadd3c: stur            w0, [x2, #0x23]
    // 0xbadd40: ldur            x0, [fp, #-0x38]
    // 0xbadd44: StoreField: r2->field_27 = r0
    //     0xbadd44: stur            w0, [x2, #0x27]
    // 0xbadd48: r1 = <Widget>
    //     0xbadd48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbadd4c: r0 = AllocateGrowableArray()
    //     0xbadd4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbadd50: mov             x1, x0
    // 0xbadd54: ldur            x0, [fp, #-0x40]
    // 0xbadd58: stur            x1, [fp, #-8]
    // 0xbadd5c: StoreField: r1->field_f = r0
    //     0xbadd5c: stur            w0, [x1, #0xf]
    // 0xbadd60: r0 = 14
    //     0xbadd60: movz            x0, #0xe
    // 0xbadd64: StoreField: r1->field_b = r0
    //     0xbadd64: stur            w0, [x1, #0xb]
    // 0xbadd68: r0 = Row()
    //     0xbadd68: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbadd6c: r1 = Instance_Axis
    //     0xbadd6c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbadd70: StoreField: r0->field_f = r1
    //     0xbadd70: stur            w1, [x0, #0xf]
    // 0xbadd74: r1 = Instance_MainAxisAlignment
    //     0xbadd74: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f28] Obj!MainAxisAlignment@d734a1
    //     0xbadd78: ldr             x1, [x1, #0xf28]
    // 0xbadd7c: StoreField: r0->field_13 = r1
    //     0xbadd7c: stur            w1, [x0, #0x13]
    // 0xbadd80: r1 = Instance_MainAxisSize
    //     0xbadd80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbadd84: ldr             x1, [x1, #0xa10]
    // 0xbadd88: ArrayStore: r0[0] = r1  ; List_4
    //     0xbadd88: stur            w1, [x0, #0x17]
    // 0xbadd8c: r1 = Instance_CrossAxisAlignment
    //     0xbadd8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbadd90: ldr             x1, [x1, #0xa18]
    // 0xbadd94: StoreField: r0->field_1b = r1
    //     0xbadd94: stur            w1, [x0, #0x1b]
    // 0xbadd98: r1 = Instance_VerticalDirection
    //     0xbadd98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbadd9c: ldr             x1, [x1, #0xa20]
    // 0xbadda0: StoreField: r0->field_23 = r1
    //     0xbadda0: stur            w1, [x0, #0x23]
    // 0xbadda4: r1 = Instance_Clip
    //     0xbadda4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbadda8: ldr             x1, [x1, #0x38]
    // 0xbaddac: StoreField: r0->field_2b = r1
    //     0xbaddac: stur            w1, [x0, #0x2b]
    // 0xbaddb0: StoreField: r0->field_2f = rZR
    //     0xbaddb0: stur            xzr, [x0, #0x2f]
    // 0xbaddb4: ldur            x1, [fp, #-8]
    // 0xbaddb8: StoreField: r0->field_b = r1
    //     0xbaddb8: stur            w1, [x0, #0xb]
    // 0xbaddbc: LeaveFrame
    //     0xbaddbc: mov             SP, fp
    //     0xbaddc0: ldp             fp, lr, [SP], #0x10
    // 0xbaddc4: ret
    //     0xbaddc4: ret             
    // 0xbaddc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaddc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaddcc: b               #0xbadbc8
  }
}

// class id: 4025, size: 0x14, field offset: 0xc
//   const constructor, 
class BottomStripPaymentWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80110, size: 0x24
    // 0xc80110: EnterFrame
    //     0xc80110: stp             fp, lr, [SP, #-0x10]!
    //     0xc80114: mov             fp, SP
    // 0xc80118: mov             x0, x1
    // 0xc8011c: r1 = <BottomStripPaymentWidget>
    //     0xc8011c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48608] TypeArguments: <BottomStripPaymentWidget>
    //     0xc80120: ldr             x1, [x1, #0x608]
    // 0xc80124: r0 = BottomStripPaymentStripState()
    //     0xc80124: bl              #0xc80134  ; AllocateBottomStripPaymentStripStateStub -> BottomStripPaymentStripState (size=0x14)
    // 0xc80128: LeaveFrame
    //     0xc80128: mov             SP, fp
    //     0xc8012c: ldp             fp, lr, [SP], #0x10
    // 0xc80130: ret
    //     0xc80130: ret             
  }
}
