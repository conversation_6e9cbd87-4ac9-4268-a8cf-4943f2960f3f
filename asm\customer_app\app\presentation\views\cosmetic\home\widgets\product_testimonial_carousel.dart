// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart

// class id: 1049283, size: 0x8
class :: {
}

// class id: 3422, size: 0x24, field offset: 0x14
class _ProductTestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xaef1b8, size: 0x64
    // 0xaef1b8: EnterFrame
    //     0xaef1b8: stp             fp, lr, [SP, #-0x10]!
    //     0xaef1bc: mov             fp, SP
    // 0xaef1c0: AllocStack(0x18)
    //     0xaef1c0: sub             SP, SP, #0x18
    // 0xaef1c4: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaef1c4: stur            x1, [fp, #-8]
    //     0xaef1c8: stur            x2, [fp, #-0x10]
    // 0xaef1cc: r1 = 2
    //     0xaef1cc: movz            x1, #0x2
    // 0xaef1d0: r0 = AllocateContext()
    //     0xaef1d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xaef1d4: mov             x1, x0
    // 0xaef1d8: ldur            x0, [fp, #-8]
    // 0xaef1dc: stur            x1, [fp, #-0x18]
    // 0xaef1e0: StoreField: r1->field_f = r0
    //     0xaef1e0: stur            w0, [x1, #0xf]
    // 0xaef1e4: ldur            x0, [fp, #-0x10]
    // 0xaef1e8: StoreField: r1->field_13 = r0
    //     0xaef1e8: stur            w0, [x1, #0x13]
    // 0xaef1ec: r0 = Obx()
    //     0xaef1ec: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaef1f0: ldur            x2, [fp, #-0x18]
    // 0xaef1f4: r1 = Function '<anonymous closure>':.
    //     0xaef1f4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58110] AnonymousClosure: (0xaef240), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xaef1b8)
    //     0xaef1f8: ldr             x1, [x1, #0x110]
    // 0xaef1fc: stur            x0, [fp, #-8]
    // 0xaef200: r0 = AllocateClosure()
    //     0xaef200: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaef204: mov             x1, x0
    // 0xaef208: ldur            x0, [fp, #-8]
    // 0xaef20c: StoreField: r0->field_b = r1
    //     0xaef20c: stur            w1, [x0, #0xb]
    // 0xaef210: LeaveFrame
    //     0xaef210: mov             SP, fp
    //     0xaef214: ldp             fp, lr, [SP], #0x10
    // 0xaef218: ret
    //     0xaef218: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xaef240, size: 0x8bc
    // 0xaef240: EnterFrame
    //     0xaef240: stp             fp, lr, [SP, #-0x10]!
    //     0xaef244: mov             fp, SP
    // 0xaef248: AllocStack(0x78)
    //     0xaef248: sub             SP, SP, #0x78
    // 0xaef24c: SetupParameters()
    //     0xaef24c: ldr             x0, [fp, #0x10]
    //     0xaef250: ldur            w3, [x0, #0x17]
    //     0xaef254: add             x3, x3, HEAP, lsl #32
    //     0xaef258: stur            x3, [fp, #-0x10]
    // 0xaef25c: CheckStackOverflow
    //     0xaef25c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef260: cmp             SP, x16
    //     0xaef264: b.ls            #0xaefabc
    // 0xaef268: LoadField: r0 = r3->field_f
    //     0xaef268: ldur            w0, [x3, #0xf]
    // 0xaef26c: DecompressPointer r0
    //     0xaef26c: add             x0, x0, HEAP, lsl #32
    // 0xaef270: LoadField: r1 = r0->field_b
    //     0xaef270: ldur            w1, [x0, #0xb]
    // 0xaef274: DecompressPointer r1
    //     0xaef274: add             x1, x1, HEAP, lsl #32
    // 0xaef278: cmp             w1, NULL
    // 0xaef27c: b.eq            #0xaefac4
    // 0xaef280: LoadField: r0 = r1->field_13
    //     0xaef280: ldur            w0, [x1, #0x13]
    // 0xaef284: DecompressPointer r0
    //     0xaef284: add             x0, x0, HEAP, lsl #32
    // 0xaef288: LoadField: r1 = r0->field_7
    //     0xaef288: ldur            w1, [x0, #7]
    // 0xaef28c: DecompressPointer r1
    //     0xaef28c: add             x1, x1, HEAP, lsl #32
    // 0xaef290: cmp             w1, NULL
    // 0xaef294: b.ne            #0xaef2a4
    // 0xaef298: r0 = Instance_TitleAlignment
    //     0xaef298: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaef29c: ldr             x0, [x0, #0x518]
    // 0xaef2a0: b               #0xaef2a8
    // 0xaef2a4: mov             x0, x1
    // 0xaef2a8: r16 = Instance_TitleAlignment
    //     0xaef2a8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaef2ac: ldr             x16, [x16, #0x520]
    // 0xaef2b0: cmp             w0, w16
    // 0xaef2b4: b.ne            #0xaef2c4
    // 0xaef2b8: r0 = Instance_CrossAxisAlignment
    //     0xaef2b8: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xaef2bc: ldr             x0, [x0, #0xc68]
    // 0xaef2c0: b               #0xaef2e8
    // 0xaef2c4: r16 = Instance_TitleAlignment
    //     0xaef2c4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaef2c8: ldr             x16, [x16, #0x518]
    // 0xaef2cc: cmp             w0, w16
    // 0xaef2d0: b.ne            #0xaef2e0
    // 0xaef2d4: r0 = Instance_CrossAxisAlignment
    //     0xaef2d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaef2d8: ldr             x0, [x0, #0x890]
    // 0xaef2dc: b               #0xaef2e8
    // 0xaef2e0: r0 = Instance_CrossAxisAlignment
    //     0xaef2e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaef2e4: ldr             x0, [x0, #0xa18]
    // 0xaef2e8: stur            x0, [fp, #-8]
    // 0xaef2ec: r1 = <Widget>
    //     0xaef2ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaef2f0: r2 = 0
    //     0xaef2f0: movz            x2, #0
    // 0xaef2f4: r0 = _GrowableList()
    //     0xaef2f4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaef2f8: mov             x1, x0
    // 0xaef2fc: ldur            x2, [fp, #-0x10]
    // 0xaef300: stur            x1, [fp, #-0x18]
    // 0xaef304: LoadField: r0 = r2->field_f
    //     0xaef304: ldur            w0, [x2, #0xf]
    // 0xaef308: DecompressPointer r0
    //     0xaef308: add             x0, x0, HEAP, lsl #32
    // 0xaef30c: LoadField: r3 = r0->field_b
    //     0xaef30c: ldur            w3, [x0, #0xb]
    // 0xaef310: DecompressPointer r3
    //     0xaef310: add             x3, x3, HEAP, lsl #32
    // 0xaef314: cmp             w3, NULL
    // 0xaef318: b.eq            #0xaefac8
    // 0xaef31c: LoadField: r0 = r3->field_f
    //     0xaef31c: ldur            w0, [x3, #0xf]
    // 0xaef320: DecompressPointer r0
    //     0xaef320: add             x0, x0, HEAP, lsl #32
    // 0xaef324: LoadField: r3 = r0->field_7
    //     0xaef324: ldur            w3, [x0, #7]
    // 0xaef328: cbz             w3, #0xaef4cc
    // 0xaef32c: r3 = LoadClassIdInstr(r0)
    //     0xaef32c: ldur            x3, [x0, #-1]
    //     0xaef330: ubfx            x3, x3, #0xc, #0x14
    // 0xaef334: str             x0, [SP]
    // 0xaef338: mov             x0, x3
    // 0xaef33c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaef33c: sub             lr, x0, #1, lsl #12
    //     0xaef340: ldr             lr, [x21, lr, lsl #3]
    //     0xaef344: blr             lr
    // 0xaef348: ldur            x2, [fp, #-0x10]
    // 0xaef34c: stur            x0, [fp, #-0x28]
    // 0xaef350: LoadField: r1 = r2->field_f
    //     0xaef350: ldur            w1, [x2, #0xf]
    // 0xaef354: DecompressPointer r1
    //     0xaef354: add             x1, x1, HEAP, lsl #32
    // 0xaef358: LoadField: r3 = r1->field_b
    //     0xaef358: ldur            w3, [x1, #0xb]
    // 0xaef35c: DecompressPointer r3
    //     0xaef35c: add             x3, x3, HEAP, lsl #32
    // 0xaef360: cmp             w3, NULL
    // 0xaef364: b.eq            #0xaefacc
    // 0xaef368: LoadField: r1 = r3->field_13
    //     0xaef368: ldur            w1, [x3, #0x13]
    // 0xaef36c: DecompressPointer r1
    //     0xaef36c: add             x1, x1, HEAP, lsl #32
    // 0xaef370: LoadField: r3 = r1->field_7
    //     0xaef370: ldur            w3, [x1, #7]
    // 0xaef374: DecompressPointer r3
    //     0xaef374: add             x3, x3, HEAP, lsl #32
    // 0xaef378: cmp             w3, NULL
    // 0xaef37c: b.ne            #0xaef38c
    // 0xaef380: r1 = Instance_TitleAlignment
    //     0xaef380: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaef384: ldr             x1, [x1, #0x518]
    // 0xaef388: b               #0xaef390
    // 0xaef38c: mov             x1, x3
    // 0xaef390: r16 = Instance_TitleAlignment
    //     0xaef390: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaef394: ldr             x16, [x16, #0x520]
    // 0xaef398: cmp             w1, w16
    // 0xaef39c: b.ne            #0xaef3a8
    // 0xaef3a0: r4 = Instance_TextAlign
    //     0xaef3a0: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xaef3a4: b               #0xaef3c4
    // 0xaef3a8: r16 = Instance_TitleAlignment
    //     0xaef3a8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaef3ac: ldr             x16, [x16, #0x518]
    // 0xaef3b0: cmp             w1, w16
    // 0xaef3b4: b.ne            #0xaef3c0
    // 0xaef3b8: r4 = Instance_TextAlign
    //     0xaef3b8: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xaef3bc: b               #0xaef3c4
    // 0xaef3c0: r4 = Instance_TextAlign
    //     0xaef3c0: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaef3c4: ldur            x3, [fp, #-0x18]
    // 0xaef3c8: stur            x4, [fp, #-0x20]
    // 0xaef3cc: LoadField: r1 = r2->field_13
    //     0xaef3cc: ldur            w1, [x2, #0x13]
    // 0xaef3d0: DecompressPointer r1
    //     0xaef3d0: add             x1, x1, HEAP, lsl #32
    // 0xaef3d4: r0 = of()
    //     0xaef3d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaef3d8: LoadField: r1 = r0->field_87
    //     0xaef3d8: ldur            w1, [x0, #0x87]
    // 0xaef3dc: DecompressPointer r1
    //     0xaef3dc: add             x1, x1, HEAP, lsl #32
    // 0xaef3e0: LoadField: r0 = r1->field_7
    //     0xaef3e0: ldur            w0, [x1, #7]
    // 0xaef3e4: DecompressPointer r0
    //     0xaef3e4: add             x0, x0, HEAP, lsl #32
    // 0xaef3e8: r16 = Instance_Color
    //     0xaef3e8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaef3ec: r30 = 32.000000
    //     0xaef3ec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xaef3f0: ldr             lr, [lr, #0x848]
    // 0xaef3f4: stp             lr, x16, [SP]
    // 0xaef3f8: mov             x1, x0
    // 0xaef3fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaef3fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaef400: ldr             x4, [x4, #0x9b8]
    // 0xaef404: r0 = copyWith()
    //     0xaef404: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaef408: stur            x0, [fp, #-0x30]
    // 0xaef40c: r0 = Text()
    //     0xaef40c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaef410: mov             x1, x0
    // 0xaef414: ldur            x0, [fp, #-0x28]
    // 0xaef418: stur            x1, [fp, #-0x38]
    // 0xaef41c: StoreField: r1->field_b = r0
    //     0xaef41c: stur            w0, [x1, #0xb]
    // 0xaef420: ldur            x0, [fp, #-0x30]
    // 0xaef424: StoreField: r1->field_13 = r0
    //     0xaef424: stur            w0, [x1, #0x13]
    // 0xaef428: ldur            x0, [fp, #-0x20]
    // 0xaef42c: StoreField: r1->field_1b = r0
    //     0xaef42c: stur            w0, [x1, #0x1b]
    // 0xaef430: r0 = Padding()
    //     0xaef430: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaef434: mov             x2, x0
    // 0xaef438: r0 = Instance_EdgeInsets
    //     0xaef438: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xaef43c: ldr             x0, [x0, #0x78]
    // 0xaef440: stur            x2, [fp, #-0x20]
    // 0xaef444: StoreField: r2->field_f = r0
    //     0xaef444: stur            w0, [x2, #0xf]
    // 0xaef448: ldur            x0, [fp, #-0x38]
    // 0xaef44c: StoreField: r2->field_b = r0
    //     0xaef44c: stur            w0, [x2, #0xb]
    // 0xaef450: ldur            x0, [fp, #-0x18]
    // 0xaef454: LoadField: r1 = r0->field_b
    //     0xaef454: ldur            w1, [x0, #0xb]
    // 0xaef458: LoadField: r3 = r0->field_f
    //     0xaef458: ldur            w3, [x0, #0xf]
    // 0xaef45c: DecompressPointer r3
    //     0xaef45c: add             x3, x3, HEAP, lsl #32
    // 0xaef460: LoadField: r4 = r3->field_b
    //     0xaef460: ldur            w4, [x3, #0xb]
    // 0xaef464: r3 = LoadInt32Instr(r1)
    //     0xaef464: sbfx            x3, x1, #1, #0x1f
    // 0xaef468: stur            x3, [fp, #-0x40]
    // 0xaef46c: r1 = LoadInt32Instr(r4)
    //     0xaef46c: sbfx            x1, x4, #1, #0x1f
    // 0xaef470: cmp             x3, x1
    // 0xaef474: b.ne            #0xaef480
    // 0xaef478: mov             x1, x0
    // 0xaef47c: r0 = _growToNextCapacity()
    //     0xaef47c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaef480: ldur            x2, [fp, #-0x18]
    // 0xaef484: ldur            x3, [fp, #-0x40]
    // 0xaef488: add             x0, x3, #1
    // 0xaef48c: lsl             x1, x0, #1
    // 0xaef490: StoreField: r2->field_b = r1
    //     0xaef490: stur            w1, [x2, #0xb]
    // 0xaef494: LoadField: r1 = r2->field_f
    //     0xaef494: ldur            w1, [x2, #0xf]
    // 0xaef498: DecompressPointer r1
    //     0xaef498: add             x1, x1, HEAP, lsl #32
    // 0xaef49c: ldur            x0, [fp, #-0x20]
    // 0xaef4a0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaef4a0: add             x25, x1, x3, lsl #2
    //     0xaef4a4: add             x25, x25, #0xf
    //     0xaef4a8: str             w0, [x25]
    //     0xaef4ac: tbz             w0, #0, #0xaef4c8
    //     0xaef4b0: ldurb           w16, [x1, #-1]
    //     0xaef4b4: ldurb           w17, [x0, #-1]
    //     0xaef4b8: and             x16, x17, x16, lsr #2
    //     0xaef4bc: tst             x16, HEAP, lsr #32
    //     0xaef4c0: b.eq            #0xaef4c8
    //     0xaef4c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaef4c8: b               #0xaef4d0
    // 0xaef4cc: mov             x2, x1
    // 0xaef4d0: ldur            x0, [fp, #-0x10]
    // 0xaef4d4: LoadField: r1 = r0->field_13
    //     0xaef4d4: ldur            w1, [x0, #0x13]
    // 0xaef4d8: DecompressPointer r1
    //     0xaef4d8: add             x1, x1, HEAP, lsl #32
    // 0xaef4dc: r0 = of()
    //     0xaef4dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaef4e0: LoadField: r1 = r0->field_5b
    //     0xaef4e0: ldur            w1, [x0, #0x5b]
    // 0xaef4e4: DecompressPointer r1
    //     0xaef4e4: add             x1, x1, HEAP, lsl #32
    // 0xaef4e8: stur            x1, [fp, #-0x20]
    // 0xaef4ec: r0 = BoxDecoration()
    //     0xaef4ec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaef4f0: mov             x1, x0
    // 0xaef4f4: ldur            x0, [fp, #-0x20]
    // 0xaef4f8: stur            x1, [fp, #-0x28]
    // 0xaef4fc: StoreField: r1->field_7 = r0
    //     0xaef4fc: stur            w0, [x1, #7]
    // 0xaef500: r0 = Instance_BorderRadius
    //     0xaef500: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xaef504: ldr             x0, [x0, #0x460]
    // 0xaef508: StoreField: r1->field_13 = r0
    //     0xaef508: stur            w0, [x1, #0x13]
    // 0xaef50c: r0 = Instance_BoxShape
    //     0xaef50c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaef510: ldr             x0, [x0, #0x80]
    // 0xaef514: StoreField: r1->field_23 = r0
    //     0xaef514: stur            w0, [x1, #0x23]
    // 0xaef518: ldur            x2, [fp, #-0x10]
    // 0xaef51c: LoadField: r3 = r2->field_f
    //     0xaef51c: ldur            w3, [x2, #0xf]
    // 0xaef520: DecompressPointer r3
    //     0xaef520: add             x3, x3, HEAP, lsl #32
    // 0xaef524: LoadField: r4 = r3->field_b
    //     0xaef524: ldur            w4, [x3, #0xb]
    // 0xaef528: DecompressPointer r4
    //     0xaef528: add             x4, x4, HEAP, lsl #32
    // 0xaef52c: cmp             w4, NULL
    // 0xaef530: b.eq            #0xaefad0
    // 0xaef534: LoadField: r3 = r4->field_2b
    //     0xaef534: ldur            w3, [x4, #0x2b]
    // 0xaef538: DecompressPointer r3
    //     0xaef538: add             x3, x3, HEAP, lsl #32
    // 0xaef53c: cmp             w3, NULL
    // 0xaef540: b.ne            #0xaef54c
    // 0xaef544: r4 = Null
    //     0xaef544: mov             x4, NULL
    // 0xaef548: b               #0xaef554
    // 0xaef54c: LoadField: r4 = r3->field_7
    //     0xaef54c: ldur            w4, [x3, #7]
    // 0xaef550: DecompressPointer r4
    //     0xaef550: add             x4, x4, HEAP, lsl #32
    // 0xaef554: ldur            x3, [fp, #-0x18]
    // 0xaef558: str             x4, [SP]
    // 0xaef55c: r0 = _interpolateSingle()
    //     0xaef55c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xaef560: ldur            x2, [fp, #-0x10]
    // 0xaef564: stur            x0, [fp, #-0x20]
    // 0xaef568: LoadField: r1 = r2->field_13
    //     0xaef568: ldur            w1, [x2, #0x13]
    // 0xaef56c: DecompressPointer r1
    //     0xaef56c: add             x1, x1, HEAP, lsl #32
    // 0xaef570: r0 = of()
    //     0xaef570: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaef574: LoadField: r1 = r0->field_87
    //     0xaef574: ldur            w1, [x0, #0x87]
    // 0xaef578: DecompressPointer r1
    //     0xaef578: add             x1, x1, HEAP, lsl #32
    // 0xaef57c: LoadField: r0 = r1->field_2b
    //     0xaef57c: ldur            w0, [x1, #0x2b]
    // 0xaef580: DecompressPointer r0
    //     0xaef580: add             x0, x0, HEAP, lsl #32
    // 0xaef584: r16 = 16.000000
    //     0xaef584: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaef588: ldr             x16, [x16, #0x188]
    // 0xaef58c: r30 = Instance_Color
    //     0xaef58c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaef590: stp             lr, x16, [SP]
    // 0xaef594: mov             x1, x0
    // 0xaef598: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaef598: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaef59c: ldr             x4, [x4, #0xaa0]
    // 0xaef5a0: r0 = copyWith()
    //     0xaef5a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaef5a4: stur            x0, [fp, #-0x30]
    // 0xaef5a8: r0 = Text()
    //     0xaef5a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaef5ac: mov             x1, x0
    // 0xaef5b0: ldur            x0, [fp, #-0x20]
    // 0xaef5b4: stur            x1, [fp, #-0x38]
    // 0xaef5b8: StoreField: r1->field_b = r0
    //     0xaef5b8: stur            w0, [x1, #0xb]
    // 0xaef5bc: ldur            x0, [fp, #-0x30]
    // 0xaef5c0: StoreField: r1->field_13 = r0
    //     0xaef5c0: stur            w0, [x1, #0x13]
    // 0xaef5c4: r0 = Center()
    //     0xaef5c4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaef5c8: mov             x1, x0
    // 0xaef5cc: r0 = Instance_Alignment
    //     0xaef5cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaef5d0: ldr             x0, [x0, #0xb10]
    // 0xaef5d4: stur            x1, [fp, #-0x20]
    // 0xaef5d8: StoreField: r1->field_f = r0
    //     0xaef5d8: stur            w0, [x1, #0xf]
    // 0xaef5dc: ldur            x0, [fp, #-0x38]
    // 0xaef5e0: StoreField: r1->field_b = r0
    //     0xaef5e0: stur            w0, [x1, #0xb]
    // 0xaef5e4: r0 = Container()
    //     0xaef5e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaef5e8: stur            x0, [fp, #-0x30]
    // 0xaef5ec: r16 = 40.000000
    //     0xaef5ec: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xaef5f0: ldr             x16, [x16, #8]
    // 0xaef5f4: r30 = 110.000000
    //     0xaef5f4: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xaef5f8: ldr             lr, [lr, #0x770]
    // 0xaef5fc: stp             lr, x16, [SP, #0x10]
    // 0xaef600: ldur            x16, [fp, #-0x28]
    // 0xaef604: ldur            lr, [fp, #-0x20]
    // 0xaef608: stp             lr, x16, [SP]
    // 0xaef60c: mov             x1, x0
    // 0xaef610: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xaef610: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xaef614: ldr             x4, [x4, #0x8c0]
    // 0xaef618: r0 = Container()
    //     0xaef618: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaef61c: r0 = InkWell()
    //     0xaef61c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaef620: mov             x3, x0
    // 0xaef624: ldur            x0, [fp, #-0x30]
    // 0xaef628: stur            x3, [fp, #-0x20]
    // 0xaef62c: StoreField: r3->field_b = r0
    //     0xaef62c: stur            w0, [x3, #0xb]
    // 0xaef630: ldur            x2, [fp, #-0x10]
    // 0xaef634: r1 = Function '<anonymous closure>':.
    //     0xaef634: add             x1, PP, #0x58, lsl #12  ; [pp+0x58118] AnonymousClosure: (0xaf0bbc), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xaef1b8)
    //     0xaef638: ldr             x1, [x1, #0x118]
    // 0xaef63c: r0 = AllocateClosure()
    //     0xaef63c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaef640: mov             x1, x0
    // 0xaef644: ldur            x0, [fp, #-0x20]
    // 0xaef648: StoreField: r0->field_f = r1
    //     0xaef648: stur            w1, [x0, #0xf]
    // 0xaef64c: r1 = true
    //     0xaef64c: add             x1, NULL, #0x20  ; true
    // 0xaef650: StoreField: r0->field_43 = r1
    //     0xaef650: stur            w1, [x0, #0x43]
    // 0xaef654: r2 = Instance_BoxShape
    //     0xaef654: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaef658: ldr             x2, [x2, #0x80]
    // 0xaef65c: StoreField: r0->field_47 = r2
    //     0xaef65c: stur            w2, [x0, #0x47]
    // 0xaef660: StoreField: r0->field_6f = r1
    //     0xaef660: stur            w1, [x0, #0x6f]
    // 0xaef664: r2 = false
    //     0xaef664: add             x2, NULL, #0x30  ; false
    // 0xaef668: StoreField: r0->field_73 = r2
    //     0xaef668: stur            w2, [x0, #0x73]
    // 0xaef66c: StoreField: r0->field_83 = r1
    //     0xaef66c: stur            w1, [x0, #0x83]
    // 0xaef670: StoreField: r0->field_7b = r2
    //     0xaef670: stur            w2, [x0, #0x7b]
    // 0xaef674: r0 = Padding()
    //     0xaef674: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaef678: mov             x2, x0
    // 0xaef67c: r0 = Instance_EdgeInsets
    //     0xaef67c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xaef680: ldr             x0, [x0, #0xf30]
    // 0xaef684: stur            x2, [fp, #-0x28]
    // 0xaef688: StoreField: r2->field_f = r0
    //     0xaef688: stur            w0, [x2, #0xf]
    // 0xaef68c: ldur            x0, [fp, #-0x20]
    // 0xaef690: StoreField: r2->field_b = r0
    //     0xaef690: stur            w0, [x2, #0xb]
    // 0xaef694: ldur            x0, [fp, #-0x18]
    // 0xaef698: LoadField: r1 = r0->field_b
    //     0xaef698: ldur            w1, [x0, #0xb]
    // 0xaef69c: LoadField: r3 = r0->field_f
    //     0xaef69c: ldur            w3, [x0, #0xf]
    // 0xaef6a0: DecompressPointer r3
    //     0xaef6a0: add             x3, x3, HEAP, lsl #32
    // 0xaef6a4: LoadField: r4 = r3->field_b
    //     0xaef6a4: ldur            w4, [x3, #0xb]
    // 0xaef6a8: r3 = LoadInt32Instr(r1)
    //     0xaef6a8: sbfx            x3, x1, #1, #0x1f
    // 0xaef6ac: stur            x3, [fp, #-0x40]
    // 0xaef6b0: r1 = LoadInt32Instr(r4)
    //     0xaef6b0: sbfx            x1, x4, #1, #0x1f
    // 0xaef6b4: cmp             x3, x1
    // 0xaef6b8: b.ne            #0xaef6c4
    // 0xaef6bc: mov             x1, x0
    // 0xaef6c0: r0 = _growToNextCapacity()
    //     0xaef6c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaef6c4: ldur            x4, [fp, #-0x10]
    // 0xaef6c8: ldur            x3, [fp, #-0x18]
    // 0xaef6cc: ldur            x2, [fp, #-0x40]
    // 0xaef6d0: add             x0, x2, #1
    // 0xaef6d4: lsl             x1, x0, #1
    // 0xaef6d8: StoreField: r3->field_b = r1
    //     0xaef6d8: stur            w1, [x3, #0xb]
    // 0xaef6dc: LoadField: r1 = r3->field_f
    //     0xaef6dc: ldur            w1, [x3, #0xf]
    // 0xaef6e0: DecompressPointer r1
    //     0xaef6e0: add             x1, x1, HEAP, lsl #32
    // 0xaef6e4: ldur            x0, [fp, #-0x28]
    // 0xaef6e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaef6e8: add             x25, x1, x2, lsl #2
    //     0xaef6ec: add             x25, x25, #0xf
    //     0xaef6f0: str             w0, [x25]
    //     0xaef6f4: tbz             w0, #0, #0xaef710
    //     0xaef6f8: ldurb           w16, [x1, #-1]
    //     0xaef6fc: ldurb           w17, [x0, #-1]
    //     0xaef700: and             x16, x17, x16, lsr #2
    //     0xaef704: tst             x16, HEAP, lsr #32
    //     0xaef708: b.eq            #0xaef710
    //     0xaef70c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaef710: LoadField: r1 = r4->field_f
    //     0xaef710: ldur            w1, [x4, #0xf]
    // 0xaef714: DecompressPointer r1
    //     0xaef714: add             x1, x1, HEAP, lsl #32
    // 0xaef718: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xaef718: ldur            x2, [x1, #0x17]
    // 0xaef71c: r0 = _calculateCardHeight()
    //     0xaef71c: bl              #0xa5b7d8  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_calculateCardHeight
    // 0xaef720: ldur            x0, [fp, #-0x10]
    // 0xaef724: stur            d0, [fp, #-0x58]
    // 0xaef728: LoadField: r1 = r0->field_f
    //     0xaef728: ldur            w1, [x0, #0xf]
    // 0xaef72c: DecompressPointer r1
    //     0xaef72c: add             x1, x1, HEAP, lsl #32
    // 0xaef730: LoadField: r2 = r1->field_b
    //     0xaef730: ldur            w2, [x1, #0xb]
    // 0xaef734: DecompressPointer r2
    //     0xaef734: add             x2, x2, HEAP, lsl #32
    // 0xaef738: cmp             w2, NULL
    // 0xaef73c: b.eq            #0xaefad4
    // 0xaef740: LoadField: r3 = r2->field_b
    //     0xaef740: ldur            w3, [x2, #0xb]
    // 0xaef744: DecompressPointer r3
    //     0xaef744: add             x3, x3, HEAP, lsl #32
    // 0xaef748: cmp             w3, NULL
    // 0xaef74c: b.ne            #0xaef758
    // 0xaef750: r4 = Null
    //     0xaef750: mov             x4, NULL
    // 0xaef754: b               #0xaef760
    // 0xaef758: LoadField: r2 = r3->field_b
    //     0xaef758: ldur            w2, [x3, #0xb]
    // 0xaef75c: mov             x4, x2
    // 0xaef760: ldur            x3, [fp, #-0x18]
    // 0xaef764: stur            x4, [fp, #-0x28]
    // 0xaef768: LoadField: r5 = r1->field_13
    //     0xaef768: ldur            w5, [x1, #0x13]
    // 0xaef76c: DecompressPointer r5
    //     0xaef76c: add             x5, x5, HEAP, lsl #32
    // 0xaef770: r16 = Sentinel
    //     0xaef770: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaef774: cmp             w5, w16
    // 0xaef778: b.eq            #0xaefad8
    // 0xaef77c: mov             x2, x0
    // 0xaef780: stur            x5, [fp, #-0x20]
    // 0xaef784: r1 = Function '<anonymous closure>':.
    //     0xaef784: add             x1, PP, #0x58, lsl #12  ; [pp+0x58120] AnonymousClosure: (0xaf0aac), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xaef1b8)
    //     0xaef788: ldr             x1, [x1, #0x120]
    // 0xaef78c: r0 = AllocateClosure()
    //     0xaef78c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaef790: ldur            x2, [fp, #-0x10]
    // 0xaef794: r1 = Function '<anonymous closure>':.
    //     0xaef794: add             x1, PP, #0x58, lsl #12  ; [pp+0x58128] AnonymousClosure: (0xaefafc), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xaef1b8)
    //     0xaef798: ldr             x1, [x1, #0x128]
    // 0xaef79c: stur            x0, [fp, #-0x30]
    // 0xaef7a0: r0 = AllocateClosure()
    //     0xaef7a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaef7a4: stur            x0, [fp, #-0x38]
    // 0xaef7a8: r0 = PageView()
    //     0xaef7a8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xaef7ac: stur            x0, [fp, #-0x48]
    // 0xaef7b0: r16 = Instance_BouncingScrollPhysics
    //     0xaef7b0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xaef7b4: ldr             x16, [x16, #0x890]
    // 0xaef7b8: ldur            lr, [fp, #-0x20]
    // 0xaef7bc: stp             lr, x16, [SP]
    // 0xaef7c0: mov             x1, x0
    // 0xaef7c4: ldur            x2, [fp, #-0x38]
    // 0xaef7c8: ldur            x3, [fp, #-0x28]
    // 0xaef7cc: ldur            x5, [fp, #-0x30]
    // 0xaef7d0: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xaef7d0: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xaef7d4: ldr             x4, [x4, #0xe40]
    // 0xaef7d8: r0 = PageView.builder()
    //     0xaef7d8: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xaef7dc: ldur            d0, [fp, #-0x58]
    // 0xaef7e0: r0 = inline_Allocate_Double()
    //     0xaef7e0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaef7e4: add             x0, x0, #0x10
    //     0xaef7e8: cmp             x1, x0
    //     0xaef7ec: b.ls            #0xaefae4
    //     0xaef7f0: str             x0, [THR, #0x50]  ; THR::top
    //     0xaef7f4: sub             x0, x0, #0xf
    //     0xaef7f8: movz            x1, #0xe15c
    //     0xaef7fc: movk            x1, #0x3, lsl #16
    //     0xaef800: stur            x1, [x0, #-1]
    // 0xaef804: StoreField: r0->field_7 = d0
    //     0xaef804: stur            d0, [x0, #7]
    // 0xaef808: stur            x0, [fp, #-0x20]
    // 0xaef80c: r0 = SizedBox()
    //     0xaef80c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaef810: mov             x2, x0
    // 0xaef814: ldur            x0, [fp, #-0x20]
    // 0xaef818: stur            x2, [fp, #-0x28]
    // 0xaef81c: StoreField: r2->field_13 = r0
    //     0xaef81c: stur            w0, [x2, #0x13]
    // 0xaef820: ldur            x0, [fp, #-0x48]
    // 0xaef824: StoreField: r2->field_b = r0
    //     0xaef824: stur            w0, [x2, #0xb]
    // 0xaef828: ldur            x0, [fp, #-0x18]
    // 0xaef82c: LoadField: r1 = r0->field_b
    //     0xaef82c: ldur            w1, [x0, #0xb]
    // 0xaef830: LoadField: r3 = r0->field_f
    //     0xaef830: ldur            w3, [x0, #0xf]
    // 0xaef834: DecompressPointer r3
    //     0xaef834: add             x3, x3, HEAP, lsl #32
    // 0xaef838: LoadField: r4 = r3->field_b
    //     0xaef838: ldur            w4, [x3, #0xb]
    // 0xaef83c: r3 = LoadInt32Instr(r1)
    //     0xaef83c: sbfx            x3, x1, #1, #0x1f
    // 0xaef840: stur            x3, [fp, #-0x40]
    // 0xaef844: r1 = LoadInt32Instr(r4)
    //     0xaef844: sbfx            x1, x4, #1, #0x1f
    // 0xaef848: cmp             x3, x1
    // 0xaef84c: b.ne            #0xaef858
    // 0xaef850: mov             x1, x0
    // 0xaef854: r0 = _growToNextCapacity()
    //     0xaef854: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaef858: ldur            x4, [fp, #-0x10]
    // 0xaef85c: ldur            x2, [fp, #-0x18]
    // 0xaef860: ldur            x3, [fp, #-0x40]
    // 0xaef864: add             x0, x3, #1
    // 0xaef868: lsl             x1, x0, #1
    // 0xaef86c: StoreField: r2->field_b = r1
    //     0xaef86c: stur            w1, [x2, #0xb]
    // 0xaef870: LoadField: r1 = r2->field_f
    //     0xaef870: ldur            w1, [x2, #0xf]
    // 0xaef874: DecompressPointer r1
    //     0xaef874: add             x1, x1, HEAP, lsl #32
    // 0xaef878: ldur            x0, [fp, #-0x28]
    // 0xaef87c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaef87c: add             x25, x1, x3, lsl #2
    //     0xaef880: add             x25, x25, #0xf
    //     0xaef884: str             w0, [x25]
    //     0xaef888: tbz             w0, #0, #0xaef8a4
    //     0xaef88c: ldurb           w16, [x1, #-1]
    //     0xaef890: ldurb           w17, [x0, #-1]
    //     0xaef894: and             x16, x17, x16, lsr #2
    //     0xaef898: tst             x16, HEAP, lsr #32
    //     0xaef89c: b.eq            #0xaef8a4
    //     0xaef8a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaef8a4: LoadField: r0 = r4->field_f
    //     0xaef8a4: ldur            w0, [x4, #0xf]
    // 0xaef8a8: DecompressPointer r0
    //     0xaef8a8: add             x0, x0, HEAP, lsl #32
    // 0xaef8ac: LoadField: r1 = r0->field_b
    //     0xaef8ac: ldur            w1, [x0, #0xb]
    // 0xaef8b0: DecompressPointer r1
    //     0xaef8b0: add             x1, x1, HEAP, lsl #32
    // 0xaef8b4: cmp             w1, NULL
    // 0xaef8b8: b.eq            #0xaefaf4
    // 0xaef8bc: LoadField: r3 = r1->field_b
    //     0xaef8bc: ldur            w3, [x1, #0xb]
    // 0xaef8c0: DecompressPointer r3
    //     0xaef8c0: add             x3, x3, HEAP, lsl #32
    // 0xaef8c4: cmp             w3, NULL
    // 0xaef8c8: b.eq            #0xaefaf8
    // 0xaef8cc: LoadField: r1 = r3->field_b
    //     0xaef8cc: ldur            w1, [x3, #0xb]
    // 0xaef8d0: r3 = LoadInt32Instr(r1)
    //     0xaef8d0: sbfx            x3, x1, #1, #0x1f
    // 0xaef8d4: cmp             x3, #1
    // 0xaef8d8: b.le            #0xaef9b0
    // 0xaef8dc: r3 = LoadInt32Instr(r1)
    //     0xaef8dc: sbfx            x3, x1, #1, #0x1f
    // 0xaef8e0: stur            x3, [fp, #-0x50]
    // 0xaef8e4: ArrayLoad: r5 = r0[0]  ; List_8
    //     0xaef8e4: ldur            x5, [x0, #0x17]
    // 0xaef8e8: stur            x5, [fp, #-0x40]
    // 0xaef8ec: LoadField: r1 = r4->field_13
    //     0xaef8ec: ldur            w1, [x4, #0x13]
    // 0xaef8f0: DecompressPointer r1
    //     0xaef8f0: add             x1, x1, HEAP, lsl #32
    // 0xaef8f4: r0 = of()
    //     0xaef8f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaef8f8: LoadField: r1 = r0->field_5b
    //     0xaef8f8: ldur            w1, [x0, #0x5b]
    // 0xaef8fc: DecompressPointer r1
    //     0xaef8fc: add             x1, x1, HEAP, lsl #32
    // 0xaef900: stur            x1, [fp, #-0x10]
    // 0xaef904: r0 = CarouselIndicator()
    //     0xaef904: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xaef908: mov             x2, x0
    // 0xaef90c: ldur            x0, [fp, #-0x50]
    // 0xaef910: stur            x2, [fp, #-0x20]
    // 0xaef914: StoreField: r2->field_b = r0
    //     0xaef914: stur            x0, [x2, #0xb]
    // 0xaef918: ldur            x0, [fp, #-0x40]
    // 0xaef91c: StoreField: r2->field_13 = r0
    //     0xaef91c: stur            x0, [x2, #0x13]
    // 0xaef920: ldur            x0, [fp, #-0x10]
    // 0xaef924: StoreField: r2->field_1b = r0
    //     0xaef924: stur            w0, [x2, #0x1b]
    // 0xaef928: r0 = Instance_Color
    //     0xaef928: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xaef92c: ldr             x0, [x0, #0x90]
    // 0xaef930: StoreField: r2->field_1f = r0
    //     0xaef930: stur            w0, [x2, #0x1f]
    // 0xaef934: ldur            x0, [fp, #-0x18]
    // 0xaef938: LoadField: r1 = r0->field_b
    //     0xaef938: ldur            w1, [x0, #0xb]
    // 0xaef93c: LoadField: r3 = r0->field_f
    //     0xaef93c: ldur            w3, [x0, #0xf]
    // 0xaef940: DecompressPointer r3
    //     0xaef940: add             x3, x3, HEAP, lsl #32
    // 0xaef944: LoadField: r4 = r3->field_b
    //     0xaef944: ldur            w4, [x3, #0xb]
    // 0xaef948: r3 = LoadInt32Instr(r1)
    //     0xaef948: sbfx            x3, x1, #1, #0x1f
    // 0xaef94c: stur            x3, [fp, #-0x40]
    // 0xaef950: r1 = LoadInt32Instr(r4)
    //     0xaef950: sbfx            x1, x4, #1, #0x1f
    // 0xaef954: cmp             x3, x1
    // 0xaef958: b.ne            #0xaef964
    // 0xaef95c: mov             x1, x0
    // 0xaef960: r0 = _growToNextCapacity()
    //     0xaef960: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaef964: ldur            x2, [fp, #-0x18]
    // 0xaef968: ldur            x3, [fp, #-0x40]
    // 0xaef96c: add             x0, x3, #1
    // 0xaef970: lsl             x1, x0, #1
    // 0xaef974: StoreField: r2->field_b = r1
    //     0xaef974: stur            w1, [x2, #0xb]
    // 0xaef978: LoadField: r1 = r2->field_f
    //     0xaef978: ldur            w1, [x2, #0xf]
    // 0xaef97c: DecompressPointer r1
    //     0xaef97c: add             x1, x1, HEAP, lsl #32
    // 0xaef980: ldur            x0, [fp, #-0x20]
    // 0xaef984: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaef984: add             x25, x1, x3, lsl #2
    //     0xaef988: add             x25, x25, #0xf
    //     0xaef98c: str             w0, [x25]
    //     0xaef990: tbz             w0, #0, #0xaef9ac
    //     0xaef994: ldurb           w16, [x1, #-1]
    //     0xaef998: ldurb           w17, [x0, #-1]
    //     0xaef99c: and             x16, x17, x16, lsr #2
    //     0xaef9a0: tst             x16, HEAP, lsr #32
    //     0xaef9a4: b.eq            #0xaef9ac
    //     0xaef9a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaef9ac: b               #0xaefa3c
    // 0xaef9b0: r0 = Container()
    //     0xaef9b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaef9b4: mov             x1, x0
    // 0xaef9b8: stur            x0, [fp, #-0x10]
    // 0xaef9bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaef9bc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaef9c0: r0 = Container()
    //     0xaef9c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaef9c4: ldur            x0, [fp, #-0x18]
    // 0xaef9c8: LoadField: r1 = r0->field_b
    //     0xaef9c8: ldur            w1, [x0, #0xb]
    // 0xaef9cc: LoadField: r2 = r0->field_f
    //     0xaef9cc: ldur            w2, [x0, #0xf]
    // 0xaef9d0: DecompressPointer r2
    //     0xaef9d0: add             x2, x2, HEAP, lsl #32
    // 0xaef9d4: LoadField: r3 = r2->field_b
    //     0xaef9d4: ldur            w3, [x2, #0xb]
    // 0xaef9d8: r2 = LoadInt32Instr(r1)
    //     0xaef9d8: sbfx            x2, x1, #1, #0x1f
    // 0xaef9dc: stur            x2, [fp, #-0x40]
    // 0xaef9e0: r1 = LoadInt32Instr(r3)
    //     0xaef9e0: sbfx            x1, x3, #1, #0x1f
    // 0xaef9e4: cmp             x2, x1
    // 0xaef9e8: b.ne            #0xaef9f4
    // 0xaef9ec: mov             x1, x0
    // 0xaef9f0: r0 = _growToNextCapacity()
    //     0xaef9f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaef9f4: ldur            x2, [fp, #-0x18]
    // 0xaef9f8: ldur            x3, [fp, #-0x40]
    // 0xaef9fc: add             x0, x3, #1
    // 0xaefa00: lsl             x1, x0, #1
    // 0xaefa04: StoreField: r2->field_b = r1
    //     0xaefa04: stur            w1, [x2, #0xb]
    // 0xaefa08: LoadField: r1 = r2->field_f
    //     0xaefa08: ldur            w1, [x2, #0xf]
    // 0xaefa0c: DecompressPointer r1
    //     0xaefa0c: add             x1, x1, HEAP, lsl #32
    // 0xaefa10: ldur            x0, [fp, #-0x10]
    // 0xaefa14: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaefa14: add             x25, x1, x3, lsl #2
    //     0xaefa18: add             x25, x25, #0xf
    //     0xaefa1c: str             w0, [x25]
    //     0xaefa20: tbz             w0, #0, #0xaefa3c
    //     0xaefa24: ldurb           w16, [x1, #-1]
    //     0xaefa28: ldurb           w17, [x0, #-1]
    //     0xaefa2c: and             x16, x17, x16, lsr #2
    //     0xaefa30: tst             x16, HEAP, lsr #32
    //     0xaefa34: b.eq            #0xaefa3c
    //     0xaefa38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaefa3c: ldur            x0, [fp, #-8]
    // 0xaefa40: r0 = Column()
    //     0xaefa40: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaefa44: mov             x1, x0
    // 0xaefa48: r0 = Instance_Axis
    //     0xaefa48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaefa4c: stur            x1, [fp, #-0x10]
    // 0xaefa50: StoreField: r1->field_f = r0
    //     0xaefa50: stur            w0, [x1, #0xf]
    // 0xaefa54: r0 = Instance_MainAxisAlignment
    //     0xaefa54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaefa58: ldr             x0, [x0, #0xa08]
    // 0xaefa5c: StoreField: r1->field_13 = r0
    //     0xaefa5c: stur            w0, [x1, #0x13]
    // 0xaefa60: r0 = Instance_MainAxisSize
    //     0xaefa60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaefa64: ldr             x0, [x0, #0xa10]
    // 0xaefa68: ArrayStore: r1[0] = r0  ; List_4
    //     0xaefa68: stur            w0, [x1, #0x17]
    // 0xaefa6c: ldur            x0, [fp, #-8]
    // 0xaefa70: StoreField: r1->field_1b = r0
    //     0xaefa70: stur            w0, [x1, #0x1b]
    // 0xaefa74: r0 = Instance_VerticalDirection
    //     0xaefa74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaefa78: ldr             x0, [x0, #0xa20]
    // 0xaefa7c: StoreField: r1->field_23 = r0
    //     0xaefa7c: stur            w0, [x1, #0x23]
    // 0xaefa80: r0 = Instance_Clip
    //     0xaefa80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaefa84: ldr             x0, [x0, #0x38]
    // 0xaefa88: StoreField: r1->field_2b = r0
    //     0xaefa88: stur            w0, [x1, #0x2b]
    // 0xaefa8c: StoreField: r1->field_2f = rZR
    //     0xaefa8c: stur            xzr, [x1, #0x2f]
    // 0xaefa90: ldur            x0, [fp, #-0x18]
    // 0xaefa94: StoreField: r1->field_b = r0
    //     0xaefa94: stur            w0, [x1, #0xb]
    // 0xaefa98: r0 = Padding()
    //     0xaefa98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaefa9c: r1 = Instance_EdgeInsets
    //     0xaefa9c: add             x1, PP, #0x55, lsl #12  ; [pp+0x550a8] Obj!EdgeInsets@d58761
    //     0xaefaa0: ldr             x1, [x1, #0xa8]
    // 0xaefaa4: StoreField: r0->field_f = r1
    //     0xaefaa4: stur            w1, [x0, #0xf]
    // 0xaefaa8: ldur            x1, [fp, #-0x10]
    // 0xaefaac: StoreField: r0->field_b = r1
    //     0xaefaac: stur            w1, [x0, #0xb]
    // 0xaefab0: LeaveFrame
    //     0xaefab0: mov             SP, fp
    //     0xaefab4: ldp             fp, lr, [SP], #0x10
    // 0xaefab8: ret
    //     0xaefab8: ret             
    // 0xaefabc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaefabc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaefac0: b               #0xaef268
    // 0xaefac4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefac4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaefac8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefac8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaefacc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefacc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaefad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefad0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaefad4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaefad4: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xaefad8: r9 = _pageController
    //     0xaefad8: add             x9, PP, #0x58, lsl #12  ; [pp+0x58108] Field <_ProductTestimonialCarouselState@1468390880._pageController@1468390880>: late (offset: 0x14)
    //     0xaefadc: ldr             x9, [x9, #0x108]
    // 0xaefae0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xaefae0: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xaefae4: SaveReg d0
    //     0xaefae4: str             q0, [SP, #-0x10]!
    // 0xaefae8: r0 = AllocateDouble()
    //     0xaefae8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaefaec: RestoreReg d0
    //     0xaefaec: ldr             q0, [SP], #0x10
    // 0xaefaf0: b               #0xaef804
    // 0xaefaf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefaf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaefaf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefaf8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaefafc, size: 0x7c
    // 0xaefafc: EnterFrame
    //     0xaefafc: stp             fp, lr, [SP, #-0x10]!
    //     0xaefb00: mov             fp, SP
    // 0xaefb04: ldr             x0, [fp, #0x20]
    // 0xaefb08: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaefb08: ldur            w1, [x0, #0x17]
    // 0xaefb0c: DecompressPointer r1
    //     0xaefb0c: add             x1, x1, HEAP, lsl #32
    // 0xaefb10: CheckStackOverflow
    //     0xaefb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaefb14: cmp             SP, x16
    //     0xaefb18: b.ls            #0xaefb68
    // 0xaefb1c: LoadField: r0 = r1->field_f
    //     0xaefb1c: ldur            w0, [x1, #0xf]
    // 0xaefb20: DecompressPointer r0
    //     0xaefb20: add             x0, x0, HEAP, lsl #32
    // 0xaefb24: LoadField: r1 = r0->field_b
    //     0xaefb24: ldur            w1, [x0, #0xb]
    // 0xaefb28: DecompressPointer r1
    //     0xaefb28: add             x1, x1, HEAP, lsl #32
    // 0xaefb2c: cmp             w1, NULL
    // 0xaefb30: b.eq            #0xaefb70
    // 0xaefb34: LoadField: r2 = r1->field_b
    //     0xaefb34: ldur            w2, [x1, #0xb]
    // 0xaefb38: DecompressPointer r2
    //     0xaefb38: add             x2, x2, HEAP, lsl #32
    // 0xaefb3c: cmp             w2, NULL
    // 0xaefb40: b.eq            #0xaefb74
    // 0xaefb44: ldr             x1, [fp, #0x10]
    // 0xaefb48: r3 = LoadInt32Instr(r1)
    //     0xaefb48: sbfx            x3, x1, #1, #0x1f
    //     0xaefb4c: tbz             w1, #0, #0xaefb54
    //     0xaefb50: ldur            x3, [x1, #7]
    // 0xaefb54: mov             x1, x0
    // 0xaefb58: r0 = _testimonialCard()
    //     0xaefb58: bl              #0xaefb78  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard
    // 0xaefb5c: LeaveFrame
    //     0xaefb5c: mov             SP, fp
    //     0xaefb60: ldp             fp, lr, [SP], #0x10
    // 0xaefb64: ret
    //     0xaefb64: ret             
    // 0xaefb68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaefb68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaefb6c: b               #0xaefb1c
    // 0xaefb70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefb70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaefb74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaefb74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialCard(/* No info */) {
    // ** addr: 0xaefb78, size: 0xea8
    // 0xaefb78: EnterFrame
    //     0xaefb78: stp             fp, lr, [SP, #-0x10]!
    //     0xaefb7c: mov             fp, SP
    // 0xaefb80: AllocStack(0xa0)
    //     0xaefb80: sub             SP, SP, #0xa0
    // 0xaefb84: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xaefb84: mov             x0, x1
    //     0xaefb88: stur            x1, [fp, #-8]
    //     0xaefb8c: mov             x1, x3
    //     0xaefb90: stur            x2, [fp, #-0x10]
    //     0xaefb94: stur            x3, [fp, #-0x18]
    // 0xaefb98: CheckStackOverflow
    //     0xaefb98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaefb9c: cmp             SP, x16
    //     0xaefba0: b.ls            #0xaf09e4
    // 0xaefba4: r1 = 2
    //     0xaefba4: movz            x1, #0x2
    // 0xaefba8: r0 = AllocateContext()
    //     0xaefba8: bl              #0x16f6108  ; AllocateContextStub
    // 0xaefbac: mov             x4, x0
    // 0xaefbb0: ldur            x3, [fp, #-8]
    // 0xaefbb4: stur            x4, [fp, #-0x38]
    // 0xaefbb8: StoreField: r4->field_f = r3
    //     0xaefbb8: stur            w3, [x4, #0xf]
    // 0xaefbbc: ldur            x5, [fp, #-0x10]
    // 0xaefbc0: LoadField: r0 = r5->field_b
    //     0xaefbc0: ldur            w0, [x5, #0xb]
    // 0xaefbc4: r1 = LoadInt32Instr(r0)
    //     0xaefbc4: sbfx            x1, x0, #1, #0x1f
    // 0xaefbc8: mov             x0, x1
    // 0xaefbcc: ldur            x1, [fp, #-0x18]
    // 0xaefbd0: cmp             x1, x0
    // 0xaefbd4: b.hs            #0xaf09ec
    // 0xaefbd8: LoadField: r2 = r5->field_f
    //     0xaefbd8: ldur            w2, [x5, #0xf]
    // 0xaefbdc: DecompressPointer r2
    //     0xaefbdc: add             x2, x2, HEAP, lsl #32
    // 0xaefbe0: ldur            x6, [fp, #-0x18]
    // 0xaefbe4: r0 = BoxInt64Instr(r6)
    //     0xaefbe4: sbfiz           x0, x6, #1, #0x1f
    //     0xaefbe8: cmp             x6, x0, asr #1
    //     0xaefbec: b.eq            #0xaefbf8
    //     0xaefbf0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaefbf4: stur            x6, [x0, #7]
    // 0xaefbf8: ArrayLoad: r7 = r2[r6]  ; Unknown_4
    //     0xaefbf8: add             x16, x2, x6, lsl #2
    //     0xaefbfc: ldur            w7, [x16, #0xf]
    // 0xaefc00: DecompressPointer r7
    //     0xaefc00: add             x7, x7, HEAP, lsl #32
    // 0xaefc04: stur            x7, [fp, #-0x30]
    // 0xaefc08: LoadField: r1 = r7->field_ab
    //     0xaefc08: ldur            w1, [x7, #0xab]
    // 0xaefc0c: DecompressPointer r1
    //     0xaefc0c: add             x1, x1, HEAP, lsl #32
    // 0xaefc10: cmp             w1, NULL
    // 0xaefc14: b.ne            #0xaefc20
    // 0xaefc18: r1 = Null
    //     0xaefc18: mov             x1, NULL
    // 0xaefc1c: b               #0xaefc34
    // 0xaefc20: LoadField: r2 = r1->field_b
    //     0xaefc20: ldur            w2, [x1, #0xb]
    // 0xaefc24: cbnz            w2, #0xaefc30
    // 0xaefc28: r1 = false
    //     0xaefc28: add             x1, NULL, #0x30  ; false
    // 0xaefc2c: b               #0xaefc34
    // 0xaefc30: r1 = true
    //     0xaefc30: add             x1, NULL, #0x20  ; true
    // 0xaefc34: cmp             w1, NULL
    // 0xaefc38: b.ne            #0xaefc44
    // 0xaefc3c: r8 = false
    //     0xaefc3c: add             x8, NULL, #0x30  ; false
    // 0xaefc40: b               #0xaefc48
    // 0xaefc44: mov             x8, x1
    // 0xaefc48: stur            x8, [fp, #-0x28]
    // 0xaefc4c: LoadField: r9 = r3->field_1f
    //     0xaefc4c: ldur            w9, [x3, #0x1f]
    // 0xaefc50: DecompressPointer r9
    //     0xaefc50: add             x9, x9, HEAP, lsl #32
    // 0xaefc54: mov             x1, x9
    // 0xaefc58: mov             x2, x0
    // 0xaefc5c: stur            x9, [fp, #-0x20]
    // 0xaefc60: r0 = _getValueOrData()
    //     0xaefc60: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xaefc64: mov             x1, x0
    // 0xaefc68: ldur            x0, [fp, #-0x20]
    // 0xaefc6c: LoadField: r2 = r0->field_f
    //     0xaefc6c: ldur            w2, [x0, #0xf]
    // 0xaefc70: DecompressPointer r2
    //     0xaefc70: add             x2, x2, HEAP, lsl #32
    // 0xaefc74: cmp             w2, w1
    // 0xaefc78: b.ne            #0xaefc84
    // 0xaefc7c: r0 = Null
    //     0xaefc7c: mov             x0, NULL
    // 0xaefc80: b               #0xaefc88
    // 0xaefc84: mov             x0, x1
    // 0xaefc88: cmp             w0, NULL
    // 0xaefc8c: b.ne            #0xaefccc
    // 0xaefc90: r1 = <bool>
    //     0xaefc90: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xaefc94: r0 = RxBool()
    //     0xaefc94: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xaefc98: mov             x2, x0
    // 0xaefc9c: r0 = Sentinel
    //     0xaefc9c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaefca0: stur            x2, [fp, #-0x20]
    // 0xaefca4: StoreField: r2->field_13 = r0
    //     0xaefca4: stur            w0, [x2, #0x13]
    // 0xaefca8: r0 = true
    //     0xaefca8: add             x0, NULL, #0x20  ; true
    // 0xaefcac: ArrayStore: r2[0] = r0  ; List_4
    //     0xaefcac: stur            w0, [x2, #0x17]
    // 0xaefcb0: mov             x1, x2
    // 0xaefcb4: r0 = RxNotifier()
    //     0xaefcb4: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xaefcb8: ldur            x0, [fp, #-0x20]
    // 0xaefcbc: r1 = false
    //     0xaefcbc: add             x1, NULL, #0x30  ; false
    // 0xaefcc0: StoreField: r0->field_13 = r1
    //     0xaefcc0: stur            w1, [x0, #0x13]
    // 0xaefcc4: mov             x4, x0
    // 0xaefcc8: b               #0xaefcd4
    // 0xaefccc: r1 = false
    //     0xaefccc: add             x1, NULL, #0x30  ; false
    // 0xaefcd0: mov             x4, x0
    // 0xaefcd4: ldur            x2, [fp, #-0x38]
    // 0xaefcd8: ldur            x3, [fp, #-0x28]
    // 0xaefcdc: mov             x0, x4
    // 0xaefce0: stur            x4, [fp, #-0x20]
    // 0xaefce4: StoreField: r2->field_13 = r0
    //     0xaefce4: stur            w0, [x2, #0x13]
    //     0xaefce8: ldurb           w16, [x2, #-1]
    //     0xaefcec: ldurb           w17, [x0, #-1]
    //     0xaefcf0: and             x16, x17, x16, lsr #2
    //     0xaefcf4: tst             x16, HEAP, lsr #32
    //     0xaefcf8: b.eq            #0xaefd00
    //     0xaefcfc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xaefd00: r0 = Radius()
    //     0xaefd00: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaefd04: d0 = 12.000000
    //     0xaefd04: fmov            d0, #12.00000000
    // 0xaefd08: stur            x0, [fp, #-0x40]
    // 0xaefd0c: StoreField: r0->field_7 = d0
    //     0xaefd0c: stur            d0, [x0, #7]
    // 0xaefd10: StoreField: r0->field_f = d0
    //     0xaefd10: stur            d0, [x0, #0xf]
    // 0xaefd14: r0 = BorderRadius()
    //     0xaefd14: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaefd18: mov             x1, x0
    // 0xaefd1c: ldur            x0, [fp, #-0x40]
    // 0xaefd20: stur            x1, [fp, #-0x48]
    // 0xaefd24: StoreField: r1->field_7 = r0
    //     0xaefd24: stur            w0, [x1, #7]
    // 0xaefd28: StoreField: r1->field_b = r0
    //     0xaefd28: stur            w0, [x1, #0xb]
    // 0xaefd2c: StoreField: r1->field_f = r0
    //     0xaefd2c: stur            w0, [x1, #0xf]
    // 0xaefd30: StoreField: r1->field_13 = r0
    //     0xaefd30: stur            w0, [x1, #0x13]
    // 0xaefd34: r0 = RoundedRectangleBorder()
    //     0xaefd34: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaefd38: mov             x3, x0
    // 0xaefd3c: ldur            x0, [fp, #-0x48]
    // 0xaefd40: stur            x3, [fp, #-0x40]
    // 0xaefd44: StoreField: r3->field_b = r0
    //     0xaefd44: stur            w0, [x3, #0xb]
    // 0xaefd48: r0 = Instance_BorderSide
    //     0xaefd48: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaefd4c: ldr             x0, [x0, #0xe20]
    // 0xaefd50: StoreField: r3->field_7 = r0
    //     0xaefd50: stur            w0, [x3, #7]
    // 0xaefd54: r1 = <Widget>
    //     0xaefd54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaefd58: r2 = 0
    //     0xaefd58: movz            x2, #0
    // 0xaefd5c: r0 = _GrowableList()
    //     0xaefd5c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaefd60: mov             x3, x0
    // 0xaefd64: ldur            x0, [fp, #-0x28]
    // 0xaefd68: stur            x3, [fp, #-0x48]
    // 0xaefd6c: tbnz            w0, #4, #0xaeff30
    // 0xaefd70: ldur            x4, [fp, #-0x10]
    // 0xaefd74: ldur            x5, [fp, #-0x18]
    // 0xaefd78: LoadField: r0 = r4->field_b
    //     0xaefd78: ldur            w0, [x4, #0xb]
    // 0xaefd7c: r1 = LoadInt32Instr(r0)
    //     0xaefd7c: sbfx            x1, x0, #1, #0x1f
    // 0xaefd80: mov             x0, x1
    // 0xaefd84: mov             x1, x5
    // 0xaefd88: cmp             x1, x0
    // 0xaefd8c: b.hs            #0xaf09f0
    // 0xaefd90: LoadField: r0 = r4->field_f
    //     0xaefd90: ldur            w0, [x4, #0xf]
    // 0xaefd94: DecompressPointer r0
    //     0xaefd94: add             x0, x0, HEAP, lsl #32
    // 0xaefd98: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaefd98: add             x16, x0, x5, lsl #2
    //     0xaefd9c: ldur            w1, [x16, #0xf]
    // 0xaefda0: DecompressPointer r1
    //     0xaefda0: add             x1, x1, HEAP, lsl #32
    // 0xaefda4: LoadField: r2 = r1->field_ab
    //     0xaefda4: ldur            w2, [x1, #0xab]
    // 0xaefda8: DecompressPointer r2
    //     0xaefda8: add             x2, x2, HEAP, lsl #32
    // 0xaefdac: cmp             w2, NULL
    // 0xaefdb0: b.ne            #0xaefdbc
    // 0xaefdb4: r0 = Null
    //     0xaefdb4: mov             x0, NULL
    // 0xaefdb8: b               #0xaefe08
    // 0xaefdbc: LoadField: r0 = r2->field_b
    //     0xaefdbc: ldur            w0, [x2, #0xb]
    // 0xaefdc0: r1 = LoadInt32Instr(r0)
    //     0xaefdc0: sbfx            x1, x0, #1, #0x1f
    // 0xaefdc4: mov             x0, x1
    // 0xaefdc8: r1 = 0
    //     0xaefdc8: movz            x1, #0
    // 0xaefdcc: cmp             x1, x0
    // 0xaefdd0: b.hs            #0xaf09f4
    // 0xaefdd4: LoadField: r0 = r2->field_f
    //     0xaefdd4: ldur            w0, [x2, #0xf]
    // 0xaefdd8: DecompressPointer r0
    //     0xaefdd8: add             x0, x0, HEAP, lsl #32
    // 0xaefddc: LoadField: r1 = r0->field_f
    //     0xaefddc: ldur            w1, [x0, #0xf]
    // 0xaefde0: DecompressPointer r1
    //     0xaefde0: add             x1, x1, HEAP, lsl #32
    // 0xaefde4: LoadField: r0 = r1->field_7
    //     0xaefde4: ldur            w0, [x1, #7]
    // 0xaefde8: DecompressPointer r0
    //     0xaefde8: add             x0, x0, HEAP, lsl #32
    // 0xaefdec: cmp             w0, NULL
    // 0xaefdf0: b.ne            #0xaefdfc
    // 0xaefdf4: r0 = Null
    //     0xaefdf4: mov             x0, NULL
    // 0xaefdf8: b               #0xaefe08
    // 0xaefdfc: LoadField: r1 = r0->field_b
    //     0xaefdfc: ldur            w1, [x0, #0xb]
    // 0xaefe00: DecompressPointer r1
    //     0xaefe00: add             x1, x1, HEAP, lsl #32
    // 0xaefe04: mov             x0, x1
    // 0xaefe08: cmp             w0, NULL
    // 0xaefe0c: b.ne            #0xaefe14
    // 0xaefe10: r0 = ""
    //     0xaefe10: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaefe14: stur            x0, [fp, #-0x28]
    // 0xaefe18: r1 = Function '<anonymous closure>':.
    //     0xaefe18: add             x1, PP, #0x58, lsl #12  ; [pp+0x58130] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaefe1c: ldr             x1, [x1, #0x130]
    // 0xaefe20: r2 = Null
    //     0xaefe20: mov             x2, NULL
    // 0xaefe24: r0 = AllocateClosure()
    //     0xaefe24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaefe28: r1 = Function '<anonymous closure>':.
    //     0xaefe28: add             x1, PP, #0x58, lsl #12  ; [pp+0x58138] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaefe2c: ldr             x1, [x1, #0x138]
    // 0xaefe30: r2 = Null
    //     0xaefe30: mov             x2, NULL
    // 0xaefe34: stur            x0, [fp, #-0x50]
    // 0xaefe38: r0 = AllocateClosure()
    //     0xaefe38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaefe3c: stur            x0, [fp, #-0x58]
    // 0xaefe40: r0 = CachedNetworkImage()
    //     0xaefe40: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaefe44: stur            x0, [fp, #-0x60]
    // 0xaefe48: ldur            x16, [fp, #-0x50]
    // 0xaefe4c: ldur            lr, [fp, #-0x58]
    // 0xaefe50: stp             lr, x16, [SP, #0x18]
    // 0xaefe54: r16 = inf
    //     0xaefe54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xaefe58: ldr             x16, [x16, #0x9f8]
    // 0xaefe5c: r30 = 336.000000
    //     0xaefe5c: add             lr, PP, #0x55, lsl #12  ; [pp+0x55530] 336
    //     0xaefe60: ldr             lr, [lr, #0x530]
    // 0xaefe64: stp             lr, x16, [SP, #8]
    // 0xaefe68: r16 = Instance_BoxFit
    //     0xaefe68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xaefe6c: ldr             x16, [x16, #0x118]
    // 0xaefe70: str             x16, [SP]
    // 0xaefe74: mov             x1, x0
    // 0xaefe78: ldur            x2, [fp, #-0x28]
    // 0xaefe7c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x3, fit, 0x6, height, 0x5, progressIndicatorBuilder, 0x2, width, 0x4, null]
    //     0xaefe7c: add             x4, PP, #0x55, lsl #12  ; [pp+0x55538] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x3, "fit", 0x6, "height", 0x5, "progressIndicatorBuilder", 0x2, "width", 0x4, Null]
    //     0xaefe80: ldr             x4, [x4, #0x538]
    // 0xaefe84: r0 = CachedNetworkImage()
    //     0xaefe84: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaefe88: r0 = ClipRRect()
    //     0xaefe88: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xaefe8c: mov             x2, x0
    // 0xaefe90: r0 = Instance_BorderRadius
    //     0xaefe90: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xaefe94: ldr             x0, [x0, #0xe10]
    // 0xaefe98: stur            x2, [fp, #-0x28]
    // 0xaefe9c: StoreField: r2->field_f = r0
    //     0xaefe9c: stur            w0, [x2, #0xf]
    // 0xaefea0: r0 = Instance_Clip
    //     0xaefea0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xaefea4: ldr             x0, [x0, #0x138]
    // 0xaefea8: ArrayStore: r2[0] = r0  ; List_4
    //     0xaefea8: stur            w0, [x2, #0x17]
    // 0xaefeac: ldur            x0, [fp, #-0x60]
    // 0xaefeb0: StoreField: r2->field_b = r0
    //     0xaefeb0: stur            w0, [x2, #0xb]
    // 0xaefeb4: ldur            x0, [fp, #-0x48]
    // 0xaefeb8: LoadField: r1 = r0->field_b
    //     0xaefeb8: ldur            w1, [x0, #0xb]
    // 0xaefebc: LoadField: r3 = r0->field_f
    //     0xaefebc: ldur            w3, [x0, #0xf]
    // 0xaefec0: DecompressPointer r3
    //     0xaefec0: add             x3, x3, HEAP, lsl #32
    // 0xaefec4: LoadField: r4 = r3->field_b
    //     0xaefec4: ldur            w4, [x3, #0xb]
    // 0xaefec8: r3 = LoadInt32Instr(r1)
    //     0xaefec8: sbfx            x3, x1, #1, #0x1f
    // 0xaefecc: stur            x3, [fp, #-0x68]
    // 0xaefed0: r1 = LoadInt32Instr(r4)
    //     0xaefed0: sbfx            x1, x4, #1, #0x1f
    // 0xaefed4: cmp             x3, x1
    // 0xaefed8: b.ne            #0xaefee4
    // 0xaefedc: mov             x1, x0
    // 0xaefee0: r0 = _growToNextCapacity()
    //     0xaefee0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaefee4: ldur            x2, [fp, #-0x48]
    // 0xaefee8: ldur            x3, [fp, #-0x68]
    // 0xaefeec: add             x0, x3, #1
    // 0xaefef0: lsl             x1, x0, #1
    // 0xaefef4: StoreField: r2->field_b = r1
    //     0xaefef4: stur            w1, [x2, #0xb]
    // 0xaefef8: LoadField: r1 = r2->field_f
    //     0xaefef8: ldur            w1, [x2, #0xf]
    // 0xaefefc: DecompressPointer r1
    //     0xaefefc: add             x1, x1, HEAP, lsl #32
    // 0xaeff00: ldur            x0, [fp, #-0x28]
    // 0xaeff04: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaeff04: add             x25, x1, x3, lsl #2
    //     0xaeff08: add             x25, x25, #0xf
    //     0xaeff0c: str             w0, [x25]
    //     0xaeff10: tbz             w0, #0, #0xaeff2c
    //     0xaeff14: ldurb           w16, [x1, #-1]
    //     0xaeff18: ldurb           w17, [x0, #-1]
    //     0xaeff1c: and             x16, x17, x16, lsr #2
    //     0xaeff20: tst             x16, HEAP, lsr #32
    //     0xaeff24: b.eq            #0xaeff2c
    //     0xaeff28: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaeff2c: b               #0xaeffc0
    // 0xaeff30: mov             x2, x3
    // 0xaeff34: r0 = Container()
    //     0xaeff34: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaeff38: mov             x1, x0
    // 0xaeff3c: stur            x0, [fp, #-0x28]
    // 0xaeff40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaeff40: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaeff44: r0 = Container()
    //     0xaeff44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaeff48: ldur            x0, [fp, #-0x48]
    // 0xaeff4c: LoadField: r1 = r0->field_b
    //     0xaeff4c: ldur            w1, [x0, #0xb]
    // 0xaeff50: LoadField: r2 = r0->field_f
    //     0xaeff50: ldur            w2, [x0, #0xf]
    // 0xaeff54: DecompressPointer r2
    //     0xaeff54: add             x2, x2, HEAP, lsl #32
    // 0xaeff58: LoadField: r3 = r2->field_b
    //     0xaeff58: ldur            w3, [x2, #0xb]
    // 0xaeff5c: r2 = LoadInt32Instr(r1)
    //     0xaeff5c: sbfx            x2, x1, #1, #0x1f
    // 0xaeff60: stur            x2, [fp, #-0x68]
    // 0xaeff64: r1 = LoadInt32Instr(r3)
    //     0xaeff64: sbfx            x1, x3, #1, #0x1f
    // 0xaeff68: cmp             x2, x1
    // 0xaeff6c: b.ne            #0xaeff78
    // 0xaeff70: mov             x1, x0
    // 0xaeff74: r0 = _growToNextCapacity()
    //     0xaeff74: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaeff78: ldur            x2, [fp, #-0x48]
    // 0xaeff7c: ldur            x3, [fp, #-0x68]
    // 0xaeff80: add             x0, x3, #1
    // 0xaeff84: lsl             x1, x0, #1
    // 0xaeff88: StoreField: r2->field_b = r1
    //     0xaeff88: stur            w1, [x2, #0xb]
    // 0xaeff8c: LoadField: r1 = r2->field_f
    //     0xaeff8c: ldur            w1, [x2, #0xf]
    // 0xaeff90: DecompressPointer r1
    //     0xaeff90: add             x1, x1, HEAP, lsl #32
    // 0xaeff94: ldur            x0, [fp, #-0x28]
    // 0xaeff98: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaeff98: add             x25, x1, x3, lsl #2
    //     0xaeff9c: add             x25, x25, #0xf
    //     0xaeffa0: str             w0, [x25]
    //     0xaeffa4: tbz             w0, #0, #0xaeffc0
    //     0xaeffa8: ldurb           w16, [x1, #-1]
    //     0xaeffac: ldurb           w17, [x0, #-1]
    //     0xaeffb0: and             x16, x17, x16, lsr #2
    //     0xaeffb4: tst             x16, HEAP, lsr #32
    //     0xaeffb8: b.eq            #0xaeffc0
    //     0xaeffbc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaeffc0: ldur            x3, [fp, #-0x10]
    // 0xaeffc4: ldur            x4, [fp, #-0x18]
    // 0xaeffc8: LoadField: r0 = r3->field_b
    //     0xaeffc8: ldur            w0, [x3, #0xb]
    // 0xaeffcc: r1 = LoadInt32Instr(r0)
    //     0xaeffcc: sbfx            x1, x0, #1, #0x1f
    // 0xaeffd0: mov             x0, x1
    // 0xaeffd4: mov             x1, x4
    // 0xaeffd8: cmp             x1, x0
    // 0xaeffdc: b.hs            #0xaf09f8
    // 0xaeffe0: LoadField: r0 = r3->field_f
    //     0xaeffe0: ldur            w0, [x3, #0xf]
    // 0xaeffe4: DecompressPointer r0
    //     0xaeffe4: add             x0, x0, HEAP, lsl #32
    // 0xaeffe8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xaeffe8: add             x16, x0, x4, lsl #2
    //     0xaeffec: ldur            w1, [x16, #0xf]
    // 0xaefff0: DecompressPointer r1
    //     0xaefff0: add             x1, x1, HEAP, lsl #32
    // 0xaefff4: LoadField: r0 = r1->field_97
    //     0xaefff4: ldur            w0, [x1, #0x97]
    // 0xaefff8: DecompressPointer r0
    //     0xaefff8: add             x0, x0, HEAP, lsl #32
    // 0xaefffc: cmp             w0, NULL
    // 0xaf0000: b.ne            #0xaf000c
    // 0xaf0004: r1 = ""
    //     0xaf0004: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf0008: b               #0xaf0010
    // 0xaf000c: mov             x1, x0
    // 0xaf0010: ldur            x0, [fp, #-8]
    // 0xaf0014: r0 = capitalizeFirstWord()
    //     0xaf0014: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaf0018: mov             x2, x0
    // 0xaf001c: ldur            x0, [fp, #-8]
    // 0xaf0020: stur            x2, [fp, #-0x28]
    // 0xaf0024: LoadField: r1 = r0->field_f
    //     0xaf0024: ldur            w1, [x0, #0xf]
    // 0xaf0028: DecompressPointer r1
    //     0xaf0028: add             x1, x1, HEAP, lsl #32
    // 0xaf002c: cmp             w1, NULL
    // 0xaf0030: b.eq            #0xaf09fc
    // 0xaf0034: r0 = of()
    //     0xaf0034: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf0038: LoadField: r1 = r0->field_87
    //     0xaf0038: ldur            w1, [x0, #0x87]
    // 0xaf003c: DecompressPointer r1
    //     0xaf003c: add             x1, x1, HEAP, lsl #32
    // 0xaf0040: LoadField: r0 = r1->field_7
    //     0xaf0040: ldur            w0, [x1, #7]
    // 0xaf0044: DecompressPointer r0
    //     0xaf0044: add             x0, x0, HEAP, lsl #32
    // 0xaf0048: r16 = 14.000000
    //     0xaf0048: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf004c: ldr             x16, [x16, #0x1d8]
    // 0xaf0050: r30 = Instance_Color
    //     0xaf0050: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf0054: stp             lr, x16, [SP]
    // 0xaf0058: mov             x1, x0
    // 0xaf005c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf005c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf0060: ldr             x4, [x4, #0xaa0]
    // 0xaf0064: r0 = copyWith()
    //     0xaf0064: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf0068: stur            x0, [fp, #-0x50]
    // 0xaf006c: r0 = Text()
    //     0xaf006c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf0070: mov             x1, x0
    // 0xaf0074: ldur            x0, [fp, #-0x28]
    // 0xaf0078: stur            x1, [fp, #-0x58]
    // 0xaf007c: StoreField: r1->field_b = r0
    //     0xaf007c: stur            w0, [x1, #0xb]
    // 0xaf0080: ldur            x0, [fp, #-0x50]
    // 0xaf0084: StoreField: r1->field_13 = r0
    //     0xaf0084: stur            w0, [x1, #0x13]
    // 0xaf0088: r0 = Padding()
    //     0xaf0088: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf008c: mov             x2, x0
    // 0xaf0090: r0 = Instance_EdgeInsets
    //     0xaf0090: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xaf0094: ldr             x0, [x0, #0xa98]
    // 0xaf0098: stur            x2, [fp, #-0x28]
    // 0xaf009c: StoreField: r2->field_f = r0
    //     0xaf009c: stur            w0, [x2, #0xf]
    // 0xaf00a0: ldur            x0, [fp, #-0x58]
    // 0xaf00a4: StoreField: r2->field_b = r0
    //     0xaf00a4: stur            w0, [x2, #0xb]
    // 0xaf00a8: ldur            x0, [fp, #-0x48]
    // 0xaf00ac: LoadField: r1 = r0->field_b
    //     0xaf00ac: ldur            w1, [x0, #0xb]
    // 0xaf00b0: LoadField: r3 = r0->field_f
    //     0xaf00b0: ldur            w3, [x0, #0xf]
    // 0xaf00b4: DecompressPointer r3
    //     0xaf00b4: add             x3, x3, HEAP, lsl #32
    // 0xaf00b8: LoadField: r4 = r3->field_b
    //     0xaf00b8: ldur            w4, [x3, #0xb]
    // 0xaf00bc: r3 = LoadInt32Instr(r1)
    //     0xaf00bc: sbfx            x3, x1, #1, #0x1f
    // 0xaf00c0: stur            x3, [fp, #-0x68]
    // 0xaf00c4: r1 = LoadInt32Instr(r4)
    //     0xaf00c4: sbfx            x1, x4, #1, #0x1f
    // 0xaf00c8: cmp             x3, x1
    // 0xaf00cc: b.ne            #0xaf00d8
    // 0xaf00d0: mov             x1, x0
    // 0xaf00d4: r0 = _growToNextCapacity()
    //     0xaf00d4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf00d8: ldur            x2, [fp, #-0x48]
    // 0xaf00dc: ldur            x3, [fp, #-0x68]
    // 0xaf00e0: add             x4, x3, #1
    // 0xaf00e4: stur            x4, [fp, #-0x70]
    // 0xaf00e8: lsl             x0, x4, #1
    // 0xaf00ec: StoreField: r2->field_b = r0
    //     0xaf00ec: stur            w0, [x2, #0xb]
    // 0xaf00f0: LoadField: r5 = r2->field_f
    //     0xaf00f0: ldur            w5, [x2, #0xf]
    // 0xaf00f4: DecompressPointer r5
    //     0xaf00f4: add             x5, x5, HEAP, lsl #32
    // 0xaf00f8: mov             x1, x5
    // 0xaf00fc: ldur            x0, [fp, #-0x28]
    // 0xaf0100: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf0100: add             x25, x1, x3, lsl #2
    //     0xaf0104: add             x25, x25, #0xf
    //     0xaf0108: str             w0, [x25]
    //     0xaf010c: tbz             w0, #0, #0xaf0128
    //     0xaf0110: ldurb           w16, [x1, #-1]
    //     0xaf0114: ldurb           w17, [x0, #-1]
    //     0xaf0118: and             x16, x17, x16, lsr #2
    //     0xaf011c: tst             x16, HEAP, lsr #32
    //     0xaf0120: b.eq            #0xaf0128
    //     0xaf0124: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf0128: LoadField: r0 = r5->field_b
    //     0xaf0128: ldur            w0, [x5, #0xb]
    // 0xaf012c: r1 = LoadInt32Instr(r0)
    //     0xaf012c: sbfx            x1, x0, #1, #0x1f
    // 0xaf0130: cmp             x4, x1
    // 0xaf0134: b.ne            #0xaf0140
    // 0xaf0138: mov             x1, x2
    // 0xaf013c: r0 = _growToNextCapacity()
    //     0xaf013c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf0140: ldur            x3, [fp, #-0x10]
    // 0xaf0144: ldur            x4, [fp, #-0x18]
    // 0xaf0148: ldur            x2, [fp, #-0x48]
    // 0xaf014c: ldur            x0, [fp, #-0x70]
    // 0xaf0150: add             x1, x0, #1
    // 0xaf0154: lsl             x5, x1, #1
    // 0xaf0158: StoreField: r2->field_b = r5
    //     0xaf0158: stur            w5, [x2, #0xb]
    // 0xaf015c: LoadField: r1 = r2->field_f
    //     0xaf015c: ldur            w1, [x2, #0xf]
    // 0xaf0160: DecompressPointer r1
    //     0xaf0160: add             x1, x1, HEAP, lsl #32
    // 0xaf0164: add             x5, x1, x0, lsl #2
    // 0xaf0168: r16 = Instance_SizedBox
    //     0xaf0168: add             x16, PP, #0x55, lsl #12  ; [pp+0x55540] Obj!SizedBox@d67fe1
    //     0xaf016c: ldr             x16, [x16, #0x540]
    // 0xaf0170: StoreField: r5->field_f = r16
    //     0xaf0170: stur            w16, [x5, #0xf]
    // 0xaf0174: LoadField: r0 = r3->field_b
    //     0xaf0174: ldur            w0, [x3, #0xb]
    // 0xaf0178: r1 = LoadInt32Instr(r0)
    //     0xaf0178: sbfx            x1, x0, #1, #0x1f
    // 0xaf017c: mov             x0, x1
    // 0xaf0180: mov             x1, x4
    // 0xaf0184: cmp             x1, x0
    // 0xaf0188: b.hs            #0xaf0a00
    // 0xaf018c: LoadField: r0 = r3->field_f
    //     0xaf018c: ldur            w0, [x3, #0xf]
    // 0xaf0190: DecompressPointer r0
    //     0xaf0190: add             x0, x0, HEAP, lsl #32
    // 0xaf0194: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xaf0194: add             x16, x0, x4, lsl #2
    //     0xaf0198: ldur            w1, [x16, #0xf]
    // 0xaf019c: DecompressPointer r1
    //     0xaf019c: add             x1, x1, HEAP, lsl #32
    // 0xaf01a0: LoadField: r0 = r1->field_9b
    //     0xaf01a0: ldur            w0, [x1, #0x9b]
    // 0xaf01a4: DecompressPointer r0
    //     0xaf01a4: add             x0, x0, HEAP, lsl #32
    // 0xaf01a8: cmp             w0, NULL
    // 0xaf01ac: b.ne            #0xaf01b8
    // 0xaf01b0: r1 = ""
    //     0xaf01b0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf01b4: b               #0xaf01bc
    // 0xaf01b8: mov             x1, x0
    // 0xaf01bc: r0 = parse()
    //     0xaf01bc: bl              #0x64333c  ; [dart:core] double::parse
    // 0xaf01c0: ldur            x2, [fp, #-0x10]
    // 0xaf01c4: stur            d0, [fp, #-0x78]
    // 0xaf01c8: LoadField: r0 = r2->field_b
    //     0xaf01c8: ldur            w0, [x2, #0xb]
    // 0xaf01cc: r1 = LoadInt32Instr(r0)
    //     0xaf01cc: sbfx            x1, x0, #1, #0x1f
    // 0xaf01d0: mov             x0, x1
    // 0xaf01d4: ldur            x1, [fp, #-0x18]
    // 0xaf01d8: cmp             x1, x0
    // 0xaf01dc: b.hs            #0xaf0a04
    // 0xaf01e0: LoadField: r0 = r2->field_f
    //     0xaf01e0: ldur            w0, [x2, #0xf]
    // 0xaf01e4: DecompressPointer r0
    //     0xaf01e4: add             x0, x0, HEAP, lsl #32
    // 0xaf01e8: ldur            x3, [fp, #-0x18]
    // 0xaf01ec: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xaf01ec: add             x16, x0, x3, lsl #2
    //     0xaf01f0: ldur            w1, [x16, #0xf]
    // 0xaf01f4: DecompressPointer r1
    //     0xaf01f4: add             x1, x1, HEAP, lsl #32
    // 0xaf01f8: LoadField: r0 = r1->field_9b
    //     0xaf01f8: ldur            w0, [x1, #0x9b]
    // 0xaf01fc: DecompressPointer r0
    //     0xaf01fc: add             x0, x0, HEAP, lsl #32
    // 0xaf0200: cmp             w0, NULL
    // 0xaf0204: b.ne            #0xaf0210
    // 0xaf0208: r1 = ""
    //     0xaf0208: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf020c: b               #0xaf0214
    // 0xaf0210: mov             x1, x0
    // 0xaf0214: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf0214: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf0218: r0 = parse()
    //     0xaf0218: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xaf021c: stur            x0, [fp, #-0x68]
    // 0xaf0220: r0 = RatingWidget()
    //     0xaf0220: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xaf0224: mov             x3, x0
    // 0xaf0228: r0 = Instance_Icon
    //     0xaf0228: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xaf022c: ldr             x0, [x0, #0x190]
    // 0xaf0230: stur            x3, [fp, #-0x28]
    // 0xaf0234: StoreField: r3->field_7 = r0
    //     0xaf0234: stur            w0, [x3, #7]
    // 0xaf0238: r0 = Instance_Icon
    //     0xaf0238: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xaf023c: ldr             x0, [x0, #0x198]
    // 0xaf0240: StoreField: r3->field_b = r0
    //     0xaf0240: stur            w0, [x3, #0xb]
    // 0xaf0244: r0 = Instance_Icon
    //     0xaf0244: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xaf0248: ldr             x0, [x0, #0x1a0]
    // 0xaf024c: StoreField: r3->field_f = r0
    //     0xaf024c: stur            w0, [x3, #0xf]
    // 0xaf0250: r1 = Function '<anonymous closure>':.
    //     0xaf0250: add             x1, PP, #0x58, lsl #12  ; [pp+0x58140] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaf0254: ldr             x1, [x1, #0x140]
    // 0xaf0258: r2 = Null
    //     0xaf0258: mov             x2, NULL
    // 0xaf025c: r0 = AllocateClosure()
    //     0xaf025c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf0260: stur            x0, [fp, #-0x50]
    // 0xaf0264: r0 = RatingBar()
    //     0xaf0264: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xaf0268: mov             x2, x0
    // 0xaf026c: ldur            x0, [fp, #-0x50]
    // 0xaf0270: stur            x2, [fp, #-0x58]
    // 0xaf0274: StoreField: r2->field_b = r0
    //     0xaf0274: stur            w0, [x2, #0xb]
    // 0xaf0278: r3 = true
    //     0xaf0278: add             x3, NULL, #0x20  ; true
    // 0xaf027c: StoreField: r2->field_1f = r3
    //     0xaf027c: stur            w3, [x2, #0x1f]
    // 0xaf0280: r4 = Instance_Axis
    //     0xaf0280: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf0284: StoreField: r2->field_23 = r4
    //     0xaf0284: stur            w4, [x2, #0x23]
    // 0xaf0288: StoreField: r2->field_27 = r3
    //     0xaf0288: stur            w3, [x2, #0x27]
    // 0xaf028c: d0 = 2.000000
    //     0xaf028c: fmov            d0, #2.00000000
    // 0xaf0290: StoreField: r2->field_2b = d0
    //     0xaf0290: stur            d0, [x2, #0x2b]
    // 0xaf0294: StoreField: r2->field_33 = r3
    //     0xaf0294: stur            w3, [x2, #0x33]
    // 0xaf0298: ldur            d0, [fp, #-0x78]
    // 0xaf029c: StoreField: r2->field_37 = d0
    //     0xaf029c: stur            d0, [x2, #0x37]
    // 0xaf02a0: ldur            x0, [fp, #-0x68]
    // 0xaf02a4: StoreField: r2->field_3f = r0
    //     0xaf02a4: stur            x0, [x2, #0x3f]
    // 0xaf02a8: r0 = Instance_EdgeInsets
    //     0xaf02a8: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xaf02ac: StoreField: r2->field_47 = r0
    //     0xaf02ac: stur            w0, [x2, #0x47]
    // 0xaf02b0: d0 = 18.000000
    //     0xaf02b0: fmov            d0, #18.00000000
    // 0xaf02b4: StoreField: r2->field_4b = d0
    //     0xaf02b4: stur            d0, [x2, #0x4b]
    // 0xaf02b8: StoreField: r2->field_53 = rZR
    //     0xaf02b8: stur            xzr, [x2, #0x53]
    // 0xaf02bc: r0 = false
    //     0xaf02bc: add             x0, NULL, #0x30  ; false
    // 0xaf02c0: StoreField: r2->field_5b = r0
    //     0xaf02c0: stur            w0, [x2, #0x5b]
    // 0xaf02c4: StoreField: r2->field_5f = r0
    //     0xaf02c4: stur            w0, [x2, #0x5f]
    // 0xaf02c8: ldur            x0, [fp, #-0x28]
    // 0xaf02cc: StoreField: r2->field_67 = r0
    //     0xaf02cc: stur            w0, [x2, #0x67]
    // 0xaf02d0: ldur            x5, [fp, #-0x10]
    // 0xaf02d4: LoadField: r0 = r5->field_b
    //     0xaf02d4: ldur            w0, [x5, #0xb]
    // 0xaf02d8: r1 = LoadInt32Instr(r0)
    //     0xaf02d8: sbfx            x1, x0, #1, #0x1f
    // 0xaf02dc: mov             x0, x1
    // 0xaf02e0: ldur            x1, [fp, #-0x18]
    // 0xaf02e4: cmp             x1, x0
    // 0xaf02e8: b.hs            #0xaf0a08
    // 0xaf02ec: LoadField: r0 = r5->field_f
    //     0xaf02ec: ldur            w0, [x5, #0xf]
    // 0xaf02f0: DecompressPointer r0
    //     0xaf02f0: add             x0, x0, HEAP, lsl #32
    // 0xaf02f4: ldur            x1, [fp, #-0x18]
    // 0xaf02f8: ArrayLoad: r5 = r0[r1]  ; Unknown_4
    //     0xaf02f8: add             x16, x0, x1, lsl #2
    //     0xaf02fc: ldur            w5, [x16, #0xf]
    // 0xaf0300: DecompressPointer r5
    //     0xaf0300: add             x5, x5, HEAP, lsl #32
    // 0xaf0304: LoadField: r0 = r5->field_a7
    //     0xaf0304: ldur            w0, [x5, #0xa7]
    // 0xaf0308: DecompressPointer r0
    //     0xaf0308: add             x0, x0, HEAP, lsl #32
    // 0xaf030c: cmp             w0, NULL
    // 0xaf0310: b.ne            #0xaf031c
    // 0xaf0314: r6 = ""
    //     0xaf0314: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf0318: b               #0xaf0320
    // 0xaf031c: mov             x6, x0
    // 0xaf0320: ldur            x5, [fp, #-8]
    // 0xaf0324: ldur            x0, [fp, #-0x48]
    // 0xaf0328: stur            x6, [fp, #-0x10]
    // 0xaf032c: LoadField: r1 = r5->field_f
    //     0xaf032c: ldur            w1, [x5, #0xf]
    // 0xaf0330: DecompressPointer r1
    //     0xaf0330: add             x1, x1, HEAP, lsl #32
    // 0xaf0334: cmp             w1, NULL
    // 0xaf0338: b.eq            #0xaf0a0c
    // 0xaf033c: r0 = of()
    //     0xaf033c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf0340: LoadField: r1 = r0->field_87
    //     0xaf0340: ldur            w1, [x0, #0x87]
    // 0xaf0344: DecompressPointer r1
    //     0xaf0344: add             x1, x1, HEAP, lsl #32
    // 0xaf0348: LoadField: r0 = r1->field_2b
    //     0xaf0348: ldur            w0, [x1, #0x2b]
    // 0xaf034c: DecompressPointer r0
    //     0xaf034c: add             x0, x0, HEAP, lsl #32
    // 0xaf0350: stur            x0, [fp, #-0x28]
    // 0xaf0354: r1 = Instance_Color
    //     0xaf0354: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf0358: d0 = 0.700000
    //     0xaf0358: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf035c: ldr             d0, [x17, #0xf48]
    // 0xaf0360: r0 = withOpacity()
    //     0xaf0360: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf0364: r16 = 14.000000
    //     0xaf0364: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf0368: ldr             x16, [x16, #0x1d8]
    // 0xaf036c: stp             x0, x16, [SP]
    // 0xaf0370: ldur            x1, [fp, #-0x28]
    // 0xaf0374: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf0374: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf0378: ldr             x4, [x4, #0xaa0]
    // 0xaf037c: r0 = copyWith()
    //     0xaf037c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf0380: stur            x0, [fp, #-0x28]
    // 0xaf0384: r0 = Text()
    //     0xaf0384: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf0388: mov             x3, x0
    // 0xaf038c: ldur            x0, [fp, #-0x10]
    // 0xaf0390: stur            x3, [fp, #-0x50]
    // 0xaf0394: StoreField: r3->field_b = r0
    //     0xaf0394: stur            w0, [x3, #0xb]
    // 0xaf0398: ldur            x0, [fp, #-0x28]
    // 0xaf039c: StoreField: r3->field_13 = r0
    //     0xaf039c: stur            w0, [x3, #0x13]
    // 0xaf03a0: r1 = Null
    //     0xaf03a0: mov             x1, NULL
    // 0xaf03a4: r2 = 6
    //     0xaf03a4: movz            x2, #0x6
    // 0xaf03a8: r0 = AllocateArray()
    //     0xaf03a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf03ac: mov             x2, x0
    // 0xaf03b0: ldur            x0, [fp, #-0x58]
    // 0xaf03b4: stur            x2, [fp, #-0x10]
    // 0xaf03b8: StoreField: r2->field_f = r0
    //     0xaf03b8: stur            w0, [x2, #0xf]
    // 0xaf03bc: r16 = Instance_SizedBox
    //     0xaf03bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xaf03c0: ldr             x16, [x16, #0x940]
    // 0xaf03c4: StoreField: r2->field_13 = r16
    //     0xaf03c4: stur            w16, [x2, #0x13]
    // 0xaf03c8: ldur            x0, [fp, #-0x50]
    // 0xaf03cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf03cc: stur            w0, [x2, #0x17]
    // 0xaf03d0: r1 = <Widget>
    //     0xaf03d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf03d4: r0 = AllocateGrowableArray()
    //     0xaf03d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf03d8: mov             x1, x0
    // 0xaf03dc: ldur            x0, [fp, #-0x10]
    // 0xaf03e0: stur            x1, [fp, #-0x28]
    // 0xaf03e4: StoreField: r1->field_f = r0
    //     0xaf03e4: stur            w0, [x1, #0xf]
    // 0xaf03e8: r0 = 6
    //     0xaf03e8: movz            x0, #0x6
    // 0xaf03ec: StoreField: r1->field_b = r0
    //     0xaf03ec: stur            w0, [x1, #0xb]
    // 0xaf03f0: r0 = Row()
    //     0xaf03f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf03f4: mov             x1, x0
    // 0xaf03f8: r0 = Instance_Axis
    //     0xaf03f8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf03fc: stur            x1, [fp, #-0x10]
    // 0xaf0400: StoreField: r1->field_f = r0
    //     0xaf0400: stur            w0, [x1, #0xf]
    // 0xaf0404: r0 = Instance_MainAxisAlignment
    //     0xaf0404: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf0408: ldr             x0, [x0, #0xa08]
    // 0xaf040c: StoreField: r1->field_13 = r0
    //     0xaf040c: stur            w0, [x1, #0x13]
    // 0xaf0410: r2 = Instance_MainAxisSize
    //     0xaf0410: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf0414: ldr             x2, [x2, #0xa10]
    // 0xaf0418: ArrayStore: r1[0] = r2  ; List_4
    //     0xaf0418: stur            w2, [x1, #0x17]
    // 0xaf041c: r3 = Instance_CrossAxisAlignment
    //     0xaf041c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf0420: ldr             x3, [x3, #0xa18]
    // 0xaf0424: StoreField: r1->field_1b = r3
    //     0xaf0424: stur            w3, [x1, #0x1b]
    // 0xaf0428: r3 = Instance_VerticalDirection
    //     0xaf0428: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf042c: ldr             x3, [x3, #0xa20]
    // 0xaf0430: StoreField: r1->field_23 = r3
    //     0xaf0430: stur            w3, [x1, #0x23]
    // 0xaf0434: r4 = Instance_Clip
    //     0xaf0434: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf0438: ldr             x4, [x4, #0x38]
    // 0xaf043c: StoreField: r1->field_2b = r4
    //     0xaf043c: stur            w4, [x1, #0x2b]
    // 0xaf0440: StoreField: r1->field_2f = rZR
    //     0xaf0440: stur            xzr, [x1, #0x2f]
    // 0xaf0444: ldur            x5, [fp, #-0x28]
    // 0xaf0448: StoreField: r1->field_b = r5
    //     0xaf0448: stur            w5, [x1, #0xb]
    // 0xaf044c: r0 = Padding()
    //     0xaf044c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf0450: mov             x2, x0
    // 0xaf0454: r0 = Instance_EdgeInsets
    //     0xaf0454: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xaf0458: ldr             x0, [x0, #0x668]
    // 0xaf045c: stur            x2, [fp, #-0x28]
    // 0xaf0460: StoreField: r2->field_f = r0
    //     0xaf0460: stur            w0, [x2, #0xf]
    // 0xaf0464: ldur            x0, [fp, #-0x10]
    // 0xaf0468: StoreField: r2->field_b = r0
    //     0xaf0468: stur            w0, [x2, #0xb]
    // 0xaf046c: ldur            x0, [fp, #-0x48]
    // 0xaf0470: LoadField: r1 = r0->field_b
    //     0xaf0470: ldur            w1, [x0, #0xb]
    // 0xaf0474: LoadField: r3 = r0->field_f
    //     0xaf0474: ldur            w3, [x0, #0xf]
    // 0xaf0478: DecompressPointer r3
    //     0xaf0478: add             x3, x3, HEAP, lsl #32
    // 0xaf047c: LoadField: r4 = r3->field_b
    //     0xaf047c: ldur            w4, [x3, #0xb]
    // 0xaf0480: r3 = LoadInt32Instr(r1)
    //     0xaf0480: sbfx            x3, x1, #1, #0x1f
    // 0xaf0484: stur            x3, [fp, #-0x18]
    // 0xaf0488: r1 = LoadInt32Instr(r4)
    //     0xaf0488: sbfx            x1, x4, #1, #0x1f
    // 0xaf048c: cmp             x3, x1
    // 0xaf0490: b.ne            #0xaf049c
    // 0xaf0494: mov             x1, x0
    // 0xaf0498: r0 = _growToNextCapacity()
    //     0xaf0498: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf049c: ldur            x2, [fp, #-0x48]
    // 0xaf04a0: ldur            x3, [fp, #-0x18]
    // 0xaf04a4: ldur            x4, [fp, #-0x30]
    // 0xaf04a8: add             x0, x3, #1
    // 0xaf04ac: lsl             x1, x0, #1
    // 0xaf04b0: StoreField: r2->field_b = r1
    //     0xaf04b0: stur            w1, [x2, #0xb]
    // 0xaf04b4: LoadField: r1 = r2->field_f
    //     0xaf04b4: ldur            w1, [x2, #0xf]
    // 0xaf04b8: DecompressPointer r1
    //     0xaf04b8: add             x1, x1, HEAP, lsl #32
    // 0xaf04bc: ldur            x0, [fp, #-0x28]
    // 0xaf04c0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf04c0: add             x25, x1, x3, lsl #2
    //     0xaf04c4: add             x25, x25, #0xf
    //     0xaf04c8: str             w0, [x25]
    //     0xaf04cc: tbz             w0, #0, #0xaf04e8
    //     0xaf04d0: ldurb           w16, [x1, #-1]
    //     0xaf04d4: ldurb           w17, [x0, #-1]
    //     0xaf04d8: and             x16, x17, x16, lsr #2
    //     0xaf04dc: tst             x16, HEAP, lsr #32
    //     0xaf04e0: b.eq            #0xaf04e8
    //     0xaf04e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf04e8: LoadField: r1 = r4->field_9f
    //     0xaf04e8: ldur            w1, [x4, #0x9f]
    // 0xaf04ec: DecompressPointer r1
    //     0xaf04ec: add             x1, x1, HEAP, lsl #32
    // 0xaf04f0: cmp             w1, NULL
    // 0xaf04f4: b.ne            #0xaf0500
    // 0xaf04f8: r0 = Null
    //     0xaf04f8: mov             x0, NULL
    // 0xaf04fc: b               #0xaf0504
    // 0xaf0500: r0 = trim()
    //     0xaf0500: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xaf0504: cmp             w0, NULL
    // 0xaf0508: b.ne            #0xaf0510
    // 0xaf050c: r0 = ""
    //     0xaf050c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf0510: ldur            x1, [fp, #-0x20]
    // 0xaf0514: stur            x0, [fp, #-0x10]
    // 0xaf0518: r0 = value()
    //     0xaf0518: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf051c: tbnz            w0, #4, #0xaf0528
    // 0xaf0520: r0 = Null
    //     0xaf0520: mov             x0, NULL
    // 0xaf0524: b               #0xaf052c
    // 0xaf0528: r0 = 4
    //     0xaf0528: movz            x0, #0x4
    // 0xaf052c: ldur            x1, [fp, #-0x20]
    // 0xaf0530: stur            x0, [fp, #-0x28]
    // 0xaf0534: r0 = value()
    //     0xaf0534: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf0538: tbnz            w0, #4, #0xaf0548
    // 0xaf053c: r5 = Instance_TextOverflow
    //     0xaf053c: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xaf0540: ldr             x5, [x5, #0x3a8]
    // 0xaf0544: b               #0xaf0550
    // 0xaf0548: r5 = Instance_TextOverflow
    //     0xaf0548: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xaf054c: ldr             x5, [x5, #0xe10]
    // 0xaf0550: ldur            x4, [fp, #-8]
    // 0xaf0554: ldur            x2, [fp, #-0x10]
    // 0xaf0558: ldur            x0, [fp, #-0x28]
    // 0xaf055c: ldur            x3, [fp, #-0x30]
    // 0xaf0560: stur            x5, [fp, #-0x50]
    // 0xaf0564: LoadField: r1 = r4->field_f
    //     0xaf0564: ldur            w1, [x4, #0xf]
    // 0xaf0568: DecompressPointer r1
    //     0xaf0568: add             x1, x1, HEAP, lsl #32
    // 0xaf056c: cmp             w1, NULL
    // 0xaf0570: b.eq            #0xaf0a10
    // 0xaf0574: r0 = of()
    //     0xaf0574: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf0578: LoadField: r1 = r0->field_87
    //     0xaf0578: ldur            w1, [x0, #0x87]
    // 0xaf057c: DecompressPointer r1
    //     0xaf057c: add             x1, x1, HEAP, lsl #32
    // 0xaf0580: LoadField: r0 = r1->field_2b
    //     0xaf0580: ldur            w0, [x1, #0x2b]
    // 0xaf0584: DecompressPointer r0
    //     0xaf0584: add             x0, x0, HEAP, lsl #32
    // 0xaf0588: LoadField: r1 = r0->field_13
    //     0xaf0588: ldur            w1, [x0, #0x13]
    // 0xaf058c: DecompressPointer r1
    //     0xaf058c: add             x1, x1, HEAP, lsl #32
    // 0xaf0590: stur            x1, [fp, #-0x58]
    // 0xaf0594: r0 = TextStyle()
    //     0xaf0594: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xaf0598: mov             x1, x0
    // 0xaf059c: r0 = true
    //     0xaf059c: add             x0, NULL, #0x20  ; true
    // 0xaf05a0: stur            x1, [fp, #-0x60]
    // 0xaf05a4: StoreField: r1->field_7 = r0
    //     0xaf05a4: stur            w0, [x1, #7]
    // 0xaf05a8: r2 = Instance_Color
    //     0xaf05a8: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf05ac: StoreField: r1->field_b = r2
    //     0xaf05ac: stur            w2, [x1, #0xb]
    // 0xaf05b0: r2 = 12.000000
    //     0xaf05b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf05b4: ldr             x2, [x2, #0x9e8]
    // 0xaf05b8: StoreField: r1->field_1f = r2
    //     0xaf05b8: stur            w2, [x1, #0x1f]
    // 0xaf05bc: ldur            x2, [fp, #-0x58]
    // 0xaf05c0: StoreField: r1->field_13 = r2
    //     0xaf05c0: stur            w2, [x1, #0x13]
    // 0xaf05c4: r0 = Text()
    //     0xaf05c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf05c8: mov             x3, x0
    // 0xaf05cc: ldur            x0, [fp, #-0x10]
    // 0xaf05d0: stur            x3, [fp, #-0x58]
    // 0xaf05d4: StoreField: r3->field_b = r0
    //     0xaf05d4: stur            w0, [x3, #0xb]
    // 0xaf05d8: ldur            x0, [fp, #-0x60]
    // 0xaf05dc: StoreField: r3->field_13 = r0
    //     0xaf05dc: stur            w0, [x3, #0x13]
    // 0xaf05e0: r0 = Instance_TextAlign
    //     0xaf05e0: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xaf05e4: StoreField: r3->field_1b = r0
    //     0xaf05e4: stur            w0, [x3, #0x1b]
    // 0xaf05e8: ldur            x0, [fp, #-0x50]
    // 0xaf05ec: StoreField: r3->field_2b = r0
    //     0xaf05ec: stur            w0, [x3, #0x2b]
    // 0xaf05f0: ldur            x0, [fp, #-0x28]
    // 0xaf05f4: StoreField: r3->field_37 = r0
    //     0xaf05f4: stur            w0, [x3, #0x37]
    // 0xaf05f8: r1 = Null
    //     0xaf05f8: mov             x1, NULL
    // 0xaf05fc: r2 = 2
    //     0xaf05fc: movz            x2, #0x2
    // 0xaf0600: r0 = AllocateArray()
    //     0xaf0600: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf0604: mov             x2, x0
    // 0xaf0608: ldur            x0, [fp, #-0x58]
    // 0xaf060c: stur            x2, [fp, #-0x10]
    // 0xaf0610: StoreField: r2->field_f = r0
    //     0xaf0610: stur            w0, [x2, #0xf]
    // 0xaf0614: r1 = <Widget>
    //     0xaf0614: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf0618: r0 = AllocateGrowableArray()
    //     0xaf0618: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf061c: mov             x2, x0
    // 0xaf0620: ldur            x0, [fp, #-0x10]
    // 0xaf0624: stur            x2, [fp, #-0x28]
    // 0xaf0628: StoreField: r2->field_f = r0
    //     0xaf0628: stur            w0, [x2, #0xf]
    // 0xaf062c: r0 = 2
    //     0xaf062c: movz            x0, #0x2
    // 0xaf0630: StoreField: r2->field_b = r0
    //     0xaf0630: stur            w0, [x2, #0xb]
    // 0xaf0634: ldur            x0, [fp, #-0x30]
    // 0xaf0638: LoadField: r1 = r0->field_9f
    //     0xaf0638: ldur            w1, [x0, #0x9f]
    // 0xaf063c: DecompressPointer r1
    //     0xaf063c: add             x1, x1, HEAP, lsl #32
    // 0xaf0640: cmp             w1, NULL
    // 0xaf0644: b.ne            #0xaf0650
    // 0xaf0648: r0 = Null
    //     0xaf0648: mov             x0, NULL
    // 0xaf064c: b               #0xaf0654
    // 0xaf0650: r0 = trim()
    //     0xaf0650: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xaf0654: cmp             w0, NULL
    // 0xaf0658: b.ne            #0xaf0664
    // 0xaf065c: r1 = ""
    //     0xaf065c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf0660: b               #0xaf0668
    // 0xaf0664: mov             x1, x0
    // 0xaf0668: ldur            x0, [fp, #-8]
    // 0xaf066c: LoadField: r2 = r0->field_f
    //     0xaf066c: ldur            w2, [x0, #0xf]
    // 0xaf0670: DecompressPointer r2
    //     0xaf0670: add             x2, x2, HEAP, lsl #32
    // 0xaf0674: cmp             w2, NULL
    // 0xaf0678: b.eq            #0xaf0a14
    // 0xaf067c: r0 = TextExceeds.textExceedsLines()
    //     0xaf067c: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xaf0680: tbnz            w0, #4, #0xaf0810
    // 0xaf0684: ldur            x1, [fp, #-0x20]
    // 0xaf0688: r0 = value()
    //     0xaf0688: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf068c: tbnz            w0, #4, #0xaf069c
    // 0xaf0690: r3 = "Know Less"
    //     0xaf0690: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xaf0694: ldr             x3, [x3, #0x1d0]
    // 0xaf0698: b               #0xaf06a4
    // 0xaf069c: r3 = "Know more"
    //     0xaf069c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xaf06a0: ldr             x3, [x3, #0x20]
    // 0xaf06a4: ldur            x0, [fp, #-8]
    // 0xaf06a8: ldur            x2, [fp, #-0x28]
    // 0xaf06ac: stur            x3, [fp, #-0x10]
    // 0xaf06b0: LoadField: r1 = r0->field_f
    //     0xaf06b0: ldur            w1, [x0, #0xf]
    // 0xaf06b4: DecompressPointer r1
    //     0xaf06b4: add             x1, x1, HEAP, lsl #32
    // 0xaf06b8: cmp             w1, NULL
    // 0xaf06bc: b.eq            #0xaf0a18
    // 0xaf06c0: r0 = of()
    //     0xaf06c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf06c4: LoadField: r1 = r0->field_87
    //     0xaf06c4: ldur            w1, [x0, #0x87]
    // 0xaf06c8: DecompressPointer r1
    //     0xaf06c8: add             x1, x1, HEAP, lsl #32
    // 0xaf06cc: LoadField: r0 = r1->field_7
    //     0xaf06cc: ldur            w0, [x1, #7]
    // 0xaf06d0: DecompressPointer r0
    //     0xaf06d0: add             x0, x0, HEAP, lsl #32
    // 0xaf06d4: ldur            x1, [fp, #-8]
    // 0xaf06d8: stur            x0, [fp, #-0x20]
    // 0xaf06dc: LoadField: r2 = r1->field_f
    //     0xaf06dc: ldur            w2, [x1, #0xf]
    // 0xaf06e0: DecompressPointer r2
    //     0xaf06e0: add             x2, x2, HEAP, lsl #32
    // 0xaf06e4: cmp             w2, NULL
    // 0xaf06e8: b.eq            #0xaf0a1c
    // 0xaf06ec: mov             x1, x2
    // 0xaf06f0: r0 = of()
    //     0xaf06f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf06f4: LoadField: r1 = r0->field_5b
    //     0xaf06f4: ldur            w1, [x0, #0x5b]
    // 0xaf06f8: DecompressPointer r1
    //     0xaf06f8: add             x1, x1, HEAP, lsl #32
    // 0xaf06fc: r16 = 12.000000
    //     0xaf06fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf0700: ldr             x16, [x16, #0x9e8]
    // 0xaf0704: stp             x1, x16, [SP, #8]
    // 0xaf0708: r16 = Instance_TextDecoration
    //     0xaf0708: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xaf070c: ldr             x16, [x16, #0x10]
    // 0xaf0710: str             x16, [SP]
    // 0xaf0714: ldur            x1, [fp, #-0x20]
    // 0xaf0718: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xaf0718: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xaf071c: ldr             x4, [x4, #0xe38]
    // 0xaf0720: r0 = copyWith()
    //     0xaf0720: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf0724: stur            x0, [fp, #-8]
    // 0xaf0728: r0 = Text()
    //     0xaf0728: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf072c: mov             x1, x0
    // 0xaf0730: ldur            x0, [fp, #-0x10]
    // 0xaf0734: stur            x1, [fp, #-0x20]
    // 0xaf0738: StoreField: r1->field_b = r0
    //     0xaf0738: stur            w0, [x1, #0xb]
    // 0xaf073c: ldur            x0, [fp, #-8]
    // 0xaf0740: StoreField: r1->field_13 = r0
    //     0xaf0740: stur            w0, [x1, #0x13]
    // 0xaf0744: r0 = Padding()
    //     0xaf0744: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf0748: mov             x1, x0
    // 0xaf074c: r0 = Instance_EdgeInsets
    //     0xaf074c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xaf0750: ldr             x0, [x0, #0x668]
    // 0xaf0754: stur            x1, [fp, #-8]
    // 0xaf0758: StoreField: r1->field_f = r0
    //     0xaf0758: stur            w0, [x1, #0xf]
    // 0xaf075c: ldur            x0, [fp, #-0x20]
    // 0xaf0760: StoreField: r1->field_b = r0
    //     0xaf0760: stur            w0, [x1, #0xb]
    // 0xaf0764: r0 = GestureDetector()
    //     0xaf0764: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf0768: ldur            x2, [fp, #-0x38]
    // 0xaf076c: r1 = Function '<anonymous closure>':.
    //     0xaf076c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58148] AnonymousClosure: (0xaf0a20), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard (0xaefb78)
    //     0xaf0770: ldr             x1, [x1, #0x148]
    // 0xaf0774: stur            x0, [fp, #-0x10]
    // 0xaf0778: r0 = AllocateClosure()
    //     0xaf0778: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf077c: ldur            x16, [fp, #-8]
    // 0xaf0780: stp             x16, x0, [SP]
    // 0xaf0784: ldur            x1, [fp, #-0x10]
    // 0xaf0788: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaf0788: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaf078c: ldr             x4, [x4, #0xaf0]
    // 0xaf0790: r0 = GestureDetector()
    //     0xaf0790: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf0794: ldur            x0, [fp, #-0x28]
    // 0xaf0798: LoadField: r1 = r0->field_b
    //     0xaf0798: ldur            w1, [x0, #0xb]
    // 0xaf079c: LoadField: r2 = r0->field_f
    //     0xaf079c: ldur            w2, [x0, #0xf]
    // 0xaf07a0: DecompressPointer r2
    //     0xaf07a0: add             x2, x2, HEAP, lsl #32
    // 0xaf07a4: LoadField: r3 = r2->field_b
    //     0xaf07a4: ldur            w3, [x2, #0xb]
    // 0xaf07a8: r2 = LoadInt32Instr(r1)
    //     0xaf07a8: sbfx            x2, x1, #1, #0x1f
    // 0xaf07ac: stur            x2, [fp, #-0x18]
    // 0xaf07b0: r1 = LoadInt32Instr(r3)
    //     0xaf07b0: sbfx            x1, x3, #1, #0x1f
    // 0xaf07b4: cmp             x2, x1
    // 0xaf07b8: b.ne            #0xaf07c4
    // 0xaf07bc: mov             x1, x0
    // 0xaf07c0: r0 = _growToNextCapacity()
    //     0xaf07c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf07c4: ldur            x2, [fp, #-0x28]
    // 0xaf07c8: ldur            x3, [fp, #-0x18]
    // 0xaf07cc: add             x0, x3, #1
    // 0xaf07d0: lsl             x1, x0, #1
    // 0xaf07d4: StoreField: r2->field_b = r1
    //     0xaf07d4: stur            w1, [x2, #0xb]
    // 0xaf07d8: LoadField: r1 = r2->field_f
    //     0xaf07d8: ldur            w1, [x2, #0xf]
    // 0xaf07dc: DecompressPointer r1
    //     0xaf07dc: add             x1, x1, HEAP, lsl #32
    // 0xaf07e0: ldur            x0, [fp, #-0x10]
    // 0xaf07e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf07e4: add             x25, x1, x3, lsl #2
    //     0xaf07e8: add             x25, x25, #0xf
    //     0xaf07ec: str             w0, [x25]
    //     0xaf07f0: tbz             w0, #0, #0xaf080c
    //     0xaf07f4: ldurb           w16, [x1, #-1]
    //     0xaf07f8: ldurb           w17, [x0, #-1]
    //     0xaf07fc: and             x16, x17, x16, lsr #2
    //     0xaf0800: tst             x16, HEAP, lsr #32
    //     0xaf0804: b.eq            #0xaf080c
    //     0xaf0808: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf080c: b               #0xaf0814
    // 0xaf0810: ldur            x2, [fp, #-0x28]
    // 0xaf0814: ldur            x1, [fp, #-0x48]
    // 0xaf0818: r0 = Column()
    //     0xaf0818: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf081c: mov             x1, x0
    // 0xaf0820: r0 = Instance_Axis
    //     0xaf0820: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf0824: stur            x1, [fp, #-8]
    // 0xaf0828: StoreField: r1->field_f = r0
    //     0xaf0828: stur            w0, [x1, #0xf]
    // 0xaf082c: r0 = Instance_MainAxisAlignment
    //     0xaf082c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf0830: ldr             x0, [x0, #0xa08]
    // 0xaf0834: StoreField: r1->field_13 = r0
    //     0xaf0834: stur            w0, [x1, #0x13]
    // 0xaf0838: r0 = Instance_MainAxisSize
    //     0xaf0838: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf083c: ldr             x0, [x0, #0xa10]
    // 0xaf0840: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf0840: stur            w0, [x1, #0x17]
    // 0xaf0844: r0 = Instance_CrossAxisAlignment
    //     0xaf0844: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf0848: ldr             x0, [x0, #0x890]
    // 0xaf084c: StoreField: r1->field_1b = r0
    //     0xaf084c: stur            w0, [x1, #0x1b]
    // 0xaf0850: r0 = Instance_VerticalDirection
    //     0xaf0850: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf0854: ldr             x0, [x0, #0xa20]
    // 0xaf0858: StoreField: r1->field_23 = r0
    //     0xaf0858: stur            w0, [x1, #0x23]
    // 0xaf085c: r0 = Instance_Clip
    //     0xaf085c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf0860: ldr             x0, [x0, #0x38]
    // 0xaf0864: StoreField: r1->field_2b = r0
    //     0xaf0864: stur            w0, [x1, #0x2b]
    // 0xaf0868: StoreField: r1->field_2f = rZR
    //     0xaf0868: stur            xzr, [x1, #0x2f]
    // 0xaf086c: ldur            x0, [fp, #-0x28]
    // 0xaf0870: StoreField: r1->field_b = r0
    //     0xaf0870: stur            w0, [x1, #0xb]
    // 0xaf0874: r0 = Padding()
    //     0xaf0874: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf0878: mov             x2, x0
    // 0xaf087c: r0 = Instance_EdgeInsets
    //     0xaf087c: add             x0, PP, #0x57, lsl #12  ; [pp+0x57b78] Obj!EdgeInsets@d58f71
    //     0xaf0880: ldr             x0, [x0, #0xb78]
    // 0xaf0884: stur            x2, [fp, #-0x10]
    // 0xaf0888: StoreField: r2->field_f = r0
    //     0xaf0888: stur            w0, [x2, #0xf]
    // 0xaf088c: ldur            x0, [fp, #-8]
    // 0xaf0890: StoreField: r2->field_b = r0
    //     0xaf0890: stur            w0, [x2, #0xb]
    // 0xaf0894: ldur            x0, [fp, #-0x48]
    // 0xaf0898: LoadField: r1 = r0->field_b
    //     0xaf0898: ldur            w1, [x0, #0xb]
    // 0xaf089c: LoadField: r3 = r0->field_f
    //     0xaf089c: ldur            w3, [x0, #0xf]
    // 0xaf08a0: DecompressPointer r3
    //     0xaf08a0: add             x3, x3, HEAP, lsl #32
    // 0xaf08a4: LoadField: r4 = r3->field_b
    //     0xaf08a4: ldur            w4, [x3, #0xb]
    // 0xaf08a8: r3 = LoadInt32Instr(r1)
    //     0xaf08a8: sbfx            x3, x1, #1, #0x1f
    // 0xaf08ac: stur            x3, [fp, #-0x18]
    // 0xaf08b0: r1 = LoadInt32Instr(r4)
    //     0xaf08b0: sbfx            x1, x4, #1, #0x1f
    // 0xaf08b4: cmp             x3, x1
    // 0xaf08b8: b.ne            #0xaf08c4
    // 0xaf08bc: mov             x1, x0
    // 0xaf08c0: r0 = _growToNextCapacity()
    //     0xaf08c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf08c4: ldur            x4, [fp, #-0x40]
    // 0xaf08c8: ldur            x2, [fp, #-0x48]
    // 0xaf08cc: ldur            x3, [fp, #-0x18]
    // 0xaf08d0: add             x0, x3, #1
    // 0xaf08d4: lsl             x1, x0, #1
    // 0xaf08d8: StoreField: r2->field_b = r1
    //     0xaf08d8: stur            w1, [x2, #0xb]
    // 0xaf08dc: LoadField: r1 = r2->field_f
    //     0xaf08dc: ldur            w1, [x2, #0xf]
    // 0xaf08e0: DecompressPointer r1
    //     0xaf08e0: add             x1, x1, HEAP, lsl #32
    // 0xaf08e4: ldur            x0, [fp, #-0x10]
    // 0xaf08e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf08e8: add             x25, x1, x3, lsl #2
    //     0xaf08ec: add             x25, x25, #0xf
    //     0xaf08f0: str             w0, [x25]
    //     0xaf08f4: tbz             w0, #0, #0xaf0910
    //     0xaf08f8: ldurb           w16, [x1, #-1]
    //     0xaf08fc: ldurb           w17, [x0, #-1]
    //     0xaf0900: and             x16, x17, x16, lsr #2
    //     0xaf0904: tst             x16, HEAP, lsr #32
    //     0xaf0908: b.eq            #0xaf0910
    //     0xaf090c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf0910: r0 = ListView()
    //     0xaf0910: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xaf0914: stur            x0, [fp, #-8]
    // 0xaf0918: r16 = Instance_NeverScrollableScrollPhysics
    //     0xaf0918: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xaf091c: ldr             x16, [x16, #0x1c8]
    // 0xaf0920: r30 = true
    //     0xaf0920: add             lr, NULL, #0x20  ; true
    // 0xaf0924: stp             lr, x16, [SP, #8]
    // 0xaf0928: r16 = Instance_EdgeInsets
    //     0xaf0928: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xaf092c: str             x16, [SP]
    // 0xaf0930: mov             x1, x0
    // 0xaf0934: ldur            x2, [fp, #-0x48]
    // 0xaf0938: r4 = const [0, 0x5, 0x3, 0x2, padding, 0x4, physics, 0x2, shrinkWrap, 0x3, null]
    //     0xaf0938: add             x4, PP, #0x52, lsl #12  ; [pp+0x526b8] List(11) [0, 0x5, 0x3, 0x2, "padding", 0x4, "physics", 0x2, "shrinkWrap", 0x3, Null]
    //     0xaf093c: ldr             x4, [x4, #0x6b8]
    // 0xaf0940: r0 = ListView()
    //     0xaf0940: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xaf0944: r0 = Card()
    //     0xaf0944: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xaf0948: mov             x1, x0
    // 0xaf094c: r0 = 0.000000
    //     0xaf094c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaf0950: stur            x1, [fp, #-0x10]
    // 0xaf0954: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf0954: stur            w0, [x1, #0x17]
    // 0xaf0958: ldur            x0, [fp, #-0x40]
    // 0xaf095c: StoreField: r1->field_1b = r0
    //     0xaf095c: stur            w0, [x1, #0x1b]
    // 0xaf0960: r0 = true
    //     0xaf0960: add             x0, NULL, #0x20  ; true
    // 0xaf0964: StoreField: r1->field_1f = r0
    //     0xaf0964: stur            w0, [x1, #0x1f]
    // 0xaf0968: ldur            x2, [fp, #-8]
    // 0xaf096c: StoreField: r1->field_2f = r2
    //     0xaf096c: stur            w2, [x1, #0x2f]
    // 0xaf0970: StoreField: r1->field_2b = r0
    //     0xaf0970: stur            w0, [x1, #0x2b]
    // 0xaf0974: r0 = Instance__CardVariant
    //     0xaf0974: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xaf0978: ldr             x0, [x0, #0xa68]
    // 0xaf097c: StoreField: r1->field_33 = r0
    //     0xaf097c: stur            w0, [x1, #0x33]
    // 0xaf0980: r0 = Container()
    //     0xaf0980: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf0984: stur            x0, [fp, #-8]
    // 0xaf0988: r16 = Instance_EdgeInsets
    //     0xaf0988: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaf098c: ldr             x16, [x16, #0x980]
    // 0xaf0990: ldur            lr, [fp, #-0x10]
    // 0xaf0994: stp             lr, x16, [SP]
    // 0xaf0998: mov             x1, x0
    // 0xaf099c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xaf099c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xaf09a0: ldr             x4, [x4, #0x30]
    // 0xaf09a4: r0 = Container()
    //     0xaf09a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf09a8: r0 = AnimatedContainer()
    //     0xaf09a8: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xaf09ac: stur            x0, [fp, #-0x10]
    // 0xaf09b0: r16 = Instance_Cubic
    //     0xaf09b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xaf09b4: ldr             x16, [x16, #0xaf8]
    // 0xaf09b8: str             x16, [SP]
    // 0xaf09bc: mov             x1, x0
    // 0xaf09c0: ldur            x2, [fp, #-8]
    // 0xaf09c4: r3 = Instance_Duration
    //     0xaf09c4: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xaf09c8: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xaf09c8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xaf09cc: ldr             x4, [x4, #0xbc8]
    // 0xaf09d0: r0 = AnimatedContainer()
    //     0xaf09d0: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xaf09d4: ldur            x0, [fp, #-0x10]
    // 0xaf09d8: LeaveFrame
    //     0xaf09d8: mov             SP, fp
    //     0xaf09dc: ldp             fp, lr, [SP], #0x10
    // 0xaf09e0: ret
    //     0xaf09e0: ret             
    // 0xaf09e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf09e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf09e8: b               #0xaefba4
    // 0xaf09ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf09ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf09f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf09f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf09f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf09f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf09f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf09f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf09fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf09fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf0a00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf0a00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf0a04: r0 = RangeErrorSharedWithFPURegs()
    //     0xaf0a04: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xaf0a08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf0a08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf0a0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf0a0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf0a10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf0a10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf0a14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf0a14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf0a18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf0a18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf0a1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf0a1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf0a20, size: 0x8c
    // 0xaf0a20: EnterFrame
    //     0xaf0a20: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0a24: mov             fp, SP
    // 0xaf0a28: AllocStack(0x10)
    //     0xaf0a28: sub             SP, SP, #0x10
    // 0xaf0a2c: SetupParameters()
    //     0xaf0a2c: ldr             x0, [fp, #0x10]
    //     0xaf0a30: ldur            w2, [x0, #0x17]
    //     0xaf0a34: add             x2, x2, HEAP, lsl #32
    //     0xaf0a38: stur            x2, [fp, #-0x10]
    // 0xaf0a3c: CheckStackOverflow
    //     0xaf0a3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0a40: cmp             SP, x16
    //     0xaf0a44: b.ls            #0xaf0aa4
    // 0xaf0a48: LoadField: r0 = r2->field_13
    //     0xaf0a48: ldur            w0, [x2, #0x13]
    // 0xaf0a4c: DecompressPointer r0
    //     0xaf0a4c: add             x0, x0, HEAP, lsl #32
    // 0xaf0a50: mov             x1, x0
    // 0xaf0a54: stur            x0, [fp, #-8]
    // 0xaf0a58: r0 = value()
    //     0xaf0a58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf0a5c: eor             x2, x0, #0x10
    // 0xaf0a60: ldur            x1, [fp, #-8]
    // 0xaf0a64: r0 = value=()
    //     0xaf0a64: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf0a68: ldur            x0, [fp, #-0x10]
    // 0xaf0a6c: LoadField: r3 = r0->field_f
    //     0xaf0a6c: ldur            w3, [x0, #0xf]
    // 0xaf0a70: DecompressPointer r3
    //     0xaf0a70: add             x3, x3, HEAP, lsl #32
    // 0xaf0a74: stur            x3, [fp, #-8]
    // 0xaf0a78: r1 = Function '<anonymous closure>':.
    //     0xaf0a78: add             x1, PP, #0x58, lsl #12  ; [pp+0x58150] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaf0a7c: ldr             x1, [x1, #0x150]
    // 0xaf0a80: r2 = Null
    //     0xaf0a80: mov             x2, NULL
    // 0xaf0a84: r0 = AllocateClosure()
    //     0xaf0a84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf0a88: ldur            x1, [fp, #-8]
    // 0xaf0a8c: mov             x2, x0
    // 0xaf0a90: r0 = setState()
    //     0xaf0a90: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf0a94: r0 = Null
    //     0xaf0a94: mov             x0, NULL
    // 0xaf0a98: LeaveFrame
    //     0xaf0a98: mov             SP, fp
    //     0xaf0a9c: ldp             fp, lr, [SP], #0x10
    // 0xaf0aa0: ret
    //     0xaf0aa0: ret             
    // 0xaf0aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0aa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0aa8: b               #0xaf0a48
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xaf0aac, size: 0x84
    // 0xaf0aac: EnterFrame
    //     0xaf0aac: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0ab0: mov             fp, SP
    // 0xaf0ab4: AllocStack(0x10)
    //     0xaf0ab4: sub             SP, SP, #0x10
    // 0xaf0ab8: SetupParameters()
    //     0xaf0ab8: ldr             x0, [fp, #0x18]
    //     0xaf0abc: ldur            w1, [x0, #0x17]
    //     0xaf0ac0: add             x1, x1, HEAP, lsl #32
    //     0xaf0ac4: stur            x1, [fp, #-8]
    // 0xaf0ac8: CheckStackOverflow
    //     0xaf0ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0acc: cmp             SP, x16
    //     0xaf0ad0: b.ls            #0xaf0b28
    // 0xaf0ad4: r1 = 1
    //     0xaf0ad4: movz            x1, #0x1
    // 0xaf0ad8: r0 = AllocateContext()
    //     0xaf0ad8: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf0adc: mov             x1, x0
    // 0xaf0ae0: ldur            x0, [fp, #-8]
    // 0xaf0ae4: StoreField: r1->field_b = r0
    //     0xaf0ae4: stur            w0, [x1, #0xb]
    // 0xaf0ae8: ldr             x2, [fp, #0x10]
    // 0xaf0aec: StoreField: r1->field_f = r2
    //     0xaf0aec: stur            w2, [x1, #0xf]
    // 0xaf0af0: LoadField: r3 = r0->field_f
    //     0xaf0af0: ldur            w3, [x0, #0xf]
    // 0xaf0af4: DecompressPointer r3
    //     0xaf0af4: add             x3, x3, HEAP, lsl #32
    // 0xaf0af8: mov             x2, x1
    // 0xaf0afc: stur            x3, [fp, #-0x10]
    // 0xaf0b00: r1 = Function '<anonymous closure>':.
    //     0xaf0b00: add             x1, PP, #0x58, lsl #12  ; [pp+0x58158] AnonymousClosure: (0xaf0b30), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xaef1b8)
    //     0xaf0b04: ldr             x1, [x1, #0x158]
    // 0xaf0b08: r0 = AllocateClosure()
    //     0xaf0b08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf0b0c: ldur            x1, [fp, #-0x10]
    // 0xaf0b10: mov             x2, x0
    // 0xaf0b14: r0 = setState()
    //     0xaf0b14: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf0b18: r0 = Null
    //     0xaf0b18: mov             x0, NULL
    // 0xaf0b1c: LeaveFrame
    //     0xaf0b1c: mov             SP, fp
    //     0xaf0b20: ldp             fp, lr, [SP], #0x10
    // 0xaf0b24: ret
    //     0xaf0b24: ret             
    // 0xaf0b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0b28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0b2c: b               #0xaf0ad4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf0b30, size: 0x8c
    // 0xaf0b30: EnterFrame
    //     0xaf0b30: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0b34: mov             fp, SP
    // 0xaf0b38: AllocStack(0x8)
    //     0xaf0b38: sub             SP, SP, #8
    // 0xaf0b3c: SetupParameters()
    //     0xaf0b3c: ldr             x0, [fp, #0x10]
    //     0xaf0b40: ldur            w1, [x0, #0x17]
    //     0xaf0b44: add             x1, x1, HEAP, lsl #32
    // 0xaf0b48: CheckStackOverflow
    //     0xaf0b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0b4c: cmp             SP, x16
    //     0xaf0b50: b.ls            #0xaf0bb4
    // 0xaf0b54: LoadField: r0 = r1->field_b
    //     0xaf0b54: ldur            w0, [x1, #0xb]
    // 0xaf0b58: DecompressPointer r0
    //     0xaf0b58: add             x0, x0, HEAP, lsl #32
    // 0xaf0b5c: LoadField: r2 = r0->field_f
    //     0xaf0b5c: ldur            w2, [x0, #0xf]
    // 0xaf0b60: DecompressPointer r2
    //     0xaf0b60: add             x2, x2, HEAP, lsl #32
    // 0xaf0b64: LoadField: r0 = r1->field_f
    //     0xaf0b64: ldur            w0, [x1, #0xf]
    // 0xaf0b68: DecompressPointer r0
    //     0xaf0b68: add             x0, x0, HEAP, lsl #32
    // 0xaf0b6c: r1 = LoadInt32Instr(r0)
    //     0xaf0b6c: sbfx            x1, x0, #1, #0x1f
    //     0xaf0b70: tbz             w0, #0, #0xaf0b78
    //     0xaf0b74: ldur            x1, [x0, #7]
    // 0xaf0b78: ArrayStore: r2[0] = r1  ; List_8
    //     0xaf0b78: stur            x1, [x2, #0x17]
    // 0xaf0b7c: LoadField: r0 = r2->field_1f
    //     0xaf0b7c: ldur            w0, [x2, #0x1f]
    // 0xaf0b80: DecompressPointer r0
    //     0xaf0b80: add             x0, x0, HEAP, lsl #32
    // 0xaf0b84: stur            x0, [fp, #-8]
    // 0xaf0b88: r1 = Function '<anonymous closure>':.
    //     0xaf0b88: add             x1, PP, #0x58, lsl #12  ; [pp+0x58160] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xaf0b8c: ldr             x1, [x1, #0x160]
    // 0xaf0b90: r2 = Null
    //     0xaf0b90: mov             x2, NULL
    // 0xaf0b94: r0 = AllocateClosure()
    //     0xaf0b94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf0b98: ldur            x1, [fp, #-8]
    // 0xaf0b9c: mov             x2, x0
    // 0xaf0ba0: r0 = forEach()
    //     0xaf0ba0: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xaf0ba4: r0 = Null
    //     0xaf0ba4: mov             x0, NULL
    // 0xaf0ba8: LeaveFrame
    //     0xaf0ba8: mov             SP, fp
    //     0xaf0bac: ldp             fp, lr, [SP], #0x10
    // 0xaf0bb0: ret
    //     0xaf0bb0: ret             
    // 0xaf0bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0bb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0bb8: b               #0xaf0b54
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf0bbc, size: 0xa0
    // 0xaf0bbc: EnterFrame
    //     0xaf0bbc: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0bc0: mov             fp, SP
    // 0xaf0bc4: AllocStack(0x28)
    //     0xaf0bc4: sub             SP, SP, #0x28
    // 0xaf0bc8: SetupParameters()
    //     0xaf0bc8: ldr             x0, [fp, #0x10]
    //     0xaf0bcc: ldur            w1, [x0, #0x17]
    //     0xaf0bd0: add             x1, x1, HEAP, lsl #32
    // 0xaf0bd4: CheckStackOverflow
    //     0xaf0bd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0bd8: cmp             SP, x16
    //     0xaf0bdc: b.ls            #0xaf0c50
    // 0xaf0be0: LoadField: r0 = r1->field_f
    //     0xaf0be0: ldur            w0, [x1, #0xf]
    // 0xaf0be4: DecompressPointer r0
    //     0xaf0be4: add             x0, x0, HEAP, lsl #32
    // 0xaf0be8: LoadField: r1 = r0->field_b
    //     0xaf0be8: ldur            w1, [x0, #0xb]
    // 0xaf0bec: DecompressPointer r1
    //     0xaf0bec: add             x1, x1, HEAP, lsl #32
    // 0xaf0bf0: cmp             w1, NULL
    // 0xaf0bf4: b.eq            #0xaf0c58
    // 0xaf0bf8: LoadField: r0 = r1->field_1b
    //     0xaf0bf8: ldur            w0, [x1, #0x1b]
    // 0xaf0bfc: DecompressPointer r0
    //     0xaf0bfc: add             x0, x0, HEAP, lsl #32
    // 0xaf0c00: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaf0c00: ldur            w2, [x1, #0x17]
    // 0xaf0c04: DecompressPointer r2
    //     0xaf0c04: add             x2, x2, HEAP, lsl #32
    // 0xaf0c08: LoadField: r3 = r1->field_23
    //     0xaf0c08: ldur            w3, [x1, #0x23]
    // 0xaf0c0c: DecompressPointer r3
    //     0xaf0c0c: add             x3, x3, HEAP, lsl #32
    // 0xaf0c10: LoadField: r4 = r1->field_1f
    //     0xaf0c10: ldur            w4, [x1, #0x1f]
    // 0xaf0c14: DecompressPointer r4
    //     0xaf0c14: add             x4, x4, HEAP, lsl #32
    // 0xaf0c18: LoadField: r5 = r1->field_27
    //     0xaf0c18: ldur            w5, [x1, #0x27]
    // 0xaf0c1c: DecompressPointer r5
    //     0xaf0c1c: add             x5, x5, HEAP, lsl #32
    // 0xaf0c20: stp             x0, x5, [SP, #0x18]
    // 0xaf0c24: stp             x3, x2, [SP, #8]
    // 0xaf0c28: str             x4, [SP]
    // 0xaf0c2c: r4 = 0
    //     0xaf0c2c: movz            x4, #0
    // 0xaf0c30: ldr             x0, [SP, #0x20]
    // 0xaf0c34: r5 = UnlinkedCall_0x613b5c
    //     0xaf0c34: add             x16, PP, #0x58, lsl #12  ; [pp+0x58168] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaf0c38: ldp             x5, lr, [x16, #0x168]
    // 0xaf0c3c: blr             lr
    // 0xaf0c40: r0 = Null
    //     0xaf0c40: mov             x0, NULL
    // 0xaf0c44: LeaveFrame
    //     0xaf0c44: mov             SP, fp
    //     0xaf0c48: ldp             fp, lr, [SP], #0x10
    // 0xaf0c4c: ret
    //     0xaf0c4c: ret             
    // 0xaf0c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0c50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0c54: b               #0xaf0be0
    // 0xaf0c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf0c58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87474, size: 0x54
    // 0xc87474: EnterFrame
    //     0xc87474: stp             fp, lr, [SP, #-0x10]!
    //     0xc87478: mov             fp, SP
    // 0xc8747c: CheckStackOverflow
    //     0xc8747c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87480: cmp             SP, x16
    //     0xc87484: b.ls            #0xc874b4
    // 0xc87488: LoadField: r0 = r1->field_13
    //     0xc87488: ldur            w0, [x1, #0x13]
    // 0xc8748c: DecompressPointer r0
    //     0xc8748c: add             x0, x0, HEAP, lsl #32
    // 0xc87490: r16 = Sentinel
    //     0xc87490: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87494: cmp             w0, w16
    // 0xc87498: b.eq            #0xc874bc
    // 0xc8749c: mov             x1, x0
    // 0xc874a0: r0 = dispose()
    //     0xc874a0: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc874a4: r0 = Null
    //     0xc874a4: mov             x0, NULL
    // 0xc874a8: LeaveFrame
    //     0xc874a8: mov             SP, fp
    //     0xc874ac: ldp             fp, lr, [SP], #0x10
    // 0xc874b0: ret
    //     0xc874b0: ret             
    // 0xc874b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc874b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc874b8: b               #0xc87488
    // 0xc874bc: r9 = _pageController
    //     0xc874bc: add             x9, PP, #0x58, lsl #12  ; [pp+0x58108] Field <_ProductTestimonialCarouselState@1468390880._pageController@1468390880>: late (offset: 0x14)
    //     0xc874c0: ldr             x9, [x9, #0x108]
    // 0xc874c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc874c4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4158, size: 0x30, field offset: 0xc
//   const constructor, 
class ProductTestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7db34, size: 0x84
    // 0xc7db34: EnterFrame
    //     0xc7db34: stp             fp, lr, [SP, #-0x10]!
    //     0xc7db38: mov             fp, SP
    // 0xc7db3c: AllocStack(0x18)
    //     0xc7db3c: sub             SP, SP, #0x18
    // 0xc7db40: CheckStackOverflow
    //     0xc7db40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7db44: cmp             SP, x16
    //     0xc7db48: b.ls            #0xc7dbb0
    // 0xc7db4c: r1 = <ProductTestimonialCarousel>
    //     0xc7db4c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bc8] TypeArguments: <ProductTestimonialCarousel>
    //     0xc7db50: ldr             x1, [x1, #0xbc8]
    // 0xc7db54: r0 = _ProductTestimonialCarouselState()
    //     0xc7db54: bl              #0xc7dbb8  ; Allocate_ProductTestimonialCarouselStateStub -> _ProductTestimonialCarouselState (size=0x24)
    // 0xc7db58: mov             x1, x0
    // 0xc7db5c: r0 = Sentinel
    //     0xc7db5c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7db60: stur            x1, [fp, #-8]
    // 0xc7db64: StoreField: r1->field_13 = r0
    //     0xc7db64: stur            w0, [x1, #0x13]
    // 0xc7db68: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7db68: stur            xzr, [x1, #0x17]
    // 0xc7db6c: r16 = <int, RxBool>
    //     0xc7db6c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7db70: ldr             x16, [x16, #0x298]
    // 0xc7db74: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7db78: stp             lr, x16, [SP]
    // 0xc7db7c: r0 = Map._fromLiteral()
    //     0xc7db7c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7db80: ldur            x1, [fp, #-8]
    // 0xc7db84: StoreField: r1->field_1f = r0
    //     0xc7db84: stur            w0, [x1, #0x1f]
    //     0xc7db88: ldurb           w16, [x1, #-1]
    //     0xc7db8c: ldurb           w17, [x0, #-1]
    //     0xc7db90: and             x16, x17, x16, lsr #2
    //     0xc7db94: tst             x16, HEAP, lsr #32
    //     0xc7db98: b.eq            #0xc7dba0
    //     0xc7db9c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7dba0: mov             x0, x1
    // 0xc7dba4: LeaveFrame
    //     0xc7dba4: mov             SP, fp
    //     0xc7dba8: ldp             fp, lr, [SP], #0x10
    // 0xc7dbac: ret
    //     0xc7dbac: ret             
    // 0xc7dbb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7dbb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7dbb4: b               #0xc7db4c
  }
}
