// lib: , url: package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart

// class id: 1049394, size: 0x8
class :: {
}

// class id: 4567, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeReturnIntermediateScreen extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1360984, size: 0x1d0
    // 0x1360984: EnterFrame
    //     0x1360984: stp             fp, lr, [SP, #-0x10]!
    //     0x1360988: mov             fp, SP
    // 0x136098c: AllocStack(0x38)
    //     0x136098c: sub             SP, SP, #0x38
    // 0x1360990: SetupParameters(ExchangeReturnIntermediateScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x1360990: mov             x0, x1
    //     0x1360994: stur            x1, [fp, #-8]
    //     0x1360998: mov             x1, x2
    //     0x136099c: stur            x2, [fp, #-0x10]
    // 0x13609a0: CheckStackOverflow
    //     0x13609a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13609a4: cmp             SP, x16
    //     0x13609a8: b.ls            #0x1360b4c
    // 0x13609ac: r1 = 2
    //     0x13609ac: movz            x1, #0x2
    // 0x13609b0: r0 = AllocateContext()
    //     0x13609b0: bl              #0x16f6108  ; AllocateContextStub
    // 0x13609b4: mov             x2, x0
    // 0x13609b8: ldur            x0, [fp, #-8]
    // 0x13609bc: stur            x2, [fp, #-0x18]
    // 0x13609c0: StoreField: r2->field_f = r0
    //     0x13609c0: stur            w0, [x2, #0xf]
    // 0x13609c4: ldur            x0, [fp, #-0x10]
    // 0x13609c8: StoreField: r2->field_13 = r0
    //     0x13609c8: stur            w0, [x2, #0x13]
    // 0x13609cc: mov             x1, x0
    // 0x13609d0: r0 = of()
    //     0x13609d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13609d4: LoadField: r1 = r0->field_5b
    //     0x13609d4: ldur            w1, [x0, #0x5b]
    // 0x13609d8: DecompressPointer r1
    //     0x13609d8: add             x1, x1, HEAP, lsl #32
    // 0x13609dc: r16 = <Color>
    //     0x13609dc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13609e0: ldr             x16, [x16, #0xf80]
    // 0x13609e4: stp             x1, x16, [SP]
    // 0x13609e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13609e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13609ec: r0 = all()
    //     0x13609ec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13609f0: stur            x0, [fp, #-8]
    // 0x13609f4: r16 = <RoundedRectangleBorder>
    //     0x13609f4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13609f8: ldr             x16, [x16, #0xf78]
    // 0x13609fc: r30 = Instance_RoundedRectangleBorder
    //     0x13609fc: add             lr, PP, #0x40, lsl #12  ; [pp+0x406d0] Obj!RoundedRectangleBorder@d5acc1
    //     0x1360a00: ldr             lr, [lr, #0x6d0]
    // 0x1360a04: stp             lr, x16, [SP]
    // 0x1360a08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1360a08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1360a0c: r0 = all()
    //     0x1360a0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1360a10: stur            x0, [fp, #-0x20]
    // 0x1360a14: r0 = ButtonStyle()
    //     0x1360a14: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1360a18: mov             x1, x0
    // 0x1360a1c: ldur            x0, [fp, #-8]
    // 0x1360a20: stur            x1, [fp, #-0x28]
    // 0x1360a24: StoreField: r1->field_b = r0
    //     0x1360a24: stur            w0, [x1, #0xb]
    // 0x1360a28: ldur            x0, [fp, #-0x20]
    // 0x1360a2c: StoreField: r1->field_43 = r0
    //     0x1360a2c: stur            w0, [x1, #0x43]
    // 0x1360a30: r0 = TextButtonThemeData()
    //     0x1360a30: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1360a34: mov             x2, x0
    // 0x1360a38: ldur            x0, [fp, #-0x28]
    // 0x1360a3c: stur            x2, [fp, #-8]
    // 0x1360a40: StoreField: r2->field_7 = r0
    //     0x1360a40: stur            w0, [x2, #7]
    // 0x1360a44: ldur            x1, [fp, #-0x10]
    // 0x1360a48: r0 = of()
    //     0x1360a48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1360a4c: LoadField: r1 = r0->field_87
    //     0x1360a4c: ldur            w1, [x0, #0x87]
    // 0x1360a50: DecompressPointer r1
    //     0x1360a50: add             x1, x1, HEAP, lsl #32
    // 0x1360a54: LoadField: r0 = r1->field_2b
    //     0x1360a54: ldur            w0, [x1, #0x2b]
    // 0x1360a58: DecompressPointer r0
    //     0x1360a58: add             x0, x0, HEAP, lsl #32
    // 0x1360a5c: r16 = Instance_Color
    //     0x1360a5c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1360a60: r30 = 16.000000
    //     0x1360a60: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1360a64: ldr             lr, [lr, #0x188]
    // 0x1360a68: stp             lr, x16, [SP]
    // 0x1360a6c: mov             x1, x0
    // 0x1360a70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1360a70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1360a74: ldr             x4, [x4, #0x9b8]
    // 0x1360a78: r0 = copyWith()
    //     0x1360a78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1360a7c: stur            x0, [fp, #-0x10]
    // 0x1360a80: r0 = Text()
    //     0x1360a80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1360a84: mov             x3, x0
    // 0x1360a88: r0 = "Continue"
    //     0x1360a88: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x1360a8c: ldr             x0, [x0, #0xfe0]
    // 0x1360a90: stur            x3, [fp, #-0x20]
    // 0x1360a94: StoreField: r3->field_b = r0
    //     0x1360a94: stur            w0, [x3, #0xb]
    // 0x1360a98: ldur            x0, [fp, #-0x10]
    // 0x1360a9c: StoreField: r3->field_13 = r0
    //     0x1360a9c: stur            w0, [x3, #0x13]
    // 0x1360aa0: ldur            x2, [fp, #-0x18]
    // 0x1360aa4: r1 = Function '<anonymous closure>':.
    //     0x1360aa4: add             x1, PP, #0x40, lsl #12  ; [pp+0x406d8] AnonymousClosure: (0x1360b54), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x1360984)
    //     0x1360aa8: ldr             x1, [x1, #0x6d8]
    // 0x1360aac: r0 = AllocateClosure()
    //     0x1360aac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360ab0: stur            x0, [fp, #-0x10]
    // 0x1360ab4: r0 = TextButton()
    //     0x1360ab4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1360ab8: mov             x1, x0
    // 0x1360abc: ldur            x0, [fp, #-0x10]
    // 0x1360ac0: stur            x1, [fp, #-0x18]
    // 0x1360ac4: StoreField: r1->field_b = r0
    //     0x1360ac4: stur            w0, [x1, #0xb]
    // 0x1360ac8: r0 = false
    //     0x1360ac8: add             x0, NULL, #0x30  ; false
    // 0x1360acc: StoreField: r1->field_27 = r0
    //     0x1360acc: stur            w0, [x1, #0x27]
    // 0x1360ad0: r0 = true
    //     0x1360ad0: add             x0, NULL, #0x20  ; true
    // 0x1360ad4: StoreField: r1->field_2f = r0
    //     0x1360ad4: stur            w0, [x1, #0x2f]
    // 0x1360ad8: ldur            x0, [fp, #-0x20]
    // 0x1360adc: StoreField: r1->field_37 = r0
    //     0x1360adc: stur            w0, [x1, #0x37]
    // 0x1360ae0: r0 = TextButtonTheme()
    //     0x1360ae0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1360ae4: mov             x1, x0
    // 0x1360ae8: ldur            x0, [fp, #-8]
    // 0x1360aec: stur            x1, [fp, #-0x10]
    // 0x1360af0: StoreField: r1->field_f = r0
    //     0x1360af0: stur            w0, [x1, #0xf]
    // 0x1360af4: ldur            x0, [fp, #-0x18]
    // 0x1360af8: StoreField: r1->field_b = r0
    //     0x1360af8: stur            w0, [x1, #0xb]
    // 0x1360afc: r0 = SizedBox()
    //     0x1360afc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1360b00: mov             x1, x0
    // 0x1360b04: r0 = inf
    //     0x1360b04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1360b08: ldr             x0, [x0, #0x9f8]
    // 0x1360b0c: stur            x1, [fp, #-8]
    // 0x1360b10: StoreField: r1->field_f = r0
    //     0x1360b10: stur            w0, [x1, #0xf]
    // 0x1360b14: r0 = 44.000000
    //     0x1360b14: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0x1360b18: ldr             x0, [x0, #0xad8]
    // 0x1360b1c: StoreField: r1->field_13 = r0
    //     0x1360b1c: stur            w0, [x1, #0x13]
    // 0x1360b20: ldur            x0, [fp, #-0x10]
    // 0x1360b24: StoreField: r1->field_b = r0
    //     0x1360b24: stur            w0, [x1, #0xb]
    // 0x1360b28: r0 = Padding()
    //     0x1360b28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1360b2c: r1 = Instance_EdgeInsets
    //     0x1360b2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1360b30: ldr             x1, [x1, #0x1f0]
    // 0x1360b34: StoreField: r0->field_f = r1
    //     0x1360b34: stur            w1, [x0, #0xf]
    // 0x1360b38: ldur            x1, [fp, #-8]
    // 0x1360b3c: StoreField: r0->field_b = r1
    //     0x1360b3c: stur            w1, [x0, #0xb]
    // 0x1360b40: LeaveFrame
    //     0x1360b40: mov             SP, fp
    //     0x1360b44: ldp             fp, lr, [SP], #0x10
    // 0x1360b48: ret
    //     0x1360b48: ret             
    // 0x1360b4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1360b4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1360b50: b               #0x13609ac
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1360b54, size: 0x38c
    // 0x1360b54: EnterFrame
    //     0x1360b54: stp             fp, lr, [SP, #-0x10]!
    //     0x1360b58: mov             fp, SP
    // 0x1360b5c: AllocStack(0x38)
    //     0x1360b5c: sub             SP, SP, #0x38
    // 0x1360b60: SetupParameters()
    //     0x1360b60: ldr             x0, [fp, #0x10]
    //     0x1360b64: ldur            w2, [x0, #0x17]
    //     0x1360b68: add             x2, x2, HEAP, lsl #32
    //     0x1360b6c: stur            x2, [fp, #-8]
    // 0x1360b70: CheckStackOverflow
    //     0x1360b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1360b74: cmp             SP, x16
    //     0x1360b78: b.ls            #0x1360ed8
    // 0x1360b7c: LoadField: r1 = r2->field_f
    //     0x1360b7c: ldur            w1, [x2, #0xf]
    // 0x1360b80: DecompressPointer r1
    //     0x1360b80: add             x1, x1, HEAP, lsl #32
    // 0x1360b84: r0 = controller()
    //     0x1360b84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360b88: LoadField: r1 = r0->field_7f
    //     0x1360b88: ldur            w1, [x0, #0x7f]
    // 0x1360b8c: DecompressPointer r1
    //     0x1360b8c: add             x1, x1, HEAP, lsl #32
    // 0x1360b90: r0 = value()
    //     0x1360b90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360b94: LoadField: r1 = r0->field_b
    //     0x1360b94: ldur            w1, [x0, #0xb]
    // 0x1360b98: DecompressPointer r1
    //     0x1360b98: add             x1, x1, HEAP, lsl #32
    // 0x1360b9c: cmp             w1, NULL
    // 0x1360ba0: b.ne            #0x1360bac
    // 0x1360ba4: ldur            x2, [fp, #-8]
    // 0x1360ba8: b               #0x1360d60
    // 0x1360bac: LoadField: r0 = r1->field_7
    //     0x1360bac: ldur            w0, [x1, #7]
    // 0x1360bb0: DecompressPointer r0
    //     0x1360bb0: add             x0, x0, HEAP, lsl #32
    // 0x1360bb4: cmp             w0, NULL
    // 0x1360bb8: b.eq            #0x1360d5c
    // 0x1360bbc: ldur            x2, [fp, #-8]
    // 0x1360bc0: LoadField: r1 = r2->field_f
    //     0x1360bc0: ldur            w1, [x2, #0xf]
    // 0x1360bc4: DecompressPointer r1
    //     0x1360bc4: add             x1, x1, HEAP, lsl #32
    // 0x1360bc8: r0 = controller()
    //     0x1360bc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360bcc: LoadField: r1 = r0->field_73
    //     0x1360bcc: ldur            w1, [x0, #0x73]
    // 0x1360bd0: DecompressPointer r1
    //     0x1360bd0: add             x1, x1, HEAP, lsl #32
    // 0x1360bd4: r0 = value()
    //     0x1360bd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360bd8: r1 = LoadClassIdInstr(r0)
    //     0x1360bd8: ldur            x1, [x0, #-1]
    //     0x1360bdc: ubfx            x1, x1, #0xc, #0x14
    // 0x1360be0: r16 = "return"
    //     0x1360be0: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x1360be4: ldr             x16, [x16, #0x9b8]
    // 0x1360be8: stp             x16, x0, [SP]
    // 0x1360bec: mov             x0, x1
    // 0x1360bf0: mov             lr, x0
    // 0x1360bf4: ldr             lr, [x21, lr, lsl #3]
    // 0x1360bf8: blr             lr
    // 0x1360bfc: tbnz            w0, #4, #0x1360cf0
    // 0x1360c00: ldur            x2, [fp, #-8]
    // 0x1360c04: LoadField: r1 = r2->field_f
    //     0x1360c04: ldur            w1, [x2, #0xf]
    // 0x1360c08: DecompressPointer r1
    //     0x1360c08: add             x1, x1, HEAP, lsl #32
    // 0x1360c0c: r0 = controller()
    //     0x1360c0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360c10: LoadField: r1 = r0->field_4f
    //     0x1360c10: ldur            w1, [x0, #0x4f]
    // 0x1360c14: DecompressPointer r1
    //     0x1360c14: add             x1, x1, HEAP, lsl #32
    // 0x1360c18: r0 = value()
    //     0x1360c18: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360c1c: LoadField: r1 = r0->field_b
    //     0x1360c1c: ldur            w1, [x0, #0xb]
    // 0x1360c20: DecompressPointer r1
    //     0x1360c20: add             x1, x1, HEAP, lsl #32
    // 0x1360c24: cmp             w1, NULL
    // 0x1360c28: b.ne            #0x1360c34
    // 0x1360c2c: r0 = Null
    //     0x1360c2c: mov             x0, NULL
    // 0x1360c30: b               #0x1360c58
    // 0x1360c34: LoadField: r0 = r1->field_3f
    //     0x1360c34: ldur            w0, [x1, #0x3f]
    // 0x1360c38: DecompressPointer r0
    //     0x1360c38: add             x0, x0, HEAP, lsl #32
    // 0x1360c3c: cmp             w0, NULL
    // 0x1360c40: b.ne            #0x1360c4c
    // 0x1360c44: r0 = Null
    //     0x1360c44: mov             x0, NULL
    // 0x1360c48: b               #0x1360c58
    // 0x1360c4c: LoadField: r1 = r0->field_2b
    //     0x1360c4c: ldur            w1, [x0, #0x2b]
    // 0x1360c50: DecompressPointer r1
    //     0x1360c50: add             x1, x1, HEAP, lsl #32
    // 0x1360c54: mov             x0, x1
    // 0x1360c58: cmp             w0, NULL
    // 0x1360c5c: b.ne            #0x1360c68
    // 0x1360c60: ldur            x2, [fp, #-8]
    // 0x1360c64: b               #0x1360cb8
    // 0x1360c68: tbnz            w0, #4, #0x1360cb4
    // 0x1360c6c: ldur            x2, [fp, #-8]
    // 0x1360c70: LoadField: r0 = r2->field_13
    //     0x1360c70: ldur            w0, [x2, #0x13]
    // 0x1360c74: DecompressPointer r0
    //     0x1360c74: add             x0, x0, HEAP, lsl #32
    // 0x1360c78: stur            x0, [fp, #-0x10]
    // 0x1360c7c: r1 = Function '<anonymous closure>':.
    //     0x1360c7c: add             x1, PP, #0x40, lsl #12  ; [pp+0x406e0] AnonymousClosure: (0x1361120), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x1360984)
    //     0x1360c80: ldr             x1, [x1, #0x6e0]
    // 0x1360c84: r0 = AllocateClosure()
    //     0x1360c84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360c88: stp             x0, NULL, [SP, #0x18]
    // 0x1360c8c: ldur            x16, [fp, #-0x10]
    // 0x1360c90: r30 = Instance_RoundedRectangleBorder
    //     0x1360c90: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1360c94: ldr             lr, [lr, #0xc78]
    // 0x1360c98: stp             lr, x16, [SP, #8]
    // 0x1360c9c: r16 = true
    //     0x1360c9c: add             x16, NULL, #0x20  ; true
    // 0x1360ca0: str             x16, [SP]
    // 0x1360ca4: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x1360ca4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x1360ca8: ldr             x4, [x4, #0xd70]
    // 0x1360cac: r0 = showModalBottomSheet()
    //     0x1360cac: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1360cb0: b               #0x1360ec8
    // 0x1360cb4: ldur            x2, [fp, #-8]
    // 0x1360cb8: LoadField: r1 = r2->field_f
    //     0x1360cb8: ldur            w1, [x2, #0xf]
    // 0x1360cbc: DecompressPointer r1
    //     0x1360cbc: add             x1, x1, HEAP, lsl #32
    // 0x1360cc0: r0 = controller()
    //     0x1360cc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360cc4: mov             x1, x0
    // 0x1360cc8: r2 = "return_exchange_continue"
    //     0x1360cc8: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x1360ccc: ldr             x2, [x2, #0x718]
    // 0x1360cd0: r0 = ctaPostEvent()
    //     0x1360cd0: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x1360cd4: ldur            x2, [fp, #-8]
    // 0x1360cd8: LoadField: r1 = r2->field_f
    //     0x1360cd8: ldur            w1, [x2, #0xf]
    // 0x1360cdc: DecompressPointer r1
    //     0x1360cdc: add             x1, x1, HEAP, lsl #32
    // 0x1360ce0: r0 = controller()
    //     0x1360ce0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360ce4: mov             x1, x0
    // 0x1360ce8: r0 = navigateToScreen()
    //     0x1360ce8: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x1360cec: b               #0x1360ec8
    // 0x1360cf0: ldur            x2, [fp, #-8]
    // 0x1360cf4: LoadField: r1 = r2->field_f
    //     0x1360cf4: ldur            w1, [x2, #0xf]
    // 0x1360cf8: DecompressPointer r1
    //     0x1360cf8: add             x1, x1, HEAP, lsl #32
    // 0x1360cfc: r0 = controller()
    //     0x1360cfc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360d00: mov             x1, x0
    // 0x1360d04: r2 = "Continue"
    //     0x1360d04: add             x2, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x1360d08: ldr             x2, [x2, #0xfe0]
    // 0x1360d0c: r0 = exchangePopupPostEvent()
    //     0x1360d0c: bl              #0x1318d64  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::exchangePopupPostEvent
    // 0x1360d10: ldur            x2, [fp, #-8]
    // 0x1360d14: LoadField: r0 = r2->field_13
    //     0x1360d14: ldur            w0, [x2, #0x13]
    // 0x1360d18: DecompressPointer r0
    //     0x1360d18: add             x0, x0, HEAP, lsl #32
    // 0x1360d1c: stur            x0, [fp, #-0x10]
    // 0x1360d20: r1 = Function '<anonymous closure>':.
    //     0x1360d20: add             x1, PP, #0x40, lsl #12  ; [pp+0x406e8] AnonymousClosure: (0x13610a4), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x1360984)
    //     0x1360d24: ldr             x1, [x1, #0x6e8]
    // 0x1360d28: r2 = Null
    //     0x1360d28: mov             x2, NULL
    // 0x1360d2c: r0 = AllocateClosure()
    //     0x1360d2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360d30: stp             x0, NULL, [SP, #0x18]
    // 0x1360d34: ldur            x16, [fp, #-0x10]
    // 0x1360d38: r30 = Instance_RoundedRectangleBorder
    //     0x1360d38: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1360d3c: ldr             lr, [lr, #0xc78]
    // 0x1360d40: stp             lr, x16, [SP, #8]
    // 0x1360d44: r16 = true
    //     0x1360d44: add             x16, NULL, #0x20  ; true
    // 0x1360d48: str             x16, [SP]
    // 0x1360d4c: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x1360d4c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x1360d50: ldr             x4, [x4, #0xd70]
    // 0x1360d54: r0 = showModalBottomSheet()
    //     0x1360d54: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1360d58: b               #0x1360ec8
    // 0x1360d5c: ldur            x2, [fp, #-8]
    // 0x1360d60: LoadField: r1 = r2->field_f
    //     0x1360d60: ldur            w1, [x2, #0xf]
    // 0x1360d64: DecompressPointer r1
    //     0x1360d64: add             x1, x1, HEAP, lsl #32
    // 0x1360d68: r0 = controller()
    //     0x1360d68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360d6c: LoadField: r1 = r0->field_73
    //     0x1360d6c: ldur            w1, [x0, #0x73]
    // 0x1360d70: DecompressPointer r1
    //     0x1360d70: add             x1, x1, HEAP, lsl #32
    // 0x1360d74: r0 = value()
    //     0x1360d74: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360d78: r1 = LoadClassIdInstr(r0)
    //     0x1360d78: ldur            x1, [x0, #-1]
    //     0x1360d7c: ubfx            x1, x1, #0xc, #0x14
    // 0x1360d80: r16 = "return"
    //     0x1360d80: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x1360d84: ldr             x16, [x16, #0x9b8]
    // 0x1360d88: stp             x16, x0, [SP]
    // 0x1360d8c: mov             x0, x1
    // 0x1360d90: mov             lr, x0
    // 0x1360d94: ldr             lr, [x21, lr, lsl #3]
    // 0x1360d98: blr             lr
    // 0x1360d9c: tbnz            w0, #4, #0x1360e90
    // 0x1360da0: ldur            x2, [fp, #-8]
    // 0x1360da4: LoadField: r1 = r2->field_f
    //     0x1360da4: ldur            w1, [x2, #0xf]
    // 0x1360da8: DecompressPointer r1
    //     0x1360da8: add             x1, x1, HEAP, lsl #32
    // 0x1360dac: r0 = controller()
    //     0x1360dac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360db0: LoadField: r1 = r0->field_4f
    //     0x1360db0: ldur            w1, [x0, #0x4f]
    // 0x1360db4: DecompressPointer r1
    //     0x1360db4: add             x1, x1, HEAP, lsl #32
    // 0x1360db8: r0 = value()
    //     0x1360db8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360dbc: LoadField: r1 = r0->field_b
    //     0x1360dbc: ldur            w1, [x0, #0xb]
    // 0x1360dc0: DecompressPointer r1
    //     0x1360dc0: add             x1, x1, HEAP, lsl #32
    // 0x1360dc4: cmp             w1, NULL
    // 0x1360dc8: b.ne            #0x1360dd4
    // 0x1360dcc: r0 = Null
    //     0x1360dcc: mov             x0, NULL
    // 0x1360dd0: b               #0x1360df8
    // 0x1360dd4: LoadField: r0 = r1->field_3f
    //     0x1360dd4: ldur            w0, [x1, #0x3f]
    // 0x1360dd8: DecompressPointer r0
    //     0x1360dd8: add             x0, x0, HEAP, lsl #32
    // 0x1360ddc: cmp             w0, NULL
    // 0x1360de0: b.ne            #0x1360dec
    // 0x1360de4: r0 = Null
    //     0x1360de4: mov             x0, NULL
    // 0x1360de8: b               #0x1360df8
    // 0x1360dec: LoadField: r1 = r0->field_2b
    //     0x1360dec: ldur            w1, [x0, #0x2b]
    // 0x1360df0: DecompressPointer r1
    //     0x1360df0: add             x1, x1, HEAP, lsl #32
    // 0x1360df4: mov             x0, x1
    // 0x1360df8: cmp             w0, NULL
    // 0x1360dfc: b.ne            #0x1360e08
    // 0x1360e00: ldur            x2, [fp, #-8]
    // 0x1360e04: b               #0x1360e58
    // 0x1360e08: tbnz            w0, #4, #0x1360e54
    // 0x1360e0c: ldur            x2, [fp, #-8]
    // 0x1360e10: LoadField: r0 = r2->field_13
    //     0x1360e10: ldur            w0, [x2, #0x13]
    // 0x1360e14: DecompressPointer r0
    //     0x1360e14: add             x0, x0, HEAP, lsl #32
    // 0x1360e18: stur            x0, [fp, #-0x10]
    // 0x1360e1c: r1 = Function '<anonymous closure>':.
    //     0x1360e1c: add             x1, PP, #0x40, lsl #12  ; [pp+0x406f0] AnonymousClosure: (0x1360ee0), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x1360984)
    //     0x1360e20: ldr             x1, [x1, #0x6f0]
    // 0x1360e24: r0 = AllocateClosure()
    //     0x1360e24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360e28: stp             x0, NULL, [SP, #0x18]
    // 0x1360e2c: ldur            x16, [fp, #-0x10]
    // 0x1360e30: r30 = Instance_RoundedRectangleBorder
    //     0x1360e30: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1360e34: ldr             lr, [lr, #0xc78]
    // 0x1360e38: stp             lr, x16, [SP, #8]
    // 0x1360e3c: r16 = true
    //     0x1360e3c: add             x16, NULL, #0x20  ; true
    // 0x1360e40: str             x16, [SP]
    // 0x1360e44: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x1360e44: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x1360e48: ldr             x4, [x4, #0xd70]
    // 0x1360e4c: r0 = showModalBottomSheet()
    //     0x1360e4c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1360e50: b               #0x1360ec8
    // 0x1360e54: ldur            x2, [fp, #-8]
    // 0x1360e58: LoadField: r1 = r2->field_f
    //     0x1360e58: ldur            w1, [x2, #0xf]
    // 0x1360e5c: DecompressPointer r1
    //     0x1360e5c: add             x1, x1, HEAP, lsl #32
    // 0x1360e60: r0 = controller()
    //     0x1360e60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360e64: mov             x1, x0
    // 0x1360e68: r2 = "return_exchange_continue"
    //     0x1360e68: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x1360e6c: ldr             x2, [x2, #0x718]
    // 0x1360e70: r0 = ctaPostEvent()
    //     0x1360e70: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x1360e74: ldur            x0, [fp, #-8]
    // 0x1360e78: LoadField: r1 = r0->field_f
    //     0x1360e78: ldur            w1, [x0, #0xf]
    // 0x1360e7c: DecompressPointer r1
    //     0x1360e7c: add             x1, x1, HEAP, lsl #32
    // 0x1360e80: r0 = controller()
    //     0x1360e80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360e84: mov             x1, x0
    // 0x1360e88: r0 = navigateToScreen()
    //     0x1360e88: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x1360e8c: b               #0x1360ec8
    // 0x1360e90: ldur            x0, [fp, #-8]
    // 0x1360e94: LoadField: r1 = r0->field_f
    //     0x1360e94: ldur            w1, [x0, #0xf]
    // 0x1360e98: DecompressPointer r1
    //     0x1360e98: add             x1, x1, HEAP, lsl #32
    // 0x1360e9c: r0 = controller()
    //     0x1360e9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360ea0: mov             x1, x0
    // 0x1360ea4: r2 = "return_exchange_continue"
    //     0x1360ea4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x1360ea8: ldr             x2, [x2, #0x718]
    // 0x1360eac: r0 = ctaPostEvent()
    //     0x1360eac: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x1360eb0: ldur            x0, [fp, #-8]
    // 0x1360eb4: LoadField: r1 = r0->field_f
    //     0x1360eb4: ldur            w1, [x0, #0xf]
    // 0x1360eb8: DecompressPointer r1
    //     0x1360eb8: add             x1, x1, HEAP, lsl #32
    // 0x1360ebc: r0 = controller()
    //     0x1360ebc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360ec0: mov             x1, x0
    // 0x1360ec4: r0 = navigateToScreen()
    //     0x1360ec4: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x1360ec8: r0 = Null
    //     0x1360ec8: mov             x0, NULL
    // 0x1360ecc: LeaveFrame
    //     0x1360ecc: mov             SP, fp
    //     0x1360ed0: ldp             fp, lr, [SP], #0x10
    // 0x1360ed4: ret
    //     0x1360ed4: ret             
    // 0x1360ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1360ed8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1360edc: b               #0x1360b7c
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1360ee0, size: 0x1b8
    // 0x1360ee0: EnterFrame
    //     0x1360ee0: stp             fp, lr, [SP, #-0x10]!
    //     0x1360ee4: mov             fp, SP
    // 0x1360ee8: AllocStack(0x28)
    //     0x1360ee8: sub             SP, SP, #0x28
    // 0x1360eec: SetupParameters()
    //     0x1360eec: ldr             x0, [fp, #0x18]
    //     0x1360ef0: ldur            w2, [x0, #0x17]
    //     0x1360ef4: add             x2, x2, HEAP, lsl #32
    //     0x1360ef8: stur            x2, [fp, #-8]
    // 0x1360efc: CheckStackOverflow
    //     0x1360efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1360f00: cmp             SP, x16
    //     0x1360f04: b.ls            #0x1361090
    // 0x1360f08: LoadField: r1 = r2->field_f
    //     0x1360f08: ldur            w1, [x2, #0xf]
    // 0x1360f0c: DecompressPointer r1
    //     0x1360f0c: add             x1, x1, HEAP, lsl #32
    // 0x1360f10: r0 = controller()
    //     0x1360f10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1360f14: LoadField: r1 = r0->field_4f
    //     0x1360f14: ldur            w1, [x0, #0x4f]
    // 0x1360f18: DecompressPointer r1
    //     0x1360f18: add             x1, x1, HEAP, lsl #32
    // 0x1360f1c: r0 = value()
    //     0x1360f1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1360f20: LoadField: r1 = r0->field_b
    //     0x1360f20: ldur            w1, [x0, #0xb]
    // 0x1360f24: DecompressPointer r1
    //     0x1360f24: add             x1, x1, HEAP, lsl #32
    // 0x1360f28: cmp             w1, NULL
    // 0x1360f2c: b.ne            #0x1360f38
    // 0x1360f30: d0 = inf
    //     0x1360f30: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1360f34: b               #0x1360fa0
    // 0x1360f38: LoadField: r0 = r1->field_3f
    //     0x1360f38: ldur            w0, [x1, #0x3f]
    // 0x1360f3c: DecompressPointer r0
    //     0x1360f3c: add             x0, x0, HEAP, lsl #32
    // 0x1360f40: cmp             w0, NULL
    // 0x1360f44: b.eq            #0x1360f9c
    // 0x1360f48: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1360f48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1360f4c: ldr             x0, [x0, #0x1c80]
    //     0x1360f50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1360f54: cmp             w0, w16
    //     0x1360f58: b.ne            #0x1360f64
    //     0x1360f5c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1360f60: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1360f64: r0 = GetNavigation.size()
    //     0x1360f64: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1360f68: LoadField: d0 = r0->field_f
    //     0x1360f68: ldur            d0, [x0, #0xf]
    // 0x1360f6c: d1 = 0.430000
    //     0x1360f6c: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x1360f70: ldr             d1, [x17, #0xb8]
    // 0x1360f74: fmul            d2, d0, d1
    // 0x1360f78: stur            d2, [fp, #-0x28]
    // 0x1360f7c: r0 = BoxConstraints()
    //     0x1360f7c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1360f80: StoreField: r0->field_7 = rZR
    //     0x1360f80: stur            xzr, [x0, #7]
    // 0x1360f84: d0 = inf
    //     0x1360f84: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1360f88: StoreField: r0->field_f = d0
    //     0x1360f88: stur            d0, [x0, #0xf]
    // 0x1360f8c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1360f8c: stur            xzr, [x0, #0x17]
    // 0x1360f90: ldur            d0, [fp, #-0x28]
    // 0x1360f94: StoreField: r0->field_1f = d0
    //     0x1360f94: stur            d0, [x0, #0x1f]
    // 0x1360f98: b               #0x1360ff0
    // 0x1360f9c: d0 = inf
    //     0x1360f9c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1360fa0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1360fa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1360fa4: ldr             x0, [x0, #0x1c80]
    //     0x1360fa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1360fac: cmp             w0, w16
    //     0x1360fb0: b.ne            #0x1360fbc
    //     0x1360fb4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1360fb8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1360fbc: r0 = GetNavigation.size()
    //     0x1360fbc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1360fc0: LoadField: d0 = r0->field_f
    //     0x1360fc0: ldur            d0, [x0, #0xf]
    // 0x1360fc4: d1 = 0.300000
    //     0x1360fc4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x1360fc8: ldr             d1, [x17, #0x658]
    // 0x1360fcc: fmul            d2, d0, d1
    // 0x1360fd0: stur            d2, [fp, #-0x28]
    // 0x1360fd4: r0 = BoxConstraints()
    //     0x1360fd4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1360fd8: StoreField: r0->field_7 = rZR
    //     0x1360fd8: stur            xzr, [x0, #7]
    // 0x1360fdc: d0 = inf
    //     0x1360fdc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1360fe0: StoreField: r0->field_f = d0
    //     0x1360fe0: stur            d0, [x0, #0xf]
    // 0x1360fe4: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1360fe4: stur            xzr, [x0, #0x17]
    // 0x1360fe8: ldur            d0, [fp, #-0x28]
    // 0x1360fec: StoreField: r0->field_1f = d0
    //     0x1360fec: stur            d0, [x0, #0x1f]
    // 0x1360ff0: ldur            x2, [fp, #-8]
    // 0x1360ff4: stur            x0, [fp, #-0x10]
    // 0x1360ff8: LoadField: r1 = r2->field_f
    //     0x1360ff8: ldur            w1, [x2, #0xf]
    // 0x1360ffc: DecompressPointer r1
    //     0x1360ffc: add             x1, x1, HEAP, lsl #32
    // 0x1361000: r0 = controller()
    //     0x1361000: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361004: LoadField: r1 = r0->field_4f
    //     0x1361004: ldur            w1, [x0, #0x4f]
    // 0x1361008: DecompressPointer r1
    //     0x1361008: add             x1, x1, HEAP, lsl #32
    // 0x136100c: r0 = value()
    //     0x136100c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361010: LoadField: r3 = r0->field_b
    //     0x1361010: ldur            w3, [x0, #0xb]
    // 0x1361014: DecompressPointer r3
    //     0x1361014: add             x3, x3, HEAP, lsl #32
    // 0x1361018: ldur            x2, [fp, #-8]
    // 0x136101c: stur            x3, [fp, #-0x18]
    // 0x1361020: r1 = Function '<anonymous closure>':.
    //     0x1361020: add             x1, PP, #0x40, lsl #12  ; [pp+0x406f8] AnonymousClosure: (0x131cffc), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x1361024: ldr             x1, [x1, #0x6f8]
    // 0x1361028: r0 = AllocateClosure()
    //     0x1361028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x136102c: stur            x0, [fp, #-8]
    // 0x1361030: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x1361030: bl              #0x1361098  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x1361034: mov             x1, x0
    // 0x1361038: ldur            x0, [fp, #-8]
    // 0x136103c: stur            x1, [fp, #-0x20]
    // 0x1361040: StoreField: r1->field_b = r0
    //     0x1361040: stur            w0, [x1, #0xb]
    // 0x1361044: r0 = "Confirm Return"
    //     0x1361044: add             x0, PP, #0x36, lsl #12  ; [pp+0x36220] "Confirm Return"
    //     0x1361048: ldr             x0, [x0, #0x220]
    // 0x136104c: StoreField: r1->field_13 = r0
    //     0x136104c: stur            w0, [x1, #0x13]
    // 0x1361050: r0 = "Returning this product requires returning the free gift as well"
    //     0x1361050: add             x0, PP, #0x36, lsl #12  ; [pp+0x36228] "Returning this product requires returning the free gift as well"
    //     0x1361054: ldr             x0, [x0, #0x228]
    // 0x1361058: ArrayStore: r1[0] = r0  ; List_4
    //     0x1361058: stur            w0, [x1, #0x17]
    // 0x136105c: r0 = "return_order_intermediate"
    //     0x136105c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0x1361060: ldr             x0, [x0, #0xb00]
    // 0x1361064: StoreField: r1->field_f = r0
    //     0x1361064: stur            w0, [x1, #0xf]
    // 0x1361068: ldur            x0, [fp, #-0x18]
    // 0x136106c: StoreField: r1->field_23 = r0
    //     0x136106c: stur            w0, [x1, #0x23]
    // 0x1361070: r0 = ConstrainedBox()
    //     0x1361070: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1361074: ldur            x1, [fp, #-0x10]
    // 0x1361078: StoreField: r0->field_f = r1
    //     0x1361078: stur            w1, [x0, #0xf]
    // 0x136107c: ldur            x1, [fp, #-0x20]
    // 0x1361080: StoreField: r0->field_b = r1
    //     0x1361080: stur            w1, [x0, #0xb]
    // 0x1361084: LeaveFrame
    //     0x1361084: mov             SP, fp
    //     0x1361088: ldp             fp, lr, [SP], #0x10
    // 0x136108c: ret
    //     0x136108c: ret             
    // 0x1361090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1361090: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1361094: b               #0x1360f08
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x13610a4, size: 0x7c
    // 0x13610a4: EnterFrame
    //     0x13610a4: stp             fp, lr, [SP, #-0x10]!
    //     0x13610a8: mov             fp, SP
    // 0x13610ac: AllocStack(0x10)
    //     0x13610ac: sub             SP, SP, #0x10
    // 0x13610b0: CheckStackOverflow
    //     0x13610b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13610b4: cmp             SP, x16
    //     0x13610b8: b.ls            #0x1361118
    // 0x13610bc: ldr             x1, [fp, #0x10]
    // 0x13610c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13610c0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13610c4: r0 = _of()
    //     0x13610c4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13610c8: LoadField: r1 = r0->field_23
    //     0x13610c8: ldur            w1, [x0, #0x23]
    // 0x13610cc: DecompressPointer r1
    //     0x13610cc: add             x1, x1, HEAP, lsl #32
    // 0x13610d0: LoadField: d0 = r1->field_1f
    //     0x13610d0: ldur            d0, [x1, #0x1f]
    // 0x13610d4: stur            d0, [fp, #-0x10]
    // 0x13610d8: r0 = EdgeInsets()
    //     0x13610d8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x13610dc: stur            x0, [fp, #-8]
    // 0x13610e0: StoreField: r0->field_7 = rZR
    //     0x13610e0: stur            xzr, [x0, #7]
    // 0x13610e4: StoreField: r0->field_f = rZR
    //     0x13610e4: stur            xzr, [x0, #0xf]
    // 0x13610e8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13610e8: stur            xzr, [x0, #0x17]
    // 0x13610ec: ldur            d0, [fp, #-0x10]
    // 0x13610f0: StoreField: r0->field_1f = d0
    //     0x13610f0: stur            d0, [x0, #0x1f]
    // 0x13610f4: r0 = Padding()
    //     0x13610f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13610f8: ldur            x1, [fp, #-8]
    // 0x13610fc: StoreField: r0->field_f = r1
    //     0x13610fc: stur            w1, [x0, #0xf]
    // 0x1361100: r1 = Instance_SizedBox
    //     0x1361100: add             x1, PP, #0x40, lsl #12  ; [pp+0x40700] Obj!SizedBox@d681c1
    //     0x1361104: ldr             x1, [x1, #0x700]
    // 0x1361108: StoreField: r0->field_b = r1
    //     0x1361108: stur            w1, [x0, #0xb]
    // 0x136110c: LeaveFrame
    //     0x136110c: mov             SP, fp
    //     0x1361110: ldp             fp, lr, [SP], #0x10
    // 0x1361114: ret
    //     0x1361114: ret             
    // 0x1361118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1361118: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x136111c: b               #0x13610bc
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1361120, size: 0x1b8
    // 0x1361120: EnterFrame
    //     0x1361120: stp             fp, lr, [SP, #-0x10]!
    //     0x1361124: mov             fp, SP
    // 0x1361128: AllocStack(0x28)
    //     0x1361128: sub             SP, SP, #0x28
    // 0x136112c: SetupParameters()
    //     0x136112c: ldr             x0, [fp, #0x18]
    //     0x1361130: ldur            w2, [x0, #0x17]
    //     0x1361134: add             x2, x2, HEAP, lsl #32
    //     0x1361138: stur            x2, [fp, #-8]
    // 0x136113c: CheckStackOverflow
    //     0x136113c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1361140: cmp             SP, x16
    //     0x1361144: b.ls            #0x13612d0
    // 0x1361148: LoadField: r1 = r2->field_f
    //     0x1361148: ldur            w1, [x2, #0xf]
    // 0x136114c: DecompressPointer r1
    //     0x136114c: add             x1, x1, HEAP, lsl #32
    // 0x1361150: r0 = controller()
    //     0x1361150: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361154: LoadField: r1 = r0->field_4f
    //     0x1361154: ldur            w1, [x0, #0x4f]
    // 0x1361158: DecompressPointer r1
    //     0x1361158: add             x1, x1, HEAP, lsl #32
    // 0x136115c: r0 = value()
    //     0x136115c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361160: LoadField: r1 = r0->field_b
    //     0x1361160: ldur            w1, [x0, #0xb]
    // 0x1361164: DecompressPointer r1
    //     0x1361164: add             x1, x1, HEAP, lsl #32
    // 0x1361168: cmp             w1, NULL
    // 0x136116c: b.ne            #0x1361178
    // 0x1361170: d0 = inf
    //     0x1361170: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1361174: b               #0x13611e0
    // 0x1361178: LoadField: r0 = r1->field_3f
    //     0x1361178: ldur            w0, [x1, #0x3f]
    // 0x136117c: DecompressPointer r0
    //     0x136117c: add             x0, x0, HEAP, lsl #32
    // 0x1361180: cmp             w0, NULL
    // 0x1361184: b.eq            #0x13611dc
    // 0x1361188: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1361188: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x136118c: ldr             x0, [x0, #0x1c80]
    //     0x1361190: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1361194: cmp             w0, w16
    //     0x1361198: b.ne            #0x13611a4
    //     0x136119c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13611a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13611a4: r0 = GetNavigation.size()
    //     0x13611a4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13611a8: LoadField: d0 = r0->field_f
    //     0x13611a8: ldur            d0, [x0, #0xf]
    // 0x13611ac: d1 = 0.430000
    //     0x13611ac: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x13611b0: ldr             d1, [x17, #0xb8]
    // 0x13611b4: fmul            d2, d0, d1
    // 0x13611b8: stur            d2, [fp, #-0x28]
    // 0x13611bc: r0 = BoxConstraints()
    //     0x13611bc: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x13611c0: StoreField: r0->field_7 = rZR
    //     0x13611c0: stur            xzr, [x0, #7]
    // 0x13611c4: d0 = inf
    //     0x13611c4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x13611c8: StoreField: r0->field_f = d0
    //     0x13611c8: stur            d0, [x0, #0xf]
    // 0x13611cc: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13611cc: stur            xzr, [x0, #0x17]
    // 0x13611d0: ldur            d0, [fp, #-0x28]
    // 0x13611d4: StoreField: r0->field_1f = d0
    //     0x13611d4: stur            d0, [x0, #0x1f]
    // 0x13611d8: b               #0x1361230
    // 0x13611dc: d0 = inf
    //     0x13611dc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x13611e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13611e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13611e4: ldr             x0, [x0, #0x1c80]
    //     0x13611e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13611ec: cmp             w0, w16
    //     0x13611f0: b.ne            #0x13611fc
    //     0x13611f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13611f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13611fc: r0 = GetNavigation.size()
    //     0x13611fc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1361200: LoadField: d0 = r0->field_f
    //     0x1361200: ldur            d0, [x0, #0xf]
    // 0x1361204: d1 = 0.300000
    //     0x1361204: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x1361208: ldr             d1, [x17, #0x658]
    // 0x136120c: fmul            d2, d0, d1
    // 0x1361210: stur            d2, [fp, #-0x28]
    // 0x1361214: r0 = BoxConstraints()
    //     0x1361214: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1361218: StoreField: r0->field_7 = rZR
    //     0x1361218: stur            xzr, [x0, #7]
    // 0x136121c: d0 = inf
    //     0x136121c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1361220: StoreField: r0->field_f = d0
    //     0x1361220: stur            d0, [x0, #0xf]
    // 0x1361224: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1361224: stur            xzr, [x0, #0x17]
    // 0x1361228: ldur            d0, [fp, #-0x28]
    // 0x136122c: StoreField: r0->field_1f = d0
    //     0x136122c: stur            d0, [x0, #0x1f]
    // 0x1361230: ldur            x2, [fp, #-8]
    // 0x1361234: stur            x0, [fp, #-0x10]
    // 0x1361238: LoadField: r1 = r2->field_f
    //     0x1361238: ldur            w1, [x2, #0xf]
    // 0x136123c: DecompressPointer r1
    //     0x136123c: add             x1, x1, HEAP, lsl #32
    // 0x1361240: r0 = controller()
    //     0x1361240: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361244: LoadField: r1 = r0->field_4f
    //     0x1361244: ldur            w1, [x0, #0x4f]
    // 0x1361248: DecompressPointer r1
    //     0x1361248: add             x1, x1, HEAP, lsl #32
    // 0x136124c: r0 = value()
    //     0x136124c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361250: LoadField: r3 = r0->field_b
    //     0x1361250: ldur            w3, [x0, #0xb]
    // 0x1361254: DecompressPointer r3
    //     0x1361254: add             x3, x3, HEAP, lsl #32
    // 0x1361258: ldur            x2, [fp, #-8]
    // 0x136125c: stur            x3, [fp, #-0x18]
    // 0x1361260: r1 = Function '<anonymous closure>':.
    //     0x1361260: add             x1, PP, #0x40, lsl #12  ; [pp+0x40708] AnonymousClosure: (0x131cffc), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x1361264: ldr             x1, [x1, #0x708]
    // 0x1361268: r0 = AllocateClosure()
    //     0x1361268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x136126c: stur            x0, [fp, #-8]
    // 0x1361270: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x1361270: bl              #0x1361098  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x1361274: mov             x1, x0
    // 0x1361278: ldur            x0, [fp, #-8]
    // 0x136127c: stur            x1, [fp, #-0x20]
    // 0x1361280: StoreField: r1->field_b = r0
    //     0x1361280: stur            w0, [x1, #0xb]
    // 0x1361284: r0 = "Confirm Return"
    //     0x1361284: add             x0, PP, #0x36, lsl #12  ; [pp+0x36220] "Confirm Return"
    //     0x1361288: ldr             x0, [x0, #0x220]
    // 0x136128c: StoreField: r1->field_13 = r0
    //     0x136128c: stur            w0, [x1, #0x13]
    // 0x1361290: r0 = "Returning this product requires returning the free gift as well"
    //     0x1361290: add             x0, PP, #0x36, lsl #12  ; [pp+0x36228] "Returning this product requires returning the free gift as well"
    //     0x1361294: ldr             x0, [x0, #0x228]
    // 0x1361298: ArrayStore: r1[0] = r0  ; List_4
    //     0x1361298: stur            w0, [x1, #0x17]
    // 0x136129c: r0 = "return_order_intermediate"
    //     0x136129c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0x13612a0: ldr             x0, [x0, #0xb00]
    // 0x13612a4: StoreField: r1->field_f = r0
    //     0x13612a4: stur            w0, [x1, #0xf]
    // 0x13612a8: ldur            x0, [fp, #-0x18]
    // 0x13612ac: StoreField: r1->field_23 = r0
    //     0x13612ac: stur            w0, [x1, #0x23]
    // 0x13612b0: r0 = ConstrainedBox()
    //     0x13612b0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x13612b4: ldur            x1, [fp, #-0x10]
    // 0x13612b8: StoreField: r0->field_f = r1
    //     0x13612b8: stur            w1, [x0, #0xf]
    // 0x13612bc: ldur            x1, [fp, #-0x20]
    // 0x13612c0: StoreField: r0->field_b = r1
    //     0x13612c0: stur            w1, [x0, #0xb]
    // 0x13612c4: LeaveFrame
    //     0x13612c4: mov             SP, fp
    //     0x13612c8: ldp             fp, lr, [SP], #0x10
    // 0x13612cc: ret
    //     0x13612cc: ret             
    // 0x13612d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13612d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13612d4: b               #0x1361148
  }
  _ body(/* No info */) {
    // ** addr: 0x14e3ec4, size: 0x64
    // 0x14e3ec4: EnterFrame
    //     0x14e3ec4: stp             fp, lr, [SP, #-0x10]!
    //     0x14e3ec8: mov             fp, SP
    // 0x14e3ecc: AllocStack(0x18)
    //     0x14e3ecc: sub             SP, SP, #0x18
    // 0x14e3ed0: SetupParameters(ExchangeReturnIntermediateScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14e3ed0: stur            x1, [fp, #-8]
    //     0x14e3ed4: stur            x2, [fp, #-0x10]
    // 0x14e3ed8: r1 = 2
    //     0x14e3ed8: movz            x1, #0x2
    // 0x14e3edc: r0 = AllocateContext()
    //     0x14e3edc: bl              #0x16f6108  ; AllocateContextStub
    // 0x14e3ee0: mov             x1, x0
    // 0x14e3ee4: ldur            x0, [fp, #-8]
    // 0x14e3ee8: stur            x1, [fp, #-0x18]
    // 0x14e3eec: StoreField: r1->field_f = r0
    //     0x14e3eec: stur            w0, [x1, #0xf]
    // 0x14e3ef0: ldur            x0, [fp, #-0x10]
    // 0x14e3ef4: StoreField: r1->field_13 = r0
    //     0x14e3ef4: stur            w0, [x1, #0x13]
    // 0x14e3ef8: r0 = Obx()
    //     0x14e3ef8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14e3efc: ldur            x2, [fp, #-0x18]
    // 0x14e3f00: r1 = Function '<anonymous closure>':.
    //     0x14e3f00: add             x1, PP, #0x40, lsl #12  ; [pp+0x40710] AnonymousClosure: (0x14e3f28), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x14e3ec4)
    //     0x14e3f04: ldr             x1, [x1, #0x710]
    // 0x14e3f08: stur            x0, [fp, #-8]
    // 0x14e3f0c: r0 = AllocateClosure()
    //     0x14e3f0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e3f10: mov             x1, x0
    // 0x14e3f14: ldur            x0, [fp, #-8]
    // 0x14e3f18: StoreField: r0->field_b = r1
    //     0x14e3f18: stur            w1, [x0, #0xb]
    // 0x14e3f1c: LeaveFrame
    //     0x14e3f1c: mov             SP, fp
    //     0x14e3f20: ldp             fp, lr, [SP], #0x10
    // 0x14e3f24: ret
    //     0x14e3f24: ret             
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x14e3f28, size: 0xd10
    // 0x14e3f28: EnterFrame
    //     0x14e3f28: stp             fp, lr, [SP, #-0x10]!
    //     0x14e3f2c: mov             fp, SP
    // 0x14e3f30: AllocStack(0x70)
    //     0x14e3f30: sub             SP, SP, #0x70
    // 0x14e3f34: SetupParameters()
    //     0x14e3f34: ldr             x0, [fp, #0x10]
    //     0x14e3f38: ldur            w2, [x0, #0x17]
    //     0x14e3f3c: add             x2, x2, HEAP, lsl #32
    //     0x14e3f40: stur            x2, [fp, #-8]
    // 0x14e3f44: CheckStackOverflow
    //     0x14e3f44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14e3f48: cmp             SP, x16
    //     0x14e3f4c: b.ls            #0x14e4c14
    // 0x14e3f50: r0 = Radius()
    //     0x14e3f50: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e3f54: d0 = 20.000000
    //     0x14e3f54: fmov            d0, #20.00000000
    // 0x14e3f58: stur            x0, [fp, #-0x10]
    // 0x14e3f5c: StoreField: r0->field_7 = d0
    //     0x14e3f5c: stur            d0, [x0, #7]
    // 0x14e3f60: StoreField: r0->field_f = d0
    //     0x14e3f60: stur            d0, [x0, #0xf]
    // 0x14e3f64: r0 = BorderRadius()
    //     0x14e3f64: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e3f68: mov             x1, x0
    // 0x14e3f6c: ldur            x0, [fp, #-0x10]
    // 0x14e3f70: stur            x1, [fp, #-0x18]
    // 0x14e3f74: StoreField: r1->field_7 = r0
    //     0x14e3f74: stur            w0, [x1, #7]
    // 0x14e3f78: StoreField: r1->field_b = r0
    //     0x14e3f78: stur            w0, [x1, #0xb]
    // 0x14e3f7c: StoreField: r1->field_f = r0
    //     0x14e3f7c: stur            w0, [x1, #0xf]
    // 0x14e3f80: StoreField: r1->field_13 = r0
    //     0x14e3f80: stur            w0, [x1, #0x13]
    // 0x14e3f84: r0 = BoxDecoration()
    //     0x14e3f84: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e3f88: mov             x2, x0
    // 0x14e3f8c: r0 = Instance_Color
    //     0x14e3f8c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14e3f90: stur            x2, [fp, #-0x10]
    // 0x14e3f94: StoreField: r2->field_7 = r0
    //     0x14e3f94: stur            w0, [x2, #7]
    // 0x14e3f98: ldur            x1, [fp, #-0x18]
    // 0x14e3f9c: StoreField: r2->field_13 = r1
    //     0x14e3f9c: stur            w1, [x2, #0x13]
    // 0x14e3fa0: r3 = Instance_BoxShape
    //     0x14e3fa0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e3fa4: ldr             x3, [x3, #0x80]
    // 0x14e3fa8: StoreField: r2->field_23 = r3
    //     0x14e3fa8: stur            w3, [x2, #0x23]
    // 0x14e3fac: ldur            x4, [fp, #-8]
    // 0x14e3fb0: LoadField: r1 = r4->field_13
    //     0x14e3fb0: ldur            w1, [x4, #0x13]
    // 0x14e3fb4: DecompressPointer r1
    //     0x14e3fb4: add             x1, x1, HEAP, lsl #32
    // 0x14e3fb8: r0 = of()
    //     0x14e3fb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e3fbc: LoadField: r1 = r0->field_87
    //     0x14e3fbc: ldur            w1, [x0, #0x87]
    // 0x14e3fc0: DecompressPointer r1
    //     0x14e3fc0: add             x1, x1, HEAP, lsl #32
    // 0x14e3fc4: LoadField: r0 = r1->field_7
    //     0x14e3fc4: ldur            w0, [x1, #7]
    // 0x14e3fc8: DecompressPointer r0
    //     0x14e3fc8: add             x0, x0, HEAP, lsl #32
    // 0x14e3fcc: stur            x0, [fp, #-0x18]
    // 0x14e3fd0: r1 = Instance_Color
    //     0x14e3fd0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e3fd4: d0 = 0.700000
    //     0x14e3fd4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e3fd8: ldr             d0, [x17, #0xf48]
    // 0x14e3fdc: r0 = withOpacity()
    //     0x14e3fdc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e3fe0: r16 = 21.000000
    //     0x14e3fe0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x14e3fe4: ldr             x16, [x16, #0x9b0]
    // 0x14e3fe8: stp             x16, x0, [SP]
    // 0x14e3fec: ldur            x1, [fp, #-0x18]
    // 0x14e3ff0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e3ff0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e3ff4: ldr             x4, [x4, #0x9b8]
    // 0x14e3ff8: r0 = copyWith()
    //     0x14e3ff8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e3ffc: stur            x0, [fp, #-0x18]
    // 0x14e4000: r0 = Text()
    //     0x14e4000: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14e4004: mov             x2, x0
    // 0x14e4008: r0 = "Want to Exchange\?"
    //     0x14e4008: add             x0, PP, #0x40, lsl #12  ; [pp+0x40718] "Want to Exchange\?"
    //     0x14e400c: ldr             x0, [x0, #0x718]
    // 0x14e4010: stur            x2, [fp, #-0x20]
    // 0x14e4014: StoreField: r2->field_b = r0
    //     0x14e4014: stur            w0, [x2, #0xb]
    // 0x14e4018: ldur            x0, [fp, #-0x18]
    // 0x14e401c: StoreField: r2->field_13 = r0
    //     0x14e401c: stur            w0, [x2, #0x13]
    // 0x14e4020: ldur            x0, [fp, #-8]
    // 0x14e4024: LoadField: r1 = r0->field_13
    //     0x14e4024: ldur            w1, [x0, #0x13]
    // 0x14e4028: DecompressPointer r1
    //     0x14e4028: add             x1, x1, HEAP, lsl #32
    // 0x14e402c: r0 = of()
    //     0x14e402c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e4030: LoadField: r1 = r0->field_87
    //     0x14e4030: ldur            w1, [x0, #0x87]
    // 0x14e4034: DecompressPointer r1
    //     0x14e4034: add             x1, x1, HEAP, lsl #32
    // 0x14e4038: LoadField: r0 = r1->field_2b
    //     0x14e4038: ldur            w0, [x1, #0x2b]
    // 0x14e403c: DecompressPointer r0
    //     0x14e403c: add             x0, x0, HEAP, lsl #32
    // 0x14e4040: stur            x0, [fp, #-0x18]
    // 0x14e4044: r1 = Instance_Color
    //     0x14e4044: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e4048: d0 = 0.700000
    //     0x14e4048: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e404c: ldr             d0, [x17, #0xf48]
    // 0x14e4050: r0 = withOpacity()
    //     0x14e4050: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e4054: r16 = 14.000000
    //     0x14e4054: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14e4058: ldr             x16, [x16, #0x1d8]
    // 0x14e405c: stp             x16, x0, [SP]
    // 0x14e4060: ldur            x1, [fp, #-0x18]
    // 0x14e4064: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e4064: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e4068: ldr             x4, [x4, #0x9b8]
    // 0x14e406c: r0 = copyWith()
    //     0x14e406c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e4070: stur            x0, [fp, #-0x18]
    // 0x14e4074: r0 = Text()
    //     0x14e4074: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14e4078: mov             x3, x0
    // 0x14e407c: r0 = "Like something else\? No worries, just exchange"
    //     0x14e407c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ff8] "Like something else\? No worries, just exchange"
    //     0x14e4080: ldr             x0, [x0, #0xff8]
    // 0x14e4084: stur            x3, [fp, #-0x28]
    // 0x14e4088: StoreField: r3->field_b = r0
    //     0x14e4088: stur            w0, [x3, #0xb]
    // 0x14e408c: ldur            x0, [fp, #-0x18]
    // 0x14e4090: StoreField: r3->field_13 = r0
    //     0x14e4090: stur            w0, [x3, #0x13]
    // 0x14e4094: r1 = <Widget>
    //     0x14e4094: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14e4098: r2 = 30
    //     0x14e4098: movz            x2, #0x1e
    // 0x14e409c: r0 = _GrowableList()
    //     0x14e409c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x14e40a0: stur            x0, [fp, #-0x18]
    // 0x14e40a4: r2 = 0
    //     0x14e40a4: movz            x2, #0
    // 0x14e40a8: stur            x2, [fp, #-0x30]
    // 0x14e40ac: CheckStackOverflow
    //     0x14e40ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14e40b0: cmp             SP, x16
    //     0x14e40b4: b.ls            #0x14e4c1c
    // 0x14e40b8: LoadField: r1 = r0->field_b
    //     0x14e40b8: ldur            w1, [x0, #0xb]
    // 0x14e40bc: r3 = LoadInt32Instr(r1)
    //     0x14e40bc: sbfx            x3, x1, #1, #0x1f
    // 0x14e40c0: cmp             x2, x3
    // 0x14e40c4: b.ge            #0x14e41b8
    // 0x14e40c8: tbnz            w2, #0, #0x14e40dc
    // 0x14e40cc: mov             x1, x2
    // 0x14e40d0: r2 = Instance_Color
    //     0x14e40d0: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14e40d4: ldr             x2, [x2, #0xf88]
    // 0x14e40d8: b               #0x14e40f4
    // 0x14e40dc: r1 = Instance_Color
    //     0x14e40dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e40e0: d0 = 0.100000
    //     0x14e40e0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14e40e4: r0 = withOpacity()
    //     0x14e40e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e40e8: mov             x2, x0
    // 0x14e40ec: ldur            x0, [fp, #-0x18]
    // 0x14e40f0: ldur            x1, [fp, #-0x30]
    // 0x14e40f4: stur            x2, [fp, #-0x38]
    // 0x14e40f8: r0 = Container()
    //     0x14e40f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e40fc: stur            x0, [fp, #-0x40]
    // 0x14e4100: ldur            x16, [fp, #-0x38]
    // 0x14e4104: r30 = 1.000000
    //     0x14e4104: ldr             lr, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14e4108: stp             lr, x16, [SP]
    // 0x14e410c: mov             x1, x0
    // 0x14e4110: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, height, 0x2, null]
    //     0x14e4110: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd30] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "height", 0x2, Null]
    //     0x14e4114: ldr             x4, [x4, #0xd30]
    // 0x14e4118: r0 = Container()
    //     0x14e4118: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e411c: r1 = <FlexParentData>
    //     0x14e411c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14e4120: ldr             x1, [x1, #0xe00]
    // 0x14e4124: r0 = Expanded()
    //     0x14e4124: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14e4128: mov             x2, x0
    // 0x14e412c: r0 = 1
    //     0x14e412c: movz            x0, #0x1
    // 0x14e4130: stur            x2, [fp, #-0x38]
    // 0x14e4134: StoreField: r2->field_13 = r0
    //     0x14e4134: stur            x0, [x2, #0x13]
    // 0x14e4138: r3 = Instance_FlexFit
    //     0x14e4138: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14e413c: ldr             x3, [x3, #0xe08]
    // 0x14e4140: StoreField: r2->field_1b = r3
    //     0x14e4140: stur            w3, [x2, #0x1b]
    // 0x14e4144: ldur            x1, [fp, #-0x40]
    // 0x14e4148: StoreField: r2->field_b = r1
    //     0x14e4148: stur            w1, [x2, #0xb]
    // 0x14e414c: mov             x1, x2
    // 0x14e4150: r0 = _NativeScene._()
    //     0x14e4150: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0x14e4154: ldur            x3, [fp, #-0x18]
    // 0x14e4158: LoadField: r0 = r3->field_b
    //     0x14e4158: ldur            w0, [x3, #0xb]
    // 0x14e415c: r1 = LoadInt32Instr(r0)
    //     0x14e415c: sbfx            x1, x0, #1, #0x1f
    // 0x14e4160: mov             x0, x1
    // 0x14e4164: ldur            x1, [fp, #-0x30]
    // 0x14e4168: cmp             x1, x0
    // 0x14e416c: b.hs            #0x14e4c24
    // 0x14e4170: LoadField: r1 = r3->field_f
    //     0x14e4170: ldur            w1, [x3, #0xf]
    // 0x14e4174: DecompressPointer r1
    //     0x14e4174: add             x1, x1, HEAP, lsl #32
    // 0x14e4178: ldur            x0, [fp, #-0x38]
    // 0x14e417c: ldur            x2, [fp, #-0x30]
    // 0x14e4180: ArrayStore: r1[r2] = r0  ; List_4
    //     0x14e4180: add             x25, x1, x2, lsl #2
    //     0x14e4184: add             x25, x25, #0xf
    //     0x14e4188: str             w0, [x25]
    //     0x14e418c: tbz             w0, #0, #0x14e41a8
    //     0x14e4190: ldurb           w16, [x1, #-1]
    //     0x14e4194: ldurb           w17, [x0, #-1]
    //     0x14e4198: and             x16, x17, x16, lsr #2
    //     0x14e419c: tst             x16, HEAP, lsr #32
    //     0x14e41a0: b.eq            #0x14e41a8
    //     0x14e41a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14e41a8: add             x0, x2, #1
    // 0x14e41ac: mov             x2, x0
    // 0x14e41b0: mov             x0, x3
    // 0x14e41b4: b               #0x14e40a8
    // 0x14e41b8: ldur            x2, [fp, #-8]
    // 0x14e41bc: mov             x3, x0
    // 0x14e41c0: r0 = Row()
    //     0x14e41c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14e41c4: mov             x1, x0
    // 0x14e41c8: r0 = Instance_Axis
    //     0x14e41c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14e41cc: stur            x1, [fp, #-0x38]
    // 0x14e41d0: StoreField: r1->field_f = r0
    //     0x14e41d0: stur            w0, [x1, #0xf]
    // 0x14e41d4: r0 = Instance_MainAxisAlignment
    //     0x14e41d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14e41d8: ldr             x0, [x0, #0xa08]
    // 0x14e41dc: StoreField: r1->field_13 = r0
    //     0x14e41dc: stur            w0, [x1, #0x13]
    // 0x14e41e0: r2 = Instance_MainAxisSize
    //     0x14e41e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14e41e4: ldr             x2, [x2, #0xa10]
    // 0x14e41e8: ArrayStore: r1[0] = r2  ; List_4
    //     0x14e41e8: stur            w2, [x1, #0x17]
    // 0x14e41ec: r3 = Instance_CrossAxisAlignment
    //     0x14e41ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14e41f0: ldr             x3, [x3, #0xa18]
    // 0x14e41f4: StoreField: r1->field_1b = r3
    //     0x14e41f4: stur            w3, [x1, #0x1b]
    // 0x14e41f8: r4 = Instance_VerticalDirection
    //     0x14e41f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14e41fc: ldr             x4, [x4, #0xa20]
    // 0x14e4200: StoreField: r1->field_23 = r4
    //     0x14e4200: stur            w4, [x1, #0x23]
    // 0x14e4204: r5 = Instance_Clip
    //     0x14e4204: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14e4208: ldr             x5, [x5, #0x38]
    // 0x14e420c: StoreField: r1->field_2b = r5
    //     0x14e420c: stur            w5, [x1, #0x2b]
    // 0x14e4210: StoreField: r1->field_2f = rZR
    //     0x14e4210: stur            xzr, [x1, #0x2f]
    // 0x14e4214: ldur            x6, [fp, #-0x18]
    // 0x14e4218: StoreField: r1->field_b = r6
    //     0x14e4218: stur            w6, [x1, #0xb]
    // 0x14e421c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14e421c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14e4220: ldr             x0, [x0, #0x1c80]
    //     0x14e4224: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14e4228: cmp             w0, w16
    //     0x14e422c: b.ne            #0x14e4238
    //     0x14e4230: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14e4234: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14e4238: r0 = GetNavigation.size()
    //     0x14e4238: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14e423c: LoadField: d0 = r0->field_f
    //     0x14e423c: ldur            d0, [x0, #0xf]
    // 0x14e4240: d1 = 0.150000
    //     0x14e4240: ldr             d1, [PP, #0x5788]  ; [pp+0x5788] IMM: double(0.15) from 0x3fc3333333333333
    // 0x14e4244: fmul            d2, d0, d1
    // 0x14e4248: ldur            x2, [fp, #-8]
    // 0x14e424c: stur            d2, [fp, #-0x50]
    // 0x14e4250: LoadField: r1 = r2->field_f
    //     0x14e4250: ldur            w1, [x2, #0xf]
    // 0x14e4254: DecompressPointer r1
    //     0x14e4254: add             x1, x1, HEAP, lsl #32
    // 0x14e4258: r0 = controller()
    //     0x14e4258: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e425c: LoadField: r1 = r0->field_4f
    //     0x14e425c: ldur            w1, [x0, #0x4f]
    // 0x14e4260: DecompressPointer r1
    //     0x14e4260: add             x1, x1, HEAP, lsl #32
    // 0x14e4264: r0 = value()
    //     0x14e4264: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e4268: LoadField: r1 = r0->field_b
    //     0x14e4268: ldur            w1, [x0, #0xb]
    // 0x14e426c: DecompressPointer r1
    //     0x14e426c: add             x1, x1, HEAP, lsl #32
    // 0x14e4270: cmp             w1, NULL
    // 0x14e4274: b.ne            #0x14e4280
    // 0x14e4278: r6 = Null
    //     0x14e4278: mov             x6, NULL
    // 0x14e427c: b               #0x14e4290
    // 0x14e4280: LoadField: r0 = r1->field_7
    //     0x14e4280: ldur            w0, [x1, #7]
    // 0x14e4284: DecompressPointer r0
    //     0x14e4284: add             x0, x0, HEAP, lsl #32
    // 0x14e4288: LoadField: r1 = r0->field_b
    //     0x14e4288: ldur            w1, [x0, #0xb]
    // 0x14e428c: mov             x6, x1
    // 0x14e4290: ldur            x0, [fp, #-8]
    // 0x14e4294: ldur            x5, [fp, #-0x20]
    // 0x14e4298: ldur            x4, [fp, #-0x28]
    // 0x14e429c: ldur            x3, [fp, #-0x38]
    // 0x14e42a0: ldur            d0, [fp, #-0x50]
    // 0x14e42a4: mov             x2, x0
    // 0x14e42a8: stur            x6, [fp, #-0x18]
    // 0x14e42ac: r1 = Function '<anonymous closure>':.
    //     0x14e42ac: add             x1, PP, #0x40, lsl #12  ; [pp+0x40720] AnonymousClosure: (0x14e5d0c), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x14e3ec4)
    //     0x14e42b0: ldr             x1, [x1, #0x720]
    // 0x14e42b4: r0 = AllocateClosure()
    //     0x14e42b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e42b8: stur            x0, [fp, #-0x40]
    // 0x14e42bc: r0 = ListView()
    //     0x14e42bc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14e42c0: stur            x0, [fp, #-0x48]
    // 0x14e42c4: r16 = true
    //     0x14e42c4: add             x16, NULL, #0x20  ; true
    // 0x14e42c8: r30 = Instance_NeverScrollableScrollPhysics
    //     0x14e42c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14e42cc: ldr             lr, [lr, #0x1c8]
    // 0x14e42d0: stp             lr, x16, [SP]
    // 0x14e42d4: mov             x1, x0
    // 0x14e42d8: ldur            x2, [fp, #-0x40]
    // 0x14e42dc: ldur            x3, [fp, #-0x18]
    // 0x14e42e0: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x14e42e0: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x14e42e4: ldr             x4, [x4, #8]
    // 0x14e42e8: r0 = ListView.builder()
    //     0x14e42e8: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14e42ec: ldur            d0, [fp, #-0x50]
    // 0x14e42f0: r0 = inline_Allocate_Double()
    //     0x14e42f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x14e42f4: add             x0, x0, #0x10
    //     0x14e42f8: cmp             x1, x0
    //     0x14e42fc: b.ls            #0x14e4c28
    //     0x14e4300: str             x0, [THR, #0x50]  ; THR::top
    //     0x14e4304: sub             x0, x0, #0xf
    //     0x14e4308: movz            x1, #0xe15c
    //     0x14e430c: movk            x1, #0x3, lsl #16
    //     0x14e4310: stur            x1, [x0, #-1]
    // 0x14e4314: StoreField: r0->field_7 = d0
    //     0x14e4314: stur            d0, [x0, #7]
    // 0x14e4318: stur            x0, [fp, #-0x18]
    // 0x14e431c: r0 = SizedBox()
    //     0x14e431c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14e4320: mov             x3, x0
    // 0x14e4324: ldur            x0, [fp, #-0x18]
    // 0x14e4328: stur            x3, [fp, #-0x40]
    // 0x14e432c: StoreField: r3->field_13 = r0
    //     0x14e432c: stur            w0, [x3, #0x13]
    // 0x14e4330: ldur            x0, [fp, #-0x48]
    // 0x14e4334: StoreField: r3->field_b = r0
    //     0x14e4334: stur            w0, [x3, #0xb]
    // 0x14e4338: r1 = Null
    //     0x14e4338: mov             x1, NULL
    // 0x14e433c: r2 = 14
    //     0x14e433c: movz            x2, #0xe
    // 0x14e4340: r0 = AllocateArray()
    //     0x14e4340: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e4344: mov             x2, x0
    // 0x14e4348: ldur            x0, [fp, #-0x20]
    // 0x14e434c: stur            x2, [fp, #-0x18]
    // 0x14e4350: StoreField: r2->field_f = r0
    //     0x14e4350: stur            w0, [x2, #0xf]
    // 0x14e4354: r16 = Instance_SizedBox
    //     0x14e4354: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14e4358: ldr             x16, [x16, #0xc70]
    // 0x14e435c: StoreField: r2->field_13 = r16
    //     0x14e435c: stur            w16, [x2, #0x13]
    // 0x14e4360: ldur            x0, [fp, #-0x28]
    // 0x14e4364: ArrayStore: r2[0] = r0  ; List_4
    //     0x14e4364: stur            w0, [x2, #0x17]
    // 0x14e4368: r16 = Instance_SizedBox
    //     0x14e4368: add             x16, PP, #0x38, lsl #12  ; [pp+0x38010] Obj!SizedBox@d681e1
    //     0x14e436c: ldr             x16, [x16, #0x10]
    // 0x14e4370: StoreField: r2->field_1b = r16
    //     0x14e4370: stur            w16, [x2, #0x1b]
    // 0x14e4374: ldur            x0, [fp, #-0x38]
    // 0x14e4378: StoreField: r2->field_1f = r0
    //     0x14e4378: stur            w0, [x2, #0x1f]
    // 0x14e437c: r16 = Instance_SizedBox
    //     0x14e437c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14e4380: ldr             x16, [x16, #0x9f0]
    // 0x14e4384: StoreField: r2->field_23 = r16
    //     0x14e4384: stur            w16, [x2, #0x23]
    // 0x14e4388: ldur            x0, [fp, #-0x40]
    // 0x14e438c: StoreField: r2->field_27 = r0
    //     0x14e438c: stur            w0, [x2, #0x27]
    // 0x14e4390: r1 = <Widget>
    //     0x14e4390: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14e4394: r0 = AllocateGrowableArray()
    //     0x14e4394: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14e4398: mov             x1, x0
    // 0x14e439c: ldur            x0, [fp, #-0x18]
    // 0x14e43a0: stur            x1, [fp, #-0x20]
    // 0x14e43a4: StoreField: r1->field_f = r0
    //     0x14e43a4: stur            w0, [x1, #0xf]
    // 0x14e43a8: r0 = 14
    //     0x14e43a8: movz            x0, #0xe
    // 0x14e43ac: StoreField: r1->field_b = r0
    //     0x14e43ac: stur            w0, [x1, #0xb]
    // 0x14e43b0: r0 = Column()
    //     0x14e43b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14e43b4: mov             x1, x0
    // 0x14e43b8: r0 = Instance_Axis
    //     0x14e43b8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14e43bc: stur            x1, [fp, #-0x18]
    // 0x14e43c0: StoreField: r1->field_f = r0
    //     0x14e43c0: stur            w0, [x1, #0xf]
    // 0x14e43c4: r2 = Instance_MainAxisAlignment
    //     0x14e43c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14e43c8: ldr             x2, [x2, #0xa08]
    // 0x14e43cc: StoreField: r1->field_13 = r2
    //     0x14e43cc: stur            w2, [x1, #0x13]
    // 0x14e43d0: r3 = Instance_MainAxisSize
    //     0x14e43d0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14e43d4: ldr             x3, [x3, #0xa10]
    // 0x14e43d8: ArrayStore: r1[0] = r3  ; List_4
    //     0x14e43d8: stur            w3, [x1, #0x17]
    // 0x14e43dc: r4 = Instance_CrossAxisAlignment
    //     0x14e43dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14e43e0: ldr             x4, [x4, #0xa18]
    // 0x14e43e4: StoreField: r1->field_1b = r4
    //     0x14e43e4: stur            w4, [x1, #0x1b]
    // 0x14e43e8: r4 = Instance_VerticalDirection
    //     0x14e43e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14e43ec: ldr             x4, [x4, #0xa20]
    // 0x14e43f0: StoreField: r1->field_23 = r4
    //     0x14e43f0: stur            w4, [x1, #0x23]
    // 0x14e43f4: r5 = Instance_Clip
    //     0x14e43f4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14e43f8: ldr             x5, [x5, #0x38]
    // 0x14e43fc: StoreField: r1->field_2b = r5
    //     0x14e43fc: stur            w5, [x1, #0x2b]
    // 0x14e4400: StoreField: r1->field_2f = rZR
    //     0x14e4400: stur            xzr, [x1, #0x2f]
    // 0x14e4404: ldur            x6, [fp, #-0x20]
    // 0x14e4408: StoreField: r1->field_b = r6
    //     0x14e4408: stur            w6, [x1, #0xb]
    // 0x14e440c: r0 = Container()
    //     0x14e440c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e4410: stur            x0, [fp, #-0x20]
    // 0x14e4414: r16 = Instance_EdgeInsets
    //     0x14e4414: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x14e4418: ldr             x16, [x16, #0xf98]
    // 0x14e441c: r30 = inf
    //     0x14e441c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14e4420: ldr             lr, [lr, #0x9f8]
    // 0x14e4424: stp             lr, x16, [SP, #0x10]
    // 0x14e4428: ldur            x16, [fp, #-0x10]
    // 0x14e442c: ldur            lr, [fp, #-0x18]
    // 0x14e4430: stp             lr, x16, [SP]
    // 0x14e4434: mov             x1, x0
    // 0x14e4438: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x14e4438: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x14e443c: ldr             x4, [x4, #0x18]
    // 0x14e4440: r0 = Container()
    //     0x14e4440: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e4444: r0 = Padding()
    //     0x14e4444: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14e4448: mov             x1, x0
    // 0x14e444c: r0 = Instance_EdgeInsets
    //     0x14e444c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x14e4450: ldr             x0, [x0, #0xb0]
    // 0x14e4454: stur            x1, [fp, #-0x10]
    // 0x14e4458: StoreField: r1->field_f = r0
    //     0x14e4458: stur            w0, [x1, #0xf]
    // 0x14e445c: ldur            x2, [fp, #-0x20]
    // 0x14e4460: StoreField: r1->field_b = r2
    //     0x14e4460: stur            w2, [x1, #0xb]
    // 0x14e4464: r0 = Radius()
    //     0x14e4464: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e4468: d0 = 20.000000
    //     0x14e4468: fmov            d0, #20.00000000
    // 0x14e446c: stur            x0, [fp, #-0x18]
    // 0x14e4470: StoreField: r0->field_7 = d0
    //     0x14e4470: stur            d0, [x0, #7]
    // 0x14e4474: StoreField: r0->field_f = d0
    //     0x14e4474: stur            d0, [x0, #0xf]
    // 0x14e4478: r0 = BorderRadius()
    //     0x14e4478: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e447c: mov             x1, x0
    // 0x14e4480: ldur            x0, [fp, #-0x18]
    // 0x14e4484: stur            x1, [fp, #-0x20]
    // 0x14e4488: StoreField: r1->field_7 = r0
    //     0x14e4488: stur            w0, [x1, #7]
    // 0x14e448c: StoreField: r1->field_b = r0
    //     0x14e448c: stur            w0, [x1, #0xb]
    // 0x14e4490: StoreField: r1->field_f = r0
    //     0x14e4490: stur            w0, [x1, #0xf]
    // 0x14e4494: StoreField: r1->field_13 = r0
    //     0x14e4494: stur            w0, [x1, #0x13]
    // 0x14e4498: r0 = BoxDecoration()
    //     0x14e4498: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e449c: mov             x2, x0
    // 0x14e44a0: r0 = Instance_Color
    //     0x14e44a0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14e44a4: stur            x2, [fp, #-0x18]
    // 0x14e44a8: StoreField: r2->field_7 = r0
    //     0x14e44a8: stur            w0, [x2, #7]
    // 0x14e44ac: ldur            x1, [fp, #-0x20]
    // 0x14e44b0: StoreField: r2->field_13 = r1
    //     0x14e44b0: stur            w1, [x2, #0x13]
    // 0x14e44b4: r3 = Instance_BoxShape
    //     0x14e44b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e44b8: ldr             x3, [x3, #0x80]
    // 0x14e44bc: StoreField: r2->field_23 = r3
    //     0x14e44bc: stur            w3, [x2, #0x23]
    // 0x14e44c0: ldur            x4, [fp, #-8]
    // 0x14e44c4: LoadField: r1 = r4->field_13
    //     0x14e44c4: ldur            w1, [x4, #0x13]
    // 0x14e44c8: DecompressPointer r1
    //     0x14e44c8: add             x1, x1, HEAP, lsl #32
    // 0x14e44cc: r0 = of()
    //     0x14e44cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e44d0: LoadField: r1 = r0->field_87
    //     0x14e44d0: ldur            w1, [x0, #0x87]
    // 0x14e44d4: DecompressPointer r1
    //     0x14e44d4: add             x1, x1, HEAP, lsl #32
    // 0x14e44d8: LoadField: r0 = r1->field_7
    //     0x14e44d8: ldur            w0, [x1, #7]
    // 0x14e44dc: DecompressPointer r0
    //     0x14e44dc: add             x0, x0, HEAP, lsl #32
    // 0x14e44e0: stur            x0, [fp, #-0x20]
    // 0x14e44e4: r1 = Instance_Color
    //     0x14e44e4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e44e8: d0 = 0.700000
    //     0x14e44e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e44ec: ldr             d0, [x17, #0xf48]
    // 0x14e44f0: r0 = withOpacity()
    //     0x14e44f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e44f4: r16 = 14.000000
    //     0x14e44f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14e44f8: ldr             x16, [x16, #0x1d8]
    // 0x14e44fc: stp             x16, x0, [SP]
    // 0x14e4500: ldur            x1, [fp, #-0x20]
    // 0x14e4504: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e4504: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e4508: ldr             x4, [x4, #0x9b8]
    // 0x14e450c: r0 = copyWith()
    //     0x14e450c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e4510: stur            x0, [fp, #-0x20]
    // 0x14e4514: r0 = TextSpan()
    //     0x14e4514: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14e4518: mov             x2, x0
    // 0x14e451c: r0 = "Select Issue Detail"
    //     0x14e451c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38020] "Select Issue Detail"
    //     0x14e4520: ldr             x0, [x0, #0x20]
    // 0x14e4524: stur            x2, [fp, #-0x28]
    // 0x14e4528: StoreField: r2->field_b = r0
    //     0x14e4528: stur            w0, [x2, #0xb]
    // 0x14e452c: r0 = Instance__DeferringMouseCursor
    //     0x14e452c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14e4530: ArrayStore: r2[0] = r0  ; List_4
    //     0x14e4530: stur            w0, [x2, #0x17]
    // 0x14e4534: ldur            x1, [fp, #-0x20]
    // 0x14e4538: StoreField: r2->field_7 = r1
    //     0x14e4538: stur            w1, [x2, #7]
    // 0x14e453c: ldur            x3, [fp, #-8]
    // 0x14e4540: LoadField: r1 = r3->field_13
    //     0x14e4540: ldur            w1, [x3, #0x13]
    // 0x14e4544: DecompressPointer r1
    //     0x14e4544: add             x1, x1, HEAP, lsl #32
    // 0x14e4548: r0 = of()
    //     0x14e4548: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e454c: LoadField: r1 = r0->field_87
    //     0x14e454c: ldur            w1, [x0, #0x87]
    // 0x14e4550: DecompressPointer r1
    //     0x14e4550: add             x1, x1, HEAP, lsl #32
    // 0x14e4554: LoadField: r0 = r1->field_33
    //     0x14e4554: ldur            w0, [x1, #0x33]
    // 0x14e4558: DecompressPointer r0
    //     0x14e4558: add             x0, x0, HEAP, lsl #32
    // 0x14e455c: cmp             w0, NULL
    // 0x14e4560: b.ne            #0x14e456c
    // 0x14e4564: r1 = Null
    //     0x14e4564: mov             x1, NULL
    // 0x14e4568: b               #0x14e458c
    // 0x14e456c: r16 = Instance_MaterialColor
    //     0x14e456c: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x14e4570: ldr             x16, [x16, #0x180]
    // 0x14e4574: str             x16, [SP]
    // 0x14e4578: mov             x1, x0
    // 0x14e457c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x14e457c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x14e4580: ldr             x4, [x4, #0xf40]
    // 0x14e4584: r0 = copyWith()
    //     0x14e4584: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e4588: mov             x1, x0
    // 0x14e458c: ldur            x2, [fp, #-8]
    // 0x14e4590: ldur            x0, [fp, #-0x28]
    // 0x14e4594: stur            x1, [fp, #-0x20]
    // 0x14e4598: r0 = TextSpan()
    //     0x14e4598: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14e459c: mov             x3, x0
    // 0x14e45a0: r0 = " *"
    //     0x14e45a0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0x14e45a4: ldr             x0, [x0, #0xfc8]
    // 0x14e45a8: stur            x3, [fp, #-0x38]
    // 0x14e45ac: StoreField: r3->field_b = r0
    //     0x14e45ac: stur            w0, [x3, #0xb]
    // 0x14e45b0: r0 = Instance__DeferringMouseCursor
    //     0x14e45b0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14e45b4: ArrayStore: r3[0] = r0  ; List_4
    //     0x14e45b4: stur            w0, [x3, #0x17]
    // 0x14e45b8: ldur            x1, [fp, #-0x20]
    // 0x14e45bc: StoreField: r3->field_7 = r1
    //     0x14e45bc: stur            w1, [x3, #7]
    // 0x14e45c0: r1 = Null
    //     0x14e45c0: mov             x1, NULL
    // 0x14e45c4: r2 = 4
    //     0x14e45c4: movz            x2, #0x4
    // 0x14e45c8: r0 = AllocateArray()
    //     0x14e45c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e45cc: mov             x2, x0
    // 0x14e45d0: ldur            x0, [fp, #-0x28]
    // 0x14e45d4: stur            x2, [fp, #-0x20]
    // 0x14e45d8: StoreField: r2->field_f = r0
    //     0x14e45d8: stur            w0, [x2, #0xf]
    // 0x14e45dc: ldur            x0, [fp, #-0x38]
    // 0x14e45e0: StoreField: r2->field_13 = r0
    //     0x14e45e0: stur            w0, [x2, #0x13]
    // 0x14e45e4: r1 = <InlineSpan>
    //     0x14e45e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14e45e8: ldr             x1, [x1, #0xe40]
    // 0x14e45ec: r0 = AllocateGrowableArray()
    //     0x14e45ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14e45f0: mov             x1, x0
    // 0x14e45f4: ldur            x0, [fp, #-0x20]
    // 0x14e45f8: stur            x1, [fp, #-0x28]
    // 0x14e45fc: StoreField: r1->field_f = r0
    //     0x14e45fc: stur            w0, [x1, #0xf]
    // 0x14e4600: r2 = 4
    //     0x14e4600: movz            x2, #0x4
    // 0x14e4604: StoreField: r1->field_b = r2
    //     0x14e4604: stur            w2, [x1, #0xb]
    // 0x14e4608: r0 = TextSpan()
    //     0x14e4608: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14e460c: mov             x1, x0
    // 0x14e4610: ldur            x0, [fp, #-0x28]
    // 0x14e4614: stur            x1, [fp, #-0x20]
    // 0x14e4618: StoreField: r1->field_f = r0
    //     0x14e4618: stur            w0, [x1, #0xf]
    // 0x14e461c: r0 = Instance__DeferringMouseCursor
    //     0x14e461c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14e4620: ArrayStore: r1[0] = r0  ; List_4
    //     0x14e4620: stur            w0, [x1, #0x17]
    // 0x14e4624: r0 = RichText()
    //     0x14e4624: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14e4628: mov             x1, x0
    // 0x14e462c: ldur            x2, [fp, #-0x20]
    // 0x14e4630: stur            x0, [fp, #-0x20]
    // 0x14e4634: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14e4634: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14e4638: r0 = RichText()
    //     0x14e4638: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14e463c: r1 = Instance_Color
    //     0x14e463c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e4640: d0 = 0.100000
    //     0x14e4640: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14e4644: r0 = withOpacity()
    //     0x14e4644: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e4648: mov             x2, x0
    // 0x14e464c: r1 = Null
    //     0x14e464c: mov             x1, NULL
    // 0x14e4650: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14e4650: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14e4654: r0 = Border.all()
    //     0x14e4654: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14e4658: stur            x0, [fp, #-0x28]
    // 0x14e465c: r0 = Radius()
    //     0x14e465c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e4660: d0 = 20.000000
    //     0x14e4660: fmov            d0, #20.00000000
    // 0x14e4664: stur            x0, [fp, #-0x38]
    // 0x14e4668: StoreField: r0->field_7 = d0
    //     0x14e4668: stur            d0, [x0, #7]
    // 0x14e466c: StoreField: r0->field_f = d0
    //     0x14e466c: stur            d0, [x0, #0xf]
    // 0x14e4670: r0 = BorderRadius()
    //     0x14e4670: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e4674: mov             x1, x0
    // 0x14e4678: ldur            x0, [fp, #-0x38]
    // 0x14e467c: stur            x1, [fp, #-0x40]
    // 0x14e4680: StoreField: r1->field_7 = r0
    //     0x14e4680: stur            w0, [x1, #7]
    // 0x14e4684: StoreField: r1->field_b = r0
    //     0x14e4684: stur            w0, [x1, #0xb]
    // 0x14e4688: StoreField: r1->field_f = r0
    //     0x14e4688: stur            w0, [x1, #0xf]
    // 0x14e468c: StoreField: r1->field_13 = r0
    //     0x14e468c: stur            w0, [x1, #0x13]
    // 0x14e4690: r0 = BoxDecoration()
    //     0x14e4690: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e4694: mov             x2, x0
    // 0x14e4698: ldur            x0, [fp, #-0x28]
    // 0x14e469c: stur            x2, [fp, #-0x38]
    // 0x14e46a0: StoreField: r2->field_f = r0
    //     0x14e46a0: stur            w0, [x2, #0xf]
    // 0x14e46a4: ldur            x0, [fp, #-0x40]
    // 0x14e46a8: StoreField: r2->field_13 = r0
    //     0x14e46a8: stur            w0, [x2, #0x13]
    // 0x14e46ac: r0 = Instance_BoxShape
    //     0x14e46ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e46b0: ldr             x0, [x0, #0x80]
    // 0x14e46b4: StoreField: r2->field_23 = r0
    //     0x14e46b4: stur            w0, [x2, #0x23]
    // 0x14e46b8: ldur            x3, [fp, #-8]
    // 0x14e46bc: LoadField: r1 = r3->field_f
    //     0x14e46bc: ldur            w1, [x3, #0xf]
    // 0x14e46c0: DecompressPointer r1
    //     0x14e46c0: add             x1, x1, HEAP, lsl #32
    // 0x14e46c4: r0 = controller()
    //     0x14e46c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e46c8: LoadField: r1 = r0->field_4f
    //     0x14e46c8: ldur            w1, [x0, #0x4f]
    // 0x14e46cc: DecompressPointer r1
    //     0x14e46cc: add             x1, x1, HEAP, lsl #32
    // 0x14e46d0: r0 = value()
    //     0x14e46d0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e46d4: LoadField: r1 = r0->field_b
    //     0x14e46d4: ldur            w1, [x0, #0xb]
    // 0x14e46d8: DecompressPointer r1
    //     0x14e46d8: add             x1, x1, HEAP, lsl #32
    // 0x14e46dc: cmp             w1, NULL
    // 0x14e46e0: b.ne            #0x14e46ec
    // 0x14e46e4: r0 = Null
    //     0x14e46e4: mov             x0, NULL
    // 0x14e46e8: b               #0x14e46fc
    // 0x14e46ec: LoadField: r0 = r1->field_f
    //     0x14e46ec: ldur            w0, [x1, #0xf]
    // 0x14e46f0: DecompressPointer r0
    //     0x14e46f0: add             x0, x0, HEAP, lsl #32
    // 0x14e46f4: LoadField: r1 = r0->field_b
    //     0x14e46f4: ldur            w1, [x0, #0xb]
    // 0x14e46f8: mov             x0, x1
    // 0x14e46fc: cmp             w0, NULL
    // 0x14e4700: b.ne            #0x14e470c
    // 0x14e4704: r5 = 0
    //     0x14e4704: movz            x5, #0
    // 0x14e4708: b               #0x14e4714
    // 0x14e470c: r1 = LoadInt32Instr(r0)
    //     0x14e470c: sbfx            x1, x0, #1, #0x1f
    // 0x14e4710: mov             x5, x1
    // 0x14e4714: ldur            x0, [fp, #-8]
    // 0x14e4718: ldur            x4, [fp, #-0x10]
    // 0x14e471c: ldur            x3, [fp, #-0x20]
    // 0x14e4720: mov             x2, x0
    // 0x14e4724: stur            x5, [fp, #-0x30]
    // 0x14e4728: r1 = Function '<anonymous closure>':.
    //     0x14e4728: add             x1, PP, #0x40, lsl #12  ; [pp+0x40728] AnonymousClosure: (0x14e4d58), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x14e3ec4)
    //     0x14e472c: ldr             x1, [x1, #0x728]
    // 0x14e4730: r0 = AllocateClosure()
    //     0x14e4730: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e4734: ldur            x2, [fp, #-8]
    // 0x14e4738: r1 = Function '<anonymous closure>':.
    //     0x14e4738: add             x1, PP, #0x40, lsl #12  ; [pp+0x40730] AnonymousClosure: (0x14e4c38), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x14e3ec4)
    //     0x14e473c: ldr             x1, [x1, #0x730]
    // 0x14e4740: stur            x0, [fp, #-0x28]
    // 0x14e4744: r0 = AllocateClosure()
    //     0x14e4744: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e4748: stur            x0, [fp, #-0x40]
    // 0x14e474c: r0 = ListView()
    //     0x14e474c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14e4750: stur            x0, [fp, #-0x48]
    // 0x14e4754: r16 = true
    //     0x14e4754: add             x16, NULL, #0x20  ; true
    // 0x14e4758: r30 = Instance_NeverScrollableScrollPhysics
    //     0x14e4758: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14e475c: ldr             lr, [lr, #0x1c8]
    // 0x14e4760: stp             lr, x16, [SP]
    // 0x14e4764: mov             x1, x0
    // 0x14e4768: ldur            x2, [fp, #-0x28]
    // 0x14e476c: ldur            x3, [fp, #-0x30]
    // 0x14e4770: ldur            x5, [fp, #-0x40]
    // 0x14e4774: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0x14e4774: add             x4, PP, #0x40, lsl #12  ; [pp+0x40738] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14e4778: ldr             x4, [x4, #0x738]
    // 0x14e477c: r0 = ListView.separated()
    //     0x14e477c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14e4780: r0 = Container()
    //     0x14e4780: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e4784: stur            x0, [fp, #-0x28]
    // 0x14e4788: r16 = inf
    //     0x14e4788: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14e478c: ldr             x16, [x16, #0x9f8]
    // 0x14e4790: ldur            lr, [fp, #-0x38]
    // 0x14e4794: stp             lr, x16, [SP, #8]
    // 0x14e4798: ldur            x16, [fp, #-0x48]
    // 0x14e479c: str             x16, [SP]
    // 0x14e47a0: mov             x1, x0
    // 0x14e47a4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0x14e47a4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0x14e47a8: ldr             x4, [x4, #0x830]
    // 0x14e47ac: r0 = Container()
    //     0x14e47ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e47b0: r0 = Padding()
    //     0x14e47b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14e47b4: mov             x3, x0
    // 0x14e47b8: r0 = Instance_EdgeInsets
    //     0x14e47b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x14e47bc: ldr             x0, [x0, #0xa00]
    // 0x14e47c0: stur            x3, [fp, #-0x38]
    // 0x14e47c4: StoreField: r3->field_f = r0
    //     0x14e47c4: stur            w0, [x3, #0xf]
    // 0x14e47c8: ldur            x0, [fp, #-0x28]
    // 0x14e47cc: StoreField: r3->field_b = r0
    //     0x14e47cc: stur            w0, [x3, #0xb]
    // 0x14e47d0: r1 = Null
    //     0x14e47d0: mov             x1, NULL
    // 0x14e47d4: r2 = 4
    //     0x14e47d4: movz            x2, #0x4
    // 0x14e47d8: r0 = AllocateArray()
    //     0x14e47d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e47dc: mov             x2, x0
    // 0x14e47e0: ldur            x0, [fp, #-0x20]
    // 0x14e47e4: stur            x2, [fp, #-0x28]
    // 0x14e47e8: StoreField: r2->field_f = r0
    //     0x14e47e8: stur            w0, [x2, #0xf]
    // 0x14e47ec: ldur            x0, [fp, #-0x38]
    // 0x14e47f0: StoreField: r2->field_13 = r0
    //     0x14e47f0: stur            w0, [x2, #0x13]
    // 0x14e47f4: r1 = <Widget>
    //     0x14e47f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14e47f8: r0 = AllocateGrowableArray()
    //     0x14e47f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14e47fc: mov             x1, x0
    // 0x14e4800: ldur            x0, [fp, #-0x28]
    // 0x14e4804: stur            x1, [fp, #-0x20]
    // 0x14e4808: StoreField: r1->field_f = r0
    //     0x14e4808: stur            w0, [x1, #0xf]
    // 0x14e480c: r0 = 4
    //     0x14e480c: movz            x0, #0x4
    // 0x14e4810: StoreField: r1->field_b = r0
    //     0x14e4810: stur            w0, [x1, #0xb]
    // 0x14e4814: r0 = Column()
    //     0x14e4814: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14e4818: mov             x1, x0
    // 0x14e481c: r0 = Instance_Axis
    //     0x14e481c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14e4820: stur            x1, [fp, #-0x28]
    // 0x14e4824: StoreField: r1->field_f = r0
    //     0x14e4824: stur            w0, [x1, #0xf]
    // 0x14e4828: r2 = Instance_MainAxisAlignment
    //     0x14e4828: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14e482c: ldr             x2, [x2, #0xa08]
    // 0x14e4830: StoreField: r1->field_13 = r2
    //     0x14e4830: stur            w2, [x1, #0x13]
    // 0x14e4834: r3 = Instance_MainAxisSize
    //     0x14e4834: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14e4838: ldr             x3, [x3, #0xa10]
    // 0x14e483c: ArrayStore: r1[0] = r3  ; List_4
    //     0x14e483c: stur            w3, [x1, #0x17]
    // 0x14e4840: r4 = Instance_CrossAxisAlignment
    //     0x14e4840: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14e4844: ldr             x4, [x4, #0x890]
    // 0x14e4848: StoreField: r1->field_1b = r4
    //     0x14e4848: stur            w4, [x1, #0x1b]
    // 0x14e484c: r5 = Instance_VerticalDirection
    //     0x14e484c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14e4850: ldr             x5, [x5, #0xa20]
    // 0x14e4854: StoreField: r1->field_23 = r5
    //     0x14e4854: stur            w5, [x1, #0x23]
    // 0x14e4858: r6 = Instance_Clip
    //     0x14e4858: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14e485c: ldr             x6, [x6, #0x38]
    // 0x14e4860: StoreField: r1->field_2b = r6
    //     0x14e4860: stur            w6, [x1, #0x2b]
    // 0x14e4864: StoreField: r1->field_2f = rZR
    //     0x14e4864: stur            xzr, [x1, #0x2f]
    // 0x14e4868: ldur            x7, [fp, #-0x20]
    // 0x14e486c: StoreField: r1->field_b = r7
    //     0x14e486c: stur            w7, [x1, #0xb]
    // 0x14e4870: r0 = Container()
    //     0x14e4870: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e4874: stur            x0, [fp, #-0x20]
    // 0x14e4878: r16 = Instance_EdgeInsets
    //     0x14e4878: add             x16, PP, #0x40, lsl #12  ; [pp+0x40740] Obj!EdgeInsets@d59f61
    //     0x14e487c: ldr             x16, [x16, #0x740]
    // 0x14e4880: r30 = inf
    //     0x14e4880: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14e4884: ldr             lr, [lr, #0x9f8]
    // 0x14e4888: stp             lr, x16, [SP, #0x10]
    // 0x14e488c: ldur            x16, [fp, #-0x18]
    // 0x14e4890: ldur            lr, [fp, #-0x28]
    // 0x14e4894: stp             lr, x16, [SP]
    // 0x14e4898: mov             x1, x0
    // 0x14e489c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x14e489c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x14e48a0: ldr             x4, [x4, #0x18]
    // 0x14e48a4: r0 = Container()
    //     0x14e48a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e48a8: r0 = Padding()
    //     0x14e48a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14e48ac: mov             x1, x0
    // 0x14e48b0: r0 = Instance_EdgeInsets
    //     0x14e48b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x14e48b4: ldr             x0, [x0, #0xb0]
    // 0x14e48b8: stur            x1, [fp, #-0x18]
    // 0x14e48bc: StoreField: r1->field_f = r0
    //     0x14e48bc: stur            w0, [x1, #0xf]
    // 0x14e48c0: ldur            x0, [fp, #-0x20]
    // 0x14e48c4: StoreField: r1->field_b = r0
    //     0x14e48c4: stur            w0, [x1, #0xb]
    // 0x14e48c8: r0 = Radius()
    //     0x14e48c8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e48cc: d0 = 20.000000
    //     0x14e48cc: fmov            d0, #20.00000000
    // 0x14e48d0: stur            x0, [fp, #-0x20]
    // 0x14e48d4: StoreField: r0->field_7 = d0
    //     0x14e48d4: stur            d0, [x0, #7]
    // 0x14e48d8: StoreField: r0->field_f = d0
    //     0x14e48d8: stur            d0, [x0, #0xf]
    // 0x14e48dc: r0 = BorderRadius()
    //     0x14e48dc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e48e0: mov             x1, x0
    // 0x14e48e4: ldur            x0, [fp, #-0x20]
    // 0x14e48e8: stur            x1, [fp, #-0x28]
    // 0x14e48ec: StoreField: r1->field_7 = r0
    //     0x14e48ec: stur            w0, [x1, #7]
    // 0x14e48f0: StoreField: r1->field_b = r0
    //     0x14e48f0: stur            w0, [x1, #0xb]
    // 0x14e48f4: StoreField: r1->field_f = r0
    //     0x14e48f4: stur            w0, [x1, #0xf]
    // 0x14e48f8: StoreField: r1->field_13 = r0
    //     0x14e48f8: stur            w0, [x1, #0x13]
    // 0x14e48fc: r0 = BoxDecoration()
    //     0x14e48fc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e4900: mov             x2, x0
    // 0x14e4904: r0 = Instance_Color
    //     0x14e4904: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14e4908: stur            x2, [fp, #-0x20]
    // 0x14e490c: StoreField: r2->field_7 = r0
    //     0x14e490c: stur            w0, [x2, #7]
    // 0x14e4910: ldur            x0, [fp, #-0x28]
    // 0x14e4914: StoreField: r2->field_13 = r0
    //     0x14e4914: stur            w0, [x2, #0x13]
    // 0x14e4918: r0 = Instance_BoxShape
    //     0x14e4918: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e491c: ldr             x0, [x0, #0x80]
    // 0x14e4920: StoreField: r2->field_23 = r0
    //     0x14e4920: stur            w0, [x2, #0x23]
    // 0x14e4924: ldur            x0, [fp, #-8]
    // 0x14e4928: LoadField: r1 = r0->field_13
    //     0x14e4928: ldur            w1, [x0, #0x13]
    // 0x14e492c: DecompressPointer r1
    //     0x14e492c: add             x1, x1, HEAP, lsl #32
    // 0x14e4930: r0 = of()
    //     0x14e4930: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e4934: LoadField: r1 = r0->field_87
    //     0x14e4934: ldur            w1, [x0, #0x87]
    // 0x14e4938: DecompressPointer r1
    //     0x14e4938: add             x1, x1, HEAP, lsl #32
    // 0x14e493c: LoadField: r0 = r1->field_7
    //     0x14e493c: ldur            w0, [x1, #7]
    // 0x14e4940: DecompressPointer r0
    //     0x14e4940: add             x0, x0, HEAP, lsl #32
    // 0x14e4944: r16 = Instance_Color
    //     0x14e4944: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14e4948: ldr             x16, [x16, #0x50]
    // 0x14e494c: r30 = 14.000000
    //     0x14e494c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14e4950: ldr             lr, [lr, #0x1d8]
    // 0x14e4954: stp             lr, x16, [SP]
    // 0x14e4958: mov             x1, x0
    // 0x14e495c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e495c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e4960: ldr             x4, [x4, #0x9b8]
    // 0x14e4964: r0 = copyWith()
    //     0x14e4964: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e4968: stur            x0, [fp, #-0x28]
    // 0x14e496c: r0 = Text()
    //     0x14e496c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14e4970: mov             x2, x0
    // 0x14e4974: r0 = "Please Note:"
    //     0x14e4974: add             x0, PP, #0x38, lsl #12  ; [pp+0x38030] "Please Note:"
    //     0x14e4978: ldr             x0, [x0, #0x30]
    // 0x14e497c: stur            x2, [fp, #-0x38]
    // 0x14e4980: StoreField: r2->field_b = r0
    //     0x14e4980: stur            w0, [x2, #0xb]
    // 0x14e4984: ldur            x0, [fp, #-0x28]
    // 0x14e4988: StoreField: r2->field_13 = r0
    //     0x14e4988: stur            w0, [x2, #0x13]
    // 0x14e498c: ldur            x0, [fp, #-8]
    // 0x14e4990: LoadField: r1 = r0->field_13
    //     0x14e4990: ldur            w1, [x0, #0x13]
    // 0x14e4994: DecompressPointer r1
    //     0x14e4994: add             x1, x1, HEAP, lsl #32
    // 0x14e4998: r0 = of()
    //     0x14e4998: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e499c: LoadField: r1 = r0->field_87
    //     0x14e499c: ldur            w1, [x0, #0x87]
    // 0x14e49a0: DecompressPointer r1
    //     0x14e49a0: add             x1, x1, HEAP, lsl #32
    // 0x14e49a4: LoadField: r0 = r1->field_2b
    //     0x14e49a4: ldur            w0, [x1, #0x2b]
    // 0x14e49a8: DecompressPointer r0
    //     0x14e49a8: add             x0, x0, HEAP, lsl #32
    // 0x14e49ac: stur            x0, [fp, #-8]
    // 0x14e49b0: r1 = Instance_Color
    //     0x14e49b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e49b4: d0 = 0.700000
    //     0x14e49b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e49b8: ldr             d0, [x17, #0xf48]
    // 0x14e49bc: r0 = withOpacity()
    //     0x14e49bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e49c0: r16 = 12.000000
    //     0x14e49c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14e49c4: ldr             x16, [x16, #0x9e8]
    // 0x14e49c8: stp             x16, x0, [SP]
    // 0x14e49cc: ldur            x1, [fp, #-8]
    // 0x14e49d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e49d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e49d4: ldr             x4, [x4, #0x9b8]
    // 0x14e49d8: r0 = copyWith()
    //     0x14e49d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e49dc: stur            x0, [fp, #-8]
    // 0x14e49e0: r0 = Text()
    //     0x14e49e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14e49e4: mov             x3, x0
    // 0x14e49e8: r0 = "You can only exchange one new product at a time"
    //     0x14e49e8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40748] "You can only exchange one new product at a time"
    //     0x14e49ec: ldr             x0, [x0, #0x748]
    // 0x14e49f0: stur            x3, [fp, #-0x28]
    // 0x14e49f4: StoreField: r3->field_b = r0
    //     0x14e49f4: stur            w0, [x3, #0xb]
    // 0x14e49f8: ldur            x0, [fp, #-8]
    // 0x14e49fc: StoreField: r3->field_13 = r0
    //     0x14e49fc: stur            w0, [x3, #0x13]
    // 0x14e4a00: r1 = Null
    //     0x14e4a00: mov             x1, NULL
    // 0x14e4a04: r2 = 6
    //     0x14e4a04: movz            x2, #0x6
    // 0x14e4a08: r0 = AllocateArray()
    //     0x14e4a08: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e4a0c: mov             x2, x0
    // 0x14e4a10: ldur            x0, [fp, #-0x38]
    // 0x14e4a14: stur            x2, [fp, #-8]
    // 0x14e4a18: StoreField: r2->field_f = r0
    //     0x14e4a18: stur            w0, [x2, #0xf]
    // 0x14e4a1c: r16 = Instance_SizedBox
    //     0x14e4a1c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14e4a20: ldr             x16, [x16, #0xc70]
    // 0x14e4a24: StoreField: r2->field_13 = r16
    //     0x14e4a24: stur            w16, [x2, #0x13]
    // 0x14e4a28: ldur            x0, [fp, #-0x28]
    // 0x14e4a2c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14e4a2c: stur            w0, [x2, #0x17]
    // 0x14e4a30: r1 = <Widget>
    //     0x14e4a30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14e4a34: r0 = AllocateGrowableArray()
    //     0x14e4a34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14e4a38: mov             x1, x0
    // 0x14e4a3c: ldur            x0, [fp, #-8]
    // 0x14e4a40: stur            x1, [fp, #-0x28]
    // 0x14e4a44: StoreField: r1->field_f = r0
    //     0x14e4a44: stur            w0, [x1, #0xf]
    // 0x14e4a48: r2 = 6
    //     0x14e4a48: movz            x2, #0x6
    // 0x14e4a4c: StoreField: r1->field_b = r2
    //     0x14e4a4c: stur            w2, [x1, #0xb]
    // 0x14e4a50: r0 = Column()
    //     0x14e4a50: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14e4a54: mov             x1, x0
    // 0x14e4a58: r0 = Instance_Axis
    //     0x14e4a58: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14e4a5c: stur            x1, [fp, #-8]
    // 0x14e4a60: StoreField: r1->field_f = r0
    //     0x14e4a60: stur            w0, [x1, #0xf]
    // 0x14e4a64: r2 = Instance_MainAxisAlignment
    //     0x14e4a64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14e4a68: ldr             x2, [x2, #0xa08]
    // 0x14e4a6c: StoreField: r1->field_13 = r2
    //     0x14e4a6c: stur            w2, [x1, #0x13]
    // 0x14e4a70: r3 = Instance_MainAxisSize
    //     0x14e4a70: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14e4a74: ldr             x3, [x3, #0xa10]
    // 0x14e4a78: ArrayStore: r1[0] = r3  ; List_4
    //     0x14e4a78: stur            w3, [x1, #0x17]
    // 0x14e4a7c: r4 = Instance_CrossAxisAlignment
    //     0x14e4a7c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14e4a80: ldr             x4, [x4, #0x890]
    // 0x14e4a84: StoreField: r1->field_1b = r4
    //     0x14e4a84: stur            w4, [x1, #0x1b]
    // 0x14e4a88: r5 = Instance_VerticalDirection
    //     0x14e4a88: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14e4a8c: ldr             x5, [x5, #0xa20]
    // 0x14e4a90: StoreField: r1->field_23 = r5
    //     0x14e4a90: stur            w5, [x1, #0x23]
    // 0x14e4a94: r6 = Instance_Clip
    //     0x14e4a94: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14e4a98: ldr             x6, [x6, #0x38]
    // 0x14e4a9c: StoreField: r1->field_2b = r6
    //     0x14e4a9c: stur            w6, [x1, #0x2b]
    // 0x14e4aa0: StoreField: r1->field_2f = rZR
    //     0x14e4aa0: stur            xzr, [x1, #0x2f]
    // 0x14e4aa4: ldur            x7, [fp, #-0x28]
    // 0x14e4aa8: StoreField: r1->field_b = r7
    //     0x14e4aa8: stur            w7, [x1, #0xb]
    // 0x14e4aac: r0 = Container()
    //     0x14e4aac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e4ab0: stur            x0, [fp, #-0x28]
    // 0x14e4ab4: r16 = Instance_EdgeInsets
    //     0x14e4ab4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x14e4ab8: ldr             x16, [x16, #0xf98]
    // 0x14e4abc: r30 = inf
    //     0x14e4abc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14e4ac0: ldr             lr, [lr, #0x9f8]
    // 0x14e4ac4: stp             lr, x16, [SP, #0x10]
    // 0x14e4ac8: ldur            x16, [fp, #-0x20]
    // 0x14e4acc: ldur            lr, [fp, #-8]
    // 0x14e4ad0: stp             lr, x16, [SP]
    // 0x14e4ad4: mov             x1, x0
    // 0x14e4ad8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x14e4ad8: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x14e4adc: ldr             x4, [x4, #0x18]
    // 0x14e4ae0: r0 = Container()
    //     0x14e4ae0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e4ae4: r1 = Null
    //     0x14e4ae4: mov             x1, NULL
    // 0x14e4ae8: r2 = 6
    //     0x14e4ae8: movz            x2, #0x6
    // 0x14e4aec: r0 = AllocateArray()
    //     0x14e4aec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e4af0: mov             x2, x0
    // 0x14e4af4: ldur            x0, [fp, #-0x10]
    // 0x14e4af8: stur            x2, [fp, #-8]
    // 0x14e4afc: StoreField: r2->field_f = r0
    //     0x14e4afc: stur            w0, [x2, #0xf]
    // 0x14e4b00: ldur            x0, [fp, #-0x18]
    // 0x14e4b04: StoreField: r2->field_13 = r0
    //     0x14e4b04: stur            w0, [x2, #0x13]
    // 0x14e4b08: ldur            x0, [fp, #-0x28]
    // 0x14e4b0c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14e4b0c: stur            w0, [x2, #0x17]
    // 0x14e4b10: r1 = <Widget>
    //     0x14e4b10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14e4b14: r0 = AllocateGrowableArray()
    //     0x14e4b14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14e4b18: mov             x1, x0
    // 0x14e4b1c: ldur            x0, [fp, #-8]
    // 0x14e4b20: stur            x1, [fp, #-0x10]
    // 0x14e4b24: StoreField: r1->field_f = r0
    //     0x14e4b24: stur            w0, [x1, #0xf]
    // 0x14e4b28: r0 = 6
    //     0x14e4b28: movz            x0, #0x6
    // 0x14e4b2c: StoreField: r1->field_b = r0
    //     0x14e4b2c: stur            w0, [x1, #0xb]
    // 0x14e4b30: r0 = Column()
    //     0x14e4b30: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14e4b34: mov             x1, x0
    // 0x14e4b38: r0 = Instance_Axis
    //     0x14e4b38: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14e4b3c: stur            x1, [fp, #-8]
    // 0x14e4b40: StoreField: r1->field_f = r0
    //     0x14e4b40: stur            w0, [x1, #0xf]
    // 0x14e4b44: r2 = Instance_MainAxisAlignment
    //     0x14e4b44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14e4b48: ldr             x2, [x2, #0xa08]
    // 0x14e4b4c: StoreField: r1->field_13 = r2
    //     0x14e4b4c: stur            w2, [x1, #0x13]
    // 0x14e4b50: r2 = Instance_MainAxisSize
    //     0x14e4b50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14e4b54: ldr             x2, [x2, #0xa10]
    // 0x14e4b58: ArrayStore: r1[0] = r2  ; List_4
    //     0x14e4b58: stur            w2, [x1, #0x17]
    // 0x14e4b5c: r2 = Instance_CrossAxisAlignment
    //     0x14e4b5c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14e4b60: ldr             x2, [x2, #0x890]
    // 0x14e4b64: StoreField: r1->field_1b = r2
    //     0x14e4b64: stur            w2, [x1, #0x1b]
    // 0x14e4b68: r2 = Instance_VerticalDirection
    //     0x14e4b68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14e4b6c: ldr             x2, [x2, #0xa20]
    // 0x14e4b70: StoreField: r1->field_23 = r2
    //     0x14e4b70: stur            w2, [x1, #0x23]
    // 0x14e4b74: r2 = Instance_Clip
    //     0x14e4b74: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14e4b78: ldr             x2, [x2, #0x38]
    // 0x14e4b7c: StoreField: r1->field_2b = r2
    //     0x14e4b7c: stur            w2, [x1, #0x2b]
    // 0x14e4b80: StoreField: r1->field_2f = rZR
    //     0x14e4b80: stur            xzr, [x1, #0x2f]
    // 0x14e4b84: ldur            x2, [fp, #-0x10]
    // 0x14e4b88: StoreField: r1->field_b = r2
    //     0x14e4b88: stur            w2, [x1, #0xb]
    // 0x14e4b8c: r0 = Padding()
    //     0x14e4b8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14e4b90: mov             x1, x0
    // 0x14e4b94: r0 = Instance_EdgeInsets
    //     0x14e4b94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14e4b98: ldr             x0, [x0, #0x1f0]
    // 0x14e4b9c: stur            x1, [fp, #-0x10]
    // 0x14e4ba0: StoreField: r1->field_f = r0
    //     0x14e4ba0: stur            w0, [x1, #0xf]
    // 0x14e4ba4: ldur            x0, [fp, #-8]
    // 0x14e4ba8: StoreField: r1->field_b = r0
    //     0x14e4ba8: stur            w0, [x1, #0xb]
    // 0x14e4bac: r0 = ColoredBox()
    //     0x14e4bac: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x14e4bb0: mov             x1, x0
    // 0x14e4bb4: r0 = Instance_Color
    //     0x14e4bb4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14e4bb8: ldr             x0, [x0, #0x90]
    // 0x14e4bbc: stur            x1, [fp, #-8]
    // 0x14e4bc0: StoreField: r1->field_f = r0
    //     0x14e4bc0: stur            w0, [x1, #0xf]
    // 0x14e4bc4: ldur            x0, [fp, #-0x10]
    // 0x14e4bc8: StoreField: r1->field_b = r0
    //     0x14e4bc8: stur            w0, [x1, #0xb]
    // 0x14e4bcc: r0 = SingleChildScrollView()
    //     0x14e4bcc: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14e4bd0: r1 = Instance_Axis
    //     0x14e4bd0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14e4bd4: StoreField: r0->field_b = r1
    //     0x14e4bd4: stur            w1, [x0, #0xb]
    // 0x14e4bd8: r1 = false
    //     0x14e4bd8: add             x1, NULL, #0x30  ; false
    // 0x14e4bdc: StoreField: r0->field_f = r1
    //     0x14e4bdc: stur            w1, [x0, #0xf]
    // 0x14e4be0: ldur            x1, [fp, #-8]
    // 0x14e4be4: StoreField: r0->field_23 = r1
    //     0x14e4be4: stur            w1, [x0, #0x23]
    // 0x14e4be8: r1 = Instance_DragStartBehavior
    //     0x14e4be8: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14e4bec: StoreField: r0->field_27 = r1
    //     0x14e4bec: stur            w1, [x0, #0x27]
    // 0x14e4bf0: r1 = Instance_Clip
    //     0x14e4bf0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14e4bf4: ldr             x1, [x1, #0x7e0]
    // 0x14e4bf8: StoreField: r0->field_2b = r1
    //     0x14e4bf8: stur            w1, [x0, #0x2b]
    // 0x14e4bfc: r1 = Instance_HitTestBehavior
    //     0x14e4bfc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14e4c00: ldr             x1, [x1, #0x288]
    // 0x14e4c04: StoreField: r0->field_2f = r1
    //     0x14e4c04: stur            w1, [x0, #0x2f]
    // 0x14e4c08: LeaveFrame
    //     0x14e4c08: mov             SP, fp
    //     0x14e4c0c: ldp             fp, lr, [SP], #0x10
    // 0x14e4c10: ret
    //     0x14e4c10: ret             
    // 0x14e4c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14e4c14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14e4c18: b               #0x14e3f50
    // 0x14e4c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14e4c1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14e4c20: b               #0x14e40b8
    // 0x14e4c24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e4c24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14e4c28: SaveReg d0
    //     0x14e4c28: str             q0, [SP, #-0x10]!
    // 0x14e4c2c: r0 = AllocateDouble()
    //     0x14e4c2c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14e4c30: RestoreReg d0
    //     0x14e4c30: ldr             q0, [SP], #0x10
    // 0x14e4c34: b               #0x14e4314
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14e4c38, size: 0x120
    // 0x14e4c38: EnterFrame
    //     0x14e4c38: stp             fp, lr, [SP, #-0x10]!
    //     0x14e4c3c: mov             fp, SP
    // 0x14e4c40: AllocStack(0x20)
    //     0x14e4c40: sub             SP, SP, #0x20
    // 0x14e4c44: SetupParameters()
    //     0x14e4c44: ldr             x0, [fp, #0x20]
    //     0x14e4c48: ldur            w1, [x0, #0x17]
    //     0x14e4c4c: add             x1, x1, HEAP, lsl #32
    // 0x14e4c50: CheckStackOverflow
    //     0x14e4c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14e4c54: cmp             SP, x16
    //     0x14e4c58: b.ls            #0x14e4d4c
    // 0x14e4c5c: LoadField: r0 = r1->field_f
    //     0x14e4c5c: ldur            w0, [x1, #0xf]
    // 0x14e4c60: DecompressPointer r0
    //     0x14e4c60: add             x0, x0, HEAP, lsl #32
    // 0x14e4c64: mov             x1, x0
    // 0x14e4c68: r0 = controller()
    //     0x14e4c68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e4c6c: LoadField: r1 = r0->field_4f
    //     0x14e4c6c: ldur            w1, [x0, #0x4f]
    // 0x14e4c70: DecompressPointer r1
    //     0x14e4c70: add             x1, x1, HEAP, lsl #32
    // 0x14e4c74: r0 = value()
    //     0x14e4c74: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e4c78: LoadField: r1 = r0->field_b
    //     0x14e4c78: ldur            w1, [x0, #0xb]
    // 0x14e4c7c: DecompressPointer r1
    //     0x14e4c7c: add             x1, x1, HEAP, lsl #32
    // 0x14e4c80: cmp             w1, NULL
    // 0x14e4c84: b.ne            #0x14e4c90
    // 0x14e4c88: r0 = Null
    //     0x14e4c88: mov             x0, NULL
    // 0x14e4c8c: b               #0x14e4cd8
    // 0x14e4c90: ldr             x0, [fp, #0x10]
    // 0x14e4c94: LoadField: r2 = r1->field_f
    //     0x14e4c94: ldur            w2, [x1, #0xf]
    // 0x14e4c98: DecompressPointer r2
    //     0x14e4c98: add             x2, x2, HEAP, lsl #32
    // 0x14e4c9c: LoadField: r1 = r2->field_b
    //     0x14e4c9c: ldur            w1, [x2, #0xb]
    // 0x14e4ca0: r3 = LoadInt32Instr(r0)
    //     0x14e4ca0: sbfx            x3, x0, #1, #0x1f
    //     0x14e4ca4: tbz             w0, #0, #0x14e4cac
    //     0x14e4ca8: ldur            x3, [x0, #7]
    // 0x14e4cac: r0 = LoadInt32Instr(r1)
    //     0x14e4cac: sbfx            x0, x1, #1, #0x1f
    // 0x14e4cb0: mov             x1, x3
    // 0x14e4cb4: cmp             x1, x0
    // 0x14e4cb8: b.hs            #0x14e4d54
    // 0x14e4cbc: LoadField: r0 = r2->field_f
    //     0x14e4cbc: ldur            w0, [x2, #0xf]
    // 0x14e4cc0: DecompressPointer r0
    //     0x14e4cc0: add             x0, x0, HEAP, lsl #32
    // 0x14e4cc4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14e4cc4: add             x16, x0, x3, lsl #2
    //     0x14e4cc8: ldur            w1, [x16, #0xf]
    // 0x14e4ccc: DecompressPointer r1
    //     0x14e4ccc: add             x1, x1, HEAP, lsl #32
    // 0x14e4cd0: LoadField: r0 = r1->field_7
    //     0x14e4cd0: ldur            w0, [x1, #7]
    // 0x14e4cd4: DecompressPointer r0
    //     0x14e4cd4: add             x0, x0, HEAP, lsl #32
    // 0x14e4cd8: cmp             w0, NULL
    // 0x14e4cdc: b.eq            #0x14e4ce4
    // 0x14e4ce0: tbz             w0, #4, #0x14e4d08
    // 0x14e4ce4: r0 = Container()
    //     0x14e4ce4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e4ce8: mov             x1, x0
    // 0x14e4cec: stur            x0, [fp, #-8]
    // 0x14e4cf0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14e4cf0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14e4cf4: r0 = Container()
    //     0x14e4cf4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e4cf8: ldur            x0, [fp, #-8]
    // 0x14e4cfc: LeaveFrame
    //     0x14e4cfc: mov             SP, fp
    //     0x14e4d00: ldp             fp, lr, [SP], #0x10
    // 0x14e4d04: ret
    //     0x14e4d04: ret             
    // 0x14e4d08: r1 = Instance_Color
    //     0x14e4d08: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e4d0c: d0 = 0.100000
    //     0x14e4d0c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14e4d10: r0 = withOpacity()
    //     0x14e4d10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e4d14: stur            x0, [fp, #-8]
    // 0x14e4d18: r0 = Container()
    //     0x14e4d18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e4d1c: stur            x0, [fp, #-0x10]
    // 0x14e4d20: r16 = 1.000000
    //     0x14e4d20: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14e4d24: ldur            lr, [fp, #-8]
    // 0x14e4d28: stp             lr, x16, [SP]
    // 0x14e4d2c: mov             x1, x0
    // 0x14e4d30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, height, 0x1, null]
    //     0x14e4d30: add             x4, PP, #0x40, lsl #12  ; [pp+0x40750] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "height", 0x1, Null]
    //     0x14e4d34: ldr             x4, [x4, #0x750]
    // 0x14e4d38: r0 = Container()
    //     0x14e4d38: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e4d3c: ldur            x0, [fp, #-0x10]
    // 0x14e4d40: LeaveFrame
    //     0x14e4d40: mov             SP, fp
    //     0x14e4d44: ldp             fp, lr, [SP], #0x10
    // 0x14e4d48: ret
    //     0x14e4d48: ret             
    // 0x14e4d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14e4d4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14e4d50: b               #0x14e4c5c
    // 0x14e4d54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e4d54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14e4d58, size: 0x560
    // 0x14e4d58: EnterFrame
    //     0x14e4d58: stp             fp, lr, [SP, #-0x10]!
    //     0x14e4d5c: mov             fp, SP
    // 0x14e4d60: AllocStack(0x40)
    //     0x14e4d60: sub             SP, SP, #0x40
    // 0x14e4d64: SetupParameters()
    //     0x14e4d64: ldr             x0, [fp, #0x20]
    //     0x14e4d68: ldur            w1, [x0, #0x17]
    //     0x14e4d6c: add             x1, x1, HEAP, lsl #32
    //     0x14e4d70: stur            x1, [fp, #-8]
    // 0x14e4d74: CheckStackOverflow
    //     0x14e4d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14e4d78: cmp             SP, x16
    //     0x14e4d7c: b.ls            #0x14e52ac
    // 0x14e4d80: r1 = 3
    //     0x14e4d80: movz            x1, #0x3
    // 0x14e4d84: r0 = AllocateContext()
    //     0x14e4d84: bl              #0x16f6108  ; AllocateContextStub
    // 0x14e4d88: mov             x2, x0
    // 0x14e4d8c: ldur            x0, [fp, #-8]
    // 0x14e4d90: stur            x2, [fp, #-0x10]
    // 0x14e4d94: StoreField: r2->field_b = r0
    //     0x14e4d94: stur            w0, [x2, #0xb]
    // 0x14e4d98: ldr             x1, [fp, #0x18]
    // 0x14e4d9c: StoreField: r2->field_f = r1
    //     0x14e4d9c: stur            w1, [x2, #0xf]
    // 0x14e4da0: ldr             x3, [fp, #0x10]
    // 0x14e4da4: StoreField: r2->field_13 = r3
    //     0x14e4da4: stur            w3, [x2, #0x13]
    // 0x14e4da8: LoadField: r1 = r0->field_f
    //     0x14e4da8: ldur            w1, [x0, #0xf]
    // 0x14e4dac: DecompressPointer r1
    //     0x14e4dac: add             x1, x1, HEAP, lsl #32
    // 0x14e4db0: r0 = controller()
    //     0x14e4db0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e4db4: LoadField: r1 = r0->field_4f
    //     0x14e4db4: ldur            w1, [x0, #0x4f]
    // 0x14e4db8: DecompressPointer r1
    //     0x14e4db8: add             x1, x1, HEAP, lsl #32
    // 0x14e4dbc: r0 = value()
    //     0x14e4dbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e4dc0: LoadField: r1 = r0->field_b
    //     0x14e4dc0: ldur            w1, [x0, #0xb]
    // 0x14e4dc4: DecompressPointer r1
    //     0x14e4dc4: add             x1, x1, HEAP, lsl #32
    // 0x14e4dc8: cmp             w1, NULL
    // 0x14e4dcc: b.ne            #0x14e4ddc
    // 0x14e4dd0: ldr             x2, [fp, #0x10]
    // 0x14e4dd4: r0 = Null
    //     0x14e4dd4: mov             x0, NULL
    // 0x14e4dd8: b               #0x14e4e28
    // 0x14e4ddc: ldr             x2, [fp, #0x10]
    // 0x14e4de0: LoadField: r3 = r1->field_f
    //     0x14e4de0: ldur            w3, [x1, #0xf]
    // 0x14e4de4: DecompressPointer r3
    //     0x14e4de4: add             x3, x3, HEAP, lsl #32
    // 0x14e4de8: LoadField: r0 = r3->field_b
    //     0x14e4de8: ldur            w0, [x3, #0xb]
    // 0x14e4dec: r4 = LoadInt32Instr(r2)
    //     0x14e4dec: sbfx            x4, x2, #1, #0x1f
    //     0x14e4df0: tbz             w2, #0, #0x14e4df8
    //     0x14e4df4: ldur            x4, [x2, #7]
    // 0x14e4df8: r1 = LoadInt32Instr(r0)
    //     0x14e4df8: sbfx            x1, x0, #1, #0x1f
    // 0x14e4dfc: mov             x0, x1
    // 0x14e4e00: mov             x1, x4
    // 0x14e4e04: cmp             x1, x0
    // 0x14e4e08: b.hs            #0x14e52b4
    // 0x14e4e0c: LoadField: r0 = r3->field_f
    //     0x14e4e0c: ldur            w0, [x3, #0xf]
    // 0x14e4e10: DecompressPointer r0
    //     0x14e4e10: add             x0, x0, HEAP, lsl #32
    // 0x14e4e14: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14e4e14: add             x16, x0, x4, lsl #2
    //     0x14e4e18: ldur            w1, [x16, #0xf]
    // 0x14e4e1c: DecompressPointer r1
    //     0x14e4e1c: add             x1, x1, HEAP, lsl #32
    // 0x14e4e20: LoadField: r0 = r1->field_7
    //     0x14e4e20: ldur            w0, [x1, #7]
    // 0x14e4e24: DecompressPointer r0
    //     0x14e4e24: add             x0, x0, HEAP, lsl #32
    // 0x14e4e28: cmp             w0, NULL
    // 0x14e4e2c: b.ne            #0x14e4e38
    // 0x14e4e30: r1 = false
    //     0x14e4e30: add             x1, NULL, #0x30  ; false
    // 0x14e4e34: b               #0x14e4e3c
    // 0x14e4e38: mov             x1, x0
    // 0x14e4e3c: ldur            x0, [fp, #-0x10]
    // 0x14e4e40: ArrayStore: r0[0] = r1  ; List_4
    //     0x14e4e40: stur            w1, [x0, #0x17]
    // 0x14e4e44: tbz             w1, #4, #0x14e5208
    // 0x14e4e48: ldur            x0, [fp, #-8]
    // 0x14e4e4c: LoadField: r1 = r0->field_f
    //     0x14e4e4c: ldur            w1, [x0, #0xf]
    // 0x14e4e50: DecompressPointer r1
    //     0x14e4e50: add             x1, x1, HEAP, lsl #32
    // 0x14e4e54: r0 = controller()
    //     0x14e4e54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e4e58: LoadField: r1 = r0->field_4f
    //     0x14e4e58: ldur            w1, [x0, #0x4f]
    // 0x14e4e5c: DecompressPointer r1
    //     0x14e4e5c: add             x1, x1, HEAP, lsl #32
    // 0x14e4e60: r0 = value()
    //     0x14e4e60: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e4e64: LoadField: r1 = r0->field_b
    //     0x14e4e64: ldur            w1, [x0, #0xb]
    // 0x14e4e68: DecompressPointer r1
    //     0x14e4e68: add             x1, x1, HEAP, lsl #32
    // 0x14e4e6c: cmp             w1, NULL
    // 0x14e4e70: b.eq            #0x14e4ec0
    // 0x14e4e74: LoadField: r0 = r1->field_f
    //     0x14e4e74: ldur            w0, [x1, #0xf]
    // 0x14e4e78: DecompressPointer r0
    //     0x14e4e78: add             x0, x0, HEAP, lsl #32
    // 0x14e4e7c: LoadField: r1 = r0->field_b
    //     0x14e4e7c: ldur            w1, [x0, #0xb]
    // 0x14e4e80: cmp             w1, #2
    // 0x14e4e84: b.ne            #0x14e4ec0
    // 0x14e4e88: r0 = Radius()
    //     0x14e4e88: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e4e8c: d0 = 20.000000
    //     0x14e4e8c: fmov            d0, #20.00000000
    // 0x14e4e90: stur            x0, [fp, #-0x18]
    // 0x14e4e94: StoreField: r0->field_7 = d0
    //     0x14e4e94: stur            d0, [x0, #7]
    // 0x14e4e98: StoreField: r0->field_f = d0
    //     0x14e4e98: stur            d0, [x0, #0xf]
    // 0x14e4e9c: r0 = BorderRadius()
    //     0x14e4e9c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e4ea0: mov             x1, x0
    // 0x14e4ea4: ldur            x0, [fp, #-0x18]
    // 0x14e4ea8: StoreField: r1->field_7 = r0
    //     0x14e4ea8: stur            w0, [x1, #7]
    // 0x14e4eac: StoreField: r1->field_b = r0
    //     0x14e4eac: stur            w0, [x1, #0xb]
    // 0x14e4eb0: StoreField: r1->field_f = r0
    //     0x14e4eb0: stur            w0, [x1, #0xf]
    // 0x14e4eb4: StoreField: r1->field_13 = r0
    //     0x14e4eb4: stur            w0, [x1, #0x13]
    // 0x14e4eb8: mov             x0, x1
    // 0x14e4ebc: b               #0x14e51b4
    // 0x14e4ec0: ldur            x0, [fp, #-8]
    // 0x14e4ec4: LoadField: r1 = r0->field_f
    //     0x14e4ec4: ldur            w1, [x0, #0xf]
    // 0x14e4ec8: DecompressPointer r1
    //     0x14e4ec8: add             x1, x1, HEAP, lsl #32
    // 0x14e4ecc: r0 = controller()
    //     0x14e4ecc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e4ed0: LoadField: r1 = r0->field_4f
    //     0x14e4ed0: ldur            w1, [x0, #0x4f]
    // 0x14e4ed4: DecompressPointer r1
    //     0x14e4ed4: add             x1, x1, HEAP, lsl #32
    // 0x14e4ed8: r0 = value()
    //     0x14e4ed8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e4edc: LoadField: r1 = r0->field_b
    //     0x14e4edc: ldur            w1, [x0, #0xb]
    // 0x14e4ee0: DecompressPointer r1
    //     0x14e4ee0: add             x1, x1, HEAP, lsl #32
    // 0x14e4ee4: cmp             w1, NULL
    // 0x14e4ee8: b.ne            #0x14e4ef4
    // 0x14e4eec: ldr             x0, [fp, #0x10]
    // 0x14e4ef0: b               #0x14e4fa4
    // 0x14e4ef4: LoadField: r0 = r1->field_f
    //     0x14e4ef4: ldur            w0, [x1, #0xf]
    // 0x14e4ef8: DecompressPointer r0
    //     0x14e4ef8: add             x0, x0, HEAP, lsl #32
    // 0x14e4efc: LoadField: r1 = r0->field_b
    //     0x14e4efc: ldur            w1, [x0, #0xb]
    // 0x14e4f00: cmp             w1, #4
    // 0x14e4f04: b.ne            #0x14e4fa0
    // 0x14e4f08: ldr             x0, [fp, #0x10]
    // 0x14e4f0c: r1 = LoadInt32Instr(r0)
    //     0x14e4f0c: sbfx            x1, x0, #1, #0x1f
    //     0x14e4f10: tbz             w0, #0, #0x14e4f18
    //     0x14e4f14: ldur            x1, [x0, #7]
    // 0x14e4f18: cbnz            x1, #0x14e4f28
    // 0x14e4f1c: r0 = Instance_Radius
    //     0x14e4f1c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e4f20: ldr             x0, [x0, #0x758]
    // 0x14e4f24: b               #0x14e4f30
    // 0x14e4f28: r0 = Instance_Radius
    //     0x14e4f28: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e4f2c: ldr             x0, [x0, #0xb48]
    // 0x14e4f30: stur            x0, [fp, #-0x28]
    // 0x14e4f34: cbnz            x1, #0x14e4f44
    // 0x14e4f38: r2 = Instance_Radius
    //     0x14e4f38: add             x2, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e4f3c: ldr             x2, [x2, #0xb48]
    // 0x14e4f40: b               #0x14e4f4c
    // 0x14e4f44: r2 = Instance_Radius
    //     0x14e4f44: add             x2, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e4f48: ldr             x2, [x2, #0x758]
    // 0x14e4f4c: stur            x2, [fp, #-0x20]
    // 0x14e4f50: cbnz            x1, #0x14e4f60
    // 0x14e4f54: r1 = Instance_Radius
    //     0x14e4f54: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e4f58: ldr             x1, [x1, #0xb48]
    // 0x14e4f5c: b               #0x14e4f68
    // 0x14e4f60: r1 = Instance_Radius
    //     0x14e4f60: add             x1, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e4f64: ldr             x1, [x1, #0x758]
    // 0x14e4f68: stur            x1, [fp, #-0x18]
    // 0x14e4f6c: r0 = BorderRadius()
    //     0x14e4f6c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e4f70: mov             x1, x0
    // 0x14e4f74: r0 = Instance_Radius
    //     0x14e4f74: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e4f78: ldr             x0, [x0, #0xb48]
    // 0x14e4f7c: StoreField: r1->field_7 = r0
    //     0x14e4f7c: stur            w0, [x1, #7]
    // 0x14e4f80: ldur            x0, [fp, #-0x28]
    // 0x14e4f84: StoreField: r1->field_b = r0
    //     0x14e4f84: stur            w0, [x1, #0xb]
    // 0x14e4f88: ldur            x0, [fp, #-0x18]
    // 0x14e4f8c: StoreField: r1->field_f = r0
    //     0x14e4f8c: stur            w0, [x1, #0xf]
    // 0x14e4f90: ldur            x0, [fp, #-0x20]
    // 0x14e4f94: StoreField: r1->field_13 = r0
    //     0x14e4f94: stur            w0, [x1, #0x13]
    // 0x14e4f98: mov             x0, x1
    // 0x14e4f9c: b               #0x14e51b4
    // 0x14e4fa0: ldr             x0, [fp, #0x10]
    // 0x14e4fa4: r2 = LoadInt32Instr(r0)
    //     0x14e4fa4: sbfx            x2, x0, #1, #0x1f
    //     0x14e4fa8: tbz             w0, #0, #0x14e4fb0
    //     0x14e4fac: ldur            x2, [x0, #7]
    // 0x14e4fb0: stur            x2, [fp, #-0x30]
    // 0x14e4fb4: cbnz            x2, #0x14e4fc8
    // 0x14e4fb8: mov             x0, x2
    // 0x14e4fbc: r2 = Instance_Radius
    //     0x14e4fbc: add             x2, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e4fc0: ldr             x2, [x2, #0x758]
    // 0x14e4fc4: b               #0x14e5000
    // 0x14e4fc8: ldur            x0, [fp, #-8]
    // 0x14e4fcc: LoadField: r1 = r0->field_f
    //     0x14e4fcc: ldur            w1, [x0, #0xf]
    // 0x14e4fd0: DecompressPointer r1
    //     0x14e4fd0: add             x1, x1, HEAP, lsl #32
    // 0x14e4fd4: r0 = controller()
    //     0x14e4fd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e4fd8: LoadField: r1 = r0->field_4f
    //     0x14e4fd8: ldur            w1, [x0, #0x4f]
    // 0x14e4fdc: DecompressPointer r1
    //     0x14e4fdc: add             x1, x1, HEAP, lsl #32
    // 0x14e4fe0: r0 = value()
    //     0x14e4fe0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e4fe4: LoadField: r1 = r0->field_b
    //     0x14e4fe4: ldur            w1, [x0, #0xb]
    // 0x14e4fe8: DecompressPointer r1
    //     0x14e4fe8: add             x1, x1, HEAP, lsl #32
    // 0x14e4fec: cmp             w1, NULL
    // 0x14e4ff0: b.eq            #0x14e4ff4
    // 0x14e4ff4: ldur            x0, [fp, #-0x30]
    // 0x14e4ff8: r2 = Instance_Radius
    //     0x14e4ff8: add             x2, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e4ffc: ldr             x2, [x2, #0xb48]
    // 0x14e5000: stur            x2, [fp, #-0x18]
    // 0x14e5004: cbnz            x0, #0x14e5014
    // 0x14e5008: r2 = Instance_Radius
    //     0x14e5008: add             x2, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e500c: ldr             x2, [x2, #0xb48]
    // 0x14e5010: b               #0x14e5094
    // 0x14e5014: ldur            x3, [fp, #-8]
    // 0x14e5018: LoadField: r1 = r3->field_f
    //     0x14e5018: ldur            w1, [x3, #0xf]
    // 0x14e501c: DecompressPointer r1
    //     0x14e501c: add             x1, x1, HEAP, lsl #32
    // 0x14e5020: r0 = controller()
    //     0x14e5020: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5024: LoadField: r1 = r0->field_4f
    //     0x14e5024: ldur            w1, [x0, #0x4f]
    // 0x14e5028: DecompressPointer r1
    //     0x14e5028: add             x1, x1, HEAP, lsl #32
    // 0x14e502c: r0 = value()
    //     0x14e502c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5030: LoadField: r1 = r0->field_b
    //     0x14e5030: ldur            w1, [x0, #0xb]
    // 0x14e5034: DecompressPointer r1
    //     0x14e5034: add             x1, x1, HEAP, lsl #32
    // 0x14e5038: cmp             w1, NULL
    // 0x14e503c: b.ne            #0x14e5048
    // 0x14e5040: r0 = Null
    //     0x14e5040: mov             x0, NULL
    // 0x14e5044: b               #0x14e5058
    // 0x14e5048: LoadField: r0 = r1->field_f
    //     0x14e5048: ldur            w0, [x1, #0xf]
    // 0x14e504c: DecompressPointer r0
    //     0x14e504c: add             x0, x0, HEAP, lsl #32
    // 0x14e5050: LoadField: r1 = r0->field_b
    //     0x14e5050: ldur            w1, [x0, #0xb]
    // 0x14e5054: mov             x0, x1
    // 0x14e5058: cmp             w0, NULL
    // 0x14e505c: b.ne            #0x14e5068
    // 0x14e5060: r1 = 0
    //     0x14e5060: movz            x1, #0
    // 0x14e5064: b               #0x14e506c
    // 0x14e5068: r1 = LoadInt32Instr(r0)
    //     0x14e5068: sbfx            x1, x0, #1, #0x1f
    // 0x14e506c: ldur            x0, [fp, #-0x30]
    // 0x14e5070: sub             x2, x1, #1
    // 0x14e5074: cmp             x0, x2
    // 0x14e5078: b.ne            #0x14e5088
    // 0x14e507c: r1 = Instance_Radius
    //     0x14e507c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e5080: ldr             x1, [x1, #0x758]
    // 0x14e5084: b               #0x14e5090
    // 0x14e5088: r1 = Instance_Radius
    //     0x14e5088: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e508c: ldr             x1, [x1, #0xb48]
    // 0x14e5090: mov             x2, x1
    // 0x14e5094: stur            x2, [fp, #-0x20]
    // 0x14e5098: cbnz            x0, #0x14e50a8
    // 0x14e509c: r2 = Instance_Radius
    //     0x14e509c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e50a0: ldr             x2, [x2, #0x758]
    // 0x14e50a4: b               #0x14e50e0
    // 0x14e50a8: ldur            x3, [fp, #-8]
    // 0x14e50ac: LoadField: r1 = r3->field_f
    //     0x14e50ac: ldur            w1, [x3, #0xf]
    // 0x14e50b0: DecompressPointer r1
    //     0x14e50b0: add             x1, x1, HEAP, lsl #32
    // 0x14e50b4: r0 = controller()
    //     0x14e50b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e50b8: LoadField: r1 = r0->field_4f
    //     0x14e50b8: ldur            w1, [x0, #0x4f]
    // 0x14e50bc: DecompressPointer r1
    //     0x14e50bc: add             x1, x1, HEAP, lsl #32
    // 0x14e50c0: r0 = value()
    //     0x14e50c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e50c4: LoadField: r1 = r0->field_b
    //     0x14e50c4: ldur            w1, [x0, #0xb]
    // 0x14e50c8: DecompressPointer r1
    //     0x14e50c8: add             x1, x1, HEAP, lsl #32
    // 0x14e50cc: cmp             w1, NULL
    // 0x14e50d0: b.eq            #0x14e50d4
    // 0x14e50d4: ldur            x0, [fp, #-0x30]
    // 0x14e50d8: r2 = Instance_Radius
    //     0x14e50d8: add             x2, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e50dc: ldr             x2, [x2, #0xb48]
    // 0x14e50e0: stur            x2, [fp, #-0x28]
    // 0x14e50e4: cbnz            x0, #0x14e50f8
    // 0x14e50e8: mov             x0, x2
    // 0x14e50ec: r3 = Instance_Radius
    //     0x14e50ec: add             x3, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e50f0: ldr             x3, [x3, #0xb48]
    // 0x14e50f4: b               #0x14e517c
    // 0x14e50f8: ldur            x3, [fp, #-8]
    // 0x14e50fc: LoadField: r1 = r3->field_f
    //     0x14e50fc: ldur            w1, [x3, #0xf]
    // 0x14e5100: DecompressPointer r1
    //     0x14e5100: add             x1, x1, HEAP, lsl #32
    // 0x14e5104: r0 = controller()
    //     0x14e5104: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5108: LoadField: r1 = r0->field_4f
    //     0x14e5108: ldur            w1, [x0, #0x4f]
    // 0x14e510c: DecompressPointer r1
    //     0x14e510c: add             x1, x1, HEAP, lsl #32
    // 0x14e5110: r0 = value()
    //     0x14e5110: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5114: LoadField: r1 = r0->field_b
    //     0x14e5114: ldur            w1, [x0, #0xb]
    // 0x14e5118: DecompressPointer r1
    //     0x14e5118: add             x1, x1, HEAP, lsl #32
    // 0x14e511c: cmp             w1, NULL
    // 0x14e5120: b.ne            #0x14e512c
    // 0x14e5124: r0 = Null
    //     0x14e5124: mov             x0, NULL
    // 0x14e5128: b               #0x14e513c
    // 0x14e512c: LoadField: r0 = r1->field_f
    //     0x14e512c: ldur            w0, [x1, #0xf]
    // 0x14e5130: DecompressPointer r0
    //     0x14e5130: add             x0, x0, HEAP, lsl #32
    // 0x14e5134: LoadField: r1 = r0->field_b
    //     0x14e5134: ldur            w1, [x0, #0xb]
    // 0x14e5138: mov             x0, x1
    // 0x14e513c: cmp             w0, NULL
    // 0x14e5140: b.ne            #0x14e514c
    // 0x14e5144: r1 = 0
    //     0x14e5144: movz            x1, #0
    // 0x14e5148: b               #0x14e5150
    // 0x14e514c: r1 = LoadInt32Instr(r0)
    //     0x14e514c: sbfx            x1, x0, #1, #0x1f
    // 0x14e5150: ldur            x0, [fp, #-0x30]
    // 0x14e5154: sub             x2, x1, #1
    // 0x14e5158: cmp             x0, x2
    // 0x14e515c: b.ne            #0x14e516c
    // 0x14e5160: r0 = Instance_Radius
    //     0x14e5160: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e5164: ldr             x0, [x0, #0x758]
    // 0x14e5168: b               #0x14e5174
    // 0x14e516c: r0 = Instance_Radius
    //     0x14e516c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e5170: ldr             x0, [x0, #0xb48]
    // 0x14e5174: mov             x3, x0
    // 0x14e5178: ldur            x0, [fp, #-0x28]
    // 0x14e517c: ldur            x2, [fp, #-0x18]
    // 0x14e5180: ldur            x1, [fp, #-0x20]
    // 0x14e5184: stur            x3, [fp, #-0x38]
    // 0x14e5188: r0 = BorderRadius()
    //     0x14e5188: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e518c: mov             x1, x0
    // 0x14e5190: ldur            x0, [fp, #-0x28]
    // 0x14e5194: StoreField: r1->field_7 = r0
    //     0x14e5194: stur            w0, [x1, #7]
    // 0x14e5198: ldur            x0, [fp, #-0x18]
    // 0x14e519c: StoreField: r1->field_b = r0
    //     0x14e519c: stur            w0, [x1, #0xb]
    // 0x14e51a0: ldur            x0, [fp, #-0x38]
    // 0x14e51a4: StoreField: r1->field_f = r0
    //     0x14e51a4: stur            w0, [x1, #0xf]
    // 0x14e51a8: ldur            x0, [fp, #-0x20]
    // 0x14e51ac: StoreField: r1->field_13 = r0
    //     0x14e51ac: stur            w0, [x1, #0x13]
    // 0x14e51b0: mov             x0, x1
    // 0x14e51b4: stur            x0, [fp, #-0x18]
    // 0x14e51b8: r0 = BoxDecoration()
    //     0x14e51b8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e51bc: mov             x1, x0
    // 0x14e51c0: ldur            x0, [fp, #-0x18]
    // 0x14e51c4: stur            x1, [fp, #-0x20]
    // 0x14e51c8: StoreField: r1->field_13 = r0
    //     0x14e51c8: stur            w0, [x1, #0x13]
    // 0x14e51cc: r0 = Instance_BoxShape
    //     0x14e51cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e51d0: ldr             x0, [x0, #0x80]
    // 0x14e51d4: StoreField: r1->field_23 = r0
    //     0x14e51d4: stur            w0, [x1, #0x23]
    // 0x14e51d8: r0 = Container()
    //     0x14e51d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e51dc: stur            x0, [fp, #-0x18]
    // 0x14e51e0: ldur            x16, [fp, #-0x20]
    // 0x14e51e4: str             x16, [SP]
    // 0x14e51e8: mov             x1, x0
    // 0x14e51ec: r4 = const [0, 0x2, 0x1, 0x1, decoration, 0x1, null]
    //     0x14e51ec: add             x4, PP, #0x40, lsl #12  ; [pp+0x40760] List(7) [0, 0x2, 0x1, 0x1, "decoration", 0x1, Null]
    //     0x14e51f0: ldr             x4, [x4, #0x760]
    // 0x14e51f4: r0 = Container()
    //     0x14e51f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e51f8: ldur            x0, [fp, #-0x18]
    // 0x14e51fc: LeaveFrame
    //     0x14e51fc: mov             SP, fp
    //     0x14e5200: ldp             fp, lr, [SP], #0x10
    // 0x14e5204: ret
    //     0x14e5204: ret             
    // 0x14e5208: ldur            x3, [fp, #-8]
    // 0x14e520c: LoadField: r1 = r3->field_f
    //     0x14e520c: ldur            w1, [x3, #0xf]
    // 0x14e5210: DecompressPointer r1
    //     0x14e5210: add             x1, x1, HEAP, lsl #32
    // 0x14e5214: r0 = controller()
    //     0x14e5214: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5218: LoadField: r2 = r0->field_73
    //     0x14e5218: ldur            w2, [x0, #0x73]
    // 0x14e521c: DecompressPointer r2
    //     0x14e521c: add             x2, x2, HEAP, lsl #32
    // 0x14e5220: ldur            x0, [fp, #-8]
    // 0x14e5224: stur            x2, [fp, #-0x18]
    // 0x14e5228: LoadField: r1 = r0->field_f
    //     0x14e5228: ldur            w1, [x0, #0xf]
    // 0x14e522c: DecompressPointer r1
    //     0x14e522c: add             x1, x1, HEAP, lsl #32
    // 0x14e5230: r0 = controller()
    //     0x14e5230: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5234: LoadField: r1 = r0->field_4f
    //     0x14e5234: ldur            w1, [x0, #0x4f]
    // 0x14e5238: DecompressPointer r1
    //     0x14e5238: add             x1, x1, HEAP, lsl #32
    // 0x14e523c: r0 = value()
    //     0x14e523c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5240: LoadField: r1 = r0->field_b
    //     0x14e5240: ldur            w1, [x0, #0xb]
    // 0x14e5244: DecompressPointer r1
    //     0x14e5244: add             x1, x1, HEAP, lsl #32
    // 0x14e5248: cmp             w1, NULL
    // 0x14e524c: b.ne            #0x14e5258
    // 0x14e5250: r0 = Null
    //     0x14e5250: mov             x0, NULL
    // 0x14e5254: b               #0x14e5260
    // 0x14e5258: LoadField: r0 = r1->field_13
    //     0x14e5258: ldur            w0, [x1, #0x13]
    // 0x14e525c: DecompressPointer r0
    //     0x14e525c: add             x0, x0, HEAP, lsl #32
    // 0x14e5260: cmp             w0, NULL
    // 0x14e5264: b.ne            #0x14e5270
    // 0x14e5268: r2 = ""
    //     0x14e5268: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14e526c: b               #0x14e5274
    // 0x14e5270: mov             x2, x0
    // 0x14e5274: ldur            x1, [fp, #-0x18]
    // 0x14e5278: r0 = value=()
    //     0x14e5278: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14e527c: r0 = Obx()
    //     0x14e527c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14e5280: ldur            x2, [fp, #-0x10]
    // 0x14e5284: r1 = Function '<anonymous closure>':.
    //     0x14e5284: add             x1, PP, #0x40, lsl #12  ; [pp+0x40768] AnonymousClosure: (0x14e52b8), in [package:customer_app/app/presentation/views/glass/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x14e3ec4)
    //     0x14e5288: ldr             x1, [x1, #0x768]
    // 0x14e528c: stur            x0, [fp, #-8]
    // 0x14e5290: r0 = AllocateClosure()
    //     0x14e5290: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e5294: mov             x1, x0
    // 0x14e5298: ldur            x0, [fp, #-8]
    // 0x14e529c: StoreField: r0->field_b = r1
    //     0x14e529c: stur            w1, [x0, #0xb]
    // 0x14e52a0: LeaveFrame
    //     0x14e52a0: mov             SP, fp
    //     0x14e52a4: ldp             fp, lr, [SP], #0x10
    // 0x14e52a8: ret
    //     0x14e52a8: ret             
    // 0x14e52ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14e52ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14e52b0: b               #0x14e4d80
    // 0x14e52b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e52b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic) {
    // ** addr: 0x14e52b8, size: 0xa54
    // 0x14e52b8: EnterFrame
    //     0x14e52b8: stp             fp, lr, [SP, #-0x10]!
    //     0x14e52bc: mov             fp, SP
    // 0x14e52c0: AllocStack(0x50)
    //     0x14e52c0: sub             SP, SP, #0x50
    // 0x14e52c4: SetupParameters()
    //     0x14e52c4: ldr             x0, [fp, #0x10]
    //     0x14e52c8: ldur            w2, [x0, #0x17]
    //     0x14e52cc: add             x2, x2, HEAP, lsl #32
    //     0x14e52d0: stur            x2, [fp, #-0x10]
    // 0x14e52d4: CheckStackOverflow
    //     0x14e52d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14e52d8: cmp             SP, x16
    //     0x14e52dc: b.ls            #0x14e5cf4
    // 0x14e52e0: LoadField: r0 = r2->field_b
    //     0x14e52e0: ldur            w0, [x2, #0xb]
    // 0x14e52e4: DecompressPointer r0
    //     0x14e52e4: add             x0, x0, HEAP, lsl #32
    // 0x14e52e8: stur            x0, [fp, #-8]
    // 0x14e52ec: LoadField: r1 = r0->field_f
    //     0x14e52ec: ldur            w1, [x0, #0xf]
    // 0x14e52f0: DecompressPointer r1
    //     0x14e52f0: add             x1, x1, HEAP, lsl #32
    // 0x14e52f4: r0 = controller()
    //     0x14e52f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e52f8: LoadField: r1 = r0->field_4f
    //     0x14e52f8: ldur            w1, [x0, #0x4f]
    // 0x14e52fc: DecompressPointer r1
    //     0x14e52fc: add             x1, x1, HEAP, lsl #32
    // 0x14e5300: r0 = value()
    //     0x14e5300: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5304: LoadField: r1 = r0->field_b
    //     0x14e5304: ldur            w1, [x0, #0xb]
    // 0x14e5308: DecompressPointer r1
    //     0x14e5308: add             x1, x1, HEAP, lsl #32
    // 0x14e530c: cmp             w1, NULL
    // 0x14e5310: b.eq            #0x14e5360
    // 0x14e5314: LoadField: r0 = r1->field_f
    //     0x14e5314: ldur            w0, [x1, #0xf]
    // 0x14e5318: DecompressPointer r0
    //     0x14e5318: add             x0, x0, HEAP, lsl #32
    // 0x14e531c: LoadField: r1 = r0->field_b
    //     0x14e531c: ldur            w1, [x0, #0xb]
    // 0x14e5320: cmp             w1, #2
    // 0x14e5324: b.ne            #0x14e5360
    // 0x14e5328: r0 = Radius()
    //     0x14e5328: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e532c: d0 = 20.000000
    //     0x14e532c: fmov            d0, #20.00000000
    // 0x14e5330: stur            x0, [fp, #-0x18]
    // 0x14e5334: StoreField: r0->field_7 = d0
    //     0x14e5334: stur            d0, [x0, #7]
    // 0x14e5338: StoreField: r0->field_f = d0
    //     0x14e5338: stur            d0, [x0, #0xf]
    // 0x14e533c: r0 = BorderRadius()
    //     0x14e533c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e5340: mov             x1, x0
    // 0x14e5344: ldur            x0, [fp, #-0x18]
    // 0x14e5348: StoreField: r1->field_7 = r0
    //     0x14e5348: stur            w0, [x1, #7]
    // 0x14e534c: StoreField: r1->field_b = r0
    //     0x14e534c: stur            w0, [x1, #0xb]
    // 0x14e5350: StoreField: r1->field_f = r0
    //     0x14e5350: stur            w0, [x1, #0xf]
    // 0x14e5354: StoreField: r1->field_13 = r0
    //     0x14e5354: stur            w0, [x1, #0x13]
    // 0x14e5358: mov             x2, x1
    // 0x14e535c: b               #0x14e56b0
    // 0x14e5360: ldur            x0, [fp, #-8]
    // 0x14e5364: LoadField: r1 = r0->field_f
    //     0x14e5364: ldur            w1, [x0, #0xf]
    // 0x14e5368: DecompressPointer r1
    //     0x14e5368: add             x1, x1, HEAP, lsl #32
    // 0x14e536c: r0 = controller()
    //     0x14e536c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5370: LoadField: r1 = r0->field_4f
    //     0x14e5370: ldur            w1, [x0, #0x4f]
    // 0x14e5374: DecompressPointer r1
    //     0x14e5374: add             x1, x1, HEAP, lsl #32
    // 0x14e5378: r0 = value()
    //     0x14e5378: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e537c: LoadField: r1 = r0->field_b
    //     0x14e537c: ldur            w1, [x0, #0xb]
    // 0x14e5380: DecompressPointer r1
    //     0x14e5380: add             x1, x1, HEAP, lsl #32
    // 0x14e5384: cmp             w1, NULL
    // 0x14e5388: b.eq            #0x14e5470
    // 0x14e538c: LoadField: r0 = r1->field_f
    //     0x14e538c: ldur            w0, [x1, #0xf]
    // 0x14e5390: DecompressPointer r0
    //     0x14e5390: add             x0, x0, HEAP, lsl #32
    // 0x14e5394: LoadField: r1 = r0->field_b
    //     0x14e5394: ldur            w1, [x0, #0xb]
    // 0x14e5398: cmp             w1, #4
    // 0x14e539c: b.ne            #0x14e5470
    // 0x14e53a0: ldur            x2, [fp, #-0x10]
    // 0x14e53a4: LoadField: r0 = r2->field_13
    //     0x14e53a4: ldur            w0, [x2, #0x13]
    // 0x14e53a8: DecompressPointer r0
    //     0x14e53a8: add             x0, x0, HEAP, lsl #32
    // 0x14e53ac: r1 = LoadInt32Instr(r0)
    //     0x14e53ac: sbfx            x1, x0, #1, #0x1f
    //     0x14e53b0: tbz             w0, #0, #0x14e53b8
    //     0x14e53b4: ldur            x1, [x0, #7]
    // 0x14e53b8: cbz             x1, #0x14e53c8
    // 0x14e53bc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14e53bc: ldur            w0, [x2, #0x17]
    // 0x14e53c0: DecompressPointer r0
    //     0x14e53c0: add             x0, x0, HEAP, lsl #32
    // 0x14e53c4: tbnz            w0, #4, #0x14e53d4
    // 0x14e53c8: r0 = Instance_Radius
    //     0x14e53c8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e53cc: ldr             x0, [x0, #0x758]
    // 0x14e53d0: b               #0x14e53dc
    // 0x14e53d4: r0 = Instance_Radius
    //     0x14e53d4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e53d8: ldr             x0, [x0, #0xb48]
    // 0x14e53dc: stur            x0, [fp, #-0x30]
    // 0x14e53e0: cbnz            x1, #0x14e53f0
    // 0x14e53e4: r3 = Instance_Radius
    //     0x14e53e4: add             x3, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e53e8: ldr             x3, [x3, #0xb48]
    // 0x14e53ec: b               #0x14e53f8
    // 0x14e53f0: r3 = Instance_Radius
    //     0x14e53f0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e53f4: ldr             x3, [x3, #0x758]
    // 0x14e53f8: stur            x3, [fp, #-0x28]
    // 0x14e53fc: cbz             x1, #0x14e540c
    // 0x14e5400: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x14e5400: ldur            w4, [x2, #0x17]
    // 0x14e5404: DecompressPointer r4
    //     0x14e5404: add             x4, x4, HEAP, lsl #32
    // 0x14e5408: tbnz            w4, #4, #0x14e5418
    // 0x14e540c: r4 = Instance_Radius
    //     0x14e540c: add             x4, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e5410: ldr             x4, [x4, #0x758]
    // 0x14e5414: b               #0x14e5420
    // 0x14e5418: r4 = Instance_Radius
    //     0x14e5418: add             x4, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e541c: ldr             x4, [x4, #0xb48]
    // 0x14e5420: stur            x4, [fp, #-0x20]
    // 0x14e5424: cbnz            x1, #0x14e5434
    // 0x14e5428: r1 = Instance_Radius
    //     0x14e5428: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e542c: ldr             x1, [x1, #0xb48]
    // 0x14e5430: b               #0x14e543c
    // 0x14e5434: r1 = Instance_Radius
    //     0x14e5434: add             x1, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e5438: ldr             x1, [x1, #0x758]
    // 0x14e543c: stur            x1, [fp, #-0x18]
    // 0x14e5440: r0 = BorderRadius()
    //     0x14e5440: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e5444: mov             x1, x0
    // 0x14e5448: ldur            x0, [fp, #-0x20]
    // 0x14e544c: StoreField: r1->field_7 = r0
    //     0x14e544c: stur            w0, [x1, #7]
    // 0x14e5450: ldur            x0, [fp, #-0x30]
    // 0x14e5454: StoreField: r1->field_b = r0
    //     0x14e5454: stur            w0, [x1, #0xb]
    // 0x14e5458: ldur            x0, [fp, #-0x18]
    // 0x14e545c: StoreField: r1->field_f = r0
    //     0x14e545c: stur            w0, [x1, #0xf]
    // 0x14e5460: ldur            x0, [fp, #-0x28]
    // 0x14e5464: StoreField: r1->field_13 = r0
    //     0x14e5464: stur            w0, [x1, #0x13]
    // 0x14e5468: mov             x0, x1
    // 0x14e546c: b               #0x14e56ac
    // 0x14e5470: ldur            x2, [fp, #-0x10]
    // 0x14e5474: LoadField: r0 = r2->field_13
    //     0x14e5474: ldur            w0, [x2, #0x13]
    // 0x14e5478: DecompressPointer r0
    //     0x14e5478: add             x0, x0, HEAP, lsl #32
    // 0x14e547c: cbnz            w0, #0x14e548c
    // 0x14e5480: r0 = Instance_Radius
    //     0x14e5480: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e5484: ldr             x0, [x0, #0x758]
    // 0x14e5488: b               #0x14e54c4
    // 0x14e548c: ldur            x0, [fp, #-8]
    // 0x14e5490: LoadField: r1 = r0->field_f
    //     0x14e5490: ldur            w1, [x0, #0xf]
    // 0x14e5494: DecompressPointer r1
    //     0x14e5494: add             x1, x1, HEAP, lsl #32
    // 0x14e5498: r0 = controller()
    //     0x14e5498: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e549c: LoadField: r1 = r0->field_4f
    //     0x14e549c: ldur            w1, [x0, #0x4f]
    // 0x14e54a0: DecompressPointer r1
    //     0x14e54a0: add             x1, x1, HEAP, lsl #32
    // 0x14e54a4: r0 = value()
    //     0x14e54a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e54a8: LoadField: r1 = r0->field_b
    //     0x14e54a8: ldur            w1, [x0, #0xb]
    // 0x14e54ac: DecompressPointer r1
    //     0x14e54ac: add             x1, x1, HEAP, lsl #32
    // 0x14e54b0: cmp             w1, NULL
    // 0x14e54b4: b.eq            #0x14e54b8
    // 0x14e54b8: ldur            x2, [fp, #-0x10]
    // 0x14e54bc: r0 = Instance_Radius
    //     0x14e54bc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e54c0: ldr             x0, [x0, #0xb48]
    // 0x14e54c4: stur            x0, [fp, #-0x18]
    // 0x14e54c8: LoadField: r1 = r2->field_13
    //     0x14e54c8: ldur            w1, [x2, #0x13]
    // 0x14e54cc: DecompressPointer r1
    //     0x14e54cc: add             x1, x1, HEAP, lsl #32
    // 0x14e54d0: r3 = LoadInt32Instr(r1)
    //     0x14e54d0: sbfx            x3, x1, #1, #0x1f
    //     0x14e54d4: tbz             w1, #0, #0x14e54dc
    //     0x14e54d8: ldur            x3, [x1, #7]
    // 0x14e54dc: stur            x3, [fp, #-0x38]
    // 0x14e54e0: cbnz            x3, #0x14e54f0
    // 0x14e54e4: r0 = Instance_Radius
    //     0x14e54e4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e54e8: ldr             x0, [x0, #0xb48]
    // 0x14e54ec: b               #0x14e5570
    // 0x14e54f0: ldur            x4, [fp, #-8]
    // 0x14e54f4: LoadField: r1 = r4->field_f
    //     0x14e54f4: ldur            w1, [x4, #0xf]
    // 0x14e54f8: DecompressPointer r1
    //     0x14e54f8: add             x1, x1, HEAP, lsl #32
    // 0x14e54fc: r0 = controller()
    //     0x14e54fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5500: LoadField: r1 = r0->field_4f
    //     0x14e5500: ldur            w1, [x0, #0x4f]
    // 0x14e5504: DecompressPointer r1
    //     0x14e5504: add             x1, x1, HEAP, lsl #32
    // 0x14e5508: r0 = value()
    //     0x14e5508: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e550c: LoadField: r1 = r0->field_b
    //     0x14e550c: ldur            w1, [x0, #0xb]
    // 0x14e5510: DecompressPointer r1
    //     0x14e5510: add             x1, x1, HEAP, lsl #32
    // 0x14e5514: cmp             w1, NULL
    // 0x14e5518: b.ne            #0x14e5524
    // 0x14e551c: r0 = Null
    //     0x14e551c: mov             x0, NULL
    // 0x14e5520: b               #0x14e5534
    // 0x14e5524: LoadField: r0 = r1->field_f
    //     0x14e5524: ldur            w0, [x1, #0xf]
    // 0x14e5528: DecompressPointer r0
    //     0x14e5528: add             x0, x0, HEAP, lsl #32
    // 0x14e552c: LoadField: r1 = r0->field_b
    //     0x14e552c: ldur            w1, [x0, #0xb]
    // 0x14e5530: mov             x0, x1
    // 0x14e5534: cmp             w0, NULL
    // 0x14e5538: b.ne            #0x14e5544
    // 0x14e553c: r1 = 0
    //     0x14e553c: movz            x1, #0
    // 0x14e5540: b               #0x14e5548
    // 0x14e5544: r1 = LoadInt32Instr(r0)
    //     0x14e5544: sbfx            x1, x0, #1, #0x1f
    // 0x14e5548: ldur            x0, [fp, #-0x38]
    // 0x14e554c: sub             x2, x1, #1
    // 0x14e5550: cmp             x0, x2
    // 0x14e5554: b.ne            #0x14e5564
    // 0x14e5558: r0 = Instance_Radius
    //     0x14e5558: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e555c: ldr             x0, [x0, #0x758]
    // 0x14e5560: b               #0x14e556c
    // 0x14e5564: r0 = Instance_Radius
    //     0x14e5564: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e5568: ldr             x0, [x0, #0xb48]
    // 0x14e556c: ldur            x2, [fp, #-0x10]
    // 0x14e5570: stur            x0, [fp, #-0x20]
    // 0x14e5574: LoadField: r1 = r2->field_13
    //     0x14e5574: ldur            w1, [x2, #0x13]
    // 0x14e5578: DecompressPointer r1
    //     0x14e5578: add             x1, x1, HEAP, lsl #32
    // 0x14e557c: cbnz            w1, #0x14e558c
    // 0x14e5580: r0 = Instance_Radius
    //     0x14e5580: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e5584: ldr             x0, [x0, #0x758]
    // 0x14e5588: b               #0x14e55c4
    // 0x14e558c: ldur            x3, [fp, #-8]
    // 0x14e5590: LoadField: r1 = r3->field_f
    //     0x14e5590: ldur            w1, [x3, #0xf]
    // 0x14e5594: DecompressPointer r1
    //     0x14e5594: add             x1, x1, HEAP, lsl #32
    // 0x14e5598: r0 = controller()
    //     0x14e5598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e559c: LoadField: r1 = r0->field_4f
    //     0x14e559c: ldur            w1, [x0, #0x4f]
    // 0x14e55a0: DecompressPointer r1
    //     0x14e55a0: add             x1, x1, HEAP, lsl #32
    // 0x14e55a4: r0 = value()
    //     0x14e55a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e55a8: LoadField: r1 = r0->field_b
    //     0x14e55a8: ldur            w1, [x0, #0xb]
    // 0x14e55ac: DecompressPointer r1
    //     0x14e55ac: add             x1, x1, HEAP, lsl #32
    // 0x14e55b0: cmp             w1, NULL
    // 0x14e55b4: b.eq            #0x14e55b8
    // 0x14e55b8: ldur            x2, [fp, #-0x10]
    // 0x14e55bc: r0 = Instance_Radius
    //     0x14e55bc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e55c0: ldr             x0, [x0, #0xb48]
    // 0x14e55c4: stur            x0, [fp, #-0x28]
    // 0x14e55c8: LoadField: r1 = r2->field_13
    //     0x14e55c8: ldur            w1, [x2, #0x13]
    // 0x14e55cc: DecompressPointer r1
    //     0x14e55cc: add             x1, x1, HEAP, lsl #32
    // 0x14e55d0: r3 = LoadInt32Instr(r1)
    //     0x14e55d0: sbfx            x3, x1, #1, #0x1f
    //     0x14e55d4: tbz             w1, #0, #0x14e55dc
    //     0x14e55d8: ldur            x3, [x1, #7]
    // 0x14e55dc: stur            x3, [fp, #-0x38]
    // 0x14e55e0: cbnz            x3, #0x14e55f0
    // 0x14e55e4: r3 = Instance_Radius
    //     0x14e55e4: add             x3, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e55e8: ldr             x3, [x3, #0xb48]
    // 0x14e55ec: b               #0x14e5674
    // 0x14e55f0: ldur            x4, [fp, #-8]
    // 0x14e55f4: LoadField: r1 = r4->field_f
    //     0x14e55f4: ldur            w1, [x4, #0xf]
    // 0x14e55f8: DecompressPointer r1
    //     0x14e55f8: add             x1, x1, HEAP, lsl #32
    // 0x14e55fc: r0 = controller()
    //     0x14e55fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5600: LoadField: r1 = r0->field_4f
    //     0x14e5600: ldur            w1, [x0, #0x4f]
    // 0x14e5604: DecompressPointer r1
    //     0x14e5604: add             x1, x1, HEAP, lsl #32
    // 0x14e5608: r0 = value()
    //     0x14e5608: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e560c: LoadField: r1 = r0->field_b
    //     0x14e560c: ldur            w1, [x0, #0xb]
    // 0x14e5610: DecompressPointer r1
    //     0x14e5610: add             x1, x1, HEAP, lsl #32
    // 0x14e5614: cmp             w1, NULL
    // 0x14e5618: b.ne            #0x14e5624
    // 0x14e561c: r0 = Null
    //     0x14e561c: mov             x0, NULL
    // 0x14e5620: b               #0x14e5634
    // 0x14e5624: LoadField: r0 = r1->field_f
    //     0x14e5624: ldur            w0, [x1, #0xf]
    // 0x14e5628: DecompressPointer r0
    //     0x14e5628: add             x0, x0, HEAP, lsl #32
    // 0x14e562c: LoadField: r1 = r0->field_b
    //     0x14e562c: ldur            w1, [x0, #0xb]
    // 0x14e5630: mov             x0, x1
    // 0x14e5634: cmp             w0, NULL
    // 0x14e5638: b.ne            #0x14e5644
    // 0x14e563c: r1 = 0
    //     0x14e563c: movz            x1, #0
    // 0x14e5640: b               #0x14e5648
    // 0x14e5644: r1 = LoadInt32Instr(r0)
    //     0x14e5644: sbfx            x1, x0, #1, #0x1f
    // 0x14e5648: ldur            x0, [fp, #-0x38]
    // 0x14e564c: sub             x2, x1, #1
    // 0x14e5650: cmp             x0, x2
    // 0x14e5654: b.ne            #0x14e5664
    // 0x14e5658: r0 = Instance_Radius
    //     0x14e5658: add             x0, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14e565c: ldr             x0, [x0, #0x758]
    // 0x14e5660: b               #0x14e566c
    // 0x14e5664: r0 = Instance_Radius
    //     0x14e5664: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x14e5668: ldr             x0, [x0, #0xb48]
    // 0x14e566c: mov             x3, x0
    // 0x14e5670: ldur            x0, [fp, #-0x28]
    // 0x14e5674: ldur            x2, [fp, #-0x18]
    // 0x14e5678: ldur            x1, [fp, #-0x20]
    // 0x14e567c: stur            x3, [fp, #-0x30]
    // 0x14e5680: r0 = BorderRadius()
    //     0x14e5680: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e5684: mov             x1, x0
    // 0x14e5688: ldur            x0, [fp, #-0x28]
    // 0x14e568c: StoreField: r1->field_7 = r0
    //     0x14e568c: stur            w0, [x1, #7]
    // 0x14e5690: ldur            x0, [fp, #-0x18]
    // 0x14e5694: StoreField: r1->field_b = r0
    //     0x14e5694: stur            w0, [x1, #0xb]
    // 0x14e5698: ldur            x0, [fp, #-0x30]
    // 0x14e569c: StoreField: r1->field_f = r0
    //     0x14e569c: stur            w0, [x1, #0xf]
    // 0x14e56a0: ldur            x0, [fp, #-0x20]
    // 0x14e56a4: StoreField: r1->field_13 = r0
    //     0x14e56a4: stur            w0, [x1, #0x13]
    // 0x14e56a8: mov             x0, x1
    // 0x14e56ac: mov             x2, x0
    // 0x14e56b0: ldur            x0, [fp, #-8]
    // 0x14e56b4: stur            x2, [fp, #-0x18]
    // 0x14e56b8: LoadField: r1 = r0->field_f
    //     0x14e56b8: ldur            w1, [x0, #0xf]
    // 0x14e56bc: DecompressPointer r1
    //     0x14e56bc: add             x1, x1, HEAP, lsl #32
    // 0x14e56c0: r0 = controller()
    //     0x14e56c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e56c4: LoadField: r1 = r0->field_73
    //     0x14e56c4: ldur            w1, [x0, #0x73]
    // 0x14e56c8: DecompressPointer r1
    //     0x14e56c8: add             x1, x1, HEAP, lsl #32
    // 0x14e56cc: r0 = value()
    //     0x14e56cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e56d0: mov             x2, x0
    // 0x14e56d4: ldur            x0, [fp, #-8]
    // 0x14e56d8: stur            x2, [fp, #-0x20]
    // 0x14e56dc: LoadField: r1 = r0->field_f
    //     0x14e56dc: ldur            w1, [x0, #0xf]
    // 0x14e56e0: DecompressPointer r1
    //     0x14e56e0: add             x1, x1, HEAP, lsl #32
    // 0x14e56e4: r0 = controller()
    //     0x14e56e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e56e8: LoadField: r1 = r0->field_4f
    //     0x14e56e8: ldur            w1, [x0, #0x4f]
    // 0x14e56ec: DecompressPointer r1
    //     0x14e56ec: add             x1, x1, HEAP, lsl #32
    // 0x14e56f0: r0 = value()
    //     0x14e56f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e56f4: LoadField: r1 = r0->field_b
    //     0x14e56f4: ldur            w1, [x0, #0xb]
    // 0x14e56f8: DecompressPointer r1
    //     0x14e56f8: add             x1, x1, HEAP, lsl #32
    // 0x14e56fc: cmp             w1, NULL
    // 0x14e5700: b.ne            #0x14e5710
    // 0x14e5704: ldur            x2, [fp, #-0x10]
    // 0x14e5708: r1 = Null
    //     0x14e5708: mov             x1, NULL
    // 0x14e570c: b               #0x14e5764
    // 0x14e5710: ldur            x2, [fp, #-0x10]
    // 0x14e5714: LoadField: r3 = r1->field_f
    //     0x14e5714: ldur            w3, [x1, #0xf]
    // 0x14e5718: DecompressPointer r3
    //     0x14e5718: add             x3, x3, HEAP, lsl #32
    // 0x14e571c: LoadField: r0 = r2->field_13
    //     0x14e571c: ldur            w0, [x2, #0x13]
    // 0x14e5720: DecompressPointer r0
    //     0x14e5720: add             x0, x0, HEAP, lsl #32
    // 0x14e5724: LoadField: r1 = r3->field_b
    //     0x14e5724: ldur            w1, [x3, #0xb]
    // 0x14e5728: r4 = LoadInt32Instr(r0)
    //     0x14e5728: sbfx            x4, x0, #1, #0x1f
    //     0x14e572c: tbz             w0, #0, #0x14e5734
    //     0x14e5730: ldur            x4, [x0, #7]
    // 0x14e5734: r0 = LoadInt32Instr(r1)
    //     0x14e5734: sbfx            x0, x1, #1, #0x1f
    // 0x14e5738: mov             x1, x4
    // 0x14e573c: cmp             x1, x0
    // 0x14e5740: b.hs            #0x14e5cfc
    // 0x14e5744: LoadField: r0 = r3->field_f
    //     0x14e5744: ldur            w0, [x3, #0xf]
    // 0x14e5748: DecompressPointer r0
    //     0x14e5748: add             x0, x0, HEAP, lsl #32
    // 0x14e574c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14e574c: add             x16, x0, x4, lsl #2
    //     0x14e5750: ldur            w1, [x16, #0xf]
    // 0x14e5754: DecompressPointer r1
    //     0x14e5754: add             x1, x1, HEAP, lsl #32
    // 0x14e5758: LoadField: r0 = r1->field_b
    //     0x14e5758: ldur            w0, [x1, #0xb]
    // 0x14e575c: DecompressPointer r0
    //     0x14e575c: add             x0, x0, HEAP, lsl #32
    // 0x14e5760: mov             x1, x0
    // 0x14e5764: ldur            x0, [fp, #-0x20]
    // 0x14e5768: r3 = LoadClassIdInstr(r0)
    //     0x14e5768: ldur            x3, [x0, #-1]
    //     0x14e576c: ubfx            x3, x3, #0xc, #0x14
    // 0x14e5770: stp             x1, x0, [SP]
    // 0x14e5774: mov             x0, x3
    // 0x14e5778: mov             lr, x0
    // 0x14e577c: ldr             lr, [x21, lr, lsl #3]
    // 0x14e5780: blr             lr
    // 0x14e5784: tbnz            w0, #4, #0x14e57a0
    // 0x14e5788: r1 = Instance_Color
    //     0x14e5788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e578c: d0 = 0.030000
    //     0x14e578c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14e5790: ldr             d0, [x17, #0x238]
    // 0x14e5794: r0 = withOpacity()
    //     0x14e5794: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e5798: mov             x2, x0
    // 0x14e579c: b               #0x14e57a4
    // 0x14e57a0: r2 = Instance_Color
    //     0x14e57a0: ldr             x2, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14e57a4: ldur            x0, [fp, #-8]
    // 0x14e57a8: ldur            x1, [fp, #-0x18]
    // 0x14e57ac: stur            x2, [fp, #-0x20]
    // 0x14e57b0: r0 = BoxDecoration()
    //     0x14e57b0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e57b4: mov             x2, x0
    // 0x14e57b8: ldur            x0, [fp, #-0x20]
    // 0x14e57bc: stur            x2, [fp, #-0x28]
    // 0x14e57c0: StoreField: r2->field_7 = r0
    //     0x14e57c0: stur            w0, [x2, #7]
    // 0x14e57c4: ldur            x0, [fp, #-0x18]
    // 0x14e57c8: StoreField: r2->field_13 = r0
    //     0x14e57c8: stur            w0, [x2, #0x13]
    // 0x14e57cc: r0 = Instance_BoxShape
    //     0x14e57cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e57d0: ldr             x0, [x0, #0x80]
    // 0x14e57d4: StoreField: r2->field_23 = r0
    //     0x14e57d4: stur            w0, [x2, #0x23]
    // 0x14e57d8: ldur            x0, [fp, #-8]
    // 0x14e57dc: LoadField: r1 = r0->field_f
    //     0x14e57dc: ldur            w1, [x0, #0xf]
    // 0x14e57e0: DecompressPointer r1
    //     0x14e57e0: add             x1, x1, HEAP, lsl #32
    // 0x14e57e4: r0 = controller()
    //     0x14e57e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e57e8: LoadField: r1 = r0->field_4f
    //     0x14e57e8: ldur            w1, [x0, #0x4f]
    // 0x14e57ec: DecompressPointer r1
    //     0x14e57ec: add             x1, x1, HEAP, lsl #32
    // 0x14e57f0: r0 = value()
    //     0x14e57f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e57f4: LoadField: r1 = r0->field_b
    //     0x14e57f4: ldur            w1, [x0, #0xb]
    // 0x14e57f8: DecompressPointer r1
    //     0x14e57f8: add             x1, x1, HEAP, lsl #32
    // 0x14e57fc: cmp             w1, NULL
    // 0x14e5800: b.ne            #0x14e5810
    // 0x14e5804: ldur            x2, [fp, #-0x10]
    // 0x14e5808: r1 = Null
    //     0x14e5808: mov             x1, NULL
    // 0x14e580c: b               #0x14e5864
    // 0x14e5810: ldur            x2, [fp, #-0x10]
    // 0x14e5814: LoadField: r3 = r1->field_f
    //     0x14e5814: ldur            w3, [x1, #0xf]
    // 0x14e5818: DecompressPointer r3
    //     0x14e5818: add             x3, x3, HEAP, lsl #32
    // 0x14e581c: LoadField: r0 = r2->field_13
    //     0x14e581c: ldur            w0, [x2, #0x13]
    // 0x14e5820: DecompressPointer r0
    //     0x14e5820: add             x0, x0, HEAP, lsl #32
    // 0x14e5824: LoadField: r1 = r3->field_b
    //     0x14e5824: ldur            w1, [x3, #0xb]
    // 0x14e5828: r4 = LoadInt32Instr(r0)
    //     0x14e5828: sbfx            x4, x0, #1, #0x1f
    //     0x14e582c: tbz             w0, #0, #0x14e5834
    //     0x14e5830: ldur            x4, [x0, #7]
    // 0x14e5834: r0 = LoadInt32Instr(r1)
    //     0x14e5834: sbfx            x0, x1, #1, #0x1f
    // 0x14e5838: mov             x1, x4
    // 0x14e583c: cmp             x1, x0
    // 0x14e5840: b.hs            #0x14e5d00
    // 0x14e5844: LoadField: r0 = r3->field_f
    //     0x14e5844: ldur            w0, [x3, #0xf]
    // 0x14e5848: DecompressPointer r0
    //     0x14e5848: add             x0, x0, HEAP, lsl #32
    // 0x14e584c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14e584c: add             x16, x0, x4, lsl #2
    //     0x14e5850: ldur            w1, [x16, #0xf]
    // 0x14e5854: DecompressPointer r1
    //     0x14e5854: add             x1, x1, HEAP, lsl #32
    // 0x14e5858: LoadField: r0 = r1->field_f
    //     0x14e5858: ldur            w0, [x1, #0xf]
    // 0x14e585c: DecompressPointer r0
    //     0x14e585c: add             x0, x0, HEAP, lsl #32
    // 0x14e5860: mov             x1, x0
    // 0x14e5864: ldur            x0, [fp, #-8]
    // 0x14e5868: str             x1, [SP]
    // 0x14e586c: r0 = _interpolateSingle()
    //     0x14e586c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14e5870: ldur            x2, [fp, #-0x10]
    // 0x14e5874: stur            x0, [fp, #-0x18]
    // 0x14e5878: LoadField: r1 = r2->field_f
    //     0x14e5878: ldur            w1, [x2, #0xf]
    // 0x14e587c: DecompressPointer r1
    //     0x14e587c: add             x1, x1, HEAP, lsl #32
    // 0x14e5880: r0 = of()
    //     0x14e5880: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e5884: LoadField: r1 = r0->field_87
    //     0x14e5884: ldur            w1, [x0, #0x87]
    // 0x14e5888: DecompressPointer r1
    //     0x14e5888: add             x1, x1, HEAP, lsl #32
    // 0x14e588c: LoadField: r0 = r1->field_7
    //     0x14e588c: ldur            w0, [x1, #7]
    // 0x14e5890: DecompressPointer r0
    //     0x14e5890: add             x0, x0, HEAP, lsl #32
    // 0x14e5894: stur            x0, [fp, #-0x20]
    // 0x14e5898: r1 = Instance_Color
    //     0x14e5898: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e589c: d0 = 0.700000
    //     0x14e589c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e58a0: ldr             d0, [x17, #0xf48]
    // 0x14e58a4: r0 = withOpacity()
    //     0x14e58a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e58a8: r16 = 14.000000
    //     0x14e58a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14e58ac: ldr             x16, [x16, #0x1d8]
    // 0x14e58b0: stp             x16, x0, [SP]
    // 0x14e58b4: ldur            x1, [fp, #-0x20]
    // 0x14e58b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e58b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e58bc: ldr             x4, [x4, #0x9b8]
    // 0x14e58c0: r0 = copyWith()
    //     0x14e58c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e58c4: stur            x0, [fp, #-0x20]
    // 0x14e58c8: r0 = Text()
    //     0x14e58c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14e58cc: mov             x2, x0
    // 0x14e58d0: ldur            x0, [fp, #-0x18]
    // 0x14e58d4: stur            x2, [fp, #-0x30]
    // 0x14e58d8: StoreField: r2->field_b = r0
    //     0x14e58d8: stur            w0, [x2, #0xb]
    // 0x14e58dc: ldur            x0, [fp, #-0x20]
    // 0x14e58e0: StoreField: r2->field_13 = r0
    //     0x14e58e0: stur            w0, [x2, #0x13]
    // 0x14e58e4: r1 = <FlexParentData>
    //     0x14e58e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14e58e8: ldr             x1, [x1, #0xe00]
    // 0x14e58ec: r0 = Expanded()
    //     0x14e58ec: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14e58f0: mov             x2, x0
    // 0x14e58f4: r0 = 1
    //     0x14e58f4: movz            x0, #0x1
    // 0x14e58f8: stur            x2, [fp, #-0x18]
    // 0x14e58fc: StoreField: r2->field_13 = r0
    //     0x14e58fc: stur            x0, [x2, #0x13]
    // 0x14e5900: r0 = Instance_FlexFit
    //     0x14e5900: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14e5904: ldr             x0, [x0, #0xe08]
    // 0x14e5908: StoreField: r2->field_1b = r0
    //     0x14e5908: stur            w0, [x2, #0x1b]
    // 0x14e590c: ldur            x0, [fp, #-0x30]
    // 0x14e5910: StoreField: r2->field_b = r0
    //     0x14e5910: stur            w0, [x2, #0xb]
    // 0x14e5914: ldur            x0, [fp, #-8]
    // 0x14e5918: LoadField: r1 = r0->field_f
    //     0x14e5918: ldur            w1, [x0, #0xf]
    // 0x14e591c: DecompressPointer r1
    //     0x14e591c: add             x1, x1, HEAP, lsl #32
    // 0x14e5920: r0 = controller()
    //     0x14e5920: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5924: LoadField: r1 = r0->field_4f
    //     0x14e5924: ldur            w1, [x0, #0x4f]
    // 0x14e5928: DecompressPointer r1
    //     0x14e5928: add             x1, x1, HEAP, lsl #32
    // 0x14e592c: r0 = value()
    //     0x14e592c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5930: LoadField: r1 = r0->field_b
    //     0x14e5930: ldur            w1, [x0, #0xb]
    // 0x14e5934: DecompressPointer r1
    //     0x14e5934: add             x1, x1, HEAP, lsl #32
    // 0x14e5938: cmp             w1, NULL
    // 0x14e593c: b.ne            #0x14e594c
    // 0x14e5940: ldur            x3, [fp, #-0x10]
    // 0x14e5944: r5 = Null
    //     0x14e5944: mov             x5, NULL
    // 0x14e5948: b               #0x14e59bc
    // 0x14e594c: ldur            x3, [fp, #-0x10]
    // 0x14e5950: LoadField: r2 = r1->field_f
    //     0x14e5950: ldur            w2, [x1, #0xf]
    // 0x14e5954: DecompressPointer r2
    //     0x14e5954: add             x2, x2, HEAP, lsl #32
    // 0x14e5958: LoadField: r0 = r3->field_13
    //     0x14e5958: ldur            w0, [x3, #0x13]
    // 0x14e595c: DecompressPointer r0
    //     0x14e595c: add             x0, x0, HEAP, lsl #32
    // 0x14e5960: LoadField: r1 = r2->field_b
    //     0x14e5960: ldur            w1, [x2, #0xb]
    // 0x14e5964: r4 = LoadInt32Instr(r0)
    //     0x14e5964: sbfx            x4, x0, #1, #0x1f
    //     0x14e5968: tbz             w0, #0, #0x14e5970
    //     0x14e596c: ldur            x4, [x0, #7]
    // 0x14e5970: r0 = LoadInt32Instr(r1)
    //     0x14e5970: sbfx            x0, x1, #1, #0x1f
    // 0x14e5974: mov             x1, x4
    // 0x14e5978: cmp             x1, x0
    // 0x14e597c: b.hs            #0x14e5d04
    // 0x14e5980: LoadField: r0 = r2->field_f
    //     0x14e5980: ldur            w0, [x2, #0xf]
    // 0x14e5984: DecompressPointer r0
    //     0x14e5984: add             x0, x0, HEAP, lsl #32
    // 0x14e5988: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14e5988: add             x16, x0, x4, lsl #2
    //     0x14e598c: ldur            w1, [x16, #0xf]
    // 0x14e5990: DecompressPointer r1
    //     0x14e5990: add             x1, x1, HEAP, lsl #32
    // 0x14e5994: LoadField: r0 = r1->field_13
    //     0x14e5994: ldur            w0, [x1, #0x13]
    // 0x14e5998: DecompressPointer r0
    //     0x14e5998: add             x0, x0, HEAP, lsl #32
    // 0x14e599c: cmp             w0, NULL
    // 0x14e59a0: b.ne            #0x14e59ac
    // 0x14e59a4: r0 = Null
    //     0x14e59a4: mov             x0, NULL
    // 0x14e59a8: b               #0x14e59b8
    // 0x14e59ac: LoadField: r1 = r0->field_7
    //     0x14e59ac: ldur            w1, [x0, #7]
    // 0x14e59b0: DecompressPointer r1
    //     0x14e59b0: add             x1, x1, HEAP, lsl #32
    // 0x14e59b4: mov             x0, x1
    // 0x14e59b8: mov             x5, x0
    // 0x14e59bc: ldur            x4, [fp, #-8]
    // 0x14e59c0: ldur            x0, [fp, #-0x18]
    // 0x14e59c4: stur            x5, [fp, #-0x20]
    // 0x14e59c8: r1 = Null
    //     0x14e59c8: mov             x1, NULL
    // 0x14e59cc: r2 = 4
    //     0x14e59cc: movz            x2, #0x4
    // 0x14e59d0: r0 = AllocateArray()
    //     0x14e59d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e59d4: mov             x1, x0
    // 0x14e59d8: ldur            x0, [fp, #-0x20]
    // 0x14e59dc: StoreField: r1->field_f = r0
    //     0x14e59dc: stur            w0, [x1, #0xf]
    // 0x14e59e0: r16 = " Charges"
    //     0x14e59e0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40770] " Charges"
    //     0x14e59e4: ldr             x16, [x16, #0x770]
    // 0x14e59e8: StoreField: r1->field_13 = r16
    //     0x14e59e8: stur            w16, [x1, #0x13]
    // 0x14e59ec: str             x1, [SP]
    // 0x14e59f0: r0 = _interpolate()
    //     0x14e59f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14e59f4: ldur            x2, [fp, #-0x10]
    // 0x14e59f8: stur            x0, [fp, #-0x20]
    // 0x14e59fc: LoadField: r1 = r2->field_f
    //     0x14e59fc: ldur            w1, [x2, #0xf]
    // 0x14e5a00: DecompressPointer r1
    //     0x14e5a00: add             x1, x1, HEAP, lsl #32
    // 0x14e5a04: r0 = of()
    //     0x14e5a04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e5a08: LoadField: r1 = r0->field_87
    //     0x14e5a08: ldur            w1, [x0, #0x87]
    // 0x14e5a0c: DecompressPointer r1
    //     0x14e5a0c: add             x1, x1, HEAP, lsl #32
    // 0x14e5a10: LoadField: r0 = r1->field_7
    //     0x14e5a10: ldur            w0, [x1, #7]
    // 0x14e5a14: DecompressPointer r0
    //     0x14e5a14: add             x0, x0, HEAP, lsl #32
    // 0x14e5a18: stur            x0, [fp, #-0x30]
    // 0x14e5a1c: r1 = Instance_Color
    //     0x14e5a1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e5a20: d0 = 0.700000
    //     0x14e5a20: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e5a24: ldr             d0, [x17, #0xf48]
    // 0x14e5a28: r0 = withOpacity()
    //     0x14e5a28: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e5a2c: r16 = 12.000000
    //     0x14e5a2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14e5a30: ldr             x16, [x16, #0x9e8]
    // 0x14e5a34: stp             x16, x0, [SP]
    // 0x14e5a38: ldur            x1, [fp, #-0x30]
    // 0x14e5a3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14e5a3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14e5a40: ldr             x4, [x4, #0x9b8]
    // 0x14e5a44: r0 = copyWith()
    //     0x14e5a44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e5a48: stur            x0, [fp, #-0x30]
    // 0x14e5a4c: r0 = Text()
    //     0x14e5a4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14e5a50: mov             x3, x0
    // 0x14e5a54: ldur            x0, [fp, #-0x20]
    // 0x14e5a58: stur            x3, [fp, #-0x40]
    // 0x14e5a5c: StoreField: r3->field_b = r0
    //     0x14e5a5c: stur            w0, [x3, #0xb]
    // 0x14e5a60: ldur            x0, [fp, #-0x30]
    // 0x14e5a64: StoreField: r3->field_13 = r0
    //     0x14e5a64: stur            w0, [x3, #0x13]
    // 0x14e5a68: r1 = Null
    //     0x14e5a68: mov             x1, NULL
    // 0x14e5a6c: r2 = 6
    //     0x14e5a6c: movz            x2, #0x6
    // 0x14e5a70: r0 = AllocateArray()
    //     0x14e5a70: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14e5a74: mov             x2, x0
    // 0x14e5a78: ldur            x0, [fp, #-0x18]
    // 0x14e5a7c: stur            x2, [fp, #-0x20]
    // 0x14e5a80: StoreField: r2->field_f = r0
    //     0x14e5a80: stur            w0, [x2, #0xf]
    // 0x14e5a84: r16 = Instance_SizedBox
    //     0x14e5a84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x14e5a88: ldr             x16, [x16, #0x998]
    // 0x14e5a8c: StoreField: r2->field_13 = r16
    //     0x14e5a8c: stur            w16, [x2, #0x13]
    // 0x14e5a90: ldur            x0, [fp, #-0x40]
    // 0x14e5a94: ArrayStore: r2[0] = r0  ; List_4
    //     0x14e5a94: stur            w0, [x2, #0x17]
    // 0x14e5a98: r1 = <Widget>
    //     0x14e5a98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14e5a9c: r0 = AllocateGrowableArray()
    //     0x14e5a9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14e5aa0: mov             x1, x0
    // 0x14e5aa4: ldur            x0, [fp, #-0x20]
    // 0x14e5aa8: stur            x1, [fp, #-0x18]
    // 0x14e5aac: StoreField: r1->field_f = r0
    //     0x14e5aac: stur            w0, [x1, #0xf]
    // 0x14e5ab0: r0 = 6
    //     0x14e5ab0: movz            x0, #0x6
    // 0x14e5ab4: StoreField: r1->field_b = r0
    //     0x14e5ab4: stur            w0, [x1, #0xb]
    // 0x14e5ab8: r0 = Row()
    //     0x14e5ab8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14e5abc: mov             x2, x0
    // 0x14e5ac0: r0 = Instance_Axis
    //     0x14e5ac0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14e5ac4: stur            x2, [fp, #-0x20]
    // 0x14e5ac8: StoreField: r2->field_f = r0
    //     0x14e5ac8: stur            w0, [x2, #0xf]
    // 0x14e5acc: r0 = Instance_MainAxisAlignment
    //     0x14e5acc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14e5ad0: ldr             x0, [x0, #0xa8]
    // 0x14e5ad4: StoreField: r2->field_13 = r0
    //     0x14e5ad4: stur            w0, [x2, #0x13]
    // 0x14e5ad8: r0 = Instance_MainAxisSize
    //     0x14e5ad8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14e5adc: ldr             x0, [x0, #0xa10]
    // 0x14e5ae0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14e5ae0: stur            w0, [x2, #0x17]
    // 0x14e5ae4: r0 = Instance_CrossAxisAlignment
    //     0x14e5ae4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14e5ae8: ldr             x0, [x0, #0xa18]
    // 0x14e5aec: StoreField: r2->field_1b = r0
    //     0x14e5aec: stur            w0, [x2, #0x1b]
    // 0x14e5af0: r0 = Instance_VerticalDirection
    //     0x14e5af0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14e5af4: ldr             x0, [x0, #0xa20]
    // 0x14e5af8: StoreField: r2->field_23 = r0
    //     0x14e5af8: stur            w0, [x2, #0x23]
    // 0x14e5afc: r0 = Instance_Clip
    //     0x14e5afc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14e5b00: ldr             x0, [x0, #0x38]
    // 0x14e5b04: StoreField: r2->field_2b = r0
    //     0x14e5b04: stur            w0, [x2, #0x2b]
    // 0x14e5b08: StoreField: r2->field_2f = rZR
    //     0x14e5b08: stur            xzr, [x2, #0x2f]
    // 0x14e5b0c: ldur            x0, [fp, #-0x18]
    // 0x14e5b10: StoreField: r2->field_b = r0
    //     0x14e5b10: stur            w0, [x2, #0xb]
    // 0x14e5b14: ldur            x0, [fp, #-0x10]
    // 0x14e5b18: LoadField: r1 = r0->field_f
    //     0x14e5b18: ldur            w1, [x0, #0xf]
    // 0x14e5b1c: DecompressPointer r1
    //     0x14e5b1c: add             x1, x1, HEAP, lsl #32
    // 0x14e5b20: r0 = of()
    //     0x14e5b20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e5b24: LoadField: r1 = r0->field_5b
    //     0x14e5b24: ldur            w1, [x0, #0x5b]
    // 0x14e5b28: DecompressPointer r1
    //     0x14e5b28: add             x1, x1, HEAP, lsl #32
    // 0x14e5b2c: r0 = LoadClassIdInstr(r1)
    //     0x14e5b2c: ldur            x0, [x1, #-1]
    //     0x14e5b30: ubfx            x0, x0, #0xc, #0x14
    // 0x14e5b34: d0 = 0.700000
    //     0x14e5b34: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e5b38: ldr             d0, [x17, #0xf48]
    // 0x14e5b3c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14e5b3c: sub             lr, x0, #0xffa
    //     0x14e5b40: ldr             lr, [x21, lr, lsl #3]
    //     0x14e5b44: blr             lr
    // 0x14e5b48: mov             x2, x0
    // 0x14e5b4c: ldur            x0, [fp, #-8]
    // 0x14e5b50: stur            x2, [fp, #-0x18]
    // 0x14e5b54: LoadField: r1 = r0->field_f
    //     0x14e5b54: ldur            w1, [x0, #0xf]
    // 0x14e5b58: DecompressPointer r1
    //     0x14e5b58: add             x1, x1, HEAP, lsl #32
    // 0x14e5b5c: r0 = controller()
    //     0x14e5b5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5b60: LoadField: r1 = r0->field_4f
    //     0x14e5b60: ldur            w1, [x0, #0x4f]
    // 0x14e5b64: DecompressPointer r1
    //     0x14e5b64: add             x1, x1, HEAP, lsl #32
    // 0x14e5b68: r0 = value()
    //     0x14e5b68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5b6c: LoadField: r1 = r0->field_b
    //     0x14e5b6c: ldur            w1, [x0, #0xb]
    // 0x14e5b70: DecompressPointer r1
    //     0x14e5b70: add             x1, x1, HEAP, lsl #32
    // 0x14e5b74: cmp             w1, NULL
    // 0x14e5b78: b.ne            #0x14e5b88
    // 0x14e5b7c: ldur            x2, [fp, #-0x10]
    // 0x14e5b80: r0 = Null
    //     0x14e5b80: mov             x0, NULL
    // 0x14e5b84: b               #0x14e5bd8
    // 0x14e5b88: ldur            x2, [fp, #-0x10]
    // 0x14e5b8c: LoadField: r3 = r1->field_f
    //     0x14e5b8c: ldur            w3, [x1, #0xf]
    // 0x14e5b90: DecompressPointer r3
    //     0x14e5b90: add             x3, x3, HEAP, lsl #32
    // 0x14e5b94: LoadField: r0 = r2->field_13
    //     0x14e5b94: ldur            w0, [x2, #0x13]
    // 0x14e5b98: DecompressPointer r0
    //     0x14e5b98: add             x0, x0, HEAP, lsl #32
    // 0x14e5b9c: LoadField: r1 = r3->field_b
    //     0x14e5b9c: ldur            w1, [x3, #0xb]
    // 0x14e5ba0: r4 = LoadInt32Instr(r0)
    //     0x14e5ba0: sbfx            x4, x0, #1, #0x1f
    //     0x14e5ba4: tbz             w0, #0, #0x14e5bac
    //     0x14e5ba8: ldur            x4, [x0, #7]
    // 0x14e5bac: r0 = LoadInt32Instr(r1)
    //     0x14e5bac: sbfx            x0, x1, #1, #0x1f
    // 0x14e5bb0: mov             x1, x4
    // 0x14e5bb4: cmp             x1, x0
    // 0x14e5bb8: b.hs            #0x14e5d08
    // 0x14e5bbc: LoadField: r0 = r3->field_f
    //     0x14e5bbc: ldur            w0, [x3, #0xf]
    // 0x14e5bc0: DecompressPointer r0
    //     0x14e5bc0: add             x0, x0, HEAP, lsl #32
    // 0x14e5bc4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14e5bc4: add             x16, x0, x4, lsl #2
    //     0x14e5bc8: ldur            w1, [x16, #0xf]
    // 0x14e5bcc: DecompressPointer r1
    //     0x14e5bcc: add             x1, x1, HEAP, lsl #32
    // 0x14e5bd0: LoadField: r0 = r1->field_b
    //     0x14e5bd0: ldur            w0, [x1, #0xb]
    // 0x14e5bd4: DecompressPointer r0
    //     0x14e5bd4: add             x0, x0, HEAP, lsl #32
    // 0x14e5bd8: cmp             w0, NULL
    // 0x14e5bdc: b.ne            #0x14e5be8
    // 0x14e5be0: r5 = ""
    //     0x14e5be0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14e5be4: b               #0x14e5bec
    // 0x14e5be8: mov             x5, x0
    // 0x14e5bec: ldur            x0, [fp, #-8]
    // 0x14e5bf0: ldur            x4, [fp, #-0x20]
    // 0x14e5bf4: ldur            x3, [fp, #-0x18]
    // 0x14e5bf8: stur            x5, [fp, #-0x30]
    // 0x14e5bfc: LoadField: r1 = r0->field_f
    //     0x14e5bfc: ldur            w1, [x0, #0xf]
    // 0x14e5c00: DecompressPointer r1
    //     0x14e5c00: add             x1, x1, HEAP, lsl #32
    // 0x14e5c04: r0 = controller()
    //     0x14e5c04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5c08: LoadField: r1 = r0->field_73
    //     0x14e5c08: ldur            w1, [x0, #0x73]
    // 0x14e5c0c: DecompressPointer r1
    //     0x14e5c0c: add             x1, x1, HEAP, lsl #32
    // 0x14e5c10: r0 = value()
    //     0x14e5c10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5c14: r1 = <String>
    //     0x14e5c14: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14e5c18: stur            x0, [fp, #-8]
    // 0x14e5c1c: r0 = RadioListTile()
    //     0x14e5c1c: bl              #0x997b1c  ; AllocateRadioListTileStub -> RadioListTile<X0> (size=0xa0)
    // 0x14e5c20: mov             x3, x0
    // 0x14e5c24: ldur            x0, [fp, #-0x30]
    // 0x14e5c28: stur            x3, [fp, #-0x40]
    // 0x14e5c2c: StoreField: r3->field_f = r0
    //     0x14e5c2c: stur            w0, [x3, #0xf]
    // 0x14e5c30: ldur            x0, [fp, #-8]
    // 0x14e5c34: StoreField: r3->field_13 = r0
    //     0x14e5c34: stur            w0, [x3, #0x13]
    // 0x14e5c38: ldur            x2, [fp, #-0x10]
    // 0x14e5c3c: r1 = Function '<anonymous closure>':.
    //     0x14e5c3c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40778] AnonymousClosure: (0x13cf540), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x1505aa4)
    //     0x14e5c40: ldr             x1, [x1, #0x778]
    // 0x14e5c44: r0 = AllocateClosure()
    //     0x14e5c44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14e5c48: mov             x1, x0
    // 0x14e5c4c: ldur            x0, [fp, #-0x40]
    // 0x14e5c50: ArrayStore: r0[0] = r1  ; List_4
    //     0x14e5c50: stur            w1, [x0, #0x17]
    // 0x14e5c54: r1 = false
    //     0x14e5c54: add             x1, NULL, #0x30  ; false
    // 0x14e5c58: StoreField: r0->field_1f = r1
    //     0x14e5c58: stur            w1, [x0, #0x1f]
    // 0x14e5c5c: ldur            x2, [fp, #-0x18]
    // 0x14e5c60: StoreField: r0->field_23 = r2
    //     0x14e5c60: stur            w2, [x0, #0x23]
    // 0x14e5c64: ldur            x2, [fp, #-0x20]
    // 0x14e5c68: StoreField: r0->field_3b = r2
    //     0x14e5c68: stur            w2, [x0, #0x3b]
    // 0x14e5c6c: r2 = true
    //     0x14e5c6c: add             x2, NULL, #0x20  ; true
    // 0x14e5c70: StoreField: r0->field_4b = r2
    //     0x14e5c70: stur            w2, [x0, #0x4b]
    // 0x14e5c74: StoreField: r0->field_4f = r1
    //     0x14e5c74: stur            w1, [x0, #0x4f]
    // 0x14e5c78: StoreField: r0->field_57 = r1
    //     0x14e5c78: stur            w1, [x0, #0x57]
    // 0x14e5c7c: r2 = Instance_EdgeInsets
    //     0x14e5c7c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14e5c80: StoreField: r0->field_5b = r2
    //     0x14e5c80: stur            w2, [x0, #0x5b]
    // 0x14e5c84: d0 = 1.000000
    //     0x14e5c84: fmov            d0, #1.00000000
    // 0x14e5c88: StoreField: r0->field_8b = d0
    //     0x14e5c88: stur            d0, [x0, #0x8b]
    // 0x14e5c8c: StoreField: r0->field_83 = r1
    //     0x14e5c8c: stur            w1, [x0, #0x83]
    // 0x14e5c90: r2 = Instance__RadioType
    //     0x14e5c90: add             x2, PP, #0x38, lsl #12  ; [pp+0x38050] Obj!_RadioType@d74141
    //     0x14e5c94: ldr             x2, [x2, #0x50]
    // 0x14e5c98: StoreField: r0->field_7b = r2
    //     0x14e5c98: stur            w2, [x0, #0x7b]
    // 0x14e5c9c: StoreField: r0->field_87 = r1
    //     0x14e5c9c: stur            w1, [x0, #0x87]
    // 0x14e5ca0: r0 = Padding()
    //     0x14e5ca0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14e5ca4: mov             x1, x0
    // 0x14e5ca8: r0 = Instance_EdgeInsets
    //     0x14e5ca8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x14e5cac: ldr             x0, [x0, #0x668]
    // 0x14e5cb0: stur            x1, [fp, #-8]
    // 0x14e5cb4: StoreField: r1->field_f = r0
    //     0x14e5cb4: stur            w0, [x1, #0xf]
    // 0x14e5cb8: ldur            x0, [fp, #-0x40]
    // 0x14e5cbc: StoreField: r1->field_b = r0
    //     0x14e5cbc: stur            w0, [x1, #0xb]
    // 0x14e5cc0: r0 = Container()
    //     0x14e5cc0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e5cc4: stur            x0, [fp, #-0x10]
    // 0x14e5cc8: ldur            x16, [fp, #-0x28]
    // 0x14e5ccc: ldur            lr, [fp, #-8]
    // 0x14e5cd0: stp             lr, x16, [SP]
    // 0x14e5cd4: mov             x1, x0
    // 0x14e5cd8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14e5cd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14e5cdc: ldr             x4, [x4, #0x88]
    // 0x14e5ce0: r0 = Container()
    //     0x14e5ce0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e5ce4: ldur            x0, [fp, #-0x10]
    // 0x14e5ce8: LeaveFrame
    //     0x14e5ce8: mov             SP, fp
    //     0x14e5cec: ldp             fp, lr, [SP], #0x10
    // 0x14e5cf0: ret
    //     0x14e5cf0: ret             
    // 0x14e5cf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14e5cf4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14e5cf8: b               #0x14e52e0
    // 0x14e5cfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e5cfc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14e5d00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e5d00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14e5d04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e5d04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14e5d08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e5d08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14e5d0c, size: 0x264
    // 0x14e5d0c: EnterFrame
    //     0x14e5d0c: stp             fp, lr, [SP, #-0x10]!
    //     0x14e5d10: mov             fp, SP
    // 0x14e5d14: AllocStack(0x48)
    //     0x14e5d14: sub             SP, SP, #0x48
    // 0x14e5d18: SetupParameters()
    //     0x14e5d18: ldr             x0, [fp, #0x20]
    //     0x14e5d1c: ldur            w1, [x0, #0x17]
    //     0x14e5d20: add             x1, x1, HEAP, lsl #32
    //     0x14e5d24: stur            x1, [fp, #-8]
    // 0x14e5d28: CheckStackOverflow
    //     0x14e5d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14e5d2c: cmp             SP, x16
    //     0x14e5d30: b.ls            #0x14e5f64
    // 0x14e5d34: r0 = Radius()
    //     0x14e5d34: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14e5d38: d0 = 100.000000
    //     0x14e5d38: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0x14e5d3c: stur            x0, [fp, #-0x10]
    // 0x14e5d40: StoreField: r0->field_7 = d0
    //     0x14e5d40: stur            d0, [x0, #7]
    // 0x14e5d44: StoreField: r0->field_f = d0
    //     0x14e5d44: stur            d0, [x0, #0xf]
    // 0x14e5d48: r0 = BorderRadius()
    //     0x14e5d48: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14e5d4c: mov             x2, x0
    // 0x14e5d50: ldur            x0, [fp, #-0x10]
    // 0x14e5d54: stur            x2, [fp, #-0x18]
    // 0x14e5d58: StoreField: r2->field_7 = r0
    //     0x14e5d58: stur            w0, [x2, #7]
    // 0x14e5d5c: StoreField: r2->field_b = r0
    //     0x14e5d5c: stur            w0, [x2, #0xb]
    // 0x14e5d60: StoreField: r2->field_f = r0
    //     0x14e5d60: stur            w0, [x2, #0xf]
    // 0x14e5d64: StoreField: r2->field_13 = r0
    //     0x14e5d64: stur            w0, [x2, #0x13]
    // 0x14e5d68: r1 = Instance_Color
    //     0x14e5d68: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e5d6c: d0 = 0.030000
    //     0x14e5d6c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14e5d70: ldr             d0, [x17, #0x238]
    // 0x14e5d74: r0 = withOpacity()
    //     0x14e5d74: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e5d78: stur            x0, [fp, #-0x10]
    // 0x14e5d7c: r0 = BoxDecoration()
    //     0x14e5d7c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14e5d80: mov             x1, x0
    // 0x14e5d84: ldur            x0, [fp, #-0x10]
    // 0x14e5d88: stur            x1, [fp, #-0x20]
    // 0x14e5d8c: StoreField: r1->field_7 = r0
    //     0x14e5d8c: stur            w0, [x1, #7]
    // 0x14e5d90: ldur            x0, [fp, #-0x18]
    // 0x14e5d94: StoreField: r1->field_13 = r0
    //     0x14e5d94: stur            w0, [x1, #0x13]
    // 0x14e5d98: r0 = Instance_BoxShape
    //     0x14e5d98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14e5d9c: ldr             x0, [x0, #0x80]
    // 0x14e5da0: StoreField: r1->field_23 = r0
    //     0x14e5da0: stur            w0, [x1, #0x23]
    // 0x14e5da4: r0 = SvgPicture()
    //     0x14e5da4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14e5da8: stur            x0, [fp, #-0x10]
    // 0x14e5dac: r16 = Instance_BoxFit
    //     0x14e5dac: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x14e5db0: ldr             x16, [x16, #0xb18]
    // 0x14e5db4: str             x16, [SP]
    // 0x14e5db8: mov             x1, x0
    // 0x14e5dbc: r2 = "assets/images/exchange_check_icon.svg"
    //     0x14e5dbc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38058] "assets/images/exchange_check_icon.svg"
    //     0x14e5dc0: ldr             x2, [x2, #0x58]
    // 0x14e5dc4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0x14e5dc4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0x14e5dc8: ldr             x4, [x4, #0xb0]
    // 0x14e5dcc: r0 = SvgPicture.asset()
    //     0x14e5dcc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14e5dd0: r0 = Container()
    //     0x14e5dd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14e5dd4: stur            x0, [fp, #-0x18]
    // 0x14e5dd8: r16 = 40.000000
    //     0x14e5dd8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0x14e5ddc: ldr             x16, [x16, #8]
    // 0x14e5de0: r30 = 40.000000
    //     0x14e5de0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0x14e5de4: ldr             lr, [lr, #8]
    // 0x14e5de8: stp             lr, x16, [SP, #0x18]
    // 0x14e5dec: r16 = Instance_EdgeInsets
    //     0x14e5dec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14e5df0: ldr             x16, [x16, #0x980]
    // 0x14e5df4: ldur            lr, [fp, #-0x20]
    // 0x14e5df8: stp             lr, x16, [SP, #8]
    // 0x14e5dfc: ldur            x16, [fp, #-0x10]
    // 0x14e5e00: str             x16, [SP]
    // 0x14e5e04: mov             x1, x0
    // 0x14e5e08: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x2, padding, 0x3, width, 0x1, null]
    //     0x14e5e08: add             x4, PP, #0x38, lsl #12  ; [pp+0x38060] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0x14e5e0c: ldr             x4, [x4, #0x60]
    // 0x14e5e10: r0 = Container()
    //     0x14e5e10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14e5e14: ldur            x0, [fp, #-8]
    // 0x14e5e18: LoadField: r1 = r0->field_f
    //     0x14e5e18: ldur            w1, [x0, #0xf]
    // 0x14e5e1c: DecompressPointer r1
    //     0x14e5e1c: add             x1, x1, HEAP, lsl #32
    // 0x14e5e20: r0 = controller()
    //     0x14e5e20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14e5e24: LoadField: r1 = r0->field_4f
    //     0x14e5e24: ldur            w1, [x0, #0x4f]
    // 0x14e5e28: DecompressPointer r1
    //     0x14e5e28: add             x1, x1, HEAP, lsl #32
    // 0x14e5e2c: r0 = value()
    //     0x14e5e2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14e5e30: LoadField: r1 = r0->field_b
    //     0x14e5e30: ldur            w1, [x0, #0xb]
    // 0x14e5e34: DecompressPointer r1
    //     0x14e5e34: add             x1, x1, HEAP, lsl #32
    // 0x14e5e38: cmp             w1, NULL
    // 0x14e5e3c: b.ne            #0x14e5e48
    // 0x14e5e40: r1 = Null
    //     0x14e5e40: mov             x1, NULL
    // 0x14e5e44: b               #0x14e5e88
    // 0x14e5e48: ldr             x0, [fp, #0x10]
    // 0x14e5e4c: LoadField: r2 = r1->field_7
    //     0x14e5e4c: ldur            w2, [x1, #7]
    // 0x14e5e50: DecompressPointer r2
    //     0x14e5e50: add             x2, x2, HEAP, lsl #32
    // 0x14e5e54: LoadField: r1 = r2->field_b
    //     0x14e5e54: ldur            w1, [x2, #0xb]
    // 0x14e5e58: r3 = LoadInt32Instr(r0)
    //     0x14e5e58: sbfx            x3, x0, #1, #0x1f
    //     0x14e5e5c: tbz             w0, #0, #0x14e5e64
    //     0x14e5e60: ldur            x3, [x0, #7]
    // 0x14e5e64: r0 = LoadInt32Instr(r1)
    //     0x14e5e64: sbfx            x0, x1, #1, #0x1f
    // 0x14e5e68: mov             x1, x3
    // 0x14e5e6c: cmp             x1, x0
    // 0x14e5e70: b.hs            #0x14e5f6c
    // 0x14e5e74: LoadField: r0 = r2->field_f
    //     0x14e5e74: ldur            w0, [x2, #0xf]
    // 0x14e5e78: DecompressPointer r0
    //     0x14e5e78: add             x0, x0, HEAP, lsl #32
    // 0x14e5e7c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14e5e7c: add             x16, x0, x3, lsl #2
    //     0x14e5e80: ldur            w1, [x16, #0xf]
    // 0x14e5e84: DecompressPointer r1
    //     0x14e5e84: add             x1, x1, HEAP, lsl #32
    // 0x14e5e88: ldur            x0, [fp, #-0x18]
    // 0x14e5e8c: str             x1, [SP]
    // 0x14e5e90: r0 = _interpolateSingle()
    //     0x14e5e90: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14e5e94: ldr             x1, [fp, #0x18]
    // 0x14e5e98: stur            x0, [fp, #-8]
    // 0x14e5e9c: r0 = of()
    //     0x14e5e9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14e5ea0: LoadField: r1 = r0->field_87
    //     0x14e5ea0: ldur            w1, [x0, #0x87]
    // 0x14e5ea4: DecompressPointer r1
    //     0x14e5ea4: add             x1, x1, HEAP, lsl #32
    // 0x14e5ea8: LoadField: r0 = r1->field_2b
    //     0x14e5ea8: ldur            w0, [x1, #0x2b]
    // 0x14e5eac: DecompressPointer r0
    //     0x14e5eac: add             x0, x0, HEAP, lsl #32
    // 0x14e5eb0: stur            x0, [fp, #-0x10]
    // 0x14e5eb4: r1 = Instance_Color
    //     0x14e5eb4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14e5eb8: d0 = 0.700000
    //     0x14e5eb8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14e5ebc: ldr             d0, [x17, #0xf48]
    // 0x14e5ec0: r0 = withOpacity()
    //     0x14e5ec0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14e5ec4: r16 = 12.000000
    //     0x14e5ec4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14e5ec8: ldr             x16, [x16, #0x9e8]
    // 0x14e5ecc: stp             x0, x16, [SP]
    // 0x14e5ed0: ldur            x1, [fp, #-0x10]
    // 0x14e5ed4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14e5ed4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14e5ed8: ldr             x4, [x4, #0xaa0]
    // 0x14e5edc: r0 = copyWith()
    //     0x14e5edc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14e5ee0: stur            x0, [fp, #-0x10]
    // 0x14e5ee4: r0 = HtmlWidget()
    //     0x14e5ee4: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0x14e5ee8: mov             x1, x0
    // 0x14e5eec: ldur            x0, [fp, #-8]
    // 0x14e5ef0: stur            x1, [fp, #-0x20]
    // 0x14e5ef4: StoreField: r1->field_1f = r0
    //     0x14e5ef4: stur            w0, [x1, #0x1f]
    // 0x14e5ef8: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0x14e5ef8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0x14e5efc: ldr             x0, [x0, #0x1e0]
    // 0x14e5f00: StoreField: r1->field_23 = r0
    //     0x14e5f00: stur            w0, [x1, #0x23]
    // 0x14e5f04: r0 = Instance_ColumnMode
    //     0x14e5f04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0x14e5f08: ldr             x0, [x0, #0x1e8]
    // 0x14e5f0c: StoreField: r1->field_3b = r0
    //     0x14e5f0c: stur            w0, [x1, #0x3b]
    // 0x14e5f10: ldur            x0, [fp, #-0x10]
    // 0x14e5f14: StoreField: r1->field_3f = r0
    //     0x14e5f14: stur            w0, [x1, #0x3f]
    // 0x14e5f18: r0 = ListTile()
    //     0x14e5f18: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0x14e5f1c: ldur            x1, [fp, #-0x18]
    // 0x14e5f20: StoreField: r0->field_b = r1
    //     0x14e5f20: stur            w1, [x0, #0xb]
    // 0x14e5f24: ldur            x1, [fp, #-0x20]
    // 0x14e5f28: StoreField: r0->field_f = r1
    //     0x14e5f28: stur            w1, [x0, #0xf]
    // 0x14e5f2c: r1 = Instance_EdgeInsets
    //     0x14e5f2c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14e5f30: StoreField: r0->field_47 = r1
    //     0x14e5f30: stur            w1, [x0, #0x47]
    // 0x14e5f34: r1 = true
    //     0x14e5f34: add             x1, NULL, #0x20  ; true
    // 0x14e5f38: StoreField: r0->field_4b = r1
    //     0x14e5f38: stur            w1, [x0, #0x4b]
    // 0x14e5f3c: r2 = false
    //     0x14e5f3c: add             x2, NULL, #0x30  ; false
    // 0x14e5f40: StoreField: r0->field_5f = r2
    //     0x14e5f40: stur            w2, [x0, #0x5f]
    // 0x14e5f44: StoreField: r0->field_73 = r2
    //     0x14e5f44: stur            w2, [x0, #0x73]
    // 0x14e5f48: r2 = 0.000000
    //     0x14e5f48: ldr             x2, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14e5f4c: StoreField: r0->field_87 = r2
    //     0x14e5f4c: stur            w2, [x0, #0x87]
    // 0x14e5f50: StoreField: r0->field_8b = r2
    //     0x14e5f50: stur            w2, [x0, #0x8b]
    // 0x14e5f54: StoreField: r0->field_97 = r1
    //     0x14e5f54: stur            w1, [x0, #0x97]
    // 0x14e5f58: LeaveFrame
    //     0x14e5f58: mov             SP, fp
    //     0x14e5f5c: ldp             fp, lr, [SP], #0x10
    // 0x14e5f60: ret
    //     0x14e5f60: ret             
    // 0x14e5f64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14e5f64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14e5f68: b               #0x14e5d34
    // 0x14e5f6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14e5f6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e1ea0, size: 0x18c
    // 0x15e1ea0: EnterFrame
    //     0x15e1ea0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e1ea4: mov             fp, SP
    // 0x15e1ea8: AllocStack(0x28)
    //     0x15e1ea8: sub             SP, SP, #0x28
    // 0x15e1eac: SetupParameters(ExchangeReturnIntermediateScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e1eac: mov             x0, x1
    //     0x15e1eb0: stur            x1, [fp, #-8]
    //     0x15e1eb4: mov             x1, x2
    //     0x15e1eb8: stur            x2, [fp, #-0x10]
    // 0x15e1ebc: CheckStackOverflow
    //     0x15e1ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1ec0: cmp             SP, x16
    //     0x15e1ec4: b.ls            #0x15e2024
    // 0x15e1ec8: r1 = 2
    //     0x15e1ec8: movz            x1, #0x2
    // 0x15e1ecc: r0 = AllocateContext()
    //     0x15e1ecc: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e1ed0: mov             x1, x0
    // 0x15e1ed4: ldur            x0, [fp, #-8]
    // 0x15e1ed8: stur            x1, [fp, #-0x18]
    // 0x15e1edc: StoreField: r1->field_f = r0
    //     0x15e1edc: stur            w0, [x1, #0xf]
    // 0x15e1ee0: ldur            x0, [fp, #-0x10]
    // 0x15e1ee4: StoreField: r1->field_13 = r0
    //     0x15e1ee4: stur            w0, [x1, #0x13]
    // 0x15e1ee8: r0 = Obx()
    //     0x15e1ee8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e1eec: ldur            x2, [fp, #-0x18]
    // 0x15e1ef0: r1 = Function '<anonymous closure>':.
    //     0x15e1ef0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40780] AnonymousClosure: (0x15d0660), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::appBar (0x15ea09c)
    //     0x15e1ef4: ldr             x1, [x1, #0x780]
    // 0x15e1ef8: stur            x0, [fp, #-8]
    // 0x15e1efc: r0 = AllocateClosure()
    //     0x15e1efc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1f00: mov             x1, x0
    // 0x15e1f04: ldur            x0, [fp, #-8]
    // 0x15e1f08: StoreField: r0->field_b = r1
    //     0x15e1f08: stur            w1, [x0, #0xb]
    // 0x15e1f0c: ldur            x1, [fp, #-0x10]
    // 0x15e1f10: r0 = of()
    //     0x15e1f10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e1f14: LoadField: r1 = r0->field_5b
    //     0x15e1f14: ldur            w1, [x0, #0x5b]
    // 0x15e1f18: DecompressPointer r1
    //     0x15e1f18: add             x1, x1, HEAP, lsl #32
    // 0x15e1f1c: stur            x1, [fp, #-0x10]
    // 0x15e1f20: r0 = ColorFilter()
    //     0x15e1f20: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e1f24: mov             x1, x0
    // 0x15e1f28: ldur            x0, [fp, #-0x10]
    // 0x15e1f2c: stur            x1, [fp, #-0x20]
    // 0x15e1f30: StoreField: r1->field_7 = r0
    //     0x15e1f30: stur            w0, [x1, #7]
    // 0x15e1f34: r0 = Instance_BlendMode
    //     0x15e1f34: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e1f38: ldr             x0, [x0, #0xb30]
    // 0x15e1f3c: StoreField: r1->field_b = r0
    //     0x15e1f3c: stur            w0, [x1, #0xb]
    // 0x15e1f40: r0 = 1
    //     0x15e1f40: movz            x0, #0x1
    // 0x15e1f44: StoreField: r1->field_13 = r0
    //     0x15e1f44: stur            x0, [x1, #0x13]
    // 0x15e1f48: r0 = SvgPicture()
    //     0x15e1f48: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1f4c: stur            x0, [fp, #-0x10]
    // 0x15e1f50: ldur            x16, [fp, #-0x20]
    // 0x15e1f54: str             x16, [SP]
    // 0x15e1f58: mov             x1, x0
    // 0x15e1f5c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e1f5c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e1f60: ldr             x2, [x2, #0xa40]
    // 0x15e1f64: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e1f64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e1f68: ldr             x4, [x4, #0xa38]
    // 0x15e1f6c: r0 = SvgPicture.asset()
    //     0x15e1f6c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e1f70: r0 = Align()
    //     0x15e1f70: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e1f74: mov             x1, x0
    // 0x15e1f78: r0 = Instance_Alignment
    //     0x15e1f78: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1f7c: ldr             x0, [x0, #0xb10]
    // 0x15e1f80: stur            x1, [fp, #-0x20]
    // 0x15e1f84: StoreField: r1->field_f = r0
    //     0x15e1f84: stur            w0, [x1, #0xf]
    // 0x15e1f88: r0 = 1.000000
    //     0x15e1f88: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e1f8c: StoreField: r1->field_13 = r0
    //     0x15e1f8c: stur            w0, [x1, #0x13]
    // 0x15e1f90: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e1f90: stur            w0, [x1, #0x17]
    // 0x15e1f94: ldur            x0, [fp, #-0x10]
    // 0x15e1f98: StoreField: r1->field_b = r0
    //     0x15e1f98: stur            w0, [x1, #0xb]
    // 0x15e1f9c: r0 = InkWell()
    //     0x15e1f9c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e1fa0: mov             x3, x0
    // 0x15e1fa4: ldur            x0, [fp, #-0x20]
    // 0x15e1fa8: stur            x3, [fp, #-0x10]
    // 0x15e1fac: StoreField: r3->field_b = r0
    //     0x15e1fac: stur            w0, [x3, #0xb]
    // 0x15e1fb0: ldur            x2, [fp, #-0x18]
    // 0x15e1fb4: r1 = Function '<anonymous closure>':.
    //     0x15e1fb4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40788] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e1fb8: ldr             x1, [x1, #0x788]
    // 0x15e1fbc: r0 = AllocateClosure()
    //     0x15e1fbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1fc0: ldur            x2, [fp, #-0x10]
    // 0x15e1fc4: StoreField: r2->field_f = r0
    //     0x15e1fc4: stur            w0, [x2, #0xf]
    // 0x15e1fc8: r0 = true
    //     0x15e1fc8: add             x0, NULL, #0x20  ; true
    // 0x15e1fcc: StoreField: r2->field_43 = r0
    //     0x15e1fcc: stur            w0, [x2, #0x43]
    // 0x15e1fd0: r1 = Instance_BoxShape
    //     0x15e1fd0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e1fd4: ldr             x1, [x1, #0x80]
    // 0x15e1fd8: StoreField: r2->field_47 = r1
    //     0x15e1fd8: stur            w1, [x2, #0x47]
    // 0x15e1fdc: StoreField: r2->field_6f = r0
    //     0x15e1fdc: stur            w0, [x2, #0x6f]
    // 0x15e1fe0: r1 = false
    //     0x15e1fe0: add             x1, NULL, #0x30  ; false
    // 0x15e1fe4: StoreField: r2->field_73 = r1
    //     0x15e1fe4: stur            w1, [x2, #0x73]
    // 0x15e1fe8: StoreField: r2->field_83 = r0
    //     0x15e1fe8: stur            w0, [x2, #0x83]
    // 0x15e1fec: StoreField: r2->field_7b = r1
    //     0x15e1fec: stur            w1, [x2, #0x7b]
    // 0x15e1ff0: r0 = AppBar()
    //     0x15e1ff0: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e1ff4: stur            x0, [fp, #-0x18]
    // 0x15e1ff8: ldur            x16, [fp, #-8]
    // 0x15e1ffc: str             x16, [SP]
    // 0x15e2000: mov             x1, x0
    // 0x15e2004: ldur            x2, [fp, #-0x10]
    // 0x15e2008: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e2008: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e200c: ldr             x4, [x4, #0xf00]
    // 0x15e2010: r0 = AppBar()
    //     0x15e2010: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e2014: ldur            x0, [fp, #-0x18]
    // 0x15e2018: LeaveFrame
    //     0x15e2018: mov             SP, fp
    //     0x15e201c: ldp             fp, lr, [SP], #0x10
    // 0x15e2020: ret
    //     0x15e2020: ret             
    // 0x15e2024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e2024: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e2028: b               #0x15e1ec8
  }
}
