// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/reply_to_seller_item_view.dart

// class id: 1049443, size: 0x8
class :: {
}

// class id: 3307, size: 0x14, field offset: 0x14
class _ReplyToSellerItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb8e1c8, size: 0x7b0
    // 0xb8e1c8: EnterFrame
    //     0xb8e1c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e1cc: mov             fp, SP
    // 0xb8e1d0: AllocStack(0x80)
    //     0xb8e1d0: sub             SP, SP, #0x80
    // 0xb8e1d4: SetupParameters(_ReplyToSellerItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb8e1d4: mov             x0, x1
    //     0xb8e1d8: stur            x1, [fp, #-8]
    //     0xb8e1dc: mov             x1, x2
    //     0xb8e1e0: stur            x2, [fp, #-0x10]
    // 0xb8e1e4: CheckStackOverflow
    //     0xb8e1e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e1e8: cmp             SP, x16
    //     0xb8e1ec: b.ls            #0xb8e938
    // 0xb8e1f0: r1 = 1
    //     0xb8e1f0: movz            x1, #0x1
    // 0xb8e1f4: r0 = AllocateContext()
    //     0xb8e1f4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8e1f8: mov             x1, x0
    // 0xb8e1fc: ldur            x0, [fp, #-8]
    // 0xb8e200: stur            x1, [fp, #-0x28]
    // 0xb8e204: StoreField: r1->field_f = r0
    //     0xb8e204: stur            w0, [x1, #0xf]
    // 0xb8e208: LoadField: r2 = r0->field_b
    //     0xb8e208: ldur            w2, [x0, #0xb]
    // 0xb8e20c: DecompressPointer r2
    //     0xb8e20c: add             x2, x2, HEAP, lsl #32
    // 0xb8e210: stur            x2, [fp, #-0x20]
    // 0xb8e214: cmp             w2, NULL
    // 0xb8e218: b.eq            #0xb8e940
    // 0xb8e21c: LoadField: r3 = r2->field_f
    //     0xb8e21c: ldur            w3, [x2, #0xf]
    // 0xb8e220: DecompressPointer r3
    //     0xb8e220: add             x3, x3, HEAP, lsl #32
    // 0xb8e224: cmp             w3, NULL
    // 0xb8e228: b.ne            #0xb8e234
    // 0xb8e22c: r3 = Null
    //     0xb8e22c: mov             x3, NULL
    // 0xb8e230: b               #0xb8e240
    // 0xb8e234: LoadField: r4 = r3->field_6f
    //     0xb8e234: ldur            w4, [x3, #0x6f]
    // 0xb8e238: DecompressPointer r4
    //     0xb8e238: add             x4, x4, HEAP, lsl #32
    // 0xb8e23c: mov             x3, x4
    // 0xb8e240: cmp             w3, NULL
    // 0xb8e244: b.eq            #0xb8e294
    // 0xb8e248: tbnz            w3, #4, #0xb8e294
    // 0xb8e24c: LoadField: r3 = r2->field_b
    //     0xb8e24c: ldur            w3, [x2, #0xb]
    // 0xb8e250: DecompressPointer r3
    //     0xb8e250: add             x3, x3, HEAP, lsl #32
    // 0xb8e254: LoadField: r4 = r3->field_f
    //     0xb8e254: ldur            w4, [x3, #0xf]
    // 0xb8e258: DecompressPointer r4
    //     0xb8e258: add             x4, x4, HEAP, lsl #32
    // 0xb8e25c: cmp             w4, NULL
    // 0xb8e260: b.ne            #0xb8e26c
    // 0xb8e264: r3 = Null
    //     0xb8e264: mov             x3, NULL
    // 0xb8e268: b               #0xb8e284
    // 0xb8e26c: LoadField: r3 = r4->field_7
    //     0xb8e26c: ldur            w3, [x4, #7]
    // 0xb8e270: cbnz            w3, #0xb8e27c
    // 0xb8e274: r4 = false
    //     0xb8e274: add             x4, NULL, #0x30  ; false
    // 0xb8e278: b               #0xb8e280
    // 0xb8e27c: r4 = true
    //     0xb8e27c: add             x4, NULL, #0x20  ; true
    // 0xb8e280: mov             x3, x4
    // 0xb8e284: cmp             w3, NULL
    // 0xb8e288: b.ne            #0xb8e298
    // 0xb8e28c: r3 = false
    //     0xb8e28c: add             x3, NULL, #0x30  ; false
    // 0xb8e290: b               #0xb8e298
    // 0xb8e294: r3 = false
    //     0xb8e294: add             x3, NULL, #0x30  ; false
    // 0xb8e298: stur            x3, [fp, #-0x18]
    // 0xb8e29c: r0 = Radius()
    //     0xb8e29c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8e2a0: d0 = 20.000000
    //     0xb8e2a0: fmov            d0, #20.00000000
    // 0xb8e2a4: stur            x0, [fp, #-0x30]
    // 0xb8e2a8: StoreField: r0->field_7 = d0
    //     0xb8e2a8: stur            d0, [x0, #7]
    // 0xb8e2ac: StoreField: r0->field_f = d0
    //     0xb8e2ac: stur            d0, [x0, #0xf]
    // 0xb8e2b0: r0 = BorderRadius()
    //     0xb8e2b0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8e2b4: mov             x1, x0
    // 0xb8e2b8: ldur            x0, [fp, #-0x30]
    // 0xb8e2bc: stur            x1, [fp, #-0x38]
    // 0xb8e2c0: StoreField: r1->field_7 = r0
    //     0xb8e2c0: stur            w0, [x1, #7]
    // 0xb8e2c4: StoreField: r1->field_b = r0
    //     0xb8e2c4: stur            w0, [x1, #0xb]
    // 0xb8e2c8: StoreField: r1->field_f = r0
    //     0xb8e2c8: stur            w0, [x1, #0xf]
    // 0xb8e2cc: StoreField: r1->field_13 = r0
    //     0xb8e2cc: stur            w0, [x1, #0x13]
    // 0xb8e2d0: r0 = BoxDecoration()
    //     0xb8e2d0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8e2d4: mov             x2, x0
    // 0xb8e2d8: r0 = Instance_DecorationImage
    //     0xb8e2d8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52ea0] Obj!DecorationImage@d5a141
    //     0xb8e2dc: ldr             x0, [x0, #0xea0]
    // 0xb8e2e0: stur            x2, [fp, #-0x30]
    // 0xb8e2e4: StoreField: r2->field_b = r0
    //     0xb8e2e4: stur            w0, [x2, #0xb]
    // 0xb8e2e8: ldur            x0, [fp, #-0x38]
    // 0xb8e2ec: StoreField: r2->field_13 = r0
    //     0xb8e2ec: stur            w0, [x2, #0x13]
    // 0xb8e2f0: r0 = Instance_BoxShape
    //     0xb8e2f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8e2f4: ldr             x0, [x0, #0x80]
    // 0xb8e2f8: StoreField: r2->field_23 = r0
    //     0xb8e2f8: stur            w0, [x2, #0x23]
    // 0xb8e2fc: ldur            x1, [fp, #-0x20]
    // 0xb8e300: LoadField: r3 = r1->field_b
    //     0xb8e300: ldur            w3, [x1, #0xb]
    // 0xb8e304: DecompressPointer r3
    //     0xb8e304: add             x3, x3, HEAP, lsl #32
    // 0xb8e308: LoadField: r1 = r3->field_f
    //     0xb8e308: ldur            w1, [x3, #0xf]
    // 0xb8e30c: DecompressPointer r1
    //     0xb8e30c: add             x1, x1, HEAP, lsl #32
    // 0xb8e310: cmp             w1, NULL
    // 0xb8e314: b.ne            #0xb8e320
    // 0xb8e318: r4 = ""
    //     0xb8e318: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8e31c: b               #0xb8e324
    // 0xb8e320: mov             x4, x1
    // 0xb8e324: ldur            x3, [fp, #-8]
    // 0xb8e328: ldur            x1, [fp, #-0x10]
    // 0xb8e32c: stur            x4, [fp, #-0x20]
    // 0xb8e330: r0 = of()
    //     0xb8e330: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8e334: LoadField: r1 = r0->field_87
    //     0xb8e334: ldur            w1, [x0, #0x87]
    // 0xb8e338: DecompressPointer r1
    //     0xb8e338: add             x1, x1, HEAP, lsl #32
    // 0xb8e33c: LoadField: r0 = r1->field_7
    //     0xb8e33c: ldur            w0, [x1, #7]
    // 0xb8e340: DecompressPointer r0
    //     0xb8e340: add             x0, x0, HEAP, lsl #32
    // 0xb8e344: r16 = 16.000000
    //     0xb8e344: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb8e348: ldr             x16, [x16, #0x188]
    // 0xb8e34c: r30 = Instance_Color
    //     0xb8e34c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8e350: stp             lr, x16, [SP]
    // 0xb8e354: mov             x1, x0
    // 0xb8e358: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8e358: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8e35c: ldr             x4, [x4, #0xaa0]
    // 0xb8e360: r0 = copyWith()
    //     0xb8e360: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8e364: stur            x0, [fp, #-0x38]
    // 0xb8e368: r0 = Text()
    //     0xb8e368: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8e36c: mov             x1, x0
    // 0xb8e370: ldur            x0, [fp, #-0x20]
    // 0xb8e374: stur            x1, [fp, #-0x40]
    // 0xb8e378: StoreField: r1->field_b = r0
    //     0xb8e378: stur            w0, [x1, #0xb]
    // 0xb8e37c: ldur            x0, [fp, #-0x38]
    // 0xb8e380: StoreField: r1->field_13 = r0
    //     0xb8e380: stur            w0, [x1, #0x13]
    // 0xb8e384: r0 = Padding()
    //     0xb8e384: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8e388: mov             x2, x0
    // 0xb8e38c: r0 = Instance_EdgeInsets
    //     0xb8e38c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb8e390: ldr             x0, [x0, #0x858]
    // 0xb8e394: stur            x2, [fp, #-0x38]
    // 0xb8e398: StoreField: r2->field_f = r0
    //     0xb8e398: stur            w0, [x2, #0xf]
    // 0xb8e39c: ldur            x0, [fp, #-0x40]
    // 0xb8e3a0: StoreField: r2->field_b = r0
    //     0xb8e3a0: stur            w0, [x2, #0xb]
    // 0xb8e3a4: ldur            x0, [fp, #-8]
    // 0xb8e3a8: LoadField: r1 = r0->field_b
    //     0xb8e3a8: ldur            w1, [x0, #0xb]
    // 0xb8e3ac: DecompressPointer r1
    //     0xb8e3ac: add             x1, x1, HEAP, lsl #32
    // 0xb8e3b0: cmp             w1, NULL
    // 0xb8e3b4: b.eq            #0xb8e944
    // 0xb8e3b8: LoadField: r3 = r1->field_b
    //     0xb8e3b8: ldur            w3, [x1, #0xb]
    // 0xb8e3bc: DecompressPointer r3
    //     0xb8e3bc: add             x3, x3, HEAP, lsl #32
    // 0xb8e3c0: LoadField: r1 = r3->field_73
    //     0xb8e3c0: ldur            w1, [x3, #0x73]
    // 0xb8e3c4: DecompressPointer r1
    //     0xb8e3c4: add             x1, x1, HEAP, lsl #32
    // 0xb8e3c8: cmp             w1, NULL
    // 0xb8e3cc: b.ne            #0xb8e3d8
    // 0xb8e3d0: r3 = ""
    //     0xb8e3d0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8e3d4: b               #0xb8e3dc
    // 0xb8e3d8: mov             x3, x1
    // 0xb8e3dc: ldur            x1, [fp, #-0x10]
    // 0xb8e3e0: stur            x3, [fp, #-0x20]
    // 0xb8e3e4: r0 = of()
    //     0xb8e3e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8e3e8: LoadField: r1 = r0->field_87
    //     0xb8e3e8: ldur            w1, [x0, #0x87]
    // 0xb8e3ec: DecompressPointer r1
    //     0xb8e3ec: add             x1, x1, HEAP, lsl #32
    // 0xb8e3f0: LoadField: r0 = r1->field_7
    //     0xb8e3f0: ldur            w0, [x1, #7]
    // 0xb8e3f4: DecompressPointer r0
    //     0xb8e3f4: add             x0, x0, HEAP, lsl #32
    // 0xb8e3f8: stur            x0, [fp, #-0x40]
    // 0xb8e3fc: r1 = Instance_Color
    //     0xb8e3fc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8e400: d0 = 0.300000
    //     0xb8e400: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb8e404: ldr             d0, [x17, #0x658]
    // 0xb8e408: r0 = withOpacity()
    //     0xb8e408: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8e40c: r16 = 12.000000
    //     0xb8e40c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8e410: ldr             x16, [x16, #0x9e8]
    // 0xb8e414: stp             x0, x16, [SP]
    // 0xb8e418: ldur            x1, [fp, #-0x40]
    // 0xb8e41c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8e41c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8e420: ldr             x4, [x4, #0xaa0]
    // 0xb8e424: r0 = copyWith()
    //     0xb8e424: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8e428: stur            x0, [fp, #-0x40]
    // 0xb8e42c: r0 = Text()
    //     0xb8e42c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8e430: mov             x1, x0
    // 0xb8e434: ldur            x0, [fp, #-0x20]
    // 0xb8e438: stur            x1, [fp, #-0x48]
    // 0xb8e43c: StoreField: r1->field_b = r0
    //     0xb8e43c: stur            w0, [x1, #0xb]
    // 0xb8e440: ldur            x0, [fp, #-0x40]
    // 0xb8e444: StoreField: r1->field_13 = r0
    //     0xb8e444: stur            w0, [x1, #0x13]
    // 0xb8e448: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8e448: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8e44c: ldr             x0, [x0, #0x1c80]
    //     0xb8e450: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8e454: cmp             w0, w16
    //     0xb8e458: b.ne            #0xb8e464
    //     0xb8e45c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb8e460: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb8e464: r0 = GetNavigation.size()
    //     0xb8e464: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb8e468: LoadField: d0 = r0->field_7
    //     0xb8e468: ldur            d0, [x0, #7]
    // 0xb8e46c: stur            d0, [fp, #-0x58]
    // 0xb8e470: r0 = Radius()
    //     0xb8e470: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8e474: d0 = 24.000000
    //     0xb8e474: fmov            d0, #24.00000000
    // 0xb8e478: stur            x0, [fp, #-0x20]
    // 0xb8e47c: StoreField: r0->field_7 = d0
    //     0xb8e47c: stur            d0, [x0, #7]
    // 0xb8e480: StoreField: r0->field_f = d0
    //     0xb8e480: stur            d0, [x0, #0xf]
    // 0xb8e484: r0 = BorderRadius()
    //     0xb8e484: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8e488: mov             x2, x0
    // 0xb8e48c: ldur            x0, [fp, #-0x20]
    // 0xb8e490: stur            x2, [fp, #-0x40]
    // 0xb8e494: StoreField: r2->field_7 = r0
    //     0xb8e494: stur            w0, [x2, #7]
    // 0xb8e498: StoreField: r2->field_b = r0
    //     0xb8e498: stur            w0, [x2, #0xb]
    // 0xb8e49c: StoreField: r2->field_f = r0
    //     0xb8e49c: stur            w0, [x2, #0xf]
    // 0xb8e4a0: StoreField: r2->field_13 = r0
    //     0xb8e4a0: stur            w0, [x2, #0x13]
    // 0xb8e4a4: ldur            x1, [fp, #-0x10]
    // 0xb8e4a8: r0 = of()
    //     0xb8e4a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8e4ac: LoadField: r2 = r0->field_5b
    //     0xb8e4ac: ldur            w2, [x0, #0x5b]
    // 0xb8e4b0: DecompressPointer r2
    //     0xb8e4b0: add             x2, x2, HEAP, lsl #32
    // 0xb8e4b4: r16 = 1.000000
    //     0xb8e4b4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb8e4b8: str             x16, [SP]
    // 0xb8e4bc: r1 = Null
    //     0xb8e4bc: mov             x1, NULL
    // 0xb8e4c0: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb8e4c0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb8e4c4: ldr             x4, [x4, #0x108]
    // 0xb8e4c8: r0 = Border.all()
    //     0xb8e4c8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb8e4cc: stur            x0, [fp, #-0x20]
    // 0xb8e4d0: r0 = BoxDecoration()
    //     0xb8e4d0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8e4d4: mov             x1, x0
    // 0xb8e4d8: r0 = Instance_Color
    //     0xb8e4d8: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb8e4dc: stur            x1, [fp, #-0x50]
    // 0xb8e4e0: StoreField: r1->field_7 = r0
    //     0xb8e4e0: stur            w0, [x1, #7]
    // 0xb8e4e4: ldur            x0, [fp, #-0x20]
    // 0xb8e4e8: StoreField: r1->field_f = r0
    //     0xb8e4e8: stur            w0, [x1, #0xf]
    // 0xb8e4ec: ldur            x0, [fp, #-0x40]
    // 0xb8e4f0: StoreField: r1->field_13 = r0
    //     0xb8e4f0: stur            w0, [x1, #0x13]
    // 0xb8e4f4: r0 = Instance_BoxShape
    //     0xb8e4f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8e4f8: ldr             x0, [x0, #0x80]
    // 0xb8e4fc: StoreField: r1->field_23 = r0
    //     0xb8e4fc: stur            w0, [x1, #0x23]
    // 0xb8e500: r0 = SvgPicture()
    //     0xb8e500: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb8e504: mov             x1, x0
    // 0xb8e508: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xb8e508: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xb8e50c: ldr             x2, [x2, #0x140]
    // 0xb8e510: stur            x0, [fp, #-0x20]
    // 0xb8e514: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb8e514: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb8e518: r0 = SvgPicture.asset()
    //     0xb8e518: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8e51c: r0 = Align()
    //     0xb8e51c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb8e520: mov             x2, x0
    // 0xb8e524: r0 = Instance_Alignment
    //     0xb8e524: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8e528: ldr             x0, [x0, #0xb10]
    // 0xb8e52c: stur            x2, [fp, #-0x40]
    // 0xb8e530: StoreField: r2->field_f = r0
    //     0xb8e530: stur            w0, [x2, #0xf]
    // 0xb8e534: r0 = 1.000000
    //     0xb8e534: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb8e538: StoreField: r2->field_13 = r0
    //     0xb8e538: stur            w0, [x2, #0x13]
    // 0xb8e53c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8e53c: stur            w0, [x2, #0x17]
    // 0xb8e540: ldur            x0, [fp, #-0x20]
    // 0xb8e544: StoreField: r2->field_b = r0
    //     0xb8e544: stur            w0, [x2, #0xb]
    // 0xb8e548: ldur            x0, [fp, #-8]
    // 0xb8e54c: LoadField: r1 = r0->field_b
    //     0xb8e54c: ldur            w1, [x0, #0xb]
    // 0xb8e550: DecompressPointer r1
    //     0xb8e550: add             x1, x1, HEAP, lsl #32
    // 0xb8e554: cmp             w1, NULL
    // 0xb8e558: b.eq            #0xb8e948
    // 0xb8e55c: LoadField: r0 = r1->field_b
    //     0xb8e55c: ldur            w0, [x1, #0xb]
    // 0xb8e560: DecompressPointer r0
    //     0xb8e560: add             x0, x0, HEAP, lsl #32
    // 0xb8e564: LoadField: r1 = r0->field_77
    //     0xb8e564: ldur            w1, [x0, #0x77]
    // 0xb8e568: DecompressPointer r1
    //     0xb8e568: add             x1, x1, HEAP, lsl #32
    // 0xb8e56c: cmp             w1, NULL
    // 0xb8e570: b.ne            #0xb8e57c
    // 0xb8e574: r5 = ""
    //     0xb8e574: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8e578: b               #0xb8e580
    // 0xb8e57c: mov             x5, x1
    // 0xb8e580: ldur            x4, [fp, #-0x18]
    // 0xb8e584: ldur            x3, [fp, #-0x38]
    // 0xb8e588: ldur            x0, [fp, #-0x48]
    // 0xb8e58c: ldur            d0, [fp, #-0x58]
    // 0xb8e590: ldur            x1, [fp, #-0x10]
    // 0xb8e594: stur            x5, [fp, #-8]
    // 0xb8e598: r0 = of()
    //     0xb8e598: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8e59c: LoadField: r1 = r0->field_87
    //     0xb8e59c: ldur            w1, [x0, #0x87]
    // 0xb8e5a0: DecompressPointer r1
    //     0xb8e5a0: add             x1, x1, HEAP, lsl #32
    // 0xb8e5a4: LoadField: r0 = r1->field_7
    //     0xb8e5a4: ldur            w0, [x1, #7]
    // 0xb8e5a8: DecompressPointer r0
    //     0xb8e5a8: add             x0, x0, HEAP, lsl #32
    // 0xb8e5ac: ldur            x1, [fp, #-0x10]
    // 0xb8e5b0: stur            x0, [fp, #-0x20]
    // 0xb8e5b4: r0 = of()
    //     0xb8e5b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8e5b8: LoadField: r1 = r0->field_5b
    //     0xb8e5b8: ldur            w1, [x0, #0x5b]
    // 0xb8e5bc: DecompressPointer r1
    //     0xb8e5bc: add             x1, x1, HEAP, lsl #32
    // 0xb8e5c0: r16 = 16.000000
    //     0xb8e5c0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb8e5c4: ldr             x16, [x16, #0x188]
    // 0xb8e5c8: stp             x1, x16, [SP]
    // 0xb8e5cc: ldur            x1, [fp, #-0x20]
    // 0xb8e5d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8e5d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8e5d4: ldr             x4, [x4, #0xaa0]
    // 0xb8e5d8: r0 = copyWith()
    //     0xb8e5d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8e5dc: stur            x0, [fp, #-0x10]
    // 0xb8e5e0: r0 = Text()
    //     0xb8e5e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8e5e4: mov             x1, x0
    // 0xb8e5e8: ldur            x0, [fp, #-8]
    // 0xb8e5ec: stur            x1, [fp, #-0x20]
    // 0xb8e5f0: StoreField: r1->field_b = r0
    //     0xb8e5f0: stur            w0, [x1, #0xb]
    // 0xb8e5f4: ldur            x0, [fp, #-0x10]
    // 0xb8e5f8: StoreField: r1->field_13 = r0
    //     0xb8e5f8: stur            w0, [x1, #0x13]
    // 0xb8e5fc: r0 = InkWell()
    //     0xb8e5fc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8e600: mov             x3, x0
    // 0xb8e604: ldur            x0, [fp, #-0x20]
    // 0xb8e608: stur            x3, [fp, #-8]
    // 0xb8e60c: StoreField: r3->field_b = r0
    //     0xb8e60c: stur            w0, [x3, #0xb]
    // 0xb8e610: ldur            x2, [fp, #-0x28]
    // 0xb8e614: r1 = Function '<anonymous closure>':.
    //     0xb8e614: add             x1, PP, #0x55, lsl #12  ; [pp+0x559e0] AnonymousClosure: (0xa8e78c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart] _ReplyToSellerItemViewState::build (0xc0b9f8)
    //     0xb8e618: ldr             x1, [x1, #0x9e0]
    // 0xb8e61c: r0 = AllocateClosure()
    //     0xb8e61c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8e620: mov             x1, x0
    // 0xb8e624: ldur            x0, [fp, #-8]
    // 0xb8e628: StoreField: r0->field_f = r1
    //     0xb8e628: stur            w1, [x0, #0xf]
    // 0xb8e62c: r3 = true
    //     0xb8e62c: add             x3, NULL, #0x20  ; true
    // 0xb8e630: StoreField: r0->field_43 = r3
    //     0xb8e630: stur            w3, [x0, #0x43]
    // 0xb8e634: r4 = Instance_BoxShape
    //     0xb8e634: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8e638: ldr             x4, [x4, #0x80]
    // 0xb8e63c: StoreField: r0->field_47 = r4
    //     0xb8e63c: stur            w4, [x0, #0x47]
    // 0xb8e640: StoreField: r0->field_6f = r3
    //     0xb8e640: stur            w3, [x0, #0x6f]
    // 0xb8e644: r5 = false
    //     0xb8e644: add             x5, NULL, #0x30  ; false
    // 0xb8e648: StoreField: r0->field_73 = r5
    //     0xb8e648: stur            w5, [x0, #0x73]
    // 0xb8e64c: StoreField: r0->field_83 = r3
    //     0xb8e64c: stur            w3, [x0, #0x83]
    // 0xb8e650: StoreField: r0->field_7b = r5
    //     0xb8e650: stur            w5, [x0, #0x7b]
    // 0xb8e654: r1 = Null
    //     0xb8e654: mov             x1, NULL
    // 0xb8e658: r2 = 6
    //     0xb8e658: movz            x2, #0x6
    // 0xb8e65c: r0 = AllocateArray()
    //     0xb8e65c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8e660: mov             x2, x0
    // 0xb8e664: ldur            x0, [fp, #-0x40]
    // 0xb8e668: stur            x2, [fp, #-0x10]
    // 0xb8e66c: StoreField: r2->field_f = r0
    //     0xb8e66c: stur            w0, [x2, #0xf]
    // 0xb8e670: r16 = Instance_SizedBox
    //     0xb8e670: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb8e674: ldr             x16, [x16, #0x998]
    // 0xb8e678: StoreField: r2->field_13 = r16
    //     0xb8e678: stur            w16, [x2, #0x13]
    // 0xb8e67c: ldur            x0, [fp, #-8]
    // 0xb8e680: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8e680: stur            w0, [x2, #0x17]
    // 0xb8e684: r1 = <Widget>
    //     0xb8e684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8e688: r0 = AllocateGrowableArray()
    //     0xb8e688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8e68c: mov             x1, x0
    // 0xb8e690: ldur            x0, [fp, #-0x10]
    // 0xb8e694: stur            x1, [fp, #-8]
    // 0xb8e698: StoreField: r1->field_f = r0
    //     0xb8e698: stur            w0, [x1, #0xf]
    // 0xb8e69c: r0 = 6
    //     0xb8e69c: movz            x0, #0x6
    // 0xb8e6a0: StoreField: r1->field_b = r0
    //     0xb8e6a0: stur            w0, [x1, #0xb]
    // 0xb8e6a4: r0 = Row()
    //     0xb8e6a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8e6a8: mov             x1, x0
    // 0xb8e6ac: r0 = Instance_Axis
    //     0xb8e6ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8e6b0: stur            x1, [fp, #-0x10]
    // 0xb8e6b4: StoreField: r1->field_f = r0
    //     0xb8e6b4: stur            w0, [x1, #0xf]
    // 0xb8e6b8: r0 = Instance_MainAxisAlignment
    //     0xb8e6b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb8e6bc: ldr             x0, [x0, #0xab0]
    // 0xb8e6c0: StoreField: r1->field_13 = r0
    //     0xb8e6c0: stur            w0, [x1, #0x13]
    // 0xb8e6c4: r0 = Instance_MainAxisSize
    //     0xb8e6c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8e6c8: ldr             x0, [x0, #0xa10]
    // 0xb8e6cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8e6cc: stur            w0, [x1, #0x17]
    // 0xb8e6d0: r2 = Instance_CrossAxisAlignment
    //     0xb8e6d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8e6d4: ldr             x2, [x2, #0xa18]
    // 0xb8e6d8: StoreField: r1->field_1b = r2
    //     0xb8e6d8: stur            w2, [x1, #0x1b]
    // 0xb8e6dc: r3 = Instance_VerticalDirection
    //     0xb8e6dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8e6e0: ldr             x3, [x3, #0xa20]
    // 0xb8e6e4: StoreField: r1->field_23 = r3
    //     0xb8e6e4: stur            w3, [x1, #0x23]
    // 0xb8e6e8: r4 = Instance_Clip
    //     0xb8e6e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8e6ec: ldr             x4, [x4, #0x38]
    // 0xb8e6f0: StoreField: r1->field_2b = r4
    //     0xb8e6f0: stur            w4, [x1, #0x2b]
    // 0xb8e6f4: StoreField: r1->field_2f = rZR
    //     0xb8e6f4: stur            xzr, [x1, #0x2f]
    // 0xb8e6f8: ldur            x5, [fp, #-8]
    // 0xb8e6fc: StoreField: r1->field_b = r5
    //     0xb8e6fc: stur            w5, [x1, #0xb]
    // 0xb8e700: ldur            d0, [fp, #-0x58]
    // 0xb8e704: r5 = inline_Allocate_Double()
    //     0xb8e704: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xb8e708: add             x5, x5, #0x10
    //     0xb8e70c: cmp             x6, x5
    //     0xb8e710: b.ls            #0xb8e94c
    //     0xb8e714: str             x5, [THR, #0x50]  ; THR::top
    //     0xb8e718: sub             x5, x5, #0xf
    //     0xb8e71c: movz            x6, #0xe15c
    //     0xb8e720: movk            x6, #0x3, lsl #16
    //     0xb8e724: stur            x6, [x5, #-1]
    // 0xb8e728: StoreField: r5->field_7 = d0
    //     0xb8e728: stur            d0, [x5, #7]
    // 0xb8e72c: stur            x5, [fp, #-8]
    // 0xb8e730: r0 = Container()
    //     0xb8e730: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8e734: stur            x0, [fp, #-0x20]
    // 0xb8e738: ldur            x16, [fp, #-8]
    // 0xb8e73c: r30 = 48.000000
    //     0xb8e73c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb8e740: ldr             lr, [lr, #0xad8]
    // 0xb8e744: stp             lr, x16, [SP, #0x18]
    // 0xb8e748: ldur            x16, [fp, #-0x50]
    // 0xb8e74c: r30 = Instance_Alignment
    //     0xb8e74c: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8e750: ldr             lr, [lr, #0xb10]
    // 0xb8e754: stp             lr, x16, [SP, #8]
    // 0xb8e758: ldur            x16, [fp, #-0x10]
    // 0xb8e75c: str             x16, [SP]
    // 0xb8e760: mov             x1, x0
    // 0xb8e764: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb8e764: add             x4, PP, #0x55, lsl #12  ; [pp+0x559e8] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb8e768: ldr             x4, [x4, #0x9e8]
    // 0xb8e76c: r0 = Container()
    //     0xb8e76c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8e770: r0 = InkWell()
    //     0xb8e770: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8e774: mov             x3, x0
    // 0xb8e778: ldur            x0, [fp, #-0x20]
    // 0xb8e77c: stur            x3, [fp, #-8]
    // 0xb8e780: StoreField: r3->field_b = r0
    //     0xb8e780: stur            w0, [x3, #0xb]
    // 0xb8e784: ldur            x2, [fp, #-0x28]
    // 0xb8e788: r1 = Function '<anonymous closure>':.
    //     0xb8e788: add             x1, PP, #0x55, lsl #12  ; [pp+0x559f0] AnonymousClosure: (0xa8e78c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart] _ReplyToSellerItemViewState::build (0xc0b9f8)
    //     0xb8e78c: ldr             x1, [x1, #0x9f0]
    // 0xb8e790: r0 = AllocateClosure()
    //     0xb8e790: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8e794: mov             x1, x0
    // 0xb8e798: ldur            x0, [fp, #-8]
    // 0xb8e79c: StoreField: r0->field_f = r1
    //     0xb8e79c: stur            w1, [x0, #0xf]
    // 0xb8e7a0: r1 = true
    //     0xb8e7a0: add             x1, NULL, #0x20  ; true
    // 0xb8e7a4: StoreField: r0->field_43 = r1
    //     0xb8e7a4: stur            w1, [x0, #0x43]
    // 0xb8e7a8: r2 = Instance_BoxShape
    //     0xb8e7a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8e7ac: ldr             x2, [x2, #0x80]
    // 0xb8e7b0: StoreField: r0->field_47 = r2
    //     0xb8e7b0: stur            w2, [x0, #0x47]
    // 0xb8e7b4: StoreField: r0->field_6f = r1
    //     0xb8e7b4: stur            w1, [x0, #0x6f]
    // 0xb8e7b8: r2 = false
    //     0xb8e7b8: add             x2, NULL, #0x30  ; false
    // 0xb8e7bc: StoreField: r0->field_73 = r2
    //     0xb8e7bc: stur            w2, [x0, #0x73]
    // 0xb8e7c0: StoreField: r0->field_83 = r1
    //     0xb8e7c0: stur            w1, [x0, #0x83]
    // 0xb8e7c4: StoreField: r0->field_7b = r2
    //     0xb8e7c4: stur            w2, [x0, #0x7b]
    // 0xb8e7c8: r0 = Padding()
    //     0xb8e7c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8e7cc: mov             x3, x0
    // 0xb8e7d0: r0 = Instance_EdgeInsets
    //     0xb8e7d0: add             x0, PP, #0x55, lsl #12  ; [pp+0x559f8] Obj!EdgeInsets@d59121
    //     0xb8e7d4: ldr             x0, [x0, #0x9f8]
    // 0xb8e7d8: stur            x3, [fp, #-0x10]
    // 0xb8e7dc: StoreField: r3->field_f = r0
    //     0xb8e7dc: stur            w0, [x3, #0xf]
    // 0xb8e7e0: ldur            x0, [fp, #-8]
    // 0xb8e7e4: StoreField: r3->field_b = r0
    //     0xb8e7e4: stur            w0, [x3, #0xb]
    // 0xb8e7e8: r1 = Null
    //     0xb8e7e8: mov             x1, NULL
    // 0xb8e7ec: r2 = 8
    //     0xb8e7ec: movz            x2, #0x8
    // 0xb8e7f0: r0 = AllocateArray()
    //     0xb8e7f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8e7f4: mov             x2, x0
    // 0xb8e7f8: ldur            x0, [fp, #-0x38]
    // 0xb8e7fc: stur            x2, [fp, #-8]
    // 0xb8e800: StoreField: r2->field_f = r0
    //     0xb8e800: stur            w0, [x2, #0xf]
    // 0xb8e804: r16 = Instance_SizedBox
    //     0xb8e804: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb8e808: ldr             x16, [x16, #0x8b8]
    // 0xb8e80c: StoreField: r2->field_13 = r16
    //     0xb8e80c: stur            w16, [x2, #0x13]
    // 0xb8e810: ldur            x0, [fp, #-0x48]
    // 0xb8e814: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8e814: stur            w0, [x2, #0x17]
    // 0xb8e818: ldur            x0, [fp, #-0x10]
    // 0xb8e81c: StoreField: r2->field_1b = r0
    //     0xb8e81c: stur            w0, [x2, #0x1b]
    // 0xb8e820: r1 = <Widget>
    //     0xb8e820: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8e824: r0 = AllocateGrowableArray()
    //     0xb8e824: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8e828: mov             x1, x0
    // 0xb8e82c: ldur            x0, [fp, #-8]
    // 0xb8e830: stur            x1, [fp, #-0x10]
    // 0xb8e834: StoreField: r1->field_f = r0
    //     0xb8e834: stur            w0, [x1, #0xf]
    // 0xb8e838: r0 = 8
    //     0xb8e838: movz            x0, #0x8
    // 0xb8e83c: StoreField: r1->field_b = r0
    //     0xb8e83c: stur            w0, [x1, #0xb]
    // 0xb8e840: r0 = Column()
    //     0xb8e840: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8e844: mov             x1, x0
    // 0xb8e848: r0 = Instance_Axis
    //     0xb8e848: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8e84c: stur            x1, [fp, #-8]
    // 0xb8e850: StoreField: r1->field_f = r0
    //     0xb8e850: stur            w0, [x1, #0xf]
    // 0xb8e854: r0 = Instance_MainAxisAlignment
    //     0xb8e854: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8e858: ldr             x0, [x0, #0xa08]
    // 0xb8e85c: StoreField: r1->field_13 = r0
    //     0xb8e85c: stur            w0, [x1, #0x13]
    // 0xb8e860: r0 = Instance_MainAxisSize
    //     0xb8e860: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8e864: ldr             x0, [x0, #0xa10]
    // 0xb8e868: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8e868: stur            w0, [x1, #0x17]
    // 0xb8e86c: r0 = Instance_CrossAxisAlignment
    //     0xb8e86c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8e870: ldr             x0, [x0, #0xa18]
    // 0xb8e874: StoreField: r1->field_1b = r0
    //     0xb8e874: stur            w0, [x1, #0x1b]
    // 0xb8e878: r0 = Instance_VerticalDirection
    //     0xb8e878: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8e87c: ldr             x0, [x0, #0xa20]
    // 0xb8e880: StoreField: r1->field_23 = r0
    //     0xb8e880: stur            w0, [x1, #0x23]
    // 0xb8e884: r0 = Instance_Clip
    //     0xb8e884: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8e888: ldr             x0, [x0, #0x38]
    // 0xb8e88c: StoreField: r1->field_2b = r0
    //     0xb8e88c: stur            w0, [x1, #0x2b]
    // 0xb8e890: StoreField: r1->field_2f = rZR
    //     0xb8e890: stur            xzr, [x1, #0x2f]
    // 0xb8e894: ldur            x0, [fp, #-0x10]
    // 0xb8e898: StoreField: r1->field_b = r0
    //     0xb8e898: stur            w0, [x1, #0xb]
    // 0xb8e89c: r0 = Container()
    //     0xb8e89c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8e8a0: stur            x0, [fp, #-0x10]
    // 0xb8e8a4: r16 = inf
    //     0xb8e8a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb8e8a8: ldr             x16, [x16, #0x9f8]
    // 0xb8e8ac: r30 = 152.000000
    //     0xb8e8ac: add             lr, PP, #0x55, lsl #12  ; [pp+0x55a00] 152
    //     0xb8e8b0: ldr             lr, [lr, #0xa00]
    // 0xb8e8b4: stp             lr, x16, [SP, #0x10]
    // 0xb8e8b8: ldur            x16, [fp, #-0x30]
    // 0xb8e8bc: ldur            lr, [fp, #-8]
    // 0xb8e8c0: stp             lr, x16, [SP]
    // 0xb8e8c4: mov             x1, x0
    // 0xb8e8c8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb8e8c8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb8e8cc: ldr             x4, [x4, #0x870]
    // 0xb8e8d0: r0 = Container()
    //     0xb8e8d0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8e8d4: r0 = Visibility()
    //     0xb8e8d4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8e8d8: mov             x1, x0
    // 0xb8e8dc: ldur            x0, [fp, #-0x10]
    // 0xb8e8e0: stur            x1, [fp, #-8]
    // 0xb8e8e4: StoreField: r1->field_b = r0
    //     0xb8e8e4: stur            w0, [x1, #0xb]
    // 0xb8e8e8: r0 = Instance_SizedBox
    //     0xb8e8e8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8e8ec: StoreField: r1->field_f = r0
    //     0xb8e8ec: stur            w0, [x1, #0xf]
    // 0xb8e8f0: ldur            x0, [fp, #-0x18]
    // 0xb8e8f4: StoreField: r1->field_13 = r0
    //     0xb8e8f4: stur            w0, [x1, #0x13]
    // 0xb8e8f8: r0 = false
    //     0xb8e8f8: add             x0, NULL, #0x30  ; false
    // 0xb8e8fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8e8fc: stur            w0, [x1, #0x17]
    // 0xb8e900: StoreField: r1->field_1b = r0
    //     0xb8e900: stur            w0, [x1, #0x1b]
    // 0xb8e904: StoreField: r1->field_1f = r0
    //     0xb8e904: stur            w0, [x1, #0x1f]
    // 0xb8e908: StoreField: r1->field_23 = r0
    //     0xb8e908: stur            w0, [x1, #0x23]
    // 0xb8e90c: StoreField: r1->field_27 = r0
    //     0xb8e90c: stur            w0, [x1, #0x27]
    // 0xb8e910: StoreField: r1->field_2b = r0
    //     0xb8e910: stur            w0, [x1, #0x2b]
    // 0xb8e914: r0 = Padding()
    //     0xb8e914: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8e918: r1 = Instance_EdgeInsets
    //     0xb8e918: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb8e91c: ldr             x1, [x1, #0x110]
    // 0xb8e920: StoreField: r0->field_f = r1
    //     0xb8e920: stur            w1, [x0, #0xf]
    // 0xb8e924: ldur            x1, [fp, #-8]
    // 0xb8e928: StoreField: r0->field_b = r1
    //     0xb8e928: stur            w1, [x0, #0xb]
    // 0xb8e92c: LeaveFrame
    //     0xb8e92c: mov             SP, fp
    //     0xb8e930: ldp             fp, lr, [SP], #0x10
    // 0xb8e934: ret
    //     0xb8e934: ret             
    // 0xb8e938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e938: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e93c: b               #0xb8e1f0
    // 0xb8e940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e940: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8e944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e944: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8e948: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e948: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8e94c: SaveReg d0
    //     0xb8e94c: str             q0, [SP, #-0x10]!
    // 0xb8e950: stp             x3, x4, [SP, #-0x10]!
    // 0xb8e954: stp             x1, x2, [SP, #-0x10]!
    // 0xb8e958: SaveReg r0
    //     0xb8e958: str             x0, [SP, #-8]!
    // 0xb8e95c: r0 = AllocateDouble()
    //     0xb8e95c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb8e960: mov             x5, x0
    // 0xb8e964: RestoreReg r0
    //     0xb8e964: ldr             x0, [SP], #8
    // 0xb8e968: ldp             x1, x2, [SP], #0x10
    // 0xb8e96c: ldp             x3, x4, [SP], #0x10
    // 0xb8e970: RestoreReg d0
    //     0xb8e970: ldr             q0, [SP], #0x10
    // 0xb8e974: b               #0xb8e728
  }
}

// class id: 4050, size: 0x14, field offset: 0xc
//   const constructor, 
class ReplyToSellerItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fa28, size: 0x24
    // 0xc7fa28: EnterFrame
    //     0xc7fa28: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fa2c: mov             fp, SP
    // 0xc7fa30: mov             x0, x1
    // 0xc7fa34: r1 = <ReplyToSellerItemView>
    //     0xc7fa34: add             x1, PP, #0x48, lsl #12  ; [pp+0x487e0] TypeArguments: <ReplyToSellerItemView>
    //     0xc7fa38: ldr             x1, [x1, #0x7e0]
    // 0xc7fa3c: r0 = _ReplyToSellerItemViewState()
    //     0xc7fa3c: bl              #0xc7fa4c  ; Allocate_ReplyToSellerItemViewStateStub -> _ReplyToSellerItemViewState (size=0x14)
    // 0xc7fa40: LeaveFrame
    //     0xc7fa40: mov             SP, fp
    //     0xc7fa44: ldp             fp, lr, [SP], #0x10
    // 0xc7fa48: ret
    //     0xc7fa48: ret             
  }
}
