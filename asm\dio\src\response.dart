// lib: , url: package:dio/src/response.dart

// class id: 1049613, size: 0x8
class :: {
}

// class id: 4973, size: 0x2c, field offset: 0x8
class Response<X0> extends Object {

  _ Response(/* No info */) {
    // ** addr: 0x862bac, size: 0x414
    // 0x862bac: EnterFrame
    //     0x862bac: stp             fp, lr, [SP, #-0x10]!
    //     0x862bb0: mov             fp, SP
    // 0x862bb4: AllocStack(0x28)
    //     0x862bb4: sub             SP, SP, #0x28
    // 0x862bb8: SetupParameters(Response<X0> this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1 */, {dynamic data = Null /* r5 */, dynamic extra = Null /* r6, fp-0x8 */, dynamic headers = Null /* r7 */, dynamic isRedirect = false /* r8 */, dynamic redirects = const [] /* r9 */, dynamic statusCode = Null /* r10 */, dynamic statusMessage = Null /* r3 */})
    //     0x862bb8: stur            x1, [fp, #-0x10]
    //     0x862bbc: mov             x16, x2
    //     0x862bc0: mov             x2, x1
    //     0x862bc4: mov             x1, x16
    //     0x862bc8: ldur            w0, [x4, #0x13]
    //     0x862bcc: ldur            w3, [x4, #0x1f]
    //     0x862bd0: add             x3, x3, HEAP, lsl #32
    //     0x862bd4: ldr             x16, [PP, #0x1318]  ; [pp+0x1318] "data"
    //     0x862bd8: cmp             w3, w16
    //     0x862bdc: b.ne            #0x862c00
    //     0x862be0: ldur            w3, [x4, #0x23]
    //     0x862be4: add             x3, x3, HEAP, lsl #32
    //     0x862be8: sub             w5, w0, w3
    //     0x862bec: add             x3, fp, w5, sxtw #2
    //     0x862bf0: ldr             x3, [x3, #8]
    //     0x862bf4: mov             x5, x3
    //     0x862bf8: movz            x3, #0x1
    //     0x862bfc: b               #0x862c08
    //     0x862c00: mov             x5, NULL
    //     0x862c04: movz            x3, #0
    //     0x862c08: lsl             x6, x3, #1
    //     0x862c0c: lsl             w7, w6, #1
    //     0x862c10: add             w8, w7, #8
    //     0x862c14: add             x16, x4, w8, sxtw #1
    //     0x862c18: ldur            w9, [x16, #0xf]
    //     0x862c1c: add             x9, x9, HEAP, lsl #32
    //     0x862c20: add             x16, PP, #0xa, lsl #12  ; [pp+0xa778] "extra"
    //     0x862c24: ldr             x16, [x16, #0x778]
    //     0x862c28: cmp             w9, w16
    //     0x862c2c: b.ne            #0x862c60
    //     0x862c30: add             w3, w7, #0xa
    //     0x862c34: add             x16, x4, w3, sxtw #1
    //     0x862c38: ldur            w7, [x16, #0xf]
    //     0x862c3c: add             x7, x7, HEAP, lsl #32
    //     0x862c40: sub             w3, w0, w7
    //     0x862c44: add             x7, fp, w3, sxtw #2
    //     0x862c48: ldr             x7, [x7, #8]
    //     0x862c4c: add             w3, w6, #2
    //     0x862c50: sbfx            x6, x3, #1, #0x1f
    //     0x862c54: mov             x3, x6
    //     0x862c58: mov             x6, x7
    //     0x862c5c: b               #0x862c64
    //     0x862c60: mov             x6, NULL
    //     0x862c64: stur            x6, [fp, #-8]
    //     0x862c68: lsl             x7, x3, #1
    //     0x862c6c: lsl             w8, w7, #1
    //     0x862c70: add             w9, w8, #8
    //     0x862c74: add             x16, x4, w9, sxtw #1
    //     0x862c78: ldur            w10, [x16, #0xf]
    //     0x862c7c: add             x10, x10, HEAP, lsl #32
    //     0x862c80: add             x16, PP, #0xa, lsl #12  ; [pp+0xa780] "headers"
    //     0x862c84: ldr             x16, [x16, #0x780]
    //     0x862c88: cmp             w10, w16
    //     0x862c8c: b.ne            #0x862cc0
    //     0x862c90: add             w3, w8, #0xa
    //     0x862c94: add             x16, x4, w3, sxtw #1
    //     0x862c98: ldur            w8, [x16, #0xf]
    //     0x862c9c: add             x8, x8, HEAP, lsl #32
    //     0x862ca0: sub             w3, w0, w8
    //     0x862ca4: add             x8, fp, w3, sxtw #2
    //     0x862ca8: ldr             x8, [x8, #8]
    //     0x862cac: add             w3, w7, #2
    //     0x862cb0: sbfx            x7, x3, #1, #0x1f
    //     0x862cb4: mov             x3, x7
    //     0x862cb8: mov             x7, x8
    //     0x862cbc: b               #0x862cc4
    //     0x862cc0: mov             x7, NULL
    //     0x862cc4: lsl             x8, x3, #1
    //     0x862cc8: lsl             w9, w8, #1
    //     0x862ccc: add             w10, w9, #8
    //     0x862cd0: add             x16, x4, w10, sxtw #1
    //     0x862cd4: ldur            w11, [x16, #0xf]
    //     0x862cd8: add             x11, x11, HEAP, lsl #32
    //     0x862cdc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa788] "isRedirect"
    //     0x862ce0: ldr             x16, [x16, #0x788]
    //     0x862ce4: cmp             w11, w16
    //     0x862ce8: b.ne            #0x862d1c
    //     0x862cec: add             w3, w9, #0xa
    //     0x862cf0: add             x16, x4, w3, sxtw #1
    //     0x862cf4: ldur            w9, [x16, #0xf]
    //     0x862cf8: add             x9, x9, HEAP, lsl #32
    //     0x862cfc: sub             w3, w0, w9
    //     0x862d00: add             x9, fp, w3, sxtw #2
    //     0x862d04: ldr             x9, [x9, #8]
    //     0x862d08: add             w3, w8, #2
    //     0x862d0c: sbfx            x8, x3, #1, #0x1f
    //     0x862d10: mov             x3, x8
    //     0x862d14: mov             x8, x9
    //     0x862d18: b               #0x862d20
    //     0x862d1c: add             x8, NULL, #0x30  ; false
    //     0x862d20: lsl             x9, x3, #1
    //     0x862d24: lsl             w10, w9, #1
    //     0x862d28: add             w11, w10, #8
    //     0x862d2c: add             x16, x4, w11, sxtw #1
    //     0x862d30: ldur            w12, [x16, #0xf]
    //     0x862d34: add             x12, x12, HEAP, lsl #32
    //     0x862d38: add             x16, PP, #0xa, lsl #12  ; [pp+0xa790] "redirects"
    //     0x862d3c: ldr             x16, [x16, #0x790]
    //     0x862d40: cmp             w12, w16
    //     0x862d44: b.ne            #0x862d78
    //     0x862d48: add             w3, w10, #0xa
    //     0x862d4c: add             x16, x4, w3, sxtw #1
    //     0x862d50: ldur            w10, [x16, #0xf]
    //     0x862d54: add             x10, x10, HEAP, lsl #32
    //     0x862d58: sub             w3, w0, w10
    //     0x862d5c: add             x10, fp, w3, sxtw #2
    //     0x862d60: ldr             x10, [x10, #8]
    //     0x862d64: add             w3, w9, #2
    //     0x862d68: sbfx            x9, x3, #1, #0x1f
    //     0x862d6c: mov             x3, x9
    //     0x862d70: mov             x9, x10
    //     0x862d74: b               #0x862d80
    //     0x862d78: add             x9, PP, #0xa, lsl #12  ; [pp+0xa798] List<RedirectRecord>(0)
    //     0x862d7c: ldr             x9, [x9, #0x798]
    //     0x862d80: lsl             x10, x3, #1
    //     0x862d84: lsl             w11, w10, #1
    //     0x862d88: add             w12, w11, #8
    //     0x862d8c: add             x16, x4, w12, sxtw #1
    //     0x862d90: ldur            w13, [x16, #0xf]
    //     0x862d94: add             x13, x13, HEAP, lsl #32
    //     0x862d98: add             x16, PP, #8, lsl #12  ; [pp+0x8018] "statusCode"
    //     0x862d9c: ldr             x16, [x16, #0x18]
    //     0x862da0: cmp             w13, w16
    //     0x862da4: b.ne            #0x862dd8
    //     0x862da8: add             w3, w11, #0xa
    //     0x862dac: add             x16, x4, w3, sxtw #1
    //     0x862db0: ldur            w11, [x16, #0xf]
    //     0x862db4: add             x11, x11, HEAP, lsl #32
    //     0x862db8: sub             w3, w0, w11
    //     0x862dbc: add             x11, fp, w3, sxtw #2
    //     0x862dc0: ldr             x11, [x11, #8]
    //     0x862dc4: add             w3, w10, #2
    //     0x862dc8: sbfx            x10, x3, #1, #0x1f
    //     0x862dcc: mov             x3, x10
    //     0x862dd0: mov             x10, x11
    //     0x862dd4: b               #0x862ddc
    //     0x862dd8: mov             x10, NULL
    //     0x862ddc: lsl             x11, x3, #1
    //     0x862de0: lsl             w3, w11, #1
    //     0x862de4: add             w11, w3, #8
    //     0x862de8: add             x16, x4, w11, sxtw #1
    //     0x862dec: ldur            w12, [x16, #0xf]
    //     0x862df0: add             x12, x12, HEAP, lsl #32
    //     0x862df4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7a0] "statusMessage"
    //     0x862df8: ldr             x16, [x16, #0x7a0]
    //     0x862dfc: cmp             w12, w16
    //     0x862e00: b.ne            #0x862e28
    //     0x862e04: add             w11, w3, #0xa
    //     0x862e08: add             x16, x4, w11, sxtw #1
    //     0x862e0c: ldur            w3, [x16, #0xf]
    //     0x862e10: add             x3, x3, HEAP, lsl #32
    //     0x862e14: sub             w4, w0, w3
    //     0x862e18: add             x0, fp, w4, sxtw #2
    //     0x862e1c: ldr             x0, [x0, #8]
    //     0x862e20: mov             x3, x0
    //     0x862e24: b               #0x862e2c
    //     0x862e28: mov             x3, NULL
    // 0x862e2c: CheckStackOverflow
    //     0x862e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862e30: cmp             SP, x16
    //     0x862e34: b.ls            #0x862fac
    // 0x862e38: mov             x0, x5
    // 0x862e3c: StoreField: r2->field_b = r0
    //     0x862e3c: stur            w0, [x2, #0xb]
    //     0x862e40: tbz             w0, #0, #0x862e5c
    //     0x862e44: ldurb           w16, [x2, #-1]
    //     0x862e48: ldurb           w17, [x0, #-1]
    //     0x862e4c: and             x16, x17, x16, lsr #2
    //     0x862e50: tst             x16, HEAP, lsr #32
    //     0x862e54: b.eq            #0x862e5c
    //     0x862e58: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x862e5c: mov             x0, x1
    // 0x862e60: StoreField: r2->field_f = r0
    //     0x862e60: stur            w0, [x2, #0xf]
    //     0x862e64: ldurb           w16, [x2, #-1]
    //     0x862e68: ldurb           w17, [x0, #-1]
    //     0x862e6c: and             x16, x17, x16, lsr #2
    //     0x862e70: tst             x16, HEAP, lsr #32
    //     0x862e74: b.eq            #0x862e7c
    //     0x862e78: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x862e7c: mov             x0, x10
    // 0x862e80: StoreField: r2->field_13 = r0
    //     0x862e80: stur            w0, [x2, #0x13]
    //     0x862e84: tbz             w0, #0, #0x862ea0
    //     0x862e88: ldurb           w16, [x2, #-1]
    //     0x862e8c: ldurb           w17, [x0, #-1]
    //     0x862e90: and             x16, x17, x16, lsr #2
    //     0x862e94: tst             x16, HEAP, lsr #32
    //     0x862e98: b.eq            #0x862ea0
    //     0x862e9c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x862ea0: mov             x0, x3
    // 0x862ea4: ArrayStore: r2[0] = r0  ; List_4
    //     0x862ea4: stur            w0, [x2, #0x17]
    //     0x862ea8: ldurb           w16, [x2, #-1]
    //     0x862eac: ldurb           w17, [x0, #-1]
    //     0x862eb0: and             x16, x17, x16, lsr #2
    //     0x862eb4: tst             x16, HEAP, lsr #32
    //     0x862eb8: b.eq            #0x862ec0
    //     0x862ebc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x862ec0: StoreField: r2->field_1f = r8
    //     0x862ec0: stur            w8, [x2, #0x1f]
    // 0x862ec4: mov             x0, x9
    // 0x862ec8: StoreField: r2->field_23 = r0
    //     0x862ec8: stur            w0, [x2, #0x23]
    //     0x862ecc: ldurb           w16, [x2, #-1]
    //     0x862ed0: ldurb           w17, [x0, #-1]
    //     0x862ed4: and             x16, x17, x16, lsr #2
    //     0x862ed8: tst             x16, HEAP, lsr #32
    //     0x862edc: b.eq            #0x862ee4
    //     0x862ee0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x862ee4: cmp             w7, NULL
    // 0x862ee8: b.ne            #0x862f30
    // 0x862eec: LoadField: r0 = r1->field_f
    //     0x862eec: ldur            w0, [x1, #0xf]
    // 0x862ef0: DecompressPointer r0
    //     0x862ef0: add             x0, x0, HEAP, lsl #32
    // 0x862ef4: r16 = Sentinel
    //     0x862ef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x862ef8: cmp             w0, w16
    // 0x862efc: b.eq            #0x862fb4
    // 0x862f00: r16 = <List<String>>
    //     0x862f00: add             x16, PP, #8, lsl #12  ; [pp+0x8b30] TypeArguments: <List<String>>
    //     0x862f04: ldr             x16, [x16, #0xb30]
    // 0x862f08: str             x16, [SP]
    // 0x862f0c: r4 = const [0x1, 0, 0, 0, null]
    //     0x862f0c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x862f10: r0 = caseInsensitiveKeyMap()
    //     0x862f10: bl              #0x8626d4  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x862f14: stur            x0, [fp, #-0x18]
    // 0x862f18: r0 = Headers()
    //     0x862f18: bl              #0x862ba0  ; AllocateHeadersStub -> Headers (size=0xc)
    // 0x862f1c: mov             x1, x0
    // 0x862f20: ldur            x0, [fp, #-0x18]
    // 0x862f24: StoreField: r1->field_7 = r0
    //     0x862f24: stur            w0, [x1, #7]
    // 0x862f28: mov             x0, x1
    // 0x862f2c: b               #0x862f34
    // 0x862f30: mov             x0, x7
    // 0x862f34: ldur            x1, [fp, #-0x10]
    // 0x862f38: ldur            x2, [fp, #-8]
    // 0x862f3c: StoreField: r1->field_1b = r0
    //     0x862f3c: stur            w0, [x1, #0x1b]
    //     0x862f40: ldurb           w16, [x1, #-1]
    //     0x862f44: ldurb           w17, [x0, #-1]
    //     0x862f48: and             x16, x17, x16, lsr #2
    //     0x862f4c: tst             x16, HEAP, lsr #32
    //     0x862f50: b.eq            #0x862f58
    //     0x862f54: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x862f58: cmp             w2, NULL
    // 0x862f5c: b.ne            #0x862f74
    // 0x862f60: r16 = <String, dynamic>
    //     0x862f60: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x862f64: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x862f68: stp             lr, x16, [SP]
    // 0x862f6c: r0 = Map._fromLiteral()
    //     0x862f6c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x862f70: b               #0x862f78
    // 0x862f74: mov             x0, x2
    // 0x862f78: ldur            x1, [fp, #-0x10]
    // 0x862f7c: StoreField: r1->field_27 = r0
    //     0x862f7c: stur            w0, [x1, #0x27]
    //     0x862f80: tbz             w0, #0, #0x862f9c
    //     0x862f84: ldurb           w16, [x1, #-1]
    //     0x862f88: ldurb           w17, [x0, #-1]
    //     0x862f8c: and             x16, x17, x16, lsr #2
    //     0x862f90: tst             x16, HEAP, lsr #32
    //     0x862f94: b.eq            #0x862f9c
    //     0x862f98: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x862f9c: r0 = Null
    //     0x862f9c: mov             x0, NULL
    // 0x862fa0: LeaveFrame
    //     0x862fa0: mov             SP, fp
    //     0x862fa4: ldp             fp, lr, [SP], #0x10
    // 0x862fa8: ret
    //     0x862fa8: ret             
    // 0x862fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862fac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862fb0: b               #0x862e38
    // 0x862fb4: r9 = preserveHeaderCase
    //     0x862fb4: add             x9, PP, #8, lsl #12  ; [pp+0x84c0] Field <<EMAIL>>: late (offset: 0x10)
    //     0x862fb8: ldr             x9, [x9, #0x4c0]
    // 0x862fbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x862fbc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0x156874c, size: 0x144
    // 0x156874c: EnterFrame
    //     0x156874c: stp             fp, lr, [SP, #-0x10]!
    //     0x1568750: mov             fp, SP
    // 0x1568754: AllocStack(0x10)
    //     0x1568754: sub             SP, SP, #0x10
    // 0x1568758: CheckStackOverflow
    //     0x1568758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x156875c: cmp             SP, x16
    //     0x1568760: b.ls            #0x1568888
    // 0x1568764: ldr             x0, [fp, #0x10]
    // 0x1568768: LoadField: r3 = r0->field_b
    //     0x1568768: ldur            w3, [x0, #0xb]
    // 0x156876c: DecompressPointer r3
    //     0x156876c: add             x3, x3, HEAP, lsl #32
    // 0x1568770: mov             x0, x3
    // 0x1568774: stur            x3, [fp, #-8]
    // 0x1568778: r2 = Null
    //     0x1568778: mov             x2, NULL
    // 0x156877c: r1 = Null
    //     0x156877c: mov             x1, NULL
    // 0x1568780: cmp             w0, NULL
    // 0x1568784: b.eq            #0x156881c
    // 0x1568788: branchIfSmi(r0, 0x156881c)
    //     0x1568788: tbz             w0, #0, #0x156881c
    // 0x156878c: r3 = LoadClassIdInstr(r0)
    //     0x156878c: ldur            x3, [x0, #-1]
    //     0x1568790: ubfx            x3, x3, #0xc, #0x14
    // 0x1568794: r17 = 6675
    //     0x1568794: movz            x17, #0x1a13
    // 0x1568798: cmp             x3, x17
    // 0x156879c: b.eq            #0x1568824
    // 0x15687a0: r4 = LoadClassIdInstr(r0)
    //     0x15687a0: ldur            x4, [x0, #-1]
    //     0x15687a4: ubfx            x4, x4, #0xc, #0x14
    // 0x15687a8: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x15687ac: ldr             x3, [x3, #0x18]
    // 0x15687b0: ldr             x3, [x3, x4, lsl #3]
    // 0x15687b4: LoadField: r3 = r3->field_2b
    //     0x15687b4: ldur            w3, [x3, #0x2b]
    // 0x15687b8: DecompressPointer r3
    //     0x15687b8: add             x3, x3, HEAP, lsl #32
    // 0x15687bc: cmp             w3, NULL
    // 0x15687c0: b.eq            #0x156881c
    // 0x15687c4: LoadField: r3 = r3->field_f
    //     0x15687c4: ldur            w3, [x3, #0xf]
    // 0x15687c8: lsr             x3, x3, #3
    // 0x15687cc: r17 = 6675
    //     0x15687cc: movz            x17, #0x1a13
    // 0x15687d0: cmp             x3, x17
    // 0x15687d4: b.eq            #0x1568824
    // 0x15687d8: r3 = SubtypeTestCache
    //     0x15687d8: add             x3, PP, #0x26, lsl #12  ; [pp+0x26220] SubtypeTestCache
    //     0x15687dc: ldr             x3, [x3, #0x220]
    // 0x15687e0: r30 = Subtype1TestCacheStub
    //     0x15687e0: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x15687e4: LoadField: r30 = r30->field_7
    //     0x15687e4: ldur            lr, [lr, #7]
    // 0x15687e8: blr             lr
    // 0x15687ec: cmp             w7, NULL
    // 0x15687f0: b.eq            #0x15687fc
    // 0x15687f4: tbnz            w7, #4, #0x156881c
    // 0x15687f8: b               #0x1568824
    // 0x15687fc: r8 = Map
    //     0x15687fc: add             x8, PP, #0x26, lsl #12  ; [pp+0x26228] Type: Map
    //     0x1568800: ldr             x8, [x8, #0x228]
    // 0x1568804: r3 = SubtypeTestCache
    //     0x1568804: add             x3, PP, #0x26, lsl #12  ; [pp+0x26230] SubtypeTestCache
    //     0x1568808: ldr             x3, [x3, #0x230]
    // 0x156880c: r30 = InstanceOfStub
    //     0x156880c: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x1568810: LoadField: r30 = r30->field_7
    //     0x1568810: ldur            lr, [lr, #7]
    // 0x1568814: blr             lr
    // 0x1568818: b               #0x1568828
    // 0x156881c: r0 = false
    //     0x156881c: add             x0, NULL, #0x30  ; false
    // 0x1568820: b               #0x1568828
    // 0x1568824: r0 = true
    //     0x1568824: add             x0, NULL, #0x20  ; true
    // 0x1568828: tbnz            w0, #4, #0x156884c
    // 0x156882c: ldur            x2, [fp, #-8]
    // 0x1568830: r1 = Instance_JsonCodec
    //     0x1568830: add             x1, PP, #8, lsl #12  ; [pp+0x8ea0] Obj!JsonCodec@d6ed71
    //     0x1568834: ldr             x1, [x1, #0xea0]
    // 0x1568838: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1568838: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x156883c: r0 = encode()
    //     0x156883c: bl              #0x16375b0  ; [dart:convert] JsonCodec::encode
    // 0x1568840: LeaveFrame
    //     0x1568840: mov             SP, fp
    //     0x1568844: ldp             fp, lr, [SP], #0x10
    // 0x1568848: ret
    //     0x1568848: ret             
    // 0x156884c: ldur            x0, [fp, #-8]
    // 0x1568850: r1 = 60
    //     0x1568850: movz            x1, #0x3c
    // 0x1568854: branchIfSmi(r0, 0x1568860)
    //     0x1568854: tbz             w0, #0, #0x1568860
    // 0x1568858: r1 = LoadClassIdInstr(r0)
    //     0x1568858: ldur            x1, [x0, #-1]
    //     0x156885c: ubfx            x1, x1, #0xc, #0x14
    // 0x1568860: str             x0, [SP]
    // 0x1568864: mov             x0, x1
    // 0x1568868: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x1568868: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x156886c: r0 = GDT[cid_x0 + 0x2700]()
    //     0x156886c: movz            x17, #0x2700
    //     0x1568870: add             lr, x0, x17
    //     0x1568874: ldr             lr, [x21, lr, lsl #3]
    //     0x1568878: blr             lr
    // 0x156887c: LeaveFrame
    //     0x156887c: mov             SP, fp
    //     0x1568880: ldp             fp, lr, [SP], #0x10
    // 0x1568884: ret
    //     0x1568884: ret             
    // 0x1568888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568888: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x156888c: b               #0x1568764
  }
}
