// lib: , url: package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart

// class id: 1049548, size: 0x8
class :: {
}

// class id: 4525, size: 0x14, field offset: 0x14
//   const constructor, 
class ReplaceCallOrderView extends BaseView<dynamic> {

  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x137aadc, size: 0x94
    // 0x137aadc: EnterFrame
    //     0x137aadc: stp             fp, lr, [SP, #-0x10]!
    //     0x137aae0: mov             fp, SP
    // 0x137aae4: AllocStack(0x8)
    //     0x137aae4: sub             SP, SP, #8
    // 0x137aae8: SetupParameters()
    //     0x137aae8: ldr             x0, [fp, #0x10]
    //     0x137aaec: ldur            w1, [x0, #0x17]
    //     0x137aaf0: add             x1, x1, HEAP, lsl #32
    // 0x137aaf4: CheckStackOverflow
    //     0x137aaf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137aaf8: cmp             SP, x16
    //     0x137aafc: b.ls            #0x137ab68
    // 0x137ab00: LoadField: r0 = r1->field_f
    //     0x137ab00: ldur            w0, [x1, #0xf]
    // 0x137ab04: DecompressPointer r0
    //     0x137ab04: add             x0, x0, HEAP, lsl #32
    // 0x137ab08: mov             x1, x0
    // 0x137ab0c: r2 = false
    //     0x137ab0c: add             x2, NULL, #0x30  ; false
    // 0x137ab10: r0 = showLoading()
    //     0x137ab10: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x137ab14: r1 = <bool>
    //     0x137ab14: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x137ab18: r0 = _Future()
    //     0x137ab18: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x137ab1c: stur            x0, [fp, #-8]
    // 0x137ab20: StoreField: r0->field_b = rZR
    //     0x137ab20: stur            xzr, [x0, #0xb]
    // 0x137ab24: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x137ab24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x137ab28: ldr             x0, [x0, #0x778]
    //     0x137ab2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x137ab30: cmp             w0, w16
    //     0x137ab34: b.ne            #0x137ab40
    //     0x137ab38: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x137ab3c: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x137ab40: mov             x1, x0
    // 0x137ab44: ldur            x0, [fp, #-8]
    // 0x137ab48: StoreField: r0->field_13 = r1
    //     0x137ab48: stur            w1, [x0, #0x13]
    // 0x137ab4c: mov             x1, x0
    // 0x137ab50: r2 = true
    //     0x137ab50: add             x2, NULL, #0x20  ; true
    // 0x137ab54: r0 = _asyncComplete()
    //     0x137ab54: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x137ab58: ldur            x0, [fp, #-8]
    // 0x137ab5c: LeaveFrame
    //     0x137ab5c: mov             SP, fp
    //     0x137ab60: ldp             fp, lr, [SP], #0x10
    // 0x137ab64: ret
    //     0x137ab64: ret             
    // 0x137ab68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137ab68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137ab6c: b               #0x137ab00
  }
  _ body(/* No info */) {
    // ** addr: 0x1506d28, size: 0x88
    // 0x1506d28: EnterFrame
    //     0x1506d28: stp             fp, lr, [SP, #-0x10]!
    //     0x1506d2c: mov             fp, SP
    // 0x1506d30: AllocStack(0x18)
    //     0x1506d30: sub             SP, SP, #0x18
    // 0x1506d34: SetupParameters(ReplaceCallOrderView this /* r1 => r1, fp-0x8 */)
    //     0x1506d34: stur            x1, [fp, #-8]
    // 0x1506d38: r1 = 1
    //     0x1506d38: movz            x1, #0x1
    // 0x1506d3c: r0 = AllocateContext()
    //     0x1506d3c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1506d40: mov             x1, x0
    // 0x1506d44: ldur            x0, [fp, #-8]
    // 0x1506d48: stur            x1, [fp, #-0x10]
    // 0x1506d4c: StoreField: r1->field_f = r0
    //     0x1506d4c: stur            w0, [x1, #0xf]
    // 0x1506d50: r0 = Obx()
    //     0x1506d50: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1506d54: ldur            x2, [fp, #-0x10]
    // 0x1506d58: r1 = Function '<anonymous closure>':.
    //     0x1506d58: add             x1, PP, #0x34, lsl #12  ; [pp+0x34068] AnonymousClosure: (0x1506db0), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x1506d5c: ldr             x1, [x1, #0x68]
    // 0x1506d60: stur            x0, [fp, #-8]
    // 0x1506d64: r0 = AllocateClosure()
    //     0x1506d64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506d68: mov             x1, x0
    // 0x1506d6c: ldur            x0, [fp, #-8]
    // 0x1506d70: StoreField: r0->field_b = r1
    //     0x1506d70: stur            w1, [x0, #0xb]
    // 0x1506d74: r0 = WillPopScope()
    //     0x1506d74: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1506d78: mov             x3, x0
    // 0x1506d7c: ldur            x0, [fp, #-8]
    // 0x1506d80: stur            x3, [fp, #-0x18]
    // 0x1506d84: StoreField: r3->field_b = r0
    //     0x1506d84: stur            w0, [x3, #0xb]
    // 0x1506d88: ldur            x2, [fp, #-0x10]
    // 0x1506d8c: r1 = Function '<anonymous closure>':.
    //     0x1506d8c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34070] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x1506d90: ldr             x1, [x1, #0x70]
    // 0x1506d94: r0 = AllocateClosure()
    //     0x1506d94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506d98: mov             x1, x0
    // 0x1506d9c: ldur            x0, [fp, #-0x18]
    // 0x1506da0: StoreField: r0->field_f = r1
    //     0x1506da0: stur            w1, [x0, #0xf]
    // 0x1506da4: LeaveFrame
    //     0x1506da4: mov             SP, fp
    //     0x1506da8: ldp             fp, lr, [SP], #0x10
    // 0x1506dac: ret
    //     0x1506dac: ret             
  }
  [closure] ReplaceOrderWidget <anonymous closure>(dynamic) {
    // ** addr: 0x1506db0, size: 0x88
    // 0x1506db0: EnterFrame
    //     0x1506db0: stp             fp, lr, [SP, #-0x10]!
    //     0x1506db4: mov             fp, SP
    // 0x1506db8: AllocStack(0x10)
    //     0x1506db8: sub             SP, SP, #0x10
    // 0x1506dbc: SetupParameters()
    //     0x1506dbc: ldr             x0, [fp, #0x10]
    //     0x1506dc0: ldur            w2, [x0, #0x17]
    //     0x1506dc4: add             x2, x2, HEAP, lsl #32
    //     0x1506dc8: stur            x2, [fp, #-8]
    // 0x1506dcc: CheckStackOverflow
    //     0x1506dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506dd0: cmp             SP, x16
    //     0x1506dd4: b.ls            #0x1506e30
    // 0x1506dd8: LoadField: r1 = r2->field_f
    //     0x1506dd8: ldur            w1, [x2, #0xf]
    // 0x1506ddc: DecompressPointer r1
    //     0x1506ddc: add             x1, x1, HEAP, lsl #32
    // 0x1506de0: r0 = controller()
    //     0x1506de0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1506de4: mov             x1, x0
    // 0x1506de8: r0 = orderReturnCallNumber()
    //     0x1506de8: bl              #0x1441450  ; [package:customer_app/app/presentation/controllers/post_order/replace_call_order_controller.dart] ReplaceCallOrderController::orderReturnCallNumber
    // 0x1506dec: mov             x2, x0
    // 0x1506df0: ldur            x0, [fp, #-8]
    // 0x1506df4: stur            x2, [fp, #-0x10]
    // 0x1506df8: LoadField: r1 = r0->field_f
    //     0x1506df8: ldur            w1, [x0, #0xf]
    // 0x1506dfc: DecompressPointer r1
    //     0x1506dfc: add             x1, x1, HEAP, lsl #32
    // 0x1506e00: r0 = controller()
    //     0x1506e00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1506e04: mov             x1, x0
    // 0x1506e08: r0 = configData()
    //     0x1506e08: bl              #0x13187d8  ; [package:customer_app/app/presentation/controllers/browse/browse_controller.dart] BrowseController::configData
    // 0x1506e0c: stur            x0, [fp, #-8]
    // 0x1506e10: r0 = ReplaceOrderWidget()
    //     0x1506e10: bl              #0x1441488  ; AllocateReplaceOrderWidgetStub -> ReplaceOrderWidget (size=0x14)
    // 0x1506e14: ldur            x1, [fp, #-0x10]
    // 0x1506e18: StoreField: r0->field_b = r1
    //     0x1506e18: stur            w1, [x0, #0xb]
    // 0x1506e1c: ldur            x1, [fp, #-8]
    // 0x1506e20: StoreField: r0->field_f = r1
    //     0x1506e20: stur            w1, [x0, #0xf]
    // 0x1506e24: LeaveFrame
    //     0x1506e24: mov             SP, fp
    //     0x1506e28: ldp             fp, lr, [SP], #0x10
    // 0x1506e2c: ret
    //     0x1506e2c: ret             
    // 0x1506e30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1506e30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506e34: b               #0x1506dd8
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15ecaec, size: 0x1bc
    // 0x15ecaec: EnterFrame
    //     0x15ecaec: stp             fp, lr, [SP, #-0x10]!
    //     0x15ecaf0: mov             fp, SP
    // 0x15ecaf4: AllocStack(0x30)
    //     0x15ecaf4: sub             SP, SP, #0x30
    // 0x15ecaf8: SetupParameters(ReplaceCallOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15ecaf8: mov             x0, x1
    //     0x15ecafc: stur            x1, [fp, #-8]
    //     0x15ecb00: mov             x1, x2
    //     0x15ecb04: stur            x2, [fp, #-0x10]
    // 0x15ecb08: CheckStackOverflow
    //     0x15ecb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ecb0c: cmp             SP, x16
    //     0x15ecb10: b.ls            #0x15ecca0
    // 0x15ecb14: r1 = 1
    //     0x15ecb14: movz            x1, #0x1
    // 0x15ecb18: r0 = AllocateContext()
    //     0x15ecb18: bl              #0x16f6108  ; AllocateContextStub
    // 0x15ecb1c: mov             x2, x0
    // 0x15ecb20: ldur            x0, [fp, #-8]
    // 0x15ecb24: stur            x2, [fp, #-0x18]
    // 0x15ecb28: StoreField: r2->field_f = r0
    //     0x15ecb28: stur            w0, [x2, #0xf]
    // 0x15ecb2c: ldur            x1, [fp, #-0x10]
    // 0x15ecb30: r0 = of()
    //     0x15ecb30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ecb34: LoadField: r1 = r0->field_87
    //     0x15ecb34: ldur            w1, [x0, #0x87]
    // 0x15ecb38: DecompressPointer r1
    //     0x15ecb38: add             x1, x1, HEAP, lsl #32
    // 0x15ecb3c: LoadField: r0 = r1->field_2b
    //     0x15ecb3c: ldur            w0, [x1, #0x2b]
    // 0x15ecb40: DecompressPointer r0
    //     0x15ecb40: add             x0, x0, HEAP, lsl #32
    // 0x15ecb44: r16 = 16.000000
    //     0x15ecb44: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15ecb48: ldr             x16, [x16, #0x188]
    // 0x15ecb4c: r30 = Instance_Color
    //     0x15ecb4c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15ecb50: stp             lr, x16, [SP]
    // 0x15ecb54: mov             x1, x0
    // 0x15ecb58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15ecb58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15ecb5c: ldr             x4, [x4, #0xaa0]
    // 0x15ecb60: r0 = copyWith()
    //     0x15ecb60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15ecb64: stur            x0, [fp, #-8]
    // 0x15ecb68: r0 = Text()
    //     0x15ecb68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15ecb6c: mov             x2, x0
    // 0x15ecb70: r0 = "Return/Replace Order"
    //     0x15ecb70: add             x0, PP, #0x34, lsl #12  ; [pp+0x34078] "Return/Replace Order"
    //     0x15ecb74: ldr             x0, [x0, #0x78]
    // 0x15ecb78: stur            x2, [fp, #-0x20]
    // 0x15ecb7c: StoreField: r2->field_b = r0
    //     0x15ecb7c: stur            w0, [x2, #0xb]
    // 0x15ecb80: ldur            x0, [fp, #-8]
    // 0x15ecb84: StoreField: r2->field_13 = r0
    //     0x15ecb84: stur            w0, [x2, #0x13]
    // 0x15ecb88: ldur            x1, [fp, #-0x10]
    // 0x15ecb8c: r0 = of()
    //     0x15ecb8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ecb90: LoadField: r1 = r0->field_5b
    //     0x15ecb90: ldur            w1, [x0, #0x5b]
    // 0x15ecb94: DecompressPointer r1
    //     0x15ecb94: add             x1, x1, HEAP, lsl #32
    // 0x15ecb98: stur            x1, [fp, #-8]
    // 0x15ecb9c: r0 = ColorFilter()
    //     0x15ecb9c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ecba0: mov             x1, x0
    // 0x15ecba4: ldur            x0, [fp, #-8]
    // 0x15ecba8: stur            x1, [fp, #-0x10]
    // 0x15ecbac: StoreField: r1->field_7 = r0
    //     0x15ecbac: stur            w0, [x1, #7]
    // 0x15ecbb0: r0 = Instance_BlendMode
    //     0x15ecbb0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ecbb4: ldr             x0, [x0, #0xb30]
    // 0x15ecbb8: StoreField: r1->field_b = r0
    //     0x15ecbb8: stur            w0, [x1, #0xb]
    // 0x15ecbbc: r0 = 1
    //     0x15ecbbc: movz            x0, #0x1
    // 0x15ecbc0: StoreField: r1->field_13 = r0
    //     0x15ecbc0: stur            x0, [x1, #0x13]
    // 0x15ecbc4: r0 = SvgPicture()
    //     0x15ecbc4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ecbc8: stur            x0, [fp, #-8]
    // 0x15ecbcc: ldur            x16, [fp, #-0x10]
    // 0x15ecbd0: str             x16, [SP]
    // 0x15ecbd4: mov             x1, x0
    // 0x15ecbd8: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ecbd8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ecbdc: ldr             x2, [x2, #0xa40]
    // 0x15ecbe0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ecbe0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ecbe4: ldr             x4, [x4, #0xa38]
    // 0x15ecbe8: r0 = SvgPicture.asset()
    //     0x15ecbe8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ecbec: r0 = Align()
    //     0x15ecbec: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ecbf0: mov             x1, x0
    // 0x15ecbf4: r0 = Instance_Alignment
    //     0x15ecbf4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ecbf8: ldr             x0, [x0, #0xb10]
    // 0x15ecbfc: stur            x1, [fp, #-0x10]
    // 0x15ecc00: StoreField: r1->field_f = r0
    //     0x15ecc00: stur            w0, [x1, #0xf]
    // 0x15ecc04: r0 = 1.000000
    //     0x15ecc04: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ecc08: StoreField: r1->field_13 = r0
    //     0x15ecc08: stur            w0, [x1, #0x13]
    // 0x15ecc0c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ecc0c: stur            w0, [x1, #0x17]
    // 0x15ecc10: ldur            x0, [fp, #-8]
    // 0x15ecc14: StoreField: r1->field_b = r0
    //     0x15ecc14: stur            w0, [x1, #0xb]
    // 0x15ecc18: r0 = InkWell()
    //     0x15ecc18: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ecc1c: mov             x3, x0
    // 0x15ecc20: ldur            x0, [fp, #-0x10]
    // 0x15ecc24: stur            x3, [fp, #-8]
    // 0x15ecc28: StoreField: r3->field_b = r0
    //     0x15ecc28: stur            w0, [x3, #0xb]
    // 0x15ecc2c: ldur            x2, [fp, #-0x18]
    // 0x15ecc30: r1 = Function '<anonymous closure>':.
    //     0x15ecc30: add             x1, PP, #0x34, lsl #12  ; [pp+0x34080] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15ecc34: ldr             x1, [x1, #0x80]
    // 0x15ecc38: r0 = AllocateClosure()
    //     0x15ecc38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ecc3c: ldur            x2, [fp, #-8]
    // 0x15ecc40: StoreField: r2->field_f = r0
    //     0x15ecc40: stur            w0, [x2, #0xf]
    // 0x15ecc44: r0 = true
    //     0x15ecc44: add             x0, NULL, #0x20  ; true
    // 0x15ecc48: StoreField: r2->field_43 = r0
    //     0x15ecc48: stur            w0, [x2, #0x43]
    // 0x15ecc4c: r1 = Instance_BoxShape
    //     0x15ecc4c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ecc50: ldr             x1, [x1, #0x80]
    // 0x15ecc54: StoreField: r2->field_47 = r1
    //     0x15ecc54: stur            w1, [x2, #0x47]
    // 0x15ecc58: StoreField: r2->field_6f = r0
    //     0x15ecc58: stur            w0, [x2, #0x6f]
    // 0x15ecc5c: r1 = false
    //     0x15ecc5c: add             x1, NULL, #0x30  ; false
    // 0x15ecc60: StoreField: r2->field_73 = r1
    //     0x15ecc60: stur            w1, [x2, #0x73]
    // 0x15ecc64: StoreField: r2->field_83 = r0
    //     0x15ecc64: stur            w0, [x2, #0x83]
    // 0x15ecc68: StoreField: r2->field_7b = r1
    //     0x15ecc68: stur            w1, [x2, #0x7b]
    // 0x15ecc6c: r0 = AppBar()
    //     0x15ecc6c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ecc70: stur            x0, [fp, #-0x10]
    // 0x15ecc74: ldur            x16, [fp, #-0x20]
    // 0x15ecc78: str             x16, [SP]
    // 0x15ecc7c: mov             x1, x0
    // 0x15ecc80: ldur            x2, [fp, #-8]
    // 0x15ecc84: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15ecc84: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15ecc88: ldr             x4, [x4, #0xf00]
    // 0x15ecc8c: r0 = AppBar()
    //     0x15ecc8c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ecc90: ldur            x0, [fp, #-0x10]
    // 0x15ecc94: LeaveFrame
    //     0x15ecc94: mov             SP, fp
    //     0x15ecc98: ldp             fp, lr, [SP], #0x10
    // 0x15ecc9c: ret
    //     0x15ecc9c: ret             
    // 0x15ecca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ecca0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ecca4: b               #0x15ecb14
  }
}
