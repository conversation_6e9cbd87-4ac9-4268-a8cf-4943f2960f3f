// lib: , url: package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart

// class id: 1049159, size: 0x8
class :: {
}

// class id: 3514, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ProductGridItemViewState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ activate(/* No info */) {
    // ** addr: 0x7f4b14, size: 0x30
    // 0x7f4b14: EnterFrame
    //     0x7f4b14: stp             fp, lr, [SP, #-0x10]!
    //     0x7f4b18: mov             fp, SP
    // 0x7f4b1c: CheckStackOverflow
    //     0x7f4b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f4b20: cmp             SP, x16
    //     0x7f4b24: b.ls            #0x7f4b3c
    // 0x7f4b28: r0 = _updateTickerModeNotifier()
    //     0x7f4b28: bl              #0x7f4b68  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] __ProductGridItemViewState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f4b2c: r0 = Null
    //     0x7f4b2c: mov             x0, NULL
    // 0x7f4b30: LeaveFrame
    //     0x7f4b30: mov             SP, fp
    //     0x7f4b34: ldp             fp, lr, [SP], #0x10
    // 0x7f4b38: ret
    //     0x7f4b38: ret             
    // 0x7f4b3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f4b3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f4b40: b               #0x7f4b28
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x7f4b68, size: 0x124
    // 0x7f4b68: EnterFrame
    //     0x7f4b68: stp             fp, lr, [SP, #-0x10]!
    //     0x7f4b6c: mov             fp, SP
    // 0x7f4b70: AllocStack(0x18)
    //     0x7f4b70: sub             SP, SP, #0x18
    // 0x7f4b74: SetupParameters(__ProductGridItemViewState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x7f4b74: mov             x2, x1
    //     0x7f4b78: stur            x1, [fp, #-8]
    // 0x7f4b7c: CheckStackOverflow
    //     0x7f4b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f4b80: cmp             SP, x16
    //     0x7f4b84: b.ls            #0x7f4c80
    // 0x7f4b88: LoadField: r1 = r2->field_f
    //     0x7f4b88: ldur            w1, [x2, #0xf]
    // 0x7f4b8c: DecompressPointer r1
    //     0x7f4b8c: add             x1, x1, HEAP, lsl #32
    // 0x7f4b90: cmp             w1, NULL
    // 0x7f4b94: b.eq            #0x7f4c88
    // 0x7f4b98: r0 = getNotifier()
    //     0x7f4b98: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x7f4b9c: mov             x3, x0
    // 0x7f4ba0: ldur            x0, [fp, #-8]
    // 0x7f4ba4: stur            x3, [fp, #-0x18]
    // 0x7f4ba8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x7f4ba8: ldur            w4, [x0, #0x17]
    // 0x7f4bac: DecompressPointer r4
    //     0x7f4bac: add             x4, x4, HEAP, lsl #32
    // 0x7f4bb0: stur            x4, [fp, #-0x10]
    // 0x7f4bb4: cmp             w3, w4
    // 0x7f4bb8: b.ne            #0x7f4bcc
    // 0x7f4bbc: r0 = Null
    //     0x7f4bbc: mov             x0, NULL
    // 0x7f4bc0: LeaveFrame
    //     0x7f4bc0: mov             SP, fp
    //     0x7f4bc4: ldp             fp, lr, [SP], #0x10
    // 0x7f4bc8: ret
    //     0x7f4bc8: ret             
    // 0x7f4bcc: cmp             w4, NULL
    // 0x7f4bd0: b.eq            #0x7f4c14
    // 0x7f4bd4: mov             x2, x0
    // 0x7f4bd8: r1 = Function '_updateTicker@356311458':.
    //     0x7f4bd8: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab70] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f4bdc: ldr             x1, [x1, #0xb70]
    // 0x7f4be0: r0 = AllocateClosure()
    //     0x7f4be0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f4be4: ldur            x1, [fp, #-0x10]
    // 0x7f4be8: r2 = LoadClassIdInstr(r1)
    //     0x7f4be8: ldur            x2, [x1, #-1]
    //     0x7f4bec: ubfx            x2, x2, #0xc, #0x14
    // 0x7f4bf0: mov             x16, x0
    // 0x7f4bf4: mov             x0, x2
    // 0x7f4bf8: mov             x2, x16
    // 0x7f4bfc: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x7f4bfc: movz            x17, #0xdc2b
    //     0x7f4c00: add             lr, x0, x17
    //     0x7f4c04: ldr             lr, [x21, lr, lsl #3]
    //     0x7f4c08: blr             lr
    // 0x7f4c0c: ldur            x0, [fp, #-8]
    // 0x7f4c10: ldur            x3, [fp, #-0x18]
    // 0x7f4c14: mov             x2, x0
    // 0x7f4c18: r1 = Function '_updateTicker@356311458':.
    //     0x7f4c18: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab70] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f4c1c: ldr             x1, [x1, #0xb70]
    // 0x7f4c20: r0 = AllocateClosure()
    //     0x7f4c20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f4c24: ldur            x3, [fp, #-0x18]
    // 0x7f4c28: r1 = LoadClassIdInstr(r3)
    //     0x7f4c28: ldur            x1, [x3, #-1]
    //     0x7f4c2c: ubfx            x1, x1, #0xc, #0x14
    // 0x7f4c30: mov             x2, x0
    // 0x7f4c34: mov             x0, x1
    // 0x7f4c38: mov             x1, x3
    // 0x7f4c3c: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x7f4c3c: movz            x17, #0xdc71
    //     0x7f4c40: add             lr, x0, x17
    //     0x7f4c44: ldr             lr, [x21, lr, lsl #3]
    //     0x7f4c48: blr             lr
    // 0x7f4c4c: ldur            x0, [fp, #-0x18]
    // 0x7f4c50: ldur            x1, [fp, #-8]
    // 0x7f4c54: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f4c54: stur            w0, [x1, #0x17]
    //     0x7f4c58: ldurb           w16, [x1, #-1]
    //     0x7f4c5c: ldurb           w17, [x0, #-1]
    //     0x7f4c60: and             x16, x17, x16, lsr #2
    //     0x7f4c64: tst             x16, HEAP, lsr #32
    //     0x7f4c68: b.eq            #0x7f4c70
    //     0x7f4c6c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x7f4c70: r0 = Null
    //     0x7f4c70: mov             x0, NULL
    // 0x7f4c74: LeaveFrame
    //     0x7f4c74: mov             SP, fp
    //     0x7f4c78: ldp             fp, lr, [SP], #0x10
    // 0x7f4c7c: ret
    //     0x7f4c7c: ret             
    // 0x7f4c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f4c80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f4c84: b               #0x7f4b88
    // 0x7f4c88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f4c88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc86c88, size: 0x94
    // 0xc86c88: EnterFrame
    //     0xc86c88: stp             fp, lr, [SP, #-0x10]!
    //     0xc86c8c: mov             fp, SP
    // 0xc86c90: AllocStack(0x10)
    //     0xc86c90: sub             SP, SP, #0x10
    // 0xc86c94: SetupParameters(__ProductGridItemViewState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc86c94: mov             x0, x1
    //     0xc86c98: stur            x1, [fp, #-0x10]
    // 0xc86c9c: CheckStackOverflow
    //     0xc86c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc86ca0: cmp             SP, x16
    //     0xc86ca4: b.ls            #0xc86d14
    // 0xc86ca8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc86ca8: ldur            w3, [x0, #0x17]
    // 0xc86cac: DecompressPointer r3
    //     0xc86cac: add             x3, x3, HEAP, lsl #32
    // 0xc86cb0: stur            x3, [fp, #-8]
    // 0xc86cb4: cmp             w3, NULL
    // 0xc86cb8: b.ne            #0xc86cc4
    // 0xc86cbc: mov             x1, x0
    // 0xc86cc0: b               #0xc86d00
    // 0xc86cc4: mov             x2, x0
    // 0xc86cc8: r1 = Function '_updateTicker@356311458':.
    //     0xc86cc8: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab70] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc86ccc: ldr             x1, [x1, #0xb70]
    // 0xc86cd0: r0 = AllocateClosure()
    //     0xc86cd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc86cd4: ldur            x1, [fp, #-8]
    // 0xc86cd8: r2 = LoadClassIdInstr(r1)
    //     0xc86cd8: ldur            x2, [x1, #-1]
    //     0xc86cdc: ubfx            x2, x2, #0xc, #0x14
    // 0xc86ce0: mov             x16, x0
    // 0xc86ce4: mov             x0, x2
    // 0xc86ce8: mov             x2, x16
    // 0xc86cec: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc86cec: movz            x17, #0xdc2b
    //     0xc86cf0: add             lr, x0, x17
    //     0xc86cf4: ldr             lr, [x21, lr, lsl #3]
    //     0xc86cf8: blr             lr
    // 0xc86cfc: ldur            x1, [fp, #-0x10]
    // 0xc86d00: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc86d00: stur            NULL, [x1, #0x17]
    // 0xc86d04: r0 = Null
    //     0xc86d04: mov             x0, NULL
    // 0xc86d08: LeaveFrame
    //     0xc86d08: mov             SP, fp
    //     0xc86d0c: ldp             fp, lr, [SP], #0x10
    // 0xc86d10: ret
    //     0xc86d10: ret             
    // 0xc86d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc86d14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc86d18: b               #0xc86ca8
  }
}

// class id: 3515, size: 0x28, field offset: 0x1c
class _ProductGridItemViewState extends __ProductGridItemViewState&State&SingleTickerProviderStateMixin {

  late PageController _pageController; // offset: 0x1c

  _ build(/* No info */) {
    // ** addr: 0xa4b904, size: 0x1d2c
    // 0xa4b904: EnterFrame
    //     0xa4b904: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b908: mov             fp, SP
    // 0xa4b90c: AllocStack(0xb8)
    //     0xa4b90c: sub             SP, SP, #0xb8
    // 0xa4b910: SetupParameters(_ProductGridItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa4b910: mov             x0, x1
    //     0xa4b914: stur            x1, [fp, #-8]
    //     0xa4b918: mov             x1, x2
    //     0xa4b91c: stur            x2, [fp, #-0x10]
    // 0xa4b920: CheckStackOverflow
    //     0xa4b920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4b924: cmp             SP, x16
    //     0xa4b928: b.ls            #0xa4d5f0
    // 0xa4b92c: r1 = 1
    //     0xa4b92c: movz            x1, #0x1
    // 0xa4b930: r0 = AllocateContext()
    //     0xa4b930: bl              #0x16f6108  ; AllocateContextStub
    // 0xa4b934: mov             x3, x0
    // 0xa4b938: ldur            x0, [fp, #-8]
    // 0xa4b93c: stur            x3, [fp, #-0x28]
    // 0xa4b940: StoreField: r3->field_f = r0
    //     0xa4b940: stur            w0, [x3, #0xf]
    // 0xa4b944: LoadField: r1 = r0->field_b
    //     0xa4b944: ldur            w1, [x0, #0xb]
    // 0xa4b948: DecompressPointer r1
    //     0xa4b948: add             x1, x1, HEAP, lsl #32
    // 0xa4b94c: cmp             w1, NULL
    // 0xa4b950: b.eq            #0xa4d5f8
    // 0xa4b954: LoadField: r2 = r1->field_1b
    //     0xa4b954: ldur            w2, [x1, #0x1b]
    // 0xa4b958: DecompressPointer r2
    //     0xa4b958: add             x2, x2, HEAP, lsl #32
    // 0xa4b95c: tbnz            w2, #4, #0xa4b96c
    // 0xa4b960: r4 = Instance_MainAxisAlignment
    //     0xa4b960: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa4b964: ldr             x4, [x4, #0xa08]
    // 0xa4b968: b               #0xa4b974
    // 0xa4b96c: r4 = Instance_MainAxisAlignment
    //     0xa4b96c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xa4b970: ldr             x4, [x4, #0xab0]
    // 0xa4b974: stur            x4, [fp, #-0x20]
    // 0xa4b978: LoadField: r2 = r1->field_23
    //     0xa4b978: ldur            w2, [x1, #0x23]
    // 0xa4b97c: DecompressPointer r2
    //     0xa4b97c: add             x2, x2, HEAP, lsl #32
    // 0xa4b980: LoadField: r1 = r2->field_7
    //     0xa4b980: ldur            w1, [x2, #7]
    // 0xa4b984: DecompressPointer r1
    //     0xa4b984: add             x1, x1, HEAP, lsl #32
    // 0xa4b988: cmp             w1, NULL
    // 0xa4b98c: b.ne            #0xa4b998
    // 0xa4b990: r1 = Instance_TitleAlignment
    //     0xa4b990: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xa4b994: ldr             x1, [x1, #0x518]
    // 0xa4b998: r16 = Instance_TitleAlignment
    //     0xa4b998: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xa4b99c: ldr             x16, [x16, #0x520]
    // 0xa4b9a0: cmp             w1, w16
    // 0xa4b9a4: b.ne            #0xa4b9b4
    // 0xa4b9a8: r5 = Instance_CrossAxisAlignment
    //     0xa4b9a8: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xa4b9ac: ldr             x5, [x5, #0xc68]
    // 0xa4b9b0: b               #0xa4b9d8
    // 0xa4b9b4: r16 = Instance_TitleAlignment
    //     0xa4b9b4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xa4b9b8: ldr             x16, [x16, #0x518]
    // 0xa4b9bc: cmp             w1, w16
    // 0xa4b9c0: b.ne            #0xa4b9d0
    // 0xa4b9c4: r5 = Instance_CrossAxisAlignment
    //     0xa4b9c4: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa4b9c8: ldr             x5, [x5, #0x890]
    // 0xa4b9cc: b               #0xa4b9d8
    // 0xa4b9d0: r5 = Instance_CrossAxisAlignment
    //     0xa4b9d0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4b9d4: ldr             x5, [x5, #0xa18]
    // 0xa4b9d8: stur            x5, [fp, #-0x18]
    // 0xa4b9dc: r1 = <Widget>
    //     0xa4b9dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4b9e0: r2 = 0
    //     0xa4b9e0: movz            x2, #0
    // 0xa4b9e4: r0 = _GrowableList()
    //     0xa4b9e4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa4b9e8: mov             x2, x0
    // 0xa4b9ec: ldur            x0, [fp, #-8]
    // 0xa4b9f0: stur            x2, [fp, #-0x38]
    // 0xa4b9f4: LoadField: r1 = r0->field_b
    //     0xa4b9f4: ldur            w1, [x0, #0xb]
    // 0xa4b9f8: DecompressPointer r1
    //     0xa4b9f8: add             x1, x1, HEAP, lsl #32
    // 0xa4b9fc: cmp             w1, NULL
    // 0xa4ba00: b.eq            #0xa4d5fc
    // 0xa4ba04: LoadField: r3 = r1->field_23
    //     0xa4ba04: ldur            w3, [x1, #0x23]
    // 0xa4ba08: DecompressPointer r3
    //     0xa4ba08: add             x3, x3, HEAP, lsl #32
    // 0xa4ba0c: LoadField: r4 = r3->field_7
    //     0xa4ba0c: ldur            w4, [x3, #7]
    // 0xa4ba10: DecompressPointer r4
    //     0xa4ba10: add             x4, x4, HEAP, lsl #32
    // 0xa4ba14: r16 = Instance_TitleAlignment
    //     0xa4ba14: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xa4ba18: ldr             x16, [x16, #0x518]
    // 0xa4ba1c: cmp             w4, w16
    // 0xa4ba20: b.ne            #0xa4c068
    // 0xa4ba24: LoadField: r3 = r1->field_f
    //     0xa4ba24: ldur            w3, [x1, #0xf]
    // 0xa4ba28: DecompressPointer r3
    //     0xa4ba28: add             x3, x3, HEAP, lsl #32
    // 0xa4ba2c: cmp             w3, NULL
    // 0xa4ba30: b.ne            #0xa4ba3c
    // 0xa4ba34: r1 = Null
    //     0xa4ba34: mov             x1, NULL
    // 0xa4ba38: b               #0xa4ba54
    // 0xa4ba3c: LoadField: r1 = r3->field_7
    //     0xa4ba3c: ldur            w1, [x3, #7]
    // 0xa4ba40: cbnz            w1, #0xa4ba4c
    // 0xa4ba44: r4 = false
    //     0xa4ba44: add             x4, NULL, #0x30  ; false
    // 0xa4ba48: b               #0xa4ba50
    // 0xa4ba4c: r4 = true
    //     0xa4ba4c: add             x4, NULL, #0x20  ; true
    // 0xa4ba50: mov             x1, x4
    // 0xa4ba54: cmp             w1, NULL
    // 0xa4ba58: b.eq            #0xa4c004
    // 0xa4ba5c: tbnz            w1, #4, #0xa4c004
    // 0xa4ba60: cmp             w3, NULL
    // 0xa4ba64: b.ne            #0xa4ba6c
    // 0xa4ba68: r3 = ""
    //     0xa4ba68: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4ba6c: ldur            x1, [fp, #-0x10]
    // 0xa4ba70: stur            x3, [fp, #-0x30]
    // 0xa4ba74: r0 = of()
    //     0xa4ba74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4ba78: LoadField: r1 = r0->field_87
    //     0xa4ba78: ldur            w1, [x0, #0x87]
    // 0xa4ba7c: DecompressPointer r1
    //     0xa4ba7c: add             x1, x1, HEAP, lsl #32
    // 0xa4ba80: LoadField: r0 = r1->field_7
    //     0xa4ba80: ldur            w0, [x1, #7]
    // 0xa4ba84: DecompressPointer r0
    //     0xa4ba84: add             x0, x0, HEAP, lsl #32
    // 0xa4ba88: stur            x0, [fp, #-0x40]
    // 0xa4ba8c: r1 = Instance_Color
    //     0xa4ba8c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4ba90: d0 = 0.700000
    //     0xa4ba90: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4ba94: ldr             d0, [x17, #0xf48]
    // 0xa4ba98: r0 = withOpacity()
    //     0xa4ba98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4ba9c: r16 = 21.000000
    //     0xa4ba9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa4baa0: ldr             x16, [x16, #0x9b0]
    // 0xa4baa4: stp             x0, x16, [SP]
    // 0xa4baa8: ldur            x1, [fp, #-0x40]
    // 0xa4baac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4baac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4bab0: ldr             x4, [x4, #0xaa0]
    // 0xa4bab4: r0 = copyWith()
    //     0xa4bab4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4bab8: stur            x0, [fp, #-0x40]
    // 0xa4babc: r0 = Text()
    //     0xa4babc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4bac0: mov             x2, x0
    // 0xa4bac4: ldur            x0, [fp, #-0x30]
    // 0xa4bac8: stur            x2, [fp, #-0x48]
    // 0xa4bacc: StoreField: r2->field_b = r0
    //     0xa4bacc: stur            w0, [x2, #0xb]
    // 0xa4bad0: ldur            x0, [fp, #-0x40]
    // 0xa4bad4: StoreField: r2->field_13 = r0
    //     0xa4bad4: stur            w0, [x2, #0x13]
    // 0xa4bad8: r1 = <FlexParentData>
    //     0xa4bad8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa4badc: ldr             x1, [x1, #0xe00]
    // 0xa4bae0: r0 = Expanded()
    //     0xa4bae0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa4bae4: mov             x3, x0
    // 0xa4bae8: r0 = 1
    //     0xa4bae8: movz            x0, #0x1
    // 0xa4baec: stur            x3, [fp, #-0x30]
    // 0xa4baf0: StoreField: r3->field_13 = r0
    //     0xa4baf0: stur            x0, [x3, #0x13]
    // 0xa4baf4: r0 = Instance_FlexFit
    //     0xa4baf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa4baf8: ldr             x0, [x0, #0xe08]
    // 0xa4bafc: StoreField: r3->field_1b = r0
    //     0xa4bafc: stur            w0, [x3, #0x1b]
    // 0xa4bb00: ldur            x0, [fp, #-0x48]
    // 0xa4bb04: StoreField: r3->field_b = r0
    //     0xa4bb04: stur            w0, [x3, #0xb]
    // 0xa4bb08: r1 = Null
    //     0xa4bb08: mov             x1, NULL
    // 0xa4bb0c: r2 = 2
    //     0xa4bb0c: movz            x2, #0x2
    // 0xa4bb10: r0 = AllocateArray()
    //     0xa4bb10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4bb14: mov             x2, x0
    // 0xa4bb18: ldur            x0, [fp, #-0x30]
    // 0xa4bb1c: stur            x2, [fp, #-0x40]
    // 0xa4bb20: StoreField: r2->field_f = r0
    //     0xa4bb20: stur            w0, [x2, #0xf]
    // 0xa4bb24: r1 = <Widget>
    //     0xa4bb24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4bb28: r0 = AllocateGrowableArray()
    //     0xa4bb28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4bb2c: mov             x2, x0
    // 0xa4bb30: ldur            x0, [fp, #-0x40]
    // 0xa4bb34: stur            x2, [fp, #-0x48]
    // 0xa4bb38: StoreField: r2->field_f = r0
    //     0xa4bb38: stur            w0, [x2, #0xf]
    // 0xa4bb3c: r0 = 2
    //     0xa4bb3c: movz            x0, #0x2
    // 0xa4bb40: StoreField: r2->field_b = r0
    //     0xa4bb40: stur            w0, [x2, #0xb]
    // 0xa4bb44: ldur            x3, [fp, #-8]
    // 0xa4bb48: LoadField: r1 = r3->field_b
    //     0xa4bb48: ldur            w1, [x3, #0xb]
    // 0xa4bb4c: DecompressPointer r1
    //     0xa4bb4c: add             x1, x1, HEAP, lsl #32
    // 0xa4bb50: cmp             w1, NULL
    // 0xa4bb54: b.eq            #0xa4d600
    // 0xa4bb58: LoadField: r4 = r1->field_13
    //     0xa4bb58: ldur            w4, [x1, #0x13]
    // 0xa4bb5c: DecompressPointer r4
    //     0xa4bb5c: add             x4, x4, HEAP, lsl #32
    // 0xa4bb60: tbnz            w4, #4, #0xa4bed8
    // 0xa4bb64: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4bb64: ldur            w4, [x1, #0x17]
    // 0xa4bb68: DecompressPointer r4
    //     0xa4bb68: add             x4, x4, HEAP, lsl #32
    // 0xa4bb6c: cmp             w4, NULL
    // 0xa4bb70: b.ne            #0xa4bb7c
    // 0xa4bb74: r1 = Null
    //     0xa4bb74: mov             x1, NULL
    // 0xa4bb78: b               #0xa4bba8
    // 0xa4bb7c: LoadField: r1 = r4->field_7
    //     0xa4bb7c: ldur            w1, [x4, #7]
    // 0xa4bb80: DecompressPointer r1
    //     0xa4bb80: add             x1, x1, HEAP, lsl #32
    // 0xa4bb84: cmp             w1, NULL
    // 0xa4bb88: b.ne            #0xa4bb94
    // 0xa4bb8c: r1 = Null
    //     0xa4bb8c: mov             x1, NULL
    // 0xa4bb90: b               #0xa4bba8
    // 0xa4bb94: LoadField: r5 = r1->field_7
    //     0xa4bb94: ldur            w5, [x1, #7]
    // 0xa4bb98: cbnz            w5, #0xa4bba4
    // 0xa4bb9c: r1 = false
    //     0xa4bb9c: add             x1, NULL, #0x30  ; false
    // 0xa4bba0: b               #0xa4bba8
    // 0xa4bba4: r1 = true
    //     0xa4bba4: add             x1, NULL, #0x20  ; true
    // 0xa4bba8: cmp             w1, NULL
    // 0xa4bbac: b.eq            #0xa4bed8
    // 0xa4bbb0: tbnz            w1, #4, #0xa4bed8
    // 0xa4bbb4: cmp             w4, NULL
    // 0xa4bbb8: b.ne            #0xa4bbc4
    // 0xa4bbbc: r1 = Null
    //     0xa4bbbc: mov             x1, NULL
    // 0xa4bbc0: b               #0xa4bbf0
    // 0xa4bbc4: LoadField: r1 = r4->field_7
    //     0xa4bbc4: ldur            w1, [x4, #7]
    // 0xa4bbc8: DecompressPointer r1
    //     0xa4bbc8: add             x1, x1, HEAP, lsl #32
    // 0xa4bbcc: cmp             w1, NULL
    // 0xa4bbd0: b.ne            #0xa4bbdc
    // 0xa4bbd4: r1 = Null
    //     0xa4bbd4: mov             x1, NULL
    // 0xa4bbd8: b               #0xa4bbf0
    // 0xa4bbdc: LoadField: r4 = r1->field_7
    //     0xa4bbdc: ldur            w4, [x1, #7]
    // 0xa4bbe0: cbnz            w4, #0xa4bbec
    // 0xa4bbe4: r1 = false
    //     0xa4bbe4: add             x1, NULL, #0x30  ; false
    // 0xa4bbe8: b               #0xa4bbf0
    // 0xa4bbec: r1 = true
    //     0xa4bbec: add             x1, NULL, #0x20  ; true
    // 0xa4bbf0: cmp             w1, NULL
    // 0xa4bbf4: b.ne            #0xa4bc00
    // 0xa4bbf8: r4 = false
    //     0xa4bbf8: add             x4, NULL, #0x30  ; false
    // 0xa4bbfc: b               #0xa4bc04
    // 0xa4bc00: mov             x4, x1
    // 0xa4bc04: ldur            x1, [fp, #-0x10]
    // 0xa4bc08: stur            x4, [fp, #-0x30]
    // 0xa4bc0c: r0 = of()
    //     0xa4bc0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4bc10: LoadField: r2 = r0->field_5b
    //     0xa4bc10: ldur            w2, [x0, #0x5b]
    // 0xa4bc14: DecompressPointer r2
    //     0xa4bc14: add             x2, x2, HEAP, lsl #32
    // 0xa4bc18: r1 = Null
    //     0xa4bc18: mov             x1, NULL
    // 0xa4bc1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4bc1c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4bc20: r0 = Border.all()
    //     0xa4bc20: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa4bc24: stur            x0, [fp, #-0x40]
    // 0xa4bc28: r0 = BoxDecoration()
    //     0xa4bc28: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4bc2c: mov             x2, x0
    // 0xa4bc30: ldur            x0, [fp, #-0x40]
    // 0xa4bc34: stur            x2, [fp, #-0x50]
    // 0xa4bc38: StoreField: r2->field_f = r0
    //     0xa4bc38: stur            w0, [x2, #0xf]
    // 0xa4bc3c: r0 = Instance_BorderRadius
    //     0xa4bc3c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa4bc40: ldr             x0, [x0, #0xbe8]
    // 0xa4bc44: StoreField: r2->field_13 = r0
    //     0xa4bc44: stur            w0, [x2, #0x13]
    // 0xa4bc48: r0 = Instance_BoxShape
    //     0xa4bc48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4bc4c: ldr             x0, [x0, #0x80]
    // 0xa4bc50: StoreField: r2->field_23 = r0
    //     0xa4bc50: stur            w0, [x2, #0x23]
    // 0xa4bc54: ldur            x3, [fp, #-8]
    // 0xa4bc58: LoadField: r1 = r3->field_b
    //     0xa4bc58: ldur            w1, [x3, #0xb]
    // 0xa4bc5c: DecompressPointer r1
    //     0xa4bc5c: add             x1, x1, HEAP, lsl #32
    // 0xa4bc60: cmp             w1, NULL
    // 0xa4bc64: b.eq            #0xa4d604
    // 0xa4bc68: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4bc68: ldur            w4, [x1, #0x17]
    // 0xa4bc6c: DecompressPointer r4
    //     0xa4bc6c: add             x4, x4, HEAP, lsl #32
    // 0xa4bc70: cmp             w4, NULL
    // 0xa4bc74: b.ne            #0xa4bc80
    // 0xa4bc78: r1 = Null
    //     0xa4bc78: mov             x1, NULL
    // 0xa4bc7c: b               #0xa4bc88
    // 0xa4bc80: LoadField: r1 = r4->field_7
    //     0xa4bc80: ldur            w1, [x4, #7]
    // 0xa4bc84: DecompressPointer r1
    //     0xa4bc84: add             x1, x1, HEAP, lsl #32
    // 0xa4bc88: cmp             w1, NULL
    // 0xa4bc8c: b.ne            #0xa4bc98
    // 0xa4bc90: r6 = ""
    //     0xa4bc90: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4bc94: b               #0xa4bc9c
    // 0xa4bc98: mov             x6, x1
    // 0xa4bc9c: ldur            x5, [fp, #-0x30]
    // 0xa4bca0: ldur            x4, [fp, #-0x48]
    // 0xa4bca4: ldur            x1, [fp, #-0x10]
    // 0xa4bca8: stur            x6, [fp, #-0x40]
    // 0xa4bcac: r0 = of()
    //     0xa4bcac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4bcb0: LoadField: r1 = r0->field_87
    //     0xa4bcb0: ldur            w1, [x0, #0x87]
    // 0xa4bcb4: DecompressPointer r1
    //     0xa4bcb4: add             x1, x1, HEAP, lsl #32
    // 0xa4bcb8: LoadField: r0 = r1->field_7
    //     0xa4bcb8: ldur            w0, [x1, #7]
    // 0xa4bcbc: DecompressPointer r0
    //     0xa4bcbc: add             x0, x0, HEAP, lsl #32
    // 0xa4bcc0: ldur            x1, [fp, #-0x10]
    // 0xa4bcc4: stur            x0, [fp, #-0x58]
    // 0xa4bcc8: r0 = of()
    //     0xa4bcc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4bccc: LoadField: r1 = r0->field_5b
    //     0xa4bccc: ldur            w1, [x0, #0x5b]
    // 0xa4bcd0: DecompressPointer r1
    //     0xa4bcd0: add             x1, x1, HEAP, lsl #32
    // 0xa4bcd4: r0 = LoadClassIdInstr(r1)
    //     0xa4bcd4: ldur            x0, [x1, #-1]
    //     0xa4bcd8: ubfx            x0, x0, #0xc, #0x14
    // 0xa4bcdc: d0 = 0.700000
    //     0xa4bcdc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4bce0: ldr             d0, [x17, #0xf48]
    // 0xa4bce4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa4bce4: sub             lr, x0, #0xffa
    //     0xa4bce8: ldr             lr, [x21, lr, lsl #3]
    //     0xa4bcec: blr             lr
    // 0xa4bcf0: r16 = 14.000000
    //     0xa4bcf0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4bcf4: ldr             x16, [x16, #0x1d8]
    // 0xa4bcf8: stp             x0, x16, [SP]
    // 0xa4bcfc: ldur            x1, [fp, #-0x58]
    // 0xa4bd00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4bd00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4bd04: ldr             x4, [x4, #0xaa0]
    // 0xa4bd08: r0 = copyWith()
    //     0xa4bd08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4bd0c: stur            x0, [fp, #-0x58]
    // 0xa4bd10: r0 = Text()
    //     0xa4bd10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4bd14: mov             x1, x0
    // 0xa4bd18: ldur            x0, [fp, #-0x40]
    // 0xa4bd1c: stur            x1, [fp, #-0x60]
    // 0xa4bd20: StoreField: r1->field_b = r0
    //     0xa4bd20: stur            w0, [x1, #0xb]
    // 0xa4bd24: ldur            x0, [fp, #-0x58]
    // 0xa4bd28: StoreField: r1->field_13 = r0
    //     0xa4bd28: stur            w0, [x1, #0x13]
    // 0xa4bd2c: r0 = Center()
    //     0xa4bd2c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa4bd30: r2 = Instance_Alignment
    //     0xa4bd30: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa4bd34: ldr             x2, [x2, #0xb10]
    // 0xa4bd38: stur            x0, [fp, #-0x40]
    // 0xa4bd3c: StoreField: r0->field_f = r2
    //     0xa4bd3c: stur            w2, [x0, #0xf]
    // 0xa4bd40: ldur            x1, [fp, #-0x60]
    // 0xa4bd44: StoreField: r0->field_b = r1
    //     0xa4bd44: stur            w1, [x0, #0xb]
    // 0xa4bd48: r0 = Padding()
    //     0xa4bd48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4bd4c: r3 = Instance_EdgeInsets
    //     0xa4bd4c: add             x3, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa4bd50: ldr             x3, [x3, #0x898]
    // 0xa4bd54: stur            x0, [fp, #-0x58]
    // 0xa4bd58: StoreField: r0->field_f = r3
    //     0xa4bd58: stur            w3, [x0, #0xf]
    // 0xa4bd5c: ldur            x1, [fp, #-0x40]
    // 0xa4bd60: StoreField: r0->field_b = r1
    //     0xa4bd60: stur            w1, [x0, #0xb]
    // 0xa4bd64: r0 = Container()
    //     0xa4bd64: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4bd68: stur            x0, [fp, #-0x40]
    // 0xa4bd6c: r16 = 32.000000
    //     0xa4bd6c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa4bd70: ldr             x16, [x16, #0x848]
    // 0xa4bd74: r30 = 90.000000
    //     0xa4bd74: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa4bd78: ldr             lr, [lr, #0xe68]
    // 0xa4bd7c: stp             lr, x16, [SP, #0x10]
    // 0xa4bd80: ldur            x16, [fp, #-0x50]
    // 0xa4bd84: ldur            lr, [fp, #-0x58]
    // 0xa4bd88: stp             lr, x16, [SP]
    // 0xa4bd8c: mov             x1, x0
    // 0xa4bd90: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa4bd90: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa4bd94: ldr             x4, [x4, #0x8c0]
    // 0xa4bd98: r0 = Container()
    //     0xa4bd98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4bd9c: r0 = Padding()
    //     0xa4bd9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4bda0: mov             x1, x0
    // 0xa4bda4: r0 = Instance_EdgeInsets
    //     0xa4bda4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xa4bda8: ldr             x0, [x0, #0xd48]
    // 0xa4bdac: stur            x1, [fp, #-0x50]
    // 0xa4bdb0: StoreField: r1->field_f = r0
    //     0xa4bdb0: stur            w0, [x1, #0xf]
    // 0xa4bdb4: ldur            x0, [fp, #-0x40]
    // 0xa4bdb8: StoreField: r1->field_b = r0
    //     0xa4bdb8: stur            w0, [x1, #0xb]
    // 0xa4bdbc: r0 = Visibility()
    //     0xa4bdbc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4bdc0: mov             x1, x0
    // 0xa4bdc4: ldur            x0, [fp, #-0x50]
    // 0xa4bdc8: stur            x1, [fp, #-0x40]
    // 0xa4bdcc: StoreField: r1->field_b = r0
    //     0xa4bdcc: stur            w0, [x1, #0xb]
    // 0xa4bdd0: r0 = Instance_SizedBox
    //     0xa4bdd0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4bdd4: StoreField: r1->field_f = r0
    //     0xa4bdd4: stur            w0, [x1, #0xf]
    // 0xa4bdd8: ldur            x2, [fp, #-0x30]
    // 0xa4bddc: StoreField: r1->field_13 = r2
    //     0xa4bddc: stur            w2, [x1, #0x13]
    // 0xa4bde0: r2 = false
    //     0xa4bde0: add             x2, NULL, #0x30  ; false
    // 0xa4bde4: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4bde4: stur            w2, [x1, #0x17]
    // 0xa4bde8: StoreField: r1->field_1b = r2
    //     0xa4bde8: stur            w2, [x1, #0x1b]
    // 0xa4bdec: StoreField: r1->field_1f = r2
    //     0xa4bdec: stur            w2, [x1, #0x1f]
    // 0xa4bdf0: StoreField: r1->field_23 = r2
    //     0xa4bdf0: stur            w2, [x1, #0x23]
    // 0xa4bdf4: StoreField: r1->field_27 = r2
    //     0xa4bdf4: stur            w2, [x1, #0x27]
    // 0xa4bdf8: StoreField: r1->field_2b = r2
    //     0xa4bdf8: stur            w2, [x1, #0x2b]
    // 0xa4bdfc: r0 = InkWell()
    //     0xa4bdfc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa4be00: mov             x3, x0
    // 0xa4be04: ldur            x0, [fp, #-0x40]
    // 0xa4be08: stur            x3, [fp, #-0x30]
    // 0xa4be0c: StoreField: r3->field_b = r0
    //     0xa4be0c: stur            w0, [x3, #0xb]
    // 0xa4be10: ldur            x2, [fp, #-0x28]
    // 0xa4be14: r1 = Function '<anonymous closure>':.
    //     0xa4be14: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aa68] AnonymousClosure: (0xa549e8), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4be18: ldr             x1, [x1, #0xa68]
    // 0xa4be1c: r0 = AllocateClosure()
    //     0xa4be1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4be20: mov             x1, x0
    // 0xa4be24: ldur            x0, [fp, #-0x30]
    // 0xa4be28: StoreField: r0->field_f = r1
    //     0xa4be28: stur            w1, [x0, #0xf]
    // 0xa4be2c: r5 = true
    //     0xa4be2c: add             x5, NULL, #0x20  ; true
    // 0xa4be30: StoreField: r0->field_43 = r5
    //     0xa4be30: stur            w5, [x0, #0x43]
    // 0xa4be34: r2 = Instance_BoxShape
    //     0xa4be34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4be38: ldr             x2, [x2, #0x80]
    // 0xa4be3c: StoreField: r0->field_47 = r2
    //     0xa4be3c: stur            w2, [x0, #0x47]
    // 0xa4be40: StoreField: r0->field_6f = r5
    //     0xa4be40: stur            w5, [x0, #0x6f]
    // 0xa4be44: r3 = false
    //     0xa4be44: add             x3, NULL, #0x30  ; false
    // 0xa4be48: StoreField: r0->field_73 = r3
    //     0xa4be48: stur            w3, [x0, #0x73]
    // 0xa4be4c: StoreField: r0->field_83 = r5
    //     0xa4be4c: stur            w5, [x0, #0x83]
    // 0xa4be50: StoreField: r0->field_7b = r3
    //     0xa4be50: stur            w3, [x0, #0x7b]
    // 0xa4be54: ldur            x4, [fp, #-0x48]
    // 0xa4be58: LoadField: r1 = r4->field_b
    //     0xa4be58: ldur            w1, [x4, #0xb]
    // 0xa4be5c: LoadField: r5 = r4->field_f
    //     0xa4be5c: ldur            w5, [x4, #0xf]
    // 0xa4be60: DecompressPointer r5
    //     0xa4be60: add             x5, x5, HEAP, lsl #32
    // 0xa4be64: LoadField: r6 = r5->field_b
    //     0xa4be64: ldur            w6, [x5, #0xb]
    // 0xa4be68: r5 = LoadInt32Instr(r1)
    //     0xa4be68: sbfx            x5, x1, #1, #0x1f
    // 0xa4be6c: stur            x5, [fp, #-0x68]
    // 0xa4be70: r1 = LoadInt32Instr(r6)
    //     0xa4be70: sbfx            x1, x6, #1, #0x1f
    // 0xa4be74: cmp             x5, x1
    // 0xa4be78: b.ne            #0xa4be84
    // 0xa4be7c: mov             x1, x4
    // 0xa4be80: r0 = _growToNextCapacity()
    //     0xa4be80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4be84: ldur            x2, [fp, #-0x48]
    // 0xa4be88: ldur            x3, [fp, #-0x68]
    // 0xa4be8c: add             x0, x3, #1
    // 0xa4be90: lsl             x1, x0, #1
    // 0xa4be94: StoreField: r2->field_b = r1
    //     0xa4be94: stur            w1, [x2, #0xb]
    // 0xa4be98: LoadField: r1 = r2->field_f
    //     0xa4be98: ldur            w1, [x2, #0xf]
    // 0xa4be9c: DecompressPointer r1
    //     0xa4be9c: add             x1, x1, HEAP, lsl #32
    // 0xa4bea0: ldur            x0, [fp, #-0x30]
    // 0xa4bea4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4bea4: add             x25, x1, x3, lsl #2
    //     0xa4bea8: add             x25, x25, #0xf
    //     0xa4beac: str             w0, [x25]
    //     0xa4beb0: tbz             w0, #0, #0xa4becc
    //     0xa4beb4: ldurb           w16, [x1, #-1]
    //     0xa4beb8: ldurb           w17, [x0, #-1]
    //     0xa4bebc: and             x16, x17, x16, lsr #2
    //     0xa4bec0: tst             x16, HEAP, lsr #32
    //     0xa4bec4: b.eq            #0xa4becc
    //     0xa4bec8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4becc: mov             x0, x2
    // 0xa4bed0: r2 = 4
    //     0xa4bed0: movz            x2, #0x4
    // 0xa4bed4: b               #0xa4befc
    // 0xa4bed8: mov             x1, x2
    // 0xa4bedc: r0 = _growToNextCapacity()
    //     0xa4bedc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4bee0: ldur            x0, [fp, #-0x48]
    // 0xa4bee4: r2 = 4
    //     0xa4bee4: movz            x2, #0x4
    // 0xa4bee8: StoreField: r0->field_b = r2
    //     0xa4bee8: stur            w2, [x0, #0xb]
    // 0xa4beec: LoadField: r1 = r0->field_f
    //     0xa4beec: ldur            w1, [x0, #0xf]
    // 0xa4bef0: DecompressPointer r1
    //     0xa4bef0: add             x1, x1, HEAP, lsl #32
    // 0xa4bef4: r16 = Instance_SizedBox
    //     0xa4bef4: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4bef8: StoreField: r1->field_13 = r16
    //     0xa4bef8: stur            w16, [x1, #0x13]
    // 0xa4befc: ldur            x1, [fp, #-0x38]
    // 0xa4bf00: r0 = Row()
    //     0xa4bf00: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4bf04: mov             x1, x0
    // 0xa4bf08: r0 = Instance_Axis
    //     0xa4bf08: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa4bf0c: stur            x1, [fp, #-0x30]
    // 0xa4bf10: StoreField: r1->field_f = r0
    //     0xa4bf10: stur            w0, [x1, #0xf]
    // 0xa4bf14: r6 = Instance_MainAxisAlignment
    //     0xa4bf14: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa4bf18: ldr             x6, [x6, #0xd10]
    // 0xa4bf1c: StoreField: r1->field_13 = r6
    //     0xa4bf1c: stur            w6, [x1, #0x13]
    // 0xa4bf20: r2 = Instance_MainAxisSize
    //     0xa4bf20: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4bf24: ldr             x2, [x2, #0xa10]
    // 0xa4bf28: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4bf28: stur            w2, [x1, #0x17]
    // 0xa4bf2c: r3 = Instance_CrossAxisAlignment
    //     0xa4bf2c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4bf30: ldr             x3, [x3, #0xa18]
    // 0xa4bf34: StoreField: r1->field_1b = r3
    //     0xa4bf34: stur            w3, [x1, #0x1b]
    // 0xa4bf38: r4 = Instance_VerticalDirection
    //     0xa4bf38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4bf3c: ldr             x4, [x4, #0xa20]
    // 0xa4bf40: StoreField: r1->field_23 = r4
    //     0xa4bf40: stur            w4, [x1, #0x23]
    // 0xa4bf44: r5 = Instance_Clip
    //     0xa4bf44: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4bf48: ldr             x5, [x5, #0x38]
    // 0xa4bf4c: StoreField: r1->field_2b = r5
    //     0xa4bf4c: stur            w5, [x1, #0x2b]
    // 0xa4bf50: StoreField: r1->field_2f = rZR
    //     0xa4bf50: stur            xzr, [x1, #0x2f]
    // 0xa4bf54: ldur            x6, [fp, #-0x48]
    // 0xa4bf58: StoreField: r1->field_b = r6
    //     0xa4bf58: stur            w6, [x1, #0xb]
    // 0xa4bf5c: r0 = Padding()
    //     0xa4bf5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4bf60: r7 = Instance_EdgeInsets
    //     0xa4bf60: add             x7, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xa4bf64: ldr             x7, [x7, #0x100]
    // 0xa4bf68: stur            x0, [fp, #-0x40]
    // 0xa4bf6c: StoreField: r0->field_f = r7
    //     0xa4bf6c: stur            w7, [x0, #0xf]
    // 0xa4bf70: ldur            x1, [fp, #-0x30]
    // 0xa4bf74: StoreField: r0->field_b = r1
    //     0xa4bf74: stur            w1, [x0, #0xb]
    // 0xa4bf78: ldur            x2, [fp, #-0x38]
    // 0xa4bf7c: LoadField: r1 = r2->field_b
    //     0xa4bf7c: ldur            w1, [x2, #0xb]
    // 0xa4bf80: LoadField: r3 = r2->field_f
    //     0xa4bf80: ldur            w3, [x2, #0xf]
    // 0xa4bf84: DecompressPointer r3
    //     0xa4bf84: add             x3, x3, HEAP, lsl #32
    // 0xa4bf88: LoadField: r4 = r3->field_b
    //     0xa4bf88: ldur            w4, [x3, #0xb]
    // 0xa4bf8c: r3 = LoadInt32Instr(r1)
    //     0xa4bf8c: sbfx            x3, x1, #1, #0x1f
    // 0xa4bf90: stur            x3, [fp, #-0x68]
    // 0xa4bf94: r1 = LoadInt32Instr(r4)
    //     0xa4bf94: sbfx            x1, x4, #1, #0x1f
    // 0xa4bf98: cmp             x3, x1
    // 0xa4bf9c: b.ne            #0xa4bfa8
    // 0xa4bfa0: mov             x1, x2
    // 0xa4bfa4: r0 = _growToNextCapacity()
    //     0xa4bfa4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4bfa8: ldur            x2, [fp, #-0x38]
    // 0xa4bfac: ldur            x3, [fp, #-0x68]
    // 0xa4bfb0: add             x4, x3, #1
    // 0xa4bfb4: lsl             x0, x4, #1
    // 0xa4bfb8: StoreField: r2->field_b = r0
    //     0xa4bfb8: stur            w0, [x2, #0xb]
    // 0xa4bfbc: LoadField: r5 = r2->field_f
    //     0xa4bfbc: ldur            w5, [x2, #0xf]
    // 0xa4bfc0: DecompressPointer r5
    //     0xa4bfc0: add             x5, x5, HEAP, lsl #32
    // 0xa4bfc4: mov             x1, x5
    // 0xa4bfc8: ldur            x0, [fp, #-0x40]
    // 0xa4bfcc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4bfcc: add             x25, x1, x3, lsl #2
    //     0xa4bfd0: add             x25, x25, #0xf
    //     0xa4bfd4: str             w0, [x25]
    //     0xa4bfd8: tbz             w0, #0, #0xa4bff4
    //     0xa4bfdc: ldurb           w16, [x1, #-1]
    //     0xa4bfe0: ldurb           w17, [x0, #-1]
    //     0xa4bfe4: and             x16, x17, x16, lsr #2
    //     0xa4bfe8: tst             x16, HEAP, lsr #32
    //     0xa4bfec: b.eq            #0xa4bff4
    //     0xa4bff0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4bff4: mov             x1, x4
    // 0xa4bff8: mov             x0, x5
    // 0xa4bffc: mov             x8, x2
    // 0xa4c000: b               #0xa4c05c
    // 0xa4c004: LoadField: r0 = r2->field_b
    //     0xa4c004: ldur            w0, [x2, #0xb]
    // 0xa4c008: LoadField: r1 = r2->field_f
    //     0xa4c008: ldur            w1, [x2, #0xf]
    // 0xa4c00c: DecompressPointer r1
    //     0xa4c00c: add             x1, x1, HEAP, lsl #32
    // 0xa4c010: LoadField: r3 = r1->field_b
    //     0xa4c010: ldur            w3, [x1, #0xb]
    // 0xa4c014: r4 = LoadInt32Instr(r0)
    //     0xa4c014: sbfx            x4, x0, #1, #0x1f
    // 0xa4c018: stur            x4, [fp, #-0x68]
    // 0xa4c01c: r0 = LoadInt32Instr(r3)
    //     0xa4c01c: sbfx            x0, x3, #1, #0x1f
    // 0xa4c020: cmp             x4, x0
    // 0xa4c024: b.ne            #0xa4c030
    // 0xa4c028: mov             x1, x2
    // 0xa4c02c: r0 = _growToNextCapacity()
    //     0xa4c02c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4c030: ldur            x8, [fp, #-0x38]
    // 0xa4c034: ldur            x0, [fp, #-0x68]
    // 0xa4c038: add             x1, x0, #1
    // 0xa4c03c: lsl             x2, x1, #1
    // 0xa4c040: StoreField: r8->field_b = r2
    //     0xa4c040: stur            w2, [x8, #0xb]
    // 0xa4c044: LoadField: r2 = r8->field_f
    //     0xa4c044: ldur            w2, [x8, #0xf]
    // 0xa4c048: DecompressPointer r2
    //     0xa4c048: add             x2, x2, HEAP, lsl #32
    // 0xa4c04c: add             x3, x2, x0, lsl #2
    // 0xa4c050: r16 = Instance_SizedBox
    //     0xa4c050: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c054: StoreField: r3->field_f = r16
    //     0xa4c054: stur            w16, [x3, #0xf]
    // 0xa4c058: mov             x0, x2
    // 0xa4c05c: mov             x3, x1
    // 0xa4c060: mov             x2, x8
    // 0xa4c064: b               #0xa4ca7c
    // 0xa4c068: mov             x8, x2
    // 0xa4c06c: r5 = true
    //     0xa4c06c: add             x5, NULL, #0x20  ; true
    // 0xa4c070: r7 = Instance_EdgeInsets
    //     0xa4c070: add             x7, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xa4c074: ldr             x7, [x7, #0x100]
    // 0xa4c078: r0 = Instance_BorderRadius
    //     0xa4c078: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa4c07c: ldr             x0, [x0, #0xbe8]
    // 0xa4c080: r3 = Instance_EdgeInsets
    //     0xa4c080: add             x3, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa4c084: ldr             x3, [x3, #0x898]
    // 0xa4c088: r6 = Instance_MainAxisAlignment
    //     0xa4c088: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa4c08c: ldr             x6, [x6, #0xd10]
    // 0xa4c090: r2 = Instance_Alignment
    //     0xa4c090: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa4c094: ldr             x2, [x2, #0xb10]
    // 0xa4c098: r16 = Instance_TitleAlignment
    //     0xa4c098: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xa4c09c: ldr             x16, [x16, #0x520]
    // 0xa4c0a0: cmp             w4, w16
    // 0xa4c0a4: b.ne            #0xa4c4f4
    // 0xa4c0a8: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4c0a8: ldur            w4, [x1, #0x17]
    // 0xa4c0ac: DecompressPointer r4
    //     0xa4c0ac: add             x4, x4, HEAP, lsl #32
    // 0xa4c0b0: cmp             w4, NULL
    // 0xa4c0b4: b.ne            #0xa4c0c0
    // 0xa4c0b8: r4 = Null
    //     0xa4c0b8: mov             x4, NULL
    // 0xa4c0bc: b               #0xa4c0f0
    // 0xa4c0c0: LoadField: r9 = r4->field_7
    //     0xa4c0c0: ldur            w9, [x4, #7]
    // 0xa4c0c4: DecompressPointer r9
    //     0xa4c0c4: add             x9, x9, HEAP, lsl #32
    // 0xa4c0c8: cmp             w9, NULL
    // 0xa4c0cc: b.ne            #0xa4c0d8
    // 0xa4c0d0: r4 = Null
    //     0xa4c0d0: mov             x4, NULL
    // 0xa4c0d4: b               #0xa4c0f0
    // 0xa4c0d8: LoadField: r4 = r9->field_7
    //     0xa4c0d8: ldur            w4, [x9, #7]
    // 0xa4c0dc: cbnz            w4, #0xa4c0e8
    // 0xa4c0e0: r9 = false
    //     0xa4c0e0: add             x9, NULL, #0x30  ; false
    // 0xa4c0e4: b               #0xa4c0ec
    // 0xa4c0e8: r9 = true
    //     0xa4c0e8: add             x9, NULL, #0x20  ; true
    // 0xa4c0ec: mov             x4, x9
    // 0xa4c0f0: cmp             w4, NULL
    // 0xa4c0f4: b.ne            #0xa4c100
    // 0xa4c0f8: r9 = false
    //     0xa4c0f8: add             x9, NULL, #0x30  ; false
    // 0xa4c0fc: b               #0xa4c104
    // 0xa4c100: mov             x9, x4
    // 0xa4c104: ldur            x4, [fp, #-8]
    // 0xa4c108: stur            x9, [fp, #-0x40]
    // 0xa4c10c: LoadField: r10 = r1->field_13
    //     0xa4c10c: ldur            w10, [x1, #0x13]
    // 0xa4c110: DecompressPointer r10
    //     0xa4c110: add             x10, x10, HEAP, lsl #32
    // 0xa4c114: ldur            x1, [fp, #-0x10]
    // 0xa4c118: stur            x10, [fp, #-0x30]
    // 0xa4c11c: r0 = of()
    //     0xa4c11c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c120: LoadField: r2 = r0->field_5b
    //     0xa4c120: ldur            w2, [x0, #0x5b]
    // 0xa4c124: DecompressPointer r2
    //     0xa4c124: add             x2, x2, HEAP, lsl #32
    // 0xa4c128: r1 = Null
    //     0xa4c128: mov             x1, NULL
    // 0xa4c12c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4c12c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4c130: r0 = Border.all()
    //     0xa4c130: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa4c134: stur            x0, [fp, #-0x48]
    // 0xa4c138: r0 = BoxDecoration()
    //     0xa4c138: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4c13c: mov             x2, x0
    // 0xa4c140: ldur            x0, [fp, #-0x48]
    // 0xa4c144: stur            x2, [fp, #-0x50]
    // 0xa4c148: StoreField: r2->field_f = r0
    //     0xa4c148: stur            w0, [x2, #0xf]
    // 0xa4c14c: r0 = Instance_BorderRadius
    //     0xa4c14c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa4c150: ldr             x0, [x0, #0xbe8]
    // 0xa4c154: StoreField: r2->field_13 = r0
    //     0xa4c154: stur            w0, [x2, #0x13]
    // 0xa4c158: r0 = Instance_BoxShape
    //     0xa4c158: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4c15c: ldr             x0, [x0, #0x80]
    // 0xa4c160: StoreField: r2->field_23 = r0
    //     0xa4c160: stur            w0, [x2, #0x23]
    // 0xa4c164: ldur            x3, [fp, #-8]
    // 0xa4c168: LoadField: r1 = r3->field_b
    //     0xa4c168: ldur            w1, [x3, #0xb]
    // 0xa4c16c: DecompressPointer r1
    //     0xa4c16c: add             x1, x1, HEAP, lsl #32
    // 0xa4c170: cmp             w1, NULL
    // 0xa4c174: b.eq            #0xa4d608
    // 0xa4c178: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4c178: ldur            w4, [x1, #0x17]
    // 0xa4c17c: DecompressPointer r4
    //     0xa4c17c: add             x4, x4, HEAP, lsl #32
    // 0xa4c180: cmp             w4, NULL
    // 0xa4c184: b.ne            #0xa4c190
    // 0xa4c188: r1 = Null
    //     0xa4c188: mov             x1, NULL
    // 0xa4c18c: b               #0xa4c198
    // 0xa4c190: LoadField: r1 = r4->field_7
    //     0xa4c190: ldur            w1, [x4, #7]
    // 0xa4c194: DecompressPointer r1
    //     0xa4c194: add             x1, x1, HEAP, lsl #32
    // 0xa4c198: cmp             w1, NULL
    // 0xa4c19c: b.ne            #0xa4c1a8
    // 0xa4c1a0: r5 = ""
    //     0xa4c1a0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4c1a4: b               #0xa4c1ac
    // 0xa4c1a8: mov             x5, x1
    // 0xa4c1ac: ldur            x4, [fp, #-0x30]
    // 0xa4c1b0: ldur            x1, [fp, #-0x10]
    // 0xa4c1b4: stur            x5, [fp, #-0x48]
    // 0xa4c1b8: r0 = of()
    //     0xa4c1b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c1bc: LoadField: r1 = r0->field_87
    //     0xa4c1bc: ldur            w1, [x0, #0x87]
    // 0xa4c1c0: DecompressPointer r1
    //     0xa4c1c0: add             x1, x1, HEAP, lsl #32
    // 0xa4c1c4: LoadField: r0 = r1->field_7
    //     0xa4c1c4: ldur            w0, [x1, #7]
    // 0xa4c1c8: DecompressPointer r0
    //     0xa4c1c8: add             x0, x0, HEAP, lsl #32
    // 0xa4c1cc: ldur            x1, [fp, #-0x10]
    // 0xa4c1d0: stur            x0, [fp, #-0x58]
    // 0xa4c1d4: r0 = of()
    //     0xa4c1d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c1d8: LoadField: r1 = r0->field_5b
    //     0xa4c1d8: ldur            w1, [x0, #0x5b]
    // 0xa4c1dc: DecompressPointer r1
    //     0xa4c1dc: add             x1, x1, HEAP, lsl #32
    // 0xa4c1e0: r0 = LoadClassIdInstr(r1)
    //     0xa4c1e0: ldur            x0, [x1, #-1]
    //     0xa4c1e4: ubfx            x0, x0, #0xc, #0x14
    // 0xa4c1e8: d0 = 0.700000
    //     0xa4c1e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4c1ec: ldr             d0, [x17, #0xf48]
    // 0xa4c1f0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa4c1f0: sub             lr, x0, #0xffa
    //     0xa4c1f4: ldr             lr, [x21, lr, lsl #3]
    //     0xa4c1f8: blr             lr
    // 0xa4c1fc: r16 = 14.000000
    //     0xa4c1fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4c200: ldr             x16, [x16, #0x1d8]
    // 0xa4c204: stp             x0, x16, [SP]
    // 0xa4c208: ldur            x1, [fp, #-0x58]
    // 0xa4c20c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4c20c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4c210: ldr             x4, [x4, #0xaa0]
    // 0xa4c214: r0 = copyWith()
    //     0xa4c214: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4c218: stur            x0, [fp, #-0x58]
    // 0xa4c21c: r0 = Text()
    //     0xa4c21c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4c220: mov             x1, x0
    // 0xa4c224: ldur            x0, [fp, #-0x48]
    // 0xa4c228: stur            x1, [fp, #-0x60]
    // 0xa4c22c: StoreField: r1->field_b = r0
    //     0xa4c22c: stur            w0, [x1, #0xb]
    // 0xa4c230: ldur            x0, [fp, #-0x58]
    // 0xa4c234: StoreField: r1->field_13 = r0
    //     0xa4c234: stur            w0, [x1, #0x13]
    // 0xa4c238: r0 = Center()
    //     0xa4c238: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa4c23c: r2 = Instance_Alignment
    //     0xa4c23c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa4c240: ldr             x2, [x2, #0xb10]
    // 0xa4c244: stur            x0, [fp, #-0x48]
    // 0xa4c248: StoreField: r0->field_f = r2
    //     0xa4c248: stur            w2, [x0, #0xf]
    // 0xa4c24c: ldur            x1, [fp, #-0x60]
    // 0xa4c250: StoreField: r0->field_b = r1
    //     0xa4c250: stur            w1, [x0, #0xb]
    // 0xa4c254: r0 = Padding()
    //     0xa4c254: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4c258: r3 = Instance_EdgeInsets
    //     0xa4c258: add             x3, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa4c25c: ldr             x3, [x3, #0x898]
    // 0xa4c260: stur            x0, [fp, #-0x58]
    // 0xa4c264: StoreField: r0->field_f = r3
    //     0xa4c264: stur            w3, [x0, #0xf]
    // 0xa4c268: ldur            x1, [fp, #-0x48]
    // 0xa4c26c: StoreField: r0->field_b = r1
    //     0xa4c26c: stur            w1, [x0, #0xb]
    // 0xa4c270: r0 = Container()
    //     0xa4c270: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4c274: stur            x0, [fp, #-0x48]
    // 0xa4c278: r16 = 32.000000
    //     0xa4c278: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa4c27c: ldr             x16, [x16, #0x848]
    // 0xa4c280: r30 = 90.000000
    //     0xa4c280: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa4c284: ldr             lr, [lr, #0xe68]
    // 0xa4c288: stp             lr, x16, [SP, #0x10]
    // 0xa4c28c: ldur            x16, [fp, #-0x50]
    // 0xa4c290: ldur            lr, [fp, #-0x58]
    // 0xa4c294: stp             lr, x16, [SP]
    // 0xa4c298: mov             x1, x0
    // 0xa4c29c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa4c29c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa4c2a0: ldr             x4, [x4, #0x8c0]
    // 0xa4c2a4: r0 = Container()
    //     0xa4c2a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4c2a8: r0 = InkWell()
    //     0xa4c2a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa4c2ac: mov             x3, x0
    // 0xa4c2b0: ldur            x0, [fp, #-0x48]
    // 0xa4c2b4: stur            x3, [fp, #-0x50]
    // 0xa4c2b8: StoreField: r3->field_b = r0
    //     0xa4c2b8: stur            w0, [x3, #0xb]
    // 0xa4c2bc: ldur            x2, [fp, #-0x28]
    // 0xa4c2c0: r1 = Function '<anonymous closure>':.
    //     0xa4c2c0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aa70] AnonymousClosure: (0xa54780), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4c2c4: ldr             x1, [x1, #0xa70]
    // 0xa4c2c8: r0 = AllocateClosure()
    //     0xa4c2c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4c2cc: mov             x1, x0
    // 0xa4c2d0: ldur            x0, [fp, #-0x50]
    // 0xa4c2d4: StoreField: r0->field_f = r1
    //     0xa4c2d4: stur            w1, [x0, #0xf]
    // 0xa4c2d8: r4 = true
    //     0xa4c2d8: add             x4, NULL, #0x20  ; true
    // 0xa4c2dc: StoreField: r0->field_43 = r4
    //     0xa4c2dc: stur            w4, [x0, #0x43]
    // 0xa4c2e0: r1 = Instance_BoxShape
    //     0xa4c2e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4c2e4: ldr             x1, [x1, #0x80]
    // 0xa4c2e8: StoreField: r0->field_47 = r1
    //     0xa4c2e8: stur            w1, [x0, #0x47]
    // 0xa4c2ec: StoreField: r0->field_6f = r4
    //     0xa4c2ec: stur            w4, [x0, #0x6f]
    // 0xa4c2f0: r2 = false
    //     0xa4c2f0: add             x2, NULL, #0x30  ; false
    // 0xa4c2f4: StoreField: r0->field_73 = r2
    //     0xa4c2f4: stur            w2, [x0, #0x73]
    // 0xa4c2f8: StoreField: r0->field_83 = r4
    //     0xa4c2f8: stur            w4, [x0, #0x83]
    // 0xa4c2fc: StoreField: r0->field_7b = r2
    //     0xa4c2fc: stur            w2, [x0, #0x7b]
    // 0xa4c300: r0 = Visibility()
    //     0xa4c300: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4c304: mov             x2, x0
    // 0xa4c308: ldur            x0, [fp, #-0x50]
    // 0xa4c30c: stur            x2, [fp, #-0x48]
    // 0xa4c310: StoreField: r2->field_b = r0
    //     0xa4c310: stur            w0, [x2, #0xb]
    // 0xa4c314: r0 = Instance_SizedBox
    //     0xa4c314: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c318: StoreField: r2->field_f = r0
    //     0xa4c318: stur            w0, [x2, #0xf]
    // 0xa4c31c: ldur            x1, [fp, #-0x30]
    // 0xa4c320: StoreField: r2->field_13 = r1
    //     0xa4c320: stur            w1, [x2, #0x13]
    // 0xa4c324: r3 = false
    //     0xa4c324: add             x3, NULL, #0x30  ; false
    // 0xa4c328: ArrayStore: r2[0] = r3  ; List_4
    //     0xa4c328: stur            w3, [x2, #0x17]
    // 0xa4c32c: StoreField: r2->field_1b = r3
    //     0xa4c32c: stur            w3, [x2, #0x1b]
    // 0xa4c330: StoreField: r2->field_1f = r3
    //     0xa4c330: stur            w3, [x2, #0x1f]
    // 0xa4c334: StoreField: r2->field_23 = r3
    //     0xa4c334: stur            w3, [x2, #0x23]
    // 0xa4c338: StoreField: r2->field_27 = r3
    //     0xa4c338: stur            w3, [x2, #0x27]
    // 0xa4c33c: StoreField: r2->field_2b = r3
    //     0xa4c33c: stur            w3, [x2, #0x2b]
    // 0xa4c340: ldur            x4, [fp, #-8]
    // 0xa4c344: LoadField: r1 = r4->field_b
    //     0xa4c344: ldur            w1, [x4, #0xb]
    // 0xa4c348: DecompressPointer r1
    //     0xa4c348: add             x1, x1, HEAP, lsl #32
    // 0xa4c34c: cmp             w1, NULL
    // 0xa4c350: b.eq            #0xa4d60c
    // 0xa4c354: LoadField: r5 = r1->field_f
    //     0xa4c354: ldur            w5, [x1, #0xf]
    // 0xa4c358: DecompressPointer r5
    //     0xa4c358: add             x5, x5, HEAP, lsl #32
    // 0xa4c35c: cmp             w5, NULL
    // 0xa4c360: b.ne            #0xa4c36c
    // 0xa4c364: r6 = ""
    //     0xa4c364: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4c368: b               #0xa4c370
    // 0xa4c36c: mov             x6, x5
    // 0xa4c370: ldur            x5, [fp, #-0x40]
    // 0xa4c374: ldur            x1, [fp, #-0x10]
    // 0xa4c378: stur            x6, [fp, #-0x30]
    // 0xa4c37c: r0 = of()
    //     0xa4c37c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c380: LoadField: r1 = r0->field_87
    //     0xa4c380: ldur            w1, [x0, #0x87]
    // 0xa4c384: DecompressPointer r1
    //     0xa4c384: add             x1, x1, HEAP, lsl #32
    // 0xa4c388: LoadField: r0 = r1->field_27
    //     0xa4c388: ldur            w0, [x1, #0x27]
    // 0xa4c38c: DecompressPointer r0
    //     0xa4c38c: add             x0, x0, HEAP, lsl #32
    // 0xa4c390: stur            x0, [fp, #-0x50]
    // 0xa4c394: r1 = Instance_Color
    //     0xa4c394: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4c398: d0 = 0.700000
    //     0xa4c398: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4c39c: ldr             d0, [x17, #0xf48]
    // 0xa4c3a0: r0 = withOpacity()
    //     0xa4c3a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4c3a4: r16 = 21.000000
    //     0xa4c3a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa4c3a8: ldr             x16, [x16, #0x9b0]
    // 0xa4c3ac: stp             x0, x16, [SP]
    // 0xa4c3b0: ldur            x1, [fp, #-0x50]
    // 0xa4c3b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4c3b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4c3b8: ldr             x4, [x4, #0xaa0]
    // 0xa4c3bc: r0 = copyWith()
    //     0xa4c3bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4c3c0: stur            x0, [fp, #-0x50]
    // 0xa4c3c4: r0 = Text()
    //     0xa4c3c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4c3c8: mov             x3, x0
    // 0xa4c3cc: ldur            x0, [fp, #-0x30]
    // 0xa4c3d0: stur            x3, [fp, #-0x58]
    // 0xa4c3d4: StoreField: r3->field_b = r0
    //     0xa4c3d4: stur            w0, [x3, #0xb]
    // 0xa4c3d8: ldur            x0, [fp, #-0x50]
    // 0xa4c3dc: StoreField: r3->field_13 = r0
    //     0xa4c3dc: stur            w0, [x3, #0x13]
    // 0xa4c3e0: r1 = Null
    //     0xa4c3e0: mov             x1, NULL
    // 0xa4c3e4: r2 = 6
    //     0xa4c3e4: movz            x2, #0x6
    // 0xa4c3e8: r0 = AllocateArray()
    //     0xa4c3e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4c3ec: mov             x2, x0
    // 0xa4c3f0: ldur            x0, [fp, #-0x48]
    // 0xa4c3f4: stur            x2, [fp, #-0x30]
    // 0xa4c3f8: StoreField: r2->field_f = r0
    //     0xa4c3f8: stur            w0, [x2, #0xf]
    // 0xa4c3fc: r16 = Instance_Spacer
    //     0xa4c3fc: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa4c400: ldr             x16, [x16, #0xf0]
    // 0xa4c404: StoreField: r2->field_13 = r16
    //     0xa4c404: stur            w16, [x2, #0x13]
    // 0xa4c408: ldur            x0, [fp, #-0x58]
    // 0xa4c40c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4c40c: stur            w0, [x2, #0x17]
    // 0xa4c410: r1 = <Widget>
    //     0xa4c410: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4c414: r0 = AllocateGrowableArray()
    //     0xa4c414: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4c418: mov             x1, x0
    // 0xa4c41c: ldur            x0, [fp, #-0x30]
    // 0xa4c420: stur            x1, [fp, #-0x48]
    // 0xa4c424: StoreField: r1->field_f = r0
    //     0xa4c424: stur            w0, [x1, #0xf]
    // 0xa4c428: r2 = 6
    //     0xa4c428: movz            x2, #0x6
    // 0xa4c42c: StoreField: r1->field_b = r2
    //     0xa4c42c: stur            w2, [x1, #0xb]
    // 0xa4c430: r0 = Row()
    //     0xa4c430: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4c434: mov             x1, x0
    // 0xa4c438: r0 = Instance_Axis
    //     0xa4c438: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa4c43c: stur            x1, [fp, #-0x30]
    // 0xa4c440: StoreField: r1->field_f = r0
    //     0xa4c440: stur            w0, [x1, #0xf]
    // 0xa4c444: r2 = Instance_MainAxisAlignment
    //     0xa4c444: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa4c448: ldr             x2, [x2, #0xd10]
    // 0xa4c44c: StoreField: r1->field_13 = r2
    //     0xa4c44c: stur            w2, [x1, #0x13]
    // 0xa4c450: r2 = Instance_MainAxisSize
    //     0xa4c450: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4c454: ldr             x2, [x2, #0xa10]
    // 0xa4c458: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4c458: stur            w2, [x1, #0x17]
    // 0xa4c45c: r3 = Instance_CrossAxisAlignment
    //     0xa4c45c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4c460: ldr             x3, [x3, #0xa18]
    // 0xa4c464: StoreField: r1->field_1b = r3
    //     0xa4c464: stur            w3, [x1, #0x1b]
    // 0xa4c468: r4 = Instance_VerticalDirection
    //     0xa4c468: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4c46c: ldr             x4, [x4, #0xa20]
    // 0xa4c470: StoreField: r1->field_23 = r4
    //     0xa4c470: stur            w4, [x1, #0x23]
    // 0xa4c474: r5 = Instance_Clip
    //     0xa4c474: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4c478: ldr             x5, [x5, #0x38]
    // 0xa4c47c: StoreField: r1->field_2b = r5
    //     0xa4c47c: stur            w5, [x1, #0x2b]
    // 0xa4c480: StoreField: r1->field_2f = rZR
    //     0xa4c480: stur            xzr, [x1, #0x2f]
    // 0xa4c484: ldur            x6, [fp, #-0x48]
    // 0xa4c488: StoreField: r1->field_b = r6
    //     0xa4c488: stur            w6, [x1, #0xb]
    // 0xa4c48c: r0 = Padding()
    //     0xa4c48c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4c490: r5 = Instance_EdgeInsets
    //     0xa4c490: add             x5, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xa4c494: ldr             x5, [x5, #0x100]
    // 0xa4c498: stur            x0, [fp, #-0x48]
    // 0xa4c49c: StoreField: r0->field_f = r5
    //     0xa4c49c: stur            w5, [x0, #0xf]
    // 0xa4c4a0: ldur            x1, [fp, #-0x30]
    // 0xa4c4a4: StoreField: r0->field_b = r1
    //     0xa4c4a4: stur            w1, [x0, #0xb]
    // 0xa4c4a8: r0 = Visibility()
    //     0xa4c4a8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4c4ac: mov             x1, x0
    // 0xa4c4b0: ldur            x0, [fp, #-0x48]
    // 0xa4c4b4: StoreField: r1->field_b = r0
    //     0xa4c4b4: stur            w0, [x1, #0xb]
    // 0xa4c4b8: r6 = Instance_SizedBox
    //     0xa4c4b8: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c4bc: StoreField: r1->field_f = r6
    //     0xa4c4bc: stur            w6, [x1, #0xf]
    // 0xa4c4c0: ldur            x0, [fp, #-0x40]
    // 0xa4c4c4: StoreField: r1->field_13 = r0
    //     0xa4c4c4: stur            w0, [x1, #0x13]
    // 0xa4c4c8: r7 = false
    //     0xa4c4c8: add             x7, NULL, #0x30  ; false
    // 0xa4c4cc: ArrayStore: r1[0] = r7  ; List_4
    //     0xa4c4cc: stur            w7, [x1, #0x17]
    // 0xa4c4d0: StoreField: r1->field_1b = r7
    //     0xa4c4d0: stur            w7, [x1, #0x1b]
    // 0xa4c4d4: StoreField: r1->field_1f = r7
    //     0xa4c4d4: stur            w7, [x1, #0x1f]
    // 0xa4c4d8: StoreField: r1->field_23 = r7
    //     0xa4c4d8: stur            w7, [x1, #0x23]
    // 0xa4c4dc: StoreField: r1->field_27 = r7
    //     0xa4c4dc: stur            w7, [x1, #0x27]
    // 0xa4c4e0: StoreField: r1->field_2b = r7
    //     0xa4c4e0: stur            w7, [x1, #0x2b]
    // 0xa4c4e4: mov             x4, x1
    // 0xa4c4e8: mov             x2, x7
    // 0xa4c4ec: mov             x0, x6
    // 0xa4c4f0: b               #0xa4c9f4
    // 0xa4c4f4: mov             x4, x5
    // 0xa4c4f8: mov             x5, x7
    // 0xa4c4fc: r7 = false
    //     0xa4c4fc: add             x7, NULL, #0x30  ; false
    // 0xa4c500: r6 = Instance_SizedBox
    //     0xa4c500: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c504: LoadField: r8 = r1->field_f
    //     0xa4c504: ldur            w8, [x1, #0xf]
    // 0xa4c508: DecompressPointer r8
    //     0xa4c508: add             x8, x8, HEAP, lsl #32
    // 0xa4c50c: cmp             w8, NULL
    // 0xa4c510: b.ne            #0xa4c51c
    // 0xa4c514: r1 = Null
    //     0xa4c514: mov             x1, NULL
    // 0xa4c518: b               #0xa4c534
    // 0xa4c51c: LoadField: r1 = r8->field_7
    //     0xa4c51c: ldur            w1, [x8, #7]
    // 0xa4c520: cbnz            w1, #0xa4c52c
    // 0xa4c524: r9 = false
    //     0xa4c524: add             x9, NULL, #0x30  ; false
    // 0xa4c528: b               #0xa4c530
    // 0xa4c52c: r9 = true
    //     0xa4c52c: add             x9, NULL, #0x20  ; true
    // 0xa4c530: mov             x1, x9
    // 0xa4c534: cmp             w1, NULL
    // 0xa4c538: b.ne            #0xa4c544
    // 0xa4c53c: r9 = false
    //     0xa4c53c: add             x9, NULL, #0x30  ; false
    // 0xa4c540: b               #0xa4c548
    // 0xa4c544: mov             x9, x1
    // 0xa4c548: stur            x9, [fp, #-0x40]
    // 0xa4c54c: cmp             w8, NULL
    // 0xa4c550: b.ne            #0xa4c55c
    // 0xa4c554: r10 = ""
    //     0xa4c554: ldr             x10, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4c558: b               #0xa4c560
    // 0xa4c55c: mov             x10, x8
    // 0xa4c560: ldur            x8, [fp, #-8]
    // 0xa4c564: ldur            x1, [fp, #-0x10]
    // 0xa4c568: stur            x10, [fp, #-0x30]
    // 0xa4c56c: r0 = of()
    //     0xa4c56c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c570: LoadField: r1 = r0->field_87
    //     0xa4c570: ldur            w1, [x0, #0x87]
    // 0xa4c574: DecompressPointer r1
    //     0xa4c574: add             x1, x1, HEAP, lsl #32
    // 0xa4c578: LoadField: r0 = r1->field_27
    //     0xa4c578: ldur            w0, [x1, #0x27]
    // 0xa4c57c: DecompressPointer r0
    //     0xa4c57c: add             x0, x0, HEAP, lsl #32
    // 0xa4c580: stur            x0, [fp, #-0x48]
    // 0xa4c584: r1 = Instance_Color
    //     0xa4c584: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4c588: d0 = 0.700000
    //     0xa4c588: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4c58c: ldr             d0, [x17, #0xf48]
    // 0xa4c590: r0 = withOpacity()
    //     0xa4c590: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4c594: r16 = 21.000000
    //     0xa4c594: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa4c598: ldr             x16, [x16, #0x9b0]
    // 0xa4c59c: stp             x0, x16, [SP]
    // 0xa4c5a0: ldur            x1, [fp, #-0x48]
    // 0xa4c5a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4c5a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4c5a8: ldr             x4, [x4, #0xaa0]
    // 0xa4c5ac: r0 = copyWith()
    //     0xa4c5ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4c5b0: stur            x0, [fp, #-0x48]
    // 0xa4c5b4: r0 = Text()
    //     0xa4c5b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4c5b8: mov             x1, x0
    // 0xa4c5bc: ldur            x0, [fp, #-0x30]
    // 0xa4c5c0: stur            x1, [fp, #-0x50]
    // 0xa4c5c4: StoreField: r1->field_b = r0
    //     0xa4c5c4: stur            w0, [x1, #0xb]
    // 0xa4c5c8: ldur            x0, [fp, #-0x48]
    // 0xa4c5cc: StoreField: r1->field_13 = r0
    //     0xa4c5cc: stur            w0, [x1, #0x13]
    // 0xa4c5d0: r0 = Padding()
    //     0xa4c5d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4c5d4: mov             x2, x0
    // 0xa4c5d8: r0 = Instance_EdgeInsets
    //     0xa4c5d8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xa4c5dc: ldr             x0, [x0, #0x100]
    // 0xa4c5e0: stur            x2, [fp, #-0x58]
    // 0xa4c5e4: StoreField: r2->field_f = r0
    //     0xa4c5e4: stur            w0, [x2, #0xf]
    // 0xa4c5e8: ldur            x0, [fp, #-0x50]
    // 0xa4c5ec: StoreField: r2->field_b = r0
    //     0xa4c5ec: stur            w0, [x2, #0xb]
    // 0xa4c5f0: ldur            x0, [fp, #-8]
    // 0xa4c5f4: LoadField: r1 = r0->field_b
    //     0xa4c5f4: ldur            w1, [x0, #0xb]
    // 0xa4c5f8: DecompressPointer r1
    //     0xa4c5f8: add             x1, x1, HEAP, lsl #32
    // 0xa4c5fc: cmp             w1, NULL
    // 0xa4c600: b.eq            #0xa4d610
    // 0xa4c604: LoadField: r3 = r1->field_13
    //     0xa4c604: ldur            w3, [x1, #0x13]
    // 0xa4c608: DecompressPointer r3
    //     0xa4c608: add             x3, x3, HEAP, lsl #32
    // 0xa4c60c: stur            x3, [fp, #-0x48]
    // 0xa4c610: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4c610: ldur            w4, [x1, #0x17]
    // 0xa4c614: DecompressPointer r4
    //     0xa4c614: add             x4, x4, HEAP, lsl #32
    // 0xa4c618: cmp             w4, NULL
    // 0xa4c61c: b.ne            #0xa4c628
    // 0xa4c620: r1 = Null
    //     0xa4c620: mov             x1, NULL
    // 0xa4c624: b               #0xa4c654
    // 0xa4c628: LoadField: r1 = r4->field_7
    //     0xa4c628: ldur            w1, [x4, #7]
    // 0xa4c62c: DecompressPointer r1
    //     0xa4c62c: add             x1, x1, HEAP, lsl #32
    // 0xa4c630: cmp             w1, NULL
    // 0xa4c634: b.ne            #0xa4c640
    // 0xa4c638: r1 = Null
    //     0xa4c638: mov             x1, NULL
    // 0xa4c63c: b               #0xa4c654
    // 0xa4c640: LoadField: r4 = r1->field_7
    //     0xa4c640: ldur            w4, [x1, #7]
    // 0xa4c644: cbnz            w4, #0xa4c650
    // 0xa4c648: r1 = false
    //     0xa4c648: add             x1, NULL, #0x30  ; false
    // 0xa4c64c: b               #0xa4c654
    // 0xa4c650: r1 = true
    //     0xa4c650: add             x1, NULL, #0x20  ; true
    // 0xa4c654: cmp             w1, NULL
    // 0xa4c658: b.ne            #0xa4c664
    // 0xa4c65c: r4 = false
    //     0xa4c65c: add             x4, NULL, #0x30  ; false
    // 0xa4c660: b               #0xa4c668
    // 0xa4c664: mov             x4, x1
    // 0xa4c668: ldur            x1, [fp, #-0x10]
    // 0xa4c66c: stur            x4, [fp, #-0x30]
    // 0xa4c670: r0 = of()
    //     0xa4c670: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c674: LoadField: r2 = r0->field_5b
    //     0xa4c674: ldur            w2, [x0, #0x5b]
    // 0xa4c678: DecompressPointer r2
    //     0xa4c678: add             x2, x2, HEAP, lsl #32
    // 0xa4c67c: r1 = Null
    //     0xa4c67c: mov             x1, NULL
    // 0xa4c680: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4c680: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4c684: r0 = Border.all()
    //     0xa4c684: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa4c688: stur            x0, [fp, #-0x50]
    // 0xa4c68c: r0 = BoxDecoration()
    //     0xa4c68c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4c690: mov             x2, x0
    // 0xa4c694: ldur            x0, [fp, #-0x50]
    // 0xa4c698: stur            x2, [fp, #-0x60]
    // 0xa4c69c: StoreField: r2->field_f = r0
    //     0xa4c69c: stur            w0, [x2, #0xf]
    // 0xa4c6a0: r0 = Instance_BorderRadius
    //     0xa4c6a0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa4c6a4: ldr             x0, [x0, #0xbe8]
    // 0xa4c6a8: StoreField: r2->field_13 = r0
    //     0xa4c6a8: stur            w0, [x2, #0x13]
    // 0xa4c6ac: r0 = Instance_BoxShape
    //     0xa4c6ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4c6b0: ldr             x0, [x0, #0x80]
    // 0xa4c6b4: StoreField: r2->field_23 = r0
    //     0xa4c6b4: stur            w0, [x2, #0x23]
    // 0xa4c6b8: ldur            x3, [fp, #-8]
    // 0xa4c6bc: LoadField: r1 = r3->field_b
    //     0xa4c6bc: ldur            w1, [x3, #0xb]
    // 0xa4c6c0: DecompressPointer r1
    //     0xa4c6c0: add             x1, x1, HEAP, lsl #32
    // 0xa4c6c4: cmp             w1, NULL
    // 0xa4c6c8: b.eq            #0xa4d614
    // 0xa4c6cc: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4c6cc: ldur            w4, [x1, #0x17]
    // 0xa4c6d0: DecompressPointer r4
    //     0xa4c6d0: add             x4, x4, HEAP, lsl #32
    // 0xa4c6d4: cmp             w4, NULL
    // 0xa4c6d8: b.ne            #0xa4c6e4
    // 0xa4c6dc: r1 = Null
    //     0xa4c6dc: mov             x1, NULL
    // 0xa4c6e0: b               #0xa4c6ec
    // 0xa4c6e4: LoadField: r1 = r4->field_7
    //     0xa4c6e4: ldur            w1, [x4, #7]
    // 0xa4c6e8: DecompressPointer r1
    //     0xa4c6e8: add             x1, x1, HEAP, lsl #32
    // 0xa4c6ec: cmp             w1, NULL
    // 0xa4c6f0: b.ne            #0xa4c6fc
    // 0xa4c6f4: r8 = ""
    //     0xa4c6f4: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4c6f8: b               #0xa4c700
    // 0xa4c6fc: mov             x8, x1
    // 0xa4c700: ldur            x7, [fp, #-0x40]
    // 0xa4c704: ldur            x4, [fp, #-0x58]
    // 0xa4c708: ldur            x5, [fp, #-0x48]
    // 0xa4c70c: ldur            x6, [fp, #-0x30]
    // 0xa4c710: ldur            x1, [fp, #-0x10]
    // 0xa4c714: stur            x8, [fp, #-0x50]
    // 0xa4c718: r0 = of()
    //     0xa4c718: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c71c: LoadField: r1 = r0->field_87
    //     0xa4c71c: ldur            w1, [x0, #0x87]
    // 0xa4c720: DecompressPointer r1
    //     0xa4c720: add             x1, x1, HEAP, lsl #32
    // 0xa4c724: LoadField: r0 = r1->field_7
    //     0xa4c724: ldur            w0, [x1, #7]
    // 0xa4c728: DecompressPointer r0
    //     0xa4c728: add             x0, x0, HEAP, lsl #32
    // 0xa4c72c: ldur            x1, [fp, #-0x10]
    // 0xa4c730: stur            x0, [fp, #-0x70]
    // 0xa4c734: r0 = of()
    //     0xa4c734: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4c738: LoadField: r1 = r0->field_5b
    //     0xa4c738: ldur            w1, [x0, #0x5b]
    // 0xa4c73c: DecompressPointer r1
    //     0xa4c73c: add             x1, x1, HEAP, lsl #32
    // 0xa4c740: r0 = LoadClassIdInstr(r1)
    //     0xa4c740: ldur            x0, [x1, #-1]
    //     0xa4c744: ubfx            x0, x0, #0xc, #0x14
    // 0xa4c748: d0 = 0.700000
    //     0xa4c748: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4c74c: ldr             d0, [x17, #0xf48]
    // 0xa4c750: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa4c750: sub             lr, x0, #0xffa
    //     0xa4c754: ldr             lr, [x21, lr, lsl #3]
    //     0xa4c758: blr             lr
    // 0xa4c75c: r16 = 14.000000
    //     0xa4c75c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4c760: ldr             x16, [x16, #0x1d8]
    // 0xa4c764: stp             x0, x16, [SP]
    // 0xa4c768: ldur            x1, [fp, #-0x70]
    // 0xa4c76c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4c76c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4c770: ldr             x4, [x4, #0xaa0]
    // 0xa4c774: r0 = copyWith()
    //     0xa4c774: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4c778: stur            x0, [fp, #-0x70]
    // 0xa4c77c: r0 = Text()
    //     0xa4c77c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4c780: mov             x1, x0
    // 0xa4c784: ldur            x0, [fp, #-0x50]
    // 0xa4c788: stur            x1, [fp, #-0x78]
    // 0xa4c78c: StoreField: r1->field_b = r0
    //     0xa4c78c: stur            w0, [x1, #0xb]
    // 0xa4c790: ldur            x0, [fp, #-0x70]
    // 0xa4c794: StoreField: r1->field_13 = r0
    //     0xa4c794: stur            w0, [x1, #0x13]
    // 0xa4c798: r0 = Center()
    //     0xa4c798: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa4c79c: mov             x1, x0
    // 0xa4c7a0: r0 = Instance_Alignment
    //     0xa4c7a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa4c7a4: ldr             x0, [x0, #0xb10]
    // 0xa4c7a8: stur            x1, [fp, #-0x50]
    // 0xa4c7ac: StoreField: r1->field_f = r0
    //     0xa4c7ac: stur            w0, [x1, #0xf]
    // 0xa4c7b0: ldur            x0, [fp, #-0x78]
    // 0xa4c7b4: StoreField: r1->field_b = r0
    //     0xa4c7b4: stur            w0, [x1, #0xb]
    // 0xa4c7b8: r0 = Padding()
    //     0xa4c7b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4c7bc: mov             x1, x0
    // 0xa4c7c0: r0 = Instance_EdgeInsets
    //     0xa4c7c0: add             x0, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa4c7c4: ldr             x0, [x0, #0x898]
    // 0xa4c7c8: stur            x1, [fp, #-0x70]
    // 0xa4c7cc: StoreField: r1->field_f = r0
    //     0xa4c7cc: stur            w0, [x1, #0xf]
    // 0xa4c7d0: ldur            x0, [fp, #-0x50]
    // 0xa4c7d4: StoreField: r1->field_b = r0
    //     0xa4c7d4: stur            w0, [x1, #0xb]
    // 0xa4c7d8: r0 = Container()
    //     0xa4c7d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4c7dc: stur            x0, [fp, #-0x50]
    // 0xa4c7e0: r16 = 32.000000
    //     0xa4c7e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa4c7e4: ldr             x16, [x16, #0x848]
    // 0xa4c7e8: r30 = 90.000000
    //     0xa4c7e8: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa4c7ec: ldr             lr, [lr, #0xe68]
    // 0xa4c7f0: stp             lr, x16, [SP, #0x10]
    // 0xa4c7f4: ldur            x16, [fp, #-0x60]
    // 0xa4c7f8: ldur            lr, [fp, #-0x70]
    // 0xa4c7fc: stp             lr, x16, [SP]
    // 0xa4c800: mov             x1, x0
    // 0xa4c804: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa4c804: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa4c808: ldr             x4, [x4, #0x8c0]
    // 0xa4c80c: r0 = Container()
    //     0xa4c80c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4c810: r0 = Padding()
    //     0xa4c810: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4c814: mov             x1, x0
    // 0xa4c818: r0 = Instance_EdgeInsets
    //     0xa4c818: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa4c81c: ldr             x0, [x0, #0xa78]
    // 0xa4c820: stur            x1, [fp, #-0x60]
    // 0xa4c824: StoreField: r1->field_f = r0
    //     0xa4c824: stur            w0, [x1, #0xf]
    // 0xa4c828: ldur            x0, [fp, #-0x50]
    // 0xa4c82c: StoreField: r1->field_b = r0
    //     0xa4c82c: stur            w0, [x1, #0xb]
    // 0xa4c830: r0 = Visibility()
    //     0xa4c830: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4c834: mov             x1, x0
    // 0xa4c838: ldur            x0, [fp, #-0x60]
    // 0xa4c83c: stur            x1, [fp, #-0x50]
    // 0xa4c840: StoreField: r1->field_b = r0
    //     0xa4c840: stur            w0, [x1, #0xb]
    // 0xa4c844: r0 = Instance_SizedBox
    //     0xa4c844: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c848: StoreField: r1->field_f = r0
    //     0xa4c848: stur            w0, [x1, #0xf]
    // 0xa4c84c: ldur            x2, [fp, #-0x30]
    // 0xa4c850: StoreField: r1->field_13 = r2
    //     0xa4c850: stur            w2, [x1, #0x13]
    // 0xa4c854: r2 = false
    //     0xa4c854: add             x2, NULL, #0x30  ; false
    // 0xa4c858: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4c858: stur            w2, [x1, #0x17]
    // 0xa4c85c: StoreField: r1->field_1b = r2
    //     0xa4c85c: stur            w2, [x1, #0x1b]
    // 0xa4c860: StoreField: r1->field_1f = r2
    //     0xa4c860: stur            w2, [x1, #0x1f]
    // 0xa4c864: StoreField: r1->field_23 = r2
    //     0xa4c864: stur            w2, [x1, #0x23]
    // 0xa4c868: StoreField: r1->field_27 = r2
    //     0xa4c868: stur            w2, [x1, #0x27]
    // 0xa4c86c: StoreField: r1->field_2b = r2
    //     0xa4c86c: stur            w2, [x1, #0x2b]
    // 0xa4c870: r0 = InkWell()
    //     0xa4c870: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa4c874: mov             x3, x0
    // 0xa4c878: ldur            x0, [fp, #-0x50]
    // 0xa4c87c: stur            x3, [fp, #-0x30]
    // 0xa4c880: StoreField: r3->field_b = r0
    //     0xa4c880: stur            w0, [x3, #0xb]
    // 0xa4c884: ldur            x2, [fp, #-0x28]
    // 0xa4c888: r1 = Function '<anonymous closure>':.
    //     0xa4c888: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aa78] AnonymousClosure: (0xa54518), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4c88c: ldr             x1, [x1, #0xa78]
    // 0xa4c890: r0 = AllocateClosure()
    //     0xa4c890: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4c894: mov             x1, x0
    // 0xa4c898: ldur            x0, [fp, #-0x30]
    // 0xa4c89c: StoreField: r0->field_f = r1
    //     0xa4c89c: stur            w1, [x0, #0xf]
    // 0xa4c8a0: r1 = true
    //     0xa4c8a0: add             x1, NULL, #0x20  ; true
    // 0xa4c8a4: StoreField: r0->field_43 = r1
    //     0xa4c8a4: stur            w1, [x0, #0x43]
    // 0xa4c8a8: r2 = Instance_BoxShape
    //     0xa4c8a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4c8ac: ldr             x2, [x2, #0x80]
    // 0xa4c8b0: StoreField: r0->field_47 = r2
    //     0xa4c8b0: stur            w2, [x0, #0x47]
    // 0xa4c8b4: StoreField: r0->field_6f = r1
    //     0xa4c8b4: stur            w1, [x0, #0x6f]
    // 0xa4c8b8: r3 = false
    //     0xa4c8b8: add             x3, NULL, #0x30  ; false
    // 0xa4c8bc: StoreField: r0->field_73 = r3
    //     0xa4c8bc: stur            w3, [x0, #0x73]
    // 0xa4c8c0: StoreField: r0->field_83 = r1
    //     0xa4c8c0: stur            w1, [x0, #0x83]
    // 0xa4c8c4: StoreField: r0->field_7b = r3
    //     0xa4c8c4: stur            w3, [x0, #0x7b]
    // 0xa4c8c8: r0 = Visibility()
    //     0xa4c8c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4c8cc: mov             x3, x0
    // 0xa4c8d0: ldur            x0, [fp, #-0x30]
    // 0xa4c8d4: stur            x3, [fp, #-0x50]
    // 0xa4c8d8: StoreField: r3->field_b = r0
    //     0xa4c8d8: stur            w0, [x3, #0xb]
    // 0xa4c8dc: r0 = Instance_SizedBox
    //     0xa4c8dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c8e0: StoreField: r3->field_f = r0
    //     0xa4c8e0: stur            w0, [x3, #0xf]
    // 0xa4c8e4: ldur            x1, [fp, #-0x48]
    // 0xa4c8e8: StoreField: r3->field_13 = r1
    //     0xa4c8e8: stur            w1, [x3, #0x13]
    // 0xa4c8ec: r4 = false
    //     0xa4c8ec: add             x4, NULL, #0x30  ; false
    // 0xa4c8f0: ArrayStore: r3[0] = r4  ; List_4
    //     0xa4c8f0: stur            w4, [x3, #0x17]
    // 0xa4c8f4: StoreField: r3->field_1b = r4
    //     0xa4c8f4: stur            w4, [x3, #0x1b]
    // 0xa4c8f8: StoreField: r3->field_1f = r4
    //     0xa4c8f8: stur            w4, [x3, #0x1f]
    // 0xa4c8fc: StoreField: r3->field_23 = r4
    //     0xa4c8fc: stur            w4, [x3, #0x23]
    // 0xa4c900: StoreField: r3->field_27 = r4
    //     0xa4c900: stur            w4, [x3, #0x27]
    // 0xa4c904: StoreField: r3->field_2b = r4
    //     0xa4c904: stur            w4, [x3, #0x2b]
    // 0xa4c908: r1 = Null
    //     0xa4c908: mov             x1, NULL
    // 0xa4c90c: r2 = 6
    //     0xa4c90c: movz            x2, #0x6
    // 0xa4c910: r0 = AllocateArray()
    //     0xa4c910: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4c914: mov             x2, x0
    // 0xa4c918: ldur            x0, [fp, #-0x58]
    // 0xa4c91c: stur            x2, [fp, #-0x30]
    // 0xa4c920: StoreField: r2->field_f = r0
    //     0xa4c920: stur            w0, [x2, #0xf]
    // 0xa4c924: r16 = Instance_SizedBox
    //     0xa4c924: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xa4c928: ldr             x16, [x16, #0xc70]
    // 0xa4c92c: StoreField: r2->field_13 = r16
    //     0xa4c92c: stur            w16, [x2, #0x13]
    // 0xa4c930: ldur            x0, [fp, #-0x50]
    // 0xa4c934: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4c934: stur            w0, [x2, #0x17]
    // 0xa4c938: r1 = <Widget>
    //     0xa4c938: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4c93c: r0 = AllocateGrowableArray()
    //     0xa4c93c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4c940: mov             x1, x0
    // 0xa4c944: ldur            x0, [fp, #-0x30]
    // 0xa4c948: stur            x1, [fp, #-0x48]
    // 0xa4c94c: StoreField: r1->field_f = r0
    //     0xa4c94c: stur            w0, [x1, #0xf]
    // 0xa4c950: r2 = 6
    //     0xa4c950: movz            x2, #0x6
    // 0xa4c954: StoreField: r1->field_b = r2
    //     0xa4c954: stur            w2, [x1, #0xb]
    // 0xa4c958: r0 = Column()
    //     0xa4c958: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa4c95c: mov             x1, x0
    // 0xa4c960: r0 = Instance_Axis
    //     0xa4c960: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa4c964: stur            x1, [fp, #-0x30]
    // 0xa4c968: StoreField: r1->field_f = r0
    //     0xa4c968: stur            w0, [x1, #0xf]
    // 0xa4c96c: r2 = Instance_MainAxisAlignment
    //     0xa4c96c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa4c970: ldr             x2, [x2, #0xa08]
    // 0xa4c974: StoreField: r1->field_13 = r2
    //     0xa4c974: stur            w2, [x1, #0x13]
    // 0xa4c978: r2 = Instance_MainAxisSize
    //     0xa4c978: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4c97c: ldr             x2, [x2, #0xa10]
    // 0xa4c980: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4c980: stur            w2, [x1, #0x17]
    // 0xa4c984: r3 = Instance_CrossAxisAlignment
    //     0xa4c984: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4c988: ldr             x3, [x3, #0xa18]
    // 0xa4c98c: StoreField: r1->field_1b = r3
    //     0xa4c98c: stur            w3, [x1, #0x1b]
    // 0xa4c990: r4 = Instance_VerticalDirection
    //     0xa4c990: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4c994: ldr             x4, [x4, #0xa20]
    // 0xa4c998: StoreField: r1->field_23 = r4
    //     0xa4c998: stur            w4, [x1, #0x23]
    // 0xa4c99c: r5 = Instance_Clip
    //     0xa4c99c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4c9a0: ldr             x5, [x5, #0x38]
    // 0xa4c9a4: StoreField: r1->field_2b = r5
    //     0xa4c9a4: stur            w5, [x1, #0x2b]
    // 0xa4c9a8: StoreField: r1->field_2f = rZR
    //     0xa4c9a8: stur            xzr, [x1, #0x2f]
    // 0xa4c9ac: ldur            x6, [fp, #-0x48]
    // 0xa4c9b0: StoreField: r1->field_b = r6
    //     0xa4c9b0: stur            w6, [x1, #0xb]
    // 0xa4c9b4: r0 = Visibility()
    //     0xa4c9b4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4c9b8: mov             x1, x0
    // 0xa4c9bc: ldur            x0, [fp, #-0x30]
    // 0xa4c9c0: StoreField: r1->field_b = r0
    //     0xa4c9c0: stur            w0, [x1, #0xb]
    // 0xa4c9c4: r0 = Instance_SizedBox
    //     0xa4c9c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4c9c8: StoreField: r1->field_f = r0
    //     0xa4c9c8: stur            w0, [x1, #0xf]
    // 0xa4c9cc: ldur            x2, [fp, #-0x40]
    // 0xa4c9d0: StoreField: r1->field_13 = r2
    //     0xa4c9d0: stur            w2, [x1, #0x13]
    // 0xa4c9d4: r2 = false
    //     0xa4c9d4: add             x2, NULL, #0x30  ; false
    // 0xa4c9d8: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4c9d8: stur            w2, [x1, #0x17]
    // 0xa4c9dc: StoreField: r1->field_1b = r2
    //     0xa4c9dc: stur            w2, [x1, #0x1b]
    // 0xa4c9e0: StoreField: r1->field_1f = r2
    //     0xa4c9e0: stur            w2, [x1, #0x1f]
    // 0xa4c9e4: StoreField: r1->field_23 = r2
    //     0xa4c9e4: stur            w2, [x1, #0x23]
    // 0xa4c9e8: StoreField: r1->field_27 = r2
    //     0xa4c9e8: stur            w2, [x1, #0x27]
    // 0xa4c9ec: StoreField: r1->field_2b = r2
    //     0xa4c9ec: stur            w2, [x1, #0x2b]
    // 0xa4c9f0: mov             x4, x1
    // 0xa4c9f4: ldur            x3, [fp, #-0x38]
    // 0xa4c9f8: stur            x4, [fp, #-0x30]
    // 0xa4c9fc: LoadField: r1 = r3->field_b
    //     0xa4c9fc: ldur            w1, [x3, #0xb]
    // 0xa4ca00: LoadField: r5 = r3->field_f
    //     0xa4ca00: ldur            w5, [x3, #0xf]
    // 0xa4ca04: DecompressPointer r5
    //     0xa4ca04: add             x5, x5, HEAP, lsl #32
    // 0xa4ca08: LoadField: r6 = r5->field_b
    //     0xa4ca08: ldur            w6, [x5, #0xb]
    // 0xa4ca0c: r5 = LoadInt32Instr(r1)
    //     0xa4ca0c: sbfx            x5, x1, #1, #0x1f
    // 0xa4ca10: stur            x5, [fp, #-0x68]
    // 0xa4ca14: r1 = LoadInt32Instr(r6)
    //     0xa4ca14: sbfx            x1, x6, #1, #0x1f
    // 0xa4ca18: cmp             x5, x1
    // 0xa4ca1c: b.ne            #0xa4ca28
    // 0xa4ca20: mov             x1, x3
    // 0xa4ca24: r0 = _growToNextCapacity()
    //     0xa4ca24: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4ca28: ldur            x2, [fp, #-0x38]
    // 0xa4ca2c: ldur            x3, [fp, #-0x68]
    // 0xa4ca30: add             x4, x3, #1
    // 0xa4ca34: lsl             x0, x4, #1
    // 0xa4ca38: StoreField: r2->field_b = r0
    //     0xa4ca38: stur            w0, [x2, #0xb]
    // 0xa4ca3c: LoadField: r5 = r2->field_f
    //     0xa4ca3c: ldur            w5, [x2, #0xf]
    // 0xa4ca40: DecompressPointer r5
    //     0xa4ca40: add             x5, x5, HEAP, lsl #32
    // 0xa4ca44: mov             x1, x5
    // 0xa4ca48: ldur            x0, [fp, #-0x30]
    // 0xa4ca4c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4ca4c: add             x25, x1, x3, lsl #2
    //     0xa4ca50: add             x25, x25, #0xf
    //     0xa4ca54: str             w0, [x25]
    //     0xa4ca58: tbz             w0, #0, #0xa4ca74
    //     0xa4ca5c: ldurb           w16, [x1, #-1]
    //     0xa4ca60: ldurb           w17, [x0, #-1]
    //     0xa4ca64: and             x16, x17, x16, lsr #2
    //     0xa4ca68: tst             x16, HEAP, lsr #32
    //     0xa4ca6c: b.eq            #0xa4ca74
    //     0xa4ca70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4ca74: mov             x3, x4
    // 0xa4ca78: mov             x0, x5
    // 0xa4ca7c: stur            x3, [fp, #-0x68]
    // 0xa4ca80: LoadField: r1 = r0->field_b
    //     0xa4ca80: ldur            w1, [x0, #0xb]
    // 0xa4ca84: r0 = LoadInt32Instr(r1)
    //     0xa4ca84: sbfx            x0, x1, #1, #0x1f
    // 0xa4ca88: cmp             x3, x0
    // 0xa4ca8c: b.ne            #0xa4ca98
    // 0xa4ca90: mov             x1, x2
    // 0xa4ca94: r0 = _growToNextCapacity()
    //     0xa4ca94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4ca98: ldur            x4, [fp, #-8]
    // 0xa4ca9c: ldur            x2, [fp, #-0x38]
    // 0xa4caa0: ldur            x3, [fp, #-0x68]
    // 0xa4caa4: add             x0, x3, #1
    // 0xa4caa8: lsl             x1, x0, #1
    // 0xa4caac: StoreField: r2->field_b = r1
    //     0xa4caac: stur            w1, [x2, #0xb]
    // 0xa4cab0: mov             x1, x3
    // 0xa4cab4: cmp             x1, x0
    // 0xa4cab8: b.hs            #0xa4d618
    // 0xa4cabc: LoadField: r0 = r2->field_f
    //     0xa4cabc: ldur            w0, [x2, #0xf]
    // 0xa4cac0: DecompressPointer r0
    //     0xa4cac0: add             x0, x0, HEAP, lsl #32
    // 0xa4cac4: add             x1, x0, x3, lsl #2
    // 0xa4cac8: r16 = Instance_SizedBox
    //     0xa4cac8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xa4cacc: ldr             x16, [x16, #0x9f0]
    // 0xa4cad0: StoreField: r1->field_f = r16
    //     0xa4cad0: stur            w16, [x1, #0xf]
    // 0xa4cad4: LoadField: r0 = r4->field_b
    //     0xa4cad4: ldur            w0, [x4, #0xb]
    // 0xa4cad8: DecompressPointer r0
    //     0xa4cad8: add             x0, x0, HEAP, lsl #32
    // 0xa4cadc: cmp             w0, NULL
    // 0xa4cae0: b.eq            #0xa4d61c
    // 0xa4cae4: LoadField: r1 = r0->field_47
    //     0xa4cae4: ldur            w1, [x0, #0x47]
    // 0xa4cae8: DecompressPointer r1
    //     0xa4cae8: add             x1, x1, HEAP, lsl #32
    // 0xa4caec: stur            x1, [fp, #-0x40]
    // 0xa4caf0: cmp             w1, NULL
    // 0xa4caf4: b.ne            #0xa4cb00
    // 0xa4caf8: r0 = Null
    //     0xa4caf8: mov             x0, NULL
    // 0xa4cafc: b               #0xa4cb08
    // 0xa4cb00: LoadField: r0 = r1->field_f
    //     0xa4cb00: ldur            w0, [x1, #0xf]
    // 0xa4cb04: DecompressPointer r0
    //     0xa4cb04: add             x0, x0, HEAP, lsl #32
    // 0xa4cb08: cmp             w0, NULL
    // 0xa4cb0c: r16 = true
    //     0xa4cb0c: add             x16, NULL, #0x20  ; true
    // 0xa4cb10: r17 = false
    //     0xa4cb10: add             x17, NULL, #0x30  ; false
    // 0xa4cb14: csel            x3, x16, x17, ne
    // 0xa4cb18: stur            x3, [fp, #-0x30]
    // 0xa4cb1c: cmp             w1, NULL
    // 0xa4cb20: b.ne            #0xa4cb2c
    // 0xa4cb24: r0 = Null
    //     0xa4cb24: mov             x0, NULL
    // 0xa4cb28: b               #0xa4cb50
    // 0xa4cb2c: LoadField: r0 = r1->field_13
    //     0xa4cb2c: ldur            w0, [x1, #0x13]
    // 0xa4cb30: DecompressPointer r0
    //     0xa4cb30: add             x0, x0, HEAP, lsl #32
    // 0xa4cb34: cmp             w0, NULL
    // 0xa4cb38: b.ne            #0xa4cb44
    // 0xa4cb3c: r0 = Null
    //     0xa4cb3c: mov             x0, NULL
    // 0xa4cb40: b               #0xa4cb50
    // 0xa4cb44: LoadField: r5 = r0->field_7
    //     0xa4cb44: ldur            w5, [x0, #7]
    // 0xa4cb48: DecompressPointer r5
    //     0xa4cb48: add             x5, x5, HEAP, lsl #32
    // 0xa4cb4c: mov             x0, x5
    // 0xa4cb50: cmp             w0, NULL
    // 0xa4cb54: b.ne            #0xa4cb60
    // 0xa4cb58: r0 = 0
    //     0xa4cb58: movz            x0, #0
    // 0xa4cb5c: b               #0xa4cb70
    // 0xa4cb60: r5 = LoadInt32Instr(r0)
    //     0xa4cb60: sbfx            x5, x0, #1, #0x1f
    //     0xa4cb64: tbz             w0, #0, #0xa4cb6c
    //     0xa4cb68: ldur            x5, [x0, #7]
    // 0xa4cb6c: mov             x0, x5
    // 0xa4cb70: stur            x0, [fp, #-0x88]
    // 0xa4cb74: cmp             w1, NULL
    // 0xa4cb78: b.ne            #0xa4cb84
    // 0xa4cb7c: r5 = Null
    //     0xa4cb7c: mov             x5, NULL
    // 0xa4cb80: b               #0xa4cba8
    // 0xa4cb84: LoadField: r5 = r1->field_13
    //     0xa4cb84: ldur            w5, [x1, #0x13]
    // 0xa4cb88: DecompressPointer r5
    //     0xa4cb88: add             x5, x5, HEAP, lsl #32
    // 0xa4cb8c: cmp             w5, NULL
    // 0xa4cb90: b.ne            #0xa4cb9c
    // 0xa4cb94: r5 = Null
    //     0xa4cb94: mov             x5, NULL
    // 0xa4cb98: b               #0xa4cba8
    // 0xa4cb9c: LoadField: r6 = r5->field_b
    //     0xa4cb9c: ldur            w6, [x5, #0xb]
    // 0xa4cba0: DecompressPointer r6
    //     0xa4cba0: add             x6, x6, HEAP, lsl #32
    // 0xa4cba4: mov             x5, x6
    // 0xa4cba8: cmp             w5, NULL
    // 0xa4cbac: b.ne            #0xa4cbb8
    // 0xa4cbb0: r5 = 0
    //     0xa4cbb0: movz            x5, #0
    // 0xa4cbb4: b               #0xa4cbc8
    // 0xa4cbb8: r6 = LoadInt32Instr(r5)
    //     0xa4cbb8: sbfx            x6, x5, #1, #0x1f
    //     0xa4cbbc: tbz             w5, #0, #0xa4cbc4
    //     0xa4cbc0: ldur            x6, [x5, #7]
    // 0xa4cbc4: mov             x5, x6
    // 0xa4cbc8: stur            x5, [fp, #-0x80]
    // 0xa4cbcc: cmp             w1, NULL
    // 0xa4cbd0: b.ne            #0xa4cbdc
    // 0xa4cbd4: r6 = Null
    //     0xa4cbd4: mov             x6, NULL
    // 0xa4cbd8: b               #0xa4cc00
    // 0xa4cbdc: LoadField: r6 = r1->field_13
    //     0xa4cbdc: ldur            w6, [x1, #0x13]
    // 0xa4cbe0: DecompressPointer r6
    //     0xa4cbe0: add             x6, x6, HEAP, lsl #32
    // 0xa4cbe4: cmp             w6, NULL
    // 0xa4cbe8: b.ne            #0xa4cbf4
    // 0xa4cbec: r6 = Null
    //     0xa4cbec: mov             x6, NULL
    // 0xa4cbf0: b               #0xa4cc00
    // 0xa4cbf4: LoadField: r7 = r6->field_f
    //     0xa4cbf4: ldur            w7, [x6, #0xf]
    // 0xa4cbf8: DecompressPointer r7
    //     0xa4cbf8: add             x7, x7, HEAP, lsl #32
    // 0xa4cbfc: mov             x6, x7
    // 0xa4cc00: cmp             w6, NULL
    // 0xa4cc04: b.ne            #0xa4cc10
    // 0xa4cc08: r6 = 0
    //     0xa4cc08: movz            x6, #0
    // 0xa4cc0c: b               #0xa4cc20
    // 0xa4cc10: r7 = LoadInt32Instr(r6)
    //     0xa4cc10: sbfx            x7, x6, #1, #0x1f
    //     0xa4cc14: tbz             w6, #0, #0xa4cc1c
    //     0xa4cc18: ldur            x7, [x6, #7]
    // 0xa4cc1c: mov             x6, x7
    // 0xa4cc20: stur            x6, [fp, #-0x68]
    // 0xa4cc24: r0 = Color()
    //     0xa4cc24: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa4cc28: mov             x1, x0
    // 0xa4cc2c: r0 = Instance_ColorSpace
    //     0xa4cc2c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa4cc30: stur            x1, [fp, #-0x48]
    // 0xa4cc34: StoreField: r1->field_27 = r0
    //     0xa4cc34: stur            w0, [x1, #0x27]
    // 0xa4cc38: d0 = 1.000000
    //     0xa4cc38: fmov            d0, #1.00000000
    // 0xa4cc3c: StoreField: r1->field_7 = d0
    //     0xa4cc3c: stur            d0, [x1, #7]
    // 0xa4cc40: ldur            x2, [fp, #-0x88]
    // 0xa4cc44: ubfx            x2, x2, #0, #0x20
    // 0xa4cc48: and             w3, w2, #0xff
    // 0xa4cc4c: ubfx            x3, x3, #0, #0x20
    // 0xa4cc50: scvtf           d0, x3
    // 0xa4cc54: d1 = 255.000000
    //     0xa4cc54: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa4cc58: fdiv            d2, d0, d1
    // 0xa4cc5c: StoreField: r1->field_f = d2
    //     0xa4cc5c: stur            d2, [x1, #0xf]
    // 0xa4cc60: ldur            x2, [fp, #-0x80]
    // 0xa4cc64: ubfx            x2, x2, #0, #0x20
    // 0xa4cc68: and             w3, w2, #0xff
    // 0xa4cc6c: ubfx            x3, x3, #0, #0x20
    // 0xa4cc70: scvtf           d0, x3
    // 0xa4cc74: fdiv            d2, d0, d1
    // 0xa4cc78: ArrayStore: r1[0] = d2  ; List_8
    //     0xa4cc78: stur            d2, [x1, #0x17]
    // 0xa4cc7c: ldur            x2, [fp, #-0x68]
    // 0xa4cc80: ubfx            x2, x2, #0, #0x20
    // 0xa4cc84: and             w3, w2, #0xff
    // 0xa4cc88: ubfx            x3, x3, #0, #0x20
    // 0xa4cc8c: scvtf           d0, x3
    // 0xa4cc90: fdiv            d2, d0, d1
    // 0xa4cc94: StoreField: r1->field_1f = d2
    //     0xa4cc94: stur            d2, [x1, #0x1f]
    // 0xa4cc98: ldur            x2, [fp, #-0x40]
    // 0xa4cc9c: cmp             w2, NULL
    // 0xa4cca0: b.ne            #0xa4ccac
    // 0xa4cca4: r3 = Null
    //     0xa4cca4: mov             x3, NULL
    // 0xa4cca8: b               #0xa4ccd0
    // 0xa4ccac: LoadField: r3 = r2->field_13
    //     0xa4ccac: ldur            w3, [x2, #0x13]
    // 0xa4ccb0: DecompressPointer r3
    //     0xa4ccb0: add             x3, x3, HEAP, lsl #32
    // 0xa4ccb4: cmp             w3, NULL
    // 0xa4ccb8: b.ne            #0xa4ccc4
    // 0xa4ccbc: r3 = Null
    //     0xa4ccbc: mov             x3, NULL
    // 0xa4ccc0: b               #0xa4ccd0
    // 0xa4ccc4: LoadField: r4 = r3->field_7
    //     0xa4ccc4: ldur            w4, [x3, #7]
    // 0xa4ccc8: DecompressPointer r4
    //     0xa4ccc8: add             x4, x4, HEAP, lsl #32
    // 0xa4cccc: mov             x3, x4
    // 0xa4ccd0: cmp             w3, NULL
    // 0xa4ccd4: b.ne            #0xa4cce0
    // 0xa4ccd8: r3 = 0
    //     0xa4ccd8: movz            x3, #0
    // 0xa4ccdc: b               #0xa4ccf0
    // 0xa4cce0: r4 = LoadInt32Instr(r3)
    //     0xa4cce0: sbfx            x4, x3, #1, #0x1f
    //     0xa4cce4: tbz             w3, #0, #0xa4ccec
    //     0xa4cce8: ldur            x4, [x3, #7]
    // 0xa4ccec: mov             x3, x4
    // 0xa4ccf0: stur            x3, [fp, #-0x88]
    // 0xa4ccf4: cmp             w2, NULL
    // 0xa4ccf8: b.ne            #0xa4cd04
    // 0xa4ccfc: r4 = Null
    //     0xa4ccfc: mov             x4, NULL
    // 0xa4cd00: b               #0xa4cd28
    // 0xa4cd04: LoadField: r4 = r2->field_13
    //     0xa4cd04: ldur            w4, [x2, #0x13]
    // 0xa4cd08: DecompressPointer r4
    //     0xa4cd08: add             x4, x4, HEAP, lsl #32
    // 0xa4cd0c: cmp             w4, NULL
    // 0xa4cd10: b.ne            #0xa4cd1c
    // 0xa4cd14: r4 = Null
    //     0xa4cd14: mov             x4, NULL
    // 0xa4cd18: b               #0xa4cd28
    // 0xa4cd1c: LoadField: r5 = r4->field_b
    //     0xa4cd1c: ldur            w5, [x4, #0xb]
    // 0xa4cd20: DecompressPointer r5
    //     0xa4cd20: add             x5, x5, HEAP, lsl #32
    // 0xa4cd24: mov             x4, x5
    // 0xa4cd28: cmp             w4, NULL
    // 0xa4cd2c: b.ne            #0xa4cd38
    // 0xa4cd30: r4 = 0
    //     0xa4cd30: movz            x4, #0
    // 0xa4cd34: b               #0xa4cd48
    // 0xa4cd38: r5 = LoadInt32Instr(r4)
    //     0xa4cd38: sbfx            x5, x4, #1, #0x1f
    //     0xa4cd3c: tbz             w4, #0, #0xa4cd44
    //     0xa4cd40: ldur            x5, [x4, #7]
    // 0xa4cd44: mov             x4, x5
    // 0xa4cd48: stur            x4, [fp, #-0x80]
    // 0xa4cd4c: cmp             w2, NULL
    // 0xa4cd50: b.ne            #0xa4cd5c
    // 0xa4cd54: r2 = Null
    //     0xa4cd54: mov             x2, NULL
    // 0xa4cd58: b               #0xa4cd7c
    // 0xa4cd5c: LoadField: r5 = r2->field_13
    //     0xa4cd5c: ldur            w5, [x2, #0x13]
    // 0xa4cd60: DecompressPointer r5
    //     0xa4cd60: add             x5, x5, HEAP, lsl #32
    // 0xa4cd64: cmp             w5, NULL
    // 0xa4cd68: b.ne            #0xa4cd74
    // 0xa4cd6c: r2 = Null
    //     0xa4cd6c: mov             x2, NULL
    // 0xa4cd70: b               #0xa4cd7c
    // 0xa4cd74: LoadField: r2 = r5->field_f
    //     0xa4cd74: ldur            w2, [x5, #0xf]
    // 0xa4cd78: DecompressPointer r2
    //     0xa4cd78: add             x2, x2, HEAP, lsl #32
    // 0xa4cd7c: cmp             w2, NULL
    // 0xa4cd80: b.ne            #0xa4cd8c
    // 0xa4cd84: r5 = 0
    //     0xa4cd84: movz            x5, #0
    // 0xa4cd88: b               #0xa4cd98
    // 0xa4cd8c: r5 = LoadInt32Instr(r2)
    //     0xa4cd8c: sbfx            x5, x2, #1, #0x1f
    //     0xa4cd90: tbz             w2, #0, #0xa4cd98
    //     0xa4cd94: ldur            x5, [x2, #7]
    // 0xa4cd98: ldur            x2, [fp, #-8]
    // 0xa4cd9c: stur            x5, [fp, #-0x68]
    // 0xa4cda0: r0 = Color()
    //     0xa4cda0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa4cda4: mov             x3, x0
    // 0xa4cda8: r0 = Instance_ColorSpace
    //     0xa4cda8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa4cdac: stur            x3, [fp, #-0x40]
    // 0xa4cdb0: StoreField: r3->field_27 = r0
    //     0xa4cdb0: stur            w0, [x3, #0x27]
    // 0xa4cdb4: d0 = 0.700000
    //     0xa4cdb4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4cdb8: ldr             d0, [x17, #0xf48]
    // 0xa4cdbc: StoreField: r3->field_7 = d0
    //     0xa4cdbc: stur            d0, [x3, #7]
    // 0xa4cdc0: ldur            x0, [fp, #-0x88]
    // 0xa4cdc4: ubfx            x0, x0, #0, #0x20
    // 0xa4cdc8: and             w1, w0, #0xff
    // 0xa4cdcc: ubfx            x1, x1, #0, #0x20
    // 0xa4cdd0: scvtf           d0, x1
    // 0xa4cdd4: d1 = 255.000000
    //     0xa4cdd4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa4cdd8: fdiv            d2, d0, d1
    // 0xa4cddc: StoreField: r3->field_f = d2
    //     0xa4cddc: stur            d2, [x3, #0xf]
    // 0xa4cde0: ldur            x0, [fp, #-0x80]
    // 0xa4cde4: ubfx            x0, x0, #0, #0x20
    // 0xa4cde8: and             w1, w0, #0xff
    // 0xa4cdec: ubfx            x1, x1, #0, #0x20
    // 0xa4cdf0: scvtf           d0, x1
    // 0xa4cdf4: fdiv            d2, d0, d1
    // 0xa4cdf8: ArrayStore: r3[0] = d2  ; List_8
    //     0xa4cdf8: stur            d2, [x3, #0x17]
    // 0xa4cdfc: ldur            x0, [fp, #-0x68]
    // 0xa4ce00: ubfx            x0, x0, #0, #0x20
    // 0xa4ce04: and             w1, w0, #0xff
    // 0xa4ce08: ubfx            x1, x1, #0, #0x20
    // 0xa4ce0c: scvtf           d0, x1
    // 0xa4ce10: fdiv            d2, d0, d1
    // 0xa4ce14: StoreField: r3->field_1f = d2
    //     0xa4ce14: stur            d2, [x3, #0x1f]
    // 0xa4ce18: r1 = Null
    //     0xa4ce18: mov             x1, NULL
    // 0xa4ce1c: r2 = 4
    //     0xa4ce1c: movz            x2, #0x4
    // 0xa4ce20: r0 = AllocateArray()
    //     0xa4ce20: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4ce24: mov             x2, x0
    // 0xa4ce28: ldur            x0, [fp, #-0x48]
    // 0xa4ce2c: stur            x2, [fp, #-0x50]
    // 0xa4ce30: StoreField: r2->field_f = r0
    //     0xa4ce30: stur            w0, [x2, #0xf]
    // 0xa4ce34: ldur            x0, [fp, #-0x40]
    // 0xa4ce38: StoreField: r2->field_13 = r0
    //     0xa4ce38: stur            w0, [x2, #0x13]
    // 0xa4ce3c: r1 = <Color>
    //     0xa4ce3c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa4ce40: ldr             x1, [x1, #0xf80]
    // 0xa4ce44: r0 = AllocateGrowableArray()
    //     0xa4ce44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4ce48: mov             x1, x0
    // 0xa4ce4c: ldur            x0, [fp, #-0x50]
    // 0xa4ce50: stur            x1, [fp, #-0x40]
    // 0xa4ce54: StoreField: r1->field_f = r0
    //     0xa4ce54: stur            w0, [x1, #0xf]
    // 0xa4ce58: r2 = 4
    //     0xa4ce58: movz            x2, #0x4
    // 0xa4ce5c: StoreField: r1->field_b = r2
    //     0xa4ce5c: stur            w2, [x1, #0xb]
    // 0xa4ce60: r0 = LinearGradient()
    //     0xa4ce60: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xa4ce64: mov             x1, x0
    // 0xa4ce68: r0 = Instance_Alignment
    //     0xa4ce68: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xa4ce6c: ldr             x0, [x0, #0xce0]
    // 0xa4ce70: stur            x1, [fp, #-0x48]
    // 0xa4ce74: StoreField: r1->field_13 = r0
    //     0xa4ce74: stur            w0, [x1, #0x13]
    // 0xa4ce78: r0 = Instance_Alignment
    //     0xa4ce78: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xa4ce7c: ldr             x0, [x0, #0xce8]
    // 0xa4ce80: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4ce80: stur            w0, [x1, #0x17]
    // 0xa4ce84: r0 = Instance_TileMode
    //     0xa4ce84: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xa4ce88: ldr             x0, [x0, #0xcf0]
    // 0xa4ce8c: StoreField: r1->field_1b = r0
    //     0xa4ce8c: stur            w0, [x1, #0x1b]
    // 0xa4ce90: ldur            x0, [fp, #-0x40]
    // 0xa4ce94: StoreField: r1->field_7 = r0
    //     0xa4ce94: stur            w0, [x1, #7]
    // 0xa4ce98: r0 = BoxDecoration()
    //     0xa4ce98: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4ce9c: mov             x2, x0
    // 0xa4cea0: r0 = Instance_BorderRadius
    //     0xa4cea0: add             x0, PP, #0x48, lsl #12  ; [pp+0x48b20] Obj!BorderRadius@d5a2e1
    //     0xa4cea4: ldr             x0, [x0, #0xb20]
    // 0xa4cea8: stur            x2, [fp, #-0x40]
    // 0xa4ceac: StoreField: r2->field_13 = r0
    //     0xa4ceac: stur            w0, [x2, #0x13]
    // 0xa4ceb0: ldur            x0, [fp, #-0x48]
    // 0xa4ceb4: StoreField: r2->field_1b = r0
    //     0xa4ceb4: stur            w0, [x2, #0x1b]
    // 0xa4ceb8: r0 = Instance_BoxShape
    //     0xa4ceb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4cebc: ldr             x0, [x0, #0x80]
    // 0xa4cec0: StoreField: r2->field_23 = r0
    //     0xa4cec0: stur            w0, [x2, #0x23]
    // 0xa4cec4: ldur            x1, [fp, #-0x10]
    // 0xa4cec8: r0 = of()
    //     0xa4cec8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4cecc: LoadField: r1 = r0->field_87
    //     0xa4cecc: ldur            w1, [x0, #0x87]
    // 0xa4ced0: DecompressPointer r1
    //     0xa4ced0: add             x1, x1, HEAP, lsl #32
    // 0xa4ced4: LoadField: r0 = r1->field_7
    //     0xa4ced4: ldur            w0, [x1, #7]
    // 0xa4ced8: DecompressPointer r0
    //     0xa4ced8: add             x0, x0, HEAP, lsl #32
    // 0xa4cedc: r16 = 16.000000
    //     0xa4cedc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa4cee0: ldr             x16, [x16, #0x188]
    // 0xa4cee4: r30 = Instance_Color
    //     0xa4cee4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa4cee8: stp             lr, x16, [SP]
    // 0xa4ceec: mov             x1, x0
    // 0xa4cef0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4cef0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4cef4: ldr             x4, [x4, #0xaa0]
    // 0xa4cef8: r0 = copyWith()
    //     0xa4cef8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4cefc: stur            x0, [fp, #-0x48]
    // 0xa4cf00: r0 = TextSpan()
    //     0xa4cf00: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4cf04: mov             x2, x0
    // 0xa4cf08: r0 = "BUMPER OFFER\n"
    //     0xa4cf08: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xa4cf0c: ldr             x0, [x0, #0x338]
    // 0xa4cf10: stur            x2, [fp, #-0x50]
    // 0xa4cf14: StoreField: r2->field_b = r0
    //     0xa4cf14: stur            w0, [x2, #0xb]
    // 0xa4cf18: r0 = Instance__DeferringMouseCursor
    //     0xa4cf18: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4cf1c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4cf1c: stur            w0, [x2, #0x17]
    // 0xa4cf20: ldur            x1, [fp, #-0x48]
    // 0xa4cf24: StoreField: r2->field_7 = r1
    //     0xa4cf24: stur            w1, [x2, #7]
    // 0xa4cf28: ldur            x1, [fp, #-0x10]
    // 0xa4cf2c: r0 = of()
    //     0xa4cf2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4cf30: LoadField: r1 = r0->field_87
    //     0xa4cf30: ldur            w1, [x0, #0x87]
    // 0xa4cf34: DecompressPointer r1
    //     0xa4cf34: add             x1, x1, HEAP, lsl #32
    // 0xa4cf38: LoadField: r0 = r1->field_2b
    //     0xa4cf38: ldur            w0, [x1, #0x2b]
    // 0xa4cf3c: DecompressPointer r0
    //     0xa4cf3c: add             x0, x0, HEAP, lsl #32
    // 0xa4cf40: r16 = 12.000000
    //     0xa4cf40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa4cf44: ldr             x16, [x16, #0x9e8]
    // 0xa4cf48: r30 = Instance_Color
    //     0xa4cf48: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa4cf4c: stp             lr, x16, [SP]
    // 0xa4cf50: mov             x1, x0
    // 0xa4cf54: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4cf54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4cf58: ldr             x4, [x4, #0xaa0]
    // 0xa4cf5c: r0 = copyWith()
    //     0xa4cf5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4cf60: stur            x0, [fp, #-0x48]
    // 0xa4cf64: r0 = TextSpan()
    //     0xa4cf64: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4cf68: mov             x3, x0
    // 0xa4cf6c: r0 = "Unlocked from your last order"
    //     0xa4cf6c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xa4cf70: ldr             x0, [x0, #0x340]
    // 0xa4cf74: stur            x3, [fp, #-0x58]
    // 0xa4cf78: StoreField: r3->field_b = r0
    //     0xa4cf78: stur            w0, [x3, #0xb]
    // 0xa4cf7c: r0 = Instance__DeferringMouseCursor
    //     0xa4cf7c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4cf80: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4cf80: stur            w0, [x3, #0x17]
    // 0xa4cf84: ldur            x1, [fp, #-0x48]
    // 0xa4cf88: StoreField: r3->field_7 = r1
    //     0xa4cf88: stur            w1, [x3, #7]
    // 0xa4cf8c: r1 = Null
    //     0xa4cf8c: mov             x1, NULL
    // 0xa4cf90: r2 = 4
    //     0xa4cf90: movz            x2, #0x4
    // 0xa4cf94: r0 = AllocateArray()
    //     0xa4cf94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4cf98: mov             x2, x0
    // 0xa4cf9c: ldur            x0, [fp, #-0x50]
    // 0xa4cfa0: stur            x2, [fp, #-0x48]
    // 0xa4cfa4: StoreField: r2->field_f = r0
    //     0xa4cfa4: stur            w0, [x2, #0xf]
    // 0xa4cfa8: ldur            x0, [fp, #-0x58]
    // 0xa4cfac: StoreField: r2->field_13 = r0
    //     0xa4cfac: stur            w0, [x2, #0x13]
    // 0xa4cfb0: r1 = <InlineSpan>
    //     0xa4cfb0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa4cfb4: ldr             x1, [x1, #0xe40]
    // 0xa4cfb8: r0 = AllocateGrowableArray()
    //     0xa4cfb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4cfbc: mov             x1, x0
    // 0xa4cfc0: ldur            x0, [fp, #-0x48]
    // 0xa4cfc4: stur            x1, [fp, #-0x50]
    // 0xa4cfc8: StoreField: r1->field_f = r0
    //     0xa4cfc8: stur            w0, [x1, #0xf]
    // 0xa4cfcc: r2 = 4
    //     0xa4cfcc: movz            x2, #0x4
    // 0xa4cfd0: StoreField: r1->field_b = r2
    //     0xa4cfd0: stur            w2, [x1, #0xb]
    // 0xa4cfd4: r0 = TextSpan()
    //     0xa4cfd4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4cfd8: mov             x1, x0
    // 0xa4cfdc: ldur            x0, [fp, #-0x50]
    // 0xa4cfe0: stur            x1, [fp, #-0x48]
    // 0xa4cfe4: StoreField: r1->field_f = r0
    //     0xa4cfe4: stur            w0, [x1, #0xf]
    // 0xa4cfe8: r0 = Instance__DeferringMouseCursor
    //     0xa4cfe8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4cfec: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4cfec: stur            w0, [x1, #0x17]
    // 0xa4cff0: r0 = RichText()
    //     0xa4cff0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa4cff4: mov             x1, x0
    // 0xa4cff8: ldur            x2, [fp, #-0x48]
    // 0xa4cffc: stur            x0, [fp, #-0x48]
    // 0xa4d000: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4d000: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4d004: r0 = RichText()
    //     0xa4d004: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa4d008: ldur            x0, [fp, #-8]
    // 0xa4d00c: LoadField: r1 = r0->field_b
    //     0xa4d00c: ldur            w1, [x0, #0xb]
    // 0xa4d010: DecompressPointer r1
    //     0xa4d010: add             x1, x1, HEAP, lsl #32
    // 0xa4d014: cmp             w1, NULL
    // 0xa4d018: b.eq            #0xa4d620
    // 0xa4d01c: LoadField: r2 = r1->field_47
    //     0xa4d01c: ldur            w2, [x1, #0x47]
    // 0xa4d020: DecompressPointer r2
    //     0xa4d020: add             x2, x2, HEAP, lsl #32
    // 0xa4d024: cmp             w2, NULL
    // 0xa4d028: b.ne            #0xa4d034
    // 0xa4d02c: r6 = Null
    //     0xa4d02c: mov             x6, NULL
    // 0xa4d030: b               #0xa4d040
    // 0xa4d034: LoadField: r1 = r2->field_7
    //     0xa4d034: ldur            w1, [x2, #7]
    // 0xa4d038: DecompressPointer r1
    //     0xa4d038: add             x1, x1, HEAP, lsl #32
    // 0xa4d03c: mov             x6, x1
    // 0xa4d040: ldur            x4, [fp, #-0x38]
    // 0xa4d044: ldur            x5, [fp, #-0x30]
    // 0xa4d048: ldur            x3, [fp, #-0x48]
    // 0xa4d04c: stur            x6, [fp, #-0x50]
    // 0xa4d050: r1 = Null
    //     0xa4d050: mov             x1, NULL
    // 0xa4d054: r2 = 4
    //     0xa4d054: movz            x2, #0x4
    // 0xa4d058: r0 = AllocateArray()
    //     0xa4d058: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4d05c: mov             x1, x0
    // 0xa4d060: ldur            x0, [fp, #-0x50]
    // 0xa4d064: StoreField: r1->field_f = r0
    //     0xa4d064: stur            w0, [x1, #0xf]
    // 0xa4d068: r16 = "\n"
    //     0xa4d068: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xa4d06c: StoreField: r1->field_13 = r16
    //     0xa4d06c: stur            w16, [x1, #0x13]
    // 0xa4d070: str             x1, [SP]
    // 0xa4d074: r0 = _interpolate()
    //     0xa4d074: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa4d078: ldur            x1, [fp, #-0x10]
    // 0xa4d07c: stur            x0, [fp, #-0x50]
    // 0xa4d080: r0 = of()
    //     0xa4d080: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4d084: LoadField: r1 = r0->field_87
    //     0xa4d084: ldur            w1, [x0, #0x87]
    // 0xa4d088: DecompressPointer r1
    //     0xa4d088: add             x1, x1, HEAP, lsl #32
    // 0xa4d08c: LoadField: r0 = r1->field_7
    //     0xa4d08c: ldur            w0, [x1, #7]
    // 0xa4d090: DecompressPointer r0
    //     0xa4d090: add             x0, x0, HEAP, lsl #32
    // 0xa4d094: r16 = 32.000000
    //     0xa4d094: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa4d098: ldr             x16, [x16, #0x848]
    // 0xa4d09c: r30 = Instance_Color
    //     0xa4d09c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa4d0a0: stp             lr, x16, [SP]
    // 0xa4d0a4: mov             x1, x0
    // 0xa4d0a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4d0a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4d0ac: ldr             x4, [x4, #0xaa0]
    // 0xa4d0b0: r0 = copyWith()
    //     0xa4d0b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4d0b4: ldur            x1, [fp, #-0x10]
    // 0xa4d0b8: stur            x0, [fp, #-0x10]
    // 0xa4d0bc: r0 = of()
    //     0xa4d0bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4d0c0: LoadField: r1 = r0->field_87
    //     0xa4d0c0: ldur            w1, [x0, #0x87]
    // 0xa4d0c4: DecompressPointer r1
    //     0xa4d0c4: add             x1, x1, HEAP, lsl #32
    // 0xa4d0c8: LoadField: r0 = r1->field_2b
    //     0xa4d0c8: ldur            w0, [x1, #0x2b]
    // 0xa4d0cc: DecompressPointer r0
    //     0xa4d0cc: add             x0, x0, HEAP, lsl #32
    // 0xa4d0d0: r16 = Instance_Color
    //     0xa4d0d0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa4d0d4: r30 = 16.000000
    //     0xa4d0d4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa4d0d8: ldr             lr, [lr, #0x188]
    // 0xa4d0dc: stp             lr, x16, [SP]
    // 0xa4d0e0: mov             x1, x0
    // 0xa4d0e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa4d0e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa4d0e8: ldr             x4, [x4, #0x9b8]
    // 0xa4d0ec: r0 = copyWith()
    //     0xa4d0ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4d0f0: stur            x0, [fp, #-0x58]
    // 0xa4d0f4: r0 = TextSpan()
    //     0xa4d0f4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4d0f8: mov             x3, x0
    // 0xa4d0fc: r0 = "OFF"
    //     0xa4d0fc: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xa4d100: ldr             x0, [x0, #0x348]
    // 0xa4d104: stur            x3, [fp, #-0x60]
    // 0xa4d108: StoreField: r3->field_b = r0
    //     0xa4d108: stur            w0, [x3, #0xb]
    // 0xa4d10c: r0 = Instance__DeferringMouseCursor
    //     0xa4d10c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4d110: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4d110: stur            w0, [x3, #0x17]
    // 0xa4d114: ldur            x1, [fp, #-0x58]
    // 0xa4d118: StoreField: r3->field_7 = r1
    //     0xa4d118: stur            w1, [x3, #7]
    // 0xa4d11c: r1 = Null
    //     0xa4d11c: mov             x1, NULL
    // 0xa4d120: r2 = 2
    //     0xa4d120: movz            x2, #0x2
    // 0xa4d124: r0 = AllocateArray()
    //     0xa4d124: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4d128: mov             x2, x0
    // 0xa4d12c: ldur            x0, [fp, #-0x60]
    // 0xa4d130: stur            x2, [fp, #-0x58]
    // 0xa4d134: StoreField: r2->field_f = r0
    //     0xa4d134: stur            w0, [x2, #0xf]
    // 0xa4d138: r1 = <InlineSpan>
    //     0xa4d138: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa4d13c: ldr             x1, [x1, #0xe40]
    // 0xa4d140: r0 = AllocateGrowableArray()
    //     0xa4d140: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4d144: mov             x1, x0
    // 0xa4d148: ldur            x0, [fp, #-0x58]
    // 0xa4d14c: stur            x1, [fp, #-0x60]
    // 0xa4d150: StoreField: r1->field_f = r0
    //     0xa4d150: stur            w0, [x1, #0xf]
    // 0xa4d154: r0 = 2
    //     0xa4d154: movz            x0, #0x2
    // 0xa4d158: StoreField: r1->field_b = r0
    //     0xa4d158: stur            w0, [x1, #0xb]
    // 0xa4d15c: r0 = TextSpan()
    //     0xa4d15c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4d160: mov             x1, x0
    // 0xa4d164: ldur            x0, [fp, #-0x50]
    // 0xa4d168: stur            x1, [fp, #-0x58]
    // 0xa4d16c: StoreField: r1->field_b = r0
    //     0xa4d16c: stur            w0, [x1, #0xb]
    // 0xa4d170: ldur            x0, [fp, #-0x60]
    // 0xa4d174: StoreField: r1->field_f = r0
    //     0xa4d174: stur            w0, [x1, #0xf]
    // 0xa4d178: r0 = Instance__DeferringMouseCursor
    //     0xa4d178: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4d17c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4d17c: stur            w0, [x1, #0x17]
    // 0xa4d180: ldur            x0, [fp, #-0x10]
    // 0xa4d184: StoreField: r1->field_7 = r0
    //     0xa4d184: stur            w0, [x1, #7]
    // 0xa4d188: r0 = RichText()
    //     0xa4d188: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa4d18c: stur            x0, [fp, #-0x10]
    // 0xa4d190: r16 = Instance_TextAlign
    //     0xa4d190: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa4d194: str             x16, [SP]
    // 0xa4d198: mov             x1, x0
    // 0xa4d19c: ldur            x2, [fp, #-0x58]
    // 0xa4d1a0: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xa4d1a0: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xa4d1a4: ldr             x4, [x4, #0x350]
    // 0xa4d1a8: r0 = RichText()
    //     0xa4d1a8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa4d1ac: r1 = Null
    //     0xa4d1ac: mov             x1, NULL
    // 0xa4d1b0: r2 = 6
    //     0xa4d1b0: movz            x2, #0x6
    // 0xa4d1b4: r0 = AllocateArray()
    //     0xa4d1b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4d1b8: mov             x2, x0
    // 0xa4d1bc: ldur            x0, [fp, #-0x48]
    // 0xa4d1c0: stur            x2, [fp, #-0x50]
    // 0xa4d1c4: StoreField: r2->field_f = r0
    //     0xa4d1c4: stur            w0, [x2, #0xf]
    // 0xa4d1c8: r16 = Instance_VerticalDivider
    //     0xa4d1c8: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0xa4d1cc: ldr             x16, [x16, #0x760]
    // 0xa4d1d0: StoreField: r2->field_13 = r16
    //     0xa4d1d0: stur            w16, [x2, #0x13]
    // 0xa4d1d4: ldur            x0, [fp, #-0x10]
    // 0xa4d1d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4d1d8: stur            w0, [x2, #0x17]
    // 0xa4d1dc: r1 = <Widget>
    //     0xa4d1dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4d1e0: r0 = AllocateGrowableArray()
    //     0xa4d1e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4d1e4: mov             x1, x0
    // 0xa4d1e8: ldur            x0, [fp, #-0x50]
    // 0xa4d1ec: stur            x1, [fp, #-0x10]
    // 0xa4d1f0: StoreField: r1->field_f = r0
    //     0xa4d1f0: stur            w0, [x1, #0xf]
    // 0xa4d1f4: r0 = 6
    //     0xa4d1f4: movz            x0, #0x6
    // 0xa4d1f8: StoreField: r1->field_b = r0
    //     0xa4d1f8: stur            w0, [x1, #0xb]
    // 0xa4d1fc: r0 = Row()
    //     0xa4d1fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4d200: mov             x1, x0
    // 0xa4d204: r0 = Instance_Axis
    //     0xa4d204: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa4d208: stur            x1, [fp, #-0x48]
    // 0xa4d20c: StoreField: r1->field_f = r0
    //     0xa4d20c: stur            w0, [x1, #0xf]
    // 0xa4d210: r0 = Instance_MainAxisAlignment
    //     0xa4d210: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa4d214: ldr             x0, [x0, #0xa8]
    // 0xa4d218: StoreField: r1->field_13 = r0
    //     0xa4d218: stur            w0, [x1, #0x13]
    // 0xa4d21c: r0 = Instance_MainAxisSize
    //     0xa4d21c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4d220: ldr             x0, [x0, #0xa10]
    // 0xa4d224: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4d224: stur            w0, [x1, #0x17]
    // 0xa4d228: r2 = Instance_CrossAxisAlignment
    //     0xa4d228: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4d22c: ldr             x2, [x2, #0xa18]
    // 0xa4d230: StoreField: r1->field_1b = r2
    //     0xa4d230: stur            w2, [x1, #0x1b]
    // 0xa4d234: r2 = Instance_VerticalDirection
    //     0xa4d234: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4d238: ldr             x2, [x2, #0xa20]
    // 0xa4d23c: StoreField: r1->field_23 = r2
    //     0xa4d23c: stur            w2, [x1, #0x23]
    // 0xa4d240: r3 = Instance_Clip
    //     0xa4d240: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4d244: ldr             x3, [x3, #0x38]
    // 0xa4d248: StoreField: r1->field_2b = r3
    //     0xa4d248: stur            w3, [x1, #0x2b]
    // 0xa4d24c: StoreField: r1->field_2f = rZR
    //     0xa4d24c: stur            xzr, [x1, #0x2f]
    // 0xa4d250: ldur            x4, [fp, #-0x10]
    // 0xa4d254: StoreField: r1->field_b = r4
    //     0xa4d254: stur            w4, [x1, #0xb]
    // 0xa4d258: r0 = Padding()
    //     0xa4d258: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4d25c: mov             x1, x0
    // 0xa4d260: r0 = Instance_EdgeInsets
    //     0xa4d260: add             x0, PP, #0x48, lsl #12  ; [pp+0x48768] Obj!EdgeInsets@d585b1
    //     0xa4d264: ldr             x0, [x0, #0x768]
    // 0xa4d268: stur            x1, [fp, #-0x10]
    // 0xa4d26c: StoreField: r1->field_f = r0
    //     0xa4d26c: stur            w0, [x1, #0xf]
    // 0xa4d270: ldur            x0, [fp, #-0x48]
    // 0xa4d274: StoreField: r1->field_b = r0
    //     0xa4d274: stur            w0, [x1, #0xb]
    // 0xa4d278: r0 = IntrinsicHeight()
    //     0xa4d278: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xa4d27c: mov             x1, x0
    // 0xa4d280: ldur            x0, [fp, #-0x10]
    // 0xa4d284: stur            x1, [fp, #-0x48]
    // 0xa4d288: StoreField: r1->field_b = r0
    //     0xa4d288: stur            w0, [x1, #0xb]
    // 0xa4d28c: r0 = Container()
    //     0xa4d28c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4d290: stur            x0, [fp, #-0x10]
    // 0xa4d294: r16 = 100.000000
    //     0xa4d294: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xa4d298: ldur            lr, [fp, #-0x40]
    // 0xa4d29c: stp             lr, x16, [SP, #8]
    // 0xa4d2a0: ldur            x16, [fp, #-0x48]
    // 0xa4d2a4: str             x16, [SP]
    // 0xa4d2a8: mov             x1, x0
    // 0xa4d2ac: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xa4d2ac: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xa4d2b0: ldr             x4, [x4, #0xc78]
    // 0xa4d2b4: r0 = Container()
    //     0xa4d2b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4d2b8: r1 = <Path>
    //     0xa4d2b8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xa4d2bc: ldr             x1, [x1, #0xd30]
    // 0xa4d2c0: r0 = MovieTicketClipper()
    //     0xa4d2c0: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xa4d2c4: stur            x0, [fp, #-0x40]
    // 0xa4d2c8: r0 = ClipPath()
    //     0xa4d2c8: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xa4d2cc: mov             x1, x0
    // 0xa4d2d0: ldur            x0, [fp, #-0x40]
    // 0xa4d2d4: stur            x1, [fp, #-0x48]
    // 0xa4d2d8: StoreField: r1->field_f = r0
    //     0xa4d2d8: stur            w0, [x1, #0xf]
    // 0xa4d2dc: r0 = Instance_Clip
    //     0xa4d2dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa4d2e0: ldr             x0, [x0, #0x138]
    // 0xa4d2e4: StoreField: r1->field_13 = r0
    //     0xa4d2e4: stur            w0, [x1, #0x13]
    // 0xa4d2e8: ldur            x0, [fp, #-0x10]
    // 0xa4d2ec: StoreField: r1->field_b = r0
    //     0xa4d2ec: stur            w0, [x1, #0xb]
    // 0xa4d2f0: r0 = Visibility()
    //     0xa4d2f0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4d2f4: mov             x2, x0
    // 0xa4d2f8: ldur            x0, [fp, #-0x48]
    // 0xa4d2fc: stur            x2, [fp, #-0x10]
    // 0xa4d300: StoreField: r2->field_b = r0
    //     0xa4d300: stur            w0, [x2, #0xb]
    // 0xa4d304: r0 = Instance_SizedBox
    //     0xa4d304: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4d308: StoreField: r2->field_f = r0
    //     0xa4d308: stur            w0, [x2, #0xf]
    // 0xa4d30c: ldur            x0, [fp, #-0x30]
    // 0xa4d310: StoreField: r2->field_13 = r0
    //     0xa4d310: stur            w0, [x2, #0x13]
    // 0xa4d314: r0 = false
    //     0xa4d314: add             x0, NULL, #0x30  ; false
    // 0xa4d318: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4d318: stur            w0, [x2, #0x17]
    // 0xa4d31c: StoreField: r2->field_1b = r0
    //     0xa4d31c: stur            w0, [x2, #0x1b]
    // 0xa4d320: StoreField: r2->field_1f = r0
    //     0xa4d320: stur            w0, [x2, #0x1f]
    // 0xa4d324: StoreField: r2->field_23 = r0
    //     0xa4d324: stur            w0, [x2, #0x23]
    // 0xa4d328: StoreField: r2->field_27 = r0
    //     0xa4d328: stur            w0, [x2, #0x27]
    // 0xa4d32c: StoreField: r2->field_2b = r0
    //     0xa4d32c: stur            w0, [x2, #0x2b]
    // 0xa4d330: ldur            x0, [fp, #-0x38]
    // 0xa4d334: LoadField: r1 = r0->field_b
    //     0xa4d334: ldur            w1, [x0, #0xb]
    // 0xa4d338: LoadField: r3 = r0->field_f
    //     0xa4d338: ldur            w3, [x0, #0xf]
    // 0xa4d33c: DecompressPointer r3
    //     0xa4d33c: add             x3, x3, HEAP, lsl #32
    // 0xa4d340: LoadField: r4 = r3->field_b
    //     0xa4d340: ldur            w4, [x3, #0xb]
    // 0xa4d344: r3 = LoadInt32Instr(r1)
    //     0xa4d344: sbfx            x3, x1, #1, #0x1f
    // 0xa4d348: stur            x3, [fp, #-0x68]
    // 0xa4d34c: r1 = LoadInt32Instr(r4)
    //     0xa4d34c: sbfx            x1, x4, #1, #0x1f
    // 0xa4d350: cmp             x3, x1
    // 0xa4d354: b.ne            #0xa4d360
    // 0xa4d358: mov             x1, x0
    // 0xa4d35c: r0 = _growToNextCapacity()
    //     0xa4d35c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4d360: ldur            x4, [fp, #-8]
    // 0xa4d364: ldur            x2, [fp, #-0x38]
    // 0xa4d368: ldur            x3, [fp, #-0x68]
    // 0xa4d36c: add             x0, x3, #1
    // 0xa4d370: lsl             x1, x0, #1
    // 0xa4d374: StoreField: r2->field_b = r1
    //     0xa4d374: stur            w1, [x2, #0xb]
    // 0xa4d378: LoadField: r1 = r2->field_f
    //     0xa4d378: ldur            w1, [x2, #0xf]
    // 0xa4d37c: DecompressPointer r1
    //     0xa4d37c: add             x1, x1, HEAP, lsl #32
    // 0xa4d380: ldur            x0, [fp, #-0x10]
    // 0xa4d384: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4d384: add             x25, x1, x3, lsl #2
    //     0xa4d388: add             x25, x25, #0xf
    //     0xa4d38c: str             w0, [x25]
    //     0xa4d390: tbz             w0, #0, #0xa4d3ac
    //     0xa4d394: ldurb           w16, [x1, #-1]
    //     0xa4d398: ldurb           w17, [x0, #-1]
    //     0xa4d39c: and             x16, x17, x16, lsr #2
    //     0xa4d3a0: tst             x16, HEAP, lsr #32
    //     0xa4d3a4: b.eq            #0xa4d3ac
    //     0xa4d3a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4d3ac: LoadField: r0 = r4->field_b
    //     0xa4d3ac: ldur            w0, [x4, #0xb]
    // 0xa4d3b0: DecompressPointer r0
    //     0xa4d3b0: add             x0, x0, HEAP, lsl #32
    // 0xa4d3b4: cmp             w0, NULL
    // 0xa4d3b8: b.eq            #0xa4d624
    // 0xa4d3bc: LoadField: r1 = r0->field_43
    //     0xa4d3bc: ldur            w1, [x0, #0x43]
    // 0xa4d3c0: DecompressPointer r1
    //     0xa4d3c0: add             x1, x1, HEAP, lsl #32
    // 0xa4d3c4: r0 = LoadClassIdInstr(r1)
    //     0xa4d3c4: ldur            x0, [x1, #-1]
    //     0xa4d3c8: ubfx            x0, x0, #0xc, #0x14
    // 0xa4d3cc: r16 = "search_page"
    //     0xa4d3cc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xa4d3d0: ldr             x16, [x16, #0xe58]
    // 0xa4d3d4: stp             x16, x1, [SP]
    // 0xa4d3d8: mov             lr, x0
    // 0xa4d3dc: ldr             lr, [x21, lr, lsl #3]
    // 0xa4d3e0: blr             lr
    // 0xa4d3e4: tbnz            w0, #4, #0xa4d3f0
    // 0xa4d3e8: d0 = 16.000000
    //     0xa4d3e8: fmov            d0, #16.00000000
    // 0xa4d3ec: b               #0xa4d3f4
    // 0xa4d3f0: d0 = 2.000000
    //     0xa4d3f0: fmov            d0, #2.00000000
    // 0xa4d3f4: ldur            x1, [fp, #-8]
    // 0xa4d3f8: stur            d0, [fp, #-0x90]
    // 0xa4d3fc: LoadField: r0 = r1->field_b
    //     0xa4d3fc: ldur            w0, [x1, #0xb]
    // 0xa4d400: DecompressPointer r0
    //     0xa4d400: add             x0, x0, HEAP, lsl #32
    // 0xa4d404: cmp             w0, NULL
    // 0xa4d408: b.eq            #0xa4d628
    // 0xa4d40c: LoadField: r2 = r0->field_43
    //     0xa4d40c: ldur            w2, [x0, #0x43]
    // 0xa4d410: DecompressPointer r2
    //     0xa4d410: add             x2, x2, HEAP, lsl #32
    // 0xa4d414: r0 = LoadClassIdInstr(r2)
    //     0xa4d414: ldur            x0, [x2, #-1]
    //     0xa4d418: ubfx            x0, x0, #0xc, #0x14
    // 0xa4d41c: r16 = "search_page"
    //     0xa4d41c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xa4d420: ldr             x16, [x16, #0xe58]
    // 0xa4d424: stp             x16, x2, [SP]
    // 0xa4d428: mov             lr, x0
    // 0xa4d42c: ldr             lr, [x21, lr, lsl #3]
    // 0xa4d430: blr             lr
    // 0xa4d434: tbnz            w0, #4, #0xa4d444
    // 0xa4d438: d1 = 0.666667
    //     0xa4d438: add             x17, PP, #0x50, lsl #12  ; [pp+0x50e98] IMM: double(0.6666666666666666) from 0x3fe5555555555555
    //     0xa4d43c: ldr             d1, [x17, #0xe98]
    // 0xa4d440: b               #0xa4d44c
    // 0xa4d444: d1 = 0.540541
    //     0xa4d444: add             x17, PP, #0x5a, lsl #12  ; [pp+0x5aa80] IMM: double(0.5405405405405405) from 0x3fe14c1bacf914c1
    //     0xa4d448: ldr             d1, [x17, #0xa80]
    // 0xa4d44c: ldur            x0, [fp, #-8]
    // 0xa4d450: ldur            x1, [fp, #-0x38]
    // 0xa4d454: ldur            d0, [fp, #-0x90]
    // 0xa4d458: stur            d1, [fp, #-0x98]
    // 0xa4d45c: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xa4d45c: bl              #0xa4d630  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xa4d460: mov             x1, x0
    // 0xa4d464: r0 = 2
    //     0xa4d464: movz            x0, #0x2
    // 0xa4d468: stur            x1, [fp, #-0x10]
    // 0xa4d46c: StoreField: r1->field_7 = r0
    //     0xa4d46c: stur            x0, [x1, #7]
    // 0xa4d470: ldur            d0, [fp, #-0x90]
    // 0xa4d474: StoreField: r1->field_f = d0
    //     0xa4d474: stur            d0, [x1, #0xf]
    // 0xa4d478: d0 = 2.000000
    //     0xa4d478: fmov            d0, #2.00000000
    // 0xa4d47c: ArrayStore: r1[0] = d0  ; List_8
    //     0xa4d47c: stur            d0, [x1, #0x17]
    // 0xa4d480: ldur            d0, [fp, #-0x98]
    // 0xa4d484: StoreField: r1->field_1f = d0
    //     0xa4d484: stur            d0, [x1, #0x1f]
    // 0xa4d488: ldur            x0, [fp, #-8]
    // 0xa4d48c: LoadField: r2 = r0->field_b
    //     0xa4d48c: ldur            w2, [x0, #0xb]
    // 0xa4d490: DecompressPointer r2
    //     0xa4d490: add             x2, x2, HEAP, lsl #32
    // 0xa4d494: cmp             w2, NULL
    // 0xa4d498: b.eq            #0xa4d62c
    // 0xa4d49c: LoadField: r0 = r2->field_b
    //     0xa4d49c: ldur            w0, [x2, #0xb]
    // 0xa4d4a0: DecompressPointer r0
    //     0xa4d4a0: add             x0, x0, HEAP, lsl #32
    // 0xa4d4a4: r2 = LoadClassIdInstr(r0)
    //     0xa4d4a4: ldur            x2, [x0, #-1]
    //     0xa4d4a8: ubfx            x2, x2, #0xc, #0x14
    // 0xa4d4ac: str             x0, [SP]
    // 0xa4d4b0: mov             x0, x2
    // 0xa4d4b4: r0 = GDT[cid_x0 + 0xc898]()
    //     0xa4d4b4: movz            x17, #0xc898
    //     0xa4d4b8: add             lr, x0, x17
    //     0xa4d4bc: ldr             lr, [x21, lr, lsl #3]
    //     0xa4d4c0: blr             lr
    // 0xa4d4c4: ldur            x2, [fp, #-0x28]
    // 0xa4d4c8: r1 = Function '<anonymous closure>':.
    //     0xa4d4c8: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aa88] AnonymousClosure: (0xa4d63c), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4d4cc: ldr             x1, [x1, #0xa88]
    // 0xa4d4d0: stur            x0, [fp, #-8]
    // 0xa4d4d4: r0 = AllocateClosure()
    //     0xa4d4d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4d4d8: stur            x0, [fp, #-0x28]
    // 0xa4d4dc: r0 = GridView()
    //     0xa4d4dc: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xa4d4e0: mov             x1, x0
    // 0xa4d4e4: ldur            x2, [fp, #-0x10]
    // 0xa4d4e8: ldur            x3, [fp, #-0x28]
    // 0xa4d4ec: ldur            x5, [fp, #-8]
    // 0xa4d4f0: stur            x0, [fp, #-8]
    // 0xa4d4f4: r0 = GridView.builder()
    //     0xa4d4f4: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xa4d4f8: ldur            x0, [fp, #-0x38]
    // 0xa4d4fc: LoadField: r1 = r0->field_b
    //     0xa4d4fc: ldur            w1, [x0, #0xb]
    // 0xa4d500: LoadField: r2 = r0->field_f
    //     0xa4d500: ldur            w2, [x0, #0xf]
    // 0xa4d504: DecompressPointer r2
    //     0xa4d504: add             x2, x2, HEAP, lsl #32
    // 0xa4d508: LoadField: r3 = r2->field_b
    //     0xa4d508: ldur            w3, [x2, #0xb]
    // 0xa4d50c: r2 = LoadInt32Instr(r1)
    //     0xa4d50c: sbfx            x2, x1, #1, #0x1f
    // 0xa4d510: stur            x2, [fp, #-0x68]
    // 0xa4d514: r1 = LoadInt32Instr(r3)
    //     0xa4d514: sbfx            x1, x3, #1, #0x1f
    // 0xa4d518: cmp             x2, x1
    // 0xa4d51c: b.ne            #0xa4d528
    // 0xa4d520: mov             x1, x0
    // 0xa4d524: r0 = _growToNextCapacity()
    //     0xa4d524: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4d528: ldur            x4, [fp, #-0x20]
    // 0xa4d52c: ldur            x2, [fp, #-0x38]
    // 0xa4d530: ldur            x5, [fp, #-0x18]
    // 0xa4d534: ldur            x3, [fp, #-0x68]
    // 0xa4d538: add             x0, x3, #1
    // 0xa4d53c: lsl             x1, x0, #1
    // 0xa4d540: StoreField: r2->field_b = r1
    //     0xa4d540: stur            w1, [x2, #0xb]
    // 0xa4d544: LoadField: r1 = r2->field_f
    //     0xa4d544: ldur            w1, [x2, #0xf]
    // 0xa4d548: DecompressPointer r1
    //     0xa4d548: add             x1, x1, HEAP, lsl #32
    // 0xa4d54c: ldur            x0, [fp, #-8]
    // 0xa4d550: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4d550: add             x25, x1, x3, lsl #2
    //     0xa4d554: add             x25, x25, #0xf
    //     0xa4d558: str             w0, [x25]
    //     0xa4d55c: tbz             w0, #0, #0xa4d578
    //     0xa4d560: ldurb           w16, [x1, #-1]
    //     0xa4d564: ldurb           w17, [x0, #-1]
    //     0xa4d568: and             x16, x17, x16, lsr #2
    //     0xa4d56c: tst             x16, HEAP, lsr #32
    //     0xa4d570: b.eq            #0xa4d578
    //     0xa4d574: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4d578: r0 = Column()
    //     0xa4d578: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa4d57c: mov             x1, x0
    // 0xa4d580: r0 = Instance_Axis
    //     0xa4d580: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa4d584: stur            x1, [fp, #-8]
    // 0xa4d588: StoreField: r1->field_f = r0
    //     0xa4d588: stur            w0, [x1, #0xf]
    // 0xa4d58c: ldur            x0, [fp, #-0x20]
    // 0xa4d590: StoreField: r1->field_13 = r0
    //     0xa4d590: stur            w0, [x1, #0x13]
    // 0xa4d594: r0 = Instance_MainAxisSize
    //     0xa4d594: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4d598: ldr             x0, [x0, #0xa10]
    // 0xa4d59c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4d59c: stur            w0, [x1, #0x17]
    // 0xa4d5a0: ldur            x0, [fp, #-0x18]
    // 0xa4d5a4: StoreField: r1->field_1b = r0
    //     0xa4d5a4: stur            w0, [x1, #0x1b]
    // 0xa4d5a8: r0 = Instance_VerticalDirection
    //     0xa4d5a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4d5ac: ldr             x0, [x0, #0xa20]
    // 0xa4d5b0: StoreField: r1->field_23 = r0
    //     0xa4d5b0: stur            w0, [x1, #0x23]
    // 0xa4d5b4: r0 = Instance_Clip
    //     0xa4d5b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4d5b8: ldr             x0, [x0, #0x38]
    // 0xa4d5bc: StoreField: r1->field_2b = r0
    //     0xa4d5bc: stur            w0, [x1, #0x2b]
    // 0xa4d5c0: StoreField: r1->field_2f = rZR
    //     0xa4d5c0: stur            xzr, [x1, #0x2f]
    // 0xa4d5c4: ldur            x0, [fp, #-0x38]
    // 0xa4d5c8: StoreField: r1->field_b = r0
    //     0xa4d5c8: stur            w0, [x1, #0xb]
    // 0xa4d5cc: r0 = Padding()
    //     0xa4d5cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4d5d0: r1 = Instance_EdgeInsets
    //     0xa4d5d0: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xa4d5d4: ldr             x1, [x1, #0x110]
    // 0xa4d5d8: StoreField: r0->field_f = r1
    //     0xa4d5d8: stur            w1, [x0, #0xf]
    // 0xa4d5dc: ldur            x1, [fp, #-8]
    // 0xa4d5e0: StoreField: r0->field_b = r1
    //     0xa4d5e0: stur            w1, [x0, #0xb]
    // 0xa4d5e4: LeaveFrame
    //     0xa4d5e4: mov             SP, fp
    //     0xa4d5e8: ldp             fp, lr, [SP], #0x10
    // 0xa4d5ec: ret
    //     0xa4d5ec: ret             
    // 0xa4d5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d5f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d5f4: b               #0xa4b92c
    // 0xa4d5f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d5f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d5fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d5fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d600: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d600: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d604: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d604: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d608: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d608: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d60c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d60c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d610: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d614: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d618: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4d618: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4d61c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d61c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d620: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d624: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d628: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4d628: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4d62c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d62c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa4d63c, size: 0x1e70
    // 0xa4d63c: EnterFrame
    //     0xa4d63c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d640: mov             fp, SP
    // 0xa4d644: AllocStack(0x98)
    //     0xa4d644: sub             SP, SP, #0x98
    // 0xa4d648: SetupParameters()
    //     0xa4d648: ldr             x0, [fp, #0x20]
    //     0xa4d64c: ldur            w1, [x0, #0x17]
    //     0xa4d650: add             x1, x1, HEAP, lsl #32
    //     0xa4d654: stur            x1, [fp, #-8]
    // 0xa4d658: CheckStackOverflow
    //     0xa4d658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d65c: cmp             SP, x16
    //     0xa4d660: b.ls            #0xa4f41c
    // 0xa4d664: r1 = 1
    //     0xa4d664: movz            x1, #0x1
    // 0xa4d668: r0 = AllocateContext()
    //     0xa4d668: bl              #0x16f6108  ; AllocateContextStub
    // 0xa4d66c: mov             x1, x0
    // 0xa4d670: ldur            x0, [fp, #-8]
    // 0xa4d674: stur            x1, [fp, #-0x10]
    // 0xa4d678: StoreField: r1->field_b = r0
    //     0xa4d678: stur            w0, [x1, #0xb]
    // 0xa4d67c: ldr             x2, [fp, #0x10]
    // 0xa4d680: StoreField: r1->field_f = r2
    //     0xa4d680: stur            w2, [x1, #0xf]
    // 0xa4d684: str             x2, [SP]
    // 0xa4d688: r0 = _interpolateSingle()
    //     0xa4d688: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa4d68c: r1 = <String>
    //     0xa4d68c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa4d690: stur            x0, [fp, #-0x18]
    // 0xa4d694: r0 = ValueKey()
    //     0xa4d694: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa4d698: mov             x1, x0
    // 0xa4d69c: ldur            x0, [fp, #-0x18]
    // 0xa4d6a0: stur            x1, [fp, #-0x20]
    // 0xa4d6a4: StoreField: r1->field_b = r0
    //     0xa4d6a4: stur            w0, [x1, #0xb]
    // 0xa4d6a8: r0 = Radius()
    //     0xa4d6a8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa4d6ac: d0 = 12.000000
    //     0xa4d6ac: fmov            d0, #12.00000000
    // 0xa4d6b0: stur            x0, [fp, #-0x18]
    // 0xa4d6b4: StoreField: r0->field_7 = d0
    //     0xa4d6b4: stur            d0, [x0, #7]
    // 0xa4d6b8: StoreField: r0->field_f = d0
    //     0xa4d6b8: stur            d0, [x0, #0xf]
    // 0xa4d6bc: r0 = BorderRadius()
    //     0xa4d6bc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa4d6c0: mov             x1, x0
    // 0xa4d6c4: ldur            x0, [fp, #-0x18]
    // 0xa4d6c8: stur            x1, [fp, #-0x28]
    // 0xa4d6cc: StoreField: r1->field_7 = r0
    //     0xa4d6cc: stur            w0, [x1, #7]
    // 0xa4d6d0: StoreField: r1->field_b = r0
    //     0xa4d6d0: stur            w0, [x1, #0xb]
    // 0xa4d6d4: StoreField: r1->field_f = r0
    //     0xa4d6d4: stur            w0, [x1, #0xf]
    // 0xa4d6d8: StoreField: r1->field_13 = r0
    //     0xa4d6d8: stur            w0, [x1, #0x13]
    // 0xa4d6dc: r0 = RoundedRectangleBorder()
    //     0xa4d6dc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa4d6e0: mov             x1, x0
    // 0xa4d6e4: ldur            x0, [fp, #-0x28]
    // 0xa4d6e8: stur            x1, [fp, #-0x18]
    // 0xa4d6ec: StoreField: r1->field_b = r0
    //     0xa4d6ec: stur            w0, [x1, #0xb]
    // 0xa4d6f0: r0 = Instance_BorderSide
    //     0xa4d6f0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa4d6f4: ldr             x0, [x0, #0xe20]
    // 0xa4d6f8: StoreField: r1->field_7 = r0
    //     0xa4d6f8: stur            w0, [x1, #7]
    // 0xa4d6fc: ldur            x2, [fp, #-8]
    // 0xa4d700: LoadField: r0 = r2->field_f
    //     0xa4d700: ldur            w0, [x2, #0xf]
    // 0xa4d704: DecompressPointer r0
    //     0xa4d704: add             x0, x0, HEAP, lsl #32
    // 0xa4d708: LoadField: r3 = r0->field_b
    //     0xa4d708: ldur            w3, [x0, #0xb]
    // 0xa4d70c: DecompressPointer r3
    //     0xa4d70c: add             x3, x3, HEAP, lsl #32
    // 0xa4d710: cmp             w3, NULL
    // 0xa4d714: b.eq            #0xa4f424
    // 0xa4d718: LoadField: r0 = r3->field_b
    //     0xa4d718: ldur            w0, [x3, #0xb]
    // 0xa4d71c: DecompressPointer r0
    //     0xa4d71c: add             x0, x0, HEAP, lsl #32
    // 0xa4d720: ldur            x3, [fp, #-0x10]
    // 0xa4d724: LoadField: r4 = r3->field_f
    //     0xa4d724: ldur            w4, [x3, #0xf]
    // 0xa4d728: DecompressPointer r4
    //     0xa4d728: add             x4, x4, HEAP, lsl #32
    // 0xa4d72c: r5 = LoadClassIdInstr(r0)
    //     0xa4d72c: ldur            x5, [x0, #-1]
    //     0xa4d730: ubfx            x5, x5, #0xc, #0x14
    // 0xa4d734: stp             x4, x0, [SP]
    // 0xa4d738: mov             x0, x5
    // 0xa4d73c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4d73c: sub             lr, x0, #0xb7
    //     0xa4d740: ldr             lr, [x21, lr, lsl #3]
    //     0xa4d744: blr             lr
    // 0xa4d748: cmp             w0, NULL
    // 0xa4d74c: b.ne            #0xa4d758
    // 0xa4d750: r4 = Null
    //     0xa4d750: mov             x4, NULL
    // 0xa4d754: b               #0xa4d770
    // 0xa4d758: LoadField: r1 = r0->field_37
    //     0xa4d758: ldur            w1, [x0, #0x37]
    // 0xa4d75c: DecompressPointer r1
    //     0xa4d75c: add             x1, x1, HEAP, lsl #32
    // 0xa4d760: cmp             w1, NULL
    // 0xa4d764: b.eq            #0xa4f428
    // 0xa4d768: LoadField: r0 = r1->field_b
    //     0xa4d768: ldur            w0, [x1, #0xb]
    // 0xa4d76c: mov             x4, x0
    // 0xa4d770: ldur            x0, [fp, #-8]
    // 0xa4d774: ldur            x3, [fp, #-0x10]
    // 0xa4d778: stur            x4, [fp, #-0x30]
    // 0xa4d77c: LoadField: r1 = r0->field_f
    //     0xa4d77c: ldur            w1, [x0, #0xf]
    // 0xa4d780: DecompressPointer r1
    //     0xa4d780: add             x1, x1, HEAP, lsl #32
    // 0xa4d784: LoadField: r5 = r1->field_1b
    //     0xa4d784: ldur            w5, [x1, #0x1b]
    // 0xa4d788: DecompressPointer r5
    //     0xa4d788: add             x5, x5, HEAP, lsl #32
    // 0xa4d78c: r16 = Sentinel
    //     0xa4d78c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4d790: cmp             w5, w16
    // 0xa4d794: b.eq            #0xa4f42c
    // 0xa4d798: mov             x2, x3
    // 0xa4d79c: stur            x5, [fp, #-0x28]
    // 0xa4d7a0: r1 = Function '<anonymous closure>':.
    //     0xa4d7a0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aa90] AnonymousClosure: (0xa51484), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xa4d7a4: ldr             x1, [x1, #0xa90]
    // 0xa4d7a8: r0 = AllocateClosure()
    //     0xa4d7a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4d7ac: ldur            x2, [fp, #-0x10]
    // 0xa4d7b0: r1 = Function '<anonymous closure>':.
    //     0xa4d7b0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aa98] AnonymousClosure: (0xa4ff40), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4d7b4: ldr             x1, [x1, #0xa98]
    // 0xa4d7b8: stur            x0, [fp, #-0x38]
    // 0xa4d7bc: r0 = AllocateClosure()
    //     0xa4d7bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4d7c0: stur            x0, [fp, #-0x40]
    // 0xa4d7c4: r0 = PageView()
    //     0xa4d7c4: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xa4d7c8: stur            x0, [fp, #-0x48]
    // 0xa4d7cc: ldur            x16, [fp, #-0x28]
    // 0xa4d7d0: str             x16, [SP]
    // 0xa4d7d4: mov             x1, x0
    // 0xa4d7d8: ldur            x2, [fp, #-0x40]
    // 0xa4d7dc: ldur            x3, [fp, #-0x30]
    // 0xa4d7e0: ldur            x5, [fp, #-0x38]
    // 0xa4d7e4: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xa4d7e4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xa4d7e8: ldr             x4, [x4, #0xd60]
    // 0xa4d7ec: r0 = PageView.builder()
    //     0xa4d7ec: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xa4d7f0: r0 = AspectRatio()
    //     0xa4d7f0: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xa4d7f4: mov             x1, x0
    // 0xa4d7f8: d0 = 1.000000
    //     0xa4d7f8: fmov            d0, #1.00000000
    // 0xa4d7fc: stur            x1, [fp, #-0x28]
    // 0xa4d800: StoreField: r1->field_f = d0
    //     0xa4d800: stur            d0, [x1, #0xf]
    // 0xa4d804: ldur            x0, [fp, #-0x48]
    // 0xa4d808: StoreField: r1->field_b = r0
    //     0xa4d808: stur            w0, [x1, #0xb]
    // 0xa4d80c: ldur            x2, [fp, #-8]
    // 0xa4d810: LoadField: r0 = r2->field_f
    //     0xa4d810: ldur            w0, [x2, #0xf]
    // 0xa4d814: DecompressPointer r0
    //     0xa4d814: add             x0, x0, HEAP, lsl #32
    // 0xa4d818: LoadField: r3 = r0->field_b
    //     0xa4d818: ldur            w3, [x0, #0xb]
    // 0xa4d81c: DecompressPointer r3
    //     0xa4d81c: add             x3, x3, HEAP, lsl #32
    // 0xa4d820: cmp             w3, NULL
    // 0xa4d824: b.eq            #0xa4f438
    // 0xa4d828: LoadField: r0 = r3->field_b
    //     0xa4d828: ldur            w0, [x3, #0xb]
    // 0xa4d82c: DecompressPointer r0
    //     0xa4d82c: add             x0, x0, HEAP, lsl #32
    // 0xa4d830: ldur            x3, [fp, #-0x10]
    // 0xa4d834: LoadField: r4 = r3->field_f
    //     0xa4d834: ldur            w4, [x3, #0xf]
    // 0xa4d838: DecompressPointer r4
    //     0xa4d838: add             x4, x4, HEAP, lsl #32
    // 0xa4d83c: r5 = LoadClassIdInstr(r0)
    //     0xa4d83c: ldur            x5, [x0, #-1]
    //     0xa4d840: ubfx            x5, x5, #0xc, #0x14
    // 0xa4d844: stp             x4, x0, [SP]
    // 0xa4d848: mov             x0, x5
    // 0xa4d84c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4d84c: sub             lr, x0, #0xb7
    //     0xa4d850: ldr             lr, [x21, lr, lsl #3]
    //     0xa4d854: blr             lr
    // 0xa4d858: cmp             w0, NULL
    // 0xa4d85c: b.ne            #0xa4d868
    // 0xa4d860: r0 = Null
    //     0xa4d860: mov             x0, NULL
    // 0xa4d864: b               #0xa4d888
    // 0xa4d868: LoadField: r1 = r0->field_33
    //     0xa4d868: ldur            w1, [x0, #0x33]
    // 0xa4d86c: DecompressPointer r1
    //     0xa4d86c: add             x1, x1, HEAP, lsl #32
    // 0xa4d870: cmp             w1, NULL
    // 0xa4d874: b.ne            #0xa4d880
    // 0xa4d878: r0 = Null
    //     0xa4d878: mov             x0, NULL
    // 0xa4d87c: b               #0xa4d888
    // 0xa4d880: LoadField: r0 = r1->field_7
    //     0xa4d880: ldur            w0, [x1, #7]
    // 0xa4d884: DecompressPointer r0
    //     0xa4d884: add             x0, x0, HEAP, lsl #32
    // 0xa4d888: cmp             w0, NULL
    // 0xa4d88c: b.ne            #0xa4d898
    // 0xa4d890: r1 = ""
    //     0xa4d890: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4d894: b               #0xa4d89c
    // 0xa4d898: mov             x1, x0
    // 0xa4d89c: ldur            x0, [fp, #-8]
    // 0xa4d8a0: ldur            x2, [fp, #-0x10]
    // 0xa4d8a4: r0 = capitalizeFirstWord()
    //     0xa4d8a4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa4d8a8: ldr             x1, [fp, #0x18]
    // 0xa4d8ac: stur            x0, [fp, #-0x30]
    // 0xa4d8b0: r0 = of()
    //     0xa4d8b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4d8b4: LoadField: r1 = r0->field_87
    //     0xa4d8b4: ldur            w1, [x0, #0x87]
    // 0xa4d8b8: DecompressPointer r1
    //     0xa4d8b8: add             x1, x1, HEAP, lsl #32
    // 0xa4d8bc: LoadField: r0 = r1->field_2b
    //     0xa4d8bc: ldur            w0, [x1, #0x2b]
    // 0xa4d8c0: DecompressPointer r0
    //     0xa4d8c0: add             x0, x0, HEAP, lsl #32
    // 0xa4d8c4: stur            x0, [fp, #-0x38]
    // 0xa4d8c8: r1 = Instance_Color
    //     0xa4d8c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4d8cc: d0 = 0.700000
    //     0xa4d8cc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4d8d0: ldr             d0, [x17, #0xf48]
    // 0xa4d8d4: r0 = withOpacity()
    //     0xa4d8d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4d8d8: r16 = 12.000000
    //     0xa4d8d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa4d8dc: ldr             x16, [x16, #0x9e8]
    // 0xa4d8e0: stp             x0, x16, [SP]
    // 0xa4d8e4: ldur            x1, [fp, #-0x38]
    // 0xa4d8e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4d8e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4d8ec: ldr             x4, [x4, #0xaa0]
    // 0xa4d8f0: r0 = copyWith()
    //     0xa4d8f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4d8f4: stur            x0, [fp, #-0x38]
    // 0xa4d8f8: r0 = Text()
    //     0xa4d8f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4d8fc: mov             x1, x0
    // 0xa4d900: ldur            x0, [fp, #-0x30]
    // 0xa4d904: stur            x1, [fp, #-0x40]
    // 0xa4d908: StoreField: r1->field_b = r0
    //     0xa4d908: stur            w0, [x1, #0xb]
    // 0xa4d90c: ldur            x0, [fp, #-0x38]
    // 0xa4d910: StoreField: r1->field_13 = r0
    //     0xa4d910: stur            w0, [x1, #0x13]
    // 0xa4d914: r0 = Instance_TextOverflow
    //     0xa4d914: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xa4d918: ldr             x0, [x0, #0xe10]
    // 0xa4d91c: StoreField: r1->field_2b = r0
    //     0xa4d91c: stur            w0, [x1, #0x2b]
    // 0xa4d920: r0 = 2
    //     0xa4d920: movz            x0, #0x2
    // 0xa4d924: StoreField: r1->field_37 = r0
    //     0xa4d924: stur            w0, [x1, #0x37]
    // 0xa4d928: r0 = Padding()
    //     0xa4d928: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4d92c: mov             x1, x0
    // 0xa4d930: r0 = Instance_EdgeInsets
    //     0xa4d930: add             x0, PP, #0x5a, lsl #12  ; [pp+0x5aaa0] Obj!EdgeInsets@d58431
    //     0xa4d934: ldr             x0, [x0, #0xaa0]
    // 0xa4d938: stur            x1, [fp, #-0x30]
    // 0xa4d93c: StoreField: r1->field_f = r0
    //     0xa4d93c: stur            w0, [x1, #0xf]
    // 0xa4d940: ldur            x0, [fp, #-0x40]
    // 0xa4d944: StoreField: r1->field_b = r0
    //     0xa4d944: stur            w0, [x1, #0xb]
    // 0xa4d948: ldur            x2, [fp, #-8]
    // 0xa4d94c: LoadField: r0 = r2->field_f
    //     0xa4d94c: ldur            w0, [x2, #0xf]
    // 0xa4d950: DecompressPointer r0
    //     0xa4d950: add             x0, x0, HEAP, lsl #32
    // 0xa4d954: LoadField: r3 = r0->field_b
    //     0xa4d954: ldur            w3, [x0, #0xb]
    // 0xa4d958: DecompressPointer r3
    //     0xa4d958: add             x3, x3, HEAP, lsl #32
    // 0xa4d95c: cmp             w3, NULL
    // 0xa4d960: b.eq            #0xa4f43c
    // 0xa4d964: LoadField: r0 = r3->field_b
    //     0xa4d964: ldur            w0, [x3, #0xb]
    // 0xa4d968: DecompressPointer r0
    //     0xa4d968: add             x0, x0, HEAP, lsl #32
    // 0xa4d96c: ldur            x3, [fp, #-0x10]
    // 0xa4d970: LoadField: r4 = r3->field_f
    //     0xa4d970: ldur            w4, [x3, #0xf]
    // 0xa4d974: DecompressPointer r4
    //     0xa4d974: add             x4, x4, HEAP, lsl #32
    // 0xa4d978: r5 = LoadClassIdInstr(r0)
    //     0xa4d978: ldur            x5, [x0, #-1]
    //     0xa4d97c: ubfx            x5, x5, #0xc, #0x14
    // 0xa4d980: stp             x4, x0, [SP]
    // 0xa4d984: mov             x0, x5
    // 0xa4d988: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4d988: sub             lr, x0, #0xb7
    //     0xa4d98c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4d990: blr             lr
    // 0xa4d994: cmp             w0, NULL
    // 0xa4d998: b.ne            #0xa4d9a4
    // 0xa4d99c: r0 = Null
    //     0xa4d99c: mov             x0, NULL
    // 0xa4d9a0: b               #0xa4d9c4
    // 0xa4d9a4: LoadField: r1 = r0->field_7b
    //     0xa4d9a4: ldur            w1, [x0, #0x7b]
    // 0xa4d9a8: DecompressPointer r1
    //     0xa4d9a8: add             x1, x1, HEAP, lsl #32
    // 0xa4d9ac: cmp             w1, NULL
    // 0xa4d9b0: b.ne            #0xa4d9bc
    // 0xa4d9b4: r0 = Null
    //     0xa4d9b4: mov             x0, NULL
    // 0xa4d9b8: b               #0xa4d9c4
    // 0xa4d9bc: LoadField: r0 = r1->field_7
    //     0xa4d9bc: ldur            w0, [x1, #7]
    // 0xa4d9c0: DecompressPointer r0
    //     0xa4d9c0: add             x0, x0, HEAP, lsl #32
    // 0xa4d9c4: ldur            x1, [fp, #-8]
    // 0xa4d9c8: ldur            x2, [fp, #-0x10]
    // 0xa4d9cc: r3 = LoadClassIdInstr(r0)
    //     0xa4d9cc: ldur            x3, [x0, #-1]
    //     0xa4d9d0: ubfx            x3, x3, #0xc, #0x14
    // 0xa4d9d4: r16 = 0.000000
    //     0xa4d9d4: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa4d9d8: stp             x16, x0, [SP]
    // 0xa4d9dc: mov             x0, x3
    // 0xa4d9e0: mov             lr, x0
    // 0xa4d9e4: ldr             lr, [x21, lr, lsl #3]
    // 0xa4d9e8: blr             lr
    // 0xa4d9ec: eor             x1, x0, #0x10
    // 0xa4d9f0: ldur            x2, [fp, #-8]
    // 0xa4d9f4: stur            x1, [fp, #-0x38]
    // 0xa4d9f8: LoadField: r0 = r2->field_f
    //     0xa4d9f8: ldur            w0, [x2, #0xf]
    // 0xa4d9fc: DecompressPointer r0
    //     0xa4d9fc: add             x0, x0, HEAP, lsl #32
    // 0xa4da00: LoadField: r3 = r0->field_b
    //     0xa4da00: ldur            w3, [x0, #0xb]
    // 0xa4da04: DecompressPointer r3
    //     0xa4da04: add             x3, x3, HEAP, lsl #32
    // 0xa4da08: cmp             w3, NULL
    // 0xa4da0c: b.eq            #0xa4f440
    // 0xa4da10: LoadField: r0 = r3->field_b
    //     0xa4da10: ldur            w0, [x3, #0xb]
    // 0xa4da14: DecompressPointer r0
    //     0xa4da14: add             x0, x0, HEAP, lsl #32
    // 0xa4da18: ldur            x3, [fp, #-0x10]
    // 0xa4da1c: LoadField: r4 = r3->field_f
    //     0xa4da1c: ldur            w4, [x3, #0xf]
    // 0xa4da20: DecompressPointer r4
    //     0xa4da20: add             x4, x4, HEAP, lsl #32
    // 0xa4da24: r5 = LoadClassIdInstr(r0)
    //     0xa4da24: ldur            x5, [x0, #-1]
    //     0xa4da28: ubfx            x5, x5, #0xc, #0x14
    // 0xa4da2c: stp             x4, x0, [SP]
    // 0xa4da30: mov             x0, x5
    // 0xa4da34: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4da34: sub             lr, x0, #0xb7
    //     0xa4da38: ldr             lr, [x21, lr, lsl #3]
    //     0xa4da3c: blr             lr
    // 0xa4da40: cmp             w0, NULL
    // 0xa4da44: b.ne            #0xa4da50
    // 0xa4da48: r0 = Null
    //     0xa4da48: mov             x0, NULL
    // 0xa4da4c: b               #0xa4da70
    // 0xa4da50: LoadField: r1 = r0->field_7b
    //     0xa4da50: ldur            w1, [x0, #0x7b]
    // 0xa4da54: DecompressPointer r1
    //     0xa4da54: add             x1, x1, HEAP, lsl #32
    // 0xa4da58: cmp             w1, NULL
    // 0xa4da5c: b.ne            #0xa4da68
    // 0xa4da60: r0 = Null
    //     0xa4da60: mov             x0, NULL
    // 0xa4da64: b               #0xa4da70
    // 0xa4da68: LoadField: r0 = r1->field_f
    //     0xa4da68: ldur            w0, [x1, #0xf]
    // 0xa4da6c: DecompressPointer r0
    //     0xa4da6c: add             x0, x0, HEAP, lsl #32
    // 0xa4da70: r1 = LoadClassIdInstr(r0)
    //     0xa4da70: ldur            x1, [x0, #-1]
    //     0xa4da74: ubfx            x1, x1, #0xc, #0x14
    // 0xa4da78: r16 = "product_rating"
    //     0xa4da78: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xa4da7c: ldr             x16, [x16, #0xf20]
    // 0xa4da80: stp             x16, x0, [SP]
    // 0xa4da84: mov             x0, x1
    // 0xa4da88: mov             lr, x0
    // 0xa4da8c: ldr             lr, [x21, lr, lsl #3]
    // 0xa4da90: blr             lr
    // 0xa4da94: tbnz            w0, #4, #0xa4e230
    // 0xa4da98: ldur            x1, [fp, #-8]
    // 0xa4da9c: ldur            x2, [fp, #-0x10]
    // 0xa4daa0: LoadField: r0 = r1->field_f
    //     0xa4daa0: ldur            w0, [x1, #0xf]
    // 0xa4daa4: DecompressPointer r0
    //     0xa4daa4: add             x0, x0, HEAP, lsl #32
    // 0xa4daa8: LoadField: r3 = r0->field_b
    //     0xa4daa8: ldur            w3, [x0, #0xb]
    // 0xa4daac: DecompressPointer r3
    //     0xa4daac: add             x3, x3, HEAP, lsl #32
    // 0xa4dab0: cmp             w3, NULL
    // 0xa4dab4: b.eq            #0xa4f444
    // 0xa4dab8: LoadField: r0 = r3->field_b
    //     0xa4dab8: ldur            w0, [x3, #0xb]
    // 0xa4dabc: DecompressPointer r0
    //     0xa4dabc: add             x0, x0, HEAP, lsl #32
    // 0xa4dac0: LoadField: r3 = r2->field_f
    //     0xa4dac0: ldur            w3, [x2, #0xf]
    // 0xa4dac4: DecompressPointer r3
    //     0xa4dac4: add             x3, x3, HEAP, lsl #32
    // 0xa4dac8: r4 = LoadClassIdInstr(r0)
    //     0xa4dac8: ldur            x4, [x0, #-1]
    //     0xa4dacc: ubfx            x4, x4, #0xc, #0x14
    // 0xa4dad0: stp             x3, x0, [SP]
    // 0xa4dad4: mov             x0, x4
    // 0xa4dad8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4dad8: sub             lr, x0, #0xb7
    //     0xa4dadc: ldr             lr, [x21, lr, lsl #3]
    //     0xa4dae0: blr             lr
    // 0xa4dae4: cmp             w0, NULL
    // 0xa4dae8: b.ne            #0xa4daf4
    // 0xa4daec: r0 = Null
    //     0xa4daec: mov             x0, NULL
    // 0xa4daf0: b               #0xa4db14
    // 0xa4daf4: LoadField: r1 = r0->field_7b
    //     0xa4daf4: ldur            w1, [x0, #0x7b]
    // 0xa4daf8: DecompressPointer r1
    //     0xa4daf8: add             x1, x1, HEAP, lsl #32
    // 0xa4dafc: cmp             w1, NULL
    // 0xa4db00: b.ne            #0xa4db0c
    // 0xa4db04: r0 = Null
    //     0xa4db04: mov             x0, NULL
    // 0xa4db08: b               #0xa4db14
    // 0xa4db0c: LoadField: r0 = r1->field_7
    //     0xa4db0c: ldur            w0, [x1, #7]
    // 0xa4db10: DecompressPointer r0
    //     0xa4db10: add             x0, x0, HEAP, lsl #32
    // 0xa4db14: ldur            x1, [fp, #-8]
    // 0xa4db18: ldur            x2, [fp, #-0x10]
    // 0xa4db1c: cmp             w0, NULL
    // 0xa4db20: r16 = true
    //     0xa4db20: add             x16, NULL, #0x20  ; true
    // 0xa4db24: r17 = false
    //     0xa4db24: add             x17, NULL, #0x30  ; false
    // 0xa4db28: csel            x3, x16, x17, ne
    // 0xa4db2c: stur            x3, [fp, #-0x40]
    // 0xa4db30: LoadField: r0 = r1->field_f
    //     0xa4db30: ldur            w0, [x1, #0xf]
    // 0xa4db34: DecompressPointer r0
    //     0xa4db34: add             x0, x0, HEAP, lsl #32
    // 0xa4db38: LoadField: r4 = r0->field_b
    //     0xa4db38: ldur            w4, [x0, #0xb]
    // 0xa4db3c: DecompressPointer r4
    //     0xa4db3c: add             x4, x4, HEAP, lsl #32
    // 0xa4db40: cmp             w4, NULL
    // 0xa4db44: b.eq            #0xa4f448
    // 0xa4db48: LoadField: r0 = r4->field_b
    //     0xa4db48: ldur            w0, [x4, #0xb]
    // 0xa4db4c: DecompressPointer r0
    //     0xa4db4c: add             x0, x0, HEAP, lsl #32
    // 0xa4db50: LoadField: r4 = r2->field_f
    //     0xa4db50: ldur            w4, [x2, #0xf]
    // 0xa4db54: DecompressPointer r4
    //     0xa4db54: add             x4, x4, HEAP, lsl #32
    // 0xa4db58: r5 = LoadClassIdInstr(r0)
    //     0xa4db58: ldur            x5, [x0, #-1]
    //     0xa4db5c: ubfx            x5, x5, #0xc, #0x14
    // 0xa4db60: stp             x4, x0, [SP]
    // 0xa4db64: mov             x0, x5
    // 0xa4db68: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4db68: sub             lr, x0, #0xb7
    //     0xa4db6c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4db70: blr             lr
    // 0xa4db74: cmp             w0, NULL
    // 0xa4db78: b.ne            #0xa4db84
    // 0xa4db7c: r0 = Null
    //     0xa4db7c: mov             x0, NULL
    // 0xa4db80: b               #0xa4dba4
    // 0xa4db84: LoadField: r1 = r0->field_7b
    //     0xa4db84: ldur            w1, [x0, #0x7b]
    // 0xa4db88: DecompressPointer r1
    //     0xa4db88: add             x1, x1, HEAP, lsl #32
    // 0xa4db8c: cmp             w1, NULL
    // 0xa4db90: b.ne            #0xa4db9c
    // 0xa4db94: r0 = Null
    //     0xa4db94: mov             x0, NULL
    // 0xa4db98: b               #0xa4dba4
    // 0xa4db9c: LoadField: r0 = r1->field_7
    //     0xa4db9c: ldur            w0, [x1, #7]
    // 0xa4dba0: DecompressPointer r0
    //     0xa4dba0: add             x0, x0, HEAP, lsl #32
    // 0xa4dba4: cmp             w0, NULL
    // 0xa4dba8: b.ne            #0xa4dbb4
    // 0xa4dbac: d1 = 0.000000
    //     0xa4dbac: eor             v1.16b, v1.16b, v1.16b
    // 0xa4dbb0: b               #0xa4dbbc
    // 0xa4dbb4: LoadField: d0 = r0->field_7
    //     0xa4dbb4: ldur            d0, [x0, #7]
    // 0xa4dbb8: mov             v1.16b, v0.16b
    // 0xa4dbbc: d0 = 4.000000
    //     0xa4dbbc: fmov            d0, #4.00000000
    // 0xa4dbc0: fcmp            d1, d0
    // 0xa4dbc4: b.lt            #0xa4dbd4
    // 0xa4dbc8: r1 = Instance_Color
    //     0xa4dbc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa4dbcc: ldr             x1, [x1, #0x858]
    // 0xa4dbd0: b               #0xa4dd44
    // 0xa4dbd4: ldur            x1, [fp, #-8]
    // 0xa4dbd8: ldur            x2, [fp, #-0x10]
    // 0xa4dbdc: LoadField: r0 = r1->field_f
    //     0xa4dbdc: ldur            w0, [x1, #0xf]
    // 0xa4dbe0: DecompressPointer r0
    //     0xa4dbe0: add             x0, x0, HEAP, lsl #32
    // 0xa4dbe4: LoadField: r3 = r0->field_b
    //     0xa4dbe4: ldur            w3, [x0, #0xb]
    // 0xa4dbe8: DecompressPointer r3
    //     0xa4dbe8: add             x3, x3, HEAP, lsl #32
    // 0xa4dbec: cmp             w3, NULL
    // 0xa4dbf0: b.eq            #0xa4f44c
    // 0xa4dbf4: LoadField: r0 = r3->field_b
    //     0xa4dbf4: ldur            w0, [x3, #0xb]
    // 0xa4dbf8: DecompressPointer r0
    //     0xa4dbf8: add             x0, x0, HEAP, lsl #32
    // 0xa4dbfc: LoadField: r3 = r2->field_f
    //     0xa4dbfc: ldur            w3, [x2, #0xf]
    // 0xa4dc00: DecompressPointer r3
    //     0xa4dc00: add             x3, x3, HEAP, lsl #32
    // 0xa4dc04: r4 = LoadClassIdInstr(r0)
    //     0xa4dc04: ldur            x4, [x0, #-1]
    //     0xa4dc08: ubfx            x4, x4, #0xc, #0x14
    // 0xa4dc0c: stp             x3, x0, [SP]
    // 0xa4dc10: mov             x0, x4
    // 0xa4dc14: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4dc14: sub             lr, x0, #0xb7
    //     0xa4dc18: ldr             lr, [x21, lr, lsl #3]
    //     0xa4dc1c: blr             lr
    // 0xa4dc20: cmp             w0, NULL
    // 0xa4dc24: b.ne            #0xa4dc30
    // 0xa4dc28: r0 = Null
    //     0xa4dc28: mov             x0, NULL
    // 0xa4dc2c: b               #0xa4dc50
    // 0xa4dc30: LoadField: r1 = r0->field_7b
    //     0xa4dc30: ldur            w1, [x0, #0x7b]
    // 0xa4dc34: DecompressPointer r1
    //     0xa4dc34: add             x1, x1, HEAP, lsl #32
    // 0xa4dc38: cmp             w1, NULL
    // 0xa4dc3c: b.ne            #0xa4dc48
    // 0xa4dc40: r0 = Null
    //     0xa4dc40: mov             x0, NULL
    // 0xa4dc44: b               #0xa4dc50
    // 0xa4dc48: LoadField: r0 = r1->field_7
    //     0xa4dc48: ldur            w0, [x1, #7]
    // 0xa4dc4c: DecompressPointer r0
    //     0xa4dc4c: add             x0, x0, HEAP, lsl #32
    // 0xa4dc50: cmp             w0, NULL
    // 0xa4dc54: b.ne            #0xa4dc60
    // 0xa4dc58: d1 = 0.000000
    //     0xa4dc58: eor             v1.16b, v1.16b, v1.16b
    // 0xa4dc5c: b               #0xa4dc68
    // 0xa4dc60: LoadField: d0 = r0->field_7
    //     0xa4dc60: ldur            d0, [x0, #7]
    // 0xa4dc64: mov             v1.16b, v0.16b
    // 0xa4dc68: d0 = 3.500000
    //     0xa4dc68: fmov            d0, #3.50000000
    // 0xa4dc6c: fcmp            d1, d0
    // 0xa4dc70: b.lt            #0xa4dc8c
    // 0xa4dc74: r1 = Instance_Color
    //     0xa4dc74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa4dc78: ldr             x1, [x1, #0x858]
    // 0xa4dc7c: d0 = 0.700000
    //     0xa4dc7c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4dc80: ldr             d0, [x17, #0xf48]
    // 0xa4dc84: r0 = withOpacity()
    //     0xa4dc84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4dc88: b               #0xa4dd40
    // 0xa4dc8c: ldur            x1, [fp, #-8]
    // 0xa4dc90: ldur            x2, [fp, #-0x10]
    // 0xa4dc94: LoadField: r0 = r1->field_f
    //     0xa4dc94: ldur            w0, [x1, #0xf]
    // 0xa4dc98: DecompressPointer r0
    //     0xa4dc98: add             x0, x0, HEAP, lsl #32
    // 0xa4dc9c: LoadField: r3 = r0->field_b
    //     0xa4dc9c: ldur            w3, [x0, #0xb]
    // 0xa4dca0: DecompressPointer r3
    //     0xa4dca0: add             x3, x3, HEAP, lsl #32
    // 0xa4dca4: cmp             w3, NULL
    // 0xa4dca8: b.eq            #0xa4f450
    // 0xa4dcac: LoadField: r0 = r3->field_b
    //     0xa4dcac: ldur            w0, [x3, #0xb]
    // 0xa4dcb0: DecompressPointer r0
    //     0xa4dcb0: add             x0, x0, HEAP, lsl #32
    // 0xa4dcb4: LoadField: r3 = r2->field_f
    //     0xa4dcb4: ldur            w3, [x2, #0xf]
    // 0xa4dcb8: DecompressPointer r3
    //     0xa4dcb8: add             x3, x3, HEAP, lsl #32
    // 0xa4dcbc: r4 = LoadClassIdInstr(r0)
    //     0xa4dcbc: ldur            x4, [x0, #-1]
    //     0xa4dcc0: ubfx            x4, x4, #0xc, #0x14
    // 0xa4dcc4: stp             x3, x0, [SP]
    // 0xa4dcc8: mov             x0, x4
    // 0xa4dccc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4dccc: sub             lr, x0, #0xb7
    //     0xa4dcd0: ldr             lr, [x21, lr, lsl #3]
    //     0xa4dcd4: blr             lr
    // 0xa4dcd8: cmp             w0, NULL
    // 0xa4dcdc: b.ne            #0xa4dce8
    // 0xa4dce0: r0 = Null
    //     0xa4dce0: mov             x0, NULL
    // 0xa4dce4: b               #0xa4dd08
    // 0xa4dce8: LoadField: r1 = r0->field_7b
    //     0xa4dce8: ldur            w1, [x0, #0x7b]
    // 0xa4dcec: DecompressPointer r1
    //     0xa4dcec: add             x1, x1, HEAP, lsl #32
    // 0xa4dcf0: cmp             w1, NULL
    // 0xa4dcf4: b.ne            #0xa4dd00
    // 0xa4dcf8: r0 = Null
    //     0xa4dcf8: mov             x0, NULL
    // 0xa4dcfc: b               #0xa4dd08
    // 0xa4dd00: LoadField: r0 = r1->field_7
    //     0xa4dd00: ldur            w0, [x1, #7]
    // 0xa4dd04: DecompressPointer r0
    //     0xa4dd04: add             x0, x0, HEAP, lsl #32
    // 0xa4dd08: cmp             w0, NULL
    // 0xa4dd0c: b.ne            #0xa4dd18
    // 0xa4dd10: d1 = 0.000000
    //     0xa4dd10: eor             v1.16b, v1.16b, v1.16b
    // 0xa4dd14: b               #0xa4dd20
    // 0xa4dd18: LoadField: d0 = r0->field_7
    //     0xa4dd18: ldur            d0, [x0, #7]
    // 0xa4dd1c: mov             v1.16b, v0.16b
    // 0xa4dd20: d0 = 2.000000
    //     0xa4dd20: fmov            d0, #2.00000000
    // 0xa4dd24: fcmp            d1, d0
    // 0xa4dd28: b.lt            #0xa4dd38
    // 0xa4dd2c: r0 = Instance_Color
    //     0xa4dd2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xa4dd30: ldr             x0, [x0, #0x860]
    // 0xa4dd34: b               #0xa4dd40
    // 0xa4dd38: r0 = Instance_Color
    //     0xa4dd38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa4dd3c: ldr             x0, [x0, #0x50]
    // 0xa4dd40: mov             x1, x0
    // 0xa4dd44: ldur            x0, [fp, #-8]
    // 0xa4dd48: ldur            x2, [fp, #-0x10]
    // 0xa4dd4c: stur            x1, [fp, #-0x48]
    // 0xa4dd50: r0 = ColorFilter()
    //     0xa4dd50: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa4dd54: mov             x1, x0
    // 0xa4dd58: ldur            x0, [fp, #-0x48]
    // 0xa4dd5c: stur            x1, [fp, #-0x50]
    // 0xa4dd60: StoreField: r1->field_7 = r0
    //     0xa4dd60: stur            w0, [x1, #7]
    // 0xa4dd64: r2 = Instance_BlendMode
    //     0xa4dd64: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa4dd68: ldr             x2, [x2, #0xb30]
    // 0xa4dd6c: StoreField: r1->field_b = r2
    //     0xa4dd6c: stur            w2, [x1, #0xb]
    // 0xa4dd70: r0 = 1
    //     0xa4dd70: movz            x0, #0x1
    // 0xa4dd74: StoreField: r1->field_13 = r0
    //     0xa4dd74: stur            x0, [x1, #0x13]
    // 0xa4dd78: r0 = SvgPicture()
    //     0xa4dd78: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa4dd7c: stur            x0, [fp, #-0x48]
    // 0xa4dd80: ldur            x16, [fp, #-0x50]
    // 0xa4dd84: str             x16, [SP]
    // 0xa4dd88: mov             x1, x0
    // 0xa4dd8c: r2 = "assets/images/green_star.svg"
    //     0xa4dd8c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa4dd90: ldr             x2, [x2, #0x9a0]
    // 0xa4dd94: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa4dd94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa4dd98: ldr             x4, [x4, #0xa38]
    // 0xa4dd9c: r0 = SvgPicture.asset()
    //     0xa4dd9c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa4dda0: ldur            x1, [fp, #-8]
    // 0xa4dda4: LoadField: r0 = r1->field_f
    //     0xa4dda4: ldur            w0, [x1, #0xf]
    // 0xa4dda8: DecompressPointer r0
    //     0xa4dda8: add             x0, x0, HEAP, lsl #32
    // 0xa4ddac: LoadField: r2 = r0->field_b
    //     0xa4ddac: ldur            w2, [x0, #0xb]
    // 0xa4ddb0: DecompressPointer r2
    //     0xa4ddb0: add             x2, x2, HEAP, lsl #32
    // 0xa4ddb4: cmp             w2, NULL
    // 0xa4ddb8: b.eq            #0xa4f454
    // 0xa4ddbc: LoadField: r0 = r2->field_b
    //     0xa4ddbc: ldur            w0, [x2, #0xb]
    // 0xa4ddc0: DecompressPointer r0
    //     0xa4ddc0: add             x0, x0, HEAP, lsl #32
    // 0xa4ddc4: ldur            x2, [fp, #-0x10]
    // 0xa4ddc8: LoadField: r3 = r2->field_f
    //     0xa4ddc8: ldur            w3, [x2, #0xf]
    // 0xa4ddcc: DecompressPointer r3
    //     0xa4ddcc: add             x3, x3, HEAP, lsl #32
    // 0xa4ddd0: r4 = LoadClassIdInstr(r0)
    //     0xa4ddd0: ldur            x4, [x0, #-1]
    //     0xa4ddd4: ubfx            x4, x4, #0xc, #0x14
    // 0xa4ddd8: stp             x3, x0, [SP]
    // 0xa4dddc: mov             x0, x4
    // 0xa4dde0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4dde0: sub             lr, x0, #0xb7
    //     0xa4dde4: ldr             lr, [x21, lr, lsl #3]
    //     0xa4dde8: blr             lr
    // 0xa4ddec: cmp             w0, NULL
    // 0xa4ddf0: b.ne            #0xa4ddfc
    // 0xa4ddf4: r0 = Null
    //     0xa4ddf4: mov             x0, NULL
    // 0xa4ddf8: b               #0xa4de34
    // 0xa4ddfc: LoadField: r1 = r0->field_7b
    //     0xa4ddfc: ldur            w1, [x0, #0x7b]
    // 0xa4de00: DecompressPointer r1
    //     0xa4de00: add             x1, x1, HEAP, lsl #32
    // 0xa4de04: cmp             w1, NULL
    // 0xa4de08: b.ne            #0xa4de14
    // 0xa4de0c: r0 = Null
    //     0xa4de0c: mov             x0, NULL
    // 0xa4de10: b               #0xa4de34
    // 0xa4de14: LoadField: r0 = r1->field_7
    //     0xa4de14: ldur            w0, [x1, #7]
    // 0xa4de18: DecompressPointer r0
    //     0xa4de18: add             x0, x0, HEAP, lsl #32
    // 0xa4de1c: cmp             w0, NULL
    // 0xa4de20: b.ne            #0xa4de2c
    // 0xa4de24: r0 = Null
    //     0xa4de24: mov             x0, NULL
    // 0xa4de28: b               #0xa4de34
    // 0xa4de2c: str             x0, [SP]
    // 0xa4de30: r0 = toString()
    //     0xa4de30: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xa4de34: cmp             w0, NULL
    // 0xa4de38: b.ne            #0xa4de44
    // 0xa4de3c: r4 = ""
    //     0xa4de3c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4de40: b               #0xa4de48
    // 0xa4de44: mov             x4, x0
    // 0xa4de48: ldur            x0, [fp, #-8]
    // 0xa4de4c: ldur            x2, [fp, #-0x10]
    // 0xa4de50: ldur            x3, [fp, #-0x48]
    // 0xa4de54: ldr             x1, [fp, #0x18]
    // 0xa4de58: stur            x4, [fp, #-0x50]
    // 0xa4de5c: r0 = of()
    //     0xa4de5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4de60: LoadField: r1 = r0->field_87
    //     0xa4de60: ldur            w1, [x0, #0x87]
    // 0xa4de64: DecompressPointer r1
    //     0xa4de64: add             x1, x1, HEAP, lsl #32
    // 0xa4de68: LoadField: r0 = r1->field_7
    //     0xa4de68: ldur            w0, [x1, #7]
    // 0xa4de6c: DecompressPointer r0
    //     0xa4de6c: add             x0, x0, HEAP, lsl #32
    // 0xa4de70: r16 = 12.000000
    //     0xa4de70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa4de74: ldr             x16, [x16, #0x9e8]
    // 0xa4de78: r30 = Instance_Color
    //     0xa4de78: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4de7c: stp             lr, x16, [SP]
    // 0xa4de80: mov             x1, x0
    // 0xa4de84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4de84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4de88: ldr             x4, [x4, #0xaa0]
    // 0xa4de8c: r0 = copyWith()
    //     0xa4de8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4de90: stur            x0, [fp, #-0x58]
    // 0xa4de94: r0 = Text()
    //     0xa4de94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4de98: mov             x3, x0
    // 0xa4de9c: ldur            x0, [fp, #-0x50]
    // 0xa4dea0: stur            x3, [fp, #-0x60]
    // 0xa4dea4: StoreField: r3->field_b = r0
    //     0xa4dea4: stur            w0, [x3, #0xb]
    // 0xa4dea8: ldur            x0, [fp, #-0x58]
    // 0xa4deac: StoreField: r3->field_13 = r0
    //     0xa4deac: stur            w0, [x3, #0x13]
    // 0xa4deb0: r1 = Null
    //     0xa4deb0: mov             x1, NULL
    // 0xa4deb4: r2 = 4
    //     0xa4deb4: movz            x2, #0x4
    // 0xa4deb8: r0 = AllocateArray()
    //     0xa4deb8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4debc: mov             x2, x0
    // 0xa4dec0: ldur            x0, [fp, #-0x48]
    // 0xa4dec4: stur            x2, [fp, #-0x50]
    // 0xa4dec8: StoreField: r2->field_f = r0
    //     0xa4dec8: stur            w0, [x2, #0xf]
    // 0xa4decc: ldur            x0, [fp, #-0x60]
    // 0xa4ded0: StoreField: r2->field_13 = r0
    //     0xa4ded0: stur            w0, [x2, #0x13]
    // 0xa4ded4: r1 = <Widget>
    //     0xa4ded4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4ded8: r0 = AllocateGrowableArray()
    //     0xa4ded8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4dedc: mov             x1, x0
    // 0xa4dee0: ldur            x0, [fp, #-0x50]
    // 0xa4dee4: stur            x1, [fp, #-0x48]
    // 0xa4dee8: StoreField: r1->field_f = r0
    //     0xa4dee8: stur            w0, [x1, #0xf]
    // 0xa4deec: r2 = 4
    //     0xa4deec: movz            x2, #0x4
    // 0xa4def0: StoreField: r1->field_b = r2
    //     0xa4def0: stur            w2, [x1, #0xb]
    // 0xa4def4: ldur            x3, [fp, #-8]
    // 0xa4def8: LoadField: r0 = r3->field_f
    //     0xa4def8: ldur            w0, [x3, #0xf]
    // 0xa4defc: DecompressPointer r0
    //     0xa4defc: add             x0, x0, HEAP, lsl #32
    // 0xa4df00: LoadField: r4 = r0->field_b
    //     0xa4df00: ldur            w4, [x0, #0xb]
    // 0xa4df04: DecompressPointer r4
    //     0xa4df04: add             x4, x4, HEAP, lsl #32
    // 0xa4df08: cmp             w4, NULL
    // 0xa4df0c: b.eq            #0xa4f458
    // 0xa4df10: LoadField: r0 = r4->field_b
    //     0xa4df10: ldur            w0, [x4, #0xb]
    // 0xa4df14: DecompressPointer r0
    //     0xa4df14: add             x0, x0, HEAP, lsl #32
    // 0xa4df18: ldur            x4, [fp, #-0x10]
    // 0xa4df1c: LoadField: r5 = r4->field_f
    //     0xa4df1c: ldur            w5, [x4, #0xf]
    // 0xa4df20: DecompressPointer r5
    //     0xa4df20: add             x5, x5, HEAP, lsl #32
    // 0xa4df24: r6 = LoadClassIdInstr(r0)
    //     0xa4df24: ldur            x6, [x0, #-1]
    //     0xa4df28: ubfx            x6, x6, #0xc, #0x14
    // 0xa4df2c: stp             x5, x0, [SP]
    // 0xa4df30: mov             x0, x6
    // 0xa4df34: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4df34: sub             lr, x0, #0xb7
    //     0xa4df38: ldr             lr, [x21, lr, lsl #3]
    //     0xa4df3c: blr             lr
    // 0xa4df40: cmp             w0, NULL
    // 0xa4df44: b.ne            #0xa4df50
    // 0xa4df48: ldur            x2, [fp, #-0x48]
    // 0xa4df4c: b               #0xa4e168
    // 0xa4df50: LoadField: r1 = r0->field_7b
    //     0xa4df50: ldur            w1, [x0, #0x7b]
    // 0xa4df54: DecompressPointer r1
    //     0xa4df54: add             x1, x1, HEAP, lsl #32
    // 0xa4df58: cmp             w1, NULL
    // 0xa4df5c: b.ne            #0xa4df68
    // 0xa4df60: ldur            x2, [fp, #-0x48]
    // 0xa4df64: b               #0xa4e168
    // 0xa4df68: LoadField: r0 = r1->field_b
    //     0xa4df68: ldur            w0, [x1, #0xb]
    // 0xa4df6c: DecompressPointer r0
    //     0xa4df6c: add             x0, x0, HEAP, lsl #32
    // 0xa4df70: cmp             w0, NULL
    // 0xa4df74: b.eq            #0xa4e164
    // 0xa4df78: ldur            x0, [fp, #-8]
    // 0xa4df7c: ldur            x3, [fp, #-0x10]
    // 0xa4df80: r1 = Null
    //     0xa4df80: mov             x1, NULL
    // 0xa4df84: r2 = 6
    //     0xa4df84: movz            x2, #0x6
    // 0xa4df88: r0 = AllocateArray()
    //     0xa4df88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4df8c: mov             x1, x0
    // 0xa4df90: stur            x1, [fp, #-0x50]
    // 0xa4df94: r16 = " | ("
    //     0xa4df94: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xa4df98: ldr             x16, [x16, #0xd70]
    // 0xa4df9c: StoreField: r1->field_f = r16
    //     0xa4df9c: stur            w16, [x1, #0xf]
    // 0xa4dfa0: ldur            x2, [fp, #-8]
    // 0xa4dfa4: LoadField: r0 = r2->field_f
    //     0xa4dfa4: ldur            w0, [x2, #0xf]
    // 0xa4dfa8: DecompressPointer r0
    //     0xa4dfa8: add             x0, x0, HEAP, lsl #32
    // 0xa4dfac: LoadField: r3 = r0->field_b
    //     0xa4dfac: ldur            w3, [x0, #0xb]
    // 0xa4dfb0: DecompressPointer r3
    //     0xa4dfb0: add             x3, x3, HEAP, lsl #32
    // 0xa4dfb4: cmp             w3, NULL
    // 0xa4dfb8: b.eq            #0xa4f45c
    // 0xa4dfbc: LoadField: r0 = r3->field_b
    //     0xa4dfbc: ldur            w0, [x3, #0xb]
    // 0xa4dfc0: DecompressPointer r0
    //     0xa4dfc0: add             x0, x0, HEAP, lsl #32
    // 0xa4dfc4: ldur            x3, [fp, #-0x10]
    // 0xa4dfc8: LoadField: r4 = r3->field_f
    //     0xa4dfc8: ldur            w4, [x3, #0xf]
    // 0xa4dfcc: DecompressPointer r4
    //     0xa4dfcc: add             x4, x4, HEAP, lsl #32
    // 0xa4dfd0: r5 = LoadClassIdInstr(r0)
    //     0xa4dfd0: ldur            x5, [x0, #-1]
    //     0xa4dfd4: ubfx            x5, x5, #0xc, #0x14
    // 0xa4dfd8: stp             x4, x0, [SP]
    // 0xa4dfdc: mov             x0, x5
    // 0xa4dfe0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4dfe0: sub             lr, x0, #0xb7
    //     0xa4dfe4: ldr             lr, [x21, lr, lsl #3]
    //     0xa4dfe8: blr             lr
    // 0xa4dfec: cmp             w0, NULL
    // 0xa4dff0: b.ne            #0xa4dffc
    // 0xa4dff4: r0 = Null
    //     0xa4dff4: mov             x0, NULL
    // 0xa4dff8: b               #0xa4e04c
    // 0xa4dffc: LoadField: r1 = r0->field_7b
    //     0xa4dffc: ldur            w1, [x0, #0x7b]
    // 0xa4e000: DecompressPointer r1
    //     0xa4e000: add             x1, x1, HEAP, lsl #32
    // 0xa4e004: cmp             w1, NULL
    // 0xa4e008: b.ne            #0xa4e014
    // 0xa4e00c: r0 = Null
    //     0xa4e00c: mov             x0, NULL
    // 0xa4e010: b               #0xa4e04c
    // 0xa4e014: LoadField: r0 = r1->field_b
    //     0xa4e014: ldur            w0, [x1, #0xb]
    // 0xa4e018: DecompressPointer r0
    //     0xa4e018: add             x0, x0, HEAP, lsl #32
    // 0xa4e01c: cmp             w0, NULL
    // 0xa4e020: b.ne            #0xa4e02c
    // 0xa4e024: r0 = Null
    //     0xa4e024: mov             x0, NULL
    // 0xa4e028: b               #0xa4e04c
    // 0xa4e02c: LoadField: d0 = r0->field_7
    //     0xa4e02c: ldur            d0, [x0, #7]
    // 0xa4e030: fcmp            d0, d0
    // 0xa4e034: b.vs            #0xa4f460
    // 0xa4e038: fcvtzs          x0, d0
    // 0xa4e03c: asr             x16, x0, #0x1e
    // 0xa4e040: cmp             x16, x0, asr #63
    // 0xa4e044: b.ne            #0xa4f460
    // 0xa4e048: lsl             x0, x0, #1
    // 0xa4e04c: ldur            x2, [fp, #-0x50]
    // 0xa4e050: ldur            x3, [fp, #-0x48]
    // 0xa4e054: mov             x1, x2
    // 0xa4e058: ArrayStore: r1[1] = r0  ; List_4
    //     0xa4e058: add             x25, x1, #0x13
    //     0xa4e05c: str             w0, [x25]
    //     0xa4e060: tbz             w0, #0, #0xa4e07c
    //     0xa4e064: ldurb           w16, [x1, #-1]
    //     0xa4e068: ldurb           w17, [x0, #-1]
    //     0xa4e06c: and             x16, x17, x16, lsr #2
    //     0xa4e070: tst             x16, HEAP, lsr #32
    //     0xa4e074: b.eq            #0xa4e07c
    //     0xa4e078: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4e07c: r16 = ")"
    //     0xa4e07c: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xa4e080: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4e080: stur            w16, [x2, #0x17]
    // 0xa4e084: str             x2, [SP]
    // 0xa4e088: r0 = _interpolate()
    //     0xa4e088: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa4e08c: ldr             x1, [fp, #0x18]
    // 0xa4e090: stur            x0, [fp, #-0x50]
    // 0xa4e094: r0 = of()
    //     0xa4e094: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e098: LoadField: r1 = r0->field_87
    //     0xa4e098: ldur            w1, [x0, #0x87]
    // 0xa4e09c: DecompressPointer r1
    //     0xa4e09c: add             x1, x1, HEAP, lsl #32
    // 0xa4e0a0: LoadField: r0 = r1->field_2b
    //     0xa4e0a0: ldur            w0, [x1, #0x2b]
    // 0xa4e0a4: DecompressPointer r0
    //     0xa4e0a4: add             x0, x0, HEAP, lsl #32
    // 0xa4e0a8: r16 = 12.000000
    //     0xa4e0a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa4e0ac: ldr             x16, [x16, #0x9e8]
    // 0xa4e0b0: r30 = Instance_Color
    //     0xa4e0b0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4e0b4: stp             lr, x16, [SP]
    // 0xa4e0b8: mov             x1, x0
    // 0xa4e0bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4e0bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4e0c0: ldr             x4, [x4, #0xaa0]
    // 0xa4e0c4: r0 = copyWith()
    //     0xa4e0c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4e0c8: stur            x0, [fp, #-0x58]
    // 0xa4e0cc: r0 = Text()
    //     0xa4e0cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4e0d0: mov             x2, x0
    // 0xa4e0d4: ldur            x0, [fp, #-0x50]
    // 0xa4e0d8: stur            x2, [fp, #-0x60]
    // 0xa4e0dc: StoreField: r2->field_b = r0
    //     0xa4e0dc: stur            w0, [x2, #0xb]
    // 0xa4e0e0: ldur            x0, [fp, #-0x58]
    // 0xa4e0e4: StoreField: r2->field_13 = r0
    //     0xa4e0e4: stur            w0, [x2, #0x13]
    // 0xa4e0e8: ldur            x0, [fp, #-0x48]
    // 0xa4e0ec: LoadField: r1 = r0->field_b
    //     0xa4e0ec: ldur            w1, [x0, #0xb]
    // 0xa4e0f0: LoadField: r3 = r0->field_f
    //     0xa4e0f0: ldur            w3, [x0, #0xf]
    // 0xa4e0f4: DecompressPointer r3
    //     0xa4e0f4: add             x3, x3, HEAP, lsl #32
    // 0xa4e0f8: LoadField: r4 = r3->field_b
    //     0xa4e0f8: ldur            w4, [x3, #0xb]
    // 0xa4e0fc: r3 = LoadInt32Instr(r1)
    //     0xa4e0fc: sbfx            x3, x1, #1, #0x1f
    // 0xa4e100: stur            x3, [fp, #-0x68]
    // 0xa4e104: r1 = LoadInt32Instr(r4)
    //     0xa4e104: sbfx            x1, x4, #1, #0x1f
    // 0xa4e108: cmp             x3, x1
    // 0xa4e10c: b.ne            #0xa4e118
    // 0xa4e110: mov             x1, x0
    // 0xa4e114: r0 = _growToNextCapacity()
    //     0xa4e114: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4e118: ldur            x2, [fp, #-0x48]
    // 0xa4e11c: ldur            x3, [fp, #-0x68]
    // 0xa4e120: add             x0, x3, #1
    // 0xa4e124: lsl             x1, x0, #1
    // 0xa4e128: StoreField: r2->field_b = r1
    //     0xa4e128: stur            w1, [x2, #0xb]
    // 0xa4e12c: LoadField: r1 = r2->field_f
    //     0xa4e12c: ldur            w1, [x2, #0xf]
    // 0xa4e130: DecompressPointer r1
    //     0xa4e130: add             x1, x1, HEAP, lsl #32
    // 0xa4e134: ldur            x0, [fp, #-0x60]
    // 0xa4e138: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4e138: add             x25, x1, x3, lsl #2
    //     0xa4e13c: add             x25, x25, #0xf
    //     0xa4e140: str             w0, [x25]
    //     0xa4e144: tbz             w0, #0, #0xa4e160
    //     0xa4e148: ldurb           w16, [x1, #-1]
    //     0xa4e14c: ldurb           w17, [x0, #-1]
    //     0xa4e150: and             x16, x17, x16, lsr #2
    //     0xa4e154: tst             x16, HEAP, lsr #32
    //     0xa4e158: b.eq            #0xa4e160
    //     0xa4e15c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4e160: b               #0xa4e168
    // 0xa4e164: ldur            x2, [fp, #-0x48]
    // 0xa4e168: ldur            x0, [fp, #-0x40]
    // 0xa4e16c: r0 = Row()
    //     0xa4e16c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4e170: mov             x1, x0
    // 0xa4e174: r0 = Instance_Axis
    //     0xa4e174: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa4e178: stur            x1, [fp, #-0x50]
    // 0xa4e17c: StoreField: r1->field_f = r0
    //     0xa4e17c: stur            w0, [x1, #0xf]
    // 0xa4e180: r2 = Instance_MainAxisAlignment
    //     0xa4e180: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa4e184: ldr             x2, [x2, #0xa08]
    // 0xa4e188: StoreField: r1->field_13 = r2
    //     0xa4e188: stur            w2, [x1, #0x13]
    // 0xa4e18c: r3 = Instance_MainAxisSize
    //     0xa4e18c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4e190: ldr             x3, [x3, #0xa10]
    // 0xa4e194: ArrayStore: r1[0] = r3  ; List_4
    //     0xa4e194: stur            w3, [x1, #0x17]
    // 0xa4e198: r4 = Instance_CrossAxisAlignment
    //     0xa4e198: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4e19c: ldr             x4, [x4, #0xa18]
    // 0xa4e1a0: StoreField: r1->field_1b = r4
    //     0xa4e1a0: stur            w4, [x1, #0x1b]
    // 0xa4e1a4: r5 = Instance_VerticalDirection
    //     0xa4e1a4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4e1a8: ldr             x5, [x5, #0xa20]
    // 0xa4e1ac: StoreField: r1->field_23 = r5
    //     0xa4e1ac: stur            w5, [x1, #0x23]
    // 0xa4e1b0: r6 = Instance_Clip
    //     0xa4e1b0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4e1b4: ldr             x6, [x6, #0x38]
    // 0xa4e1b8: StoreField: r1->field_2b = r6
    //     0xa4e1b8: stur            w6, [x1, #0x2b]
    // 0xa4e1bc: StoreField: r1->field_2f = rZR
    //     0xa4e1bc: stur            xzr, [x1, #0x2f]
    // 0xa4e1c0: ldur            x7, [fp, #-0x48]
    // 0xa4e1c4: StoreField: r1->field_b = r7
    //     0xa4e1c4: stur            w7, [x1, #0xb]
    // 0xa4e1c8: r0 = Padding()
    //     0xa4e1c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4e1cc: r1 = Instance_EdgeInsets
    //     0xa4e1cc: add             x1, PP, #0x52, lsl #12  ; [pp+0x521b0] Obj!EdgeInsets@d57201
    //     0xa4e1d0: ldr             x1, [x1, #0x1b0]
    // 0xa4e1d4: stur            x0, [fp, #-0x48]
    // 0xa4e1d8: StoreField: r0->field_f = r1
    //     0xa4e1d8: stur            w1, [x0, #0xf]
    // 0xa4e1dc: ldur            x1, [fp, #-0x50]
    // 0xa4e1e0: StoreField: r0->field_b = r1
    //     0xa4e1e0: stur            w1, [x0, #0xb]
    // 0xa4e1e4: r0 = Visibility()
    //     0xa4e1e4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4e1e8: mov             x1, x0
    // 0xa4e1ec: ldur            x0, [fp, #-0x48]
    // 0xa4e1f0: StoreField: r1->field_b = r0
    //     0xa4e1f0: stur            w0, [x1, #0xb]
    // 0xa4e1f4: r3 = Instance_SizedBox
    //     0xa4e1f4: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4e1f8: StoreField: r1->field_f = r3
    //     0xa4e1f8: stur            w3, [x1, #0xf]
    // 0xa4e1fc: ldur            x0, [fp, #-0x40]
    // 0xa4e200: StoreField: r1->field_13 = r0
    //     0xa4e200: stur            w0, [x1, #0x13]
    // 0xa4e204: r4 = false
    //     0xa4e204: add             x4, NULL, #0x30  ; false
    // 0xa4e208: ArrayStore: r1[0] = r4  ; List_4
    //     0xa4e208: stur            w4, [x1, #0x17]
    // 0xa4e20c: StoreField: r1->field_1b = r4
    //     0xa4e20c: stur            w4, [x1, #0x1b]
    // 0xa4e210: StoreField: r1->field_1f = r4
    //     0xa4e210: stur            w4, [x1, #0x1f]
    // 0xa4e214: StoreField: r1->field_23 = r4
    //     0xa4e214: stur            w4, [x1, #0x23]
    // 0xa4e218: StoreField: r1->field_27 = r4
    //     0xa4e218: stur            w4, [x1, #0x27]
    // 0xa4e21c: StoreField: r1->field_2b = r4
    //     0xa4e21c: stur            w4, [x1, #0x2b]
    // 0xa4e220: mov             x5, x1
    // 0xa4e224: mov             x2, x4
    // 0xa4e228: mov             x0, x3
    // 0xa4e22c: b               #0xa4e5c4
    // 0xa4e230: ldur            x5, [fp, #-8]
    // 0xa4e234: ldur            x6, [fp, #-0x10]
    // 0xa4e238: r1 = Instance_EdgeInsets
    //     0xa4e238: add             x1, PP, #0x52, lsl #12  ; [pp+0x521b0] Obj!EdgeInsets@d57201
    //     0xa4e23c: ldr             x1, [x1, #0x1b0]
    // 0xa4e240: r4 = false
    //     0xa4e240: add             x4, NULL, #0x30  ; false
    // 0xa4e244: r3 = Instance_SizedBox
    //     0xa4e244: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4e248: r2 = Instance_BlendMode
    //     0xa4e248: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa4e24c: ldr             x2, [x2, #0xb30]
    // 0xa4e250: LoadField: r0 = r5->field_f
    //     0xa4e250: ldur            w0, [x5, #0xf]
    // 0xa4e254: DecompressPointer r0
    //     0xa4e254: add             x0, x0, HEAP, lsl #32
    // 0xa4e258: LoadField: r7 = r0->field_b
    //     0xa4e258: ldur            w7, [x0, #0xb]
    // 0xa4e25c: DecompressPointer r7
    //     0xa4e25c: add             x7, x7, HEAP, lsl #32
    // 0xa4e260: cmp             w7, NULL
    // 0xa4e264: b.eq            #0xa4f47c
    // 0xa4e268: LoadField: r0 = r7->field_b
    //     0xa4e268: ldur            w0, [x7, #0xb]
    // 0xa4e26c: DecompressPointer r0
    //     0xa4e26c: add             x0, x0, HEAP, lsl #32
    // 0xa4e270: LoadField: r7 = r6->field_f
    //     0xa4e270: ldur            w7, [x6, #0xf]
    // 0xa4e274: DecompressPointer r7
    //     0xa4e274: add             x7, x7, HEAP, lsl #32
    // 0xa4e278: r8 = LoadClassIdInstr(r0)
    //     0xa4e278: ldur            x8, [x0, #-1]
    //     0xa4e27c: ubfx            x8, x8, #0xc, #0x14
    // 0xa4e280: stp             x7, x0, [SP]
    // 0xa4e284: mov             x0, x8
    // 0xa4e288: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e288: sub             lr, x0, #0xb7
    //     0xa4e28c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e290: blr             lr
    // 0xa4e294: cmp             w0, NULL
    // 0xa4e298: b.ne            #0xa4e2a4
    // 0xa4e29c: r1 = Null
    //     0xa4e29c: mov             x1, NULL
    // 0xa4e2a0: b               #0xa4e2c8
    // 0xa4e2a4: LoadField: r1 = r0->field_7b
    //     0xa4e2a4: ldur            w1, [x0, #0x7b]
    // 0xa4e2a8: DecompressPointer r1
    //     0xa4e2a8: add             x1, x1, HEAP, lsl #32
    // 0xa4e2ac: cmp             w1, NULL
    // 0xa4e2b0: b.ne            #0xa4e2bc
    // 0xa4e2b4: r0 = Null
    //     0xa4e2b4: mov             x0, NULL
    // 0xa4e2b8: b               #0xa4e2c4
    // 0xa4e2bc: LoadField: r0 = r1->field_7
    //     0xa4e2bc: ldur            w0, [x1, #7]
    // 0xa4e2c0: DecompressPointer r0
    //     0xa4e2c0: add             x0, x0, HEAP, lsl #32
    // 0xa4e2c4: mov             x1, x0
    // 0xa4e2c8: ldur            x0, [fp, #-8]
    // 0xa4e2cc: ldur            x2, [fp, #-0x10]
    // 0xa4e2d0: cmp             w1, NULL
    // 0xa4e2d4: r16 = true
    //     0xa4e2d4: add             x16, NULL, #0x20  ; true
    // 0xa4e2d8: r17 = false
    //     0xa4e2d8: add             x17, NULL, #0x30  ; false
    // 0xa4e2dc: csel            x3, x16, x17, ne
    // 0xa4e2e0: ldr             x1, [fp, #0x18]
    // 0xa4e2e4: stur            x3, [fp, #-0x40]
    // 0xa4e2e8: r0 = of()
    //     0xa4e2e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e2ec: LoadField: r1 = r0->field_5b
    //     0xa4e2ec: ldur            w1, [x0, #0x5b]
    // 0xa4e2f0: DecompressPointer r1
    //     0xa4e2f0: add             x1, x1, HEAP, lsl #32
    // 0xa4e2f4: stur            x1, [fp, #-0x48]
    // 0xa4e2f8: r0 = ColorFilter()
    //     0xa4e2f8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa4e2fc: mov             x1, x0
    // 0xa4e300: ldur            x0, [fp, #-0x48]
    // 0xa4e304: stur            x1, [fp, #-0x50]
    // 0xa4e308: StoreField: r1->field_7 = r0
    //     0xa4e308: stur            w0, [x1, #7]
    // 0xa4e30c: r0 = Instance_BlendMode
    //     0xa4e30c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa4e310: ldr             x0, [x0, #0xb30]
    // 0xa4e314: StoreField: r1->field_b = r0
    //     0xa4e314: stur            w0, [x1, #0xb]
    // 0xa4e318: r0 = 1
    //     0xa4e318: movz            x0, #0x1
    // 0xa4e31c: StoreField: r1->field_13 = r0
    //     0xa4e31c: stur            x0, [x1, #0x13]
    // 0xa4e320: r0 = SvgPicture()
    //     0xa4e320: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa4e324: stur            x0, [fp, #-0x48]
    // 0xa4e328: ldur            x16, [fp, #-0x50]
    // 0xa4e32c: str             x16, [SP]
    // 0xa4e330: mov             x1, x0
    // 0xa4e334: r2 = "assets/images/green_star.svg"
    //     0xa4e334: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa4e338: ldr             x2, [x2, #0x9a0]
    // 0xa4e33c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa4e33c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa4e340: ldr             x4, [x4, #0xa38]
    // 0xa4e344: r0 = SvgPicture.asset()
    //     0xa4e344: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa4e348: ldur            x1, [fp, #-8]
    // 0xa4e34c: LoadField: r0 = r1->field_f
    //     0xa4e34c: ldur            w0, [x1, #0xf]
    // 0xa4e350: DecompressPointer r0
    //     0xa4e350: add             x0, x0, HEAP, lsl #32
    // 0xa4e354: LoadField: r2 = r0->field_b
    //     0xa4e354: ldur            w2, [x0, #0xb]
    // 0xa4e358: DecompressPointer r2
    //     0xa4e358: add             x2, x2, HEAP, lsl #32
    // 0xa4e35c: cmp             w2, NULL
    // 0xa4e360: b.eq            #0xa4f480
    // 0xa4e364: LoadField: r0 = r2->field_b
    //     0xa4e364: ldur            w0, [x2, #0xb]
    // 0xa4e368: DecompressPointer r0
    //     0xa4e368: add             x0, x0, HEAP, lsl #32
    // 0xa4e36c: ldur            x2, [fp, #-0x10]
    // 0xa4e370: LoadField: r3 = r2->field_f
    //     0xa4e370: ldur            w3, [x2, #0xf]
    // 0xa4e374: DecompressPointer r3
    //     0xa4e374: add             x3, x3, HEAP, lsl #32
    // 0xa4e378: r4 = LoadClassIdInstr(r0)
    //     0xa4e378: ldur            x4, [x0, #-1]
    //     0xa4e37c: ubfx            x4, x4, #0xc, #0x14
    // 0xa4e380: stp             x3, x0, [SP]
    // 0xa4e384: mov             x0, x4
    // 0xa4e388: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e388: sub             lr, x0, #0xb7
    //     0xa4e38c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e390: blr             lr
    // 0xa4e394: cmp             w0, NULL
    // 0xa4e398: b.ne            #0xa4e3a4
    // 0xa4e39c: r0 = Null
    //     0xa4e39c: mov             x0, NULL
    // 0xa4e3a0: b               #0xa4e3dc
    // 0xa4e3a4: LoadField: r1 = r0->field_7b
    //     0xa4e3a4: ldur            w1, [x0, #0x7b]
    // 0xa4e3a8: DecompressPointer r1
    //     0xa4e3a8: add             x1, x1, HEAP, lsl #32
    // 0xa4e3ac: cmp             w1, NULL
    // 0xa4e3b0: b.ne            #0xa4e3bc
    // 0xa4e3b4: r0 = Null
    //     0xa4e3b4: mov             x0, NULL
    // 0xa4e3b8: b               #0xa4e3dc
    // 0xa4e3bc: LoadField: r0 = r1->field_7
    //     0xa4e3bc: ldur            w0, [x1, #7]
    // 0xa4e3c0: DecompressPointer r0
    //     0xa4e3c0: add             x0, x0, HEAP, lsl #32
    // 0xa4e3c4: cmp             w0, NULL
    // 0xa4e3c8: b.ne            #0xa4e3d4
    // 0xa4e3cc: r0 = Null
    //     0xa4e3cc: mov             x0, NULL
    // 0xa4e3d0: b               #0xa4e3dc
    // 0xa4e3d4: str             x0, [SP]
    // 0xa4e3d8: r0 = toString()
    //     0xa4e3d8: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xa4e3dc: cmp             w0, NULL
    // 0xa4e3e0: b.ne            #0xa4e3ec
    // 0xa4e3e4: r3 = ""
    //     0xa4e3e4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4e3e8: b               #0xa4e3f0
    // 0xa4e3ec: mov             x3, x0
    // 0xa4e3f0: ldur            x2, [fp, #-0x40]
    // 0xa4e3f4: ldur            x0, [fp, #-0x48]
    // 0xa4e3f8: ldr             x1, [fp, #0x18]
    // 0xa4e3fc: stur            x3, [fp, #-0x50]
    // 0xa4e400: r0 = of()
    //     0xa4e400: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e404: LoadField: r1 = r0->field_87
    //     0xa4e404: ldur            w1, [x0, #0x87]
    // 0xa4e408: DecompressPointer r1
    //     0xa4e408: add             x1, x1, HEAP, lsl #32
    // 0xa4e40c: LoadField: r0 = r1->field_7
    //     0xa4e40c: ldur            w0, [x1, #7]
    // 0xa4e410: DecompressPointer r0
    //     0xa4e410: add             x0, x0, HEAP, lsl #32
    // 0xa4e414: r16 = 12.000000
    //     0xa4e414: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa4e418: ldr             x16, [x16, #0x9e8]
    // 0xa4e41c: r30 = Instance_Color
    //     0xa4e41c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4e420: stp             lr, x16, [SP]
    // 0xa4e424: mov             x1, x0
    // 0xa4e428: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4e428: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4e42c: ldr             x4, [x4, #0xaa0]
    // 0xa4e430: r0 = copyWith()
    //     0xa4e430: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4e434: stur            x0, [fp, #-0x58]
    // 0xa4e438: r0 = Text()
    //     0xa4e438: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4e43c: mov             x2, x0
    // 0xa4e440: ldur            x0, [fp, #-0x50]
    // 0xa4e444: stur            x2, [fp, #-0x60]
    // 0xa4e448: StoreField: r2->field_b = r0
    //     0xa4e448: stur            w0, [x2, #0xb]
    // 0xa4e44c: ldur            x0, [fp, #-0x58]
    // 0xa4e450: StoreField: r2->field_13 = r0
    //     0xa4e450: stur            w0, [x2, #0x13]
    // 0xa4e454: ldr             x1, [fp, #0x18]
    // 0xa4e458: r0 = of()
    //     0xa4e458: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e45c: LoadField: r1 = r0->field_87
    //     0xa4e45c: ldur            w1, [x0, #0x87]
    // 0xa4e460: DecompressPointer r1
    //     0xa4e460: add             x1, x1, HEAP, lsl #32
    // 0xa4e464: LoadField: r0 = r1->field_2b
    //     0xa4e464: ldur            w0, [x1, #0x2b]
    // 0xa4e468: DecompressPointer r0
    //     0xa4e468: add             x0, x0, HEAP, lsl #32
    // 0xa4e46c: ldr             x1, [fp, #0x18]
    // 0xa4e470: stur            x0, [fp, #-0x50]
    // 0xa4e474: r0 = of()
    //     0xa4e474: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e478: LoadField: r1 = r0->field_5b
    //     0xa4e478: ldur            w1, [x0, #0x5b]
    // 0xa4e47c: DecompressPointer r1
    //     0xa4e47c: add             x1, x1, HEAP, lsl #32
    // 0xa4e480: r16 = 10.000000
    //     0xa4e480: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa4e484: stp             x1, x16, [SP]
    // 0xa4e488: ldur            x1, [fp, #-0x50]
    // 0xa4e48c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4e48c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4e490: ldr             x4, [x4, #0xaa0]
    // 0xa4e494: r0 = copyWith()
    //     0xa4e494: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4e498: stur            x0, [fp, #-0x50]
    // 0xa4e49c: r0 = Text()
    //     0xa4e49c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4e4a0: mov             x3, x0
    // 0xa4e4a4: r0 = " Brand Rating"
    //     0xa4e4a4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xa4e4a8: ldr             x0, [x0, #0xd78]
    // 0xa4e4ac: stur            x3, [fp, #-0x58]
    // 0xa4e4b0: StoreField: r3->field_b = r0
    //     0xa4e4b0: stur            w0, [x3, #0xb]
    // 0xa4e4b4: ldur            x0, [fp, #-0x50]
    // 0xa4e4b8: StoreField: r3->field_13 = r0
    //     0xa4e4b8: stur            w0, [x3, #0x13]
    // 0xa4e4bc: r1 = Null
    //     0xa4e4bc: mov             x1, NULL
    // 0xa4e4c0: r2 = 6
    //     0xa4e4c0: movz            x2, #0x6
    // 0xa4e4c4: r0 = AllocateArray()
    //     0xa4e4c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4e4c8: mov             x2, x0
    // 0xa4e4cc: ldur            x0, [fp, #-0x48]
    // 0xa4e4d0: stur            x2, [fp, #-0x50]
    // 0xa4e4d4: StoreField: r2->field_f = r0
    //     0xa4e4d4: stur            w0, [x2, #0xf]
    // 0xa4e4d8: ldur            x0, [fp, #-0x60]
    // 0xa4e4dc: StoreField: r2->field_13 = r0
    //     0xa4e4dc: stur            w0, [x2, #0x13]
    // 0xa4e4e0: ldur            x0, [fp, #-0x58]
    // 0xa4e4e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4e4e4: stur            w0, [x2, #0x17]
    // 0xa4e4e8: r1 = <Widget>
    //     0xa4e4e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4e4ec: r0 = AllocateGrowableArray()
    //     0xa4e4ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4e4f0: mov             x1, x0
    // 0xa4e4f4: ldur            x0, [fp, #-0x50]
    // 0xa4e4f8: stur            x1, [fp, #-0x48]
    // 0xa4e4fc: StoreField: r1->field_f = r0
    //     0xa4e4fc: stur            w0, [x1, #0xf]
    // 0xa4e500: r2 = 6
    //     0xa4e500: movz            x2, #0x6
    // 0xa4e504: StoreField: r1->field_b = r2
    //     0xa4e504: stur            w2, [x1, #0xb]
    // 0xa4e508: r0 = Row()
    //     0xa4e508: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4e50c: mov             x1, x0
    // 0xa4e510: r0 = Instance_Axis
    //     0xa4e510: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa4e514: stur            x1, [fp, #-0x50]
    // 0xa4e518: StoreField: r1->field_f = r0
    //     0xa4e518: stur            w0, [x1, #0xf]
    // 0xa4e51c: r2 = Instance_MainAxisAlignment
    //     0xa4e51c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa4e520: ldr             x2, [x2, #0xa08]
    // 0xa4e524: StoreField: r1->field_13 = r2
    //     0xa4e524: stur            w2, [x1, #0x13]
    // 0xa4e528: r3 = Instance_MainAxisSize
    //     0xa4e528: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4e52c: ldr             x3, [x3, #0xa10]
    // 0xa4e530: ArrayStore: r1[0] = r3  ; List_4
    //     0xa4e530: stur            w3, [x1, #0x17]
    // 0xa4e534: r4 = Instance_CrossAxisAlignment
    //     0xa4e534: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4e538: ldr             x4, [x4, #0xa18]
    // 0xa4e53c: StoreField: r1->field_1b = r4
    //     0xa4e53c: stur            w4, [x1, #0x1b]
    // 0xa4e540: r5 = Instance_VerticalDirection
    //     0xa4e540: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4e544: ldr             x5, [x5, #0xa20]
    // 0xa4e548: StoreField: r1->field_23 = r5
    //     0xa4e548: stur            w5, [x1, #0x23]
    // 0xa4e54c: r6 = Instance_Clip
    //     0xa4e54c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4e550: ldr             x6, [x6, #0x38]
    // 0xa4e554: StoreField: r1->field_2b = r6
    //     0xa4e554: stur            w6, [x1, #0x2b]
    // 0xa4e558: StoreField: r1->field_2f = rZR
    //     0xa4e558: stur            xzr, [x1, #0x2f]
    // 0xa4e55c: ldur            x7, [fp, #-0x48]
    // 0xa4e560: StoreField: r1->field_b = r7
    //     0xa4e560: stur            w7, [x1, #0xb]
    // 0xa4e564: r0 = Padding()
    //     0xa4e564: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4e568: mov             x1, x0
    // 0xa4e56c: r0 = Instance_EdgeInsets
    //     0xa4e56c: add             x0, PP, #0x52, lsl #12  ; [pp+0x521b0] Obj!EdgeInsets@d57201
    //     0xa4e570: ldr             x0, [x0, #0x1b0]
    // 0xa4e574: stur            x1, [fp, #-0x48]
    // 0xa4e578: StoreField: r1->field_f = r0
    //     0xa4e578: stur            w0, [x1, #0xf]
    // 0xa4e57c: ldur            x0, [fp, #-0x50]
    // 0xa4e580: StoreField: r1->field_b = r0
    //     0xa4e580: stur            w0, [x1, #0xb]
    // 0xa4e584: r0 = Visibility()
    //     0xa4e584: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4e588: mov             x1, x0
    // 0xa4e58c: ldur            x0, [fp, #-0x48]
    // 0xa4e590: StoreField: r1->field_b = r0
    //     0xa4e590: stur            w0, [x1, #0xb]
    // 0xa4e594: r0 = Instance_SizedBox
    //     0xa4e594: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4e598: StoreField: r1->field_f = r0
    //     0xa4e598: stur            w0, [x1, #0xf]
    // 0xa4e59c: ldur            x2, [fp, #-0x40]
    // 0xa4e5a0: StoreField: r1->field_13 = r2
    //     0xa4e5a0: stur            w2, [x1, #0x13]
    // 0xa4e5a4: r2 = false
    //     0xa4e5a4: add             x2, NULL, #0x30  ; false
    // 0xa4e5a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4e5a8: stur            w2, [x1, #0x17]
    // 0xa4e5ac: StoreField: r1->field_1b = r2
    //     0xa4e5ac: stur            w2, [x1, #0x1b]
    // 0xa4e5b0: StoreField: r1->field_1f = r2
    //     0xa4e5b0: stur            w2, [x1, #0x1f]
    // 0xa4e5b4: StoreField: r1->field_23 = r2
    //     0xa4e5b4: stur            w2, [x1, #0x23]
    // 0xa4e5b8: StoreField: r1->field_27 = r2
    //     0xa4e5b8: stur            w2, [x1, #0x27]
    // 0xa4e5bc: StoreField: r1->field_2b = r2
    //     0xa4e5bc: stur            w2, [x1, #0x2b]
    // 0xa4e5c0: mov             x5, x1
    // 0xa4e5c4: ldur            x1, [fp, #-8]
    // 0xa4e5c8: ldur            x3, [fp, #-0x10]
    // 0xa4e5cc: ldur            x4, [fp, #-0x38]
    // 0xa4e5d0: stur            x5, [fp, #-0x40]
    // 0xa4e5d4: r0 = Visibility()
    //     0xa4e5d4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4e5d8: mov             x1, x0
    // 0xa4e5dc: ldur            x0, [fp, #-0x40]
    // 0xa4e5e0: stur            x1, [fp, #-0x48]
    // 0xa4e5e4: StoreField: r1->field_b = r0
    //     0xa4e5e4: stur            w0, [x1, #0xb]
    // 0xa4e5e8: r2 = Instance_SizedBox
    //     0xa4e5e8: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4e5ec: StoreField: r1->field_f = r2
    //     0xa4e5ec: stur            w2, [x1, #0xf]
    // 0xa4e5f0: ldur            x0, [fp, #-0x38]
    // 0xa4e5f4: StoreField: r1->field_13 = r0
    //     0xa4e5f4: stur            w0, [x1, #0x13]
    // 0xa4e5f8: r3 = false
    //     0xa4e5f8: add             x3, NULL, #0x30  ; false
    // 0xa4e5fc: ArrayStore: r1[0] = r3  ; List_4
    //     0xa4e5fc: stur            w3, [x1, #0x17]
    // 0xa4e600: StoreField: r1->field_1b = r3
    //     0xa4e600: stur            w3, [x1, #0x1b]
    // 0xa4e604: StoreField: r1->field_1f = r3
    //     0xa4e604: stur            w3, [x1, #0x1f]
    // 0xa4e608: StoreField: r1->field_23 = r3
    //     0xa4e608: stur            w3, [x1, #0x23]
    // 0xa4e60c: StoreField: r1->field_27 = r3
    //     0xa4e60c: stur            w3, [x1, #0x27]
    // 0xa4e610: StoreField: r1->field_2b = r3
    //     0xa4e610: stur            w3, [x1, #0x2b]
    // 0xa4e614: ldur            x4, [fp, #-8]
    // 0xa4e618: LoadField: r0 = r4->field_f
    //     0xa4e618: ldur            w0, [x4, #0xf]
    // 0xa4e61c: DecompressPointer r0
    //     0xa4e61c: add             x0, x0, HEAP, lsl #32
    // 0xa4e620: LoadField: r5 = r0->field_b
    //     0xa4e620: ldur            w5, [x0, #0xb]
    // 0xa4e624: DecompressPointer r5
    //     0xa4e624: add             x5, x5, HEAP, lsl #32
    // 0xa4e628: cmp             w5, NULL
    // 0xa4e62c: b.eq            #0xa4f484
    // 0xa4e630: LoadField: r0 = r5->field_b
    //     0xa4e630: ldur            w0, [x5, #0xb]
    // 0xa4e634: DecompressPointer r0
    //     0xa4e634: add             x0, x0, HEAP, lsl #32
    // 0xa4e638: ldur            x5, [fp, #-0x10]
    // 0xa4e63c: LoadField: r6 = r5->field_f
    //     0xa4e63c: ldur            w6, [x5, #0xf]
    // 0xa4e640: DecompressPointer r6
    //     0xa4e640: add             x6, x6, HEAP, lsl #32
    // 0xa4e644: r7 = LoadClassIdInstr(r0)
    //     0xa4e644: ldur            x7, [x0, #-1]
    //     0xa4e648: ubfx            x7, x7, #0xc, #0x14
    // 0xa4e64c: stp             x6, x0, [SP]
    // 0xa4e650: mov             x0, x7
    // 0xa4e654: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e654: sub             lr, x0, #0xb7
    //     0xa4e658: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e65c: blr             lr
    // 0xa4e660: cmp             w0, NULL
    // 0xa4e664: b.ne            #0xa4e670
    // 0xa4e668: r4 = Null
    //     0xa4e668: mov             x4, NULL
    // 0xa4e66c: b               #0xa4e67c
    // 0xa4e670: LoadField: r1 = r0->field_43
    //     0xa4e670: ldur            w1, [x0, #0x43]
    // 0xa4e674: DecompressPointer r1
    //     0xa4e674: add             x1, x1, HEAP, lsl #32
    // 0xa4e678: mov             x4, x1
    // 0xa4e67c: ldur            x0, [fp, #-8]
    // 0xa4e680: ldur            x3, [fp, #-0x10]
    // 0xa4e684: stur            x4, [fp, #-0x38]
    // 0xa4e688: r1 = Null
    //     0xa4e688: mov             x1, NULL
    // 0xa4e68c: r2 = 4
    //     0xa4e68c: movz            x2, #0x4
    // 0xa4e690: r0 = AllocateArray()
    //     0xa4e690: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4e694: mov             x1, x0
    // 0xa4e698: ldur            x0, [fp, #-0x38]
    // 0xa4e69c: StoreField: r1->field_f = r0
    //     0xa4e69c: stur            w0, [x1, #0xf]
    // 0xa4e6a0: r16 = " "
    //     0xa4e6a0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xa4e6a4: StoreField: r1->field_13 = r16
    //     0xa4e6a4: stur            w16, [x1, #0x13]
    // 0xa4e6a8: str             x1, [SP]
    // 0xa4e6ac: r0 = _interpolate()
    //     0xa4e6ac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa4e6b0: ldr             x1, [fp, #0x18]
    // 0xa4e6b4: stur            x0, [fp, #-0x38]
    // 0xa4e6b8: r0 = of()
    //     0xa4e6b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e6bc: LoadField: r1 = r0->field_87
    //     0xa4e6bc: ldur            w1, [x0, #0x87]
    // 0xa4e6c0: DecompressPointer r1
    //     0xa4e6c0: add             x1, x1, HEAP, lsl #32
    // 0xa4e6c4: LoadField: r0 = r1->field_7
    //     0xa4e6c4: ldur            w0, [x1, #7]
    // 0xa4e6c8: DecompressPointer r0
    //     0xa4e6c8: add             x0, x0, HEAP, lsl #32
    // 0xa4e6cc: stur            x0, [fp, #-0x40]
    // 0xa4e6d0: r1 = Instance_Color
    //     0xa4e6d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4e6d4: d0 = 0.700000
    //     0xa4e6d4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4e6d8: ldr             d0, [x17, #0xf48]
    // 0xa4e6dc: r0 = withOpacity()
    //     0xa4e6dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4e6e0: r16 = 14.000000
    //     0xa4e6e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4e6e4: ldr             x16, [x16, #0x1d8]
    // 0xa4e6e8: stp             x0, x16, [SP]
    // 0xa4e6ec: ldur            x1, [fp, #-0x40]
    // 0xa4e6f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4e6f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4e6f4: ldr             x4, [x4, #0xaa0]
    // 0xa4e6f8: r0 = copyWith()
    //     0xa4e6f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4e6fc: stur            x0, [fp, #-0x40]
    // 0xa4e700: r0 = Text()
    //     0xa4e700: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4e704: mov             x1, x0
    // 0xa4e708: ldur            x0, [fp, #-0x38]
    // 0xa4e70c: stur            x1, [fp, #-0x50]
    // 0xa4e710: StoreField: r1->field_b = r0
    //     0xa4e710: stur            w0, [x1, #0xb]
    // 0xa4e714: ldur            x0, [fp, #-0x40]
    // 0xa4e718: StoreField: r1->field_13 = r0
    //     0xa4e718: stur            w0, [x1, #0x13]
    // 0xa4e71c: r0 = WidgetSpan()
    //     0xa4e71c: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xa4e720: mov             x1, x0
    // 0xa4e724: ldur            x0, [fp, #-0x50]
    // 0xa4e728: stur            x1, [fp, #-0x38]
    // 0xa4e72c: StoreField: r1->field_13 = r0
    //     0xa4e72c: stur            w0, [x1, #0x13]
    // 0xa4e730: r0 = Instance_PlaceholderAlignment
    //     0xa4e730: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xa4e734: ldr             x0, [x0, #0xa0]
    // 0xa4e738: StoreField: r1->field_b = r0
    //     0xa4e738: stur            w0, [x1, #0xb]
    // 0xa4e73c: ldur            x2, [fp, #-8]
    // 0xa4e740: LoadField: r0 = r2->field_f
    //     0xa4e740: ldur            w0, [x2, #0xf]
    // 0xa4e744: DecompressPointer r0
    //     0xa4e744: add             x0, x0, HEAP, lsl #32
    // 0xa4e748: LoadField: r3 = r0->field_b
    //     0xa4e748: ldur            w3, [x0, #0xb]
    // 0xa4e74c: DecompressPointer r3
    //     0xa4e74c: add             x3, x3, HEAP, lsl #32
    // 0xa4e750: cmp             w3, NULL
    // 0xa4e754: b.eq            #0xa4f488
    // 0xa4e758: LoadField: r0 = r3->field_b
    //     0xa4e758: ldur            w0, [x3, #0xb]
    // 0xa4e75c: DecompressPointer r0
    //     0xa4e75c: add             x0, x0, HEAP, lsl #32
    // 0xa4e760: ldur            x3, [fp, #-0x10]
    // 0xa4e764: LoadField: r4 = r3->field_f
    //     0xa4e764: ldur            w4, [x3, #0xf]
    // 0xa4e768: DecompressPointer r4
    //     0xa4e768: add             x4, x4, HEAP, lsl #32
    // 0xa4e76c: r5 = LoadClassIdInstr(r0)
    //     0xa4e76c: ldur            x5, [x0, #-1]
    //     0xa4e770: ubfx            x5, x5, #0xc, #0x14
    // 0xa4e774: stp             x4, x0, [SP]
    // 0xa4e778: mov             x0, x5
    // 0xa4e77c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e77c: sub             lr, x0, #0xb7
    //     0xa4e780: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e784: blr             lr
    // 0xa4e788: cmp             w0, NULL
    // 0xa4e78c: b.ne            #0xa4e798
    // 0xa4e790: r1 = Null
    //     0xa4e790: mov             x1, NULL
    // 0xa4e794: b               #0xa4e7a0
    // 0xa4e798: LoadField: r1 = r0->field_4b
    //     0xa4e798: ldur            w1, [x0, #0x4b]
    // 0xa4e79c: DecompressPointer r1
    //     0xa4e79c: add             x1, x1, HEAP, lsl #32
    // 0xa4e7a0: ldur            x0, [fp, #-8]
    // 0xa4e7a4: ldur            x2, [fp, #-0x10]
    // 0xa4e7a8: str             x1, [SP]
    // 0xa4e7ac: r0 = _interpolateSingle()
    //     0xa4e7ac: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa4e7b0: ldr             x1, [fp, #0x18]
    // 0xa4e7b4: stur            x0, [fp, #-0x40]
    // 0xa4e7b8: r0 = of()
    //     0xa4e7b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4e7bc: LoadField: r1 = r0->field_87
    //     0xa4e7bc: ldur            w1, [x0, #0x87]
    // 0xa4e7c0: DecompressPointer r1
    //     0xa4e7c0: add             x1, x1, HEAP, lsl #32
    // 0xa4e7c4: LoadField: r0 = r1->field_2b
    //     0xa4e7c4: ldur            w0, [x1, #0x2b]
    // 0xa4e7c8: DecompressPointer r0
    //     0xa4e7c8: add             x0, x0, HEAP, lsl #32
    // 0xa4e7cc: stur            x0, [fp, #-0x50]
    // 0xa4e7d0: r1 = Instance_Color
    //     0xa4e7d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4e7d4: d0 = 0.250000
    //     0xa4e7d4: fmov            d0, #0.25000000
    // 0xa4e7d8: r0 = withOpacity()
    //     0xa4e7d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4e7dc: r16 = Instance_TextDecoration
    //     0xa4e7dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xa4e7e0: ldr             x16, [x16, #0xe30]
    // 0xa4e7e4: r30 = 14.000000
    //     0xa4e7e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4e7e8: ldr             lr, [lr, #0x1d8]
    // 0xa4e7ec: stp             lr, x16, [SP, #8]
    // 0xa4e7f0: str             x0, [SP]
    // 0xa4e7f4: ldur            x1, [fp, #-0x50]
    // 0xa4e7f8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xa4e7f8: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xa4e7fc: ldr             x4, [x4, #0xb60]
    // 0xa4e800: r0 = copyWith()
    //     0xa4e800: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4e804: stur            x0, [fp, #-0x50]
    // 0xa4e808: r0 = TextSpan()
    //     0xa4e808: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4e80c: mov             x1, x0
    // 0xa4e810: ldur            x0, [fp, #-0x40]
    // 0xa4e814: stur            x1, [fp, #-0x58]
    // 0xa4e818: StoreField: r1->field_b = r0
    //     0xa4e818: stur            w0, [x1, #0xb]
    // 0xa4e81c: r2 = Instance__DeferringMouseCursor
    //     0xa4e81c: ldr             x2, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4e820: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4e820: stur            w2, [x1, #0x17]
    // 0xa4e824: ldur            x0, [fp, #-0x50]
    // 0xa4e828: StoreField: r1->field_7 = r0
    //     0xa4e828: stur            w0, [x1, #7]
    // 0xa4e82c: ldur            x3, [fp, #-8]
    // 0xa4e830: LoadField: r0 = r3->field_f
    //     0xa4e830: ldur            w0, [x3, #0xf]
    // 0xa4e834: DecompressPointer r0
    //     0xa4e834: add             x0, x0, HEAP, lsl #32
    // 0xa4e838: LoadField: r4 = r0->field_b
    //     0xa4e838: ldur            w4, [x0, #0xb]
    // 0xa4e83c: DecompressPointer r4
    //     0xa4e83c: add             x4, x4, HEAP, lsl #32
    // 0xa4e840: cmp             w4, NULL
    // 0xa4e844: b.eq            #0xa4f48c
    // 0xa4e848: LoadField: r0 = r4->field_b
    //     0xa4e848: ldur            w0, [x4, #0xb]
    // 0xa4e84c: DecompressPointer r0
    //     0xa4e84c: add             x0, x0, HEAP, lsl #32
    // 0xa4e850: ldur            x4, [fp, #-0x10]
    // 0xa4e854: LoadField: r5 = r4->field_f
    //     0xa4e854: ldur            w5, [x4, #0xf]
    // 0xa4e858: DecompressPointer r5
    //     0xa4e858: add             x5, x5, HEAP, lsl #32
    // 0xa4e85c: r6 = LoadClassIdInstr(r0)
    //     0xa4e85c: ldur            x6, [x0, #-1]
    //     0xa4e860: ubfx            x6, x6, #0xc, #0x14
    // 0xa4e864: stp             x5, x0, [SP]
    // 0xa4e868: mov             x0, x6
    // 0xa4e86c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e86c: sub             lr, x0, #0xb7
    //     0xa4e870: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e874: blr             lr
    // 0xa4e878: cmp             w0, NULL
    // 0xa4e87c: b.ne            #0xa4e888
    // 0xa4e880: r0 = Null
    //     0xa4e880: mov             x0, NULL
    // 0xa4e884: b               #0xa4e894
    // 0xa4e888: LoadField: r1 = r0->field_63
    //     0xa4e888: ldur            w1, [x0, #0x63]
    // 0xa4e88c: DecompressPointer r1
    //     0xa4e88c: add             x1, x1, HEAP, lsl #32
    // 0xa4e890: mov             x0, x1
    // 0xa4e894: r1 = 60
    //     0xa4e894: movz            x1, #0x3c
    // 0xa4e898: branchIfSmi(r0, 0xa4e8a4)
    //     0xa4e898: tbz             w0, #0, #0xa4e8a4
    // 0xa4e89c: r1 = LoadClassIdInstr(r0)
    //     0xa4e89c: ldur            x1, [x0, #-1]
    //     0xa4e8a0: ubfx            x1, x1, #0xc, #0x14
    // 0xa4e8a4: r16 = 0.000000
    //     0xa4e8a4: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa4e8a8: stp             x16, x0, [SP]
    // 0xa4e8ac: mov             x0, x1
    // 0xa4e8b0: mov             lr, x0
    // 0xa4e8b4: ldr             lr, [x21, lr, lsl #3]
    // 0xa4e8b8: blr             lr
    // 0xa4e8bc: tbz             w0, #4, #0xa4ea24
    // 0xa4e8c0: ldur            x1, [fp, #-8]
    // 0xa4e8c4: ldur            x2, [fp, #-0x10]
    // 0xa4e8c8: LoadField: r0 = r1->field_f
    //     0xa4e8c8: ldur            w0, [x1, #0xf]
    // 0xa4e8cc: DecompressPointer r0
    //     0xa4e8cc: add             x0, x0, HEAP, lsl #32
    // 0xa4e8d0: LoadField: r3 = r0->field_b
    //     0xa4e8d0: ldur            w3, [x0, #0xb]
    // 0xa4e8d4: DecompressPointer r3
    //     0xa4e8d4: add             x3, x3, HEAP, lsl #32
    // 0xa4e8d8: cmp             w3, NULL
    // 0xa4e8dc: b.eq            #0xa4f490
    // 0xa4e8e0: LoadField: r0 = r3->field_b
    //     0xa4e8e0: ldur            w0, [x3, #0xb]
    // 0xa4e8e4: DecompressPointer r0
    //     0xa4e8e4: add             x0, x0, HEAP, lsl #32
    // 0xa4e8e8: LoadField: r3 = r2->field_f
    //     0xa4e8e8: ldur            w3, [x2, #0xf]
    // 0xa4e8ec: DecompressPointer r3
    //     0xa4e8ec: add             x3, x3, HEAP, lsl #32
    // 0xa4e8f0: r4 = LoadClassIdInstr(r0)
    //     0xa4e8f0: ldur            x4, [x0, #-1]
    //     0xa4e8f4: ubfx            x4, x4, #0xc, #0x14
    // 0xa4e8f8: stp             x3, x0, [SP]
    // 0xa4e8fc: mov             x0, x4
    // 0xa4e900: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e900: sub             lr, x0, #0xb7
    //     0xa4e904: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e908: blr             lr
    // 0xa4e90c: cmp             w0, NULL
    // 0xa4e910: b.eq            #0xa4ea24
    // 0xa4e914: LoadField: r1 = r0->field_63
    //     0xa4e914: ldur            w1, [x0, #0x63]
    // 0xa4e918: DecompressPointer r1
    //     0xa4e918: add             x1, x1, HEAP, lsl #32
    // 0xa4e91c: cmp             w1, NULL
    // 0xa4e920: b.eq            #0xa4ea24
    // 0xa4e924: ldur            x0, [fp, #-8]
    // 0xa4e928: ldur            x3, [fp, #-0x10]
    // 0xa4e92c: r1 = Null
    //     0xa4e92c: mov             x1, NULL
    // 0xa4e930: r2 = 6
    //     0xa4e930: movz            x2, #0x6
    // 0xa4e934: r0 = AllocateArray()
    //     0xa4e934: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4e938: mov             x1, x0
    // 0xa4e93c: stur            x1, [fp, #-0x40]
    // 0xa4e940: r16 = " | "
    //     0xa4e940: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xa4e944: ldr             x16, [x16, #0xd80]
    // 0xa4e948: StoreField: r1->field_f = r16
    //     0xa4e948: stur            w16, [x1, #0xf]
    // 0xa4e94c: ldur            x2, [fp, #-8]
    // 0xa4e950: LoadField: r0 = r2->field_f
    //     0xa4e950: ldur            w0, [x2, #0xf]
    // 0xa4e954: DecompressPointer r0
    //     0xa4e954: add             x0, x0, HEAP, lsl #32
    // 0xa4e958: LoadField: r3 = r0->field_b
    //     0xa4e958: ldur            w3, [x0, #0xb]
    // 0xa4e95c: DecompressPointer r3
    //     0xa4e95c: add             x3, x3, HEAP, lsl #32
    // 0xa4e960: cmp             w3, NULL
    // 0xa4e964: b.eq            #0xa4f494
    // 0xa4e968: LoadField: r0 = r3->field_b
    //     0xa4e968: ldur            w0, [x3, #0xb]
    // 0xa4e96c: DecompressPointer r0
    //     0xa4e96c: add             x0, x0, HEAP, lsl #32
    // 0xa4e970: ldur            x3, [fp, #-0x10]
    // 0xa4e974: LoadField: r4 = r3->field_f
    //     0xa4e974: ldur            w4, [x3, #0xf]
    // 0xa4e978: DecompressPointer r4
    //     0xa4e978: add             x4, x4, HEAP, lsl #32
    // 0xa4e97c: r5 = LoadClassIdInstr(r0)
    //     0xa4e97c: ldur            x5, [x0, #-1]
    //     0xa4e980: ubfx            x5, x5, #0xc, #0x14
    // 0xa4e984: stp             x4, x0, [SP]
    // 0xa4e988: mov             x0, x5
    // 0xa4e98c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4e98c: sub             lr, x0, #0xb7
    //     0xa4e990: ldr             lr, [x21, lr, lsl #3]
    //     0xa4e994: blr             lr
    // 0xa4e998: cmp             w0, NULL
    // 0xa4e99c: b.ne            #0xa4e9a8
    // 0xa4e9a0: r0 = Null
    //     0xa4e9a0: mov             x0, NULL
    // 0xa4e9a4: b               #0xa4e9dc
    // 0xa4e9a8: LoadField: r1 = r0->field_63
    //     0xa4e9a8: ldur            w1, [x0, #0x63]
    // 0xa4e9ac: DecompressPointer r1
    //     0xa4e9ac: add             x1, x1, HEAP, lsl #32
    // 0xa4e9b0: cmp             w1, NULL
    // 0xa4e9b4: b.ne            #0xa4e9c0
    // 0xa4e9b8: r0 = Null
    //     0xa4e9b8: mov             x0, NULL
    // 0xa4e9bc: b               #0xa4e9dc
    // 0xa4e9c0: stp             xzr, x1, [SP]
    // 0xa4e9c4: r4 = 0
    //     0xa4e9c4: movz            x4, #0
    // 0xa4e9c8: ldr             x0, [SP, #8]
    // 0xa4e9cc: r16 = UnlinkedCall_0x613b5c
    //     0xa4e9cc: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5aaa8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4e9d0: add             x16, x16, #0xaa8
    // 0xa4e9d4: ldp             x5, lr, [x16]
    // 0xa4e9d8: blr             lr
    // 0xa4e9dc: ldur            x2, [fp, #-0x40]
    // 0xa4e9e0: mov             x1, x2
    // 0xa4e9e4: ArrayStore: r1[1] = r0  ; List_4
    //     0xa4e9e4: add             x25, x1, #0x13
    //     0xa4e9e8: str             w0, [x25]
    //     0xa4e9ec: tbz             w0, #0, #0xa4ea08
    //     0xa4e9f0: ldurb           w16, [x1, #-1]
    //     0xa4e9f4: ldurb           w17, [x0, #-1]
    //     0xa4e9f8: and             x16, x17, x16, lsr #2
    //     0xa4e9fc: tst             x16, HEAP, lsr #32
    //     0xa4ea00: b.eq            #0xa4ea08
    //     0xa4ea04: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4ea08: r16 = "% OFF"
    //     0xa4ea08: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xa4ea0c: ldr             x16, [x16, #0xd98]
    // 0xa4ea10: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4ea10: stur            w16, [x2, #0x17]
    // 0xa4ea14: str             x2, [SP]
    // 0xa4ea18: r0 = _interpolate()
    //     0xa4ea18: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa4ea1c: mov             x4, x0
    // 0xa4ea20: b               #0xa4ea28
    // 0xa4ea24: r4 = ""
    //     0xa4ea24: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4ea28: ldur            x0, [fp, #-8]
    // 0xa4ea2c: ldur            x3, [fp, #-0x38]
    // 0xa4ea30: ldur            x2, [fp, #-0x58]
    // 0xa4ea34: ldr             x1, [fp, #0x18]
    // 0xa4ea38: stur            x4, [fp, #-0x40]
    // 0xa4ea3c: r0 = of()
    //     0xa4ea3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4ea40: LoadField: r1 = r0->field_87
    //     0xa4ea40: ldur            w1, [x0, #0x87]
    // 0xa4ea44: DecompressPointer r1
    //     0xa4ea44: add             x1, x1, HEAP, lsl #32
    // 0xa4ea48: LoadField: r0 = r1->field_2b
    //     0xa4ea48: ldur            w0, [x1, #0x2b]
    // 0xa4ea4c: DecompressPointer r0
    //     0xa4ea4c: add             x0, x0, HEAP, lsl #32
    // 0xa4ea50: r16 = Instance_Color
    //     0xa4ea50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa4ea54: ldr             x16, [x16, #0x858]
    // 0xa4ea58: r30 = 14.000000
    //     0xa4ea58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4ea5c: ldr             lr, [lr, #0x1d8]
    // 0xa4ea60: stp             lr, x16, [SP]
    // 0xa4ea64: mov             x1, x0
    // 0xa4ea68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa4ea68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa4ea6c: ldr             x4, [x4, #0x9b8]
    // 0xa4ea70: r0 = copyWith()
    //     0xa4ea70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4ea74: stur            x0, [fp, #-0x50]
    // 0xa4ea78: r0 = TextSpan()
    //     0xa4ea78: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4ea7c: mov             x3, x0
    // 0xa4ea80: ldur            x0, [fp, #-0x40]
    // 0xa4ea84: stur            x3, [fp, #-0x60]
    // 0xa4ea88: StoreField: r3->field_b = r0
    //     0xa4ea88: stur            w0, [x3, #0xb]
    // 0xa4ea8c: r0 = Instance__DeferringMouseCursor
    //     0xa4ea8c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4ea90: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4ea90: stur            w0, [x3, #0x17]
    // 0xa4ea94: ldur            x1, [fp, #-0x50]
    // 0xa4ea98: StoreField: r3->field_7 = r1
    //     0xa4ea98: stur            w1, [x3, #7]
    // 0xa4ea9c: r1 = Null
    //     0xa4ea9c: mov             x1, NULL
    // 0xa4eaa0: r2 = 6
    //     0xa4eaa0: movz            x2, #0x6
    // 0xa4eaa4: r0 = AllocateArray()
    //     0xa4eaa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4eaa8: mov             x2, x0
    // 0xa4eaac: ldur            x0, [fp, #-0x38]
    // 0xa4eab0: stur            x2, [fp, #-0x40]
    // 0xa4eab4: StoreField: r2->field_f = r0
    //     0xa4eab4: stur            w0, [x2, #0xf]
    // 0xa4eab8: ldur            x0, [fp, #-0x58]
    // 0xa4eabc: StoreField: r2->field_13 = r0
    //     0xa4eabc: stur            w0, [x2, #0x13]
    // 0xa4eac0: ldur            x0, [fp, #-0x60]
    // 0xa4eac4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4eac4: stur            w0, [x2, #0x17]
    // 0xa4eac8: r1 = <InlineSpan>
    //     0xa4eac8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa4eacc: ldr             x1, [x1, #0xe40]
    // 0xa4ead0: r0 = AllocateGrowableArray()
    //     0xa4ead0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4ead4: mov             x1, x0
    // 0xa4ead8: ldur            x0, [fp, #-0x40]
    // 0xa4eadc: stur            x1, [fp, #-0x38]
    // 0xa4eae0: StoreField: r1->field_f = r0
    //     0xa4eae0: stur            w0, [x1, #0xf]
    // 0xa4eae4: r0 = 6
    //     0xa4eae4: movz            x0, #0x6
    // 0xa4eae8: StoreField: r1->field_b = r0
    //     0xa4eae8: stur            w0, [x1, #0xb]
    // 0xa4eaec: r0 = TextSpan()
    //     0xa4eaec: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa4eaf0: mov             x1, x0
    // 0xa4eaf4: ldur            x0, [fp, #-0x38]
    // 0xa4eaf8: stur            x1, [fp, #-0x40]
    // 0xa4eafc: StoreField: r1->field_f = r0
    //     0xa4eafc: stur            w0, [x1, #0xf]
    // 0xa4eb00: r0 = Instance__DeferringMouseCursor
    //     0xa4eb00: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa4eb04: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4eb04: stur            w0, [x1, #0x17]
    // 0xa4eb08: r0 = RichText()
    //     0xa4eb08: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa4eb0c: mov             x1, x0
    // 0xa4eb10: ldur            x2, [fp, #-0x40]
    // 0xa4eb14: stur            x0, [fp, #-0x38]
    // 0xa4eb18: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4eb18: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4eb1c: r0 = RichText()
    //     0xa4eb1c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa4eb20: r0 = Padding()
    //     0xa4eb20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4eb24: mov             x2, x0
    // 0xa4eb28: r0 = Instance_EdgeInsets
    //     0xa4eb28: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xa4eb2c: ldr             x0, [x0, #0xf30]
    // 0xa4eb30: stur            x2, [fp, #-0x40]
    // 0xa4eb34: StoreField: r2->field_f = r0
    //     0xa4eb34: stur            w0, [x2, #0xf]
    // 0xa4eb38: ldur            x0, [fp, #-0x38]
    // 0xa4eb3c: StoreField: r2->field_b = r0
    //     0xa4eb3c: stur            w0, [x2, #0xb]
    // 0xa4eb40: ldur            x0, [fp, #-8]
    // 0xa4eb44: LoadField: r1 = r0->field_f
    //     0xa4eb44: ldur            w1, [x0, #0xf]
    // 0xa4eb48: DecompressPointer r1
    //     0xa4eb48: add             x1, x1, HEAP, lsl #32
    // 0xa4eb4c: LoadField: r3 = r1->field_b
    //     0xa4eb4c: ldur            w3, [x1, #0xb]
    // 0xa4eb50: DecompressPointer r3
    //     0xa4eb50: add             x3, x3, HEAP, lsl #32
    // 0xa4eb54: cmp             w3, NULL
    // 0xa4eb58: b.eq            #0xa4f498
    // 0xa4eb5c: LoadField: r1 = r3->field_1f
    //     0xa4eb5c: ldur            w1, [x3, #0x1f]
    // 0xa4eb60: DecompressPointer r1
    //     0xa4eb60: add             x1, x1, HEAP, lsl #32
    // 0xa4eb64: LoadField: r4 = r1->field_1f
    //     0xa4eb64: ldur            w4, [x1, #0x1f]
    // 0xa4eb68: DecompressPointer r4
    //     0xa4eb68: add             x4, x4, HEAP, lsl #32
    // 0xa4eb6c: cmp             w4, NULL
    // 0xa4eb70: b.ne            #0xa4eb7c
    // 0xa4eb74: r1 = Null
    //     0xa4eb74: mov             x1, NULL
    // 0xa4eb78: b               #0xa4eb84
    // 0xa4eb7c: LoadField: r1 = r4->field_7
    //     0xa4eb7c: ldur            w1, [x4, #7]
    // 0xa4eb80: DecompressPointer r1
    //     0xa4eb80: add             x1, x1, HEAP, lsl #32
    // 0xa4eb84: cmp             w1, NULL
    // 0xa4eb88: b.ne            #0xa4eb94
    // 0xa4eb8c: r4 = false
    //     0xa4eb8c: add             x4, NULL, #0x30  ; false
    // 0xa4eb90: b               #0xa4eb98
    // 0xa4eb94: mov             x4, x1
    // 0xa4eb98: stur            x4, [fp, #-0x38]
    // 0xa4eb9c: LoadField: r1 = r3->field_1f
    //     0xa4eb9c: ldur            w1, [x3, #0x1f]
    // 0xa4eba0: DecompressPointer r1
    //     0xa4eba0: add             x1, x1, HEAP, lsl #32
    // 0xa4eba4: LoadField: r5 = r1->field_3f
    //     0xa4eba4: ldur            w5, [x1, #0x3f]
    // 0xa4eba8: DecompressPointer r5
    //     0xa4eba8: add             x5, x5, HEAP, lsl #32
    // 0xa4ebac: cmp             w5, NULL
    // 0xa4ebb0: b.ne            #0xa4ebbc
    // 0xa4ebb4: r1 = Null
    //     0xa4ebb4: mov             x1, NULL
    // 0xa4ebb8: b               #0xa4ebc4
    // 0xa4ebbc: LoadField: r1 = r5->field_23
    //     0xa4ebbc: ldur            w1, [x5, #0x23]
    // 0xa4ebc0: DecompressPointer r1
    //     0xa4ebc0: add             x1, x1, HEAP, lsl #32
    // 0xa4ebc4: cmp             w1, NULL
    // 0xa4ebc8: b.eq            #0xa4f1dc
    // 0xa4ebcc: tbnz            w1, #4, #0xa4f1dc
    // 0xa4ebd0: LoadField: r1 = r3->field_13
    //     0xa4ebd0: ldur            w1, [x3, #0x13]
    // 0xa4ebd4: DecompressPointer r1
    //     0xa4ebd4: add             x1, x1, HEAP, lsl #32
    // 0xa4ebd8: tbnz            w1, #4, #0xa4f1ac
    // 0xa4ebdc: ldur            x3, [fp, #-0x10]
    // 0xa4ebe0: r1 = Instance_Color
    //     0xa4ebe0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa4ebe4: d0 = 0.025000
    //     0xa4ebe4: add             x17, PP, #0x5a, lsl #12  ; [pp+0x5a160] IMM: double(0.025) from 0x3f9999999999999a
    //     0xa4ebe8: ldr             d0, [x17, #0x160]
    // 0xa4ebec: r0 = withOpacity()
    //     0xa4ebec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa4ebf0: stur            x0, [fp, #-0x50]
    // 0xa4ebf4: r0 = BoxDecoration()
    //     0xa4ebf4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4ebf8: mov             x1, x0
    // 0xa4ebfc: ldur            x0, [fp, #-0x50]
    // 0xa4ec00: stur            x1, [fp, #-0x58]
    // 0xa4ec04: StoreField: r1->field_7 = r0
    //     0xa4ec04: stur            w0, [x1, #7]
    // 0xa4ec08: r0 = Instance_BoxShape
    //     0xa4ec08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xa4ec0c: ldr             x0, [x0, #0x970]
    // 0xa4ec10: StoreField: r1->field_23 = r0
    //     0xa4ec10: stur            w0, [x1, #0x23]
    // 0xa4ec14: r0 = SvgPicture()
    //     0xa4ec14: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa4ec18: stur            x0, [fp, #-0x50]
    // 0xa4ec1c: r16 = "whatsapp"
    //     0xa4ec1c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10068] "whatsapp"
    //     0xa4ec20: ldr             x16, [x16, #0x68]
    // 0xa4ec24: r30 = Instance_BoxFit
    //     0xa4ec24: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xa4ec28: ldr             lr, [lr, #0xb18]
    // 0xa4ec2c: stp             lr, x16, [SP]
    // 0xa4ec30: mov             x1, x0
    // 0xa4ec34: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xa4ec34: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xa4ec38: ldr             x2, [x2, #0x140]
    // 0xa4ec3c: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xa4ec3c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xa4ec40: ldr             x4, [x4, #0xb28]
    // 0xa4ec44: r0 = SvgPicture.asset()
    //     0xa4ec44: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa4ec48: r0 = Container()
    //     0xa4ec48: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4ec4c: stur            x0, [fp, #-0x60]
    // 0xa4ec50: r16 = 32.000000
    //     0xa4ec50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa4ec54: ldr             x16, [x16, #0x848]
    // 0xa4ec58: r30 = 32.000000
    //     0xa4ec58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa4ec5c: ldr             lr, [lr, #0x848]
    // 0xa4ec60: stp             lr, x16, [SP, #0x10]
    // 0xa4ec64: ldur            x16, [fp, #-0x58]
    // 0xa4ec68: ldur            lr, [fp, #-0x50]
    // 0xa4ec6c: stp             lr, x16, [SP]
    // 0xa4ec70: mov             x1, x0
    // 0xa4ec74: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa4ec74: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa4ec78: ldr             x4, [x4, #0x870]
    // 0xa4ec7c: r0 = Container()
    //     0xa4ec7c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4ec80: r16 = <Size?>
    //     0xa4ec80: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xa4ec84: ldr             x16, [x16, #0x768]
    // 0xa4ec88: r30 = Instance_Size
    //     0xa4ec88: add             lr, PP, #0x5a, lsl #12  ; [pp+0x5a168] Obj!Size@d6c201
    //     0xa4ec8c: ldr             lr, [lr, #0x168]
    // 0xa4ec90: stp             lr, x16, [SP]
    // 0xa4ec94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa4ec94: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa4ec98: r0 = all()
    //     0xa4ec98: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa4ec9c: mov             x2, x0
    // 0xa4eca0: ldur            x1, [fp, #-8]
    // 0xa4eca4: stur            x2, [fp, #-0x50]
    // 0xa4eca8: LoadField: r0 = r1->field_f
    //     0xa4eca8: ldur            w0, [x1, #0xf]
    // 0xa4ecac: DecompressPointer r0
    //     0xa4ecac: add             x0, x0, HEAP, lsl #32
    // 0xa4ecb0: LoadField: r3 = r0->field_b
    //     0xa4ecb0: ldur            w3, [x0, #0xb]
    // 0xa4ecb4: DecompressPointer r3
    //     0xa4ecb4: add             x3, x3, HEAP, lsl #32
    // 0xa4ecb8: cmp             w3, NULL
    // 0xa4ecbc: b.eq            #0xa4f49c
    // 0xa4ecc0: LoadField: r0 = r3->field_b
    //     0xa4ecc0: ldur            w0, [x3, #0xb]
    // 0xa4ecc4: DecompressPointer r0
    //     0xa4ecc4: add             x0, x0, HEAP, lsl #32
    // 0xa4ecc8: ldur            x3, [fp, #-0x10]
    // 0xa4eccc: LoadField: r4 = r3->field_f
    //     0xa4eccc: ldur            w4, [x3, #0xf]
    // 0xa4ecd0: DecompressPointer r4
    //     0xa4ecd0: add             x4, x4, HEAP, lsl #32
    // 0xa4ecd4: r5 = LoadClassIdInstr(r0)
    //     0xa4ecd4: ldur            x5, [x0, #-1]
    //     0xa4ecd8: ubfx            x5, x5, #0xc, #0x14
    // 0xa4ecdc: stp             x4, x0, [SP]
    // 0xa4ece0: mov             x0, x5
    // 0xa4ece4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4ece4: sub             lr, x0, #0xb7
    //     0xa4ece8: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ecec: blr             lr
    // 0xa4ecf0: cmp             w0, NULL
    // 0xa4ecf4: b.ne            #0xa4ed00
    // 0xa4ecf8: r0 = Null
    //     0xa4ecf8: mov             x0, NULL
    // 0xa4ecfc: b               #0xa4ed0c
    // 0xa4ed00: LoadField: r1 = r0->field_4f
    //     0xa4ed00: ldur            w1, [x0, #0x4f]
    // 0xa4ed04: DecompressPointer r1
    //     0xa4ed04: add             x1, x1, HEAP, lsl #32
    // 0xa4ed08: mov             x0, x1
    // 0xa4ed0c: cmp             w0, NULL
    // 0xa4ed10: b.eq            #0xa4ed18
    // 0xa4ed14: tbnz            w0, #4, #0xa4ed5c
    // 0xa4ed18: ldr             x1, [fp, #0x18]
    // 0xa4ed1c: r0 = of()
    //     0xa4ed1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4ed20: LoadField: r1 = r0->field_5b
    //     0xa4ed20: ldur            w1, [x0, #0x5b]
    // 0xa4ed24: DecompressPointer r1
    //     0xa4ed24: add             x1, x1, HEAP, lsl #32
    // 0xa4ed28: r0 = LoadClassIdInstr(r1)
    //     0xa4ed28: ldur            x0, [x1, #-1]
    //     0xa4ed2c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4ed30: d0 = 0.200000
    //     0xa4ed30: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xa4ed34: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa4ed34: sub             lr, x0, #0xffa
    //     0xa4ed38: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ed3c: blr             lr
    // 0xa4ed40: r16 = <Color>
    //     0xa4ed40: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa4ed44: ldr             x16, [x16, #0xf80]
    // 0xa4ed48: stp             x0, x16, [SP]
    // 0xa4ed4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa4ed4c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa4ed50: r0 = all()
    //     0xa4ed50: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa4ed54: mov             x4, x0
    // 0xa4ed58: b               #0xa4ed78
    // 0xa4ed5c: r16 = <Color>
    //     0xa4ed5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa4ed60: ldr             x16, [x16, #0xf80]
    // 0xa4ed64: r30 = Instance_Color
    //     0xa4ed64: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa4ed68: stp             lr, x16, [SP]
    // 0xa4ed6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa4ed6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa4ed70: r0 = all()
    //     0xa4ed70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa4ed74: mov             x4, x0
    // 0xa4ed78: ldur            x0, [fp, #-8]
    // 0xa4ed7c: ldur            x3, [fp, #-0x10]
    // 0xa4ed80: ldur            x2, [fp, #-0x50]
    // 0xa4ed84: ldr             x1, [fp, #0x18]
    // 0xa4ed88: stur            x4, [fp, #-0x58]
    // 0xa4ed8c: r0 = of()
    //     0xa4ed8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4ed90: LoadField: r1 = r0->field_5b
    //     0xa4ed90: ldur            w1, [x0, #0x5b]
    // 0xa4ed94: DecompressPointer r1
    //     0xa4ed94: add             x1, x1, HEAP, lsl #32
    // 0xa4ed98: stur            x1, [fp, #-0x70]
    // 0xa4ed9c: r0 = BorderSide()
    //     0xa4ed9c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xa4eda0: mov             x1, x0
    // 0xa4eda4: ldur            x0, [fp, #-0x70]
    // 0xa4eda8: stur            x1, [fp, #-0x78]
    // 0xa4edac: StoreField: r1->field_7 = r0
    //     0xa4edac: stur            w0, [x1, #7]
    // 0xa4edb0: d0 = 1.000000
    //     0xa4edb0: fmov            d0, #1.00000000
    // 0xa4edb4: StoreField: r1->field_b = d0
    //     0xa4edb4: stur            d0, [x1, #0xb]
    // 0xa4edb8: r0 = Instance_BorderStyle
    //     0xa4edb8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xa4edbc: ldr             x0, [x0, #0xf68]
    // 0xa4edc0: StoreField: r1->field_13 = r0
    //     0xa4edc0: stur            w0, [x1, #0x13]
    // 0xa4edc4: d0 = -1.000000
    //     0xa4edc4: fmov            d0, #-1.00000000
    // 0xa4edc8: ArrayStore: r1[0] = d0  ; List_8
    //     0xa4edc8: stur            d0, [x1, #0x17]
    // 0xa4edcc: r0 = RoundedRectangleBorder()
    //     0xa4edcc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa4edd0: mov             x1, x0
    // 0xa4edd4: r0 = Instance_BorderRadius
    //     0xa4edd4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa4edd8: ldr             x0, [x0, #0xbe8]
    // 0xa4eddc: StoreField: r1->field_b = r0
    //     0xa4eddc: stur            w0, [x1, #0xb]
    // 0xa4ede0: ldur            x0, [fp, #-0x78]
    // 0xa4ede4: StoreField: r1->field_7 = r0
    //     0xa4ede4: stur            w0, [x1, #7]
    // 0xa4ede8: r16 = <RoundedRectangleBorder>
    //     0xa4ede8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa4edec: ldr             x16, [x16, #0xf78]
    // 0xa4edf0: stp             x1, x16, [SP]
    // 0xa4edf4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa4edf4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa4edf8: r0 = all()
    //     0xa4edf8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa4edfc: stur            x0, [fp, #-0x70]
    // 0xa4ee00: r0 = ButtonStyle()
    //     0xa4ee00: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa4ee04: mov             x1, x0
    // 0xa4ee08: ldur            x0, [fp, #-0x58]
    // 0xa4ee0c: stur            x1, [fp, #-0x78]
    // 0xa4ee10: StoreField: r1->field_b = r0
    //     0xa4ee10: stur            w0, [x1, #0xb]
    // 0xa4ee14: ldur            x0, [fp, #-0x50]
    // 0xa4ee18: StoreField: r1->field_27 = r0
    //     0xa4ee18: stur            w0, [x1, #0x27]
    // 0xa4ee1c: ldur            x0, [fp, #-0x70]
    // 0xa4ee20: StoreField: r1->field_43 = r0
    //     0xa4ee20: stur            w0, [x1, #0x43]
    // 0xa4ee24: r0 = TextButtonThemeData()
    //     0xa4ee24: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa4ee28: mov             x1, x0
    // 0xa4ee2c: ldur            x0, [fp, #-0x78]
    // 0xa4ee30: stur            x1, [fp, #-0x50]
    // 0xa4ee34: StoreField: r1->field_7 = r0
    //     0xa4ee34: stur            w0, [x1, #7]
    // 0xa4ee38: ldur            x2, [fp, #-8]
    // 0xa4ee3c: LoadField: r0 = r2->field_f
    //     0xa4ee3c: ldur            w0, [x2, #0xf]
    // 0xa4ee40: DecompressPointer r0
    //     0xa4ee40: add             x0, x0, HEAP, lsl #32
    // 0xa4ee44: LoadField: r3 = r0->field_b
    //     0xa4ee44: ldur            w3, [x0, #0xb]
    // 0xa4ee48: DecompressPointer r3
    //     0xa4ee48: add             x3, x3, HEAP, lsl #32
    // 0xa4ee4c: cmp             w3, NULL
    // 0xa4ee50: b.eq            #0xa4f4a0
    // 0xa4ee54: LoadField: r0 = r3->field_b
    //     0xa4ee54: ldur            w0, [x3, #0xb]
    // 0xa4ee58: DecompressPointer r0
    //     0xa4ee58: add             x0, x0, HEAP, lsl #32
    // 0xa4ee5c: ldur            x3, [fp, #-0x10]
    // 0xa4ee60: LoadField: r4 = r3->field_f
    //     0xa4ee60: ldur            w4, [x3, #0xf]
    // 0xa4ee64: DecompressPointer r4
    //     0xa4ee64: add             x4, x4, HEAP, lsl #32
    // 0xa4ee68: r5 = LoadClassIdInstr(r0)
    //     0xa4ee68: ldur            x5, [x0, #-1]
    //     0xa4ee6c: ubfx            x5, x5, #0xc, #0x14
    // 0xa4ee70: stp             x4, x0, [SP]
    // 0xa4ee74: mov             x0, x5
    // 0xa4ee78: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4ee78: sub             lr, x0, #0xb7
    //     0xa4ee7c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ee80: blr             lr
    // 0xa4ee84: cmp             w0, NULL
    // 0xa4ee88: b.ne            #0xa4ee94
    // 0xa4ee8c: r0 = Null
    //     0xa4ee8c: mov             x0, NULL
    // 0xa4ee90: b               #0xa4eea0
    // 0xa4ee94: LoadField: r1 = r0->field_4f
    //     0xa4ee94: ldur            w1, [x0, #0x4f]
    // 0xa4ee98: DecompressPointer r1
    //     0xa4ee98: add             x1, x1, HEAP, lsl #32
    // 0xa4ee9c: mov             x0, x1
    // 0xa4eea0: cmp             w0, NULL
    // 0xa4eea4: b.eq            #0xa4eeac
    // 0xa4eea8: tbnz            w0, #4, #0xa4efb4
    // 0xa4eeac: ldur            x0, [fp, #-8]
    // 0xa4eeb0: ldur            x2, [fp, #-0x10]
    // 0xa4eeb4: LoadField: r1 = r0->field_f
    //     0xa4eeb4: ldur            w1, [x0, #0xf]
    // 0xa4eeb8: DecompressPointer r1
    //     0xa4eeb8: add             x1, x1, HEAP, lsl #32
    // 0xa4eebc: LoadField: r0 = r1->field_b
    //     0xa4eebc: ldur            w0, [x1, #0xb]
    // 0xa4eec0: DecompressPointer r0
    //     0xa4eec0: add             x0, x0, HEAP, lsl #32
    // 0xa4eec4: cmp             w0, NULL
    // 0xa4eec8: b.eq            #0xa4f4a4
    // 0xa4eecc: LoadField: r1 = r0->field_b
    //     0xa4eecc: ldur            w1, [x0, #0xb]
    // 0xa4eed0: DecompressPointer r1
    //     0xa4eed0: add             x1, x1, HEAP, lsl #32
    // 0xa4eed4: LoadField: r0 = r2->field_f
    //     0xa4eed4: ldur            w0, [x2, #0xf]
    // 0xa4eed8: DecompressPointer r0
    //     0xa4eed8: add             x0, x0, HEAP, lsl #32
    // 0xa4eedc: r3 = LoadClassIdInstr(r1)
    //     0xa4eedc: ldur            x3, [x1, #-1]
    //     0xa4eee0: ubfx            x3, x3, #0xc, #0x14
    // 0xa4eee4: stp             x0, x1, [SP]
    // 0xa4eee8: mov             x0, x3
    // 0xa4eeec: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4eeec: sub             lr, x0, #0xb7
    //     0xa4eef0: ldr             lr, [x21, lr, lsl #3]
    //     0xa4eef4: blr             lr
    // 0xa4eef8: cmp             w0, NULL
    // 0xa4eefc: b.ne            #0xa4ef08
    // 0xa4ef00: r0 = Null
    //     0xa4ef00: mov             x0, NULL
    // 0xa4ef04: b               #0xa4ef14
    // 0xa4ef08: LoadField: r1 = r0->field_f
    //     0xa4ef08: ldur            w1, [x0, #0xf]
    // 0xa4ef0c: DecompressPointer r1
    //     0xa4ef0c: add             x1, x1, HEAP, lsl #32
    // 0xa4ef10: mov             x0, x1
    // 0xa4ef14: cmp             w0, NULL
    // 0xa4ef18: b.ne            #0xa4ef24
    // 0xa4ef1c: r1 = ""
    //     0xa4ef1c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4ef20: b               #0xa4ef28
    // 0xa4ef24: mov             x1, x0
    // 0xa4ef28: r0 = capitalizeFirstWord()
    //     0xa4ef28: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa4ef2c: ldr             x1, [fp, #0x18]
    // 0xa4ef30: stur            x0, [fp, #-0x58]
    // 0xa4ef34: r0 = of()
    //     0xa4ef34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4ef38: LoadField: r1 = r0->field_87
    //     0xa4ef38: ldur            w1, [x0, #0x87]
    // 0xa4ef3c: DecompressPointer r1
    //     0xa4ef3c: add             x1, x1, HEAP, lsl #32
    // 0xa4ef40: LoadField: r0 = r1->field_7
    //     0xa4ef40: ldur            w0, [x1, #7]
    // 0xa4ef44: DecompressPointer r0
    //     0xa4ef44: add             x0, x0, HEAP, lsl #32
    // 0xa4ef48: ldr             x1, [fp, #0x18]
    // 0xa4ef4c: stur            x0, [fp, #-0x70]
    // 0xa4ef50: r0 = of()
    //     0xa4ef50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4ef54: LoadField: r1 = r0->field_5b
    //     0xa4ef54: ldur            w1, [x0, #0x5b]
    // 0xa4ef58: DecompressPointer r1
    //     0xa4ef58: add             x1, x1, HEAP, lsl #32
    // 0xa4ef5c: r0 = LoadClassIdInstr(r1)
    //     0xa4ef5c: ldur            x0, [x1, #-1]
    //     0xa4ef60: ubfx            x0, x0, #0xc, #0x14
    // 0xa4ef64: d0 = 0.400000
    //     0xa4ef64: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa4ef68: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa4ef68: sub             lr, x0, #0xffa
    //     0xa4ef6c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ef70: blr             lr
    // 0xa4ef74: r16 = 14.000000
    //     0xa4ef74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4ef78: ldr             x16, [x16, #0x1d8]
    // 0xa4ef7c: stp             x0, x16, [SP]
    // 0xa4ef80: ldur            x1, [fp, #-0x70]
    // 0xa4ef84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4ef84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4ef88: ldr             x4, [x4, #0xaa0]
    // 0xa4ef8c: r0 = copyWith()
    //     0xa4ef8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4ef90: stur            x0, [fp, #-0x70]
    // 0xa4ef94: r0 = Text()
    //     0xa4ef94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4ef98: mov             x1, x0
    // 0xa4ef9c: ldur            x0, [fp, #-0x58]
    // 0xa4efa0: StoreField: r1->field_b = r0
    //     0xa4efa0: stur            w0, [x1, #0xb]
    // 0xa4efa4: ldur            x0, [fp, #-0x70]
    // 0xa4efa8: StoreField: r1->field_13 = r0
    //     0xa4efa8: stur            w0, [x1, #0x13]
    // 0xa4efac: mov             x4, x1
    // 0xa4efb0: b               #0xa4f0a0
    // 0xa4efb4: ldur            x0, [fp, #-8]
    // 0xa4efb8: ldur            x2, [fp, #-0x10]
    // 0xa4efbc: LoadField: r1 = r0->field_f
    //     0xa4efbc: ldur            w1, [x0, #0xf]
    // 0xa4efc0: DecompressPointer r1
    //     0xa4efc0: add             x1, x1, HEAP, lsl #32
    // 0xa4efc4: LoadField: r0 = r1->field_b
    //     0xa4efc4: ldur            w0, [x1, #0xb]
    // 0xa4efc8: DecompressPointer r0
    //     0xa4efc8: add             x0, x0, HEAP, lsl #32
    // 0xa4efcc: cmp             w0, NULL
    // 0xa4efd0: b.eq            #0xa4f4a8
    // 0xa4efd4: LoadField: r1 = r0->field_b
    //     0xa4efd4: ldur            w1, [x0, #0xb]
    // 0xa4efd8: DecompressPointer r1
    //     0xa4efd8: add             x1, x1, HEAP, lsl #32
    // 0xa4efdc: LoadField: r0 = r2->field_f
    //     0xa4efdc: ldur            w0, [x2, #0xf]
    // 0xa4efe0: DecompressPointer r0
    //     0xa4efe0: add             x0, x0, HEAP, lsl #32
    // 0xa4efe4: r3 = LoadClassIdInstr(r1)
    //     0xa4efe4: ldur            x3, [x1, #-1]
    //     0xa4efe8: ubfx            x3, x3, #0xc, #0x14
    // 0xa4efec: stp             x0, x1, [SP]
    // 0xa4eff0: mov             x0, x3
    // 0xa4eff4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4eff4: sub             lr, x0, #0xb7
    //     0xa4eff8: ldr             lr, [x21, lr, lsl #3]
    //     0xa4effc: blr             lr
    // 0xa4f000: cmp             w0, NULL
    // 0xa4f004: b.ne            #0xa4f010
    // 0xa4f008: r0 = Null
    //     0xa4f008: mov             x0, NULL
    // 0xa4f00c: b               #0xa4f01c
    // 0xa4f010: LoadField: r1 = r0->field_f
    //     0xa4f010: ldur            w1, [x0, #0xf]
    // 0xa4f014: DecompressPointer r1
    //     0xa4f014: add             x1, x1, HEAP, lsl #32
    // 0xa4f018: mov             x0, x1
    // 0xa4f01c: cmp             w0, NULL
    // 0xa4f020: b.ne            #0xa4f02c
    // 0xa4f024: r1 = ""
    //     0xa4f024: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f028: b               #0xa4f030
    // 0xa4f02c: mov             x1, x0
    // 0xa4f030: r0 = capitalizeFirstWord()
    //     0xa4f030: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa4f034: ldr             x1, [fp, #0x18]
    // 0xa4f038: stur            x0, [fp, #-8]
    // 0xa4f03c: r0 = of()
    //     0xa4f03c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4f040: LoadField: r1 = r0->field_87
    //     0xa4f040: ldur            w1, [x0, #0x87]
    // 0xa4f044: DecompressPointer r1
    //     0xa4f044: add             x1, x1, HEAP, lsl #32
    // 0xa4f048: LoadField: r0 = r1->field_7
    //     0xa4f048: ldur            w0, [x1, #7]
    // 0xa4f04c: DecompressPointer r0
    //     0xa4f04c: add             x0, x0, HEAP, lsl #32
    // 0xa4f050: ldr             x1, [fp, #0x18]
    // 0xa4f054: stur            x0, [fp, #-0x58]
    // 0xa4f058: r0 = of()
    //     0xa4f058: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4f05c: LoadField: r1 = r0->field_5b
    //     0xa4f05c: ldur            w1, [x0, #0x5b]
    // 0xa4f060: DecompressPointer r1
    //     0xa4f060: add             x1, x1, HEAP, lsl #32
    // 0xa4f064: r16 = 14.000000
    //     0xa4f064: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa4f068: ldr             x16, [x16, #0x1d8]
    // 0xa4f06c: stp             x1, x16, [SP]
    // 0xa4f070: ldur            x1, [fp, #-0x58]
    // 0xa4f074: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa4f074: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa4f078: ldr             x4, [x4, #0xaa0]
    // 0xa4f07c: r0 = copyWith()
    //     0xa4f07c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa4f080: stur            x0, [fp, #-0x58]
    // 0xa4f084: r0 = Text()
    //     0xa4f084: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa4f088: mov             x1, x0
    // 0xa4f08c: ldur            x0, [fp, #-8]
    // 0xa4f090: StoreField: r1->field_b = r0
    //     0xa4f090: stur            w0, [x1, #0xb]
    // 0xa4f094: ldur            x0, [fp, #-0x58]
    // 0xa4f098: StoreField: r1->field_13 = r0
    //     0xa4f098: stur            w0, [x1, #0x13]
    // 0xa4f09c: mov             x4, x1
    // 0xa4f0a0: ldur            x3, [fp, #-0x60]
    // 0xa4f0a4: ldur            x0, [fp, #-0x50]
    // 0xa4f0a8: ldur            x2, [fp, #-0x10]
    // 0xa4f0ac: stur            x4, [fp, #-8]
    // 0xa4f0b0: r1 = Function '<anonymous closure>':.
    //     0xa4f0b0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aab8] AnonymousClosure: (0xa4fb8c), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4f0b4: ldr             x1, [x1, #0xab8]
    // 0xa4f0b8: r0 = AllocateClosure()
    //     0xa4f0b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4f0bc: stur            x0, [fp, #-0x58]
    // 0xa4f0c0: r0 = TextButton()
    //     0xa4f0c0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa4f0c4: mov             x1, x0
    // 0xa4f0c8: ldur            x0, [fp, #-0x58]
    // 0xa4f0cc: stur            x1, [fp, #-0x70]
    // 0xa4f0d0: StoreField: r1->field_b = r0
    //     0xa4f0d0: stur            w0, [x1, #0xb]
    // 0xa4f0d4: r0 = false
    //     0xa4f0d4: add             x0, NULL, #0x30  ; false
    // 0xa4f0d8: StoreField: r1->field_27 = r0
    //     0xa4f0d8: stur            w0, [x1, #0x27]
    // 0xa4f0dc: r2 = true
    //     0xa4f0dc: add             x2, NULL, #0x20  ; true
    // 0xa4f0e0: StoreField: r1->field_2f = r2
    //     0xa4f0e0: stur            w2, [x1, #0x2f]
    // 0xa4f0e4: ldur            x3, [fp, #-8]
    // 0xa4f0e8: StoreField: r1->field_37 = r3
    //     0xa4f0e8: stur            w3, [x1, #0x37]
    // 0xa4f0ec: r0 = TextButtonTheme()
    //     0xa4f0ec: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa4f0f0: mov             x3, x0
    // 0xa4f0f4: ldur            x0, [fp, #-0x50]
    // 0xa4f0f8: stur            x3, [fp, #-8]
    // 0xa4f0fc: StoreField: r3->field_f = r0
    //     0xa4f0fc: stur            w0, [x3, #0xf]
    // 0xa4f100: ldur            x0, [fp, #-0x70]
    // 0xa4f104: StoreField: r3->field_b = r0
    //     0xa4f104: stur            w0, [x3, #0xb]
    // 0xa4f108: r1 = Null
    //     0xa4f108: mov             x1, NULL
    // 0xa4f10c: r2 = 4
    //     0xa4f10c: movz            x2, #0x4
    // 0xa4f110: r0 = AllocateArray()
    //     0xa4f110: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4f114: mov             x2, x0
    // 0xa4f118: ldur            x0, [fp, #-0x60]
    // 0xa4f11c: stur            x2, [fp, #-0x50]
    // 0xa4f120: StoreField: r2->field_f = r0
    //     0xa4f120: stur            w0, [x2, #0xf]
    // 0xa4f124: ldur            x0, [fp, #-8]
    // 0xa4f128: StoreField: r2->field_13 = r0
    //     0xa4f128: stur            w0, [x2, #0x13]
    // 0xa4f12c: r1 = <Widget>
    //     0xa4f12c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4f130: r0 = AllocateGrowableArray()
    //     0xa4f130: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4f134: mov             x1, x0
    // 0xa4f138: ldur            x0, [fp, #-0x50]
    // 0xa4f13c: stur            x1, [fp, #-8]
    // 0xa4f140: StoreField: r1->field_f = r0
    //     0xa4f140: stur            w0, [x1, #0xf]
    // 0xa4f144: r0 = 4
    //     0xa4f144: movz            x0, #0x4
    // 0xa4f148: StoreField: r1->field_b = r0
    //     0xa4f148: stur            w0, [x1, #0xb]
    // 0xa4f14c: r0 = Row()
    //     0xa4f14c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4f150: mov             x1, x0
    // 0xa4f154: r0 = Instance_Axis
    //     0xa4f154: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa4f158: StoreField: r1->field_f = r0
    //     0xa4f158: stur            w0, [x1, #0xf]
    // 0xa4f15c: r0 = Instance_MainAxisAlignment
    //     0xa4f15c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa4f160: ldr             x0, [x0, #0xd10]
    // 0xa4f164: StoreField: r1->field_13 = r0
    //     0xa4f164: stur            w0, [x1, #0x13]
    // 0xa4f168: r0 = Instance_MainAxisSize
    //     0xa4f168: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa4f16c: ldr             x0, [x0, #0xa10]
    // 0xa4f170: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4f170: stur            w0, [x1, #0x17]
    // 0xa4f174: r0 = Instance_CrossAxisAlignment
    //     0xa4f174: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa4f178: ldr             x0, [x0, #0xa18]
    // 0xa4f17c: StoreField: r1->field_1b = r0
    //     0xa4f17c: stur            w0, [x1, #0x1b]
    // 0xa4f180: r0 = Instance_VerticalDirection
    //     0xa4f180: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4f184: ldr             x0, [x0, #0xa20]
    // 0xa4f188: StoreField: r1->field_23 = r0
    //     0xa4f188: stur            w0, [x1, #0x23]
    // 0xa4f18c: r2 = Instance_Clip
    //     0xa4f18c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4f190: ldr             x2, [x2, #0x38]
    // 0xa4f194: StoreField: r1->field_2b = r2
    //     0xa4f194: stur            w2, [x1, #0x2b]
    // 0xa4f198: StoreField: r1->field_2f = rZR
    //     0xa4f198: stur            xzr, [x1, #0x2f]
    // 0xa4f19c: ldur            x3, [fp, #-8]
    // 0xa4f1a0: StoreField: r1->field_b = r3
    //     0xa4f1a0: stur            w3, [x1, #0xb]
    // 0xa4f1a4: mov             x0, x1
    // 0xa4f1a8: b               #0xa4f1d4
    // 0xa4f1ac: r0 = Instance_VerticalDirection
    //     0xa4f1ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4f1b0: ldr             x0, [x0, #0xa20]
    // 0xa4f1b4: r2 = Instance_Clip
    //     0xa4f1b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4f1b8: ldr             x2, [x2, #0x38]
    // 0xa4f1bc: r0 = Container()
    //     0xa4f1bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4f1c0: mov             x1, x0
    // 0xa4f1c4: stur            x0, [fp, #-8]
    // 0xa4f1c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4f1c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4f1cc: r0 = Container()
    //     0xa4f1cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4f1d0: ldur            x0, [fp, #-8]
    // 0xa4f1d4: mov             x7, x0
    // 0xa4f1d8: b               #0xa4f1f4
    // 0xa4f1dc: r0 = Container()
    //     0xa4f1dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4f1e0: mov             x1, x0
    // 0xa4f1e4: stur            x0, [fp, #-8]
    // 0xa4f1e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4f1e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4f1ec: r0 = Container()
    //     0xa4f1ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4f1f0: ldur            x7, [fp, #-8]
    // 0xa4f1f4: ldur            x6, [fp, #-0x20]
    // 0xa4f1f8: ldur            x5, [fp, #-0x18]
    // 0xa4f1fc: ldur            x4, [fp, #-0x28]
    // 0xa4f200: ldur            x3, [fp, #-0x30]
    // 0xa4f204: ldur            x2, [fp, #-0x48]
    // 0xa4f208: ldur            x0, [fp, #-0x40]
    // 0xa4f20c: ldur            x1, [fp, #-0x38]
    // 0xa4f210: stur            x7, [fp, #-8]
    // 0xa4f214: r0 = Visibility()
    //     0xa4f214: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa4f218: mov             x2, x0
    // 0xa4f21c: ldur            x0, [fp, #-8]
    // 0xa4f220: stur            x2, [fp, #-0x50]
    // 0xa4f224: StoreField: r2->field_b = r0
    //     0xa4f224: stur            w0, [x2, #0xb]
    // 0xa4f228: r0 = Instance_SizedBox
    //     0xa4f228: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa4f22c: StoreField: r2->field_f = r0
    //     0xa4f22c: stur            w0, [x2, #0xf]
    // 0xa4f230: ldur            x0, [fp, #-0x38]
    // 0xa4f234: StoreField: r2->field_13 = r0
    //     0xa4f234: stur            w0, [x2, #0x13]
    // 0xa4f238: r0 = false
    //     0xa4f238: add             x0, NULL, #0x30  ; false
    // 0xa4f23c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4f23c: stur            w0, [x2, #0x17]
    // 0xa4f240: StoreField: r2->field_1b = r0
    //     0xa4f240: stur            w0, [x2, #0x1b]
    // 0xa4f244: StoreField: r2->field_1f = r0
    //     0xa4f244: stur            w0, [x2, #0x1f]
    // 0xa4f248: StoreField: r2->field_23 = r0
    //     0xa4f248: stur            w0, [x2, #0x23]
    // 0xa4f24c: StoreField: r2->field_27 = r0
    //     0xa4f24c: stur            w0, [x2, #0x27]
    // 0xa4f250: StoreField: r2->field_2b = r0
    //     0xa4f250: stur            w0, [x2, #0x2b]
    // 0xa4f254: r1 = <FlexParentData>
    //     0xa4f254: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa4f258: ldr             x1, [x1, #0xe00]
    // 0xa4f25c: r0 = Expanded()
    //     0xa4f25c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa4f260: mov             x3, x0
    // 0xa4f264: r0 = 1
    //     0xa4f264: movz            x0, #0x1
    // 0xa4f268: stur            x3, [fp, #-8]
    // 0xa4f26c: StoreField: r3->field_13 = r0
    //     0xa4f26c: stur            x0, [x3, #0x13]
    // 0xa4f270: r0 = Instance_FlexFit
    //     0xa4f270: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa4f274: ldr             x0, [x0, #0xe08]
    // 0xa4f278: StoreField: r3->field_1b = r0
    //     0xa4f278: stur            w0, [x3, #0x1b]
    // 0xa4f27c: ldur            x0, [fp, #-0x50]
    // 0xa4f280: StoreField: r3->field_b = r0
    //     0xa4f280: stur            w0, [x3, #0xb]
    // 0xa4f284: r1 = Null
    //     0xa4f284: mov             x1, NULL
    // 0xa4f288: r2 = 10
    //     0xa4f288: movz            x2, #0xa
    // 0xa4f28c: r0 = AllocateArray()
    //     0xa4f28c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4f290: mov             x2, x0
    // 0xa4f294: ldur            x0, [fp, #-0x28]
    // 0xa4f298: stur            x2, [fp, #-0x38]
    // 0xa4f29c: StoreField: r2->field_f = r0
    //     0xa4f29c: stur            w0, [x2, #0xf]
    // 0xa4f2a0: ldur            x0, [fp, #-0x30]
    // 0xa4f2a4: StoreField: r2->field_13 = r0
    //     0xa4f2a4: stur            w0, [x2, #0x13]
    // 0xa4f2a8: ldur            x0, [fp, #-0x48]
    // 0xa4f2ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4f2ac: stur            w0, [x2, #0x17]
    // 0xa4f2b0: ldur            x0, [fp, #-0x40]
    // 0xa4f2b4: StoreField: r2->field_1b = r0
    //     0xa4f2b4: stur            w0, [x2, #0x1b]
    // 0xa4f2b8: ldur            x0, [fp, #-8]
    // 0xa4f2bc: StoreField: r2->field_1f = r0
    //     0xa4f2bc: stur            w0, [x2, #0x1f]
    // 0xa4f2c0: r1 = <Widget>
    //     0xa4f2c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa4f2c4: r0 = AllocateGrowableArray()
    //     0xa4f2c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa4f2c8: mov             x1, x0
    // 0xa4f2cc: ldur            x0, [fp, #-0x38]
    // 0xa4f2d0: stur            x1, [fp, #-8]
    // 0xa4f2d4: StoreField: r1->field_f = r0
    //     0xa4f2d4: stur            w0, [x1, #0xf]
    // 0xa4f2d8: r0 = 10
    //     0xa4f2d8: movz            x0, #0xa
    // 0xa4f2dc: StoreField: r1->field_b = r0
    //     0xa4f2dc: stur            w0, [x1, #0xb]
    // 0xa4f2e0: r0 = Column()
    //     0xa4f2e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa4f2e4: mov             x1, x0
    // 0xa4f2e8: r0 = Instance_Axis
    //     0xa4f2e8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa4f2ec: stur            x1, [fp, #-0x28]
    // 0xa4f2f0: StoreField: r1->field_f = r0
    //     0xa4f2f0: stur            w0, [x1, #0xf]
    // 0xa4f2f4: r0 = Instance_MainAxisAlignment
    //     0xa4f2f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa4f2f8: ldr             x0, [x0, #0xa08]
    // 0xa4f2fc: StoreField: r1->field_13 = r0
    //     0xa4f2fc: stur            w0, [x1, #0x13]
    // 0xa4f300: r0 = Instance_MainAxisSize
    //     0xa4f300: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xa4f304: ldr             x0, [x0, #0xdd0]
    // 0xa4f308: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4f308: stur            w0, [x1, #0x17]
    // 0xa4f30c: r0 = Instance_CrossAxisAlignment
    //     0xa4f30c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa4f310: ldr             x0, [x0, #0x890]
    // 0xa4f314: StoreField: r1->field_1b = r0
    //     0xa4f314: stur            w0, [x1, #0x1b]
    // 0xa4f318: r0 = Instance_VerticalDirection
    //     0xa4f318: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa4f31c: ldr             x0, [x0, #0xa20]
    // 0xa4f320: StoreField: r1->field_23 = r0
    //     0xa4f320: stur            w0, [x1, #0x23]
    // 0xa4f324: r0 = Instance_Clip
    //     0xa4f324: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4f328: ldr             x0, [x0, #0x38]
    // 0xa4f32c: StoreField: r1->field_2b = r0
    //     0xa4f32c: stur            w0, [x1, #0x2b]
    // 0xa4f330: StoreField: r1->field_2f = rZR
    //     0xa4f330: stur            xzr, [x1, #0x2f]
    // 0xa4f334: ldur            x0, [fp, #-8]
    // 0xa4f338: StoreField: r1->field_b = r0
    //     0xa4f338: stur            w0, [x1, #0xb]
    // 0xa4f33c: r0 = Card()
    //     0xa4f33c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xa4f340: mov             x3, x0
    // 0xa4f344: r0 = Instance_Color
    //     0xa4f344: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa4f348: stur            x3, [fp, #-8]
    // 0xa4f34c: StoreField: r3->field_b = r0
    //     0xa4f34c: stur            w0, [x3, #0xb]
    // 0xa4f350: r0 = 0.000000
    //     0xa4f350: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa4f354: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4f354: stur            w0, [x3, #0x17]
    // 0xa4f358: ldur            x0, [fp, #-0x18]
    // 0xa4f35c: StoreField: r3->field_1b = r0
    //     0xa4f35c: stur            w0, [x3, #0x1b]
    // 0xa4f360: r0 = true
    //     0xa4f360: add             x0, NULL, #0x20  ; true
    // 0xa4f364: StoreField: r3->field_1f = r0
    //     0xa4f364: stur            w0, [x3, #0x1f]
    // 0xa4f368: ldur            x1, [fp, #-0x28]
    // 0xa4f36c: StoreField: r3->field_2f = r1
    //     0xa4f36c: stur            w1, [x3, #0x2f]
    // 0xa4f370: StoreField: r3->field_2b = r0
    //     0xa4f370: stur            w0, [x3, #0x2b]
    // 0xa4f374: r1 = Instance__CardVariant
    //     0xa4f374: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xa4f378: ldr             x1, [x1, #0xa68]
    // 0xa4f37c: StoreField: r3->field_33 = r1
    //     0xa4f37c: stur            w1, [x3, #0x33]
    // 0xa4f380: ldur            x2, [fp, #-0x10]
    // 0xa4f384: r1 = Function '<anonymous closure>':.
    //     0xa4f384: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aac0] AnonymousClosure: (0xa4f714), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4f388: ldr             x1, [x1, #0xac0]
    // 0xa4f38c: r0 = AllocateClosure()
    //     0xa4f38c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4f390: stur            x0, [fp, #-0x18]
    // 0xa4f394: r0 = VisibilityDetector()
    //     0xa4f394: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xa4f398: mov             x1, x0
    // 0xa4f39c: ldur            x0, [fp, #-0x18]
    // 0xa4f3a0: stur            x1, [fp, #-0x28]
    // 0xa4f3a4: StoreField: r1->field_f = r0
    //     0xa4f3a4: stur            w0, [x1, #0xf]
    // 0xa4f3a8: ldur            x0, [fp, #-8]
    // 0xa4f3ac: StoreField: r1->field_b = r0
    //     0xa4f3ac: stur            w0, [x1, #0xb]
    // 0xa4f3b0: ldur            x0, [fp, #-0x20]
    // 0xa4f3b4: StoreField: r1->field_7 = r0
    //     0xa4f3b4: stur            w0, [x1, #7]
    // 0xa4f3b8: r0 = InkWell()
    //     0xa4f3b8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa4f3bc: mov             x3, x0
    // 0xa4f3c0: ldur            x0, [fp, #-0x28]
    // 0xa4f3c4: stur            x3, [fp, #-8]
    // 0xa4f3c8: StoreField: r3->field_b = r0
    //     0xa4f3c8: stur            w0, [x3, #0xb]
    // 0xa4f3cc: ldur            x2, [fp, #-0x10]
    // 0xa4f3d0: r1 = Function '<anonymous closure>':.
    //     0xa4f3d0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aac8] AnonymousClosure: (0xa4f4b8), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xa4b904)
    //     0xa4f3d4: ldr             x1, [x1, #0xac8]
    // 0xa4f3d8: r0 = AllocateClosure()
    //     0xa4f3d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4f3dc: mov             x1, x0
    // 0xa4f3e0: ldur            x0, [fp, #-8]
    // 0xa4f3e4: StoreField: r0->field_f = r1
    //     0xa4f3e4: stur            w1, [x0, #0xf]
    // 0xa4f3e8: r1 = true
    //     0xa4f3e8: add             x1, NULL, #0x20  ; true
    // 0xa4f3ec: StoreField: r0->field_43 = r1
    //     0xa4f3ec: stur            w1, [x0, #0x43]
    // 0xa4f3f0: r2 = Instance_BoxShape
    //     0xa4f3f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa4f3f4: ldr             x2, [x2, #0x80]
    // 0xa4f3f8: StoreField: r0->field_47 = r2
    //     0xa4f3f8: stur            w2, [x0, #0x47]
    // 0xa4f3fc: StoreField: r0->field_6f = r1
    //     0xa4f3fc: stur            w1, [x0, #0x6f]
    // 0xa4f400: r2 = false
    //     0xa4f400: add             x2, NULL, #0x30  ; false
    // 0xa4f404: StoreField: r0->field_73 = r2
    //     0xa4f404: stur            w2, [x0, #0x73]
    // 0xa4f408: StoreField: r0->field_83 = r1
    //     0xa4f408: stur            w1, [x0, #0x83]
    // 0xa4f40c: StoreField: r0->field_7b = r2
    //     0xa4f40c: stur            w2, [x0, #0x7b]
    // 0xa4f410: LeaveFrame
    //     0xa4f410: mov             SP, fp
    //     0xa4f414: ldp             fp, lr, [SP], #0x10
    // 0xa4f418: ret
    //     0xa4f418: ret             
    // 0xa4f41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f41c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f420: b               #0xa4d664
    // 0xa4f424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f428: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f42c: r9 = _pageController
    //     0xa4f42c: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5aad0] Field <_ProductGridItemViewState@**********._pageController@**********>: late (offset: 0x1c)
    //     0xa4f430: ldr             x9, [x9, #0xad0]
    // 0xa4f434: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4f434: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4f438: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4f438: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4f43c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f43c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f440: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f440: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f444: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f444: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f448: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f448: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f44c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f44c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f450: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f454: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f458: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f45c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f45c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f460: SaveReg d0
    //     0xa4f460: str             q0, [SP, #-0x10]!
    // 0xa4f464: r0 = 74
    //     0xa4f464: movz            x0, #0x4a
    // 0xa4f468: r30 = DoubleToIntegerStub
    //     0xa4f468: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xa4f46c: LoadField: r30 = r30->field_7
    //     0xa4f46c: ldur            lr, [lr, #7]
    // 0xa4f470: blr             lr
    // 0xa4f474: RestoreReg d0
    //     0xa4f474: ldr             q0, [SP], #0x10
    // 0xa4f478: b               #0xa4e04c
    // 0xa4f47c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f47c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f480: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f484: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f488: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f48c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f48c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f490: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f494: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f498: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f498: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f49c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f49c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f4a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f4a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f4a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f4a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f4a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f4a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4f4b8, size: 0x25c
    // 0xa4f4b8: EnterFrame
    //     0xa4f4b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4f4bc: mov             fp, SP
    // 0xa4f4c0: AllocStack(0x98)
    //     0xa4f4c0: sub             SP, SP, #0x98
    // 0xa4f4c4: SetupParameters()
    //     0xa4f4c4: ldr             x0, [fp, #0x10]
    //     0xa4f4c8: ldur            w1, [x0, #0x17]
    //     0xa4f4cc: add             x1, x1, HEAP, lsl #32
    //     0xa4f4d0: stur            x1, [fp, #-0x48]
    // 0xa4f4d4: CheckStackOverflow
    //     0xa4f4d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4f4d8: cmp             SP, x16
    //     0xa4f4dc: b.ls            #0xa4f704
    // 0xa4f4e0: LoadField: r2 = r1->field_b
    //     0xa4f4e0: ldur            w2, [x1, #0xb]
    // 0xa4f4e4: DecompressPointer r2
    //     0xa4f4e4: add             x2, x2, HEAP, lsl #32
    // 0xa4f4e8: stur            x2, [fp, #-0x40]
    // 0xa4f4ec: LoadField: r0 = r2->field_f
    //     0xa4f4ec: ldur            w0, [x2, #0xf]
    // 0xa4f4f0: DecompressPointer r0
    //     0xa4f4f0: add             x0, x0, HEAP, lsl #32
    // 0xa4f4f4: LoadField: r3 = r0->field_b
    //     0xa4f4f4: ldur            w3, [x0, #0xb]
    // 0xa4f4f8: DecompressPointer r3
    //     0xa4f4f8: add             x3, x3, HEAP, lsl #32
    // 0xa4f4fc: stur            x3, [fp, #-0x38]
    // 0xa4f500: cmp             w3, NULL
    // 0xa4f504: b.eq            #0xa4f70c
    // 0xa4f508: LoadField: r0 = r3->field_2f
    //     0xa4f508: ldur            w0, [x3, #0x2f]
    // 0xa4f50c: DecompressPointer r0
    //     0xa4f50c: add             x0, x0, HEAP, lsl #32
    // 0xa4f510: cmp             w0, NULL
    // 0xa4f514: b.ne            #0xa4f520
    // 0xa4f518: r4 = ""
    //     0xa4f518: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f51c: b               #0xa4f524
    // 0xa4f520: mov             x4, x0
    // 0xa4f524: stur            x4, [fp, #-0x30]
    // 0xa4f528: LoadField: r0 = r3->field_2b
    //     0xa4f528: ldur            w0, [x3, #0x2b]
    // 0xa4f52c: DecompressPointer r0
    //     0xa4f52c: add             x0, x0, HEAP, lsl #32
    // 0xa4f530: cmp             w0, NULL
    // 0xa4f534: b.ne            #0xa4f540
    // 0xa4f538: r5 = ""
    //     0xa4f538: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f53c: b               #0xa4f544
    // 0xa4f540: mov             x5, x0
    // 0xa4f544: stur            x5, [fp, #-0x28]
    // 0xa4f548: LoadField: r0 = r3->field_37
    //     0xa4f548: ldur            w0, [x3, #0x37]
    // 0xa4f54c: DecompressPointer r0
    //     0xa4f54c: add             x0, x0, HEAP, lsl #32
    // 0xa4f550: cmp             w0, NULL
    // 0xa4f554: b.ne            #0xa4f560
    // 0xa4f558: r6 = ""
    //     0xa4f558: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f55c: b               #0xa4f564
    // 0xa4f560: mov             x6, x0
    // 0xa4f564: stur            x6, [fp, #-0x20]
    // 0xa4f568: LoadField: r7 = r3->field_3f
    //     0xa4f568: ldur            w7, [x3, #0x3f]
    // 0xa4f56c: DecompressPointer r7
    //     0xa4f56c: add             x7, x7, HEAP, lsl #32
    // 0xa4f570: stur            x7, [fp, #-0x18]
    // 0xa4f574: LoadField: r0 = r3->field_33
    //     0xa4f574: ldur            w0, [x3, #0x33]
    // 0xa4f578: DecompressPointer r0
    //     0xa4f578: add             x0, x0, HEAP, lsl #32
    // 0xa4f57c: cmp             w0, NULL
    // 0xa4f580: b.ne            #0xa4f58c
    // 0xa4f584: r8 = ""
    //     0xa4f584: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f588: b               #0xa4f590
    // 0xa4f58c: mov             x8, x0
    // 0xa4f590: stur            x8, [fp, #-0x10]
    // 0xa4f594: LoadField: r0 = r3->field_f
    //     0xa4f594: ldur            w0, [x3, #0xf]
    // 0xa4f598: DecompressPointer r0
    //     0xa4f598: add             x0, x0, HEAP, lsl #32
    // 0xa4f59c: cmp             w0, NULL
    // 0xa4f5a0: b.ne            #0xa4f5ac
    // 0xa4f5a4: r9 = ""
    //     0xa4f5a4: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f5a8: b               #0xa4f5b0
    // 0xa4f5ac: mov             x9, x0
    // 0xa4f5b0: stur            x9, [fp, #-8]
    // 0xa4f5b4: LoadField: r0 = r3->field_b
    //     0xa4f5b4: ldur            w0, [x3, #0xb]
    // 0xa4f5b8: DecompressPointer r0
    //     0xa4f5b8: add             x0, x0, HEAP, lsl #32
    // 0xa4f5bc: LoadField: r10 = r1->field_f
    //     0xa4f5bc: ldur            w10, [x1, #0xf]
    // 0xa4f5c0: DecompressPointer r10
    //     0xa4f5c0: add             x10, x10, HEAP, lsl #32
    // 0xa4f5c4: r11 = LoadClassIdInstr(r0)
    //     0xa4f5c4: ldur            x11, [x0, #-1]
    //     0xa4f5c8: ubfx            x11, x11, #0xc, #0x14
    // 0xa4f5cc: stp             x10, x0, [SP]
    // 0xa4f5d0: mov             x0, x11
    // 0xa4f5d4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f5d4: sub             lr, x0, #0xb7
    //     0xa4f5d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f5dc: blr             lr
    // 0xa4f5e0: cmp             w0, NULL
    // 0xa4f5e4: b.ne            #0xa4f5f0
    // 0xa4f5e8: r0 = Null
    //     0xa4f5e8: mov             x0, NULL
    // 0xa4f5ec: b               #0xa4f5fc
    // 0xa4f5f0: LoadField: r1 = r0->field_2b
    //     0xa4f5f0: ldur            w1, [x0, #0x2b]
    // 0xa4f5f4: DecompressPointer r1
    //     0xa4f5f4: add             x1, x1, HEAP, lsl #32
    // 0xa4f5f8: mov             x0, x1
    // 0xa4f5fc: cmp             w0, NULL
    // 0xa4f600: b.ne            #0xa4f60c
    // 0xa4f604: r2 = ""
    //     0xa4f604: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f608: b               #0xa4f610
    // 0xa4f60c: mov             x2, x0
    // 0xa4f610: ldur            x0, [fp, #-0x48]
    // 0xa4f614: ldur            x1, [fp, #-0x40]
    // 0xa4f618: stur            x2, [fp, #-0x50]
    // 0xa4f61c: LoadField: r3 = r1->field_f
    //     0xa4f61c: ldur            w3, [x1, #0xf]
    // 0xa4f620: DecompressPointer r3
    //     0xa4f620: add             x3, x3, HEAP, lsl #32
    // 0xa4f624: LoadField: r1 = r3->field_b
    //     0xa4f624: ldur            w1, [x3, #0xb]
    // 0xa4f628: DecompressPointer r1
    //     0xa4f628: add             x1, x1, HEAP, lsl #32
    // 0xa4f62c: cmp             w1, NULL
    // 0xa4f630: b.eq            #0xa4f710
    // 0xa4f634: LoadField: r3 = r1->field_b
    //     0xa4f634: ldur            w3, [x1, #0xb]
    // 0xa4f638: DecompressPointer r3
    //     0xa4f638: add             x3, x3, HEAP, lsl #32
    // 0xa4f63c: LoadField: r1 = r0->field_f
    //     0xa4f63c: ldur            w1, [x0, #0xf]
    // 0xa4f640: DecompressPointer r1
    //     0xa4f640: add             x1, x1, HEAP, lsl #32
    // 0xa4f644: r0 = LoadClassIdInstr(r3)
    //     0xa4f644: ldur            x0, [x3, #-1]
    //     0xa4f648: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f64c: stp             x1, x3, [SP]
    // 0xa4f650: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f650: sub             lr, x0, #0xb7
    //     0xa4f654: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f658: blr             lr
    // 0xa4f65c: cmp             w0, NULL
    // 0xa4f660: b.ne            #0xa4f66c
    // 0xa4f664: r0 = Null
    //     0xa4f664: mov             x0, NULL
    // 0xa4f668: b               #0xa4f68c
    // 0xa4f66c: LoadField: r1 = r0->field_3b
    //     0xa4f66c: ldur            w1, [x0, #0x3b]
    // 0xa4f670: DecompressPointer r1
    //     0xa4f670: add             x1, x1, HEAP, lsl #32
    // 0xa4f674: cmp             w1, NULL
    // 0xa4f678: b.ne            #0xa4f684
    // 0xa4f67c: r0 = Null
    //     0xa4f67c: mov             x0, NULL
    // 0xa4f680: b               #0xa4f68c
    // 0xa4f684: LoadField: r0 = r1->field_b
    //     0xa4f684: ldur            w0, [x1, #0xb]
    // 0xa4f688: DecompressPointer r0
    //     0xa4f688: add             x0, x0, HEAP, lsl #32
    // 0xa4f68c: cmp             w0, NULL
    // 0xa4f690: b.ne            #0xa4f69c
    // 0xa4f694: r1 = ""
    //     0xa4f694: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4f698: b               #0xa4f6a0
    // 0xa4f69c: mov             x1, x0
    // 0xa4f6a0: ldur            x0, [fp, #-0x38]
    // 0xa4f6a4: LoadField: r2 = r0->field_57
    //     0xa4f6a4: ldur            w2, [x0, #0x57]
    // 0xa4f6a8: DecompressPointer r2
    //     0xa4f6a8: add             x2, x2, HEAP, lsl #32
    // 0xa4f6ac: ldur            x16, [fp, #-0x30]
    // 0xa4f6b0: stp             x16, x2, [SP, #0x38]
    // 0xa4f6b4: ldur            x16, [fp, #-0x28]
    // 0xa4f6b8: ldur            lr, [fp, #-0x20]
    // 0xa4f6bc: stp             lr, x16, [SP, #0x28]
    // 0xa4f6c0: ldur            x16, [fp, #-0x18]
    // 0xa4f6c4: ldur            lr, [fp, #-0x10]
    // 0xa4f6c8: stp             lr, x16, [SP, #0x18]
    // 0xa4f6cc: ldur            x16, [fp, #-8]
    // 0xa4f6d0: ldur            lr, [fp, #-0x50]
    // 0xa4f6d4: stp             lr, x16, [SP, #8]
    // 0xa4f6d8: str             x1, [SP]
    // 0xa4f6dc: r4 = 0
    //     0xa4f6dc: movz            x4, #0
    // 0xa4f6e0: ldr             x0, [SP, #0x40]
    // 0xa4f6e4: r16 = UnlinkedCall_0x613b5c
    //     0xa4f6e4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5aad8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4f6e8: add             x16, x16, #0xad8
    // 0xa4f6ec: ldp             x5, lr, [x16]
    // 0xa4f6f0: blr             lr
    // 0xa4f6f4: r0 = Null
    //     0xa4f6f4: mov             x0, NULL
    // 0xa4f6f8: LeaveFrame
    //     0xa4f6f8: mov             SP, fp
    //     0xa4f6fc: ldp             fp, lr, [SP], #0x10
    // 0xa4f700: ret
    //     0xa4f700: ret             
    // 0xa4f704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f704: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f708: b               #0xa4f4e0
    // 0xa4f70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f70c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xa4f714, size: 0x2fc
    // 0xa4f714: EnterFrame
    //     0xa4f714: stp             fp, lr, [SP, #-0x10]!
    //     0xa4f718: mov             fp, SP
    // 0xa4f71c: AllocStack(0x68)
    //     0xa4f71c: sub             SP, SP, #0x68
    // 0xa4f720: SetupParameters()
    //     0xa4f720: ldr             x0, [fp, #0x18]
    //     0xa4f724: ldur            w2, [x0, #0x17]
    //     0xa4f728: add             x2, x2, HEAP, lsl #32
    //     0xa4f72c: stur            x2, [fp, #-8]
    // 0xa4f730: CheckStackOverflow
    //     0xa4f730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4f734: cmp             SP, x16
    //     0xa4f738: b.ls            #0xa4f9f4
    // 0xa4f73c: ldr             x1, [fp, #0x10]
    // 0xa4f740: r0 = visibleFraction()
    //     0xa4f740: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xa4f744: mov             v1.16b, v0.16b
    // 0xa4f748: d0 = 0.500000
    //     0xa4f748: fmov            d0, #0.50000000
    // 0xa4f74c: fcmp            d1, d0
    // 0xa4f750: b.le            #0xa4f9e4
    // 0xa4f754: ldur            x1, [fp, #-8]
    // 0xa4f758: LoadField: r2 = r1->field_b
    //     0xa4f758: ldur            w2, [x1, #0xb]
    // 0xa4f75c: DecompressPointer r2
    //     0xa4f75c: add             x2, x2, HEAP, lsl #32
    // 0xa4f760: stur            x2, [fp, #-0x18]
    // 0xa4f764: LoadField: r0 = r2->field_f
    //     0xa4f764: ldur            w0, [x2, #0xf]
    // 0xa4f768: DecompressPointer r0
    //     0xa4f768: add             x0, x0, HEAP, lsl #32
    // 0xa4f76c: LoadField: r3 = r0->field_b
    //     0xa4f76c: ldur            w3, [x0, #0xb]
    // 0xa4f770: DecompressPointer r3
    //     0xa4f770: add             x3, x3, HEAP, lsl #32
    // 0xa4f774: stur            x3, [fp, #-0x10]
    // 0xa4f778: cmp             w3, NULL
    // 0xa4f77c: b.eq            #0xa4f9fc
    // 0xa4f780: LoadField: r0 = r3->field_b
    //     0xa4f780: ldur            w0, [x3, #0xb]
    // 0xa4f784: DecompressPointer r0
    //     0xa4f784: add             x0, x0, HEAP, lsl #32
    // 0xa4f788: LoadField: r4 = r1->field_f
    //     0xa4f788: ldur            w4, [x1, #0xf]
    // 0xa4f78c: DecompressPointer r4
    //     0xa4f78c: add             x4, x4, HEAP, lsl #32
    // 0xa4f790: r5 = LoadClassIdInstr(r0)
    //     0xa4f790: ldur            x5, [x0, #-1]
    //     0xa4f794: ubfx            x5, x5, #0xc, #0x14
    // 0xa4f798: stp             x4, x0, [SP]
    // 0xa4f79c: mov             x0, x5
    // 0xa4f7a0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f7a0: sub             lr, x0, #0xb7
    //     0xa4f7a4: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f7a8: blr             lr
    // 0xa4f7ac: cmp             w0, NULL
    // 0xa4f7b0: b.ne            #0xa4f7bc
    // 0xa4f7b4: r3 = Null
    //     0xa4f7b4: mov             x3, NULL
    // 0xa4f7b8: b               #0xa4f7c8
    // 0xa4f7bc: LoadField: r1 = r0->field_53
    //     0xa4f7bc: ldur            w1, [x0, #0x53]
    // 0xa4f7c0: DecompressPointer r1
    //     0xa4f7c0: add             x1, x1, HEAP, lsl #32
    // 0xa4f7c4: mov             x3, x1
    // 0xa4f7c8: ldur            x1, [fp, #-8]
    // 0xa4f7cc: ldur            x2, [fp, #-0x18]
    // 0xa4f7d0: stur            x3, [fp, #-0x20]
    // 0xa4f7d4: LoadField: r0 = r2->field_f
    //     0xa4f7d4: ldur            w0, [x2, #0xf]
    // 0xa4f7d8: DecompressPointer r0
    //     0xa4f7d8: add             x0, x0, HEAP, lsl #32
    // 0xa4f7dc: LoadField: r4 = r0->field_b
    //     0xa4f7dc: ldur            w4, [x0, #0xb]
    // 0xa4f7e0: DecompressPointer r4
    //     0xa4f7e0: add             x4, x4, HEAP, lsl #32
    // 0xa4f7e4: cmp             w4, NULL
    // 0xa4f7e8: b.eq            #0xa4fa00
    // 0xa4f7ec: LoadField: r0 = r4->field_b
    //     0xa4f7ec: ldur            w0, [x4, #0xb]
    // 0xa4f7f0: DecompressPointer r0
    //     0xa4f7f0: add             x0, x0, HEAP, lsl #32
    // 0xa4f7f4: LoadField: r4 = r1->field_f
    //     0xa4f7f4: ldur            w4, [x1, #0xf]
    // 0xa4f7f8: DecompressPointer r4
    //     0xa4f7f8: add             x4, x4, HEAP, lsl #32
    // 0xa4f7fc: r5 = LoadClassIdInstr(r0)
    //     0xa4f7fc: ldur            x5, [x0, #-1]
    //     0xa4f800: ubfx            x5, x5, #0xc, #0x14
    // 0xa4f804: stp             x4, x0, [SP]
    // 0xa4f808: mov             x0, x5
    // 0xa4f80c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f80c: sub             lr, x0, #0xb7
    //     0xa4f810: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f814: blr             lr
    // 0xa4f818: cmp             w0, NULL
    // 0xa4f81c: b.ne            #0xa4f828
    // 0xa4f820: r3 = Null
    //     0xa4f820: mov             x3, NULL
    // 0xa4f824: b               #0xa4f84c
    // 0xa4f828: LoadField: r1 = r0->field_3b
    //     0xa4f828: ldur            w1, [x0, #0x3b]
    // 0xa4f82c: DecompressPointer r1
    //     0xa4f82c: add             x1, x1, HEAP, lsl #32
    // 0xa4f830: cmp             w1, NULL
    // 0xa4f834: b.ne            #0xa4f840
    // 0xa4f838: r0 = Null
    //     0xa4f838: mov             x0, NULL
    // 0xa4f83c: b               #0xa4f848
    // 0xa4f840: LoadField: r0 = r1->field_b
    //     0xa4f840: ldur            w0, [x1, #0xb]
    // 0xa4f844: DecompressPointer r0
    //     0xa4f844: add             x0, x0, HEAP, lsl #32
    // 0xa4f848: mov             x3, x0
    // 0xa4f84c: ldur            x1, [fp, #-8]
    // 0xa4f850: ldur            x2, [fp, #-0x18]
    // 0xa4f854: stur            x3, [fp, #-0x28]
    // 0xa4f858: LoadField: r0 = r2->field_f
    //     0xa4f858: ldur            w0, [x2, #0xf]
    // 0xa4f85c: DecompressPointer r0
    //     0xa4f85c: add             x0, x0, HEAP, lsl #32
    // 0xa4f860: LoadField: r4 = r0->field_b
    //     0xa4f860: ldur            w4, [x0, #0xb]
    // 0xa4f864: DecompressPointer r4
    //     0xa4f864: add             x4, x4, HEAP, lsl #32
    // 0xa4f868: cmp             w4, NULL
    // 0xa4f86c: b.eq            #0xa4fa04
    // 0xa4f870: LoadField: r0 = r4->field_b
    //     0xa4f870: ldur            w0, [x4, #0xb]
    // 0xa4f874: DecompressPointer r0
    //     0xa4f874: add             x0, x0, HEAP, lsl #32
    // 0xa4f878: LoadField: r4 = r1->field_f
    //     0xa4f878: ldur            w4, [x1, #0xf]
    // 0xa4f87c: DecompressPointer r4
    //     0xa4f87c: add             x4, x4, HEAP, lsl #32
    // 0xa4f880: r5 = LoadClassIdInstr(r0)
    //     0xa4f880: ldur            x5, [x0, #-1]
    //     0xa4f884: ubfx            x5, x5, #0xc, #0x14
    // 0xa4f888: stp             x4, x0, [SP]
    // 0xa4f88c: mov             x0, x5
    // 0xa4f890: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f890: sub             lr, x0, #0xb7
    //     0xa4f894: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f898: blr             lr
    // 0xa4f89c: cmp             w0, NULL
    // 0xa4f8a0: b.ne            #0xa4f8ac
    // 0xa4f8a4: r3 = Null
    //     0xa4f8a4: mov             x3, NULL
    // 0xa4f8a8: b               #0xa4f8b8
    // 0xa4f8ac: LoadField: r1 = r0->field_2b
    //     0xa4f8ac: ldur            w1, [x0, #0x2b]
    // 0xa4f8b0: DecompressPointer r1
    //     0xa4f8b0: add             x1, x1, HEAP, lsl #32
    // 0xa4f8b4: mov             x3, x1
    // 0xa4f8b8: ldur            x1, [fp, #-8]
    // 0xa4f8bc: ldur            x2, [fp, #-0x18]
    // 0xa4f8c0: stur            x3, [fp, #-0x30]
    // 0xa4f8c4: LoadField: r0 = r2->field_f
    //     0xa4f8c4: ldur            w0, [x2, #0xf]
    // 0xa4f8c8: DecompressPointer r0
    //     0xa4f8c8: add             x0, x0, HEAP, lsl #32
    // 0xa4f8cc: LoadField: r4 = r0->field_b
    //     0xa4f8cc: ldur            w4, [x0, #0xb]
    // 0xa4f8d0: DecompressPointer r4
    //     0xa4f8d0: add             x4, x4, HEAP, lsl #32
    // 0xa4f8d4: cmp             w4, NULL
    // 0xa4f8d8: b.eq            #0xa4fa08
    // 0xa4f8dc: LoadField: r0 = r4->field_b
    //     0xa4f8dc: ldur            w0, [x4, #0xb]
    // 0xa4f8e0: DecompressPointer r0
    //     0xa4f8e0: add             x0, x0, HEAP, lsl #32
    // 0xa4f8e4: LoadField: r4 = r1->field_f
    //     0xa4f8e4: ldur            w4, [x1, #0xf]
    // 0xa4f8e8: DecompressPointer r4
    //     0xa4f8e8: add             x4, x4, HEAP, lsl #32
    // 0xa4f8ec: r5 = LoadClassIdInstr(r0)
    //     0xa4f8ec: ldur            x5, [x0, #-1]
    //     0xa4f8f0: ubfx            x5, x5, #0xc, #0x14
    // 0xa4f8f4: stp             x4, x0, [SP]
    // 0xa4f8f8: mov             x0, x5
    // 0xa4f8fc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f8fc: sub             lr, x0, #0xb7
    //     0xa4f900: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f904: blr             lr
    // 0xa4f908: cmp             w0, NULL
    // 0xa4f90c: b.ne            #0xa4f918
    // 0xa4f910: r2 = Null
    //     0xa4f910: mov             x2, NULL
    // 0xa4f914: b               #0xa4f924
    // 0xa4f918: LoadField: r1 = r0->field_57
    //     0xa4f918: ldur            w1, [x0, #0x57]
    // 0xa4f91c: DecompressPointer r1
    //     0xa4f91c: add             x1, x1, HEAP, lsl #32
    // 0xa4f920: mov             x2, x1
    // 0xa4f924: ldur            x0, [fp, #-8]
    // 0xa4f928: ldur            x1, [fp, #-0x18]
    // 0xa4f92c: stur            x2, [fp, #-0x38]
    // 0xa4f930: LoadField: r3 = r1->field_f
    //     0xa4f930: ldur            w3, [x1, #0xf]
    // 0xa4f934: DecompressPointer r3
    //     0xa4f934: add             x3, x3, HEAP, lsl #32
    // 0xa4f938: LoadField: r1 = r3->field_b
    //     0xa4f938: ldur            w1, [x3, #0xb]
    // 0xa4f93c: DecompressPointer r1
    //     0xa4f93c: add             x1, x1, HEAP, lsl #32
    // 0xa4f940: cmp             w1, NULL
    // 0xa4f944: b.eq            #0xa4fa0c
    // 0xa4f948: LoadField: r3 = r1->field_b
    //     0xa4f948: ldur            w3, [x1, #0xb]
    // 0xa4f94c: DecompressPointer r3
    //     0xa4f94c: add             x3, x3, HEAP, lsl #32
    // 0xa4f950: LoadField: r1 = r0->field_f
    //     0xa4f950: ldur            w1, [x0, #0xf]
    // 0xa4f954: DecompressPointer r1
    //     0xa4f954: add             x1, x1, HEAP, lsl #32
    // 0xa4f958: r0 = LoadClassIdInstr(r3)
    //     0xa4f958: ldur            x0, [x3, #-1]
    //     0xa4f95c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f960: stp             x1, x3, [SP]
    // 0xa4f964: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4f964: sub             lr, x0, #0xb7
    //     0xa4f968: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f96c: blr             lr
    // 0xa4f970: cmp             w0, NULL
    // 0xa4f974: b.ne            #0xa4f980
    // 0xa4f978: r0 = Null
    //     0xa4f978: mov             x0, NULL
    // 0xa4f97c: b               #0xa4f98c
    // 0xa4f980: LoadField: r1 = r0->field_7b
    //     0xa4f980: ldur            w1, [x0, #0x7b]
    // 0xa4f984: DecompressPointer r1
    //     0xa4f984: add             x1, x1, HEAP, lsl #32
    // 0xa4f988: mov             x0, x1
    // 0xa4f98c: cmp             w0, NULL
    // 0xa4f990: b.ne            #0xa4f9a0
    // 0xa4f994: r0 = ProductRating()
    //     0xa4f994: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xa4f998: mov             x1, x0
    // 0xa4f99c: b               #0xa4f9a4
    // 0xa4f9a0: mov             x1, x0
    // 0xa4f9a4: ldur            x0, [fp, #-0x10]
    // 0xa4f9a8: LoadField: r2 = r0->field_4f
    //     0xa4f9a8: ldur            w2, [x0, #0x4f]
    // 0xa4f9ac: DecompressPointer r2
    //     0xa4f9ac: add             x2, x2, HEAP, lsl #32
    // 0xa4f9b0: ldur            x16, [fp, #-0x20]
    // 0xa4f9b4: stp             x16, x2, [SP, #0x20]
    // 0xa4f9b8: ldur            x16, [fp, #-0x28]
    // 0xa4f9bc: ldur            lr, [fp, #-0x30]
    // 0xa4f9c0: stp             lr, x16, [SP, #0x10]
    // 0xa4f9c4: ldur            x16, [fp, #-0x38]
    // 0xa4f9c8: stp             x1, x16, [SP]
    // 0xa4f9cc: r4 = 0
    //     0xa4f9cc: movz            x4, #0
    // 0xa4f9d0: ldr             x0, [SP, #0x28]
    // 0xa4f9d4: r16 = UnlinkedCall_0x613b5c
    //     0xa4f9d4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5aae8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4f9d8: add             x16, x16, #0xae8
    // 0xa4f9dc: ldp             x5, lr, [x16]
    // 0xa4f9e0: blr             lr
    // 0xa4f9e4: r0 = Null
    //     0xa4f9e4: mov             x0, NULL
    // 0xa4f9e8: LeaveFrame
    //     0xa4f9e8: mov             SP, fp
    //     0xa4f9ec: ldp             fp, lr, [SP], #0x10
    // 0xa4f9f0: ret
    //     0xa4f9f0: ret             
    // 0xa4f9f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f9f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f9f8: b               #0xa4f73c
    // 0xa4f9fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f9fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fa00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fa04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fa08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fa0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4fb8c, size: 0x3b4
    // 0xa4fb8c: EnterFrame
    //     0xa4fb8c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fb90: mov             fp, SP
    // 0xa4fb94: AllocStack(0x48)
    //     0xa4fb94: sub             SP, SP, #0x48
    // 0xa4fb98: SetupParameters()
    //     0xa4fb98: ldr             x0, [fp, #0x10]
    //     0xa4fb9c: ldur            w1, [x0, #0x17]
    //     0xa4fba0: add             x1, x1, HEAP, lsl #32
    //     0xa4fba4: stur            x1, [fp, #-0x10]
    // 0xa4fba8: CheckStackOverflow
    //     0xa4fba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fbac: cmp             SP, x16
    //     0xa4fbb0: b.ls            #0xa4ff20
    // 0xa4fbb4: LoadField: r2 = r1->field_b
    //     0xa4fbb4: ldur            w2, [x1, #0xb]
    // 0xa4fbb8: DecompressPointer r2
    //     0xa4fbb8: add             x2, x2, HEAP, lsl #32
    // 0xa4fbbc: stur            x2, [fp, #-8]
    // 0xa4fbc0: LoadField: r0 = r2->field_f
    //     0xa4fbc0: ldur            w0, [x2, #0xf]
    // 0xa4fbc4: DecompressPointer r0
    //     0xa4fbc4: add             x0, x0, HEAP, lsl #32
    // 0xa4fbc8: LoadField: r3 = r0->field_b
    //     0xa4fbc8: ldur            w3, [x0, #0xb]
    // 0xa4fbcc: DecompressPointer r3
    //     0xa4fbcc: add             x3, x3, HEAP, lsl #32
    // 0xa4fbd0: cmp             w3, NULL
    // 0xa4fbd4: b.eq            #0xa4ff28
    // 0xa4fbd8: LoadField: r0 = r3->field_b
    //     0xa4fbd8: ldur            w0, [x3, #0xb]
    // 0xa4fbdc: DecompressPointer r0
    //     0xa4fbdc: add             x0, x0, HEAP, lsl #32
    // 0xa4fbe0: LoadField: r3 = r1->field_f
    //     0xa4fbe0: ldur            w3, [x1, #0xf]
    // 0xa4fbe4: DecompressPointer r3
    //     0xa4fbe4: add             x3, x3, HEAP, lsl #32
    // 0xa4fbe8: r4 = LoadClassIdInstr(r0)
    //     0xa4fbe8: ldur            x4, [x0, #-1]
    //     0xa4fbec: ubfx            x4, x4, #0xc, #0x14
    // 0xa4fbf0: stp             x3, x0, [SP]
    // 0xa4fbf4: mov             x0, x4
    // 0xa4fbf8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4fbf8: sub             lr, x0, #0xb7
    //     0xa4fbfc: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fc00: blr             lr
    // 0xa4fc04: cmp             w0, NULL
    // 0xa4fc08: b.ne            #0xa4fc14
    // 0xa4fc0c: r0 = Null
    //     0xa4fc0c: mov             x0, NULL
    // 0xa4fc10: b               #0xa4fc20
    // 0xa4fc14: LoadField: r1 = r0->field_4f
    //     0xa4fc14: ldur            w1, [x0, #0x4f]
    // 0xa4fc18: DecompressPointer r1
    //     0xa4fc18: add             x1, x1, HEAP, lsl #32
    // 0xa4fc1c: mov             x0, x1
    // 0xa4fc20: cmp             w0, NULL
    // 0xa4fc24: b.eq            #0xa4fc2c
    // 0xa4fc28: tbz             w0, #4, #0xa4ff10
    // 0xa4fc2c: ldur            x1, [fp, #-0x10]
    // 0xa4fc30: ldur            x2, [fp, #-8]
    // 0xa4fc34: LoadField: r0 = r2->field_f
    //     0xa4fc34: ldur            w0, [x2, #0xf]
    // 0xa4fc38: DecompressPointer r0
    //     0xa4fc38: add             x0, x0, HEAP, lsl #32
    // 0xa4fc3c: LoadField: r3 = r0->field_b
    //     0xa4fc3c: ldur            w3, [x0, #0xb]
    // 0xa4fc40: DecompressPointer r3
    //     0xa4fc40: add             x3, x3, HEAP, lsl #32
    // 0xa4fc44: stur            x3, [fp, #-0x18]
    // 0xa4fc48: cmp             w3, NULL
    // 0xa4fc4c: b.eq            #0xa4ff2c
    // 0xa4fc50: LoadField: r0 = r3->field_b
    //     0xa4fc50: ldur            w0, [x3, #0xb]
    // 0xa4fc54: DecompressPointer r0
    //     0xa4fc54: add             x0, x0, HEAP, lsl #32
    // 0xa4fc58: LoadField: r4 = r1->field_f
    //     0xa4fc58: ldur            w4, [x1, #0xf]
    // 0xa4fc5c: DecompressPointer r4
    //     0xa4fc5c: add             x4, x4, HEAP, lsl #32
    // 0xa4fc60: r5 = LoadClassIdInstr(r0)
    //     0xa4fc60: ldur            x5, [x0, #-1]
    //     0xa4fc64: ubfx            x5, x5, #0xc, #0x14
    // 0xa4fc68: stp             x4, x0, [SP]
    // 0xa4fc6c: mov             x0, x5
    // 0xa4fc70: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4fc70: sub             lr, x0, #0xb7
    //     0xa4fc74: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fc78: blr             lr
    // 0xa4fc7c: cmp             w0, NULL
    // 0xa4fc80: b.ne            #0xa4fc8c
    // 0xa4fc84: r3 = Null
    //     0xa4fc84: mov             x3, NULL
    // 0xa4fc88: b               #0xa4fc98
    // 0xa4fc8c: LoadField: r1 = r0->field_2b
    //     0xa4fc8c: ldur            w1, [x0, #0x2b]
    // 0xa4fc90: DecompressPointer r1
    //     0xa4fc90: add             x1, x1, HEAP, lsl #32
    // 0xa4fc94: mov             x3, x1
    // 0xa4fc98: ldur            x1, [fp, #-0x10]
    // 0xa4fc9c: ldur            x2, [fp, #-8]
    // 0xa4fca0: stur            x3, [fp, #-0x20]
    // 0xa4fca4: LoadField: r0 = r2->field_f
    //     0xa4fca4: ldur            w0, [x2, #0xf]
    // 0xa4fca8: DecompressPointer r0
    //     0xa4fca8: add             x0, x0, HEAP, lsl #32
    // 0xa4fcac: LoadField: r4 = r0->field_b
    //     0xa4fcac: ldur            w4, [x0, #0xb]
    // 0xa4fcb0: DecompressPointer r4
    //     0xa4fcb0: add             x4, x4, HEAP, lsl #32
    // 0xa4fcb4: cmp             w4, NULL
    // 0xa4fcb8: b.eq            #0xa4ff30
    // 0xa4fcbc: LoadField: r0 = r4->field_b
    //     0xa4fcbc: ldur            w0, [x4, #0xb]
    // 0xa4fcc0: DecompressPointer r0
    //     0xa4fcc0: add             x0, x0, HEAP, lsl #32
    // 0xa4fcc4: LoadField: r4 = r1->field_f
    //     0xa4fcc4: ldur            w4, [x1, #0xf]
    // 0xa4fcc8: DecompressPointer r4
    //     0xa4fcc8: add             x4, x4, HEAP, lsl #32
    // 0xa4fccc: r5 = LoadClassIdInstr(r0)
    //     0xa4fccc: ldur            x5, [x0, #-1]
    //     0xa4fcd0: ubfx            x5, x5, #0xc, #0x14
    // 0xa4fcd4: stp             x4, x0, [SP]
    // 0xa4fcd8: mov             x0, x5
    // 0xa4fcdc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4fcdc: sub             lr, x0, #0xb7
    //     0xa4fce0: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fce4: blr             lr
    // 0xa4fce8: cmp             w0, NULL
    // 0xa4fcec: b.ne            #0xa4fcf8
    // 0xa4fcf0: r3 = Null
    //     0xa4fcf0: mov             x3, NULL
    // 0xa4fcf4: b               #0xa4fd1c
    // 0xa4fcf8: LoadField: r1 = r0->field_3b
    //     0xa4fcf8: ldur            w1, [x0, #0x3b]
    // 0xa4fcfc: DecompressPointer r1
    //     0xa4fcfc: add             x1, x1, HEAP, lsl #32
    // 0xa4fd00: cmp             w1, NULL
    // 0xa4fd04: b.ne            #0xa4fd10
    // 0xa4fd08: r0 = Null
    //     0xa4fd08: mov             x0, NULL
    // 0xa4fd0c: b               #0xa4fd18
    // 0xa4fd10: LoadField: r0 = r1->field_7
    //     0xa4fd10: ldur            w0, [x1, #7]
    // 0xa4fd14: DecompressPointer r0
    //     0xa4fd14: add             x0, x0, HEAP, lsl #32
    // 0xa4fd18: mov             x3, x0
    // 0xa4fd1c: ldur            x1, [fp, #-0x10]
    // 0xa4fd20: ldur            x2, [fp, #-8]
    // 0xa4fd24: stur            x3, [fp, #-0x28]
    // 0xa4fd28: LoadField: r0 = r2->field_f
    //     0xa4fd28: ldur            w0, [x2, #0xf]
    // 0xa4fd2c: DecompressPointer r0
    //     0xa4fd2c: add             x0, x0, HEAP, lsl #32
    // 0xa4fd30: LoadField: r4 = r0->field_b
    //     0xa4fd30: ldur            w4, [x0, #0xb]
    // 0xa4fd34: DecompressPointer r4
    //     0xa4fd34: add             x4, x4, HEAP, lsl #32
    // 0xa4fd38: cmp             w4, NULL
    // 0xa4fd3c: b.eq            #0xa4ff34
    // 0xa4fd40: LoadField: r0 = r4->field_b
    //     0xa4fd40: ldur            w0, [x4, #0xb]
    // 0xa4fd44: DecompressPointer r0
    //     0xa4fd44: add             x0, x0, HEAP, lsl #32
    // 0xa4fd48: LoadField: r4 = r1->field_f
    //     0xa4fd48: ldur            w4, [x1, #0xf]
    // 0xa4fd4c: DecompressPointer r4
    //     0xa4fd4c: add             x4, x4, HEAP, lsl #32
    // 0xa4fd50: r5 = LoadClassIdInstr(r0)
    //     0xa4fd50: ldur            x5, [x0, #-1]
    //     0xa4fd54: ubfx            x5, x5, #0xc, #0x14
    // 0xa4fd58: stp             x4, x0, [SP]
    // 0xa4fd5c: mov             x0, x5
    // 0xa4fd60: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4fd60: sub             lr, x0, #0xb7
    //     0xa4fd64: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fd68: blr             lr
    // 0xa4fd6c: cmp             w0, NULL
    // 0xa4fd70: b.ne            #0xa4fd7c
    // 0xa4fd74: r3 = Null
    //     0xa4fd74: mov             x3, NULL
    // 0xa4fd78: b               #0xa4fda0
    // 0xa4fd7c: LoadField: r1 = r0->field_3b
    //     0xa4fd7c: ldur            w1, [x0, #0x3b]
    // 0xa4fd80: DecompressPointer r1
    //     0xa4fd80: add             x1, x1, HEAP, lsl #32
    // 0xa4fd84: cmp             w1, NULL
    // 0xa4fd88: b.ne            #0xa4fd94
    // 0xa4fd8c: r0 = Null
    //     0xa4fd8c: mov             x0, NULL
    // 0xa4fd90: b               #0xa4fd9c
    // 0xa4fd94: LoadField: r0 = r1->field_b
    //     0xa4fd94: ldur            w0, [x1, #0xb]
    // 0xa4fd98: DecompressPointer r0
    //     0xa4fd98: add             x0, x0, HEAP, lsl #32
    // 0xa4fd9c: mov             x3, x0
    // 0xa4fda0: ldur            x0, [fp, #-0x10]
    // 0xa4fda4: ldur            x1, [fp, #-8]
    // 0xa4fda8: ldur            x2, [fp, #-0x18]
    // 0xa4fdac: LoadField: r4 = r2->field_4b
    //     0xa4fdac: ldur            w4, [x2, #0x4b]
    // 0xa4fdb0: DecompressPointer r4
    //     0xa4fdb0: add             x4, x4, HEAP, lsl #32
    // 0xa4fdb4: ldur            x16, [fp, #-0x20]
    // 0xa4fdb8: stp             x16, x4, [SP, #0x10]
    // 0xa4fdbc: ldur            x16, [fp, #-0x28]
    // 0xa4fdc0: stp             x3, x16, [SP]
    // 0xa4fdc4: r4 = 0
    //     0xa4fdc4: movz            x4, #0
    // 0xa4fdc8: ldr             x0, [SP, #0x18]
    // 0xa4fdcc: r16 = UnlinkedCall_0x613b5c
    //     0xa4fdcc: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5aaf8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4fdd0: add             x16, x16, #0xaf8
    // 0xa4fdd4: ldp             x5, lr, [x16]
    // 0xa4fdd8: blr             lr
    // 0xa4fddc: ldur            x1, [fp, #-8]
    // 0xa4fde0: LoadField: r0 = r1->field_f
    //     0xa4fde0: ldur            w0, [x1, #0xf]
    // 0xa4fde4: DecompressPointer r0
    //     0xa4fde4: add             x0, x0, HEAP, lsl #32
    // 0xa4fde8: LoadField: r2 = r0->field_b
    //     0xa4fde8: ldur            w2, [x0, #0xb]
    // 0xa4fdec: DecompressPointer r2
    //     0xa4fdec: add             x2, x2, HEAP, lsl #32
    // 0xa4fdf0: stur            x2, [fp, #-0x18]
    // 0xa4fdf4: cmp             w2, NULL
    // 0xa4fdf8: b.eq            #0xa4ff38
    // 0xa4fdfc: LoadField: r0 = r2->field_b
    //     0xa4fdfc: ldur            w0, [x2, #0xb]
    // 0xa4fe00: DecompressPointer r0
    //     0xa4fe00: add             x0, x0, HEAP, lsl #32
    // 0xa4fe04: ldur            x3, [fp, #-0x10]
    // 0xa4fe08: LoadField: r4 = r3->field_f
    //     0xa4fe08: ldur            w4, [x3, #0xf]
    // 0xa4fe0c: DecompressPointer r4
    //     0xa4fe0c: add             x4, x4, HEAP, lsl #32
    // 0xa4fe10: r5 = LoadClassIdInstr(r0)
    //     0xa4fe10: ldur            x5, [x0, #-1]
    //     0xa4fe14: ubfx            x5, x5, #0xc, #0x14
    // 0xa4fe18: stp             x4, x0, [SP]
    // 0xa4fe1c: mov             x0, x5
    // 0xa4fe20: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4fe20: sub             lr, x0, #0xb7
    //     0xa4fe24: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fe28: blr             lr
    // 0xa4fe2c: cmp             w0, NULL
    // 0xa4fe30: b.ne            #0xa4fe40
    // 0xa4fe34: r0 = Entity()
    //     0xa4fe34: bl              #0x9118a4  ; AllocateEntityStub -> Entity (size=0xc0)
    // 0xa4fe38: mov             x1, x0
    // 0xa4fe3c: b               #0xa4fe44
    // 0xa4fe40: mov             x1, x0
    // 0xa4fe44: ldur            x0, [fp, #-8]
    // 0xa4fe48: stur            x1, [fp, #-0x20]
    // 0xa4fe4c: LoadField: r2 = r0->field_f
    //     0xa4fe4c: ldur            w2, [x0, #0xf]
    // 0xa4fe50: DecompressPointer r2
    //     0xa4fe50: add             x2, x2, HEAP, lsl #32
    // 0xa4fe54: LoadField: r0 = r2->field_b
    //     0xa4fe54: ldur            w0, [x2, #0xb]
    // 0xa4fe58: DecompressPointer r0
    //     0xa4fe58: add             x0, x0, HEAP, lsl #32
    // 0xa4fe5c: cmp             w0, NULL
    // 0xa4fe60: b.eq            #0xa4ff3c
    // 0xa4fe64: LoadField: r2 = r0->field_27
    //     0xa4fe64: ldur            w2, [x0, #0x27]
    // 0xa4fe68: DecompressPointer r2
    //     0xa4fe68: add             x2, x2, HEAP, lsl #32
    // 0xa4fe6c: cmp             w2, NULL
    // 0xa4fe70: b.ne            #0xa4fe7c
    // 0xa4fe74: r3 = ""
    //     0xa4fe74: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4fe78: b               #0xa4fe80
    // 0xa4fe7c: mov             x3, x2
    // 0xa4fe80: ldur            x2, [fp, #-0x10]
    // 0xa4fe84: stur            x3, [fp, #-8]
    // 0xa4fe88: LoadField: r4 = r0->field_b
    //     0xa4fe88: ldur            w4, [x0, #0xb]
    // 0xa4fe8c: DecompressPointer r4
    //     0xa4fe8c: add             x4, x4, HEAP, lsl #32
    // 0xa4fe90: LoadField: r0 = r2->field_f
    //     0xa4fe90: ldur            w0, [x2, #0xf]
    // 0xa4fe94: DecompressPointer r0
    //     0xa4fe94: add             x0, x0, HEAP, lsl #32
    // 0xa4fe98: r2 = LoadClassIdInstr(r4)
    //     0xa4fe98: ldur            x2, [x4, #-1]
    //     0xa4fe9c: ubfx            x2, x2, #0xc, #0x14
    // 0xa4fea0: stp             x0, x4, [SP]
    // 0xa4fea4: mov             x0, x2
    // 0xa4fea8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4fea8: sub             lr, x0, #0xb7
    //     0xa4feac: ldr             lr, [x21, lr, lsl #3]
    //     0xa4feb0: blr             lr
    // 0xa4feb4: cmp             w0, NULL
    // 0xa4feb8: b.ne            #0xa4fec4
    // 0xa4febc: r0 = Null
    //     0xa4febc: mov             x0, NULL
    // 0xa4fec0: b               #0xa4fed0
    // 0xa4fec4: LoadField: r1 = r0->field_b3
    //     0xa4fec4: ldur            w1, [x0, #0xb3]
    // 0xa4fec8: DecompressPointer r1
    //     0xa4fec8: add             x1, x1, HEAP, lsl #32
    // 0xa4fecc: mov             x0, x1
    // 0xa4fed0: cmp             w0, NULL
    // 0xa4fed4: b.ne            #0xa4fee0
    // 0xa4fed8: r1 = ""
    //     0xa4fed8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4fedc: b               #0xa4fee4
    // 0xa4fee0: mov             x1, x0
    // 0xa4fee4: ldur            x0, [fp, #-0x18]
    // 0xa4fee8: LoadField: r2 = r0->field_5b
    //     0xa4fee8: ldur            w2, [x0, #0x5b]
    // 0xa4feec: DecompressPointer r2
    //     0xa4feec: add             x2, x2, HEAP, lsl #32
    // 0xa4fef0: ldur            x16, [fp, #-0x20]
    // 0xa4fef4: stp             x16, x2, [SP, #0x10]
    // 0xa4fef8: ldur            x16, [fp, #-8]
    // 0xa4fefc: stp             x1, x16, [SP]
    // 0xa4ff00: mov             x0, x2
    // 0xa4ff04: ClosureCall
    //     0xa4ff04: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xa4ff08: ldur            x2, [x0, #0x1f]
    //     0xa4ff0c: blr             x2
    // 0xa4ff10: r0 = Null
    //     0xa4ff10: mov             x0, NULL
    // 0xa4ff14: LeaveFrame
    //     0xa4ff14: mov             SP, fp
    //     0xa4ff18: ldp             fp, lr, [SP], #0x10
    // 0xa4ff1c: ret
    //     0xa4ff1c: ret             
    // 0xa4ff20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ff20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ff24: b               #0xa4fbb4
    // 0xa4ff28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ff28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ff2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ff2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ff30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ff30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ff34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ff34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ff38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ff38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ff3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ff3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa4ff40, size: 0x2e0
    // 0xa4ff40: EnterFrame
    //     0xa4ff40: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ff44: mov             fp, SP
    // 0xa4ff48: AllocStack(0x30)
    //     0xa4ff48: sub             SP, SP, #0x30
    // 0xa4ff4c: SetupParameters()
    //     0xa4ff4c: ldr             x0, [fp, #0x20]
    //     0xa4ff50: ldur            w1, [x0, #0x17]
    //     0xa4ff54: add             x1, x1, HEAP, lsl #32
    //     0xa4ff58: stur            x1, [fp, #-0x18]
    // 0xa4ff5c: CheckStackOverflow
    //     0xa4ff5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ff60: cmp             SP, x16
    //     0xa4ff64: b.ls            #0xa50208
    // 0xa4ff68: LoadField: r2 = r1->field_b
    //     0xa4ff68: ldur            w2, [x1, #0xb]
    // 0xa4ff6c: DecompressPointer r2
    //     0xa4ff6c: add             x2, x2, HEAP, lsl #32
    // 0xa4ff70: stur            x2, [fp, #-0x10]
    // 0xa4ff74: LoadField: r3 = r2->field_f
    //     0xa4ff74: ldur            w3, [x2, #0xf]
    // 0xa4ff78: DecompressPointer r3
    //     0xa4ff78: add             x3, x3, HEAP, lsl #32
    // 0xa4ff7c: stur            x3, [fp, #-8]
    // 0xa4ff80: LoadField: r0 = r3->field_b
    //     0xa4ff80: ldur            w0, [x3, #0xb]
    // 0xa4ff84: DecompressPointer r0
    //     0xa4ff84: add             x0, x0, HEAP, lsl #32
    // 0xa4ff88: cmp             w0, NULL
    // 0xa4ff8c: b.eq            #0xa50210
    // 0xa4ff90: LoadField: r4 = r0->field_b
    //     0xa4ff90: ldur            w4, [x0, #0xb]
    // 0xa4ff94: DecompressPointer r4
    //     0xa4ff94: add             x4, x4, HEAP, lsl #32
    // 0xa4ff98: LoadField: r0 = r1->field_f
    //     0xa4ff98: ldur            w0, [x1, #0xf]
    // 0xa4ff9c: DecompressPointer r0
    //     0xa4ff9c: add             x0, x0, HEAP, lsl #32
    // 0xa4ffa0: r5 = LoadClassIdInstr(r4)
    //     0xa4ffa0: ldur            x5, [x4, #-1]
    //     0xa4ffa4: ubfx            x5, x5, #0xc, #0x14
    // 0xa4ffa8: stp             x0, x4, [SP]
    // 0xa4ffac: mov             x0, x5
    // 0xa4ffb0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa4ffb0: sub             lr, x0, #0xb7
    //     0xa4ffb4: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ffb8: blr             lr
    // 0xa4ffbc: cmp             w0, NULL
    // 0xa4ffc0: b.ne            #0xa4ffcc
    // 0xa4ffc4: r3 = Null
    //     0xa4ffc4: mov             x3, NULL
    // 0xa4ffc8: b               #0xa50024
    // 0xa4ffcc: LoadField: r2 = r0->field_37
    //     0xa4ffcc: ldur            w2, [x0, #0x37]
    // 0xa4ffd0: DecompressPointer r2
    //     0xa4ffd0: add             x2, x2, HEAP, lsl #32
    // 0xa4ffd4: cmp             w2, NULL
    // 0xa4ffd8: b.ne            #0xa4ffe4
    // 0xa4ffdc: r0 = Null
    //     0xa4ffdc: mov             x0, NULL
    // 0xa4ffe0: b               #0xa50020
    // 0xa4ffe4: ldr             x0, [fp, #0x10]
    // 0xa4ffe8: LoadField: r1 = r2->field_b
    //     0xa4ffe8: ldur            w1, [x2, #0xb]
    // 0xa4ffec: r3 = LoadInt32Instr(r0)
    //     0xa4ffec: sbfx            x3, x0, #1, #0x1f
    //     0xa4fff0: tbz             w0, #0, #0xa4fff8
    //     0xa4fff4: ldur            x3, [x0, #7]
    // 0xa4fff8: r0 = LoadInt32Instr(r1)
    //     0xa4fff8: sbfx            x0, x1, #1, #0x1f
    // 0xa4fffc: mov             x1, x3
    // 0xa50000: cmp             x1, x0
    // 0xa50004: b.hs            #0xa50214
    // 0xa50008: LoadField: r0 = r2->field_f
    //     0xa50008: ldur            w0, [x2, #0xf]
    // 0xa5000c: DecompressPointer r0
    //     0xa5000c: add             x0, x0, HEAP, lsl #32
    // 0xa50010: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa50010: add             x16, x0, x3, lsl #2
    //     0xa50014: ldur            w1, [x16, #0xf]
    // 0xa50018: DecompressPointer r1
    //     0xa50018: add             x1, x1, HEAP, lsl #32
    // 0xa5001c: mov             x0, x1
    // 0xa50020: mov             x3, x0
    // 0xa50024: ldur            x1, [fp, #-0x18]
    // 0xa50028: ldur            x2, [fp, #-0x10]
    // 0xa5002c: stur            x3, [fp, #-0x20]
    // 0xa50030: LoadField: r0 = r2->field_f
    //     0xa50030: ldur            w0, [x2, #0xf]
    // 0xa50034: DecompressPointer r0
    //     0xa50034: add             x0, x0, HEAP, lsl #32
    // 0xa50038: LoadField: r4 = r0->field_b
    //     0xa50038: ldur            w4, [x0, #0xb]
    // 0xa5003c: DecompressPointer r4
    //     0xa5003c: add             x4, x4, HEAP, lsl #32
    // 0xa50040: cmp             w4, NULL
    // 0xa50044: b.eq            #0xa50218
    // 0xa50048: LoadField: r0 = r4->field_b
    //     0xa50048: ldur            w0, [x4, #0xb]
    // 0xa5004c: DecompressPointer r0
    //     0xa5004c: add             x0, x0, HEAP, lsl #32
    // 0xa50050: LoadField: r4 = r1->field_f
    //     0xa50050: ldur            w4, [x1, #0xf]
    // 0xa50054: DecompressPointer r4
    //     0xa50054: add             x4, x4, HEAP, lsl #32
    // 0xa50058: r5 = LoadClassIdInstr(r0)
    //     0xa50058: ldur            x5, [x0, #-1]
    //     0xa5005c: ubfx            x5, x5, #0xc, #0x14
    // 0xa50060: stp             x4, x0, [SP]
    // 0xa50064: mov             x0, x5
    // 0xa50068: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa50068: sub             lr, x0, #0xb7
    //     0xa5006c: ldr             lr, [x21, lr, lsl #3]
    //     0xa50070: blr             lr
    // 0xa50074: ldur            x1, [fp, #-8]
    // 0xa50078: ldur            x2, [fp, #-0x20]
    // 0xa5007c: mov             x3, x0
    // 0xa50080: r0 = basicThemeSlider()
    //     0xa50080: bl              #0xa50220  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::basicThemeSlider
    // 0xa50084: mov             x1, x0
    // 0xa50088: ldur            x0, [fp, #-0x10]
    // 0xa5008c: stur            x1, [fp, #-8]
    // 0xa50090: LoadField: r2 = r0->field_f
    //     0xa50090: ldur            w2, [x0, #0xf]
    // 0xa50094: DecompressPointer r2
    //     0xa50094: add             x2, x2, HEAP, lsl #32
    // 0xa50098: LoadField: r0 = r2->field_b
    //     0xa50098: ldur            w0, [x2, #0xb]
    // 0xa5009c: DecompressPointer r0
    //     0xa5009c: add             x0, x0, HEAP, lsl #32
    // 0xa500a0: cmp             w0, NULL
    // 0xa500a4: b.eq            #0xa5021c
    // 0xa500a8: LoadField: r2 = r0->field_b
    //     0xa500a8: ldur            w2, [x0, #0xb]
    // 0xa500ac: DecompressPointer r2
    //     0xa500ac: add             x2, x2, HEAP, lsl #32
    // 0xa500b0: ldur            x0, [fp, #-0x18]
    // 0xa500b4: LoadField: r3 = r0->field_f
    //     0xa500b4: ldur            w3, [x0, #0xf]
    // 0xa500b8: DecompressPointer r3
    //     0xa500b8: add             x3, x3, HEAP, lsl #32
    // 0xa500bc: r0 = LoadClassIdInstr(r2)
    //     0xa500bc: ldur            x0, [x2, #-1]
    //     0xa500c0: ubfx            x0, x0, #0xc, #0x14
    // 0xa500c4: stp             x3, x2, [SP]
    // 0xa500c8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa500c8: sub             lr, x0, #0xb7
    //     0xa500cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa500d0: blr             lr
    // 0xa500d4: cmp             w0, NULL
    // 0xa500d8: b.ne            #0xa500e4
    // 0xa500dc: r0 = Null
    //     0xa500dc: mov             x0, NULL
    // 0xa500e0: b               #0xa500f0
    // 0xa500e4: LoadField: r1 = r0->field_bb
    //     0xa500e4: ldur            w1, [x0, #0xbb]
    // 0xa500e8: DecompressPointer r1
    //     0xa500e8: add             x1, x1, HEAP, lsl #32
    // 0xa500ec: mov             x0, x1
    // 0xa500f0: cmp             w0, NULL
    // 0xa500f4: b.ne            #0xa50100
    // 0xa500f8: r1 = false
    //     0xa500f8: add             x1, NULL, #0x30  ; false
    // 0xa500fc: b               #0xa50104
    // 0xa50100: mov             x1, x0
    // 0xa50104: ldur            x0, [fp, #-8]
    // 0xa50108: stur            x1, [fp, #-0x10]
    // 0xa5010c: r0 = SvgPicture()
    //     0xa5010c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa50110: mov             x1, x0
    // 0xa50114: r2 = "assets/images/free-gift-icon.svg"
    //     0xa50114: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xa50118: ldr             x2, [x2, #0xd40]
    // 0xa5011c: stur            x0, [fp, #-0x18]
    // 0xa50120: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa50120: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa50124: r0 = SvgPicture.asset()
    //     0xa50124: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa50128: r0 = Padding()
    //     0xa50128: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa5012c: mov             x1, x0
    // 0xa50130: r0 = Instance_EdgeInsets
    //     0xa50130: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xa50134: ldr             x0, [x0, #0xd48]
    // 0xa50138: stur            x1, [fp, #-0x20]
    // 0xa5013c: StoreField: r1->field_f = r0
    //     0xa5013c: stur            w0, [x1, #0xf]
    // 0xa50140: ldur            x0, [fp, #-0x18]
    // 0xa50144: StoreField: r1->field_b = r0
    //     0xa50144: stur            w0, [x1, #0xb]
    // 0xa50148: r0 = Visibility()
    //     0xa50148: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa5014c: mov             x3, x0
    // 0xa50150: ldur            x0, [fp, #-0x20]
    // 0xa50154: stur            x3, [fp, #-0x18]
    // 0xa50158: StoreField: r3->field_b = r0
    //     0xa50158: stur            w0, [x3, #0xb]
    // 0xa5015c: r0 = Instance_SizedBox
    //     0xa5015c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa50160: StoreField: r3->field_f = r0
    //     0xa50160: stur            w0, [x3, #0xf]
    // 0xa50164: ldur            x0, [fp, #-0x10]
    // 0xa50168: StoreField: r3->field_13 = r0
    //     0xa50168: stur            w0, [x3, #0x13]
    // 0xa5016c: r0 = false
    //     0xa5016c: add             x0, NULL, #0x30  ; false
    // 0xa50170: ArrayStore: r3[0] = r0  ; List_4
    //     0xa50170: stur            w0, [x3, #0x17]
    // 0xa50174: StoreField: r3->field_1b = r0
    //     0xa50174: stur            w0, [x3, #0x1b]
    // 0xa50178: StoreField: r3->field_1f = r0
    //     0xa50178: stur            w0, [x3, #0x1f]
    // 0xa5017c: StoreField: r3->field_23 = r0
    //     0xa5017c: stur            w0, [x3, #0x23]
    // 0xa50180: StoreField: r3->field_27 = r0
    //     0xa50180: stur            w0, [x3, #0x27]
    // 0xa50184: StoreField: r3->field_2b = r0
    //     0xa50184: stur            w0, [x3, #0x2b]
    // 0xa50188: r1 = Null
    //     0xa50188: mov             x1, NULL
    // 0xa5018c: r2 = 4
    //     0xa5018c: movz            x2, #0x4
    // 0xa50190: r0 = AllocateArray()
    //     0xa50190: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa50194: mov             x2, x0
    // 0xa50198: ldur            x0, [fp, #-8]
    // 0xa5019c: stur            x2, [fp, #-0x10]
    // 0xa501a0: StoreField: r2->field_f = r0
    //     0xa501a0: stur            w0, [x2, #0xf]
    // 0xa501a4: ldur            x0, [fp, #-0x18]
    // 0xa501a8: StoreField: r2->field_13 = r0
    //     0xa501a8: stur            w0, [x2, #0x13]
    // 0xa501ac: r1 = <Widget>
    //     0xa501ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa501b0: r0 = AllocateGrowableArray()
    //     0xa501b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa501b4: mov             x1, x0
    // 0xa501b8: ldur            x0, [fp, #-0x10]
    // 0xa501bc: stur            x1, [fp, #-8]
    // 0xa501c0: StoreField: r1->field_f = r0
    //     0xa501c0: stur            w0, [x1, #0xf]
    // 0xa501c4: r0 = 4
    //     0xa501c4: movz            x0, #0x4
    // 0xa501c8: StoreField: r1->field_b = r0
    //     0xa501c8: stur            w0, [x1, #0xb]
    // 0xa501cc: r0 = Stack()
    //     0xa501cc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa501d0: r1 = Instance_Alignment
    //     0xa501d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xa501d4: ldr             x1, [x1, #0x950]
    // 0xa501d8: StoreField: r0->field_f = r1
    //     0xa501d8: stur            w1, [x0, #0xf]
    // 0xa501dc: r1 = Instance_StackFit
    //     0xa501dc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa501e0: ldr             x1, [x1, #0xfa8]
    // 0xa501e4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa501e4: stur            w1, [x0, #0x17]
    // 0xa501e8: r1 = Instance_Clip
    //     0xa501e8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa501ec: ldr             x1, [x1, #0x7e0]
    // 0xa501f0: StoreField: r0->field_1b = r1
    //     0xa501f0: stur            w1, [x0, #0x1b]
    // 0xa501f4: ldur            x1, [fp, #-8]
    // 0xa501f8: StoreField: r0->field_b = r1
    //     0xa501f8: stur            w1, [x0, #0xb]
    // 0xa501fc: LeaveFrame
    //     0xa501fc: mov             SP, fp
    //     0xa50200: ldp             fp, lr, [SP], #0x10
    // 0xa50204: ret
    //     0xa50204: ret             
    // 0xa50208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50208: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5020c: b               #0xa4ff68
    // 0xa50210: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50210: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50214: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa50214: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa50218: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50218: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa5021c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5021c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ basicThemeSlider(/* No info */) {
    // ** addr: 0xa50220, size: 0x1128
    // 0xa50220: EnterFrame
    //     0xa50220: stp             fp, lr, [SP, #-0x10]!
    //     0xa50224: mov             fp, SP
    // 0xa50228: AllocStack(0x90)
    //     0xa50228: sub             SP, SP, #0x90
    // 0xa5022c: SetupParameters(_ProductGridItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa5022c: stur            x1, [fp, #-8]
    //     0xa50230: stur            x2, [fp, #-0x10]
    //     0xa50234: stur            x3, [fp, #-0x18]
    // 0xa50238: CheckStackOverflow
    //     0xa50238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5023c: cmp             SP, x16
    //     0xa50240: b.ls            #0xa51304
    // 0xa50244: r1 = 2
    //     0xa50244: movz            x1, #0x2
    // 0xa50248: r0 = AllocateContext()
    //     0xa50248: bl              #0x16f6108  ; AllocateContextStub
    // 0xa5024c: mov             x3, x0
    // 0xa50250: ldur            x0, [fp, #-8]
    // 0xa50254: stur            x3, [fp, #-0x20]
    // 0xa50258: StoreField: r3->field_f = r0
    //     0xa50258: stur            w0, [x3, #0xf]
    // 0xa5025c: ldur            x1, [fp, #-0x18]
    // 0xa50260: StoreField: r3->field_13 = r1
    //     0xa50260: stur            w1, [x3, #0x13]
    // 0xa50264: ldur            x1, [fp, #-0x10]
    // 0xa50268: cmp             w1, NULL
    // 0xa5026c: b.eq            #0xa5130c
    // 0xa50270: LoadField: r2 = r1->field_b
    //     0xa50270: ldur            w2, [x1, #0xb]
    // 0xa50274: DecompressPointer r2
    //     0xa50274: add             x2, x2, HEAP, lsl #32
    // 0xa50278: cmp             w2, NULL
    // 0xa5027c: b.ne            #0xa50288
    // 0xa50280: r4 = ""
    //     0xa50280: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa50284: b               #0xa5028c
    // 0xa50288: mov             x4, x2
    // 0xa5028c: stur            x4, [fp, #-0x10]
    // 0xa50290: r1 = Function '<anonymous closure>':.
    //     0xa50290: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab08] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa50294: ldr             x1, [x1, #0xb08]
    // 0xa50298: r2 = Null
    //     0xa50298: mov             x2, NULL
    // 0xa5029c: r0 = AllocateClosure()
    //     0xa5029c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa502a0: r1 = Function '<anonymous closure>':.
    //     0xa502a0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab10] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa502a4: ldr             x1, [x1, #0xb10]
    // 0xa502a8: r2 = Null
    //     0xa502a8: mov             x2, NULL
    // 0xa502ac: stur            x0, [fp, #-0x18]
    // 0xa502b0: r0 = AllocateClosure()
    //     0xa502b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa502b4: stur            x0, [fp, #-0x28]
    // 0xa502b8: r0 = CachedNetworkImage()
    //     0xa502b8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa502bc: stur            x0, [fp, #-0x30]
    // 0xa502c0: r16 = Instance_BoxFit
    //     0xa502c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa502c4: ldr             x16, [x16, #0x118]
    // 0xa502c8: ldur            lr, [fp, #-0x18]
    // 0xa502cc: stp             lr, x16, [SP, #8]
    // 0xa502d0: ldur            x16, [fp, #-0x28]
    // 0xa502d4: str             x16, [SP]
    // 0xa502d8: mov             x1, x0
    // 0xa502dc: ldur            x2, [fp, #-0x10]
    // 0xa502e0: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xa502e0: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xa502e4: ldr             x4, [x4, #0x638]
    // 0xa502e8: r0 = CachedNetworkImage()
    //     0xa502e8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa502ec: r0 = ClipRRect()
    //     0xa502ec: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa502f0: mov             x1, x0
    // 0xa502f4: r0 = Instance_BorderRadius
    //     0xa502f4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xa502f8: ldr             x0, [x0, #0xe10]
    // 0xa502fc: stur            x1, [fp, #-0x10]
    // 0xa50300: StoreField: r1->field_f = r0
    //     0xa50300: stur            w0, [x1, #0xf]
    // 0xa50304: r0 = Instance_Clip
    //     0xa50304: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa50308: ldr             x0, [x0, #0x138]
    // 0xa5030c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5030c: stur            w0, [x1, #0x17]
    // 0xa50310: ldur            x0, [fp, #-0x30]
    // 0xa50314: StoreField: r1->field_b = r0
    //     0xa50314: stur            w0, [x1, #0xb]
    // 0xa50318: r0 = Center()
    //     0xa50318: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa5031c: mov             x1, x0
    // 0xa50320: r0 = Instance_Alignment
    //     0xa50320: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa50324: ldr             x0, [x0, #0xb10]
    // 0xa50328: stur            x1, [fp, #-0x28]
    // 0xa5032c: StoreField: r1->field_f = r0
    //     0xa5032c: stur            w0, [x1, #0xf]
    // 0xa50330: ldur            x0, [fp, #-0x10]
    // 0xa50334: StoreField: r1->field_b = r0
    //     0xa50334: stur            w0, [x1, #0xb]
    // 0xa50338: ldur            x2, [fp, #-0x20]
    // 0xa5033c: LoadField: r0 = r2->field_13
    //     0xa5033c: ldur            w0, [x2, #0x13]
    // 0xa50340: DecompressPointer r0
    //     0xa50340: add             x0, x0, HEAP, lsl #32
    // 0xa50344: stur            x0, [fp, #-0x18]
    // 0xa50348: cmp             w0, NULL
    // 0xa5034c: b.ne            #0xa50358
    // 0xa50350: r3 = Null
    //     0xa50350: mov             x3, NULL
    // 0xa50354: b               #0xa50384
    // 0xa50358: LoadField: r3 = r0->field_7f
    //     0xa50358: ldur            w3, [x0, #0x7f]
    // 0xa5035c: DecompressPointer r3
    //     0xa5035c: add             x3, x3, HEAP, lsl #32
    // 0xa50360: cmp             w3, NULL
    // 0xa50364: b.ne            #0xa50370
    // 0xa50368: r3 = Null
    //     0xa50368: mov             x3, NULL
    // 0xa5036c: b               #0xa50384
    // 0xa50370: LoadField: r4 = r3->field_7
    //     0xa50370: ldur            w4, [x3, #7]
    // 0xa50374: cbnz            w4, #0xa50380
    // 0xa50378: r3 = false
    //     0xa50378: add             x3, NULL, #0x30  ; false
    // 0xa5037c: b               #0xa50384
    // 0xa50380: r3 = true
    //     0xa50380: add             x3, NULL, #0x20  ; true
    // 0xa50384: cmp             w3, NULL
    // 0xa50388: b.ne            #0xa50390
    // 0xa5038c: r3 = false
    //     0xa5038c: add             x3, NULL, #0x30  ; false
    // 0xa50390: stur            x3, [fp, #-0x10]
    // 0xa50394: cmp             w0, NULL
    // 0xa50398: b.ne            #0xa503a4
    // 0xa5039c: r4 = Null
    //     0xa5039c: mov             x4, NULL
    // 0xa503a0: b               #0xa503ac
    // 0xa503a4: LoadField: r4 = r0->field_93
    //     0xa503a4: ldur            w4, [x0, #0x93]
    // 0xa503a8: DecompressPointer r4
    //     0xa503a8: add             x4, x4, HEAP, lsl #32
    // 0xa503ac: cmp             w4, NULL
    // 0xa503b0: b.ne            #0xa503c8
    // 0xa503b4: mov             x4, x2
    // 0xa503b8: r0 = Instance_Alignment
    //     0xa503b8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa503bc: ldr             x0, [x0, #0xfa0]
    // 0xa503c0: r2 = 4
    //     0xa503c0: movz            x2, #0x4
    // 0xa503c4: b               #0xa50920
    // 0xa503c8: tbnz            w4, #4, #0xa50910
    // 0xa503cc: ldur            x4, [fp, #-8]
    // 0xa503d0: r0 = Radius()
    //     0xa503d0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa503d4: d0 = 8.000000
    //     0xa503d4: fmov            d0, #8.00000000
    // 0xa503d8: stur            x0, [fp, #-0x30]
    // 0xa503dc: StoreField: r0->field_7 = d0
    //     0xa503dc: stur            d0, [x0, #7]
    // 0xa503e0: StoreField: r0->field_f = d0
    //     0xa503e0: stur            d0, [x0, #0xf]
    // 0xa503e4: r0 = BorderRadius()
    //     0xa503e4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa503e8: mov             x1, x0
    // 0xa503ec: ldur            x0, [fp, #-0x30]
    // 0xa503f0: stur            x1, [fp, #-0x50]
    // 0xa503f4: StoreField: r1->field_7 = r0
    //     0xa503f4: stur            w0, [x1, #7]
    // 0xa503f8: StoreField: r1->field_b = r0
    //     0xa503f8: stur            w0, [x1, #0xb]
    // 0xa503fc: StoreField: r1->field_f = r0
    //     0xa503fc: stur            w0, [x1, #0xf]
    // 0xa50400: StoreField: r1->field_13 = r0
    //     0xa50400: stur            w0, [x1, #0x13]
    // 0xa50404: ldur            x0, [fp, #-8]
    // 0xa50408: LoadField: r2 = r0->field_b
    //     0xa50408: ldur            w2, [x0, #0xb]
    // 0xa5040c: DecompressPointer r2
    //     0xa5040c: add             x2, x2, HEAP, lsl #32
    // 0xa50410: cmp             w2, NULL
    // 0xa50414: b.eq            #0xa51310
    // 0xa50418: LoadField: r3 = r2->field_47
    //     0xa50418: ldur            w3, [x2, #0x47]
    // 0xa5041c: DecompressPointer r3
    //     0xa5041c: add             x3, x3, HEAP, lsl #32
    // 0xa50420: stur            x3, [fp, #-0x30]
    // 0xa50424: cmp             w3, NULL
    // 0xa50428: b.ne            #0xa50434
    // 0xa5042c: r2 = Null
    //     0xa5042c: mov             x2, NULL
    // 0xa50430: b               #0xa50458
    // 0xa50434: LoadField: r2 = r3->field_13
    //     0xa50434: ldur            w2, [x3, #0x13]
    // 0xa50438: DecompressPointer r2
    //     0xa50438: add             x2, x2, HEAP, lsl #32
    // 0xa5043c: cmp             w2, NULL
    // 0xa50440: b.ne            #0xa5044c
    // 0xa50444: r2 = Null
    //     0xa50444: mov             x2, NULL
    // 0xa50448: b               #0xa50458
    // 0xa5044c: LoadField: r4 = r2->field_7
    //     0xa5044c: ldur            w4, [x2, #7]
    // 0xa50450: DecompressPointer r4
    //     0xa50450: add             x4, x4, HEAP, lsl #32
    // 0xa50454: mov             x2, x4
    // 0xa50458: cmp             w2, NULL
    // 0xa5045c: b.ne            #0xa50468
    // 0xa50460: r2 = 0
    //     0xa50460: movz            x2, #0
    // 0xa50464: b               #0xa50478
    // 0xa50468: r4 = LoadInt32Instr(r2)
    //     0xa50468: sbfx            x4, x2, #1, #0x1f
    //     0xa5046c: tbz             w2, #0, #0xa50474
    //     0xa50470: ldur            x4, [x2, #7]
    // 0xa50474: mov             x2, x4
    // 0xa50478: stur            x2, [fp, #-0x48]
    // 0xa5047c: cmp             w3, NULL
    // 0xa50480: b.ne            #0xa5048c
    // 0xa50484: r4 = Null
    //     0xa50484: mov             x4, NULL
    // 0xa50488: b               #0xa504b0
    // 0xa5048c: LoadField: r4 = r3->field_13
    //     0xa5048c: ldur            w4, [x3, #0x13]
    // 0xa50490: DecompressPointer r4
    //     0xa50490: add             x4, x4, HEAP, lsl #32
    // 0xa50494: cmp             w4, NULL
    // 0xa50498: b.ne            #0xa504a4
    // 0xa5049c: r4 = Null
    //     0xa5049c: mov             x4, NULL
    // 0xa504a0: b               #0xa504b0
    // 0xa504a4: LoadField: r5 = r4->field_b
    //     0xa504a4: ldur            w5, [x4, #0xb]
    // 0xa504a8: DecompressPointer r5
    //     0xa504a8: add             x5, x5, HEAP, lsl #32
    // 0xa504ac: mov             x4, x5
    // 0xa504b0: cmp             w4, NULL
    // 0xa504b4: b.ne            #0xa504c0
    // 0xa504b8: r4 = 0
    //     0xa504b8: movz            x4, #0
    // 0xa504bc: b               #0xa504d0
    // 0xa504c0: r5 = LoadInt32Instr(r4)
    //     0xa504c0: sbfx            x5, x4, #1, #0x1f
    //     0xa504c4: tbz             w4, #0, #0xa504cc
    //     0xa504c8: ldur            x5, [x4, #7]
    // 0xa504cc: mov             x4, x5
    // 0xa504d0: stur            x4, [fp, #-0x40]
    // 0xa504d4: cmp             w3, NULL
    // 0xa504d8: b.ne            #0xa504e4
    // 0xa504dc: r5 = Null
    //     0xa504dc: mov             x5, NULL
    // 0xa504e0: b               #0xa50508
    // 0xa504e4: LoadField: r5 = r3->field_13
    //     0xa504e4: ldur            w5, [x3, #0x13]
    // 0xa504e8: DecompressPointer r5
    //     0xa504e8: add             x5, x5, HEAP, lsl #32
    // 0xa504ec: cmp             w5, NULL
    // 0xa504f0: b.ne            #0xa504fc
    // 0xa504f4: r5 = Null
    //     0xa504f4: mov             x5, NULL
    // 0xa504f8: b               #0xa50508
    // 0xa504fc: LoadField: r6 = r5->field_f
    //     0xa504fc: ldur            w6, [x5, #0xf]
    // 0xa50500: DecompressPointer r6
    //     0xa50500: add             x6, x6, HEAP, lsl #32
    // 0xa50504: mov             x5, x6
    // 0xa50508: cmp             w5, NULL
    // 0xa5050c: b.ne            #0xa50518
    // 0xa50510: r5 = 0
    //     0xa50510: movz            x5, #0
    // 0xa50514: b               #0xa50528
    // 0xa50518: r6 = LoadInt32Instr(r5)
    //     0xa50518: sbfx            x6, x5, #1, #0x1f
    //     0xa5051c: tbz             w5, #0, #0xa50524
    //     0xa50520: ldur            x6, [x5, #7]
    // 0xa50524: mov             x5, x6
    // 0xa50528: stur            x5, [fp, #-0x38]
    // 0xa5052c: r0 = Color()
    //     0xa5052c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa50530: mov             x1, x0
    // 0xa50534: r0 = Instance_ColorSpace
    //     0xa50534: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa50538: stur            x1, [fp, #-0x58]
    // 0xa5053c: StoreField: r1->field_27 = r0
    //     0xa5053c: stur            w0, [x1, #0x27]
    // 0xa50540: d0 = 1.000000
    //     0xa50540: fmov            d0, #1.00000000
    // 0xa50544: StoreField: r1->field_7 = d0
    //     0xa50544: stur            d0, [x1, #7]
    // 0xa50548: ldur            x2, [fp, #-0x48]
    // 0xa5054c: ubfx            x2, x2, #0, #0x20
    // 0xa50550: and             w3, w2, #0xff
    // 0xa50554: ubfx            x3, x3, #0, #0x20
    // 0xa50558: scvtf           d0, x3
    // 0xa5055c: d1 = 255.000000
    //     0xa5055c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa50560: fdiv            d2, d0, d1
    // 0xa50564: StoreField: r1->field_f = d2
    //     0xa50564: stur            d2, [x1, #0xf]
    // 0xa50568: ldur            x2, [fp, #-0x40]
    // 0xa5056c: ubfx            x2, x2, #0, #0x20
    // 0xa50570: and             w3, w2, #0xff
    // 0xa50574: ubfx            x3, x3, #0, #0x20
    // 0xa50578: scvtf           d0, x3
    // 0xa5057c: fdiv            d2, d0, d1
    // 0xa50580: ArrayStore: r1[0] = d2  ; List_8
    //     0xa50580: stur            d2, [x1, #0x17]
    // 0xa50584: ldur            x2, [fp, #-0x38]
    // 0xa50588: ubfx            x2, x2, #0, #0x20
    // 0xa5058c: and             w3, w2, #0xff
    // 0xa50590: ubfx            x3, x3, #0, #0x20
    // 0xa50594: scvtf           d0, x3
    // 0xa50598: fdiv            d2, d0, d1
    // 0xa5059c: StoreField: r1->field_1f = d2
    //     0xa5059c: stur            d2, [x1, #0x1f]
    // 0xa505a0: ldur            x2, [fp, #-0x30]
    // 0xa505a4: cmp             w2, NULL
    // 0xa505a8: b.ne            #0xa505b4
    // 0xa505ac: r3 = Null
    //     0xa505ac: mov             x3, NULL
    // 0xa505b0: b               #0xa505d8
    // 0xa505b4: LoadField: r3 = r2->field_13
    //     0xa505b4: ldur            w3, [x2, #0x13]
    // 0xa505b8: DecompressPointer r3
    //     0xa505b8: add             x3, x3, HEAP, lsl #32
    // 0xa505bc: cmp             w3, NULL
    // 0xa505c0: b.ne            #0xa505cc
    // 0xa505c4: r3 = Null
    //     0xa505c4: mov             x3, NULL
    // 0xa505c8: b               #0xa505d8
    // 0xa505cc: LoadField: r4 = r3->field_7
    //     0xa505cc: ldur            w4, [x3, #7]
    // 0xa505d0: DecompressPointer r4
    //     0xa505d0: add             x4, x4, HEAP, lsl #32
    // 0xa505d4: mov             x3, x4
    // 0xa505d8: cmp             w3, NULL
    // 0xa505dc: b.ne            #0xa505e8
    // 0xa505e0: r3 = 0
    //     0xa505e0: movz            x3, #0
    // 0xa505e4: b               #0xa505f8
    // 0xa505e8: r4 = LoadInt32Instr(r3)
    //     0xa505e8: sbfx            x4, x3, #1, #0x1f
    //     0xa505ec: tbz             w3, #0, #0xa505f4
    //     0xa505f0: ldur            x4, [x3, #7]
    // 0xa505f4: mov             x3, x4
    // 0xa505f8: stur            x3, [fp, #-0x48]
    // 0xa505fc: cmp             w2, NULL
    // 0xa50600: b.ne            #0xa5060c
    // 0xa50604: r4 = Null
    //     0xa50604: mov             x4, NULL
    // 0xa50608: b               #0xa50630
    // 0xa5060c: LoadField: r4 = r2->field_13
    //     0xa5060c: ldur            w4, [x2, #0x13]
    // 0xa50610: DecompressPointer r4
    //     0xa50610: add             x4, x4, HEAP, lsl #32
    // 0xa50614: cmp             w4, NULL
    // 0xa50618: b.ne            #0xa50624
    // 0xa5061c: r4 = Null
    //     0xa5061c: mov             x4, NULL
    // 0xa50620: b               #0xa50630
    // 0xa50624: LoadField: r5 = r4->field_b
    //     0xa50624: ldur            w5, [x4, #0xb]
    // 0xa50628: DecompressPointer r5
    //     0xa50628: add             x5, x5, HEAP, lsl #32
    // 0xa5062c: mov             x4, x5
    // 0xa50630: cmp             w4, NULL
    // 0xa50634: b.ne            #0xa50640
    // 0xa50638: r4 = 0
    //     0xa50638: movz            x4, #0
    // 0xa5063c: b               #0xa50650
    // 0xa50640: r5 = LoadInt32Instr(r4)
    //     0xa50640: sbfx            x5, x4, #1, #0x1f
    //     0xa50644: tbz             w4, #0, #0xa5064c
    //     0xa50648: ldur            x5, [x4, #7]
    // 0xa5064c: mov             x4, x5
    // 0xa50650: stur            x4, [fp, #-0x40]
    // 0xa50654: cmp             w2, NULL
    // 0xa50658: b.ne            #0xa50664
    // 0xa5065c: r2 = Null
    //     0xa5065c: mov             x2, NULL
    // 0xa50660: b               #0xa50684
    // 0xa50664: LoadField: r5 = r2->field_13
    //     0xa50664: ldur            w5, [x2, #0x13]
    // 0xa50668: DecompressPointer r5
    //     0xa50668: add             x5, x5, HEAP, lsl #32
    // 0xa5066c: cmp             w5, NULL
    // 0xa50670: b.ne            #0xa5067c
    // 0xa50674: r2 = Null
    //     0xa50674: mov             x2, NULL
    // 0xa50678: b               #0xa50684
    // 0xa5067c: LoadField: r2 = r5->field_f
    //     0xa5067c: ldur            w2, [x5, #0xf]
    // 0xa50680: DecompressPointer r2
    //     0xa50680: add             x2, x2, HEAP, lsl #32
    // 0xa50684: cmp             w2, NULL
    // 0xa50688: b.ne            #0xa50694
    // 0xa5068c: r6 = 0
    //     0xa5068c: movz            x6, #0
    // 0xa50690: b               #0xa506a4
    // 0xa50694: r5 = LoadInt32Instr(r2)
    //     0xa50694: sbfx            x5, x2, #1, #0x1f
    //     0xa50698: tbz             w2, #0, #0xa506a0
    //     0xa5069c: ldur            x5, [x2, #7]
    // 0xa506a0: mov             x6, x5
    // 0xa506a4: ldur            x5, [fp, #-0x18]
    // 0xa506a8: ldur            x2, [fp, #-0x50]
    // 0xa506ac: stur            x6, [fp, #-0x38]
    // 0xa506b0: r0 = Color()
    //     0xa506b0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa506b4: mov             x3, x0
    // 0xa506b8: r0 = Instance_ColorSpace
    //     0xa506b8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa506bc: stur            x3, [fp, #-0x30]
    // 0xa506c0: StoreField: r3->field_27 = r0
    //     0xa506c0: stur            w0, [x3, #0x27]
    // 0xa506c4: d0 = 0.700000
    //     0xa506c4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa506c8: ldr             d0, [x17, #0xf48]
    // 0xa506cc: StoreField: r3->field_7 = d0
    //     0xa506cc: stur            d0, [x3, #7]
    // 0xa506d0: ldur            x0, [fp, #-0x48]
    // 0xa506d4: ubfx            x0, x0, #0, #0x20
    // 0xa506d8: and             w1, w0, #0xff
    // 0xa506dc: ubfx            x1, x1, #0, #0x20
    // 0xa506e0: scvtf           d0, x1
    // 0xa506e4: d1 = 255.000000
    //     0xa506e4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa506e8: fdiv            d2, d0, d1
    // 0xa506ec: StoreField: r3->field_f = d2
    //     0xa506ec: stur            d2, [x3, #0xf]
    // 0xa506f0: ldur            x0, [fp, #-0x40]
    // 0xa506f4: ubfx            x0, x0, #0, #0x20
    // 0xa506f8: and             w1, w0, #0xff
    // 0xa506fc: ubfx            x1, x1, #0, #0x20
    // 0xa50700: scvtf           d0, x1
    // 0xa50704: fdiv            d2, d0, d1
    // 0xa50708: ArrayStore: r3[0] = d2  ; List_8
    //     0xa50708: stur            d2, [x3, #0x17]
    // 0xa5070c: ldur            x0, [fp, #-0x38]
    // 0xa50710: ubfx            x0, x0, #0, #0x20
    // 0xa50714: and             w1, w0, #0xff
    // 0xa50718: ubfx            x1, x1, #0, #0x20
    // 0xa5071c: scvtf           d0, x1
    // 0xa50720: fdiv            d2, d0, d1
    // 0xa50724: StoreField: r3->field_1f = d2
    //     0xa50724: stur            d2, [x3, #0x1f]
    // 0xa50728: r1 = Null
    //     0xa50728: mov             x1, NULL
    // 0xa5072c: r2 = 4
    //     0xa5072c: movz            x2, #0x4
    // 0xa50730: r0 = AllocateArray()
    //     0xa50730: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa50734: mov             x2, x0
    // 0xa50738: ldur            x0, [fp, #-0x58]
    // 0xa5073c: stur            x2, [fp, #-0x60]
    // 0xa50740: StoreField: r2->field_f = r0
    //     0xa50740: stur            w0, [x2, #0xf]
    // 0xa50744: ldur            x0, [fp, #-0x30]
    // 0xa50748: StoreField: r2->field_13 = r0
    //     0xa50748: stur            w0, [x2, #0x13]
    // 0xa5074c: r1 = <Color>
    //     0xa5074c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa50750: ldr             x1, [x1, #0xf80]
    // 0xa50754: r0 = AllocateGrowableArray()
    //     0xa50754: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa50758: mov             x1, x0
    // 0xa5075c: ldur            x0, [fp, #-0x60]
    // 0xa50760: stur            x1, [fp, #-0x30]
    // 0xa50764: StoreField: r1->field_f = r0
    //     0xa50764: stur            w0, [x1, #0xf]
    // 0xa50768: r2 = 4
    //     0xa50768: movz            x2, #0x4
    // 0xa5076c: StoreField: r1->field_b = r2
    //     0xa5076c: stur            w2, [x1, #0xb]
    // 0xa50770: r0 = LinearGradient()
    //     0xa50770: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xa50774: mov             x1, x0
    // 0xa50778: r0 = Instance_Alignment
    //     0xa50778: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xa5077c: ldr             x0, [x0, #0xce0]
    // 0xa50780: stur            x1, [fp, #-0x58]
    // 0xa50784: StoreField: r1->field_13 = r0
    //     0xa50784: stur            w0, [x1, #0x13]
    // 0xa50788: r0 = Instance_Alignment
    //     0xa50788: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xa5078c: ldr             x0, [x0, #0xce8]
    // 0xa50790: ArrayStore: r1[0] = r0  ; List_4
    //     0xa50790: stur            w0, [x1, #0x17]
    // 0xa50794: r0 = Instance_TileMode
    //     0xa50794: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xa50798: ldr             x0, [x0, #0xcf0]
    // 0xa5079c: StoreField: r1->field_1b = r0
    //     0xa5079c: stur            w0, [x1, #0x1b]
    // 0xa507a0: ldur            x0, [fp, #-0x30]
    // 0xa507a4: StoreField: r1->field_7 = r0
    //     0xa507a4: stur            w0, [x1, #7]
    // 0xa507a8: r0 = BoxDecoration()
    //     0xa507a8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa507ac: mov             x2, x0
    // 0xa507b0: ldur            x0, [fp, #-0x50]
    // 0xa507b4: stur            x2, [fp, #-0x30]
    // 0xa507b8: StoreField: r2->field_13 = r0
    //     0xa507b8: stur            w0, [x2, #0x13]
    // 0xa507bc: ldur            x0, [fp, #-0x58]
    // 0xa507c0: StoreField: r2->field_1b = r0
    //     0xa507c0: stur            w0, [x2, #0x1b]
    // 0xa507c4: r0 = Instance_BoxShape
    //     0xa507c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa507c8: ldr             x0, [x0, #0x80]
    // 0xa507cc: StoreField: r2->field_23 = r0
    //     0xa507cc: stur            w0, [x2, #0x23]
    // 0xa507d0: ldur            x1, [fp, #-0x18]
    // 0xa507d4: cmp             w1, NULL
    // 0xa507d8: b.ne            #0xa507e4
    // 0xa507dc: r1 = Null
    //     0xa507dc: mov             x1, NULL
    // 0xa507e0: b               #0xa507f0
    // 0xa507e4: LoadField: r3 = r1->field_7f
    //     0xa507e4: ldur            w3, [x1, #0x7f]
    // 0xa507e8: DecompressPointer r3
    //     0xa507e8: add             x3, x3, HEAP, lsl #32
    // 0xa507ec: mov             x1, x3
    // 0xa507f0: cmp             w1, NULL
    // 0xa507f4: b.ne            #0xa50800
    // 0xa507f8: r4 = ""
    //     0xa507f8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa507fc: b               #0xa50804
    // 0xa50800: mov             x4, x1
    // 0xa50804: ldur            x3, [fp, #-8]
    // 0xa50808: stur            x4, [fp, #-0x18]
    // 0xa5080c: LoadField: r1 = r3->field_f
    //     0xa5080c: ldur            w1, [x3, #0xf]
    // 0xa50810: DecompressPointer r1
    //     0xa50810: add             x1, x1, HEAP, lsl #32
    // 0xa50814: cmp             w1, NULL
    // 0xa50818: b.eq            #0xa51314
    // 0xa5081c: r0 = of()
    //     0xa5081c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa50820: LoadField: r1 = r0->field_87
    //     0xa50820: ldur            w1, [x0, #0x87]
    // 0xa50824: DecompressPointer r1
    //     0xa50824: add             x1, x1, HEAP, lsl #32
    // 0xa50828: LoadField: r0 = r1->field_7
    //     0xa50828: ldur            w0, [x1, #7]
    // 0xa5082c: DecompressPointer r0
    //     0xa5082c: add             x0, x0, HEAP, lsl #32
    // 0xa50830: r16 = 12.000000
    //     0xa50830: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa50834: ldr             x16, [x16, #0x9e8]
    // 0xa50838: r30 = Instance_Color
    //     0xa50838: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa5083c: stp             lr, x16, [SP]
    // 0xa50840: mov             x1, x0
    // 0xa50844: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa50844: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa50848: ldr             x4, [x4, #0xaa0]
    // 0xa5084c: r0 = copyWith()
    //     0xa5084c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa50850: stur            x0, [fp, #-0x50]
    // 0xa50854: r0 = Text()
    //     0xa50854: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa50858: mov             x1, x0
    // 0xa5085c: ldur            x0, [fp, #-0x18]
    // 0xa50860: stur            x1, [fp, #-0x58]
    // 0xa50864: StoreField: r1->field_b = r0
    //     0xa50864: stur            w0, [x1, #0xb]
    // 0xa50868: ldur            x0, [fp, #-0x50]
    // 0xa5086c: StoreField: r1->field_13 = r0
    //     0xa5086c: stur            w0, [x1, #0x13]
    // 0xa50870: r0 = Instance_TextAlign
    //     0xa50870: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa50874: StoreField: r1->field_1b = r0
    //     0xa50874: stur            w0, [x1, #0x1b]
    // 0xa50878: r0 = Padding()
    //     0xa50878: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa5087c: mov             x1, x0
    // 0xa50880: r0 = Instance_EdgeInsets
    //     0xa50880: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xa50884: ldr             x0, [x0, #0x850]
    // 0xa50888: stur            x1, [fp, #-0x18]
    // 0xa5088c: StoreField: r1->field_f = r0
    //     0xa5088c: stur            w0, [x1, #0xf]
    // 0xa50890: ldur            x0, [fp, #-0x58]
    // 0xa50894: StoreField: r1->field_b = r0
    //     0xa50894: stur            w0, [x1, #0xb]
    // 0xa50898: r0 = Container()
    //     0xa50898: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa5089c: stur            x0, [fp, #-0x50]
    // 0xa508a0: r16 = 20.000000
    //     0xa508a0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa508a4: ldr             x16, [x16, #0xac8]
    // 0xa508a8: r30 = 120.000000
    //     0xa508a8: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xa508ac: ldr             lr, [lr, #0x3a0]
    // 0xa508b0: stp             lr, x16, [SP, #0x10]
    // 0xa508b4: ldur            x16, [fp, #-0x30]
    // 0xa508b8: ldur            lr, [fp, #-0x18]
    // 0xa508bc: stp             lr, x16, [SP]
    // 0xa508c0: mov             x1, x0
    // 0xa508c4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa508c4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa508c8: ldr             x4, [x4, #0x8c0]
    // 0xa508cc: r0 = Container()
    //     0xa508cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa508d0: r0 = Align()
    //     0xa508d0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa508d4: mov             x1, x0
    // 0xa508d8: r0 = Instance_Alignment
    //     0xa508d8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa508dc: ldr             x0, [x0, #0xfa0]
    // 0xa508e0: stur            x1, [fp, #-0x18]
    // 0xa508e4: StoreField: r1->field_f = r0
    //     0xa508e4: stur            w0, [x1, #0xf]
    // 0xa508e8: ldur            x0, [fp, #-0x50]
    // 0xa508ec: StoreField: r1->field_b = r0
    //     0xa508ec: stur            w0, [x1, #0xb]
    // 0xa508f0: r0 = Padding()
    //     0xa508f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa508f4: mov             x1, x0
    // 0xa508f8: r0 = Instance_EdgeInsets
    //     0xa508f8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xa508fc: ldr             x0, [x0, #0xe50]
    // 0xa50900: StoreField: r1->field_f = r0
    //     0xa50900: stur            w0, [x1, #0xf]
    // 0xa50904: ldur            x0, [fp, #-0x18]
    // 0xa50908: StoreField: r1->field_b = r0
    //     0xa50908: stur            w0, [x1, #0xb]
    // 0xa5090c: b               #0xa50c48
    // 0xa50910: r0 = Instance_Alignment
    //     0xa50910: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa50914: ldr             x0, [x0, #0xfa0]
    // 0xa50918: r2 = 4
    //     0xa50918: movz            x2, #0x4
    // 0xa5091c: ldur            x4, [fp, #-0x20]
    // 0xa50920: ldur            x3, [fp, #-8]
    // 0xa50924: LoadField: r1 = r3->field_f
    //     0xa50924: ldur            w1, [x3, #0xf]
    // 0xa50928: DecompressPointer r1
    //     0xa50928: add             x1, x1, HEAP, lsl #32
    // 0xa5092c: cmp             w1, NULL
    // 0xa50930: b.eq            #0xa51318
    // 0xa50934: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa50934: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa50938: r0 = _of()
    //     0xa50938: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa5093c: LoadField: r1 = r0->field_7
    //     0xa5093c: ldur            w1, [x0, #7]
    // 0xa50940: DecompressPointer r1
    //     0xa50940: add             x1, x1, HEAP, lsl #32
    // 0xa50944: LoadField: d0 = r1->field_7
    //     0xa50944: ldur            d0, [x1, #7]
    // 0xa50948: d1 = 0.370000
    //     0xa50948: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xa5094c: ldr             d1, [x17, #0xe40]
    // 0xa50950: fmul            d2, d0, d1
    // 0xa50954: stur            d2, [fp, #-0x70]
    // 0xa50958: r1 = Instance_Color
    //     0xa50958: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa5095c: d0 = 0.900000
    //     0xa5095c: ldr             d0, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xa50960: r0 = withOpacity()
    //     0xa50960: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa50964: stur            x0, [fp, #-0x18]
    // 0xa50968: r0 = Radius()
    //     0xa50968: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa5096c: d0 = 4.000000
    //     0xa5096c: fmov            d0, #4.00000000
    // 0xa50970: stur            x0, [fp, #-0x30]
    // 0xa50974: StoreField: r0->field_7 = d0
    //     0xa50974: stur            d0, [x0, #7]
    // 0xa50978: StoreField: r0->field_f = d0
    //     0xa50978: stur            d0, [x0, #0xf]
    // 0xa5097c: r0 = BorderRadius()
    //     0xa5097c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa50980: mov             x1, x0
    // 0xa50984: ldur            x0, [fp, #-0x30]
    // 0xa50988: stur            x1, [fp, #-0x50]
    // 0xa5098c: StoreField: r1->field_7 = r0
    //     0xa5098c: stur            w0, [x1, #7]
    // 0xa50990: StoreField: r1->field_b = r0
    //     0xa50990: stur            w0, [x1, #0xb]
    // 0xa50994: StoreField: r1->field_f = r0
    //     0xa50994: stur            w0, [x1, #0xf]
    // 0xa50998: StoreField: r1->field_13 = r0
    //     0xa50998: stur            w0, [x1, #0x13]
    // 0xa5099c: r0 = BoxDecoration()
    //     0xa5099c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa509a0: mov             x2, x0
    // 0xa509a4: ldur            x0, [fp, #-0x18]
    // 0xa509a8: stur            x2, [fp, #-0x30]
    // 0xa509ac: StoreField: r2->field_7 = r0
    //     0xa509ac: stur            w0, [x2, #7]
    // 0xa509b0: ldur            x0, [fp, #-0x50]
    // 0xa509b4: StoreField: r2->field_13 = r0
    //     0xa509b4: stur            w0, [x2, #0x13]
    // 0xa509b8: r0 = Instance_BoxShape
    //     0xa509b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa509bc: ldr             x0, [x0, #0x80]
    // 0xa509c0: StoreField: r2->field_23 = r0
    //     0xa509c0: stur            w0, [x2, #0x23]
    // 0xa509c4: ldur            x3, [fp, #-8]
    // 0xa509c8: LoadField: r1 = r3->field_f
    //     0xa509c8: ldur            w1, [x3, #0xf]
    // 0xa509cc: DecompressPointer r1
    //     0xa509cc: add             x1, x1, HEAP, lsl #32
    // 0xa509d0: cmp             w1, NULL
    // 0xa509d4: b.eq            #0xa5131c
    // 0xa509d8: r0 = of()
    //     0xa509d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa509dc: LoadField: r1 = r0->field_5b
    //     0xa509dc: ldur            w1, [x0, #0x5b]
    // 0xa509e0: DecompressPointer r1
    //     0xa509e0: add             x1, x1, HEAP, lsl #32
    // 0xa509e4: stur            x1, [fp, #-0x18]
    // 0xa509e8: r0 = ColorFilter()
    //     0xa509e8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa509ec: mov             x1, x0
    // 0xa509f0: ldur            x0, [fp, #-0x18]
    // 0xa509f4: stur            x1, [fp, #-0x50]
    // 0xa509f8: StoreField: r1->field_7 = r0
    //     0xa509f8: stur            w0, [x1, #7]
    // 0xa509fc: r0 = Instance_BlendMode
    //     0xa509fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa50a00: ldr             x0, [x0, #0xb30]
    // 0xa50a04: StoreField: r1->field_b = r0
    //     0xa50a04: stur            w0, [x1, #0xb]
    // 0xa50a08: r0 = 1
    //     0xa50a08: movz            x0, #0x1
    // 0xa50a0c: StoreField: r1->field_13 = r0
    //     0xa50a0c: stur            x0, [x1, #0x13]
    // 0xa50a10: r0 = SvgPicture()
    //     0xa50a10: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa50a14: stur            x0, [fp, #-0x18]
    // 0xa50a18: ldur            x16, [fp, #-0x50]
    // 0xa50a1c: str             x16, [SP]
    // 0xa50a20: mov             x1, x0
    // 0xa50a24: r2 = "assets/images/bumper_coupon.svg"
    //     0xa50a24: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xa50a28: ldr             x2, [x2, #0xe48]
    // 0xa50a2c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa50a2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa50a30: ldr             x4, [x4, #0xa38]
    // 0xa50a34: r0 = SvgPicture.asset()
    //     0xa50a34: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa50a38: ldur            x2, [fp, #-0x20]
    // 0xa50a3c: LoadField: r0 = r2->field_13
    //     0xa50a3c: ldur            w0, [x2, #0x13]
    // 0xa50a40: DecompressPointer r0
    //     0xa50a40: add             x0, x0, HEAP, lsl #32
    // 0xa50a44: cmp             w0, NULL
    // 0xa50a48: b.ne            #0xa50a54
    // 0xa50a4c: r0 = Null
    //     0xa50a4c: mov             x0, NULL
    // 0xa50a50: b               #0xa50a60
    // 0xa50a54: LoadField: r1 = r0->field_7f
    //     0xa50a54: ldur            w1, [x0, #0x7f]
    // 0xa50a58: DecompressPointer r1
    //     0xa50a58: add             x1, x1, HEAP, lsl #32
    // 0xa50a5c: mov             x0, x1
    // 0xa50a60: cmp             w0, NULL
    // 0xa50a64: b.ne            #0xa50a70
    // 0xa50a68: r4 = ""
    //     0xa50a68: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa50a6c: b               #0xa50a74
    // 0xa50a70: mov             x4, x0
    // 0xa50a74: ldur            x3, [fp, #-8]
    // 0xa50a78: ldur            d0, [fp, #-0x70]
    // 0xa50a7c: ldur            x0, [fp, #-0x18]
    // 0xa50a80: stur            x4, [fp, #-0x50]
    // 0xa50a84: LoadField: r1 = r3->field_f
    //     0xa50a84: ldur            w1, [x3, #0xf]
    // 0xa50a88: DecompressPointer r1
    //     0xa50a88: add             x1, x1, HEAP, lsl #32
    // 0xa50a8c: cmp             w1, NULL
    // 0xa50a90: b.eq            #0xa51320
    // 0xa50a94: r0 = of()
    //     0xa50a94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa50a98: LoadField: r1 = r0->field_87
    //     0xa50a98: ldur            w1, [x0, #0x87]
    // 0xa50a9c: DecompressPointer r1
    //     0xa50a9c: add             x1, x1, HEAP, lsl #32
    // 0xa50aa0: LoadField: r0 = r1->field_2b
    //     0xa50aa0: ldur            w0, [x1, #0x2b]
    // 0xa50aa4: DecompressPointer r0
    //     0xa50aa4: add             x0, x0, HEAP, lsl #32
    // 0xa50aa8: r16 = 12.000000
    //     0xa50aa8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa50aac: ldr             x16, [x16, #0x9e8]
    // 0xa50ab0: r30 = Instance_Color
    //     0xa50ab0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa50ab4: stp             lr, x16, [SP]
    // 0xa50ab8: mov             x1, x0
    // 0xa50abc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa50abc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa50ac0: ldr             x4, [x4, #0xaa0]
    // 0xa50ac4: r0 = copyWith()
    //     0xa50ac4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa50ac8: stur            x0, [fp, #-0x58]
    // 0xa50acc: r0 = Text()
    //     0xa50acc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa50ad0: mov             x1, x0
    // 0xa50ad4: ldur            x0, [fp, #-0x50]
    // 0xa50ad8: stur            x1, [fp, #-0x60]
    // 0xa50adc: StoreField: r1->field_b = r0
    //     0xa50adc: stur            w0, [x1, #0xb]
    // 0xa50ae0: ldur            x0, [fp, #-0x58]
    // 0xa50ae4: StoreField: r1->field_13 = r0
    //     0xa50ae4: stur            w0, [x1, #0x13]
    // 0xa50ae8: r0 = Padding()
    //     0xa50ae8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa50aec: mov             x3, x0
    // 0xa50af0: r0 = Instance_EdgeInsets
    //     0xa50af0: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xa50af4: ldr             x0, [x0, #0xc40]
    // 0xa50af8: stur            x3, [fp, #-0x50]
    // 0xa50afc: StoreField: r3->field_f = r0
    //     0xa50afc: stur            w0, [x3, #0xf]
    // 0xa50b00: ldur            x0, [fp, #-0x60]
    // 0xa50b04: StoreField: r3->field_b = r0
    //     0xa50b04: stur            w0, [x3, #0xb]
    // 0xa50b08: r1 = Null
    //     0xa50b08: mov             x1, NULL
    // 0xa50b0c: r2 = 4
    //     0xa50b0c: movz            x2, #0x4
    // 0xa50b10: r0 = AllocateArray()
    //     0xa50b10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa50b14: mov             x2, x0
    // 0xa50b18: ldur            x0, [fp, #-0x18]
    // 0xa50b1c: stur            x2, [fp, #-0x58]
    // 0xa50b20: StoreField: r2->field_f = r0
    //     0xa50b20: stur            w0, [x2, #0xf]
    // 0xa50b24: ldur            x0, [fp, #-0x50]
    // 0xa50b28: StoreField: r2->field_13 = r0
    //     0xa50b28: stur            w0, [x2, #0x13]
    // 0xa50b2c: r1 = <Widget>
    //     0xa50b2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa50b30: r0 = AllocateGrowableArray()
    //     0xa50b30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa50b34: mov             x1, x0
    // 0xa50b38: ldur            x0, [fp, #-0x58]
    // 0xa50b3c: stur            x1, [fp, #-0x18]
    // 0xa50b40: StoreField: r1->field_f = r0
    //     0xa50b40: stur            w0, [x1, #0xf]
    // 0xa50b44: r0 = 4
    //     0xa50b44: movz            x0, #0x4
    // 0xa50b48: StoreField: r1->field_b = r0
    //     0xa50b48: stur            w0, [x1, #0xb]
    // 0xa50b4c: r0 = Row()
    //     0xa50b4c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa50b50: mov             x1, x0
    // 0xa50b54: r0 = Instance_Axis
    //     0xa50b54: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa50b58: stur            x1, [fp, #-0x50]
    // 0xa50b5c: StoreField: r1->field_f = r0
    //     0xa50b5c: stur            w0, [x1, #0xf]
    // 0xa50b60: r0 = Instance_MainAxisAlignment
    //     0xa50b60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa50b64: ldr             x0, [x0, #0xa08]
    // 0xa50b68: StoreField: r1->field_13 = r0
    //     0xa50b68: stur            w0, [x1, #0x13]
    // 0xa50b6c: r0 = Instance_MainAxisSize
    //     0xa50b6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa50b70: ldr             x0, [x0, #0xa10]
    // 0xa50b74: ArrayStore: r1[0] = r0  ; List_4
    //     0xa50b74: stur            w0, [x1, #0x17]
    // 0xa50b78: r0 = Instance_CrossAxisAlignment
    //     0xa50b78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa50b7c: ldr             x0, [x0, #0xa18]
    // 0xa50b80: StoreField: r1->field_1b = r0
    //     0xa50b80: stur            w0, [x1, #0x1b]
    // 0xa50b84: r0 = Instance_VerticalDirection
    //     0xa50b84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa50b88: ldr             x0, [x0, #0xa20]
    // 0xa50b8c: StoreField: r1->field_23 = r0
    //     0xa50b8c: stur            w0, [x1, #0x23]
    // 0xa50b90: r0 = Instance_Clip
    //     0xa50b90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa50b94: ldr             x0, [x0, #0x38]
    // 0xa50b98: StoreField: r1->field_2b = r0
    //     0xa50b98: stur            w0, [x1, #0x2b]
    // 0xa50b9c: StoreField: r1->field_2f = rZR
    //     0xa50b9c: stur            xzr, [x1, #0x2f]
    // 0xa50ba0: ldur            x0, [fp, #-0x18]
    // 0xa50ba4: StoreField: r1->field_b = r0
    //     0xa50ba4: stur            w0, [x1, #0xb]
    // 0xa50ba8: ldur            d0, [fp, #-0x70]
    // 0xa50bac: r0 = inline_Allocate_Double()
    //     0xa50bac: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa50bb0: add             x0, x0, #0x10
    //     0xa50bb4: cmp             x2, x0
    //     0xa50bb8: b.ls            #0xa51324
    //     0xa50bbc: str             x0, [THR, #0x50]  ; THR::top
    //     0xa50bc0: sub             x0, x0, #0xf
    //     0xa50bc4: movz            x2, #0xe15c
    //     0xa50bc8: movk            x2, #0x3, lsl #16
    //     0xa50bcc: stur            x2, [x0, #-1]
    // 0xa50bd0: StoreField: r0->field_7 = d0
    //     0xa50bd0: stur            d0, [x0, #7]
    // 0xa50bd4: stur            x0, [fp, #-0x18]
    // 0xa50bd8: r0 = Container()
    //     0xa50bd8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa50bdc: stur            x0, [fp, #-0x58]
    // 0xa50be0: r16 = 20.000000
    //     0xa50be0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa50be4: ldr             x16, [x16, #0xac8]
    // 0xa50be8: ldur            lr, [fp, #-0x18]
    // 0xa50bec: stp             lr, x16, [SP, #0x10]
    // 0xa50bf0: ldur            x16, [fp, #-0x30]
    // 0xa50bf4: ldur            lr, [fp, #-0x50]
    // 0xa50bf8: stp             lr, x16, [SP]
    // 0xa50bfc: mov             x1, x0
    // 0xa50c00: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa50c00: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa50c04: ldr             x4, [x4, #0x8c0]
    // 0xa50c08: r0 = Container()
    //     0xa50c08: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa50c0c: r0 = Padding()
    //     0xa50c0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa50c10: mov             x1, x0
    // 0xa50c14: r0 = Instance_EdgeInsets
    //     0xa50c14: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xa50c18: ldr             x0, [x0, #0xf70]
    // 0xa50c1c: stur            x1, [fp, #-0x18]
    // 0xa50c20: StoreField: r1->field_f = r0
    //     0xa50c20: stur            w0, [x1, #0xf]
    // 0xa50c24: ldur            x0, [fp, #-0x58]
    // 0xa50c28: StoreField: r1->field_b = r0
    //     0xa50c28: stur            w0, [x1, #0xb]
    // 0xa50c2c: r0 = Align()
    //     0xa50c2c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa50c30: mov             x1, x0
    // 0xa50c34: r0 = Instance_Alignment
    //     0xa50c34: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa50c38: ldr             x0, [x0, #0xfa0]
    // 0xa50c3c: StoreField: r1->field_f = r0
    //     0xa50c3c: stur            w0, [x1, #0xf]
    // 0xa50c40: ldur            x0, [fp, #-0x18]
    // 0xa50c44: StoreField: r1->field_b = r0
    //     0xa50c44: stur            w0, [x1, #0xb]
    // 0xa50c48: ldur            x2, [fp, #-0x20]
    // 0xa50c4c: ldur            x0, [fp, #-0x10]
    // 0xa50c50: stur            x1, [fp, #-0x18]
    // 0xa50c54: r0 = Visibility()
    //     0xa50c54: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa50c58: mov             x1, x0
    // 0xa50c5c: ldur            x0, [fp, #-0x18]
    // 0xa50c60: stur            x1, [fp, #-0x30]
    // 0xa50c64: StoreField: r1->field_b = r0
    //     0xa50c64: stur            w0, [x1, #0xb]
    // 0xa50c68: r0 = Instance_SizedBox
    //     0xa50c68: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa50c6c: StoreField: r1->field_f = r0
    //     0xa50c6c: stur            w0, [x1, #0xf]
    // 0xa50c70: ldur            x2, [fp, #-0x10]
    // 0xa50c74: StoreField: r1->field_13 = r2
    //     0xa50c74: stur            w2, [x1, #0x13]
    // 0xa50c78: r2 = false
    //     0xa50c78: add             x2, NULL, #0x30  ; false
    // 0xa50c7c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa50c7c: stur            w2, [x1, #0x17]
    // 0xa50c80: StoreField: r1->field_1b = r2
    //     0xa50c80: stur            w2, [x1, #0x1b]
    // 0xa50c84: StoreField: r1->field_1f = r2
    //     0xa50c84: stur            w2, [x1, #0x1f]
    // 0xa50c88: StoreField: r1->field_23 = r2
    //     0xa50c88: stur            w2, [x1, #0x23]
    // 0xa50c8c: StoreField: r1->field_27 = r2
    //     0xa50c8c: stur            w2, [x1, #0x27]
    // 0xa50c90: StoreField: r1->field_2b = r2
    //     0xa50c90: stur            w2, [x1, #0x2b]
    // 0xa50c94: ldur            x3, [fp, #-0x20]
    // 0xa50c98: LoadField: r4 = r3->field_13
    //     0xa50c98: ldur            w4, [x3, #0x13]
    // 0xa50c9c: DecompressPointer r4
    //     0xa50c9c: add             x4, x4, HEAP, lsl #32
    // 0xa50ca0: cmp             w4, NULL
    // 0xa50ca4: b.ne            #0xa50cb0
    // 0xa50ca8: r5 = Null
    //     0xa50ca8: mov             x5, NULL
    // 0xa50cac: b               #0xa50cdc
    // 0xa50cb0: LoadField: r5 = r4->field_b7
    //     0xa50cb0: ldur            w5, [x4, #0xb7]
    // 0xa50cb4: DecompressPointer r5
    //     0xa50cb4: add             x5, x5, HEAP, lsl #32
    // 0xa50cb8: cmp             w5, NULL
    // 0xa50cbc: b.ne            #0xa50cc8
    // 0xa50cc0: r5 = Null
    //     0xa50cc0: mov             x5, NULL
    // 0xa50cc4: b               #0xa50cdc
    // 0xa50cc8: LoadField: r6 = r5->field_7
    //     0xa50cc8: ldur            w6, [x5, #7]
    // 0xa50ccc: cbnz            w6, #0xa50cd8
    // 0xa50cd0: r5 = false
    //     0xa50cd0: add             x5, NULL, #0x30  ; false
    // 0xa50cd4: b               #0xa50cdc
    // 0xa50cd8: r5 = true
    //     0xa50cd8: add             x5, NULL, #0x20  ; true
    // 0xa50cdc: cmp             w5, NULL
    // 0xa50ce0: b.ne            #0xa50ce8
    // 0xa50ce4: r5 = false
    //     0xa50ce4: add             x5, NULL, #0x30  ; false
    // 0xa50ce8: stur            x5, [fp, #-0x10]
    // 0xa50cec: cmp             w4, NULL
    // 0xa50cf0: b.ne            #0xa50cfc
    // 0xa50cf4: r4 = Null
    //     0xa50cf4: mov             x4, NULL
    // 0xa50cf8: b               #0xa50d08
    // 0xa50cfc: LoadField: r6 = r4->field_8b
    //     0xa50cfc: ldur            w6, [x4, #0x8b]
    // 0xa50d00: DecompressPointer r6
    //     0xa50d00: add             x6, x6, HEAP, lsl #32
    // 0xa50d04: mov             x4, x6
    // 0xa50d08: cmp             w4, NULL
    // 0xa50d0c: b.eq            #0xa50d20
    // 0xa50d10: tbnz            w4, #4, #0xa50d20
    // 0xa50d14: d0 = 38.000000
    //     0xa50d14: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xa50d18: ldr             d0, [x17, #0xd10]
    // 0xa50d1c: b               #0xa50d24
    // 0xa50d20: d0 = 4.000000
    //     0xa50d20: fmov            d0, #4.00000000
    // 0xa50d24: stur            d0, [fp, #-0x70]
    // 0xa50d28: r0 = EdgeInsets()
    //     0xa50d28: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa50d2c: d0 = 8.000000
    //     0xa50d2c: fmov            d0, #8.00000000
    // 0xa50d30: stur            x0, [fp, #-0x18]
    // 0xa50d34: StoreField: r0->field_7 = d0
    //     0xa50d34: stur            d0, [x0, #7]
    // 0xa50d38: StoreField: r0->field_f = rZR
    //     0xa50d38: stur            xzr, [x0, #0xf]
    // 0xa50d3c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa50d3c: stur            xzr, [x0, #0x17]
    // 0xa50d40: ldur            d0, [fp, #-0x70]
    // 0xa50d44: StoreField: r0->field_1f = d0
    //     0xa50d44: stur            d0, [x0, #0x1f]
    // 0xa50d48: r16 = <EdgeInsets>
    //     0xa50d48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa50d4c: ldr             x16, [x16, #0xda0]
    // 0xa50d50: r30 = Instance_EdgeInsets
    //     0xa50d50: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xa50d54: ldr             lr, [lr, #0x668]
    // 0xa50d58: stp             lr, x16, [SP]
    // 0xa50d5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa50d5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa50d60: r0 = all()
    //     0xa50d60: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa50d64: stur            x0, [fp, #-0x50]
    // 0xa50d68: r16 = <Color>
    //     0xa50d68: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa50d6c: ldr             x16, [x16, #0xf80]
    // 0xa50d70: r30 = Instance_Color
    //     0xa50d70: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa50d74: stp             lr, x16, [SP]
    // 0xa50d78: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa50d78: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa50d7c: r0 = all()
    //     0xa50d7c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa50d80: stur            x0, [fp, #-0x58]
    // 0xa50d84: r0 = Radius()
    //     0xa50d84: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa50d88: d0 = 5.000000
    //     0xa50d88: fmov            d0, #5.00000000
    // 0xa50d8c: stur            x0, [fp, #-0x60]
    // 0xa50d90: StoreField: r0->field_7 = d0
    //     0xa50d90: stur            d0, [x0, #7]
    // 0xa50d94: StoreField: r0->field_f = d0
    //     0xa50d94: stur            d0, [x0, #0xf]
    // 0xa50d98: r0 = BorderRadius()
    //     0xa50d98: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa50d9c: mov             x1, x0
    // 0xa50da0: ldur            x0, [fp, #-0x60]
    // 0xa50da4: stur            x1, [fp, #-0x68]
    // 0xa50da8: StoreField: r1->field_7 = r0
    //     0xa50da8: stur            w0, [x1, #7]
    // 0xa50dac: StoreField: r1->field_b = r0
    //     0xa50dac: stur            w0, [x1, #0xb]
    // 0xa50db0: StoreField: r1->field_f = r0
    //     0xa50db0: stur            w0, [x1, #0xf]
    // 0xa50db4: StoreField: r1->field_13 = r0
    //     0xa50db4: stur            w0, [x1, #0x13]
    // 0xa50db8: r0 = RoundedRectangleBorder()
    //     0xa50db8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa50dbc: mov             x1, x0
    // 0xa50dc0: ldur            x0, [fp, #-0x68]
    // 0xa50dc4: StoreField: r1->field_b = r0
    //     0xa50dc4: stur            w0, [x1, #0xb]
    // 0xa50dc8: r0 = Instance_BorderSide
    //     0xa50dc8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa50dcc: ldr             x0, [x0, #0xe20]
    // 0xa50dd0: StoreField: r1->field_7 = r0
    //     0xa50dd0: stur            w0, [x1, #7]
    // 0xa50dd4: r16 = <RoundedRectangleBorder>
    //     0xa50dd4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa50dd8: ldr             x16, [x16, #0xf78]
    // 0xa50ddc: stp             x1, x16, [SP]
    // 0xa50de0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa50de0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa50de4: r0 = all()
    //     0xa50de4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa50de8: stur            x0, [fp, #-0x60]
    // 0xa50dec: r0 = ButtonStyle()
    //     0xa50dec: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa50df0: mov             x1, x0
    // 0xa50df4: ldur            x0, [fp, #-0x58]
    // 0xa50df8: stur            x1, [fp, #-0x68]
    // 0xa50dfc: StoreField: r1->field_b = r0
    //     0xa50dfc: stur            w0, [x1, #0xb]
    // 0xa50e00: ldur            x0, [fp, #-0x50]
    // 0xa50e04: StoreField: r1->field_23 = r0
    //     0xa50e04: stur            w0, [x1, #0x23]
    // 0xa50e08: ldur            x0, [fp, #-0x60]
    // 0xa50e0c: StoreField: r1->field_43 = r0
    //     0xa50e0c: stur            w0, [x1, #0x43]
    // 0xa50e10: r0 = TextButtonThemeData()
    //     0xa50e10: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa50e14: mov             x1, x0
    // 0xa50e18: ldur            x0, [fp, #-0x68]
    // 0xa50e1c: stur            x1, [fp, #-0x50]
    // 0xa50e20: StoreField: r1->field_7 = r0
    //     0xa50e20: stur            w0, [x1, #7]
    // 0xa50e24: ldur            x2, [fp, #-0x20]
    // 0xa50e28: LoadField: r0 = r2->field_13
    //     0xa50e28: ldur            w0, [x2, #0x13]
    // 0xa50e2c: DecompressPointer r0
    //     0xa50e2c: add             x0, x0, HEAP, lsl #32
    // 0xa50e30: cmp             w0, NULL
    // 0xa50e34: b.ne            #0xa50e40
    // 0xa50e38: r5 = Null
    //     0xa50e38: mov             x5, NULL
    // 0xa50e3c: b               #0xa50e4c
    // 0xa50e40: LoadField: r3 = r0->field_b7
    //     0xa50e40: ldur            w3, [x0, #0xb7]
    // 0xa50e44: DecompressPointer r3
    //     0xa50e44: add             x3, x3, HEAP, lsl #32
    // 0xa50e48: mov             x5, x3
    // 0xa50e4c: ldur            x4, [fp, #-8]
    // 0xa50e50: ldur            x3, [fp, #-0x10]
    // 0xa50e54: ldur            x0, [fp, #-0x18]
    // 0xa50e58: str             x5, [SP]
    // 0xa50e5c: r0 = _interpolateSingle()
    //     0xa50e5c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa50e60: mov             x2, x0
    // 0xa50e64: ldur            x0, [fp, #-8]
    // 0xa50e68: stur            x2, [fp, #-0x58]
    // 0xa50e6c: LoadField: r1 = r0->field_f
    //     0xa50e6c: ldur            w1, [x0, #0xf]
    // 0xa50e70: DecompressPointer r1
    //     0xa50e70: add             x1, x1, HEAP, lsl #32
    // 0xa50e74: cmp             w1, NULL
    // 0xa50e78: b.eq            #0xa5133c
    // 0xa50e7c: r0 = of()
    //     0xa50e7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa50e80: LoadField: r1 = r0->field_87
    //     0xa50e80: ldur            w1, [x0, #0x87]
    // 0xa50e84: DecompressPointer r1
    //     0xa50e84: add             x1, x1, HEAP, lsl #32
    // 0xa50e88: LoadField: r0 = r1->field_2b
    //     0xa50e88: ldur            w0, [x1, #0x2b]
    // 0xa50e8c: DecompressPointer r0
    //     0xa50e8c: add             x0, x0, HEAP, lsl #32
    // 0xa50e90: r16 = 12.000000
    //     0xa50e90: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa50e94: ldr             x16, [x16, #0x9e8]
    // 0xa50e98: r30 = Instance_Color
    //     0xa50e98: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa50e9c: stp             lr, x16, [SP]
    // 0xa50ea0: mov             x1, x0
    // 0xa50ea4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa50ea4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa50ea8: ldr             x4, [x4, #0xaa0]
    // 0xa50eac: r0 = copyWith()
    //     0xa50eac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa50eb0: stur            x0, [fp, #-0x60]
    // 0xa50eb4: r0 = Text()
    //     0xa50eb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa50eb8: mov             x3, x0
    // 0xa50ebc: ldur            x0, [fp, #-0x58]
    // 0xa50ec0: stur            x3, [fp, #-0x68]
    // 0xa50ec4: StoreField: r3->field_b = r0
    //     0xa50ec4: stur            w0, [x3, #0xb]
    // 0xa50ec8: ldur            x0, [fp, #-0x60]
    // 0xa50ecc: StoreField: r3->field_13 = r0
    //     0xa50ecc: stur            w0, [x3, #0x13]
    // 0xa50ed0: r1 = Function '<anonymous closure>':.
    //     0xa50ed0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab18] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa50ed4: ldr             x1, [x1, #0xb18]
    // 0xa50ed8: r2 = Null
    //     0xa50ed8: mov             x2, NULL
    // 0xa50edc: r0 = AllocateClosure()
    //     0xa50edc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa50ee0: stur            x0, [fp, #-0x58]
    // 0xa50ee4: r0 = TextButton()
    //     0xa50ee4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa50ee8: mov             x1, x0
    // 0xa50eec: ldur            x0, [fp, #-0x58]
    // 0xa50ef0: stur            x1, [fp, #-0x60]
    // 0xa50ef4: StoreField: r1->field_b = r0
    //     0xa50ef4: stur            w0, [x1, #0xb]
    // 0xa50ef8: r0 = false
    //     0xa50ef8: add             x0, NULL, #0x30  ; false
    // 0xa50efc: StoreField: r1->field_27 = r0
    //     0xa50efc: stur            w0, [x1, #0x27]
    // 0xa50f00: r2 = true
    //     0xa50f00: add             x2, NULL, #0x20  ; true
    // 0xa50f04: StoreField: r1->field_2f = r2
    //     0xa50f04: stur            w2, [x1, #0x2f]
    // 0xa50f08: ldur            x3, [fp, #-0x68]
    // 0xa50f0c: StoreField: r1->field_37 = r3
    //     0xa50f0c: stur            w3, [x1, #0x37]
    // 0xa50f10: r0 = TextButtonTheme()
    //     0xa50f10: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa50f14: mov             x1, x0
    // 0xa50f18: ldur            x0, [fp, #-0x50]
    // 0xa50f1c: stur            x1, [fp, #-0x58]
    // 0xa50f20: StoreField: r1->field_f = r0
    //     0xa50f20: stur            w0, [x1, #0xf]
    // 0xa50f24: ldur            x0, [fp, #-0x60]
    // 0xa50f28: StoreField: r1->field_b = r0
    //     0xa50f28: stur            w0, [x1, #0xb]
    // 0xa50f2c: r0 = Padding()
    //     0xa50f2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa50f30: mov             x1, x0
    // 0xa50f34: ldur            x0, [fp, #-0x18]
    // 0xa50f38: stur            x1, [fp, #-0x50]
    // 0xa50f3c: StoreField: r1->field_f = r0
    //     0xa50f3c: stur            w0, [x1, #0xf]
    // 0xa50f40: ldur            x0, [fp, #-0x58]
    // 0xa50f44: StoreField: r1->field_b = r0
    //     0xa50f44: stur            w0, [x1, #0xb]
    // 0xa50f48: r0 = Visibility()
    //     0xa50f48: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa50f4c: mov             x2, x0
    // 0xa50f50: ldur            x0, [fp, #-0x50]
    // 0xa50f54: stur            x2, [fp, #-0x18]
    // 0xa50f58: StoreField: r2->field_b = r0
    //     0xa50f58: stur            w0, [x2, #0xb]
    // 0xa50f5c: r0 = Instance_SizedBox
    //     0xa50f5c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa50f60: StoreField: r2->field_f = r0
    //     0xa50f60: stur            w0, [x2, #0xf]
    // 0xa50f64: ldur            x1, [fp, #-0x10]
    // 0xa50f68: StoreField: r2->field_13 = r1
    //     0xa50f68: stur            w1, [x2, #0x13]
    // 0xa50f6c: r3 = false
    //     0xa50f6c: add             x3, NULL, #0x30  ; false
    // 0xa50f70: ArrayStore: r2[0] = r3  ; List_4
    //     0xa50f70: stur            w3, [x2, #0x17]
    // 0xa50f74: StoreField: r2->field_1b = r3
    //     0xa50f74: stur            w3, [x2, #0x1b]
    // 0xa50f78: StoreField: r2->field_1f = r3
    //     0xa50f78: stur            w3, [x2, #0x1f]
    // 0xa50f7c: StoreField: r2->field_23 = r3
    //     0xa50f7c: stur            w3, [x2, #0x23]
    // 0xa50f80: StoreField: r2->field_27 = r3
    //     0xa50f80: stur            w3, [x2, #0x27]
    // 0xa50f84: StoreField: r2->field_2b = r3
    //     0xa50f84: stur            w3, [x2, #0x2b]
    // 0xa50f88: ldur            x4, [fp, #-0x20]
    // 0xa50f8c: LoadField: r1 = r4->field_13
    //     0xa50f8c: ldur            w1, [x4, #0x13]
    // 0xa50f90: DecompressPointer r1
    //     0xa50f90: add             x1, x1, HEAP, lsl #32
    // 0xa50f94: cmp             w1, NULL
    // 0xa50f98: b.ne            #0xa50fa4
    // 0xa50f9c: r1 = Null
    //     0xa50f9c: mov             x1, NULL
    // 0xa50fa0: b               #0xa50fb0
    // 0xa50fa4: LoadField: r5 = r1->field_8b
    //     0xa50fa4: ldur            w5, [x1, #0x8b]
    // 0xa50fa8: DecompressPointer r5
    //     0xa50fa8: add             x5, x5, HEAP, lsl #32
    // 0xa50fac: mov             x1, x5
    // 0xa50fb0: cmp             w1, NULL
    // 0xa50fb4: b.ne            #0xa50fc0
    // 0xa50fb8: r8 = false
    //     0xa50fb8: add             x8, NULL, #0x30  ; false
    // 0xa50fbc: b               #0xa50fc4
    // 0xa50fc0: mov             x8, x1
    // 0xa50fc4: ldur            x5, [fp, #-8]
    // 0xa50fc8: ldur            x7, [fp, #-0x28]
    // 0xa50fcc: ldur            x6, [fp, #-0x30]
    // 0xa50fd0: stur            x8, [fp, #-0x10]
    // 0xa50fd4: LoadField: r1 = r5->field_f
    //     0xa50fd4: ldur            w1, [x5, #0xf]
    // 0xa50fd8: DecompressPointer r1
    //     0xa50fd8: add             x1, x1, HEAP, lsl #32
    // 0xa50fdc: cmp             w1, NULL
    // 0xa50fe0: b.eq            #0xa51340
    // 0xa50fe4: r0 = of()
    //     0xa50fe4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa50fe8: LoadField: r1 = r0->field_5b
    //     0xa50fe8: ldur            w1, [x0, #0x5b]
    // 0xa50fec: DecompressPointer r1
    //     0xa50fec: add             x1, x1, HEAP, lsl #32
    // 0xa50ff0: r16 = <Color>
    //     0xa50ff0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa50ff4: ldr             x16, [x16, #0xf80]
    // 0xa50ff8: stp             x1, x16, [SP]
    // 0xa50ffc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa50ffc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa51000: r0 = all()
    //     0xa51000: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa51004: stur            x0, [fp, #-0x50]
    // 0xa51008: r0 = Radius()
    //     0xa51008: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa5100c: d0 = 5.000000
    //     0xa5100c: fmov            d0, #5.00000000
    // 0xa51010: stur            x0, [fp, #-0x58]
    // 0xa51014: StoreField: r0->field_7 = d0
    //     0xa51014: stur            d0, [x0, #7]
    // 0xa51018: StoreField: r0->field_f = d0
    //     0xa51018: stur            d0, [x0, #0xf]
    // 0xa5101c: r0 = BorderRadius()
    //     0xa5101c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa51020: mov             x1, x0
    // 0xa51024: ldur            x0, [fp, #-0x58]
    // 0xa51028: stur            x1, [fp, #-0x60]
    // 0xa5102c: StoreField: r1->field_7 = r0
    //     0xa5102c: stur            w0, [x1, #7]
    // 0xa51030: StoreField: r1->field_b = r0
    //     0xa51030: stur            w0, [x1, #0xb]
    // 0xa51034: StoreField: r1->field_f = r0
    //     0xa51034: stur            w0, [x1, #0xf]
    // 0xa51038: StoreField: r1->field_13 = r0
    //     0xa51038: stur            w0, [x1, #0x13]
    // 0xa5103c: r0 = RoundedRectangleBorder()
    //     0xa5103c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa51040: mov             x1, x0
    // 0xa51044: ldur            x0, [fp, #-0x60]
    // 0xa51048: StoreField: r1->field_b = r0
    //     0xa51048: stur            w0, [x1, #0xb]
    // 0xa5104c: r0 = Instance_BorderSide
    //     0xa5104c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa51050: ldr             x0, [x0, #0xe20]
    // 0xa51054: StoreField: r1->field_7 = r0
    //     0xa51054: stur            w0, [x1, #7]
    // 0xa51058: r16 = <RoundedRectangleBorder>
    //     0xa51058: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa5105c: ldr             x16, [x16, #0xf78]
    // 0xa51060: stp             x1, x16, [SP]
    // 0xa51064: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa51064: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa51068: r0 = all()
    //     0xa51068: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa5106c: stur            x0, [fp, #-0x58]
    // 0xa51070: r0 = ButtonStyle()
    //     0xa51070: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa51074: mov             x1, x0
    // 0xa51078: ldur            x0, [fp, #-0x50]
    // 0xa5107c: stur            x1, [fp, #-0x60]
    // 0xa51080: StoreField: r1->field_b = r0
    //     0xa51080: stur            w0, [x1, #0xb]
    // 0xa51084: ldur            x0, [fp, #-0x58]
    // 0xa51088: StoreField: r1->field_43 = r0
    //     0xa51088: stur            w0, [x1, #0x43]
    // 0xa5108c: r0 = TextButtonThemeData()
    //     0xa5108c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa51090: mov             x2, x0
    // 0xa51094: ldur            x0, [fp, #-0x60]
    // 0xa51098: stur            x2, [fp, #-0x50]
    // 0xa5109c: StoreField: r2->field_7 = r0
    //     0xa5109c: stur            w0, [x2, #7]
    // 0xa510a0: ldur            x0, [fp, #-8]
    // 0xa510a4: LoadField: r1 = r0->field_f
    //     0xa510a4: ldur            w1, [x0, #0xf]
    // 0xa510a8: DecompressPointer r1
    //     0xa510a8: add             x1, x1, HEAP, lsl #32
    // 0xa510ac: cmp             w1, NULL
    // 0xa510b0: b.eq            #0xa51344
    // 0xa510b4: r0 = of()
    //     0xa510b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa510b8: LoadField: r1 = r0->field_87
    //     0xa510b8: ldur            w1, [x0, #0x87]
    // 0xa510bc: DecompressPointer r1
    //     0xa510bc: add             x1, x1, HEAP, lsl #32
    // 0xa510c0: LoadField: r0 = r1->field_2b
    //     0xa510c0: ldur            w0, [x1, #0x2b]
    // 0xa510c4: DecompressPointer r0
    //     0xa510c4: add             x0, x0, HEAP, lsl #32
    // 0xa510c8: r16 = 12.000000
    //     0xa510c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa510cc: ldr             x16, [x16, #0x9e8]
    // 0xa510d0: r30 = Instance_Color
    //     0xa510d0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa510d4: stp             lr, x16, [SP]
    // 0xa510d8: mov             x1, x0
    // 0xa510dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa510dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa510e0: ldr             x4, [x4, #0xaa0]
    // 0xa510e4: r0 = copyWith()
    //     0xa510e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa510e8: stur            x0, [fp, #-8]
    // 0xa510ec: r0 = Text()
    //     0xa510ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa510f0: mov             x3, x0
    // 0xa510f4: r0 = "Customisable"
    //     0xa510f4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xa510f8: ldr             x0, [x0, #0x970]
    // 0xa510fc: stur            x3, [fp, #-0x58]
    // 0xa51100: StoreField: r3->field_b = r0
    //     0xa51100: stur            w0, [x3, #0xb]
    // 0xa51104: ldur            x0, [fp, #-8]
    // 0xa51108: StoreField: r3->field_13 = r0
    //     0xa51108: stur            w0, [x3, #0x13]
    // 0xa5110c: r1 = Function '<anonymous closure>':.
    //     0xa5110c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab20] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa51110: ldr             x1, [x1, #0xb20]
    // 0xa51114: r2 = Null
    //     0xa51114: mov             x2, NULL
    // 0xa51118: r0 = AllocateClosure()
    //     0xa51118: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5111c: stur            x0, [fp, #-8]
    // 0xa51120: r0 = TextButton()
    //     0xa51120: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa51124: mov             x1, x0
    // 0xa51128: ldur            x0, [fp, #-8]
    // 0xa5112c: stur            x1, [fp, #-0x60]
    // 0xa51130: StoreField: r1->field_b = r0
    //     0xa51130: stur            w0, [x1, #0xb]
    // 0xa51134: r0 = false
    //     0xa51134: add             x0, NULL, #0x30  ; false
    // 0xa51138: StoreField: r1->field_27 = r0
    //     0xa51138: stur            w0, [x1, #0x27]
    // 0xa5113c: r2 = true
    //     0xa5113c: add             x2, NULL, #0x20  ; true
    // 0xa51140: StoreField: r1->field_2f = r2
    //     0xa51140: stur            w2, [x1, #0x2f]
    // 0xa51144: ldur            x3, [fp, #-0x58]
    // 0xa51148: StoreField: r1->field_37 = r3
    //     0xa51148: stur            w3, [x1, #0x37]
    // 0xa5114c: r0 = TextButtonTheme()
    //     0xa5114c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa51150: mov             x1, x0
    // 0xa51154: ldur            x0, [fp, #-0x50]
    // 0xa51158: stur            x1, [fp, #-8]
    // 0xa5115c: StoreField: r1->field_f = r0
    //     0xa5115c: stur            w0, [x1, #0xf]
    // 0xa51160: ldur            x0, [fp, #-0x60]
    // 0xa51164: StoreField: r1->field_b = r0
    //     0xa51164: stur            w0, [x1, #0xb]
    // 0xa51168: r0 = SizedBox()
    //     0xa51168: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa5116c: mov             x1, x0
    // 0xa51170: r0 = 30.000000
    //     0xa51170: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xa51174: ldr             x0, [x0, #0x768]
    // 0xa51178: stur            x1, [fp, #-0x50]
    // 0xa5117c: StoreField: r1->field_13 = r0
    //     0xa5117c: stur            w0, [x1, #0x13]
    // 0xa51180: ldur            x0, [fp, #-8]
    // 0xa51184: StoreField: r1->field_b = r0
    //     0xa51184: stur            w0, [x1, #0xb]
    // 0xa51188: r0 = Padding()
    //     0xa51188: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa5118c: mov             x1, x0
    // 0xa51190: r0 = Instance_EdgeInsets
    //     0xa51190: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xa51194: ldr             x0, [x0, #0xe68]
    // 0xa51198: stur            x1, [fp, #-8]
    // 0xa5119c: StoreField: r1->field_f = r0
    //     0xa5119c: stur            w0, [x1, #0xf]
    // 0xa511a0: ldur            x0, [fp, #-0x50]
    // 0xa511a4: StoreField: r1->field_b = r0
    //     0xa511a4: stur            w0, [x1, #0xb]
    // 0xa511a8: r0 = Visibility()
    //     0xa511a8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa511ac: mov             x3, x0
    // 0xa511b0: ldur            x0, [fp, #-8]
    // 0xa511b4: stur            x3, [fp, #-0x50]
    // 0xa511b8: StoreField: r3->field_b = r0
    //     0xa511b8: stur            w0, [x3, #0xb]
    // 0xa511bc: r0 = Instance_SizedBox
    //     0xa511bc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa511c0: StoreField: r3->field_f = r0
    //     0xa511c0: stur            w0, [x3, #0xf]
    // 0xa511c4: ldur            x0, [fp, #-0x10]
    // 0xa511c8: StoreField: r3->field_13 = r0
    //     0xa511c8: stur            w0, [x3, #0x13]
    // 0xa511cc: r0 = false
    //     0xa511cc: add             x0, NULL, #0x30  ; false
    // 0xa511d0: ArrayStore: r3[0] = r0  ; List_4
    //     0xa511d0: stur            w0, [x3, #0x17]
    // 0xa511d4: StoreField: r3->field_1b = r0
    //     0xa511d4: stur            w0, [x3, #0x1b]
    // 0xa511d8: StoreField: r3->field_1f = r0
    //     0xa511d8: stur            w0, [x3, #0x1f]
    // 0xa511dc: StoreField: r3->field_23 = r0
    //     0xa511dc: stur            w0, [x3, #0x23]
    // 0xa511e0: StoreField: r3->field_27 = r0
    //     0xa511e0: stur            w0, [x3, #0x27]
    // 0xa511e4: StoreField: r3->field_2b = r0
    //     0xa511e4: stur            w0, [x3, #0x2b]
    // 0xa511e8: r1 = Null
    //     0xa511e8: mov             x1, NULL
    // 0xa511ec: r2 = 8
    //     0xa511ec: movz            x2, #0x8
    // 0xa511f0: r0 = AllocateArray()
    //     0xa511f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa511f4: mov             x2, x0
    // 0xa511f8: ldur            x0, [fp, #-0x28]
    // 0xa511fc: stur            x2, [fp, #-8]
    // 0xa51200: StoreField: r2->field_f = r0
    //     0xa51200: stur            w0, [x2, #0xf]
    // 0xa51204: ldur            x0, [fp, #-0x30]
    // 0xa51208: StoreField: r2->field_13 = r0
    //     0xa51208: stur            w0, [x2, #0x13]
    // 0xa5120c: ldur            x0, [fp, #-0x18]
    // 0xa51210: ArrayStore: r2[0] = r0  ; List_4
    //     0xa51210: stur            w0, [x2, #0x17]
    // 0xa51214: ldur            x0, [fp, #-0x50]
    // 0xa51218: StoreField: r2->field_1b = r0
    //     0xa51218: stur            w0, [x2, #0x1b]
    // 0xa5121c: r1 = <Widget>
    //     0xa5121c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa51220: r0 = AllocateGrowableArray()
    //     0xa51220: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa51224: mov             x1, x0
    // 0xa51228: ldur            x0, [fp, #-8]
    // 0xa5122c: stur            x1, [fp, #-0x10]
    // 0xa51230: StoreField: r1->field_f = r0
    //     0xa51230: stur            w0, [x1, #0xf]
    // 0xa51234: r0 = 8
    //     0xa51234: movz            x0, #0x8
    // 0xa51238: StoreField: r1->field_b = r0
    //     0xa51238: stur            w0, [x1, #0xb]
    // 0xa5123c: r0 = Stack()
    //     0xa5123c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa51240: mov             x1, x0
    // 0xa51244: r0 = Instance_Alignment
    //     0xa51244: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xa51248: ldr             x0, [x0, #0x5b8]
    // 0xa5124c: stur            x1, [fp, #-8]
    // 0xa51250: StoreField: r1->field_f = r0
    //     0xa51250: stur            w0, [x1, #0xf]
    // 0xa51254: r0 = Instance_StackFit
    //     0xa51254: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa51258: ldr             x0, [x0, #0xfa8]
    // 0xa5125c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5125c: stur            w0, [x1, #0x17]
    // 0xa51260: r0 = Instance_Clip
    //     0xa51260: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa51264: ldr             x0, [x0, #0x7e0]
    // 0xa51268: StoreField: r1->field_1b = r0
    //     0xa51268: stur            w0, [x1, #0x1b]
    // 0xa5126c: ldur            x0, [fp, #-0x10]
    // 0xa51270: StoreField: r1->field_b = r0
    //     0xa51270: stur            w0, [x1, #0xb]
    // 0xa51274: r0 = InkWell()
    //     0xa51274: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa51278: mov             x3, x0
    // 0xa5127c: ldur            x0, [fp, #-8]
    // 0xa51280: stur            x3, [fp, #-0x10]
    // 0xa51284: StoreField: r3->field_b = r0
    //     0xa51284: stur            w0, [x3, #0xb]
    // 0xa51288: ldur            x2, [fp, #-0x20]
    // 0xa5128c: r1 = Function '<anonymous closure>':.
    //     0xa5128c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab28] AnonymousClosure: (0xa51348), in [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::basicThemeSlider (0xa50220)
    //     0xa51290: ldr             x1, [x1, #0xb28]
    // 0xa51294: r0 = AllocateClosure()
    //     0xa51294: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa51298: ldur            x2, [fp, #-0x10]
    // 0xa5129c: StoreField: r2->field_f = r0
    //     0xa5129c: stur            w0, [x2, #0xf]
    // 0xa512a0: r0 = true
    //     0xa512a0: add             x0, NULL, #0x20  ; true
    // 0xa512a4: StoreField: r2->field_43 = r0
    //     0xa512a4: stur            w0, [x2, #0x43]
    // 0xa512a8: r1 = Instance_BoxShape
    //     0xa512a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa512ac: ldr             x1, [x1, #0x80]
    // 0xa512b0: StoreField: r2->field_47 = r1
    //     0xa512b0: stur            w1, [x2, #0x47]
    // 0xa512b4: StoreField: r2->field_6f = r0
    //     0xa512b4: stur            w0, [x2, #0x6f]
    // 0xa512b8: r1 = false
    //     0xa512b8: add             x1, NULL, #0x30  ; false
    // 0xa512bc: StoreField: r2->field_73 = r1
    //     0xa512bc: stur            w1, [x2, #0x73]
    // 0xa512c0: StoreField: r2->field_83 = r0
    //     0xa512c0: stur            w0, [x2, #0x83]
    // 0xa512c4: StoreField: r2->field_7b = r1
    //     0xa512c4: stur            w1, [x2, #0x7b]
    // 0xa512c8: r0 = AnimatedContainer()
    //     0xa512c8: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xa512cc: stur            x0, [fp, #-8]
    // 0xa512d0: r16 = Instance_Cubic
    //     0xa512d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xa512d4: ldr             x16, [x16, #0xaf8]
    // 0xa512d8: str             x16, [SP]
    // 0xa512dc: mov             x1, x0
    // 0xa512e0: ldur            x2, [fp, #-0x10]
    // 0xa512e4: r3 = Instance_Duration
    //     0xa512e4: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xa512e8: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xa512e8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xa512ec: ldr             x4, [x4, #0xbc8]
    // 0xa512f0: r0 = AnimatedContainer()
    //     0xa512f0: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xa512f4: ldur            x0, [fp, #-8]
    // 0xa512f8: LeaveFrame
    //     0xa512f8: mov             SP, fp
    //     0xa512fc: ldp             fp, lr, [SP], #0x10
    // 0xa51300: ret
    //     0xa51300: ret             
    // 0xa51304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa51304: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa51308: b               #0xa50244
    // 0xa5130c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa5130c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa51310: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51310: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51314: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51318: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51318: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa5131c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5131c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51320: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa51320: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa51324: SaveReg d0
    //     0xa51324: str             q0, [SP, #-0x10]!
    // 0xa51328: SaveReg r1
    //     0xa51328: str             x1, [SP, #-8]!
    // 0xa5132c: r0 = AllocateDouble()
    //     0xa5132c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa51330: RestoreReg r1
    //     0xa51330: ldr             x1, [SP], #8
    // 0xa51334: RestoreReg d0
    //     0xa51334: ldr             q0, [SP], #0x10
    // 0xa51338: b               #0xa50bd0
    // 0xa5133c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5133c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51340: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51344: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa51348, size: 0x13c
    // 0xa51348: EnterFrame
    //     0xa51348: stp             fp, lr, [SP, #-0x10]!
    //     0xa5134c: mov             fp, SP
    // 0xa51350: AllocStack(0x48)
    //     0xa51350: sub             SP, SP, #0x48
    // 0xa51354: SetupParameters()
    //     0xa51354: ldr             x0, [fp, #0x10]
    //     0xa51358: ldur            w1, [x0, #0x17]
    //     0xa5135c: add             x1, x1, HEAP, lsl #32
    // 0xa51360: CheckStackOverflow
    //     0xa51360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa51364: cmp             SP, x16
    //     0xa51368: b.ls            #0xa51478
    // 0xa5136c: LoadField: r0 = r1->field_f
    //     0xa5136c: ldur            w0, [x1, #0xf]
    // 0xa51370: DecompressPointer r0
    //     0xa51370: add             x0, x0, HEAP, lsl #32
    // 0xa51374: LoadField: r2 = r0->field_b
    //     0xa51374: ldur            w2, [x0, #0xb]
    // 0xa51378: DecompressPointer r2
    //     0xa51378: add             x2, x2, HEAP, lsl #32
    // 0xa5137c: cmp             w2, NULL
    // 0xa51380: b.eq            #0xa51480
    // 0xa51384: LoadField: r0 = r2->field_2f
    //     0xa51384: ldur            w0, [x2, #0x2f]
    // 0xa51388: DecompressPointer r0
    //     0xa51388: add             x0, x0, HEAP, lsl #32
    // 0xa5138c: cmp             w0, NULL
    // 0xa51390: b.ne            #0xa51398
    // 0xa51394: r0 = ""
    //     0xa51394: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa51398: LoadField: r3 = r2->field_2b
    //     0xa51398: ldur            w3, [x2, #0x2b]
    // 0xa5139c: DecompressPointer r3
    //     0xa5139c: add             x3, x3, HEAP, lsl #32
    // 0xa513a0: cmp             w3, NULL
    // 0xa513a4: b.ne            #0xa513ac
    // 0xa513a8: r3 = ""
    //     0xa513a8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa513ac: LoadField: r4 = r2->field_37
    //     0xa513ac: ldur            w4, [x2, #0x37]
    // 0xa513b0: DecompressPointer r4
    //     0xa513b0: add             x4, x4, HEAP, lsl #32
    // 0xa513b4: LoadField: r5 = r2->field_3f
    //     0xa513b4: ldur            w5, [x2, #0x3f]
    // 0xa513b8: DecompressPointer r5
    //     0xa513b8: add             x5, x5, HEAP, lsl #32
    // 0xa513bc: LoadField: r6 = r2->field_33
    //     0xa513bc: ldur            w6, [x2, #0x33]
    // 0xa513c0: DecompressPointer r6
    //     0xa513c0: add             x6, x6, HEAP, lsl #32
    // 0xa513c4: cmp             w6, NULL
    // 0xa513c8: b.ne            #0xa513d0
    // 0xa513cc: r6 = ""
    //     0xa513cc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa513d0: LoadField: r7 = r2->field_f
    //     0xa513d0: ldur            w7, [x2, #0xf]
    // 0xa513d4: DecompressPointer r7
    //     0xa513d4: add             x7, x7, HEAP, lsl #32
    // 0xa513d8: cmp             w7, NULL
    // 0xa513dc: b.ne            #0xa513e4
    // 0xa513e0: r7 = ""
    //     0xa513e0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa513e4: LoadField: r8 = r1->field_13
    //     0xa513e4: ldur            w8, [x1, #0x13]
    // 0xa513e8: DecompressPointer r8
    //     0xa513e8: add             x8, x8, HEAP, lsl #32
    // 0xa513ec: cmp             w8, NULL
    // 0xa513f0: b.ne            #0xa513fc
    // 0xa513f4: r1 = Null
    //     0xa513f4: mov             x1, NULL
    // 0xa513f8: b               #0xa51404
    // 0xa513fc: LoadField: r1 = r8->field_2b
    //     0xa513fc: ldur            w1, [x8, #0x2b]
    // 0xa51400: DecompressPointer r1
    //     0xa51400: add             x1, x1, HEAP, lsl #32
    // 0xa51404: cmp             w8, NULL
    // 0xa51408: b.ne            #0xa51414
    // 0xa5140c: r8 = Null
    //     0xa5140c: mov             x8, NULL
    // 0xa51410: b               #0xa51434
    // 0xa51414: LoadField: r9 = r8->field_3b
    //     0xa51414: ldur            w9, [x8, #0x3b]
    // 0xa51418: DecompressPointer r9
    //     0xa51418: add             x9, x9, HEAP, lsl #32
    // 0xa5141c: cmp             w9, NULL
    // 0xa51420: b.ne            #0xa5142c
    // 0xa51424: r8 = Null
    //     0xa51424: mov             x8, NULL
    // 0xa51428: b               #0xa51434
    // 0xa5142c: LoadField: r8 = r9->field_b
    //     0xa5142c: ldur            w8, [x9, #0xb]
    // 0xa51430: DecompressPointer r8
    //     0xa51430: add             x8, x8, HEAP, lsl #32
    // 0xa51434: LoadField: r9 = r2->field_57
    //     0xa51434: ldur            w9, [x2, #0x57]
    // 0xa51438: DecompressPointer r9
    //     0xa51438: add             x9, x9, HEAP, lsl #32
    // 0xa5143c: stp             x0, x9, [SP, #0x38]
    // 0xa51440: stp             x4, x3, [SP, #0x28]
    // 0xa51444: stp             x6, x5, [SP, #0x18]
    // 0xa51448: stp             x1, x7, [SP, #8]
    // 0xa5144c: str             x8, [SP]
    // 0xa51450: r4 = 0
    //     0xa51450: movz            x4, #0
    // 0xa51454: ldr             x0, [SP, #0x40]
    // 0xa51458: r16 = UnlinkedCall_0x613b5c
    //     0xa51458: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ab30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa5145c: add             x16, x16, #0xb30
    // 0xa51460: ldp             x5, lr, [x16]
    // 0xa51464: blr             lr
    // 0xa51468: r0 = Null
    //     0xa51468: mov             x0, NULL
    // 0xa5146c: LeaveFrame
    //     0xa5146c: mov             SP, fp
    //     0xa51470: ldp             fp, lr, [SP], #0x10
    // 0xa51474: ret
    //     0xa51474: ret             
    // 0xa51478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa51478: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5147c: b               #0xa5136c
    // 0xa51480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51480: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa54518, size: 0x268
    // 0xa54518: EnterFrame
    //     0xa54518: stp             fp, lr, [SP, #-0x10]!
    //     0xa5451c: mov             fp, SP
    // 0xa54520: AllocStack(0x48)
    //     0xa54520: sub             SP, SP, #0x48
    // 0xa54524: SetupParameters()
    //     0xa54524: ldr             x0, [fp, #0x10]
    //     0xa54528: ldur            w1, [x0, #0x17]
    //     0xa5452c: add             x1, x1, HEAP, lsl #32
    //     0xa54530: stur            x1, [fp, #-0x28]
    // 0xa54534: CheckStackOverflow
    //     0xa54534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa54538: cmp             SP, x16
    //     0xa5453c: b.ls            #0xa5476c
    // 0xa54540: LoadField: r0 = r1->field_f
    //     0xa54540: ldur            w0, [x1, #0xf]
    // 0xa54544: DecompressPointer r0
    //     0xa54544: add             x0, x0, HEAP, lsl #32
    // 0xa54548: LoadField: r2 = r0->field_b
    //     0xa54548: ldur            w2, [x0, #0xb]
    // 0xa5454c: DecompressPointer r2
    //     0xa5454c: add             x2, x2, HEAP, lsl #32
    // 0xa54550: stur            x2, [fp, #-0x20]
    // 0xa54554: cmp             w2, NULL
    // 0xa54558: b.eq            #0xa54774
    // 0xa5455c: LoadField: r0 = r2->field_37
    //     0xa5455c: ldur            w0, [x2, #0x37]
    // 0xa54560: DecompressPointer r0
    //     0xa54560: add             x0, x0, HEAP, lsl #32
    // 0xa54564: stur            x0, [fp, #-0x18]
    // 0xa54568: LoadField: r3 = r2->field_2f
    //     0xa54568: ldur            w3, [x2, #0x2f]
    // 0xa5456c: DecompressPointer r3
    //     0xa5456c: add             x3, x3, HEAP, lsl #32
    // 0xa54570: stur            x3, [fp, #-0x10]
    // 0xa54574: LoadField: r4 = r2->field_2b
    //     0xa54574: ldur            w4, [x2, #0x2b]
    // 0xa54578: DecompressPointer r4
    //     0xa54578: add             x4, x4, HEAP, lsl #32
    // 0xa5457c: stur            x4, [fp, #-8]
    // 0xa54580: r0 = EventData()
    //     0xa54580: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xa54584: mov             x1, x0
    // 0xa54588: ldur            x0, [fp, #-0x18]
    // 0xa5458c: stur            x1, [fp, #-0x30]
    // 0xa54590: StoreField: r1->field_13 = r0
    //     0xa54590: stur            w0, [x1, #0x13]
    // 0xa54594: ldur            x0, [fp, #-8]
    // 0xa54598: StoreField: r1->field_53 = r0
    //     0xa54598: stur            w0, [x1, #0x53]
    // 0xa5459c: ldur            x0, [fp, #-0x10]
    // 0xa545a0: StoreField: r1->field_57 = r0
    //     0xa545a0: stur            w0, [x1, #0x57]
    // 0xa545a4: r0 = "view_all"
    //     0xa545a4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xa545a8: ldr             x0, [x0, #0xba0]
    // 0xa545ac: StoreField: r1->field_eb = r0
    //     0xa545ac: stur            w0, [x1, #0xeb]
    // 0xa545b0: r0 = EventsRequest()
    //     0xa545b0: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xa545b4: mov             x1, x0
    // 0xa545b8: r0 = "cta_clicked"
    //     0xa545b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0xa545bc: ldr             x0, [x0, #0xdf8]
    // 0xa545c0: StoreField: r1->field_7 = r0
    //     0xa545c0: stur            w0, [x1, #7]
    // 0xa545c4: ldur            x0, [fp, #-0x30]
    // 0xa545c8: StoreField: r1->field_b = r0
    //     0xa545c8: stur            w0, [x1, #0xb]
    // 0xa545cc: ldur            x0, [fp, #-0x20]
    // 0xa545d0: LoadField: r2 = r0->field_3b
    //     0xa545d0: ldur            w2, [x0, #0x3b]
    // 0xa545d4: DecompressPointer r2
    //     0xa545d4: add             x2, x2, HEAP, lsl #32
    // 0xa545d8: stp             x1, x2, [SP]
    // 0xa545dc: r4 = 0
    //     0xa545dc: movz            x4, #0
    // 0xa545e0: ldr             x0, [SP, #8]
    // 0xa545e4: r16 = UnlinkedCall_0x613b5c
    //     0xa545e4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ab40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa545e8: add             x16, x16, #0xb40
    // 0xa545ec: ldp             x5, lr, [x16]
    // 0xa545f0: blr             lr
    // 0xa545f4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa545f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa545f8: ldr             x0, [x0, #0x1c80]
    //     0xa545fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa54600: cmp             w0, w16
    //     0xa54604: b.ne            #0xa54610
    //     0xa54608: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa5460c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa54610: r1 = Null
    //     0xa54610: mov             x1, NULL
    // 0xa54614: r2 = 12
    //     0xa54614: movz            x2, #0xc
    // 0xa54618: r0 = AllocateArray()
    //     0xa54618: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa5461c: stur            x0, [fp, #-8]
    // 0xa54620: r16 = "link"
    //     0xa54620: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0xa54624: StoreField: r0->field_f = r16
    //     0xa54624: stur            w16, [x0, #0xf]
    // 0xa54628: ldur            x1, [fp, #-0x28]
    // 0xa5462c: LoadField: r2 = r1->field_f
    //     0xa5462c: ldur            w2, [x1, #0xf]
    // 0xa54630: DecompressPointer r2
    //     0xa54630: add             x2, x2, HEAP, lsl #32
    // 0xa54634: LoadField: r3 = r2->field_b
    //     0xa54634: ldur            w3, [x2, #0xb]
    // 0xa54638: DecompressPointer r3
    //     0xa54638: add             x3, x3, HEAP, lsl #32
    // 0xa5463c: cmp             w3, NULL
    // 0xa54640: b.eq            #0xa54778
    // 0xa54644: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa54644: ldur            w2, [x3, #0x17]
    // 0xa54648: DecompressPointer r2
    //     0xa54648: add             x2, x2, HEAP, lsl #32
    // 0xa5464c: cmp             w2, NULL
    // 0xa54650: b.ne            #0xa5465c
    // 0xa54654: r2 = Null
    //     0xa54654: mov             x2, NULL
    // 0xa54658: b               #0xa54668
    // 0xa5465c: LoadField: r3 = r2->field_b
    //     0xa5465c: ldur            w3, [x2, #0xb]
    // 0xa54660: DecompressPointer r3
    //     0xa54660: add             x3, x3, HEAP, lsl #32
    // 0xa54664: mov             x2, x3
    // 0xa54668: str             x2, [SP]
    // 0xa5466c: r0 = _interpolateSingle()
    //     0xa5466c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa54670: ldur            x1, [fp, #-8]
    // 0xa54674: ArrayStore: r1[1] = r0  ; List_4
    //     0xa54674: add             x25, x1, #0x13
    //     0xa54678: str             w0, [x25]
    //     0xa5467c: tbz             w0, #0, #0xa54698
    //     0xa54680: ldurb           w16, [x1, #-1]
    //     0xa54684: ldurb           w17, [x0, #-1]
    //     0xa54688: and             x16, x17, x16, lsr #2
    //     0xa5468c: tst             x16, HEAP, lsr #32
    //     0xa54690: b.eq            #0xa54698
    //     0xa54694: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa54698: ldur            x2, [fp, #-8]
    // 0xa5469c: r16 = "previousScreenSource"
    //     0xa5469c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa546a0: ldr             x16, [x16, #0x448]
    // 0xa546a4: ArrayStore: r2[0] = r16  ; List_4
    //     0xa546a4: stur            w16, [x2, #0x17]
    // 0xa546a8: ldur            x0, [fp, #-0x28]
    // 0xa546ac: LoadField: r1 = r0->field_f
    //     0xa546ac: ldur            w1, [x0, #0xf]
    // 0xa546b0: DecompressPointer r1
    //     0xa546b0: add             x1, x1, HEAP, lsl #32
    // 0xa546b4: LoadField: r3 = r1->field_b
    //     0xa546b4: ldur            w3, [x1, #0xb]
    // 0xa546b8: DecompressPointer r3
    //     0xa546b8: add             x3, x3, HEAP, lsl #32
    // 0xa546bc: cmp             w3, NULL
    // 0xa546c0: b.eq            #0xa5477c
    // 0xa546c4: LoadField: r0 = r3->field_37
    //     0xa546c4: ldur            w0, [x3, #0x37]
    // 0xa546c8: DecompressPointer r0
    //     0xa546c8: add             x0, x0, HEAP, lsl #32
    // 0xa546cc: mov             x1, x2
    // 0xa546d0: ArrayStore: r1[3] = r0  ; List_4
    //     0xa546d0: add             x25, x1, #0x1b
    //     0xa546d4: str             w0, [x25]
    //     0xa546d8: tbz             w0, #0, #0xa546f4
    //     0xa546dc: ldurb           w16, [x1, #-1]
    //     0xa546e0: ldurb           w17, [x0, #-1]
    //     0xa546e4: and             x16, x17, x16, lsr #2
    //     0xa546e8: tst             x16, HEAP, lsr #32
    //     0xa546ec: b.eq            #0xa546f4
    //     0xa546f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa546f4: r16 = "screenSource"
    //     0xa546f4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xa546f8: ldr             x16, [x16, #0x450]
    // 0xa546fc: StoreField: r2->field_1f = r16
    //     0xa546fc: stur            w16, [x2, #0x1f]
    // 0xa54700: LoadField: r0 = r3->field_33
    //     0xa54700: ldur            w0, [x3, #0x33]
    // 0xa54704: DecompressPointer r0
    //     0xa54704: add             x0, x0, HEAP, lsl #32
    // 0xa54708: mov             x1, x2
    // 0xa5470c: ArrayStore: r1[5] = r0  ; List_4
    //     0xa5470c: add             x25, x1, #0x23
    //     0xa54710: str             w0, [x25]
    //     0xa54714: tbz             w0, #0, #0xa54730
    //     0xa54718: ldurb           w16, [x1, #-1]
    //     0xa5471c: ldurb           w17, [x0, #-1]
    //     0xa54720: and             x16, x17, x16, lsr #2
    //     0xa54724: tst             x16, HEAP, lsr #32
    //     0xa54728: b.eq            #0xa54730
    //     0xa5472c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa54730: r16 = <String, String?>
    //     0xa54730: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xa54734: ldr             x16, [x16, #0x3c8]
    // 0xa54738: stp             x2, x16, [SP]
    // 0xa5473c: r0 = Map._fromLiteral()
    //     0xa5473c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa54740: r16 = "/collection"
    //     0xa54740: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0xa54744: ldr             x16, [x16, #0x458]
    // 0xa54748: stp             x16, NULL, [SP, #8]
    // 0xa5474c: str             x0, [SP]
    // 0xa54750: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa54750: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa54754: ldr             x4, [x4, #0x438]
    // 0xa54758: r0 = GetNavigation.toNamed()
    //     0xa54758: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa5475c: r0 = Null
    //     0xa5475c: mov             x0, NULL
    // 0xa54760: LeaveFrame
    //     0xa54760: mov             SP, fp
    //     0xa54764: ldp             fp, lr, [SP], #0x10
    // 0xa54768: ret
    //     0xa54768: ret             
    // 0xa5476c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5476c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa54770: b               #0xa54540
    // 0xa54774: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa54774: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa54778: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa54778: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa5477c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5477c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa54780, size: 0x268
    // 0xa54780: EnterFrame
    //     0xa54780: stp             fp, lr, [SP, #-0x10]!
    //     0xa54784: mov             fp, SP
    // 0xa54788: AllocStack(0x48)
    //     0xa54788: sub             SP, SP, #0x48
    // 0xa5478c: SetupParameters()
    //     0xa5478c: ldr             x0, [fp, #0x10]
    //     0xa54790: ldur            w1, [x0, #0x17]
    //     0xa54794: add             x1, x1, HEAP, lsl #32
    //     0xa54798: stur            x1, [fp, #-0x28]
    // 0xa5479c: CheckStackOverflow
    //     0xa5479c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa547a0: cmp             SP, x16
    //     0xa547a4: b.ls            #0xa549d4
    // 0xa547a8: LoadField: r0 = r1->field_f
    //     0xa547a8: ldur            w0, [x1, #0xf]
    // 0xa547ac: DecompressPointer r0
    //     0xa547ac: add             x0, x0, HEAP, lsl #32
    // 0xa547b0: LoadField: r2 = r0->field_b
    //     0xa547b0: ldur            w2, [x0, #0xb]
    // 0xa547b4: DecompressPointer r2
    //     0xa547b4: add             x2, x2, HEAP, lsl #32
    // 0xa547b8: stur            x2, [fp, #-0x20]
    // 0xa547bc: cmp             w2, NULL
    // 0xa547c0: b.eq            #0xa549dc
    // 0xa547c4: LoadField: r0 = r2->field_37
    //     0xa547c4: ldur            w0, [x2, #0x37]
    // 0xa547c8: DecompressPointer r0
    //     0xa547c8: add             x0, x0, HEAP, lsl #32
    // 0xa547cc: stur            x0, [fp, #-0x18]
    // 0xa547d0: LoadField: r3 = r2->field_2f
    //     0xa547d0: ldur            w3, [x2, #0x2f]
    // 0xa547d4: DecompressPointer r3
    //     0xa547d4: add             x3, x3, HEAP, lsl #32
    // 0xa547d8: stur            x3, [fp, #-0x10]
    // 0xa547dc: LoadField: r4 = r2->field_2b
    //     0xa547dc: ldur            w4, [x2, #0x2b]
    // 0xa547e0: DecompressPointer r4
    //     0xa547e0: add             x4, x4, HEAP, lsl #32
    // 0xa547e4: stur            x4, [fp, #-8]
    // 0xa547e8: r0 = EventData()
    //     0xa547e8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xa547ec: mov             x1, x0
    // 0xa547f0: ldur            x0, [fp, #-0x18]
    // 0xa547f4: stur            x1, [fp, #-0x30]
    // 0xa547f8: StoreField: r1->field_13 = r0
    //     0xa547f8: stur            w0, [x1, #0x13]
    // 0xa547fc: ldur            x0, [fp, #-8]
    // 0xa54800: StoreField: r1->field_53 = r0
    //     0xa54800: stur            w0, [x1, #0x53]
    // 0xa54804: ldur            x0, [fp, #-0x10]
    // 0xa54808: StoreField: r1->field_57 = r0
    //     0xa54808: stur            w0, [x1, #0x57]
    // 0xa5480c: r0 = "view_all"
    //     0xa5480c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xa54810: ldr             x0, [x0, #0xba0]
    // 0xa54814: StoreField: r1->field_eb = r0
    //     0xa54814: stur            w0, [x1, #0xeb]
    // 0xa54818: r0 = EventsRequest()
    //     0xa54818: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xa5481c: mov             x1, x0
    // 0xa54820: r0 = "cta_clicked"
    //     0xa54820: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0xa54824: ldr             x0, [x0, #0xdf8]
    // 0xa54828: StoreField: r1->field_7 = r0
    //     0xa54828: stur            w0, [x1, #7]
    // 0xa5482c: ldur            x0, [fp, #-0x30]
    // 0xa54830: StoreField: r1->field_b = r0
    //     0xa54830: stur            w0, [x1, #0xb]
    // 0xa54834: ldur            x0, [fp, #-0x20]
    // 0xa54838: LoadField: r2 = r0->field_3b
    //     0xa54838: ldur            w2, [x0, #0x3b]
    // 0xa5483c: DecompressPointer r2
    //     0xa5483c: add             x2, x2, HEAP, lsl #32
    // 0xa54840: stp             x1, x2, [SP]
    // 0xa54844: r4 = 0
    //     0xa54844: movz            x4, #0
    // 0xa54848: ldr             x0, [SP, #8]
    // 0xa5484c: r16 = UnlinkedCall_0x613b5c
    //     0xa5484c: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ab50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa54850: add             x16, x16, #0xb50
    // 0xa54854: ldp             x5, lr, [x16]
    // 0xa54858: blr             lr
    // 0xa5485c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa5485c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa54860: ldr             x0, [x0, #0x1c80]
    //     0xa54864: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa54868: cmp             w0, w16
    //     0xa5486c: b.ne            #0xa54878
    //     0xa54870: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa54874: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa54878: r1 = Null
    //     0xa54878: mov             x1, NULL
    // 0xa5487c: r2 = 12
    //     0xa5487c: movz            x2, #0xc
    // 0xa54880: r0 = AllocateArray()
    //     0xa54880: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa54884: stur            x0, [fp, #-8]
    // 0xa54888: r16 = "link"
    //     0xa54888: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0xa5488c: StoreField: r0->field_f = r16
    //     0xa5488c: stur            w16, [x0, #0xf]
    // 0xa54890: ldur            x1, [fp, #-0x28]
    // 0xa54894: LoadField: r2 = r1->field_f
    //     0xa54894: ldur            w2, [x1, #0xf]
    // 0xa54898: DecompressPointer r2
    //     0xa54898: add             x2, x2, HEAP, lsl #32
    // 0xa5489c: LoadField: r3 = r2->field_b
    //     0xa5489c: ldur            w3, [x2, #0xb]
    // 0xa548a0: DecompressPointer r3
    //     0xa548a0: add             x3, x3, HEAP, lsl #32
    // 0xa548a4: cmp             w3, NULL
    // 0xa548a8: b.eq            #0xa549e0
    // 0xa548ac: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa548ac: ldur            w2, [x3, #0x17]
    // 0xa548b0: DecompressPointer r2
    //     0xa548b0: add             x2, x2, HEAP, lsl #32
    // 0xa548b4: cmp             w2, NULL
    // 0xa548b8: b.ne            #0xa548c4
    // 0xa548bc: r2 = Null
    //     0xa548bc: mov             x2, NULL
    // 0xa548c0: b               #0xa548d0
    // 0xa548c4: LoadField: r3 = r2->field_b
    //     0xa548c4: ldur            w3, [x2, #0xb]
    // 0xa548c8: DecompressPointer r3
    //     0xa548c8: add             x3, x3, HEAP, lsl #32
    // 0xa548cc: mov             x2, x3
    // 0xa548d0: str             x2, [SP]
    // 0xa548d4: r0 = _interpolateSingle()
    //     0xa548d4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa548d8: ldur            x1, [fp, #-8]
    // 0xa548dc: ArrayStore: r1[1] = r0  ; List_4
    //     0xa548dc: add             x25, x1, #0x13
    //     0xa548e0: str             w0, [x25]
    //     0xa548e4: tbz             w0, #0, #0xa54900
    //     0xa548e8: ldurb           w16, [x1, #-1]
    //     0xa548ec: ldurb           w17, [x0, #-1]
    //     0xa548f0: and             x16, x17, x16, lsr #2
    //     0xa548f4: tst             x16, HEAP, lsr #32
    //     0xa548f8: b.eq            #0xa54900
    //     0xa548fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa54900: ldur            x2, [fp, #-8]
    // 0xa54904: r16 = "previousScreenSource"
    //     0xa54904: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa54908: ldr             x16, [x16, #0x448]
    // 0xa5490c: ArrayStore: r2[0] = r16  ; List_4
    //     0xa5490c: stur            w16, [x2, #0x17]
    // 0xa54910: ldur            x0, [fp, #-0x28]
    // 0xa54914: LoadField: r1 = r0->field_f
    //     0xa54914: ldur            w1, [x0, #0xf]
    // 0xa54918: DecompressPointer r1
    //     0xa54918: add             x1, x1, HEAP, lsl #32
    // 0xa5491c: LoadField: r3 = r1->field_b
    //     0xa5491c: ldur            w3, [x1, #0xb]
    // 0xa54920: DecompressPointer r3
    //     0xa54920: add             x3, x3, HEAP, lsl #32
    // 0xa54924: cmp             w3, NULL
    // 0xa54928: b.eq            #0xa549e4
    // 0xa5492c: LoadField: r0 = r3->field_37
    //     0xa5492c: ldur            w0, [x3, #0x37]
    // 0xa54930: DecompressPointer r0
    //     0xa54930: add             x0, x0, HEAP, lsl #32
    // 0xa54934: mov             x1, x2
    // 0xa54938: ArrayStore: r1[3] = r0  ; List_4
    //     0xa54938: add             x25, x1, #0x1b
    //     0xa5493c: str             w0, [x25]
    //     0xa54940: tbz             w0, #0, #0xa5495c
    //     0xa54944: ldurb           w16, [x1, #-1]
    //     0xa54948: ldurb           w17, [x0, #-1]
    //     0xa5494c: and             x16, x17, x16, lsr #2
    //     0xa54950: tst             x16, HEAP, lsr #32
    //     0xa54954: b.eq            #0xa5495c
    //     0xa54958: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa5495c: r16 = "screenSource"
    //     0xa5495c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xa54960: ldr             x16, [x16, #0x450]
    // 0xa54964: StoreField: r2->field_1f = r16
    //     0xa54964: stur            w16, [x2, #0x1f]
    // 0xa54968: LoadField: r0 = r3->field_33
    //     0xa54968: ldur            w0, [x3, #0x33]
    // 0xa5496c: DecompressPointer r0
    //     0xa5496c: add             x0, x0, HEAP, lsl #32
    // 0xa54970: mov             x1, x2
    // 0xa54974: ArrayStore: r1[5] = r0  ; List_4
    //     0xa54974: add             x25, x1, #0x23
    //     0xa54978: str             w0, [x25]
    //     0xa5497c: tbz             w0, #0, #0xa54998
    //     0xa54980: ldurb           w16, [x1, #-1]
    //     0xa54984: ldurb           w17, [x0, #-1]
    //     0xa54988: and             x16, x17, x16, lsr #2
    //     0xa5498c: tst             x16, HEAP, lsr #32
    //     0xa54990: b.eq            #0xa54998
    //     0xa54994: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa54998: r16 = <String, String?>
    //     0xa54998: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xa5499c: ldr             x16, [x16, #0x3c8]
    // 0xa549a0: stp             x2, x16, [SP]
    // 0xa549a4: r0 = Map._fromLiteral()
    //     0xa549a4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa549a8: r16 = "/collection"
    //     0xa549a8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0xa549ac: ldr             x16, [x16, #0x458]
    // 0xa549b0: stp             x16, NULL, [SP, #8]
    // 0xa549b4: str             x0, [SP]
    // 0xa549b8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa549b8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa549bc: ldr             x4, [x4, #0x438]
    // 0xa549c0: r0 = GetNavigation.toNamed()
    //     0xa549c0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa549c4: r0 = Null
    //     0xa549c4: mov             x0, NULL
    // 0xa549c8: LeaveFrame
    //     0xa549c8: mov             SP, fp
    //     0xa549cc: ldp             fp, lr, [SP], #0x10
    // 0xa549d0: ret
    //     0xa549d0: ret             
    // 0xa549d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa549d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa549d8: b               #0xa547a8
    // 0xa549dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa549dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa549e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa549e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa549e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa549e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa549e8, size: 0xc8
    // 0xa549e8: EnterFrame
    //     0xa549e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa549ec: mov             fp, SP
    // 0xa549f0: AllocStack(0x30)
    //     0xa549f0: sub             SP, SP, #0x30
    // 0xa549f4: SetupParameters()
    //     0xa549f4: ldr             x0, [fp, #0x10]
    //     0xa549f8: ldur            w1, [x0, #0x17]
    //     0xa549fc: add             x1, x1, HEAP, lsl #32
    // 0xa54a00: CheckStackOverflow
    //     0xa54a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa54a04: cmp             SP, x16
    //     0xa54a08: b.ls            #0xa54aa4
    // 0xa54a0c: LoadField: r0 = r1->field_f
    //     0xa54a0c: ldur            w0, [x1, #0xf]
    // 0xa54a10: DecompressPointer r0
    //     0xa54a10: add             x0, x0, HEAP, lsl #32
    // 0xa54a14: LoadField: r1 = r0->field_b
    //     0xa54a14: ldur            w1, [x0, #0xb]
    // 0xa54a18: DecompressPointer r1
    //     0xa54a18: add             x1, x1, HEAP, lsl #32
    // 0xa54a1c: cmp             w1, NULL
    // 0xa54a20: b.eq            #0xa54aac
    // 0xa54a24: LoadField: r0 = r1->field_2f
    //     0xa54a24: ldur            w0, [x1, #0x2f]
    // 0xa54a28: DecompressPointer r0
    //     0xa54a28: add             x0, x0, HEAP, lsl #32
    // 0xa54a2c: LoadField: r2 = r1->field_2b
    //     0xa54a2c: ldur            w2, [x1, #0x2b]
    // 0xa54a30: DecompressPointer r2
    //     0xa54a30: add             x2, x2, HEAP, lsl #32
    // 0xa54a34: LoadField: r3 = r1->field_37
    //     0xa54a34: ldur            w3, [x1, #0x37]
    // 0xa54a38: DecompressPointer r3
    //     0xa54a38: add             x3, x3, HEAP, lsl #32
    // 0xa54a3c: LoadField: r4 = r1->field_33
    //     0xa54a3c: ldur            w4, [x1, #0x33]
    // 0xa54a40: DecompressPointer r4
    //     0xa54a40: add             x4, x4, HEAP, lsl #32
    // 0xa54a44: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xa54a44: ldur            w5, [x1, #0x17]
    // 0xa54a48: DecompressPointer r5
    //     0xa54a48: add             x5, x5, HEAP, lsl #32
    // 0xa54a4c: cmp             w5, NULL
    // 0xa54a50: b.ne            #0xa54a5c
    // 0xa54a54: r5 = Null
    //     0xa54a54: mov             x5, NULL
    // 0xa54a58: b               #0xa54a68
    // 0xa54a5c: LoadField: r6 = r5->field_b
    //     0xa54a5c: ldur            w6, [x5, #0xb]
    // 0xa54a60: DecompressPointer r6
    //     0xa54a60: add             x6, x6, HEAP, lsl #32
    // 0xa54a64: mov             x5, x6
    // 0xa54a68: LoadField: r6 = r1->field_53
    //     0xa54a68: ldur            w6, [x1, #0x53]
    // 0xa54a6c: DecompressPointer r6
    //     0xa54a6c: add             x6, x6, HEAP, lsl #32
    // 0xa54a70: stp             x0, x6, [SP, #0x20]
    // 0xa54a74: stp             x3, x2, [SP, #0x10]
    // 0xa54a78: stp             x5, x4, [SP]
    // 0xa54a7c: r4 = 0
    //     0xa54a7c: movz            x4, #0
    // 0xa54a80: ldr             x0, [SP, #0x28]
    // 0xa54a84: r16 = UnlinkedCall_0x613b5c
    //     0xa54a84: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ab60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa54a88: add             x16, x16, #0xb60
    // 0xa54a8c: ldp             x5, lr, [x16]
    // 0xa54a90: blr             lr
    // 0xa54a94: r0 = Null
    //     0xa54a94: mov             x0, NULL
    // 0xa54a98: LeaveFrame
    //     0xa54a98: mov             SP, fp
    //     0xa54a9c: ldp             fp, lr, [SP], #0x10
    // 0xa54aa0: ret
    //     0xa54aa0: ret             
    // 0xa54aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa54aa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa54aa8: b               #0xa54a0c
    // 0xa54aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa54aac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc86c24, size: 0x64
    // 0xc86c24: EnterFrame
    //     0xc86c24: stp             fp, lr, [SP, #-0x10]!
    //     0xc86c28: mov             fp, SP
    // 0xc86c2c: AllocStack(0x8)
    //     0xc86c2c: sub             SP, SP, #8
    // 0xc86c30: SetupParameters(_ProductGridItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc86c30: mov             x0, x1
    //     0xc86c34: stur            x1, [fp, #-8]
    // 0xc86c38: CheckStackOverflow
    //     0xc86c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc86c3c: cmp             SP, x16
    //     0xc86c40: b.ls            #0xc86c74
    // 0xc86c44: LoadField: r1 = r0->field_1b
    //     0xc86c44: ldur            w1, [x0, #0x1b]
    // 0xc86c48: DecompressPointer r1
    //     0xc86c48: add             x1, x1, HEAP, lsl #32
    // 0xc86c4c: r16 = Sentinel
    //     0xc86c4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc86c50: cmp             w1, w16
    // 0xc86c54: b.eq            #0xc86c7c
    // 0xc86c58: r0 = dispose()
    //     0xc86c58: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc86c5c: ldur            x1, [fp, #-8]
    // 0xc86c60: r0 = dispose()
    //     0xc86c60: bl              #0xc86c88  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_grid_item_view.dart] __ProductGridItemViewState&State&SingleTickerProviderStateMixin::dispose
    // 0xc86c64: r0 = Null
    //     0xc86c64: mov             x0, NULL
    // 0xc86c68: LeaveFrame
    //     0xc86c68: mov             SP, fp
    //     0xc86c6c: ldp             fp, lr, [SP], #0x10
    // 0xc86c70: ret
    //     0xc86c70: ret             
    // 0xc86c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc86c74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc86c78: b               #0xc86c44
    // 0xc86c7c: r9 = _pageController
    //     0xc86c7c: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5aad0] Field <_ProductGridItemViewState@**********._pageController@**********>: late (offset: 0x1c)
    //     0xc86c80: ldr             x9, [x9, #0xad0]
    // 0xc86c84: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc86c84: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4244, size: 0x60, field offset: 0xc
//   const constructor, 
class ProductGridItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7bb48, size: 0x30
    // 0xc7bb48: EnterFrame
    //     0xc7bb48: stp             fp, lr, [SP, #-0x10]!
    //     0xc7bb4c: mov             fp, SP
    // 0xc7bb50: mov             x0, x1
    // 0xc7bb54: r1 = <ProductGridItemView>
    //     0xc7bb54: add             x1, PP, #0x49, lsl #12  ; [pp+0x49090] TypeArguments: <ProductGridItemView>
    //     0xc7bb58: ldr             x1, [x1, #0x90]
    // 0xc7bb5c: r0 = _ProductGridItemViewState()
    //     0xc7bb5c: bl              #0xc7bb78  ; Allocate_ProductGridItemViewStateStub -> _ProductGridItemViewState (size=0x28)
    // 0xc7bb60: r1 = Sentinel
    //     0xc7bb60: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7bb64: StoreField: r0->field_1b = r1
    //     0xc7bb64: stur            w1, [x0, #0x1b]
    // 0xc7bb68: StoreField: r0->field_1f = rZR
    //     0xc7bb68: stur            xzr, [x0, #0x1f]
    // 0xc7bb6c: LeaveFrame
    //     0xc7bb6c: mov             SP, fp
    //     0xc7bb70: ldp             fp, lr, [SP], #0x10
    // 0xc7bb74: ret
    //     0xc7bb74: ret             
  }
}
