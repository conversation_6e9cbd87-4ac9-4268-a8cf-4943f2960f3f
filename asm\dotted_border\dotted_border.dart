// lib: dotted_border, url: package:dotted_border/dotted_border.dart

// class id: 1049620, size: 0x8
class :: {
}

// class id: 4480, size: 0x3c, field offset: 0xc
class DottedBorder extends StatelessWidget {

  _ DottedBorder(/* No info */) {
    // ** addr: 0x9f8704, size: 0x190
    // 0x9f8704: EnterFrame
    //     0x9f8704: stp             fp, lr, [SP, #-0x10]!
    //     0x9f8708: mov             fp, SP
    // 0x9f870c: mov             x0, x2
    // 0x9f8710: mov             x2, x3
    // 0x9f8714: mov             x3, x1
    // 0x9f8718: mov             x1, x5
    // 0x9f871c: LoadField: r5 = r4->field_13
    //     0x9f871c: ldur            w5, [x4, #0x13]
    // 0x9f8720: LoadField: r6 = r4->field_1f
    //     0x9f8720: ldur            w6, [x4, #0x1f]
    // 0x9f8724: DecompressPointer r6
    //     0x9f8724: add             x6, x6, HEAP, lsl #32
    // 0x9f8728: r16 = "borderType"
    //     0x9f8728: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bf8] "borderType"
    //     0x9f872c: ldr             x16, [x16, #0xbf8]
    // 0x9f8730: cmp             w6, w16
    // 0x9f8734: b.ne            #0x9f8758
    // 0x9f8738: LoadField: r6 = r4->field_23
    //     0x9f8738: ldur            w6, [x4, #0x23]
    // 0x9f873c: DecompressPointer r6
    //     0x9f873c: add             x6, x6, HEAP, lsl #32
    // 0x9f8740: sub             w7, w5, w6
    // 0x9f8744: add             x6, fp, w7, sxtw #2
    // 0x9f8748: ldr             x6, [x6, #8]
    // 0x9f874c: mov             x7, x6
    // 0x9f8750: r6 = 1
    //     0x9f8750: movz            x6, #0x1
    // 0x9f8754: b               #0x9f8764
    // 0x9f8758: r7 = Instance_BorderType
    //     0x9f8758: add             x7, PP, #0x36, lsl #12  ; [pp+0x36c00] Obj!BorderType@d75081
    //     0x9f875c: ldr             x7, [x7, #0xc00]
    // 0x9f8760: r6 = 0
    //     0x9f8760: movz            x6, #0
    // 0x9f8764: lsl             x8, x6, #1
    // 0x9f8768: lsl             w6, w8, #1
    // 0x9f876c: add             w8, w6, #8
    // 0x9f8770: ArrayLoad: r9 = r4[r8]  ; Unknown_4
    //     0x9f8770: add             x16, x4, w8, sxtw #1
    //     0x9f8774: ldur            w9, [x16, #0xf]
    // 0x9f8778: DecompressPointer r9
    //     0x9f8778: add             x9, x9, HEAP, lsl #32
    // 0x9f877c: r16 = "radius"
    //     0x9f877c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c08] "radius"
    //     0x9f8780: ldr             x16, [x16, #0xc08]
    // 0x9f8784: cmp             w9, w16
    // 0x9f8788: b.ne            #0x9f87b0
    // 0x9f878c: add             w8, w6, #0xa
    // 0x9f8790: ArrayLoad: r6 = r4[r8]  ; Unknown_4
    //     0x9f8790: add             x16, x4, w8, sxtw #1
    //     0x9f8794: ldur            w6, [x16, #0xf]
    // 0x9f8798: DecompressPointer r6
    //     0x9f8798: add             x6, x6, HEAP, lsl #32
    // 0x9f879c: sub             w4, w5, w6
    // 0x9f87a0: add             x5, fp, w4, sxtw #2
    // 0x9f87a4: ldr             x5, [x5, #8]
    // 0x9f87a8: mov             x9, x5
    // 0x9f87ac: b               #0x9f87b8
    // 0x9f87b0: r9 = Instance_Radius
    //     0x9f87b0: add             x9, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x9f87b4: ldr             x9, [x9, #0xb48]
    // 0x9f87b8: r8 = Instance_EdgeInsets
    //     0x9f87b8: add             x8, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0x9f87bc: ldr             x8, [x8, #0xc10]
    // 0x9f87c0: r6 = Instance_EdgeInsets
    //     0x9f87c0: ldr             x6, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x9f87c4: r5 = Instance_StrokeCap
    //     0x9f87c4: add             x5, PP, #0x36, lsl #12  ; [pp+0x36c18] Obj!StrokeCap@d770e1
    //     0x9f87c8: ldr             x5, [x5, #0xc18]
    // 0x9f87cc: r4 = Instance_StackFit
    //     0x9f87cc: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x9f87d0: ldr             x4, [x4, #0xfa8]
    // 0x9f87d4: StoreField: r3->field_b = r0
    //     0x9f87d4: stur            w0, [x3, #0xb]
    //     0x9f87d8: ldurb           w16, [x3, #-1]
    //     0x9f87dc: ldurb           w17, [x0, #-1]
    //     0x9f87e0: and             x16, x17, x16, lsr #2
    //     0x9f87e4: tst             x16, HEAP, lsr #32
    //     0x9f87e8: b.eq            #0x9f87f0
    //     0x9f87ec: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9f87f0: mov             x0, x2
    // 0x9f87f4: StoreField: r3->field_1f = r0
    //     0x9f87f4: stur            w0, [x3, #0x1f]
    //     0x9f87f8: ldurb           w16, [x3, #-1]
    //     0x9f87fc: ldurb           w17, [x0, #-1]
    //     0x9f8800: and             x16, x17, x16, lsr #2
    //     0x9f8804: tst             x16, HEAP, lsr #32
    //     0x9f8808: b.eq            #0x9f8810
    //     0x9f880c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9f8810: ArrayStore: r3[0] = d0  ; List_8
    //     0x9f8810: stur            d0, [x3, #0x17]
    // 0x9f8814: mov             x0, x7
    // 0x9f8818: StoreField: r3->field_27 = r0
    //     0x9f8818: stur            w0, [x3, #0x27]
    //     0x9f881c: ldurb           w16, [x3, #-1]
    //     0x9f8820: ldurb           w17, [x0, #-1]
    //     0x9f8824: and             x16, x17, x16, lsr #2
    //     0x9f8828: tst             x16, HEAP, lsr #32
    //     0x9f882c: b.eq            #0x9f8834
    //     0x9f8830: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9f8834: mov             x0, x1
    // 0x9f8838: StoreField: r3->field_23 = r0
    //     0x9f8838: stur            w0, [x3, #0x23]
    //     0x9f883c: ldurb           w16, [x3, #-1]
    //     0x9f8840: ldurb           w17, [x0, #-1]
    //     0x9f8844: and             x16, x17, x16, lsr #2
    //     0x9f8848: tst             x16, HEAP, lsr #32
    //     0x9f884c: b.eq            #0x9f8854
    //     0x9f8850: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9f8854: StoreField: r3->field_f = r8
    //     0x9f8854: stur            w8, [x3, #0xf]
    // 0x9f8858: StoreField: r3->field_13 = r6
    //     0x9f8858: stur            w6, [x3, #0x13]
    // 0x9f885c: mov             x0, x9
    // 0x9f8860: StoreField: r3->field_2b = r0
    //     0x9f8860: stur            w0, [x3, #0x2b]
    //     0x9f8864: ldurb           w16, [x3, #-1]
    //     0x9f8868: ldurb           w17, [x0, #-1]
    //     0x9f886c: and             x16, x17, x16, lsr #2
    //     0x9f8870: tst             x16, HEAP, lsr #32
    //     0x9f8874: b.eq            #0x9f887c
    //     0x9f8878: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9f887c: StoreField: r3->field_2f = r5
    //     0x9f887c: stur            w5, [x3, #0x2f]
    // 0x9f8880: StoreField: r3->field_37 = r4
    //     0x9f8880: stur            w4, [x3, #0x37]
    // 0x9f8884: r0 = Null
    //     0x9f8884: mov             x0, NULL
    // 0x9f8888: LeaveFrame
    //     0x9f8888: mov             SP, fp
    //     0x9f888c: ldp             fp, lr, [SP], #0x10
    // 0x9f8890: ret
    //     0x9f8890: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0x1299410, size: 0x198
    // 0x1299410: EnterFrame
    //     0x1299410: stp             fp, lr, [SP, #-0x10]!
    //     0x1299414: mov             fp, SP
    // 0x1299418: AllocStack(0x38)
    //     0x1299418: sub             SP, SP, #0x38
    // 0x129941c: SetupParameters(DottedBorder this /* r1 => r1, fp-0x28 */)
    //     0x129941c: stur            x1, [fp, #-0x28]
    // 0x1299420: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x1299420: ldur            d0, [x1, #0x17]
    // 0x1299424: stur            d0, [fp, #-0x38]
    // 0x1299428: LoadField: r0 = r1->field_2b
    //     0x1299428: ldur            w0, [x1, #0x2b]
    // 0x129942c: DecompressPointer r0
    //     0x129942c: add             x0, x0, HEAP, lsl #32
    // 0x1299430: stur            x0, [fp, #-0x20]
    // 0x1299434: LoadField: r2 = r1->field_1f
    //     0x1299434: ldur            w2, [x1, #0x1f]
    // 0x1299438: DecompressPointer r2
    //     0x1299438: add             x2, x2, HEAP, lsl #32
    // 0x129943c: stur            x2, [fp, #-0x18]
    // 0x1299440: LoadField: r3 = r1->field_27
    //     0x1299440: ldur            w3, [x1, #0x27]
    // 0x1299444: DecompressPointer r3
    //     0x1299444: add             x3, x3, HEAP, lsl #32
    // 0x1299448: stur            x3, [fp, #-0x10]
    // 0x129944c: LoadField: r4 = r1->field_23
    //     0x129944c: ldur            w4, [x1, #0x23]
    // 0x1299450: DecompressPointer r4
    //     0x1299450: add             x4, x4, HEAP, lsl #32
    // 0x1299454: stur            x4, [fp, #-8]
    // 0x1299458: r0 = DashedPainter()
    //     0x1299458: bl              #0x12995a8  ; AllocateDashedPainterStub -> DashedPainter (size=0x30)
    // 0x129945c: ldur            d0, [fp, #-0x38]
    // 0x1299460: stur            x0, [fp, #-0x30]
    // 0x1299464: StoreField: r0->field_b = d0
    //     0x1299464: stur            d0, [x0, #0xb]
    // 0x1299468: ldur            x1, [fp, #-8]
    // 0x129946c: StoreField: r0->field_13 = r1
    //     0x129946c: stur            w1, [x0, #0x13]
    // 0x1299470: ldur            x1, [fp, #-0x18]
    // 0x1299474: ArrayStore: r0[0] = r1  ; List_4
    //     0x1299474: stur            w1, [x0, #0x17]
    // 0x1299478: ldur            x1, [fp, #-0x10]
    // 0x129947c: StoreField: r0->field_1b = r1
    //     0x129947c: stur            w1, [x0, #0x1b]
    // 0x1299480: ldur            x1, [fp, #-0x20]
    // 0x1299484: StoreField: r0->field_1f = r1
    //     0x1299484: stur            w1, [x0, #0x1f]
    // 0x1299488: r1 = Instance_StrokeCap
    //     0x1299488: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c18] Obj!StrokeCap@d770e1
    //     0x129948c: ldr             x1, [x1, #0xc18]
    // 0x1299490: StoreField: r0->field_23 = r1
    //     0x1299490: stur            w1, [x0, #0x23]
    // 0x1299494: r1 = Instance_EdgeInsets
    //     0x1299494: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1299498: StoreField: r0->field_2b = r1
    //     0x1299498: stur            w1, [x0, #0x2b]
    // 0x129949c: r0 = CustomPaint()
    //     0x129949c: bl              #0xc1bf8c  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0x12994a0: mov             x2, x0
    // 0x12994a4: ldur            x0, [fp, #-0x30]
    // 0x12994a8: stur            x2, [fp, #-8]
    // 0x12994ac: StoreField: r2->field_f = r0
    //     0x12994ac: stur            w0, [x2, #0xf]
    // 0x12994b0: r0 = Instance_Size
    //     0x12994b0: add             x0, PP, #0xd, lsl #12  ; [pp+0xd778] Obj!Size@d6c161
    //     0x12994b4: ldr             x0, [x0, #0x778]
    // 0x12994b8: ArrayStore: r2[0] = r0  ; List_4
    //     0x12994b8: stur            w0, [x2, #0x17]
    // 0x12994bc: r0 = false
    //     0x12994bc: add             x0, NULL, #0x30  ; false
    // 0x12994c0: StoreField: r2->field_1b = r0
    //     0x12994c0: stur            w0, [x2, #0x1b]
    // 0x12994c4: StoreField: r2->field_1f = r0
    //     0x12994c4: stur            w0, [x2, #0x1f]
    // 0x12994c8: r1 = <StackParentData>
    //     0x12994c8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x12994cc: ldr             x1, [x1, #0x8e0]
    // 0x12994d0: r0 = Positioned()
    //     0x12994d0: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x12994d4: mov             x1, x0
    // 0x12994d8: r0 = 0.000000
    //     0x12994d8: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x12994dc: stur            x1, [fp, #-0x10]
    // 0x12994e0: StoreField: r1->field_13 = r0
    //     0x12994e0: stur            w0, [x1, #0x13]
    // 0x12994e4: ArrayStore: r1[0] = r0  ; List_4
    //     0x12994e4: stur            w0, [x1, #0x17]
    // 0x12994e8: StoreField: r1->field_1b = r0
    //     0x12994e8: stur            w0, [x1, #0x1b]
    // 0x12994ec: StoreField: r1->field_1f = r0
    //     0x12994ec: stur            w0, [x1, #0x1f]
    // 0x12994f0: ldur            x0, [fp, #-8]
    // 0x12994f4: StoreField: r1->field_b = r0
    //     0x12994f4: stur            w0, [x1, #0xb]
    // 0x12994f8: ldur            x0, [fp, #-0x28]
    // 0x12994fc: LoadField: r2 = r0->field_b
    //     0x12994fc: ldur            w2, [x0, #0xb]
    // 0x1299500: DecompressPointer r2
    //     0x1299500: add             x2, x2, HEAP, lsl #32
    // 0x1299504: stur            x2, [fp, #-8]
    // 0x1299508: r0 = Padding()
    //     0x1299508: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x129950c: mov             x3, x0
    // 0x1299510: r0 = Instance_EdgeInsets
    //     0x1299510: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0x1299514: ldr             x0, [x0, #0xc10]
    // 0x1299518: stur            x3, [fp, #-0x18]
    // 0x129951c: StoreField: r3->field_f = r0
    //     0x129951c: stur            w0, [x3, #0xf]
    // 0x1299520: ldur            x0, [fp, #-8]
    // 0x1299524: StoreField: r3->field_b = r0
    //     0x1299524: stur            w0, [x3, #0xb]
    // 0x1299528: r1 = Null
    //     0x1299528: mov             x1, NULL
    // 0x129952c: r2 = 4
    //     0x129952c: movz            x2, #0x4
    // 0x1299530: r0 = AllocateArray()
    //     0x1299530: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1299534: mov             x2, x0
    // 0x1299538: ldur            x0, [fp, #-0x10]
    // 0x129953c: stur            x2, [fp, #-8]
    // 0x1299540: StoreField: r2->field_f = r0
    //     0x1299540: stur            w0, [x2, #0xf]
    // 0x1299544: ldur            x0, [fp, #-0x18]
    // 0x1299548: StoreField: r2->field_13 = r0
    //     0x1299548: stur            w0, [x2, #0x13]
    // 0x129954c: r1 = <Widget>
    //     0x129954c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1299550: r0 = AllocateGrowableArray()
    //     0x1299550: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1299554: mov             x1, x0
    // 0x1299558: ldur            x0, [fp, #-8]
    // 0x129955c: stur            x1, [fp, #-0x10]
    // 0x1299560: StoreField: r1->field_f = r0
    //     0x1299560: stur            w0, [x1, #0xf]
    // 0x1299564: r0 = 4
    //     0x1299564: movz            x0, #0x4
    // 0x1299568: StoreField: r1->field_b = r0
    //     0x1299568: stur            w0, [x1, #0xb]
    // 0x129956c: r0 = Stack()
    //     0x129956c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x1299570: r1 = Instance_AlignmentDirectional
    //     0x1299570: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0x1299574: ldr             x1, [x1, #0xd08]
    // 0x1299578: StoreField: r0->field_f = r1
    //     0x1299578: stur            w1, [x0, #0xf]
    // 0x129957c: r1 = Instance_StackFit
    //     0x129957c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x1299580: ldr             x1, [x1, #0xfa8]
    // 0x1299584: ArrayStore: r0[0] = r1  ; List_4
    //     0x1299584: stur            w1, [x0, #0x17]
    // 0x1299588: r1 = Instance_Clip
    //     0x1299588: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x129958c: ldr             x1, [x1, #0x7e0]
    // 0x1299590: StoreField: r0->field_1b = r1
    //     0x1299590: stur            w1, [x0, #0x1b]
    // 0x1299594: ldur            x1, [fp, #-0x10]
    // 0x1299598: StoreField: r0->field_b = r1
    //     0x1299598: stur            w1, [x0, #0xb]
    // 0x129959c: LeaveFrame
    //     0x129959c: mov             SP, fp
    //     0x12995a0: ldp             fp, lr, [SP], #0x10
    // 0x12995a4: ret
    //     0x12995a4: ret             
  }
}

// class id: 4886, size: 0x30, field offset: 0xc
class DashedPainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x160c8e4, size: 0x268
    // 0x160c8e4: EnterFrame
    //     0x160c8e4: stp             fp, lr, [SP, #-0x10]!
    //     0x160c8e8: mov             fp, SP
    // 0x160c8ec: AllocStack(0x60)
    //     0x160c8ec: sub             SP, SP, #0x60
    // 0x160c8f0: SetupParameters(DashedPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x160c8f0: stur            x1, [fp, #-8]
    //     0x160c8f4: stur            x2, [fp, #-0x10]
    //     0x160c8f8: stur            x3, [fp, #-0x18]
    // 0x160c8fc: CheckStackOverflow
    //     0x160c8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160c900: cmp             SP, x16
    //     0x160c904: b.ls            #0x160cb38
    // 0x160c908: r16 = Instance_EdgeInsets
    //     0x160c908: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x160c90c: r30 = Instance_EdgeInsets
    //     0x160c90c: ldr             lr, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x160c910: stp             lr, x16, [SP]
    // 0x160c914: r0 = ==()
    //     0x160c914: bl              #0x167f36c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::==
    // 0x160c918: tbnz            w0, #4, #0x160c924
    // 0x160c91c: ldur            x2, [fp, #-0x18]
    // 0x160c920: b               #0x160c9e4
    // 0x160c924: ldur            x0, [fp, #-0x10]
    // 0x160c928: r2 = Instance_EdgeInsets
    //     0x160c928: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x160c92c: LoadField: d0 = r2->field_7
    //     0x160c92c: ldur            d0, [x2, #7]
    // 0x160c930: stur            d0, [fp, #-0x48]
    // 0x160c934: LoadField: d1 = r2->field_f
    //     0x160c934: ldur            d1, [x2, #0xf]
    // 0x160c938: stur            d1, [fp, #-0x40]
    // 0x160c93c: LoadField: r1 = r0->field_7
    //     0x160c93c: ldur            w1, [x0, #7]
    // 0x160c940: DecompressPointer r1
    //     0x160c940: add             x1, x1, HEAP, lsl #32
    // 0x160c944: cmp             w1, NULL
    // 0x160c948: b.eq            #0x160cb40
    // 0x160c94c: LoadField: r3 = r1->field_7
    //     0x160c94c: ldur            x3, [x1, #7]
    // 0x160c950: ldr             x1, [x3]
    // 0x160c954: cbz             x1, #0x160cb18
    // 0x160c958: ldur            x3, [fp, #-0x18]
    // 0x160c95c: mov             x4, x1
    // 0x160c960: stur            x4, [fp, #-0x20]
    // 0x160c964: r1 = <Never>
    //     0x160c964: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160c968: r0 = Pointer()
    //     0x160c968: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160c96c: mov             x1, x0
    // 0x160c970: ldur            x0, [fp, #-0x20]
    // 0x160c974: StoreField: r1->field_7 = r0
    //     0x160c974: stur            x0, [x1, #7]
    // 0x160c978: ldur            d0, [fp, #-0x48]
    // 0x160c97c: ldur            d1, [fp, #-0x40]
    // 0x160c980: r0 = _translate$Method$FfiNative()
    //     0x160c980: bl              #0x76bdf8  ; [dart:ui] _NativeCanvas::_translate$Method$FfiNative
    // 0x160c984: ldur            x0, [fp, #-0x18]
    // 0x160c988: LoadField: d0 = r0->field_7
    //     0x160c988: ldur            d0, [x0, #7]
    // 0x160c98c: stur            d0, [fp, #-0x40]
    // 0x160c990: r1 = Instance_EdgeInsets
    //     0x160c990: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x160c994: r0 = horizontal()
    //     0x160c994: bl              #0x738e0c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::horizontal
    // 0x160c998: mov             v1.16b, v0.16b
    // 0x160c99c: ldur            d0, [fp, #-0x40]
    // 0x160c9a0: fsub            d2, d0, d1
    // 0x160c9a4: ldur            x0, [fp, #-0x18]
    // 0x160c9a8: stur            d2, [fp, #-0x48]
    // 0x160c9ac: LoadField: d0 = r0->field_f
    //     0x160c9ac: ldur            d0, [x0, #0xf]
    // 0x160c9b0: stur            d0, [fp, #-0x40]
    // 0x160c9b4: r1 = Instance_EdgeInsets
    //     0x160c9b4: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x160c9b8: r0 = vertical()
    //     0x160c9b8: bl              #0x738eb4  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::vertical
    // 0x160c9bc: mov             v1.16b, v0.16b
    // 0x160c9c0: ldur            d0, [fp, #-0x40]
    // 0x160c9c4: fsub            d2, d0, d1
    // 0x160c9c8: stur            d2, [fp, #-0x50]
    // 0x160c9cc: r0 = Size()
    //     0x160c9cc: bl              #0x63f480  ; AllocateSizeStub -> Size (size=0x18)
    // 0x160c9d0: ldur            d0, [fp, #-0x48]
    // 0x160c9d4: StoreField: r0->field_7 = d0
    //     0x160c9d4: stur            d0, [x0, #7]
    // 0x160c9d8: ldur            d0, [fp, #-0x50]
    // 0x160c9dc: StoreField: r0->field_f = d0
    //     0x160c9dc: stur            d0, [x0, #0xf]
    // 0x160c9e0: mov             x2, x0
    // 0x160c9e4: ldur            x1, [fp, #-8]
    // 0x160c9e8: ldur            x0, [fp, #-0x10]
    // 0x160c9ec: stur            x2, [fp, #-0x18]
    // 0x160c9f0: r16 = 136
    //     0x160c9f0: movz            x16, #0x88
    // 0x160c9f4: stp             x16, NULL, [SP]
    // 0x160c9f8: r0 = ByteData()
    //     0x160c9f8: bl              #0x63e644  ; [dart:typed_data] ByteData::ByteData
    // 0x160c9fc: stur            x0, [fp, #-0x28]
    // 0x160ca00: r0 = Paint()
    //     0x160ca00: bl              #0x673bd4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x160ca04: ldur            x5, [fp, #-0x28]
    // 0x160ca08: stur            x0, [fp, #-0x38]
    // 0x160ca0c: StoreField: r0->field_7 = r5
    //     0x160ca0c: stur            w5, [x0, #7]
    // 0x160ca10: ldur            x3, [fp, #-8]
    // 0x160ca14: LoadField: d0 = r3->field_b
    //     0x160ca14: ldur            d0, [x3, #0xb]
    // 0x160ca18: ArrayLoad: r4 = r5[0]  ; List_4
    //     0x160ca18: ldur            w4, [x5, #0x17]
    // 0x160ca1c: DecompressPointer r4
    //     0x160ca1c: add             x4, x4, HEAP, lsl #32
    // 0x160ca20: stur            x4, [fp, #-0x30]
    // 0x160ca24: fcvt            s1, d0
    // 0x160ca28: LoadField: r1 = r4->field_7
    //     0x160ca28: ldur            x1, [x4, #7]
    // 0x160ca2c: str             s1, [x1, #0x20]
    // 0x160ca30: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x160ca30: ldur            w2, [x3, #0x17]
    // 0x160ca34: DecompressPointer r2
    //     0x160ca34: add             x2, x2, HEAP, lsl #32
    // 0x160ca38: mov             x1, x0
    // 0x160ca3c: r0 = color=()
    //     0x160ca3c: bl              #0x6739e0  ; [dart:ui] Paint::color=
    // 0x160ca40: ldur            x0, [fp, #-0x30]
    // 0x160ca44: LoadField: r1 = r0->field_7
    //     0x160ca44: ldur            x1, [x0, #7]
    // 0x160ca48: str             wzr, [x1, #0x24]
    // 0x160ca4c: LoadField: r1 = r0->field_7
    //     0x160ca4c: ldur            x1, [x0, #7]
    // 0x160ca50: r0 = 1
    //     0x160ca50: movz            x0, #0x1
    // 0x160ca54: str             w0, [x1, #0x1c]
    // 0x160ca58: ldur            x1, [fp, #-8]
    // 0x160ca5c: ldur            x2, [fp, #-0x18]
    // 0x160ca60: r0 = _getPath()
    //     0x160ca60: bl              #0x160cb4c  ; [package:dotted_border/dotted_border.dart] DashedPainter::_getPath
    // 0x160ca64: mov             x2, x0
    // 0x160ca68: ldur            x0, [fp, #-0x38]
    // 0x160ca6c: stur            x2, [fp, #-0x18]
    // 0x160ca70: LoadField: r3 = r0->field_b
    //     0x160ca70: ldur            w3, [x0, #0xb]
    // 0x160ca74: DecompressPointer r3
    //     0x160ca74: add             x3, x3, HEAP, lsl #32
    // 0x160ca78: ldur            x0, [fp, #-0x10]
    // 0x160ca7c: stur            x3, [fp, #-8]
    // 0x160ca80: LoadField: r1 = r0->field_7
    //     0x160ca80: ldur            w1, [x0, #7]
    // 0x160ca84: DecompressPointer r1
    //     0x160ca84: add             x1, x1, HEAP, lsl #32
    // 0x160ca88: cmp             w1, NULL
    // 0x160ca8c: b.eq            #0x160cb44
    // 0x160ca90: LoadField: r4 = r1->field_7
    //     0x160ca90: ldur            x4, [x1, #7]
    // 0x160ca94: ldr             x1, [x4]
    // 0x160ca98: cbz             x1, #0x160cb28
    // 0x160ca9c: mov             x4, x1
    // 0x160caa0: stur            x4, [fp, #-0x20]
    // 0x160caa4: r1 = <Never>
    //     0x160caa4: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160caa8: r0 = Pointer()
    //     0x160caa8: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160caac: mov             x2, x0
    // 0x160cab0: ldur            x0, [fp, #-0x20]
    // 0x160cab4: stur            x2, [fp, #-0x30]
    // 0x160cab8: StoreField: r2->field_7 = r0
    //     0x160cab8: stur            x0, [x2, #7]
    // 0x160cabc: ldur            x0, [fp, #-0x18]
    // 0x160cac0: LoadField: r1 = r0->field_7
    //     0x160cac0: ldur            w1, [x0, #7]
    // 0x160cac4: DecompressPointer r1
    //     0x160cac4: add             x1, x1, HEAP, lsl #32
    // 0x160cac8: cmp             w1, NULL
    // 0x160cacc: b.eq            #0x160cb48
    // 0x160cad0: LoadField: r3 = r1->field_7
    //     0x160cad0: ldur            x3, [x1, #7]
    // 0x160cad4: ldr             x1, [x3]
    // 0x160cad8: mov             x3, x1
    // 0x160cadc: stur            x3, [fp, #-0x20]
    // 0x160cae0: r1 = <Never>
    //     0x160cae0: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160cae4: r0 = Pointer()
    //     0x160cae4: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160cae8: mov             x1, x0
    // 0x160caec: ldur            x0, [fp, #-0x20]
    // 0x160caf0: StoreField: r1->field_7 = r0
    //     0x160caf0: stur            x0, [x1, #7]
    // 0x160caf4: mov             x2, x1
    // 0x160caf8: ldur            x1, [fp, #-0x30]
    // 0x160cafc: ldur            x3, [fp, #-8]
    // 0x160cb00: ldur            x5, [fp, #-0x28]
    // 0x160cb04: r0 = __drawPath$Method$FfiNative()
    //     0x160cb04: bl              #0x76f594  ; [dart:ui] _NativeCanvas::__drawPath$Method$FfiNative
    // 0x160cb08: r0 = Null
    //     0x160cb08: mov             x0, NULL
    // 0x160cb0c: LeaveFrame
    //     0x160cb0c: mov             SP, fp
    //     0x160cb10: ldp             fp, lr, [SP], #0x10
    // 0x160cb14: ret
    //     0x160cb14: ret             
    // 0x160cb18: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160cb18: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160cb1c: str             x16, [SP]
    // 0x160cb20: r0 = _throwNew()
    //     0x160cb20: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160cb24: brk             #0
    // 0x160cb28: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160cb28: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160cb2c: str             x16, [SP]
    // 0x160cb30: r0 = _throwNew()
    //     0x160cb30: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160cb34: brk             #0
    // 0x160cb38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160cb38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160cb3c: b               #0x160c908
    // 0x160cb40: r0 = NullErrorSharedWithFPURegs()
    //     0x160cb40: bl              #0x16f7b24  ; NullErrorSharedWithFPURegsStub
    // 0x160cb44: r0 = NullErrorSharedWithoutFPURegs()
    //     0x160cb44: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x160cb48: r0 = NullErrorSharedWithoutFPURegs()
    //     0x160cb48: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _getPath(/* No info */) {
    // ** addr: 0x160cb4c, size: 0xd4
    // 0x160cb4c: EnterFrame
    //     0x160cb4c: stp             fp, lr, [SP, #-0x10]!
    //     0x160cb50: mov             fp, SP
    // 0x160cb54: AllocStack(0x18)
    //     0x160cb54: sub             SP, SP, #0x18
    // 0x160cb58: SetupParameters(DashedPainter this /* r1 => r0, fp-0x8 */)
    //     0x160cb58: mov             x0, x1
    //     0x160cb5c: stur            x1, [fp, #-8]
    // 0x160cb60: CheckStackOverflow
    //     0x160cb60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160cb64: cmp             SP, x16
    //     0x160cb68: b.ls            #0x160cc18
    // 0x160cb6c: LoadField: r1 = r0->field_1b
    //     0x160cb6c: ldur            w1, [x0, #0x1b]
    // 0x160cb70: DecompressPointer r1
    //     0x160cb70: add             x1, x1, HEAP, lsl #32
    // 0x160cb74: LoadField: r3 = r1->field_7
    //     0x160cb74: ldur            x3, [x1, #7]
    // 0x160cb78: cmp             x3, #1
    // 0x160cb7c: b.gt            #0x160cbb0
    // 0x160cb80: cmp             x3, #0
    // 0x160cb84: b.gt            #0x160cb98
    // 0x160cb88: mov             x1, x0
    // 0x160cb8c: r0 = _getCirclePath()
    //     0x160cb8c: bl              #0x160e06c  ; [package:dotted_border/dotted_border.dart] DashedPainter::_getCirclePath
    // 0x160cb90: mov             x2, x0
    // 0x160cb94: b               #0x160cbd4
    // 0x160cb98: LoadField: r3 = r0->field_1f
    //     0x160cb98: ldur            w3, [x0, #0x1f]
    // 0x160cb9c: DecompressPointer r3
    //     0x160cb9c: add             x3, x3, HEAP, lsl #32
    // 0x160cba0: mov             x1, x0
    // 0x160cba4: r0 = _getRRectPath()
    //     0x160cba4: bl              #0x160dec0  ; [package:dotted_border/dotted_border.dart] DashedPainter::_getRRectPath
    // 0x160cba8: mov             x2, x0
    // 0x160cbac: b               #0x160cbd4
    // 0x160cbb0: cmp             x3, #2
    // 0x160cbb4: b.gt            #0x160cbc8
    // 0x160cbb8: ldur            x1, [fp, #-8]
    // 0x160cbbc: r0 = _getRectPath()
    //     0x160cbbc: bl              #0x160ddf8  ; [package:dotted_border/dotted_border.dart] DashedPainter::_getRectPath
    // 0x160cbc0: mov             x2, x0
    // 0x160cbc4: b               #0x160cbd4
    // 0x160cbc8: ldur            x1, [fp, #-8]
    // 0x160cbcc: r0 = _getOvalPath()
    //     0x160cbcc: bl              #0x160dd30  ; [package:dotted_border/dotted_border.dart] DashedPainter::_getOvalPath
    // 0x160cbd0: mov             x2, x0
    // 0x160cbd4: ldur            x0, [fp, #-8]
    // 0x160cbd8: stur            x2, [fp, #-0x18]
    // 0x160cbdc: LoadField: r3 = r0->field_13
    //     0x160cbdc: ldur            w3, [x0, #0x13]
    // 0x160cbe0: DecompressPointer r3
    //     0x160cbe0: add             x3, x3, HEAP, lsl #32
    // 0x160cbe4: stur            x3, [fp, #-0x10]
    // 0x160cbe8: r1 = <double>
    //     0x160cbe8: ldr             x1, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0x160cbec: r0 = CircularIntervalList()
    //     0x160cbec: bl              #0x160dd24  ; AllocateCircularIntervalListStub -> CircularIntervalList<X0> (size=0x18)
    // 0x160cbf0: StoreField: r0->field_f = rZR
    //     0x160cbf0: stur            xzr, [x0, #0xf]
    // 0x160cbf4: ldur            x1, [fp, #-0x10]
    // 0x160cbf8: StoreField: r0->field_b = r1
    //     0x160cbf8: stur            w1, [x0, #0xb]
    // 0x160cbfc: ldur            x1, [fp, #-0x18]
    // 0x160cc00: mov             x2, x0
    // 0x160cc04: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x160cc04: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x160cc08: r0 = dashPath()
    //     0x160cc08: bl              #0x160cc20  ; [package:path_drawing/src/dash_path.dart] ::dashPath
    // 0x160cc0c: LeaveFrame
    //     0x160cc0c: mov             SP, fp
    //     0x160cc10: ldp             fp, lr, [SP], #0x10
    // 0x160cc14: ret
    //     0x160cc14: ret             
    // 0x160cc18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160cc18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160cc1c: b               #0x160cb6c
  }
  _ _getOvalPath(/* No info */) {
    // ** addr: 0x160dd30, size: 0xc8
    // 0x160dd30: EnterFrame
    //     0x160dd30: stp             fp, lr, [SP, #-0x10]!
    //     0x160dd34: mov             fp, SP
    // 0x160dd38: AllocStack(0x30)
    //     0x160dd38: sub             SP, SP, #0x30
    // 0x160dd3c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x160dd3c: stur            x2, [fp, #-8]
    // 0x160dd40: CheckStackOverflow
    //     0x160dd40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160dd44: cmp             SP, x16
    //     0x160dd48: b.ls            #0x160ddec
    // 0x160dd4c: r0 = _NativePath()
    //     0x160dd4c: bl              #0x76e63c  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x160dd50: mov             x1, x0
    // 0x160dd54: stur            x0, [fp, #-0x10]
    // 0x160dd58: r0 = __constructor$Method$FfiNative()
    //     0x160dd58: bl              #0x76edd4  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x160dd5c: ldur            x0, [fp, #-8]
    // 0x160dd60: LoadField: d0 = r0->field_7
    //     0x160dd60: ldur            d0, [x0, #7]
    // 0x160dd64: LoadField: d1 = r0->field_f
    //     0x160dd64: ldur            d1, [x0, #0xf]
    // 0x160dd68: d2 = 0.000000
    //     0x160dd68: eor             v2.16b, v2.16b, v2.16b
    // 0x160dd6c: fadd            d3, d0, d2
    // 0x160dd70: stur            d3, [fp, #-0x28]
    // 0x160dd74: fadd            d0, d1, d2
    // 0x160dd78: ldur            x0, [fp, #-0x10]
    // 0x160dd7c: stur            d0, [fp, #-0x20]
    // 0x160dd80: LoadField: r1 = r0->field_7
    //     0x160dd80: ldur            w1, [x0, #7]
    // 0x160dd84: DecompressPointer r1
    //     0x160dd84: add             x1, x1, HEAP, lsl #32
    // 0x160dd88: cmp             w1, NULL
    // 0x160dd8c: b.eq            #0x160ddf4
    // 0x160dd90: LoadField: r2 = r1->field_7
    //     0x160dd90: ldur            x2, [x1, #7]
    // 0x160dd94: ldr             x1, [x2]
    // 0x160dd98: cbz             x1, #0x160dddc
    // 0x160dd9c: mov             x2, x1
    // 0x160dda0: stur            x2, [fp, #-0x18]
    // 0x160dda4: r1 = <Never>
    //     0x160dda4: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160dda8: r0 = Pointer()
    //     0x160dda8: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160ddac: mov             x1, x0
    // 0x160ddb0: ldur            x0, [fp, #-0x18]
    // 0x160ddb4: StoreField: r1->field_7 = r0
    //     0x160ddb4: stur            x0, [x1, #7]
    // 0x160ddb8: ldur            d2, [fp, #-0x28]
    // 0x160ddbc: ldur            d3, [fp, #-0x20]
    // 0x160ddc0: d0 = 0.000000
    //     0x160ddc0: eor             v0.16b, v0.16b, v0.16b
    // 0x160ddc4: d1 = 0.000000
    //     0x160ddc4: eor             v1.16b, v1.16b, v1.16b
    // 0x160ddc8: r0 = __addOval$Method$FfiNative()
    //     0x160ddc8: bl              #0x15745f8  ; [dart:ui] _NativePath::__addOval$Method$FfiNative
    // 0x160ddcc: ldur            x0, [fp, #-0x10]
    // 0x160ddd0: LeaveFrame
    //     0x160ddd0: mov             SP, fp
    //     0x160ddd4: ldp             fp, lr, [SP], #0x10
    // 0x160ddd8: ret
    //     0x160ddd8: ret             
    // 0x160dddc: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160dddc: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160dde0: str             x16, [SP]
    // 0x160dde4: r0 = _throwNew()
    //     0x160dde4: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160dde8: brk             #0
    // 0x160ddec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160ddec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160ddf0: b               #0x160dd4c
    // 0x160ddf4: r0 = NullErrorSharedWithFPURegs()
    //     0x160ddf4: bl              #0x16f7b24  ; NullErrorSharedWithFPURegsStub
  }
  _ _getRectPath(/* No info */) {
    // ** addr: 0x160ddf8, size: 0xc8
    // 0x160ddf8: EnterFrame
    //     0x160ddf8: stp             fp, lr, [SP, #-0x10]!
    //     0x160ddfc: mov             fp, SP
    // 0x160de00: AllocStack(0x30)
    //     0x160de00: sub             SP, SP, #0x30
    // 0x160de04: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x160de04: stur            x2, [fp, #-8]
    // 0x160de08: CheckStackOverflow
    //     0x160de08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160de0c: cmp             SP, x16
    //     0x160de10: b.ls            #0x160deb4
    // 0x160de14: r0 = _NativePath()
    //     0x160de14: bl              #0x76e63c  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x160de18: mov             x1, x0
    // 0x160de1c: stur            x0, [fp, #-0x10]
    // 0x160de20: r0 = __constructor$Method$FfiNative()
    //     0x160de20: bl              #0x76edd4  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x160de24: ldur            x0, [fp, #-8]
    // 0x160de28: LoadField: d0 = r0->field_7
    //     0x160de28: ldur            d0, [x0, #7]
    // 0x160de2c: LoadField: d1 = r0->field_f
    //     0x160de2c: ldur            d1, [x0, #0xf]
    // 0x160de30: d2 = 0.000000
    //     0x160de30: eor             v2.16b, v2.16b, v2.16b
    // 0x160de34: fadd            d3, d0, d2
    // 0x160de38: stur            d3, [fp, #-0x28]
    // 0x160de3c: fadd            d0, d1, d2
    // 0x160de40: ldur            x0, [fp, #-0x10]
    // 0x160de44: stur            d0, [fp, #-0x20]
    // 0x160de48: LoadField: r1 = r0->field_7
    //     0x160de48: ldur            w1, [x0, #7]
    // 0x160de4c: DecompressPointer r1
    //     0x160de4c: add             x1, x1, HEAP, lsl #32
    // 0x160de50: cmp             w1, NULL
    // 0x160de54: b.eq            #0x160debc
    // 0x160de58: LoadField: r2 = r1->field_7
    //     0x160de58: ldur            x2, [x1, #7]
    // 0x160de5c: ldr             x1, [x2]
    // 0x160de60: cbz             x1, #0x160dea4
    // 0x160de64: mov             x2, x1
    // 0x160de68: stur            x2, [fp, #-0x18]
    // 0x160de6c: r1 = <Never>
    //     0x160de6c: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160de70: r0 = Pointer()
    //     0x160de70: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160de74: mov             x1, x0
    // 0x160de78: ldur            x0, [fp, #-0x18]
    // 0x160de7c: StoreField: r1->field_7 = r0
    //     0x160de7c: stur            x0, [x1, #7]
    // 0x160de80: ldur            d2, [fp, #-0x28]
    // 0x160de84: ldur            d3, [fp, #-0x20]
    // 0x160de88: d0 = 0.000000
    //     0x160de88: eor             v0.16b, v0.16b, v0.16b
    // 0x160de8c: d1 = 0.000000
    //     0x160de8c: eor             v1.16b, v1.16b, v1.16b
    // 0x160de90: r0 = __addRect$Method$FfiNative()
    //     0x160de90: bl              #0x156d650  ; [dart:ui] _NativePath::__addRect$Method$FfiNative
    // 0x160de94: ldur            x0, [fp, #-0x10]
    // 0x160de98: LeaveFrame
    //     0x160de98: mov             SP, fp
    //     0x160de9c: ldp             fp, lr, [SP], #0x10
    // 0x160dea0: ret
    //     0x160dea0: ret             
    // 0x160dea4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160dea4: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160dea8: str             x16, [SP]
    // 0x160deac: r0 = _throwNew()
    //     0x160deac: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160deb0: brk             #0
    // 0x160deb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160deb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160deb8: b               #0x160de14
    // 0x160debc: r0 = NullErrorSharedWithFPURegs()
    //     0x160debc: bl              #0x16f7b24  ; NullErrorSharedWithFPURegsStub
  }
  _ _getRRectPath(/* No info */) {
    // ** addr: 0x160dec0, size: 0x1ac
    // 0x160dec0: EnterFrame
    //     0x160dec0: stp             fp, lr, [SP, #-0x10]!
    //     0x160dec4: mov             fp, SP
    // 0x160dec8: AllocStack(0x38)
    //     0x160dec8: sub             SP, SP, #0x38
    // 0x160decc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x160decc: stur            x2, [fp, #-8]
    //     0x160ded0: stur            x3, [fp, #-0x10]
    // 0x160ded4: CheckStackOverflow
    //     0x160ded4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160ded8: cmp             SP, x16
    //     0x160dedc: b.ls            #0x160e060
    // 0x160dee0: r0 = _NativePath()
    //     0x160dee0: bl              #0x76e63c  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x160dee4: mov             x1, x0
    // 0x160dee8: stur            x0, [fp, #-0x18]
    // 0x160deec: r0 = __constructor$Method$FfiNative()
    //     0x160deec: bl              #0x76edd4  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x160def0: ldur            x0, [fp, #-8]
    // 0x160def4: LoadField: d0 = r0->field_7
    //     0x160def4: ldur            d0, [x0, #7]
    // 0x160def8: LoadField: d1 = r0->field_f
    //     0x160def8: ldur            d1, [x0, #0xf]
    // 0x160defc: d2 = 0.000000
    //     0x160defc: eor             v2.16b, v2.16b, v2.16b
    // 0x160df00: fadd            d3, d0, d2
    // 0x160df04: stur            d3, [fp, #-0x30]
    // 0x160df08: fadd            d0, d1, d2
    // 0x160df0c: stur            d0, [fp, #-0x28]
    // 0x160df10: r0 = Rect()
    //     0x160df10: bl              #0x63f3e4  ; AllocateRectStub -> Rect (size=0x28)
    // 0x160df14: stur            x0, [fp, #-8]
    // 0x160df18: StoreField: r0->field_7 = rZR
    //     0x160df18: stur            xzr, [x0, #7]
    // 0x160df1c: StoreField: r0->field_f = rZR
    //     0x160df1c: stur            xzr, [x0, #0xf]
    // 0x160df20: ldur            d0, [fp, #-0x30]
    // 0x160df24: ArrayStore: r0[0] = d0  ; List_8
    //     0x160df24: stur            d0, [x0, #0x17]
    // 0x160df28: ldur            d0, [fp, #-0x28]
    // 0x160df2c: StoreField: r0->field_1f = d0
    //     0x160df2c: stur            d0, [x0, #0x1f]
    // 0x160df30: r1 = <RRect>
    //     0x160df30: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4ba70] TypeArguments: <RRect>
    //     0x160df34: ldr             x1, [x1, #0xa70]
    // 0x160df38: r0 = RRect()
    //     0x160df38: bl              #0x76b604  ; AllocateRRectStub -> RRect (size=0x6c)
    // 0x160df3c: mov             x1, x0
    // 0x160df40: ldur            x2, [fp, #-8]
    // 0x160df44: ldur            x3, [fp, #-0x10]
    // 0x160df48: stur            x0, [fp, #-8]
    // 0x160df4c: r0 = RRect.fromRectAndRadius()
    //     0x160df4c: bl              #0x76afd0  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x160df50: ldur            x0, [fp, #-8]
    // 0x160df54: LoadField: d0 = r0->field_b
    //     0x160df54: ldur            d0, [x0, #0xb]
    // 0x160df58: fcvt            s1, d0
    // 0x160df5c: stur            d1, [fp, #-0x28]
    // 0x160df60: r4 = 24
    //     0x160df60: movz            x4, #0x18
    // 0x160df64: r0 = AllocateFloat32Array()
    //     0x160df64: bl              #0x16f6894  ; AllocateFloat32ArrayStub
    // 0x160df68: ldur            d0, [fp, #-0x28]
    // 0x160df6c: stur            x0, [fp, #-0x10]
    // 0x160df70: ArrayStore: r0[0] = d0  ; List_8
    //     0x160df70: stur            s0, [x0, #0x17]
    // 0x160df74: ldur            x1, [fp, #-8]
    // 0x160df78: LoadField: d0 = r1->field_13
    //     0x160df78: ldur            d0, [x1, #0x13]
    // 0x160df7c: fcvt            s1, d0
    // 0x160df80: StoreField: r0->field_1b = d1
    //     0x160df80: stur            s1, [x0, #0x1b]
    // 0x160df84: LoadField: d0 = r1->field_1b
    //     0x160df84: ldur            d0, [x1, #0x1b]
    // 0x160df88: fcvt            s1, d0
    // 0x160df8c: StoreField: r0->field_1f = d1
    //     0x160df8c: stur            s1, [x0, #0x1f]
    // 0x160df90: LoadField: d0 = r1->field_23
    //     0x160df90: ldur            d0, [x1, #0x23]
    // 0x160df94: fcvt            s1, d0
    // 0x160df98: StoreField: r0->field_23 = d1
    //     0x160df98: stur            s1, [x0, #0x23]
    // 0x160df9c: LoadField: d0 = r1->field_2b
    //     0x160df9c: ldur            d0, [x1, #0x2b]
    // 0x160dfa0: fcvt            s1, d0
    // 0x160dfa4: StoreField: r0->field_27 = d1
    //     0x160dfa4: stur            s1, [x0, #0x27]
    // 0x160dfa8: LoadField: d0 = r1->field_33
    //     0x160dfa8: ldur            d0, [x1, #0x33]
    // 0x160dfac: fcvt            s1, d0
    // 0x160dfb0: StoreField: r0->field_2b = d1
    //     0x160dfb0: stur            s1, [x0, #0x2b]
    // 0x160dfb4: LoadField: d0 = r1->field_3b
    //     0x160dfb4: ldur            d0, [x1, #0x3b]
    // 0x160dfb8: fcvt            s1, d0
    // 0x160dfbc: StoreField: r0->field_2f = d1
    //     0x160dfbc: stur            s1, [x0, #0x2f]
    // 0x160dfc0: LoadField: d0 = r1->field_43
    //     0x160dfc0: ldur            d0, [x1, #0x43]
    // 0x160dfc4: fcvt            s1, d0
    // 0x160dfc8: StoreField: r0->field_33 = d1
    //     0x160dfc8: stur            s1, [x0, #0x33]
    // 0x160dfcc: LoadField: d0 = r1->field_4b
    //     0x160dfcc: ldur            d0, [x1, #0x4b]
    // 0x160dfd0: fcvt            s1, d0
    // 0x160dfd4: StoreField: r0->field_37 = d1
    //     0x160dfd4: stur            s1, [x0, #0x37]
    // 0x160dfd8: LoadField: d0 = r1->field_53
    //     0x160dfd8: ldur            d0, [x1, #0x53]
    // 0x160dfdc: fcvt            s1, d0
    // 0x160dfe0: StoreField: r0->field_3b = d1
    //     0x160dfe0: stur            s1, [x0, #0x3b]
    // 0x160dfe4: LoadField: d0 = r1->field_5b
    //     0x160dfe4: ldur            d0, [x1, #0x5b]
    // 0x160dfe8: fcvt            s1, d0
    // 0x160dfec: StoreField: r0->field_3f = d1
    //     0x160dfec: stur            s1, [x0, #0x3f]
    // 0x160dff0: LoadField: d0 = r1->field_63
    //     0x160dff0: ldur            d0, [x1, #0x63]
    // 0x160dff4: fcvt            s1, d0
    // 0x160dff8: StoreField: r0->field_43 = d1
    //     0x160dff8: stur            s1, [x0, #0x43]
    // 0x160dffc: ldur            x2, [fp, #-0x18]
    // 0x160e000: LoadField: r1 = r2->field_7
    //     0x160e000: ldur            w1, [x2, #7]
    // 0x160e004: DecompressPointer r1
    //     0x160e004: add             x1, x1, HEAP, lsl #32
    // 0x160e008: cmp             w1, NULL
    // 0x160e00c: b.eq            #0x160e068
    // 0x160e010: LoadField: r3 = r1->field_7
    //     0x160e010: ldur            x3, [x1, #7]
    // 0x160e014: ldr             x1, [x3]
    // 0x160e018: cbz             x1, #0x160e050
    // 0x160e01c: mov             x3, x1
    // 0x160e020: stur            x3, [fp, #-0x20]
    // 0x160e024: r1 = <Never>
    //     0x160e024: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160e028: r0 = Pointer()
    //     0x160e028: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160e02c: mov             x1, x0
    // 0x160e030: ldur            x0, [fp, #-0x20]
    // 0x160e034: StoreField: r1->field_7 = r0
    //     0x160e034: stur            x0, [x1, #7]
    // 0x160e038: ldur            x2, [fp, #-0x10]
    // 0x160e03c: r0 = __addRRect$Method$FfiNative()
    //     0x160e03c: bl              #0x76ec20  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0x160e040: ldur            x0, [fp, #-0x18]
    // 0x160e044: LeaveFrame
    //     0x160e044: mov             SP, fp
    //     0x160e048: ldp             fp, lr, [SP], #0x10
    // 0x160e04c: ret
    //     0x160e04c: ret             
    // 0x160e050: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160e050: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160e054: str             x16, [SP]
    // 0x160e058: r0 = _throwNew()
    //     0x160e058: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160e05c: brk             #0
    // 0x160e060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160e060: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160e064: b               #0x160dee0
    // 0x160e068: r0 = NullErrorSharedWithoutFPURegs()
    //     0x160e068: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _getCirclePath(/* No info */) {
    // ** addr: 0x160e06c, size: 0x234
    // 0x160e06c: EnterFrame
    //     0x160e06c: stp             fp, lr, [SP, #-0x10]!
    //     0x160e070: mov             fp, SP
    // 0x160e074: AllocStack(0x50)
    //     0x160e074: sub             SP, SP, #0x50
    // 0x160e078: SetupParameters(DashedPainter this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x160e078: mov             x0, x1
    //     0x160e07c: mov             x1, x2
    // 0x160e080: CheckStackOverflow
    //     0x160e080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160e084: cmp             SP, x16
    //     0x160e088: b.ls            #0x160e294
    // 0x160e08c: LoadField: d0 = r1->field_7
    //     0x160e08c: ldur            d0, [x1, #7]
    // 0x160e090: stur            d0, [fp, #-0x30]
    // 0x160e094: LoadField: d1 = r1->field_f
    //     0x160e094: ldur            d1, [x1, #0xf]
    // 0x160e098: stur            d1, [fp, #-0x28]
    // 0x160e09c: r0 = shortestSide()
    //     0x160e09c: bl              #0x160e2a0  ; [dart:ui] Size::shortestSide
    // 0x160e0a0: stur            d0, [fp, #-0x38]
    // 0x160e0a4: r0 = _NativePath()
    //     0x160e0a4: bl              #0x76e63c  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x160e0a8: mov             x1, x0
    // 0x160e0ac: stur            x0, [fp, #-8]
    // 0x160e0b0: r0 = __constructor$Method$FfiNative()
    //     0x160e0b0: bl              #0x76edd4  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x160e0b4: ldur            d0, [fp, #-0x38]
    // 0x160e0b8: ldur            d1, [fp, #-0x30]
    // 0x160e0bc: fcmp            d1, d0
    // 0x160e0c0: b.le            #0x160e0d8
    // 0x160e0c4: d2 = 2.000000
    //     0x160e0c4: fmov            d2, #2.00000000
    // 0x160e0c8: fsub            d3, d1, d0
    // 0x160e0cc: fdiv            d1, d3, d2
    // 0x160e0d0: mov             v3.16b, v1.16b
    // 0x160e0d4: b               #0x160e0e0
    // 0x160e0d8: d2 = 2.000000
    //     0x160e0d8: fmov            d2, #2.00000000
    // 0x160e0dc: d3 = 0.000000
    //     0x160e0dc: eor             v3.16b, v3.16b, v3.16b
    // 0x160e0e0: ldur            d1, [fp, #-0x28]
    // 0x160e0e4: stur            d3, [fp, #-0x48]
    // 0x160e0e8: fcmp            d1, d0
    // 0x160e0ec: b.le            #0x160e0fc
    // 0x160e0f0: fsub            d4, d1, d0
    // 0x160e0f4: fdiv            d1, d4, d2
    // 0x160e0f8: b               #0x160e100
    // 0x160e0fc: d1 = 0.000000
    //     0x160e0fc: eor             v1.16b, v1.16b, v1.16b
    // 0x160e100: ldur            x0, [fp, #-8]
    // 0x160e104: stur            d1, [fp, #-0x40]
    // 0x160e108: fadd            d4, d3, d0
    // 0x160e10c: stur            d4, [fp, #-0x30]
    // 0x160e110: fadd            d5, d1, d0
    // 0x160e114: stur            d5, [fp, #-0x28]
    // 0x160e118: r0 = Rect()
    //     0x160e118: bl              #0x63f3e4  ; AllocateRectStub -> Rect (size=0x28)
    // 0x160e11c: ldur            d0, [fp, #-0x48]
    // 0x160e120: stur            x0, [fp, #-0x10]
    // 0x160e124: StoreField: r0->field_7 = d0
    //     0x160e124: stur            d0, [x0, #7]
    // 0x160e128: ldur            d0, [fp, #-0x40]
    // 0x160e12c: StoreField: r0->field_f = d0
    //     0x160e12c: stur            d0, [x0, #0xf]
    // 0x160e130: ldur            d0, [fp, #-0x30]
    // 0x160e134: ArrayStore: r0[0] = d0  ; List_8
    //     0x160e134: stur            d0, [x0, #0x17]
    // 0x160e138: ldur            d0, [fp, #-0x28]
    // 0x160e13c: StoreField: r0->field_1f = d0
    //     0x160e13c: stur            d0, [x0, #0x1f]
    // 0x160e140: ldur            d0, [fp, #-0x38]
    // 0x160e144: d1 = 2.000000
    //     0x160e144: fmov            d1, #2.00000000
    // 0x160e148: fdiv            d2, d0, d1
    // 0x160e14c: stur            d2, [fp, #-0x28]
    // 0x160e150: r0 = Radius()
    //     0x160e150: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x160e154: ldur            d0, [fp, #-0x28]
    // 0x160e158: stur            x0, [fp, #-0x18]
    // 0x160e15c: StoreField: r0->field_7 = d0
    //     0x160e15c: stur            d0, [x0, #7]
    // 0x160e160: StoreField: r0->field_f = d0
    //     0x160e160: stur            d0, [x0, #0xf]
    // 0x160e164: r1 = <RRect>
    //     0x160e164: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4ba70] TypeArguments: <RRect>
    //     0x160e168: ldr             x1, [x1, #0xa70]
    // 0x160e16c: r0 = RRect()
    //     0x160e16c: bl              #0x76b604  ; AllocateRRectStub -> RRect (size=0x6c)
    // 0x160e170: mov             x1, x0
    // 0x160e174: ldur            x2, [fp, #-0x10]
    // 0x160e178: ldur            x3, [fp, #-0x18]
    // 0x160e17c: stur            x0, [fp, #-0x10]
    // 0x160e180: r0 = RRect.fromRectAndRadius()
    //     0x160e180: bl              #0x76afd0  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x160e184: ldur            x0, [fp, #-0x10]
    // 0x160e188: LoadField: d0 = r0->field_b
    //     0x160e188: ldur            d0, [x0, #0xb]
    // 0x160e18c: fcvt            s1, d0
    // 0x160e190: stur            d1, [fp, #-0x28]
    // 0x160e194: r4 = 24
    //     0x160e194: movz            x4, #0x18
    // 0x160e198: r0 = AllocateFloat32Array()
    //     0x160e198: bl              #0x16f6894  ; AllocateFloat32ArrayStub
    // 0x160e19c: ldur            d0, [fp, #-0x28]
    // 0x160e1a0: stur            x0, [fp, #-0x18]
    // 0x160e1a4: ArrayStore: r0[0] = d0  ; List_8
    //     0x160e1a4: stur            s0, [x0, #0x17]
    // 0x160e1a8: ldur            x1, [fp, #-0x10]
    // 0x160e1ac: LoadField: d0 = r1->field_13
    //     0x160e1ac: ldur            d0, [x1, #0x13]
    // 0x160e1b0: fcvt            s1, d0
    // 0x160e1b4: StoreField: r0->field_1b = d1
    //     0x160e1b4: stur            s1, [x0, #0x1b]
    // 0x160e1b8: LoadField: d0 = r1->field_1b
    //     0x160e1b8: ldur            d0, [x1, #0x1b]
    // 0x160e1bc: fcvt            s1, d0
    // 0x160e1c0: StoreField: r0->field_1f = d1
    //     0x160e1c0: stur            s1, [x0, #0x1f]
    // 0x160e1c4: LoadField: d0 = r1->field_23
    //     0x160e1c4: ldur            d0, [x1, #0x23]
    // 0x160e1c8: fcvt            s1, d0
    // 0x160e1cc: StoreField: r0->field_23 = d1
    //     0x160e1cc: stur            s1, [x0, #0x23]
    // 0x160e1d0: LoadField: d0 = r1->field_2b
    //     0x160e1d0: ldur            d0, [x1, #0x2b]
    // 0x160e1d4: fcvt            s1, d0
    // 0x160e1d8: StoreField: r0->field_27 = d1
    //     0x160e1d8: stur            s1, [x0, #0x27]
    // 0x160e1dc: LoadField: d0 = r1->field_33
    //     0x160e1dc: ldur            d0, [x1, #0x33]
    // 0x160e1e0: fcvt            s1, d0
    // 0x160e1e4: StoreField: r0->field_2b = d1
    //     0x160e1e4: stur            s1, [x0, #0x2b]
    // 0x160e1e8: LoadField: d0 = r1->field_3b
    //     0x160e1e8: ldur            d0, [x1, #0x3b]
    // 0x160e1ec: fcvt            s1, d0
    // 0x160e1f0: StoreField: r0->field_2f = d1
    //     0x160e1f0: stur            s1, [x0, #0x2f]
    // 0x160e1f4: LoadField: d0 = r1->field_43
    //     0x160e1f4: ldur            d0, [x1, #0x43]
    // 0x160e1f8: fcvt            s1, d0
    // 0x160e1fc: StoreField: r0->field_33 = d1
    //     0x160e1fc: stur            s1, [x0, #0x33]
    // 0x160e200: LoadField: d0 = r1->field_4b
    //     0x160e200: ldur            d0, [x1, #0x4b]
    // 0x160e204: fcvt            s1, d0
    // 0x160e208: StoreField: r0->field_37 = d1
    //     0x160e208: stur            s1, [x0, #0x37]
    // 0x160e20c: LoadField: d0 = r1->field_53
    //     0x160e20c: ldur            d0, [x1, #0x53]
    // 0x160e210: fcvt            s1, d0
    // 0x160e214: StoreField: r0->field_3b = d1
    //     0x160e214: stur            s1, [x0, #0x3b]
    // 0x160e218: LoadField: d0 = r1->field_5b
    //     0x160e218: ldur            d0, [x1, #0x5b]
    // 0x160e21c: fcvt            s1, d0
    // 0x160e220: StoreField: r0->field_3f = d1
    //     0x160e220: stur            s1, [x0, #0x3f]
    // 0x160e224: LoadField: d0 = r1->field_63
    //     0x160e224: ldur            d0, [x1, #0x63]
    // 0x160e228: fcvt            s1, d0
    // 0x160e22c: StoreField: r0->field_43 = d1
    //     0x160e22c: stur            s1, [x0, #0x43]
    // 0x160e230: ldur            x2, [fp, #-8]
    // 0x160e234: LoadField: r1 = r2->field_7
    //     0x160e234: ldur            w1, [x2, #7]
    // 0x160e238: DecompressPointer r1
    //     0x160e238: add             x1, x1, HEAP, lsl #32
    // 0x160e23c: cmp             w1, NULL
    // 0x160e240: b.eq            #0x160e29c
    // 0x160e244: LoadField: r3 = r1->field_7
    //     0x160e244: ldur            x3, [x1, #7]
    // 0x160e248: ldr             x1, [x3]
    // 0x160e24c: cbz             x1, #0x160e284
    // 0x160e250: mov             x3, x1
    // 0x160e254: stur            x3, [fp, #-0x20]
    // 0x160e258: r1 = <Never>
    //     0x160e258: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160e25c: r0 = Pointer()
    //     0x160e25c: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160e260: mov             x1, x0
    // 0x160e264: ldur            x0, [fp, #-0x20]
    // 0x160e268: StoreField: r1->field_7 = r0
    //     0x160e268: stur            x0, [x1, #7]
    // 0x160e26c: ldur            x2, [fp, #-0x18]
    // 0x160e270: r0 = __addRRect$Method$FfiNative()
    //     0x160e270: bl              #0x76ec20  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0x160e274: ldur            x0, [fp, #-8]
    // 0x160e278: LeaveFrame
    //     0x160e278: mov             SP, fp
    //     0x160e27c: ldp             fp, lr, [SP], #0x10
    // 0x160e280: ret
    //     0x160e280: ret             
    // 0x160e284: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160e284: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160e288: str             x16, [SP]
    // 0x160e28c: r0 = _throwNew()
    //     0x160e28c: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160e290: brk             #0
    // 0x160e294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x160e294: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160e298: b               #0x160e08c
    // 0x160e29c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x160e29c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  _ shouldRepaint(/* No info */) {
    // ** addr: 0x1618b98, size: 0x114
    // 0x1618b98: EnterFrame
    //     0x1618b98: stp             fp, lr, [SP, #-0x10]!
    //     0x1618b9c: mov             fp, SP
    // 0x1618ba0: AllocStack(0x20)
    //     0x1618ba0: sub             SP, SP, #0x20
    // 0x1618ba4: SetupParameters(DashedPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x1618ba4: mov             x4, x1
    //     0x1618ba8: mov             x3, x2
    //     0x1618bac: stur            x1, [fp, #-8]
    //     0x1618bb0: stur            x2, [fp, #-0x10]
    // 0x1618bb4: CheckStackOverflow
    //     0x1618bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1618bb8: cmp             SP, x16
    //     0x1618bbc: b.ls            #0x1618ca4
    // 0x1618bc0: mov             x0, x3
    // 0x1618bc4: r2 = Null
    //     0x1618bc4: mov             x2, NULL
    // 0x1618bc8: r1 = Null
    //     0x1618bc8: mov             x1, NULL
    // 0x1618bcc: r4 = 60
    //     0x1618bcc: movz            x4, #0x3c
    // 0x1618bd0: branchIfSmi(r0, 0x1618bdc)
    //     0x1618bd0: tbz             w0, #0, #0x1618bdc
    // 0x1618bd4: r4 = LoadClassIdInstr(r0)
    //     0x1618bd4: ldur            x4, [x0, #-1]
    //     0x1618bd8: ubfx            x4, x4, #0xc, #0x14
    // 0x1618bdc: r17 = 4886
    //     0x1618bdc: movz            x17, #0x1316
    // 0x1618be0: cmp             x4, x17
    // 0x1618be4: b.eq            #0x1618bfc
    // 0x1618be8: r8 = DashedPainter
    //     0x1618be8: add             x8, PP, #0x62, lsl #12  ; [pp+0x620d8] Type: DashedPainter
    //     0x1618bec: ldr             x8, [x8, #0xd8]
    // 0x1618bf0: r3 = Null
    //     0x1618bf0: add             x3, PP, #0x62, lsl #12  ; [pp+0x620e0] Null
    //     0x1618bf4: ldr             x3, [x3, #0xe0]
    // 0x1618bf8: r0 = DefaultTypeTest()
    //     0x1618bf8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1618bfc: ldur            x0, [fp, #-0x10]
    // 0x1618c00: LoadField: d0 = r0->field_b
    //     0x1618c00: ldur            d0, [x0, #0xb]
    // 0x1618c04: ldur            x1, [fp, #-8]
    // 0x1618c08: LoadField: d1 = r1->field_b
    //     0x1618c08: ldur            d1, [x1, #0xb]
    // 0x1618c0c: fcmp            d0, d1
    // 0x1618c10: b.ne            #0x1618c64
    // 0x1618c14: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x1618c14: ldur            w2, [x0, #0x17]
    // 0x1618c18: DecompressPointer r2
    //     0x1618c18: add             x2, x2, HEAP, lsl #32
    // 0x1618c1c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x1618c1c: ldur            w3, [x1, #0x17]
    // 0x1618c20: DecompressPointer r3
    //     0x1618c20: add             x3, x3, HEAP, lsl #32
    // 0x1618c24: stp             x3, x2, [SP]
    // 0x1618c28: r0 = ==()
    //     0x1618c28: bl              #0x1655f84  ; [dart:ui] Color::==
    // 0x1618c2c: tbnz            w0, #4, #0x1618c64
    // 0x1618c30: ldur            x1, [fp, #-8]
    // 0x1618c34: ldur            x0, [fp, #-0x10]
    // 0x1618c38: LoadField: r2 = r0->field_13
    //     0x1618c38: ldur            w2, [x0, #0x13]
    // 0x1618c3c: DecompressPointer r2
    //     0x1618c3c: add             x2, x2, HEAP, lsl #32
    // 0x1618c40: LoadField: r3 = r1->field_13
    //     0x1618c40: ldur            w3, [x1, #0x13]
    // 0x1618c44: DecompressPointer r3
    //     0x1618c44: add             x3, x3, HEAP, lsl #32
    // 0x1618c48: cmp             w2, w3
    // 0x1618c4c: b.ne            #0x1618c64
    // 0x1618c50: r16 = Instance_EdgeInsets
    //     0x1618c50: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1618c54: r30 = Instance_EdgeInsets
    //     0x1618c54: ldr             lr, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1618c58: stp             lr, x16, [SP]
    // 0x1618c5c: r0 = ==()
    //     0x1618c5c: bl              #0x167f36c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::==
    // 0x1618c60: tbz             w0, #4, #0x1618c6c
    // 0x1618c64: r0 = true
    //     0x1618c64: add             x0, NULL, #0x20  ; true
    // 0x1618c68: b               #0x1618c98
    // 0x1618c6c: ldur            x2, [fp, #-8]
    // 0x1618c70: ldur            x1, [fp, #-0x10]
    // 0x1618c74: LoadField: r3 = r1->field_1b
    //     0x1618c74: ldur            w3, [x1, #0x1b]
    // 0x1618c78: DecompressPointer r3
    //     0x1618c78: add             x3, x3, HEAP, lsl #32
    // 0x1618c7c: LoadField: r1 = r2->field_1b
    //     0x1618c7c: ldur            w1, [x2, #0x1b]
    // 0x1618c80: DecompressPointer r1
    //     0x1618c80: add             x1, x1, HEAP, lsl #32
    // 0x1618c84: cmp             w3, w1
    // 0x1618c88: r16 = true
    //     0x1618c88: add             x16, NULL, #0x20  ; true
    // 0x1618c8c: r17 = false
    //     0x1618c8c: add             x17, NULL, #0x30  ; false
    // 0x1618c90: csel            x2, x16, x17, ne
    // 0x1618c94: mov             x0, x2
    // 0x1618c98: LeaveFrame
    //     0x1618c98: mov             SP, fp
    //     0x1618c9c: ldp             fp, lr, [SP], #0x10
    // 0x1618ca0: ret
    //     0x1618ca0: ret             
    // 0x1618ca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1618ca4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1618ca8: b               #0x1618bc0
  }
}

// class id: 7076, size: 0x14, field offset: 0x14
enum BorderType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
