// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/bag_text.dart

// class id: 1049222, size: 0x8
class :: {
}

// class id: 3469, size: 0x1c, field offset: 0x14
class _BagTextState extends State<dynamic> {

  late String value; // offset: 0x14
  late String customizedValue; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xab4f34, size: 0x854
    // 0xab4f34: EnterFrame
    //     0xab4f34: stp             fp, lr, [SP, #-0x10]!
    //     0xab4f38: mov             fp, SP
    // 0xab4f3c: AllocStack(0x50)
    //     0xab4f3c: sub             SP, SP, #0x50
    // 0xab4f40: SetupParameters(_BagTextState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xab4f40: mov             x0, x1
    //     0xab4f44: stur            x1, [fp, #-8]
    //     0xab4f48: mov             x1, x2
    //     0xab4f4c: stur            x2, [fp, #-0x10]
    // 0xab4f50: CheckStackOverflow
    //     0xab4f50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4f54: cmp             SP, x16
    //     0xab4f58: b.ls            #0xab5754
    // 0xab4f5c: LoadField: r2 = r0->field_b
    //     0xab4f5c: ldur            w2, [x0, #0xb]
    // 0xab4f60: DecompressPointer r2
    //     0xab4f60: add             x2, x2, HEAP, lsl #32
    // 0xab4f64: cmp             w2, NULL
    // 0xab4f68: b.eq            #0xab575c
    // 0xab4f6c: LoadField: r3 = r2->field_b
    //     0xab4f6c: ldur            w3, [x2, #0xb]
    // 0xab4f70: DecompressPointer r3
    //     0xab4f70: add             x3, x3, HEAP, lsl #32
    // 0xab4f74: cmp             w3, NULL
    // 0xab4f78: b.ne            #0xab4f84
    // 0xab4f7c: r2 = Null
    //     0xab4f7c: mov             x2, NULL
    // 0xab4f80: b               #0xab4fb0
    // 0xab4f84: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xab4f84: ldur            w2, [x3, #0x17]
    // 0xab4f88: DecompressPointer r2
    //     0xab4f88: add             x2, x2, HEAP, lsl #32
    // 0xab4f8c: cmp             w2, NULL
    // 0xab4f90: b.ne            #0xab4f9c
    // 0xab4f94: r2 = Null
    //     0xab4f94: mov             x2, NULL
    // 0xab4f98: b               #0xab4fb0
    // 0xab4f9c: LoadField: r3 = r2->field_7
    //     0xab4f9c: ldur            w3, [x2, #7]
    // 0xab4fa0: cbnz            w3, #0xab4fac
    // 0xab4fa4: r2 = false
    //     0xab4fa4: add             x2, NULL, #0x30  ; false
    // 0xab4fa8: b               #0xab4fb0
    // 0xab4fac: r2 = true
    //     0xab4fac: add             x2, NULL, #0x20  ; true
    // 0xab4fb0: cmp             w2, NULL
    // 0xab4fb4: b.ne            #0xab5000
    // 0xab4fb8: mov             x1, x0
    // 0xab4fbc: r3 = 6
    //     0xab4fbc: movz            x3, #0x6
    // 0xab4fc0: r2 = 4
    //     0xab4fc0: movz            x2, #0x4
    // 0xab4fc4: r7 = Instance_CrossAxisAlignment
    //     0xab4fc4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab4fc8: ldr             x7, [x7, #0xa18]
    // 0xab4fcc: r5 = Instance_MainAxisAlignment
    //     0xab4fcc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab4fd0: ldr             x5, [x5, #0xa08]
    // 0xab4fd4: r6 = Instance_MainAxisSize
    //     0xab4fd4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab4fd8: ldr             x6, [x6, #0xa10]
    // 0xab4fdc: r4 = Instance_Axis
    //     0xab4fdc: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab4fe0: r8 = Instance_VerticalDirection
    //     0xab4fe0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab4fe4: ldr             x8, [x8, #0xa20]
    // 0xab4fe8: r0 = Instance__DeferringMouseCursor
    //     0xab4fe8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab4fec: r9 = Instance_Clip
    //     0xab4fec: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab4ff0: ldr             x9, [x9, #0x38]
    // 0xab4ff4: d1 = 0.500000
    //     0xab4ff4: fmov            d1, #0.50000000
    // 0xab4ff8: d0 = inf
    //     0xab4ff8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab4ffc: b               #0xab53c8
    // 0xab5000: tbnz            w2, #4, #0xab5384
    // 0xab5004: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xab5004: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab5008: ldr             x0, [x0, #0x1c80]
    //     0xab500c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab5010: cmp             w0, w16
    //     0xab5014: b.ne            #0xab5020
    //     0xab5018: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xab501c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xab5020: r0 = GetNavigation.size()
    //     0xab5020: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xab5024: LoadField: d0 = r0->field_7
    //     0xab5024: ldur            d0, [x0, #7]
    // 0xab5028: d1 = 0.500000
    //     0xab5028: fmov            d1, #0.50000000
    // 0xab502c: fmul            d2, d0, d1
    // 0xab5030: stur            d2, [fp, #-0x40]
    // 0xab5034: r0 = BoxConstraints()
    //     0xab5034: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xab5038: stur            x0, [fp, #-0x20]
    // 0xab503c: StoreField: r0->field_7 = rZR
    //     0xab503c: stur            xzr, [x0, #7]
    // 0xab5040: ldur            d0, [fp, #-0x40]
    // 0xab5044: StoreField: r0->field_f = d0
    //     0xab5044: stur            d0, [x0, #0xf]
    // 0xab5048: ArrayStore: r0[0] = rZR  ; List_8
    //     0xab5048: stur            xzr, [x0, #0x17]
    // 0xab504c: d0 = inf
    //     0xab504c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab5050: StoreField: r0->field_1f = d0
    //     0xab5050: stur            d0, [x0, #0x1f]
    // 0xab5054: ldur            x2, [fp, #-8]
    // 0xab5058: LoadField: r1 = r2->field_b
    //     0xab5058: ldur            w1, [x2, #0xb]
    // 0xab505c: DecompressPointer r1
    //     0xab505c: add             x1, x1, HEAP, lsl #32
    // 0xab5060: cmp             w1, NULL
    // 0xab5064: b.eq            #0xab5760
    // 0xab5068: LoadField: r3 = r1->field_b
    //     0xab5068: ldur            w3, [x1, #0xb]
    // 0xab506c: DecompressPointer r3
    //     0xab506c: add             x3, x3, HEAP, lsl #32
    // 0xab5070: cmp             w3, NULL
    // 0xab5074: b.ne            #0xab5080
    // 0xab5078: r1 = Null
    //     0xab5078: mov             x1, NULL
    // 0xab507c: b               #0xab5088
    // 0xab5080: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xab5080: ldur            w1, [x3, #0x17]
    // 0xab5084: DecompressPointer r1
    //     0xab5084: add             x1, x1, HEAP, lsl #32
    // 0xab5088: cmp             w1, NULL
    // 0xab508c: b.ne            #0xab5098
    // 0xab5090: r3 = ""
    //     0xab5090: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab5094: b               #0xab509c
    // 0xab5098: mov             x3, x1
    // 0xab509c: ldur            x1, [fp, #-0x10]
    // 0xab50a0: stur            x3, [fp, #-0x18]
    // 0xab50a4: r0 = of()
    //     0xab50a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab50a8: LoadField: r1 = r0->field_87
    //     0xab50a8: ldur            w1, [x0, #0x87]
    // 0xab50ac: DecompressPointer r1
    //     0xab50ac: add             x1, x1, HEAP, lsl #32
    // 0xab50b0: LoadField: r0 = r1->field_7
    //     0xab50b0: ldur            w0, [x1, #7]
    // 0xab50b4: DecompressPointer r0
    //     0xab50b4: add             x0, x0, HEAP, lsl #32
    // 0xab50b8: r16 = 16.000000
    //     0xab50b8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab50bc: ldr             x16, [x16, #0x188]
    // 0xab50c0: r30 = Instance_Color
    //     0xab50c0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab50c4: stp             lr, x16, [SP]
    // 0xab50c8: mov             x1, x0
    // 0xab50cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab50cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab50d0: ldr             x4, [x4, #0xaa0]
    // 0xab50d4: r0 = copyWith()
    //     0xab50d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab50d8: stur            x0, [fp, #-0x28]
    // 0xab50dc: r0 = TextSpan()
    //     0xab50dc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab50e0: mov             x3, x0
    // 0xab50e4: ldur            x0, [fp, #-0x18]
    // 0xab50e8: stur            x3, [fp, #-0x30]
    // 0xab50ec: StoreField: r3->field_b = r0
    //     0xab50ec: stur            w0, [x3, #0xb]
    // 0xab50f0: r0 = Instance__DeferringMouseCursor
    //     0xab50f0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab50f4: ArrayStore: r3[0] = r0  ; List_4
    //     0xab50f4: stur            w0, [x3, #0x17]
    // 0xab50f8: ldur            x1, [fp, #-0x28]
    // 0xab50fc: StoreField: r3->field_7 = r1
    //     0xab50fc: stur            w1, [x3, #7]
    // 0xab5100: r1 = Null
    //     0xab5100: mov             x1, NULL
    // 0xab5104: r2 = 6
    //     0xab5104: movz            x2, #0x6
    // 0xab5108: r0 = AllocateArray()
    //     0xab5108: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab510c: r16 = " : "
    //     0xab510c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xab5110: ldr             x16, [x16, #0x680]
    // 0xab5114: StoreField: r0->field_f = r16
    //     0xab5114: stur            w16, [x0, #0xf]
    // 0xab5118: ldur            x1, [fp, #-8]
    // 0xab511c: LoadField: r2 = r1->field_13
    //     0xab511c: ldur            w2, [x1, #0x13]
    // 0xab5120: DecompressPointer r2
    //     0xab5120: add             x2, x2, HEAP, lsl #32
    // 0xab5124: r16 = Sentinel
    //     0xab5124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab5128: cmp             w2, w16
    // 0xab512c: b.eq            #0xab5764
    // 0xab5130: StoreField: r0->field_13 = r2
    //     0xab5130: stur            w2, [x0, #0x13]
    // 0xab5134: r16 = " "
    //     0xab5134: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xab5138: ArrayStore: r0[0] = r16  ; List_4
    //     0xab5138: stur            w16, [x0, #0x17]
    // 0xab513c: str             x0, [SP]
    // 0xab5140: r0 = _interpolate()
    //     0xab5140: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab5144: ldur            x1, [fp, #-0x10]
    // 0xab5148: stur            x0, [fp, #-0x18]
    // 0xab514c: r0 = of()
    //     0xab514c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5150: LoadField: r1 = r0->field_87
    //     0xab5150: ldur            w1, [x0, #0x87]
    // 0xab5154: DecompressPointer r1
    //     0xab5154: add             x1, x1, HEAP, lsl #32
    // 0xab5158: LoadField: r0 = r1->field_2b
    //     0xab5158: ldur            w0, [x1, #0x2b]
    // 0xab515c: DecompressPointer r0
    //     0xab515c: add             x0, x0, HEAP, lsl #32
    // 0xab5160: r16 = 14.000000
    //     0xab5160: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab5164: ldr             x16, [x16, #0x1d8]
    // 0xab5168: r30 = Instance_Color
    //     0xab5168: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab516c: stp             lr, x16, [SP]
    // 0xab5170: mov             x1, x0
    // 0xab5174: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5174: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5178: ldr             x4, [x4, #0xaa0]
    // 0xab517c: r0 = copyWith()
    //     0xab517c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5180: stur            x0, [fp, #-0x28]
    // 0xab5184: r0 = TextSpan()
    //     0xab5184: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab5188: mov             x3, x0
    // 0xab518c: ldur            x0, [fp, #-0x18]
    // 0xab5190: stur            x3, [fp, #-0x38]
    // 0xab5194: StoreField: r3->field_b = r0
    //     0xab5194: stur            w0, [x3, #0xb]
    // 0xab5198: r0 = Instance__DeferringMouseCursor
    //     0xab5198: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab519c: ArrayStore: r3[0] = r0  ; List_4
    //     0xab519c: stur            w0, [x3, #0x17]
    // 0xab51a0: ldur            x1, [fp, #-0x28]
    // 0xab51a4: StoreField: r3->field_7 = r1
    //     0xab51a4: stur            w1, [x3, #7]
    // 0xab51a8: r1 = Null
    //     0xab51a8: mov             x1, NULL
    // 0xab51ac: r2 = 4
    //     0xab51ac: movz            x2, #0x4
    // 0xab51b0: r0 = AllocateArray()
    //     0xab51b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab51b4: mov             x2, x0
    // 0xab51b8: ldur            x0, [fp, #-0x30]
    // 0xab51bc: stur            x2, [fp, #-0x18]
    // 0xab51c0: StoreField: r2->field_f = r0
    //     0xab51c0: stur            w0, [x2, #0xf]
    // 0xab51c4: ldur            x0, [fp, #-0x38]
    // 0xab51c8: StoreField: r2->field_13 = r0
    //     0xab51c8: stur            w0, [x2, #0x13]
    // 0xab51cc: r1 = <InlineSpan>
    //     0xab51cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xab51d0: ldr             x1, [x1, #0xe40]
    // 0xab51d4: r0 = AllocateGrowableArray()
    //     0xab51d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab51d8: mov             x1, x0
    // 0xab51dc: ldur            x0, [fp, #-0x18]
    // 0xab51e0: stur            x1, [fp, #-0x28]
    // 0xab51e4: StoreField: r1->field_f = r0
    //     0xab51e4: stur            w0, [x1, #0xf]
    // 0xab51e8: r2 = 4
    //     0xab51e8: movz            x2, #0x4
    // 0xab51ec: StoreField: r1->field_b = r2
    //     0xab51ec: stur            w2, [x1, #0xb]
    // 0xab51f0: r0 = TextSpan()
    //     0xab51f0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab51f4: mov             x1, x0
    // 0xab51f8: ldur            x0, [fp, #-0x28]
    // 0xab51fc: stur            x1, [fp, #-0x18]
    // 0xab5200: StoreField: r1->field_f = r0
    //     0xab5200: stur            w0, [x1, #0xf]
    // 0xab5204: r0 = Instance__DeferringMouseCursor
    //     0xab5204: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab5208: ArrayStore: r1[0] = r0  ; List_4
    //     0xab5208: stur            w0, [x1, #0x17]
    // 0xab520c: r0 = RichText()
    //     0xab520c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xab5210: mov             x1, x0
    // 0xab5214: ldur            x2, [fp, #-0x18]
    // 0xab5218: stur            x0, [fp, #-0x18]
    // 0xab521c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab521c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab5220: r0 = RichText()
    //     0xab5220: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xab5224: r0 = ConstrainedBox()
    //     0xab5224: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xab5228: mov             x2, x0
    // 0xab522c: ldur            x0, [fp, #-0x20]
    // 0xab5230: stur            x2, [fp, #-0x28]
    // 0xab5234: StoreField: r2->field_f = r0
    //     0xab5234: stur            w0, [x2, #0xf]
    // 0xab5238: ldur            x0, [fp, #-0x18]
    // 0xab523c: StoreField: r2->field_b = r0
    //     0xab523c: stur            w0, [x2, #0xb]
    // 0xab5240: ldur            x1, [fp, #-8]
    // 0xab5244: LoadField: r0 = r1->field_b
    //     0xab5244: ldur            w0, [x1, #0xb]
    // 0xab5248: DecompressPointer r0
    //     0xab5248: add             x0, x0, HEAP, lsl #32
    // 0xab524c: cmp             w0, NULL
    // 0xab5250: b.eq            #0xab5770
    // 0xab5254: LoadField: r1 = r0->field_b
    //     0xab5254: ldur            w1, [x0, #0xb]
    // 0xab5258: DecompressPointer r1
    //     0xab5258: add             x1, x1, HEAP, lsl #32
    // 0xab525c: cmp             w1, NULL
    // 0xab5260: b.ne            #0xab526c
    // 0xab5264: r0 = Null
    //     0xab5264: mov             x0, NULL
    // 0xab5268: b               #0xab5274
    // 0xab526c: LoadField: r0 = r1->field_2b
    //     0xab526c: ldur            w0, [x1, #0x2b]
    // 0xab5270: DecompressPointer r0
    //     0xab5270: add             x0, x0, HEAP, lsl #32
    // 0xab5274: cmp             w0, NULL
    // 0xab5278: b.ne            #0xab5280
    // 0xab527c: r0 = ""
    //     0xab527c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab5280: ldur            x1, [fp, #-0x10]
    // 0xab5284: stur            x0, [fp, #-0x18]
    // 0xab5288: r0 = of()
    //     0xab5288: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab528c: LoadField: r1 = r0->field_87
    //     0xab528c: ldur            w1, [x0, #0x87]
    // 0xab5290: DecompressPointer r1
    //     0xab5290: add             x1, x1, HEAP, lsl #32
    // 0xab5294: LoadField: r0 = r1->field_2b
    //     0xab5294: ldur            w0, [x1, #0x2b]
    // 0xab5298: DecompressPointer r0
    //     0xab5298: add             x0, x0, HEAP, lsl #32
    // 0xab529c: r16 = 14.000000
    //     0xab529c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab52a0: ldr             x16, [x16, #0x1d8]
    // 0xab52a4: r30 = Instance_Color
    //     0xab52a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab52a8: stp             lr, x16, [SP]
    // 0xab52ac: mov             x1, x0
    // 0xab52b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab52b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab52b4: ldr             x4, [x4, #0xaa0]
    // 0xab52b8: r0 = copyWith()
    //     0xab52b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab52bc: stur            x0, [fp, #-0x20]
    // 0xab52c0: r0 = Text()
    //     0xab52c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab52c4: mov             x3, x0
    // 0xab52c8: ldur            x0, [fp, #-0x18]
    // 0xab52cc: stur            x3, [fp, #-0x30]
    // 0xab52d0: StoreField: r3->field_b = r0
    //     0xab52d0: stur            w0, [x3, #0xb]
    // 0xab52d4: ldur            x0, [fp, #-0x20]
    // 0xab52d8: StoreField: r3->field_13 = r0
    //     0xab52d8: stur            w0, [x3, #0x13]
    // 0xab52dc: r1 = Null
    //     0xab52dc: mov             x1, NULL
    // 0xab52e0: r2 = 6
    //     0xab52e0: movz            x2, #0x6
    // 0xab52e4: r0 = AllocateArray()
    //     0xab52e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab52e8: mov             x2, x0
    // 0xab52ec: ldur            x0, [fp, #-0x28]
    // 0xab52f0: stur            x2, [fp, #-0x18]
    // 0xab52f4: StoreField: r2->field_f = r0
    //     0xab52f4: stur            w0, [x2, #0xf]
    // 0xab52f8: r16 = Instance_Spacer
    //     0xab52f8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab52fc: ldr             x16, [x16, #0xf0]
    // 0xab5300: StoreField: r2->field_13 = r16
    //     0xab5300: stur            w16, [x2, #0x13]
    // 0xab5304: ldur            x0, [fp, #-0x30]
    // 0xab5308: ArrayStore: r2[0] = r0  ; List_4
    //     0xab5308: stur            w0, [x2, #0x17]
    // 0xab530c: r1 = <Widget>
    //     0xab530c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab5310: r0 = AllocateGrowableArray()
    //     0xab5310: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab5314: mov             x1, x0
    // 0xab5318: ldur            x0, [fp, #-0x18]
    // 0xab531c: stur            x1, [fp, #-0x20]
    // 0xab5320: StoreField: r1->field_f = r0
    //     0xab5320: stur            w0, [x1, #0xf]
    // 0xab5324: r3 = 6
    //     0xab5324: movz            x3, #0x6
    // 0xab5328: StoreField: r1->field_b = r3
    //     0xab5328: stur            w3, [x1, #0xb]
    // 0xab532c: r0 = Row()
    //     0xab532c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab5330: r4 = Instance_Axis
    //     0xab5330: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab5334: StoreField: r0->field_f = r4
    //     0xab5334: stur            w4, [x0, #0xf]
    // 0xab5338: r5 = Instance_MainAxisAlignment
    //     0xab5338: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab533c: ldr             x5, [x5, #0xa08]
    // 0xab5340: StoreField: r0->field_13 = r5
    //     0xab5340: stur            w5, [x0, #0x13]
    // 0xab5344: r6 = Instance_MainAxisSize
    //     0xab5344: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab5348: ldr             x6, [x6, #0xa10]
    // 0xab534c: ArrayStore: r0[0] = r6  ; List_4
    //     0xab534c: stur            w6, [x0, #0x17]
    // 0xab5350: r7 = Instance_CrossAxisAlignment
    //     0xab5350: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab5354: ldr             x7, [x7, #0xa18]
    // 0xab5358: StoreField: r0->field_1b = r7
    //     0xab5358: stur            w7, [x0, #0x1b]
    // 0xab535c: r8 = Instance_VerticalDirection
    //     0xab535c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab5360: ldr             x8, [x8, #0xa20]
    // 0xab5364: StoreField: r0->field_23 = r8
    //     0xab5364: stur            w8, [x0, #0x23]
    // 0xab5368: r9 = Instance_Clip
    //     0xab5368: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab536c: ldr             x9, [x9, #0x38]
    // 0xab5370: StoreField: r0->field_2b = r9
    //     0xab5370: stur            w9, [x0, #0x2b]
    // 0xab5374: StoreField: r0->field_2f = rZR
    //     0xab5374: stur            xzr, [x0, #0x2f]
    // 0xab5378: ldur            x1, [fp, #-0x20]
    // 0xab537c: StoreField: r0->field_b = r1
    //     0xab537c: stur            w1, [x0, #0xb]
    // 0xab5380: b               #0xab5748
    // 0xab5384: mov             x1, x0
    // 0xab5388: r3 = 6
    //     0xab5388: movz            x3, #0x6
    // 0xab538c: r2 = 4
    //     0xab538c: movz            x2, #0x4
    // 0xab5390: r7 = Instance_CrossAxisAlignment
    //     0xab5390: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab5394: ldr             x7, [x7, #0xa18]
    // 0xab5398: r5 = Instance_MainAxisAlignment
    //     0xab5398: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab539c: ldr             x5, [x5, #0xa08]
    // 0xab53a0: r6 = Instance_MainAxisSize
    //     0xab53a0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab53a4: ldr             x6, [x6, #0xa10]
    // 0xab53a8: r4 = Instance_Axis
    //     0xab53a8: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab53ac: r8 = Instance_VerticalDirection
    //     0xab53ac: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab53b0: ldr             x8, [x8, #0xa20]
    // 0xab53b4: r0 = Instance__DeferringMouseCursor
    //     0xab53b4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab53b8: r9 = Instance_Clip
    //     0xab53b8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab53bc: ldr             x9, [x9, #0x38]
    // 0xab53c0: d1 = 0.500000
    //     0xab53c0: fmov            d1, #0.50000000
    // 0xab53c4: d0 = inf
    //     0xab53c4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab53c8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xab53c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab53cc: ldr             x0, [x0, #0x1c80]
    //     0xab53d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab53d4: cmp             w0, w16
    //     0xab53d8: b.ne            #0xab53e4
    //     0xab53dc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xab53e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xab53e4: r0 = GetNavigation.size()
    //     0xab53e4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xab53e8: LoadField: d0 = r0->field_7
    //     0xab53e8: ldur            d0, [x0, #7]
    // 0xab53ec: d1 = 0.500000
    //     0xab53ec: fmov            d1, #0.50000000
    // 0xab53f0: fmul            d2, d0, d1
    // 0xab53f4: stur            d2, [fp, #-0x40]
    // 0xab53f8: r0 = BoxConstraints()
    //     0xab53f8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xab53fc: stur            x0, [fp, #-0x20]
    // 0xab5400: StoreField: r0->field_7 = rZR
    //     0xab5400: stur            xzr, [x0, #7]
    // 0xab5404: ldur            d0, [fp, #-0x40]
    // 0xab5408: StoreField: r0->field_f = d0
    //     0xab5408: stur            d0, [x0, #0xf]
    // 0xab540c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xab540c: stur            xzr, [x0, #0x17]
    // 0xab5410: d0 = inf
    //     0xab5410: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xab5414: StoreField: r0->field_1f = d0
    //     0xab5414: stur            d0, [x0, #0x1f]
    // 0xab5418: ldur            x2, [fp, #-8]
    // 0xab541c: LoadField: r1 = r2->field_b
    //     0xab541c: ldur            w1, [x2, #0xb]
    // 0xab5420: DecompressPointer r1
    //     0xab5420: add             x1, x1, HEAP, lsl #32
    // 0xab5424: cmp             w1, NULL
    // 0xab5428: b.eq            #0xab5774
    // 0xab542c: LoadField: r3 = r1->field_f
    //     0xab542c: ldur            w3, [x1, #0xf]
    // 0xab5430: DecompressPointer r3
    //     0xab5430: add             x3, x3, HEAP, lsl #32
    // 0xab5434: cmp             w3, NULL
    // 0xab5438: b.ne            #0xab5444
    // 0xab543c: r1 = Null
    //     0xab543c: mov             x1, NULL
    // 0xab5440: b               #0xab544c
    // 0xab5444: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xab5444: ldur            w1, [x3, #0x17]
    // 0xab5448: DecompressPointer r1
    //     0xab5448: add             x1, x1, HEAP, lsl #32
    // 0xab544c: cmp             w1, NULL
    // 0xab5450: b.ne            #0xab545c
    // 0xab5454: r3 = ""
    //     0xab5454: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab5458: b               #0xab5460
    // 0xab545c: mov             x3, x1
    // 0xab5460: ldur            x1, [fp, #-0x10]
    // 0xab5464: stur            x3, [fp, #-0x18]
    // 0xab5468: r0 = of()
    //     0xab5468: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab546c: LoadField: r1 = r0->field_87
    //     0xab546c: ldur            w1, [x0, #0x87]
    // 0xab5470: DecompressPointer r1
    //     0xab5470: add             x1, x1, HEAP, lsl #32
    // 0xab5474: LoadField: r0 = r1->field_7
    //     0xab5474: ldur            w0, [x1, #7]
    // 0xab5478: DecompressPointer r0
    //     0xab5478: add             x0, x0, HEAP, lsl #32
    // 0xab547c: r16 = 16.000000
    //     0xab547c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5480: ldr             x16, [x16, #0x188]
    // 0xab5484: r30 = Instance_Color
    //     0xab5484: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab5488: stp             lr, x16, [SP]
    // 0xab548c: mov             x1, x0
    // 0xab5490: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5490: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5494: ldr             x4, [x4, #0xaa0]
    // 0xab5498: r0 = copyWith()
    //     0xab5498: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab549c: stur            x0, [fp, #-0x28]
    // 0xab54a0: r0 = TextSpan()
    //     0xab54a0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab54a4: mov             x3, x0
    // 0xab54a8: ldur            x0, [fp, #-0x18]
    // 0xab54ac: stur            x3, [fp, #-0x30]
    // 0xab54b0: StoreField: r3->field_b = r0
    //     0xab54b0: stur            w0, [x3, #0xb]
    // 0xab54b4: r0 = Instance__DeferringMouseCursor
    //     0xab54b4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab54b8: ArrayStore: r3[0] = r0  ; List_4
    //     0xab54b8: stur            w0, [x3, #0x17]
    // 0xab54bc: ldur            x1, [fp, #-0x28]
    // 0xab54c0: StoreField: r3->field_7 = r1
    //     0xab54c0: stur            w1, [x3, #7]
    // 0xab54c4: r1 = Null
    //     0xab54c4: mov             x1, NULL
    // 0xab54c8: r2 = 6
    //     0xab54c8: movz            x2, #0x6
    // 0xab54cc: r0 = AllocateArray()
    //     0xab54cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab54d0: r16 = " : "
    //     0xab54d0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xab54d4: ldr             x16, [x16, #0x680]
    // 0xab54d8: StoreField: r0->field_f = r16
    //     0xab54d8: stur            w16, [x0, #0xf]
    // 0xab54dc: ldur            x1, [fp, #-8]
    // 0xab54e0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xab54e0: ldur            w2, [x1, #0x17]
    // 0xab54e4: DecompressPointer r2
    //     0xab54e4: add             x2, x2, HEAP, lsl #32
    // 0xab54e8: r16 = Sentinel
    //     0xab54e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab54ec: cmp             w2, w16
    // 0xab54f0: b.eq            #0xab5778
    // 0xab54f4: StoreField: r0->field_13 = r2
    //     0xab54f4: stur            w2, [x0, #0x13]
    // 0xab54f8: r16 = " "
    //     0xab54f8: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xab54fc: ArrayStore: r0[0] = r16  ; List_4
    //     0xab54fc: stur            w16, [x0, #0x17]
    // 0xab5500: str             x0, [SP]
    // 0xab5504: r0 = _interpolate()
    //     0xab5504: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab5508: ldur            x1, [fp, #-0x10]
    // 0xab550c: stur            x0, [fp, #-0x18]
    // 0xab5510: r0 = of()
    //     0xab5510: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5514: LoadField: r1 = r0->field_87
    //     0xab5514: ldur            w1, [x0, #0x87]
    // 0xab5518: DecompressPointer r1
    //     0xab5518: add             x1, x1, HEAP, lsl #32
    // 0xab551c: LoadField: r0 = r1->field_2b
    //     0xab551c: ldur            w0, [x1, #0x2b]
    // 0xab5520: DecompressPointer r0
    //     0xab5520: add             x0, x0, HEAP, lsl #32
    // 0xab5524: r16 = 14.000000
    //     0xab5524: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab5528: ldr             x16, [x16, #0x1d8]
    // 0xab552c: r30 = Instance_Color
    //     0xab552c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab5530: stp             lr, x16, [SP]
    // 0xab5534: mov             x1, x0
    // 0xab5538: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5538: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab553c: ldr             x4, [x4, #0xaa0]
    // 0xab5540: r0 = copyWith()
    //     0xab5540: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5544: stur            x0, [fp, #-0x28]
    // 0xab5548: r0 = TextSpan()
    //     0xab5548: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab554c: mov             x3, x0
    // 0xab5550: ldur            x0, [fp, #-0x18]
    // 0xab5554: stur            x3, [fp, #-0x38]
    // 0xab5558: StoreField: r3->field_b = r0
    //     0xab5558: stur            w0, [x3, #0xb]
    // 0xab555c: r0 = Instance__DeferringMouseCursor
    //     0xab555c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab5560: ArrayStore: r3[0] = r0  ; List_4
    //     0xab5560: stur            w0, [x3, #0x17]
    // 0xab5564: ldur            x1, [fp, #-0x28]
    // 0xab5568: StoreField: r3->field_7 = r1
    //     0xab5568: stur            w1, [x3, #7]
    // 0xab556c: r1 = Null
    //     0xab556c: mov             x1, NULL
    // 0xab5570: r2 = 4
    //     0xab5570: movz            x2, #0x4
    // 0xab5574: r0 = AllocateArray()
    //     0xab5574: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab5578: mov             x2, x0
    // 0xab557c: ldur            x0, [fp, #-0x30]
    // 0xab5580: stur            x2, [fp, #-0x18]
    // 0xab5584: StoreField: r2->field_f = r0
    //     0xab5584: stur            w0, [x2, #0xf]
    // 0xab5588: ldur            x0, [fp, #-0x38]
    // 0xab558c: StoreField: r2->field_13 = r0
    //     0xab558c: stur            w0, [x2, #0x13]
    // 0xab5590: r1 = <InlineSpan>
    //     0xab5590: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xab5594: ldr             x1, [x1, #0xe40]
    // 0xab5598: r0 = AllocateGrowableArray()
    //     0xab5598: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab559c: mov             x1, x0
    // 0xab55a0: ldur            x0, [fp, #-0x18]
    // 0xab55a4: stur            x1, [fp, #-0x28]
    // 0xab55a8: StoreField: r1->field_f = r0
    //     0xab55a8: stur            w0, [x1, #0xf]
    // 0xab55ac: r0 = 4
    //     0xab55ac: movz            x0, #0x4
    // 0xab55b0: StoreField: r1->field_b = r0
    //     0xab55b0: stur            w0, [x1, #0xb]
    // 0xab55b4: r0 = TextSpan()
    //     0xab55b4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab55b8: mov             x1, x0
    // 0xab55bc: ldur            x0, [fp, #-0x28]
    // 0xab55c0: stur            x1, [fp, #-0x18]
    // 0xab55c4: StoreField: r1->field_f = r0
    //     0xab55c4: stur            w0, [x1, #0xf]
    // 0xab55c8: r0 = Instance__DeferringMouseCursor
    //     0xab55c8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab55cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xab55cc: stur            w0, [x1, #0x17]
    // 0xab55d0: r0 = RichText()
    //     0xab55d0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xab55d4: mov             x1, x0
    // 0xab55d8: ldur            x2, [fp, #-0x18]
    // 0xab55dc: stur            x0, [fp, #-0x18]
    // 0xab55e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab55e0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab55e4: r0 = RichText()
    //     0xab55e4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xab55e8: r0 = ConstrainedBox()
    //     0xab55e8: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xab55ec: mov             x2, x0
    // 0xab55f0: ldur            x0, [fp, #-0x20]
    // 0xab55f4: stur            x2, [fp, #-0x28]
    // 0xab55f8: StoreField: r2->field_f = r0
    //     0xab55f8: stur            w0, [x2, #0xf]
    // 0xab55fc: ldur            x0, [fp, #-0x18]
    // 0xab5600: StoreField: r2->field_b = r0
    //     0xab5600: stur            w0, [x2, #0xb]
    // 0xab5604: ldur            x0, [fp, #-8]
    // 0xab5608: LoadField: r1 = r0->field_b
    //     0xab5608: ldur            w1, [x0, #0xb]
    // 0xab560c: DecompressPointer r1
    //     0xab560c: add             x1, x1, HEAP, lsl #32
    // 0xab5610: cmp             w1, NULL
    // 0xab5614: b.eq            #0xab5784
    // 0xab5618: LoadField: r0 = r1->field_f
    //     0xab5618: ldur            w0, [x1, #0xf]
    // 0xab561c: DecompressPointer r0
    //     0xab561c: add             x0, x0, HEAP, lsl #32
    // 0xab5620: cmp             w0, NULL
    // 0xab5624: b.ne            #0xab5630
    // 0xab5628: r0 = Null
    //     0xab5628: mov             x0, NULL
    // 0xab562c: b               #0xab563c
    // 0xab5630: LoadField: r1 = r0->field_2b
    //     0xab5630: ldur            w1, [x0, #0x2b]
    // 0xab5634: DecompressPointer r1
    //     0xab5634: add             x1, x1, HEAP, lsl #32
    // 0xab5638: mov             x0, x1
    // 0xab563c: cmp             w0, NULL
    // 0xab5640: b.ne            #0xab5648
    // 0xab5644: r0 = ""
    //     0xab5644: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab5648: ldur            x1, [fp, #-0x10]
    // 0xab564c: stur            x0, [fp, #-8]
    // 0xab5650: r0 = of()
    //     0xab5650: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5654: LoadField: r1 = r0->field_87
    //     0xab5654: ldur            w1, [x0, #0x87]
    // 0xab5658: DecompressPointer r1
    //     0xab5658: add             x1, x1, HEAP, lsl #32
    // 0xab565c: LoadField: r0 = r1->field_2b
    //     0xab565c: ldur            w0, [x1, #0x2b]
    // 0xab5660: DecompressPointer r0
    //     0xab5660: add             x0, x0, HEAP, lsl #32
    // 0xab5664: r16 = 14.000000
    //     0xab5664: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab5668: ldr             x16, [x16, #0x1d8]
    // 0xab566c: r30 = Instance_Color
    //     0xab566c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab5670: stp             lr, x16, [SP]
    // 0xab5674: mov             x1, x0
    // 0xab5678: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5678: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab567c: ldr             x4, [x4, #0xaa0]
    // 0xab5680: r0 = copyWith()
    //     0xab5680: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5684: stur            x0, [fp, #-0x10]
    // 0xab5688: r0 = Text()
    //     0xab5688: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab568c: mov             x3, x0
    // 0xab5690: ldur            x0, [fp, #-8]
    // 0xab5694: stur            x3, [fp, #-0x18]
    // 0xab5698: StoreField: r3->field_b = r0
    //     0xab5698: stur            w0, [x3, #0xb]
    // 0xab569c: ldur            x0, [fp, #-0x10]
    // 0xab56a0: StoreField: r3->field_13 = r0
    //     0xab56a0: stur            w0, [x3, #0x13]
    // 0xab56a4: r1 = Null
    //     0xab56a4: mov             x1, NULL
    // 0xab56a8: r2 = 6
    //     0xab56a8: movz            x2, #0x6
    // 0xab56ac: r0 = AllocateArray()
    //     0xab56ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab56b0: mov             x2, x0
    // 0xab56b4: ldur            x0, [fp, #-0x28]
    // 0xab56b8: stur            x2, [fp, #-8]
    // 0xab56bc: StoreField: r2->field_f = r0
    //     0xab56bc: stur            w0, [x2, #0xf]
    // 0xab56c0: r16 = Instance_Spacer
    //     0xab56c0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab56c4: ldr             x16, [x16, #0xf0]
    // 0xab56c8: StoreField: r2->field_13 = r16
    //     0xab56c8: stur            w16, [x2, #0x13]
    // 0xab56cc: ldur            x0, [fp, #-0x18]
    // 0xab56d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xab56d0: stur            w0, [x2, #0x17]
    // 0xab56d4: r1 = <Widget>
    //     0xab56d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab56d8: r0 = AllocateGrowableArray()
    //     0xab56d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab56dc: mov             x1, x0
    // 0xab56e0: ldur            x0, [fp, #-8]
    // 0xab56e4: stur            x1, [fp, #-0x10]
    // 0xab56e8: StoreField: r1->field_f = r0
    //     0xab56e8: stur            w0, [x1, #0xf]
    // 0xab56ec: r0 = 6
    //     0xab56ec: movz            x0, #0x6
    // 0xab56f0: StoreField: r1->field_b = r0
    //     0xab56f0: stur            w0, [x1, #0xb]
    // 0xab56f4: r0 = Row()
    //     0xab56f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab56f8: r1 = Instance_Axis
    //     0xab56f8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab56fc: StoreField: r0->field_f = r1
    //     0xab56fc: stur            w1, [x0, #0xf]
    // 0xab5700: r1 = Instance_MainAxisAlignment
    //     0xab5700: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab5704: ldr             x1, [x1, #0xa08]
    // 0xab5708: StoreField: r0->field_13 = r1
    //     0xab5708: stur            w1, [x0, #0x13]
    // 0xab570c: r1 = Instance_MainAxisSize
    //     0xab570c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab5710: ldr             x1, [x1, #0xa10]
    // 0xab5714: ArrayStore: r0[0] = r1  ; List_4
    //     0xab5714: stur            w1, [x0, #0x17]
    // 0xab5718: r1 = Instance_CrossAxisAlignment
    //     0xab5718: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab571c: ldr             x1, [x1, #0xa18]
    // 0xab5720: StoreField: r0->field_1b = r1
    //     0xab5720: stur            w1, [x0, #0x1b]
    // 0xab5724: r1 = Instance_VerticalDirection
    //     0xab5724: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab5728: ldr             x1, [x1, #0xa20]
    // 0xab572c: StoreField: r0->field_23 = r1
    //     0xab572c: stur            w1, [x0, #0x23]
    // 0xab5730: r1 = Instance_Clip
    //     0xab5730: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab5734: ldr             x1, [x1, #0x38]
    // 0xab5738: StoreField: r0->field_2b = r1
    //     0xab5738: stur            w1, [x0, #0x2b]
    // 0xab573c: StoreField: r0->field_2f = rZR
    //     0xab573c: stur            xzr, [x0, #0x2f]
    // 0xab5740: ldur            x1, [fp, #-0x10]
    // 0xab5744: StoreField: r0->field_b = r1
    //     0xab5744: stur            w1, [x0, #0xb]
    // 0xab5748: LeaveFrame
    //     0xab5748: mov             SP, fp
    //     0xab574c: ldp             fp, lr, [SP], #0x10
    // 0xab5750: ret
    //     0xab5750: ret             
    // 0xab5754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5754: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab5758: b               #0xab4f5c
    // 0xab575c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab575c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab5760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab5760: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab5764: r9 = value
    //     0xab5764: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6afe8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xab5768: ldr             x9, [x9, #0xfe8]
    // 0xab576c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xab576c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xab5770: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab5770: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab5774: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab5774: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab5778: r9 = customizedValue
    //     0xab5778: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6aff0] Field <<EMAIL>>: late (offset: 0x18)
    //     0xab577c: ldr             x9, [x9, #0xff0]
    // 0xab5780: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xab5780: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xab5784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab5784: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4202, size: 0x14, field offset: 0xc
//   const constructor, 
class BagText extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7cf2c, size: 0x30
    // 0xc7cf2c: EnterFrame
    //     0xc7cf2c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7cf30: mov             fp, SP
    // 0xc7cf34: mov             x0, x1
    // 0xc7cf38: r1 = <BagText>
    //     0xc7cf38: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f70] TypeArguments: <BagText>
    //     0xc7cf3c: ldr             x1, [x1, #0xf70]
    // 0xc7cf40: r0 = _BagTextState()
    //     0xc7cf40: bl              #0xc7cf5c  ; Allocate_BagTextStateStub -> _BagTextState (size=0x1c)
    // 0xc7cf44: r1 = Sentinel
    //     0xc7cf44: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7cf48: StoreField: r0->field_13 = r1
    //     0xc7cf48: stur            w1, [x0, #0x13]
    // 0xc7cf4c: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7cf4c: stur            w1, [x0, #0x17]
    // 0xc7cf50: LeaveFrame
    //     0xc7cf50: mov             SP, fp
    //     0xc7cf54: ldp             fp, lr, [SP], #0x10
    // 0xc7cf58: ret
    //     0xc7cf58: ret             
  }
}
