// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart

// class id: 1049358, size: 0x8
class :: {
}

// class id: 4574, size: 0x14, field offset: 0x14
//   const constructor, 
class PaymentMethodsCheckoutWidget extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x130534c, size: 0xdc
    // 0x130534c: EnterFrame
    //     0x130534c: stp             fp, lr, [SP, #-0x10]!
    //     0x1305350: mov             fp, SP
    // 0x1305354: AllocStack(0x20)
    //     0x1305354: sub             SP, SP, #0x20
    // 0x1305358: SetupParameters()
    //     0x1305358: ldr             x0, [fp, #0x10]
    //     0x130535c: ldur            w2, [x0, #0x17]
    //     0x1305360: add             x2, x2, HEAP, lsl #32
    //     0x1305364: stur            x2, [fp, #-8]
    // 0x1305368: CheckStackOverflow
    //     0x1305368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130536c: cmp             SP, x16
    //     0x1305370: b.ls            #0x1305420
    // 0x1305374: LoadField: r1 = r2->field_f
    //     0x1305374: ldur            w1, [x2, #0xf]
    // 0x1305378: DecompressPointer r1
    //     0x1305378: add             x1, x1, HEAP, lsl #32
    // 0x130537c: r0 = controller()
    //     0x130537c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1305380: LoadField: r1 = r0->field_7f
    //     0x1305380: ldur            w1, [x0, #0x7f]
    // 0x1305384: DecompressPointer r1
    //     0x1305384: add             x1, x1, HEAP, lsl #32
    // 0x1305388: r0 = value()
    //     0x1305388: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130538c: r1 = 60
    //     0x130538c: movz            x1, #0x3c
    // 0x1305390: branchIfSmi(r0, 0x130539c)
    //     0x1305390: tbz             w0, #0, #0x130539c
    // 0x1305394: r1 = LoadClassIdInstr(r0)
    //     0x1305394: ldur            x1, [x0, #-1]
    //     0x1305398: ubfx            x1, x1, #0xc, #0x14
    // 0x130539c: r16 = ""
    //     0x130539c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13053a0: stp             x16, x0, [SP]
    // 0x13053a4: mov             x0, x1
    // 0x13053a8: mov             lr, x0
    // 0x13053ac: ldr             lr, [x21, lr, lsl #3]
    // 0x13053b0: blr             lr
    // 0x13053b4: tbz             w0, #4, #0x13053f8
    // 0x13053b8: ldur            x0, [fp, #-8]
    // 0x13053bc: LoadField: r1 = r0->field_f
    //     0x13053bc: ldur            w1, [x0, #0xf]
    // 0x13053c0: DecompressPointer r1
    //     0x13053c0: add             x1, x1, HEAP, lsl #32
    // 0x13053c4: r0 = controller()
    //     0x13053c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13053c8: mov             x2, x0
    // 0x13053cc: ldur            x0, [fp, #-8]
    // 0x13053d0: stur            x2, [fp, #-0x10]
    // 0x13053d4: LoadField: r1 = r0->field_f
    //     0x13053d4: ldur            w1, [x0, #0xf]
    // 0x13053d8: DecompressPointer r1
    //     0x13053d8: add             x1, x1, HEAP, lsl #32
    // 0x13053dc: r0 = controller()
    //     0x13053dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13053e0: LoadField: r1 = r0->field_8b
    //     0x13053e0: ldur            w1, [x0, #0x8b]
    // 0x13053e4: DecompressPointer r1
    //     0x13053e4: add             x1, x1, HEAP, lsl #32
    // 0x13053e8: r0 = value()
    //     0x13053e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13053ec: ldur            x1, [fp, #-0x10]
    // 0x13053f0: r0 = initPaymentDetails()
    //     0x13053f0: bl              #0x12db22c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::initPaymentDetails
    // 0x13053f4: b               #0x1305410
    // 0x13053f8: ldur            x0, [fp, #-8]
    // 0x13053fc: LoadField: r1 = r0->field_f
    //     0x13053fc: ldur            w1, [x0, #0xf]
    // 0x1305400: DecompressPointer r1
    //     0x1305400: add             x1, x1, HEAP, lsl #32
    // 0x1305404: r2 = "Please select any payment mode!"
    //     0x1305404: add             x2, PP, #0x38, lsl #12  ; [pp+0x38198] "Please select any payment mode!"
    //     0x1305408: ldr             x2, [x2, #0x198]
    // 0x130540c: r0 = showErrorSnackBar()
    //     0x130540c: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x1305410: r0 = Null
    //     0x1305410: mov             x0, NULL
    // 0x1305414: LeaveFrame
    //     0x1305414: mov             SP, fp
    //     0x1305418: ldp             fp, lr, [SP], #0x10
    // 0x130541c: ret
    //     0x130541c: ret             
    // 0x1305420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1305420: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1305424: b               #0x1305374
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x1305428, size: 0x644
    // 0x1305428: EnterFrame
    //     0x1305428: stp             fp, lr, [SP, #-0x10]!
    //     0x130542c: mov             fp, SP
    // 0x1305430: AllocStack(0x50)
    //     0x1305430: sub             SP, SP, #0x50
    // 0x1305434: SetupParameters()
    //     0x1305434: ldr             x0, [fp, #0x10]
    //     0x1305438: ldur            w2, [x0, #0x17]
    //     0x130543c: add             x2, x2, HEAP, lsl #32
    //     0x1305440: stur            x2, [fp, #-8]
    // 0x1305444: CheckStackOverflow
    //     0x1305444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1305448: cmp             SP, x16
    //     0x130544c: b.ls            #0x1305a54
    // 0x1305450: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1305450: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1305454: ldr             x0, [x0, #0x1c80]
    //     0x1305458: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130545c: cmp             w0, w16
    //     0x1305460: b.ne            #0x130546c
    //     0x1305464: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1305468: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130546c: r0 = GetNavigation.size()
    //     0x130546c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1305470: LoadField: d0 = r0->field_f
    //     0x1305470: ldur            d0, [x0, #0xf]
    // 0x1305474: d1 = 0.120000
    //     0x1305474: ldr             d1, [PP, #0x54a8]  ; [pp+0x54a8] IMM: double(0.12) from 0x3fbeb851eb851eb8
    // 0x1305478: fmul            d2, d0, d1
    // 0x130547c: stur            d2, [fp, #-0x40]
    // 0x1305480: r1 = _ConstMap len:11
    //     0x1305480: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x1305484: ldr             x1, [x1, #0xc28]
    // 0x1305488: r2 = 8
    //     0x1305488: movz            x2, #0x8
    // 0x130548c: r0 = []()
    //     0x130548c: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1305490: stur            x0, [fp, #-0x10]
    // 0x1305494: r0 = BoxDecoration()
    //     0x1305494: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1305498: mov             x2, x0
    // 0x130549c: r0 = Instance_Color
    //     0x130549c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13054a0: stur            x2, [fp, #-0x18]
    // 0x13054a4: StoreField: r2->field_7 = r0
    //     0x13054a4: stur            w0, [x2, #7]
    // 0x13054a8: ldur            x0, [fp, #-0x10]
    // 0x13054ac: ArrayStore: r2[0] = r0  ; List_4
    //     0x13054ac: stur            w0, [x2, #0x17]
    // 0x13054b0: r0 = Instance_BoxShape
    //     0x13054b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13054b4: ldr             x0, [x0, #0x80]
    // 0x13054b8: StoreField: r2->field_23 = r0
    //     0x13054b8: stur            w0, [x2, #0x23]
    // 0x13054bc: ldur            x0, [fp, #-8]
    // 0x13054c0: LoadField: r1 = r0->field_f
    //     0x13054c0: ldur            w1, [x0, #0xf]
    // 0x13054c4: DecompressPointer r1
    //     0x13054c4: add             x1, x1, HEAP, lsl #32
    // 0x13054c8: r0 = controller()
    //     0x13054c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13054cc: LoadField: r1 = r0->field_87
    //     0x13054cc: ldur            w1, [x0, #0x87]
    // 0x13054d0: DecompressPointer r1
    //     0x13054d0: add             x1, x1, HEAP, lsl #32
    // 0x13054d4: r0 = value()
    //     0x13054d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13054d8: LoadField: r1 = r0->field_f
    //     0x13054d8: ldur            w1, [x0, #0xf]
    // 0x13054dc: DecompressPointer r1
    //     0x13054dc: add             x1, x1, HEAP, lsl #32
    // 0x13054e0: cmp             w1, NULL
    // 0x13054e4: b.ne            #0x13054f0
    // 0x13054e8: r0 = ""
    //     0x13054e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13054ec: b               #0x13054f4
    // 0x13054f0: mov             x0, x1
    // 0x13054f4: ldur            x2, [fp, #-8]
    // 0x13054f8: stur            x0, [fp, #-0x10]
    // 0x13054fc: LoadField: r1 = r2->field_13
    //     0x13054fc: ldur            w1, [x2, #0x13]
    // 0x1305500: DecompressPointer r1
    //     0x1305500: add             x1, x1, HEAP, lsl #32
    // 0x1305504: r0 = of()
    //     0x1305504: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1305508: LoadField: r1 = r0->field_87
    //     0x1305508: ldur            w1, [x0, #0x87]
    // 0x130550c: DecompressPointer r1
    //     0x130550c: add             x1, x1, HEAP, lsl #32
    // 0x1305510: LoadField: r0 = r1->field_2b
    //     0x1305510: ldur            w0, [x1, #0x2b]
    // 0x1305514: DecompressPointer r0
    //     0x1305514: add             x0, x0, HEAP, lsl #32
    // 0x1305518: ldur            x2, [fp, #-8]
    // 0x130551c: stur            x0, [fp, #-0x20]
    // 0x1305520: LoadField: r1 = r2->field_13
    //     0x1305520: ldur            w1, [x2, #0x13]
    // 0x1305524: DecompressPointer r1
    //     0x1305524: add             x1, x1, HEAP, lsl #32
    // 0x1305528: r0 = of()
    //     0x1305528: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130552c: LoadField: r1 = r0->field_5b
    //     0x130552c: ldur            w1, [x0, #0x5b]
    // 0x1305530: DecompressPointer r1
    //     0x1305530: add             x1, x1, HEAP, lsl #32
    // 0x1305534: r0 = LoadClassIdInstr(r1)
    //     0x1305534: ldur            x0, [x1, #-1]
    //     0x1305538: ubfx            x0, x0, #0xc, #0x14
    // 0x130553c: d0 = 0.700000
    //     0x130553c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1305540: ldr             d0, [x17, #0xf48]
    // 0x1305544: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1305544: sub             lr, x0, #0xffa
    //     0x1305548: ldr             lr, [x21, lr, lsl #3]
    //     0x130554c: blr             lr
    // 0x1305550: r16 = 12.000000
    //     0x1305550: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1305554: ldr             x16, [x16, #0x9e8]
    // 0x1305558: stp             x0, x16, [SP]
    // 0x130555c: ldur            x1, [fp, #-0x20]
    // 0x1305560: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1305560: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1305564: ldr             x4, [x4, #0xaa0]
    // 0x1305568: r0 = copyWith()
    //     0x1305568: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130556c: stur            x0, [fp, #-0x20]
    // 0x1305570: r0 = Text()
    //     0x1305570: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1305574: mov             x2, x0
    // 0x1305578: ldur            x0, [fp, #-0x10]
    // 0x130557c: stur            x2, [fp, #-0x28]
    // 0x1305580: StoreField: r2->field_b = r0
    //     0x1305580: stur            w0, [x2, #0xb]
    // 0x1305584: ldur            x0, [fp, #-0x20]
    // 0x1305588: StoreField: r2->field_13 = r0
    //     0x1305588: stur            w0, [x2, #0x13]
    // 0x130558c: ldur            x0, [fp, #-8]
    // 0x1305590: LoadField: r1 = r0->field_f
    //     0x1305590: ldur            w1, [x0, #0xf]
    // 0x1305594: DecompressPointer r1
    //     0x1305594: add             x1, x1, HEAP, lsl #32
    // 0x1305598: r0 = controller()
    //     0x1305598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130559c: LoadField: r1 = r0->field_87
    //     0x130559c: ldur            w1, [x0, #0x87]
    // 0x13055a0: DecompressPointer r1
    //     0x13055a0: add             x1, x1, HEAP, lsl #32
    // 0x13055a4: r0 = value()
    //     0x13055a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13055a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13055a8: ldur            w1, [x0, #0x17]
    // 0x13055ac: DecompressPointer r1
    //     0x13055ac: add             x1, x1, HEAP, lsl #32
    // 0x13055b0: cmp             w1, NULL
    // 0x13055b4: b.ne            #0x13055c0
    // 0x13055b8: r3 = ""
    //     0x13055b8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13055bc: b               #0x13055c4
    // 0x13055c0: mov             x3, x1
    // 0x13055c4: ldur            x2, [fp, #-8]
    // 0x13055c8: ldur            d0, [fp, #-0x40]
    // 0x13055cc: ldur            x0, [fp, #-0x28]
    // 0x13055d0: stur            x3, [fp, #-0x10]
    // 0x13055d4: LoadField: r1 = r2->field_13
    //     0x13055d4: ldur            w1, [x2, #0x13]
    // 0x13055d8: DecompressPointer r1
    //     0x13055d8: add             x1, x1, HEAP, lsl #32
    // 0x13055dc: r0 = of()
    //     0x13055dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13055e0: LoadField: r1 = r0->field_87
    //     0x13055e0: ldur            w1, [x0, #0x87]
    // 0x13055e4: DecompressPointer r1
    //     0x13055e4: add             x1, x1, HEAP, lsl #32
    // 0x13055e8: LoadField: r0 = r1->field_7
    //     0x13055e8: ldur            w0, [x1, #7]
    // 0x13055ec: DecompressPointer r0
    //     0x13055ec: add             x0, x0, HEAP, lsl #32
    // 0x13055f0: ldur            x2, [fp, #-8]
    // 0x13055f4: stur            x0, [fp, #-0x20]
    // 0x13055f8: LoadField: r1 = r2->field_13
    //     0x13055f8: ldur            w1, [x2, #0x13]
    // 0x13055fc: DecompressPointer r1
    //     0x13055fc: add             x1, x1, HEAP, lsl #32
    // 0x1305600: r0 = of()
    //     0x1305600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1305604: LoadField: r1 = r0->field_5b
    //     0x1305604: ldur            w1, [x0, #0x5b]
    // 0x1305608: DecompressPointer r1
    //     0x1305608: add             x1, x1, HEAP, lsl #32
    // 0x130560c: r16 = 16.000000
    //     0x130560c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1305610: ldr             x16, [x16, #0x188]
    // 0x1305614: stp             x1, x16, [SP]
    // 0x1305618: ldur            x1, [fp, #-0x20]
    // 0x130561c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130561c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1305620: ldr             x4, [x4, #0xaa0]
    // 0x1305624: r0 = copyWith()
    //     0x1305624: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1305628: stur            x0, [fp, #-0x20]
    // 0x130562c: r0 = Text()
    //     0x130562c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1305630: mov             x1, x0
    // 0x1305634: ldur            x0, [fp, #-0x10]
    // 0x1305638: stur            x1, [fp, #-0x30]
    // 0x130563c: StoreField: r1->field_b = r0
    //     0x130563c: stur            w0, [x1, #0xb]
    // 0x1305640: ldur            x0, [fp, #-0x20]
    // 0x1305644: StoreField: r1->field_13 = r0
    //     0x1305644: stur            w0, [x1, #0x13]
    // 0x1305648: r0 = Padding()
    //     0x1305648: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130564c: mov             x3, x0
    // 0x1305650: r0 = Instance_EdgeInsets
    //     0x1305650: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x1305654: ldr             x0, [x0, #0x668]
    // 0x1305658: stur            x3, [fp, #-0x10]
    // 0x130565c: StoreField: r3->field_f = r0
    //     0x130565c: stur            w0, [x3, #0xf]
    // 0x1305660: ldur            x0, [fp, #-0x30]
    // 0x1305664: StoreField: r3->field_b = r0
    //     0x1305664: stur            w0, [x3, #0xb]
    // 0x1305668: r1 = Null
    //     0x1305668: mov             x1, NULL
    // 0x130566c: r2 = 2
    //     0x130566c: movz            x2, #0x2
    // 0x1305670: r0 = AllocateArray()
    //     0x1305670: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1305674: mov             x2, x0
    // 0x1305678: ldur            x0, [fp, #-0x10]
    // 0x130567c: stur            x2, [fp, #-0x20]
    // 0x1305680: StoreField: r2->field_f = r0
    //     0x1305680: stur            w0, [x2, #0xf]
    // 0x1305684: r1 = <Widget>
    //     0x1305684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1305688: r0 = AllocateGrowableArray()
    //     0x1305688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130568c: mov             x1, x0
    // 0x1305690: ldur            x0, [fp, #-0x20]
    // 0x1305694: stur            x1, [fp, #-0x10]
    // 0x1305698: StoreField: r1->field_f = r0
    //     0x1305698: stur            w0, [x1, #0xf]
    // 0x130569c: r0 = 2
    //     0x130569c: movz            x0, #0x2
    // 0x13056a0: StoreField: r1->field_b = r0
    //     0x13056a0: stur            w0, [x1, #0xb]
    // 0x13056a4: r0 = Row()
    //     0x13056a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13056a8: mov             x3, x0
    // 0x13056ac: r0 = Instance_Axis
    //     0x13056ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13056b0: stur            x3, [fp, #-0x20]
    // 0x13056b4: StoreField: r3->field_f = r0
    //     0x13056b4: stur            w0, [x3, #0xf]
    // 0x13056b8: r4 = Instance_MainAxisAlignment
    //     0x13056b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13056bc: ldr             x4, [x4, #0xa08]
    // 0x13056c0: StoreField: r3->field_13 = r4
    //     0x13056c0: stur            w4, [x3, #0x13]
    // 0x13056c4: r5 = Instance_MainAxisSize
    //     0x13056c4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13056c8: ldr             x5, [x5, #0xa10]
    // 0x13056cc: ArrayStore: r3[0] = r5  ; List_4
    //     0x13056cc: stur            w5, [x3, #0x17]
    // 0x13056d0: r6 = Instance_CrossAxisAlignment
    //     0x13056d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13056d4: ldr             x6, [x6, #0xa18]
    // 0x13056d8: StoreField: r3->field_1b = r6
    //     0x13056d8: stur            w6, [x3, #0x1b]
    // 0x13056dc: r7 = Instance_VerticalDirection
    //     0x13056dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13056e0: ldr             x7, [x7, #0xa20]
    // 0x13056e4: StoreField: r3->field_23 = r7
    //     0x13056e4: stur            w7, [x3, #0x23]
    // 0x13056e8: r8 = Instance_Clip
    //     0x13056e8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13056ec: ldr             x8, [x8, #0x38]
    // 0x13056f0: StoreField: r3->field_2b = r8
    //     0x13056f0: stur            w8, [x3, #0x2b]
    // 0x13056f4: StoreField: r3->field_2f = rZR
    //     0x13056f4: stur            xzr, [x3, #0x2f]
    // 0x13056f8: ldur            x1, [fp, #-0x10]
    // 0x13056fc: StoreField: r3->field_b = r1
    //     0x13056fc: stur            w1, [x3, #0xb]
    // 0x1305700: r1 = Null
    //     0x1305700: mov             x1, NULL
    // 0x1305704: r2 = 4
    //     0x1305704: movz            x2, #0x4
    // 0x1305708: r0 = AllocateArray()
    //     0x1305708: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130570c: mov             x2, x0
    // 0x1305710: ldur            x0, [fp, #-0x28]
    // 0x1305714: stur            x2, [fp, #-0x10]
    // 0x1305718: StoreField: r2->field_f = r0
    //     0x1305718: stur            w0, [x2, #0xf]
    // 0x130571c: ldur            x0, [fp, #-0x20]
    // 0x1305720: StoreField: r2->field_13 = r0
    //     0x1305720: stur            w0, [x2, #0x13]
    // 0x1305724: r1 = <Widget>
    //     0x1305724: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1305728: r0 = AllocateGrowableArray()
    //     0x1305728: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130572c: mov             x1, x0
    // 0x1305730: ldur            x0, [fp, #-0x10]
    // 0x1305734: stur            x1, [fp, #-0x20]
    // 0x1305738: StoreField: r1->field_f = r0
    //     0x1305738: stur            w0, [x1, #0xf]
    // 0x130573c: r0 = 4
    //     0x130573c: movz            x0, #0x4
    // 0x1305740: StoreField: r1->field_b = r0
    //     0x1305740: stur            w0, [x1, #0xb]
    // 0x1305744: r0 = Column()
    //     0x1305744: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1305748: mov             x1, x0
    // 0x130574c: r0 = Instance_Axis
    //     0x130574c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1305750: stur            x1, [fp, #-0x10]
    // 0x1305754: StoreField: r1->field_f = r0
    //     0x1305754: stur            w0, [x1, #0xf]
    // 0x1305758: r0 = Instance_MainAxisAlignment
    //     0x1305758: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x130575c: ldr             x0, [x0, #0xa08]
    // 0x1305760: StoreField: r1->field_13 = r0
    //     0x1305760: stur            w0, [x1, #0x13]
    // 0x1305764: r2 = Instance_MainAxisSize
    //     0x1305764: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1305768: ldr             x2, [x2, #0xa10]
    // 0x130576c: ArrayStore: r1[0] = r2  ; List_4
    //     0x130576c: stur            w2, [x1, #0x17]
    // 0x1305770: r3 = Instance_CrossAxisAlignment
    //     0x1305770: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1305774: ldr             x3, [x3, #0x890]
    // 0x1305778: StoreField: r1->field_1b = r3
    //     0x1305778: stur            w3, [x1, #0x1b]
    // 0x130577c: r3 = Instance_VerticalDirection
    //     0x130577c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1305780: ldr             x3, [x3, #0xa20]
    // 0x1305784: StoreField: r1->field_23 = r3
    //     0x1305784: stur            w3, [x1, #0x23]
    // 0x1305788: r4 = Instance_Clip
    //     0x1305788: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130578c: ldr             x4, [x4, #0x38]
    // 0x1305790: StoreField: r1->field_2b = r4
    //     0x1305790: stur            w4, [x1, #0x2b]
    // 0x1305794: StoreField: r1->field_2f = rZR
    //     0x1305794: stur            xzr, [x1, #0x2f]
    // 0x1305798: ldur            x5, [fp, #-0x20]
    // 0x130579c: StoreField: r1->field_b = r5
    //     0x130579c: stur            w5, [x1, #0xb]
    // 0x13057a0: r16 = <EdgeInsets>
    //     0x13057a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13057a4: ldr             x16, [x16, #0xda0]
    // 0x13057a8: r30 = Instance_EdgeInsets
    //     0x13057a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13057ac: ldr             lr, [lr, #0x1f0]
    // 0x13057b0: stp             lr, x16, [SP]
    // 0x13057b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13057b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13057b8: r0 = all()
    //     0x13057b8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13057bc: ldur            x2, [fp, #-8]
    // 0x13057c0: stur            x0, [fp, #-0x20]
    // 0x13057c4: LoadField: r1 = r2->field_13
    //     0x13057c4: ldur            w1, [x2, #0x13]
    // 0x13057c8: DecompressPointer r1
    //     0x13057c8: add             x1, x1, HEAP, lsl #32
    // 0x13057cc: r0 = of()
    //     0x13057cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13057d0: LoadField: r1 = r0->field_5b
    //     0x13057d0: ldur            w1, [x0, #0x5b]
    // 0x13057d4: DecompressPointer r1
    //     0x13057d4: add             x1, x1, HEAP, lsl #32
    // 0x13057d8: r16 = <Color>
    //     0x13057d8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13057dc: ldr             x16, [x16, #0xf80]
    // 0x13057e0: stp             x1, x16, [SP]
    // 0x13057e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13057e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13057e8: r0 = all()
    //     0x13057e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13057ec: stur            x0, [fp, #-0x28]
    // 0x13057f0: r16 = <RoundedRectangleBorder>
    //     0x13057f0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13057f4: ldr             x16, [x16, #0xf78]
    // 0x13057f8: r30 = Instance_RoundedRectangleBorder
    //     0x13057f8: add             lr, PP, #0x40, lsl #12  ; [pp+0x409a8] Obj!RoundedRectangleBorder@d5acb1
    //     0x13057fc: ldr             lr, [lr, #0x9a8]
    // 0x1305800: stp             lr, x16, [SP]
    // 0x1305804: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1305804: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1305808: r0 = all()
    //     0x1305808: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130580c: stur            x0, [fp, #-0x30]
    // 0x1305810: r0 = ButtonStyle()
    //     0x1305810: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1305814: mov             x1, x0
    // 0x1305818: ldur            x0, [fp, #-0x28]
    // 0x130581c: stur            x1, [fp, #-0x38]
    // 0x1305820: StoreField: r1->field_b = r0
    //     0x1305820: stur            w0, [x1, #0xb]
    // 0x1305824: ldur            x0, [fp, #-0x20]
    // 0x1305828: StoreField: r1->field_23 = r0
    //     0x1305828: stur            w0, [x1, #0x23]
    // 0x130582c: ldur            x0, [fp, #-0x30]
    // 0x1305830: StoreField: r1->field_43 = r0
    //     0x1305830: stur            w0, [x1, #0x43]
    // 0x1305834: r0 = TextButtonThemeData()
    //     0x1305834: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1305838: mov             x2, x0
    // 0x130583c: ldur            x0, [fp, #-0x38]
    // 0x1305840: stur            x2, [fp, #-0x20]
    // 0x1305844: StoreField: r2->field_7 = r0
    //     0x1305844: stur            w0, [x2, #7]
    // 0x1305848: ldur            x0, [fp, #-8]
    // 0x130584c: LoadField: r1 = r0->field_13
    //     0x130584c: ldur            w1, [x0, #0x13]
    // 0x1305850: DecompressPointer r1
    //     0x1305850: add             x1, x1, HEAP, lsl #32
    // 0x1305854: r0 = of()
    //     0x1305854: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1305858: LoadField: r1 = r0->field_87
    //     0x1305858: ldur            w1, [x0, #0x87]
    // 0x130585c: DecompressPointer r1
    //     0x130585c: add             x1, x1, HEAP, lsl #32
    // 0x1305860: LoadField: r0 = r1->field_7
    //     0x1305860: ldur            w0, [x1, #7]
    // 0x1305864: DecompressPointer r0
    //     0x1305864: add             x0, x0, HEAP, lsl #32
    // 0x1305868: r16 = Instance_Color
    //     0x1305868: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x130586c: r30 = 14.000000
    //     0x130586c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1305870: ldr             lr, [lr, #0x1d8]
    // 0x1305874: stp             lr, x16, [SP]
    // 0x1305878: mov             x1, x0
    // 0x130587c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x130587c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1305880: ldr             x4, [x4, #0x9b8]
    // 0x1305884: r0 = copyWith()
    //     0x1305884: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1305888: stur            x0, [fp, #-0x28]
    // 0x130588c: r0 = Text()
    //     0x130588c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1305890: mov             x3, x0
    // 0x1305894: r0 = "Place Order"
    //     0x1305894: add             x0, PP, #0x40, lsl #12  ; [pp+0x40d90] "Place Order"
    //     0x1305898: ldr             x0, [x0, #0xd90]
    // 0x130589c: stur            x3, [fp, #-0x30]
    // 0x13058a0: StoreField: r3->field_b = r0
    //     0x13058a0: stur            w0, [x3, #0xb]
    // 0x13058a4: ldur            x0, [fp, #-0x28]
    // 0x13058a8: StoreField: r3->field_13 = r0
    //     0x13058a8: stur            w0, [x3, #0x13]
    // 0x13058ac: ldur            x2, [fp, #-8]
    // 0x13058b0: r1 = Function '<anonymous closure>':.
    //     0x13058b0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d98] AnonymousClosure: (0x130534c), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::bottomNavigationBar (0x13601b8)
    //     0x13058b4: ldr             x1, [x1, #0xd98]
    // 0x13058b8: r0 = AllocateClosure()
    //     0x13058b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13058bc: stur            x0, [fp, #-8]
    // 0x13058c0: r0 = TextButton()
    //     0x13058c0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13058c4: mov             x1, x0
    // 0x13058c8: ldur            x0, [fp, #-8]
    // 0x13058cc: stur            x1, [fp, #-0x28]
    // 0x13058d0: StoreField: r1->field_b = r0
    //     0x13058d0: stur            w0, [x1, #0xb]
    // 0x13058d4: r0 = false
    //     0x13058d4: add             x0, NULL, #0x30  ; false
    // 0x13058d8: StoreField: r1->field_27 = r0
    //     0x13058d8: stur            w0, [x1, #0x27]
    // 0x13058dc: r0 = true
    //     0x13058dc: add             x0, NULL, #0x20  ; true
    // 0x13058e0: StoreField: r1->field_2f = r0
    //     0x13058e0: stur            w0, [x1, #0x2f]
    // 0x13058e4: ldur            x0, [fp, #-0x30]
    // 0x13058e8: StoreField: r1->field_37 = r0
    //     0x13058e8: stur            w0, [x1, #0x37]
    // 0x13058ec: r0 = Instance_ValueKey
    //     0x13058ec: add             x0, PP, #0x34, lsl #12  ; [pp+0x34680] Obj!ValueKey<String>@d5b361
    //     0x13058f0: ldr             x0, [x0, #0x680]
    // 0x13058f4: StoreField: r1->field_7 = r0
    //     0x13058f4: stur            w0, [x1, #7]
    // 0x13058f8: r0 = TextButtonTheme()
    //     0x13058f8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13058fc: mov             x3, x0
    // 0x1305900: ldur            x0, [fp, #-0x20]
    // 0x1305904: stur            x3, [fp, #-8]
    // 0x1305908: StoreField: r3->field_f = r0
    //     0x1305908: stur            w0, [x3, #0xf]
    // 0x130590c: ldur            x0, [fp, #-0x28]
    // 0x1305910: StoreField: r3->field_b = r0
    //     0x1305910: stur            w0, [x3, #0xb]
    // 0x1305914: r1 = Null
    //     0x1305914: mov             x1, NULL
    // 0x1305918: r2 = 6
    //     0x1305918: movz            x2, #0x6
    // 0x130591c: r0 = AllocateArray()
    //     0x130591c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1305920: mov             x2, x0
    // 0x1305924: ldur            x0, [fp, #-0x10]
    // 0x1305928: stur            x2, [fp, #-0x20]
    // 0x130592c: StoreField: r2->field_f = r0
    //     0x130592c: stur            w0, [x2, #0xf]
    // 0x1305930: r16 = Instance_Spacer
    //     0x1305930: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x1305934: ldr             x16, [x16, #0xf0]
    // 0x1305938: StoreField: r2->field_13 = r16
    //     0x1305938: stur            w16, [x2, #0x13]
    // 0x130593c: ldur            x0, [fp, #-8]
    // 0x1305940: ArrayStore: r2[0] = r0  ; List_4
    //     0x1305940: stur            w0, [x2, #0x17]
    // 0x1305944: r1 = <Widget>
    //     0x1305944: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1305948: r0 = AllocateGrowableArray()
    //     0x1305948: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130594c: mov             x1, x0
    // 0x1305950: ldur            x0, [fp, #-0x20]
    // 0x1305954: stur            x1, [fp, #-8]
    // 0x1305958: StoreField: r1->field_f = r0
    //     0x1305958: stur            w0, [x1, #0xf]
    // 0x130595c: r0 = 6
    //     0x130595c: movz            x0, #0x6
    // 0x1305960: StoreField: r1->field_b = r0
    //     0x1305960: stur            w0, [x1, #0xb]
    // 0x1305964: r0 = Row()
    //     0x1305964: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1305968: mov             x1, x0
    // 0x130596c: r0 = Instance_Axis
    //     0x130596c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1305970: stur            x1, [fp, #-0x10]
    // 0x1305974: StoreField: r1->field_f = r0
    //     0x1305974: stur            w0, [x1, #0xf]
    // 0x1305978: r0 = Instance_MainAxisAlignment
    //     0x1305978: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x130597c: ldr             x0, [x0, #0xa08]
    // 0x1305980: StoreField: r1->field_13 = r0
    //     0x1305980: stur            w0, [x1, #0x13]
    // 0x1305984: r0 = Instance_MainAxisSize
    //     0x1305984: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1305988: ldr             x0, [x0, #0xa10]
    // 0x130598c: ArrayStore: r1[0] = r0  ; List_4
    //     0x130598c: stur            w0, [x1, #0x17]
    // 0x1305990: r0 = Instance_CrossAxisAlignment
    //     0x1305990: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1305994: ldr             x0, [x0, #0xa18]
    // 0x1305998: StoreField: r1->field_1b = r0
    //     0x1305998: stur            w0, [x1, #0x1b]
    // 0x130599c: r0 = Instance_VerticalDirection
    //     0x130599c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13059a0: ldr             x0, [x0, #0xa20]
    // 0x13059a4: StoreField: r1->field_23 = r0
    //     0x13059a4: stur            w0, [x1, #0x23]
    // 0x13059a8: r0 = Instance_Clip
    //     0x13059a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13059ac: ldr             x0, [x0, #0x38]
    // 0x13059b0: StoreField: r1->field_2b = r0
    //     0x13059b0: stur            w0, [x1, #0x2b]
    // 0x13059b4: StoreField: r1->field_2f = rZR
    //     0x13059b4: stur            xzr, [x1, #0x2f]
    // 0x13059b8: ldur            x0, [fp, #-8]
    // 0x13059bc: StoreField: r1->field_b = r0
    //     0x13059bc: stur            w0, [x1, #0xb]
    // 0x13059c0: r0 = Padding()
    //     0x13059c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13059c4: mov             x1, x0
    // 0x13059c8: r0 = Instance_EdgeInsets
    //     0x13059c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13059cc: ldr             x0, [x0, #0x1f0]
    // 0x13059d0: stur            x1, [fp, #-8]
    // 0x13059d4: StoreField: r1->field_f = r0
    //     0x13059d4: stur            w0, [x1, #0xf]
    // 0x13059d8: ldur            x0, [fp, #-0x10]
    // 0x13059dc: StoreField: r1->field_b = r0
    //     0x13059dc: stur            w0, [x1, #0xb]
    // 0x13059e0: r0 = Container()
    //     0x13059e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13059e4: stur            x0, [fp, #-0x10]
    // 0x13059e8: ldur            x16, [fp, #-0x18]
    // 0x13059ec: ldur            lr, [fp, #-8]
    // 0x13059f0: stp             lr, x16, [SP]
    // 0x13059f4: mov             x1, x0
    // 0x13059f8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13059f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13059fc: ldr             x4, [x4, #0x88]
    // 0x1305a00: r0 = Container()
    //     0x1305a00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1305a04: ldur            d0, [fp, #-0x40]
    // 0x1305a08: r0 = inline_Allocate_Double()
    //     0x1305a08: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1305a0c: add             x0, x0, #0x10
    //     0x1305a10: cmp             x1, x0
    //     0x1305a14: b.ls            #0x1305a5c
    //     0x1305a18: str             x0, [THR, #0x50]  ; THR::top
    //     0x1305a1c: sub             x0, x0, #0xf
    //     0x1305a20: movz            x1, #0xe15c
    //     0x1305a24: movk            x1, #0x3, lsl #16
    //     0x1305a28: stur            x1, [x0, #-1]
    // 0x1305a2c: StoreField: r0->field_7 = d0
    //     0x1305a2c: stur            d0, [x0, #7]
    // 0x1305a30: stur            x0, [fp, #-8]
    // 0x1305a34: r0 = SizedBox()
    //     0x1305a34: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1305a38: ldur            x1, [fp, #-8]
    // 0x1305a3c: StoreField: r0->field_13 = r1
    //     0x1305a3c: stur            w1, [x0, #0x13]
    // 0x1305a40: ldur            x1, [fp, #-0x10]
    // 0x1305a44: StoreField: r0->field_b = r1
    //     0x1305a44: stur            w1, [x0, #0xb]
    // 0x1305a48: LeaveFrame
    //     0x1305a48: mov             SP, fp
    //     0x1305a4c: ldp             fp, lr, [SP], #0x10
    // 0x1305a50: ret
    //     0x1305a50: ret             
    // 0x1305a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1305a54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1305a58: b               #0x1305450
    // 0x1305a5c: SaveReg d0
    //     0x1305a5c: str             q0, [SP, #-0x10]!
    // 0x1305a60: r0 = AllocateDouble()
    //     0x1305a60: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1305a64: RestoreReg d0
    //     0x1305a64: ldr             q0, [SP], #0x10
    // 0x1305a68: b               #0x1305a2c
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x13601b8, size: 0x64
    // 0x13601b8: EnterFrame
    //     0x13601b8: stp             fp, lr, [SP, #-0x10]!
    //     0x13601bc: mov             fp, SP
    // 0x13601c0: AllocStack(0x18)
    //     0x13601c0: sub             SP, SP, #0x18
    // 0x13601c4: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x13601c4: stur            x1, [fp, #-8]
    //     0x13601c8: stur            x2, [fp, #-0x10]
    // 0x13601cc: r1 = 2
    //     0x13601cc: movz            x1, #0x2
    // 0x13601d0: r0 = AllocateContext()
    //     0x13601d0: bl              #0x16f6108  ; AllocateContextStub
    // 0x13601d4: mov             x1, x0
    // 0x13601d8: ldur            x0, [fp, #-8]
    // 0x13601dc: stur            x1, [fp, #-0x18]
    // 0x13601e0: StoreField: r1->field_f = r0
    //     0x13601e0: stur            w0, [x1, #0xf]
    // 0x13601e4: ldur            x0, [fp, #-0x10]
    // 0x13601e8: StoreField: r1->field_13 = r0
    //     0x13601e8: stur            w0, [x1, #0x13]
    // 0x13601ec: r0 = Obx()
    //     0x13601ec: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13601f0: ldur            x2, [fp, #-0x18]
    // 0x13601f4: r1 = Function '<anonymous closure>':.
    //     0x13601f4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d88] AnonymousClosure: (0x1305428), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::bottomNavigationBar (0x13601b8)
    //     0x13601f8: ldr             x1, [x1, #0xd88]
    // 0x13601fc: stur            x0, [fp, #-8]
    // 0x1360200: r0 = AllocateClosure()
    //     0x1360200: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360204: mov             x1, x0
    // 0x1360208: ldur            x0, [fp, #-8]
    // 0x136020c: StoreField: r0->field_b = r1
    //     0x136020c: stur            w1, [x0, #0xb]
    // 0x1360210: LeaveFrame
    //     0x1360210: mov             SP, fp
    //     0x1360214: ldp             fp, lr, [SP], #0x10
    // 0x1360218: ret
    //     0x1360218: ret             
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x148ca10, size: 0x74
    // 0x148ca10: EnterFrame
    //     0x148ca10: stp             fp, lr, [SP, #-0x10]!
    //     0x148ca14: mov             fp, SP
    // 0x148ca18: AllocStack(0x10)
    //     0x148ca18: sub             SP, SP, #0x10
    // 0x148ca1c: CheckStackOverflow
    //     0x148ca1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148ca20: cmp             SP, x16
    //     0x148ca24: b.ls            #0x148ca7c
    // 0x148ca28: r1 = Instance_Color
    //     0x148ca28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148ca2c: d0 = 0.100000
    //     0x148ca2c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x148ca30: r0 = withOpacity()
    //     0x148ca30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148ca34: stur            x0, [fp, #-8]
    // 0x148ca38: r0 = Divider()
    //     0x148ca38: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x148ca3c: mov             x1, x0
    // 0x148ca40: r0 = 2.000000
    //     0x148ca40: add             x0, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0x148ca44: ldr             x0, [x0, #0xdf8]
    // 0x148ca48: stur            x1, [fp, #-0x10]
    // 0x148ca4c: StoreField: r1->field_f = r0
    //     0x148ca4c: stur            w0, [x1, #0xf]
    // 0x148ca50: ldur            x0, [fp, #-8]
    // 0x148ca54: StoreField: r1->field_1f = r0
    //     0x148ca54: stur            w0, [x1, #0x1f]
    // 0x148ca58: r0 = Padding()
    //     0x148ca58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148ca5c: r1 = Instance_EdgeInsets
    //     0x148ca5c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x148ca60: ldr             x1, [x1, #0x868]
    // 0x148ca64: StoreField: r0->field_f = r1
    //     0x148ca64: stur            w1, [x0, #0xf]
    // 0x148ca68: ldur            x1, [fp, #-0x10]
    // 0x148ca6c: StoreField: r0->field_b = r1
    //     0x148ca6c: stur            w1, [x0, #0xb]
    // 0x148ca70: LeaveFrame
    //     0x148ca70: mov             SP, fp
    //     0x148ca74: ldp             fp, lr, [SP], #0x10
    // 0x148ca78: ret
    //     0x148ca78: ret             
    // 0x148ca7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148ca7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148ca80: b               #0x148ca28
  }
  [closure] WillPopScope <anonymous closure>(dynamic) {
    // ** addr: 0x148ca84, size: 0x10f8
    // 0x148ca84: EnterFrame
    //     0x148ca84: stp             fp, lr, [SP, #-0x10]!
    //     0x148ca88: mov             fp, SP
    // 0x148ca8c: AllocStack(0xa8)
    //     0x148ca8c: sub             SP, SP, #0xa8
    // 0x148ca90: SetupParameters()
    //     0x148ca90: ldr             x0, [fp, #0x10]
    //     0x148ca94: ldur            w2, [x0, #0x17]
    //     0x148ca98: add             x2, x2, HEAP, lsl #32
    //     0x148ca9c: stur            x2, [fp, #-0x10]
    // 0x148caa0: CheckStackOverflow
    //     0x148caa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148caa4: cmp             SP, x16
    //     0x148caa8: b.ls            #0x148db74
    // 0x148caac: LoadField: r0 = r2->field_f
    //     0x148caac: ldur            w0, [x2, #0xf]
    // 0x148cab0: DecompressPointer r0
    //     0x148cab0: add             x0, x0, HEAP, lsl #32
    // 0x148cab4: stur            x0, [fp, #-8]
    // 0x148cab8: r0 = Obx()
    //     0x148cab8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148cabc: ldur            x2, [fp, #-0x10]
    // 0x148cac0: r1 = Function '<anonymous closure>':.
    //     0x148cac0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40da8] AnonymousClosure: (0x148eb0c), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x148cac4: ldr             x1, [x1, #0xda8]
    // 0x148cac8: stur            x0, [fp, #-0x18]
    // 0x148cacc: r0 = AllocateClosure()
    //     0x148cacc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148cad0: mov             x1, x0
    // 0x148cad4: ldur            x0, [fp, #-0x18]
    // 0x148cad8: StoreField: r0->field_b = r1
    //     0x148cad8: stur            w1, [x0, #0xb]
    // 0x148cadc: ldur            x2, [fp, #-0x10]
    // 0x148cae0: LoadField: r1 = r2->field_13
    //     0x148cae0: ldur            w1, [x2, #0x13]
    // 0x148cae4: DecompressPointer r1
    //     0x148cae4: add             x1, x1, HEAP, lsl #32
    // 0x148cae8: r0 = of()
    //     0x148cae8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148caec: LoadField: r1 = r0->field_5b
    //     0x148caec: ldur            w1, [x0, #0x5b]
    // 0x148caf0: DecompressPointer r1
    //     0x148caf0: add             x1, x1, HEAP, lsl #32
    // 0x148caf4: r0 = LoadClassIdInstr(r1)
    //     0x148caf4: ldur            x0, [x1, #-1]
    //     0x148caf8: ubfx            x0, x0, #0xc, #0x14
    // 0x148cafc: d0 = 0.030000
    //     0x148cafc: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x148cb00: ldr             d0, [x17, #0x238]
    // 0x148cb04: r0 = GDT[cid_x0 + -0xffa]()
    //     0x148cb04: sub             lr, x0, #0xffa
    //     0x148cb08: ldr             lr, [x21, lr, lsl #3]
    //     0x148cb0c: blr             lr
    // 0x148cb10: stur            x0, [fp, #-0x20]
    // 0x148cb14: r0 = BoxDecoration()
    //     0x148cb14: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x148cb18: mov             x3, x0
    // 0x148cb1c: ldur            x0, [fp, #-0x20]
    // 0x148cb20: stur            x3, [fp, #-0x28]
    // 0x148cb24: StoreField: r3->field_7 = r0
    //     0x148cb24: stur            w0, [x3, #7]
    // 0x148cb28: r0 = Instance_BoxShape
    //     0x148cb28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x148cb2c: ldr             x0, [x0, #0x80]
    // 0x148cb30: StoreField: r3->field_23 = r0
    //     0x148cb30: stur            w0, [x3, #0x23]
    // 0x148cb34: r1 = Null
    //     0x148cb34: mov             x1, NULL
    // 0x148cb38: r2 = 4
    //     0x148cb38: movz            x2, #0x4
    // 0x148cb3c: r0 = AllocateArray()
    //     0x148cb3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148cb40: stur            x0, [fp, #-0x20]
    // 0x148cb44: r16 = "Bag "
    //     0x148cb44: add             x16, PP, #0x34, lsl #12  ; [pp+0x346a8] "Bag "
    //     0x148cb48: ldr             x16, [x16, #0x6a8]
    // 0x148cb4c: StoreField: r0->field_f = r16
    //     0x148cb4c: stur            w16, [x0, #0xf]
    // 0x148cb50: ldur            x2, [fp, #-0x10]
    // 0x148cb54: LoadField: r1 = r2->field_f
    //     0x148cb54: ldur            w1, [x2, #0xf]
    // 0x148cb58: DecompressPointer r1
    //     0x148cb58: add             x1, x1, HEAP, lsl #32
    // 0x148cb5c: r0 = controller()
    //     0x148cb5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148cb60: LoadField: r1 = r0->field_73
    //     0x148cb60: ldur            w1, [x0, #0x73]
    // 0x148cb64: DecompressPointer r1
    //     0x148cb64: add             x1, x1, HEAP, lsl #32
    // 0x148cb68: r0 = value()
    //     0x148cb68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148cb6c: LoadField: r1 = r0->field_b
    //     0x148cb6c: ldur            w1, [x0, #0xb]
    // 0x148cb70: DecompressPointer r1
    //     0x148cb70: add             x1, x1, HEAP, lsl #32
    // 0x148cb74: cmp             w1, NULL
    // 0x148cb78: b.ne            #0x148cb84
    // 0x148cb7c: r0 = Null
    //     0x148cb7c: mov             x0, NULL
    // 0x148cb80: b               #0x148cba8
    // 0x148cb84: LoadField: r0 = r1->field_f
    //     0x148cb84: ldur            w0, [x1, #0xf]
    // 0x148cb88: DecompressPointer r0
    //     0x148cb88: add             x0, x0, HEAP, lsl #32
    // 0x148cb8c: cmp             w0, NULL
    // 0x148cb90: b.ne            #0x148cb9c
    // 0x148cb94: r0 = Null
    //     0x148cb94: mov             x0, NULL
    // 0x148cb98: b               #0x148cba8
    // 0x148cb9c: LoadField: r1 = r0->field_7
    //     0x148cb9c: ldur            w1, [x0, #7]
    // 0x148cba0: DecompressPointer r1
    //     0x148cba0: add             x1, x1, HEAP, lsl #32
    // 0x148cba4: mov             x0, x1
    // 0x148cba8: cmp             w0, NULL
    // 0x148cbac: b.ne            #0x148cbb4
    // 0x148cbb0: r0 = ""
    //     0x148cbb0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148cbb4: ldur            x2, [fp, #-0x10]
    // 0x148cbb8: ldur            x1, [fp, #-0x20]
    // 0x148cbbc: ArrayStore: r1[1] = r0  ; List_4
    //     0x148cbbc: add             x25, x1, #0x13
    //     0x148cbc0: str             w0, [x25]
    //     0x148cbc4: tbz             w0, #0, #0x148cbe0
    //     0x148cbc8: ldurb           w16, [x1, #-1]
    //     0x148cbcc: ldurb           w17, [x0, #-1]
    //     0x148cbd0: and             x16, x17, x16, lsr #2
    //     0x148cbd4: tst             x16, HEAP, lsr #32
    //     0x148cbd8: b.eq            #0x148cbe0
    //     0x148cbdc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148cbe0: ldur            x16, [fp, #-0x20]
    // 0x148cbe4: str             x16, [SP]
    // 0x148cbe8: r0 = _interpolate()
    //     0x148cbe8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x148cbec: ldur            x2, [fp, #-0x10]
    // 0x148cbf0: stur            x0, [fp, #-0x20]
    // 0x148cbf4: LoadField: r1 = r2->field_13
    //     0x148cbf4: ldur            w1, [x2, #0x13]
    // 0x148cbf8: DecompressPointer r1
    //     0x148cbf8: add             x1, x1, HEAP, lsl #32
    // 0x148cbfc: r0 = of()
    //     0x148cbfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148cc00: LoadField: r1 = r0->field_87
    //     0x148cc00: ldur            w1, [x0, #0x87]
    // 0x148cc04: DecompressPointer r1
    //     0x148cc04: add             x1, x1, HEAP, lsl #32
    // 0x148cc08: LoadField: r0 = r1->field_7
    //     0x148cc08: ldur            w0, [x1, #7]
    // 0x148cc0c: DecompressPointer r0
    //     0x148cc0c: add             x0, x0, HEAP, lsl #32
    // 0x148cc10: r16 = Instance_Color
    //     0x148cc10: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148cc14: r30 = 14.000000
    //     0x148cc14: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x148cc18: ldr             lr, [lr, #0x1d8]
    // 0x148cc1c: stp             lr, x16, [SP]
    // 0x148cc20: mov             x1, x0
    // 0x148cc24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x148cc24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x148cc28: ldr             x4, [x4, #0x9b8]
    // 0x148cc2c: r0 = copyWith()
    //     0x148cc2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148cc30: stur            x0, [fp, #-0x30]
    // 0x148cc34: r0 = Text()
    //     0x148cc34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148cc38: mov             x3, x0
    // 0x148cc3c: ldur            x0, [fp, #-0x20]
    // 0x148cc40: stur            x3, [fp, #-0x38]
    // 0x148cc44: StoreField: r3->field_b = r0
    //     0x148cc44: stur            w0, [x3, #0xb]
    // 0x148cc48: ldur            x0, [fp, #-0x30]
    // 0x148cc4c: StoreField: r3->field_13 = r0
    //     0x148cc4c: stur            w0, [x3, #0x13]
    // 0x148cc50: r1 = <Widget>
    //     0x148cc50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148cc54: r2 = 0
    //     0x148cc54: movz            x2, #0
    // 0x148cc58: r0 = _GrowableList()
    //     0x148cc58: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x148cc5c: ldur            x2, [fp, #-0x10]
    // 0x148cc60: stur            x0, [fp, #-0x20]
    // 0x148cc64: LoadField: r1 = r2->field_f
    //     0x148cc64: ldur            w1, [x2, #0xf]
    // 0x148cc68: DecompressPointer r1
    //     0x148cc68: add             x1, x1, HEAP, lsl #32
    // 0x148cc6c: r0 = controller()
    //     0x148cc6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148cc70: LoadField: r1 = r0->field_73
    //     0x148cc70: ldur            w1, [x0, #0x73]
    // 0x148cc74: DecompressPointer r1
    //     0x148cc74: add             x1, x1, HEAP, lsl #32
    // 0x148cc78: r0 = value()
    //     0x148cc78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148cc7c: LoadField: r1 = r0->field_b
    //     0x148cc7c: ldur            w1, [x0, #0xb]
    // 0x148cc80: DecompressPointer r1
    //     0x148cc80: add             x1, x1, HEAP, lsl #32
    // 0x148cc84: cmp             w1, NULL
    // 0x148cc88: b.ne            #0x148cc94
    // 0x148cc8c: ldur            x2, [fp, #-0x20]
    // 0x148cc90: b               #0x148d50c
    // 0x148cc94: LoadField: r0 = r1->field_43
    //     0x148cc94: ldur            w0, [x1, #0x43]
    // 0x148cc98: DecompressPointer r0
    //     0x148cc98: add             x0, x0, HEAP, lsl #32
    // 0x148cc9c: cmp             w0, NULL
    // 0x148cca0: b.eq            #0x148d508
    // 0x148cca4: ldur            x2, [fp, #-0x10]
    // 0x148cca8: LoadField: r1 = r2->field_f
    //     0x148cca8: ldur            w1, [x2, #0xf]
    // 0x148ccac: DecompressPointer r1
    //     0x148ccac: add             x1, x1, HEAP, lsl #32
    // 0x148ccb0: r0 = controller()
    //     0x148ccb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148ccb4: LoadField: r1 = r0->field_73
    //     0x148ccb4: ldur            w1, [x0, #0x73]
    // 0x148ccb8: DecompressPointer r1
    //     0x148ccb8: add             x1, x1, HEAP, lsl #32
    // 0x148ccbc: r0 = value()
    //     0x148ccbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148ccc0: LoadField: r1 = r0->field_b
    //     0x148ccc0: ldur            w1, [x0, #0xb]
    // 0x148ccc4: DecompressPointer r1
    //     0x148ccc4: add             x1, x1, HEAP, lsl #32
    // 0x148ccc8: cmp             w1, NULL
    // 0x148cccc: b.ne            #0x148ccd8
    // 0x148ccd0: r0 = Null
    //     0x148ccd0: mov             x0, NULL
    // 0x148ccd4: b               #0x148ccfc
    // 0x148ccd8: LoadField: r0 = r1->field_43
    //     0x148ccd8: ldur            w0, [x1, #0x43]
    // 0x148ccdc: DecompressPointer r0
    //     0x148ccdc: add             x0, x0, HEAP, lsl #32
    // 0x148cce0: cmp             w0, NULL
    // 0x148cce4: b.ne            #0x148ccf0
    // 0x148cce8: r0 = Null
    //     0x148cce8: mov             x0, NULL
    // 0x148ccec: b               #0x148ccfc
    // 0x148ccf0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148ccf0: ldur            w1, [x0, #0x17]
    // 0x148ccf4: DecompressPointer r1
    //     0x148ccf4: add             x1, x1, HEAP, lsl #32
    // 0x148ccf8: mov             x0, x1
    // 0x148ccfc: cmp             w0, NULL
    // 0x148cd00: b.ne            #0x148cd08
    // 0x148cd04: r0 = false
    //     0x148cd04: add             x0, NULL, #0x30  ; false
    // 0x148cd08: ldur            x2, [fp, #-0x10]
    // 0x148cd0c: stur            x0, [fp, #-0x30]
    // 0x148cd10: r0 = Radius()
    //     0x148cd10: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x148cd14: d0 = 12.000000
    //     0x148cd14: fmov            d0, #12.00000000
    // 0x148cd18: stur            x0, [fp, #-0x40]
    // 0x148cd1c: StoreField: r0->field_7 = d0
    //     0x148cd1c: stur            d0, [x0, #7]
    // 0x148cd20: StoreField: r0->field_f = d0
    //     0x148cd20: stur            d0, [x0, #0xf]
    // 0x148cd24: r0 = BorderRadius()
    //     0x148cd24: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x148cd28: mov             x2, x0
    // 0x148cd2c: ldur            x0, [fp, #-0x40]
    // 0x148cd30: stur            x2, [fp, #-0x48]
    // 0x148cd34: StoreField: r2->field_7 = r0
    //     0x148cd34: stur            w0, [x2, #7]
    // 0x148cd38: StoreField: r2->field_b = r0
    //     0x148cd38: stur            w0, [x2, #0xb]
    // 0x148cd3c: StoreField: r2->field_f = r0
    //     0x148cd3c: stur            w0, [x2, #0xf]
    // 0x148cd40: StoreField: r2->field_13 = r0
    //     0x148cd40: stur            w0, [x2, #0x13]
    // 0x148cd44: r1 = Instance_Color
    //     0x148cd44: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148cd48: d0 = 0.070000
    //     0x148cd48: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x148cd4c: ldr             d0, [x17, #0x5f8]
    // 0x148cd50: r0 = withOpacity()
    //     0x148cd50: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148cd54: r16 = 1.000000
    //     0x148cd54: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x148cd58: str             x16, [SP]
    // 0x148cd5c: mov             x2, x0
    // 0x148cd60: r1 = Null
    //     0x148cd60: mov             x1, NULL
    // 0x148cd64: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x148cd64: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x148cd68: ldr             x4, [x4, #0x108]
    // 0x148cd6c: r0 = Border.all()
    //     0x148cd6c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x148cd70: stur            x0, [fp, #-0x40]
    // 0x148cd74: r0 = BoxDecoration()
    //     0x148cd74: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x148cd78: mov             x2, x0
    // 0x148cd7c: ldur            x0, [fp, #-0x40]
    // 0x148cd80: stur            x2, [fp, #-0x50]
    // 0x148cd84: StoreField: r2->field_f = r0
    //     0x148cd84: stur            w0, [x2, #0xf]
    // 0x148cd88: ldur            x0, [fp, #-0x48]
    // 0x148cd8c: StoreField: r2->field_13 = r0
    //     0x148cd8c: stur            w0, [x2, #0x13]
    // 0x148cd90: r0 = Instance_LinearGradient
    //     0x148cd90: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0x148cd94: ldr             x0, [x0, #0x660]
    // 0x148cd98: StoreField: r2->field_1b = r0
    //     0x148cd98: stur            w0, [x2, #0x1b]
    // 0x148cd9c: r0 = Instance_BoxShape
    //     0x148cd9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x148cda0: ldr             x0, [x0, #0x80]
    // 0x148cda4: StoreField: r2->field_23 = r0
    //     0x148cda4: stur            w0, [x2, #0x23]
    // 0x148cda8: ldur            x0, [fp, #-0x10]
    // 0x148cdac: LoadField: r1 = r0->field_13
    //     0x148cdac: ldur            w1, [x0, #0x13]
    // 0x148cdb0: DecompressPointer r1
    //     0x148cdb0: add             x1, x1, HEAP, lsl #32
    // 0x148cdb4: r0 = of()
    //     0x148cdb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148cdb8: LoadField: r1 = r0->field_87
    //     0x148cdb8: ldur            w1, [x0, #0x87]
    // 0x148cdbc: DecompressPointer r1
    //     0x148cdbc: add             x1, x1, HEAP, lsl #32
    // 0x148cdc0: LoadField: r0 = r1->field_7
    //     0x148cdc0: ldur            w0, [x1, #7]
    // 0x148cdc4: DecompressPointer r0
    //     0x148cdc4: add             x0, x0, HEAP, lsl #32
    // 0x148cdc8: r16 = 12.000000
    //     0x148cdc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148cdcc: ldr             x16, [x16, #0x9e8]
    // 0x148cdd0: r30 = Instance_Color
    //     0x148cdd0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x148cdd4: stp             lr, x16, [SP]
    // 0x148cdd8: mov             x1, x0
    // 0x148cddc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148cddc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148cde0: ldr             x4, [x4, #0xaa0]
    // 0x148cde4: r0 = copyWith()
    //     0x148cde4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148cde8: stur            x0, [fp, #-0x40]
    // 0x148cdec: r0 = Text()
    //     0x148cdec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148cdf0: mov             x1, x0
    // 0x148cdf4: r0 = "Free"
    //     0x148cdf4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x148cdf8: ldr             x0, [x0, #0x668]
    // 0x148cdfc: stur            x1, [fp, #-0x48]
    // 0x148ce00: StoreField: r1->field_b = r0
    //     0x148ce00: stur            w0, [x1, #0xb]
    // 0x148ce04: ldur            x2, [fp, #-0x40]
    // 0x148ce08: StoreField: r1->field_13 = r2
    //     0x148ce08: stur            w2, [x1, #0x13]
    // 0x148ce0c: r0 = Center()
    //     0x148ce0c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x148ce10: mov             x1, x0
    // 0x148ce14: r0 = Instance_Alignment
    //     0x148ce14: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x148ce18: ldr             x0, [x0, #0xb10]
    // 0x148ce1c: stur            x1, [fp, #-0x40]
    // 0x148ce20: StoreField: r1->field_f = r0
    //     0x148ce20: stur            w0, [x1, #0xf]
    // 0x148ce24: ldur            x0, [fp, #-0x48]
    // 0x148ce28: StoreField: r1->field_b = r0
    //     0x148ce28: stur            w0, [x1, #0xb]
    // 0x148ce2c: r0 = RotatedBox()
    //     0x148ce2c: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0x148ce30: mov             x1, x0
    // 0x148ce34: r0 = -1
    //     0x148ce34: movn            x0, #0
    // 0x148ce38: stur            x1, [fp, #-0x48]
    // 0x148ce3c: StoreField: r1->field_f = r0
    //     0x148ce3c: stur            x0, [x1, #0xf]
    // 0x148ce40: ldur            x0, [fp, #-0x40]
    // 0x148ce44: StoreField: r1->field_b = r0
    //     0x148ce44: stur            w0, [x1, #0xb]
    // 0x148ce48: r0 = Container()
    //     0x148ce48: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148ce4c: stur            x0, [fp, #-0x40]
    // 0x148ce50: r16 = 24.000000
    //     0x148ce50: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x148ce54: ldr             x16, [x16, #0xba8]
    // 0x148ce58: r30 = 56.000000
    //     0x148ce58: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148ce5c: ldr             lr, [lr, #0xb78]
    // 0x148ce60: stp             lr, x16, [SP, #0x10]
    // 0x148ce64: r16 = Instance_BoxDecoration
    //     0x148ce64: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0x148ce68: ldr             x16, [x16, #0xdb0]
    // 0x148ce6c: ldur            lr, [fp, #-0x48]
    // 0x148ce70: stp             lr, x16, [SP]
    // 0x148ce74: mov             x1, x0
    // 0x148ce78: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x148ce78: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x148ce7c: ldr             x4, [x4, #0x870]
    // 0x148ce80: r0 = Container()
    //     0x148ce80: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148ce84: ldur            x2, [fp, #-0x10]
    // 0x148ce88: LoadField: r1 = r2->field_f
    //     0x148ce88: ldur            w1, [x2, #0xf]
    // 0x148ce8c: DecompressPointer r1
    //     0x148ce8c: add             x1, x1, HEAP, lsl #32
    // 0x148ce90: r0 = controller()
    //     0x148ce90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148ce94: LoadField: r1 = r0->field_73
    //     0x148ce94: ldur            w1, [x0, #0x73]
    // 0x148ce98: DecompressPointer r1
    //     0x148ce98: add             x1, x1, HEAP, lsl #32
    // 0x148ce9c: r0 = value()
    //     0x148ce9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148cea0: LoadField: r1 = r0->field_b
    //     0x148cea0: ldur            w1, [x0, #0xb]
    // 0x148cea4: DecompressPointer r1
    //     0x148cea4: add             x1, x1, HEAP, lsl #32
    // 0x148cea8: cmp             w1, NULL
    // 0x148ceac: b.ne            #0x148ceb8
    // 0x148ceb0: r0 = Null
    //     0x148ceb0: mov             x0, NULL
    // 0x148ceb4: b               #0x148cedc
    // 0x148ceb8: LoadField: r0 = r1->field_43
    //     0x148ceb8: ldur            w0, [x1, #0x43]
    // 0x148cebc: DecompressPointer r0
    //     0x148cebc: add             x0, x0, HEAP, lsl #32
    // 0x148cec0: cmp             w0, NULL
    // 0x148cec4: b.ne            #0x148ced0
    // 0x148cec8: r0 = Null
    //     0x148cec8: mov             x0, NULL
    // 0x148cecc: b               #0x148cedc
    // 0x148ced0: LoadField: r1 = r0->field_7
    //     0x148ced0: ldur            w1, [x0, #7]
    // 0x148ced4: DecompressPointer r1
    //     0x148ced4: add             x1, x1, HEAP, lsl #32
    // 0x148ced8: mov             x0, x1
    // 0x148cedc: cmp             w0, NULL
    // 0x148cee0: b.ne            #0x148ceec
    // 0x148cee4: r3 = ""
    //     0x148cee4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148cee8: b               #0x148cef0
    // 0x148ceec: mov             x3, x0
    // 0x148cef0: ldur            x0, [fp, #-0x10]
    // 0x148cef4: stur            x3, [fp, #-0x48]
    // 0x148cef8: r1 = Function '<anonymous closure>':.
    //     0x148cef8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40db8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148cefc: ldr             x1, [x1, #0xdb8]
    // 0x148cf00: r2 = Null
    //     0x148cf00: mov             x2, NULL
    // 0x148cf04: r0 = AllocateClosure()
    //     0x148cf04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148cf08: r1 = Function '<anonymous closure>':.
    //     0x148cf08: add             x1, PP, #0x40, lsl #12  ; [pp+0x40dc0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148cf0c: ldr             x1, [x1, #0xdc0]
    // 0x148cf10: r2 = Null
    //     0x148cf10: mov             x2, NULL
    // 0x148cf14: stur            x0, [fp, #-0x58]
    // 0x148cf18: r0 = AllocateClosure()
    //     0x148cf18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148cf1c: stur            x0, [fp, #-0x60]
    // 0x148cf20: r0 = CachedNetworkImage()
    //     0x148cf20: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x148cf24: stur            x0, [fp, #-0x68]
    // 0x148cf28: r16 = 56.000000
    //     0x148cf28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148cf2c: ldr             x16, [x16, #0xb78]
    // 0x148cf30: r30 = 56.000000
    //     0x148cf30: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148cf34: ldr             lr, [lr, #0xb78]
    // 0x148cf38: stp             lr, x16, [SP, #0x18]
    // 0x148cf3c: r16 = Instance_BoxFit
    //     0x148cf3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x148cf40: ldr             x16, [x16, #0x118]
    // 0x148cf44: ldur            lr, [fp, #-0x58]
    // 0x148cf48: stp             lr, x16, [SP, #8]
    // 0x148cf4c: ldur            x16, [fp, #-0x60]
    // 0x148cf50: str             x16, [SP]
    // 0x148cf54: mov             x1, x0
    // 0x148cf58: ldur            x2, [fp, #-0x48]
    // 0x148cf5c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0x148cf5c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0x148cf60: ldr             x4, [x4, #0x710]
    // 0x148cf64: r0 = CachedNetworkImage()
    //     0x148cf64: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x148cf68: ldur            x2, [fp, #-0x10]
    // 0x148cf6c: LoadField: r1 = r2->field_f
    //     0x148cf6c: ldur            w1, [x2, #0xf]
    // 0x148cf70: DecompressPointer r1
    //     0x148cf70: add             x1, x1, HEAP, lsl #32
    // 0x148cf74: r0 = controller()
    //     0x148cf74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148cf78: LoadField: r1 = r0->field_73
    //     0x148cf78: ldur            w1, [x0, #0x73]
    // 0x148cf7c: DecompressPointer r1
    //     0x148cf7c: add             x1, x1, HEAP, lsl #32
    // 0x148cf80: r0 = value()
    //     0x148cf80: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148cf84: LoadField: r1 = r0->field_b
    //     0x148cf84: ldur            w1, [x0, #0xb]
    // 0x148cf88: DecompressPointer r1
    //     0x148cf88: add             x1, x1, HEAP, lsl #32
    // 0x148cf8c: cmp             w1, NULL
    // 0x148cf90: b.ne            #0x148cf9c
    // 0x148cf94: r0 = Null
    //     0x148cf94: mov             x0, NULL
    // 0x148cf98: b               #0x148cfc0
    // 0x148cf9c: LoadField: r0 = r1->field_43
    //     0x148cf9c: ldur            w0, [x1, #0x43]
    // 0x148cfa0: DecompressPointer r0
    //     0x148cfa0: add             x0, x0, HEAP, lsl #32
    // 0x148cfa4: cmp             w0, NULL
    // 0x148cfa8: b.ne            #0x148cfb4
    // 0x148cfac: r0 = Null
    //     0x148cfac: mov             x0, NULL
    // 0x148cfb0: b               #0x148cfc0
    // 0x148cfb4: LoadField: r1 = r0->field_b
    //     0x148cfb4: ldur            w1, [x0, #0xb]
    // 0x148cfb8: DecompressPointer r1
    //     0x148cfb8: add             x1, x1, HEAP, lsl #32
    // 0x148cfbc: mov             x0, x1
    // 0x148cfc0: cmp             w0, NULL
    // 0x148cfc4: b.ne            #0x148cfcc
    // 0x148cfc8: r0 = ""
    //     0x148cfc8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148cfcc: ldur            x2, [fp, #-0x10]
    // 0x148cfd0: stur            x0, [fp, #-0x48]
    // 0x148cfd4: LoadField: r1 = r2->field_13
    //     0x148cfd4: ldur            w1, [x2, #0x13]
    // 0x148cfd8: DecompressPointer r1
    //     0x148cfd8: add             x1, x1, HEAP, lsl #32
    // 0x148cfdc: r0 = of()
    //     0x148cfdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148cfe0: LoadField: r1 = r0->field_87
    //     0x148cfe0: ldur            w1, [x0, #0x87]
    // 0x148cfe4: DecompressPointer r1
    //     0x148cfe4: add             x1, x1, HEAP, lsl #32
    // 0x148cfe8: LoadField: r0 = r1->field_7
    //     0x148cfe8: ldur            w0, [x1, #7]
    // 0x148cfec: DecompressPointer r0
    //     0x148cfec: add             x0, x0, HEAP, lsl #32
    // 0x148cff0: r16 = 12.000000
    //     0x148cff0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148cff4: ldr             x16, [x16, #0x9e8]
    // 0x148cff8: r30 = Instance_Color
    //     0x148cff8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148cffc: stp             lr, x16, [SP]
    // 0x148d000: mov             x1, x0
    // 0x148d004: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148d004: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148d008: ldr             x4, [x4, #0xaa0]
    // 0x148d00c: r0 = copyWith()
    //     0x148d00c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148d010: stur            x0, [fp, #-0x58]
    // 0x148d014: r0 = Text()
    //     0x148d014: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148d018: mov             x1, x0
    // 0x148d01c: ldur            x0, [fp, #-0x48]
    // 0x148d020: stur            x1, [fp, #-0x60]
    // 0x148d024: StoreField: r1->field_b = r0
    //     0x148d024: stur            w0, [x1, #0xb]
    // 0x148d028: ldur            x0, [fp, #-0x58]
    // 0x148d02c: StoreField: r1->field_13 = r0
    //     0x148d02c: stur            w0, [x1, #0x13]
    // 0x148d030: r0 = Instance_TextOverflow
    //     0x148d030: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x148d034: ldr             x0, [x0, #0xe10]
    // 0x148d038: StoreField: r1->field_2b = r0
    //     0x148d038: stur            w0, [x1, #0x2b]
    // 0x148d03c: r0 = 2
    //     0x148d03c: movz            x0, #0x2
    // 0x148d040: StoreField: r1->field_37 = r0
    //     0x148d040: stur            w0, [x1, #0x37]
    // 0x148d044: r0 = SizedBox()
    //     0x148d044: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x148d048: mov             x2, x0
    // 0x148d04c: r0 = 150.000000
    //     0x148d04c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0x148d050: ldr             x0, [x0, #0x690]
    // 0x148d054: stur            x2, [fp, #-0x48]
    // 0x148d058: StoreField: r2->field_f = r0
    //     0x148d058: stur            w0, [x2, #0xf]
    // 0x148d05c: ldur            x0, [fp, #-0x60]
    // 0x148d060: StoreField: r2->field_b = r0
    //     0x148d060: stur            w0, [x2, #0xb]
    // 0x148d064: ldur            x0, [fp, #-0x10]
    // 0x148d068: LoadField: r1 = r0->field_13
    //     0x148d068: ldur            w1, [x0, #0x13]
    // 0x148d06c: DecompressPointer r1
    //     0x148d06c: add             x1, x1, HEAP, lsl #32
    // 0x148d070: r0 = of()
    //     0x148d070: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148d074: LoadField: r1 = r0->field_87
    //     0x148d074: ldur            w1, [x0, #0x87]
    // 0x148d078: DecompressPointer r1
    //     0x148d078: add             x1, x1, HEAP, lsl #32
    // 0x148d07c: LoadField: r0 = r1->field_2b
    //     0x148d07c: ldur            w0, [x1, #0x2b]
    // 0x148d080: DecompressPointer r0
    //     0x148d080: add             x0, x0, HEAP, lsl #32
    // 0x148d084: r16 = 12.000000
    //     0x148d084: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148d088: ldr             x16, [x16, #0x9e8]
    // 0x148d08c: r30 = Instance_Color
    //     0x148d08c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x148d090: ldr             lr, [lr, #0x858]
    // 0x148d094: stp             lr, x16, [SP]
    // 0x148d098: mov             x1, x0
    // 0x148d09c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148d09c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148d0a0: ldr             x4, [x4, #0xaa0]
    // 0x148d0a4: r0 = copyWith()
    //     0x148d0a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148d0a8: stur            x0, [fp, #-0x58]
    // 0x148d0ac: r0 = Text()
    //     0x148d0ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148d0b0: mov             x2, x0
    // 0x148d0b4: r0 = "Free"
    //     0x148d0b4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x148d0b8: ldr             x0, [x0, #0x668]
    // 0x148d0bc: stur            x2, [fp, #-0x60]
    // 0x148d0c0: StoreField: r2->field_b = r0
    //     0x148d0c0: stur            w0, [x2, #0xb]
    // 0x148d0c4: ldur            x0, [fp, #-0x58]
    // 0x148d0c8: StoreField: r2->field_13 = r0
    //     0x148d0c8: stur            w0, [x2, #0x13]
    // 0x148d0cc: ldur            x0, [fp, #-0x10]
    // 0x148d0d0: LoadField: r1 = r0->field_f
    //     0x148d0d0: ldur            w1, [x0, #0xf]
    // 0x148d0d4: DecompressPointer r1
    //     0x148d0d4: add             x1, x1, HEAP, lsl #32
    // 0x148d0d8: r0 = controller()
    //     0x148d0d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148d0dc: LoadField: r1 = r0->field_73
    //     0x148d0dc: ldur            w1, [x0, #0x73]
    // 0x148d0e0: DecompressPointer r1
    //     0x148d0e0: add             x1, x1, HEAP, lsl #32
    // 0x148d0e4: r0 = value()
    //     0x148d0e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148d0e8: LoadField: r1 = r0->field_b
    //     0x148d0e8: ldur            w1, [x0, #0xb]
    // 0x148d0ec: DecompressPointer r1
    //     0x148d0ec: add             x1, x1, HEAP, lsl #32
    // 0x148d0f0: cmp             w1, NULL
    // 0x148d0f4: b.ne            #0x148d100
    // 0x148d0f8: r0 = Null
    //     0x148d0f8: mov             x0, NULL
    // 0x148d0fc: b               #0x148d124
    // 0x148d100: LoadField: r0 = r1->field_43
    //     0x148d100: ldur            w0, [x1, #0x43]
    // 0x148d104: DecompressPointer r0
    //     0x148d104: add             x0, x0, HEAP, lsl #32
    // 0x148d108: cmp             w0, NULL
    // 0x148d10c: b.ne            #0x148d118
    // 0x148d110: r0 = Null
    //     0x148d110: mov             x0, NULL
    // 0x148d114: b               #0x148d124
    // 0x148d118: LoadField: r1 = r0->field_13
    //     0x148d118: ldur            w1, [x0, #0x13]
    // 0x148d11c: DecompressPointer r1
    //     0x148d11c: add             x1, x1, HEAP, lsl #32
    // 0x148d120: mov             x0, x1
    // 0x148d124: cmp             w0, NULL
    // 0x148d128: b.ne            #0x148d134
    // 0x148d12c: r8 = ""
    //     0x148d12c: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148d130: b               #0x148d138
    // 0x148d134: mov             x8, x0
    // 0x148d138: ldur            x2, [fp, #-0x10]
    // 0x148d13c: ldur            x7, [fp, #-0x20]
    // 0x148d140: ldur            x6, [fp, #-0x30]
    // 0x148d144: ldur            x5, [fp, #-0x40]
    // 0x148d148: ldur            x4, [fp, #-0x68]
    // 0x148d14c: ldur            x3, [fp, #-0x48]
    // 0x148d150: ldur            x0, [fp, #-0x60]
    // 0x148d154: stur            x8, [fp, #-0x58]
    // 0x148d158: LoadField: r1 = r2->field_13
    //     0x148d158: ldur            w1, [x2, #0x13]
    // 0x148d15c: DecompressPointer r1
    //     0x148d15c: add             x1, x1, HEAP, lsl #32
    // 0x148d160: r0 = of()
    //     0x148d160: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148d164: LoadField: r1 = r0->field_87
    //     0x148d164: ldur            w1, [x0, #0x87]
    // 0x148d168: DecompressPointer r1
    //     0x148d168: add             x1, x1, HEAP, lsl #32
    // 0x148d16c: LoadField: r0 = r1->field_2b
    //     0x148d16c: ldur            w0, [x1, #0x2b]
    // 0x148d170: DecompressPointer r0
    //     0x148d170: add             x0, x0, HEAP, lsl #32
    // 0x148d174: r16 = 12.000000
    //     0x148d174: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148d178: ldr             x16, [x16, #0x9e8]
    // 0x148d17c: r30 = Instance_TextDecoration
    //     0x148d17c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x148d180: ldr             lr, [lr, #0xe30]
    // 0x148d184: stp             lr, x16, [SP]
    // 0x148d188: mov             x1, x0
    // 0x148d18c: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x148d18c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x148d190: ldr             x4, [x4, #0x698]
    // 0x148d194: r0 = copyWith()
    //     0x148d194: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148d198: stur            x0, [fp, #-0x70]
    // 0x148d19c: r0 = Text()
    //     0x148d19c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148d1a0: mov             x3, x0
    // 0x148d1a4: ldur            x0, [fp, #-0x58]
    // 0x148d1a8: stur            x3, [fp, #-0x78]
    // 0x148d1ac: StoreField: r3->field_b = r0
    //     0x148d1ac: stur            w0, [x3, #0xb]
    // 0x148d1b0: ldur            x0, [fp, #-0x70]
    // 0x148d1b4: StoreField: r3->field_13 = r0
    //     0x148d1b4: stur            w0, [x3, #0x13]
    // 0x148d1b8: r1 = Null
    //     0x148d1b8: mov             x1, NULL
    // 0x148d1bc: r2 = 6
    //     0x148d1bc: movz            x2, #0x6
    // 0x148d1c0: r0 = AllocateArray()
    //     0x148d1c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148d1c4: mov             x2, x0
    // 0x148d1c8: ldur            x0, [fp, #-0x60]
    // 0x148d1cc: stur            x2, [fp, #-0x58]
    // 0x148d1d0: StoreField: r2->field_f = r0
    //     0x148d1d0: stur            w0, [x2, #0xf]
    // 0x148d1d4: r16 = Instance_SizedBox
    //     0x148d1d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x148d1d8: ldr             x16, [x16, #0xa50]
    // 0x148d1dc: StoreField: r2->field_13 = r16
    //     0x148d1dc: stur            w16, [x2, #0x13]
    // 0x148d1e0: ldur            x0, [fp, #-0x78]
    // 0x148d1e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x148d1e4: stur            w0, [x2, #0x17]
    // 0x148d1e8: r1 = <Widget>
    //     0x148d1e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148d1ec: r0 = AllocateGrowableArray()
    //     0x148d1ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148d1f0: mov             x1, x0
    // 0x148d1f4: ldur            x0, [fp, #-0x58]
    // 0x148d1f8: stur            x1, [fp, #-0x60]
    // 0x148d1fc: StoreField: r1->field_f = r0
    //     0x148d1fc: stur            w0, [x1, #0xf]
    // 0x148d200: r2 = 6
    //     0x148d200: movz            x2, #0x6
    // 0x148d204: StoreField: r1->field_b = r2
    //     0x148d204: stur            w2, [x1, #0xb]
    // 0x148d208: r0 = Row()
    //     0x148d208: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148d20c: mov             x3, x0
    // 0x148d210: r0 = Instance_Axis
    //     0x148d210: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148d214: stur            x3, [fp, #-0x58]
    // 0x148d218: StoreField: r3->field_f = r0
    //     0x148d218: stur            w0, [x3, #0xf]
    // 0x148d21c: r4 = Instance_MainAxisAlignment
    //     0x148d21c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148d220: ldr             x4, [x4, #0xa08]
    // 0x148d224: StoreField: r3->field_13 = r4
    //     0x148d224: stur            w4, [x3, #0x13]
    // 0x148d228: r5 = Instance_MainAxisSize
    //     0x148d228: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148d22c: ldr             x5, [x5, #0xa10]
    // 0x148d230: ArrayStore: r3[0] = r5  ; List_4
    //     0x148d230: stur            w5, [x3, #0x17]
    // 0x148d234: r6 = Instance_CrossAxisAlignment
    //     0x148d234: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148d238: ldr             x6, [x6, #0xa18]
    // 0x148d23c: StoreField: r3->field_1b = r6
    //     0x148d23c: stur            w6, [x3, #0x1b]
    // 0x148d240: r7 = Instance_VerticalDirection
    //     0x148d240: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148d244: ldr             x7, [x7, #0xa20]
    // 0x148d248: StoreField: r3->field_23 = r7
    //     0x148d248: stur            w7, [x3, #0x23]
    // 0x148d24c: r8 = Instance_Clip
    //     0x148d24c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148d250: ldr             x8, [x8, #0x38]
    // 0x148d254: StoreField: r3->field_2b = r8
    //     0x148d254: stur            w8, [x3, #0x2b]
    // 0x148d258: StoreField: r3->field_2f = rZR
    //     0x148d258: stur            xzr, [x3, #0x2f]
    // 0x148d25c: ldur            x1, [fp, #-0x60]
    // 0x148d260: StoreField: r3->field_b = r1
    //     0x148d260: stur            w1, [x3, #0xb]
    // 0x148d264: r1 = Null
    //     0x148d264: mov             x1, NULL
    // 0x148d268: r2 = 6
    //     0x148d268: movz            x2, #0x6
    // 0x148d26c: r0 = AllocateArray()
    //     0x148d26c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148d270: mov             x2, x0
    // 0x148d274: ldur            x0, [fp, #-0x48]
    // 0x148d278: stur            x2, [fp, #-0x60]
    // 0x148d27c: StoreField: r2->field_f = r0
    //     0x148d27c: stur            w0, [x2, #0xf]
    // 0x148d280: r16 = Instance_SizedBox
    //     0x148d280: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x148d284: ldr             x16, [x16, #0xc70]
    // 0x148d288: StoreField: r2->field_13 = r16
    //     0x148d288: stur            w16, [x2, #0x13]
    // 0x148d28c: ldur            x0, [fp, #-0x58]
    // 0x148d290: ArrayStore: r2[0] = r0  ; List_4
    //     0x148d290: stur            w0, [x2, #0x17]
    // 0x148d294: r1 = <Widget>
    //     0x148d294: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148d298: r0 = AllocateGrowableArray()
    //     0x148d298: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148d29c: mov             x1, x0
    // 0x148d2a0: ldur            x0, [fp, #-0x60]
    // 0x148d2a4: stur            x1, [fp, #-0x48]
    // 0x148d2a8: StoreField: r1->field_f = r0
    //     0x148d2a8: stur            w0, [x1, #0xf]
    // 0x148d2ac: r2 = 6
    //     0x148d2ac: movz            x2, #0x6
    // 0x148d2b0: StoreField: r1->field_b = r2
    //     0x148d2b0: stur            w2, [x1, #0xb]
    // 0x148d2b4: r0 = Column()
    //     0x148d2b4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148d2b8: mov             x1, x0
    // 0x148d2bc: r0 = Instance_Axis
    //     0x148d2bc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148d2c0: stur            x1, [fp, #-0x58]
    // 0x148d2c4: StoreField: r1->field_f = r0
    //     0x148d2c4: stur            w0, [x1, #0xf]
    // 0x148d2c8: r2 = Instance_MainAxisAlignment
    //     0x148d2c8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148d2cc: ldr             x2, [x2, #0xa08]
    // 0x148d2d0: StoreField: r1->field_13 = r2
    //     0x148d2d0: stur            w2, [x1, #0x13]
    // 0x148d2d4: r3 = Instance_MainAxisSize
    //     0x148d2d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148d2d8: ldr             x3, [x3, #0xa10]
    // 0x148d2dc: ArrayStore: r1[0] = r3  ; List_4
    //     0x148d2dc: stur            w3, [x1, #0x17]
    // 0x148d2e0: r4 = Instance_CrossAxisAlignment
    //     0x148d2e0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x148d2e4: ldr             x4, [x4, #0x890]
    // 0x148d2e8: StoreField: r1->field_1b = r4
    //     0x148d2e8: stur            w4, [x1, #0x1b]
    // 0x148d2ec: r4 = Instance_VerticalDirection
    //     0x148d2ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148d2f0: ldr             x4, [x4, #0xa20]
    // 0x148d2f4: StoreField: r1->field_23 = r4
    //     0x148d2f4: stur            w4, [x1, #0x23]
    // 0x148d2f8: r5 = Instance_Clip
    //     0x148d2f8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148d2fc: ldr             x5, [x5, #0x38]
    // 0x148d300: StoreField: r1->field_2b = r5
    //     0x148d300: stur            w5, [x1, #0x2b]
    // 0x148d304: StoreField: r1->field_2f = rZR
    //     0x148d304: stur            xzr, [x1, #0x2f]
    // 0x148d308: ldur            x6, [fp, #-0x48]
    // 0x148d30c: StoreField: r1->field_b = r6
    //     0x148d30c: stur            w6, [x1, #0xb]
    // 0x148d310: r0 = Padding()
    //     0x148d310: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148d314: mov             x2, x0
    // 0x148d318: r0 = Instance_EdgeInsets
    //     0x148d318: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x148d31c: ldr             x0, [x0, #0xa78]
    // 0x148d320: stur            x2, [fp, #-0x48]
    // 0x148d324: StoreField: r2->field_f = r0
    //     0x148d324: stur            w0, [x2, #0xf]
    // 0x148d328: ldur            x0, [fp, #-0x58]
    // 0x148d32c: StoreField: r2->field_b = r0
    //     0x148d32c: stur            w0, [x2, #0xb]
    // 0x148d330: r1 = <FlexParentData>
    //     0x148d330: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x148d334: ldr             x1, [x1, #0xe00]
    // 0x148d338: r0 = Expanded()
    //     0x148d338: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x148d33c: mov             x3, x0
    // 0x148d340: r0 = 1
    //     0x148d340: movz            x0, #0x1
    // 0x148d344: stur            x3, [fp, #-0x58]
    // 0x148d348: StoreField: r3->field_13 = r0
    //     0x148d348: stur            x0, [x3, #0x13]
    // 0x148d34c: r0 = Instance_FlexFit
    //     0x148d34c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x148d350: ldr             x0, [x0, #0xe08]
    // 0x148d354: StoreField: r3->field_1b = r0
    //     0x148d354: stur            w0, [x3, #0x1b]
    // 0x148d358: ldur            x0, [fp, #-0x48]
    // 0x148d35c: StoreField: r3->field_b = r0
    //     0x148d35c: stur            w0, [x3, #0xb]
    // 0x148d360: r1 = Null
    //     0x148d360: mov             x1, NULL
    // 0x148d364: r2 = 6
    //     0x148d364: movz            x2, #0x6
    // 0x148d368: r0 = AllocateArray()
    //     0x148d368: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148d36c: mov             x2, x0
    // 0x148d370: ldur            x0, [fp, #-0x40]
    // 0x148d374: stur            x2, [fp, #-0x48]
    // 0x148d378: StoreField: r2->field_f = r0
    //     0x148d378: stur            w0, [x2, #0xf]
    // 0x148d37c: ldur            x0, [fp, #-0x68]
    // 0x148d380: StoreField: r2->field_13 = r0
    //     0x148d380: stur            w0, [x2, #0x13]
    // 0x148d384: ldur            x0, [fp, #-0x58]
    // 0x148d388: ArrayStore: r2[0] = r0  ; List_4
    //     0x148d388: stur            w0, [x2, #0x17]
    // 0x148d38c: r1 = <Widget>
    //     0x148d38c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148d390: r0 = AllocateGrowableArray()
    //     0x148d390: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148d394: mov             x1, x0
    // 0x148d398: ldur            x0, [fp, #-0x48]
    // 0x148d39c: stur            x1, [fp, #-0x40]
    // 0x148d3a0: StoreField: r1->field_f = r0
    //     0x148d3a0: stur            w0, [x1, #0xf]
    // 0x148d3a4: r0 = 6
    //     0x148d3a4: movz            x0, #0x6
    // 0x148d3a8: StoreField: r1->field_b = r0
    //     0x148d3a8: stur            w0, [x1, #0xb]
    // 0x148d3ac: r0 = Row()
    //     0x148d3ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148d3b0: mov             x1, x0
    // 0x148d3b4: r0 = Instance_Axis
    //     0x148d3b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148d3b8: stur            x1, [fp, #-0x48]
    // 0x148d3bc: StoreField: r1->field_f = r0
    //     0x148d3bc: stur            w0, [x1, #0xf]
    // 0x148d3c0: r2 = Instance_MainAxisAlignment
    //     0x148d3c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148d3c4: ldr             x2, [x2, #0xa08]
    // 0x148d3c8: StoreField: r1->field_13 = r2
    //     0x148d3c8: stur            w2, [x1, #0x13]
    // 0x148d3cc: r3 = Instance_MainAxisSize
    //     0x148d3cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148d3d0: ldr             x3, [x3, #0xa10]
    // 0x148d3d4: ArrayStore: r1[0] = r3  ; List_4
    //     0x148d3d4: stur            w3, [x1, #0x17]
    // 0x148d3d8: r4 = Instance_CrossAxisAlignment
    //     0x148d3d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148d3dc: ldr             x4, [x4, #0xa18]
    // 0x148d3e0: StoreField: r1->field_1b = r4
    //     0x148d3e0: stur            w4, [x1, #0x1b]
    // 0x148d3e4: r5 = Instance_VerticalDirection
    //     0x148d3e4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148d3e8: ldr             x5, [x5, #0xa20]
    // 0x148d3ec: StoreField: r1->field_23 = r5
    //     0x148d3ec: stur            w5, [x1, #0x23]
    // 0x148d3f0: r6 = Instance_Clip
    //     0x148d3f0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148d3f4: ldr             x6, [x6, #0x38]
    // 0x148d3f8: StoreField: r1->field_2b = r6
    //     0x148d3f8: stur            w6, [x1, #0x2b]
    // 0x148d3fc: StoreField: r1->field_2f = rZR
    //     0x148d3fc: stur            xzr, [x1, #0x2f]
    // 0x148d400: ldur            x7, [fp, #-0x40]
    // 0x148d404: StoreField: r1->field_b = r7
    //     0x148d404: stur            w7, [x1, #0xb]
    // 0x148d408: r0 = Container()
    //     0x148d408: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148d40c: stur            x0, [fp, #-0x40]
    // 0x148d410: ldur            x16, [fp, #-0x50]
    // 0x148d414: ldur            lr, [fp, #-0x48]
    // 0x148d418: stp             lr, x16, [SP]
    // 0x148d41c: mov             x1, x0
    // 0x148d420: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x148d420: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x148d424: ldr             x4, [x4, #0x88]
    // 0x148d428: r0 = Container()
    //     0x148d428: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148d42c: r0 = Padding()
    //     0x148d42c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148d430: mov             x1, x0
    // 0x148d434: r0 = Instance_EdgeInsets
    //     0x148d434: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x148d438: ldr             x0, [x0, #0x778]
    // 0x148d43c: stur            x1, [fp, #-0x48]
    // 0x148d440: StoreField: r1->field_f = r0
    //     0x148d440: stur            w0, [x1, #0xf]
    // 0x148d444: ldur            x2, [fp, #-0x40]
    // 0x148d448: StoreField: r1->field_b = r2
    //     0x148d448: stur            w2, [x1, #0xb]
    // 0x148d44c: r0 = Visibility()
    //     0x148d44c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x148d450: mov             x2, x0
    // 0x148d454: ldur            x0, [fp, #-0x48]
    // 0x148d458: stur            x2, [fp, #-0x40]
    // 0x148d45c: StoreField: r2->field_b = r0
    //     0x148d45c: stur            w0, [x2, #0xb]
    // 0x148d460: r0 = Instance_SizedBox
    //     0x148d460: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x148d464: StoreField: r2->field_f = r0
    //     0x148d464: stur            w0, [x2, #0xf]
    // 0x148d468: ldur            x0, [fp, #-0x30]
    // 0x148d46c: StoreField: r2->field_13 = r0
    //     0x148d46c: stur            w0, [x2, #0x13]
    // 0x148d470: r0 = false
    //     0x148d470: add             x0, NULL, #0x30  ; false
    // 0x148d474: ArrayStore: r2[0] = r0  ; List_4
    //     0x148d474: stur            w0, [x2, #0x17]
    // 0x148d478: StoreField: r2->field_1b = r0
    //     0x148d478: stur            w0, [x2, #0x1b]
    // 0x148d47c: StoreField: r2->field_1f = r0
    //     0x148d47c: stur            w0, [x2, #0x1f]
    // 0x148d480: StoreField: r2->field_23 = r0
    //     0x148d480: stur            w0, [x2, #0x23]
    // 0x148d484: StoreField: r2->field_27 = r0
    //     0x148d484: stur            w0, [x2, #0x27]
    // 0x148d488: StoreField: r2->field_2b = r0
    //     0x148d488: stur            w0, [x2, #0x2b]
    // 0x148d48c: ldur            x3, [fp, #-0x20]
    // 0x148d490: LoadField: r1 = r3->field_b
    //     0x148d490: ldur            w1, [x3, #0xb]
    // 0x148d494: LoadField: r4 = r3->field_f
    //     0x148d494: ldur            w4, [x3, #0xf]
    // 0x148d498: DecompressPointer r4
    //     0x148d498: add             x4, x4, HEAP, lsl #32
    // 0x148d49c: LoadField: r5 = r4->field_b
    //     0x148d49c: ldur            w5, [x4, #0xb]
    // 0x148d4a0: r4 = LoadInt32Instr(r1)
    //     0x148d4a0: sbfx            x4, x1, #1, #0x1f
    // 0x148d4a4: stur            x4, [fp, #-0x80]
    // 0x148d4a8: r1 = LoadInt32Instr(r5)
    //     0x148d4a8: sbfx            x1, x5, #1, #0x1f
    // 0x148d4ac: cmp             x4, x1
    // 0x148d4b0: b.ne            #0x148d4bc
    // 0x148d4b4: mov             x1, x3
    // 0x148d4b8: r0 = _growToNextCapacity()
    //     0x148d4b8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x148d4bc: ldur            x2, [fp, #-0x20]
    // 0x148d4c0: ldur            x3, [fp, #-0x80]
    // 0x148d4c4: add             x0, x3, #1
    // 0x148d4c8: lsl             x1, x0, #1
    // 0x148d4cc: StoreField: r2->field_b = r1
    //     0x148d4cc: stur            w1, [x2, #0xb]
    // 0x148d4d0: LoadField: r1 = r2->field_f
    //     0x148d4d0: ldur            w1, [x2, #0xf]
    // 0x148d4d4: DecompressPointer r1
    //     0x148d4d4: add             x1, x1, HEAP, lsl #32
    // 0x148d4d8: ldur            x0, [fp, #-0x40]
    // 0x148d4dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x148d4dc: add             x25, x1, x3, lsl #2
    //     0x148d4e0: add             x25, x25, #0xf
    //     0x148d4e4: str             w0, [x25]
    //     0x148d4e8: tbz             w0, #0, #0x148d504
    //     0x148d4ec: ldurb           w16, [x1, #-1]
    //     0x148d4f0: ldurb           w17, [x0, #-1]
    //     0x148d4f4: and             x16, x17, x16, lsr #2
    //     0x148d4f8: tst             x16, HEAP, lsr #32
    //     0x148d4fc: b.eq            #0x148d504
    //     0x148d500: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148d504: b               #0x148d50c
    // 0x148d508: ldur            x2, [fp, #-0x20]
    // 0x148d50c: ldur            x0, [fp, #-0x10]
    // 0x148d510: LoadField: r1 = r0->field_f
    //     0x148d510: ldur            w1, [x0, #0xf]
    // 0x148d514: DecompressPointer r1
    //     0x148d514: add             x1, x1, HEAP, lsl #32
    // 0x148d518: r0 = controller()
    //     0x148d518: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148d51c: LoadField: r1 = r0->field_73
    //     0x148d51c: ldur            w1, [x0, #0x73]
    // 0x148d520: DecompressPointer r1
    //     0x148d520: add             x1, x1, HEAP, lsl #32
    // 0x148d524: r0 = value()
    //     0x148d524: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148d528: LoadField: r1 = r0->field_b
    //     0x148d528: ldur            w1, [x0, #0xb]
    // 0x148d52c: DecompressPointer r1
    //     0x148d52c: add             x1, x1, HEAP, lsl #32
    // 0x148d530: cmp             w1, NULL
    // 0x148d534: b.ne            #0x148d540
    // 0x148d538: r0 = Null
    //     0x148d538: mov             x0, NULL
    // 0x148d53c: b               #0x148d564
    // 0x148d540: LoadField: r0 = r1->field_f
    //     0x148d540: ldur            w0, [x1, #0xf]
    // 0x148d544: DecompressPointer r0
    //     0x148d544: add             x0, x0, HEAP, lsl #32
    // 0x148d548: cmp             w0, NULL
    // 0x148d54c: b.ne            #0x148d558
    // 0x148d550: r0 = Null
    //     0x148d550: mov             x0, NULL
    // 0x148d554: b               #0x148d564
    // 0x148d558: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148d558: ldur            w1, [x0, #0x17]
    // 0x148d55c: DecompressPointer r1
    //     0x148d55c: add             x1, x1, HEAP, lsl #32
    // 0x148d560: mov             x0, x1
    // 0x148d564: cmp             w0, NULL
    // 0x148d568: b.ne            #0x148d57c
    // 0x148d56c: r1 = <BEntities>
    //     0x148d56c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0x148d570: ldr             x1, [x1, #0x130]
    // 0x148d574: r2 = 0
    //     0x148d574: movz            x2, #0
    // 0x148d578: r0 = AllocateArray()
    //     0x148d578: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148d57c: ldur            x1, [fp, #-0x20]
    // 0x148d580: r2 = LoadClassIdInstr(r0)
    //     0x148d580: ldur            x2, [x0, #-1]
    //     0x148d584: ubfx            x2, x2, #0xc, #0x14
    // 0x148d588: str             x0, [SP]
    // 0x148d58c: mov             x0, x2
    // 0x148d590: r0 = GDT[cid_x0 + 0xc898]()
    //     0x148d590: movz            x17, #0xc898
    //     0x148d594: add             lr, x0, x17
    //     0x148d598: ldr             lr, [x21, lr, lsl #3]
    //     0x148d59c: blr             lr
    // 0x148d5a0: r3 = LoadInt32Instr(r0)
    //     0x148d5a0: sbfx            x3, x0, #1, #0x1f
    // 0x148d5a4: stur            x3, [fp, #-0x80]
    // 0x148d5a8: r1 = Function '<anonymous closure>':.
    //     0x148d5a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40dc8] AnonymousClosure: (0xb4eeb8), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0x148d5ac: ldr             x1, [x1, #0xdc8]
    // 0x148d5b0: r2 = Null
    //     0x148d5b0: mov             x2, NULL
    // 0x148d5b4: r0 = AllocateClosure()
    //     0x148d5b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148d5b8: ldur            x2, [fp, #-0x10]
    // 0x148d5bc: r1 = Function '<anonymous closure>':.
    //     0x148d5bc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40dd0] AnonymousClosure: (0x148e0dc), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x148d5c0: ldr             x1, [x1, #0xdd0]
    // 0x148d5c4: stur            x0, [fp, #-0x30]
    // 0x148d5c8: r0 = AllocateClosure()
    //     0x148d5c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148d5cc: stur            x0, [fp, #-0x40]
    // 0x148d5d0: r0 = ListView()
    //     0x148d5d0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x148d5d4: stur            x0, [fp, #-0x48]
    // 0x148d5d8: r16 = true
    //     0x148d5d8: add             x16, NULL, #0x20  ; true
    // 0x148d5dc: r30 = false
    //     0x148d5dc: add             lr, NULL, #0x30  ; false
    // 0x148d5e0: stp             lr, x16, [SP, #8]
    // 0x148d5e4: r16 = Instance_NeverScrollableScrollPhysics
    //     0x148d5e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x148d5e8: ldr             x16, [x16, #0x1c8]
    // 0x148d5ec: str             x16, [SP]
    // 0x148d5f0: mov             x1, x0
    // 0x148d5f4: ldur            x2, [fp, #-0x40]
    // 0x148d5f8: ldur            x3, [fp, #-0x80]
    // 0x148d5fc: ldur            x5, [fp, #-0x30]
    // 0x148d600: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x148d600: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x148d604: ldr             x4, [x4, #0x138]
    // 0x148d608: r0 = ListView.separated()
    //     0x148d608: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x148d60c: ldur            x0, [fp, #-0x20]
    // 0x148d610: LoadField: r1 = r0->field_b
    //     0x148d610: ldur            w1, [x0, #0xb]
    // 0x148d614: LoadField: r2 = r0->field_f
    //     0x148d614: ldur            w2, [x0, #0xf]
    // 0x148d618: DecompressPointer r2
    //     0x148d618: add             x2, x2, HEAP, lsl #32
    // 0x148d61c: LoadField: r3 = r2->field_b
    //     0x148d61c: ldur            w3, [x2, #0xb]
    // 0x148d620: r2 = LoadInt32Instr(r1)
    //     0x148d620: sbfx            x2, x1, #1, #0x1f
    // 0x148d624: stur            x2, [fp, #-0x80]
    // 0x148d628: r1 = LoadInt32Instr(r3)
    //     0x148d628: sbfx            x1, x3, #1, #0x1f
    // 0x148d62c: cmp             x2, x1
    // 0x148d630: b.ne            #0x148d63c
    // 0x148d634: mov             x1, x0
    // 0x148d638: r0 = _growToNextCapacity()
    //     0x148d638: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x148d63c: ldur            x4, [fp, #-0x10]
    // 0x148d640: ldur            x5, [fp, #-0x38]
    // 0x148d644: ldur            x2, [fp, #-0x20]
    // 0x148d648: ldur            x3, [fp, #-0x80]
    // 0x148d64c: add             x0, x3, #1
    // 0x148d650: lsl             x1, x0, #1
    // 0x148d654: StoreField: r2->field_b = r1
    //     0x148d654: stur            w1, [x2, #0xb]
    // 0x148d658: LoadField: r1 = r2->field_f
    //     0x148d658: ldur            w1, [x2, #0xf]
    // 0x148d65c: DecompressPointer r1
    //     0x148d65c: add             x1, x1, HEAP, lsl #32
    // 0x148d660: ldur            x0, [fp, #-0x48]
    // 0x148d664: ArrayStore: r1[r3] = r0  ; List_4
    //     0x148d664: add             x25, x1, x3, lsl #2
    //     0x148d668: add             x25, x25, #0xf
    //     0x148d66c: str             w0, [x25]
    //     0x148d670: tbz             w0, #0, #0x148d68c
    //     0x148d674: ldurb           w16, [x1, #-1]
    //     0x148d678: ldurb           w17, [x0, #-1]
    //     0x148d67c: and             x16, x17, x16, lsr #2
    //     0x148d680: tst             x16, HEAP, lsr #32
    //     0x148d684: b.eq            #0x148d68c
    //     0x148d688: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148d68c: r0 = Column()
    //     0x148d68c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148d690: mov             x1, x0
    // 0x148d694: r0 = Instance_Axis
    //     0x148d694: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148d698: stur            x1, [fp, #-0x30]
    // 0x148d69c: StoreField: r1->field_f = r0
    //     0x148d69c: stur            w0, [x1, #0xf]
    // 0x148d6a0: r0 = Instance_MainAxisAlignment
    //     0x148d6a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148d6a4: ldr             x0, [x0, #0xa08]
    // 0x148d6a8: StoreField: r1->field_13 = r0
    //     0x148d6a8: stur            w0, [x1, #0x13]
    // 0x148d6ac: r0 = Instance_MainAxisSize
    //     0x148d6ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148d6b0: ldr             x0, [x0, #0xa10]
    // 0x148d6b4: ArrayStore: r1[0] = r0  ; List_4
    //     0x148d6b4: stur            w0, [x1, #0x17]
    // 0x148d6b8: r2 = Instance_CrossAxisAlignment
    //     0x148d6b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148d6bc: ldr             x2, [x2, #0xa18]
    // 0x148d6c0: StoreField: r1->field_1b = r2
    //     0x148d6c0: stur            w2, [x1, #0x1b]
    // 0x148d6c4: r3 = Instance_VerticalDirection
    //     0x148d6c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148d6c8: ldr             x3, [x3, #0xa20]
    // 0x148d6cc: StoreField: r1->field_23 = r3
    //     0x148d6cc: stur            w3, [x1, #0x23]
    // 0x148d6d0: r4 = Instance_Clip
    //     0x148d6d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148d6d4: ldr             x4, [x4, #0x38]
    // 0x148d6d8: StoreField: r1->field_2b = r4
    //     0x148d6d8: stur            w4, [x1, #0x2b]
    // 0x148d6dc: StoreField: r1->field_2f = rZR
    //     0x148d6dc: stur            xzr, [x1, #0x2f]
    // 0x148d6e0: ldur            x5, [fp, #-0x20]
    // 0x148d6e4: StoreField: r1->field_b = r5
    //     0x148d6e4: stur            w5, [x1, #0xb]
    // 0x148d6e8: r0 = Container()
    //     0x148d6e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148d6ec: mov             x1, x0
    // 0x148d6f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x148d6f0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x148d6f4: r0 = Container()
    //     0x148d6f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148d6f8: r0 = Accordion()
    //     0x148d6f8: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0x148d6fc: mov             x1, x0
    // 0x148d700: ldur            x0, [fp, #-0x38]
    // 0x148d704: stur            x1, [fp, #-0x20]
    // 0x148d708: StoreField: r1->field_b = r0
    //     0x148d708: stur            w0, [x1, #0xb]
    // 0x148d70c: ldur            x0, [fp, #-0x30]
    // 0x148d710: StoreField: r1->field_13 = r0
    //     0x148d710: stur            w0, [x1, #0x13]
    // 0x148d714: r0 = false
    //     0x148d714: add             x0, NULL, #0x30  ; false
    // 0x148d718: ArrayStore: r1[0] = r0  ; List_4
    //     0x148d718: stur            w0, [x1, #0x17]
    // 0x148d71c: d0 = 25.000000
    //     0x148d71c: fmov            d0, #25.00000000
    // 0x148d720: StoreField: r1->field_1b = d0
    //     0x148d720: stur            d0, [x1, #0x1b]
    // 0x148d724: r0 = true
    //     0x148d724: add             x0, NULL, #0x20  ; true
    // 0x148d728: StoreField: r1->field_23 = r0
    //     0x148d728: stur            w0, [x1, #0x23]
    // 0x148d72c: r0 = Padding()
    //     0x148d72c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148d730: mov             x1, x0
    // 0x148d734: r0 = Instance_EdgeInsets
    //     0x148d734: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x148d738: ldr             x0, [x0, #0x668]
    // 0x148d73c: stur            x1, [fp, #-0x30]
    // 0x148d740: StoreField: r1->field_f = r0
    //     0x148d740: stur            w0, [x1, #0xf]
    // 0x148d744: ldur            x2, [fp, #-0x20]
    // 0x148d748: StoreField: r1->field_b = r2
    //     0x148d748: stur            w2, [x1, #0xb]
    // 0x148d74c: r0 = Container()
    //     0x148d74c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x148d750: stur            x0, [fp, #-0x20]
    // 0x148d754: ldur            x16, [fp, #-0x28]
    // 0x148d758: ldur            lr, [fp, #-0x30]
    // 0x148d75c: stp             lr, x16, [SP]
    // 0x148d760: mov             x1, x0
    // 0x148d764: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x148d764: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x148d768: ldr             x4, [x4, #0x88]
    // 0x148d76c: r0 = Container()
    //     0x148d76c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x148d770: r0 = Padding()
    //     0x148d770: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148d774: mov             x2, x0
    // 0x148d778: r0 = Instance_EdgeInsets
    //     0x148d778: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x148d77c: ldr             x0, [x0, #0x778]
    // 0x148d780: stur            x2, [fp, #-0x28]
    // 0x148d784: StoreField: r2->field_f = r0
    //     0x148d784: stur            w0, [x2, #0xf]
    // 0x148d788: ldur            x0, [fp, #-0x20]
    // 0x148d78c: StoreField: r2->field_b = r0
    //     0x148d78c: stur            w0, [x2, #0xb]
    // 0x148d790: r1 = Instance_Color
    //     0x148d790: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148d794: d0 = 0.100000
    //     0x148d794: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x148d798: r0 = withOpacity()
    //     0x148d798: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148d79c: stur            x0, [fp, #-0x20]
    // 0x148d7a0: r0 = Divider()
    //     0x148d7a0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x148d7a4: mov             x2, x0
    // 0x148d7a8: ldur            x0, [fp, #-0x20]
    // 0x148d7ac: stur            x2, [fp, #-0x30]
    // 0x148d7b0: StoreField: r2->field_1f = r0
    //     0x148d7b0: stur            w0, [x2, #0x1f]
    // 0x148d7b4: ldur            x0, [fp, #-0x10]
    // 0x148d7b8: LoadField: r1 = r0->field_f
    //     0x148d7b8: ldur            w1, [x0, #0xf]
    // 0x148d7bc: DecompressPointer r1
    //     0x148d7bc: add             x1, x1, HEAP, lsl #32
    // 0x148d7c0: r0 = controller()
    //     0x148d7c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148d7c4: LoadField: r1 = r0->field_87
    //     0x148d7c4: ldur            w1, [x0, #0x87]
    // 0x148d7c8: DecompressPointer r1
    //     0x148d7c8: add             x1, x1, HEAP, lsl #32
    // 0x148d7cc: r0 = value()
    //     0x148d7cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148d7d0: LoadField: r1 = r0->field_2f
    //     0x148d7d0: ldur            w1, [x0, #0x2f]
    // 0x148d7d4: DecompressPointer r1
    //     0x148d7d4: add             x1, x1, HEAP, lsl #32
    // 0x148d7d8: cmp             w1, NULL
    // 0x148d7dc: b.ne            #0x148d7e8
    // 0x148d7e0: r0 = ""
    //     0x148d7e0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148d7e4: b               #0x148d7ec
    // 0x148d7e8: mov             x0, x1
    // 0x148d7ec: ldur            x2, [fp, #-0x10]
    // 0x148d7f0: stur            x0, [fp, #-0x20]
    // 0x148d7f4: LoadField: r1 = r2->field_13
    //     0x148d7f4: ldur            w1, [x2, #0x13]
    // 0x148d7f8: DecompressPointer r1
    //     0x148d7f8: add             x1, x1, HEAP, lsl #32
    // 0x148d7fc: r0 = of()
    //     0x148d7fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148d800: LoadField: r1 = r0->field_87
    //     0x148d800: ldur            w1, [x0, #0x87]
    // 0x148d804: DecompressPointer r1
    //     0x148d804: add             x1, x1, HEAP, lsl #32
    // 0x148d808: LoadField: r0 = r1->field_7
    //     0x148d808: ldur            w0, [x1, #7]
    // 0x148d80c: DecompressPointer r0
    //     0x148d80c: add             x0, x0, HEAP, lsl #32
    // 0x148d810: r16 = Instance_Color
    //     0x148d810: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148d814: r30 = 16.000000
    //     0x148d814: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x148d818: ldr             lr, [lr, #0x188]
    // 0x148d81c: stp             lr, x16, [SP]
    // 0x148d820: mov             x1, x0
    // 0x148d824: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x148d824: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x148d828: ldr             x4, [x4, #0x9b8]
    // 0x148d82c: r0 = copyWith()
    //     0x148d82c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148d830: stur            x0, [fp, #-0x38]
    // 0x148d834: r0 = Text()
    //     0x148d834: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148d838: mov             x1, x0
    // 0x148d83c: ldur            x0, [fp, #-0x20]
    // 0x148d840: stur            x1, [fp, #-0x40]
    // 0x148d844: StoreField: r1->field_b = r0
    //     0x148d844: stur            w0, [x1, #0xb]
    // 0x148d848: ldur            x0, [fp, #-0x38]
    // 0x148d84c: StoreField: r1->field_13 = r0
    //     0x148d84c: stur            w0, [x1, #0x13]
    // 0x148d850: r0 = SvgPicture()
    //     0x148d850: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x148d854: stur            x0, [fp, #-0x20]
    // 0x148d858: r16 = "return order"
    //     0x148d858: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x148d85c: ldr             x16, [x16, #0xc78]
    // 0x148d860: r30 = Instance_BoxFit
    //     0x148d860: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x148d864: ldr             lr, [lr, #0xb18]
    // 0x148d868: stp             lr, x16, [SP]
    // 0x148d86c: mov             x1, x0
    // 0x148d870: r2 = "assets/images/secure_icon.svg"
    //     0x148d870: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0x148d874: ldr             x2, [x2, #0xc80]
    // 0x148d878: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x148d878: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x148d87c: ldr             x4, [x4, #0xb28]
    // 0x148d880: r0 = SvgPicture.asset()
    //     0x148d880: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x148d884: r1 = Null
    //     0x148d884: mov             x1, NULL
    // 0x148d888: r2 = 4
    //     0x148d888: movz            x2, #0x4
    // 0x148d88c: r0 = AllocateArray()
    //     0x148d88c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148d890: mov             x2, x0
    // 0x148d894: ldur            x0, [fp, #-0x40]
    // 0x148d898: stur            x2, [fp, #-0x38]
    // 0x148d89c: StoreField: r2->field_f = r0
    //     0x148d89c: stur            w0, [x2, #0xf]
    // 0x148d8a0: ldur            x0, [fp, #-0x20]
    // 0x148d8a4: StoreField: r2->field_13 = r0
    //     0x148d8a4: stur            w0, [x2, #0x13]
    // 0x148d8a8: r1 = <Widget>
    //     0x148d8a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148d8ac: r0 = AllocateGrowableArray()
    //     0x148d8ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148d8b0: mov             x1, x0
    // 0x148d8b4: ldur            x0, [fp, #-0x38]
    // 0x148d8b8: stur            x1, [fp, #-0x20]
    // 0x148d8bc: StoreField: r1->field_f = r0
    //     0x148d8bc: stur            w0, [x1, #0xf]
    // 0x148d8c0: r0 = 4
    //     0x148d8c0: movz            x0, #0x4
    // 0x148d8c4: StoreField: r1->field_b = r0
    //     0x148d8c4: stur            w0, [x1, #0xb]
    // 0x148d8c8: r0 = Row()
    //     0x148d8c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148d8cc: mov             x1, x0
    // 0x148d8d0: r0 = Instance_Axis
    //     0x148d8d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148d8d4: stur            x1, [fp, #-0x38]
    // 0x148d8d8: StoreField: r1->field_f = r0
    //     0x148d8d8: stur            w0, [x1, #0xf]
    // 0x148d8dc: r0 = Instance_MainAxisAlignment
    //     0x148d8dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x148d8e0: ldr             x0, [x0, #0xa8]
    // 0x148d8e4: StoreField: r1->field_13 = r0
    //     0x148d8e4: stur            w0, [x1, #0x13]
    // 0x148d8e8: r0 = Instance_MainAxisSize
    //     0x148d8e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148d8ec: ldr             x0, [x0, #0xa10]
    // 0x148d8f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x148d8f0: stur            w0, [x1, #0x17]
    // 0x148d8f4: r0 = Instance_CrossAxisAlignment
    //     0x148d8f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148d8f8: ldr             x0, [x0, #0xa18]
    // 0x148d8fc: StoreField: r1->field_1b = r0
    //     0x148d8fc: stur            w0, [x1, #0x1b]
    // 0x148d900: r0 = Instance_VerticalDirection
    //     0x148d900: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148d904: ldr             x0, [x0, #0xa20]
    // 0x148d908: StoreField: r1->field_23 = r0
    //     0x148d908: stur            w0, [x1, #0x23]
    // 0x148d90c: r0 = Instance_Clip
    //     0x148d90c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148d910: ldr             x0, [x0, #0x38]
    // 0x148d914: StoreField: r1->field_2b = r0
    //     0x148d914: stur            w0, [x1, #0x2b]
    // 0x148d918: StoreField: r1->field_2f = rZR
    //     0x148d918: stur            xzr, [x1, #0x2f]
    // 0x148d91c: ldur            x0, [fp, #-0x20]
    // 0x148d920: StoreField: r1->field_b = r0
    //     0x148d920: stur            w0, [x1, #0xb]
    // 0x148d924: r0 = Padding()
    //     0x148d924: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148d928: mov             x2, x0
    // 0x148d92c: r0 = Instance_EdgeInsets
    //     0x148d92c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x148d930: ldr             x0, [x0, #0xd0]
    // 0x148d934: stur            x2, [fp, #-0x20]
    // 0x148d938: StoreField: r2->field_f = r0
    //     0x148d938: stur            w0, [x2, #0xf]
    // 0x148d93c: ldur            x0, [fp, #-0x38]
    // 0x148d940: StoreField: r2->field_b = r0
    //     0x148d940: stur            w0, [x2, #0xb]
    // 0x148d944: ldur            x0, [fp, #-0x10]
    // 0x148d948: LoadField: r1 = r0->field_f
    //     0x148d948: ldur            w1, [x0, #0xf]
    // 0x148d94c: DecompressPointer r1
    //     0x148d94c: add             x1, x1, HEAP, lsl #32
    // 0x148d950: r0 = controller()
    //     0x148d950: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148d954: LoadField: r1 = r0->field_87
    //     0x148d954: ldur            w1, [x0, #0x87]
    // 0x148d958: DecompressPointer r1
    //     0x148d958: add             x1, x1, HEAP, lsl #32
    // 0x148d95c: r0 = value()
    //     0x148d95c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148d960: LoadField: r1 = r0->field_1f
    //     0x148d960: ldur            w1, [x0, #0x1f]
    // 0x148d964: DecompressPointer r1
    //     0x148d964: add             x1, x1, HEAP, lsl #32
    // 0x148d968: cmp             w1, NULL
    // 0x148d96c: b.ne            #0x148d984
    // 0x148d970: r1 = <PaymentOptions>
    //     0x148d970: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x148d974: ldr             x1, [x1, #0x5a8]
    // 0x148d978: r2 = 0
    //     0x148d978: movz            x2, #0
    // 0x148d97c: r0 = AllocateArray()
    //     0x148d97c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148d980: b               #0x148d988
    // 0x148d984: mov             x0, x1
    // 0x148d988: ldur            x2, [fp, #-0x10]
    // 0x148d98c: ldur            x5, [fp, #-0x18]
    // 0x148d990: ldur            x4, [fp, #-0x28]
    // 0x148d994: ldur            x3, [fp, #-0x30]
    // 0x148d998: ldur            x1, [fp, #-0x20]
    // 0x148d99c: r6 = LoadClassIdInstr(r0)
    //     0x148d99c: ldur            x6, [x0, #-1]
    //     0x148d9a0: ubfx            x6, x6, #0xc, #0x14
    // 0x148d9a4: str             x0, [SP]
    // 0x148d9a8: mov             x0, x6
    // 0x148d9ac: r0 = GDT[cid_x0 + 0xc898]()
    //     0x148d9ac: movz            x17, #0xc898
    //     0x148d9b0: add             lr, x0, x17
    //     0x148d9b4: ldr             lr, [x21, lr, lsl #3]
    //     0x148d9b8: blr             lr
    // 0x148d9bc: r3 = LoadInt32Instr(r0)
    //     0x148d9bc: sbfx            x3, x0, #1, #0x1f
    // 0x148d9c0: ldur            x2, [fp, #-0x10]
    // 0x148d9c4: stur            x3, [fp, #-0x80]
    // 0x148d9c8: r1 = Function '<anonymous closure>':.
    //     0x148d9c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40dd8] AnonymousClosure: (0x148dbb4), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x148d9cc: ldr             x1, [x1, #0xdd8]
    // 0x148d9d0: r0 = AllocateClosure()
    //     0x148d9d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148d9d4: r1 = Function '<anonymous closure>':.
    //     0x148d9d4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40de0] AnonymousClosure: (0x148ca10), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x148d9d8: ldr             x1, [x1, #0xde0]
    // 0x148d9dc: r2 = Null
    //     0x148d9dc: mov             x2, NULL
    // 0x148d9e0: stur            x0, [fp, #-0x38]
    // 0x148d9e4: r0 = AllocateClosure()
    //     0x148d9e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148d9e8: stur            x0, [fp, #-0x40]
    // 0x148d9ec: r0 = ListView()
    //     0x148d9ec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x148d9f0: stur            x0, [fp, #-0x48]
    // 0x148d9f4: r16 = true
    //     0x148d9f4: add             x16, NULL, #0x20  ; true
    // 0x148d9f8: r30 = false
    //     0x148d9f8: add             lr, NULL, #0x30  ; false
    // 0x148d9fc: stp             lr, x16, [SP]
    // 0x148da00: mov             x1, x0
    // 0x148da04: ldur            x2, [fp, #-0x38]
    // 0x148da08: ldur            x3, [fp, #-0x80]
    // 0x148da0c: ldur            x5, [fp, #-0x40]
    // 0x148da10: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x148da10: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x148da14: ldr             x4, [x4, #0xc98]
    // 0x148da18: r0 = ListView.separated()
    //     0x148da18: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x148da1c: r0 = Padding()
    //     0x148da1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148da20: mov             x2, x0
    // 0x148da24: r0 = Instance_EdgeInsets
    //     0x148da24: add             x0, PP, #0x40, lsl #12  ; [pp+0x40de8] Obj!EdgeInsets@d57351
    //     0x148da28: ldr             x0, [x0, #0xde8]
    // 0x148da2c: stur            x2, [fp, #-0x38]
    // 0x148da30: StoreField: r2->field_f = r0
    //     0x148da30: stur            w0, [x2, #0xf]
    // 0x148da34: ldur            x0, [fp, #-0x48]
    // 0x148da38: StoreField: r2->field_b = r0
    //     0x148da38: stur            w0, [x2, #0xb]
    // 0x148da3c: ldur            x0, [fp, #-0x10]
    // 0x148da40: LoadField: r1 = r0->field_f
    //     0x148da40: ldur            w1, [x0, #0xf]
    // 0x148da44: DecompressPointer r1
    //     0x148da44: add             x1, x1, HEAP, lsl #32
    // 0x148da48: r0 = controller()
    //     0x148da48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148da4c: LoadField: r1 = r0->field_57
    //     0x148da4c: ldur            w1, [x0, #0x57]
    // 0x148da50: DecompressPointer r1
    //     0x148da50: add             x1, x1, HEAP, lsl #32
    // 0x148da54: r0 = value()
    //     0x148da54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148da58: stur            x0, [fp, #-0x10]
    // 0x148da5c: r0 = PaymentTrustMarkers()
    //     0x148da5c: bl              #0xbc7fe4  ; AllocatePaymentTrustMarkersStub -> PaymentTrustMarkers (size=0x14)
    // 0x148da60: mov             x1, x0
    // 0x148da64: r0 = true
    //     0x148da64: add             x0, NULL, #0x20  ; true
    // 0x148da68: stur            x1, [fp, #-0x40]
    // 0x148da6c: StoreField: r1->field_b = r0
    //     0x148da6c: stur            w0, [x1, #0xb]
    // 0x148da70: ldur            x0, [fp, #-0x10]
    // 0x148da74: StoreField: r1->field_f = r0
    //     0x148da74: stur            w0, [x1, #0xf]
    // 0x148da78: r0 = Padding()
    //     0x148da78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148da7c: mov             x3, x0
    // 0x148da80: r0 = Instance_EdgeInsets
    //     0x148da80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x148da84: ldr             x0, [x0, #0x668]
    // 0x148da88: stur            x3, [fp, #-0x10]
    // 0x148da8c: StoreField: r3->field_f = r0
    //     0x148da8c: stur            w0, [x3, #0xf]
    // 0x148da90: ldur            x0, [fp, #-0x40]
    // 0x148da94: StoreField: r3->field_b = r0
    //     0x148da94: stur            w0, [x3, #0xb]
    // 0x148da98: r1 = Null
    //     0x148da98: mov             x1, NULL
    // 0x148da9c: r2 = 12
    //     0x148da9c: movz            x2, #0xc
    // 0x148daa0: r0 = AllocateArray()
    //     0x148daa0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148daa4: mov             x2, x0
    // 0x148daa8: ldur            x0, [fp, #-0x18]
    // 0x148daac: stur            x2, [fp, #-0x40]
    // 0x148dab0: StoreField: r2->field_f = r0
    //     0x148dab0: stur            w0, [x2, #0xf]
    // 0x148dab4: ldur            x0, [fp, #-0x28]
    // 0x148dab8: StoreField: r2->field_13 = r0
    //     0x148dab8: stur            w0, [x2, #0x13]
    // 0x148dabc: ldur            x0, [fp, #-0x30]
    // 0x148dac0: ArrayStore: r2[0] = r0  ; List_4
    //     0x148dac0: stur            w0, [x2, #0x17]
    // 0x148dac4: ldur            x0, [fp, #-0x20]
    // 0x148dac8: StoreField: r2->field_1b = r0
    //     0x148dac8: stur            w0, [x2, #0x1b]
    // 0x148dacc: ldur            x0, [fp, #-0x38]
    // 0x148dad0: StoreField: r2->field_1f = r0
    //     0x148dad0: stur            w0, [x2, #0x1f]
    // 0x148dad4: ldur            x0, [fp, #-0x10]
    // 0x148dad8: StoreField: r2->field_23 = r0
    //     0x148dad8: stur            w0, [x2, #0x23]
    // 0x148dadc: r1 = <Widget>
    //     0x148dadc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148dae0: r0 = AllocateGrowableArray()
    //     0x148dae0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148dae4: mov             x1, x0
    // 0x148dae8: ldur            x0, [fp, #-0x40]
    // 0x148daec: stur            x1, [fp, #-0x10]
    // 0x148daf0: StoreField: r1->field_f = r0
    //     0x148daf0: stur            w0, [x1, #0xf]
    // 0x148daf4: r0 = 12
    //     0x148daf4: movz            x0, #0xc
    // 0x148daf8: StoreField: r1->field_b = r0
    //     0x148daf8: stur            w0, [x1, #0xb]
    // 0x148dafc: r0 = ListView()
    //     0x148dafc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x148db00: stur            x0, [fp, #-0x18]
    // 0x148db04: r16 = Instance_EdgeInsets
    //     0x148db04: add             x16, PP, #0x40, lsl #12  ; [pp+0x409e0] Obj!EdgeInsets@d58bb1
    //     0x148db08: ldr             x16, [x16, #0x9e0]
    // 0x148db0c: r30 = true
    //     0x148db0c: add             lr, NULL, #0x20  ; true
    // 0x148db10: stp             lr, x16, [SP, #0x10]
    // 0x148db14: r16 = false
    //     0x148db14: add             x16, NULL, #0x30  ; false
    // 0x148db18: r30 = Instance_RangeMaintainingScrollPhysics
    //     0x148db18: add             lr, PP, #0x3a, lsl #12  ; [pp+0x3afa0] Obj!RangeMaintainingScrollPhysics@d55901
    //     0x148db1c: ldr             lr, [lr, #0xfa0]
    // 0x148db20: stp             lr, x16, [SP]
    // 0x148db24: mov             x1, x0
    // 0x148db28: ldur            x2, [fp, #-0x10]
    // 0x148db2c: r4 = const [0, 0x6, 0x4, 0x2, padding, 0x2, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x148db2c: add             x4, PP, #0x40, lsl #12  ; [pp+0x409e8] List(13) [0, 0x6, 0x4, 0x2, "padding", 0x2, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x148db30: ldr             x4, [x4, #0x9e8]
    // 0x148db34: r0 = ListView()
    //     0x148db34: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x148db38: r0 = WillPopScope()
    //     0x148db38: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x148db3c: mov             x3, x0
    // 0x148db40: ldur            x0, [fp, #-0x18]
    // 0x148db44: stur            x3, [fp, #-0x10]
    // 0x148db48: StoreField: r3->field_b = r0
    //     0x148db48: stur            w0, [x3, #0xb]
    // 0x148db4c: ldur            x2, [fp, #-8]
    // 0x148db50: r1 = Function 'onBackPress':.
    //     0x148db50: add             x1, PP, #0x40, lsl #12  ; [pp+0x40df0] AnonymousClosure: (0x148db7c), in [package:customer_app/app/presentation/views/basic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::onBackPress (0x13ac748)
    //     0x148db54: ldr             x1, [x1, #0xdf0]
    // 0x148db58: r0 = AllocateClosure()
    //     0x148db58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148db5c: mov             x1, x0
    // 0x148db60: ldur            x0, [fp, #-0x10]
    // 0x148db64: StoreField: r0->field_f = r1
    //     0x148db64: stur            w1, [x0, #0xf]
    // 0x148db68: LeaveFrame
    //     0x148db68: mov             SP, fp
    //     0x148db6c: ldp             fp, lr, [SP], #0x10
    // 0x148db70: ret
    //     0x148db70: ret             
    // 0x148db74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148db74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148db78: b               #0x148caac
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x148db7c, size: 0x38
    // 0x148db7c: EnterFrame
    //     0x148db7c: stp             fp, lr, [SP, #-0x10]!
    //     0x148db80: mov             fp, SP
    // 0x148db84: ldr             x0, [fp, #0x10]
    // 0x148db88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148db88: ldur            w1, [x0, #0x17]
    // 0x148db8c: DecompressPointer r1
    //     0x148db8c: add             x1, x1, HEAP, lsl #32
    // 0x148db90: CheckStackOverflow
    //     0x148db90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148db94: cmp             SP, x16
    //     0x148db98: b.ls            #0x148dbac
    // 0x148db9c: r0 = onBackPress()
    //     0x148db9c: bl              #0x13ac748  ; [package:customer_app/app/presentation/views/basic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::onBackPress
    // 0x148dba0: LeaveFrame
    //     0x148dba0: mov             SP, fp
    //     0x148dba4: ldp             fp, lr, [SP], #0x10
    // 0x148dba8: ret
    //     0x148dba8: ret             
    // 0x148dbac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148dbac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148dbb0: b               #0x148db9c
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x148dbb4, size: 0xd4
    // 0x148dbb4: EnterFrame
    //     0x148dbb4: stp             fp, lr, [SP, #-0x10]!
    //     0x148dbb8: mov             fp, SP
    // 0x148dbbc: AllocStack(0x8)
    //     0x148dbbc: sub             SP, SP, #8
    // 0x148dbc0: SetupParameters()
    //     0x148dbc0: ldr             x0, [fp, #0x20]
    //     0x148dbc4: ldur            w1, [x0, #0x17]
    //     0x148dbc8: add             x1, x1, HEAP, lsl #32
    // 0x148dbcc: CheckStackOverflow
    //     0x148dbcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148dbd0: cmp             SP, x16
    //     0x148dbd4: b.ls            #0x148dc7c
    // 0x148dbd8: LoadField: r0 = r1->field_f
    //     0x148dbd8: ldur            w0, [x1, #0xf]
    // 0x148dbdc: DecompressPointer r0
    //     0x148dbdc: add             x0, x0, HEAP, lsl #32
    // 0x148dbe0: mov             x1, x0
    // 0x148dbe4: stur            x0, [fp, #-8]
    // 0x148dbe8: r0 = controller()
    //     0x148dbe8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148dbec: LoadField: r1 = r0->field_87
    //     0x148dbec: ldur            w1, [x0, #0x87]
    // 0x148dbf0: DecompressPointer r1
    //     0x148dbf0: add             x1, x1, HEAP, lsl #32
    // 0x148dbf4: r0 = value()
    //     0x148dbf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148dbf8: LoadField: r2 = r0->field_1f
    //     0x148dbf8: ldur            w2, [x0, #0x1f]
    // 0x148dbfc: DecompressPointer r2
    //     0x148dbfc: add             x2, x2, HEAP, lsl #32
    // 0x148dc00: cmp             w2, NULL
    // 0x148dc04: b.ne            #0x148dc14
    // 0x148dc08: ldr             x3, [fp, #0x10]
    // 0x148dc0c: r2 = Null
    //     0x148dc0c: mov             x2, NULL
    // 0x148dc10: b               #0x148dc54
    // 0x148dc14: ldr             x3, [fp, #0x10]
    // 0x148dc18: LoadField: r0 = r2->field_b
    //     0x148dc18: ldur            w0, [x2, #0xb]
    // 0x148dc1c: r4 = LoadInt32Instr(r3)
    //     0x148dc1c: sbfx            x4, x3, #1, #0x1f
    //     0x148dc20: tbz             w3, #0, #0x148dc28
    //     0x148dc24: ldur            x4, [x3, #7]
    // 0x148dc28: r1 = LoadInt32Instr(r0)
    //     0x148dc28: sbfx            x1, x0, #1, #0x1f
    // 0x148dc2c: mov             x0, x1
    // 0x148dc30: mov             x1, x4
    // 0x148dc34: cmp             x1, x0
    // 0x148dc38: b.hs            #0x148dc84
    // 0x148dc3c: LoadField: r0 = r2->field_f
    //     0x148dc3c: ldur            w0, [x2, #0xf]
    // 0x148dc40: DecompressPointer r0
    //     0x148dc40: add             x0, x0, HEAP, lsl #32
    // 0x148dc44: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x148dc44: add             x16, x0, x4, lsl #2
    //     0x148dc48: ldur            w1, [x16, #0xf]
    // 0x148dc4c: DecompressPointer r1
    //     0x148dc4c: add             x1, x1, HEAP, lsl #32
    // 0x148dc50: mov             x2, x1
    // 0x148dc54: r0 = LoadInt32Instr(r3)
    //     0x148dc54: sbfx            x0, x3, #1, #0x1f
    //     0x148dc58: tbz             w3, #0, #0x148dc60
    //     0x148dc5c: ldur            x0, [x3, #7]
    // 0x148dc60: ldur            x1, [fp, #-8]
    // 0x148dc64: mov             x3, x0
    // 0x148dc68: ldr             x5, [fp, #0x18]
    // 0x148dc6c: r0 = glassThemePaymentMethodCard()
    //     0x148dc6c: bl              #0x148dc88  ; [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::glassThemePaymentMethodCard
    // 0x148dc70: LeaveFrame
    //     0x148dc70: mov             SP, fp
    //     0x148dc74: ldp             fp, lr, [SP], #0x10
    // 0x148dc78: ret
    //     0x148dc78: ret             
    // 0x148dc7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148dc7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148dc80: b               #0x148dbd8
    // 0x148dc84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x148dc84: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ glassThemePaymentMethodCard(/* No info */) {
    // ** addr: 0x148dc88, size: 0x90
    // 0x148dc88: EnterFrame
    //     0x148dc88: stp             fp, lr, [SP, #-0x10]!
    //     0x148dc8c: mov             fp, SP
    // 0x148dc90: AllocStack(0x28)
    //     0x148dc90: sub             SP, SP, #0x28
    // 0x148dc94: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x148dc94: stur            x1, [fp, #-8]
    //     0x148dc98: stur            x2, [fp, #-0x10]
    //     0x148dc9c: stur            x3, [fp, #-0x18]
    //     0x148dca0: stur            x5, [fp, #-0x20]
    // 0x148dca4: r1 = 4
    //     0x148dca4: movz            x1, #0x4
    // 0x148dca8: r0 = AllocateContext()
    //     0x148dca8: bl              #0x16f6108  ; AllocateContextStub
    // 0x148dcac: mov             x2, x0
    // 0x148dcb0: ldur            x0, [fp, #-8]
    // 0x148dcb4: stur            x2, [fp, #-0x28]
    // 0x148dcb8: StoreField: r2->field_f = r0
    //     0x148dcb8: stur            w0, [x2, #0xf]
    // 0x148dcbc: ldur            x0, [fp, #-0x10]
    // 0x148dcc0: StoreField: r2->field_13 = r0
    //     0x148dcc0: stur            w0, [x2, #0x13]
    // 0x148dcc4: ldur            x3, [fp, #-0x18]
    // 0x148dcc8: r0 = BoxInt64Instr(r3)
    //     0x148dcc8: sbfiz           x0, x3, #1, #0x1f
    //     0x148dccc: cmp             x3, x0, asr #1
    //     0x148dcd0: b.eq            #0x148dcdc
    //     0x148dcd4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x148dcd8: stur            x3, [x0, #7]
    // 0x148dcdc: ArrayStore: r2[0] = r0  ; List_4
    //     0x148dcdc: stur            w0, [x2, #0x17]
    // 0x148dce0: ldur            x0, [fp, #-0x20]
    // 0x148dce4: StoreField: r2->field_1b = r0
    //     0x148dce4: stur            w0, [x2, #0x1b]
    // 0x148dce8: r0 = Obx()
    //     0x148dce8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148dcec: ldur            x2, [fp, #-0x28]
    // 0x148dcf0: r1 = Function '<anonymous closure>':.
    //     0x148dcf0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e00] AnonymousClosure: (0x148dd18), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::glassThemePaymentMethodCard (0x148dc88)
    //     0x148dcf4: ldr             x1, [x1, #0xe00]
    // 0x148dcf8: stur            x0, [fp, #-8]
    // 0x148dcfc: r0 = AllocateClosure()
    //     0x148dcfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148dd00: mov             x1, x0
    // 0x148dd04: ldur            x0, [fp, #-8]
    // 0x148dd08: StoreField: r0->field_b = r1
    //     0x148dd08: stur            w1, [x0, #0xb]
    // 0x148dd0c: LeaveFrame
    //     0x148dd0c: mov             SP, fp
    //     0x148dd10: ldp             fp, lr, [SP], #0x10
    // 0x148dd14: ret
    //     0x148dd14: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0x148dd18, size: 0x3c4
    // 0x148dd18: EnterFrame
    //     0x148dd18: stp             fp, lr, [SP, #-0x10]!
    //     0x148dd1c: mov             fp, SP
    // 0x148dd20: AllocStack(0x40)
    //     0x148dd20: sub             SP, SP, #0x40
    // 0x148dd24: SetupParameters()
    //     0x148dd24: ldr             x0, [fp, #0x10]
    //     0x148dd28: ldur            w2, [x0, #0x17]
    //     0x148dd2c: add             x2, x2, HEAP, lsl #32
    //     0x148dd30: stur            x2, [fp, #-8]
    // 0x148dd34: CheckStackOverflow
    //     0x148dd34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148dd38: cmp             SP, x16
    //     0x148dd3c: b.ls            #0x148e0d4
    // 0x148dd40: LoadField: r1 = r2->field_f
    //     0x148dd40: ldur            w1, [x2, #0xf]
    // 0x148dd44: DecompressPointer r1
    //     0x148dd44: add             x1, x1, HEAP, lsl #32
    // 0x148dd48: r0 = controller()
    //     0x148dd48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148dd4c: LoadField: r1 = r0->field_7f
    //     0x148dd4c: ldur            w1, [x0, #0x7f]
    // 0x148dd50: DecompressPointer r1
    //     0x148dd50: add             x1, x1, HEAP, lsl #32
    // 0x148dd54: r0 = value()
    //     0x148dd54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148dd58: ldur            x2, [fp, #-8]
    // 0x148dd5c: LoadField: r1 = r2->field_13
    //     0x148dd5c: ldur            w1, [x2, #0x13]
    // 0x148dd60: DecompressPointer r1
    //     0x148dd60: add             x1, x1, HEAP, lsl #32
    // 0x148dd64: cmp             w1, NULL
    // 0x148dd68: b.ne            #0x148dd74
    // 0x148dd6c: r1 = Null
    //     0x148dd6c: mov             x1, NULL
    // 0x148dd70: b               #0x148dd80
    // 0x148dd74: LoadField: r3 = r1->field_f
    //     0x148dd74: ldur            w3, [x1, #0xf]
    // 0x148dd78: DecompressPointer r3
    //     0x148dd78: add             x3, x3, HEAP, lsl #32
    // 0x148dd7c: mov             x1, x3
    // 0x148dd80: r3 = 60
    //     0x148dd80: movz            x3, #0x3c
    // 0x148dd84: branchIfSmi(r0, 0x148dd90)
    //     0x148dd84: tbz             w0, #0, #0x148dd90
    // 0x148dd88: r3 = LoadClassIdInstr(r0)
    //     0x148dd88: ldur            x3, [x0, #-1]
    //     0x148dd8c: ubfx            x3, x3, #0xc, #0x14
    // 0x148dd90: stp             x1, x0, [SP]
    // 0x148dd94: mov             x0, x3
    // 0x148dd98: mov             lr, x0
    // 0x148dd9c: ldr             lr, [x21, lr, lsl #3]
    // 0x148dda0: blr             lr
    // 0x148dda4: tbnz            w0, #4, #0x148ddfc
    // 0x148dda8: ldur            x2, [fp, #-8]
    // 0x148ddac: LoadField: r1 = r2->field_f
    //     0x148ddac: ldur            w1, [x2, #0xf]
    // 0x148ddb0: DecompressPointer r1
    //     0x148ddb0: add             x1, x1, HEAP, lsl #32
    // 0x148ddb4: r0 = controller()
    //     0x148ddb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148ddb8: LoadField: r1 = r0->field_a3
    //     0x148ddb8: ldur            w1, [x0, #0xa3]
    // 0x148ddbc: DecompressPointer r1
    //     0x148ddbc: add             x1, x1, HEAP, lsl #32
    // 0x148ddc0: r0 = value()
    //     0x148ddc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148ddc4: ldur            x2, [fp, #-8]
    // 0x148ddc8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x148ddc8: ldur            w1, [x2, #0x17]
    // 0x148ddcc: DecompressPointer r1
    //     0x148ddcc: add             x1, x1, HEAP, lsl #32
    // 0x148ddd0: r3 = LoadInt32Instr(r0)
    //     0x148ddd0: sbfx            x3, x0, #1, #0x1f
    //     0x148ddd4: tbz             w0, #0, #0x148dddc
    //     0x148ddd8: ldur            x3, [x0, #7]
    // 0x148dddc: r0 = LoadInt32Instr(r1)
    //     0x148dddc: sbfx            x0, x1, #1, #0x1f
    //     0x148dde0: tbz             w1, #0, #0x148dde8
    //     0x148dde4: ldur            x0, [x1, #7]
    // 0x148dde8: cmp             x3, x0
    // 0x148ddec: b.ne            #0x148de00
    // 0x148ddf0: r0 = Instance_IconData
    //     0x148ddf0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x148ddf4: ldr             x0, [x0, #0x30]
    // 0x148ddf8: b               #0x148de08
    // 0x148ddfc: ldur            x2, [fp, #-8]
    // 0x148de00: r0 = Instance_IconData
    //     0x148de00: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x148de04: ldr             x0, [x0, #0x38]
    // 0x148de08: stur            x0, [fp, #-0x10]
    // 0x148de0c: LoadField: r1 = r2->field_1b
    //     0x148de0c: ldur            w1, [x2, #0x1b]
    // 0x148de10: DecompressPointer r1
    //     0x148de10: add             x1, x1, HEAP, lsl #32
    // 0x148de14: r0 = of()
    //     0x148de14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148de18: LoadField: r1 = r0->field_5b
    //     0x148de18: ldur            w1, [x0, #0x5b]
    // 0x148de1c: DecompressPointer r1
    //     0x148de1c: add             x1, x1, HEAP, lsl #32
    // 0x148de20: stur            x1, [fp, #-0x18]
    // 0x148de24: r0 = Icon()
    //     0x148de24: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x148de28: mov             x1, x0
    // 0x148de2c: ldur            x0, [fp, #-0x10]
    // 0x148de30: stur            x1, [fp, #-0x20]
    // 0x148de34: StoreField: r1->field_b = r0
    //     0x148de34: stur            w0, [x1, #0xb]
    // 0x148de38: ldur            x0, [fp, #-0x18]
    // 0x148de3c: StoreField: r1->field_23 = r0
    //     0x148de3c: stur            w0, [x1, #0x23]
    // 0x148de40: ldur            x2, [fp, #-8]
    // 0x148de44: LoadField: r0 = r2->field_13
    //     0x148de44: ldur            w0, [x2, #0x13]
    // 0x148de48: DecompressPointer r0
    //     0x148de48: add             x0, x0, HEAP, lsl #32
    // 0x148de4c: cmp             w0, NULL
    // 0x148de50: b.ne            #0x148de5c
    // 0x148de54: r0 = Null
    //     0x148de54: mov             x0, NULL
    // 0x148de58: b               #0x148de68
    // 0x148de5c: LoadField: r3 = r0->field_7
    //     0x148de5c: ldur            w3, [x0, #7]
    // 0x148de60: DecompressPointer r3
    //     0x148de60: add             x3, x3, HEAP, lsl #32
    // 0x148de64: mov             x0, x3
    // 0x148de68: cmp             w0, NULL
    // 0x148de6c: b.ne            #0x148de74
    // 0x148de70: r0 = ""
    //     0x148de70: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148de74: stur            x0, [fp, #-0x10]
    // 0x148de78: r0 = CachedNetworkImage()
    //     0x148de78: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x148de7c: stur            x0, [fp, #-0x18]
    // 0x148de80: r16 = 48.000000
    //     0x148de80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x148de84: ldr             x16, [x16, #0xad8]
    // 0x148de88: r30 = 48.000000
    //     0x148de88: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x148de8c: ldr             lr, [lr, #0xad8]
    // 0x148de90: stp             lr, x16, [SP]
    // 0x148de94: mov             x1, x0
    // 0x148de98: ldur            x2, [fp, #-0x10]
    // 0x148de9c: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x148de9c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x148dea0: ldr             x4, [x4, #0x900]
    // 0x148dea4: r0 = CachedNetworkImage()
    //     0x148dea4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x148dea8: r0 = Padding()
    //     0x148dea8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148deac: mov             x2, x0
    // 0x148deb0: r0 = Instance_EdgeInsets
    //     0x148deb0: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x148deb4: ldr             x0, [x0, #0x9f8]
    // 0x148deb8: stur            x2, [fp, #-0x28]
    // 0x148debc: StoreField: r2->field_f = r0
    //     0x148debc: stur            w0, [x2, #0xf]
    // 0x148dec0: ldur            x1, [fp, #-0x18]
    // 0x148dec4: StoreField: r2->field_b = r1
    //     0x148dec4: stur            w1, [x2, #0xb]
    // 0x148dec8: ldur            x3, [fp, #-8]
    // 0x148decc: LoadField: r1 = r3->field_13
    //     0x148decc: ldur            w1, [x3, #0x13]
    // 0x148ded0: DecompressPointer r1
    //     0x148ded0: add             x1, x1, HEAP, lsl #32
    // 0x148ded4: cmp             w1, NULL
    // 0x148ded8: b.ne            #0x148dee4
    // 0x148dedc: r1 = Null
    //     0x148dedc: mov             x1, NULL
    // 0x148dee0: b               #0x148def0
    // 0x148dee4: LoadField: r4 = r1->field_b
    //     0x148dee4: ldur            w4, [x1, #0xb]
    // 0x148dee8: DecompressPointer r4
    //     0x148dee8: add             x4, x4, HEAP, lsl #32
    // 0x148deec: mov             x1, x4
    // 0x148def0: cmp             w1, NULL
    // 0x148def4: b.ne            #0x148df00
    // 0x148def8: r5 = ""
    //     0x148def8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148defc: b               #0x148df04
    // 0x148df00: mov             x5, x1
    // 0x148df04: ldur            x4, [fp, #-0x20]
    // 0x148df08: stur            x5, [fp, #-0x10]
    // 0x148df0c: LoadField: r1 = r3->field_1b
    //     0x148df0c: ldur            w1, [x3, #0x1b]
    // 0x148df10: DecompressPointer r1
    //     0x148df10: add             x1, x1, HEAP, lsl #32
    // 0x148df14: r0 = of()
    //     0x148df14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148df18: LoadField: r1 = r0->field_87
    //     0x148df18: ldur            w1, [x0, #0x87]
    // 0x148df1c: DecompressPointer r1
    //     0x148df1c: add             x1, x1, HEAP, lsl #32
    // 0x148df20: LoadField: r0 = r1->field_2b
    //     0x148df20: ldur            w0, [x1, #0x2b]
    // 0x148df24: DecompressPointer r0
    //     0x148df24: add             x0, x0, HEAP, lsl #32
    // 0x148df28: r16 = 14.000000
    //     0x148df28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x148df2c: ldr             x16, [x16, #0x1d8]
    // 0x148df30: r30 = Instance_Color
    //     0x148df30: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148df34: stp             lr, x16, [SP]
    // 0x148df38: mov             x1, x0
    // 0x148df3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148df3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148df40: ldr             x4, [x4, #0xaa0]
    // 0x148df44: r0 = copyWith()
    //     0x148df44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148df48: stur            x0, [fp, #-0x18]
    // 0x148df4c: r0 = Text()
    //     0x148df4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148df50: mov             x1, x0
    // 0x148df54: ldur            x0, [fp, #-0x10]
    // 0x148df58: stur            x1, [fp, #-0x30]
    // 0x148df5c: StoreField: r1->field_b = r0
    //     0x148df5c: stur            w0, [x1, #0xb]
    // 0x148df60: ldur            x0, [fp, #-0x18]
    // 0x148df64: StoreField: r1->field_13 = r0
    //     0x148df64: stur            w0, [x1, #0x13]
    // 0x148df68: r0 = Padding()
    //     0x148df68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148df6c: mov             x3, x0
    // 0x148df70: r0 = Instance_EdgeInsets
    //     0x148df70: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x148df74: ldr             x0, [x0, #0x9f8]
    // 0x148df78: stur            x3, [fp, #-0x10]
    // 0x148df7c: StoreField: r3->field_f = r0
    //     0x148df7c: stur            w0, [x3, #0xf]
    // 0x148df80: ldur            x0, [fp, #-0x30]
    // 0x148df84: StoreField: r3->field_b = r0
    //     0x148df84: stur            w0, [x3, #0xb]
    // 0x148df88: r1 = Null
    //     0x148df88: mov             x1, NULL
    // 0x148df8c: r2 = 6
    //     0x148df8c: movz            x2, #0x6
    // 0x148df90: r0 = AllocateArray()
    //     0x148df90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148df94: mov             x2, x0
    // 0x148df98: ldur            x0, [fp, #-0x20]
    // 0x148df9c: stur            x2, [fp, #-0x18]
    // 0x148dfa0: StoreField: r2->field_f = r0
    //     0x148dfa0: stur            w0, [x2, #0xf]
    // 0x148dfa4: ldur            x0, [fp, #-0x28]
    // 0x148dfa8: StoreField: r2->field_13 = r0
    //     0x148dfa8: stur            w0, [x2, #0x13]
    // 0x148dfac: ldur            x0, [fp, #-0x10]
    // 0x148dfb0: ArrayStore: r2[0] = r0  ; List_4
    //     0x148dfb0: stur            w0, [x2, #0x17]
    // 0x148dfb4: r1 = <Widget>
    //     0x148dfb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148dfb8: r0 = AllocateGrowableArray()
    //     0x148dfb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148dfbc: mov             x1, x0
    // 0x148dfc0: ldur            x0, [fp, #-0x18]
    // 0x148dfc4: stur            x1, [fp, #-0x10]
    // 0x148dfc8: StoreField: r1->field_f = r0
    //     0x148dfc8: stur            w0, [x1, #0xf]
    // 0x148dfcc: r0 = 6
    //     0x148dfcc: movz            x0, #0x6
    // 0x148dfd0: StoreField: r1->field_b = r0
    //     0x148dfd0: stur            w0, [x1, #0xb]
    // 0x148dfd4: r0 = Row()
    //     0x148dfd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148dfd8: mov             x1, x0
    // 0x148dfdc: r0 = Instance_Axis
    //     0x148dfdc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148dfe0: stur            x1, [fp, #-0x18]
    // 0x148dfe4: StoreField: r1->field_f = r0
    //     0x148dfe4: stur            w0, [x1, #0xf]
    // 0x148dfe8: r0 = Instance_MainAxisAlignment
    //     0x148dfe8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148dfec: ldr             x0, [x0, #0xa08]
    // 0x148dff0: StoreField: r1->field_13 = r0
    //     0x148dff0: stur            w0, [x1, #0x13]
    // 0x148dff4: r0 = Instance_MainAxisSize
    //     0x148dff4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148dff8: ldr             x0, [x0, #0xa10]
    // 0x148dffc: ArrayStore: r1[0] = r0  ; List_4
    //     0x148dffc: stur            w0, [x1, #0x17]
    // 0x148e000: r0 = Instance_CrossAxisAlignment
    //     0x148e000: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148e004: ldr             x0, [x0, #0xa18]
    // 0x148e008: StoreField: r1->field_1b = r0
    //     0x148e008: stur            w0, [x1, #0x1b]
    // 0x148e00c: r0 = Instance_VerticalDirection
    //     0x148e00c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148e010: ldr             x0, [x0, #0xa20]
    // 0x148e014: StoreField: r1->field_23 = r0
    //     0x148e014: stur            w0, [x1, #0x23]
    // 0x148e018: r0 = Instance_Clip
    //     0x148e018: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148e01c: ldr             x0, [x0, #0x38]
    // 0x148e020: StoreField: r1->field_2b = r0
    //     0x148e020: stur            w0, [x1, #0x2b]
    // 0x148e024: StoreField: r1->field_2f = rZR
    //     0x148e024: stur            xzr, [x1, #0x2f]
    // 0x148e028: ldur            x0, [fp, #-0x10]
    // 0x148e02c: StoreField: r1->field_b = r0
    //     0x148e02c: stur            w0, [x1, #0xb]
    // 0x148e030: r0 = Center()
    //     0x148e030: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x148e034: mov             x1, x0
    // 0x148e038: r0 = Instance_Alignment
    //     0x148e038: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x148e03c: ldr             x0, [x0, #0xb10]
    // 0x148e040: stur            x1, [fp, #-0x10]
    // 0x148e044: StoreField: r1->field_f = r0
    //     0x148e044: stur            w0, [x1, #0xf]
    // 0x148e048: ldur            x0, [fp, #-0x18]
    // 0x148e04c: StoreField: r1->field_b = r0
    //     0x148e04c: stur            w0, [x1, #0xb]
    // 0x148e050: r0 = Padding()
    //     0x148e050: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148e054: mov             x1, x0
    // 0x148e058: r0 = Instance_EdgeInsets
    //     0x148e058: add             x0, PP, #0x40, lsl #12  ; [pp+0x40800] Obj!EdgeInsets@d59b11
    //     0x148e05c: ldr             x0, [x0, #0x800]
    // 0x148e060: stur            x1, [fp, #-0x18]
    // 0x148e064: StoreField: r1->field_f = r0
    //     0x148e064: stur            w0, [x1, #0xf]
    // 0x148e068: ldur            x0, [fp, #-0x10]
    // 0x148e06c: StoreField: r1->field_b = r0
    //     0x148e06c: stur            w0, [x1, #0xb]
    // 0x148e070: r0 = InkWell()
    //     0x148e070: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x148e074: mov             x3, x0
    // 0x148e078: ldur            x0, [fp, #-0x18]
    // 0x148e07c: stur            x3, [fp, #-0x10]
    // 0x148e080: StoreField: r3->field_b = r0
    //     0x148e080: stur            w0, [x3, #0xb]
    // 0x148e084: ldur            x2, [fp, #-8]
    // 0x148e088: r1 = Function '<anonymous closure>':.
    //     0x148e088: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e08] AnonymousClosure: (0x13ace4c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::lineThemePaymentMethodCard (0x13ad294)
    //     0x148e08c: ldr             x1, [x1, #0xe08]
    // 0x148e090: r0 = AllocateClosure()
    //     0x148e090: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148e094: mov             x1, x0
    // 0x148e098: ldur            x0, [fp, #-0x10]
    // 0x148e09c: StoreField: r0->field_f = r1
    //     0x148e09c: stur            w1, [x0, #0xf]
    // 0x148e0a0: r1 = true
    //     0x148e0a0: add             x1, NULL, #0x20  ; true
    // 0x148e0a4: StoreField: r0->field_43 = r1
    //     0x148e0a4: stur            w1, [x0, #0x43]
    // 0x148e0a8: r2 = Instance_BoxShape
    //     0x148e0a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x148e0ac: ldr             x2, [x2, #0x80]
    // 0x148e0b0: StoreField: r0->field_47 = r2
    //     0x148e0b0: stur            w2, [x0, #0x47]
    // 0x148e0b4: StoreField: r0->field_6f = r1
    //     0x148e0b4: stur            w1, [x0, #0x6f]
    // 0x148e0b8: r2 = false
    //     0x148e0b8: add             x2, NULL, #0x30  ; false
    // 0x148e0bc: StoreField: r0->field_73 = r2
    //     0x148e0bc: stur            w2, [x0, #0x73]
    // 0x148e0c0: StoreField: r0->field_83 = r1
    //     0x148e0c0: stur            w1, [x0, #0x83]
    // 0x148e0c4: StoreField: r0->field_7b = r2
    //     0x148e0c4: stur            w2, [x0, #0x7b]
    // 0x148e0c8: LeaveFrame
    //     0x148e0c8: mov             SP, fp
    //     0x148e0cc: ldp             fp, lr, [SP], #0x10
    // 0x148e0d0: ret
    //     0x148e0d0: ret             
    // 0x148e0d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148e0d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148e0d8: b               #0x148dd40
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x148e0dc, size: 0x74
    // 0x148e0dc: EnterFrame
    //     0x148e0dc: stp             fp, lr, [SP, #-0x10]!
    //     0x148e0e0: mov             fp, SP
    // 0x148e0e4: AllocStack(0x10)
    //     0x148e0e4: sub             SP, SP, #0x10
    // 0x148e0e8: SetupParameters()
    //     0x148e0e8: ldr             x0, [fp, #0x20]
    //     0x148e0ec: ldur            w1, [x0, #0x17]
    //     0x148e0f0: add             x1, x1, HEAP, lsl #32
    //     0x148e0f4: stur            x1, [fp, #-8]
    // 0x148e0f8: r1 = 2
    //     0x148e0f8: movz            x1, #0x2
    // 0x148e0fc: r0 = AllocateContext()
    //     0x148e0fc: bl              #0x16f6108  ; AllocateContextStub
    // 0x148e100: mov             x1, x0
    // 0x148e104: ldur            x0, [fp, #-8]
    // 0x148e108: stur            x1, [fp, #-0x10]
    // 0x148e10c: StoreField: r1->field_b = r0
    //     0x148e10c: stur            w0, [x1, #0xb]
    // 0x148e110: ldr             x0, [fp, #0x18]
    // 0x148e114: StoreField: r1->field_f = r0
    //     0x148e114: stur            w0, [x1, #0xf]
    // 0x148e118: ldr             x0, [fp, #0x10]
    // 0x148e11c: StoreField: r1->field_13 = r0
    //     0x148e11c: stur            w0, [x1, #0x13]
    // 0x148e120: r0 = Obx()
    //     0x148e120: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x148e124: ldur            x2, [fp, #-0x10]
    // 0x148e128: r1 = Function '<anonymous closure>':.
    //     0x148e128: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e10] AnonymousClosure: (0x148e150), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x148e12c: ldr             x1, [x1, #0xe10]
    // 0x148e130: stur            x0, [fp, #-8]
    // 0x148e134: r0 = AllocateClosure()
    //     0x148e134: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148e138: mov             x1, x0
    // 0x148e13c: ldur            x0, [fp, #-8]
    // 0x148e140: StoreField: r0->field_b = r1
    //     0x148e140: stur            w1, [x0, #0xb]
    // 0x148e144: LeaveFrame
    //     0x148e144: mov             SP, fp
    //     0x148e148: ldp             fp, lr, [SP], #0x10
    // 0x148e14c: ret
    //     0x148e14c: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x148e150, size: 0x134
    // 0x148e150: EnterFrame
    //     0x148e150: stp             fp, lr, [SP, #-0x10]!
    //     0x148e154: mov             fp, SP
    // 0x148e158: AllocStack(0x10)
    //     0x148e158: sub             SP, SP, #0x10
    // 0x148e15c: SetupParameters()
    //     0x148e15c: ldr             x0, [fp, #0x10]
    //     0x148e160: ldur            w2, [x0, #0x17]
    //     0x148e164: add             x2, x2, HEAP, lsl #32
    //     0x148e168: stur            x2, [fp, #-0x10]
    // 0x148e16c: CheckStackOverflow
    //     0x148e16c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148e170: cmp             SP, x16
    //     0x148e174: b.ls            #0x148e278
    // 0x148e178: LoadField: r0 = r2->field_b
    //     0x148e178: ldur            w0, [x2, #0xb]
    // 0x148e17c: DecompressPointer r0
    //     0x148e17c: add             x0, x0, HEAP, lsl #32
    // 0x148e180: stur            x0, [fp, #-8]
    // 0x148e184: LoadField: r1 = r0->field_f
    //     0x148e184: ldur            w1, [x0, #0xf]
    // 0x148e188: DecompressPointer r1
    //     0x148e188: add             x1, x1, HEAP, lsl #32
    // 0x148e18c: r0 = controller()
    //     0x148e18c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148e190: LoadField: r1 = r0->field_73
    //     0x148e190: ldur            w1, [x0, #0x73]
    // 0x148e194: DecompressPointer r1
    //     0x148e194: add             x1, x1, HEAP, lsl #32
    // 0x148e198: r0 = value()
    //     0x148e198: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148e19c: LoadField: r1 = r0->field_b
    //     0x148e19c: ldur            w1, [x0, #0xb]
    // 0x148e1a0: DecompressPointer r1
    //     0x148e1a0: add             x1, x1, HEAP, lsl #32
    // 0x148e1a4: cmp             w1, NULL
    // 0x148e1a8: b.ne            #0x148e1b8
    // 0x148e1ac: ldur            x3, [fp, #-0x10]
    // 0x148e1b0: r0 = Null
    //     0x148e1b0: mov             x0, NULL
    // 0x148e1b4: b               #0x148e234
    // 0x148e1b8: LoadField: r0 = r1->field_f
    //     0x148e1b8: ldur            w0, [x1, #0xf]
    // 0x148e1bc: DecompressPointer r0
    //     0x148e1bc: add             x0, x0, HEAP, lsl #32
    // 0x148e1c0: cmp             w0, NULL
    // 0x148e1c4: b.ne            #0x148e1d4
    // 0x148e1c8: ldur            x3, [fp, #-0x10]
    // 0x148e1cc: r0 = Null
    //     0x148e1cc: mov             x0, NULL
    // 0x148e1d0: b               #0x148e234
    // 0x148e1d4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x148e1d4: ldur            w2, [x0, #0x17]
    // 0x148e1d8: DecompressPointer r2
    //     0x148e1d8: add             x2, x2, HEAP, lsl #32
    // 0x148e1dc: cmp             w2, NULL
    // 0x148e1e0: b.ne            #0x148e1f0
    // 0x148e1e4: ldur            x3, [fp, #-0x10]
    // 0x148e1e8: r0 = Null
    //     0x148e1e8: mov             x0, NULL
    // 0x148e1ec: b               #0x148e234
    // 0x148e1f0: ldur            x3, [fp, #-0x10]
    // 0x148e1f4: LoadField: r0 = r3->field_13
    //     0x148e1f4: ldur            w0, [x3, #0x13]
    // 0x148e1f8: DecompressPointer r0
    //     0x148e1f8: add             x0, x0, HEAP, lsl #32
    // 0x148e1fc: LoadField: r1 = r2->field_b
    //     0x148e1fc: ldur            w1, [x2, #0xb]
    // 0x148e200: r4 = LoadInt32Instr(r0)
    //     0x148e200: sbfx            x4, x0, #1, #0x1f
    //     0x148e204: tbz             w0, #0, #0x148e20c
    //     0x148e208: ldur            x4, [x0, #7]
    // 0x148e20c: r0 = LoadInt32Instr(r1)
    //     0x148e20c: sbfx            x0, x1, #1, #0x1f
    // 0x148e210: mov             x1, x4
    // 0x148e214: cmp             x1, x0
    // 0x148e218: b.hs            #0x148e280
    // 0x148e21c: LoadField: r0 = r2->field_f
    //     0x148e21c: ldur            w0, [x2, #0xf]
    // 0x148e220: DecompressPointer r0
    //     0x148e220: add             x0, x0, HEAP, lsl #32
    // 0x148e224: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x148e224: add             x16, x0, x4, lsl #2
    //     0x148e228: ldur            w1, [x16, #0xf]
    // 0x148e22c: DecompressPointer r1
    //     0x148e22c: add             x1, x1, HEAP, lsl #32
    // 0x148e230: mov             x0, x1
    // 0x148e234: cmp             w0, NULL
    // 0x148e238: b.ne            #0x148e248
    // 0x148e23c: r0 = BEntities()
    //     0x148e23c: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0x148e240: mov             x2, x0
    // 0x148e244: b               #0x148e24c
    // 0x148e248: mov             x2, x0
    // 0x148e24c: ldur            x0, [fp, #-0x10]
    // 0x148e250: ldur            x1, [fp, #-8]
    // 0x148e254: LoadField: r3 = r0->field_f
    //     0x148e254: ldur            w3, [x0, #0xf]
    // 0x148e258: DecompressPointer r3
    //     0x148e258: add             x3, x3, HEAP, lsl #32
    // 0x148e25c: LoadField: r0 = r1->field_f
    //     0x148e25c: ldur            w0, [x1, #0xf]
    // 0x148e260: DecompressPointer r0
    //     0x148e260: add             x0, x0, HEAP, lsl #32
    // 0x148e264: mov             x1, x0
    // 0x148e268: r0 = glassThemeBagItem()
    //     0x148e268: bl              #0x148e284  ; [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::glassThemeBagItem
    // 0x148e26c: LeaveFrame
    //     0x148e26c: mov             SP, fp
    //     0x148e270: ldp             fp, lr, [SP], #0x10
    // 0x148e274: ret
    //     0x148e274: ret             
    // 0x148e278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148e278: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148e27c: b               #0x148e178
    // 0x148e280: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x148e280: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ glassThemeBagItem(/* No info */) {
    // ** addr: 0x148e284, size: 0x888
    // 0x148e284: EnterFrame
    //     0x148e284: stp             fp, lr, [SP, #-0x10]!
    //     0x148e288: mov             fp, SP
    // 0x148e28c: AllocStack(0x70)
    //     0x148e28c: sub             SP, SP, #0x70
    // 0x148e290: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x148e290: mov             x0, x1
    //     0x148e294: stur            x1, [fp, #-8]
    //     0x148e298: mov             x1, x3
    //     0x148e29c: stur            x2, [fp, #-0x10]
    //     0x148e2a0: stur            x3, [fp, #-0x18]
    // 0x148e2a4: CheckStackOverflow
    //     0x148e2a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148e2a8: cmp             SP, x16
    //     0x148e2ac: b.ls            #0x148eb04
    // 0x148e2b0: r0 = Radius()
    //     0x148e2b0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x148e2b4: d0 = 12.000000
    //     0x148e2b4: fmov            d0, #12.00000000
    // 0x148e2b8: stur            x0, [fp, #-0x20]
    // 0x148e2bc: StoreField: r0->field_7 = d0
    //     0x148e2bc: stur            d0, [x0, #7]
    // 0x148e2c0: StoreField: r0->field_f = d0
    //     0x148e2c0: stur            d0, [x0, #0xf]
    // 0x148e2c4: r0 = BorderRadius()
    //     0x148e2c4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x148e2c8: mov             x1, x0
    // 0x148e2cc: ldur            x0, [fp, #-0x20]
    // 0x148e2d0: stur            x1, [fp, #-0x28]
    // 0x148e2d4: StoreField: r1->field_7 = r0
    //     0x148e2d4: stur            w0, [x1, #7]
    // 0x148e2d8: StoreField: r1->field_b = r0
    //     0x148e2d8: stur            w0, [x1, #0xb]
    // 0x148e2dc: StoreField: r1->field_f = r0
    //     0x148e2dc: stur            w0, [x1, #0xf]
    // 0x148e2e0: StoreField: r1->field_13 = r0
    //     0x148e2e0: stur            w0, [x1, #0x13]
    // 0x148e2e4: r0 = RoundedRectangleBorder()
    //     0x148e2e4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x148e2e8: mov             x1, x0
    // 0x148e2ec: ldur            x0, [fp, #-0x28]
    // 0x148e2f0: stur            x1, [fp, #-0x20]
    // 0x148e2f4: StoreField: r1->field_b = r0
    //     0x148e2f4: stur            w0, [x1, #0xb]
    // 0x148e2f8: r0 = Instance_BorderSide
    //     0x148e2f8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x148e2fc: ldr             x0, [x0, #0xe20]
    // 0x148e300: StoreField: r1->field_7 = r0
    //     0x148e300: stur            w0, [x1, #7]
    // 0x148e304: r0 = Radius()
    //     0x148e304: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x148e308: d0 = 12.000000
    //     0x148e308: fmov            d0, #12.00000000
    // 0x148e30c: stur            x0, [fp, #-0x28]
    // 0x148e310: StoreField: r0->field_7 = d0
    //     0x148e310: stur            d0, [x0, #7]
    // 0x148e314: StoreField: r0->field_f = d0
    //     0x148e314: stur            d0, [x0, #0xf]
    // 0x148e318: r0 = BorderRadius()
    //     0x148e318: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x148e31c: mov             x3, x0
    // 0x148e320: ldur            x0, [fp, #-0x28]
    // 0x148e324: stur            x3, [fp, #-0x30]
    // 0x148e328: StoreField: r3->field_7 = r0
    //     0x148e328: stur            w0, [x3, #7]
    // 0x148e32c: StoreField: r3->field_b = r0
    //     0x148e32c: stur            w0, [x3, #0xb]
    // 0x148e330: StoreField: r3->field_f = r0
    //     0x148e330: stur            w0, [x3, #0xf]
    // 0x148e334: StoreField: r3->field_13 = r0
    //     0x148e334: stur            w0, [x3, #0x13]
    // 0x148e338: ldur            x0, [fp, #-0x10]
    // 0x148e33c: LoadField: r1 = r0->field_13
    //     0x148e33c: ldur            w1, [x0, #0x13]
    // 0x148e340: DecompressPointer r1
    //     0x148e340: add             x1, x1, HEAP, lsl #32
    // 0x148e344: cmp             w1, NULL
    // 0x148e348: b.ne            #0x148e354
    // 0x148e34c: r4 = ""
    //     0x148e34c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148e350: b               #0x148e358
    // 0x148e354: mov             x4, x1
    // 0x148e358: stur            x4, [fp, #-0x28]
    // 0x148e35c: r1 = Function '<anonymous closure>':.
    //     0x148e35c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e18] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148e360: ldr             x1, [x1, #0xe18]
    // 0x148e364: r2 = Null
    //     0x148e364: mov             x2, NULL
    // 0x148e368: r0 = AllocateClosure()
    //     0x148e368: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148e36c: r1 = Function '<anonymous closure>':.
    //     0x148e36c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e20] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x148e370: ldr             x1, [x1, #0xe20]
    // 0x148e374: r2 = Null
    //     0x148e374: mov             x2, NULL
    // 0x148e378: stur            x0, [fp, #-0x38]
    // 0x148e37c: r0 = AllocateClosure()
    //     0x148e37c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148e380: stur            x0, [fp, #-0x40]
    // 0x148e384: r0 = CachedNetworkImage()
    //     0x148e384: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x148e388: stur            x0, [fp, #-0x48]
    // 0x148e38c: r16 = 56.000000
    //     0x148e38c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148e390: ldr             x16, [x16, #0xb78]
    // 0x148e394: r30 = 56.000000
    //     0x148e394: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x148e398: ldr             lr, [lr, #0xb78]
    // 0x148e39c: stp             lr, x16, [SP, #0x18]
    // 0x148e3a0: r16 = Instance_BoxFit
    //     0x148e3a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x148e3a4: ldr             x16, [x16, #0x118]
    // 0x148e3a8: ldur            lr, [fp, #-0x38]
    // 0x148e3ac: stp             lr, x16, [SP, #8]
    // 0x148e3b0: ldur            x16, [fp, #-0x40]
    // 0x148e3b4: str             x16, [SP]
    // 0x148e3b8: mov             x1, x0
    // 0x148e3bc: ldur            x2, [fp, #-0x28]
    // 0x148e3c0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0x148e3c0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0x148e3c4: ldr             x4, [x4, #0x710]
    // 0x148e3c8: r0 = CachedNetworkImage()
    //     0x148e3c8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x148e3cc: r0 = ClipRRect()
    //     0x148e3cc: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x148e3d0: mov             x2, x0
    // 0x148e3d4: ldur            x0, [fp, #-0x30]
    // 0x148e3d8: stur            x2, [fp, #-0x38]
    // 0x148e3dc: StoreField: r2->field_f = r0
    //     0x148e3dc: stur            w0, [x2, #0xf]
    // 0x148e3e0: r0 = Instance_Clip
    //     0x148e3e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x148e3e4: ldr             x0, [x0, #0x138]
    // 0x148e3e8: ArrayStore: r2[0] = r0  ; List_4
    //     0x148e3e8: stur            w0, [x2, #0x17]
    // 0x148e3ec: ldur            x0, [fp, #-0x48]
    // 0x148e3f0: StoreField: r2->field_b = r0
    //     0x148e3f0: stur            w0, [x2, #0xb]
    // 0x148e3f4: ldur            x0, [fp, #-0x10]
    // 0x148e3f8: LoadField: r1 = r0->field_f
    //     0x148e3f8: ldur            w1, [x0, #0xf]
    // 0x148e3fc: DecompressPointer r1
    //     0x148e3fc: add             x1, x1, HEAP, lsl #32
    // 0x148e400: cmp             w1, NULL
    // 0x148e404: b.ne            #0x148e410
    // 0x148e408: r3 = ""
    //     0x148e408: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148e40c: b               #0x148e414
    // 0x148e410: mov             x3, x1
    // 0x148e414: ldur            x1, [fp, #-0x18]
    // 0x148e418: stur            x3, [fp, #-0x28]
    // 0x148e41c: r0 = of()
    //     0x148e41c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148e420: LoadField: r1 = r0->field_87
    //     0x148e420: ldur            w1, [x0, #0x87]
    // 0x148e424: DecompressPointer r1
    //     0x148e424: add             x1, x1, HEAP, lsl #32
    // 0x148e428: LoadField: r0 = r1->field_2b
    //     0x148e428: ldur            w0, [x1, #0x2b]
    // 0x148e42c: DecompressPointer r0
    //     0x148e42c: add             x0, x0, HEAP, lsl #32
    // 0x148e430: stur            x0, [fp, #-0x30]
    // 0x148e434: r1 = Instance_Color
    //     0x148e434: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148e438: d0 = 0.700000
    //     0x148e438: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x148e43c: ldr             d0, [x17, #0xf48]
    // 0x148e440: r0 = withOpacity()
    //     0x148e440: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148e444: r16 = 12.000000
    //     0x148e444: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148e448: ldr             x16, [x16, #0x9e8]
    // 0x148e44c: stp             x0, x16, [SP]
    // 0x148e450: ldur            x1, [fp, #-0x30]
    // 0x148e454: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148e454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148e458: ldr             x4, [x4, #0xaa0]
    // 0x148e45c: r0 = copyWith()
    //     0x148e45c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148e460: stur            x0, [fp, #-0x30]
    // 0x148e464: r0 = Text()
    //     0x148e464: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148e468: mov             x3, x0
    // 0x148e46c: ldur            x0, [fp, #-0x28]
    // 0x148e470: stur            x3, [fp, #-0x40]
    // 0x148e474: StoreField: r3->field_b = r0
    //     0x148e474: stur            w0, [x3, #0xb]
    // 0x148e478: ldur            x0, [fp, #-0x30]
    // 0x148e47c: StoreField: r3->field_13 = r0
    //     0x148e47c: stur            w0, [x3, #0x13]
    // 0x148e480: r0 = 4
    //     0x148e480: movz            x0, #0x4
    // 0x148e484: StoreField: r3->field_37 = r0
    //     0x148e484: stur            w0, [x3, #0x37]
    // 0x148e488: r1 = Null
    //     0x148e488: mov             x1, NULL
    // 0x148e48c: r2 = 8
    //     0x148e48c: movz            x2, #0x8
    // 0x148e490: r0 = AllocateArray()
    //     0x148e490: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148e494: mov             x1, x0
    // 0x148e498: stur            x1, [fp, #-0x28]
    // 0x148e49c: r16 = "Size: "
    //     0x148e49c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x148e4a0: ldr             x16, [x16, #0xf00]
    // 0x148e4a4: StoreField: r1->field_f = r16
    //     0x148e4a4: stur            w16, [x1, #0xf]
    // 0x148e4a8: ldur            x2, [fp, #-0x10]
    // 0x148e4ac: LoadField: r0 = r2->field_2f
    //     0x148e4ac: ldur            w0, [x2, #0x2f]
    // 0x148e4b0: DecompressPointer r0
    //     0x148e4b0: add             x0, x0, HEAP, lsl #32
    // 0x148e4b4: cmp             w0, NULL
    // 0x148e4b8: b.ne            #0x148e4c0
    // 0x148e4bc: r0 = ""
    //     0x148e4bc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148e4c0: StoreField: r1->field_13 = r0
    //     0x148e4c0: stur            w0, [x1, #0x13]
    // 0x148e4c4: r16 = " / Qty: "
    //     0x148e4c4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x148e4c8: ldr             x16, [x16, #0x760]
    // 0x148e4cc: ArrayStore: r1[0] = r16  ; List_4
    //     0x148e4cc: stur            w16, [x1, #0x17]
    // 0x148e4d0: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x148e4d0: ldur            w0, [x2, #0x17]
    // 0x148e4d4: DecompressPointer r0
    //     0x148e4d4: add             x0, x0, HEAP, lsl #32
    // 0x148e4d8: r3 = 60
    //     0x148e4d8: movz            x3, #0x3c
    // 0x148e4dc: branchIfSmi(r0, 0x148e4e8)
    //     0x148e4dc: tbz             w0, #0, #0x148e4e8
    // 0x148e4e0: r3 = LoadClassIdInstr(r0)
    //     0x148e4e0: ldur            x3, [x0, #-1]
    //     0x148e4e4: ubfx            x3, x3, #0xc, #0x14
    // 0x148e4e8: str             x0, [SP]
    // 0x148e4ec: mov             x0, x3
    // 0x148e4f0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x148e4f0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x148e4f4: r0 = GDT[cid_x0 + 0x2700]()
    //     0x148e4f4: movz            x17, #0x2700
    //     0x148e4f8: add             lr, x0, x17
    //     0x148e4fc: ldr             lr, [x21, lr, lsl #3]
    //     0x148e500: blr             lr
    // 0x148e504: mov             x1, x0
    // 0x148e508: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x148e508: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x148e50c: r0 = tryParse()
    //     0x148e50c: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x148e510: cmp             w0, NULL
    // 0x148e514: b.ne            #0x148e520
    // 0x148e518: r3 = 0
    //     0x148e518: movz            x3, #0
    // 0x148e51c: b               #0x148e530
    // 0x148e520: r1 = LoadInt32Instr(r0)
    //     0x148e520: sbfx            x1, x0, #1, #0x1f
    //     0x148e524: tbz             w0, #0, #0x148e52c
    //     0x148e528: ldur            x1, [x0, #7]
    // 0x148e52c: mov             x3, x1
    // 0x148e530: ldur            x2, [fp, #-0x10]
    // 0x148e534: r0 = BoxInt64Instr(r3)
    //     0x148e534: sbfiz           x0, x3, #1, #0x1f
    //     0x148e538: cmp             x3, x0, asr #1
    //     0x148e53c: b.eq            #0x148e548
    //     0x148e540: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x148e544: stur            x3, [x0, #7]
    // 0x148e548: ldur            x1, [fp, #-0x28]
    // 0x148e54c: ArrayStore: r1[3] = r0  ; List_4
    //     0x148e54c: add             x25, x1, #0x1b
    //     0x148e550: str             w0, [x25]
    //     0x148e554: tbz             w0, #0, #0x148e570
    //     0x148e558: ldurb           w16, [x1, #-1]
    //     0x148e55c: ldurb           w17, [x0, #-1]
    //     0x148e560: and             x16, x17, x16, lsr #2
    //     0x148e564: tst             x16, HEAP, lsr #32
    //     0x148e568: b.eq            #0x148e570
    //     0x148e56c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x148e570: ldur            x16, [fp, #-0x28]
    // 0x148e574: str             x16, [SP]
    // 0x148e578: r0 = _interpolate()
    //     0x148e578: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x148e57c: ldur            x1, [fp, #-0x18]
    // 0x148e580: stur            x0, [fp, #-0x28]
    // 0x148e584: r0 = of()
    //     0x148e584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148e588: LoadField: r1 = r0->field_87
    //     0x148e588: ldur            w1, [x0, #0x87]
    // 0x148e58c: DecompressPointer r1
    //     0x148e58c: add             x1, x1, HEAP, lsl #32
    // 0x148e590: LoadField: r0 = r1->field_7
    //     0x148e590: ldur            w0, [x1, #7]
    // 0x148e594: DecompressPointer r0
    //     0x148e594: add             x0, x0, HEAP, lsl #32
    // 0x148e598: stur            x0, [fp, #-0x30]
    // 0x148e59c: r1 = Instance_Color
    //     0x148e59c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148e5a0: d0 = 0.700000
    //     0x148e5a0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x148e5a4: ldr             d0, [x17, #0xf48]
    // 0x148e5a8: r0 = withOpacity()
    //     0x148e5a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x148e5ac: r16 = 12.000000
    //     0x148e5ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148e5b0: ldr             x16, [x16, #0x9e8]
    // 0x148e5b4: stp             x0, x16, [SP]
    // 0x148e5b8: ldur            x1, [fp, #-0x30]
    // 0x148e5bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148e5bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148e5c0: ldr             x4, [x4, #0xaa0]
    // 0x148e5c4: r0 = copyWith()
    //     0x148e5c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148e5c8: stur            x0, [fp, #-0x30]
    // 0x148e5cc: r0 = Text()
    //     0x148e5cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148e5d0: mov             x2, x0
    // 0x148e5d4: ldur            x0, [fp, #-0x28]
    // 0x148e5d8: stur            x2, [fp, #-0x48]
    // 0x148e5dc: StoreField: r2->field_b = r0
    //     0x148e5dc: stur            w0, [x2, #0xb]
    // 0x148e5e0: ldur            x0, [fp, #-0x30]
    // 0x148e5e4: StoreField: r2->field_13 = r0
    //     0x148e5e4: stur            w0, [x2, #0x13]
    // 0x148e5e8: ldur            x0, [fp, #-0x10]
    // 0x148e5ec: LoadField: r1 = r0->field_2b
    //     0x148e5ec: ldur            w1, [x0, #0x2b]
    // 0x148e5f0: DecompressPointer r1
    //     0x148e5f0: add             x1, x1, HEAP, lsl #32
    // 0x148e5f4: cmp             w1, NULL
    // 0x148e5f8: b.ne            #0x148e608
    // 0x148e5fc: r5 = "₹0"
    //     0x148e5fc: add             x5, PP, #0x34, lsl #12  ; [pp+0x34a10] "₹0"
    //     0x148e600: ldr             x5, [x5, #0xa10]
    // 0x148e604: b               #0x148e60c
    // 0x148e608: mov             x5, x1
    // 0x148e60c: ldur            x4, [fp, #-0x38]
    // 0x148e610: ldur            x3, [fp, #-0x40]
    // 0x148e614: ldur            x1, [fp, #-0x18]
    // 0x148e618: stur            x5, [fp, #-0x28]
    // 0x148e61c: r0 = of()
    //     0x148e61c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x148e620: LoadField: r1 = r0->field_87
    //     0x148e620: ldur            w1, [x0, #0x87]
    // 0x148e624: DecompressPointer r1
    //     0x148e624: add             x1, x1, HEAP, lsl #32
    // 0x148e628: LoadField: r0 = r1->field_2b
    //     0x148e628: ldur            w0, [x1, #0x2b]
    // 0x148e62c: DecompressPointer r0
    //     0x148e62c: add             x0, x0, HEAP, lsl #32
    // 0x148e630: r16 = 12.000000
    //     0x148e630: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x148e634: ldr             x16, [x16, #0x9e8]
    // 0x148e638: r30 = Instance_Color
    //     0x148e638: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x148e63c: stp             lr, x16, [SP]
    // 0x148e640: mov             x1, x0
    // 0x148e644: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148e644: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x148e648: ldr             x4, [x4, #0xaa0]
    // 0x148e64c: r0 = copyWith()
    //     0x148e64c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x148e650: stur            x0, [fp, #-0x18]
    // 0x148e654: r0 = Text()
    //     0x148e654: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x148e658: mov             x3, x0
    // 0x148e65c: ldur            x0, [fp, #-0x28]
    // 0x148e660: stur            x3, [fp, #-0x30]
    // 0x148e664: StoreField: r3->field_b = r0
    //     0x148e664: stur            w0, [x3, #0xb]
    // 0x148e668: ldur            x0, [fp, #-0x18]
    // 0x148e66c: StoreField: r3->field_13 = r0
    //     0x148e66c: stur            w0, [x3, #0x13]
    // 0x148e670: r1 = Null
    //     0x148e670: mov             x1, NULL
    // 0x148e674: r2 = 10
    //     0x148e674: movz            x2, #0xa
    // 0x148e678: r0 = AllocateArray()
    //     0x148e678: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148e67c: mov             x2, x0
    // 0x148e680: ldur            x0, [fp, #-0x40]
    // 0x148e684: stur            x2, [fp, #-0x18]
    // 0x148e688: StoreField: r2->field_f = r0
    //     0x148e688: stur            w0, [x2, #0xf]
    // 0x148e68c: r16 = Instance_SizedBox
    //     0x148e68c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a60] Obj!SizedBox@d68041
    //     0x148e690: ldr             x16, [x16, #0xa60]
    // 0x148e694: StoreField: r2->field_13 = r16
    //     0x148e694: stur            w16, [x2, #0x13]
    // 0x148e698: ldur            x0, [fp, #-0x48]
    // 0x148e69c: ArrayStore: r2[0] = r0  ; List_4
    //     0x148e69c: stur            w0, [x2, #0x17]
    // 0x148e6a0: r16 = Instance_SizedBox
    //     0x148e6a0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a60] Obj!SizedBox@d68041
    //     0x148e6a4: ldr             x16, [x16, #0xa60]
    // 0x148e6a8: StoreField: r2->field_1b = r16
    //     0x148e6a8: stur            w16, [x2, #0x1b]
    // 0x148e6ac: ldur            x0, [fp, #-0x30]
    // 0x148e6b0: StoreField: r2->field_1f = r0
    //     0x148e6b0: stur            w0, [x2, #0x1f]
    // 0x148e6b4: r1 = <Widget>
    //     0x148e6b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148e6b8: r0 = AllocateGrowableArray()
    //     0x148e6b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148e6bc: mov             x1, x0
    // 0x148e6c0: ldur            x0, [fp, #-0x18]
    // 0x148e6c4: stur            x1, [fp, #-0x28]
    // 0x148e6c8: StoreField: r1->field_f = r0
    //     0x148e6c8: stur            w0, [x1, #0xf]
    // 0x148e6cc: r0 = 10
    //     0x148e6cc: movz            x0, #0xa
    // 0x148e6d0: StoreField: r1->field_b = r0
    //     0x148e6d0: stur            w0, [x1, #0xb]
    // 0x148e6d4: r0 = Column()
    //     0x148e6d4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148e6d8: mov             x1, x0
    // 0x148e6dc: r0 = Instance_Axis
    //     0x148e6dc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148e6e0: stur            x1, [fp, #-0x18]
    // 0x148e6e4: StoreField: r1->field_f = r0
    //     0x148e6e4: stur            w0, [x1, #0xf]
    // 0x148e6e8: r2 = Instance_MainAxisAlignment
    //     0x148e6e8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x148e6ec: ldr             x2, [x2, #0xa8]
    // 0x148e6f0: StoreField: r1->field_13 = r2
    //     0x148e6f0: stur            w2, [x1, #0x13]
    // 0x148e6f4: r2 = Instance_MainAxisSize
    //     0x148e6f4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148e6f8: ldr             x2, [x2, #0xa10]
    // 0x148e6fc: ArrayStore: r1[0] = r2  ; List_4
    //     0x148e6fc: stur            w2, [x1, #0x17]
    // 0x148e700: r3 = Instance_CrossAxisAlignment
    //     0x148e700: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x148e704: ldr             x3, [x3, #0x890]
    // 0x148e708: StoreField: r1->field_1b = r3
    //     0x148e708: stur            w3, [x1, #0x1b]
    // 0x148e70c: r3 = Instance_VerticalDirection
    //     0x148e70c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148e710: ldr             x3, [x3, #0xa20]
    // 0x148e714: StoreField: r1->field_23 = r3
    //     0x148e714: stur            w3, [x1, #0x23]
    // 0x148e718: r4 = Instance_Clip
    //     0x148e718: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148e71c: ldr             x4, [x4, #0x38]
    // 0x148e720: StoreField: r1->field_2b = r4
    //     0x148e720: stur            w4, [x1, #0x2b]
    // 0x148e724: StoreField: r1->field_2f = rZR
    //     0x148e724: stur            xzr, [x1, #0x2f]
    // 0x148e728: ldur            x5, [fp, #-0x28]
    // 0x148e72c: StoreField: r1->field_b = r5
    //     0x148e72c: stur            w5, [x1, #0xb]
    // 0x148e730: r0 = Padding()
    //     0x148e730: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x148e734: mov             x2, x0
    // 0x148e738: r0 = Instance_EdgeInsets
    //     0x148e738: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x148e73c: ldr             x0, [x0, #0x980]
    // 0x148e740: stur            x2, [fp, #-0x28]
    // 0x148e744: StoreField: r2->field_f = r0
    //     0x148e744: stur            w0, [x2, #0xf]
    // 0x148e748: ldur            x0, [fp, #-0x18]
    // 0x148e74c: StoreField: r2->field_b = r0
    //     0x148e74c: stur            w0, [x2, #0xb]
    // 0x148e750: r1 = <FlexParentData>
    //     0x148e750: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x148e754: ldr             x1, [x1, #0xe00]
    // 0x148e758: r0 = Expanded()
    //     0x148e758: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x148e75c: mov             x3, x0
    // 0x148e760: r0 = 1
    //     0x148e760: movz            x0, #0x1
    // 0x148e764: stur            x3, [fp, #-0x18]
    // 0x148e768: StoreField: r3->field_13 = r0
    //     0x148e768: stur            x0, [x3, #0x13]
    // 0x148e76c: r0 = Instance_FlexFit
    //     0x148e76c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x148e770: ldr             x0, [x0, #0xe08]
    // 0x148e774: StoreField: r3->field_1b = r0
    //     0x148e774: stur            w0, [x3, #0x1b]
    // 0x148e778: ldur            x0, [fp, #-0x28]
    // 0x148e77c: StoreField: r3->field_b = r0
    //     0x148e77c: stur            w0, [x3, #0xb]
    // 0x148e780: r1 = Null
    //     0x148e780: mov             x1, NULL
    // 0x148e784: r2 = 4
    //     0x148e784: movz            x2, #0x4
    // 0x148e788: r0 = AllocateArray()
    //     0x148e788: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148e78c: mov             x2, x0
    // 0x148e790: ldur            x0, [fp, #-0x38]
    // 0x148e794: stur            x2, [fp, #-0x28]
    // 0x148e798: StoreField: r2->field_f = r0
    //     0x148e798: stur            w0, [x2, #0xf]
    // 0x148e79c: ldur            x0, [fp, #-0x18]
    // 0x148e7a0: StoreField: r2->field_13 = r0
    //     0x148e7a0: stur            w0, [x2, #0x13]
    // 0x148e7a4: r1 = <Widget>
    //     0x148e7a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148e7a8: r0 = AllocateGrowableArray()
    //     0x148e7a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148e7ac: mov             x1, x0
    // 0x148e7b0: ldur            x0, [fp, #-0x28]
    // 0x148e7b4: stur            x1, [fp, #-0x18]
    // 0x148e7b8: StoreField: r1->field_f = r0
    //     0x148e7b8: stur            w0, [x1, #0xf]
    // 0x148e7bc: r2 = 4
    //     0x148e7bc: movz            x2, #0x4
    // 0x148e7c0: StoreField: r1->field_b = r2
    //     0x148e7c0: stur            w2, [x1, #0xb]
    // 0x148e7c4: r0 = Row()
    //     0x148e7c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x148e7c8: mov             x2, x0
    // 0x148e7cc: r0 = Instance_Axis
    //     0x148e7cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x148e7d0: stur            x2, [fp, #-0x28]
    // 0x148e7d4: StoreField: r2->field_f = r0
    //     0x148e7d4: stur            w0, [x2, #0xf]
    // 0x148e7d8: r0 = Instance_MainAxisAlignment
    //     0x148e7d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148e7dc: ldr             x0, [x0, #0xa08]
    // 0x148e7e0: StoreField: r2->field_13 = r0
    //     0x148e7e0: stur            w0, [x2, #0x13]
    // 0x148e7e4: r3 = Instance_MainAxisSize
    //     0x148e7e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148e7e8: ldr             x3, [x3, #0xa10]
    // 0x148e7ec: ArrayStore: r2[0] = r3  ; List_4
    //     0x148e7ec: stur            w3, [x2, #0x17]
    // 0x148e7f0: r4 = Instance_CrossAxisAlignment
    //     0x148e7f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148e7f4: ldr             x4, [x4, #0xa18]
    // 0x148e7f8: StoreField: r2->field_1b = r4
    //     0x148e7f8: stur            w4, [x2, #0x1b]
    // 0x148e7fc: r5 = Instance_VerticalDirection
    //     0x148e7fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148e800: ldr             x5, [x5, #0xa20]
    // 0x148e804: StoreField: r2->field_23 = r5
    //     0x148e804: stur            w5, [x2, #0x23]
    // 0x148e808: r6 = Instance_Clip
    //     0x148e808: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148e80c: ldr             x6, [x6, #0x38]
    // 0x148e810: StoreField: r2->field_2b = r6
    //     0x148e810: stur            w6, [x2, #0x2b]
    // 0x148e814: StoreField: r2->field_2f = rZR
    //     0x148e814: stur            xzr, [x2, #0x2f]
    // 0x148e818: ldur            x1, [fp, #-0x18]
    // 0x148e81c: StoreField: r2->field_b = r1
    //     0x148e81c: stur            w1, [x2, #0xb]
    // 0x148e820: ldur            x7, [fp, #-0x10]
    // 0x148e824: LoadField: r1 = r7->field_3f
    //     0x148e824: ldur            w1, [x7, #0x3f]
    // 0x148e828: DecompressPointer r1
    //     0x148e828: add             x1, x1, HEAP, lsl #32
    // 0x148e82c: cmp             w1, NULL
    // 0x148e830: b.ne            #0x148e83c
    // 0x148e834: r1 = Null
    //     0x148e834: mov             x1, NULL
    // 0x148e838: b               #0x148e850
    // 0x148e83c: LoadField: r8 = r1->field_b
    //     0x148e83c: ldur            w8, [x1, #0xb]
    // 0x148e840: cbnz            w8, #0x148e84c
    // 0x148e844: r1 = false
    //     0x148e844: add             x1, NULL, #0x30  ; false
    // 0x148e848: b               #0x148e850
    // 0x148e84c: r1 = true
    //     0x148e84c: add             x1, NULL, #0x20  ; true
    // 0x148e850: cmp             w1, NULL
    // 0x148e854: b.eq            #0x148e868
    // 0x148e858: tbnz            w1, #4, #0x148e868
    // 0x148e85c: mov             x0, x7
    // 0x148e860: r2 = true
    //     0x148e860: add             x2, NULL, #0x20  ; true
    // 0x148e864: b               #0x148e8c0
    // 0x148e868: ldur            x1, [fp, #-8]
    // 0x148e86c: r0 = controller()
    //     0x148e86c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148e870: LoadField: r1 = r0->field_6f
    //     0x148e870: ldur            w1, [x0, #0x6f]
    // 0x148e874: DecompressPointer r1
    //     0x148e874: add             x1, x1, HEAP, lsl #32
    // 0x148e878: r0 = value()
    //     0x148e878: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148e87c: LoadField: r1 = r0->field_13
    //     0x148e87c: ldur            w1, [x0, #0x13]
    // 0x148e880: DecompressPointer r1
    //     0x148e880: add             x1, x1, HEAP, lsl #32
    // 0x148e884: cmp             w1, NULL
    // 0x148e888: b.ne            #0x148e894
    // 0x148e88c: r0 = Null
    //     0x148e88c: mov             x0, NULL
    // 0x148e890: b               #0x148e8ac
    // 0x148e894: LoadField: r0 = r1->field_b
    //     0x148e894: ldur            w0, [x1, #0xb]
    // 0x148e898: cbnz            w0, #0x148e8a4
    // 0x148e89c: r1 = false
    //     0x148e89c: add             x1, NULL, #0x30  ; false
    // 0x148e8a0: b               #0x148e8a8
    // 0x148e8a4: r1 = true
    //     0x148e8a4: add             x1, NULL, #0x20  ; true
    // 0x148e8a8: mov             x0, x1
    // 0x148e8ac: cmp             w0, NULL
    // 0x148e8b0: b.ne            #0x148e8b8
    // 0x148e8b4: r0 = false
    //     0x148e8b4: add             x0, NULL, #0x30  ; false
    // 0x148e8b8: mov             x2, x0
    // 0x148e8bc: ldur            x0, [fp, #-0x10]
    // 0x148e8c0: stur            x2, [fp, #-0x30]
    // 0x148e8c4: LoadField: r3 = r0->field_3f
    //     0x148e8c4: ldur            w3, [x0, #0x3f]
    // 0x148e8c8: DecompressPointer r3
    //     0x148e8c8: add             x3, x3, HEAP, lsl #32
    // 0x148e8cc: ldur            x1, [fp, #-8]
    // 0x148e8d0: stur            x3, [fp, #-0x18]
    // 0x148e8d4: r0 = controller()
    //     0x148e8d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148e8d8: LoadField: r1 = r0->field_6f
    //     0x148e8d8: ldur            w1, [x0, #0x6f]
    // 0x148e8dc: DecompressPointer r1
    //     0x148e8dc: add             x1, x1, HEAP, lsl #32
    // 0x148e8e0: r0 = value()
    //     0x148e8e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148e8e4: mov             x2, x0
    // 0x148e8e8: ldur            x0, [fp, #-0x10]
    // 0x148e8ec: stur            x2, [fp, #-0x38]
    // 0x148e8f0: LoadField: r1 = r0->field_43
    //     0x148e8f0: ldur            w1, [x0, #0x43]
    // 0x148e8f4: DecompressPointer r1
    //     0x148e8f4: add             x1, x1, HEAP, lsl #32
    // 0x148e8f8: cbz             w1, #0x148e910
    // 0x148e8fc: LoadField: r1 = r0->field_47
    //     0x148e8fc: ldur            w1, [x0, #0x47]
    // 0x148e900: DecompressPointer r1
    //     0x148e900: add             x1, x1, HEAP, lsl #32
    // 0x148e904: mov             x5, x1
    // 0x148e908: mov             x0, x2
    // 0x148e90c: b               #0x148e9ac
    // 0x148e910: ldur            x1, [fp, #-8]
    // 0x148e914: r0 = controller()
    //     0x148e914: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148e918: LoadField: r1 = r0->field_bf
    //     0x148e918: ldur            w1, [x0, #0xbf]
    // 0x148e91c: DecompressPointer r1
    //     0x148e91c: add             x1, x1, HEAP, lsl #32
    // 0x148e920: cmp             w1, NULL
    // 0x148e924: b.eq            #0x148e994
    // 0x148e928: ldur            x1, [fp, #-8]
    // 0x148e92c: r0 = controller()
    //     0x148e92c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148e930: LoadField: r1 = r0->field_bf
    //     0x148e930: ldur            w1, [x0, #0xbf]
    // 0x148e934: DecompressPointer r1
    //     0x148e934: add             x1, x1, HEAP, lsl #32
    // 0x148e938: cmp             w1, NULL
    // 0x148e93c: b.ne            #0x148e948
    // 0x148e940: r0 = Null
    //     0x148e940: mov             x0, NULL
    // 0x148e944: b               #0x148e960
    // 0x148e948: LoadField: r0 = r1->field_7
    //     0x148e948: ldur            w0, [x1, #7]
    // 0x148e94c: cbnz            w0, #0x148e958
    // 0x148e950: r1 = false
    //     0x148e950: add             x1, NULL, #0x30  ; false
    // 0x148e954: b               #0x148e95c
    // 0x148e958: r1 = true
    //     0x148e958: add             x1, NULL, #0x20  ; true
    // 0x148e95c: mov             x0, x1
    // 0x148e960: cmp             w0, NULL
    // 0x148e964: b.eq            #0x148e994
    // 0x148e968: tbnz            w0, #4, #0x148e994
    // 0x148e96c: ldur            x1, [fp, #-8]
    // 0x148e970: r0 = controller()
    //     0x148e970: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148e974: LoadField: r1 = r0->field_bf
    //     0x148e974: ldur            w1, [x0, #0xbf]
    // 0x148e978: DecompressPointer r1
    //     0x148e978: add             x1, x1, HEAP, lsl #32
    // 0x148e97c: cmp             w1, NULL
    // 0x148e980: b.ne            #0x148e98c
    // 0x148e984: r0 = ""
    //     0x148e984: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x148e988: b               #0x148e9a4
    // 0x148e98c: mov             x0, x1
    // 0x148e990: b               #0x148e9a4
    // 0x148e994: ldur            x0, [fp, #-0x10]
    // 0x148e998: LoadField: r1 = r0->field_47
    //     0x148e998: ldur            w1, [x0, #0x47]
    // 0x148e99c: DecompressPointer r1
    //     0x148e99c: add             x1, x1, HEAP, lsl #32
    // 0x148e9a0: mov             x0, x1
    // 0x148e9a4: mov             x5, x0
    // 0x148e9a8: ldur            x0, [fp, #-0x38]
    // 0x148e9ac: ldur            x4, [fp, #-0x20]
    // 0x148e9b0: ldur            x3, [fp, #-0x28]
    // 0x148e9b4: ldur            x1, [fp, #-0x30]
    // 0x148e9b8: ldur            x2, [fp, #-0x18]
    // 0x148e9bc: stur            x5, [fp, #-8]
    // 0x148e9c0: r0 = CustomisedStrip()
    //     0x148e9c0: bl              #0xa1c2f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0x148e9c4: mov             x1, x0
    // 0x148e9c8: ldur            x0, [fp, #-0x18]
    // 0x148e9cc: stur            x1, [fp, #-0x10]
    // 0x148e9d0: StoreField: r1->field_b = r0
    //     0x148e9d0: stur            w0, [x1, #0xb]
    // 0x148e9d4: ldur            x0, [fp, #-0x38]
    // 0x148e9d8: StoreField: r1->field_f = r0
    //     0x148e9d8: stur            w0, [x1, #0xf]
    // 0x148e9dc: ldur            x0, [fp, #-8]
    // 0x148e9e0: StoreField: r1->field_13 = r0
    //     0x148e9e0: stur            w0, [x1, #0x13]
    // 0x148e9e4: r0 = Visibility()
    //     0x148e9e4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x148e9e8: mov             x3, x0
    // 0x148e9ec: ldur            x0, [fp, #-0x10]
    // 0x148e9f0: stur            x3, [fp, #-8]
    // 0x148e9f4: StoreField: r3->field_b = r0
    //     0x148e9f4: stur            w0, [x3, #0xb]
    // 0x148e9f8: r0 = Instance_SizedBox
    //     0x148e9f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x148e9fc: StoreField: r3->field_f = r0
    //     0x148e9fc: stur            w0, [x3, #0xf]
    // 0x148ea00: ldur            x0, [fp, #-0x30]
    // 0x148ea04: StoreField: r3->field_13 = r0
    //     0x148ea04: stur            w0, [x3, #0x13]
    // 0x148ea08: r0 = false
    //     0x148ea08: add             x0, NULL, #0x30  ; false
    // 0x148ea0c: ArrayStore: r3[0] = r0  ; List_4
    //     0x148ea0c: stur            w0, [x3, #0x17]
    // 0x148ea10: StoreField: r3->field_1b = r0
    //     0x148ea10: stur            w0, [x3, #0x1b]
    // 0x148ea14: StoreField: r3->field_1f = r0
    //     0x148ea14: stur            w0, [x3, #0x1f]
    // 0x148ea18: StoreField: r3->field_23 = r0
    //     0x148ea18: stur            w0, [x3, #0x23]
    // 0x148ea1c: StoreField: r3->field_27 = r0
    //     0x148ea1c: stur            w0, [x3, #0x27]
    // 0x148ea20: StoreField: r3->field_2b = r0
    //     0x148ea20: stur            w0, [x3, #0x2b]
    // 0x148ea24: r1 = Null
    //     0x148ea24: mov             x1, NULL
    // 0x148ea28: r2 = 4
    //     0x148ea28: movz            x2, #0x4
    // 0x148ea2c: r0 = AllocateArray()
    //     0x148ea2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x148ea30: mov             x2, x0
    // 0x148ea34: ldur            x0, [fp, #-0x28]
    // 0x148ea38: stur            x2, [fp, #-0x10]
    // 0x148ea3c: StoreField: r2->field_f = r0
    //     0x148ea3c: stur            w0, [x2, #0xf]
    // 0x148ea40: ldur            x0, [fp, #-8]
    // 0x148ea44: StoreField: r2->field_13 = r0
    //     0x148ea44: stur            w0, [x2, #0x13]
    // 0x148ea48: r1 = <Widget>
    //     0x148ea48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148ea4c: r0 = AllocateGrowableArray()
    //     0x148ea4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x148ea50: mov             x1, x0
    // 0x148ea54: ldur            x0, [fp, #-0x10]
    // 0x148ea58: stur            x1, [fp, #-8]
    // 0x148ea5c: StoreField: r1->field_f = r0
    //     0x148ea5c: stur            w0, [x1, #0xf]
    // 0x148ea60: r0 = 4
    //     0x148ea60: movz            x0, #0x4
    // 0x148ea64: StoreField: r1->field_b = r0
    //     0x148ea64: stur            w0, [x1, #0xb]
    // 0x148ea68: r0 = Column()
    //     0x148ea68: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148ea6c: mov             x1, x0
    // 0x148ea70: r0 = Instance_Axis
    //     0x148ea70: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148ea74: stur            x1, [fp, #-0x10]
    // 0x148ea78: StoreField: r1->field_f = r0
    //     0x148ea78: stur            w0, [x1, #0xf]
    // 0x148ea7c: r0 = Instance_MainAxisAlignment
    //     0x148ea7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x148ea80: ldr             x0, [x0, #0xa08]
    // 0x148ea84: StoreField: r1->field_13 = r0
    //     0x148ea84: stur            w0, [x1, #0x13]
    // 0x148ea88: r0 = Instance_MainAxisSize
    //     0x148ea88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148ea8c: ldr             x0, [x0, #0xa10]
    // 0x148ea90: ArrayStore: r1[0] = r0  ; List_4
    //     0x148ea90: stur            w0, [x1, #0x17]
    // 0x148ea94: r0 = Instance_CrossAxisAlignment
    //     0x148ea94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x148ea98: ldr             x0, [x0, #0xa18]
    // 0x148ea9c: StoreField: r1->field_1b = r0
    //     0x148ea9c: stur            w0, [x1, #0x1b]
    // 0x148eaa0: r0 = Instance_VerticalDirection
    //     0x148eaa0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x148eaa4: ldr             x0, [x0, #0xa20]
    // 0x148eaa8: StoreField: r1->field_23 = r0
    //     0x148eaa8: stur            w0, [x1, #0x23]
    // 0x148eaac: r0 = Instance_Clip
    //     0x148eaac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148eab0: ldr             x0, [x0, #0x38]
    // 0x148eab4: StoreField: r1->field_2b = r0
    //     0x148eab4: stur            w0, [x1, #0x2b]
    // 0x148eab8: StoreField: r1->field_2f = rZR
    //     0x148eab8: stur            xzr, [x1, #0x2f]
    // 0x148eabc: ldur            x0, [fp, #-8]
    // 0x148eac0: StoreField: r1->field_b = r0
    //     0x148eac0: stur            w0, [x1, #0xb]
    // 0x148eac4: r0 = Card()
    //     0x148eac4: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x148eac8: r1 = 0.000000
    //     0x148eac8: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x148eacc: ArrayStore: r0[0] = r1  ; List_4
    //     0x148eacc: stur            w1, [x0, #0x17]
    // 0x148ead0: ldur            x1, [fp, #-0x20]
    // 0x148ead4: StoreField: r0->field_1b = r1
    //     0x148ead4: stur            w1, [x0, #0x1b]
    // 0x148ead8: r1 = true
    //     0x148ead8: add             x1, NULL, #0x20  ; true
    // 0x148eadc: StoreField: r0->field_1f = r1
    //     0x148eadc: stur            w1, [x0, #0x1f]
    // 0x148eae0: ldur            x2, [fp, #-0x10]
    // 0x148eae4: StoreField: r0->field_2f = r2
    //     0x148eae4: stur            w2, [x0, #0x2f]
    // 0x148eae8: StoreField: r0->field_2b = r1
    //     0x148eae8: stur            w1, [x0, #0x2b]
    // 0x148eaec: r1 = Instance__CardVariant
    //     0x148eaec: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x148eaf0: ldr             x1, [x1, #0xa68]
    // 0x148eaf4: StoreField: r0->field_33 = r1
    //     0x148eaf4: stur            w1, [x0, #0x33]
    // 0x148eaf8: LeaveFrame
    //     0x148eaf8: mov             SP, fp
    //     0x148eafc: ldp             fp, lr, [SP], #0x10
    // 0x148eb00: ret
    //     0x148eb00: ret             
    // 0x148eb04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148eb04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148eb08: b               #0x148e2b0
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x148eb0c, size: 0x64
    // 0x148eb0c: EnterFrame
    //     0x148eb0c: stp             fp, lr, [SP, #-0x10]!
    //     0x148eb10: mov             fp, SP
    // 0x148eb14: AllocStack(0x8)
    //     0x148eb14: sub             SP, SP, #8
    // 0x148eb18: SetupParameters()
    //     0x148eb18: ldr             x0, [fp, #0x10]
    //     0x148eb1c: ldur            w1, [x0, #0x17]
    //     0x148eb20: add             x1, x1, HEAP, lsl #32
    // 0x148eb24: CheckStackOverflow
    //     0x148eb24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148eb28: cmp             SP, x16
    //     0x148eb2c: b.ls            #0x148eb68
    // 0x148eb30: LoadField: r0 = r1->field_f
    //     0x148eb30: ldur            w0, [x1, #0xf]
    // 0x148eb34: DecompressPointer r0
    //     0x148eb34: add             x0, x0, HEAP, lsl #32
    // 0x148eb38: mov             x1, x0
    // 0x148eb3c: r0 = controller()
    //     0x148eb3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148eb40: LoadField: r1 = r0->field_ab
    //     0x148eb40: ldur            w1, [x0, #0xab]
    // 0x148eb44: DecompressPointer r1
    //     0x148eb44: add             x1, x1, HEAP, lsl #32
    // 0x148eb48: r0 = value()
    //     0x148eb48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148eb4c: stur            x0, [fp, #-8]
    // 0x148eb50: r0 = CheckoutBreadCrumb()
    //     0x148eb50: bl              #0x13939f4  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x10)
    // 0x148eb54: ldur            x1, [fp, #-8]
    // 0x148eb58: StoreField: r0->field_b = r1
    //     0x148eb58: stur            w1, [x0, #0xb]
    // 0x148eb5c: LeaveFrame
    //     0x148eb5c: mov             SP, fp
    //     0x148eb60: ldp             fp, lr, [SP], #0x10
    // 0x148eb64: ret
    //     0x148eb64: ret             
    // 0x148eb68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148eb68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148eb6c: b               #0x148eb30
  }
  _ body(/* No info */) {
    // ** addr: 0x14d817c, size: 0x64
    // 0x14d817c: EnterFrame
    //     0x14d817c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d8180: mov             fp, SP
    // 0x14d8184: AllocStack(0x18)
    //     0x14d8184: sub             SP, SP, #0x18
    // 0x14d8188: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d8188: stur            x1, [fp, #-8]
    //     0x14d818c: stur            x2, [fp, #-0x10]
    // 0x14d8190: r1 = 2
    //     0x14d8190: movz            x1, #0x2
    // 0x14d8194: r0 = AllocateContext()
    //     0x14d8194: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d8198: mov             x1, x0
    // 0x14d819c: ldur            x0, [fp, #-8]
    // 0x14d81a0: stur            x1, [fp, #-0x18]
    // 0x14d81a4: StoreField: r1->field_f = r0
    //     0x14d81a4: stur            w0, [x1, #0xf]
    // 0x14d81a8: ldur            x0, [fp, #-0x10]
    // 0x14d81ac: StoreField: r1->field_13 = r0
    //     0x14d81ac: stur            w0, [x1, #0x13]
    // 0x14d81b0: r0 = Obx()
    //     0x14d81b0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d81b4: ldur            x2, [fp, #-0x18]
    // 0x14d81b8: r1 = Function '<anonymous closure>':.
    //     0x14d81b8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40da0] AnonymousClosure: (0x148ca84), in [package:customer_app/app/presentation/views/glass/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x14d817c)
    //     0x14d81bc: ldr             x1, [x1, #0xda0]
    // 0x14d81c0: stur            x0, [fp, #-8]
    // 0x14d81c4: r0 = AllocateClosure()
    //     0x14d81c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d81c8: mov             x1, x0
    // 0x14d81cc: ldur            x0, [fp, #-8]
    // 0x14d81d0: StoreField: r0->field_b = r1
    //     0x14d81d0: stur            w1, [x0, #0xb]
    // 0x14d81d4: LeaveFrame
    //     0x14d81d4: mov             SP, fp
    //     0x14d81d8: ldp             fp, lr, [SP], #0x10
    // 0x14d81dc: ret
    //     0x14d81dc: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e0f54, size: 0x250
    // 0x15e0f54: EnterFrame
    //     0x15e0f54: stp             fp, lr, [SP, #-0x10]!
    //     0x15e0f58: mov             fp, SP
    // 0x15e0f5c: AllocStack(0x28)
    //     0x15e0f5c: sub             SP, SP, #0x28
    // 0x15e0f60: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e0f60: stur            x1, [fp, #-8]
    //     0x15e0f64: stur            x2, [fp, #-0x10]
    // 0x15e0f68: CheckStackOverflow
    //     0x15e0f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e0f6c: cmp             SP, x16
    //     0x15e0f70: b.ls            #0x15e119c
    // 0x15e0f74: r1 = 2
    //     0x15e0f74: movz            x1, #0x2
    // 0x15e0f78: r0 = AllocateContext()
    //     0x15e0f78: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e0f7c: ldur            x1, [fp, #-8]
    // 0x15e0f80: stur            x0, [fp, #-0x18]
    // 0x15e0f84: StoreField: r0->field_f = r1
    //     0x15e0f84: stur            w1, [x0, #0xf]
    // 0x15e0f88: ldur            x2, [fp, #-0x10]
    // 0x15e0f8c: StoreField: r0->field_13 = r2
    //     0x15e0f8c: stur            w2, [x0, #0x13]
    // 0x15e0f90: r0 = Obx()
    //     0x15e0f90: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e0f94: ldur            x2, [fp, #-0x18]
    // 0x15e0f98: r1 = Function '<anonymous closure>':.
    //     0x15e0f98: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e28] AnonymousClosure: (0x15ce458), in [package:customer_app/app/presentation/views/glass/post_order/order_failure/order_failed_widget.dart] OrderFailedWidget::appBar (0x15e3c40)
    //     0x15e0f9c: ldr             x1, [x1, #0xe28]
    // 0x15e0fa0: stur            x0, [fp, #-0x10]
    // 0x15e0fa4: r0 = AllocateClosure()
    //     0x15e0fa4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e0fa8: mov             x1, x0
    // 0x15e0fac: ldur            x0, [fp, #-0x10]
    // 0x15e0fb0: StoreField: r0->field_b = r1
    //     0x15e0fb0: stur            w1, [x0, #0xb]
    // 0x15e0fb4: ldur            x1, [fp, #-8]
    // 0x15e0fb8: r0 = controller()
    //     0x15e0fb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e0fbc: LoadField: r1 = r0->field_5b
    //     0x15e0fbc: ldur            w1, [x0, #0x5b]
    // 0x15e0fc0: DecompressPointer r1
    //     0x15e0fc0: add             x1, x1, HEAP, lsl #32
    // 0x15e0fc4: r0 = value()
    //     0x15e0fc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e0fc8: tbnz            w0, #4, #0x15e1060
    // 0x15e0fcc: ldur            x2, [fp, #-0x18]
    // 0x15e0fd0: LoadField: r1 = r2->field_13
    //     0x15e0fd0: ldur            w1, [x2, #0x13]
    // 0x15e0fd4: DecompressPointer r1
    //     0x15e0fd4: add             x1, x1, HEAP, lsl #32
    // 0x15e0fd8: r0 = of()
    //     0x15e0fd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e0fdc: LoadField: r1 = r0->field_5b
    //     0x15e0fdc: ldur            w1, [x0, #0x5b]
    // 0x15e0fe0: DecompressPointer r1
    //     0x15e0fe0: add             x1, x1, HEAP, lsl #32
    // 0x15e0fe4: stur            x1, [fp, #-8]
    // 0x15e0fe8: r0 = ColorFilter()
    //     0x15e0fe8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e0fec: mov             x1, x0
    // 0x15e0ff0: ldur            x0, [fp, #-8]
    // 0x15e0ff4: stur            x1, [fp, #-0x20]
    // 0x15e0ff8: StoreField: r1->field_7 = r0
    //     0x15e0ff8: stur            w0, [x1, #7]
    // 0x15e0ffc: r0 = Instance_BlendMode
    //     0x15e0ffc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e1000: ldr             x0, [x0, #0xb30]
    // 0x15e1004: StoreField: r1->field_b = r0
    //     0x15e1004: stur            w0, [x1, #0xb]
    // 0x15e1008: r2 = 1
    //     0x15e1008: movz            x2, #0x1
    // 0x15e100c: StoreField: r1->field_13 = r2
    //     0x15e100c: stur            x2, [x1, #0x13]
    // 0x15e1010: r0 = SvgPicture()
    //     0x15e1010: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1014: stur            x0, [fp, #-8]
    // 0x15e1018: ldur            x16, [fp, #-0x20]
    // 0x15e101c: str             x16, [SP]
    // 0x15e1020: mov             x1, x0
    // 0x15e1024: r2 = "assets/images/search.svg"
    //     0x15e1024: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e1028: ldr             x2, [x2, #0xa30]
    // 0x15e102c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e102c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e1030: ldr             x4, [x4, #0xa38]
    // 0x15e1034: r0 = SvgPicture.asset()
    //     0x15e1034: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e1038: r0 = Align()
    //     0x15e1038: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e103c: r3 = Instance_Alignment
    //     0x15e103c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1040: ldr             x3, [x3, #0xb10]
    // 0x15e1044: StoreField: r0->field_f = r3
    //     0x15e1044: stur            w3, [x0, #0xf]
    // 0x15e1048: r4 = 1.000000
    //     0x15e1048: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e104c: StoreField: r0->field_13 = r4
    //     0x15e104c: stur            w4, [x0, #0x13]
    // 0x15e1050: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e1050: stur            w4, [x0, #0x17]
    // 0x15e1054: ldur            x1, [fp, #-8]
    // 0x15e1058: StoreField: r0->field_b = r1
    //     0x15e1058: stur            w1, [x0, #0xb]
    // 0x15e105c: b               #0x15e1110
    // 0x15e1060: ldur            x5, [fp, #-0x18]
    // 0x15e1064: r4 = 1.000000
    //     0x15e1064: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e1068: r0 = Instance_BlendMode
    //     0x15e1068: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e106c: ldr             x0, [x0, #0xb30]
    // 0x15e1070: r3 = Instance_Alignment
    //     0x15e1070: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1074: ldr             x3, [x3, #0xb10]
    // 0x15e1078: r2 = 1
    //     0x15e1078: movz            x2, #0x1
    // 0x15e107c: LoadField: r1 = r5->field_13
    //     0x15e107c: ldur            w1, [x5, #0x13]
    // 0x15e1080: DecompressPointer r1
    //     0x15e1080: add             x1, x1, HEAP, lsl #32
    // 0x15e1084: r0 = of()
    //     0x15e1084: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e1088: LoadField: r1 = r0->field_5b
    //     0x15e1088: ldur            w1, [x0, #0x5b]
    // 0x15e108c: DecompressPointer r1
    //     0x15e108c: add             x1, x1, HEAP, lsl #32
    // 0x15e1090: stur            x1, [fp, #-8]
    // 0x15e1094: r0 = ColorFilter()
    //     0x15e1094: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e1098: mov             x1, x0
    // 0x15e109c: ldur            x0, [fp, #-8]
    // 0x15e10a0: stur            x1, [fp, #-0x20]
    // 0x15e10a4: StoreField: r1->field_7 = r0
    //     0x15e10a4: stur            w0, [x1, #7]
    // 0x15e10a8: r0 = Instance_BlendMode
    //     0x15e10a8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e10ac: ldr             x0, [x0, #0xb30]
    // 0x15e10b0: StoreField: r1->field_b = r0
    //     0x15e10b0: stur            w0, [x1, #0xb]
    // 0x15e10b4: r0 = 1
    //     0x15e10b4: movz            x0, #0x1
    // 0x15e10b8: StoreField: r1->field_13 = r0
    //     0x15e10b8: stur            x0, [x1, #0x13]
    // 0x15e10bc: r0 = SvgPicture()
    //     0x15e10bc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e10c0: stur            x0, [fp, #-8]
    // 0x15e10c4: ldur            x16, [fp, #-0x20]
    // 0x15e10c8: str             x16, [SP]
    // 0x15e10cc: mov             x1, x0
    // 0x15e10d0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e10d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e10d4: ldr             x2, [x2, #0xa40]
    // 0x15e10d8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e10d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e10dc: ldr             x4, [x4, #0xa38]
    // 0x15e10e0: r0 = SvgPicture.asset()
    //     0x15e10e0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e10e4: r0 = Align()
    //     0x15e10e4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e10e8: mov             x1, x0
    // 0x15e10ec: r0 = Instance_Alignment
    //     0x15e10ec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e10f0: ldr             x0, [x0, #0xb10]
    // 0x15e10f4: StoreField: r1->field_f = r0
    //     0x15e10f4: stur            w0, [x1, #0xf]
    // 0x15e10f8: r0 = 1.000000
    //     0x15e10f8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e10fc: StoreField: r1->field_13 = r0
    //     0x15e10fc: stur            w0, [x1, #0x13]
    // 0x15e1100: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e1100: stur            w0, [x1, #0x17]
    // 0x15e1104: ldur            x0, [fp, #-8]
    // 0x15e1108: StoreField: r1->field_b = r0
    //     0x15e1108: stur            w0, [x1, #0xb]
    // 0x15e110c: mov             x0, x1
    // 0x15e1110: stur            x0, [fp, #-8]
    // 0x15e1114: r0 = InkWell()
    //     0x15e1114: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e1118: mov             x3, x0
    // 0x15e111c: ldur            x0, [fp, #-8]
    // 0x15e1120: stur            x3, [fp, #-0x20]
    // 0x15e1124: StoreField: r3->field_b = r0
    //     0x15e1124: stur            w0, [x3, #0xb]
    // 0x15e1128: ldur            x2, [fp, #-0x18]
    // 0x15e112c: r1 = Function '<anonymous closure>':.
    //     0x15e112c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e30] AnonymousClosure: (0x15ce36c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::appBar (0x15e90ec)
    //     0x15e1130: ldr             x1, [x1, #0xe30]
    // 0x15e1134: r0 = AllocateClosure()
    //     0x15e1134: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1138: ldur            x2, [fp, #-0x20]
    // 0x15e113c: StoreField: r2->field_f = r0
    //     0x15e113c: stur            w0, [x2, #0xf]
    // 0x15e1140: r0 = true
    //     0x15e1140: add             x0, NULL, #0x20  ; true
    // 0x15e1144: StoreField: r2->field_43 = r0
    //     0x15e1144: stur            w0, [x2, #0x43]
    // 0x15e1148: r1 = Instance_BoxShape
    //     0x15e1148: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e114c: ldr             x1, [x1, #0x80]
    // 0x15e1150: StoreField: r2->field_47 = r1
    //     0x15e1150: stur            w1, [x2, #0x47]
    // 0x15e1154: StoreField: r2->field_6f = r0
    //     0x15e1154: stur            w0, [x2, #0x6f]
    // 0x15e1158: r1 = false
    //     0x15e1158: add             x1, NULL, #0x30  ; false
    // 0x15e115c: StoreField: r2->field_73 = r1
    //     0x15e115c: stur            w1, [x2, #0x73]
    // 0x15e1160: StoreField: r2->field_83 = r0
    //     0x15e1160: stur            w0, [x2, #0x83]
    // 0x15e1164: StoreField: r2->field_7b = r1
    //     0x15e1164: stur            w1, [x2, #0x7b]
    // 0x15e1168: r0 = AppBar()
    //     0x15e1168: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e116c: stur            x0, [fp, #-8]
    // 0x15e1170: ldur            x16, [fp, #-0x10]
    // 0x15e1174: str             x16, [SP]
    // 0x15e1178: mov             x1, x0
    // 0x15e117c: ldur            x2, [fp, #-0x20]
    // 0x15e1180: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e1180: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e1184: ldr             x4, [x4, #0xf00]
    // 0x15e1188: r0 = AppBar()
    //     0x15e1188: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e118c: ldur            x0, [fp, #-8]
    // 0x15e1190: LeaveFrame
    //     0x15e1190: mov             SP, fp
    //     0x15e1194: ldp             fp, lr, [SP], #0x10
    // 0x15e1198: ret
    //     0x15e1198: ret             
    // 0x15e119c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e119c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e11a0: b               #0x15e0f74
  }
}
