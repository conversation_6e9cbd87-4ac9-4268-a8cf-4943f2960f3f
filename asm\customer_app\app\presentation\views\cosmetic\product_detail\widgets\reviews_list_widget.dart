// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart

// class id: 1049323, size: 0x8
class :: {
}

// class id: 4587, size: 0x14, field offset: 0x14
//   const constructor, 
class ReviewListWidget extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14caf48, size: 0xc0
    // 0x14caf48: EnterFrame
    //     0x14caf48: stp             fp, lr, [SP, #-0x10]!
    //     0x14caf4c: mov             fp, SP
    // 0x14caf50: AllocStack(0x18)
    //     0x14caf50: sub             SP, SP, #0x18
    // 0x14caf54: SetupParameters(ReviewListWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14caf54: stur            x1, [fp, #-8]
    //     0x14caf58: stur            x2, [fp, #-0x10]
    // 0x14caf5c: CheckStackOverflow
    //     0x14caf5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14caf60: cmp             SP, x16
    //     0x14caf64: b.ls            #0x14cb000
    // 0x14caf68: r1 = 2
    //     0x14caf68: movz            x1, #0x2
    // 0x14caf6c: r0 = AllocateContext()
    //     0x14caf6c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14caf70: mov             x1, x0
    // 0x14caf74: ldur            x0, [fp, #-8]
    // 0x14caf78: stur            x1, [fp, #-0x18]
    // 0x14caf7c: StoreField: r1->field_f = r0
    //     0x14caf7c: stur            w0, [x1, #0xf]
    // 0x14caf80: ldur            x0, [fp, #-0x10]
    // 0x14caf84: StoreField: r1->field_13 = r0
    //     0x14caf84: stur            w0, [x1, #0x13]
    // 0x14caf88: r0 = Obx()
    //     0x14caf88: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14caf8c: ldur            x2, [fp, #-0x18]
    // 0x14caf90: r1 = Function '<anonymous closure>':.
    //     0x14caf90: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d28] AnonymousClosure: (0x14cb008), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14caf94: ldr             x1, [x1, #0xd28]
    // 0x14caf98: stur            x0, [fp, #-8]
    // 0x14caf9c: r0 = AllocateClosure()
    //     0x14caf9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cafa0: mov             x1, x0
    // 0x14cafa4: ldur            x0, [fp, #-8]
    // 0x14cafa8: StoreField: r0->field_b = r1
    //     0x14cafa8: stur            w1, [x0, #0xb]
    // 0x14cafac: ldur            x2, [fp, #-0x18]
    // 0x14cafb0: r1 = Function '<anonymous closure>':.
    //     0x14cafb0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d30] AnonymousClosure: (0x146fc60), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cafb4: ldr             x1, [x1, #0xd30]
    // 0x14cafb8: r0 = AllocateClosure()
    //     0x14cafb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cafbc: ldur            x2, [fp, #-0x18]
    // 0x14cafc0: r1 = Function '<anonymous closure>':.
    //     0x14cafc0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d38] AnonymousClosure: (0x146fba8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cafc4: ldr             x1, [x1, #0xd38]
    // 0x14cafc8: stur            x0, [fp, #-0x10]
    // 0x14cafcc: r0 = AllocateClosure()
    //     0x14cafcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cafd0: stur            x0, [fp, #-0x18]
    // 0x14cafd4: r0 = PagingView()
    //     0x14cafd4: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14cafd8: mov             x1, x0
    // 0x14cafdc: ldur            x2, [fp, #-8]
    // 0x14cafe0: ldur            x3, [fp, #-0x18]
    // 0x14cafe4: ldur            x5, [fp, #-0x10]
    // 0x14cafe8: stur            x0, [fp, #-8]
    // 0x14cafec: r0 = PagingView()
    //     0x14cafec: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14caff0: ldur            x0, [fp, #-8]
    // 0x14caff4: LeaveFrame
    //     0x14caff4: mov             SP, fp
    //     0x14caff8: ldp             fp, lr, [SP], #0x10
    // 0x14caffc: ret
    //     0x14caffc: ret             
    // 0x14cb000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cb000: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cb004: b               #0x14caf68
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14cb008, size: 0x29f8
    // 0x14cb008: EnterFrame
    //     0x14cb008: stp             fp, lr, [SP, #-0x10]!
    //     0x14cb00c: mov             fp, SP
    // 0x14cb010: AllocStack(0x88)
    //     0x14cb010: sub             SP, SP, #0x88
    // 0x14cb014: SetupParameters()
    //     0x14cb014: ldr             x0, [fp, #0x10]
    //     0x14cb018: ldur            w2, [x0, #0x17]
    //     0x14cb01c: add             x2, x2, HEAP, lsl #32
    //     0x14cb020: stur            x2, [fp, #-8]
    // 0x14cb024: CheckStackOverflow
    //     0x14cb024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cb028: cmp             SP, x16
    //     0x14cb02c: b.ls            #0x14cd9f8
    // 0x14cb030: LoadField: r1 = r2->field_f
    //     0x14cb030: ldur            w1, [x2, #0xf]
    // 0x14cb034: DecompressPointer r1
    //     0x14cb034: add             x1, x1, HEAP, lsl #32
    // 0x14cb038: r0 = controller()
    //     0x14cb038: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb03c: LoadField: r1 = r0->field_7b
    //     0x14cb03c: ldur            w1, [x0, #0x7b]
    // 0x14cb040: DecompressPointer r1
    //     0x14cb040: add             x1, x1, HEAP, lsl #32
    // 0x14cb044: r0 = value()
    //     0x14cb044: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb048: LoadField: r1 = r0->field_b
    //     0x14cb048: ldur            w1, [x0, #0xb]
    // 0x14cb04c: DecompressPointer r1
    //     0x14cb04c: add             x1, x1, HEAP, lsl #32
    // 0x14cb050: cmp             w1, NULL
    // 0x14cb054: b.ne            #0x14cb074
    // 0x14cb058: r0 = Container()
    //     0x14cb058: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14cb05c: mov             x1, x0
    // 0x14cb060: stur            x0, [fp, #-0x10]
    // 0x14cb064: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cb064: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cb068: r0 = Container()
    //     0x14cb068: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14cb06c: ldur            x0, [fp, #-0x10]
    // 0x14cb070: b               #0x14cd9ec
    // 0x14cb074: ldur            x2, [fp, #-8]
    // 0x14cb078: LoadField: r1 = r2->field_f
    //     0x14cb078: ldur            w1, [x2, #0xf]
    // 0x14cb07c: DecompressPointer r1
    //     0x14cb07c: add             x1, x1, HEAP, lsl #32
    // 0x14cb080: r0 = controller()
    //     0x14cb080: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb084: LoadField: r1 = r0->field_7b
    //     0x14cb084: ldur            w1, [x0, #0x7b]
    // 0x14cb088: DecompressPointer r1
    //     0x14cb088: add             x1, x1, HEAP, lsl #32
    // 0x14cb08c: r0 = value()
    //     0x14cb08c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb090: LoadField: r1 = r0->field_b
    //     0x14cb090: ldur            w1, [x0, #0xb]
    // 0x14cb094: DecompressPointer r1
    //     0x14cb094: add             x1, x1, HEAP, lsl #32
    // 0x14cb098: cmp             w1, NULL
    // 0x14cb09c: b.ne            #0x14cb0a8
    // 0x14cb0a0: r0 = Null
    //     0x14cb0a0: mov             x0, NULL
    // 0x14cb0a4: b               #0x14cb0cc
    // 0x14cb0a8: LoadField: r0 = r1->field_f
    //     0x14cb0a8: ldur            w0, [x1, #0xf]
    // 0x14cb0ac: DecompressPointer r0
    //     0x14cb0ac: add             x0, x0, HEAP, lsl #32
    // 0x14cb0b0: cmp             w0, NULL
    // 0x14cb0b4: b.ne            #0x14cb0c0
    // 0x14cb0b8: r0 = Null
    //     0x14cb0b8: mov             x0, NULL
    // 0x14cb0bc: b               #0x14cb0cc
    // 0x14cb0c0: LoadField: r1 = r0->field_f
    //     0x14cb0c0: ldur            w1, [x0, #0xf]
    // 0x14cb0c4: DecompressPointer r1
    //     0x14cb0c4: add             x1, x1, HEAP, lsl #32
    // 0x14cb0c8: mov             x0, x1
    // 0x14cb0cc: ldur            x2, [fp, #-8]
    // 0x14cb0d0: r1 = LoadClassIdInstr(r0)
    //     0x14cb0d0: ldur            x1, [x0, #-1]
    //     0x14cb0d4: ubfx            x1, x1, #0xc, #0x14
    // 0x14cb0d8: stp             xzr, x0, [SP]
    // 0x14cb0dc: mov             x0, x1
    // 0x14cb0e0: mov             lr, x0
    // 0x14cb0e4: ldr             lr, [x21, lr, lsl #3]
    // 0x14cb0e8: blr             lr
    // 0x14cb0ec: eor             x1, x0, #0x10
    // 0x14cb0f0: stur            x1, [fp, #-0x10]
    // 0x14cb0f4: r0 = Radius()
    //     0x14cb0f4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14cb0f8: d0 = 15.000000
    //     0x14cb0f8: fmov            d0, #15.00000000
    // 0x14cb0fc: stur            x0, [fp, #-0x18]
    // 0x14cb100: StoreField: r0->field_7 = d0
    //     0x14cb100: stur            d0, [x0, #7]
    // 0x14cb104: StoreField: r0->field_f = d0
    //     0x14cb104: stur            d0, [x0, #0xf]
    // 0x14cb108: r0 = BorderRadius()
    //     0x14cb108: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14cb10c: mov             x1, x0
    // 0x14cb110: ldur            x0, [fp, #-0x18]
    // 0x14cb114: stur            x1, [fp, #-0x20]
    // 0x14cb118: StoreField: r1->field_7 = r0
    //     0x14cb118: stur            w0, [x1, #7]
    // 0x14cb11c: StoreField: r1->field_b = r0
    //     0x14cb11c: stur            w0, [x1, #0xb]
    // 0x14cb120: StoreField: r1->field_f = r0
    //     0x14cb120: stur            w0, [x1, #0xf]
    // 0x14cb124: StoreField: r1->field_13 = r0
    //     0x14cb124: stur            w0, [x1, #0x13]
    // 0x14cb128: r0 = BoxDecoration()
    //     0x14cb128: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14cb12c: mov             x2, x0
    // 0x14cb130: r0 = Instance_Color
    //     0x14cb130: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14cb134: stur            x2, [fp, #-0x18]
    // 0x14cb138: StoreField: r2->field_7 = r0
    //     0x14cb138: stur            w0, [x2, #7]
    // 0x14cb13c: ldur            x0, [fp, #-0x20]
    // 0x14cb140: StoreField: r2->field_13 = r0
    //     0x14cb140: stur            w0, [x2, #0x13]
    // 0x14cb144: r0 = Instance_BoxShape
    //     0x14cb144: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14cb148: ldr             x0, [x0, #0x80]
    // 0x14cb14c: StoreField: r2->field_23 = r0
    //     0x14cb14c: stur            w0, [x2, #0x23]
    // 0x14cb150: ldur            x0, [fp, #-8]
    // 0x14cb154: LoadField: r1 = r0->field_f
    //     0x14cb154: ldur            w1, [x0, #0xf]
    // 0x14cb158: DecompressPointer r1
    //     0x14cb158: add             x1, x1, HEAP, lsl #32
    // 0x14cb15c: r0 = controller()
    //     0x14cb15c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb160: LoadField: r1 = r0->field_7b
    //     0x14cb160: ldur            w1, [x0, #0x7b]
    // 0x14cb164: DecompressPointer r1
    //     0x14cb164: add             x1, x1, HEAP, lsl #32
    // 0x14cb168: r0 = value()
    //     0x14cb168: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb16c: LoadField: r1 = r0->field_b
    //     0x14cb16c: ldur            w1, [x0, #0xb]
    // 0x14cb170: DecompressPointer r1
    //     0x14cb170: add             x1, x1, HEAP, lsl #32
    // 0x14cb174: cmp             w1, NULL
    // 0x14cb178: b.ne            #0x14cb184
    // 0x14cb17c: r0 = Null
    //     0x14cb17c: mov             x0, NULL
    // 0x14cb180: b               #0x14cb1c4
    // 0x14cb184: LoadField: r0 = r1->field_f
    //     0x14cb184: ldur            w0, [x1, #0xf]
    // 0x14cb188: DecompressPointer r0
    //     0x14cb188: add             x0, x0, HEAP, lsl #32
    // 0x14cb18c: cmp             w0, NULL
    // 0x14cb190: b.ne            #0x14cb19c
    // 0x14cb194: r0 = Null
    //     0x14cb194: mov             x0, NULL
    // 0x14cb198: b               #0x14cb1c4
    // 0x14cb19c: LoadField: r1 = r0->field_f
    //     0x14cb19c: ldur            w1, [x0, #0xf]
    // 0x14cb1a0: DecompressPointer r1
    //     0x14cb1a0: add             x1, x1, HEAP, lsl #32
    // 0x14cb1a4: r0 = LoadClassIdInstr(r1)
    //     0x14cb1a4: ldur            x0, [x1, #-1]
    //     0x14cb1a8: ubfx            x0, x0, #0xc, #0x14
    // 0x14cb1ac: str             x1, [SP]
    // 0x14cb1b0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x14cb1b0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x14cb1b4: r0 = GDT[cid_x0 + 0x2700]()
    //     0x14cb1b4: movz            x17, #0x2700
    //     0x14cb1b8: add             lr, x0, x17
    //     0x14cb1bc: ldr             lr, [x21, lr, lsl #3]
    //     0x14cb1c0: blr             lr
    // 0x14cb1c4: cmp             w0, NULL
    // 0x14cb1c8: b.ne            #0x14cb1d0
    // 0x14cb1cc: r0 = ""
    //     0x14cb1cc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cb1d0: ldur            x2, [fp, #-8]
    // 0x14cb1d4: stur            x0, [fp, #-0x20]
    // 0x14cb1d8: LoadField: r1 = r2->field_13
    //     0x14cb1d8: ldur            w1, [x2, #0x13]
    // 0x14cb1dc: DecompressPointer r1
    //     0x14cb1dc: add             x1, x1, HEAP, lsl #32
    // 0x14cb1e0: r0 = of()
    //     0x14cb1e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cb1e4: LoadField: r1 = r0->field_87
    //     0x14cb1e4: ldur            w1, [x0, #0x87]
    // 0x14cb1e8: DecompressPointer r1
    //     0x14cb1e8: add             x1, x1, HEAP, lsl #32
    // 0x14cb1ec: LoadField: r0 = r1->field_23
    //     0x14cb1ec: ldur            w0, [x1, #0x23]
    // 0x14cb1f0: DecompressPointer r0
    //     0x14cb1f0: add             x0, x0, HEAP, lsl #32
    // 0x14cb1f4: r16 = Instance_Color
    //     0x14cb1f4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cb1f8: r30 = 32.000000
    //     0x14cb1f8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x14cb1fc: ldr             lr, [lr, #0x848]
    // 0x14cb200: stp             lr, x16, [SP]
    // 0x14cb204: mov             x1, x0
    // 0x14cb208: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14cb208: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14cb20c: ldr             x4, [x4, #0x9b8]
    // 0x14cb210: r0 = copyWith()
    //     0x14cb210: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cb214: stur            x0, [fp, #-0x28]
    // 0x14cb218: r0 = Text()
    //     0x14cb218: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cb21c: mov             x1, x0
    // 0x14cb220: ldur            x0, [fp, #-0x20]
    // 0x14cb224: stur            x1, [fp, #-0x30]
    // 0x14cb228: StoreField: r1->field_b = r0
    //     0x14cb228: stur            w0, [x1, #0xb]
    // 0x14cb22c: ldur            x0, [fp, #-0x28]
    // 0x14cb230: StoreField: r1->field_13 = r0
    //     0x14cb230: stur            w0, [x1, #0x13]
    // 0x14cb234: r0 = Padding()
    //     0x14cb234: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14cb238: mov             x2, x0
    // 0x14cb23c: r0 = Instance_EdgeInsets
    //     0x14cb23c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0x14cb240: ldr             x0, [x0, #0x850]
    // 0x14cb244: stur            x2, [fp, #-0x20]
    // 0x14cb248: StoreField: r2->field_f = r0
    //     0x14cb248: stur            w0, [x2, #0xf]
    // 0x14cb24c: ldur            x0, [fp, #-0x30]
    // 0x14cb250: StoreField: r2->field_b = r0
    //     0x14cb250: stur            w0, [x2, #0xb]
    // 0x14cb254: ldur            x0, [fp, #-8]
    // 0x14cb258: LoadField: r1 = r0->field_f
    //     0x14cb258: ldur            w1, [x0, #0xf]
    // 0x14cb25c: DecompressPointer r1
    //     0x14cb25c: add             x1, x1, HEAP, lsl #32
    // 0x14cb260: r0 = controller()
    //     0x14cb260: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb264: LoadField: r1 = r0->field_7b
    //     0x14cb264: ldur            w1, [x0, #0x7b]
    // 0x14cb268: DecompressPointer r1
    //     0x14cb268: add             x1, x1, HEAP, lsl #32
    // 0x14cb26c: r0 = value()
    //     0x14cb26c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb270: LoadField: r1 = r0->field_b
    //     0x14cb270: ldur            w1, [x0, #0xb]
    // 0x14cb274: DecompressPointer r1
    //     0x14cb274: add             x1, x1, HEAP, lsl #32
    // 0x14cb278: cmp             w1, NULL
    // 0x14cb27c: b.ne            #0x14cb288
    // 0x14cb280: r0 = Null
    //     0x14cb280: mov             x0, NULL
    // 0x14cb284: b               #0x14cb2ac
    // 0x14cb288: LoadField: r0 = r1->field_f
    //     0x14cb288: ldur            w0, [x1, #0xf]
    // 0x14cb28c: DecompressPointer r0
    //     0x14cb28c: add             x0, x0, HEAP, lsl #32
    // 0x14cb290: cmp             w0, NULL
    // 0x14cb294: b.ne            #0x14cb2a0
    // 0x14cb298: r0 = Null
    //     0x14cb298: mov             x0, NULL
    // 0x14cb29c: b               #0x14cb2ac
    // 0x14cb2a0: LoadField: r1 = r0->field_f
    //     0x14cb2a0: ldur            w1, [x0, #0xf]
    // 0x14cb2a4: DecompressPointer r1
    //     0x14cb2a4: add             x1, x1, HEAP, lsl #32
    // 0x14cb2a8: mov             x0, x1
    // 0x14cb2ac: cmp             w0, NULL
    // 0x14cb2b0: b.ne            #0x14cb2bc
    // 0x14cb2b4: d1 = 0.000000
    //     0x14cb2b4: eor             v1.16b, v1.16b, v1.16b
    // 0x14cb2b8: b               #0x14cb2c4
    // 0x14cb2bc: LoadField: d0 = r0->field_7
    //     0x14cb2bc: ldur            d0, [x0, #7]
    // 0x14cb2c0: mov             v1.16b, v0.16b
    // 0x14cb2c4: d0 = 4.000000
    //     0x14cb2c4: fmov            d0, #4.00000000
    // 0x14cb2c8: fcmp            d1, d0
    // 0x14cb2cc: b.lt            #0x14cb2dc
    // 0x14cb2d0: r1 = Instance_Color
    //     0x14cb2d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14cb2d4: ldr             x1, [x1, #0x858]
    // 0x14cb2d8: b               #0x14cb404
    // 0x14cb2dc: ldur            x2, [fp, #-8]
    // 0x14cb2e0: LoadField: r1 = r2->field_f
    //     0x14cb2e0: ldur            w1, [x2, #0xf]
    // 0x14cb2e4: DecompressPointer r1
    //     0x14cb2e4: add             x1, x1, HEAP, lsl #32
    // 0x14cb2e8: r0 = controller()
    //     0x14cb2e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb2ec: LoadField: r1 = r0->field_7b
    //     0x14cb2ec: ldur            w1, [x0, #0x7b]
    // 0x14cb2f0: DecompressPointer r1
    //     0x14cb2f0: add             x1, x1, HEAP, lsl #32
    // 0x14cb2f4: r0 = value()
    //     0x14cb2f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb2f8: LoadField: r1 = r0->field_b
    //     0x14cb2f8: ldur            w1, [x0, #0xb]
    // 0x14cb2fc: DecompressPointer r1
    //     0x14cb2fc: add             x1, x1, HEAP, lsl #32
    // 0x14cb300: cmp             w1, NULL
    // 0x14cb304: b.ne            #0x14cb310
    // 0x14cb308: r0 = Null
    //     0x14cb308: mov             x0, NULL
    // 0x14cb30c: b               #0x14cb334
    // 0x14cb310: LoadField: r0 = r1->field_f
    //     0x14cb310: ldur            w0, [x1, #0xf]
    // 0x14cb314: DecompressPointer r0
    //     0x14cb314: add             x0, x0, HEAP, lsl #32
    // 0x14cb318: cmp             w0, NULL
    // 0x14cb31c: b.ne            #0x14cb328
    // 0x14cb320: r0 = Null
    //     0x14cb320: mov             x0, NULL
    // 0x14cb324: b               #0x14cb334
    // 0x14cb328: LoadField: r1 = r0->field_f
    //     0x14cb328: ldur            w1, [x0, #0xf]
    // 0x14cb32c: DecompressPointer r1
    //     0x14cb32c: add             x1, x1, HEAP, lsl #32
    // 0x14cb330: mov             x0, x1
    // 0x14cb334: cmp             w0, NULL
    // 0x14cb338: b.ne            #0x14cb344
    // 0x14cb33c: d1 = 0.000000
    //     0x14cb33c: eor             v1.16b, v1.16b, v1.16b
    // 0x14cb340: b               #0x14cb34c
    // 0x14cb344: LoadField: d0 = r0->field_7
    //     0x14cb344: ldur            d0, [x0, #7]
    // 0x14cb348: mov             v1.16b, v0.16b
    // 0x14cb34c: d0 = 3.500000
    //     0x14cb34c: fmov            d0, #3.50000000
    // 0x14cb350: fcmp            d1, d0
    // 0x14cb354: b.lt            #0x14cb370
    // 0x14cb358: r1 = Instance_Color
    //     0x14cb358: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14cb35c: ldr             x1, [x1, #0x858]
    // 0x14cb360: d0 = 0.700000
    //     0x14cb360: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14cb364: ldr             d0, [x17, #0xf48]
    // 0x14cb368: r0 = withOpacity()
    //     0x14cb368: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14cb36c: b               #0x14cb400
    // 0x14cb370: ldur            x2, [fp, #-8]
    // 0x14cb374: LoadField: r1 = r2->field_f
    //     0x14cb374: ldur            w1, [x2, #0xf]
    // 0x14cb378: DecompressPointer r1
    //     0x14cb378: add             x1, x1, HEAP, lsl #32
    // 0x14cb37c: r0 = controller()
    //     0x14cb37c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb380: LoadField: r1 = r0->field_7b
    //     0x14cb380: ldur            w1, [x0, #0x7b]
    // 0x14cb384: DecompressPointer r1
    //     0x14cb384: add             x1, x1, HEAP, lsl #32
    // 0x14cb388: r0 = value()
    //     0x14cb388: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb38c: LoadField: r1 = r0->field_b
    //     0x14cb38c: ldur            w1, [x0, #0xb]
    // 0x14cb390: DecompressPointer r1
    //     0x14cb390: add             x1, x1, HEAP, lsl #32
    // 0x14cb394: cmp             w1, NULL
    // 0x14cb398: b.ne            #0x14cb3a4
    // 0x14cb39c: r0 = Null
    //     0x14cb39c: mov             x0, NULL
    // 0x14cb3a0: b               #0x14cb3c8
    // 0x14cb3a4: LoadField: r0 = r1->field_f
    //     0x14cb3a4: ldur            w0, [x1, #0xf]
    // 0x14cb3a8: DecompressPointer r0
    //     0x14cb3a8: add             x0, x0, HEAP, lsl #32
    // 0x14cb3ac: cmp             w0, NULL
    // 0x14cb3b0: b.ne            #0x14cb3bc
    // 0x14cb3b4: r0 = Null
    //     0x14cb3b4: mov             x0, NULL
    // 0x14cb3b8: b               #0x14cb3c8
    // 0x14cb3bc: LoadField: r1 = r0->field_f
    //     0x14cb3bc: ldur            w1, [x0, #0xf]
    // 0x14cb3c0: DecompressPointer r1
    //     0x14cb3c0: add             x1, x1, HEAP, lsl #32
    // 0x14cb3c4: mov             x0, x1
    // 0x14cb3c8: cmp             w0, NULL
    // 0x14cb3cc: b.ne            #0x14cb3d8
    // 0x14cb3d0: d1 = 0.000000
    //     0x14cb3d0: eor             v1.16b, v1.16b, v1.16b
    // 0x14cb3d4: b               #0x14cb3e0
    // 0x14cb3d8: LoadField: d0 = r0->field_7
    //     0x14cb3d8: ldur            d0, [x0, #7]
    // 0x14cb3dc: mov             v1.16b, v0.16b
    // 0x14cb3e0: d0 = 2.000000
    //     0x14cb3e0: fmov            d0, #2.00000000
    // 0x14cb3e4: fcmp            d1, d0
    // 0x14cb3e8: b.lt            #0x14cb3f8
    // 0x14cb3ec: r0 = Instance_Color
    //     0x14cb3ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0x14cb3f0: ldr             x0, [x0, #0x860]
    // 0x14cb3f4: b               #0x14cb400
    // 0x14cb3f8: r0 = Instance_Color
    //     0x14cb3f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14cb3fc: ldr             x0, [x0, #0x50]
    // 0x14cb400: mov             x1, x0
    // 0x14cb404: ldur            x2, [fp, #-8]
    // 0x14cb408: ldur            x0, [fp, #-0x20]
    // 0x14cb40c: stur            x1, [fp, #-0x28]
    // 0x14cb410: r0 = ColorFilter()
    //     0x14cb410: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14cb414: mov             x1, x0
    // 0x14cb418: ldur            x0, [fp, #-0x28]
    // 0x14cb41c: stur            x1, [fp, #-0x30]
    // 0x14cb420: StoreField: r1->field_7 = r0
    //     0x14cb420: stur            w0, [x1, #7]
    // 0x14cb424: r0 = Instance_BlendMode
    //     0x14cb424: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14cb428: ldr             x0, [x0, #0xb30]
    // 0x14cb42c: StoreField: r1->field_b = r0
    //     0x14cb42c: stur            w0, [x1, #0xb]
    // 0x14cb430: r0 = 1
    //     0x14cb430: movz            x0, #0x1
    // 0x14cb434: StoreField: r1->field_13 = r0
    //     0x14cb434: stur            x0, [x1, #0x13]
    // 0x14cb438: r0 = SvgPicture()
    //     0x14cb438: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14cb43c: stur            x0, [fp, #-0x28]
    // 0x14cb440: ldur            x16, [fp, #-0x30]
    // 0x14cb444: str             x16, [SP]
    // 0x14cb448: mov             x1, x0
    // 0x14cb44c: r2 = "assets/images/big_green_star.svg"
    //     0x14cb44c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f868] "assets/images/big_green_star.svg"
    //     0x14cb450: ldr             x2, [x2, #0x868]
    // 0x14cb454: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x14cb454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x14cb458: ldr             x4, [x4, #0xa38]
    // 0x14cb45c: r0 = SvgPicture.asset()
    //     0x14cb45c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14cb460: r1 = Null
    //     0x14cb460: mov             x1, NULL
    // 0x14cb464: r2 = 4
    //     0x14cb464: movz            x2, #0x4
    // 0x14cb468: r0 = AllocateArray()
    //     0x14cb468: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cb46c: mov             x2, x0
    // 0x14cb470: ldur            x0, [fp, #-0x20]
    // 0x14cb474: stur            x2, [fp, #-0x30]
    // 0x14cb478: StoreField: r2->field_f = r0
    //     0x14cb478: stur            w0, [x2, #0xf]
    // 0x14cb47c: ldur            x0, [fp, #-0x28]
    // 0x14cb480: StoreField: r2->field_13 = r0
    //     0x14cb480: stur            w0, [x2, #0x13]
    // 0x14cb484: r1 = <Widget>
    //     0x14cb484: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cb488: r0 = AllocateGrowableArray()
    //     0x14cb488: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cb48c: mov             x1, x0
    // 0x14cb490: ldur            x0, [fp, #-0x30]
    // 0x14cb494: stur            x1, [fp, #-0x20]
    // 0x14cb498: StoreField: r1->field_f = r0
    //     0x14cb498: stur            w0, [x1, #0xf]
    // 0x14cb49c: r2 = 4
    //     0x14cb49c: movz            x2, #0x4
    // 0x14cb4a0: StoreField: r1->field_b = r2
    //     0x14cb4a0: stur            w2, [x1, #0xb]
    // 0x14cb4a4: r0 = Row()
    //     0x14cb4a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14cb4a8: mov             x3, x0
    // 0x14cb4ac: r0 = Instance_Axis
    //     0x14cb4ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14cb4b0: stur            x3, [fp, #-0x28]
    // 0x14cb4b4: StoreField: r3->field_f = r0
    //     0x14cb4b4: stur            w0, [x3, #0xf]
    // 0x14cb4b8: r4 = Instance_MainAxisAlignment
    //     0x14cb4b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cb4bc: ldr             x4, [x4, #0xa08]
    // 0x14cb4c0: StoreField: r3->field_13 = r4
    //     0x14cb4c0: stur            w4, [x3, #0x13]
    // 0x14cb4c4: r5 = Instance_MainAxisSize
    //     0x14cb4c4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cb4c8: ldr             x5, [x5, #0xa10]
    // 0x14cb4cc: ArrayStore: r3[0] = r5  ; List_4
    //     0x14cb4cc: stur            w5, [x3, #0x17]
    // 0x14cb4d0: r6 = Instance_CrossAxisAlignment
    //     0x14cb4d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cb4d4: ldr             x6, [x6, #0xa18]
    // 0x14cb4d8: StoreField: r3->field_1b = r6
    //     0x14cb4d8: stur            w6, [x3, #0x1b]
    // 0x14cb4dc: r7 = Instance_VerticalDirection
    //     0x14cb4dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cb4e0: ldr             x7, [x7, #0xa20]
    // 0x14cb4e4: StoreField: r3->field_23 = r7
    //     0x14cb4e4: stur            w7, [x3, #0x23]
    // 0x14cb4e8: r8 = Instance_Clip
    //     0x14cb4e8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cb4ec: ldr             x8, [x8, #0x38]
    // 0x14cb4f0: StoreField: r3->field_2b = r8
    //     0x14cb4f0: stur            w8, [x3, #0x2b]
    // 0x14cb4f4: StoreField: r3->field_2f = rZR
    //     0x14cb4f4: stur            xzr, [x3, #0x2f]
    // 0x14cb4f8: ldur            x1, [fp, #-0x20]
    // 0x14cb4fc: StoreField: r3->field_b = r1
    //     0x14cb4fc: stur            w1, [x3, #0xb]
    // 0x14cb500: r1 = Null
    //     0x14cb500: mov             x1, NULL
    // 0x14cb504: r2 = 2
    //     0x14cb504: movz            x2, #0x2
    // 0x14cb508: r0 = AllocateArray()
    //     0x14cb508: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cb50c: mov             x2, x0
    // 0x14cb510: ldur            x0, [fp, #-0x28]
    // 0x14cb514: stur            x2, [fp, #-0x20]
    // 0x14cb518: StoreField: r2->field_f = r0
    //     0x14cb518: stur            w0, [x2, #0xf]
    // 0x14cb51c: r1 = <Widget>
    //     0x14cb51c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cb520: r0 = AllocateGrowableArray()
    //     0x14cb520: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cb524: mov             x2, x0
    // 0x14cb528: ldur            x0, [fp, #-0x20]
    // 0x14cb52c: stur            x2, [fp, #-0x28]
    // 0x14cb530: StoreField: r2->field_f = r0
    //     0x14cb530: stur            w0, [x2, #0xf]
    // 0x14cb534: r0 = 2
    //     0x14cb534: movz            x0, #0x2
    // 0x14cb538: StoreField: r2->field_b = r0
    //     0x14cb538: stur            w0, [x2, #0xb]
    // 0x14cb53c: ldur            x3, [fp, #-8]
    // 0x14cb540: LoadField: r1 = r3->field_f
    //     0x14cb540: ldur            w1, [x3, #0xf]
    // 0x14cb544: DecompressPointer r1
    //     0x14cb544: add             x1, x1, HEAP, lsl #32
    // 0x14cb548: r0 = controller()
    //     0x14cb548: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb54c: LoadField: r1 = r0->field_7b
    //     0x14cb54c: ldur            w1, [x0, #0x7b]
    // 0x14cb550: DecompressPointer r1
    //     0x14cb550: add             x1, x1, HEAP, lsl #32
    // 0x14cb554: r0 = value()
    //     0x14cb554: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb558: LoadField: r1 = r0->field_b
    //     0x14cb558: ldur            w1, [x0, #0xb]
    // 0x14cb55c: DecompressPointer r1
    //     0x14cb55c: add             x1, x1, HEAP, lsl #32
    // 0x14cb560: cmp             w1, NULL
    // 0x14cb564: b.eq            #0x14cb584
    // 0x14cb568: LoadField: r0 = r1->field_f
    //     0x14cb568: ldur            w0, [x1, #0xf]
    // 0x14cb56c: DecompressPointer r0
    //     0x14cb56c: add             x0, x0, HEAP, lsl #32
    // 0x14cb570: cmp             w0, NULL
    // 0x14cb574: b.eq            #0x14cb584
    // 0x14cb578: LoadField: r1 = r0->field_13
    //     0x14cb578: ldur            w1, [x0, #0x13]
    // 0x14cb57c: DecompressPointer r1
    //     0x14cb57c: add             x1, x1, HEAP, lsl #32
    // 0x14cb580: cbz             w1, #0x14cb714
    // 0x14cb584: ldur            x2, [fp, #-8]
    // 0x14cb588: LoadField: r1 = r2->field_f
    //     0x14cb588: ldur            w1, [x2, #0xf]
    // 0x14cb58c: DecompressPointer r1
    //     0x14cb58c: add             x1, x1, HEAP, lsl #32
    // 0x14cb590: r0 = controller()
    //     0x14cb590: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb594: LoadField: r1 = r0->field_7b
    //     0x14cb594: ldur            w1, [x0, #0x7b]
    // 0x14cb598: DecompressPointer r1
    //     0x14cb598: add             x1, x1, HEAP, lsl #32
    // 0x14cb59c: r0 = value()
    //     0x14cb59c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb5a0: LoadField: r1 = r0->field_b
    //     0x14cb5a0: ldur            w1, [x0, #0xb]
    // 0x14cb5a4: DecompressPointer r1
    //     0x14cb5a4: add             x1, x1, HEAP, lsl #32
    // 0x14cb5a8: cmp             w1, NULL
    // 0x14cb5ac: b.ne            #0x14cb5b8
    // 0x14cb5b0: r0 = Null
    //     0x14cb5b0: mov             x0, NULL
    // 0x14cb5b4: b               #0x14cb5dc
    // 0x14cb5b8: LoadField: r0 = r1->field_f
    //     0x14cb5b8: ldur            w0, [x1, #0xf]
    // 0x14cb5bc: DecompressPointer r0
    //     0x14cb5bc: add             x0, x0, HEAP, lsl #32
    // 0x14cb5c0: cmp             w0, NULL
    // 0x14cb5c4: b.ne            #0x14cb5d0
    // 0x14cb5c8: r0 = Null
    //     0x14cb5c8: mov             x0, NULL
    // 0x14cb5cc: b               #0x14cb5dc
    // 0x14cb5d0: LoadField: r1 = r0->field_13
    //     0x14cb5d0: ldur            w1, [x0, #0x13]
    // 0x14cb5d4: DecompressPointer r1
    //     0x14cb5d4: add             x1, x1, HEAP, lsl #32
    // 0x14cb5d8: mov             x0, x1
    // 0x14cb5dc: cmp             w0, NULL
    // 0x14cb5e0: b.ne            #0x14cb5ec
    // 0x14cb5e4: r4 = ""
    //     0x14cb5e4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cb5e8: b               #0x14cb5f0
    // 0x14cb5ec: mov             x4, x0
    // 0x14cb5f0: ldur            x0, [fp, #-8]
    // 0x14cb5f4: ldur            x3, [fp, #-0x28]
    // 0x14cb5f8: stur            x4, [fp, #-0x20]
    // 0x14cb5fc: r1 = Null
    //     0x14cb5fc: mov             x1, NULL
    // 0x14cb600: r2 = 4
    //     0x14cb600: movz            x2, #0x4
    // 0x14cb604: r0 = AllocateArray()
    //     0x14cb604: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cb608: mov             x1, x0
    // 0x14cb60c: ldur            x0, [fp, #-0x20]
    // 0x14cb610: StoreField: r1->field_f = r0
    //     0x14cb610: stur            w0, [x1, #0xf]
    // 0x14cb614: r16 = " Ratings"
    //     0x14cb614: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f870] " Ratings"
    //     0x14cb618: ldr             x16, [x16, #0x870]
    // 0x14cb61c: StoreField: r1->field_13 = r16
    //     0x14cb61c: stur            w16, [x1, #0x13]
    // 0x14cb620: str             x1, [SP]
    // 0x14cb624: r0 = _interpolate()
    //     0x14cb624: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14cb628: ldur            x2, [fp, #-8]
    // 0x14cb62c: stur            x0, [fp, #-0x20]
    // 0x14cb630: LoadField: r1 = r2->field_13
    //     0x14cb630: ldur            w1, [x2, #0x13]
    // 0x14cb634: DecompressPointer r1
    //     0x14cb634: add             x1, x1, HEAP, lsl #32
    // 0x14cb638: r0 = of()
    //     0x14cb638: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cb63c: LoadField: r1 = r0->field_87
    //     0x14cb63c: ldur            w1, [x0, #0x87]
    // 0x14cb640: DecompressPointer r1
    //     0x14cb640: add             x1, x1, HEAP, lsl #32
    // 0x14cb644: LoadField: r0 = r1->field_2b
    //     0x14cb644: ldur            w0, [x1, #0x2b]
    // 0x14cb648: DecompressPointer r0
    //     0x14cb648: add             x0, x0, HEAP, lsl #32
    // 0x14cb64c: stur            x0, [fp, #-0x30]
    // 0x14cb650: r1 = Instance_Color
    //     0x14cb650: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cb654: d0 = 0.850000
    //     0x14cb654: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0x14cb658: ldr             d0, [x17, #0x878]
    // 0x14cb65c: r0 = withOpacity()
    //     0x14cb65c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14cb660: r16 = 10.000000
    //     0x14cb660: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x14cb664: stp             x0, x16, [SP]
    // 0x14cb668: ldur            x1, [fp, #-0x30]
    // 0x14cb66c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14cb66c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14cb670: ldr             x4, [x4, #0xaa0]
    // 0x14cb674: r0 = copyWith()
    //     0x14cb674: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cb678: stur            x0, [fp, #-0x30]
    // 0x14cb67c: r0 = Text()
    //     0x14cb67c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cb680: mov             x2, x0
    // 0x14cb684: ldur            x0, [fp, #-0x20]
    // 0x14cb688: stur            x2, [fp, #-0x40]
    // 0x14cb68c: StoreField: r2->field_b = r0
    //     0x14cb68c: stur            w0, [x2, #0xb]
    // 0x14cb690: ldur            x0, [fp, #-0x30]
    // 0x14cb694: StoreField: r2->field_13 = r0
    //     0x14cb694: stur            w0, [x2, #0x13]
    // 0x14cb698: ldur            x0, [fp, #-0x28]
    // 0x14cb69c: LoadField: r1 = r0->field_b
    //     0x14cb69c: ldur            w1, [x0, #0xb]
    // 0x14cb6a0: LoadField: r3 = r0->field_f
    //     0x14cb6a0: ldur            w3, [x0, #0xf]
    // 0x14cb6a4: DecompressPointer r3
    //     0x14cb6a4: add             x3, x3, HEAP, lsl #32
    // 0x14cb6a8: LoadField: r4 = r3->field_b
    //     0x14cb6a8: ldur            w4, [x3, #0xb]
    // 0x14cb6ac: r3 = LoadInt32Instr(r1)
    //     0x14cb6ac: sbfx            x3, x1, #1, #0x1f
    // 0x14cb6b0: stur            x3, [fp, #-0x38]
    // 0x14cb6b4: r1 = LoadInt32Instr(r4)
    //     0x14cb6b4: sbfx            x1, x4, #1, #0x1f
    // 0x14cb6b8: cmp             x3, x1
    // 0x14cb6bc: b.ne            #0x14cb6c8
    // 0x14cb6c0: mov             x1, x0
    // 0x14cb6c4: r0 = _growToNextCapacity()
    //     0x14cb6c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cb6c8: ldur            x2, [fp, #-0x28]
    // 0x14cb6cc: ldur            x3, [fp, #-0x38]
    // 0x14cb6d0: add             x0, x3, #1
    // 0x14cb6d4: lsl             x1, x0, #1
    // 0x14cb6d8: StoreField: r2->field_b = r1
    //     0x14cb6d8: stur            w1, [x2, #0xb]
    // 0x14cb6dc: LoadField: r1 = r2->field_f
    //     0x14cb6dc: ldur            w1, [x2, #0xf]
    // 0x14cb6e0: DecompressPointer r1
    //     0x14cb6e0: add             x1, x1, HEAP, lsl #32
    // 0x14cb6e4: ldur            x0, [fp, #-0x40]
    // 0x14cb6e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14cb6e8: add             x25, x1, x3, lsl #2
    //     0x14cb6ec: add             x25, x25, #0xf
    //     0x14cb6f0: str             w0, [x25]
    //     0x14cb6f4: tbz             w0, #0, #0x14cb710
    //     0x14cb6f8: ldurb           w16, [x1, #-1]
    //     0x14cb6fc: ldurb           w17, [x0, #-1]
    //     0x14cb700: and             x16, x17, x16, lsr #2
    //     0x14cb704: tst             x16, HEAP, lsr #32
    //     0x14cb708: b.eq            #0x14cb710
    //     0x14cb70c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cb710: b               #0x14cb718
    // 0x14cb714: ldur            x2, [fp, #-0x28]
    // 0x14cb718: ldur            x0, [fp, #-8]
    // 0x14cb71c: LoadField: r1 = r0->field_f
    //     0x14cb71c: ldur            w1, [x0, #0xf]
    // 0x14cb720: DecompressPointer r1
    //     0x14cb720: add             x1, x1, HEAP, lsl #32
    // 0x14cb724: r0 = controller()
    //     0x14cb724: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb728: LoadField: r1 = r0->field_7b
    //     0x14cb728: ldur            w1, [x0, #0x7b]
    // 0x14cb72c: DecompressPointer r1
    //     0x14cb72c: add             x1, x1, HEAP, lsl #32
    // 0x14cb730: r0 = value()
    //     0x14cb730: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb734: LoadField: r1 = r0->field_b
    //     0x14cb734: ldur            w1, [x0, #0xb]
    // 0x14cb738: DecompressPointer r1
    //     0x14cb738: add             x1, x1, HEAP, lsl #32
    // 0x14cb73c: cmp             w1, NULL
    // 0x14cb740: b.eq            #0x14cb760
    // 0x14cb744: LoadField: r0 = r1->field_f
    //     0x14cb744: ldur            w0, [x1, #0xf]
    // 0x14cb748: DecompressPointer r0
    //     0x14cb748: add             x0, x0, HEAP, lsl #32
    // 0x14cb74c: cmp             w0, NULL
    // 0x14cb750: b.eq            #0x14cb760
    // 0x14cb754: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14cb754: ldur            w1, [x0, #0x17]
    // 0x14cb758: DecompressPointer r1
    //     0x14cb758: add             x1, x1, HEAP, lsl #32
    // 0x14cb75c: cbz             w1, #0x14cb918
    // 0x14cb760: ldur            x0, [fp, #-8]
    // 0x14cb764: r1 = Null
    //     0x14cb764: mov             x1, NULL
    // 0x14cb768: r2 = 6
    //     0x14cb768: movz            x2, #0x6
    // 0x14cb76c: r0 = AllocateArray()
    //     0x14cb76c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cb770: stur            x0, [fp, #-0x20]
    // 0x14cb774: r16 = "& "
    //     0x14cb774: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f880] "& "
    //     0x14cb778: ldr             x16, [x16, #0x880]
    // 0x14cb77c: StoreField: r0->field_f = r16
    //     0x14cb77c: stur            w16, [x0, #0xf]
    // 0x14cb780: ldur            x2, [fp, #-8]
    // 0x14cb784: LoadField: r1 = r2->field_f
    //     0x14cb784: ldur            w1, [x2, #0xf]
    // 0x14cb788: DecompressPointer r1
    //     0x14cb788: add             x1, x1, HEAP, lsl #32
    // 0x14cb78c: r0 = controller()
    //     0x14cb78c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb790: LoadField: r1 = r0->field_7b
    //     0x14cb790: ldur            w1, [x0, #0x7b]
    // 0x14cb794: DecompressPointer r1
    //     0x14cb794: add             x1, x1, HEAP, lsl #32
    // 0x14cb798: r0 = value()
    //     0x14cb798: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb79c: LoadField: r1 = r0->field_b
    //     0x14cb79c: ldur            w1, [x0, #0xb]
    // 0x14cb7a0: DecompressPointer r1
    //     0x14cb7a0: add             x1, x1, HEAP, lsl #32
    // 0x14cb7a4: cmp             w1, NULL
    // 0x14cb7a8: b.ne            #0x14cb7b4
    // 0x14cb7ac: r0 = Null
    //     0x14cb7ac: mov             x0, NULL
    // 0x14cb7b0: b               #0x14cb7d8
    // 0x14cb7b4: LoadField: r0 = r1->field_f
    //     0x14cb7b4: ldur            w0, [x1, #0xf]
    // 0x14cb7b8: DecompressPointer r0
    //     0x14cb7b8: add             x0, x0, HEAP, lsl #32
    // 0x14cb7bc: cmp             w0, NULL
    // 0x14cb7c0: b.ne            #0x14cb7cc
    // 0x14cb7c4: r0 = Null
    //     0x14cb7c4: mov             x0, NULL
    // 0x14cb7c8: b               #0x14cb7d8
    // 0x14cb7cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14cb7cc: ldur            w1, [x0, #0x17]
    // 0x14cb7d0: DecompressPointer r1
    //     0x14cb7d0: add             x1, x1, HEAP, lsl #32
    // 0x14cb7d4: mov             x0, x1
    // 0x14cb7d8: cmp             w0, NULL
    // 0x14cb7dc: b.ne            #0x14cb7e4
    // 0x14cb7e0: r0 = ""
    //     0x14cb7e0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cb7e4: ldur            x3, [fp, #-8]
    // 0x14cb7e8: ldur            x2, [fp, #-0x20]
    // 0x14cb7ec: ldur            x4, [fp, #-0x28]
    // 0x14cb7f0: mov             x1, x2
    // 0x14cb7f4: ArrayStore: r1[1] = r0  ; List_4
    //     0x14cb7f4: add             x25, x1, #0x13
    //     0x14cb7f8: str             w0, [x25]
    //     0x14cb7fc: tbz             w0, #0, #0x14cb818
    //     0x14cb800: ldurb           w16, [x1, #-1]
    //     0x14cb804: ldurb           w17, [x0, #-1]
    //     0x14cb808: and             x16, x17, x16, lsr #2
    //     0x14cb80c: tst             x16, HEAP, lsr #32
    //     0x14cb810: b.eq            #0x14cb818
    //     0x14cb814: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cb818: r16 = " Reviews"
    //     0x14cb818: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f888] " Reviews"
    //     0x14cb81c: ldr             x16, [x16, #0x888]
    // 0x14cb820: ArrayStore: r2[0] = r16  ; List_4
    //     0x14cb820: stur            w16, [x2, #0x17]
    // 0x14cb824: str             x2, [SP]
    // 0x14cb828: r0 = _interpolate()
    //     0x14cb828: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14cb82c: ldur            x2, [fp, #-8]
    // 0x14cb830: stur            x0, [fp, #-0x20]
    // 0x14cb834: LoadField: r1 = r2->field_13
    //     0x14cb834: ldur            w1, [x2, #0x13]
    // 0x14cb838: DecompressPointer r1
    //     0x14cb838: add             x1, x1, HEAP, lsl #32
    // 0x14cb83c: r0 = of()
    //     0x14cb83c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cb840: LoadField: r1 = r0->field_87
    //     0x14cb840: ldur            w1, [x0, #0x87]
    // 0x14cb844: DecompressPointer r1
    //     0x14cb844: add             x1, x1, HEAP, lsl #32
    // 0x14cb848: LoadField: r0 = r1->field_2b
    //     0x14cb848: ldur            w0, [x1, #0x2b]
    // 0x14cb84c: DecompressPointer r0
    //     0x14cb84c: add             x0, x0, HEAP, lsl #32
    // 0x14cb850: stur            x0, [fp, #-0x30]
    // 0x14cb854: r1 = Instance_Color
    //     0x14cb854: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cb858: d0 = 0.850000
    //     0x14cb858: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0x14cb85c: ldr             d0, [x17, #0x878]
    // 0x14cb860: r0 = withOpacity()
    //     0x14cb860: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14cb864: r16 = 10.000000
    //     0x14cb864: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x14cb868: stp             x0, x16, [SP]
    // 0x14cb86c: ldur            x1, [fp, #-0x30]
    // 0x14cb870: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14cb870: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14cb874: ldr             x4, [x4, #0xaa0]
    // 0x14cb878: r0 = copyWith()
    //     0x14cb878: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cb87c: stur            x0, [fp, #-0x30]
    // 0x14cb880: r0 = Text()
    //     0x14cb880: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cb884: mov             x2, x0
    // 0x14cb888: ldur            x0, [fp, #-0x20]
    // 0x14cb88c: stur            x2, [fp, #-0x40]
    // 0x14cb890: StoreField: r2->field_b = r0
    //     0x14cb890: stur            w0, [x2, #0xb]
    // 0x14cb894: ldur            x0, [fp, #-0x30]
    // 0x14cb898: StoreField: r2->field_13 = r0
    //     0x14cb898: stur            w0, [x2, #0x13]
    // 0x14cb89c: ldur            x0, [fp, #-0x28]
    // 0x14cb8a0: LoadField: r1 = r0->field_b
    //     0x14cb8a0: ldur            w1, [x0, #0xb]
    // 0x14cb8a4: LoadField: r3 = r0->field_f
    //     0x14cb8a4: ldur            w3, [x0, #0xf]
    // 0x14cb8a8: DecompressPointer r3
    //     0x14cb8a8: add             x3, x3, HEAP, lsl #32
    // 0x14cb8ac: LoadField: r4 = r3->field_b
    //     0x14cb8ac: ldur            w4, [x3, #0xb]
    // 0x14cb8b0: r3 = LoadInt32Instr(r1)
    //     0x14cb8b0: sbfx            x3, x1, #1, #0x1f
    // 0x14cb8b4: stur            x3, [fp, #-0x38]
    // 0x14cb8b8: r1 = LoadInt32Instr(r4)
    //     0x14cb8b8: sbfx            x1, x4, #1, #0x1f
    // 0x14cb8bc: cmp             x3, x1
    // 0x14cb8c0: b.ne            #0x14cb8cc
    // 0x14cb8c4: mov             x1, x0
    // 0x14cb8c8: r0 = _growToNextCapacity()
    //     0x14cb8c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cb8cc: ldur            x2, [fp, #-0x28]
    // 0x14cb8d0: ldur            x3, [fp, #-0x38]
    // 0x14cb8d4: add             x0, x3, #1
    // 0x14cb8d8: lsl             x1, x0, #1
    // 0x14cb8dc: StoreField: r2->field_b = r1
    //     0x14cb8dc: stur            w1, [x2, #0xb]
    // 0x14cb8e0: LoadField: r1 = r2->field_f
    //     0x14cb8e0: ldur            w1, [x2, #0xf]
    // 0x14cb8e4: DecompressPointer r1
    //     0x14cb8e4: add             x1, x1, HEAP, lsl #32
    // 0x14cb8e8: ldur            x0, [fp, #-0x40]
    // 0x14cb8ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14cb8ec: add             x25, x1, x3, lsl #2
    //     0x14cb8f0: add             x25, x25, #0xf
    //     0x14cb8f4: str             w0, [x25]
    //     0x14cb8f8: tbz             w0, #0, #0x14cb914
    //     0x14cb8fc: ldurb           w16, [x1, #-1]
    //     0x14cb900: ldurb           w17, [x0, #-1]
    //     0x14cb904: and             x16, x17, x16, lsr #2
    //     0x14cb908: tst             x16, HEAP, lsr #32
    //     0x14cb90c: b.eq            #0x14cb914
    //     0x14cb910: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cb914: b               #0x14cb91c
    // 0x14cb918: ldur            x2, [fp, #-0x28]
    // 0x14cb91c: ldur            x0, [fp, #-8]
    // 0x14cb920: r0 = Column()
    //     0x14cb920: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14cb924: mov             x2, x0
    // 0x14cb928: r0 = Instance_Axis
    //     0x14cb928: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cb92c: stur            x2, [fp, #-0x30]
    // 0x14cb930: StoreField: r2->field_f = r0
    //     0x14cb930: stur            w0, [x2, #0xf]
    // 0x14cb934: r3 = Instance_MainAxisAlignment
    //     0x14cb934: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cb938: ldr             x3, [x3, #0xa08]
    // 0x14cb93c: StoreField: r2->field_13 = r3
    //     0x14cb93c: stur            w3, [x2, #0x13]
    // 0x14cb940: r4 = Instance_MainAxisSize
    //     0x14cb940: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cb944: ldr             x4, [x4, #0xa10]
    // 0x14cb948: ArrayStore: r2[0] = r4  ; List_4
    //     0x14cb948: stur            w4, [x2, #0x17]
    // 0x14cb94c: r5 = Instance_CrossAxisAlignment
    //     0x14cb94c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14cb950: ldr             x5, [x5, #0x890]
    // 0x14cb954: StoreField: r2->field_1b = r5
    //     0x14cb954: stur            w5, [x2, #0x1b]
    // 0x14cb958: r6 = Instance_VerticalDirection
    //     0x14cb958: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cb95c: ldr             x6, [x6, #0xa20]
    // 0x14cb960: StoreField: r2->field_23 = r6
    //     0x14cb960: stur            w6, [x2, #0x23]
    // 0x14cb964: r7 = Instance_Clip
    //     0x14cb964: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cb968: ldr             x7, [x7, #0x38]
    // 0x14cb96c: StoreField: r2->field_2b = r7
    //     0x14cb96c: stur            w7, [x2, #0x2b]
    // 0x14cb970: StoreField: r2->field_2f = rZR
    //     0x14cb970: stur            xzr, [x2, #0x2f]
    // 0x14cb974: ldur            x1, [fp, #-0x28]
    // 0x14cb978: StoreField: r2->field_b = r1
    //     0x14cb978: stur            w1, [x2, #0xb]
    // 0x14cb97c: ldur            x8, [fp, #-8]
    // 0x14cb980: LoadField: r9 = r8->field_f
    //     0x14cb980: ldur            w9, [x8, #0xf]
    // 0x14cb984: DecompressPointer r9
    //     0x14cb984: add             x9, x9, HEAP, lsl #32
    // 0x14cb988: stur            x9, [fp, #-0x28]
    // 0x14cb98c: LoadField: r10 = r8->field_13
    //     0x14cb98c: ldur            w10, [x8, #0x13]
    // 0x14cb990: DecompressPointer r10
    //     0x14cb990: add             x10, x10, HEAP, lsl #32
    // 0x14cb994: mov             x1, x9
    // 0x14cb998: stur            x10, [fp, #-0x20]
    // 0x14cb99c: r0 = controller()
    //     0x14cb99c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cb9a0: LoadField: r1 = r0->field_7b
    //     0x14cb9a0: ldur            w1, [x0, #0x7b]
    // 0x14cb9a4: DecompressPointer r1
    //     0x14cb9a4: add             x1, x1, HEAP, lsl #32
    // 0x14cb9a8: r0 = value()
    //     0x14cb9a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cb9ac: LoadField: r1 = r0->field_b
    //     0x14cb9ac: ldur            w1, [x0, #0xb]
    // 0x14cb9b0: DecompressPointer r1
    //     0x14cb9b0: add             x1, x1, HEAP, lsl #32
    // 0x14cb9b4: cmp             w1, NULL
    // 0x14cb9b8: b.ne            #0x14cb9c4
    // 0x14cb9bc: r0 = Null
    //     0x14cb9bc: mov             x0, NULL
    // 0x14cb9c0: b               #0x14cb9fc
    // 0x14cb9c4: LoadField: r0 = r1->field_f
    //     0x14cb9c4: ldur            w0, [x1, #0xf]
    // 0x14cb9c8: DecompressPointer r0
    //     0x14cb9c8: add             x0, x0, HEAP, lsl #32
    // 0x14cb9cc: cmp             w0, NULL
    // 0x14cb9d0: b.ne            #0x14cb9dc
    // 0x14cb9d4: r0 = Null
    //     0x14cb9d4: mov             x0, NULL
    // 0x14cb9d8: b               #0x14cb9fc
    // 0x14cb9dc: LoadField: r1 = r0->field_1b
    //     0x14cb9dc: ldur            w1, [x0, #0x1b]
    // 0x14cb9e0: DecompressPointer r1
    //     0x14cb9e0: add             x1, x1, HEAP, lsl #32
    // 0x14cb9e4: cmp             w1, NULL
    // 0x14cb9e8: b.ne            #0x14cb9f4
    // 0x14cb9ec: r0 = Null
    //     0x14cb9ec: mov             x0, NULL
    // 0x14cb9f0: b               #0x14cb9fc
    // 0x14cb9f4: LoadField: r0 = r1->field_7
    //     0x14cb9f4: ldur            w0, [x1, #7]
    // 0x14cb9f8: DecompressPointer r0
    //     0x14cb9f8: add             x0, x0, HEAP, lsl #32
    // 0x14cb9fc: cmp             w0, NULL
    // 0x14cba00: b.ne            #0x14cba0c
    // 0x14cba04: r0 = 0
    //     0x14cba04: movz            x0, #0
    // 0x14cba08: b               #0x14cba1c
    // 0x14cba0c: r1 = LoadInt32Instr(r0)
    //     0x14cba0c: sbfx            x1, x0, #1, #0x1f
    //     0x14cba10: tbz             w0, #0, #0x14cba18
    //     0x14cba14: ldur            x1, [x0, #7]
    // 0x14cba18: mov             x0, x1
    // 0x14cba1c: ldur            x2, [fp, #-8]
    // 0x14cba20: stur            x0, [fp, #-0x38]
    // 0x14cba24: LoadField: r1 = r2->field_f
    //     0x14cba24: ldur            w1, [x2, #0xf]
    // 0x14cba28: DecompressPointer r1
    //     0x14cba28: add             x1, x1, HEAP, lsl #32
    // 0x14cba2c: r0 = controller()
    //     0x14cba2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cba30: LoadField: r1 = r0->field_7b
    //     0x14cba30: ldur            w1, [x0, #0x7b]
    // 0x14cba34: DecompressPointer r1
    //     0x14cba34: add             x1, x1, HEAP, lsl #32
    // 0x14cba38: r0 = value()
    //     0x14cba38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cba3c: LoadField: r1 = r0->field_b
    //     0x14cba3c: ldur            w1, [x0, #0xb]
    // 0x14cba40: DecompressPointer r1
    //     0x14cba40: add             x1, x1, HEAP, lsl #32
    // 0x14cba44: cmp             w1, NULL
    // 0x14cba48: b.ne            #0x14cba54
    // 0x14cba4c: r0 = Null
    //     0x14cba4c: mov             x0, NULL
    // 0x14cba50: b               #0x14cba8c
    // 0x14cba54: LoadField: r0 = r1->field_f
    //     0x14cba54: ldur            w0, [x1, #0xf]
    // 0x14cba58: DecompressPointer r0
    //     0x14cba58: add             x0, x0, HEAP, lsl #32
    // 0x14cba5c: cmp             w0, NULL
    // 0x14cba60: b.ne            #0x14cba6c
    // 0x14cba64: r0 = Null
    //     0x14cba64: mov             x0, NULL
    // 0x14cba68: b               #0x14cba8c
    // 0x14cba6c: LoadField: r1 = r0->field_1b
    //     0x14cba6c: ldur            w1, [x0, #0x1b]
    // 0x14cba70: DecompressPointer r1
    //     0x14cba70: add             x1, x1, HEAP, lsl #32
    // 0x14cba74: cmp             w1, NULL
    // 0x14cba78: b.ne            #0x14cba84
    // 0x14cba7c: r0 = Null
    //     0x14cba7c: mov             x0, NULL
    // 0x14cba80: b               #0x14cba8c
    // 0x14cba84: LoadField: r0 = r1->field_7
    //     0x14cba84: ldur            w0, [x1, #7]
    // 0x14cba88: DecompressPointer r0
    //     0x14cba88: add             x0, x0, HEAP, lsl #32
    // 0x14cba8c: cmp             w0, NULL
    // 0x14cba90: b.ne            #0x14cba9c
    // 0x14cba94: r0 = 0
    //     0x14cba94: movz            x0, #0
    // 0x14cba98: b               #0x14cbaac
    // 0x14cba9c: r1 = LoadInt32Instr(r0)
    //     0x14cba9c: sbfx            x1, x0, #1, #0x1f
    //     0x14cbaa0: tbz             w0, #0, #0x14cbaa8
    //     0x14cbaa4: ldur            x1, [x0, #7]
    // 0x14cbaa8: mov             x0, x1
    // 0x14cbaac: ldur            x2, [fp, #-8]
    // 0x14cbab0: stur            x0, [fp, #-0x48]
    // 0x14cbab4: LoadField: r1 = r2->field_f
    //     0x14cbab4: ldur            w1, [x2, #0xf]
    // 0x14cbab8: DecompressPointer r1
    //     0x14cbab8: add             x1, x1, HEAP, lsl #32
    // 0x14cbabc: r0 = controller()
    //     0x14cbabc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbac0: LoadField: r1 = r0->field_7b
    //     0x14cbac0: ldur            w1, [x0, #0x7b]
    // 0x14cbac4: DecompressPointer r1
    //     0x14cbac4: add             x1, x1, HEAP, lsl #32
    // 0x14cbac8: r0 = value()
    //     0x14cbac8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbacc: LoadField: r1 = r0->field_b
    //     0x14cbacc: ldur            w1, [x0, #0xb]
    // 0x14cbad0: DecompressPointer r1
    //     0x14cbad0: add             x1, x1, HEAP, lsl #32
    // 0x14cbad4: cmp             w1, NULL
    // 0x14cbad8: b.ne            #0x14cbae4
    // 0x14cbadc: r0 = Null
    //     0x14cbadc: mov             x0, NULL
    // 0x14cbae0: b               #0x14cbb1c
    // 0x14cbae4: LoadField: r0 = r1->field_f
    //     0x14cbae4: ldur            w0, [x1, #0xf]
    // 0x14cbae8: DecompressPointer r0
    //     0x14cbae8: add             x0, x0, HEAP, lsl #32
    // 0x14cbaec: cmp             w0, NULL
    // 0x14cbaf0: b.ne            #0x14cbafc
    // 0x14cbaf4: r0 = Null
    //     0x14cbaf4: mov             x0, NULL
    // 0x14cbaf8: b               #0x14cbb1c
    // 0x14cbafc: LoadField: r1 = r0->field_1b
    //     0x14cbafc: ldur            w1, [x0, #0x1b]
    // 0x14cbb00: DecompressPointer r1
    //     0x14cbb00: add             x1, x1, HEAP, lsl #32
    // 0x14cbb04: cmp             w1, NULL
    // 0x14cbb08: b.ne            #0x14cbb14
    // 0x14cbb0c: r0 = Null
    //     0x14cbb0c: mov             x0, NULL
    // 0x14cbb10: b               #0x14cbb1c
    // 0x14cbb14: LoadField: r0 = r1->field_b
    //     0x14cbb14: ldur            w0, [x1, #0xb]
    // 0x14cbb18: DecompressPointer r0
    //     0x14cbb18: add             x0, x0, HEAP, lsl #32
    // 0x14cbb1c: cmp             w0, NULL
    // 0x14cbb20: b.ne            #0x14cbb2c
    // 0x14cbb24: r1 = 0
    //     0x14cbb24: movz            x1, #0
    // 0x14cbb28: b               #0x14cbb38
    // 0x14cbb2c: r1 = LoadInt32Instr(r0)
    //     0x14cbb2c: sbfx            x1, x0, #1, #0x1f
    //     0x14cbb30: tbz             w0, #0, #0x14cbb38
    //     0x14cbb34: ldur            x1, [x0, #7]
    // 0x14cbb38: ldur            x2, [fp, #-8]
    // 0x14cbb3c: ldur            x0, [fp, #-0x48]
    // 0x14cbb40: add             x3, x0, x1
    // 0x14cbb44: stur            x3, [fp, #-0x50]
    // 0x14cbb48: LoadField: r1 = r2->field_f
    //     0x14cbb48: ldur            w1, [x2, #0xf]
    // 0x14cbb4c: DecompressPointer r1
    //     0x14cbb4c: add             x1, x1, HEAP, lsl #32
    // 0x14cbb50: r0 = controller()
    //     0x14cbb50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbb54: LoadField: r1 = r0->field_7b
    //     0x14cbb54: ldur            w1, [x0, #0x7b]
    // 0x14cbb58: DecompressPointer r1
    //     0x14cbb58: add             x1, x1, HEAP, lsl #32
    // 0x14cbb5c: r0 = value()
    //     0x14cbb5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbb60: LoadField: r1 = r0->field_b
    //     0x14cbb60: ldur            w1, [x0, #0xb]
    // 0x14cbb64: DecompressPointer r1
    //     0x14cbb64: add             x1, x1, HEAP, lsl #32
    // 0x14cbb68: cmp             w1, NULL
    // 0x14cbb6c: b.ne            #0x14cbb78
    // 0x14cbb70: r0 = Null
    //     0x14cbb70: mov             x0, NULL
    // 0x14cbb74: b               #0x14cbbb0
    // 0x14cbb78: LoadField: r0 = r1->field_f
    //     0x14cbb78: ldur            w0, [x1, #0xf]
    // 0x14cbb7c: DecompressPointer r0
    //     0x14cbb7c: add             x0, x0, HEAP, lsl #32
    // 0x14cbb80: cmp             w0, NULL
    // 0x14cbb84: b.ne            #0x14cbb90
    // 0x14cbb88: r0 = Null
    //     0x14cbb88: mov             x0, NULL
    // 0x14cbb8c: b               #0x14cbbb0
    // 0x14cbb90: LoadField: r1 = r0->field_1b
    //     0x14cbb90: ldur            w1, [x0, #0x1b]
    // 0x14cbb94: DecompressPointer r1
    //     0x14cbb94: add             x1, x1, HEAP, lsl #32
    // 0x14cbb98: cmp             w1, NULL
    // 0x14cbb9c: b.ne            #0x14cbba8
    // 0x14cbba0: r0 = Null
    //     0x14cbba0: mov             x0, NULL
    // 0x14cbba4: b               #0x14cbbb0
    // 0x14cbba8: LoadField: r0 = r1->field_f
    //     0x14cbba8: ldur            w0, [x1, #0xf]
    // 0x14cbbac: DecompressPointer r0
    //     0x14cbbac: add             x0, x0, HEAP, lsl #32
    // 0x14cbbb0: cmp             w0, NULL
    // 0x14cbbb4: b.ne            #0x14cbbc0
    // 0x14cbbb8: r1 = 0
    //     0x14cbbb8: movz            x1, #0
    // 0x14cbbbc: b               #0x14cbbcc
    // 0x14cbbc0: r1 = LoadInt32Instr(r0)
    //     0x14cbbc0: sbfx            x1, x0, #1, #0x1f
    //     0x14cbbc4: tbz             w0, #0, #0x14cbbcc
    //     0x14cbbc8: ldur            x1, [x0, #7]
    // 0x14cbbcc: ldur            x2, [fp, #-8]
    // 0x14cbbd0: ldur            x0, [fp, #-0x50]
    // 0x14cbbd4: add             x3, x0, x1
    // 0x14cbbd8: stur            x3, [fp, #-0x48]
    // 0x14cbbdc: LoadField: r1 = r2->field_f
    //     0x14cbbdc: ldur            w1, [x2, #0xf]
    // 0x14cbbe0: DecompressPointer r1
    //     0x14cbbe0: add             x1, x1, HEAP, lsl #32
    // 0x14cbbe4: r0 = controller()
    //     0x14cbbe4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbbe8: LoadField: r1 = r0->field_7b
    //     0x14cbbe8: ldur            w1, [x0, #0x7b]
    // 0x14cbbec: DecompressPointer r1
    //     0x14cbbec: add             x1, x1, HEAP, lsl #32
    // 0x14cbbf0: r0 = value()
    //     0x14cbbf0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbbf4: LoadField: r1 = r0->field_b
    //     0x14cbbf4: ldur            w1, [x0, #0xb]
    // 0x14cbbf8: DecompressPointer r1
    //     0x14cbbf8: add             x1, x1, HEAP, lsl #32
    // 0x14cbbfc: cmp             w1, NULL
    // 0x14cbc00: b.ne            #0x14cbc0c
    // 0x14cbc04: r0 = Null
    //     0x14cbc04: mov             x0, NULL
    // 0x14cbc08: b               #0x14cbc44
    // 0x14cbc0c: LoadField: r0 = r1->field_f
    //     0x14cbc0c: ldur            w0, [x1, #0xf]
    // 0x14cbc10: DecompressPointer r0
    //     0x14cbc10: add             x0, x0, HEAP, lsl #32
    // 0x14cbc14: cmp             w0, NULL
    // 0x14cbc18: b.ne            #0x14cbc24
    // 0x14cbc1c: r0 = Null
    //     0x14cbc1c: mov             x0, NULL
    // 0x14cbc20: b               #0x14cbc44
    // 0x14cbc24: LoadField: r1 = r0->field_1b
    //     0x14cbc24: ldur            w1, [x0, #0x1b]
    // 0x14cbc28: DecompressPointer r1
    //     0x14cbc28: add             x1, x1, HEAP, lsl #32
    // 0x14cbc2c: cmp             w1, NULL
    // 0x14cbc30: b.ne            #0x14cbc3c
    // 0x14cbc34: r0 = Null
    //     0x14cbc34: mov             x0, NULL
    // 0x14cbc38: b               #0x14cbc44
    // 0x14cbc3c: LoadField: r0 = r1->field_13
    //     0x14cbc3c: ldur            w0, [x1, #0x13]
    // 0x14cbc40: DecompressPointer r0
    //     0x14cbc40: add             x0, x0, HEAP, lsl #32
    // 0x14cbc44: cmp             w0, NULL
    // 0x14cbc48: b.ne            #0x14cbc54
    // 0x14cbc4c: r1 = 0
    //     0x14cbc4c: movz            x1, #0
    // 0x14cbc50: b               #0x14cbc60
    // 0x14cbc54: r1 = LoadInt32Instr(r0)
    //     0x14cbc54: sbfx            x1, x0, #1, #0x1f
    //     0x14cbc58: tbz             w0, #0, #0x14cbc60
    //     0x14cbc5c: ldur            x1, [x0, #7]
    // 0x14cbc60: ldur            x2, [fp, #-8]
    // 0x14cbc64: ldur            x0, [fp, #-0x48]
    // 0x14cbc68: add             x3, x0, x1
    // 0x14cbc6c: stur            x3, [fp, #-0x50]
    // 0x14cbc70: LoadField: r1 = r2->field_f
    //     0x14cbc70: ldur            w1, [x2, #0xf]
    // 0x14cbc74: DecompressPointer r1
    //     0x14cbc74: add             x1, x1, HEAP, lsl #32
    // 0x14cbc78: r0 = controller()
    //     0x14cbc78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbc7c: LoadField: r1 = r0->field_7b
    //     0x14cbc7c: ldur            w1, [x0, #0x7b]
    // 0x14cbc80: DecompressPointer r1
    //     0x14cbc80: add             x1, x1, HEAP, lsl #32
    // 0x14cbc84: r0 = value()
    //     0x14cbc84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbc88: LoadField: r1 = r0->field_b
    //     0x14cbc88: ldur            w1, [x0, #0xb]
    // 0x14cbc8c: DecompressPointer r1
    //     0x14cbc8c: add             x1, x1, HEAP, lsl #32
    // 0x14cbc90: cmp             w1, NULL
    // 0x14cbc94: b.ne            #0x14cbca0
    // 0x14cbc98: r0 = Null
    //     0x14cbc98: mov             x0, NULL
    // 0x14cbc9c: b               #0x14cbcd8
    // 0x14cbca0: LoadField: r0 = r1->field_f
    //     0x14cbca0: ldur            w0, [x1, #0xf]
    // 0x14cbca4: DecompressPointer r0
    //     0x14cbca4: add             x0, x0, HEAP, lsl #32
    // 0x14cbca8: cmp             w0, NULL
    // 0x14cbcac: b.ne            #0x14cbcb8
    // 0x14cbcb0: r0 = Null
    //     0x14cbcb0: mov             x0, NULL
    // 0x14cbcb4: b               #0x14cbcd8
    // 0x14cbcb8: LoadField: r1 = r0->field_1b
    //     0x14cbcb8: ldur            w1, [x0, #0x1b]
    // 0x14cbcbc: DecompressPointer r1
    //     0x14cbcbc: add             x1, x1, HEAP, lsl #32
    // 0x14cbcc0: cmp             w1, NULL
    // 0x14cbcc4: b.ne            #0x14cbcd0
    // 0x14cbcc8: r0 = Null
    //     0x14cbcc8: mov             x0, NULL
    // 0x14cbccc: b               #0x14cbcd8
    // 0x14cbcd0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14cbcd0: ldur            w0, [x1, #0x17]
    // 0x14cbcd4: DecompressPointer r0
    //     0x14cbcd4: add             x0, x0, HEAP, lsl #32
    // 0x14cbcd8: cmp             w0, NULL
    // 0x14cbcdc: b.ne            #0x14cbce8
    // 0x14cbce0: r3 = 0
    //     0x14cbce0: movz            x3, #0
    // 0x14cbce4: b               #0x14cbcf8
    // 0x14cbce8: r1 = LoadInt32Instr(r0)
    //     0x14cbce8: sbfx            x1, x0, #1, #0x1f
    //     0x14cbcec: tbz             w0, #0, #0x14cbcf4
    //     0x14cbcf0: ldur            x1, [x0, #7]
    // 0x14cbcf4: mov             x3, x1
    // 0x14cbcf8: ldur            x2, [fp, #-8]
    // 0x14cbcfc: ldur            x1, [fp, #-0x38]
    // 0x14cbd00: ldur            x0, [fp, #-0x50]
    // 0x14cbd04: add             x4, x0, x3
    // 0x14cbd08: scvtf           d0, x1
    // 0x14cbd0c: scvtf           d1, x4
    // 0x14cbd10: fdiv            d2, d0, d1
    // 0x14cbd14: stur            d2, [fp, #-0x70]
    // 0x14cbd18: LoadField: r1 = r2->field_f
    //     0x14cbd18: ldur            w1, [x2, #0xf]
    // 0x14cbd1c: DecompressPointer r1
    //     0x14cbd1c: add             x1, x1, HEAP, lsl #32
    // 0x14cbd20: r0 = controller()
    //     0x14cbd20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbd24: LoadField: r1 = r0->field_7b
    //     0x14cbd24: ldur            w1, [x0, #0x7b]
    // 0x14cbd28: DecompressPointer r1
    //     0x14cbd28: add             x1, x1, HEAP, lsl #32
    // 0x14cbd2c: r0 = value()
    //     0x14cbd2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbd30: LoadField: r1 = r0->field_b
    //     0x14cbd30: ldur            w1, [x0, #0xb]
    // 0x14cbd34: DecompressPointer r1
    //     0x14cbd34: add             x1, x1, HEAP, lsl #32
    // 0x14cbd38: cmp             w1, NULL
    // 0x14cbd3c: b.ne            #0x14cbd48
    // 0x14cbd40: r0 = Null
    //     0x14cbd40: mov             x0, NULL
    // 0x14cbd44: b               #0x14cbd80
    // 0x14cbd48: LoadField: r0 = r1->field_f
    //     0x14cbd48: ldur            w0, [x1, #0xf]
    // 0x14cbd4c: DecompressPointer r0
    //     0x14cbd4c: add             x0, x0, HEAP, lsl #32
    // 0x14cbd50: cmp             w0, NULL
    // 0x14cbd54: b.ne            #0x14cbd60
    // 0x14cbd58: r0 = Null
    //     0x14cbd58: mov             x0, NULL
    // 0x14cbd5c: b               #0x14cbd80
    // 0x14cbd60: LoadField: r1 = r0->field_1b
    //     0x14cbd60: ldur            w1, [x0, #0x1b]
    // 0x14cbd64: DecompressPointer r1
    //     0x14cbd64: add             x1, x1, HEAP, lsl #32
    // 0x14cbd68: cmp             w1, NULL
    // 0x14cbd6c: b.ne            #0x14cbd78
    // 0x14cbd70: r0 = Null
    //     0x14cbd70: mov             x0, NULL
    // 0x14cbd74: b               #0x14cbd80
    // 0x14cbd78: LoadField: r0 = r1->field_7
    //     0x14cbd78: ldur            w0, [x1, #7]
    // 0x14cbd7c: DecompressPointer r0
    //     0x14cbd7c: add             x0, x0, HEAP, lsl #32
    // 0x14cbd80: cmp             w0, NULL
    // 0x14cbd84: b.ne            #0x14cbd90
    // 0x14cbd88: r5 = 0
    //     0x14cbd88: movz            x5, #0
    // 0x14cbd8c: b               #0x14cbda0
    // 0x14cbd90: r1 = LoadInt32Instr(r0)
    //     0x14cbd90: sbfx            x1, x0, #1, #0x1f
    //     0x14cbd94: tbz             w0, #0, #0x14cbd9c
    //     0x14cbd98: ldur            x1, [x0, #7]
    // 0x14cbd9c: mov             x5, x1
    // 0x14cbda0: ldur            x0, [fp, #-8]
    // 0x14cbda4: ldur            x1, [fp, #-0x28]
    // 0x14cbda8: ldur            x2, [fp, #-0x20]
    // 0x14cbdac: ldur            d0, [fp, #-0x70]
    // 0x14cbdb0: r3 = "5"
    //     0x14cbdb0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f898] "5"
    //     0x14cbdb4: ldr             x3, [x3, #0x898]
    // 0x14cbdb8: r0 = chartRow()
    //     0x14cbdb8: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x14cbdbc: ldur            x2, [fp, #-8]
    // 0x14cbdc0: stur            x0, [fp, #-0x40]
    // 0x14cbdc4: LoadField: r3 = r2->field_f
    //     0x14cbdc4: ldur            w3, [x2, #0xf]
    // 0x14cbdc8: DecompressPointer r3
    //     0x14cbdc8: add             x3, x3, HEAP, lsl #32
    // 0x14cbdcc: stur            x3, [fp, #-0x28]
    // 0x14cbdd0: LoadField: r4 = r2->field_13
    //     0x14cbdd0: ldur            w4, [x2, #0x13]
    // 0x14cbdd4: DecompressPointer r4
    //     0x14cbdd4: add             x4, x4, HEAP, lsl #32
    // 0x14cbdd8: mov             x1, x3
    // 0x14cbddc: stur            x4, [fp, #-0x20]
    // 0x14cbde0: r0 = controller()
    //     0x14cbde0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbde4: LoadField: r1 = r0->field_7b
    //     0x14cbde4: ldur            w1, [x0, #0x7b]
    // 0x14cbde8: DecompressPointer r1
    //     0x14cbde8: add             x1, x1, HEAP, lsl #32
    // 0x14cbdec: r0 = value()
    //     0x14cbdec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbdf0: LoadField: r1 = r0->field_b
    //     0x14cbdf0: ldur            w1, [x0, #0xb]
    // 0x14cbdf4: DecompressPointer r1
    //     0x14cbdf4: add             x1, x1, HEAP, lsl #32
    // 0x14cbdf8: cmp             w1, NULL
    // 0x14cbdfc: b.ne            #0x14cbe08
    // 0x14cbe00: r0 = Null
    //     0x14cbe00: mov             x0, NULL
    // 0x14cbe04: b               #0x14cbe40
    // 0x14cbe08: LoadField: r0 = r1->field_f
    //     0x14cbe08: ldur            w0, [x1, #0xf]
    // 0x14cbe0c: DecompressPointer r0
    //     0x14cbe0c: add             x0, x0, HEAP, lsl #32
    // 0x14cbe10: cmp             w0, NULL
    // 0x14cbe14: b.ne            #0x14cbe20
    // 0x14cbe18: r0 = Null
    //     0x14cbe18: mov             x0, NULL
    // 0x14cbe1c: b               #0x14cbe40
    // 0x14cbe20: LoadField: r1 = r0->field_1b
    //     0x14cbe20: ldur            w1, [x0, #0x1b]
    // 0x14cbe24: DecompressPointer r1
    //     0x14cbe24: add             x1, x1, HEAP, lsl #32
    // 0x14cbe28: cmp             w1, NULL
    // 0x14cbe2c: b.ne            #0x14cbe38
    // 0x14cbe30: r0 = Null
    //     0x14cbe30: mov             x0, NULL
    // 0x14cbe34: b               #0x14cbe40
    // 0x14cbe38: LoadField: r0 = r1->field_b
    //     0x14cbe38: ldur            w0, [x1, #0xb]
    // 0x14cbe3c: DecompressPointer r0
    //     0x14cbe3c: add             x0, x0, HEAP, lsl #32
    // 0x14cbe40: cmp             w0, NULL
    // 0x14cbe44: b.ne            #0x14cbe50
    // 0x14cbe48: r0 = 0
    //     0x14cbe48: movz            x0, #0
    // 0x14cbe4c: b               #0x14cbe60
    // 0x14cbe50: r1 = LoadInt32Instr(r0)
    //     0x14cbe50: sbfx            x1, x0, #1, #0x1f
    //     0x14cbe54: tbz             w0, #0, #0x14cbe5c
    //     0x14cbe58: ldur            x1, [x0, #7]
    // 0x14cbe5c: mov             x0, x1
    // 0x14cbe60: ldur            x2, [fp, #-8]
    // 0x14cbe64: stur            x0, [fp, #-0x38]
    // 0x14cbe68: LoadField: r1 = r2->field_f
    //     0x14cbe68: ldur            w1, [x2, #0xf]
    // 0x14cbe6c: DecompressPointer r1
    //     0x14cbe6c: add             x1, x1, HEAP, lsl #32
    // 0x14cbe70: r0 = controller()
    //     0x14cbe70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbe74: LoadField: r1 = r0->field_7b
    //     0x14cbe74: ldur            w1, [x0, #0x7b]
    // 0x14cbe78: DecompressPointer r1
    //     0x14cbe78: add             x1, x1, HEAP, lsl #32
    // 0x14cbe7c: r0 = value()
    //     0x14cbe7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbe80: LoadField: r1 = r0->field_b
    //     0x14cbe80: ldur            w1, [x0, #0xb]
    // 0x14cbe84: DecompressPointer r1
    //     0x14cbe84: add             x1, x1, HEAP, lsl #32
    // 0x14cbe88: cmp             w1, NULL
    // 0x14cbe8c: b.ne            #0x14cbe98
    // 0x14cbe90: r0 = Null
    //     0x14cbe90: mov             x0, NULL
    // 0x14cbe94: b               #0x14cbed0
    // 0x14cbe98: LoadField: r0 = r1->field_f
    //     0x14cbe98: ldur            w0, [x1, #0xf]
    // 0x14cbe9c: DecompressPointer r0
    //     0x14cbe9c: add             x0, x0, HEAP, lsl #32
    // 0x14cbea0: cmp             w0, NULL
    // 0x14cbea4: b.ne            #0x14cbeb0
    // 0x14cbea8: r0 = Null
    //     0x14cbea8: mov             x0, NULL
    // 0x14cbeac: b               #0x14cbed0
    // 0x14cbeb0: LoadField: r1 = r0->field_1b
    //     0x14cbeb0: ldur            w1, [x0, #0x1b]
    // 0x14cbeb4: DecompressPointer r1
    //     0x14cbeb4: add             x1, x1, HEAP, lsl #32
    // 0x14cbeb8: cmp             w1, NULL
    // 0x14cbebc: b.ne            #0x14cbec8
    // 0x14cbec0: r0 = Null
    //     0x14cbec0: mov             x0, NULL
    // 0x14cbec4: b               #0x14cbed0
    // 0x14cbec8: LoadField: r0 = r1->field_7
    //     0x14cbec8: ldur            w0, [x1, #7]
    // 0x14cbecc: DecompressPointer r0
    //     0x14cbecc: add             x0, x0, HEAP, lsl #32
    // 0x14cbed0: cmp             w0, NULL
    // 0x14cbed4: b.ne            #0x14cbee0
    // 0x14cbed8: r0 = 0
    //     0x14cbed8: movz            x0, #0
    // 0x14cbedc: b               #0x14cbef0
    // 0x14cbee0: r1 = LoadInt32Instr(r0)
    //     0x14cbee0: sbfx            x1, x0, #1, #0x1f
    //     0x14cbee4: tbz             w0, #0, #0x14cbeec
    //     0x14cbee8: ldur            x1, [x0, #7]
    // 0x14cbeec: mov             x0, x1
    // 0x14cbef0: ldur            x2, [fp, #-8]
    // 0x14cbef4: stur            x0, [fp, #-0x48]
    // 0x14cbef8: LoadField: r1 = r2->field_f
    //     0x14cbef8: ldur            w1, [x2, #0xf]
    // 0x14cbefc: DecompressPointer r1
    //     0x14cbefc: add             x1, x1, HEAP, lsl #32
    // 0x14cbf00: r0 = controller()
    //     0x14cbf00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbf04: LoadField: r1 = r0->field_7b
    //     0x14cbf04: ldur            w1, [x0, #0x7b]
    // 0x14cbf08: DecompressPointer r1
    //     0x14cbf08: add             x1, x1, HEAP, lsl #32
    // 0x14cbf0c: r0 = value()
    //     0x14cbf0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbf10: LoadField: r1 = r0->field_b
    //     0x14cbf10: ldur            w1, [x0, #0xb]
    // 0x14cbf14: DecompressPointer r1
    //     0x14cbf14: add             x1, x1, HEAP, lsl #32
    // 0x14cbf18: cmp             w1, NULL
    // 0x14cbf1c: b.ne            #0x14cbf28
    // 0x14cbf20: r0 = Null
    //     0x14cbf20: mov             x0, NULL
    // 0x14cbf24: b               #0x14cbf60
    // 0x14cbf28: LoadField: r0 = r1->field_f
    //     0x14cbf28: ldur            w0, [x1, #0xf]
    // 0x14cbf2c: DecompressPointer r0
    //     0x14cbf2c: add             x0, x0, HEAP, lsl #32
    // 0x14cbf30: cmp             w0, NULL
    // 0x14cbf34: b.ne            #0x14cbf40
    // 0x14cbf38: r0 = Null
    //     0x14cbf38: mov             x0, NULL
    // 0x14cbf3c: b               #0x14cbf60
    // 0x14cbf40: LoadField: r1 = r0->field_1b
    //     0x14cbf40: ldur            w1, [x0, #0x1b]
    // 0x14cbf44: DecompressPointer r1
    //     0x14cbf44: add             x1, x1, HEAP, lsl #32
    // 0x14cbf48: cmp             w1, NULL
    // 0x14cbf4c: b.ne            #0x14cbf58
    // 0x14cbf50: r0 = Null
    //     0x14cbf50: mov             x0, NULL
    // 0x14cbf54: b               #0x14cbf60
    // 0x14cbf58: LoadField: r0 = r1->field_b
    //     0x14cbf58: ldur            w0, [x1, #0xb]
    // 0x14cbf5c: DecompressPointer r0
    //     0x14cbf5c: add             x0, x0, HEAP, lsl #32
    // 0x14cbf60: cmp             w0, NULL
    // 0x14cbf64: b.ne            #0x14cbf70
    // 0x14cbf68: r1 = 0
    //     0x14cbf68: movz            x1, #0
    // 0x14cbf6c: b               #0x14cbf7c
    // 0x14cbf70: r1 = LoadInt32Instr(r0)
    //     0x14cbf70: sbfx            x1, x0, #1, #0x1f
    //     0x14cbf74: tbz             w0, #0, #0x14cbf7c
    //     0x14cbf78: ldur            x1, [x0, #7]
    // 0x14cbf7c: ldur            x2, [fp, #-8]
    // 0x14cbf80: ldur            x0, [fp, #-0x48]
    // 0x14cbf84: add             x3, x0, x1
    // 0x14cbf88: stur            x3, [fp, #-0x50]
    // 0x14cbf8c: LoadField: r1 = r2->field_f
    //     0x14cbf8c: ldur            w1, [x2, #0xf]
    // 0x14cbf90: DecompressPointer r1
    //     0x14cbf90: add             x1, x1, HEAP, lsl #32
    // 0x14cbf94: r0 = controller()
    //     0x14cbf94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cbf98: LoadField: r1 = r0->field_7b
    //     0x14cbf98: ldur            w1, [x0, #0x7b]
    // 0x14cbf9c: DecompressPointer r1
    //     0x14cbf9c: add             x1, x1, HEAP, lsl #32
    // 0x14cbfa0: r0 = value()
    //     0x14cbfa0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cbfa4: LoadField: r1 = r0->field_b
    //     0x14cbfa4: ldur            w1, [x0, #0xb]
    // 0x14cbfa8: DecompressPointer r1
    //     0x14cbfa8: add             x1, x1, HEAP, lsl #32
    // 0x14cbfac: cmp             w1, NULL
    // 0x14cbfb0: b.ne            #0x14cbfbc
    // 0x14cbfb4: r0 = Null
    //     0x14cbfb4: mov             x0, NULL
    // 0x14cbfb8: b               #0x14cbff4
    // 0x14cbfbc: LoadField: r0 = r1->field_f
    //     0x14cbfbc: ldur            w0, [x1, #0xf]
    // 0x14cbfc0: DecompressPointer r0
    //     0x14cbfc0: add             x0, x0, HEAP, lsl #32
    // 0x14cbfc4: cmp             w0, NULL
    // 0x14cbfc8: b.ne            #0x14cbfd4
    // 0x14cbfcc: r0 = Null
    //     0x14cbfcc: mov             x0, NULL
    // 0x14cbfd0: b               #0x14cbff4
    // 0x14cbfd4: LoadField: r1 = r0->field_1b
    //     0x14cbfd4: ldur            w1, [x0, #0x1b]
    // 0x14cbfd8: DecompressPointer r1
    //     0x14cbfd8: add             x1, x1, HEAP, lsl #32
    // 0x14cbfdc: cmp             w1, NULL
    // 0x14cbfe0: b.ne            #0x14cbfec
    // 0x14cbfe4: r0 = Null
    //     0x14cbfe4: mov             x0, NULL
    // 0x14cbfe8: b               #0x14cbff4
    // 0x14cbfec: LoadField: r0 = r1->field_f
    //     0x14cbfec: ldur            w0, [x1, #0xf]
    // 0x14cbff0: DecompressPointer r0
    //     0x14cbff0: add             x0, x0, HEAP, lsl #32
    // 0x14cbff4: cmp             w0, NULL
    // 0x14cbff8: b.ne            #0x14cc004
    // 0x14cbffc: r1 = 0
    //     0x14cbffc: movz            x1, #0
    // 0x14cc000: b               #0x14cc010
    // 0x14cc004: r1 = LoadInt32Instr(r0)
    //     0x14cc004: sbfx            x1, x0, #1, #0x1f
    //     0x14cc008: tbz             w0, #0, #0x14cc010
    //     0x14cc00c: ldur            x1, [x0, #7]
    // 0x14cc010: ldur            x2, [fp, #-8]
    // 0x14cc014: ldur            x0, [fp, #-0x50]
    // 0x14cc018: add             x3, x0, x1
    // 0x14cc01c: stur            x3, [fp, #-0x48]
    // 0x14cc020: LoadField: r1 = r2->field_f
    //     0x14cc020: ldur            w1, [x2, #0xf]
    // 0x14cc024: DecompressPointer r1
    //     0x14cc024: add             x1, x1, HEAP, lsl #32
    // 0x14cc028: r0 = controller()
    //     0x14cc028: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc02c: LoadField: r1 = r0->field_7b
    //     0x14cc02c: ldur            w1, [x0, #0x7b]
    // 0x14cc030: DecompressPointer r1
    //     0x14cc030: add             x1, x1, HEAP, lsl #32
    // 0x14cc034: r0 = value()
    //     0x14cc034: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc038: LoadField: r1 = r0->field_b
    //     0x14cc038: ldur            w1, [x0, #0xb]
    // 0x14cc03c: DecompressPointer r1
    //     0x14cc03c: add             x1, x1, HEAP, lsl #32
    // 0x14cc040: cmp             w1, NULL
    // 0x14cc044: b.ne            #0x14cc050
    // 0x14cc048: r0 = Null
    //     0x14cc048: mov             x0, NULL
    // 0x14cc04c: b               #0x14cc088
    // 0x14cc050: LoadField: r0 = r1->field_f
    //     0x14cc050: ldur            w0, [x1, #0xf]
    // 0x14cc054: DecompressPointer r0
    //     0x14cc054: add             x0, x0, HEAP, lsl #32
    // 0x14cc058: cmp             w0, NULL
    // 0x14cc05c: b.ne            #0x14cc068
    // 0x14cc060: r0 = Null
    //     0x14cc060: mov             x0, NULL
    // 0x14cc064: b               #0x14cc088
    // 0x14cc068: LoadField: r1 = r0->field_1b
    //     0x14cc068: ldur            w1, [x0, #0x1b]
    // 0x14cc06c: DecompressPointer r1
    //     0x14cc06c: add             x1, x1, HEAP, lsl #32
    // 0x14cc070: cmp             w1, NULL
    // 0x14cc074: b.ne            #0x14cc080
    // 0x14cc078: r0 = Null
    //     0x14cc078: mov             x0, NULL
    // 0x14cc07c: b               #0x14cc088
    // 0x14cc080: LoadField: r0 = r1->field_13
    //     0x14cc080: ldur            w0, [x1, #0x13]
    // 0x14cc084: DecompressPointer r0
    //     0x14cc084: add             x0, x0, HEAP, lsl #32
    // 0x14cc088: cmp             w0, NULL
    // 0x14cc08c: b.ne            #0x14cc098
    // 0x14cc090: r1 = 0
    //     0x14cc090: movz            x1, #0
    // 0x14cc094: b               #0x14cc0a4
    // 0x14cc098: r1 = LoadInt32Instr(r0)
    //     0x14cc098: sbfx            x1, x0, #1, #0x1f
    //     0x14cc09c: tbz             w0, #0, #0x14cc0a4
    //     0x14cc0a0: ldur            x1, [x0, #7]
    // 0x14cc0a4: ldur            x2, [fp, #-8]
    // 0x14cc0a8: ldur            x0, [fp, #-0x48]
    // 0x14cc0ac: add             x3, x0, x1
    // 0x14cc0b0: stur            x3, [fp, #-0x50]
    // 0x14cc0b4: LoadField: r1 = r2->field_f
    //     0x14cc0b4: ldur            w1, [x2, #0xf]
    // 0x14cc0b8: DecompressPointer r1
    //     0x14cc0b8: add             x1, x1, HEAP, lsl #32
    // 0x14cc0bc: r0 = controller()
    //     0x14cc0bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc0c0: LoadField: r1 = r0->field_7b
    //     0x14cc0c0: ldur            w1, [x0, #0x7b]
    // 0x14cc0c4: DecompressPointer r1
    //     0x14cc0c4: add             x1, x1, HEAP, lsl #32
    // 0x14cc0c8: r0 = value()
    //     0x14cc0c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc0cc: LoadField: r1 = r0->field_b
    //     0x14cc0cc: ldur            w1, [x0, #0xb]
    // 0x14cc0d0: DecompressPointer r1
    //     0x14cc0d0: add             x1, x1, HEAP, lsl #32
    // 0x14cc0d4: cmp             w1, NULL
    // 0x14cc0d8: b.ne            #0x14cc0e4
    // 0x14cc0dc: r0 = Null
    //     0x14cc0dc: mov             x0, NULL
    // 0x14cc0e0: b               #0x14cc11c
    // 0x14cc0e4: LoadField: r0 = r1->field_f
    //     0x14cc0e4: ldur            w0, [x1, #0xf]
    // 0x14cc0e8: DecompressPointer r0
    //     0x14cc0e8: add             x0, x0, HEAP, lsl #32
    // 0x14cc0ec: cmp             w0, NULL
    // 0x14cc0f0: b.ne            #0x14cc0fc
    // 0x14cc0f4: r0 = Null
    //     0x14cc0f4: mov             x0, NULL
    // 0x14cc0f8: b               #0x14cc11c
    // 0x14cc0fc: LoadField: r1 = r0->field_1b
    //     0x14cc0fc: ldur            w1, [x0, #0x1b]
    // 0x14cc100: DecompressPointer r1
    //     0x14cc100: add             x1, x1, HEAP, lsl #32
    // 0x14cc104: cmp             w1, NULL
    // 0x14cc108: b.ne            #0x14cc114
    // 0x14cc10c: r0 = Null
    //     0x14cc10c: mov             x0, NULL
    // 0x14cc110: b               #0x14cc11c
    // 0x14cc114: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14cc114: ldur            w0, [x1, #0x17]
    // 0x14cc118: DecompressPointer r0
    //     0x14cc118: add             x0, x0, HEAP, lsl #32
    // 0x14cc11c: cmp             w0, NULL
    // 0x14cc120: b.ne            #0x14cc12c
    // 0x14cc124: r3 = 0
    //     0x14cc124: movz            x3, #0
    // 0x14cc128: b               #0x14cc13c
    // 0x14cc12c: r1 = LoadInt32Instr(r0)
    //     0x14cc12c: sbfx            x1, x0, #1, #0x1f
    //     0x14cc130: tbz             w0, #0, #0x14cc138
    //     0x14cc134: ldur            x1, [x0, #7]
    // 0x14cc138: mov             x3, x1
    // 0x14cc13c: ldur            x2, [fp, #-8]
    // 0x14cc140: ldur            x1, [fp, #-0x38]
    // 0x14cc144: ldur            x0, [fp, #-0x50]
    // 0x14cc148: add             x4, x0, x3
    // 0x14cc14c: scvtf           d0, x1
    // 0x14cc150: scvtf           d1, x4
    // 0x14cc154: fdiv            d2, d0, d1
    // 0x14cc158: stur            d2, [fp, #-0x70]
    // 0x14cc15c: LoadField: r1 = r2->field_f
    //     0x14cc15c: ldur            w1, [x2, #0xf]
    // 0x14cc160: DecompressPointer r1
    //     0x14cc160: add             x1, x1, HEAP, lsl #32
    // 0x14cc164: r0 = controller()
    //     0x14cc164: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc168: LoadField: r1 = r0->field_7b
    //     0x14cc168: ldur            w1, [x0, #0x7b]
    // 0x14cc16c: DecompressPointer r1
    //     0x14cc16c: add             x1, x1, HEAP, lsl #32
    // 0x14cc170: r0 = value()
    //     0x14cc170: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc174: LoadField: r1 = r0->field_b
    //     0x14cc174: ldur            w1, [x0, #0xb]
    // 0x14cc178: DecompressPointer r1
    //     0x14cc178: add             x1, x1, HEAP, lsl #32
    // 0x14cc17c: cmp             w1, NULL
    // 0x14cc180: b.ne            #0x14cc18c
    // 0x14cc184: r0 = Null
    //     0x14cc184: mov             x0, NULL
    // 0x14cc188: b               #0x14cc1c4
    // 0x14cc18c: LoadField: r0 = r1->field_f
    //     0x14cc18c: ldur            w0, [x1, #0xf]
    // 0x14cc190: DecompressPointer r0
    //     0x14cc190: add             x0, x0, HEAP, lsl #32
    // 0x14cc194: cmp             w0, NULL
    // 0x14cc198: b.ne            #0x14cc1a4
    // 0x14cc19c: r0 = Null
    //     0x14cc19c: mov             x0, NULL
    // 0x14cc1a0: b               #0x14cc1c4
    // 0x14cc1a4: LoadField: r1 = r0->field_1b
    //     0x14cc1a4: ldur            w1, [x0, #0x1b]
    // 0x14cc1a8: DecompressPointer r1
    //     0x14cc1a8: add             x1, x1, HEAP, lsl #32
    // 0x14cc1ac: cmp             w1, NULL
    // 0x14cc1b0: b.ne            #0x14cc1bc
    // 0x14cc1b4: r0 = Null
    //     0x14cc1b4: mov             x0, NULL
    // 0x14cc1b8: b               #0x14cc1c4
    // 0x14cc1bc: LoadField: r0 = r1->field_b
    //     0x14cc1bc: ldur            w0, [x1, #0xb]
    // 0x14cc1c0: DecompressPointer r0
    //     0x14cc1c0: add             x0, x0, HEAP, lsl #32
    // 0x14cc1c4: cmp             w0, NULL
    // 0x14cc1c8: b.ne            #0x14cc1d4
    // 0x14cc1cc: r5 = 0
    //     0x14cc1cc: movz            x5, #0
    // 0x14cc1d0: b               #0x14cc1e4
    // 0x14cc1d4: r1 = LoadInt32Instr(r0)
    //     0x14cc1d4: sbfx            x1, x0, #1, #0x1f
    //     0x14cc1d8: tbz             w0, #0, #0x14cc1e0
    //     0x14cc1dc: ldur            x1, [x0, #7]
    // 0x14cc1e0: mov             x5, x1
    // 0x14cc1e4: ldur            x0, [fp, #-8]
    // 0x14cc1e8: ldur            x1, [fp, #-0x28]
    // 0x14cc1ec: ldur            x2, [fp, #-0x20]
    // 0x14cc1f0: ldur            d0, [fp, #-0x70]
    // 0x14cc1f4: r3 = "4"
    //     0x14cc1f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] "4"
    //     0x14cc1f8: ldr             x3, [x3, #0x8a0]
    // 0x14cc1fc: r0 = chartRow()
    //     0x14cc1fc: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x14cc200: ldur            x2, [fp, #-8]
    // 0x14cc204: stur            x0, [fp, #-0x58]
    // 0x14cc208: LoadField: r3 = r2->field_f
    //     0x14cc208: ldur            w3, [x2, #0xf]
    // 0x14cc20c: DecompressPointer r3
    //     0x14cc20c: add             x3, x3, HEAP, lsl #32
    // 0x14cc210: stur            x3, [fp, #-0x28]
    // 0x14cc214: LoadField: r4 = r2->field_13
    //     0x14cc214: ldur            w4, [x2, #0x13]
    // 0x14cc218: DecompressPointer r4
    //     0x14cc218: add             x4, x4, HEAP, lsl #32
    // 0x14cc21c: mov             x1, x3
    // 0x14cc220: stur            x4, [fp, #-0x20]
    // 0x14cc224: r0 = controller()
    //     0x14cc224: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc228: LoadField: r1 = r0->field_7b
    //     0x14cc228: ldur            w1, [x0, #0x7b]
    // 0x14cc22c: DecompressPointer r1
    //     0x14cc22c: add             x1, x1, HEAP, lsl #32
    // 0x14cc230: r0 = value()
    //     0x14cc230: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc234: LoadField: r1 = r0->field_b
    //     0x14cc234: ldur            w1, [x0, #0xb]
    // 0x14cc238: DecompressPointer r1
    //     0x14cc238: add             x1, x1, HEAP, lsl #32
    // 0x14cc23c: cmp             w1, NULL
    // 0x14cc240: b.ne            #0x14cc24c
    // 0x14cc244: r0 = Null
    //     0x14cc244: mov             x0, NULL
    // 0x14cc248: b               #0x14cc284
    // 0x14cc24c: LoadField: r0 = r1->field_f
    //     0x14cc24c: ldur            w0, [x1, #0xf]
    // 0x14cc250: DecompressPointer r0
    //     0x14cc250: add             x0, x0, HEAP, lsl #32
    // 0x14cc254: cmp             w0, NULL
    // 0x14cc258: b.ne            #0x14cc264
    // 0x14cc25c: r0 = Null
    //     0x14cc25c: mov             x0, NULL
    // 0x14cc260: b               #0x14cc284
    // 0x14cc264: LoadField: r1 = r0->field_1b
    //     0x14cc264: ldur            w1, [x0, #0x1b]
    // 0x14cc268: DecompressPointer r1
    //     0x14cc268: add             x1, x1, HEAP, lsl #32
    // 0x14cc26c: cmp             w1, NULL
    // 0x14cc270: b.ne            #0x14cc27c
    // 0x14cc274: r0 = Null
    //     0x14cc274: mov             x0, NULL
    // 0x14cc278: b               #0x14cc284
    // 0x14cc27c: LoadField: r0 = r1->field_f
    //     0x14cc27c: ldur            w0, [x1, #0xf]
    // 0x14cc280: DecompressPointer r0
    //     0x14cc280: add             x0, x0, HEAP, lsl #32
    // 0x14cc284: cmp             w0, NULL
    // 0x14cc288: b.ne            #0x14cc294
    // 0x14cc28c: r0 = 0
    //     0x14cc28c: movz            x0, #0
    // 0x14cc290: b               #0x14cc2a4
    // 0x14cc294: r1 = LoadInt32Instr(r0)
    //     0x14cc294: sbfx            x1, x0, #1, #0x1f
    //     0x14cc298: tbz             w0, #0, #0x14cc2a0
    //     0x14cc29c: ldur            x1, [x0, #7]
    // 0x14cc2a0: mov             x0, x1
    // 0x14cc2a4: ldur            x2, [fp, #-8]
    // 0x14cc2a8: stur            x0, [fp, #-0x38]
    // 0x14cc2ac: LoadField: r1 = r2->field_f
    //     0x14cc2ac: ldur            w1, [x2, #0xf]
    // 0x14cc2b0: DecompressPointer r1
    //     0x14cc2b0: add             x1, x1, HEAP, lsl #32
    // 0x14cc2b4: r0 = controller()
    //     0x14cc2b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc2b8: LoadField: r1 = r0->field_7b
    //     0x14cc2b8: ldur            w1, [x0, #0x7b]
    // 0x14cc2bc: DecompressPointer r1
    //     0x14cc2bc: add             x1, x1, HEAP, lsl #32
    // 0x14cc2c0: r0 = value()
    //     0x14cc2c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc2c4: LoadField: r1 = r0->field_b
    //     0x14cc2c4: ldur            w1, [x0, #0xb]
    // 0x14cc2c8: DecompressPointer r1
    //     0x14cc2c8: add             x1, x1, HEAP, lsl #32
    // 0x14cc2cc: cmp             w1, NULL
    // 0x14cc2d0: b.ne            #0x14cc2dc
    // 0x14cc2d4: r0 = Null
    //     0x14cc2d4: mov             x0, NULL
    // 0x14cc2d8: b               #0x14cc314
    // 0x14cc2dc: LoadField: r0 = r1->field_f
    //     0x14cc2dc: ldur            w0, [x1, #0xf]
    // 0x14cc2e0: DecompressPointer r0
    //     0x14cc2e0: add             x0, x0, HEAP, lsl #32
    // 0x14cc2e4: cmp             w0, NULL
    // 0x14cc2e8: b.ne            #0x14cc2f4
    // 0x14cc2ec: r0 = Null
    //     0x14cc2ec: mov             x0, NULL
    // 0x14cc2f0: b               #0x14cc314
    // 0x14cc2f4: LoadField: r1 = r0->field_1b
    //     0x14cc2f4: ldur            w1, [x0, #0x1b]
    // 0x14cc2f8: DecompressPointer r1
    //     0x14cc2f8: add             x1, x1, HEAP, lsl #32
    // 0x14cc2fc: cmp             w1, NULL
    // 0x14cc300: b.ne            #0x14cc30c
    // 0x14cc304: r0 = Null
    //     0x14cc304: mov             x0, NULL
    // 0x14cc308: b               #0x14cc314
    // 0x14cc30c: LoadField: r0 = r1->field_7
    //     0x14cc30c: ldur            w0, [x1, #7]
    // 0x14cc310: DecompressPointer r0
    //     0x14cc310: add             x0, x0, HEAP, lsl #32
    // 0x14cc314: cmp             w0, NULL
    // 0x14cc318: b.ne            #0x14cc324
    // 0x14cc31c: r0 = 0
    //     0x14cc31c: movz            x0, #0
    // 0x14cc320: b               #0x14cc334
    // 0x14cc324: r1 = LoadInt32Instr(r0)
    //     0x14cc324: sbfx            x1, x0, #1, #0x1f
    //     0x14cc328: tbz             w0, #0, #0x14cc330
    //     0x14cc32c: ldur            x1, [x0, #7]
    // 0x14cc330: mov             x0, x1
    // 0x14cc334: ldur            x2, [fp, #-8]
    // 0x14cc338: stur            x0, [fp, #-0x48]
    // 0x14cc33c: LoadField: r1 = r2->field_f
    //     0x14cc33c: ldur            w1, [x2, #0xf]
    // 0x14cc340: DecompressPointer r1
    //     0x14cc340: add             x1, x1, HEAP, lsl #32
    // 0x14cc344: r0 = controller()
    //     0x14cc344: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc348: LoadField: r1 = r0->field_7b
    //     0x14cc348: ldur            w1, [x0, #0x7b]
    // 0x14cc34c: DecompressPointer r1
    //     0x14cc34c: add             x1, x1, HEAP, lsl #32
    // 0x14cc350: r0 = value()
    //     0x14cc350: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc354: LoadField: r1 = r0->field_b
    //     0x14cc354: ldur            w1, [x0, #0xb]
    // 0x14cc358: DecompressPointer r1
    //     0x14cc358: add             x1, x1, HEAP, lsl #32
    // 0x14cc35c: cmp             w1, NULL
    // 0x14cc360: b.ne            #0x14cc36c
    // 0x14cc364: r0 = Null
    //     0x14cc364: mov             x0, NULL
    // 0x14cc368: b               #0x14cc3a4
    // 0x14cc36c: LoadField: r0 = r1->field_f
    //     0x14cc36c: ldur            w0, [x1, #0xf]
    // 0x14cc370: DecompressPointer r0
    //     0x14cc370: add             x0, x0, HEAP, lsl #32
    // 0x14cc374: cmp             w0, NULL
    // 0x14cc378: b.ne            #0x14cc384
    // 0x14cc37c: r0 = Null
    //     0x14cc37c: mov             x0, NULL
    // 0x14cc380: b               #0x14cc3a4
    // 0x14cc384: LoadField: r1 = r0->field_1b
    //     0x14cc384: ldur            w1, [x0, #0x1b]
    // 0x14cc388: DecompressPointer r1
    //     0x14cc388: add             x1, x1, HEAP, lsl #32
    // 0x14cc38c: cmp             w1, NULL
    // 0x14cc390: b.ne            #0x14cc39c
    // 0x14cc394: r0 = Null
    //     0x14cc394: mov             x0, NULL
    // 0x14cc398: b               #0x14cc3a4
    // 0x14cc39c: LoadField: r0 = r1->field_b
    //     0x14cc39c: ldur            w0, [x1, #0xb]
    // 0x14cc3a0: DecompressPointer r0
    //     0x14cc3a0: add             x0, x0, HEAP, lsl #32
    // 0x14cc3a4: cmp             w0, NULL
    // 0x14cc3a8: b.ne            #0x14cc3b4
    // 0x14cc3ac: r1 = 0
    //     0x14cc3ac: movz            x1, #0
    // 0x14cc3b0: b               #0x14cc3c0
    // 0x14cc3b4: r1 = LoadInt32Instr(r0)
    //     0x14cc3b4: sbfx            x1, x0, #1, #0x1f
    //     0x14cc3b8: tbz             w0, #0, #0x14cc3c0
    //     0x14cc3bc: ldur            x1, [x0, #7]
    // 0x14cc3c0: ldur            x2, [fp, #-8]
    // 0x14cc3c4: ldur            x0, [fp, #-0x48]
    // 0x14cc3c8: add             x3, x0, x1
    // 0x14cc3cc: stur            x3, [fp, #-0x50]
    // 0x14cc3d0: LoadField: r1 = r2->field_f
    //     0x14cc3d0: ldur            w1, [x2, #0xf]
    // 0x14cc3d4: DecompressPointer r1
    //     0x14cc3d4: add             x1, x1, HEAP, lsl #32
    // 0x14cc3d8: r0 = controller()
    //     0x14cc3d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc3dc: LoadField: r1 = r0->field_7b
    //     0x14cc3dc: ldur            w1, [x0, #0x7b]
    // 0x14cc3e0: DecompressPointer r1
    //     0x14cc3e0: add             x1, x1, HEAP, lsl #32
    // 0x14cc3e4: r0 = value()
    //     0x14cc3e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc3e8: LoadField: r1 = r0->field_b
    //     0x14cc3e8: ldur            w1, [x0, #0xb]
    // 0x14cc3ec: DecompressPointer r1
    //     0x14cc3ec: add             x1, x1, HEAP, lsl #32
    // 0x14cc3f0: cmp             w1, NULL
    // 0x14cc3f4: b.ne            #0x14cc400
    // 0x14cc3f8: r0 = Null
    //     0x14cc3f8: mov             x0, NULL
    // 0x14cc3fc: b               #0x14cc438
    // 0x14cc400: LoadField: r0 = r1->field_f
    //     0x14cc400: ldur            w0, [x1, #0xf]
    // 0x14cc404: DecompressPointer r0
    //     0x14cc404: add             x0, x0, HEAP, lsl #32
    // 0x14cc408: cmp             w0, NULL
    // 0x14cc40c: b.ne            #0x14cc418
    // 0x14cc410: r0 = Null
    //     0x14cc410: mov             x0, NULL
    // 0x14cc414: b               #0x14cc438
    // 0x14cc418: LoadField: r1 = r0->field_1b
    //     0x14cc418: ldur            w1, [x0, #0x1b]
    // 0x14cc41c: DecompressPointer r1
    //     0x14cc41c: add             x1, x1, HEAP, lsl #32
    // 0x14cc420: cmp             w1, NULL
    // 0x14cc424: b.ne            #0x14cc430
    // 0x14cc428: r0 = Null
    //     0x14cc428: mov             x0, NULL
    // 0x14cc42c: b               #0x14cc438
    // 0x14cc430: LoadField: r0 = r1->field_f
    //     0x14cc430: ldur            w0, [x1, #0xf]
    // 0x14cc434: DecompressPointer r0
    //     0x14cc434: add             x0, x0, HEAP, lsl #32
    // 0x14cc438: cmp             w0, NULL
    // 0x14cc43c: b.ne            #0x14cc448
    // 0x14cc440: r1 = 0
    //     0x14cc440: movz            x1, #0
    // 0x14cc444: b               #0x14cc454
    // 0x14cc448: r1 = LoadInt32Instr(r0)
    //     0x14cc448: sbfx            x1, x0, #1, #0x1f
    //     0x14cc44c: tbz             w0, #0, #0x14cc454
    //     0x14cc450: ldur            x1, [x0, #7]
    // 0x14cc454: ldur            x2, [fp, #-8]
    // 0x14cc458: ldur            x0, [fp, #-0x50]
    // 0x14cc45c: add             x3, x0, x1
    // 0x14cc460: stur            x3, [fp, #-0x48]
    // 0x14cc464: LoadField: r1 = r2->field_f
    //     0x14cc464: ldur            w1, [x2, #0xf]
    // 0x14cc468: DecompressPointer r1
    //     0x14cc468: add             x1, x1, HEAP, lsl #32
    // 0x14cc46c: r0 = controller()
    //     0x14cc46c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc470: LoadField: r1 = r0->field_7b
    //     0x14cc470: ldur            w1, [x0, #0x7b]
    // 0x14cc474: DecompressPointer r1
    //     0x14cc474: add             x1, x1, HEAP, lsl #32
    // 0x14cc478: r0 = value()
    //     0x14cc478: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc47c: LoadField: r1 = r0->field_b
    //     0x14cc47c: ldur            w1, [x0, #0xb]
    // 0x14cc480: DecompressPointer r1
    //     0x14cc480: add             x1, x1, HEAP, lsl #32
    // 0x14cc484: cmp             w1, NULL
    // 0x14cc488: b.ne            #0x14cc494
    // 0x14cc48c: r0 = Null
    //     0x14cc48c: mov             x0, NULL
    // 0x14cc490: b               #0x14cc4cc
    // 0x14cc494: LoadField: r0 = r1->field_f
    //     0x14cc494: ldur            w0, [x1, #0xf]
    // 0x14cc498: DecompressPointer r0
    //     0x14cc498: add             x0, x0, HEAP, lsl #32
    // 0x14cc49c: cmp             w0, NULL
    // 0x14cc4a0: b.ne            #0x14cc4ac
    // 0x14cc4a4: r0 = Null
    //     0x14cc4a4: mov             x0, NULL
    // 0x14cc4a8: b               #0x14cc4cc
    // 0x14cc4ac: LoadField: r1 = r0->field_1b
    //     0x14cc4ac: ldur            w1, [x0, #0x1b]
    // 0x14cc4b0: DecompressPointer r1
    //     0x14cc4b0: add             x1, x1, HEAP, lsl #32
    // 0x14cc4b4: cmp             w1, NULL
    // 0x14cc4b8: b.ne            #0x14cc4c4
    // 0x14cc4bc: r0 = Null
    //     0x14cc4bc: mov             x0, NULL
    // 0x14cc4c0: b               #0x14cc4cc
    // 0x14cc4c4: LoadField: r0 = r1->field_13
    //     0x14cc4c4: ldur            w0, [x1, #0x13]
    // 0x14cc4c8: DecompressPointer r0
    //     0x14cc4c8: add             x0, x0, HEAP, lsl #32
    // 0x14cc4cc: cmp             w0, NULL
    // 0x14cc4d0: b.ne            #0x14cc4dc
    // 0x14cc4d4: r1 = 0
    //     0x14cc4d4: movz            x1, #0
    // 0x14cc4d8: b               #0x14cc4e8
    // 0x14cc4dc: r1 = LoadInt32Instr(r0)
    //     0x14cc4dc: sbfx            x1, x0, #1, #0x1f
    //     0x14cc4e0: tbz             w0, #0, #0x14cc4e8
    //     0x14cc4e4: ldur            x1, [x0, #7]
    // 0x14cc4e8: ldur            x2, [fp, #-8]
    // 0x14cc4ec: ldur            x0, [fp, #-0x48]
    // 0x14cc4f0: add             x3, x0, x1
    // 0x14cc4f4: stur            x3, [fp, #-0x50]
    // 0x14cc4f8: LoadField: r1 = r2->field_f
    //     0x14cc4f8: ldur            w1, [x2, #0xf]
    // 0x14cc4fc: DecompressPointer r1
    //     0x14cc4fc: add             x1, x1, HEAP, lsl #32
    // 0x14cc500: r0 = controller()
    //     0x14cc500: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc504: LoadField: r1 = r0->field_7b
    //     0x14cc504: ldur            w1, [x0, #0x7b]
    // 0x14cc508: DecompressPointer r1
    //     0x14cc508: add             x1, x1, HEAP, lsl #32
    // 0x14cc50c: r0 = value()
    //     0x14cc50c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc510: LoadField: r1 = r0->field_b
    //     0x14cc510: ldur            w1, [x0, #0xb]
    // 0x14cc514: DecompressPointer r1
    //     0x14cc514: add             x1, x1, HEAP, lsl #32
    // 0x14cc518: cmp             w1, NULL
    // 0x14cc51c: b.ne            #0x14cc528
    // 0x14cc520: r0 = Null
    //     0x14cc520: mov             x0, NULL
    // 0x14cc524: b               #0x14cc560
    // 0x14cc528: LoadField: r0 = r1->field_f
    //     0x14cc528: ldur            w0, [x1, #0xf]
    // 0x14cc52c: DecompressPointer r0
    //     0x14cc52c: add             x0, x0, HEAP, lsl #32
    // 0x14cc530: cmp             w0, NULL
    // 0x14cc534: b.ne            #0x14cc540
    // 0x14cc538: r0 = Null
    //     0x14cc538: mov             x0, NULL
    // 0x14cc53c: b               #0x14cc560
    // 0x14cc540: LoadField: r1 = r0->field_1b
    //     0x14cc540: ldur            w1, [x0, #0x1b]
    // 0x14cc544: DecompressPointer r1
    //     0x14cc544: add             x1, x1, HEAP, lsl #32
    // 0x14cc548: cmp             w1, NULL
    // 0x14cc54c: b.ne            #0x14cc558
    // 0x14cc550: r0 = Null
    //     0x14cc550: mov             x0, NULL
    // 0x14cc554: b               #0x14cc560
    // 0x14cc558: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14cc558: ldur            w0, [x1, #0x17]
    // 0x14cc55c: DecompressPointer r0
    //     0x14cc55c: add             x0, x0, HEAP, lsl #32
    // 0x14cc560: cmp             w0, NULL
    // 0x14cc564: b.ne            #0x14cc570
    // 0x14cc568: r3 = 0
    //     0x14cc568: movz            x3, #0
    // 0x14cc56c: b               #0x14cc580
    // 0x14cc570: r1 = LoadInt32Instr(r0)
    //     0x14cc570: sbfx            x1, x0, #1, #0x1f
    //     0x14cc574: tbz             w0, #0, #0x14cc57c
    //     0x14cc578: ldur            x1, [x0, #7]
    // 0x14cc57c: mov             x3, x1
    // 0x14cc580: ldur            x2, [fp, #-8]
    // 0x14cc584: ldur            x1, [fp, #-0x38]
    // 0x14cc588: ldur            x0, [fp, #-0x50]
    // 0x14cc58c: add             x4, x0, x3
    // 0x14cc590: scvtf           d0, x1
    // 0x14cc594: scvtf           d1, x4
    // 0x14cc598: fdiv            d2, d0, d1
    // 0x14cc59c: stur            d2, [fp, #-0x70]
    // 0x14cc5a0: LoadField: r1 = r2->field_f
    //     0x14cc5a0: ldur            w1, [x2, #0xf]
    // 0x14cc5a4: DecompressPointer r1
    //     0x14cc5a4: add             x1, x1, HEAP, lsl #32
    // 0x14cc5a8: r0 = controller()
    //     0x14cc5a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc5ac: LoadField: r1 = r0->field_7b
    //     0x14cc5ac: ldur            w1, [x0, #0x7b]
    // 0x14cc5b0: DecompressPointer r1
    //     0x14cc5b0: add             x1, x1, HEAP, lsl #32
    // 0x14cc5b4: r0 = value()
    //     0x14cc5b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc5b8: LoadField: r1 = r0->field_b
    //     0x14cc5b8: ldur            w1, [x0, #0xb]
    // 0x14cc5bc: DecompressPointer r1
    //     0x14cc5bc: add             x1, x1, HEAP, lsl #32
    // 0x14cc5c0: cmp             w1, NULL
    // 0x14cc5c4: b.ne            #0x14cc5d0
    // 0x14cc5c8: r0 = Null
    //     0x14cc5c8: mov             x0, NULL
    // 0x14cc5cc: b               #0x14cc608
    // 0x14cc5d0: LoadField: r0 = r1->field_f
    //     0x14cc5d0: ldur            w0, [x1, #0xf]
    // 0x14cc5d4: DecompressPointer r0
    //     0x14cc5d4: add             x0, x0, HEAP, lsl #32
    // 0x14cc5d8: cmp             w0, NULL
    // 0x14cc5dc: b.ne            #0x14cc5e8
    // 0x14cc5e0: r0 = Null
    //     0x14cc5e0: mov             x0, NULL
    // 0x14cc5e4: b               #0x14cc608
    // 0x14cc5e8: LoadField: r1 = r0->field_1b
    //     0x14cc5e8: ldur            w1, [x0, #0x1b]
    // 0x14cc5ec: DecompressPointer r1
    //     0x14cc5ec: add             x1, x1, HEAP, lsl #32
    // 0x14cc5f0: cmp             w1, NULL
    // 0x14cc5f4: b.ne            #0x14cc600
    // 0x14cc5f8: r0 = Null
    //     0x14cc5f8: mov             x0, NULL
    // 0x14cc5fc: b               #0x14cc608
    // 0x14cc600: LoadField: r0 = r1->field_f
    //     0x14cc600: ldur            w0, [x1, #0xf]
    // 0x14cc604: DecompressPointer r0
    //     0x14cc604: add             x0, x0, HEAP, lsl #32
    // 0x14cc608: cmp             w0, NULL
    // 0x14cc60c: b.ne            #0x14cc618
    // 0x14cc610: r5 = 0
    //     0x14cc610: movz            x5, #0
    // 0x14cc614: b               #0x14cc628
    // 0x14cc618: r1 = LoadInt32Instr(r0)
    //     0x14cc618: sbfx            x1, x0, #1, #0x1f
    //     0x14cc61c: tbz             w0, #0, #0x14cc624
    //     0x14cc620: ldur            x1, [x0, #7]
    // 0x14cc624: mov             x5, x1
    // 0x14cc628: ldur            x0, [fp, #-8]
    // 0x14cc62c: ldur            x1, [fp, #-0x28]
    // 0x14cc630: ldur            x2, [fp, #-0x20]
    // 0x14cc634: ldur            d0, [fp, #-0x70]
    // 0x14cc638: r3 = "3"
    //     0x14cc638: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] "3"
    //     0x14cc63c: ldr             x3, [x3, #0x8a8]
    // 0x14cc640: r0 = chartRow()
    //     0x14cc640: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x14cc644: ldur            x2, [fp, #-8]
    // 0x14cc648: stur            x0, [fp, #-0x60]
    // 0x14cc64c: LoadField: r3 = r2->field_f
    //     0x14cc64c: ldur            w3, [x2, #0xf]
    // 0x14cc650: DecompressPointer r3
    //     0x14cc650: add             x3, x3, HEAP, lsl #32
    // 0x14cc654: stur            x3, [fp, #-0x28]
    // 0x14cc658: LoadField: r4 = r2->field_13
    //     0x14cc658: ldur            w4, [x2, #0x13]
    // 0x14cc65c: DecompressPointer r4
    //     0x14cc65c: add             x4, x4, HEAP, lsl #32
    // 0x14cc660: mov             x1, x3
    // 0x14cc664: stur            x4, [fp, #-0x20]
    // 0x14cc668: r0 = controller()
    //     0x14cc668: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc66c: LoadField: r1 = r0->field_7b
    //     0x14cc66c: ldur            w1, [x0, #0x7b]
    // 0x14cc670: DecompressPointer r1
    //     0x14cc670: add             x1, x1, HEAP, lsl #32
    // 0x14cc674: r0 = value()
    //     0x14cc674: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc678: LoadField: r1 = r0->field_b
    //     0x14cc678: ldur            w1, [x0, #0xb]
    // 0x14cc67c: DecompressPointer r1
    //     0x14cc67c: add             x1, x1, HEAP, lsl #32
    // 0x14cc680: cmp             w1, NULL
    // 0x14cc684: b.ne            #0x14cc690
    // 0x14cc688: r0 = Null
    //     0x14cc688: mov             x0, NULL
    // 0x14cc68c: b               #0x14cc6c8
    // 0x14cc690: LoadField: r0 = r1->field_f
    //     0x14cc690: ldur            w0, [x1, #0xf]
    // 0x14cc694: DecompressPointer r0
    //     0x14cc694: add             x0, x0, HEAP, lsl #32
    // 0x14cc698: cmp             w0, NULL
    // 0x14cc69c: b.ne            #0x14cc6a8
    // 0x14cc6a0: r0 = Null
    //     0x14cc6a0: mov             x0, NULL
    // 0x14cc6a4: b               #0x14cc6c8
    // 0x14cc6a8: LoadField: r1 = r0->field_1b
    //     0x14cc6a8: ldur            w1, [x0, #0x1b]
    // 0x14cc6ac: DecompressPointer r1
    //     0x14cc6ac: add             x1, x1, HEAP, lsl #32
    // 0x14cc6b0: cmp             w1, NULL
    // 0x14cc6b4: b.ne            #0x14cc6c0
    // 0x14cc6b8: r0 = Null
    //     0x14cc6b8: mov             x0, NULL
    // 0x14cc6bc: b               #0x14cc6c8
    // 0x14cc6c0: LoadField: r0 = r1->field_13
    //     0x14cc6c0: ldur            w0, [x1, #0x13]
    // 0x14cc6c4: DecompressPointer r0
    //     0x14cc6c4: add             x0, x0, HEAP, lsl #32
    // 0x14cc6c8: cmp             w0, NULL
    // 0x14cc6cc: b.ne            #0x14cc6d8
    // 0x14cc6d0: r0 = 0
    //     0x14cc6d0: movz            x0, #0
    // 0x14cc6d4: b               #0x14cc6e8
    // 0x14cc6d8: r1 = LoadInt32Instr(r0)
    //     0x14cc6d8: sbfx            x1, x0, #1, #0x1f
    //     0x14cc6dc: tbz             w0, #0, #0x14cc6e4
    //     0x14cc6e0: ldur            x1, [x0, #7]
    // 0x14cc6e4: mov             x0, x1
    // 0x14cc6e8: ldur            x2, [fp, #-8]
    // 0x14cc6ec: stur            x0, [fp, #-0x38]
    // 0x14cc6f0: LoadField: r1 = r2->field_f
    //     0x14cc6f0: ldur            w1, [x2, #0xf]
    // 0x14cc6f4: DecompressPointer r1
    //     0x14cc6f4: add             x1, x1, HEAP, lsl #32
    // 0x14cc6f8: r0 = controller()
    //     0x14cc6f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc6fc: LoadField: r1 = r0->field_7b
    //     0x14cc6fc: ldur            w1, [x0, #0x7b]
    // 0x14cc700: DecompressPointer r1
    //     0x14cc700: add             x1, x1, HEAP, lsl #32
    // 0x14cc704: r0 = value()
    //     0x14cc704: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc708: LoadField: r1 = r0->field_b
    //     0x14cc708: ldur            w1, [x0, #0xb]
    // 0x14cc70c: DecompressPointer r1
    //     0x14cc70c: add             x1, x1, HEAP, lsl #32
    // 0x14cc710: cmp             w1, NULL
    // 0x14cc714: b.ne            #0x14cc720
    // 0x14cc718: r0 = Null
    //     0x14cc718: mov             x0, NULL
    // 0x14cc71c: b               #0x14cc758
    // 0x14cc720: LoadField: r0 = r1->field_f
    //     0x14cc720: ldur            w0, [x1, #0xf]
    // 0x14cc724: DecompressPointer r0
    //     0x14cc724: add             x0, x0, HEAP, lsl #32
    // 0x14cc728: cmp             w0, NULL
    // 0x14cc72c: b.ne            #0x14cc738
    // 0x14cc730: r0 = Null
    //     0x14cc730: mov             x0, NULL
    // 0x14cc734: b               #0x14cc758
    // 0x14cc738: LoadField: r1 = r0->field_1b
    //     0x14cc738: ldur            w1, [x0, #0x1b]
    // 0x14cc73c: DecompressPointer r1
    //     0x14cc73c: add             x1, x1, HEAP, lsl #32
    // 0x14cc740: cmp             w1, NULL
    // 0x14cc744: b.ne            #0x14cc750
    // 0x14cc748: r0 = Null
    //     0x14cc748: mov             x0, NULL
    // 0x14cc74c: b               #0x14cc758
    // 0x14cc750: LoadField: r0 = r1->field_7
    //     0x14cc750: ldur            w0, [x1, #7]
    // 0x14cc754: DecompressPointer r0
    //     0x14cc754: add             x0, x0, HEAP, lsl #32
    // 0x14cc758: cmp             w0, NULL
    // 0x14cc75c: b.ne            #0x14cc768
    // 0x14cc760: r0 = 0
    //     0x14cc760: movz            x0, #0
    // 0x14cc764: b               #0x14cc778
    // 0x14cc768: r1 = LoadInt32Instr(r0)
    //     0x14cc768: sbfx            x1, x0, #1, #0x1f
    //     0x14cc76c: tbz             w0, #0, #0x14cc774
    //     0x14cc770: ldur            x1, [x0, #7]
    // 0x14cc774: mov             x0, x1
    // 0x14cc778: ldur            x2, [fp, #-8]
    // 0x14cc77c: stur            x0, [fp, #-0x48]
    // 0x14cc780: LoadField: r1 = r2->field_f
    //     0x14cc780: ldur            w1, [x2, #0xf]
    // 0x14cc784: DecompressPointer r1
    //     0x14cc784: add             x1, x1, HEAP, lsl #32
    // 0x14cc788: r0 = controller()
    //     0x14cc788: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc78c: LoadField: r1 = r0->field_7b
    //     0x14cc78c: ldur            w1, [x0, #0x7b]
    // 0x14cc790: DecompressPointer r1
    //     0x14cc790: add             x1, x1, HEAP, lsl #32
    // 0x14cc794: r0 = value()
    //     0x14cc794: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc798: LoadField: r1 = r0->field_b
    //     0x14cc798: ldur            w1, [x0, #0xb]
    // 0x14cc79c: DecompressPointer r1
    //     0x14cc79c: add             x1, x1, HEAP, lsl #32
    // 0x14cc7a0: cmp             w1, NULL
    // 0x14cc7a4: b.ne            #0x14cc7b0
    // 0x14cc7a8: r0 = Null
    //     0x14cc7a8: mov             x0, NULL
    // 0x14cc7ac: b               #0x14cc7e8
    // 0x14cc7b0: LoadField: r0 = r1->field_f
    //     0x14cc7b0: ldur            w0, [x1, #0xf]
    // 0x14cc7b4: DecompressPointer r0
    //     0x14cc7b4: add             x0, x0, HEAP, lsl #32
    // 0x14cc7b8: cmp             w0, NULL
    // 0x14cc7bc: b.ne            #0x14cc7c8
    // 0x14cc7c0: r0 = Null
    //     0x14cc7c0: mov             x0, NULL
    // 0x14cc7c4: b               #0x14cc7e8
    // 0x14cc7c8: LoadField: r1 = r0->field_1b
    //     0x14cc7c8: ldur            w1, [x0, #0x1b]
    // 0x14cc7cc: DecompressPointer r1
    //     0x14cc7cc: add             x1, x1, HEAP, lsl #32
    // 0x14cc7d0: cmp             w1, NULL
    // 0x14cc7d4: b.ne            #0x14cc7e0
    // 0x14cc7d8: r0 = Null
    //     0x14cc7d8: mov             x0, NULL
    // 0x14cc7dc: b               #0x14cc7e8
    // 0x14cc7e0: LoadField: r0 = r1->field_b
    //     0x14cc7e0: ldur            w0, [x1, #0xb]
    // 0x14cc7e4: DecompressPointer r0
    //     0x14cc7e4: add             x0, x0, HEAP, lsl #32
    // 0x14cc7e8: cmp             w0, NULL
    // 0x14cc7ec: b.ne            #0x14cc7f8
    // 0x14cc7f0: r1 = 0
    //     0x14cc7f0: movz            x1, #0
    // 0x14cc7f4: b               #0x14cc804
    // 0x14cc7f8: r1 = LoadInt32Instr(r0)
    //     0x14cc7f8: sbfx            x1, x0, #1, #0x1f
    //     0x14cc7fc: tbz             w0, #0, #0x14cc804
    //     0x14cc800: ldur            x1, [x0, #7]
    // 0x14cc804: ldur            x2, [fp, #-8]
    // 0x14cc808: ldur            x0, [fp, #-0x48]
    // 0x14cc80c: add             x3, x0, x1
    // 0x14cc810: stur            x3, [fp, #-0x50]
    // 0x14cc814: LoadField: r1 = r2->field_f
    //     0x14cc814: ldur            w1, [x2, #0xf]
    // 0x14cc818: DecompressPointer r1
    //     0x14cc818: add             x1, x1, HEAP, lsl #32
    // 0x14cc81c: r0 = controller()
    //     0x14cc81c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc820: LoadField: r1 = r0->field_7b
    //     0x14cc820: ldur            w1, [x0, #0x7b]
    // 0x14cc824: DecompressPointer r1
    //     0x14cc824: add             x1, x1, HEAP, lsl #32
    // 0x14cc828: r0 = value()
    //     0x14cc828: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc82c: LoadField: r1 = r0->field_b
    //     0x14cc82c: ldur            w1, [x0, #0xb]
    // 0x14cc830: DecompressPointer r1
    //     0x14cc830: add             x1, x1, HEAP, lsl #32
    // 0x14cc834: cmp             w1, NULL
    // 0x14cc838: b.ne            #0x14cc844
    // 0x14cc83c: r0 = Null
    //     0x14cc83c: mov             x0, NULL
    // 0x14cc840: b               #0x14cc87c
    // 0x14cc844: LoadField: r0 = r1->field_f
    //     0x14cc844: ldur            w0, [x1, #0xf]
    // 0x14cc848: DecompressPointer r0
    //     0x14cc848: add             x0, x0, HEAP, lsl #32
    // 0x14cc84c: cmp             w0, NULL
    // 0x14cc850: b.ne            #0x14cc85c
    // 0x14cc854: r0 = Null
    //     0x14cc854: mov             x0, NULL
    // 0x14cc858: b               #0x14cc87c
    // 0x14cc85c: LoadField: r1 = r0->field_1b
    //     0x14cc85c: ldur            w1, [x0, #0x1b]
    // 0x14cc860: DecompressPointer r1
    //     0x14cc860: add             x1, x1, HEAP, lsl #32
    // 0x14cc864: cmp             w1, NULL
    // 0x14cc868: b.ne            #0x14cc874
    // 0x14cc86c: r0 = Null
    //     0x14cc86c: mov             x0, NULL
    // 0x14cc870: b               #0x14cc87c
    // 0x14cc874: LoadField: r0 = r1->field_f
    //     0x14cc874: ldur            w0, [x1, #0xf]
    // 0x14cc878: DecompressPointer r0
    //     0x14cc878: add             x0, x0, HEAP, lsl #32
    // 0x14cc87c: cmp             w0, NULL
    // 0x14cc880: b.ne            #0x14cc88c
    // 0x14cc884: r1 = 0
    //     0x14cc884: movz            x1, #0
    // 0x14cc888: b               #0x14cc898
    // 0x14cc88c: r1 = LoadInt32Instr(r0)
    //     0x14cc88c: sbfx            x1, x0, #1, #0x1f
    //     0x14cc890: tbz             w0, #0, #0x14cc898
    //     0x14cc894: ldur            x1, [x0, #7]
    // 0x14cc898: ldur            x2, [fp, #-8]
    // 0x14cc89c: ldur            x0, [fp, #-0x50]
    // 0x14cc8a0: add             x3, x0, x1
    // 0x14cc8a4: stur            x3, [fp, #-0x48]
    // 0x14cc8a8: LoadField: r1 = r2->field_f
    //     0x14cc8a8: ldur            w1, [x2, #0xf]
    // 0x14cc8ac: DecompressPointer r1
    //     0x14cc8ac: add             x1, x1, HEAP, lsl #32
    // 0x14cc8b0: r0 = controller()
    //     0x14cc8b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc8b4: LoadField: r1 = r0->field_7b
    //     0x14cc8b4: ldur            w1, [x0, #0x7b]
    // 0x14cc8b8: DecompressPointer r1
    //     0x14cc8b8: add             x1, x1, HEAP, lsl #32
    // 0x14cc8bc: r0 = value()
    //     0x14cc8bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc8c0: LoadField: r1 = r0->field_b
    //     0x14cc8c0: ldur            w1, [x0, #0xb]
    // 0x14cc8c4: DecompressPointer r1
    //     0x14cc8c4: add             x1, x1, HEAP, lsl #32
    // 0x14cc8c8: cmp             w1, NULL
    // 0x14cc8cc: b.ne            #0x14cc8d8
    // 0x14cc8d0: r0 = Null
    //     0x14cc8d0: mov             x0, NULL
    // 0x14cc8d4: b               #0x14cc910
    // 0x14cc8d8: LoadField: r0 = r1->field_f
    //     0x14cc8d8: ldur            w0, [x1, #0xf]
    // 0x14cc8dc: DecompressPointer r0
    //     0x14cc8dc: add             x0, x0, HEAP, lsl #32
    // 0x14cc8e0: cmp             w0, NULL
    // 0x14cc8e4: b.ne            #0x14cc8f0
    // 0x14cc8e8: r0 = Null
    //     0x14cc8e8: mov             x0, NULL
    // 0x14cc8ec: b               #0x14cc910
    // 0x14cc8f0: LoadField: r1 = r0->field_1b
    //     0x14cc8f0: ldur            w1, [x0, #0x1b]
    // 0x14cc8f4: DecompressPointer r1
    //     0x14cc8f4: add             x1, x1, HEAP, lsl #32
    // 0x14cc8f8: cmp             w1, NULL
    // 0x14cc8fc: b.ne            #0x14cc908
    // 0x14cc900: r0 = Null
    //     0x14cc900: mov             x0, NULL
    // 0x14cc904: b               #0x14cc910
    // 0x14cc908: LoadField: r0 = r1->field_13
    //     0x14cc908: ldur            w0, [x1, #0x13]
    // 0x14cc90c: DecompressPointer r0
    //     0x14cc90c: add             x0, x0, HEAP, lsl #32
    // 0x14cc910: cmp             w0, NULL
    // 0x14cc914: b.ne            #0x14cc920
    // 0x14cc918: r1 = 0
    //     0x14cc918: movz            x1, #0
    // 0x14cc91c: b               #0x14cc92c
    // 0x14cc920: r1 = LoadInt32Instr(r0)
    //     0x14cc920: sbfx            x1, x0, #1, #0x1f
    //     0x14cc924: tbz             w0, #0, #0x14cc92c
    //     0x14cc928: ldur            x1, [x0, #7]
    // 0x14cc92c: ldur            x2, [fp, #-8]
    // 0x14cc930: ldur            x0, [fp, #-0x48]
    // 0x14cc934: add             x3, x0, x1
    // 0x14cc938: stur            x3, [fp, #-0x50]
    // 0x14cc93c: LoadField: r1 = r2->field_f
    //     0x14cc93c: ldur            w1, [x2, #0xf]
    // 0x14cc940: DecompressPointer r1
    //     0x14cc940: add             x1, x1, HEAP, lsl #32
    // 0x14cc944: r0 = controller()
    //     0x14cc944: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc948: LoadField: r1 = r0->field_7b
    //     0x14cc948: ldur            w1, [x0, #0x7b]
    // 0x14cc94c: DecompressPointer r1
    //     0x14cc94c: add             x1, x1, HEAP, lsl #32
    // 0x14cc950: r0 = value()
    //     0x14cc950: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc954: LoadField: r1 = r0->field_b
    //     0x14cc954: ldur            w1, [x0, #0xb]
    // 0x14cc958: DecompressPointer r1
    //     0x14cc958: add             x1, x1, HEAP, lsl #32
    // 0x14cc95c: cmp             w1, NULL
    // 0x14cc960: b.ne            #0x14cc96c
    // 0x14cc964: r0 = Null
    //     0x14cc964: mov             x0, NULL
    // 0x14cc968: b               #0x14cc9a4
    // 0x14cc96c: LoadField: r0 = r1->field_f
    //     0x14cc96c: ldur            w0, [x1, #0xf]
    // 0x14cc970: DecompressPointer r0
    //     0x14cc970: add             x0, x0, HEAP, lsl #32
    // 0x14cc974: cmp             w0, NULL
    // 0x14cc978: b.ne            #0x14cc984
    // 0x14cc97c: r0 = Null
    //     0x14cc97c: mov             x0, NULL
    // 0x14cc980: b               #0x14cc9a4
    // 0x14cc984: LoadField: r1 = r0->field_1b
    //     0x14cc984: ldur            w1, [x0, #0x1b]
    // 0x14cc988: DecompressPointer r1
    //     0x14cc988: add             x1, x1, HEAP, lsl #32
    // 0x14cc98c: cmp             w1, NULL
    // 0x14cc990: b.ne            #0x14cc99c
    // 0x14cc994: r0 = Null
    //     0x14cc994: mov             x0, NULL
    // 0x14cc998: b               #0x14cc9a4
    // 0x14cc99c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14cc99c: ldur            w0, [x1, #0x17]
    // 0x14cc9a0: DecompressPointer r0
    //     0x14cc9a0: add             x0, x0, HEAP, lsl #32
    // 0x14cc9a4: cmp             w0, NULL
    // 0x14cc9a8: b.ne            #0x14cc9b4
    // 0x14cc9ac: r3 = 0
    //     0x14cc9ac: movz            x3, #0
    // 0x14cc9b0: b               #0x14cc9c4
    // 0x14cc9b4: r1 = LoadInt32Instr(r0)
    //     0x14cc9b4: sbfx            x1, x0, #1, #0x1f
    //     0x14cc9b8: tbz             w0, #0, #0x14cc9c0
    //     0x14cc9bc: ldur            x1, [x0, #7]
    // 0x14cc9c0: mov             x3, x1
    // 0x14cc9c4: ldur            x2, [fp, #-8]
    // 0x14cc9c8: ldur            x1, [fp, #-0x38]
    // 0x14cc9cc: ldur            x0, [fp, #-0x50]
    // 0x14cc9d0: add             x4, x0, x3
    // 0x14cc9d4: scvtf           d0, x1
    // 0x14cc9d8: scvtf           d1, x4
    // 0x14cc9dc: fdiv            d2, d0, d1
    // 0x14cc9e0: stur            d2, [fp, #-0x70]
    // 0x14cc9e4: LoadField: r1 = r2->field_f
    //     0x14cc9e4: ldur            w1, [x2, #0xf]
    // 0x14cc9e8: DecompressPointer r1
    //     0x14cc9e8: add             x1, x1, HEAP, lsl #32
    // 0x14cc9ec: r0 = controller()
    //     0x14cc9ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cc9f0: LoadField: r1 = r0->field_7b
    //     0x14cc9f0: ldur            w1, [x0, #0x7b]
    // 0x14cc9f4: DecompressPointer r1
    //     0x14cc9f4: add             x1, x1, HEAP, lsl #32
    // 0x14cc9f8: r0 = value()
    //     0x14cc9f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cc9fc: LoadField: r1 = r0->field_b
    //     0x14cc9fc: ldur            w1, [x0, #0xb]
    // 0x14cca00: DecompressPointer r1
    //     0x14cca00: add             x1, x1, HEAP, lsl #32
    // 0x14cca04: cmp             w1, NULL
    // 0x14cca08: b.ne            #0x14cca14
    // 0x14cca0c: r0 = Null
    //     0x14cca0c: mov             x0, NULL
    // 0x14cca10: b               #0x14cca4c
    // 0x14cca14: LoadField: r0 = r1->field_f
    //     0x14cca14: ldur            w0, [x1, #0xf]
    // 0x14cca18: DecompressPointer r0
    //     0x14cca18: add             x0, x0, HEAP, lsl #32
    // 0x14cca1c: cmp             w0, NULL
    // 0x14cca20: b.ne            #0x14cca2c
    // 0x14cca24: r0 = Null
    //     0x14cca24: mov             x0, NULL
    // 0x14cca28: b               #0x14cca4c
    // 0x14cca2c: LoadField: r1 = r0->field_1b
    //     0x14cca2c: ldur            w1, [x0, #0x1b]
    // 0x14cca30: DecompressPointer r1
    //     0x14cca30: add             x1, x1, HEAP, lsl #32
    // 0x14cca34: cmp             w1, NULL
    // 0x14cca38: b.ne            #0x14cca44
    // 0x14cca3c: r0 = Null
    //     0x14cca3c: mov             x0, NULL
    // 0x14cca40: b               #0x14cca4c
    // 0x14cca44: LoadField: r0 = r1->field_13
    //     0x14cca44: ldur            w0, [x1, #0x13]
    // 0x14cca48: DecompressPointer r0
    //     0x14cca48: add             x0, x0, HEAP, lsl #32
    // 0x14cca4c: cmp             w0, NULL
    // 0x14cca50: b.ne            #0x14cca5c
    // 0x14cca54: r5 = 0
    //     0x14cca54: movz            x5, #0
    // 0x14cca58: b               #0x14cca6c
    // 0x14cca5c: r1 = LoadInt32Instr(r0)
    //     0x14cca5c: sbfx            x1, x0, #1, #0x1f
    //     0x14cca60: tbz             w0, #0, #0x14cca68
    //     0x14cca64: ldur            x1, [x0, #7]
    // 0x14cca68: mov             x5, x1
    // 0x14cca6c: ldur            x0, [fp, #-8]
    // 0x14cca70: ldur            x1, [fp, #-0x28]
    // 0x14cca74: ldur            x2, [fp, #-0x20]
    // 0x14cca78: ldur            d0, [fp, #-0x70]
    // 0x14cca7c: r3 = "2"
    //     0x14cca7c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] "2"
    //     0x14cca80: ldr             x3, [x3, #0x8b0]
    // 0x14cca84: r0 = chartRow()
    //     0x14cca84: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x14cca88: ldur            x2, [fp, #-8]
    // 0x14cca8c: stur            x0, [fp, #-0x68]
    // 0x14cca90: LoadField: r3 = r2->field_f
    //     0x14cca90: ldur            w3, [x2, #0xf]
    // 0x14cca94: DecompressPointer r3
    //     0x14cca94: add             x3, x3, HEAP, lsl #32
    // 0x14cca98: stur            x3, [fp, #-0x28]
    // 0x14cca9c: LoadField: r4 = r2->field_13
    //     0x14cca9c: ldur            w4, [x2, #0x13]
    // 0x14ccaa0: DecompressPointer r4
    //     0x14ccaa0: add             x4, x4, HEAP, lsl #32
    // 0x14ccaa4: mov             x1, x3
    // 0x14ccaa8: stur            x4, [fp, #-0x20]
    // 0x14ccaac: r0 = controller()
    //     0x14ccaac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ccab0: LoadField: r1 = r0->field_7b
    //     0x14ccab0: ldur            w1, [x0, #0x7b]
    // 0x14ccab4: DecompressPointer r1
    //     0x14ccab4: add             x1, x1, HEAP, lsl #32
    // 0x14ccab8: r0 = value()
    //     0x14ccab8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ccabc: LoadField: r1 = r0->field_b
    //     0x14ccabc: ldur            w1, [x0, #0xb]
    // 0x14ccac0: DecompressPointer r1
    //     0x14ccac0: add             x1, x1, HEAP, lsl #32
    // 0x14ccac4: cmp             w1, NULL
    // 0x14ccac8: b.ne            #0x14ccad4
    // 0x14ccacc: r0 = Null
    //     0x14ccacc: mov             x0, NULL
    // 0x14ccad0: b               #0x14ccb0c
    // 0x14ccad4: LoadField: r0 = r1->field_f
    //     0x14ccad4: ldur            w0, [x1, #0xf]
    // 0x14ccad8: DecompressPointer r0
    //     0x14ccad8: add             x0, x0, HEAP, lsl #32
    // 0x14ccadc: cmp             w0, NULL
    // 0x14ccae0: b.ne            #0x14ccaec
    // 0x14ccae4: r0 = Null
    //     0x14ccae4: mov             x0, NULL
    // 0x14ccae8: b               #0x14ccb0c
    // 0x14ccaec: LoadField: r1 = r0->field_1b
    //     0x14ccaec: ldur            w1, [x0, #0x1b]
    // 0x14ccaf0: DecompressPointer r1
    //     0x14ccaf0: add             x1, x1, HEAP, lsl #32
    // 0x14ccaf4: cmp             w1, NULL
    // 0x14ccaf8: b.ne            #0x14ccb04
    // 0x14ccafc: r0 = Null
    //     0x14ccafc: mov             x0, NULL
    // 0x14ccb00: b               #0x14ccb0c
    // 0x14ccb04: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14ccb04: ldur            w0, [x1, #0x17]
    // 0x14ccb08: DecompressPointer r0
    //     0x14ccb08: add             x0, x0, HEAP, lsl #32
    // 0x14ccb0c: cmp             w0, NULL
    // 0x14ccb10: b.ne            #0x14ccb1c
    // 0x14ccb14: r0 = 0
    //     0x14ccb14: movz            x0, #0
    // 0x14ccb18: b               #0x14ccb2c
    // 0x14ccb1c: r1 = LoadInt32Instr(r0)
    //     0x14ccb1c: sbfx            x1, x0, #1, #0x1f
    //     0x14ccb20: tbz             w0, #0, #0x14ccb28
    //     0x14ccb24: ldur            x1, [x0, #7]
    // 0x14ccb28: mov             x0, x1
    // 0x14ccb2c: ldur            x2, [fp, #-8]
    // 0x14ccb30: stur            x0, [fp, #-0x38]
    // 0x14ccb34: LoadField: r1 = r2->field_f
    //     0x14ccb34: ldur            w1, [x2, #0xf]
    // 0x14ccb38: DecompressPointer r1
    //     0x14ccb38: add             x1, x1, HEAP, lsl #32
    // 0x14ccb3c: r0 = controller()
    //     0x14ccb3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ccb40: LoadField: r1 = r0->field_7b
    //     0x14ccb40: ldur            w1, [x0, #0x7b]
    // 0x14ccb44: DecompressPointer r1
    //     0x14ccb44: add             x1, x1, HEAP, lsl #32
    // 0x14ccb48: r0 = value()
    //     0x14ccb48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ccb4c: LoadField: r1 = r0->field_b
    //     0x14ccb4c: ldur            w1, [x0, #0xb]
    // 0x14ccb50: DecompressPointer r1
    //     0x14ccb50: add             x1, x1, HEAP, lsl #32
    // 0x14ccb54: cmp             w1, NULL
    // 0x14ccb58: b.ne            #0x14ccb64
    // 0x14ccb5c: r0 = Null
    //     0x14ccb5c: mov             x0, NULL
    // 0x14ccb60: b               #0x14ccb9c
    // 0x14ccb64: LoadField: r0 = r1->field_f
    //     0x14ccb64: ldur            w0, [x1, #0xf]
    // 0x14ccb68: DecompressPointer r0
    //     0x14ccb68: add             x0, x0, HEAP, lsl #32
    // 0x14ccb6c: cmp             w0, NULL
    // 0x14ccb70: b.ne            #0x14ccb7c
    // 0x14ccb74: r0 = Null
    //     0x14ccb74: mov             x0, NULL
    // 0x14ccb78: b               #0x14ccb9c
    // 0x14ccb7c: LoadField: r1 = r0->field_1b
    //     0x14ccb7c: ldur            w1, [x0, #0x1b]
    // 0x14ccb80: DecompressPointer r1
    //     0x14ccb80: add             x1, x1, HEAP, lsl #32
    // 0x14ccb84: cmp             w1, NULL
    // 0x14ccb88: b.ne            #0x14ccb94
    // 0x14ccb8c: r0 = Null
    //     0x14ccb8c: mov             x0, NULL
    // 0x14ccb90: b               #0x14ccb9c
    // 0x14ccb94: LoadField: r0 = r1->field_7
    //     0x14ccb94: ldur            w0, [x1, #7]
    // 0x14ccb98: DecompressPointer r0
    //     0x14ccb98: add             x0, x0, HEAP, lsl #32
    // 0x14ccb9c: cmp             w0, NULL
    // 0x14ccba0: b.ne            #0x14ccbac
    // 0x14ccba4: r0 = 0
    //     0x14ccba4: movz            x0, #0
    // 0x14ccba8: b               #0x14ccbbc
    // 0x14ccbac: r1 = LoadInt32Instr(r0)
    //     0x14ccbac: sbfx            x1, x0, #1, #0x1f
    //     0x14ccbb0: tbz             w0, #0, #0x14ccbb8
    //     0x14ccbb4: ldur            x1, [x0, #7]
    // 0x14ccbb8: mov             x0, x1
    // 0x14ccbbc: ldur            x2, [fp, #-8]
    // 0x14ccbc0: stur            x0, [fp, #-0x48]
    // 0x14ccbc4: LoadField: r1 = r2->field_f
    //     0x14ccbc4: ldur            w1, [x2, #0xf]
    // 0x14ccbc8: DecompressPointer r1
    //     0x14ccbc8: add             x1, x1, HEAP, lsl #32
    // 0x14ccbcc: r0 = controller()
    //     0x14ccbcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ccbd0: LoadField: r1 = r0->field_7b
    //     0x14ccbd0: ldur            w1, [x0, #0x7b]
    // 0x14ccbd4: DecompressPointer r1
    //     0x14ccbd4: add             x1, x1, HEAP, lsl #32
    // 0x14ccbd8: r0 = value()
    //     0x14ccbd8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ccbdc: LoadField: r1 = r0->field_b
    //     0x14ccbdc: ldur            w1, [x0, #0xb]
    // 0x14ccbe0: DecompressPointer r1
    //     0x14ccbe0: add             x1, x1, HEAP, lsl #32
    // 0x14ccbe4: cmp             w1, NULL
    // 0x14ccbe8: b.ne            #0x14ccbf4
    // 0x14ccbec: r0 = Null
    //     0x14ccbec: mov             x0, NULL
    // 0x14ccbf0: b               #0x14ccc2c
    // 0x14ccbf4: LoadField: r0 = r1->field_f
    //     0x14ccbf4: ldur            w0, [x1, #0xf]
    // 0x14ccbf8: DecompressPointer r0
    //     0x14ccbf8: add             x0, x0, HEAP, lsl #32
    // 0x14ccbfc: cmp             w0, NULL
    // 0x14ccc00: b.ne            #0x14ccc0c
    // 0x14ccc04: r0 = Null
    //     0x14ccc04: mov             x0, NULL
    // 0x14ccc08: b               #0x14ccc2c
    // 0x14ccc0c: LoadField: r1 = r0->field_1b
    //     0x14ccc0c: ldur            w1, [x0, #0x1b]
    // 0x14ccc10: DecompressPointer r1
    //     0x14ccc10: add             x1, x1, HEAP, lsl #32
    // 0x14ccc14: cmp             w1, NULL
    // 0x14ccc18: b.ne            #0x14ccc24
    // 0x14ccc1c: r0 = Null
    //     0x14ccc1c: mov             x0, NULL
    // 0x14ccc20: b               #0x14ccc2c
    // 0x14ccc24: LoadField: r0 = r1->field_b
    //     0x14ccc24: ldur            w0, [x1, #0xb]
    // 0x14ccc28: DecompressPointer r0
    //     0x14ccc28: add             x0, x0, HEAP, lsl #32
    // 0x14ccc2c: cmp             w0, NULL
    // 0x14ccc30: b.ne            #0x14ccc3c
    // 0x14ccc34: r1 = 0
    //     0x14ccc34: movz            x1, #0
    // 0x14ccc38: b               #0x14ccc48
    // 0x14ccc3c: r1 = LoadInt32Instr(r0)
    //     0x14ccc3c: sbfx            x1, x0, #1, #0x1f
    //     0x14ccc40: tbz             w0, #0, #0x14ccc48
    //     0x14ccc44: ldur            x1, [x0, #7]
    // 0x14ccc48: ldur            x2, [fp, #-8]
    // 0x14ccc4c: ldur            x0, [fp, #-0x48]
    // 0x14ccc50: add             x3, x0, x1
    // 0x14ccc54: stur            x3, [fp, #-0x50]
    // 0x14ccc58: LoadField: r1 = r2->field_f
    //     0x14ccc58: ldur            w1, [x2, #0xf]
    // 0x14ccc5c: DecompressPointer r1
    //     0x14ccc5c: add             x1, x1, HEAP, lsl #32
    // 0x14ccc60: r0 = controller()
    //     0x14ccc60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ccc64: LoadField: r1 = r0->field_7b
    //     0x14ccc64: ldur            w1, [x0, #0x7b]
    // 0x14ccc68: DecompressPointer r1
    //     0x14ccc68: add             x1, x1, HEAP, lsl #32
    // 0x14ccc6c: r0 = value()
    //     0x14ccc6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ccc70: LoadField: r1 = r0->field_b
    //     0x14ccc70: ldur            w1, [x0, #0xb]
    // 0x14ccc74: DecompressPointer r1
    //     0x14ccc74: add             x1, x1, HEAP, lsl #32
    // 0x14ccc78: cmp             w1, NULL
    // 0x14ccc7c: b.ne            #0x14ccc88
    // 0x14ccc80: r0 = Null
    //     0x14ccc80: mov             x0, NULL
    // 0x14ccc84: b               #0x14cccc0
    // 0x14ccc88: LoadField: r0 = r1->field_f
    //     0x14ccc88: ldur            w0, [x1, #0xf]
    // 0x14ccc8c: DecompressPointer r0
    //     0x14ccc8c: add             x0, x0, HEAP, lsl #32
    // 0x14ccc90: cmp             w0, NULL
    // 0x14ccc94: b.ne            #0x14ccca0
    // 0x14ccc98: r0 = Null
    //     0x14ccc98: mov             x0, NULL
    // 0x14ccc9c: b               #0x14cccc0
    // 0x14ccca0: LoadField: r1 = r0->field_1b
    //     0x14ccca0: ldur            w1, [x0, #0x1b]
    // 0x14ccca4: DecompressPointer r1
    //     0x14ccca4: add             x1, x1, HEAP, lsl #32
    // 0x14ccca8: cmp             w1, NULL
    // 0x14cccac: b.ne            #0x14cccb8
    // 0x14cccb0: r0 = Null
    //     0x14cccb0: mov             x0, NULL
    // 0x14cccb4: b               #0x14cccc0
    // 0x14cccb8: LoadField: r0 = r1->field_f
    //     0x14cccb8: ldur            w0, [x1, #0xf]
    // 0x14cccbc: DecompressPointer r0
    //     0x14cccbc: add             x0, x0, HEAP, lsl #32
    // 0x14cccc0: cmp             w0, NULL
    // 0x14cccc4: b.ne            #0x14cccd0
    // 0x14cccc8: r1 = 0
    //     0x14cccc8: movz            x1, #0
    // 0x14ccccc: b               #0x14cccdc
    // 0x14cccd0: r1 = LoadInt32Instr(r0)
    //     0x14cccd0: sbfx            x1, x0, #1, #0x1f
    //     0x14cccd4: tbz             w0, #0, #0x14cccdc
    //     0x14cccd8: ldur            x1, [x0, #7]
    // 0x14cccdc: ldur            x2, [fp, #-8]
    // 0x14ccce0: ldur            x0, [fp, #-0x50]
    // 0x14ccce4: add             x3, x0, x1
    // 0x14ccce8: stur            x3, [fp, #-0x48]
    // 0x14cccec: LoadField: r1 = r2->field_f
    //     0x14cccec: ldur            w1, [x2, #0xf]
    // 0x14cccf0: DecompressPointer r1
    //     0x14cccf0: add             x1, x1, HEAP, lsl #32
    // 0x14cccf4: r0 = controller()
    //     0x14cccf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cccf8: LoadField: r1 = r0->field_7b
    //     0x14cccf8: ldur            w1, [x0, #0x7b]
    // 0x14cccfc: DecompressPointer r1
    //     0x14cccfc: add             x1, x1, HEAP, lsl #32
    // 0x14ccd00: r0 = value()
    //     0x14ccd00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ccd04: LoadField: r1 = r0->field_b
    //     0x14ccd04: ldur            w1, [x0, #0xb]
    // 0x14ccd08: DecompressPointer r1
    //     0x14ccd08: add             x1, x1, HEAP, lsl #32
    // 0x14ccd0c: cmp             w1, NULL
    // 0x14ccd10: b.ne            #0x14ccd1c
    // 0x14ccd14: r0 = Null
    //     0x14ccd14: mov             x0, NULL
    // 0x14ccd18: b               #0x14ccd54
    // 0x14ccd1c: LoadField: r0 = r1->field_f
    //     0x14ccd1c: ldur            w0, [x1, #0xf]
    // 0x14ccd20: DecompressPointer r0
    //     0x14ccd20: add             x0, x0, HEAP, lsl #32
    // 0x14ccd24: cmp             w0, NULL
    // 0x14ccd28: b.ne            #0x14ccd34
    // 0x14ccd2c: r0 = Null
    //     0x14ccd2c: mov             x0, NULL
    // 0x14ccd30: b               #0x14ccd54
    // 0x14ccd34: LoadField: r1 = r0->field_1b
    //     0x14ccd34: ldur            w1, [x0, #0x1b]
    // 0x14ccd38: DecompressPointer r1
    //     0x14ccd38: add             x1, x1, HEAP, lsl #32
    // 0x14ccd3c: cmp             w1, NULL
    // 0x14ccd40: b.ne            #0x14ccd4c
    // 0x14ccd44: r0 = Null
    //     0x14ccd44: mov             x0, NULL
    // 0x14ccd48: b               #0x14ccd54
    // 0x14ccd4c: LoadField: r0 = r1->field_13
    //     0x14ccd4c: ldur            w0, [x1, #0x13]
    // 0x14ccd50: DecompressPointer r0
    //     0x14ccd50: add             x0, x0, HEAP, lsl #32
    // 0x14ccd54: cmp             w0, NULL
    // 0x14ccd58: b.ne            #0x14ccd64
    // 0x14ccd5c: r1 = 0
    //     0x14ccd5c: movz            x1, #0
    // 0x14ccd60: b               #0x14ccd70
    // 0x14ccd64: r1 = LoadInt32Instr(r0)
    //     0x14ccd64: sbfx            x1, x0, #1, #0x1f
    //     0x14ccd68: tbz             w0, #0, #0x14ccd70
    //     0x14ccd6c: ldur            x1, [x0, #7]
    // 0x14ccd70: ldur            x2, [fp, #-8]
    // 0x14ccd74: ldur            x0, [fp, #-0x48]
    // 0x14ccd78: add             x3, x0, x1
    // 0x14ccd7c: stur            x3, [fp, #-0x50]
    // 0x14ccd80: LoadField: r1 = r2->field_f
    //     0x14ccd80: ldur            w1, [x2, #0xf]
    // 0x14ccd84: DecompressPointer r1
    //     0x14ccd84: add             x1, x1, HEAP, lsl #32
    // 0x14ccd88: r0 = controller()
    //     0x14ccd88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ccd8c: LoadField: r1 = r0->field_7b
    //     0x14ccd8c: ldur            w1, [x0, #0x7b]
    // 0x14ccd90: DecompressPointer r1
    //     0x14ccd90: add             x1, x1, HEAP, lsl #32
    // 0x14ccd94: r0 = value()
    //     0x14ccd94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ccd98: LoadField: r1 = r0->field_b
    //     0x14ccd98: ldur            w1, [x0, #0xb]
    // 0x14ccd9c: DecompressPointer r1
    //     0x14ccd9c: add             x1, x1, HEAP, lsl #32
    // 0x14ccda0: cmp             w1, NULL
    // 0x14ccda4: b.ne            #0x14ccdb0
    // 0x14ccda8: r0 = Null
    //     0x14ccda8: mov             x0, NULL
    // 0x14ccdac: b               #0x14ccde8
    // 0x14ccdb0: LoadField: r0 = r1->field_f
    //     0x14ccdb0: ldur            w0, [x1, #0xf]
    // 0x14ccdb4: DecompressPointer r0
    //     0x14ccdb4: add             x0, x0, HEAP, lsl #32
    // 0x14ccdb8: cmp             w0, NULL
    // 0x14ccdbc: b.ne            #0x14ccdc8
    // 0x14ccdc0: r0 = Null
    //     0x14ccdc0: mov             x0, NULL
    // 0x14ccdc4: b               #0x14ccde8
    // 0x14ccdc8: LoadField: r1 = r0->field_1b
    //     0x14ccdc8: ldur            w1, [x0, #0x1b]
    // 0x14ccdcc: DecompressPointer r1
    //     0x14ccdcc: add             x1, x1, HEAP, lsl #32
    // 0x14ccdd0: cmp             w1, NULL
    // 0x14ccdd4: b.ne            #0x14ccde0
    // 0x14ccdd8: r0 = Null
    //     0x14ccdd8: mov             x0, NULL
    // 0x14ccddc: b               #0x14ccde8
    // 0x14ccde0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14ccde0: ldur            w0, [x1, #0x17]
    // 0x14ccde4: DecompressPointer r0
    //     0x14ccde4: add             x0, x0, HEAP, lsl #32
    // 0x14ccde8: cmp             w0, NULL
    // 0x14ccdec: b.ne            #0x14ccdf8
    // 0x14ccdf0: r3 = 0
    //     0x14ccdf0: movz            x3, #0
    // 0x14ccdf4: b               #0x14cce08
    // 0x14ccdf8: r1 = LoadInt32Instr(r0)
    //     0x14ccdf8: sbfx            x1, x0, #1, #0x1f
    //     0x14ccdfc: tbz             w0, #0, #0x14cce04
    //     0x14cce00: ldur            x1, [x0, #7]
    // 0x14cce04: mov             x3, x1
    // 0x14cce08: ldur            x2, [fp, #-8]
    // 0x14cce0c: ldur            x1, [fp, #-0x38]
    // 0x14cce10: ldur            x0, [fp, #-0x50]
    // 0x14cce14: add             x4, x0, x3
    // 0x14cce18: scvtf           d0, x1
    // 0x14cce1c: scvtf           d1, x4
    // 0x14cce20: fdiv            d2, d0, d1
    // 0x14cce24: stur            d2, [fp, #-0x70]
    // 0x14cce28: LoadField: r1 = r2->field_f
    //     0x14cce28: ldur            w1, [x2, #0xf]
    // 0x14cce2c: DecompressPointer r1
    //     0x14cce2c: add             x1, x1, HEAP, lsl #32
    // 0x14cce30: r0 = controller()
    //     0x14cce30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cce34: LoadField: r1 = r0->field_7b
    //     0x14cce34: ldur            w1, [x0, #0x7b]
    // 0x14cce38: DecompressPointer r1
    //     0x14cce38: add             x1, x1, HEAP, lsl #32
    // 0x14cce3c: r0 = value()
    //     0x14cce3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cce40: LoadField: r1 = r0->field_b
    //     0x14cce40: ldur            w1, [x0, #0xb]
    // 0x14cce44: DecompressPointer r1
    //     0x14cce44: add             x1, x1, HEAP, lsl #32
    // 0x14cce48: cmp             w1, NULL
    // 0x14cce4c: b.ne            #0x14cce58
    // 0x14cce50: r0 = Null
    //     0x14cce50: mov             x0, NULL
    // 0x14cce54: b               #0x14cce90
    // 0x14cce58: LoadField: r0 = r1->field_f
    //     0x14cce58: ldur            w0, [x1, #0xf]
    // 0x14cce5c: DecompressPointer r0
    //     0x14cce5c: add             x0, x0, HEAP, lsl #32
    // 0x14cce60: cmp             w0, NULL
    // 0x14cce64: b.ne            #0x14cce70
    // 0x14cce68: r0 = Null
    //     0x14cce68: mov             x0, NULL
    // 0x14cce6c: b               #0x14cce90
    // 0x14cce70: LoadField: r1 = r0->field_1b
    //     0x14cce70: ldur            w1, [x0, #0x1b]
    // 0x14cce74: DecompressPointer r1
    //     0x14cce74: add             x1, x1, HEAP, lsl #32
    // 0x14cce78: cmp             w1, NULL
    // 0x14cce7c: b.ne            #0x14cce88
    // 0x14cce80: r0 = Null
    //     0x14cce80: mov             x0, NULL
    // 0x14cce84: b               #0x14cce90
    // 0x14cce88: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14cce88: ldur            w0, [x1, #0x17]
    // 0x14cce8c: DecompressPointer r0
    //     0x14cce8c: add             x0, x0, HEAP, lsl #32
    // 0x14cce90: cmp             w0, NULL
    // 0x14cce94: b.ne            #0x14ccea0
    // 0x14cce98: r5 = 0
    //     0x14cce98: movz            x5, #0
    // 0x14cce9c: b               #0x14cceb0
    // 0x14ccea0: r1 = LoadInt32Instr(r0)
    //     0x14ccea0: sbfx            x1, x0, #1, #0x1f
    //     0x14ccea4: tbz             w0, #0, #0x14cceac
    //     0x14ccea8: ldur            x1, [x0, #7]
    // 0x14cceac: mov             x5, x1
    // 0x14cceb0: ldur            x0, [fp, #-8]
    // 0x14cceb4: ldur            x10, [fp, #-0x10]
    // 0x14cceb8: ldur            x9, [fp, #-0x30]
    // 0x14ccebc: ldur            x8, [fp, #-0x40]
    // 0x14ccec0: ldur            x7, [fp, #-0x58]
    // 0x14ccec4: ldur            x6, [fp, #-0x60]
    // 0x14ccec8: ldur            x4, [fp, #-0x68]
    // 0x14ccecc: ldur            x1, [fp, #-0x28]
    // 0x14cced0: ldur            x2, [fp, #-0x20]
    // 0x14cced4: ldur            d0, [fp, #-0x70]
    // 0x14cced8: r3 = "1"
    //     0x14cced8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0x14ccedc: ldr             x3, [x3, #0xba8]
    // 0x14ccee0: r0 = chartRow()
    //     0x14ccee0: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x14ccee4: r1 = Null
    //     0x14ccee4: mov             x1, NULL
    // 0x14ccee8: r2 = 14
    //     0x14ccee8: movz            x2, #0xe
    // 0x14cceec: stur            x0, [fp, #-0x20]
    // 0x14ccef0: r0 = AllocateArray()
    //     0x14ccef0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ccef4: stur            x0, [fp, #-0x28]
    // 0x14ccef8: r16 = Instance_SizedBox
    //     0x14ccef8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x14ccefc: ldr             x16, [x16, #0x8b8]
    // 0x14ccf00: StoreField: r0->field_f = r16
    //     0x14ccf00: stur            w16, [x0, #0xf]
    // 0x14ccf04: ldur            x1, [fp, #-0x40]
    // 0x14ccf08: StoreField: r0->field_13 = r1
    //     0x14ccf08: stur            w1, [x0, #0x13]
    // 0x14ccf0c: ldur            x1, [fp, #-0x58]
    // 0x14ccf10: ArrayStore: r0[0] = r1  ; List_4
    //     0x14ccf10: stur            w1, [x0, #0x17]
    // 0x14ccf14: ldur            x1, [fp, #-0x60]
    // 0x14ccf18: StoreField: r0->field_1b = r1
    //     0x14ccf18: stur            w1, [x0, #0x1b]
    // 0x14ccf1c: ldur            x1, [fp, #-0x68]
    // 0x14ccf20: StoreField: r0->field_1f = r1
    //     0x14ccf20: stur            w1, [x0, #0x1f]
    // 0x14ccf24: ldur            x1, [fp, #-0x20]
    // 0x14ccf28: StoreField: r0->field_23 = r1
    //     0x14ccf28: stur            w1, [x0, #0x23]
    // 0x14ccf2c: r16 = Instance_SizedBox
    //     0x14ccf2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x14ccf30: ldr             x16, [x16, #0x8b8]
    // 0x14ccf34: StoreField: r0->field_27 = r16
    //     0x14ccf34: stur            w16, [x0, #0x27]
    // 0x14ccf38: r1 = <Widget>
    //     0x14ccf38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ccf3c: r0 = AllocateGrowableArray()
    //     0x14ccf3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ccf40: mov             x1, x0
    // 0x14ccf44: ldur            x0, [fp, #-0x28]
    // 0x14ccf48: stur            x1, [fp, #-0x20]
    // 0x14ccf4c: StoreField: r1->field_f = r0
    //     0x14ccf4c: stur            w0, [x1, #0xf]
    // 0x14ccf50: r0 = 14
    //     0x14ccf50: movz            x0, #0xe
    // 0x14ccf54: StoreField: r1->field_b = r0
    //     0x14ccf54: stur            w0, [x1, #0xb]
    // 0x14ccf58: r0 = Column()
    //     0x14ccf58: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14ccf5c: mov             x3, x0
    // 0x14ccf60: r0 = Instance_Axis
    //     0x14ccf60: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14ccf64: stur            x3, [fp, #-0x28]
    // 0x14ccf68: StoreField: r3->field_f = r0
    //     0x14ccf68: stur            w0, [x3, #0xf]
    // 0x14ccf6c: r4 = Instance_MainAxisAlignment
    //     0x14ccf6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ccf70: ldr             x4, [x4, #0xa08]
    // 0x14ccf74: StoreField: r3->field_13 = r4
    //     0x14ccf74: stur            w4, [x3, #0x13]
    // 0x14ccf78: r5 = Instance_MainAxisSize
    //     0x14ccf78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ccf7c: ldr             x5, [x5, #0xa10]
    // 0x14ccf80: ArrayStore: r3[0] = r5  ; List_4
    //     0x14ccf80: stur            w5, [x3, #0x17]
    // 0x14ccf84: r1 = Instance_CrossAxisAlignment
    //     0x14ccf84: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14ccf88: ldr             x1, [x1, #0x890]
    // 0x14ccf8c: StoreField: r3->field_1b = r1
    //     0x14ccf8c: stur            w1, [x3, #0x1b]
    // 0x14ccf90: r6 = Instance_VerticalDirection
    //     0x14ccf90: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ccf94: ldr             x6, [x6, #0xa20]
    // 0x14ccf98: StoreField: r3->field_23 = r6
    //     0x14ccf98: stur            w6, [x3, #0x23]
    // 0x14ccf9c: r7 = Instance_Clip
    //     0x14ccf9c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ccfa0: ldr             x7, [x7, #0x38]
    // 0x14ccfa4: StoreField: r3->field_2b = r7
    //     0x14ccfa4: stur            w7, [x3, #0x2b]
    // 0x14ccfa8: StoreField: r3->field_2f = rZR
    //     0x14ccfa8: stur            xzr, [x3, #0x2f]
    // 0x14ccfac: ldur            x1, [fp, #-0x20]
    // 0x14ccfb0: StoreField: r3->field_b = r1
    //     0x14ccfb0: stur            w1, [x3, #0xb]
    // 0x14ccfb4: r1 = Null
    //     0x14ccfb4: mov             x1, NULL
    // 0x14ccfb8: r2 = 4
    //     0x14ccfb8: movz            x2, #0x4
    // 0x14ccfbc: r0 = AllocateArray()
    //     0x14ccfbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ccfc0: mov             x2, x0
    // 0x14ccfc4: ldur            x0, [fp, #-0x30]
    // 0x14ccfc8: stur            x2, [fp, #-0x20]
    // 0x14ccfcc: StoreField: r2->field_f = r0
    //     0x14ccfcc: stur            w0, [x2, #0xf]
    // 0x14ccfd0: ldur            x0, [fp, #-0x28]
    // 0x14ccfd4: StoreField: r2->field_13 = r0
    //     0x14ccfd4: stur            w0, [x2, #0x13]
    // 0x14ccfd8: r1 = <Widget>
    //     0x14ccfd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ccfdc: r0 = AllocateGrowableArray()
    //     0x14ccfdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ccfe0: mov             x1, x0
    // 0x14ccfe4: ldur            x0, [fp, #-0x20]
    // 0x14ccfe8: stur            x1, [fp, #-0x28]
    // 0x14ccfec: StoreField: r1->field_f = r0
    //     0x14ccfec: stur            w0, [x1, #0xf]
    // 0x14ccff0: r0 = 4
    //     0x14ccff0: movz            x0, #0x4
    // 0x14ccff4: StoreField: r1->field_b = r0
    //     0x14ccff4: stur            w0, [x1, #0xb]
    // 0x14ccff8: r0 = Row()
    //     0x14ccff8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14ccffc: mov             x1, x0
    // 0x14cd000: r0 = Instance_Axis
    //     0x14cd000: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14cd004: stur            x1, [fp, #-0x20]
    // 0x14cd008: StoreField: r1->field_f = r0
    //     0x14cd008: stur            w0, [x1, #0xf]
    // 0x14cd00c: r2 = Instance_MainAxisAlignment
    //     0x14cd00c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14cd010: ldr             x2, [x2, #0xa8]
    // 0x14cd014: StoreField: r1->field_13 = r2
    //     0x14cd014: stur            w2, [x1, #0x13]
    // 0x14cd018: r2 = Instance_MainAxisSize
    //     0x14cd018: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cd01c: ldr             x2, [x2, #0xa10]
    // 0x14cd020: ArrayStore: r1[0] = r2  ; List_4
    //     0x14cd020: stur            w2, [x1, #0x17]
    // 0x14cd024: r3 = Instance_CrossAxisAlignment
    //     0x14cd024: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cd028: ldr             x3, [x3, #0xa18]
    // 0x14cd02c: StoreField: r1->field_1b = r3
    //     0x14cd02c: stur            w3, [x1, #0x1b]
    // 0x14cd030: r4 = Instance_VerticalDirection
    //     0x14cd030: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cd034: ldr             x4, [x4, #0xa20]
    // 0x14cd038: StoreField: r1->field_23 = r4
    //     0x14cd038: stur            w4, [x1, #0x23]
    // 0x14cd03c: r5 = Instance_Clip
    //     0x14cd03c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cd040: ldr             x5, [x5, #0x38]
    // 0x14cd044: StoreField: r1->field_2b = r5
    //     0x14cd044: stur            w5, [x1, #0x2b]
    // 0x14cd048: StoreField: r1->field_2f = rZR
    //     0x14cd048: stur            xzr, [x1, #0x2f]
    // 0x14cd04c: ldur            x6, [fp, #-0x28]
    // 0x14cd050: StoreField: r1->field_b = r6
    //     0x14cd050: stur            w6, [x1, #0xb]
    // 0x14cd054: r0 = Container()
    //     0x14cd054: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14cd058: stur            x0, [fp, #-0x28]
    // 0x14cd05c: ldur            x16, [fp, #-0x18]
    // 0x14cd060: r30 = Instance_EdgeInsets
    //     0x14cd060: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14cd064: ldr             lr, [lr, #0x980]
    // 0x14cd068: stp             lr, x16, [SP, #8]
    // 0x14cd06c: ldur            x16, [fp, #-0x20]
    // 0x14cd070: str             x16, [SP]
    // 0x14cd074: mov             x1, x0
    // 0x14cd078: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x14cd078: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x14cd07c: ldr             x4, [x4, #0xb40]
    // 0x14cd080: r0 = Container()
    //     0x14cd080: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14cd084: r0 = Visibility()
    //     0x14cd084: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14cd088: mov             x3, x0
    // 0x14cd08c: ldur            x0, [fp, #-0x28]
    // 0x14cd090: stur            x3, [fp, #-0x18]
    // 0x14cd094: StoreField: r3->field_b = r0
    //     0x14cd094: stur            w0, [x3, #0xb]
    // 0x14cd098: r0 = Instance_SizedBox
    //     0x14cd098: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14cd09c: StoreField: r3->field_f = r0
    //     0x14cd09c: stur            w0, [x3, #0xf]
    // 0x14cd0a0: ldur            x0, [fp, #-0x10]
    // 0x14cd0a4: StoreField: r3->field_13 = r0
    //     0x14cd0a4: stur            w0, [x3, #0x13]
    // 0x14cd0a8: r0 = false
    //     0x14cd0a8: add             x0, NULL, #0x30  ; false
    // 0x14cd0ac: ArrayStore: r3[0] = r0  ; List_4
    //     0x14cd0ac: stur            w0, [x3, #0x17]
    // 0x14cd0b0: StoreField: r3->field_1b = r0
    //     0x14cd0b0: stur            w0, [x3, #0x1b]
    // 0x14cd0b4: StoreField: r3->field_1f = r0
    //     0x14cd0b4: stur            w0, [x3, #0x1f]
    // 0x14cd0b8: StoreField: r3->field_23 = r0
    //     0x14cd0b8: stur            w0, [x3, #0x23]
    // 0x14cd0bc: StoreField: r3->field_27 = r0
    //     0x14cd0bc: stur            w0, [x3, #0x27]
    // 0x14cd0c0: StoreField: r3->field_2b = r0
    //     0x14cd0c0: stur            w0, [x3, #0x2b]
    // 0x14cd0c4: r1 = Null
    //     0x14cd0c4: mov             x1, NULL
    // 0x14cd0c8: r2 = 2
    //     0x14cd0c8: movz            x2, #0x2
    // 0x14cd0cc: r0 = AllocateArray()
    //     0x14cd0cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cd0d0: mov             x2, x0
    // 0x14cd0d4: ldur            x0, [fp, #-0x18]
    // 0x14cd0d8: stur            x2, [fp, #-0x10]
    // 0x14cd0dc: StoreField: r2->field_f = r0
    //     0x14cd0dc: stur            w0, [x2, #0xf]
    // 0x14cd0e0: r1 = <Widget>
    //     0x14cd0e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cd0e4: r0 = AllocateGrowableArray()
    //     0x14cd0e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cd0e8: mov             x2, x0
    // 0x14cd0ec: ldur            x0, [fp, #-0x10]
    // 0x14cd0f0: stur            x2, [fp, #-0x18]
    // 0x14cd0f4: StoreField: r2->field_f = r0
    //     0x14cd0f4: stur            w0, [x2, #0xf]
    // 0x14cd0f8: r0 = 2
    //     0x14cd0f8: movz            x0, #0x2
    // 0x14cd0fc: StoreField: r2->field_b = r0
    //     0x14cd0fc: stur            w0, [x2, #0xb]
    // 0x14cd100: ldur            x0, [fp, #-8]
    // 0x14cd104: LoadField: r1 = r0->field_f
    //     0x14cd104: ldur            w1, [x0, #0xf]
    // 0x14cd108: DecompressPointer r1
    //     0x14cd108: add             x1, x1, HEAP, lsl #32
    // 0x14cd10c: r0 = controller()
    //     0x14cd10c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd110: LoadField: r1 = r0->field_7f
    //     0x14cd110: ldur            w1, [x0, #0x7f]
    // 0x14cd114: DecompressPointer r1
    //     0x14cd114: add             x1, x1, HEAP, lsl #32
    // 0x14cd118: r0 = value()
    //     0x14cd118: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cd11c: LoadField: r3 = r0->field_f
    //     0x14cd11c: ldur            w3, [x0, #0xf]
    // 0x14cd120: DecompressPointer r3
    //     0x14cd120: add             x3, x3, HEAP, lsl #32
    // 0x14cd124: stur            x3, [fp, #-0x10]
    // 0x14cd128: cmp             w3, NULL
    // 0x14cd12c: b.ne            #0x14cd138
    // 0x14cd130: r0 = Null
    //     0x14cd130: mov             x0, NULL
    // 0x14cd134: b               #0x14cd18c
    // 0x14cd138: r1 = Function '<anonymous closure>':.
    //     0x14cd138: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d40] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14cd13c: ldr             x1, [x1, #0xd40]
    // 0x14cd140: r2 = Null
    //     0x14cd140: mov             x2, NULL
    // 0x14cd144: r0 = AllocateClosure()
    //     0x14cd144: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd148: ldur            x16, [fp, #-0x10]
    // 0x14cd14c: stp             x16, NULL, [SP, #8]
    // 0x14cd150: str             x0, [SP]
    // 0x14cd154: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cd154: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cd158: r0 = expand()
    //     0x14cd158: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14cd15c: mov             x1, x0
    // 0x14cd160: r0 = iterator()
    //     0x14cd160: bl              #0x7e2acc  ; [dart:_internal] ExpandIterable::iterator
    // 0x14cd164: r1 = LoadClassIdInstr(r0)
    //     0x14cd164: ldur            x1, [x0, #-1]
    //     0x14cd168: ubfx            x1, x1, #0xc, #0x14
    // 0x14cd16c: mov             x16, x0
    // 0x14cd170: mov             x0, x1
    // 0x14cd174: mov             x1, x16
    // 0x14cd178: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x14cd178: add             lr, x0, #0x5ea
    //     0x14cd17c: ldr             lr, [x21, lr, lsl #3]
    //     0x14cd180: blr             lr
    // 0x14cd184: eor             x1, x0, #0x10
    // 0x14cd188: eor             x0, x1, #0x10
    // 0x14cd18c: cmp             w0, NULL
    // 0x14cd190: b.ne            #0x14cd19c
    // 0x14cd194: ldur            x2, [fp, #-0x18]
    // 0x14cd198: b               #0x14cd4ec
    // 0x14cd19c: tbnz            w0, #4, #0x14cd4e8
    // 0x14cd1a0: ldur            x2, [fp, #-8]
    // 0x14cd1a4: LoadField: r1 = r2->field_13
    //     0x14cd1a4: ldur            w1, [x2, #0x13]
    // 0x14cd1a8: DecompressPointer r1
    //     0x14cd1a8: add             x1, x1, HEAP, lsl #32
    // 0x14cd1ac: r0 = of()
    //     0x14cd1ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cd1b0: LoadField: r1 = r0->field_87
    //     0x14cd1b0: ldur            w1, [x0, #0x87]
    // 0x14cd1b4: DecompressPointer r1
    //     0x14cd1b4: add             x1, x1, HEAP, lsl #32
    // 0x14cd1b8: LoadField: r0 = r1->field_2f
    //     0x14cd1b8: ldur            w0, [x1, #0x2f]
    // 0x14cd1bc: DecompressPointer r0
    //     0x14cd1bc: add             x0, x0, HEAP, lsl #32
    // 0x14cd1c0: cmp             w0, NULL
    // 0x14cd1c4: b.ne            #0x14cd1d0
    // 0x14cd1c8: r0 = Null
    //     0x14cd1c8: mov             x0, NULL
    // 0x14cd1cc: b               #0x14cd1f0
    // 0x14cd1d0: r16 = Instance_Color
    //     0x14cd1d0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cd1d4: r30 = 14.000000
    //     0x14cd1d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14cd1d8: ldr             lr, [lr, #0x1d8]
    // 0x14cd1dc: stp             lr, x16, [SP]
    // 0x14cd1e0: mov             x1, x0
    // 0x14cd1e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14cd1e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14cd1e8: ldr             x4, [x4, #0x9b8]
    // 0x14cd1ec: r0 = copyWith()
    //     0x14cd1ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cd1f0: ldur            x2, [fp, #-8]
    // 0x14cd1f4: stur            x0, [fp, #-0x10]
    // 0x14cd1f8: r0 = Text()
    //     0x14cd1f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cd1fc: mov             x2, x0
    // 0x14cd200: r0 = "Real images from customers"
    //     0x14cd200: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0x14cd204: ldr             x0, [x0, #0x88]
    // 0x14cd208: stur            x2, [fp, #-0x20]
    // 0x14cd20c: StoreField: r2->field_b = r0
    //     0x14cd20c: stur            w0, [x2, #0xb]
    // 0x14cd210: ldur            x0, [fp, #-0x10]
    // 0x14cd214: StoreField: r2->field_13 = r0
    //     0x14cd214: stur            w0, [x2, #0x13]
    // 0x14cd218: ldur            x0, [fp, #-8]
    // 0x14cd21c: LoadField: r1 = r0->field_f
    //     0x14cd21c: ldur            w1, [x0, #0xf]
    // 0x14cd220: DecompressPointer r1
    //     0x14cd220: add             x1, x1, HEAP, lsl #32
    // 0x14cd224: r0 = controller()
    //     0x14cd224: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd228: LoadField: r1 = r0->field_7f
    //     0x14cd228: ldur            w1, [x0, #0x7f]
    // 0x14cd22c: DecompressPointer r1
    //     0x14cd22c: add             x1, x1, HEAP, lsl #32
    // 0x14cd230: r0 = value()
    //     0x14cd230: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cd234: LoadField: r3 = r0->field_f
    //     0x14cd234: ldur            w3, [x0, #0xf]
    // 0x14cd238: DecompressPointer r3
    //     0x14cd238: add             x3, x3, HEAP, lsl #32
    // 0x14cd23c: stur            x3, [fp, #-0x10]
    // 0x14cd240: cmp             w3, NULL
    // 0x14cd244: b.ne            #0x14cd250
    // 0x14cd248: r0 = Null
    //     0x14cd248: mov             x0, NULL
    // 0x14cd24c: b               #0x14cd27c
    // 0x14cd250: r1 = Function '<anonymous closure>':.
    //     0x14cd250: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d48] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14cd254: ldr             x1, [x1, #0xd48]
    // 0x14cd258: r2 = Null
    //     0x14cd258: mov             x2, NULL
    // 0x14cd25c: r0 = AllocateClosure()
    //     0x14cd25c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd260: ldur            x16, [fp, #-0x10]
    // 0x14cd264: stp             x16, NULL, [SP, #8]
    // 0x14cd268: str             x0, [SP]
    // 0x14cd26c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cd26c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cd270: r0 = expand()
    //     0x14cd270: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14cd274: str             x0, [SP]
    // 0x14cd278: r0 = length()
    //     0x14cd278: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x14cd27c: cmp             w0, NULL
    // 0x14cd280: b.ne            #0x14cd28c
    // 0x14cd284: r0 = 0
    //     0x14cd284: movz            x0, #0
    // 0x14cd288: b               #0x14cd29c
    // 0x14cd28c: r1 = LoadInt32Instr(r0)
    //     0x14cd28c: sbfx            x1, x0, #1, #0x1f
    //     0x14cd290: tbz             w0, #0, #0x14cd298
    //     0x14cd294: ldur            x1, [x0, #7]
    // 0x14cd298: mov             x0, x1
    // 0x14cd29c: cmp             x0, #5
    // 0x14cd2a0: b.le            #0x14cd2ac
    // 0x14cd2a4: r4 = 5
    //     0x14cd2a4: movz            x4, #0x5
    // 0x14cd2a8: b               #0x14cd334
    // 0x14cd2ac: ldur            x2, [fp, #-8]
    // 0x14cd2b0: LoadField: r1 = r2->field_f
    //     0x14cd2b0: ldur            w1, [x2, #0xf]
    // 0x14cd2b4: DecompressPointer r1
    //     0x14cd2b4: add             x1, x1, HEAP, lsl #32
    // 0x14cd2b8: r0 = controller()
    //     0x14cd2b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd2bc: LoadField: r1 = r0->field_7f
    //     0x14cd2bc: ldur            w1, [x0, #0x7f]
    // 0x14cd2c0: DecompressPointer r1
    //     0x14cd2c0: add             x1, x1, HEAP, lsl #32
    // 0x14cd2c4: r0 = value()
    //     0x14cd2c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cd2c8: LoadField: r3 = r0->field_f
    //     0x14cd2c8: ldur            w3, [x0, #0xf]
    // 0x14cd2cc: DecompressPointer r3
    //     0x14cd2cc: add             x3, x3, HEAP, lsl #32
    // 0x14cd2d0: stur            x3, [fp, #-0x10]
    // 0x14cd2d4: cmp             w3, NULL
    // 0x14cd2d8: b.ne            #0x14cd2e4
    // 0x14cd2dc: r0 = Null
    //     0x14cd2dc: mov             x0, NULL
    // 0x14cd2e0: b               #0x14cd310
    // 0x14cd2e4: r1 = Function '<anonymous closure>':.
    //     0x14cd2e4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d50] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14cd2e8: ldr             x1, [x1, #0xd50]
    // 0x14cd2ec: r2 = Null
    //     0x14cd2ec: mov             x2, NULL
    // 0x14cd2f0: r0 = AllocateClosure()
    //     0x14cd2f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd2f4: ldur            x16, [fp, #-0x10]
    // 0x14cd2f8: stp             x16, NULL, [SP, #8]
    // 0x14cd2fc: str             x0, [SP]
    // 0x14cd300: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cd300: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cd304: r0 = expand()
    //     0x14cd304: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14cd308: str             x0, [SP]
    // 0x14cd30c: r0 = length()
    //     0x14cd30c: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x14cd310: cmp             w0, NULL
    // 0x14cd314: b.ne            #0x14cd320
    // 0x14cd318: r0 = 0
    //     0x14cd318: movz            x0, #0
    // 0x14cd31c: b               #0x14cd330
    // 0x14cd320: r1 = LoadInt32Instr(r0)
    //     0x14cd320: sbfx            x1, x0, #1, #0x1f
    //     0x14cd324: tbz             w0, #0, #0x14cd32c
    //     0x14cd328: ldur            x1, [x0, #7]
    // 0x14cd32c: mov             x0, x1
    // 0x14cd330: mov             x4, x0
    // 0x14cd334: ldur            x0, [fp, #-0x20]
    // 0x14cd338: ldur            x3, [fp, #-0x18]
    // 0x14cd33c: ldur            x2, [fp, #-8]
    // 0x14cd340: stur            x4, [fp, #-0x38]
    // 0x14cd344: r1 = Function '<anonymous closure>':.
    //     0x14cd344: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d58] AnonymousClosure: (0x14cfcfc), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14cd348: ldr             x1, [x1, #0xd58]
    // 0x14cd34c: r0 = AllocateClosure()
    //     0x14cd34c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd350: r1 = Function '<anonymous closure>':.
    //     0x14cd350: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d60] AnonymousClosure: (0x9ba768), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cd354: ldr             x1, [x1, #0xd60]
    // 0x14cd358: r2 = Null
    //     0x14cd358: mov             x2, NULL
    // 0x14cd35c: stur            x0, [fp, #-0x10]
    // 0x14cd360: r0 = AllocateClosure()
    //     0x14cd360: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd364: stur            x0, [fp, #-0x28]
    // 0x14cd368: r0 = ListView()
    //     0x14cd368: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14cd36c: stur            x0, [fp, #-0x30]
    // 0x14cd370: r16 = true
    //     0x14cd370: add             x16, NULL, #0x20  ; true
    // 0x14cd374: r30 = Instance_Axis
    //     0x14cd374: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14cd378: stp             lr, x16, [SP]
    // 0x14cd37c: mov             x1, x0
    // 0x14cd380: ldur            x2, [fp, #-0x10]
    // 0x14cd384: ldur            x3, [fp, #-0x38]
    // 0x14cd388: ldur            x5, [fp, #-0x28]
    // 0x14cd38c: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0x14cd38c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14cd390: ldr             x4, [x4, #0x8e8]
    // 0x14cd394: r0 = ListView.separated()
    //     0x14cd394: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14cd398: r0 = SizedBox()
    //     0x14cd398: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14cd39c: mov             x3, x0
    // 0x14cd3a0: r0 = 60.000000
    //     0x14cd3a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14cd3a4: ldr             x0, [x0, #0x110]
    // 0x14cd3a8: stur            x3, [fp, #-0x10]
    // 0x14cd3ac: StoreField: r3->field_13 = r0
    //     0x14cd3ac: stur            w0, [x3, #0x13]
    // 0x14cd3b0: ldur            x0, [fp, #-0x30]
    // 0x14cd3b4: StoreField: r3->field_b = r0
    //     0x14cd3b4: stur            w0, [x3, #0xb]
    // 0x14cd3b8: r1 = Null
    //     0x14cd3b8: mov             x1, NULL
    // 0x14cd3bc: r2 = 8
    //     0x14cd3bc: movz            x2, #0x8
    // 0x14cd3c0: r0 = AllocateArray()
    //     0x14cd3c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cd3c4: stur            x0, [fp, #-0x28]
    // 0x14cd3c8: r16 = Instance_SizedBox
    //     0x14cd3c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14cd3cc: ldr             x16, [x16, #0x9f0]
    // 0x14cd3d0: StoreField: r0->field_f = r16
    //     0x14cd3d0: stur            w16, [x0, #0xf]
    // 0x14cd3d4: ldur            x1, [fp, #-0x20]
    // 0x14cd3d8: StoreField: r0->field_13 = r1
    //     0x14cd3d8: stur            w1, [x0, #0x13]
    // 0x14cd3dc: r16 = Instance_SizedBox
    //     0x14cd3dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x14cd3e0: ldr             x16, [x16, #0x8f0]
    // 0x14cd3e4: ArrayStore: r0[0] = r16  ; List_4
    //     0x14cd3e4: stur            w16, [x0, #0x17]
    // 0x14cd3e8: ldur            x1, [fp, #-0x10]
    // 0x14cd3ec: StoreField: r0->field_1b = r1
    //     0x14cd3ec: stur            w1, [x0, #0x1b]
    // 0x14cd3f0: r1 = <Widget>
    //     0x14cd3f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cd3f4: r0 = AllocateGrowableArray()
    //     0x14cd3f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cd3f8: mov             x1, x0
    // 0x14cd3fc: ldur            x0, [fp, #-0x28]
    // 0x14cd400: stur            x1, [fp, #-0x10]
    // 0x14cd404: StoreField: r1->field_f = r0
    //     0x14cd404: stur            w0, [x1, #0xf]
    // 0x14cd408: r0 = 8
    //     0x14cd408: movz            x0, #0x8
    // 0x14cd40c: StoreField: r1->field_b = r0
    //     0x14cd40c: stur            w0, [x1, #0xb]
    // 0x14cd410: r0 = Column()
    //     0x14cd410: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14cd414: mov             x2, x0
    // 0x14cd418: r0 = Instance_Axis
    //     0x14cd418: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cd41c: stur            x2, [fp, #-0x20]
    // 0x14cd420: StoreField: r2->field_f = r0
    //     0x14cd420: stur            w0, [x2, #0xf]
    // 0x14cd424: r3 = Instance_MainAxisAlignment
    //     0x14cd424: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cd428: ldr             x3, [x3, #0xa08]
    // 0x14cd42c: StoreField: r2->field_13 = r3
    //     0x14cd42c: stur            w3, [x2, #0x13]
    // 0x14cd430: r4 = Instance_MainAxisSize
    //     0x14cd430: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cd434: ldr             x4, [x4, #0xa10]
    // 0x14cd438: ArrayStore: r2[0] = r4  ; List_4
    //     0x14cd438: stur            w4, [x2, #0x17]
    // 0x14cd43c: r5 = Instance_CrossAxisAlignment
    //     0x14cd43c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cd440: ldr             x5, [x5, #0xa18]
    // 0x14cd444: StoreField: r2->field_1b = r5
    //     0x14cd444: stur            w5, [x2, #0x1b]
    // 0x14cd448: r6 = Instance_VerticalDirection
    //     0x14cd448: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cd44c: ldr             x6, [x6, #0xa20]
    // 0x14cd450: StoreField: r2->field_23 = r6
    //     0x14cd450: stur            w6, [x2, #0x23]
    // 0x14cd454: r7 = Instance_Clip
    //     0x14cd454: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cd458: ldr             x7, [x7, #0x38]
    // 0x14cd45c: StoreField: r2->field_2b = r7
    //     0x14cd45c: stur            w7, [x2, #0x2b]
    // 0x14cd460: StoreField: r2->field_2f = rZR
    //     0x14cd460: stur            xzr, [x2, #0x2f]
    // 0x14cd464: ldur            x1, [fp, #-0x10]
    // 0x14cd468: StoreField: r2->field_b = r1
    //     0x14cd468: stur            w1, [x2, #0xb]
    // 0x14cd46c: ldur            x8, [fp, #-0x18]
    // 0x14cd470: LoadField: r1 = r8->field_b
    //     0x14cd470: ldur            w1, [x8, #0xb]
    // 0x14cd474: LoadField: r9 = r8->field_f
    //     0x14cd474: ldur            w9, [x8, #0xf]
    // 0x14cd478: DecompressPointer r9
    //     0x14cd478: add             x9, x9, HEAP, lsl #32
    // 0x14cd47c: LoadField: r10 = r9->field_b
    //     0x14cd47c: ldur            w10, [x9, #0xb]
    // 0x14cd480: r9 = LoadInt32Instr(r1)
    //     0x14cd480: sbfx            x9, x1, #1, #0x1f
    // 0x14cd484: stur            x9, [fp, #-0x38]
    // 0x14cd488: r1 = LoadInt32Instr(r10)
    //     0x14cd488: sbfx            x1, x10, #1, #0x1f
    // 0x14cd48c: cmp             x9, x1
    // 0x14cd490: b.ne            #0x14cd49c
    // 0x14cd494: mov             x1, x8
    // 0x14cd498: r0 = _growToNextCapacity()
    //     0x14cd498: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cd49c: ldur            x2, [fp, #-0x18]
    // 0x14cd4a0: ldur            x3, [fp, #-0x38]
    // 0x14cd4a4: add             x0, x3, #1
    // 0x14cd4a8: lsl             x1, x0, #1
    // 0x14cd4ac: StoreField: r2->field_b = r1
    //     0x14cd4ac: stur            w1, [x2, #0xb]
    // 0x14cd4b0: LoadField: r1 = r2->field_f
    //     0x14cd4b0: ldur            w1, [x2, #0xf]
    // 0x14cd4b4: DecompressPointer r1
    //     0x14cd4b4: add             x1, x1, HEAP, lsl #32
    // 0x14cd4b8: ldur            x0, [fp, #-0x20]
    // 0x14cd4bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14cd4bc: add             x25, x1, x3, lsl #2
    //     0x14cd4c0: add             x25, x25, #0xf
    //     0x14cd4c4: str             w0, [x25]
    //     0x14cd4c8: tbz             w0, #0, #0x14cd4e4
    //     0x14cd4cc: ldurb           w16, [x1, #-1]
    //     0x14cd4d0: ldurb           w17, [x0, #-1]
    //     0x14cd4d4: and             x16, x17, x16, lsr #2
    //     0x14cd4d8: tst             x16, HEAP, lsr #32
    //     0x14cd4dc: b.eq            #0x14cd4e4
    //     0x14cd4e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cd4e4: b               #0x14cd4ec
    // 0x14cd4e8: ldur            x2, [fp, #-0x18]
    // 0x14cd4ec: ldur            x0, [fp, #-8]
    // 0x14cd4f0: LoadField: r1 = r0->field_f
    //     0x14cd4f0: ldur            w1, [x0, #0xf]
    // 0x14cd4f4: DecompressPointer r1
    //     0x14cd4f4: add             x1, x1, HEAP, lsl #32
    // 0x14cd4f8: r0 = controller()
    //     0x14cd4f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd4fc: LoadField: r1 = r0->field_77
    //     0x14cd4fc: ldur            w1, [x0, #0x77]
    // 0x14cd500: DecompressPointer r1
    //     0x14cd500: add             x1, x1, HEAP, lsl #32
    // 0x14cd504: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cd504: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cd508: r0 = toList()
    //     0x14cd508: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cd50c: LoadField: r1 = r0->field_b
    //     0x14cd50c: ldur            w1, [x0, #0xb]
    // 0x14cd510: cbz             w1, #0x14cd930
    // 0x14cd514: ldur            x2, [fp, #-8]
    // 0x14cd518: r0 = SvgPicture()
    //     0x14cd518: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14cd51c: stur            x0, [fp, #-0x10]
    // 0x14cd520: r16 = 20.000000
    //     0x14cd520: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x14cd524: ldr             x16, [x16, #0xac8]
    // 0x14cd528: r30 = 20.000000
    //     0x14cd528: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x14cd52c: ldr             lr, [lr, #0xac8]
    // 0x14cd530: stp             lr, x16, [SP]
    // 0x14cd534: mov             x1, x0
    // 0x14cd538: r2 = "assets/images/bar_chart.svg"
    //     0x14cd538: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f8f8] "assets/images/bar_chart.svg"
    //     0x14cd53c: ldr             x2, [x2, #0x8f8]
    // 0x14cd540: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x14cd540: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x14cd544: ldr             x4, [x4, #0x900]
    // 0x14cd548: r0 = SvgPicture.asset()
    //     0x14cd548: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14cd54c: ldur            x2, [fp, #-8]
    // 0x14cd550: LoadField: r1 = r2->field_f
    //     0x14cd550: ldur            w1, [x2, #0xf]
    // 0x14cd554: DecompressPointer r1
    //     0x14cd554: add             x1, x1, HEAP, lsl #32
    // 0x14cd558: r0 = controller()
    //     0x14cd558: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd55c: LoadField: r1 = r0->field_83
    //     0x14cd55c: ldur            w1, [x0, #0x83]
    // 0x14cd560: DecompressPointer r1
    //     0x14cd560: add             x1, x1, HEAP, lsl #32
    // 0x14cd564: r0 = value()
    //     0x14cd564: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cd568: LoadField: r1 = r0->field_b
    //     0x14cd568: ldur            w1, [x0, #0xb]
    // 0x14cd56c: DecompressPointer r1
    //     0x14cd56c: add             x1, x1, HEAP, lsl #32
    // 0x14cd570: cmp             w1, NULL
    // 0x14cd574: b.ne            #0x14cd584
    // 0x14cd578: r5 = "Most Recent"
    //     0x14cd578: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f908] "Most Recent"
    //     0x14cd57c: ldr             x5, [x5, #0x908]
    // 0x14cd580: b               #0x14cd588
    // 0x14cd584: mov             x5, x1
    // 0x14cd588: ldur            x2, [fp, #-8]
    // 0x14cd58c: stur            x5, [fp, #-0x20]
    // 0x14cd590: LoadField: r1 = r2->field_f
    //     0x14cd590: ldur            w1, [x2, #0xf]
    // 0x14cd594: DecompressPointer r1
    //     0x14cd594: add             x1, x1, HEAP, lsl #32
    // 0x14cd598: r0 = controller()
    //     0x14cd598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd59c: LoadField: r1 = r0->field_7b
    //     0x14cd59c: ldur            w1, [x0, #0x7b]
    // 0x14cd5a0: DecompressPointer r1
    //     0x14cd5a0: add             x1, x1, HEAP, lsl #32
    // 0x14cd5a4: r0 = value()
    //     0x14cd5a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cd5a8: LoadField: r1 = r0->field_b
    //     0x14cd5a8: ldur            w1, [x0, #0xb]
    // 0x14cd5ac: DecompressPointer r1
    //     0x14cd5ac: add             x1, x1, HEAP, lsl #32
    // 0x14cd5b0: cmp             w1, NULL
    // 0x14cd5b4: b.ne            #0x14cd5c0
    // 0x14cd5b8: r5 = Null
    //     0x14cd5b8: mov             x5, NULL
    // 0x14cd5bc: b               #0x14cd608
    // 0x14cd5c0: LoadField: r0 = r1->field_b
    //     0x14cd5c0: ldur            w0, [x1, #0xb]
    // 0x14cd5c4: DecompressPointer r0
    //     0x14cd5c4: add             x0, x0, HEAP, lsl #32
    // 0x14cd5c8: ldur            x2, [fp, #-8]
    // 0x14cd5cc: stur            x0, [fp, #-0x28]
    // 0x14cd5d0: r1 = Function '<anonymous closure>':.
    //     0x14cd5d0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d68] AnonymousClosure: (0x9ba648), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cd5d4: ldr             x1, [x1, #0xd68]
    // 0x14cd5d8: r0 = AllocateClosure()
    //     0x14cd5d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd5dc: r16 = <DropdownMenuItem<String>>
    //     0x14cd5dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f918] TypeArguments: <DropdownMenuItem<String>>
    //     0x14cd5e0: ldr             x16, [x16, #0x918]
    // 0x14cd5e4: ldur            lr, [fp, #-0x28]
    // 0x14cd5e8: stp             lr, x16, [SP, #8]
    // 0x14cd5ec: str             x0, [SP]
    // 0x14cd5f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cd5f0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cd5f4: r0 = map()
    //     0x14cd5f4: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x14cd5f8: mov             x1, x0
    // 0x14cd5fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cd5fc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cd600: r0 = toList()
    //     0x14cd600: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x14cd604: mov             x5, x0
    // 0x14cd608: ldur            x0, [fp, #-8]
    // 0x14cd60c: ldur            x3, [fp, #-0x10]
    // 0x14cd610: ldur            x4, [fp, #-0x18]
    // 0x14cd614: mov             x2, x0
    // 0x14cd618: stur            x5, [fp, #-0x28]
    // 0x14cd61c: r1 = Function '<anonymous closure>':.
    //     0x14cd61c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d70] AnonymousClosure: (0x14cfab0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14cd620: ldr             x1, [x1, #0xd70]
    // 0x14cd624: r0 = AllocateClosure()
    //     0x14cd624: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd628: ldur            x2, [fp, #-8]
    // 0x14cd62c: r1 = Function '<anonymous closure>':.
    //     0x14cd62c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d78] AnonymousClosure: (0x9b66a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cd630: ldr             x1, [x1, #0xd78]
    // 0x14cd634: stur            x0, [fp, #-0x30]
    // 0x14cd638: r0 = AllocateClosure()
    //     0x14cd638: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd63c: r1 = <String>
    //     0x14cd63c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14cd640: stur            x0, [fp, #-0x40]
    // 0x14cd644: r0 = DropdownButton()
    //     0x14cd644: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0x14cd648: stur            x0, [fp, #-0x58]
    // 0x14cd64c: r16 = Instance_Color
    //     0x14cd64c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14cd650: r30 = Instance_Icon
    //     0x14cd650: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f930] Obj!Icon@d65ef1
    //     0x14cd654: ldr             lr, [lr, #0x930]
    // 0x14cd658: stp             lr, x16, [SP, #8]
    // 0x14cd65c: ldur            x16, [fp, #-0x40]
    // 0x14cd660: str             x16, [SP]
    // 0x14cd664: mov             x1, x0
    // 0x14cd668: ldur            x2, [fp, #-0x28]
    // 0x14cd66c: ldur            x3, [fp, #-0x30]
    // 0x14cd670: ldur            x5, [fp, #-0x20]
    // 0x14cd674: r4 = const [0, 0x7, 0x3, 0x4, dropdownColor, 0x4, icon, 0x5, onTap, 0x6, null]
    //     0x14cd674: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f938] List(11) [0, 0x7, 0x3, 0x4, "dropdownColor", 0x4, "icon", 0x5, "onTap", 0x6, Null]
    //     0x14cd678: ldr             x4, [x4, #0x938]
    // 0x14cd67c: r0 = DropdownButton()
    //     0x14cd67c: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0x14cd680: r0 = DropdownButtonHideUnderline()
    //     0x14cd680: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0x14cd684: mov             x3, x0
    // 0x14cd688: ldur            x0, [fp, #-0x58]
    // 0x14cd68c: stur            x3, [fp, #-0x20]
    // 0x14cd690: StoreField: r3->field_b = r0
    //     0x14cd690: stur            w0, [x3, #0xb]
    // 0x14cd694: r1 = Null
    //     0x14cd694: mov             x1, NULL
    // 0x14cd698: r2 = 6
    //     0x14cd698: movz            x2, #0x6
    // 0x14cd69c: r0 = AllocateArray()
    //     0x14cd69c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cd6a0: mov             x2, x0
    // 0x14cd6a4: ldur            x0, [fp, #-0x10]
    // 0x14cd6a8: stur            x2, [fp, #-0x28]
    // 0x14cd6ac: StoreField: r2->field_f = r0
    //     0x14cd6ac: stur            w0, [x2, #0xf]
    // 0x14cd6b0: r16 = Instance_SizedBox
    //     0x14cd6b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0x14cd6b4: ldr             x16, [x16, #0x940]
    // 0x14cd6b8: StoreField: r2->field_13 = r16
    //     0x14cd6b8: stur            w16, [x2, #0x13]
    // 0x14cd6bc: ldur            x0, [fp, #-0x20]
    // 0x14cd6c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14cd6c0: stur            w0, [x2, #0x17]
    // 0x14cd6c4: r1 = <Widget>
    //     0x14cd6c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cd6c8: r0 = AllocateGrowableArray()
    //     0x14cd6c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cd6cc: mov             x1, x0
    // 0x14cd6d0: ldur            x0, [fp, #-0x28]
    // 0x14cd6d4: stur            x1, [fp, #-0x10]
    // 0x14cd6d8: StoreField: r1->field_f = r0
    //     0x14cd6d8: stur            w0, [x1, #0xf]
    // 0x14cd6dc: r2 = 6
    //     0x14cd6dc: movz            x2, #0x6
    // 0x14cd6e0: StoreField: r1->field_b = r2
    //     0x14cd6e0: stur            w2, [x1, #0xb]
    // 0x14cd6e4: r0 = Row()
    //     0x14cd6e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14cd6e8: mov             x1, x0
    // 0x14cd6ec: r0 = Instance_Axis
    //     0x14cd6ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14cd6f0: stur            x1, [fp, #-0x20]
    // 0x14cd6f4: StoreField: r1->field_f = r0
    //     0x14cd6f4: stur            w0, [x1, #0xf]
    // 0x14cd6f8: r0 = Instance_MainAxisAlignment
    //     0x14cd6f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cd6fc: ldr             x0, [x0, #0xa08]
    // 0x14cd700: StoreField: r1->field_13 = r0
    //     0x14cd700: stur            w0, [x1, #0x13]
    // 0x14cd704: r2 = Instance_MainAxisSize
    //     0x14cd704: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cd708: ldr             x2, [x2, #0xa10]
    // 0x14cd70c: ArrayStore: r1[0] = r2  ; List_4
    //     0x14cd70c: stur            w2, [x1, #0x17]
    // 0x14cd710: r3 = Instance_CrossAxisAlignment
    //     0x14cd710: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cd714: ldr             x3, [x3, #0xa18]
    // 0x14cd718: StoreField: r1->field_1b = r3
    //     0x14cd718: stur            w3, [x1, #0x1b]
    // 0x14cd71c: r4 = Instance_VerticalDirection
    //     0x14cd71c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cd720: ldr             x4, [x4, #0xa20]
    // 0x14cd724: StoreField: r1->field_23 = r4
    //     0x14cd724: stur            w4, [x1, #0x23]
    // 0x14cd728: r5 = Instance_Clip
    //     0x14cd728: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cd72c: ldr             x5, [x5, #0x38]
    // 0x14cd730: StoreField: r1->field_2b = r5
    //     0x14cd730: stur            w5, [x1, #0x2b]
    // 0x14cd734: StoreField: r1->field_2f = rZR
    //     0x14cd734: stur            xzr, [x1, #0x2f]
    // 0x14cd738: ldur            x6, [fp, #-0x10]
    // 0x14cd73c: StoreField: r1->field_b = r6
    //     0x14cd73c: stur            w6, [x1, #0xb]
    // 0x14cd740: r0 = SizedBox()
    //     0x14cd740: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14cd744: mov             x1, x0
    // 0x14cd748: r0 = 155.000000
    //     0x14cd748: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f948] 155
    //     0x14cd74c: ldr             x0, [x0, #0x948]
    // 0x14cd750: stur            x1, [fp, #-0x10]
    // 0x14cd754: StoreField: r1->field_f = r0
    //     0x14cd754: stur            w0, [x1, #0xf]
    // 0x14cd758: ldur            x0, [fp, #-0x20]
    // 0x14cd75c: StoreField: r1->field_b = r0
    //     0x14cd75c: stur            w0, [x1, #0xb]
    // 0x14cd760: r0 = Align()
    //     0x14cd760: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14cd764: mov             x2, x0
    // 0x14cd768: r0 = Instance_Alignment
    //     0x14cd768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14cd76c: ldr             x0, [x0, #0x950]
    // 0x14cd770: stur            x2, [fp, #-0x20]
    // 0x14cd774: StoreField: r2->field_f = r0
    //     0x14cd774: stur            w0, [x2, #0xf]
    // 0x14cd778: ldur            x0, [fp, #-0x10]
    // 0x14cd77c: StoreField: r2->field_b = r0
    //     0x14cd77c: stur            w0, [x2, #0xb]
    // 0x14cd780: ldur            x0, [fp, #-8]
    // 0x14cd784: LoadField: r1 = r0->field_f
    //     0x14cd784: ldur            w1, [x0, #0xf]
    // 0x14cd788: DecompressPointer r1
    //     0x14cd788: add             x1, x1, HEAP, lsl #32
    // 0x14cd78c: r0 = controller()
    //     0x14cd78c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cd790: LoadField: r1 = r0->field_77
    //     0x14cd790: ldur            w1, [x0, #0x77]
    // 0x14cd794: DecompressPointer r1
    //     0x14cd794: add             x1, x1, HEAP, lsl #32
    // 0x14cd798: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cd798: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cd79c: r0 = toList()
    //     0x14cd79c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cd7a0: LoadField: r1 = r0->field_b
    //     0x14cd7a0: ldur            w1, [x0, #0xb]
    // 0x14cd7a4: r3 = LoadInt32Instr(r1)
    //     0x14cd7a4: sbfx            x3, x1, #1, #0x1f
    // 0x14cd7a8: stur            x3, [fp, #-0x38]
    // 0x14cd7ac: r1 = Function '<anonymous closure>':.
    //     0x14cd7ac: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d80] AnonymousClosure: (0xa928e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0x14cd7b0: ldr             x1, [x1, #0xd80]
    // 0x14cd7b4: r2 = Null
    //     0x14cd7b4: mov             x2, NULL
    // 0x14cd7b8: r0 = AllocateClosure()
    //     0x14cd7b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd7bc: ldur            x2, [fp, #-8]
    // 0x14cd7c0: r1 = Function '<anonymous closure>':.
    //     0x14cd7c0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d88] AnonymousClosure: (0x14cda00), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14cd7c4: ldr             x1, [x1, #0xd88]
    // 0x14cd7c8: stur            x0, [fp, #-8]
    // 0x14cd7cc: r0 = AllocateClosure()
    //     0x14cd7cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cd7d0: stur            x0, [fp, #-0x10]
    // 0x14cd7d4: r0 = ListView()
    //     0x14cd7d4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14cd7d8: stur            x0, [fp, #-0x28]
    // 0x14cd7dc: r16 = Instance_NeverScrollableScrollPhysics
    //     0x14cd7dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14cd7e0: ldr             x16, [x16, #0x1c8]
    // 0x14cd7e4: r30 = true
    //     0x14cd7e4: add             lr, NULL, #0x20  ; true
    // 0x14cd7e8: stp             lr, x16, [SP]
    // 0x14cd7ec: mov             x1, x0
    // 0x14cd7f0: ldur            x2, [fp, #-0x10]
    // 0x14cd7f4: ldur            x3, [fp, #-0x38]
    // 0x14cd7f8: ldur            x5, [fp, #-8]
    // 0x14cd7fc: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0x14cd7fc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0x14cd800: ldr             x4, [x4, #0x968]
    // 0x14cd804: r0 = ListView.separated()
    //     0x14cd804: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14cd808: r1 = Null
    //     0x14cd808: mov             x1, NULL
    // 0x14cd80c: r2 = 6
    //     0x14cd80c: movz            x2, #0x6
    // 0x14cd810: r0 = AllocateArray()
    //     0x14cd810: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cd814: mov             x2, x0
    // 0x14cd818: ldur            x0, [fp, #-0x20]
    // 0x14cd81c: stur            x2, [fp, #-8]
    // 0x14cd820: StoreField: r2->field_f = r0
    //     0x14cd820: stur            w0, [x2, #0xf]
    // 0x14cd824: r16 = Instance_SizedBox
    //     0x14cd824: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a60] Obj!SizedBox@d68041
    //     0x14cd828: ldr             x16, [x16, #0xa60]
    // 0x14cd82c: StoreField: r2->field_13 = r16
    //     0x14cd82c: stur            w16, [x2, #0x13]
    // 0x14cd830: ldur            x0, [fp, #-0x28]
    // 0x14cd834: ArrayStore: r2[0] = r0  ; List_4
    //     0x14cd834: stur            w0, [x2, #0x17]
    // 0x14cd838: r1 = <Widget>
    //     0x14cd838: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cd83c: r0 = AllocateGrowableArray()
    //     0x14cd83c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cd840: mov             x1, x0
    // 0x14cd844: ldur            x0, [fp, #-8]
    // 0x14cd848: stur            x1, [fp, #-0x10]
    // 0x14cd84c: StoreField: r1->field_f = r0
    //     0x14cd84c: stur            w0, [x1, #0xf]
    // 0x14cd850: r0 = 6
    //     0x14cd850: movz            x0, #0x6
    // 0x14cd854: StoreField: r1->field_b = r0
    //     0x14cd854: stur            w0, [x1, #0xb]
    // 0x14cd858: r0 = Column()
    //     0x14cd858: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14cd85c: mov             x2, x0
    // 0x14cd860: r0 = Instance_Axis
    //     0x14cd860: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cd864: stur            x2, [fp, #-8]
    // 0x14cd868: StoreField: r2->field_f = r0
    //     0x14cd868: stur            w0, [x2, #0xf]
    // 0x14cd86c: r3 = Instance_MainAxisAlignment
    //     0x14cd86c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cd870: ldr             x3, [x3, #0xa08]
    // 0x14cd874: StoreField: r2->field_13 = r3
    //     0x14cd874: stur            w3, [x2, #0x13]
    // 0x14cd878: r4 = Instance_MainAxisSize
    //     0x14cd878: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cd87c: ldr             x4, [x4, #0xa10]
    // 0x14cd880: ArrayStore: r2[0] = r4  ; List_4
    //     0x14cd880: stur            w4, [x2, #0x17]
    // 0x14cd884: r5 = Instance_CrossAxisAlignment
    //     0x14cd884: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cd888: ldr             x5, [x5, #0xa18]
    // 0x14cd88c: StoreField: r2->field_1b = r5
    //     0x14cd88c: stur            w5, [x2, #0x1b]
    // 0x14cd890: r6 = Instance_VerticalDirection
    //     0x14cd890: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cd894: ldr             x6, [x6, #0xa20]
    // 0x14cd898: StoreField: r2->field_23 = r6
    //     0x14cd898: stur            w6, [x2, #0x23]
    // 0x14cd89c: r7 = Instance_Clip
    //     0x14cd89c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cd8a0: ldr             x7, [x7, #0x38]
    // 0x14cd8a4: StoreField: r2->field_2b = r7
    //     0x14cd8a4: stur            w7, [x2, #0x2b]
    // 0x14cd8a8: StoreField: r2->field_2f = rZR
    //     0x14cd8a8: stur            xzr, [x2, #0x2f]
    // 0x14cd8ac: ldur            x1, [fp, #-0x10]
    // 0x14cd8b0: StoreField: r2->field_b = r1
    //     0x14cd8b0: stur            w1, [x2, #0xb]
    // 0x14cd8b4: ldur            x8, [fp, #-0x18]
    // 0x14cd8b8: LoadField: r1 = r8->field_b
    //     0x14cd8b8: ldur            w1, [x8, #0xb]
    // 0x14cd8bc: LoadField: r9 = r8->field_f
    //     0x14cd8bc: ldur            w9, [x8, #0xf]
    // 0x14cd8c0: DecompressPointer r9
    //     0x14cd8c0: add             x9, x9, HEAP, lsl #32
    // 0x14cd8c4: LoadField: r10 = r9->field_b
    //     0x14cd8c4: ldur            w10, [x9, #0xb]
    // 0x14cd8c8: r9 = LoadInt32Instr(r1)
    //     0x14cd8c8: sbfx            x9, x1, #1, #0x1f
    // 0x14cd8cc: stur            x9, [fp, #-0x38]
    // 0x14cd8d0: r1 = LoadInt32Instr(r10)
    //     0x14cd8d0: sbfx            x1, x10, #1, #0x1f
    // 0x14cd8d4: cmp             x9, x1
    // 0x14cd8d8: b.ne            #0x14cd8e4
    // 0x14cd8dc: mov             x1, x8
    // 0x14cd8e0: r0 = _growToNextCapacity()
    //     0x14cd8e0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cd8e4: ldur            x2, [fp, #-0x18]
    // 0x14cd8e8: ldur            x3, [fp, #-0x38]
    // 0x14cd8ec: add             x0, x3, #1
    // 0x14cd8f0: lsl             x1, x0, #1
    // 0x14cd8f4: StoreField: r2->field_b = r1
    //     0x14cd8f4: stur            w1, [x2, #0xb]
    // 0x14cd8f8: LoadField: r1 = r2->field_f
    //     0x14cd8f8: ldur            w1, [x2, #0xf]
    // 0x14cd8fc: DecompressPointer r1
    //     0x14cd8fc: add             x1, x1, HEAP, lsl #32
    // 0x14cd900: ldur            x0, [fp, #-8]
    // 0x14cd904: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14cd904: add             x25, x1, x3, lsl #2
    //     0x14cd908: add             x25, x25, #0xf
    //     0x14cd90c: str             w0, [x25]
    //     0x14cd910: tbz             w0, #0, #0x14cd92c
    //     0x14cd914: ldurb           w16, [x1, #-1]
    //     0x14cd918: ldurb           w17, [x0, #-1]
    //     0x14cd91c: and             x16, x17, x16, lsr #2
    //     0x14cd920: tst             x16, HEAP, lsr #32
    //     0x14cd924: b.eq            #0x14cd92c
    //     0x14cd928: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cd92c: b               #0x14cd934
    // 0x14cd930: ldur            x2, [fp, #-0x18]
    // 0x14cd934: r0 = Column()
    //     0x14cd934: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14cd938: mov             x1, x0
    // 0x14cd93c: r0 = Instance_Axis
    //     0x14cd93c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cd940: stur            x1, [fp, #-8]
    // 0x14cd944: StoreField: r1->field_f = r0
    //     0x14cd944: stur            w0, [x1, #0xf]
    // 0x14cd948: r2 = Instance_MainAxisAlignment
    //     0x14cd948: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cd94c: ldr             x2, [x2, #0xa08]
    // 0x14cd950: StoreField: r1->field_13 = r2
    //     0x14cd950: stur            w2, [x1, #0x13]
    // 0x14cd954: r2 = Instance_MainAxisSize
    //     0x14cd954: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cd958: ldr             x2, [x2, #0xa10]
    // 0x14cd95c: ArrayStore: r1[0] = r2  ; List_4
    //     0x14cd95c: stur            w2, [x1, #0x17]
    // 0x14cd960: r2 = Instance_CrossAxisAlignment
    //     0x14cd960: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cd964: ldr             x2, [x2, #0xa18]
    // 0x14cd968: StoreField: r1->field_1b = r2
    //     0x14cd968: stur            w2, [x1, #0x1b]
    // 0x14cd96c: r2 = Instance_VerticalDirection
    //     0x14cd96c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cd970: ldr             x2, [x2, #0xa20]
    // 0x14cd974: StoreField: r1->field_23 = r2
    //     0x14cd974: stur            w2, [x1, #0x23]
    // 0x14cd978: r2 = Instance_Clip
    //     0x14cd978: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cd97c: ldr             x2, [x2, #0x38]
    // 0x14cd980: StoreField: r1->field_2b = r2
    //     0x14cd980: stur            w2, [x1, #0x2b]
    // 0x14cd984: StoreField: r1->field_2f = rZR
    //     0x14cd984: stur            xzr, [x1, #0x2f]
    // 0x14cd988: ldur            x2, [fp, #-0x18]
    // 0x14cd98c: StoreField: r1->field_b = r2
    //     0x14cd98c: stur            w2, [x1, #0xb]
    // 0x14cd990: r0 = Padding()
    //     0x14cd990: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14cd994: mov             x1, x0
    // 0x14cd998: r0 = Instance_EdgeInsets
    //     0x14cd998: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14cd99c: ldr             x0, [x0, #0x1f0]
    // 0x14cd9a0: stur            x1, [fp, #-0x10]
    // 0x14cd9a4: StoreField: r1->field_f = r0
    //     0x14cd9a4: stur            w0, [x1, #0xf]
    // 0x14cd9a8: ldur            x0, [fp, #-8]
    // 0x14cd9ac: StoreField: r1->field_b = r0
    //     0x14cd9ac: stur            w0, [x1, #0xb]
    // 0x14cd9b0: r0 = SingleChildScrollView()
    //     0x14cd9b0: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14cd9b4: r1 = Instance_Axis
    //     0x14cd9b4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cd9b8: StoreField: r0->field_b = r1
    //     0x14cd9b8: stur            w1, [x0, #0xb]
    // 0x14cd9bc: r1 = false
    //     0x14cd9bc: add             x1, NULL, #0x30  ; false
    // 0x14cd9c0: StoreField: r0->field_f = r1
    //     0x14cd9c0: stur            w1, [x0, #0xf]
    // 0x14cd9c4: ldur            x1, [fp, #-0x10]
    // 0x14cd9c8: StoreField: r0->field_23 = r1
    //     0x14cd9c8: stur            w1, [x0, #0x23]
    // 0x14cd9cc: r1 = Instance_DragStartBehavior
    //     0x14cd9cc: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14cd9d0: StoreField: r0->field_27 = r1
    //     0x14cd9d0: stur            w1, [x0, #0x27]
    // 0x14cd9d4: r1 = Instance_Clip
    //     0x14cd9d4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14cd9d8: ldr             x1, [x1, #0x7e0]
    // 0x14cd9dc: StoreField: r0->field_2b = r1
    //     0x14cd9dc: stur            w1, [x0, #0x2b]
    // 0x14cd9e0: r1 = Instance_HitTestBehavior
    //     0x14cd9e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14cd9e4: ldr             x1, [x1, #0x288]
    // 0x14cd9e8: StoreField: r0->field_2f = r1
    //     0x14cd9e8: stur            w1, [x0, #0x2f]
    // 0x14cd9ec: LeaveFrame
    //     0x14cd9ec: mov             SP, fp
    //     0x14cd9f0: ldp             fp, lr, [SP], #0x10
    // 0x14cd9f4: ret
    //     0x14cd9f4: ret             
    // 0x14cd9f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cd9f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cd9fc: b               #0x14cb030
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14cda00, size: 0x13f0
    // 0x14cda00: EnterFrame
    //     0x14cda00: stp             fp, lr, [SP, #-0x10]!
    //     0x14cda04: mov             fp, SP
    // 0x14cda08: AllocStack(0x80)
    //     0x14cda08: sub             SP, SP, #0x80
    // 0x14cda0c: SetupParameters()
    //     0x14cda0c: ldr             x0, [fp, #0x20]
    //     0x14cda10: ldur            w1, [x0, #0x17]
    //     0x14cda14: add             x1, x1, HEAP, lsl #32
    //     0x14cda18: stur            x1, [fp, #-8]
    // 0x14cda1c: CheckStackOverflow
    //     0x14cda1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cda20: cmp             SP, x16
    //     0x14cda24: b.ls            #0x14ced98
    // 0x14cda28: r1 = 2
    //     0x14cda28: movz            x1, #0x2
    // 0x14cda2c: r0 = AllocateContext()
    //     0x14cda2c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14cda30: mov             x1, x0
    // 0x14cda34: ldur            x0, [fp, #-8]
    // 0x14cda38: stur            x1, [fp, #-0x10]
    // 0x14cda3c: StoreField: r1->field_b = r0
    //     0x14cda3c: stur            w0, [x1, #0xb]
    // 0x14cda40: ldr             x2, [fp, #0x18]
    // 0x14cda44: StoreField: r1->field_f = r2
    //     0x14cda44: stur            w2, [x1, #0xf]
    // 0x14cda48: ldr             x2, [fp, #0x10]
    // 0x14cda4c: StoreField: r1->field_13 = r2
    //     0x14cda4c: stur            w2, [x1, #0x13]
    // 0x14cda50: r0 = Radius()
    //     0x14cda50: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14cda54: d0 = 15.000000
    //     0x14cda54: fmov            d0, #15.00000000
    // 0x14cda58: stur            x0, [fp, #-0x18]
    // 0x14cda5c: StoreField: r0->field_7 = d0
    //     0x14cda5c: stur            d0, [x0, #7]
    // 0x14cda60: StoreField: r0->field_f = d0
    //     0x14cda60: stur            d0, [x0, #0xf]
    // 0x14cda64: r0 = BorderRadius()
    //     0x14cda64: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14cda68: mov             x1, x0
    // 0x14cda6c: ldur            x0, [fp, #-0x18]
    // 0x14cda70: stur            x1, [fp, #-0x20]
    // 0x14cda74: StoreField: r1->field_7 = r0
    //     0x14cda74: stur            w0, [x1, #7]
    // 0x14cda78: StoreField: r1->field_b = r0
    //     0x14cda78: stur            w0, [x1, #0xb]
    // 0x14cda7c: StoreField: r1->field_f = r0
    //     0x14cda7c: stur            w0, [x1, #0xf]
    // 0x14cda80: StoreField: r1->field_13 = r0
    //     0x14cda80: stur            w0, [x1, #0x13]
    // 0x14cda84: r0 = BoxDecoration()
    //     0x14cda84: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14cda88: mov             x2, x0
    // 0x14cda8c: r0 = Instance_Color
    //     0x14cda8c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14cda90: stur            x2, [fp, #-0x18]
    // 0x14cda94: StoreField: r2->field_7 = r0
    //     0x14cda94: stur            w0, [x2, #7]
    // 0x14cda98: ldur            x0, [fp, #-0x20]
    // 0x14cda9c: StoreField: r2->field_13 = r0
    //     0x14cda9c: stur            w0, [x2, #0x13]
    // 0x14cdaa0: r0 = Instance_BoxShape
    //     0x14cdaa0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14cdaa4: ldr             x0, [x0, #0x80]
    // 0x14cdaa8: StoreField: r2->field_23 = r0
    //     0x14cdaa8: stur            w0, [x2, #0x23]
    // 0x14cdaac: r1 = Instance_Color
    //     0x14cdaac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cdab0: d0 = 0.050000
    //     0x14cdab0: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x14cdab4: r0 = withOpacity()
    //     0x14cdab4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14cdab8: stur            x0, [fp, #-0x20]
    // 0x14cdabc: r0 = BoxDecoration()
    //     0x14cdabc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14cdac0: mov             x2, x0
    // 0x14cdac4: ldur            x0, [fp, #-0x20]
    // 0x14cdac8: stur            x2, [fp, #-0x28]
    // 0x14cdacc: StoreField: r2->field_7 = r0
    //     0x14cdacc: stur            w0, [x2, #7]
    // 0x14cdad0: r0 = Instance_BoxShape
    //     0x14cdad0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14cdad4: ldr             x0, [x0, #0x970]
    // 0x14cdad8: StoreField: r2->field_23 = r0
    //     0x14cdad8: stur            w0, [x2, #0x23]
    // 0x14cdadc: ldur            x0, [fp, #-8]
    // 0x14cdae0: LoadField: r1 = r0->field_f
    //     0x14cdae0: ldur            w1, [x0, #0xf]
    // 0x14cdae4: DecompressPointer r1
    //     0x14cdae4: add             x1, x1, HEAP, lsl #32
    // 0x14cdae8: r0 = controller()
    //     0x14cdae8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cdaec: LoadField: r1 = r0->field_77
    //     0x14cdaec: ldur            w1, [x0, #0x77]
    // 0x14cdaf0: DecompressPointer r1
    //     0x14cdaf0: add             x1, x1, HEAP, lsl #32
    // 0x14cdaf4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cdaf4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cdaf8: r0 = toList()
    //     0x14cdaf8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cdafc: mov             x3, x0
    // 0x14cdb00: ldur            x2, [fp, #-0x10]
    // 0x14cdb04: LoadField: r0 = r2->field_13
    //     0x14cdb04: ldur            w0, [x2, #0x13]
    // 0x14cdb08: DecompressPointer r0
    //     0x14cdb08: add             x0, x0, HEAP, lsl #32
    // 0x14cdb0c: LoadField: r1 = r3->field_b
    //     0x14cdb0c: ldur            w1, [x3, #0xb]
    // 0x14cdb10: r4 = LoadInt32Instr(r0)
    //     0x14cdb10: sbfx            x4, x0, #1, #0x1f
    //     0x14cdb14: tbz             w0, #0, #0x14cdb1c
    //     0x14cdb18: ldur            x4, [x0, #7]
    // 0x14cdb1c: r0 = LoadInt32Instr(r1)
    //     0x14cdb1c: sbfx            x0, x1, #1, #0x1f
    // 0x14cdb20: mov             x1, x4
    // 0x14cdb24: cmp             x1, x0
    // 0x14cdb28: b.hs            #0x14ceda0
    // 0x14cdb2c: LoadField: r0 = r3->field_f
    //     0x14cdb2c: ldur            w0, [x3, #0xf]
    // 0x14cdb30: DecompressPointer r0
    //     0x14cdb30: add             x0, x0, HEAP, lsl #32
    // 0x14cdb34: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cdb34: add             x16, x0, x4, lsl #2
    //     0x14cdb38: ldur            w1, [x16, #0xf]
    // 0x14cdb3c: DecompressPointer r1
    //     0x14cdb3c: add             x1, x1, HEAP, lsl #32
    // 0x14cdb40: cmp             w1, NULL
    // 0x14cdb44: b.ne            #0x14cdb50
    // 0x14cdb48: r0 = Null
    //     0x14cdb48: mov             x0, NULL
    // 0x14cdb4c: b               #0x14cdb8c
    // 0x14cdb50: LoadField: r0 = r1->field_7
    //     0x14cdb50: ldur            w0, [x1, #7]
    // 0x14cdb54: DecompressPointer r0
    //     0x14cdb54: add             x0, x0, HEAP, lsl #32
    // 0x14cdb58: cmp             w0, NULL
    // 0x14cdb5c: b.ne            #0x14cdb68
    // 0x14cdb60: r0 = Null
    //     0x14cdb60: mov             x0, NULL
    // 0x14cdb64: b               #0x14cdb8c
    // 0x14cdb68: stp             xzr, x0, [SP]
    // 0x14cdb6c: r0 = []()
    //     0x14cdb6c: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0x14cdb70: r1 = LoadClassIdInstr(r0)
    //     0x14cdb70: ldur            x1, [x0, #-1]
    //     0x14cdb74: ubfx            x1, x1, #0xc, #0x14
    // 0x14cdb78: str             x0, [SP]
    // 0x14cdb7c: mov             x0, x1
    // 0x14cdb80: r0 = GDT[cid_x0 + -0x1000]()
    //     0x14cdb80: sub             lr, x0, #1, lsl #12
    //     0x14cdb84: ldr             lr, [x21, lr, lsl #3]
    //     0x14cdb88: blr             lr
    // 0x14cdb8c: cmp             w0, NULL
    // 0x14cdb90: b.ne            #0x14cdb9c
    // 0x14cdb94: r3 = ""
    //     0x14cdb94: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cdb98: b               #0x14cdba0
    // 0x14cdb9c: mov             x3, x0
    // 0x14cdba0: ldur            x0, [fp, #-8]
    // 0x14cdba4: ldur            x2, [fp, #-0x10]
    // 0x14cdba8: stur            x3, [fp, #-0x20]
    // 0x14cdbac: LoadField: r1 = r2->field_f
    //     0x14cdbac: ldur            w1, [x2, #0xf]
    // 0x14cdbb0: DecompressPointer r1
    //     0x14cdbb0: add             x1, x1, HEAP, lsl #32
    // 0x14cdbb4: r0 = of()
    //     0x14cdbb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cdbb8: LoadField: r1 = r0->field_87
    //     0x14cdbb8: ldur            w1, [x0, #0x87]
    // 0x14cdbbc: DecompressPointer r1
    //     0x14cdbbc: add             x1, x1, HEAP, lsl #32
    // 0x14cdbc0: LoadField: r0 = r1->field_7
    //     0x14cdbc0: ldur            w0, [x1, #7]
    // 0x14cdbc4: DecompressPointer r0
    //     0x14cdbc4: add             x0, x0, HEAP, lsl #32
    // 0x14cdbc8: stur            x0, [fp, #-0x30]
    // 0x14cdbcc: r1 = Instance_Color
    //     0x14cdbcc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cdbd0: d0 = 0.500000
    //     0x14cdbd0: fmov            d0, #0.50000000
    // 0x14cdbd4: r0 = withOpacity()
    //     0x14cdbd4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14cdbd8: r16 = 16.000000
    //     0x14cdbd8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14cdbdc: ldr             x16, [x16, #0x188]
    // 0x14cdbe0: stp             x0, x16, [SP]
    // 0x14cdbe4: ldur            x1, [fp, #-0x30]
    // 0x14cdbe8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14cdbe8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14cdbec: ldr             x4, [x4, #0xaa0]
    // 0x14cdbf0: r0 = copyWith()
    //     0x14cdbf0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cdbf4: stur            x0, [fp, #-0x30]
    // 0x14cdbf8: r0 = Text()
    //     0x14cdbf8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cdbfc: mov             x1, x0
    // 0x14cdc00: ldur            x0, [fp, #-0x20]
    // 0x14cdc04: stur            x1, [fp, #-0x38]
    // 0x14cdc08: StoreField: r1->field_b = r0
    //     0x14cdc08: stur            w0, [x1, #0xb]
    // 0x14cdc0c: ldur            x0, [fp, #-0x30]
    // 0x14cdc10: StoreField: r1->field_13 = r0
    //     0x14cdc10: stur            w0, [x1, #0x13]
    // 0x14cdc14: r0 = Center()
    //     0x14cdc14: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14cdc18: mov             x1, x0
    // 0x14cdc1c: r0 = Instance_Alignment
    //     0x14cdc1c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14cdc20: ldr             x0, [x0, #0xb10]
    // 0x14cdc24: stur            x1, [fp, #-0x20]
    // 0x14cdc28: StoreField: r1->field_f = r0
    //     0x14cdc28: stur            w0, [x1, #0xf]
    // 0x14cdc2c: ldur            x0, [fp, #-0x38]
    // 0x14cdc30: StoreField: r1->field_b = r0
    //     0x14cdc30: stur            w0, [x1, #0xb]
    // 0x14cdc34: r0 = Container()
    //     0x14cdc34: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14cdc38: stur            x0, [fp, #-0x30]
    // 0x14cdc3c: r16 = 34.000000
    //     0x14cdc3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0x14cdc40: ldr             x16, [x16, #0x978]
    // 0x14cdc44: r30 = 34.000000
    //     0x14cdc44: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0x14cdc48: ldr             lr, [lr, #0x978]
    // 0x14cdc4c: stp             lr, x16, [SP, #0x18]
    // 0x14cdc50: r16 = Instance_EdgeInsets
    //     0x14cdc50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14cdc54: ldr             x16, [x16, #0x980]
    // 0x14cdc58: ldur            lr, [fp, #-0x28]
    // 0x14cdc5c: stp             lr, x16, [SP, #8]
    // 0x14cdc60: ldur            x16, [fp, #-0x20]
    // 0x14cdc64: str             x16, [SP]
    // 0x14cdc68: mov             x1, x0
    // 0x14cdc6c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0x14cdc6c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0x14cdc70: ldr             x4, [x4, #0x988]
    // 0x14cdc74: r0 = Container()
    //     0x14cdc74: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14cdc78: ldur            x0, [fp, #-8]
    // 0x14cdc7c: LoadField: r1 = r0->field_f
    //     0x14cdc7c: ldur            w1, [x0, #0xf]
    // 0x14cdc80: DecompressPointer r1
    //     0x14cdc80: add             x1, x1, HEAP, lsl #32
    // 0x14cdc84: r0 = controller()
    //     0x14cdc84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cdc88: LoadField: r1 = r0->field_77
    //     0x14cdc88: ldur            w1, [x0, #0x77]
    // 0x14cdc8c: DecompressPointer r1
    //     0x14cdc8c: add             x1, x1, HEAP, lsl #32
    // 0x14cdc90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cdc90: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cdc94: r0 = toList()
    //     0x14cdc94: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cdc98: mov             x3, x0
    // 0x14cdc9c: ldur            x2, [fp, #-0x10]
    // 0x14cdca0: LoadField: r0 = r2->field_13
    //     0x14cdca0: ldur            w0, [x2, #0x13]
    // 0x14cdca4: DecompressPointer r0
    //     0x14cdca4: add             x0, x0, HEAP, lsl #32
    // 0x14cdca8: LoadField: r1 = r3->field_b
    //     0x14cdca8: ldur            w1, [x3, #0xb]
    // 0x14cdcac: r4 = LoadInt32Instr(r0)
    //     0x14cdcac: sbfx            x4, x0, #1, #0x1f
    //     0x14cdcb0: tbz             w0, #0, #0x14cdcb8
    //     0x14cdcb4: ldur            x4, [x0, #7]
    // 0x14cdcb8: r0 = LoadInt32Instr(r1)
    //     0x14cdcb8: sbfx            x0, x1, #1, #0x1f
    // 0x14cdcbc: mov             x1, x4
    // 0x14cdcc0: cmp             x1, x0
    // 0x14cdcc4: b.hs            #0x14ceda4
    // 0x14cdcc8: LoadField: r0 = r3->field_f
    //     0x14cdcc8: ldur            w0, [x3, #0xf]
    // 0x14cdccc: DecompressPointer r0
    //     0x14cdccc: add             x0, x0, HEAP, lsl #32
    // 0x14cdcd0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cdcd0: add             x16, x0, x4, lsl #2
    //     0x14cdcd4: ldur            w1, [x16, #0xf]
    // 0x14cdcd8: DecompressPointer r1
    //     0x14cdcd8: add             x1, x1, HEAP, lsl #32
    // 0x14cdcdc: cmp             w1, NULL
    // 0x14cdce0: b.ne            #0x14cdcec
    // 0x14cdce4: r0 = Null
    //     0x14cdce4: mov             x0, NULL
    // 0x14cdce8: b               #0x14cdcf4
    // 0x14cdcec: LoadField: r0 = r1->field_7
    //     0x14cdcec: ldur            w0, [x1, #7]
    // 0x14cdcf0: DecompressPointer r0
    //     0x14cdcf0: add             x0, x0, HEAP, lsl #32
    // 0x14cdcf4: cmp             w0, NULL
    // 0x14cdcf8: b.ne            #0x14cdd04
    // 0x14cdcfc: r3 = ""
    //     0x14cdcfc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cdd00: b               #0x14cdd08
    // 0x14cdd04: mov             x3, x0
    // 0x14cdd08: ldur            x0, [fp, #-8]
    // 0x14cdd0c: stur            x3, [fp, #-0x20]
    // 0x14cdd10: LoadField: r1 = r2->field_f
    //     0x14cdd10: ldur            w1, [x2, #0xf]
    // 0x14cdd14: DecompressPointer r1
    //     0x14cdd14: add             x1, x1, HEAP, lsl #32
    // 0x14cdd18: r0 = of()
    //     0x14cdd18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cdd1c: LoadField: r1 = r0->field_87
    //     0x14cdd1c: ldur            w1, [x0, #0x87]
    // 0x14cdd20: DecompressPointer r1
    //     0x14cdd20: add             x1, x1, HEAP, lsl #32
    // 0x14cdd24: LoadField: r0 = r1->field_7
    //     0x14cdd24: ldur            w0, [x1, #7]
    // 0x14cdd28: DecompressPointer r0
    //     0x14cdd28: add             x0, x0, HEAP, lsl #32
    // 0x14cdd2c: r16 = 14.000000
    //     0x14cdd2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14cdd30: ldr             x16, [x16, #0x1d8]
    // 0x14cdd34: r30 = Instance_Color
    //     0x14cdd34: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cdd38: stp             lr, x16, [SP]
    // 0x14cdd3c: mov             x1, x0
    // 0x14cdd40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14cdd40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14cdd44: ldr             x4, [x4, #0xaa0]
    // 0x14cdd48: r0 = copyWith()
    //     0x14cdd48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cdd4c: stur            x0, [fp, #-0x28]
    // 0x14cdd50: r0 = Text()
    //     0x14cdd50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cdd54: mov             x2, x0
    // 0x14cdd58: ldur            x0, [fp, #-0x20]
    // 0x14cdd5c: stur            x2, [fp, #-0x38]
    // 0x14cdd60: StoreField: r2->field_b = r0
    //     0x14cdd60: stur            w0, [x2, #0xb]
    // 0x14cdd64: ldur            x0, [fp, #-0x28]
    // 0x14cdd68: StoreField: r2->field_13 = r0
    //     0x14cdd68: stur            w0, [x2, #0x13]
    // 0x14cdd6c: ldur            x0, [fp, #-8]
    // 0x14cdd70: LoadField: r1 = r0->field_f
    //     0x14cdd70: ldur            w1, [x0, #0xf]
    // 0x14cdd74: DecompressPointer r1
    //     0x14cdd74: add             x1, x1, HEAP, lsl #32
    // 0x14cdd78: r0 = controller()
    //     0x14cdd78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cdd7c: LoadField: r1 = r0->field_77
    //     0x14cdd7c: ldur            w1, [x0, #0x77]
    // 0x14cdd80: DecompressPointer r1
    //     0x14cdd80: add             x1, x1, HEAP, lsl #32
    // 0x14cdd84: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cdd84: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cdd88: r0 = toList()
    //     0x14cdd88: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cdd8c: mov             x3, x0
    // 0x14cdd90: ldur            x2, [fp, #-0x10]
    // 0x14cdd94: LoadField: r0 = r2->field_13
    //     0x14cdd94: ldur            w0, [x2, #0x13]
    // 0x14cdd98: DecompressPointer r0
    //     0x14cdd98: add             x0, x0, HEAP, lsl #32
    // 0x14cdd9c: LoadField: r1 = r3->field_b
    //     0x14cdd9c: ldur            w1, [x3, #0xb]
    // 0x14cdda0: r4 = LoadInt32Instr(r0)
    //     0x14cdda0: sbfx            x4, x0, #1, #0x1f
    //     0x14cdda4: tbz             w0, #0, #0x14cddac
    //     0x14cdda8: ldur            x4, [x0, #7]
    // 0x14cddac: r0 = LoadInt32Instr(r1)
    //     0x14cddac: sbfx            x0, x1, #1, #0x1f
    // 0x14cddb0: mov             x1, x4
    // 0x14cddb4: cmp             x1, x0
    // 0x14cddb8: b.hs            #0x14ceda8
    // 0x14cddbc: LoadField: r0 = r3->field_f
    //     0x14cddbc: ldur            w0, [x3, #0xf]
    // 0x14cddc0: DecompressPointer r0
    //     0x14cddc0: add             x0, x0, HEAP, lsl #32
    // 0x14cddc4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cddc4: add             x16, x0, x4, lsl #2
    //     0x14cddc8: ldur            w1, [x16, #0xf]
    // 0x14cddcc: DecompressPointer r1
    //     0x14cddcc: add             x1, x1, HEAP, lsl #32
    // 0x14cddd0: cmp             w1, NULL
    // 0x14cddd4: b.ne            #0x14cdde0
    // 0x14cddd8: r0 = Null
    //     0x14cddd8: mov             x0, NULL
    // 0x14cdddc: b               #0x14cdde8
    // 0x14cdde0: LoadField: r0 = r1->field_1f
    //     0x14cdde0: ldur            w0, [x1, #0x1f]
    // 0x14cdde4: DecompressPointer r0
    //     0x14cdde4: add             x0, x0, HEAP, lsl #32
    // 0x14cdde8: cmp             w0, NULL
    // 0x14cddec: b.ne            #0x14cddf4
    // 0x14cddf0: r0 = ""
    //     0x14cddf0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cddf4: stur            x0, [fp, #-0x20]
    // 0x14cddf8: LoadField: r1 = r2->field_f
    //     0x14cddf8: ldur            w1, [x2, #0xf]
    // 0x14cddfc: DecompressPointer r1
    //     0x14cddfc: add             x1, x1, HEAP, lsl #32
    // 0x14cde00: r0 = of()
    //     0x14cde00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cde04: LoadField: r1 = r0->field_87
    //     0x14cde04: ldur            w1, [x0, #0x87]
    // 0x14cde08: DecompressPointer r1
    //     0x14cde08: add             x1, x1, HEAP, lsl #32
    // 0x14cde0c: LoadField: r0 = r1->field_33
    //     0x14cde0c: ldur            w0, [x1, #0x33]
    // 0x14cde10: DecompressPointer r0
    //     0x14cde10: add             x0, x0, HEAP, lsl #32
    // 0x14cde14: stur            x0, [fp, #-0x28]
    // 0x14cde18: cmp             w0, NULL
    // 0x14cde1c: b.ne            #0x14cde28
    // 0x14cde20: r5 = Null
    //     0x14cde20: mov             x5, NULL
    // 0x14cde24: b               #0x14cde50
    // 0x14cde28: r1 = Instance_Color
    //     0x14cde28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cde2c: d0 = 0.500000
    //     0x14cde2c: fmov            d0, #0.50000000
    // 0x14cde30: r0 = withOpacity()
    //     0x14cde30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14cde34: r16 = 10.000000
    //     0x14cde34: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x14cde38: stp             x0, x16, [SP]
    // 0x14cde3c: ldur            x1, [fp, #-0x28]
    // 0x14cde40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14cde40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14cde44: ldr             x4, [x4, #0xaa0]
    // 0x14cde48: r0 = copyWith()
    //     0x14cde48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cde4c: mov             x5, x0
    // 0x14cde50: ldur            x3, [fp, #-8]
    // 0x14cde54: ldur            x2, [fp, #-0x10]
    // 0x14cde58: ldur            x4, [fp, #-0x30]
    // 0x14cde5c: ldur            x1, [fp, #-0x38]
    // 0x14cde60: ldur            x0, [fp, #-0x20]
    // 0x14cde64: stur            x5, [fp, #-0x28]
    // 0x14cde68: r0 = Text()
    //     0x14cde68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cde6c: mov             x1, x0
    // 0x14cde70: ldur            x0, [fp, #-0x20]
    // 0x14cde74: stur            x1, [fp, #-0x40]
    // 0x14cde78: StoreField: r1->field_b = r0
    //     0x14cde78: stur            w0, [x1, #0xb]
    // 0x14cde7c: ldur            x0, [fp, #-0x28]
    // 0x14cde80: StoreField: r1->field_13 = r0
    //     0x14cde80: stur            w0, [x1, #0x13]
    // 0x14cde84: r0 = Padding()
    //     0x14cde84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14cde88: mov             x3, x0
    // 0x14cde8c: r0 = Instance_EdgeInsets
    //     0x14cde8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0x14cde90: ldr             x0, [x0, #0x990]
    // 0x14cde94: stur            x3, [fp, #-0x20]
    // 0x14cde98: StoreField: r3->field_f = r0
    //     0x14cde98: stur            w0, [x3, #0xf]
    // 0x14cde9c: ldur            x0, [fp, #-0x40]
    // 0x14cdea0: StoreField: r3->field_b = r0
    //     0x14cdea0: stur            w0, [x3, #0xb]
    // 0x14cdea4: r1 = Null
    //     0x14cdea4: mov             x1, NULL
    // 0x14cdea8: r2 = 4
    //     0x14cdea8: movz            x2, #0x4
    // 0x14cdeac: r0 = AllocateArray()
    //     0x14cdeac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cdeb0: mov             x2, x0
    // 0x14cdeb4: ldur            x0, [fp, #-0x38]
    // 0x14cdeb8: stur            x2, [fp, #-0x28]
    // 0x14cdebc: StoreField: r2->field_f = r0
    //     0x14cdebc: stur            w0, [x2, #0xf]
    // 0x14cdec0: ldur            x0, [fp, #-0x20]
    // 0x14cdec4: StoreField: r2->field_13 = r0
    //     0x14cdec4: stur            w0, [x2, #0x13]
    // 0x14cdec8: r1 = <Widget>
    //     0x14cdec8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cdecc: r0 = AllocateGrowableArray()
    //     0x14cdecc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cded0: mov             x1, x0
    // 0x14cded4: ldur            x0, [fp, #-0x28]
    // 0x14cded8: stur            x1, [fp, #-0x20]
    // 0x14cdedc: StoreField: r1->field_f = r0
    //     0x14cdedc: stur            w0, [x1, #0xf]
    // 0x14cdee0: r2 = 4
    //     0x14cdee0: movz            x2, #0x4
    // 0x14cdee4: StoreField: r1->field_b = r2
    //     0x14cdee4: stur            w2, [x1, #0xb]
    // 0x14cdee8: r0 = Column()
    //     0x14cdee8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14cdeec: mov             x3, x0
    // 0x14cdef0: r0 = Instance_Axis
    //     0x14cdef0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cdef4: stur            x3, [fp, #-0x28]
    // 0x14cdef8: StoreField: r3->field_f = r0
    //     0x14cdef8: stur            w0, [x3, #0xf]
    // 0x14cdefc: r4 = Instance_MainAxisAlignment
    //     0x14cdefc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cdf00: ldr             x4, [x4, #0xa08]
    // 0x14cdf04: StoreField: r3->field_13 = r4
    //     0x14cdf04: stur            w4, [x3, #0x13]
    // 0x14cdf08: r5 = Instance_MainAxisSize
    //     0x14cdf08: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cdf0c: ldr             x5, [x5, #0xa10]
    // 0x14cdf10: ArrayStore: r3[0] = r5  ; List_4
    //     0x14cdf10: stur            w5, [x3, #0x17]
    // 0x14cdf14: r6 = Instance_CrossAxisAlignment
    //     0x14cdf14: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14cdf18: ldr             x6, [x6, #0x890]
    // 0x14cdf1c: StoreField: r3->field_1b = r6
    //     0x14cdf1c: stur            w6, [x3, #0x1b]
    // 0x14cdf20: r7 = Instance_VerticalDirection
    //     0x14cdf20: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cdf24: ldr             x7, [x7, #0xa20]
    // 0x14cdf28: StoreField: r3->field_23 = r7
    //     0x14cdf28: stur            w7, [x3, #0x23]
    // 0x14cdf2c: r8 = Instance_Clip
    //     0x14cdf2c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cdf30: ldr             x8, [x8, #0x38]
    // 0x14cdf34: StoreField: r3->field_2b = r8
    //     0x14cdf34: stur            w8, [x3, #0x2b]
    // 0x14cdf38: StoreField: r3->field_2f = rZR
    //     0x14cdf38: stur            xzr, [x3, #0x2f]
    // 0x14cdf3c: ldur            x1, [fp, #-0x20]
    // 0x14cdf40: StoreField: r3->field_b = r1
    //     0x14cdf40: stur            w1, [x3, #0xb]
    // 0x14cdf44: r1 = Null
    //     0x14cdf44: mov             x1, NULL
    // 0x14cdf48: r2 = 6
    //     0x14cdf48: movz            x2, #0x6
    // 0x14cdf4c: r0 = AllocateArray()
    //     0x14cdf4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cdf50: mov             x2, x0
    // 0x14cdf54: ldur            x0, [fp, #-0x30]
    // 0x14cdf58: stur            x2, [fp, #-0x20]
    // 0x14cdf5c: StoreField: r2->field_f = r0
    //     0x14cdf5c: stur            w0, [x2, #0xf]
    // 0x14cdf60: r16 = Instance_SizedBox
    //     0x14cdf60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x14cdf64: ldr             x16, [x16, #0x998]
    // 0x14cdf68: StoreField: r2->field_13 = r16
    //     0x14cdf68: stur            w16, [x2, #0x13]
    // 0x14cdf6c: ldur            x0, [fp, #-0x28]
    // 0x14cdf70: ArrayStore: r2[0] = r0  ; List_4
    //     0x14cdf70: stur            w0, [x2, #0x17]
    // 0x14cdf74: r1 = <Widget>
    //     0x14cdf74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cdf78: r0 = AllocateGrowableArray()
    //     0x14cdf78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cdf7c: mov             x1, x0
    // 0x14cdf80: ldur            x0, [fp, #-0x20]
    // 0x14cdf84: stur            x1, [fp, #-0x28]
    // 0x14cdf88: StoreField: r1->field_f = r0
    //     0x14cdf88: stur            w0, [x1, #0xf]
    // 0x14cdf8c: r2 = 6
    //     0x14cdf8c: movz            x2, #0x6
    // 0x14cdf90: StoreField: r1->field_b = r2
    //     0x14cdf90: stur            w2, [x1, #0xb]
    // 0x14cdf94: r0 = Row()
    //     0x14cdf94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14cdf98: mov             x2, x0
    // 0x14cdf9c: r0 = Instance_Axis
    //     0x14cdf9c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14cdfa0: stur            x2, [fp, #-0x20]
    // 0x14cdfa4: StoreField: r2->field_f = r0
    //     0x14cdfa4: stur            w0, [x2, #0xf]
    // 0x14cdfa8: r3 = Instance_MainAxisAlignment
    //     0x14cdfa8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cdfac: ldr             x3, [x3, #0xa08]
    // 0x14cdfb0: StoreField: r2->field_13 = r3
    //     0x14cdfb0: stur            w3, [x2, #0x13]
    // 0x14cdfb4: r4 = Instance_MainAxisSize
    //     0x14cdfb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cdfb8: ldr             x4, [x4, #0xa10]
    // 0x14cdfbc: ArrayStore: r2[0] = r4  ; List_4
    //     0x14cdfbc: stur            w4, [x2, #0x17]
    // 0x14cdfc0: r5 = Instance_CrossAxisAlignment
    //     0x14cdfc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cdfc4: ldr             x5, [x5, #0xa18]
    // 0x14cdfc8: StoreField: r2->field_1b = r5
    //     0x14cdfc8: stur            w5, [x2, #0x1b]
    // 0x14cdfcc: r6 = Instance_VerticalDirection
    //     0x14cdfcc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cdfd0: ldr             x6, [x6, #0xa20]
    // 0x14cdfd4: StoreField: r2->field_23 = r6
    //     0x14cdfd4: stur            w6, [x2, #0x23]
    // 0x14cdfd8: r7 = Instance_Clip
    //     0x14cdfd8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cdfdc: ldr             x7, [x7, #0x38]
    // 0x14cdfe0: StoreField: r2->field_2b = r7
    //     0x14cdfe0: stur            w7, [x2, #0x2b]
    // 0x14cdfe4: StoreField: r2->field_2f = rZR
    //     0x14cdfe4: stur            xzr, [x2, #0x2f]
    // 0x14cdfe8: ldur            x1, [fp, #-0x28]
    // 0x14cdfec: StoreField: r2->field_b = r1
    //     0x14cdfec: stur            w1, [x2, #0xb]
    // 0x14cdff0: ldur            x8, [fp, #-8]
    // 0x14cdff4: LoadField: r1 = r8->field_f
    //     0x14cdff4: ldur            w1, [x8, #0xf]
    // 0x14cdff8: DecompressPointer r1
    //     0x14cdff8: add             x1, x1, HEAP, lsl #32
    // 0x14cdffc: r0 = controller()
    //     0x14cdffc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce000: LoadField: r1 = r0->field_77
    //     0x14ce000: ldur            w1, [x0, #0x77]
    // 0x14ce004: DecompressPointer r1
    //     0x14ce004: add             x1, x1, HEAP, lsl #32
    // 0x14ce008: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce008: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce00c: r0 = toList()
    //     0x14ce00c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce010: mov             x3, x0
    // 0x14ce014: ldur            x2, [fp, #-0x10]
    // 0x14ce018: LoadField: r0 = r2->field_13
    //     0x14ce018: ldur            w0, [x2, #0x13]
    // 0x14ce01c: DecompressPointer r0
    //     0x14ce01c: add             x0, x0, HEAP, lsl #32
    // 0x14ce020: LoadField: r1 = r3->field_b
    //     0x14ce020: ldur            w1, [x3, #0xb]
    // 0x14ce024: r4 = LoadInt32Instr(r0)
    //     0x14ce024: sbfx            x4, x0, #1, #0x1f
    //     0x14ce028: tbz             w0, #0, #0x14ce030
    //     0x14ce02c: ldur            x4, [x0, #7]
    // 0x14ce030: r0 = LoadInt32Instr(r1)
    //     0x14ce030: sbfx            x0, x1, #1, #0x1f
    // 0x14ce034: mov             x1, x4
    // 0x14ce038: cmp             x1, x0
    // 0x14ce03c: b.hs            #0x14cedac
    // 0x14ce040: LoadField: r0 = r3->field_f
    //     0x14ce040: ldur            w0, [x3, #0xf]
    // 0x14ce044: DecompressPointer r0
    //     0x14ce044: add             x0, x0, HEAP, lsl #32
    // 0x14ce048: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce048: add             x16, x0, x4, lsl #2
    //     0x14ce04c: ldur            w1, [x16, #0xf]
    // 0x14ce050: DecompressPointer r1
    //     0x14ce050: add             x1, x1, HEAP, lsl #32
    // 0x14ce054: cmp             w1, NULL
    // 0x14ce058: b.eq            #0x14ce06c
    // 0x14ce05c: LoadField: r0 = r1->field_f
    //     0x14ce05c: ldur            w0, [x1, #0xf]
    // 0x14ce060: DecompressPointer r0
    //     0x14ce060: add             x0, x0, HEAP, lsl #32
    // 0x14ce064: cmp             w0, #0xa
    // 0x14ce068: b.eq            #0x14ce0e8
    // 0x14ce06c: ldur            x0, [fp, #-8]
    // 0x14ce070: LoadField: r1 = r0->field_f
    //     0x14ce070: ldur            w1, [x0, #0xf]
    // 0x14ce074: DecompressPointer r1
    //     0x14ce074: add             x1, x1, HEAP, lsl #32
    // 0x14ce078: r0 = controller()
    //     0x14ce078: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce07c: LoadField: r1 = r0->field_77
    //     0x14ce07c: ldur            w1, [x0, #0x77]
    // 0x14ce080: DecompressPointer r1
    //     0x14ce080: add             x1, x1, HEAP, lsl #32
    // 0x14ce084: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce084: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce088: r0 = toList()
    //     0x14ce088: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce08c: mov             x3, x0
    // 0x14ce090: ldur            x2, [fp, #-0x10]
    // 0x14ce094: LoadField: r0 = r2->field_13
    //     0x14ce094: ldur            w0, [x2, #0x13]
    // 0x14ce098: DecompressPointer r0
    //     0x14ce098: add             x0, x0, HEAP, lsl #32
    // 0x14ce09c: LoadField: r1 = r3->field_b
    //     0x14ce09c: ldur            w1, [x3, #0xb]
    // 0x14ce0a0: r4 = LoadInt32Instr(r0)
    //     0x14ce0a0: sbfx            x4, x0, #1, #0x1f
    //     0x14ce0a4: tbz             w0, #0, #0x14ce0ac
    //     0x14ce0a8: ldur            x4, [x0, #7]
    // 0x14ce0ac: r0 = LoadInt32Instr(r1)
    //     0x14ce0ac: sbfx            x0, x1, #1, #0x1f
    // 0x14ce0b0: mov             x1, x4
    // 0x14ce0b4: cmp             x1, x0
    // 0x14ce0b8: b.hs            #0x14cedb0
    // 0x14ce0bc: LoadField: r0 = r3->field_f
    //     0x14ce0bc: ldur            w0, [x3, #0xf]
    // 0x14ce0c0: DecompressPointer r0
    //     0x14ce0c0: add             x0, x0, HEAP, lsl #32
    // 0x14ce0c4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce0c4: add             x16, x0, x4, lsl #2
    //     0x14ce0c8: ldur            w1, [x16, #0xf]
    // 0x14ce0cc: DecompressPointer r1
    //     0x14ce0cc: add             x1, x1, HEAP, lsl #32
    // 0x14ce0d0: cmp             w1, NULL
    // 0x14ce0d4: b.eq            #0x14ce0f4
    // 0x14ce0d8: LoadField: r0 = r1->field_f
    //     0x14ce0d8: ldur            w0, [x1, #0xf]
    // 0x14ce0dc: DecompressPointer r0
    //     0x14ce0dc: add             x0, x0, HEAP, lsl #32
    // 0x14ce0e0: cmp             w0, #8
    // 0x14ce0e4: b.ne            #0x14ce0f4
    // 0x14ce0e8: r1 = Instance_Color
    //     0x14ce0e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14ce0ec: ldr             x1, [x1, #0x858]
    // 0x14ce0f0: b               #0x14ce224
    // 0x14ce0f4: ldur            x0, [fp, #-8]
    // 0x14ce0f8: LoadField: r1 = r0->field_f
    //     0x14ce0f8: ldur            w1, [x0, #0xf]
    // 0x14ce0fc: DecompressPointer r1
    //     0x14ce0fc: add             x1, x1, HEAP, lsl #32
    // 0x14ce100: r0 = controller()
    //     0x14ce100: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce104: LoadField: r1 = r0->field_77
    //     0x14ce104: ldur            w1, [x0, #0x77]
    // 0x14ce108: DecompressPointer r1
    //     0x14ce108: add             x1, x1, HEAP, lsl #32
    // 0x14ce10c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce10c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce110: r0 = toList()
    //     0x14ce110: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce114: mov             x3, x0
    // 0x14ce118: ldur            x2, [fp, #-0x10]
    // 0x14ce11c: LoadField: r0 = r2->field_13
    //     0x14ce11c: ldur            w0, [x2, #0x13]
    // 0x14ce120: DecompressPointer r0
    //     0x14ce120: add             x0, x0, HEAP, lsl #32
    // 0x14ce124: LoadField: r1 = r3->field_b
    //     0x14ce124: ldur            w1, [x3, #0xb]
    // 0x14ce128: r4 = LoadInt32Instr(r0)
    //     0x14ce128: sbfx            x4, x0, #1, #0x1f
    //     0x14ce12c: tbz             w0, #0, #0x14ce134
    //     0x14ce130: ldur            x4, [x0, #7]
    // 0x14ce134: r0 = LoadInt32Instr(r1)
    //     0x14ce134: sbfx            x0, x1, #1, #0x1f
    // 0x14ce138: mov             x1, x4
    // 0x14ce13c: cmp             x1, x0
    // 0x14ce140: b.hs            #0x14cedb4
    // 0x14ce144: LoadField: r0 = r3->field_f
    //     0x14ce144: ldur            w0, [x3, #0xf]
    // 0x14ce148: DecompressPointer r0
    //     0x14ce148: add             x0, x0, HEAP, lsl #32
    // 0x14ce14c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce14c: add             x16, x0, x4, lsl #2
    //     0x14ce150: ldur            w1, [x16, #0xf]
    // 0x14ce154: DecompressPointer r1
    //     0x14ce154: add             x1, x1, HEAP, lsl #32
    // 0x14ce158: cmp             w1, NULL
    // 0x14ce15c: b.eq            #0x14ce190
    // 0x14ce160: LoadField: r0 = r1->field_f
    //     0x14ce160: ldur            w0, [x1, #0xf]
    // 0x14ce164: DecompressPointer r0
    //     0x14ce164: add             x0, x0, HEAP, lsl #32
    // 0x14ce168: cmp             w0, #6
    // 0x14ce16c: b.ne            #0x14ce18c
    // 0x14ce170: r1 = Instance_Color
    //     0x14ce170: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14ce174: ldr             x1, [x1, #0x858]
    // 0x14ce178: d0 = 0.700000
    //     0x14ce178: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14ce17c: ldr             d0, [x17, #0xf48]
    // 0x14ce180: r0 = withOpacity()
    //     0x14ce180: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14ce184: ldur            x2, [fp, #-0x10]
    // 0x14ce188: b               #0x14ce220
    // 0x14ce18c: ldur            x2, [fp, #-0x10]
    // 0x14ce190: ldur            x0, [fp, #-8]
    // 0x14ce194: LoadField: r1 = r0->field_f
    //     0x14ce194: ldur            w1, [x0, #0xf]
    // 0x14ce198: DecompressPointer r1
    //     0x14ce198: add             x1, x1, HEAP, lsl #32
    // 0x14ce19c: r0 = controller()
    //     0x14ce19c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce1a0: LoadField: r1 = r0->field_77
    //     0x14ce1a0: ldur            w1, [x0, #0x77]
    // 0x14ce1a4: DecompressPointer r1
    //     0x14ce1a4: add             x1, x1, HEAP, lsl #32
    // 0x14ce1a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce1a8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce1ac: r0 = toList()
    //     0x14ce1ac: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce1b0: mov             x3, x0
    // 0x14ce1b4: ldur            x2, [fp, #-0x10]
    // 0x14ce1b8: LoadField: r0 = r2->field_13
    //     0x14ce1b8: ldur            w0, [x2, #0x13]
    // 0x14ce1bc: DecompressPointer r0
    //     0x14ce1bc: add             x0, x0, HEAP, lsl #32
    // 0x14ce1c0: LoadField: r1 = r3->field_b
    //     0x14ce1c0: ldur            w1, [x3, #0xb]
    // 0x14ce1c4: r4 = LoadInt32Instr(r0)
    //     0x14ce1c4: sbfx            x4, x0, #1, #0x1f
    //     0x14ce1c8: tbz             w0, #0, #0x14ce1d0
    //     0x14ce1cc: ldur            x4, [x0, #7]
    // 0x14ce1d0: r0 = LoadInt32Instr(r1)
    //     0x14ce1d0: sbfx            x0, x1, #1, #0x1f
    // 0x14ce1d4: mov             x1, x4
    // 0x14ce1d8: cmp             x1, x0
    // 0x14ce1dc: b.hs            #0x14cedb8
    // 0x14ce1e0: LoadField: r0 = r3->field_f
    //     0x14ce1e0: ldur            w0, [x3, #0xf]
    // 0x14ce1e4: DecompressPointer r0
    //     0x14ce1e4: add             x0, x0, HEAP, lsl #32
    // 0x14ce1e8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce1e8: add             x16, x0, x4, lsl #2
    //     0x14ce1ec: ldur            w1, [x16, #0xf]
    // 0x14ce1f0: DecompressPointer r1
    //     0x14ce1f0: add             x1, x1, HEAP, lsl #32
    // 0x14ce1f4: cmp             w1, NULL
    // 0x14ce1f8: b.eq            #0x14ce218
    // 0x14ce1fc: LoadField: r0 = r1->field_f
    //     0x14ce1fc: ldur            w0, [x1, #0xf]
    // 0x14ce200: DecompressPointer r0
    //     0x14ce200: add             x0, x0, HEAP, lsl #32
    // 0x14ce204: cmp             w0, #4
    // 0x14ce208: b.ne            #0x14ce218
    // 0x14ce20c: r0 = Instance_Color
    //     0x14ce20c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0x14ce210: ldr             x0, [x0, #0x860]
    // 0x14ce214: b               #0x14ce220
    // 0x14ce218: r0 = Instance_Color
    //     0x14ce218: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14ce21c: ldr             x0, [x0, #0x50]
    // 0x14ce220: mov             x1, x0
    // 0x14ce224: ldur            x0, [fp, #-8]
    // 0x14ce228: stur            x1, [fp, #-0x28]
    // 0x14ce22c: r0 = ColorFilter()
    //     0x14ce22c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14ce230: mov             x1, x0
    // 0x14ce234: ldur            x0, [fp, #-0x28]
    // 0x14ce238: stur            x1, [fp, #-0x30]
    // 0x14ce23c: StoreField: r1->field_7 = r0
    //     0x14ce23c: stur            w0, [x1, #7]
    // 0x14ce240: r0 = Instance_BlendMode
    //     0x14ce240: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14ce244: ldr             x0, [x0, #0xb30]
    // 0x14ce248: StoreField: r1->field_b = r0
    //     0x14ce248: stur            w0, [x1, #0xb]
    // 0x14ce24c: r0 = 1
    //     0x14ce24c: movz            x0, #0x1
    // 0x14ce250: StoreField: r1->field_13 = r0
    //     0x14ce250: stur            x0, [x1, #0x13]
    // 0x14ce254: r0 = SvgPicture()
    //     0x14ce254: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14ce258: stur            x0, [fp, #-0x28]
    // 0x14ce25c: ldur            x16, [fp, #-0x30]
    // 0x14ce260: str             x16, [SP]
    // 0x14ce264: mov             x1, x0
    // 0x14ce268: r2 = "assets/images/green_star.svg"
    //     0x14ce268: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0x14ce26c: ldr             x2, [x2, #0x9a0]
    // 0x14ce270: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x14ce270: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x14ce274: ldr             x4, [x4, #0xa38]
    // 0x14ce278: r0 = SvgPicture.asset()
    //     0x14ce278: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14ce27c: ldur            x0, [fp, #-8]
    // 0x14ce280: LoadField: r1 = r0->field_f
    //     0x14ce280: ldur            w1, [x0, #0xf]
    // 0x14ce284: DecompressPointer r1
    //     0x14ce284: add             x1, x1, HEAP, lsl #32
    // 0x14ce288: r0 = controller()
    //     0x14ce288: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce28c: LoadField: r1 = r0->field_77
    //     0x14ce28c: ldur            w1, [x0, #0x77]
    // 0x14ce290: DecompressPointer r1
    //     0x14ce290: add             x1, x1, HEAP, lsl #32
    // 0x14ce294: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce294: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce298: r0 = toList()
    //     0x14ce298: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce29c: mov             x3, x0
    // 0x14ce2a0: ldur            x2, [fp, #-0x10]
    // 0x14ce2a4: LoadField: r0 = r2->field_13
    //     0x14ce2a4: ldur            w0, [x2, #0x13]
    // 0x14ce2a8: DecompressPointer r0
    //     0x14ce2a8: add             x0, x0, HEAP, lsl #32
    // 0x14ce2ac: LoadField: r1 = r3->field_b
    //     0x14ce2ac: ldur            w1, [x3, #0xb]
    // 0x14ce2b0: r4 = LoadInt32Instr(r0)
    //     0x14ce2b0: sbfx            x4, x0, #1, #0x1f
    //     0x14ce2b4: tbz             w0, #0, #0x14ce2bc
    //     0x14ce2b8: ldur            x4, [x0, #7]
    // 0x14ce2bc: r0 = LoadInt32Instr(r1)
    //     0x14ce2bc: sbfx            x0, x1, #1, #0x1f
    // 0x14ce2c0: mov             x1, x4
    // 0x14ce2c4: cmp             x1, x0
    // 0x14ce2c8: b.hs            #0x14cedbc
    // 0x14ce2cc: LoadField: r0 = r3->field_f
    //     0x14ce2cc: ldur            w0, [x3, #0xf]
    // 0x14ce2d0: DecompressPointer r0
    //     0x14ce2d0: add             x0, x0, HEAP, lsl #32
    // 0x14ce2d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce2d4: add             x16, x0, x4, lsl #2
    //     0x14ce2d8: ldur            w1, [x16, #0xf]
    // 0x14ce2dc: DecompressPointer r1
    //     0x14ce2dc: add             x1, x1, HEAP, lsl #32
    // 0x14ce2e0: cmp             w1, NULL
    // 0x14ce2e4: b.ne            #0x14ce2f0
    // 0x14ce2e8: r0 = Null
    //     0x14ce2e8: mov             x0, NULL
    // 0x14ce2ec: b               #0x14ce324
    // 0x14ce2f0: LoadField: r0 = r1->field_f
    //     0x14ce2f0: ldur            w0, [x1, #0xf]
    // 0x14ce2f4: DecompressPointer r0
    //     0x14ce2f4: add             x0, x0, HEAP, lsl #32
    // 0x14ce2f8: r1 = 60
    //     0x14ce2f8: movz            x1, #0x3c
    // 0x14ce2fc: branchIfSmi(r0, 0x14ce308)
    //     0x14ce2fc: tbz             w0, #0, #0x14ce308
    // 0x14ce300: r1 = LoadClassIdInstr(r0)
    //     0x14ce300: ldur            x1, [x0, #-1]
    //     0x14ce304: ubfx            x1, x1, #0xc, #0x14
    // 0x14ce308: str             x0, [SP]
    // 0x14ce30c: mov             x0, x1
    // 0x14ce310: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x14ce310: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x14ce314: r0 = GDT[cid_x0 + 0x2700]()
    //     0x14ce314: movz            x17, #0x2700
    //     0x14ce318: add             lr, x0, x17
    //     0x14ce31c: ldr             lr, [x21, lr, lsl #3]
    //     0x14ce320: blr             lr
    // 0x14ce324: cmp             w0, NULL
    // 0x14ce328: b.ne            #0x14ce334
    // 0x14ce32c: r5 = ""
    //     0x14ce32c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14ce330: b               #0x14ce338
    // 0x14ce334: mov             x5, x0
    // 0x14ce338: ldur            x0, [fp, #-8]
    // 0x14ce33c: ldur            x2, [fp, #-0x10]
    // 0x14ce340: ldur            x4, [fp, #-0x20]
    // 0x14ce344: ldur            x3, [fp, #-0x28]
    // 0x14ce348: stur            x5, [fp, #-0x30]
    // 0x14ce34c: LoadField: r1 = r2->field_f
    //     0x14ce34c: ldur            w1, [x2, #0xf]
    // 0x14ce350: DecompressPointer r1
    //     0x14ce350: add             x1, x1, HEAP, lsl #32
    // 0x14ce354: r0 = of()
    //     0x14ce354: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ce358: LoadField: r1 = r0->field_87
    //     0x14ce358: ldur            w1, [x0, #0x87]
    // 0x14ce35c: DecompressPointer r1
    //     0x14ce35c: add             x1, x1, HEAP, lsl #32
    // 0x14ce360: LoadField: r0 = r1->field_7
    //     0x14ce360: ldur            w0, [x1, #7]
    // 0x14ce364: DecompressPointer r0
    //     0x14ce364: add             x0, x0, HEAP, lsl #32
    // 0x14ce368: r16 = 12.000000
    //     0x14ce368: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14ce36c: ldr             x16, [x16, #0x9e8]
    // 0x14ce370: r30 = Instance_Color
    //     0x14ce370: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ce374: stp             lr, x16, [SP]
    // 0x14ce378: mov             x1, x0
    // 0x14ce37c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14ce37c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14ce380: ldr             x4, [x4, #0xaa0]
    // 0x14ce384: r0 = copyWith()
    //     0x14ce384: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ce388: stur            x0, [fp, #-0x38]
    // 0x14ce38c: r0 = Text()
    //     0x14ce38c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ce390: mov             x3, x0
    // 0x14ce394: ldur            x0, [fp, #-0x30]
    // 0x14ce398: stur            x3, [fp, #-0x40]
    // 0x14ce39c: StoreField: r3->field_b = r0
    //     0x14ce39c: stur            w0, [x3, #0xb]
    // 0x14ce3a0: ldur            x0, [fp, #-0x38]
    // 0x14ce3a4: StoreField: r3->field_13 = r0
    //     0x14ce3a4: stur            w0, [x3, #0x13]
    // 0x14ce3a8: r1 = Null
    //     0x14ce3a8: mov             x1, NULL
    // 0x14ce3ac: r2 = 6
    //     0x14ce3ac: movz            x2, #0x6
    // 0x14ce3b0: r0 = AllocateArray()
    //     0x14ce3b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ce3b4: mov             x2, x0
    // 0x14ce3b8: ldur            x0, [fp, #-0x28]
    // 0x14ce3bc: stur            x2, [fp, #-0x30]
    // 0x14ce3c0: StoreField: r2->field_f = r0
    //     0x14ce3c0: stur            w0, [x2, #0xf]
    // 0x14ce3c4: r16 = Instance_SizedBox
    //     0x14ce3c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] Obj!SizedBox@d67ea1
    //     0x14ce3c8: ldr             x16, [x16, #0x9a8]
    // 0x14ce3cc: StoreField: r2->field_13 = r16
    //     0x14ce3cc: stur            w16, [x2, #0x13]
    // 0x14ce3d0: ldur            x0, [fp, #-0x40]
    // 0x14ce3d4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ce3d4: stur            w0, [x2, #0x17]
    // 0x14ce3d8: r1 = <Widget>
    //     0x14ce3d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ce3dc: r0 = AllocateGrowableArray()
    //     0x14ce3dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ce3e0: mov             x1, x0
    // 0x14ce3e4: ldur            x0, [fp, #-0x30]
    // 0x14ce3e8: stur            x1, [fp, #-0x28]
    // 0x14ce3ec: StoreField: r1->field_f = r0
    //     0x14ce3ec: stur            w0, [x1, #0xf]
    // 0x14ce3f0: r0 = 6
    //     0x14ce3f0: movz            x0, #0x6
    // 0x14ce3f4: StoreField: r1->field_b = r0
    //     0x14ce3f4: stur            w0, [x1, #0xb]
    // 0x14ce3f8: r0 = Row()
    //     0x14ce3f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14ce3fc: mov             x3, x0
    // 0x14ce400: r0 = Instance_Axis
    //     0x14ce400: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14ce404: stur            x3, [fp, #-0x30]
    // 0x14ce408: StoreField: r3->field_f = r0
    //     0x14ce408: stur            w0, [x3, #0xf]
    // 0x14ce40c: r4 = Instance_MainAxisAlignment
    //     0x14ce40c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ce410: ldr             x4, [x4, #0xa08]
    // 0x14ce414: StoreField: r3->field_13 = r4
    //     0x14ce414: stur            w4, [x3, #0x13]
    // 0x14ce418: r5 = Instance_MainAxisSize
    //     0x14ce418: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ce41c: ldr             x5, [x5, #0xa10]
    // 0x14ce420: ArrayStore: r3[0] = r5  ; List_4
    //     0x14ce420: stur            w5, [x3, #0x17]
    // 0x14ce424: r6 = Instance_CrossAxisAlignment
    //     0x14ce424: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14ce428: ldr             x6, [x6, #0xa18]
    // 0x14ce42c: StoreField: r3->field_1b = r6
    //     0x14ce42c: stur            w6, [x3, #0x1b]
    // 0x14ce430: r7 = Instance_VerticalDirection
    //     0x14ce430: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ce434: ldr             x7, [x7, #0xa20]
    // 0x14ce438: StoreField: r3->field_23 = r7
    //     0x14ce438: stur            w7, [x3, #0x23]
    // 0x14ce43c: r8 = Instance_Clip
    //     0x14ce43c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ce440: ldr             x8, [x8, #0x38]
    // 0x14ce444: StoreField: r3->field_2b = r8
    //     0x14ce444: stur            w8, [x3, #0x2b]
    // 0x14ce448: StoreField: r3->field_2f = rZR
    //     0x14ce448: stur            xzr, [x3, #0x2f]
    // 0x14ce44c: ldur            x1, [fp, #-0x28]
    // 0x14ce450: StoreField: r3->field_b = r1
    //     0x14ce450: stur            w1, [x3, #0xb]
    // 0x14ce454: r1 = Null
    //     0x14ce454: mov             x1, NULL
    // 0x14ce458: r2 = 4
    //     0x14ce458: movz            x2, #0x4
    // 0x14ce45c: r0 = AllocateArray()
    //     0x14ce45c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ce460: mov             x2, x0
    // 0x14ce464: ldur            x0, [fp, #-0x20]
    // 0x14ce468: stur            x2, [fp, #-0x28]
    // 0x14ce46c: StoreField: r2->field_f = r0
    //     0x14ce46c: stur            w0, [x2, #0xf]
    // 0x14ce470: ldur            x0, [fp, #-0x30]
    // 0x14ce474: StoreField: r2->field_13 = r0
    //     0x14ce474: stur            w0, [x2, #0x13]
    // 0x14ce478: r1 = <Widget>
    //     0x14ce478: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ce47c: r0 = AllocateGrowableArray()
    //     0x14ce47c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ce480: mov             x1, x0
    // 0x14ce484: ldur            x0, [fp, #-0x28]
    // 0x14ce488: stur            x1, [fp, #-0x20]
    // 0x14ce48c: StoreField: r1->field_f = r0
    //     0x14ce48c: stur            w0, [x1, #0xf]
    // 0x14ce490: r2 = 4
    //     0x14ce490: movz            x2, #0x4
    // 0x14ce494: StoreField: r1->field_b = r2
    //     0x14ce494: stur            w2, [x1, #0xb]
    // 0x14ce498: r0 = Row()
    //     0x14ce498: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14ce49c: mov             x3, x0
    // 0x14ce4a0: r0 = Instance_Axis
    //     0x14ce4a0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14ce4a4: stur            x3, [fp, #-0x28]
    // 0x14ce4a8: StoreField: r3->field_f = r0
    //     0x14ce4a8: stur            w0, [x3, #0xf]
    // 0x14ce4ac: r0 = Instance_MainAxisAlignment
    //     0x14ce4ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14ce4b0: ldr             x0, [x0, #0xa8]
    // 0x14ce4b4: StoreField: r3->field_13 = r0
    //     0x14ce4b4: stur            w0, [x3, #0x13]
    // 0x14ce4b8: r0 = Instance_MainAxisSize
    //     0x14ce4b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ce4bc: ldr             x0, [x0, #0xa10]
    // 0x14ce4c0: ArrayStore: r3[0] = r0  ; List_4
    //     0x14ce4c0: stur            w0, [x3, #0x17]
    // 0x14ce4c4: r1 = Instance_CrossAxisAlignment
    //     0x14ce4c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14ce4c8: ldr             x1, [x1, #0xa18]
    // 0x14ce4cc: StoreField: r3->field_1b = r1
    //     0x14ce4cc: stur            w1, [x3, #0x1b]
    // 0x14ce4d0: r4 = Instance_VerticalDirection
    //     0x14ce4d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ce4d4: ldr             x4, [x4, #0xa20]
    // 0x14ce4d8: StoreField: r3->field_23 = r4
    //     0x14ce4d8: stur            w4, [x3, #0x23]
    // 0x14ce4dc: r5 = Instance_Clip
    //     0x14ce4dc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ce4e0: ldr             x5, [x5, #0x38]
    // 0x14ce4e4: StoreField: r3->field_2b = r5
    //     0x14ce4e4: stur            w5, [x3, #0x2b]
    // 0x14ce4e8: StoreField: r3->field_2f = rZR
    //     0x14ce4e8: stur            xzr, [x3, #0x2f]
    // 0x14ce4ec: ldur            x1, [fp, #-0x20]
    // 0x14ce4f0: StoreField: r3->field_b = r1
    //     0x14ce4f0: stur            w1, [x3, #0xb]
    // 0x14ce4f4: r1 = Null
    //     0x14ce4f4: mov             x1, NULL
    // 0x14ce4f8: r2 = 2
    //     0x14ce4f8: movz            x2, #0x2
    // 0x14ce4fc: r0 = AllocateArray()
    //     0x14ce4fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ce500: mov             x2, x0
    // 0x14ce504: ldur            x0, [fp, #-0x28]
    // 0x14ce508: stur            x2, [fp, #-0x20]
    // 0x14ce50c: StoreField: r2->field_f = r0
    //     0x14ce50c: stur            w0, [x2, #0xf]
    // 0x14ce510: r1 = <Widget>
    //     0x14ce510: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ce514: r0 = AllocateGrowableArray()
    //     0x14ce514: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ce518: mov             x2, x0
    // 0x14ce51c: ldur            x0, [fp, #-0x20]
    // 0x14ce520: stur            x2, [fp, #-0x28]
    // 0x14ce524: StoreField: r2->field_f = r0
    //     0x14ce524: stur            w0, [x2, #0xf]
    // 0x14ce528: r0 = 2
    //     0x14ce528: movz            x0, #0x2
    // 0x14ce52c: StoreField: r2->field_b = r0
    //     0x14ce52c: stur            w0, [x2, #0xb]
    // 0x14ce530: ldur            x0, [fp, #-8]
    // 0x14ce534: LoadField: r1 = r0->field_f
    //     0x14ce534: ldur            w1, [x0, #0xf]
    // 0x14ce538: DecompressPointer r1
    //     0x14ce538: add             x1, x1, HEAP, lsl #32
    // 0x14ce53c: r0 = controller()
    //     0x14ce53c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce540: LoadField: r1 = r0->field_77
    //     0x14ce540: ldur            w1, [x0, #0x77]
    // 0x14ce544: DecompressPointer r1
    //     0x14ce544: add             x1, x1, HEAP, lsl #32
    // 0x14ce548: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce548: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce54c: r0 = toList()
    //     0x14ce54c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce550: mov             x3, x0
    // 0x14ce554: ldur            x2, [fp, #-0x10]
    // 0x14ce558: LoadField: r0 = r2->field_13
    //     0x14ce558: ldur            w0, [x2, #0x13]
    // 0x14ce55c: DecompressPointer r0
    //     0x14ce55c: add             x0, x0, HEAP, lsl #32
    // 0x14ce560: LoadField: r1 = r3->field_b
    //     0x14ce560: ldur            w1, [x3, #0xb]
    // 0x14ce564: r4 = LoadInt32Instr(r0)
    //     0x14ce564: sbfx            x4, x0, #1, #0x1f
    //     0x14ce568: tbz             w0, #0, #0x14ce570
    //     0x14ce56c: ldur            x4, [x0, #7]
    // 0x14ce570: r0 = LoadInt32Instr(r1)
    //     0x14ce570: sbfx            x0, x1, #1, #0x1f
    // 0x14ce574: mov             x1, x4
    // 0x14ce578: cmp             x1, x0
    // 0x14ce57c: b.hs            #0x14cedc0
    // 0x14ce580: LoadField: r0 = r3->field_f
    //     0x14ce580: ldur            w0, [x3, #0xf]
    // 0x14ce584: DecompressPointer r0
    //     0x14ce584: add             x0, x0, HEAP, lsl #32
    // 0x14ce588: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce588: add             x16, x0, x4, lsl #2
    //     0x14ce58c: ldur            w1, [x16, #0xf]
    // 0x14ce590: DecompressPointer r1
    //     0x14ce590: add             x1, x1, HEAP, lsl #32
    // 0x14ce594: cmp             w1, NULL
    // 0x14ce598: b.ne            #0x14ce5a4
    // 0x14ce59c: r0 = Null
    //     0x14ce59c: mov             x0, NULL
    // 0x14ce5a0: b               #0x14ce5d0
    // 0x14ce5a4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14ce5a4: ldur            w0, [x1, #0x17]
    // 0x14ce5a8: DecompressPointer r0
    //     0x14ce5a8: add             x0, x0, HEAP, lsl #32
    // 0x14ce5ac: cmp             w0, NULL
    // 0x14ce5b0: b.ne            #0x14ce5bc
    // 0x14ce5b4: r0 = Null
    //     0x14ce5b4: mov             x0, NULL
    // 0x14ce5b8: b               #0x14ce5d0
    // 0x14ce5bc: LoadField: r1 = r0->field_7
    //     0x14ce5bc: ldur            w1, [x0, #7]
    // 0x14ce5c0: cbnz            w1, #0x14ce5cc
    // 0x14ce5c4: r0 = false
    //     0x14ce5c4: add             x0, NULL, #0x30  ; false
    // 0x14ce5c8: b               #0x14ce5d0
    // 0x14ce5cc: r0 = true
    //     0x14ce5cc: add             x0, NULL, #0x20  ; true
    // 0x14ce5d0: cmp             w0, NULL
    // 0x14ce5d4: b.ne            #0x14ce5e0
    // 0x14ce5d8: ldur            x2, [fp, #-0x28]
    // 0x14ce5dc: b               #0x14ce880
    // 0x14ce5e0: tbnz            w0, #4, #0x14ce87c
    // 0x14ce5e4: ldur            x0, [fp, #-8]
    // 0x14ce5e8: LoadField: r1 = r0->field_f
    //     0x14ce5e8: ldur            w1, [x0, #0xf]
    // 0x14ce5ec: DecompressPointer r1
    //     0x14ce5ec: add             x1, x1, HEAP, lsl #32
    // 0x14ce5f0: r0 = controller()
    //     0x14ce5f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce5f4: LoadField: r1 = r0->field_77
    //     0x14ce5f4: ldur            w1, [x0, #0x77]
    // 0x14ce5f8: DecompressPointer r1
    //     0x14ce5f8: add             x1, x1, HEAP, lsl #32
    // 0x14ce5fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce5fc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce600: r0 = toList()
    //     0x14ce600: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce604: mov             x3, x0
    // 0x14ce608: ldur            x2, [fp, #-0x10]
    // 0x14ce60c: LoadField: r0 = r2->field_13
    //     0x14ce60c: ldur            w0, [x2, #0x13]
    // 0x14ce610: DecompressPointer r0
    //     0x14ce610: add             x0, x0, HEAP, lsl #32
    // 0x14ce614: LoadField: r1 = r3->field_b
    //     0x14ce614: ldur            w1, [x3, #0xb]
    // 0x14ce618: r4 = LoadInt32Instr(r0)
    //     0x14ce618: sbfx            x4, x0, #1, #0x1f
    //     0x14ce61c: tbz             w0, #0, #0x14ce624
    //     0x14ce620: ldur            x4, [x0, #7]
    // 0x14ce624: r0 = LoadInt32Instr(r1)
    //     0x14ce624: sbfx            x0, x1, #1, #0x1f
    // 0x14ce628: mov             x1, x4
    // 0x14ce62c: cmp             x1, x0
    // 0x14ce630: b.hs            #0x14cedc4
    // 0x14ce634: LoadField: r0 = r3->field_f
    //     0x14ce634: ldur            w0, [x3, #0xf]
    // 0x14ce638: DecompressPointer r0
    //     0x14ce638: add             x0, x0, HEAP, lsl #32
    // 0x14ce63c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce63c: add             x16, x0, x4, lsl #2
    //     0x14ce640: ldur            w1, [x16, #0xf]
    // 0x14ce644: DecompressPointer r1
    //     0x14ce644: add             x1, x1, HEAP, lsl #32
    // 0x14ce648: cmp             w1, NULL
    // 0x14ce64c: b.ne            #0x14ce658
    // 0x14ce650: r0 = Null
    //     0x14ce650: mov             x0, NULL
    // 0x14ce654: b               #0x14ce678
    // 0x14ce658: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14ce658: ldur            w0, [x1, #0x17]
    // 0x14ce65c: DecompressPointer r0
    //     0x14ce65c: add             x0, x0, HEAP, lsl #32
    // 0x14ce660: cmp             w0, NULL
    // 0x14ce664: b.ne            #0x14ce670
    // 0x14ce668: r0 = Null
    //     0x14ce668: mov             x0, NULL
    // 0x14ce66c: b               #0x14ce678
    // 0x14ce670: mov             x1, x0
    // 0x14ce674: r0 = trim()
    //     0x14ce674: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x14ce678: cmp             w0, NULL
    // 0x14ce67c: b.ne            #0x14ce688
    // 0x14ce680: r3 = ""
    //     0x14ce680: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14ce684: b               #0x14ce68c
    // 0x14ce688: mov             x3, x0
    // 0x14ce68c: ldur            x2, [fp, #-0x10]
    // 0x14ce690: ldur            x0, [fp, #-0x28]
    // 0x14ce694: stur            x3, [fp, #-0x20]
    // 0x14ce698: LoadField: r1 = r2->field_f
    //     0x14ce698: ldur            w1, [x2, #0xf]
    // 0x14ce69c: DecompressPointer r1
    //     0x14ce69c: add             x1, x1, HEAP, lsl #32
    // 0x14ce6a0: r0 = of()
    //     0x14ce6a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ce6a4: LoadField: r1 = r0->field_87
    //     0x14ce6a4: ldur            w1, [x0, #0x87]
    // 0x14ce6a8: DecompressPointer r1
    //     0x14ce6a8: add             x1, x1, HEAP, lsl #32
    // 0x14ce6ac: LoadField: r0 = r1->field_2b
    //     0x14ce6ac: ldur            w0, [x1, #0x2b]
    // 0x14ce6b0: DecompressPointer r0
    //     0x14ce6b0: add             x0, x0, HEAP, lsl #32
    // 0x14ce6b4: LoadField: r1 = r0->field_13
    //     0x14ce6b4: ldur            w1, [x0, #0x13]
    // 0x14ce6b8: DecompressPointer r1
    //     0x14ce6b8: add             x1, x1, HEAP, lsl #32
    // 0x14ce6bc: r16 = Instance_Color
    //     0x14ce6bc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ce6c0: stp             x16, x1, [SP]
    // 0x14ce6c4: r1 = Instance_TextStyle
    //     0x14ce6c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0x14ce6c8: ldr             x1, [x1, #0x9b0]
    // 0x14ce6cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0x14ce6cc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0x14ce6d0: ldr             x4, [x4, #0x9b8]
    // 0x14ce6d4: r0 = copyWith()
    //     0x14ce6d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ce6d8: ldur            x2, [fp, #-0x10]
    // 0x14ce6dc: stur            x0, [fp, #-0x30]
    // 0x14ce6e0: LoadField: r1 = r2->field_f
    //     0x14ce6e0: ldur            w1, [x2, #0xf]
    // 0x14ce6e4: DecompressPointer r1
    //     0x14ce6e4: add             x1, x1, HEAP, lsl #32
    // 0x14ce6e8: r0 = of()
    //     0x14ce6e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ce6ec: LoadField: r1 = r0->field_87
    //     0x14ce6ec: ldur            w1, [x0, #0x87]
    // 0x14ce6f0: DecompressPointer r1
    //     0x14ce6f0: add             x1, x1, HEAP, lsl #32
    // 0x14ce6f4: LoadField: r0 = r1->field_7
    //     0x14ce6f4: ldur            w0, [x1, #7]
    // 0x14ce6f8: DecompressPointer r0
    //     0x14ce6f8: add             x0, x0, HEAP, lsl #32
    // 0x14ce6fc: r16 = Instance_Color
    //     0x14ce6fc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ce700: r30 = 12.000000
    //     0x14ce700: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14ce704: ldr             lr, [lr, #0x9e8]
    // 0x14ce708: stp             lr, x16, [SP]
    // 0x14ce70c: mov             x1, x0
    // 0x14ce710: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14ce710: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14ce714: ldr             x4, [x4, #0x9b8]
    // 0x14ce718: r0 = copyWith()
    //     0x14ce718: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ce71c: ldur            x2, [fp, #-0x10]
    // 0x14ce720: stur            x0, [fp, #-0x38]
    // 0x14ce724: LoadField: r1 = r2->field_f
    //     0x14ce724: ldur            w1, [x2, #0xf]
    // 0x14ce728: DecompressPointer r1
    //     0x14ce728: add             x1, x1, HEAP, lsl #32
    // 0x14ce72c: r0 = of()
    //     0x14ce72c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ce730: LoadField: r1 = r0->field_87
    //     0x14ce730: ldur            w1, [x0, #0x87]
    // 0x14ce734: DecompressPointer r1
    //     0x14ce734: add             x1, x1, HEAP, lsl #32
    // 0x14ce738: LoadField: r0 = r1->field_7
    //     0x14ce738: ldur            w0, [x1, #7]
    // 0x14ce73c: DecompressPointer r0
    //     0x14ce73c: add             x0, x0, HEAP, lsl #32
    // 0x14ce740: r16 = Instance_Color
    //     0x14ce740: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ce744: r30 = 12.000000
    //     0x14ce744: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14ce748: ldr             lr, [lr, #0x9e8]
    // 0x14ce74c: stp             lr, x16, [SP]
    // 0x14ce750: mov             x1, x0
    // 0x14ce754: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14ce754: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14ce758: ldr             x4, [x4, #0x9b8]
    // 0x14ce75c: r0 = copyWith()
    //     0x14ce75c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ce760: stur            x0, [fp, #-0x40]
    // 0x14ce764: r0 = ReadMoreText()
    //     0x14ce764: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0x14ce768: mov             x1, x0
    // 0x14ce76c: ldur            x0, [fp, #-0x20]
    // 0x14ce770: stur            x1, [fp, #-0x48]
    // 0x14ce774: StoreField: r1->field_3f = r0
    //     0x14ce774: stur            w0, [x1, #0x3f]
    // 0x14ce778: r0 = " Read Less"
    //     0x14ce778: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0x14ce77c: ldr             x0, [x0, #0x9c0]
    // 0x14ce780: StoreField: r1->field_43 = r0
    //     0x14ce780: stur            w0, [x1, #0x43]
    // 0x14ce784: r0 = "Read More"
    //     0x14ce784: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0x14ce788: ldr             x0, [x0, #0x9c8]
    // 0x14ce78c: StoreField: r1->field_47 = r0
    //     0x14ce78c: stur            w0, [x1, #0x47]
    // 0x14ce790: r0 = 240
    //     0x14ce790: movz            x0, #0xf0
    // 0x14ce794: StoreField: r1->field_f = r0
    //     0x14ce794: stur            x0, [x1, #0xf]
    // 0x14ce798: r0 = 2
    //     0x14ce798: movz            x0, #0x2
    // 0x14ce79c: ArrayStore: r1[0] = r0  ; List_8
    //     0x14ce79c: stur            x0, [x1, #0x17]
    // 0x14ce7a0: r0 = Instance_TrimMode
    //     0x14ce7a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0x14ce7a4: ldr             x0, [x0, #0x9d0]
    // 0x14ce7a8: StoreField: r1->field_1f = r0
    //     0x14ce7a8: stur            w0, [x1, #0x1f]
    // 0x14ce7ac: ldur            x0, [fp, #-0x30]
    // 0x14ce7b0: StoreField: r1->field_4f = r0
    //     0x14ce7b0: stur            w0, [x1, #0x4f]
    // 0x14ce7b4: r0 = Instance_TextAlign
    //     0x14ce7b4: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0x14ce7b8: StoreField: r1->field_53 = r0
    //     0x14ce7b8: stur            w0, [x1, #0x53]
    // 0x14ce7bc: ldur            x0, [fp, #-0x38]
    // 0x14ce7c0: StoreField: r1->field_23 = r0
    //     0x14ce7c0: stur            w0, [x1, #0x23]
    // 0x14ce7c4: ldur            x0, [fp, #-0x40]
    // 0x14ce7c8: StoreField: r1->field_27 = r0
    //     0x14ce7c8: stur            w0, [x1, #0x27]
    // 0x14ce7cc: r0 = "… "
    //     0x14ce7cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0x14ce7d0: ldr             x0, [x0, #0x9d8]
    // 0x14ce7d4: StoreField: r1->field_3b = r0
    //     0x14ce7d4: stur            w0, [x1, #0x3b]
    // 0x14ce7d8: r0 = true
    //     0x14ce7d8: add             x0, NULL, #0x20  ; true
    // 0x14ce7dc: StoreField: r1->field_37 = r0
    //     0x14ce7dc: stur            w0, [x1, #0x37]
    // 0x14ce7e0: r0 = Padding()
    //     0x14ce7e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ce7e4: mov             x2, x0
    // 0x14ce7e8: r0 = Instance_EdgeInsets
    //     0x14ce7e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0x14ce7ec: ldr             x0, [x0, #0x9e0]
    // 0x14ce7f0: stur            x2, [fp, #-0x20]
    // 0x14ce7f4: StoreField: r2->field_f = r0
    //     0x14ce7f4: stur            w0, [x2, #0xf]
    // 0x14ce7f8: ldur            x0, [fp, #-0x48]
    // 0x14ce7fc: StoreField: r2->field_b = r0
    //     0x14ce7fc: stur            w0, [x2, #0xb]
    // 0x14ce800: ldur            x0, [fp, #-0x28]
    // 0x14ce804: LoadField: r1 = r0->field_b
    //     0x14ce804: ldur            w1, [x0, #0xb]
    // 0x14ce808: LoadField: r3 = r0->field_f
    //     0x14ce808: ldur            w3, [x0, #0xf]
    // 0x14ce80c: DecompressPointer r3
    //     0x14ce80c: add             x3, x3, HEAP, lsl #32
    // 0x14ce810: LoadField: r4 = r3->field_b
    //     0x14ce810: ldur            w4, [x3, #0xb]
    // 0x14ce814: r3 = LoadInt32Instr(r1)
    //     0x14ce814: sbfx            x3, x1, #1, #0x1f
    // 0x14ce818: stur            x3, [fp, #-0x50]
    // 0x14ce81c: r1 = LoadInt32Instr(r4)
    //     0x14ce81c: sbfx            x1, x4, #1, #0x1f
    // 0x14ce820: cmp             x3, x1
    // 0x14ce824: b.ne            #0x14ce830
    // 0x14ce828: mov             x1, x0
    // 0x14ce82c: r0 = _growToNextCapacity()
    //     0x14ce82c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14ce830: ldur            x2, [fp, #-0x28]
    // 0x14ce834: ldur            x3, [fp, #-0x50]
    // 0x14ce838: add             x0, x3, #1
    // 0x14ce83c: lsl             x1, x0, #1
    // 0x14ce840: StoreField: r2->field_b = r1
    //     0x14ce840: stur            w1, [x2, #0xb]
    // 0x14ce844: LoadField: r1 = r2->field_f
    //     0x14ce844: ldur            w1, [x2, #0xf]
    // 0x14ce848: DecompressPointer r1
    //     0x14ce848: add             x1, x1, HEAP, lsl #32
    // 0x14ce84c: ldur            x0, [fp, #-0x20]
    // 0x14ce850: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14ce850: add             x25, x1, x3, lsl #2
    //     0x14ce854: add             x25, x25, #0xf
    //     0x14ce858: str             w0, [x25]
    //     0x14ce85c: tbz             w0, #0, #0x14ce878
    //     0x14ce860: ldurb           w16, [x1, #-1]
    //     0x14ce864: ldurb           w17, [x0, #-1]
    //     0x14ce868: and             x16, x17, x16, lsr #2
    //     0x14ce86c: tst             x16, HEAP, lsr #32
    //     0x14ce870: b.eq            #0x14ce878
    //     0x14ce874: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14ce878: b               #0x14ce880
    // 0x14ce87c: ldur            x2, [fp, #-0x28]
    // 0x14ce880: ldur            x3, [fp, #-8]
    // 0x14ce884: ldur            x0, [fp, #-0x10]
    // 0x14ce888: LoadField: r1 = r3->field_f
    //     0x14ce888: ldur            w1, [x3, #0xf]
    // 0x14ce88c: DecompressPointer r1
    //     0x14ce88c: add             x1, x1, HEAP, lsl #32
    // 0x14ce890: r0 = controller()
    //     0x14ce890: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce894: LoadField: r1 = r0->field_77
    //     0x14ce894: ldur            w1, [x0, #0x77]
    // 0x14ce898: DecompressPointer r1
    //     0x14ce898: add             x1, x1, HEAP, lsl #32
    // 0x14ce89c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce89c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce8a0: r0 = toList()
    //     0x14ce8a0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce8a4: mov             x3, x0
    // 0x14ce8a8: ldur            x2, [fp, #-0x10]
    // 0x14ce8ac: LoadField: r0 = r2->field_13
    //     0x14ce8ac: ldur            w0, [x2, #0x13]
    // 0x14ce8b0: DecompressPointer r0
    //     0x14ce8b0: add             x0, x0, HEAP, lsl #32
    // 0x14ce8b4: LoadField: r1 = r3->field_b
    //     0x14ce8b4: ldur            w1, [x3, #0xb]
    // 0x14ce8b8: r4 = LoadInt32Instr(r0)
    //     0x14ce8b8: sbfx            x4, x0, #1, #0x1f
    //     0x14ce8bc: tbz             w0, #0, #0x14ce8c4
    //     0x14ce8c0: ldur            x4, [x0, #7]
    // 0x14ce8c4: r0 = LoadInt32Instr(r1)
    //     0x14ce8c4: sbfx            x0, x1, #1, #0x1f
    // 0x14ce8c8: mov             x1, x4
    // 0x14ce8cc: cmp             x1, x0
    // 0x14ce8d0: b.hs            #0x14cedc8
    // 0x14ce8d4: LoadField: r0 = r3->field_f
    //     0x14ce8d4: ldur            w0, [x3, #0xf]
    // 0x14ce8d8: DecompressPointer r0
    //     0x14ce8d8: add             x0, x0, HEAP, lsl #32
    // 0x14ce8dc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce8dc: add             x16, x0, x4, lsl #2
    //     0x14ce8e0: ldur            w1, [x16, #0xf]
    // 0x14ce8e4: DecompressPointer r1
    //     0x14ce8e4: add             x1, x1, HEAP, lsl #32
    // 0x14ce8e8: cmp             w1, NULL
    // 0x14ce8ec: b.ne            #0x14ce8f8
    // 0x14ce8f0: r1 = Null
    //     0x14ce8f0: mov             x1, NULL
    // 0x14ce8f4: b               #0x14ce904
    // 0x14ce8f8: LoadField: r0 = r1->field_1b
    //     0x14ce8f8: ldur            w0, [x1, #0x1b]
    // 0x14ce8fc: DecompressPointer r0
    //     0x14ce8fc: add             x0, x0, HEAP, lsl #32
    // 0x14ce900: LoadField: r1 = r0->field_b
    //     0x14ce900: ldur            w1, [x0, #0xb]
    // 0x14ce904: ldur            x0, [fp, #-8]
    // 0x14ce908: cmp             w1, NULL
    // 0x14ce90c: b.eq            #0x14cedcc
    // 0x14ce910: r3 = LoadInt32Instr(r1)
    //     0x14ce910: sbfx            x3, x1, #1, #0x1f
    // 0x14ce914: cmp             x3, #1
    // 0x14ce918: r16 = true
    //     0x14ce918: add             x16, NULL, #0x20  ; true
    // 0x14ce91c: r17 = false
    //     0x14ce91c: add             x17, NULL, #0x30  ; false
    // 0x14ce920: csel            x4, x16, x17, ge
    // 0x14ce924: stur            x4, [fp, #-0x20]
    // 0x14ce928: LoadField: r1 = r0->field_f
    //     0x14ce928: ldur            w1, [x0, #0xf]
    // 0x14ce92c: DecompressPointer r1
    //     0x14ce92c: add             x1, x1, HEAP, lsl #32
    // 0x14ce930: r0 = controller()
    //     0x14ce930: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce934: LoadField: r1 = r0->field_77
    //     0x14ce934: ldur            w1, [x0, #0x77]
    // 0x14ce938: DecompressPointer r1
    //     0x14ce938: add             x1, x1, HEAP, lsl #32
    // 0x14ce93c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce93c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce940: r0 = toList()
    //     0x14ce940: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce944: mov             x3, x0
    // 0x14ce948: ldur            x2, [fp, #-0x10]
    // 0x14ce94c: LoadField: r0 = r2->field_13
    //     0x14ce94c: ldur            w0, [x2, #0x13]
    // 0x14ce950: DecompressPointer r0
    //     0x14ce950: add             x0, x0, HEAP, lsl #32
    // 0x14ce954: LoadField: r1 = r3->field_b
    //     0x14ce954: ldur            w1, [x3, #0xb]
    // 0x14ce958: r4 = LoadInt32Instr(r0)
    //     0x14ce958: sbfx            x4, x0, #1, #0x1f
    //     0x14ce95c: tbz             w0, #0, #0x14ce964
    //     0x14ce960: ldur            x4, [x0, #7]
    // 0x14ce964: r0 = LoadInt32Instr(r1)
    //     0x14ce964: sbfx            x0, x1, #1, #0x1f
    // 0x14ce968: mov             x1, x4
    // 0x14ce96c: cmp             x1, x0
    // 0x14ce970: b.hs            #0x14cedd0
    // 0x14ce974: LoadField: r0 = r3->field_f
    //     0x14ce974: ldur            w0, [x3, #0xf]
    // 0x14ce978: DecompressPointer r0
    //     0x14ce978: add             x0, x0, HEAP, lsl #32
    // 0x14ce97c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ce97c: add             x16, x0, x4, lsl #2
    //     0x14ce980: ldur            w1, [x16, #0xf]
    // 0x14ce984: DecompressPointer r1
    //     0x14ce984: add             x1, x1, HEAP, lsl #32
    // 0x14ce988: cmp             w1, NULL
    // 0x14ce98c: b.ne            #0x14ce998
    // 0x14ce990: r0 = Null
    //     0x14ce990: mov             x0, NULL
    // 0x14ce994: b               #0x14ce9b4
    // 0x14ce998: LoadField: r0 = r1->field_1b
    //     0x14ce998: ldur            w0, [x1, #0x1b]
    // 0x14ce99c: DecompressPointer r0
    //     0x14ce99c: add             x0, x0, HEAP, lsl #32
    // 0x14ce9a0: LoadField: r1 = r0->field_b
    //     0x14ce9a0: ldur            w1, [x0, #0xb]
    // 0x14ce9a4: cbz             w1, #0x14ce9b0
    // 0x14ce9a8: r0 = false
    //     0x14ce9a8: add             x0, NULL, #0x30  ; false
    // 0x14ce9ac: b               #0x14ce9b4
    // 0x14ce9b0: r0 = true
    //     0x14ce9b0: add             x0, NULL, #0x20  ; true
    // 0x14ce9b4: cmp             w0, NULL
    // 0x14ce9b8: b.eq            #0x14ce9c8
    // 0x14ce9bc: tbnz            w0, #4, #0x14ce9c8
    // 0x14ce9c0: d0 = 0.000000
    //     0x14ce9c0: eor             v0.16b, v0.16b, v0.16b
    // 0x14ce9c4: b               #0x14ce9cc
    // 0x14ce9c8: d0 = 48.000000
    //     0x14ce9c8: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0x14ce9cc: ldur            x0, [fp, #-8]
    // 0x14ce9d0: stur            d0, [fp, #-0x58]
    // 0x14ce9d4: LoadField: r1 = r0->field_f
    //     0x14ce9d4: ldur            w1, [x0, #0xf]
    // 0x14ce9d8: DecompressPointer r1
    //     0x14ce9d8: add             x1, x1, HEAP, lsl #32
    // 0x14ce9dc: r0 = controller()
    //     0x14ce9dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ce9e0: LoadField: r1 = r0->field_77
    //     0x14ce9e0: ldur            w1, [x0, #0x77]
    // 0x14ce9e4: DecompressPointer r1
    //     0x14ce9e4: add             x1, x1, HEAP, lsl #32
    // 0x14ce9e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ce9e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ce9ec: r0 = toList()
    //     0x14ce9ec: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ce9f0: mov             x2, x0
    // 0x14ce9f4: ldur            x3, [fp, #-0x10]
    // 0x14ce9f8: LoadField: r0 = r3->field_13
    //     0x14ce9f8: ldur            w0, [x3, #0x13]
    // 0x14ce9fc: DecompressPointer r0
    //     0x14ce9fc: add             x0, x0, HEAP, lsl #32
    // 0x14cea00: LoadField: r1 = r2->field_b
    //     0x14cea00: ldur            w1, [x2, #0xb]
    // 0x14cea04: r4 = LoadInt32Instr(r0)
    //     0x14cea04: sbfx            x4, x0, #1, #0x1f
    //     0x14cea08: tbz             w0, #0, #0x14cea10
    //     0x14cea0c: ldur            x4, [x0, #7]
    // 0x14cea10: r0 = LoadInt32Instr(r1)
    //     0x14cea10: sbfx            x0, x1, #1, #0x1f
    // 0x14cea14: mov             x1, x4
    // 0x14cea18: cmp             x1, x0
    // 0x14cea1c: b.hs            #0x14cedd4
    // 0x14cea20: LoadField: r0 = r2->field_f
    //     0x14cea20: ldur            w0, [x2, #0xf]
    // 0x14cea24: DecompressPointer r0
    //     0x14cea24: add             x0, x0, HEAP, lsl #32
    // 0x14cea28: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cea28: add             x16, x0, x4, lsl #2
    //     0x14cea2c: ldur            w1, [x16, #0xf]
    // 0x14cea30: DecompressPointer r1
    //     0x14cea30: add             x1, x1, HEAP, lsl #32
    // 0x14cea34: cmp             w1, NULL
    // 0x14cea38: b.ne            #0x14cea44
    // 0x14cea3c: r0 = Null
    //     0x14cea3c: mov             x0, NULL
    // 0x14cea40: b               #0x14cea54
    // 0x14cea44: LoadField: r0 = r1->field_1b
    //     0x14cea44: ldur            w0, [x1, #0x1b]
    // 0x14cea48: DecompressPointer r0
    //     0x14cea48: add             x0, x0, HEAP, lsl #32
    // 0x14cea4c: LoadField: r1 = r0->field_b
    //     0x14cea4c: ldur            w1, [x0, #0xb]
    // 0x14cea50: mov             x0, x1
    // 0x14cea54: cmp             w0, NULL
    // 0x14cea58: b.ne            #0x14cea64
    // 0x14cea5c: r5 = 0
    //     0x14cea5c: movz            x5, #0
    // 0x14cea60: b               #0x14cea6c
    // 0x14cea64: r1 = LoadInt32Instr(r0)
    //     0x14cea64: sbfx            x1, x0, #1, #0x1f
    // 0x14cea68: mov             x5, x1
    // 0x14cea6c: ldur            x0, [fp, #-0x20]
    // 0x14cea70: ldur            d0, [fp, #-0x58]
    // 0x14cea74: ldur            x4, [fp, #-0x28]
    // 0x14cea78: stur            x5, [fp, #-0x50]
    // 0x14cea7c: r1 = Function '<anonymous closure>':.
    //     0x14cea7c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d90] AnonymousClosure: (0x9b3480), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cea80: ldr             x1, [x1, #0xd90]
    // 0x14cea84: r2 = Null
    //     0x14cea84: mov             x2, NULL
    // 0x14cea88: r0 = AllocateClosure()
    //     0x14cea88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cea8c: ldur            x2, [fp, #-0x10]
    // 0x14cea90: r1 = Function '<anonymous closure>':.
    //     0x14cea90: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d98] AnonymousClosure: (0x14cf4b4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14cea94: ldr             x1, [x1, #0xd98]
    // 0x14cea98: stur            x0, [fp, #-8]
    // 0x14cea9c: r0 = AllocateClosure()
    //     0x14cea9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ceaa0: stur            x0, [fp, #-0x30]
    // 0x14ceaa4: r0 = ListView()
    //     0x14ceaa4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14ceaa8: stur            x0, [fp, #-0x38]
    // 0x14ceaac: r16 = true
    //     0x14ceaac: add             x16, NULL, #0x20  ; true
    // 0x14ceab0: r30 = Instance_Axis
    //     0x14ceab0: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14ceab4: stp             lr, x16, [SP]
    // 0x14ceab8: mov             x1, x0
    // 0x14ceabc: ldur            x2, [fp, #-0x30]
    // 0x14ceac0: ldur            x3, [fp, #-0x50]
    // 0x14ceac4: ldur            x5, [fp, #-8]
    // 0x14ceac8: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0x14ceac8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14ceacc: ldr             x4, [x4, #0x8e8]
    // 0x14cead0: r0 = ListView.separated()
    //     0x14cead0: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14cead4: r0 = SizedBox()
    //     0x14cead4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14cead8: mov             x1, x0
    // 0x14ceadc: r0 = inf
    //     0x14ceadc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14ceae0: ldr             x0, [x0, #0x9f8]
    // 0x14ceae4: stur            x1, [fp, #-8]
    // 0x14ceae8: StoreField: r1->field_f = r0
    //     0x14ceae8: stur            w0, [x1, #0xf]
    // 0x14ceaec: ldur            d0, [fp, #-0x58]
    // 0x14ceaf0: r0 = inline_Allocate_Double()
    //     0x14ceaf0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x14ceaf4: add             x0, x0, #0x10
    //     0x14ceaf8: cmp             x2, x0
    //     0x14ceafc: b.ls            #0x14cedd8
    //     0x14ceb00: str             x0, [THR, #0x50]  ; THR::top
    //     0x14ceb04: sub             x0, x0, #0xf
    //     0x14ceb08: movz            x2, #0xe15c
    //     0x14ceb0c: movk            x2, #0x3, lsl #16
    //     0x14ceb10: stur            x2, [x0, #-1]
    // 0x14ceb14: StoreField: r0->field_7 = d0
    //     0x14ceb14: stur            d0, [x0, #7]
    // 0x14ceb18: StoreField: r1->field_13 = r0
    //     0x14ceb18: stur            w0, [x1, #0x13]
    // 0x14ceb1c: ldur            x0, [fp, #-0x38]
    // 0x14ceb20: StoreField: r1->field_b = r0
    //     0x14ceb20: stur            w0, [x1, #0xb]
    // 0x14ceb24: r0 = Padding()
    //     0x14ceb24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ceb28: mov             x1, x0
    // 0x14ceb2c: r0 = Instance_EdgeInsets
    //     0x14ceb2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x14ceb30: ldr             x0, [x0, #0xa00]
    // 0x14ceb34: stur            x1, [fp, #-0x30]
    // 0x14ceb38: StoreField: r1->field_f = r0
    //     0x14ceb38: stur            w0, [x1, #0xf]
    // 0x14ceb3c: ldur            x0, [fp, #-8]
    // 0x14ceb40: StoreField: r1->field_b = r0
    //     0x14ceb40: stur            w0, [x1, #0xb]
    // 0x14ceb44: r0 = Visibility()
    //     0x14ceb44: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14ceb48: mov             x1, x0
    // 0x14ceb4c: ldur            x0, [fp, #-0x30]
    // 0x14ceb50: stur            x1, [fp, #-8]
    // 0x14ceb54: StoreField: r1->field_b = r0
    //     0x14ceb54: stur            w0, [x1, #0xb]
    // 0x14ceb58: r0 = Instance_SizedBox
    //     0x14ceb58: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14ceb5c: StoreField: r1->field_f = r0
    //     0x14ceb5c: stur            w0, [x1, #0xf]
    // 0x14ceb60: ldur            x0, [fp, #-0x20]
    // 0x14ceb64: StoreField: r1->field_13 = r0
    //     0x14ceb64: stur            w0, [x1, #0x13]
    // 0x14ceb68: r0 = false
    //     0x14ceb68: add             x0, NULL, #0x30  ; false
    // 0x14ceb6c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14ceb6c: stur            w0, [x1, #0x17]
    // 0x14ceb70: StoreField: r1->field_1b = r0
    //     0x14ceb70: stur            w0, [x1, #0x1b]
    // 0x14ceb74: StoreField: r1->field_1f = r0
    //     0x14ceb74: stur            w0, [x1, #0x1f]
    // 0x14ceb78: StoreField: r1->field_23 = r0
    //     0x14ceb78: stur            w0, [x1, #0x23]
    // 0x14ceb7c: StoreField: r1->field_27 = r0
    //     0x14ceb7c: stur            w0, [x1, #0x27]
    // 0x14ceb80: StoreField: r1->field_2b = r0
    //     0x14ceb80: stur            w0, [x1, #0x2b]
    // 0x14ceb84: r0 = GestureDetector()
    //     0x14ceb84: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14ceb88: ldur            x2, [fp, #-0x10]
    // 0x14ceb8c: r1 = Function '<anonymous closure>':.
    //     0x14ceb8c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41da0] AnonymousClosure: (0x9b3408), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14ceb90: ldr             x1, [x1, #0xda0]
    // 0x14ceb94: stur            x0, [fp, #-0x20]
    // 0x14ceb98: r0 = AllocateClosure()
    //     0x14ceb98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ceb9c: ldur            x2, [fp, #-0x10]
    // 0x14ceba0: r1 = Function '<anonymous closure>':.
    //     0x14ceba0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41da8] AnonymousClosure: (0x14cedf0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14ceba4: ldr             x1, [x1, #0xda8]
    // 0x14ceba8: stur            x0, [fp, #-0x10]
    // 0x14cebac: r0 = AllocateClosure()
    //     0x14cebac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cebb0: ldur            x16, [fp, #-0x10]
    // 0x14cebb4: stp             x0, x16, [SP, #8]
    // 0x14cebb8: r16 = Instance_Icon
    //     0x14cebb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa18] Obj!Icon@d65e71
    //     0x14cebbc: ldr             x16, [x16, #0xa18]
    // 0x14cebc0: str             x16, [SP]
    // 0x14cebc4: ldur            x1, [fp, #-0x20]
    // 0x14cebc8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0x14cebc8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0x14cebcc: ldr             x4, [x4, #0xa20]
    // 0x14cebd0: r0 = GestureDetector()
    //     0x14cebd0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14cebd4: r0 = Align()
    //     0x14cebd4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14cebd8: mov             x3, x0
    // 0x14cebdc: r0 = Instance_Alignment
    //     0x14cebdc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x14cebe0: ldr             x0, [x0, #0xa28]
    // 0x14cebe4: stur            x3, [fp, #-0x10]
    // 0x14cebe8: StoreField: r3->field_f = r0
    //     0x14cebe8: stur            w0, [x3, #0xf]
    // 0x14cebec: ldur            x1, [fp, #-0x20]
    // 0x14cebf0: StoreField: r3->field_b = r1
    //     0x14cebf0: stur            w1, [x3, #0xb]
    // 0x14cebf4: r1 = Null
    //     0x14cebf4: mov             x1, NULL
    // 0x14cebf8: r2 = 4
    //     0x14cebf8: movz            x2, #0x4
    // 0x14cebfc: r0 = AllocateArray()
    //     0x14cebfc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cec00: mov             x2, x0
    // 0x14cec04: ldur            x0, [fp, #-8]
    // 0x14cec08: stur            x2, [fp, #-0x20]
    // 0x14cec0c: StoreField: r2->field_f = r0
    //     0x14cec0c: stur            w0, [x2, #0xf]
    // 0x14cec10: ldur            x0, [fp, #-0x10]
    // 0x14cec14: StoreField: r2->field_13 = r0
    //     0x14cec14: stur            w0, [x2, #0x13]
    // 0x14cec18: r1 = <Widget>
    //     0x14cec18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cec1c: r0 = AllocateGrowableArray()
    //     0x14cec1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cec20: mov             x1, x0
    // 0x14cec24: ldur            x0, [fp, #-0x20]
    // 0x14cec28: stur            x1, [fp, #-8]
    // 0x14cec2c: StoreField: r1->field_f = r0
    //     0x14cec2c: stur            w0, [x1, #0xf]
    // 0x14cec30: r0 = 4
    //     0x14cec30: movz            x0, #0x4
    // 0x14cec34: StoreField: r1->field_b = r0
    //     0x14cec34: stur            w0, [x1, #0xb]
    // 0x14cec38: r0 = Stack()
    //     0x14cec38: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14cec3c: mov             x2, x0
    // 0x14cec40: r0 = Instance_Alignment
    //     0x14cec40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x14cec44: ldr             x0, [x0, #0xa28]
    // 0x14cec48: stur            x2, [fp, #-0x10]
    // 0x14cec4c: StoreField: r2->field_f = r0
    //     0x14cec4c: stur            w0, [x2, #0xf]
    // 0x14cec50: r0 = Instance_StackFit
    //     0x14cec50: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14cec54: ldr             x0, [x0, #0xfa8]
    // 0x14cec58: ArrayStore: r2[0] = r0  ; List_4
    //     0x14cec58: stur            w0, [x2, #0x17]
    // 0x14cec5c: r0 = Instance_Clip
    //     0x14cec5c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14cec60: ldr             x0, [x0, #0x7e0]
    // 0x14cec64: StoreField: r2->field_1b = r0
    //     0x14cec64: stur            w0, [x2, #0x1b]
    // 0x14cec68: ldur            x0, [fp, #-8]
    // 0x14cec6c: StoreField: r2->field_b = r0
    //     0x14cec6c: stur            w0, [x2, #0xb]
    // 0x14cec70: ldur            x0, [fp, #-0x28]
    // 0x14cec74: LoadField: r1 = r0->field_b
    //     0x14cec74: ldur            w1, [x0, #0xb]
    // 0x14cec78: LoadField: r3 = r0->field_f
    //     0x14cec78: ldur            w3, [x0, #0xf]
    // 0x14cec7c: DecompressPointer r3
    //     0x14cec7c: add             x3, x3, HEAP, lsl #32
    // 0x14cec80: LoadField: r4 = r3->field_b
    //     0x14cec80: ldur            w4, [x3, #0xb]
    // 0x14cec84: r3 = LoadInt32Instr(r1)
    //     0x14cec84: sbfx            x3, x1, #1, #0x1f
    // 0x14cec88: stur            x3, [fp, #-0x50]
    // 0x14cec8c: r1 = LoadInt32Instr(r4)
    //     0x14cec8c: sbfx            x1, x4, #1, #0x1f
    // 0x14cec90: cmp             x3, x1
    // 0x14cec94: b.ne            #0x14ceca0
    // 0x14cec98: mov             x1, x0
    // 0x14cec9c: r0 = _growToNextCapacity()
    //     0x14cec9c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14ceca0: ldur            x2, [fp, #-0x28]
    // 0x14ceca4: ldur            x3, [fp, #-0x50]
    // 0x14ceca8: add             x0, x3, #1
    // 0x14cecac: lsl             x1, x0, #1
    // 0x14cecb0: StoreField: r2->field_b = r1
    //     0x14cecb0: stur            w1, [x2, #0xb]
    // 0x14cecb4: LoadField: r1 = r2->field_f
    //     0x14cecb4: ldur            w1, [x2, #0xf]
    // 0x14cecb8: DecompressPointer r1
    //     0x14cecb8: add             x1, x1, HEAP, lsl #32
    // 0x14cecbc: ldur            x0, [fp, #-0x10]
    // 0x14cecc0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14cecc0: add             x25, x1, x3, lsl #2
    //     0x14cecc4: add             x25, x25, #0xf
    //     0x14cecc8: str             w0, [x25]
    //     0x14ceccc: tbz             w0, #0, #0x14cece8
    //     0x14cecd0: ldurb           w16, [x1, #-1]
    //     0x14cecd4: ldurb           w17, [x0, #-1]
    //     0x14cecd8: and             x16, x17, x16, lsr #2
    //     0x14cecdc: tst             x16, HEAP, lsr #32
    //     0x14cece0: b.eq            #0x14cece8
    //     0x14cece4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cece8: r0 = Column()
    //     0x14cece8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14cecec: mov             x1, x0
    // 0x14cecf0: r0 = Instance_Axis
    //     0x14cecf0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14cecf4: stur            x1, [fp, #-8]
    // 0x14cecf8: StoreField: r1->field_f = r0
    //     0x14cecf8: stur            w0, [x1, #0xf]
    // 0x14cecfc: r0 = Instance_MainAxisAlignment
    //     0x14cecfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ced00: ldr             x0, [x0, #0xa08]
    // 0x14ced04: StoreField: r1->field_13 = r0
    //     0x14ced04: stur            w0, [x1, #0x13]
    // 0x14ced08: r0 = Instance_MainAxisSize
    //     0x14ced08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ced0c: ldr             x0, [x0, #0xa10]
    // 0x14ced10: ArrayStore: r1[0] = r0  ; List_4
    //     0x14ced10: stur            w0, [x1, #0x17]
    // 0x14ced14: r0 = Instance_CrossAxisAlignment
    //     0x14ced14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14ced18: ldr             x0, [x0, #0x890]
    // 0x14ced1c: StoreField: r1->field_1b = r0
    //     0x14ced1c: stur            w0, [x1, #0x1b]
    // 0x14ced20: r0 = Instance_VerticalDirection
    //     0x14ced20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ced24: ldr             x0, [x0, #0xa20]
    // 0x14ced28: StoreField: r1->field_23 = r0
    //     0x14ced28: stur            w0, [x1, #0x23]
    // 0x14ced2c: r0 = Instance_Clip
    //     0x14ced2c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ced30: ldr             x0, [x0, #0x38]
    // 0x14ced34: StoreField: r1->field_2b = r0
    //     0x14ced34: stur            w0, [x1, #0x2b]
    // 0x14ced38: StoreField: r1->field_2f = rZR
    //     0x14ced38: stur            xzr, [x1, #0x2f]
    // 0x14ced3c: ldur            x0, [fp, #-0x28]
    // 0x14ced40: StoreField: r1->field_b = r0
    //     0x14ced40: stur            w0, [x1, #0xb]
    // 0x14ced44: r0 = Padding()
    //     0x14ced44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ced48: mov             x1, x0
    // 0x14ced4c: r0 = Instance_EdgeInsets
    //     0x14ced4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14ced50: ldr             x0, [x0, #0x1f0]
    // 0x14ced54: stur            x1, [fp, #-0x10]
    // 0x14ced58: StoreField: r1->field_f = r0
    //     0x14ced58: stur            w0, [x1, #0xf]
    // 0x14ced5c: ldur            x0, [fp, #-8]
    // 0x14ced60: StoreField: r1->field_b = r0
    //     0x14ced60: stur            w0, [x1, #0xb]
    // 0x14ced64: r0 = Container()
    //     0x14ced64: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ced68: stur            x0, [fp, #-8]
    // 0x14ced6c: ldur            x16, [fp, #-0x18]
    // 0x14ced70: ldur            lr, [fp, #-0x10]
    // 0x14ced74: stp             lr, x16, [SP]
    // 0x14ced78: mov             x1, x0
    // 0x14ced7c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14ced7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14ced80: ldr             x4, [x4, #0x88]
    // 0x14ced84: r0 = Container()
    //     0x14ced84: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ced88: ldur            x0, [fp, #-8]
    // 0x14ced8c: LeaveFrame
    //     0x14ced8c: mov             SP, fp
    //     0x14ced90: ldp             fp, lr, [SP], #0x10
    // 0x14ced94: ret
    //     0x14ced94: ret             
    // 0x14ced98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ced98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ced9c: b               #0x14cda28
    // 0x14ceda0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ceda0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ceda4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ceda4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ceda8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ceda8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedb0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedb4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedbc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedc0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedc8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14cedcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x14cedd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cedd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cedd8: SaveReg d0
    //     0x14cedd8: str             q0, [SP, #-0x10]!
    // 0x14ceddc: SaveReg r1
    //     0x14ceddc: str             x1, [SP, #-8]!
    // 0x14cede0: r0 = AllocateDouble()
    //     0x14cede0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14cede4: RestoreReg r1
    //     0x14cede4: ldr             x1, [SP], #8
    // 0x14cede8: RestoreReg d0
    //     0x14cede8: ldr             q0, [SP], #0x10
    // 0x14cedec: b               #0x14ceb14
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14cedf0, size: 0x178
    // 0x14cedf0: EnterFrame
    //     0x14cedf0: stp             fp, lr, [SP, #-0x10]!
    //     0x14cedf4: mov             fp, SP
    // 0x14cedf8: AllocStack(0x28)
    //     0x14cedf8: sub             SP, SP, #0x28
    // 0x14cedfc: SetupParameters()
    //     0x14cedfc: ldr             x0, [fp, #0x10]
    //     0x14cee00: ldur            w2, [x0, #0x17]
    //     0x14cee04: add             x2, x2, HEAP, lsl #32
    //     0x14cee08: stur            x2, [fp, #-0x10]
    // 0x14cee0c: CheckStackOverflow
    //     0x14cee0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cee10: cmp             SP, x16
    //     0x14cee14: b.ls            #0x14cef58
    // 0x14cee18: LoadField: r0 = r2->field_b
    //     0x14cee18: ldur            w0, [x2, #0xb]
    // 0x14cee1c: DecompressPointer r0
    //     0x14cee1c: add             x0, x0, HEAP, lsl #32
    // 0x14cee20: stur            x0, [fp, #-8]
    // 0x14cee24: LoadField: r1 = r0->field_f
    //     0x14cee24: ldur            w1, [x0, #0xf]
    // 0x14cee28: DecompressPointer r1
    //     0x14cee28: add             x1, x1, HEAP, lsl #32
    // 0x14cee2c: r0 = controller()
    //     0x14cee2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cee30: LoadField: r1 = r0->field_8f
    //     0x14cee30: ldur            w1, [x0, #0x8f]
    // 0x14cee34: DecompressPointer r1
    //     0x14cee34: add             x1, x1, HEAP, lsl #32
    // 0x14cee38: cmp             w1, NULL
    // 0x14cee3c: b.eq            #0x14cef48
    // 0x14cee40: ldur            x0, [fp, #-0x10]
    // 0x14cee44: ldur            x2, [fp, #-8]
    // 0x14cee48: LoadField: r1 = r2->field_f
    //     0x14cee48: ldur            w1, [x2, #0xf]
    // 0x14cee4c: DecompressPointer r1
    //     0x14cee4c: add             x1, x1, HEAP, lsl #32
    // 0x14cee50: r0 = controller()
    //     0x14cee50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cee54: mov             x1, x0
    // 0x14cee58: r2 = "flag_dots"
    //     0x14cee58: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fa30] "flag_dots"
    //     0x14cee5c: ldr             x2, [x2, #0xa30]
    // 0x14cee60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14cee60: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14cee64: r0 = ratingReviewClickedEvent()
    //     0x14cee64: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x14cee68: ldur            x0, [fp, #-8]
    // 0x14cee6c: LoadField: r2 = r0->field_f
    //     0x14cee6c: ldur            w2, [x0, #0xf]
    // 0x14cee70: DecompressPointer r2
    //     0x14cee70: add             x2, x2, HEAP, lsl #32
    // 0x14cee74: ldur            x3, [fp, #-0x10]
    // 0x14cee78: stur            x2, [fp, #-0x20]
    // 0x14cee7c: LoadField: r4 = r3->field_f
    //     0x14cee7c: ldur            w4, [x3, #0xf]
    // 0x14cee80: DecompressPointer r4
    //     0x14cee80: add             x4, x4, HEAP, lsl #32
    // 0x14cee84: mov             x1, x2
    // 0x14cee88: stur            x4, [fp, #-0x18]
    // 0x14cee8c: r0 = controller()
    //     0x14cee8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cee90: LoadField: r3 = r0->field_8f
    //     0x14cee90: ldur            w3, [x0, #0x8f]
    // 0x14cee94: DecompressPointer r3
    //     0x14cee94: add             x3, x3, HEAP, lsl #32
    // 0x14cee98: stur            x3, [fp, #-0x28]
    // 0x14cee9c: cmp             w3, NULL
    // 0x14ceea0: b.eq            #0x14cef60
    // 0x14ceea4: ldur            x0, [fp, #-8]
    // 0x14ceea8: LoadField: r1 = r0->field_f
    //     0x14ceea8: ldur            w1, [x0, #0xf]
    // 0x14ceeac: DecompressPointer r1
    //     0x14ceeac: add             x1, x1, HEAP, lsl #32
    // 0x14ceeb0: r0 = controller()
    //     0x14ceeb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ceeb4: LoadField: r1 = r0->field_77
    //     0x14ceeb4: ldur            w1, [x0, #0x77]
    // 0x14ceeb8: DecompressPointer r1
    //     0x14ceeb8: add             x1, x1, HEAP, lsl #32
    // 0x14ceebc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ceebc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ceec0: r0 = toList()
    //     0x14ceec0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14ceec4: mov             x2, x0
    // 0x14ceec8: ldur            x0, [fp, #-0x10]
    // 0x14ceecc: LoadField: r1 = r0->field_13
    //     0x14ceecc: ldur            w1, [x0, #0x13]
    // 0x14ceed0: DecompressPointer r1
    //     0x14ceed0: add             x1, x1, HEAP, lsl #32
    // 0x14ceed4: LoadField: r0 = r2->field_b
    //     0x14ceed4: ldur            w0, [x2, #0xb]
    // 0x14ceed8: r3 = LoadInt32Instr(r1)
    //     0x14ceed8: sbfx            x3, x1, #1, #0x1f
    //     0x14ceedc: tbz             w1, #0, #0x14ceee4
    //     0x14ceee0: ldur            x3, [x1, #7]
    // 0x14ceee4: r1 = LoadInt32Instr(r0)
    //     0x14ceee4: sbfx            x1, x0, #1, #0x1f
    // 0x14ceee8: mov             x0, x1
    // 0x14ceeec: mov             x1, x3
    // 0x14ceef0: cmp             x1, x0
    // 0x14ceef4: b.hs            #0x14cef64
    // 0x14ceef8: LoadField: r0 = r2->field_f
    //     0x14ceef8: ldur            w0, [x2, #0xf]
    // 0x14ceefc: DecompressPointer r0
    //     0x14ceefc: add             x0, x0, HEAP, lsl #32
    // 0x14cef00: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14cef00: add             x16, x0, x3, lsl #2
    //     0x14cef04: ldur            w1, [x16, #0xf]
    // 0x14cef08: DecompressPointer r1
    //     0x14cef08: add             x1, x1, HEAP, lsl #32
    // 0x14cef0c: cmp             w1, NULL
    // 0x14cef10: b.ne            #0x14cef1c
    // 0x14cef14: r0 = Null
    //     0x14cef14: mov             x0, NULL
    // 0x14cef18: b               #0x14cef24
    // 0x14cef1c: LoadField: r0 = r1->field_b
    //     0x14cef1c: ldur            w0, [x1, #0xb]
    // 0x14cef20: DecompressPointer r0
    //     0x14cef20: add             x0, x0, HEAP, lsl #32
    // 0x14cef24: cmp             w0, NULL
    // 0x14cef28: b.ne            #0x14cef34
    // 0x14cef2c: r5 = ""
    //     0x14cef2c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cef30: b               #0x14cef38
    // 0x14cef34: mov             x5, x0
    // 0x14cef38: ldur            x1, [fp, #-0x20]
    // 0x14cef3c: ldur            x2, [fp, #-0x18]
    // 0x14cef40: ldur            x3, [fp, #-0x28]
    // 0x14cef44: r0 = showMenuItem()
    //     0x14cef44: bl              #0x14cef68  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem
    // 0x14cef48: r0 = Null
    //     0x14cef48: mov             x0, NULL
    // 0x14cef4c: LeaveFrame
    //     0x14cef4c: mov             SP, fp
    //     0x14cef50: ldp             fp, lr, [SP], #0x10
    // 0x14cef54: ret
    //     0x14cef54: ret             
    // 0x14cef58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cef58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cef5c: b               #0x14cee18
    // 0x14cef60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14cef60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x14cef64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cef64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0x14cef68, size: 0x54c
    // 0x14cef68: EnterFrame
    //     0x14cef68: stp             fp, lr, [SP, #-0x10]!
    //     0x14cef6c: mov             fp, SP
    // 0x14cef70: AllocStack(0xa0)
    //     0x14cef70: sub             SP, SP, #0xa0
    // 0x14cef74: SetupParameters(ReviewListWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x14cef74: mov             x0, x1
    //     0x14cef78: stur            x1, [fp, #-8]
    //     0x14cef7c: mov             x1, x2
    //     0x14cef80: stur            x2, [fp, #-0x10]
    //     0x14cef84: stur            x3, [fp, #-0x18]
    //     0x14cef88: stur            x5, [fp, #-0x20]
    // 0x14cef8c: CheckStackOverflow
    //     0x14cef8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cef90: cmp             SP, x16
    //     0x14cef94: b.ls            #0x14cf4ac
    // 0x14cef98: r1 = 2
    //     0x14cef98: movz            x1, #0x2
    // 0x14cef9c: r0 = AllocateContext()
    //     0x14cef9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14cefa0: mov             x2, x0
    // 0x14cefa4: ldur            x0, [fp, #-8]
    // 0x14cefa8: stur            x2, [fp, #-0x28]
    // 0x14cefac: StoreField: r2->field_f = r0
    //     0x14cefac: stur            w0, [x2, #0xf]
    // 0x14cefb0: ldur            x1, [fp, #-0x20]
    // 0x14cefb4: StoreField: r2->field_13 = r1
    //     0x14cefb4: stur            w1, [x2, #0x13]
    // 0x14cefb8: mov             x1, x0
    // 0x14cefbc: r0 = controller()
    //     0x14cefbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cefc0: LoadField: r1 = r0->field_93
    //     0x14cefc0: ldur            w1, [x0, #0x93]
    // 0x14cefc4: DecompressPointer r1
    //     0x14cefc4: add             x1, x1, HEAP, lsl #32
    // 0x14cefc8: ldur            x0, [fp, #-0x28]
    // 0x14cefcc: LoadField: r2 = r0->field_13
    //     0x14cefcc: ldur            w2, [x0, #0x13]
    // 0x14cefd0: DecompressPointer r2
    //     0x14cefd0: add             x2, x2, HEAP, lsl #32
    // 0x14cefd4: r0 = []()
    //     0x14cefd4: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x14cefd8: r1 = 60
    //     0x14cefd8: movz            x1, #0x3c
    // 0x14cefdc: branchIfSmi(r0, 0x14cefe8)
    //     0x14cefdc: tbz             w0, #0, #0x14cefe8
    // 0x14cefe0: r1 = LoadClassIdInstr(r0)
    //     0x14cefe0: ldur            x1, [x0, #-1]
    //     0x14cefe4: ubfx            x1, x1, #0xc, #0x14
    // 0x14cefe8: r16 = true
    //     0x14cefe8: add             x16, NULL, #0x20  ; true
    // 0x14cefec: stp             x16, x0, [SP]
    // 0x14ceff0: mov             x0, x1
    // 0x14ceff4: mov             lr, x0
    // 0x14ceff8: ldr             lr, [x21, lr, lsl #3]
    // 0x14ceffc: blr             lr
    // 0x14cf000: tbnz            w0, #4, #0x14cf00c
    // 0x14cf004: d0 = 100.000000
    //     0x14cf004: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0x14cf008: b               #0x14cf014
    // 0x14cf00c: d0 = 120.000000
    //     0x14cf00c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0x14cf010: ldr             d0, [x17, #0xa38]
    // 0x14cf014: ldur            x0, [fp, #-0x18]
    // 0x14cf018: ldur            x2, [fp, #-0x28]
    // 0x14cf01c: stur            d0, [fp, #-0x58]
    // 0x14cf020: r0 = BoxConstraints()
    //     0x14cf020: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x14cf024: stur            x0, [fp, #-0x20]
    // 0x14cf028: StoreField: r0->field_7 = rZR
    //     0x14cf028: stur            xzr, [x0, #7]
    // 0x14cf02c: ldur            d0, [fp, #-0x58]
    // 0x14cf030: StoreField: r0->field_f = d0
    //     0x14cf030: stur            d0, [x0, #0xf]
    // 0x14cf034: ArrayStore: r0[0] = rZR  ; List_8
    //     0x14cf034: stur            xzr, [x0, #0x17]
    // 0x14cf038: d0 = inf
    //     0x14cf038: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x14cf03c: StoreField: r0->field_1f = d0
    //     0x14cf03c: stur            d0, [x0, #0x1f]
    // 0x14cf040: ldur            x1, [fp, #-0x18]
    // 0x14cf044: LoadField: d0 = r1->field_7
    //     0x14cf044: ldur            d0, [x1, #7]
    // 0x14cf048: stur            d0, [fp, #-0x70]
    // 0x14cf04c: LoadField: d1 = r1->field_f
    //     0x14cf04c: ldur            d1, [x1, #0xf]
    // 0x14cf050: stur            d1, [fp, #-0x68]
    // 0x14cf054: d2 = 50.000000
    //     0x14cf054: ldr             d2, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0x14cf058: fsub            d3, d1, d2
    // 0x14cf05c: stur            d3, [fp, #-0x60]
    // 0x14cf060: fadd            d4, d0, d2
    // 0x14cf064: stur            d4, [fp, #-0x58]
    // 0x14cf068: r0 = RelativeRect()
    //     0x14cf068: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0x14cf06c: ldur            d0, [fp, #-0x70]
    // 0x14cf070: stur            x0, [fp, #-0x18]
    // 0x14cf074: StoreField: r0->field_7 = d0
    //     0x14cf074: stur            d0, [x0, #7]
    // 0x14cf078: ldur            d0, [fp, #-0x60]
    // 0x14cf07c: StoreField: r0->field_f = d0
    //     0x14cf07c: stur            d0, [x0, #0xf]
    // 0x14cf080: ldur            d0, [fp, #-0x58]
    // 0x14cf084: ArrayStore: r0[0] = d0  ; List_8
    //     0x14cf084: stur            d0, [x0, #0x17]
    // 0x14cf088: ldur            d0, [fp, #-0x68]
    // 0x14cf08c: StoreField: r0->field_1f = d0
    //     0x14cf08c: stur            d0, [x0, #0x1f]
    // 0x14cf090: ldur            x1, [fp, #-8]
    // 0x14cf094: r0 = controller()
    //     0x14cf094: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf098: LoadField: r1 = r0->field_93
    //     0x14cf098: ldur            w1, [x0, #0x93]
    // 0x14cf09c: DecompressPointer r1
    //     0x14cf09c: add             x1, x1, HEAP, lsl #32
    // 0x14cf0a0: ldur            x0, [fp, #-0x28]
    // 0x14cf0a4: LoadField: r2 = r0->field_13
    //     0x14cf0a4: ldur            w2, [x0, #0x13]
    // 0x14cf0a8: DecompressPointer r2
    //     0x14cf0a8: add             x2, x2, HEAP, lsl #32
    // 0x14cf0ac: r0 = []()
    //     0x14cf0ac: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x14cf0b0: r1 = 60
    //     0x14cf0b0: movz            x1, #0x3c
    // 0x14cf0b4: branchIfSmi(r0, 0x14cf0c0)
    //     0x14cf0b4: tbz             w0, #0, #0x14cf0c0
    // 0x14cf0b8: r1 = LoadClassIdInstr(r0)
    //     0x14cf0b8: ldur            x1, [x0, #-1]
    //     0x14cf0bc: ubfx            x1, x1, #0xc, #0x14
    // 0x14cf0c0: r16 = true
    //     0x14cf0c0: add             x16, NULL, #0x20  ; true
    // 0x14cf0c4: stp             x16, x0, [SP]
    // 0x14cf0c8: mov             x0, x1
    // 0x14cf0cc: mov             lr, x0
    // 0x14cf0d0: ldr             lr, [x21, lr, lsl #3]
    // 0x14cf0d4: blr             lr
    // 0x14cf0d8: tbnz            w0, #4, #0x14cf0e4
    // 0x14cf0dc: r3 = Null
    //     0x14cf0dc: mov             x3, NULL
    // 0x14cf0e0: b               #0x14cf0f8
    // 0x14cf0e4: ldur            x2, [fp, #-0x28]
    // 0x14cf0e8: r1 = Function '<anonymous closure>':.
    //     0x14cf0e8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41db0] AnonymousClosure: (0x9b3344), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem (0x9b2c74)
    //     0x14cf0ec: ldr             x1, [x1, #0xdb0]
    // 0x14cf0f0: r0 = AllocateClosure()
    //     0x14cf0f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cf0f4: mov             x3, x0
    // 0x14cf0f8: ldur            x0, [fp, #-0x28]
    // 0x14cf0fc: stur            x3, [fp, #-0x30]
    // 0x14cf100: r1 = <Widget>
    //     0x14cf100: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cf104: r2 = 0
    //     0x14cf104: movz            x2, #0
    // 0x14cf108: r0 = _GrowableList()
    //     0x14cf108: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x14cf10c: ldur            x1, [fp, #-8]
    // 0x14cf110: stur            x0, [fp, #-0x38]
    // 0x14cf114: r0 = controller()
    //     0x14cf114: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf118: LoadField: r1 = r0->field_93
    //     0x14cf118: ldur            w1, [x0, #0x93]
    // 0x14cf11c: DecompressPointer r1
    //     0x14cf11c: add             x1, x1, HEAP, lsl #32
    // 0x14cf120: ldur            x0, [fp, #-0x28]
    // 0x14cf124: LoadField: r2 = r0->field_13
    //     0x14cf124: ldur            w2, [x0, #0x13]
    // 0x14cf128: DecompressPointer r2
    //     0x14cf128: add             x2, x2, HEAP, lsl #32
    // 0x14cf12c: r0 = []()
    //     0x14cf12c: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x14cf130: r1 = 60
    //     0x14cf130: movz            x1, #0x3c
    // 0x14cf134: branchIfSmi(r0, 0x14cf140)
    //     0x14cf134: tbz             w0, #0, #0x14cf140
    // 0x14cf138: r1 = LoadClassIdInstr(r0)
    //     0x14cf138: ldur            x1, [x0, #-1]
    //     0x14cf13c: ubfx            x1, x1, #0xc, #0x14
    // 0x14cf140: r16 = true
    //     0x14cf140: add             x16, NULL, #0x20  ; true
    // 0x14cf144: stp             x16, x0, [SP]
    // 0x14cf148: mov             x0, x1
    // 0x14cf14c: mov             lr, x0
    // 0x14cf150: ldr             lr, [x21, lr, lsl #3]
    // 0x14cf154: blr             lr
    // 0x14cf158: tbnz            w0, #4, #0x14cf1bc
    // 0x14cf15c: ldur            x0, [fp, #-0x38]
    // 0x14cf160: LoadField: r1 = r0->field_b
    //     0x14cf160: ldur            w1, [x0, #0xb]
    // 0x14cf164: LoadField: r2 = r0->field_f
    //     0x14cf164: ldur            w2, [x0, #0xf]
    // 0x14cf168: DecompressPointer r2
    //     0x14cf168: add             x2, x2, HEAP, lsl #32
    // 0x14cf16c: LoadField: r3 = r2->field_b
    //     0x14cf16c: ldur            w3, [x2, #0xb]
    // 0x14cf170: r2 = LoadInt32Instr(r1)
    //     0x14cf170: sbfx            x2, x1, #1, #0x1f
    // 0x14cf174: stur            x2, [fp, #-0x40]
    // 0x14cf178: r1 = LoadInt32Instr(r3)
    //     0x14cf178: sbfx            x1, x3, #1, #0x1f
    // 0x14cf17c: cmp             x2, x1
    // 0x14cf180: b.ne            #0x14cf18c
    // 0x14cf184: mov             x1, x0
    // 0x14cf188: r0 = _growToNextCapacity()
    //     0x14cf188: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cf18c: ldur            x0, [fp, #-0x38]
    // 0x14cf190: ldur            x1, [fp, #-0x40]
    // 0x14cf194: add             x2, x1, #1
    // 0x14cf198: lsl             x3, x2, #1
    // 0x14cf19c: StoreField: r0->field_b = r3
    //     0x14cf19c: stur            w3, [x0, #0xb]
    // 0x14cf1a0: LoadField: r2 = r0->field_f
    //     0x14cf1a0: ldur            w2, [x0, #0xf]
    // 0x14cf1a4: DecompressPointer r2
    //     0x14cf1a4: add             x2, x2, HEAP, lsl #32
    // 0x14cf1a8: add             x3, x2, x1, lsl #2
    // 0x14cf1ac: r16 = Instance_Icon
    //     0x14cf1ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0x14cf1b0: ldr             x16, [x16, #0xa48]
    // 0x14cf1b4: StoreField: r3->field_f = r16
    //     0x14cf1b4: stur            w16, [x3, #0xf]
    // 0x14cf1b8: b               #0x14cf1c0
    // 0x14cf1bc: ldur            x0, [fp, #-0x38]
    // 0x14cf1c0: LoadField: r1 = r0->field_b
    //     0x14cf1c0: ldur            w1, [x0, #0xb]
    // 0x14cf1c4: LoadField: r2 = r0->field_f
    //     0x14cf1c4: ldur            w2, [x0, #0xf]
    // 0x14cf1c8: DecompressPointer r2
    //     0x14cf1c8: add             x2, x2, HEAP, lsl #32
    // 0x14cf1cc: LoadField: r3 = r2->field_b
    //     0x14cf1cc: ldur            w3, [x2, #0xb]
    // 0x14cf1d0: r2 = LoadInt32Instr(r1)
    //     0x14cf1d0: sbfx            x2, x1, #1, #0x1f
    // 0x14cf1d4: stur            x2, [fp, #-0x40]
    // 0x14cf1d8: r1 = LoadInt32Instr(r3)
    //     0x14cf1d8: sbfx            x1, x3, #1, #0x1f
    // 0x14cf1dc: cmp             x2, x1
    // 0x14cf1e0: b.ne            #0x14cf1ec
    // 0x14cf1e4: mov             x1, x0
    // 0x14cf1e8: r0 = _growToNextCapacity()
    //     0x14cf1e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cf1ec: ldur            x2, [fp, #-0x28]
    // 0x14cf1f0: ldur            x0, [fp, #-0x38]
    // 0x14cf1f4: ldur            x1, [fp, #-0x40]
    // 0x14cf1f8: add             x3, x1, #1
    // 0x14cf1fc: lsl             x4, x3, #1
    // 0x14cf200: StoreField: r0->field_b = r4
    //     0x14cf200: stur            w4, [x0, #0xb]
    // 0x14cf204: LoadField: r3 = r0->field_f
    //     0x14cf204: ldur            w3, [x0, #0xf]
    // 0x14cf208: DecompressPointer r3
    //     0x14cf208: add             x3, x3, HEAP, lsl #32
    // 0x14cf20c: add             x4, x3, x1, lsl #2
    // 0x14cf210: r16 = Instance_SizedBox
    //     0x14cf210: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x14cf214: ldr             x16, [x16, #0xa50]
    // 0x14cf218: StoreField: r4->field_f = r16
    //     0x14cf218: stur            w16, [x4, #0xf]
    // 0x14cf21c: ldur            x1, [fp, #-8]
    // 0x14cf220: r0 = controller()
    //     0x14cf220: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf224: LoadField: r1 = r0->field_93
    //     0x14cf224: ldur            w1, [x0, #0x93]
    // 0x14cf228: DecompressPointer r1
    //     0x14cf228: add             x1, x1, HEAP, lsl #32
    // 0x14cf22c: ldur            x0, [fp, #-0x28]
    // 0x14cf230: LoadField: r2 = r0->field_13
    //     0x14cf230: ldur            w2, [x0, #0x13]
    // 0x14cf234: DecompressPointer r2
    //     0x14cf234: add             x2, x2, HEAP, lsl #32
    // 0x14cf238: r0 = []()
    //     0x14cf238: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x14cf23c: r1 = 60
    //     0x14cf23c: movz            x1, #0x3c
    // 0x14cf240: branchIfSmi(r0, 0x14cf24c)
    //     0x14cf240: tbz             w0, #0, #0x14cf24c
    // 0x14cf244: r1 = LoadClassIdInstr(r0)
    //     0x14cf244: ldur            x1, [x0, #-1]
    //     0x14cf248: ubfx            x1, x1, #0xc, #0x14
    // 0x14cf24c: r16 = true
    //     0x14cf24c: add             x16, NULL, #0x20  ; true
    // 0x14cf250: stp             x16, x0, [SP]
    // 0x14cf254: mov             x0, x1
    // 0x14cf258: mov             lr, x0
    // 0x14cf25c: ldr             lr, [x21, lr, lsl #3]
    // 0x14cf260: blr             lr
    // 0x14cf264: tbnz            w0, #4, #0x14cf274
    // 0x14cf268: r0 = "Flagged"
    //     0x14cf268: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0x14cf26c: ldr             x0, [x0, #0xa58]
    // 0x14cf270: b               #0x14cf27c
    // 0x14cf274: r0 = "Flag as abusive"
    //     0x14cf274: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0x14cf278: ldr             x0, [x0, #0xa60]
    // 0x14cf27c: ldur            x1, [fp, #-0x10]
    // 0x14cf280: stur            x0, [fp, #-8]
    // 0x14cf284: r0 = of()
    //     0x14cf284: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14cf288: LoadField: r1 = r0->field_87
    //     0x14cf288: ldur            w1, [x0, #0x87]
    // 0x14cf28c: DecompressPointer r1
    //     0x14cf28c: add             x1, x1, HEAP, lsl #32
    // 0x14cf290: LoadField: r0 = r1->field_33
    //     0x14cf290: ldur            w0, [x1, #0x33]
    // 0x14cf294: DecompressPointer r0
    //     0x14cf294: add             x0, x0, HEAP, lsl #32
    // 0x14cf298: cmp             w0, NULL
    // 0x14cf29c: b.ne            #0x14cf2a8
    // 0x14cf2a0: r2 = Null
    //     0x14cf2a0: mov             x2, NULL
    // 0x14cf2a4: b               #0x14cf2cc
    // 0x14cf2a8: r16 = 12.000000
    //     0x14cf2a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14cf2ac: ldr             x16, [x16, #0x9e8]
    // 0x14cf2b0: r30 = Instance_Color
    //     0x14cf2b0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14cf2b4: stp             lr, x16, [SP]
    // 0x14cf2b8: mov             x1, x0
    // 0x14cf2bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14cf2bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14cf2c0: ldr             x4, [x4, #0xaa0]
    // 0x14cf2c4: r0 = copyWith()
    //     0x14cf2c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14cf2c8: mov             x2, x0
    // 0x14cf2cc: ldur            x1, [fp, #-0x38]
    // 0x14cf2d0: ldur            x0, [fp, #-8]
    // 0x14cf2d4: stur            x2, [fp, #-0x48]
    // 0x14cf2d8: r0 = Text()
    //     0x14cf2d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14cf2dc: mov             x2, x0
    // 0x14cf2e0: ldur            x0, [fp, #-8]
    // 0x14cf2e4: stur            x2, [fp, #-0x50]
    // 0x14cf2e8: StoreField: r2->field_b = r0
    //     0x14cf2e8: stur            w0, [x2, #0xb]
    // 0x14cf2ec: ldur            x0, [fp, #-0x48]
    // 0x14cf2f0: StoreField: r2->field_13 = r0
    //     0x14cf2f0: stur            w0, [x2, #0x13]
    // 0x14cf2f4: ldur            x0, [fp, #-0x38]
    // 0x14cf2f8: LoadField: r1 = r0->field_b
    //     0x14cf2f8: ldur            w1, [x0, #0xb]
    // 0x14cf2fc: LoadField: r3 = r0->field_f
    //     0x14cf2fc: ldur            w3, [x0, #0xf]
    // 0x14cf300: DecompressPointer r3
    //     0x14cf300: add             x3, x3, HEAP, lsl #32
    // 0x14cf304: LoadField: r4 = r3->field_b
    //     0x14cf304: ldur            w4, [x3, #0xb]
    // 0x14cf308: r3 = LoadInt32Instr(r1)
    //     0x14cf308: sbfx            x3, x1, #1, #0x1f
    // 0x14cf30c: stur            x3, [fp, #-0x40]
    // 0x14cf310: r1 = LoadInt32Instr(r4)
    //     0x14cf310: sbfx            x1, x4, #1, #0x1f
    // 0x14cf314: cmp             x3, x1
    // 0x14cf318: b.ne            #0x14cf324
    // 0x14cf31c: mov             x1, x0
    // 0x14cf320: r0 = _growToNextCapacity()
    //     0x14cf320: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14cf324: ldur            x4, [fp, #-0x30]
    // 0x14cf328: ldur            x2, [fp, #-0x38]
    // 0x14cf32c: ldur            x3, [fp, #-0x40]
    // 0x14cf330: add             x0, x3, #1
    // 0x14cf334: lsl             x1, x0, #1
    // 0x14cf338: StoreField: r2->field_b = r1
    //     0x14cf338: stur            w1, [x2, #0xb]
    // 0x14cf33c: LoadField: r1 = r2->field_f
    //     0x14cf33c: ldur            w1, [x2, #0xf]
    // 0x14cf340: DecompressPointer r1
    //     0x14cf340: add             x1, x1, HEAP, lsl #32
    // 0x14cf344: ldur            x0, [fp, #-0x50]
    // 0x14cf348: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14cf348: add             x25, x1, x3, lsl #2
    //     0x14cf34c: add             x25, x25, #0xf
    //     0x14cf350: str             w0, [x25]
    //     0x14cf354: tbz             w0, #0, #0x14cf370
    //     0x14cf358: ldurb           w16, [x1, #-1]
    //     0x14cf35c: ldurb           w17, [x0, #-1]
    //     0x14cf360: and             x16, x17, x16, lsr #2
    //     0x14cf364: tst             x16, HEAP, lsr #32
    //     0x14cf368: b.eq            #0x14cf370
    //     0x14cf36c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14cf370: r0 = Row()
    //     0x14cf370: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14cf374: mov             x2, x0
    // 0x14cf378: r0 = Instance_Axis
    //     0x14cf378: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14cf37c: stur            x2, [fp, #-8]
    // 0x14cf380: StoreField: r2->field_f = r0
    //     0x14cf380: stur            w0, [x2, #0xf]
    // 0x14cf384: r0 = Instance_MainAxisAlignment
    //     0x14cf384: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14cf388: ldr             x0, [x0, #0xa08]
    // 0x14cf38c: StoreField: r2->field_13 = r0
    //     0x14cf38c: stur            w0, [x2, #0x13]
    // 0x14cf390: r0 = Instance_MainAxisSize
    //     0x14cf390: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14cf394: ldr             x0, [x0, #0xa10]
    // 0x14cf398: ArrayStore: r2[0] = r0  ; List_4
    //     0x14cf398: stur            w0, [x2, #0x17]
    // 0x14cf39c: r0 = Instance_CrossAxisAlignment
    //     0x14cf39c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14cf3a0: ldr             x0, [x0, #0xa18]
    // 0x14cf3a4: StoreField: r2->field_1b = r0
    //     0x14cf3a4: stur            w0, [x2, #0x1b]
    // 0x14cf3a8: r0 = Instance_VerticalDirection
    //     0x14cf3a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14cf3ac: ldr             x0, [x0, #0xa20]
    // 0x14cf3b0: StoreField: r2->field_23 = r0
    //     0x14cf3b0: stur            w0, [x2, #0x23]
    // 0x14cf3b4: r0 = Instance_Clip
    //     0x14cf3b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14cf3b8: ldr             x0, [x0, #0x38]
    // 0x14cf3bc: StoreField: r2->field_2b = r0
    //     0x14cf3bc: stur            w0, [x2, #0x2b]
    // 0x14cf3c0: StoreField: r2->field_2f = rZR
    //     0x14cf3c0: stur            xzr, [x2, #0x2f]
    // 0x14cf3c4: ldur            x0, [fp, #-0x38]
    // 0x14cf3c8: StoreField: r2->field_b = r0
    //     0x14cf3c8: stur            w0, [x2, #0xb]
    // 0x14cf3cc: r1 = <String>
    //     0x14cf3cc: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14cf3d0: r0 = PopupMenuItem()
    //     0x14cf3d0: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0x14cf3d4: mov             x3, x0
    // 0x14cf3d8: r0 = "flag"
    //     0x14cf3d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0x14cf3dc: ldr             x0, [x0, #0xa68]
    // 0x14cf3e0: stur            x3, [fp, #-0x38]
    // 0x14cf3e4: StoreField: r3->field_f = r0
    //     0x14cf3e4: stur            w0, [x3, #0xf]
    // 0x14cf3e8: ldur            x0, [fp, #-0x30]
    // 0x14cf3ec: StoreField: r3->field_13 = r0
    //     0x14cf3ec: stur            w0, [x3, #0x13]
    // 0x14cf3f0: r0 = true
    //     0x14cf3f0: add             x0, NULL, #0x20  ; true
    // 0x14cf3f4: ArrayStore: r3[0] = r0  ; List_4
    //     0x14cf3f4: stur            w0, [x3, #0x17]
    // 0x14cf3f8: d0 = 25.000000
    //     0x14cf3f8: fmov            d0, #25.00000000
    // 0x14cf3fc: StoreField: r3->field_1b = d0
    //     0x14cf3fc: stur            d0, [x3, #0x1b]
    // 0x14cf400: ldur            x0, [fp, #-8]
    // 0x14cf404: StoreField: r3->field_33 = r0
    //     0x14cf404: stur            w0, [x3, #0x33]
    // 0x14cf408: r1 = Null
    //     0x14cf408: mov             x1, NULL
    // 0x14cf40c: r2 = 2
    //     0x14cf40c: movz            x2, #0x2
    // 0x14cf410: r0 = AllocateArray()
    //     0x14cf410: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cf414: mov             x2, x0
    // 0x14cf418: ldur            x0, [fp, #-0x38]
    // 0x14cf41c: stur            x2, [fp, #-8]
    // 0x14cf420: StoreField: r2->field_f = r0
    //     0x14cf420: stur            w0, [x2, #0xf]
    // 0x14cf424: r1 = <PopupMenuEntry<String>>
    //     0x14cf424: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0x14cf428: ldr             x1, [x1, #0xa70]
    // 0x14cf42c: r0 = AllocateGrowableArray()
    //     0x14cf42c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cf430: mov             x1, x0
    // 0x14cf434: ldur            x0, [fp, #-8]
    // 0x14cf438: StoreField: r1->field_f = r0
    //     0x14cf438: stur            w0, [x1, #0xf]
    // 0x14cf43c: r0 = 2
    //     0x14cf43c: movz            x0, #0x2
    // 0x14cf440: StoreField: r1->field_b = r0
    //     0x14cf440: stur            w0, [x1, #0xb]
    // 0x14cf444: r16 = <String>
    //     0x14cf444: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14cf448: ldur            lr, [fp, #-0x10]
    // 0x14cf44c: stp             lr, x16, [SP, #0x20]
    // 0x14cf450: ldur            x16, [fp, #-0x18]
    // 0x14cf454: stp             x16, x1, [SP, #0x10]
    // 0x14cf458: r16 = Instance_Color
    //     0x14cf458: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14cf45c: ldur            lr, [fp, #-0x20]
    // 0x14cf460: stp             lr, x16, [SP]
    // 0x14cf464: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0x14cf464: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0x14cf468: ldr             x4, [x4, #0xa78]
    // 0x14cf46c: r0 = showMenu()
    //     0x14cf46c: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0x14cf470: ldur            x2, [fp, #-0x28]
    // 0x14cf474: r1 = Function '<anonymous closure>':.
    //     0x14cf474: add             x1, PP, #0x41, lsl #12  ; [pp+0x41db8] AnonymousClosure: (0x9b31c0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem (0x9b2c74)
    //     0x14cf478: ldr             x1, [x1, #0xdb8]
    // 0x14cf47c: stur            x0, [fp, #-8]
    // 0x14cf480: r0 = AllocateClosure()
    //     0x14cf480: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cf484: r16 = <Null?>
    //     0x14cf484: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x14cf488: ldur            lr, [fp, #-8]
    // 0x14cf48c: stp             lr, x16, [SP, #8]
    // 0x14cf490: str             x0, [SP]
    // 0x14cf494: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cf494: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cf498: r0 = then()
    //     0x14cf498: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x14cf49c: r0 = Null
    //     0x14cf49c: mov             x0, NULL
    // 0x14cf4a0: LeaveFrame
    //     0x14cf4a0: mov             SP, fp
    //     0x14cf4a4: ldp             fp, lr, [SP], #0x10
    // 0x14cf4a8: ret
    //     0x14cf4a8: ret             
    // 0x14cf4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cf4ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cf4b0: b               #0x14cef98
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14cf4b4, size: 0x3cc
    // 0x14cf4b4: EnterFrame
    //     0x14cf4b4: stp             fp, lr, [SP, #-0x10]!
    //     0x14cf4b8: mov             fp, SP
    // 0x14cf4bc: AllocStack(0x60)
    //     0x14cf4bc: sub             SP, SP, #0x60
    // 0x14cf4c0: SetupParameters()
    //     0x14cf4c0: ldr             x0, [fp, #0x20]
    //     0x14cf4c4: ldur            w1, [x0, #0x17]
    //     0x14cf4c8: add             x1, x1, HEAP, lsl #32
    //     0x14cf4cc: stur            x1, [fp, #-8]
    // 0x14cf4d0: CheckStackOverflow
    //     0x14cf4d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cf4d4: cmp             SP, x16
    //     0x14cf4d8: b.ls            #0x14cf868
    // 0x14cf4dc: r1 = 3
    //     0x14cf4dc: movz            x1, #0x3
    // 0x14cf4e0: r0 = AllocateContext()
    //     0x14cf4e0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14cf4e4: mov             x2, x0
    // 0x14cf4e8: ldur            x0, [fp, #-8]
    // 0x14cf4ec: stur            x2, [fp, #-0x18]
    // 0x14cf4f0: StoreField: r2->field_b = r0
    //     0x14cf4f0: stur            w0, [x2, #0xb]
    // 0x14cf4f4: ldr             x1, [fp, #0x18]
    // 0x14cf4f8: StoreField: r2->field_f = r1
    //     0x14cf4f8: stur            w1, [x2, #0xf]
    // 0x14cf4fc: ldr             x1, [fp, #0x10]
    // 0x14cf500: StoreField: r2->field_13 = r1
    //     0x14cf500: stur            w1, [x2, #0x13]
    // 0x14cf504: LoadField: r3 = r0->field_b
    //     0x14cf504: ldur            w3, [x0, #0xb]
    // 0x14cf508: DecompressPointer r3
    //     0x14cf508: add             x3, x3, HEAP, lsl #32
    // 0x14cf50c: stur            x3, [fp, #-0x10]
    // 0x14cf510: LoadField: r1 = r3->field_f
    //     0x14cf510: ldur            w1, [x3, #0xf]
    // 0x14cf514: DecompressPointer r1
    //     0x14cf514: add             x1, x1, HEAP, lsl #32
    // 0x14cf518: r0 = controller()
    //     0x14cf518: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf51c: LoadField: r1 = r0->field_77
    //     0x14cf51c: ldur            w1, [x0, #0x77]
    // 0x14cf520: DecompressPointer r1
    //     0x14cf520: add             x1, x1, HEAP, lsl #32
    // 0x14cf524: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cf524: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cf528: r0 = toList()
    //     0x14cf528: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cf52c: mov             x3, x0
    // 0x14cf530: ldur            x2, [fp, #-8]
    // 0x14cf534: LoadField: r0 = r2->field_13
    //     0x14cf534: ldur            w0, [x2, #0x13]
    // 0x14cf538: DecompressPointer r0
    //     0x14cf538: add             x0, x0, HEAP, lsl #32
    // 0x14cf53c: LoadField: r1 = r3->field_b
    //     0x14cf53c: ldur            w1, [x3, #0xb]
    // 0x14cf540: r4 = LoadInt32Instr(r0)
    //     0x14cf540: sbfx            x4, x0, #1, #0x1f
    //     0x14cf544: tbz             w0, #0, #0x14cf54c
    //     0x14cf548: ldur            x4, [x0, #7]
    // 0x14cf54c: r0 = LoadInt32Instr(r1)
    //     0x14cf54c: sbfx            x0, x1, #1, #0x1f
    // 0x14cf550: mov             x1, x4
    // 0x14cf554: cmp             x1, x0
    // 0x14cf558: b.hs            #0x14cf870
    // 0x14cf55c: LoadField: r0 = r3->field_f
    //     0x14cf55c: ldur            w0, [x3, #0xf]
    // 0x14cf560: DecompressPointer r0
    //     0x14cf560: add             x0, x0, HEAP, lsl #32
    // 0x14cf564: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cf564: add             x16, x0, x4, lsl #2
    //     0x14cf568: ldur            w1, [x16, #0xf]
    // 0x14cf56c: DecompressPointer r1
    //     0x14cf56c: add             x1, x1, HEAP, lsl #32
    // 0x14cf570: cmp             w1, NULL
    // 0x14cf574: b.ne            #0x14cf584
    // 0x14cf578: ldur            x3, [fp, #-0x18]
    // 0x14cf57c: r4 = Null
    //     0x14cf57c: mov             x4, NULL
    // 0x14cf580: b               #0x14cf5d8
    // 0x14cf584: ldur            x3, [fp, #-0x18]
    // 0x14cf588: LoadField: r4 = r1->field_1b
    //     0x14cf588: ldur            w4, [x1, #0x1b]
    // 0x14cf58c: DecompressPointer r4
    //     0x14cf58c: add             x4, x4, HEAP, lsl #32
    // 0x14cf590: LoadField: r0 = r3->field_13
    //     0x14cf590: ldur            w0, [x3, #0x13]
    // 0x14cf594: DecompressPointer r0
    //     0x14cf594: add             x0, x0, HEAP, lsl #32
    // 0x14cf598: LoadField: r1 = r4->field_b
    //     0x14cf598: ldur            w1, [x4, #0xb]
    // 0x14cf59c: r5 = LoadInt32Instr(r0)
    //     0x14cf59c: sbfx            x5, x0, #1, #0x1f
    //     0x14cf5a0: tbz             w0, #0, #0x14cf5a8
    //     0x14cf5a4: ldur            x5, [x0, #7]
    // 0x14cf5a8: r0 = LoadInt32Instr(r1)
    //     0x14cf5a8: sbfx            x0, x1, #1, #0x1f
    // 0x14cf5ac: mov             x1, x5
    // 0x14cf5b0: cmp             x1, x0
    // 0x14cf5b4: b.hs            #0x14cf874
    // 0x14cf5b8: LoadField: r0 = r4->field_f
    //     0x14cf5b8: ldur            w0, [x4, #0xf]
    // 0x14cf5bc: DecompressPointer r0
    //     0x14cf5bc: add             x0, x0, HEAP, lsl #32
    // 0x14cf5c0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x14cf5c0: add             x16, x0, x5, lsl #2
    //     0x14cf5c4: ldur            w1, [x16, #0xf]
    // 0x14cf5c8: DecompressPointer r1
    //     0x14cf5c8: add             x1, x1, HEAP, lsl #32
    // 0x14cf5cc: LoadField: r0 = r1->field_13
    //     0x14cf5cc: ldur            w0, [x1, #0x13]
    // 0x14cf5d0: DecompressPointer r0
    //     0x14cf5d0: add             x0, x0, HEAP, lsl #32
    // 0x14cf5d4: mov             x4, x0
    // 0x14cf5d8: ldur            x1, [fp, #-0x10]
    // 0x14cf5dc: mov             x0, x4
    // 0x14cf5e0: stur            x4, [fp, #-0x20]
    // 0x14cf5e4: ArrayStore: r3[0] = r0  ; List_4
    //     0x14cf5e4: stur            w0, [x3, #0x17]
    //     0x14cf5e8: ldurb           w16, [x3, #-1]
    //     0x14cf5ec: ldurb           w17, [x0, #-1]
    //     0x14cf5f0: and             x16, x17, x16, lsr #2
    //     0x14cf5f4: tst             x16, HEAP, lsr #32
    //     0x14cf5f8: b.eq            #0x14cf600
    //     0x14cf5fc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14cf600: LoadField: r0 = r1->field_f
    //     0x14cf600: ldur            w0, [x1, #0xf]
    // 0x14cf604: DecompressPointer r0
    //     0x14cf604: add             x0, x0, HEAP, lsl #32
    // 0x14cf608: mov             x1, x0
    // 0x14cf60c: r0 = controller()
    //     0x14cf60c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf610: LoadField: r1 = r0->field_77
    //     0x14cf610: ldur            w1, [x0, #0x77]
    // 0x14cf614: DecompressPointer r1
    //     0x14cf614: add             x1, x1, HEAP, lsl #32
    // 0x14cf618: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cf618: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cf61c: r0 = toList()
    //     0x14cf61c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cf620: mov             x2, x0
    // 0x14cf624: ldur            x0, [fp, #-8]
    // 0x14cf628: LoadField: r1 = r0->field_13
    //     0x14cf628: ldur            w1, [x0, #0x13]
    // 0x14cf62c: DecompressPointer r1
    //     0x14cf62c: add             x1, x1, HEAP, lsl #32
    // 0x14cf630: LoadField: r0 = r2->field_b
    //     0x14cf630: ldur            w0, [x2, #0xb]
    // 0x14cf634: r3 = LoadInt32Instr(r1)
    //     0x14cf634: sbfx            x3, x1, #1, #0x1f
    //     0x14cf638: tbz             w1, #0, #0x14cf640
    //     0x14cf63c: ldur            x3, [x1, #7]
    // 0x14cf640: r1 = LoadInt32Instr(r0)
    //     0x14cf640: sbfx            x1, x0, #1, #0x1f
    // 0x14cf644: mov             x0, x1
    // 0x14cf648: mov             x1, x3
    // 0x14cf64c: cmp             x1, x0
    // 0x14cf650: b.hs            #0x14cf878
    // 0x14cf654: LoadField: r0 = r2->field_f
    //     0x14cf654: ldur            w0, [x2, #0xf]
    // 0x14cf658: DecompressPointer r0
    //     0x14cf658: add             x0, x0, HEAP, lsl #32
    // 0x14cf65c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14cf65c: add             x16, x0, x3, lsl #2
    //     0x14cf660: ldur            w1, [x16, #0xf]
    // 0x14cf664: DecompressPointer r1
    //     0x14cf664: add             x1, x1, HEAP, lsl #32
    // 0x14cf668: cmp             w1, NULL
    // 0x14cf66c: b.ne            #0x14cf67c
    // 0x14cf670: ldur            x2, [fp, #-0x18]
    // 0x14cf674: r0 = Null
    //     0x14cf674: mov             x0, NULL
    // 0x14cf678: b               #0x14cf6cc
    // 0x14cf67c: ldur            x2, [fp, #-0x18]
    // 0x14cf680: LoadField: r3 = r1->field_1b
    //     0x14cf680: ldur            w3, [x1, #0x1b]
    // 0x14cf684: DecompressPointer r3
    //     0x14cf684: add             x3, x3, HEAP, lsl #32
    // 0x14cf688: LoadField: r0 = r2->field_13
    //     0x14cf688: ldur            w0, [x2, #0x13]
    // 0x14cf68c: DecompressPointer r0
    //     0x14cf68c: add             x0, x0, HEAP, lsl #32
    // 0x14cf690: LoadField: r1 = r3->field_b
    //     0x14cf690: ldur            w1, [x3, #0xb]
    // 0x14cf694: r4 = LoadInt32Instr(r0)
    //     0x14cf694: sbfx            x4, x0, #1, #0x1f
    //     0x14cf698: tbz             w0, #0, #0x14cf6a0
    //     0x14cf69c: ldur            x4, [x0, #7]
    // 0x14cf6a0: r0 = LoadInt32Instr(r1)
    //     0x14cf6a0: sbfx            x0, x1, #1, #0x1f
    // 0x14cf6a4: mov             x1, x4
    // 0x14cf6a8: cmp             x1, x0
    // 0x14cf6ac: b.hs            #0x14cf87c
    // 0x14cf6b0: LoadField: r0 = r3->field_f
    //     0x14cf6b0: ldur            w0, [x3, #0xf]
    // 0x14cf6b4: DecompressPointer r0
    //     0x14cf6b4: add             x0, x0, HEAP, lsl #32
    // 0x14cf6b8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cf6b8: add             x16, x0, x4, lsl #2
    //     0x14cf6bc: ldur            w1, [x16, #0xf]
    // 0x14cf6c0: DecompressPointer r1
    //     0x14cf6c0: add             x1, x1, HEAP, lsl #32
    // 0x14cf6c4: LoadField: r0 = r1->field_f
    //     0x14cf6c4: ldur            w0, [x1, #0xf]
    // 0x14cf6c8: DecompressPointer r0
    //     0x14cf6c8: add             x0, x0, HEAP, lsl #32
    // 0x14cf6cc: r1 = LoadClassIdInstr(r0)
    //     0x14cf6cc: ldur            x1, [x0, #-1]
    //     0x14cf6d0: ubfx            x1, x1, #0xc, #0x14
    // 0x14cf6d4: r16 = "image"
    //     0x14cf6d4: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x14cf6d8: stp             x16, x0, [SP]
    // 0x14cf6dc: mov             x0, x1
    // 0x14cf6e0: mov             lr, x0
    // 0x14cf6e4: ldr             lr, [x21, lr, lsl #3]
    // 0x14cf6e8: blr             lr
    // 0x14cf6ec: tbnz            w0, #4, #0x14cf7d8
    // 0x14cf6f0: ldur            x0, [fp, #-0x20]
    // 0x14cf6f4: r0 = Radius()
    //     0x14cf6f4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14cf6f8: d0 = 12.000000
    //     0x14cf6f8: fmov            d0, #12.00000000
    // 0x14cf6fc: stur            x0, [fp, #-8]
    // 0x14cf700: StoreField: r0->field_7 = d0
    //     0x14cf700: stur            d0, [x0, #7]
    // 0x14cf704: StoreField: r0->field_f = d0
    //     0x14cf704: stur            d0, [x0, #0xf]
    // 0x14cf708: r0 = BorderRadius()
    //     0x14cf708: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14cf70c: mov             x3, x0
    // 0x14cf710: ldur            x0, [fp, #-8]
    // 0x14cf714: stur            x3, [fp, #-0x10]
    // 0x14cf718: StoreField: r3->field_7 = r0
    //     0x14cf718: stur            w0, [x3, #7]
    // 0x14cf71c: StoreField: r3->field_b = r0
    //     0x14cf71c: stur            w0, [x3, #0xb]
    // 0x14cf720: StoreField: r3->field_f = r0
    //     0x14cf720: stur            w0, [x3, #0xf]
    // 0x14cf724: StoreField: r3->field_13 = r0
    //     0x14cf724: stur            w0, [x3, #0x13]
    // 0x14cf728: ldur            x0, [fp, #-0x20]
    // 0x14cf72c: cmp             w0, NULL
    // 0x14cf730: b.ne            #0x14cf738
    // 0x14cf734: r0 = ""
    //     0x14cf734: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cf738: stur            x0, [fp, #-8]
    // 0x14cf73c: r1 = Function '<anonymous closure>':.
    //     0x14cf73c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41dc0] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cf740: ldr             x1, [x1, #0xdc0]
    // 0x14cf744: r2 = Null
    //     0x14cf744: mov             x2, NULL
    // 0x14cf748: r0 = AllocateClosure()
    //     0x14cf748: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cf74c: r1 = Function '<anonymous closure>':.
    //     0x14cf74c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41dc8] AnonymousClosure: (0x9b17ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cf750: ldr             x1, [x1, #0xdc8]
    // 0x14cf754: r2 = Null
    //     0x14cf754: mov             x2, NULL
    // 0x14cf758: stur            x0, [fp, #-0x28]
    // 0x14cf75c: r0 = AllocateClosure()
    //     0x14cf75c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cf760: stur            x0, [fp, #-0x30]
    // 0x14cf764: r0 = CachedNetworkImage()
    //     0x14cf764: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14cf768: stur            x0, [fp, #-0x38]
    // 0x14cf76c: r16 = Instance_BoxFit
    //     0x14cf76c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14cf770: ldr             x16, [x16, #0x118]
    // 0x14cf774: r30 = 48.000000
    //     0x14cf774: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14cf778: ldr             lr, [lr, #0xad8]
    // 0x14cf77c: stp             lr, x16, [SP, #0x18]
    // 0x14cf780: r16 = 48.000000
    //     0x14cf780: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14cf784: ldr             x16, [x16, #0xad8]
    // 0x14cf788: ldur            lr, [fp, #-0x28]
    // 0x14cf78c: stp             lr, x16, [SP, #8]
    // 0x14cf790: ldur            x16, [fp, #-0x30]
    // 0x14cf794: str             x16, [SP]
    // 0x14cf798: mov             x1, x0
    // 0x14cf79c: ldur            x2, [fp, #-8]
    // 0x14cf7a0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14cf7a0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14cf7a4: ldr             x4, [x4, #0xae0]
    // 0x14cf7a8: r0 = CachedNetworkImage()
    //     0x14cf7a8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14cf7ac: r0 = ClipRRect()
    //     0x14cf7ac: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14cf7b0: mov             x1, x0
    // 0x14cf7b4: ldur            x0, [fp, #-0x10]
    // 0x14cf7b8: StoreField: r1->field_f = r0
    //     0x14cf7b8: stur            w0, [x1, #0xf]
    // 0x14cf7bc: r0 = Instance_Clip
    //     0x14cf7bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14cf7c0: ldr             x0, [x0, #0x138]
    // 0x14cf7c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x14cf7c4: stur            w0, [x1, #0x17]
    // 0x14cf7c8: ldur            x0, [fp, #-0x38]
    // 0x14cf7cc: StoreField: r1->field_b = r0
    //     0x14cf7cc: stur            w0, [x1, #0xb]
    // 0x14cf7d0: mov             x0, x1
    // 0x14cf7d4: b               #0x14cf824
    // 0x14cf7d8: ldur            x0, [fp, #-0x20]
    // 0x14cf7dc: cmp             w0, NULL
    // 0x14cf7e0: b.ne            #0x14cf7e8
    // 0x14cf7e4: r0 = ""
    //     0x14cf7e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cf7e8: stur            x0, [fp, #-8]
    // 0x14cf7ec: r0 = VideoPlayerWidget()
    //     0x14cf7ec: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x14cf7f0: mov             x1, x0
    // 0x14cf7f4: ldur            x0, [fp, #-8]
    // 0x14cf7f8: stur            x1, [fp, #-0x10]
    // 0x14cf7fc: StoreField: r1->field_b = r0
    //     0x14cf7fc: stur            w0, [x1, #0xb]
    // 0x14cf800: r0 = SizedBox()
    //     0x14cf800: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14cf804: mov             x1, x0
    // 0x14cf808: r0 = 48.000000
    //     0x14cf808: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14cf80c: ldr             x0, [x0, #0xad8]
    // 0x14cf810: StoreField: r1->field_f = r0
    //     0x14cf810: stur            w0, [x1, #0xf]
    // 0x14cf814: StoreField: r1->field_13 = r0
    //     0x14cf814: stur            w0, [x1, #0x13]
    // 0x14cf818: ldur            x0, [fp, #-0x10]
    // 0x14cf81c: StoreField: r1->field_b = r0
    //     0x14cf81c: stur            w0, [x1, #0xb]
    // 0x14cf820: mov             x0, x1
    // 0x14cf824: stur            x0, [fp, #-8]
    // 0x14cf828: r0 = GestureDetector()
    //     0x14cf828: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14cf82c: ldur            x2, [fp, #-0x18]
    // 0x14cf830: r1 = Function '<anonymous closure>':.
    //     0x14cf830: add             x1, PP, #0x41, lsl #12  ; [pp+0x41dd0] AnonymousClosure: (0x14cf880), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14cf834: ldr             x1, [x1, #0xdd0]
    // 0x14cf838: stur            x0, [fp, #-0x10]
    // 0x14cf83c: r0 = AllocateClosure()
    //     0x14cf83c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cf840: ldur            x16, [fp, #-8]
    // 0x14cf844: stp             x16, x0, [SP]
    // 0x14cf848: ldur            x1, [fp, #-0x10]
    // 0x14cf84c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x14cf84c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x14cf850: ldr             x4, [x4, #0xaf0]
    // 0x14cf854: r0 = GestureDetector()
    //     0x14cf854: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14cf858: ldur            x0, [fp, #-0x10]
    // 0x14cf85c: LeaveFrame
    //     0x14cf85c: mov             SP, fp
    //     0x14cf860: ldp             fp, lr, [SP], #0x10
    // 0x14cf864: ret
    //     0x14cf864: ret             
    // 0x14cf868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cf868: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cf86c: b               #0x14cf4dc
    // 0x14cf870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cf870: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cf874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cf874: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cf878: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cf878: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14cf87c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cf87c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14cf880, size: 0xf4
    // 0x14cf880: EnterFrame
    //     0x14cf880: stp             fp, lr, [SP, #-0x10]!
    //     0x14cf884: mov             fp, SP
    // 0x14cf888: AllocStack(0x28)
    //     0x14cf888: sub             SP, SP, #0x28
    // 0x14cf88c: SetupParameters()
    //     0x14cf88c: ldr             x0, [fp, #0x10]
    //     0x14cf890: ldur            w2, [x0, #0x17]
    //     0x14cf894: add             x2, x2, HEAP, lsl #32
    //     0x14cf898: stur            x2, [fp, #-8]
    // 0x14cf89c: CheckStackOverflow
    //     0x14cf89c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cf8a0: cmp             SP, x16
    //     0x14cf8a4: b.ls            #0x14cf96c
    // 0x14cf8a8: LoadField: r0 = r2->field_b
    //     0x14cf8a8: ldur            w0, [x2, #0xb]
    // 0x14cf8ac: DecompressPointer r0
    //     0x14cf8ac: add             x0, x0, HEAP, lsl #32
    // 0x14cf8b0: LoadField: r1 = r0->field_b
    //     0x14cf8b0: ldur            w1, [x0, #0xb]
    // 0x14cf8b4: DecompressPointer r1
    //     0x14cf8b4: add             x1, x1, HEAP, lsl #32
    // 0x14cf8b8: LoadField: r0 = r1->field_f
    //     0x14cf8b8: ldur            w0, [x1, #0xf]
    // 0x14cf8bc: DecompressPointer r0
    //     0x14cf8bc: add             x0, x0, HEAP, lsl #32
    // 0x14cf8c0: mov             x1, x0
    // 0x14cf8c4: r0 = controller()
    //     0x14cf8c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf8c8: mov             x1, x0
    // 0x14cf8cc: ldur            x0, [fp, #-8]
    // 0x14cf8d0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14cf8d0: ldur            w2, [x0, #0x17]
    // 0x14cf8d4: DecompressPointer r2
    //     0x14cf8d4: add             x2, x2, HEAP, lsl #32
    // 0x14cf8d8: cmp             w2, NULL
    // 0x14cf8dc: b.ne            #0x14cf8e4
    // 0x14cf8e0: r2 = ""
    //     0x14cf8e0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cf8e4: str             x2, [SP]
    // 0x14cf8e8: r2 = "single_media"
    //     0x14cf8e8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0x14cf8ec: ldr             x2, [x2, #0xab0]
    // 0x14cf8f0: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0x14cf8f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0x14cf8f4: ldr             x4, [x4, #0xaf8]
    // 0x14cf8f8: r0 = ratingReviewClickedEvent()
    //     0x14cf8f8: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x14cf8fc: ldur            x2, [fp, #-8]
    // 0x14cf900: LoadField: r1 = r2->field_f
    //     0x14cf900: ldur            w1, [x2, #0xf]
    // 0x14cf904: DecompressPointer r1
    //     0x14cf904: add             x1, x1, HEAP, lsl #32
    // 0x14cf908: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cf908: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cf90c: r0 = of()
    //     0x14cf90c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x14cf910: ldur            x2, [fp, #-8]
    // 0x14cf914: r1 = Function '<anonymous closure>':.
    //     0x14cf914: add             x1, PP, #0x41, lsl #12  ; [pp+0x41dd8] AnonymousClosure: (0x14cf974), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14cf918: ldr             x1, [x1, #0xdd8]
    // 0x14cf91c: stur            x0, [fp, #-8]
    // 0x14cf920: r0 = AllocateClosure()
    //     0x14cf920: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cf924: r1 = Null
    //     0x14cf924: mov             x1, NULL
    // 0x14cf928: stur            x0, [fp, #-0x10]
    // 0x14cf92c: r0 = MaterialPageRoute()
    //     0x14cf92c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x14cf930: mov             x1, x0
    // 0x14cf934: ldur            x2, [fp, #-0x10]
    // 0x14cf938: stur            x0, [fp, #-0x10]
    // 0x14cf93c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14cf93c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14cf940: r0 = MaterialPageRoute()
    //     0x14cf940: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x14cf944: ldur            x16, [fp, #-8]
    // 0x14cf948: stp             x16, NULL, [SP, #8]
    // 0x14cf94c: ldur            x16, [fp, #-0x10]
    // 0x14cf950: str             x16, [SP]
    // 0x14cf954: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cf954: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cf958: r0 = push()
    //     0x14cf958: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x14cf95c: r0 = Null
    //     0x14cf95c: mov             x0, NULL
    // 0x14cf960: LeaveFrame
    //     0x14cf960: mov             SP, fp
    //     0x14cf964: ldp             fp, lr, [SP], #0x10
    // 0x14cf968: ret
    //     0x14cf968: ret             
    // 0x14cf96c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cf96c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cf970: b               #0x14cf8a8
  }
  [closure] RatingReviewOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14cf974, size: 0x13c
    // 0x14cf974: EnterFrame
    //     0x14cf974: stp             fp, lr, [SP, #-0x10]!
    //     0x14cf978: mov             fp, SP
    // 0x14cf97c: AllocStack(0x28)
    //     0x14cf97c: sub             SP, SP, #0x28
    // 0x14cf980: SetupParameters()
    //     0x14cf980: ldr             x0, [fp, #0x18]
    //     0x14cf984: ldur            w2, [x0, #0x17]
    //     0x14cf988: add             x2, x2, HEAP, lsl #32
    //     0x14cf98c: stur            x2, [fp, #-0x18]
    // 0x14cf990: CheckStackOverflow
    //     0x14cf990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cf994: cmp             SP, x16
    //     0x14cf998: b.ls            #0x14cfaa4
    // 0x14cf99c: LoadField: r0 = r2->field_b
    //     0x14cf99c: ldur            w0, [x2, #0xb]
    // 0x14cf9a0: DecompressPointer r0
    //     0x14cf9a0: add             x0, x0, HEAP, lsl #32
    // 0x14cf9a4: stur            x0, [fp, #-0x10]
    // 0x14cf9a8: LoadField: r3 = r0->field_b
    //     0x14cf9a8: ldur            w3, [x0, #0xb]
    // 0x14cf9ac: DecompressPointer r3
    //     0x14cf9ac: add             x3, x3, HEAP, lsl #32
    // 0x14cf9b0: stur            x3, [fp, #-8]
    // 0x14cf9b4: LoadField: r1 = r3->field_f
    //     0x14cf9b4: ldur            w1, [x3, #0xf]
    // 0x14cf9b8: DecompressPointer r1
    //     0x14cf9b8: add             x1, x1, HEAP, lsl #32
    // 0x14cf9bc: r0 = controller()
    //     0x14cf9bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cf9c0: LoadField: r1 = r0->field_77
    //     0x14cf9c0: ldur            w1, [x0, #0x77]
    // 0x14cf9c4: DecompressPointer r1
    //     0x14cf9c4: add             x1, x1, HEAP, lsl #32
    // 0x14cf9c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cf9c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cf9cc: r0 = toList()
    //     0x14cf9cc: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14cf9d0: mov             x2, x0
    // 0x14cf9d4: ldur            x0, [fp, #-0x10]
    // 0x14cf9d8: LoadField: r1 = r0->field_13
    //     0x14cf9d8: ldur            w1, [x0, #0x13]
    // 0x14cf9dc: DecompressPointer r1
    //     0x14cf9dc: add             x1, x1, HEAP, lsl #32
    // 0x14cf9e0: LoadField: r0 = r2->field_b
    //     0x14cf9e0: ldur            w0, [x2, #0xb]
    // 0x14cf9e4: r3 = LoadInt32Instr(r1)
    //     0x14cf9e4: sbfx            x3, x1, #1, #0x1f
    //     0x14cf9e8: tbz             w1, #0, #0x14cf9f0
    //     0x14cf9ec: ldur            x3, [x1, #7]
    // 0x14cf9f0: r1 = LoadInt32Instr(r0)
    //     0x14cf9f0: sbfx            x1, x0, #1, #0x1f
    // 0x14cf9f4: mov             x0, x1
    // 0x14cf9f8: mov             x1, x3
    // 0x14cf9fc: cmp             x1, x0
    // 0x14cfa00: b.hs            #0x14cfaac
    // 0x14cfa04: LoadField: r0 = r2->field_f
    //     0x14cfa04: ldur            w0, [x2, #0xf]
    // 0x14cfa08: DecompressPointer r0
    //     0x14cfa08: add             x0, x0, HEAP, lsl #32
    // 0x14cfa0c: ArrayLoad: r2 = r0[r3]  ; Unknown_4
    //     0x14cfa0c: add             x16, x0, x3, lsl #2
    //     0x14cfa10: ldur            w2, [x16, #0xf]
    // 0x14cfa14: DecompressPointer r2
    //     0x14cfa14: add             x2, x2, HEAP, lsl #32
    // 0x14cfa18: ldur            x0, [fp, #-0x18]
    // 0x14cfa1c: stur            x2, [fp, #-0x20]
    // 0x14cfa20: LoadField: r3 = r0->field_13
    //     0x14cfa20: ldur            w3, [x0, #0x13]
    // 0x14cfa24: DecompressPointer r3
    //     0x14cfa24: add             x3, x3, HEAP, lsl #32
    // 0x14cfa28: ldur            x1, [fp, #-8]
    // 0x14cfa2c: stur            x3, [fp, #-0x10]
    // 0x14cfa30: LoadField: r4 = r1->field_f
    //     0x14cfa30: ldur            w4, [x1, #0xf]
    // 0x14cfa34: DecompressPointer r4
    //     0x14cfa34: add             x4, x4, HEAP, lsl #32
    // 0x14cfa38: mov             x1, x4
    // 0x14cfa3c: r0 = controller()
    //     0x14cfa3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfa40: LoadField: r1 = r0->field_97
    //     0x14cfa40: ldur            w1, [x0, #0x97]
    // 0x14cfa44: DecompressPointer r1
    //     0x14cfa44: add             x1, x1, HEAP, lsl #32
    // 0x14cfa48: stur            x1, [fp, #-8]
    // 0x14cfa4c: r0 = RatingReviewOnTapImage()
    //     0x14cfa4c: bl              #0xb18498  ; AllocateRatingReviewOnTapImageStub -> RatingReviewOnTapImage (size=0x20)
    // 0x14cfa50: mov             x3, x0
    // 0x14cfa54: ldur            x0, [fp, #-0x20]
    // 0x14cfa58: stur            x3, [fp, #-0x28]
    // 0x14cfa5c: StoreField: r3->field_b = r0
    //     0x14cfa5c: stur            w0, [x3, #0xb]
    // 0x14cfa60: ldur            x0, [fp, #-0x10]
    // 0x14cfa64: r1 = LoadInt32Instr(r0)
    //     0x14cfa64: sbfx            x1, x0, #1, #0x1f
    //     0x14cfa68: tbz             w0, #0, #0x14cfa70
    //     0x14cfa6c: ldur            x1, [x0, #7]
    // 0x14cfa70: StoreField: r3->field_f = r1
    //     0x14cfa70: stur            x1, [x3, #0xf]
    // 0x14cfa74: ldur            x2, [fp, #-0x18]
    // 0x14cfa78: r1 = Function '<anonymous closure>':.
    //     0x14cfa78: add             x1, PP, #0x41, lsl #12  ; [pp+0x41de0] AnonymousClosure: (0x9b170c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cfa7c: ldr             x1, [x1, #0xde0]
    // 0x14cfa80: r0 = AllocateClosure()
    //     0x14cfa80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cfa84: mov             x1, x0
    // 0x14cfa88: ldur            x0, [fp, #-0x28]
    // 0x14cfa8c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14cfa8c: stur            w1, [x0, #0x17]
    // 0x14cfa90: ldur            x1, [fp, #-8]
    // 0x14cfa94: StoreField: r0->field_1b = r1
    //     0x14cfa94: stur            w1, [x0, #0x1b]
    // 0x14cfa98: LeaveFrame
    //     0x14cfa98: mov             SP, fp
    //     0x14cfa9c: ldp             fp, lr, [SP], #0x10
    // 0x14cfaa0: ret
    //     0x14cfaa0: ret             
    // 0x14cfaa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cfaa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cfaa8: b               #0x14cf99c
    // 0x14cfaac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14cfaac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14cfab0, size: 0x24c
    // 0x14cfab0: EnterFrame
    //     0x14cfab0: stp             fp, lr, [SP, #-0x10]!
    //     0x14cfab4: mov             fp, SP
    // 0x14cfab8: AllocStack(0x20)
    //     0x14cfab8: sub             SP, SP, #0x20
    // 0x14cfabc: SetupParameters()
    //     0x14cfabc: ldr             x0, [fp, #0x18]
    //     0x14cfac0: ldur            w1, [x0, #0x17]
    //     0x14cfac4: add             x1, x1, HEAP, lsl #32
    //     0x14cfac8: stur            x1, [fp, #-8]
    // 0x14cfacc: CheckStackOverflow
    //     0x14cfacc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cfad0: cmp             SP, x16
    //     0x14cfad4: b.ls            #0x14cfcf4
    // 0x14cfad8: r1 = 1
    //     0x14cfad8: movz            x1, #0x1
    // 0x14cfadc: r0 = AllocateContext()
    //     0x14cfadc: bl              #0x16f6108  ; AllocateContextStub
    // 0x14cfae0: mov             x2, x0
    // 0x14cfae4: ldur            x0, [fp, #-8]
    // 0x14cfae8: stur            x2, [fp, #-0x10]
    // 0x14cfaec: StoreField: r2->field_b = r0
    //     0x14cfaec: stur            w0, [x2, #0xb]
    // 0x14cfaf0: ldr             x1, [fp, #0x10]
    // 0x14cfaf4: StoreField: r2->field_f = r1
    //     0x14cfaf4: stur            w1, [x2, #0xf]
    // 0x14cfaf8: LoadField: r1 = r0->field_f
    //     0x14cfaf8: ldur            w1, [x0, #0xf]
    // 0x14cfafc: DecompressPointer r1
    //     0x14cfafc: add             x1, x1, HEAP, lsl #32
    // 0x14cfb00: r0 = controller()
    //     0x14cfb00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfb04: LoadField: r1 = r0->field_7b
    //     0x14cfb04: ldur            w1, [x0, #0x7b]
    // 0x14cfb08: DecompressPointer r1
    //     0x14cfb08: add             x1, x1, HEAP, lsl #32
    // 0x14cfb0c: r0 = value()
    //     0x14cfb0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cfb10: LoadField: r1 = r0->field_b
    //     0x14cfb10: ldur            w1, [x0, #0xb]
    // 0x14cfb14: DecompressPointer r1
    //     0x14cfb14: add             x1, x1, HEAP, lsl #32
    // 0x14cfb18: cmp             w1, NULL
    // 0x14cfb1c: b.ne            #0x14cfb28
    // 0x14cfb20: r2 = Null
    //     0x14cfb20: mov             x2, NULL
    // 0x14cfb24: b               #0x14cfb74
    // 0x14cfb28: LoadField: r0 = r1->field_b
    //     0x14cfb28: ldur            w0, [x1, #0xb]
    // 0x14cfb2c: DecompressPointer r0
    //     0x14cfb2c: add             x0, x0, HEAP, lsl #32
    // 0x14cfb30: ldur            x2, [fp, #-0x10]
    // 0x14cfb34: stur            x0, [fp, #-0x18]
    // 0x14cfb38: r1 = Function '<anonymous closure>':.
    //     0x14cfb38: add             x1, PP, #0x41, lsl #12  ; [pp+0x41de8] AnonymousClosure: (0x9ba5e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cfb3c: ldr             x1, [x1, #0xde8]
    // 0x14cfb40: r0 = AllocateClosure()
    //     0x14cfb40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cfb44: r1 = Function '<anonymous closure>':.
    //     0x14cfb44: add             x1, PP, #0x41, lsl #12  ; [pp+0x41df0] AnonymousClosure: (0x9ba5c8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cfb48: ldr             x1, [x1, #0xdf0]
    // 0x14cfb4c: r2 = Null
    //     0x14cfb4c: mov             x2, NULL
    // 0x14cfb50: stur            x0, [fp, #-0x10]
    // 0x14cfb54: r0 = AllocateClosure()
    //     0x14cfb54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cfb58: str             x0, [SP]
    // 0x14cfb5c: ldur            x1, [fp, #-0x18]
    // 0x14cfb60: ldur            x2, [fp, #-0x10]
    // 0x14cfb64: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x14cfb64: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x14cfb68: ldr             x4, [x4, #0xb48]
    // 0x14cfb6c: r0 = firstWhere()
    //     0x14cfb6c: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x14cfb70: mov             x2, x0
    // 0x14cfb74: ldur            x0, [fp, #-8]
    // 0x14cfb78: stur            x2, [fp, #-0x10]
    // 0x14cfb7c: LoadField: r1 = r0->field_f
    //     0x14cfb7c: ldur            w1, [x0, #0xf]
    // 0x14cfb80: DecompressPointer r1
    //     0x14cfb80: add             x1, x1, HEAP, lsl #32
    // 0x14cfb84: r0 = controller()
    //     0x14cfb84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfb88: LoadField: r3 = r0->field_83
    //     0x14cfb88: ldur            w3, [x0, #0x83]
    // 0x14cfb8c: DecompressPointer r3
    //     0x14cfb8c: add             x3, x3, HEAP, lsl #32
    // 0x14cfb90: ldur            x0, [fp, #-0x10]
    // 0x14cfb94: stur            x3, [fp, #-0x18]
    // 0x14cfb98: r2 = Null
    //     0x14cfb98: mov             x2, NULL
    // 0x14cfb9c: r1 = Null
    //     0x14cfb9c: mov             x1, NULL
    // 0x14cfba0: r4 = LoadClassIdInstr(r0)
    //     0x14cfba0: ldur            x4, [x0, #-1]
    //     0x14cfba4: ubfx            x4, x4, #0xc, #0x14
    // 0x14cfba8: r17 = 5457
    //     0x14cfba8: movz            x17, #0x1551
    // 0x14cfbac: cmp             x4, x17
    // 0x14cfbb0: b.eq            #0x14cfbc8
    // 0x14cfbb4: r8 = Filter
    //     0x14cfbb4: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fb58] Type: Filter
    //     0x14cfbb8: ldr             x8, [x8, #0xb58]
    // 0x14cfbbc: r3 = Null
    //     0x14cfbbc: add             x3, PP, #0x41, lsl #12  ; [pp+0x41df8] Null
    //     0x14cfbc0: ldr             x3, [x3, #0xdf8]
    // 0x14cfbc4: r0 = Filter()
    //     0x14cfbc4: bl              #0x9b76bc  ; IsType_Filter_Stub
    // 0x14cfbc8: ldur            x1, [fp, #-0x18]
    // 0x14cfbcc: ldur            x2, [fp, #-0x10]
    // 0x14cfbd0: r0 = value=()
    //     0x14cfbd0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14cfbd4: ldur            x0, [fp, #-8]
    // 0x14cfbd8: LoadField: r1 = r0->field_f
    //     0x14cfbd8: ldur            w1, [x0, #0xf]
    // 0x14cfbdc: DecompressPointer r1
    //     0x14cfbdc: add             x1, x1, HEAP, lsl #32
    // 0x14cfbe0: r0 = controller()
    //     0x14cfbe0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfbe4: LoadField: r1 = r0->field_6b
    //     0x14cfbe4: ldur            w1, [x0, #0x6b]
    // 0x14cfbe8: DecompressPointer r1
    //     0x14cfbe8: add             x1, x1, HEAP, lsl #32
    // 0x14cfbec: r0 = initRefresh()
    //     0x14cfbec: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x14cfbf0: ldur            x0, [fp, #-8]
    // 0x14cfbf4: LoadField: r1 = r0->field_f
    //     0x14cfbf4: ldur            w1, [x0, #0xf]
    // 0x14cfbf8: DecompressPointer r1
    //     0x14cfbf8: add             x1, x1, HEAP, lsl #32
    // 0x14cfbfc: r0 = controller()
    //     0x14cfbfc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfc00: LoadField: r1 = r0->field_67
    //     0x14cfc00: ldur            w1, [x0, #0x67]
    // 0x14cfc04: DecompressPointer r1
    //     0x14cfc04: add             x1, x1, HEAP, lsl #32
    // 0x14cfc08: ldur            x0, [fp, #-0x10]
    // 0x14cfc0c: LoadField: r2 = r0->field_7
    //     0x14cfc0c: ldur            w2, [x0, #7]
    // 0x14cfc10: DecompressPointer r2
    //     0x14cfc10: add             x2, x2, HEAP, lsl #32
    // 0x14cfc14: cmp             w2, NULL
    // 0x14cfc18: b.ne            #0x14cfc20
    // 0x14cfc1c: r2 = ""
    //     0x14cfc1c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cfc20: ldur            x3, [fp, #-8]
    // 0x14cfc24: r0 = value=()
    //     0x14cfc24: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14cfc28: ldur            x0, [fp, #-8]
    // 0x14cfc2c: LoadField: r1 = r0->field_f
    //     0x14cfc2c: ldur            w1, [x0, #0xf]
    // 0x14cfc30: DecompressPointer r1
    //     0x14cfc30: add             x1, x1, HEAP, lsl #32
    // 0x14cfc34: r0 = controller()
    //     0x14cfc34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfc38: mov             x2, x0
    // 0x14cfc3c: ldur            x0, [fp, #-0x10]
    // 0x14cfc40: stur            x2, [fp, #-0x18]
    // 0x14cfc44: LoadField: r1 = r0->field_7
    //     0x14cfc44: ldur            w1, [x0, #7]
    // 0x14cfc48: DecompressPointer r1
    //     0x14cfc48: add             x1, x1, HEAP, lsl #32
    // 0x14cfc4c: cmp             w1, NULL
    // 0x14cfc50: b.ne            #0x14cfc5c
    // 0x14cfc54: r3 = ""
    //     0x14cfc54: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14cfc58: b               #0x14cfc60
    // 0x14cfc5c: mov             x3, x1
    // 0x14cfc60: ldur            x0, [fp, #-8]
    // 0x14cfc64: stur            x3, [fp, #-0x10]
    // 0x14cfc68: LoadField: r1 = r0->field_f
    //     0x14cfc68: ldur            w1, [x0, #0xf]
    // 0x14cfc6c: DecompressPointer r1
    //     0x14cfc6c: add             x1, x1, HEAP, lsl #32
    // 0x14cfc70: r0 = controller()
    //     0x14cfc70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfc74: LoadField: r2 = r0->field_53
    //     0x14cfc74: ldur            w2, [x0, #0x53]
    // 0x14cfc78: DecompressPointer r2
    //     0x14cfc78: add             x2, x2, HEAP, lsl #32
    // 0x14cfc7c: ldur            x16, [fp, #-0x10]
    // 0x14cfc80: str             x16, [SP]
    // 0x14cfc84: ldur            x1, [fp, #-0x18]
    // 0x14cfc88: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0x14cfc88: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0x14cfc8c: ldr             x4, [x4, #0x220]
    // 0x14cfc90: r0 = getViewAllReviews()
    //     0x14cfc90: bl              #0x9b692c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::getViewAllReviews
    // 0x14cfc94: ldur            x0, [fp, #-8]
    // 0x14cfc98: LoadField: r1 = r0->field_f
    //     0x14cfc98: ldur            w1, [x0, #0xf]
    // 0x14cfc9c: DecompressPointer r1
    //     0x14cfc9c: add             x1, x1, HEAP, lsl #32
    // 0x14cfca0: r0 = controller()
    //     0x14cfca0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfca4: mov             x2, x0
    // 0x14cfca8: ldur            x0, [fp, #-8]
    // 0x14cfcac: stur            x2, [fp, #-0x10]
    // 0x14cfcb0: LoadField: r1 = r0->field_f
    //     0x14cfcb0: ldur            w1, [x0, #0xf]
    // 0x14cfcb4: DecompressPointer r1
    //     0x14cfcb4: add             x1, x1, HEAP, lsl #32
    // 0x14cfcb8: r0 = controller()
    //     0x14cfcb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfcbc: LoadField: r1 = r0->field_67
    //     0x14cfcbc: ldur            w1, [x0, #0x67]
    // 0x14cfcc0: DecompressPointer r1
    //     0x14cfcc0: add             x1, x1, HEAP, lsl #32
    // 0x14cfcc4: r0 = value()
    //     0x14cfcc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cfcc8: str             x0, [SP]
    // 0x14cfccc: ldur            x1, [fp, #-0x10]
    // 0x14cfcd0: r2 = "sort_selected"
    //     0x14cfcd0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fb50] "sort_selected"
    //     0x14cfcd4: ldr             x2, [x2, #0xb50]
    // 0x14cfcd8: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0x14cfcd8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0x14cfcdc: ldr             x4, [x4, #0xaf8]
    // 0x14cfce0: r0 = ratingReviewClickedEvent()
    //     0x14cfce0: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x14cfce4: r0 = Null
    //     0x14cfce4: mov             x0, NULL
    // 0x14cfce8: LeaveFrame
    //     0x14cfce8: mov             SP, fp
    //     0x14cfcec: ldp             fp, lr, [SP], #0x10
    // 0x14cfcf0: ret
    //     0x14cfcf0: ret             
    // 0x14cfcf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cfcf4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cfcf8: b               #0x14cfad8
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14cfcfc, size: 0x78c
    // 0x14cfcfc: EnterFrame
    //     0x14cfcfc: stp             fp, lr, [SP, #-0x10]!
    //     0x14cfd00: mov             fp, SP
    // 0x14cfd04: AllocStack(0x68)
    //     0x14cfd04: sub             SP, SP, #0x68
    // 0x14cfd08: SetupParameters()
    //     0x14cfd08: ldr             x0, [fp, #0x20]
    //     0x14cfd0c: ldur            w1, [x0, #0x17]
    //     0x14cfd10: add             x1, x1, HEAP, lsl #32
    //     0x14cfd14: stur            x1, [fp, #-8]
    // 0x14cfd18: CheckStackOverflow
    //     0x14cfd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cfd1c: cmp             SP, x16
    //     0x14cfd20: b.ls            #0x14d047c
    // 0x14cfd24: r1 = 3
    //     0x14cfd24: movz            x1, #0x3
    // 0x14cfd28: r0 = AllocateContext()
    //     0x14cfd28: bl              #0x16f6108  ; AllocateContextStub
    // 0x14cfd2c: mov             x2, x0
    // 0x14cfd30: ldur            x0, [fp, #-8]
    // 0x14cfd34: stur            x2, [fp, #-0x10]
    // 0x14cfd38: StoreField: r2->field_b = r0
    //     0x14cfd38: stur            w0, [x2, #0xb]
    // 0x14cfd3c: ldr             x3, [fp, #0x18]
    // 0x14cfd40: StoreField: r2->field_f = r3
    //     0x14cfd40: stur            w3, [x2, #0xf]
    // 0x14cfd44: ldr             x4, [fp, #0x10]
    // 0x14cfd48: StoreField: r2->field_13 = r4
    //     0x14cfd48: stur            w4, [x2, #0x13]
    // 0x14cfd4c: LoadField: r1 = r0->field_f
    //     0x14cfd4c: ldur            w1, [x0, #0xf]
    // 0x14cfd50: DecompressPointer r1
    //     0x14cfd50: add             x1, x1, HEAP, lsl #32
    // 0x14cfd54: r0 = controller()
    //     0x14cfd54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cfd58: LoadField: r1 = r0->field_7f
    //     0x14cfd58: ldur            w1, [x0, #0x7f]
    // 0x14cfd5c: DecompressPointer r1
    //     0x14cfd5c: add             x1, x1, HEAP, lsl #32
    // 0x14cfd60: r0 = value()
    //     0x14cfd60: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14cfd64: LoadField: r3 = r0->field_f
    //     0x14cfd64: ldur            w3, [x0, #0xf]
    // 0x14cfd68: DecompressPointer r3
    //     0x14cfd68: add             x3, x3, HEAP, lsl #32
    // 0x14cfd6c: stur            x3, [fp, #-0x18]
    // 0x14cfd70: cmp             w3, NULL
    // 0x14cfd74: b.ne            #0x14cfd80
    // 0x14cfd78: r2 = Null
    //     0x14cfd78: mov             x2, NULL
    // 0x14cfd7c: b               #0x14cfdb4
    // 0x14cfd80: r1 = Function '<anonymous closure>':.
    //     0x14cfd80: add             x1, PP, #0x41, lsl #12  ; [pp+0x41e08] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14cfd84: ldr             x1, [x1, #0xe08]
    // 0x14cfd88: r2 = Null
    //     0x14cfd88: mov             x2, NULL
    // 0x14cfd8c: r0 = AllocateClosure()
    //     0x14cfd8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cfd90: ldur            x16, [fp, #-0x18]
    // 0x14cfd94: stp             x16, NULL, [SP, #8]
    // 0x14cfd98: str             x0, [SP]
    // 0x14cfd9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14cfd9c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14cfda0: r0 = expand()
    //     0x14cfda0: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14cfda4: mov             x1, x0
    // 0x14cfda8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14cfda8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14cfdac: r0 = toList()
    //     0x14cfdac: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x14cfdb0: mov             x2, x0
    // 0x14cfdb4: cmp             w2, NULL
    // 0x14cfdb8: b.ne            #0x14cfdc8
    // 0x14cfdbc: ldr             x3, [fp, #0x10]
    // 0x14cfdc0: r1 = Null
    //     0x14cfdc0: mov             x1, NULL
    // 0x14cfdc4: b               #0x14cfe04
    // 0x14cfdc8: ldr             x3, [fp, #0x10]
    // 0x14cfdcc: LoadField: r0 = r2->field_b
    //     0x14cfdcc: ldur            w0, [x2, #0xb]
    // 0x14cfdd0: r4 = LoadInt32Instr(r3)
    //     0x14cfdd0: sbfx            x4, x3, #1, #0x1f
    //     0x14cfdd4: tbz             w3, #0, #0x14cfddc
    //     0x14cfdd8: ldur            x4, [x3, #7]
    // 0x14cfddc: r1 = LoadInt32Instr(r0)
    //     0x14cfddc: sbfx            x1, x0, #1, #0x1f
    // 0x14cfde0: mov             x0, x1
    // 0x14cfde4: mov             x1, x4
    // 0x14cfde8: cmp             x1, x0
    // 0x14cfdec: b.hs            #0x14d0484
    // 0x14cfdf0: LoadField: r0 = r2->field_f
    //     0x14cfdf0: ldur            w0, [x2, #0xf]
    // 0x14cfdf4: DecompressPointer r0
    //     0x14cfdf4: add             x0, x0, HEAP, lsl #32
    // 0x14cfdf8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14cfdf8: add             x16, x0, x4, lsl #2
    //     0x14cfdfc: ldur            w1, [x16, #0xf]
    // 0x14cfe00: DecompressPointer r1
    //     0x14cfe00: add             x1, x1, HEAP, lsl #32
    // 0x14cfe04: ldur            x2, [fp, #-0x10]
    // 0x14cfe08: mov             x0, x1
    // 0x14cfe0c: stur            x1, [fp, #-0x18]
    // 0x14cfe10: ArrayStore: r2[0] = r0  ; List_4
    //     0x14cfe10: stur            w0, [x2, #0x17]
    //     0x14cfe14: tbz             w0, #0, #0x14cfe30
    //     0x14cfe18: ldurb           w16, [x2, #-1]
    //     0x14cfe1c: ldurb           w17, [x0, #-1]
    //     0x14cfe20: and             x16, x17, x16, lsr #2
    //     0x14cfe24: tst             x16, HEAP, lsr #32
    //     0x14cfe28: b.eq            #0x14cfe30
    //     0x14cfe2c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x14cfe30: r0 = LoadInt32Instr(r3)
    //     0x14cfe30: sbfx            x0, x3, #1, #0x1f
    //     0x14cfe34: tbz             w3, #0, #0x14cfe3c
    //     0x14cfe38: ldur            x0, [x3, #7]
    // 0x14cfe3c: cmp             x0, #4
    // 0x14cfe40: b.ge            #0x14d0098
    // 0x14cfe44: r0 = Radius()
    //     0x14cfe44: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14cfe48: d0 = 12.000000
    //     0x14cfe48: fmov            d0, #12.00000000
    // 0x14cfe4c: stur            x0, [fp, #-0x20]
    // 0x14cfe50: StoreField: r0->field_7 = d0
    //     0x14cfe50: stur            d0, [x0, #7]
    // 0x14cfe54: StoreField: r0->field_f = d0
    //     0x14cfe54: stur            d0, [x0, #0xf]
    // 0x14cfe58: r0 = BorderRadius()
    //     0x14cfe58: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14cfe5c: mov             x1, x0
    // 0x14cfe60: ldur            x0, [fp, #-0x20]
    // 0x14cfe64: stur            x1, [fp, #-0x28]
    // 0x14cfe68: StoreField: r1->field_7 = r0
    //     0x14cfe68: stur            w0, [x1, #7]
    // 0x14cfe6c: StoreField: r1->field_b = r0
    //     0x14cfe6c: stur            w0, [x1, #0xb]
    // 0x14cfe70: StoreField: r1->field_f = r0
    //     0x14cfe70: stur            w0, [x1, #0xf]
    // 0x14cfe74: StoreField: r1->field_13 = r0
    //     0x14cfe74: stur            w0, [x1, #0x13]
    // 0x14cfe78: ldur            x16, [fp, #-0x18]
    // 0x14cfe7c: str             x16, [SP]
    // 0x14cfe80: r4 = 0
    //     0x14cfe80: movz            x4, #0
    // 0x14cfe84: ldr             x0, [SP]
    // 0x14cfe88: r16 = UnlinkedCall_0x613b5c
    //     0x14cfe88: add             x16, PP, #0x41, lsl #12  ; [pp+0x41e10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14cfe8c: add             x16, x16, #0xe10
    // 0x14cfe90: ldp             x5, lr, [x16]
    // 0x14cfe94: blr             lr
    // 0x14cfe98: r1 = 60
    //     0x14cfe98: movz            x1, #0x3c
    // 0x14cfe9c: branchIfSmi(r0, 0x14cfea8)
    //     0x14cfe9c: tbz             w0, #0, #0x14cfea8
    // 0x14cfea0: r1 = LoadClassIdInstr(r0)
    //     0x14cfea0: ldur            x1, [x0, #-1]
    //     0x14cfea4: ubfx            x1, x1, #0xc, #0x14
    // 0x14cfea8: r16 = "image"
    //     0x14cfea8: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x14cfeac: stp             x16, x0, [SP]
    // 0x14cfeb0: mov             x0, x1
    // 0x14cfeb4: mov             lr, x0
    // 0x14cfeb8: ldr             lr, [x21, lr, lsl #3]
    // 0x14cfebc: blr             lr
    // 0x14cfec0: tbnz            w0, #4, #0x14cff98
    // 0x14cfec4: ldur            x16, [fp, #-0x18]
    // 0x14cfec8: str             x16, [SP]
    // 0x14cfecc: r4 = 0
    //     0x14cfecc: movz            x4, #0
    // 0x14cfed0: ldr             x0, [SP]
    // 0x14cfed4: r16 = UnlinkedCall_0x613b5c
    //     0x14cfed4: add             x16, PP, #0x41, lsl #12  ; [pp+0x41e20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14cfed8: add             x16, x16, #0xe20
    // 0x14cfedc: ldp             x5, lr, [x16]
    // 0x14cfee0: blr             lr
    // 0x14cfee4: mov             x3, x0
    // 0x14cfee8: r2 = Null
    //     0x14cfee8: mov             x2, NULL
    // 0x14cfeec: r1 = Null
    //     0x14cfeec: mov             x1, NULL
    // 0x14cfef0: stur            x3, [fp, #-0x20]
    // 0x14cfef4: r4 = 60
    //     0x14cfef4: movz            x4, #0x3c
    // 0x14cfef8: branchIfSmi(r0, 0x14cff04)
    //     0x14cfef8: tbz             w0, #0, #0x14cff04
    // 0x14cfefc: r4 = LoadClassIdInstr(r0)
    //     0x14cfefc: ldur            x4, [x0, #-1]
    //     0x14cff00: ubfx            x4, x4, #0xc, #0x14
    // 0x14cff04: sub             x4, x4, #0x5e
    // 0x14cff08: cmp             x4, #1
    // 0x14cff0c: b.ls            #0x14cff20
    // 0x14cff10: r8 = String
    //     0x14cff10: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14cff14: r3 = Null
    //     0x14cff14: add             x3, PP, #0x41, lsl #12  ; [pp+0x41e30] Null
    //     0x14cff18: ldr             x3, [x3, #0xe30]
    // 0x14cff1c: r0 = String()
    //     0x14cff1c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14cff20: r1 = Function '<anonymous closure>':.
    //     0x14cff20: add             x1, PP, #0x41, lsl #12  ; [pp+0x41e40] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14cff24: ldr             x1, [x1, #0xe40]
    // 0x14cff28: r2 = Null
    //     0x14cff28: mov             x2, NULL
    // 0x14cff2c: r0 = AllocateClosure()
    //     0x14cff2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cff30: r1 = Function '<anonymous closure>':.
    //     0x14cff30: add             x1, PP, #0x41, lsl #12  ; [pp+0x41e48] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14cff34: ldr             x1, [x1, #0xe48]
    // 0x14cff38: r2 = Null
    //     0x14cff38: mov             x2, NULL
    // 0x14cff3c: stur            x0, [fp, #-0x30]
    // 0x14cff40: r0 = AllocateClosure()
    //     0x14cff40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cff44: stur            x0, [fp, #-0x38]
    // 0x14cff48: r0 = CachedNetworkImage()
    //     0x14cff48: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14cff4c: stur            x0, [fp, #-0x40]
    // 0x14cff50: r16 = 60.000000
    //     0x14cff50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14cff54: ldr             x16, [x16, #0x110]
    // 0x14cff58: r30 = 62.000000
    //     0x14cff58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0x14cff5c: ldr             lr, [lr, #0xbc0]
    // 0x14cff60: stp             lr, x16, [SP, #0x18]
    // 0x14cff64: r16 = Instance_BoxFit
    //     0x14cff64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14cff68: ldr             x16, [x16, #0x118]
    // 0x14cff6c: ldur            lr, [fp, #-0x30]
    // 0x14cff70: stp             lr, x16, [SP, #8]
    // 0x14cff74: ldur            x16, [fp, #-0x38]
    // 0x14cff78: str             x16, [SP]
    // 0x14cff7c: mov             x1, x0
    // 0x14cff80: ldur            x2, [fp, #-0x20]
    // 0x14cff84: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14cff84: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14cff88: ldr             x4, [x4, #0xc28]
    // 0x14cff8c: r0 = CachedNetworkImage()
    //     0x14cff8c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14cff90: ldur            x1, [fp, #-0x40]
    // 0x14cff94: b               #0x14d0004
    // 0x14cff98: ldur            x16, [fp, #-0x18]
    // 0x14cff9c: str             x16, [SP]
    // 0x14cffa0: r4 = 0
    //     0x14cffa0: movz            x4, #0
    // 0x14cffa4: ldr             x0, [SP]
    // 0x14cffa8: r16 = UnlinkedCall_0x613b5c
    //     0x14cffa8: add             x16, PP, #0x41, lsl #12  ; [pp+0x41e50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14cffac: add             x16, x16, #0xe50
    // 0x14cffb0: ldp             x5, lr, [x16]
    // 0x14cffb4: blr             lr
    // 0x14cffb8: mov             x3, x0
    // 0x14cffbc: r2 = Null
    //     0x14cffbc: mov             x2, NULL
    // 0x14cffc0: r1 = Null
    //     0x14cffc0: mov             x1, NULL
    // 0x14cffc4: stur            x3, [fp, #-0x20]
    // 0x14cffc8: r4 = 60
    //     0x14cffc8: movz            x4, #0x3c
    // 0x14cffcc: branchIfSmi(r0, 0x14cffd8)
    //     0x14cffcc: tbz             w0, #0, #0x14cffd8
    // 0x14cffd0: r4 = LoadClassIdInstr(r0)
    //     0x14cffd0: ldur            x4, [x0, #-1]
    //     0x14cffd4: ubfx            x4, x4, #0xc, #0x14
    // 0x14cffd8: sub             x4, x4, #0x5e
    // 0x14cffdc: cmp             x4, #1
    // 0x14cffe0: b.ls            #0x14cfff4
    // 0x14cffe4: r8 = String
    //     0x14cffe4: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14cffe8: r3 = Null
    //     0x14cffe8: add             x3, PP, #0x41, lsl #12  ; [pp+0x41e60] Null
    //     0x14cffec: ldr             x3, [x3, #0xe60]
    // 0x14cfff0: r0 = String()
    //     0x14cfff0: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14cfff4: r0 = VideoPlayerWidget()
    //     0x14cfff4: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x14cfff8: mov             x1, x0
    // 0x14cfffc: ldur            x0, [fp, #-0x20]
    // 0x14d0000: StoreField: r1->field_b = r0
    //     0x14d0000: stur            w0, [x1, #0xb]
    // 0x14d0004: ldur            x0, [fp, #-0x28]
    // 0x14d0008: stur            x1, [fp, #-0x20]
    // 0x14d000c: r0 = InkWell()
    //     0x14d000c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d0010: mov             x3, x0
    // 0x14d0014: ldur            x0, [fp, #-0x20]
    // 0x14d0018: stur            x3, [fp, #-0x30]
    // 0x14d001c: StoreField: r3->field_b = r0
    //     0x14d001c: stur            w0, [x3, #0xb]
    // 0x14d0020: ldur            x2, [fp, #-0x10]
    // 0x14d0024: r1 = Function '<anonymous closure>':.
    //     0x14d0024: add             x1, PP, #0x41, lsl #12  ; [pp+0x41e70] AnonymousClosure: (0x14d0488), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14d0028: ldr             x1, [x1, #0xe70]
    // 0x14d002c: r0 = AllocateClosure()
    //     0x14d002c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0030: mov             x1, x0
    // 0x14d0034: ldur            x0, [fp, #-0x30]
    // 0x14d0038: StoreField: r0->field_f = r1
    //     0x14d0038: stur            w1, [x0, #0xf]
    // 0x14d003c: r1 = true
    //     0x14d003c: add             x1, NULL, #0x20  ; true
    // 0x14d0040: StoreField: r0->field_43 = r1
    //     0x14d0040: stur            w1, [x0, #0x43]
    // 0x14d0044: r2 = Instance_BoxShape
    //     0x14d0044: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0048: ldr             x2, [x2, #0x80]
    // 0x14d004c: StoreField: r0->field_47 = r2
    //     0x14d004c: stur            w2, [x0, #0x47]
    // 0x14d0050: StoreField: r0->field_6f = r1
    //     0x14d0050: stur            w1, [x0, #0x6f]
    // 0x14d0054: r3 = false
    //     0x14d0054: add             x3, NULL, #0x30  ; false
    // 0x14d0058: StoreField: r0->field_73 = r3
    //     0x14d0058: stur            w3, [x0, #0x73]
    // 0x14d005c: StoreField: r0->field_83 = r1
    //     0x14d005c: stur            w1, [x0, #0x83]
    // 0x14d0060: StoreField: r0->field_7b = r3
    //     0x14d0060: stur            w3, [x0, #0x7b]
    // 0x14d0064: r0 = ClipRRect()
    //     0x14d0064: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14d0068: mov             x1, x0
    // 0x14d006c: ldur            x0, [fp, #-0x28]
    // 0x14d0070: StoreField: r1->field_f = r0
    //     0x14d0070: stur            w0, [x1, #0xf]
    // 0x14d0074: r0 = Instance_Clip
    //     0x14d0074: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14d0078: ldr             x0, [x0, #0x138]
    // 0x14d007c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d007c: stur            w0, [x1, #0x17]
    // 0x14d0080: ldur            x0, [fp, #-0x30]
    // 0x14d0084: StoreField: r1->field_b = r0
    //     0x14d0084: stur            w0, [x1, #0xb]
    // 0x14d0088: mov             x0, x1
    // 0x14d008c: LeaveFrame
    //     0x14d008c: mov             SP, fp
    //     0x14d0090: ldp             fp, lr, [SP], #0x10
    // 0x14d0094: ret
    //     0x14d0094: ret             
    // 0x14d0098: ldur            x4, [fp, #-8]
    // 0x14d009c: r1 = true
    //     0x14d009c: add             x1, NULL, #0x20  ; true
    // 0x14d00a0: r3 = false
    //     0x14d00a0: add             x3, NULL, #0x30  ; false
    // 0x14d00a4: r2 = Instance_BoxShape
    //     0x14d00a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d00a8: ldr             x2, [x2, #0x80]
    // 0x14d00ac: r0 = Instance_Clip
    //     0x14d00ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14d00b0: ldr             x0, [x0, #0x138]
    // 0x14d00b4: d0 = 12.000000
    //     0x14d00b4: fmov            d0, #12.00000000
    // 0x14d00b8: r0 = Radius()
    //     0x14d00b8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14d00bc: d0 = 12.000000
    //     0x14d00bc: fmov            d0, #12.00000000
    // 0x14d00c0: stur            x0, [fp, #-0x20]
    // 0x14d00c4: StoreField: r0->field_7 = d0
    //     0x14d00c4: stur            d0, [x0, #7]
    // 0x14d00c8: StoreField: r0->field_f = d0
    //     0x14d00c8: stur            d0, [x0, #0xf]
    // 0x14d00cc: r0 = BorderRadius()
    //     0x14d00cc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14d00d0: mov             x1, x0
    // 0x14d00d4: ldur            x0, [fp, #-0x20]
    // 0x14d00d8: stur            x1, [fp, #-0x28]
    // 0x14d00dc: StoreField: r1->field_7 = r0
    //     0x14d00dc: stur            w0, [x1, #7]
    // 0x14d00e0: StoreField: r1->field_b = r0
    //     0x14d00e0: stur            w0, [x1, #0xb]
    // 0x14d00e4: StoreField: r1->field_f = r0
    //     0x14d00e4: stur            w0, [x1, #0xf]
    // 0x14d00e8: StoreField: r1->field_13 = r0
    //     0x14d00e8: stur            w0, [x1, #0x13]
    // 0x14d00ec: ldur            x16, [fp, #-0x18]
    // 0x14d00f0: str             x16, [SP]
    // 0x14d00f4: r4 = 0
    //     0x14d00f4: movz            x4, #0
    // 0x14d00f8: ldr             x0, [SP]
    // 0x14d00fc: r16 = UnlinkedCall_0x613b5c
    //     0x14d00fc: add             x16, PP, #0x41, lsl #12  ; [pp+0x41e78] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d0100: add             x16, x16, #0xe78
    // 0x14d0104: ldp             x5, lr, [x16]
    // 0x14d0108: blr             lr
    // 0x14d010c: mov             x3, x0
    // 0x14d0110: r2 = Null
    //     0x14d0110: mov             x2, NULL
    // 0x14d0114: r1 = Null
    //     0x14d0114: mov             x1, NULL
    // 0x14d0118: stur            x3, [fp, #-0x18]
    // 0x14d011c: r4 = 60
    //     0x14d011c: movz            x4, #0x3c
    // 0x14d0120: branchIfSmi(r0, 0x14d012c)
    //     0x14d0120: tbz             w0, #0, #0x14d012c
    // 0x14d0124: r4 = LoadClassIdInstr(r0)
    //     0x14d0124: ldur            x4, [x0, #-1]
    //     0x14d0128: ubfx            x4, x4, #0xc, #0x14
    // 0x14d012c: sub             x4, x4, #0x5e
    // 0x14d0130: cmp             x4, #1
    // 0x14d0134: b.ls            #0x14d0148
    // 0x14d0138: r8 = String
    //     0x14d0138: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14d013c: r3 = Null
    //     0x14d013c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41e88] Null
    //     0x14d0140: ldr             x3, [x3, #0xe88]
    // 0x14d0144: r0 = String()
    //     0x14d0144: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14d0148: r1 = Function '<anonymous closure>':.
    //     0x14d0148: add             x1, PP, #0x41, lsl #12  ; [pp+0x41e98] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14d014c: ldr             x1, [x1, #0xe98]
    // 0x14d0150: r2 = Null
    //     0x14d0150: mov             x2, NULL
    // 0x14d0154: r0 = AllocateClosure()
    //     0x14d0154: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0158: r1 = Function '<anonymous closure>':.
    //     0x14d0158: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ea0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14d015c: ldr             x1, [x1, #0xea0]
    // 0x14d0160: r2 = Null
    //     0x14d0160: mov             x2, NULL
    // 0x14d0164: stur            x0, [fp, #-0x20]
    // 0x14d0168: r0 = AllocateClosure()
    //     0x14d0168: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d016c: stur            x0, [fp, #-0x30]
    // 0x14d0170: r0 = CachedNetworkImage()
    //     0x14d0170: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14d0174: stur            x0, [fp, #-0x38]
    // 0x14d0178: r16 = 60.000000
    //     0x14d0178: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14d017c: ldr             x16, [x16, #0x110]
    // 0x14d0180: r30 = 62.000000
    //     0x14d0180: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0x14d0184: ldr             lr, [lr, #0xbc0]
    // 0x14d0188: stp             lr, x16, [SP, #0x18]
    // 0x14d018c: r16 = Instance_BoxFit
    //     0x14d018c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14d0190: ldr             x16, [x16, #0x118]
    // 0x14d0194: ldur            lr, [fp, #-0x20]
    // 0x14d0198: stp             lr, x16, [SP, #8]
    // 0x14d019c: ldur            x16, [fp, #-0x30]
    // 0x14d01a0: str             x16, [SP]
    // 0x14d01a4: mov             x1, x0
    // 0x14d01a8: ldur            x2, [fp, #-0x18]
    // 0x14d01ac: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14d01ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14d01b0: ldr             x4, [x4, #0xc28]
    // 0x14d01b4: r0 = CachedNetworkImage()
    //     0x14d01b4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14d01b8: r0 = ClipRRect()
    //     0x14d01b8: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14d01bc: mov             x1, x0
    // 0x14d01c0: ldur            x0, [fp, #-0x28]
    // 0x14d01c4: stur            x1, [fp, #-0x18]
    // 0x14d01c8: StoreField: r1->field_f = r0
    //     0x14d01c8: stur            w0, [x1, #0xf]
    // 0x14d01cc: r0 = Instance_Clip
    //     0x14d01cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14d01d0: ldr             x0, [x0, #0x138]
    // 0x14d01d4: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d01d4: stur            w0, [x1, #0x17]
    // 0x14d01d8: ldur            x0, [fp, #-0x38]
    // 0x14d01dc: StoreField: r1->field_b = r0
    //     0x14d01dc: stur            w0, [x1, #0xb]
    // 0x14d01e0: r0 = Radius()
    //     0x14d01e0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14d01e4: d0 = 12.000000
    //     0x14d01e4: fmov            d0, #12.00000000
    // 0x14d01e8: stur            x0, [fp, #-0x20]
    // 0x14d01ec: StoreField: r0->field_7 = d0
    //     0x14d01ec: stur            d0, [x0, #7]
    // 0x14d01f0: StoreField: r0->field_f = d0
    //     0x14d01f0: stur            d0, [x0, #0xf]
    // 0x14d01f4: r0 = BorderRadius()
    //     0x14d01f4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14d01f8: mov             x2, x0
    // 0x14d01fc: ldur            x0, [fp, #-0x20]
    // 0x14d0200: stur            x2, [fp, #-0x28]
    // 0x14d0204: StoreField: r2->field_7 = r0
    //     0x14d0204: stur            w0, [x2, #7]
    // 0x14d0208: StoreField: r2->field_b = r0
    //     0x14d0208: stur            w0, [x2, #0xb]
    // 0x14d020c: StoreField: r2->field_f = r0
    //     0x14d020c: stur            w0, [x2, #0xf]
    // 0x14d0210: StoreField: r2->field_13 = r0
    //     0x14d0210: stur            w0, [x2, #0x13]
    // 0x14d0214: r1 = Instance_Color
    //     0x14d0214: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d0218: d0 = 0.600000
    //     0x14d0218: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0x14d021c: r0 = withOpacity()
    //     0x14d021c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d0220: stur            x0, [fp, #-0x20]
    // 0x14d0224: r0 = BoxDecoration()
    //     0x14d0224: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14d0228: mov             x3, x0
    // 0x14d022c: ldur            x0, [fp, #-0x20]
    // 0x14d0230: stur            x3, [fp, #-0x30]
    // 0x14d0234: StoreField: r3->field_7 = r0
    //     0x14d0234: stur            w0, [x3, #7]
    // 0x14d0238: ldur            x0, [fp, #-0x28]
    // 0x14d023c: StoreField: r3->field_13 = r0
    //     0x14d023c: stur            w0, [x3, #0x13]
    // 0x14d0240: r0 = Instance_BoxShape
    //     0x14d0240: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0244: ldr             x0, [x0, #0x80]
    // 0x14d0248: StoreField: r3->field_23 = r0
    //     0x14d0248: stur            w0, [x3, #0x23]
    // 0x14d024c: r1 = Null
    //     0x14d024c: mov             x1, NULL
    // 0x14d0250: r2 = 4
    //     0x14d0250: movz            x2, #0x4
    // 0x14d0254: r0 = AllocateArray()
    //     0x14d0254: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d0258: stur            x0, [fp, #-0x20]
    // 0x14d025c: r16 = "+ "
    //     0x14d025c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0x14d0260: ldr             x16, [x16, #0xc30]
    // 0x14d0264: StoreField: r0->field_f = r16
    //     0x14d0264: stur            w16, [x0, #0xf]
    // 0x14d0268: ldur            x1, [fp, #-8]
    // 0x14d026c: LoadField: r2 = r1->field_f
    //     0x14d026c: ldur            w2, [x1, #0xf]
    // 0x14d0270: DecompressPointer r2
    //     0x14d0270: add             x2, x2, HEAP, lsl #32
    // 0x14d0274: mov             x1, x2
    // 0x14d0278: r0 = controller()
    //     0x14d0278: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d027c: LoadField: r1 = r0->field_7f
    //     0x14d027c: ldur            w1, [x0, #0x7f]
    // 0x14d0280: DecompressPointer r1
    //     0x14d0280: add             x1, x1, HEAP, lsl #32
    // 0x14d0284: r0 = value()
    //     0x14d0284: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d0288: LoadField: r1 = r0->field_b
    //     0x14d0288: ldur            w1, [x0, #0xb]
    // 0x14d028c: DecompressPointer r1
    //     0x14d028c: add             x1, x1, HEAP, lsl #32
    // 0x14d0290: cmp             w1, NULL
    // 0x14d0294: b.ne            #0x14d02a0
    // 0x14d0298: r0 = 0
    //     0x14d0298: movz            x0, #0
    // 0x14d029c: b               #0x14d02ac
    // 0x14d02a0: r0 = LoadInt32Instr(r1)
    //     0x14d02a0: sbfx            x0, x1, #1, #0x1f
    //     0x14d02a4: tbz             w1, #0, #0x14d02ac
    //     0x14d02a8: ldur            x0, [x1, #7]
    // 0x14d02ac: ldur            x2, [fp, #-0x18]
    // 0x14d02b0: sub             x3, x0, #4
    // 0x14d02b4: r0 = BoxInt64Instr(r3)
    //     0x14d02b4: sbfiz           x0, x3, #1, #0x1f
    //     0x14d02b8: cmp             x3, x0, asr #1
    //     0x14d02bc: b.eq            #0x14d02c8
    //     0x14d02c0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x14d02c4: stur            x3, [x0, #7]
    // 0x14d02c8: ldur            x1, [fp, #-0x20]
    // 0x14d02cc: ArrayStore: r1[1] = r0  ; List_4
    //     0x14d02cc: add             x25, x1, #0x13
    //     0x14d02d0: str             w0, [x25]
    //     0x14d02d4: tbz             w0, #0, #0x14d02f0
    //     0x14d02d8: ldurb           w16, [x1, #-1]
    //     0x14d02dc: ldurb           w17, [x0, #-1]
    //     0x14d02e0: and             x16, x17, x16, lsr #2
    //     0x14d02e4: tst             x16, HEAP, lsr #32
    //     0x14d02e8: b.eq            #0x14d02f0
    //     0x14d02ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d02f0: ldur            x16, [fp, #-0x20]
    // 0x14d02f4: str             x16, [SP]
    // 0x14d02f8: r0 = _interpolate()
    //     0x14d02f8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14d02fc: ldr             x1, [fp, #0x18]
    // 0x14d0300: stur            x0, [fp, #-8]
    // 0x14d0304: r0 = of()
    //     0x14d0304: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d0308: LoadField: r1 = r0->field_87
    //     0x14d0308: ldur            w1, [x0, #0x87]
    // 0x14d030c: DecompressPointer r1
    //     0x14d030c: add             x1, x1, HEAP, lsl #32
    // 0x14d0310: LoadField: r0 = r1->field_7
    //     0x14d0310: ldur            w0, [x1, #7]
    // 0x14d0314: DecompressPointer r0
    //     0x14d0314: add             x0, x0, HEAP, lsl #32
    // 0x14d0318: r16 = 12.000000
    //     0x14d0318: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14d031c: ldr             x16, [x16, #0x9e8]
    // 0x14d0320: r30 = Instance_Color
    //     0x14d0320: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14d0324: stp             lr, x16, [SP]
    // 0x14d0328: mov             x1, x0
    // 0x14d032c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14d032c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14d0330: ldr             x4, [x4, #0xaa0]
    // 0x14d0334: r0 = copyWith()
    //     0x14d0334: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d0338: stur            x0, [fp, #-0x20]
    // 0x14d033c: r0 = Text()
    //     0x14d033c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d0340: mov             x1, x0
    // 0x14d0344: ldur            x0, [fp, #-8]
    // 0x14d0348: stur            x1, [fp, #-0x28]
    // 0x14d034c: StoreField: r1->field_b = r0
    //     0x14d034c: stur            w0, [x1, #0xb]
    // 0x14d0350: ldur            x0, [fp, #-0x20]
    // 0x14d0354: StoreField: r1->field_13 = r0
    //     0x14d0354: stur            w0, [x1, #0x13]
    // 0x14d0358: r0 = Container()
    //     0x14d0358: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d035c: stur            x0, [fp, #-8]
    // 0x14d0360: r16 = 60.000000
    //     0x14d0360: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14d0364: ldr             x16, [x16, #0x110]
    // 0x14d0368: r30 = 62.000000
    //     0x14d0368: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0x14d036c: ldr             lr, [lr, #0xbc0]
    // 0x14d0370: stp             lr, x16, [SP, #0x18]
    // 0x14d0374: ldur            x16, [fp, #-0x30]
    // 0x14d0378: r30 = Instance_Alignment
    //     0x14d0378: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14d037c: ldr             lr, [lr, #0xb10]
    // 0x14d0380: stp             lr, x16, [SP, #8]
    // 0x14d0384: ldur            x16, [fp, #-0x28]
    // 0x14d0388: str             x16, [SP]
    // 0x14d038c: mov             x1, x0
    // 0x14d0390: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x14d0390: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eb90] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x14d0394: ldr             x4, [x4, #0xb90]
    // 0x14d0398: r0 = Container()
    //     0x14d0398: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d039c: r1 = Null
    //     0x14d039c: mov             x1, NULL
    // 0x14d03a0: r2 = 4
    //     0x14d03a0: movz            x2, #0x4
    // 0x14d03a4: r0 = AllocateArray()
    //     0x14d03a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d03a8: mov             x2, x0
    // 0x14d03ac: ldur            x0, [fp, #-0x18]
    // 0x14d03b0: stur            x2, [fp, #-0x20]
    // 0x14d03b4: StoreField: r2->field_f = r0
    //     0x14d03b4: stur            w0, [x2, #0xf]
    // 0x14d03b8: ldur            x0, [fp, #-8]
    // 0x14d03bc: StoreField: r2->field_13 = r0
    //     0x14d03bc: stur            w0, [x2, #0x13]
    // 0x14d03c0: r1 = <Widget>
    //     0x14d03c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d03c4: r0 = AllocateGrowableArray()
    //     0x14d03c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d03c8: mov             x1, x0
    // 0x14d03cc: ldur            x0, [fp, #-0x20]
    // 0x14d03d0: stur            x1, [fp, #-8]
    // 0x14d03d4: StoreField: r1->field_f = r0
    //     0x14d03d4: stur            w0, [x1, #0xf]
    // 0x14d03d8: r0 = 4
    //     0x14d03d8: movz            x0, #0x4
    // 0x14d03dc: StoreField: r1->field_b = r0
    //     0x14d03dc: stur            w0, [x1, #0xb]
    // 0x14d03e0: r0 = Stack()
    //     0x14d03e0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14d03e4: mov             x1, x0
    // 0x14d03e8: r0 = Instance_Alignment
    //     0x14d03e8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14d03ec: ldr             x0, [x0, #0xb10]
    // 0x14d03f0: stur            x1, [fp, #-0x18]
    // 0x14d03f4: StoreField: r1->field_f = r0
    //     0x14d03f4: stur            w0, [x1, #0xf]
    // 0x14d03f8: r0 = Instance_StackFit
    //     0x14d03f8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14d03fc: ldr             x0, [x0, #0xfa8]
    // 0x14d0400: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d0400: stur            w0, [x1, #0x17]
    // 0x14d0404: r0 = Instance_Clip
    //     0x14d0404: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14d0408: ldr             x0, [x0, #0x7e0]
    // 0x14d040c: StoreField: r1->field_1b = r0
    //     0x14d040c: stur            w0, [x1, #0x1b]
    // 0x14d0410: ldur            x0, [fp, #-8]
    // 0x14d0414: StoreField: r1->field_b = r0
    //     0x14d0414: stur            w0, [x1, #0xb]
    // 0x14d0418: r0 = InkWell()
    //     0x14d0418: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d041c: mov             x3, x0
    // 0x14d0420: ldur            x0, [fp, #-0x18]
    // 0x14d0424: stur            x3, [fp, #-8]
    // 0x14d0428: StoreField: r3->field_b = r0
    //     0x14d0428: stur            w0, [x3, #0xb]
    // 0x14d042c: ldur            x2, [fp, #-0x10]
    // 0x14d0430: r1 = Function '<anonymous closure>':.
    //     0x14d0430: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ea8] AnonymousClosure: (0x9bade8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14d0434: ldr             x1, [x1, #0xea8]
    // 0x14d0438: r0 = AllocateClosure()
    //     0x14d0438: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d043c: mov             x1, x0
    // 0x14d0440: ldur            x0, [fp, #-8]
    // 0x14d0444: StoreField: r0->field_f = r1
    //     0x14d0444: stur            w1, [x0, #0xf]
    // 0x14d0448: r1 = true
    //     0x14d0448: add             x1, NULL, #0x20  ; true
    // 0x14d044c: StoreField: r0->field_43 = r1
    //     0x14d044c: stur            w1, [x0, #0x43]
    // 0x14d0450: r2 = Instance_BoxShape
    //     0x14d0450: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d0454: ldr             x2, [x2, #0x80]
    // 0x14d0458: StoreField: r0->field_47 = r2
    //     0x14d0458: stur            w2, [x0, #0x47]
    // 0x14d045c: StoreField: r0->field_6f = r1
    //     0x14d045c: stur            w1, [x0, #0x6f]
    // 0x14d0460: r2 = false
    //     0x14d0460: add             x2, NULL, #0x30  ; false
    // 0x14d0464: StoreField: r0->field_73 = r2
    //     0x14d0464: stur            w2, [x0, #0x73]
    // 0x14d0468: StoreField: r0->field_83 = r1
    //     0x14d0468: stur            w1, [x0, #0x83]
    // 0x14d046c: StoreField: r0->field_7b = r2
    //     0x14d046c: stur            w2, [x0, #0x7b]
    // 0x14d0470: LeaveFrame
    //     0x14d0470: mov             SP, fp
    //     0x14d0474: ldp             fp, lr, [SP], #0x10
    // 0x14d0478: ret
    //     0x14d0478: ret             
    // 0x14d047c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d047c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d0480: b               #0x14cfd24
    // 0x14d0484: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d0484: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d0488, size: 0xfc
    // 0x14d0488: EnterFrame
    //     0x14d0488: stp             fp, lr, [SP, #-0x10]!
    //     0x14d048c: mov             fp, SP
    // 0x14d0490: AllocStack(0x28)
    //     0x14d0490: sub             SP, SP, #0x28
    // 0x14d0494: SetupParameters()
    //     0x14d0494: ldr             x0, [fp, #0x10]
    //     0x14d0498: ldur            w2, [x0, #0x17]
    //     0x14d049c: add             x2, x2, HEAP, lsl #32
    //     0x14d04a0: stur            x2, [fp, #-8]
    // 0x14d04a4: CheckStackOverflow
    //     0x14d04a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d04a8: cmp             SP, x16
    //     0x14d04ac: b.ls            #0x14d057c
    // 0x14d04b0: LoadField: r0 = r2->field_b
    //     0x14d04b0: ldur            w0, [x2, #0xb]
    // 0x14d04b4: DecompressPointer r0
    //     0x14d04b4: add             x0, x0, HEAP, lsl #32
    // 0x14d04b8: LoadField: r1 = r0->field_f
    //     0x14d04b8: ldur            w1, [x0, #0xf]
    // 0x14d04bc: DecompressPointer r1
    //     0x14d04bc: add             x1, x1, HEAP, lsl #32
    // 0x14d04c0: r0 = controller()
    //     0x14d04c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d04c4: ldur            x2, [fp, #-8]
    // 0x14d04c8: stur            x0, [fp, #-0x10]
    // 0x14d04cc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14d04cc: ldur            w1, [x2, #0x17]
    // 0x14d04d0: DecompressPointer r1
    //     0x14d04d0: add             x1, x1, HEAP, lsl #32
    // 0x14d04d4: str             x1, [SP]
    // 0x14d04d8: r4 = 0
    //     0x14d04d8: movz            x4, #0
    // 0x14d04dc: ldr             x0, [SP]
    // 0x14d04e0: r16 = UnlinkedCall_0x613b5c
    //     0x14d04e0: add             x16, PP, #0x41, lsl #12  ; [pp+0x41eb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d04e4: add             x16, x16, #0xeb0
    // 0x14d04e8: ldp             x5, lr, [x16]
    // 0x14d04ec: blr             lr
    // 0x14d04f0: str             x0, [SP]
    // 0x14d04f4: ldur            x1, [fp, #-0x10]
    // 0x14d04f8: r2 = "single_media"
    //     0x14d04f8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0x14d04fc: ldr             x2, [x2, #0xab0]
    // 0x14d0500: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0x14d0500: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0x14d0504: ldr             x4, [x4, #0xaf8]
    // 0x14d0508: r0 = ratingReviewClickedEvent()
    //     0x14d0508: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x14d050c: ldur            x2, [fp, #-8]
    // 0x14d0510: LoadField: r1 = r2->field_f
    //     0x14d0510: ldur            w1, [x2, #0xf]
    // 0x14d0514: DecompressPointer r1
    //     0x14d0514: add             x1, x1, HEAP, lsl #32
    // 0x14d0518: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d0518: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d051c: r0 = of()
    //     0x14d051c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x14d0520: ldur            x2, [fp, #-8]
    // 0x14d0524: r1 = Function '<anonymous closure>':.
    //     0x14d0524: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ec0] AnonymousClosure: (0x14d0584), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14caf48)
    //     0x14d0528: ldr             x1, [x1, #0xec0]
    // 0x14d052c: stur            x0, [fp, #-8]
    // 0x14d0530: r0 = AllocateClosure()
    //     0x14d0530: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d0534: r1 = Null
    //     0x14d0534: mov             x1, NULL
    // 0x14d0538: stur            x0, [fp, #-0x10]
    // 0x14d053c: r0 = MaterialPageRoute()
    //     0x14d053c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x14d0540: mov             x1, x0
    // 0x14d0544: ldur            x2, [fp, #-0x10]
    // 0x14d0548: stur            x0, [fp, #-0x10]
    // 0x14d054c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14d054c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14d0550: r0 = MaterialPageRoute()
    //     0x14d0550: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x14d0554: ldur            x16, [fp, #-8]
    // 0x14d0558: stp             x16, NULL, [SP, #8]
    // 0x14d055c: ldur            x16, [fp, #-0x10]
    // 0x14d0560: str             x16, [SP]
    // 0x14d0564: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14d0564: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14d0568: r0 = push()
    //     0x14d0568: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x14d056c: r0 = Null
    //     0x14d056c: mov             x0, NULL
    // 0x14d0570: LeaveFrame
    //     0x14d0570: mov             SP, fp
    //     0x14d0574: ldp             fp, lr, [SP], #0x10
    // 0x14d0578: ret
    //     0x14d0578: ret             
    // 0x14d057c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d057c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d0580: b               #0x14d04b0
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14d0584, size: 0x190
    // 0x14d0584: EnterFrame
    //     0x14d0584: stp             fp, lr, [SP, #-0x10]!
    //     0x14d0588: mov             fp, SP
    // 0x14d058c: AllocStack(0x38)
    //     0x14d058c: sub             SP, SP, #0x38
    // 0x14d0590: SetupParameters()
    //     0x14d0590: ldr             x0, [fp, #0x18]
    //     0x14d0594: ldur            w2, [x0, #0x17]
    //     0x14d0598: add             x2, x2, HEAP, lsl #32
    //     0x14d059c: stur            x2, [fp, #-0x10]
    // 0x14d05a0: CheckStackOverflow
    //     0x14d05a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d05a4: cmp             SP, x16
    //     0x14d05a8: b.ls            #0x14d070c
    // 0x14d05ac: LoadField: r0 = r2->field_b
    //     0x14d05ac: ldur            w0, [x2, #0xb]
    // 0x14d05b0: DecompressPointer r0
    //     0x14d05b0: add             x0, x0, HEAP, lsl #32
    // 0x14d05b4: stur            x0, [fp, #-8]
    // 0x14d05b8: LoadField: r1 = r0->field_f
    //     0x14d05b8: ldur            w1, [x0, #0xf]
    // 0x14d05bc: DecompressPointer r1
    //     0x14d05bc: add             x1, x1, HEAP, lsl #32
    // 0x14d05c0: r0 = controller()
    //     0x14d05c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d05c4: LoadField: r1 = r0->field_7f
    //     0x14d05c4: ldur            w1, [x0, #0x7f]
    // 0x14d05c8: DecompressPointer r1
    //     0x14d05c8: add             x1, x1, HEAP, lsl #32
    // 0x14d05cc: r0 = value()
    //     0x14d05cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d05d0: LoadField: r1 = r0->field_f
    //     0x14d05d0: ldur            w1, [x0, #0xf]
    // 0x14d05d4: DecompressPointer r1
    //     0x14d05d4: add             x1, x1, HEAP, lsl #32
    // 0x14d05d8: cmp             w1, NULL
    // 0x14d05dc: b.ne            #0x14d05f4
    // 0x14d05e0: r1 = <ReviewRatingEntity>
    //     0x14d05e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0x14d05e4: ldr             x1, [x1, #0x150]
    // 0x14d05e8: r2 = 0
    //     0x14d05e8: movz            x2, #0
    // 0x14d05ec: r0 = _GrowableList()
    //     0x14d05ec: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x14d05f0: mov             x1, x0
    // 0x14d05f4: ldur            x2, [fp, #-0x10]
    // 0x14d05f8: ldur            x0, [fp, #-8]
    // 0x14d05fc: stur            x1, [fp, #-0x20]
    // 0x14d0600: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14d0600: ldur            w3, [x2, #0x17]
    // 0x14d0604: DecompressPointer r3
    //     0x14d0604: add             x3, x3, HEAP, lsl #32
    // 0x14d0608: stur            x3, [fp, #-0x18]
    // 0x14d060c: str             x3, [SP]
    // 0x14d0610: r4 = 0
    //     0x14d0610: movz            x4, #0
    // 0x14d0614: ldr             x0, [SP]
    // 0x14d0618: r16 = UnlinkedCall_0x613b5c
    //     0x14d0618: add             x16, PP, #0x41, lsl #12  ; [pp+0x41ec8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d061c: add             x16, x16, #0xec8
    // 0x14d0620: ldp             x5, lr, [x16]
    // 0x14d0624: blr             lr
    // 0x14d0628: stur            x0, [fp, #-0x28]
    // 0x14d062c: ldur            x16, [fp, #-0x18]
    // 0x14d0630: str             x16, [SP]
    // 0x14d0634: r4 = 0
    //     0x14d0634: movz            x4, #0
    // 0x14d0638: ldr             x0, [SP]
    // 0x14d063c: r16 = UnlinkedCall_0x613b5c
    //     0x14d063c: add             x16, PP, #0x41, lsl #12  ; [pp+0x41ed8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d0640: add             x16, x16, #0xed8
    // 0x14d0644: ldp             x5, lr, [x16]
    // 0x14d0648: blr             lr
    // 0x14d064c: mov             x3, x0
    // 0x14d0650: r2 = Null
    //     0x14d0650: mov             x2, NULL
    // 0x14d0654: r1 = Null
    //     0x14d0654: mov             x1, NULL
    // 0x14d0658: stur            x3, [fp, #-0x18]
    // 0x14d065c: branchIfSmi(r0, 0x14d0684)
    //     0x14d065c: tbz             w0, #0, #0x14d0684
    // 0x14d0660: r4 = LoadClassIdInstr(r0)
    //     0x14d0660: ldur            x4, [x0, #-1]
    //     0x14d0664: ubfx            x4, x4, #0xc, #0x14
    // 0x14d0668: sub             x4, x4, #0x3c
    // 0x14d066c: cmp             x4, #1
    // 0x14d0670: b.ls            #0x14d0684
    // 0x14d0674: r8 = int?
    //     0x14d0674: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0x14d0678: r3 = Null
    //     0x14d0678: add             x3, PP, #0x41, lsl #12  ; [pp+0x41ee8] Null
    //     0x14d067c: ldr             x3, [x3, #0xee8]
    // 0x14d0680: r0 = int?()
    //     0x14d0680: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0x14d0684: ldur            x0, [fp, #-8]
    // 0x14d0688: LoadField: r1 = r0->field_f
    //     0x14d0688: ldur            w1, [x0, #0xf]
    // 0x14d068c: DecompressPointer r1
    //     0x14d068c: add             x1, x1, HEAP, lsl #32
    // 0x14d0690: r0 = controller()
    //     0x14d0690: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d0694: LoadField: r1 = r0->field_97
    //     0x14d0694: ldur            w1, [x0, #0x97]
    // 0x14d0698: DecompressPointer r1
    //     0x14d0698: add             x1, x1, HEAP, lsl #32
    // 0x14d069c: stur            x1, [fp, #-8]
    // 0x14d06a0: r0 = RatingReviewAllMediaOnTapImage()
    //     0x14d06a0: bl              #0xb15c24  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0x14d06a4: mov             x3, x0
    // 0x14d06a8: ldur            x0, [fp, #-0x20]
    // 0x14d06ac: stur            x3, [fp, #-0x30]
    // 0x14d06b0: StoreField: r3->field_f = r0
    //     0x14d06b0: stur            w0, [x3, #0xf]
    // 0x14d06b4: ldur            x0, [fp, #-0x28]
    // 0x14d06b8: r1 = LoadInt32Instr(r0)
    //     0x14d06b8: sbfx            x1, x0, #1, #0x1f
    //     0x14d06bc: tbz             w0, #0, #0x14d06c4
    //     0x14d06c0: ldur            x1, [x0, #7]
    // 0x14d06c4: StoreField: r3->field_13 = r1
    //     0x14d06c4: stur            x1, [x3, #0x13]
    // 0x14d06c8: ldur            x0, [fp, #-0x18]
    // 0x14d06cc: StoreField: r3->field_1b = r0
    //     0x14d06cc: stur            w0, [x3, #0x1b]
    // 0x14d06d0: r0 = "direct_image"
    //     0x14d06d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0x14d06d4: ldr             x0, [x0, #0xc98]
    // 0x14d06d8: StoreField: r3->field_b = r0
    //     0x14d06d8: stur            w0, [x3, #0xb]
    // 0x14d06dc: ldur            x2, [fp, #-0x10]
    // 0x14d06e0: r1 = Function '<anonymous closure>':.
    //     0x14d06e0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ef8] AnonymousClosure: (0x9bb314), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14d06e4: ldr             x1, [x1, #0xef8]
    // 0x14d06e8: r0 = AllocateClosure()
    //     0x14d06e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d06ec: mov             x1, x0
    // 0x14d06f0: ldur            x0, [fp, #-0x30]
    // 0x14d06f4: StoreField: r0->field_1f = r1
    //     0x14d06f4: stur            w1, [x0, #0x1f]
    // 0x14d06f8: ldur            x1, [fp, #-8]
    // 0x14d06fc: StoreField: r0->field_23 = r1
    //     0x14d06fc: stur            w1, [x0, #0x23]
    // 0x14d0700: LeaveFrame
    //     0x14d0700: mov             SP, fp
    //     0x14d0704: ldp             fp, lr, [SP], #0x10
    // 0x14d0708: ret
    //     0x14d0708: ret             
    // 0x14d070c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d070c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d0710: b               #0x14d05ac
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15de0b8, size: 0x38c
    // 0x15de0b8: EnterFrame
    //     0x15de0b8: stp             fp, lr, [SP, #-0x10]!
    //     0x15de0bc: mov             fp, SP
    // 0x15de0c0: AllocStack(0x60)
    //     0x15de0c0: sub             SP, SP, #0x60
    // 0x15de0c4: SetupParameters(ReviewListWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15de0c4: mov             x0, x1
    //     0x15de0c8: stur            x1, [fp, #-8]
    //     0x15de0cc: mov             x1, x2
    //     0x15de0d0: stur            x2, [fp, #-0x10]
    // 0x15de0d4: CheckStackOverflow
    //     0x15de0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15de0d8: cmp             SP, x16
    //     0x15de0dc: b.ls            #0x15de43c
    // 0x15de0e0: r1 = 2
    //     0x15de0e0: movz            x1, #0x2
    // 0x15de0e4: r0 = AllocateContext()
    //     0x15de0e4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15de0e8: mov             x2, x0
    // 0x15de0ec: ldur            x0, [fp, #-8]
    // 0x15de0f0: stur            x2, [fp, #-0x18]
    // 0x15de0f4: StoreField: r2->field_f = r0
    //     0x15de0f4: stur            w0, [x2, #0xf]
    // 0x15de0f8: ldur            x1, [fp, #-0x10]
    // 0x15de0fc: StoreField: r2->field_13 = r1
    //     0x15de0fc: stur            w1, [x2, #0x13]
    // 0x15de100: r0 = of()
    //     0x15de100: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de104: LoadField: r1 = r0->field_5b
    //     0x15de104: ldur            w1, [x0, #0x5b]
    // 0x15de108: DecompressPointer r1
    //     0x15de108: add             x1, x1, HEAP, lsl #32
    // 0x15de10c: stur            x1, [fp, #-0x10]
    // 0x15de110: r0 = ColorFilter()
    //     0x15de110: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15de114: mov             x1, x0
    // 0x15de118: ldur            x0, [fp, #-0x10]
    // 0x15de11c: stur            x1, [fp, #-0x20]
    // 0x15de120: StoreField: r1->field_7 = r0
    //     0x15de120: stur            w0, [x1, #7]
    // 0x15de124: r0 = Instance_BlendMode
    //     0x15de124: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15de128: ldr             x0, [x0, #0xb30]
    // 0x15de12c: StoreField: r1->field_b = r0
    //     0x15de12c: stur            w0, [x1, #0xb]
    // 0x15de130: r0 = 1
    //     0x15de130: movz            x0, #0x1
    // 0x15de134: StoreField: r1->field_13 = r0
    //     0x15de134: stur            x0, [x1, #0x13]
    // 0x15de138: r0 = SvgPicture()
    //     0x15de138: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15de13c: stur            x0, [fp, #-0x10]
    // 0x15de140: ldur            x16, [fp, #-0x20]
    // 0x15de144: str             x16, [SP]
    // 0x15de148: mov             x1, x0
    // 0x15de14c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15de14c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15de150: ldr             x2, [x2, #0xa40]
    // 0x15de154: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15de154: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15de158: ldr             x4, [x4, #0xa38]
    // 0x15de15c: r0 = SvgPicture.asset()
    //     0x15de15c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15de160: r0 = Align()
    //     0x15de160: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15de164: mov             x1, x0
    // 0x15de168: r0 = Instance_Alignment
    //     0x15de168: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15de16c: ldr             x0, [x0, #0xb10]
    // 0x15de170: stur            x1, [fp, #-0x20]
    // 0x15de174: StoreField: r1->field_f = r0
    //     0x15de174: stur            w0, [x1, #0xf]
    // 0x15de178: r0 = 1.500000
    //     0x15de178: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd18] 1.5
    //     0x15de17c: ldr             x0, [x0, #0xd18]
    // 0x15de180: StoreField: r1->field_13 = r0
    //     0x15de180: stur            w0, [x1, #0x13]
    // 0x15de184: ArrayStore: r1[0] = r0  ; List_4
    //     0x15de184: stur            w0, [x1, #0x17]
    // 0x15de188: ldur            x0, [fp, #-0x10]
    // 0x15de18c: StoreField: r1->field_b = r0
    //     0x15de18c: stur            w0, [x1, #0xb]
    // 0x15de190: r0 = InkWell()
    //     0x15de190: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15de194: mov             x3, x0
    // 0x15de198: ldur            x0, [fp, #-0x20]
    // 0x15de19c: stur            x3, [fp, #-0x10]
    // 0x15de1a0: StoreField: r3->field_b = r0
    //     0x15de1a0: stur            w0, [x3, #0xb]
    // 0x15de1a4: ldur            x2, [fp, #-0x18]
    // 0x15de1a8: r1 = Function '<anonymous closure>':.
    //     0x15de1a8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41f00] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x15de1ac: ldr             x1, [x1, #0xf00]
    // 0x15de1b0: r0 = AllocateClosure()
    //     0x15de1b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15de1b4: ldur            x2, [fp, #-0x10]
    // 0x15de1b8: StoreField: r2->field_f = r0
    //     0x15de1b8: stur            w0, [x2, #0xf]
    // 0x15de1bc: r0 = true
    //     0x15de1bc: add             x0, NULL, #0x20  ; true
    // 0x15de1c0: StoreField: r2->field_43 = r0
    //     0x15de1c0: stur            w0, [x2, #0x43]
    // 0x15de1c4: r3 = Instance_BoxShape
    //     0x15de1c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15de1c8: ldr             x3, [x3, #0x80]
    // 0x15de1cc: StoreField: r2->field_47 = r3
    //     0x15de1cc: stur            w3, [x2, #0x47]
    // 0x15de1d0: StoreField: r2->field_6f = r0
    //     0x15de1d0: stur            w0, [x2, #0x6f]
    // 0x15de1d4: r4 = false
    //     0x15de1d4: add             x4, NULL, #0x30  ; false
    // 0x15de1d8: StoreField: r2->field_73 = r4
    //     0x15de1d8: stur            w4, [x2, #0x73]
    // 0x15de1dc: StoreField: r2->field_83 = r0
    //     0x15de1dc: stur            w0, [x2, #0x83]
    // 0x15de1e0: StoreField: r2->field_7b = r4
    //     0x15de1e0: stur            w4, [x2, #0x7b]
    // 0x15de1e4: ldur            x5, [fp, #-0x18]
    // 0x15de1e8: LoadField: r1 = r5->field_13
    //     0x15de1e8: ldur            w1, [x5, #0x13]
    // 0x15de1ec: DecompressPointer r1
    //     0x15de1ec: add             x1, x1, HEAP, lsl #32
    // 0x15de1f0: r0 = of()
    //     0x15de1f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15de1f4: LoadField: r1 = r0->field_87
    //     0x15de1f4: ldur            w1, [x0, #0x87]
    // 0x15de1f8: DecompressPointer r1
    //     0x15de1f8: add             x1, x1, HEAP, lsl #32
    // 0x15de1fc: LoadField: r0 = r1->field_2b
    //     0x15de1fc: ldur            w0, [x1, #0x2b]
    // 0x15de200: DecompressPointer r0
    //     0x15de200: add             x0, x0, HEAP, lsl #32
    // 0x15de204: stur            x0, [fp, #-0x20]
    // 0x15de208: r0 = Text()
    //     0x15de208: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15de20c: mov             x1, x0
    // 0x15de210: r0 = "Ratings & Reviews"
    //     0x15de210: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd28] "Ratings & Reviews"
    //     0x15de214: ldr             x0, [x0, #0xd28]
    // 0x15de218: stur            x1, [fp, #-0x28]
    // 0x15de21c: StoreField: r1->field_b = r0
    //     0x15de21c: stur            w0, [x1, #0xb]
    // 0x15de220: ldur            x0, [fp, #-0x20]
    // 0x15de224: StoreField: r1->field_13 = r0
    //     0x15de224: stur            w0, [x1, #0x13]
    // 0x15de228: r0 = Container()
    //     0x15de228: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15de22c: stur            x0, [fp, #-0x20]
    // 0x15de230: r16 = Instance_Color
    //     0x15de230: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15de234: r30 = 1.000000
    //     0x15de234: ldr             lr, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15de238: stp             lr, x16, [SP]
    // 0x15de23c: mov             x1, x0
    // 0x15de240: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, height, 0x2, null]
    //     0x15de240: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd30] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "height", 0x2, Null]
    //     0x15de244: ldr             x4, [x4, #0xd30]
    // 0x15de248: r0 = Container()
    //     0x15de248: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15de24c: r0 = PreferredSize()
    //     0x15de24c: bl              #0x15d6644  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0x15de250: mov             x3, x0
    // 0x15de254: r0 = Instance_Size
    //     0x15de254: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb70] Obj!Size@d6c3e1
    //     0x15de258: ldr             x0, [x0, #0xb70]
    // 0x15de25c: stur            x3, [fp, #-0x30]
    // 0x15de260: StoreField: r3->field_f = r0
    //     0x15de260: stur            w0, [x3, #0xf]
    // 0x15de264: ldur            x0, [fp, #-0x20]
    // 0x15de268: StoreField: r3->field_b = r0
    //     0x15de268: stur            w0, [x3, #0xb]
    // 0x15de26c: r1 = <Widget>
    //     0x15de26c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15de270: r2 = 0
    //     0x15de270: movz            x2, #0
    // 0x15de274: r0 = _GrowableList()
    //     0x15de274: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x15de278: ldur            x1, [fp, #-8]
    // 0x15de27c: stur            x0, [fp, #-8]
    // 0x15de280: r0 = controller()
    //     0x15de280: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de284: LoadField: r1 = r0->field_7b
    //     0x15de284: ldur            w1, [x0, #0x7b]
    // 0x15de288: DecompressPointer r1
    //     0x15de288: add             x1, x1, HEAP, lsl #32
    // 0x15de28c: r0 = value()
    //     0x15de28c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15de290: LoadField: r1 = r0->field_b
    //     0x15de290: ldur            w1, [x0, #0xb]
    // 0x15de294: DecompressPointer r1
    //     0x15de294: add             x1, x1, HEAP, lsl #32
    // 0x15de298: cmp             w1, NULL
    // 0x15de29c: b.ne            #0x15de2a8
    // 0x15de2a0: r0 = Null
    //     0x15de2a0: mov             x0, NULL
    // 0x15de2a4: b               #0x15de2cc
    // 0x15de2a8: LoadField: r0 = r1->field_f
    //     0x15de2a8: ldur            w0, [x1, #0xf]
    // 0x15de2ac: DecompressPointer r0
    //     0x15de2ac: add             x0, x0, HEAP, lsl #32
    // 0x15de2b0: cmp             w0, NULL
    // 0x15de2b4: b.ne            #0x15de2c0
    // 0x15de2b8: r0 = Null
    //     0x15de2b8: mov             x0, NULL
    // 0x15de2bc: b               #0x15de2cc
    // 0x15de2c0: LoadField: r1 = r0->field_b
    //     0x15de2c0: ldur            w1, [x0, #0xb]
    // 0x15de2c4: DecompressPointer r1
    //     0x15de2c4: add             x1, x1, HEAP, lsl #32
    // 0x15de2c8: mov             x0, x1
    // 0x15de2cc: cmp             w0, NULL
    // 0x15de2d0: b.ne            #0x15de2dc
    // 0x15de2d4: ldur            x2, [fp, #-8]
    // 0x15de2d8: b               #0x15de3f8
    // 0x15de2dc: tbnz            w0, #4, #0x15de3f4
    // 0x15de2e0: ldur            x1, [fp, #-8]
    // 0x15de2e4: r0 = SvgPicture()
    //     0x15de2e4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15de2e8: mov             x1, x0
    // 0x15de2ec: r2 = "assets/images/shopdeck-tag.svg"
    //     0x15de2ec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd38] "assets/images/shopdeck-tag.svg"
    //     0x15de2f0: ldr             x2, [x2, #0xd38]
    // 0x15de2f4: stur            x0, [fp, #-0x20]
    // 0x15de2f8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15de2f8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15de2fc: r0 = SvgPicture.asset()
    //     0x15de2fc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15de300: r0 = InkWell()
    //     0x15de300: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15de304: mov             x3, x0
    // 0x15de308: ldur            x0, [fp, #-0x20]
    // 0x15de30c: stur            x3, [fp, #-0x38]
    // 0x15de310: StoreField: r3->field_b = r0
    //     0x15de310: stur            w0, [x3, #0xb]
    // 0x15de314: ldur            x2, [fp, #-0x18]
    // 0x15de318: r1 = Function '<anonymous closure>':.
    //     0x15de318: add             x1, PP, #0x41, lsl #12  ; [pp+0x41f08] AnonymousClosure: (0x15de444), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15de0b8)
    //     0x15de31c: ldr             x1, [x1, #0xf08]
    // 0x15de320: r0 = AllocateClosure()
    //     0x15de320: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15de324: mov             x1, x0
    // 0x15de328: ldur            x0, [fp, #-0x38]
    // 0x15de32c: StoreField: r0->field_f = r1
    //     0x15de32c: stur            w1, [x0, #0xf]
    // 0x15de330: r1 = true
    //     0x15de330: add             x1, NULL, #0x20  ; true
    // 0x15de334: StoreField: r0->field_43 = r1
    //     0x15de334: stur            w1, [x0, #0x43]
    // 0x15de338: r2 = Instance_BoxShape
    //     0x15de338: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15de33c: ldr             x2, [x2, #0x80]
    // 0x15de340: StoreField: r0->field_47 = r2
    //     0x15de340: stur            w2, [x0, #0x47]
    // 0x15de344: StoreField: r0->field_6f = r1
    //     0x15de344: stur            w1, [x0, #0x6f]
    // 0x15de348: r2 = false
    //     0x15de348: add             x2, NULL, #0x30  ; false
    // 0x15de34c: StoreField: r0->field_73 = r2
    //     0x15de34c: stur            w2, [x0, #0x73]
    // 0x15de350: StoreField: r0->field_83 = r1
    //     0x15de350: stur            w1, [x0, #0x83]
    // 0x15de354: StoreField: r0->field_7b = r2
    //     0x15de354: stur            w2, [x0, #0x7b]
    // 0x15de358: r0 = Padding()
    //     0x15de358: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15de35c: mov             x2, x0
    // 0x15de360: r0 = Instance_EdgeInsets
    //     0x15de360: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0x15de364: ldr             x0, [x0, #0xd48]
    // 0x15de368: stur            x2, [fp, #-0x18]
    // 0x15de36c: StoreField: r2->field_f = r0
    //     0x15de36c: stur            w0, [x2, #0xf]
    // 0x15de370: ldur            x0, [fp, #-0x38]
    // 0x15de374: StoreField: r2->field_b = r0
    //     0x15de374: stur            w0, [x2, #0xb]
    // 0x15de378: ldur            x0, [fp, #-8]
    // 0x15de37c: LoadField: r1 = r0->field_b
    //     0x15de37c: ldur            w1, [x0, #0xb]
    // 0x15de380: LoadField: r3 = r0->field_f
    //     0x15de380: ldur            w3, [x0, #0xf]
    // 0x15de384: DecompressPointer r3
    //     0x15de384: add             x3, x3, HEAP, lsl #32
    // 0x15de388: LoadField: r4 = r3->field_b
    //     0x15de388: ldur            w4, [x3, #0xb]
    // 0x15de38c: r3 = LoadInt32Instr(r1)
    //     0x15de38c: sbfx            x3, x1, #1, #0x1f
    // 0x15de390: stur            x3, [fp, #-0x40]
    // 0x15de394: r1 = LoadInt32Instr(r4)
    //     0x15de394: sbfx            x1, x4, #1, #0x1f
    // 0x15de398: cmp             x3, x1
    // 0x15de39c: b.ne            #0x15de3a8
    // 0x15de3a0: mov             x1, x0
    // 0x15de3a4: r0 = _growToNextCapacity()
    //     0x15de3a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15de3a8: ldur            x2, [fp, #-8]
    // 0x15de3ac: ldur            x3, [fp, #-0x40]
    // 0x15de3b0: add             x0, x3, #1
    // 0x15de3b4: lsl             x1, x0, #1
    // 0x15de3b8: StoreField: r2->field_b = r1
    //     0x15de3b8: stur            w1, [x2, #0xb]
    // 0x15de3bc: LoadField: r1 = r2->field_f
    //     0x15de3bc: ldur            w1, [x2, #0xf]
    // 0x15de3c0: DecompressPointer r1
    //     0x15de3c0: add             x1, x1, HEAP, lsl #32
    // 0x15de3c4: ldur            x0, [fp, #-0x18]
    // 0x15de3c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x15de3c8: add             x25, x1, x3, lsl #2
    //     0x15de3cc: add             x25, x25, #0xf
    //     0x15de3d0: str             w0, [x25]
    //     0x15de3d4: tbz             w0, #0, #0x15de3f0
    //     0x15de3d8: ldurb           w16, [x1, #-1]
    //     0x15de3dc: ldurb           w17, [x0, #-1]
    //     0x15de3e0: and             x16, x17, x16, lsr #2
    //     0x15de3e4: tst             x16, HEAP, lsr #32
    //     0x15de3e8: b.eq            #0x15de3f0
    //     0x15de3ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15de3f0: b               #0x15de3f8
    // 0x15de3f4: ldur            x2, [fp, #-8]
    // 0x15de3f8: r0 = AppBar()
    //     0x15de3f8: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15de3fc: stur            x0, [fp, #-0x18]
    // 0x15de400: r16 = 0.000000
    //     0x15de400: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x15de404: ldur            lr, [fp, #-0x28]
    // 0x15de408: stp             lr, x16, [SP, #0x10]
    // 0x15de40c: ldur            x16, [fp, #-0x30]
    // 0x15de410: ldur            lr, [fp, #-8]
    // 0x15de414: stp             lr, x16, [SP]
    // 0x15de418: mov             x1, x0
    // 0x15de41c: ldur            x2, [fp, #-0x10]
    // 0x15de420: r4 = const [0, 0x6, 0x4, 0x2, actions, 0x5, bottom, 0x4, title, 0x3, titleSpacing, 0x2, null]
    //     0x15de420: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd50] List(13) [0, 0x6, 0x4, 0x2, "actions", 0x5, "bottom", 0x4, "title", 0x3, "titleSpacing", 0x2, Null]
    //     0x15de424: ldr             x4, [x4, #0xd50]
    // 0x15de428: r0 = AppBar()
    //     0x15de428: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15de42c: ldur            x0, [fp, #-0x18]
    // 0x15de430: LeaveFrame
    //     0x15de430: mov             SP, fp
    //     0x15de434: ldp             fp, lr, [SP], #0x10
    // 0x15de438: ret
    //     0x15de438: ret             
    // 0x15de43c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15de43c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15de440: b               #0x15de0e0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15de444, size: 0xa8
    // 0x15de444: EnterFrame
    //     0x15de444: stp             fp, lr, [SP, #-0x10]!
    //     0x15de448: mov             fp, SP
    // 0x15de44c: AllocStack(0x38)
    //     0x15de44c: sub             SP, SP, #0x38
    // 0x15de450: SetupParameters()
    //     0x15de450: ldr             x0, [fp, #0x10]
    //     0x15de454: ldur            w2, [x0, #0x17]
    //     0x15de458: add             x2, x2, HEAP, lsl #32
    //     0x15de45c: stur            x2, [fp, #-8]
    // 0x15de460: CheckStackOverflow
    //     0x15de460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15de464: cmp             SP, x16
    //     0x15de468: b.ls            #0x15de4e4
    // 0x15de46c: LoadField: r1 = r2->field_f
    //     0x15de46c: ldur            w1, [x2, #0xf]
    // 0x15de470: DecompressPointer r1
    //     0x15de470: add             x1, x1, HEAP, lsl #32
    // 0x15de474: r0 = controller()
    //     0x15de474: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15de478: mov             x1, x0
    // 0x15de47c: r2 = "trusted_badge"
    //     0x15de47c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "trusted_badge"
    //     0x15de480: ldr             x2, [x2, #0xd58]
    // 0x15de484: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15de484: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15de488: r0 = ratingReviewClickedEvent()
    //     0x15de488: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x15de48c: ldur            x0, [fp, #-8]
    // 0x15de490: LoadField: r3 = r0->field_13
    //     0x15de490: ldur            w3, [x0, #0x13]
    // 0x15de494: DecompressPointer r3
    //     0x15de494: add             x3, x3, HEAP, lsl #32
    // 0x15de498: stur            x3, [fp, #-0x10]
    // 0x15de49c: r1 = Function '<anonymous closure>':.
    //     0x15de49c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41f10] AnonymousClosure: (0x999958), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x15de4a0: ldr             x1, [x1, #0xf10]
    // 0x15de4a4: r2 = Null
    //     0x15de4a4: mov             x2, NULL
    // 0x15de4a8: r0 = AllocateClosure()
    //     0x15de4a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15de4ac: stp             x0, NULL, [SP, #0x18]
    // 0x15de4b0: ldur            x16, [fp, #-0x10]
    // 0x15de4b4: r30 = Instance_RoundedRectangleBorder
    //     0x15de4b4: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec08] Obj!RoundedRectangleBorder@d5abe1
    //     0x15de4b8: ldr             lr, [lr, #0xc08]
    // 0x15de4bc: stp             lr, x16, [SP, #8]
    // 0x15de4c0: r16 = true
    //     0x15de4c0: add             x16, NULL, #0x20  ; true
    // 0x15de4c4: str             x16, [SP]
    // 0x15de4c8: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x15de4c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x15de4cc: ldr             x4, [x4, #0xd70]
    // 0x15de4d0: r0 = showModalBottomSheet()
    //     0x15de4d0: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x15de4d4: r0 = Null
    //     0x15de4d4: mov             x0, NULL
    // 0x15de4d8: LeaveFrame
    //     0x15de4d8: mov             SP, fp
    //     0x15de4dc: ldp             fp, lr, [SP], #0x10
    // 0x15de4e0: ret
    //     0x15de4e0: ret             
    // 0x15de4e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15de4e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15de4e8: b               #0x15de46c
  }
}
