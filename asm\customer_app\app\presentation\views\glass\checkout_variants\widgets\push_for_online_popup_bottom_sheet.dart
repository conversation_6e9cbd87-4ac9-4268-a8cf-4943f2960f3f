// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/push_for_online_popup_bottom_sheet.dart

// class id: 1049378, size: 0x8
class :: {
}

// class id: 3352, size: 0x14, field offset: 0x14
class _PartialCodPopupBottomSheet extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb52764, size: 0xdac
    // 0xb52764: EnterFrame
    //     0xb52764: stp             fp, lr, [SP, #-0x10]!
    //     0xb52768: mov             fp, SP
    // 0xb5276c: AllocStack(0x88)
    //     0xb5276c: sub             SP, SP, #0x88
    // 0xb52770: SetupParameters(_PartialCodPopupBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb52770: mov             x0, x1
    //     0xb52774: stur            x1, [fp, #-8]
    //     0xb52778: mov             x1, x2
    //     0xb5277c: stur            x2, [fp, #-0x10]
    // 0xb52780: CheckStackOverflow
    //     0xb52780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb52784: cmp             SP, x16
    //     0xb52788: b.ls            #0xb534d0
    // 0xb5278c: r1 = 1
    //     0xb5278c: movz            x1, #0x1
    // 0xb52790: r0 = AllocateContext()
    //     0xb52790: bl              #0x16f6108  ; AllocateContextStub
    // 0xb52794: mov             x1, x0
    // 0xb52798: ldur            x0, [fp, #-8]
    // 0xb5279c: stur            x1, [fp, #-0x18]
    // 0xb527a0: StoreField: r1->field_f = r0
    //     0xb527a0: stur            w0, [x1, #0xf]
    // 0xb527a4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb527a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb527a8: ldr             x0, [x0, #0x1c80]
    //     0xb527ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb527b0: cmp             w0, w16
    //     0xb527b4: b.ne            #0xb527c0
    //     0xb527b8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb527bc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb527c0: r0 = GetNavigation.size()
    //     0xb527c0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb527c4: LoadField: d0 = r0->field_f
    //     0xb527c4: ldur            d0, [x0, #0xf]
    // 0xb527c8: d1 = 0.300000
    //     0xb527c8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb527cc: ldr             d1, [x17, #0x658]
    // 0xb527d0: fmul            d2, d0, d1
    // 0xb527d4: ldur            x0, [fp, #-8]
    // 0xb527d8: stur            d2, [fp, #-0x70]
    // 0xb527dc: LoadField: r1 = r0->field_b
    //     0xb527dc: ldur            w1, [x0, #0xb]
    // 0xb527e0: DecompressPointer r1
    //     0xb527e0: add             x1, x1, HEAP, lsl #32
    // 0xb527e4: cmp             w1, NULL
    // 0xb527e8: b.eq            #0xb534d8
    // 0xb527ec: LoadField: r2 = r1->field_b
    //     0xb527ec: ldur            w2, [x1, #0xb]
    // 0xb527f0: DecompressPointer r2
    //     0xb527f0: add             x2, x2, HEAP, lsl #32
    // 0xb527f4: LoadField: r1 = r2->field_7
    //     0xb527f4: ldur            w1, [x2, #7]
    // 0xb527f8: DecompressPointer r1
    //     0xb527f8: add             x1, x1, HEAP, lsl #32
    // 0xb527fc: cmp             w1, NULL
    // 0xb52800: b.ne            #0xb5280c
    // 0xb52804: r2 = ""
    //     0xb52804: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb52808: b               #0xb52810
    // 0xb5280c: mov             x2, x1
    // 0xb52810: ldur            x1, [fp, #-0x10]
    // 0xb52814: stur            x2, [fp, #-0x20]
    // 0xb52818: r0 = of()
    //     0xb52818: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5281c: LoadField: r1 = r0->field_87
    //     0xb5281c: ldur            w1, [x0, #0x87]
    // 0xb52820: DecompressPointer r1
    //     0xb52820: add             x1, x1, HEAP, lsl #32
    // 0xb52824: LoadField: r0 = r1->field_7
    //     0xb52824: ldur            w0, [x1, #7]
    // 0xb52828: DecompressPointer r0
    //     0xb52828: add             x0, x0, HEAP, lsl #32
    // 0xb5282c: r16 = Instance_Color
    //     0xb5282c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb52830: r30 = 16.000000
    //     0xb52830: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb52834: ldr             lr, [lr, #0x188]
    // 0xb52838: stp             lr, x16, [SP]
    // 0xb5283c: mov             x1, x0
    // 0xb52840: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb52840: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb52844: ldr             x4, [x4, #0x9b8]
    // 0xb52848: r0 = copyWith()
    //     0xb52848: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5284c: stur            x0, [fp, #-0x28]
    // 0xb52850: r0 = Text()
    //     0xb52850: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb52854: mov             x2, x0
    // 0xb52858: ldur            x0, [fp, #-0x20]
    // 0xb5285c: stur            x2, [fp, #-0x30]
    // 0xb52860: StoreField: r2->field_b = r0
    //     0xb52860: stur            w0, [x2, #0xb]
    // 0xb52864: ldur            x0, [fp, #-0x28]
    // 0xb52868: StoreField: r2->field_13 = r0
    //     0xb52868: stur            w0, [x2, #0x13]
    // 0xb5286c: ldur            x0, [fp, #-8]
    // 0xb52870: LoadField: r1 = r0->field_b
    //     0xb52870: ldur            w1, [x0, #0xb]
    // 0xb52874: DecompressPointer r1
    //     0xb52874: add             x1, x1, HEAP, lsl #32
    // 0xb52878: cmp             w1, NULL
    // 0xb5287c: b.eq            #0xb534dc
    // 0xb52880: LoadField: r3 = r1->field_b
    //     0xb52880: ldur            w3, [x1, #0xb]
    // 0xb52884: DecompressPointer r3
    //     0xb52884: add             x3, x3, HEAP, lsl #32
    // 0xb52888: LoadField: r1 = r3->field_b
    //     0xb52888: ldur            w1, [x3, #0xb]
    // 0xb5288c: DecompressPointer r1
    //     0xb5288c: add             x1, x1, HEAP, lsl #32
    // 0xb52890: cmp             w1, NULL
    // 0xb52894: b.ne            #0xb528a0
    // 0xb52898: r3 = ""
    //     0xb52898: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5289c: b               #0xb528a4
    // 0xb528a0: mov             x3, x1
    // 0xb528a4: ldur            x1, [fp, #-0x10]
    // 0xb528a8: stur            x3, [fp, #-0x20]
    // 0xb528ac: r0 = of()
    //     0xb528ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb528b0: LoadField: r1 = r0->field_87
    //     0xb528b0: ldur            w1, [x0, #0x87]
    // 0xb528b4: DecompressPointer r1
    //     0xb528b4: add             x1, x1, HEAP, lsl #32
    // 0xb528b8: LoadField: r0 = r1->field_2f
    //     0xb528b8: ldur            w0, [x1, #0x2f]
    // 0xb528bc: DecompressPointer r0
    //     0xb528bc: add             x0, x0, HEAP, lsl #32
    // 0xb528c0: cmp             w0, NULL
    // 0xb528c4: b.ne            #0xb528d0
    // 0xb528c8: r3 = Null
    //     0xb528c8: mov             x3, NULL
    // 0xb528cc: b               #0xb528f4
    // 0xb528d0: r16 = 14.000000
    //     0xb528d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb528d4: ldr             x16, [x16, #0x1d8]
    // 0xb528d8: r30 = Instance_Color
    //     0xb528d8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb528dc: stp             lr, x16, [SP]
    // 0xb528e0: mov             x1, x0
    // 0xb528e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb528e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb528e8: ldr             x4, [x4, #0xaa0]
    // 0xb528ec: r0 = copyWith()
    //     0xb528ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb528f0: mov             x3, x0
    // 0xb528f4: ldur            x1, [fp, #-8]
    // 0xb528f8: ldur            d0, [fp, #-0x70]
    // 0xb528fc: ldur            x0, [fp, #-0x30]
    // 0xb52900: ldur            x2, [fp, #-0x20]
    // 0xb52904: stur            x3, [fp, #-0x28]
    // 0xb52908: r0 = Text()
    //     0xb52908: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5290c: mov             x1, x0
    // 0xb52910: ldur            x0, [fp, #-0x20]
    // 0xb52914: stur            x1, [fp, #-0x38]
    // 0xb52918: StoreField: r1->field_b = r0
    //     0xb52918: stur            w0, [x1, #0xb]
    // 0xb5291c: ldur            x0, [fp, #-0x28]
    // 0xb52920: StoreField: r1->field_13 = r0
    //     0xb52920: stur            w0, [x1, #0x13]
    // 0xb52924: r0 = Padding()
    //     0xb52924: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb52928: mov             x3, x0
    // 0xb5292c: r0 = Instance_EdgeInsets
    //     0xb5292c: add             x0, PP, #0x54, lsl #12  ; [pp+0x542d8] Obj!EdgeInsets@d58251
    //     0xb52930: ldr             x0, [x0, #0x2d8]
    // 0xb52934: stur            x3, [fp, #-0x20]
    // 0xb52938: StoreField: r3->field_f = r0
    //     0xb52938: stur            w0, [x3, #0xf]
    // 0xb5293c: ldur            x0, [fp, #-0x38]
    // 0xb52940: StoreField: r3->field_b = r0
    //     0xb52940: stur            w0, [x3, #0xb]
    // 0xb52944: r1 = Null
    //     0xb52944: mov             x1, NULL
    // 0xb52948: r2 = 4
    //     0xb52948: movz            x2, #0x4
    // 0xb5294c: r0 = AllocateArray()
    //     0xb5294c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb52950: stur            x0, [fp, #-0x28]
    // 0xb52954: r16 = Instance_Padding
    //     0xb52954: add             x16, PP, #0x54, lsl #12  ; [pp+0x542e0] Obj!Padding@d68481
    //     0xb52958: ldr             x16, [x16, #0x2e0]
    // 0xb5295c: StoreField: r0->field_f = r16
    //     0xb5295c: stur            w16, [x0, #0xf]
    // 0xb52960: ldur            x1, [fp, #-0x20]
    // 0xb52964: StoreField: r0->field_13 = r1
    //     0xb52964: stur            w1, [x0, #0x13]
    // 0xb52968: r1 = <Widget>
    //     0xb52968: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5296c: r0 = AllocateGrowableArray()
    //     0xb5296c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb52970: mov             x1, x0
    // 0xb52974: ldur            x0, [fp, #-0x28]
    // 0xb52978: stur            x1, [fp, #-0x20]
    // 0xb5297c: StoreField: r1->field_f = r0
    //     0xb5297c: stur            w0, [x1, #0xf]
    // 0xb52980: r2 = 4
    //     0xb52980: movz            x2, #0x4
    // 0xb52984: StoreField: r1->field_b = r2
    //     0xb52984: stur            w2, [x1, #0xb]
    // 0xb52988: r0 = Row()
    //     0xb52988: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5298c: mov             x1, x0
    // 0xb52990: r0 = Instance_Axis
    //     0xb52990: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb52994: stur            x1, [fp, #-0x28]
    // 0xb52998: StoreField: r1->field_f = r0
    //     0xb52998: stur            w0, [x1, #0xf]
    // 0xb5299c: r2 = Instance_MainAxisAlignment
    //     0xb5299c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb529a0: ldr             x2, [x2, #0xa08]
    // 0xb529a4: StoreField: r1->field_13 = r2
    //     0xb529a4: stur            w2, [x1, #0x13]
    // 0xb529a8: r2 = Instance_MainAxisSize
    //     0xb529a8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb529ac: ldr             x2, [x2, #0xdd0]
    // 0xb529b0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb529b0: stur            w2, [x1, #0x17]
    // 0xb529b4: r2 = Instance_CrossAxisAlignment
    //     0xb529b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb529b8: ldr             x2, [x2, #0xa18]
    // 0xb529bc: StoreField: r1->field_1b = r2
    //     0xb529bc: stur            w2, [x1, #0x1b]
    // 0xb529c0: r3 = Instance_VerticalDirection
    //     0xb529c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb529c4: ldr             x3, [x3, #0xa20]
    // 0xb529c8: StoreField: r1->field_23 = r3
    //     0xb529c8: stur            w3, [x1, #0x23]
    // 0xb529cc: r4 = Instance_Clip
    //     0xb529cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb529d0: ldr             x4, [x4, #0x38]
    // 0xb529d4: StoreField: r1->field_2b = r4
    //     0xb529d4: stur            w4, [x1, #0x2b]
    // 0xb529d8: StoreField: r1->field_2f = rZR
    //     0xb529d8: stur            xzr, [x1, #0x2f]
    // 0xb529dc: ldur            x5, [fp, #-0x20]
    // 0xb529e0: StoreField: r1->field_b = r5
    //     0xb529e0: stur            w5, [x1, #0xb]
    // 0xb529e4: r0 = Container()
    //     0xb529e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb529e8: stur            x0, [fp, #-0x20]
    // 0xb529ec: r16 = 55.000000
    //     0xb529ec: add             x16, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb529f0: ldr             x16, [x16, #0x9b8]
    // 0xb529f4: r30 = Instance_BoxDecoration
    //     0xb529f4: add             lr, PP, #0x56, lsl #12  ; [pp+0x56998] Obj!BoxDecoration@d64951
    //     0xb529f8: ldr             lr, [lr, #0x998]
    // 0xb529fc: stp             lr, x16, [SP, #8]
    // 0xb52a00: ldur            x16, [fp, #-0x28]
    // 0xb52a04: str             x16, [SP]
    // 0xb52a08: mov             x1, x0
    // 0xb52a0c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb52a0c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb52a10: ldr             x4, [x4, #0xc78]
    // 0xb52a14: r0 = Container()
    //     0xb52a14: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb52a18: r0 = SvgPicture()
    //     0xb52a18: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52a1c: stur            x0, [fp, #-0x28]
    // 0xb52a20: r16 = Instance_BoxFit
    //     0xb52a20: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52a24: ldr             x16, [x16, #0xb18]
    // 0xb52a28: str             x16, [SP]
    // 0xb52a2c: mov             x1, x0
    // 0xb52a30: r2 = "assets/images/icons/phonepe.svg"
    //     0xb52a30: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e00] "assets/images/icons/phonepe.svg"
    //     0xb52a34: ldr             x2, [x2, #0xe00]
    // 0xb52a38: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52a38: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52a3c: ldr             x4, [x4, #0xb0]
    // 0xb52a40: r0 = SvgPicture.asset()
    //     0xb52a40: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52a44: r0 = SvgPicture()
    //     0xb52a44: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52a48: stur            x0, [fp, #-0x38]
    // 0xb52a4c: r16 = Instance_BoxFit
    //     0xb52a4c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52a50: ldr             x16, [x16, #0xb18]
    // 0xb52a54: str             x16, [SP]
    // 0xb52a58: mov             x1, x0
    // 0xb52a5c: r2 = "assets/images/icons/google-pay.svg"
    //     0xb52a5c: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e08] "assets/images/icons/google-pay.svg"
    //     0xb52a60: ldr             x2, [x2, #0xe08]
    // 0xb52a64: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52a64: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52a68: ldr             x4, [x4, #0xb0]
    // 0xb52a6c: r0 = SvgPicture.asset()
    //     0xb52a6c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52a70: r0 = SvgPicture()
    //     0xb52a70: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52a74: stur            x0, [fp, #-0x40]
    // 0xb52a78: r16 = Instance_BoxFit
    //     0xb52a78: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52a7c: ldr             x16, [x16, #0xb18]
    // 0xb52a80: str             x16, [SP]
    // 0xb52a84: mov             x1, x0
    // 0xb52a88: r2 = "assets/images/icons/paytm.svg"
    //     0xb52a88: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e10] "assets/images/icons/paytm.svg"
    //     0xb52a8c: ldr             x2, [x2, #0xe10]
    // 0xb52a90: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52a90: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52a94: ldr             x4, [x4, #0xb0]
    // 0xb52a98: r0 = SvgPicture.asset()
    //     0xb52a98: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52a9c: r0 = SvgPicture()
    //     0xb52a9c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52aa0: stur            x0, [fp, #-0x48]
    // 0xb52aa4: r16 = Instance_BoxFit
    //     0xb52aa4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52aa8: ldr             x16, [x16, #0xb18]
    // 0xb52aac: str             x16, [SP]
    // 0xb52ab0: mov             x1, x0
    // 0xb52ab4: r2 = "assets/images/icons/upi.svg"
    //     0xb52ab4: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e18] "assets/images/icons/upi.svg"
    //     0xb52ab8: ldr             x2, [x2, #0xe18]
    // 0xb52abc: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52abc: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52ac0: ldr             x4, [x4, #0xb0]
    // 0xb52ac4: r0 = SvgPicture.asset()
    //     0xb52ac4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52ac8: r0 = SvgPicture()
    //     0xb52ac8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52acc: stur            x0, [fp, #-0x50]
    // 0xb52ad0: r16 = Instance_BoxFit
    //     0xb52ad0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52ad4: ldr             x16, [x16, #0xb18]
    // 0xb52ad8: str             x16, [SP]
    // 0xb52adc: mov             x1, x0
    // 0xb52ae0: r2 = "assets/images/icons/rupay.svg"
    //     0xb52ae0: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e20] "assets/images/icons/rupay.svg"
    //     0xb52ae4: ldr             x2, [x2, #0xe20]
    // 0xb52ae8: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52ae8: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52aec: ldr             x4, [x4, #0xb0]
    // 0xb52af0: r0 = SvgPicture.asset()
    //     0xb52af0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52af4: r0 = SvgPicture()
    //     0xb52af4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52af8: stur            x0, [fp, #-0x58]
    // 0xb52afc: r16 = Instance_BoxFit
    //     0xb52afc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52b00: ldr             x16, [x16, #0xb18]
    // 0xb52b04: str             x16, [SP]
    // 0xb52b08: mov             x1, x0
    // 0xb52b0c: r2 = "assets/images/icons/mastercard.svg"
    //     0xb52b0c: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e28] "assets/images/icons/mastercard.svg"
    //     0xb52b10: ldr             x2, [x2, #0xe28]
    // 0xb52b14: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52b14: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52b18: ldr             x4, [x4, #0xb0]
    // 0xb52b1c: r0 = SvgPicture.asset()
    //     0xb52b1c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52b20: r0 = SvgPicture()
    //     0xb52b20: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb52b24: stur            x0, [fp, #-0x60]
    // 0xb52b28: r16 = Instance_BoxFit
    //     0xb52b28: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb52b2c: ldr             x16, [x16, #0xb18]
    // 0xb52b30: str             x16, [SP]
    // 0xb52b34: mov             x1, x0
    // 0xb52b38: r2 = "assets/images/icons/visa.svg"
    //     0xb52b38: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e30] "assets/images/icons/visa.svg"
    //     0xb52b3c: ldr             x2, [x2, #0xe30]
    // 0xb52b40: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb52b40: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb52b44: ldr             x4, [x4, #0xb0]
    // 0xb52b48: r0 = SvgPicture.asset()
    //     0xb52b48: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb52b4c: r1 = Null
    //     0xb52b4c: mov             x1, NULL
    // 0xb52b50: r2 = 14
    //     0xb52b50: movz            x2, #0xe
    // 0xb52b54: r0 = AllocateArray()
    //     0xb52b54: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb52b58: mov             x2, x0
    // 0xb52b5c: ldur            x0, [fp, #-0x28]
    // 0xb52b60: stur            x2, [fp, #-0x68]
    // 0xb52b64: StoreField: r2->field_f = r0
    //     0xb52b64: stur            w0, [x2, #0xf]
    // 0xb52b68: ldur            x0, [fp, #-0x38]
    // 0xb52b6c: StoreField: r2->field_13 = r0
    //     0xb52b6c: stur            w0, [x2, #0x13]
    // 0xb52b70: ldur            x0, [fp, #-0x40]
    // 0xb52b74: ArrayStore: r2[0] = r0  ; List_4
    //     0xb52b74: stur            w0, [x2, #0x17]
    // 0xb52b78: ldur            x0, [fp, #-0x48]
    // 0xb52b7c: StoreField: r2->field_1b = r0
    //     0xb52b7c: stur            w0, [x2, #0x1b]
    // 0xb52b80: ldur            x0, [fp, #-0x50]
    // 0xb52b84: StoreField: r2->field_1f = r0
    //     0xb52b84: stur            w0, [x2, #0x1f]
    // 0xb52b88: ldur            x0, [fp, #-0x58]
    // 0xb52b8c: StoreField: r2->field_23 = r0
    //     0xb52b8c: stur            w0, [x2, #0x23]
    // 0xb52b90: ldur            x0, [fp, #-0x60]
    // 0xb52b94: StoreField: r2->field_27 = r0
    //     0xb52b94: stur            w0, [x2, #0x27]
    // 0xb52b98: r1 = <Widget>
    //     0xb52b98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb52b9c: r0 = AllocateGrowableArray()
    //     0xb52b9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb52ba0: mov             x1, x0
    // 0xb52ba4: ldur            x0, [fp, #-0x68]
    // 0xb52ba8: stur            x1, [fp, #-0x28]
    // 0xb52bac: StoreField: r1->field_f = r0
    //     0xb52bac: stur            w0, [x1, #0xf]
    // 0xb52bb0: r0 = 14
    //     0xb52bb0: movz            x0, #0xe
    // 0xb52bb4: StoreField: r1->field_b = r0
    //     0xb52bb4: stur            w0, [x1, #0xb]
    // 0xb52bb8: r0 = Row()
    //     0xb52bb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb52bbc: mov             x1, x0
    // 0xb52bc0: r0 = Instance_Axis
    //     0xb52bc0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb52bc4: stur            x1, [fp, #-0x38]
    // 0xb52bc8: StoreField: r1->field_f = r0
    //     0xb52bc8: stur            w0, [x1, #0xf]
    // 0xb52bcc: r2 = Instance_MainAxisAlignment
    //     0xb52bcc: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f28] Obj!MainAxisAlignment@d734a1
    //     0xb52bd0: ldr             x2, [x2, #0xf28]
    // 0xb52bd4: StoreField: r1->field_13 = r2
    //     0xb52bd4: stur            w2, [x1, #0x13]
    // 0xb52bd8: r2 = Instance_MainAxisSize
    //     0xb52bd8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb52bdc: ldr             x2, [x2, #0xa10]
    // 0xb52be0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb52be0: stur            w2, [x1, #0x17]
    // 0xb52be4: r3 = Instance_CrossAxisAlignment
    //     0xb52be4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb52be8: ldr             x3, [x3, #0xa18]
    // 0xb52bec: StoreField: r1->field_1b = r3
    //     0xb52bec: stur            w3, [x1, #0x1b]
    // 0xb52bf0: r4 = Instance_VerticalDirection
    //     0xb52bf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb52bf4: ldr             x4, [x4, #0xa20]
    // 0xb52bf8: StoreField: r1->field_23 = r4
    //     0xb52bf8: stur            w4, [x1, #0x23]
    // 0xb52bfc: r5 = Instance_Clip
    //     0xb52bfc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb52c00: ldr             x5, [x5, #0x38]
    // 0xb52c04: StoreField: r1->field_2b = r5
    //     0xb52c04: stur            w5, [x1, #0x2b]
    // 0xb52c08: StoreField: r1->field_2f = rZR
    //     0xb52c08: stur            xzr, [x1, #0x2f]
    // 0xb52c0c: ldur            x6, [fp, #-0x28]
    // 0xb52c10: StoreField: r1->field_b = r6
    //     0xb52c10: stur            w6, [x1, #0xb]
    // 0xb52c14: r0 = Padding()
    //     0xb52c14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb52c18: mov             x2, x0
    // 0xb52c1c: r0 = Instance_EdgeInsets
    //     0xb52c1c: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3d1c0] Obj!EdgeInsets@d57471
    //     0xb52c20: ldr             x0, [x0, #0x1c0]
    // 0xb52c24: stur            x2, [fp, #-0x28]
    // 0xb52c28: StoreField: r2->field_f = r0
    //     0xb52c28: stur            w0, [x2, #0xf]
    // 0xb52c2c: ldur            x0, [fp, #-0x38]
    // 0xb52c30: StoreField: r2->field_b = r0
    //     0xb52c30: stur            w0, [x2, #0xb]
    // 0xb52c34: r1 = Instance_Color
    //     0xb52c34: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb52c38: d0 = 0.100000
    //     0xb52c38: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb52c3c: r0 = withOpacity()
    //     0xb52c3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb52c40: stur            x0, [fp, #-0x38]
    // 0xb52c44: r0 = Divider()
    //     0xb52c44: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb52c48: mov             x3, x0
    // 0xb52c4c: ldur            x0, [fp, #-0x38]
    // 0xb52c50: stur            x3, [fp, #-0x40]
    // 0xb52c54: StoreField: r3->field_1f = r0
    //     0xb52c54: stur            w0, [x3, #0x1f]
    // 0xb52c58: r1 = Null
    //     0xb52c58: mov             x1, NULL
    // 0xb52c5c: r2 = 10
    //     0xb52c5c: movz            x2, #0xa
    // 0xb52c60: r0 = AllocateArray()
    //     0xb52c60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb52c64: mov             x2, x0
    // 0xb52c68: ldur            x0, [fp, #-0x30]
    // 0xb52c6c: stur            x2, [fp, #-0x38]
    // 0xb52c70: StoreField: r2->field_f = r0
    //     0xb52c70: stur            w0, [x2, #0xf]
    // 0xb52c74: r16 = Instance_SizedBox
    //     0xb52c74: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb52c78: ldr             x16, [x16, #0x578]
    // 0xb52c7c: StoreField: r2->field_13 = r16
    //     0xb52c7c: stur            w16, [x2, #0x13]
    // 0xb52c80: ldur            x0, [fp, #-0x20]
    // 0xb52c84: ArrayStore: r2[0] = r0  ; List_4
    //     0xb52c84: stur            w0, [x2, #0x17]
    // 0xb52c88: ldur            x0, [fp, #-0x28]
    // 0xb52c8c: StoreField: r2->field_1b = r0
    //     0xb52c8c: stur            w0, [x2, #0x1b]
    // 0xb52c90: ldur            x0, [fp, #-0x40]
    // 0xb52c94: StoreField: r2->field_1f = r0
    //     0xb52c94: stur            w0, [x2, #0x1f]
    // 0xb52c98: r1 = <Widget>
    //     0xb52c98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb52c9c: r0 = AllocateGrowableArray()
    //     0xb52c9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb52ca0: mov             x1, x0
    // 0xb52ca4: ldur            x0, [fp, #-0x38]
    // 0xb52ca8: stur            x1, [fp, #-0x20]
    // 0xb52cac: StoreField: r1->field_f = r0
    //     0xb52cac: stur            w0, [x1, #0xf]
    // 0xb52cb0: r0 = 10
    //     0xb52cb0: movz            x0, #0xa
    // 0xb52cb4: StoreField: r1->field_b = r0
    //     0xb52cb4: stur            w0, [x1, #0xb]
    // 0xb52cb8: r0 = ListView()
    //     0xb52cb8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb52cbc: stur            x0, [fp, #-0x28]
    // 0xb52cc0: r16 = Instance_BouncingScrollPhysics
    //     0xb52cc0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb52cc4: ldr             x16, [x16, #0x890]
    // 0xb52cc8: str             x16, [SP]
    // 0xb52ccc: mov             x1, x0
    // 0xb52cd0: ldur            x2, [fp, #-0x20]
    // 0xb52cd4: r4 = const [0, 0x3, 0x1, 0x2, physics, 0x2, null]
    //     0xb52cd4: add             x4, PP, #0x54, lsl #12  ; [pp+0x542e8] List(7) [0, 0x3, 0x1, 0x2, "physics", 0x2, Null]
    //     0xb52cd8: ldr             x4, [x4, #0x2e8]
    // 0xb52cdc: r0 = ListView()
    //     0xb52cdc: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb52ce0: ldur            d0, [fp, #-0x70]
    // 0xb52ce4: r0 = inline_Allocate_Double()
    //     0xb52ce4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb52ce8: add             x0, x0, #0x10
    //     0xb52cec: cmp             x1, x0
    //     0xb52cf0: b.ls            #0xb534e0
    //     0xb52cf4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb52cf8: sub             x0, x0, #0xf
    //     0xb52cfc: movz            x1, #0xe15c
    //     0xb52d00: movk            x1, #0x3, lsl #16
    //     0xb52d04: stur            x1, [x0, #-1]
    // 0xb52d08: StoreField: r0->field_7 = d0
    //     0xb52d08: stur            d0, [x0, #7]
    // 0xb52d0c: stur            x0, [fp, #-0x20]
    // 0xb52d10: r0 = SizedBox()
    //     0xb52d10: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb52d14: mov             x1, x0
    // 0xb52d18: ldur            x0, [fp, #-0x20]
    // 0xb52d1c: stur            x1, [fp, #-0x30]
    // 0xb52d20: StoreField: r1->field_13 = r0
    //     0xb52d20: stur            w0, [x1, #0x13]
    // 0xb52d24: ldur            x0, [fp, #-0x28]
    // 0xb52d28: StoreField: r1->field_b = r0
    //     0xb52d28: stur            w0, [x1, #0xb]
    // 0xb52d2c: r0 = Padding()
    //     0xb52d2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb52d30: mov             x1, x0
    // 0xb52d34: r0 = Instance_EdgeInsets
    //     0xb52d34: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b08] Obj!EdgeInsets@d570b1
    //     0xb52d38: ldr             x0, [x0, #0xb08]
    // 0xb52d3c: stur            x1, [fp, #-0x20]
    // 0xb52d40: StoreField: r1->field_f = r0
    //     0xb52d40: stur            w0, [x1, #0xf]
    // 0xb52d44: ldur            x0, [fp, #-0x30]
    // 0xb52d48: StoreField: r1->field_b = r0
    //     0xb52d48: stur            w0, [x1, #0xb]
    // 0xb52d4c: r0 = GetNavigation.size()
    //     0xb52d4c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb52d50: LoadField: d0 = r0->field_7
    //     0xb52d50: ldur            d0, [x0, #7]
    // 0xb52d54: d1 = 0.940000
    //     0xb52d54: add             x17, PP, #0x54, lsl #12  ; [pp+0x542f0] IMM: double(0.94) from 0x3fee147ae147ae14
    //     0xb52d58: ldr             d1, [x17, #0x2f0]
    // 0xb52d5c: fmul            d2, d0, d1
    // 0xb52d60: stur            d2, [fp, #-0x70]
    // 0xb52d64: r16 = <EdgeInsets>
    //     0xb52d64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb52d68: ldr             x16, [x16, #0xda0]
    // 0xb52d6c: r30 = Instance_EdgeInsets
    //     0xb52d6c: add             lr, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xb52d70: ldr             lr, [lr, #0x50]
    // 0xb52d74: stp             lr, x16, [SP]
    // 0xb52d78: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb52d78: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb52d7c: r0 = all()
    //     0xb52d7c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb52d80: stur            x0, [fp, #-0x28]
    // 0xb52d84: r16 = <Color>
    //     0xb52d84: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb52d88: ldr             x16, [x16, #0xf80]
    // 0xb52d8c: r30 = Instance_Color
    //     0xb52d8c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb52d90: stp             lr, x16, [SP]
    // 0xb52d94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb52d94: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb52d98: r0 = all()
    //     0xb52d98: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb52d9c: ldur            x1, [fp, #-0x10]
    // 0xb52da0: stur            x0, [fp, #-0x30]
    // 0xb52da4: r0 = of()
    //     0xb52da4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb52da8: LoadField: r1 = r0->field_5b
    //     0xb52da8: ldur            w1, [x0, #0x5b]
    // 0xb52dac: DecompressPointer r1
    //     0xb52dac: add             x1, x1, HEAP, lsl #32
    // 0xb52db0: r0 = LoadClassIdInstr(r1)
    //     0xb52db0: ldur            x0, [x1, #-1]
    //     0xb52db4: ubfx            x0, x0, #0xc, #0x14
    // 0xb52db8: d0 = 0.500000
    //     0xb52db8: fmov            d0, #0.50000000
    // 0xb52dbc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb52dbc: sub             lr, x0, #0xffa
    //     0xb52dc0: ldr             lr, [x21, lr, lsl #3]
    //     0xb52dc4: blr             lr
    // 0xb52dc8: stur            x0, [fp, #-0x38]
    // 0xb52dcc: r0 = BorderSide()
    //     0xb52dcc: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb52dd0: mov             x1, x0
    // 0xb52dd4: ldur            x0, [fp, #-0x38]
    // 0xb52dd8: stur            x1, [fp, #-0x40]
    // 0xb52ddc: StoreField: r1->field_7 = r0
    //     0xb52ddc: stur            w0, [x1, #7]
    // 0xb52de0: d0 = 1.000000
    //     0xb52de0: fmov            d0, #1.00000000
    // 0xb52de4: StoreField: r1->field_b = d0
    //     0xb52de4: stur            d0, [x1, #0xb]
    // 0xb52de8: r0 = Instance_BorderStyle
    //     0xb52de8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb52dec: ldr             x0, [x0, #0xf68]
    // 0xb52df0: StoreField: r1->field_13 = r0
    //     0xb52df0: stur            w0, [x1, #0x13]
    // 0xb52df4: d1 = -1.000000
    //     0xb52df4: fmov            d1, #-1.00000000
    // 0xb52df8: ArrayStore: r1[0] = d1  ; List_8
    //     0xb52df8: stur            d1, [x1, #0x17]
    // 0xb52dfc: r0 = RoundedRectangleBorder()
    //     0xb52dfc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb52e00: mov             x1, x0
    // 0xb52e04: r0 = Instance_BorderRadius
    //     0xb52e04: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fdc0] Obj!BorderRadius@d5a301
    //     0xb52e08: ldr             x0, [x0, #0xdc0]
    // 0xb52e0c: StoreField: r1->field_b = r0
    //     0xb52e0c: stur            w0, [x1, #0xb]
    // 0xb52e10: ldur            x2, [fp, #-0x40]
    // 0xb52e14: StoreField: r1->field_7 = r2
    //     0xb52e14: stur            w2, [x1, #7]
    // 0xb52e18: r16 = <RoundedRectangleBorder>
    //     0xb52e18: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb52e1c: ldr             x16, [x16, #0xf78]
    // 0xb52e20: stp             x1, x16, [SP]
    // 0xb52e24: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb52e24: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb52e28: r0 = all()
    //     0xb52e28: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb52e2c: stur            x0, [fp, #-0x38]
    // 0xb52e30: r0 = ButtonStyle()
    //     0xb52e30: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb52e34: mov             x1, x0
    // 0xb52e38: ldur            x0, [fp, #-0x30]
    // 0xb52e3c: stur            x1, [fp, #-0x40]
    // 0xb52e40: StoreField: r1->field_b = r0
    //     0xb52e40: stur            w0, [x1, #0xb]
    // 0xb52e44: ldur            x0, [fp, #-0x28]
    // 0xb52e48: StoreField: r1->field_23 = r0
    //     0xb52e48: stur            w0, [x1, #0x23]
    // 0xb52e4c: ldur            x0, [fp, #-0x38]
    // 0xb52e50: StoreField: r1->field_43 = r0
    //     0xb52e50: stur            w0, [x1, #0x43]
    // 0xb52e54: r0 = TextButtonThemeData()
    //     0xb52e54: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb52e58: mov             x2, x0
    // 0xb52e5c: ldur            x0, [fp, #-0x40]
    // 0xb52e60: stur            x2, [fp, #-0x28]
    // 0xb52e64: StoreField: r2->field_7 = r0
    //     0xb52e64: stur            w0, [x2, #7]
    // 0xb52e68: r1 = "not now"
    //     0xb52e68: add             x1, PP, #0x56, lsl #12  ; [pp+0x569a0] "not now"
    //     0xb52e6c: ldr             x1, [x1, #0x9a0]
    // 0xb52e70: r0 = capitalizeFirstWord()
    //     0xb52e70: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb52e74: ldur            x1, [fp, #-0x10]
    // 0xb52e78: stur            x0, [fp, #-0x30]
    // 0xb52e7c: r0 = of()
    //     0xb52e7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb52e80: LoadField: r1 = r0->field_87
    //     0xb52e80: ldur            w1, [x0, #0x87]
    // 0xb52e84: DecompressPointer r1
    //     0xb52e84: add             x1, x1, HEAP, lsl #32
    // 0xb52e88: LoadField: r0 = r1->field_7
    //     0xb52e88: ldur            w0, [x1, #7]
    // 0xb52e8c: DecompressPointer r0
    //     0xb52e8c: add             x0, x0, HEAP, lsl #32
    // 0xb52e90: ldur            x1, [fp, #-0x10]
    // 0xb52e94: stur            x0, [fp, #-0x38]
    // 0xb52e98: r0 = of()
    //     0xb52e98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb52e9c: LoadField: r1 = r0->field_5b
    //     0xb52e9c: ldur            w1, [x0, #0x5b]
    // 0xb52ea0: DecompressPointer r1
    //     0xb52ea0: add             x1, x1, HEAP, lsl #32
    // 0xb52ea4: r16 = 16.000000
    //     0xb52ea4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb52ea8: ldr             x16, [x16, #0x188]
    // 0xb52eac: stp             x1, x16, [SP]
    // 0xb52eb0: ldur            x1, [fp, #-0x38]
    // 0xb52eb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb52eb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb52eb8: ldr             x4, [x4, #0xaa0]
    // 0xb52ebc: r0 = copyWith()
    //     0xb52ebc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb52ec0: stur            x0, [fp, #-0x38]
    // 0xb52ec4: r0 = Text()
    //     0xb52ec4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb52ec8: mov             x3, x0
    // 0xb52ecc: ldur            x0, [fp, #-0x30]
    // 0xb52ed0: stur            x3, [fp, #-0x40]
    // 0xb52ed4: StoreField: r3->field_b = r0
    //     0xb52ed4: stur            w0, [x3, #0xb]
    // 0xb52ed8: ldur            x0, [fp, #-0x38]
    // 0xb52edc: StoreField: r3->field_13 = r0
    //     0xb52edc: stur            w0, [x3, #0x13]
    // 0xb52ee0: ldur            x2, [fp, #-0x18]
    // 0xb52ee4: r1 = Function '<anonymous closure>':.
    //     0xb52ee4: add             x1, PP, #0x56, lsl #12  ; [pp+0x569a8] AnonymousClosure: (0xb535dc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/push_for_online_popup_bottom_sheet.dart] _PartialCodPopupBottomSheet::build (0xb52764)
    //     0xb52ee8: ldr             x1, [x1, #0x9a8]
    // 0xb52eec: r0 = AllocateClosure()
    //     0xb52eec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb52ef0: stur            x0, [fp, #-0x30]
    // 0xb52ef4: r0 = TextButton()
    //     0xb52ef4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb52ef8: mov             x1, x0
    // 0xb52efc: ldur            x0, [fp, #-0x30]
    // 0xb52f00: stur            x1, [fp, #-0x38]
    // 0xb52f04: StoreField: r1->field_b = r0
    //     0xb52f04: stur            w0, [x1, #0xb]
    // 0xb52f08: r0 = false
    //     0xb52f08: add             x0, NULL, #0x30  ; false
    // 0xb52f0c: StoreField: r1->field_27 = r0
    //     0xb52f0c: stur            w0, [x1, #0x27]
    // 0xb52f10: r2 = true
    //     0xb52f10: add             x2, NULL, #0x20  ; true
    // 0xb52f14: StoreField: r1->field_2f = r2
    //     0xb52f14: stur            w2, [x1, #0x2f]
    // 0xb52f18: ldur            x3, [fp, #-0x40]
    // 0xb52f1c: StoreField: r1->field_37 = r3
    //     0xb52f1c: stur            w3, [x1, #0x37]
    // 0xb52f20: r0 = TextButtonTheme()
    //     0xb52f20: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb52f24: mov             x2, x0
    // 0xb52f28: ldur            x0, [fp, #-0x28]
    // 0xb52f2c: stur            x2, [fp, #-0x30]
    // 0xb52f30: StoreField: r2->field_f = r0
    //     0xb52f30: stur            w0, [x2, #0xf]
    // 0xb52f34: ldur            x0, [fp, #-0x38]
    // 0xb52f38: StoreField: r2->field_b = r0
    //     0xb52f38: stur            w0, [x2, #0xb]
    // 0xb52f3c: r1 = <FlexParentData>
    //     0xb52f3c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb52f40: ldr             x1, [x1, #0xe00]
    // 0xb52f44: r0 = Expanded()
    //     0xb52f44: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb52f48: mov             x1, x0
    // 0xb52f4c: r0 = 1
    //     0xb52f4c: movz            x0, #0x1
    // 0xb52f50: stur            x1, [fp, #-0x28]
    // 0xb52f54: StoreField: r1->field_13 = r0
    //     0xb52f54: stur            x0, [x1, #0x13]
    // 0xb52f58: r0 = Instance_FlexFit
    //     0xb52f58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb52f5c: ldr             x0, [x0, #0xe08]
    // 0xb52f60: StoreField: r1->field_1b = r0
    //     0xb52f60: stur            w0, [x1, #0x1b]
    // 0xb52f64: ldur            x2, [fp, #-0x30]
    // 0xb52f68: StoreField: r1->field_b = r2
    //     0xb52f68: stur            w2, [x1, #0xb]
    // 0xb52f6c: r16 = <EdgeInsets>
    //     0xb52f6c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb52f70: ldr             x16, [x16, #0xda0]
    // 0xb52f74: r30 = Instance_EdgeInsets
    //     0xb52f74: add             lr, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xb52f78: ldr             lr, [lr, #0x50]
    // 0xb52f7c: stp             lr, x16, [SP]
    // 0xb52f80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb52f80: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb52f84: r0 = all()
    //     0xb52f84: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb52f88: ldur            x1, [fp, #-0x10]
    // 0xb52f8c: stur            x0, [fp, #-0x30]
    // 0xb52f90: r0 = of()
    //     0xb52f90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb52f94: LoadField: r1 = r0->field_5b
    //     0xb52f94: ldur            w1, [x0, #0x5b]
    // 0xb52f98: DecompressPointer r1
    //     0xb52f98: add             x1, x1, HEAP, lsl #32
    // 0xb52f9c: r16 = <Color>
    //     0xb52f9c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb52fa0: ldr             x16, [x16, #0xf80]
    // 0xb52fa4: stp             x1, x16, [SP]
    // 0xb52fa8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb52fa8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb52fac: r0 = all()
    //     0xb52fac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb52fb0: ldur            x1, [fp, #-0x10]
    // 0xb52fb4: stur            x0, [fp, #-0x38]
    // 0xb52fb8: r0 = of()
    //     0xb52fb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb52fbc: LoadField: r1 = r0->field_5b
    //     0xb52fbc: ldur            w1, [x0, #0x5b]
    // 0xb52fc0: DecompressPointer r1
    //     0xb52fc0: add             x1, x1, HEAP, lsl #32
    // 0xb52fc4: stur            x1, [fp, #-0x40]
    // 0xb52fc8: r0 = BorderSide()
    //     0xb52fc8: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb52fcc: mov             x1, x0
    // 0xb52fd0: ldur            x0, [fp, #-0x40]
    // 0xb52fd4: stur            x1, [fp, #-0x48]
    // 0xb52fd8: StoreField: r1->field_7 = r0
    //     0xb52fd8: stur            w0, [x1, #7]
    // 0xb52fdc: d0 = 1.000000
    //     0xb52fdc: fmov            d0, #1.00000000
    // 0xb52fe0: StoreField: r1->field_b = d0
    //     0xb52fe0: stur            d0, [x1, #0xb]
    // 0xb52fe4: r0 = Instance_BorderStyle
    //     0xb52fe4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb52fe8: ldr             x0, [x0, #0xf68]
    // 0xb52fec: StoreField: r1->field_13 = r0
    //     0xb52fec: stur            w0, [x1, #0x13]
    // 0xb52ff0: d0 = -1.000000
    //     0xb52ff0: fmov            d0, #-1.00000000
    // 0xb52ff4: ArrayStore: r1[0] = d0  ; List_8
    //     0xb52ff4: stur            d0, [x1, #0x17]
    // 0xb52ff8: r0 = RoundedRectangleBorder()
    //     0xb52ff8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb52ffc: mov             x1, x0
    // 0xb53000: r0 = Instance_BorderRadius
    //     0xb53000: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fdc0] Obj!BorderRadius@d5a301
    //     0xb53004: ldr             x0, [x0, #0xdc0]
    // 0xb53008: StoreField: r1->field_b = r0
    //     0xb53008: stur            w0, [x1, #0xb]
    // 0xb5300c: ldur            x0, [fp, #-0x48]
    // 0xb53010: StoreField: r1->field_7 = r0
    //     0xb53010: stur            w0, [x1, #7]
    // 0xb53014: r16 = <RoundedRectangleBorder>
    //     0xb53014: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb53018: ldr             x16, [x16, #0xf78]
    // 0xb5301c: stp             x1, x16, [SP]
    // 0xb53020: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb53020: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb53024: r0 = all()
    //     0xb53024: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb53028: stur            x0, [fp, #-0x40]
    // 0xb5302c: r0 = ButtonStyle()
    //     0xb5302c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb53030: mov             x1, x0
    // 0xb53034: ldur            x0, [fp, #-0x38]
    // 0xb53038: stur            x1, [fp, #-0x48]
    // 0xb5303c: StoreField: r1->field_b = r0
    //     0xb5303c: stur            w0, [x1, #0xb]
    // 0xb53040: ldur            x0, [fp, #-0x30]
    // 0xb53044: StoreField: r1->field_23 = r0
    //     0xb53044: stur            w0, [x1, #0x23]
    // 0xb53048: ldur            x0, [fp, #-0x40]
    // 0xb5304c: StoreField: r1->field_43 = r0
    //     0xb5304c: stur            w0, [x1, #0x43]
    // 0xb53050: r0 = TextButtonThemeData()
    //     0xb53050: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb53054: mov             x3, x0
    // 0xb53058: ldur            x0, [fp, #-0x48]
    // 0xb5305c: stur            x3, [fp, #-0x30]
    // 0xb53060: StoreField: r3->field_7 = r0
    //     0xb53060: stur            w0, [x3, #7]
    // 0xb53064: r1 = Null
    //     0xb53064: mov             x1, NULL
    // 0xb53068: r2 = 6
    //     0xb53068: movz            x2, #0x6
    // 0xb5306c: r0 = AllocateArray()
    //     0xb5306c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb53070: r16 = "Pay Only "
    //     0xb53070: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca78] "Pay Only "
    //     0xb53074: ldr             x16, [x16, #0xa78]
    // 0xb53078: StoreField: r0->field_f = r16
    //     0xb53078: stur            w16, [x0, #0xf]
    // 0xb5307c: ldur            x1, [fp, #-8]
    // 0xb53080: LoadField: r2 = r1->field_b
    //     0xb53080: ldur            w2, [x1, #0xb]
    // 0xb53084: DecompressPointer r2
    //     0xb53084: add             x2, x2, HEAP, lsl #32
    // 0xb53088: cmp             w2, NULL
    // 0xb5308c: b.eq            #0xb534f0
    // 0xb53090: LoadField: r3 = r2->field_b
    //     0xb53090: ldur            w3, [x2, #0xb]
    // 0xb53094: DecompressPointer r3
    //     0xb53094: add             x3, x3, HEAP, lsl #32
    // 0xb53098: LoadField: r2 = r3->field_1b
    //     0xb53098: ldur            w2, [x3, #0x1b]
    // 0xb5309c: DecompressPointer r2
    //     0xb5309c: add             x2, x2, HEAP, lsl #32
    // 0xb530a0: StoreField: r0->field_13 = r2
    //     0xb530a0: stur            w2, [x0, #0x13]
    // 0xb530a4: r16 = " "
    //     0xb530a4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb530a8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb530a8: stur            w16, [x0, #0x17]
    // 0xb530ac: str             x0, [SP]
    // 0xb530b0: r0 = _interpolate()
    //     0xb530b0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb530b4: ldur            x1, [fp, #-0x10]
    // 0xb530b8: stur            x0, [fp, #-0x38]
    // 0xb530bc: r0 = of()
    //     0xb530bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb530c0: LoadField: r1 = r0->field_87
    //     0xb530c0: ldur            w1, [x0, #0x87]
    // 0xb530c4: DecompressPointer r1
    //     0xb530c4: add             x1, x1, HEAP, lsl #32
    // 0xb530c8: LoadField: r0 = r1->field_7
    //     0xb530c8: ldur            w0, [x1, #7]
    // 0xb530cc: DecompressPointer r0
    //     0xb530cc: add             x0, x0, HEAP, lsl #32
    // 0xb530d0: r16 = 16.000000
    //     0xb530d0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb530d4: ldr             x16, [x16, #0x188]
    // 0xb530d8: r30 = Instance_Color
    //     0xb530d8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb530dc: stp             lr, x16, [SP]
    // 0xb530e0: mov             x1, x0
    // 0xb530e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb530e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb530e8: ldr             x4, [x4, #0xaa0]
    // 0xb530ec: r0 = copyWith()
    //     0xb530ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb530f0: stur            x0, [fp, #-0x40]
    // 0xb530f4: r0 = Text()
    //     0xb530f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb530f8: mov             x1, x0
    // 0xb530fc: ldur            x0, [fp, #-0x38]
    // 0xb53100: stur            x1, [fp, #-0x48]
    // 0xb53104: StoreField: r1->field_b = r0
    //     0xb53104: stur            w0, [x1, #0xb]
    // 0xb53108: ldur            x0, [fp, #-0x40]
    // 0xb5310c: StoreField: r1->field_13 = r0
    //     0xb5310c: stur            w0, [x1, #0x13]
    // 0xb53110: ldur            x0, [fp, #-8]
    // 0xb53114: LoadField: r2 = r0->field_b
    //     0xb53114: ldur            w2, [x0, #0xb]
    // 0xb53118: DecompressPointer r2
    //     0xb53118: add             x2, x2, HEAP, lsl #32
    // 0xb5311c: cmp             w2, NULL
    // 0xb53120: b.eq            #0xb534f4
    // 0xb53124: LoadField: r0 = r2->field_b
    //     0xb53124: ldur            w0, [x2, #0xb]
    // 0xb53128: DecompressPointer r0
    //     0xb53128: add             x0, x0, HEAP, lsl #32
    // 0xb5312c: LoadField: r2 = r0->field_13
    //     0xb5312c: ldur            w2, [x0, #0x13]
    // 0xb53130: DecompressPointer r2
    //     0xb53130: add             x2, x2, HEAP, lsl #32
    // 0xb53134: str             x2, [SP]
    // 0xb53138: r0 = _interpolateSingle()
    //     0xb53138: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb5313c: ldur            x1, [fp, #-0x10]
    // 0xb53140: stur            x0, [fp, #-8]
    // 0xb53144: r0 = of()
    //     0xb53144: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb53148: LoadField: r1 = r0->field_87
    //     0xb53148: ldur            w1, [x0, #0x87]
    // 0xb5314c: DecompressPointer r1
    //     0xb5314c: add             x1, x1, HEAP, lsl #32
    // 0xb53150: LoadField: r0 = r1->field_7
    //     0xb53150: ldur            w0, [x1, #7]
    // 0xb53154: DecompressPointer r0
    //     0xb53154: add             x0, x0, HEAP, lsl #32
    // 0xb53158: r16 = 16.000000
    //     0xb53158: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5315c: ldr             x16, [x16, #0x188]
    // 0xb53160: r30 = Instance_Color
    //     0xb53160: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb53164: stp             lr, x16, [SP, #8]
    // 0xb53168: r16 = Instance_TextDecoration
    //     0xb53168: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb5316c: ldr             x16, [x16, #0xe30]
    // 0xb53170: str             x16, [SP]
    // 0xb53174: mov             x1, x0
    // 0xb53178: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb53178: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb5317c: ldr             x4, [x4, #0xe38]
    // 0xb53180: r0 = copyWith()
    //     0xb53180: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb53184: stur            x0, [fp, #-0x10]
    // 0xb53188: r0 = Text()
    //     0xb53188: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5318c: mov             x3, x0
    // 0xb53190: ldur            x0, [fp, #-8]
    // 0xb53194: stur            x3, [fp, #-0x38]
    // 0xb53198: StoreField: r3->field_b = r0
    //     0xb53198: stur            w0, [x3, #0xb]
    // 0xb5319c: ldur            x0, [fp, #-0x10]
    // 0xb531a0: StoreField: r3->field_13 = r0
    //     0xb531a0: stur            w0, [x3, #0x13]
    // 0xb531a4: r1 = Null
    //     0xb531a4: mov             x1, NULL
    // 0xb531a8: r2 = 4
    //     0xb531a8: movz            x2, #0x4
    // 0xb531ac: r0 = AllocateArray()
    //     0xb531ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb531b0: mov             x2, x0
    // 0xb531b4: ldur            x0, [fp, #-0x48]
    // 0xb531b8: stur            x2, [fp, #-8]
    // 0xb531bc: StoreField: r2->field_f = r0
    //     0xb531bc: stur            w0, [x2, #0xf]
    // 0xb531c0: ldur            x0, [fp, #-0x38]
    // 0xb531c4: StoreField: r2->field_13 = r0
    //     0xb531c4: stur            w0, [x2, #0x13]
    // 0xb531c8: r1 = <Widget>
    //     0xb531c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb531cc: r0 = AllocateGrowableArray()
    //     0xb531cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb531d0: mov             x1, x0
    // 0xb531d4: ldur            x0, [fp, #-8]
    // 0xb531d8: stur            x1, [fp, #-0x10]
    // 0xb531dc: StoreField: r1->field_f = r0
    //     0xb531dc: stur            w0, [x1, #0xf]
    // 0xb531e0: r2 = 4
    //     0xb531e0: movz            x2, #0x4
    // 0xb531e4: StoreField: r1->field_b = r2
    //     0xb531e4: stur            w2, [x1, #0xb]
    // 0xb531e8: r0 = Row()
    //     0xb531e8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb531ec: mov             x3, x0
    // 0xb531f0: r0 = Instance_Axis
    //     0xb531f0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb531f4: stur            x3, [fp, #-8]
    // 0xb531f8: StoreField: r3->field_f = r0
    //     0xb531f8: stur            w0, [x3, #0xf]
    // 0xb531fc: r1 = Instance_MainAxisAlignment
    //     0xb531fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb53200: ldr             x1, [x1, #0xab0]
    // 0xb53204: StoreField: r3->field_13 = r1
    //     0xb53204: stur            w1, [x3, #0x13]
    // 0xb53208: r4 = Instance_MainAxisSize
    //     0xb53208: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5320c: ldr             x4, [x4, #0xa10]
    // 0xb53210: ArrayStore: r3[0] = r4  ; List_4
    //     0xb53210: stur            w4, [x3, #0x17]
    // 0xb53214: r5 = Instance_CrossAxisAlignment
    //     0xb53214: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb53218: ldr             x5, [x5, #0xa18]
    // 0xb5321c: StoreField: r3->field_1b = r5
    //     0xb5321c: stur            w5, [x3, #0x1b]
    // 0xb53220: r6 = Instance_VerticalDirection
    //     0xb53220: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb53224: ldr             x6, [x6, #0xa20]
    // 0xb53228: StoreField: r3->field_23 = r6
    //     0xb53228: stur            w6, [x3, #0x23]
    // 0xb5322c: r7 = Instance_Clip
    //     0xb5322c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb53230: ldr             x7, [x7, #0x38]
    // 0xb53234: StoreField: r3->field_2b = r7
    //     0xb53234: stur            w7, [x3, #0x2b]
    // 0xb53238: StoreField: r3->field_2f = rZR
    //     0xb53238: stur            xzr, [x3, #0x2f]
    // 0xb5323c: ldur            x1, [fp, #-0x10]
    // 0xb53240: StoreField: r3->field_b = r1
    //     0xb53240: stur            w1, [x3, #0xb]
    // 0xb53244: ldur            x2, [fp, #-0x18]
    // 0xb53248: r1 = Function '<anonymous closure>':.
    //     0xb53248: add             x1, PP, #0x56, lsl #12  ; [pp+0x569b0] AnonymousClosure: (0xb53530), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/push_for_online_popup_bottom_sheet.dart] _PartialCodPopupBottomSheet::build (0xb52764)
    //     0xb5324c: ldr             x1, [x1, #0x9b0]
    // 0xb53250: r0 = AllocateClosure()
    //     0xb53250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb53254: stur            x0, [fp, #-0x10]
    // 0xb53258: r0 = TextButton()
    //     0xb53258: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb5325c: mov             x1, x0
    // 0xb53260: ldur            x0, [fp, #-0x10]
    // 0xb53264: stur            x1, [fp, #-0x18]
    // 0xb53268: StoreField: r1->field_b = r0
    //     0xb53268: stur            w0, [x1, #0xb]
    // 0xb5326c: r0 = false
    //     0xb5326c: add             x0, NULL, #0x30  ; false
    // 0xb53270: StoreField: r1->field_27 = r0
    //     0xb53270: stur            w0, [x1, #0x27]
    // 0xb53274: r0 = true
    //     0xb53274: add             x0, NULL, #0x20  ; true
    // 0xb53278: StoreField: r1->field_2f = r0
    //     0xb53278: stur            w0, [x1, #0x2f]
    // 0xb5327c: ldur            x0, [fp, #-8]
    // 0xb53280: StoreField: r1->field_37 = r0
    //     0xb53280: stur            w0, [x1, #0x37]
    // 0xb53284: r0 = TextButtonTheme()
    //     0xb53284: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb53288: mov             x2, x0
    // 0xb5328c: ldur            x0, [fp, #-0x30]
    // 0xb53290: stur            x2, [fp, #-8]
    // 0xb53294: StoreField: r2->field_f = r0
    //     0xb53294: stur            w0, [x2, #0xf]
    // 0xb53298: ldur            x0, [fp, #-0x18]
    // 0xb5329c: StoreField: r2->field_b = r0
    //     0xb5329c: stur            w0, [x2, #0xb]
    // 0xb532a0: r1 = <FlexParentData>
    //     0xb532a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb532a4: ldr             x1, [x1, #0xe00]
    // 0xb532a8: r0 = Expanded()
    //     0xb532a8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb532ac: mov             x3, x0
    // 0xb532b0: r0 = 2
    //     0xb532b0: movz            x0, #0x2
    // 0xb532b4: stur            x3, [fp, #-0x10]
    // 0xb532b8: StoreField: r3->field_13 = r0
    //     0xb532b8: stur            x0, [x3, #0x13]
    // 0xb532bc: r0 = Instance_FlexFit
    //     0xb532bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb532c0: ldr             x0, [x0, #0xe08]
    // 0xb532c4: StoreField: r3->field_1b = r0
    //     0xb532c4: stur            w0, [x3, #0x1b]
    // 0xb532c8: ldur            x0, [fp, #-8]
    // 0xb532cc: StoreField: r3->field_b = r0
    //     0xb532cc: stur            w0, [x3, #0xb]
    // 0xb532d0: r1 = Null
    //     0xb532d0: mov             x1, NULL
    // 0xb532d4: r2 = 6
    //     0xb532d4: movz            x2, #0x6
    // 0xb532d8: r0 = AllocateArray()
    //     0xb532d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb532dc: mov             x2, x0
    // 0xb532e0: ldur            x0, [fp, #-0x28]
    // 0xb532e4: stur            x2, [fp, #-8]
    // 0xb532e8: StoreField: r2->field_f = r0
    //     0xb532e8: stur            w0, [x2, #0xf]
    // 0xb532ec: r16 = Instance_SizedBox
    //     0xb532ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb532f0: ldr             x16, [x16, #0xb20]
    // 0xb532f4: StoreField: r2->field_13 = r16
    //     0xb532f4: stur            w16, [x2, #0x13]
    // 0xb532f8: ldur            x0, [fp, #-0x10]
    // 0xb532fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb532fc: stur            w0, [x2, #0x17]
    // 0xb53300: r1 = <Widget>
    //     0xb53300: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb53304: r0 = AllocateGrowableArray()
    //     0xb53304: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb53308: mov             x1, x0
    // 0xb5330c: ldur            x0, [fp, #-8]
    // 0xb53310: stur            x1, [fp, #-0x10]
    // 0xb53314: StoreField: r1->field_f = r0
    //     0xb53314: stur            w0, [x1, #0xf]
    // 0xb53318: r0 = 6
    //     0xb53318: movz            x0, #0x6
    // 0xb5331c: StoreField: r1->field_b = r0
    //     0xb5331c: stur            w0, [x1, #0xb]
    // 0xb53320: r0 = Row()
    //     0xb53320: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb53324: mov             x1, x0
    // 0xb53328: r0 = Instance_Axis
    //     0xb53328: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5332c: stur            x1, [fp, #-0x18]
    // 0xb53330: StoreField: r1->field_f = r0
    //     0xb53330: stur            w0, [x1, #0xf]
    // 0xb53334: r0 = Instance_MainAxisAlignment
    //     0xb53334: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb53338: ldr             x0, [x0, #0xd10]
    // 0xb5333c: StoreField: r1->field_13 = r0
    //     0xb5333c: stur            w0, [x1, #0x13]
    // 0xb53340: r0 = Instance_MainAxisSize
    //     0xb53340: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb53344: ldr             x0, [x0, #0xa10]
    // 0xb53348: ArrayStore: r1[0] = r0  ; List_4
    //     0xb53348: stur            w0, [x1, #0x17]
    // 0xb5334c: r0 = Instance_CrossAxisAlignment
    //     0xb5334c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb53350: ldr             x0, [x0, #0xa18]
    // 0xb53354: StoreField: r1->field_1b = r0
    //     0xb53354: stur            w0, [x1, #0x1b]
    // 0xb53358: r0 = Instance_VerticalDirection
    //     0xb53358: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5335c: ldr             x0, [x0, #0xa20]
    // 0xb53360: StoreField: r1->field_23 = r0
    //     0xb53360: stur            w0, [x1, #0x23]
    // 0xb53364: r0 = Instance_Clip
    //     0xb53364: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb53368: ldr             x0, [x0, #0x38]
    // 0xb5336c: StoreField: r1->field_2b = r0
    //     0xb5336c: stur            w0, [x1, #0x2b]
    // 0xb53370: StoreField: r1->field_2f = rZR
    //     0xb53370: stur            xzr, [x1, #0x2f]
    // 0xb53374: ldur            x0, [fp, #-0x10]
    // 0xb53378: StoreField: r1->field_b = r0
    //     0xb53378: stur            w0, [x1, #0xb]
    // 0xb5337c: ldur            d0, [fp, #-0x70]
    // 0xb53380: r0 = inline_Allocate_Double()
    //     0xb53380: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb53384: add             x0, x0, #0x10
    //     0xb53388: cmp             x2, x0
    //     0xb5338c: b.ls            #0xb534f8
    //     0xb53390: str             x0, [THR, #0x50]  ; THR::top
    //     0xb53394: sub             x0, x0, #0xf
    //     0xb53398: movz            x2, #0xe15c
    //     0xb5339c: movk            x2, #0x3, lsl #16
    //     0xb533a0: stur            x2, [x0, #-1]
    // 0xb533a4: StoreField: r0->field_7 = d0
    //     0xb533a4: stur            d0, [x0, #7]
    // 0xb533a8: stur            x0, [fp, #-8]
    // 0xb533ac: r0 = SizedBox()
    //     0xb533ac: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb533b0: mov             x1, x0
    // 0xb533b4: ldur            x0, [fp, #-8]
    // 0xb533b8: stur            x1, [fp, #-0x10]
    // 0xb533bc: StoreField: r1->field_f = r0
    //     0xb533bc: stur            w0, [x1, #0xf]
    // 0xb533c0: r0 = 80.000000
    //     0xb533c0: add             x0, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb533c4: ldr             x0, [x0, #0x2f8]
    // 0xb533c8: StoreField: r1->field_13 = r0
    //     0xb533c8: stur            w0, [x1, #0x13]
    // 0xb533cc: ldur            x0, [fp, #-0x18]
    // 0xb533d0: StoreField: r1->field_b = r0
    //     0xb533d0: stur            w0, [x1, #0xb]
    // 0xb533d4: r0 = Container()
    //     0xb533d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb533d8: stur            x0, [fp, #-8]
    // 0xb533dc: r16 = Instance_EdgeInsets
    //     0xb533dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb533e0: ldr             x16, [x16, #0xd0]
    // 0xb533e4: r30 = Instance_Color
    //     0xb533e4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb533e8: stp             lr, x16, [SP, #8]
    // 0xb533ec: ldur            x16, [fp, #-0x10]
    // 0xb533f0: str             x16, [SP]
    // 0xb533f4: mov             x1, x0
    // 0xb533f8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, padding, 0x1, null]
    //     0xb533f8: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d98] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "padding", 0x1, Null]
    //     0xb533fc: ldr             x4, [x4, #0xd98]
    // 0xb53400: r0 = Container()
    //     0xb53400: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb53404: r1 = <StackParentData>
    //     0xb53404: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb53408: ldr             x1, [x1, #0x8e0]
    // 0xb5340c: r0 = Positioned()
    //     0xb5340c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb53410: mov             x3, x0
    // 0xb53414: r0 = 0.000000
    //     0xb53414: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb53418: stur            x3, [fp, #-0x10]
    // 0xb5341c: StoreField: r3->field_13 = r0
    //     0xb5341c: stur            w0, [x3, #0x13]
    // 0xb53420: StoreField: r3->field_1b = r0
    //     0xb53420: stur            w0, [x3, #0x1b]
    // 0xb53424: StoreField: r3->field_1f = r0
    //     0xb53424: stur            w0, [x3, #0x1f]
    // 0xb53428: ldur            x0, [fp, #-8]
    // 0xb5342c: StoreField: r3->field_b = r0
    //     0xb5342c: stur            w0, [x3, #0xb]
    // 0xb53430: r1 = Null
    //     0xb53430: mov             x1, NULL
    // 0xb53434: r2 = 4
    //     0xb53434: movz            x2, #0x4
    // 0xb53438: r0 = AllocateArray()
    //     0xb53438: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5343c: mov             x2, x0
    // 0xb53440: ldur            x0, [fp, #-0x20]
    // 0xb53444: stur            x2, [fp, #-8]
    // 0xb53448: StoreField: r2->field_f = r0
    //     0xb53448: stur            w0, [x2, #0xf]
    // 0xb5344c: ldur            x0, [fp, #-0x10]
    // 0xb53450: StoreField: r2->field_13 = r0
    //     0xb53450: stur            w0, [x2, #0x13]
    // 0xb53454: r1 = <Widget>
    //     0xb53454: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb53458: r0 = AllocateGrowableArray()
    //     0xb53458: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5345c: mov             x1, x0
    // 0xb53460: ldur            x0, [fp, #-8]
    // 0xb53464: stur            x1, [fp, #-0x10]
    // 0xb53468: StoreField: r1->field_f = r0
    //     0xb53468: stur            w0, [x1, #0xf]
    // 0xb5346c: r0 = 4
    //     0xb5346c: movz            x0, #0x4
    // 0xb53470: StoreField: r1->field_b = r0
    //     0xb53470: stur            w0, [x1, #0xb]
    // 0xb53474: r0 = Stack()
    //     0xb53474: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb53478: mov             x1, x0
    // 0xb5347c: r0 = Instance_AlignmentDirectional
    //     0xb5347c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb53480: ldr             x0, [x0, #0xd08]
    // 0xb53484: stur            x1, [fp, #-8]
    // 0xb53488: StoreField: r1->field_f = r0
    //     0xb53488: stur            w0, [x1, #0xf]
    // 0xb5348c: r0 = Instance_StackFit
    //     0xb5348c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb53490: ldr             x0, [x0, #0xfa8]
    // 0xb53494: ArrayStore: r1[0] = r0  ; List_4
    //     0xb53494: stur            w0, [x1, #0x17]
    // 0xb53498: r0 = Instance_Clip
    //     0xb53498: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb5349c: ldr             x0, [x0, #0x7e0]
    // 0xb534a0: StoreField: r1->field_1b = r0
    //     0xb534a0: stur            w0, [x1, #0x1b]
    // 0xb534a4: ldur            x0, [fp, #-0x10]
    // 0xb534a8: StoreField: r1->field_b = r0
    //     0xb534a8: stur            w0, [x1, #0xb]
    // 0xb534ac: r0 = Padding()
    //     0xb534ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb534b0: r1 = Instance_EdgeInsets
    //     0xb534b0: add             x1, PP, #0x38, lsl #12  ; [pp+0x38dc0] Obj!EdgeInsets@d58221
    //     0xb534b4: ldr             x1, [x1, #0xdc0]
    // 0xb534b8: StoreField: r0->field_f = r1
    //     0xb534b8: stur            w1, [x0, #0xf]
    // 0xb534bc: ldur            x1, [fp, #-8]
    // 0xb534c0: StoreField: r0->field_b = r1
    //     0xb534c0: stur            w1, [x0, #0xb]
    // 0xb534c4: LeaveFrame
    //     0xb534c4: mov             SP, fp
    //     0xb534c8: ldp             fp, lr, [SP], #0x10
    // 0xb534cc: ret
    //     0xb534cc: ret             
    // 0xb534d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb534d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb534d4: b               #0xb5278c
    // 0xb534d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb534d8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb534dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb534dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb534e0: SaveReg d0
    //     0xb534e0: str             q0, [SP, #-0x10]!
    // 0xb534e4: r0 = AllocateDouble()
    //     0xb534e4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb534e8: RestoreReg d0
    //     0xb534e8: ldr             q0, [SP], #0x10
    // 0xb534ec: b               #0xb52d08
    // 0xb534f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb534f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb534f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb534f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb534f8: SaveReg d0
    //     0xb534f8: str             q0, [SP, #-0x10]!
    // 0xb534fc: SaveReg r1
    //     0xb534fc: str             x1, [SP, #-8]!
    // 0xb53500: r0 = AllocateDouble()
    //     0xb53500: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb53504: RestoreReg r1
    //     0xb53504: ldr             x1, [SP], #8
    // 0xb53508: RestoreReg d0
    //     0xb53508: ldr             q0, [SP], #0x10
    // 0xb5350c: b               #0xb533a4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb53530, size: 0xac
    // 0xb53530: EnterFrame
    //     0xb53530: stp             fp, lr, [SP, #-0x10]!
    //     0xb53534: mov             fp, SP
    // 0xb53538: AllocStack(0x10)
    //     0xb53538: sub             SP, SP, #0x10
    // 0xb5353c: SetupParameters()
    //     0xb5353c: ldr             x0, [fp, #0x10]
    //     0xb53540: ldur            w1, [x0, #0x17]
    //     0xb53544: add             x1, x1, HEAP, lsl #32
    //     0xb53548: stur            x1, [fp, #-8]
    // 0xb5354c: CheckStackOverflow
    //     0xb5354c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53550: cmp             SP, x16
    //     0xb53554: b.ls            #0xb535d0
    // 0xb53558: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb53558: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb5355c: ldr             x0, [x0, #0x1c80]
    //     0xb53560: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb53564: cmp             w0, w16
    //     0xb53568: b.ne            #0xb53574
    //     0xb5356c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb53570: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb53574: str             NULL, [SP]
    // 0xb53578: r4 = const [0x1, 0, 0, 0, null]
    //     0xb53578: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb5357c: r0 = GetNavigation.back()
    //     0xb5357c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb53580: ldur            x0, [fp, #-8]
    // 0xb53584: LoadField: r1 = r0->field_f
    //     0xb53584: ldur            w1, [x0, #0xf]
    // 0xb53588: DecompressPointer r1
    //     0xb53588: add             x1, x1, HEAP, lsl #32
    // 0xb5358c: LoadField: r0 = r1->field_b
    //     0xb5358c: ldur            w0, [x1, #0xb]
    // 0xb53590: DecompressPointer r0
    //     0xb53590: add             x0, x0, HEAP, lsl #32
    // 0xb53594: cmp             w0, NULL
    // 0xb53598: b.eq            #0xb535d8
    // 0xb5359c: LoadField: r1 = r0->field_f
    //     0xb5359c: ldur            w1, [x0, #0xf]
    // 0xb535a0: DecompressPointer r1
    //     0xb535a0: add             x1, x1, HEAP, lsl #32
    // 0xb535a4: str             x1, [SP]
    // 0xb535a8: r4 = 0
    //     0xb535a8: movz            x4, #0
    // 0xb535ac: ldr             x0, [SP]
    // 0xb535b0: r16 = UnlinkedCall_0x613b5c
    //     0xb535b0: add             x16, PP, #0x56, lsl #12  ; [pp+0x569b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb535b4: add             x16, x16, #0x9b8
    // 0xb535b8: ldp             x5, lr, [x16]
    // 0xb535bc: blr             lr
    // 0xb535c0: r0 = Null
    //     0xb535c0: mov             x0, NULL
    // 0xb535c4: LeaveFrame
    //     0xb535c4: mov             SP, fp
    //     0xb535c8: ldp             fp, lr, [SP], #0x10
    // 0xb535cc: ret
    //     0xb535cc: ret             
    // 0xb535d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb535d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb535d4: b               #0xb53558
    // 0xb535d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb535d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb535dc, size: 0xac
    // 0xb535dc: EnterFrame
    //     0xb535dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb535e0: mov             fp, SP
    // 0xb535e4: AllocStack(0x10)
    //     0xb535e4: sub             SP, SP, #0x10
    // 0xb535e8: SetupParameters()
    //     0xb535e8: ldr             x0, [fp, #0x10]
    //     0xb535ec: ldur            w1, [x0, #0x17]
    //     0xb535f0: add             x1, x1, HEAP, lsl #32
    //     0xb535f4: stur            x1, [fp, #-8]
    // 0xb535f8: CheckStackOverflow
    //     0xb535f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb535fc: cmp             SP, x16
    //     0xb53600: b.ls            #0xb5367c
    // 0xb53604: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb53604: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb53608: ldr             x0, [x0, #0x1c80]
    //     0xb5360c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb53610: cmp             w0, w16
    //     0xb53614: b.ne            #0xb53620
    //     0xb53618: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb5361c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb53620: str             NULL, [SP]
    // 0xb53624: r4 = const [0x1, 0, 0, 0, null]
    //     0xb53624: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb53628: r0 = GetNavigation.back()
    //     0xb53628: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb5362c: ldur            x0, [fp, #-8]
    // 0xb53630: LoadField: r1 = r0->field_f
    //     0xb53630: ldur            w1, [x0, #0xf]
    // 0xb53634: DecompressPointer r1
    //     0xb53634: add             x1, x1, HEAP, lsl #32
    // 0xb53638: LoadField: r0 = r1->field_b
    //     0xb53638: ldur            w0, [x1, #0xb]
    // 0xb5363c: DecompressPointer r0
    //     0xb5363c: add             x0, x0, HEAP, lsl #32
    // 0xb53640: cmp             w0, NULL
    // 0xb53644: b.eq            #0xb53684
    // 0xb53648: LoadField: r1 = r0->field_13
    //     0xb53648: ldur            w1, [x0, #0x13]
    // 0xb5364c: DecompressPointer r1
    //     0xb5364c: add             x1, x1, HEAP, lsl #32
    // 0xb53650: str             x1, [SP]
    // 0xb53654: r4 = 0
    //     0xb53654: movz            x4, #0
    // 0xb53658: ldr             x0, [SP]
    // 0xb5365c: r16 = UnlinkedCall_0x613b5c
    //     0xb5365c: add             x16, PP, #0x56, lsl #12  ; [pp+0x569c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb53660: add             x16, x16, #0x9c8
    // 0xb53664: ldp             x5, lr, [x16]
    // 0xb53668: blr             lr
    // 0xb5366c: r0 = Null
    //     0xb5366c: mov             x0, NULL
    // 0xb53670: LeaveFrame
    //     0xb53670: mov             SP, fp
    //     0xb53674: ldp             fp, lr, [SP], #0x10
    // 0xb53678: ret
    //     0xb53678: ret             
    // 0xb5367c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5367c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb53680: b               #0xb53604
    // 0xb53684: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb53684: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4092, size: 0x18, field offset: 0xc
//   const constructor, 
class PushOnlinePopupBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ef68, size: 0x24
    // 0xc7ef68: EnterFrame
    //     0xc7ef68: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ef6c: mov             fp, SP
    // 0xc7ef70: mov             x0, x1
    // 0xc7ef74: r1 = <PushOnlinePopupBottomSheet>
    //     0xc7ef74: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a20] TypeArguments: <PushOnlinePopupBottomSheet>
    //     0xc7ef78: ldr             x1, [x1, #0xa20]
    // 0xc7ef7c: r0 = _PartialCodPopupBottomSheet()
    //     0xc7ef7c: bl              #0xc7ef8c  ; Allocate_PartialCodPopupBottomSheetStub -> _PartialCodPopupBottomSheet (size=0x14)
    // 0xc7ef80: LeaveFrame
    //     0xc7ef80: mov             SP, fp
    //     0xc7ef84: ldp             fp, lr, [SP], #0x10
    // 0xc7ef88: ret
    //     0xc7ef88: ret             
  }
}
