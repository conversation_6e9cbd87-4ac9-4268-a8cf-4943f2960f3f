// lib: , url: package:customer_app/app/presentation/views/line/orders/order_card.dart

// class id: 1049535, size: 0x8
class :: {
}

// class id: 3237, size: 0x1c, field offset: 0x14
class _OrderCardState extends State<dynamic> {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x80725c, size: 0x1f4
    // 0x80725c: EnterFrame
    //     0x80725c: stp             fp, lr, [SP, #-0x10]!
    //     0x807260: mov             fp, SP
    // 0x807264: AllocStack(0x20)
    //     0x807264: sub             SP, SP, #0x20
    // 0x807268: SetupParameters(_OrderCardState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x807268: mov             x4, x1
    //     0x80726c: mov             x3, x2
    //     0x807270: stur            x1, [fp, #-8]
    //     0x807274: stur            x2, [fp, #-0x10]
    // 0x807278: CheckStackOverflow
    //     0x807278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80727c: cmp             SP, x16
    //     0x807280: b.ls            #0x807444
    // 0x807284: mov             x0, x3
    // 0x807288: r2 = Null
    //     0x807288: mov             x2, NULL
    // 0x80728c: r1 = Null
    //     0x80728c: mov             x1, NULL
    // 0x807290: r4 = 60
    //     0x807290: movz            x4, #0x3c
    // 0x807294: branchIfSmi(r0, 0x8072a0)
    //     0x807294: tbz             w0, #0, #0x8072a0
    // 0x807298: r4 = LoadClassIdInstr(r0)
    //     0x807298: ldur            x4, [x0, #-1]
    //     0x80729c: ubfx            x4, x4, #0xc, #0x14
    // 0x8072a0: cmp             x4, #0xf90
    // 0x8072a4: b.eq            #0x8072bc
    // 0x8072a8: r8 = OrderCard
    //     0x8072a8: add             x8, PP, #0x6a, lsl #12  ; [pp+0x6a380] Type: OrderCard
    //     0x8072ac: ldr             x8, [x8, #0x380]
    // 0x8072b0: r3 = Null
    //     0x8072b0: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a388] Null
    //     0x8072b4: ldr             x3, [x3, #0x388]
    // 0x8072b8: r0 = OrderCard()
    //     0x8072b8: bl              #0x807450  ; IsType_OrderCard_Stub
    // 0x8072bc: ldur            x3, [fp, #-8]
    // 0x8072c0: LoadField: r2 = r3->field_7
    //     0x8072c0: ldur            w2, [x3, #7]
    // 0x8072c4: DecompressPointer r2
    //     0x8072c4: add             x2, x2, HEAP, lsl #32
    // 0x8072c8: ldur            x0, [fp, #-0x10]
    // 0x8072cc: r1 = Null
    //     0x8072cc: mov             x1, NULL
    // 0x8072d0: cmp             w2, NULL
    // 0x8072d4: b.eq            #0x8072f8
    // 0x8072d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8072d8: ldur            w4, [x2, #0x17]
    // 0x8072dc: DecompressPointer r4
    //     0x8072dc: add             x4, x4, HEAP, lsl #32
    // 0x8072e0: r8 = X0 bound StatefulWidget
    //     0x8072e0: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x8072e4: ldr             x8, [x8, #0x7a0]
    // 0x8072e8: LoadField: r9 = r4->field_7
    //     0x8072e8: ldur            x9, [x4, #7]
    // 0x8072ec: r3 = Null
    //     0x8072ec: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a398] Null
    //     0x8072f0: ldr             x3, [x3, #0x398]
    // 0x8072f4: blr             x9
    // 0x8072f8: ldur            x2, [fp, #-8]
    // 0x8072fc: LoadField: r0 = r2->field_b
    //     0x8072fc: ldur            w0, [x2, #0xb]
    // 0x807300: DecompressPointer r0
    //     0x807300: add             x0, x0, HEAP, lsl #32
    // 0x807304: cmp             w0, NULL
    // 0x807308: b.eq            #0x80744c
    // 0x80730c: LoadField: r1 = r0->field_b
    //     0x80730c: ldur            w1, [x0, #0xb]
    // 0x807310: DecompressPointer r1
    //     0x807310: add             x1, x1, HEAP, lsl #32
    // 0x807314: LoadField: r3 = r1->field_8b
    //     0x807314: ldur            w3, [x1, #0x8b]
    // 0x807318: DecompressPointer r3
    //     0x807318: add             x3, x3, HEAP, lsl #32
    // 0x80731c: cmp             w3, NULL
    // 0x807320: b.ne            #0x80732c
    // 0x807324: r4 = Null
    //     0x807324: mov             x4, NULL
    // 0x807328: b               #0x807348
    // 0x80732c: LoadField: r4 = r3->field_f
    //     0x80732c: ldur            x4, [x3, #0xf]
    // 0x807330: r0 = BoxInt64Instr(r4)
    //     0x807330: sbfiz           x0, x4, #1, #0x1f
    //     0x807334: cmp             x4, x0, asr #1
    //     0x807338: b.eq            #0x807344
    //     0x80733c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x807340: stur            x4, [x0, #7]
    // 0x807344: mov             x4, x0
    // 0x807348: ldur            x0, [fp, #-0x10]
    // 0x80734c: LoadField: r1 = r0->field_b
    //     0x80734c: ldur            w1, [x0, #0xb]
    // 0x807350: DecompressPointer r1
    //     0x807350: add             x1, x1, HEAP, lsl #32
    // 0x807354: LoadField: r0 = r1->field_8b
    //     0x807354: ldur            w0, [x1, #0x8b]
    // 0x807358: DecompressPointer r0
    //     0x807358: add             x0, x0, HEAP, lsl #32
    // 0x80735c: cmp             w0, NULL
    // 0x807360: b.ne            #0x80736c
    // 0x807364: r0 = Null
    //     0x807364: mov             x0, NULL
    // 0x807368: b               #0x807384
    // 0x80736c: LoadField: r5 = r0->field_f
    //     0x80736c: ldur            x5, [x0, #0xf]
    // 0x807370: r0 = BoxInt64Instr(r5)
    //     0x807370: sbfiz           x0, x5, #1, #0x1f
    //     0x807374: cmp             x5, x0, asr #1
    //     0x807378: b.eq            #0x807384
    //     0x80737c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x807380: stur            x5, [x0, #7]
    // 0x807384: cmp             w4, w0
    // 0x807388: b.eq            #0x807434
    // 0x80738c: and             w16, w4, w0
    // 0x807390: branchIfSmi(r16, 0x8073c4)
    //     0x807390: tbz             w16, #0, #0x8073c4
    // 0x807394: r16 = LoadClassIdInstr(r4)
    //     0x807394: ldur            x16, [x4, #-1]
    //     0x807398: ubfx            x16, x16, #0xc, #0x14
    // 0x80739c: cmp             x16, #0x3d
    // 0x8073a0: b.ne            #0x8073c4
    // 0x8073a4: r16 = LoadClassIdInstr(r0)
    //     0x8073a4: ldur            x16, [x0, #-1]
    //     0x8073a8: ubfx            x16, x16, #0xc, #0x14
    // 0x8073ac: cmp             x16, #0x3d
    // 0x8073b0: b.ne            #0x8073c4
    // 0x8073b4: LoadField: r16 = r4->field_7
    //     0x8073b4: ldur            x16, [x4, #7]
    // 0x8073b8: LoadField: r17 = r0->field_7
    //     0x8073b8: ldur            x17, [x0, #7]
    // 0x8073bc: cmp             x16, x17
    // 0x8073c0: b.eq            #0x807434
    // 0x8073c4: cmp             w3, NULL
    // 0x8073c8: b.ne            #0x8073d4
    // 0x8073cc: r0 = Null
    //     0x8073cc: mov             x0, NULL
    // 0x8073d0: b               #0x8073ec
    // 0x8073d4: LoadField: r4 = r3->field_f
    //     0x8073d4: ldur            x4, [x3, #0xf]
    // 0x8073d8: r0 = BoxInt64Instr(r4)
    //     0x8073d8: sbfiz           x0, x4, #1, #0x1f
    //     0x8073dc: cmp             x4, x0, asr #1
    //     0x8073e0: b.eq            #0x8073ec
    //     0x8073e4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8073e8: stur            x4, [x0, #7]
    // 0x8073ec: cmp             w0, NULL
    // 0x8073f0: b.ne            #0x8073fc
    // 0x8073f4: r3 = 0
    //     0x8073f4: movz            x3, #0
    // 0x8073f8: b               #0x80740c
    // 0x8073fc: r1 = LoadInt32Instr(r0)
    //     0x8073fc: sbfx            x1, x0, #1, #0x1f
    //     0x807400: tbz             w0, #0, #0x807408
    //     0x807404: ldur            x1, [x0, #7]
    // 0x807408: mov             x3, x1
    // 0x80740c: r0 = BoxInt64Instr(r3)
    //     0x80740c: sbfiz           x0, x3, #1, #0x1f
    //     0x807410: cmp             x3, x0, asr #1
    //     0x807414: b.eq            #0x807420
    //     0x807418: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80741c: stur            x3, [x0, #7]
    // 0x807420: stp             x0, NULL, [SP]
    // 0x807424: r0 = _Double.fromInteger()
    //     0x807424: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x807428: LoadField: d0 = r0->field_7
    //     0x807428: ldur            d0, [x0, #7]
    // 0x80742c: ldur            x1, [fp, #-8]
    // 0x807430: StoreField: r1->field_13 = d0
    //     0x807430: stur            d0, [x1, #0x13]
    // 0x807434: r0 = Null
    //     0x807434: mov             x0, NULL
    // 0x807438: LeaveFrame
    //     0x807438: mov             SP, fp
    //     0x80743c: ldp             fp, lr, [SP], #0x10
    // 0x807440: ret
    //     0x807440: ret             
    // 0x807444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807448: b               #0x807284
    // 0x80744c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80744c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x94a594, size: 0xcc
    // 0x94a594: EnterFrame
    //     0x94a594: stp             fp, lr, [SP, #-0x10]!
    //     0x94a598: mov             fp, SP
    // 0x94a59c: AllocStack(0x18)
    //     0x94a59c: sub             SP, SP, #0x18
    // 0x94a5a0: SetupParameters(_OrderCardState this /* r1 => r2, fp-0x8 */)
    //     0x94a5a0: mov             x2, x1
    //     0x94a5a4: stur            x1, [fp, #-8]
    // 0x94a5a8: CheckStackOverflow
    //     0x94a5a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a5ac: cmp             SP, x16
    //     0x94a5b0: b.ls            #0x94a654
    // 0x94a5b4: LoadField: r0 = r2->field_b
    //     0x94a5b4: ldur            w0, [x2, #0xb]
    // 0x94a5b8: DecompressPointer r0
    //     0x94a5b8: add             x0, x0, HEAP, lsl #32
    // 0x94a5bc: cmp             w0, NULL
    // 0x94a5c0: b.eq            #0x94a65c
    // 0x94a5c4: LoadField: r1 = r0->field_b
    //     0x94a5c4: ldur            w1, [x0, #0xb]
    // 0x94a5c8: DecompressPointer r1
    //     0x94a5c8: add             x1, x1, HEAP, lsl #32
    // 0x94a5cc: LoadField: r0 = r1->field_8b
    //     0x94a5cc: ldur            w0, [x1, #0x8b]
    // 0x94a5d0: DecompressPointer r0
    //     0x94a5d0: add             x0, x0, HEAP, lsl #32
    // 0x94a5d4: cmp             w0, NULL
    // 0x94a5d8: b.ne            #0x94a5e4
    // 0x94a5dc: r0 = Null
    //     0x94a5dc: mov             x0, NULL
    // 0x94a5e0: b               #0x94a5fc
    // 0x94a5e4: LoadField: r3 = r0->field_f
    //     0x94a5e4: ldur            x3, [x0, #0xf]
    // 0x94a5e8: r0 = BoxInt64Instr(r3)
    //     0x94a5e8: sbfiz           x0, x3, #1, #0x1f
    //     0x94a5ec: cmp             x3, x0, asr #1
    //     0x94a5f0: b.eq            #0x94a5fc
    //     0x94a5f4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94a5f8: stur            x3, [x0, #7]
    // 0x94a5fc: cmp             w0, NULL
    // 0x94a600: b.ne            #0x94a60c
    // 0x94a604: r3 = 0
    //     0x94a604: movz            x3, #0
    // 0x94a608: b               #0x94a61c
    // 0x94a60c: r1 = LoadInt32Instr(r0)
    //     0x94a60c: sbfx            x1, x0, #1, #0x1f
    //     0x94a610: tbz             w0, #0, #0x94a618
    //     0x94a614: ldur            x1, [x0, #7]
    // 0x94a618: mov             x3, x1
    // 0x94a61c: r0 = BoxInt64Instr(r3)
    //     0x94a61c: sbfiz           x0, x3, #1, #0x1f
    //     0x94a620: cmp             x3, x0, asr #1
    //     0x94a624: b.eq            #0x94a630
    //     0x94a628: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94a62c: stur            x3, [x0, #7]
    // 0x94a630: stp             x0, NULL, [SP]
    // 0x94a634: r0 = _Double.fromInteger()
    //     0x94a634: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x94a638: LoadField: d0 = r0->field_7
    //     0x94a638: ldur            d0, [x0, #7]
    // 0x94a63c: ldur            x1, [fp, #-8]
    // 0x94a640: StoreField: r1->field_13 = d0
    //     0x94a640: stur            d0, [x1, #0x13]
    // 0x94a644: r0 = Null
    //     0x94a644: mov             x0, NULL
    // 0x94a648: LeaveFrame
    //     0x94a648: mov             SP, fp
    //     0x94a64c: ldp             fp, lr, [SP], #0x10
    // 0x94a650: ret
    //     0x94a650: ret             
    // 0x94a654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a654: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a658: b               #0x94a5b4
    // 0x94a65c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94a65c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa6d658, size: 0xc
    // 0xa6d658: r0 = Instance_Padding
    //     0xa6d658: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a338] Obj!Padding@d684a1
    //     0xa6d65c: ldr             x0, [x0, #0x338]
    // 0xa6d660: ret
    //     0xa6d660: ret             
  }
  [closure] SvgPicture <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa6d740, size: 0x10c
    // 0xa6d740: EnterFrame
    //     0xa6d740: stp             fp, lr, [SP, #-0x10]!
    //     0xa6d744: mov             fp, SP
    // 0xa6d748: AllocStack(0x10)
    //     0xa6d748: sub             SP, SP, #0x10
    // 0xa6d74c: SetupParameters()
    //     0xa6d74c: ldr             x0, [fp, #0x20]
    //     0xa6d750: ldur            w1, [x0, #0x17]
    //     0xa6d754: add             x1, x1, HEAP, lsl #32
    // 0xa6d758: CheckStackOverflow
    //     0xa6d758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6d75c: cmp             SP, x16
    //     0xa6d760: b.ls            #0xa6d828
    // 0xa6d764: LoadField: r0 = r1->field_f
    //     0xa6d764: ldur            w0, [x1, #0xf]
    // 0xa6d768: DecompressPointer r0
    //     0xa6d768: add             x0, x0, HEAP, lsl #32
    // 0xa6d76c: LoadField: d0 = r0->field_13
    //     0xa6d76c: ldur            d0, [x0, #0x13]
    // 0xa6d770: fcmp            d0, d0
    // 0xa6d774: b.vs            #0xa6d830
    // 0xa6d778: fcvtzs          x0, d0
    // 0xa6d77c: asr             x16, x0, #0x1e
    // 0xa6d780: cmp             x16, x0, asr #63
    // 0xa6d784: b.ne            #0xa6d830
    // 0xa6d788: lsl             x0, x0, #1
    // 0xa6d78c: stur            x0, [fp, #-8]
    // 0xa6d790: r1 = LoadInt32Instr(r0)
    //     0xa6d790: sbfx            x1, x0, #1, #0x1f
    //     0xa6d794: tbz             w0, #0, #0xa6d79c
    //     0xa6d798: ldur            x1, [x0, #7]
    // 0xa6d79c: sub             x2, x1, #1
    // 0xa6d7a0: ldr             x1, [fp, #0x10]
    // 0xa6d7a4: r3 = LoadInt32Instr(r1)
    //     0xa6d7a4: sbfx            x3, x1, #1, #0x1f
    //     0xa6d7a8: tbz             w1, #0, #0xa6d7b0
    //     0xa6d7ac: ldur            x3, [x1, #7]
    // 0xa6d7b0: cmp             x3, x2
    // 0xa6d7b4: b.ne            #0xa6d7f4
    // 0xa6d7b8: r1 = Null
    //     0xa6d7b8: mov             x1, NULL
    // 0xa6d7bc: r2 = 6
    //     0xa6d7bc: movz            x2, #0x6
    // 0xa6d7c0: r0 = AllocateArray()
    //     0xa6d7c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa6d7c4: r16 = "assets/images/star"
    //     0xa6d7c4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bb0] "assets/images/star"
    //     0xa6d7c8: ldr             x16, [x16, #0xbb0]
    // 0xa6d7cc: StoreField: r0->field_f = r16
    //     0xa6d7cc: stur            w16, [x0, #0xf]
    // 0xa6d7d0: ldur            x1, [fp, #-8]
    // 0xa6d7d4: StoreField: r0->field_13 = r1
    //     0xa6d7d4: stur            w1, [x0, #0x13]
    // 0xa6d7d8: r16 = ".svg"
    //     0xa6d7d8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bb8] ".svg"
    //     0xa6d7dc: ldr             x16, [x16, #0xbb8]
    // 0xa6d7e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xa6d7e0: stur            w16, [x0, #0x17]
    // 0xa6d7e4: str             x0, [SP]
    // 0xa6d7e8: r0 = _interpolate()
    //     0xa6d7e8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa6d7ec: mov             x2, x0
    // 0xa6d7f0: b               #0xa6d7fc
    // 0xa6d7f4: r2 = "assets/images/ratedStar.svg"
    //     0xa6d7f4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36bc0] "assets/images/ratedStar.svg"
    //     0xa6d7f8: ldr             x2, [x2, #0xbc0]
    // 0xa6d7fc: stur            x2, [fp, #-8]
    // 0xa6d800: r0 = SvgPicture()
    //     0xa6d800: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa6d804: mov             x1, x0
    // 0xa6d808: ldur            x2, [fp, #-8]
    // 0xa6d80c: stur            x0, [fp, #-8]
    // 0xa6d810: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa6d810: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa6d814: r0 = SvgPicture.asset()
    //     0xa6d814: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa6d818: ldur            x0, [fp, #-8]
    // 0xa6d81c: LeaveFrame
    //     0xa6d81c: mov             SP, fp
    //     0xa6d820: ldp             fp, lr, [SP], #0x10
    // 0xa6d824: ret
    //     0xa6d824: ret             
    // 0xa6d828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6d828: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6d82c: b               #0xa6d764
    // 0xa6d830: SaveReg d0
    //     0xa6d830: str             q0, [SP, #-0x10]!
    // 0xa6d834: r0 = 74
    //     0xa6d834: movz            x0, #0x4a
    // 0xa6d838: r30 = DoubleToIntegerStub
    //     0xa6d838: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xa6d83c: LoadField: r30 = r30->field_7
    //     0xa6d83c: ldur            lr, [lr, #7]
    // 0xa6d840: blr             lr
    // 0xa6d844: RestoreReg d0
    //     0xa6d844: ldr             q0, [SP], #0x10
    // 0xa6d848: b               #0xa6d78c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa6d968, size: 0x34
    // 0xa6d968: ldr             x1, [SP]
    // 0xa6d96c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa6d96c: ldur            w2, [x1, #0x17]
    // 0xa6d970: DecompressPointer r2
    //     0xa6d970: add             x2, x2, HEAP, lsl #32
    // 0xa6d974: LoadField: r1 = r2->field_b
    //     0xa6d974: ldur            w1, [x2, #0xb]
    // 0xa6d978: DecompressPointer r1
    //     0xa6d978: add             x1, x1, HEAP, lsl #32
    // 0xa6d97c: LoadField: r3 = r1->field_f
    //     0xa6d97c: ldur            w3, [x1, #0xf]
    // 0xa6d980: DecompressPointer r3
    //     0xa6d980: add             x3, x3, HEAP, lsl #32
    // 0xa6d984: LoadField: r1 = r2->field_f
    //     0xa6d984: ldur            w1, [x2, #0xf]
    // 0xa6d988: DecompressPointer r1
    //     0xa6d988: add             x1, x1, HEAP, lsl #32
    // 0xa6d98c: LoadField: d0 = r1->field_7
    //     0xa6d98c: ldur            d0, [x1, #7]
    // 0xa6d990: StoreField: r3->field_13 = d0
    //     0xa6d990: stur            d0, [x3, #0x13]
    // 0xa6d994: r0 = Null
    //     0xa6d994: mov             x0, NULL
    // 0xa6d998: ret
    //     0xa6d998: ret             
  }
  [closure] void <anonymous closure>(dynamic, double) {
    // ** addr: 0xa6d99c, size: 0x120
    // 0xa6d99c: EnterFrame
    //     0xa6d99c: stp             fp, lr, [SP, #-0x10]!
    //     0xa6d9a0: mov             fp, SP
    // 0xa6d9a4: AllocStack(0x30)
    //     0xa6d9a4: sub             SP, SP, #0x30
    // 0xa6d9a8: SetupParameters()
    //     0xa6d9a8: ldr             x0, [fp, #0x18]
    //     0xa6d9ac: ldur            w1, [x0, #0x17]
    //     0xa6d9b0: add             x1, x1, HEAP, lsl #32
    //     0xa6d9b4: stur            x1, [fp, #-8]
    // 0xa6d9b8: CheckStackOverflow
    //     0xa6d9b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6d9bc: cmp             SP, x16
    //     0xa6d9c0: b.ls            #0xa6da98
    // 0xa6d9c4: r1 = 1
    //     0xa6d9c4: movz            x1, #0x1
    // 0xa6d9c8: r0 = AllocateContext()
    //     0xa6d9c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xa6d9cc: mov             x1, x0
    // 0xa6d9d0: ldur            x0, [fp, #-8]
    // 0xa6d9d4: StoreField: r1->field_b = r0
    //     0xa6d9d4: stur            w0, [x1, #0xb]
    // 0xa6d9d8: ldr             x2, [fp, #0x10]
    // 0xa6d9dc: StoreField: r1->field_f = r2
    //     0xa6d9dc: stur            w2, [x1, #0xf]
    // 0xa6d9e0: LoadField: r3 = r0->field_f
    //     0xa6d9e0: ldur            w3, [x0, #0xf]
    // 0xa6d9e4: DecompressPointer r3
    //     0xa6d9e4: add             x3, x3, HEAP, lsl #32
    // 0xa6d9e8: mov             x2, x1
    // 0xa6d9ec: stur            x3, [fp, #-0x10]
    // 0xa6d9f0: r1 = Function '<anonymous closure>':.
    //     0xa6d9f0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a350] AnonymousClosure: (0xa6d968), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xa6d9f4: ldr             x1, [x1, #0x350]
    // 0xa6d9f8: r0 = AllocateClosure()
    //     0xa6d9f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa6d9fc: ldur            x1, [fp, #-0x10]
    // 0xa6da00: mov             x2, x0
    // 0xa6da04: r0 = setState()
    //     0xa6da04: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa6da08: ldur            x0, [fp, #-8]
    // 0xa6da0c: LoadField: r1 = r0->field_f
    //     0xa6da0c: ldur            w1, [x0, #0xf]
    // 0xa6da10: DecompressPointer r1
    //     0xa6da10: add             x1, x1, HEAP, lsl #32
    // 0xa6da14: LoadField: r0 = r1->field_b
    //     0xa6da14: ldur            w0, [x1, #0xb]
    // 0xa6da18: DecompressPointer r0
    //     0xa6da18: add             x0, x0, HEAP, lsl #32
    // 0xa6da1c: cmp             w0, NULL
    // 0xa6da20: b.eq            #0xa6daa0
    // 0xa6da24: LoadField: d0 = r1->field_13
    //     0xa6da24: ldur            d0, [x1, #0x13]
    // 0xa6da28: LoadField: r1 = r0->field_b
    //     0xa6da28: ldur            w1, [x0, #0xb]
    // 0xa6da2c: DecompressPointer r1
    //     0xa6da2c: add             x1, x1, HEAP, lsl #32
    // 0xa6da30: LoadField: r2 = r0->field_2b
    //     0xa6da30: ldur            w2, [x0, #0x2b]
    // 0xa6da34: DecompressPointer r2
    //     0xa6da34: add             x2, x2, HEAP, lsl #32
    // 0xa6da38: r0 = inline_Allocate_Double()
    //     0xa6da38: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xa6da3c: add             x0, x0, #0x10
    //     0xa6da40: cmp             x3, x0
    //     0xa6da44: b.ls            #0xa6daa4
    //     0xa6da48: str             x0, [THR, #0x50]  ; THR::top
    //     0xa6da4c: sub             x0, x0, #0xf
    //     0xa6da50: movz            x3, #0xe15c
    //     0xa6da54: movk            x3, #0x3, lsl #16
    //     0xa6da58: stur            x3, [x0, #-1]
    // 0xa6da5c: StoreField: r0->field_7 = d0
    //     0xa6da5c: stur            d0, [x0, #7]
    // 0xa6da60: stp             x0, x2, [SP, #0x10]
    // 0xa6da64: r16 = "stars"
    //     0xa6da64: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb28] "stars"
    //     0xa6da68: ldr             x16, [x16, #0xb28]
    // 0xa6da6c: stp             x16, x1, [SP]
    // 0xa6da70: r4 = 0
    //     0xa6da70: movz            x4, #0
    // 0xa6da74: ldr             x0, [SP, #0x18]
    // 0xa6da78: r16 = UnlinkedCall_0x613b5c
    //     0xa6da78: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a358] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa6da7c: add             x16, x16, #0x358
    // 0xa6da80: ldp             x5, lr, [x16]
    // 0xa6da84: blr             lr
    // 0xa6da88: r0 = Null
    //     0xa6da88: mov             x0, NULL
    // 0xa6da8c: LeaveFrame
    //     0xa6da8c: mov             SP, fp
    //     0xa6da90: ldp             fp, lr, [SP], #0x10
    // 0xa6da94: ret
    //     0xa6da94: ret             
    // 0xa6da98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6da98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6da9c: b               #0xa6d9c4
    // 0xa6daa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6daa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6daa4: SaveReg d0
    //     0xa6daa4: str             q0, [SP, #-0x10]!
    // 0xa6daa8: stp             x1, x2, [SP, #-0x10]!
    // 0xa6daac: r0 = AllocateDouble()
    //     0xa6daac: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa6dab0: ldp             x1, x2, [SP], #0x10
    // 0xa6dab4: RestoreReg d0
    //     0xa6dab4: ldr             q0, [SP], #0x10
    // 0xa6dab8: b               #0xa6da5c
  }
  _ build(/* No info */) {
    // ** addr: 0xbf6f24, size: 0x18fc
    // 0xbf6f24: EnterFrame
    //     0xbf6f24: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6f28: mov             fp, SP
    // 0xbf6f2c: AllocStack(0xa8)
    //     0xbf6f2c: sub             SP, SP, #0xa8
    // 0xbf6f30: SetupParameters(_OrderCardState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbf6f30: mov             x0, x1
    //     0xbf6f34: stur            x1, [fp, #-8]
    //     0xbf6f38: mov             x1, x2
    //     0xbf6f3c: stur            x2, [fp, #-0x10]
    // 0xbf6f40: CheckStackOverflow
    //     0xbf6f40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf6f44: cmp             SP, x16
    //     0xbf6f48: b.ls            #0xbf8780
    // 0xbf6f4c: r1 = 1
    //     0xbf6f4c: movz            x1, #0x1
    // 0xbf6f50: r0 = AllocateContext()
    //     0xbf6f50: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf6f54: mov             x2, x0
    // 0xbf6f58: ldur            x0, [fp, #-8]
    // 0xbf6f5c: stur            x2, [fp, #-0x18]
    // 0xbf6f60: StoreField: r2->field_f = r0
    //     0xbf6f60: stur            w0, [x2, #0xf]
    // 0xbf6f64: ldur            x1, [fp, #-0x10]
    // 0xbf6f68: r0 = of()
    //     0xbf6f68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf6f6c: LoadField: r1 = r0->field_5b
    //     0xbf6f6c: ldur            w1, [x0, #0x5b]
    // 0xbf6f70: DecompressPointer r1
    //     0xbf6f70: add             x1, x1, HEAP, lsl #32
    // 0xbf6f74: r0 = LoadClassIdInstr(r1)
    //     0xbf6f74: ldur            x0, [x1, #-1]
    //     0xbf6f78: ubfx            x0, x0, #0xc, #0x14
    // 0xbf6f7c: d0 = 0.100000
    //     0xbf6f7c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbf6f80: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbf6f80: sub             lr, x0, #0xffa
    //     0xbf6f84: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6f88: blr             lr
    // 0xbf6f8c: mov             x2, x0
    // 0xbf6f90: r1 = Null
    //     0xbf6f90: mov             x1, NULL
    // 0xbf6f94: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf6f94: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf6f98: r0 = Border.all()
    //     0xbf6f98: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbf6f9c: stur            x0, [fp, #-0x20]
    // 0xbf6fa0: r0 = BoxDecoration()
    //     0xbf6fa0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf6fa4: mov             x1, x0
    // 0xbf6fa8: ldur            x0, [fp, #-0x20]
    // 0xbf6fac: stur            x1, [fp, #-0x28]
    // 0xbf6fb0: StoreField: r1->field_f = r0
    //     0xbf6fb0: stur            w0, [x1, #0xf]
    // 0xbf6fb4: r0 = Instance_BoxShape
    //     0xbf6fb4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf6fb8: ldr             x0, [x0, #0x80]
    // 0xbf6fbc: StoreField: r1->field_23 = r0
    //     0xbf6fbc: stur            w0, [x1, #0x23]
    // 0xbf6fc0: r0 = ImageHeaders.forImages()
    //     0xbf6fc0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf6fc4: mov             x3, x0
    // 0xbf6fc8: ldur            x0, [fp, #-8]
    // 0xbf6fcc: stur            x3, [fp, #-0x30]
    // 0xbf6fd0: LoadField: r1 = r0->field_b
    //     0xbf6fd0: ldur            w1, [x0, #0xb]
    // 0xbf6fd4: DecompressPointer r1
    //     0xbf6fd4: add             x1, x1, HEAP, lsl #32
    // 0xbf6fd8: cmp             w1, NULL
    // 0xbf6fdc: b.eq            #0xbf8788
    // 0xbf6fe0: LoadField: r2 = r1->field_b
    //     0xbf6fe0: ldur            w2, [x1, #0xb]
    // 0xbf6fe4: DecompressPointer r2
    //     0xbf6fe4: add             x2, x2, HEAP, lsl #32
    // 0xbf6fe8: LoadField: r1 = r2->field_b
    //     0xbf6fe8: ldur            w1, [x2, #0xb]
    // 0xbf6fec: DecompressPointer r1
    //     0xbf6fec: add             x1, x1, HEAP, lsl #32
    // 0xbf6ff0: cmp             w1, NULL
    // 0xbf6ff4: b.ne            #0xbf7000
    // 0xbf6ff8: r4 = ""
    //     0xbf6ff8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf6ffc: b               #0xbf7004
    // 0xbf7000: mov             x4, x1
    // 0xbf7004: stur            x4, [fp, #-0x20]
    // 0xbf7008: r1 = Function '<anonymous closure>':.
    //     0xbf7008: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a1d0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf700c: ldr             x1, [x1, #0x1d0]
    // 0xbf7010: r2 = Null
    //     0xbf7010: mov             x2, NULL
    // 0xbf7014: r0 = AllocateClosure()
    //     0xbf7014: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf7018: r1 = Function '<anonymous closure>':.
    //     0xbf7018: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a1d8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf701c: ldr             x1, [x1, #0x1d8]
    // 0xbf7020: r2 = Null
    //     0xbf7020: mov             x2, NULL
    // 0xbf7024: stur            x0, [fp, #-0x38]
    // 0xbf7028: r0 = AllocateClosure()
    //     0xbf7028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf702c: stur            x0, [fp, #-0x40]
    // 0xbf7030: r0 = CachedNetworkImage()
    //     0xbf7030: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf7034: stur            x0, [fp, #-0x48]
    // 0xbf7038: r16 = 108.000000
    //     0xbf7038: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a1e0] 108
    //     0xbf703c: ldr             x16, [x16, #0x1e0]
    // 0xbf7040: r30 = 72.000000
    //     0xbf7040: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6a1e8] 72
    //     0xbf7044: ldr             lr, [lr, #0x1e8]
    // 0xbf7048: stp             lr, x16, [SP, #0x20]
    // 0xbf704c: r16 = Instance_BoxFit
    //     0xbf704c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbf7050: ldr             x16, [x16, #0x118]
    // 0xbf7054: ldur            lr, [fp, #-0x30]
    // 0xbf7058: stp             lr, x16, [SP, #0x10]
    // 0xbf705c: ldur            x16, [fp, #-0x38]
    // 0xbf7060: ldur            lr, [fp, #-0x40]
    // 0xbf7064: stp             lr, x16, [SP]
    // 0xbf7068: mov             x1, x0
    // 0xbf706c: ldur            x2, [fp, #-0x20]
    // 0xbf7070: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x4, height, 0x2, httpHeaders, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xbf7070: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f120] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x4, "height", 0x2, "httpHeaders", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xbf7074: ldr             x4, [x4, #0x120]
    // 0xbf7078: r0 = CachedNetworkImage()
    //     0xbf7078: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf707c: ldur            x0, [fp, #-8]
    // 0xbf7080: LoadField: r1 = r0->field_b
    //     0xbf7080: ldur            w1, [x0, #0xb]
    // 0xbf7084: DecompressPointer r1
    //     0xbf7084: add             x1, x1, HEAP, lsl #32
    // 0xbf7088: cmp             w1, NULL
    // 0xbf708c: b.eq            #0xbf878c
    // 0xbf7090: ldur            x1, [fp, #-0x10]
    // 0xbf7094: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbf7094: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbf7098: r0 = _of()
    //     0xbf7098: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbf709c: LoadField: r1 = r0->field_7
    //     0xbf709c: ldur            w1, [x0, #7]
    // 0xbf70a0: DecompressPointer r1
    //     0xbf70a0: add             x1, x1, HEAP, lsl #32
    // 0xbf70a4: LoadField: d0 = r1->field_7
    //     0xbf70a4: ldur            d0, [x1, #7]
    // 0xbf70a8: d1 = 0.500000
    //     0xbf70a8: fmov            d1, #0.50000000
    // 0xbf70ac: fmul            d2, d0, d1
    // 0xbf70b0: ldur            x0, [fp, #-8]
    // 0xbf70b4: stur            d2, [fp, #-0x78]
    // 0xbf70b8: LoadField: r1 = r0->field_b
    //     0xbf70b8: ldur            w1, [x0, #0xb]
    // 0xbf70bc: DecompressPointer r1
    //     0xbf70bc: add             x1, x1, HEAP, lsl #32
    // 0xbf70c0: cmp             w1, NULL
    // 0xbf70c4: b.eq            #0xbf8790
    // 0xbf70c8: LoadField: r2 = r1->field_b
    //     0xbf70c8: ldur            w2, [x1, #0xb]
    // 0xbf70cc: DecompressPointer r2
    //     0xbf70cc: add             x2, x2, HEAP, lsl #32
    // 0xbf70d0: LoadField: r1 = r2->field_5b
    //     0xbf70d0: ldur            w1, [x2, #0x5b]
    // 0xbf70d4: DecompressPointer r1
    //     0xbf70d4: add             x1, x1, HEAP, lsl #32
    // 0xbf70d8: cmp             w1, NULL
    // 0xbf70dc: b.ne            #0xbf70e8
    // 0xbf70e0: r2 = false
    //     0xbf70e0: add             x2, NULL, #0x30  ; false
    // 0xbf70e4: b               #0xbf70ec
    // 0xbf70e8: mov             x2, x1
    // 0xbf70ec: ldur            x1, [fp, #-0x10]
    // 0xbf70f0: stur            x2, [fp, #-0x20]
    // 0xbf70f4: r0 = of()
    //     0xbf70f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf70f8: LoadField: r1 = r0->field_5b
    //     0xbf70f8: ldur            w1, [x0, #0x5b]
    // 0xbf70fc: DecompressPointer r1
    //     0xbf70fc: add             x1, x1, HEAP, lsl #32
    // 0xbf7100: stur            x1, [fp, #-0x30]
    // 0xbf7104: r0 = BoxDecoration()
    //     0xbf7104: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf7108: mov             x2, x0
    // 0xbf710c: ldur            x0, [fp, #-0x30]
    // 0xbf7110: stur            x2, [fp, #-0x38]
    // 0xbf7114: StoreField: r2->field_7 = r0
    //     0xbf7114: stur            w0, [x2, #7]
    // 0xbf7118: r0 = Instance_BoxShape
    //     0xbf7118: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf711c: ldr             x0, [x0, #0x80]
    // 0xbf7120: StoreField: r2->field_23 = r0
    //     0xbf7120: stur            w0, [x2, #0x23]
    // 0xbf7124: ldur            x1, [fp, #-0x10]
    // 0xbf7128: r0 = of()
    //     0xbf7128: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf712c: LoadField: r1 = r0->field_87
    //     0xbf712c: ldur            w1, [x0, #0x87]
    // 0xbf7130: DecompressPointer r1
    //     0xbf7130: add             x1, x1, HEAP, lsl #32
    // 0xbf7134: LoadField: r0 = r1->field_7
    //     0xbf7134: ldur            w0, [x1, #7]
    // 0xbf7138: DecompressPointer r0
    //     0xbf7138: add             x0, x0, HEAP, lsl #32
    // 0xbf713c: r16 = 12.000000
    //     0xbf713c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7140: ldr             x16, [x16, #0x9e8]
    // 0xbf7144: r30 = Instance_Color
    //     0xbf7144: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf7148: stp             lr, x16, [SP]
    // 0xbf714c: mov             x1, x0
    // 0xbf7150: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf7150: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf7154: ldr             x4, [x4, #0xaa0]
    // 0xbf7158: r0 = copyWith()
    //     0xbf7158: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf715c: stur            x0, [fp, #-0x30]
    // 0xbf7160: r0 = Text()
    //     0xbf7160: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf7164: mov             x1, x0
    // 0xbf7168: r0 = "Exchange"
    //     0xbf7168: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ee8] "Exchange"
    //     0xbf716c: ldr             x0, [x0, #0xee8]
    // 0xbf7170: stur            x1, [fp, #-0x40]
    // 0xbf7174: StoreField: r1->field_b = r0
    //     0xbf7174: stur            w0, [x1, #0xb]
    // 0xbf7178: ldur            x0, [fp, #-0x30]
    // 0xbf717c: StoreField: r1->field_13 = r0
    //     0xbf717c: stur            w0, [x1, #0x13]
    // 0xbf7180: r0 = Container()
    //     0xbf7180: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf7184: stur            x0, [fp, #-0x30]
    // 0xbf7188: r16 = Instance_EdgeInsets
    //     0xbf7188: add             x16, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xbf718c: ldr             x16, [x16, #0xdb0]
    // 0xbf7190: ldur            lr, [fp, #-0x38]
    // 0xbf7194: stp             lr, x16, [SP, #8]
    // 0xbf7198: ldur            x16, [fp, #-0x40]
    // 0xbf719c: str             x16, [SP]
    // 0xbf71a0: mov             x1, x0
    // 0xbf71a4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xbf71a4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xbf71a8: ldr             x4, [x4, #0x610]
    // 0xbf71ac: r0 = Container()
    //     0xbf71ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf71b0: r0 = Visibility()
    //     0xbf71b0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf71b4: mov             x2, x0
    // 0xbf71b8: ldur            x0, [fp, #-0x30]
    // 0xbf71bc: stur            x2, [fp, #-0x38]
    // 0xbf71c0: StoreField: r2->field_b = r0
    //     0xbf71c0: stur            w0, [x2, #0xb]
    // 0xbf71c4: r0 = Instance_SizedBox
    //     0xbf71c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf71c8: StoreField: r2->field_f = r0
    //     0xbf71c8: stur            w0, [x2, #0xf]
    // 0xbf71cc: ldur            x1, [fp, #-0x20]
    // 0xbf71d0: StoreField: r2->field_13 = r1
    //     0xbf71d0: stur            w1, [x2, #0x13]
    // 0xbf71d4: r3 = false
    //     0xbf71d4: add             x3, NULL, #0x30  ; false
    // 0xbf71d8: ArrayStore: r2[0] = r3  ; List_4
    //     0xbf71d8: stur            w3, [x2, #0x17]
    // 0xbf71dc: StoreField: r2->field_1b = r3
    //     0xbf71dc: stur            w3, [x2, #0x1b]
    // 0xbf71e0: StoreField: r2->field_1f = r3
    //     0xbf71e0: stur            w3, [x2, #0x1f]
    // 0xbf71e4: StoreField: r2->field_23 = r3
    //     0xbf71e4: stur            w3, [x2, #0x23]
    // 0xbf71e8: StoreField: r2->field_27 = r3
    //     0xbf71e8: stur            w3, [x2, #0x27]
    // 0xbf71ec: StoreField: r2->field_2b = r3
    //     0xbf71ec: stur            w3, [x2, #0x2b]
    // 0xbf71f0: ldur            x4, [fp, #-8]
    // 0xbf71f4: LoadField: r1 = r4->field_b
    //     0xbf71f4: ldur            w1, [x4, #0xb]
    // 0xbf71f8: DecompressPointer r1
    //     0xbf71f8: add             x1, x1, HEAP, lsl #32
    // 0xbf71fc: cmp             w1, NULL
    // 0xbf7200: b.eq            #0xbf8794
    // 0xbf7204: LoadField: r5 = r1->field_b
    //     0xbf7204: ldur            w5, [x1, #0xb]
    // 0xbf7208: DecompressPointer r5
    //     0xbf7208: add             x5, x5, HEAP, lsl #32
    // 0xbf720c: LoadField: r1 = r5->field_f
    //     0xbf720c: ldur            w1, [x5, #0xf]
    // 0xbf7210: DecompressPointer r1
    //     0xbf7210: add             x1, x1, HEAP, lsl #32
    // 0xbf7214: cmp             w1, NULL
    // 0xbf7218: b.ne            #0xbf7220
    // 0xbf721c: r1 = ""
    //     0xbf721c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf7220: r0 = capitalizeFirstWord()
    //     0xbf7220: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xbf7224: ldur            x1, [fp, #-0x10]
    // 0xbf7228: stur            x0, [fp, #-0x20]
    // 0xbf722c: r0 = of()
    //     0xbf722c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7230: LoadField: r1 = r0->field_87
    //     0xbf7230: ldur            w1, [x0, #0x87]
    // 0xbf7234: DecompressPointer r1
    //     0xbf7234: add             x1, x1, HEAP, lsl #32
    // 0xbf7238: LoadField: r0 = r1->field_f
    //     0xbf7238: ldur            w0, [x1, #0xf]
    // 0xbf723c: DecompressPointer r0
    //     0xbf723c: add             x0, x0, HEAP, lsl #32
    // 0xbf7240: cmp             w0, NULL
    // 0xbf7244: b.ne            #0xbf7250
    // 0xbf7248: r2 = Null
    //     0xbf7248: mov             x2, NULL
    // 0xbf724c: b               #0xbf7274
    // 0xbf7250: r16 = Instance_Color
    //     0xbf7250: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf7254: r30 = 12.000000
    //     0xbf7254: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7258: ldr             lr, [lr, #0x9e8]
    // 0xbf725c: stp             lr, x16, [SP]
    // 0xbf7260: mov             x1, x0
    // 0xbf7264: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbf7264: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbf7268: ldr             x4, [x4, #0x9b8]
    // 0xbf726c: r0 = copyWith()
    //     0xbf726c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7270: mov             x2, x0
    // 0xbf7274: ldur            x1, [fp, #-8]
    // 0xbf7278: ldur            x0, [fp, #-0x20]
    // 0xbf727c: stur            x2, [fp, #-0x30]
    // 0xbf7280: r0 = Text()
    //     0xbf7280: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf7284: mov             x1, x0
    // 0xbf7288: ldur            x0, [fp, #-0x20]
    // 0xbf728c: stur            x1, [fp, #-0x40]
    // 0xbf7290: StoreField: r1->field_b = r0
    //     0xbf7290: stur            w0, [x1, #0xb]
    // 0xbf7294: ldur            x0, [fp, #-0x30]
    // 0xbf7298: StoreField: r1->field_13 = r0
    //     0xbf7298: stur            w0, [x1, #0x13]
    // 0xbf729c: r0 = Instance_TextOverflow
    //     0xbf729c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbf72a0: ldr             x0, [x0, #0xe10]
    // 0xbf72a4: StoreField: r1->field_2b = r0
    //     0xbf72a4: stur            w0, [x1, #0x2b]
    // 0xbf72a8: r2 = 4
    //     0xbf72a8: movz            x2, #0x4
    // 0xbf72ac: StoreField: r1->field_37 = r2
    //     0xbf72ac: stur            w2, [x1, #0x37]
    // 0xbf72b0: r0 = Padding()
    //     0xbf72b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf72b4: mov             x2, x0
    // 0xbf72b8: r1 = Instance_EdgeInsets
    //     0xbf72b8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf72bc: ldr             x1, [x1, #0x668]
    // 0xbf72c0: stur            x2, [fp, #-0x20]
    // 0xbf72c4: StoreField: r2->field_f = r1
    //     0xbf72c4: stur            w1, [x2, #0xf]
    // 0xbf72c8: ldur            x0, [fp, #-0x40]
    // 0xbf72cc: StoreField: r2->field_b = r0
    //     0xbf72cc: stur            w0, [x2, #0xb]
    // 0xbf72d0: ldur            x3, [fp, #-8]
    // 0xbf72d4: LoadField: r0 = r3->field_b
    //     0xbf72d4: ldur            w0, [x3, #0xb]
    // 0xbf72d8: DecompressPointer r0
    //     0xbf72d8: add             x0, x0, HEAP, lsl #32
    // 0xbf72dc: cmp             w0, NULL
    // 0xbf72e0: b.eq            #0xbf8798
    // 0xbf72e4: LoadField: r4 = r0->field_b
    //     0xbf72e4: ldur            w4, [x0, #0xb]
    // 0xbf72e8: DecompressPointer r4
    //     0xbf72e8: add             x4, x4, HEAP, lsl #32
    // 0xbf72ec: LoadField: r0 = r4->field_47
    //     0xbf72ec: ldur            w0, [x4, #0x47]
    // 0xbf72f0: DecompressPointer r0
    //     0xbf72f0: add             x0, x0, HEAP, lsl #32
    // 0xbf72f4: r4 = LoadClassIdInstr(r0)
    //     0xbf72f4: ldur            x4, [x0, #-1]
    //     0xbf72f8: ubfx            x4, x4, #0xc, #0x14
    // 0xbf72fc: r16 = "size"
    //     0xbf72fc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbf7300: ldr             x16, [x16, #0x9c0]
    // 0xbf7304: stp             x16, x0, [SP]
    // 0xbf7308: mov             x0, x4
    // 0xbf730c: mov             lr, x0
    // 0xbf7310: ldr             lr, [x21, lr, lsl #3]
    // 0xbf7314: blr             lr
    // 0xbf7318: tbnz            w0, #4, #0xbf73a0
    // 0xbf731c: ldur            x0, [fp, #-8]
    // 0xbf7320: r1 = Null
    //     0xbf7320: mov             x1, NULL
    // 0xbf7324: r2 = 8
    //     0xbf7324: movz            x2, #0x8
    // 0xbf7328: r0 = AllocateArray()
    //     0xbf7328: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf732c: r16 = "Size :  "
    //     0xbf732c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0xbf7330: ldr             x16, [x16, #0x758]
    // 0xbf7334: StoreField: r0->field_f = r16
    //     0xbf7334: stur            w16, [x0, #0xf]
    // 0xbf7338: ldur            x1, [fp, #-8]
    // 0xbf733c: LoadField: r2 = r1->field_b
    //     0xbf733c: ldur            w2, [x1, #0xb]
    // 0xbf7340: DecompressPointer r2
    //     0xbf7340: add             x2, x2, HEAP, lsl #32
    // 0xbf7344: cmp             w2, NULL
    // 0xbf7348: b.eq            #0xbf879c
    // 0xbf734c: LoadField: r3 = r2->field_b
    //     0xbf734c: ldur            w3, [x2, #0xb]
    // 0xbf7350: DecompressPointer r3
    //     0xbf7350: add             x3, x3, HEAP, lsl #32
    // 0xbf7354: LoadField: r2 = r3->field_1f
    //     0xbf7354: ldur            w2, [x3, #0x1f]
    // 0xbf7358: DecompressPointer r2
    //     0xbf7358: add             x2, x2, HEAP, lsl #32
    // 0xbf735c: cmp             w2, NULL
    // 0xbf7360: b.ne            #0xbf7368
    // 0xbf7364: r2 = ""
    //     0xbf7364: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf7368: StoreField: r0->field_13 = r2
    //     0xbf7368: stur            w2, [x0, #0x13]
    // 0xbf736c: r16 = " / Qty:"
    //     0xbf736c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a1f0] " / Qty:"
    //     0xbf7370: ldr             x16, [x16, #0x1f0]
    // 0xbf7374: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf7374: stur            w16, [x0, #0x17]
    // 0xbf7378: LoadField: r2 = r3->field_23
    //     0xbf7378: ldur            w2, [x3, #0x23]
    // 0xbf737c: DecompressPointer r2
    //     0xbf737c: add             x2, x2, HEAP, lsl #32
    // 0xbf7380: cmp             w2, NULL
    // 0xbf7384: b.ne            #0xbf738c
    // 0xbf7388: r2 = ""
    //     0xbf7388: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf738c: StoreField: r0->field_1b = r2
    //     0xbf738c: stur            w2, [x0, #0x1b]
    // 0xbf7390: str             x0, [SP]
    // 0xbf7394: r0 = _interpolate()
    //     0xbf7394: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf7398: mov             x2, x0
    // 0xbf739c: b               #0xbf7414
    // 0xbf73a0: ldur            x0, [fp, #-8]
    // 0xbf73a4: r1 = Null
    //     0xbf73a4: mov             x1, NULL
    // 0xbf73a8: r2 = 8
    //     0xbf73a8: movz            x2, #0x8
    // 0xbf73ac: r0 = AllocateArray()
    //     0xbf73ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf73b0: r16 = "Variant : "
    //     0xbf73b0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0xbf73b4: ldr             x16, [x16, #0x768]
    // 0xbf73b8: StoreField: r0->field_f = r16
    //     0xbf73b8: stur            w16, [x0, #0xf]
    // 0xbf73bc: ldur            x1, [fp, #-8]
    // 0xbf73c0: LoadField: r2 = r1->field_b
    //     0xbf73c0: ldur            w2, [x1, #0xb]
    // 0xbf73c4: DecompressPointer r2
    //     0xbf73c4: add             x2, x2, HEAP, lsl #32
    // 0xbf73c8: cmp             w2, NULL
    // 0xbf73cc: b.eq            #0xbf87a0
    // 0xbf73d0: LoadField: r3 = r2->field_b
    //     0xbf73d0: ldur            w3, [x2, #0xb]
    // 0xbf73d4: DecompressPointer r3
    //     0xbf73d4: add             x3, x3, HEAP, lsl #32
    // 0xbf73d8: LoadField: r2 = r3->field_1f
    //     0xbf73d8: ldur            w2, [x3, #0x1f]
    // 0xbf73dc: DecompressPointer r2
    //     0xbf73dc: add             x2, x2, HEAP, lsl #32
    // 0xbf73e0: StoreField: r0->field_13 = r2
    //     0xbf73e0: stur            w2, [x0, #0x13]
    // 0xbf73e4: r16 = " / Qty:"
    //     0xbf73e4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a1f0] " / Qty:"
    //     0xbf73e8: ldr             x16, [x16, #0x1f0]
    // 0xbf73ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf73ec: stur            w16, [x0, #0x17]
    // 0xbf73f0: LoadField: r2 = r3->field_23
    //     0xbf73f0: ldur            w2, [x3, #0x23]
    // 0xbf73f4: DecompressPointer r2
    //     0xbf73f4: add             x2, x2, HEAP, lsl #32
    // 0xbf73f8: cmp             w2, NULL
    // 0xbf73fc: b.ne            #0xbf7404
    // 0xbf7400: r2 = ""
    //     0xbf7400: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf7404: StoreField: r0->field_1b = r2
    //     0xbf7404: stur            w2, [x0, #0x1b]
    // 0xbf7408: str             x0, [SP]
    // 0xbf740c: r0 = _interpolate()
    //     0xbf740c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf7410: mov             x2, x0
    // 0xbf7414: ldur            x0, [fp, #-8]
    // 0xbf7418: ldur            x1, [fp, #-0x10]
    // 0xbf741c: stur            x2, [fp, #-0x30]
    // 0xbf7420: r0 = of()
    //     0xbf7420: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7424: LoadField: r1 = r0->field_87
    //     0xbf7424: ldur            w1, [x0, #0x87]
    // 0xbf7428: DecompressPointer r1
    //     0xbf7428: add             x1, x1, HEAP, lsl #32
    // 0xbf742c: LoadField: r0 = r1->field_2b
    //     0xbf742c: ldur            w0, [x1, #0x2b]
    // 0xbf7430: DecompressPointer r0
    //     0xbf7430: add             x0, x0, HEAP, lsl #32
    // 0xbf7434: r16 = 12.000000
    //     0xbf7434: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7438: ldr             x16, [x16, #0x9e8]
    // 0xbf743c: r30 = Instance_Color
    //     0xbf743c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf7440: stp             lr, x16, [SP]
    // 0xbf7444: mov             x1, x0
    // 0xbf7448: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf7448: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf744c: ldr             x4, [x4, #0xaa0]
    // 0xbf7450: r0 = copyWith()
    //     0xbf7450: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7454: stur            x0, [fp, #-0x40]
    // 0xbf7458: r0 = Text()
    //     0xbf7458: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf745c: mov             x1, x0
    // 0xbf7460: ldur            x0, [fp, #-0x30]
    // 0xbf7464: stur            x1, [fp, #-0x50]
    // 0xbf7468: StoreField: r1->field_b = r0
    //     0xbf7468: stur            w0, [x1, #0xb]
    // 0xbf746c: ldur            x0, [fp, #-0x40]
    // 0xbf7470: StoreField: r1->field_13 = r0
    //     0xbf7470: stur            w0, [x1, #0x13]
    // 0xbf7474: r0 = Padding()
    //     0xbf7474: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf7478: mov             x1, x0
    // 0xbf747c: r0 = Instance_EdgeInsets
    //     0xbf747c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbf7480: ldr             x0, [x0, #0x770]
    // 0xbf7484: stur            x1, [fp, #-0x30]
    // 0xbf7488: StoreField: r1->field_f = r0
    //     0xbf7488: stur            w0, [x1, #0xf]
    // 0xbf748c: ldur            x2, [fp, #-0x50]
    // 0xbf7490: StoreField: r1->field_b = r2
    //     0xbf7490: stur            w2, [x1, #0xb]
    // 0xbf7494: r0 = Visibility()
    //     0xbf7494: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf7498: mov             x2, x0
    // 0xbf749c: ldur            x0, [fp, #-0x30]
    // 0xbf74a0: stur            x2, [fp, #-0x40]
    // 0xbf74a4: StoreField: r2->field_b = r0
    //     0xbf74a4: stur            w0, [x2, #0xb]
    // 0xbf74a8: r0 = Instance_SizedBox
    //     0xbf74a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf74ac: StoreField: r2->field_f = r0
    //     0xbf74ac: stur            w0, [x2, #0xf]
    // 0xbf74b0: r3 = true
    //     0xbf74b0: add             x3, NULL, #0x20  ; true
    // 0xbf74b4: StoreField: r2->field_13 = r3
    //     0xbf74b4: stur            w3, [x2, #0x13]
    // 0xbf74b8: r4 = false
    //     0xbf74b8: add             x4, NULL, #0x30  ; false
    // 0xbf74bc: ArrayStore: r2[0] = r4  ; List_4
    //     0xbf74bc: stur            w4, [x2, #0x17]
    // 0xbf74c0: StoreField: r2->field_1b = r4
    //     0xbf74c0: stur            w4, [x2, #0x1b]
    // 0xbf74c4: StoreField: r2->field_1f = r4
    //     0xbf74c4: stur            w4, [x2, #0x1f]
    // 0xbf74c8: StoreField: r2->field_23 = r4
    //     0xbf74c8: stur            w4, [x2, #0x23]
    // 0xbf74cc: StoreField: r2->field_27 = r4
    //     0xbf74cc: stur            w4, [x2, #0x27]
    // 0xbf74d0: StoreField: r2->field_2b = r4
    //     0xbf74d0: stur            w4, [x2, #0x2b]
    // 0xbf74d4: ldur            x5, [fp, #-8]
    // 0xbf74d8: LoadField: r1 = r5->field_b
    //     0xbf74d8: ldur            w1, [x5, #0xb]
    // 0xbf74dc: DecompressPointer r1
    //     0xbf74dc: add             x1, x1, HEAP, lsl #32
    // 0xbf74e0: cmp             w1, NULL
    // 0xbf74e4: b.eq            #0xbf87a4
    // 0xbf74e8: LoadField: r6 = r1->field_b
    //     0xbf74e8: ldur            w6, [x1, #0xb]
    // 0xbf74ec: DecompressPointer r6
    //     0xbf74ec: add             x6, x6, HEAP, lsl #32
    // 0xbf74f0: LoadField: r1 = r6->field_13
    //     0xbf74f0: ldur            w1, [x6, #0x13]
    // 0xbf74f4: DecompressPointer r1
    //     0xbf74f4: add             x1, x1, HEAP, lsl #32
    // 0xbf74f8: cmp             w1, NULL
    // 0xbf74fc: b.ne            #0xbf7508
    // 0xbf7500: r6 = ""
    //     0xbf7500: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf7504: b               #0xbf750c
    // 0xbf7508: mov             x6, x1
    // 0xbf750c: ldur            x1, [fp, #-0x10]
    // 0xbf7510: stur            x6, [fp, #-0x30]
    // 0xbf7514: r0 = of()
    //     0xbf7514: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7518: LoadField: r1 = r0->field_87
    //     0xbf7518: ldur            w1, [x0, #0x87]
    // 0xbf751c: DecompressPointer r1
    //     0xbf751c: add             x1, x1, HEAP, lsl #32
    // 0xbf7520: LoadField: r2 = r1->field_7
    //     0xbf7520: ldur            w2, [x1, #7]
    // 0xbf7524: DecompressPointer r2
    //     0xbf7524: add             x2, x2, HEAP, lsl #32
    // 0xbf7528: ldur            x1, [fp, #-8]
    // 0xbf752c: stur            x2, [fp, #-0x50]
    // 0xbf7530: LoadField: r0 = r1->field_b
    //     0xbf7530: ldur            w0, [x1, #0xb]
    // 0xbf7534: DecompressPointer r0
    //     0xbf7534: add             x0, x0, HEAP, lsl #32
    // 0xbf7538: cmp             w0, NULL
    // 0xbf753c: b.eq            #0xbf87a8
    // 0xbf7540: LoadField: r3 = r0->field_b
    //     0xbf7540: ldur            w3, [x0, #0xb]
    // 0xbf7544: DecompressPointer r3
    //     0xbf7544: add             x3, x3, HEAP, lsl #32
    // 0xbf7548: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbf7548: ldur            w0, [x3, #0x17]
    // 0xbf754c: DecompressPointer r0
    //     0xbf754c: add             x0, x0, HEAP, lsl #32
    // 0xbf7550: r3 = LoadClassIdInstr(r0)
    //     0xbf7550: ldur            x3, [x0, #-1]
    //     0xbf7554: ubfx            x3, x3, #0xc, #0x14
    // 0xbf7558: r16 = "pending"
    //     0xbf7558: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec0] "pending"
    //     0xbf755c: ldr             x16, [x16, #0xec0]
    // 0xbf7560: stp             x16, x0, [SP]
    // 0xbf7564: mov             x0, x3
    // 0xbf7568: mov             lr, x0
    // 0xbf756c: ldr             lr, [x21, lr, lsl #3]
    // 0xbf7570: blr             lr
    // 0xbf7574: tbnz            w0, #4, #0xbf7584
    // 0xbf7578: r1 = Instance_MaterialColor
    //     0xbf7578: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!MaterialColor@d6bda1
    //     0xbf757c: ldr             x1, [x1, #0xec8]
    // 0xbf7580: b               #0xbf7634
    // 0xbf7584: ldur            x1, [fp, #-8]
    // 0xbf7588: LoadField: r0 = r1->field_b
    //     0xbf7588: ldur            w0, [x1, #0xb]
    // 0xbf758c: DecompressPointer r0
    //     0xbf758c: add             x0, x0, HEAP, lsl #32
    // 0xbf7590: cmp             w0, NULL
    // 0xbf7594: b.eq            #0xbf87ac
    // 0xbf7598: LoadField: r2 = r0->field_b
    //     0xbf7598: ldur            w2, [x0, #0xb]
    // 0xbf759c: DecompressPointer r2
    //     0xbf759c: add             x2, x2, HEAP, lsl #32
    // 0xbf75a0: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbf75a0: ldur            w0, [x2, #0x17]
    // 0xbf75a4: DecompressPointer r0
    //     0xbf75a4: add             x0, x0, HEAP, lsl #32
    // 0xbf75a8: r2 = LoadClassIdInstr(r0)
    //     0xbf75a8: ldur            x2, [x0, #-1]
    //     0xbf75ac: ubfx            x2, x2, #0xc, #0x14
    // 0xbf75b0: r16 = "cancel_initiated"
    //     0xbf75b0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ed0] "cancel_initiated"
    //     0xbf75b4: ldr             x16, [x16, #0xed0]
    // 0xbf75b8: stp             x16, x0, [SP]
    // 0xbf75bc: mov             x0, x2
    // 0xbf75c0: mov             lr, x0
    // 0xbf75c4: ldr             lr, [x21, lr, lsl #3]
    // 0xbf75c8: blr             lr
    // 0xbf75cc: tbz             w0, #4, #0xbf761c
    // 0xbf75d0: ldur            x1, [fp, #-8]
    // 0xbf75d4: LoadField: r0 = r1->field_b
    //     0xbf75d4: ldur            w0, [x1, #0xb]
    // 0xbf75d8: DecompressPointer r0
    //     0xbf75d8: add             x0, x0, HEAP, lsl #32
    // 0xbf75dc: cmp             w0, NULL
    // 0xbf75e0: b.eq            #0xbf87b0
    // 0xbf75e4: LoadField: r2 = r0->field_b
    //     0xbf75e4: ldur            w2, [x0, #0xb]
    // 0xbf75e8: DecompressPointer r2
    //     0xbf75e8: add             x2, x2, HEAP, lsl #32
    // 0xbf75ec: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbf75ec: ldur            w0, [x2, #0x17]
    // 0xbf75f0: DecompressPointer r0
    //     0xbf75f0: add             x0, x0, HEAP, lsl #32
    // 0xbf75f4: r2 = LoadClassIdInstr(r0)
    //     0xbf75f4: ldur            x2, [x0, #-1]
    //     0xbf75f8: ubfx            x2, x2, #0xc, #0x14
    // 0xbf75fc: r16 = "failure"
    //     0xbf75fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ebe8] "failure"
    //     0xbf7600: ldr             x16, [x16, #0xbe8]
    // 0xbf7604: stp             x16, x0, [SP]
    // 0xbf7608: mov             x0, x2
    // 0xbf760c: mov             lr, x0
    // 0xbf7610: ldr             lr, [x21, lr, lsl #3]
    // 0xbf7614: blr             lr
    // 0xbf7618: tbnz            w0, #4, #0xbf7628
    // 0xbf761c: r0 = Instance_MaterialAccentColor
    //     0xbf761c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!MaterialAccentColor@d6bca1
    //     0xbf7620: ldr             x0, [x0, #0xed8]
    // 0xbf7624: b               #0xbf7630
    // 0xbf7628: r0 = Instance_Color
    //     0xbf7628: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbf762c: ldr             x0, [x0, #0x858]
    // 0xbf7630: mov             x1, x0
    // 0xbf7634: ldur            x0, [fp, #-8]
    // 0xbf7638: ldur            x2, [fp, #-0x30]
    // 0xbf763c: r16 = 12.000000
    //     0xbf763c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7640: ldr             x16, [x16, #0x9e8]
    // 0xbf7644: stp             x1, x16, [SP]
    // 0xbf7648: ldur            x1, [fp, #-0x50]
    // 0xbf764c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf764c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf7650: ldr             x4, [x4, #0xaa0]
    // 0xbf7654: r0 = copyWith()
    //     0xbf7654: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7658: stur            x0, [fp, #-0x50]
    // 0xbf765c: r0 = Text()
    //     0xbf765c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf7660: mov             x1, x0
    // 0xbf7664: ldur            x0, [fp, #-0x30]
    // 0xbf7668: stur            x1, [fp, #-0x58]
    // 0xbf766c: StoreField: r1->field_b = r0
    //     0xbf766c: stur            w0, [x1, #0xb]
    // 0xbf7670: ldur            x0, [fp, #-0x50]
    // 0xbf7674: StoreField: r1->field_13 = r0
    //     0xbf7674: stur            w0, [x1, #0x13]
    // 0xbf7678: r0 = Padding()
    //     0xbf7678: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf767c: mov             x1, x0
    // 0xbf7680: r0 = Instance_EdgeInsets
    //     0xbf7680: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbf7684: ldr             x0, [x0, #0x770]
    // 0xbf7688: stur            x1, [fp, #-0x30]
    // 0xbf768c: StoreField: r1->field_f = r0
    //     0xbf768c: stur            w0, [x1, #0xf]
    // 0xbf7690: ldur            x0, [fp, #-0x58]
    // 0xbf7694: StoreField: r1->field_b = r0
    //     0xbf7694: stur            w0, [x1, #0xb]
    // 0xbf7698: r0 = Visibility()
    //     0xbf7698: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf769c: mov             x3, x0
    // 0xbf76a0: ldur            x0, [fp, #-0x30]
    // 0xbf76a4: stur            x3, [fp, #-0x50]
    // 0xbf76a8: StoreField: r3->field_b = r0
    //     0xbf76a8: stur            w0, [x3, #0xb]
    // 0xbf76ac: r0 = Instance_SizedBox
    //     0xbf76ac: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf76b0: StoreField: r3->field_f = r0
    //     0xbf76b0: stur            w0, [x3, #0xf]
    // 0xbf76b4: r4 = true
    //     0xbf76b4: add             x4, NULL, #0x20  ; true
    // 0xbf76b8: StoreField: r3->field_13 = r4
    //     0xbf76b8: stur            w4, [x3, #0x13]
    // 0xbf76bc: r5 = false
    //     0xbf76bc: add             x5, NULL, #0x30  ; false
    // 0xbf76c0: ArrayStore: r3[0] = r5  ; List_4
    //     0xbf76c0: stur            w5, [x3, #0x17]
    // 0xbf76c4: StoreField: r3->field_1b = r5
    //     0xbf76c4: stur            w5, [x3, #0x1b]
    // 0xbf76c8: StoreField: r3->field_1f = r5
    //     0xbf76c8: stur            w5, [x3, #0x1f]
    // 0xbf76cc: StoreField: r3->field_23 = r5
    //     0xbf76cc: stur            w5, [x3, #0x23]
    // 0xbf76d0: StoreField: r3->field_27 = r5
    //     0xbf76d0: stur            w5, [x3, #0x27]
    // 0xbf76d4: StoreField: r3->field_2b = r5
    //     0xbf76d4: stur            w5, [x3, #0x2b]
    // 0xbf76d8: ldur            x6, [fp, #-8]
    // 0xbf76dc: LoadField: r1 = r6->field_b
    //     0xbf76dc: ldur            w1, [x6, #0xb]
    // 0xbf76e0: DecompressPointer r1
    //     0xbf76e0: add             x1, x1, HEAP, lsl #32
    // 0xbf76e4: cmp             w1, NULL
    // 0xbf76e8: b.eq            #0xbf87b4
    // 0xbf76ec: LoadField: r2 = r1->field_b
    //     0xbf76ec: ldur            w2, [x1, #0xb]
    // 0xbf76f0: DecompressPointer r2
    //     0xbf76f0: add             x2, x2, HEAP, lsl #32
    // 0xbf76f4: LoadField: r1 = r2->field_3b
    //     0xbf76f4: ldur            w1, [x2, #0x3b]
    // 0xbf76f8: DecompressPointer r1
    //     0xbf76f8: add             x1, x1, HEAP, lsl #32
    // 0xbf76fc: cmp             w1, NULL
    // 0xbf7700: b.ne            #0xbf7718
    // 0xbf7704: r1 = <ProductCustomisation>
    //     0xbf7704: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xbf7708: ldr             x1, [x1, #0x370]
    // 0xbf770c: r2 = 0
    //     0xbf770c: movz            x2, #0
    // 0xbf7710: r0 = AllocateArray()
    //     0xbf7710: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf7714: mov             x1, x0
    // 0xbf7718: ldur            x3, [fp, #-8]
    // 0xbf771c: ldur            x7, [fp, #-0x48]
    // 0xbf7720: ldur            x6, [fp, #-0x38]
    // 0xbf7724: ldur            x5, [fp, #-0x20]
    // 0xbf7728: ldur            x4, [fp, #-0x40]
    // 0xbf772c: ldur            x2, [fp, #-0x50]
    // 0xbf7730: ldur            d0, [fp, #-0x78]
    // 0xbf7734: r0 = LoadClassIdInstr(r1)
    //     0xbf7734: ldur            x0, [x1, #-1]
    //     0xbf7738: ubfx            x0, x0, #0xc, #0x14
    // 0xbf773c: r0 = GDT[cid_x0 + 0xe517]()
    //     0xbf773c: movz            x17, #0xe517
    //     0xbf7740: add             lr, x0, x17
    //     0xbf7744: ldr             lr, [x21, lr, lsl #3]
    //     0xbf7748: blr             lr
    // 0xbf774c: ldur            x1, [fp, #-0x10]
    // 0xbf7750: stur            x0, [fp, #-0x30]
    // 0xbf7754: r0 = of()
    //     0xbf7754: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7758: LoadField: r1 = r0->field_87
    //     0xbf7758: ldur            w1, [x0, #0x87]
    // 0xbf775c: DecompressPointer r1
    //     0xbf775c: add             x1, x1, HEAP, lsl #32
    // 0xbf7760: LoadField: r0 = r1->field_7
    //     0xbf7760: ldur            w0, [x1, #7]
    // 0xbf7764: DecompressPointer r0
    //     0xbf7764: add             x0, x0, HEAP, lsl #32
    // 0xbf7768: stur            x0, [fp, #-0x58]
    // 0xbf776c: r1 = Instance_Color
    //     0xbf776c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf7770: d0 = 0.700000
    //     0xbf7770: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf7774: ldr             d0, [x17, #0xf48]
    // 0xbf7778: r0 = withOpacity()
    //     0xbf7778: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf777c: r16 = 12.000000
    //     0xbf777c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7780: ldr             x16, [x16, #0x9e8]
    // 0xbf7784: stp             x0, x16, [SP]
    // 0xbf7788: ldur            x1, [fp, #-0x58]
    // 0xbf778c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf778c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf7790: ldr             x4, [x4, #0xaa0]
    // 0xbf7794: r0 = copyWith()
    //     0xbf7794: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7798: stur            x0, [fp, #-0x58]
    // 0xbf779c: r0 = Text()
    //     0xbf779c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf77a0: mov             x1, x0
    // 0xbf77a4: r0 = "Customised"
    //     0xbf77a4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xbf77a8: ldr             x0, [x0, #0xd88]
    // 0xbf77ac: stur            x1, [fp, #-0x60]
    // 0xbf77b0: StoreField: r1->field_b = r0
    //     0xbf77b0: stur            w0, [x1, #0xb]
    // 0xbf77b4: ldur            x0, [fp, #-0x58]
    // 0xbf77b8: StoreField: r1->field_13 = r0
    //     0xbf77b8: stur            w0, [x1, #0x13]
    // 0xbf77bc: r0 = Padding()
    //     0xbf77bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf77c0: mov             x1, x0
    // 0xbf77c4: r0 = Instance_EdgeInsets
    //     0xbf77c4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf77c8: ldr             x0, [x0, #0x668]
    // 0xbf77cc: stur            x1, [fp, #-0x58]
    // 0xbf77d0: StoreField: r1->field_f = r0
    //     0xbf77d0: stur            w0, [x1, #0xf]
    // 0xbf77d4: ldur            x2, [fp, #-0x60]
    // 0xbf77d8: StoreField: r1->field_b = r2
    //     0xbf77d8: stur            w2, [x1, #0xb]
    // 0xbf77dc: r0 = Visibility()
    //     0xbf77dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf77e0: mov             x1, x0
    // 0xbf77e4: ldur            x0, [fp, #-0x58]
    // 0xbf77e8: stur            x1, [fp, #-0x60]
    // 0xbf77ec: StoreField: r1->field_b = r0
    //     0xbf77ec: stur            w0, [x1, #0xb]
    // 0xbf77f0: r0 = Instance_SizedBox
    //     0xbf77f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf77f4: StoreField: r1->field_f = r0
    //     0xbf77f4: stur            w0, [x1, #0xf]
    // 0xbf77f8: ldur            x2, [fp, #-0x30]
    // 0xbf77fc: StoreField: r1->field_13 = r2
    //     0xbf77fc: stur            w2, [x1, #0x13]
    // 0xbf7800: r2 = false
    //     0xbf7800: add             x2, NULL, #0x30  ; false
    // 0xbf7804: ArrayStore: r1[0] = r2  ; List_4
    //     0xbf7804: stur            w2, [x1, #0x17]
    // 0xbf7808: StoreField: r1->field_1b = r2
    //     0xbf7808: stur            w2, [x1, #0x1b]
    // 0xbf780c: StoreField: r1->field_1f = r2
    //     0xbf780c: stur            w2, [x1, #0x1f]
    // 0xbf7810: StoreField: r1->field_23 = r2
    //     0xbf7810: stur            w2, [x1, #0x23]
    // 0xbf7814: StoreField: r1->field_27 = r2
    //     0xbf7814: stur            w2, [x1, #0x27]
    // 0xbf7818: StoreField: r1->field_2b = r2
    //     0xbf7818: stur            w2, [x1, #0x2b]
    // 0xbf781c: ldur            x3, [fp, #-8]
    // 0xbf7820: LoadField: r4 = r3->field_b
    //     0xbf7820: ldur            w4, [x3, #0xb]
    // 0xbf7824: DecompressPointer r4
    //     0xbf7824: add             x4, x4, HEAP, lsl #32
    // 0xbf7828: cmp             w4, NULL
    // 0xbf782c: b.eq            #0xbf87b8
    // 0xbf7830: LoadField: r5 = r4->field_b
    //     0xbf7830: ldur            w5, [x4, #0xb]
    // 0xbf7834: DecompressPointer r5
    //     0xbf7834: add             x5, x5, HEAP, lsl #32
    // 0xbf7838: LoadField: r4 = r5->field_27
    //     0xbf7838: ldur            w4, [x5, #0x27]
    // 0xbf783c: DecompressPointer r4
    //     0xbf783c: add             x4, x4, HEAP, lsl #32
    // 0xbf7840: str             x4, [SP]
    // 0xbf7844: r0 = _interpolateSingle()
    //     0xbf7844: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbf7848: ldur            x1, [fp, #-0x10]
    // 0xbf784c: stur            x0, [fp, #-0x30]
    // 0xbf7850: r0 = of()
    //     0xbf7850: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7854: LoadField: r1 = r0->field_87
    //     0xbf7854: ldur            w1, [x0, #0x87]
    // 0xbf7858: DecompressPointer r1
    //     0xbf7858: add             x1, x1, HEAP, lsl #32
    // 0xbf785c: LoadField: r0 = r1->field_7
    //     0xbf785c: ldur            w0, [x1, #7]
    // 0xbf7860: DecompressPointer r0
    //     0xbf7860: add             x0, x0, HEAP, lsl #32
    // 0xbf7864: r16 = 12.000000
    //     0xbf7864: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7868: ldr             x16, [x16, #0x9e8]
    // 0xbf786c: r30 = Instance_Color
    //     0xbf786c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf7870: stp             lr, x16, [SP]
    // 0xbf7874: mov             x1, x0
    // 0xbf7878: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf7878: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf787c: ldr             x4, [x4, #0xaa0]
    // 0xbf7880: r0 = copyWith()
    //     0xbf7880: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7884: stur            x0, [fp, #-0x58]
    // 0xbf7888: r0 = Text()
    //     0xbf7888: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf788c: mov             x1, x0
    // 0xbf7890: ldur            x0, [fp, #-0x30]
    // 0xbf7894: stur            x1, [fp, #-0x68]
    // 0xbf7898: StoreField: r1->field_b = r0
    //     0xbf7898: stur            w0, [x1, #0xb]
    // 0xbf789c: ldur            x0, [fp, #-0x58]
    // 0xbf78a0: StoreField: r1->field_13 = r0
    //     0xbf78a0: stur            w0, [x1, #0x13]
    // 0xbf78a4: r0 = Visibility()
    //     0xbf78a4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf78a8: mov             x3, x0
    // 0xbf78ac: ldur            x0, [fp, #-0x68]
    // 0xbf78b0: stur            x3, [fp, #-0x30]
    // 0xbf78b4: StoreField: r3->field_b = r0
    //     0xbf78b4: stur            w0, [x3, #0xb]
    // 0xbf78b8: r0 = Instance_SizedBox
    //     0xbf78b8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf78bc: StoreField: r3->field_f = r0
    //     0xbf78bc: stur            w0, [x3, #0xf]
    // 0xbf78c0: r4 = false
    //     0xbf78c0: add             x4, NULL, #0x30  ; false
    // 0xbf78c4: StoreField: r3->field_13 = r4
    //     0xbf78c4: stur            w4, [x3, #0x13]
    // 0xbf78c8: ArrayStore: r3[0] = r4  ; List_4
    //     0xbf78c8: stur            w4, [x3, #0x17]
    // 0xbf78cc: StoreField: r3->field_1b = r4
    //     0xbf78cc: stur            w4, [x3, #0x1b]
    // 0xbf78d0: StoreField: r3->field_1f = r4
    //     0xbf78d0: stur            w4, [x3, #0x1f]
    // 0xbf78d4: StoreField: r3->field_23 = r4
    //     0xbf78d4: stur            w4, [x3, #0x23]
    // 0xbf78d8: StoreField: r3->field_27 = r4
    //     0xbf78d8: stur            w4, [x3, #0x27]
    // 0xbf78dc: StoreField: r3->field_2b = r4
    //     0xbf78dc: stur            w4, [x3, #0x2b]
    // 0xbf78e0: r1 = Null
    //     0xbf78e0: mov             x1, NULL
    // 0xbf78e4: r2 = 14
    //     0xbf78e4: movz            x2, #0xe
    // 0xbf78e8: r0 = AllocateArray()
    //     0xbf78e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf78ec: mov             x2, x0
    // 0xbf78f0: ldur            x0, [fp, #-0x38]
    // 0xbf78f4: stur            x2, [fp, #-0x58]
    // 0xbf78f8: StoreField: r2->field_f = r0
    //     0xbf78f8: stur            w0, [x2, #0xf]
    // 0xbf78fc: ldur            x0, [fp, #-0x20]
    // 0xbf7900: StoreField: r2->field_13 = r0
    //     0xbf7900: stur            w0, [x2, #0x13]
    // 0xbf7904: ldur            x0, [fp, #-0x40]
    // 0xbf7908: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf7908: stur            w0, [x2, #0x17]
    // 0xbf790c: ldur            x0, [fp, #-0x50]
    // 0xbf7910: StoreField: r2->field_1b = r0
    //     0xbf7910: stur            w0, [x2, #0x1b]
    // 0xbf7914: ldur            x0, [fp, #-0x60]
    // 0xbf7918: StoreField: r2->field_1f = r0
    //     0xbf7918: stur            w0, [x2, #0x1f]
    // 0xbf791c: r16 = Instance_SizedBox
    //     0xbf791c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbf7920: ldr             x16, [x16, #0xc70]
    // 0xbf7924: StoreField: r2->field_23 = r16
    //     0xbf7924: stur            w16, [x2, #0x23]
    // 0xbf7928: ldur            x0, [fp, #-0x30]
    // 0xbf792c: StoreField: r2->field_27 = r0
    //     0xbf792c: stur            w0, [x2, #0x27]
    // 0xbf7930: r1 = <Widget>
    //     0xbf7930: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf7934: r0 = AllocateGrowableArray()
    //     0xbf7934: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf7938: mov             x1, x0
    // 0xbf793c: ldur            x0, [fp, #-0x58]
    // 0xbf7940: stur            x1, [fp, #-0x20]
    // 0xbf7944: StoreField: r1->field_f = r0
    //     0xbf7944: stur            w0, [x1, #0xf]
    // 0xbf7948: r0 = 14
    //     0xbf7948: movz            x0, #0xe
    // 0xbf794c: StoreField: r1->field_b = r0
    //     0xbf794c: stur            w0, [x1, #0xb]
    // 0xbf7950: r0 = Column()
    //     0xbf7950: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf7954: mov             x1, x0
    // 0xbf7958: r0 = Instance_Axis
    //     0xbf7958: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf795c: stur            x1, [fp, #-0x30]
    // 0xbf7960: StoreField: r1->field_f = r0
    //     0xbf7960: stur            w0, [x1, #0xf]
    // 0xbf7964: r2 = Instance_MainAxisAlignment
    //     0xbf7964: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf7968: ldr             x2, [x2, #0xa08]
    // 0xbf796c: StoreField: r1->field_13 = r2
    //     0xbf796c: stur            w2, [x1, #0x13]
    // 0xbf7970: r3 = Instance_MainAxisSize
    //     0xbf7970: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbf7974: ldr             x3, [x3, #0xdd0]
    // 0xbf7978: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf7978: stur            w3, [x1, #0x17]
    // 0xbf797c: r3 = Instance_CrossAxisAlignment
    //     0xbf797c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf7980: ldr             x3, [x3, #0x890]
    // 0xbf7984: StoreField: r1->field_1b = r3
    //     0xbf7984: stur            w3, [x1, #0x1b]
    // 0xbf7988: r4 = Instance_VerticalDirection
    //     0xbf7988: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf798c: ldr             x4, [x4, #0xa20]
    // 0xbf7990: StoreField: r1->field_23 = r4
    //     0xbf7990: stur            w4, [x1, #0x23]
    // 0xbf7994: r5 = Instance_Clip
    //     0xbf7994: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf7998: ldr             x5, [x5, #0x38]
    // 0xbf799c: StoreField: r1->field_2b = r5
    //     0xbf799c: stur            w5, [x1, #0x2b]
    // 0xbf79a0: StoreField: r1->field_2f = rZR
    //     0xbf79a0: stur            xzr, [x1, #0x2f]
    // 0xbf79a4: ldur            x6, [fp, #-0x20]
    // 0xbf79a8: StoreField: r1->field_b = r6
    //     0xbf79a8: stur            w6, [x1, #0xb]
    // 0xbf79ac: ldur            d0, [fp, #-0x78]
    // 0xbf79b0: r6 = inline_Allocate_Double()
    //     0xbf79b0: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xbf79b4: add             x6, x6, #0x10
    //     0xbf79b8: cmp             x7, x6
    //     0xbf79bc: b.ls            #0xbf87bc
    //     0xbf79c0: str             x6, [THR, #0x50]  ; THR::top
    //     0xbf79c4: sub             x6, x6, #0xf
    //     0xbf79c8: movz            x7, #0xe15c
    //     0xbf79cc: movk            x7, #0x3, lsl #16
    //     0xbf79d0: stur            x7, [x6, #-1]
    // 0xbf79d4: StoreField: r6->field_7 = d0
    //     0xbf79d4: stur            d0, [x6, #7]
    // 0xbf79d8: stur            x6, [fp, #-0x20]
    // 0xbf79dc: r0 = SizedBox()
    //     0xbf79dc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbf79e0: mov             x1, x0
    // 0xbf79e4: ldur            x0, [fp, #-0x20]
    // 0xbf79e8: stur            x1, [fp, #-0x38]
    // 0xbf79ec: StoreField: r1->field_f = r0
    //     0xbf79ec: stur            w0, [x1, #0xf]
    // 0xbf79f0: ldur            x0, [fp, #-0x30]
    // 0xbf79f4: StoreField: r1->field_b = r0
    //     0xbf79f4: stur            w0, [x1, #0xb]
    // 0xbf79f8: ldur            x0, [fp, #-8]
    // 0xbf79fc: LoadField: r2 = r0->field_b
    //     0xbf79fc: ldur            w2, [x0, #0xb]
    // 0xbf7a00: DecompressPointer r2
    //     0xbf7a00: add             x2, x2, HEAP, lsl #32
    // 0xbf7a04: cmp             w2, NULL
    // 0xbf7a08: b.eq            #0xbf87e8
    // 0xbf7a0c: r0 = SvgPicture()
    //     0xbf7a0c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf7a10: mov             x1, x0
    // 0xbf7a14: r2 = "assets/images/small_right.svg"
    //     0xbf7a14: add             x2, PP, #0x46, lsl #12  ; [pp+0x46a70] "assets/images/small_right.svg"
    //     0xbf7a18: ldr             x2, [x2, #0xa70]
    // 0xbf7a1c: stur            x0, [fp, #-0x20]
    // 0xbf7a20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf7a20: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf7a24: r0 = SvgPicture.asset()
    //     0xbf7a24: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf7a28: r0 = Visibility()
    //     0xbf7a28: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf7a2c: mov             x3, x0
    // 0xbf7a30: ldur            x0, [fp, #-0x20]
    // 0xbf7a34: stur            x3, [fp, #-0x30]
    // 0xbf7a38: StoreField: r3->field_b = r0
    //     0xbf7a38: stur            w0, [x3, #0xb]
    // 0xbf7a3c: r0 = Instance_SizedBox
    //     0xbf7a3c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf7a40: StoreField: r3->field_f = r0
    //     0xbf7a40: stur            w0, [x3, #0xf]
    // 0xbf7a44: r4 = true
    //     0xbf7a44: add             x4, NULL, #0x20  ; true
    // 0xbf7a48: StoreField: r3->field_13 = r4
    //     0xbf7a48: stur            w4, [x3, #0x13]
    // 0xbf7a4c: r5 = false
    //     0xbf7a4c: add             x5, NULL, #0x30  ; false
    // 0xbf7a50: ArrayStore: r3[0] = r5  ; List_4
    //     0xbf7a50: stur            w5, [x3, #0x17]
    // 0xbf7a54: StoreField: r3->field_1b = r5
    //     0xbf7a54: stur            w5, [x3, #0x1b]
    // 0xbf7a58: StoreField: r3->field_1f = r5
    //     0xbf7a58: stur            w5, [x3, #0x1f]
    // 0xbf7a5c: StoreField: r3->field_23 = r5
    //     0xbf7a5c: stur            w5, [x3, #0x23]
    // 0xbf7a60: StoreField: r3->field_27 = r5
    //     0xbf7a60: stur            w5, [x3, #0x27]
    // 0xbf7a64: StoreField: r3->field_2b = r5
    //     0xbf7a64: stur            w5, [x3, #0x2b]
    // 0xbf7a68: r1 = Null
    //     0xbf7a68: mov             x1, NULL
    // 0xbf7a6c: r2 = 12
    //     0xbf7a6c: movz            x2, #0xc
    // 0xbf7a70: r0 = AllocateArray()
    //     0xbf7a70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf7a74: mov             x2, x0
    // 0xbf7a78: ldur            x0, [fp, #-0x48]
    // 0xbf7a7c: stur            x2, [fp, #-0x20]
    // 0xbf7a80: StoreField: r2->field_f = r0
    //     0xbf7a80: stur            w0, [x2, #0xf]
    // 0xbf7a84: r16 = Instance_Spacer
    //     0xbf7a84: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbf7a88: ldr             x16, [x16, #0xf0]
    // 0xbf7a8c: StoreField: r2->field_13 = r16
    //     0xbf7a8c: stur            w16, [x2, #0x13]
    // 0xbf7a90: ldur            x0, [fp, #-0x38]
    // 0xbf7a94: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf7a94: stur            w0, [x2, #0x17]
    // 0xbf7a98: r16 = Instance_Spacer
    //     0xbf7a98: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbf7a9c: ldr             x16, [x16, #0xf0]
    // 0xbf7aa0: StoreField: r2->field_1b = r16
    //     0xbf7aa0: stur            w16, [x2, #0x1b]
    // 0xbf7aa4: ldur            x0, [fp, #-0x30]
    // 0xbf7aa8: StoreField: r2->field_1f = r0
    //     0xbf7aa8: stur            w0, [x2, #0x1f]
    // 0xbf7aac: r16 = Instance_Spacer
    //     0xbf7aac: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbf7ab0: ldr             x16, [x16, #0xf0]
    // 0xbf7ab4: StoreField: r2->field_23 = r16
    //     0xbf7ab4: stur            w16, [x2, #0x23]
    // 0xbf7ab8: r1 = <Widget>
    //     0xbf7ab8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf7abc: r0 = AllocateGrowableArray()
    //     0xbf7abc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf7ac0: mov             x1, x0
    // 0xbf7ac4: ldur            x0, [fp, #-0x20]
    // 0xbf7ac8: stur            x1, [fp, #-0x30]
    // 0xbf7acc: StoreField: r1->field_f = r0
    //     0xbf7acc: stur            w0, [x1, #0xf]
    // 0xbf7ad0: r0 = 12
    //     0xbf7ad0: movz            x0, #0xc
    // 0xbf7ad4: StoreField: r1->field_b = r0
    //     0xbf7ad4: stur            w0, [x1, #0xb]
    // 0xbf7ad8: r0 = Row()
    //     0xbf7ad8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf7adc: mov             x1, x0
    // 0xbf7ae0: r0 = Instance_Axis
    //     0xbf7ae0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf7ae4: stur            x1, [fp, #-0x40]
    // 0xbf7ae8: StoreField: r1->field_f = r0
    //     0xbf7ae8: stur            w0, [x1, #0xf]
    // 0xbf7aec: r2 = Instance_MainAxisAlignment
    //     0xbf7aec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf7af0: ldr             x2, [x2, #0xa08]
    // 0xbf7af4: StoreField: r1->field_13 = r2
    //     0xbf7af4: stur            w2, [x1, #0x13]
    // 0xbf7af8: r3 = Instance_MainAxisSize
    //     0xbf7af8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf7afc: ldr             x3, [x3, #0xa10]
    // 0xbf7b00: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf7b00: stur            w3, [x1, #0x17]
    // 0xbf7b04: r4 = Instance_CrossAxisAlignment
    //     0xbf7b04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf7b08: ldr             x4, [x4, #0xa18]
    // 0xbf7b0c: StoreField: r1->field_1b = r4
    //     0xbf7b0c: stur            w4, [x1, #0x1b]
    // 0xbf7b10: r5 = Instance_VerticalDirection
    //     0xbf7b10: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf7b14: ldr             x5, [x5, #0xa20]
    // 0xbf7b18: StoreField: r1->field_23 = r5
    //     0xbf7b18: stur            w5, [x1, #0x23]
    // 0xbf7b1c: r6 = Instance_Clip
    //     0xbf7b1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf7b20: ldr             x6, [x6, #0x38]
    // 0xbf7b24: StoreField: r1->field_2b = r6
    //     0xbf7b24: stur            w6, [x1, #0x2b]
    // 0xbf7b28: StoreField: r1->field_2f = rZR
    //     0xbf7b28: stur            xzr, [x1, #0x2f]
    // 0xbf7b2c: ldur            x7, [fp, #-0x30]
    // 0xbf7b30: StoreField: r1->field_b = r7
    //     0xbf7b30: stur            w7, [x1, #0xb]
    // 0xbf7b34: ldur            x7, [fp, #-8]
    // 0xbf7b38: LoadField: r8 = r7->field_b
    //     0xbf7b38: ldur            w8, [x7, #0xb]
    // 0xbf7b3c: DecompressPointer r8
    //     0xbf7b3c: add             x8, x8, HEAP, lsl #32
    // 0xbf7b40: cmp             w8, NULL
    // 0xbf7b44: b.eq            #0xbf87ec
    // 0xbf7b48: LoadField: r9 = r8->field_b
    //     0xbf7b48: ldur            w9, [x8, #0xb]
    // 0xbf7b4c: DecompressPointer r9
    //     0xbf7b4c: add             x9, x9, HEAP, lsl #32
    // 0xbf7b50: stur            x9, [fp, #-0x38]
    // 0xbf7b54: LoadField: r8 = r9->field_3b
    //     0xbf7b54: ldur            w8, [x9, #0x3b]
    // 0xbf7b58: DecompressPointer r8
    //     0xbf7b58: add             x8, x8, HEAP, lsl #32
    // 0xbf7b5c: stur            x8, [fp, #-0x30]
    // 0xbf7b60: LoadField: r10 = r9->field_43
    //     0xbf7b60: ldur            w10, [x9, #0x43]
    // 0xbf7b64: DecompressPointer r10
    //     0xbf7b64: add             x10, x10, HEAP, lsl #32
    // 0xbf7b68: stur            x10, [fp, #-0x20]
    // 0xbf7b6c: r0 = CustomisedStrip()
    //     0xbf7b6c: bl              #0x9d72b8  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xbf7b70: mov             x1, x0
    // 0xbf7b74: ldur            x0, [fp, #-0x30]
    // 0xbf7b78: stur            x1, [fp, #-0x48]
    // 0xbf7b7c: StoreField: r1->field_b = r0
    //     0xbf7b7c: stur            w0, [x1, #0xb]
    // 0xbf7b80: ldur            x0, [fp, #-0x20]
    // 0xbf7b84: StoreField: r1->field_13 = r0
    //     0xbf7b84: stur            w0, [x1, #0x13]
    // 0xbf7b88: r0 = Visibility()
    //     0xbf7b88: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf7b8c: mov             x3, x0
    // 0xbf7b90: ldur            x0, [fp, #-0x48]
    // 0xbf7b94: stur            x3, [fp, #-0x20]
    // 0xbf7b98: StoreField: r3->field_b = r0
    //     0xbf7b98: stur            w0, [x3, #0xb]
    // 0xbf7b9c: r0 = Instance_SizedBox
    //     0xbf7b9c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf7ba0: StoreField: r3->field_f = r0
    //     0xbf7ba0: stur            w0, [x3, #0xf]
    // 0xbf7ba4: r4 = false
    //     0xbf7ba4: add             x4, NULL, #0x30  ; false
    // 0xbf7ba8: StoreField: r3->field_13 = r4
    //     0xbf7ba8: stur            w4, [x3, #0x13]
    // 0xbf7bac: ArrayStore: r3[0] = r4  ; List_4
    //     0xbf7bac: stur            w4, [x3, #0x17]
    // 0xbf7bb0: StoreField: r3->field_1b = r4
    //     0xbf7bb0: stur            w4, [x3, #0x1b]
    // 0xbf7bb4: StoreField: r3->field_1f = r4
    //     0xbf7bb4: stur            w4, [x3, #0x1f]
    // 0xbf7bb8: StoreField: r3->field_23 = r4
    //     0xbf7bb8: stur            w4, [x3, #0x23]
    // 0xbf7bbc: StoreField: r3->field_27 = r4
    //     0xbf7bbc: stur            w4, [x3, #0x27]
    // 0xbf7bc0: StoreField: r3->field_2b = r4
    //     0xbf7bc0: stur            w4, [x3, #0x2b]
    // 0xbf7bc4: r1 = Null
    //     0xbf7bc4: mov             x1, NULL
    // 0xbf7bc8: r2 = 4
    //     0xbf7bc8: movz            x2, #0x4
    // 0xbf7bcc: r0 = AllocateArray()
    //     0xbf7bcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf7bd0: mov             x2, x0
    // 0xbf7bd4: ldur            x0, [fp, #-0x40]
    // 0xbf7bd8: stur            x2, [fp, #-0x30]
    // 0xbf7bdc: StoreField: r2->field_f = r0
    //     0xbf7bdc: stur            w0, [x2, #0xf]
    // 0xbf7be0: ldur            x0, [fp, #-0x20]
    // 0xbf7be4: StoreField: r2->field_13 = r0
    //     0xbf7be4: stur            w0, [x2, #0x13]
    // 0xbf7be8: r1 = <Widget>
    //     0xbf7be8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf7bec: r0 = AllocateGrowableArray()
    //     0xbf7bec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf7bf0: mov             x1, x0
    // 0xbf7bf4: ldur            x0, [fp, #-0x30]
    // 0xbf7bf8: stur            x1, [fp, #-0x20]
    // 0xbf7bfc: StoreField: r1->field_f = r0
    //     0xbf7bfc: stur            w0, [x1, #0xf]
    // 0xbf7c00: r2 = 4
    //     0xbf7c00: movz            x2, #0x4
    // 0xbf7c04: StoreField: r1->field_b = r2
    //     0xbf7c04: stur            w2, [x1, #0xb]
    // 0xbf7c08: r0 = Column()
    //     0xbf7c08: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf7c0c: mov             x1, x0
    // 0xbf7c10: r0 = Instance_Axis
    //     0xbf7c10: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf7c14: stur            x1, [fp, #-0x30]
    // 0xbf7c18: StoreField: r1->field_f = r0
    //     0xbf7c18: stur            w0, [x1, #0xf]
    // 0xbf7c1c: r2 = Instance_MainAxisAlignment
    //     0xbf7c1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf7c20: ldr             x2, [x2, #0xa08]
    // 0xbf7c24: StoreField: r1->field_13 = r2
    //     0xbf7c24: stur            w2, [x1, #0x13]
    // 0xbf7c28: r3 = Instance_MainAxisSize
    //     0xbf7c28: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf7c2c: ldr             x3, [x3, #0xa10]
    // 0xbf7c30: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf7c30: stur            w3, [x1, #0x17]
    // 0xbf7c34: r4 = Instance_CrossAxisAlignment
    //     0xbf7c34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf7c38: ldr             x4, [x4, #0xa18]
    // 0xbf7c3c: StoreField: r1->field_1b = r4
    //     0xbf7c3c: stur            w4, [x1, #0x1b]
    // 0xbf7c40: r5 = Instance_VerticalDirection
    //     0xbf7c40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf7c44: ldr             x5, [x5, #0xa20]
    // 0xbf7c48: StoreField: r1->field_23 = r5
    //     0xbf7c48: stur            w5, [x1, #0x23]
    // 0xbf7c4c: r6 = Instance_Clip
    //     0xbf7c4c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf7c50: ldr             x6, [x6, #0x38]
    // 0xbf7c54: StoreField: r1->field_2b = r6
    //     0xbf7c54: stur            w6, [x1, #0x2b]
    // 0xbf7c58: StoreField: r1->field_2f = rZR
    //     0xbf7c58: stur            xzr, [x1, #0x2f]
    // 0xbf7c5c: ldur            x7, [fp, #-0x20]
    // 0xbf7c60: StoreField: r1->field_b = r7
    //     0xbf7c60: stur            w7, [x1, #0xb]
    // 0xbf7c64: r0 = InkWell()
    //     0xbf7c64: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbf7c68: mov             x3, x0
    // 0xbf7c6c: ldur            x0, [fp, #-0x30]
    // 0xbf7c70: stur            x3, [fp, #-0x20]
    // 0xbf7c74: StoreField: r3->field_b = r0
    //     0xbf7c74: stur            w0, [x3, #0xb]
    // 0xbf7c78: ldur            x2, [fp, #-0x18]
    // 0xbf7c7c: r1 = Function '<anonymous closure>':.
    //     0xbf7c7c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a1f8] AnonymousClosure: (0xbf9918), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf7c80: ldr             x1, [x1, #0x1f8]
    // 0xbf7c84: r0 = AllocateClosure()
    //     0xbf7c84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf7c88: mov             x1, x0
    // 0xbf7c8c: ldur            x0, [fp, #-0x20]
    // 0xbf7c90: StoreField: r0->field_f = r1
    //     0xbf7c90: stur            w1, [x0, #0xf]
    // 0xbf7c94: r1 = true
    //     0xbf7c94: add             x1, NULL, #0x20  ; true
    // 0xbf7c98: StoreField: r0->field_43 = r1
    //     0xbf7c98: stur            w1, [x0, #0x43]
    // 0xbf7c9c: r2 = Instance_BoxShape
    //     0xbf7c9c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf7ca0: ldr             x2, [x2, #0x80]
    // 0xbf7ca4: StoreField: r0->field_47 = r2
    //     0xbf7ca4: stur            w2, [x0, #0x47]
    // 0xbf7ca8: StoreField: r0->field_6f = r1
    //     0xbf7ca8: stur            w1, [x0, #0x6f]
    // 0xbf7cac: r3 = false
    //     0xbf7cac: add             x3, NULL, #0x30  ; false
    // 0xbf7cb0: StoreField: r0->field_73 = r3
    //     0xbf7cb0: stur            w3, [x0, #0x73]
    // 0xbf7cb4: StoreField: r0->field_83 = r1
    //     0xbf7cb4: stur            w1, [x0, #0x83]
    // 0xbf7cb8: StoreField: r0->field_7b = r3
    //     0xbf7cb8: stur            w3, [x0, #0x7b]
    // 0xbf7cbc: r0 = Padding()
    //     0xbf7cbc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf7cc0: mov             x2, x0
    // 0xbf7cc4: r0 = Instance_EdgeInsets
    //     0xbf7cc4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbf7cc8: ldr             x0, [x0, #0x1f0]
    // 0xbf7ccc: stur            x2, [fp, #-0x30]
    // 0xbf7cd0: StoreField: r2->field_f = r0
    //     0xbf7cd0: stur            w0, [x2, #0xf]
    // 0xbf7cd4: ldur            x0, [fp, #-0x20]
    // 0xbf7cd8: StoreField: r2->field_b = r0
    //     0xbf7cd8: stur            w0, [x2, #0xb]
    // 0xbf7cdc: ldur            x0, [fp, #-0x38]
    // 0xbf7ce0: LoadField: r1 = r0->field_1b
    //     0xbf7ce0: ldur            w1, [x0, #0x1b]
    // 0xbf7ce4: DecompressPointer r1
    //     0xbf7ce4: add             x1, x1, HEAP, lsl #32
    // 0xbf7ce8: cmp             w1, NULL
    // 0xbf7cec: b.ne            #0xbf7cf8
    // 0xbf7cf0: r0 = Null
    //     0xbf7cf0: mov             x0, NULL
    // 0xbf7cf4: b               #0xbf7d10
    // 0xbf7cf8: LoadField: r0 = r1->field_7
    //     0xbf7cf8: ldur            w0, [x1, #7]
    // 0xbf7cfc: cbnz            w0, #0xbf7d08
    // 0xbf7d00: r1 = false
    //     0xbf7d00: add             x1, NULL, #0x30  ; false
    // 0xbf7d04: b               #0xbf7d0c
    // 0xbf7d08: r1 = true
    //     0xbf7d08: add             x1, NULL, #0x20  ; true
    // 0xbf7d0c: mov             x0, x1
    // 0xbf7d10: cmp             w0, NULL
    // 0xbf7d14: b.ne            #0xbf7d20
    // 0xbf7d18: r3 = false
    //     0xbf7d18: add             x3, NULL, #0x30  ; false
    // 0xbf7d1c: b               #0xbf7d24
    // 0xbf7d20: mov             x3, x0
    // 0xbf7d24: ldur            x0, [fp, #-8]
    // 0xbf7d28: ldur            x1, [fp, #-0x10]
    // 0xbf7d2c: stur            x3, [fp, #-0x20]
    // 0xbf7d30: r0 = of()
    //     0xbf7d30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7d34: LoadField: r1 = r0->field_5b
    //     0xbf7d34: ldur            w1, [x0, #0x5b]
    // 0xbf7d38: DecompressPointer r1
    //     0xbf7d38: add             x1, x1, HEAP, lsl #32
    // 0xbf7d3c: r0 = LoadClassIdInstr(r1)
    //     0xbf7d3c: ldur            x0, [x1, #-1]
    //     0xbf7d40: ubfx            x0, x0, #0xc, #0x14
    // 0xbf7d44: d0 = 0.030000
    //     0xbf7d44: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xbf7d48: ldr             d0, [x17, #0x238]
    // 0xbf7d4c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbf7d4c: sub             lr, x0, #0xffa
    //     0xbf7d50: ldr             lr, [x21, lr, lsl #3]
    //     0xbf7d54: blr             lr
    // 0xbf7d58: stur            x0, [fp, #-0x38]
    // 0xbf7d5c: r0 = BoxDecoration()
    //     0xbf7d5c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf7d60: mov             x2, x0
    // 0xbf7d64: ldur            x0, [fp, #-0x38]
    // 0xbf7d68: stur            x2, [fp, #-0x40]
    // 0xbf7d6c: StoreField: r2->field_7 = r0
    //     0xbf7d6c: stur            w0, [x2, #7]
    // 0xbf7d70: r0 = Instance_BoxShape
    //     0xbf7d70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf7d74: ldr             x0, [x0, #0x80]
    // 0xbf7d78: StoreField: r2->field_23 = r0
    //     0xbf7d78: stur            w0, [x2, #0x23]
    // 0xbf7d7c: ldur            x3, [fp, #-8]
    // 0xbf7d80: LoadField: r1 = r3->field_b
    //     0xbf7d80: ldur            w1, [x3, #0xb]
    // 0xbf7d84: DecompressPointer r1
    //     0xbf7d84: add             x1, x1, HEAP, lsl #32
    // 0xbf7d88: cmp             w1, NULL
    // 0xbf7d8c: b.eq            #0xbf87f0
    // 0xbf7d90: LoadField: r4 = r1->field_b
    //     0xbf7d90: ldur            w4, [x1, #0xb]
    // 0xbf7d94: DecompressPointer r4
    //     0xbf7d94: add             x4, x4, HEAP, lsl #32
    // 0xbf7d98: LoadField: r1 = r4->field_13
    //     0xbf7d98: ldur            w1, [x4, #0x13]
    // 0xbf7d9c: DecompressPointer r1
    //     0xbf7d9c: add             x1, x1, HEAP, lsl #32
    // 0xbf7da0: cmp             w1, NULL
    // 0xbf7da4: b.ne            #0xbf7db0
    // 0xbf7da8: r4 = ""
    //     0xbf7da8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf7dac: b               #0xbf7db4
    // 0xbf7db0: mov             x4, x1
    // 0xbf7db4: ldur            x1, [fp, #-0x10]
    // 0xbf7db8: stur            x4, [fp, #-0x38]
    // 0xbf7dbc: r0 = of()
    //     0xbf7dbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7dc0: LoadField: r1 = r0->field_87
    //     0xbf7dc0: ldur            w1, [x0, #0x87]
    // 0xbf7dc4: DecompressPointer r1
    //     0xbf7dc4: add             x1, x1, HEAP, lsl #32
    // 0xbf7dc8: LoadField: r0 = r1->field_27
    //     0xbf7dc8: ldur            w0, [x1, #0x27]
    // 0xbf7dcc: DecompressPointer r0
    //     0xbf7dcc: add             x0, x0, HEAP, lsl #32
    // 0xbf7dd0: r16 = 16.000000
    //     0xbf7dd0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbf7dd4: ldr             x16, [x16, #0x188]
    // 0xbf7dd8: r30 = Instance_Color
    //     0xbf7dd8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf7ddc: stp             lr, x16, [SP]
    // 0xbf7de0: mov             x1, x0
    // 0xbf7de4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf7de4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf7de8: ldr             x4, [x4, #0xaa0]
    // 0xbf7dec: r0 = copyWith()
    //     0xbf7dec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7df0: stur            x0, [fp, #-0x48]
    // 0xbf7df4: r0 = Text()
    //     0xbf7df4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf7df8: mov             x2, x0
    // 0xbf7dfc: ldur            x0, [fp, #-0x38]
    // 0xbf7e00: stur            x2, [fp, #-0x50]
    // 0xbf7e04: StoreField: r2->field_b = r0
    //     0xbf7e04: stur            w0, [x2, #0xb]
    // 0xbf7e08: ldur            x0, [fp, #-0x48]
    // 0xbf7e0c: StoreField: r2->field_13 = r0
    //     0xbf7e0c: stur            w0, [x2, #0x13]
    // 0xbf7e10: ldur            x0, [fp, #-8]
    // 0xbf7e14: LoadField: r1 = r0->field_b
    //     0xbf7e14: ldur            w1, [x0, #0xb]
    // 0xbf7e18: DecompressPointer r1
    //     0xbf7e18: add             x1, x1, HEAP, lsl #32
    // 0xbf7e1c: cmp             w1, NULL
    // 0xbf7e20: b.eq            #0xbf87f4
    // 0xbf7e24: LoadField: r3 = r1->field_b
    //     0xbf7e24: ldur            w3, [x1, #0xb]
    // 0xbf7e28: DecompressPointer r3
    //     0xbf7e28: add             x3, x3, HEAP, lsl #32
    // 0xbf7e2c: LoadField: r1 = r3->field_1b
    //     0xbf7e2c: ldur            w1, [x3, #0x1b]
    // 0xbf7e30: DecompressPointer r1
    //     0xbf7e30: add             x1, x1, HEAP, lsl #32
    // 0xbf7e34: cmp             w1, NULL
    // 0xbf7e38: b.ne            #0xbf7e44
    // 0xbf7e3c: r4 = ""
    //     0xbf7e3c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf7e40: b               #0xbf7e48
    // 0xbf7e44: mov             x4, x1
    // 0xbf7e48: ldur            x3, [fp, #-0x20]
    // 0xbf7e4c: ldur            x1, [fp, #-0x10]
    // 0xbf7e50: stur            x4, [fp, #-0x38]
    // 0xbf7e54: r0 = of()
    //     0xbf7e54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf7e58: LoadField: r1 = r0->field_87
    //     0xbf7e58: ldur            w1, [x0, #0x87]
    // 0xbf7e5c: DecompressPointer r1
    //     0xbf7e5c: add             x1, x1, HEAP, lsl #32
    // 0xbf7e60: LoadField: r0 = r1->field_2b
    //     0xbf7e60: ldur            w0, [x1, #0x2b]
    // 0xbf7e64: DecompressPointer r0
    //     0xbf7e64: add             x0, x0, HEAP, lsl #32
    // 0xbf7e68: r16 = 12.000000
    //     0xbf7e68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf7e6c: ldr             x16, [x16, #0x9e8]
    // 0xbf7e70: r30 = Instance_Color
    //     0xbf7e70: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf7e74: stp             lr, x16, [SP]
    // 0xbf7e78: mov             x1, x0
    // 0xbf7e7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf7e7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf7e80: ldr             x4, [x4, #0xaa0]
    // 0xbf7e84: r0 = copyWith()
    //     0xbf7e84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf7e88: stur            x0, [fp, #-0x48]
    // 0xbf7e8c: r0 = Text()
    //     0xbf7e8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf7e90: mov             x3, x0
    // 0xbf7e94: ldur            x0, [fp, #-0x38]
    // 0xbf7e98: stur            x3, [fp, #-0x58]
    // 0xbf7e9c: StoreField: r3->field_b = r0
    //     0xbf7e9c: stur            w0, [x3, #0xb]
    // 0xbf7ea0: ldur            x0, [fp, #-0x48]
    // 0xbf7ea4: StoreField: r3->field_13 = r0
    //     0xbf7ea4: stur            w0, [x3, #0x13]
    // 0xbf7ea8: r1 = Null
    //     0xbf7ea8: mov             x1, NULL
    // 0xbf7eac: r2 = 6
    //     0xbf7eac: movz            x2, #0x6
    // 0xbf7eb0: r0 = AllocateArray()
    //     0xbf7eb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf7eb4: mov             x2, x0
    // 0xbf7eb8: ldur            x0, [fp, #-0x50]
    // 0xbf7ebc: stur            x2, [fp, #-0x38]
    // 0xbf7ec0: StoreField: r2->field_f = r0
    //     0xbf7ec0: stur            w0, [x2, #0xf]
    // 0xbf7ec4: r16 = Instance_SizedBox
    //     0xbf7ec4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xbf7ec8: ldr             x16, [x16, #0x328]
    // 0xbf7ecc: StoreField: r2->field_13 = r16
    //     0xbf7ecc: stur            w16, [x2, #0x13]
    // 0xbf7ed0: ldur            x0, [fp, #-0x58]
    // 0xbf7ed4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf7ed4: stur            w0, [x2, #0x17]
    // 0xbf7ed8: r1 = <Widget>
    //     0xbf7ed8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf7edc: r0 = AllocateGrowableArray()
    //     0xbf7edc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf7ee0: mov             x1, x0
    // 0xbf7ee4: ldur            x0, [fp, #-0x38]
    // 0xbf7ee8: stur            x1, [fp, #-0x48]
    // 0xbf7eec: StoreField: r1->field_f = r0
    //     0xbf7eec: stur            w0, [x1, #0xf]
    // 0xbf7ef0: r0 = 6
    //     0xbf7ef0: movz            x0, #0x6
    // 0xbf7ef4: StoreField: r1->field_b = r0
    //     0xbf7ef4: stur            w0, [x1, #0xb]
    // 0xbf7ef8: r0 = Column()
    //     0xbf7ef8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf7efc: mov             x1, x0
    // 0xbf7f00: r0 = Instance_Axis
    //     0xbf7f00: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf7f04: stur            x1, [fp, #-0x38]
    // 0xbf7f08: StoreField: r1->field_f = r0
    //     0xbf7f08: stur            w0, [x1, #0xf]
    // 0xbf7f0c: r2 = Instance_MainAxisAlignment
    //     0xbf7f0c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf7f10: ldr             x2, [x2, #0xa08]
    // 0xbf7f14: StoreField: r1->field_13 = r2
    //     0xbf7f14: stur            w2, [x1, #0x13]
    // 0xbf7f18: r3 = Instance_MainAxisSize
    //     0xbf7f18: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf7f1c: ldr             x3, [x3, #0xa10]
    // 0xbf7f20: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf7f20: stur            w3, [x1, #0x17]
    // 0xbf7f24: r4 = Instance_CrossAxisAlignment
    //     0xbf7f24: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf7f28: ldr             x4, [x4, #0x890]
    // 0xbf7f2c: StoreField: r1->field_1b = r4
    //     0xbf7f2c: stur            w4, [x1, #0x1b]
    // 0xbf7f30: r5 = Instance_VerticalDirection
    //     0xbf7f30: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf7f34: ldr             x5, [x5, #0xa20]
    // 0xbf7f38: StoreField: r1->field_23 = r5
    //     0xbf7f38: stur            w5, [x1, #0x23]
    // 0xbf7f3c: r6 = Instance_Clip
    //     0xbf7f3c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf7f40: ldr             x6, [x6, #0x38]
    // 0xbf7f44: StoreField: r1->field_2b = r6
    //     0xbf7f44: stur            w6, [x1, #0x2b]
    // 0xbf7f48: StoreField: r1->field_2f = rZR
    //     0xbf7f48: stur            xzr, [x1, #0x2f]
    // 0xbf7f4c: ldur            x7, [fp, #-0x48]
    // 0xbf7f50: StoreField: r1->field_b = r7
    //     0xbf7f50: stur            w7, [x1, #0xb]
    // 0xbf7f54: r0 = Padding()
    //     0xbf7f54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf7f58: mov             x1, x0
    // 0xbf7f5c: r0 = Instance_EdgeInsets
    //     0xbf7f5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbf7f60: ldr             x0, [x0, #0x980]
    // 0xbf7f64: stur            x1, [fp, #-0x48]
    // 0xbf7f68: StoreField: r1->field_f = r0
    //     0xbf7f68: stur            w0, [x1, #0xf]
    // 0xbf7f6c: ldur            x0, [fp, #-0x38]
    // 0xbf7f70: StoreField: r1->field_b = r0
    //     0xbf7f70: stur            w0, [x1, #0xb]
    // 0xbf7f74: r0 = Container()
    //     0xbf7f74: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf7f78: stur            x0, [fp, #-0x38]
    // 0xbf7f7c: ldur            x16, [fp, #-0x40]
    // 0xbf7f80: ldur            lr, [fp, #-0x48]
    // 0xbf7f84: stp             lr, x16, [SP]
    // 0xbf7f88: mov             x1, x0
    // 0xbf7f8c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbf7f8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbf7f90: ldr             x4, [x4, #0x88]
    // 0xbf7f94: r0 = Container()
    //     0xbf7f94: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf7f98: r0 = Visibility()
    //     0xbf7f98: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf7f9c: mov             x2, x0
    // 0xbf7fa0: ldur            x0, [fp, #-0x38]
    // 0xbf7fa4: stur            x2, [fp, #-0x40]
    // 0xbf7fa8: StoreField: r2->field_b = r0
    //     0xbf7fa8: stur            w0, [x2, #0xb]
    // 0xbf7fac: r3 = Instance_SizedBox
    //     0xbf7fac: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf7fb0: StoreField: r2->field_f = r3
    //     0xbf7fb0: stur            w3, [x2, #0xf]
    // 0xbf7fb4: ldur            x0, [fp, #-0x20]
    // 0xbf7fb8: StoreField: r2->field_13 = r0
    //     0xbf7fb8: stur            w0, [x2, #0x13]
    // 0xbf7fbc: r4 = false
    //     0xbf7fbc: add             x4, NULL, #0x30  ; false
    // 0xbf7fc0: ArrayStore: r2[0] = r4  ; List_4
    //     0xbf7fc0: stur            w4, [x2, #0x17]
    // 0xbf7fc4: StoreField: r2->field_1b = r4
    //     0xbf7fc4: stur            w4, [x2, #0x1b]
    // 0xbf7fc8: StoreField: r2->field_1f = r4
    //     0xbf7fc8: stur            w4, [x2, #0x1f]
    // 0xbf7fcc: StoreField: r2->field_23 = r4
    //     0xbf7fcc: stur            w4, [x2, #0x23]
    // 0xbf7fd0: StoreField: r2->field_27 = r4
    //     0xbf7fd0: stur            w4, [x2, #0x27]
    // 0xbf7fd4: StoreField: r2->field_2b = r4
    //     0xbf7fd4: stur            w4, [x2, #0x2b]
    // 0xbf7fd8: ldur            x5, [fp, #-8]
    // 0xbf7fdc: LoadField: r0 = r5->field_b
    //     0xbf7fdc: ldur            w0, [x5, #0xb]
    // 0xbf7fe0: DecompressPointer r0
    //     0xbf7fe0: add             x0, x0, HEAP, lsl #32
    // 0xbf7fe4: cmp             w0, NULL
    // 0xbf7fe8: b.eq            #0xbf87f8
    // 0xbf7fec: LoadField: r1 = r0->field_b
    //     0xbf7fec: ldur            w1, [x0, #0xb]
    // 0xbf7ff0: DecompressPointer r1
    //     0xbf7ff0: add             x1, x1, HEAP, lsl #32
    // 0xbf7ff4: LoadField: r0 = r1->field_83
    //     0xbf7ff4: ldur            w0, [x1, #0x83]
    // 0xbf7ff8: DecompressPointer r0
    //     0xbf7ff8: add             x0, x0, HEAP, lsl #32
    // 0xbf7ffc: cmp             w0, NULL
    // 0xbf8000: b.ne            #0xbf800c
    // 0xbf8004: r6 = false
    //     0xbf8004: add             x6, NULL, #0x30  ; false
    // 0xbf8008: b               #0xbf8010
    // 0xbf800c: mov             x6, x0
    // 0xbf8010: stur            x6, [fp, #-0x20]
    // 0xbf8014: LoadField: r0 = r1->field_8b
    //     0xbf8014: ldur            w0, [x1, #0x8b]
    // 0xbf8018: DecompressPointer r0
    //     0xbf8018: add             x0, x0, HEAP, lsl #32
    // 0xbf801c: cmp             w0, NULL
    // 0xbf8020: b.ne            #0xbf802c
    // 0xbf8024: r0 = Null
    //     0xbf8024: mov             x0, NULL
    // 0xbf8028: b               #0xbf8044
    // 0xbf802c: LoadField: r7 = r0->field_f
    //     0xbf802c: ldur            x7, [x0, #0xf]
    // 0xbf8030: r0 = BoxInt64Instr(r7)
    //     0xbf8030: sbfiz           x0, x7, #1, #0x1f
    //     0xbf8034: cmp             x7, x0, asr #1
    //     0xbf8038: b.eq            #0xbf8044
    //     0xbf803c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf8040: stur            x7, [x0, #7]
    // 0xbf8044: cmp             w0, NULL
    // 0xbf8048: b.ne            #0xbf8054
    // 0xbf804c: r7 = 0
    //     0xbf804c: movz            x7, #0
    // 0xbf8050: b               #0xbf8064
    // 0xbf8054: r1 = LoadInt32Instr(r0)
    //     0xbf8054: sbfx            x1, x0, #1, #0x1f
    //     0xbf8058: tbz             w0, #0, #0xbf8060
    //     0xbf805c: ldur            x1, [x0, #7]
    // 0xbf8060: mov             x7, x1
    // 0xbf8064: r0 = BoxInt64Instr(r7)
    //     0xbf8064: sbfiz           x0, x7, #1, #0x1f
    //     0xbf8068: cmp             x7, x0, asr #1
    //     0xbf806c: b.eq            #0xbf8078
    //     0xbf8070: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf8074: stur            x7, [x0, #7]
    // 0xbf8078: stp             x0, NULL, [SP]
    // 0xbf807c: r0 = _Double.fromInteger()
    //     0xbf807c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbf8080: ldur            x2, [fp, #-0x18]
    // 0xbf8084: r1 = Function '<anonymous closure>':.
    //     0xbf8084: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a200] AnonymousClosure: (0xa6d99c), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf8088: ldr             x1, [x1, #0x200]
    // 0xbf808c: stur            x0, [fp, #-0x38]
    // 0xbf8090: r0 = AllocateClosure()
    //     0xbf8090: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf8094: stur            x0, [fp, #-0x48]
    // 0xbf8098: r0 = RatingBar()
    //     0xbf8098: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xbf809c: mov             x3, x0
    // 0xbf80a0: ldur            x0, [fp, #-0x48]
    // 0xbf80a4: stur            x3, [fp, #-0x50]
    // 0xbf80a8: StoreField: r3->field_b = r0
    //     0xbf80a8: stur            w0, [x3, #0xb]
    // 0xbf80ac: r0 = false
    //     0xbf80ac: add             x0, NULL, #0x30  ; false
    // 0xbf80b0: StoreField: r3->field_1f = r0
    //     0xbf80b0: stur            w0, [x3, #0x1f]
    // 0xbf80b4: r4 = Instance_Axis
    //     0xbf80b4: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf80b8: StoreField: r3->field_23 = r4
    //     0xbf80b8: stur            w4, [x3, #0x23]
    // 0xbf80bc: r5 = true
    //     0xbf80bc: add             x5, NULL, #0x20  ; true
    // 0xbf80c0: StoreField: r3->field_27 = r5
    //     0xbf80c0: stur            w5, [x3, #0x27]
    // 0xbf80c4: d0 = 2.000000
    //     0xbf80c4: fmov            d0, #2.00000000
    // 0xbf80c8: StoreField: r3->field_2b = d0
    //     0xbf80c8: stur            d0, [x3, #0x2b]
    // 0xbf80cc: StoreField: r3->field_33 = r0
    //     0xbf80cc: stur            w0, [x3, #0x33]
    // 0xbf80d0: ldur            x1, [fp, #-0x38]
    // 0xbf80d4: LoadField: d0 = r1->field_7
    //     0xbf80d4: ldur            d0, [x1, #7]
    // 0xbf80d8: StoreField: r3->field_37 = d0
    //     0xbf80d8: stur            d0, [x3, #0x37]
    // 0xbf80dc: r1 = 5
    //     0xbf80dc: movz            x1, #0x5
    // 0xbf80e0: StoreField: r3->field_3f = r1
    //     0xbf80e0: stur            x1, [x3, #0x3f]
    // 0xbf80e4: r1 = Instance_EdgeInsets
    //     0xbf80e4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xbf80e8: ldr             x1, [x1, #0xa68]
    // 0xbf80ec: StoreField: r3->field_47 = r1
    //     0xbf80ec: stur            w1, [x3, #0x47]
    // 0xbf80f0: d0 = 20.000000
    //     0xbf80f0: fmov            d0, #20.00000000
    // 0xbf80f4: StoreField: r3->field_4b = d0
    //     0xbf80f4: stur            d0, [x3, #0x4b]
    // 0xbf80f8: d0 = 1.000000
    //     0xbf80f8: fmov            d0, #1.00000000
    // 0xbf80fc: StoreField: r3->field_53 = d0
    //     0xbf80fc: stur            d0, [x3, #0x53]
    // 0xbf8100: StoreField: r3->field_5b = r0
    //     0xbf8100: stur            w0, [x3, #0x5b]
    // 0xbf8104: StoreField: r3->field_5f = r0
    //     0xbf8104: stur            w0, [x3, #0x5f]
    // 0xbf8108: ldur            x2, [fp, #-0x18]
    // 0xbf810c: r1 = Function '<anonymous closure>':.
    //     0xbf810c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a208] AnonymousClosure: (0xa6d740), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf8110: ldr             x1, [x1, #0x208]
    // 0xbf8114: r0 = AllocateClosure()
    //     0xbf8114: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf8118: mov             x1, x0
    // 0xbf811c: ldur            x0, [fp, #-0x50]
    // 0xbf8120: StoreField: r0->field_63 = r1
    //     0xbf8120: stur            w1, [x0, #0x63]
    // 0xbf8124: ldur            x1, [fp, #-0x10]
    // 0xbf8128: r0 = of()
    //     0xbf8128: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf812c: LoadField: r1 = r0->field_87
    //     0xbf812c: ldur            w1, [x0, #0x87]
    // 0xbf8130: DecompressPointer r1
    //     0xbf8130: add             x1, x1, HEAP, lsl #32
    // 0xbf8134: LoadField: r0 = r1->field_2b
    //     0xbf8134: ldur            w0, [x1, #0x2b]
    // 0xbf8138: DecompressPointer r0
    //     0xbf8138: add             x0, x0, HEAP, lsl #32
    // 0xbf813c: stur            x0, [fp, #-0x38]
    // 0xbf8140: r1 = Instance_Color
    //     0xbf8140: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf8144: d0 = 0.300000
    //     0xbf8144: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbf8148: ldr             d0, [x17, #0x658]
    // 0xbf814c: r0 = withOpacity()
    //     0xbf814c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf8150: r16 = 12.000000
    //     0xbf8150: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf8154: ldr             x16, [x16, #0x9e8]
    // 0xbf8158: stp             x0, x16, [SP]
    // 0xbf815c: ldur            x1, [fp, #-0x38]
    // 0xbf8160: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf8160: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf8164: ldr             x4, [x4, #0xaa0]
    // 0xbf8168: r0 = copyWith()
    //     0xbf8168: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf816c: stur            x0, [fp, #-0x38]
    // 0xbf8170: r0 = Text()
    //     0xbf8170: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf8174: mov             x1, x0
    // 0xbf8178: r0 = "Rate this product"
    //     0xbf8178: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fcb0] "Rate this product"
    //     0xbf817c: ldr             x0, [x0, #0xcb0]
    // 0xbf8180: stur            x1, [fp, #-0x48]
    // 0xbf8184: StoreField: r1->field_b = r0
    //     0xbf8184: stur            w0, [x1, #0xb]
    // 0xbf8188: ldur            x0, [fp, #-0x38]
    // 0xbf818c: StoreField: r1->field_13 = r0
    //     0xbf818c: stur            w0, [x1, #0x13]
    // 0xbf8190: r0 = Padding()
    //     0xbf8190: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf8194: mov             x3, x0
    // 0xbf8198: r0 = Instance_EdgeInsets
    //     0xbf8198: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf819c: ldr             x0, [x0, #0x668]
    // 0xbf81a0: stur            x3, [fp, #-0x38]
    // 0xbf81a4: StoreField: r3->field_f = r0
    //     0xbf81a4: stur            w0, [x3, #0xf]
    // 0xbf81a8: ldur            x0, [fp, #-0x48]
    // 0xbf81ac: StoreField: r3->field_b = r0
    //     0xbf81ac: stur            w0, [x3, #0xb]
    // 0xbf81b0: r1 = Null
    //     0xbf81b0: mov             x1, NULL
    // 0xbf81b4: r2 = 4
    //     0xbf81b4: movz            x2, #0x4
    // 0xbf81b8: r0 = AllocateArray()
    //     0xbf81b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf81bc: mov             x2, x0
    // 0xbf81c0: ldur            x0, [fp, #-0x50]
    // 0xbf81c4: stur            x2, [fp, #-0x48]
    // 0xbf81c8: StoreField: r2->field_f = r0
    //     0xbf81c8: stur            w0, [x2, #0xf]
    // 0xbf81cc: ldur            x0, [fp, #-0x38]
    // 0xbf81d0: StoreField: r2->field_13 = r0
    //     0xbf81d0: stur            w0, [x2, #0x13]
    // 0xbf81d4: r1 = <Widget>
    //     0xbf81d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf81d8: r0 = AllocateGrowableArray()
    //     0xbf81d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf81dc: mov             x1, x0
    // 0xbf81e0: ldur            x0, [fp, #-0x48]
    // 0xbf81e4: stur            x1, [fp, #-0x38]
    // 0xbf81e8: StoreField: r1->field_f = r0
    //     0xbf81e8: stur            w0, [x1, #0xf]
    // 0xbf81ec: r2 = 4
    //     0xbf81ec: movz            x2, #0x4
    // 0xbf81f0: StoreField: r1->field_b = r2
    //     0xbf81f0: stur            w2, [x1, #0xb]
    // 0xbf81f4: r0 = Column()
    //     0xbf81f4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf81f8: mov             x2, x0
    // 0xbf81fc: r1 = Instance_Axis
    //     0xbf81fc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf8200: stur            x2, [fp, #-0x48]
    // 0xbf8204: StoreField: r2->field_f = r1
    //     0xbf8204: stur            w1, [x2, #0xf]
    // 0xbf8208: r3 = Instance_MainAxisAlignment
    //     0xbf8208: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf820c: ldr             x3, [x3, #0xa08]
    // 0xbf8210: StoreField: r2->field_13 = r3
    //     0xbf8210: stur            w3, [x2, #0x13]
    // 0xbf8214: r4 = Instance_MainAxisSize
    //     0xbf8214: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf8218: ldr             x4, [x4, #0xa10]
    // 0xbf821c: ArrayStore: r2[0] = r4  ; List_4
    //     0xbf821c: stur            w4, [x2, #0x17]
    // 0xbf8220: r0 = Instance_CrossAxisAlignment
    //     0xbf8220: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf8224: ldr             x0, [x0, #0x890]
    // 0xbf8228: StoreField: r2->field_1b = r0
    //     0xbf8228: stur            w0, [x2, #0x1b]
    // 0xbf822c: r5 = Instance_VerticalDirection
    //     0xbf822c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf8230: ldr             x5, [x5, #0xa20]
    // 0xbf8234: StoreField: r2->field_23 = r5
    //     0xbf8234: stur            w5, [x2, #0x23]
    // 0xbf8238: r6 = Instance_Clip
    //     0xbf8238: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf823c: ldr             x6, [x6, #0x38]
    // 0xbf8240: StoreField: r2->field_2b = r6
    //     0xbf8240: stur            w6, [x2, #0x2b]
    // 0xbf8244: StoreField: r2->field_2f = rZR
    //     0xbf8244: stur            xzr, [x2, #0x2f]
    // 0xbf8248: ldur            x0, [fp, #-0x38]
    // 0xbf824c: StoreField: r2->field_b = r0
    //     0xbf824c: stur            w0, [x2, #0xb]
    // 0xbf8250: ldur            x7, [fp, #-8]
    // 0xbf8254: LoadField: r0 = r7->field_b
    //     0xbf8254: ldur            w0, [x7, #0xb]
    // 0xbf8258: DecompressPointer r0
    //     0xbf8258: add             x0, x0, HEAP, lsl #32
    // 0xbf825c: cmp             w0, NULL
    // 0xbf8260: b.eq            #0xbf87fc
    // 0xbf8264: LoadField: r8 = r0->field_b
    //     0xbf8264: ldur            w8, [x0, #0xb]
    // 0xbf8268: DecompressPointer r8
    //     0xbf8268: add             x8, x8, HEAP, lsl #32
    // 0xbf826c: LoadField: r0 = r8->field_8f
    //     0xbf826c: ldur            w0, [x8, #0x8f]
    // 0xbf8270: DecompressPointer r0
    //     0xbf8270: add             x0, x0, HEAP, lsl #32
    // 0xbf8274: cmp             w0, NULL
    // 0xbf8278: b.ne            #0xbf8280
    // 0xbf827c: r0 = ""
    //     0xbf827c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf8280: r8 = LoadClassIdInstr(r0)
    //     0xbf8280: ldur            x8, [x0, #-1]
    //     0xbf8284: ubfx            x8, x8, #0xc, #0x14
    // 0xbf8288: str             x0, [SP]
    // 0xbf828c: mov             x0, x8
    // 0xbf8290: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbf8290: sub             lr, x0, #1, lsl #12
    //     0xbf8294: ldr             lr, [x21, lr, lsl #3]
    //     0xbf8298: blr             lr
    // 0xbf829c: ldur            x1, [fp, #-0x10]
    // 0xbf82a0: stur            x0, [fp, #-0x10]
    // 0xbf82a4: r0 = of()
    //     0xbf82a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf82a8: LoadField: r1 = r0->field_87
    //     0xbf82a8: ldur            w1, [x0, #0x87]
    // 0xbf82ac: DecompressPointer r1
    //     0xbf82ac: add             x1, x1, HEAP, lsl #32
    // 0xbf82b0: LoadField: r0 = r1->field_f
    //     0xbf82b0: ldur            w0, [x1, #0xf]
    // 0xbf82b4: DecompressPointer r0
    //     0xbf82b4: add             x0, x0, HEAP, lsl #32
    // 0xbf82b8: cmp             w0, NULL
    // 0xbf82bc: b.ne            #0xbf82c8
    // 0xbf82c0: r4 = Null
    //     0xbf82c0: mov             x4, NULL
    // 0xbf82c4: b               #0xbf82f0
    // 0xbf82c8: r16 = 12.000000
    //     0xbf82c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf82cc: ldr             x16, [x16, #0x9e8]
    // 0xbf82d0: r30 = Instance_Color
    //     0xbf82d0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbf82d4: ldr             lr, [lr, #0x858]
    // 0xbf82d8: stp             lr, x16, [SP]
    // 0xbf82dc: mov             x1, x0
    // 0xbf82e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf82e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf82e4: ldr             x4, [x4, #0xaa0]
    // 0xbf82e8: r0 = copyWith()
    //     0xbf82e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf82ec: mov             x4, x0
    // 0xbf82f0: ldur            x2, [fp, #-8]
    // 0xbf82f4: ldur            x3, [fp, #-0x20]
    // 0xbf82f8: ldur            x1, [fp, #-0x48]
    // 0xbf82fc: ldur            x0, [fp, #-0x10]
    // 0xbf8300: stur            x4, [fp, #-0x38]
    // 0xbf8304: r0 = Text()
    //     0xbf8304: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf8308: mov             x1, x0
    // 0xbf830c: ldur            x0, [fp, #-0x10]
    // 0xbf8310: stur            x1, [fp, #-0x50]
    // 0xbf8314: StoreField: r1->field_b = r0
    //     0xbf8314: stur            w0, [x1, #0xb]
    // 0xbf8318: ldur            x0, [fp, #-0x38]
    // 0xbf831c: StoreField: r1->field_13 = r0
    //     0xbf831c: stur            w0, [x1, #0x13]
    // 0xbf8320: r0 = InkWell()
    //     0xbf8320: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbf8324: mov             x3, x0
    // 0xbf8328: ldur            x0, [fp, #-0x50]
    // 0xbf832c: stur            x3, [fp, #-0x10]
    // 0xbf8330: StoreField: r3->field_b = r0
    //     0xbf8330: stur            w0, [x3, #0xb]
    // 0xbf8334: ldur            x2, [fp, #-0x18]
    // 0xbf8338: r1 = Function '<anonymous closure>':.
    //     0xbf8338: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a210] AnonymousClosure: (0xbf9838), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf833c: ldr             x1, [x1, #0x210]
    // 0xbf8340: r0 = AllocateClosure()
    //     0xbf8340: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf8344: mov             x1, x0
    // 0xbf8348: ldur            x0, [fp, #-0x10]
    // 0xbf834c: StoreField: r0->field_f = r1
    //     0xbf834c: stur            w1, [x0, #0xf]
    // 0xbf8350: r1 = true
    //     0xbf8350: add             x1, NULL, #0x20  ; true
    // 0xbf8354: StoreField: r0->field_43 = r1
    //     0xbf8354: stur            w1, [x0, #0x43]
    // 0xbf8358: r2 = Instance_BoxShape
    //     0xbf8358: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf835c: ldr             x2, [x2, #0x80]
    // 0xbf8360: StoreField: r0->field_47 = r2
    //     0xbf8360: stur            w2, [x0, #0x47]
    // 0xbf8364: StoreField: r0->field_6f = r1
    //     0xbf8364: stur            w1, [x0, #0x6f]
    // 0xbf8368: r3 = false
    //     0xbf8368: add             x3, NULL, #0x30  ; false
    // 0xbf836c: StoreField: r0->field_73 = r3
    //     0xbf836c: stur            w3, [x0, #0x73]
    // 0xbf8370: StoreField: r0->field_83 = r1
    //     0xbf8370: stur            w1, [x0, #0x83]
    // 0xbf8374: StoreField: r0->field_7b = r3
    //     0xbf8374: stur            w3, [x0, #0x7b]
    // 0xbf8378: r1 = Null
    //     0xbf8378: mov             x1, NULL
    // 0xbf837c: r2 = 4
    //     0xbf837c: movz            x2, #0x4
    // 0xbf8380: r0 = AllocateArray()
    //     0xbf8380: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf8384: mov             x2, x0
    // 0xbf8388: ldur            x0, [fp, #-0x48]
    // 0xbf838c: stur            x2, [fp, #-0x38]
    // 0xbf8390: StoreField: r2->field_f = r0
    //     0xbf8390: stur            w0, [x2, #0xf]
    // 0xbf8394: ldur            x0, [fp, #-0x10]
    // 0xbf8398: StoreField: r2->field_13 = r0
    //     0xbf8398: stur            w0, [x2, #0x13]
    // 0xbf839c: r1 = <Widget>
    //     0xbf839c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf83a0: r0 = AllocateGrowableArray()
    //     0xbf83a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf83a4: mov             x1, x0
    // 0xbf83a8: ldur            x0, [fp, #-0x38]
    // 0xbf83ac: stur            x1, [fp, #-0x10]
    // 0xbf83b0: StoreField: r1->field_f = r0
    //     0xbf83b0: stur            w0, [x1, #0xf]
    // 0xbf83b4: r0 = 4
    //     0xbf83b4: movz            x0, #0x4
    // 0xbf83b8: StoreField: r1->field_b = r0
    //     0xbf83b8: stur            w0, [x1, #0xb]
    // 0xbf83bc: r0 = Row()
    //     0xbf83bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf83c0: mov             x1, x0
    // 0xbf83c4: r0 = Instance_Axis
    //     0xbf83c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf83c8: stur            x1, [fp, #-0x38]
    // 0xbf83cc: StoreField: r1->field_f = r0
    //     0xbf83cc: stur            w0, [x1, #0xf]
    // 0xbf83d0: r0 = Instance_MainAxisAlignment
    //     0xbf83d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbf83d4: ldr             x0, [x0, #0xa8]
    // 0xbf83d8: StoreField: r1->field_13 = r0
    //     0xbf83d8: stur            w0, [x1, #0x13]
    // 0xbf83dc: r0 = Instance_MainAxisSize
    //     0xbf83dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf83e0: ldr             x0, [x0, #0xa10]
    // 0xbf83e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf83e4: stur            w0, [x1, #0x17]
    // 0xbf83e8: r2 = Instance_CrossAxisAlignment
    //     0xbf83e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf83ec: ldr             x2, [x2, #0xa18]
    // 0xbf83f0: StoreField: r1->field_1b = r2
    //     0xbf83f0: stur            w2, [x1, #0x1b]
    // 0xbf83f4: r3 = Instance_VerticalDirection
    //     0xbf83f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf83f8: ldr             x3, [x3, #0xa20]
    // 0xbf83fc: StoreField: r1->field_23 = r3
    //     0xbf83fc: stur            w3, [x1, #0x23]
    // 0xbf8400: r4 = Instance_Clip
    //     0xbf8400: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf8404: ldr             x4, [x4, #0x38]
    // 0xbf8408: StoreField: r1->field_2b = r4
    //     0xbf8408: stur            w4, [x1, #0x2b]
    // 0xbf840c: StoreField: r1->field_2f = rZR
    //     0xbf840c: stur            xzr, [x1, #0x2f]
    // 0xbf8410: ldur            x5, [fp, #-0x10]
    // 0xbf8414: StoreField: r1->field_b = r5
    //     0xbf8414: stur            w5, [x1, #0xb]
    // 0xbf8418: r0 = Padding()
    //     0xbf8418: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf841c: mov             x1, x0
    // 0xbf8420: r0 = Instance_EdgeInsets
    //     0xbf8420: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbf8424: ldr             x0, [x0, #0xf30]
    // 0xbf8428: stur            x1, [fp, #-0x10]
    // 0xbf842c: StoreField: r1->field_f = r0
    //     0xbf842c: stur            w0, [x1, #0xf]
    // 0xbf8430: ldur            x0, [fp, #-0x38]
    // 0xbf8434: StoreField: r1->field_b = r0
    //     0xbf8434: stur            w0, [x1, #0xb]
    // 0xbf8438: r0 = Visibility()
    //     0xbf8438: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf843c: mov             x1, x0
    // 0xbf8440: ldur            x0, [fp, #-0x10]
    // 0xbf8444: stur            x1, [fp, #-0x38]
    // 0xbf8448: StoreField: r1->field_b = r0
    //     0xbf8448: stur            w0, [x1, #0xb]
    // 0xbf844c: r2 = Instance_SizedBox
    //     0xbf844c: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf8450: StoreField: r1->field_f = r2
    //     0xbf8450: stur            w2, [x1, #0xf]
    // 0xbf8454: ldur            x0, [fp, #-0x20]
    // 0xbf8458: StoreField: r1->field_13 = r0
    //     0xbf8458: stur            w0, [x1, #0x13]
    // 0xbf845c: r3 = false
    //     0xbf845c: add             x3, NULL, #0x30  ; false
    // 0xbf8460: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf8460: stur            w3, [x1, #0x17]
    // 0xbf8464: StoreField: r1->field_1b = r3
    //     0xbf8464: stur            w3, [x1, #0x1b]
    // 0xbf8468: StoreField: r1->field_1f = r3
    //     0xbf8468: stur            w3, [x1, #0x1f]
    // 0xbf846c: StoreField: r1->field_23 = r3
    //     0xbf846c: stur            w3, [x1, #0x23]
    // 0xbf8470: StoreField: r1->field_27 = r3
    //     0xbf8470: stur            w3, [x1, #0x27]
    // 0xbf8474: StoreField: r1->field_2b = r3
    //     0xbf8474: stur            w3, [x1, #0x2b]
    // 0xbf8478: ldur            x4, [fp, #-8]
    // 0xbf847c: LoadField: r0 = r4->field_b
    //     0xbf847c: ldur            w0, [x4, #0xb]
    // 0xbf8480: DecompressPointer r0
    //     0xbf8480: add             x0, x0, HEAP, lsl #32
    // 0xbf8484: cmp             w0, NULL
    // 0xbf8488: b.eq            #0xbf8800
    // 0xbf848c: LoadField: r5 = r0->field_b
    //     0xbf848c: ldur            w5, [x0, #0xb]
    // 0xbf8490: DecompressPointer r5
    //     0xbf8490: add             x5, x5, HEAP, lsl #32
    // 0xbf8494: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xbf8494: ldur            w0, [x5, #0x17]
    // 0xbf8498: DecompressPointer r0
    //     0xbf8498: add             x0, x0, HEAP, lsl #32
    // 0xbf849c: r5 = LoadClassIdInstr(r0)
    //     0xbf849c: ldur            x5, [x0, #-1]
    //     0xbf84a0: ubfx            x5, x5, #0xc, #0x14
    // 0xbf84a4: r16 = "pending"
    //     0xbf84a4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec0] "pending"
    //     0xbf84a8: ldr             x16, [x16, #0xec0]
    // 0xbf84ac: stp             x16, x0, [SP]
    // 0xbf84b0: mov             x0, x5
    // 0xbf84b4: mov             lr, x0
    // 0xbf84b8: ldr             lr, [x21, lr, lsl #3]
    // 0xbf84bc: blr             lr
    // 0xbf84c0: eor             x1, x0, #0x10
    // 0xbf84c4: stur            x1, [fp, #-0x10]
    // 0xbf84c8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbf84c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf84cc: ldr             x0, [x0, #0x1c80]
    //     0xbf84d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbf84d4: cmp             w0, w16
    //     0xbf84d8: b.ne            #0xbf84e4
    //     0xbf84dc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbf84e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbf84e4: r0 = GetNavigation.size()
    //     0xbf84e4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbf84e8: LoadField: d0 = r0->field_7
    //     0xbf84e8: ldur            d0, [x0, #7]
    // 0xbf84ec: ldur            x0, [fp, #-8]
    // 0xbf84f0: stur            d0, [fp, #-0x78]
    // 0xbf84f4: LoadField: r1 = r0->field_b
    //     0xbf84f4: ldur            w1, [x0, #0xb]
    // 0xbf84f8: DecompressPointer r1
    //     0xbf84f8: add             x1, x1, HEAP, lsl #32
    // 0xbf84fc: cmp             w1, NULL
    // 0xbf8500: b.eq            #0xbf8804
    // 0xbf8504: LoadField: r0 = r1->field_b
    //     0xbf8504: ldur            w0, [x1, #0xb]
    // 0xbf8508: DecompressPointer r0
    //     0xbf8508: add             x0, x0, HEAP, lsl #32
    // 0xbf850c: LoadField: r1 = r0->field_57
    //     0xbf850c: ldur            w1, [x0, #0x57]
    // 0xbf8510: DecompressPointer r1
    //     0xbf8510: add             x1, x1, HEAP, lsl #32
    // 0xbf8514: cmp             w1, NULL
    // 0xbf8518: b.ne            #0xbf8524
    // 0xbf851c: r0 = Null
    //     0xbf851c: mov             x0, NULL
    // 0xbf8520: b               #0xbf8528
    // 0xbf8524: LoadField: r0 = r1->field_b
    //     0xbf8524: ldur            w0, [x1, #0xb]
    // 0xbf8528: cmp             w0, NULL
    // 0xbf852c: b.ne            #0xbf8538
    // 0xbf8530: r6 = 0
    //     0xbf8530: movz            x6, #0
    // 0xbf8534: b               #0xbf8540
    // 0xbf8538: r1 = LoadInt32Instr(r0)
    //     0xbf8538: sbfx            x1, x0, #1, #0x1f
    // 0xbf853c: mov             x6, x1
    // 0xbf8540: ldur            x5, [fp, #-0x30]
    // 0xbf8544: ldur            x4, [fp, #-0x40]
    // 0xbf8548: ldur            x3, [fp, #-0x38]
    // 0xbf854c: ldur            x0, [fp, #-0x10]
    // 0xbf8550: stur            x6, [fp, #-0x70]
    // 0xbf8554: r1 = Function '<anonymous closure>':.
    //     0xbf8554: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a218] AnonymousClosure: (0xa6d658), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf8558: ldr             x1, [x1, #0x218]
    // 0xbf855c: r2 = Null
    //     0xbf855c: mov             x2, NULL
    // 0xbf8560: r0 = AllocateClosure()
    //     0xbf8560: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf8564: ldur            x2, [fp, #-0x18]
    // 0xbf8568: r1 = Function '<anonymous closure>':.
    //     0xbf8568: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a220] AnonymousClosure: (0xbf8820), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf856c: ldr             x1, [x1, #0x220]
    // 0xbf8570: stur            x0, [fp, #-8]
    // 0xbf8574: r0 = AllocateClosure()
    //     0xbf8574: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf8578: stur            x0, [fp, #-0x18]
    // 0xbf857c: r0 = ListView()
    //     0xbf857c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbf8580: stur            x0, [fp, #-0x20]
    // 0xbf8584: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbf8584: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbf8588: ldr             x16, [x16, #0x1c8]
    // 0xbf858c: r30 = Instance_Axis
    //     0xbf858c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf8590: stp             lr, x16, [SP, #8]
    // 0xbf8594: r16 = true
    //     0xbf8594: add             x16, NULL, #0x20  ; true
    // 0xbf8598: str             x16, [SP]
    // 0xbf859c: mov             x1, x0
    // 0xbf85a0: ldur            x2, [fp, #-0x18]
    // 0xbf85a4: ldur            x3, [fp, #-0x70]
    // 0xbf85a8: ldur            x5, [fp, #-8]
    // 0xbf85ac: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x5, shrinkWrap, 0x6, null]
    //     0xbf85ac: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a228] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x6, Null]
    //     0xbf85b0: ldr             x4, [x4, #0x228]
    // 0xbf85b4: r0 = ListView.separated()
    //     0xbf85b4: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbf85b8: r0 = Align()
    //     0xbf85b8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbf85bc: mov             x1, x0
    // 0xbf85c0: r0 = Instance_Alignment
    //     0xbf85c0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbf85c4: ldr             x0, [x0, #0xb10]
    // 0xbf85c8: stur            x1, [fp, #-0x18]
    // 0xbf85cc: StoreField: r1->field_f = r0
    //     0xbf85cc: stur            w0, [x1, #0xf]
    // 0xbf85d0: ldur            x0, [fp, #-0x20]
    // 0xbf85d4: StoreField: r1->field_b = r0
    //     0xbf85d4: stur            w0, [x1, #0xb]
    // 0xbf85d8: ldur            d0, [fp, #-0x78]
    // 0xbf85dc: r0 = inline_Allocate_Double()
    //     0xbf85dc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbf85e0: add             x0, x0, #0x10
    //     0xbf85e4: cmp             x2, x0
    //     0xbf85e8: b.ls            #0xbf8808
    //     0xbf85ec: str             x0, [THR, #0x50]  ; THR::top
    //     0xbf85f0: sub             x0, x0, #0xf
    //     0xbf85f4: movz            x2, #0xe15c
    //     0xbf85f8: movk            x2, #0x3, lsl #16
    //     0xbf85fc: stur            x2, [x0, #-1]
    // 0xbf8600: StoreField: r0->field_7 = d0
    //     0xbf8600: stur            d0, [x0, #7]
    // 0xbf8604: stur            x0, [fp, #-8]
    // 0xbf8608: r0 = SizedBox()
    //     0xbf8608: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbf860c: mov             x1, x0
    // 0xbf8610: ldur            x0, [fp, #-8]
    // 0xbf8614: stur            x1, [fp, #-0x20]
    // 0xbf8618: StoreField: r1->field_f = r0
    //     0xbf8618: stur            w0, [x1, #0xf]
    // 0xbf861c: r0 = 25.000000
    //     0xbf861c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0xbf8620: ldr             x0, [x0, #0x98]
    // 0xbf8624: StoreField: r1->field_13 = r0
    //     0xbf8624: stur            w0, [x1, #0x13]
    // 0xbf8628: ldur            x0, [fp, #-0x18]
    // 0xbf862c: StoreField: r1->field_b = r0
    //     0xbf862c: stur            w0, [x1, #0xb]
    // 0xbf8630: r0 = Padding()
    //     0xbf8630: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf8634: mov             x1, x0
    // 0xbf8638: r0 = Instance_EdgeInsets
    //     0xbf8638: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xbf863c: ldr             x0, [x0, #0x868]
    // 0xbf8640: stur            x1, [fp, #-8]
    // 0xbf8644: StoreField: r1->field_f = r0
    //     0xbf8644: stur            w0, [x1, #0xf]
    // 0xbf8648: ldur            x0, [fp, #-0x20]
    // 0xbf864c: StoreField: r1->field_b = r0
    //     0xbf864c: stur            w0, [x1, #0xb]
    // 0xbf8650: r0 = Visibility()
    //     0xbf8650: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbf8654: mov             x3, x0
    // 0xbf8658: ldur            x0, [fp, #-8]
    // 0xbf865c: stur            x3, [fp, #-0x18]
    // 0xbf8660: StoreField: r3->field_b = r0
    //     0xbf8660: stur            w0, [x3, #0xb]
    // 0xbf8664: r0 = Instance_SizedBox
    //     0xbf8664: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbf8668: StoreField: r3->field_f = r0
    //     0xbf8668: stur            w0, [x3, #0xf]
    // 0xbf866c: ldur            x0, [fp, #-0x10]
    // 0xbf8670: StoreField: r3->field_13 = r0
    //     0xbf8670: stur            w0, [x3, #0x13]
    // 0xbf8674: r0 = false
    //     0xbf8674: add             x0, NULL, #0x30  ; false
    // 0xbf8678: ArrayStore: r3[0] = r0  ; List_4
    //     0xbf8678: stur            w0, [x3, #0x17]
    // 0xbf867c: StoreField: r3->field_1b = r0
    //     0xbf867c: stur            w0, [x3, #0x1b]
    // 0xbf8680: StoreField: r3->field_1f = r0
    //     0xbf8680: stur            w0, [x3, #0x1f]
    // 0xbf8684: StoreField: r3->field_23 = r0
    //     0xbf8684: stur            w0, [x3, #0x23]
    // 0xbf8688: StoreField: r3->field_27 = r0
    //     0xbf8688: stur            w0, [x3, #0x27]
    // 0xbf868c: StoreField: r3->field_2b = r0
    //     0xbf868c: stur            w0, [x3, #0x2b]
    // 0xbf8690: r1 = Null
    //     0xbf8690: mov             x1, NULL
    // 0xbf8694: r2 = 8
    //     0xbf8694: movz            x2, #0x8
    // 0xbf8698: r0 = AllocateArray()
    //     0xbf8698: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf869c: mov             x2, x0
    // 0xbf86a0: ldur            x0, [fp, #-0x30]
    // 0xbf86a4: stur            x2, [fp, #-8]
    // 0xbf86a8: StoreField: r2->field_f = r0
    //     0xbf86a8: stur            w0, [x2, #0xf]
    // 0xbf86ac: ldur            x0, [fp, #-0x40]
    // 0xbf86b0: StoreField: r2->field_13 = r0
    //     0xbf86b0: stur            w0, [x2, #0x13]
    // 0xbf86b4: ldur            x0, [fp, #-0x38]
    // 0xbf86b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf86b8: stur            w0, [x2, #0x17]
    // 0xbf86bc: ldur            x0, [fp, #-0x18]
    // 0xbf86c0: StoreField: r2->field_1b = r0
    //     0xbf86c0: stur            w0, [x2, #0x1b]
    // 0xbf86c4: r1 = <Widget>
    //     0xbf86c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf86c8: r0 = AllocateGrowableArray()
    //     0xbf86c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf86cc: mov             x1, x0
    // 0xbf86d0: ldur            x0, [fp, #-8]
    // 0xbf86d4: stur            x1, [fp, #-0x10]
    // 0xbf86d8: StoreField: r1->field_f = r0
    //     0xbf86d8: stur            w0, [x1, #0xf]
    // 0xbf86dc: r0 = 8
    //     0xbf86dc: movz            x0, #0x8
    // 0xbf86e0: StoreField: r1->field_b = r0
    //     0xbf86e0: stur            w0, [x1, #0xb]
    // 0xbf86e4: r0 = Column()
    //     0xbf86e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf86e8: mov             x1, x0
    // 0xbf86ec: r0 = Instance_Axis
    //     0xbf86ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf86f0: stur            x1, [fp, #-8]
    // 0xbf86f4: StoreField: r1->field_f = r0
    //     0xbf86f4: stur            w0, [x1, #0xf]
    // 0xbf86f8: r0 = Instance_MainAxisAlignment
    //     0xbf86f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf86fc: ldr             x0, [x0, #0xa08]
    // 0xbf8700: StoreField: r1->field_13 = r0
    //     0xbf8700: stur            w0, [x1, #0x13]
    // 0xbf8704: r0 = Instance_MainAxisSize
    //     0xbf8704: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf8708: ldr             x0, [x0, #0xa10]
    // 0xbf870c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf870c: stur            w0, [x1, #0x17]
    // 0xbf8710: r0 = Instance_CrossAxisAlignment
    //     0xbf8710: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf8714: ldr             x0, [x0, #0xa18]
    // 0xbf8718: StoreField: r1->field_1b = r0
    //     0xbf8718: stur            w0, [x1, #0x1b]
    // 0xbf871c: r0 = Instance_VerticalDirection
    //     0xbf871c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf8720: ldr             x0, [x0, #0xa20]
    // 0xbf8724: StoreField: r1->field_23 = r0
    //     0xbf8724: stur            w0, [x1, #0x23]
    // 0xbf8728: r0 = Instance_Clip
    //     0xbf8728: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf872c: ldr             x0, [x0, #0x38]
    // 0xbf8730: StoreField: r1->field_2b = r0
    //     0xbf8730: stur            w0, [x1, #0x2b]
    // 0xbf8734: StoreField: r1->field_2f = rZR
    //     0xbf8734: stur            xzr, [x1, #0x2f]
    // 0xbf8738: ldur            x0, [fp, #-0x10]
    // 0xbf873c: StoreField: r1->field_b = r0
    //     0xbf873c: stur            w0, [x1, #0xb]
    // 0xbf8740: r0 = Container()
    //     0xbf8740: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf8744: stur            x0, [fp, #-0x10]
    // 0xbf8748: r16 = Instance_EdgeInsets
    //     0xbf8748: add             x16, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf874c: ldr             x16, [x16, #0x668]
    // 0xbf8750: ldur            lr, [fp, #-0x28]
    // 0xbf8754: stp             lr, x16, [SP, #8]
    // 0xbf8758: ldur            x16, [fp, #-8]
    // 0xbf875c: str             x16, [SP]
    // 0xbf8760: mov             x1, x0
    // 0xbf8764: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, margin, 0x1, null]
    //     0xbf8764: add             x4, PP, #0x35, lsl #12  ; [pp+0x35f50] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "margin", 0x1, Null]
    //     0xbf8768: ldr             x4, [x4, #0xf50]
    // 0xbf876c: r0 = Container()
    //     0xbf876c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf8770: ldur            x0, [fp, #-0x10]
    // 0xbf8774: LeaveFrame
    //     0xbf8774: mov             SP, fp
    //     0xbf8778: ldp             fp, lr, [SP], #0x10
    // 0xbf877c: ret
    //     0xbf877c: ret             
    // 0xbf8780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf8780: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf8784: b               #0xbf6f4c
    // 0xbf8788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf8788: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf878c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf878c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf8790: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbf8790: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbf8794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf8794: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf8798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf8798: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf879c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf879c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87bc: SaveReg d0
    //     0xbf87bc: str             q0, [SP, #-0x10]!
    // 0xbf87c0: stp             x4, x5, [SP, #-0x10]!
    // 0xbf87c4: stp             x2, x3, [SP, #-0x10]!
    // 0xbf87c8: stp             x0, x1, [SP, #-0x10]!
    // 0xbf87cc: r0 = AllocateDouble()
    //     0xbf87cc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf87d0: mov             x6, x0
    // 0xbf87d4: ldp             x0, x1, [SP], #0x10
    // 0xbf87d8: ldp             x2, x3, [SP], #0x10
    // 0xbf87dc: ldp             x4, x5, [SP], #0x10
    // 0xbf87e0: RestoreReg d0
    //     0xbf87e0: ldr             q0, [SP], #0x10
    // 0xbf87e4: b               #0xbf79d4
    // 0xbf87e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf87fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf87fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf8800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf8800: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf8804: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbf8804: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbf8808: SaveReg d0
    //     0xbf8808: str             q0, [SP, #-0x10]!
    // 0xbf880c: SaveReg r1
    //     0xbf880c: str             x1, [SP, #-8]!
    // 0xbf8810: r0 = AllocateDouble()
    //     0xbf8810: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf8814: RestoreReg r1
    //     0xbf8814: ldr             x1, [SP], #8
    // 0xbf8818: RestoreReg d0
    //     0xbf8818: ldr             q0, [SP], #0x10
    // 0xbf881c: b               #0xbf8600
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbf8820, size: 0x858
    // 0xbf8820: EnterFrame
    //     0xbf8820: stp             fp, lr, [SP, #-0x10]!
    //     0xbf8824: mov             fp, SP
    // 0xbf8828: AllocStack(0x50)
    //     0xbf8828: sub             SP, SP, #0x50
    // 0xbf882c: SetupParameters()
    //     0xbf882c: ldr             x0, [fp, #0x20]
    //     0xbf8830: ldur            w1, [x0, #0x17]
    //     0xbf8834: add             x1, x1, HEAP, lsl #32
    //     0xbf8838: stur            x1, [fp, #-8]
    // 0xbf883c: CheckStackOverflow
    //     0xbf883c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf8840: cmp             SP, x16
    //     0xbf8844: b.ls            #0xbf9044
    // 0xbf8848: r1 = 2
    //     0xbf8848: movz            x1, #0x2
    // 0xbf884c: r0 = AllocateContext()
    //     0xbf884c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf8850: mov             x3, x0
    // 0xbf8854: ldur            x0, [fp, #-8]
    // 0xbf8858: stur            x3, [fp, #-0x10]
    // 0xbf885c: StoreField: r3->field_b = r0
    //     0xbf885c: stur            w0, [x3, #0xb]
    // 0xbf8860: ldr             x4, [fp, #0x18]
    // 0xbf8864: StoreField: r3->field_f = r4
    //     0xbf8864: stur            w4, [x3, #0xf]
    // 0xbf8868: ldr             x5, [fp, #0x10]
    // 0xbf886c: StoreField: r3->field_13 = r5
    //     0xbf886c: stur            w5, [x3, #0x13]
    // 0xbf8870: r1 = <Widget>
    //     0xbf8870: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf8874: r2 = 0
    //     0xbf8874: movz            x2, #0
    // 0xbf8878: r0 = _GrowableList()
    //     0xbf8878: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbf887c: mov             x3, x0
    // 0xbf8880: ldur            x2, [fp, #-8]
    // 0xbf8884: stur            x3, [fp, #-0x18]
    // 0xbf8888: LoadField: r0 = r2->field_f
    //     0xbf8888: ldur            w0, [x2, #0xf]
    // 0xbf888c: DecompressPointer r0
    //     0xbf888c: add             x0, x0, HEAP, lsl #32
    // 0xbf8890: LoadField: r1 = r0->field_b
    //     0xbf8890: ldur            w1, [x0, #0xb]
    // 0xbf8894: DecompressPointer r1
    //     0xbf8894: add             x1, x1, HEAP, lsl #32
    // 0xbf8898: cmp             w1, NULL
    // 0xbf889c: b.eq            #0xbf904c
    // 0xbf88a0: LoadField: r0 = r1->field_b
    //     0xbf88a0: ldur            w0, [x1, #0xb]
    // 0xbf88a4: DecompressPointer r0
    //     0xbf88a4: add             x0, x0, HEAP, lsl #32
    // 0xbf88a8: LoadField: r4 = r0->field_57
    //     0xbf88a8: ldur            w4, [x0, #0x57]
    // 0xbf88ac: DecompressPointer r4
    //     0xbf88ac: add             x4, x4, HEAP, lsl #32
    // 0xbf88b0: cmp             w4, NULL
    // 0xbf88b4: b.ne            #0xbf88c4
    // 0xbf88b8: ldr             x5, [fp, #0x10]
    // 0xbf88bc: r0 = Null
    //     0xbf88bc: mov             x0, NULL
    // 0xbf88c0: b               #0xbf8918
    // 0xbf88c4: ldr             x5, [fp, #0x10]
    // 0xbf88c8: LoadField: r0 = r4->field_b
    //     0xbf88c8: ldur            w0, [x4, #0xb]
    // 0xbf88cc: r6 = LoadInt32Instr(r5)
    //     0xbf88cc: sbfx            x6, x5, #1, #0x1f
    //     0xbf88d0: tbz             w5, #0, #0xbf88d8
    //     0xbf88d4: ldur            x6, [x5, #7]
    // 0xbf88d8: r1 = LoadInt32Instr(r0)
    //     0xbf88d8: sbfx            x1, x0, #1, #0x1f
    // 0xbf88dc: mov             x0, x1
    // 0xbf88e0: mov             x1, x6
    // 0xbf88e4: cmp             x1, x0
    // 0xbf88e8: b.hs            #0xbf9050
    // 0xbf88ec: LoadField: r0 = r4->field_f
    //     0xbf88ec: ldur            w0, [x4, #0xf]
    // 0xbf88f0: DecompressPointer r0
    //     0xbf88f0: add             x0, x0, HEAP, lsl #32
    // 0xbf88f4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbf88f4: add             x16, x0, x6, lsl #2
    //     0xbf88f8: ldur            w1, [x16, #0xf]
    // 0xbf88fc: DecompressPointer r1
    //     0xbf88fc: add             x1, x1, HEAP, lsl #32
    // 0xbf8900: cmp             w1, NULL
    // 0xbf8904: b.ne            #0xbf8910
    // 0xbf8908: r0 = Null
    //     0xbf8908: mov             x0, NULL
    // 0xbf890c: b               #0xbf8918
    // 0xbf8910: LoadField: r0 = r1->field_7
    //     0xbf8910: ldur            w0, [x1, #7]
    // 0xbf8914: DecompressPointer r0
    //     0xbf8914: add             x0, x0, HEAP, lsl #32
    // 0xbf8918: r1 = LoadClassIdInstr(r0)
    //     0xbf8918: ldur            x1, [x0, #-1]
    //     0xbf891c: ubfx            x1, x1, #0xc, #0x14
    // 0xbf8920: r16 = "cancel_order"
    //     0xbf8920: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf8924: ldr             x16, [x16, #0x98]
    // 0xbf8928: stp             x16, x0, [SP]
    // 0xbf892c: mov             x0, x1
    // 0xbf8930: mov             lr, x0
    // 0xbf8934: ldr             lr, [x21, lr, lsl #3]
    // 0xbf8938: blr             lr
    // 0xbf893c: tbz             w0, #4, #0xbf8ab8
    // 0xbf8940: ldur            x2, [fp, #-8]
    // 0xbf8944: LoadField: r0 = r2->field_f
    //     0xbf8944: ldur            w0, [x2, #0xf]
    // 0xbf8948: DecompressPointer r0
    //     0xbf8948: add             x0, x0, HEAP, lsl #32
    // 0xbf894c: LoadField: r1 = r0->field_b
    //     0xbf894c: ldur            w1, [x0, #0xb]
    // 0xbf8950: DecompressPointer r1
    //     0xbf8950: add             x1, x1, HEAP, lsl #32
    // 0xbf8954: cmp             w1, NULL
    // 0xbf8958: b.eq            #0xbf9054
    // 0xbf895c: LoadField: r0 = r1->field_b
    //     0xbf895c: ldur            w0, [x1, #0xb]
    // 0xbf8960: DecompressPointer r0
    //     0xbf8960: add             x0, x0, HEAP, lsl #32
    // 0xbf8964: LoadField: r3 = r0->field_57
    //     0xbf8964: ldur            w3, [x0, #0x57]
    // 0xbf8968: DecompressPointer r3
    //     0xbf8968: add             x3, x3, HEAP, lsl #32
    // 0xbf896c: cmp             w3, NULL
    // 0xbf8970: b.ne            #0xbf8980
    // 0xbf8974: ldr             x4, [fp, #0x10]
    // 0xbf8978: r0 = Null
    //     0xbf8978: mov             x0, NULL
    // 0xbf897c: b               #0xbf89d4
    // 0xbf8980: ldr             x4, [fp, #0x10]
    // 0xbf8984: LoadField: r0 = r3->field_b
    //     0xbf8984: ldur            w0, [x3, #0xb]
    // 0xbf8988: r5 = LoadInt32Instr(r4)
    //     0xbf8988: sbfx            x5, x4, #1, #0x1f
    //     0xbf898c: tbz             w4, #0, #0xbf8994
    //     0xbf8990: ldur            x5, [x4, #7]
    // 0xbf8994: r1 = LoadInt32Instr(r0)
    //     0xbf8994: sbfx            x1, x0, #1, #0x1f
    // 0xbf8998: mov             x0, x1
    // 0xbf899c: mov             x1, x5
    // 0xbf89a0: cmp             x1, x0
    // 0xbf89a4: b.hs            #0xbf9058
    // 0xbf89a8: LoadField: r0 = r3->field_f
    //     0xbf89a8: ldur            w0, [x3, #0xf]
    // 0xbf89ac: DecompressPointer r0
    //     0xbf89ac: add             x0, x0, HEAP, lsl #32
    // 0xbf89b0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbf89b0: add             x16, x0, x5, lsl #2
    //     0xbf89b4: ldur            w1, [x16, #0xf]
    // 0xbf89b8: DecompressPointer r1
    //     0xbf89b8: add             x1, x1, HEAP, lsl #32
    // 0xbf89bc: cmp             w1, NULL
    // 0xbf89c0: b.ne            #0xbf89cc
    // 0xbf89c4: r0 = Null
    //     0xbf89c4: mov             x0, NULL
    // 0xbf89c8: b               #0xbf89d4
    // 0xbf89cc: LoadField: r0 = r1->field_7
    //     0xbf89cc: ldur            w0, [x1, #7]
    // 0xbf89d0: DecompressPointer r0
    //     0xbf89d0: add             x0, x0, HEAP, lsl #32
    // 0xbf89d4: r1 = LoadClassIdInstr(r0)
    //     0xbf89d4: ldur            x1, [x0, #-1]
    //     0xbf89d8: ubfx            x1, x1, #0xc, #0x14
    // 0xbf89dc: r16 = "exchange_cancel"
    //     0xbf89dc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] "exchange_cancel"
    //     0xbf89e0: ldr             x16, [x16, #0xdf8]
    // 0xbf89e4: stp             x16, x0, [SP]
    // 0xbf89e8: mov             x0, x1
    // 0xbf89ec: mov             lr, x0
    // 0xbf89f0: ldr             lr, [x21, lr, lsl #3]
    // 0xbf89f4: blr             lr
    // 0xbf89f8: tbz             w0, #4, #0xbf8ab8
    // 0xbf89fc: ldur            x2, [fp, #-8]
    // 0xbf8a00: LoadField: r0 = r2->field_f
    //     0xbf8a00: ldur            w0, [x2, #0xf]
    // 0xbf8a04: DecompressPointer r0
    //     0xbf8a04: add             x0, x0, HEAP, lsl #32
    // 0xbf8a08: LoadField: r1 = r0->field_b
    //     0xbf8a08: ldur            w1, [x0, #0xb]
    // 0xbf8a0c: DecompressPointer r1
    //     0xbf8a0c: add             x1, x1, HEAP, lsl #32
    // 0xbf8a10: cmp             w1, NULL
    // 0xbf8a14: b.eq            #0xbf905c
    // 0xbf8a18: LoadField: r0 = r1->field_b
    //     0xbf8a18: ldur            w0, [x1, #0xb]
    // 0xbf8a1c: DecompressPointer r0
    //     0xbf8a1c: add             x0, x0, HEAP, lsl #32
    // 0xbf8a20: LoadField: r3 = r0->field_57
    //     0xbf8a20: ldur            w3, [x0, #0x57]
    // 0xbf8a24: DecompressPointer r3
    //     0xbf8a24: add             x3, x3, HEAP, lsl #32
    // 0xbf8a28: cmp             w3, NULL
    // 0xbf8a2c: b.ne            #0xbf8a3c
    // 0xbf8a30: ldr             x4, [fp, #0x10]
    // 0xbf8a34: r0 = Null
    //     0xbf8a34: mov             x0, NULL
    // 0xbf8a38: b               #0xbf8a90
    // 0xbf8a3c: ldr             x4, [fp, #0x10]
    // 0xbf8a40: LoadField: r0 = r3->field_b
    //     0xbf8a40: ldur            w0, [x3, #0xb]
    // 0xbf8a44: r5 = LoadInt32Instr(r4)
    //     0xbf8a44: sbfx            x5, x4, #1, #0x1f
    //     0xbf8a48: tbz             w4, #0, #0xbf8a50
    //     0xbf8a4c: ldur            x5, [x4, #7]
    // 0xbf8a50: r1 = LoadInt32Instr(r0)
    //     0xbf8a50: sbfx            x1, x0, #1, #0x1f
    // 0xbf8a54: mov             x0, x1
    // 0xbf8a58: mov             x1, x5
    // 0xbf8a5c: cmp             x1, x0
    // 0xbf8a60: b.hs            #0xbf9060
    // 0xbf8a64: LoadField: r0 = r3->field_f
    //     0xbf8a64: ldur            w0, [x3, #0xf]
    // 0xbf8a68: DecompressPointer r0
    //     0xbf8a68: add             x0, x0, HEAP, lsl #32
    // 0xbf8a6c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbf8a6c: add             x16, x0, x5, lsl #2
    //     0xbf8a70: ldur            w1, [x16, #0xf]
    // 0xbf8a74: DecompressPointer r1
    //     0xbf8a74: add             x1, x1, HEAP, lsl #32
    // 0xbf8a78: cmp             w1, NULL
    // 0xbf8a7c: b.ne            #0xbf8a88
    // 0xbf8a80: r0 = Null
    //     0xbf8a80: mov             x0, NULL
    // 0xbf8a84: b               #0xbf8a90
    // 0xbf8a88: LoadField: r0 = r1->field_7
    //     0xbf8a88: ldur            w0, [x1, #7]
    // 0xbf8a8c: DecompressPointer r0
    //     0xbf8a8c: add             x0, x0, HEAP, lsl #32
    // 0xbf8a90: r1 = LoadClassIdInstr(r0)
    //     0xbf8a90: ldur            x1, [x0, #-1]
    //     0xbf8a94: ubfx            x1, x1, #0xc, #0x14
    // 0xbf8a98: r16 = "cancel_return"
    //     0xbf8a98: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df0] "cancel_return"
    //     0xbf8a9c: ldr             x16, [x16, #0xdf0]
    // 0xbf8aa0: stp             x16, x0, [SP]
    // 0xbf8aa4: mov             x0, x1
    // 0xbf8aa8: mov             lr, x0
    // 0xbf8aac: ldr             lr, [x21, lr, lsl #3]
    // 0xbf8ab0: blr             lr
    // 0xbf8ab4: tbnz            w0, #4, #0xbf8bc4
    // 0xbf8ab8: ldur            x0, [fp, #-0x18]
    // 0xbf8abc: ldr             x1, [fp, #0x18]
    // 0xbf8ac0: r0 = of()
    //     0xbf8ac0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf8ac4: LoadField: r1 = r0->field_5b
    //     0xbf8ac4: ldur            w1, [x0, #0x5b]
    // 0xbf8ac8: DecompressPointer r1
    //     0xbf8ac8: add             x1, x1, HEAP, lsl #32
    // 0xbf8acc: stur            x1, [fp, #-0x20]
    // 0xbf8ad0: r0 = ColorFilter()
    //     0xbf8ad0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbf8ad4: mov             x1, x0
    // 0xbf8ad8: ldur            x0, [fp, #-0x20]
    // 0xbf8adc: stur            x1, [fp, #-0x28]
    // 0xbf8ae0: StoreField: r1->field_7 = r0
    //     0xbf8ae0: stur            w0, [x1, #7]
    // 0xbf8ae4: r2 = Instance_BlendMode
    //     0xbf8ae4: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf8ae8: ldr             x2, [x2, #0xb30]
    // 0xbf8aec: StoreField: r1->field_b = r2
    //     0xbf8aec: stur            w2, [x1, #0xb]
    // 0xbf8af0: r3 = 1
    //     0xbf8af0: movz            x3, #0x1
    // 0xbf8af4: StoreField: r1->field_13 = r3
    //     0xbf8af4: stur            x3, [x1, #0x13]
    // 0xbf8af8: r0 = SvgPicture()
    //     0xbf8af8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf8afc: stur            x0, [fp, #-0x20]
    // 0xbf8b00: r16 = 16.000000
    //     0xbf8b00: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbf8b04: ldr             x16, [x16, #0x188]
    // 0xbf8b08: r30 = 16.000000
    //     0xbf8b08: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbf8b0c: ldr             lr, [lr, #0x188]
    // 0xbf8b10: stp             lr, x16, [SP, #0x10]
    // 0xbf8b14: r16 = Instance_BoxFit
    //     0xbf8b14: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf8b18: ldr             x16, [x16, #0xb18]
    // 0xbf8b1c: ldur            lr, [fp, #-0x28]
    // 0xbf8b20: stp             lr, x16, [SP]
    // 0xbf8b24: mov             x1, x0
    // 0xbf8b28: r2 = "assets/images/x-circle.svg"
    //     0xbf8b28: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a230] "assets/images/x-circle.svg"
    //     0xbf8b2c: ldr             x2, [x2, #0x230]
    // 0xbf8b30: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xbf8b30: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a238] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xbf8b34: ldr             x4, [x4, #0x238]
    // 0xbf8b38: r0 = SvgPicture.asset()
    //     0xbf8b38: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf8b3c: ldur            x0, [fp, #-0x18]
    // 0xbf8b40: LoadField: r1 = r0->field_b
    //     0xbf8b40: ldur            w1, [x0, #0xb]
    // 0xbf8b44: LoadField: r2 = r0->field_f
    //     0xbf8b44: ldur            w2, [x0, #0xf]
    // 0xbf8b48: DecompressPointer r2
    //     0xbf8b48: add             x2, x2, HEAP, lsl #32
    // 0xbf8b4c: LoadField: r3 = r2->field_b
    //     0xbf8b4c: ldur            w3, [x2, #0xb]
    // 0xbf8b50: r2 = LoadInt32Instr(r1)
    //     0xbf8b50: sbfx            x2, x1, #1, #0x1f
    // 0xbf8b54: stur            x2, [fp, #-0x30]
    // 0xbf8b58: r1 = LoadInt32Instr(r3)
    //     0xbf8b58: sbfx            x1, x3, #1, #0x1f
    // 0xbf8b5c: cmp             x2, x1
    // 0xbf8b60: b.ne            #0xbf8b6c
    // 0xbf8b64: mov             x1, x0
    // 0xbf8b68: r0 = _growToNextCapacity()
    //     0xbf8b68: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf8b6c: ldur            x4, [fp, #-0x18]
    // 0xbf8b70: ldur            x2, [fp, #-0x30]
    // 0xbf8b74: add             x3, x2, #1
    // 0xbf8b78: lsl             x0, x3, #1
    // 0xbf8b7c: StoreField: r4->field_b = r0
    //     0xbf8b7c: stur            w0, [x4, #0xb]
    // 0xbf8b80: LoadField: r5 = r4->field_f
    //     0xbf8b80: ldur            w5, [x4, #0xf]
    // 0xbf8b84: DecompressPointer r5
    //     0xbf8b84: add             x5, x5, HEAP, lsl #32
    // 0xbf8b88: mov             x1, x5
    // 0xbf8b8c: ldur            x0, [fp, #-0x20]
    // 0xbf8b90: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbf8b90: add             x25, x1, x2, lsl #2
    //     0xbf8b94: add             x25, x25, #0xf
    //     0xbf8b98: str             w0, [x25]
    //     0xbf8b9c: tbz             w0, #0, #0xbf8bb8
    //     0xbf8ba0: ldurb           w16, [x1, #-1]
    //     0xbf8ba4: ldurb           w17, [x0, #-1]
    //     0xbf8ba8: and             x16, x17, x16, lsr #2
    //     0xbf8bac: tst             x16, HEAP, lsr #32
    //     0xbf8bb0: b.eq            #0xbf8bb8
    //     0xbf8bb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf8bb8: mov             x0, x5
    // 0xbf8bbc: mov             x2, x4
    // 0xbf8bc0: b               #0xbf8da8
    // 0xbf8bc4: ldur            x5, [fp, #-8]
    // 0xbf8bc8: ldur            x4, [fp, #-0x18]
    // 0xbf8bcc: r2 = Instance_BlendMode
    //     0xbf8bcc: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf8bd0: ldr             x2, [x2, #0xb30]
    // 0xbf8bd4: r3 = 1
    //     0xbf8bd4: movz            x3, #0x1
    // 0xbf8bd8: LoadField: r0 = r5->field_f
    //     0xbf8bd8: ldur            w0, [x5, #0xf]
    // 0xbf8bdc: DecompressPointer r0
    //     0xbf8bdc: add             x0, x0, HEAP, lsl #32
    // 0xbf8be0: LoadField: r1 = r0->field_b
    //     0xbf8be0: ldur            w1, [x0, #0xb]
    // 0xbf8be4: DecompressPointer r1
    //     0xbf8be4: add             x1, x1, HEAP, lsl #32
    // 0xbf8be8: cmp             w1, NULL
    // 0xbf8bec: b.eq            #0xbf9064
    // 0xbf8bf0: LoadField: r0 = r1->field_b
    //     0xbf8bf0: ldur            w0, [x1, #0xb]
    // 0xbf8bf4: DecompressPointer r0
    //     0xbf8bf4: add             x0, x0, HEAP, lsl #32
    // 0xbf8bf8: LoadField: r6 = r0->field_57
    //     0xbf8bf8: ldur            w6, [x0, #0x57]
    // 0xbf8bfc: DecompressPointer r6
    //     0xbf8bfc: add             x6, x6, HEAP, lsl #32
    // 0xbf8c00: cmp             w6, NULL
    // 0xbf8c04: b.ne            #0xbf8c14
    // 0xbf8c08: ldr             x7, [fp, #0x10]
    // 0xbf8c0c: r0 = Null
    //     0xbf8c0c: mov             x0, NULL
    // 0xbf8c10: b               #0xbf8c68
    // 0xbf8c14: ldr             x7, [fp, #0x10]
    // 0xbf8c18: LoadField: r0 = r6->field_b
    //     0xbf8c18: ldur            w0, [x6, #0xb]
    // 0xbf8c1c: r8 = LoadInt32Instr(r7)
    //     0xbf8c1c: sbfx            x8, x7, #1, #0x1f
    //     0xbf8c20: tbz             w7, #0, #0xbf8c28
    //     0xbf8c24: ldur            x8, [x7, #7]
    // 0xbf8c28: r1 = LoadInt32Instr(r0)
    //     0xbf8c28: sbfx            x1, x0, #1, #0x1f
    // 0xbf8c2c: mov             x0, x1
    // 0xbf8c30: mov             x1, x8
    // 0xbf8c34: cmp             x1, x0
    // 0xbf8c38: b.hs            #0xbf9068
    // 0xbf8c3c: LoadField: r0 = r6->field_f
    //     0xbf8c3c: ldur            w0, [x6, #0xf]
    // 0xbf8c40: DecompressPointer r0
    //     0xbf8c40: add             x0, x0, HEAP, lsl #32
    // 0xbf8c44: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbf8c44: add             x16, x0, x8, lsl #2
    //     0xbf8c48: ldur            w1, [x16, #0xf]
    // 0xbf8c4c: DecompressPointer r1
    //     0xbf8c4c: add             x1, x1, HEAP, lsl #32
    // 0xbf8c50: cmp             w1, NULL
    // 0xbf8c54: b.ne            #0xbf8c60
    // 0xbf8c58: r0 = Null
    //     0xbf8c58: mov             x0, NULL
    // 0xbf8c5c: b               #0xbf8c68
    // 0xbf8c60: LoadField: r0 = r1->field_7
    //     0xbf8c60: ldur            w0, [x1, #7]
    // 0xbf8c64: DecompressPointer r0
    //     0xbf8c64: add             x0, x0, HEAP, lsl #32
    // 0xbf8c68: r1 = LoadClassIdInstr(r0)
    //     0xbf8c68: ldur            x1, [x0, #-1]
    //     0xbf8c6c: ubfx            x1, x1, #0xc, #0x14
    // 0xbf8c70: r16 = "track_order"
    //     0xbf8c70: add             x16, PP, #0x36, lsl #12  ; [pp+0x36080] "track_order"
    //     0xbf8c74: ldr             x16, [x16, #0x80]
    // 0xbf8c78: stp             x16, x0, [SP]
    // 0xbf8c7c: mov             x0, x1
    // 0xbf8c80: mov             lr, x0
    // 0xbf8c84: ldr             lr, [x21, lr, lsl #3]
    // 0xbf8c88: blr             lr
    // 0xbf8c8c: tbnz            w0, #4, #0xbf8c9c
    // 0xbf8c90: r2 = Instance_Icon
    //     0xbf8c90: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a240] Obj!Icon@d663f1
    //     0xbf8c94: ldr             x2, [x2, #0x240]
    // 0xbf8c98: b               #0xbf8d20
    // 0xbf8c9c: ldr             x1, [fp, #0x18]
    // 0xbf8ca0: r0 = of()
    //     0xbf8ca0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf8ca4: LoadField: r1 = r0->field_5b
    //     0xbf8ca4: ldur            w1, [x0, #0x5b]
    // 0xbf8ca8: DecompressPointer r1
    //     0xbf8ca8: add             x1, x1, HEAP, lsl #32
    // 0xbf8cac: stur            x1, [fp, #-0x20]
    // 0xbf8cb0: r0 = ColorFilter()
    //     0xbf8cb0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbf8cb4: mov             x1, x0
    // 0xbf8cb8: ldur            x0, [fp, #-0x20]
    // 0xbf8cbc: stur            x1, [fp, #-0x28]
    // 0xbf8cc0: StoreField: r1->field_7 = r0
    //     0xbf8cc0: stur            w0, [x1, #7]
    // 0xbf8cc4: r0 = Instance_BlendMode
    //     0xbf8cc4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf8cc8: ldr             x0, [x0, #0xb30]
    // 0xbf8ccc: StoreField: r1->field_b = r0
    //     0xbf8ccc: stur            w0, [x1, #0xb]
    // 0xbf8cd0: r0 = 1
    //     0xbf8cd0: movz            x0, #0x1
    // 0xbf8cd4: StoreField: r1->field_13 = r0
    //     0xbf8cd4: stur            x0, [x1, #0x13]
    // 0xbf8cd8: r0 = SvgPicture()
    //     0xbf8cd8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf8cdc: stur            x0, [fp, #-0x20]
    // 0xbf8ce0: r16 = 16.000000
    //     0xbf8ce0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbf8ce4: ldr             x16, [x16, #0x188]
    // 0xbf8ce8: r30 = 16.000000
    //     0xbf8ce8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbf8cec: ldr             lr, [lr, #0x188]
    // 0xbf8cf0: stp             lr, x16, [SP, #0x10]
    // 0xbf8cf4: r16 = Instance_BoxFit
    //     0xbf8cf4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf8cf8: ldr             x16, [x16, #0xb18]
    // 0xbf8cfc: ldur            lr, [fp, #-0x28]
    // 0xbf8d00: stp             lr, x16, [SP]
    // 0xbf8d04: mov             x1, x0
    // 0xbf8d08: r2 = "assets/images/exchange.svg"
    //     0xbf8d08: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xbf8d0c: ldr             x2, [x2, #0xca0]
    // 0xbf8d10: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xbf8d10: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a238] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xbf8d14: ldr             x4, [x4, #0x238]
    // 0xbf8d18: r0 = SvgPicture.asset()
    //     0xbf8d18: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf8d1c: ldur            x2, [fp, #-0x20]
    // 0xbf8d20: ldur            x0, [fp, #-0x18]
    // 0xbf8d24: stur            x2, [fp, #-0x20]
    // 0xbf8d28: LoadField: r1 = r0->field_b
    //     0xbf8d28: ldur            w1, [x0, #0xb]
    // 0xbf8d2c: LoadField: r3 = r0->field_f
    //     0xbf8d2c: ldur            w3, [x0, #0xf]
    // 0xbf8d30: DecompressPointer r3
    //     0xbf8d30: add             x3, x3, HEAP, lsl #32
    // 0xbf8d34: LoadField: r4 = r3->field_b
    //     0xbf8d34: ldur            w4, [x3, #0xb]
    // 0xbf8d38: r3 = LoadInt32Instr(r1)
    //     0xbf8d38: sbfx            x3, x1, #1, #0x1f
    // 0xbf8d3c: stur            x3, [fp, #-0x30]
    // 0xbf8d40: r1 = LoadInt32Instr(r4)
    //     0xbf8d40: sbfx            x1, x4, #1, #0x1f
    // 0xbf8d44: cmp             x3, x1
    // 0xbf8d48: b.ne            #0xbf8d54
    // 0xbf8d4c: mov             x1, x0
    // 0xbf8d50: r0 = _growToNextCapacity()
    //     0xbf8d50: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf8d54: ldur            x2, [fp, #-0x18]
    // 0xbf8d58: ldur            x3, [fp, #-0x30]
    // 0xbf8d5c: add             x4, x3, #1
    // 0xbf8d60: lsl             x0, x4, #1
    // 0xbf8d64: StoreField: r2->field_b = r0
    //     0xbf8d64: stur            w0, [x2, #0xb]
    // 0xbf8d68: LoadField: r5 = r2->field_f
    //     0xbf8d68: ldur            w5, [x2, #0xf]
    // 0xbf8d6c: DecompressPointer r5
    //     0xbf8d6c: add             x5, x5, HEAP, lsl #32
    // 0xbf8d70: mov             x1, x5
    // 0xbf8d74: ldur            x0, [fp, #-0x20]
    // 0xbf8d78: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf8d78: add             x25, x1, x3, lsl #2
    //     0xbf8d7c: add             x25, x25, #0xf
    //     0xbf8d80: str             w0, [x25]
    //     0xbf8d84: tbz             w0, #0, #0xbf8da0
    //     0xbf8d88: ldurb           w16, [x1, #-1]
    //     0xbf8d8c: ldurb           w17, [x0, #-1]
    //     0xbf8d90: and             x16, x17, x16, lsr #2
    //     0xbf8d94: tst             x16, HEAP, lsr #32
    //     0xbf8d98: b.eq            #0xbf8da0
    //     0xbf8d9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf8da0: mov             x3, x4
    // 0xbf8da4: mov             x0, x5
    // 0xbf8da8: stur            x3, [fp, #-0x30]
    // 0xbf8dac: LoadField: r1 = r0->field_b
    //     0xbf8dac: ldur            w1, [x0, #0xb]
    // 0xbf8db0: r0 = LoadInt32Instr(r1)
    //     0xbf8db0: sbfx            x0, x1, #1, #0x1f
    // 0xbf8db4: cmp             x3, x0
    // 0xbf8db8: b.ne            #0xbf8dc4
    // 0xbf8dbc: mov             x1, x2
    // 0xbf8dc0: r0 = _growToNextCapacity()
    //     0xbf8dc0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf8dc4: ldur            x4, [fp, #-8]
    // 0xbf8dc8: ldur            x2, [fp, #-0x18]
    // 0xbf8dcc: ldur            x3, [fp, #-0x30]
    // 0xbf8dd0: add             x0, x3, #1
    // 0xbf8dd4: lsl             x1, x0, #1
    // 0xbf8dd8: StoreField: r2->field_b = r1
    //     0xbf8dd8: stur            w1, [x2, #0xb]
    // 0xbf8ddc: mov             x1, x3
    // 0xbf8de0: cmp             x1, x0
    // 0xbf8de4: b.hs            #0xbf906c
    // 0xbf8de8: LoadField: r0 = r2->field_f
    //     0xbf8de8: ldur            w0, [x2, #0xf]
    // 0xbf8dec: DecompressPointer r0
    //     0xbf8dec: add             x0, x0, HEAP, lsl #32
    // 0xbf8df0: add             x1, x0, x3, lsl #2
    // 0xbf8df4: r16 = Instance_SizedBox
    //     0xbf8df4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xbf8df8: ldr             x16, [x16, #0x998]
    // 0xbf8dfc: StoreField: r1->field_f = r16
    //     0xbf8dfc: stur            w16, [x1, #0xf]
    // 0xbf8e00: LoadField: r0 = r4->field_f
    //     0xbf8e00: ldur            w0, [x4, #0xf]
    // 0xbf8e04: DecompressPointer r0
    //     0xbf8e04: add             x0, x0, HEAP, lsl #32
    // 0xbf8e08: LoadField: r1 = r0->field_b
    //     0xbf8e08: ldur            w1, [x0, #0xb]
    // 0xbf8e0c: DecompressPointer r1
    //     0xbf8e0c: add             x1, x1, HEAP, lsl #32
    // 0xbf8e10: cmp             w1, NULL
    // 0xbf8e14: b.eq            #0xbf9070
    // 0xbf8e18: LoadField: r0 = r1->field_b
    //     0xbf8e18: ldur            w0, [x1, #0xb]
    // 0xbf8e1c: DecompressPointer r0
    //     0xbf8e1c: add             x0, x0, HEAP, lsl #32
    // 0xbf8e20: LoadField: r3 = r0->field_57
    //     0xbf8e20: ldur            w3, [x0, #0x57]
    // 0xbf8e24: DecompressPointer r3
    //     0xbf8e24: add             x3, x3, HEAP, lsl #32
    // 0xbf8e28: cmp             w3, NULL
    // 0xbf8e2c: b.ne            #0xbf8e38
    // 0xbf8e30: r0 = Null
    //     0xbf8e30: mov             x0, NULL
    // 0xbf8e34: b               #0xbf8e88
    // 0xbf8e38: ldr             x0, [fp, #0x10]
    // 0xbf8e3c: LoadField: r1 = r3->field_b
    //     0xbf8e3c: ldur            w1, [x3, #0xb]
    // 0xbf8e40: r4 = LoadInt32Instr(r0)
    //     0xbf8e40: sbfx            x4, x0, #1, #0x1f
    //     0xbf8e44: tbz             w0, #0, #0xbf8e4c
    //     0xbf8e48: ldur            x4, [x0, #7]
    // 0xbf8e4c: r0 = LoadInt32Instr(r1)
    //     0xbf8e4c: sbfx            x0, x1, #1, #0x1f
    // 0xbf8e50: mov             x1, x4
    // 0xbf8e54: cmp             x1, x0
    // 0xbf8e58: b.hs            #0xbf9074
    // 0xbf8e5c: LoadField: r0 = r3->field_f
    //     0xbf8e5c: ldur            w0, [x3, #0xf]
    // 0xbf8e60: DecompressPointer r0
    //     0xbf8e60: add             x0, x0, HEAP, lsl #32
    // 0xbf8e64: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbf8e64: add             x16, x0, x4, lsl #2
    //     0xbf8e68: ldur            w1, [x16, #0xf]
    // 0xbf8e6c: DecompressPointer r1
    //     0xbf8e6c: add             x1, x1, HEAP, lsl #32
    // 0xbf8e70: cmp             w1, NULL
    // 0xbf8e74: b.ne            #0xbf8e80
    // 0xbf8e78: r0 = Null
    //     0xbf8e78: mov             x0, NULL
    // 0xbf8e7c: b               #0xbf8e88
    // 0xbf8e80: LoadField: r0 = r1->field_b
    //     0xbf8e80: ldur            w0, [x1, #0xb]
    // 0xbf8e84: DecompressPointer r0
    //     0xbf8e84: add             x0, x0, HEAP, lsl #32
    // 0xbf8e88: cmp             w0, NULL
    // 0xbf8e8c: b.ne            #0xbf8e94
    // 0xbf8e90: r0 = ""
    //     0xbf8e90: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf8e94: r1 = LoadClassIdInstr(r0)
    //     0xbf8e94: ldur            x1, [x0, #-1]
    //     0xbf8e98: ubfx            x1, x1, #0xc, #0x14
    // 0xbf8e9c: str             x0, [SP]
    // 0xbf8ea0: mov             x0, x1
    // 0xbf8ea4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbf8ea4: sub             lr, x0, #1, lsl #12
    //     0xbf8ea8: ldr             lr, [x21, lr, lsl #3]
    //     0xbf8eac: blr             lr
    // 0xbf8eb0: ldr             x1, [fp, #0x18]
    // 0xbf8eb4: stur            x0, [fp, #-8]
    // 0xbf8eb8: r0 = of()
    //     0xbf8eb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf8ebc: LoadField: r1 = r0->field_87
    //     0xbf8ebc: ldur            w1, [x0, #0x87]
    // 0xbf8ec0: DecompressPointer r1
    //     0xbf8ec0: add             x1, x1, HEAP, lsl #32
    // 0xbf8ec4: LoadField: r0 = r1->field_2b
    //     0xbf8ec4: ldur            w0, [x1, #0x2b]
    // 0xbf8ec8: DecompressPointer r0
    //     0xbf8ec8: add             x0, x0, HEAP, lsl #32
    // 0xbf8ecc: r16 = 12.000000
    //     0xbf8ecc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf8ed0: ldr             x16, [x16, #0x9e8]
    // 0xbf8ed4: r30 = Instance_Color
    //     0xbf8ed4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf8ed8: stp             lr, x16, [SP]
    // 0xbf8edc: mov             x1, x0
    // 0xbf8ee0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf8ee0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf8ee4: ldr             x4, [x4, #0xaa0]
    // 0xbf8ee8: r0 = copyWith()
    //     0xbf8ee8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf8eec: stur            x0, [fp, #-0x20]
    // 0xbf8ef0: r0 = Text()
    //     0xbf8ef0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf8ef4: mov             x2, x0
    // 0xbf8ef8: ldur            x0, [fp, #-8]
    // 0xbf8efc: stur            x2, [fp, #-0x28]
    // 0xbf8f00: StoreField: r2->field_b = r0
    //     0xbf8f00: stur            w0, [x2, #0xb]
    // 0xbf8f04: ldur            x0, [fp, #-0x20]
    // 0xbf8f08: StoreField: r2->field_13 = r0
    //     0xbf8f08: stur            w0, [x2, #0x13]
    // 0xbf8f0c: ldur            x0, [fp, #-0x18]
    // 0xbf8f10: LoadField: r1 = r0->field_b
    //     0xbf8f10: ldur            w1, [x0, #0xb]
    // 0xbf8f14: LoadField: r3 = r0->field_f
    //     0xbf8f14: ldur            w3, [x0, #0xf]
    // 0xbf8f18: DecompressPointer r3
    //     0xbf8f18: add             x3, x3, HEAP, lsl #32
    // 0xbf8f1c: LoadField: r4 = r3->field_b
    //     0xbf8f1c: ldur            w4, [x3, #0xb]
    // 0xbf8f20: r3 = LoadInt32Instr(r1)
    //     0xbf8f20: sbfx            x3, x1, #1, #0x1f
    // 0xbf8f24: stur            x3, [fp, #-0x30]
    // 0xbf8f28: r1 = LoadInt32Instr(r4)
    //     0xbf8f28: sbfx            x1, x4, #1, #0x1f
    // 0xbf8f2c: cmp             x3, x1
    // 0xbf8f30: b.ne            #0xbf8f3c
    // 0xbf8f34: mov             x1, x0
    // 0xbf8f38: r0 = _growToNextCapacity()
    //     0xbf8f38: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbf8f3c: ldur            x2, [fp, #-0x18]
    // 0xbf8f40: ldur            x3, [fp, #-0x30]
    // 0xbf8f44: add             x0, x3, #1
    // 0xbf8f48: lsl             x1, x0, #1
    // 0xbf8f4c: StoreField: r2->field_b = r1
    //     0xbf8f4c: stur            w1, [x2, #0xb]
    // 0xbf8f50: LoadField: r1 = r2->field_f
    //     0xbf8f50: ldur            w1, [x2, #0xf]
    // 0xbf8f54: DecompressPointer r1
    //     0xbf8f54: add             x1, x1, HEAP, lsl #32
    // 0xbf8f58: ldur            x0, [fp, #-0x28]
    // 0xbf8f5c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbf8f5c: add             x25, x1, x3, lsl #2
    //     0xbf8f60: add             x25, x25, #0xf
    //     0xbf8f64: str             w0, [x25]
    //     0xbf8f68: tbz             w0, #0, #0xbf8f84
    //     0xbf8f6c: ldurb           w16, [x1, #-1]
    //     0xbf8f70: ldurb           w17, [x0, #-1]
    //     0xbf8f74: and             x16, x17, x16, lsr #2
    //     0xbf8f78: tst             x16, HEAP, lsr #32
    //     0xbf8f7c: b.eq            #0xbf8f84
    //     0xbf8f80: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbf8f84: r0 = Row()
    //     0xbf8f84: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf8f88: mov             x1, x0
    // 0xbf8f8c: r0 = Instance_Axis
    //     0xbf8f8c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf8f90: stur            x1, [fp, #-8]
    // 0xbf8f94: StoreField: r1->field_f = r0
    //     0xbf8f94: stur            w0, [x1, #0xf]
    // 0xbf8f98: r0 = Instance_MainAxisAlignment
    //     0xbf8f98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf8f9c: ldr             x0, [x0, #0xa08]
    // 0xbf8fa0: StoreField: r1->field_13 = r0
    //     0xbf8fa0: stur            w0, [x1, #0x13]
    // 0xbf8fa4: r0 = Instance_MainAxisSize
    //     0xbf8fa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf8fa8: ldr             x0, [x0, #0xa10]
    // 0xbf8fac: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf8fac: stur            w0, [x1, #0x17]
    // 0xbf8fb0: r0 = Instance_CrossAxisAlignment
    //     0xbf8fb0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf8fb4: ldr             x0, [x0, #0xa18]
    // 0xbf8fb8: StoreField: r1->field_1b = r0
    //     0xbf8fb8: stur            w0, [x1, #0x1b]
    // 0xbf8fbc: r0 = Instance_VerticalDirection
    //     0xbf8fbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf8fc0: ldr             x0, [x0, #0xa20]
    // 0xbf8fc4: StoreField: r1->field_23 = r0
    //     0xbf8fc4: stur            w0, [x1, #0x23]
    // 0xbf8fc8: r0 = Instance_Clip
    //     0xbf8fc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf8fcc: ldr             x0, [x0, #0x38]
    // 0xbf8fd0: StoreField: r1->field_2b = r0
    //     0xbf8fd0: stur            w0, [x1, #0x2b]
    // 0xbf8fd4: StoreField: r1->field_2f = rZR
    //     0xbf8fd4: stur            xzr, [x1, #0x2f]
    // 0xbf8fd8: ldur            x0, [fp, #-0x18]
    // 0xbf8fdc: StoreField: r1->field_b = r0
    //     0xbf8fdc: stur            w0, [x1, #0xb]
    // 0xbf8fe0: r0 = InkWell()
    //     0xbf8fe0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbf8fe4: mov             x3, x0
    // 0xbf8fe8: ldur            x0, [fp, #-8]
    // 0xbf8fec: stur            x3, [fp, #-0x18]
    // 0xbf8ff0: StoreField: r3->field_b = r0
    //     0xbf8ff0: stur            w0, [x3, #0xb]
    // 0xbf8ff4: ldur            x2, [fp, #-0x10]
    // 0xbf8ff8: r1 = Function '<anonymous closure>':.
    //     0xbf8ff8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a248] AnonymousClosure: (0xbf9078), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf8ffc: ldr             x1, [x1, #0x248]
    // 0xbf9000: r0 = AllocateClosure()
    //     0xbf9000: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf9004: mov             x1, x0
    // 0xbf9008: ldur            x0, [fp, #-0x18]
    // 0xbf900c: StoreField: r0->field_f = r1
    //     0xbf900c: stur            w1, [x0, #0xf]
    // 0xbf9010: r1 = true
    //     0xbf9010: add             x1, NULL, #0x20  ; true
    // 0xbf9014: StoreField: r0->field_43 = r1
    //     0xbf9014: stur            w1, [x0, #0x43]
    // 0xbf9018: r2 = Instance_BoxShape
    //     0xbf9018: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf901c: ldr             x2, [x2, #0x80]
    // 0xbf9020: StoreField: r0->field_47 = r2
    //     0xbf9020: stur            w2, [x0, #0x47]
    // 0xbf9024: StoreField: r0->field_6f = r1
    //     0xbf9024: stur            w1, [x0, #0x6f]
    // 0xbf9028: r2 = false
    //     0xbf9028: add             x2, NULL, #0x30  ; false
    // 0xbf902c: StoreField: r0->field_73 = r2
    //     0xbf902c: stur            w2, [x0, #0x73]
    // 0xbf9030: StoreField: r0->field_83 = r1
    //     0xbf9030: stur            w1, [x0, #0x83]
    // 0xbf9034: StoreField: r0->field_7b = r2
    //     0xbf9034: stur            w2, [x0, #0x7b]
    // 0xbf9038: LeaveFrame
    //     0xbf9038: mov             SP, fp
    //     0xbf903c: ldp             fp, lr, [SP], #0x10
    // 0xbf9040: ret
    //     0xbf9040: ret             
    // 0xbf9044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9048: b               #0xbf8848
    // 0xbf904c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf904c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9050: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf9050: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf9054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9054: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9058: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf9058: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf905c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf905c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9060: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf9060: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf9064: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9064: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9068: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf9068: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf906c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf906c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf9070: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9070: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9074: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf9074: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf9078, size: 0x68c
    // 0xbf9078: EnterFrame
    //     0xbf9078: stp             fp, lr, [SP, #-0x10]!
    //     0xbf907c: mov             fp, SP
    // 0xbf9080: AllocStack(0x60)
    //     0xbf9080: sub             SP, SP, #0x60
    // 0xbf9084: SetupParameters()
    //     0xbf9084: ldr             x0, [fp, #0x10]
    //     0xbf9088: ldur            w2, [x0, #0x17]
    //     0xbf908c: add             x2, x2, HEAP, lsl #32
    //     0xbf9090: stur            x2, [fp, #-0x10]
    // 0xbf9094: CheckStackOverflow
    //     0xbf9094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9098: cmp             SP, x16
    //     0xbf909c: b.ls            #0xbf96c0
    // 0xbf90a0: LoadField: r3 = r2->field_b
    //     0xbf90a0: ldur            w3, [x2, #0xb]
    // 0xbf90a4: DecompressPointer r3
    //     0xbf90a4: add             x3, x3, HEAP, lsl #32
    // 0xbf90a8: stur            x3, [fp, #-8]
    // 0xbf90ac: LoadField: r0 = r3->field_f
    //     0xbf90ac: ldur            w0, [x3, #0xf]
    // 0xbf90b0: DecompressPointer r0
    //     0xbf90b0: add             x0, x0, HEAP, lsl #32
    // 0xbf90b4: LoadField: r1 = r0->field_b
    //     0xbf90b4: ldur            w1, [x0, #0xb]
    // 0xbf90b8: DecompressPointer r1
    //     0xbf90b8: add             x1, x1, HEAP, lsl #32
    // 0xbf90bc: cmp             w1, NULL
    // 0xbf90c0: b.eq            #0xbf96c8
    // 0xbf90c4: LoadField: r0 = r1->field_b
    //     0xbf90c4: ldur            w0, [x1, #0xb]
    // 0xbf90c8: DecompressPointer r0
    //     0xbf90c8: add             x0, x0, HEAP, lsl #32
    // 0xbf90cc: LoadField: r4 = r0->field_57
    //     0xbf90cc: ldur            w4, [x0, #0x57]
    // 0xbf90d0: DecompressPointer r4
    //     0xbf90d0: add             x4, x4, HEAP, lsl #32
    // 0xbf90d4: cmp             w4, NULL
    // 0xbf90d8: b.ne            #0xbf90e4
    // 0xbf90dc: r0 = Null
    //     0xbf90dc: mov             x0, NULL
    // 0xbf90e0: b               #0xbf9138
    // 0xbf90e4: LoadField: r0 = r2->field_13
    //     0xbf90e4: ldur            w0, [x2, #0x13]
    // 0xbf90e8: DecompressPointer r0
    //     0xbf90e8: add             x0, x0, HEAP, lsl #32
    // 0xbf90ec: LoadField: r1 = r4->field_b
    //     0xbf90ec: ldur            w1, [x4, #0xb]
    // 0xbf90f0: r5 = LoadInt32Instr(r0)
    //     0xbf90f0: sbfx            x5, x0, #1, #0x1f
    //     0xbf90f4: tbz             w0, #0, #0xbf90fc
    //     0xbf90f8: ldur            x5, [x0, #7]
    // 0xbf90fc: r0 = LoadInt32Instr(r1)
    //     0xbf90fc: sbfx            x0, x1, #1, #0x1f
    // 0xbf9100: mov             x1, x5
    // 0xbf9104: cmp             x1, x0
    // 0xbf9108: b.hs            #0xbf96cc
    // 0xbf910c: LoadField: r0 = r4->field_f
    //     0xbf910c: ldur            w0, [x4, #0xf]
    // 0xbf9110: DecompressPointer r0
    //     0xbf9110: add             x0, x0, HEAP, lsl #32
    // 0xbf9114: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbf9114: add             x16, x0, x5, lsl #2
    //     0xbf9118: ldur            w1, [x16, #0xf]
    // 0xbf911c: DecompressPointer r1
    //     0xbf911c: add             x1, x1, HEAP, lsl #32
    // 0xbf9120: cmp             w1, NULL
    // 0xbf9124: b.ne            #0xbf9130
    // 0xbf9128: r0 = Null
    //     0xbf9128: mov             x0, NULL
    // 0xbf912c: b               #0xbf9138
    // 0xbf9130: LoadField: r0 = r1->field_7
    //     0xbf9130: ldur            w0, [x1, #7]
    // 0xbf9134: DecompressPointer r0
    //     0xbf9134: add             x0, x0, HEAP, lsl #32
    // 0xbf9138: r1 = LoadClassIdInstr(r0)
    //     0xbf9138: ldur            x1, [x0, #-1]
    //     0xbf913c: ubfx            x1, x1, #0xc, #0x14
    // 0xbf9140: r16 = "cancel_order"
    //     0xbf9140: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf9144: ldr             x16, [x16, #0x98]
    // 0xbf9148: stp             x16, x0, [SP]
    // 0xbf914c: mov             x0, x1
    // 0xbf9150: mov             lr, x0
    // 0xbf9154: ldr             lr, [x21, lr, lsl #3]
    // 0xbf9158: blr             lr
    // 0xbf915c: tbnz            w0, #4, #0xbf925c
    // 0xbf9160: ldur            x2, [fp, #-8]
    // 0xbf9164: LoadField: r0 = r2->field_f
    //     0xbf9164: ldur            w0, [x2, #0xf]
    // 0xbf9168: DecompressPointer r0
    //     0xbf9168: add             x0, x0, HEAP, lsl #32
    // 0xbf916c: LoadField: r1 = r0->field_b
    //     0xbf916c: ldur            w1, [x0, #0xb]
    // 0xbf9170: DecompressPointer r1
    //     0xbf9170: add             x1, x1, HEAP, lsl #32
    // 0xbf9174: cmp             w1, NULL
    // 0xbf9178: b.eq            #0xbf96d0
    // 0xbf917c: LoadField: r0 = r1->field_b
    //     0xbf917c: ldur            w0, [x1, #0xb]
    // 0xbf9180: DecompressPointer r0
    //     0xbf9180: add             x0, x0, HEAP, lsl #32
    // 0xbf9184: LoadField: r2 = r0->field_7f
    //     0xbf9184: ldur            w2, [x0, #0x7f]
    // 0xbf9188: DecompressPointer r2
    //     0xbf9188: add             x2, x2, HEAP, lsl #32
    // 0xbf918c: cmp             w2, NULL
    // 0xbf9190: b.ne            #0xbf919c
    // 0xbf9194: r0 = Null
    //     0xbf9194: mov             x0, NULL
    // 0xbf9198: b               #0xbf91a4
    // 0xbf919c: LoadField: r0 = r2->field_2b
    //     0xbf919c: ldur            w0, [x2, #0x2b]
    // 0xbf91a0: DecompressPointer r0
    //     0xbf91a0: add             x0, x0, HEAP, lsl #32
    // 0xbf91a4: cmp             w0, NULL
    // 0xbf91a8: b.ne            #0xbf91b4
    // 0xbf91ac: ldur            x3, [fp, #-0x10]
    // 0xbf91b0: b               #0xbf9204
    // 0xbf91b4: tbnz            w0, #4, #0xbf9200
    // 0xbf91b8: ldur            x3, [fp, #-0x10]
    // 0xbf91bc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbf91bc: ldur            w0, [x1, #0x17]
    // 0xbf91c0: DecompressPointer r0
    //     0xbf91c0: add             x0, x0, HEAP, lsl #32
    // 0xbf91c4: LoadField: r2 = r1->field_b
    //     0xbf91c4: ldur            w2, [x1, #0xb]
    // 0xbf91c8: DecompressPointer r2
    //     0xbf91c8: add             x2, x2, HEAP, lsl #32
    // 0xbf91cc: LoadField: r1 = r2->field_7
    //     0xbf91cc: ldur            w1, [x2, #7]
    // 0xbf91d0: DecompressPointer r1
    //     0xbf91d0: add             x1, x1, HEAP, lsl #32
    // 0xbf91d4: LoadField: r4 = r3->field_f
    //     0xbf91d4: ldur            w4, [x3, #0xf]
    // 0xbf91d8: DecompressPointer r4
    //     0xbf91d8: add             x4, x4, HEAP, lsl #32
    // 0xbf91dc: stp             x2, x0, [SP, #0x10]
    // 0xbf91e0: stp             x4, x1, [SP]
    // 0xbf91e4: r4 = 0
    //     0xbf91e4: movz            x4, #0
    // 0xbf91e8: ldr             x0, [SP, #0x18]
    // 0xbf91ec: r16 = UnlinkedCall_0x613b5c
    //     0xbf91ec: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a250] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf91f0: add             x16, x16, #0x250
    // 0xbf91f4: ldp             x5, lr, [x16]
    // 0xbf91f8: blr             lr
    // 0xbf91fc: b               #0xbf96b0
    // 0xbf9200: ldur            x3, [fp, #-0x10]
    // 0xbf9204: LoadField: r0 = r1->field_13
    //     0xbf9204: ldur            w0, [x1, #0x13]
    // 0xbf9208: DecompressPointer r0
    //     0xbf9208: add             x0, x0, HEAP, lsl #32
    // 0xbf920c: LoadField: r2 = r1->field_b
    //     0xbf920c: ldur            w2, [x1, #0xb]
    // 0xbf9210: DecompressPointer r2
    //     0xbf9210: add             x2, x2, HEAP, lsl #32
    // 0xbf9214: LoadField: r1 = r2->field_37
    //     0xbf9214: ldur            w1, [x2, #0x37]
    // 0xbf9218: DecompressPointer r1
    //     0xbf9218: add             x1, x1, HEAP, lsl #32
    // 0xbf921c: LoadField: r4 = r2->field_7
    //     0xbf921c: ldur            w4, [x2, #7]
    // 0xbf9220: DecompressPointer r4
    //     0xbf9220: add             x4, x4, HEAP, lsl #32
    // 0xbf9224: LoadField: r5 = r3->field_f
    //     0xbf9224: ldur            w5, [x3, #0xf]
    // 0xbf9228: DecompressPointer r5
    //     0xbf9228: add             x5, x5, HEAP, lsl #32
    // 0xbf922c: LoadField: r3 = r2->field_2f
    //     0xbf922c: ldur            w3, [x2, #0x2f]
    // 0xbf9230: DecompressPointer r3
    //     0xbf9230: add             x3, x3, HEAP, lsl #32
    // 0xbf9234: stp             x1, x0, [SP, #0x18]
    // 0xbf9238: stp             x5, x4, [SP, #8]
    // 0xbf923c: str             x3, [SP]
    // 0xbf9240: r4 = 0
    //     0xbf9240: movz            x4, #0
    // 0xbf9244: ldr             x0, [SP, #0x20]
    // 0xbf9248: r16 = UnlinkedCall_0x613b5c
    //     0xbf9248: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a260] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf924c: add             x16, x16, #0x260
    // 0xbf9250: ldp             x5, lr, [x16]
    // 0xbf9254: blr             lr
    // 0xbf9258: b               #0xbf96b0
    // 0xbf925c: ldur            x3, [fp, #-0x10]
    // 0xbf9260: ldur            x2, [fp, #-8]
    // 0xbf9264: LoadField: r0 = r2->field_f
    //     0xbf9264: ldur            w0, [x2, #0xf]
    // 0xbf9268: DecompressPointer r0
    //     0xbf9268: add             x0, x0, HEAP, lsl #32
    // 0xbf926c: LoadField: r1 = r0->field_b
    //     0xbf926c: ldur            w1, [x0, #0xb]
    // 0xbf9270: DecompressPointer r1
    //     0xbf9270: add             x1, x1, HEAP, lsl #32
    // 0xbf9274: cmp             w1, NULL
    // 0xbf9278: b.eq            #0xbf96d4
    // 0xbf927c: LoadField: r0 = r1->field_b
    //     0xbf927c: ldur            w0, [x1, #0xb]
    // 0xbf9280: DecompressPointer r0
    //     0xbf9280: add             x0, x0, HEAP, lsl #32
    // 0xbf9284: LoadField: r4 = r0->field_57
    //     0xbf9284: ldur            w4, [x0, #0x57]
    // 0xbf9288: DecompressPointer r4
    //     0xbf9288: add             x4, x4, HEAP, lsl #32
    // 0xbf928c: cmp             w4, NULL
    // 0xbf9290: b.ne            #0xbf929c
    // 0xbf9294: r0 = Null
    //     0xbf9294: mov             x0, NULL
    // 0xbf9298: b               #0xbf92f0
    // 0xbf929c: LoadField: r0 = r3->field_13
    //     0xbf929c: ldur            w0, [x3, #0x13]
    // 0xbf92a0: DecompressPointer r0
    //     0xbf92a0: add             x0, x0, HEAP, lsl #32
    // 0xbf92a4: LoadField: r1 = r4->field_b
    //     0xbf92a4: ldur            w1, [x4, #0xb]
    // 0xbf92a8: r5 = LoadInt32Instr(r0)
    //     0xbf92a8: sbfx            x5, x0, #1, #0x1f
    //     0xbf92ac: tbz             w0, #0, #0xbf92b4
    //     0xbf92b0: ldur            x5, [x0, #7]
    // 0xbf92b4: r0 = LoadInt32Instr(r1)
    //     0xbf92b4: sbfx            x0, x1, #1, #0x1f
    // 0xbf92b8: mov             x1, x5
    // 0xbf92bc: cmp             x1, x0
    // 0xbf92c0: b.hs            #0xbf96d8
    // 0xbf92c4: LoadField: r0 = r4->field_f
    //     0xbf92c4: ldur            w0, [x4, #0xf]
    // 0xbf92c8: DecompressPointer r0
    //     0xbf92c8: add             x0, x0, HEAP, lsl #32
    // 0xbf92cc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbf92cc: add             x16, x0, x5, lsl #2
    //     0xbf92d0: ldur            w1, [x16, #0xf]
    // 0xbf92d4: DecompressPointer r1
    //     0xbf92d4: add             x1, x1, HEAP, lsl #32
    // 0xbf92d8: cmp             w1, NULL
    // 0xbf92dc: b.ne            #0xbf92e8
    // 0xbf92e0: r0 = Null
    //     0xbf92e0: mov             x0, NULL
    // 0xbf92e4: b               #0xbf92f0
    // 0xbf92e8: LoadField: r0 = r1->field_7
    //     0xbf92e8: ldur            w0, [x1, #7]
    // 0xbf92ec: DecompressPointer r0
    //     0xbf92ec: add             x0, x0, HEAP, lsl #32
    // 0xbf92f0: r1 = LoadClassIdInstr(r0)
    //     0xbf92f0: ldur            x1, [x0, #-1]
    //     0xbf92f4: ubfx            x1, x1, #0xc, #0x14
    // 0xbf92f8: r16 = "exchange_cancel"
    //     0xbf92f8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] "exchange_cancel"
    //     0xbf92fc: ldr             x16, [x16, #0xdf8]
    // 0xbf9300: stp             x16, x0, [SP]
    // 0xbf9304: mov             x0, x1
    // 0xbf9308: mov             lr, x0
    // 0xbf930c: ldr             lr, [x21, lr, lsl #3]
    // 0xbf9310: blr             lr
    // 0xbf9314: tbnz            w0, #4, #0xbf9438
    // 0xbf9318: ldur            x3, [fp, #-0x10]
    // 0xbf931c: ldur            x2, [fp, #-8]
    // 0xbf9320: LoadField: r0 = r2->field_f
    //     0xbf9320: ldur            w0, [x2, #0xf]
    // 0xbf9324: DecompressPointer r0
    //     0xbf9324: add             x0, x0, HEAP, lsl #32
    // 0xbf9328: LoadField: r2 = r0->field_b
    //     0xbf9328: ldur            w2, [x0, #0xb]
    // 0xbf932c: DecompressPointer r2
    //     0xbf932c: add             x2, x2, HEAP, lsl #32
    // 0xbf9330: cmp             w2, NULL
    // 0xbf9334: b.eq            #0xbf96dc
    // 0xbf9338: LoadField: r4 = r2->field_1f
    //     0xbf9338: ldur            w4, [x2, #0x1f]
    // 0xbf933c: DecompressPointer r4
    //     0xbf933c: add             x4, x4, HEAP, lsl #32
    // 0xbf9340: LoadField: r5 = r3->field_f
    //     0xbf9340: ldur            w5, [x3, #0xf]
    // 0xbf9344: DecompressPointer r5
    //     0xbf9344: add             x5, x5, HEAP, lsl #32
    // 0xbf9348: LoadField: r0 = r2->field_b
    //     0xbf9348: ldur            w0, [x2, #0xb]
    // 0xbf934c: DecompressPointer r0
    //     0xbf934c: add             x0, x0, HEAP, lsl #32
    // 0xbf9350: LoadField: r6 = r0->field_63
    //     0xbf9350: ldur            w6, [x0, #0x63]
    // 0xbf9354: DecompressPointer r6
    //     0xbf9354: add             x6, x6, HEAP, lsl #32
    // 0xbf9358: LoadField: r7 = r0->field_67
    //     0xbf9358: ldur            w7, [x0, #0x67]
    // 0xbf935c: DecompressPointer r7
    //     0xbf935c: add             x7, x7, HEAP, lsl #32
    // 0xbf9360: LoadField: r8 = r0->field_73
    //     0xbf9360: ldur            w8, [x0, #0x73]
    // 0xbf9364: DecompressPointer r8
    //     0xbf9364: add             x8, x8, HEAP, lsl #32
    // 0xbf9368: LoadField: r9 = r0->field_6f
    //     0xbf9368: ldur            w9, [x0, #0x6f]
    // 0xbf936c: DecompressPointer r9
    //     0xbf936c: add             x9, x9, HEAP, lsl #32
    // 0xbf9370: LoadField: r10 = r0->field_6b
    //     0xbf9370: ldur            w10, [x0, #0x6b]
    // 0xbf9374: DecompressPointer r10
    //     0xbf9374: add             x10, x10, HEAP, lsl #32
    // 0xbf9378: LoadField: r11 = r0->field_77
    //     0xbf9378: ldur            w11, [x0, #0x77]
    // 0xbf937c: DecompressPointer r11
    //     0xbf937c: add             x11, x11, HEAP, lsl #32
    // 0xbf9380: LoadField: r12 = r0->field_57
    //     0xbf9380: ldur            w12, [x0, #0x57]
    // 0xbf9384: DecompressPointer r12
    //     0xbf9384: add             x12, x12, HEAP, lsl #32
    // 0xbf9388: cmp             w12, NULL
    // 0xbf938c: b.ne            #0xbf9398
    // 0xbf9390: r0 = Null
    //     0xbf9390: mov             x0, NULL
    // 0xbf9394: b               #0xbf93ec
    // 0xbf9398: LoadField: r0 = r3->field_13
    //     0xbf9398: ldur            w0, [x3, #0x13]
    // 0xbf939c: DecompressPointer r0
    //     0xbf939c: add             x0, x0, HEAP, lsl #32
    // 0xbf93a0: LoadField: r1 = r12->field_b
    //     0xbf93a0: ldur            w1, [x12, #0xb]
    // 0xbf93a4: r3 = LoadInt32Instr(r0)
    //     0xbf93a4: sbfx            x3, x0, #1, #0x1f
    //     0xbf93a8: tbz             w0, #0, #0xbf93b0
    //     0xbf93ac: ldur            x3, [x0, #7]
    // 0xbf93b0: r0 = LoadInt32Instr(r1)
    //     0xbf93b0: sbfx            x0, x1, #1, #0x1f
    // 0xbf93b4: mov             x1, x3
    // 0xbf93b8: cmp             x1, x0
    // 0xbf93bc: b.hs            #0xbf96e0
    // 0xbf93c0: LoadField: r0 = r12->field_f
    //     0xbf93c0: ldur            w0, [x12, #0xf]
    // 0xbf93c4: DecompressPointer r0
    //     0xbf93c4: add             x0, x0, HEAP, lsl #32
    // 0xbf93c8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbf93c8: add             x16, x0, x3, lsl #2
    //     0xbf93cc: ldur            w1, [x16, #0xf]
    // 0xbf93d0: DecompressPointer r1
    //     0xbf93d0: add             x1, x1, HEAP, lsl #32
    // 0xbf93d4: cmp             w1, NULL
    // 0xbf93d8: b.ne            #0xbf93e4
    // 0xbf93dc: r0 = Null
    //     0xbf93dc: mov             x0, NULL
    // 0xbf93e0: b               #0xbf93ec
    // 0xbf93e4: LoadField: r0 = r1->field_b
    //     0xbf93e4: ldur            w0, [x1, #0xb]
    // 0xbf93e8: DecompressPointer r0
    //     0xbf93e8: add             x0, x0, HEAP, lsl #32
    // 0xbf93ec: cmp             w0, NULL
    // 0xbf93f0: b.ne            #0xbf93f8
    // 0xbf93f4: r0 = ""
    //     0xbf93f4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf93f8: LoadField: r1 = r2->field_b
    //     0xbf93f8: ldur            w1, [x2, #0xb]
    // 0xbf93fc: DecompressPointer r1
    //     0xbf93fc: add             x1, x1, HEAP, lsl #32
    // 0xbf9400: LoadField: r2 = r1->field_7
    //     0xbf9400: ldur            w2, [x1, #7]
    // 0xbf9404: DecompressPointer r2
    //     0xbf9404: add             x2, x2, HEAP, lsl #32
    // 0xbf9408: stp             x5, x4, [SP, #0x40]
    // 0xbf940c: stp             x7, x6, [SP, #0x30]
    // 0xbf9410: stp             x9, x8, [SP, #0x20]
    // 0xbf9414: stp             x11, x10, [SP, #0x10]
    // 0xbf9418: stp             x2, x0, [SP]
    // 0xbf941c: r4 = 0
    //     0xbf941c: movz            x4, #0
    // 0xbf9420: ldr             x0, [SP, #0x48]
    // 0xbf9424: r16 = UnlinkedCall_0x613b5c
    //     0xbf9424: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a270] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf9428: add             x16, x16, #0x270
    // 0xbf942c: ldp             x5, lr, [x16]
    // 0xbf9430: blr             lr
    // 0xbf9434: b               #0xbf96b0
    // 0xbf9438: ldur            x3, [fp, #-0x10]
    // 0xbf943c: ldur            x2, [fp, #-8]
    // 0xbf9440: LoadField: r0 = r2->field_f
    //     0xbf9440: ldur            w0, [x2, #0xf]
    // 0xbf9444: DecompressPointer r0
    //     0xbf9444: add             x0, x0, HEAP, lsl #32
    // 0xbf9448: LoadField: r1 = r0->field_b
    //     0xbf9448: ldur            w1, [x0, #0xb]
    // 0xbf944c: DecompressPointer r1
    //     0xbf944c: add             x1, x1, HEAP, lsl #32
    // 0xbf9450: cmp             w1, NULL
    // 0xbf9454: b.eq            #0xbf96e4
    // 0xbf9458: LoadField: r0 = r1->field_b
    //     0xbf9458: ldur            w0, [x1, #0xb]
    // 0xbf945c: DecompressPointer r0
    //     0xbf945c: add             x0, x0, HEAP, lsl #32
    // 0xbf9460: LoadField: r4 = r0->field_57
    //     0xbf9460: ldur            w4, [x0, #0x57]
    // 0xbf9464: DecompressPointer r4
    //     0xbf9464: add             x4, x4, HEAP, lsl #32
    // 0xbf9468: cmp             w4, NULL
    // 0xbf946c: b.ne            #0xbf9478
    // 0xbf9470: r0 = Null
    //     0xbf9470: mov             x0, NULL
    // 0xbf9474: b               #0xbf94cc
    // 0xbf9478: LoadField: r0 = r3->field_13
    //     0xbf9478: ldur            w0, [x3, #0x13]
    // 0xbf947c: DecompressPointer r0
    //     0xbf947c: add             x0, x0, HEAP, lsl #32
    // 0xbf9480: LoadField: r1 = r4->field_b
    //     0xbf9480: ldur            w1, [x4, #0xb]
    // 0xbf9484: r5 = LoadInt32Instr(r0)
    //     0xbf9484: sbfx            x5, x0, #1, #0x1f
    //     0xbf9488: tbz             w0, #0, #0xbf9490
    //     0xbf948c: ldur            x5, [x0, #7]
    // 0xbf9490: r0 = LoadInt32Instr(r1)
    //     0xbf9490: sbfx            x0, x1, #1, #0x1f
    // 0xbf9494: mov             x1, x5
    // 0xbf9498: cmp             x1, x0
    // 0xbf949c: b.hs            #0xbf96e8
    // 0xbf94a0: LoadField: r0 = r4->field_f
    //     0xbf94a0: ldur            w0, [x4, #0xf]
    // 0xbf94a4: DecompressPointer r0
    //     0xbf94a4: add             x0, x0, HEAP, lsl #32
    // 0xbf94a8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbf94a8: add             x16, x0, x5, lsl #2
    //     0xbf94ac: ldur            w1, [x16, #0xf]
    // 0xbf94b0: DecompressPointer r1
    //     0xbf94b0: add             x1, x1, HEAP, lsl #32
    // 0xbf94b4: cmp             w1, NULL
    // 0xbf94b8: b.ne            #0xbf94c4
    // 0xbf94bc: r0 = Null
    //     0xbf94bc: mov             x0, NULL
    // 0xbf94c0: b               #0xbf94cc
    // 0xbf94c4: LoadField: r0 = r1->field_7
    //     0xbf94c4: ldur            w0, [x1, #7]
    // 0xbf94c8: DecompressPointer r0
    //     0xbf94c8: add             x0, x0, HEAP, lsl #32
    // 0xbf94cc: r1 = LoadClassIdInstr(r0)
    //     0xbf94cc: ldur            x1, [x0, #-1]
    //     0xbf94d0: ubfx            x1, x1, #0xc, #0x14
    // 0xbf94d4: r16 = "cancel_return"
    //     0xbf94d4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df0] "cancel_return"
    //     0xbf94d8: ldr             x16, [x16, #0xdf0]
    // 0xbf94dc: stp             x16, x0, [SP]
    // 0xbf94e0: mov             x0, x1
    // 0xbf94e4: mov             lr, x0
    // 0xbf94e8: ldr             lr, [x21, lr, lsl #3]
    // 0xbf94ec: blr             lr
    // 0xbf94f0: tbnz            w0, #4, #0xbf9560
    // 0xbf94f4: ldur            x0, [fp, #-0x10]
    // 0xbf94f8: ldur            x2, [fp, #-8]
    // 0xbf94fc: LoadField: r1 = r2->field_f
    //     0xbf94fc: ldur            w1, [x2, #0xf]
    // 0xbf9500: DecompressPointer r1
    //     0xbf9500: add             x1, x1, HEAP, lsl #32
    // 0xbf9504: LoadField: r2 = r1->field_b
    //     0xbf9504: ldur            w2, [x1, #0xb]
    // 0xbf9508: DecompressPointer r2
    //     0xbf9508: add             x2, x2, HEAP, lsl #32
    // 0xbf950c: cmp             w2, NULL
    // 0xbf9510: b.eq            #0xbf96ec
    // 0xbf9514: LoadField: r1 = r2->field_1b
    //     0xbf9514: ldur            w1, [x2, #0x1b]
    // 0xbf9518: DecompressPointer r1
    //     0xbf9518: add             x1, x1, HEAP, lsl #32
    // 0xbf951c: LoadField: r3 = r0->field_f
    //     0xbf951c: ldur            w3, [x0, #0xf]
    // 0xbf9520: DecompressPointer r3
    //     0xbf9520: add             x3, x3, HEAP, lsl #32
    // 0xbf9524: LoadField: r0 = r2->field_b
    //     0xbf9524: ldur            w0, [x2, #0xb]
    // 0xbf9528: DecompressPointer r0
    //     0xbf9528: add             x0, x0, HEAP, lsl #32
    // 0xbf952c: LoadField: r2 = r0->field_5f
    //     0xbf952c: ldur            w2, [x0, #0x5f]
    // 0xbf9530: DecompressPointer r2
    //     0xbf9530: add             x2, x2, HEAP, lsl #32
    // 0xbf9534: LoadField: r4 = r0->field_67
    //     0xbf9534: ldur            w4, [x0, #0x67]
    // 0xbf9538: DecompressPointer r4
    //     0xbf9538: add             x4, x4, HEAP, lsl #32
    // 0xbf953c: stp             x3, x1, [SP, #0x10]
    // 0xbf9540: stp             x4, x2, [SP]
    // 0xbf9544: r4 = 0
    //     0xbf9544: movz            x4, #0
    // 0xbf9548: ldr             x0, [SP, #0x18]
    // 0xbf954c: r16 = UnlinkedCall_0x613b5c
    //     0xbf954c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a280] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf9550: add             x16, x16, #0x280
    // 0xbf9554: ldp             x5, lr, [x16]
    // 0xbf9558: blr             lr
    // 0xbf955c: b               #0xbf96b0
    // 0xbf9560: ldur            x0, [fp, #-0x10]
    // 0xbf9564: ldur            x2, [fp, #-8]
    // 0xbf9568: LoadField: r1 = r2->field_f
    //     0xbf9568: ldur            w1, [x2, #0xf]
    // 0xbf956c: DecompressPointer r1
    //     0xbf956c: add             x1, x1, HEAP, lsl #32
    // 0xbf9570: LoadField: r3 = r1->field_b
    //     0xbf9570: ldur            w3, [x1, #0xb]
    // 0xbf9574: DecompressPointer r3
    //     0xbf9574: add             x3, x3, HEAP, lsl #32
    // 0xbf9578: cmp             w3, NULL
    // 0xbf957c: b.eq            #0xbf96f0
    // 0xbf9580: LoadField: r1 = r3->field_b
    //     0xbf9580: ldur            w1, [x3, #0xb]
    // 0xbf9584: DecompressPointer r1
    //     0xbf9584: add             x1, x1, HEAP, lsl #32
    // 0xbf9588: LoadField: r3 = r1->field_57
    //     0xbf9588: ldur            w3, [x1, #0x57]
    // 0xbf958c: DecompressPointer r3
    //     0xbf958c: add             x3, x3, HEAP, lsl #32
    // 0xbf9590: cmp             w3, NULL
    // 0xbf9594: b.ne            #0xbf95a0
    // 0xbf9598: r0 = Null
    //     0xbf9598: mov             x0, NULL
    // 0xbf959c: b               #0xbf95f8
    // 0xbf95a0: LoadField: r1 = r0->field_13
    //     0xbf95a0: ldur            w1, [x0, #0x13]
    // 0xbf95a4: DecompressPointer r1
    //     0xbf95a4: add             x1, x1, HEAP, lsl #32
    // 0xbf95a8: LoadField: r0 = r3->field_b
    //     0xbf95a8: ldur            w0, [x3, #0xb]
    // 0xbf95ac: r4 = LoadInt32Instr(r1)
    //     0xbf95ac: sbfx            x4, x1, #1, #0x1f
    //     0xbf95b0: tbz             w1, #0, #0xbf95b8
    //     0xbf95b4: ldur            x4, [x1, #7]
    // 0xbf95b8: r1 = LoadInt32Instr(r0)
    //     0xbf95b8: sbfx            x1, x0, #1, #0x1f
    // 0xbf95bc: mov             x0, x1
    // 0xbf95c0: mov             x1, x4
    // 0xbf95c4: cmp             x1, x0
    // 0xbf95c8: b.hs            #0xbf96f4
    // 0xbf95cc: LoadField: r0 = r3->field_f
    //     0xbf95cc: ldur            w0, [x3, #0xf]
    // 0xbf95d0: DecompressPointer r0
    //     0xbf95d0: add             x0, x0, HEAP, lsl #32
    // 0xbf95d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbf95d4: add             x16, x0, x4, lsl #2
    //     0xbf95d8: ldur            w1, [x16, #0xf]
    // 0xbf95dc: DecompressPointer r1
    //     0xbf95dc: add             x1, x1, HEAP, lsl #32
    // 0xbf95e0: cmp             w1, NULL
    // 0xbf95e4: b.ne            #0xbf95f0
    // 0xbf95e8: r0 = Null
    //     0xbf95e8: mov             x0, NULL
    // 0xbf95ec: b               #0xbf95f8
    // 0xbf95f0: LoadField: r0 = r1->field_7
    //     0xbf95f0: ldur            w0, [x1, #7]
    // 0xbf95f4: DecompressPointer r0
    //     0xbf95f4: add             x0, x0, HEAP, lsl #32
    // 0xbf95f8: r1 = LoadClassIdInstr(r0)
    //     0xbf95f8: ldur            x1, [x0, #-1]
    //     0xbf95fc: ubfx            x1, x1, #0xc, #0x14
    // 0xbf9600: r16 = "track_order"
    //     0xbf9600: add             x16, PP, #0x36, lsl #12  ; [pp+0x36080] "track_order"
    //     0xbf9604: ldr             x16, [x16, #0x80]
    // 0xbf9608: stp             x16, x0, [SP]
    // 0xbf960c: mov             x0, x1
    // 0xbf9610: mov             lr, x0
    // 0xbf9614: ldr             lr, [x21, lr, lsl #3]
    // 0xbf9618: blr             lr
    // 0xbf961c: tbnz            w0, #4, #0xbf9688
    // 0xbf9620: ldur            x0, [fp, #-8]
    // 0xbf9624: LoadField: r1 = r0->field_f
    //     0xbf9624: ldur            w1, [x0, #0xf]
    // 0xbf9628: DecompressPointer r1
    //     0xbf9628: add             x1, x1, HEAP, lsl #32
    // 0xbf962c: LoadField: r0 = r1->field_b
    //     0xbf962c: ldur            w0, [x1, #0xb]
    // 0xbf9630: DecompressPointer r0
    //     0xbf9630: add             x0, x0, HEAP, lsl #32
    // 0xbf9634: cmp             w0, NULL
    // 0xbf9638: b.eq            #0xbf96f8
    // 0xbf963c: LoadField: r1 = r0->field_b
    //     0xbf963c: ldur            w1, [x0, #0xb]
    // 0xbf9640: DecompressPointer r1
    //     0xbf9640: add             x1, x1, HEAP, lsl #32
    // 0xbf9644: LoadField: r2 = r1->field_33
    //     0xbf9644: ldur            w2, [x1, #0x33]
    // 0xbf9648: DecompressPointer r2
    //     0xbf9648: add             x2, x2, HEAP, lsl #32
    // 0xbf964c: cmp             w2, NULL
    // 0xbf9650: b.eq            #0xbf96b0
    // 0xbf9654: LoadField: r1 = r0->field_b
    //     0xbf9654: ldur            w1, [x0, #0xb]
    // 0xbf9658: DecompressPointer r1
    //     0xbf9658: add             x1, x1, HEAP, lsl #32
    // 0xbf965c: LoadField: r0 = r1->field_33
    //     0xbf965c: ldur            w0, [x1, #0x33]
    // 0xbf9660: DecompressPointer r0
    //     0xbf9660: add             x0, x0, HEAP, lsl #32
    // 0xbf9664: cmp             w0, NULL
    // 0xbf9668: b.eq            #0xbf96fc
    // 0xbf966c: mov             x1, x0
    // 0xbf9670: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbf9670: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbf9674: r0 = parse()
    //     0xbf9674: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0xbf9678: mov             x1, x0
    // 0xbf967c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbf967c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbf9680: r0 = launchUrl()
    //     0xbf9680: bl              #0x85cb94  ; [package:url_launcher/src/url_launcher_uri.dart] ::launchUrl
    // 0xbf9684: b               #0xbf96b0
    // 0xbf9688: ldur            x0, [fp, #-8]
    // 0xbf968c: LoadField: r1 = r0->field_f
    //     0xbf968c: ldur            w1, [x0, #0xf]
    // 0xbf9690: DecompressPointer r1
    //     0xbf9690: add             x1, x1, HEAP, lsl #32
    // 0xbf9694: LoadField: r0 = r1->field_b
    //     0xbf9694: ldur            w0, [x1, #0xb]
    // 0xbf9698: DecompressPointer r0
    //     0xbf9698: add             x0, x0, HEAP, lsl #32
    // 0xbf969c: cmp             w0, NULL
    // 0xbf96a0: b.eq            #0xbf9700
    // 0xbf96a4: LoadField: r2 = r0->field_b
    //     0xbf96a4: ldur            w2, [x0, #0xb]
    // 0xbf96a8: DecompressPointer r2
    //     0xbf96a8: add             x2, x2, HEAP, lsl #32
    // 0xbf96ac: r0 = findProductIds()
    //     0xbf96ac: bl              #0xbf9704  ; [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::findProductIds
    // 0xbf96b0: r0 = Null
    //     0xbf96b0: mov             x0, NULL
    // 0xbf96b4: LeaveFrame
    //     0xbf96b4: mov             SP, fp
    //     0xbf96b8: ldp             fp, lr, [SP], #0x10
    // 0xbf96bc: ret
    //     0xbf96bc: ret             
    // 0xbf96c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf96c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf96c4: b               #0xbf90a0
    // 0xbf96c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf96cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf96d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf96d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf96dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf96e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf96e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf96e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf96ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf96f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf96f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf96fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf96fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9700: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9700: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ findProductIds(/* No info */) {
    // ** addr: 0xbf9704, size: 0x134
    // 0xbf9704: EnterFrame
    //     0xbf9704: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9708: mov             fp, SP
    // 0xbf970c: AllocStack(0x40)
    //     0xbf970c: sub             SP, SP, #0x40
    // 0xbf9710: SetupParameters(_OrderCardState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xbf9710: mov             x4, x1
    //     0xbf9714: mov             x3, x2
    //     0xbf9718: stur            x1, [fp, #-8]
    //     0xbf971c: stur            x2, [fp, #-0x10]
    // 0xbf9720: CheckStackOverflow
    //     0xbf9720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9724: cmp             SP, x16
    //     0xbf9728: b.ls            #0xbf9824
    // 0xbf972c: LoadField: r1 = r3->field_2b
    //     0xbf972c: ldur            w1, [x3, #0x2b]
    // 0xbf9730: DecompressPointer r1
    //     0xbf9730: add             x1, x1, HEAP, lsl #32
    // 0xbf9734: cmp             w1, NULL
    // 0xbf9738: b.ne            #0xbf9744
    // 0xbf973c: r2 = Null
    //     0xbf973c: mov             x2, NULL
    // 0xbf9740: b               #0xbf9760
    // 0xbf9744: r0 = LoadClassIdInstr(r1)
    //     0xbf9744: ldur            x0, [x1, #-1]
    //     0xbf9748: ubfx            x0, x0, #0xc, #0x14
    // 0xbf974c: r2 = "/"
    //     0xbf974c: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0xbf9750: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbf9750: sub             lr, x0, #0xffc
    //     0xbf9754: ldr             lr, [x21, lr, lsl #3]
    //     0xbf9758: blr             lr
    // 0xbf975c: mov             x2, x0
    // 0xbf9760: cmp             w2, NULL
    // 0xbf9764: b.eq            #0xbf9814
    // 0xbf9768: ldur            x4, [fp, #-8]
    // 0xbf976c: ldur            x3, [fp, #-0x10]
    // 0xbf9770: LoadField: r0 = r2->field_b
    //     0xbf9770: ldur            w0, [x2, #0xb]
    // 0xbf9774: r5 = LoadInt32Instr(r0)
    //     0xbf9774: sbfx            x5, x0, #1, #0x1f
    // 0xbf9778: sub             x6, x5, #2
    // 0xbf977c: mov             x0, x5
    // 0xbf9780: mov             x1, x6
    // 0xbf9784: cmp             x1, x0
    // 0xbf9788: b.hs            #0xbf982c
    // 0xbf978c: LoadField: r7 = r2->field_f
    //     0xbf978c: ldur            w7, [x2, #0xf]
    // 0xbf9790: DecompressPointer r7
    //     0xbf9790: add             x7, x7, HEAP, lsl #32
    // 0xbf9794: ArrayLoad: r2 = r7[r6]  ; Unknown_4
    //     0xbf9794: add             x16, x7, x6, lsl #2
    //     0xbf9798: ldur            w2, [x16, #0xf]
    // 0xbf979c: DecompressPointer r2
    //     0xbf979c: add             x2, x2, HEAP, lsl #32
    // 0xbf97a0: sub             x6, x5, #1
    // 0xbf97a4: mov             x0, x5
    // 0xbf97a8: mov             x1, x6
    // 0xbf97ac: cmp             x1, x0
    // 0xbf97b0: b.hs            #0xbf9830
    // 0xbf97b4: ArrayLoad: r0 = r7[r6]  ; Unknown_4
    //     0xbf97b4: add             x16, x7, x6, lsl #2
    //     0xbf97b8: ldur            w0, [x16, #0xf]
    // 0xbf97bc: DecompressPointer r0
    //     0xbf97bc: add             x0, x0, HEAP, lsl #32
    // 0xbf97c0: LoadField: r1 = r4->field_b
    //     0xbf97c0: ldur            w1, [x4, #0xb]
    // 0xbf97c4: DecompressPointer r1
    //     0xbf97c4: add             x1, x1, HEAP, lsl #32
    // 0xbf97c8: cmp             w1, NULL
    // 0xbf97cc: b.eq            #0xbf9834
    // 0xbf97d0: LoadField: r4 = r1->field_27
    //     0xbf97d0: ldur            w4, [x1, #0x27]
    // 0xbf97d4: DecompressPointer r4
    //     0xbf97d4: add             x4, x4, HEAP, lsl #32
    // 0xbf97d8: LoadField: r1 = r3->field_6b
    //     0xbf97d8: ldur            w1, [x3, #0x6b]
    // 0xbf97dc: DecompressPointer r1
    //     0xbf97dc: add             x1, x1, HEAP, lsl #32
    // 0xbf97e0: LoadField: r5 = r3->field_6f
    //     0xbf97e0: ldur            w5, [x3, #0x6f]
    // 0xbf97e4: DecompressPointer r5
    //     0xbf97e4: add             x5, x5, HEAP, lsl #32
    // 0xbf97e8: LoadField: r6 = r3->field_7
    //     0xbf97e8: ldur            w6, [x3, #7]
    // 0xbf97ec: DecompressPointer r6
    //     0xbf97ec: add             x6, x6, HEAP, lsl #32
    // 0xbf97f0: stp             x2, x4, [SP, #0x20]
    // 0xbf97f4: stp             x1, x0, [SP, #0x10]
    // 0xbf97f8: stp             x6, x5, [SP]
    // 0xbf97fc: r4 = 0
    //     0xbf97fc: movz            x4, #0
    // 0xbf9800: ldr             x0, [SP, #0x28]
    // 0xbf9804: r16 = UnlinkedCall_0x613b5c
    //     0xbf9804: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a328] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf9808: add             x16, x16, #0x328
    // 0xbf980c: ldp             x5, lr, [x16]
    // 0xbf9810: blr             lr
    // 0xbf9814: r0 = Null
    //     0xbf9814: mov             x0, NULL
    // 0xbf9818: LeaveFrame
    //     0xbf9818: mov             SP, fp
    //     0xbf981c: ldp             fp, lr, [SP], #0x10
    // 0xbf9820: ret
    //     0xbf9820: ret             
    // 0xbf9824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9824: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9828: b               #0xbf972c
    // 0xbf982c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf982c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf9830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf9830: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf9834: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9834: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf9838, size: 0xe0
    // 0xbf9838: EnterFrame
    //     0xbf9838: stp             fp, lr, [SP, #-0x10]!
    //     0xbf983c: mov             fp, SP
    // 0xbf9840: AllocStack(0x20)
    //     0xbf9840: sub             SP, SP, #0x20
    // 0xbf9844: SetupParameters()
    //     0xbf9844: ldr             x0, [fp, #0x10]
    //     0xbf9848: ldur            w1, [x0, #0x17]
    //     0xbf984c: add             x1, x1, HEAP, lsl #32
    // 0xbf9850: CheckStackOverflow
    //     0xbf9850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9854: cmp             SP, x16
    //     0xbf9858: b.ls            #0xbf98e8
    // 0xbf985c: LoadField: r0 = r1->field_f
    //     0xbf985c: ldur            w0, [x1, #0xf]
    // 0xbf9860: DecompressPointer r0
    //     0xbf9860: add             x0, x0, HEAP, lsl #32
    // 0xbf9864: LoadField: r1 = r0->field_b
    //     0xbf9864: ldur            w1, [x0, #0xb]
    // 0xbf9868: DecompressPointer r1
    //     0xbf9868: add             x1, x1, HEAP, lsl #32
    // 0xbf986c: cmp             w1, NULL
    // 0xbf9870: b.eq            #0xbf98f0
    // 0xbf9874: LoadField: d0 = r0->field_13
    //     0xbf9874: ldur            d0, [x0, #0x13]
    // 0xbf9878: LoadField: r0 = r1->field_b
    //     0xbf9878: ldur            w0, [x1, #0xb]
    // 0xbf987c: DecompressPointer r0
    //     0xbf987c: add             x0, x0, HEAP, lsl #32
    // 0xbf9880: LoadField: r2 = r0->field_8f
    //     0xbf9880: ldur            w2, [x0, #0x8f]
    // 0xbf9884: DecompressPointer r2
    //     0xbf9884: add             x2, x2, HEAP, lsl #32
    // 0xbf9888: LoadField: r3 = r1->field_2b
    //     0xbf9888: ldur            w3, [x1, #0x2b]
    // 0xbf988c: DecompressPointer r3
    //     0xbf988c: add             x3, x3, HEAP, lsl #32
    // 0xbf9890: r1 = inline_Allocate_Double()
    //     0xbf9890: ldp             x1, x4, [THR, #0x50]  ; THR::top
    //     0xbf9894: add             x1, x1, #0x10
    //     0xbf9898: cmp             x4, x1
    //     0xbf989c: b.ls            #0xbf98f4
    //     0xbf98a0: str             x1, [THR, #0x50]  ; THR::top
    //     0xbf98a4: sub             x1, x1, #0xf
    //     0xbf98a8: movz            x4, #0xe15c
    //     0xbf98ac: movk            x4, #0x3, lsl #16
    //     0xbf98b0: stur            x4, [x1, #-1]
    // 0xbf98b4: StoreField: r1->field_7 = d0
    //     0xbf98b4: stur            d0, [x1, #7]
    // 0xbf98b8: stp             x1, x3, [SP, #0x10]
    // 0xbf98bc: stp             x2, x0, [SP]
    // 0xbf98c0: r4 = 0
    //     0xbf98c0: movz            x4, #0
    // 0xbf98c4: ldr             x0, [SP, #0x18]
    // 0xbf98c8: r16 = UnlinkedCall_0x613b5c
    //     0xbf98c8: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a340] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf98cc: add             x16, x16, #0x340
    // 0xbf98d0: ldp             x5, lr, [x16]
    // 0xbf98d4: blr             lr
    // 0xbf98d8: r0 = Null
    //     0xbf98d8: mov             x0, NULL
    // 0xbf98dc: LeaveFrame
    //     0xbf98dc: mov             SP, fp
    //     0xbf98e0: ldp             fp, lr, [SP], #0x10
    // 0xbf98e4: ret
    //     0xbf98e4: ret             
    // 0xbf98e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf98e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf98ec: b               #0xbf985c
    // 0xbf98f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf98f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf98f4: SaveReg d0
    //     0xbf98f4: str             q0, [SP, #-0x10]!
    // 0xbf98f8: stp             x2, x3, [SP, #-0x10]!
    // 0xbf98fc: SaveReg r0
    //     0xbf98fc: str             x0, [SP, #-8]!
    // 0xbf9900: r0 = AllocateDouble()
    //     0xbf9900: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf9904: mov             x1, x0
    // 0xbf9908: RestoreReg r0
    //     0xbf9908: ldr             x0, [SP], #8
    // 0xbf990c: ldp             x2, x3, [SP], #0x10
    // 0xbf9910: RestoreReg d0
    //     0xbf9910: ldr             q0, [SP], #0x10
    // 0xbf9914: b               #0xbf98b4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf9918, size: 0x154
    // 0xbf9918: EnterFrame
    //     0xbf9918: stp             fp, lr, [SP, #-0x10]!
    //     0xbf991c: mov             fp, SP
    // 0xbf9920: AllocStack(0x38)
    //     0xbf9920: sub             SP, SP, #0x38
    // 0xbf9924: SetupParameters()
    //     0xbf9924: ldr             x0, [fp, #0x10]
    //     0xbf9928: ldur            w2, [x0, #0x17]
    //     0xbf992c: add             x2, x2, HEAP, lsl #32
    //     0xbf9930: stur            x2, [fp, #-8]
    // 0xbf9934: CheckStackOverflow
    //     0xbf9934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9938: cmp             SP, x16
    //     0xbf993c: b.ls            #0xbf9a50
    // 0xbf9940: LoadField: r0 = r2->field_f
    //     0xbf9940: ldur            w0, [x2, #0xf]
    // 0xbf9944: DecompressPointer r0
    //     0xbf9944: add             x0, x0, HEAP, lsl #32
    // 0xbf9948: LoadField: r1 = r0->field_b
    //     0xbf9948: ldur            w1, [x0, #0xb]
    // 0xbf994c: DecompressPointer r1
    //     0xbf994c: add             x1, x1, HEAP, lsl #32
    // 0xbf9950: cmp             w1, NULL
    // 0xbf9954: b.eq            #0xbf9a58
    // 0xbf9958: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbf9958: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf995c: ldr             x0, [x0, #0x1c80]
    //     0xbf9960: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbf9964: cmp             w0, w16
    //     0xbf9968: b.ne            #0xbf9974
    //     0xbf996c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbf9970: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbf9974: ldur            x2, [fp, #-8]
    // 0xbf9978: LoadField: r0 = r2->field_f
    //     0xbf9978: ldur            w0, [x2, #0xf]
    // 0xbf997c: DecompressPointer r0
    //     0xbf997c: add             x0, x0, HEAP, lsl #32
    // 0xbf9980: LoadField: r1 = r0->field_b
    //     0xbf9980: ldur            w1, [x0, #0xb]
    // 0xbf9984: DecompressPointer r1
    //     0xbf9984: add             x1, x1, HEAP, lsl #32
    // 0xbf9988: cmp             w1, NULL
    // 0xbf998c: b.eq            #0xbf9a5c
    // 0xbf9990: LoadField: r0 = r1->field_b
    //     0xbf9990: ldur            w0, [x1, #0xb]
    // 0xbf9994: DecompressPointer r0
    //     0xbf9994: add             x0, x0, HEAP, lsl #32
    // 0xbf9998: LoadField: r1 = r0->field_7
    //     0xbf9998: ldur            w1, [x0, #7]
    // 0xbf999c: DecompressPointer r1
    //     0xbf999c: add             x1, x1, HEAP, lsl #32
    // 0xbf99a0: stur            x1, [fp, #-0x20]
    // 0xbf99a4: cmp             w1, NULL
    // 0xbf99a8: b.eq            #0xbf9a60
    // 0xbf99ac: LoadField: r3 = r0->field_f
    //     0xbf99ac: ldur            w3, [x0, #0xf]
    // 0xbf99b0: DecompressPointer r3
    //     0xbf99b0: add             x3, x3, HEAP, lsl #32
    // 0xbf99b4: stur            x3, [fp, #-0x18]
    // 0xbf99b8: cmp             w3, NULL
    // 0xbf99bc: b.eq            #0xbf9a64
    // 0xbf99c0: LoadField: r4 = r0->field_23
    //     0xbf99c0: ldur            w4, [x0, #0x23]
    // 0xbf99c4: DecompressPointer r4
    //     0xbf99c4: add             x4, x4, HEAP, lsl #32
    // 0xbf99c8: stur            x4, [fp, #-0x10]
    // 0xbf99cc: cmp             w4, NULL
    // 0xbf99d0: b.eq            #0xbf9a68
    // 0xbf99d4: r0 = OrderItemModel()
    //     0xbf99d4: bl              #0x925de8  ; AllocateOrderItemModelStub -> OrderItemModel (size=0x14)
    // 0xbf99d8: mov             x1, x0
    // 0xbf99dc: ldur            x0, [fp, #-0x20]
    // 0xbf99e0: StoreField: r1->field_7 = r0
    //     0xbf99e0: stur            w0, [x1, #7]
    // 0xbf99e4: ldur            x0, [fp, #-0x18]
    // 0xbf99e8: StoreField: r1->field_b = r0
    //     0xbf99e8: stur            w0, [x1, #0xb]
    // 0xbf99ec: ldur            x0, [fp, #-0x10]
    // 0xbf99f0: StoreField: r1->field_f = r0
    //     0xbf99f0: stur            w0, [x1, #0xf]
    // 0xbf99f4: r16 = "/order"
    //     0xbf99f4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb430] "/order"
    //     0xbf99f8: ldr             x16, [x16, #0x430]
    // 0xbf99fc: stp             x16, NULL, [SP, #8]
    // 0xbf9a00: str             x1, [SP]
    // 0xbf9a04: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbf9a04: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbf9a08: ldr             x4, [x4, #0x438]
    // 0xbf9a0c: r0 = GetNavigation.toNamed()
    //     0xbf9a0c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbf9a10: stur            x0, [fp, #-0x10]
    // 0xbf9a14: cmp             w0, NULL
    // 0xbf9a18: b.eq            #0xbf9a40
    // 0xbf9a1c: ldur            x2, [fp, #-8]
    // 0xbf9a20: r1 = Function '<anonymous closure>':.
    //     0xbf9a20: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a368] AnonymousClosure: (0xbf9a6c), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xbf9a24: ldr             x1, [x1, #0x368]
    // 0xbf9a28: r0 = AllocateClosure()
    //     0xbf9a28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf9a2c: ldur            x16, [fp, #-0x10]
    // 0xbf9a30: stp             x16, NULL, [SP, #8]
    // 0xbf9a34: str             x0, [SP]
    // 0xbf9a38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf9a38: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf9a3c: r0 = then()
    //     0xbf9a3c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xbf9a40: r0 = Null
    //     0xbf9a40: mov             x0, NULL
    // 0xbf9a44: LeaveFrame
    //     0xbf9a44: mov             SP, fp
    //     0xbf9a48: ldp             fp, lr, [SP], #0x10
    // 0xbf9a4c: ret
    //     0xbf9a4c: ret             
    // 0xbf9a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9a50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9a54: b               #0xbf9940
    // 0xbf9a58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9a58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9a5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9a5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9a60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9a60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9a64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9a64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf9a68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9a68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xbf9a6c, size: 0x7c
    // 0xbf9a6c: EnterFrame
    //     0xbf9a6c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9a70: mov             fp, SP
    // 0xbf9a74: AllocStack(0x8)
    //     0xbf9a74: sub             SP, SP, #8
    // 0xbf9a78: SetupParameters()
    //     0xbf9a78: ldr             x0, [fp, #0x18]
    //     0xbf9a7c: ldur            w1, [x0, #0x17]
    //     0xbf9a80: add             x1, x1, HEAP, lsl #32
    // 0xbf9a84: CheckStackOverflow
    //     0xbf9a84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9a88: cmp             SP, x16
    //     0xbf9a8c: b.ls            #0xbf9adc
    // 0xbf9a90: LoadField: r0 = r1->field_f
    //     0xbf9a90: ldur            w0, [x1, #0xf]
    // 0xbf9a94: DecompressPointer r0
    //     0xbf9a94: add             x0, x0, HEAP, lsl #32
    // 0xbf9a98: LoadField: r1 = r0->field_b
    //     0xbf9a98: ldur            w1, [x0, #0xb]
    // 0xbf9a9c: DecompressPointer r1
    //     0xbf9a9c: add             x1, x1, HEAP, lsl #32
    // 0xbf9aa0: cmp             w1, NULL
    // 0xbf9aa4: b.eq            #0xbf9ae4
    // 0xbf9aa8: LoadField: r0 = r1->field_23
    //     0xbf9aa8: ldur            w0, [x1, #0x23]
    // 0xbf9aac: DecompressPointer r0
    //     0xbf9aac: add             x0, x0, HEAP, lsl #32
    // 0xbf9ab0: str             x0, [SP]
    // 0xbf9ab4: r4 = 0
    //     0xbf9ab4: movz            x4, #0
    // 0xbf9ab8: ldr             x0, [SP]
    // 0xbf9abc: r16 = UnlinkedCall_0x613b5c
    //     0xbf9abc: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a370] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf9ac0: add             x16, x16, #0x370
    // 0xbf9ac4: ldp             x5, lr, [x16]
    // 0xbf9ac8: blr             lr
    // 0xbf9acc: r0 = Null
    //     0xbf9acc: mov             x0, NULL
    // 0xbf9ad0: LeaveFrame
    //     0xbf9ad0: mov             SP, fp
    //     0xbf9ad4: ldp             fp, lr, [SP], #0x10
    // 0xbf9ad8: ret
    //     0xbf9ad8: ret             
    // 0xbf9adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9adc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf9ae0: b               #0xbf9a90
    // 0xbf9ae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9ae4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3984, size: 0x30, field offset: 0xc
//   const constructor, 
class OrderCard extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80d44, size: 0x28
    // 0xc80d44: EnterFrame
    //     0xc80d44: stp             fp, lr, [SP, #-0x10]!
    //     0xc80d48: mov             fp, SP
    // 0xc80d4c: mov             x0, x1
    // 0xc80d50: r1 = <OrderCard>
    //     0xc80d50: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c20] TypeArguments: <OrderCard>
    //     0xc80d54: ldr             x1, [x1, #0xc20]
    // 0xc80d58: r0 = _OrderCardState()
    //     0xc80d58: bl              #0xc80d6c  ; Allocate_OrderCardStateStub -> _OrderCardState (size=0x1c)
    // 0xc80d5c: StoreField: r0->field_13 = rZR
    //     0xc80d5c: stur            xzr, [x0, #0x13]
    // 0xc80d60: LeaveFrame
    //     0xc80d60: mov             SP, fp
    //     0xc80d64: ldp             fp, lr, [SP], #0x10
    // 0xc80d68: ret
    //     0xc80d68: ret             
  }
}
