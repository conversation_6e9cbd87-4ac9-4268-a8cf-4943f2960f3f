// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/color_picker_detail.dart

// class id: 1049308, size: 0x8
class :: {
}

// class id: 4498, size: 0x18, field offset: 0xc
//   const constructor, 
class ColorPickerDetail extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x128f010, size: 0xf4
    // 0x128f010: EnterFrame
    //     0x128f010: stp             fp, lr, [SP, #-0x10]!
    //     0x128f014: mov             fp, SP
    // 0x128f018: AllocStack(0x30)
    //     0x128f018: sub             SP, SP, #0x30
    // 0x128f01c: SetupParameters(ColorPickerDetail this /* r1 => r1, fp-0x8 */)
    //     0x128f01c: stur            x1, [fp, #-8]
    // 0x128f020: CheckStackOverflow
    //     0x128f020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128f024: cmp             SP, x16
    //     0x128f028: b.ls            #0x128f0fc
    // 0x128f02c: r1 = 1
    //     0x128f02c: movz            x1, #0x1
    // 0x128f030: r0 = AllocateContext()
    //     0x128f030: bl              #0x16f6108  ; AllocateContextStub
    // 0x128f034: mov             x1, x0
    // 0x128f038: ldur            x0, [fp, #-8]
    // 0x128f03c: StoreField: r1->field_f = r0
    //     0x128f03c: stur            w0, [x1, #0xf]
    // 0x128f040: LoadField: r2 = r0->field_b
    //     0x128f040: ldur            w2, [x0, #0xb]
    // 0x128f044: DecompressPointer r2
    //     0x128f044: add             x2, x2, HEAP, lsl #32
    // 0x128f048: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x128f048: ldur            w0, [x2, #0x17]
    // 0x128f04c: DecompressPointer r0
    //     0x128f04c: add             x0, x0, HEAP, lsl #32
    // 0x128f050: cmp             w0, NULL
    // 0x128f054: b.eq            #0x128f064
    // 0x128f058: LoadField: r3 = r0->field_b
    //     0x128f058: ldur            w3, [x0, #0xb]
    // 0x128f05c: stur            x3, [fp, #-8]
    // 0x128f060: cbnz            w3, #0x128f074
    // 0x128f064: r0 = Instance_SizedBox
    //     0x128f064: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x128f068: LeaveFrame
    //     0x128f068: mov             SP, fp
    //     0x128f06c: ldp             fp, lr, [SP], #0x10
    // 0x128f070: ret
    //     0x128f070: ret             
    // 0x128f074: mov             x2, x1
    // 0x128f078: r1 = Function '<anonymous closure>':.
    //     0x128f078: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b60] AnonymousClosure: (0x128f104), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x128f010)
    //     0x128f07c: ldr             x1, [x1, #0xb60]
    // 0x128f080: r0 = AllocateClosure()
    //     0x128f080: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128f084: stur            x0, [fp, #-0x10]
    // 0x128f088: r0 = ListView()
    //     0x128f088: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x128f08c: stur            x0, [fp, #-0x18]
    // 0x128f090: r16 = Instance_EdgeInsets
    //     0x128f090: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x128f094: ldr             x16, [x16, #0x1f0]
    // 0x128f098: r30 = Instance_Axis
    //     0x128f098: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128f09c: stp             lr, x16, [SP]
    // 0x128f0a0: mov             x1, x0
    // 0x128f0a4: ldur            x2, [fp, #-0x10]
    // 0x128f0a8: ldur            x3, [fp, #-8]
    // 0x128f0ac: r4 = const [0, 0x5, 0x2, 0x3, padding, 0x3, scrollDirection, 0x4, null]
    //     0x128f0ac: add             x4, PP, #0x48, lsl #12  ; [pp+0x48398] List(9) [0, 0x5, 0x2, 0x3, "padding", 0x3, "scrollDirection", 0x4, Null]
    //     0x128f0b0: ldr             x4, [x4, #0x398]
    // 0x128f0b4: r0 = ListView.builder()
    //     0x128f0b4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x128f0b8: r0 = Container()
    //     0x128f0b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128f0bc: stur            x0, [fp, #-8]
    // 0x128f0c0: r16 = Instance_Color
    //     0x128f0c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x128f0c4: ldr             x16, [x16, #0x90]
    // 0x128f0c8: r30 = 120.000000
    //     0x128f0c8: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0x128f0cc: ldr             lr, [lr, #0x3a0]
    // 0x128f0d0: stp             lr, x16, [SP, #8]
    // 0x128f0d4: ldur            x16, [fp, #-0x18]
    // 0x128f0d8: str             x16, [SP]
    // 0x128f0dc: mov             x1, x0
    // 0x128f0e0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, height, 0x2, null]
    //     0x128f0e0: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f308] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "height", 0x2, Null]
    //     0x128f0e4: ldr             x4, [x4, #0x308]
    // 0x128f0e8: r0 = Container()
    //     0x128f0e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128f0ec: ldur            x0, [fp, #-8]
    // 0x128f0f0: LeaveFrame
    //     0x128f0f0: mov             SP, fp
    //     0x128f0f4: ldp             fp, lr, [SP], #0x10
    // 0x128f0f8: ret
    //     0x128f0f8: ret             
    // 0x128f0fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128f0fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128f100: b               #0x128f02c
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x128f104, size: 0x4dc
    // 0x128f104: EnterFrame
    //     0x128f104: stp             fp, lr, [SP, #-0x10]!
    //     0x128f108: mov             fp, SP
    // 0x128f10c: AllocStack(0x78)
    //     0x128f10c: sub             SP, SP, #0x78
    // 0x128f110: SetupParameters()
    //     0x128f110: ldr             x0, [fp, #0x20]
    //     0x128f114: ldur            w1, [x0, #0x17]
    //     0x128f118: add             x1, x1, HEAP, lsl #32
    //     0x128f11c: stur            x1, [fp, #-8]
    // 0x128f120: CheckStackOverflow
    //     0x128f120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128f124: cmp             SP, x16
    //     0x128f128: b.ls            #0x128f5d0
    // 0x128f12c: r1 = 1
    //     0x128f12c: movz            x1, #0x1
    // 0x128f130: r0 = AllocateContext()
    //     0x128f130: bl              #0x16f6108  ; AllocateContextStub
    // 0x128f134: mov             x2, x0
    // 0x128f138: ldur            x0, [fp, #-8]
    // 0x128f13c: stur            x2, [fp, #-0x10]
    // 0x128f140: StoreField: r2->field_b = r0
    //     0x128f140: stur            w0, [x2, #0xb]
    // 0x128f144: LoadField: r3 = r0->field_f
    //     0x128f144: ldur            w3, [x0, #0xf]
    // 0x128f148: DecompressPointer r3
    //     0x128f148: add             x3, x3, HEAP, lsl #32
    // 0x128f14c: LoadField: r0 = r3->field_b
    //     0x128f14c: ldur            w0, [x3, #0xb]
    // 0x128f150: DecompressPointer r0
    //     0x128f150: add             x0, x0, HEAP, lsl #32
    // 0x128f154: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x128f154: ldur            w4, [x0, #0x17]
    // 0x128f158: DecompressPointer r4
    //     0x128f158: add             x4, x4, HEAP, lsl #32
    // 0x128f15c: cmp             w4, NULL
    // 0x128f160: b.eq            #0x128f5d8
    // 0x128f164: LoadField: r0 = r4->field_b
    //     0x128f164: ldur            w0, [x4, #0xb]
    // 0x128f168: ldr             x1, [fp, #0x10]
    // 0x128f16c: r5 = LoadInt32Instr(r1)
    //     0x128f16c: sbfx            x5, x1, #1, #0x1f
    //     0x128f170: tbz             w1, #0, #0x128f178
    //     0x128f174: ldur            x5, [x1, #7]
    // 0x128f178: r1 = LoadInt32Instr(r0)
    //     0x128f178: sbfx            x1, x0, #1, #0x1f
    // 0x128f17c: mov             x0, x1
    // 0x128f180: mov             x1, x5
    // 0x128f184: cmp             x1, x0
    // 0x128f188: b.hs            #0x128f5dc
    // 0x128f18c: LoadField: r0 = r4->field_f
    //     0x128f18c: ldur            w0, [x4, #0xf]
    // 0x128f190: DecompressPointer r0
    //     0x128f190: add             x0, x0, HEAP, lsl #32
    // 0x128f194: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x128f194: add             x16, x0, x5, lsl #2
    //     0x128f198: ldur            w1, [x16, #0xf]
    // 0x128f19c: DecompressPointer r1
    //     0x128f19c: add             x1, x1, HEAP, lsl #32
    // 0x128f1a0: stur            x1, [fp, #-8]
    // 0x128f1a4: StoreField: r2->field_f = r1
    //     0x128f1a4: stur            w1, [x2, #0xf]
    // 0x128f1a8: LoadField: r0 = r1->field_37
    //     0x128f1a8: ldur            w0, [x1, #0x37]
    // 0x128f1ac: DecompressPointer r0
    //     0x128f1ac: add             x0, x0, HEAP, lsl #32
    // 0x128f1b0: LoadField: r4 = r3->field_f
    //     0x128f1b0: ldur            w4, [x3, #0xf]
    // 0x128f1b4: DecompressPointer r4
    //     0x128f1b4: add             x4, x4, HEAP, lsl #32
    // 0x128f1b8: r3 = LoadClassIdInstr(r0)
    //     0x128f1b8: ldur            x3, [x0, #-1]
    //     0x128f1bc: ubfx            x3, x3, #0xc, #0x14
    // 0x128f1c0: stp             x4, x0, [SP]
    // 0x128f1c4: mov             x0, x3
    // 0x128f1c8: mov             lr, x0
    // 0x128f1cc: ldr             lr, [x21, lr, lsl #3]
    // 0x128f1d0: blr             lr
    // 0x128f1d4: stur            x0, [fp, #-0x18]
    // 0x128f1d8: tbnz            w0, #4, #0x128f1e4
    // 0x128f1dc: r2 = Instance_Color
    //     0x128f1dc: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128f1e0: b               #0x128f1ec
    // 0x128f1e4: r2 = Instance_Color
    //     0x128f1e4: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x128f1e8: ldr             x2, [x2, #0xf88]
    // 0x128f1ec: ldur            x3, [fp, #-8]
    // 0x128f1f0: r16 = 2.000000
    //     0x128f1f0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0x128f1f4: ldr             x16, [x16, #0xdf8]
    // 0x128f1f8: str             x16, [SP]
    // 0x128f1fc: r1 = Null
    //     0x128f1fc: mov             x1, NULL
    // 0x128f200: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x128f200: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x128f204: ldr             x4, [x4, #0x108]
    // 0x128f208: r0 = Border.all()
    //     0x128f208: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x128f20c: stur            x0, [fp, #-0x20]
    // 0x128f210: r0 = Radius()
    //     0x128f210: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128f214: d0 = 10.000000
    //     0x128f214: fmov            d0, #10.00000000
    // 0x128f218: stur            x0, [fp, #-0x28]
    // 0x128f21c: StoreField: r0->field_7 = d0
    //     0x128f21c: stur            d0, [x0, #7]
    // 0x128f220: StoreField: r0->field_f = d0
    //     0x128f220: stur            d0, [x0, #0xf]
    // 0x128f224: r0 = BorderRadius()
    //     0x128f224: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128f228: mov             x1, x0
    // 0x128f22c: ldur            x0, [fp, #-0x28]
    // 0x128f230: stur            x1, [fp, #-0x30]
    // 0x128f234: StoreField: r1->field_7 = r0
    //     0x128f234: stur            w0, [x1, #7]
    // 0x128f238: StoreField: r1->field_b = r0
    //     0x128f238: stur            w0, [x1, #0xb]
    // 0x128f23c: StoreField: r1->field_f = r0
    //     0x128f23c: stur            w0, [x1, #0xf]
    // 0x128f240: StoreField: r1->field_13 = r0
    //     0x128f240: stur            w0, [x1, #0x13]
    // 0x128f244: r0 = BoxDecoration()
    //     0x128f244: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128f248: mov             x1, x0
    // 0x128f24c: ldur            x0, [fp, #-0x20]
    // 0x128f250: stur            x1, [fp, #-0x28]
    // 0x128f254: StoreField: r1->field_f = r0
    //     0x128f254: stur            w0, [x1, #0xf]
    // 0x128f258: ldur            x0, [fp, #-0x30]
    // 0x128f25c: StoreField: r1->field_13 = r0
    //     0x128f25c: stur            w0, [x1, #0x13]
    // 0x128f260: r0 = Instance_BoxShape
    //     0x128f260: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128f264: ldr             x0, [x0, #0x80]
    // 0x128f268: StoreField: r1->field_23 = r0
    //     0x128f268: stur            w0, [x1, #0x23]
    // 0x128f26c: r0 = Radius()
    //     0x128f26c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128f270: d0 = 10.000000
    //     0x128f270: fmov            d0, #10.00000000
    // 0x128f274: stur            x0, [fp, #-0x20]
    // 0x128f278: StoreField: r0->field_7 = d0
    //     0x128f278: stur            d0, [x0, #7]
    // 0x128f27c: StoreField: r0->field_f = d0
    //     0x128f27c: stur            d0, [x0, #0xf]
    // 0x128f280: r0 = BorderRadius()
    //     0x128f280: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128f284: mov             x3, x0
    // 0x128f288: ldur            x0, [fp, #-0x20]
    // 0x128f28c: stur            x3, [fp, #-0x30]
    // 0x128f290: StoreField: r3->field_7 = r0
    //     0x128f290: stur            w0, [x3, #7]
    // 0x128f294: StoreField: r3->field_b = r0
    //     0x128f294: stur            w0, [x3, #0xb]
    // 0x128f298: StoreField: r3->field_f = r0
    //     0x128f298: stur            w0, [x3, #0xf]
    // 0x128f29c: StoreField: r3->field_13 = r0
    //     0x128f29c: stur            w0, [x3, #0x13]
    // 0x128f2a0: ldur            x0, [fp, #-8]
    // 0x128f2a4: LoadField: r1 = r0->field_13
    //     0x128f2a4: ldur            w1, [x0, #0x13]
    // 0x128f2a8: DecompressPointer r1
    //     0x128f2a8: add             x1, x1, HEAP, lsl #32
    // 0x128f2ac: cmp             w1, NULL
    // 0x128f2b0: b.ne            #0x128f2bc
    // 0x128f2b4: r4 = ""
    //     0x128f2b4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128f2b8: b               #0x128f2c0
    // 0x128f2bc: mov             x4, x1
    // 0x128f2c0: stur            x4, [fp, #-0x20]
    // 0x128f2c4: r1 = Function '<anonymous closure>':.
    //     0x128f2c4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b68] AnonymousClosure: (0x1289e68), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x12933b0)
    //     0x128f2c8: ldr             x1, [x1, #0xb68]
    // 0x128f2cc: r2 = Null
    //     0x128f2cc: mov             x2, NULL
    // 0x128f2d0: r0 = AllocateClosure()
    //     0x128f2d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128f2d4: r1 = Function '<anonymous closure>':.
    //     0x128f2d4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b70] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x128f2d8: ldr             x1, [x1, #0xb70]
    // 0x128f2dc: r2 = Null
    //     0x128f2dc: mov             x2, NULL
    // 0x128f2e0: stur            x0, [fp, #-0x38]
    // 0x128f2e4: r0 = AllocateClosure()
    //     0x128f2e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128f2e8: stur            x0, [fp, #-0x40]
    // 0x128f2ec: r0 = CachedNetworkImage()
    //     0x128f2ec: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x128f2f0: stur            x0, [fp, #-0x48]
    // 0x128f2f4: r16 = 60.000000
    //     0x128f2f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x128f2f8: ldr             x16, [x16, #0x110]
    // 0x128f2fc: r30 = 60.000000
    //     0x128f2fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x128f300: ldr             lr, [lr, #0x110]
    // 0x128f304: stp             lr, x16, [SP, #0x18]
    // 0x128f308: r16 = Instance_BoxFit
    //     0x128f308: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x128f30c: ldr             x16, [x16, #0xb18]
    // 0x128f310: ldur            lr, [fp, #-0x38]
    // 0x128f314: stp             lr, x16, [SP, #8]
    // 0x128f318: ldur            x16, [fp, #-0x40]
    // 0x128f31c: str             x16, [SP]
    // 0x128f320: mov             x1, x0
    // 0x128f324: ldur            x2, [fp, #-0x20]
    // 0x128f328: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x128f328: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x128f32c: ldr             x4, [x4, #0xc28]
    // 0x128f330: r0 = CachedNetworkImage()
    //     0x128f330: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x128f334: r0 = ClipRRect()
    //     0x128f334: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x128f338: mov             x1, x0
    // 0x128f33c: ldur            x0, [fp, #-0x30]
    // 0x128f340: stur            x1, [fp, #-0x20]
    // 0x128f344: StoreField: r1->field_f = r0
    //     0x128f344: stur            w0, [x1, #0xf]
    // 0x128f348: r0 = Instance_Clip
    //     0x128f348: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x128f34c: ldr             x0, [x0, #0x138]
    // 0x128f350: ArrayStore: r1[0] = r0  ; List_4
    //     0x128f350: stur            w0, [x1, #0x17]
    // 0x128f354: ldur            x0, [fp, #-0x48]
    // 0x128f358: StoreField: r1->field_b = r0
    //     0x128f358: stur            w0, [x1, #0xb]
    // 0x128f35c: r0 = Container()
    //     0x128f35c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128f360: stur            x0, [fp, #-0x30]
    // 0x128f364: ldur            x16, [fp, #-0x28]
    // 0x128f368: ldur            lr, [fp, #-0x20]
    // 0x128f36c: stp             lr, x16, [SP]
    // 0x128f370: mov             x1, x0
    // 0x128f374: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x128f374: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x128f378: ldr             x4, [x4, #0x88]
    // 0x128f37c: r0 = Container()
    //     0x128f37c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128f380: r1 = Null
    //     0x128f380: mov             x1, NULL
    // 0x128f384: r2 = 2
    //     0x128f384: movz            x2, #0x2
    // 0x128f388: r0 = AllocateArray()
    //     0x128f388: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128f38c: mov             x2, x0
    // 0x128f390: ldur            x0, [fp, #-0x30]
    // 0x128f394: stur            x2, [fp, #-0x20]
    // 0x128f398: StoreField: r2->field_f = r0
    //     0x128f398: stur            w0, [x2, #0xf]
    // 0x128f39c: r1 = <Widget>
    //     0x128f39c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128f3a0: r0 = AllocateGrowableArray()
    //     0x128f3a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128f3a4: mov             x2, x0
    // 0x128f3a8: ldur            x0, [fp, #-0x20]
    // 0x128f3ac: stur            x2, [fp, #-0x28]
    // 0x128f3b0: StoreField: r2->field_f = r0
    //     0x128f3b0: stur            w0, [x2, #0xf]
    // 0x128f3b4: r0 = 2
    //     0x128f3b4: movz            x0, #0x2
    // 0x128f3b8: StoreField: r2->field_b = r0
    //     0x128f3b8: stur            w0, [x2, #0xb]
    // 0x128f3bc: ldur            x0, [fp, #-8]
    // 0x128f3c0: r17 = 279
    //     0x128f3c0: movz            x17, #0x117
    // 0x128f3c4: ldr             w1, [x0, x17]
    // 0x128f3c8: DecompressPointer r1
    //     0x128f3c8: add             x1, x1, HEAP, lsl #32
    // 0x128f3cc: cmp             w1, NULL
    // 0x128f3d0: b.eq            #0x128f4f8
    // 0x128f3d4: ldur            x0, [fp, #-0x18]
    // 0x128f3d8: r0 = StringExtension.toTitleCase()
    //     0x128f3d8: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0x128f3dc: ldr             x1, [fp, #0x18]
    // 0x128f3e0: stur            x0, [fp, #-8]
    // 0x128f3e4: r0 = of()
    //     0x128f3e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128f3e8: LoadField: r1 = r0->field_87
    //     0x128f3e8: ldur            w1, [x0, #0x87]
    // 0x128f3ec: DecompressPointer r1
    //     0x128f3ec: add             x1, x1, HEAP, lsl #32
    // 0x128f3f0: LoadField: r0 = r1->field_2b
    //     0x128f3f0: ldur            w0, [x1, #0x2b]
    // 0x128f3f4: DecompressPointer r0
    //     0x128f3f4: add             x0, x0, HEAP, lsl #32
    // 0x128f3f8: ldur            x1, [fp, #-0x18]
    // 0x128f3fc: stur            x0, [fp, #-0x20]
    // 0x128f400: tbnz            w1, #4, #0x128f40c
    // 0x128f404: r1 = Instance_Color
    //     0x128f404: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128f408: b               #0x128f41c
    // 0x128f40c: r1 = Instance_Color
    //     0x128f40c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128f410: d0 = 0.400000
    //     0x128f410: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x128f414: r0 = withOpacity()
    //     0x128f414: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x128f418: mov             x1, x0
    // 0x128f41c: ldur            x0, [fp, #-8]
    // 0x128f420: ldur            x2, [fp, #-0x28]
    // 0x128f424: r16 = 12.000000
    //     0x128f424: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x128f428: ldr             x16, [x16, #0x9e8]
    // 0x128f42c: stp             x1, x16, [SP]
    // 0x128f430: ldur            x1, [fp, #-0x20]
    // 0x128f434: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x128f434: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x128f438: ldr             x4, [x4, #0xaa0]
    // 0x128f43c: r0 = copyWith()
    //     0x128f43c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x128f440: stur            x0, [fp, #-0x18]
    // 0x128f444: r0 = Text()
    //     0x128f444: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128f448: mov             x1, x0
    // 0x128f44c: ldur            x0, [fp, #-8]
    // 0x128f450: stur            x1, [fp, #-0x20]
    // 0x128f454: StoreField: r1->field_b = r0
    //     0x128f454: stur            w0, [x1, #0xb]
    // 0x128f458: ldur            x0, [fp, #-0x18]
    // 0x128f45c: StoreField: r1->field_13 = r0
    //     0x128f45c: stur            w0, [x1, #0x13]
    // 0x128f460: r0 = Padding()
    //     0x128f460: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128f464: mov             x2, x0
    // 0x128f468: r0 = Instance_EdgeInsets
    //     0x128f468: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x128f46c: ldr             x0, [x0, #0x770]
    // 0x128f470: stur            x2, [fp, #-8]
    // 0x128f474: StoreField: r2->field_f = r0
    //     0x128f474: stur            w0, [x2, #0xf]
    // 0x128f478: ldur            x0, [fp, #-0x20]
    // 0x128f47c: StoreField: r2->field_b = r0
    //     0x128f47c: stur            w0, [x2, #0xb]
    // 0x128f480: ldur            x0, [fp, #-0x28]
    // 0x128f484: LoadField: r1 = r0->field_b
    //     0x128f484: ldur            w1, [x0, #0xb]
    // 0x128f488: LoadField: r3 = r0->field_f
    //     0x128f488: ldur            w3, [x0, #0xf]
    // 0x128f48c: DecompressPointer r3
    //     0x128f48c: add             x3, x3, HEAP, lsl #32
    // 0x128f490: LoadField: r4 = r3->field_b
    //     0x128f490: ldur            w4, [x3, #0xb]
    // 0x128f494: r3 = LoadInt32Instr(r1)
    //     0x128f494: sbfx            x3, x1, #1, #0x1f
    // 0x128f498: stur            x3, [fp, #-0x50]
    // 0x128f49c: r1 = LoadInt32Instr(r4)
    //     0x128f49c: sbfx            x1, x4, #1, #0x1f
    // 0x128f4a0: cmp             x3, x1
    // 0x128f4a4: b.ne            #0x128f4b0
    // 0x128f4a8: mov             x1, x0
    // 0x128f4ac: r0 = _growToNextCapacity()
    //     0x128f4ac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128f4b0: ldur            x2, [fp, #-0x28]
    // 0x128f4b4: ldur            x3, [fp, #-0x50]
    // 0x128f4b8: add             x0, x3, #1
    // 0x128f4bc: lsl             x1, x0, #1
    // 0x128f4c0: StoreField: r2->field_b = r1
    //     0x128f4c0: stur            w1, [x2, #0xb]
    // 0x128f4c4: LoadField: r1 = r2->field_f
    //     0x128f4c4: ldur            w1, [x2, #0xf]
    // 0x128f4c8: DecompressPointer r1
    //     0x128f4c8: add             x1, x1, HEAP, lsl #32
    // 0x128f4cc: ldur            x0, [fp, #-8]
    // 0x128f4d0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128f4d0: add             x25, x1, x3, lsl #2
    //     0x128f4d4: add             x25, x25, #0xf
    //     0x128f4d8: str             w0, [x25]
    //     0x128f4dc: tbz             w0, #0, #0x128f4f8
    //     0x128f4e0: ldurb           w16, [x1, #-1]
    //     0x128f4e4: ldurb           w17, [x0, #-1]
    //     0x128f4e8: and             x16, x17, x16, lsr #2
    //     0x128f4ec: tst             x16, HEAP, lsr #32
    //     0x128f4f0: b.eq            #0x128f4f8
    //     0x128f4f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128f4f8: r0 = Column()
    //     0x128f4f8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128f4fc: mov             x1, x0
    // 0x128f500: r0 = Instance_Axis
    //     0x128f500: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128f504: stur            x1, [fp, #-8]
    // 0x128f508: StoreField: r1->field_f = r0
    //     0x128f508: stur            w0, [x1, #0xf]
    // 0x128f50c: r0 = Instance_MainAxisAlignment
    //     0x128f50c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128f510: ldr             x0, [x0, #0xa08]
    // 0x128f514: StoreField: r1->field_13 = r0
    //     0x128f514: stur            w0, [x1, #0x13]
    // 0x128f518: r0 = Instance_MainAxisSize
    //     0x128f518: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x128f51c: ldr             x0, [x0, #0xdd0]
    // 0x128f520: ArrayStore: r1[0] = r0  ; List_4
    //     0x128f520: stur            w0, [x1, #0x17]
    // 0x128f524: r0 = Instance_CrossAxisAlignment
    //     0x128f524: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128f528: ldr             x0, [x0, #0xa18]
    // 0x128f52c: StoreField: r1->field_1b = r0
    //     0x128f52c: stur            w0, [x1, #0x1b]
    // 0x128f530: r0 = Instance_VerticalDirection
    //     0x128f530: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128f534: ldr             x0, [x0, #0xa20]
    // 0x128f538: StoreField: r1->field_23 = r0
    //     0x128f538: stur            w0, [x1, #0x23]
    // 0x128f53c: r0 = Instance_Clip
    //     0x128f53c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128f540: ldr             x0, [x0, #0x38]
    // 0x128f544: StoreField: r1->field_2b = r0
    //     0x128f544: stur            w0, [x1, #0x2b]
    // 0x128f548: StoreField: r1->field_2f = rZR
    //     0x128f548: stur            xzr, [x1, #0x2f]
    // 0x128f54c: ldur            x0, [fp, #-0x28]
    // 0x128f550: StoreField: r1->field_b = r0
    //     0x128f550: stur            w0, [x1, #0xb]
    // 0x128f554: r0 = InkWell()
    //     0x128f554: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x128f558: mov             x3, x0
    // 0x128f55c: ldur            x0, [fp, #-8]
    // 0x128f560: stur            x3, [fp, #-0x18]
    // 0x128f564: StoreField: r3->field_b = r0
    //     0x128f564: stur            w0, [x3, #0xb]
    // 0x128f568: ldur            x2, [fp, #-0x10]
    // 0x128f56c: r1 = Function '<anonymous closure>':.
    //     0x128f56c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b78] AnonymousClosure: (0x128f5e0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x128f010)
    //     0x128f570: ldr             x1, [x1, #0xb78]
    // 0x128f574: r0 = AllocateClosure()
    //     0x128f574: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128f578: mov             x1, x0
    // 0x128f57c: ldur            x0, [fp, #-0x18]
    // 0x128f580: StoreField: r0->field_f = r1
    //     0x128f580: stur            w1, [x0, #0xf]
    // 0x128f584: r1 = true
    //     0x128f584: add             x1, NULL, #0x20  ; true
    // 0x128f588: StoreField: r0->field_43 = r1
    //     0x128f588: stur            w1, [x0, #0x43]
    // 0x128f58c: r2 = Instance_BoxShape
    //     0x128f58c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128f590: ldr             x2, [x2, #0x80]
    // 0x128f594: StoreField: r0->field_47 = r2
    //     0x128f594: stur            w2, [x0, #0x47]
    // 0x128f598: StoreField: r0->field_6f = r1
    //     0x128f598: stur            w1, [x0, #0x6f]
    // 0x128f59c: r2 = false
    //     0x128f59c: add             x2, NULL, #0x30  ; false
    // 0x128f5a0: StoreField: r0->field_73 = r2
    //     0x128f5a0: stur            w2, [x0, #0x73]
    // 0x128f5a4: StoreField: r0->field_83 = r1
    //     0x128f5a4: stur            w1, [x0, #0x83]
    // 0x128f5a8: StoreField: r0->field_7b = r2
    //     0x128f5a8: stur            w2, [x0, #0x7b]
    // 0x128f5ac: r0 = Padding()
    //     0x128f5ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128f5b0: r1 = Instance_EdgeInsets
    //     0x128f5b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0x128f5b4: ldr             x1, [x1, #0xd48]
    // 0x128f5b8: StoreField: r0->field_f = r1
    //     0x128f5b8: stur            w1, [x0, #0xf]
    // 0x128f5bc: ldur            x1, [fp, #-0x18]
    // 0x128f5c0: StoreField: r0->field_b = r1
    //     0x128f5c0: stur            w1, [x0, #0xb]
    // 0x128f5c4: LeaveFrame
    //     0x128f5c4: mov             SP, fp
    //     0x128f5c8: ldp             fp, lr, [SP], #0x10
    // 0x128f5cc: ret
    //     0x128f5cc: ret             
    // 0x128f5d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128f5d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128f5d4: b               #0x128f12c
    // 0x128f5d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x128f5d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x128f5dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x128f5dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x128f5e0, size: 0xb0
    // 0x128f5e0: EnterFrame
    //     0x128f5e0: stp             fp, lr, [SP, #-0x10]!
    //     0x128f5e4: mov             fp, SP
    // 0x128f5e8: AllocStack(0x18)
    //     0x128f5e8: sub             SP, SP, #0x18
    // 0x128f5ec: SetupParameters()
    //     0x128f5ec: ldr             x0, [fp, #0x10]
    //     0x128f5f0: ldur            w1, [x0, #0x17]
    //     0x128f5f4: add             x1, x1, HEAP, lsl #32
    // 0x128f5f8: CheckStackOverflow
    //     0x128f5f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128f5fc: cmp             SP, x16
    //     0x128f600: b.ls            #0x128f688
    // 0x128f604: LoadField: r0 = r1->field_f
    //     0x128f604: ldur            w0, [x1, #0xf]
    // 0x128f608: DecompressPointer r0
    //     0x128f608: add             x0, x0, HEAP, lsl #32
    // 0x128f60c: r17 = 283
    //     0x128f60c: movz            x17, #0x11b
    // 0x128f610: ldr             w2, [x0, x17]
    // 0x128f614: DecompressPointer r2
    //     0x128f614: add             x2, x2, HEAP, lsl #32
    // 0x128f618: cmp             w2, NULL
    // 0x128f61c: b.ne            #0x128f624
    // 0x128f620: r2 = ""
    //     0x128f620: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128f624: LoadField: r3 = r0->field_37
    //     0x128f624: ldur            w3, [x0, #0x37]
    // 0x128f628: DecompressPointer r3
    //     0x128f628: add             x3, x3, HEAP, lsl #32
    // 0x128f62c: cmp             w3, NULL
    // 0x128f630: b.ne            #0x128f63c
    // 0x128f634: r0 = ""
    //     0x128f634: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128f638: b               #0x128f640
    // 0x128f63c: mov             x0, x3
    // 0x128f640: LoadField: r3 = r1->field_b
    //     0x128f640: ldur            w3, [x1, #0xb]
    // 0x128f644: DecompressPointer r3
    //     0x128f644: add             x3, x3, HEAP, lsl #32
    // 0x128f648: LoadField: r1 = r3->field_f
    //     0x128f648: ldur            w1, [x3, #0xf]
    // 0x128f64c: DecompressPointer r1
    //     0x128f64c: add             x1, x1, HEAP, lsl #32
    // 0x128f650: LoadField: r3 = r1->field_13
    //     0x128f650: ldur            w3, [x1, #0x13]
    // 0x128f654: DecompressPointer r3
    //     0x128f654: add             x3, x3, HEAP, lsl #32
    // 0x128f658: stp             x2, x3, [SP, #8]
    // 0x128f65c: str             x0, [SP]
    // 0x128f660: r4 = 0
    //     0x128f660: movz            x4, #0
    // 0x128f664: ldr             x0, [SP, #0x10]
    // 0x128f668: r16 = UnlinkedCall_0x613b5c
    //     0x128f668: add             x16, PP, #0x48, lsl #12  ; [pp+0x48b80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x128f66c: add             x16, x16, #0xb80
    // 0x128f670: ldp             x5, lr, [x16]
    // 0x128f674: blr             lr
    // 0x128f678: r0 = Null
    //     0x128f678: mov             x0, NULL
    // 0x128f67c: LeaveFrame
    //     0x128f67c: mov             SP, fp
    //     0x128f680: ldp             fp, lr, [SP], #0x10
    // 0x128f684: ret
    //     0x128f684: ret             
    // 0x128f688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128f688: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128f68c: b               #0x128f604
  }
}
