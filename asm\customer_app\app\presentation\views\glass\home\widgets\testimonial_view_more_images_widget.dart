// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart

// class id: 1049408, size: 0x8
class :: {
}

// class id: 3330, size: 0x20, field offset: 0x14
class _TestimonialMoreImagesWidgetState extends State<dynamic> {

  late PageController _pageController; // offset: 0x1c

  _ build(/* No info */) {
    // ** addr: 0xb711f4, size: 0x41c
    // 0xb711f4: EnterFrame
    //     0xb711f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb711f8: mov             fp, SP
    // 0xb711fc: AllocStack(0x60)
    //     0xb711fc: sub             SP, SP, #0x60
    // 0xb71200: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb71200: stur            x1, [fp, #-8]
    //     0xb71204: stur            x2, [fp, #-0x10]
    // 0xb71208: CheckStackOverflow
    //     0xb71208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7120c: cmp             SP, x16
    //     0xb71210: b.ls            #0xb715f4
    // 0xb71214: r1 = 2
    //     0xb71214: movz            x1, #0x2
    // 0xb71218: r0 = AllocateContext()
    //     0xb71218: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7121c: mov             x3, x0
    // 0xb71220: ldur            x0, [fp, #-8]
    // 0xb71224: stur            x3, [fp, #-0x18]
    // 0xb71228: StoreField: r3->field_f = r0
    //     0xb71228: stur            w0, [x3, #0xf]
    // 0xb7122c: ldur            x1, [fp, #-0x10]
    // 0xb71230: StoreField: r3->field_13 = r1
    //     0xb71230: stur            w1, [x3, #0x13]
    // 0xb71234: mov             x2, x3
    // 0xb71238: r1 = Function '<anonymous closure>':.
    //     0xb71238: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ac0] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xb7123c: ldr             x1, [x1, #0xac0]
    // 0xb71240: r0 = AllocateClosure()
    //     0xb71240: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71244: stur            x0, [fp, #-0x10]
    // 0xb71248: r0 = IconButton()
    //     0xb71248: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xb7124c: mov             x1, x0
    // 0xb71250: ldur            x0, [fp, #-0x10]
    // 0xb71254: stur            x1, [fp, #-0x20]
    // 0xb71258: StoreField: r1->field_3b = r0
    //     0xb71258: stur            w0, [x1, #0x3b]
    // 0xb7125c: r0 = false
    //     0xb7125c: add             x0, NULL, #0x30  ; false
    // 0xb71260: StoreField: r1->field_4f = r0
    //     0xb71260: stur            w0, [x1, #0x4f]
    // 0xb71264: r2 = Instance_Icon
    //     0xb71264: add             x2, PP, #0x53, lsl #12  ; [pp+0x53478] Obj!Icon@d663b1
    //     0xb71268: ldr             x2, [x2, #0x478]
    // 0xb7126c: StoreField: r1->field_1f = r2
    //     0xb7126c: stur            w2, [x1, #0x1f]
    // 0xb71270: r2 = Instance__IconButtonVariant
    //     0xb71270: add             x2, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xb71274: ldr             x2, [x2, #0x900]
    // 0xb71278: StoreField: r1->field_6b = r2
    //     0xb71278: stur            w2, [x1, #0x6b]
    // 0xb7127c: r0 = Padding()
    //     0xb7127c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb71280: mov             x1, x0
    // 0xb71284: r0 = Instance_EdgeInsets
    //     0xb71284: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb71288: ldr             x0, [x0, #0x980]
    // 0xb7128c: stur            x1, [fp, #-0x10]
    // 0xb71290: StoreField: r1->field_f = r0
    //     0xb71290: stur            w0, [x1, #0xf]
    // 0xb71294: ldur            x0, [fp, #-0x20]
    // 0xb71298: StoreField: r1->field_b = r0
    //     0xb71298: stur            w0, [x1, #0xb]
    // 0xb7129c: r0 = Align()
    //     0xb7129c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb712a0: mov             x1, x0
    // 0xb712a4: r0 = Instance_Alignment
    //     0xb712a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb712a8: ldr             x0, [x0, #0x950]
    // 0xb712ac: stur            x1, [fp, #-0x20]
    // 0xb712b0: StoreField: r1->field_f = r0
    //     0xb712b0: stur            w0, [x1, #0xf]
    // 0xb712b4: ldur            x0, [fp, #-0x10]
    // 0xb712b8: StoreField: r1->field_b = r0
    //     0xb712b8: stur            w0, [x1, #0xb]
    // 0xb712bc: ldur            x2, [fp, #-8]
    // 0xb712c0: LoadField: r0 = r2->field_b
    //     0xb712c0: ldur            w0, [x2, #0xb]
    // 0xb712c4: DecompressPointer r0
    //     0xb712c4: add             x0, x0, HEAP, lsl #32
    // 0xb712c8: cmp             w0, NULL
    // 0xb712cc: b.eq            #0xb715fc
    // 0xb712d0: LoadField: r3 = r0->field_b
    //     0xb712d0: ldur            w3, [x0, #0xb]
    // 0xb712d4: DecompressPointer r3
    //     0xb712d4: add             x3, x3, HEAP, lsl #32
    // 0xb712d8: cmp             w3, NULL
    // 0xb712dc: b.ne            #0xb712e8
    // 0xb712e0: r3 = Null
    //     0xb712e0: mov             x3, NULL
    // 0xb712e4: b               #0xb712f0
    // 0xb712e8: LoadField: r4 = r3->field_b
    //     0xb712e8: ldur            w4, [x3, #0xb]
    // 0xb712ec: mov             x3, x4
    // 0xb712f0: cmp             w3, NULL
    // 0xb712f4: b.ne            #0xb71334
    // 0xb712f8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb712f8: ldur            w3, [x0, #0x17]
    // 0xb712fc: DecompressPointer r3
    //     0xb712fc: add             x3, x3, HEAP, lsl #32
    // 0xb71300: cmp             w3, NULL
    // 0xb71304: b.ne            #0xb71310
    // 0xb71308: r0 = Null
    //     0xb71308: mov             x0, NULL
    // 0xb7130c: b               #0xb7132c
    // 0xb71310: r0 = LoadClassIdInstr(r3)
    //     0xb71310: ldur            x0, [x3, #-1]
    //     0xb71314: ubfx            x0, x0, #0xc, #0x14
    // 0xb71318: str             x3, [SP]
    // 0xb7131c: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb7131c: movz            x17, #0xc898
    //     0xb71320: add             lr, x0, x17
    //     0xb71324: ldr             lr, [x21, lr, lsl #3]
    //     0xb71328: blr             lr
    // 0xb7132c: mov             x4, x0
    // 0xb71330: b               #0xb71338
    // 0xb71334: mov             x4, x3
    // 0xb71338: ldur            x3, [fp, #-8]
    // 0xb7133c: ldur            x0, [fp, #-0x20]
    // 0xb71340: stur            x4, [fp, #-0x28]
    // 0xb71344: LoadField: r5 = r3->field_1b
    //     0xb71344: ldur            w5, [x3, #0x1b]
    // 0xb71348: DecompressPointer r5
    //     0xb71348: add             x5, x5, HEAP, lsl #32
    // 0xb7134c: r16 = Sentinel
    //     0xb7134c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb71350: cmp             w5, w16
    // 0xb71354: b.eq            #0xb71600
    // 0xb71358: ldur            x2, [fp, #-0x18]
    // 0xb7135c: stur            x5, [fp, #-0x10]
    // 0xb71360: r1 = Function '<anonymous closure>':.
    //     0xb71360: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ac8] AnonymousClosure: (0xb71bc8), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xb711f4)
    //     0xb71364: ldr             x1, [x1, #0xac8]
    // 0xb71368: r0 = AllocateClosure()
    //     0xb71368: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7136c: ldur            x2, [fp, #-0x18]
    // 0xb71370: r1 = Function '<anonymous closure>':.
    //     0xb71370: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ad0] AnonymousClosure: (0xb71a00), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xb711f4)
    //     0xb71374: ldr             x1, [x1, #0xad0]
    // 0xb71378: stur            x0, [fp, #-0x30]
    // 0xb7137c: r0 = AllocateClosure()
    //     0xb7137c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71380: stur            x0, [fp, #-0x38]
    // 0xb71384: r0 = PageView()
    //     0xb71384: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb71388: stur            x0, [fp, #-0x40]
    // 0xb7138c: ldur            x16, [fp, #-0x10]
    // 0xb71390: str             x16, [SP]
    // 0xb71394: mov             x1, x0
    // 0xb71398: ldur            x2, [fp, #-0x38]
    // 0xb7139c: ldur            x3, [fp, #-0x28]
    // 0xb713a0: ldur            x5, [fp, #-0x30]
    // 0xb713a4: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xb713a4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xb713a8: ldr             x4, [x4, #0xd60]
    // 0xb713ac: r0 = PageView.builder()
    //     0xb713ac: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb713b0: r1 = <FlexParentData>
    //     0xb713b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb713b4: ldr             x1, [x1, #0xe00]
    // 0xb713b8: r0 = Expanded()
    //     0xb713b8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb713bc: mov             x3, x0
    // 0xb713c0: r0 = 1
    //     0xb713c0: movz            x0, #0x1
    // 0xb713c4: stur            x3, [fp, #-0x10]
    // 0xb713c8: StoreField: r3->field_13 = r0
    //     0xb713c8: stur            x0, [x3, #0x13]
    // 0xb713cc: r0 = Instance_FlexFit
    //     0xb713cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb713d0: ldr             x0, [x0, #0xe08]
    // 0xb713d4: StoreField: r3->field_1b = r0
    //     0xb713d4: stur            w0, [x3, #0x1b]
    // 0xb713d8: ldur            x0, [fp, #-0x40]
    // 0xb713dc: StoreField: r3->field_b = r0
    //     0xb713dc: stur            w0, [x3, #0xb]
    // 0xb713e0: r1 = Null
    //     0xb713e0: mov             x1, NULL
    // 0xb713e4: r2 = 6
    //     0xb713e4: movz            x2, #0x6
    // 0xb713e8: r0 = AllocateArray()
    //     0xb713e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb713ec: mov             x2, x0
    // 0xb713f0: ldur            x0, [fp, #-0x20]
    // 0xb713f4: stur            x2, [fp, #-0x28]
    // 0xb713f8: StoreField: r2->field_f = r0
    //     0xb713f8: stur            w0, [x2, #0xf]
    // 0xb713fc: r16 = Instance_SizedBox
    //     0xb713fc: add             x16, PP, #0x53, lsl #12  ; [pp+0x53490] Obj!SizedBox@d68021
    //     0xb71400: ldr             x16, [x16, #0x490]
    // 0xb71404: StoreField: r2->field_13 = r16
    //     0xb71404: stur            w16, [x2, #0x13]
    // 0xb71408: ldur            x0, [fp, #-0x10]
    // 0xb7140c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7140c: stur            w0, [x2, #0x17]
    // 0xb71410: r1 = <Widget>
    //     0xb71410: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb71414: r0 = AllocateGrowableArray()
    //     0xb71414: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb71418: mov             x1, x0
    // 0xb7141c: ldur            x0, [fp, #-0x28]
    // 0xb71420: stur            x1, [fp, #-0x10]
    // 0xb71424: StoreField: r1->field_f = r0
    //     0xb71424: stur            w0, [x1, #0xf]
    // 0xb71428: r0 = 6
    //     0xb71428: movz            x0, #0x6
    // 0xb7142c: StoreField: r1->field_b = r0
    //     0xb7142c: stur            w0, [x1, #0xb]
    // 0xb71430: r0 = Column()
    //     0xb71430: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb71434: mov             x1, x0
    // 0xb71438: r0 = Instance_Axis
    //     0xb71438: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7143c: stur            x1, [fp, #-0x20]
    // 0xb71440: StoreField: r1->field_f = r0
    //     0xb71440: stur            w0, [x1, #0xf]
    // 0xb71444: r0 = Instance_MainAxisAlignment
    //     0xb71444: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb71448: ldr             x0, [x0, #0xa08]
    // 0xb7144c: StoreField: r1->field_13 = r0
    //     0xb7144c: stur            w0, [x1, #0x13]
    // 0xb71450: r0 = Instance_MainAxisSize
    //     0xb71450: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb71454: ldr             x0, [x0, #0xa10]
    // 0xb71458: ArrayStore: r1[0] = r0  ; List_4
    //     0xb71458: stur            w0, [x1, #0x17]
    // 0xb7145c: r0 = Instance_CrossAxisAlignment
    //     0xb7145c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb71460: ldr             x0, [x0, #0xa18]
    // 0xb71464: StoreField: r1->field_1b = r0
    //     0xb71464: stur            w0, [x1, #0x1b]
    // 0xb71468: r0 = Instance_VerticalDirection
    //     0xb71468: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7146c: ldr             x0, [x0, #0xa20]
    // 0xb71470: StoreField: r1->field_23 = r0
    //     0xb71470: stur            w0, [x1, #0x23]
    // 0xb71474: r0 = Instance_Clip
    //     0xb71474: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb71478: ldr             x0, [x0, #0x38]
    // 0xb7147c: StoreField: r1->field_2b = r0
    //     0xb7147c: stur            w0, [x1, #0x2b]
    // 0xb71480: StoreField: r1->field_2f = rZR
    //     0xb71480: stur            xzr, [x1, #0x2f]
    // 0xb71484: ldur            x0, [fp, #-0x10]
    // 0xb71488: StoreField: r1->field_b = r0
    //     0xb71488: stur            w0, [x1, #0xb]
    // 0xb7148c: r0 = SafeArea()
    //     0xb7148c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb71490: mov             x2, x0
    // 0xb71494: r1 = true
    //     0xb71494: add             x1, NULL, #0x20  ; true
    // 0xb71498: stur            x2, [fp, #-0x10]
    // 0xb7149c: StoreField: r2->field_b = r1
    //     0xb7149c: stur            w1, [x2, #0xb]
    // 0xb714a0: StoreField: r2->field_f = r1
    //     0xb714a0: stur            w1, [x2, #0xf]
    // 0xb714a4: StoreField: r2->field_13 = r1
    //     0xb714a4: stur            w1, [x2, #0x13]
    // 0xb714a8: ArrayStore: r2[0] = r1  ; List_4
    //     0xb714a8: stur            w1, [x2, #0x17]
    // 0xb714ac: r0 = Instance_EdgeInsets
    //     0xb714ac: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb714b0: StoreField: r2->field_1b = r0
    //     0xb714b0: stur            w0, [x2, #0x1b]
    // 0xb714b4: r3 = false
    //     0xb714b4: add             x3, NULL, #0x30  ; false
    // 0xb714b8: StoreField: r2->field_1f = r3
    //     0xb714b8: stur            w3, [x2, #0x1f]
    // 0xb714bc: ldur            x0, [fp, #-0x20]
    // 0xb714c0: StoreField: r2->field_23 = r0
    //     0xb714c0: stur            w0, [x2, #0x23]
    // 0xb714c4: ldur            x0, [fp, #-8]
    // 0xb714c8: LoadField: r4 = r0->field_b
    //     0xb714c8: ldur            w4, [x0, #0xb]
    // 0xb714cc: DecompressPointer r4
    //     0xb714cc: add             x4, x4, HEAP, lsl #32
    // 0xb714d0: cmp             w4, NULL
    // 0xb714d4: b.eq            #0xb7160c
    // 0xb714d8: LoadField: r0 = r4->field_b
    //     0xb714d8: ldur            w0, [x4, #0xb]
    // 0xb714dc: DecompressPointer r0
    //     0xb714dc: add             x0, x0, HEAP, lsl #32
    // 0xb714e0: cmp             w0, NULL
    // 0xb714e4: b.ne            #0xb714f0
    // 0xb714e8: r0 = Null
    //     0xb714e8: mov             x0, NULL
    // 0xb714ec: b               #0xb714f8
    // 0xb714f0: LoadField: r5 = r0->field_b
    //     0xb714f0: ldur            w5, [x0, #0xb]
    // 0xb714f4: mov             x0, x5
    // 0xb714f8: cmp             w0, NULL
    // 0xb714fc: b.ne            #0xb71540
    // 0xb71500: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb71500: ldur            w0, [x4, #0x17]
    // 0xb71504: DecompressPointer r0
    //     0xb71504: add             x0, x0, HEAP, lsl #32
    // 0xb71508: cmp             w0, NULL
    // 0xb7150c: b.ne            #0xb71518
    // 0xb71510: r0 = Null
    //     0xb71510: mov             x0, NULL
    // 0xb71514: b               #0xb71538
    // 0xb71518: r4 = LoadClassIdInstr(r0)
    //     0xb71518: ldur            x4, [x0, #-1]
    //     0xb7151c: ubfx            x4, x4, #0xc, #0x14
    // 0xb71520: str             x0, [SP]
    // 0xb71524: mov             x0, x4
    // 0xb71528: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb71528: movz            x17, #0xc898
    //     0xb7152c: add             lr, x0, x17
    //     0xb71530: ldr             lr, [x21, lr, lsl #3]
    //     0xb71534: blr             lr
    // 0xb71538: mov             x3, x0
    // 0xb7153c: b               #0xb71544
    // 0xb71540: mov             x3, x0
    // 0xb71544: ldur            x0, [fp, #-0x10]
    // 0xb71548: ldur            x2, [fp, #-0x18]
    // 0xb7154c: stur            x3, [fp, #-8]
    // 0xb71550: r1 = Function '<anonymous closure>':.
    //     0xb71550: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ad8] AnonymousClosure: (0xb71610), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xb711f4)
    //     0xb71554: ldr             x1, [x1, #0xad8]
    // 0xb71558: r0 = AllocateClosure()
    //     0xb71558: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7155c: stur            x0, [fp, #-0x18]
    // 0xb71560: r0 = ListView()
    //     0xb71560: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb71564: stur            x0, [fp, #-0x20]
    // 0xb71568: r16 = Instance_Axis
    //     0xb71568: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7156c: str             x16, [SP]
    // 0xb71570: mov             x1, x0
    // 0xb71574: ldur            x2, [fp, #-0x18]
    // 0xb71578: ldur            x3, [fp, #-8]
    // 0xb7157c: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb7157c: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb71580: ldr             x4, [x4, #0x4a0]
    // 0xb71584: r0 = ListView.builder()
    //     0xb71584: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb71588: r0 = Container()
    //     0xb71588: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7158c: stur            x0, [fp, #-8]
    // 0xb71590: r16 = Instance_Color
    //     0xb71590: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb71594: r30 = 170.000000
    //     0xb71594: add             lr, PP, #0x53, lsl #12  ; [pp+0x534a8] 170
    //     0xb71598: ldr             lr, [lr, #0x4a8]
    // 0xb7159c: stp             lr, x16, [SP, #0x10]
    // 0xb715a0: r16 = Instance_EdgeInsets
    //     0xb715a0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb715a4: ldr             x16, [x16, #0x878]
    // 0xb715a8: ldur            lr, [fp, #-0x20]
    // 0xb715ac: stp             lr, x16, [SP]
    // 0xb715b0: mov             x1, x0
    // 0xb715b4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, height, 0x2, padding, 0x3, null]
    //     0xb715b4: add             x4, PP, #0x53, lsl #12  ; [pp+0x534b0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "height", 0x2, "padding", 0x3, Null]
    //     0xb715b8: ldr             x4, [x4, #0x4b0]
    // 0xb715bc: r0 = Container()
    //     0xb715bc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb715c0: r0 = Scaffold()
    //     0xb715c0: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb715c4: ldur            x1, [fp, #-0x10]
    // 0xb715c8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb715c8: stur            w1, [x0, #0x17]
    // 0xb715cc: ldur            x1, [fp, #-8]
    // 0xb715d0: StoreField: r0->field_37 = r1
    //     0xb715d0: stur            w1, [x0, #0x37]
    // 0xb715d4: r1 = true
    //     0xb715d4: add             x1, NULL, #0x20  ; true
    // 0xb715d8: StoreField: r0->field_43 = r1
    //     0xb715d8: stur            w1, [x0, #0x43]
    // 0xb715dc: r1 = false
    //     0xb715dc: add             x1, NULL, #0x30  ; false
    // 0xb715e0: StoreField: r0->field_b = r1
    //     0xb715e0: stur            w1, [x0, #0xb]
    // 0xb715e4: StoreField: r0->field_f = r1
    //     0xb715e4: stur            w1, [x0, #0xf]
    // 0xb715e8: LeaveFrame
    //     0xb715e8: mov             SP, fp
    //     0xb715ec: ldp             fp, lr, [SP], #0x10
    // 0xb715f0: ret
    //     0xb715f0: ret             
    // 0xb715f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb715f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb715f8: b               #0xb71214
    // 0xb715fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb715fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb71600: r9 = _pageController
    //     0xb71600: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ae0] Field <_TestimonialMoreImagesWidgetState@1592151003._pageController@1592151003>: late (offset: 0x1c)
    //     0xb71604: ldr             x9, [x9, #0xae0]
    // 0xb71608: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb71608: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb7160c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7160c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb71610, size: 0x29c
    // 0xb71610: EnterFrame
    //     0xb71610: stp             fp, lr, [SP, #-0x10]!
    //     0xb71614: mov             fp, SP
    // 0xb71618: AllocStack(0x48)
    //     0xb71618: sub             SP, SP, #0x48
    // 0xb7161c: SetupParameters()
    //     0xb7161c: ldr             x0, [fp, #0x20]
    //     0xb71620: ldur            w1, [x0, #0x17]
    //     0xb71624: add             x1, x1, HEAP, lsl #32
    //     0xb71628: stur            x1, [fp, #-8]
    // 0xb7162c: CheckStackOverflow
    //     0xb7162c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71630: cmp             SP, x16
    //     0xb71634: b.ls            #0xb7189c
    // 0xb71638: r1 = 1
    //     0xb71638: movz            x1, #0x1
    // 0xb7163c: r0 = AllocateContext()
    //     0xb7163c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb71640: mov             x3, x0
    // 0xb71644: ldur            x0, [fp, #-8]
    // 0xb71648: stur            x3, [fp, #-0x10]
    // 0xb7164c: StoreField: r3->field_b = r0
    //     0xb7164c: stur            w0, [x3, #0xb]
    // 0xb71650: ldr             x1, [fp, #0x10]
    // 0xb71654: StoreField: r3->field_f = r1
    //     0xb71654: stur            w1, [x3, #0xf]
    // 0xb71658: LoadField: r2 = r0->field_f
    //     0xb71658: ldur            w2, [x0, #0xf]
    // 0xb7165c: DecompressPointer r2
    //     0xb7165c: add             x2, x2, HEAP, lsl #32
    // 0xb71660: LoadField: r4 = r2->field_13
    //     0xb71660: ldur            x4, [x2, #0x13]
    // 0xb71664: r2 = LoadInt32Instr(r1)
    //     0xb71664: sbfx            x2, x1, #1, #0x1f
    //     0xb71668: tbz             w1, #0, #0xb71670
    //     0xb7166c: ldur            x2, [x1, #7]
    // 0xb71670: cmp             x4, x2
    // 0xb71674: b.ne            #0xb71684
    // 0xb71678: r2 = Instance_Color
    //     0xb71678: add             x2, PP, #0x53, lsl #12  ; [pp+0x534c0] Obj!Color@d6b101
    //     0xb7167c: ldr             x2, [x2, #0x4c0]
    // 0xb71680: b               #0xb7168c
    // 0xb71684: r2 = Instance_Color
    //     0xb71684: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb71688: ldr             x2, [x2, #0xf88]
    // 0xb7168c: r16 = 3.000000
    //     0xb7168c: add             x16, PP, #0x53, lsl #12  ; [pp+0x534c8] 3
    //     0xb71690: ldr             x16, [x16, #0x4c8]
    // 0xb71694: str             x16, [SP]
    // 0xb71698: r1 = Null
    //     0xb71698: mov             x1, NULL
    // 0xb7169c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb7169c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb716a0: ldr             x4, [x4, #0x108]
    // 0xb716a4: r0 = Border.all()
    //     0xb716a4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb716a8: stur            x0, [fp, #-0x18]
    // 0xb716ac: r0 = BoxDecoration()
    //     0xb716ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb716b0: mov             x2, x0
    // 0xb716b4: ldur            x0, [fp, #-0x18]
    // 0xb716b8: stur            x2, [fp, #-0x20]
    // 0xb716bc: StoreField: r2->field_f = r0
    //     0xb716bc: stur            w0, [x2, #0xf]
    // 0xb716c0: r0 = Instance_BoxShape
    //     0xb716c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb716c4: ldr             x0, [x0, #0x80]
    // 0xb716c8: StoreField: r2->field_23 = r0
    //     0xb716c8: stur            w0, [x2, #0x23]
    // 0xb716cc: ldur            x0, [fp, #-8]
    // 0xb716d0: LoadField: r1 = r0->field_f
    //     0xb716d0: ldur            w1, [x0, #0xf]
    // 0xb716d4: DecompressPointer r1
    //     0xb716d4: add             x1, x1, HEAP, lsl #32
    // 0xb716d8: LoadField: r3 = r1->field_b
    //     0xb716d8: ldur            w3, [x1, #0xb]
    // 0xb716dc: DecompressPointer r3
    //     0xb716dc: add             x3, x3, HEAP, lsl #32
    // 0xb716e0: cmp             w3, NULL
    // 0xb716e4: b.eq            #0xb718a4
    // 0xb716e8: LoadField: r4 = r3->field_b
    //     0xb716e8: ldur            w4, [x3, #0xb]
    // 0xb716ec: DecompressPointer r4
    //     0xb716ec: add             x4, x4, HEAP, lsl #32
    // 0xb716f0: cmp             w4, NULL
    // 0xb716f4: b.ne            #0xb71704
    // 0xb716f8: ldur            x5, [fp, #-0x10]
    // 0xb716fc: r0 = Null
    //     0xb716fc: mov             x0, NULL
    // 0xb71700: b               #0xb71768
    // 0xb71704: ldur            x5, [fp, #-0x10]
    // 0xb71708: LoadField: r0 = r5->field_f
    //     0xb71708: ldur            w0, [x5, #0xf]
    // 0xb7170c: DecompressPointer r0
    //     0xb7170c: add             x0, x0, HEAP, lsl #32
    // 0xb71710: LoadField: r1 = r4->field_b
    //     0xb71710: ldur            w1, [x4, #0xb]
    // 0xb71714: r6 = LoadInt32Instr(r0)
    //     0xb71714: sbfx            x6, x0, #1, #0x1f
    //     0xb71718: tbz             w0, #0, #0xb71720
    //     0xb7171c: ldur            x6, [x0, #7]
    // 0xb71720: r0 = LoadInt32Instr(r1)
    //     0xb71720: sbfx            x0, x1, #1, #0x1f
    // 0xb71724: mov             x1, x6
    // 0xb71728: cmp             x1, x0
    // 0xb7172c: b.hs            #0xb718a8
    // 0xb71730: LoadField: r0 = r4->field_f
    //     0xb71730: ldur            w0, [x4, #0xf]
    // 0xb71734: DecompressPointer r0
    //     0xb71734: add             x0, x0, HEAP, lsl #32
    // 0xb71738: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb71738: add             x16, x0, x6, lsl #2
    //     0xb7173c: ldur            w1, [x16, #0xf]
    // 0xb71740: DecompressPointer r1
    //     0xb71740: add             x1, x1, HEAP, lsl #32
    // 0xb71744: LoadField: r0 = r1->field_7
    //     0xb71744: ldur            w0, [x1, #7]
    // 0xb71748: DecompressPointer r0
    //     0xb71748: add             x0, x0, HEAP, lsl #32
    // 0xb7174c: cmp             w0, NULL
    // 0xb71750: b.ne            #0xb7175c
    // 0xb71754: r0 = Null
    //     0xb71754: mov             x0, NULL
    // 0xb71758: b               #0xb71768
    // 0xb7175c: LoadField: r1 = r0->field_b
    //     0xb7175c: ldur            w1, [x0, #0xb]
    // 0xb71760: DecompressPointer r1
    //     0xb71760: add             x1, x1, HEAP, lsl #32
    // 0xb71764: mov             x0, x1
    // 0xb71768: cmp             w0, NULL
    // 0xb7176c: b.ne            #0xb717cc
    // 0xb71770: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb71770: ldur            w0, [x3, #0x17]
    // 0xb71774: DecompressPointer r0
    //     0xb71774: add             x0, x0, HEAP, lsl #32
    // 0xb71778: cmp             w0, NULL
    // 0xb7177c: b.ne            #0xb71788
    // 0xb71780: r0 = Null
    //     0xb71780: mov             x0, NULL
    // 0xb71784: b               #0xb717cc
    // 0xb71788: LoadField: r1 = r5->field_f
    //     0xb71788: ldur            w1, [x5, #0xf]
    // 0xb7178c: DecompressPointer r1
    //     0xb7178c: add             x1, x1, HEAP, lsl #32
    // 0xb71790: r3 = LoadClassIdInstr(r0)
    //     0xb71790: ldur            x3, [x0, #-1]
    //     0xb71794: ubfx            x3, x3, #0xc, #0x14
    // 0xb71798: stp             x1, x0, [SP]
    // 0xb7179c: mov             x0, x3
    // 0xb717a0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb717a0: sub             lr, x0, #0xb7
    //     0xb717a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb717a8: blr             lr
    // 0xb717ac: LoadField: r1 = r0->field_2b
    //     0xb717ac: ldur            w1, [x0, #0x2b]
    // 0xb717b0: DecompressPointer r1
    //     0xb717b0: add             x1, x1, HEAP, lsl #32
    // 0xb717b4: cmp             w1, NULL
    // 0xb717b8: b.ne            #0xb717c4
    // 0xb717bc: r0 = Null
    //     0xb717bc: mov             x0, NULL
    // 0xb717c0: b               #0xb717cc
    // 0xb717c4: LoadField: r0 = r1->field_b
    //     0xb717c4: ldur            w0, [x1, #0xb]
    // 0xb717c8: DecompressPointer r0
    //     0xb717c8: add             x0, x0, HEAP, lsl #32
    // 0xb717cc: cmp             w0, NULL
    // 0xb717d0: b.ne            #0xb717d8
    // 0xb717d4: r0 = ""
    //     0xb717d4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb717d8: stur            x0, [fp, #-8]
    // 0xb717dc: r1 = Function '<anonymous closure>':.
    //     0xb717dc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ae8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb717e0: ldr             x1, [x1, #0xae8]
    // 0xb717e4: r2 = Null
    //     0xb717e4: mov             x2, NULL
    // 0xb717e8: r0 = AllocateClosure()
    //     0xb717e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb717ec: r1 = Function '<anonymous closure>':.
    //     0xb717ec: add             x1, PP, #0x55, lsl #12  ; [pp+0x55af0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb717f0: ldr             x1, [x1, #0xaf0]
    // 0xb717f4: r2 = Null
    //     0xb717f4: mov             x2, NULL
    // 0xb717f8: stur            x0, [fp, #-0x18]
    // 0xb717fc: r0 = AllocateClosure()
    //     0xb717fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71800: stur            x0, [fp, #-0x28]
    // 0xb71804: r0 = CachedNetworkImage()
    //     0xb71804: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb71808: stur            x0, [fp, #-0x30]
    // 0xb7180c: ldur            x16, [fp, #-0x18]
    // 0xb71810: ldur            lr, [fp, #-0x28]
    // 0xb71814: stp             lr, x16, [SP]
    // 0xb71818: mov             x1, x0
    // 0xb7181c: ldur            x2, [fp, #-8]
    // 0xb71820: r4 = const [0, 0x4, 0x2, 0x2, errorWidget, 0x3, progressIndicatorBuilder, 0x2, null]
    //     0xb71820: add             x4, PP, #0x55, lsl #12  ; [pp+0x55af8] List(9) [0, 0x4, 0x2, 0x2, "errorWidget", 0x3, "progressIndicatorBuilder", 0x2, Null]
    //     0xb71824: ldr             x4, [x4, #0xaf8]
    // 0xb71828: r0 = CachedNetworkImage()
    //     0xb71828: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb7182c: r0 = Container()
    //     0xb7182c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb71830: stur            x0, [fp, #-8]
    // 0xb71834: ldur            x16, [fp, #-0x20]
    // 0xb71838: r30 = Instance_EdgeInsets
    //     0xb71838: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb7183c: ldr             lr, [lr, #0x980]
    // 0xb71840: stp             lr, x16, [SP, #8]
    // 0xb71844: ldur            x16, [fp, #-0x30]
    // 0xb71848: str             x16, [SP]
    // 0xb7184c: mov             x1, x0
    // 0xb71850: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, margin, 0x2, null]
    //     0xb71850: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "margin", 0x2, Null]
    //     0xb71854: ldr             x4, [x4, #0x4e8]
    // 0xb71858: r0 = Container()
    //     0xb71858: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7185c: r0 = GestureDetector()
    //     0xb7185c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb71860: ldur            x2, [fp, #-0x10]
    // 0xb71864: r1 = Function '<anonymous closure>':.
    //     0xb71864: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b00] AnonymousClosure: (0xb718ac), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xb711f4)
    //     0xb71868: ldr             x1, [x1, #0xb00]
    // 0xb7186c: stur            x0, [fp, #-0x10]
    // 0xb71870: r0 = AllocateClosure()
    //     0xb71870: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71874: ldur            x16, [fp, #-8]
    // 0xb71878: stp             x16, x0, [SP]
    // 0xb7187c: ldur            x1, [fp, #-0x10]
    // 0xb71880: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb71880: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb71884: ldr             x4, [x4, #0xaf0]
    // 0xb71888: r0 = GestureDetector()
    //     0xb71888: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb7188c: ldur            x0, [fp, #-0x10]
    // 0xb71890: LeaveFrame
    //     0xb71890: mov             SP, fp
    //     0xb71894: ldp             fp, lr, [SP], #0x10
    // 0xb71898: ret
    //     0xb71898: ret             
    // 0xb7189c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7189c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb718a0: b               #0xb71638
    // 0xb718a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb718a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb718a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb718a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb718ac, size: 0xd4
    // 0xb718ac: EnterFrame
    //     0xb718ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb718b0: mov             fp, SP
    // 0xb718b4: AllocStack(0x10)
    //     0xb718b4: sub             SP, SP, #0x10
    // 0xb718b8: SetupParameters()
    //     0xb718b8: ldr             x0, [fp, #0x10]
    //     0xb718bc: ldur            w4, [x0, #0x17]
    //     0xb718c0: add             x4, x4, HEAP, lsl #32
    //     0xb718c4: stur            x4, [fp, #-0x10]
    // 0xb718c8: CheckStackOverflow
    //     0xb718c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb718cc: cmp             SP, x16
    //     0xb718d0: b.ls            #0xb7196c
    // 0xb718d4: LoadField: r0 = r4->field_b
    //     0xb718d4: ldur            w0, [x4, #0xb]
    // 0xb718d8: DecompressPointer r0
    //     0xb718d8: add             x0, x0, HEAP, lsl #32
    // 0xb718dc: stur            x0, [fp, #-8]
    // 0xb718e0: LoadField: r1 = r0->field_f
    //     0xb718e0: ldur            w1, [x0, #0xf]
    // 0xb718e4: DecompressPointer r1
    //     0xb718e4: add             x1, x1, HEAP, lsl #32
    // 0xb718e8: LoadField: r2 = r4->field_f
    //     0xb718e8: ldur            w2, [x4, #0xf]
    // 0xb718ec: DecompressPointer r2
    //     0xb718ec: add             x2, x2, HEAP, lsl #32
    // 0xb718f0: r3 = LoadInt32Instr(r2)
    //     0xb718f0: sbfx            x3, x2, #1, #0x1f
    //     0xb718f4: tbz             w2, #0, #0xb718fc
    //     0xb718f8: ldur            x3, [x2, #7]
    // 0xb718fc: StoreField: r1->field_13 = r3
    //     0xb718fc: stur            x3, [x1, #0x13]
    // 0xb71900: LoadField: r2 = r1->field_1b
    //     0xb71900: ldur            w2, [x1, #0x1b]
    // 0xb71904: DecompressPointer r2
    //     0xb71904: add             x2, x2, HEAP, lsl #32
    // 0xb71908: r16 = Sentinel
    //     0xb71908: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb7190c: cmp             w2, w16
    // 0xb71910: b.eq            #0xb71974
    // 0xb71914: mov             x1, x2
    // 0xb71918: mov             x2, x3
    // 0xb7191c: r3 = Instance_Cubic
    //     0xb7191c: add             x3, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0xb71920: ldr             x3, [x3, #0x2b0]
    // 0xb71924: r5 = Instance_Duration
    //     0xb71924: add             x5, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0xb71928: ldr             x5, [x5, #0xf00]
    // 0xb7192c: r0 = animateToPage()
    //     0xb7192c: bl              #0x7f6cd0  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0xb71930: ldur            x0, [fp, #-8]
    // 0xb71934: LoadField: r1 = r0->field_f
    //     0xb71934: ldur            w1, [x0, #0xf]
    // 0xb71938: DecompressPointer r1
    //     0xb71938: add             x1, x1, HEAP, lsl #32
    // 0xb7193c: ldur            x0, [fp, #-0x10]
    // 0xb71940: LoadField: r2 = r0->field_f
    //     0xb71940: ldur            w2, [x0, #0xf]
    // 0xb71944: DecompressPointer r2
    //     0xb71944: add             x2, x2, HEAP, lsl #32
    // 0xb71948: r0 = LoadInt32Instr(r2)
    //     0xb71948: sbfx            x0, x2, #1, #0x1f
    //     0xb7194c: tbz             w2, #0, #0xb71954
    //     0xb71950: ldur            x0, [x2, #7]
    // 0xb71954: mov             x2, x0
    // 0xb71958: r0 = _onImageTapped()
    //     0xb71958: bl              #0xb71980  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped
    // 0xb7195c: r0 = Null
    //     0xb7195c: mov             x0, NULL
    // 0xb71960: LeaveFrame
    //     0xb71960: mov             SP, fp
    //     0xb71964: ldp             fp, lr, [SP], #0x10
    // 0xb71968: ret
    //     0xb71968: ret             
    // 0xb7196c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7196c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb71970: b               #0xb718d4
    // 0xb71974: r9 = _pageController
    //     0xb71974: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ae0] Field <_TestimonialMoreImagesWidgetState@1592151003._pageController@1592151003>: late (offset: 0x1c)
    //     0xb71978: ldr             x9, [x9, #0xae0]
    // 0xb7197c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb7197c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _onImageTapped(/* No info */) {
    // ** addr: 0xb71980, size: 0x80
    // 0xb71980: EnterFrame
    //     0xb71980: stp             fp, lr, [SP, #-0x10]!
    //     0xb71984: mov             fp, SP
    // 0xb71988: AllocStack(0x10)
    //     0xb71988: sub             SP, SP, #0x10
    // 0xb7198c: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb7198c: stur            x1, [fp, #-8]
    //     0xb71990: stur            x2, [fp, #-0x10]
    // 0xb71994: CheckStackOverflow
    //     0xb71994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71998: cmp             SP, x16
    //     0xb7199c: b.ls            #0xb719f8
    // 0xb719a0: r1 = 2
    //     0xb719a0: movz            x1, #0x2
    // 0xb719a4: r0 = AllocateContext()
    //     0xb719a4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb719a8: mov             x2, x0
    // 0xb719ac: ldur            x3, [fp, #-8]
    // 0xb719b0: StoreField: r2->field_f = r3
    //     0xb719b0: stur            w3, [x2, #0xf]
    // 0xb719b4: ldur            x4, [fp, #-0x10]
    // 0xb719b8: r0 = BoxInt64Instr(r4)
    //     0xb719b8: sbfiz           x0, x4, #1, #0x1f
    //     0xb719bc: cmp             x4, x0, asr #1
    //     0xb719c0: b.eq            #0xb719cc
    //     0xb719c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb719c8: stur            x4, [x0, #7]
    // 0xb719cc: StoreField: r2->field_13 = r0
    //     0xb719cc: stur            w0, [x2, #0x13]
    // 0xb719d0: r1 = Function '<anonymous closure>':.
    //     0xb719d0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b08] AnonymousClosure: (0xa66fc8), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped (0xa66ffc)
    //     0xb719d4: ldr             x1, [x1, #0xb08]
    // 0xb719d8: r0 = AllocateClosure()
    //     0xb719d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb719dc: ldur            x1, [fp, #-8]
    // 0xb719e0: mov             x2, x0
    // 0xb719e4: r0 = setState()
    //     0xb719e4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb719e8: r0 = Null
    //     0xb719e8: mov             x0, NULL
    // 0xb719ec: LeaveFrame
    //     0xb719ec: mov             SP, fp
    //     0xb719f0: ldp             fp, lr, [SP], #0x10
    // 0xb719f4: ret
    //     0xb719f4: ret             
    // 0xb719f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb719f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb719fc: b               #0xb719a0
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb71a00, size: 0x1c8
    // 0xb71a00: EnterFrame
    //     0xb71a00: stp             fp, lr, [SP, #-0x10]!
    //     0xb71a04: mov             fp, SP
    // 0xb71a08: AllocStack(0x38)
    //     0xb71a08: sub             SP, SP, #0x38
    // 0xb71a0c: SetupParameters()
    //     0xb71a0c: ldr             x0, [fp, #0x20]
    //     0xb71a10: ldur            w1, [x0, #0x17]
    //     0xb71a14: add             x1, x1, HEAP, lsl #32
    // 0xb71a18: CheckStackOverflow
    //     0xb71a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71a1c: cmp             SP, x16
    //     0xb71a20: b.ls            #0xb71bb8
    // 0xb71a24: LoadField: r0 = r1->field_f
    //     0xb71a24: ldur            w0, [x1, #0xf]
    // 0xb71a28: DecompressPointer r0
    //     0xb71a28: add             x0, x0, HEAP, lsl #32
    // 0xb71a2c: LoadField: r2 = r0->field_b
    //     0xb71a2c: ldur            w2, [x0, #0xb]
    // 0xb71a30: DecompressPointer r2
    //     0xb71a30: add             x2, x2, HEAP, lsl #32
    // 0xb71a34: cmp             w2, NULL
    // 0xb71a38: b.eq            #0xb71bc0
    // 0xb71a3c: LoadField: r3 = r2->field_b
    //     0xb71a3c: ldur            w3, [x2, #0xb]
    // 0xb71a40: DecompressPointer r3
    //     0xb71a40: add             x3, x3, HEAP, lsl #32
    // 0xb71a44: cmp             w3, NULL
    // 0xb71a48: b.ne            #0xb71a58
    // 0xb71a4c: ldr             x4, [fp, #0x10]
    // 0xb71a50: r0 = Null
    //     0xb71a50: mov             x0, NULL
    // 0xb71a54: b               #0xb71ab8
    // 0xb71a58: ldr             x4, [fp, #0x10]
    // 0xb71a5c: LoadField: r0 = r3->field_b
    //     0xb71a5c: ldur            w0, [x3, #0xb]
    // 0xb71a60: r5 = LoadInt32Instr(r4)
    //     0xb71a60: sbfx            x5, x4, #1, #0x1f
    //     0xb71a64: tbz             w4, #0, #0xb71a6c
    //     0xb71a68: ldur            x5, [x4, #7]
    // 0xb71a6c: r1 = LoadInt32Instr(r0)
    //     0xb71a6c: sbfx            x1, x0, #1, #0x1f
    // 0xb71a70: mov             x0, x1
    // 0xb71a74: mov             x1, x5
    // 0xb71a78: cmp             x1, x0
    // 0xb71a7c: b.hs            #0xb71bc4
    // 0xb71a80: LoadField: r0 = r3->field_f
    //     0xb71a80: ldur            w0, [x3, #0xf]
    // 0xb71a84: DecompressPointer r0
    //     0xb71a84: add             x0, x0, HEAP, lsl #32
    // 0xb71a88: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb71a88: add             x16, x0, x5, lsl #2
    //     0xb71a8c: ldur            w1, [x16, #0xf]
    // 0xb71a90: DecompressPointer r1
    //     0xb71a90: add             x1, x1, HEAP, lsl #32
    // 0xb71a94: LoadField: r0 = r1->field_7
    //     0xb71a94: ldur            w0, [x1, #7]
    // 0xb71a98: DecompressPointer r0
    //     0xb71a98: add             x0, x0, HEAP, lsl #32
    // 0xb71a9c: cmp             w0, NULL
    // 0xb71aa0: b.ne            #0xb71aac
    // 0xb71aa4: r0 = Null
    //     0xb71aa4: mov             x0, NULL
    // 0xb71aa8: b               #0xb71ab8
    // 0xb71aac: LoadField: r1 = r0->field_b
    //     0xb71aac: ldur            w1, [x0, #0xb]
    // 0xb71ab0: DecompressPointer r1
    //     0xb71ab0: add             x1, x1, HEAP, lsl #32
    // 0xb71ab4: mov             x0, x1
    // 0xb71ab8: cmp             w0, NULL
    // 0xb71abc: b.ne            #0xb71b14
    // 0xb71ac0: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb71ac0: ldur            w0, [x2, #0x17]
    // 0xb71ac4: DecompressPointer r0
    //     0xb71ac4: add             x0, x0, HEAP, lsl #32
    // 0xb71ac8: cmp             w0, NULL
    // 0xb71acc: b.ne            #0xb71ad8
    // 0xb71ad0: r0 = Null
    //     0xb71ad0: mov             x0, NULL
    // 0xb71ad4: b               #0xb71b14
    // 0xb71ad8: r1 = LoadClassIdInstr(r0)
    //     0xb71ad8: ldur            x1, [x0, #-1]
    //     0xb71adc: ubfx            x1, x1, #0xc, #0x14
    // 0xb71ae0: stp             x4, x0, [SP]
    // 0xb71ae4: mov             x0, x1
    // 0xb71ae8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb71ae8: sub             lr, x0, #0xb7
    //     0xb71aec: ldr             lr, [x21, lr, lsl #3]
    //     0xb71af0: blr             lr
    // 0xb71af4: LoadField: r1 = r0->field_2b
    //     0xb71af4: ldur            w1, [x0, #0x2b]
    // 0xb71af8: DecompressPointer r1
    //     0xb71af8: add             x1, x1, HEAP, lsl #32
    // 0xb71afc: cmp             w1, NULL
    // 0xb71b00: b.ne            #0xb71b0c
    // 0xb71b04: r0 = Null
    //     0xb71b04: mov             x0, NULL
    // 0xb71b08: b               #0xb71b14
    // 0xb71b0c: LoadField: r0 = r1->field_b
    //     0xb71b0c: ldur            w0, [x1, #0xb]
    // 0xb71b10: DecompressPointer r0
    //     0xb71b10: add             x0, x0, HEAP, lsl #32
    // 0xb71b14: cmp             w0, NULL
    // 0xb71b18: b.ne            #0xb71b20
    // 0xb71b1c: r0 = ""
    //     0xb71b1c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb71b20: stur            x0, [fp, #-8]
    // 0xb71b24: r1 = Function '<anonymous closure>':.
    //     0xb71b24: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b10] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb71b28: ldr             x1, [x1, #0xb10]
    // 0xb71b2c: r2 = Null
    //     0xb71b2c: mov             x2, NULL
    // 0xb71b30: r0 = AllocateClosure()
    //     0xb71b30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71b34: r1 = Function '<anonymous closure>':.
    //     0xb71b34: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b18] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb71b38: ldr             x1, [x1, #0xb18]
    // 0xb71b3c: r2 = Null
    //     0xb71b3c: mov             x2, NULL
    // 0xb71b40: stur            x0, [fp, #-0x10]
    // 0xb71b44: r0 = AllocateClosure()
    //     0xb71b44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71b48: stur            x0, [fp, #-0x18]
    // 0xb71b4c: r0 = CachedNetworkImage()
    //     0xb71b4c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb71b50: stur            x0, [fp, #-0x20]
    // 0xb71b54: r16 = inf
    //     0xb71b54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb71b58: ldr             x16, [x16, #0x9f8]
    // 0xb71b5c: ldur            lr, [fp, #-0x10]
    // 0xb71b60: stp             lr, x16, [SP, #8]
    // 0xb71b64: ldur            x16, [fp, #-0x18]
    // 0xb71b68: str             x16, [SP]
    // 0xb71b6c: mov             x1, x0
    // 0xb71b70: ldur            x2, [fp, #-8]
    // 0xb71b74: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, progressIndicatorBuilder, 0x3, width, 0x2, null]
    //     0xb71b74: add             x4, PP, #0x55, lsl #12  ; [pp+0x55b20] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "progressIndicatorBuilder", 0x3, "width", 0x2, Null]
    //     0xb71b78: ldr             x4, [x4, #0xb20]
    // 0xb71b7c: r0 = CachedNetworkImage()
    //     0xb71b7c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb71b80: r0 = Container()
    //     0xb71b80: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb71b84: stur            x0, [fp, #-8]
    // 0xb71b88: r16 = Instance_EdgeInsets
    //     0xb71b88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb71b8c: ldr             x16, [x16, #0x980]
    // 0xb71b90: ldur            lr, [fp, #-0x20]
    // 0xb71b94: stp             lr, x16, [SP]
    // 0xb71b98: mov             x1, x0
    // 0xb71b9c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb71b9c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb71ba0: ldr             x4, [x4, #0x30]
    // 0xb71ba4: r0 = Container()
    //     0xb71ba4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb71ba8: ldur            x0, [fp, #-8]
    // 0xb71bac: LeaveFrame
    //     0xb71bac: mov             SP, fp
    //     0xb71bb0: ldp             fp, lr, [SP], #0x10
    // 0xb71bb4: ret
    //     0xb71bb4: ret             
    // 0xb71bb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb71bb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb71bbc: b               #0xb71a24
    // 0xb71bc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb71bc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb71bc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb71bc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb71bc8, size: 0x5c
    // 0xb71bc8: EnterFrame
    //     0xb71bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb71bcc: mov             fp, SP
    // 0xb71bd0: ldr             x0, [fp, #0x18]
    // 0xb71bd4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb71bd4: ldur            w1, [x0, #0x17]
    // 0xb71bd8: DecompressPointer r1
    //     0xb71bd8: add             x1, x1, HEAP, lsl #32
    // 0xb71bdc: CheckStackOverflow
    //     0xb71bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71be0: cmp             SP, x16
    //     0xb71be4: b.ls            #0xb71c1c
    // 0xb71be8: LoadField: r0 = r1->field_f
    //     0xb71be8: ldur            w0, [x1, #0xf]
    // 0xb71bec: DecompressPointer r0
    //     0xb71bec: add             x0, x0, HEAP, lsl #32
    // 0xb71bf0: ldr             x1, [fp, #0x10]
    // 0xb71bf4: r2 = LoadInt32Instr(r1)
    //     0xb71bf4: sbfx            x2, x1, #1, #0x1f
    //     0xb71bf8: tbz             w1, #0, #0xb71c00
    //     0xb71bfc: ldur            x2, [x1, #7]
    // 0xb71c00: StoreField: r0->field_13 = r2
    //     0xb71c00: stur            x2, [x0, #0x13]
    // 0xb71c04: mov             x1, x0
    // 0xb71c08: r0 = _onImageTapped()
    //     0xb71c08: bl              #0xb71980  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped
    // 0xb71c0c: r0 = Null
    //     0xb71c0c: mov             x0, NULL
    // 0xb71c10: LeaveFrame
    //     0xb71c10: mov             SP, fp
    //     0xb71c14: ldp             fp, lr, [SP], #0x10
    // 0xb71c18: ret
    //     0xb71c18: ret             
    // 0xb71c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb71c1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb71c20: b               #0xb71be8
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87b9c, size: 0x54
    // 0xc87b9c: EnterFrame
    //     0xc87b9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc87ba0: mov             fp, SP
    // 0xc87ba4: CheckStackOverflow
    //     0xc87ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87ba8: cmp             SP, x16
    //     0xc87bac: b.ls            #0xc87bdc
    // 0xc87bb0: LoadField: r0 = r1->field_1b
    //     0xc87bb0: ldur            w0, [x1, #0x1b]
    // 0xc87bb4: DecompressPointer r0
    //     0xc87bb4: add             x0, x0, HEAP, lsl #32
    // 0xc87bb8: r16 = Sentinel
    //     0xc87bb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87bbc: cmp             w0, w16
    // 0xc87bc0: b.eq            #0xc87be4
    // 0xc87bc4: mov             x1, x0
    // 0xc87bc8: r0 = dispose()
    //     0xc87bc8: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87bcc: r0 = Null
    //     0xc87bcc: mov             x0, NULL
    // 0xc87bd0: LeaveFrame
    //     0xc87bd0: mov             SP, fp
    //     0xc87bd4: ldp             fp, lr, [SP], #0x10
    // 0xc87bd8: ret
    //     0xc87bd8: ret             
    // 0xc87bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87bdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87be0: b               #0xc87bb0
    // 0xc87be4: r9 = _pageController
    //     0xc87be4: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ae0] Field <_TestimonialMoreImagesWidgetState@1592151003._pageController@1592151003>: late (offset: 0x1c)
    //     0xc87be8: ldr             x9, [x9, #0xae0]
    // 0xc87bec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87bec: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4071, size: 0x1c, field offset: 0xc
//   const constructor, 
class TestimonialMoreImagesWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f518, size: 0x30
    // 0xc7f518: EnterFrame
    //     0xc7f518: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f51c: mov             fp, SP
    // 0xc7f520: mov             x0, x1
    // 0xc7f524: r1 = <TestimonialMoreImagesWidget>
    //     0xc7f524: add             x1, PP, #0x48, lsl #12  ; [pp+0x48808] TypeArguments: <TestimonialMoreImagesWidget>
    //     0xc7f528: ldr             x1, [x1, #0x808]
    // 0xc7f52c: r0 = _TestimonialMoreImagesWidgetState()
    //     0xc7f52c: bl              #0xc7f548  ; Allocate_TestimonialMoreImagesWidgetStateStub -> _TestimonialMoreImagesWidgetState (size=0x20)
    // 0xc7f530: StoreField: r0->field_13 = rZR
    //     0xc7f530: stur            xzr, [x0, #0x13]
    // 0xc7f534: r1 = Sentinel
    //     0xc7f534: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f538: StoreField: r0->field_1b = r1
    //     0xc7f538: stur            w1, [x0, #0x1b]
    // 0xc7f53c: LeaveFrame
    //     0xc7f53c: mov             SP, fp
    //     0xc7f540: ldp             fp, lr, [SP], #0x10
    // 0xc7f544: ret
    //     0xc7f544: ret             
  }
}
