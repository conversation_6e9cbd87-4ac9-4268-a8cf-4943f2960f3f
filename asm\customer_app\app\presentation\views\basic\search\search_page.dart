// lib: , url: package:customer_app/app/presentation/views/basic/search/search_page.dart

// class id: 1049214, size: 0x8
class :: {
}

// class id: 4616, size: 0x14, field offset: 0x14
//   const constructor, 
class SearchPage extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x1479530, size: 0xe4
    // 0x1479530: EnterFrame
    //     0x1479530: stp             fp, lr, [SP, #-0x10]!
    //     0x1479534: mov             fp, SP
    // 0x1479538: AllocStack(0x20)
    //     0x1479538: sub             SP, SP, #0x20
    // 0x147953c: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */)
    //     0x147953c: mov             x0, x1
    //     0x1479540: stur            x1, [fp, #-8]
    // 0x1479544: CheckStackOverflow
    //     0x1479544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479548: cmp             SP, x16
    //     0x147954c: b.ls            #0x147960c
    // 0x1479550: r1 = 1
    //     0x1479550: movz            x1, #0x1
    // 0x1479554: r0 = AllocateContext()
    //     0x1479554: bl              #0x16f6108  ; AllocateContextStub
    // 0x1479558: ldur            x2, [fp, #-8]
    // 0x147955c: stur            x0, [fp, #-0x10]
    // 0x1479560: StoreField: r0->field_f = r2
    //     0x1479560: stur            w2, [x0, #0xf]
    // 0x1479564: r0 = Obx()
    //     0x1479564: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1479568: ldur            x2, [fp, #-0x10]
    // 0x147956c: r1 = Function '<anonymous closure>':.
    //     0x147956c: add             x1, PP, #0x44, lsl #12  ; [pp+0x449e0] AnonymousClosure: (0x147985c), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::body (0x1479530)
    //     0x1479570: ldr             x1, [x1, #0x9e0]
    // 0x1479574: stur            x0, [fp, #-0x18]
    // 0x1479578: r0 = AllocateClosure()
    //     0x1479578: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147957c: mov             x1, x0
    // 0x1479580: ldur            x0, [fp, #-0x18]
    // 0x1479584: StoreField: r0->field_b = r1
    //     0x1479584: stur            w1, [x0, #0xb]
    // 0x1479588: r0 = WillPopScope()
    //     0x1479588: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x147958c: mov             x3, x0
    // 0x1479590: ldur            x0, [fp, #-0x18]
    // 0x1479594: stur            x3, [fp, #-0x20]
    // 0x1479598: StoreField: r3->field_b = r0
    //     0x1479598: stur            w0, [x3, #0xb]
    // 0x147959c: ldur            x2, [fp, #-8]
    // 0x14795a0: r1 = Function 'onBackPress':.
    //     0x14795a0: add             x1, PP, #0x44, lsl #12  ; [pp+0x449e8] AnonymousClosure: (0x147974c), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress (0x1479784)
    //     0x14795a4: ldr             x1, [x1, #0x9e8]
    // 0x14795a8: r0 = AllocateClosure()
    //     0x14795a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14795ac: mov             x1, x0
    // 0x14795b0: ldur            x0, [fp, #-0x20]
    // 0x14795b4: StoreField: r0->field_f = r1
    //     0x14795b4: stur            w1, [x0, #0xf]
    // 0x14795b8: ldur            x2, [fp, #-0x10]
    // 0x14795bc: r1 = Function '<anonymous closure>':.
    //     0x14795bc: add             x1, PP, #0x44, lsl #12  ; [pp+0x449f0] AnonymousClosure: (0x14796e4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14795c0: ldr             x1, [x1, #0x9f0]
    // 0x14795c4: r0 = AllocateClosure()
    //     0x14795c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14795c8: ldur            x2, [fp, #-0x10]
    // 0x14795cc: r1 = Function '<anonymous closure>':.
    //     0x14795cc: add             x1, PP, #0x44, lsl #12  ; [pp+0x449f8] AnonymousClosure: (0x1479614), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14795d0: ldr             x1, [x1, #0x9f8]
    // 0x14795d4: stur            x0, [fp, #-8]
    // 0x14795d8: r0 = AllocateClosure()
    //     0x14795d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14795dc: stur            x0, [fp, #-0x10]
    // 0x14795e0: r0 = PagingView()
    //     0x14795e0: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14795e4: mov             x1, x0
    // 0x14795e8: ldur            x2, [fp, #-0x20]
    // 0x14795ec: ldur            x3, [fp, #-0x10]
    // 0x14795f0: ldur            x5, [fp, #-8]
    // 0x14795f4: stur            x0, [fp, #-8]
    // 0x14795f8: r0 = PagingView()
    //     0x14795f8: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14795fc: ldur            x0, [fp, #-8]
    // 0x1479600: LeaveFrame
    //     0x1479600: mov             SP, fp
    //     0x1479604: ldp             fp, lr, [SP], #0x10
    // 0x1479608: ret
    //     0x1479608: ret             
    // 0x147960c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147960c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479610: b               #0x1479550
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x147974c, size: 0x38
    // 0x147974c: EnterFrame
    //     0x147974c: stp             fp, lr, [SP, #-0x10]!
    //     0x1479750: mov             fp, SP
    // 0x1479754: ldr             x0, [fp, #0x10]
    // 0x1479758: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1479758: ldur            w1, [x0, #0x17]
    // 0x147975c: DecompressPointer r1
    //     0x147975c: add             x1, x1, HEAP, lsl #32
    // 0x1479760: CheckStackOverflow
    //     0x1479760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479764: cmp             SP, x16
    //     0x1479768: b.ls            #0x147977c
    // 0x147976c: r0 = onBackPress()
    //     0x147976c: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x1479770: LeaveFrame
    //     0x1479770: mov             SP, fp
    //     0x1479774: ldp             fp, lr, [SP], #0x10
    // 0x1479778: ret
    //     0x1479778: ret             
    // 0x147977c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147977c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479780: b               #0x147976c
  }
  _ onBackPress(/* No info */) {
    // ** addr: 0x1479784, size: 0xd8
    // 0x1479784: EnterFrame
    //     0x1479784: stp             fp, lr, [SP, #-0x10]!
    //     0x1479788: mov             fp, SP
    // 0x147978c: AllocStack(0x20)
    //     0x147978c: sub             SP, SP, #0x20
    // 0x1479790: CheckStackOverflow
    //     0x1479790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479794: cmp             SP, x16
    //     0x1479798: b.ls            #0x1479854
    // 0x147979c: r2 = false
    //     0x147979c: add             x2, NULL, #0x30  ; false
    // 0x14797a0: r0 = showLoading()
    //     0x14797a0: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x14797a4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14797a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14797a8: ldr             x0, [x0, #0x1c80]
    //     0x14797ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14797b0: cmp             w0, w16
    //     0x14797b4: b.ne            #0x14797c0
    //     0x14797b8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14797bc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14797c0: r1 = Null
    //     0x14797c0: mov             x1, NULL
    // 0x14797c4: r2 = 4
    //     0x14797c4: movz            x2, #0x4
    // 0x14797c8: r0 = AllocateArray()
    //     0x14797c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14797cc: r16 = "selected_bottom_index"
    //     0x14797cc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb478] "selected_bottom_index"
    //     0x14797d0: ldr             x16, [x16, #0x478]
    // 0x14797d4: StoreField: r0->field_f = r16
    //     0x14797d4: stur            w16, [x0, #0xf]
    // 0x14797d8: StoreField: r0->field_13 = rZR
    //     0x14797d8: stur            wzr, [x0, #0x13]
    // 0x14797dc: r16 = <String, int>
    //     0x14797dc: ldr             x16, [PP, #0xd40]  ; [pp+0xd40] TypeArguments: <String, int>
    // 0x14797e0: stp             x0, x16, [SP]
    // 0x14797e4: r0 = Map._fromLiteral()
    //     0x14797e4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x14797e8: r16 = "/"
    //     0x14797e8: ldr             x16, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x14797ec: stp             x16, NULL, [SP, #8]
    // 0x14797f0: str             x0, [SP]
    // 0x14797f4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x14797f4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x14797f8: ldr             x4, [x4, #0x438]
    // 0x14797fc: r0 = GetNavigation.toNamed()
    //     0x14797fc: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1479800: r1 = <bool>
    //     0x1479800: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x1479804: r0 = _Future()
    //     0x1479804: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x1479808: stur            x0, [fp, #-8]
    // 0x147980c: StoreField: r0->field_b = rZR
    //     0x147980c: stur            xzr, [x0, #0xb]
    // 0x1479810: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x1479810: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1479814: ldr             x0, [x0, #0x778]
    //     0x1479818: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x147981c: cmp             w0, w16
    //     0x1479820: b.ne            #0x147982c
    //     0x1479824: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x1479828: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x147982c: mov             x1, x0
    // 0x1479830: ldur            x0, [fp, #-8]
    // 0x1479834: StoreField: r0->field_13 = r1
    //     0x1479834: stur            w1, [x0, #0x13]
    // 0x1479838: mov             x1, x0
    // 0x147983c: r2 = true
    //     0x147983c: add             x2, NULL, #0x20  ; true
    // 0x1479840: r0 = _asyncComplete()
    //     0x1479840: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x1479844: ldur            x0, [fp, #-8]
    // 0x1479848: LeaveFrame
    //     0x1479848: mov             SP, fp
    //     0x147984c: ldp             fp, lr, [SP], #0x10
    // 0x1479850: ret
    //     0x1479850: ret             
    // 0x1479854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1479854: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479858: b               #0x147979c
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x147985c, size: 0x2d4
    // 0x147985c: EnterFrame
    //     0x147985c: stp             fp, lr, [SP, #-0x10]!
    //     0x1479860: mov             fp, SP
    // 0x1479864: AllocStack(0xd0)
    //     0x1479864: sub             SP, SP, #0xd0
    // 0x1479868: SetupParameters()
    //     0x1479868: ldr             x0, [fp, #0x10]
    //     0x147986c: ldur            w2, [x0, #0x17]
    //     0x1479870: add             x2, x2, HEAP, lsl #32
    //     0x1479874: stur            x2, [fp, #-8]
    // 0x1479878: CheckStackOverflow
    //     0x1479878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147987c: cmp             SP, x16
    //     0x1479880: b.ls            #0x1479b28
    // 0x1479884: LoadField: r1 = r2->field_f
    //     0x1479884: ldur            w1, [x2, #0xf]
    // 0x1479888: DecompressPointer r1
    //     0x1479888: add             x1, x1, HEAP, lsl #32
    // 0x147988c: r0 = controller()
    //     0x147988c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479890: LoadField: r1 = r0->field_57
    //     0x1479890: ldur            w1, [x0, #0x57]
    // 0x1479894: DecompressPointer r1
    //     0x1479894: add             x1, x1, HEAP, lsl #32
    // 0x1479898: r0 = value()
    //     0x1479898: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147989c: ldur            x2, [fp, #-8]
    // 0x14798a0: stur            x0, [fp, #-0x10]
    // 0x14798a4: LoadField: r1 = r2->field_f
    //     0x14798a4: ldur            w1, [x2, #0xf]
    // 0x14798a8: DecompressPointer r1
    //     0x14798a8: add             x1, x1, HEAP, lsl #32
    // 0x14798ac: r0 = controller()
    //     0x14798ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14798b0: LoadField: r1 = r0->field_57
    //     0x14798b0: ldur            w1, [x0, #0x57]
    // 0x14798b4: DecompressPointer r1
    //     0x14798b4: add             x1, x1, HEAP, lsl #32
    // 0x14798b8: r0 = value()
    //     0x14798b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14798bc: LoadField: r1 = r0->field_b
    //     0x14798bc: ldur            w1, [x0, #0xb]
    // 0x14798c0: DecompressPointer r1
    //     0x14798c0: add             x1, x1, HEAP, lsl #32
    // 0x14798c4: cmp             w1, NULL
    // 0x14798c8: b.ne            #0x14798d4
    // 0x14798cc: r0 = Null
    //     0x14798cc: mov             x0, NULL
    // 0x14798d0: b               #0x14798e8
    // 0x14798d4: LoadField: r0 = r1->field_f
    //     0x14798d4: ldur            w0, [x1, #0xf]
    // 0x14798d8: DecompressPointer r0
    //     0x14798d8: add             x0, x0, HEAP, lsl #32
    // 0x14798dc: LoadField: r1 = r0->field_b
    //     0x14798dc: ldur            w1, [x0, #0xb]
    // 0x14798e0: DecompressPointer r1
    //     0x14798e0: add             x1, x1, HEAP, lsl #32
    // 0x14798e4: mov             x0, x1
    // 0x14798e8: cmp             w0, NULL
    // 0x14798ec: b.ne            #0x14798f8
    // 0x14798f0: r3 = ""
    //     0x14798f0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14798f4: b               #0x14798fc
    // 0x14798f8: mov             x3, x0
    // 0x14798fc: ldur            x2, [fp, #-8]
    // 0x1479900: stur            x3, [fp, #-0x18]
    // 0x1479904: LoadField: r1 = r2->field_f
    //     0x1479904: ldur            w1, [x2, #0xf]
    // 0x1479908: DecompressPointer r1
    //     0x1479908: add             x1, x1, HEAP, lsl #32
    // 0x147990c: r0 = controller()
    //     0x147990c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479910: LoadField: r1 = r0->field_67
    //     0x1479910: ldur            w1, [x0, #0x67]
    // 0x1479914: DecompressPointer r1
    //     0x1479914: add             x1, x1, HEAP, lsl #32
    // 0x1479918: r0 = value()
    //     0x1479918: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147991c: ldur            x2, [fp, #-8]
    // 0x1479920: stur            x0, [fp, #-0x20]
    // 0x1479924: LoadField: r1 = r2->field_f
    //     0x1479924: ldur            w1, [x2, #0xf]
    // 0x1479928: DecompressPointer r1
    //     0x1479928: add             x1, x1, HEAP, lsl #32
    // 0x147992c: r0 = controller()
    //     0x147992c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479930: LoadField: r1 = r0->field_4f
    //     0x1479930: ldur            w1, [x0, #0x4f]
    // 0x1479934: DecompressPointer r1
    //     0x1479934: add             x1, x1, HEAP, lsl #32
    // 0x1479938: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1479938: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x147993c: r0 = toList()
    //     0x147993c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x1479940: ldur            x2, [fp, #-8]
    // 0x1479944: stur            x0, [fp, #-0x28]
    // 0x1479948: LoadField: r1 = r2->field_f
    //     0x1479948: ldur            w1, [x2, #0xf]
    // 0x147994c: DecompressPointer r1
    //     0x147994c: add             x1, x1, HEAP, lsl #32
    // 0x1479950: r0 = controller()
    //     0x1479950: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479954: LoadField: r1 = r0->field_5b
    //     0x1479954: ldur            w1, [x0, #0x5b]
    // 0x1479958: DecompressPointer r1
    //     0x1479958: add             x1, x1, HEAP, lsl #32
    // 0x147995c: r0 = value()
    //     0x147995c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1479960: ldur            x2, [fp, #-8]
    // 0x1479964: r1 = Function '<anonymous closure>':.
    //     0x1479964: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a00] AnonymousClosure: (0x13b3d00), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x1479968: ldr             x1, [x1, #0xa00]
    // 0x147996c: stur            x0, [fp, #-0x30]
    // 0x1479970: r0 = AllocateClosure()
    //     0x1479970: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479974: ldur            x2, [fp, #-8]
    // 0x1479978: r1 = Function '<anonymous closure>':.
    //     0x1479978: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a08] AnonymousClosure: (0x13b3290), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x147997c: ldr             x1, [x1, #0xa08]
    // 0x1479980: stur            x0, [fp, #-0x38]
    // 0x1479984: r0 = AllocateClosure()
    //     0x1479984: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479988: ldur            x2, [fp, #-8]
    // 0x147998c: r1 = Function '<anonymous closure>':.
    //     0x147998c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a10] AnonymousClosure: (0x13b3208), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x1479990: ldr             x1, [x1, #0xa10]
    // 0x1479994: stur            x0, [fp, #-0x40]
    // 0x1479998: r0 = AllocateClosure()
    //     0x1479998: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147999c: ldur            x2, [fp, #-8]
    // 0x14799a0: r1 = Function '<anonymous closure>':.
    //     0x14799a0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a18] AnonymousClosure: (0x13b3058), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14799a4: ldr             x1, [x1, #0xa18]
    // 0x14799a8: stur            x0, [fp, #-0x48]
    // 0x14799ac: r0 = AllocateClosure()
    //     0x14799ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14799b0: ldur            x2, [fp, #-8]
    // 0x14799b4: r1 = Function '<anonymous closure>':.
    //     0x14799b4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a20] AnonymousClosure: (0x13b2fc4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14799b8: ldr             x1, [x1, #0xa20]
    // 0x14799bc: stur            x0, [fp, #-0x50]
    // 0x14799c0: r0 = AllocateClosure()
    //     0x14799c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14799c4: ldur            x2, [fp, #-8]
    // 0x14799c8: r1 = Function '<anonymous closure>':.
    //     0x14799c8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a28] AnonymousClosure: (0x13b2cdc), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14799cc: ldr             x1, [x1, #0xa28]
    // 0x14799d0: stur            x0, [fp, #-0x58]
    // 0x14799d4: r0 = AllocateClosure()
    //     0x14799d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14799d8: ldur            x2, [fp, #-8]
    // 0x14799dc: r1 = Function '<anonymous closure>':.
    //     0x14799dc: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a30] AnonymousClosure: (0x1479d4c), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::body (0x1479530)
    //     0x14799e0: ldr             x1, [x1, #0xa30]
    // 0x14799e4: stur            x0, [fp, #-0x60]
    // 0x14799e8: r0 = AllocateClosure()
    //     0x14799e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14799ec: ldur            x2, [fp, #-8]
    // 0x14799f0: r1 = Function '<anonymous closure>':.
    //     0x14799f0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a38] AnonymousClosure: (0x13b276c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14799f4: ldr             x1, [x1, #0xa38]
    // 0x14799f8: stur            x0, [fp, #-0x68]
    // 0x14799fc: r0 = AllocateClosure()
    //     0x14799fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479a00: ldur            x2, [fp, #-8]
    // 0x1479a04: r1 = Function '<anonymous closure>':.
    //     0x1479a04: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a40] AnonymousClosure: (0x13b0b1c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x1479a08: ldr             x1, [x1, #0xa40]
    // 0x1479a0c: stur            x0, [fp, #-0x70]
    // 0x1479a10: r0 = AllocateClosure()
    //     0x1479a10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479a14: ldur            x2, [fp, #-8]
    // 0x1479a18: r1 = Function '<anonymous closure>':.
    //     0x1479a18: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a48] AnonymousClosure: (0x13b1124), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x1479a1c: ldr             x1, [x1, #0xa48]
    // 0x1479a20: stur            x0, [fp, #-8]
    // 0x1479a24: r0 = AllocateClosure()
    //     0x1479a24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1479a28: stur            x0, [fp, #-0x78]
    // 0x1479a2c: r0 = SearchView()
    //     0x1479a2c: bl              #0x1479d40  ; AllocateSearchViewStub -> SearchView (size=0x48)
    // 0x1479a30: stur            x0, [fp, #-0x80]
    // 0x1479a34: ldur            x16, [fp, #-0x28]
    // 0x1479a38: ldur            lr, [fp, #-0x78]
    // 0x1479a3c: stp             lr, x16, [SP, #0x40]
    // 0x1479a40: ldur            x16, [fp, #-0x48]
    // 0x1479a44: ldur            lr, [fp, #-0x40]
    // 0x1479a48: stp             lr, x16, [SP, #0x30]
    // 0x1479a4c: ldur            x16, [fp, #-0x38]
    // 0x1479a50: ldur            lr, [fp, #-8]
    // 0x1479a54: stp             lr, x16, [SP, #0x20]
    // 0x1479a58: ldur            x16, [fp, #-0x50]
    // 0x1479a5c: ldur            lr, [fp, #-0x68]
    // 0x1479a60: stp             lr, x16, [SP, #0x10]
    // 0x1479a64: ldur            x16, [fp, #-0x30]
    // 0x1479a68: ldur            lr, [fp, #-0x70]
    // 0x1479a6c: stp             lr, x16, [SP]
    // 0x1479a70: mov             x1, x0
    // 0x1479a74: ldur            x2, [fp, #-0x10]
    // 0x1479a78: ldur            x3, [fp, #-0x18]
    // 0x1479a7c: ldur            x5, [fp, #-0x60]
    // 0x1479a80: ldur            x6, [fp, #-0x20]
    // 0x1479a84: ldur            x7, [fp, #-0x58]
    // 0x1479a88: r0 = SearchView()
    //     0x1479a88: bl              #0x1479b30  ; [package:customer_app/app/presentation/views/basic/search/widgets/search_view.dart] SearchView::SearchView
    // 0x1479a8c: r1 = Null
    //     0x1479a8c: mov             x1, NULL
    // 0x1479a90: r2 = 2
    //     0x1479a90: movz            x2, #0x2
    // 0x1479a94: r0 = AllocateArray()
    //     0x1479a94: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1479a98: mov             x2, x0
    // 0x1479a9c: ldur            x0, [fp, #-0x80]
    // 0x1479aa0: stur            x2, [fp, #-8]
    // 0x1479aa4: StoreField: r2->field_f = r0
    //     0x1479aa4: stur            w0, [x2, #0xf]
    // 0x1479aa8: r1 = <Widget>
    //     0x1479aa8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1479aac: r0 = AllocateGrowableArray()
    //     0x1479aac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1479ab0: mov             x1, x0
    // 0x1479ab4: ldur            x0, [fp, #-8]
    // 0x1479ab8: stur            x1, [fp, #-0x10]
    // 0x1479abc: StoreField: r1->field_f = r0
    //     0x1479abc: stur            w0, [x1, #0xf]
    // 0x1479ac0: r0 = 2
    //     0x1479ac0: movz            x0, #0x2
    // 0x1479ac4: StoreField: r1->field_b = r0
    //     0x1479ac4: stur            w0, [x1, #0xb]
    // 0x1479ac8: r0 = Column()
    //     0x1479ac8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1479acc: r1 = Instance_Axis
    //     0x1479acc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1479ad0: StoreField: r0->field_f = r1
    //     0x1479ad0: stur            w1, [x0, #0xf]
    // 0x1479ad4: r1 = Instance_MainAxisAlignment
    //     0x1479ad4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1479ad8: ldr             x1, [x1, #0xa08]
    // 0x1479adc: StoreField: r0->field_13 = r1
    //     0x1479adc: stur            w1, [x0, #0x13]
    // 0x1479ae0: r1 = Instance_MainAxisSize
    //     0x1479ae0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1479ae4: ldr             x1, [x1, #0xa10]
    // 0x1479ae8: ArrayStore: r0[0] = r1  ; List_4
    //     0x1479ae8: stur            w1, [x0, #0x17]
    // 0x1479aec: r1 = Instance_CrossAxisAlignment
    //     0x1479aec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1479af0: ldr             x1, [x1, #0xa18]
    // 0x1479af4: StoreField: r0->field_1b = r1
    //     0x1479af4: stur            w1, [x0, #0x1b]
    // 0x1479af8: r1 = Instance_VerticalDirection
    //     0x1479af8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1479afc: ldr             x1, [x1, #0xa20]
    // 0x1479b00: StoreField: r0->field_23 = r1
    //     0x1479b00: stur            w1, [x0, #0x23]
    // 0x1479b04: r1 = Instance_Clip
    //     0x1479b04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1479b08: ldr             x1, [x1, #0x38]
    // 0x1479b0c: StoreField: r0->field_2b = r1
    //     0x1479b0c: stur            w1, [x0, #0x2b]
    // 0x1479b10: StoreField: r0->field_2f = rZR
    //     0x1479b10: stur            xzr, [x0, #0x2f]
    // 0x1479b14: ldur            x1, [fp, #-0x10]
    // 0x1479b18: StoreField: r0->field_b = r1
    //     0x1479b18: stur            w1, [x0, #0xb]
    // 0x1479b1c: LeaveFrame
    //     0x1479b1c: mov             SP, fp
    //     0x1479b20: ldp             fp, lr, [SP], #0x10
    // 0x1479b24: ret
    //     0x1479b24: ret             
    // 0x1479b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1479b28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479b2c: b               #0x1479884
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x1479d4c, size: 0x180
    // 0x1479d4c: EnterFrame
    //     0x1479d4c: stp             fp, lr, [SP, #-0x10]!
    //     0x1479d50: mov             fp, SP
    // 0x1479d54: AllocStack(0x10)
    //     0x1479d54: sub             SP, SP, #0x10
    // 0x1479d58: SetupParameters()
    //     0x1479d58: ldr             x0, [fp, #0x38]
    //     0x1479d5c: ldur            w1, [x0, #0x17]
    //     0x1479d60: add             x1, x1, HEAP, lsl #32
    // 0x1479d64: CheckStackOverflow
    //     0x1479d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479d68: cmp             SP, x16
    //     0x1479d6c: b.ls            #0x1479ec4
    // 0x1479d70: LoadField: r0 = r1->field_f
    //     0x1479d70: ldur            w0, [x1, #0xf]
    // 0x1479d74: DecompressPointer r0
    //     0x1479d74: add             x0, x0, HEAP, lsl #32
    // 0x1479d78: mov             x1, x0
    // 0x1479d7c: r0 = controller()
    //     0x1479d7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479d80: mov             x3, x0
    // 0x1479d84: ldr             x0, [fp, #0x28]
    // 0x1479d88: r2 = Null
    //     0x1479d88: mov             x2, NULL
    // 0x1479d8c: r1 = Null
    //     0x1479d8c: mov             x1, NULL
    // 0x1479d90: stur            x3, [fp, #-8]
    // 0x1479d94: r4 = 60
    //     0x1479d94: movz            x4, #0x3c
    // 0x1479d98: branchIfSmi(r0, 0x1479da4)
    //     0x1479d98: tbz             w0, #0, #0x1479da4
    // 0x1479d9c: r4 = LoadClassIdInstr(r0)
    //     0x1479d9c: ldur            x4, [x0, #-1]
    //     0x1479da0: ubfx            x4, x4, #0xc, #0x14
    // 0x1479da4: sub             x4, x4, #0x5e
    // 0x1479da8: cmp             x4, #1
    // 0x1479dac: b.ls            #0x1479dc0
    // 0x1479db0: r8 = String
    //     0x1479db0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x1479db4: r3 = Null
    //     0x1479db4: add             x3, PP, #0x44, lsl #12  ; [pp+0x44a50] Null
    //     0x1479db8: ldr             x3, [x3, #0xa50]
    // 0x1479dbc: r0 = String()
    //     0x1479dbc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x1479dc0: ldr             x0, [fp, #0x20]
    // 0x1479dc4: r2 = Null
    //     0x1479dc4: mov             x2, NULL
    // 0x1479dc8: r1 = Null
    //     0x1479dc8: mov             x1, NULL
    // 0x1479dcc: r4 = 60
    //     0x1479dcc: movz            x4, #0x3c
    // 0x1479dd0: branchIfSmi(r0, 0x1479ddc)
    //     0x1479dd0: tbz             w0, #0, #0x1479ddc
    // 0x1479dd4: r4 = LoadClassIdInstr(r0)
    //     0x1479dd4: ldur            x4, [x0, #-1]
    //     0x1479dd8: ubfx            x4, x4, #0xc, #0x14
    // 0x1479ddc: sub             x4, x4, #0x5e
    // 0x1479de0: cmp             x4, #1
    // 0x1479de4: b.ls            #0x1479df8
    // 0x1479de8: r8 = String
    //     0x1479de8: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x1479dec: r3 = Null
    //     0x1479dec: add             x3, PP, #0x44, lsl #12  ; [pp+0x44a60] Null
    //     0x1479df0: ldr             x3, [x3, #0xa60]
    // 0x1479df4: r0 = String()
    //     0x1479df4: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x1479df8: ldr             x0, [fp, #0x18]
    // 0x1479dfc: r2 = Null
    //     0x1479dfc: mov             x2, NULL
    // 0x1479e00: r1 = Null
    //     0x1479e00: mov             x1, NULL
    // 0x1479e04: branchIfSmi(r0, 0x1479e2c)
    //     0x1479e04: tbz             w0, #0, #0x1479e2c
    // 0x1479e08: r4 = LoadClassIdInstr(r0)
    //     0x1479e08: ldur            x4, [x0, #-1]
    //     0x1479e0c: ubfx            x4, x4, #0xc, #0x14
    // 0x1479e10: sub             x4, x4, #0x3c
    // 0x1479e14: cmp             x4, #1
    // 0x1479e18: b.ls            #0x1479e2c
    // 0x1479e1c: r8 = int
    //     0x1479e1c: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x1479e20: r3 = Null
    //     0x1479e20: add             x3, PP, #0x44, lsl #12  ; [pp+0x44a70] Null
    //     0x1479e24: ldr             x3, [x3, #0xa70]
    // 0x1479e28: r0 = int()
    //     0x1479e28: bl              #0x16fc548  ; IsType_int_Stub
    // 0x1479e2c: ldr             x0, [fp, #0x10]
    // 0x1479e30: cmp             w0, NULL
    // 0x1479e34: b.ne            #0x1479e44
    // 0x1479e38: r0 = ProductRating()
    //     0x1479e38: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0x1479e3c: mov             x4, x0
    // 0x1479e40: b               #0x1479e48
    // 0x1479e44: mov             x4, x0
    // 0x1479e48: ldr             x3, [fp, #0x18]
    // 0x1479e4c: mov             x0, x4
    // 0x1479e50: stur            x4, [fp, #-0x10]
    // 0x1479e54: r2 = Null
    //     0x1479e54: mov             x2, NULL
    // 0x1479e58: r1 = Null
    //     0x1479e58: mov             x1, NULL
    // 0x1479e5c: r4 = 60
    //     0x1479e5c: movz            x4, #0x3c
    // 0x1479e60: branchIfSmi(r0, 0x1479e6c)
    //     0x1479e60: tbz             w0, #0, #0x1479e6c
    // 0x1479e64: r4 = LoadClassIdInstr(r0)
    //     0x1479e64: ldur            x4, [x0, #-1]
    //     0x1479e68: ubfx            x4, x4, #0xc, #0x14
    // 0x1479e6c: r17 = 5178
    //     0x1479e6c: movz            x17, #0x143a
    // 0x1479e70: cmp             x4, x17
    // 0x1479e74: b.eq            #0x1479e8c
    // 0x1479e78: r8 = ProductRating
    //     0x1479e78: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee30] Type: ProductRating
    //     0x1479e7c: ldr             x8, [x8, #0xe30]
    // 0x1479e80: r3 = Null
    //     0x1479e80: add             x3, PP, #0x44, lsl #12  ; [pp+0x44a80] Null
    //     0x1479e84: ldr             x3, [x3, #0xa80]
    // 0x1479e88: r0 = DefaultTypeTest()
    //     0x1479e88: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1479e8c: ldr             x0, [fp, #0x18]
    // 0x1479e90: r6 = LoadInt32Instr(r0)
    //     0x1479e90: sbfx            x6, x0, #1, #0x1f
    //     0x1479e94: tbz             w0, #0, #0x1479e9c
    //     0x1479e98: ldur            x6, [x0, #7]
    // 0x1479e9c: ldur            x1, [fp, #-8]
    // 0x1479ea0: ldr             x2, [fp, #0x30]
    // 0x1479ea4: ldr             x3, [fp, #0x28]
    // 0x1479ea8: ldr             x5, [fp, #0x20]
    // 0x1479eac: ldur            x7, [fp, #-0x10]
    // 0x1479eb0: r0 = productViewedEvent()
    //     0x1479eb0: bl              #0x13b2a20  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::productViewedEvent
    // 0x1479eb4: r0 = Null
    //     0x1479eb4: mov             x0, NULL
    // 0x1479eb8: LeaveFrame
    //     0x1479eb8: mov             SP, fp
    //     0x1479ebc: ldp             fp, lr, [SP], #0x10
    // 0x1479ec0: ret
    //     0x1479ec0: ret             
    // 0x1479ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1479ec4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479ec8: b               #0x1479d70
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d7390, size: 0x18c
    // 0x15d7390: EnterFrame
    //     0x15d7390: stp             fp, lr, [SP, #-0x10]!
    //     0x15d7394: mov             fp, SP
    // 0x15d7398: AllocStack(0x28)
    //     0x15d7398: sub             SP, SP, #0x28
    // 0x15d739c: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15d739c: mov             x0, x1
    //     0x15d73a0: stur            x1, [fp, #-8]
    //     0x15d73a4: mov             x1, x2
    //     0x15d73a8: stur            x2, [fp, #-0x10]
    // 0x15d73ac: CheckStackOverflow
    //     0x15d73ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d73b0: cmp             SP, x16
    //     0x15d73b4: b.ls            #0x15d7514
    // 0x15d73b8: r1 = 2
    //     0x15d73b8: movz            x1, #0x2
    // 0x15d73bc: r0 = AllocateContext()
    //     0x15d73bc: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d73c0: mov             x1, x0
    // 0x15d73c4: ldur            x0, [fp, #-8]
    // 0x15d73c8: stur            x1, [fp, #-0x18]
    // 0x15d73cc: StoreField: r1->field_f = r0
    //     0x15d73cc: stur            w0, [x1, #0xf]
    // 0x15d73d0: ldur            x0, [fp, #-0x10]
    // 0x15d73d4: StoreField: r1->field_13 = r0
    //     0x15d73d4: stur            w0, [x1, #0x13]
    // 0x15d73d8: r0 = Obx()
    //     0x15d73d8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d73dc: ldur            x2, [fp, #-0x18]
    // 0x15d73e0: r1 = Function '<anonymous closure>':.
    //     0x15d73e0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a90] AnonymousClosure: (0x15d7564), in [package:customer_app/app/presentation/views/glass/search/search_page.dart] SearchPage::appBar (0x15e5d14)
    //     0x15d73e4: ldr             x1, [x1, #0xa90]
    // 0x15d73e8: stur            x0, [fp, #-8]
    // 0x15d73ec: r0 = AllocateClosure()
    //     0x15d73ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d73f0: mov             x1, x0
    // 0x15d73f4: ldur            x0, [fp, #-8]
    // 0x15d73f8: StoreField: r0->field_b = r1
    //     0x15d73f8: stur            w1, [x0, #0xb]
    // 0x15d73fc: ldur            x1, [fp, #-0x10]
    // 0x15d7400: r0 = of()
    //     0x15d7400: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d7404: LoadField: r1 = r0->field_5b
    //     0x15d7404: ldur            w1, [x0, #0x5b]
    // 0x15d7408: DecompressPointer r1
    //     0x15d7408: add             x1, x1, HEAP, lsl #32
    // 0x15d740c: stur            x1, [fp, #-0x10]
    // 0x15d7410: r0 = ColorFilter()
    //     0x15d7410: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d7414: mov             x1, x0
    // 0x15d7418: ldur            x0, [fp, #-0x10]
    // 0x15d741c: stur            x1, [fp, #-0x20]
    // 0x15d7420: StoreField: r1->field_7 = r0
    //     0x15d7420: stur            w0, [x1, #7]
    // 0x15d7424: r0 = Instance_BlendMode
    //     0x15d7424: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d7428: ldr             x0, [x0, #0xb30]
    // 0x15d742c: StoreField: r1->field_b = r0
    //     0x15d742c: stur            w0, [x1, #0xb]
    // 0x15d7430: r0 = 1
    //     0x15d7430: movz            x0, #0x1
    // 0x15d7434: StoreField: r1->field_13 = r0
    //     0x15d7434: stur            x0, [x1, #0x13]
    // 0x15d7438: r0 = SvgPicture()
    //     0x15d7438: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d743c: stur            x0, [fp, #-0x10]
    // 0x15d7440: ldur            x16, [fp, #-0x20]
    // 0x15d7444: str             x16, [SP]
    // 0x15d7448: mov             x1, x0
    // 0x15d744c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d744c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d7450: ldr             x2, [x2, #0xa40]
    // 0x15d7454: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d7454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d7458: ldr             x4, [x4, #0xa38]
    // 0x15d745c: r0 = SvgPicture.asset()
    //     0x15d745c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d7460: r0 = Align()
    //     0x15d7460: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d7464: mov             x1, x0
    // 0x15d7468: r0 = Instance_Alignment
    //     0x15d7468: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d746c: ldr             x0, [x0, #0xb10]
    // 0x15d7470: stur            x1, [fp, #-0x20]
    // 0x15d7474: StoreField: r1->field_f = r0
    //     0x15d7474: stur            w0, [x1, #0xf]
    // 0x15d7478: r0 = 1.000000
    //     0x15d7478: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d747c: StoreField: r1->field_13 = r0
    //     0x15d747c: stur            w0, [x1, #0x13]
    // 0x15d7480: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d7480: stur            w0, [x1, #0x17]
    // 0x15d7484: ldur            x0, [fp, #-0x10]
    // 0x15d7488: StoreField: r1->field_b = r0
    //     0x15d7488: stur            w0, [x1, #0xb]
    // 0x15d748c: r0 = InkWell()
    //     0x15d748c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d7490: mov             x3, x0
    // 0x15d7494: ldur            x0, [fp, #-0x20]
    // 0x15d7498: stur            x3, [fp, #-0x10]
    // 0x15d749c: StoreField: r3->field_b = r0
    //     0x15d749c: stur            w0, [x3, #0xb]
    // 0x15d74a0: ldur            x2, [fp, #-0x18]
    // 0x15d74a4: r1 = Function '<anonymous closure>':.
    //     0x15d74a4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a98] AnonymousClosure: (0x15d751c), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::appBar (0x15d7390)
    //     0x15d74a8: ldr             x1, [x1, #0xa98]
    // 0x15d74ac: r0 = AllocateClosure()
    //     0x15d74ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d74b0: ldur            x2, [fp, #-0x10]
    // 0x15d74b4: StoreField: r2->field_f = r0
    //     0x15d74b4: stur            w0, [x2, #0xf]
    // 0x15d74b8: r0 = true
    //     0x15d74b8: add             x0, NULL, #0x20  ; true
    // 0x15d74bc: StoreField: r2->field_43 = r0
    //     0x15d74bc: stur            w0, [x2, #0x43]
    // 0x15d74c0: r1 = Instance_BoxShape
    //     0x15d74c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d74c4: ldr             x1, [x1, #0x80]
    // 0x15d74c8: StoreField: r2->field_47 = r1
    //     0x15d74c8: stur            w1, [x2, #0x47]
    // 0x15d74cc: StoreField: r2->field_6f = r0
    //     0x15d74cc: stur            w0, [x2, #0x6f]
    // 0x15d74d0: r1 = false
    //     0x15d74d0: add             x1, NULL, #0x30  ; false
    // 0x15d74d4: StoreField: r2->field_73 = r1
    //     0x15d74d4: stur            w1, [x2, #0x73]
    // 0x15d74d8: StoreField: r2->field_83 = r0
    //     0x15d74d8: stur            w0, [x2, #0x83]
    // 0x15d74dc: StoreField: r2->field_7b = r1
    //     0x15d74dc: stur            w1, [x2, #0x7b]
    // 0x15d74e0: r0 = AppBar()
    //     0x15d74e0: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d74e4: stur            x0, [fp, #-0x18]
    // 0x15d74e8: ldur            x16, [fp, #-8]
    // 0x15d74ec: str             x16, [SP]
    // 0x15d74f0: mov             x1, x0
    // 0x15d74f4: ldur            x2, [fp, #-0x10]
    // 0x15d74f8: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15d74f8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15d74fc: ldr             x4, [x4, #0xf00]
    // 0x15d7500: r0 = AppBar()
    //     0x15d7500: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d7504: ldur            x0, [fp, #-0x18]
    // 0x15d7508: LeaveFrame
    //     0x15d7508: mov             SP, fp
    //     0x15d750c: ldp             fp, lr, [SP], #0x10
    // 0x15d7510: ret
    //     0x15d7510: ret             
    // 0x15d7514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d7514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d7518: b               #0x15d73b8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d751c, size: 0x48
    // 0x15d751c: EnterFrame
    //     0x15d751c: stp             fp, lr, [SP, #-0x10]!
    //     0x15d7520: mov             fp, SP
    // 0x15d7524: ldr             x0, [fp, #0x10]
    // 0x15d7528: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15d7528: ldur            w1, [x0, #0x17]
    // 0x15d752c: DecompressPointer r1
    //     0x15d752c: add             x1, x1, HEAP, lsl #32
    // 0x15d7530: CheckStackOverflow
    //     0x15d7530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d7534: cmp             SP, x16
    //     0x15d7538: b.ls            #0x15d755c
    // 0x15d753c: LoadField: r0 = r1->field_f
    //     0x15d753c: ldur            w0, [x1, #0xf]
    // 0x15d7540: DecompressPointer r0
    //     0x15d7540: add             x0, x0, HEAP, lsl #32
    // 0x15d7544: mov             x1, x0
    // 0x15d7548: r0 = onBackPress()
    //     0x15d7548: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x15d754c: r0 = Null
    //     0x15d754c: mov             x0, NULL
    // 0x15d7550: LeaveFrame
    //     0x15d7550: mov             SP, fp
    //     0x15d7554: ldp             fp, lr, [SP], #0x10
    // 0x15d7558: ret
    //     0x15d7558: ret             
    // 0x15d755c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d755c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d7560: b               #0x15d753c
  }
}
