// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart

// class id: 1049227, size: 0x8
class :: {
}

// class id: 3465, size: 0x14, field offset: 0x14
class _OffersContentItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xab8e64, size: 0x2b8
    // 0xab8e64: EnterFrame
    //     0xab8e64: stp             fp, lr, [SP, #-0x10]!
    //     0xab8e68: mov             fp, SP
    // 0xab8e6c: AllocStack(0x40)
    //     0xab8e6c: sub             SP, SP, #0x40
    // 0xab8e70: SetupParameters(_OffersContentItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xab8e70: mov             x0, x1
    //     0xab8e74: stur            x1, [fp, #-8]
    //     0xab8e78: mov             x1, x2
    //     0xab8e7c: stur            x2, [fp, #-0x10]
    // 0xab8e80: CheckStackOverflow
    //     0xab8e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab8e84: cmp             SP, x16
    //     0xab8e88: b.ls            #0xab910c
    // 0xab8e8c: r1 = 1
    //     0xab8e8c: movz            x1, #0x1
    // 0xab8e90: r0 = AllocateContext()
    //     0xab8e90: bl              #0x16f6108  ; AllocateContextStub
    // 0xab8e94: mov             x2, x0
    // 0xab8e98: ldur            x0, [fp, #-8]
    // 0xab8e9c: stur            x2, [fp, #-0x28]
    // 0xab8ea0: StoreField: r2->field_f = r0
    //     0xab8ea0: stur            w0, [x2, #0xf]
    // 0xab8ea4: LoadField: r1 = r0->field_b
    //     0xab8ea4: ldur            w1, [x0, #0xb]
    // 0xab8ea8: DecompressPointer r1
    //     0xab8ea8: add             x1, x1, HEAP, lsl #32
    // 0xab8eac: cmp             w1, NULL
    // 0xab8eb0: b.eq            #0xab9114
    // 0xab8eb4: LoadField: r3 = r1->field_f
    //     0xab8eb4: ldur            w3, [x1, #0xf]
    // 0xab8eb8: DecompressPointer r3
    //     0xab8eb8: add             x3, x3, HEAP, lsl #32
    // 0xab8ebc: cmp             w3, NULL
    // 0xab8ec0: b.ne            #0xab8ecc
    // 0xab8ec4: r1 = Null
    //     0xab8ec4: mov             x1, NULL
    // 0xab8ec8: b               #0xab8ee8
    // 0xab8ecc: LoadField: r1 = r3->field_f
    //     0xab8ecc: ldur            w1, [x3, #0xf]
    // 0xab8ed0: DecompressPointer r1
    //     0xab8ed0: add             x1, x1, HEAP, lsl #32
    // 0xab8ed4: LoadField: r4 = r1->field_b
    //     0xab8ed4: ldur            w4, [x1, #0xb]
    // 0xab8ed8: cbnz            w4, #0xab8ee4
    // 0xab8edc: r1 = false
    //     0xab8edc: add             x1, NULL, #0x30  ; false
    // 0xab8ee0: b               #0xab8ee8
    // 0xab8ee4: r1 = true
    //     0xab8ee4: add             x1, NULL, #0x20  ; true
    // 0xab8ee8: cmp             w1, NULL
    // 0xab8eec: b.ne            #0xab8ef8
    // 0xab8ef0: r4 = false
    //     0xab8ef0: add             x4, NULL, #0x30  ; false
    // 0xab8ef4: b               #0xab8efc
    // 0xab8ef8: mov             x4, x1
    // 0xab8efc: stur            x4, [fp, #-0x20]
    // 0xab8f00: cmp             w3, NULL
    // 0xab8f04: b.ne            #0xab8f10
    // 0xab8f08: r1 = Null
    //     0xab8f08: mov             x1, NULL
    // 0xab8f0c: b               #0xab8f18
    // 0xab8f10: LoadField: r1 = r3->field_b
    //     0xab8f10: ldur            w1, [x3, #0xb]
    // 0xab8f14: DecompressPointer r1
    //     0xab8f14: add             x1, x1, HEAP, lsl #32
    // 0xab8f18: cmp             w1, NULL
    // 0xab8f1c: b.ne            #0xab8f28
    // 0xab8f20: r3 = ""
    //     0xab8f20: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab8f24: b               #0xab8f2c
    // 0xab8f28: mov             x3, x1
    // 0xab8f2c: ldur            x1, [fp, #-0x10]
    // 0xab8f30: stur            x3, [fp, #-0x18]
    // 0xab8f34: r0 = of()
    //     0xab8f34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab8f38: LoadField: r1 = r0->field_87
    //     0xab8f38: ldur            w1, [x0, #0x87]
    // 0xab8f3c: DecompressPointer r1
    //     0xab8f3c: add             x1, x1, HEAP, lsl #32
    // 0xab8f40: LoadField: r0 = r1->field_2b
    //     0xab8f40: ldur            w0, [x1, #0x2b]
    // 0xab8f44: DecompressPointer r0
    //     0xab8f44: add             x0, x0, HEAP, lsl #32
    // 0xab8f48: r16 = 16.000000
    //     0xab8f48: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab8f4c: ldr             x16, [x16, #0x188]
    // 0xab8f50: r30 = Instance_Color
    //     0xab8f50: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab8f54: stp             lr, x16, [SP]
    // 0xab8f58: mov             x1, x0
    // 0xab8f5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab8f5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab8f60: ldr             x4, [x4, #0xaa0]
    // 0xab8f64: r0 = copyWith()
    //     0xab8f64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab8f68: stur            x0, [fp, #-0x10]
    // 0xab8f6c: r0 = Text()
    //     0xab8f6c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab8f70: mov             x1, x0
    // 0xab8f74: ldur            x0, [fp, #-0x18]
    // 0xab8f78: stur            x1, [fp, #-0x30]
    // 0xab8f7c: StoreField: r1->field_b = r0
    //     0xab8f7c: stur            w0, [x1, #0xb]
    // 0xab8f80: ldur            x0, [fp, #-0x10]
    // 0xab8f84: StoreField: r1->field_13 = r0
    //     0xab8f84: stur            w0, [x1, #0x13]
    // 0xab8f88: r0 = Padding()
    //     0xab8f88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab8f8c: mov             x1, x0
    // 0xab8f90: r0 = Instance_EdgeInsets
    //     0xab8f90: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xab8f94: ldr             x0, [x0, #0xf70]
    // 0xab8f98: stur            x1, [fp, #-0x10]
    // 0xab8f9c: StoreField: r1->field_f = r0
    //     0xab8f9c: stur            w0, [x1, #0xf]
    // 0xab8fa0: ldur            x0, [fp, #-0x30]
    // 0xab8fa4: StoreField: r1->field_b = r0
    //     0xab8fa4: stur            w0, [x1, #0xb]
    // 0xab8fa8: r0 = Visibility()
    //     0xab8fa8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xab8fac: mov             x3, x0
    // 0xab8fb0: ldur            x0, [fp, #-0x10]
    // 0xab8fb4: stur            x3, [fp, #-0x18]
    // 0xab8fb8: StoreField: r3->field_b = r0
    //     0xab8fb8: stur            w0, [x3, #0xb]
    // 0xab8fbc: r0 = Instance_SizedBox
    //     0xab8fbc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xab8fc0: StoreField: r3->field_f = r0
    //     0xab8fc0: stur            w0, [x3, #0xf]
    // 0xab8fc4: ldur            x0, [fp, #-0x20]
    // 0xab8fc8: StoreField: r3->field_13 = r0
    //     0xab8fc8: stur            w0, [x3, #0x13]
    // 0xab8fcc: r0 = false
    //     0xab8fcc: add             x0, NULL, #0x30  ; false
    // 0xab8fd0: ArrayStore: r3[0] = r0  ; List_4
    //     0xab8fd0: stur            w0, [x3, #0x17]
    // 0xab8fd4: StoreField: r3->field_1b = r0
    //     0xab8fd4: stur            w0, [x3, #0x1b]
    // 0xab8fd8: StoreField: r3->field_1f = r0
    //     0xab8fd8: stur            w0, [x3, #0x1f]
    // 0xab8fdc: StoreField: r3->field_23 = r0
    //     0xab8fdc: stur            w0, [x3, #0x23]
    // 0xab8fe0: StoreField: r3->field_27 = r0
    //     0xab8fe0: stur            w0, [x3, #0x27]
    // 0xab8fe4: StoreField: r3->field_2b = r0
    //     0xab8fe4: stur            w0, [x3, #0x2b]
    // 0xab8fe8: ldur            x0, [fp, #-8]
    // 0xab8fec: LoadField: r1 = r0->field_b
    //     0xab8fec: ldur            w1, [x0, #0xb]
    // 0xab8ff0: DecompressPointer r1
    //     0xab8ff0: add             x1, x1, HEAP, lsl #32
    // 0xab8ff4: cmp             w1, NULL
    // 0xab8ff8: b.eq            #0xab9118
    // 0xab8ffc: LoadField: r0 = r1->field_f
    //     0xab8ffc: ldur            w0, [x1, #0xf]
    // 0xab9000: DecompressPointer r0
    //     0xab9000: add             x0, x0, HEAP, lsl #32
    // 0xab9004: cmp             w0, NULL
    // 0xab9008: b.ne            #0xab9014
    // 0xab900c: r0 = Null
    //     0xab900c: mov             x0, NULL
    // 0xab9010: b               #0xab9020
    // 0xab9014: LoadField: r1 = r0->field_f
    //     0xab9014: ldur            w1, [x0, #0xf]
    // 0xab9018: DecompressPointer r1
    //     0xab9018: add             x1, x1, HEAP, lsl #32
    // 0xab901c: LoadField: r0 = r1->field_b
    //     0xab901c: ldur            w0, [x1, #0xb]
    // 0xab9020: ldur            x2, [fp, #-0x28]
    // 0xab9024: stur            x0, [fp, #-8]
    // 0xab9028: r1 = Function '<anonymous closure>':.
    //     0xab9028: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6af70] AnonymousClosure: (0xab9140), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xab902c: ldr             x1, [x1, #0xf70]
    // 0xab9030: r0 = AllocateClosure()
    //     0xab9030: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab9034: stur            x0, [fp, #-0x10]
    // 0xab9038: r0 = ListView()
    //     0xab9038: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xab903c: stur            x0, [fp, #-0x20]
    // 0xab9040: r16 = true
    //     0xab9040: add             x16, NULL, #0x20  ; true
    // 0xab9044: r30 = Instance_NeverScrollableScrollPhysics
    //     0xab9044: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xab9048: ldr             lr, [lr, #0x1c8]
    // 0xab904c: stp             lr, x16, [SP]
    // 0xab9050: mov             x1, x0
    // 0xab9054: ldur            x2, [fp, #-0x10]
    // 0xab9058: ldur            x3, [fp, #-8]
    // 0xab905c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xab905c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xab9060: ldr             x4, [x4, #8]
    // 0xab9064: r0 = ListView.builder()
    //     0xab9064: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xab9068: r1 = Null
    //     0xab9068: mov             x1, NULL
    // 0xab906c: r2 = 4
    //     0xab906c: movz            x2, #0x4
    // 0xab9070: r0 = AllocateArray()
    //     0xab9070: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab9074: mov             x2, x0
    // 0xab9078: ldur            x0, [fp, #-0x18]
    // 0xab907c: stur            x2, [fp, #-8]
    // 0xab9080: StoreField: r2->field_f = r0
    //     0xab9080: stur            w0, [x2, #0xf]
    // 0xab9084: ldur            x0, [fp, #-0x20]
    // 0xab9088: StoreField: r2->field_13 = r0
    //     0xab9088: stur            w0, [x2, #0x13]
    // 0xab908c: r1 = <Widget>
    //     0xab908c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab9090: r0 = AllocateGrowableArray()
    //     0xab9090: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab9094: mov             x1, x0
    // 0xab9098: ldur            x0, [fp, #-8]
    // 0xab909c: stur            x1, [fp, #-0x10]
    // 0xab90a0: StoreField: r1->field_f = r0
    //     0xab90a0: stur            w0, [x1, #0xf]
    // 0xab90a4: r0 = 4
    //     0xab90a4: movz            x0, #0x4
    // 0xab90a8: StoreField: r1->field_b = r0
    //     0xab90a8: stur            w0, [x1, #0xb]
    // 0xab90ac: r0 = Column()
    //     0xab90ac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xab90b0: r1 = Instance_Axis
    //     0xab90b0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xab90b4: StoreField: r0->field_f = r1
    //     0xab90b4: stur            w1, [x0, #0xf]
    // 0xab90b8: r1 = Instance_MainAxisAlignment
    //     0xab90b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab90bc: ldr             x1, [x1, #0xa08]
    // 0xab90c0: StoreField: r0->field_13 = r1
    //     0xab90c0: stur            w1, [x0, #0x13]
    // 0xab90c4: r1 = Instance_MainAxisSize
    //     0xab90c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab90c8: ldr             x1, [x1, #0xa10]
    // 0xab90cc: ArrayStore: r0[0] = r1  ; List_4
    //     0xab90cc: stur            w1, [x0, #0x17]
    // 0xab90d0: r1 = Instance_CrossAxisAlignment
    //     0xab90d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xab90d4: ldr             x1, [x1, #0x890]
    // 0xab90d8: StoreField: r0->field_1b = r1
    //     0xab90d8: stur            w1, [x0, #0x1b]
    // 0xab90dc: r1 = Instance_VerticalDirection
    //     0xab90dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab90e0: ldr             x1, [x1, #0xa20]
    // 0xab90e4: StoreField: r0->field_23 = r1
    //     0xab90e4: stur            w1, [x0, #0x23]
    // 0xab90e8: r1 = Instance_Clip
    //     0xab90e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab90ec: ldr             x1, [x1, #0x38]
    // 0xab90f0: StoreField: r0->field_2b = r1
    //     0xab90f0: stur            w1, [x0, #0x2b]
    // 0xab90f4: StoreField: r0->field_2f = rZR
    //     0xab90f4: stur            xzr, [x0, #0x2f]
    // 0xab90f8: ldur            x1, [fp, #-0x10]
    // 0xab90fc: StoreField: r0->field_b = r1
    //     0xab90fc: stur            w1, [x0, #0xb]
    // 0xab9100: LeaveFrame
    //     0xab9100: mov             SP, fp
    //     0xab9104: ldp             fp, lr, [SP], #0x10
    // 0xab9108: ret
    //     0xab9108: ret             
    // 0xab910c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab910c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab9110: b               #0xab8e8c
    // 0xab9114: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab9114: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab9118: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab9118: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xab9140, size: 0x2174
    // 0xab9140: EnterFrame
    //     0xab9140: stp             fp, lr, [SP, #-0x10]!
    //     0xab9144: mov             fp, SP
    // 0xab9148: AllocStack(0x80)
    //     0xab9148: sub             SP, SP, #0x80
    // 0xab914c: SetupParameters()
    //     0xab914c: ldr             x0, [fp, #0x20]
    //     0xab9150: ldur            w1, [x0, #0x17]
    //     0xab9154: add             x1, x1, HEAP, lsl #32
    //     0xab9158: stur            x1, [fp, #-8]
    // 0xab915c: CheckStackOverflow
    //     0xab915c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab9160: cmp             SP, x16
    //     0xab9164: b.ls            #0xabb1e0
    // 0xab9168: r1 = 2
    //     0xab9168: movz            x1, #0x2
    // 0xab916c: r0 = AllocateContext()
    //     0xab916c: bl              #0x16f6108  ; AllocateContextStub
    // 0xab9170: mov             x3, x0
    // 0xab9174: ldur            x2, [fp, #-8]
    // 0xab9178: stur            x3, [fp, #-0x10]
    // 0xab917c: StoreField: r3->field_b = r2
    //     0xab917c: stur            w2, [x3, #0xb]
    // 0xab9180: ldr             x0, [fp, #0x18]
    // 0xab9184: StoreField: r3->field_f = r0
    //     0xab9184: stur            w0, [x3, #0xf]
    // 0xab9188: ldr             x0, [fp, #0x10]
    // 0xab918c: StoreField: r3->field_13 = r0
    //     0xab918c: stur            w0, [x3, #0x13]
    // 0xab9190: LoadField: r1 = r2->field_f
    //     0xab9190: ldur            w1, [x2, #0xf]
    // 0xab9194: DecompressPointer r1
    //     0xab9194: add             x1, x1, HEAP, lsl #32
    // 0xab9198: LoadField: r4 = r1->field_b
    //     0xab9198: ldur            w4, [x1, #0xb]
    // 0xab919c: DecompressPointer r4
    //     0xab919c: add             x4, x4, HEAP, lsl #32
    // 0xab91a0: cmp             w4, NULL
    // 0xab91a4: b.eq            #0xabb1e8
    // 0xab91a8: LoadField: r1 = r4->field_f
    //     0xab91a8: ldur            w1, [x4, #0xf]
    // 0xab91ac: DecompressPointer r1
    //     0xab91ac: add             x1, x1, HEAP, lsl #32
    // 0xab91b0: cmp             w1, NULL
    // 0xab91b4: b.ne            #0xab91c0
    // 0xab91b8: r0 = Null
    //     0xab91b8: mov             x0, NULL
    // 0xab91bc: b               #0xab9204
    // 0xab91c0: LoadField: r4 = r1->field_f
    //     0xab91c0: ldur            w4, [x1, #0xf]
    // 0xab91c4: DecompressPointer r4
    //     0xab91c4: add             x4, x4, HEAP, lsl #32
    // 0xab91c8: LoadField: r1 = r4->field_b
    //     0xab91c8: ldur            w1, [x4, #0xb]
    // 0xab91cc: r5 = LoadInt32Instr(r0)
    //     0xab91cc: sbfx            x5, x0, #1, #0x1f
    //     0xab91d0: tbz             w0, #0, #0xab91d8
    //     0xab91d4: ldur            x5, [x0, #7]
    // 0xab91d8: r0 = LoadInt32Instr(r1)
    //     0xab91d8: sbfx            x0, x1, #1, #0x1f
    // 0xab91dc: mov             x1, x5
    // 0xab91e0: cmp             x1, x0
    // 0xab91e4: b.hs            #0xabb1ec
    // 0xab91e8: LoadField: r0 = r4->field_f
    //     0xab91e8: ldur            w0, [x4, #0xf]
    // 0xab91ec: DecompressPointer r0
    //     0xab91ec: add             x0, x0, HEAP, lsl #32
    // 0xab91f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab91f0: add             x16, x0, x5, lsl #2
    //     0xab91f4: ldur            w1, [x16, #0xf]
    // 0xab91f8: DecompressPointer r1
    //     0xab91f8: add             x1, x1, HEAP, lsl #32
    // 0xab91fc: LoadField: r0 = r1->field_b
    //     0xab91fc: ldur            w0, [x1, #0xb]
    // 0xab9200: DecompressPointer r0
    //     0xab9200: add             x0, x0, HEAP, lsl #32
    // 0xab9204: r1 = LoadClassIdInstr(r0)
    //     0xab9204: ldur            x1, [x0, #-1]
    //     0xab9208: ubfx            x1, x1, #0xc, #0x14
    // 0xab920c: r16 = "bumper_coupon"
    //     0xab920c: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xab9210: ldr             x16, [x16, #0x8d8]
    // 0xab9214: stp             x16, x0, [SP]
    // 0xab9218: mov             x0, x1
    // 0xab921c: mov             lr, x0
    // 0xab9220: ldr             lr, [x21, lr, lsl #3]
    // 0xab9224: blr             lr
    // 0xab9228: tbnz            w0, #4, #0xaba1d0
    // 0xab922c: ldur            x1, [fp, #-8]
    // 0xab9230: LoadField: r0 = r1->field_f
    //     0xab9230: ldur            w0, [x1, #0xf]
    // 0xab9234: DecompressPointer r0
    //     0xab9234: add             x0, x0, HEAP, lsl #32
    // 0xab9238: LoadField: r2 = r0->field_b
    //     0xab9238: ldur            w2, [x0, #0xb]
    // 0xab923c: DecompressPointer r2
    //     0xab923c: add             x2, x2, HEAP, lsl #32
    // 0xab9240: cmp             w2, NULL
    // 0xab9244: b.eq            #0xabb1f0
    // 0xab9248: LoadField: r0 = r2->field_b
    //     0xab9248: ldur            w0, [x2, #0xb]
    // 0xab924c: DecompressPointer r0
    //     0xab924c: add             x0, x0, HEAP, lsl #32
    // 0xab9250: r2 = LoadClassIdInstr(r0)
    //     0xab9250: ldur            x2, [x0, #-1]
    //     0xab9254: ubfx            x2, x2, #0xc, #0x14
    // 0xab9258: r16 = "other_offers"
    //     0xab9258: add             x16, PP, #0x57, lsl #12  ; [pp+0x571d0] "other_offers"
    //     0xab925c: ldr             x16, [x16, #0x1d0]
    // 0xab9260: stp             x16, x0, [SP]
    // 0xab9264: mov             x0, x2
    // 0xab9268: mov             lr, x0
    // 0xab926c: ldr             lr, [x21, lr, lsl #3]
    // 0xab9270: blr             lr
    // 0xab9274: tbz             w0, #4, #0xaba148
    // 0xab9278: ldur            x0, [fp, #-8]
    // 0xab927c: LoadField: r1 = r0->field_f
    //     0xab927c: ldur            w1, [x0, #0xf]
    // 0xab9280: DecompressPointer r1
    //     0xab9280: add             x1, x1, HEAP, lsl #32
    // 0xab9284: LoadField: r2 = r1->field_b
    //     0xab9284: ldur            w2, [x1, #0xb]
    // 0xab9288: DecompressPointer r2
    //     0xab9288: add             x2, x2, HEAP, lsl #32
    // 0xab928c: stur            x2, [fp, #-0x30]
    // 0xab9290: cmp             w2, NULL
    // 0xab9294: b.eq            #0xabb1f4
    // 0xab9298: LoadField: r1 = r2->field_23
    //     0xab9298: ldur            w1, [x2, #0x23]
    // 0xab929c: DecompressPointer r1
    //     0xab929c: add             x1, x1, HEAP, lsl #32
    // 0xab92a0: LoadField: r3 = r1->field_13
    //     0xab92a0: ldur            w3, [x1, #0x13]
    // 0xab92a4: DecompressPointer r3
    //     0xab92a4: add             x3, x3, HEAP, lsl #32
    // 0xab92a8: cmp             w3, NULL
    // 0xab92ac: b.ne            #0xab92b8
    // 0xab92b0: r1 = Null
    //     0xab92b0: mov             x1, NULL
    // 0xab92b4: b               #0xab92c0
    // 0xab92b8: LoadField: r1 = r3->field_7
    //     0xab92b8: ldur            w1, [x3, #7]
    // 0xab92bc: DecompressPointer r1
    //     0xab92bc: add             x1, x1, HEAP, lsl #32
    // 0xab92c0: cmp             w1, NULL
    // 0xab92c4: b.ne            #0xab92d0
    // 0xab92c8: r1 = 0
    //     0xab92c8: movz            x1, #0
    // 0xab92cc: b               #0xab92e0
    // 0xab92d0: r3 = LoadInt32Instr(r1)
    //     0xab92d0: sbfx            x3, x1, #1, #0x1f
    //     0xab92d4: tbz             w1, #0, #0xab92dc
    //     0xab92d8: ldur            x3, [x1, #7]
    // 0xab92dc: mov             x1, x3
    // 0xab92e0: stur            x1, [fp, #-0x28]
    // 0xab92e4: LoadField: r3 = r2->field_23
    //     0xab92e4: ldur            w3, [x2, #0x23]
    // 0xab92e8: DecompressPointer r3
    //     0xab92e8: add             x3, x3, HEAP, lsl #32
    // 0xab92ec: LoadField: r4 = r3->field_13
    //     0xab92ec: ldur            w4, [x3, #0x13]
    // 0xab92f0: DecompressPointer r4
    //     0xab92f0: add             x4, x4, HEAP, lsl #32
    // 0xab92f4: cmp             w4, NULL
    // 0xab92f8: b.ne            #0xab9304
    // 0xab92fc: r3 = Null
    //     0xab92fc: mov             x3, NULL
    // 0xab9300: b               #0xab930c
    // 0xab9304: LoadField: r3 = r4->field_b
    //     0xab9304: ldur            w3, [x4, #0xb]
    // 0xab9308: DecompressPointer r3
    //     0xab9308: add             x3, x3, HEAP, lsl #32
    // 0xab930c: cmp             w3, NULL
    // 0xab9310: b.ne            #0xab931c
    // 0xab9314: r3 = 0
    //     0xab9314: movz            x3, #0
    // 0xab9318: b               #0xab932c
    // 0xab931c: r4 = LoadInt32Instr(r3)
    //     0xab931c: sbfx            x4, x3, #1, #0x1f
    //     0xab9320: tbz             w3, #0, #0xab9328
    //     0xab9324: ldur            x4, [x3, #7]
    // 0xab9328: mov             x3, x4
    // 0xab932c: stur            x3, [fp, #-0x20]
    // 0xab9330: LoadField: r4 = r2->field_23
    //     0xab9330: ldur            w4, [x2, #0x23]
    // 0xab9334: DecompressPointer r4
    //     0xab9334: add             x4, x4, HEAP, lsl #32
    // 0xab9338: LoadField: r5 = r4->field_13
    //     0xab9338: ldur            w5, [x4, #0x13]
    // 0xab933c: DecompressPointer r5
    //     0xab933c: add             x5, x5, HEAP, lsl #32
    // 0xab9340: cmp             w5, NULL
    // 0xab9344: b.ne            #0xab9350
    // 0xab9348: r4 = Null
    //     0xab9348: mov             x4, NULL
    // 0xab934c: b               #0xab9358
    // 0xab9350: LoadField: r4 = r5->field_f
    //     0xab9350: ldur            w4, [x5, #0xf]
    // 0xab9354: DecompressPointer r4
    //     0xab9354: add             x4, x4, HEAP, lsl #32
    // 0xab9358: cmp             w4, NULL
    // 0xab935c: b.ne            #0xab9368
    // 0xab9360: r4 = 0
    //     0xab9360: movz            x4, #0
    // 0xab9364: b               #0xab9378
    // 0xab9368: r5 = LoadInt32Instr(r4)
    //     0xab9368: sbfx            x5, x4, #1, #0x1f
    //     0xab936c: tbz             w4, #0, #0xab9374
    //     0xab9370: ldur            x5, [x4, #7]
    // 0xab9374: mov             x4, x5
    // 0xab9378: stur            x4, [fp, #-0x18]
    // 0xab937c: r0 = Color()
    //     0xab937c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xab9380: mov             x1, x0
    // 0xab9384: r0 = Instance_ColorSpace
    //     0xab9384: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xab9388: stur            x1, [fp, #-0x38]
    // 0xab938c: StoreField: r1->field_27 = r0
    //     0xab938c: stur            w0, [x1, #0x27]
    // 0xab9390: d1 = 1.000000
    //     0xab9390: fmov            d1, #1.00000000
    // 0xab9394: StoreField: r1->field_7 = d1
    //     0xab9394: stur            d1, [x1, #7]
    // 0xab9398: ldur            x2, [fp, #-0x28]
    // 0xab939c: ubfx            x2, x2, #0, #0x20
    // 0xab93a0: and             w3, w2, #0xff
    // 0xab93a4: ubfx            x3, x3, #0, #0x20
    // 0xab93a8: scvtf           d0, x3
    // 0xab93ac: d1 = 255.000000
    //     0xab93ac: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xab93b0: fdiv            d2, d0, d1
    // 0xab93b4: StoreField: r1->field_f = d2
    //     0xab93b4: stur            d2, [x1, #0xf]
    // 0xab93b8: ldur            x2, [fp, #-0x20]
    // 0xab93bc: ubfx            x2, x2, #0, #0x20
    // 0xab93c0: and             w3, w2, #0xff
    // 0xab93c4: ubfx            x3, x3, #0, #0x20
    // 0xab93c8: scvtf           d0, x3
    // 0xab93cc: fdiv            d2, d0, d1
    // 0xab93d0: ArrayStore: r1[0] = d2  ; List_8
    //     0xab93d0: stur            d2, [x1, #0x17]
    // 0xab93d4: ldur            x2, [fp, #-0x18]
    // 0xab93d8: ubfx            x2, x2, #0, #0x20
    // 0xab93dc: and             w3, w2, #0xff
    // 0xab93e0: ubfx            x3, x3, #0, #0x20
    // 0xab93e4: scvtf           d0, x3
    // 0xab93e8: fdiv            d2, d0, d1
    // 0xab93ec: StoreField: r1->field_1f = d2
    //     0xab93ec: stur            d2, [x1, #0x1f]
    // 0xab93f0: ldur            x2, [fp, #-0x30]
    // 0xab93f4: LoadField: r3 = r2->field_23
    //     0xab93f4: ldur            w3, [x2, #0x23]
    // 0xab93f8: DecompressPointer r3
    //     0xab93f8: add             x3, x3, HEAP, lsl #32
    // 0xab93fc: LoadField: r4 = r3->field_13
    //     0xab93fc: ldur            w4, [x3, #0x13]
    // 0xab9400: DecompressPointer r4
    //     0xab9400: add             x4, x4, HEAP, lsl #32
    // 0xab9404: cmp             w4, NULL
    // 0xab9408: b.ne            #0xab9414
    // 0xab940c: r3 = Null
    //     0xab940c: mov             x3, NULL
    // 0xab9410: b               #0xab941c
    // 0xab9414: LoadField: r3 = r4->field_7
    //     0xab9414: ldur            w3, [x4, #7]
    // 0xab9418: DecompressPointer r3
    //     0xab9418: add             x3, x3, HEAP, lsl #32
    // 0xab941c: cmp             w3, NULL
    // 0xab9420: b.ne            #0xab942c
    // 0xab9424: r3 = 0
    //     0xab9424: movz            x3, #0
    // 0xab9428: b               #0xab943c
    // 0xab942c: r4 = LoadInt32Instr(r3)
    //     0xab942c: sbfx            x4, x3, #1, #0x1f
    //     0xab9430: tbz             w3, #0, #0xab9438
    //     0xab9434: ldur            x4, [x3, #7]
    // 0xab9438: mov             x3, x4
    // 0xab943c: stur            x3, [fp, #-0x28]
    // 0xab9440: LoadField: r4 = r2->field_23
    //     0xab9440: ldur            w4, [x2, #0x23]
    // 0xab9444: DecompressPointer r4
    //     0xab9444: add             x4, x4, HEAP, lsl #32
    // 0xab9448: LoadField: r5 = r4->field_13
    //     0xab9448: ldur            w5, [x4, #0x13]
    // 0xab944c: DecompressPointer r5
    //     0xab944c: add             x5, x5, HEAP, lsl #32
    // 0xab9450: cmp             w5, NULL
    // 0xab9454: b.ne            #0xab9460
    // 0xab9458: r4 = Null
    //     0xab9458: mov             x4, NULL
    // 0xab945c: b               #0xab9468
    // 0xab9460: LoadField: r4 = r5->field_b
    //     0xab9460: ldur            w4, [x5, #0xb]
    // 0xab9464: DecompressPointer r4
    //     0xab9464: add             x4, x4, HEAP, lsl #32
    // 0xab9468: cmp             w4, NULL
    // 0xab946c: b.ne            #0xab9478
    // 0xab9470: r4 = 0
    //     0xab9470: movz            x4, #0
    // 0xab9474: b               #0xab9488
    // 0xab9478: r5 = LoadInt32Instr(r4)
    //     0xab9478: sbfx            x5, x4, #1, #0x1f
    //     0xab947c: tbz             w4, #0, #0xab9484
    //     0xab9480: ldur            x5, [x4, #7]
    // 0xab9484: mov             x4, x5
    // 0xab9488: stur            x4, [fp, #-0x20]
    // 0xab948c: LoadField: r5 = r2->field_23
    //     0xab948c: ldur            w5, [x2, #0x23]
    // 0xab9490: DecompressPointer r5
    //     0xab9490: add             x5, x5, HEAP, lsl #32
    // 0xab9494: LoadField: r2 = r5->field_13
    //     0xab9494: ldur            w2, [x5, #0x13]
    // 0xab9498: DecompressPointer r2
    //     0xab9498: add             x2, x2, HEAP, lsl #32
    // 0xab949c: cmp             w2, NULL
    // 0xab94a0: b.ne            #0xab94ac
    // 0xab94a4: r2 = Null
    //     0xab94a4: mov             x2, NULL
    // 0xab94a8: b               #0xab94b8
    // 0xab94ac: LoadField: r5 = r2->field_f
    //     0xab94ac: ldur            w5, [x2, #0xf]
    // 0xab94b0: DecompressPointer r5
    //     0xab94b0: add             x5, x5, HEAP, lsl #32
    // 0xab94b4: mov             x2, x5
    // 0xab94b8: cmp             w2, NULL
    // 0xab94bc: b.ne            #0xab94c8
    // 0xab94c0: r6 = 0
    //     0xab94c0: movz            x6, #0
    // 0xab94c4: b               #0xab94d8
    // 0xab94c8: r5 = LoadInt32Instr(r2)
    //     0xab94c8: sbfx            x5, x2, #1, #0x1f
    //     0xab94cc: tbz             w2, #0, #0xab94d4
    //     0xab94d0: ldur            x5, [x2, #7]
    // 0xab94d4: mov             x6, x5
    // 0xab94d8: ldur            x2, [fp, #-8]
    // 0xab94dc: ldur            x5, [fp, #-0x10]
    // 0xab94e0: stur            x6, [fp, #-0x18]
    // 0xab94e4: r0 = Color()
    //     0xab94e4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xab94e8: mov             x3, x0
    // 0xab94ec: r0 = Instance_ColorSpace
    //     0xab94ec: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xab94f0: stur            x3, [fp, #-0x30]
    // 0xab94f4: StoreField: r3->field_27 = r0
    //     0xab94f4: stur            w0, [x3, #0x27]
    // 0xab94f8: d0 = 0.700000
    //     0xab94f8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xab94fc: ldr             d0, [x17, #0xf48]
    // 0xab9500: StoreField: r3->field_7 = d0
    //     0xab9500: stur            d0, [x3, #7]
    // 0xab9504: ldur            x0, [fp, #-0x28]
    // 0xab9508: ubfx            x0, x0, #0, #0x20
    // 0xab950c: and             w1, w0, #0xff
    // 0xab9510: ubfx            x1, x1, #0, #0x20
    // 0xab9514: scvtf           d0, x1
    // 0xab9518: d1 = 255.000000
    //     0xab9518: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xab951c: fdiv            d2, d0, d1
    // 0xab9520: StoreField: r3->field_f = d2
    //     0xab9520: stur            d2, [x3, #0xf]
    // 0xab9524: ldur            x0, [fp, #-0x20]
    // 0xab9528: ubfx            x0, x0, #0, #0x20
    // 0xab952c: and             w1, w0, #0xff
    // 0xab9530: ubfx            x1, x1, #0, #0x20
    // 0xab9534: scvtf           d0, x1
    // 0xab9538: fdiv            d2, d0, d1
    // 0xab953c: ArrayStore: r3[0] = d2  ; List_8
    //     0xab953c: stur            d2, [x3, #0x17]
    // 0xab9540: ldur            x0, [fp, #-0x18]
    // 0xab9544: ubfx            x0, x0, #0, #0x20
    // 0xab9548: and             w1, w0, #0xff
    // 0xab954c: ubfx            x1, x1, #0, #0x20
    // 0xab9550: scvtf           d0, x1
    // 0xab9554: fdiv            d2, d0, d1
    // 0xab9558: StoreField: r3->field_1f = d2
    //     0xab9558: stur            d2, [x3, #0x1f]
    // 0xab955c: r1 = Null
    //     0xab955c: mov             x1, NULL
    // 0xab9560: r2 = 4
    //     0xab9560: movz            x2, #0x4
    // 0xab9564: r0 = AllocateArray()
    //     0xab9564: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab9568: mov             x2, x0
    // 0xab956c: ldur            x0, [fp, #-0x38]
    // 0xab9570: stur            x2, [fp, #-0x40]
    // 0xab9574: StoreField: r2->field_f = r0
    //     0xab9574: stur            w0, [x2, #0xf]
    // 0xab9578: ldur            x0, [fp, #-0x30]
    // 0xab957c: StoreField: r2->field_13 = r0
    //     0xab957c: stur            w0, [x2, #0x13]
    // 0xab9580: r1 = <Color>
    //     0xab9580: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xab9584: ldr             x1, [x1, #0xf80]
    // 0xab9588: r0 = AllocateGrowableArray()
    //     0xab9588: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab958c: mov             x1, x0
    // 0xab9590: ldur            x0, [fp, #-0x40]
    // 0xab9594: stur            x1, [fp, #-0x30]
    // 0xab9598: StoreField: r1->field_f = r0
    //     0xab9598: stur            w0, [x1, #0xf]
    // 0xab959c: r2 = 4
    //     0xab959c: movz            x2, #0x4
    // 0xab95a0: StoreField: r1->field_b = r2
    //     0xab95a0: stur            w2, [x1, #0xb]
    // 0xab95a4: r0 = LinearGradient()
    //     0xab95a4: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xab95a8: mov             x1, x0
    // 0xab95ac: r0 = Instance_Alignment
    //     0xab95ac: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xab95b0: ldr             x0, [x0, #0xce0]
    // 0xab95b4: stur            x1, [fp, #-0x38]
    // 0xab95b8: StoreField: r1->field_13 = r0
    //     0xab95b8: stur            w0, [x1, #0x13]
    // 0xab95bc: r0 = Instance_Alignment
    //     0xab95bc: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xab95c0: ldr             x0, [x0, #0xce8]
    // 0xab95c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xab95c4: stur            w0, [x1, #0x17]
    // 0xab95c8: r0 = Instance_TileMode
    //     0xab95c8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xab95cc: ldr             x0, [x0, #0xcf0]
    // 0xab95d0: StoreField: r1->field_1b = r0
    //     0xab95d0: stur            w0, [x1, #0x1b]
    // 0xab95d4: ldur            x0, [fp, #-0x30]
    // 0xab95d8: StoreField: r1->field_7 = r0
    //     0xab95d8: stur            w0, [x1, #7]
    // 0xab95dc: r0 = BoxDecoration()
    //     0xab95dc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xab95e0: mov             x2, x0
    // 0xab95e4: r0 = Instance_BorderRadius
    //     0xab95e4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e18] Obj!BorderRadius@d5a1e1
    //     0xab95e8: ldr             x0, [x0, #0xe18]
    // 0xab95ec: stur            x2, [fp, #-0x30]
    // 0xab95f0: StoreField: r2->field_13 = r0
    //     0xab95f0: stur            w0, [x2, #0x13]
    // 0xab95f4: ldur            x0, [fp, #-0x38]
    // 0xab95f8: StoreField: r2->field_1b = r0
    //     0xab95f8: stur            w0, [x2, #0x1b]
    // 0xab95fc: r0 = Instance_BoxShape
    //     0xab95fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xab9600: ldr             x0, [x0, #0x80]
    // 0xab9604: StoreField: r2->field_23 = r0
    //     0xab9604: stur            w0, [x2, #0x23]
    // 0xab9608: ldur            x3, [fp, #-0x10]
    // 0xab960c: LoadField: r1 = r3->field_f
    //     0xab960c: ldur            w1, [x3, #0xf]
    // 0xab9610: DecompressPointer r1
    //     0xab9610: add             x1, x1, HEAP, lsl #32
    // 0xab9614: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab9614: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab9618: r0 = _of()
    //     0xab9618: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xab961c: LoadField: r1 = r0->field_7
    //     0xab961c: ldur            w1, [x0, #7]
    // 0xab9620: DecompressPointer r1
    //     0xab9620: add             x1, x1, HEAP, lsl #32
    // 0xab9624: LoadField: d0 = r1->field_7
    //     0xab9624: ldur            d0, [x1, #7]
    // 0xab9628: d2 = 0.500000
    //     0xab9628: fmov            d2, #0.50000000
    // 0xab962c: fmul            d1, d0, d2
    // 0xab9630: ldur            x0, [fp, #-8]
    // 0xab9634: stur            d1, [fp, #-0x68]
    // 0xab9638: LoadField: r1 = r0->field_f
    //     0xab9638: ldur            w1, [x0, #0xf]
    // 0xab963c: DecompressPointer r1
    //     0xab963c: add             x1, x1, HEAP, lsl #32
    // 0xab9640: LoadField: r3 = r1->field_b
    //     0xab9640: ldur            w3, [x1, #0xb]
    // 0xab9644: DecompressPointer r3
    //     0xab9644: add             x3, x3, HEAP, lsl #32
    // 0xab9648: stur            x3, [fp, #-0x38]
    // 0xab964c: cmp             w3, NULL
    // 0xab9650: b.eq            #0xabb1f8
    // 0xab9654: LoadField: r1 = r3->field_1f
    //     0xab9654: ldur            w1, [x3, #0x1f]
    // 0xab9658: DecompressPointer r1
    //     0xab9658: add             x1, x1, HEAP, lsl #32
    // 0xab965c: tbz             w1, #4, #0xab96cc
    // 0xab9660: r1 = Null
    //     0xab9660: mov             x1, NULL
    // 0xab9664: r2 = 6
    //     0xab9664: movz            x2, #0x6
    // 0xab9668: r0 = AllocateArray()
    //     0xab9668: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab966c: r16 = "Save upto "
    //     0xab966c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca0] "Save upto "
    //     0xab9670: ldr             x16, [x16, #0xca0]
    // 0xab9674: StoreField: r0->field_f = r16
    //     0xab9674: stur            w16, [x0, #0xf]
    // 0xab9678: ldur            x2, [fp, #-0x38]
    // 0xab967c: LoadField: r1 = r2->field_27
    //     0xab967c: ldur            w1, [x2, #0x27]
    // 0xab9680: DecompressPointer r1
    //     0xab9680: add             x1, x1, HEAP, lsl #32
    // 0xab9684: cmp             w1, NULL
    // 0xab9688: b.ne            #0xab9694
    // 0xab968c: r1 = Null
    //     0xab968c: mov             x1, NULL
    // 0xab9690: b               #0xab96a0
    // 0xab9694: LoadField: r2 = r1->field_f
    //     0xab9694: ldur            w2, [x1, #0xf]
    // 0xab9698: DecompressPointer r2
    //     0xab9698: add             x2, x2, HEAP, lsl #32
    // 0xab969c: mov             x1, x2
    // 0xab96a0: cmp             w1, NULL
    // 0xab96a4: b.ne            #0xab96ac
    // 0xab96a8: r1 = ""
    //     0xab96a8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab96ac: StoreField: r0->field_13 = r1
    //     0xab96ac: stur            w1, [x0, #0x13]
    // 0xab96b0: r16 = "!"
    //     0xab96b0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xab96b4: ldr             x16, [x16, #0xd00]
    // 0xab96b8: ArrayStore: r0[0] = r16  ; List_4
    //     0xab96b8: stur            w16, [x0, #0x17]
    // 0xab96bc: str             x0, [SP]
    // 0xab96c0: r0 = _interpolate()
    //     0xab96c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab96c4: mov             x3, x0
    // 0xab96c8: b               #0xab987c
    // 0xab96cc: mov             x2, x3
    // 0xab96d0: LoadField: r0 = r2->field_f
    //     0xab96d0: ldur            w0, [x2, #0xf]
    // 0xab96d4: DecompressPointer r0
    //     0xab96d4: add             x0, x0, HEAP, lsl #32
    // 0xab96d8: cmp             w0, NULL
    // 0xab96dc: b.ne            #0xab96ec
    // 0xab96e0: ldur            x3, [fp, #-0x10]
    // 0xab96e4: r0 = Null
    //     0xab96e4: mov             x0, NULL
    // 0xab96e8: b               #0xab973c
    // 0xab96ec: ldur            x3, [fp, #-0x10]
    // 0xab96f0: LoadField: r4 = r0->field_f
    //     0xab96f0: ldur            w4, [x0, #0xf]
    // 0xab96f4: DecompressPointer r4
    //     0xab96f4: add             x4, x4, HEAP, lsl #32
    // 0xab96f8: LoadField: r0 = r3->field_13
    //     0xab96f8: ldur            w0, [x3, #0x13]
    // 0xab96fc: DecompressPointer r0
    //     0xab96fc: add             x0, x0, HEAP, lsl #32
    // 0xab9700: LoadField: r1 = r4->field_b
    //     0xab9700: ldur            w1, [x4, #0xb]
    // 0xab9704: r5 = LoadInt32Instr(r0)
    //     0xab9704: sbfx            x5, x0, #1, #0x1f
    //     0xab9708: tbz             w0, #0, #0xab9710
    //     0xab970c: ldur            x5, [x0, #7]
    // 0xab9710: r0 = LoadInt32Instr(r1)
    //     0xab9710: sbfx            x0, x1, #1, #0x1f
    // 0xab9714: mov             x1, x5
    // 0xab9718: cmp             x1, x0
    // 0xab971c: b.hs            #0xabb1fc
    // 0xab9720: LoadField: r0 = r4->field_f
    //     0xab9720: ldur            w0, [x4, #0xf]
    // 0xab9724: DecompressPointer r0
    //     0xab9724: add             x0, x0, HEAP, lsl #32
    // 0xab9728: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab9728: add             x16, x0, x5, lsl #2
    //     0xab972c: ldur            w1, [x16, #0xf]
    // 0xab9730: DecompressPointer r1
    //     0xab9730: add             x1, x1, HEAP, lsl #32
    // 0xab9734: LoadField: r0 = r1->field_7
    //     0xab9734: ldur            w0, [x1, #7]
    // 0xab9738: DecompressPointer r0
    //     0xab9738: add             x0, x0, HEAP, lsl #32
    // 0xab973c: LoadField: r1 = r2->field_13
    //     0xab973c: ldur            w1, [x2, #0x13]
    // 0xab9740: DecompressPointer r1
    //     0xab9740: add             x1, x1, HEAP, lsl #32
    // 0xab9744: r2 = LoadClassIdInstr(r0)
    //     0xab9744: ldur            x2, [x0, #-1]
    //     0xab9748: ubfx            x2, x2, #0xc, #0x14
    // 0xab974c: stp             x1, x0, [SP]
    // 0xab9750: mov             x0, x2
    // 0xab9754: mov             lr, x0
    // 0xab9758: ldr             lr, [x21, lr, lsl #3]
    // 0xab975c: blr             lr
    // 0xab9760: tbnz            w0, #4, #0xab97e8
    // 0xab9764: ldur            x0, [fp, #-8]
    // 0xab9768: r1 = Null
    //     0xab9768: mov             x1, NULL
    // 0xab976c: r2 = 6
    //     0xab976c: movz            x2, #0x6
    // 0xab9770: r0 = AllocateArray()
    //     0xab9770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab9774: r16 = "You\'ve Saved "
    //     0xab9774: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a5c8] "You\'ve Saved "
    //     0xab9778: ldr             x16, [x16, #0x5c8]
    // 0xab977c: StoreField: r0->field_f = r16
    //     0xab977c: stur            w16, [x0, #0xf]
    // 0xab9780: ldur            x1, [fp, #-8]
    // 0xab9784: LoadField: r2 = r1->field_f
    //     0xab9784: ldur            w2, [x1, #0xf]
    // 0xab9788: DecompressPointer r2
    //     0xab9788: add             x2, x2, HEAP, lsl #32
    // 0xab978c: LoadField: r3 = r2->field_b
    //     0xab978c: ldur            w3, [x2, #0xb]
    // 0xab9790: DecompressPointer r3
    //     0xab9790: add             x3, x3, HEAP, lsl #32
    // 0xab9794: cmp             w3, NULL
    // 0xab9798: b.eq            #0xabb200
    // 0xab979c: LoadField: r2 = r3->field_27
    //     0xab979c: ldur            w2, [x3, #0x27]
    // 0xab97a0: DecompressPointer r2
    //     0xab97a0: add             x2, x2, HEAP, lsl #32
    // 0xab97a4: cmp             w2, NULL
    // 0xab97a8: b.ne            #0xab97b4
    // 0xab97ac: r2 = Null
    //     0xab97ac: mov             x2, NULL
    // 0xab97b0: b               #0xab97c0
    // 0xab97b4: LoadField: r3 = r2->field_f
    //     0xab97b4: ldur            w3, [x2, #0xf]
    // 0xab97b8: DecompressPointer r3
    //     0xab97b8: add             x3, x3, HEAP, lsl #32
    // 0xab97bc: mov             x2, x3
    // 0xab97c0: cmp             w2, NULL
    // 0xab97c4: b.ne            #0xab97cc
    // 0xab97c8: r2 = ""
    //     0xab97c8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab97cc: StoreField: r0->field_13 = r2
    //     0xab97cc: stur            w2, [x0, #0x13]
    // 0xab97d0: r16 = "!"
    //     0xab97d0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xab97d4: ldr             x16, [x16, #0xd00]
    // 0xab97d8: ArrayStore: r0[0] = r16  ; List_4
    //     0xab97d8: stur            w16, [x0, #0x17]
    // 0xab97dc: str             x0, [SP]
    // 0xab97e0: r0 = _interpolate()
    //     0xab97e0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab97e4: b               #0xab9878
    // 0xab97e8: ldur            x2, [fp, #-8]
    // 0xab97ec: LoadField: r0 = r2->field_f
    //     0xab97ec: ldur            w0, [x2, #0xf]
    // 0xab97f0: DecompressPointer r0
    //     0xab97f0: add             x0, x0, HEAP, lsl #32
    // 0xab97f4: LoadField: r1 = r0->field_b
    //     0xab97f4: ldur            w1, [x0, #0xb]
    // 0xab97f8: DecompressPointer r1
    //     0xab97f8: add             x1, x1, HEAP, lsl #32
    // 0xab97fc: cmp             w1, NULL
    // 0xab9800: b.eq            #0xabb204
    // 0xab9804: LoadField: r0 = r1->field_f
    //     0xab9804: ldur            w0, [x1, #0xf]
    // 0xab9808: DecompressPointer r0
    //     0xab9808: add             x0, x0, HEAP, lsl #32
    // 0xab980c: cmp             w0, NULL
    // 0xab9810: b.ne            #0xab9820
    // 0xab9814: ldur            x3, [fp, #-0x10]
    // 0xab9818: r0 = Null
    //     0xab9818: mov             x0, NULL
    // 0xab981c: b               #0xab9870
    // 0xab9820: ldur            x3, [fp, #-0x10]
    // 0xab9824: LoadField: r4 = r0->field_f
    //     0xab9824: ldur            w4, [x0, #0xf]
    // 0xab9828: DecompressPointer r4
    //     0xab9828: add             x4, x4, HEAP, lsl #32
    // 0xab982c: LoadField: r0 = r3->field_13
    //     0xab982c: ldur            w0, [x3, #0x13]
    // 0xab9830: DecompressPointer r0
    //     0xab9830: add             x0, x0, HEAP, lsl #32
    // 0xab9834: LoadField: r1 = r4->field_b
    //     0xab9834: ldur            w1, [x4, #0xb]
    // 0xab9838: r5 = LoadInt32Instr(r0)
    //     0xab9838: sbfx            x5, x0, #1, #0x1f
    //     0xab983c: tbz             w0, #0, #0xab9844
    //     0xab9840: ldur            x5, [x0, #7]
    // 0xab9844: r0 = LoadInt32Instr(r1)
    //     0xab9844: sbfx            x0, x1, #1, #0x1f
    // 0xab9848: mov             x1, x5
    // 0xab984c: cmp             x1, x0
    // 0xab9850: b.hs            #0xabb208
    // 0xab9854: LoadField: r0 = r4->field_f
    //     0xab9854: ldur            w0, [x4, #0xf]
    // 0xab9858: DecompressPointer r0
    //     0xab9858: add             x0, x0, HEAP, lsl #32
    // 0xab985c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab985c: add             x16, x0, x5, lsl #2
    //     0xab9860: ldur            w1, [x16, #0xf]
    // 0xab9864: DecompressPointer r1
    //     0xab9864: add             x1, x1, HEAP, lsl #32
    // 0xab9868: LoadField: r0 = r1->field_13
    //     0xab9868: ldur            w0, [x1, #0x13]
    // 0xab986c: DecompressPointer r0
    //     0xab986c: add             x0, x0, HEAP, lsl #32
    // 0xab9870: str             x0, [SP]
    // 0xab9874: r0 = _interpolateSingle()
    //     0xab9874: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xab9878: mov             x3, x0
    // 0xab987c: ldur            x0, [fp, #-8]
    // 0xab9880: ldur            x2, [fp, #-0x10]
    // 0xab9884: ldur            d0, [fp, #-0x68]
    // 0xab9888: stur            x3, [fp, #-0x38]
    // 0xab988c: LoadField: r1 = r2->field_f
    //     0xab988c: ldur            w1, [x2, #0xf]
    // 0xab9890: DecompressPointer r1
    //     0xab9890: add             x1, x1, HEAP, lsl #32
    // 0xab9894: r0 = of()
    //     0xab9894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9898: LoadField: r1 = r0->field_87
    //     0xab9898: ldur            w1, [x0, #0x87]
    // 0xab989c: DecompressPointer r1
    //     0xab989c: add             x1, x1, HEAP, lsl #32
    // 0xab98a0: LoadField: r0 = r1->field_7
    //     0xab98a0: ldur            w0, [x1, #7]
    // 0xab98a4: DecompressPointer r0
    //     0xab98a4: add             x0, x0, HEAP, lsl #32
    // 0xab98a8: r16 = 16.000000
    //     0xab98a8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab98ac: ldr             x16, [x16, #0x188]
    // 0xab98b0: r30 = Instance_Color
    //     0xab98b0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab98b4: stp             lr, x16, [SP]
    // 0xab98b8: mov             x1, x0
    // 0xab98bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab98bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab98c0: ldr             x4, [x4, #0xaa0]
    // 0xab98c4: r0 = copyWith()
    //     0xab98c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab98c8: stur            x0, [fp, #-0x40]
    // 0xab98cc: r0 = Text()
    //     0xab98cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab98d0: mov             x1, x0
    // 0xab98d4: ldur            x0, [fp, #-0x38]
    // 0xab98d8: stur            x1, [fp, #-0x48]
    // 0xab98dc: StoreField: r1->field_b = r0
    //     0xab98dc: stur            w0, [x1, #0xb]
    // 0xab98e0: ldur            x0, [fp, #-0x40]
    // 0xab98e4: StoreField: r1->field_13 = r0
    //     0xab98e4: stur            w0, [x1, #0x13]
    // 0xab98e8: r2 = 4
    //     0xab98e8: movz            x2, #0x4
    // 0xab98ec: StoreField: r1->field_37 = r2
    //     0xab98ec: stur            w2, [x1, #0x37]
    // 0xab98f0: r0 = Padding()
    //     0xab98f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab98f4: mov             x1, x0
    // 0xab98f8: r0 = Instance_EdgeInsets
    //     0xab98f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xab98fc: ldr             x0, [x0, #0x980]
    // 0xab9900: stur            x1, [fp, #-0x40]
    // 0xab9904: StoreField: r1->field_f = r0
    //     0xab9904: stur            w0, [x1, #0xf]
    // 0xab9908: ldur            x2, [fp, #-0x48]
    // 0xab990c: StoreField: r1->field_b = r2
    //     0xab990c: stur            w2, [x1, #0xb]
    // 0xab9910: ldur            d0, [fp, #-0x68]
    // 0xab9914: r2 = inline_Allocate_Double()
    //     0xab9914: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xab9918: add             x2, x2, #0x10
    //     0xab991c: cmp             x3, x2
    //     0xab9920: b.ls            #0xabb20c
    //     0xab9924: str             x2, [THR, #0x50]  ; THR::top
    //     0xab9928: sub             x2, x2, #0xf
    //     0xab992c: movz            x3, #0xe15c
    //     0xab9930: movk            x3, #0x3, lsl #16
    //     0xab9934: stur            x3, [x2, #-1]
    // 0xab9938: StoreField: r2->field_7 = d0
    //     0xab9938: stur            d0, [x2, #7]
    // 0xab993c: stur            x2, [fp, #-0x38]
    // 0xab9940: r0 = SizedBox()
    //     0xab9940: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xab9944: mov             x2, x0
    // 0xab9948: ldur            x0, [fp, #-0x38]
    // 0xab994c: stur            x2, [fp, #-0x48]
    // 0xab9950: StoreField: r2->field_f = r0
    //     0xab9950: stur            w0, [x2, #0xf]
    // 0xab9954: ldur            x0, [fp, #-0x40]
    // 0xab9958: StoreField: r2->field_b = r0
    //     0xab9958: stur            w0, [x2, #0xb]
    // 0xab995c: ldur            x3, [fp, #-8]
    // 0xab9960: LoadField: r0 = r3->field_f
    //     0xab9960: ldur            w0, [x3, #0xf]
    // 0xab9964: DecompressPointer r0
    //     0xab9964: add             x0, x0, HEAP, lsl #32
    // 0xab9968: LoadField: r4 = r0->field_b
    //     0xab9968: ldur            w4, [x0, #0xb]
    // 0xab996c: DecompressPointer r4
    //     0xab996c: add             x4, x4, HEAP, lsl #32
    // 0xab9970: cmp             w4, NULL
    // 0xab9974: b.eq            #0xabb228
    // 0xab9978: LoadField: r0 = r4->field_f
    //     0xab9978: ldur            w0, [x4, #0xf]
    // 0xab997c: DecompressPointer r0
    //     0xab997c: add             x0, x0, HEAP, lsl #32
    // 0xab9980: cmp             w0, NULL
    // 0xab9984: b.ne            #0xab9994
    // 0xab9988: ldur            x5, [fp, #-0x10]
    // 0xab998c: r0 = Null
    //     0xab998c: mov             x0, NULL
    // 0xab9990: b               #0xab99e4
    // 0xab9994: ldur            x5, [fp, #-0x10]
    // 0xab9998: LoadField: r6 = r0->field_f
    //     0xab9998: ldur            w6, [x0, #0xf]
    // 0xab999c: DecompressPointer r6
    //     0xab999c: add             x6, x6, HEAP, lsl #32
    // 0xab99a0: LoadField: r0 = r5->field_13
    //     0xab99a0: ldur            w0, [x5, #0x13]
    // 0xab99a4: DecompressPointer r0
    //     0xab99a4: add             x0, x0, HEAP, lsl #32
    // 0xab99a8: LoadField: r1 = r6->field_b
    //     0xab99a8: ldur            w1, [x6, #0xb]
    // 0xab99ac: r7 = LoadInt32Instr(r0)
    //     0xab99ac: sbfx            x7, x0, #1, #0x1f
    //     0xab99b0: tbz             w0, #0, #0xab99b8
    //     0xab99b4: ldur            x7, [x0, #7]
    // 0xab99b8: r0 = LoadInt32Instr(r1)
    //     0xab99b8: sbfx            x0, x1, #1, #0x1f
    // 0xab99bc: mov             x1, x7
    // 0xab99c0: cmp             x1, x0
    // 0xab99c4: b.hs            #0xabb22c
    // 0xab99c8: LoadField: r0 = r6->field_f
    //     0xab99c8: ldur            w0, [x6, #0xf]
    // 0xab99cc: DecompressPointer r0
    //     0xab99cc: add             x0, x0, HEAP, lsl #32
    // 0xab99d0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xab99d0: add             x16, x0, x7, lsl #2
    //     0xab99d4: ldur            w1, [x16, #0xf]
    // 0xab99d8: DecompressPointer r1
    //     0xab99d8: add             x1, x1, HEAP, lsl #32
    // 0xab99dc: LoadField: r0 = r1->field_7
    //     0xab99dc: ldur            w0, [x1, #7]
    // 0xab99e0: DecompressPointer r0
    //     0xab99e0: add             x0, x0, HEAP, lsl #32
    // 0xab99e4: LoadField: r1 = r4->field_13
    //     0xab99e4: ldur            w1, [x4, #0x13]
    // 0xab99e8: DecompressPointer r1
    //     0xab99e8: add             x1, x1, HEAP, lsl #32
    // 0xab99ec: r4 = LoadClassIdInstr(r0)
    //     0xab99ec: ldur            x4, [x0, #-1]
    //     0xab99f0: ubfx            x4, x4, #0xc, #0x14
    // 0xab99f4: stp             x1, x0, [SP]
    // 0xab99f8: mov             x0, x4
    // 0xab99fc: mov             lr, x0
    // 0xab9a00: ldr             lr, [x21, lr, lsl #3]
    // 0xab9a04: blr             lr
    // 0xab9a08: tbnz            w0, #4, #0xab9a14
    // 0xab9a0c: ldur            x0, [fp, #-8]
    // 0xab9a10: b               #0xab9a3c
    // 0xab9a14: ldur            x0, [fp, #-8]
    // 0xab9a18: LoadField: r1 = r0->field_f
    //     0xab9a18: ldur            w1, [x0, #0xf]
    // 0xab9a1c: DecompressPointer r1
    //     0xab9a1c: add             x1, x1, HEAP, lsl #32
    // 0xab9a20: LoadField: r2 = r1->field_b
    //     0xab9a20: ldur            w2, [x1, #0xb]
    // 0xab9a24: DecompressPointer r2
    //     0xab9a24: add             x2, x2, HEAP, lsl #32
    // 0xab9a28: cmp             w2, NULL
    // 0xab9a2c: b.eq            #0xabb230
    // 0xab9a30: LoadField: r1 = r2->field_1f
    //     0xab9a30: ldur            w1, [x2, #0x1f]
    // 0xab9a34: DecompressPointer r1
    //     0xab9a34: add             x1, x1, HEAP, lsl #32
    // 0xab9a38: tbz             w1, #4, #0xab9aac
    // 0xab9a3c: ldur            x2, [fp, #-0x10]
    // 0xab9a40: LoadField: r1 = r2->field_f
    //     0xab9a40: ldur            w1, [x2, #0xf]
    // 0xab9a44: DecompressPointer r1
    //     0xab9a44: add             x1, x1, HEAP, lsl #32
    // 0xab9a48: r0 = of()
    //     0xab9a48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9a4c: LoadField: r1 = r0->field_87
    //     0xab9a4c: ldur            w1, [x0, #0x87]
    // 0xab9a50: DecompressPointer r1
    //     0xab9a50: add             x1, x1, HEAP, lsl #32
    // 0xab9a54: LoadField: r0 = r1->field_7
    //     0xab9a54: ldur            w0, [x1, #7]
    // 0xab9a58: DecompressPointer r0
    //     0xab9a58: add             x0, x0, HEAP, lsl #32
    // 0xab9a5c: r16 = Instance_Color
    //     0xab9a5c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab9a60: r30 = 14.000000
    //     0xab9a60: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab9a64: ldr             lr, [lr, #0x1d8]
    // 0xab9a68: stp             lr, x16, [SP]
    // 0xab9a6c: mov             x1, x0
    // 0xab9a70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab9a70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab9a74: ldr             x4, [x4, #0x9b8]
    // 0xab9a78: r0 = copyWith()
    //     0xab9a78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab9a7c: stur            x0, [fp, #-0x38]
    // 0xab9a80: r0 = Text()
    //     0xab9a80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab9a84: mov             x1, x0
    // 0xab9a88: r0 = "Bumper Offer Applied!"
    //     0xab9a88: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d08] "Bumper Offer Applied!"
    //     0xab9a8c: ldr             x0, [x0, #0xd08]
    // 0xab9a90: StoreField: r1->field_b = r0
    //     0xab9a90: stur            w0, [x1, #0xb]
    // 0xab9a94: ldur            x0, [fp, #-0x38]
    // 0xab9a98: StoreField: r1->field_13 = r0
    //     0xab9a98: stur            w0, [x1, #0x13]
    // 0xab9a9c: r2 = 4
    //     0xab9a9c: movz            x2, #0x4
    // 0xab9aa0: StoreField: r1->field_37 = r2
    //     0xab9aa0: stur            w2, [x1, #0x37]
    // 0xab9aa4: mov             x2, x1
    // 0xab9aa8: b               #0xab9c04
    // 0xab9aac: ldur            x0, [fp, #-0x10]
    // 0xab9ab0: r2 = 4
    //     0xab9ab0: movz            x2, #0x4
    // 0xab9ab4: LoadField: r1 = r0->field_f
    //     0xab9ab4: ldur            w1, [x0, #0xf]
    // 0xab9ab8: DecompressPointer r1
    //     0xab9ab8: add             x1, x1, HEAP, lsl #32
    // 0xab9abc: r0 = of()
    //     0xab9abc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9ac0: LoadField: r1 = r0->field_87
    //     0xab9ac0: ldur            w1, [x0, #0x87]
    // 0xab9ac4: DecompressPointer r1
    //     0xab9ac4: add             x1, x1, HEAP, lsl #32
    // 0xab9ac8: LoadField: r0 = r1->field_7
    //     0xab9ac8: ldur            w0, [x1, #7]
    // 0xab9acc: DecompressPointer r0
    //     0xab9acc: add             x0, x0, HEAP, lsl #32
    // 0xab9ad0: r16 = Instance_Color
    //     0xab9ad0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab9ad4: r30 = 14.000000
    //     0xab9ad4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab9ad8: ldr             lr, [lr, #0x1d8]
    // 0xab9adc: stp             lr, x16, [SP]
    // 0xab9ae0: mov             x1, x0
    // 0xab9ae4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab9ae4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab9ae8: ldr             x4, [x4, #0x9b8]
    // 0xab9aec: r0 = copyWith()
    //     0xab9aec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab9af0: stur            x0, [fp, #-0x38]
    // 0xab9af4: r0 = TextSpan()
    //     0xab9af4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab9af8: mov             x2, x0
    // 0xab9afc: r0 = "Know "
    //     0xab9afc: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xab9b00: ldr             x0, [x0, #0x5d0]
    // 0xab9b04: stur            x2, [fp, #-0x40]
    // 0xab9b08: StoreField: r2->field_b = r0
    //     0xab9b08: stur            w0, [x2, #0xb]
    // 0xab9b0c: r0 = Instance__DeferringMouseCursor
    //     0xab9b0c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab9b10: ArrayStore: r2[0] = r0  ; List_4
    //     0xab9b10: stur            w0, [x2, #0x17]
    // 0xab9b14: ldur            x1, [fp, #-0x38]
    // 0xab9b18: StoreField: r2->field_7 = r1
    //     0xab9b18: stur            w1, [x2, #7]
    // 0xab9b1c: ldur            x3, [fp, #-0x10]
    // 0xab9b20: LoadField: r1 = r3->field_f
    //     0xab9b20: ldur            w1, [x3, #0xf]
    // 0xab9b24: DecompressPointer r1
    //     0xab9b24: add             x1, x1, HEAP, lsl #32
    // 0xab9b28: r0 = of()
    //     0xab9b28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9b2c: LoadField: r1 = r0->field_87
    //     0xab9b2c: ldur            w1, [x0, #0x87]
    // 0xab9b30: DecompressPointer r1
    //     0xab9b30: add             x1, x1, HEAP, lsl #32
    // 0xab9b34: LoadField: r0 = r1->field_7
    //     0xab9b34: ldur            w0, [x1, #7]
    // 0xab9b38: DecompressPointer r0
    //     0xab9b38: add             x0, x0, HEAP, lsl #32
    // 0xab9b3c: r16 = Instance_Color
    //     0xab9b3c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab9b40: r30 = 14.000000
    //     0xab9b40: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab9b44: ldr             lr, [lr, #0x1d8]
    // 0xab9b48: stp             lr, x16, [SP]
    // 0xab9b4c: mov             x1, x0
    // 0xab9b50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab9b50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab9b54: ldr             x4, [x4, #0x9b8]
    // 0xab9b58: r0 = copyWith()
    //     0xab9b58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab9b5c: stur            x0, [fp, #-0x38]
    // 0xab9b60: r0 = TextSpan()
    //     0xab9b60: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab9b64: r2 = "More"
    //     0xab9b64: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xab9b68: ldr             x2, [x2, #0x5d8]
    // 0xab9b6c: stur            x0, [fp, #-0x50]
    // 0xab9b70: StoreField: r0->field_b = r2
    //     0xab9b70: stur            w2, [x0, #0xb]
    // 0xab9b74: r3 = Instance__DeferringMouseCursor
    //     0xab9b74: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab9b78: ArrayStore: r0[0] = r3  ; List_4
    //     0xab9b78: stur            w3, [x0, #0x17]
    // 0xab9b7c: ldur            x1, [fp, #-0x38]
    // 0xab9b80: StoreField: r0->field_7 = r1
    //     0xab9b80: stur            w1, [x0, #7]
    // 0xab9b84: r1 = Null
    //     0xab9b84: mov             x1, NULL
    // 0xab9b88: r2 = 4
    //     0xab9b88: movz            x2, #0x4
    // 0xab9b8c: r0 = AllocateArray()
    //     0xab9b8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab9b90: mov             x2, x0
    // 0xab9b94: ldur            x0, [fp, #-0x40]
    // 0xab9b98: stur            x2, [fp, #-0x38]
    // 0xab9b9c: StoreField: r2->field_f = r0
    //     0xab9b9c: stur            w0, [x2, #0xf]
    // 0xab9ba0: ldur            x0, [fp, #-0x50]
    // 0xab9ba4: StoreField: r2->field_13 = r0
    //     0xab9ba4: stur            w0, [x2, #0x13]
    // 0xab9ba8: r1 = <TextSpan>
    //     0xab9ba8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xab9bac: ldr             x1, [x1, #0x940]
    // 0xab9bb0: r0 = AllocateGrowableArray()
    //     0xab9bb0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab9bb4: mov             x1, x0
    // 0xab9bb8: ldur            x0, [fp, #-0x38]
    // 0xab9bbc: stur            x1, [fp, #-0x40]
    // 0xab9bc0: StoreField: r1->field_f = r0
    //     0xab9bc0: stur            w0, [x1, #0xf]
    // 0xab9bc4: r2 = 4
    //     0xab9bc4: movz            x2, #0x4
    // 0xab9bc8: StoreField: r1->field_b = r2
    //     0xab9bc8: stur            w2, [x1, #0xb]
    // 0xab9bcc: r0 = TextSpan()
    //     0xab9bcc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xab9bd0: mov             x1, x0
    // 0xab9bd4: ldur            x0, [fp, #-0x40]
    // 0xab9bd8: stur            x1, [fp, #-0x38]
    // 0xab9bdc: StoreField: r1->field_f = r0
    //     0xab9bdc: stur            w0, [x1, #0xf]
    // 0xab9be0: r3 = Instance__DeferringMouseCursor
    //     0xab9be0: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xab9be4: ArrayStore: r1[0] = r3  ; List_4
    //     0xab9be4: stur            w3, [x1, #0x17]
    // 0xab9be8: r0 = RichText()
    //     0xab9be8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xab9bec: mov             x1, x0
    // 0xab9bf0: ldur            x2, [fp, #-0x38]
    // 0xab9bf4: stur            x0, [fp, #-0x38]
    // 0xab9bf8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab9bf8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab9bfc: r0 = RichText()
    //     0xab9bfc: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xab9c00: ldur            x2, [fp, #-0x38]
    // 0xab9c04: ldur            x0, [fp, #-8]
    // 0xab9c08: ldur            x1, [fp, #-0x48]
    // 0xab9c0c: stur            x2, [fp, #-0x38]
    // 0xab9c10: r0 = Padding()
    //     0xab9c10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab9c14: r4 = Instance_EdgeInsets
    //     0xab9c14: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xab9c18: ldr             x4, [x4, #0xc40]
    // 0xab9c1c: stur            x0, [fp, #-0x40]
    // 0xab9c20: StoreField: r0->field_f = r4
    //     0xab9c20: stur            w4, [x0, #0xf]
    // 0xab9c24: ldur            x1, [fp, #-0x38]
    // 0xab9c28: StoreField: r0->field_b = r1
    //     0xab9c28: stur            w1, [x0, #0xb]
    // 0xab9c2c: r0 = InkWell()
    //     0xab9c2c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xab9c30: mov             x3, x0
    // 0xab9c34: ldur            x0, [fp, #-0x40]
    // 0xab9c38: stur            x3, [fp, #-0x38]
    // 0xab9c3c: StoreField: r3->field_b = r0
    //     0xab9c3c: stur            w0, [x3, #0xb]
    // 0xab9c40: ldur            x2, [fp, #-0x10]
    // 0xab9c44: r1 = Function '<anonymous closure>':.
    //     0xab9c44: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6af78] AnonymousClosure: (0xabbc6c), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xab9c48: ldr             x1, [x1, #0xf78]
    // 0xab9c4c: r0 = AllocateClosure()
    //     0xab9c4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab9c50: mov             x1, x0
    // 0xab9c54: ldur            x0, [fp, #-0x38]
    // 0xab9c58: StoreField: r0->field_f = r1
    //     0xab9c58: stur            w1, [x0, #0xf]
    // 0xab9c5c: r2 = true
    //     0xab9c5c: add             x2, NULL, #0x20  ; true
    // 0xab9c60: StoreField: r0->field_43 = r2
    //     0xab9c60: stur            w2, [x0, #0x43]
    // 0xab9c64: r3 = Instance_BoxShape
    //     0xab9c64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xab9c68: ldr             x3, [x3, #0x80]
    // 0xab9c6c: StoreField: r0->field_47 = r3
    //     0xab9c6c: stur            w3, [x0, #0x47]
    // 0xab9c70: StoreField: r0->field_6f = r2
    //     0xab9c70: stur            w2, [x0, #0x6f]
    // 0xab9c74: r4 = false
    //     0xab9c74: add             x4, NULL, #0x30  ; false
    // 0xab9c78: StoreField: r0->field_73 = r4
    //     0xab9c78: stur            w4, [x0, #0x73]
    // 0xab9c7c: StoreField: r0->field_83 = r2
    //     0xab9c7c: stur            w2, [x0, #0x83]
    // 0xab9c80: StoreField: r0->field_7b = r4
    //     0xab9c80: stur            w4, [x0, #0x7b]
    // 0xab9c84: r1 = <FlexParentData>
    //     0xab9c84: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xab9c88: ldr             x1, [x1, #0xe00]
    // 0xab9c8c: r0 = Expanded()
    //     0xab9c8c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xab9c90: stur            x0, [fp, #-0x40]
    // 0xab9c94: StoreField: r0->field_13 = rZR
    //     0xab9c94: stur            xzr, [x0, #0x13]
    // 0xab9c98: r3 = Instance_FlexFit
    //     0xab9c98: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xab9c9c: ldr             x3, [x3, #0xe08]
    // 0xab9ca0: StoreField: r0->field_1b = r3
    //     0xab9ca0: stur            w3, [x0, #0x1b]
    // 0xab9ca4: ldur            x1, [fp, #-0x38]
    // 0xab9ca8: StoreField: r0->field_b = r1
    //     0xab9ca8: stur            w1, [x0, #0xb]
    // 0xab9cac: r1 = Null
    //     0xab9cac: mov             x1, NULL
    // 0xab9cb0: r2 = 4
    //     0xab9cb0: movz            x2, #0x4
    // 0xab9cb4: r0 = AllocateArray()
    //     0xab9cb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab9cb8: mov             x2, x0
    // 0xab9cbc: ldur            x0, [fp, #-0x48]
    // 0xab9cc0: stur            x2, [fp, #-0x38]
    // 0xab9cc4: StoreField: r2->field_f = r0
    //     0xab9cc4: stur            w0, [x2, #0xf]
    // 0xab9cc8: ldur            x0, [fp, #-0x40]
    // 0xab9ccc: StoreField: r2->field_13 = r0
    //     0xab9ccc: stur            w0, [x2, #0x13]
    // 0xab9cd0: r1 = <Widget>
    //     0xab9cd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab9cd4: r0 = AllocateGrowableArray()
    //     0xab9cd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab9cd8: mov             x1, x0
    // 0xab9cdc: ldur            x0, [fp, #-0x38]
    // 0xab9ce0: stur            x1, [fp, #-0x40]
    // 0xab9ce4: StoreField: r1->field_f = r0
    //     0xab9ce4: stur            w0, [x1, #0xf]
    // 0xab9ce8: r5 = 4
    //     0xab9ce8: movz            x5, #0x4
    // 0xab9cec: StoreField: r1->field_b = r5
    //     0xab9cec: stur            w5, [x1, #0xb]
    // 0xab9cf0: r0 = Column()
    //     0xab9cf0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xab9cf4: mov             x2, x0
    // 0xab9cf8: r6 = Instance_Axis
    //     0xab9cf8: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xab9cfc: stur            x2, [fp, #-0x38]
    // 0xab9d00: StoreField: r2->field_f = r6
    //     0xab9d00: stur            w6, [x2, #0xf]
    // 0xab9d04: r3 = Instance_MainAxisAlignment
    //     0xab9d04: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab9d08: ldr             x3, [x3, #0xa08]
    // 0xab9d0c: StoreField: r2->field_13 = r3
    //     0xab9d0c: stur            w3, [x2, #0x13]
    // 0xab9d10: r4 = Instance_MainAxisSize
    //     0xab9d10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab9d14: ldr             x4, [x4, #0xa10]
    // 0xab9d18: ArrayStore: r2[0] = r4  ; List_4
    //     0xab9d18: stur            w4, [x2, #0x17]
    // 0xab9d1c: r7 = Instance_CrossAxisAlignment
    //     0xab9d1c: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xab9d20: ldr             x7, [x7, #0x890]
    // 0xab9d24: StoreField: r2->field_1b = r7
    //     0xab9d24: stur            w7, [x2, #0x1b]
    // 0xab9d28: r5 = Instance_VerticalDirection
    //     0xab9d28: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab9d2c: ldr             x5, [x5, #0xa20]
    // 0xab9d30: StoreField: r2->field_23 = r5
    //     0xab9d30: stur            w5, [x2, #0x23]
    // 0xab9d34: r6 = Instance_Clip
    //     0xab9d34: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab9d38: ldr             x6, [x6, #0x38]
    // 0xab9d3c: StoreField: r2->field_2b = r6
    //     0xab9d3c: stur            w6, [x2, #0x2b]
    // 0xab9d40: StoreField: r2->field_2f = rZR
    //     0xab9d40: stur            xzr, [x2, #0x2f]
    // 0xab9d44: ldur            x0, [fp, #-0x40]
    // 0xab9d48: StoreField: r2->field_b = r0
    //     0xab9d48: stur            w0, [x2, #0xb]
    // 0xab9d4c: ldur            x7, [fp, #-8]
    // 0xab9d50: LoadField: r0 = r7->field_f
    //     0xab9d50: ldur            w0, [x7, #0xf]
    // 0xab9d54: DecompressPointer r0
    //     0xab9d54: add             x0, x0, HEAP, lsl #32
    // 0xab9d58: LoadField: r8 = r0->field_b
    //     0xab9d58: ldur            w8, [x0, #0xb]
    // 0xab9d5c: DecompressPointer r8
    //     0xab9d5c: add             x8, x8, HEAP, lsl #32
    // 0xab9d60: cmp             w8, NULL
    // 0xab9d64: b.eq            #0xabb234
    // 0xab9d68: LoadField: r0 = r8->field_1f
    //     0xab9d68: ldur            w0, [x8, #0x1f]
    // 0xab9d6c: DecompressPointer r0
    //     0xab9d6c: add             x0, x0, HEAP, lsl #32
    // 0xab9d70: tbz             w0, #4, #0xab9d7c
    // 0xab9d74: mov             x8, x7
    // 0xab9d78: b               #0xab9e14
    // 0xab9d7c: LoadField: r0 = r8->field_f
    //     0xab9d7c: ldur            w0, [x8, #0xf]
    // 0xab9d80: DecompressPointer r0
    //     0xab9d80: add             x0, x0, HEAP, lsl #32
    // 0xab9d84: cmp             w0, NULL
    // 0xab9d88: b.ne            #0xab9d98
    // 0xab9d8c: ldur            x9, [fp, #-0x10]
    // 0xab9d90: r0 = Null
    //     0xab9d90: mov             x0, NULL
    // 0xab9d94: b               #0xab9de8
    // 0xab9d98: ldur            x9, [fp, #-0x10]
    // 0xab9d9c: LoadField: r10 = r0->field_f
    //     0xab9d9c: ldur            w10, [x0, #0xf]
    // 0xab9da0: DecompressPointer r10
    //     0xab9da0: add             x10, x10, HEAP, lsl #32
    // 0xab9da4: LoadField: r0 = r9->field_13
    //     0xab9da4: ldur            w0, [x9, #0x13]
    // 0xab9da8: DecompressPointer r0
    //     0xab9da8: add             x0, x0, HEAP, lsl #32
    // 0xab9dac: LoadField: r1 = r10->field_b
    //     0xab9dac: ldur            w1, [x10, #0xb]
    // 0xab9db0: r11 = LoadInt32Instr(r0)
    //     0xab9db0: sbfx            x11, x0, #1, #0x1f
    //     0xab9db4: tbz             w0, #0, #0xab9dbc
    //     0xab9db8: ldur            x11, [x0, #7]
    // 0xab9dbc: r0 = LoadInt32Instr(r1)
    //     0xab9dbc: sbfx            x0, x1, #1, #0x1f
    // 0xab9dc0: mov             x1, x11
    // 0xab9dc4: cmp             x1, x0
    // 0xab9dc8: b.hs            #0xabb238
    // 0xab9dcc: LoadField: r0 = r10->field_f
    //     0xab9dcc: ldur            w0, [x10, #0xf]
    // 0xab9dd0: DecompressPointer r0
    //     0xab9dd0: add             x0, x0, HEAP, lsl #32
    // 0xab9dd4: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xab9dd4: add             x16, x0, x11, lsl #2
    //     0xab9dd8: ldur            w1, [x16, #0xf]
    // 0xab9ddc: DecompressPointer r1
    //     0xab9ddc: add             x1, x1, HEAP, lsl #32
    // 0xab9de0: LoadField: r0 = r1->field_7
    //     0xab9de0: ldur            w0, [x1, #7]
    // 0xab9de4: DecompressPointer r0
    //     0xab9de4: add             x0, x0, HEAP, lsl #32
    // 0xab9de8: LoadField: r1 = r8->field_13
    //     0xab9de8: ldur            w1, [x8, #0x13]
    // 0xab9dec: DecompressPointer r1
    //     0xab9dec: add             x1, x1, HEAP, lsl #32
    // 0xab9df0: r8 = LoadClassIdInstr(r0)
    //     0xab9df0: ldur            x8, [x0, #-1]
    //     0xab9df4: ubfx            x8, x8, #0xc, #0x14
    // 0xab9df8: stp             x1, x0, [SP]
    // 0xab9dfc: mov             x0, x8
    // 0xab9e00: mov             lr, x0
    // 0xab9e04: ldr             lr, [x21, lr, lsl #3]
    // 0xab9e08: blr             lr
    // 0xab9e0c: tbnz            w0, #4, #0xab9f24
    // 0xab9e10: ldur            x8, [fp, #-8]
    // 0xab9e14: LoadField: r0 = r8->field_f
    //     0xab9e14: ldur            w0, [x8, #0xf]
    // 0xab9e18: DecompressPointer r0
    //     0xab9e18: add             x0, x0, HEAP, lsl #32
    // 0xab9e1c: LoadField: r1 = r0->field_b
    //     0xab9e1c: ldur            w1, [x0, #0xb]
    // 0xab9e20: DecompressPointer r1
    //     0xab9e20: add             x1, x1, HEAP, lsl #32
    // 0xab9e24: cmp             w1, NULL
    // 0xab9e28: b.eq            #0xabb23c
    // 0xab9e2c: LoadField: r0 = r1->field_f
    //     0xab9e2c: ldur            w0, [x1, #0xf]
    // 0xab9e30: DecompressPointer r0
    //     0xab9e30: add             x0, x0, HEAP, lsl #32
    // 0xab9e34: cmp             w0, NULL
    // 0xab9e38: b.ne            #0xab9e48
    // 0xab9e3c: ldur            x2, [fp, #-0x10]
    // 0xab9e40: r0 = Null
    //     0xab9e40: mov             x0, NULL
    // 0xab9e44: b               #0xab9e98
    // 0xab9e48: ldur            x2, [fp, #-0x10]
    // 0xab9e4c: LoadField: r3 = r0->field_f
    //     0xab9e4c: ldur            w3, [x0, #0xf]
    // 0xab9e50: DecompressPointer r3
    //     0xab9e50: add             x3, x3, HEAP, lsl #32
    // 0xab9e54: LoadField: r0 = r2->field_13
    //     0xab9e54: ldur            w0, [x2, #0x13]
    // 0xab9e58: DecompressPointer r0
    //     0xab9e58: add             x0, x0, HEAP, lsl #32
    // 0xab9e5c: LoadField: r1 = r3->field_b
    //     0xab9e5c: ldur            w1, [x3, #0xb]
    // 0xab9e60: r4 = LoadInt32Instr(r0)
    //     0xab9e60: sbfx            x4, x0, #1, #0x1f
    //     0xab9e64: tbz             w0, #0, #0xab9e6c
    //     0xab9e68: ldur            x4, [x0, #7]
    // 0xab9e6c: r0 = LoadInt32Instr(r1)
    //     0xab9e6c: sbfx            x0, x1, #1, #0x1f
    // 0xab9e70: mov             x1, x4
    // 0xab9e74: cmp             x1, x0
    // 0xab9e78: b.hs            #0xabb240
    // 0xab9e7c: LoadField: r0 = r3->field_f
    //     0xab9e7c: ldur            w0, [x3, #0xf]
    // 0xab9e80: DecompressPointer r0
    //     0xab9e80: add             x0, x0, HEAP, lsl #32
    // 0xab9e84: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xab9e84: add             x16, x0, x4, lsl #2
    //     0xab9e88: ldur            w1, [x16, #0xf]
    // 0xab9e8c: DecompressPointer r1
    //     0xab9e8c: add             x1, x1, HEAP, lsl #32
    // 0xab9e90: LoadField: r0 = r1->field_b
    //     0xab9e90: ldur            w0, [x1, #0xb]
    // 0xab9e94: DecompressPointer r0
    //     0xab9e94: add             x0, x0, HEAP, lsl #32
    // 0xab9e98: r1 = LoadClassIdInstr(r0)
    //     0xab9e98: ldur            x1, [x0, #-1]
    //     0xab9e9c: ubfx            x1, x1, #0xc, #0x14
    // 0xab9ea0: r16 = "bumper_coupon"
    //     0xab9ea0: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xab9ea4: ldr             x16, [x16, #0x8d8]
    // 0xab9ea8: stp             x16, x0, [SP]
    // 0xab9eac: mov             x0, x1
    // 0xab9eb0: mov             lr, x0
    // 0xab9eb4: ldr             lr, [x21, lr, lsl #3]
    // 0xab9eb8: blr             lr
    // 0xab9ebc: tbnz            w0, #4, #0xab9f24
    // 0xab9ec0: ldur            x2, [fp, #-0x10]
    // 0xab9ec4: LoadField: r1 = r2->field_f
    //     0xab9ec4: ldur            w1, [x2, #0xf]
    // 0xab9ec8: DecompressPointer r1
    //     0xab9ec8: add             x1, x1, HEAP, lsl #32
    // 0xab9ecc: r0 = of()
    //     0xab9ecc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9ed0: LoadField: r1 = r0->field_87
    //     0xab9ed0: ldur            w1, [x0, #0x87]
    // 0xab9ed4: DecompressPointer r1
    //     0xab9ed4: add             x1, x1, HEAP, lsl #32
    // 0xab9ed8: LoadField: r0 = r1->field_7
    //     0xab9ed8: ldur            w0, [x1, #7]
    // 0xab9edc: DecompressPointer r0
    //     0xab9edc: add             x0, x0, HEAP, lsl #32
    // 0xab9ee0: r16 = Instance_Color
    //     0xab9ee0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab9ee4: r30 = 12.000000
    //     0xab9ee4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xab9ee8: ldr             lr, [lr, #0x9e8]
    // 0xab9eec: stp             lr, x16, [SP]
    // 0xab9ef0: mov             x1, x0
    // 0xab9ef4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab9ef4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab9ef8: ldr             x4, [x4, #0x9b8]
    // 0xab9efc: r0 = copyWith()
    //     0xab9efc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab9f00: stur            x0, [fp, #-0x40]
    // 0xab9f04: r0 = Text()
    //     0xab9f04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab9f08: r9 = "APPLIED"
    //     0xab9f08: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xab9f0c: ldr             x9, [x9, #0x5e8]
    // 0xab9f10: StoreField: r0->field_b = r9
    //     0xab9f10: stur            w9, [x0, #0xb]
    // 0xab9f14: ldur            x1, [fp, #-0x40]
    // 0xab9f18: StoreField: r0->field_13 = r1
    //     0xab9f18: stur            w1, [x0, #0x13]
    // 0xab9f1c: mov             x1, x0
    // 0xab9f20: b               #0xab9f84
    // 0xab9f24: ldur            x2, [fp, #-0x10]
    // 0xab9f28: LoadField: r1 = r2->field_f
    //     0xab9f28: ldur            w1, [x2, #0xf]
    // 0xab9f2c: DecompressPointer r1
    //     0xab9f2c: add             x1, x1, HEAP, lsl #32
    // 0xab9f30: r0 = of()
    //     0xab9f30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9f34: LoadField: r1 = r0->field_87
    //     0xab9f34: ldur            w1, [x0, #0x87]
    // 0xab9f38: DecompressPointer r1
    //     0xab9f38: add             x1, x1, HEAP, lsl #32
    // 0xab9f3c: LoadField: r0 = r1->field_7
    //     0xab9f3c: ldur            w0, [x1, #7]
    // 0xab9f40: DecompressPointer r0
    //     0xab9f40: add             x0, x0, HEAP, lsl #32
    // 0xab9f44: r16 = Instance_Color
    //     0xab9f44: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab9f48: r30 = 14.000000
    //     0xab9f48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab9f4c: ldr             lr, [lr, #0x1d8]
    // 0xab9f50: stp             lr, x16, [SP]
    // 0xab9f54: mov             x1, x0
    // 0xab9f58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab9f58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab9f5c: ldr             x4, [x4, #0x9b8]
    // 0xab9f60: r0 = copyWith()
    //     0xab9f60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab9f64: stur            x0, [fp, #-0x40]
    // 0xab9f68: r0 = Text()
    //     0xab9f68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab9f6c: r10 = "APPLY"
    //     0xab9f6c: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xab9f70: ldr             x10, [x10, #0x5f0]
    // 0xab9f74: StoreField: r0->field_b = r10
    //     0xab9f74: stur            w10, [x0, #0xb]
    // 0xab9f78: ldur            x1, [fp, #-0x40]
    // 0xab9f7c: StoreField: r0->field_13 = r1
    //     0xab9f7c: stur            w1, [x0, #0x13]
    // 0xab9f80: mov             x1, x0
    // 0xab9f84: ldur            x0, [fp, #-0x38]
    // 0xab9f88: stur            x1, [fp, #-0x40]
    // 0xab9f8c: r0 = InkWell()
    //     0xab9f8c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xab9f90: mov             x3, x0
    // 0xab9f94: ldur            x0, [fp, #-0x40]
    // 0xab9f98: stur            x3, [fp, #-0x48]
    // 0xab9f9c: StoreField: r3->field_b = r0
    //     0xab9f9c: stur            w0, [x3, #0xb]
    // 0xab9fa0: ldur            x2, [fp, #-0x10]
    // 0xab9fa4: r1 = Function '<anonymous closure>':.
    //     0xab9fa4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6af80] AnonymousClosure: (0xabb908), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xab9fa8: ldr             x1, [x1, #0xf80]
    // 0xab9fac: r0 = AllocateClosure()
    //     0xab9fac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab9fb0: mov             x1, x0
    // 0xab9fb4: ldur            x0, [fp, #-0x48]
    // 0xab9fb8: StoreField: r0->field_f = r1
    //     0xab9fb8: stur            w1, [x0, #0xf]
    // 0xab9fbc: r11 = true
    //     0xab9fbc: add             x11, NULL, #0x20  ; true
    // 0xab9fc0: StoreField: r0->field_43 = r11
    //     0xab9fc0: stur            w11, [x0, #0x43]
    // 0xab9fc4: r12 = Instance_BoxShape
    //     0xab9fc4: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xab9fc8: ldr             x12, [x12, #0x80]
    // 0xab9fcc: StoreField: r0->field_47 = r12
    //     0xab9fcc: stur            w12, [x0, #0x47]
    // 0xab9fd0: StoreField: r0->field_6f = r11
    //     0xab9fd0: stur            w11, [x0, #0x6f]
    // 0xab9fd4: r13 = false
    //     0xab9fd4: add             x13, NULL, #0x30  ; false
    // 0xab9fd8: StoreField: r0->field_73 = r13
    //     0xab9fd8: stur            w13, [x0, #0x73]
    // 0xab9fdc: StoreField: r0->field_83 = r11
    //     0xab9fdc: stur            w11, [x0, #0x83]
    // 0xab9fe0: StoreField: r0->field_7b = r13
    //     0xab9fe0: stur            w13, [x0, #0x7b]
    // 0xab9fe4: r1 = <FlexParentData>
    //     0xab9fe4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xab9fe8: ldr             x1, [x1, #0xe00]
    // 0xab9fec: r0 = Expanded()
    //     0xab9fec: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xab9ff0: r14 = 1
    //     0xab9ff0: movz            x14, #0x1
    // 0xab9ff4: stur            x0, [fp, #-0x40]
    // 0xab9ff8: StoreField: r0->field_13 = r14
    //     0xab9ff8: stur            x14, [x0, #0x13]
    // 0xab9ffc: r19 = Instance_FlexFit
    //     0xab9ffc: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaba000: ldr             x19, [x19, #0xe08]
    // 0xaba004: StoreField: r0->field_1b = r19
    //     0xaba004: stur            w19, [x0, #0x1b]
    // 0xaba008: ldur            x1, [fp, #-0x48]
    // 0xaba00c: StoreField: r0->field_b = r1
    //     0xaba00c: stur            w1, [x0, #0xb]
    // 0xaba010: r1 = Null
    //     0xaba010: mov             x1, NULL
    // 0xaba014: r2 = 8
    //     0xaba014: movz            x2, #0x8
    // 0xaba018: r0 = AllocateArray()
    //     0xaba018: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaba01c: stur            x0, [fp, #-0x48]
    // 0xaba020: r16 = Instance_Expanded
    //     0xaba020: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a600] Obj!Expanded@d656d1
    //     0xaba024: ldr             x16, [x16, #0x600]
    // 0xaba028: StoreField: r0->field_f = r16
    //     0xaba028: stur            w16, [x0, #0xf]
    // 0xaba02c: ldur            x1, [fp, #-0x38]
    // 0xaba030: StoreField: r0->field_13 = r1
    //     0xaba030: stur            w1, [x0, #0x13]
    // 0xaba034: r16 = Instance_Spacer
    //     0xaba034: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xaba038: ldr             x16, [x16, #0xf0]
    // 0xaba03c: ArrayStore: r0[0] = r16  ; List_4
    //     0xaba03c: stur            w16, [x0, #0x17]
    // 0xaba040: ldur            x1, [fp, #-0x40]
    // 0xaba044: StoreField: r0->field_1b = r1
    //     0xaba044: stur            w1, [x0, #0x1b]
    // 0xaba048: r1 = <Widget>
    //     0xaba048: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaba04c: r0 = AllocateGrowableArray()
    //     0xaba04c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaba050: mov             x1, x0
    // 0xaba054: ldur            x0, [fp, #-0x48]
    // 0xaba058: stur            x1, [fp, #-0x38]
    // 0xaba05c: StoreField: r1->field_f = r0
    //     0xaba05c: stur            w0, [x1, #0xf]
    // 0xaba060: r20 = 8
    //     0xaba060: movz            x20, #0x8
    // 0xaba064: StoreField: r1->field_b = r20
    //     0xaba064: stur            w20, [x1, #0xb]
    // 0xaba068: r0 = Row()
    //     0xaba068: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaba06c: r23 = Instance_Axis
    //     0xaba06c: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaba070: stur            x0, [fp, #-0x40]
    // 0xaba074: StoreField: r0->field_f = r23
    //     0xaba074: stur            w23, [x0, #0xf]
    // 0xaba078: r24 = Instance_MainAxisAlignment
    //     0xaba078: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaba07c: ldr             x24, [x24, #0xa08]
    // 0xaba080: StoreField: r0->field_13 = r24
    //     0xaba080: stur            w24, [x0, #0x13]
    // 0xaba084: r25 = Instance_MainAxisSize
    //     0xaba084: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaba088: ldr             x25, [x25, #0xa10]
    // 0xaba08c: ArrayStore: r0[0] = r25  ; List_4
    //     0xaba08c: stur            w25, [x0, #0x17]
    // 0xaba090: r1 = Instance_CrossAxisAlignment
    //     0xaba090: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaba094: ldr             x1, [x1, #0xa18]
    // 0xaba098: StoreField: r0->field_1b = r1
    //     0xaba098: stur            w1, [x0, #0x1b]
    // 0xaba09c: r1 = Instance_VerticalDirection
    //     0xaba09c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaba0a0: ldr             x1, [x1, #0xa20]
    // 0xaba0a4: StoreField: r0->field_23 = r1
    //     0xaba0a4: stur            w1, [x0, #0x23]
    // 0xaba0a8: r1 = Instance_Clip
    //     0xaba0a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaba0ac: ldr             x1, [x1, #0x38]
    // 0xaba0b0: StoreField: r0->field_2b = r1
    //     0xaba0b0: stur            w1, [x0, #0x2b]
    // 0xaba0b4: StoreField: r0->field_2f = rZR
    //     0xaba0b4: stur            xzr, [x0, #0x2f]
    // 0xaba0b8: ldur            x1, [fp, #-0x38]
    // 0xaba0bc: StoreField: r0->field_b = r1
    //     0xaba0bc: stur            w1, [x0, #0xb]
    // 0xaba0c0: r0 = Padding()
    //     0xaba0c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaba0c4: mov             x1, x0
    // 0xaba0c8: r0 = Instance_EdgeInsets
    //     0xaba0c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaba0cc: ldr             x0, [x0, #0x980]
    // 0xaba0d0: stur            x1, [fp, #-0x38]
    // 0xaba0d4: StoreField: r1->field_f = r0
    //     0xaba0d4: stur            w0, [x1, #0xf]
    // 0xaba0d8: ldur            x2, [fp, #-0x40]
    // 0xaba0dc: StoreField: r1->field_b = r2
    //     0xaba0dc: stur            w2, [x1, #0xb]
    // 0xaba0e0: r0 = Container()
    //     0xaba0e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaba0e4: stur            x0, [fp, #-0x40]
    // 0xaba0e8: r16 = 100.000000
    //     0xaba0e8: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xaba0ec: ldur            lr, [fp, #-0x30]
    // 0xaba0f0: stp             lr, x16, [SP, #8]
    // 0xaba0f4: ldur            x16, [fp, #-0x38]
    // 0xaba0f8: str             x16, [SP]
    // 0xaba0fc: mov             x1, x0
    // 0xaba100: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xaba100: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xaba104: ldr             x4, [x4, #0xc78]
    // 0xaba108: r0 = Container()
    //     0xaba108: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaba10c: r1 = <Path>
    //     0xaba10c: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xaba110: ldr             x1, [x1, #0xd30]
    // 0xaba114: r0 = MovieTicketClipper()
    //     0xaba114: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xaba118: stur            x0, [fp, #-0x30]
    // 0xaba11c: r0 = ClipPath()
    //     0xaba11c: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xaba120: mov             x1, x0
    // 0xaba124: ldur            x0, [fp, #-0x30]
    // 0xaba128: StoreField: r1->field_f = r0
    //     0xaba128: stur            w0, [x1, #0xf]
    // 0xaba12c: r0 = Instance_Clip
    //     0xaba12c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xaba130: ldr             x0, [x0, #0x138]
    // 0xaba134: StoreField: r1->field_13 = r0
    //     0xaba134: stur            w0, [x1, #0x13]
    // 0xaba138: ldur            x0, [fp, #-0x40]
    // 0xaba13c: StoreField: r1->field_b = r0
    //     0xaba13c: stur            w0, [x1, #0xb]
    // 0xaba140: mov             x0, x1
    // 0xaba144: b               #0xabb1b8
    // 0xaba148: ldur            x8, [fp, #-8]
    // 0xaba14c: r11 = true
    //     0xaba14c: add             x11, NULL, #0x20  ; true
    // 0xaba150: r5 = 4
    //     0xaba150: movz            x5, #0x4
    // 0xaba154: r0 = "Know "
    //     0xaba154: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xaba158: ldr             x0, [x0, #0x5d0]
    // 0xaba15c: r2 = "More"
    //     0xaba15c: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xaba160: ldr             x2, [x2, #0x5d8]
    // 0xaba164: r4 = Instance_EdgeInsets
    //     0xaba164: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xaba168: ldr             x4, [x4, #0xc40]
    // 0xaba16c: r7 = Instance_CrossAxisAlignment
    //     0xaba16c: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaba170: ldr             x7, [x7, #0x890]
    // 0xaba174: r24 = Instance_MainAxisAlignment
    //     0xaba174: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaba178: ldr             x24, [x24, #0xa08]
    // 0xaba17c: r13 = false
    //     0xaba17c: add             x13, NULL, #0x30  ; false
    // 0xaba180: r10 = "APPLY"
    //     0xaba180: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xaba184: ldr             x10, [x10, #0x5f0]
    // 0xaba188: r9 = "APPLIED"
    //     0xaba188: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xaba18c: ldr             x9, [x9, #0x5e8]
    // 0xaba190: r25 = Instance_MainAxisSize
    //     0xaba190: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaba194: ldr             x25, [x25, #0xa10]
    // 0xaba198: r12 = Instance_BoxShape
    //     0xaba198: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaba19c: ldr             x12, [x12, #0x80]
    // 0xaba1a0: r19 = Instance_FlexFit
    //     0xaba1a0: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaba1a4: ldr             x19, [x19, #0xe08]
    // 0xaba1a8: r20 = 8
    //     0xaba1a8: movz            x20, #0x8
    // 0xaba1ac: r1 = Instance_CrossAxisAlignment
    //     0xaba1ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaba1b0: ldr             x1, [x1, #0xa18]
    // 0xaba1b4: r23 = Instance_Axis
    //     0xaba1b4: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaba1b8: r3 = Instance__DeferringMouseCursor
    //     0xaba1b8: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaba1bc: r6 = Instance_Axis
    //     0xaba1bc: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaba1c0: d1 = 1.000000
    //     0xaba1c0: fmov            d1, #1.00000000
    // 0xaba1c4: d2 = 0.500000
    //     0xaba1c4: fmov            d2, #0.50000000
    // 0xaba1c8: r14 = 1
    //     0xaba1c8: movz            x14, #0x1
    // 0xaba1cc: b               #0xaba254
    // 0xaba1d0: ldur            x8, [fp, #-8]
    // 0xaba1d4: r11 = true
    //     0xaba1d4: add             x11, NULL, #0x20  ; true
    // 0xaba1d8: r5 = 4
    //     0xaba1d8: movz            x5, #0x4
    // 0xaba1dc: r0 = "Know "
    //     0xaba1dc: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xaba1e0: ldr             x0, [x0, #0x5d0]
    // 0xaba1e4: r2 = "More"
    //     0xaba1e4: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xaba1e8: ldr             x2, [x2, #0x5d8]
    // 0xaba1ec: r4 = Instance_EdgeInsets
    //     0xaba1ec: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xaba1f0: ldr             x4, [x4, #0xc40]
    // 0xaba1f4: r7 = Instance_CrossAxisAlignment
    //     0xaba1f4: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaba1f8: ldr             x7, [x7, #0x890]
    // 0xaba1fc: r24 = Instance_MainAxisAlignment
    //     0xaba1fc: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaba200: ldr             x24, [x24, #0xa08]
    // 0xaba204: r13 = false
    //     0xaba204: add             x13, NULL, #0x30  ; false
    // 0xaba208: r10 = "APPLY"
    //     0xaba208: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xaba20c: ldr             x10, [x10, #0x5f0]
    // 0xaba210: r9 = "APPLIED"
    //     0xaba210: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xaba214: ldr             x9, [x9, #0x5e8]
    // 0xaba218: r25 = Instance_MainAxisSize
    //     0xaba218: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaba21c: ldr             x25, [x25, #0xa10]
    // 0xaba220: r12 = Instance_BoxShape
    //     0xaba220: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaba224: ldr             x12, [x12, #0x80]
    // 0xaba228: r19 = Instance_FlexFit
    //     0xaba228: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaba22c: ldr             x19, [x19, #0xe08]
    // 0xaba230: r20 = 8
    //     0xaba230: movz            x20, #0x8
    // 0xaba234: r1 = Instance_CrossAxisAlignment
    //     0xaba234: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaba238: ldr             x1, [x1, #0xa18]
    // 0xaba23c: r23 = Instance_Axis
    //     0xaba23c: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaba240: r3 = Instance__DeferringMouseCursor
    //     0xaba240: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaba244: r6 = Instance_Axis
    //     0xaba244: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaba248: d1 = 1.000000
    //     0xaba248: fmov            d1, #1.00000000
    // 0xaba24c: d2 = 0.500000
    //     0xaba24c: fmov            d2, #0.50000000
    // 0xaba250: r14 = 1
    //     0xaba250: movz            x14, #0x1
    // 0xaba254: r1 = Instance_MaterialColor
    //     0xaba254: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xaba258: ldr             x1, [x1, #0xdc0]
    // 0xaba25c: d0 = 0.300000
    //     0xaba25c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xaba260: ldr             d0, [x17, #0x658]
    // 0xaba264: r0 = withOpacity()
    //     0xaba264: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaba268: mov             x3, x0
    // 0xaba26c: ldur            x2, [fp, #-8]
    // 0xaba270: stur            x3, [fp, #-0x30]
    // 0xaba274: LoadField: r0 = r2->field_f
    //     0xaba274: ldur            w0, [x2, #0xf]
    // 0xaba278: DecompressPointer r0
    //     0xaba278: add             x0, x0, HEAP, lsl #32
    // 0xaba27c: LoadField: r1 = r0->field_b
    //     0xaba27c: ldur            w1, [x0, #0xb]
    // 0xaba280: DecompressPointer r1
    //     0xaba280: add             x1, x1, HEAP, lsl #32
    // 0xaba284: cmp             w1, NULL
    // 0xaba288: b.eq            #0xabb244
    // 0xaba28c: LoadField: r0 = r1->field_f
    //     0xaba28c: ldur            w0, [x1, #0xf]
    // 0xaba290: DecompressPointer r0
    //     0xaba290: add             x0, x0, HEAP, lsl #32
    // 0xaba294: cmp             w0, NULL
    // 0xaba298: b.ne            #0xaba2a8
    // 0xaba29c: ldur            x4, [fp, #-0x10]
    // 0xaba2a0: r0 = Null
    //     0xaba2a0: mov             x0, NULL
    // 0xaba2a4: b               #0xaba2f8
    // 0xaba2a8: ldur            x4, [fp, #-0x10]
    // 0xaba2ac: LoadField: r5 = r0->field_f
    //     0xaba2ac: ldur            w5, [x0, #0xf]
    // 0xaba2b0: DecompressPointer r5
    //     0xaba2b0: add             x5, x5, HEAP, lsl #32
    // 0xaba2b4: LoadField: r0 = r4->field_13
    //     0xaba2b4: ldur            w0, [x4, #0x13]
    // 0xaba2b8: DecompressPointer r0
    //     0xaba2b8: add             x0, x0, HEAP, lsl #32
    // 0xaba2bc: LoadField: r1 = r5->field_b
    //     0xaba2bc: ldur            w1, [x5, #0xb]
    // 0xaba2c0: r6 = LoadInt32Instr(r0)
    //     0xaba2c0: sbfx            x6, x0, #1, #0x1f
    //     0xaba2c4: tbz             w0, #0, #0xaba2cc
    //     0xaba2c8: ldur            x6, [x0, #7]
    // 0xaba2cc: r0 = LoadInt32Instr(r1)
    //     0xaba2cc: sbfx            x0, x1, #1, #0x1f
    // 0xaba2d0: mov             x1, x6
    // 0xaba2d4: cmp             x1, x0
    // 0xaba2d8: b.hs            #0xabb248
    // 0xaba2dc: LoadField: r0 = r5->field_f
    //     0xaba2dc: ldur            w0, [x5, #0xf]
    // 0xaba2e0: DecompressPointer r0
    //     0xaba2e0: add             x0, x0, HEAP, lsl #32
    // 0xaba2e4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaba2e4: add             x16, x0, x6, lsl #2
    //     0xaba2e8: ldur            w1, [x16, #0xf]
    // 0xaba2ec: DecompressPointer r1
    //     0xaba2ec: add             x1, x1, HEAP, lsl #32
    // 0xaba2f0: LoadField: r0 = r1->field_b
    //     0xaba2f0: ldur            w0, [x1, #0xb]
    // 0xaba2f4: DecompressPointer r0
    //     0xaba2f4: add             x0, x0, HEAP, lsl #32
    // 0xaba2f8: r1 = LoadClassIdInstr(r0)
    //     0xaba2f8: ldur            x1, [x0, #-1]
    //     0xaba2fc: ubfx            x1, x1, #0xc, #0x14
    // 0xaba300: r16 = "free_gift"
    //     0xaba300: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a608] "free_gift"
    //     0xaba304: ldr             x16, [x16, #0x608]
    // 0xaba308: stp             x16, x0, [SP]
    // 0xaba30c: mov             x0, x1
    // 0xaba310: mov             lr, x0
    // 0xaba314: ldr             lr, [x21, lr, lsl #3]
    // 0xaba318: blr             lr
    // 0xaba31c: tbz             w0, #4, #0xaba484
    // 0xaba320: ldur            x2, [fp, #-8]
    // 0xaba324: LoadField: r0 = r2->field_f
    //     0xaba324: ldur            w0, [x2, #0xf]
    // 0xaba328: DecompressPointer r0
    //     0xaba328: add             x0, x0, HEAP, lsl #32
    // 0xaba32c: LoadField: r3 = r0->field_b
    //     0xaba32c: ldur            w3, [x0, #0xb]
    // 0xaba330: DecompressPointer r3
    //     0xaba330: add             x3, x3, HEAP, lsl #32
    // 0xaba334: cmp             w3, NULL
    // 0xaba338: b.eq            #0xabb24c
    // 0xaba33c: LoadField: r0 = r3->field_f
    //     0xaba33c: ldur            w0, [x3, #0xf]
    // 0xaba340: DecompressPointer r0
    //     0xaba340: add             x0, x0, HEAP, lsl #32
    // 0xaba344: cmp             w0, NULL
    // 0xaba348: b.ne            #0xaba358
    // 0xaba34c: ldur            x4, [fp, #-0x10]
    // 0xaba350: r0 = Null
    //     0xaba350: mov             x0, NULL
    // 0xaba354: b               #0xaba3a8
    // 0xaba358: ldur            x4, [fp, #-0x10]
    // 0xaba35c: LoadField: r5 = r0->field_f
    //     0xaba35c: ldur            w5, [x0, #0xf]
    // 0xaba360: DecompressPointer r5
    //     0xaba360: add             x5, x5, HEAP, lsl #32
    // 0xaba364: LoadField: r0 = r4->field_13
    //     0xaba364: ldur            w0, [x4, #0x13]
    // 0xaba368: DecompressPointer r0
    //     0xaba368: add             x0, x0, HEAP, lsl #32
    // 0xaba36c: LoadField: r1 = r5->field_b
    //     0xaba36c: ldur            w1, [x5, #0xb]
    // 0xaba370: r6 = LoadInt32Instr(r0)
    //     0xaba370: sbfx            x6, x0, #1, #0x1f
    //     0xaba374: tbz             w0, #0, #0xaba37c
    //     0xaba378: ldur            x6, [x0, #7]
    // 0xaba37c: r0 = LoadInt32Instr(r1)
    //     0xaba37c: sbfx            x0, x1, #1, #0x1f
    // 0xaba380: mov             x1, x6
    // 0xaba384: cmp             x1, x0
    // 0xaba388: b.hs            #0xabb250
    // 0xaba38c: LoadField: r0 = r5->field_f
    //     0xaba38c: ldur            w0, [x5, #0xf]
    // 0xaba390: DecompressPointer r0
    //     0xaba390: add             x0, x0, HEAP, lsl #32
    // 0xaba394: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaba394: add             x16, x0, x6, lsl #2
    //     0xaba398: ldur            w1, [x16, #0xf]
    // 0xaba39c: DecompressPointer r1
    //     0xaba39c: add             x1, x1, HEAP, lsl #32
    // 0xaba3a0: LoadField: r0 = r1->field_27
    //     0xaba3a0: ldur            w0, [x1, #0x27]
    // 0xaba3a4: DecompressPointer r0
    //     0xaba3a4: add             x0, x0, HEAP, lsl #32
    // 0xaba3a8: cmp             w0, NULL
    // 0xaba3ac: b.ne            #0xaba440
    // 0xaba3b0: LoadField: r0 = r3->field_f
    //     0xaba3b0: ldur            w0, [x3, #0xf]
    // 0xaba3b4: DecompressPointer r0
    //     0xaba3b4: add             x0, x0, HEAP, lsl #32
    // 0xaba3b8: cmp             w0, NULL
    // 0xaba3bc: b.ne            #0xaba3c8
    // 0xaba3c0: r0 = Null
    //     0xaba3c0: mov             x0, NULL
    // 0xaba3c4: b               #0xaba414
    // 0xaba3c8: LoadField: r3 = r0->field_f
    //     0xaba3c8: ldur            w3, [x0, #0xf]
    // 0xaba3cc: DecompressPointer r3
    //     0xaba3cc: add             x3, x3, HEAP, lsl #32
    // 0xaba3d0: LoadField: r0 = r4->field_13
    //     0xaba3d0: ldur            w0, [x4, #0x13]
    // 0xaba3d4: DecompressPointer r0
    //     0xaba3d4: add             x0, x0, HEAP, lsl #32
    // 0xaba3d8: LoadField: r1 = r3->field_b
    //     0xaba3d8: ldur            w1, [x3, #0xb]
    // 0xaba3dc: r5 = LoadInt32Instr(r0)
    //     0xaba3dc: sbfx            x5, x0, #1, #0x1f
    //     0xaba3e0: tbz             w0, #0, #0xaba3e8
    //     0xaba3e4: ldur            x5, [x0, #7]
    // 0xaba3e8: r0 = LoadInt32Instr(r1)
    //     0xaba3e8: sbfx            x0, x1, #1, #0x1f
    // 0xaba3ec: mov             x1, x5
    // 0xaba3f0: cmp             x1, x0
    // 0xaba3f4: b.hs            #0xabb254
    // 0xaba3f8: LoadField: r0 = r3->field_f
    //     0xaba3f8: ldur            w0, [x3, #0xf]
    // 0xaba3fc: DecompressPointer r0
    //     0xaba3fc: add             x0, x0, HEAP, lsl #32
    // 0xaba400: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaba400: add             x16, x0, x5, lsl #2
    //     0xaba404: ldur            w1, [x16, #0xf]
    // 0xaba408: DecompressPointer r1
    //     0xaba408: add             x1, x1, HEAP, lsl #32
    // 0xaba40c: LoadField: r0 = r1->field_f
    //     0xaba40c: ldur            w0, [x1, #0xf]
    // 0xaba410: DecompressPointer r0
    //     0xaba410: add             x0, x0, HEAP, lsl #32
    // 0xaba414: r1 = LoadClassIdInstr(r0)
    //     0xaba414: ldur            x1, [x0, #-1]
    //     0xaba418: ubfx            x1, x1, #0xc, #0x14
    // 0xaba41c: r16 = "sale_event_static_coupon"
    //     0xaba41c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xaba420: ldr             x16, [x16, #0x610]
    // 0xaba424: stp             x16, x0, [SP]
    // 0xaba428: mov             x0, x1
    // 0xaba42c: mov             lr, x0
    // 0xaba430: ldr             lr, [x21, lr, lsl #3]
    // 0xaba434: blr             lr
    // 0xaba438: tbnz            w0, #4, #0xaba450
    // 0xaba43c: b               #0xaba444
    // 0xaba440: tbnz            w0, #4, #0xaba450
    // 0xaba444: r0 = Instance_Color
    //     0xaba444: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xaba448: ldr             x0, [x0, #0x858]
    // 0xaba44c: b               #0xaba45c
    // 0xaba450: r1 = Instance_Color
    //     0xaba450: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaba454: d0 = 0.400000
    //     0xaba454: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaba458: r0 = withOpacity()
    //     0xaba458: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaba45c: stur            x0, [fp, #-0x38]
    // 0xaba460: r0 = Icon()
    //     0xaba460: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xaba464: mov             x1, x0
    // 0xaba468: r0 = Instance_IconData
    //     0xaba468: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a618] Obj!IconData@d55541
    //     0xaba46c: ldr             x0, [x0, #0x618]
    // 0xaba470: StoreField: r1->field_b = r0
    //     0xaba470: stur            w0, [x1, #0xb]
    // 0xaba474: ldur            x0, [fp, #-0x38]
    // 0xaba478: StoreField: r1->field_23 = r0
    //     0xaba478: stur            w0, [x1, #0x23]
    // 0xaba47c: mov             x3, x1
    // 0xaba480: b               #0xaba4bc
    // 0xaba484: r0 = SvgPicture()
    //     0xaba484: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaba488: stur            x0, [fp, #-0x38]
    // 0xaba48c: r16 = 23.000000
    //     0xaba48c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aae8] 23
    //     0xaba490: ldr             x16, [x16, #0xae8]
    // 0xaba494: r30 = 23.000000
    //     0xaba494: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6aae8] 23
    //     0xaba498: ldr             lr, [lr, #0xae8]
    // 0xaba49c: stp             lr, x16, [SP]
    // 0xaba4a0: mov             x1, x0
    // 0xaba4a4: r2 = "assets/images/gift-icon-popup.svg"
    //     0xaba4a4: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xaba4a8: ldr             x2, [x2, #0x8e8]
    // 0xaba4ac: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xaba4ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xaba4b0: ldr             x4, [x4, #0x900]
    // 0xaba4b4: r0 = SvgPicture.asset()
    //     0xaba4b4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaba4b8: ldur            x3, [fp, #-0x38]
    // 0xaba4bc: ldur            x0, [fp, #-8]
    // 0xaba4c0: ldur            x2, [fp, #-0x10]
    // 0xaba4c4: stur            x3, [fp, #-0x38]
    // 0xaba4c8: r1 = <FlexParentData>
    //     0xaba4c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaba4cc: ldr             x1, [x1, #0xe00]
    // 0xaba4d0: r0 = Expanded()
    //     0xaba4d0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaba4d4: stur            x0, [fp, #-0x40]
    // 0xaba4d8: StoreField: r0->field_13 = rZR
    //     0xaba4d8: stur            xzr, [x0, #0x13]
    // 0xaba4dc: r2 = Instance_FlexFit
    //     0xaba4dc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaba4e0: ldr             x2, [x2, #0xe08]
    // 0xaba4e4: StoreField: r0->field_1b = r2
    //     0xaba4e4: stur            w2, [x0, #0x1b]
    // 0xaba4e8: ldur            x1, [fp, #-0x38]
    // 0xaba4ec: StoreField: r0->field_b = r1
    //     0xaba4ec: stur            w1, [x0, #0xb]
    // 0xaba4f0: ldur            x3, [fp, #-0x10]
    // 0xaba4f4: LoadField: r1 = r3->field_f
    //     0xaba4f4: ldur            w1, [x3, #0xf]
    // 0xaba4f8: DecompressPointer r1
    //     0xaba4f8: add             x1, x1, HEAP, lsl #32
    // 0xaba4fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaba4fc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaba500: r0 = _of()
    //     0xaba500: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaba504: LoadField: r1 = r0->field_7
    //     0xaba504: ldur            w1, [x0, #7]
    // 0xaba508: DecompressPointer r1
    //     0xaba508: add             x1, x1, HEAP, lsl #32
    // 0xaba50c: LoadField: d0 = r1->field_7
    //     0xaba50c: ldur            d0, [x1, #7]
    // 0xaba510: d1 = 0.500000
    //     0xaba510: fmov            d1, #0.50000000
    // 0xaba514: fmul            d2, d0, d1
    // 0xaba518: ldur            x2, [fp, #-8]
    // 0xaba51c: stur            d2, [fp, #-0x68]
    // 0xaba520: LoadField: r0 = r2->field_f
    //     0xaba520: ldur            w0, [x2, #0xf]
    // 0xaba524: DecompressPointer r0
    //     0xaba524: add             x0, x0, HEAP, lsl #32
    // 0xaba528: LoadField: r1 = r0->field_b
    //     0xaba528: ldur            w1, [x0, #0xb]
    // 0xaba52c: DecompressPointer r1
    //     0xaba52c: add             x1, x1, HEAP, lsl #32
    // 0xaba530: cmp             w1, NULL
    // 0xaba534: b.eq            #0xabb258
    // 0xaba538: LoadField: r0 = r1->field_f
    //     0xaba538: ldur            w0, [x1, #0xf]
    // 0xaba53c: DecompressPointer r0
    //     0xaba53c: add             x0, x0, HEAP, lsl #32
    // 0xaba540: cmp             w0, NULL
    // 0xaba544: b.ne            #0xaba554
    // 0xaba548: ldur            x3, [fp, #-0x10]
    // 0xaba54c: r0 = Null
    //     0xaba54c: mov             x0, NULL
    // 0xaba550: b               #0xaba5a4
    // 0xaba554: ldur            x3, [fp, #-0x10]
    // 0xaba558: LoadField: r4 = r0->field_f
    //     0xaba558: ldur            w4, [x0, #0xf]
    // 0xaba55c: DecompressPointer r4
    //     0xaba55c: add             x4, x4, HEAP, lsl #32
    // 0xaba560: LoadField: r0 = r3->field_13
    //     0xaba560: ldur            w0, [x3, #0x13]
    // 0xaba564: DecompressPointer r0
    //     0xaba564: add             x0, x0, HEAP, lsl #32
    // 0xaba568: LoadField: r1 = r4->field_b
    //     0xaba568: ldur            w1, [x4, #0xb]
    // 0xaba56c: r5 = LoadInt32Instr(r0)
    //     0xaba56c: sbfx            x5, x0, #1, #0x1f
    //     0xaba570: tbz             w0, #0, #0xaba578
    //     0xaba574: ldur            x5, [x0, #7]
    // 0xaba578: r0 = LoadInt32Instr(r1)
    //     0xaba578: sbfx            x0, x1, #1, #0x1f
    // 0xaba57c: mov             x1, x5
    // 0xaba580: cmp             x1, x0
    // 0xaba584: b.hs            #0xabb25c
    // 0xaba588: LoadField: r0 = r4->field_f
    //     0xaba588: ldur            w0, [x4, #0xf]
    // 0xaba58c: DecompressPointer r0
    //     0xaba58c: add             x0, x0, HEAP, lsl #32
    // 0xaba590: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaba590: add             x16, x0, x5, lsl #2
    //     0xaba594: ldur            w1, [x16, #0xf]
    // 0xaba598: DecompressPointer r1
    //     0xaba598: add             x1, x1, HEAP, lsl #32
    // 0xaba59c: LoadField: r0 = r1->field_13
    //     0xaba59c: ldur            w0, [x1, #0x13]
    // 0xaba5a0: DecompressPointer r0
    //     0xaba5a0: add             x0, x0, HEAP, lsl #32
    // 0xaba5a4: cmp             w0, NULL
    // 0xaba5a8: b.ne            #0xaba5b0
    // 0xaba5ac: r0 = ""
    //     0xaba5ac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaba5b0: stur            x0, [fp, #-0x38]
    // 0xaba5b4: LoadField: r1 = r3->field_f
    //     0xaba5b4: ldur            w1, [x3, #0xf]
    // 0xaba5b8: DecompressPointer r1
    //     0xaba5b8: add             x1, x1, HEAP, lsl #32
    // 0xaba5bc: r0 = of()
    //     0xaba5bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaba5c0: LoadField: r1 = r0->field_87
    //     0xaba5c0: ldur            w1, [x0, #0x87]
    // 0xaba5c4: DecompressPointer r1
    //     0xaba5c4: add             x1, x1, HEAP, lsl #32
    // 0xaba5c8: LoadField: r2 = r1->field_2b
    //     0xaba5c8: ldur            w2, [x1, #0x2b]
    // 0xaba5cc: DecompressPointer r2
    //     0xaba5cc: add             x2, x2, HEAP, lsl #32
    // 0xaba5d0: ldur            x3, [fp, #-8]
    // 0xaba5d4: stur            x2, [fp, #-0x48]
    // 0xaba5d8: LoadField: r0 = r3->field_f
    //     0xaba5d8: ldur            w0, [x3, #0xf]
    // 0xaba5dc: DecompressPointer r0
    //     0xaba5dc: add             x0, x0, HEAP, lsl #32
    // 0xaba5e0: LoadField: r4 = r0->field_b
    //     0xaba5e0: ldur            w4, [x0, #0xb]
    // 0xaba5e4: DecompressPointer r4
    //     0xaba5e4: add             x4, x4, HEAP, lsl #32
    // 0xaba5e8: cmp             w4, NULL
    // 0xaba5ec: b.eq            #0xabb260
    // 0xaba5f0: LoadField: r0 = r4->field_f
    //     0xaba5f0: ldur            w0, [x4, #0xf]
    // 0xaba5f4: DecompressPointer r0
    //     0xaba5f4: add             x0, x0, HEAP, lsl #32
    // 0xaba5f8: cmp             w0, NULL
    // 0xaba5fc: b.ne            #0xaba60c
    // 0xaba600: ldur            x5, [fp, #-0x10]
    // 0xaba604: r0 = Null
    //     0xaba604: mov             x0, NULL
    // 0xaba608: b               #0xaba65c
    // 0xaba60c: ldur            x5, [fp, #-0x10]
    // 0xaba610: LoadField: r6 = r0->field_f
    //     0xaba610: ldur            w6, [x0, #0xf]
    // 0xaba614: DecompressPointer r6
    //     0xaba614: add             x6, x6, HEAP, lsl #32
    // 0xaba618: LoadField: r0 = r5->field_13
    //     0xaba618: ldur            w0, [x5, #0x13]
    // 0xaba61c: DecompressPointer r0
    //     0xaba61c: add             x0, x0, HEAP, lsl #32
    // 0xaba620: LoadField: r1 = r6->field_b
    //     0xaba620: ldur            w1, [x6, #0xb]
    // 0xaba624: r7 = LoadInt32Instr(r0)
    //     0xaba624: sbfx            x7, x0, #1, #0x1f
    //     0xaba628: tbz             w0, #0, #0xaba630
    //     0xaba62c: ldur            x7, [x0, #7]
    // 0xaba630: r0 = LoadInt32Instr(r1)
    //     0xaba630: sbfx            x0, x1, #1, #0x1f
    // 0xaba634: mov             x1, x7
    // 0xaba638: cmp             x1, x0
    // 0xaba63c: b.hs            #0xabb264
    // 0xaba640: LoadField: r0 = r6->field_f
    //     0xaba640: ldur            w0, [x6, #0xf]
    // 0xaba644: DecompressPointer r0
    //     0xaba644: add             x0, x0, HEAP, lsl #32
    // 0xaba648: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaba648: add             x16, x0, x7, lsl #2
    //     0xaba64c: ldur            w1, [x16, #0xf]
    // 0xaba650: DecompressPointer r1
    //     0xaba650: add             x1, x1, HEAP, lsl #32
    // 0xaba654: LoadField: r0 = r1->field_27
    //     0xaba654: ldur            w0, [x1, #0x27]
    // 0xaba658: DecompressPointer r0
    //     0xaba658: add             x0, x0, HEAP, lsl #32
    // 0xaba65c: cmp             w0, NULL
    // 0xaba660: b.ne            #0xaba6f4
    // 0xaba664: LoadField: r0 = r4->field_f
    //     0xaba664: ldur            w0, [x4, #0xf]
    // 0xaba668: DecompressPointer r0
    //     0xaba668: add             x0, x0, HEAP, lsl #32
    // 0xaba66c: cmp             w0, NULL
    // 0xaba670: b.ne            #0xaba67c
    // 0xaba674: r0 = Null
    //     0xaba674: mov             x0, NULL
    // 0xaba678: b               #0xaba6c8
    // 0xaba67c: LoadField: r4 = r0->field_f
    //     0xaba67c: ldur            w4, [x0, #0xf]
    // 0xaba680: DecompressPointer r4
    //     0xaba680: add             x4, x4, HEAP, lsl #32
    // 0xaba684: LoadField: r0 = r5->field_13
    //     0xaba684: ldur            w0, [x5, #0x13]
    // 0xaba688: DecompressPointer r0
    //     0xaba688: add             x0, x0, HEAP, lsl #32
    // 0xaba68c: LoadField: r1 = r4->field_b
    //     0xaba68c: ldur            w1, [x4, #0xb]
    // 0xaba690: r6 = LoadInt32Instr(r0)
    //     0xaba690: sbfx            x6, x0, #1, #0x1f
    //     0xaba694: tbz             w0, #0, #0xaba69c
    //     0xaba698: ldur            x6, [x0, #7]
    // 0xaba69c: r0 = LoadInt32Instr(r1)
    //     0xaba69c: sbfx            x0, x1, #1, #0x1f
    // 0xaba6a0: mov             x1, x6
    // 0xaba6a4: cmp             x1, x0
    // 0xaba6a8: b.hs            #0xabb268
    // 0xaba6ac: LoadField: r0 = r4->field_f
    //     0xaba6ac: ldur            w0, [x4, #0xf]
    // 0xaba6b0: DecompressPointer r0
    //     0xaba6b0: add             x0, x0, HEAP, lsl #32
    // 0xaba6b4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaba6b4: add             x16, x0, x6, lsl #2
    //     0xaba6b8: ldur            w1, [x16, #0xf]
    // 0xaba6bc: DecompressPointer r1
    //     0xaba6bc: add             x1, x1, HEAP, lsl #32
    // 0xaba6c0: LoadField: r0 = r1->field_f
    //     0xaba6c0: ldur            w0, [x1, #0xf]
    // 0xaba6c4: DecompressPointer r0
    //     0xaba6c4: add             x0, x0, HEAP, lsl #32
    // 0xaba6c8: r1 = LoadClassIdInstr(r0)
    //     0xaba6c8: ldur            x1, [x0, #-1]
    //     0xaba6cc: ubfx            x1, x1, #0xc, #0x14
    // 0xaba6d0: r16 = "sale_event_static_coupon"
    //     0xaba6d0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xaba6d4: ldr             x16, [x16, #0x610]
    // 0xaba6d8: stp             x16, x0, [SP]
    // 0xaba6dc: mov             x0, x1
    // 0xaba6e0: mov             lr, x0
    // 0xaba6e4: ldr             lr, [x21, lr, lsl #3]
    // 0xaba6e8: blr             lr
    // 0xaba6ec: tbnz            w0, #4, #0xaba700
    // 0xaba6f0: b               #0xaba6f8
    // 0xaba6f4: tbnz            w0, #4, #0xaba700
    // 0xaba6f8: r1 = Instance_Color
    //     0xaba6f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaba6fc: b               #0xaba710
    // 0xaba700: r1 = Instance_Color
    //     0xaba700: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaba704: d0 = 0.400000
    //     0xaba704: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaba708: r0 = withOpacity()
    //     0xaba708: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaba70c: mov             x1, x0
    // 0xaba710: ldur            x0, [fp, #-8]
    // 0xaba714: ldur            d0, [fp, #-0x68]
    // 0xaba718: ldur            x2, [fp, #-0x38]
    // 0xaba71c: r16 = 14.000000
    //     0xaba71c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaba720: ldr             x16, [x16, #0x1d8]
    // 0xaba724: stp             x16, x1, [SP]
    // 0xaba728: ldur            x1, [fp, #-0x48]
    // 0xaba72c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaba72c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaba730: ldr             x4, [x4, #0x9b8]
    // 0xaba734: r0 = copyWith()
    //     0xaba734: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaba738: stur            x0, [fp, #-0x48]
    // 0xaba73c: r0 = Text()
    //     0xaba73c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaba740: mov             x1, x0
    // 0xaba744: ldur            x0, [fp, #-0x38]
    // 0xaba748: stur            x1, [fp, #-0x50]
    // 0xaba74c: StoreField: r1->field_b = r0
    //     0xaba74c: stur            w0, [x1, #0xb]
    // 0xaba750: ldur            x0, [fp, #-0x48]
    // 0xaba754: StoreField: r1->field_13 = r0
    //     0xaba754: stur            w0, [x1, #0x13]
    // 0xaba758: r2 = 4
    //     0xaba758: movz            x2, #0x4
    // 0xaba75c: StoreField: r1->field_37 = r2
    //     0xaba75c: stur            w2, [x1, #0x37]
    // 0xaba760: r0 = Padding()
    //     0xaba760: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaba764: mov             x1, x0
    // 0xaba768: r0 = Instance_EdgeInsets
    //     0xaba768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaba76c: ldr             x0, [x0, #0x980]
    // 0xaba770: stur            x1, [fp, #-0x48]
    // 0xaba774: StoreField: r1->field_f = r0
    //     0xaba774: stur            w0, [x1, #0xf]
    // 0xaba778: ldur            x2, [fp, #-0x50]
    // 0xaba77c: StoreField: r1->field_b = r2
    //     0xaba77c: stur            w2, [x1, #0xb]
    // 0xaba780: ldur            d0, [fp, #-0x68]
    // 0xaba784: r2 = inline_Allocate_Double()
    //     0xaba784: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaba788: add             x2, x2, #0x10
    //     0xaba78c: cmp             x3, x2
    //     0xaba790: b.ls            #0xabb26c
    //     0xaba794: str             x2, [THR, #0x50]  ; THR::top
    //     0xaba798: sub             x2, x2, #0xf
    //     0xaba79c: movz            x3, #0xe15c
    //     0xaba7a0: movk            x3, #0x3, lsl #16
    //     0xaba7a4: stur            x3, [x2, #-1]
    // 0xaba7a8: StoreField: r2->field_7 = d0
    //     0xaba7a8: stur            d0, [x2, #7]
    // 0xaba7ac: stur            x2, [fp, #-0x38]
    // 0xaba7b0: r0 = SizedBox()
    //     0xaba7b0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaba7b4: mov             x2, x0
    // 0xaba7b8: ldur            x0, [fp, #-0x38]
    // 0xaba7bc: stur            x2, [fp, #-0x50]
    // 0xaba7c0: StoreField: r2->field_f = r0
    //     0xaba7c0: stur            w0, [x2, #0xf]
    // 0xaba7c4: ldur            x0, [fp, #-0x48]
    // 0xaba7c8: StoreField: r2->field_b = r0
    //     0xaba7c8: stur            w0, [x2, #0xb]
    // 0xaba7cc: ldur            x3, [fp, #-8]
    // 0xaba7d0: LoadField: r0 = r3->field_f
    //     0xaba7d0: ldur            w0, [x3, #0xf]
    // 0xaba7d4: DecompressPointer r0
    //     0xaba7d4: add             x0, x0, HEAP, lsl #32
    // 0xaba7d8: LoadField: r1 = r0->field_b
    //     0xaba7d8: ldur            w1, [x0, #0xb]
    // 0xaba7dc: DecompressPointer r1
    //     0xaba7dc: add             x1, x1, HEAP, lsl #32
    // 0xaba7e0: cmp             w1, NULL
    // 0xaba7e4: b.eq            #0xabb288
    // 0xaba7e8: LoadField: r0 = r1->field_f
    //     0xaba7e8: ldur            w0, [x1, #0xf]
    // 0xaba7ec: DecompressPointer r0
    //     0xaba7ec: add             x0, x0, HEAP, lsl #32
    // 0xaba7f0: cmp             w0, NULL
    // 0xaba7f4: b.ne            #0xaba804
    // 0xaba7f8: ldur            x4, [fp, #-0x10]
    // 0xaba7fc: r0 = Null
    //     0xaba7fc: mov             x0, NULL
    // 0xaba800: b               #0xaba854
    // 0xaba804: ldur            x4, [fp, #-0x10]
    // 0xaba808: LoadField: r5 = r0->field_f
    //     0xaba808: ldur            w5, [x0, #0xf]
    // 0xaba80c: DecompressPointer r5
    //     0xaba80c: add             x5, x5, HEAP, lsl #32
    // 0xaba810: LoadField: r0 = r4->field_13
    //     0xaba810: ldur            w0, [x4, #0x13]
    // 0xaba814: DecompressPointer r0
    //     0xaba814: add             x0, x0, HEAP, lsl #32
    // 0xaba818: LoadField: r1 = r5->field_b
    //     0xaba818: ldur            w1, [x5, #0xb]
    // 0xaba81c: r6 = LoadInt32Instr(r0)
    //     0xaba81c: sbfx            x6, x0, #1, #0x1f
    //     0xaba820: tbz             w0, #0, #0xaba828
    //     0xaba824: ldur            x6, [x0, #7]
    // 0xaba828: r0 = LoadInt32Instr(r1)
    //     0xaba828: sbfx            x0, x1, #1, #0x1f
    // 0xaba82c: mov             x1, x6
    // 0xaba830: cmp             x1, x0
    // 0xaba834: b.hs            #0xabb28c
    // 0xaba838: LoadField: r0 = r5->field_f
    //     0xaba838: ldur            w0, [x5, #0xf]
    // 0xaba83c: DecompressPointer r0
    //     0xaba83c: add             x0, x0, HEAP, lsl #32
    // 0xaba840: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaba840: add             x16, x0, x6, lsl #2
    //     0xaba844: ldur            w1, [x16, #0xf]
    // 0xaba848: DecompressPointer r1
    //     0xaba848: add             x1, x1, HEAP, lsl #32
    // 0xaba84c: LoadField: r0 = r1->field_f
    //     0xaba84c: ldur            w0, [x1, #0xf]
    // 0xaba850: DecompressPointer r0
    //     0xaba850: add             x0, x0, HEAP, lsl #32
    // 0xaba854: r1 = LoadClassIdInstr(r0)
    //     0xaba854: ldur            x1, [x0, #-1]
    //     0xaba858: ubfx            x1, x1, #0xc, #0x14
    // 0xaba85c: r16 = "sale_event_static_coupon"
    //     0xaba85c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xaba860: ldr             x16, [x16, #0x610]
    // 0xaba864: stp             x16, x0, [SP]
    // 0xaba868: mov             x0, x1
    // 0xaba86c: mov             lr, x0
    // 0xaba870: ldr             lr, [x21, lr, lsl #3]
    // 0xaba874: blr             lr
    // 0xaba878: eor             x2, x0, #0x10
    // 0xaba87c: ldur            x0, [fp, #-0x10]
    // 0xaba880: stur            x2, [fp, #-0x38]
    // 0xaba884: LoadField: r1 = r0->field_f
    //     0xaba884: ldur            w1, [x0, #0xf]
    // 0xaba888: DecompressPointer r1
    //     0xaba888: add             x1, x1, HEAP, lsl #32
    // 0xaba88c: r0 = of()
    //     0xaba88c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaba890: LoadField: r1 = r0->field_87
    //     0xaba890: ldur            w1, [x0, #0x87]
    // 0xaba894: DecompressPointer r1
    //     0xaba894: add             x1, x1, HEAP, lsl #32
    // 0xaba898: LoadField: r0 = r1->field_7
    //     0xaba898: ldur            w0, [x1, #7]
    // 0xaba89c: DecompressPointer r0
    //     0xaba89c: add             x0, x0, HEAP, lsl #32
    // 0xaba8a0: r16 = Instance_Color
    //     0xaba8a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xaba8a4: ldr             x16, [x16, #0x858]
    // 0xaba8a8: r30 = 14.000000
    //     0xaba8a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaba8ac: ldr             lr, [lr, #0x1d8]
    // 0xaba8b0: stp             lr, x16, [SP]
    // 0xaba8b4: mov             x1, x0
    // 0xaba8b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaba8b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaba8bc: ldr             x4, [x4, #0x9b8]
    // 0xaba8c0: r0 = copyWith()
    //     0xaba8c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaba8c4: stur            x0, [fp, #-0x48]
    // 0xaba8c8: r0 = TextSpan()
    //     0xaba8c8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaba8cc: mov             x2, x0
    // 0xaba8d0: r0 = "Know "
    //     0xaba8d0: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xaba8d4: ldr             x0, [x0, #0x5d0]
    // 0xaba8d8: stur            x2, [fp, #-0x58]
    // 0xaba8dc: StoreField: r2->field_b = r0
    //     0xaba8dc: stur            w0, [x2, #0xb]
    // 0xaba8e0: r0 = Instance__DeferringMouseCursor
    //     0xaba8e0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaba8e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xaba8e4: stur            w0, [x2, #0x17]
    // 0xaba8e8: ldur            x1, [fp, #-0x48]
    // 0xaba8ec: StoreField: r2->field_7 = r1
    //     0xaba8ec: stur            w1, [x2, #7]
    // 0xaba8f0: ldur            x3, [fp, #-0x10]
    // 0xaba8f4: LoadField: r1 = r3->field_f
    //     0xaba8f4: ldur            w1, [x3, #0xf]
    // 0xaba8f8: DecompressPointer r1
    //     0xaba8f8: add             x1, x1, HEAP, lsl #32
    // 0xaba8fc: r0 = of()
    //     0xaba8fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaba900: LoadField: r1 = r0->field_87
    //     0xaba900: ldur            w1, [x0, #0x87]
    // 0xaba904: DecompressPointer r1
    //     0xaba904: add             x1, x1, HEAP, lsl #32
    // 0xaba908: LoadField: r0 = r1->field_7
    //     0xaba908: ldur            w0, [x1, #7]
    // 0xaba90c: DecompressPointer r0
    //     0xaba90c: add             x0, x0, HEAP, lsl #32
    // 0xaba910: r16 = Instance_Color
    //     0xaba910: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xaba914: ldr             x16, [x16, #0x858]
    // 0xaba918: r30 = 14.000000
    //     0xaba918: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaba91c: ldr             lr, [lr, #0x1d8]
    // 0xaba920: stp             lr, x16, [SP]
    // 0xaba924: mov             x1, x0
    // 0xaba928: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaba928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaba92c: ldr             x4, [x4, #0x9b8]
    // 0xaba930: r0 = copyWith()
    //     0xaba930: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaba934: stur            x0, [fp, #-0x48]
    // 0xaba938: r0 = TextSpan()
    //     0xaba938: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaba93c: mov             x3, x0
    // 0xaba940: r0 = "More"
    //     0xaba940: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xaba944: ldr             x0, [x0, #0x5d8]
    // 0xaba948: stur            x3, [fp, #-0x60]
    // 0xaba94c: StoreField: r3->field_b = r0
    //     0xaba94c: stur            w0, [x3, #0xb]
    // 0xaba950: r0 = Instance__DeferringMouseCursor
    //     0xaba950: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaba954: ArrayStore: r3[0] = r0  ; List_4
    //     0xaba954: stur            w0, [x3, #0x17]
    // 0xaba958: ldur            x1, [fp, #-0x48]
    // 0xaba95c: StoreField: r3->field_7 = r1
    //     0xaba95c: stur            w1, [x3, #7]
    // 0xaba960: r1 = Null
    //     0xaba960: mov             x1, NULL
    // 0xaba964: r2 = 4
    //     0xaba964: movz            x2, #0x4
    // 0xaba968: r0 = AllocateArray()
    //     0xaba968: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaba96c: mov             x2, x0
    // 0xaba970: ldur            x0, [fp, #-0x58]
    // 0xaba974: stur            x2, [fp, #-0x48]
    // 0xaba978: StoreField: r2->field_f = r0
    //     0xaba978: stur            w0, [x2, #0xf]
    // 0xaba97c: ldur            x0, [fp, #-0x60]
    // 0xaba980: StoreField: r2->field_13 = r0
    //     0xaba980: stur            w0, [x2, #0x13]
    // 0xaba984: r1 = <TextSpan>
    //     0xaba984: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xaba988: ldr             x1, [x1, #0x940]
    // 0xaba98c: r0 = AllocateGrowableArray()
    //     0xaba98c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaba990: mov             x1, x0
    // 0xaba994: ldur            x0, [fp, #-0x48]
    // 0xaba998: stur            x1, [fp, #-0x58]
    // 0xaba99c: StoreField: r1->field_f = r0
    //     0xaba99c: stur            w0, [x1, #0xf]
    // 0xaba9a0: r2 = 4
    //     0xaba9a0: movz            x2, #0x4
    // 0xaba9a4: StoreField: r1->field_b = r2
    //     0xaba9a4: stur            w2, [x1, #0xb]
    // 0xaba9a8: r0 = TextSpan()
    //     0xaba9a8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaba9ac: mov             x1, x0
    // 0xaba9b0: ldur            x0, [fp, #-0x58]
    // 0xaba9b4: stur            x1, [fp, #-0x48]
    // 0xaba9b8: StoreField: r1->field_f = r0
    //     0xaba9b8: stur            w0, [x1, #0xf]
    // 0xaba9bc: r0 = Instance__DeferringMouseCursor
    //     0xaba9bc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaba9c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xaba9c0: stur            w0, [x1, #0x17]
    // 0xaba9c4: r0 = RichText()
    //     0xaba9c4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaba9c8: mov             x1, x0
    // 0xaba9cc: ldur            x2, [fp, #-0x48]
    // 0xaba9d0: stur            x0, [fp, #-0x48]
    // 0xaba9d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaba9d4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaba9d8: r0 = RichText()
    //     0xaba9d8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaba9dc: r0 = Padding()
    //     0xaba9dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaba9e0: mov             x1, x0
    // 0xaba9e4: r0 = Instance_EdgeInsets
    //     0xaba9e4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xaba9e8: ldr             x0, [x0, #0xc40]
    // 0xaba9ec: stur            x1, [fp, #-0x58]
    // 0xaba9f0: StoreField: r1->field_f = r0
    //     0xaba9f0: stur            w0, [x1, #0xf]
    // 0xaba9f4: ldur            x0, [fp, #-0x48]
    // 0xaba9f8: StoreField: r1->field_b = r0
    //     0xaba9f8: stur            w0, [x1, #0xb]
    // 0xaba9fc: r0 = Visibility()
    //     0xaba9fc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xabaa00: mov             x1, x0
    // 0xabaa04: ldur            x0, [fp, #-0x58]
    // 0xabaa08: stur            x1, [fp, #-0x48]
    // 0xabaa0c: StoreField: r1->field_b = r0
    //     0xabaa0c: stur            w0, [x1, #0xb]
    // 0xabaa10: r0 = Instance_SizedBox
    //     0xabaa10: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xabaa14: StoreField: r1->field_f = r0
    //     0xabaa14: stur            w0, [x1, #0xf]
    // 0xabaa18: ldur            x2, [fp, #-0x38]
    // 0xabaa1c: StoreField: r1->field_13 = r2
    //     0xabaa1c: stur            w2, [x1, #0x13]
    // 0xabaa20: r2 = false
    //     0xabaa20: add             x2, NULL, #0x30  ; false
    // 0xabaa24: ArrayStore: r1[0] = r2  ; List_4
    //     0xabaa24: stur            w2, [x1, #0x17]
    // 0xabaa28: StoreField: r1->field_1b = r2
    //     0xabaa28: stur            w2, [x1, #0x1b]
    // 0xabaa2c: StoreField: r1->field_1f = r2
    //     0xabaa2c: stur            w2, [x1, #0x1f]
    // 0xabaa30: StoreField: r1->field_23 = r2
    //     0xabaa30: stur            w2, [x1, #0x23]
    // 0xabaa34: StoreField: r1->field_27 = r2
    //     0xabaa34: stur            w2, [x1, #0x27]
    // 0xabaa38: StoreField: r1->field_2b = r2
    //     0xabaa38: stur            w2, [x1, #0x2b]
    // 0xabaa3c: r0 = InkWell()
    //     0xabaa3c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xabaa40: mov             x3, x0
    // 0xabaa44: ldur            x0, [fp, #-0x48]
    // 0xabaa48: stur            x3, [fp, #-0x38]
    // 0xabaa4c: StoreField: r3->field_b = r0
    //     0xabaa4c: stur            w0, [x3, #0xb]
    // 0xabaa50: ldur            x2, [fp, #-0x10]
    // 0xabaa54: r1 = Function '<anonymous closure>':.
    //     0xabaa54: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6af88] AnonymousClosure: (0xabb60c), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xabaa58: ldr             x1, [x1, #0xf88]
    // 0xabaa5c: r0 = AllocateClosure()
    //     0xabaa5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabaa60: mov             x1, x0
    // 0xabaa64: ldur            x0, [fp, #-0x38]
    // 0xabaa68: StoreField: r0->field_f = r1
    //     0xabaa68: stur            w1, [x0, #0xf]
    // 0xabaa6c: r2 = true
    //     0xabaa6c: add             x2, NULL, #0x20  ; true
    // 0xabaa70: StoreField: r0->field_43 = r2
    //     0xabaa70: stur            w2, [x0, #0x43]
    // 0xabaa74: r3 = Instance_BoxShape
    //     0xabaa74: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabaa78: ldr             x3, [x3, #0x80]
    // 0xabaa7c: StoreField: r0->field_47 = r3
    //     0xabaa7c: stur            w3, [x0, #0x47]
    // 0xabaa80: StoreField: r0->field_6f = r2
    //     0xabaa80: stur            w2, [x0, #0x6f]
    // 0xabaa84: r4 = false
    //     0xabaa84: add             x4, NULL, #0x30  ; false
    // 0xabaa88: StoreField: r0->field_73 = r4
    //     0xabaa88: stur            w4, [x0, #0x73]
    // 0xabaa8c: StoreField: r0->field_83 = r2
    //     0xabaa8c: stur            w2, [x0, #0x83]
    // 0xabaa90: StoreField: r0->field_7b = r4
    //     0xabaa90: stur            w4, [x0, #0x7b]
    // 0xabaa94: r1 = <FlexParentData>
    //     0xabaa94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabaa98: ldr             x1, [x1, #0xe00]
    // 0xabaa9c: r0 = Expanded()
    //     0xabaa9c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabaaa0: stur            x0, [fp, #-0x48]
    // 0xabaaa4: StoreField: r0->field_13 = rZR
    //     0xabaaa4: stur            xzr, [x0, #0x13]
    // 0xabaaa8: r3 = Instance_FlexFit
    //     0xabaaa8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabaaac: ldr             x3, [x3, #0xe08]
    // 0xabaab0: StoreField: r0->field_1b = r3
    //     0xabaab0: stur            w3, [x0, #0x1b]
    // 0xabaab4: ldur            x1, [fp, #-0x38]
    // 0xabaab8: StoreField: r0->field_b = r1
    //     0xabaab8: stur            w1, [x0, #0xb]
    // 0xabaabc: r1 = Null
    //     0xabaabc: mov             x1, NULL
    // 0xabaac0: r2 = 4
    //     0xabaac0: movz            x2, #0x4
    // 0xabaac4: r0 = AllocateArray()
    //     0xabaac4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabaac8: mov             x2, x0
    // 0xabaacc: ldur            x0, [fp, #-0x50]
    // 0xabaad0: stur            x2, [fp, #-0x38]
    // 0xabaad4: StoreField: r2->field_f = r0
    //     0xabaad4: stur            w0, [x2, #0xf]
    // 0xabaad8: ldur            x0, [fp, #-0x48]
    // 0xabaadc: StoreField: r2->field_13 = r0
    //     0xabaadc: stur            w0, [x2, #0x13]
    // 0xabaae0: r1 = <Widget>
    //     0xabaae0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabaae4: r0 = AllocateGrowableArray()
    //     0xabaae4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabaae8: mov             x1, x0
    // 0xabaaec: ldur            x0, [fp, #-0x38]
    // 0xabaaf0: stur            x1, [fp, #-0x48]
    // 0xabaaf4: StoreField: r1->field_f = r0
    //     0xabaaf4: stur            w0, [x1, #0xf]
    // 0xabaaf8: r0 = 4
    //     0xabaaf8: movz            x0, #0x4
    // 0xabaafc: StoreField: r1->field_b = r0
    //     0xabaafc: stur            w0, [x1, #0xb]
    // 0xabab00: r0 = Column()
    //     0xabab00: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabab04: mov             x1, x0
    // 0xabab08: r0 = Instance_Axis
    //     0xabab08: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabab0c: stur            x1, [fp, #-0x38]
    // 0xabab10: StoreField: r1->field_f = r0
    //     0xabab10: stur            w0, [x1, #0xf]
    // 0xabab14: r2 = Instance_MainAxisAlignment
    //     0xabab14: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabab18: ldr             x2, [x2, #0xa08]
    // 0xabab1c: StoreField: r1->field_13 = r2
    //     0xabab1c: stur            w2, [x1, #0x13]
    // 0xabab20: r0 = Instance_MainAxisSize
    //     0xabab20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xabab24: ldr             x0, [x0, #0xdd0]
    // 0xabab28: ArrayStore: r1[0] = r0  ; List_4
    //     0xabab28: stur            w0, [x1, #0x17]
    // 0xabab2c: r0 = Instance_CrossAxisAlignment
    //     0xabab2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabab30: ldr             x0, [x0, #0x890]
    // 0xabab34: StoreField: r1->field_1b = r0
    //     0xabab34: stur            w0, [x1, #0x1b]
    // 0xabab38: r3 = Instance_VerticalDirection
    //     0xabab38: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabab3c: ldr             x3, [x3, #0xa20]
    // 0xabab40: StoreField: r1->field_23 = r3
    //     0xabab40: stur            w3, [x1, #0x23]
    // 0xabab44: r4 = Instance_Clip
    //     0xabab44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabab48: ldr             x4, [x4, #0x38]
    // 0xabab4c: StoreField: r1->field_2b = r4
    //     0xabab4c: stur            w4, [x1, #0x2b]
    // 0xabab50: StoreField: r1->field_2f = rZR
    //     0xabab50: stur            xzr, [x1, #0x2f]
    // 0xabab54: ldur            x0, [fp, #-0x48]
    // 0xabab58: StoreField: r1->field_b = r0
    //     0xabab58: stur            w0, [x1, #0xb]
    // 0xabab5c: ldur            x5, [fp, #-8]
    // 0xabab60: LoadField: r0 = r5->field_f
    //     0xabab60: ldur            w0, [x5, #0xf]
    // 0xabab64: DecompressPointer r0
    //     0xabab64: add             x0, x0, HEAP, lsl #32
    // 0xabab68: LoadField: r6 = r0->field_b
    //     0xabab68: ldur            w6, [x0, #0xb]
    // 0xabab6c: DecompressPointer r6
    //     0xabab6c: add             x6, x6, HEAP, lsl #32
    // 0xabab70: cmp             w6, NULL
    // 0xabab74: b.eq            #0xabb290
    // 0xabab78: LoadField: r0 = r6->field_b
    //     0xabab78: ldur            w0, [x6, #0xb]
    // 0xabab7c: DecompressPointer r0
    //     0xabab7c: add             x0, x0, HEAP, lsl #32
    // 0xabab80: r6 = LoadClassIdInstr(r0)
    //     0xabab80: ldur            x6, [x0, #-1]
    //     0xabab84: ubfx            x6, x6, #0xc, #0x14
    // 0xabab88: r16 = "checkout_offers"
    //     0xabab88: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xabab8c: ldr             x16, [x16, #0x1c8]
    // 0xabab90: stp             x16, x0, [SP]
    // 0xabab94: mov             x0, x6
    // 0xabab98: mov             lr, x0
    // 0xabab9c: ldr             lr, [x21, lr, lsl #3]
    // 0xababa0: blr             lr
    // 0xababa4: tbz             w0, #4, #0xabac74
    // 0xababa8: ldur            x2, [fp, #-8]
    // 0xababac: LoadField: r0 = r2->field_f
    //     0xababac: ldur            w0, [x2, #0xf]
    // 0xababb0: DecompressPointer r0
    //     0xababb0: add             x0, x0, HEAP, lsl #32
    // 0xababb4: LoadField: r1 = r0->field_b
    //     0xababb4: ldur            w1, [x0, #0xb]
    // 0xababb8: DecompressPointer r1
    //     0xababb8: add             x1, x1, HEAP, lsl #32
    // 0xababbc: cmp             w1, NULL
    // 0xababc0: b.eq            #0xabb294
    // 0xababc4: LoadField: r0 = r1->field_1f
    //     0xababc4: ldur            w0, [x1, #0x1f]
    // 0xababc8: DecompressPointer r0
    //     0xababc8: add             x0, x0, HEAP, lsl #32
    // 0xababcc: tbnz            w0, #4, #0xababd8
    // 0xababd0: r3 = true
    //     0xababd0: add             x3, NULL, #0x20  ; true
    // 0xababd4: b               #0xabac7c
    // 0xababd8: LoadField: r0 = r1->field_f
    //     0xababd8: ldur            w0, [x1, #0xf]
    // 0xababdc: DecompressPointer r0
    //     0xababdc: add             x0, x0, HEAP, lsl #32
    // 0xababe0: cmp             w0, NULL
    // 0xababe4: b.ne            #0xababf4
    // 0xababe8: ldur            x3, [fp, #-0x10]
    // 0xababec: r0 = Null
    //     0xababec: mov             x0, NULL
    // 0xababf0: b               #0xabac44
    // 0xababf4: ldur            x3, [fp, #-0x10]
    // 0xababf8: LoadField: r4 = r0->field_f
    //     0xababf8: ldur            w4, [x0, #0xf]
    // 0xababfc: DecompressPointer r4
    //     0xababfc: add             x4, x4, HEAP, lsl #32
    // 0xabac00: LoadField: r0 = r3->field_13
    //     0xabac00: ldur            w0, [x3, #0x13]
    // 0xabac04: DecompressPointer r0
    //     0xabac04: add             x0, x0, HEAP, lsl #32
    // 0xabac08: LoadField: r1 = r4->field_b
    //     0xabac08: ldur            w1, [x4, #0xb]
    // 0xabac0c: r5 = LoadInt32Instr(r0)
    //     0xabac0c: sbfx            x5, x0, #1, #0x1f
    //     0xabac10: tbz             w0, #0, #0xabac18
    //     0xabac14: ldur            x5, [x0, #7]
    // 0xabac18: r0 = LoadInt32Instr(r1)
    //     0xabac18: sbfx            x0, x1, #1, #0x1f
    // 0xabac1c: mov             x1, x5
    // 0xabac20: cmp             x1, x0
    // 0xabac24: b.hs            #0xabb298
    // 0xabac28: LoadField: r0 = r4->field_f
    //     0xabac28: ldur            w0, [x4, #0xf]
    // 0xabac2c: DecompressPointer r0
    //     0xabac2c: add             x0, x0, HEAP, lsl #32
    // 0xabac30: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabac30: add             x16, x0, x5, lsl #2
    //     0xabac34: ldur            w1, [x16, #0xf]
    // 0xabac38: DecompressPointer r1
    //     0xabac38: add             x1, x1, HEAP, lsl #32
    // 0xabac3c: LoadField: r0 = r1->field_f
    //     0xabac3c: ldur            w0, [x1, #0xf]
    // 0xabac40: DecompressPointer r0
    //     0xabac40: add             x0, x0, HEAP, lsl #32
    // 0xabac44: r1 = LoadClassIdInstr(r0)
    //     0xabac44: ldur            x1, [x0, #-1]
    //     0xabac48: ubfx            x1, x1, #0xc, #0x14
    // 0xabac4c: r16 = "sale_event_static_coupon"
    //     0xabac4c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xabac50: ldr             x16, [x16, #0x610]
    // 0xabac54: stp             x16, x0, [SP]
    // 0xabac58: mov             x0, x1
    // 0xabac5c: mov             lr, x0
    // 0xabac60: ldr             lr, [x21, lr, lsl #3]
    // 0xabac64: blr             lr
    // 0xabac68: mov             x3, x0
    // 0xabac6c: ldur            x2, [fp, #-8]
    // 0xabac70: b               #0xabac7c
    // 0xabac74: ldur            x2, [fp, #-8]
    // 0xabac78: r3 = false
    //     0xabac78: add             x3, NULL, #0x30  ; false
    // 0xabac7c: stur            x3, [fp, #-0x48]
    // 0xabac80: LoadField: r0 = r2->field_f
    //     0xabac80: ldur            w0, [x2, #0xf]
    // 0xabac84: DecompressPointer r0
    //     0xabac84: add             x0, x0, HEAP, lsl #32
    // 0xabac88: LoadField: r4 = r0->field_b
    //     0xabac88: ldur            w4, [x0, #0xb]
    // 0xabac8c: DecompressPointer r4
    //     0xabac8c: add             x4, x4, HEAP, lsl #32
    // 0xabac90: cmp             w4, NULL
    // 0xabac94: b.eq            #0xabb29c
    // 0xabac98: LoadField: r0 = r4->field_f
    //     0xabac98: ldur            w0, [x4, #0xf]
    // 0xabac9c: DecompressPointer r0
    //     0xabac9c: add             x0, x0, HEAP, lsl #32
    // 0xabaca0: cmp             w0, NULL
    // 0xabaca4: b.ne            #0xabacb4
    // 0xabaca8: ldur            x5, [fp, #-0x10]
    // 0xabacac: r0 = Null
    //     0xabacac: mov             x0, NULL
    // 0xabacb0: b               #0xabad04
    // 0xabacb4: ldur            x5, [fp, #-0x10]
    // 0xabacb8: LoadField: r6 = r0->field_f
    //     0xabacb8: ldur            w6, [x0, #0xf]
    // 0xabacbc: DecompressPointer r6
    //     0xabacbc: add             x6, x6, HEAP, lsl #32
    // 0xabacc0: LoadField: r0 = r5->field_13
    //     0xabacc0: ldur            w0, [x5, #0x13]
    // 0xabacc4: DecompressPointer r0
    //     0xabacc4: add             x0, x0, HEAP, lsl #32
    // 0xabacc8: LoadField: r1 = r6->field_b
    //     0xabacc8: ldur            w1, [x6, #0xb]
    // 0xabaccc: r7 = LoadInt32Instr(r0)
    //     0xabaccc: sbfx            x7, x0, #1, #0x1f
    //     0xabacd0: tbz             w0, #0, #0xabacd8
    //     0xabacd4: ldur            x7, [x0, #7]
    // 0xabacd8: r0 = LoadInt32Instr(r1)
    //     0xabacd8: sbfx            x0, x1, #1, #0x1f
    // 0xabacdc: mov             x1, x7
    // 0xabace0: cmp             x1, x0
    // 0xabace4: b.hs            #0xabb2a0
    // 0xabace8: LoadField: r0 = r6->field_f
    //     0xabace8: ldur            w0, [x6, #0xf]
    // 0xabacec: DecompressPointer r0
    //     0xabacec: add             x0, x0, HEAP, lsl #32
    // 0xabacf0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xabacf0: add             x16, x0, x7, lsl #2
    //     0xabacf4: ldur            w1, [x16, #0xf]
    // 0xabacf8: DecompressPointer r1
    //     0xabacf8: add             x1, x1, HEAP, lsl #32
    // 0xabacfc: LoadField: r0 = r1->field_7
    //     0xabacfc: ldur            w0, [x1, #7]
    // 0xabad00: DecompressPointer r0
    //     0xabad00: add             x0, x0, HEAP, lsl #32
    // 0xabad04: LoadField: r1 = r4->field_13
    //     0xabad04: ldur            w1, [x4, #0x13]
    // 0xabad08: DecompressPointer r1
    //     0xabad08: add             x1, x1, HEAP, lsl #32
    // 0xabad0c: r4 = LoadClassIdInstr(r0)
    //     0xabad0c: ldur            x4, [x0, #-1]
    //     0xabad10: ubfx            x4, x4, #0xc, #0x14
    // 0xabad14: stp             x1, x0, [SP]
    // 0xabad18: mov             x0, x4
    // 0xabad1c: mov             lr, x0
    // 0xabad20: ldr             lr, [x21, lr, lsl #3]
    // 0xabad24: blr             lr
    // 0xabad28: tbz             w0, #4, #0xabaddc
    // 0xabad2c: ldur            x2, [fp, #-8]
    // 0xabad30: LoadField: r0 = r2->field_f
    //     0xabad30: ldur            w0, [x2, #0xf]
    // 0xabad34: DecompressPointer r0
    //     0xabad34: add             x0, x0, HEAP, lsl #32
    // 0xabad38: LoadField: r1 = r0->field_b
    //     0xabad38: ldur            w1, [x0, #0xb]
    // 0xabad3c: DecompressPointer r1
    //     0xabad3c: add             x1, x1, HEAP, lsl #32
    // 0xabad40: cmp             w1, NULL
    // 0xabad44: b.eq            #0xabb2a4
    // 0xabad48: LoadField: r0 = r1->field_f
    //     0xabad48: ldur            w0, [x1, #0xf]
    // 0xabad4c: DecompressPointer r0
    //     0xabad4c: add             x0, x0, HEAP, lsl #32
    // 0xabad50: cmp             w0, NULL
    // 0xabad54: b.ne            #0xabad64
    // 0xabad58: ldur            x3, [fp, #-0x10]
    // 0xabad5c: r0 = Null
    //     0xabad5c: mov             x0, NULL
    // 0xabad60: b               #0xabadb4
    // 0xabad64: ldur            x3, [fp, #-0x10]
    // 0xabad68: LoadField: r4 = r0->field_f
    //     0xabad68: ldur            w4, [x0, #0xf]
    // 0xabad6c: DecompressPointer r4
    //     0xabad6c: add             x4, x4, HEAP, lsl #32
    // 0xabad70: LoadField: r0 = r3->field_13
    //     0xabad70: ldur            w0, [x3, #0x13]
    // 0xabad74: DecompressPointer r0
    //     0xabad74: add             x0, x0, HEAP, lsl #32
    // 0xabad78: LoadField: r1 = r4->field_b
    //     0xabad78: ldur            w1, [x4, #0xb]
    // 0xabad7c: r5 = LoadInt32Instr(r0)
    //     0xabad7c: sbfx            x5, x0, #1, #0x1f
    //     0xabad80: tbz             w0, #0, #0xabad88
    //     0xabad84: ldur            x5, [x0, #7]
    // 0xabad88: r0 = LoadInt32Instr(r1)
    //     0xabad88: sbfx            x0, x1, #1, #0x1f
    // 0xabad8c: mov             x1, x5
    // 0xabad90: cmp             x1, x0
    // 0xabad94: b.hs            #0xabb2a8
    // 0xabad98: LoadField: r0 = r4->field_f
    //     0xabad98: ldur            w0, [x4, #0xf]
    // 0xabad9c: DecompressPointer r0
    //     0xabad9c: add             x0, x0, HEAP, lsl #32
    // 0xabada0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabada0: add             x16, x0, x5, lsl #2
    //     0xabada4: ldur            w1, [x16, #0xf]
    // 0xabada8: DecompressPointer r1
    //     0xabada8: add             x1, x1, HEAP, lsl #32
    // 0xabadac: LoadField: r0 = r1->field_f
    //     0xabadac: ldur            w0, [x1, #0xf]
    // 0xabadb0: DecompressPointer r0
    //     0xabadb0: add             x0, x0, HEAP, lsl #32
    // 0xabadb4: r1 = LoadClassIdInstr(r0)
    //     0xabadb4: ldur            x1, [x0, #-1]
    //     0xabadb8: ubfx            x1, x1, #0xc, #0x14
    // 0xabadbc: r16 = "sale_event_static_coupon"
    //     0xabadbc: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xabadc0: ldr             x16, [x16, #0x610]
    // 0xabadc4: stp             x16, x0, [SP]
    // 0xabadc8: mov             x0, x1
    // 0xabadcc: mov             lr, x0
    // 0xabadd0: ldr             lr, [x21, lr, lsl #3]
    // 0xabadd4: blr             lr
    // 0xabadd8: tbnz            w0, #4, #0xabae48
    // 0xabaddc: ldur            x2, [fp, #-0x10]
    // 0xabade0: LoadField: r1 = r2->field_f
    //     0xabade0: ldur            w1, [x2, #0xf]
    // 0xabade4: DecompressPointer r1
    //     0xabade4: add             x1, x1, HEAP, lsl #32
    // 0xabade8: r0 = of()
    //     0xabade8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabadec: LoadField: r1 = r0->field_87
    //     0xabadec: ldur            w1, [x0, #0x87]
    // 0xabadf0: DecompressPointer r1
    //     0xabadf0: add             x1, x1, HEAP, lsl #32
    // 0xabadf4: LoadField: r0 = r1->field_7
    //     0xabadf4: ldur            w0, [x1, #7]
    // 0xabadf8: DecompressPointer r0
    //     0xabadf8: add             x0, x0, HEAP, lsl #32
    // 0xabadfc: r16 = Instance_Color
    //     0xabadfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xabae00: ldr             x16, [x16, #0x858]
    // 0xabae04: r30 = 12.000000
    //     0xabae04: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabae08: ldr             lr, [lr, #0x9e8]
    // 0xabae0c: stp             lr, x16, [SP]
    // 0xabae10: mov             x1, x0
    // 0xabae14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xabae14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xabae18: ldr             x4, [x4, #0x9b8]
    // 0xabae1c: r0 = copyWith()
    //     0xabae1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabae20: stur            x0, [fp, #-0x50]
    // 0xabae24: r0 = Text()
    //     0xabae24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabae28: mov             x1, x0
    // 0xabae2c: r0 = "APPLIED"
    //     0xabae2c: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xabae30: ldr             x0, [x0, #0x5e8]
    // 0xabae34: StoreField: r1->field_b = r0
    //     0xabae34: stur            w0, [x1, #0xb]
    // 0xabae38: ldur            x0, [fp, #-0x50]
    // 0xabae3c: StoreField: r1->field_13 = r0
    //     0xabae3c: stur            w0, [x1, #0x13]
    // 0xabae40: mov             x3, x1
    // 0xabae44: b               #0xabaf58
    // 0xabae48: ldur            x0, [fp, #-8]
    // 0xabae4c: ldur            x2, [fp, #-0x10]
    // 0xabae50: LoadField: r1 = r2->field_f
    //     0xabae50: ldur            w1, [x2, #0xf]
    // 0xabae54: DecompressPointer r1
    //     0xabae54: add             x1, x1, HEAP, lsl #32
    // 0xabae58: r0 = of()
    //     0xabae58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabae5c: LoadField: r1 = r0->field_87
    //     0xabae5c: ldur            w1, [x0, #0x87]
    // 0xabae60: DecompressPointer r1
    //     0xabae60: add             x1, x1, HEAP, lsl #32
    // 0xabae64: LoadField: r2 = r1->field_7
    //     0xabae64: ldur            w2, [x1, #7]
    // 0xabae68: DecompressPointer r2
    //     0xabae68: add             x2, x2, HEAP, lsl #32
    // 0xabae6c: ldur            x0, [fp, #-8]
    // 0xabae70: stur            x2, [fp, #-0x50]
    // 0xabae74: LoadField: r1 = r0->field_f
    //     0xabae74: ldur            w1, [x0, #0xf]
    // 0xabae78: DecompressPointer r1
    //     0xabae78: add             x1, x1, HEAP, lsl #32
    // 0xabae7c: LoadField: r0 = r1->field_b
    //     0xabae7c: ldur            w0, [x1, #0xb]
    // 0xabae80: DecompressPointer r0
    //     0xabae80: add             x0, x0, HEAP, lsl #32
    // 0xabae84: cmp             w0, NULL
    // 0xabae88: b.eq            #0xabb2ac
    // 0xabae8c: LoadField: r1 = r0->field_f
    //     0xabae8c: ldur            w1, [x0, #0xf]
    // 0xabae90: DecompressPointer r1
    //     0xabae90: add             x1, x1, HEAP, lsl #32
    // 0xabae94: cmp             w1, NULL
    // 0xabae98: b.ne            #0xabaea8
    // 0xabae9c: ldur            x3, [fp, #-0x10]
    // 0xabaea0: r0 = Null
    //     0xabaea0: mov             x0, NULL
    // 0xabaea4: b               #0xabaef8
    // 0xabaea8: ldur            x3, [fp, #-0x10]
    // 0xabaeac: LoadField: r4 = r1->field_f
    //     0xabaeac: ldur            w4, [x1, #0xf]
    // 0xabaeb0: DecompressPointer r4
    //     0xabaeb0: add             x4, x4, HEAP, lsl #32
    // 0xabaeb4: LoadField: r0 = r3->field_13
    //     0xabaeb4: ldur            w0, [x3, #0x13]
    // 0xabaeb8: DecompressPointer r0
    //     0xabaeb8: add             x0, x0, HEAP, lsl #32
    // 0xabaebc: LoadField: r1 = r4->field_b
    //     0xabaebc: ldur            w1, [x4, #0xb]
    // 0xabaec0: r5 = LoadInt32Instr(r0)
    //     0xabaec0: sbfx            x5, x0, #1, #0x1f
    //     0xabaec4: tbz             w0, #0, #0xabaecc
    //     0xabaec8: ldur            x5, [x0, #7]
    // 0xabaecc: r0 = LoadInt32Instr(r1)
    //     0xabaecc: sbfx            x0, x1, #1, #0x1f
    // 0xabaed0: mov             x1, x5
    // 0xabaed4: cmp             x1, x0
    // 0xabaed8: b.hs            #0xabb2b0
    // 0xabaedc: LoadField: r0 = r4->field_f
    //     0xabaedc: ldur            w0, [x4, #0xf]
    // 0xabaee0: DecompressPointer r0
    //     0xabaee0: add             x0, x0, HEAP, lsl #32
    // 0xabaee4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabaee4: add             x16, x0, x5, lsl #2
    //     0xabaee8: ldur            w1, [x16, #0xf]
    // 0xabaeec: DecompressPointer r1
    //     0xabaeec: add             x1, x1, HEAP, lsl #32
    // 0xabaef0: LoadField: r0 = r1->field_27
    //     0xabaef0: ldur            w0, [x1, #0x27]
    // 0xabaef4: DecompressPointer r0
    //     0xabaef4: add             x0, x0, HEAP, lsl #32
    // 0xabaef8: cmp             w0, NULL
    // 0xabaefc: b.eq            #0xabaf0c
    // 0xabaf00: tbnz            w0, #4, #0xabaf0c
    // 0xabaf04: r0 = Instance_Color
    //     0xabaf04: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabaf08: b               #0xabaf18
    // 0xabaf0c: r1 = Instance_Color
    //     0xabaf0c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabaf10: d0 = 0.400000
    //     0xabaf10: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xabaf14: r0 = withOpacity()
    //     0xabaf14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xabaf18: r16 = 14.000000
    //     0xabaf18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xabaf1c: ldr             x16, [x16, #0x1d8]
    // 0xabaf20: stp             x16, x0, [SP]
    // 0xabaf24: ldur            x1, [fp, #-0x50]
    // 0xabaf28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xabaf28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xabaf2c: ldr             x4, [x4, #0x9b8]
    // 0xabaf30: r0 = copyWith()
    //     0xabaf30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabaf34: stur            x0, [fp, #-8]
    // 0xabaf38: r0 = Text()
    //     0xabaf38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabaf3c: mov             x1, x0
    // 0xabaf40: r0 = "APPLY"
    //     0xabaf40: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xabaf44: ldr             x0, [x0, #0x5f0]
    // 0xabaf48: StoreField: r1->field_b = r0
    //     0xabaf48: stur            w0, [x1, #0xb]
    // 0xabaf4c: ldur            x0, [fp, #-8]
    // 0xabaf50: StoreField: r1->field_13 = r0
    //     0xabaf50: stur            w0, [x1, #0x13]
    // 0xabaf54: mov             x3, x1
    // 0xabaf58: ldur            x2, [fp, #-0x40]
    // 0xabaf5c: ldur            x1, [fp, #-0x38]
    // 0xabaf60: ldur            x0, [fp, #-0x48]
    // 0xabaf64: stur            x3, [fp, #-8]
    // 0xabaf68: r0 = InkWell()
    //     0xabaf68: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xabaf6c: mov             x3, x0
    // 0xabaf70: ldur            x0, [fp, #-8]
    // 0xabaf74: stur            x3, [fp, #-0x50]
    // 0xabaf78: StoreField: r3->field_b = r0
    //     0xabaf78: stur            w0, [x3, #0xb]
    // 0xabaf7c: ldur            x2, [fp, #-0x10]
    // 0xabaf80: r1 = Function '<anonymous closure>':.
    //     0xabaf80: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6af90] AnonymousClosure: (0xabb2b4), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xabaf84: ldr             x1, [x1, #0xf90]
    // 0xabaf88: r0 = AllocateClosure()
    //     0xabaf88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabaf8c: mov             x1, x0
    // 0xabaf90: ldur            x0, [fp, #-0x50]
    // 0xabaf94: StoreField: r0->field_f = r1
    //     0xabaf94: stur            w1, [x0, #0xf]
    // 0xabaf98: r2 = true
    //     0xabaf98: add             x2, NULL, #0x20  ; true
    // 0xabaf9c: StoreField: r0->field_43 = r2
    //     0xabaf9c: stur            w2, [x0, #0x43]
    // 0xabafa0: r1 = Instance_BoxShape
    //     0xabafa0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabafa4: ldr             x1, [x1, #0x80]
    // 0xabafa8: StoreField: r0->field_47 = r1
    //     0xabafa8: stur            w1, [x0, #0x47]
    // 0xabafac: StoreField: r0->field_6f = r2
    //     0xabafac: stur            w2, [x0, #0x6f]
    // 0xabafb0: r3 = false
    //     0xabafb0: add             x3, NULL, #0x30  ; false
    // 0xabafb4: StoreField: r0->field_73 = r3
    //     0xabafb4: stur            w3, [x0, #0x73]
    // 0xabafb8: StoreField: r0->field_83 = r2
    //     0xabafb8: stur            w2, [x0, #0x83]
    // 0xabafbc: StoreField: r0->field_7b = r3
    //     0xabafbc: stur            w3, [x0, #0x7b]
    // 0xabafc0: r1 = <FlexParentData>
    //     0xabafc0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabafc4: ldr             x1, [x1, #0xe00]
    // 0xabafc8: r0 = Expanded()
    //     0xabafc8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabafcc: mov             x1, x0
    // 0xabafd0: r0 = 1
    //     0xabafd0: movz            x0, #0x1
    // 0xabafd4: stur            x1, [fp, #-8]
    // 0xabafd8: StoreField: r1->field_13 = r0
    //     0xabafd8: stur            x0, [x1, #0x13]
    // 0xabafdc: r0 = Instance_FlexFit
    //     0xabafdc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabafe0: ldr             x0, [x0, #0xe08]
    // 0xabafe4: StoreField: r1->field_1b = r0
    //     0xabafe4: stur            w0, [x1, #0x1b]
    // 0xabafe8: ldur            x0, [fp, #-0x50]
    // 0xabafec: StoreField: r1->field_b = r0
    //     0xabafec: stur            w0, [x1, #0xb]
    // 0xabaff0: r0 = Visibility()
    //     0xabaff0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xabaff4: mov             x3, x0
    // 0xabaff8: ldur            x0, [fp, #-8]
    // 0xabaffc: stur            x3, [fp, #-0x10]
    // 0xabb000: StoreField: r3->field_b = r0
    //     0xabb000: stur            w0, [x3, #0xb]
    // 0xabb004: r0 = Instance_SizedBox
    //     0xabb004: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xabb008: StoreField: r3->field_f = r0
    //     0xabb008: stur            w0, [x3, #0xf]
    // 0xabb00c: ldur            x0, [fp, #-0x48]
    // 0xabb010: StoreField: r3->field_13 = r0
    //     0xabb010: stur            w0, [x3, #0x13]
    // 0xabb014: r0 = false
    //     0xabb014: add             x0, NULL, #0x30  ; false
    // 0xabb018: ArrayStore: r3[0] = r0  ; List_4
    //     0xabb018: stur            w0, [x3, #0x17]
    // 0xabb01c: StoreField: r3->field_1b = r0
    //     0xabb01c: stur            w0, [x3, #0x1b]
    // 0xabb020: StoreField: r3->field_1f = r0
    //     0xabb020: stur            w0, [x3, #0x1f]
    // 0xabb024: StoreField: r3->field_23 = r0
    //     0xabb024: stur            w0, [x3, #0x23]
    // 0xabb028: StoreField: r3->field_27 = r0
    //     0xabb028: stur            w0, [x3, #0x27]
    // 0xabb02c: StoreField: r3->field_2b = r0
    //     0xabb02c: stur            w0, [x3, #0x2b]
    // 0xabb030: r1 = Null
    //     0xabb030: mov             x1, NULL
    // 0xabb034: r2 = 8
    //     0xabb034: movz            x2, #0x8
    // 0xabb038: r0 = AllocateArray()
    //     0xabb038: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabb03c: mov             x2, x0
    // 0xabb040: ldur            x0, [fp, #-0x40]
    // 0xabb044: stur            x2, [fp, #-8]
    // 0xabb048: StoreField: r2->field_f = r0
    //     0xabb048: stur            w0, [x2, #0xf]
    // 0xabb04c: ldur            x0, [fp, #-0x38]
    // 0xabb050: StoreField: r2->field_13 = r0
    //     0xabb050: stur            w0, [x2, #0x13]
    // 0xabb054: r16 = Instance_Spacer
    //     0xabb054: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xabb058: ldr             x16, [x16, #0xf0]
    // 0xabb05c: ArrayStore: r2[0] = r16  ; List_4
    //     0xabb05c: stur            w16, [x2, #0x17]
    // 0xabb060: ldur            x0, [fp, #-0x10]
    // 0xabb064: StoreField: r2->field_1b = r0
    //     0xabb064: stur            w0, [x2, #0x1b]
    // 0xabb068: r1 = <Widget>
    //     0xabb068: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabb06c: r0 = AllocateGrowableArray()
    //     0xabb06c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabb070: mov             x1, x0
    // 0xabb074: ldur            x0, [fp, #-8]
    // 0xabb078: stur            x1, [fp, #-0x10]
    // 0xabb07c: StoreField: r1->field_f = r0
    //     0xabb07c: stur            w0, [x1, #0xf]
    // 0xabb080: r0 = 8
    //     0xabb080: movz            x0, #0x8
    // 0xabb084: StoreField: r1->field_b = r0
    //     0xabb084: stur            w0, [x1, #0xb]
    // 0xabb088: r0 = Row()
    //     0xabb088: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabb08c: mov             x1, x0
    // 0xabb090: r0 = Instance_Axis
    //     0xabb090: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabb094: stur            x1, [fp, #-8]
    // 0xabb098: StoreField: r1->field_f = r0
    //     0xabb098: stur            w0, [x1, #0xf]
    // 0xabb09c: r0 = Instance_MainAxisAlignment
    //     0xabb09c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabb0a0: ldr             x0, [x0, #0xa08]
    // 0xabb0a4: StoreField: r1->field_13 = r0
    //     0xabb0a4: stur            w0, [x1, #0x13]
    // 0xabb0a8: r0 = Instance_MainAxisSize
    //     0xabb0a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabb0ac: ldr             x0, [x0, #0xa10]
    // 0xabb0b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xabb0b0: stur            w0, [x1, #0x17]
    // 0xabb0b4: r0 = Instance_CrossAxisAlignment
    //     0xabb0b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabb0b8: ldr             x0, [x0, #0xa18]
    // 0xabb0bc: StoreField: r1->field_1b = r0
    //     0xabb0bc: stur            w0, [x1, #0x1b]
    // 0xabb0c0: r0 = Instance_VerticalDirection
    //     0xabb0c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabb0c4: ldr             x0, [x0, #0xa20]
    // 0xabb0c8: StoreField: r1->field_23 = r0
    //     0xabb0c8: stur            w0, [x1, #0x23]
    // 0xabb0cc: r0 = Instance_Clip
    //     0xabb0cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabb0d0: ldr             x0, [x0, #0x38]
    // 0xabb0d4: StoreField: r1->field_2b = r0
    //     0xabb0d4: stur            w0, [x1, #0x2b]
    // 0xabb0d8: StoreField: r1->field_2f = rZR
    //     0xabb0d8: stur            xzr, [x1, #0x2f]
    // 0xabb0dc: ldur            x0, [fp, #-0x10]
    // 0xabb0e0: StoreField: r1->field_b = r0
    //     0xabb0e0: stur            w0, [x1, #0xb]
    // 0xabb0e4: r0 = Padding()
    //     0xabb0e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabb0e8: mov             x1, x0
    // 0xabb0ec: r0 = Instance_EdgeInsets
    //     0xabb0ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xabb0f0: ldr             x0, [x0, #0x980]
    // 0xabb0f4: stur            x1, [fp, #-0x10]
    // 0xabb0f8: StoreField: r1->field_f = r0
    //     0xabb0f8: stur            w0, [x1, #0xf]
    // 0xabb0fc: ldur            x2, [fp, #-8]
    // 0xabb100: StoreField: r1->field_b = r2
    //     0xabb100: stur            w2, [x1, #0xb]
    // 0xabb104: r0 = ClipRRect()
    //     0xabb104: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xabb108: mov             x1, x0
    // 0xabb10c: r0 = Instance_BorderRadius
    //     0xabb10c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xabb110: ldr             x0, [x0, #0x460]
    // 0xabb114: stur            x1, [fp, #-8]
    // 0xabb118: StoreField: r1->field_f = r0
    //     0xabb118: stur            w0, [x1, #0xf]
    // 0xabb11c: r0 = Instance_Clip
    //     0xabb11c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xabb120: ldr             x0, [x0, #0x138]
    // 0xabb124: ArrayStore: r1[0] = r0  ; List_4
    //     0xabb124: stur            w0, [x1, #0x17]
    // 0xabb128: ldur            x0, [fp, #-0x10]
    // 0xabb12c: StoreField: r1->field_b = r0
    //     0xabb12c: stur            w0, [x1, #0xb]
    // 0xabb130: r0 = DottedBorder()
    //     0xabb130: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0xabb134: stur            x0, [fp, #-0x10]
    // 0xabb138: r16 = Instance_BorderType
    //     0xabb138: add             x16, PP, #0x40, lsl #12  ; [pp+0x40078] Obj!BorderType@d750a1
    //     0xabb13c: ldr             x16, [x16, #0x78]
    // 0xabb140: r30 = Instance_Radius
    //     0xabb140: add             lr, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0xabb144: ldr             lr, [lr, #0x758]
    // 0xabb148: stp             lr, x16, [SP]
    // 0xabb14c: mov             x1, x0
    // 0xabb150: ldur            x2, [fp, #-8]
    // 0xabb154: ldur            x3, [fp, #-0x30]
    // 0xabb158: r5 = const [5.0, 5.0]
    //     0xabb158: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0xabb15c: ldr             x5, [x5, #0xab0]
    // 0xabb160: d0 = 1.000000
    //     0xabb160: fmov            d0, #1.00000000
    // 0xabb164: r4 = const [0, 0x7, 0x2, 0x5, borderType, 0x5, radius, 0x6, null]
    //     0xabb164: add             x4, PP, #0x40, lsl #12  ; [pp+0x40088] List(9) [0, 0x7, 0x2, 0x5, "borderType", 0x5, "radius", 0x6, Null]
    //     0xabb168: ldr             x4, [x4, #0x88]
    // 0xabb16c: r0 = DottedBorder()
    //     0xabb16c: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0xabb170: r0 = Card()
    //     0xabb170: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xabb174: mov             x1, x0
    // 0xabb178: r0 = 0.000000
    //     0xabb178: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xabb17c: ArrayStore: r1[0] = r0  ; List_4
    //     0xabb17c: stur            w0, [x1, #0x17]
    // 0xabb180: r0 = Instance_RoundedRectangleBorder
    //     0xabb180: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4d0] Obj!RoundedRectangleBorder@d5abf1
    //     0xabb184: ldr             x0, [x0, #0x4d0]
    // 0xabb188: StoreField: r1->field_1b = r0
    //     0xabb188: stur            w0, [x1, #0x1b]
    // 0xabb18c: r0 = true
    //     0xabb18c: add             x0, NULL, #0x20  ; true
    // 0xabb190: StoreField: r1->field_1f = r0
    //     0xabb190: stur            w0, [x1, #0x1f]
    // 0xabb194: r2 = Instance_EdgeInsets
    //     0xabb194: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xabb198: StoreField: r1->field_27 = r2
    //     0xabb198: stur            w2, [x1, #0x27]
    // 0xabb19c: ldur            x2, [fp, #-0x10]
    // 0xabb1a0: StoreField: r1->field_2f = r2
    //     0xabb1a0: stur            w2, [x1, #0x2f]
    // 0xabb1a4: StoreField: r1->field_2b = r0
    //     0xabb1a4: stur            w0, [x1, #0x2b]
    // 0xabb1a8: r0 = Instance__CardVariant
    //     0xabb1a8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xabb1ac: ldr             x0, [x0, #0xa68]
    // 0xabb1b0: StoreField: r1->field_33 = r0
    //     0xabb1b0: stur            w0, [x1, #0x33]
    // 0xabb1b4: mov             x0, x1
    // 0xabb1b8: stur            x0, [fp, #-8]
    // 0xabb1bc: r0 = Padding()
    //     0xabb1bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabb1c0: r1 = Instance_EdgeInsets
    //     0xabb1c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xabb1c4: ldr             x1, [x1, #0x980]
    // 0xabb1c8: StoreField: r0->field_f = r1
    //     0xabb1c8: stur            w1, [x0, #0xf]
    // 0xabb1cc: ldur            x1, [fp, #-8]
    // 0xabb1d0: StoreField: r0->field_b = r1
    //     0xabb1d0: stur            w1, [x0, #0xb]
    // 0xabb1d4: LeaveFrame
    //     0xabb1d4: mov             SP, fp
    //     0xabb1d8: ldp             fp, lr, [SP], #0x10
    // 0xabb1dc: ret
    //     0xabb1dc: ret             
    // 0xabb1e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabb1e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabb1e4: b               #0xab9168
    // 0xabb1e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb1e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb1ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb1ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb1f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb1f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb1f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb1f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb1f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xabb1f8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xabb1fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb1fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb200: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb200: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb204: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb204: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb208: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb208: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb20c: SaveReg d0
    //     0xabb20c: str             q0, [SP, #-0x10]!
    // 0xabb210: stp             x0, x1, [SP, #-0x10]!
    // 0xabb214: r0 = AllocateDouble()
    //     0xabb214: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xabb218: mov             x2, x0
    // 0xabb21c: ldp             x0, x1, [SP], #0x10
    // 0xabb220: RestoreReg d0
    //     0xabb220: ldr             q0, [SP], #0x10
    // 0xabb224: b               #0xab9938
    // 0xabb228: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb228: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb22c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb22c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb230: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb230: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb234: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb234: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb238: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb238: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb23c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb23c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb240: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb244: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb248: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb248: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb24c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb24c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb250: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb250: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb254: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb254: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb258: r0 = NullCastErrorSharedWithFPURegs()
    //     0xabb258: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xabb25c: r0 = RangeErrorSharedWithFPURegs()
    //     0xabb25c: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xabb260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb264: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb264: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb268: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb268: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb26c: SaveReg d0
    //     0xabb26c: str             q0, [SP, #-0x10]!
    // 0xabb270: stp             x0, x1, [SP, #-0x10]!
    // 0xabb274: r0 = AllocateDouble()
    //     0xabb274: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xabb278: mov             x2, x0
    // 0xabb27c: ldp             x0, x1, [SP], #0x10
    // 0xabb280: RestoreReg d0
    //     0xabb280: ldr             q0, [SP], #0x10
    // 0xabb284: b               #0xaba7a8
    // 0xabb288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb288: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb28c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb28c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb290: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb294: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb298: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb298: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb29c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb29c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb2a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb2a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb2a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb2a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb2a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb2a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb2ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb2ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb2b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb2b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabb2b4, size: 0x358
    // 0xabb2b4: EnterFrame
    //     0xabb2b4: stp             fp, lr, [SP, #-0x10]!
    //     0xabb2b8: mov             fp, SP
    // 0xabb2bc: AllocStack(0x28)
    //     0xabb2bc: sub             SP, SP, #0x28
    // 0xabb2c0: SetupParameters()
    //     0xabb2c0: ldr             x0, [fp, #0x10]
    //     0xabb2c4: ldur            w2, [x0, #0x17]
    //     0xabb2c8: add             x2, x2, HEAP, lsl #32
    //     0xabb2cc: stur            x2, [fp, #-8]
    // 0xabb2d0: CheckStackOverflow
    //     0xabb2d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabb2d4: cmp             SP, x16
    //     0xabb2d8: b.ls            #0xabb5ec
    // 0xabb2dc: LoadField: r0 = r2->field_b
    //     0xabb2dc: ldur            w0, [x2, #0xb]
    // 0xabb2e0: DecompressPointer r0
    //     0xabb2e0: add             x0, x0, HEAP, lsl #32
    // 0xabb2e4: LoadField: r1 = r0->field_f
    //     0xabb2e4: ldur            w1, [x0, #0xf]
    // 0xabb2e8: DecompressPointer r1
    //     0xabb2e8: add             x1, x1, HEAP, lsl #32
    // 0xabb2ec: LoadField: r3 = r1->field_b
    //     0xabb2ec: ldur            w3, [x1, #0xb]
    // 0xabb2f0: DecompressPointer r3
    //     0xabb2f0: add             x3, x3, HEAP, lsl #32
    // 0xabb2f4: cmp             w3, NULL
    // 0xabb2f8: b.eq            #0xabb5f4
    // 0xabb2fc: LoadField: r0 = r3->field_f
    //     0xabb2fc: ldur            w0, [x3, #0xf]
    // 0xabb300: DecompressPointer r0
    //     0xabb300: add             x0, x0, HEAP, lsl #32
    // 0xabb304: cmp             w0, NULL
    // 0xabb308: b.ne            #0xabb314
    // 0xabb30c: r0 = Null
    //     0xabb30c: mov             x0, NULL
    // 0xabb310: b               #0xabb360
    // 0xabb314: LoadField: r4 = r0->field_f
    //     0xabb314: ldur            w4, [x0, #0xf]
    // 0xabb318: DecompressPointer r4
    //     0xabb318: add             x4, x4, HEAP, lsl #32
    // 0xabb31c: LoadField: r0 = r2->field_13
    //     0xabb31c: ldur            w0, [x2, #0x13]
    // 0xabb320: DecompressPointer r0
    //     0xabb320: add             x0, x0, HEAP, lsl #32
    // 0xabb324: LoadField: r1 = r4->field_b
    //     0xabb324: ldur            w1, [x4, #0xb]
    // 0xabb328: r5 = LoadInt32Instr(r0)
    //     0xabb328: sbfx            x5, x0, #1, #0x1f
    //     0xabb32c: tbz             w0, #0, #0xabb334
    //     0xabb330: ldur            x5, [x0, #7]
    // 0xabb334: r0 = LoadInt32Instr(r1)
    //     0xabb334: sbfx            x0, x1, #1, #0x1f
    // 0xabb338: mov             x1, x5
    // 0xabb33c: cmp             x1, x0
    // 0xabb340: b.hs            #0xabb5f8
    // 0xabb344: LoadField: r0 = r4->field_f
    //     0xabb344: ldur            w0, [x4, #0xf]
    // 0xabb348: DecompressPointer r0
    //     0xabb348: add             x0, x0, HEAP, lsl #32
    // 0xabb34c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabb34c: add             x16, x0, x5, lsl #2
    //     0xabb350: ldur            w1, [x16, #0xf]
    // 0xabb354: DecompressPointer r1
    //     0xabb354: add             x1, x1, HEAP, lsl #32
    // 0xabb358: LoadField: r0 = r1->field_27
    //     0xabb358: ldur            w0, [x1, #0x27]
    // 0xabb35c: DecompressPointer r0
    //     0xabb35c: add             x0, x0, HEAP, lsl #32
    // 0xabb360: cmp             w0, NULL
    // 0xabb364: b.eq            #0xabb5dc
    // 0xabb368: tbnz            w0, #4, #0xabb5dc
    // 0xabb36c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xabb36c: ldur            w0, [x3, #0x17]
    // 0xabb370: DecompressPointer r0
    //     0xabb370: add             x0, x0, HEAP, lsl #32
    // 0xabb374: cmp             w0, NULL
    // 0xabb378: b.eq            #0xabb494
    // 0xabb37c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xabb37c: ldur            w4, [x3, #0x17]
    // 0xabb380: DecompressPointer r4
    //     0xabb380: add             x4, x4, HEAP, lsl #32
    // 0xabb384: cmp             w4, NULL
    // 0xabb388: b.eq            #0xabb5c0
    // 0xabb38c: LoadField: r0 = r3->field_f
    //     0xabb38c: ldur            w0, [x3, #0xf]
    // 0xabb390: DecompressPointer r0
    //     0xabb390: add             x0, x0, HEAP, lsl #32
    // 0xabb394: cmp             w0, NULL
    // 0xabb398: b.ne            #0xabb3a4
    // 0xabb39c: r0 = Null
    //     0xabb39c: mov             x0, NULL
    // 0xabb3a0: b               #0xabb3f0
    // 0xabb3a4: LoadField: r5 = r0->field_f
    //     0xabb3a4: ldur            w5, [x0, #0xf]
    // 0xabb3a8: DecompressPointer r5
    //     0xabb3a8: add             x5, x5, HEAP, lsl #32
    // 0xabb3ac: LoadField: r0 = r2->field_13
    //     0xabb3ac: ldur            w0, [x2, #0x13]
    // 0xabb3b0: DecompressPointer r0
    //     0xabb3b0: add             x0, x0, HEAP, lsl #32
    // 0xabb3b4: LoadField: r1 = r5->field_b
    //     0xabb3b4: ldur            w1, [x5, #0xb]
    // 0xabb3b8: r6 = LoadInt32Instr(r0)
    //     0xabb3b8: sbfx            x6, x0, #1, #0x1f
    //     0xabb3bc: tbz             w0, #0, #0xabb3c4
    //     0xabb3c0: ldur            x6, [x0, #7]
    // 0xabb3c4: r0 = LoadInt32Instr(r1)
    //     0xabb3c4: sbfx            x0, x1, #1, #0x1f
    // 0xabb3c8: mov             x1, x6
    // 0xabb3cc: cmp             x1, x0
    // 0xabb3d0: b.hs            #0xabb5fc
    // 0xabb3d4: LoadField: r0 = r5->field_f
    //     0xabb3d4: ldur            w0, [x5, #0xf]
    // 0xabb3d8: DecompressPointer r0
    //     0xabb3d8: add             x0, x0, HEAP, lsl #32
    // 0xabb3dc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabb3dc: add             x16, x0, x6, lsl #2
    //     0xabb3e0: ldur            w1, [x16, #0xf]
    // 0xabb3e4: DecompressPointer r1
    //     0xabb3e4: add             x1, x1, HEAP, lsl #32
    // 0xabb3e8: LoadField: r0 = r1->field_7
    //     0xabb3e8: ldur            w0, [x1, #7]
    // 0xabb3ec: DecompressPointer r0
    //     0xabb3ec: add             x0, x0, HEAP, lsl #32
    // 0xabb3f0: cmp             w0, NULL
    // 0xabb3f4: b.ne            #0xabb400
    // 0xabb3f8: r5 = ""
    //     0xabb3f8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabb3fc: b               #0xabb404
    // 0xabb400: mov             x5, x0
    // 0xabb404: LoadField: r0 = r3->field_f
    //     0xabb404: ldur            w0, [x3, #0xf]
    // 0xabb408: DecompressPointer r0
    //     0xabb408: add             x0, x0, HEAP, lsl #32
    // 0xabb40c: cmp             w0, NULL
    // 0xabb410: b.ne            #0xabb41c
    // 0xabb414: r0 = Null
    //     0xabb414: mov             x0, NULL
    // 0xabb418: b               #0xabb468
    // 0xabb41c: LoadField: r3 = r0->field_f
    //     0xabb41c: ldur            w3, [x0, #0xf]
    // 0xabb420: DecompressPointer r3
    //     0xabb420: add             x3, x3, HEAP, lsl #32
    // 0xabb424: LoadField: r0 = r2->field_13
    //     0xabb424: ldur            w0, [x2, #0x13]
    // 0xabb428: DecompressPointer r0
    //     0xabb428: add             x0, x0, HEAP, lsl #32
    // 0xabb42c: LoadField: r1 = r3->field_b
    //     0xabb42c: ldur            w1, [x3, #0xb]
    // 0xabb430: r6 = LoadInt32Instr(r0)
    //     0xabb430: sbfx            x6, x0, #1, #0x1f
    //     0xabb434: tbz             w0, #0, #0xabb43c
    //     0xabb438: ldur            x6, [x0, #7]
    // 0xabb43c: r0 = LoadInt32Instr(r1)
    //     0xabb43c: sbfx            x0, x1, #1, #0x1f
    // 0xabb440: mov             x1, x6
    // 0xabb444: cmp             x1, x0
    // 0xabb448: b.hs            #0xabb600
    // 0xabb44c: LoadField: r0 = r3->field_f
    //     0xabb44c: ldur            w0, [x3, #0xf]
    // 0xabb450: DecompressPointer r0
    //     0xabb450: add             x0, x0, HEAP, lsl #32
    // 0xabb454: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabb454: add             x16, x0, x6, lsl #2
    //     0xabb458: ldur            w1, [x16, #0xf]
    // 0xabb45c: DecompressPointer r1
    //     0xabb45c: add             x1, x1, HEAP, lsl #32
    // 0xabb460: LoadField: r0 = r1->field_b
    //     0xabb460: ldur            w0, [x1, #0xb]
    // 0xabb464: DecompressPointer r0
    //     0xabb464: add             x0, x0, HEAP, lsl #32
    // 0xabb468: stp             x5, x4, [SP, #0x10]
    // 0xabb46c: r16 = "apply"
    //     0xabb46c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xabb470: ldr             x16, [x16, #0xfe8]
    // 0xabb474: stp             x16, x0, [SP]
    // 0xabb478: r4 = 0
    //     0xabb478: movz            x4, #0
    // 0xabb47c: ldr             x0, [SP, #0x18]
    // 0xabb480: r16 = UnlinkedCall_0x613b5c
    //     0xabb480: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6af98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabb484: add             x16, x16, #0xf98
    // 0xabb488: ldp             x5, lr, [x16]
    // 0xabb48c: blr             lr
    // 0xabb490: b               #0xabb5c0
    // 0xabb494: LoadField: r0 = r3->field_1b
    //     0xabb494: ldur            w0, [x3, #0x1b]
    // 0xabb498: DecompressPointer r0
    //     0xabb498: add             x0, x0, HEAP, lsl #32
    // 0xabb49c: cmp             w0, NULL
    // 0xabb4a0: b.eq            #0xabb5c0
    // 0xabb4a4: LoadField: r2 = r3->field_1b
    //     0xabb4a4: ldur            w2, [x3, #0x1b]
    // 0xabb4a8: DecompressPointer r2
    //     0xabb4a8: add             x2, x2, HEAP, lsl #32
    // 0xabb4ac: cmp             w2, NULL
    // 0xabb4b0: b.eq            #0xabb5c0
    // 0xabb4b4: LoadField: r0 = r3->field_f
    //     0xabb4b4: ldur            w0, [x3, #0xf]
    // 0xabb4b8: DecompressPointer r0
    //     0xabb4b8: add             x0, x0, HEAP, lsl #32
    // 0xabb4bc: cmp             w0, NULL
    // 0xabb4c0: b.ne            #0xabb4d0
    // 0xabb4c4: ldur            x4, [fp, #-8]
    // 0xabb4c8: r0 = Null
    //     0xabb4c8: mov             x0, NULL
    // 0xabb4cc: b               #0xabb520
    // 0xabb4d0: ldur            x4, [fp, #-8]
    // 0xabb4d4: LoadField: r5 = r0->field_f
    //     0xabb4d4: ldur            w5, [x0, #0xf]
    // 0xabb4d8: DecompressPointer r5
    //     0xabb4d8: add             x5, x5, HEAP, lsl #32
    // 0xabb4dc: LoadField: r0 = r4->field_13
    //     0xabb4dc: ldur            w0, [x4, #0x13]
    // 0xabb4e0: DecompressPointer r0
    //     0xabb4e0: add             x0, x0, HEAP, lsl #32
    // 0xabb4e4: LoadField: r1 = r5->field_b
    //     0xabb4e4: ldur            w1, [x5, #0xb]
    // 0xabb4e8: r6 = LoadInt32Instr(r0)
    //     0xabb4e8: sbfx            x6, x0, #1, #0x1f
    //     0xabb4ec: tbz             w0, #0, #0xabb4f4
    //     0xabb4f0: ldur            x6, [x0, #7]
    // 0xabb4f4: r0 = LoadInt32Instr(r1)
    //     0xabb4f4: sbfx            x0, x1, #1, #0x1f
    // 0xabb4f8: mov             x1, x6
    // 0xabb4fc: cmp             x1, x0
    // 0xabb500: b.hs            #0xabb604
    // 0xabb504: LoadField: r0 = r5->field_f
    //     0xabb504: ldur            w0, [x5, #0xf]
    // 0xabb508: DecompressPointer r0
    //     0xabb508: add             x0, x0, HEAP, lsl #32
    // 0xabb50c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabb50c: add             x16, x0, x6, lsl #2
    //     0xabb510: ldur            w1, [x16, #0xf]
    // 0xabb514: DecompressPointer r1
    //     0xabb514: add             x1, x1, HEAP, lsl #32
    // 0xabb518: LoadField: r0 = r1->field_7
    //     0xabb518: ldur            w0, [x1, #7]
    // 0xabb51c: DecompressPointer r0
    //     0xabb51c: add             x0, x0, HEAP, lsl #32
    // 0xabb520: cmp             w0, NULL
    // 0xabb524: b.ne            #0xabb530
    // 0xabb528: r5 = ""
    //     0xabb528: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabb52c: b               #0xabb534
    // 0xabb530: mov             x5, x0
    // 0xabb534: LoadField: r0 = r3->field_f
    //     0xabb534: ldur            w0, [x3, #0xf]
    // 0xabb538: DecompressPointer r0
    //     0xabb538: add             x0, x0, HEAP, lsl #32
    // 0xabb53c: cmp             w0, NULL
    // 0xabb540: b.ne            #0xabb54c
    // 0xabb544: r0 = Null
    //     0xabb544: mov             x0, NULL
    // 0xabb548: b               #0xabb598
    // 0xabb54c: LoadField: r3 = r0->field_f
    //     0xabb54c: ldur            w3, [x0, #0xf]
    // 0xabb550: DecompressPointer r3
    //     0xabb550: add             x3, x3, HEAP, lsl #32
    // 0xabb554: LoadField: r0 = r4->field_13
    //     0xabb554: ldur            w0, [x4, #0x13]
    // 0xabb558: DecompressPointer r0
    //     0xabb558: add             x0, x0, HEAP, lsl #32
    // 0xabb55c: LoadField: r1 = r3->field_b
    //     0xabb55c: ldur            w1, [x3, #0xb]
    // 0xabb560: r6 = LoadInt32Instr(r0)
    //     0xabb560: sbfx            x6, x0, #1, #0x1f
    //     0xabb564: tbz             w0, #0, #0xabb56c
    //     0xabb568: ldur            x6, [x0, #7]
    // 0xabb56c: r0 = LoadInt32Instr(r1)
    //     0xabb56c: sbfx            x0, x1, #1, #0x1f
    // 0xabb570: mov             x1, x6
    // 0xabb574: cmp             x1, x0
    // 0xabb578: b.hs            #0xabb608
    // 0xabb57c: LoadField: r0 = r3->field_f
    //     0xabb57c: ldur            w0, [x3, #0xf]
    // 0xabb580: DecompressPointer r0
    //     0xabb580: add             x0, x0, HEAP, lsl #32
    // 0xabb584: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabb584: add             x16, x0, x6, lsl #2
    //     0xabb588: ldur            w1, [x16, #0xf]
    // 0xabb58c: DecompressPointer r1
    //     0xabb58c: add             x1, x1, HEAP, lsl #32
    // 0xabb590: LoadField: r0 = r1->field_b
    //     0xabb590: ldur            w0, [x1, #0xb]
    // 0xabb594: DecompressPointer r0
    //     0xabb594: add             x0, x0, HEAP, lsl #32
    // 0xabb598: stp             x5, x2, [SP, #0x10]
    // 0xabb59c: r16 = "remove"
    //     0xabb59c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xabb5a0: ldr             x16, [x16, #0xff0]
    // 0xabb5a4: stp             x16, x0, [SP]
    // 0xabb5a8: r4 = 0
    //     0xabb5a8: movz            x4, #0
    // 0xabb5ac: ldr             x0, [SP, #0x18]
    // 0xabb5b0: r16 = UnlinkedCall_0x613b5c
    //     0xabb5b0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6afa8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabb5b4: add             x16, x16, #0xfa8
    // 0xabb5b8: ldp             x5, lr, [x16]
    // 0xabb5bc: blr             lr
    // 0xabb5c0: ldur            x0, [fp, #-8]
    // 0xabb5c4: LoadField: r1 = r0->field_f
    //     0xabb5c4: ldur            w1, [x0, #0xf]
    // 0xabb5c8: DecompressPointer r1
    //     0xabb5c8: add             x1, x1, HEAP, lsl #32
    // 0xabb5cc: r16 = <Object?>
    //     0xabb5cc: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xabb5d0: stp             x1, x16, [SP]
    // 0xabb5d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xabb5d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xabb5d8: r0 = pop()
    //     0xabb5d8: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xabb5dc: r0 = Null
    //     0xabb5dc: mov             x0, NULL
    // 0xabb5e0: LeaveFrame
    //     0xabb5e0: mov             SP, fp
    //     0xabb5e4: ldp             fp, lr, [SP], #0x10
    // 0xabb5e8: ret
    //     0xabb5e8: ret             
    // 0xabb5ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabb5ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabb5f0: b               #0xabb2dc
    // 0xabb5f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb5f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb5f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb5f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb5fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb5fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb600: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb600: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb604: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabb608: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb608: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabb60c, size: 0xa4
    // 0xabb60c: EnterFrame
    //     0xabb60c: stp             fp, lr, [SP, #-0x10]!
    //     0xabb610: mov             fp, SP
    // 0xabb614: AllocStack(0x40)
    //     0xabb614: sub             SP, SP, #0x40
    // 0xabb618: SetupParameters()
    //     0xabb618: ldr             x0, [fp, #0x10]
    //     0xabb61c: ldur            w2, [x0, #0x17]
    //     0xabb620: add             x2, x2, HEAP, lsl #32
    //     0xabb624: stur            x2, [fp, #-8]
    // 0xabb628: CheckStackOverflow
    //     0xabb628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabb62c: cmp             SP, x16
    //     0xabb630: b.ls            #0xabb6a8
    // 0xabb634: LoadField: r0 = r2->field_f
    //     0xabb634: ldur            w0, [x2, #0xf]
    // 0xabb638: DecompressPointer r0
    //     0xabb638: add             x0, x0, HEAP, lsl #32
    // 0xabb63c: r16 = <Object?>
    //     0xabb63c: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xabb640: stp             x0, x16, [SP]
    // 0xabb644: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xabb644: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xabb648: r0 = pop()
    //     0xabb648: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xabb64c: ldur            x2, [fp, #-8]
    // 0xabb650: LoadField: r0 = r2->field_f
    //     0xabb650: ldur            w0, [x2, #0xf]
    // 0xabb654: DecompressPointer r0
    //     0xabb654: add             x0, x0, HEAP, lsl #32
    // 0xabb658: stur            x0, [fp, #-0x10]
    // 0xabb65c: r1 = Function '<anonymous closure>':.
    //     0xabb65c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6afb8] AnonymousClosure: (0xabb6b0), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xabb660: ldr             x1, [x1, #0xfb8]
    // 0xabb664: r0 = AllocateClosure()
    //     0xabb664: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabb668: stp             x0, NULL, [SP, #0x20]
    // 0xabb66c: ldur            x16, [fp, #-0x10]
    // 0xabb670: r30 = true
    //     0xabb670: add             lr, NULL, #0x20  ; true
    // 0xabb674: stp             lr, x16, [SP, #0x10]
    // 0xabb678: r16 = Instance_Color
    //     0xabb678: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xabb67c: ldr             x16, [x16, #0x90]
    // 0xabb680: r30 = Instance_RoundedRectangleBorder
    //     0xabb680: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xabb684: ldr             lr, [lr, #0xc78]
    // 0xabb688: stp             lr, x16, [SP]
    // 0xabb68c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, shape, 0x4, null]
    //     0xabb68c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d308] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, "shape", 0x4, Null]
    //     0xabb690: ldr             x4, [x4, #0x308]
    // 0xabb694: r0 = showModalBottomSheet()
    //     0xabb694: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xabb698: r0 = Null
    //     0xabb698: mov             x0, NULL
    // 0xabb69c: LeaveFrame
    //     0xabb69c: mov             SP, fp
    //     0xabb6a0: ldp             fp, lr, [SP], #0x10
    // 0xabb6a4: ret
    //     0xabb6a4: ret             
    // 0xabb6a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabb6a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabb6ac: b               #0xabb634
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xabb6b0, size: 0x24c
    // 0xabb6b0: EnterFrame
    //     0xabb6b0: stp             fp, lr, [SP, #-0x10]!
    //     0xabb6b4: mov             fp, SP
    // 0xabb6b8: AllocStack(0x60)
    //     0xabb6b8: sub             SP, SP, #0x60
    // 0xabb6bc: SetupParameters()
    //     0xabb6bc: ldr             x0, [fp, #0x18]
    //     0xabb6c0: ldur            w1, [x0, #0x17]
    //     0xabb6c4: add             x1, x1, HEAP, lsl #32
    //     0xabb6c8: stur            x1, [fp, #-8]
    // 0xabb6cc: CheckStackOverflow
    //     0xabb6cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabb6d0: cmp             SP, x16
    //     0xabb6d4: b.ls            #0xabb8e8
    // 0xabb6d8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xabb6d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xabb6dc: ldr             x0, [x0, #0x1c80]
    //     0xabb6e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xabb6e4: cmp             w0, w16
    //     0xabb6e8: b.ne            #0xabb6f4
    //     0xabb6ec: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xabb6f0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xabb6f4: r0 = GetNavigation.size()
    //     0xabb6f4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xabb6f8: LoadField: d0 = r0->field_f
    //     0xabb6f8: ldur            d0, [x0, #0xf]
    // 0xabb6fc: d1 = 0.650000
    //     0xabb6fc: add             x17, PP, #0x45, lsl #12  ; [pp+0x45448] IMM: double(0.65) from 0x3fe4cccccccccccd
    //     0xabb700: ldr             d1, [x17, #0x448]
    // 0xabb704: fmul            d2, d0, d1
    // 0xabb708: ldur            x1, [fp, #-8]
    // 0xabb70c: stur            d2, [fp, #-0x48]
    // 0xabb710: LoadField: r2 = r1->field_b
    //     0xabb710: ldur            w2, [x1, #0xb]
    // 0xabb714: DecompressPointer r2
    //     0xabb714: add             x2, x2, HEAP, lsl #32
    // 0xabb718: stur            x2, [fp, #-0x10]
    // 0xabb71c: LoadField: r0 = r2->field_f
    //     0xabb71c: ldur            w0, [x2, #0xf]
    // 0xabb720: DecompressPointer r0
    //     0xabb720: add             x0, x0, HEAP, lsl #32
    // 0xabb724: LoadField: r3 = r0->field_b
    //     0xabb724: ldur            w3, [x0, #0xb]
    // 0xabb728: DecompressPointer r3
    //     0xabb728: add             x3, x3, HEAP, lsl #32
    // 0xabb72c: cmp             w3, NULL
    // 0xabb730: b.eq            #0xabb8f0
    // 0xabb734: LoadField: r0 = r3->field_b
    //     0xabb734: ldur            w0, [x3, #0xb]
    // 0xabb738: DecompressPointer r0
    //     0xabb738: add             x0, x0, HEAP, lsl #32
    // 0xabb73c: r3 = LoadClassIdInstr(r0)
    //     0xabb73c: ldur            x3, [x0, #-1]
    //     0xabb740: ubfx            x3, x3, #0xc, #0x14
    // 0xabb744: r16 = "checkout_offers"
    //     0xabb744: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xabb748: ldr             x16, [x16, #0x1c8]
    // 0xabb74c: stp             x16, x0, [SP]
    // 0xabb750: mov             x0, x3
    // 0xabb754: mov             lr, x0
    // 0xabb758: ldr             lr, [x21, lr, lsl #3]
    // 0xabb75c: blr             lr
    // 0xabb760: tbnz            w0, #4, #0xabb780
    // 0xabb764: r0 = GetNavigation.size()
    //     0xabb764: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xabb768: LoadField: d0 = r0->field_f
    //     0xabb768: ldur            d0, [x0, #0xf]
    // 0xabb76c: d1 = 0.300000
    //     0xabb76c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xabb770: ldr             d1, [x17, #0x658]
    // 0xabb774: fmul            d2, d0, d1
    // 0xabb778: mov             v1.16b, v2.16b
    // 0xabb77c: b               #0xabb798
    // 0xabb780: r0 = GetNavigation.size()
    //     0xabb780: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xabb784: LoadField: d0 = r0->field_f
    //     0xabb784: ldur            d0, [x0, #0xf]
    // 0xabb788: d1 = 0.450000
    //     0xabb788: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xabb78c: ldr             d1, [x17, #0xd08]
    // 0xabb790: fmul            d2, d0, d1
    // 0xabb794: mov             v1.16b, v2.16b
    // 0xabb798: ldur            d0, [fp, #-0x48]
    // 0xabb79c: ldur            x0, [fp, #-0x10]
    // 0xabb7a0: stur            d1, [fp, #-0x50]
    // 0xabb7a4: r0 = BoxConstraints()
    //     0xabb7a4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xabb7a8: mov             x2, x0
    // 0xabb7ac: stur            x2, [fp, #-0x38]
    // 0xabb7b0: StoreField: r2->field_7 = rZR
    //     0xabb7b0: stur            xzr, [x2, #7]
    // 0xabb7b4: d0 = inf
    //     0xabb7b4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xabb7b8: StoreField: r2->field_f = d0
    //     0xabb7b8: stur            d0, [x2, #0xf]
    // 0xabb7bc: ldur            d0, [fp, #-0x50]
    // 0xabb7c0: ArrayStore: r2[0] = d0  ; List_8
    //     0xabb7c0: stur            d0, [x2, #0x17]
    // 0xabb7c4: ldur            d0, [fp, #-0x48]
    // 0xabb7c8: StoreField: r2->field_1f = d0
    //     0xabb7c8: stur            d0, [x2, #0x1f]
    // 0xabb7cc: ldur            x0, [fp, #-0x10]
    // 0xabb7d0: LoadField: r1 = r0->field_f
    //     0xabb7d0: ldur            w1, [x0, #0xf]
    // 0xabb7d4: DecompressPointer r1
    //     0xabb7d4: add             x1, x1, HEAP, lsl #32
    // 0xabb7d8: LoadField: r3 = r1->field_b
    //     0xabb7d8: ldur            w3, [x1, #0xb]
    // 0xabb7dc: DecompressPointer r3
    //     0xabb7dc: add             x3, x3, HEAP, lsl #32
    // 0xabb7e0: cmp             w3, NULL
    // 0xabb7e4: b.eq            #0xabb8f4
    // 0xabb7e8: LoadField: r4 = r3->field_1f
    //     0xabb7e8: ldur            w4, [x3, #0x1f]
    // 0xabb7ec: DecompressPointer r4
    //     0xabb7ec: add             x4, x4, HEAP, lsl #32
    // 0xabb7f0: stur            x4, [fp, #-0x30]
    // 0xabb7f4: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xabb7f4: ldur            w5, [x3, #0x17]
    // 0xabb7f8: DecompressPointer r5
    //     0xabb7f8: add             x5, x5, HEAP, lsl #32
    // 0xabb7fc: stur            x5, [fp, #-0x28]
    // 0xabb800: LoadField: r6 = r3->field_1b
    //     0xabb800: ldur            w6, [x3, #0x1b]
    // 0xabb804: DecompressPointer r6
    //     0xabb804: add             x6, x6, HEAP, lsl #32
    // 0xabb808: stur            x6, [fp, #-0x20]
    // 0xabb80c: LoadField: r7 = r3->field_b
    //     0xabb80c: ldur            w7, [x3, #0xb]
    // 0xabb810: DecompressPointer r7
    //     0xabb810: add             x7, x7, HEAP, lsl #32
    // 0xabb814: stur            x7, [fp, #-0x18]
    // 0xabb818: LoadField: r0 = r3->field_f
    //     0xabb818: ldur            w0, [x3, #0xf]
    // 0xabb81c: DecompressPointer r0
    //     0xabb81c: add             x0, x0, HEAP, lsl #32
    // 0xabb820: cmp             w0, NULL
    // 0xabb824: b.ne            #0xabb830
    // 0xabb828: r0 = Null
    //     0xabb828: mov             x0, NULL
    // 0xabb82c: b               #0xabb87c
    // 0xabb830: ldur            x1, [fp, #-8]
    // 0xabb834: LoadField: r8 = r0->field_f
    //     0xabb834: ldur            w8, [x0, #0xf]
    // 0xabb838: DecompressPointer r8
    //     0xabb838: add             x8, x8, HEAP, lsl #32
    // 0xabb83c: LoadField: r0 = r1->field_13
    //     0xabb83c: ldur            w0, [x1, #0x13]
    // 0xabb840: DecompressPointer r0
    //     0xabb840: add             x0, x0, HEAP, lsl #32
    // 0xabb844: LoadField: r1 = r8->field_b
    //     0xabb844: ldur            w1, [x8, #0xb]
    // 0xabb848: r9 = LoadInt32Instr(r0)
    //     0xabb848: sbfx            x9, x0, #1, #0x1f
    //     0xabb84c: tbz             w0, #0, #0xabb854
    //     0xabb850: ldur            x9, [x0, #7]
    // 0xabb854: r0 = LoadInt32Instr(r1)
    //     0xabb854: sbfx            x0, x1, #1, #0x1f
    // 0xabb858: mov             x1, x9
    // 0xabb85c: cmp             x1, x0
    // 0xabb860: b.hs            #0xabb8f8
    // 0xabb864: LoadField: r0 = r8->field_f
    //     0xabb864: ldur            w0, [x8, #0xf]
    // 0xabb868: DecompressPointer r0
    //     0xabb868: add             x0, x0, HEAP, lsl #32
    // 0xabb86c: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xabb86c: add             x16, x0, x9, lsl #2
    //     0xabb870: ldur            w1, [x16, #0xf]
    // 0xabb874: DecompressPointer r1
    //     0xabb874: add             x1, x1, HEAP, lsl #32
    // 0xabb878: mov             x0, x1
    // 0xabb87c: stur            x0, [fp, #-0x10]
    // 0xabb880: LoadField: r1 = r3->field_13
    //     0xabb880: ldur            w1, [x3, #0x13]
    // 0xabb884: DecompressPointer r1
    //     0xabb884: add             x1, x1, HEAP, lsl #32
    // 0xabb888: stur            x1, [fp, #-8]
    // 0xabb88c: r0 = OffersBottomSheet()
    //     0xabb88c: bl              #0xabb8fc  ; AllocateOffersBottomSheetStub -> OffersBottomSheet (size=0x24)
    // 0xabb890: mov             x1, x0
    // 0xabb894: ldur            x0, [fp, #-0x10]
    // 0xabb898: stur            x1, [fp, #-0x40]
    // 0xabb89c: StoreField: r1->field_b = r0
    //     0xabb89c: stur            w0, [x1, #0xb]
    // 0xabb8a0: ldur            x0, [fp, #-8]
    // 0xabb8a4: StoreField: r1->field_f = r0
    //     0xabb8a4: stur            w0, [x1, #0xf]
    // 0xabb8a8: ldur            x0, [fp, #-0x18]
    // 0xabb8ac: StoreField: r1->field_13 = r0
    //     0xabb8ac: stur            w0, [x1, #0x13]
    // 0xabb8b0: ldur            x0, [fp, #-0x28]
    // 0xabb8b4: ArrayStore: r1[0] = r0  ; List_4
    //     0xabb8b4: stur            w0, [x1, #0x17]
    // 0xabb8b8: ldur            x0, [fp, #-0x20]
    // 0xabb8bc: StoreField: r1->field_1b = r0
    //     0xabb8bc: stur            w0, [x1, #0x1b]
    // 0xabb8c0: ldur            x0, [fp, #-0x30]
    // 0xabb8c4: StoreField: r1->field_1f = r0
    //     0xabb8c4: stur            w0, [x1, #0x1f]
    // 0xabb8c8: r0 = ConstrainedBox()
    //     0xabb8c8: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xabb8cc: ldur            x1, [fp, #-0x38]
    // 0xabb8d0: StoreField: r0->field_f = r1
    //     0xabb8d0: stur            w1, [x0, #0xf]
    // 0xabb8d4: ldur            x1, [fp, #-0x40]
    // 0xabb8d8: StoreField: r0->field_b = r1
    //     0xabb8d8: stur            w1, [x0, #0xb]
    // 0xabb8dc: LeaveFrame
    //     0xabb8dc: mov             SP, fp
    //     0xabb8e0: ldp             fp, lr, [SP], #0x10
    // 0xabb8e4: ret
    //     0xabb8e4: ret             
    // 0xabb8e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabb8e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabb8ec: b               #0xabb6d8
    // 0xabb8f0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xabb8f0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xabb8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabb8f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabb8f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabb8f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabb908, size: 0x364
    // 0xabb908: EnterFrame
    //     0xabb908: stp             fp, lr, [SP, #-0x10]!
    //     0xabb90c: mov             fp, SP
    // 0xabb910: AllocStack(0x28)
    //     0xabb910: sub             SP, SP, #0x28
    // 0xabb914: SetupParameters()
    //     0xabb914: ldr             x0, [fp, #0x10]
    //     0xabb918: ldur            w2, [x0, #0x17]
    //     0xabb91c: add             x2, x2, HEAP, lsl #32
    //     0xabb920: stur            x2, [fp, #-8]
    // 0xabb924: CheckStackOverflow
    //     0xabb924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabb928: cmp             SP, x16
    //     0xabb92c: b.ls            #0xabbc4c
    // 0xabb930: LoadField: r0 = r2->field_b
    //     0xabb930: ldur            w0, [x2, #0xb]
    // 0xabb934: DecompressPointer r0
    //     0xabb934: add             x0, x0, HEAP, lsl #32
    // 0xabb938: LoadField: r1 = r0->field_f
    //     0xabb938: ldur            w1, [x0, #0xf]
    // 0xabb93c: DecompressPointer r1
    //     0xabb93c: add             x1, x1, HEAP, lsl #32
    // 0xabb940: LoadField: r3 = r1->field_b
    //     0xabb940: ldur            w3, [x1, #0xb]
    // 0xabb944: DecompressPointer r3
    //     0xabb944: add             x3, x3, HEAP, lsl #32
    // 0xabb948: cmp             w3, NULL
    // 0xabb94c: b.eq            #0xabbc54
    // 0xabb950: LoadField: r0 = r3->field_1f
    //     0xabb950: ldur            w0, [x3, #0x1f]
    // 0xabb954: DecompressPointer r0
    //     0xabb954: add             x0, x0, HEAP, lsl #32
    // 0xabb958: tbnz            w0, #4, #0xabbc3c
    // 0xabb95c: LoadField: r0 = r3->field_f
    //     0xabb95c: ldur            w0, [x3, #0xf]
    // 0xabb960: DecompressPointer r0
    //     0xabb960: add             x0, x0, HEAP, lsl #32
    // 0xabb964: cmp             w0, NULL
    // 0xabb968: b.ne            #0xabb974
    // 0xabb96c: r0 = Null
    //     0xabb96c: mov             x0, NULL
    // 0xabb970: b               #0xabb9c0
    // 0xabb974: LoadField: r4 = r0->field_f
    //     0xabb974: ldur            w4, [x0, #0xf]
    // 0xabb978: DecompressPointer r4
    //     0xabb978: add             x4, x4, HEAP, lsl #32
    // 0xabb97c: LoadField: r0 = r2->field_13
    //     0xabb97c: ldur            w0, [x2, #0x13]
    // 0xabb980: DecompressPointer r0
    //     0xabb980: add             x0, x0, HEAP, lsl #32
    // 0xabb984: LoadField: r1 = r4->field_b
    //     0xabb984: ldur            w1, [x4, #0xb]
    // 0xabb988: r5 = LoadInt32Instr(r0)
    //     0xabb988: sbfx            x5, x0, #1, #0x1f
    //     0xabb98c: tbz             w0, #0, #0xabb994
    //     0xabb990: ldur            x5, [x0, #7]
    // 0xabb994: r0 = LoadInt32Instr(r1)
    //     0xabb994: sbfx            x0, x1, #1, #0x1f
    // 0xabb998: mov             x1, x5
    // 0xabb99c: cmp             x1, x0
    // 0xabb9a0: b.hs            #0xabbc58
    // 0xabb9a4: LoadField: r0 = r4->field_f
    //     0xabb9a4: ldur            w0, [x4, #0xf]
    // 0xabb9a8: DecompressPointer r0
    //     0xabb9a8: add             x0, x0, HEAP, lsl #32
    // 0xabb9ac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabb9ac: add             x16, x0, x5, lsl #2
    //     0xabb9b0: ldur            w1, [x16, #0xf]
    // 0xabb9b4: DecompressPointer r1
    //     0xabb9b4: add             x1, x1, HEAP, lsl #32
    // 0xabb9b8: LoadField: r0 = r1->field_27
    //     0xabb9b8: ldur            w0, [x1, #0x27]
    // 0xabb9bc: DecompressPointer r0
    //     0xabb9bc: add             x0, x0, HEAP, lsl #32
    // 0xabb9c0: cmp             w0, NULL
    // 0xabb9c4: b.eq            #0xabbc3c
    // 0xabb9c8: tbnz            w0, #4, #0xabbc3c
    // 0xabb9cc: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xabb9cc: ldur            w0, [x3, #0x17]
    // 0xabb9d0: DecompressPointer r0
    //     0xabb9d0: add             x0, x0, HEAP, lsl #32
    // 0xabb9d4: cmp             w0, NULL
    // 0xabb9d8: b.eq            #0xabbaf4
    // 0xabb9dc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xabb9dc: ldur            w4, [x3, #0x17]
    // 0xabb9e0: DecompressPointer r4
    //     0xabb9e0: add             x4, x4, HEAP, lsl #32
    // 0xabb9e4: cmp             w4, NULL
    // 0xabb9e8: b.eq            #0xabbc20
    // 0xabb9ec: LoadField: r0 = r3->field_f
    //     0xabb9ec: ldur            w0, [x3, #0xf]
    // 0xabb9f0: DecompressPointer r0
    //     0xabb9f0: add             x0, x0, HEAP, lsl #32
    // 0xabb9f4: cmp             w0, NULL
    // 0xabb9f8: b.ne            #0xabba04
    // 0xabb9fc: r0 = Null
    //     0xabb9fc: mov             x0, NULL
    // 0xabba00: b               #0xabba50
    // 0xabba04: LoadField: r5 = r0->field_f
    //     0xabba04: ldur            w5, [x0, #0xf]
    // 0xabba08: DecompressPointer r5
    //     0xabba08: add             x5, x5, HEAP, lsl #32
    // 0xabba0c: LoadField: r0 = r2->field_13
    //     0xabba0c: ldur            w0, [x2, #0x13]
    // 0xabba10: DecompressPointer r0
    //     0xabba10: add             x0, x0, HEAP, lsl #32
    // 0xabba14: LoadField: r1 = r5->field_b
    //     0xabba14: ldur            w1, [x5, #0xb]
    // 0xabba18: r6 = LoadInt32Instr(r0)
    //     0xabba18: sbfx            x6, x0, #1, #0x1f
    //     0xabba1c: tbz             w0, #0, #0xabba24
    //     0xabba20: ldur            x6, [x0, #7]
    // 0xabba24: r0 = LoadInt32Instr(r1)
    //     0xabba24: sbfx            x0, x1, #1, #0x1f
    // 0xabba28: mov             x1, x6
    // 0xabba2c: cmp             x1, x0
    // 0xabba30: b.hs            #0xabbc5c
    // 0xabba34: LoadField: r0 = r5->field_f
    //     0xabba34: ldur            w0, [x5, #0xf]
    // 0xabba38: DecompressPointer r0
    //     0xabba38: add             x0, x0, HEAP, lsl #32
    // 0xabba3c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabba3c: add             x16, x0, x6, lsl #2
    //     0xabba40: ldur            w1, [x16, #0xf]
    // 0xabba44: DecompressPointer r1
    //     0xabba44: add             x1, x1, HEAP, lsl #32
    // 0xabba48: LoadField: r0 = r1->field_7
    //     0xabba48: ldur            w0, [x1, #7]
    // 0xabba4c: DecompressPointer r0
    //     0xabba4c: add             x0, x0, HEAP, lsl #32
    // 0xabba50: cmp             w0, NULL
    // 0xabba54: b.ne            #0xabba60
    // 0xabba58: r5 = ""
    //     0xabba58: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabba5c: b               #0xabba64
    // 0xabba60: mov             x5, x0
    // 0xabba64: LoadField: r0 = r3->field_f
    //     0xabba64: ldur            w0, [x3, #0xf]
    // 0xabba68: DecompressPointer r0
    //     0xabba68: add             x0, x0, HEAP, lsl #32
    // 0xabba6c: cmp             w0, NULL
    // 0xabba70: b.ne            #0xabba7c
    // 0xabba74: r0 = Null
    //     0xabba74: mov             x0, NULL
    // 0xabba78: b               #0xabbac8
    // 0xabba7c: LoadField: r3 = r0->field_f
    //     0xabba7c: ldur            w3, [x0, #0xf]
    // 0xabba80: DecompressPointer r3
    //     0xabba80: add             x3, x3, HEAP, lsl #32
    // 0xabba84: LoadField: r0 = r2->field_13
    //     0xabba84: ldur            w0, [x2, #0x13]
    // 0xabba88: DecompressPointer r0
    //     0xabba88: add             x0, x0, HEAP, lsl #32
    // 0xabba8c: LoadField: r1 = r3->field_b
    //     0xabba8c: ldur            w1, [x3, #0xb]
    // 0xabba90: r6 = LoadInt32Instr(r0)
    //     0xabba90: sbfx            x6, x0, #1, #0x1f
    //     0xabba94: tbz             w0, #0, #0xabba9c
    //     0xabba98: ldur            x6, [x0, #7]
    // 0xabba9c: r0 = LoadInt32Instr(r1)
    //     0xabba9c: sbfx            x0, x1, #1, #0x1f
    // 0xabbaa0: mov             x1, x6
    // 0xabbaa4: cmp             x1, x0
    // 0xabbaa8: b.hs            #0xabbc60
    // 0xabbaac: LoadField: r0 = r3->field_f
    //     0xabbaac: ldur            w0, [x3, #0xf]
    // 0xabbab0: DecompressPointer r0
    //     0xabbab0: add             x0, x0, HEAP, lsl #32
    // 0xabbab4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabbab4: add             x16, x0, x6, lsl #2
    //     0xabbab8: ldur            w1, [x16, #0xf]
    // 0xabbabc: DecompressPointer r1
    //     0xabbabc: add             x1, x1, HEAP, lsl #32
    // 0xabbac0: LoadField: r0 = r1->field_b
    //     0xabbac0: ldur            w0, [x1, #0xb]
    // 0xabbac4: DecompressPointer r0
    //     0xabbac4: add             x0, x0, HEAP, lsl #32
    // 0xabbac8: stp             x5, x4, [SP, #0x10]
    // 0xabbacc: r16 = "apply"
    //     0xabbacc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xabbad0: ldr             x16, [x16, #0xfe8]
    // 0xabbad4: stp             x16, x0, [SP]
    // 0xabbad8: r4 = 0
    //     0xabbad8: movz            x4, #0
    // 0xabbadc: ldr             x0, [SP, #0x18]
    // 0xabbae0: r16 = UnlinkedCall_0x613b5c
    //     0xabbae0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6afc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabbae4: add             x16, x16, #0xfc0
    // 0xabbae8: ldp             x5, lr, [x16]
    // 0xabbaec: blr             lr
    // 0xabbaf0: b               #0xabbc20
    // 0xabbaf4: LoadField: r0 = r3->field_1b
    //     0xabbaf4: ldur            w0, [x3, #0x1b]
    // 0xabbaf8: DecompressPointer r0
    //     0xabbaf8: add             x0, x0, HEAP, lsl #32
    // 0xabbafc: cmp             w0, NULL
    // 0xabbb00: b.eq            #0xabbc20
    // 0xabbb04: LoadField: r2 = r3->field_1b
    //     0xabbb04: ldur            w2, [x3, #0x1b]
    // 0xabbb08: DecompressPointer r2
    //     0xabbb08: add             x2, x2, HEAP, lsl #32
    // 0xabbb0c: cmp             w2, NULL
    // 0xabbb10: b.eq            #0xabbc20
    // 0xabbb14: LoadField: r0 = r3->field_f
    //     0xabbb14: ldur            w0, [x3, #0xf]
    // 0xabbb18: DecompressPointer r0
    //     0xabbb18: add             x0, x0, HEAP, lsl #32
    // 0xabbb1c: cmp             w0, NULL
    // 0xabbb20: b.ne            #0xabbb30
    // 0xabbb24: ldur            x4, [fp, #-8]
    // 0xabbb28: r0 = Null
    //     0xabbb28: mov             x0, NULL
    // 0xabbb2c: b               #0xabbb80
    // 0xabbb30: ldur            x4, [fp, #-8]
    // 0xabbb34: LoadField: r5 = r0->field_f
    //     0xabbb34: ldur            w5, [x0, #0xf]
    // 0xabbb38: DecompressPointer r5
    //     0xabbb38: add             x5, x5, HEAP, lsl #32
    // 0xabbb3c: LoadField: r0 = r4->field_13
    //     0xabbb3c: ldur            w0, [x4, #0x13]
    // 0xabbb40: DecompressPointer r0
    //     0xabbb40: add             x0, x0, HEAP, lsl #32
    // 0xabbb44: LoadField: r1 = r5->field_b
    //     0xabbb44: ldur            w1, [x5, #0xb]
    // 0xabbb48: r6 = LoadInt32Instr(r0)
    //     0xabbb48: sbfx            x6, x0, #1, #0x1f
    //     0xabbb4c: tbz             w0, #0, #0xabbb54
    //     0xabbb50: ldur            x6, [x0, #7]
    // 0xabbb54: r0 = LoadInt32Instr(r1)
    //     0xabbb54: sbfx            x0, x1, #1, #0x1f
    // 0xabbb58: mov             x1, x6
    // 0xabbb5c: cmp             x1, x0
    // 0xabbb60: b.hs            #0xabbc64
    // 0xabbb64: LoadField: r0 = r5->field_f
    //     0xabbb64: ldur            w0, [x5, #0xf]
    // 0xabbb68: DecompressPointer r0
    //     0xabbb68: add             x0, x0, HEAP, lsl #32
    // 0xabbb6c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabbb6c: add             x16, x0, x6, lsl #2
    //     0xabbb70: ldur            w1, [x16, #0xf]
    // 0xabbb74: DecompressPointer r1
    //     0xabbb74: add             x1, x1, HEAP, lsl #32
    // 0xabbb78: LoadField: r0 = r1->field_7
    //     0xabbb78: ldur            w0, [x1, #7]
    // 0xabbb7c: DecompressPointer r0
    //     0xabbb7c: add             x0, x0, HEAP, lsl #32
    // 0xabbb80: cmp             w0, NULL
    // 0xabbb84: b.ne            #0xabbb90
    // 0xabbb88: r5 = ""
    //     0xabbb88: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabbb8c: b               #0xabbb94
    // 0xabbb90: mov             x5, x0
    // 0xabbb94: LoadField: r0 = r3->field_f
    //     0xabbb94: ldur            w0, [x3, #0xf]
    // 0xabbb98: DecompressPointer r0
    //     0xabbb98: add             x0, x0, HEAP, lsl #32
    // 0xabbb9c: cmp             w0, NULL
    // 0xabbba0: b.ne            #0xabbbac
    // 0xabbba4: r0 = Null
    //     0xabbba4: mov             x0, NULL
    // 0xabbba8: b               #0xabbbf8
    // 0xabbbac: LoadField: r3 = r0->field_f
    //     0xabbbac: ldur            w3, [x0, #0xf]
    // 0xabbbb0: DecompressPointer r3
    //     0xabbbb0: add             x3, x3, HEAP, lsl #32
    // 0xabbbb4: LoadField: r0 = r4->field_13
    //     0xabbbb4: ldur            w0, [x4, #0x13]
    // 0xabbbb8: DecompressPointer r0
    //     0xabbbb8: add             x0, x0, HEAP, lsl #32
    // 0xabbbbc: LoadField: r1 = r3->field_b
    //     0xabbbbc: ldur            w1, [x3, #0xb]
    // 0xabbbc0: r6 = LoadInt32Instr(r0)
    //     0xabbbc0: sbfx            x6, x0, #1, #0x1f
    //     0xabbbc4: tbz             w0, #0, #0xabbbcc
    //     0xabbbc8: ldur            x6, [x0, #7]
    // 0xabbbcc: r0 = LoadInt32Instr(r1)
    //     0xabbbcc: sbfx            x0, x1, #1, #0x1f
    // 0xabbbd0: mov             x1, x6
    // 0xabbbd4: cmp             x1, x0
    // 0xabbbd8: b.hs            #0xabbc68
    // 0xabbbdc: LoadField: r0 = r3->field_f
    //     0xabbbdc: ldur            w0, [x3, #0xf]
    // 0xabbbe0: DecompressPointer r0
    //     0xabbbe0: add             x0, x0, HEAP, lsl #32
    // 0xabbbe4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabbbe4: add             x16, x0, x6, lsl #2
    //     0xabbbe8: ldur            w1, [x16, #0xf]
    // 0xabbbec: DecompressPointer r1
    //     0xabbbec: add             x1, x1, HEAP, lsl #32
    // 0xabbbf0: LoadField: r0 = r1->field_b
    //     0xabbbf0: ldur            w0, [x1, #0xb]
    // 0xabbbf4: DecompressPointer r0
    //     0xabbbf4: add             x0, x0, HEAP, lsl #32
    // 0xabbbf8: stp             x5, x2, [SP, #0x10]
    // 0xabbbfc: r16 = "remove"
    //     0xabbbfc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xabbc00: ldr             x16, [x16, #0xff0]
    // 0xabbc04: stp             x16, x0, [SP]
    // 0xabbc08: r4 = 0
    //     0xabbc08: movz            x4, #0
    // 0xabbc0c: ldr             x0, [SP, #0x18]
    // 0xabbc10: r16 = UnlinkedCall_0x613b5c
    //     0xabbc10: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6afd0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabbc14: add             x16, x16, #0xfd0
    // 0xabbc18: ldp             x5, lr, [x16]
    // 0xabbc1c: blr             lr
    // 0xabbc20: ldur            x0, [fp, #-8]
    // 0xabbc24: LoadField: r1 = r0->field_f
    //     0xabbc24: ldur            w1, [x0, #0xf]
    // 0xabbc28: DecompressPointer r1
    //     0xabbc28: add             x1, x1, HEAP, lsl #32
    // 0xabbc2c: r16 = <Object?>
    //     0xabbc2c: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xabbc30: stp             x1, x16, [SP]
    // 0xabbc34: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xabbc34: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xabbc38: r0 = pop()
    //     0xabbc38: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xabbc3c: r0 = Null
    //     0xabbc3c: mov             x0, NULL
    // 0xabbc40: LeaveFrame
    //     0xabbc40: mov             SP, fp
    //     0xabbc44: ldp             fp, lr, [SP], #0x10
    // 0xabbc48: ret
    //     0xabbc48: ret             
    // 0xabbc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabbc4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabbc50: b               #0xabb930
    // 0xabbc54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabbc54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabbc58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabbc58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabbc5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabbc5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabbc60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabbc60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabbc64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabbc64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabbc68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabbc68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabbc6c, size: 0xa4
    // 0xabbc6c: EnterFrame
    //     0xabbc6c: stp             fp, lr, [SP, #-0x10]!
    //     0xabbc70: mov             fp, SP
    // 0xabbc74: AllocStack(0x40)
    //     0xabbc74: sub             SP, SP, #0x40
    // 0xabbc78: SetupParameters()
    //     0xabbc78: ldr             x0, [fp, #0x10]
    //     0xabbc7c: ldur            w2, [x0, #0x17]
    //     0xabbc80: add             x2, x2, HEAP, lsl #32
    //     0xabbc84: stur            x2, [fp, #-8]
    // 0xabbc88: CheckStackOverflow
    //     0xabbc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabbc8c: cmp             SP, x16
    //     0xabbc90: b.ls            #0xabbd08
    // 0xabbc94: LoadField: r0 = r2->field_f
    //     0xabbc94: ldur            w0, [x2, #0xf]
    // 0xabbc98: DecompressPointer r0
    //     0xabbc98: add             x0, x0, HEAP, lsl #32
    // 0xabbc9c: r16 = <Object?>
    //     0xabbc9c: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xabbca0: stp             x0, x16, [SP]
    // 0xabbca4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xabbca4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xabbca8: r0 = pop()
    //     0xabbca8: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xabbcac: ldur            x2, [fp, #-8]
    // 0xabbcb0: LoadField: r0 = r2->field_f
    //     0xabbcb0: ldur            w0, [x2, #0xf]
    // 0xabbcb4: DecompressPointer r0
    //     0xabbcb4: add             x0, x0, HEAP, lsl #32
    // 0xabbcb8: stur            x0, [fp, #-0x10]
    // 0xabbcbc: r1 = Function '<anonymous closure>':.
    //     0xabbcbc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6afe0] AnonymousClosure: (0xabb6b0), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xab8e64)
    //     0xabbcc0: ldr             x1, [x1, #0xfe0]
    // 0xabbcc4: r0 = AllocateClosure()
    //     0xabbcc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabbcc8: stp             x0, NULL, [SP, #0x20]
    // 0xabbccc: ldur            x16, [fp, #-0x10]
    // 0xabbcd0: r30 = true
    //     0xabbcd0: add             lr, NULL, #0x20  ; true
    // 0xabbcd4: stp             lr, x16, [SP, #0x10]
    // 0xabbcd8: r16 = Instance_Color
    //     0xabbcd8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xabbcdc: ldr             x16, [x16, #0x90]
    // 0xabbce0: r30 = Instance_RoundedRectangleBorder
    //     0xabbce0: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xabbce4: ldr             lr, [lr, #0xc78]
    // 0xabbce8: stp             lr, x16, [SP]
    // 0xabbcec: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, shape, 0x4, null]
    //     0xabbcec: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d308] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, "shape", 0x4, Null]
    //     0xabbcf0: ldr             x4, [x4, #0x308]
    // 0xabbcf4: r0 = showModalBottomSheet()
    //     0xabbcf4: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xabbcf8: r0 = Null
    //     0xabbcf8: mov             x0, NULL
    // 0xabbcfc: LeaveFrame
    //     0xabbcfc: mov             SP, fp
    //     0xabbd00: ldp             fp, lr, [SP], #0x10
    // 0xabbd04: ret
    //     0xabbd04: ret             
    // 0xabbd08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabbd08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabbd0c: b               #0xabbc94
  }
}

// class id: 4198, size: 0x2c, field offset: 0xc
//   const constructor, 
class OffersContentItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7cff8, size: 0x24
    // 0xc7cff8: EnterFrame
    //     0xc7cff8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7cffc: mov             fp, SP
    // 0xc7d000: mov             x0, x1
    // 0xc7d004: r1 = <OffersContentItemView>
    //     0xc7d004: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f68] TypeArguments: <OffersContentItemView>
    //     0xc7d008: ldr             x1, [x1, #0xf68]
    // 0xc7d00c: r0 = _OffersContentItemViewState()
    //     0xc7d00c: bl              #0xc7d01c  ; Allocate_OffersContentItemViewStateStub -> _OffersContentItemViewState (size=0x14)
    // 0xc7d010: LeaveFrame
    //     0xc7d010: mov             SP, fp
    //     0xc7d014: ldp             fp, lr, [SP], #0x10
    // 0xc7d018: ret
    //     0xc7d018: ret             
  }
}
