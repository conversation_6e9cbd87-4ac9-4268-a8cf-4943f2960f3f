// lib: , url: package:customer_app/app/presentation/views/glass/profile/widgets/policy_widget.dart

// class id: 1049454, size: 0x8
class :: {
}

// class id: 4552, size: 0x14, field offset: 0x14
//   const constructor, 
class PolicyWidget extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14fc3e0, size: 0x64
    // 0x14fc3e0: EnterFrame
    //     0x14fc3e0: stp             fp, lr, [SP, #-0x10]!
    //     0x14fc3e4: mov             fp, SP
    // 0x14fc3e8: AllocStack(0x18)
    //     0x14fc3e8: sub             SP, SP, #0x18
    // 0x14fc3ec: SetupParameters(PolicyWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14fc3ec: stur            x1, [fp, #-8]
    //     0x14fc3f0: stur            x2, [fp, #-0x10]
    // 0x14fc3f4: r1 = 2
    //     0x14fc3f4: movz            x1, #0x2
    // 0x14fc3f8: r0 = AllocateContext()
    //     0x14fc3f8: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fc3fc: mov             x1, x0
    // 0x14fc400: ldur            x0, [fp, #-8]
    // 0x14fc404: stur            x1, [fp, #-0x18]
    // 0x14fc408: StoreField: r1->field_f = r0
    //     0x14fc408: stur            w0, [x1, #0xf]
    // 0x14fc40c: ldur            x0, [fp, #-0x10]
    // 0x14fc410: StoreField: r1->field_13 = r0
    //     0x14fc410: stur            w0, [x1, #0x13]
    // 0x14fc414: r0 = Obx()
    //     0x14fc414: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fc418: ldur            x2, [fp, #-0x18]
    // 0x14fc41c: r1 = Function '<anonymous closure>':.
    //     0x14fc41c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea00] AnonymousClosure: (0x147899c), in [package:customer_app/app/presentation/views/line/profile/widgets/policy_widget.dart] PolicyWidget::body (0x15094e8)
    //     0x14fc420: ldr             x1, [x1, #0xa00]
    // 0x14fc424: stur            x0, [fp, #-8]
    // 0x14fc428: r0 = AllocateClosure()
    //     0x14fc428: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc42c: mov             x1, x0
    // 0x14fc430: ldur            x0, [fp, #-8]
    // 0x14fc434: StoreField: r0->field_b = r1
    //     0x14fc434: stur            w1, [x0, #0xb]
    // 0x14fc438: LeaveFrame
    //     0x14fc438: mov             SP, fp
    //     0x14fc43c: ldp             fp, lr, [SP], #0x10
    // 0x14fc440: ret
    //     0x14fc440: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e5be4, size: 0x130
    // 0x15e5be4: EnterFrame
    //     0x15e5be4: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5be8: mov             fp, SP
    // 0x15e5bec: AllocStack(0x18)
    //     0x15e5bec: sub             SP, SP, #0x18
    // 0x15e5bf0: SetupParameters(PolicyWidget this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x15e5bf0: mov             x0, x1
    //     0x15e5bf4: mov             x1, x2
    // 0x15e5bf8: CheckStackOverflow
    //     0x15e5bf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5bfc: cmp             SP, x16
    //     0x15e5c00: b.ls            #0x15e5d0c
    // 0x15e5c04: r0 = of()
    //     0x15e5c04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e5c08: LoadField: r1 = r0->field_5b
    //     0x15e5c08: ldur            w1, [x0, #0x5b]
    // 0x15e5c0c: DecompressPointer r1
    //     0x15e5c0c: add             x1, x1, HEAP, lsl #32
    // 0x15e5c10: stur            x1, [fp, #-8]
    // 0x15e5c14: r0 = ColorFilter()
    //     0x15e5c14: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e5c18: mov             x1, x0
    // 0x15e5c1c: ldur            x0, [fp, #-8]
    // 0x15e5c20: stur            x1, [fp, #-0x10]
    // 0x15e5c24: StoreField: r1->field_7 = r0
    //     0x15e5c24: stur            w0, [x1, #7]
    // 0x15e5c28: r0 = Instance_BlendMode
    //     0x15e5c28: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e5c2c: ldr             x0, [x0, #0xb30]
    // 0x15e5c30: StoreField: r1->field_b = r0
    //     0x15e5c30: stur            w0, [x1, #0xb]
    // 0x15e5c34: r0 = 1
    //     0x15e5c34: movz            x0, #0x1
    // 0x15e5c38: StoreField: r1->field_13 = r0
    //     0x15e5c38: stur            x0, [x1, #0x13]
    // 0x15e5c3c: r0 = SvgPicture()
    //     0x15e5c3c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e5c40: stur            x0, [fp, #-8]
    // 0x15e5c44: ldur            x16, [fp, #-0x10]
    // 0x15e5c48: str             x16, [SP]
    // 0x15e5c4c: mov             x1, x0
    // 0x15e5c50: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e5c50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e5c54: ldr             x2, [x2, #0xa40]
    // 0x15e5c58: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e5c58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e5c5c: ldr             x4, [x4, #0xa38]
    // 0x15e5c60: r0 = SvgPicture.asset()
    //     0x15e5c60: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e5c64: r0 = Align()
    //     0x15e5c64: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e5c68: mov             x1, x0
    // 0x15e5c6c: r0 = Instance_Alignment
    //     0x15e5c6c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e5c70: ldr             x0, [x0, #0xb10]
    // 0x15e5c74: stur            x1, [fp, #-0x10]
    // 0x15e5c78: StoreField: r1->field_f = r0
    //     0x15e5c78: stur            w0, [x1, #0xf]
    // 0x15e5c7c: r0 = 1.000000
    //     0x15e5c7c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e5c80: StoreField: r1->field_13 = r0
    //     0x15e5c80: stur            w0, [x1, #0x13]
    // 0x15e5c84: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e5c84: stur            w0, [x1, #0x17]
    // 0x15e5c88: ldur            x0, [fp, #-8]
    // 0x15e5c8c: StoreField: r1->field_b = r0
    //     0x15e5c8c: stur            w0, [x1, #0xb]
    // 0x15e5c90: r0 = InkWell()
    //     0x15e5c90: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e5c94: mov             x3, x0
    // 0x15e5c98: ldur            x0, [fp, #-0x10]
    // 0x15e5c9c: stur            x3, [fp, #-8]
    // 0x15e5ca0: StoreField: r3->field_b = r0
    //     0x15e5ca0: stur            w0, [x3, #0xb]
    // 0x15e5ca4: r1 = Function '<anonymous closure>':.
    //     0x15e5ca4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea08] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e5ca8: ldr             x1, [x1, #0xa08]
    // 0x15e5cac: r2 = Null
    //     0x15e5cac: mov             x2, NULL
    // 0x15e5cb0: r0 = AllocateClosure()
    //     0x15e5cb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5cb4: ldur            x2, [fp, #-8]
    // 0x15e5cb8: StoreField: r2->field_f = r0
    //     0x15e5cb8: stur            w0, [x2, #0xf]
    // 0x15e5cbc: r0 = true
    //     0x15e5cbc: add             x0, NULL, #0x20  ; true
    // 0x15e5cc0: StoreField: r2->field_43 = r0
    //     0x15e5cc0: stur            w0, [x2, #0x43]
    // 0x15e5cc4: r1 = Instance_BoxShape
    //     0x15e5cc4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e5cc8: ldr             x1, [x1, #0x80]
    // 0x15e5ccc: StoreField: r2->field_47 = r1
    //     0x15e5ccc: stur            w1, [x2, #0x47]
    // 0x15e5cd0: StoreField: r2->field_6f = r0
    //     0x15e5cd0: stur            w0, [x2, #0x6f]
    // 0x15e5cd4: r1 = false
    //     0x15e5cd4: add             x1, NULL, #0x30  ; false
    // 0x15e5cd8: StoreField: r2->field_73 = r1
    //     0x15e5cd8: stur            w1, [x2, #0x73]
    // 0x15e5cdc: StoreField: r2->field_83 = r0
    //     0x15e5cdc: stur            w0, [x2, #0x83]
    // 0x15e5ce0: StoreField: r2->field_7b = r1
    //     0x15e5ce0: stur            w1, [x2, #0x7b]
    // 0x15e5ce4: r0 = AppBar()
    //     0x15e5ce4: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e5ce8: mov             x1, x0
    // 0x15e5cec: ldur            x2, [fp, #-8]
    // 0x15e5cf0: stur            x0, [fp, #-8]
    // 0x15e5cf4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15e5cf4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15e5cf8: r0 = AppBar()
    //     0x15e5cf8: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e5cfc: ldur            x0, [fp, #-8]
    // 0x15e5d00: LeaveFrame
    //     0x15e5d00: mov             SP, fp
    //     0x15e5d04: ldp             fp, lr, [SP], #0x10
    // 0x15e5d08: ret
    //     0x15e5d08: ret             
    // 0x15e5d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e5d0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e5d10: b               #0x15e5c04
  }
}
