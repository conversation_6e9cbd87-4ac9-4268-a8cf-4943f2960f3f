// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/collection_poster_carousel.dart

// class id: 1049400, size: 0x8
class :: {
}

// class id: 3339, size: 0x20, field offset: 0x14
class _CollectionPosterCarouselState extends State<dynamic> {

  late PageController _pageLineController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb5eaf4, size: 0x6bc
    // 0xb5eaf4: EnterFrame
    //     0xb5eaf4: stp             fp, lr, [SP, #-0x10]!
    //     0xb5eaf8: mov             fp, SP
    // 0xb5eafc: AllocStack(0x68)
    //     0xb5eafc: sub             SP, SP, #0x68
    // 0xb5eb00: SetupParameters(_CollectionPosterCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb5eb00: mov             x0, x1
    //     0xb5eb04: stur            x1, [fp, #-8]
    //     0xb5eb08: mov             x1, x2
    //     0xb5eb0c: stur            x2, [fp, #-0x10]
    // 0xb5eb10: CheckStackOverflow
    //     0xb5eb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5eb14: cmp             SP, x16
    //     0xb5eb18: b.ls            #0xb5f180
    // 0xb5eb1c: r1 = 1
    //     0xb5eb1c: movz            x1, #0x1
    // 0xb5eb20: r0 = AllocateContext()
    //     0xb5eb20: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5eb24: mov             x3, x0
    // 0xb5eb28: ldur            x0, [fp, #-8]
    // 0xb5eb2c: stur            x3, [fp, #-0x20]
    // 0xb5eb30: StoreField: r3->field_f = r0
    //     0xb5eb30: stur            w0, [x3, #0xf]
    // 0xb5eb34: LoadField: r1 = r0->field_b
    //     0xb5eb34: ldur            w1, [x0, #0xb]
    // 0xb5eb38: DecompressPointer r1
    //     0xb5eb38: add             x1, x1, HEAP, lsl #32
    // 0xb5eb3c: cmp             w1, NULL
    // 0xb5eb40: b.eq            #0xb5f188
    // 0xb5eb44: LoadField: r2 = r1->field_13
    //     0xb5eb44: ldur            w2, [x1, #0x13]
    // 0xb5eb48: DecompressPointer r2
    //     0xb5eb48: add             x2, x2, HEAP, lsl #32
    // 0xb5eb4c: LoadField: r1 = r2->field_7
    //     0xb5eb4c: ldur            w1, [x2, #7]
    // 0xb5eb50: DecompressPointer r1
    //     0xb5eb50: add             x1, x1, HEAP, lsl #32
    // 0xb5eb54: cmp             w1, NULL
    // 0xb5eb58: b.ne            #0xb5eb64
    // 0xb5eb5c: r1 = Instance_TitleAlignment
    //     0xb5eb5c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb5eb60: ldr             x1, [x1, #0x518]
    // 0xb5eb64: r16 = Instance_TitleAlignment
    //     0xb5eb64: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb5eb68: ldr             x16, [x16, #0x520]
    // 0xb5eb6c: cmp             w1, w16
    // 0xb5eb70: b.ne            #0xb5eb80
    // 0xb5eb74: r4 = Instance_CrossAxisAlignment
    //     0xb5eb74: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb5eb78: ldr             x4, [x4, #0xc68]
    // 0xb5eb7c: b               #0xb5eba4
    // 0xb5eb80: r16 = Instance_TitleAlignment
    //     0xb5eb80: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb5eb84: ldr             x16, [x16, #0x518]
    // 0xb5eb88: cmp             w1, w16
    // 0xb5eb8c: b.ne            #0xb5eb9c
    // 0xb5eb90: r4 = Instance_CrossAxisAlignment
    //     0xb5eb90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5eb94: ldr             x4, [x4, #0x890]
    // 0xb5eb98: b               #0xb5eba4
    // 0xb5eb9c: r4 = Instance_CrossAxisAlignment
    //     0xb5eb9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5eba0: ldr             x4, [x4, #0xa18]
    // 0xb5eba4: stur            x4, [fp, #-0x18]
    // 0xb5eba8: r1 = <Widget>
    //     0xb5eba8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5ebac: r2 = 0
    //     0xb5ebac: movz            x2, #0
    // 0xb5ebb0: r0 = _GrowableList()
    //     0xb5ebb0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb5ebb4: mov             x2, x0
    // 0xb5ebb8: ldur            x0, [fp, #-8]
    // 0xb5ebbc: stur            x2, [fp, #-0x28]
    // 0xb5ebc0: LoadField: r1 = r0->field_b
    //     0xb5ebc0: ldur            w1, [x0, #0xb]
    // 0xb5ebc4: DecompressPointer r1
    //     0xb5ebc4: add             x1, x1, HEAP, lsl #32
    // 0xb5ebc8: cmp             w1, NULL
    // 0xb5ebcc: b.eq            #0xb5f18c
    // 0xb5ebd0: LoadField: r3 = r1->field_f
    //     0xb5ebd0: ldur            w3, [x1, #0xf]
    // 0xb5ebd4: DecompressPointer r3
    //     0xb5ebd4: add             x3, x3, HEAP, lsl #32
    // 0xb5ebd8: LoadField: r1 = r3->field_7
    //     0xb5ebd8: ldur            w1, [x3, #7]
    // 0xb5ebdc: cbz             w1, #0xb5ee10
    // 0xb5ebe0: mov             x1, x3
    // 0xb5ebe4: r0 = capitalizeFirstWord()
    //     0xb5ebe4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb5ebe8: mov             x2, x0
    // 0xb5ebec: ldur            x0, [fp, #-8]
    // 0xb5ebf0: stur            x2, [fp, #-0x38]
    // 0xb5ebf4: LoadField: r1 = r0->field_b
    //     0xb5ebf4: ldur            w1, [x0, #0xb]
    // 0xb5ebf8: DecompressPointer r1
    //     0xb5ebf8: add             x1, x1, HEAP, lsl #32
    // 0xb5ebfc: cmp             w1, NULL
    // 0xb5ec00: b.eq            #0xb5f190
    // 0xb5ec04: LoadField: r3 = r1->field_13
    //     0xb5ec04: ldur            w3, [x1, #0x13]
    // 0xb5ec08: DecompressPointer r3
    //     0xb5ec08: add             x3, x3, HEAP, lsl #32
    // 0xb5ec0c: LoadField: r1 = r3->field_7
    //     0xb5ec0c: ldur            w1, [x3, #7]
    // 0xb5ec10: DecompressPointer r1
    //     0xb5ec10: add             x1, x1, HEAP, lsl #32
    // 0xb5ec14: cmp             w1, NULL
    // 0xb5ec18: b.ne            #0xb5ec24
    // 0xb5ec1c: r1 = Instance_TitleAlignment
    //     0xb5ec1c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb5ec20: ldr             x1, [x1, #0x518]
    // 0xb5ec24: r16 = Instance_TitleAlignment
    //     0xb5ec24: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb5ec28: ldr             x16, [x16, #0x520]
    // 0xb5ec2c: cmp             w1, w16
    // 0xb5ec30: b.ne            #0xb5ec3c
    // 0xb5ec34: r4 = Instance_TextAlign
    //     0xb5ec34: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb5ec38: b               #0xb5ec58
    // 0xb5ec3c: r16 = Instance_TitleAlignment
    //     0xb5ec3c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb5ec40: ldr             x16, [x16, #0x518]
    // 0xb5ec44: cmp             w1, w16
    // 0xb5ec48: b.ne            #0xb5ec54
    // 0xb5ec4c: r4 = Instance_TextAlign
    //     0xb5ec4c: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb5ec50: b               #0xb5ec58
    // 0xb5ec54: r4 = Instance_TextAlign
    //     0xb5ec54: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb5ec58: ldur            x3, [fp, #-0x28]
    // 0xb5ec5c: ldur            x1, [fp, #-0x10]
    // 0xb5ec60: stur            x4, [fp, #-0x30]
    // 0xb5ec64: r0 = of()
    //     0xb5ec64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5ec68: LoadField: r1 = r0->field_87
    //     0xb5ec68: ldur            w1, [x0, #0x87]
    // 0xb5ec6c: DecompressPointer r1
    //     0xb5ec6c: add             x1, x1, HEAP, lsl #32
    // 0xb5ec70: LoadField: r0 = r1->field_7
    //     0xb5ec70: ldur            w0, [x1, #7]
    // 0xb5ec74: DecompressPointer r0
    //     0xb5ec74: add             x0, x0, HEAP, lsl #32
    // 0xb5ec78: stur            x0, [fp, #-0x40]
    // 0xb5ec7c: r1 = Instance_Color
    //     0xb5ec7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5ec80: d0 = 0.700000
    //     0xb5ec80: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5ec84: ldr             d0, [x17, #0xf48]
    // 0xb5ec88: r0 = withOpacity()
    //     0xb5ec88: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb5ec8c: r16 = 32.000000
    //     0xb5ec8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb5ec90: ldr             x16, [x16, #0x848]
    // 0xb5ec94: stp             x0, x16, [SP]
    // 0xb5ec98: ldur            x1, [fp, #-0x40]
    // 0xb5ec9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5ec9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5eca0: ldr             x4, [x4, #0xaa0]
    // 0xb5eca4: r0 = copyWith()
    //     0xb5eca4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5eca8: stur            x0, [fp, #-0x40]
    // 0xb5ecac: r0 = Text()
    //     0xb5ecac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5ecb0: mov             x1, x0
    // 0xb5ecb4: ldur            x0, [fp, #-0x38]
    // 0xb5ecb8: stur            x1, [fp, #-0x48]
    // 0xb5ecbc: StoreField: r1->field_b = r0
    //     0xb5ecbc: stur            w0, [x1, #0xb]
    // 0xb5ecc0: ldur            x0, [fp, #-0x40]
    // 0xb5ecc4: StoreField: r1->field_13 = r0
    //     0xb5ecc4: stur            w0, [x1, #0x13]
    // 0xb5ecc8: ldur            x0, [fp, #-0x30]
    // 0xb5eccc: StoreField: r1->field_1b = r0
    //     0xb5eccc: stur            w0, [x1, #0x1b]
    // 0xb5ecd0: r0 = Padding()
    //     0xb5ecd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5ecd4: mov             x3, x0
    // 0xb5ecd8: r0 = Instance_EdgeInsets
    //     0xb5ecd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb5ecdc: ldr             x0, [x0, #0x668]
    // 0xb5ece0: stur            x3, [fp, #-0x30]
    // 0xb5ece4: StoreField: r3->field_f = r0
    //     0xb5ece4: stur            w0, [x3, #0xf]
    // 0xb5ece8: ldur            x0, [fp, #-0x48]
    // 0xb5ecec: StoreField: r3->field_b = r0
    //     0xb5ecec: stur            w0, [x3, #0xb]
    // 0xb5ecf0: r1 = Null
    //     0xb5ecf0: mov             x1, NULL
    // 0xb5ecf4: r2 = 4
    //     0xb5ecf4: movz            x2, #0x4
    // 0xb5ecf8: r0 = AllocateArray()
    //     0xb5ecf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5ecfc: mov             x2, x0
    // 0xb5ed00: ldur            x0, [fp, #-0x30]
    // 0xb5ed04: stur            x2, [fp, #-0x38]
    // 0xb5ed08: StoreField: r2->field_f = r0
    //     0xb5ed08: stur            w0, [x2, #0xf]
    // 0xb5ed0c: r16 = Instance_SizedBox
    //     0xb5ed0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb5ed10: ldr             x16, [x16, #0x9f0]
    // 0xb5ed14: StoreField: r2->field_13 = r16
    //     0xb5ed14: stur            w16, [x2, #0x13]
    // 0xb5ed18: r1 = <Widget>
    //     0xb5ed18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5ed1c: r0 = AllocateGrowableArray()
    //     0xb5ed1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5ed20: mov             x1, x0
    // 0xb5ed24: ldur            x0, [fp, #-0x38]
    // 0xb5ed28: stur            x1, [fp, #-0x30]
    // 0xb5ed2c: StoreField: r1->field_f = r0
    //     0xb5ed2c: stur            w0, [x1, #0xf]
    // 0xb5ed30: r0 = 4
    //     0xb5ed30: movz            x0, #0x4
    // 0xb5ed34: StoreField: r1->field_b = r0
    //     0xb5ed34: stur            w0, [x1, #0xb]
    // 0xb5ed38: r0 = Column()
    //     0xb5ed38: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5ed3c: mov             x2, x0
    // 0xb5ed40: r0 = Instance_Axis
    //     0xb5ed40: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5ed44: stur            x2, [fp, #-0x38]
    // 0xb5ed48: StoreField: r2->field_f = r0
    //     0xb5ed48: stur            w0, [x2, #0xf]
    // 0xb5ed4c: r3 = Instance_MainAxisAlignment
    //     0xb5ed4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5ed50: ldr             x3, [x3, #0xa08]
    // 0xb5ed54: StoreField: r2->field_13 = r3
    //     0xb5ed54: stur            w3, [x2, #0x13]
    // 0xb5ed58: r4 = Instance_MainAxisSize
    //     0xb5ed58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5ed5c: ldr             x4, [x4, #0xa10]
    // 0xb5ed60: ArrayStore: r2[0] = r4  ; List_4
    //     0xb5ed60: stur            w4, [x2, #0x17]
    // 0xb5ed64: r5 = Instance_CrossAxisAlignment
    //     0xb5ed64: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5ed68: ldr             x5, [x5, #0xa18]
    // 0xb5ed6c: StoreField: r2->field_1b = r5
    //     0xb5ed6c: stur            w5, [x2, #0x1b]
    // 0xb5ed70: r6 = Instance_VerticalDirection
    //     0xb5ed70: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5ed74: ldr             x6, [x6, #0xa20]
    // 0xb5ed78: StoreField: r2->field_23 = r6
    //     0xb5ed78: stur            w6, [x2, #0x23]
    // 0xb5ed7c: r7 = Instance_Clip
    //     0xb5ed7c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5ed80: ldr             x7, [x7, #0x38]
    // 0xb5ed84: StoreField: r2->field_2b = r7
    //     0xb5ed84: stur            w7, [x2, #0x2b]
    // 0xb5ed88: StoreField: r2->field_2f = rZR
    //     0xb5ed88: stur            xzr, [x2, #0x2f]
    // 0xb5ed8c: ldur            x1, [fp, #-0x30]
    // 0xb5ed90: StoreField: r2->field_b = r1
    //     0xb5ed90: stur            w1, [x2, #0xb]
    // 0xb5ed94: ldur            x8, [fp, #-0x28]
    // 0xb5ed98: LoadField: r1 = r8->field_b
    //     0xb5ed98: ldur            w1, [x8, #0xb]
    // 0xb5ed9c: LoadField: r9 = r8->field_f
    //     0xb5ed9c: ldur            w9, [x8, #0xf]
    // 0xb5eda0: DecompressPointer r9
    //     0xb5eda0: add             x9, x9, HEAP, lsl #32
    // 0xb5eda4: LoadField: r10 = r9->field_b
    //     0xb5eda4: ldur            w10, [x9, #0xb]
    // 0xb5eda8: r9 = LoadInt32Instr(r1)
    //     0xb5eda8: sbfx            x9, x1, #1, #0x1f
    // 0xb5edac: stur            x9, [fp, #-0x50]
    // 0xb5edb0: r1 = LoadInt32Instr(r10)
    //     0xb5edb0: sbfx            x1, x10, #1, #0x1f
    // 0xb5edb4: cmp             x9, x1
    // 0xb5edb8: b.ne            #0xb5edc4
    // 0xb5edbc: mov             x1, x8
    // 0xb5edc0: r0 = _growToNextCapacity()
    //     0xb5edc0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5edc4: ldur            x3, [fp, #-0x28]
    // 0xb5edc8: ldur            x2, [fp, #-0x50]
    // 0xb5edcc: add             x0, x2, #1
    // 0xb5edd0: lsl             x1, x0, #1
    // 0xb5edd4: StoreField: r3->field_b = r1
    //     0xb5edd4: stur            w1, [x3, #0xb]
    // 0xb5edd8: LoadField: r1 = r3->field_f
    //     0xb5edd8: ldur            w1, [x3, #0xf]
    // 0xb5eddc: DecompressPointer r1
    //     0xb5eddc: add             x1, x1, HEAP, lsl #32
    // 0xb5ede0: ldur            x0, [fp, #-0x38]
    // 0xb5ede4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb5ede4: add             x25, x1, x2, lsl #2
    //     0xb5ede8: add             x25, x25, #0xf
    //     0xb5edec: str             w0, [x25]
    //     0xb5edf0: tbz             w0, #0, #0xb5ee0c
    //     0xb5edf4: ldurb           w16, [x1, #-1]
    //     0xb5edf8: ldurb           w17, [x0, #-1]
    //     0xb5edfc: and             x16, x17, x16, lsr #2
    //     0xb5ee00: tst             x16, HEAP, lsr #32
    //     0xb5ee04: b.eq            #0xb5ee0c
    //     0xb5ee08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5ee0c: b               #0xb5ee14
    // 0xb5ee10: mov             x3, x2
    // 0xb5ee14: ldur            x0, [fp, #-8]
    // 0xb5ee18: LoadField: r1 = r0->field_b
    //     0xb5ee18: ldur            w1, [x0, #0xb]
    // 0xb5ee1c: DecompressPointer r1
    //     0xb5ee1c: add             x1, x1, HEAP, lsl #32
    // 0xb5ee20: cmp             w1, NULL
    // 0xb5ee24: b.eq            #0xb5f194
    // 0xb5ee28: LoadField: r2 = r1->field_b
    //     0xb5ee28: ldur            w2, [x1, #0xb]
    // 0xb5ee2c: DecompressPointer r2
    //     0xb5ee2c: add             x2, x2, HEAP, lsl #32
    // 0xb5ee30: cmp             w2, NULL
    // 0xb5ee34: b.eq            #0xb5f198
    // 0xb5ee38: LoadField: r4 = r2->field_b
    //     0xb5ee38: ldur            w4, [x2, #0xb]
    // 0xb5ee3c: stur            x4, [fp, #-0x38]
    // 0xb5ee40: LoadField: r5 = r0->field_13
    //     0xb5ee40: ldur            w5, [x0, #0x13]
    // 0xb5ee44: DecompressPointer r5
    //     0xb5ee44: add             x5, x5, HEAP, lsl #32
    // 0xb5ee48: r16 = Sentinel
    //     0xb5ee48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5ee4c: cmp             w5, w16
    // 0xb5ee50: b.eq            #0xb5f19c
    // 0xb5ee54: ldur            x2, [fp, #-0x20]
    // 0xb5ee58: stur            x5, [fp, #-0x30]
    // 0xb5ee5c: r1 = Function '<anonymous closure>':.
    //     0xb5ee5c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55e60] AnonymousClosure: (0xb5fe4c), in [package:customer_app/app/presentation/views/glass/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::build (0xb5eaf4)
    //     0xb5ee60: ldr             x1, [x1, #0xe60]
    // 0xb5ee64: r0 = AllocateClosure()
    //     0xb5ee64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ee68: ldur            x2, [fp, #-0x20]
    // 0xb5ee6c: r1 = Function '<anonymous closure>':.
    //     0xb5ee6c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55e68] AnonymousClosure: (0xb5f1d0), in [package:customer_app/app/presentation/views/glass/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::build (0xb5eaf4)
    //     0xb5ee70: ldr             x1, [x1, #0xe68]
    // 0xb5ee74: stur            x0, [fp, #-0x20]
    // 0xb5ee78: r0 = AllocateClosure()
    //     0xb5ee78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ee7c: stur            x0, [fp, #-0x40]
    // 0xb5ee80: r0 = PageView()
    //     0xb5ee80: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb5ee84: stur            x0, [fp, #-0x48]
    // 0xb5ee88: r16 = Instance_BouncingScrollPhysics
    //     0xb5ee88: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb5ee8c: ldr             x16, [x16, #0x890]
    // 0xb5ee90: ldur            lr, [fp, #-0x30]
    // 0xb5ee94: stp             lr, x16, [SP]
    // 0xb5ee98: mov             x1, x0
    // 0xb5ee9c: ldur            x2, [fp, #-0x40]
    // 0xb5eea0: ldur            x3, [fp, #-0x38]
    // 0xb5eea4: ldur            x5, [fp, #-0x20]
    // 0xb5eea8: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb5eea8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb5eeac: ldr             x4, [x4, #0xe40]
    // 0xb5eeb0: r0 = PageView.builder()
    //     0xb5eeb0: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb5eeb4: r0 = AspectRatio()
    //     0xb5eeb4: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb5eeb8: d0 = 0.833333
    //     0xb5eeb8: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4dd30] IMM: double(0.8333333333333334) from 0x3feaaaaaaaaaaaab
    //     0xb5eebc: ldr             d0, [x17, #0xd30]
    // 0xb5eec0: stur            x0, [fp, #-0x20]
    // 0xb5eec4: StoreField: r0->field_f = d0
    //     0xb5eec4: stur            d0, [x0, #0xf]
    // 0xb5eec8: ldur            x1, [fp, #-0x48]
    // 0xb5eecc: StoreField: r0->field_b = r1
    //     0xb5eecc: stur            w1, [x0, #0xb]
    // 0xb5eed0: ldur            x2, [fp, #-0x28]
    // 0xb5eed4: LoadField: r1 = r2->field_b
    //     0xb5eed4: ldur            w1, [x2, #0xb]
    // 0xb5eed8: LoadField: r3 = r2->field_f
    //     0xb5eed8: ldur            w3, [x2, #0xf]
    // 0xb5eedc: DecompressPointer r3
    //     0xb5eedc: add             x3, x3, HEAP, lsl #32
    // 0xb5eee0: LoadField: r4 = r3->field_b
    //     0xb5eee0: ldur            w4, [x3, #0xb]
    // 0xb5eee4: r3 = LoadInt32Instr(r1)
    //     0xb5eee4: sbfx            x3, x1, #1, #0x1f
    // 0xb5eee8: stur            x3, [fp, #-0x50]
    // 0xb5eeec: r1 = LoadInt32Instr(r4)
    //     0xb5eeec: sbfx            x1, x4, #1, #0x1f
    // 0xb5eef0: cmp             x3, x1
    // 0xb5eef4: b.ne            #0xb5ef00
    // 0xb5eef8: mov             x1, x2
    // 0xb5eefc: r0 = _growToNextCapacity()
    //     0xb5eefc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5ef00: ldur            x4, [fp, #-8]
    // 0xb5ef04: ldur            x2, [fp, #-0x28]
    // 0xb5ef08: ldur            x3, [fp, #-0x50]
    // 0xb5ef0c: add             x0, x3, #1
    // 0xb5ef10: lsl             x1, x0, #1
    // 0xb5ef14: StoreField: r2->field_b = r1
    //     0xb5ef14: stur            w1, [x2, #0xb]
    // 0xb5ef18: LoadField: r1 = r2->field_f
    //     0xb5ef18: ldur            w1, [x2, #0xf]
    // 0xb5ef1c: DecompressPointer r1
    //     0xb5ef1c: add             x1, x1, HEAP, lsl #32
    // 0xb5ef20: ldur            x0, [fp, #-0x20]
    // 0xb5ef24: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5ef24: add             x25, x1, x3, lsl #2
    //     0xb5ef28: add             x25, x25, #0xf
    //     0xb5ef2c: str             w0, [x25]
    //     0xb5ef30: tbz             w0, #0, #0xb5ef4c
    //     0xb5ef34: ldurb           w16, [x1, #-1]
    //     0xb5ef38: ldurb           w17, [x0, #-1]
    //     0xb5ef3c: and             x16, x17, x16, lsr #2
    //     0xb5ef40: tst             x16, HEAP, lsr #32
    //     0xb5ef44: b.eq            #0xb5ef4c
    //     0xb5ef48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5ef4c: LoadField: r0 = r4->field_b
    //     0xb5ef4c: ldur            w0, [x4, #0xb]
    // 0xb5ef50: DecompressPointer r0
    //     0xb5ef50: add             x0, x0, HEAP, lsl #32
    // 0xb5ef54: cmp             w0, NULL
    // 0xb5ef58: b.eq            #0xb5f1a8
    // 0xb5ef5c: LoadField: r1 = r0->field_b
    //     0xb5ef5c: ldur            w1, [x0, #0xb]
    // 0xb5ef60: DecompressPointer r1
    //     0xb5ef60: add             x1, x1, HEAP, lsl #32
    // 0xb5ef64: cmp             w1, NULL
    // 0xb5ef68: b.eq            #0xb5f1ac
    // 0xb5ef6c: LoadField: r0 = r1->field_b
    //     0xb5ef6c: ldur            w0, [x1, #0xb]
    // 0xb5ef70: r1 = LoadInt32Instr(r0)
    //     0xb5ef70: sbfx            x1, x0, #1, #0x1f
    // 0xb5ef74: cmp             x1, #1
    // 0xb5ef78: b.le            #0xb5f100
    // 0xb5ef7c: r3 = LoadInt32Instr(r0)
    //     0xb5ef7c: sbfx            x3, x0, #1, #0x1f
    // 0xb5ef80: stur            x3, [fp, #-0x58]
    // 0xb5ef84: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xb5ef84: ldur            x0, [x4, #0x17]
    // 0xb5ef88: ldur            x1, [fp, #-0x10]
    // 0xb5ef8c: stur            x0, [fp, #-0x50]
    // 0xb5ef90: r0 = of()
    //     0xb5ef90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5ef94: LoadField: r1 = r0->field_5b
    //     0xb5ef94: ldur            w1, [x0, #0x5b]
    // 0xb5ef98: DecompressPointer r1
    //     0xb5ef98: add             x1, x1, HEAP, lsl #32
    // 0xb5ef9c: stur            x1, [fp, #-8]
    // 0xb5efa0: r0 = CarouselIndicator()
    //     0xb5efa0: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb5efa4: mov             x3, x0
    // 0xb5efa8: ldur            x0, [fp, #-0x58]
    // 0xb5efac: stur            x3, [fp, #-0x10]
    // 0xb5efb0: StoreField: r3->field_b = r0
    //     0xb5efb0: stur            x0, [x3, #0xb]
    // 0xb5efb4: ldur            x0, [fp, #-0x50]
    // 0xb5efb8: StoreField: r3->field_13 = r0
    //     0xb5efb8: stur            x0, [x3, #0x13]
    // 0xb5efbc: ldur            x0, [fp, #-8]
    // 0xb5efc0: StoreField: r3->field_1b = r0
    //     0xb5efc0: stur            w0, [x3, #0x1b]
    // 0xb5efc4: r0 = Instance_Color
    //     0xb5efc4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb5efc8: ldr             x0, [x0, #0x90]
    // 0xb5efcc: StoreField: r3->field_1f = r0
    //     0xb5efcc: stur            w0, [x3, #0x1f]
    // 0xb5efd0: r1 = Null
    //     0xb5efd0: mov             x1, NULL
    // 0xb5efd4: r2 = 2
    //     0xb5efd4: movz            x2, #0x2
    // 0xb5efd8: r0 = AllocateArray()
    //     0xb5efd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5efdc: mov             x2, x0
    // 0xb5efe0: ldur            x0, [fp, #-0x10]
    // 0xb5efe4: stur            x2, [fp, #-8]
    // 0xb5efe8: StoreField: r2->field_f = r0
    //     0xb5efe8: stur            w0, [x2, #0xf]
    // 0xb5efec: r1 = <Widget>
    //     0xb5efec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5eff0: r0 = AllocateGrowableArray()
    //     0xb5eff0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5eff4: mov             x1, x0
    // 0xb5eff8: ldur            x0, [fp, #-8]
    // 0xb5effc: stur            x1, [fp, #-0x10]
    // 0xb5f000: StoreField: r1->field_f = r0
    //     0xb5f000: stur            w0, [x1, #0xf]
    // 0xb5f004: r0 = 2
    //     0xb5f004: movz            x0, #0x2
    // 0xb5f008: StoreField: r1->field_b = r0
    //     0xb5f008: stur            w0, [x1, #0xb]
    // 0xb5f00c: r0 = Row()
    //     0xb5f00c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5f010: mov             x1, x0
    // 0xb5f014: r0 = Instance_Axis
    //     0xb5f014: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5f018: stur            x1, [fp, #-8]
    // 0xb5f01c: StoreField: r1->field_f = r0
    //     0xb5f01c: stur            w0, [x1, #0xf]
    // 0xb5f020: r0 = Instance_MainAxisAlignment
    //     0xb5f020: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb5f024: ldr             x0, [x0, #0xab0]
    // 0xb5f028: StoreField: r1->field_13 = r0
    //     0xb5f028: stur            w0, [x1, #0x13]
    // 0xb5f02c: r0 = Instance_MainAxisSize
    //     0xb5f02c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5f030: ldr             x0, [x0, #0xa10]
    // 0xb5f034: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5f034: stur            w0, [x1, #0x17]
    // 0xb5f038: r0 = Instance_CrossAxisAlignment
    //     0xb5f038: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5f03c: ldr             x0, [x0, #0xa18]
    // 0xb5f040: StoreField: r1->field_1b = r0
    //     0xb5f040: stur            w0, [x1, #0x1b]
    // 0xb5f044: r0 = Instance_VerticalDirection
    //     0xb5f044: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5f048: ldr             x0, [x0, #0xa20]
    // 0xb5f04c: StoreField: r1->field_23 = r0
    //     0xb5f04c: stur            w0, [x1, #0x23]
    // 0xb5f050: r2 = Instance_Clip
    //     0xb5f050: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5f054: ldr             x2, [x2, #0x38]
    // 0xb5f058: StoreField: r1->field_2b = r2
    //     0xb5f058: stur            w2, [x1, #0x2b]
    // 0xb5f05c: StoreField: r1->field_2f = rZR
    //     0xb5f05c: stur            xzr, [x1, #0x2f]
    // 0xb5f060: ldur            x3, [fp, #-0x10]
    // 0xb5f064: StoreField: r1->field_b = r3
    //     0xb5f064: stur            w3, [x1, #0xb]
    // 0xb5f068: r0 = Padding()
    //     0xb5f068: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5f06c: mov             x2, x0
    // 0xb5f070: r0 = Instance_EdgeInsets
    //     0xb5f070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb5f074: ldr             x0, [x0, #0xa00]
    // 0xb5f078: stur            x2, [fp, #-0x10]
    // 0xb5f07c: StoreField: r2->field_f = r0
    //     0xb5f07c: stur            w0, [x2, #0xf]
    // 0xb5f080: ldur            x0, [fp, #-8]
    // 0xb5f084: StoreField: r2->field_b = r0
    //     0xb5f084: stur            w0, [x2, #0xb]
    // 0xb5f088: ldur            x0, [fp, #-0x28]
    // 0xb5f08c: LoadField: r1 = r0->field_b
    //     0xb5f08c: ldur            w1, [x0, #0xb]
    // 0xb5f090: LoadField: r3 = r0->field_f
    //     0xb5f090: ldur            w3, [x0, #0xf]
    // 0xb5f094: DecompressPointer r3
    //     0xb5f094: add             x3, x3, HEAP, lsl #32
    // 0xb5f098: LoadField: r4 = r3->field_b
    //     0xb5f098: ldur            w4, [x3, #0xb]
    // 0xb5f09c: r3 = LoadInt32Instr(r1)
    //     0xb5f09c: sbfx            x3, x1, #1, #0x1f
    // 0xb5f0a0: stur            x3, [fp, #-0x50]
    // 0xb5f0a4: r1 = LoadInt32Instr(r4)
    //     0xb5f0a4: sbfx            x1, x4, #1, #0x1f
    // 0xb5f0a8: cmp             x3, x1
    // 0xb5f0ac: b.ne            #0xb5f0b8
    // 0xb5f0b0: mov             x1, x0
    // 0xb5f0b4: r0 = _growToNextCapacity()
    //     0xb5f0b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5f0b8: ldur            x2, [fp, #-0x28]
    // 0xb5f0bc: ldur            x3, [fp, #-0x50]
    // 0xb5f0c0: add             x0, x3, #1
    // 0xb5f0c4: lsl             x1, x0, #1
    // 0xb5f0c8: StoreField: r2->field_b = r1
    //     0xb5f0c8: stur            w1, [x2, #0xb]
    // 0xb5f0cc: LoadField: r1 = r2->field_f
    //     0xb5f0cc: ldur            w1, [x2, #0xf]
    // 0xb5f0d0: DecompressPointer r1
    //     0xb5f0d0: add             x1, x1, HEAP, lsl #32
    // 0xb5f0d4: ldur            x0, [fp, #-0x10]
    // 0xb5f0d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5f0d8: add             x25, x1, x3, lsl #2
    //     0xb5f0dc: add             x25, x25, #0xf
    //     0xb5f0e0: str             w0, [x25]
    //     0xb5f0e4: tbz             w0, #0, #0xb5f100
    //     0xb5f0e8: ldurb           w16, [x1, #-1]
    //     0xb5f0ec: ldurb           w17, [x0, #-1]
    //     0xb5f0f0: and             x16, x17, x16, lsr #2
    //     0xb5f0f4: tst             x16, HEAP, lsr #32
    //     0xb5f0f8: b.eq            #0xb5f100
    //     0xb5f0fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5f100: ldur            x0, [fp, #-0x18]
    // 0xb5f104: r0 = Column()
    //     0xb5f104: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5f108: mov             x1, x0
    // 0xb5f10c: r0 = Instance_Axis
    //     0xb5f10c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5f110: stur            x1, [fp, #-8]
    // 0xb5f114: StoreField: r1->field_f = r0
    //     0xb5f114: stur            w0, [x1, #0xf]
    // 0xb5f118: r0 = Instance_MainAxisAlignment
    //     0xb5f118: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5f11c: ldr             x0, [x0, #0xa08]
    // 0xb5f120: StoreField: r1->field_13 = r0
    //     0xb5f120: stur            w0, [x1, #0x13]
    // 0xb5f124: r0 = Instance_MainAxisSize
    //     0xb5f124: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb5f128: ldr             x0, [x0, #0xdd0]
    // 0xb5f12c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5f12c: stur            w0, [x1, #0x17]
    // 0xb5f130: ldur            x0, [fp, #-0x18]
    // 0xb5f134: StoreField: r1->field_1b = r0
    //     0xb5f134: stur            w0, [x1, #0x1b]
    // 0xb5f138: r0 = Instance_VerticalDirection
    //     0xb5f138: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5f13c: ldr             x0, [x0, #0xa20]
    // 0xb5f140: StoreField: r1->field_23 = r0
    //     0xb5f140: stur            w0, [x1, #0x23]
    // 0xb5f144: r0 = Instance_Clip
    //     0xb5f144: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5f148: ldr             x0, [x0, #0x38]
    // 0xb5f14c: StoreField: r1->field_2b = r0
    //     0xb5f14c: stur            w0, [x1, #0x2b]
    // 0xb5f150: StoreField: r1->field_2f = rZR
    //     0xb5f150: stur            xzr, [x1, #0x2f]
    // 0xb5f154: ldur            x0, [fp, #-0x28]
    // 0xb5f158: StoreField: r1->field_b = r0
    //     0xb5f158: stur            w0, [x1, #0xb]
    // 0xb5f15c: r0 = Padding()
    //     0xb5f15c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5f160: r1 = Instance_EdgeInsets
    //     0xb5f160: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb5f164: ldr             x1, [x1, #0x240]
    // 0xb5f168: StoreField: r0->field_f = r1
    //     0xb5f168: stur            w1, [x0, #0xf]
    // 0xb5f16c: ldur            x1, [fp, #-8]
    // 0xb5f170: StoreField: r0->field_b = r1
    //     0xb5f170: stur            w1, [x0, #0xb]
    // 0xb5f174: LeaveFrame
    //     0xb5f174: mov             SP, fp
    //     0xb5f178: ldp             fp, lr, [SP], #0x10
    // 0xb5f17c: ret
    //     0xb5f17c: ret             
    // 0xb5f180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5f180: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5f184: b               #0xb5eb1c
    // 0xb5f188: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f188: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5f18c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f18c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5f190: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f190: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5f194: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f194: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5f198: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f198: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5f19c: r9 = _pageLineController
    //     0xb5f19c: add             x9, PP, #0x55, lsl #12  ; [pp+0x55e70] Field <_CollectionPosterCarouselState@1587259173._pageLineController@1587259173>: late (offset: 0x14)
    //     0xb5f1a0: ldr             x9, [x9, #0xe70]
    // 0xb5f1a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb5f1a4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb5f1a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f1a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5f1ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f1ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb5f1d0, size: 0x90
    // 0xb5f1d0: EnterFrame
    //     0xb5f1d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5f1d4: mov             fp, SP
    // 0xb5f1d8: AllocStack(0x8)
    //     0xb5f1d8: sub             SP, SP, #8
    // 0xb5f1dc: SetupParameters()
    //     0xb5f1dc: ldr             x0, [fp, #0x20]
    //     0xb5f1e0: ldur            w1, [x0, #0x17]
    //     0xb5f1e4: add             x1, x1, HEAP, lsl #32
    // 0xb5f1e8: CheckStackOverflow
    //     0xb5f1e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5f1ec: cmp             SP, x16
    //     0xb5f1f0: b.ls            #0xb5f254
    // 0xb5f1f4: LoadField: r0 = r1->field_f
    //     0xb5f1f4: ldur            w0, [x1, #0xf]
    // 0xb5f1f8: DecompressPointer r0
    //     0xb5f1f8: add             x0, x0, HEAP, lsl #32
    // 0xb5f1fc: LoadField: r1 = r0->field_b
    //     0xb5f1fc: ldur            w1, [x0, #0xb]
    // 0xb5f200: DecompressPointer r1
    //     0xb5f200: add             x1, x1, HEAP, lsl #32
    // 0xb5f204: cmp             w1, NULL
    // 0xb5f208: b.eq            #0xb5f25c
    // 0xb5f20c: LoadField: r2 = r1->field_b
    //     0xb5f20c: ldur            w2, [x1, #0xb]
    // 0xb5f210: DecompressPointer r2
    //     0xb5f210: add             x2, x2, HEAP, lsl #32
    // 0xb5f214: ldr             x1, [fp, #0x10]
    // 0xb5f218: r3 = LoadInt32Instr(r1)
    //     0xb5f218: sbfx            x3, x1, #1, #0x1f
    //     0xb5f21c: tbz             w1, #0, #0xb5f224
    //     0xb5f220: ldur            x3, [x1, #7]
    // 0xb5f224: mov             x1, x0
    // 0xb5f228: r0 = glassThemeSlider()
    //     0xb5f228: bl              #0xb5f260  ; [package:customer_app/app/presentation/views/glass/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::glassThemeSlider
    // 0xb5f22c: stur            x0, [fp, #-8]
    // 0xb5f230: r0 = Padding()
    //     0xb5f230: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5f234: r1 = Instance_EdgeInsets
    //     0xb5f234: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb5f238: ldr             x1, [x1, #0x668]
    // 0xb5f23c: StoreField: r0->field_f = r1
    //     0xb5f23c: stur            w1, [x0, #0xf]
    // 0xb5f240: ldur            x1, [fp, #-8]
    // 0xb5f244: StoreField: r0->field_b = r1
    //     0xb5f244: stur            w1, [x0, #0xb]
    // 0xb5f248: LeaveFrame
    //     0xb5f248: mov             SP, fp
    //     0xb5f24c: ldp             fp, lr, [SP], #0x10
    // 0xb5f250: ret
    //     0xb5f250: ret             
    // 0xb5f254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5f254: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5f258: b               #0xb5f1f4
    // 0xb5f25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5f25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ glassThemeSlider(/* No info */) {
    // ** addr: 0xb5f260, size: 0x898
    // 0xb5f260: EnterFrame
    //     0xb5f260: stp             fp, lr, [SP, #-0x10]!
    //     0xb5f264: mov             fp, SP
    // 0xb5f268: AllocStack(0x58)
    //     0xb5f268: sub             SP, SP, #0x58
    // 0xb5f26c: SetupParameters(_CollectionPosterCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb5f26c: stur            x1, [fp, #-8]
    //     0xb5f270: stur            x2, [fp, #-0x10]
    //     0xb5f274: stur            x3, [fp, #-0x18]
    // 0xb5f278: CheckStackOverflow
    //     0xb5f278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5f27c: cmp             SP, x16
    //     0xb5f280: b.ls            #0xb5fac4
    // 0xb5f284: r1 = 3
    //     0xb5f284: movz            x1, #0x3
    // 0xb5f288: r0 = AllocateContext()
    //     0xb5f288: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5f28c: mov             x3, x0
    // 0xb5f290: ldur            x2, [fp, #-8]
    // 0xb5f294: stur            x3, [fp, #-0x20]
    // 0xb5f298: StoreField: r3->field_f = r2
    //     0xb5f298: stur            w2, [x3, #0xf]
    // 0xb5f29c: ldur            x0, [fp, #-0x10]
    // 0xb5f2a0: StoreField: r3->field_13 = r0
    //     0xb5f2a0: stur            w0, [x3, #0x13]
    // 0xb5f2a4: ldur            x4, [fp, #-0x18]
    // 0xb5f2a8: r0 = BoxInt64Instr(r4)
    //     0xb5f2a8: sbfiz           x0, x4, #1, #0x1f
    //     0xb5f2ac: cmp             x4, x0, asr #1
    //     0xb5f2b0: b.eq            #0xb5f2bc
    //     0xb5f2b4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5f2b8: stur            x4, [x0, #7]
    // 0xb5f2bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb5f2bc: stur            w0, [x3, #0x17]
    // 0xb5f2c0: LoadField: r1 = r2->field_f
    //     0xb5f2c0: ldur            w1, [x2, #0xf]
    // 0xb5f2c4: DecompressPointer r1
    //     0xb5f2c4: add             x1, x1, HEAP, lsl #32
    // 0xb5f2c8: cmp             w1, NULL
    // 0xb5f2cc: b.eq            #0xb5facc
    // 0xb5f2d0: r0 = of()
    //     0xb5f2d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5f2d4: LoadField: r1 = r0->field_5b
    //     0xb5f2d4: ldur            w1, [x0, #0x5b]
    // 0xb5f2d8: DecompressPointer r1
    //     0xb5f2d8: add             x1, x1, HEAP, lsl #32
    // 0xb5f2dc: r0 = LoadClassIdInstr(r1)
    //     0xb5f2dc: ldur            x0, [x1, #-1]
    //     0xb5f2e0: ubfx            x0, x0, #0xc, #0x14
    // 0xb5f2e4: d0 = 0.030000
    //     0xb5f2e4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb5f2e8: ldr             d0, [x17, #0x238]
    // 0xb5f2ec: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb5f2ec: sub             lr, x0, #0xffa
    //     0xb5f2f0: ldr             lr, [x21, lr, lsl #3]
    //     0xb5f2f4: blr             lr
    // 0xb5f2f8: stur            x0, [fp, #-0x10]
    // 0xb5f2fc: r0 = Radius()
    //     0xb5f2fc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb5f300: d0 = 20.000000
    //     0xb5f300: fmov            d0, #20.00000000
    // 0xb5f304: stur            x0, [fp, #-0x28]
    // 0xb5f308: StoreField: r0->field_7 = d0
    //     0xb5f308: stur            d0, [x0, #7]
    // 0xb5f30c: StoreField: r0->field_f = d0
    //     0xb5f30c: stur            d0, [x0, #0xf]
    // 0xb5f310: r0 = BorderRadius()
    //     0xb5f310: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb5f314: mov             x1, x0
    // 0xb5f318: ldur            x0, [fp, #-0x28]
    // 0xb5f31c: stur            x1, [fp, #-0x30]
    // 0xb5f320: StoreField: r1->field_7 = r0
    //     0xb5f320: stur            w0, [x1, #7]
    // 0xb5f324: StoreField: r1->field_b = r0
    //     0xb5f324: stur            w0, [x1, #0xb]
    // 0xb5f328: StoreField: r1->field_f = r0
    //     0xb5f328: stur            w0, [x1, #0xf]
    // 0xb5f32c: StoreField: r1->field_13 = r0
    //     0xb5f32c: stur            w0, [x1, #0x13]
    // 0xb5f330: r0 = BoxDecoration()
    //     0xb5f330: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb5f334: mov             x3, x0
    // 0xb5f338: ldur            x0, [fp, #-0x10]
    // 0xb5f33c: stur            x3, [fp, #-0x28]
    // 0xb5f340: StoreField: r3->field_7 = r0
    //     0xb5f340: stur            w0, [x3, #7]
    // 0xb5f344: ldur            x0, [fp, #-0x30]
    // 0xb5f348: StoreField: r3->field_13 = r0
    //     0xb5f348: stur            w0, [x3, #0x13]
    // 0xb5f34c: r4 = Instance_BoxShape
    //     0xb5f34c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5f350: ldr             x4, [x4, #0x80]
    // 0xb5f354: StoreField: r3->field_23 = r4
    //     0xb5f354: stur            w4, [x3, #0x23]
    // 0xb5f358: ldur            x5, [fp, #-0x20]
    // 0xb5f35c: LoadField: r2 = r5->field_13
    //     0xb5f35c: ldur            w2, [x5, #0x13]
    // 0xb5f360: DecompressPointer r2
    //     0xb5f360: add             x2, x2, HEAP, lsl #32
    // 0xb5f364: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xb5f364: ldur            w0, [x5, #0x17]
    // 0xb5f368: DecompressPointer r0
    //     0xb5f368: add             x0, x0, HEAP, lsl #32
    // 0xb5f36c: cmp             w2, NULL
    // 0xb5f370: b.eq            #0xb5fad0
    // 0xb5f374: LoadField: r1 = r2->field_b
    //     0xb5f374: ldur            w1, [x2, #0xb]
    // 0xb5f378: r6 = LoadInt32Instr(r0)
    //     0xb5f378: sbfx            x6, x0, #1, #0x1f
    //     0xb5f37c: tbz             w0, #0, #0xb5f384
    //     0xb5f380: ldur            x6, [x0, #7]
    // 0xb5f384: r0 = LoadInt32Instr(r1)
    //     0xb5f384: sbfx            x0, x1, #1, #0x1f
    // 0xb5f388: mov             x1, x6
    // 0xb5f38c: cmp             x1, x0
    // 0xb5f390: b.hs            #0xb5fad4
    // 0xb5f394: LoadField: r0 = r2->field_f
    //     0xb5f394: ldur            w0, [x2, #0xf]
    // 0xb5f398: DecompressPointer r0
    //     0xb5f398: add             x0, x0, HEAP, lsl #32
    // 0xb5f39c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb5f39c: add             x16, x0, x6, lsl #2
    //     0xb5f3a0: ldur            w1, [x16, #0xf]
    // 0xb5f3a4: DecompressPointer r1
    //     0xb5f3a4: add             x1, x1, HEAP, lsl #32
    // 0xb5f3a8: LoadField: r0 = r1->field_13
    //     0xb5f3a8: ldur            w0, [x1, #0x13]
    // 0xb5f3ac: DecompressPointer r0
    //     0xb5f3ac: add             x0, x0, HEAP, lsl #32
    // 0xb5f3b0: stur            x0, [fp, #-0x10]
    // 0xb5f3b4: cmp             w0, NULL
    // 0xb5f3b8: b.eq            #0xb5fad8
    // 0xb5f3bc: r1 = Function '<anonymous closure>':.
    //     0xb5f3bc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55e78] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5f3c0: ldr             x1, [x1, #0xe78]
    // 0xb5f3c4: r2 = Null
    //     0xb5f3c4: mov             x2, NULL
    // 0xb5f3c8: r0 = AllocateClosure()
    //     0xb5f3c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5f3cc: r1 = Function '<anonymous closure>':.
    //     0xb5f3cc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55e80] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5f3d0: ldr             x1, [x1, #0xe80]
    // 0xb5f3d4: r2 = Null
    //     0xb5f3d4: mov             x2, NULL
    // 0xb5f3d8: stur            x0, [fp, #-0x30]
    // 0xb5f3dc: r0 = AllocateClosure()
    //     0xb5f3dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5f3e0: stur            x0, [fp, #-0x38]
    // 0xb5f3e4: r0 = CachedNetworkImage()
    //     0xb5f3e4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb5f3e8: stur            x0, [fp, #-0x40]
    // 0xb5f3ec: ldur            x16, [fp, #-0x30]
    // 0xb5f3f0: ldur            lr, [fp, #-0x38]
    // 0xb5f3f4: stp             lr, x16, [SP, #8]
    // 0xb5f3f8: r16 = Instance_BoxFit
    //     0xb5f3f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb5f3fc: ldr             x16, [x16, #0x118]
    // 0xb5f400: str             x16, [SP]
    // 0xb5f404: mov             x1, x0
    // 0xb5f408: ldur            x2, [fp, #-0x10]
    // 0xb5f40c: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb5f40c: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb5f410: ldr             x4, [x4, #0x790]
    // 0xb5f414: r0 = CachedNetworkImage()
    //     0xb5f414: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb5f418: r0 = ClipRRect()
    //     0xb5f418: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb5f41c: mov             x1, x0
    // 0xb5f420: r0 = Instance_BorderRadius
    //     0xb5f420: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e18] Obj!BorderRadius@d5a1e1
    //     0xb5f424: ldr             x0, [x0, #0xe18]
    // 0xb5f428: stur            x1, [fp, #-0x10]
    // 0xb5f42c: StoreField: r1->field_f = r0
    //     0xb5f42c: stur            w0, [x1, #0xf]
    // 0xb5f430: r0 = Instance_Clip
    //     0xb5f430: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb5f434: ldr             x0, [x0, #0x138]
    // 0xb5f438: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5f438: stur            w0, [x1, #0x17]
    // 0xb5f43c: ldur            x0, [fp, #-0x40]
    // 0xb5f440: StoreField: r1->field_b = r0
    //     0xb5f440: stur            w0, [x1, #0xb]
    // 0xb5f444: r0 = AspectRatio()
    //     0xb5f444: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb5f448: mov             x3, x0
    // 0xb5f44c: d0 = 1.000000
    //     0xb5f44c: fmov            d0, #1.00000000
    // 0xb5f450: stur            x3, [fp, #-0x30]
    // 0xb5f454: StoreField: r3->field_f = d0
    //     0xb5f454: stur            d0, [x3, #0xf]
    // 0xb5f458: ldur            x0, [fp, #-0x10]
    // 0xb5f45c: StoreField: r3->field_b = r0
    //     0xb5f45c: stur            w0, [x3, #0xb]
    // 0xb5f460: ldur            x4, [fp, #-0x20]
    // 0xb5f464: LoadField: r2 = r4->field_13
    //     0xb5f464: ldur            w2, [x4, #0x13]
    // 0xb5f468: DecompressPointer r2
    //     0xb5f468: add             x2, x2, HEAP, lsl #32
    // 0xb5f46c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb5f46c: ldur            w0, [x4, #0x17]
    // 0xb5f470: DecompressPointer r0
    //     0xb5f470: add             x0, x0, HEAP, lsl #32
    // 0xb5f474: cmp             w2, NULL
    // 0xb5f478: b.eq            #0xb5fadc
    // 0xb5f47c: LoadField: r1 = r2->field_b
    //     0xb5f47c: ldur            w1, [x2, #0xb]
    // 0xb5f480: r5 = LoadInt32Instr(r0)
    //     0xb5f480: sbfx            x5, x0, #1, #0x1f
    //     0xb5f484: tbz             w0, #0, #0xb5f48c
    //     0xb5f488: ldur            x5, [x0, #7]
    // 0xb5f48c: r0 = LoadInt32Instr(r1)
    //     0xb5f48c: sbfx            x0, x1, #1, #0x1f
    // 0xb5f490: mov             x1, x5
    // 0xb5f494: cmp             x1, x0
    // 0xb5f498: b.hs            #0xb5fae0
    // 0xb5f49c: LoadField: r0 = r2->field_f
    //     0xb5f49c: ldur            w0, [x2, #0xf]
    // 0xb5f4a0: DecompressPointer r0
    //     0xb5f4a0: add             x0, x0, HEAP, lsl #32
    // 0xb5f4a4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5f4a4: add             x16, x0, x5, lsl #2
    //     0xb5f4a8: ldur            w1, [x16, #0xf]
    // 0xb5f4ac: DecompressPointer r1
    //     0xb5f4ac: add             x1, x1, HEAP, lsl #32
    // 0xb5f4b0: LoadField: r5 = r1->field_7
    //     0xb5f4b0: ldur            w5, [x1, #7]
    // 0xb5f4b4: DecompressPointer r5
    //     0xb5f4b4: add             x5, x5, HEAP, lsl #32
    // 0xb5f4b8: mov             x0, x5
    // 0xb5f4bc: stur            x5, [fp, #-0x10]
    // 0xb5f4c0: r2 = Null
    //     0xb5f4c0: mov             x2, NULL
    // 0xb5f4c4: r1 = Null
    //     0xb5f4c4: mov             x1, NULL
    // 0xb5f4c8: r4 = LoadClassIdInstr(r0)
    //     0xb5f4c8: ldur            x4, [x0, #-1]
    //     0xb5f4cc: ubfx            x4, x4, #0xc, #0x14
    // 0xb5f4d0: sub             x4, x4, #0x5e
    // 0xb5f4d4: cmp             x4, #1
    // 0xb5f4d8: b.ls            #0xb5f4ec
    // 0xb5f4dc: r8 = String
    //     0xb5f4dc: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb5f4e0: r3 = Null
    //     0xb5f4e0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55e88] Null
    //     0xb5f4e4: ldr             x3, [x3, #0xe88]
    // 0xb5f4e8: r0 = String()
    //     0xb5f4e8: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb5f4ec: ldur            x0, [fp, #-8]
    // 0xb5f4f0: LoadField: r1 = r0->field_f
    //     0xb5f4f0: ldur            w1, [x0, #0xf]
    // 0xb5f4f4: DecompressPointer r1
    //     0xb5f4f4: add             x1, x1, HEAP, lsl #32
    // 0xb5f4f8: cmp             w1, NULL
    // 0xb5f4fc: b.eq            #0xb5fae4
    // 0xb5f500: r0 = of()
    //     0xb5f500: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5f504: LoadField: r1 = r0->field_87
    //     0xb5f504: ldur            w1, [x0, #0x87]
    // 0xb5f508: DecompressPointer r1
    //     0xb5f508: add             x1, x1, HEAP, lsl #32
    // 0xb5f50c: LoadField: r0 = r1->field_7
    //     0xb5f50c: ldur            w0, [x1, #7]
    // 0xb5f510: DecompressPointer r0
    //     0xb5f510: add             x0, x0, HEAP, lsl #32
    // 0xb5f514: r16 = Instance_Color
    //     0xb5f514: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5f518: r30 = 14.000000
    //     0xb5f518: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5f51c: ldr             lr, [lr, #0x1d8]
    // 0xb5f520: stp             lr, x16, [SP]
    // 0xb5f524: mov             x1, x0
    // 0xb5f528: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb5f528: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5f52c: ldr             x4, [x4, #0x9b8]
    // 0xb5f530: r0 = copyWith()
    //     0xb5f530: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5f534: stur            x0, [fp, #-0x38]
    // 0xb5f538: r0 = Text()
    //     0xb5f538: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5f53c: mov             x1, x0
    // 0xb5f540: ldur            x0, [fp, #-0x10]
    // 0xb5f544: stur            x1, [fp, #-0x40]
    // 0xb5f548: StoreField: r1->field_b = r0
    //     0xb5f548: stur            w0, [x1, #0xb]
    // 0xb5f54c: ldur            x0, [fp, #-0x38]
    // 0xb5f550: StoreField: r1->field_13 = r0
    //     0xb5f550: stur            w0, [x1, #0x13]
    // 0xb5f554: r0 = Padding()
    //     0xb5f554: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5f558: mov             x3, x0
    // 0xb5f55c: r0 = Instance_EdgeInsets
    //     0xb5f55c: add             x0, PP, #0x55, lsl #12  ; [pp+0x55800] Obj!EdgeInsets@d59331
    //     0xb5f560: ldr             x0, [x0, #0x800]
    // 0xb5f564: stur            x3, [fp, #-0x10]
    // 0xb5f568: StoreField: r3->field_f = r0
    //     0xb5f568: stur            w0, [x3, #0xf]
    // 0xb5f56c: ldur            x0, [fp, #-0x40]
    // 0xb5f570: StoreField: r3->field_b = r0
    //     0xb5f570: stur            w0, [x3, #0xb]
    // 0xb5f574: r1 = Null
    //     0xb5f574: mov             x1, NULL
    // 0xb5f578: r2 = 4
    //     0xb5f578: movz            x2, #0x4
    // 0xb5f57c: r0 = AllocateArray()
    //     0xb5f57c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5f580: mov             x2, x0
    // 0xb5f584: ldur            x0, [fp, #-0x30]
    // 0xb5f588: stur            x2, [fp, #-0x38]
    // 0xb5f58c: StoreField: r2->field_f = r0
    //     0xb5f58c: stur            w0, [x2, #0xf]
    // 0xb5f590: ldur            x0, [fp, #-0x10]
    // 0xb5f594: StoreField: r2->field_13 = r0
    //     0xb5f594: stur            w0, [x2, #0x13]
    // 0xb5f598: r1 = <Widget>
    //     0xb5f598: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5f59c: r0 = AllocateGrowableArray()
    //     0xb5f59c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5f5a0: mov             x3, x0
    // 0xb5f5a4: ldur            x0, [fp, #-0x38]
    // 0xb5f5a8: stur            x3, [fp, #-0x30]
    // 0xb5f5ac: StoreField: r3->field_f = r0
    //     0xb5f5ac: stur            w0, [x3, #0xf]
    // 0xb5f5b0: r0 = 4
    //     0xb5f5b0: movz            x0, #0x4
    // 0xb5f5b4: StoreField: r3->field_b = r0
    //     0xb5f5b4: stur            w0, [x3, #0xb]
    // 0xb5f5b8: ldur            x4, [fp, #-0x20]
    // 0xb5f5bc: LoadField: r2 = r4->field_13
    //     0xb5f5bc: ldur            w2, [x4, #0x13]
    // 0xb5f5c0: DecompressPointer r2
    //     0xb5f5c0: add             x2, x2, HEAP, lsl #32
    // 0xb5f5c4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb5f5c4: ldur            w0, [x4, #0x17]
    // 0xb5f5c8: DecompressPointer r0
    //     0xb5f5c8: add             x0, x0, HEAP, lsl #32
    // 0xb5f5cc: cmp             w2, NULL
    // 0xb5f5d0: b.eq            #0xb5fae8
    // 0xb5f5d4: LoadField: r1 = r2->field_b
    //     0xb5f5d4: ldur            w1, [x2, #0xb]
    // 0xb5f5d8: r5 = LoadInt32Instr(r0)
    //     0xb5f5d8: sbfx            x5, x0, #1, #0x1f
    //     0xb5f5dc: tbz             w0, #0, #0xb5f5e4
    //     0xb5f5e0: ldur            x5, [x0, #7]
    // 0xb5f5e4: r0 = LoadInt32Instr(r1)
    //     0xb5f5e4: sbfx            x0, x1, #1, #0x1f
    // 0xb5f5e8: mov             x1, x5
    // 0xb5f5ec: cmp             x1, x0
    // 0xb5f5f0: b.hs            #0xb5faec
    // 0xb5f5f4: LoadField: r0 = r2->field_f
    //     0xb5f5f4: ldur            w0, [x2, #0xf]
    // 0xb5f5f8: DecompressPointer r0
    //     0xb5f5f8: add             x0, x0, HEAP, lsl #32
    // 0xb5f5fc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5f5fc: add             x16, x0, x5, lsl #2
    //     0xb5f600: ldur            w1, [x16, #0xf]
    // 0xb5f604: DecompressPointer r1
    //     0xb5f604: add             x1, x1, HEAP, lsl #32
    // 0xb5f608: LoadField: r0 = r1->field_f
    //     0xb5f608: ldur            w0, [x1, #0xf]
    // 0xb5f60c: DecompressPointer r0
    //     0xb5f60c: add             x0, x0, HEAP, lsl #32
    // 0xb5f610: cmp             w0, NULL
    // 0xb5f614: b.eq            #0xb5faf0
    // 0xb5f618: LoadField: r2 = r0->field_7
    //     0xb5f618: ldur            w2, [x0, #7]
    // 0xb5f61c: cbz             w2, #0xb5f888
    // 0xb5f620: ldur            x5, [fp, #-8]
    // 0xb5f624: LoadField: r6 = r1->field_f
    //     0xb5f624: ldur            w6, [x1, #0xf]
    // 0xb5f628: DecompressPointer r6
    //     0xb5f628: add             x6, x6, HEAP, lsl #32
    // 0xb5f62c: mov             x0, x6
    // 0xb5f630: stur            x6, [fp, #-0x10]
    // 0xb5f634: r2 = Null
    //     0xb5f634: mov             x2, NULL
    // 0xb5f638: r1 = Null
    //     0xb5f638: mov             x1, NULL
    // 0xb5f63c: r4 = LoadClassIdInstr(r0)
    //     0xb5f63c: ldur            x4, [x0, #-1]
    //     0xb5f640: ubfx            x4, x4, #0xc, #0x14
    // 0xb5f644: sub             x4, x4, #0x5e
    // 0xb5f648: cmp             x4, #1
    // 0xb5f64c: b.ls            #0xb5f660
    // 0xb5f650: r8 = String
    //     0xb5f650: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb5f654: r3 = Null
    //     0xb5f654: add             x3, PP, #0x55, lsl #12  ; [pp+0x55e98] Null
    //     0xb5f658: ldr             x3, [x3, #0xe98]
    // 0xb5f65c: r0 = String()
    //     0xb5f65c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb5f660: ldur            x0, [fp, #-8]
    // 0xb5f664: LoadField: r1 = r0->field_f
    //     0xb5f664: ldur            w1, [x0, #0xf]
    // 0xb5f668: DecompressPointer r1
    //     0xb5f668: add             x1, x1, HEAP, lsl #32
    // 0xb5f66c: cmp             w1, NULL
    // 0xb5f670: b.eq            #0xb5faf4
    // 0xb5f674: r0 = of()
    //     0xb5f674: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5f678: LoadField: r1 = r0->field_87
    //     0xb5f678: ldur            w1, [x0, #0x87]
    // 0xb5f67c: DecompressPointer r1
    //     0xb5f67c: add             x1, x1, HEAP, lsl #32
    // 0xb5f680: LoadField: r0 = r1->field_7
    //     0xb5f680: ldur            w0, [x1, #7]
    // 0xb5f684: DecompressPointer r0
    //     0xb5f684: add             x0, x0, HEAP, lsl #32
    // 0xb5f688: stur            x0, [fp, #-8]
    // 0xb5f68c: r1 = Instance_Color
    //     0xb5f68c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5f690: d0 = 0.700000
    //     0xb5f690: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5f694: ldr             d0, [x17, #0xf48]
    // 0xb5f698: r0 = withOpacity()
    //     0xb5f698: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb5f69c: r16 = 14.000000
    //     0xb5f69c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5f6a0: ldr             x16, [x16, #0x1d8]
    // 0xb5f6a4: stp             x16, x0, [SP, #8]
    // 0xb5f6a8: r16 = Instance_TextDecoration
    //     0xb5f6a8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb5f6ac: ldr             x16, [x16, #0x10]
    // 0xb5f6b0: str             x16, [SP]
    // 0xb5f6b4: ldur            x1, [fp, #-8]
    // 0xb5f6b8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xb5f6b8: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xb5f6bc: ldr             x4, [x4, #0x7c8]
    // 0xb5f6c0: r0 = copyWith()
    //     0xb5f6c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5f6c4: stur            x0, [fp, #-8]
    // 0xb5f6c8: r0 = Text()
    //     0xb5f6c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5f6cc: mov             x1, x0
    // 0xb5f6d0: ldur            x0, [fp, #-0x10]
    // 0xb5f6d4: stur            x1, [fp, #-0x38]
    // 0xb5f6d8: StoreField: r1->field_b = r0
    //     0xb5f6d8: stur            w0, [x1, #0xb]
    // 0xb5f6dc: ldur            x0, [fp, #-8]
    // 0xb5f6e0: StoreField: r1->field_13 = r0
    //     0xb5f6e0: stur            w0, [x1, #0x13]
    // 0xb5f6e4: r0 = InkWell()
    //     0xb5f6e4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb5f6e8: mov             x3, x0
    // 0xb5f6ec: ldur            x0, [fp, #-0x38]
    // 0xb5f6f0: stur            x3, [fp, #-8]
    // 0xb5f6f4: StoreField: r3->field_b = r0
    //     0xb5f6f4: stur            w0, [x3, #0xb]
    // 0xb5f6f8: ldur            x2, [fp, #-0x20]
    // 0xb5f6fc: r1 = Function '<anonymous closure>':.
    //     0xb5f6fc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ea8] AnonymousClosure: (0xb5fc0c), in [package:customer_app/app/presentation/views/glass/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::glassThemeSlider (0xb5f260)
    //     0xb5f700: ldr             x1, [x1, #0xea8]
    // 0xb5f704: r0 = AllocateClosure()
    //     0xb5f704: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5f708: mov             x1, x0
    // 0xb5f70c: ldur            x0, [fp, #-8]
    // 0xb5f710: StoreField: r0->field_f = r1
    //     0xb5f710: stur            w1, [x0, #0xf]
    // 0xb5f714: r3 = true
    //     0xb5f714: add             x3, NULL, #0x20  ; true
    // 0xb5f718: StoreField: r0->field_43 = r3
    //     0xb5f718: stur            w3, [x0, #0x43]
    // 0xb5f71c: r4 = Instance_BoxShape
    //     0xb5f71c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5f720: ldr             x4, [x4, #0x80]
    // 0xb5f724: StoreField: r0->field_47 = r4
    //     0xb5f724: stur            w4, [x0, #0x47]
    // 0xb5f728: StoreField: r0->field_6f = r3
    //     0xb5f728: stur            w3, [x0, #0x6f]
    // 0xb5f72c: r5 = false
    //     0xb5f72c: add             x5, NULL, #0x30  ; false
    // 0xb5f730: StoreField: r0->field_73 = r5
    //     0xb5f730: stur            w5, [x0, #0x73]
    // 0xb5f734: StoreField: r0->field_83 = r3
    //     0xb5f734: stur            w3, [x0, #0x83]
    // 0xb5f738: StoreField: r0->field_7b = r5
    //     0xb5f738: stur            w5, [x0, #0x7b]
    // 0xb5f73c: r1 = Null
    //     0xb5f73c: mov             x1, NULL
    // 0xb5f740: r2 = 6
    //     0xb5f740: movz            x2, #0x6
    // 0xb5f744: r0 = AllocateArray()
    //     0xb5f744: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5f748: mov             x2, x0
    // 0xb5f74c: ldur            x0, [fp, #-8]
    // 0xb5f750: stur            x2, [fp, #-0x10]
    // 0xb5f754: StoreField: r2->field_f = r0
    //     0xb5f754: stur            w0, [x2, #0xf]
    // 0xb5f758: r16 = Instance_SizedBox
    //     0xb5f758: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb5f75c: ldr             x16, [x16, #0xb20]
    // 0xb5f760: StoreField: r2->field_13 = r16
    //     0xb5f760: stur            w16, [x2, #0x13]
    // 0xb5f764: r16 = Instance_Icon
    //     0xb5f764: add             x16, PP, #0x55, lsl #12  ; [pp+0x55820] Obj!Icon@d66731
    //     0xb5f768: ldr             x16, [x16, #0x820]
    // 0xb5f76c: ArrayStore: r2[0] = r16  ; List_4
    //     0xb5f76c: stur            w16, [x2, #0x17]
    // 0xb5f770: r1 = <Widget>
    //     0xb5f770: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5f774: r0 = AllocateGrowableArray()
    //     0xb5f774: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5f778: mov             x1, x0
    // 0xb5f77c: ldur            x0, [fp, #-0x10]
    // 0xb5f780: stur            x1, [fp, #-8]
    // 0xb5f784: StoreField: r1->field_f = r0
    //     0xb5f784: stur            w0, [x1, #0xf]
    // 0xb5f788: r0 = 6
    //     0xb5f788: movz            x0, #0x6
    // 0xb5f78c: StoreField: r1->field_b = r0
    //     0xb5f78c: stur            w0, [x1, #0xb]
    // 0xb5f790: r0 = Row()
    //     0xb5f790: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5f794: mov             x1, x0
    // 0xb5f798: r0 = Instance_Axis
    //     0xb5f798: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5f79c: stur            x1, [fp, #-0x10]
    // 0xb5f7a0: StoreField: r1->field_f = r0
    //     0xb5f7a0: stur            w0, [x1, #0xf]
    // 0xb5f7a4: r2 = Instance_MainAxisAlignment
    //     0xb5f7a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5f7a8: ldr             x2, [x2, #0xa08]
    // 0xb5f7ac: StoreField: r1->field_13 = r2
    //     0xb5f7ac: stur            w2, [x1, #0x13]
    // 0xb5f7b0: r3 = Instance_MainAxisSize
    //     0xb5f7b0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5f7b4: ldr             x3, [x3, #0xa10]
    // 0xb5f7b8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb5f7b8: stur            w3, [x1, #0x17]
    // 0xb5f7bc: r4 = Instance_CrossAxisAlignment
    //     0xb5f7bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5f7c0: ldr             x4, [x4, #0xa18]
    // 0xb5f7c4: StoreField: r1->field_1b = r4
    //     0xb5f7c4: stur            w4, [x1, #0x1b]
    // 0xb5f7c8: r5 = Instance_VerticalDirection
    //     0xb5f7c8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5f7cc: ldr             x5, [x5, #0xa20]
    // 0xb5f7d0: StoreField: r1->field_23 = r5
    //     0xb5f7d0: stur            w5, [x1, #0x23]
    // 0xb5f7d4: r6 = Instance_Clip
    //     0xb5f7d4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5f7d8: ldr             x6, [x6, #0x38]
    // 0xb5f7dc: StoreField: r1->field_2b = r6
    //     0xb5f7dc: stur            w6, [x1, #0x2b]
    // 0xb5f7e0: StoreField: r1->field_2f = rZR
    //     0xb5f7e0: stur            xzr, [x1, #0x2f]
    // 0xb5f7e4: ldur            x7, [fp, #-8]
    // 0xb5f7e8: StoreField: r1->field_b = r7
    //     0xb5f7e8: stur            w7, [x1, #0xb]
    // 0xb5f7ec: r0 = Padding()
    //     0xb5f7ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5f7f0: mov             x2, x0
    // 0xb5f7f4: r0 = Instance_EdgeInsets
    //     0xb5f7f4: add             x0, PP, #0x55, lsl #12  ; [pp+0x55828] Obj!EdgeInsets@d59301
    //     0xb5f7f8: ldr             x0, [x0, #0x828]
    // 0xb5f7fc: stur            x2, [fp, #-8]
    // 0xb5f800: StoreField: r2->field_f = r0
    //     0xb5f800: stur            w0, [x2, #0xf]
    // 0xb5f804: ldur            x0, [fp, #-0x10]
    // 0xb5f808: StoreField: r2->field_b = r0
    //     0xb5f808: stur            w0, [x2, #0xb]
    // 0xb5f80c: ldur            x0, [fp, #-0x30]
    // 0xb5f810: LoadField: r1 = r0->field_b
    //     0xb5f810: ldur            w1, [x0, #0xb]
    // 0xb5f814: LoadField: r3 = r0->field_f
    //     0xb5f814: ldur            w3, [x0, #0xf]
    // 0xb5f818: DecompressPointer r3
    //     0xb5f818: add             x3, x3, HEAP, lsl #32
    // 0xb5f81c: LoadField: r4 = r3->field_b
    //     0xb5f81c: ldur            w4, [x3, #0xb]
    // 0xb5f820: r3 = LoadInt32Instr(r1)
    //     0xb5f820: sbfx            x3, x1, #1, #0x1f
    // 0xb5f824: stur            x3, [fp, #-0x18]
    // 0xb5f828: r1 = LoadInt32Instr(r4)
    //     0xb5f828: sbfx            x1, x4, #1, #0x1f
    // 0xb5f82c: cmp             x3, x1
    // 0xb5f830: b.ne            #0xb5f83c
    // 0xb5f834: mov             x1, x0
    // 0xb5f838: r0 = _growToNextCapacity()
    //     0xb5f838: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5f83c: ldur            x2, [fp, #-0x30]
    // 0xb5f840: ldur            x3, [fp, #-0x18]
    // 0xb5f844: add             x0, x3, #1
    // 0xb5f848: lsl             x1, x0, #1
    // 0xb5f84c: StoreField: r2->field_b = r1
    //     0xb5f84c: stur            w1, [x2, #0xb]
    // 0xb5f850: LoadField: r1 = r2->field_f
    //     0xb5f850: ldur            w1, [x2, #0xf]
    // 0xb5f854: DecompressPointer r1
    //     0xb5f854: add             x1, x1, HEAP, lsl #32
    // 0xb5f858: ldur            x0, [fp, #-8]
    // 0xb5f85c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5f85c: add             x25, x1, x3, lsl #2
    //     0xb5f860: add             x25, x25, #0xf
    //     0xb5f864: str             w0, [x25]
    //     0xb5f868: tbz             w0, #0, #0xb5f884
    //     0xb5f86c: ldurb           w16, [x1, #-1]
    //     0xb5f870: ldurb           w17, [x0, #-1]
    //     0xb5f874: and             x16, x17, x16, lsr #2
    //     0xb5f878: tst             x16, HEAP, lsr #32
    //     0xb5f87c: b.eq            #0xb5f884
    //     0xb5f880: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5f884: b               #0xb5f918
    // 0xb5f888: mov             x2, x3
    // 0xb5f88c: r0 = Container()
    //     0xb5f88c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5f890: mov             x1, x0
    // 0xb5f894: stur            x0, [fp, #-8]
    // 0xb5f898: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5f898: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5f89c: r0 = Container()
    //     0xb5f89c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5f8a0: ldur            x0, [fp, #-0x30]
    // 0xb5f8a4: LoadField: r1 = r0->field_b
    //     0xb5f8a4: ldur            w1, [x0, #0xb]
    // 0xb5f8a8: LoadField: r2 = r0->field_f
    //     0xb5f8a8: ldur            w2, [x0, #0xf]
    // 0xb5f8ac: DecompressPointer r2
    //     0xb5f8ac: add             x2, x2, HEAP, lsl #32
    // 0xb5f8b0: LoadField: r3 = r2->field_b
    //     0xb5f8b0: ldur            w3, [x2, #0xb]
    // 0xb5f8b4: r2 = LoadInt32Instr(r1)
    //     0xb5f8b4: sbfx            x2, x1, #1, #0x1f
    // 0xb5f8b8: stur            x2, [fp, #-0x18]
    // 0xb5f8bc: r1 = LoadInt32Instr(r3)
    //     0xb5f8bc: sbfx            x1, x3, #1, #0x1f
    // 0xb5f8c0: cmp             x2, x1
    // 0xb5f8c4: b.ne            #0xb5f8d0
    // 0xb5f8c8: mov             x1, x0
    // 0xb5f8cc: r0 = _growToNextCapacity()
    //     0xb5f8cc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5f8d0: ldur            x2, [fp, #-0x30]
    // 0xb5f8d4: ldur            x3, [fp, #-0x18]
    // 0xb5f8d8: add             x0, x3, #1
    // 0xb5f8dc: lsl             x1, x0, #1
    // 0xb5f8e0: StoreField: r2->field_b = r1
    //     0xb5f8e0: stur            w1, [x2, #0xb]
    // 0xb5f8e4: LoadField: r1 = r2->field_f
    //     0xb5f8e4: ldur            w1, [x2, #0xf]
    // 0xb5f8e8: DecompressPointer r1
    //     0xb5f8e8: add             x1, x1, HEAP, lsl #32
    // 0xb5f8ec: ldur            x0, [fp, #-8]
    // 0xb5f8f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5f8f0: add             x25, x1, x3, lsl #2
    //     0xb5f8f4: add             x25, x25, #0xf
    //     0xb5f8f8: str             w0, [x25]
    //     0xb5f8fc: tbz             w0, #0, #0xb5f918
    //     0xb5f900: ldurb           w16, [x1, #-1]
    //     0xb5f904: ldurb           w17, [x0, #-1]
    //     0xb5f908: and             x16, x17, x16, lsr #2
    //     0xb5f90c: tst             x16, HEAP, lsr #32
    //     0xb5f910: b.eq            #0xb5f918
    //     0xb5f914: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5f918: r0 = Column()
    //     0xb5f918: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5f91c: mov             x1, x0
    // 0xb5f920: r0 = Instance_Axis
    //     0xb5f920: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5f924: stur            x1, [fp, #-8]
    // 0xb5f928: StoreField: r1->field_f = r0
    //     0xb5f928: stur            w0, [x1, #0xf]
    // 0xb5f92c: r0 = Instance_MainAxisAlignment
    //     0xb5f92c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5f930: ldr             x0, [x0, #0xa08]
    // 0xb5f934: StoreField: r1->field_13 = r0
    //     0xb5f934: stur            w0, [x1, #0x13]
    // 0xb5f938: r2 = Instance_MainAxisSize
    //     0xb5f938: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5f93c: ldr             x2, [x2, #0xa10]
    // 0xb5f940: ArrayStore: r1[0] = r2  ; List_4
    //     0xb5f940: stur            w2, [x1, #0x17]
    // 0xb5f944: r3 = Instance_CrossAxisAlignment
    //     0xb5f944: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5f948: ldr             x3, [x3, #0x890]
    // 0xb5f94c: StoreField: r1->field_1b = r3
    //     0xb5f94c: stur            w3, [x1, #0x1b]
    // 0xb5f950: r3 = Instance_VerticalDirection
    //     0xb5f950: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5f954: ldr             x3, [x3, #0xa20]
    // 0xb5f958: StoreField: r1->field_23 = r3
    //     0xb5f958: stur            w3, [x1, #0x23]
    // 0xb5f95c: r4 = Instance_Clip
    //     0xb5f95c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5f960: ldr             x4, [x4, #0x38]
    // 0xb5f964: StoreField: r1->field_2b = r4
    //     0xb5f964: stur            w4, [x1, #0x2b]
    // 0xb5f968: StoreField: r1->field_2f = rZR
    //     0xb5f968: stur            xzr, [x1, #0x2f]
    // 0xb5f96c: ldur            x5, [fp, #-0x30]
    // 0xb5f970: StoreField: r1->field_b = r5
    //     0xb5f970: stur            w5, [x1, #0xb]
    // 0xb5f974: r0 = InkWell()
    //     0xb5f974: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb5f978: mov             x3, x0
    // 0xb5f97c: ldur            x0, [fp, #-8]
    // 0xb5f980: stur            x3, [fp, #-0x10]
    // 0xb5f984: StoreField: r3->field_b = r0
    //     0xb5f984: stur            w0, [x3, #0xb]
    // 0xb5f988: ldur            x2, [fp, #-0x20]
    // 0xb5f98c: r1 = Function '<anonymous closure>':.
    //     0xb5f98c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55eb0] AnonymousClosure: (0xb5faf8), in [package:customer_app/app/presentation/views/glass/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::glassThemeSlider (0xb5f260)
    //     0xb5f990: ldr             x1, [x1, #0xeb0]
    // 0xb5f994: r0 = AllocateClosure()
    //     0xb5f994: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5f998: ldur            x2, [fp, #-0x10]
    // 0xb5f99c: StoreField: r2->field_f = r0
    //     0xb5f99c: stur            w0, [x2, #0xf]
    // 0xb5f9a0: r0 = true
    //     0xb5f9a0: add             x0, NULL, #0x20  ; true
    // 0xb5f9a4: StoreField: r2->field_43 = r0
    //     0xb5f9a4: stur            w0, [x2, #0x43]
    // 0xb5f9a8: r1 = Instance_BoxShape
    //     0xb5f9a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5f9ac: ldr             x1, [x1, #0x80]
    // 0xb5f9b0: StoreField: r2->field_47 = r1
    //     0xb5f9b0: stur            w1, [x2, #0x47]
    // 0xb5f9b4: StoreField: r2->field_6f = r0
    //     0xb5f9b4: stur            w0, [x2, #0x6f]
    // 0xb5f9b8: r1 = false
    //     0xb5f9b8: add             x1, NULL, #0x30  ; false
    // 0xb5f9bc: StoreField: r2->field_73 = r1
    //     0xb5f9bc: stur            w1, [x2, #0x73]
    // 0xb5f9c0: StoreField: r2->field_83 = r0
    //     0xb5f9c0: stur            w0, [x2, #0x83]
    // 0xb5f9c4: StoreField: r2->field_7b = r1
    //     0xb5f9c4: stur            w1, [x2, #0x7b]
    // 0xb5f9c8: r0 = AnimatedContainer()
    //     0xb5f9c8: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb5f9cc: stur            x0, [fp, #-8]
    // 0xb5f9d0: r16 = Instance_Cubic
    //     0xb5f9d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb5f9d4: ldr             x16, [x16, #0xaf8]
    // 0xb5f9d8: ldur            lr, [fp, #-0x28]
    // 0xb5f9dc: stp             lr, x16, [SP]
    // 0xb5f9e0: mov             x1, x0
    // 0xb5f9e4: ldur            x2, [fp, #-0x10]
    // 0xb5f9e8: r3 = Instance_Duration
    //     0xb5f9e8: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb5f9ec: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, decoration, 0x4, null]
    //     0xb5f9ec: add             x4, PP, #0x55, lsl #12  ; [pp+0x55830] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "decoration", 0x4, Null]
    //     0xb5f9f0: ldr             x4, [x4, #0x830]
    // 0xb5f9f4: r0 = AnimatedContainer()
    //     0xb5f9f4: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb5f9f8: r1 = <FlexParentData>
    //     0xb5f9f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5f9fc: ldr             x1, [x1, #0xe00]
    // 0xb5fa00: r0 = Expanded()
    //     0xb5fa00: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb5fa04: mov             x3, x0
    // 0xb5fa08: r0 = 1
    //     0xb5fa08: movz            x0, #0x1
    // 0xb5fa0c: stur            x3, [fp, #-0x10]
    // 0xb5fa10: StoreField: r3->field_13 = r0
    //     0xb5fa10: stur            x0, [x3, #0x13]
    // 0xb5fa14: r0 = Instance_FlexFit
    //     0xb5fa14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb5fa18: ldr             x0, [x0, #0xe08]
    // 0xb5fa1c: StoreField: r3->field_1b = r0
    //     0xb5fa1c: stur            w0, [x3, #0x1b]
    // 0xb5fa20: ldur            x0, [fp, #-8]
    // 0xb5fa24: StoreField: r3->field_b = r0
    //     0xb5fa24: stur            w0, [x3, #0xb]
    // 0xb5fa28: r1 = Null
    //     0xb5fa28: mov             x1, NULL
    // 0xb5fa2c: r2 = 2
    //     0xb5fa2c: movz            x2, #0x2
    // 0xb5fa30: r0 = AllocateArray()
    //     0xb5fa30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5fa34: mov             x2, x0
    // 0xb5fa38: ldur            x0, [fp, #-0x10]
    // 0xb5fa3c: stur            x2, [fp, #-8]
    // 0xb5fa40: StoreField: r2->field_f = r0
    //     0xb5fa40: stur            w0, [x2, #0xf]
    // 0xb5fa44: r1 = <Widget>
    //     0xb5fa44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5fa48: r0 = AllocateGrowableArray()
    //     0xb5fa48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5fa4c: mov             x1, x0
    // 0xb5fa50: ldur            x0, [fp, #-8]
    // 0xb5fa54: stur            x1, [fp, #-0x10]
    // 0xb5fa58: StoreField: r1->field_f = r0
    //     0xb5fa58: stur            w0, [x1, #0xf]
    // 0xb5fa5c: r0 = 2
    //     0xb5fa5c: movz            x0, #0x2
    // 0xb5fa60: StoreField: r1->field_b = r0
    //     0xb5fa60: stur            w0, [x1, #0xb]
    // 0xb5fa64: r0 = Row()
    //     0xb5fa64: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5fa68: r1 = Instance_Axis
    //     0xb5fa68: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5fa6c: StoreField: r0->field_f = r1
    //     0xb5fa6c: stur            w1, [x0, #0xf]
    // 0xb5fa70: r1 = Instance_MainAxisAlignment
    //     0xb5fa70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5fa74: ldr             x1, [x1, #0xa08]
    // 0xb5fa78: StoreField: r0->field_13 = r1
    //     0xb5fa78: stur            w1, [x0, #0x13]
    // 0xb5fa7c: r1 = Instance_MainAxisSize
    //     0xb5fa7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5fa80: ldr             x1, [x1, #0xa10]
    // 0xb5fa84: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5fa84: stur            w1, [x0, #0x17]
    // 0xb5fa88: r1 = Instance_CrossAxisAlignment
    //     0xb5fa88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5fa8c: ldr             x1, [x1, #0xa18]
    // 0xb5fa90: StoreField: r0->field_1b = r1
    //     0xb5fa90: stur            w1, [x0, #0x1b]
    // 0xb5fa94: r1 = Instance_VerticalDirection
    //     0xb5fa94: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5fa98: ldr             x1, [x1, #0xa20]
    // 0xb5fa9c: StoreField: r0->field_23 = r1
    //     0xb5fa9c: stur            w1, [x0, #0x23]
    // 0xb5faa0: r1 = Instance_Clip
    //     0xb5faa0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5faa4: ldr             x1, [x1, #0x38]
    // 0xb5faa8: StoreField: r0->field_2b = r1
    //     0xb5faa8: stur            w1, [x0, #0x2b]
    // 0xb5faac: StoreField: r0->field_2f = rZR
    //     0xb5faac: stur            xzr, [x0, #0x2f]
    // 0xb5fab0: ldur            x1, [fp, #-0x10]
    // 0xb5fab4: StoreField: r0->field_b = r1
    //     0xb5fab4: stur            w1, [x0, #0xb]
    // 0xb5fab8: LeaveFrame
    //     0xb5fab8: mov             SP, fp
    //     0xb5fabc: ldp             fp, lr, [SP], #0x10
    // 0xb5fac0: ret
    //     0xb5fac0: ret             
    // 0xb5fac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5fac4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5fac8: b               #0xb5f284
    // 0xb5facc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5facc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5fad0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fad0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5fad4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5fad4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5fad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5fad8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5fadc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fadc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5fae0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5fae0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5fae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5fae4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5fae8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fae8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5faec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5faec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5faf0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5faf0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5faf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5faf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb5faf8, size: 0x114
    // 0xb5faf8: EnterFrame
    //     0xb5faf8: stp             fp, lr, [SP, #-0x10]!
    //     0xb5fafc: mov             fp, SP
    // 0xb5fb00: AllocStack(0x40)
    //     0xb5fb00: sub             SP, SP, #0x40
    // 0xb5fb04: SetupParameters()
    //     0xb5fb04: ldr             x0, [fp, #0x10]
    //     0xb5fb08: ldur            w1, [x0, #0x17]
    //     0xb5fb0c: add             x1, x1, HEAP, lsl #32
    // 0xb5fb10: CheckStackOverflow
    //     0xb5fb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5fb14: cmp             SP, x16
    //     0xb5fb18: b.ls            #0xb5fbf8
    // 0xb5fb1c: LoadField: r0 = r1->field_f
    //     0xb5fb1c: ldur            w0, [x1, #0xf]
    // 0xb5fb20: DecompressPointer r0
    //     0xb5fb20: add             x0, x0, HEAP, lsl #32
    // 0xb5fb24: LoadField: r2 = r0->field_b
    //     0xb5fb24: ldur            w2, [x0, #0xb]
    // 0xb5fb28: DecompressPointer r2
    //     0xb5fb28: add             x2, x2, HEAP, lsl #32
    // 0xb5fb2c: cmp             w2, NULL
    // 0xb5fb30: b.eq            #0xb5fc00
    // 0xb5fb34: LoadField: r3 = r2->field_1b
    //     0xb5fb34: ldur            w3, [x2, #0x1b]
    // 0xb5fb38: DecompressPointer r3
    //     0xb5fb38: add             x3, x3, HEAP, lsl #32
    // 0xb5fb3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb5fb3c: ldur            w4, [x2, #0x17]
    // 0xb5fb40: DecompressPointer r4
    //     0xb5fb40: add             x4, x4, HEAP, lsl #32
    // 0xb5fb44: LoadField: r5 = r2->field_f
    //     0xb5fb44: ldur            w5, [x2, #0xf]
    // 0xb5fb48: DecompressPointer r5
    //     0xb5fb48: add             x5, x5, HEAP, lsl #32
    // 0xb5fb4c: LoadField: r6 = r2->field_23
    //     0xb5fb4c: ldur            w6, [x2, #0x23]
    // 0xb5fb50: DecompressPointer r6
    //     0xb5fb50: add             x6, x6, HEAP, lsl #32
    // 0xb5fb54: LoadField: r7 = r2->field_1f
    //     0xb5fb54: ldur            w7, [x2, #0x1f]
    // 0xb5fb58: DecompressPointer r7
    //     0xb5fb58: add             x7, x7, HEAP, lsl #32
    // 0xb5fb5c: LoadField: r8 = r2->field_2b
    //     0xb5fb5c: ldur            w8, [x2, #0x2b]
    // 0xb5fb60: DecompressPointer r8
    //     0xb5fb60: add             x8, x8, HEAP, lsl #32
    // 0xb5fb64: LoadField: r9 = r1->field_13
    //     0xb5fb64: ldur            w9, [x1, #0x13]
    // 0xb5fb68: DecompressPointer r9
    //     0xb5fb68: add             x9, x9, HEAP, lsl #32
    // 0xb5fb6c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5fb6c: ldur            w0, [x1, #0x17]
    // 0xb5fb70: DecompressPointer r0
    //     0xb5fb70: add             x0, x0, HEAP, lsl #32
    // 0xb5fb74: cmp             w9, NULL
    // 0xb5fb78: b.eq            #0xb5fc04
    // 0xb5fb7c: LoadField: r1 = r9->field_b
    //     0xb5fb7c: ldur            w1, [x9, #0xb]
    // 0xb5fb80: r10 = LoadInt32Instr(r0)
    //     0xb5fb80: sbfx            x10, x0, #1, #0x1f
    //     0xb5fb84: tbz             w0, #0, #0xb5fb8c
    //     0xb5fb88: ldur            x10, [x0, #7]
    // 0xb5fb8c: r0 = LoadInt32Instr(r1)
    //     0xb5fb8c: sbfx            x0, x1, #1, #0x1f
    // 0xb5fb90: mov             x1, x10
    // 0xb5fb94: cmp             x1, x0
    // 0xb5fb98: b.hs            #0xb5fc08
    // 0xb5fb9c: LoadField: r0 = r9->field_f
    //     0xb5fb9c: ldur            w0, [x9, #0xf]
    // 0xb5fba0: DecompressPointer r0
    //     0xb5fba0: add             x0, x0, HEAP, lsl #32
    // 0xb5fba4: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0xb5fba4: add             x16, x0, x10, lsl #2
    //     0xb5fba8: ldur            w1, [x16, #0xf]
    // 0xb5fbac: DecompressPointer r1
    //     0xb5fbac: add             x1, x1, HEAP, lsl #32
    // 0xb5fbb0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5fbb0: ldur            w0, [x1, #0x17]
    // 0xb5fbb4: DecompressPointer r0
    //     0xb5fbb4: add             x0, x0, HEAP, lsl #32
    // 0xb5fbb8: LoadField: r1 = r2->field_27
    //     0xb5fbb8: ldur            w1, [x2, #0x27]
    // 0xb5fbbc: DecompressPointer r1
    //     0xb5fbbc: add             x1, x1, HEAP, lsl #32
    // 0xb5fbc0: stp             x3, x1, [SP, #0x30]
    // 0xb5fbc4: stp             x5, x4, [SP, #0x20]
    // 0xb5fbc8: stp             x7, x6, [SP, #0x10]
    // 0xb5fbcc: stp             x0, x8, [SP]
    // 0xb5fbd0: r4 = 0
    //     0xb5fbd0: movz            x4, #0
    // 0xb5fbd4: ldr             x0, [SP, #0x38]
    // 0xb5fbd8: r16 = UnlinkedCall_0x613b5c
    //     0xb5fbd8: add             x16, PP, #0x55, lsl #12  ; [pp+0x55eb8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5fbdc: add             x16, x16, #0xeb8
    // 0xb5fbe0: ldp             x5, lr, [x16]
    // 0xb5fbe4: blr             lr
    // 0xb5fbe8: r0 = Null
    //     0xb5fbe8: mov             x0, NULL
    // 0xb5fbec: LeaveFrame
    //     0xb5fbec: mov             SP, fp
    //     0xb5fbf0: ldp             fp, lr, [SP], #0x10
    // 0xb5fbf4: ret
    //     0xb5fbf4: ret             
    // 0xb5fbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5fbf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5fbfc: b               #0xb5fb1c
    // 0xb5fc00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5fc00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5fc04: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fc04: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5fc08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5fc08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb5fc0c, size: 0x240
    // 0xb5fc0c: EnterFrame
    //     0xb5fc0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5fc10: mov             fp, SP
    // 0xb5fc14: AllocStack(0x28)
    //     0xb5fc14: sub             SP, SP, #0x28
    // 0xb5fc18: SetupParameters()
    //     0xb5fc18: ldr             x0, [fp, #0x10]
    //     0xb5fc1c: ldur            w2, [x0, #0x17]
    //     0xb5fc20: add             x2, x2, HEAP, lsl #32
    //     0xb5fc24: stur            x2, [fp, #-8]
    // 0xb5fc28: CheckStackOverflow
    //     0xb5fc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5fc2c: cmp             SP, x16
    //     0xb5fc30: b.ls            #0xb5fe2c
    // 0xb5fc34: LoadField: r3 = r2->field_13
    //     0xb5fc34: ldur            w3, [x2, #0x13]
    // 0xb5fc38: DecompressPointer r3
    //     0xb5fc38: add             x3, x3, HEAP, lsl #32
    // 0xb5fc3c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb5fc3c: ldur            w0, [x2, #0x17]
    // 0xb5fc40: DecompressPointer r0
    //     0xb5fc40: add             x0, x0, HEAP, lsl #32
    // 0xb5fc44: cmp             w3, NULL
    // 0xb5fc48: b.eq            #0xb5fe34
    // 0xb5fc4c: LoadField: r1 = r3->field_b
    //     0xb5fc4c: ldur            w1, [x3, #0xb]
    // 0xb5fc50: r4 = LoadInt32Instr(r0)
    //     0xb5fc50: sbfx            x4, x0, #1, #0x1f
    //     0xb5fc54: tbz             w0, #0, #0xb5fc5c
    //     0xb5fc58: ldur            x4, [x0, #7]
    // 0xb5fc5c: r0 = LoadInt32Instr(r1)
    //     0xb5fc5c: sbfx            x0, x1, #1, #0x1f
    // 0xb5fc60: mov             x1, x4
    // 0xb5fc64: cmp             x1, x0
    // 0xb5fc68: b.hs            #0xb5fe38
    // 0xb5fc6c: LoadField: r0 = r3->field_f
    //     0xb5fc6c: ldur            w0, [x3, #0xf]
    // 0xb5fc70: DecompressPointer r0
    //     0xb5fc70: add             x0, x0, HEAP, lsl #32
    // 0xb5fc74: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb5fc74: add             x16, x0, x4, lsl #2
    //     0xb5fc78: ldur            w1, [x16, #0xf]
    // 0xb5fc7c: DecompressPointer r1
    //     0xb5fc7c: add             x1, x1, HEAP, lsl #32
    // 0xb5fc80: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5fc80: ldur            w0, [x1, #0x17]
    // 0xb5fc84: DecompressPointer r0
    //     0xb5fc84: add             x0, x0, HEAP, lsl #32
    // 0xb5fc88: cmp             w0, NULL
    // 0xb5fc8c: b.eq            #0xb5fe3c
    // 0xb5fc90: LoadField: r1 = r0->field_7
    //     0xb5fc90: ldur            w1, [x0, #7]
    // 0xb5fc94: cbz             w1, #0xb5fe1c
    // 0xb5fc98: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb5fc98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb5fc9c: ldr             x0, [x0, #0x1c80]
    //     0xb5fca0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb5fca4: cmp             w0, w16
    //     0xb5fca8: b.ne            #0xb5fcb4
    //     0xb5fcac: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb5fcb0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb5fcb4: r1 = Null
    //     0xb5fcb4: mov             x1, NULL
    // 0xb5fcb8: r2 = 12
    //     0xb5fcb8: movz            x2, #0xc
    // 0xb5fcbc: r0 = AllocateArray()
    //     0xb5fcbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5fcc0: mov             x2, x0
    // 0xb5fcc4: stur            x2, [fp, #-0x10]
    // 0xb5fcc8: r16 = "link"
    //     0xb5fcc8: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0xb5fccc: StoreField: r2->field_f = r16
    //     0xb5fccc: stur            w16, [x2, #0xf]
    // 0xb5fcd0: ldur            x3, [fp, #-8]
    // 0xb5fcd4: LoadField: r4 = r3->field_13
    //     0xb5fcd4: ldur            w4, [x3, #0x13]
    // 0xb5fcd8: DecompressPointer r4
    //     0xb5fcd8: add             x4, x4, HEAP, lsl #32
    // 0xb5fcdc: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb5fcdc: ldur            w0, [x3, #0x17]
    // 0xb5fce0: DecompressPointer r0
    //     0xb5fce0: add             x0, x0, HEAP, lsl #32
    // 0xb5fce4: cmp             w4, NULL
    // 0xb5fce8: b.eq            #0xb5fe40
    // 0xb5fcec: LoadField: r1 = r4->field_b
    //     0xb5fcec: ldur            w1, [x4, #0xb]
    // 0xb5fcf0: r5 = LoadInt32Instr(r0)
    //     0xb5fcf0: sbfx            x5, x0, #1, #0x1f
    //     0xb5fcf4: tbz             w0, #0, #0xb5fcfc
    //     0xb5fcf8: ldur            x5, [x0, #7]
    // 0xb5fcfc: r0 = LoadInt32Instr(r1)
    //     0xb5fcfc: sbfx            x0, x1, #1, #0x1f
    // 0xb5fd00: mov             x1, x5
    // 0xb5fd04: cmp             x1, x0
    // 0xb5fd08: b.hs            #0xb5fe44
    // 0xb5fd0c: LoadField: r0 = r4->field_f
    //     0xb5fd0c: ldur            w0, [x4, #0xf]
    // 0xb5fd10: DecompressPointer r0
    //     0xb5fd10: add             x0, x0, HEAP, lsl #32
    // 0xb5fd14: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5fd14: add             x16, x0, x5, lsl #2
    //     0xb5fd18: ldur            w1, [x16, #0xf]
    // 0xb5fd1c: DecompressPointer r1
    //     0xb5fd1c: add             x1, x1, HEAP, lsl #32
    // 0xb5fd20: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5fd20: ldur            w0, [x1, #0x17]
    // 0xb5fd24: DecompressPointer r0
    //     0xb5fd24: add             x0, x0, HEAP, lsl #32
    // 0xb5fd28: str             x0, [SP]
    // 0xb5fd2c: r0 = _interpolateSingle()
    //     0xb5fd2c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb5fd30: ldur            x1, [fp, #-0x10]
    // 0xb5fd34: ArrayStore: r1[1] = r0  ; List_4
    //     0xb5fd34: add             x25, x1, #0x13
    //     0xb5fd38: str             w0, [x25]
    //     0xb5fd3c: tbz             w0, #0, #0xb5fd58
    //     0xb5fd40: ldurb           w16, [x1, #-1]
    //     0xb5fd44: ldurb           w17, [x0, #-1]
    //     0xb5fd48: and             x16, x17, x16, lsr #2
    //     0xb5fd4c: tst             x16, HEAP, lsr #32
    //     0xb5fd50: b.eq            #0xb5fd58
    //     0xb5fd54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5fd58: ldur            x2, [fp, #-0x10]
    // 0xb5fd5c: r16 = "previousScreenSource"
    //     0xb5fd5c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xb5fd60: ldr             x16, [x16, #0x448]
    // 0xb5fd64: ArrayStore: r2[0] = r16  ; List_4
    //     0xb5fd64: stur            w16, [x2, #0x17]
    // 0xb5fd68: ldur            x0, [fp, #-8]
    // 0xb5fd6c: LoadField: r1 = r0->field_f
    //     0xb5fd6c: ldur            w1, [x0, #0xf]
    // 0xb5fd70: DecompressPointer r1
    //     0xb5fd70: add             x1, x1, HEAP, lsl #32
    // 0xb5fd74: LoadField: r3 = r1->field_b
    //     0xb5fd74: ldur            w3, [x1, #0xb]
    // 0xb5fd78: DecompressPointer r3
    //     0xb5fd78: add             x3, x3, HEAP, lsl #32
    // 0xb5fd7c: cmp             w3, NULL
    // 0xb5fd80: b.eq            #0xb5fe48
    // 0xb5fd84: LoadField: r0 = r3->field_23
    //     0xb5fd84: ldur            w0, [x3, #0x23]
    // 0xb5fd88: DecompressPointer r0
    //     0xb5fd88: add             x0, x0, HEAP, lsl #32
    // 0xb5fd8c: mov             x1, x2
    // 0xb5fd90: ArrayStore: r1[3] = r0  ; List_4
    //     0xb5fd90: add             x25, x1, #0x1b
    //     0xb5fd94: str             w0, [x25]
    //     0xb5fd98: tbz             w0, #0, #0xb5fdb4
    //     0xb5fd9c: ldurb           w16, [x1, #-1]
    //     0xb5fda0: ldurb           w17, [x0, #-1]
    //     0xb5fda4: and             x16, x17, x16, lsr #2
    //     0xb5fda8: tst             x16, HEAP, lsr #32
    //     0xb5fdac: b.eq            #0xb5fdb4
    //     0xb5fdb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5fdb4: r16 = "screenSource"
    //     0xb5fdb4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xb5fdb8: ldr             x16, [x16, #0x450]
    // 0xb5fdbc: StoreField: r2->field_1f = r16
    //     0xb5fdbc: stur            w16, [x2, #0x1f]
    // 0xb5fdc0: LoadField: r0 = r3->field_1f
    //     0xb5fdc0: ldur            w0, [x3, #0x1f]
    // 0xb5fdc4: DecompressPointer r0
    //     0xb5fdc4: add             x0, x0, HEAP, lsl #32
    // 0xb5fdc8: mov             x1, x2
    // 0xb5fdcc: ArrayStore: r1[5] = r0  ; List_4
    //     0xb5fdcc: add             x25, x1, #0x23
    //     0xb5fdd0: str             w0, [x25]
    //     0xb5fdd4: tbz             w0, #0, #0xb5fdf0
    //     0xb5fdd8: ldurb           w16, [x1, #-1]
    //     0xb5fddc: ldurb           w17, [x0, #-1]
    //     0xb5fde0: and             x16, x17, x16, lsr #2
    //     0xb5fde4: tst             x16, HEAP, lsr #32
    //     0xb5fde8: b.eq            #0xb5fdf0
    //     0xb5fdec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5fdf0: r16 = <String, String?>
    //     0xb5fdf0: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xb5fdf4: ldr             x16, [x16, #0x3c8]
    // 0xb5fdf8: stp             x2, x16, [SP]
    // 0xb5fdfc: r0 = Map._fromLiteral()
    //     0xb5fdfc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb5fe00: r16 = "/collection"
    //     0xb5fe00: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0xb5fe04: ldr             x16, [x16, #0x458]
    // 0xb5fe08: stp             x16, NULL, [SP, #8]
    // 0xb5fe0c: str             x0, [SP]
    // 0xb5fe10: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb5fe10: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb5fe14: ldr             x4, [x4, #0x438]
    // 0xb5fe18: r0 = GetNavigation.toNamed()
    //     0xb5fe18: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb5fe1c: r0 = Null
    //     0xb5fe1c: mov             x0, NULL
    // 0xb5fe20: LeaveFrame
    //     0xb5fe20: mov             SP, fp
    //     0xb5fe24: ldp             fp, lr, [SP], #0x10
    // 0xb5fe28: ret
    //     0xb5fe28: ret             
    // 0xb5fe2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5fe2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5fe30: b               #0xb5fc34
    // 0xb5fe34: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fe34: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5fe38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5fe38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5fe3c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fe3c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5fe40: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5fe40: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb5fe44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5fe44: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5fe48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5fe48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb5fe4c, size: 0x84
    // 0xb5fe4c: EnterFrame
    //     0xb5fe4c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5fe50: mov             fp, SP
    // 0xb5fe54: AllocStack(0x10)
    //     0xb5fe54: sub             SP, SP, #0x10
    // 0xb5fe58: SetupParameters()
    //     0xb5fe58: ldr             x0, [fp, #0x18]
    //     0xb5fe5c: ldur            w1, [x0, #0x17]
    //     0xb5fe60: add             x1, x1, HEAP, lsl #32
    //     0xb5fe64: stur            x1, [fp, #-8]
    // 0xb5fe68: CheckStackOverflow
    //     0xb5fe68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5fe6c: cmp             SP, x16
    //     0xb5fe70: b.ls            #0xb5fec8
    // 0xb5fe74: r1 = 1
    //     0xb5fe74: movz            x1, #0x1
    // 0xb5fe78: r0 = AllocateContext()
    //     0xb5fe78: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5fe7c: mov             x1, x0
    // 0xb5fe80: ldur            x0, [fp, #-8]
    // 0xb5fe84: StoreField: r1->field_b = r0
    //     0xb5fe84: stur            w0, [x1, #0xb]
    // 0xb5fe88: ldr             x2, [fp, #0x10]
    // 0xb5fe8c: StoreField: r1->field_f = r2
    //     0xb5fe8c: stur            w2, [x1, #0xf]
    // 0xb5fe90: LoadField: r3 = r0->field_f
    //     0xb5fe90: ldur            w3, [x0, #0xf]
    // 0xb5fe94: DecompressPointer r3
    //     0xb5fe94: add             x3, x3, HEAP, lsl #32
    // 0xb5fe98: mov             x2, x1
    // 0xb5fe9c: stur            x3, [fp, #-0x10]
    // 0xb5fea0: r1 = Function '<anonymous closure>':.
    //     0xb5fea0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ec8] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xb5fea4: ldr             x1, [x1, #0xec8]
    // 0xb5fea8: r0 = AllocateClosure()
    //     0xb5fea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5feac: ldur            x1, [fp, #-0x10]
    // 0xb5feb0: mov             x2, x0
    // 0xb5feb4: r0 = setState()
    //     0xb5feb4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb5feb8: r0 = Null
    //     0xb5feb8: mov             x0, NULL
    // 0xb5febc: LeaveFrame
    //     0xb5febc: mov             SP, fp
    //     0xb5fec0: ldp             fp, lr, [SP], #0x10
    // 0xb5fec4: ret
    //     0xb5fec4: ret             
    // 0xb5fec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5fec8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5fecc: b               #0xb5fe74
  }
}

// class id: 4079, size: 0x30, field offset: 0xc
//   const constructor, 
class CollectionPosterCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f28c, size: 0x30
    // 0xc7f28c: EnterFrame
    //     0xc7f28c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f290: mov             fp, SP
    // 0xc7f294: mov             x0, x1
    // 0xc7f298: r1 = <CollectionPosterCarousel>
    //     0xc7f298: add             x1, PP, #0x48, lsl #12  ; [pp+0x48830] TypeArguments: <CollectionPosterCarousel>
    //     0xc7f29c: ldr             x1, [x1, #0x830]
    // 0xc7f2a0: r0 = _CollectionPosterCarouselState()
    //     0xc7f2a0: bl              #0xc7f2bc  ; Allocate_CollectionPosterCarouselStateStub -> _CollectionPosterCarouselState (size=0x20)
    // 0xc7f2a4: r1 = Sentinel
    //     0xc7f2a4: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f2a8: StoreField: r0->field_13 = r1
    //     0xc7f2a8: stur            w1, [x0, #0x13]
    // 0xc7f2ac: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7f2ac: stur            xzr, [x0, #0x17]
    // 0xc7f2b0: LeaveFrame
    //     0xc7f2b0: mov             SP, fp
    //     0xc7f2b4: ldp             fp, lr, [SP], #0x10
    // 0xc7f2b8: ret
    //     0xc7f2b8: ret             
  }
}
