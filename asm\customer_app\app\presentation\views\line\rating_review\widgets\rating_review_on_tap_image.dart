// lib: , url: package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart

// class id: 1049581, size: 0x8
class :: {
}

// class id: 3207, size: 0x30, field offset: 0x14
class _RatingReviewOnTapImageState extends State<dynamic> {

  late PageController _pageController; // offset: 0x1c

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x935ab4, size: 0x24
    // 0x935ab4: r1 = true
    //     0x935ab4: add             x1, NULL, #0x20  ; true
    // 0x935ab8: ldr             x2, [SP]
    // 0x935abc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x935abc: ldur            w3, [x2, #0x17]
    // 0x935ac0: DecompressPointer r3
    //     0x935ac0: add             x3, x3, HEAP, lsl #32
    // 0x935ac4: LoadField: r2 = r3->field_f
    //     0x935ac4: ldur            w2, [x3, #0xf]
    // 0x935ac8: DecompressPointer r2
    //     0x935ac8: add             x2, x2, HEAP, lsl #32
    // 0x935acc: StoreField: r2->field_23 = r1
    //     0x935acc: stur            w1, [x2, #0x23]
    // 0x935ad0: r0 = Null
    //     0x935ad0: mov             x0, NULL
    // 0x935ad4: ret
    //     0x935ad4: ret             
  }
  _ _onCollapseChanged(/* No info */) {
    // ** addr: 0x935ad8, size: 0x98
    // 0x935ad8: EnterFrame
    //     0x935ad8: stp             fp, lr, [SP, #-0x10]!
    //     0x935adc: mov             fp, SP
    // 0x935ae0: AllocStack(0x8)
    //     0x935ae0: sub             SP, SP, #8
    // 0x935ae4: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r1, fp-0x8 */)
    //     0x935ae4: stur            x1, [fp, #-8]
    // 0x935ae8: CheckStackOverflow
    //     0x935ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x935aec: cmp             SP, x16
    //     0x935af0: b.ls            #0x935b68
    // 0x935af4: r1 = 1
    //     0x935af4: movz            x1, #0x1
    // 0x935af8: r0 = AllocateContext()
    //     0x935af8: bl              #0x16f6108  ; AllocateContextStub
    // 0x935afc: mov             x1, x0
    // 0x935b00: ldur            x0, [fp, #-8]
    // 0x935b04: StoreField: r1->field_f = r0
    //     0x935b04: stur            w0, [x1, #0xf]
    // 0x935b08: LoadField: r2 = r0->field_1f
    //     0x935b08: ldur            w2, [x0, #0x1f]
    // 0x935b0c: DecompressPointer r2
    //     0x935b0c: add             x2, x2, HEAP, lsl #32
    // 0x935b10: LoadField: r3 = r2->field_27
    //     0x935b10: ldur            w3, [x2, #0x27]
    // 0x935b14: DecompressPointer r3
    //     0x935b14: add             x3, x3, HEAP, lsl #32
    // 0x935b18: tbnz            w3, #4, #0x935b3c
    // 0x935b1c: mov             x2, x1
    // 0x935b20: r1 = Function '<anonymous closure>':.
    //     0x935b20: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f78] AnonymousClosure: (0x935b70), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x935b24: ldr             x1, [x1, #0xf78]
    // 0x935b28: r0 = AllocateClosure()
    //     0x935b28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x935b2c: ldur            x1, [fp, #-8]
    // 0x935b30: mov             x2, x0
    // 0x935b34: r0 = setState()
    //     0x935b34: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x935b38: b               #0x935b58
    // 0x935b3c: mov             x2, x1
    // 0x935b40: r1 = Function '<anonymous closure>':.
    //     0x935b40: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f80] AnonymousClosure: (0x935ab4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x935b44: ldr             x1, [x1, #0xf80]
    // 0x935b48: r0 = AllocateClosure()
    //     0x935b48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x935b4c: ldur            x1, [fp, #-8]
    // 0x935b50: mov             x2, x0
    // 0x935b54: r0 = setState()
    //     0x935b54: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x935b58: r0 = Null
    //     0x935b58: mov             x0, NULL
    // 0x935b5c: LeaveFrame
    //     0x935b5c: mov             SP, fp
    //     0x935b60: ldp             fp, lr, [SP], #0x10
    // 0x935b64: ret
    //     0x935b64: ret             
    // 0x935b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x935b68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x935b6c: b               #0x935af4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x935b70, size: 0x24
    // 0x935b70: r1 = false
    //     0x935b70: add             x1, NULL, #0x30  ; false
    // 0x935b74: ldr             x2, [SP]
    // 0x935b78: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x935b78: ldur            w3, [x2, #0x17]
    // 0x935b7c: DecompressPointer r3
    //     0x935b7c: add             x3, x3, HEAP, lsl #32
    // 0x935b80: LoadField: r2 = r3->field_f
    //     0x935b80: ldur            w2, [x3, #0xf]
    // 0x935b84: DecompressPointer r2
    //     0x935b84: add             x2, x2, HEAP, lsl #32
    // 0x935b88: StoreField: r2->field_23 = r1
    //     0x935b88: stur            w1, [x2, #0x23]
    // 0x935b8c: r0 = Null
    //     0x935b8c: mov             x0, NULL
    // 0x935b90: ret
    //     0x935b90: ret             
  }
  [closure] void _onCollapseChanged(dynamic) {
    // ** addr: 0x935b94, size: 0x38
    // 0x935b94: EnterFrame
    //     0x935b94: stp             fp, lr, [SP, #-0x10]!
    //     0x935b98: mov             fp, SP
    // 0x935b9c: ldr             x0, [fp, #0x10]
    // 0x935ba0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x935ba0: ldur            w1, [x0, #0x17]
    // 0x935ba4: DecompressPointer r1
    //     0x935ba4: add             x1, x1, HEAP, lsl #32
    // 0x935ba8: CheckStackOverflow
    //     0x935ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x935bac: cmp             SP, x16
    //     0x935bb0: b.ls            #0x935bc4
    // 0x935bb4: r0 = _onCollapseChanged()
    //     0x935bb4: bl              #0x935ad8  ; [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged
    // 0x935bb8: LeaveFrame
    //     0x935bb8: mov             SP, fp
    //     0x935bbc: ldp             fp, lr, [SP], #0x10
    // 0x935bc0: ret
    //     0x935bc0: ret             
    // 0x935bc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x935bc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x935bc8: b               #0x935bb4
  }
  _ initState(/* No info */) {
    // ** addr: 0x94b768, size: 0x110
    // 0x94b768: EnterFrame
    //     0x94b768: stp             fp, lr, [SP, #-0x10]!
    //     0x94b76c: mov             fp, SP
    // 0x94b770: AllocStack(0x18)
    //     0x94b770: sub             SP, SP, #0x18
    // 0x94b774: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r2, fp-0x10 */)
    //     0x94b774: mov             x2, x1
    //     0x94b778: stur            x1, [fp, #-0x10]
    // 0x94b77c: CheckStackOverflow
    //     0x94b77c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b780: cmp             SP, x16
    //     0x94b784: b.ls            #0x94b868
    // 0x94b788: LoadField: r0 = r2->field_b
    //     0x94b788: ldur            w0, [x2, #0xb]
    // 0x94b78c: DecompressPointer r0
    //     0x94b78c: add             x0, x0, HEAP, lsl #32
    // 0x94b790: cmp             w0, NULL
    // 0x94b794: b.eq            #0x94b870
    // 0x94b798: LoadField: r1 = r0->field_f
    //     0x94b798: ldur            x1, [x0, #0xf]
    // 0x94b79c: stur            x1, [fp, #-8]
    // 0x94b7a0: StoreField: r2->field_13 = r1
    //     0x94b7a0: stur            x1, [x2, #0x13]
    // 0x94b7a4: r0 = PageController()
    //     0x94b7a4: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x94b7a8: mov             x2, x0
    // 0x94b7ac: ldur            x0, [fp, #-8]
    // 0x94b7b0: stur            x2, [fp, #-0x18]
    // 0x94b7b4: StoreField: r2->field_3f = r0
    //     0x94b7b4: stur            x0, [x2, #0x3f]
    // 0x94b7b8: r0 = true
    //     0x94b7b8: add             x0, NULL, #0x20  ; true
    // 0x94b7bc: StoreField: r2->field_47 = r0
    //     0x94b7bc: stur            w0, [x2, #0x47]
    // 0x94b7c0: d0 = 1.000000
    //     0x94b7c0: fmov            d0, #1.00000000
    // 0x94b7c4: StoreField: r2->field_4b = d0
    //     0x94b7c4: stur            d0, [x2, #0x4b]
    // 0x94b7c8: mov             x1, x2
    // 0x94b7cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94b7cc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94b7d0: r0 = ScrollController()
    //     0x94b7d0: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94b7d4: ldur            x0, [fp, #-0x18]
    // 0x94b7d8: ldur            x3, [fp, #-0x10]
    // 0x94b7dc: StoreField: r3->field_1b = r0
    //     0x94b7dc: stur            w0, [x3, #0x1b]
    //     0x94b7e0: ldurb           w16, [x3, #-1]
    //     0x94b7e4: ldurb           w17, [x0, #-1]
    //     0x94b7e8: and             x16, x17, x16, lsr #2
    //     0x94b7ec: tst             x16, HEAP, lsr #32
    //     0x94b7f0: b.eq            #0x94b7f8
    //     0x94b7f4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x94b7f8: LoadField: r0 = r3->field_1f
    //     0x94b7f8: ldur            w0, [x3, #0x1f]
    // 0x94b7fc: DecompressPointer r0
    //     0x94b7fc: add             x0, x0, HEAP, lsl #32
    // 0x94b800: mov             x2, x3
    // 0x94b804: stur            x0, [fp, #-0x18]
    // 0x94b808: r1 = Function '_onCollapseChanged@1750089873':.
    //     0x94b808: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f70] AnonymousClosure: (0x935b94), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x94b80c: ldr             x1, [x1, #0xf70]
    // 0x94b810: r0 = AllocateClosure()
    //     0x94b810: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94b814: ldur            x1, [fp, #-0x18]
    // 0x94b818: mov             x2, x0
    // 0x94b81c: r0 = addListener()
    //     0x94b81c: bl              #0x7b8dac  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x94b820: ldur            x1, [fp, #-0x10]
    // 0x94b824: LoadField: r2 = r1->field_b
    //     0x94b824: ldur            w2, [x1, #0xb]
    // 0x94b828: DecompressPointer r2
    //     0x94b828: add             x2, x2, HEAP, lsl #32
    // 0x94b82c: cmp             w2, NULL
    // 0x94b830: b.eq            #0x94b874
    // 0x94b834: LoadField: r0 = r2->field_1b
    //     0x94b834: ldur            w0, [x2, #0x1b]
    // 0x94b838: DecompressPointer r0
    //     0x94b838: add             x0, x0, HEAP, lsl #32
    // 0x94b83c: StoreField: r1->field_27 = r0
    //     0x94b83c: stur            w0, [x1, #0x27]
    //     0x94b840: ldurb           w16, [x1, #-1]
    //     0x94b844: ldurb           w17, [x0, #-1]
    //     0x94b848: and             x16, x17, x16, lsr #2
    //     0x94b84c: tst             x16, HEAP, lsr #32
    //     0x94b850: b.eq            #0x94b858
    //     0x94b854: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94b858: r0 = Null
    //     0x94b858: mov             x0, NULL
    // 0x94b85c: LeaveFrame
    //     0x94b85c: mov             SP, fp
    //     0x94b860: ldp             fp, lr, [SP], #0x10
    // 0x94b864: ret
    //     0x94b864: ret             
    // 0x94b868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b868: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b86c: b               #0x94b788
    // 0x94b870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b870: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94b874: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b874: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa97e4, size: 0xb4
    // 0xaa97e4: EnterFrame
    //     0xaa97e4: stp             fp, lr, [SP, #-0x10]!
    //     0xaa97e8: mov             fp, SP
    // 0xaa97ec: AllocStack(0x18)
    //     0xaa97ec: sub             SP, SP, #0x18
    // 0xaa97f0: SetupParameters()
    //     0xaa97f0: ldr             x0, [fp, #0x10]
    //     0xaa97f4: ldur            w4, [x0, #0x17]
    //     0xaa97f8: add             x4, x4, HEAP, lsl #32
    //     0xaa97fc: stur            x4, [fp, #-8]
    // 0xaa9800: CheckStackOverflow
    //     0xaa9800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa9804: cmp             SP, x16
    //     0xaa9808: b.ls            #0xaa988c
    // 0xaa980c: LoadField: r0 = r4->field_f
    //     0xaa980c: ldur            w0, [x4, #0xf]
    // 0xaa9810: DecompressPointer r0
    //     0xaa9810: add             x0, x0, HEAP, lsl #32
    // 0xaa9814: LoadField: r1 = r0->field_27
    //     0xaa9814: ldur            w1, [x0, #0x27]
    // 0xaa9818: DecompressPointer r1
    //     0xaa9818: add             x1, x1, HEAP, lsl #32
    // 0xaa981c: LoadField: r2 = r4->field_13
    //     0xaa981c: ldur            w2, [x4, #0x13]
    // 0xaa9820: DecompressPointer r2
    //     0xaa9820: add             x2, x2, HEAP, lsl #32
    // 0xaa9824: r0 = LoadClassIdInstr(r1)
    //     0xaa9824: ldur            x0, [x1, #-1]
    //     0xaa9828: ubfx            x0, x0, #0xc, #0x14
    // 0xaa982c: r3 = true
    //     0xaa982c: add             x3, NULL, #0x20  ; true
    // 0xaa9830: r0 = GDT[cid_x0 + 0x35a]()
    //     0xaa9830: add             lr, x0, #0x35a
    //     0xaa9834: ldr             lr, [x21, lr, lsl #3]
    //     0xaa9838: blr             lr
    // 0xaa983c: ldur            x0, [fp, #-8]
    // 0xaa9840: LoadField: r1 = r0->field_f
    //     0xaa9840: ldur            w1, [x0, #0xf]
    // 0xaa9844: DecompressPointer r1
    //     0xaa9844: add             x1, x1, HEAP, lsl #32
    // 0xaa9848: LoadField: r0 = r1->field_b
    //     0xaa9848: ldur            w0, [x1, #0xb]
    // 0xaa984c: DecompressPointer r0
    //     0xaa984c: add             x0, x0, HEAP, lsl #32
    // 0xaa9850: cmp             w0, NULL
    // 0xaa9854: b.eq            #0xaa9894
    // 0xaa9858: LoadField: r2 = r1->field_27
    //     0xaa9858: ldur            w2, [x1, #0x27]
    // 0xaa985c: DecompressPointer r2
    //     0xaa985c: add             x2, x2, HEAP, lsl #32
    // 0xaa9860: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa9860: ldur            w1, [x0, #0x17]
    // 0xaa9864: DecompressPointer r1
    //     0xaa9864: add             x1, x1, HEAP, lsl #32
    // 0xaa9868: stp             x2, x1, [SP]
    // 0xaa986c: mov             x0, x1
    // 0xaa9870: ClosureCall
    //     0xaa9870: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xaa9874: ldur            x2, [x0, #0x1f]
    //     0xaa9878: blr             x2
    // 0xaa987c: r0 = Null
    //     0xaa987c: mov             x0, NULL
    // 0xaa9880: LeaveFrame
    //     0xaa9880: mov             SP, fp
    //     0xaa9884: ldp             fp, lr, [SP], #0x10
    // 0xaa9888: ret
    //     0xaa9888: ret             
    // 0xaa988c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa988c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa9890: b               #0xaa980c
    // 0xaa9894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa9894: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xaa9898, size: 0x94
    // 0xaa9898: EnterFrame
    //     0xaa9898: stp             fp, lr, [SP, #-0x10]!
    //     0xaa989c: mov             fp, SP
    // 0xaa98a0: AllocStack(0x20)
    //     0xaa98a0: sub             SP, SP, #0x20
    // 0xaa98a4: SetupParameters()
    //     0xaa98a4: ldr             x0, [fp, #0x18]
    //     0xaa98a8: ldur            w2, [x0, #0x17]
    //     0xaa98ac: add             x2, x2, HEAP, lsl #32
    //     0xaa98b0: stur            x2, [fp, #-8]
    // 0xaa98b4: CheckStackOverflow
    //     0xaa98b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa98b8: cmp             SP, x16
    //     0xaa98bc: b.ls            #0xaa9924
    // 0xaa98c0: ldr             x0, [fp, #0x10]
    // 0xaa98c4: r1 = LoadClassIdInstr(r0)
    //     0xaa98c4: ldur            x1, [x0, #-1]
    //     0xaa98c8: ubfx            x1, x1, #0xc, #0x14
    // 0xaa98cc: r16 = "flag"
    //     0xaa98cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xaa98d0: ldr             x16, [x16, #0xa68]
    // 0xaa98d4: stp             x16, x0, [SP]
    // 0xaa98d8: mov             x0, x1
    // 0xaa98dc: mov             lr, x0
    // 0xaa98e0: ldr             lr, [x21, lr, lsl #3]
    // 0xaa98e4: blr             lr
    // 0xaa98e8: tbnz            w0, #4, #0xaa9914
    // 0xaa98ec: ldur            x2, [fp, #-8]
    // 0xaa98f0: LoadField: r0 = r2->field_f
    //     0xaa98f0: ldur            w0, [x2, #0xf]
    // 0xaa98f4: DecompressPointer r0
    //     0xaa98f4: add             x0, x0, HEAP, lsl #32
    // 0xaa98f8: stur            x0, [fp, #-0x10]
    // 0xaa98fc: r1 = Function '<anonymous closure>':.
    //     0xaa98fc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ee0] AnonymousClosure: (0xaa97e4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xaa9900: ldr             x1, [x1, #0xee0]
    // 0xaa9904: r0 = AllocateClosure()
    //     0xaa9904: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa9908: ldur            x1, [fp, #-0x10]
    // 0xaa990c: mov             x2, x0
    // 0xaa9910: r0 = setState()
    //     0xaa9910: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa9914: r0 = Null
    //     0xaa9914: mov             x0, NULL
    // 0xaa9918: LeaveFrame
    //     0xaa9918: mov             SP, fp
    //     0xaa991c: ldp             fp, lr, [SP], #0x10
    // 0xaa9920: ret
    //     0xaa9920: ret             
    // 0xaa9924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa9924: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa9928: b               #0xaa98c0
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xaa992c, size: 0x744
    // 0xaa992c: EnterFrame
    //     0xaa992c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa9930: mov             fp, SP
    // 0xaa9934: AllocStack(0xa0)
    //     0xaa9934: sub             SP, SP, #0xa0
    // 0xaa9938: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xaa9938: mov             x0, x1
    //     0xaa993c: stur            x1, [fp, #-8]
    //     0xaa9940: mov             x1, x2
    //     0xaa9944: stur            x2, [fp, #-0x10]
    //     0xaa9948: mov             x2, x5
    //     0xaa994c: stur            x3, [fp, #-0x18]
    //     0xaa9950: stur            x5, [fp, #-0x20]
    // 0xaa9954: CheckStackOverflow
    //     0xaa9954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa9958: cmp             SP, x16
    //     0xaa995c: b.ls            #0xaa9fe8
    // 0xaa9960: r1 = 2
    //     0xaa9960: movz            x1, #0x2
    // 0xaa9964: r0 = AllocateContext()
    //     0xaa9964: bl              #0x16f6108  ; AllocateContextStub
    // 0xaa9968: mov             x4, x0
    // 0xaa996c: ldur            x3, [fp, #-8]
    // 0xaa9970: stur            x4, [fp, #-0x28]
    // 0xaa9974: StoreField: r4->field_f = r3
    //     0xaa9974: stur            w3, [x4, #0xf]
    // 0xaa9978: ldur            x2, [fp, #-0x20]
    // 0xaa997c: StoreField: r4->field_13 = r2
    //     0xaa997c: stur            w2, [x4, #0x13]
    // 0xaa9980: LoadField: r1 = r3->field_27
    //     0xaa9980: ldur            w1, [x3, #0x27]
    // 0xaa9984: DecompressPointer r1
    //     0xaa9984: add             x1, x1, HEAP, lsl #32
    // 0xaa9988: r0 = LoadClassIdInstr(r1)
    //     0xaa9988: ldur            x0, [x1, #-1]
    //     0xaa998c: ubfx            x0, x0, #0xc, #0x14
    // 0xaa9990: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa9990: sub             lr, x0, #0xfe
    //     0xaa9994: ldr             lr, [x21, lr, lsl #3]
    //     0xaa9998: blr             lr
    // 0xaa999c: r1 = 60
    //     0xaa999c: movz            x1, #0x3c
    // 0xaa99a0: branchIfSmi(r0, 0xaa99ac)
    //     0xaa99a0: tbz             w0, #0, #0xaa99ac
    // 0xaa99a4: r1 = LoadClassIdInstr(r0)
    //     0xaa99a4: ldur            x1, [x0, #-1]
    //     0xaa99a8: ubfx            x1, x1, #0xc, #0x14
    // 0xaa99ac: r16 = true
    //     0xaa99ac: add             x16, NULL, #0x20  ; true
    // 0xaa99b0: stp             x16, x0, [SP]
    // 0xaa99b4: mov             x0, x1
    // 0xaa99b8: mov             lr, x0
    // 0xaa99bc: ldr             lr, [x21, lr, lsl #3]
    // 0xaa99c0: blr             lr
    // 0xaa99c4: tbnz            w0, #4, #0xaa99d0
    // 0xaa99c8: d0 = 100.000000
    //     0xaa99c8: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xaa99cc: b               #0xaa99d8
    // 0xaa99d0: d0 = 120.000000
    //     0xaa99d0: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xaa99d4: ldr             d0, [x17, #0xa38]
    // 0xaa99d8: ldur            x0, [fp, #-0x18]
    // 0xaa99dc: stur            d0, [fp, #-0x58]
    // 0xaa99e0: r0 = BoxConstraints()
    //     0xaa99e0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xaa99e4: stur            x0, [fp, #-0x20]
    // 0xaa99e8: StoreField: r0->field_7 = rZR
    //     0xaa99e8: stur            xzr, [x0, #7]
    // 0xaa99ec: ldur            d0, [fp, #-0x58]
    // 0xaa99f0: StoreField: r0->field_f = d0
    //     0xaa99f0: stur            d0, [x0, #0xf]
    // 0xaa99f4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xaa99f4: stur            xzr, [x0, #0x17]
    // 0xaa99f8: d0 = inf
    //     0xaa99f8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xaa99fc: StoreField: r0->field_1f = d0
    //     0xaa99fc: stur            d0, [x0, #0x1f]
    // 0xaa9a00: ldur            x1, [fp, #-0x18]
    // 0xaa9a04: cmp             w1, NULL
    // 0xaa9a08: b.ne            #0xaa9a14
    // 0xaa9a0c: r2 = Null
    //     0xaa9a0c: mov             x2, NULL
    // 0xaa9a10: b               #0xaa9a40
    // 0xaa9a14: LoadField: d0 = r1->field_7
    //     0xaa9a14: ldur            d0, [x1, #7]
    // 0xaa9a18: r2 = inline_Allocate_Double()
    //     0xaa9a18: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaa9a1c: add             x2, x2, #0x10
    //     0xaa9a20: cmp             x3, x2
    //     0xaa9a24: b.ls            #0xaa9ff0
    //     0xaa9a28: str             x2, [THR, #0x50]  ; THR::top
    //     0xaa9a2c: sub             x2, x2, #0xf
    //     0xaa9a30: movz            x3, #0xe15c
    //     0xaa9a34: movk            x3, #0x3, lsl #16
    //     0xaa9a38: stur            x3, [x2, #-1]
    // 0xaa9a3c: StoreField: r2->field_7 = d0
    //     0xaa9a3c: stur            d0, [x2, #7]
    // 0xaa9a40: cmp             w2, NULL
    // 0xaa9a44: b.ne            #0xaa9a50
    // 0xaa9a48: d0 = 0.000000
    //     0xaa9a48: eor             v0.16b, v0.16b, v0.16b
    // 0xaa9a4c: b               #0xaa9a54
    // 0xaa9a50: LoadField: d0 = r2->field_7
    //     0xaa9a50: ldur            d0, [x2, #7]
    // 0xaa9a54: stur            d0, [fp, #-0x70]
    // 0xaa9a58: cmp             w1, NULL
    // 0xaa9a5c: b.ne            #0xaa9a68
    // 0xaa9a60: r2 = Null
    //     0xaa9a60: mov             x2, NULL
    // 0xaa9a64: b               #0xaa9a94
    // 0xaa9a68: LoadField: d1 = r1->field_f
    //     0xaa9a68: ldur            d1, [x1, #0xf]
    // 0xaa9a6c: r2 = inline_Allocate_Double()
    //     0xaa9a6c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaa9a70: add             x2, x2, #0x10
    //     0xaa9a74: cmp             x3, x2
    //     0xaa9a78: b.ls            #0xaaa00c
    //     0xaa9a7c: str             x2, [THR, #0x50]  ; THR::top
    //     0xaa9a80: sub             x2, x2, #0xf
    //     0xaa9a84: movz            x3, #0xe15c
    //     0xaa9a88: movk            x3, #0x3, lsl #16
    //     0xaa9a8c: stur            x3, [x2, #-1]
    // 0xaa9a90: StoreField: r2->field_7 = d1
    //     0xaa9a90: stur            d1, [x2, #7]
    // 0xaa9a94: cmp             w2, NULL
    // 0xaa9a98: b.ne            #0xaa9aa4
    // 0xaa9a9c: d2 = 0.000000
    //     0xaa9a9c: eor             v2.16b, v2.16b, v2.16b
    // 0xaa9aa0: b               #0xaa9aac
    // 0xaa9aa4: LoadField: d1 = r2->field_7
    //     0xaa9aa4: ldur            d1, [x2, #7]
    // 0xaa9aa8: mov             v2.16b, v1.16b
    // 0xaa9aac: d1 = 50.000000
    //     0xaa9aac: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xaa9ab0: fsub            d3, d2, d1
    // 0xaa9ab4: stur            d3, [fp, #-0x68]
    // 0xaa9ab8: cmp             w1, NULL
    // 0xaa9abc: b.ne            #0xaa9ac8
    // 0xaa9ac0: r2 = Null
    //     0xaa9ac0: mov             x2, NULL
    // 0xaa9ac4: b               #0xaa9af4
    // 0xaa9ac8: LoadField: d2 = r1->field_7
    //     0xaa9ac8: ldur            d2, [x1, #7]
    // 0xaa9acc: r2 = inline_Allocate_Double()
    //     0xaa9acc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaa9ad0: add             x2, x2, #0x10
    //     0xaa9ad4: cmp             x3, x2
    //     0xaa9ad8: b.ls            #0xaaa028
    //     0xaa9adc: str             x2, [THR, #0x50]  ; THR::top
    //     0xaa9ae0: sub             x2, x2, #0xf
    //     0xaa9ae4: movz            x3, #0xe15c
    //     0xaa9ae8: movk            x3, #0x3, lsl #16
    //     0xaa9aec: stur            x3, [x2, #-1]
    // 0xaa9af0: StoreField: r2->field_7 = d2
    //     0xaa9af0: stur            d2, [x2, #7]
    // 0xaa9af4: cmp             w2, NULL
    // 0xaa9af8: b.ne            #0xaa9b04
    // 0xaa9afc: d2 = 0.000000
    //     0xaa9afc: eor             v2.16b, v2.16b, v2.16b
    // 0xaa9b00: b               #0xaa9b08
    // 0xaa9b04: LoadField: d2 = r2->field_7
    //     0xaa9b04: ldur            d2, [x2, #7]
    // 0xaa9b08: fadd            d4, d2, d1
    // 0xaa9b0c: stur            d4, [fp, #-0x60]
    // 0xaa9b10: cmp             w1, NULL
    // 0xaa9b14: b.ne            #0xaa9b20
    // 0xaa9b18: r1 = Null
    //     0xaa9b18: mov             x1, NULL
    // 0xaa9b1c: b               #0xaa9b4c
    // 0xaa9b20: LoadField: d1 = r1->field_f
    //     0xaa9b20: ldur            d1, [x1, #0xf]
    // 0xaa9b24: r1 = inline_Allocate_Double()
    //     0xaa9b24: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xaa9b28: add             x1, x1, #0x10
    //     0xaa9b2c: cmp             x2, x1
    //     0xaa9b30: b.ls            #0xaaa04c
    //     0xaa9b34: str             x1, [THR, #0x50]  ; THR::top
    //     0xaa9b38: sub             x1, x1, #0xf
    //     0xaa9b3c: movz            x2, #0xe15c
    //     0xaa9b40: movk            x2, #0x3, lsl #16
    //     0xaa9b44: stur            x2, [x1, #-1]
    // 0xaa9b48: StoreField: r1->field_7 = d1
    //     0xaa9b48: stur            d1, [x1, #7]
    // 0xaa9b4c: cmp             w1, NULL
    // 0xaa9b50: b.ne            #0xaa9b5c
    // 0xaa9b54: d1 = 0.000000
    //     0xaa9b54: eor             v1.16b, v1.16b, v1.16b
    // 0xaa9b58: b               #0xaa9b60
    // 0xaa9b5c: LoadField: d1 = r1->field_7
    //     0xaa9b5c: ldur            d1, [x1, #7]
    // 0xaa9b60: ldur            x1, [fp, #-8]
    // 0xaa9b64: ldur            x2, [fp, #-0x28]
    // 0xaa9b68: stur            d1, [fp, #-0x58]
    // 0xaa9b6c: r0 = RelativeRect()
    //     0xaa9b6c: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xaa9b70: mov             x3, x0
    // 0xaa9b74: ldur            d0, [fp, #-0x70]
    // 0xaa9b78: stur            x3, [fp, #-0x18]
    // 0xaa9b7c: StoreField: r3->field_7 = d0
    //     0xaa9b7c: stur            d0, [x3, #7]
    // 0xaa9b80: ldur            d0, [fp, #-0x68]
    // 0xaa9b84: StoreField: r3->field_f = d0
    //     0xaa9b84: stur            d0, [x3, #0xf]
    // 0xaa9b88: ldur            d0, [fp, #-0x60]
    // 0xaa9b8c: ArrayStore: r3[0] = d0  ; List_8
    //     0xaa9b8c: stur            d0, [x3, #0x17]
    // 0xaa9b90: ldur            d0, [fp, #-0x58]
    // 0xaa9b94: StoreField: r3->field_1f = d0
    //     0xaa9b94: stur            d0, [x3, #0x1f]
    // 0xaa9b98: ldur            x4, [fp, #-8]
    // 0xaa9b9c: LoadField: r1 = r4->field_27
    //     0xaa9b9c: ldur            w1, [x4, #0x27]
    // 0xaa9ba0: DecompressPointer r1
    //     0xaa9ba0: add             x1, x1, HEAP, lsl #32
    // 0xaa9ba4: ldur            x5, [fp, #-0x28]
    // 0xaa9ba8: LoadField: r2 = r5->field_13
    //     0xaa9ba8: ldur            w2, [x5, #0x13]
    // 0xaa9bac: DecompressPointer r2
    //     0xaa9bac: add             x2, x2, HEAP, lsl #32
    // 0xaa9bb0: r0 = LoadClassIdInstr(r1)
    //     0xaa9bb0: ldur            x0, [x1, #-1]
    //     0xaa9bb4: ubfx            x0, x0, #0xc, #0x14
    // 0xaa9bb8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa9bb8: sub             lr, x0, #0xfe
    //     0xaa9bbc: ldr             lr, [x21, lr, lsl #3]
    //     0xaa9bc0: blr             lr
    // 0xaa9bc4: r1 = 60
    //     0xaa9bc4: movz            x1, #0x3c
    // 0xaa9bc8: branchIfSmi(r0, 0xaa9bd4)
    //     0xaa9bc8: tbz             w0, #0, #0xaa9bd4
    // 0xaa9bcc: r1 = LoadClassIdInstr(r0)
    //     0xaa9bcc: ldur            x1, [x0, #-1]
    //     0xaa9bd0: ubfx            x1, x1, #0xc, #0x14
    // 0xaa9bd4: r16 = true
    //     0xaa9bd4: add             x16, NULL, #0x20  ; true
    // 0xaa9bd8: stp             x16, x0, [SP]
    // 0xaa9bdc: mov             x0, x1
    // 0xaa9be0: mov             lr, x0
    // 0xaa9be4: ldr             lr, [x21, lr, lsl #3]
    // 0xaa9be8: blr             lr
    // 0xaa9bec: tbnz            w0, #4, #0xaa9bf8
    // 0xaa9bf0: r4 = Null
    //     0xaa9bf0: mov             x4, NULL
    // 0xaa9bf4: b               #0xaa9c0c
    // 0xaa9bf8: ldur            x2, [fp, #-0x28]
    // 0xaa9bfc: r1 = Function '<anonymous closure>':.
    //     0xaa9bfc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ed0] AnonymousClosure: (0xaaa070), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xaa9c00: ldr             x1, [x1, #0xed0]
    // 0xaa9c04: r0 = AllocateClosure()
    //     0xaa9c04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa9c08: mov             x4, x0
    // 0xaa9c0c: ldur            x0, [fp, #-8]
    // 0xaa9c10: ldur            x3, [fp, #-0x28]
    // 0xaa9c14: stur            x4, [fp, #-0x30]
    // 0xaa9c18: r1 = <Widget>
    //     0xaa9c18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaa9c1c: r2 = 0
    //     0xaa9c1c: movz            x2, #0
    // 0xaa9c20: r0 = _GrowableList()
    //     0xaa9c20: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaa9c24: mov             x4, x0
    // 0xaa9c28: ldur            x3, [fp, #-8]
    // 0xaa9c2c: stur            x4, [fp, #-0x38]
    // 0xaa9c30: LoadField: r1 = r3->field_27
    //     0xaa9c30: ldur            w1, [x3, #0x27]
    // 0xaa9c34: DecompressPointer r1
    //     0xaa9c34: add             x1, x1, HEAP, lsl #32
    // 0xaa9c38: ldur            x5, [fp, #-0x28]
    // 0xaa9c3c: LoadField: r2 = r5->field_13
    //     0xaa9c3c: ldur            w2, [x5, #0x13]
    // 0xaa9c40: DecompressPointer r2
    //     0xaa9c40: add             x2, x2, HEAP, lsl #32
    // 0xaa9c44: r0 = LoadClassIdInstr(r1)
    //     0xaa9c44: ldur            x0, [x1, #-1]
    //     0xaa9c48: ubfx            x0, x0, #0xc, #0x14
    // 0xaa9c4c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa9c4c: sub             lr, x0, #0xfe
    //     0xaa9c50: ldr             lr, [x21, lr, lsl #3]
    //     0xaa9c54: blr             lr
    // 0xaa9c58: r1 = 60
    //     0xaa9c58: movz            x1, #0x3c
    // 0xaa9c5c: branchIfSmi(r0, 0xaa9c68)
    //     0xaa9c5c: tbz             w0, #0, #0xaa9c68
    // 0xaa9c60: r1 = LoadClassIdInstr(r0)
    //     0xaa9c60: ldur            x1, [x0, #-1]
    //     0xaa9c64: ubfx            x1, x1, #0xc, #0x14
    // 0xaa9c68: r16 = true
    //     0xaa9c68: add             x16, NULL, #0x20  ; true
    // 0xaa9c6c: stp             x16, x0, [SP]
    // 0xaa9c70: mov             x0, x1
    // 0xaa9c74: mov             lr, x0
    // 0xaa9c78: ldr             lr, [x21, lr, lsl #3]
    // 0xaa9c7c: blr             lr
    // 0xaa9c80: tbnz            w0, #4, #0xaa9ce4
    // 0xaa9c84: ldur            x0, [fp, #-0x38]
    // 0xaa9c88: LoadField: r1 = r0->field_b
    //     0xaa9c88: ldur            w1, [x0, #0xb]
    // 0xaa9c8c: LoadField: r2 = r0->field_f
    //     0xaa9c8c: ldur            w2, [x0, #0xf]
    // 0xaa9c90: DecompressPointer r2
    //     0xaa9c90: add             x2, x2, HEAP, lsl #32
    // 0xaa9c94: LoadField: r3 = r2->field_b
    //     0xaa9c94: ldur            w3, [x2, #0xb]
    // 0xaa9c98: r2 = LoadInt32Instr(r1)
    //     0xaa9c98: sbfx            x2, x1, #1, #0x1f
    // 0xaa9c9c: stur            x2, [fp, #-0x40]
    // 0xaa9ca0: r1 = LoadInt32Instr(r3)
    //     0xaa9ca0: sbfx            x1, x3, #1, #0x1f
    // 0xaa9ca4: cmp             x2, x1
    // 0xaa9ca8: b.ne            #0xaa9cb4
    // 0xaa9cac: mov             x1, x0
    // 0xaa9cb0: r0 = _growToNextCapacity()
    //     0xaa9cb0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa9cb4: ldur            x0, [fp, #-0x38]
    // 0xaa9cb8: ldur            x1, [fp, #-0x40]
    // 0xaa9cbc: add             x2, x1, #1
    // 0xaa9cc0: lsl             x3, x2, #1
    // 0xaa9cc4: StoreField: r0->field_b = r3
    //     0xaa9cc4: stur            w3, [x0, #0xb]
    // 0xaa9cc8: LoadField: r2 = r0->field_f
    //     0xaa9cc8: ldur            w2, [x0, #0xf]
    // 0xaa9ccc: DecompressPointer r2
    //     0xaa9ccc: add             x2, x2, HEAP, lsl #32
    // 0xaa9cd0: add             x3, x2, x1, lsl #2
    // 0xaa9cd4: r16 = Instance_Icon
    //     0xaa9cd4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xaa9cd8: ldr             x16, [x16, #0xa48]
    // 0xaa9cdc: StoreField: r3->field_f = r16
    //     0xaa9cdc: stur            w16, [x3, #0xf]
    // 0xaa9ce0: b               #0xaa9ce8
    // 0xaa9ce4: ldur            x0, [fp, #-0x38]
    // 0xaa9ce8: LoadField: r1 = r0->field_b
    //     0xaa9ce8: ldur            w1, [x0, #0xb]
    // 0xaa9cec: LoadField: r2 = r0->field_f
    //     0xaa9cec: ldur            w2, [x0, #0xf]
    // 0xaa9cf0: DecompressPointer r2
    //     0xaa9cf0: add             x2, x2, HEAP, lsl #32
    // 0xaa9cf4: LoadField: r3 = r2->field_b
    //     0xaa9cf4: ldur            w3, [x2, #0xb]
    // 0xaa9cf8: r2 = LoadInt32Instr(r1)
    //     0xaa9cf8: sbfx            x2, x1, #1, #0x1f
    // 0xaa9cfc: stur            x2, [fp, #-0x40]
    // 0xaa9d00: r1 = LoadInt32Instr(r3)
    //     0xaa9d00: sbfx            x1, x3, #1, #0x1f
    // 0xaa9d04: cmp             x2, x1
    // 0xaa9d08: b.ne            #0xaa9d14
    // 0xaa9d0c: mov             x1, x0
    // 0xaa9d10: r0 = _growToNextCapacity()
    //     0xaa9d10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa9d14: ldur            x1, [fp, #-8]
    // 0xaa9d18: ldur            x4, [fp, #-0x28]
    // 0xaa9d1c: ldur            x3, [fp, #-0x38]
    // 0xaa9d20: ldur            x0, [fp, #-0x40]
    // 0xaa9d24: add             x2, x0, #1
    // 0xaa9d28: lsl             x5, x2, #1
    // 0xaa9d2c: StoreField: r3->field_b = r5
    //     0xaa9d2c: stur            w5, [x3, #0xb]
    // 0xaa9d30: LoadField: r2 = r3->field_f
    //     0xaa9d30: ldur            w2, [x3, #0xf]
    // 0xaa9d34: DecompressPointer r2
    //     0xaa9d34: add             x2, x2, HEAP, lsl #32
    // 0xaa9d38: add             x5, x2, x0, lsl #2
    // 0xaa9d3c: r16 = Instance_SizedBox
    //     0xaa9d3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xaa9d40: ldr             x16, [x16, #0xa50]
    // 0xaa9d44: StoreField: r5->field_f = r16
    //     0xaa9d44: stur            w16, [x5, #0xf]
    // 0xaa9d48: LoadField: r0 = r1->field_27
    //     0xaa9d48: ldur            w0, [x1, #0x27]
    // 0xaa9d4c: DecompressPointer r0
    //     0xaa9d4c: add             x0, x0, HEAP, lsl #32
    // 0xaa9d50: LoadField: r2 = r4->field_13
    //     0xaa9d50: ldur            w2, [x4, #0x13]
    // 0xaa9d54: DecompressPointer r2
    //     0xaa9d54: add             x2, x2, HEAP, lsl #32
    // 0xaa9d58: r1 = LoadClassIdInstr(r0)
    //     0xaa9d58: ldur            x1, [x0, #-1]
    //     0xaa9d5c: ubfx            x1, x1, #0xc, #0x14
    // 0xaa9d60: mov             x16, x0
    // 0xaa9d64: mov             x0, x1
    // 0xaa9d68: mov             x1, x16
    // 0xaa9d6c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa9d6c: sub             lr, x0, #0xfe
    //     0xaa9d70: ldr             lr, [x21, lr, lsl #3]
    //     0xaa9d74: blr             lr
    // 0xaa9d78: r1 = 60
    //     0xaa9d78: movz            x1, #0x3c
    // 0xaa9d7c: branchIfSmi(r0, 0xaa9d88)
    //     0xaa9d7c: tbz             w0, #0, #0xaa9d88
    // 0xaa9d80: r1 = LoadClassIdInstr(r0)
    //     0xaa9d80: ldur            x1, [x0, #-1]
    //     0xaa9d84: ubfx            x1, x1, #0xc, #0x14
    // 0xaa9d88: r16 = true
    //     0xaa9d88: add             x16, NULL, #0x20  ; true
    // 0xaa9d8c: stp             x16, x0, [SP]
    // 0xaa9d90: mov             x0, x1
    // 0xaa9d94: mov             lr, x0
    // 0xaa9d98: ldr             lr, [x21, lr, lsl #3]
    // 0xaa9d9c: blr             lr
    // 0xaa9da0: tbnz            w0, #4, #0xaa9db0
    // 0xaa9da4: r0 = "Flagged"
    //     0xaa9da4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xaa9da8: ldr             x0, [x0, #0xa58]
    // 0xaa9dac: b               #0xaa9db8
    // 0xaa9db0: r0 = "Flag as abusive"
    //     0xaa9db0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xaa9db4: ldr             x0, [x0, #0xa60]
    // 0xaa9db8: ldur            x1, [fp, #-0x10]
    // 0xaa9dbc: stur            x0, [fp, #-8]
    // 0xaa9dc0: r0 = of()
    //     0xaa9dc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa9dc4: LoadField: r1 = r0->field_87
    //     0xaa9dc4: ldur            w1, [x0, #0x87]
    // 0xaa9dc8: DecompressPointer r1
    //     0xaa9dc8: add             x1, x1, HEAP, lsl #32
    // 0xaa9dcc: LoadField: r0 = r1->field_33
    //     0xaa9dcc: ldur            w0, [x1, #0x33]
    // 0xaa9dd0: DecompressPointer r0
    //     0xaa9dd0: add             x0, x0, HEAP, lsl #32
    // 0xaa9dd4: cmp             w0, NULL
    // 0xaa9dd8: b.ne            #0xaa9de4
    // 0xaa9ddc: r2 = Null
    //     0xaa9ddc: mov             x2, NULL
    // 0xaa9de0: b               #0xaa9e08
    // 0xaa9de4: r16 = 12.000000
    //     0xaa9de4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaa9de8: ldr             x16, [x16, #0x9e8]
    // 0xaa9dec: r30 = Instance_Color
    //     0xaa9dec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaa9df0: stp             lr, x16, [SP]
    // 0xaa9df4: mov             x1, x0
    // 0xaa9df8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaa9df8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaa9dfc: ldr             x4, [x4, #0xaa0]
    // 0xaa9e00: r0 = copyWith()
    //     0xaa9e00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaa9e04: mov             x2, x0
    // 0xaa9e08: ldur            x1, [fp, #-0x38]
    // 0xaa9e0c: ldur            x0, [fp, #-8]
    // 0xaa9e10: stur            x2, [fp, #-0x48]
    // 0xaa9e14: r0 = Text()
    //     0xaa9e14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaa9e18: mov             x2, x0
    // 0xaa9e1c: ldur            x0, [fp, #-8]
    // 0xaa9e20: stur            x2, [fp, #-0x50]
    // 0xaa9e24: StoreField: r2->field_b = r0
    //     0xaa9e24: stur            w0, [x2, #0xb]
    // 0xaa9e28: ldur            x0, [fp, #-0x48]
    // 0xaa9e2c: StoreField: r2->field_13 = r0
    //     0xaa9e2c: stur            w0, [x2, #0x13]
    // 0xaa9e30: ldur            x0, [fp, #-0x38]
    // 0xaa9e34: LoadField: r1 = r0->field_b
    //     0xaa9e34: ldur            w1, [x0, #0xb]
    // 0xaa9e38: LoadField: r3 = r0->field_f
    //     0xaa9e38: ldur            w3, [x0, #0xf]
    // 0xaa9e3c: DecompressPointer r3
    //     0xaa9e3c: add             x3, x3, HEAP, lsl #32
    // 0xaa9e40: LoadField: r4 = r3->field_b
    //     0xaa9e40: ldur            w4, [x3, #0xb]
    // 0xaa9e44: r3 = LoadInt32Instr(r1)
    //     0xaa9e44: sbfx            x3, x1, #1, #0x1f
    // 0xaa9e48: stur            x3, [fp, #-0x40]
    // 0xaa9e4c: r1 = LoadInt32Instr(r4)
    //     0xaa9e4c: sbfx            x1, x4, #1, #0x1f
    // 0xaa9e50: cmp             x3, x1
    // 0xaa9e54: b.ne            #0xaa9e60
    // 0xaa9e58: mov             x1, x0
    // 0xaa9e5c: r0 = _growToNextCapacity()
    //     0xaa9e5c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa9e60: ldur            x4, [fp, #-0x30]
    // 0xaa9e64: ldur            x2, [fp, #-0x38]
    // 0xaa9e68: ldur            x3, [fp, #-0x40]
    // 0xaa9e6c: add             x0, x3, #1
    // 0xaa9e70: lsl             x1, x0, #1
    // 0xaa9e74: StoreField: r2->field_b = r1
    //     0xaa9e74: stur            w1, [x2, #0xb]
    // 0xaa9e78: LoadField: r1 = r2->field_f
    //     0xaa9e78: ldur            w1, [x2, #0xf]
    // 0xaa9e7c: DecompressPointer r1
    //     0xaa9e7c: add             x1, x1, HEAP, lsl #32
    // 0xaa9e80: ldur            x0, [fp, #-0x50]
    // 0xaa9e84: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaa9e84: add             x25, x1, x3, lsl #2
    //     0xaa9e88: add             x25, x25, #0xf
    //     0xaa9e8c: str             w0, [x25]
    //     0xaa9e90: tbz             w0, #0, #0xaa9eac
    //     0xaa9e94: ldurb           w16, [x1, #-1]
    //     0xaa9e98: ldurb           w17, [x0, #-1]
    //     0xaa9e9c: and             x16, x17, x16, lsr #2
    //     0xaa9ea0: tst             x16, HEAP, lsr #32
    //     0xaa9ea4: b.eq            #0xaa9eac
    //     0xaa9ea8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaa9eac: r0 = Row()
    //     0xaa9eac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaa9eb0: mov             x2, x0
    // 0xaa9eb4: r0 = Instance_Axis
    //     0xaa9eb4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaa9eb8: stur            x2, [fp, #-8]
    // 0xaa9ebc: StoreField: r2->field_f = r0
    //     0xaa9ebc: stur            w0, [x2, #0xf]
    // 0xaa9ec0: r0 = Instance_MainAxisAlignment
    //     0xaa9ec0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaa9ec4: ldr             x0, [x0, #0xa08]
    // 0xaa9ec8: StoreField: r2->field_13 = r0
    //     0xaa9ec8: stur            w0, [x2, #0x13]
    // 0xaa9ecc: r0 = Instance_MainAxisSize
    //     0xaa9ecc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaa9ed0: ldr             x0, [x0, #0xa10]
    // 0xaa9ed4: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa9ed4: stur            w0, [x2, #0x17]
    // 0xaa9ed8: r0 = Instance_CrossAxisAlignment
    //     0xaa9ed8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaa9edc: ldr             x0, [x0, #0xa18]
    // 0xaa9ee0: StoreField: r2->field_1b = r0
    //     0xaa9ee0: stur            w0, [x2, #0x1b]
    // 0xaa9ee4: r0 = Instance_VerticalDirection
    //     0xaa9ee4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaa9ee8: ldr             x0, [x0, #0xa20]
    // 0xaa9eec: StoreField: r2->field_23 = r0
    //     0xaa9eec: stur            w0, [x2, #0x23]
    // 0xaa9ef0: r0 = Instance_Clip
    //     0xaa9ef0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaa9ef4: ldr             x0, [x0, #0x38]
    // 0xaa9ef8: StoreField: r2->field_2b = r0
    //     0xaa9ef8: stur            w0, [x2, #0x2b]
    // 0xaa9efc: StoreField: r2->field_2f = rZR
    //     0xaa9efc: stur            xzr, [x2, #0x2f]
    // 0xaa9f00: ldur            x0, [fp, #-0x38]
    // 0xaa9f04: StoreField: r2->field_b = r0
    //     0xaa9f04: stur            w0, [x2, #0xb]
    // 0xaa9f08: r1 = <String>
    //     0xaa9f08: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xaa9f0c: r0 = PopupMenuItem()
    //     0xaa9f0c: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xaa9f10: mov             x3, x0
    // 0xaa9f14: r0 = "flag"
    //     0xaa9f14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xaa9f18: ldr             x0, [x0, #0xa68]
    // 0xaa9f1c: stur            x3, [fp, #-0x38]
    // 0xaa9f20: StoreField: r3->field_f = r0
    //     0xaa9f20: stur            w0, [x3, #0xf]
    // 0xaa9f24: ldur            x0, [fp, #-0x30]
    // 0xaa9f28: StoreField: r3->field_13 = r0
    //     0xaa9f28: stur            w0, [x3, #0x13]
    // 0xaa9f2c: r0 = true
    //     0xaa9f2c: add             x0, NULL, #0x20  ; true
    // 0xaa9f30: ArrayStore: r3[0] = r0  ; List_4
    //     0xaa9f30: stur            w0, [x3, #0x17]
    // 0xaa9f34: d0 = 25.000000
    //     0xaa9f34: fmov            d0, #25.00000000
    // 0xaa9f38: StoreField: r3->field_1b = d0
    //     0xaa9f38: stur            d0, [x3, #0x1b]
    // 0xaa9f3c: ldur            x0, [fp, #-8]
    // 0xaa9f40: StoreField: r3->field_33 = r0
    //     0xaa9f40: stur            w0, [x3, #0x33]
    // 0xaa9f44: r1 = Null
    //     0xaa9f44: mov             x1, NULL
    // 0xaa9f48: r2 = 2
    //     0xaa9f48: movz            x2, #0x2
    // 0xaa9f4c: r0 = AllocateArray()
    //     0xaa9f4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaa9f50: mov             x2, x0
    // 0xaa9f54: ldur            x0, [fp, #-0x38]
    // 0xaa9f58: stur            x2, [fp, #-8]
    // 0xaa9f5c: StoreField: r2->field_f = r0
    //     0xaa9f5c: stur            w0, [x2, #0xf]
    // 0xaa9f60: r1 = <PopupMenuEntry<String>>
    //     0xaa9f60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xaa9f64: ldr             x1, [x1, #0xa70]
    // 0xaa9f68: r0 = AllocateGrowableArray()
    //     0xaa9f68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaa9f6c: mov             x1, x0
    // 0xaa9f70: ldur            x0, [fp, #-8]
    // 0xaa9f74: StoreField: r1->field_f = r0
    //     0xaa9f74: stur            w0, [x1, #0xf]
    // 0xaa9f78: r0 = 2
    //     0xaa9f78: movz            x0, #0x2
    // 0xaa9f7c: StoreField: r1->field_b = r0
    //     0xaa9f7c: stur            w0, [x1, #0xb]
    // 0xaa9f80: r16 = <String>
    //     0xaa9f80: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xaa9f84: ldur            lr, [fp, #-0x10]
    // 0xaa9f88: stp             lr, x16, [SP, #0x20]
    // 0xaa9f8c: ldur            x16, [fp, #-0x18]
    // 0xaa9f90: stp             x16, x1, [SP, #0x10]
    // 0xaa9f94: r16 = Instance_Color
    //     0xaa9f94: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaa9f98: ldur            lr, [fp, #-0x20]
    // 0xaa9f9c: stp             lr, x16, [SP]
    // 0xaa9fa0: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xaa9fa0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xaa9fa4: ldr             x4, [x4, #0xa78]
    // 0xaa9fa8: r0 = showMenu()
    //     0xaa9fa8: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xaa9fac: ldur            x2, [fp, #-0x28]
    // 0xaa9fb0: r1 = Function '<anonymous closure>':.
    //     0xaa9fb0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ed8] AnonymousClosure: (0xaa9898), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xaa9fb4: ldr             x1, [x1, #0xed8]
    // 0xaa9fb8: stur            x0, [fp, #-8]
    // 0xaa9fbc: r0 = AllocateClosure()
    //     0xaa9fbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa9fc0: r16 = <Null?>
    //     0xaa9fc0: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xaa9fc4: ldur            lr, [fp, #-8]
    // 0xaa9fc8: stp             lr, x16, [SP, #8]
    // 0xaa9fcc: str             x0, [SP]
    // 0xaa9fd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaa9fd0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaa9fd4: r0 = then()
    //     0xaa9fd4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xaa9fd8: r0 = Null
    //     0xaa9fd8: mov             x0, NULL
    // 0xaa9fdc: LeaveFrame
    //     0xaa9fdc: mov             SP, fp
    //     0xaa9fe0: ldp             fp, lr, [SP], #0x10
    // 0xaa9fe4: ret
    //     0xaa9fe4: ret             
    // 0xaa9fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa9fe8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa9fec: b               #0xaa9960
    // 0xaa9ff0: SaveReg d0
    //     0xaa9ff0: str             q0, [SP, #-0x10]!
    // 0xaa9ff4: stp             x0, x1, [SP, #-0x10]!
    // 0xaa9ff8: r0 = AllocateDouble()
    //     0xaa9ff8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa9ffc: mov             x2, x0
    // 0xaaa000: ldp             x0, x1, [SP], #0x10
    // 0xaaa004: RestoreReg d0
    //     0xaaa004: ldr             q0, [SP], #0x10
    // 0xaaa008: b               #0xaa9a3c
    // 0xaaa00c: stp             q0, q1, [SP, #-0x20]!
    // 0xaaa010: stp             x0, x1, [SP, #-0x10]!
    // 0xaaa014: r0 = AllocateDouble()
    //     0xaaa014: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaaa018: mov             x2, x0
    // 0xaaa01c: ldp             x0, x1, [SP], #0x10
    // 0xaaa020: ldp             q0, q1, [SP], #0x20
    // 0xaaa024: b               #0xaa9a90
    // 0xaaa028: stp             q2, q3, [SP, #-0x20]!
    // 0xaaa02c: stp             q0, q1, [SP, #-0x20]!
    // 0xaaa030: stp             x0, x1, [SP, #-0x10]!
    // 0xaaa034: r0 = AllocateDouble()
    //     0xaaa034: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaaa038: mov             x2, x0
    // 0xaaa03c: ldp             x0, x1, [SP], #0x10
    // 0xaaa040: ldp             q0, q1, [SP], #0x20
    // 0xaaa044: ldp             q2, q3, [SP], #0x20
    // 0xaaa048: b               #0xaa9af0
    // 0xaaa04c: stp             q3, q4, [SP, #-0x20]!
    // 0xaaa050: stp             q0, q1, [SP, #-0x20]!
    // 0xaaa054: SaveReg r0
    //     0xaaa054: str             x0, [SP, #-8]!
    // 0xaaa058: r0 = AllocateDouble()
    //     0xaaa058: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaaa05c: mov             x1, x0
    // 0xaaa060: RestoreReg r0
    //     0xaaa060: ldr             x0, [SP], #8
    // 0xaaa064: ldp             q0, q1, [SP], #0x20
    // 0xaaa068: ldp             q3, q4, [SP], #0x20
    // 0xaaa06c: b               #0xaa9b48
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaaa070, size: 0x60
    // 0xaaa070: EnterFrame
    //     0xaaa070: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa074: mov             fp, SP
    // 0xaaa078: AllocStack(0x8)
    //     0xaaa078: sub             SP, SP, #8
    // 0xaaa07c: SetupParameters()
    //     0xaaa07c: ldr             x0, [fp, #0x10]
    //     0xaaa080: ldur            w2, [x0, #0x17]
    //     0xaaa084: add             x2, x2, HEAP, lsl #32
    // 0xaaa088: CheckStackOverflow
    //     0xaaa088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa08c: cmp             SP, x16
    //     0xaaa090: b.ls            #0xaaa0c8
    // 0xaaa094: LoadField: r0 = r2->field_f
    //     0xaaa094: ldur            w0, [x2, #0xf]
    // 0xaaa098: DecompressPointer r0
    //     0xaaa098: add             x0, x0, HEAP, lsl #32
    // 0xaaa09c: stur            x0, [fp, #-8]
    // 0xaaa0a0: r1 = Function '<anonymous closure>':.
    //     0xaaa0a0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ee8] AnonymousClosure: (0xaa97e4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xaaa0a4: ldr             x1, [x1, #0xee8]
    // 0xaaa0a8: r0 = AllocateClosure()
    //     0xaaa0a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaaa0ac: ldur            x1, [fp, #-8]
    // 0xaaa0b0: mov             x2, x0
    // 0xaaa0b4: r0 = setState()
    //     0xaaa0b4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaaa0b8: r0 = Null
    //     0xaaa0b8: mov             x0, NULL
    // 0xaaa0bc: LeaveFrame
    //     0xaaa0bc: mov             SP, fp
    //     0xaaa0c0: ldp             fp, lr, [SP], #0x10
    // 0xaaa0c4: ret
    //     0xaaa0c4: ret             
    // 0xaaa0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa0c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa0cc: b               #0xaaa094
  }
  [closure] void <anonymous closure>(dynamic, TapDownDetails) {
    // ** addr: 0xaaa130, size: 0x4c
    // 0xaaa130: ldr             x1, [SP, #8]
    // 0xaaa134: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaaa134: ldur            w2, [x1, #0x17]
    // 0xaaa138: DecompressPointer r2
    //     0xaaa138: add             x2, x2, HEAP, lsl #32
    // 0xaaa13c: LoadField: r1 = r2->field_f
    //     0xaaa13c: ldur            w1, [x2, #0xf]
    // 0xaaa140: DecompressPointer r1
    //     0xaaa140: add             x1, x1, HEAP, lsl #32
    // 0xaaa144: ldr             x2, [SP]
    // 0xaaa148: LoadField: r0 = r2->field_7
    //     0xaaa148: ldur            w0, [x2, #7]
    // 0xaaa14c: DecompressPointer r0
    //     0xaaa14c: add             x0, x0, HEAP, lsl #32
    // 0xaaa150: StoreField: r1->field_2b = r0
    //     0xaaa150: stur            w0, [x1, #0x2b]
    //     0xaaa154: ldurb           w16, [x1, #-1]
    //     0xaaa158: ldurb           w17, [x0, #-1]
    //     0xaaa15c: and             x16, x17, x16, lsr #2
    //     0xaaa160: tst             x16, HEAP, lsr #32
    //     0xaaa164: b.eq            #0xaaa174
    //     0xaaa168: str             lr, [SP, #-8]!
    //     0xaaa16c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    //     0xaaa170: ldr             lr, [SP], #8
    // 0xaaa174: r0 = Null
    //     0xaaa174: mov             x0, NULL
    // 0xaaa178: ret
    //     0xaaa178: ret             
  }
  [closure] Container <anonymous closure>(dynamic, int) {
    // ** addr: 0xaaa17c, size: 0x19c
    // 0xaaa17c: EnterFrame
    //     0xaaa17c: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa180: mov             fp, SP
    // 0xaaa184: AllocStack(0x40)
    //     0xaaa184: sub             SP, SP, #0x40
    // 0xaaa188: SetupParameters()
    //     0xaaa188: ldr             x0, [fp, #0x18]
    //     0xaaa18c: ldur            w2, [x0, #0x17]
    //     0xaaa190: add             x2, x2, HEAP, lsl #32
    //     0xaaa194: stur            x2, [fp, #-8]
    // 0xaaa198: CheckStackOverflow
    //     0xaaa198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa19c: cmp             SP, x16
    //     0xaaa1a0: b.ls            #0xaaa2f0
    // 0xaaa1a4: LoadField: r1 = r2->field_13
    //     0xaaa1a4: ldur            w1, [x2, #0x13]
    // 0xaaa1a8: DecompressPointer r1
    //     0xaaa1a8: add             x1, x1, HEAP, lsl #32
    // 0xaaa1ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaaa1ac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaaa1b0: r0 = _of()
    //     0xaaa1b0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaaa1b4: LoadField: r1 = r0->field_7
    //     0xaaa1b4: ldur            w1, [x0, #7]
    // 0xaaa1b8: DecompressPointer r1
    //     0xaaa1b8: add             x1, x1, HEAP, lsl #32
    // 0xaaa1bc: LoadField: d0 = r1->field_7
    //     0xaaa1bc: ldur            d0, [x1, #7]
    // 0xaaa1c0: d1 = 0.950000
    //     0xaaa1c0: add             x17, PP, #0x51, lsl #12  ; [pp+0x51e58] IMM: double(0.95) from 0x3fee666666666666
    //     0xaaa1c4: ldr             d1, [x17, #0xe58]
    // 0xaaa1c8: fmul            d2, d0, d1
    // 0xaaa1cc: ldur            x0, [fp, #-8]
    // 0xaaa1d0: LoadField: r1 = r0->field_f
    //     0xaaa1d0: ldur            w1, [x0, #0xf]
    // 0xaaa1d4: DecompressPointer r1
    //     0xaaa1d4: add             x1, x1, HEAP, lsl #32
    // 0xaaa1d8: LoadField: r0 = r1->field_b
    //     0xaaa1d8: ldur            w0, [x1, #0xb]
    // 0xaaa1dc: DecompressPointer r0
    //     0xaaa1dc: add             x0, x0, HEAP, lsl #32
    // 0xaaa1e0: cmp             w0, NULL
    // 0xaaa1e4: b.eq            #0xaaa2f8
    // 0xaaa1e8: LoadField: r2 = r0->field_b
    //     0xaaa1e8: ldur            w2, [x0, #0xb]
    // 0xaaa1ec: DecompressPointer r2
    //     0xaaa1ec: add             x2, x2, HEAP, lsl #32
    // 0xaaa1f0: cmp             w2, NULL
    // 0xaaa1f4: b.ne            #0xaaa200
    // 0xaaa1f8: r0 = Null
    //     0xaaa1f8: mov             x0, NULL
    // 0xaaa1fc: b               #0xaaa210
    // 0xaaa200: LoadField: r0 = r2->field_1b
    //     0xaaa200: ldur            w0, [x2, #0x1b]
    // 0xaaa204: DecompressPointer r0
    //     0xaaa204: add             x0, x0, HEAP, lsl #32
    // 0xaaa208: LoadField: r2 = r0->field_b
    //     0xaaa208: ldur            w2, [x0, #0xb]
    // 0xaaa20c: mov             x0, x2
    // 0xaaa210: cmp             w0, NULL
    // 0xaaa214: b.ne            #0xaaa220
    // 0xaaa218: r2 = 0
    //     0xaaa218: movz            x2, #0
    // 0xaaa21c: b               #0xaaa224
    // 0xaaa220: r2 = LoadInt32Instr(r0)
    //     0xaaa220: sbfx            x2, x0, #1, #0x1f
    // 0xaaa224: ldr             x0, [fp, #0x10]
    // 0xaaa228: d0 = 2.000000
    //     0xaaa228: fmov            d0, #2.00000000
    // 0xaaa22c: lsl             x3, x2, #1
    // 0xaaa230: r16 = LoadInt32Instr(r3)
    //     0xaaa230: sbfx            x16, x3, #1, #0x1f
    // 0xaaa234: scvtf           d1, w16
    // 0xaaa238: fdiv            d3, d2, d1
    // 0xaaa23c: fsub            d1, d3, d0
    // 0xaaa240: stur            d1, [fp, #-0x20]
    // 0xaaa244: LoadField: r2 = r1->field_13
    //     0xaaa244: ldur            x2, [x1, #0x13]
    // 0xaaa248: r1 = LoadInt32Instr(r0)
    //     0xaaa248: sbfx            x1, x0, #1, #0x1f
    //     0xaaa24c: tbz             w0, #0, #0xaaa254
    //     0xaaa250: ldur            x1, [x0, #7]
    // 0xaaa254: cmp             x2, x1
    // 0xaaa258: b.ne            #0xaaa268
    // 0xaaa25c: mov             v0.16b, v1.16b
    // 0xaaa260: r0 = Instance_Color
    //     0xaaa260: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaaa264: b               #0xaaa278
    // 0xaaa268: r1 = Instance_Color
    //     0xaaa268: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaaa26c: d0 = 0.400000
    //     0xaaa26c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaaa270: r0 = withOpacity()
    //     0xaaa270: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaaa274: ldur            d0, [fp, #-0x20]
    // 0xaaa278: stur            x0, [fp, #-0x10]
    // 0xaaa27c: r1 = inline_Allocate_Double()
    //     0xaaa27c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xaaa280: add             x1, x1, #0x10
    //     0xaaa284: cmp             x2, x1
    //     0xaaa288: b.ls            #0xaaa2fc
    //     0xaaa28c: str             x1, [THR, #0x50]  ; THR::top
    //     0xaaa290: sub             x1, x1, #0xf
    //     0xaaa294: movz            x2, #0xe15c
    //     0xaaa298: movk            x2, #0x3, lsl #16
    //     0xaaa29c: stur            x2, [x1, #-1]
    // 0xaaa2a0: StoreField: r1->field_7 = d0
    //     0xaaa2a0: stur            d0, [x1, #7]
    // 0xaaa2a4: stur            x1, [fp, #-8]
    // 0xaaa2a8: r0 = Container()
    //     0xaaa2a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaaa2ac: stur            x0, [fp, #-0x18]
    // 0xaaa2b0: r16 = 2.000000
    //     0xaaa2b0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xaaa2b4: ldr             x16, [x16, #0xdf8]
    // 0xaaa2b8: ldur            lr, [fp, #-8]
    // 0xaaa2bc: stp             lr, x16, [SP, #0x10]
    // 0xaaa2c0: ldur            x16, [fp, #-0x10]
    // 0xaaa2c4: r30 = Instance_EdgeInsets
    //     0xaaa2c4: add             lr, PP, #0x51, lsl #12  ; [pp+0x51ef0] Obj!EdgeInsets@d57831
    //     0xaaa2c8: ldr             lr, [lr, #0xef0]
    // 0xaaa2cc: stp             lr, x16, [SP]
    // 0xaaa2d0: mov             x1, x0
    // 0xaaa2d4: r4 = const [0, 0x5, 0x4, 0x1, color, 0x3, height, 0x1, margin, 0x4, width, 0x2, null]
    //     0xaaa2d4: add             x4, PP, #0x51, lsl #12  ; [pp+0x51ef8] List(13) [0, 0x5, 0x4, 0x1, "color", 0x3, "height", 0x1, "margin", 0x4, "width", 0x2, Null]
    //     0xaaa2d8: ldr             x4, [x4, #0xef8]
    // 0xaaa2dc: r0 = Container()
    //     0xaaa2dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaaa2e0: ldur            x0, [fp, #-0x18]
    // 0xaaa2e4: LeaveFrame
    //     0xaaa2e4: mov             SP, fp
    //     0xaaa2e8: ldp             fp, lr, [SP], #0x10
    // 0xaaa2ec: ret
    //     0xaaa2ec: ret             
    // 0xaaa2f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa2f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa2f4: b               #0xaaa1a4
    // 0xaaa2f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaaa2f8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xaaa2fc: SaveReg d0
    //     0xaaa2fc: str             q0, [SP, #-0x10]!
    // 0xaaa300: SaveReg r0
    //     0xaaa300: str             x0, [SP, #-8]!
    // 0xaaa304: r0 = AllocateDouble()
    //     0xaaa304: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaaa308: mov             x1, x0
    // 0xaaa30c: RestoreReg r0
    //     0xaaa30c: ldr             x0, [SP], #8
    // 0xaaa310: RestoreReg d0
    //     0xaaa310: ldr             q0, [SP], #0x10
    // 0xaaa314: b               #0xaaa2a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaaa7d0, size: 0x68
    // 0xaaa7d0: EnterFrame
    //     0xaaa7d0: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa7d4: mov             fp, SP
    // 0xaaa7d8: ldr             x0, [fp, #0x10]
    // 0xaaa7dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaaa7dc: ldur            w1, [x0, #0x17]
    // 0xaaa7e0: DecompressPointer r1
    //     0xaaa7e0: add             x1, x1, HEAP, lsl #32
    // 0xaaa7e4: CheckStackOverflow
    //     0xaaa7e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa7e8: cmp             SP, x16
    //     0xaaa7ec: b.ls            #0xaaa830
    // 0xaaa7f0: LoadField: r0 = r1->field_f
    //     0xaaa7f0: ldur            w0, [x1, #0xf]
    // 0xaaa7f4: DecompressPointer r0
    //     0xaaa7f4: add             x0, x0, HEAP, lsl #32
    // 0xaaa7f8: LoadField: r2 = r1->field_13
    //     0xaaa7f8: ldur            w2, [x1, #0x13]
    // 0xaaa7fc: DecompressPointer r2
    //     0xaaa7fc: add             x2, x2, HEAP, lsl #32
    // 0xaaa800: r1 = LoadInt32Instr(r2)
    //     0xaaa800: sbfx            x1, x2, #1, #0x1f
    //     0xaaa804: tbz             w2, #0, #0xaaa80c
    //     0xaaa808: ldur            x1, [x2, #7]
    // 0xaaa80c: StoreField: r0->field_13 = r1
    //     0xaaa80c: stur            x1, [x0, #0x13]
    // 0xaaa810: LoadField: r1 = r0->field_1f
    //     0xaaa810: ldur            w1, [x0, #0x1f]
    // 0xaaa814: DecompressPointer r1
    //     0xaaa814: add             x1, x1, HEAP, lsl #32
    // 0xaaa818: r2 = true
    //     0xaaa818: add             x2, NULL, #0x20  ; true
    // 0xaaa81c: r0 = value=()
    //     0xaaa81c: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xaaa820: r0 = Null
    //     0xaaa820: mov             x0, NULL
    // 0xaaa824: LeaveFrame
    //     0xaaa824: mov             SP, fp
    //     0xaaa828: ldp             fp, lr, [SP], #0x10
    // 0xaaa82c: ret
    //     0xaaa82c: ret             
    // 0xaaa830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa830: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa834: b               #0xaaa7f0
  }
  _ _onPageChanged(/* No info */) {
    // ** addr: 0xaaa838, size: 0x70
    // 0xaaa838: EnterFrame
    //     0xaaa838: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa83c: mov             fp, SP
    // 0xaaa840: AllocStack(0x10)
    //     0xaaa840: sub             SP, SP, #0x10
    // 0xaaa844: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaaa844: stur            x1, [fp, #-8]
    //     0xaaa848: stur            x2, [fp, #-0x10]
    // 0xaaa84c: CheckStackOverflow
    //     0xaaa84c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa850: cmp             SP, x16
    //     0xaaa854: b.ls            #0xaaa8a0
    // 0xaaa858: r1 = 2
    //     0xaaa858: movz            x1, #0x2
    // 0xaaa85c: r0 = AllocateContext()
    //     0xaaa85c: bl              #0x16f6108  ; AllocateContextStub
    // 0xaaa860: mov             x1, x0
    // 0xaaa864: ldur            x0, [fp, #-8]
    // 0xaaa868: StoreField: r1->field_f = r0
    //     0xaaa868: stur            w0, [x1, #0xf]
    // 0xaaa86c: ldur            x2, [fp, #-0x10]
    // 0xaaa870: StoreField: r1->field_13 = r2
    //     0xaaa870: stur            w2, [x1, #0x13]
    // 0xaaa874: mov             x2, x1
    // 0xaaa878: r1 = Function '<anonymous closure>':.
    //     0xaaa878: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f58] AnonymousClosure: (0xaaa7d0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged (0xaaa838)
    //     0xaaa87c: ldr             x1, [x1, #0xf58]
    // 0xaaa880: r0 = AllocateClosure()
    //     0xaaa880: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaaa884: ldur            x1, [fp, #-8]
    // 0xaaa888: mov             x2, x0
    // 0xaaa88c: r0 = setState()
    //     0xaaa88c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaaa890: r0 = Null
    //     0xaaa890: mov             x0, NULL
    // 0xaaa894: LeaveFrame
    //     0xaaa894: mov             SP, fp
    //     0xaaa898: ldp             fp, lr, [SP], #0x10
    // 0xaaa89c: ret
    //     0xaaa89c: ret             
    // 0xaaa8a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa8a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa8a4: b               #0xaaa858
  }
  [closure] void _onPageChanged(dynamic, int) {
    // ** addr: 0xaaa8a8, size: 0x3c
    // 0xaaa8a8: EnterFrame
    //     0xaaa8a8: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa8ac: mov             fp, SP
    // 0xaaa8b0: ldr             x0, [fp, #0x18]
    // 0xaaa8b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaaa8b4: ldur            w1, [x0, #0x17]
    // 0xaaa8b8: DecompressPointer r1
    //     0xaaa8b8: add             x1, x1, HEAP, lsl #32
    // 0xaaa8bc: CheckStackOverflow
    //     0xaaa8bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa8c0: cmp             SP, x16
    //     0xaaa8c4: b.ls            #0xaaa8dc
    // 0xaaa8c8: ldr             x2, [fp, #0x10]
    // 0xaaa8cc: r0 = _onPageChanged()
    //     0xaaa8cc: bl              #0xaaa838  ; [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged
    // 0xaaa8d0: LeaveFrame
    //     0xaaa8d0: mov             SP, fp
    //     0xaaa8d4: ldp             fp, lr, [SP], #0x10
    // 0xaaa8d8: ret
    //     0xaaa8d8: ret             
    // 0xaaa8dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa8dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa8e0: b               #0xaaa8c8
  }
  _ build(/* No info */) {
    // ** addr: 0xc14408, size: 0x22c4
    // 0xc14408: EnterFrame
    //     0xc14408: stp             fp, lr, [SP, #-0x10]!
    //     0xc1440c: mov             fp, SP
    // 0xc14410: AllocStack(0x80)
    //     0xc14410: sub             SP, SP, #0x80
    // 0xc14414: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc14414: mov             x0, x1
    //     0xc14418: stur            x1, [fp, #-8]
    //     0xc1441c: stur            x2, [fp, #-0x10]
    // 0xc14420: CheckStackOverflow
    //     0xc14420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14424: cmp             SP, x16
    //     0xc14428: b.ls            #0xc165f0
    // 0xc1442c: r1 = 2
    //     0xc1442c: movz            x1, #0x2
    // 0xc14430: r0 = AllocateContext()
    //     0xc14430: bl              #0x16f6108  ; AllocateContextStub
    // 0xc14434: mov             x3, x0
    // 0xc14438: ldur            x0, [fp, #-8]
    // 0xc1443c: stur            x3, [fp, #-0x20]
    // 0xc14440: StoreField: r3->field_f = r0
    //     0xc14440: stur            w0, [x3, #0xf]
    // 0xc14444: ldur            x1, [fp, #-0x10]
    // 0xc14448: StoreField: r3->field_13 = r1
    //     0xc14448: stur            w1, [x3, #0x13]
    // 0xc1444c: LoadField: r1 = r0->field_b
    //     0xc1444c: ldur            w1, [x0, #0xb]
    // 0xc14450: DecompressPointer r1
    //     0xc14450: add             x1, x1, HEAP, lsl #32
    // 0xc14454: cmp             w1, NULL
    // 0xc14458: b.eq            #0xc165f8
    // 0xc1445c: LoadField: r2 = r1->field_b
    //     0xc1445c: ldur            w2, [x1, #0xb]
    // 0xc14460: DecompressPointer r2
    //     0xc14460: add             x2, x2, HEAP, lsl #32
    // 0xc14464: cmp             w2, NULL
    // 0xc14468: b.ne            #0xc14474
    // 0xc1446c: r4 = Null
    //     0xc1446c: mov             x4, NULL
    // 0xc14470: b               #0xc14484
    // 0xc14474: LoadField: r1 = r2->field_1b
    //     0xc14474: ldur            w1, [x2, #0x1b]
    // 0xc14478: DecompressPointer r1
    //     0xc14478: add             x1, x1, HEAP, lsl #32
    // 0xc1447c: LoadField: r2 = r1->field_b
    //     0xc1447c: ldur            w2, [x1, #0xb]
    // 0xc14480: mov             x4, x2
    // 0xc14484: stur            x4, [fp, #-0x18]
    // 0xc14488: LoadField: r5 = r0->field_1b
    //     0xc14488: ldur            w5, [x0, #0x1b]
    // 0xc1448c: DecompressPointer r5
    //     0xc1448c: add             x5, x5, HEAP, lsl #32
    // 0xc14490: r16 = Sentinel
    //     0xc14490: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc14494: cmp             w5, w16
    // 0xc14498: b.eq            #0xc165fc
    // 0xc1449c: mov             x2, x0
    // 0xc144a0: stur            x5, [fp, #-0x10]
    // 0xc144a4: r1 = Function '_onPageChanged@1750089873':.
    //     0xc144a4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e30] AnonymousClosure: (0xaaa8a8), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged (0xaaa838)
    //     0xc144a8: ldr             x1, [x1, #0xe30]
    // 0xc144ac: r0 = AllocateClosure()
    //     0xc144ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc144b0: ldur            x2, [fp, #-0x20]
    // 0xc144b4: r1 = Function '<anonymous closure>':.
    //     0xc144b4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e38] AnonymousClosure: (0xc168ac), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xc144b8: ldr             x1, [x1, #0xe38]
    // 0xc144bc: stur            x0, [fp, #-0x28]
    // 0xc144c0: r0 = AllocateClosure()
    //     0xc144c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc144c4: stur            x0, [fp, #-0x30]
    // 0xc144c8: r0 = PageView()
    //     0xc144c8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xc144cc: stur            x0, [fp, #-0x38]
    // 0xc144d0: r16 = Instance_NeverScrollableScrollPhysics
    //     0xc144d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc144d4: ldr             x16, [x16, #0x1c8]
    // 0xc144d8: ldur            lr, [fp, #-0x10]
    // 0xc144dc: stp             lr, x16, [SP]
    // 0xc144e0: mov             x1, x0
    // 0xc144e4: ldur            x2, [fp, #-0x30]
    // 0xc144e8: ldur            x3, [fp, #-0x18]
    // 0xc144ec: ldur            x5, [fp, #-0x28]
    // 0xc144f0: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xc144f0: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xc144f4: ldr             x4, [x4, #0xe40]
    // 0xc144f8: r0 = PageView.builder()
    //     0xc144f8: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xc144fc: r1 = Null
    //     0xc144fc: mov             x1, NULL
    // 0xc14500: r2 = 2
    //     0xc14500: movz            x2, #0x2
    // 0xc14504: r0 = AllocateArray()
    //     0xc14504: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc14508: mov             x2, x0
    // 0xc1450c: ldur            x0, [fp, #-0x38]
    // 0xc14510: stur            x2, [fp, #-0x10]
    // 0xc14514: StoreField: r2->field_f = r0
    //     0xc14514: stur            w0, [x2, #0xf]
    // 0xc14518: r1 = <Widget>
    //     0xc14518: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc1451c: r0 = AllocateGrowableArray()
    //     0xc1451c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc14520: mov             x2, x0
    // 0xc14524: ldur            x0, [fp, #-0x10]
    // 0xc14528: stur            x2, [fp, #-0x18]
    // 0xc1452c: StoreField: r2->field_f = r0
    //     0xc1452c: stur            w0, [x2, #0xf]
    // 0xc14530: r0 = 2
    //     0xc14530: movz            x0, #0x2
    // 0xc14534: StoreField: r2->field_b = r0
    //     0xc14534: stur            w0, [x2, #0xb]
    // 0xc14538: ldur            x0, [fp, #-8]
    // 0xc1453c: LoadField: r1 = r0->field_13
    //     0xc1453c: ldur            x1, [x0, #0x13]
    // 0xc14540: cmp             x1, #0
    // 0xc14544: b.le            #0xc146a0
    // 0xc14548: ldur            x3, [fp, #-0x20]
    // 0xc1454c: LoadField: r1 = r3->field_13
    //     0xc1454c: ldur            w1, [x3, #0x13]
    // 0xc14550: DecompressPointer r1
    //     0xc14550: add             x1, x1, HEAP, lsl #32
    // 0xc14554: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc14554: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc14558: r0 = _of()
    //     0xc14558: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc1455c: LoadField: r1 = r0->field_7
    //     0xc1455c: ldur            w1, [x0, #7]
    // 0xc14560: DecompressPointer r1
    //     0xc14560: add             x1, x1, HEAP, lsl #32
    // 0xc14564: LoadField: d0 = r1->field_7
    //     0xc14564: ldur            d0, [x1, #7]
    // 0xc14568: d1 = 0.300000
    //     0xc14568: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xc1456c: ldr             d1, [x17, #0x658]
    // 0xc14570: fmul            d2, d0, d1
    // 0xc14574: stur            d2, [fp, #-0x58]
    // 0xc14578: r0 = Container()
    //     0xc14578: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1457c: stur            x0, [fp, #-0x10]
    // 0xc14580: r16 = Instance_Color
    //     0xc14580: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xc14584: ldr             x16, [x16, #0xf88]
    // 0xc14588: str             x16, [SP]
    // 0xc1458c: mov             x1, x0
    // 0xc14590: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xc14590: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xc14594: ldr             x4, [x4, #0xf40]
    // 0xc14598: r0 = Container()
    //     0xc14598: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc1459c: r0 = GestureDetector()
    //     0xc1459c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc145a0: ldur            x2, [fp, #-0x20]
    // 0xc145a4: r1 = Function '<anonymous closure>':.
    //     0xc145a4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e48] AnonymousClosure: (0xc1683c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xc145a8: ldr             x1, [x1, #0xe48]
    // 0xc145ac: stur            x0, [fp, #-0x28]
    // 0xc145b0: r0 = AllocateClosure()
    //     0xc145b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc145b4: ldur            x16, [fp, #-0x10]
    // 0xc145b8: stp             x16, x0, [SP]
    // 0xc145bc: ldur            x1, [fp, #-0x28]
    // 0xc145c0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc145c0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc145c4: ldr             x4, [x4, #0xaf0]
    // 0xc145c8: r0 = GestureDetector()
    //     0xc145c8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc145cc: r1 = <StackParentData>
    //     0xc145cc: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc145d0: ldr             x1, [x1, #0x8e0]
    // 0xc145d4: r0 = Positioned()
    //     0xc145d4: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc145d8: mov             x2, x0
    // 0xc145dc: r0 = 0.000000
    //     0xc145dc: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc145e0: stur            x2, [fp, #-0x10]
    // 0xc145e4: StoreField: r2->field_13 = r0
    //     0xc145e4: stur            w0, [x2, #0x13]
    // 0xc145e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xc145e8: stur            w0, [x2, #0x17]
    // 0xc145ec: StoreField: r2->field_1f = r0
    //     0xc145ec: stur            w0, [x2, #0x1f]
    // 0xc145f0: ldur            d0, [fp, #-0x58]
    // 0xc145f4: r1 = inline_Allocate_Double()
    //     0xc145f4: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xc145f8: add             x1, x1, #0x10
    //     0xc145fc: cmp             x3, x1
    //     0xc14600: b.ls            #0xc16608
    //     0xc14604: str             x1, [THR, #0x50]  ; THR::top
    //     0xc14608: sub             x1, x1, #0xf
    //     0xc1460c: movz            x3, #0xe15c
    //     0xc14610: movk            x3, #0x3, lsl #16
    //     0xc14614: stur            x3, [x1, #-1]
    // 0xc14618: StoreField: r1->field_7 = d0
    //     0xc14618: stur            d0, [x1, #7]
    // 0xc1461c: StoreField: r2->field_23 = r1
    //     0xc1461c: stur            w1, [x2, #0x23]
    // 0xc14620: ldur            x1, [fp, #-0x28]
    // 0xc14624: StoreField: r2->field_b = r1
    //     0xc14624: stur            w1, [x2, #0xb]
    // 0xc14628: ldur            x3, [fp, #-0x18]
    // 0xc1462c: LoadField: r1 = r3->field_b
    //     0xc1462c: ldur            w1, [x3, #0xb]
    // 0xc14630: LoadField: r4 = r3->field_f
    //     0xc14630: ldur            w4, [x3, #0xf]
    // 0xc14634: DecompressPointer r4
    //     0xc14634: add             x4, x4, HEAP, lsl #32
    // 0xc14638: LoadField: r5 = r4->field_b
    //     0xc14638: ldur            w5, [x4, #0xb]
    // 0xc1463c: r4 = LoadInt32Instr(r1)
    //     0xc1463c: sbfx            x4, x1, #1, #0x1f
    // 0xc14640: stur            x4, [fp, #-0x40]
    // 0xc14644: r1 = LoadInt32Instr(r5)
    //     0xc14644: sbfx            x1, x5, #1, #0x1f
    // 0xc14648: cmp             x4, x1
    // 0xc1464c: b.ne            #0xc14658
    // 0xc14650: mov             x1, x3
    // 0xc14654: r0 = _growToNextCapacity()
    //     0xc14654: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc14658: ldur            x2, [fp, #-0x18]
    // 0xc1465c: ldur            x3, [fp, #-0x40]
    // 0xc14660: add             x0, x3, #1
    // 0xc14664: lsl             x1, x0, #1
    // 0xc14668: StoreField: r2->field_b = r1
    //     0xc14668: stur            w1, [x2, #0xb]
    // 0xc1466c: LoadField: r1 = r2->field_f
    //     0xc1466c: ldur            w1, [x2, #0xf]
    // 0xc14670: DecompressPointer r1
    //     0xc14670: add             x1, x1, HEAP, lsl #32
    // 0xc14674: ldur            x0, [fp, #-0x10]
    // 0xc14678: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc14678: add             x25, x1, x3, lsl #2
    //     0xc1467c: add             x25, x25, #0xf
    //     0xc14680: str             w0, [x25]
    //     0xc14684: tbz             w0, #0, #0xc146a0
    //     0xc14688: ldurb           w16, [x1, #-1]
    //     0xc1468c: ldurb           w17, [x0, #-1]
    //     0xc14690: and             x16, x17, x16, lsr #2
    //     0xc14694: tst             x16, HEAP, lsr #32
    //     0xc14698: b.eq            #0xc146a0
    //     0xc1469c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc146a0: ldur            x0, [fp, #-8]
    // 0xc146a4: LoadField: r1 = r0->field_13
    //     0xc146a4: ldur            x1, [x0, #0x13]
    // 0xc146a8: LoadField: r3 = r0->field_b
    //     0xc146a8: ldur            w3, [x0, #0xb]
    // 0xc146ac: DecompressPointer r3
    //     0xc146ac: add             x3, x3, HEAP, lsl #32
    // 0xc146b0: cmp             w3, NULL
    // 0xc146b4: b.eq            #0xc16624
    // 0xc146b8: LoadField: r4 = r3->field_b
    //     0xc146b8: ldur            w4, [x3, #0xb]
    // 0xc146bc: DecompressPointer r4
    //     0xc146bc: add             x4, x4, HEAP, lsl #32
    // 0xc146c0: cmp             w4, NULL
    // 0xc146c4: b.ne            #0xc146d0
    // 0xc146c8: r3 = Null
    //     0xc146c8: mov             x3, NULL
    // 0xc146cc: b               #0xc146e0
    // 0xc146d0: LoadField: r3 = r4->field_1b
    //     0xc146d0: ldur            w3, [x4, #0x1b]
    // 0xc146d4: DecompressPointer r3
    //     0xc146d4: add             x3, x3, HEAP, lsl #32
    // 0xc146d8: LoadField: r4 = r3->field_b
    //     0xc146d8: ldur            w4, [x3, #0xb]
    // 0xc146dc: mov             x3, x4
    // 0xc146e0: cmp             w3, NULL
    // 0xc146e4: b.ne            #0xc146f0
    // 0xc146e8: r3 = 0
    //     0xc146e8: movz            x3, #0
    // 0xc146ec: b               #0xc146f8
    // 0xc146f0: r4 = LoadInt32Instr(r3)
    //     0xc146f0: sbfx            x4, x3, #1, #0x1f
    // 0xc146f4: mov             x3, x4
    // 0xc146f8: sub             x4, x3, #1
    // 0xc146fc: cmp             x1, x4
    // 0xc14700: b.ge            #0xc1485c
    // 0xc14704: ldur            x3, [fp, #-0x20]
    // 0xc14708: LoadField: r1 = r3->field_13
    //     0xc14708: ldur            w1, [x3, #0x13]
    // 0xc1470c: DecompressPointer r1
    //     0xc1470c: add             x1, x1, HEAP, lsl #32
    // 0xc14710: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc14710: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc14714: r0 = _of()
    //     0xc14714: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc14718: LoadField: r1 = r0->field_7
    //     0xc14718: ldur            w1, [x0, #7]
    // 0xc1471c: DecompressPointer r1
    //     0xc1471c: add             x1, x1, HEAP, lsl #32
    // 0xc14720: LoadField: d0 = r1->field_7
    //     0xc14720: ldur            d0, [x1, #7]
    // 0xc14724: d1 = 0.300000
    //     0xc14724: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xc14728: ldr             d1, [x17, #0x658]
    // 0xc1472c: fmul            d2, d0, d1
    // 0xc14730: stur            d2, [fp, #-0x58]
    // 0xc14734: r0 = Container()
    //     0xc14734: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc14738: stur            x0, [fp, #-0x10]
    // 0xc1473c: r16 = Instance_Color
    //     0xc1473c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xc14740: ldr             x16, [x16, #0xf88]
    // 0xc14744: str             x16, [SP]
    // 0xc14748: mov             x1, x0
    // 0xc1474c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xc1474c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xc14750: ldr             x4, [x4, #0xf40]
    // 0xc14754: r0 = Container()
    //     0xc14754: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc14758: r0 = GestureDetector()
    //     0xc14758: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc1475c: ldur            x2, [fp, #-0x20]
    // 0xc14760: r1 = Function '<anonymous closure>':.
    //     0xc14760: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e50] AnonymousClosure: (0xc16774), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xc14764: ldr             x1, [x1, #0xe50]
    // 0xc14768: stur            x0, [fp, #-0x28]
    // 0xc1476c: r0 = AllocateClosure()
    //     0xc1476c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc14770: ldur            x16, [fp, #-0x10]
    // 0xc14774: stp             x16, x0, [SP]
    // 0xc14778: ldur            x1, [fp, #-0x28]
    // 0xc1477c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc1477c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc14780: ldr             x4, [x4, #0xaf0]
    // 0xc14784: r0 = GestureDetector()
    //     0xc14784: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc14788: r1 = <StackParentData>
    //     0xc14788: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc1478c: ldr             x1, [x1, #0x8e0]
    // 0xc14790: r0 = Positioned()
    //     0xc14790: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc14794: mov             x2, x0
    // 0xc14798: r0 = 0.000000
    //     0xc14798: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc1479c: stur            x2, [fp, #-0x10]
    // 0xc147a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xc147a0: stur            w0, [x2, #0x17]
    // 0xc147a4: StoreField: r2->field_1b = r0
    //     0xc147a4: stur            w0, [x2, #0x1b]
    // 0xc147a8: StoreField: r2->field_1f = r0
    //     0xc147a8: stur            w0, [x2, #0x1f]
    // 0xc147ac: ldur            d0, [fp, #-0x58]
    // 0xc147b0: r1 = inline_Allocate_Double()
    //     0xc147b0: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xc147b4: add             x1, x1, #0x10
    //     0xc147b8: cmp             x3, x1
    //     0xc147bc: b.ls            #0xc16628
    //     0xc147c0: str             x1, [THR, #0x50]  ; THR::top
    //     0xc147c4: sub             x1, x1, #0xf
    //     0xc147c8: movz            x3, #0xe15c
    //     0xc147cc: movk            x3, #0x3, lsl #16
    //     0xc147d0: stur            x3, [x1, #-1]
    // 0xc147d4: StoreField: r1->field_7 = d0
    //     0xc147d4: stur            d0, [x1, #7]
    // 0xc147d8: StoreField: r2->field_23 = r1
    //     0xc147d8: stur            w1, [x2, #0x23]
    // 0xc147dc: ldur            x1, [fp, #-0x28]
    // 0xc147e0: StoreField: r2->field_b = r1
    //     0xc147e0: stur            w1, [x2, #0xb]
    // 0xc147e4: ldur            x3, [fp, #-0x18]
    // 0xc147e8: LoadField: r1 = r3->field_b
    //     0xc147e8: ldur            w1, [x3, #0xb]
    // 0xc147ec: LoadField: r4 = r3->field_f
    //     0xc147ec: ldur            w4, [x3, #0xf]
    // 0xc147f0: DecompressPointer r4
    //     0xc147f0: add             x4, x4, HEAP, lsl #32
    // 0xc147f4: LoadField: r5 = r4->field_b
    //     0xc147f4: ldur            w5, [x4, #0xb]
    // 0xc147f8: r4 = LoadInt32Instr(r1)
    //     0xc147f8: sbfx            x4, x1, #1, #0x1f
    // 0xc147fc: stur            x4, [fp, #-0x40]
    // 0xc14800: r1 = LoadInt32Instr(r5)
    //     0xc14800: sbfx            x1, x5, #1, #0x1f
    // 0xc14804: cmp             x4, x1
    // 0xc14808: b.ne            #0xc14814
    // 0xc1480c: mov             x1, x3
    // 0xc14810: r0 = _growToNextCapacity()
    //     0xc14810: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc14814: ldur            x2, [fp, #-0x18]
    // 0xc14818: ldur            x3, [fp, #-0x40]
    // 0xc1481c: add             x0, x3, #1
    // 0xc14820: lsl             x1, x0, #1
    // 0xc14824: StoreField: r2->field_b = r1
    //     0xc14824: stur            w1, [x2, #0xb]
    // 0xc14828: LoadField: r1 = r2->field_f
    //     0xc14828: ldur            w1, [x2, #0xf]
    // 0xc1482c: DecompressPointer r1
    //     0xc1482c: add             x1, x1, HEAP, lsl #32
    // 0xc14830: ldur            x0, [fp, #-0x10]
    // 0xc14834: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc14834: add             x25, x1, x3, lsl #2
    //     0xc14838: add             x25, x25, #0xf
    //     0xc1483c: str             w0, [x25]
    //     0xc14840: tbz             w0, #0, #0xc1485c
    //     0xc14844: ldurb           w16, [x1, #-1]
    //     0xc14848: ldurb           w17, [x0, #-1]
    //     0xc1484c: and             x16, x17, x16, lsr #2
    //     0xc14850: tst             x16, HEAP, lsr #32
    //     0xc14854: b.eq            #0xc1485c
    //     0xc14858: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc1485c: ldur            x0, [fp, #-8]
    // 0xc14860: ldur            x3, [fp, #-0x20]
    // 0xc14864: LoadField: r1 = r3->field_13
    //     0xc14864: ldur            w1, [x3, #0x13]
    // 0xc14868: DecompressPointer r1
    //     0xc14868: add             x1, x1, HEAP, lsl #32
    // 0xc1486c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1486c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc14870: r0 = _of()
    //     0xc14870: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc14874: LoadField: r1 = r0->field_7
    //     0xc14874: ldur            w1, [x0, #7]
    // 0xc14878: DecompressPointer r1
    //     0xc14878: add             x1, x1, HEAP, lsl #32
    // 0xc1487c: LoadField: d0 = r1->field_7
    //     0xc1487c: ldur            d0, [x1, #7]
    // 0xc14880: d1 = 0.950000
    //     0xc14880: add             x17, PP, #0x51, lsl #12  ; [pp+0x51e58] IMM: double(0.95) from 0x3fee666666666666
    //     0xc14884: ldr             d1, [x17, #0xe58]
    // 0xc14888: fmul            d2, d0, d1
    // 0xc1488c: ldur            x0, [fp, #-8]
    // 0xc14890: stur            d2, [fp, #-0x58]
    // 0xc14894: LoadField: r1 = r0->field_b
    //     0xc14894: ldur            w1, [x0, #0xb]
    // 0xc14898: DecompressPointer r1
    //     0xc14898: add             x1, x1, HEAP, lsl #32
    // 0xc1489c: cmp             w1, NULL
    // 0xc148a0: b.eq            #0xc16644
    // 0xc148a4: LoadField: r2 = r1->field_b
    //     0xc148a4: ldur            w2, [x1, #0xb]
    // 0xc148a8: DecompressPointer r2
    //     0xc148a8: add             x2, x2, HEAP, lsl #32
    // 0xc148ac: cmp             w2, NULL
    // 0xc148b0: b.ne            #0xc148bc
    // 0xc148b4: r1 = Null
    //     0xc148b4: mov             x1, NULL
    // 0xc148b8: b               #0xc148cc
    // 0xc148bc: LoadField: r1 = r2->field_1b
    //     0xc148bc: ldur            w1, [x2, #0x1b]
    // 0xc148c0: DecompressPointer r1
    //     0xc148c0: add             x1, x1, HEAP, lsl #32
    // 0xc148c4: LoadField: r2 = r1->field_b
    //     0xc148c4: ldur            w2, [x1, #0xb]
    // 0xc148c8: mov             x1, x2
    // 0xc148cc: cmp             w1, NULL
    // 0xc148d0: b.ne            #0xc148dc
    // 0xc148d4: r3 = 0
    //     0xc148d4: movz            x3, #0
    // 0xc148d8: b               #0xc148e4
    // 0xc148dc: r2 = LoadInt32Instr(r1)
    //     0xc148dc: sbfx            x2, x1, #1, #0x1f
    // 0xc148e0: mov             x3, x2
    // 0xc148e4: ldur            x2, [fp, #-0x20]
    // 0xc148e8: stur            x3, [fp, #-0x40]
    // 0xc148ec: r1 = Function '<anonymous closure>':.
    //     0xc148ec: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e60] AnonymousClosure: (0xaaa17c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xc148f0: ldr             x1, [x1, #0xe60]
    // 0xc148f4: r0 = AllocateClosure()
    //     0xc148f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc148f8: ldur            x2, [fp, #-0x40]
    // 0xc148fc: r1 = <Widget>
    //     0xc148fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc14900: stur            x0, [fp, #-0x10]
    // 0xc14904: r0 = _GrowableList()
    //     0xc14904: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc14908: mov             x1, x0
    // 0xc1490c: stur            x1, [fp, #-0x28]
    // 0xc14910: r2 = 0
    //     0xc14910: movz            x2, #0
    // 0xc14914: stur            x2, [fp, #-0x40]
    // 0xc14918: CheckStackOverflow
    //     0xc14918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1491c: cmp             SP, x16
    //     0xc14920: b.ls            #0xc16648
    // 0xc14924: LoadField: r0 = r1->field_b
    //     0xc14924: ldur            w0, [x1, #0xb]
    // 0xc14928: r3 = LoadInt32Instr(r0)
    //     0xc14928: sbfx            x3, x0, #1, #0x1f
    // 0xc1492c: cmp             x2, x3
    // 0xc14930: b.ge            #0xc149f4
    // 0xc14934: lsl             x0, x2, #1
    // 0xc14938: ldur            x16, [fp, #-0x10]
    // 0xc1493c: stp             x0, x16, [SP]
    // 0xc14940: ldur            x0, [fp, #-0x10]
    // 0xc14944: ClosureCall
    //     0xc14944: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xc14948: ldur            x2, [x0, #0x1f]
    //     0xc1494c: blr             x2
    // 0xc14950: mov             x3, x0
    // 0xc14954: r2 = Null
    //     0xc14954: mov             x2, NULL
    // 0xc14958: r1 = Null
    //     0xc14958: mov             x1, NULL
    // 0xc1495c: stur            x3, [fp, #-0x30]
    // 0xc14960: r4 = 60
    //     0xc14960: movz            x4, #0x3c
    // 0xc14964: branchIfSmi(r0, 0xc14970)
    //     0xc14964: tbz             w0, #0, #0xc14970
    // 0xc14968: r4 = LoadClassIdInstr(r0)
    //     0xc14968: ldur            x4, [x0, #-1]
    //     0xc1496c: ubfx            x4, x4, #0xc, #0x14
    // 0xc14970: sub             x4, x4, #0xe60
    // 0xc14974: cmp             x4, #0x464
    // 0xc14978: b.ls            #0xc14990
    // 0xc1497c: r8 = Widget
    //     0xc1497c: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xc14980: ldr             x8, [x8, #0xe68]
    // 0xc14984: r3 = Null
    //     0xc14984: add             x3, PP, #0x51, lsl #12  ; [pp+0x51e70] Null
    //     0xc14988: ldr             x3, [x3, #0xe70]
    // 0xc1498c: r0 = Widget()
    //     0xc1498c: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xc14990: ldur            x3, [fp, #-0x28]
    // 0xc14994: LoadField: r0 = r3->field_b
    //     0xc14994: ldur            w0, [x3, #0xb]
    // 0xc14998: r1 = LoadInt32Instr(r0)
    //     0xc14998: sbfx            x1, x0, #1, #0x1f
    // 0xc1499c: mov             x0, x1
    // 0xc149a0: ldur            x1, [fp, #-0x40]
    // 0xc149a4: cmp             x1, x0
    // 0xc149a8: b.hs            #0xc16650
    // 0xc149ac: LoadField: r1 = r3->field_f
    //     0xc149ac: ldur            w1, [x3, #0xf]
    // 0xc149b0: DecompressPointer r1
    //     0xc149b0: add             x1, x1, HEAP, lsl #32
    // 0xc149b4: ldur            x0, [fp, #-0x30]
    // 0xc149b8: ldur            x2, [fp, #-0x40]
    // 0xc149bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc149bc: add             x25, x1, x2, lsl #2
    //     0xc149c0: add             x25, x25, #0xf
    //     0xc149c4: str             w0, [x25]
    //     0xc149c8: tbz             w0, #0, #0xc149e4
    //     0xc149cc: ldurb           w16, [x1, #-1]
    //     0xc149d0: ldurb           w17, [x0, #-1]
    //     0xc149d4: and             x16, x17, x16, lsr #2
    //     0xc149d8: tst             x16, HEAP, lsr #32
    //     0xc149dc: b.eq            #0xc149e4
    //     0xc149e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc149e4: add             x0, x2, #1
    // 0xc149e8: mov             x2, x0
    // 0xc149ec: mov             x1, x3
    // 0xc149f0: b               #0xc14914
    // 0xc149f4: ldur            d0, [fp, #-0x58]
    // 0xc149f8: mov             x3, x1
    // 0xc149fc: ldur            x1, [fp, #-0x18]
    // 0xc14a00: r0 = Row()
    //     0xc14a00: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc14a04: mov             x1, x0
    // 0xc14a08: r0 = Instance_Axis
    //     0xc14a08: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc14a0c: stur            x1, [fp, #-0x30]
    // 0xc14a10: StoreField: r1->field_f = r0
    //     0xc14a10: stur            w0, [x1, #0xf]
    // 0xc14a14: r2 = Instance_MainAxisAlignment
    //     0xc14a14: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc14a18: ldr             x2, [x2, #0xab0]
    // 0xc14a1c: StoreField: r1->field_13 = r2
    //     0xc14a1c: stur            w2, [x1, #0x13]
    // 0xc14a20: r2 = Instance_MainAxisSize
    //     0xc14a20: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc14a24: ldr             x2, [x2, #0xa10]
    // 0xc14a28: ArrayStore: r1[0] = r2  ; List_4
    //     0xc14a28: stur            w2, [x1, #0x17]
    // 0xc14a2c: r3 = Instance_CrossAxisAlignment
    //     0xc14a2c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc14a30: ldr             x3, [x3, #0xa18]
    // 0xc14a34: StoreField: r1->field_1b = r3
    //     0xc14a34: stur            w3, [x1, #0x1b]
    // 0xc14a38: r4 = Instance_VerticalDirection
    //     0xc14a38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc14a3c: ldr             x4, [x4, #0xa20]
    // 0xc14a40: StoreField: r1->field_23 = r4
    //     0xc14a40: stur            w4, [x1, #0x23]
    // 0xc14a44: r5 = Instance_Clip
    //     0xc14a44: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc14a48: ldr             x5, [x5, #0x38]
    // 0xc14a4c: StoreField: r1->field_2b = r5
    //     0xc14a4c: stur            w5, [x1, #0x2b]
    // 0xc14a50: StoreField: r1->field_2f = rZR
    //     0xc14a50: stur            xzr, [x1, #0x2f]
    // 0xc14a54: ldur            x6, [fp, #-0x28]
    // 0xc14a58: StoreField: r1->field_b = r6
    //     0xc14a58: stur            w6, [x1, #0xb]
    // 0xc14a5c: ldur            d0, [fp, #-0x58]
    // 0xc14a60: r6 = inline_Allocate_Double()
    //     0xc14a60: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xc14a64: add             x6, x6, #0x10
    //     0xc14a68: cmp             x7, x6
    //     0xc14a6c: b.ls            #0xc16654
    //     0xc14a70: str             x6, [THR, #0x50]  ; THR::top
    //     0xc14a74: sub             x6, x6, #0xf
    //     0xc14a78: movz            x7, #0xe15c
    //     0xc14a7c: movk            x7, #0x3, lsl #16
    //     0xc14a80: stur            x7, [x6, #-1]
    // 0xc14a84: StoreField: r6->field_7 = d0
    //     0xc14a84: stur            d0, [x6, #7]
    // 0xc14a88: stur            x6, [fp, #-0x10]
    // 0xc14a8c: r0 = SizedBox()
    //     0xc14a8c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc14a90: mov             x2, x0
    // 0xc14a94: ldur            x0, [fp, #-0x10]
    // 0xc14a98: stur            x2, [fp, #-0x28]
    // 0xc14a9c: StoreField: r2->field_f = r0
    //     0xc14a9c: stur            w0, [x2, #0xf]
    // 0xc14aa0: ldur            x0, [fp, #-0x30]
    // 0xc14aa4: StoreField: r2->field_b = r0
    //     0xc14aa4: stur            w0, [x2, #0xb]
    // 0xc14aa8: r1 = <StackParentData>
    //     0xc14aa8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc14aac: ldr             x1, [x1, #0x8e0]
    // 0xc14ab0: r0 = Positioned()
    //     0xc14ab0: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc14ab4: mov             x2, x0
    // 0xc14ab8: r0 = 12.000000
    //     0xc14ab8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc14abc: ldr             x0, [x0, #0x9e8]
    // 0xc14ac0: stur            x2, [fp, #-0x10]
    // 0xc14ac4: ArrayStore: r2[0] = r0  ; List_4
    //     0xc14ac4: stur            w0, [x2, #0x17]
    // 0xc14ac8: ldur            x1, [fp, #-0x28]
    // 0xc14acc: StoreField: r2->field_b = r1
    //     0xc14acc: stur            w1, [x2, #0xb]
    // 0xc14ad0: ldur            x3, [fp, #-0x18]
    // 0xc14ad4: LoadField: r1 = r3->field_b
    //     0xc14ad4: ldur            w1, [x3, #0xb]
    // 0xc14ad8: LoadField: r4 = r3->field_f
    //     0xc14ad8: ldur            w4, [x3, #0xf]
    // 0xc14adc: DecompressPointer r4
    //     0xc14adc: add             x4, x4, HEAP, lsl #32
    // 0xc14ae0: LoadField: r5 = r4->field_b
    //     0xc14ae0: ldur            w5, [x4, #0xb]
    // 0xc14ae4: r4 = LoadInt32Instr(r1)
    //     0xc14ae4: sbfx            x4, x1, #1, #0x1f
    // 0xc14ae8: stur            x4, [fp, #-0x40]
    // 0xc14aec: r1 = LoadInt32Instr(r5)
    //     0xc14aec: sbfx            x1, x5, #1, #0x1f
    // 0xc14af0: cmp             x4, x1
    // 0xc14af4: b.ne            #0xc14b00
    // 0xc14af8: mov             x1, x3
    // 0xc14afc: r0 = _growToNextCapacity()
    //     0xc14afc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc14b00: ldur            x2, [fp, #-0x18]
    // 0xc14b04: ldur            x3, [fp, #-0x40]
    // 0xc14b08: add             x0, x3, #1
    // 0xc14b0c: lsl             x1, x0, #1
    // 0xc14b10: StoreField: r2->field_b = r1
    //     0xc14b10: stur            w1, [x2, #0xb]
    // 0xc14b14: LoadField: r1 = r2->field_f
    //     0xc14b14: ldur            w1, [x2, #0xf]
    // 0xc14b18: DecompressPointer r1
    //     0xc14b18: add             x1, x1, HEAP, lsl #32
    // 0xc14b1c: ldur            x0, [fp, #-0x10]
    // 0xc14b20: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc14b20: add             x25, x1, x3, lsl #2
    //     0xc14b24: add             x25, x25, #0xf
    //     0xc14b28: str             w0, [x25]
    //     0xc14b2c: tbz             w0, #0, #0xc14b48
    //     0xc14b30: ldurb           w16, [x1, #-1]
    //     0xc14b34: ldurb           w17, [x0, #-1]
    //     0xc14b38: and             x16, x17, x16, lsr #2
    //     0xc14b3c: tst             x16, HEAP, lsr #32
    //     0xc14b40: b.eq            #0xc14b48
    //     0xc14b44: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc14b48: r0 = GestureDetector()
    //     0xc14b48: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc14b4c: r1 = Function '<anonymous closure>':.
    //     0xc14b4c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e80] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc14b50: ldr             x1, [x1, #0xe80]
    // 0xc14b54: r2 = Null
    //     0xc14b54: mov             x2, NULL
    // 0xc14b58: stur            x0, [fp, #-0x10]
    // 0xc14b5c: r0 = AllocateClosure()
    //     0xc14b5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc14b60: r16 = Instance_Icon
    //     0xc14b60: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e88] Obj!Icon@d665f1
    //     0xc14b64: ldr             x16, [x16, #0xe88]
    // 0xc14b68: stp             x16, x0, [SP]
    // 0xc14b6c: ldur            x1, [fp, #-0x10]
    // 0xc14b70: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc14b70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc14b74: ldr             x4, [x4, #0xaf0]
    // 0xc14b78: r0 = GestureDetector()
    //     0xc14b78: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc14b7c: r1 = <StackParentData>
    //     0xc14b7c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc14b80: ldr             x1, [x1, #0x8e0]
    // 0xc14b84: r0 = Positioned()
    //     0xc14b84: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc14b88: mov             x2, x0
    // 0xc14b8c: r0 = 24.000000
    //     0xc14b8c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xc14b90: ldr             x0, [x0, #0xba8]
    // 0xc14b94: stur            x2, [fp, #-0x28]
    // 0xc14b98: ArrayStore: r2[0] = r0  ; List_4
    //     0xc14b98: stur            w0, [x2, #0x17]
    // 0xc14b9c: r0 = 12.000000
    //     0xc14b9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc14ba0: ldr             x0, [x0, #0x9e8]
    // 0xc14ba4: StoreField: r2->field_1b = r0
    //     0xc14ba4: stur            w0, [x2, #0x1b]
    // 0xc14ba8: ldur            x0, [fp, #-0x10]
    // 0xc14bac: StoreField: r2->field_b = r0
    //     0xc14bac: stur            w0, [x2, #0xb]
    // 0xc14bb0: ldur            x0, [fp, #-0x18]
    // 0xc14bb4: LoadField: r1 = r0->field_b
    //     0xc14bb4: ldur            w1, [x0, #0xb]
    // 0xc14bb8: LoadField: r3 = r0->field_f
    //     0xc14bb8: ldur            w3, [x0, #0xf]
    // 0xc14bbc: DecompressPointer r3
    //     0xc14bbc: add             x3, x3, HEAP, lsl #32
    // 0xc14bc0: LoadField: r4 = r3->field_b
    //     0xc14bc0: ldur            w4, [x3, #0xb]
    // 0xc14bc4: r3 = LoadInt32Instr(r1)
    //     0xc14bc4: sbfx            x3, x1, #1, #0x1f
    // 0xc14bc8: stur            x3, [fp, #-0x40]
    // 0xc14bcc: r1 = LoadInt32Instr(r4)
    //     0xc14bcc: sbfx            x1, x4, #1, #0x1f
    // 0xc14bd0: cmp             x3, x1
    // 0xc14bd4: b.ne            #0xc14be0
    // 0xc14bd8: mov             x1, x0
    // 0xc14bdc: r0 = _growToNextCapacity()
    //     0xc14bdc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc14be0: ldur            x4, [fp, #-8]
    // 0xc14be4: ldur            x2, [fp, #-0x18]
    // 0xc14be8: ldur            x3, [fp, #-0x40]
    // 0xc14bec: add             x0, x3, #1
    // 0xc14bf0: lsl             x1, x0, #1
    // 0xc14bf4: StoreField: r2->field_b = r1
    //     0xc14bf4: stur            w1, [x2, #0xb]
    // 0xc14bf8: LoadField: r1 = r2->field_f
    //     0xc14bf8: ldur            w1, [x2, #0xf]
    // 0xc14bfc: DecompressPointer r1
    //     0xc14bfc: add             x1, x1, HEAP, lsl #32
    // 0xc14c00: ldur            x0, [fp, #-0x28]
    // 0xc14c04: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc14c04: add             x25, x1, x3, lsl #2
    //     0xc14c08: add             x25, x25, #0xf
    //     0xc14c0c: str             w0, [x25]
    //     0xc14c10: tbz             w0, #0, #0xc14c2c
    //     0xc14c14: ldurb           w16, [x1, #-1]
    //     0xc14c18: ldurb           w17, [x0, #-1]
    //     0xc14c1c: and             x16, x17, x16, lsr #2
    //     0xc14c20: tst             x16, HEAP, lsr #32
    //     0xc14c24: b.eq            #0xc14c2c
    //     0xc14c28: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc14c2c: LoadField: r0 = r4->field_23
    //     0xc14c2c: ldur            w0, [x4, #0x23]
    // 0xc14c30: DecompressPointer r0
    //     0xc14c30: add             x0, x0, HEAP, lsl #32
    // 0xc14c34: tbnz            w0, #4, #0xc156b4
    // 0xc14c38: ldur            x0, [fp, #-0x20]
    // 0xc14c3c: LoadField: r1 = r0->field_13
    //     0xc14c3c: ldur            w1, [x0, #0x13]
    // 0xc14c40: DecompressPointer r1
    //     0xc14c40: add             x1, x1, HEAP, lsl #32
    // 0xc14c44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc14c44: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc14c48: r0 = _of()
    //     0xc14c48: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc14c4c: LoadField: r1 = r0->field_7
    //     0xc14c4c: ldur            w1, [x0, #7]
    // 0xc14c50: DecompressPointer r1
    //     0xc14c50: add             x1, x1, HEAP, lsl #32
    // 0xc14c54: LoadField: d1 = r1->field_7
    //     0xc14c54: ldur            d1, [x1, #7]
    // 0xc14c58: stur            d1, [fp, #-0x58]
    // 0xc14c5c: r1 = Instance_Color
    //     0xc14c5c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc14c60: d0 = 0.700000
    //     0xc14c60: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc14c64: ldr             d0, [x17, #0xf48]
    // 0xc14c68: r0 = withOpacity()
    //     0xc14c68: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc14c6c: stur            x0, [fp, #-0x10]
    // 0xc14c70: r0 = BoxDecoration()
    //     0xc14c70: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc14c74: mov             x2, x0
    // 0xc14c78: ldur            x0, [fp, #-0x10]
    // 0xc14c7c: stur            x2, [fp, #-0x28]
    // 0xc14c80: StoreField: r2->field_7 = r0
    //     0xc14c80: stur            w0, [x2, #7]
    // 0xc14c84: r0 = Instance_BorderRadius
    //     0xc14c84: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xc14c88: ldr             x0, [x0, #0xf70]
    // 0xc14c8c: StoreField: r2->field_13 = r0
    //     0xc14c8c: stur            w0, [x2, #0x13]
    // 0xc14c90: r0 = Instance_BoxShape
    //     0xc14c90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc14c94: ldr             x0, [x0, #0x80]
    // 0xc14c98: StoreField: r2->field_23 = r0
    //     0xc14c98: stur            w0, [x2, #0x23]
    // 0xc14c9c: r1 = Instance_Color
    //     0xc14c9c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc14ca0: d0 = 0.500000
    //     0xc14ca0: fmov            d0, #0.50000000
    // 0xc14ca4: r0 = withOpacity()
    //     0xc14ca4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc14ca8: stur            x0, [fp, #-0x10]
    // 0xc14cac: r0 = BoxDecoration()
    //     0xc14cac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc14cb0: mov             x1, x0
    // 0xc14cb4: ldur            x0, [fp, #-0x10]
    // 0xc14cb8: stur            x1, [fp, #-0x30]
    // 0xc14cbc: StoreField: r1->field_7 = r0
    //     0xc14cbc: stur            w0, [x1, #7]
    // 0xc14cc0: r0 = Instance_BoxShape
    //     0xc14cc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xc14cc4: ldr             x0, [x0, #0x970]
    // 0xc14cc8: StoreField: r1->field_23 = r0
    //     0xc14cc8: stur            w0, [x1, #0x23]
    // 0xc14ccc: ldur            x2, [fp, #-8]
    // 0xc14cd0: LoadField: r3 = r2->field_b
    //     0xc14cd0: ldur            w3, [x2, #0xb]
    // 0xc14cd4: DecompressPointer r3
    //     0xc14cd4: add             x3, x3, HEAP, lsl #32
    // 0xc14cd8: cmp             w3, NULL
    // 0xc14cdc: b.eq            #0xc16680
    // 0xc14ce0: LoadField: r4 = r3->field_b
    //     0xc14ce0: ldur            w4, [x3, #0xb]
    // 0xc14ce4: DecompressPointer r4
    //     0xc14ce4: add             x4, x4, HEAP, lsl #32
    // 0xc14ce8: cmp             w4, NULL
    // 0xc14cec: b.ne            #0xc14cf8
    // 0xc14cf0: r0 = Null
    //     0xc14cf0: mov             x0, NULL
    // 0xc14cf4: b               #0xc14d34
    // 0xc14cf8: LoadField: r3 = r4->field_7
    //     0xc14cf8: ldur            w3, [x4, #7]
    // 0xc14cfc: DecompressPointer r3
    //     0xc14cfc: add             x3, x3, HEAP, lsl #32
    // 0xc14d00: cmp             w3, NULL
    // 0xc14d04: b.ne            #0xc14d10
    // 0xc14d08: r0 = Null
    //     0xc14d08: mov             x0, NULL
    // 0xc14d0c: b               #0xc14d34
    // 0xc14d10: stp             xzr, x3, [SP]
    // 0xc14d14: r0 = []()
    //     0xc14d14: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xc14d18: r1 = LoadClassIdInstr(r0)
    //     0xc14d18: ldur            x1, [x0, #-1]
    //     0xc14d1c: ubfx            x1, x1, #0xc, #0x14
    // 0xc14d20: str             x0, [SP]
    // 0xc14d24: mov             x0, x1
    // 0xc14d28: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14d28: sub             lr, x0, #1, lsl #12
    //     0xc14d2c: ldr             lr, [x21, lr, lsl #3]
    //     0xc14d30: blr             lr
    // 0xc14d34: cmp             w0, NULL
    // 0xc14d38: b.ne            #0xc14d44
    // 0xc14d3c: r3 = ""
    //     0xc14d3c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc14d40: b               #0xc14d48
    // 0xc14d44: mov             x3, x0
    // 0xc14d48: ldur            x0, [fp, #-8]
    // 0xc14d4c: ldur            x2, [fp, #-0x20]
    // 0xc14d50: stur            x3, [fp, #-0x10]
    // 0xc14d54: LoadField: r1 = r2->field_13
    //     0xc14d54: ldur            w1, [x2, #0x13]
    // 0xc14d58: DecompressPointer r1
    //     0xc14d58: add             x1, x1, HEAP, lsl #32
    // 0xc14d5c: r0 = of()
    //     0xc14d5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc14d60: LoadField: r1 = r0->field_87
    //     0xc14d60: ldur            w1, [x0, #0x87]
    // 0xc14d64: DecompressPointer r1
    //     0xc14d64: add             x1, x1, HEAP, lsl #32
    // 0xc14d68: LoadField: r0 = r1->field_7
    //     0xc14d68: ldur            w0, [x1, #7]
    // 0xc14d6c: DecompressPointer r0
    //     0xc14d6c: add             x0, x0, HEAP, lsl #32
    // 0xc14d70: r16 = 16.000000
    //     0xc14d70: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc14d74: ldr             x16, [x16, #0x188]
    // 0xc14d78: r30 = Instance_Color
    //     0xc14d78: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc14d7c: stp             lr, x16, [SP]
    // 0xc14d80: mov             x1, x0
    // 0xc14d84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc14d84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc14d88: ldr             x4, [x4, #0xaa0]
    // 0xc14d8c: r0 = copyWith()
    //     0xc14d8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc14d90: stur            x0, [fp, #-0x38]
    // 0xc14d94: r0 = Text()
    //     0xc14d94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc14d98: mov             x1, x0
    // 0xc14d9c: ldur            x0, [fp, #-0x10]
    // 0xc14da0: stur            x1, [fp, #-0x48]
    // 0xc14da4: StoreField: r1->field_b = r0
    //     0xc14da4: stur            w0, [x1, #0xb]
    // 0xc14da8: ldur            x0, [fp, #-0x38]
    // 0xc14dac: StoreField: r1->field_13 = r0
    //     0xc14dac: stur            w0, [x1, #0x13]
    // 0xc14db0: r0 = Center()
    //     0xc14db0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc14db4: mov             x1, x0
    // 0xc14db8: r0 = Instance_Alignment
    //     0xc14db8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc14dbc: ldr             x0, [x0, #0xb10]
    // 0xc14dc0: stur            x1, [fp, #-0x10]
    // 0xc14dc4: StoreField: r1->field_f = r0
    //     0xc14dc4: stur            w0, [x1, #0xf]
    // 0xc14dc8: ldur            x2, [fp, #-0x48]
    // 0xc14dcc: StoreField: r1->field_b = r2
    //     0xc14dcc: stur            w2, [x1, #0xb]
    // 0xc14dd0: r0 = Container()
    //     0xc14dd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc14dd4: stur            x0, [fp, #-0x38]
    // 0xc14dd8: r16 = 34.000000
    //     0xc14dd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc14ddc: ldr             x16, [x16, #0x978]
    // 0xc14de0: r30 = 34.000000
    //     0xc14de0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc14de4: ldr             lr, [lr, #0x978]
    // 0xc14de8: stp             lr, x16, [SP, #0x18]
    // 0xc14dec: r16 = Instance_EdgeInsets
    //     0xc14dec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc14df0: ldr             x16, [x16, #0x980]
    // 0xc14df4: ldur            lr, [fp, #-0x30]
    // 0xc14df8: stp             lr, x16, [SP, #8]
    // 0xc14dfc: ldur            x16, [fp, #-0x10]
    // 0xc14e00: str             x16, [SP]
    // 0xc14e04: mov             x1, x0
    // 0xc14e08: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc14e08: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc14e0c: ldr             x4, [x4, #0x988]
    // 0xc14e10: r0 = Container()
    //     0xc14e10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc14e14: ldur            x0, [fp, #-8]
    // 0xc14e18: LoadField: r1 = r0->field_b
    //     0xc14e18: ldur            w1, [x0, #0xb]
    // 0xc14e1c: DecompressPointer r1
    //     0xc14e1c: add             x1, x1, HEAP, lsl #32
    // 0xc14e20: cmp             w1, NULL
    // 0xc14e24: b.eq            #0xc16684
    // 0xc14e28: LoadField: r2 = r1->field_b
    //     0xc14e28: ldur            w2, [x1, #0xb]
    // 0xc14e2c: DecompressPointer r2
    //     0xc14e2c: add             x2, x2, HEAP, lsl #32
    // 0xc14e30: cmp             w2, NULL
    // 0xc14e34: b.ne            #0xc14e40
    // 0xc14e38: r1 = Null
    //     0xc14e38: mov             x1, NULL
    // 0xc14e3c: b               #0xc14e48
    // 0xc14e40: LoadField: r1 = r2->field_7
    //     0xc14e40: ldur            w1, [x2, #7]
    // 0xc14e44: DecompressPointer r1
    //     0xc14e44: add             x1, x1, HEAP, lsl #32
    // 0xc14e48: cmp             w1, NULL
    // 0xc14e4c: b.ne            #0xc14e58
    // 0xc14e50: r3 = ""
    //     0xc14e50: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc14e54: b               #0xc14e5c
    // 0xc14e58: mov             x3, x1
    // 0xc14e5c: ldur            x2, [fp, #-0x20]
    // 0xc14e60: stur            x3, [fp, #-0x10]
    // 0xc14e64: LoadField: r1 = r2->field_13
    //     0xc14e64: ldur            w1, [x2, #0x13]
    // 0xc14e68: DecompressPointer r1
    //     0xc14e68: add             x1, x1, HEAP, lsl #32
    // 0xc14e6c: r0 = of()
    //     0xc14e6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc14e70: LoadField: r1 = r0->field_87
    //     0xc14e70: ldur            w1, [x0, #0x87]
    // 0xc14e74: DecompressPointer r1
    //     0xc14e74: add             x1, x1, HEAP, lsl #32
    // 0xc14e78: LoadField: r0 = r1->field_7
    //     0xc14e78: ldur            w0, [x1, #7]
    // 0xc14e7c: DecompressPointer r0
    //     0xc14e7c: add             x0, x0, HEAP, lsl #32
    // 0xc14e80: r16 = 14.000000
    //     0xc14e80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc14e84: ldr             x16, [x16, #0x1d8]
    // 0xc14e88: r30 = Instance_Color
    //     0xc14e88: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc14e8c: stp             lr, x16, [SP]
    // 0xc14e90: mov             x1, x0
    // 0xc14e94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc14e94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc14e98: ldr             x4, [x4, #0xaa0]
    // 0xc14e9c: r0 = copyWith()
    //     0xc14e9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc14ea0: stur            x0, [fp, #-0x30]
    // 0xc14ea4: r0 = Text()
    //     0xc14ea4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc14ea8: mov             x2, x0
    // 0xc14eac: ldur            x0, [fp, #-0x10]
    // 0xc14eb0: stur            x2, [fp, #-0x48]
    // 0xc14eb4: StoreField: r2->field_b = r0
    //     0xc14eb4: stur            w0, [x2, #0xb]
    // 0xc14eb8: ldur            x0, [fp, #-0x30]
    // 0xc14ebc: StoreField: r2->field_13 = r0
    //     0xc14ebc: stur            w0, [x2, #0x13]
    // 0xc14ec0: ldur            x0, [fp, #-8]
    // 0xc14ec4: LoadField: r1 = r0->field_b
    //     0xc14ec4: ldur            w1, [x0, #0xb]
    // 0xc14ec8: DecompressPointer r1
    //     0xc14ec8: add             x1, x1, HEAP, lsl #32
    // 0xc14ecc: cmp             w1, NULL
    // 0xc14ed0: b.eq            #0xc16688
    // 0xc14ed4: LoadField: r3 = r1->field_b
    //     0xc14ed4: ldur            w3, [x1, #0xb]
    // 0xc14ed8: DecompressPointer r3
    //     0xc14ed8: add             x3, x3, HEAP, lsl #32
    // 0xc14edc: cmp             w3, NULL
    // 0xc14ee0: b.ne            #0xc14eec
    // 0xc14ee4: r1 = Null
    //     0xc14ee4: mov             x1, NULL
    // 0xc14ee8: b               #0xc14ef4
    // 0xc14eec: LoadField: r1 = r3->field_1f
    //     0xc14eec: ldur            w1, [x3, #0x1f]
    // 0xc14ef0: DecompressPointer r1
    //     0xc14ef0: add             x1, x1, HEAP, lsl #32
    // 0xc14ef4: cmp             w1, NULL
    // 0xc14ef8: b.ne            #0xc14f04
    // 0xc14efc: r4 = ""
    //     0xc14efc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc14f00: b               #0xc14f08
    // 0xc14f04: mov             x4, x1
    // 0xc14f08: ldur            x3, [fp, #-0x20]
    // 0xc14f0c: stur            x4, [fp, #-0x10]
    // 0xc14f10: LoadField: r1 = r3->field_13
    //     0xc14f10: ldur            w1, [x3, #0x13]
    // 0xc14f14: DecompressPointer r1
    //     0xc14f14: add             x1, x1, HEAP, lsl #32
    // 0xc14f18: r0 = of()
    //     0xc14f18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc14f1c: LoadField: r1 = r0->field_87
    //     0xc14f1c: ldur            w1, [x0, #0x87]
    // 0xc14f20: DecompressPointer r1
    //     0xc14f20: add             x1, x1, HEAP, lsl #32
    // 0xc14f24: LoadField: r0 = r1->field_33
    //     0xc14f24: ldur            w0, [x1, #0x33]
    // 0xc14f28: DecompressPointer r0
    //     0xc14f28: add             x0, x0, HEAP, lsl #32
    // 0xc14f2c: stur            x0, [fp, #-0x30]
    // 0xc14f30: cmp             w0, NULL
    // 0xc14f34: b.ne            #0xc14f40
    // 0xc14f38: r4 = Null
    //     0xc14f38: mov             x4, NULL
    // 0xc14f3c: b               #0xc14f68
    // 0xc14f40: r1 = Instance_Color
    //     0xc14f40: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc14f44: d0 = 0.500000
    //     0xc14f44: fmov            d0, #0.50000000
    // 0xc14f48: r0 = withOpacity()
    //     0xc14f48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc14f4c: r16 = 10.000000
    //     0xc14f4c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc14f50: stp             x0, x16, [SP]
    // 0xc14f54: ldur            x1, [fp, #-0x30]
    // 0xc14f58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc14f58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc14f5c: ldr             x4, [x4, #0xaa0]
    // 0xc14f60: r0 = copyWith()
    //     0xc14f60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc14f64: mov             x4, x0
    // 0xc14f68: ldur            x1, [fp, #-8]
    // 0xc14f6c: ldur            x3, [fp, #-0x38]
    // 0xc14f70: ldur            x0, [fp, #-0x48]
    // 0xc14f74: ldur            x2, [fp, #-0x10]
    // 0xc14f78: stur            x4, [fp, #-0x30]
    // 0xc14f7c: r0 = Text()
    //     0xc14f7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc14f80: mov             x1, x0
    // 0xc14f84: ldur            x0, [fp, #-0x10]
    // 0xc14f88: stur            x1, [fp, #-0x50]
    // 0xc14f8c: StoreField: r1->field_b = r0
    //     0xc14f8c: stur            w0, [x1, #0xb]
    // 0xc14f90: ldur            x0, [fp, #-0x30]
    // 0xc14f94: StoreField: r1->field_13 = r0
    //     0xc14f94: stur            w0, [x1, #0x13]
    // 0xc14f98: r0 = Padding()
    //     0xc14f98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc14f9c: mov             x3, x0
    // 0xc14fa0: r0 = Instance_EdgeInsets
    //     0xc14fa0: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xc14fa4: ldr             x0, [x0, #0xe90]
    // 0xc14fa8: stur            x3, [fp, #-0x10]
    // 0xc14fac: StoreField: r3->field_f = r0
    //     0xc14fac: stur            w0, [x3, #0xf]
    // 0xc14fb0: ldur            x1, [fp, #-0x50]
    // 0xc14fb4: StoreField: r3->field_b = r1
    //     0xc14fb4: stur            w1, [x3, #0xb]
    // 0xc14fb8: r1 = Null
    //     0xc14fb8: mov             x1, NULL
    // 0xc14fbc: r2 = 4
    //     0xc14fbc: movz            x2, #0x4
    // 0xc14fc0: r0 = AllocateArray()
    //     0xc14fc0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc14fc4: mov             x2, x0
    // 0xc14fc8: ldur            x0, [fp, #-0x48]
    // 0xc14fcc: stur            x2, [fp, #-0x30]
    // 0xc14fd0: StoreField: r2->field_f = r0
    //     0xc14fd0: stur            w0, [x2, #0xf]
    // 0xc14fd4: ldur            x0, [fp, #-0x10]
    // 0xc14fd8: StoreField: r2->field_13 = r0
    //     0xc14fd8: stur            w0, [x2, #0x13]
    // 0xc14fdc: r1 = <Widget>
    //     0xc14fdc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc14fe0: r0 = AllocateGrowableArray()
    //     0xc14fe0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc14fe4: mov             x1, x0
    // 0xc14fe8: ldur            x0, [fp, #-0x30]
    // 0xc14fec: stur            x1, [fp, #-0x10]
    // 0xc14ff0: StoreField: r1->field_f = r0
    //     0xc14ff0: stur            w0, [x1, #0xf]
    // 0xc14ff4: r2 = 4
    //     0xc14ff4: movz            x2, #0x4
    // 0xc14ff8: StoreField: r1->field_b = r2
    //     0xc14ff8: stur            w2, [x1, #0xb]
    // 0xc14ffc: r0 = Column()
    //     0xc14ffc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc15000: mov             x3, x0
    // 0xc15004: r0 = Instance_Axis
    //     0xc15004: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc15008: stur            x3, [fp, #-0x30]
    // 0xc1500c: StoreField: r3->field_f = r0
    //     0xc1500c: stur            w0, [x3, #0xf]
    // 0xc15010: r4 = Instance_MainAxisAlignment
    //     0xc15010: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc15014: ldr             x4, [x4, #0xa08]
    // 0xc15018: StoreField: r3->field_13 = r4
    //     0xc15018: stur            w4, [x3, #0x13]
    // 0xc1501c: r5 = Instance_MainAxisSize
    //     0xc1501c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15020: ldr             x5, [x5, #0xa10]
    // 0xc15024: ArrayStore: r3[0] = r5  ; List_4
    //     0xc15024: stur            w5, [x3, #0x17]
    // 0xc15028: r6 = Instance_CrossAxisAlignment
    //     0xc15028: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc1502c: ldr             x6, [x6, #0x890]
    // 0xc15030: StoreField: r3->field_1b = r6
    //     0xc15030: stur            w6, [x3, #0x1b]
    // 0xc15034: r7 = Instance_VerticalDirection
    //     0xc15034: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc15038: ldr             x7, [x7, #0xa20]
    // 0xc1503c: StoreField: r3->field_23 = r7
    //     0xc1503c: stur            w7, [x3, #0x23]
    // 0xc15040: r8 = Instance_Clip
    //     0xc15040: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc15044: ldr             x8, [x8, #0x38]
    // 0xc15048: StoreField: r3->field_2b = r8
    //     0xc15048: stur            w8, [x3, #0x2b]
    // 0xc1504c: StoreField: r3->field_2f = rZR
    //     0xc1504c: stur            xzr, [x3, #0x2f]
    // 0xc15050: ldur            x1, [fp, #-0x10]
    // 0xc15054: StoreField: r3->field_b = r1
    //     0xc15054: stur            w1, [x3, #0xb]
    // 0xc15058: r1 = Null
    //     0xc15058: mov             x1, NULL
    // 0xc1505c: r2 = 6
    //     0xc1505c: movz            x2, #0x6
    // 0xc15060: r0 = AllocateArray()
    //     0xc15060: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc15064: mov             x2, x0
    // 0xc15068: ldur            x0, [fp, #-0x38]
    // 0xc1506c: stur            x2, [fp, #-0x10]
    // 0xc15070: StoreField: r2->field_f = r0
    //     0xc15070: stur            w0, [x2, #0xf]
    // 0xc15074: r16 = Instance_SizedBox
    //     0xc15074: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xc15078: ldr             x16, [x16, #0x998]
    // 0xc1507c: StoreField: r2->field_13 = r16
    //     0xc1507c: stur            w16, [x2, #0x13]
    // 0xc15080: ldur            x0, [fp, #-0x30]
    // 0xc15084: ArrayStore: r2[0] = r0  ; List_4
    //     0xc15084: stur            w0, [x2, #0x17]
    // 0xc15088: r1 = <Widget>
    //     0xc15088: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc1508c: r0 = AllocateGrowableArray()
    //     0xc1508c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15090: mov             x1, x0
    // 0xc15094: ldur            x0, [fp, #-0x10]
    // 0xc15098: stur            x1, [fp, #-0x30]
    // 0xc1509c: StoreField: r1->field_f = r0
    //     0xc1509c: stur            w0, [x1, #0xf]
    // 0xc150a0: r2 = 6
    //     0xc150a0: movz            x2, #0x6
    // 0xc150a4: StoreField: r1->field_b = r2
    //     0xc150a4: stur            w2, [x1, #0xb]
    // 0xc150a8: r0 = Row()
    //     0xc150a8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc150ac: mov             x2, x0
    // 0xc150b0: r1 = Instance_Axis
    //     0xc150b0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc150b4: stur            x2, [fp, #-0x10]
    // 0xc150b8: StoreField: r2->field_f = r1
    //     0xc150b8: stur            w1, [x2, #0xf]
    // 0xc150bc: r3 = Instance_MainAxisAlignment
    //     0xc150bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc150c0: ldr             x3, [x3, #0xa08]
    // 0xc150c4: StoreField: r2->field_13 = r3
    //     0xc150c4: stur            w3, [x2, #0x13]
    // 0xc150c8: r4 = Instance_MainAxisSize
    //     0xc150c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc150cc: ldr             x4, [x4, #0xa10]
    // 0xc150d0: ArrayStore: r2[0] = r4  ; List_4
    //     0xc150d0: stur            w4, [x2, #0x17]
    // 0xc150d4: r5 = Instance_CrossAxisAlignment
    //     0xc150d4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc150d8: ldr             x5, [x5, #0xa18]
    // 0xc150dc: StoreField: r2->field_1b = r5
    //     0xc150dc: stur            w5, [x2, #0x1b]
    // 0xc150e0: r6 = Instance_VerticalDirection
    //     0xc150e0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc150e4: ldr             x6, [x6, #0xa20]
    // 0xc150e8: StoreField: r2->field_23 = r6
    //     0xc150e8: stur            w6, [x2, #0x23]
    // 0xc150ec: r7 = Instance_Clip
    //     0xc150ec: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc150f0: ldr             x7, [x7, #0x38]
    // 0xc150f4: StoreField: r2->field_2b = r7
    //     0xc150f4: stur            w7, [x2, #0x2b]
    // 0xc150f8: StoreField: r2->field_2f = rZR
    //     0xc150f8: stur            xzr, [x2, #0x2f]
    // 0xc150fc: ldur            x0, [fp, #-0x30]
    // 0xc15100: StoreField: r2->field_b = r0
    //     0xc15100: stur            w0, [x2, #0xb]
    // 0xc15104: ldur            x8, [fp, #-8]
    // 0xc15108: LoadField: r0 = r8->field_b
    //     0xc15108: ldur            w0, [x8, #0xb]
    // 0xc1510c: DecompressPointer r0
    //     0xc1510c: add             x0, x0, HEAP, lsl #32
    // 0xc15110: cmp             w0, NULL
    // 0xc15114: b.eq            #0xc1668c
    // 0xc15118: LoadField: r9 = r0->field_b
    //     0xc15118: ldur            w9, [x0, #0xb]
    // 0xc1511c: DecompressPointer r9
    //     0xc1511c: add             x9, x9, HEAP, lsl #32
    // 0xc15120: cmp             w9, NULL
    // 0xc15124: b.ne            #0xc15130
    // 0xc15128: r0 = Null
    //     0xc15128: mov             x0, NULL
    // 0xc1512c: b               #0xc15164
    // 0xc15130: LoadField: r0 = r9->field_f
    //     0xc15130: ldur            w0, [x9, #0xf]
    // 0xc15134: DecompressPointer r0
    //     0xc15134: add             x0, x0, HEAP, lsl #32
    // 0xc15138: r9 = 60
    //     0xc15138: movz            x9, #0x3c
    // 0xc1513c: branchIfSmi(r0, 0xc15148)
    //     0xc1513c: tbz             w0, #0, #0xc15148
    // 0xc15140: r9 = LoadClassIdInstr(r0)
    //     0xc15140: ldur            x9, [x0, #-1]
    //     0xc15144: ubfx            x9, x9, #0xc, #0x14
    // 0xc15148: str             x0, [SP]
    // 0xc1514c: mov             x0, x9
    // 0xc15150: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15150: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc15154: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc15154: movz            x17, #0x2700
    //     0xc15158: add             lr, x0, x17
    //     0xc1515c: ldr             lr, [x21, lr, lsl #3]
    //     0xc15160: blr             lr
    // 0xc15164: cmp             w0, NULL
    // 0xc15168: b.ne            #0xc15174
    // 0xc1516c: r1 = ""
    //     0xc1516c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15170: b               #0xc15178
    // 0xc15174: mov             x1, x0
    // 0xc15178: r0 = parse()
    //     0xc15178: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc1517c: mov             v1.16b, v0.16b
    // 0xc15180: d0 = 4.000000
    //     0xc15180: fmov            d0, #4.00000000
    // 0xc15184: fcmp            d1, d0
    // 0xc15188: b.lt            #0xc1519c
    // 0xc1518c: r1 = Instance_Color
    //     0xc1518c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc15190: ldr             x1, [x1, #0x858]
    // 0xc15194: d0 = 2.000000
    //     0xc15194: fmov            d0, #2.00000000
    // 0xc15198: b               #0xc152dc
    // 0xc1519c: ldur            x1, [fp, #-8]
    // 0xc151a0: LoadField: r0 = r1->field_b
    //     0xc151a0: ldur            w0, [x1, #0xb]
    // 0xc151a4: DecompressPointer r0
    //     0xc151a4: add             x0, x0, HEAP, lsl #32
    // 0xc151a8: cmp             w0, NULL
    // 0xc151ac: b.eq            #0xc16690
    // 0xc151b0: LoadField: r2 = r0->field_b
    //     0xc151b0: ldur            w2, [x0, #0xb]
    // 0xc151b4: DecompressPointer r2
    //     0xc151b4: add             x2, x2, HEAP, lsl #32
    // 0xc151b8: cmp             w2, NULL
    // 0xc151bc: b.ne            #0xc151c8
    // 0xc151c0: r0 = Null
    //     0xc151c0: mov             x0, NULL
    // 0xc151c4: b               #0xc151fc
    // 0xc151c8: LoadField: r0 = r2->field_f
    //     0xc151c8: ldur            w0, [x2, #0xf]
    // 0xc151cc: DecompressPointer r0
    //     0xc151cc: add             x0, x0, HEAP, lsl #32
    // 0xc151d0: r2 = 60
    //     0xc151d0: movz            x2, #0x3c
    // 0xc151d4: branchIfSmi(r0, 0xc151e0)
    //     0xc151d4: tbz             w0, #0, #0xc151e0
    // 0xc151d8: r2 = LoadClassIdInstr(r0)
    //     0xc151d8: ldur            x2, [x0, #-1]
    //     0xc151dc: ubfx            x2, x2, #0xc, #0x14
    // 0xc151e0: str             x0, [SP]
    // 0xc151e4: mov             x0, x2
    // 0xc151e8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc151e8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc151ec: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc151ec: movz            x17, #0x2700
    //     0xc151f0: add             lr, x0, x17
    //     0xc151f4: ldr             lr, [x21, lr, lsl #3]
    //     0xc151f8: blr             lr
    // 0xc151fc: cmp             w0, NULL
    // 0xc15200: b.ne            #0xc1520c
    // 0xc15204: r1 = ""
    //     0xc15204: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15208: b               #0xc15210
    // 0xc1520c: mov             x1, x0
    // 0xc15210: r0 = parse()
    //     0xc15210: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc15214: d1 = 3.500000
    //     0xc15214: fmov            d1, #3.50000000
    // 0xc15218: fcmp            d0, d1
    // 0xc1521c: b.lt            #0xc1523c
    // 0xc15220: r1 = Instance_Color
    //     0xc15220: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc15224: ldr             x1, [x1, #0x858]
    // 0xc15228: d0 = 0.700000
    //     0xc15228: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc1522c: ldr             d0, [x17, #0xf48]
    // 0xc15230: r0 = withOpacity()
    //     0xc15230: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc15234: d0 = 2.000000
    //     0xc15234: fmov            d0, #2.00000000
    // 0xc15238: b               #0xc152d8
    // 0xc1523c: ldur            x1, [fp, #-8]
    // 0xc15240: LoadField: r0 = r1->field_b
    //     0xc15240: ldur            w0, [x1, #0xb]
    // 0xc15244: DecompressPointer r0
    //     0xc15244: add             x0, x0, HEAP, lsl #32
    // 0xc15248: cmp             w0, NULL
    // 0xc1524c: b.eq            #0xc16694
    // 0xc15250: LoadField: r2 = r0->field_b
    //     0xc15250: ldur            w2, [x0, #0xb]
    // 0xc15254: DecompressPointer r2
    //     0xc15254: add             x2, x2, HEAP, lsl #32
    // 0xc15258: cmp             w2, NULL
    // 0xc1525c: b.ne            #0xc15268
    // 0xc15260: r0 = Null
    //     0xc15260: mov             x0, NULL
    // 0xc15264: b               #0xc1529c
    // 0xc15268: LoadField: r0 = r2->field_f
    //     0xc15268: ldur            w0, [x2, #0xf]
    // 0xc1526c: DecompressPointer r0
    //     0xc1526c: add             x0, x0, HEAP, lsl #32
    // 0xc15270: r2 = 60
    //     0xc15270: movz            x2, #0x3c
    // 0xc15274: branchIfSmi(r0, 0xc15280)
    //     0xc15274: tbz             w0, #0, #0xc15280
    // 0xc15278: r2 = LoadClassIdInstr(r0)
    //     0xc15278: ldur            x2, [x0, #-1]
    //     0xc1527c: ubfx            x2, x2, #0xc, #0x14
    // 0xc15280: str             x0, [SP]
    // 0xc15284: mov             x0, x2
    // 0xc15288: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15288: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc1528c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc1528c: movz            x17, #0x2700
    //     0xc15290: add             lr, x0, x17
    //     0xc15294: ldr             lr, [x21, lr, lsl #3]
    //     0xc15298: blr             lr
    // 0xc1529c: cmp             w0, NULL
    // 0xc152a0: b.ne            #0xc152ac
    // 0xc152a4: r1 = ""
    //     0xc152a4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc152a8: b               #0xc152b0
    // 0xc152ac: mov             x1, x0
    // 0xc152b0: r0 = parse()
    //     0xc152b0: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc152b4: mov             v1.16b, v0.16b
    // 0xc152b8: d0 = 2.000000
    //     0xc152b8: fmov            d0, #2.00000000
    // 0xc152bc: fcmp            d1, d0
    // 0xc152c0: b.lt            #0xc152d0
    // 0xc152c4: r0 = Instance_Color
    //     0xc152c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xc152c8: ldr             x0, [x0, #0x860]
    // 0xc152cc: b               #0xc152d8
    // 0xc152d0: r0 = Instance_Color
    //     0xc152d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xc152d4: ldr             x0, [x0, #0x50]
    // 0xc152d8: mov             x1, x0
    // 0xc152dc: ldur            x0, [fp, #-8]
    // 0xc152e0: stur            x1, [fp, #-0x30]
    // 0xc152e4: r0 = ColorFilter()
    //     0xc152e4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc152e8: mov             x1, x0
    // 0xc152ec: ldur            x0, [fp, #-0x30]
    // 0xc152f0: stur            x1, [fp, #-0x38]
    // 0xc152f4: StoreField: r1->field_7 = r0
    //     0xc152f4: stur            w0, [x1, #7]
    // 0xc152f8: r0 = Instance_BlendMode
    //     0xc152f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc152fc: ldr             x0, [x0, #0xb30]
    // 0xc15300: StoreField: r1->field_b = r0
    //     0xc15300: stur            w0, [x1, #0xb]
    // 0xc15304: r2 = 1
    //     0xc15304: movz            x2, #0x1
    // 0xc15308: StoreField: r1->field_13 = r2
    //     0xc15308: stur            x2, [x1, #0x13]
    // 0xc1530c: r0 = SvgPicture()
    //     0xc1530c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc15310: stur            x0, [fp, #-0x30]
    // 0xc15314: ldur            x16, [fp, #-0x38]
    // 0xc15318: str             x16, [SP]
    // 0xc1531c: mov             x1, x0
    // 0xc15320: r2 = "assets/images/green_star.svg"
    //     0xc15320: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xc15324: ldr             x2, [x2, #0x9a0]
    // 0xc15328: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc15328: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc1532c: ldr             x4, [x4, #0xa38]
    // 0xc15330: r0 = SvgPicture.asset()
    //     0xc15330: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc15334: ldur            x1, [fp, #-8]
    // 0xc15338: LoadField: r0 = r1->field_b
    //     0xc15338: ldur            w0, [x1, #0xb]
    // 0xc1533c: DecompressPointer r0
    //     0xc1533c: add             x0, x0, HEAP, lsl #32
    // 0xc15340: cmp             w0, NULL
    // 0xc15344: b.eq            #0xc16698
    // 0xc15348: LoadField: r2 = r0->field_b
    //     0xc15348: ldur            w2, [x0, #0xb]
    // 0xc1534c: DecompressPointer r2
    //     0xc1534c: add             x2, x2, HEAP, lsl #32
    // 0xc15350: cmp             w2, NULL
    // 0xc15354: b.ne            #0xc15360
    // 0xc15358: r0 = Null
    //     0xc15358: mov             x0, NULL
    // 0xc1535c: b               #0xc15394
    // 0xc15360: LoadField: r0 = r2->field_f
    //     0xc15360: ldur            w0, [x2, #0xf]
    // 0xc15364: DecompressPointer r0
    //     0xc15364: add             x0, x0, HEAP, lsl #32
    // 0xc15368: r2 = 60
    //     0xc15368: movz            x2, #0x3c
    // 0xc1536c: branchIfSmi(r0, 0xc15378)
    //     0xc1536c: tbz             w0, #0, #0xc15378
    // 0xc15370: r2 = LoadClassIdInstr(r0)
    //     0xc15370: ldur            x2, [x0, #-1]
    //     0xc15374: ubfx            x2, x2, #0xc, #0x14
    // 0xc15378: str             x0, [SP]
    // 0xc1537c: mov             x0, x2
    // 0xc15380: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15380: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc15384: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc15384: movz            x17, #0x2700
    //     0xc15388: add             lr, x0, x17
    //     0xc1538c: ldr             lr, [x21, lr, lsl #3]
    //     0xc15390: blr             lr
    // 0xc15394: cmp             w0, NULL
    // 0xc15398: b.ne            #0xc153a4
    // 0xc1539c: r5 = ""
    //     0xc1539c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc153a0: b               #0xc153a8
    // 0xc153a4: mov             x5, x0
    // 0xc153a8: ldur            x3, [fp, #-0x20]
    // 0xc153ac: ldur            x2, [fp, #-0x10]
    // 0xc153b0: ldur            x0, [fp, #-0x30]
    // 0xc153b4: ldur            d0, [fp, #-0x58]
    // 0xc153b8: ldur            x4, [fp, #-0x18]
    // 0xc153bc: stur            x5, [fp, #-0x38]
    // 0xc153c0: LoadField: r1 = r3->field_13
    //     0xc153c0: ldur            w1, [x3, #0x13]
    // 0xc153c4: DecompressPointer r1
    //     0xc153c4: add             x1, x1, HEAP, lsl #32
    // 0xc153c8: r0 = of()
    //     0xc153c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc153cc: LoadField: r1 = r0->field_87
    //     0xc153cc: ldur            w1, [x0, #0x87]
    // 0xc153d0: DecompressPointer r1
    //     0xc153d0: add             x1, x1, HEAP, lsl #32
    // 0xc153d4: LoadField: r0 = r1->field_7
    //     0xc153d4: ldur            w0, [x1, #7]
    // 0xc153d8: DecompressPointer r0
    //     0xc153d8: add             x0, x0, HEAP, lsl #32
    // 0xc153dc: r16 = 12.000000
    //     0xc153dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc153e0: ldr             x16, [x16, #0x9e8]
    // 0xc153e4: r30 = Instance_Color
    //     0xc153e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc153e8: stp             lr, x16, [SP]
    // 0xc153ec: mov             x1, x0
    // 0xc153f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc153f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc153f4: ldr             x4, [x4, #0xaa0]
    // 0xc153f8: r0 = copyWith()
    //     0xc153f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc153fc: stur            x0, [fp, #-0x48]
    // 0xc15400: r0 = Text()
    //     0xc15400: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc15404: mov             x3, x0
    // 0xc15408: ldur            x0, [fp, #-0x38]
    // 0xc1540c: stur            x3, [fp, #-0x50]
    // 0xc15410: StoreField: r3->field_b = r0
    //     0xc15410: stur            w0, [x3, #0xb]
    // 0xc15414: ldur            x0, [fp, #-0x48]
    // 0xc15418: StoreField: r3->field_13 = r0
    //     0xc15418: stur            w0, [x3, #0x13]
    // 0xc1541c: r1 = Null
    //     0xc1541c: mov             x1, NULL
    // 0xc15420: r2 = 6
    //     0xc15420: movz            x2, #0x6
    // 0xc15424: r0 = AllocateArray()
    //     0xc15424: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc15428: mov             x2, x0
    // 0xc1542c: ldur            x0, [fp, #-0x30]
    // 0xc15430: stur            x2, [fp, #-0x38]
    // 0xc15434: StoreField: r2->field_f = r0
    //     0xc15434: stur            w0, [x2, #0xf]
    // 0xc15438: r16 = Instance_SizedBox
    //     0xc15438: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xc1543c: ldr             x16, [x16, #0xe98]
    // 0xc15440: StoreField: r2->field_13 = r16
    //     0xc15440: stur            w16, [x2, #0x13]
    // 0xc15444: ldur            x0, [fp, #-0x50]
    // 0xc15448: ArrayStore: r2[0] = r0  ; List_4
    //     0xc15448: stur            w0, [x2, #0x17]
    // 0xc1544c: r1 = <Widget>
    //     0xc1544c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15450: r0 = AllocateGrowableArray()
    //     0xc15450: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15454: mov             x1, x0
    // 0xc15458: ldur            x0, [fp, #-0x38]
    // 0xc1545c: stur            x1, [fp, #-0x30]
    // 0xc15460: StoreField: r1->field_f = r0
    //     0xc15460: stur            w0, [x1, #0xf]
    // 0xc15464: r2 = 6
    //     0xc15464: movz            x2, #0x6
    // 0xc15468: StoreField: r1->field_b = r2
    //     0xc15468: stur            w2, [x1, #0xb]
    // 0xc1546c: r0 = Row()
    //     0xc1546c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc15470: mov             x1, x0
    // 0xc15474: r0 = Instance_Axis
    //     0xc15474: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc15478: stur            x1, [fp, #-0x38]
    // 0xc1547c: StoreField: r1->field_f = r0
    //     0xc1547c: stur            w0, [x1, #0xf]
    // 0xc15480: r2 = Instance_MainAxisAlignment
    //     0xc15480: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc15484: ldr             x2, [x2, #0xa08]
    // 0xc15488: StoreField: r1->field_13 = r2
    //     0xc15488: stur            w2, [x1, #0x13]
    // 0xc1548c: r3 = Instance_MainAxisSize
    //     0xc1548c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15490: ldr             x3, [x3, #0xa10]
    // 0xc15494: ArrayStore: r1[0] = r3  ; List_4
    //     0xc15494: stur            w3, [x1, #0x17]
    // 0xc15498: r4 = Instance_CrossAxisAlignment
    //     0xc15498: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc1549c: ldr             x4, [x4, #0xa18]
    // 0xc154a0: StoreField: r1->field_1b = r4
    //     0xc154a0: stur            w4, [x1, #0x1b]
    // 0xc154a4: r5 = Instance_VerticalDirection
    //     0xc154a4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc154a8: ldr             x5, [x5, #0xa20]
    // 0xc154ac: StoreField: r1->field_23 = r5
    //     0xc154ac: stur            w5, [x1, #0x23]
    // 0xc154b0: r6 = Instance_Clip
    //     0xc154b0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc154b4: ldr             x6, [x6, #0x38]
    // 0xc154b8: StoreField: r1->field_2b = r6
    //     0xc154b8: stur            w6, [x1, #0x2b]
    // 0xc154bc: StoreField: r1->field_2f = rZR
    //     0xc154bc: stur            xzr, [x1, #0x2f]
    // 0xc154c0: ldur            x7, [fp, #-0x30]
    // 0xc154c4: StoreField: r1->field_b = r7
    //     0xc154c4: stur            w7, [x1, #0xb]
    // 0xc154c8: r0 = Align()
    //     0xc154c8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc154cc: mov             x3, x0
    // 0xc154d0: r0 = Instance_Alignment
    //     0xc154d0: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xc154d4: ldr             x0, [x0, #0xa78]
    // 0xc154d8: stur            x3, [fp, #-0x30]
    // 0xc154dc: StoreField: r3->field_f = r0
    //     0xc154dc: stur            w0, [x3, #0xf]
    // 0xc154e0: ldur            x1, [fp, #-0x38]
    // 0xc154e4: StoreField: r3->field_b = r1
    //     0xc154e4: stur            w1, [x3, #0xb]
    // 0xc154e8: r1 = Null
    //     0xc154e8: mov             x1, NULL
    // 0xc154ec: r2 = 4
    //     0xc154ec: movz            x2, #0x4
    // 0xc154f0: r0 = AllocateArray()
    //     0xc154f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc154f4: mov             x2, x0
    // 0xc154f8: ldur            x0, [fp, #-0x10]
    // 0xc154fc: stur            x2, [fp, #-0x38]
    // 0xc15500: StoreField: r2->field_f = r0
    //     0xc15500: stur            w0, [x2, #0xf]
    // 0xc15504: ldur            x0, [fp, #-0x30]
    // 0xc15508: StoreField: r2->field_13 = r0
    //     0xc15508: stur            w0, [x2, #0x13]
    // 0xc1550c: r1 = <Widget>
    //     0xc1550c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15510: r0 = AllocateGrowableArray()
    //     0xc15510: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15514: mov             x1, x0
    // 0xc15518: ldur            x0, [fp, #-0x38]
    // 0xc1551c: stur            x1, [fp, #-0x10]
    // 0xc15520: StoreField: r1->field_f = r0
    //     0xc15520: stur            w0, [x1, #0xf]
    // 0xc15524: r2 = 4
    //     0xc15524: movz            x2, #0x4
    // 0xc15528: StoreField: r1->field_b = r2
    //     0xc15528: stur            w2, [x1, #0xb]
    // 0xc1552c: r0 = Row()
    //     0xc1552c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc15530: mov             x1, x0
    // 0xc15534: r0 = Instance_Axis
    //     0xc15534: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc15538: stur            x1, [fp, #-0x30]
    // 0xc1553c: StoreField: r1->field_f = r0
    //     0xc1553c: stur            w0, [x1, #0xf]
    // 0xc15540: r2 = Instance_MainAxisAlignment
    //     0xc15540: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc15544: ldr             x2, [x2, #0xa8]
    // 0xc15548: StoreField: r1->field_13 = r2
    //     0xc15548: stur            w2, [x1, #0x13]
    // 0xc1554c: r3 = Instance_MainAxisSize
    //     0xc1554c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15550: ldr             x3, [x3, #0xa10]
    // 0xc15554: ArrayStore: r1[0] = r3  ; List_4
    //     0xc15554: stur            w3, [x1, #0x17]
    // 0xc15558: r4 = Instance_CrossAxisAlignment
    //     0xc15558: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc1555c: ldr             x4, [x4, #0xa18]
    // 0xc15560: StoreField: r1->field_1b = r4
    //     0xc15560: stur            w4, [x1, #0x1b]
    // 0xc15564: r5 = Instance_VerticalDirection
    //     0xc15564: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc15568: ldr             x5, [x5, #0xa20]
    // 0xc1556c: StoreField: r1->field_23 = r5
    //     0xc1556c: stur            w5, [x1, #0x23]
    // 0xc15570: r6 = Instance_Clip
    //     0xc15570: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc15574: ldr             x6, [x6, #0x38]
    // 0xc15578: StoreField: r1->field_2b = r6
    //     0xc15578: stur            w6, [x1, #0x2b]
    // 0xc1557c: StoreField: r1->field_2f = rZR
    //     0xc1557c: stur            xzr, [x1, #0x2f]
    // 0xc15580: ldur            x7, [fp, #-0x10]
    // 0xc15584: StoreField: r1->field_b = r7
    //     0xc15584: stur            w7, [x1, #0xb]
    // 0xc15588: r0 = Padding()
    //     0xc15588: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc1558c: mov             x1, x0
    // 0xc15590: r0 = Instance_EdgeInsets
    //     0xc15590: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xc15594: ldr             x0, [x0, #0xd0]
    // 0xc15598: stur            x1, [fp, #-0x10]
    // 0xc1559c: StoreField: r1->field_f = r0
    //     0xc1559c: stur            w0, [x1, #0xf]
    // 0xc155a0: ldur            x0, [fp, #-0x30]
    // 0xc155a4: StoreField: r1->field_b = r0
    //     0xc155a4: stur            w0, [x1, #0xb]
    // 0xc155a8: r0 = Container()
    //     0xc155a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc155ac: stur            x0, [fp, #-0x30]
    // 0xc155b0: ldur            x16, [fp, #-0x28]
    // 0xc155b4: ldur            lr, [fp, #-0x10]
    // 0xc155b8: stp             lr, x16, [SP]
    // 0xc155bc: mov             x1, x0
    // 0xc155c0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xc155c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xc155c4: ldr             x4, [x4, #0x88]
    // 0xc155c8: r0 = Container()
    //     0xc155c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc155cc: ldur            d0, [fp, #-0x58]
    // 0xc155d0: r0 = inline_Allocate_Double()
    //     0xc155d0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc155d4: add             x0, x0, #0x10
    //     0xc155d8: cmp             x1, x0
    //     0xc155dc: b.ls            #0xc1669c
    //     0xc155e0: str             x0, [THR, #0x50]  ; THR::top
    //     0xc155e4: sub             x0, x0, #0xf
    //     0xc155e8: movz            x1, #0xe15c
    //     0xc155ec: movk            x1, #0x3, lsl #16
    //     0xc155f0: stur            x1, [x0, #-1]
    // 0xc155f4: StoreField: r0->field_7 = d0
    //     0xc155f4: stur            d0, [x0, #7]
    // 0xc155f8: stur            x0, [fp, #-0x10]
    // 0xc155fc: r0 = SizedBox()
    //     0xc155fc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc15600: mov             x2, x0
    // 0xc15604: ldur            x0, [fp, #-0x10]
    // 0xc15608: stur            x2, [fp, #-0x28]
    // 0xc1560c: StoreField: r2->field_f = r0
    //     0xc1560c: stur            w0, [x2, #0xf]
    // 0xc15610: ldur            x0, [fp, #-0x30]
    // 0xc15614: StoreField: r2->field_b = r0
    //     0xc15614: stur            w0, [x2, #0xb]
    // 0xc15618: r1 = <StackParentData>
    //     0xc15618: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc1561c: ldr             x1, [x1, #0x8e0]
    // 0xc15620: r0 = Positioned()
    //     0xc15620: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc15624: mov             x2, x0
    // 0xc15628: r0 = 0.000000
    //     0xc15628: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc1562c: stur            x2, [fp, #-0x10]
    // 0xc15630: StoreField: r2->field_1f = r0
    //     0xc15630: stur            w0, [x2, #0x1f]
    // 0xc15634: ldur            x0, [fp, #-0x28]
    // 0xc15638: StoreField: r2->field_b = r0
    //     0xc15638: stur            w0, [x2, #0xb]
    // 0xc1563c: ldur            x0, [fp, #-0x18]
    // 0xc15640: LoadField: r1 = r0->field_b
    //     0xc15640: ldur            w1, [x0, #0xb]
    // 0xc15644: LoadField: r3 = r0->field_f
    //     0xc15644: ldur            w3, [x0, #0xf]
    // 0xc15648: DecompressPointer r3
    //     0xc15648: add             x3, x3, HEAP, lsl #32
    // 0xc1564c: LoadField: r4 = r3->field_b
    //     0xc1564c: ldur            w4, [x3, #0xb]
    // 0xc15650: r3 = LoadInt32Instr(r1)
    //     0xc15650: sbfx            x3, x1, #1, #0x1f
    // 0xc15654: stur            x3, [fp, #-0x40]
    // 0xc15658: r1 = LoadInt32Instr(r4)
    //     0xc15658: sbfx            x1, x4, #1, #0x1f
    // 0xc1565c: cmp             x3, x1
    // 0xc15660: b.ne            #0xc1566c
    // 0xc15664: mov             x1, x0
    // 0xc15668: r0 = _growToNextCapacity()
    //     0xc15668: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc1566c: ldur            x2, [fp, #-0x18]
    // 0xc15670: ldur            x3, [fp, #-0x40]
    // 0xc15674: add             x0, x3, #1
    // 0xc15678: lsl             x1, x0, #1
    // 0xc1567c: StoreField: r2->field_b = r1
    //     0xc1567c: stur            w1, [x2, #0xb]
    // 0xc15680: LoadField: r1 = r2->field_f
    //     0xc15680: ldur            w1, [x2, #0xf]
    // 0xc15684: DecompressPointer r1
    //     0xc15684: add             x1, x1, HEAP, lsl #32
    // 0xc15688: ldur            x0, [fp, #-0x10]
    // 0xc1568c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc1568c: add             x25, x1, x3, lsl #2
    //     0xc15690: add             x25, x25, #0xf
    //     0xc15694: str             w0, [x25]
    //     0xc15698: tbz             w0, #0, #0xc156b4
    //     0xc1569c: ldurb           w16, [x1, #-1]
    //     0xc156a0: ldurb           w17, [x0, #-1]
    //     0xc156a4: and             x16, x17, x16, lsr #2
    //     0xc156a8: tst             x16, HEAP, lsr #32
    //     0xc156ac: b.eq            #0xc156b4
    //     0xc156b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc156b4: ldur            x0, [fp, #-8]
    // 0xc156b8: r0 = Stack()
    //     0xc156b8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc156bc: mov             x1, x0
    // 0xc156c0: r0 = Instance_Alignment
    //     0xc156c0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xc156c4: ldr             x0, [x0, #0xce0]
    // 0xc156c8: stur            x1, [fp, #-0x10]
    // 0xc156cc: StoreField: r1->field_f = r0
    //     0xc156cc: stur            w0, [x1, #0xf]
    // 0xc156d0: r0 = Instance_StackFit
    //     0xc156d0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc156d4: ldr             x0, [x0, #0xfa8]
    // 0xc156d8: ArrayStore: r1[0] = r0  ; List_4
    //     0xc156d8: stur            w0, [x1, #0x17]
    // 0xc156dc: r0 = Instance_Clip
    //     0xc156dc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc156e0: ldr             x0, [x0, #0x7e0]
    // 0xc156e4: StoreField: r1->field_1b = r0
    //     0xc156e4: stur            w0, [x1, #0x1b]
    // 0xc156e8: ldur            x0, [fp, #-0x18]
    // 0xc156ec: StoreField: r1->field_b = r0
    //     0xc156ec: stur            w0, [x1, #0xb]
    // 0xc156f0: r0 = ColoredBox()
    //     0xc156f0: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xc156f4: mov             x2, x0
    // 0xc156f8: r0 = Instance_Color
    //     0xc156f8: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc156fc: stur            x2, [fp, #-0x18]
    // 0xc15700: StoreField: r2->field_f = r0
    //     0xc15700: stur            w0, [x2, #0xf]
    // 0xc15704: ldur            x1, [fp, #-0x10]
    // 0xc15708: StoreField: r2->field_b = r1
    //     0xc15708: stur            w1, [x2, #0xb]
    // 0xc1570c: r1 = <FlexParentData>
    //     0xc1570c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc15710: ldr             x1, [x1, #0xe00]
    // 0xc15714: r0 = Expanded()
    //     0xc15714: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc15718: mov             x3, x0
    // 0xc1571c: r0 = 1
    //     0xc1571c: movz            x0, #0x1
    // 0xc15720: stur            x3, [fp, #-0x10]
    // 0xc15724: StoreField: r3->field_13 = r0
    //     0xc15724: stur            x0, [x3, #0x13]
    // 0xc15728: r1 = Instance_FlexFit
    //     0xc15728: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc1572c: ldr             x1, [x1, #0xe08]
    // 0xc15730: StoreField: r3->field_1b = r1
    //     0xc15730: stur            w1, [x3, #0x1b]
    // 0xc15734: ldur            x1, [fp, #-0x18]
    // 0xc15738: StoreField: r3->field_b = r1
    //     0xc15738: stur            w1, [x3, #0xb]
    // 0xc1573c: r1 = <Widget>
    //     0xc1573c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15740: r2 = 0
    //     0xc15740: movz            x2, #0
    // 0xc15744: r0 = _GrowableList()
    //     0xc15744: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc15748: mov             x2, x0
    // 0xc1574c: ldur            x0, [fp, #-8]
    // 0xc15750: stur            x2, [fp, #-0x18]
    // 0xc15754: LoadField: r1 = r0->field_23
    //     0xc15754: ldur            w1, [x0, #0x23]
    // 0xc15758: DecompressPointer r1
    //     0xc15758: add             x1, x1, HEAP, lsl #32
    // 0xc1575c: tbz             w1, #4, #0xc160dc
    // 0xc15760: r1 = Instance_Color
    //     0xc15760: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc15764: d0 = 0.050000
    //     0xc15764: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xc15768: r0 = withOpacity()
    //     0xc15768: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc1576c: stur            x0, [fp, #-0x28]
    // 0xc15770: r0 = BoxDecoration()
    //     0xc15770: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc15774: mov             x1, x0
    // 0xc15778: ldur            x0, [fp, #-0x28]
    // 0xc1577c: stur            x1, [fp, #-0x30]
    // 0xc15780: StoreField: r1->field_7 = r0
    //     0xc15780: stur            w0, [x1, #7]
    // 0xc15784: r0 = Instance_BoxShape
    //     0xc15784: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xc15788: ldr             x0, [x0, #0x970]
    // 0xc1578c: StoreField: r1->field_23 = r0
    //     0xc1578c: stur            w0, [x1, #0x23]
    // 0xc15790: ldur            x0, [fp, #-8]
    // 0xc15794: LoadField: r2 = r0->field_b
    //     0xc15794: ldur            w2, [x0, #0xb]
    // 0xc15798: DecompressPointer r2
    //     0xc15798: add             x2, x2, HEAP, lsl #32
    // 0xc1579c: cmp             w2, NULL
    // 0xc157a0: b.eq            #0xc166ac
    // 0xc157a4: LoadField: r3 = r2->field_b
    //     0xc157a4: ldur            w3, [x2, #0xb]
    // 0xc157a8: DecompressPointer r3
    //     0xc157a8: add             x3, x3, HEAP, lsl #32
    // 0xc157ac: cmp             w3, NULL
    // 0xc157b0: b.ne            #0xc157bc
    // 0xc157b4: r0 = Null
    //     0xc157b4: mov             x0, NULL
    // 0xc157b8: b               #0xc157f8
    // 0xc157bc: LoadField: r2 = r3->field_7
    //     0xc157bc: ldur            w2, [x3, #7]
    // 0xc157c0: DecompressPointer r2
    //     0xc157c0: add             x2, x2, HEAP, lsl #32
    // 0xc157c4: cmp             w2, NULL
    // 0xc157c8: b.ne            #0xc157d4
    // 0xc157cc: r0 = Null
    //     0xc157cc: mov             x0, NULL
    // 0xc157d0: b               #0xc157f8
    // 0xc157d4: stp             xzr, x2, [SP]
    // 0xc157d8: r0 = []()
    //     0xc157d8: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xc157dc: r1 = LoadClassIdInstr(r0)
    //     0xc157dc: ldur            x1, [x0, #-1]
    //     0xc157e0: ubfx            x1, x1, #0xc, #0x14
    // 0xc157e4: str             x0, [SP]
    // 0xc157e8: mov             x0, x1
    // 0xc157ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc157ec: sub             lr, x0, #1, lsl #12
    //     0xc157f0: ldr             lr, [x21, lr, lsl #3]
    //     0xc157f4: blr             lr
    // 0xc157f8: cmp             w0, NULL
    // 0xc157fc: b.ne            #0xc15808
    // 0xc15800: r3 = ""
    //     0xc15800: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15804: b               #0xc1580c
    // 0xc15808: mov             x3, x0
    // 0xc1580c: ldur            x0, [fp, #-8]
    // 0xc15810: ldur            x2, [fp, #-0x20]
    // 0xc15814: stur            x3, [fp, #-0x28]
    // 0xc15818: LoadField: r1 = r2->field_13
    //     0xc15818: ldur            w1, [x2, #0x13]
    // 0xc1581c: DecompressPointer r1
    //     0xc1581c: add             x1, x1, HEAP, lsl #32
    // 0xc15820: r0 = of()
    //     0xc15820: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc15824: LoadField: r1 = r0->field_87
    //     0xc15824: ldur            w1, [x0, #0x87]
    // 0xc15828: DecompressPointer r1
    //     0xc15828: add             x1, x1, HEAP, lsl #32
    // 0xc1582c: LoadField: r0 = r1->field_7
    //     0xc1582c: ldur            w0, [x1, #7]
    // 0xc15830: DecompressPointer r0
    //     0xc15830: add             x0, x0, HEAP, lsl #32
    // 0xc15834: stur            x0, [fp, #-0x38]
    // 0xc15838: r1 = Instance_Color
    //     0xc15838: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc1583c: d0 = 0.500000
    //     0xc1583c: fmov            d0, #0.50000000
    // 0xc15840: r0 = withOpacity()
    //     0xc15840: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc15844: r16 = 16.000000
    //     0xc15844: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc15848: ldr             x16, [x16, #0x188]
    // 0xc1584c: stp             x0, x16, [SP]
    // 0xc15850: ldur            x1, [fp, #-0x38]
    // 0xc15854: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc15854: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc15858: ldr             x4, [x4, #0xaa0]
    // 0xc1585c: r0 = copyWith()
    //     0xc1585c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc15860: stur            x0, [fp, #-0x38]
    // 0xc15864: r0 = Text()
    //     0xc15864: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc15868: mov             x1, x0
    // 0xc1586c: ldur            x0, [fp, #-0x28]
    // 0xc15870: stur            x1, [fp, #-0x48]
    // 0xc15874: StoreField: r1->field_b = r0
    //     0xc15874: stur            w0, [x1, #0xb]
    // 0xc15878: ldur            x0, [fp, #-0x38]
    // 0xc1587c: StoreField: r1->field_13 = r0
    //     0xc1587c: stur            w0, [x1, #0x13]
    // 0xc15880: r0 = Center()
    //     0xc15880: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc15884: mov             x1, x0
    // 0xc15888: r0 = Instance_Alignment
    //     0xc15888: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc1588c: ldr             x0, [x0, #0xb10]
    // 0xc15890: stur            x1, [fp, #-0x28]
    // 0xc15894: StoreField: r1->field_f = r0
    //     0xc15894: stur            w0, [x1, #0xf]
    // 0xc15898: ldur            x0, [fp, #-0x48]
    // 0xc1589c: StoreField: r1->field_b = r0
    //     0xc1589c: stur            w0, [x1, #0xb]
    // 0xc158a0: r0 = Container()
    //     0xc158a0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc158a4: stur            x0, [fp, #-0x38]
    // 0xc158a8: r16 = 34.000000
    //     0xc158a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc158ac: ldr             x16, [x16, #0x978]
    // 0xc158b0: r30 = 34.000000
    //     0xc158b0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc158b4: ldr             lr, [lr, #0x978]
    // 0xc158b8: stp             lr, x16, [SP, #0x18]
    // 0xc158bc: r16 = Instance_EdgeInsets
    //     0xc158bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc158c0: ldr             x16, [x16, #0x980]
    // 0xc158c4: ldur            lr, [fp, #-0x30]
    // 0xc158c8: stp             lr, x16, [SP, #8]
    // 0xc158cc: ldur            x16, [fp, #-0x28]
    // 0xc158d0: str             x16, [SP]
    // 0xc158d4: mov             x1, x0
    // 0xc158d8: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc158d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc158dc: ldr             x4, [x4, #0x988]
    // 0xc158e0: r0 = Container()
    //     0xc158e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc158e4: ldur            x0, [fp, #-8]
    // 0xc158e8: LoadField: r1 = r0->field_b
    //     0xc158e8: ldur            w1, [x0, #0xb]
    // 0xc158ec: DecompressPointer r1
    //     0xc158ec: add             x1, x1, HEAP, lsl #32
    // 0xc158f0: cmp             w1, NULL
    // 0xc158f4: b.eq            #0xc166b0
    // 0xc158f8: LoadField: r2 = r1->field_b
    //     0xc158f8: ldur            w2, [x1, #0xb]
    // 0xc158fc: DecompressPointer r2
    //     0xc158fc: add             x2, x2, HEAP, lsl #32
    // 0xc15900: cmp             w2, NULL
    // 0xc15904: b.ne            #0xc15910
    // 0xc15908: r1 = Null
    //     0xc15908: mov             x1, NULL
    // 0xc1590c: b               #0xc15918
    // 0xc15910: LoadField: r1 = r2->field_7
    //     0xc15910: ldur            w1, [x2, #7]
    // 0xc15914: DecompressPointer r1
    //     0xc15914: add             x1, x1, HEAP, lsl #32
    // 0xc15918: cmp             w1, NULL
    // 0xc1591c: b.ne            #0xc15928
    // 0xc15920: r3 = ""
    //     0xc15920: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15924: b               #0xc1592c
    // 0xc15928: mov             x3, x1
    // 0xc1592c: ldur            x2, [fp, #-0x20]
    // 0xc15930: stur            x3, [fp, #-0x28]
    // 0xc15934: LoadField: r1 = r2->field_13
    //     0xc15934: ldur            w1, [x2, #0x13]
    // 0xc15938: DecompressPointer r1
    //     0xc15938: add             x1, x1, HEAP, lsl #32
    // 0xc1593c: r0 = of()
    //     0xc1593c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc15940: LoadField: r1 = r0->field_87
    //     0xc15940: ldur            w1, [x0, #0x87]
    // 0xc15944: DecompressPointer r1
    //     0xc15944: add             x1, x1, HEAP, lsl #32
    // 0xc15948: LoadField: r0 = r1->field_7
    //     0xc15948: ldur            w0, [x1, #7]
    // 0xc1594c: DecompressPointer r0
    //     0xc1594c: add             x0, x0, HEAP, lsl #32
    // 0xc15950: r16 = 14.000000
    //     0xc15950: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc15954: ldr             x16, [x16, #0x1d8]
    // 0xc15958: r30 = Instance_Color
    //     0xc15958: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc1595c: stp             lr, x16, [SP]
    // 0xc15960: mov             x1, x0
    // 0xc15964: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc15964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc15968: ldr             x4, [x4, #0xaa0]
    // 0xc1596c: r0 = copyWith()
    //     0xc1596c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc15970: stur            x0, [fp, #-0x30]
    // 0xc15974: r0 = Text()
    //     0xc15974: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc15978: mov             x2, x0
    // 0xc1597c: ldur            x0, [fp, #-0x28]
    // 0xc15980: stur            x2, [fp, #-0x48]
    // 0xc15984: StoreField: r2->field_b = r0
    //     0xc15984: stur            w0, [x2, #0xb]
    // 0xc15988: ldur            x0, [fp, #-0x30]
    // 0xc1598c: StoreField: r2->field_13 = r0
    //     0xc1598c: stur            w0, [x2, #0x13]
    // 0xc15990: ldur            x0, [fp, #-8]
    // 0xc15994: LoadField: r1 = r0->field_b
    //     0xc15994: ldur            w1, [x0, #0xb]
    // 0xc15998: DecompressPointer r1
    //     0xc15998: add             x1, x1, HEAP, lsl #32
    // 0xc1599c: cmp             w1, NULL
    // 0xc159a0: b.eq            #0xc166b4
    // 0xc159a4: LoadField: r3 = r1->field_b
    //     0xc159a4: ldur            w3, [x1, #0xb]
    // 0xc159a8: DecompressPointer r3
    //     0xc159a8: add             x3, x3, HEAP, lsl #32
    // 0xc159ac: cmp             w3, NULL
    // 0xc159b0: b.ne            #0xc159bc
    // 0xc159b4: r1 = Null
    //     0xc159b4: mov             x1, NULL
    // 0xc159b8: b               #0xc159c4
    // 0xc159bc: LoadField: r1 = r3->field_1f
    //     0xc159bc: ldur            w1, [x3, #0x1f]
    // 0xc159c0: DecompressPointer r1
    //     0xc159c0: add             x1, x1, HEAP, lsl #32
    // 0xc159c4: cmp             w1, NULL
    // 0xc159c8: b.ne            #0xc159d4
    // 0xc159cc: r4 = ""
    //     0xc159cc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc159d0: b               #0xc159d8
    // 0xc159d4: mov             x4, x1
    // 0xc159d8: ldur            x3, [fp, #-0x20]
    // 0xc159dc: stur            x4, [fp, #-0x28]
    // 0xc159e0: LoadField: r1 = r3->field_13
    //     0xc159e0: ldur            w1, [x3, #0x13]
    // 0xc159e4: DecompressPointer r1
    //     0xc159e4: add             x1, x1, HEAP, lsl #32
    // 0xc159e8: r0 = of()
    //     0xc159e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc159ec: LoadField: r1 = r0->field_87
    //     0xc159ec: ldur            w1, [x0, #0x87]
    // 0xc159f0: DecompressPointer r1
    //     0xc159f0: add             x1, x1, HEAP, lsl #32
    // 0xc159f4: LoadField: r0 = r1->field_33
    //     0xc159f4: ldur            w0, [x1, #0x33]
    // 0xc159f8: DecompressPointer r0
    //     0xc159f8: add             x0, x0, HEAP, lsl #32
    // 0xc159fc: stur            x0, [fp, #-0x30]
    // 0xc15a00: cmp             w0, NULL
    // 0xc15a04: b.ne            #0xc15a10
    // 0xc15a08: r4 = Null
    //     0xc15a08: mov             x4, NULL
    // 0xc15a0c: b               #0xc15a38
    // 0xc15a10: r1 = Instance_Color
    //     0xc15a10: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc15a14: d0 = 0.500000
    //     0xc15a14: fmov            d0, #0.50000000
    // 0xc15a18: r0 = withOpacity()
    //     0xc15a18: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc15a1c: r16 = 10.000000
    //     0xc15a1c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc15a20: stp             x0, x16, [SP]
    // 0xc15a24: ldur            x1, [fp, #-0x30]
    // 0xc15a28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc15a28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc15a2c: ldr             x4, [x4, #0xaa0]
    // 0xc15a30: r0 = copyWith()
    //     0xc15a30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc15a34: mov             x4, x0
    // 0xc15a38: ldur            x1, [fp, #-8]
    // 0xc15a3c: ldur            x3, [fp, #-0x38]
    // 0xc15a40: ldur            x0, [fp, #-0x48]
    // 0xc15a44: ldur            x2, [fp, #-0x28]
    // 0xc15a48: stur            x4, [fp, #-0x30]
    // 0xc15a4c: r0 = Text()
    //     0xc15a4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc15a50: mov             x1, x0
    // 0xc15a54: ldur            x0, [fp, #-0x28]
    // 0xc15a58: stur            x1, [fp, #-0x50]
    // 0xc15a5c: StoreField: r1->field_b = r0
    //     0xc15a5c: stur            w0, [x1, #0xb]
    // 0xc15a60: ldur            x0, [fp, #-0x30]
    // 0xc15a64: StoreField: r1->field_13 = r0
    //     0xc15a64: stur            w0, [x1, #0x13]
    // 0xc15a68: r0 = Padding()
    //     0xc15a68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc15a6c: mov             x3, x0
    // 0xc15a70: r0 = Instance_EdgeInsets
    //     0xc15a70: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xc15a74: ldr             x0, [x0, #0xe90]
    // 0xc15a78: stur            x3, [fp, #-0x28]
    // 0xc15a7c: StoreField: r3->field_f = r0
    //     0xc15a7c: stur            w0, [x3, #0xf]
    // 0xc15a80: ldur            x0, [fp, #-0x50]
    // 0xc15a84: StoreField: r3->field_b = r0
    //     0xc15a84: stur            w0, [x3, #0xb]
    // 0xc15a88: r1 = Null
    //     0xc15a88: mov             x1, NULL
    // 0xc15a8c: r2 = 4
    //     0xc15a8c: movz            x2, #0x4
    // 0xc15a90: r0 = AllocateArray()
    //     0xc15a90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc15a94: mov             x2, x0
    // 0xc15a98: ldur            x0, [fp, #-0x48]
    // 0xc15a9c: stur            x2, [fp, #-0x30]
    // 0xc15aa0: StoreField: r2->field_f = r0
    //     0xc15aa0: stur            w0, [x2, #0xf]
    // 0xc15aa4: ldur            x0, [fp, #-0x28]
    // 0xc15aa8: StoreField: r2->field_13 = r0
    //     0xc15aa8: stur            w0, [x2, #0x13]
    // 0xc15aac: r1 = <Widget>
    //     0xc15aac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15ab0: r0 = AllocateGrowableArray()
    //     0xc15ab0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15ab4: mov             x1, x0
    // 0xc15ab8: ldur            x0, [fp, #-0x30]
    // 0xc15abc: stur            x1, [fp, #-0x28]
    // 0xc15ac0: StoreField: r1->field_f = r0
    //     0xc15ac0: stur            w0, [x1, #0xf]
    // 0xc15ac4: r2 = 4
    //     0xc15ac4: movz            x2, #0x4
    // 0xc15ac8: StoreField: r1->field_b = r2
    //     0xc15ac8: stur            w2, [x1, #0xb]
    // 0xc15acc: r0 = Column()
    //     0xc15acc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc15ad0: mov             x3, x0
    // 0xc15ad4: r0 = Instance_Axis
    //     0xc15ad4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc15ad8: stur            x3, [fp, #-0x30]
    // 0xc15adc: StoreField: r3->field_f = r0
    //     0xc15adc: stur            w0, [x3, #0xf]
    // 0xc15ae0: r4 = Instance_MainAxisAlignment
    //     0xc15ae0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc15ae4: ldr             x4, [x4, #0xa08]
    // 0xc15ae8: StoreField: r3->field_13 = r4
    //     0xc15ae8: stur            w4, [x3, #0x13]
    // 0xc15aec: r5 = Instance_MainAxisSize
    //     0xc15aec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15af0: ldr             x5, [x5, #0xa10]
    // 0xc15af4: ArrayStore: r3[0] = r5  ; List_4
    //     0xc15af4: stur            w5, [x3, #0x17]
    // 0xc15af8: r1 = Instance_CrossAxisAlignment
    //     0xc15af8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc15afc: ldr             x1, [x1, #0x890]
    // 0xc15b00: StoreField: r3->field_1b = r1
    //     0xc15b00: stur            w1, [x3, #0x1b]
    // 0xc15b04: r6 = Instance_VerticalDirection
    //     0xc15b04: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc15b08: ldr             x6, [x6, #0xa20]
    // 0xc15b0c: StoreField: r3->field_23 = r6
    //     0xc15b0c: stur            w6, [x3, #0x23]
    // 0xc15b10: r7 = Instance_Clip
    //     0xc15b10: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc15b14: ldr             x7, [x7, #0x38]
    // 0xc15b18: StoreField: r3->field_2b = r7
    //     0xc15b18: stur            w7, [x3, #0x2b]
    // 0xc15b1c: StoreField: r3->field_2f = rZR
    //     0xc15b1c: stur            xzr, [x3, #0x2f]
    // 0xc15b20: ldur            x1, [fp, #-0x28]
    // 0xc15b24: StoreField: r3->field_b = r1
    //     0xc15b24: stur            w1, [x3, #0xb]
    // 0xc15b28: r1 = Null
    //     0xc15b28: mov             x1, NULL
    // 0xc15b2c: r2 = 6
    //     0xc15b2c: movz            x2, #0x6
    // 0xc15b30: r0 = AllocateArray()
    //     0xc15b30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc15b34: mov             x2, x0
    // 0xc15b38: ldur            x0, [fp, #-0x38]
    // 0xc15b3c: stur            x2, [fp, #-0x28]
    // 0xc15b40: StoreField: r2->field_f = r0
    //     0xc15b40: stur            w0, [x2, #0xf]
    // 0xc15b44: r16 = Instance_SizedBox
    //     0xc15b44: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xc15b48: ldr             x16, [x16, #0x998]
    // 0xc15b4c: StoreField: r2->field_13 = r16
    //     0xc15b4c: stur            w16, [x2, #0x13]
    // 0xc15b50: ldur            x0, [fp, #-0x30]
    // 0xc15b54: ArrayStore: r2[0] = r0  ; List_4
    //     0xc15b54: stur            w0, [x2, #0x17]
    // 0xc15b58: r1 = <Widget>
    //     0xc15b58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15b5c: r0 = AllocateGrowableArray()
    //     0xc15b5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15b60: mov             x1, x0
    // 0xc15b64: ldur            x0, [fp, #-0x28]
    // 0xc15b68: stur            x1, [fp, #-0x30]
    // 0xc15b6c: StoreField: r1->field_f = r0
    //     0xc15b6c: stur            w0, [x1, #0xf]
    // 0xc15b70: r2 = 6
    //     0xc15b70: movz            x2, #0x6
    // 0xc15b74: StoreField: r1->field_b = r2
    //     0xc15b74: stur            w2, [x1, #0xb]
    // 0xc15b78: r0 = Row()
    //     0xc15b78: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc15b7c: mov             x2, x0
    // 0xc15b80: r1 = Instance_Axis
    //     0xc15b80: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc15b84: stur            x2, [fp, #-0x28]
    // 0xc15b88: StoreField: r2->field_f = r1
    //     0xc15b88: stur            w1, [x2, #0xf]
    // 0xc15b8c: r3 = Instance_MainAxisAlignment
    //     0xc15b8c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc15b90: ldr             x3, [x3, #0xa08]
    // 0xc15b94: StoreField: r2->field_13 = r3
    //     0xc15b94: stur            w3, [x2, #0x13]
    // 0xc15b98: r4 = Instance_MainAxisSize
    //     0xc15b98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15b9c: ldr             x4, [x4, #0xa10]
    // 0xc15ba0: ArrayStore: r2[0] = r4  ; List_4
    //     0xc15ba0: stur            w4, [x2, #0x17]
    // 0xc15ba4: r5 = Instance_CrossAxisAlignment
    //     0xc15ba4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc15ba8: ldr             x5, [x5, #0xa18]
    // 0xc15bac: StoreField: r2->field_1b = r5
    //     0xc15bac: stur            w5, [x2, #0x1b]
    // 0xc15bb0: r6 = Instance_VerticalDirection
    //     0xc15bb0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc15bb4: ldr             x6, [x6, #0xa20]
    // 0xc15bb8: StoreField: r2->field_23 = r6
    //     0xc15bb8: stur            w6, [x2, #0x23]
    // 0xc15bbc: r7 = Instance_Clip
    //     0xc15bbc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc15bc0: ldr             x7, [x7, #0x38]
    // 0xc15bc4: StoreField: r2->field_2b = r7
    //     0xc15bc4: stur            w7, [x2, #0x2b]
    // 0xc15bc8: StoreField: r2->field_2f = rZR
    //     0xc15bc8: stur            xzr, [x2, #0x2f]
    // 0xc15bcc: ldur            x0, [fp, #-0x30]
    // 0xc15bd0: StoreField: r2->field_b = r0
    //     0xc15bd0: stur            w0, [x2, #0xb]
    // 0xc15bd4: ldur            x8, [fp, #-8]
    // 0xc15bd8: LoadField: r0 = r8->field_b
    //     0xc15bd8: ldur            w0, [x8, #0xb]
    // 0xc15bdc: DecompressPointer r0
    //     0xc15bdc: add             x0, x0, HEAP, lsl #32
    // 0xc15be0: cmp             w0, NULL
    // 0xc15be4: b.eq            #0xc166b8
    // 0xc15be8: LoadField: r9 = r0->field_b
    //     0xc15be8: ldur            w9, [x0, #0xb]
    // 0xc15bec: DecompressPointer r9
    //     0xc15bec: add             x9, x9, HEAP, lsl #32
    // 0xc15bf0: cmp             w9, NULL
    // 0xc15bf4: b.ne            #0xc15c00
    // 0xc15bf8: r0 = Null
    //     0xc15bf8: mov             x0, NULL
    // 0xc15bfc: b               #0xc15c34
    // 0xc15c00: LoadField: r0 = r9->field_f
    //     0xc15c00: ldur            w0, [x9, #0xf]
    // 0xc15c04: DecompressPointer r0
    //     0xc15c04: add             x0, x0, HEAP, lsl #32
    // 0xc15c08: r9 = 60
    //     0xc15c08: movz            x9, #0x3c
    // 0xc15c0c: branchIfSmi(r0, 0xc15c18)
    //     0xc15c0c: tbz             w0, #0, #0xc15c18
    // 0xc15c10: r9 = LoadClassIdInstr(r0)
    //     0xc15c10: ldur            x9, [x0, #-1]
    //     0xc15c14: ubfx            x9, x9, #0xc, #0x14
    // 0xc15c18: str             x0, [SP]
    // 0xc15c1c: mov             x0, x9
    // 0xc15c20: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15c20: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc15c24: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc15c24: movz            x17, #0x2700
    //     0xc15c28: add             lr, x0, x17
    //     0xc15c2c: ldr             lr, [x21, lr, lsl #3]
    //     0xc15c30: blr             lr
    // 0xc15c34: cmp             w0, NULL
    // 0xc15c38: b.ne            #0xc15c44
    // 0xc15c3c: r1 = ""
    //     0xc15c3c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15c40: b               #0xc15c48
    // 0xc15c44: mov             x1, x0
    // 0xc15c48: r0 = parse()
    //     0xc15c48: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc15c4c: mov             v1.16b, v0.16b
    // 0xc15c50: d0 = 4.000000
    //     0xc15c50: fmov            d0, #4.00000000
    // 0xc15c54: fcmp            d1, d0
    // 0xc15c58: b.lt            #0xc15c68
    // 0xc15c5c: r1 = Instance_Color
    //     0xc15c5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc15c60: ldr             x1, [x1, #0x858]
    // 0xc15c64: b               #0xc15da8
    // 0xc15c68: ldur            x1, [fp, #-8]
    // 0xc15c6c: LoadField: r0 = r1->field_b
    //     0xc15c6c: ldur            w0, [x1, #0xb]
    // 0xc15c70: DecompressPointer r0
    //     0xc15c70: add             x0, x0, HEAP, lsl #32
    // 0xc15c74: cmp             w0, NULL
    // 0xc15c78: b.eq            #0xc166bc
    // 0xc15c7c: LoadField: r2 = r0->field_b
    //     0xc15c7c: ldur            w2, [x0, #0xb]
    // 0xc15c80: DecompressPointer r2
    //     0xc15c80: add             x2, x2, HEAP, lsl #32
    // 0xc15c84: cmp             w2, NULL
    // 0xc15c88: b.ne            #0xc15c94
    // 0xc15c8c: r0 = Null
    //     0xc15c8c: mov             x0, NULL
    // 0xc15c90: b               #0xc15cc8
    // 0xc15c94: LoadField: r0 = r2->field_f
    //     0xc15c94: ldur            w0, [x2, #0xf]
    // 0xc15c98: DecompressPointer r0
    //     0xc15c98: add             x0, x0, HEAP, lsl #32
    // 0xc15c9c: r2 = 60
    //     0xc15c9c: movz            x2, #0x3c
    // 0xc15ca0: branchIfSmi(r0, 0xc15cac)
    //     0xc15ca0: tbz             w0, #0, #0xc15cac
    // 0xc15ca4: r2 = LoadClassIdInstr(r0)
    //     0xc15ca4: ldur            x2, [x0, #-1]
    //     0xc15ca8: ubfx            x2, x2, #0xc, #0x14
    // 0xc15cac: str             x0, [SP]
    // 0xc15cb0: mov             x0, x2
    // 0xc15cb4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15cb4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc15cb8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc15cb8: movz            x17, #0x2700
    //     0xc15cbc: add             lr, x0, x17
    //     0xc15cc0: ldr             lr, [x21, lr, lsl #3]
    //     0xc15cc4: blr             lr
    // 0xc15cc8: cmp             w0, NULL
    // 0xc15ccc: b.ne            #0xc15cd8
    // 0xc15cd0: r1 = ""
    //     0xc15cd0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15cd4: b               #0xc15cdc
    // 0xc15cd8: mov             x1, x0
    // 0xc15cdc: r0 = parse()
    //     0xc15cdc: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc15ce0: mov             v1.16b, v0.16b
    // 0xc15ce4: d0 = 3.500000
    //     0xc15ce4: fmov            d0, #3.50000000
    // 0xc15ce8: fcmp            d1, d0
    // 0xc15cec: b.lt            #0xc15d08
    // 0xc15cf0: r1 = Instance_Color
    //     0xc15cf0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc15cf4: ldr             x1, [x1, #0x858]
    // 0xc15cf8: d0 = 0.700000
    //     0xc15cf8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc15cfc: ldr             d0, [x17, #0xf48]
    // 0xc15d00: r0 = withOpacity()
    //     0xc15d00: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc15d04: b               #0xc15da4
    // 0xc15d08: ldur            x1, [fp, #-8]
    // 0xc15d0c: LoadField: r0 = r1->field_b
    //     0xc15d0c: ldur            w0, [x1, #0xb]
    // 0xc15d10: DecompressPointer r0
    //     0xc15d10: add             x0, x0, HEAP, lsl #32
    // 0xc15d14: cmp             w0, NULL
    // 0xc15d18: b.eq            #0xc166c0
    // 0xc15d1c: LoadField: r2 = r0->field_b
    //     0xc15d1c: ldur            w2, [x0, #0xb]
    // 0xc15d20: DecompressPointer r2
    //     0xc15d20: add             x2, x2, HEAP, lsl #32
    // 0xc15d24: cmp             w2, NULL
    // 0xc15d28: b.ne            #0xc15d34
    // 0xc15d2c: r0 = Null
    //     0xc15d2c: mov             x0, NULL
    // 0xc15d30: b               #0xc15d68
    // 0xc15d34: LoadField: r0 = r2->field_f
    //     0xc15d34: ldur            w0, [x2, #0xf]
    // 0xc15d38: DecompressPointer r0
    //     0xc15d38: add             x0, x0, HEAP, lsl #32
    // 0xc15d3c: r2 = 60
    //     0xc15d3c: movz            x2, #0x3c
    // 0xc15d40: branchIfSmi(r0, 0xc15d4c)
    //     0xc15d40: tbz             w0, #0, #0xc15d4c
    // 0xc15d44: r2 = LoadClassIdInstr(r0)
    //     0xc15d44: ldur            x2, [x0, #-1]
    //     0xc15d48: ubfx            x2, x2, #0xc, #0x14
    // 0xc15d4c: str             x0, [SP]
    // 0xc15d50: mov             x0, x2
    // 0xc15d54: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15d54: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc15d58: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc15d58: movz            x17, #0x2700
    //     0xc15d5c: add             lr, x0, x17
    //     0xc15d60: ldr             lr, [x21, lr, lsl #3]
    //     0xc15d64: blr             lr
    // 0xc15d68: cmp             w0, NULL
    // 0xc15d6c: b.ne            #0xc15d78
    // 0xc15d70: r1 = ""
    //     0xc15d70: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15d74: b               #0xc15d7c
    // 0xc15d78: mov             x1, x0
    // 0xc15d7c: r0 = parse()
    //     0xc15d7c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc15d80: mov             v1.16b, v0.16b
    // 0xc15d84: d0 = 2.000000
    //     0xc15d84: fmov            d0, #2.00000000
    // 0xc15d88: fcmp            d1, d0
    // 0xc15d8c: b.lt            #0xc15d9c
    // 0xc15d90: r0 = Instance_Color
    //     0xc15d90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xc15d94: ldr             x0, [x0, #0x860]
    // 0xc15d98: b               #0xc15da4
    // 0xc15d9c: r0 = Instance_Color
    //     0xc15d9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xc15da0: ldr             x0, [x0, #0x50]
    // 0xc15da4: mov             x1, x0
    // 0xc15da8: ldur            x0, [fp, #-8]
    // 0xc15dac: stur            x1, [fp, #-0x30]
    // 0xc15db0: r0 = ColorFilter()
    //     0xc15db0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc15db4: mov             x1, x0
    // 0xc15db8: ldur            x0, [fp, #-0x30]
    // 0xc15dbc: stur            x1, [fp, #-0x38]
    // 0xc15dc0: StoreField: r1->field_7 = r0
    //     0xc15dc0: stur            w0, [x1, #7]
    // 0xc15dc4: r0 = Instance_BlendMode
    //     0xc15dc4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc15dc8: ldr             x0, [x0, #0xb30]
    // 0xc15dcc: StoreField: r1->field_b = r0
    //     0xc15dcc: stur            w0, [x1, #0xb]
    // 0xc15dd0: r0 = 1
    //     0xc15dd0: movz            x0, #0x1
    // 0xc15dd4: StoreField: r1->field_13 = r0
    //     0xc15dd4: stur            x0, [x1, #0x13]
    // 0xc15dd8: r0 = SvgPicture()
    //     0xc15dd8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc15ddc: stur            x0, [fp, #-0x30]
    // 0xc15de0: ldur            x16, [fp, #-0x38]
    // 0xc15de4: str             x16, [SP]
    // 0xc15de8: mov             x1, x0
    // 0xc15dec: r2 = "assets/images/green_star.svg"
    //     0xc15dec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xc15df0: ldr             x2, [x2, #0x9a0]
    // 0xc15df4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc15df4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc15df8: ldr             x4, [x4, #0xa38]
    // 0xc15dfc: r0 = SvgPicture.asset()
    //     0xc15dfc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc15e00: ldur            x1, [fp, #-8]
    // 0xc15e04: LoadField: r0 = r1->field_b
    //     0xc15e04: ldur            w0, [x1, #0xb]
    // 0xc15e08: DecompressPointer r0
    //     0xc15e08: add             x0, x0, HEAP, lsl #32
    // 0xc15e0c: cmp             w0, NULL
    // 0xc15e10: b.eq            #0xc166c4
    // 0xc15e14: LoadField: r2 = r0->field_b
    //     0xc15e14: ldur            w2, [x0, #0xb]
    // 0xc15e18: DecompressPointer r2
    //     0xc15e18: add             x2, x2, HEAP, lsl #32
    // 0xc15e1c: cmp             w2, NULL
    // 0xc15e20: b.ne            #0xc15e2c
    // 0xc15e24: r0 = Null
    //     0xc15e24: mov             x0, NULL
    // 0xc15e28: b               #0xc15e60
    // 0xc15e2c: LoadField: r0 = r2->field_f
    //     0xc15e2c: ldur            w0, [x2, #0xf]
    // 0xc15e30: DecompressPointer r0
    //     0xc15e30: add             x0, x0, HEAP, lsl #32
    // 0xc15e34: r2 = 60
    //     0xc15e34: movz            x2, #0x3c
    // 0xc15e38: branchIfSmi(r0, 0xc15e44)
    //     0xc15e38: tbz             w0, #0, #0xc15e44
    // 0xc15e3c: r2 = LoadClassIdInstr(r0)
    //     0xc15e3c: ldur            x2, [x0, #-1]
    //     0xc15e40: ubfx            x2, x2, #0xc, #0x14
    // 0xc15e44: str             x0, [SP]
    // 0xc15e48: mov             x0, x2
    // 0xc15e4c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc15e4c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc15e50: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc15e50: movz            x17, #0x2700
    //     0xc15e54: add             lr, x0, x17
    //     0xc15e58: ldr             lr, [x21, lr, lsl #3]
    //     0xc15e5c: blr             lr
    // 0xc15e60: cmp             w0, NULL
    // 0xc15e64: b.ne            #0xc15e70
    // 0xc15e68: r5 = ""
    //     0xc15e68: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc15e6c: b               #0xc15e74
    // 0xc15e70: mov             x5, x0
    // 0xc15e74: ldur            x3, [fp, #-0x20]
    // 0xc15e78: ldur            x4, [fp, #-0x18]
    // 0xc15e7c: ldur            x2, [fp, #-0x28]
    // 0xc15e80: ldur            x0, [fp, #-0x30]
    // 0xc15e84: stur            x5, [fp, #-0x38]
    // 0xc15e88: LoadField: r1 = r3->field_13
    //     0xc15e88: ldur            w1, [x3, #0x13]
    // 0xc15e8c: DecompressPointer r1
    //     0xc15e8c: add             x1, x1, HEAP, lsl #32
    // 0xc15e90: r0 = of()
    //     0xc15e90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc15e94: LoadField: r1 = r0->field_87
    //     0xc15e94: ldur            w1, [x0, #0x87]
    // 0xc15e98: DecompressPointer r1
    //     0xc15e98: add             x1, x1, HEAP, lsl #32
    // 0xc15e9c: LoadField: r0 = r1->field_7
    //     0xc15e9c: ldur            w0, [x1, #7]
    // 0xc15ea0: DecompressPointer r0
    //     0xc15ea0: add             x0, x0, HEAP, lsl #32
    // 0xc15ea4: r16 = 12.000000
    //     0xc15ea4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc15ea8: ldr             x16, [x16, #0x9e8]
    // 0xc15eac: r30 = Instance_Color
    //     0xc15eac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc15eb0: stp             lr, x16, [SP]
    // 0xc15eb4: mov             x1, x0
    // 0xc15eb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc15eb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc15ebc: ldr             x4, [x4, #0xaa0]
    // 0xc15ec0: r0 = copyWith()
    //     0xc15ec0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc15ec4: stur            x0, [fp, #-0x48]
    // 0xc15ec8: r0 = Text()
    //     0xc15ec8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc15ecc: mov             x3, x0
    // 0xc15ed0: ldur            x0, [fp, #-0x38]
    // 0xc15ed4: stur            x3, [fp, #-0x50]
    // 0xc15ed8: StoreField: r3->field_b = r0
    //     0xc15ed8: stur            w0, [x3, #0xb]
    // 0xc15edc: ldur            x0, [fp, #-0x48]
    // 0xc15ee0: StoreField: r3->field_13 = r0
    //     0xc15ee0: stur            w0, [x3, #0x13]
    // 0xc15ee4: r1 = Null
    //     0xc15ee4: mov             x1, NULL
    // 0xc15ee8: r2 = 6
    //     0xc15ee8: movz            x2, #0x6
    // 0xc15eec: r0 = AllocateArray()
    //     0xc15eec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc15ef0: mov             x2, x0
    // 0xc15ef4: ldur            x0, [fp, #-0x30]
    // 0xc15ef8: stur            x2, [fp, #-0x38]
    // 0xc15efc: StoreField: r2->field_f = r0
    //     0xc15efc: stur            w0, [x2, #0xf]
    // 0xc15f00: r16 = Instance_SizedBox
    //     0xc15f00: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xc15f04: ldr             x16, [x16, #0xe98]
    // 0xc15f08: StoreField: r2->field_13 = r16
    //     0xc15f08: stur            w16, [x2, #0x13]
    // 0xc15f0c: ldur            x0, [fp, #-0x50]
    // 0xc15f10: ArrayStore: r2[0] = r0  ; List_4
    //     0xc15f10: stur            w0, [x2, #0x17]
    // 0xc15f14: r1 = <Widget>
    //     0xc15f14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15f18: r0 = AllocateGrowableArray()
    //     0xc15f18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15f1c: mov             x1, x0
    // 0xc15f20: ldur            x0, [fp, #-0x38]
    // 0xc15f24: stur            x1, [fp, #-0x30]
    // 0xc15f28: StoreField: r1->field_f = r0
    //     0xc15f28: stur            w0, [x1, #0xf]
    // 0xc15f2c: r0 = 6
    //     0xc15f2c: movz            x0, #0x6
    // 0xc15f30: StoreField: r1->field_b = r0
    //     0xc15f30: stur            w0, [x1, #0xb]
    // 0xc15f34: r0 = Row()
    //     0xc15f34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc15f38: mov             x3, x0
    // 0xc15f3c: r0 = Instance_Axis
    //     0xc15f3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc15f40: stur            x3, [fp, #-0x38]
    // 0xc15f44: StoreField: r3->field_f = r0
    //     0xc15f44: stur            w0, [x3, #0xf]
    // 0xc15f48: r4 = Instance_MainAxisAlignment
    //     0xc15f48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc15f4c: ldr             x4, [x4, #0xa08]
    // 0xc15f50: StoreField: r3->field_13 = r4
    //     0xc15f50: stur            w4, [x3, #0x13]
    // 0xc15f54: r5 = Instance_MainAxisSize
    //     0xc15f54: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15f58: ldr             x5, [x5, #0xa10]
    // 0xc15f5c: ArrayStore: r3[0] = r5  ; List_4
    //     0xc15f5c: stur            w5, [x3, #0x17]
    // 0xc15f60: r6 = Instance_CrossAxisAlignment
    //     0xc15f60: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc15f64: ldr             x6, [x6, #0xa18]
    // 0xc15f68: StoreField: r3->field_1b = r6
    //     0xc15f68: stur            w6, [x3, #0x1b]
    // 0xc15f6c: r7 = Instance_VerticalDirection
    //     0xc15f6c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc15f70: ldr             x7, [x7, #0xa20]
    // 0xc15f74: StoreField: r3->field_23 = r7
    //     0xc15f74: stur            w7, [x3, #0x23]
    // 0xc15f78: r8 = Instance_Clip
    //     0xc15f78: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc15f7c: ldr             x8, [x8, #0x38]
    // 0xc15f80: StoreField: r3->field_2b = r8
    //     0xc15f80: stur            w8, [x3, #0x2b]
    // 0xc15f84: StoreField: r3->field_2f = rZR
    //     0xc15f84: stur            xzr, [x3, #0x2f]
    // 0xc15f88: ldur            x1, [fp, #-0x30]
    // 0xc15f8c: StoreField: r3->field_b = r1
    //     0xc15f8c: stur            w1, [x3, #0xb]
    // 0xc15f90: r1 = Null
    //     0xc15f90: mov             x1, NULL
    // 0xc15f94: r2 = 4
    //     0xc15f94: movz            x2, #0x4
    // 0xc15f98: r0 = AllocateArray()
    //     0xc15f98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc15f9c: mov             x2, x0
    // 0xc15fa0: ldur            x0, [fp, #-0x28]
    // 0xc15fa4: stur            x2, [fp, #-0x30]
    // 0xc15fa8: StoreField: r2->field_f = r0
    //     0xc15fa8: stur            w0, [x2, #0xf]
    // 0xc15fac: ldur            x0, [fp, #-0x38]
    // 0xc15fb0: StoreField: r2->field_13 = r0
    //     0xc15fb0: stur            w0, [x2, #0x13]
    // 0xc15fb4: r1 = <Widget>
    //     0xc15fb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc15fb8: r0 = AllocateGrowableArray()
    //     0xc15fb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc15fbc: mov             x1, x0
    // 0xc15fc0: ldur            x0, [fp, #-0x30]
    // 0xc15fc4: stur            x1, [fp, #-0x28]
    // 0xc15fc8: StoreField: r1->field_f = r0
    //     0xc15fc8: stur            w0, [x1, #0xf]
    // 0xc15fcc: r2 = 4
    //     0xc15fcc: movz            x2, #0x4
    // 0xc15fd0: StoreField: r1->field_b = r2
    //     0xc15fd0: stur            w2, [x1, #0xb]
    // 0xc15fd4: r0 = Row()
    //     0xc15fd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc15fd8: mov             x1, x0
    // 0xc15fdc: r0 = Instance_Axis
    //     0xc15fdc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc15fe0: stur            x1, [fp, #-0x30]
    // 0xc15fe4: StoreField: r1->field_f = r0
    //     0xc15fe4: stur            w0, [x1, #0xf]
    // 0xc15fe8: r0 = Instance_MainAxisAlignment
    //     0xc15fe8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc15fec: ldr             x0, [x0, #0xa8]
    // 0xc15ff0: StoreField: r1->field_13 = r0
    //     0xc15ff0: stur            w0, [x1, #0x13]
    // 0xc15ff4: r0 = Instance_MainAxisSize
    //     0xc15ff4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc15ff8: ldr             x0, [x0, #0xa10]
    // 0xc15ffc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc15ffc: stur            w0, [x1, #0x17]
    // 0xc16000: r2 = Instance_CrossAxisAlignment
    //     0xc16000: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc16004: ldr             x2, [x2, #0xa18]
    // 0xc16008: StoreField: r1->field_1b = r2
    //     0xc16008: stur            w2, [x1, #0x1b]
    // 0xc1600c: r3 = Instance_VerticalDirection
    //     0xc1600c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc16010: ldr             x3, [x3, #0xa20]
    // 0xc16014: StoreField: r1->field_23 = r3
    //     0xc16014: stur            w3, [x1, #0x23]
    // 0xc16018: r4 = Instance_Clip
    //     0xc16018: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc1601c: ldr             x4, [x4, #0x38]
    // 0xc16020: StoreField: r1->field_2b = r4
    //     0xc16020: stur            w4, [x1, #0x2b]
    // 0xc16024: StoreField: r1->field_2f = rZR
    //     0xc16024: stur            xzr, [x1, #0x2f]
    // 0xc16028: ldur            x5, [fp, #-0x28]
    // 0xc1602c: StoreField: r1->field_b = r5
    //     0xc1602c: stur            w5, [x1, #0xb]
    // 0xc16030: r0 = Container()
    //     0xc16030: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc16034: stur            x0, [fp, #-0x28]
    // 0xc16038: r16 = Instance_BoxDecoration
    //     0xc16038: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xc1603c: ldr             x16, [x16, #0x5a8]
    // 0xc16040: r30 = Instance_EdgeInsets
    //     0xc16040: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc16044: ldr             lr, [lr, #0x1f0]
    // 0xc16048: stp             lr, x16, [SP, #8]
    // 0xc1604c: ldur            x16, [fp, #-0x30]
    // 0xc16050: str             x16, [SP]
    // 0xc16054: mov             x1, x0
    // 0xc16058: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xc16058: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xc1605c: ldr             x4, [x4, #0xb40]
    // 0xc16060: r0 = Container()
    //     0xc16060: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc16064: ldur            x0, [fp, #-0x18]
    // 0xc16068: LoadField: r1 = r0->field_b
    //     0xc16068: ldur            w1, [x0, #0xb]
    // 0xc1606c: LoadField: r2 = r0->field_f
    //     0xc1606c: ldur            w2, [x0, #0xf]
    // 0xc16070: DecompressPointer r2
    //     0xc16070: add             x2, x2, HEAP, lsl #32
    // 0xc16074: LoadField: r3 = r2->field_b
    //     0xc16074: ldur            w3, [x2, #0xb]
    // 0xc16078: r2 = LoadInt32Instr(r1)
    //     0xc16078: sbfx            x2, x1, #1, #0x1f
    // 0xc1607c: stur            x2, [fp, #-0x40]
    // 0xc16080: r1 = LoadInt32Instr(r3)
    //     0xc16080: sbfx            x1, x3, #1, #0x1f
    // 0xc16084: cmp             x2, x1
    // 0xc16088: b.ne            #0xc16094
    // 0xc1608c: mov             x1, x0
    // 0xc16090: r0 = _growToNextCapacity()
    //     0xc16090: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc16094: ldur            x2, [fp, #-0x18]
    // 0xc16098: ldur            x3, [fp, #-0x40]
    // 0xc1609c: add             x0, x3, #1
    // 0xc160a0: lsl             x1, x0, #1
    // 0xc160a4: StoreField: r2->field_b = r1
    //     0xc160a4: stur            w1, [x2, #0xb]
    // 0xc160a8: LoadField: r1 = r2->field_f
    //     0xc160a8: ldur            w1, [x2, #0xf]
    // 0xc160ac: DecompressPointer r1
    //     0xc160ac: add             x1, x1, HEAP, lsl #32
    // 0xc160b0: ldur            x0, [fp, #-0x28]
    // 0xc160b4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc160b4: add             x25, x1, x3, lsl #2
    //     0xc160b8: add             x25, x25, #0xf
    //     0xc160bc: str             w0, [x25]
    //     0xc160c0: tbz             w0, #0, #0xc160dc
    //     0xc160c4: ldurb           w16, [x1, #-1]
    //     0xc160c8: ldurb           w17, [x0, #-1]
    //     0xc160cc: and             x16, x17, x16, lsr #2
    //     0xc160d0: tst             x16, HEAP, lsr #32
    //     0xc160d4: b.eq            #0xc160dc
    //     0xc160d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc160dc: ldur            x0, [fp, #-8]
    // 0xc160e0: LoadField: r1 = r0->field_b
    //     0xc160e0: ldur            w1, [x0, #0xb]
    // 0xc160e4: DecompressPointer r1
    //     0xc160e4: add             x1, x1, HEAP, lsl #32
    // 0xc160e8: cmp             w1, NULL
    // 0xc160ec: b.eq            #0xc166c8
    // 0xc160f0: LoadField: r3 = r1->field_b
    //     0xc160f0: ldur            w3, [x1, #0xb]
    // 0xc160f4: DecompressPointer r3
    //     0xc160f4: add             x3, x3, HEAP, lsl #32
    // 0xc160f8: cmp             w3, NULL
    // 0xc160fc: b.ne            #0xc16108
    // 0xc16100: r1 = Null
    //     0xc16100: mov             x1, NULL
    // 0xc16104: b               #0xc16110
    // 0xc16108: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xc16108: ldur            w1, [x3, #0x17]
    // 0xc1610c: DecompressPointer r1
    //     0xc1610c: add             x1, x1, HEAP, lsl #32
    // 0xc16110: cmp             w1, NULL
    // 0xc16114: b.ne            #0xc16120
    // 0xc16118: r4 = ""
    //     0xc16118: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc1611c: b               #0xc16124
    // 0xc16120: mov             x4, x1
    // 0xc16124: ldur            x3, [fp, #-0x20]
    // 0xc16128: stur            x4, [fp, #-0x28]
    // 0xc1612c: LoadField: r1 = r3->field_13
    //     0xc1612c: ldur            w1, [x3, #0x13]
    // 0xc16130: DecompressPointer r1
    //     0xc16130: add             x1, x1, HEAP, lsl #32
    // 0xc16134: r0 = of()
    //     0xc16134: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc16138: LoadField: r1 = r0->field_87
    //     0xc16138: ldur            w1, [x0, #0x87]
    // 0xc1613c: DecompressPointer r1
    //     0xc1613c: add             x1, x1, HEAP, lsl #32
    // 0xc16140: LoadField: r0 = r1->field_2b
    //     0xc16140: ldur            w0, [x1, #0x2b]
    // 0xc16144: DecompressPointer r0
    //     0xc16144: add             x0, x0, HEAP, lsl #32
    // 0xc16148: LoadField: r1 = r0->field_13
    //     0xc16148: ldur            w1, [x0, #0x13]
    // 0xc1614c: DecompressPointer r1
    //     0xc1614c: add             x1, x1, HEAP, lsl #32
    // 0xc16150: r16 = Instance_Color
    //     0xc16150: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc16154: stp             x16, x1, [SP]
    // 0xc16158: r1 = Instance_TextStyle
    //     0xc16158: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xc1615c: ldr             x1, [x1, #0x9b0]
    // 0xc16160: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xc16160: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xc16164: ldr             x4, [x4, #0x9b8]
    // 0xc16168: r0 = copyWith()
    //     0xc16168: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc1616c: ldur            x2, [fp, #-0x20]
    // 0xc16170: stur            x0, [fp, #-0x30]
    // 0xc16174: LoadField: r1 = r2->field_13
    //     0xc16174: ldur            w1, [x2, #0x13]
    // 0xc16178: DecompressPointer r1
    //     0xc16178: add             x1, x1, HEAP, lsl #32
    // 0xc1617c: r0 = of()
    //     0xc1617c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc16180: LoadField: r1 = r0->field_87
    //     0xc16180: ldur            w1, [x0, #0x87]
    // 0xc16184: DecompressPointer r1
    //     0xc16184: add             x1, x1, HEAP, lsl #32
    // 0xc16188: LoadField: r0 = r1->field_7
    //     0xc16188: ldur            w0, [x1, #7]
    // 0xc1618c: DecompressPointer r0
    //     0xc1618c: add             x0, x0, HEAP, lsl #32
    // 0xc16190: r16 = 12.000000
    //     0xc16190: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc16194: ldr             x16, [x16, #0x9e8]
    // 0xc16198: r30 = Instance_Color
    //     0xc16198: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc1619c: stp             lr, x16, [SP, #8]
    // 0xc161a0: r16 = Instance_FontWeight
    //     0xc161a0: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xc161a4: ldr             x16, [x16, #0x20]
    // 0xc161a8: str             x16, [SP]
    // 0xc161ac: mov             x1, x0
    // 0xc161b0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xc161b0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xc161b4: ldr             x4, [x4, #0xc48]
    // 0xc161b8: r0 = copyWith()
    //     0xc161b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc161bc: ldur            x2, [fp, #-0x20]
    // 0xc161c0: stur            x0, [fp, #-0x38]
    // 0xc161c4: LoadField: r1 = r2->field_13
    //     0xc161c4: ldur            w1, [x2, #0x13]
    // 0xc161c8: DecompressPointer r1
    //     0xc161c8: add             x1, x1, HEAP, lsl #32
    // 0xc161cc: r0 = of()
    //     0xc161cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc161d0: LoadField: r1 = r0->field_87
    //     0xc161d0: ldur            w1, [x0, #0x87]
    // 0xc161d4: DecompressPointer r1
    //     0xc161d4: add             x1, x1, HEAP, lsl #32
    // 0xc161d8: LoadField: r0 = r1->field_7
    //     0xc161d8: ldur            w0, [x1, #7]
    // 0xc161dc: DecompressPointer r0
    //     0xc161dc: add             x0, x0, HEAP, lsl #32
    // 0xc161e0: r16 = 12.000000
    //     0xc161e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc161e4: ldr             x16, [x16, #0x9e8]
    // 0xc161e8: r30 = Instance_Color
    //     0xc161e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc161ec: stp             lr, x16, [SP, #8]
    // 0xc161f0: r16 = Instance_FontWeight
    //     0xc161f0: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xc161f4: ldr             x16, [x16, #0x20]
    // 0xc161f8: str             x16, [SP]
    // 0xc161fc: mov             x1, x0
    // 0xc16200: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xc16200: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xc16204: ldr             x4, [x4, #0xc48]
    // 0xc16208: r0 = copyWith()
    //     0xc16208: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc1620c: mov             x1, x0
    // 0xc16210: ldur            x0, [fp, #-8]
    // 0xc16214: stur            x1, [fp, #-0x50]
    // 0xc16218: LoadField: r2 = r0->field_1f
    //     0xc16218: ldur            w2, [x0, #0x1f]
    // 0xc1621c: DecompressPointer r2
    //     0xc1621c: add             x2, x2, HEAP, lsl #32
    // 0xc16220: stur            x2, [fp, #-0x48]
    // 0xc16224: r0 = ReadMoreText()
    //     0xc16224: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xc16228: mov             x1, x0
    // 0xc1622c: ldur            x0, [fp, #-0x28]
    // 0xc16230: stur            x1, [fp, #-8]
    // 0xc16234: StoreField: r1->field_3f = r0
    //     0xc16234: stur            w0, [x1, #0x3f]
    // 0xc16238: ldur            x0, [fp, #-0x48]
    // 0xc1623c: StoreField: r1->field_b = r0
    //     0xc1623c: stur            w0, [x1, #0xb]
    // 0xc16240: r0 = " Read Less"
    //     0xc16240: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xc16244: ldr             x0, [x0, #0x9c0]
    // 0xc16248: StoreField: r1->field_43 = r0
    //     0xc16248: stur            w0, [x1, #0x43]
    // 0xc1624c: r0 = "Read More"
    //     0xc1624c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xc16250: ldr             x0, [x0, #0x9c8]
    // 0xc16254: StoreField: r1->field_47 = r0
    //     0xc16254: stur            w0, [x1, #0x47]
    // 0xc16258: r0 = 240
    //     0xc16258: movz            x0, #0xf0
    // 0xc1625c: StoreField: r1->field_f = r0
    //     0xc1625c: stur            x0, [x1, #0xf]
    // 0xc16260: r0 = 2
    //     0xc16260: movz            x0, #0x2
    // 0xc16264: ArrayStore: r1[0] = r0  ; List_8
    //     0xc16264: stur            x0, [x1, #0x17]
    // 0xc16268: r0 = Instance_TrimMode
    //     0xc16268: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xc1626c: ldr             x0, [x0, #0x9d0]
    // 0xc16270: StoreField: r1->field_1f = r0
    //     0xc16270: stur            w0, [x1, #0x1f]
    // 0xc16274: ldur            x0, [fp, #-0x30]
    // 0xc16278: StoreField: r1->field_4f = r0
    //     0xc16278: stur            w0, [x1, #0x4f]
    // 0xc1627c: r0 = Instance_TextAlign
    //     0xc1627c: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc16280: StoreField: r1->field_53 = r0
    //     0xc16280: stur            w0, [x1, #0x53]
    // 0xc16284: ldur            x0, [fp, #-0x38]
    // 0xc16288: StoreField: r1->field_23 = r0
    //     0xc16288: stur            w0, [x1, #0x23]
    // 0xc1628c: ldur            x0, [fp, #-0x50]
    // 0xc16290: StoreField: r1->field_27 = r0
    //     0xc16290: stur            w0, [x1, #0x27]
    // 0xc16294: r0 = "… "
    //     0xc16294: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xc16298: ldr             x0, [x0, #0x9d8]
    // 0xc1629c: StoreField: r1->field_3b = r0
    //     0xc1629c: stur            w0, [x1, #0x3b]
    // 0xc162a0: r0 = true
    //     0xc162a0: add             x0, NULL, #0x20  ; true
    // 0xc162a4: StoreField: r1->field_37 = r0
    //     0xc162a4: stur            w0, [x1, #0x37]
    // 0xc162a8: r0 = Container()
    //     0xc162a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc162ac: stur            x0, [fp, #-0x28]
    // 0xc162b0: r16 = Instance_EdgeInsets
    //     0xc162b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc162b4: ldr             x16, [x16, #0x668]
    // 0xc162b8: r30 = Instance_BoxDecoration
    //     0xc162b8: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xc162bc: ldr             lr, [lr, #0x5a8]
    // 0xc162c0: stp             lr, x16, [SP, #0x10]
    // 0xc162c4: r16 = Instance_Alignment
    //     0xc162c4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xc162c8: ldr             x16, [x16, #0xf98]
    // 0xc162cc: ldur            lr, [fp, #-8]
    // 0xc162d0: stp             lr, x16, [SP]
    // 0xc162d4: mov             x1, x0
    // 0xc162d8: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x3, child, 0x4, decoration, 0x2, padding, 0x1, null]
    //     0xc162d8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51ea0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x3, "child", 0x4, "decoration", 0x2, "padding", 0x1, Null]
    //     0xc162dc: ldr             x4, [x4, #0xea0]
    // 0xc162e0: r0 = Container()
    //     0xc162e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc162e4: r0 = Padding()
    //     0xc162e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc162e8: mov             x2, x0
    // 0xc162ec: r0 = Instance_EdgeInsets
    //     0xc162ec: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a18] Obj!EdgeInsets@d58b51
    //     0xc162f0: ldr             x0, [x0, #0xa18]
    // 0xc162f4: stur            x2, [fp, #-8]
    // 0xc162f8: StoreField: r2->field_f = r0
    //     0xc162f8: stur            w0, [x2, #0xf]
    // 0xc162fc: ldur            x0, [fp, #-0x28]
    // 0xc16300: StoreField: r2->field_b = r0
    //     0xc16300: stur            w0, [x2, #0xb]
    // 0xc16304: ldur            x0, [fp, #-0x18]
    // 0xc16308: LoadField: r1 = r0->field_b
    //     0xc16308: ldur            w1, [x0, #0xb]
    // 0xc1630c: LoadField: r3 = r0->field_f
    //     0xc1630c: ldur            w3, [x0, #0xf]
    // 0xc16310: DecompressPointer r3
    //     0xc16310: add             x3, x3, HEAP, lsl #32
    // 0xc16314: LoadField: r4 = r3->field_b
    //     0xc16314: ldur            w4, [x3, #0xb]
    // 0xc16318: r3 = LoadInt32Instr(r1)
    //     0xc16318: sbfx            x3, x1, #1, #0x1f
    // 0xc1631c: stur            x3, [fp, #-0x40]
    // 0xc16320: r1 = LoadInt32Instr(r4)
    //     0xc16320: sbfx            x1, x4, #1, #0x1f
    // 0xc16324: cmp             x3, x1
    // 0xc16328: b.ne            #0xc16334
    // 0xc1632c: mov             x1, x0
    // 0xc16330: r0 = _growToNextCapacity()
    //     0xc16330: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc16334: ldur            x2, [fp, #-0x18]
    // 0xc16338: ldur            x3, [fp, #-0x40]
    // 0xc1633c: add             x0, x3, #1
    // 0xc16340: lsl             x1, x0, #1
    // 0xc16344: StoreField: r2->field_b = r1
    //     0xc16344: stur            w1, [x2, #0xb]
    // 0xc16348: LoadField: r1 = r2->field_f
    //     0xc16348: ldur            w1, [x2, #0xf]
    // 0xc1634c: DecompressPointer r1
    //     0xc1634c: add             x1, x1, HEAP, lsl #32
    // 0xc16350: ldur            x0, [fp, #-8]
    // 0xc16354: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc16354: add             x25, x1, x3, lsl #2
    //     0xc16358: add             x25, x25, #0xf
    //     0xc1635c: str             w0, [x25]
    //     0xc16360: tbz             w0, #0, #0xc1637c
    //     0xc16364: ldurb           w16, [x1, #-1]
    //     0xc16368: ldurb           w17, [x0, #-1]
    //     0xc1636c: and             x16, x17, x16, lsr #2
    //     0xc16370: tst             x16, HEAP, lsr #32
    //     0xc16374: b.eq            #0xc1637c
    //     0xc16378: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc1637c: r0 = GestureDetector()
    //     0xc1637c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc16380: ldur            x2, [fp, #-0x20]
    // 0xc16384: r1 = Function '<anonymous closure>':.
    //     0xc16384: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ea8] AnonymousClosure: (0xaaa130), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xc16388: ldr             x1, [x1, #0xea8]
    // 0xc1638c: stur            x0, [fp, #-8]
    // 0xc16390: r0 = AllocateClosure()
    //     0xc16390: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc16394: ldur            x2, [fp, #-0x20]
    // 0xc16398: r1 = Function '<anonymous closure>':.
    //     0xc16398: add             x1, PP, #0x51, lsl #12  ; [pp+0x51eb0] AnonymousClosure: (0xc166cc), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xc1639c: ldr             x1, [x1, #0xeb0]
    // 0xc163a0: stur            x0, [fp, #-0x20]
    // 0xc163a4: r0 = AllocateClosure()
    //     0xc163a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc163a8: ldur            x16, [fp, #-0x20]
    // 0xc163ac: stp             x0, x16, [SP, #8]
    // 0xc163b0: r16 = Instance_Icon
    //     0xc163b0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51eb8] Obj!Icon@d66531
    //     0xc163b4: ldr             x16, [x16, #0xeb8]
    // 0xc163b8: str             x16, [SP]
    // 0xc163bc: ldur            x1, [fp, #-8]
    // 0xc163c0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xc163c0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xc163c4: ldr             x4, [x4, #0xa20]
    // 0xc163c8: r0 = GestureDetector()
    //     0xc163c8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc163cc: r0 = Align()
    //     0xc163cc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc163d0: mov             x1, x0
    // 0xc163d4: r0 = Instance_Alignment
    //     0xc163d4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xc163d8: ldr             x0, [x0, #0xa78]
    // 0xc163dc: stur            x1, [fp, #-0x20]
    // 0xc163e0: StoreField: r1->field_f = r0
    //     0xc163e0: stur            w0, [x1, #0xf]
    // 0xc163e4: ldur            x0, [fp, #-8]
    // 0xc163e8: StoreField: r1->field_b = r0
    //     0xc163e8: stur            w0, [x1, #0xb]
    // 0xc163ec: r0 = Padding()
    //     0xc163ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc163f0: mov             x2, x0
    // 0xc163f4: r0 = Instance_EdgeInsets
    //     0xc163f4: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ec0] Obj!EdgeInsets@d58af1
    //     0xc163f8: ldr             x0, [x0, #0xec0]
    // 0xc163fc: stur            x2, [fp, #-8]
    // 0xc16400: StoreField: r2->field_f = r0
    //     0xc16400: stur            w0, [x2, #0xf]
    // 0xc16404: ldur            x0, [fp, #-0x20]
    // 0xc16408: StoreField: r2->field_b = r0
    //     0xc16408: stur            w0, [x2, #0xb]
    // 0xc1640c: ldur            x0, [fp, #-0x18]
    // 0xc16410: LoadField: r1 = r0->field_b
    //     0xc16410: ldur            w1, [x0, #0xb]
    // 0xc16414: LoadField: r3 = r0->field_f
    //     0xc16414: ldur            w3, [x0, #0xf]
    // 0xc16418: DecompressPointer r3
    //     0xc16418: add             x3, x3, HEAP, lsl #32
    // 0xc1641c: LoadField: r4 = r3->field_b
    //     0xc1641c: ldur            w4, [x3, #0xb]
    // 0xc16420: r3 = LoadInt32Instr(r1)
    //     0xc16420: sbfx            x3, x1, #1, #0x1f
    // 0xc16424: stur            x3, [fp, #-0x40]
    // 0xc16428: r1 = LoadInt32Instr(r4)
    //     0xc16428: sbfx            x1, x4, #1, #0x1f
    // 0xc1642c: cmp             x3, x1
    // 0xc16430: b.ne            #0xc1643c
    // 0xc16434: mov             x1, x0
    // 0xc16438: r0 = _growToNextCapacity()
    //     0xc16438: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc1643c: ldur            x4, [fp, #-0x10]
    // 0xc16440: ldur            x2, [fp, #-0x18]
    // 0xc16444: ldur            x3, [fp, #-0x40]
    // 0xc16448: add             x0, x3, #1
    // 0xc1644c: lsl             x1, x0, #1
    // 0xc16450: StoreField: r2->field_b = r1
    //     0xc16450: stur            w1, [x2, #0xb]
    // 0xc16454: LoadField: r1 = r2->field_f
    //     0xc16454: ldur            w1, [x2, #0xf]
    // 0xc16458: DecompressPointer r1
    //     0xc16458: add             x1, x1, HEAP, lsl #32
    // 0xc1645c: ldur            x0, [fp, #-8]
    // 0xc16460: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc16460: add             x25, x1, x3, lsl #2
    //     0xc16464: add             x25, x25, #0xf
    //     0xc16468: str             w0, [x25]
    //     0xc1646c: tbz             w0, #0, #0xc16488
    //     0xc16470: ldurb           w16, [x1, #-1]
    //     0xc16474: ldurb           w17, [x0, #-1]
    //     0xc16478: and             x16, x17, x16, lsr #2
    //     0xc1647c: tst             x16, HEAP, lsr #32
    //     0xc16480: b.eq            #0xc16488
    //     0xc16484: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc16488: r0 = Column()
    //     0xc16488: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc1648c: mov             x3, x0
    // 0xc16490: r0 = Instance_Axis
    //     0xc16490: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc16494: stur            x3, [fp, #-8]
    // 0xc16498: StoreField: r3->field_f = r0
    //     0xc16498: stur            w0, [x3, #0xf]
    // 0xc1649c: r4 = Instance_MainAxisAlignment
    //     0xc1649c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc164a0: ldr             x4, [x4, #0xa08]
    // 0xc164a4: StoreField: r3->field_13 = r4
    //     0xc164a4: stur            w4, [x3, #0x13]
    // 0xc164a8: r5 = Instance_MainAxisSize
    //     0xc164a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc164ac: ldr             x5, [x5, #0xa10]
    // 0xc164b0: ArrayStore: r3[0] = r5  ; List_4
    //     0xc164b0: stur            w5, [x3, #0x17]
    // 0xc164b4: r6 = Instance_CrossAxisAlignment
    //     0xc164b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc164b8: ldr             x6, [x6, #0xa18]
    // 0xc164bc: StoreField: r3->field_1b = r6
    //     0xc164bc: stur            w6, [x3, #0x1b]
    // 0xc164c0: r7 = Instance_VerticalDirection
    //     0xc164c0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc164c4: ldr             x7, [x7, #0xa20]
    // 0xc164c8: StoreField: r3->field_23 = r7
    //     0xc164c8: stur            w7, [x3, #0x23]
    // 0xc164cc: r8 = Instance_Clip
    //     0xc164cc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc164d0: ldr             x8, [x8, #0x38]
    // 0xc164d4: StoreField: r3->field_2b = r8
    //     0xc164d4: stur            w8, [x3, #0x2b]
    // 0xc164d8: StoreField: r3->field_2f = rZR
    //     0xc164d8: stur            xzr, [x3, #0x2f]
    // 0xc164dc: ldur            x1, [fp, #-0x18]
    // 0xc164e0: StoreField: r3->field_b = r1
    //     0xc164e0: stur            w1, [x3, #0xb]
    // 0xc164e4: r1 = Null
    //     0xc164e4: mov             x1, NULL
    // 0xc164e8: r2 = 4
    //     0xc164e8: movz            x2, #0x4
    // 0xc164ec: r0 = AllocateArray()
    //     0xc164ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc164f0: mov             x2, x0
    // 0xc164f4: ldur            x0, [fp, #-0x10]
    // 0xc164f8: stur            x2, [fp, #-0x18]
    // 0xc164fc: StoreField: r2->field_f = r0
    //     0xc164fc: stur            w0, [x2, #0xf]
    // 0xc16500: ldur            x0, [fp, #-8]
    // 0xc16504: StoreField: r2->field_13 = r0
    //     0xc16504: stur            w0, [x2, #0x13]
    // 0xc16508: r1 = <Widget>
    //     0xc16508: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc1650c: r0 = AllocateGrowableArray()
    //     0xc1650c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc16510: mov             x1, x0
    // 0xc16514: ldur            x0, [fp, #-0x18]
    // 0xc16518: stur            x1, [fp, #-8]
    // 0xc1651c: StoreField: r1->field_f = r0
    //     0xc1651c: stur            w0, [x1, #0xf]
    // 0xc16520: r0 = 4
    //     0xc16520: movz            x0, #0x4
    // 0xc16524: StoreField: r1->field_b = r0
    //     0xc16524: stur            w0, [x1, #0xb]
    // 0xc16528: r0 = Column()
    //     0xc16528: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc1652c: mov             x1, x0
    // 0xc16530: r0 = Instance_Axis
    //     0xc16530: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc16534: stur            x1, [fp, #-0x10]
    // 0xc16538: StoreField: r1->field_f = r0
    //     0xc16538: stur            w0, [x1, #0xf]
    // 0xc1653c: r0 = Instance_MainAxisAlignment
    //     0xc1653c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc16540: ldr             x0, [x0, #0xa08]
    // 0xc16544: StoreField: r1->field_13 = r0
    //     0xc16544: stur            w0, [x1, #0x13]
    // 0xc16548: r0 = Instance_MainAxisSize
    //     0xc16548: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc1654c: ldr             x0, [x0, #0xa10]
    // 0xc16550: ArrayStore: r1[0] = r0  ; List_4
    //     0xc16550: stur            w0, [x1, #0x17]
    // 0xc16554: r0 = Instance_CrossAxisAlignment
    //     0xc16554: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc16558: ldr             x0, [x0, #0xa18]
    // 0xc1655c: StoreField: r1->field_1b = r0
    //     0xc1655c: stur            w0, [x1, #0x1b]
    // 0xc16560: r0 = Instance_VerticalDirection
    //     0xc16560: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc16564: ldr             x0, [x0, #0xa20]
    // 0xc16568: StoreField: r1->field_23 = r0
    //     0xc16568: stur            w0, [x1, #0x23]
    // 0xc1656c: r0 = Instance_Clip
    //     0xc1656c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc16570: ldr             x0, [x0, #0x38]
    // 0xc16574: StoreField: r1->field_2b = r0
    //     0xc16574: stur            w0, [x1, #0x2b]
    // 0xc16578: StoreField: r1->field_2f = rZR
    //     0xc16578: stur            xzr, [x1, #0x2f]
    // 0xc1657c: ldur            x0, [fp, #-8]
    // 0xc16580: StoreField: r1->field_b = r0
    //     0xc16580: stur            w0, [x1, #0xb]
    // 0xc16584: r0 = SafeArea()
    //     0xc16584: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xc16588: mov             x1, x0
    // 0xc1658c: r0 = true
    //     0xc1658c: add             x0, NULL, #0x20  ; true
    // 0xc16590: stur            x1, [fp, #-8]
    // 0xc16594: StoreField: r1->field_b = r0
    //     0xc16594: stur            w0, [x1, #0xb]
    // 0xc16598: StoreField: r1->field_f = r0
    //     0xc16598: stur            w0, [x1, #0xf]
    // 0xc1659c: StoreField: r1->field_13 = r0
    //     0xc1659c: stur            w0, [x1, #0x13]
    // 0xc165a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc165a0: stur            w0, [x1, #0x17]
    // 0xc165a4: r2 = Instance_EdgeInsets
    //     0xc165a4: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc165a8: StoreField: r1->field_1b = r2
    //     0xc165a8: stur            w2, [x1, #0x1b]
    // 0xc165ac: r2 = false
    //     0xc165ac: add             x2, NULL, #0x30  ; false
    // 0xc165b0: StoreField: r1->field_1f = r2
    //     0xc165b0: stur            w2, [x1, #0x1f]
    // 0xc165b4: ldur            x3, [fp, #-0x10]
    // 0xc165b8: StoreField: r1->field_23 = r3
    //     0xc165b8: stur            w3, [x1, #0x23]
    // 0xc165bc: r0 = Scaffold()
    //     0xc165bc: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xc165c0: ldur            x1, [fp, #-8]
    // 0xc165c4: ArrayStore: r0[0] = r1  ; List_4
    //     0xc165c4: stur            w1, [x0, #0x17]
    // 0xc165c8: r1 = Instance_Color
    //     0xc165c8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc165cc: StoreField: r0->field_33 = r1
    //     0xc165cc: stur            w1, [x0, #0x33]
    // 0xc165d0: r1 = true
    //     0xc165d0: add             x1, NULL, #0x20  ; true
    // 0xc165d4: StoreField: r0->field_43 = r1
    //     0xc165d4: stur            w1, [x0, #0x43]
    // 0xc165d8: r1 = false
    //     0xc165d8: add             x1, NULL, #0x30  ; false
    // 0xc165dc: StoreField: r0->field_b = r1
    //     0xc165dc: stur            w1, [x0, #0xb]
    // 0xc165e0: StoreField: r0->field_f = r1
    //     0xc165e0: stur            w1, [x0, #0xf]
    // 0xc165e4: LeaveFrame
    //     0xc165e4: mov             SP, fp
    //     0xc165e8: ldp             fp, lr, [SP], #0x10
    // 0xc165ec: ret
    //     0xc165ec: ret             
    // 0xc165f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc165f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc165f4: b               #0xc1442c
    // 0xc165f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc165f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc165fc: r9 = _pageController
    //     0xc165fc: add             x9, PP, #0x51, lsl #12  ; [pp+0x51ec8] Field <_RatingReviewOnTapImageState@1750089873._pageController@1750089873>: late (offset: 0x1c)
    //     0xc16600: ldr             x9, [x9, #0xec8]
    // 0xc16604: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc16604: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc16608: SaveReg d0
    //     0xc16608: str             q0, [SP, #-0x10]!
    // 0xc1660c: stp             x0, x2, [SP, #-0x10]!
    // 0xc16610: r0 = AllocateDouble()
    //     0xc16610: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc16614: mov             x1, x0
    // 0xc16618: ldp             x0, x2, [SP], #0x10
    // 0xc1661c: RestoreReg d0
    //     0xc1661c: ldr             q0, [SP], #0x10
    // 0xc16620: b               #0xc14618
    // 0xc16624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16624: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16628: SaveReg d0
    //     0xc16628: str             q0, [SP, #-0x10]!
    // 0xc1662c: stp             x0, x2, [SP, #-0x10]!
    // 0xc16630: r0 = AllocateDouble()
    //     0xc16630: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc16634: mov             x1, x0
    // 0xc16638: ldp             x0, x2, [SP], #0x10
    // 0xc1663c: RestoreReg d0
    //     0xc1663c: ldr             q0, [SP], #0x10
    // 0xc16640: b               #0xc147d4
    // 0xc16644: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc16644: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc16648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16648: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1664c: b               #0xc14924
    // 0xc16650: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc16650: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc16654: SaveReg d0
    //     0xc16654: str             q0, [SP, #-0x10]!
    // 0xc16658: stp             x4, x5, [SP, #-0x10]!
    // 0xc1665c: stp             x2, x3, [SP, #-0x10]!
    // 0xc16660: stp             x0, x1, [SP, #-0x10]!
    // 0xc16664: r0 = AllocateDouble()
    //     0xc16664: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc16668: mov             x6, x0
    // 0xc1666c: ldp             x0, x1, [SP], #0x10
    // 0xc16670: ldp             x2, x3, [SP], #0x10
    // 0xc16674: ldp             x4, x5, [SP], #0x10
    // 0xc16678: RestoreReg d0
    //     0xc16678: ldr             q0, [SP], #0x10
    // 0xc1667c: b               #0xc14a84
    // 0xc16680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16680: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16684: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16684: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16688: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16688: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1668c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1668c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16690: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc16690: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc16694: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16694: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16698: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16698: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1669c: SaveReg d0
    //     0xc1669c: str             q0, [SP, #-0x10]!
    // 0xc166a0: r0 = AllocateDouble()
    //     0xc166a0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc166a4: RestoreReg d0
    //     0xc166a4: ldr             q0, [SP], #0x10
    // 0xc166a8: b               #0xc155f4
    // 0xc166ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc166c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc166c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc166cc, size: 0xa8
    // 0xc166cc: EnterFrame
    //     0xc166cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc166d0: mov             fp, SP
    // 0xc166d4: ldr             x0, [fp, #0x10]
    // 0xc166d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc166d8: ldur            w1, [x0, #0x17]
    // 0xc166dc: DecompressPointer r1
    //     0xc166dc: add             x1, x1, HEAP, lsl #32
    // 0xc166e0: CheckStackOverflow
    //     0xc166e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc166e4: cmp             SP, x16
    //     0xc166e8: b.ls            #0xc16768
    // 0xc166ec: LoadField: r0 = r1->field_f
    //     0xc166ec: ldur            w0, [x1, #0xf]
    // 0xc166f0: DecompressPointer r0
    //     0xc166f0: add             x0, x0, HEAP, lsl #32
    // 0xc166f4: LoadField: r3 = r0->field_2b
    //     0xc166f4: ldur            w3, [x0, #0x2b]
    // 0xc166f8: DecompressPointer r3
    //     0xc166f8: add             x3, x3, HEAP, lsl #32
    // 0xc166fc: cmp             w3, NULL
    // 0xc16700: b.eq            #0xc16758
    // 0xc16704: LoadField: r2 = r1->field_13
    //     0xc16704: ldur            w2, [x1, #0x13]
    // 0xc16708: DecompressPointer r2
    //     0xc16708: add             x2, x2, HEAP, lsl #32
    // 0xc1670c: LoadField: r1 = r0->field_b
    //     0xc1670c: ldur            w1, [x0, #0xb]
    // 0xc16710: DecompressPointer r1
    //     0xc16710: add             x1, x1, HEAP, lsl #32
    // 0xc16714: cmp             w1, NULL
    // 0xc16718: b.eq            #0xc16770
    // 0xc1671c: LoadField: r4 = r1->field_b
    //     0xc1671c: ldur            w4, [x1, #0xb]
    // 0xc16720: DecompressPointer r4
    //     0xc16720: add             x4, x4, HEAP, lsl #32
    // 0xc16724: cmp             w4, NULL
    // 0xc16728: b.ne            #0xc16734
    // 0xc1672c: r1 = Null
    //     0xc1672c: mov             x1, NULL
    // 0xc16730: b               #0xc1673c
    // 0xc16734: LoadField: r1 = r4->field_b
    //     0xc16734: ldur            w1, [x4, #0xb]
    // 0xc16738: DecompressPointer r1
    //     0xc16738: add             x1, x1, HEAP, lsl #32
    // 0xc1673c: cmp             w1, NULL
    // 0xc16740: b.ne            #0xc1674c
    // 0xc16744: r5 = ""
    //     0xc16744: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc16748: b               #0xc16750
    // 0xc1674c: mov             x5, x1
    // 0xc16750: mov             x1, x0
    // 0xc16754: r0 = showMenuItem()
    //     0xc16754: bl              #0xaa992c  ; [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem
    // 0xc16758: r0 = Null
    //     0xc16758: mov             x0, NULL
    // 0xc1675c: LeaveFrame
    //     0xc1675c: mov             SP, fp
    //     0xc16760: ldp             fp, lr, [SP], #0x10
    // 0xc16764: ret
    //     0xc16764: ret             
    // 0xc16768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16768: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1676c: b               #0xc166ec
    // 0xc16770: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16770: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc16774, size: 0xc8
    // 0xc16774: EnterFrame
    //     0xc16774: stp             fp, lr, [SP, #-0x10]!
    //     0xc16778: mov             fp, SP
    // 0xc1677c: ldr             x0, [fp, #0x10]
    // 0xc16780: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc16780: ldur            w1, [x0, #0x17]
    // 0xc16784: DecompressPointer r1
    //     0xc16784: add             x1, x1, HEAP, lsl #32
    // 0xc16788: CheckStackOverflow
    //     0xc16788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1678c: cmp             SP, x16
    //     0xc16790: b.ls            #0xc16824
    // 0xc16794: LoadField: r0 = r1->field_f
    //     0xc16794: ldur            w0, [x1, #0xf]
    // 0xc16798: DecompressPointer r0
    //     0xc16798: add             x0, x0, HEAP, lsl #32
    // 0xc1679c: LoadField: r1 = r0->field_13
    //     0xc1679c: ldur            x1, [x0, #0x13]
    // 0xc167a0: LoadField: r2 = r0->field_b
    //     0xc167a0: ldur            w2, [x0, #0xb]
    // 0xc167a4: DecompressPointer r2
    //     0xc167a4: add             x2, x2, HEAP, lsl #32
    // 0xc167a8: cmp             w2, NULL
    // 0xc167ac: b.eq            #0xc1682c
    // 0xc167b0: LoadField: r3 = r2->field_b
    //     0xc167b0: ldur            w3, [x2, #0xb]
    // 0xc167b4: DecompressPointer r3
    //     0xc167b4: add             x3, x3, HEAP, lsl #32
    // 0xc167b8: cmp             w3, NULL
    // 0xc167bc: b.ne            #0xc167c8
    // 0xc167c0: r2 = Null
    //     0xc167c0: mov             x2, NULL
    // 0xc167c4: b               #0xc167d8
    // 0xc167c8: LoadField: r2 = r3->field_1b
    //     0xc167c8: ldur            w2, [x3, #0x1b]
    // 0xc167cc: DecompressPointer r2
    //     0xc167cc: add             x2, x2, HEAP, lsl #32
    // 0xc167d0: LoadField: r3 = r2->field_b
    //     0xc167d0: ldur            w3, [x2, #0xb]
    // 0xc167d4: mov             x2, x3
    // 0xc167d8: cmp             w2, NULL
    // 0xc167dc: b.ne            #0xc167e8
    // 0xc167e0: r2 = 0
    //     0xc167e0: movz            x2, #0
    // 0xc167e4: b               #0xc167f0
    // 0xc167e8: r3 = LoadInt32Instr(r2)
    //     0xc167e8: sbfx            x3, x2, #1, #0x1f
    // 0xc167ec: mov             x2, x3
    // 0xc167f0: sub             x3, x2, #1
    // 0xc167f4: cmp             x1, x3
    // 0xc167f8: b.ge            #0xc16814
    // 0xc167fc: LoadField: r1 = r0->field_1b
    //     0xc167fc: ldur            w1, [x0, #0x1b]
    // 0xc16800: DecompressPointer r1
    //     0xc16800: add             x1, x1, HEAP, lsl #32
    // 0xc16804: r16 = Sentinel
    //     0xc16804: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc16808: cmp             w1, w16
    // 0xc1680c: b.eq            #0xc16830
    // 0xc16810: r0 = nextPage()
    //     0xc16810: bl              #0xaa5ed0  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xc16814: r0 = Null
    //     0xc16814: mov             x0, NULL
    // 0xc16818: LeaveFrame
    //     0xc16818: mov             SP, fp
    //     0xc1681c: ldp             fp, lr, [SP], #0x10
    // 0xc16820: ret
    //     0xc16820: ret             
    // 0xc16824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16824: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16828: b               #0xc16794
    // 0xc1682c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1682c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16830: r9 = _pageController
    //     0xc16830: add             x9, PP, #0x51, lsl #12  ; [pp+0x51ec8] Field <_RatingReviewOnTapImageState@1750089873._pageController@1750089873>: late (offset: 0x1c)
    //     0xc16834: ldr             x9, [x9, #0xec8]
    // 0xc16838: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc16838: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc1683c, size: 0x70
    // 0xc1683c: EnterFrame
    //     0xc1683c: stp             fp, lr, [SP, #-0x10]!
    //     0xc16840: mov             fp, SP
    // 0xc16844: ldr             x0, [fp, #0x10]
    // 0xc16848: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc16848: ldur            w1, [x0, #0x17]
    // 0xc1684c: DecompressPointer r1
    //     0xc1684c: add             x1, x1, HEAP, lsl #32
    // 0xc16850: CheckStackOverflow
    //     0xc16850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16854: cmp             SP, x16
    //     0xc16858: b.ls            #0xc16898
    // 0xc1685c: LoadField: r0 = r1->field_f
    //     0xc1685c: ldur            w0, [x1, #0xf]
    // 0xc16860: DecompressPointer r0
    //     0xc16860: add             x0, x0, HEAP, lsl #32
    // 0xc16864: LoadField: r1 = r0->field_13
    //     0xc16864: ldur            x1, [x0, #0x13]
    // 0xc16868: cmp             x1, #0
    // 0xc1686c: b.le            #0xc16888
    // 0xc16870: LoadField: r1 = r0->field_1b
    //     0xc16870: ldur            w1, [x0, #0x1b]
    // 0xc16874: DecompressPointer r1
    //     0xc16874: add             x1, x1, HEAP, lsl #32
    // 0xc16878: r16 = Sentinel
    //     0xc16878: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc1687c: cmp             w1, w16
    // 0xc16880: b.eq            #0xc168a0
    // 0xc16884: r0 = previousPage()
    //     0xc16884: bl              #0xaa6010  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xc16888: r0 = Null
    //     0xc16888: mov             x0, NULL
    // 0xc1688c: LeaveFrame
    //     0xc1688c: mov             SP, fp
    //     0xc16890: ldp             fp, lr, [SP], #0x10
    // 0xc16894: ret
    //     0xc16894: ret             
    // 0xc16898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16898: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1689c: b               #0xc1685c
    // 0xc168a0: r9 = _pageController
    //     0xc168a0: add             x9, PP, #0x51, lsl #12  ; [pp+0x51ec8] Field <_RatingReviewOnTapImageState@1750089873._pageController@1750089873>: late (offset: 0x1c)
    //     0xc168a4: ldr             x9, [x9, #0xec8]
    // 0xc168a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc168a8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc168ac, size: 0x2f4
    // 0xc168ac: EnterFrame
    //     0xc168ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc168b0: mov             fp, SP
    // 0xc168b4: AllocStack(0x60)
    //     0xc168b4: sub             SP, SP, #0x60
    // 0xc168b8: SetupParameters()
    //     0xc168b8: ldr             x0, [fp, #0x20]
    //     0xc168bc: ldur            w2, [x0, #0x17]
    //     0xc168c0: add             x2, x2, HEAP, lsl #32
    //     0xc168c4: stur            x2, [fp, #-8]
    // 0xc168c8: CheckStackOverflow
    //     0xc168c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc168cc: cmp             SP, x16
    //     0xc168d0: b.ls            #0xc16b80
    // 0xc168d4: LoadField: r0 = r2->field_f
    //     0xc168d4: ldur            w0, [x2, #0xf]
    // 0xc168d8: DecompressPointer r0
    //     0xc168d8: add             x0, x0, HEAP, lsl #32
    // 0xc168dc: LoadField: r1 = r0->field_b
    //     0xc168dc: ldur            w1, [x0, #0xb]
    // 0xc168e0: DecompressPointer r1
    //     0xc168e0: add             x1, x1, HEAP, lsl #32
    // 0xc168e4: cmp             w1, NULL
    // 0xc168e8: b.eq            #0xc16b88
    // 0xc168ec: LoadField: r0 = r1->field_b
    //     0xc168ec: ldur            w0, [x1, #0xb]
    // 0xc168f0: DecompressPointer r0
    //     0xc168f0: add             x0, x0, HEAP, lsl #32
    // 0xc168f4: cmp             w0, NULL
    // 0xc168f8: b.ne            #0xc16908
    // 0xc168fc: ldr             x3, [fp, #0x10]
    // 0xc16900: r0 = Null
    //     0xc16900: mov             x0, NULL
    // 0xc16904: b               #0xc16954
    // 0xc16908: ldr             x3, [fp, #0x10]
    // 0xc1690c: LoadField: r4 = r0->field_1b
    //     0xc1690c: ldur            w4, [x0, #0x1b]
    // 0xc16910: DecompressPointer r4
    //     0xc16910: add             x4, x4, HEAP, lsl #32
    // 0xc16914: LoadField: r0 = r4->field_b
    //     0xc16914: ldur            w0, [x4, #0xb]
    // 0xc16918: r5 = LoadInt32Instr(r3)
    //     0xc16918: sbfx            x5, x3, #1, #0x1f
    //     0xc1691c: tbz             w3, #0, #0xc16924
    //     0xc16920: ldur            x5, [x3, #7]
    // 0xc16924: r1 = LoadInt32Instr(r0)
    //     0xc16924: sbfx            x1, x0, #1, #0x1f
    // 0xc16928: mov             x0, x1
    // 0xc1692c: mov             x1, x5
    // 0xc16930: cmp             x1, x0
    // 0xc16934: b.hs            #0xc16b8c
    // 0xc16938: LoadField: r0 = r4->field_f
    //     0xc16938: ldur            w0, [x4, #0xf]
    // 0xc1693c: DecompressPointer r0
    //     0xc1693c: add             x0, x0, HEAP, lsl #32
    // 0xc16940: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc16940: add             x16, x0, x5, lsl #2
    //     0xc16944: ldur            w1, [x16, #0xf]
    // 0xc16948: DecompressPointer r1
    //     0xc16948: add             x1, x1, HEAP, lsl #32
    // 0xc1694c: LoadField: r0 = r1->field_f
    //     0xc1694c: ldur            w0, [x1, #0xf]
    // 0xc16950: DecompressPointer r0
    //     0xc16950: add             x0, x0, HEAP, lsl #32
    // 0xc16954: r1 = LoadClassIdInstr(r0)
    //     0xc16954: ldur            x1, [x0, #-1]
    //     0xc16958: ubfx            x1, x1, #0xc, #0x14
    // 0xc1695c: r16 = "image"
    //     0xc1695c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xc16960: stp             x16, x0, [SP]
    // 0xc16964: mov             x0, x1
    // 0xc16968: mov             lr, x0
    // 0xc1696c: ldr             lr, [x21, lr, lsl #3]
    // 0xc16970: blr             lr
    // 0xc16974: tbnz            w0, #4, #0xc16ad0
    // 0xc16978: ldur            x0, [fp, #-8]
    // 0xc1697c: LoadField: r1 = r0->field_f
    //     0xc1697c: ldur            w1, [x0, #0xf]
    // 0xc16980: DecompressPointer r1
    //     0xc16980: add             x1, x1, HEAP, lsl #32
    // 0xc16984: LoadField: r2 = r1->field_23
    //     0xc16984: ldur            w2, [x1, #0x23]
    // 0xc16988: DecompressPointer r2
    //     0xc16988: add             x2, x2, HEAP, lsl #32
    // 0xc1698c: tbnz            w2, #4, #0xc1699c
    // 0xc16990: r1 = Instance_BoxFit
    //     0xc16990: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xc16994: ldr             x1, [x1, #0x118]
    // 0xc16998: b               #0xc169a4
    // 0xc1699c: r1 = Instance_BoxFit
    //     0xc1699c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xc169a0: ldr             x1, [x1, #0xf38]
    // 0xc169a4: stur            x1, [fp, #-0x10]
    // 0xc169a8: r0 = ImageHeaders.forImages()
    //     0xc169a8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc169ac: mov             x3, x0
    // 0xc169b0: ldur            x0, [fp, #-8]
    // 0xc169b4: stur            x3, [fp, #-0x20]
    // 0xc169b8: LoadField: r1 = r0->field_f
    //     0xc169b8: ldur            w1, [x0, #0xf]
    // 0xc169bc: DecompressPointer r1
    //     0xc169bc: add             x1, x1, HEAP, lsl #32
    // 0xc169c0: LoadField: r0 = r1->field_b
    //     0xc169c0: ldur            w0, [x1, #0xb]
    // 0xc169c4: DecompressPointer r0
    //     0xc169c4: add             x0, x0, HEAP, lsl #32
    // 0xc169c8: cmp             w0, NULL
    // 0xc169cc: b.eq            #0xc16b90
    // 0xc169d0: LoadField: r1 = r0->field_b
    //     0xc169d0: ldur            w1, [x0, #0xb]
    // 0xc169d4: DecompressPointer r1
    //     0xc169d4: add             x1, x1, HEAP, lsl #32
    // 0xc169d8: cmp             w1, NULL
    // 0xc169dc: b.ne            #0xc169e8
    // 0xc169e0: r0 = Null
    //     0xc169e0: mov             x0, NULL
    // 0xc169e4: b               #0xc16a34
    // 0xc169e8: ldr             x2, [fp, #0x10]
    // 0xc169ec: LoadField: r4 = r1->field_1b
    //     0xc169ec: ldur            w4, [x1, #0x1b]
    // 0xc169f0: DecompressPointer r4
    //     0xc169f0: add             x4, x4, HEAP, lsl #32
    // 0xc169f4: LoadField: r0 = r4->field_b
    //     0xc169f4: ldur            w0, [x4, #0xb]
    // 0xc169f8: r5 = LoadInt32Instr(r2)
    //     0xc169f8: sbfx            x5, x2, #1, #0x1f
    //     0xc169fc: tbz             w2, #0, #0xc16a04
    //     0xc16a00: ldur            x5, [x2, #7]
    // 0xc16a04: r1 = LoadInt32Instr(r0)
    //     0xc16a04: sbfx            x1, x0, #1, #0x1f
    // 0xc16a08: mov             x0, x1
    // 0xc16a0c: mov             x1, x5
    // 0xc16a10: cmp             x1, x0
    // 0xc16a14: b.hs            #0xc16b94
    // 0xc16a18: LoadField: r0 = r4->field_f
    //     0xc16a18: ldur            w0, [x4, #0xf]
    // 0xc16a1c: DecompressPointer r0
    //     0xc16a1c: add             x0, x0, HEAP, lsl #32
    // 0xc16a20: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc16a20: add             x16, x0, x5, lsl #2
    //     0xc16a24: ldur            w1, [x16, #0xf]
    // 0xc16a28: DecompressPointer r1
    //     0xc16a28: add             x1, x1, HEAP, lsl #32
    // 0xc16a2c: LoadField: r0 = r1->field_13
    //     0xc16a2c: ldur            w0, [x1, #0x13]
    // 0xc16a30: DecompressPointer r0
    //     0xc16a30: add             x0, x0, HEAP, lsl #32
    // 0xc16a34: cmp             w0, NULL
    // 0xc16a38: b.ne            #0xc16a40
    // 0xc16a3c: r0 = ""
    //     0xc16a3c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc16a40: stur            x0, [fp, #-0x18]
    // 0xc16a44: r1 = Function '<anonymous closure>':.
    //     0xc16a44: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f40] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc16a48: ldr             x1, [x1, #0xf40]
    // 0xc16a4c: r2 = Null
    //     0xc16a4c: mov             x2, NULL
    // 0xc16a50: r0 = AllocateClosure()
    //     0xc16a50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc16a54: r1 = Function '<anonymous closure>':.
    //     0xc16a54: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f48] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc16a58: ldr             x1, [x1, #0xf48]
    // 0xc16a5c: r2 = Null
    //     0xc16a5c: mov             x2, NULL
    // 0xc16a60: stur            x0, [fp, #-0x28]
    // 0xc16a64: r0 = AllocateClosure()
    //     0xc16a64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc16a68: stur            x0, [fp, #-0x30]
    // 0xc16a6c: r0 = CachedNetworkImage()
    //     0xc16a6c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc16a70: stur            x0, [fp, #-0x38]
    // 0xc16a74: ldur            x16, [fp, #-0x10]
    // 0xc16a78: r30 = inf
    //     0xc16a78: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc16a7c: ldr             lr, [lr, #0x9f8]
    // 0xc16a80: stp             lr, x16, [SP, #0x18]
    // 0xc16a84: ldur            x16, [fp, #-0x20]
    // 0xc16a88: ldur            lr, [fp, #-0x28]
    // 0xc16a8c: stp             lr, x16, [SP, #8]
    // 0xc16a90: ldur            x16, [fp, #-0x30]
    // 0xc16a94: str             x16, [SP]
    // 0xc16a98: mov             x1, x0
    // 0xc16a9c: ldur            x2, [fp, #-0x18]
    // 0xc16aa0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xc16aa0: add             x4, PP, #0x51, lsl #12  ; [pp+0x51f50] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xc16aa4: ldr             x4, [x4, #0xf50]
    // 0xc16aa8: r0 = CachedNetworkImage()
    //     0xc16aa8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc16aac: r0 = Center()
    //     0xc16aac: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc16ab0: mov             x1, x0
    // 0xc16ab4: r0 = Instance_Alignment
    //     0xc16ab4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc16ab8: ldr             x0, [x0, #0xb10]
    // 0xc16abc: StoreField: r1->field_f = r0
    //     0xc16abc: stur            w0, [x1, #0xf]
    // 0xc16ac0: ldur            x0, [fp, #-0x38]
    // 0xc16ac4: StoreField: r1->field_b = r0
    //     0xc16ac4: stur            w0, [x1, #0xb]
    // 0xc16ac8: mov             x0, x1
    // 0xc16acc: b               #0xc16b74
    // 0xc16ad0: ldr             x2, [fp, #0x10]
    // 0xc16ad4: ldur            x0, [fp, #-8]
    // 0xc16ad8: LoadField: r1 = r0->field_f
    //     0xc16ad8: ldur            w1, [x0, #0xf]
    // 0xc16adc: DecompressPointer r1
    //     0xc16adc: add             x1, x1, HEAP, lsl #32
    // 0xc16ae0: LoadField: r0 = r1->field_b
    //     0xc16ae0: ldur            w0, [x1, #0xb]
    // 0xc16ae4: DecompressPointer r0
    //     0xc16ae4: add             x0, x0, HEAP, lsl #32
    // 0xc16ae8: cmp             w0, NULL
    // 0xc16aec: b.eq            #0xc16b98
    // 0xc16af0: LoadField: r1 = r0->field_b
    //     0xc16af0: ldur            w1, [x0, #0xb]
    // 0xc16af4: DecompressPointer r1
    //     0xc16af4: add             x1, x1, HEAP, lsl #32
    // 0xc16af8: cmp             w1, NULL
    // 0xc16afc: b.ne            #0xc16b08
    // 0xc16b00: r0 = Null
    //     0xc16b00: mov             x0, NULL
    // 0xc16b04: b               #0xc16b50
    // 0xc16b08: LoadField: r3 = r1->field_1b
    //     0xc16b08: ldur            w3, [x1, #0x1b]
    // 0xc16b0c: DecompressPointer r3
    //     0xc16b0c: add             x3, x3, HEAP, lsl #32
    // 0xc16b10: LoadField: r0 = r3->field_b
    //     0xc16b10: ldur            w0, [x3, #0xb]
    // 0xc16b14: r4 = LoadInt32Instr(r2)
    //     0xc16b14: sbfx            x4, x2, #1, #0x1f
    //     0xc16b18: tbz             w2, #0, #0xc16b20
    //     0xc16b1c: ldur            x4, [x2, #7]
    // 0xc16b20: r1 = LoadInt32Instr(r0)
    //     0xc16b20: sbfx            x1, x0, #1, #0x1f
    // 0xc16b24: mov             x0, x1
    // 0xc16b28: mov             x1, x4
    // 0xc16b2c: cmp             x1, x0
    // 0xc16b30: b.hs            #0xc16b9c
    // 0xc16b34: LoadField: r0 = r3->field_f
    //     0xc16b34: ldur            w0, [x3, #0xf]
    // 0xc16b38: DecompressPointer r0
    //     0xc16b38: add             x0, x0, HEAP, lsl #32
    // 0xc16b3c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc16b3c: add             x16, x0, x4, lsl #2
    //     0xc16b40: ldur            w1, [x16, #0xf]
    // 0xc16b44: DecompressPointer r1
    //     0xc16b44: add             x1, x1, HEAP, lsl #32
    // 0xc16b48: LoadField: r0 = r1->field_13
    //     0xc16b48: ldur            w0, [x1, #0x13]
    // 0xc16b4c: DecompressPointer r0
    //     0xc16b4c: add             x0, x0, HEAP, lsl #32
    // 0xc16b50: cmp             w0, NULL
    // 0xc16b54: b.ne            #0xc16b5c
    // 0xc16b58: r0 = ""
    //     0xc16b58: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc16b5c: stur            x0, [fp, #-8]
    // 0xc16b60: r0 = VideoPlayerWidget()
    //     0xc16b60: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xc16b64: ldur            x1, [fp, #-8]
    // 0xc16b68: StoreField: r0->field_b = r1
    //     0xc16b68: stur            w1, [x0, #0xb]
    // 0xc16b6c: r1 = true
    //     0xc16b6c: add             x1, NULL, #0x20  ; true
    // 0xc16b70: StoreField: r0->field_f = r1
    //     0xc16b70: stur            w1, [x0, #0xf]
    // 0xc16b74: LeaveFrame
    //     0xc16b74: mov             SP, fp
    //     0xc16b78: ldp             fp, lr, [SP], #0x10
    // 0xc16b7c: ret
    //     0xc16b7c: ret             
    // 0xc16b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16b80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16b84: b               #0xc168d4
    // 0xc16b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16b88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16b8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc16b8c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc16b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16b90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16b94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc16b94: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc16b98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc16b98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc16b9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc16b9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88668, size: 0x8c
    // 0xc88668: EnterFrame
    //     0xc88668: stp             fp, lr, [SP, #-0x10]!
    //     0xc8866c: mov             fp, SP
    // 0xc88670: AllocStack(0x10)
    //     0xc88670: sub             SP, SP, #0x10
    // 0xc88674: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r2, fp-0x8 */)
    //     0xc88674: mov             x2, x1
    //     0xc88678: stur            x1, [fp, #-8]
    // 0xc8867c: CheckStackOverflow
    //     0xc8867c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88680: cmp             SP, x16
    //     0xc88684: b.ls            #0xc886e0
    // 0xc88688: LoadField: r1 = r2->field_1b
    //     0xc88688: ldur            w1, [x2, #0x1b]
    // 0xc8868c: DecompressPointer r1
    //     0xc8868c: add             x1, x1, HEAP, lsl #32
    // 0xc88690: r16 = Sentinel
    //     0xc88690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88694: cmp             w1, w16
    // 0xc88698: b.eq            #0xc886e8
    // 0xc8869c: r0 = dispose()
    //     0xc8869c: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc886a0: ldur            x2, [fp, #-8]
    // 0xc886a4: LoadField: r0 = r2->field_1f
    //     0xc886a4: ldur            w0, [x2, #0x1f]
    // 0xc886a8: DecompressPointer r0
    //     0xc886a8: add             x0, x0, HEAP, lsl #32
    // 0xc886ac: stur            x0, [fp, #-0x10]
    // 0xc886b0: r1 = Function '_onCollapseChanged@1750089873':.
    //     0xc886b0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f70] AnonymousClosure: (0x935b94), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0xc886b4: ldr             x1, [x1, #0xf70]
    // 0xc886b8: r0 = AllocateClosure()
    //     0xc886b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc886bc: ldur            x1, [fp, #-0x10]
    // 0xc886c0: mov             x2, x0
    // 0xc886c4: r0 = removeListener()
    //     0xc886c4: bl              #0x7b91b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc886c8: ldur            x1, [fp, #-0x10]
    // 0xc886cc: r0 = dispose()
    //     0xc886cc: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc886d0: r0 = Null
    //     0xc886d0: mov             x0, NULL
    // 0xc886d4: LeaveFrame
    //     0xc886d4: mov             SP, fp
    //     0xc886d8: ldp             fp, lr, [SP], #0x10
    // 0xc886dc: ret
    //     0xc886dc: ret             
    // 0xc886e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc886e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc886e4: b               #0xc88688
    // 0xc886e8: r9 = _pageController
    //     0xc886e8: add             x9, PP, #0x51, lsl #12  ; [pp+0x51ec8] Field <_RatingReviewOnTapImageState@1750089873._pageController@1750089873>: late (offset: 0x1c)
    //     0xc886ec: ldr             x9, [x9, #0xec8]
    // 0xc886f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc886f0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3955, size: 0x20, field offset: 0xc
//   const constructor, 
class RatingReviewOnTapImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81504, size: 0x48
    // 0xc81504: EnterFrame
    //     0xc81504: stp             fp, lr, [SP, #-0x10]!
    //     0xc81508: mov             fp, SP
    // 0xc8150c: AllocStack(0x8)
    //     0xc8150c: sub             SP, SP, #8
    // 0xc81510: CheckStackOverflow
    //     0xc81510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc81514: cmp             SP, x16
    //     0xc81518: b.ls            #0xc81544
    // 0xc8151c: r1 = <RatingReviewOnTapImage>
    //     0xc8151c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48248] TypeArguments: <RatingReviewOnTapImage>
    //     0xc81520: ldr             x1, [x1, #0x248]
    // 0xc81524: r0 = _RatingReviewOnTapImageState()
    //     0xc81524: bl              #0xc8154c  ; Allocate_RatingReviewOnTapImageStateStub -> _RatingReviewOnTapImageState (size=0x30)
    // 0xc81528: mov             x1, x0
    // 0xc8152c: stur            x0, [fp, #-8]
    // 0xc81530: r0 = _RatingReviewOnTapImageState()
    //     0xc81530: bl              #0xc7cb88  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_RatingReviewOnTapImageState
    // 0xc81534: ldur            x0, [fp, #-8]
    // 0xc81538: LeaveFrame
    //     0xc81538: mov             SP, fp
    //     0xc8153c: ldp             fp, lr, [SP], #0x10
    // 0xc81540: ret
    //     0xc81540: ret             
    // 0xc81544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81548: b               #0xc8151c
  }
}
