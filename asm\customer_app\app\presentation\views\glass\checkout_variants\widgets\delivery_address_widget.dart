// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/delivery_address_widget.dart

// class id: 1049368, size: 0x8
class :: {
}

// class id: 3363, size: 0x14, field offset: 0x14
class _DeliveryAddressWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x940758, size: 0x130
    // 0x940758: EnterFrame
    //     0x940758: stp             fp, lr, [SP, #-0x10]!
    //     0x94075c: mov             fp, SP
    // 0x940760: AllocStack(0x18)
    //     0x940760: sub             SP, SP, #0x18
    // 0x940764: SetupParameters(_DeliveryAddressWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x940764: stur            x1, [fp, #-8]
    // 0x940768: CheckStackOverflow
    //     0x940768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94076c: cmp             SP, x16
    //     0x940770: b.ls            #0x94087c
    // 0x940774: r1 = 1
    //     0x940774: movz            x1, #0x1
    // 0x940778: r0 = AllocateContext()
    //     0x940778: bl              #0x16f6108  ; AllocateContextStub
    // 0x94077c: mov             x1, x0
    // 0x940780: ldur            x0, [fp, #-8]
    // 0x940784: StoreField: r1->field_f = r0
    //     0x940784: stur            w0, [x1, #0xf]
    // 0x940788: r0 = LoadStaticField(0x878)
    //     0x940788: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94078c: ldr             x0, [x0, #0x10f0]
    // 0x940790: cmp             w0, NULL
    // 0x940794: b.eq            #0x940884
    // 0x940798: LoadField: r3 = r0->field_53
    //     0x940798: ldur            w3, [x0, #0x53]
    // 0x94079c: DecompressPointer r3
    //     0x94079c: add             x3, x3, HEAP, lsl #32
    // 0x9407a0: stur            x3, [fp, #-0x10]
    // 0x9407a4: LoadField: r0 = r3->field_7
    //     0x9407a4: ldur            w0, [x3, #7]
    // 0x9407a8: DecompressPointer r0
    //     0x9407a8: add             x0, x0, HEAP, lsl #32
    // 0x9407ac: mov             x2, x1
    // 0x9407b0: stur            x0, [fp, #-8]
    // 0x9407b4: r1 = Function '<anonymous closure>':.
    //     0x9407b4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e38] AnonymousClosure: (0x906014), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_address_widget.dart] _DeliveryAddressWidgetState::initState (0x947f8c)
    //     0x9407b8: ldr             x1, [x1, #0xe38]
    // 0x9407bc: r0 = AllocateClosure()
    //     0x9407bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9407c0: ldur            x2, [fp, #-8]
    // 0x9407c4: mov             x3, x0
    // 0x9407c8: r1 = Null
    //     0x9407c8: mov             x1, NULL
    // 0x9407cc: stur            x3, [fp, #-8]
    // 0x9407d0: cmp             w2, NULL
    // 0x9407d4: b.eq            #0x9407f4
    // 0x9407d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9407d8: ldur            w4, [x2, #0x17]
    // 0x9407dc: DecompressPointer r4
    //     0x9407dc: add             x4, x4, HEAP, lsl #32
    // 0x9407e0: r8 = X0
    //     0x9407e0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x9407e4: LoadField: r9 = r4->field_7
    //     0x9407e4: ldur            x9, [x4, #7]
    // 0x9407e8: r3 = Null
    //     0x9407e8: add             x3, PP, #0x56, lsl #12  ; [pp+0x56e40] Null
    //     0x9407ec: ldr             x3, [x3, #0xe40]
    // 0x9407f0: blr             x9
    // 0x9407f4: ldur            x0, [fp, #-0x10]
    // 0x9407f8: LoadField: r1 = r0->field_b
    //     0x9407f8: ldur            w1, [x0, #0xb]
    // 0x9407fc: LoadField: r2 = r0->field_f
    //     0x9407fc: ldur            w2, [x0, #0xf]
    // 0x940800: DecompressPointer r2
    //     0x940800: add             x2, x2, HEAP, lsl #32
    // 0x940804: LoadField: r3 = r2->field_b
    //     0x940804: ldur            w3, [x2, #0xb]
    // 0x940808: r2 = LoadInt32Instr(r1)
    //     0x940808: sbfx            x2, x1, #1, #0x1f
    // 0x94080c: stur            x2, [fp, #-0x18]
    // 0x940810: r1 = LoadInt32Instr(r3)
    //     0x940810: sbfx            x1, x3, #1, #0x1f
    // 0x940814: cmp             x2, x1
    // 0x940818: b.ne            #0x940824
    // 0x94081c: mov             x1, x0
    // 0x940820: r0 = _growToNextCapacity()
    //     0x940820: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x940824: ldur            x2, [fp, #-0x10]
    // 0x940828: ldur            x3, [fp, #-0x18]
    // 0x94082c: add             x4, x3, #1
    // 0x940830: lsl             x5, x4, #1
    // 0x940834: StoreField: r2->field_b = r5
    //     0x940834: stur            w5, [x2, #0xb]
    // 0x940838: LoadField: r1 = r2->field_f
    //     0x940838: ldur            w1, [x2, #0xf]
    // 0x94083c: DecompressPointer r1
    //     0x94083c: add             x1, x1, HEAP, lsl #32
    // 0x940840: ldur            x0, [fp, #-8]
    // 0x940844: ArrayStore: r1[r3] = r0  ; List_4
    //     0x940844: add             x25, x1, x3, lsl #2
    //     0x940848: add             x25, x25, #0xf
    //     0x94084c: str             w0, [x25]
    //     0x940850: tbz             w0, #0, #0x94086c
    //     0x940854: ldurb           w16, [x1, #-1]
    //     0x940858: ldurb           w17, [x0, #-1]
    //     0x94085c: and             x16, x17, x16, lsr #2
    //     0x940860: tst             x16, HEAP, lsr #32
    //     0x940864: b.eq            #0x94086c
    //     0x940868: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94086c: r0 = Null
    //     0x94086c: mov             x0, NULL
    // 0x940870: LeaveFrame
    //     0x940870: mov             SP, fp
    //     0x940874: ldp             fp, lr, [SP], #0x10
    // 0x940878: ret
    //     0x940878: ret             
    // 0x94087c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94087c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x940880: b               #0x940774
    // 0x940884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x940884: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb47174, size: 0xed4
    // 0xb47174: EnterFrame
    //     0xb47174: stp             fp, lr, [SP, #-0x10]!
    //     0xb47178: mov             fp, SP
    // 0xb4717c: AllocStack(0x60)
    //     0xb4717c: sub             SP, SP, #0x60
    // 0xb47180: SetupParameters(_DeliveryAddressWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb47180: mov             x0, x1
    //     0xb47184: stur            x1, [fp, #-8]
    //     0xb47188: mov             x1, x2
    //     0xb4718c: stur            x2, [fp, #-0x10]
    // 0xb47190: CheckStackOverflow
    //     0xb47190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb47194: cmp             SP, x16
    //     0xb47198: b.ls            #0xb48004
    // 0xb4719c: r1 = 2
    //     0xb4719c: movz            x1, #0x2
    // 0xb471a0: r0 = AllocateContext()
    //     0xb471a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb471a4: mov             x2, x0
    // 0xb471a8: ldur            x0, [fp, #-8]
    // 0xb471ac: stur            x2, [fp, #-0x18]
    // 0xb471b0: StoreField: r2->field_f = r0
    //     0xb471b0: stur            w0, [x2, #0xf]
    // 0xb471b4: ldur            x1, [fp, #-0x10]
    // 0xb471b8: StoreField: r2->field_13 = r1
    //     0xb471b8: stur            w1, [x2, #0x13]
    // 0xb471bc: r0 = of()
    //     0xb471bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb471c0: LoadField: r1 = r0->field_87
    //     0xb471c0: ldur            w1, [x0, #0x87]
    // 0xb471c4: DecompressPointer r1
    //     0xb471c4: add             x1, x1, HEAP, lsl #32
    // 0xb471c8: LoadField: r0 = r1->field_7
    //     0xb471c8: ldur            w0, [x1, #7]
    // 0xb471cc: DecompressPointer r0
    //     0xb471cc: add             x0, x0, HEAP, lsl #32
    // 0xb471d0: r16 = Instance_Color
    //     0xb471d0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb471d4: r30 = 14.000000
    //     0xb471d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb471d8: ldr             lr, [lr, #0x1d8]
    // 0xb471dc: stp             lr, x16, [SP]
    // 0xb471e0: mov             x1, x0
    // 0xb471e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb471e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb471e8: ldr             x4, [x4, #0x9b8]
    // 0xb471ec: r0 = copyWith()
    //     0xb471ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb471f0: stur            x0, [fp, #-0x10]
    // 0xb471f4: r0 = Text()
    //     0xb471f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb471f8: mov             x1, x0
    // 0xb471fc: r0 = "Delivery Address"
    //     0xb471fc: add             x0, PP, #0x54, lsl #12  ; [pp+0x54780] "Delivery Address"
    //     0xb47200: ldr             x0, [x0, #0x780]
    // 0xb47204: stur            x1, [fp, #-0x20]
    // 0xb47208: StoreField: r1->field_b = r0
    //     0xb47208: stur            w0, [x1, #0xb]
    // 0xb4720c: ldur            x0, [fp, #-0x10]
    // 0xb47210: StoreField: r1->field_13 = r0
    //     0xb47210: stur            w0, [x1, #0x13]
    // 0xb47214: r0 = Padding()
    //     0xb47214: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb47218: mov             x1, x0
    // 0xb4721c: r0 = Instance_EdgeInsets
    //     0xb4721c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb47220: ldr             x0, [x0, #0x770]
    // 0xb47224: stur            x1, [fp, #-0x10]
    // 0xb47228: StoreField: r1->field_f = r0
    //     0xb47228: stur            w0, [x1, #0xf]
    // 0xb4722c: ldur            x0, [fp, #-0x20]
    // 0xb47230: StoreField: r1->field_b = r0
    //     0xb47230: stur            w0, [x1, #0xb]
    // 0xb47234: r0 = Radius()
    //     0xb47234: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb47238: d0 = 12.000000
    //     0xb47238: fmov            d0, #12.00000000
    // 0xb4723c: stur            x0, [fp, #-0x20]
    // 0xb47240: StoreField: r0->field_7 = d0
    //     0xb47240: stur            d0, [x0, #7]
    // 0xb47244: StoreField: r0->field_f = d0
    //     0xb47244: stur            d0, [x0, #0xf]
    // 0xb47248: r0 = BorderRadius()
    //     0xb47248: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4724c: mov             x2, x0
    // 0xb47250: ldur            x0, [fp, #-0x20]
    // 0xb47254: stur            x2, [fp, #-0x28]
    // 0xb47258: StoreField: r2->field_7 = r0
    //     0xb47258: stur            w0, [x2, #7]
    // 0xb4725c: StoreField: r2->field_b = r0
    //     0xb4725c: stur            w0, [x2, #0xb]
    // 0xb47260: StoreField: r2->field_f = r0
    //     0xb47260: stur            w0, [x2, #0xf]
    // 0xb47264: StoreField: r2->field_13 = r0
    //     0xb47264: stur            w0, [x2, #0x13]
    // 0xb47268: ldur            x0, [fp, #-0x18]
    // 0xb4726c: LoadField: r1 = r0->field_13
    //     0xb4726c: ldur            w1, [x0, #0x13]
    // 0xb47270: DecompressPointer r1
    //     0xb47270: add             x1, x1, HEAP, lsl #32
    // 0xb47274: r0 = of()
    //     0xb47274: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb47278: LoadField: r1 = r0->field_5b
    //     0xb47278: ldur            w1, [x0, #0x5b]
    // 0xb4727c: DecompressPointer r1
    //     0xb4727c: add             x1, x1, HEAP, lsl #32
    // 0xb47280: r0 = LoadClassIdInstr(r1)
    //     0xb47280: ldur            x0, [x1, #-1]
    //     0xb47284: ubfx            x0, x0, #0xc, #0x14
    // 0xb47288: d0 = 0.100000
    //     0xb47288: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4728c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4728c: sub             lr, x0, #0xffa
    //     0xb47290: ldr             lr, [x21, lr, lsl #3]
    //     0xb47294: blr             lr
    // 0xb47298: mov             x2, x0
    // 0xb4729c: r1 = Null
    //     0xb4729c: mov             x1, NULL
    // 0xb472a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb472a0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb472a4: r0 = Border.all()
    //     0xb472a4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb472a8: stur            x0, [fp, #-0x20]
    // 0xb472ac: r0 = BoxDecoration()
    //     0xb472ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb472b0: mov             x3, x0
    // 0xb472b4: ldur            x0, [fp, #-0x20]
    // 0xb472b8: stur            x3, [fp, #-0x30]
    // 0xb472bc: StoreField: r3->field_f = r0
    //     0xb472bc: stur            w0, [x3, #0xf]
    // 0xb472c0: ldur            x0, [fp, #-0x28]
    // 0xb472c4: StoreField: r3->field_13 = r0
    //     0xb472c4: stur            w0, [x3, #0x13]
    // 0xb472c8: r0 = Instance_BoxShape
    //     0xb472c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb472cc: ldr             x0, [x0, #0x80]
    // 0xb472d0: StoreField: r3->field_23 = r0
    //     0xb472d0: stur            w0, [x3, #0x23]
    // 0xb472d4: ldur            x4, [fp, #-8]
    // 0xb472d8: LoadField: r1 = r4->field_b
    //     0xb472d8: ldur            w1, [x4, #0xb]
    // 0xb472dc: DecompressPointer r1
    //     0xb472dc: add             x1, x1, HEAP, lsl #32
    // 0xb472e0: cmp             w1, NULL
    // 0xb472e4: b.eq            #0xb4800c
    // 0xb472e8: LoadField: r5 = r1->field_13
    //     0xb472e8: ldur            w5, [x1, #0x13]
    // 0xb472ec: DecompressPointer r5
    //     0xb472ec: add             x5, x5, HEAP, lsl #32
    // 0xb472f0: stur            x5, [fp, #-0x28]
    // 0xb472f4: LoadField: r6 = r5->field_b
    //     0xb472f4: ldur            w6, [x5, #0xb]
    // 0xb472f8: DecompressPointer r6
    //     0xb472f8: add             x6, x6, HEAP, lsl #32
    // 0xb472fc: stur            x6, [fp, #-0x20]
    // 0xb47300: r1 = Null
    //     0xb47300: mov             x1, NULL
    // 0xb47304: r2 = 6
    //     0xb47304: movz            x2, #0x6
    // 0xb47308: r0 = AllocateArray()
    //     0xb47308: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4730c: mov             x1, x0
    // 0xb47310: ldur            x0, [fp, #-0x20]
    // 0xb47314: StoreField: r1->field_f = r0
    //     0xb47314: stur            w0, [x1, #0xf]
    // 0xb47318: r16 = " "
    //     0xb47318: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb4731c: StoreField: r1->field_13 = r16
    //     0xb4731c: stur            w16, [x1, #0x13]
    // 0xb47320: ldur            x0, [fp, #-0x28]
    // 0xb47324: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb47324: ldur            w2, [x0, #0x17]
    // 0xb47328: DecompressPointer r2
    //     0xb47328: add             x2, x2, HEAP, lsl #32
    // 0xb4732c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb4732c: stur            w2, [x1, #0x17]
    // 0xb47330: str             x1, [SP]
    // 0xb47334: r0 = _interpolate()
    //     0xb47334: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb47338: ldur            x2, [fp, #-0x18]
    // 0xb4733c: stur            x0, [fp, #-0x20]
    // 0xb47340: LoadField: r1 = r2->field_13
    //     0xb47340: ldur            w1, [x2, #0x13]
    // 0xb47344: DecompressPointer r1
    //     0xb47344: add             x1, x1, HEAP, lsl #32
    // 0xb47348: r0 = of()
    //     0xb47348: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4734c: LoadField: r1 = r0->field_87
    //     0xb4734c: ldur            w1, [x0, #0x87]
    // 0xb47350: DecompressPointer r1
    //     0xb47350: add             x1, x1, HEAP, lsl #32
    // 0xb47354: LoadField: r0 = r1->field_2b
    //     0xb47354: ldur            w0, [x1, #0x2b]
    // 0xb47358: DecompressPointer r0
    //     0xb47358: add             x0, x0, HEAP, lsl #32
    // 0xb4735c: r16 = Instance_Color
    //     0xb4735c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb47360: r30 = 12.000000
    //     0xb47360: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb47364: ldr             lr, [lr, #0x9e8]
    // 0xb47368: stp             lr, x16, [SP]
    // 0xb4736c: mov             x1, x0
    // 0xb47370: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb47370: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb47374: ldr             x4, [x4, #0x9b8]
    // 0xb47378: r0 = copyWith()
    //     0xb47378: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4737c: stur            x0, [fp, #-0x28]
    // 0xb47380: r0 = Text()
    //     0xb47380: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb47384: mov             x2, x0
    // 0xb47388: ldur            x0, [fp, #-0x20]
    // 0xb4738c: stur            x2, [fp, #-0x38]
    // 0xb47390: StoreField: r2->field_b = r0
    //     0xb47390: stur            w0, [x2, #0xb]
    // 0xb47394: ldur            x0, [fp, #-0x28]
    // 0xb47398: StoreField: r2->field_13 = r0
    //     0xb47398: stur            w0, [x2, #0x13]
    // 0xb4739c: r1 = <FlexParentData>
    //     0xb4739c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb473a0: ldr             x1, [x1, #0xe00]
    // 0xb473a4: r0 = Expanded()
    //     0xb473a4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb473a8: mov             x2, x0
    // 0xb473ac: r0 = 1
    //     0xb473ac: movz            x0, #0x1
    // 0xb473b0: stur            x2, [fp, #-0x28]
    // 0xb473b4: StoreField: r2->field_13 = r0
    //     0xb473b4: stur            x0, [x2, #0x13]
    // 0xb473b8: r0 = Instance_FlexFit
    //     0xb473b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb473bc: ldr             x0, [x0, #0xe08]
    // 0xb473c0: StoreField: r2->field_1b = r0
    //     0xb473c0: stur            w0, [x2, #0x1b]
    // 0xb473c4: ldur            x0, [fp, #-0x38]
    // 0xb473c8: StoreField: r2->field_b = r0
    //     0xb473c8: stur            w0, [x2, #0xb]
    // 0xb473cc: ldur            x0, [fp, #-8]
    // 0xb473d0: LoadField: r1 = r0->field_b
    //     0xb473d0: ldur            w1, [x0, #0xb]
    // 0xb473d4: DecompressPointer r1
    //     0xb473d4: add             x1, x1, HEAP, lsl #32
    // 0xb473d8: cmp             w1, NULL
    // 0xb473dc: b.eq            #0xb48010
    // 0xb473e0: LoadField: r3 = r1->field_1b
    //     0xb473e0: ldur            w3, [x1, #0x1b]
    // 0xb473e4: DecompressPointer r3
    //     0xb473e4: add             x3, x3, HEAP, lsl #32
    // 0xb473e8: cmp             w3, NULL
    // 0xb473ec: b.ne            #0xb473f8
    // 0xb473f0: r4 = false
    //     0xb473f0: add             x4, NULL, #0x30  ; false
    // 0xb473f4: b               #0xb473fc
    // 0xb473f8: mov             x4, x3
    // 0xb473fc: ldur            x3, [fp, #-0x18]
    // 0xb47400: stur            x4, [fp, #-0x20]
    // 0xb47404: LoadField: r1 = r3->field_13
    //     0xb47404: ldur            w1, [x3, #0x13]
    // 0xb47408: DecompressPointer r1
    //     0xb47408: add             x1, x1, HEAP, lsl #32
    // 0xb4740c: r0 = of()
    //     0xb4740c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb47410: LoadField: r1 = r0->field_87
    //     0xb47410: ldur            w1, [x0, #0x87]
    // 0xb47414: DecompressPointer r1
    //     0xb47414: add             x1, x1, HEAP, lsl #32
    // 0xb47418: LoadField: r0 = r1->field_7
    //     0xb47418: ldur            w0, [x1, #7]
    // 0xb4741c: DecompressPointer r0
    //     0xb4741c: add             x0, x0, HEAP, lsl #32
    // 0xb47420: r16 = Instance_Color
    //     0xb47420: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb47424: ldr             x16, [x16, #0x858]
    // 0xb47428: r30 = 12.000000
    //     0xb47428: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4742c: ldr             lr, [lr, #0x9e8]
    // 0xb47430: stp             lr, x16, [SP]
    // 0xb47434: mov             x1, x0
    // 0xb47438: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb47438: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4743c: ldr             x4, [x4, #0x9b8]
    // 0xb47440: r0 = copyWith()
    //     0xb47440: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb47444: stur            x0, [fp, #-0x38]
    // 0xb47448: r0 = Text()
    //     0xb47448: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4744c: mov             x1, x0
    // 0xb47450: r0 = "Change"
    //     0xb47450: add             x0, PP, #0x54, lsl #12  ; [pp+0x547a8] "Change"
    //     0xb47454: ldr             x0, [x0, #0x7a8]
    // 0xb47458: stur            x1, [fp, #-0x40]
    // 0xb4745c: StoreField: r1->field_b = r0
    //     0xb4745c: stur            w0, [x1, #0xb]
    // 0xb47460: ldur            x0, [fp, #-0x38]
    // 0xb47464: StoreField: r1->field_13 = r0
    //     0xb47464: stur            w0, [x1, #0x13]
    // 0xb47468: r0 = InkWell()
    //     0xb47468: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4746c: mov             x3, x0
    // 0xb47470: ldur            x0, [fp, #-0x40]
    // 0xb47474: stur            x3, [fp, #-0x38]
    // 0xb47478: StoreField: r3->field_b = r0
    //     0xb47478: stur            w0, [x3, #0xb]
    // 0xb4747c: ldur            x2, [fp, #-0x18]
    // 0xb47480: r1 = Function '<anonymous closure>':.
    //     0xb47480: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e10] AnonymousClosure: (0xb48048), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/delivery_address_widget.dart] _DeliveryAddressWidgetState::build (0xb47174)
    //     0xb47484: ldr             x1, [x1, #0xe10]
    // 0xb47488: r0 = AllocateClosure()
    //     0xb47488: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4748c: mov             x1, x0
    // 0xb47490: ldur            x0, [fp, #-0x38]
    // 0xb47494: StoreField: r0->field_f = r1
    //     0xb47494: stur            w1, [x0, #0xf]
    // 0xb47498: r1 = true
    //     0xb47498: add             x1, NULL, #0x20  ; true
    // 0xb4749c: StoreField: r0->field_43 = r1
    //     0xb4749c: stur            w1, [x0, #0x43]
    // 0xb474a0: r2 = Instance_BoxShape
    //     0xb474a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb474a4: ldr             x2, [x2, #0x80]
    // 0xb474a8: StoreField: r0->field_47 = r2
    //     0xb474a8: stur            w2, [x0, #0x47]
    // 0xb474ac: StoreField: r0->field_6f = r1
    //     0xb474ac: stur            w1, [x0, #0x6f]
    // 0xb474b0: r3 = false
    //     0xb474b0: add             x3, NULL, #0x30  ; false
    // 0xb474b4: StoreField: r0->field_73 = r3
    //     0xb474b4: stur            w3, [x0, #0x73]
    // 0xb474b8: StoreField: r0->field_83 = r1
    //     0xb474b8: stur            w1, [x0, #0x83]
    // 0xb474bc: StoreField: r0->field_7b = r3
    //     0xb474bc: stur            w3, [x0, #0x7b]
    // 0xb474c0: r0 = Visibility()
    //     0xb474c0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb474c4: mov             x3, x0
    // 0xb474c8: ldur            x0, [fp, #-0x38]
    // 0xb474cc: stur            x3, [fp, #-0x40]
    // 0xb474d0: StoreField: r3->field_b = r0
    //     0xb474d0: stur            w0, [x3, #0xb]
    // 0xb474d4: r0 = Instance_SizedBox
    //     0xb474d4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb474d8: StoreField: r3->field_f = r0
    //     0xb474d8: stur            w0, [x3, #0xf]
    // 0xb474dc: ldur            x1, [fp, #-0x20]
    // 0xb474e0: StoreField: r3->field_13 = r1
    //     0xb474e0: stur            w1, [x3, #0x13]
    // 0xb474e4: r4 = false
    //     0xb474e4: add             x4, NULL, #0x30  ; false
    // 0xb474e8: ArrayStore: r3[0] = r4  ; List_4
    //     0xb474e8: stur            w4, [x3, #0x17]
    // 0xb474ec: StoreField: r3->field_1b = r4
    //     0xb474ec: stur            w4, [x3, #0x1b]
    // 0xb474f0: StoreField: r3->field_1f = r4
    //     0xb474f0: stur            w4, [x3, #0x1f]
    // 0xb474f4: StoreField: r3->field_23 = r4
    //     0xb474f4: stur            w4, [x3, #0x23]
    // 0xb474f8: StoreField: r3->field_27 = r4
    //     0xb474f8: stur            w4, [x3, #0x27]
    // 0xb474fc: StoreField: r3->field_2b = r4
    //     0xb474fc: stur            w4, [x3, #0x2b]
    // 0xb47500: r1 = Null
    //     0xb47500: mov             x1, NULL
    // 0xb47504: r2 = 4
    //     0xb47504: movz            x2, #0x4
    // 0xb47508: r0 = AllocateArray()
    //     0xb47508: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4750c: mov             x2, x0
    // 0xb47510: ldur            x0, [fp, #-0x28]
    // 0xb47514: stur            x2, [fp, #-0x20]
    // 0xb47518: StoreField: r2->field_f = r0
    //     0xb47518: stur            w0, [x2, #0xf]
    // 0xb4751c: ldur            x0, [fp, #-0x40]
    // 0xb47520: StoreField: r2->field_13 = r0
    //     0xb47520: stur            w0, [x2, #0x13]
    // 0xb47524: r1 = <Widget>
    //     0xb47524: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb47528: r0 = AllocateGrowableArray()
    //     0xb47528: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4752c: mov             x1, x0
    // 0xb47530: ldur            x0, [fp, #-0x20]
    // 0xb47534: stur            x1, [fp, #-0x28]
    // 0xb47538: StoreField: r1->field_f = r0
    //     0xb47538: stur            w0, [x1, #0xf]
    // 0xb4753c: r2 = 4
    //     0xb4753c: movz            x2, #0x4
    // 0xb47540: StoreField: r1->field_b = r2
    //     0xb47540: stur            w2, [x1, #0xb]
    // 0xb47544: r0 = Row()
    //     0xb47544: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb47548: mov             x3, x0
    // 0xb4754c: r0 = Instance_Axis
    //     0xb4754c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb47550: stur            x3, [fp, #-0x38]
    // 0xb47554: StoreField: r3->field_f = r0
    //     0xb47554: stur            w0, [x3, #0xf]
    // 0xb47558: r0 = Instance_MainAxisAlignment
    //     0xb47558: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4755c: ldr             x0, [x0, #0xa8]
    // 0xb47560: StoreField: r3->field_13 = r0
    //     0xb47560: stur            w0, [x3, #0x13]
    // 0xb47564: r4 = Instance_MainAxisSize
    //     0xb47564: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb47568: ldr             x4, [x4, #0xa10]
    // 0xb4756c: ArrayStore: r3[0] = r4  ; List_4
    //     0xb4756c: stur            w4, [x3, #0x17]
    // 0xb47570: r5 = Instance_CrossAxisAlignment
    //     0xb47570: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb47574: ldr             x5, [x5, #0x890]
    // 0xb47578: StoreField: r3->field_1b = r5
    //     0xb47578: stur            w5, [x3, #0x1b]
    // 0xb4757c: r6 = Instance_VerticalDirection
    //     0xb4757c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb47580: ldr             x6, [x6, #0xa20]
    // 0xb47584: StoreField: r3->field_23 = r6
    //     0xb47584: stur            w6, [x3, #0x23]
    // 0xb47588: r7 = Instance_Clip
    //     0xb47588: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4758c: ldr             x7, [x7, #0x38]
    // 0xb47590: StoreField: r3->field_2b = r7
    //     0xb47590: stur            w7, [x3, #0x2b]
    // 0xb47594: StoreField: r3->field_2f = rZR
    //     0xb47594: stur            xzr, [x3, #0x2f]
    // 0xb47598: ldur            x0, [fp, #-0x28]
    // 0xb4759c: StoreField: r3->field_b = r0
    //     0xb4759c: stur            w0, [x3, #0xb]
    // 0xb475a0: ldur            x8, [fp, #-8]
    // 0xb475a4: LoadField: r0 = r8->field_b
    //     0xb475a4: ldur            w0, [x8, #0xb]
    // 0xb475a8: DecompressPointer r0
    //     0xb475a8: add             x0, x0, HEAP, lsl #32
    // 0xb475ac: cmp             w0, NULL
    // 0xb475b0: b.eq            #0xb48014
    // 0xb475b4: LoadField: r1 = r0->field_13
    //     0xb475b4: ldur            w1, [x0, #0x13]
    // 0xb475b8: DecompressPointer r1
    //     0xb475b8: add             x1, x1, HEAP, lsl #32
    // 0xb475bc: LoadField: r9 = r1->field_1b
    //     0xb475bc: ldur            w9, [x1, #0x1b]
    // 0xb475c0: DecompressPointer r9
    //     0xb475c0: add             x9, x9, HEAP, lsl #32
    // 0xb475c4: stur            x9, [fp, #-0x28]
    // 0xb475c8: cmp             w9, NULL
    // 0xb475cc: b.ne            #0xb475d8
    // 0xb475d0: r0 = Null
    //     0xb475d0: mov             x0, NULL
    // 0xb475d4: b               #0xb47618
    // 0xb475d8: LoadField: r0 = r9->field_b
    //     0xb475d8: ldur            w0, [x9, #0xb]
    // 0xb475dc: r1 = LoadInt32Instr(r0)
    //     0xb475dc: sbfx            x1, x0, #1, #0x1f
    // 0xb475e0: mov             x0, x1
    // 0xb475e4: r1 = 0
    //     0xb475e4: movz            x1, #0
    // 0xb475e8: cmp             x1, x0
    // 0xb475ec: b.hs            #0xb48018
    // 0xb475f0: LoadField: r0 = r9->field_f
    //     0xb475f0: ldur            w0, [x9, #0xf]
    // 0xb475f4: DecompressPointer r0
    //     0xb475f4: add             x0, x0, HEAP, lsl #32
    // 0xb475f8: LoadField: r1 = r0->field_f
    //     0xb475f8: ldur            w1, [x0, #0xf]
    // 0xb475fc: DecompressPointer r1
    //     0xb475fc: add             x1, x1, HEAP, lsl #32
    // 0xb47600: cmp             w1, NULL
    // 0xb47604: b.ne            #0xb47610
    // 0xb47608: r0 = Null
    //     0xb47608: mov             x0, NULL
    // 0xb4760c: b               #0xb47618
    // 0xb47610: LoadField: r0 = r1->field_2b
    //     0xb47610: ldur            w0, [x1, #0x2b]
    // 0xb47614: DecompressPointer r0
    //     0xb47614: add             x0, x0, HEAP, lsl #32
    // 0xb47618: stur            x0, [fp, #-0x20]
    // 0xb4761c: r1 = Null
    //     0xb4761c: mov             x1, NULL
    // 0xb47620: r2 = 26
    //     0xb47620: movz            x2, #0x1a
    // 0xb47624: r0 = AllocateArray()
    //     0xb47624: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb47628: mov             x3, x0
    // 0xb4762c: ldur            x0, [fp, #-0x20]
    // 0xb47630: stur            x3, [fp, #-0x40]
    // 0xb47634: StoreField: r3->field_f = r0
    //     0xb47634: stur            w0, [x3, #0xf]
    // 0xb47638: r16 = " "
    //     0xb47638: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb4763c: StoreField: r3->field_13 = r16
    //     0xb4763c: stur            w16, [x3, #0x13]
    // 0xb47640: ldur            x4, [fp, #-0x28]
    // 0xb47644: cmp             w4, NULL
    // 0xb47648: b.ne            #0xb47654
    // 0xb4764c: r0 = Null
    //     0xb4764c: mov             x0, NULL
    // 0xb47650: b               #0xb47694
    // 0xb47654: LoadField: r0 = r4->field_b
    //     0xb47654: ldur            w0, [x4, #0xb]
    // 0xb47658: r1 = LoadInt32Instr(r0)
    //     0xb47658: sbfx            x1, x0, #1, #0x1f
    // 0xb4765c: mov             x0, x1
    // 0xb47660: r1 = 0
    //     0xb47660: movz            x1, #0
    // 0xb47664: cmp             x1, x0
    // 0xb47668: b.hs            #0xb4801c
    // 0xb4766c: LoadField: r0 = r4->field_f
    //     0xb4766c: ldur            w0, [x4, #0xf]
    // 0xb47670: DecompressPointer r0
    //     0xb47670: add             x0, x0, HEAP, lsl #32
    // 0xb47674: LoadField: r1 = r0->field_f
    //     0xb47674: ldur            w1, [x0, #0xf]
    // 0xb47678: DecompressPointer r1
    //     0xb47678: add             x1, x1, HEAP, lsl #32
    // 0xb4767c: cmp             w1, NULL
    // 0xb47680: b.ne            #0xb4768c
    // 0xb47684: r0 = Null
    //     0xb47684: mov             x0, NULL
    // 0xb47688: b               #0xb47694
    // 0xb4768c: LoadField: r0 = r1->field_13
    //     0xb4768c: ldur            w0, [x1, #0x13]
    // 0xb47690: DecompressPointer r0
    //     0xb47690: add             x0, x0, HEAP, lsl #32
    // 0xb47694: ArrayStore: r3[0] = r0  ; List_4
    //     0xb47694: stur            w0, [x3, #0x17]
    // 0xb47698: r16 = " "
    //     0xb47698: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb4769c: StoreField: r3->field_1b = r16
    //     0xb4769c: stur            w16, [x3, #0x1b]
    // 0xb476a0: cmp             w4, NULL
    // 0xb476a4: b.ne            #0xb476b0
    // 0xb476a8: r0 = Null
    //     0xb476a8: mov             x0, NULL
    // 0xb476ac: b               #0xb47714
    // 0xb476b0: LoadField: r0 = r4->field_b
    //     0xb476b0: ldur            w0, [x4, #0xb]
    // 0xb476b4: r1 = LoadInt32Instr(r0)
    //     0xb476b4: sbfx            x1, x0, #1, #0x1f
    // 0xb476b8: mov             x0, x1
    // 0xb476bc: r1 = 0
    //     0xb476bc: movz            x1, #0
    // 0xb476c0: cmp             x1, x0
    // 0xb476c4: b.hs            #0xb48020
    // 0xb476c8: LoadField: r0 = r4->field_f
    //     0xb476c8: ldur            w0, [x4, #0xf]
    // 0xb476cc: DecompressPointer r0
    //     0xb476cc: add             x0, x0, HEAP, lsl #32
    // 0xb476d0: LoadField: r1 = r0->field_f
    //     0xb476d0: ldur            w1, [x0, #0xf]
    // 0xb476d4: DecompressPointer r1
    //     0xb476d4: add             x1, x1, HEAP, lsl #32
    // 0xb476d8: cmp             w1, NULL
    // 0xb476dc: b.ne            #0xb476e8
    // 0xb476e0: r0 = Null
    //     0xb476e0: mov             x0, NULL
    // 0xb476e4: b               #0xb47714
    // 0xb476e8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb476e8: ldur            w0, [x1, #0x17]
    // 0xb476ec: DecompressPointer r0
    //     0xb476ec: add             x0, x0, HEAP, lsl #32
    // 0xb476f0: cmp             w0, NULL
    // 0xb476f4: b.ne            #0xb47700
    // 0xb476f8: r0 = Null
    //     0xb476f8: mov             x0, NULL
    // 0xb476fc: b               #0xb47714
    // 0xb47700: LoadField: r1 = r0->field_7
    //     0xb47700: ldur            w1, [x0, #7]
    // 0xb47704: cbnz            w1, #0xb47710
    // 0xb47708: r0 = false
    //     0xb47708: add             x0, NULL, #0x30  ; false
    // 0xb4770c: b               #0xb47714
    // 0xb47710: r0 = true
    //     0xb47710: add             x0, NULL, #0x20  ; true
    // 0xb47714: cmp             w0, NULL
    // 0xb47718: b.eq            #0xb4779c
    // 0xb4771c: tbnz            w0, #4, #0xb4779c
    // 0xb47720: r1 = Null
    //     0xb47720: mov             x1, NULL
    // 0xb47724: r2 = 4
    //     0xb47724: movz            x2, #0x4
    // 0xb47728: r0 = AllocateArray()
    //     0xb47728: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4772c: mov             x2, x0
    // 0xb47730: r16 = ", "
    //     0xb47730: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xb47734: StoreField: r2->field_f = r16
    //     0xb47734: stur            w16, [x2, #0xf]
    // 0xb47738: ldur            x3, [fp, #-0x28]
    // 0xb4773c: cmp             w3, NULL
    // 0xb47740: b.ne            #0xb4774c
    // 0xb47744: r0 = Null
    //     0xb47744: mov             x0, NULL
    // 0xb47748: b               #0xb4778c
    // 0xb4774c: LoadField: r0 = r3->field_b
    //     0xb4774c: ldur            w0, [x3, #0xb]
    // 0xb47750: r1 = LoadInt32Instr(r0)
    //     0xb47750: sbfx            x1, x0, #1, #0x1f
    // 0xb47754: mov             x0, x1
    // 0xb47758: r1 = 0
    //     0xb47758: movz            x1, #0
    // 0xb4775c: cmp             x1, x0
    // 0xb47760: b.hs            #0xb48024
    // 0xb47764: LoadField: r0 = r3->field_f
    //     0xb47764: ldur            w0, [x3, #0xf]
    // 0xb47768: DecompressPointer r0
    //     0xb47768: add             x0, x0, HEAP, lsl #32
    // 0xb4776c: LoadField: r1 = r0->field_f
    //     0xb4776c: ldur            w1, [x0, #0xf]
    // 0xb47770: DecompressPointer r1
    //     0xb47770: add             x1, x1, HEAP, lsl #32
    // 0xb47774: cmp             w1, NULL
    // 0xb47778: b.ne            #0xb47784
    // 0xb4777c: r0 = Null
    //     0xb4777c: mov             x0, NULL
    // 0xb47780: b               #0xb4778c
    // 0xb47784: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb47784: ldur            w0, [x1, #0x17]
    // 0xb47788: DecompressPointer r0
    //     0xb47788: add             x0, x0, HEAP, lsl #32
    // 0xb4778c: StoreField: r2->field_13 = r0
    //     0xb4778c: stur            w0, [x2, #0x13]
    // 0xb47790: str             x2, [SP]
    // 0xb47794: r0 = _interpolate()
    //     0xb47794: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb47798: b               #0xb477a0
    // 0xb4779c: r0 = ""
    //     0xb4779c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb477a0: ldur            x4, [fp, #-8]
    // 0xb477a4: ldur            x3, [fp, #-0x40]
    // 0xb477a8: mov             x1, x3
    // 0xb477ac: ArrayStore: r1[4] = r0  ; List_4
    //     0xb477ac: add             x25, x1, #0x1f
    //     0xb477b0: str             w0, [x25]
    //     0xb477b4: tbz             w0, #0, #0xb477d0
    //     0xb477b8: ldurb           w16, [x1, #-1]
    //     0xb477bc: ldurb           w17, [x0, #-1]
    //     0xb477c0: and             x16, x17, x16, lsr #2
    //     0xb477c4: tst             x16, HEAP, lsr #32
    //     0xb477c8: b.eq            #0xb477d0
    //     0xb477cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb477d0: r16 = " "
    //     0xb477d0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb477d4: StoreField: r3->field_23 = r16
    //     0xb477d4: stur            w16, [x3, #0x23]
    // 0xb477d8: LoadField: r5 = r4->field_b
    //     0xb477d8: ldur            w5, [x4, #0xb]
    // 0xb477dc: DecompressPointer r5
    //     0xb477dc: add             x5, x5, HEAP, lsl #32
    // 0xb477e0: stur            x5, [fp, #-0x28]
    // 0xb477e4: cmp             w5, NULL
    // 0xb477e8: b.eq            #0xb48028
    // 0xb477ec: LoadField: r0 = r5->field_13
    //     0xb477ec: ldur            w0, [x5, #0x13]
    // 0xb477f0: DecompressPointer r0
    //     0xb477f0: add             x0, x0, HEAP, lsl #32
    // 0xb477f4: LoadField: r6 = r0->field_1b
    //     0xb477f4: ldur            w6, [x0, #0x1b]
    // 0xb477f8: DecompressPointer r6
    //     0xb477f8: add             x6, x6, HEAP, lsl #32
    // 0xb477fc: stur            x6, [fp, #-0x20]
    // 0xb47800: cmp             w6, NULL
    // 0xb47804: b.ne            #0xb47810
    // 0xb47808: r0 = Null
    //     0xb47808: mov             x0, NULL
    // 0xb4780c: b               #0xb47850
    // 0xb47810: LoadField: r0 = r6->field_b
    //     0xb47810: ldur            w0, [x6, #0xb]
    // 0xb47814: r1 = LoadInt32Instr(r0)
    //     0xb47814: sbfx            x1, x0, #1, #0x1f
    // 0xb47818: mov             x0, x1
    // 0xb4781c: r1 = 0
    //     0xb4781c: movz            x1, #0
    // 0xb47820: cmp             x1, x0
    // 0xb47824: b.hs            #0xb4802c
    // 0xb47828: LoadField: r0 = r6->field_f
    //     0xb47828: ldur            w0, [x6, #0xf]
    // 0xb4782c: DecompressPointer r0
    //     0xb4782c: add             x0, x0, HEAP, lsl #32
    // 0xb47830: LoadField: r1 = r0->field_f
    //     0xb47830: ldur            w1, [x0, #0xf]
    // 0xb47834: DecompressPointer r1
    //     0xb47834: add             x1, x1, HEAP, lsl #32
    // 0xb47838: cmp             w1, NULL
    // 0xb4783c: b.ne            #0xb47848
    // 0xb47840: r0 = Null
    //     0xb47840: mov             x0, NULL
    // 0xb47844: b               #0xb47850
    // 0xb47848: LoadField: r0 = r1->field_1f
    //     0xb47848: ldur            w0, [x1, #0x1f]
    // 0xb4784c: DecompressPointer r0
    //     0xb4784c: add             x0, x0, HEAP, lsl #32
    // 0xb47850: mov             x1, x3
    // 0xb47854: ArrayStore: r1[6] = r0  ; List_4
    //     0xb47854: add             x25, x1, #0x27
    //     0xb47858: str             w0, [x25]
    //     0xb4785c: tbz             w0, #0, #0xb47878
    //     0xb47860: ldurb           w16, [x1, #-1]
    //     0xb47864: ldurb           w17, [x0, #-1]
    //     0xb47868: and             x16, x17, x16, lsr #2
    //     0xb4786c: tst             x16, HEAP, lsr #32
    //     0xb47870: b.eq            #0xb47878
    //     0xb47874: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb47878: r16 = ", "
    //     0xb47878: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xb4787c: StoreField: r3->field_2b = r16
    //     0xb4787c: stur            w16, [x3, #0x2b]
    // 0xb47880: cmp             w6, NULL
    // 0xb47884: b.ne            #0xb47890
    // 0xb47888: r0 = Null
    //     0xb47888: mov             x0, NULL
    // 0xb4788c: b               #0xb478d0
    // 0xb47890: LoadField: r0 = r6->field_b
    //     0xb47890: ldur            w0, [x6, #0xb]
    // 0xb47894: r1 = LoadInt32Instr(r0)
    //     0xb47894: sbfx            x1, x0, #1, #0x1f
    // 0xb47898: mov             x0, x1
    // 0xb4789c: r1 = 0
    //     0xb4789c: movz            x1, #0
    // 0xb478a0: cmp             x1, x0
    // 0xb478a4: b.hs            #0xb48030
    // 0xb478a8: LoadField: r0 = r6->field_f
    //     0xb478a8: ldur            w0, [x6, #0xf]
    // 0xb478ac: DecompressPointer r0
    //     0xb478ac: add             x0, x0, HEAP, lsl #32
    // 0xb478b0: LoadField: r1 = r0->field_f
    //     0xb478b0: ldur            w1, [x0, #0xf]
    // 0xb478b4: DecompressPointer r1
    //     0xb478b4: add             x1, x1, HEAP, lsl #32
    // 0xb478b8: cmp             w1, NULL
    // 0xb478bc: b.ne            #0xb478c8
    // 0xb478c0: r0 = Null
    //     0xb478c0: mov             x0, NULL
    // 0xb478c4: b               #0xb478d0
    // 0xb478c8: LoadField: r0 = r1->field_23
    //     0xb478c8: ldur            w0, [x1, #0x23]
    // 0xb478cc: DecompressPointer r0
    //     0xb478cc: add             x0, x0, HEAP, lsl #32
    // 0xb478d0: mov             x1, x3
    // 0xb478d4: ArrayStore: r1[8] = r0  ; List_4
    //     0xb478d4: add             x25, x1, #0x2f
    //     0xb478d8: str             w0, [x25]
    //     0xb478dc: tbz             w0, #0, #0xb478f8
    //     0xb478e0: ldurb           w16, [x1, #-1]
    //     0xb478e4: ldurb           w17, [x0, #-1]
    //     0xb478e8: and             x16, x17, x16, lsr #2
    //     0xb478ec: tst             x16, HEAP, lsr #32
    //     0xb478f0: b.eq            #0xb478f8
    //     0xb478f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb478f8: r16 = ", "
    //     0xb478f8: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xb478fc: StoreField: r3->field_33 = r16
    //     0xb478fc: stur            w16, [x3, #0x33]
    // 0xb47900: cmp             w6, NULL
    // 0xb47904: b.ne            #0xb47910
    // 0xb47908: r0 = Null
    //     0xb47908: mov             x0, NULL
    // 0xb4790c: b               #0xb47950
    // 0xb47910: LoadField: r0 = r6->field_b
    //     0xb47910: ldur            w0, [x6, #0xb]
    // 0xb47914: r1 = LoadInt32Instr(r0)
    //     0xb47914: sbfx            x1, x0, #1, #0x1f
    // 0xb47918: mov             x0, x1
    // 0xb4791c: r1 = 0
    //     0xb4791c: movz            x1, #0
    // 0xb47920: cmp             x1, x0
    // 0xb47924: b.hs            #0xb48034
    // 0xb47928: LoadField: r0 = r6->field_f
    //     0xb47928: ldur            w0, [x6, #0xf]
    // 0xb4792c: DecompressPointer r0
    //     0xb4792c: add             x0, x0, HEAP, lsl #32
    // 0xb47930: LoadField: r1 = r0->field_f
    //     0xb47930: ldur            w1, [x0, #0xf]
    // 0xb47934: DecompressPointer r1
    //     0xb47934: add             x1, x1, HEAP, lsl #32
    // 0xb47938: cmp             w1, NULL
    // 0xb4793c: b.ne            #0xb47948
    // 0xb47940: r0 = Null
    //     0xb47940: mov             x0, NULL
    // 0xb47944: b               #0xb47950
    // 0xb47948: LoadField: r0 = r1->field_1b
    //     0xb47948: ldur            w0, [x1, #0x1b]
    // 0xb4794c: DecompressPointer r0
    //     0xb4794c: add             x0, x0, HEAP, lsl #32
    // 0xb47950: mov             x1, x3
    // 0xb47954: ArrayStore: r1[10] = r0  ; List_4
    //     0xb47954: add             x25, x1, #0x37
    //     0xb47958: str             w0, [x25]
    //     0xb4795c: tbz             w0, #0, #0xb47978
    //     0xb47960: ldurb           w16, [x1, #-1]
    //     0xb47964: ldurb           w17, [x0, #-1]
    //     0xb47968: and             x16, x17, x16, lsr #2
    //     0xb4796c: tst             x16, HEAP, lsr #32
    //     0xb47970: b.eq            #0xb47978
    //     0xb47974: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb47978: r16 = "\n"
    //     0xb47978: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xb4797c: StoreField: r3->field_3b = r16
    //     0xb4797c: stur            w16, [x3, #0x3b]
    // 0xb47980: cmp             w6, NULL
    // 0xb47984: b.ne            #0xb47990
    // 0xb47988: r0 = Null
    //     0xb47988: mov             x0, NULL
    // 0xb4798c: b               #0xb479f4
    // 0xb47990: LoadField: r0 = r6->field_b
    //     0xb47990: ldur            w0, [x6, #0xb]
    // 0xb47994: r1 = LoadInt32Instr(r0)
    //     0xb47994: sbfx            x1, x0, #1, #0x1f
    // 0xb47998: mov             x0, x1
    // 0xb4799c: r1 = 0
    //     0xb4799c: movz            x1, #0
    // 0xb479a0: cmp             x1, x0
    // 0xb479a4: b.hs            #0xb48038
    // 0xb479a8: LoadField: r0 = r6->field_f
    //     0xb479a8: ldur            w0, [x6, #0xf]
    // 0xb479ac: DecompressPointer r0
    //     0xb479ac: add             x0, x0, HEAP, lsl #32
    // 0xb479b0: LoadField: r1 = r0->field_f
    //     0xb479b0: ldur            w1, [x0, #0xf]
    // 0xb479b4: DecompressPointer r1
    //     0xb479b4: add             x1, x1, HEAP, lsl #32
    // 0xb479b8: cmp             w1, NULL
    // 0xb479bc: b.ne            #0xb479c8
    // 0xb479c0: r0 = Null
    //     0xb479c0: mov             x0, NULL
    // 0xb479c4: b               #0xb479f4
    // 0xb479c8: LoadField: r0 = r1->field_2f
    //     0xb479c8: ldur            w0, [x1, #0x2f]
    // 0xb479cc: DecompressPointer r0
    //     0xb479cc: add             x0, x0, HEAP, lsl #32
    // 0xb479d0: cmp             w0, NULL
    // 0xb479d4: b.ne            #0xb479e0
    // 0xb479d8: r0 = Null
    //     0xb479d8: mov             x0, NULL
    // 0xb479dc: b               #0xb479f4
    // 0xb479e0: LoadField: r1 = r0->field_7
    //     0xb479e0: ldur            w1, [x0, #7]
    // 0xb479e4: cbnz            w1, #0xb479f0
    // 0xb479e8: r0 = false
    //     0xb479e8: add             x0, NULL, #0x30  ; false
    // 0xb479ec: b               #0xb479f4
    // 0xb479f0: r0 = true
    //     0xb479f0: add             x0, NULL, #0x20  ; true
    // 0xb479f4: cmp             w0, NULL
    // 0xb479f8: b.ne            #0xb47a04
    // 0xb479fc: mov             x0, x5
    // 0xb47a00: b               #0xb47aa4
    // 0xb47a04: tbnz            w0, #4, #0xb47aa0
    // 0xb47a08: r1 = Null
    //     0xb47a08: mov             x1, NULL
    // 0xb47a0c: r2 = 8
    //     0xb47a0c: movz            x2, #0x8
    // 0xb47a10: r0 = AllocateArray()
    //     0xb47a10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb47a14: mov             x2, x0
    // 0xb47a18: r16 = "Contact Number: "
    //     0xb47a18: add             x16, PP, #0x54, lsl #12  ; [pp+0x54790] "Contact Number: "
    //     0xb47a1c: ldr             x16, [x16, #0x790]
    // 0xb47a20: StoreField: r2->field_f = r16
    //     0xb47a20: stur            w16, [x2, #0xf]
    // 0xb47a24: ldur            x0, [fp, #-0x28]
    // 0xb47a28: LoadField: r1 = r0->field_b
    //     0xb47a28: ldur            w1, [x0, #0xb]
    // 0xb47a2c: DecompressPointer r1
    //     0xb47a2c: add             x1, x1, HEAP, lsl #32
    // 0xb47a30: StoreField: r2->field_13 = r1
    //     0xb47a30: stur            w1, [x2, #0x13]
    // 0xb47a34: r16 = ", "
    //     0xb47a34: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xb47a38: ArrayStore: r2[0] = r16  ; List_4
    //     0xb47a38: stur            w16, [x2, #0x17]
    // 0xb47a3c: ldur            x3, [fp, #-0x20]
    // 0xb47a40: cmp             w3, NULL
    // 0xb47a44: b.ne            #0xb47a50
    // 0xb47a48: r0 = Null
    //     0xb47a48: mov             x0, NULL
    // 0xb47a4c: b               #0xb47a90
    // 0xb47a50: LoadField: r0 = r3->field_b
    //     0xb47a50: ldur            w0, [x3, #0xb]
    // 0xb47a54: r1 = LoadInt32Instr(r0)
    //     0xb47a54: sbfx            x1, x0, #1, #0x1f
    // 0xb47a58: mov             x0, x1
    // 0xb47a5c: r1 = 0
    //     0xb47a5c: movz            x1, #0
    // 0xb47a60: cmp             x1, x0
    // 0xb47a64: b.hs            #0xb4803c
    // 0xb47a68: LoadField: r0 = r3->field_f
    //     0xb47a68: ldur            w0, [x3, #0xf]
    // 0xb47a6c: DecompressPointer r0
    //     0xb47a6c: add             x0, x0, HEAP, lsl #32
    // 0xb47a70: LoadField: r1 = r0->field_f
    //     0xb47a70: ldur            w1, [x0, #0xf]
    // 0xb47a74: DecompressPointer r1
    //     0xb47a74: add             x1, x1, HEAP, lsl #32
    // 0xb47a78: cmp             w1, NULL
    // 0xb47a7c: b.ne            #0xb47a88
    // 0xb47a80: r0 = Null
    //     0xb47a80: mov             x0, NULL
    // 0xb47a84: b               #0xb47a90
    // 0xb47a88: LoadField: r0 = r1->field_2f
    //     0xb47a88: ldur            w0, [x1, #0x2f]
    // 0xb47a8c: DecompressPointer r0
    //     0xb47a8c: add             x0, x0, HEAP, lsl #32
    // 0xb47a90: StoreField: r2->field_1b = r0
    //     0xb47a90: stur            w0, [x2, #0x1b]
    // 0xb47a94: str             x2, [SP]
    // 0xb47a98: r0 = _interpolate()
    //     0xb47a98: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb47a9c: b               #0xb47ad4
    // 0xb47aa0: mov             x0, x5
    // 0xb47aa4: r1 = Null
    //     0xb47aa4: mov             x1, NULL
    // 0xb47aa8: r2 = 4
    //     0xb47aa8: movz            x2, #0x4
    // 0xb47aac: r0 = AllocateArray()
    //     0xb47aac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb47ab0: r16 = "Contact Number: "
    //     0xb47ab0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54790] "Contact Number: "
    //     0xb47ab4: ldr             x16, [x16, #0x790]
    // 0xb47ab8: StoreField: r0->field_f = r16
    //     0xb47ab8: stur            w16, [x0, #0xf]
    // 0xb47abc: ldur            x1, [fp, #-0x28]
    // 0xb47ac0: LoadField: r2 = r1->field_b
    //     0xb47ac0: ldur            w2, [x1, #0xb]
    // 0xb47ac4: DecompressPointer r2
    //     0xb47ac4: add             x2, x2, HEAP, lsl #32
    // 0xb47ac8: StoreField: r0->field_13 = r2
    //     0xb47ac8: stur            w2, [x0, #0x13]
    // 0xb47acc: str             x0, [SP]
    // 0xb47ad0: r0 = _interpolate()
    //     0xb47ad0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb47ad4: ldur            x2, [fp, #-8]
    // 0xb47ad8: ldur            x3, [fp, #-0x18]
    // 0xb47adc: ldur            x1, [fp, #-0x40]
    // 0xb47ae0: ArrayStore: r1[12] = r0  ; List_4
    //     0xb47ae0: add             x25, x1, #0x3f
    //     0xb47ae4: str             w0, [x25]
    //     0xb47ae8: tbz             w0, #0, #0xb47b04
    //     0xb47aec: ldurb           w16, [x1, #-1]
    //     0xb47af0: ldurb           w17, [x0, #-1]
    //     0xb47af4: and             x16, x17, x16, lsr #2
    //     0xb47af8: tst             x16, HEAP, lsr #32
    //     0xb47afc: b.eq            #0xb47b04
    //     0xb47b00: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb47b04: ldur            x16, [fp, #-0x40]
    // 0xb47b08: str             x16, [SP]
    // 0xb47b0c: r0 = _interpolate()
    //     0xb47b0c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb47b10: mov             x2, x0
    // 0xb47b14: ldur            x0, [fp, #-0x18]
    // 0xb47b18: stur            x2, [fp, #-0x20]
    // 0xb47b1c: LoadField: r1 = r0->field_13
    //     0xb47b1c: ldur            w1, [x0, #0x13]
    // 0xb47b20: DecompressPointer r1
    //     0xb47b20: add             x1, x1, HEAP, lsl #32
    // 0xb47b24: r0 = of()
    //     0xb47b24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb47b28: LoadField: r1 = r0->field_87
    //     0xb47b28: ldur            w1, [x0, #0x87]
    // 0xb47b2c: DecompressPointer r1
    //     0xb47b2c: add             x1, x1, HEAP, lsl #32
    // 0xb47b30: LoadField: r0 = r1->field_2b
    //     0xb47b30: ldur            w0, [x1, #0x2b]
    // 0xb47b34: DecompressPointer r0
    //     0xb47b34: add             x0, x0, HEAP, lsl #32
    // 0xb47b38: stur            x0, [fp, #-0x28]
    // 0xb47b3c: r1 = Instance_Color
    //     0xb47b3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb47b40: d0 = 0.700000
    //     0xb47b40: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb47b44: ldr             d0, [x17, #0xf48]
    // 0xb47b48: r0 = withOpacity()
    //     0xb47b48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb47b4c: r16 = 12.000000
    //     0xb47b4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb47b50: ldr             x16, [x16, #0x9e8]
    // 0xb47b54: stp             x16, x0, [SP]
    // 0xb47b58: ldur            x1, [fp, #-0x28]
    // 0xb47b5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb47b5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb47b60: ldr             x4, [x4, #0x9b8]
    // 0xb47b64: r0 = copyWith()
    //     0xb47b64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb47b68: stur            x0, [fp, #-0x28]
    // 0xb47b6c: r0 = Text()
    //     0xb47b6c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb47b70: mov             x1, x0
    // 0xb47b74: ldur            x0, [fp, #-0x20]
    // 0xb47b78: stur            x1, [fp, #-0x40]
    // 0xb47b7c: StoreField: r1->field_b = r0
    //     0xb47b7c: stur            w0, [x1, #0xb]
    // 0xb47b80: ldur            x0, [fp, #-0x28]
    // 0xb47b84: StoreField: r1->field_13 = r0
    //     0xb47b84: stur            w0, [x1, #0x13]
    // 0xb47b88: r0 = Padding()
    //     0xb47b88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb47b8c: mov             x2, x0
    // 0xb47b90: r0 = Instance_EdgeInsets
    //     0xb47b90: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xb47b94: ldr             x0, [x0, #0x868]
    // 0xb47b98: stur            x2, [fp, #-0x28]
    // 0xb47b9c: StoreField: r2->field_f = r0
    //     0xb47b9c: stur            w0, [x2, #0xf]
    // 0xb47ba0: ldur            x0, [fp, #-0x40]
    // 0xb47ba4: StoreField: r2->field_b = r0
    //     0xb47ba4: stur            w0, [x2, #0xb]
    // 0xb47ba8: ldur            x0, [fp, #-8]
    // 0xb47bac: LoadField: r1 = r0->field_b
    //     0xb47bac: ldur            w1, [x0, #0xb]
    // 0xb47bb0: DecompressPointer r1
    //     0xb47bb0: add             x1, x1, HEAP, lsl #32
    // 0xb47bb4: cmp             w1, NULL
    // 0xb47bb8: b.eq            #0xb48040
    // 0xb47bbc: LoadField: r3 = r1->field_f
    //     0xb47bbc: ldur            w3, [x1, #0xf]
    // 0xb47bc0: DecompressPointer r3
    //     0xb47bc0: add             x3, x3, HEAP, lsl #32
    // 0xb47bc4: LoadField: r1 = r3->field_b
    //     0xb47bc4: ldur            w1, [x3, #0xb]
    // 0xb47bc8: DecompressPointer r1
    //     0xb47bc8: add             x1, x1, HEAP, lsl #32
    // 0xb47bcc: cmp             w1, NULL
    // 0xb47bd0: b.ne            #0xb47bdc
    // 0xb47bd4: r1 = Null
    //     0xb47bd4: mov             x1, NULL
    // 0xb47bd8: b               #0xb47c0c
    // 0xb47bdc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb47bdc: ldur            w3, [x1, #0x17]
    // 0xb47be0: DecompressPointer r3
    //     0xb47be0: add             x3, x3, HEAP, lsl #32
    // 0xb47be4: cmp             w3, NULL
    // 0xb47be8: b.ne            #0xb47bf4
    // 0xb47bec: r1 = Null
    //     0xb47bec: mov             x1, NULL
    // 0xb47bf0: b               #0xb47c0c
    // 0xb47bf4: LoadField: r1 = r3->field_7
    //     0xb47bf4: ldur            w1, [x3, #7]
    // 0xb47bf8: cbnz            w1, #0xb47c04
    // 0xb47bfc: r3 = false
    //     0xb47bfc: add             x3, NULL, #0x30  ; false
    // 0xb47c00: b               #0xb47c08
    // 0xb47c04: r3 = true
    //     0xb47c04: add             x3, NULL, #0x20  ; true
    // 0xb47c08: mov             x1, x3
    // 0xb47c0c: cmp             w1, NULL
    // 0xb47c10: b.ne            #0xb47c1c
    // 0xb47c14: r4 = false
    //     0xb47c14: add             x4, NULL, #0x30  ; false
    // 0xb47c18: b               #0xb47c20
    // 0xb47c1c: mov             x4, x1
    // 0xb47c20: ldur            x3, [fp, #-0x18]
    // 0xb47c24: stur            x4, [fp, #-0x20]
    // 0xb47c28: LoadField: r1 = r3->field_13
    //     0xb47c28: ldur            w1, [x3, #0x13]
    // 0xb47c2c: DecompressPointer r1
    //     0xb47c2c: add             x1, x1, HEAP, lsl #32
    // 0xb47c30: r0 = of()
    //     0xb47c30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb47c34: LoadField: r1 = r0->field_5b
    //     0xb47c34: ldur            w1, [x0, #0x5b]
    // 0xb47c38: DecompressPointer r1
    //     0xb47c38: add             x1, x1, HEAP, lsl #32
    // 0xb47c3c: r0 = LoadClassIdInstr(r1)
    //     0xb47c3c: ldur            x0, [x1, #-1]
    //     0xb47c40: ubfx            x0, x0, #0xc, #0x14
    // 0xb47c44: d0 = 0.030000
    //     0xb47c44: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb47c48: ldr             d0, [x17, #0x238]
    // 0xb47c4c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb47c4c: sub             lr, x0, #0xffa
    //     0xb47c50: ldr             lr, [x21, lr, lsl #3]
    //     0xb47c54: blr             lr
    // 0xb47c58: stur            x0, [fp, #-0x40]
    // 0xb47c5c: r0 = Radius()
    //     0xb47c5c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb47c60: d0 = 30.000000
    //     0xb47c60: fmov            d0, #30.00000000
    // 0xb47c64: stur            x0, [fp, #-0x48]
    // 0xb47c68: StoreField: r0->field_7 = d0
    //     0xb47c68: stur            d0, [x0, #7]
    // 0xb47c6c: StoreField: r0->field_f = d0
    //     0xb47c6c: stur            d0, [x0, #0xf]
    // 0xb47c70: r0 = BorderRadius()
    //     0xb47c70: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb47c74: mov             x1, x0
    // 0xb47c78: ldur            x0, [fp, #-0x48]
    // 0xb47c7c: stur            x1, [fp, #-0x50]
    // 0xb47c80: StoreField: r1->field_7 = r0
    //     0xb47c80: stur            w0, [x1, #7]
    // 0xb47c84: StoreField: r1->field_b = r0
    //     0xb47c84: stur            w0, [x1, #0xb]
    // 0xb47c88: StoreField: r1->field_f = r0
    //     0xb47c88: stur            w0, [x1, #0xf]
    // 0xb47c8c: StoreField: r1->field_13 = r0
    //     0xb47c8c: stur            w0, [x1, #0x13]
    // 0xb47c90: r0 = BoxDecoration()
    //     0xb47c90: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb47c94: mov             x2, x0
    // 0xb47c98: ldur            x0, [fp, #-0x40]
    // 0xb47c9c: stur            x2, [fp, #-0x48]
    // 0xb47ca0: StoreField: r2->field_7 = r0
    //     0xb47ca0: stur            w0, [x2, #7]
    // 0xb47ca4: ldur            x0, [fp, #-0x50]
    // 0xb47ca8: StoreField: r2->field_13 = r0
    //     0xb47ca8: stur            w0, [x2, #0x13]
    // 0xb47cac: r0 = Instance_BoxShape
    //     0xb47cac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb47cb0: ldr             x0, [x0, #0x80]
    // 0xb47cb4: StoreField: r2->field_23 = r0
    //     0xb47cb4: stur            w0, [x2, #0x23]
    // 0xb47cb8: ldur            x0, [fp, #-8]
    // 0xb47cbc: LoadField: r1 = r0->field_b
    //     0xb47cbc: ldur            w1, [x0, #0xb]
    // 0xb47cc0: DecompressPointer r1
    //     0xb47cc0: add             x1, x1, HEAP, lsl #32
    // 0xb47cc4: cmp             w1, NULL
    // 0xb47cc8: b.eq            #0xb48044
    // 0xb47ccc: LoadField: r0 = r1->field_f
    //     0xb47ccc: ldur            w0, [x1, #0xf]
    // 0xb47cd0: DecompressPointer r0
    //     0xb47cd0: add             x0, x0, HEAP, lsl #32
    // 0xb47cd4: LoadField: r1 = r0->field_b
    //     0xb47cd4: ldur            w1, [x0, #0xb]
    // 0xb47cd8: DecompressPointer r1
    //     0xb47cd8: add             x1, x1, HEAP, lsl #32
    // 0xb47cdc: cmp             w1, NULL
    // 0xb47ce0: b.ne            #0xb47cec
    // 0xb47ce4: r0 = Null
    //     0xb47ce4: mov             x0, NULL
    // 0xb47ce8: b               #0xb47cf4
    // 0xb47cec: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb47cec: ldur            w0, [x1, #0x17]
    // 0xb47cf0: DecompressPointer r0
    //     0xb47cf0: add             x0, x0, HEAP, lsl #32
    // 0xb47cf4: cmp             w0, NULL
    // 0xb47cf8: b.ne            #0xb47d04
    // 0xb47cfc: r6 = ""
    //     0xb47cfc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb47d00: b               #0xb47d08
    // 0xb47d04: mov             x6, x0
    // 0xb47d08: ldur            x1, [fp, #-0x18]
    // 0xb47d0c: ldur            x5, [fp, #-0x10]
    // 0xb47d10: ldur            x4, [fp, #-0x38]
    // 0xb47d14: ldur            x0, [fp, #-0x28]
    // 0xb47d18: ldur            x3, [fp, #-0x20]
    // 0xb47d1c: stur            x6, [fp, #-8]
    // 0xb47d20: LoadField: r7 = r1->field_13
    //     0xb47d20: ldur            w7, [x1, #0x13]
    // 0xb47d24: DecompressPointer r7
    //     0xb47d24: add             x7, x7, HEAP, lsl #32
    // 0xb47d28: mov             x1, x7
    // 0xb47d2c: r0 = of()
    //     0xb47d2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb47d30: LoadField: r1 = r0->field_87
    //     0xb47d30: ldur            w1, [x0, #0x87]
    // 0xb47d34: DecompressPointer r1
    //     0xb47d34: add             x1, x1, HEAP, lsl #32
    // 0xb47d38: LoadField: r0 = r1->field_7
    //     0xb47d38: ldur            w0, [x1, #7]
    // 0xb47d3c: DecompressPointer r0
    //     0xb47d3c: add             x0, x0, HEAP, lsl #32
    // 0xb47d40: stur            x0, [fp, #-0x18]
    // 0xb47d44: r1 = Instance_Color
    //     0xb47d44: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb47d48: d0 = 0.700000
    //     0xb47d48: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb47d4c: ldr             d0, [x17, #0xf48]
    // 0xb47d50: r0 = withOpacity()
    //     0xb47d50: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb47d54: r16 = 12.000000
    //     0xb47d54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb47d58: ldr             x16, [x16, #0x9e8]
    // 0xb47d5c: stp             x16, x0, [SP]
    // 0xb47d60: ldur            x1, [fp, #-0x18]
    // 0xb47d64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb47d64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb47d68: ldr             x4, [x4, #0x9b8]
    // 0xb47d6c: r0 = copyWith()
    //     0xb47d6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb47d70: stur            x0, [fp, #-0x18]
    // 0xb47d74: r0 = Text()
    //     0xb47d74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb47d78: mov             x1, x0
    // 0xb47d7c: ldur            x0, [fp, #-8]
    // 0xb47d80: stur            x1, [fp, #-0x40]
    // 0xb47d84: StoreField: r1->field_b = r0
    //     0xb47d84: stur            w0, [x1, #0xb]
    // 0xb47d88: ldur            x0, [fp, #-0x18]
    // 0xb47d8c: StoreField: r1->field_13 = r0
    //     0xb47d8c: stur            w0, [x1, #0x13]
    // 0xb47d90: r0 = Center()
    //     0xb47d90: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb47d94: mov             x1, x0
    // 0xb47d98: r0 = Instance_Alignment
    //     0xb47d98: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb47d9c: ldr             x0, [x0, #0xb10]
    // 0xb47da0: stur            x1, [fp, #-8]
    // 0xb47da4: StoreField: r1->field_f = r0
    //     0xb47da4: stur            w0, [x1, #0xf]
    // 0xb47da8: ldur            x0, [fp, #-0x40]
    // 0xb47dac: StoreField: r1->field_b = r0
    //     0xb47dac: stur            w0, [x1, #0xb]
    // 0xb47db0: r0 = Padding()
    //     0xb47db0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb47db4: mov             x1, x0
    // 0xb47db8: r0 = Instance_EdgeInsets
    //     0xb47db8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb47dbc: ldr             x0, [x0, #0x1f0]
    // 0xb47dc0: stur            x1, [fp, #-0x18]
    // 0xb47dc4: StoreField: r1->field_f = r0
    //     0xb47dc4: stur            w0, [x1, #0xf]
    // 0xb47dc8: ldur            x2, [fp, #-8]
    // 0xb47dcc: StoreField: r1->field_b = r2
    //     0xb47dcc: stur            w2, [x1, #0xb]
    // 0xb47dd0: r0 = Container()
    //     0xb47dd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb47dd4: stur            x0, [fp, #-8]
    // 0xb47dd8: ldur            x16, [fp, #-0x48]
    // 0xb47ddc: ldur            lr, [fp, #-0x18]
    // 0xb47de0: stp             lr, x16, [SP]
    // 0xb47de4: mov             x1, x0
    // 0xb47de8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb47de8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb47dec: ldr             x4, [x4, #0x88]
    // 0xb47df0: r0 = Container()
    //     0xb47df0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb47df4: r0 = Visibility()
    //     0xb47df4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb47df8: mov             x3, x0
    // 0xb47dfc: ldur            x0, [fp, #-8]
    // 0xb47e00: stur            x3, [fp, #-0x18]
    // 0xb47e04: StoreField: r3->field_b = r0
    //     0xb47e04: stur            w0, [x3, #0xb]
    // 0xb47e08: r0 = Instance_SizedBox
    //     0xb47e08: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb47e0c: StoreField: r3->field_f = r0
    //     0xb47e0c: stur            w0, [x3, #0xf]
    // 0xb47e10: ldur            x0, [fp, #-0x20]
    // 0xb47e14: StoreField: r3->field_13 = r0
    //     0xb47e14: stur            w0, [x3, #0x13]
    // 0xb47e18: r0 = false
    //     0xb47e18: add             x0, NULL, #0x30  ; false
    // 0xb47e1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb47e1c: stur            w0, [x3, #0x17]
    // 0xb47e20: StoreField: r3->field_1b = r0
    //     0xb47e20: stur            w0, [x3, #0x1b]
    // 0xb47e24: StoreField: r3->field_1f = r0
    //     0xb47e24: stur            w0, [x3, #0x1f]
    // 0xb47e28: StoreField: r3->field_23 = r0
    //     0xb47e28: stur            w0, [x3, #0x23]
    // 0xb47e2c: StoreField: r3->field_27 = r0
    //     0xb47e2c: stur            w0, [x3, #0x27]
    // 0xb47e30: StoreField: r3->field_2b = r0
    //     0xb47e30: stur            w0, [x3, #0x2b]
    // 0xb47e34: r1 = Null
    //     0xb47e34: mov             x1, NULL
    // 0xb47e38: r2 = 6
    //     0xb47e38: movz            x2, #0x6
    // 0xb47e3c: r0 = AllocateArray()
    //     0xb47e3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb47e40: mov             x2, x0
    // 0xb47e44: ldur            x0, [fp, #-0x38]
    // 0xb47e48: stur            x2, [fp, #-8]
    // 0xb47e4c: StoreField: r2->field_f = r0
    //     0xb47e4c: stur            w0, [x2, #0xf]
    // 0xb47e50: ldur            x0, [fp, #-0x28]
    // 0xb47e54: StoreField: r2->field_13 = r0
    //     0xb47e54: stur            w0, [x2, #0x13]
    // 0xb47e58: ldur            x0, [fp, #-0x18]
    // 0xb47e5c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb47e5c: stur            w0, [x2, #0x17]
    // 0xb47e60: r1 = <Widget>
    //     0xb47e60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb47e64: r0 = AllocateGrowableArray()
    //     0xb47e64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb47e68: mov             x1, x0
    // 0xb47e6c: ldur            x0, [fp, #-8]
    // 0xb47e70: stur            x1, [fp, #-0x18]
    // 0xb47e74: StoreField: r1->field_f = r0
    //     0xb47e74: stur            w0, [x1, #0xf]
    // 0xb47e78: r0 = 6
    //     0xb47e78: movz            x0, #0x6
    // 0xb47e7c: StoreField: r1->field_b = r0
    //     0xb47e7c: stur            w0, [x1, #0xb]
    // 0xb47e80: r0 = Column()
    //     0xb47e80: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb47e84: mov             x1, x0
    // 0xb47e88: r0 = Instance_Axis
    //     0xb47e88: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb47e8c: stur            x1, [fp, #-8]
    // 0xb47e90: StoreField: r1->field_f = r0
    //     0xb47e90: stur            w0, [x1, #0xf]
    // 0xb47e94: r2 = Instance_MainAxisAlignment
    //     0xb47e94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb47e98: ldr             x2, [x2, #0xa08]
    // 0xb47e9c: StoreField: r1->field_13 = r2
    //     0xb47e9c: stur            w2, [x1, #0x13]
    // 0xb47ea0: r3 = Instance_MainAxisSize
    //     0xb47ea0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb47ea4: ldr             x3, [x3, #0xa10]
    // 0xb47ea8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb47ea8: stur            w3, [x1, #0x17]
    // 0xb47eac: r4 = Instance_CrossAxisAlignment
    //     0xb47eac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb47eb0: ldr             x4, [x4, #0x890]
    // 0xb47eb4: StoreField: r1->field_1b = r4
    //     0xb47eb4: stur            w4, [x1, #0x1b]
    // 0xb47eb8: r5 = Instance_VerticalDirection
    //     0xb47eb8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb47ebc: ldr             x5, [x5, #0xa20]
    // 0xb47ec0: StoreField: r1->field_23 = r5
    //     0xb47ec0: stur            w5, [x1, #0x23]
    // 0xb47ec4: r6 = Instance_Clip
    //     0xb47ec4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb47ec8: ldr             x6, [x6, #0x38]
    // 0xb47ecc: StoreField: r1->field_2b = r6
    //     0xb47ecc: stur            w6, [x1, #0x2b]
    // 0xb47ed0: StoreField: r1->field_2f = rZR
    //     0xb47ed0: stur            xzr, [x1, #0x2f]
    // 0xb47ed4: ldur            x7, [fp, #-0x18]
    // 0xb47ed8: StoreField: r1->field_b = r7
    //     0xb47ed8: stur            w7, [x1, #0xb]
    // 0xb47edc: r0 = Padding()
    //     0xb47edc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb47ee0: mov             x1, x0
    // 0xb47ee4: r0 = Instance_EdgeInsets
    //     0xb47ee4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb47ee8: ldr             x0, [x0, #0x1f0]
    // 0xb47eec: stur            x1, [fp, #-0x18]
    // 0xb47ef0: StoreField: r1->field_f = r0
    //     0xb47ef0: stur            w0, [x1, #0xf]
    // 0xb47ef4: ldur            x2, [fp, #-8]
    // 0xb47ef8: StoreField: r1->field_b = r2
    //     0xb47ef8: stur            w2, [x1, #0xb]
    // 0xb47efc: r0 = Container()
    //     0xb47efc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb47f00: stur            x0, [fp, #-8]
    // 0xb47f04: ldur            x16, [fp, #-0x30]
    // 0xb47f08: ldur            lr, [fp, #-0x18]
    // 0xb47f0c: stp             lr, x16, [SP]
    // 0xb47f10: mov             x1, x0
    // 0xb47f14: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb47f14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb47f18: ldr             x4, [x4, #0x88]
    // 0xb47f1c: r0 = Container()
    //     0xb47f1c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb47f20: r0 = Padding()
    //     0xb47f20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb47f24: mov             x3, x0
    // 0xb47f28: r0 = Instance_EdgeInsets
    //     0xb47f28: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xb47f2c: ldr             x0, [x0, #0xb00]
    // 0xb47f30: stur            x3, [fp, #-0x18]
    // 0xb47f34: StoreField: r3->field_f = r0
    //     0xb47f34: stur            w0, [x3, #0xf]
    // 0xb47f38: ldur            x0, [fp, #-8]
    // 0xb47f3c: StoreField: r3->field_b = r0
    //     0xb47f3c: stur            w0, [x3, #0xb]
    // 0xb47f40: r1 = Null
    //     0xb47f40: mov             x1, NULL
    // 0xb47f44: r2 = 4
    //     0xb47f44: movz            x2, #0x4
    // 0xb47f48: r0 = AllocateArray()
    //     0xb47f48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb47f4c: mov             x2, x0
    // 0xb47f50: ldur            x0, [fp, #-0x10]
    // 0xb47f54: stur            x2, [fp, #-8]
    // 0xb47f58: StoreField: r2->field_f = r0
    //     0xb47f58: stur            w0, [x2, #0xf]
    // 0xb47f5c: ldur            x0, [fp, #-0x18]
    // 0xb47f60: StoreField: r2->field_13 = r0
    //     0xb47f60: stur            w0, [x2, #0x13]
    // 0xb47f64: r1 = <Widget>
    //     0xb47f64: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb47f68: r0 = AllocateGrowableArray()
    //     0xb47f68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb47f6c: mov             x1, x0
    // 0xb47f70: ldur            x0, [fp, #-8]
    // 0xb47f74: stur            x1, [fp, #-0x10]
    // 0xb47f78: StoreField: r1->field_f = r0
    //     0xb47f78: stur            w0, [x1, #0xf]
    // 0xb47f7c: r0 = 4
    //     0xb47f7c: movz            x0, #0x4
    // 0xb47f80: StoreField: r1->field_b = r0
    //     0xb47f80: stur            w0, [x1, #0xb]
    // 0xb47f84: r0 = Column()
    //     0xb47f84: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb47f88: mov             x1, x0
    // 0xb47f8c: r0 = Instance_Axis
    //     0xb47f8c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb47f90: stur            x1, [fp, #-8]
    // 0xb47f94: StoreField: r1->field_f = r0
    //     0xb47f94: stur            w0, [x1, #0xf]
    // 0xb47f98: r0 = Instance_MainAxisAlignment
    //     0xb47f98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb47f9c: ldr             x0, [x0, #0xa08]
    // 0xb47fa0: StoreField: r1->field_13 = r0
    //     0xb47fa0: stur            w0, [x1, #0x13]
    // 0xb47fa4: r0 = Instance_MainAxisSize
    //     0xb47fa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb47fa8: ldr             x0, [x0, #0xa10]
    // 0xb47fac: ArrayStore: r1[0] = r0  ; List_4
    //     0xb47fac: stur            w0, [x1, #0x17]
    // 0xb47fb0: r0 = Instance_CrossAxisAlignment
    //     0xb47fb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb47fb4: ldr             x0, [x0, #0x890]
    // 0xb47fb8: StoreField: r1->field_1b = r0
    //     0xb47fb8: stur            w0, [x1, #0x1b]
    // 0xb47fbc: r0 = Instance_VerticalDirection
    //     0xb47fbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb47fc0: ldr             x0, [x0, #0xa20]
    // 0xb47fc4: StoreField: r1->field_23 = r0
    //     0xb47fc4: stur            w0, [x1, #0x23]
    // 0xb47fc8: r0 = Instance_Clip
    //     0xb47fc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb47fcc: ldr             x0, [x0, #0x38]
    // 0xb47fd0: StoreField: r1->field_2b = r0
    //     0xb47fd0: stur            w0, [x1, #0x2b]
    // 0xb47fd4: StoreField: r1->field_2f = rZR
    //     0xb47fd4: stur            xzr, [x1, #0x2f]
    // 0xb47fd8: ldur            x0, [fp, #-0x10]
    // 0xb47fdc: StoreField: r1->field_b = r0
    //     0xb47fdc: stur            w0, [x1, #0xb]
    // 0xb47fe0: r0 = Padding()
    //     0xb47fe0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb47fe4: r1 = Instance_EdgeInsets
    //     0xb47fe4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb47fe8: ldr             x1, [x1, #0x1f0]
    // 0xb47fec: StoreField: r0->field_f = r1
    //     0xb47fec: stur            w1, [x0, #0xf]
    // 0xb47ff0: ldur            x1, [fp, #-8]
    // 0xb47ff4: StoreField: r0->field_b = r1
    //     0xb47ff4: stur            w1, [x0, #0xb]
    // 0xb47ff8: LeaveFrame
    //     0xb47ff8: mov             SP, fp
    //     0xb47ffc: ldp             fp, lr, [SP], #0x10
    // 0xb48000: ret
    //     0xb48000: ret             
    // 0xb48004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb48004: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb48008: b               #0xb4719c
    // 0xb4800c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4800c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48018: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb48018: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb4801c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4801c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48020: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb48020: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48024: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb48024: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48028: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48028: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4802c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4802c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48030: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb48030: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48034: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb48034: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48038: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb48038: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb4803c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4803c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb48040: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48040: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48044: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb48048, size: 0xe0
    // 0xb48048: EnterFrame
    //     0xb48048: stp             fp, lr, [SP, #-0x10]!
    //     0xb4804c: mov             fp, SP
    // 0xb48050: AllocStack(0x20)
    //     0xb48050: sub             SP, SP, #0x20
    // 0xb48054: SetupParameters()
    //     0xb48054: ldr             x0, [fp, #0x10]
    //     0xb48058: ldur            w1, [x0, #0x17]
    //     0xb4805c: add             x1, x1, HEAP, lsl #32
    //     0xb48060: stur            x1, [fp, #-8]
    // 0xb48064: CheckStackOverflow
    //     0xb48064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb48068: cmp             SP, x16
    //     0xb4806c: b.ls            #0xb48118
    // 0xb48070: LoadField: r0 = r1->field_f
    //     0xb48070: ldur            w0, [x1, #0xf]
    // 0xb48074: DecompressPointer r0
    //     0xb48074: add             x0, x0, HEAP, lsl #32
    // 0xb48078: LoadField: r2 = r0->field_b
    //     0xb48078: ldur            w2, [x0, #0xb]
    // 0xb4807c: DecompressPointer r2
    //     0xb4807c: add             x2, x2, HEAP, lsl #32
    // 0xb48080: cmp             w2, NULL
    // 0xb48084: b.eq            #0xb48120
    // 0xb48088: LoadField: r0 = r1->field_13
    //     0xb48088: ldur            w0, [x1, #0x13]
    // 0xb4808c: DecompressPointer r0
    //     0xb4808c: add             x0, x0, HEAP, lsl #32
    // 0xb48090: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb48090: ldur            w3, [x2, #0x17]
    // 0xb48094: DecompressPointer r3
    //     0xb48094: add             x3, x3, HEAP, lsl #32
    // 0xb48098: stp             x0, x3, [SP]
    // 0xb4809c: r4 = 0
    //     0xb4809c: movz            x4, #0
    // 0xb480a0: ldr             x0, [SP, #8]
    // 0xb480a4: r16 = UnlinkedCall_0x613b5c
    //     0xb480a4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56e18] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb480a8: add             x16, x16, #0xe18
    // 0xb480ac: ldp             x5, lr, [x16]
    // 0xb480b0: blr             lr
    // 0xb480b4: ldur            x0, [fp, #-8]
    // 0xb480b8: LoadField: r1 = r0->field_f
    //     0xb480b8: ldur            w1, [x0, #0xf]
    // 0xb480bc: DecompressPointer r1
    //     0xb480bc: add             x1, x1, HEAP, lsl #32
    // 0xb480c0: LoadField: r0 = r1->field_b
    //     0xb480c0: ldur            w0, [x1, #0xb]
    // 0xb480c4: DecompressPointer r0
    //     0xb480c4: add             x0, x0, HEAP, lsl #32
    // 0xb480c8: cmp             w0, NULL
    // 0xb480cc: b.eq            #0xb48124
    // 0xb480d0: LoadField: r1 = r0->field_1f
    //     0xb480d0: ldur            w1, [x0, #0x1f]
    // 0xb480d4: DecompressPointer r1
    //     0xb480d4: add             x1, x1, HEAP, lsl #32
    // 0xb480d8: r16 = "change"
    //     0xb480d8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d3c8] "change"
    //     0xb480dc: ldr             x16, [x16, #0x3c8]
    // 0xb480e0: stp             x16, x1, [SP, #8]
    // 0xb480e4: r16 = "Change"
    //     0xb480e4: add             x16, PP, #0x54, lsl #12  ; [pp+0x547a8] "Change"
    //     0xb480e8: ldr             x16, [x16, #0x7a8]
    // 0xb480ec: str             x16, [SP]
    // 0xb480f0: r4 = 0
    //     0xb480f0: movz            x4, #0
    // 0xb480f4: ldr             x0, [SP, #0x10]
    // 0xb480f8: r16 = UnlinkedCall_0x613b5c
    //     0xb480f8: add             x16, PP, #0x56, lsl #12  ; [pp+0x56e28] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb480fc: add             x16, x16, #0xe28
    // 0xb48100: ldp             x5, lr, [x16]
    // 0xb48104: blr             lr
    // 0xb48108: r0 = Null
    //     0xb48108: mov             x0, NULL
    // 0xb4810c: LeaveFrame
    //     0xb4810c: mov             SP, fp
    //     0xb48110: ldp             fp, lr, [SP], #0x10
    // 0xb48114: ret
    //     0xb48114: ret             
    // 0xb48118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb48118: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4811c: b               #0xb48070
    // 0xb48120: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48120: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48124: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48124: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4102, size: 0x28, field offset: 0xc
class DeliveryAddressWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ec14, size: 0x24
    // 0xc7ec14: EnterFrame
    //     0xc7ec14: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ec18: mov             fp, SP
    // 0xc7ec1c: mov             x0, x1
    // 0xc7ec20: r1 = <DeliveryAddressWidget>
    //     0xc7ec20: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a58] TypeArguments: <DeliveryAddressWidget>
    //     0xc7ec24: ldr             x1, [x1, #0xa58]
    // 0xc7ec28: r0 = _DeliveryAddressWidgetState()
    //     0xc7ec28: bl              #0xc7ec38  ; Allocate_DeliveryAddressWidgetStateStub -> _DeliveryAddressWidgetState (size=0x14)
    // 0xc7ec2c: LeaveFrame
    //     0xc7ec2c: mov             SP, fp
    //     0xc7ec30: ldp             fp, lr, [SP], #0x10
    // 0xc7ec34: ret
    //     0xc7ec34: ret             
  }
}
