// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart

// class id: 1049407, size: 0x8
class :: {
}

// class id: 3331, size: 0x24, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb6e824, size: 0x64
    // 0xb6e824: EnterFrame
    //     0xb6e824: stp             fp, lr, [SP, #-0x10]!
    //     0xb6e828: mov             fp, SP
    // 0xb6e82c: AllocStack(0x18)
    //     0xb6e82c: sub             SP, SP, #0x18
    // 0xb6e830: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb6e830: stur            x1, [fp, #-8]
    //     0xb6e834: stur            x2, [fp, #-0x10]
    // 0xb6e838: r1 = 2
    //     0xb6e838: movz            x1, #0x2
    // 0xb6e83c: r0 = AllocateContext()
    //     0xb6e83c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6e840: mov             x1, x0
    // 0xb6e844: ldur            x0, [fp, #-8]
    // 0xb6e848: stur            x1, [fp, #-0x18]
    // 0xb6e84c: StoreField: r1->field_f = r0
    //     0xb6e84c: stur            w0, [x1, #0xf]
    // 0xb6e850: ldur            x0, [fp, #-0x10]
    // 0xb6e854: StoreField: r1->field_13 = r0
    //     0xb6e854: stur            w0, [x1, #0x13]
    // 0xb6e858: r0 = Obx()
    //     0xb6e858: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb6e85c: ldur            x2, [fp, #-0x18]
    // 0xb6e860: r1 = Function '<anonymous closure>':.
    //     0xb6e860: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b28] AnonymousClosure: (0xb6e8a8), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb6e824)
    //     0xb6e864: ldr             x1, [x1, #0xb28]
    // 0xb6e868: stur            x0, [fp, #-8]
    // 0xb6e86c: r0 = AllocateClosure()
    //     0xb6e86c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6e870: mov             x1, x0
    // 0xb6e874: ldur            x0, [fp, #-8]
    // 0xb6e878: StoreField: r0->field_b = r1
    //     0xb6e878: stur            w1, [x0, #0xb]
    // 0xb6e87c: LeaveFrame
    //     0xb6e87c: mov             SP, fp
    //     0xb6e880: ldp             fp, lr, [SP], #0x10
    // 0xb6e884: ret
    //     0xb6e884: ret             
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xb6e8a8, size: 0x8ec
    // 0xb6e8a8: EnterFrame
    //     0xb6e8a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb6e8ac: mov             fp, SP
    // 0xb6e8b0: AllocStack(0x78)
    //     0xb6e8b0: sub             SP, SP, #0x78
    // 0xb6e8b4: SetupParameters()
    //     0xb6e8b4: ldr             x0, [fp, #0x10]
    //     0xb6e8b8: ldur            w3, [x0, #0x17]
    //     0xb6e8bc: add             x3, x3, HEAP, lsl #32
    //     0xb6e8c0: stur            x3, [fp, #-0x10]
    // 0xb6e8c4: CheckStackOverflow
    //     0xb6e8c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6e8c8: cmp             SP, x16
    //     0xb6e8cc: b.ls            #0xb6f158
    // 0xb6e8d0: LoadField: r0 = r3->field_f
    //     0xb6e8d0: ldur            w0, [x3, #0xf]
    // 0xb6e8d4: DecompressPointer r0
    //     0xb6e8d4: add             x0, x0, HEAP, lsl #32
    // 0xb6e8d8: LoadField: r1 = r0->field_b
    //     0xb6e8d8: ldur            w1, [x0, #0xb]
    // 0xb6e8dc: DecompressPointer r1
    //     0xb6e8dc: add             x1, x1, HEAP, lsl #32
    // 0xb6e8e0: cmp             w1, NULL
    // 0xb6e8e4: b.eq            #0xb6f160
    // 0xb6e8e8: LoadField: r0 = r1->field_13
    //     0xb6e8e8: ldur            w0, [x1, #0x13]
    // 0xb6e8ec: DecompressPointer r0
    //     0xb6e8ec: add             x0, x0, HEAP, lsl #32
    // 0xb6e8f0: cmp             w0, NULL
    // 0xb6e8f4: b.ne            #0xb6e900
    // 0xb6e8f8: r0 = Null
    //     0xb6e8f8: mov             x0, NULL
    // 0xb6e8fc: b               #0xb6e90c
    // 0xb6e900: LoadField: r1 = r0->field_7
    //     0xb6e900: ldur            w1, [x0, #7]
    // 0xb6e904: DecompressPointer r1
    //     0xb6e904: add             x1, x1, HEAP, lsl #32
    // 0xb6e908: mov             x0, x1
    // 0xb6e90c: cmp             w0, NULL
    // 0xb6e910: b.ne            #0xb6e91c
    // 0xb6e914: r0 = Instance_TitleAlignment
    //     0xb6e914: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb6e918: ldr             x0, [x0, #0x518]
    // 0xb6e91c: r16 = Instance_TitleAlignment
    //     0xb6e91c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb6e920: ldr             x16, [x16, #0x520]
    // 0xb6e924: cmp             w0, w16
    // 0xb6e928: b.ne            #0xb6e938
    // 0xb6e92c: r0 = Instance_CrossAxisAlignment
    //     0xb6e92c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb6e930: ldr             x0, [x0, #0xc68]
    // 0xb6e934: b               #0xb6e95c
    // 0xb6e938: r16 = Instance_TitleAlignment
    //     0xb6e938: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb6e93c: ldr             x16, [x16, #0x518]
    // 0xb6e940: cmp             w0, w16
    // 0xb6e944: b.ne            #0xb6e954
    // 0xb6e948: r0 = Instance_CrossAxisAlignment
    //     0xb6e948: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb6e94c: ldr             x0, [x0, #0x890]
    // 0xb6e950: b               #0xb6e95c
    // 0xb6e954: r0 = Instance_CrossAxisAlignment
    //     0xb6e954: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb6e958: ldr             x0, [x0, #0xa18]
    // 0xb6e95c: stur            x0, [fp, #-8]
    // 0xb6e960: r1 = <Widget>
    //     0xb6e960: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6e964: r2 = 0
    //     0xb6e964: movz            x2, #0
    // 0xb6e968: r0 = _GrowableList()
    //     0xb6e968: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb6e96c: ldur            x2, [fp, #-0x10]
    // 0xb6e970: stur            x0, [fp, #-0x18]
    // 0xb6e974: LoadField: r1 = r2->field_f
    //     0xb6e974: ldur            w1, [x2, #0xf]
    // 0xb6e978: DecompressPointer r1
    //     0xb6e978: add             x1, x1, HEAP, lsl #32
    // 0xb6e97c: LoadField: r3 = r1->field_b
    //     0xb6e97c: ldur            w3, [x1, #0xb]
    // 0xb6e980: DecompressPointer r3
    //     0xb6e980: add             x3, x3, HEAP, lsl #32
    // 0xb6e984: cmp             w3, NULL
    // 0xb6e988: b.eq            #0xb6f164
    // 0xb6e98c: LoadField: r1 = r3->field_f
    //     0xb6e98c: ldur            w1, [x3, #0xf]
    // 0xb6e990: DecompressPointer r1
    //     0xb6e990: add             x1, x1, HEAP, lsl #32
    // 0xb6e994: LoadField: r3 = r1->field_7
    //     0xb6e994: ldur            w3, [x1, #7]
    // 0xb6e998: cbz             w3, #0xb6eb40
    // 0xb6e99c: r0 = capitalizeFirstWord()
    //     0xb6e99c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb6e9a0: ldur            x2, [fp, #-0x10]
    // 0xb6e9a4: stur            x0, [fp, #-0x28]
    // 0xb6e9a8: LoadField: r1 = r2->field_f
    //     0xb6e9a8: ldur            w1, [x2, #0xf]
    // 0xb6e9ac: DecompressPointer r1
    //     0xb6e9ac: add             x1, x1, HEAP, lsl #32
    // 0xb6e9b0: LoadField: r3 = r1->field_b
    //     0xb6e9b0: ldur            w3, [x1, #0xb]
    // 0xb6e9b4: DecompressPointer r3
    //     0xb6e9b4: add             x3, x3, HEAP, lsl #32
    // 0xb6e9b8: cmp             w3, NULL
    // 0xb6e9bc: b.eq            #0xb6f168
    // 0xb6e9c0: LoadField: r1 = r3->field_13
    //     0xb6e9c0: ldur            w1, [x3, #0x13]
    // 0xb6e9c4: DecompressPointer r1
    //     0xb6e9c4: add             x1, x1, HEAP, lsl #32
    // 0xb6e9c8: cmp             w1, NULL
    // 0xb6e9cc: b.ne            #0xb6e9d8
    // 0xb6e9d0: r1 = Null
    //     0xb6e9d0: mov             x1, NULL
    // 0xb6e9d4: b               #0xb6e9e4
    // 0xb6e9d8: LoadField: r3 = r1->field_7
    //     0xb6e9d8: ldur            w3, [x1, #7]
    // 0xb6e9dc: DecompressPointer r3
    //     0xb6e9dc: add             x3, x3, HEAP, lsl #32
    // 0xb6e9e0: mov             x1, x3
    // 0xb6e9e4: cmp             w1, NULL
    // 0xb6e9e8: b.ne            #0xb6e9f4
    // 0xb6e9ec: r1 = Instance_TitleAlignment
    //     0xb6e9ec: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb6e9f0: ldr             x1, [x1, #0x518]
    // 0xb6e9f4: r16 = Instance_TitleAlignment
    //     0xb6e9f4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb6e9f8: ldr             x16, [x16, #0x520]
    // 0xb6e9fc: cmp             w1, w16
    // 0xb6ea00: b.ne            #0xb6ea0c
    // 0xb6ea04: r4 = Instance_TextAlign
    //     0xb6ea04: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb6ea08: b               #0xb6ea28
    // 0xb6ea0c: r16 = Instance_TitleAlignment
    //     0xb6ea0c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb6ea10: ldr             x16, [x16, #0x518]
    // 0xb6ea14: cmp             w1, w16
    // 0xb6ea18: b.ne            #0xb6ea24
    // 0xb6ea1c: r4 = Instance_TextAlign
    //     0xb6ea1c: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb6ea20: b               #0xb6ea28
    // 0xb6ea24: r4 = Instance_TextAlign
    //     0xb6ea24: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb6ea28: ldur            x3, [fp, #-0x18]
    // 0xb6ea2c: stur            x4, [fp, #-0x20]
    // 0xb6ea30: LoadField: r1 = r2->field_13
    //     0xb6ea30: ldur            w1, [x2, #0x13]
    // 0xb6ea34: DecompressPointer r1
    //     0xb6ea34: add             x1, x1, HEAP, lsl #32
    // 0xb6ea38: r0 = of()
    //     0xb6ea38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ea3c: LoadField: r1 = r0->field_87
    //     0xb6ea3c: ldur            w1, [x0, #0x87]
    // 0xb6ea40: DecompressPointer r1
    //     0xb6ea40: add             x1, x1, HEAP, lsl #32
    // 0xb6ea44: LoadField: r0 = r1->field_7
    //     0xb6ea44: ldur            w0, [x1, #7]
    // 0xb6ea48: DecompressPointer r0
    //     0xb6ea48: add             x0, x0, HEAP, lsl #32
    // 0xb6ea4c: stur            x0, [fp, #-0x30]
    // 0xb6ea50: r1 = Instance_Color
    //     0xb6ea50: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6ea54: d0 = 0.700000
    //     0xb6ea54: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6ea58: ldr             d0, [x17, #0xf48]
    // 0xb6ea5c: r0 = withOpacity()
    //     0xb6ea5c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6ea60: r16 = 32.000000
    //     0xb6ea60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb6ea64: ldr             x16, [x16, #0x848]
    // 0xb6ea68: stp             x16, x0, [SP]
    // 0xb6ea6c: ldur            x1, [fp, #-0x30]
    // 0xb6ea70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb6ea70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb6ea74: ldr             x4, [x4, #0x9b8]
    // 0xb6ea78: r0 = copyWith()
    //     0xb6ea78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6ea7c: stur            x0, [fp, #-0x30]
    // 0xb6ea80: r0 = Text()
    //     0xb6ea80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6ea84: mov             x1, x0
    // 0xb6ea88: ldur            x0, [fp, #-0x28]
    // 0xb6ea8c: stur            x1, [fp, #-0x38]
    // 0xb6ea90: StoreField: r1->field_b = r0
    //     0xb6ea90: stur            w0, [x1, #0xb]
    // 0xb6ea94: ldur            x0, [fp, #-0x30]
    // 0xb6ea98: StoreField: r1->field_13 = r0
    //     0xb6ea98: stur            w0, [x1, #0x13]
    // 0xb6ea9c: ldur            x0, [fp, #-0x20]
    // 0xb6eaa0: StoreField: r1->field_1b = r0
    //     0xb6eaa0: stur            w0, [x1, #0x1b]
    // 0xb6eaa4: r0 = Padding()
    //     0xb6eaa4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6eaa8: mov             x2, x0
    // 0xb6eaac: r0 = Instance_EdgeInsets
    //     0xb6eaac: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xb6eab0: ldr             x0, [x0, #0x78]
    // 0xb6eab4: stur            x2, [fp, #-0x20]
    // 0xb6eab8: StoreField: r2->field_f = r0
    //     0xb6eab8: stur            w0, [x2, #0xf]
    // 0xb6eabc: ldur            x0, [fp, #-0x38]
    // 0xb6eac0: StoreField: r2->field_b = r0
    //     0xb6eac0: stur            w0, [x2, #0xb]
    // 0xb6eac4: ldur            x0, [fp, #-0x18]
    // 0xb6eac8: LoadField: r1 = r0->field_b
    //     0xb6eac8: ldur            w1, [x0, #0xb]
    // 0xb6eacc: LoadField: r3 = r0->field_f
    //     0xb6eacc: ldur            w3, [x0, #0xf]
    // 0xb6ead0: DecompressPointer r3
    //     0xb6ead0: add             x3, x3, HEAP, lsl #32
    // 0xb6ead4: LoadField: r4 = r3->field_b
    //     0xb6ead4: ldur            w4, [x3, #0xb]
    // 0xb6ead8: r3 = LoadInt32Instr(r1)
    //     0xb6ead8: sbfx            x3, x1, #1, #0x1f
    // 0xb6eadc: stur            x3, [fp, #-0x40]
    // 0xb6eae0: r1 = LoadInt32Instr(r4)
    //     0xb6eae0: sbfx            x1, x4, #1, #0x1f
    // 0xb6eae4: cmp             x3, x1
    // 0xb6eae8: b.ne            #0xb6eaf4
    // 0xb6eaec: mov             x1, x0
    // 0xb6eaf0: r0 = _growToNextCapacity()
    //     0xb6eaf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6eaf4: ldur            x3, [fp, #-0x18]
    // 0xb6eaf8: ldur            x2, [fp, #-0x40]
    // 0xb6eafc: add             x0, x2, #1
    // 0xb6eb00: lsl             x1, x0, #1
    // 0xb6eb04: StoreField: r3->field_b = r1
    //     0xb6eb04: stur            w1, [x3, #0xb]
    // 0xb6eb08: LoadField: r1 = r3->field_f
    //     0xb6eb08: ldur            w1, [x3, #0xf]
    // 0xb6eb0c: DecompressPointer r1
    //     0xb6eb0c: add             x1, x1, HEAP, lsl #32
    // 0xb6eb10: ldur            x0, [fp, #-0x20]
    // 0xb6eb14: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb6eb14: add             x25, x1, x2, lsl #2
    //     0xb6eb18: add             x25, x25, #0xf
    //     0xb6eb1c: str             w0, [x25]
    //     0xb6eb20: tbz             w0, #0, #0xb6eb3c
    //     0xb6eb24: ldurb           w16, [x1, #-1]
    //     0xb6eb28: ldurb           w17, [x0, #-1]
    //     0xb6eb2c: and             x16, x17, x16, lsr #2
    //     0xb6eb30: tst             x16, HEAP, lsr #32
    //     0xb6eb34: b.eq            #0xb6eb3c
    //     0xb6eb38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6eb3c: b               #0xb6eb44
    // 0xb6eb40: mov             x3, x0
    // 0xb6eb44: ldur            x0, [fp, #-0x10]
    // 0xb6eb48: LoadField: r1 = r0->field_f
    //     0xb6eb48: ldur            w1, [x0, #0xf]
    // 0xb6eb4c: DecompressPointer r1
    //     0xb6eb4c: add             x1, x1, HEAP, lsl #32
    // 0xb6eb50: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xb6eb50: ldur            x2, [x1, #0x17]
    // 0xb6eb54: r0 = _calculateCardHeight()
    //     0xb6eb54: bl              #0xb6f194  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_calculateCardHeight
    // 0xb6eb58: ldur            x0, [fp, #-0x10]
    // 0xb6eb5c: stur            d0, [fp, #-0x58]
    // 0xb6eb60: LoadField: r1 = r0->field_f
    //     0xb6eb60: ldur            w1, [x0, #0xf]
    // 0xb6eb64: DecompressPointer r1
    //     0xb6eb64: add             x1, x1, HEAP, lsl #32
    // 0xb6eb68: LoadField: r2 = r1->field_b
    //     0xb6eb68: ldur            w2, [x1, #0xb]
    // 0xb6eb6c: DecompressPointer r2
    //     0xb6eb6c: add             x2, x2, HEAP, lsl #32
    // 0xb6eb70: cmp             w2, NULL
    // 0xb6eb74: b.eq            #0xb6f16c
    // 0xb6eb78: LoadField: r3 = r2->field_b
    //     0xb6eb78: ldur            w3, [x2, #0xb]
    // 0xb6eb7c: DecompressPointer r3
    //     0xb6eb7c: add             x3, x3, HEAP, lsl #32
    // 0xb6eb80: cmp             w3, NULL
    // 0xb6eb84: b.ne            #0xb6eb90
    // 0xb6eb88: r4 = Null
    //     0xb6eb88: mov             x4, NULL
    // 0xb6eb8c: b               #0xb6eb98
    // 0xb6eb90: LoadField: r2 = r3->field_b
    //     0xb6eb90: ldur            w2, [x3, #0xb]
    // 0xb6eb94: mov             x4, x2
    // 0xb6eb98: ldur            x3, [fp, #-0x18]
    // 0xb6eb9c: stur            x4, [fp, #-0x28]
    // 0xb6eba0: LoadField: r5 = r1->field_13
    //     0xb6eba0: ldur            w5, [x1, #0x13]
    // 0xb6eba4: DecompressPointer r5
    //     0xb6eba4: add             x5, x5, HEAP, lsl #32
    // 0xb6eba8: r16 = Sentinel
    //     0xb6eba8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6ebac: cmp             w5, w16
    // 0xb6ebb0: b.eq            #0xb6f170
    // 0xb6ebb4: mov             x2, x0
    // 0xb6ebb8: stur            x5, [fp, #-0x20]
    // 0xb6ebbc: r1 = Function '<anonymous closure>':.
    //     0xb6ebbc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b30] AnonymousClosure: (0xb710e4), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb6e824)
    //     0xb6ebc0: ldr             x1, [x1, #0xb30]
    // 0xb6ebc4: r0 = AllocateClosure()
    //     0xb6ebc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6ebc8: ldur            x2, [fp, #-0x10]
    // 0xb6ebcc: r1 = Function '<anonymous closure>':.
    //     0xb6ebcc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b38] AnonymousClosure: (0xb6f524), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb6e824)
    //     0xb6ebd0: ldr             x1, [x1, #0xb38]
    // 0xb6ebd4: stur            x0, [fp, #-0x30]
    // 0xb6ebd8: r0 = AllocateClosure()
    //     0xb6ebd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6ebdc: stur            x0, [fp, #-0x38]
    // 0xb6ebe0: r0 = PageView()
    //     0xb6ebe0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb6ebe4: stur            x0, [fp, #-0x48]
    // 0xb6ebe8: r16 = Instance_BouncingScrollPhysics
    //     0xb6ebe8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb6ebec: ldr             x16, [x16, #0x890]
    // 0xb6ebf0: ldur            lr, [fp, #-0x20]
    // 0xb6ebf4: stp             lr, x16, [SP]
    // 0xb6ebf8: mov             x1, x0
    // 0xb6ebfc: ldur            x2, [fp, #-0x38]
    // 0xb6ec00: ldur            x3, [fp, #-0x28]
    // 0xb6ec04: ldur            x5, [fp, #-0x30]
    // 0xb6ec08: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb6ec08: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb6ec0c: ldr             x4, [x4, #0xe40]
    // 0xb6ec10: r0 = PageView.builder()
    //     0xb6ec10: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb6ec14: ldur            d0, [fp, #-0x58]
    // 0xb6ec18: r0 = inline_Allocate_Double()
    //     0xb6ec18: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb6ec1c: add             x0, x0, #0x10
    //     0xb6ec20: cmp             x1, x0
    //     0xb6ec24: b.ls            #0xb6f17c
    //     0xb6ec28: str             x0, [THR, #0x50]  ; THR::top
    //     0xb6ec2c: sub             x0, x0, #0xf
    //     0xb6ec30: movz            x1, #0xe15c
    //     0xb6ec34: movk            x1, #0x3, lsl #16
    //     0xb6ec38: stur            x1, [x0, #-1]
    // 0xb6ec3c: StoreField: r0->field_7 = d0
    //     0xb6ec3c: stur            d0, [x0, #7]
    // 0xb6ec40: stur            x0, [fp, #-0x20]
    // 0xb6ec44: r0 = SizedBox()
    //     0xb6ec44: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6ec48: mov             x2, x0
    // 0xb6ec4c: ldur            x0, [fp, #-0x20]
    // 0xb6ec50: stur            x2, [fp, #-0x28]
    // 0xb6ec54: StoreField: r2->field_13 = r0
    //     0xb6ec54: stur            w0, [x2, #0x13]
    // 0xb6ec58: ldur            x0, [fp, #-0x48]
    // 0xb6ec5c: StoreField: r2->field_b = r0
    //     0xb6ec5c: stur            w0, [x2, #0xb]
    // 0xb6ec60: ldur            x0, [fp, #-0x18]
    // 0xb6ec64: LoadField: r1 = r0->field_b
    //     0xb6ec64: ldur            w1, [x0, #0xb]
    // 0xb6ec68: LoadField: r3 = r0->field_f
    //     0xb6ec68: ldur            w3, [x0, #0xf]
    // 0xb6ec6c: DecompressPointer r3
    //     0xb6ec6c: add             x3, x3, HEAP, lsl #32
    // 0xb6ec70: LoadField: r4 = r3->field_b
    //     0xb6ec70: ldur            w4, [x3, #0xb]
    // 0xb6ec74: r3 = LoadInt32Instr(r1)
    //     0xb6ec74: sbfx            x3, x1, #1, #0x1f
    // 0xb6ec78: stur            x3, [fp, #-0x40]
    // 0xb6ec7c: r1 = LoadInt32Instr(r4)
    //     0xb6ec7c: sbfx            x1, x4, #1, #0x1f
    // 0xb6ec80: cmp             x3, x1
    // 0xb6ec84: b.ne            #0xb6ec90
    // 0xb6ec88: mov             x1, x0
    // 0xb6ec8c: r0 = _growToNextCapacity()
    //     0xb6ec8c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6ec90: ldur            x4, [fp, #-0x10]
    // 0xb6ec94: ldur            x2, [fp, #-0x18]
    // 0xb6ec98: ldur            x3, [fp, #-0x40]
    // 0xb6ec9c: add             x0, x3, #1
    // 0xb6eca0: lsl             x1, x0, #1
    // 0xb6eca4: StoreField: r2->field_b = r1
    //     0xb6eca4: stur            w1, [x2, #0xb]
    // 0xb6eca8: LoadField: r1 = r2->field_f
    //     0xb6eca8: ldur            w1, [x2, #0xf]
    // 0xb6ecac: DecompressPointer r1
    //     0xb6ecac: add             x1, x1, HEAP, lsl #32
    // 0xb6ecb0: ldur            x0, [fp, #-0x28]
    // 0xb6ecb4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6ecb4: add             x25, x1, x3, lsl #2
    //     0xb6ecb8: add             x25, x25, #0xf
    //     0xb6ecbc: str             w0, [x25]
    //     0xb6ecc0: tbz             w0, #0, #0xb6ecdc
    //     0xb6ecc4: ldurb           w16, [x1, #-1]
    //     0xb6ecc8: ldurb           w17, [x0, #-1]
    //     0xb6eccc: and             x16, x17, x16, lsr #2
    //     0xb6ecd0: tst             x16, HEAP, lsr #32
    //     0xb6ecd4: b.eq            #0xb6ecdc
    //     0xb6ecd8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6ecdc: LoadField: r0 = r4->field_f
    //     0xb6ecdc: ldur            w0, [x4, #0xf]
    // 0xb6ece0: DecompressPointer r0
    //     0xb6ece0: add             x0, x0, HEAP, lsl #32
    // 0xb6ece4: LoadField: r1 = r0->field_b
    //     0xb6ece4: ldur            w1, [x0, #0xb]
    // 0xb6ece8: DecompressPointer r1
    //     0xb6ece8: add             x1, x1, HEAP, lsl #32
    // 0xb6ecec: cmp             w1, NULL
    // 0xb6ecf0: b.eq            #0xb6f18c
    // 0xb6ecf4: LoadField: r3 = r1->field_b
    //     0xb6ecf4: ldur            w3, [x1, #0xb]
    // 0xb6ecf8: DecompressPointer r3
    //     0xb6ecf8: add             x3, x3, HEAP, lsl #32
    // 0xb6ecfc: cmp             w3, NULL
    // 0xb6ed00: b.ne            #0xb6ed0c
    // 0xb6ed04: r1 = Null
    //     0xb6ed04: mov             x1, NULL
    // 0xb6ed08: b               #0xb6ed10
    // 0xb6ed0c: LoadField: r1 = r3->field_b
    //     0xb6ed0c: ldur            w1, [x3, #0xb]
    // 0xb6ed10: cmp             w1, NULL
    // 0xb6ed14: b.ne            #0xb6ed20
    // 0xb6ed18: r1 = 0
    //     0xb6ed18: movz            x1, #0
    // 0xb6ed1c: b               #0xb6ed28
    // 0xb6ed20: r5 = LoadInt32Instr(r1)
    //     0xb6ed20: sbfx            x5, x1, #1, #0x1f
    // 0xb6ed24: mov             x1, x5
    // 0xb6ed28: cmp             x1, #1
    // 0xb6ed2c: b.le            #0xb6eebc
    // 0xb6ed30: cmp             w3, NULL
    // 0xb6ed34: b.ne            #0xb6ed40
    // 0xb6ed38: r1 = Null
    //     0xb6ed38: mov             x1, NULL
    // 0xb6ed3c: b               #0xb6ed44
    // 0xb6ed40: LoadField: r1 = r3->field_b
    //     0xb6ed40: ldur            w1, [x3, #0xb]
    // 0xb6ed44: cmp             w1, NULL
    // 0xb6ed48: b.ne            #0xb6ed54
    // 0xb6ed4c: r3 = 0
    //     0xb6ed4c: movz            x3, #0
    // 0xb6ed50: b               #0xb6ed58
    // 0xb6ed54: r3 = LoadInt32Instr(r1)
    //     0xb6ed54: sbfx            x3, x1, #1, #0x1f
    // 0xb6ed58: stur            x3, [fp, #-0x50]
    // 0xb6ed5c: ArrayLoad: r5 = r0[0]  ; List_8
    //     0xb6ed5c: ldur            x5, [x0, #0x17]
    // 0xb6ed60: stur            x5, [fp, #-0x40]
    // 0xb6ed64: LoadField: r1 = r4->field_13
    //     0xb6ed64: ldur            w1, [x4, #0x13]
    // 0xb6ed68: DecompressPointer r1
    //     0xb6ed68: add             x1, x1, HEAP, lsl #32
    // 0xb6ed6c: r0 = of()
    //     0xb6ed6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ed70: LoadField: r1 = r0->field_5b
    //     0xb6ed70: ldur            w1, [x0, #0x5b]
    // 0xb6ed74: DecompressPointer r1
    //     0xb6ed74: add             x1, x1, HEAP, lsl #32
    // 0xb6ed78: stur            x1, [fp, #-0x20]
    // 0xb6ed7c: r0 = CarouselIndicator()
    //     0xb6ed7c: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb6ed80: mov             x3, x0
    // 0xb6ed84: ldur            x0, [fp, #-0x50]
    // 0xb6ed88: stur            x3, [fp, #-0x28]
    // 0xb6ed8c: StoreField: r3->field_b = r0
    //     0xb6ed8c: stur            x0, [x3, #0xb]
    // 0xb6ed90: ldur            x0, [fp, #-0x40]
    // 0xb6ed94: StoreField: r3->field_13 = r0
    //     0xb6ed94: stur            x0, [x3, #0x13]
    // 0xb6ed98: ldur            x0, [fp, #-0x20]
    // 0xb6ed9c: StoreField: r3->field_1b = r0
    //     0xb6ed9c: stur            w0, [x3, #0x1b]
    // 0xb6eda0: r0 = Instance_Color
    //     0xb6eda0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb6eda4: ldr             x0, [x0, #0x90]
    // 0xb6eda8: StoreField: r3->field_1f = r0
    //     0xb6eda8: stur            w0, [x3, #0x1f]
    // 0xb6edac: r1 = Null
    //     0xb6edac: mov             x1, NULL
    // 0xb6edb0: r2 = 2
    //     0xb6edb0: movz            x2, #0x2
    // 0xb6edb4: r0 = AllocateArray()
    //     0xb6edb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6edb8: mov             x2, x0
    // 0xb6edbc: ldur            x0, [fp, #-0x28]
    // 0xb6edc0: stur            x2, [fp, #-0x20]
    // 0xb6edc4: StoreField: r2->field_f = r0
    //     0xb6edc4: stur            w0, [x2, #0xf]
    // 0xb6edc8: r1 = <Widget>
    //     0xb6edc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6edcc: r0 = AllocateGrowableArray()
    //     0xb6edcc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6edd0: mov             x1, x0
    // 0xb6edd4: ldur            x0, [fp, #-0x20]
    // 0xb6edd8: stur            x1, [fp, #-0x28]
    // 0xb6eddc: StoreField: r1->field_f = r0
    //     0xb6eddc: stur            w0, [x1, #0xf]
    // 0xb6ede0: r0 = 2
    //     0xb6ede0: movz            x0, #0x2
    // 0xb6ede4: StoreField: r1->field_b = r0
    //     0xb6ede4: stur            w0, [x1, #0xb]
    // 0xb6ede8: r0 = Row()
    //     0xb6ede8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6edec: mov             x2, x0
    // 0xb6edf0: r0 = Instance_Axis
    //     0xb6edf0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6edf4: stur            x2, [fp, #-0x20]
    // 0xb6edf8: StoreField: r2->field_f = r0
    //     0xb6edf8: stur            w0, [x2, #0xf]
    // 0xb6edfc: r0 = Instance_MainAxisAlignment
    //     0xb6edfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb6ee00: ldr             x0, [x0, #0xab0]
    // 0xb6ee04: StoreField: r2->field_13 = r0
    //     0xb6ee04: stur            w0, [x2, #0x13]
    // 0xb6ee08: r0 = Instance_MainAxisSize
    //     0xb6ee08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6ee0c: ldr             x0, [x0, #0xa10]
    // 0xb6ee10: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6ee10: stur            w0, [x2, #0x17]
    // 0xb6ee14: r1 = Instance_CrossAxisAlignment
    //     0xb6ee14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb6ee18: ldr             x1, [x1, #0xa18]
    // 0xb6ee1c: StoreField: r2->field_1b = r1
    //     0xb6ee1c: stur            w1, [x2, #0x1b]
    // 0xb6ee20: r3 = Instance_VerticalDirection
    //     0xb6ee20: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6ee24: ldr             x3, [x3, #0xa20]
    // 0xb6ee28: StoreField: r2->field_23 = r3
    //     0xb6ee28: stur            w3, [x2, #0x23]
    // 0xb6ee2c: r4 = Instance_Clip
    //     0xb6ee2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6ee30: ldr             x4, [x4, #0x38]
    // 0xb6ee34: StoreField: r2->field_2b = r4
    //     0xb6ee34: stur            w4, [x2, #0x2b]
    // 0xb6ee38: StoreField: r2->field_2f = rZR
    //     0xb6ee38: stur            xzr, [x2, #0x2f]
    // 0xb6ee3c: ldur            x1, [fp, #-0x28]
    // 0xb6ee40: StoreField: r2->field_b = r1
    //     0xb6ee40: stur            w1, [x2, #0xb]
    // 0xb6ee44: ldur            x5, [fp, #-0x18]
    // 0xb6ee48: LoadField: r1 = r5->field_b
    //     0xb6ee48: ldur            w1, [x5, #0xb]
    // 0xb6ee4c: LoadField: r6 = r5->field_f
    //     0xb6ee4c: ldur            w6, [x5, #0xf]
    // 0xb6ee50: DecompressPointer r6
    //     0xb6ee50: add             x6, x6, HEAP, lsl #32
    // 0xb6ee54: LoadField: r7 = r6->field_b
    //     0xb6ee54: ldur            w7, [x6, #0xb]
    // 0xb6ee58: r6 = LoadInt32Instr(r1)
    //     0xb6ee58: sbfx            x6, x1, #1, #0x1f
    // 0xb6ee5c: stur            x6, [fp, #-0x40]
    // 0xb6ee60: r1 = LoadInt32Instr(r7)
    //     0xb6ee60: sbfx            x1, x7, #1, #0x1f
    // 0xb6ee64: cmp             x6, x1
    // 0xb6ee68: b.ne            #0xb6ee74
    // 0xb6ee6c: mov             x1, x5
    // 0xb6ee70: r0 = _growToNextCapacity()
    //     0xb6ee70: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6ee74: ldur            x2, [fp, #-0x18]
    // 0xb6ee78: ldur            x3, [fp, #-0x40]
    // 0xb6ee7c: add             x0, x3, #1
    // 0xb6ee80: lsl             x1, x0, #1
    // 0xb6ee84: StoreField: r2->field_b = r1
    //     0xb6ee84: stur            w1, [x2, #0xb]
    // 0xb6ee88: LoadField: r1 = r2->field_f
    //     0xb6ee88: ldur            w1, [x2, #0xf]
    // 0xb6ee8c: DecompressPointer r1
    //     0xb6ee8c: add             x1, x1, HEAP, lsl #32
    // 0xb6ee90: ldur            x0, [fp, #-0x20]
    // 0xb6ee94: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6ee94: add             x25, x1, x3, lsl #2
    //     0xb6ee98: add             x25, x25, #0xf
    //     0xb6ee9c: str             w0, [x25]
    //     0xb6eea0: tbz             w0, #0, #0xb6eebc
    //     0xb6eea4: ldurb           w16, [x1, #-1]
    //     0xb6eea8: ldurb           w17, [x0, #-1]
    //     0xb6eeac: and             x16, x17, x16, lsr #2
    //     0xb6eeb0: tst             x16, HEAP, lsr #32
    //     0xb6eeb4: b.eq            #0xb6eebc
    //     0xb6eeb8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6eebc: ldur            x0, [fp, #-0x10]
    // 0xb6eec0: LoadField: r1 = r0->field_13
    //     0xb6eec0: ldur            w1, [x0, #0x13]
    // 0xb6eec4: DecompressPointer r1
    //     0xb6eec4: add             x1, x1, HEAP, lsl #32
    // 0xb6eec8: r0 = of()
    //     0xb6eec8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6eecc: LoadField: r1 = r0->field_5b
    //     0xb6eecc: ldur            w1, [x0, #0x5b]
    // 0xb6eed0: DecompressPointer r1
    //     0xb6eed0: add             x1, x1, HEAP, lsl #32
    // 0xb6eed4: stur            x1, [fp, #-0x20]
    // 0xb6eed8: r0 = BoxDecoration()
    //     0xb6eed8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6eedc: mov             x1, x0
    // 0xb6eee0: ldur            x0, [fp, #-0x20]
    // 0xb6eee4: stur            x1, [fp, #-0x28]
    // 0xb6eee8: StoreField: r1->field_7 = r0
    //     0xb6eee8: stur            w0, [x1, #7]
    // 0xb6eeec: r0 = Instance_BorderRadius
    //     0xb6eeec: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb6eef0: ldr             x0, [x0, #0x460]
    // 0xb6eef4: StoreField: r1->field_13 = r0
    //     0xb6eef4: stur            w0, [x1, #0x13]
    // 0xb6eef8: r0 = Instance_BoxShape
    //     0xb6eef8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6eefc: ldr             x0, [x0, #0x80]
    // 0xb6ef00: StoreField: r1->field_23 = r0
    //     0xb6ef00: stur            w0, [x1, #0x23]
    // 0xb6ef04: ldur            x2, [fp, #-0x10]
    // 0xb6ef08: LoadField: r3 = r2->field_f
    //     0xb6ef08: ldur            w3, [x2, #0xf]
    // 0xb6ef0c: DecompressPointer r3
    //     0xb6ef0c: add             x3, x3, HEAP, lsl #32
    // 0xb6ef10: LoadField: r4 = r3->field_b
    //     0xb6ef10: ldur            w4, [x3, #0xb]
    // 0xb6ef14: DecompressPointer r4
    //     0xb6ef14: add             x4, x4, HEAP, lsl #32
    // 0xb6ef18: cmp             w4, NULL
    // 0xb6ef1c: b.eq            #0xb6f190
    // 0xb6ef20: LoadField: r3 = r4->field_2f
    //     0xb6ef20: ldur            w3, [x4, #0x2f]
    // 0xb6ef24: DecompressPointer r3
    //     0xb6ef24: add             x3, x3, HEAP, lsl #32
    // 0xb6ef28: cmp             w3, NULL
    // 0xb6ef2c: b.ne            #0xb6ef38
    // 0xb6ef30: r4 = Null
    //     0xb6ef30: mov             x4, NULL
    // 0xb6ef34: b               #0xb6ef40
    // 0xb6ef38: LoadField: r4 = r3->field_7
    //     0xb6ef38: ldur            w4, [x3, #7]
    // 0xb6ef3c: DecompressPointer r4
    //     0xb6ef3c: add             x4, x4, HEAP, lsl #32
    // 0xb6ef40: ldur            x3, [fp, #-0x18]
    // 0xb6ef44: str             x4, [SP]
    // 0xb6ef48: r0 = _interpolateSingle()
    //     0xb6ef48: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb6ef4c: ldur            x2, [fp, #-0x10]
    // 0xb6ef50: stur            x0, [fp, #-0x20]
    // 0xb6ef54: LoadField: r1 = r2->field_13
    //     0xb6ef54: ldur            w1, [x2, #0x13]
    // 0xb6ef58: DecompressPointer r1
    //     0xb6ef58: add             x1, x1, HEAP, lsl #32
    // 0xb6ef5c: r0 = of()
    //     0xb6ef5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ef60: LoadField: r1 = r0->field_87
    //     0xb6ef60: ldur            w1, [x0, #0x87]
    // 0xb6ef64: DecompressPointer r1
    //     0xb6ef64: add             x1, x1, HEAP, lsl #32
    // 0xb6ef68: LoadField: r0 = r1->field_2b
    //     0xb6ef68: ldur            w0, [x1, #0x2b]
    // 0xb6ef6c: DecompressPointer r0
    //     0xb6ef6c: add             x0, x0, HEAP, lsl #32
    // 0xb6ef70: r16 = 16.000000
    //     0xb6ef70: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6ef74: ldr             x16, [x16, #0x188]
    // 0xb6ef78: r30 = Instance_Color
    //     0xb6ef78: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6ef7c: stp             lr, x16, [SP]
    // 0xb6ef80: mov             x1, x0
    // 0xb6ef84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6ef84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6ef88: ldr             x4, [x4, #0xaa0]
    // 0xb6ef8c: r0 = copyWith()
    //     0xb6ef8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6ef90: stur            x0, [fp, #-0x30]
    // 0xb6ef94: r0 = Text()
    //     0xb6ef94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6ef98: mov             x1, x0
    // 0xb6ef9c: ldur            x0, [fp, #-0x20]
    // 0xb6efa0: stur            x1, [fp, #-0x38]
    // 0xb6efa4: StoreField: r1->field_b = r0
    //     0xb6efa4: stur            w0, [x1, #0xb]
    // 0xb6efa8: ldur            x0, [fp, #-0x30]
    // 0xb6efac: StoreField: r1->field_13 = r0
    //     0xb6efac: stur            w0, [x1, #0x13]
    // 0xb6efb0: r0 = Center()
    //     0xb6efb0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb6efb4: mov             x1, x0
    // 0xb6efb8: r0 = Instance_Alignment
    //     0xb6efb8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb6efbc: ldr             x0, [x0, #0xb10]
    // 0xb6efc0: stur            x1, [fp, #-0x20]
    // 0xb6efc4: StoreField: r1->field_f = r0
    //     0xb6efc4: stur            w0, [x1, #0xf]
    // 0xb6efc8: ldur            x0, [fp, #-0x38]
    // 0xb6efcc: StoreField: r1->field_b = r0
    //     0xb6efcc: stur            w0, [x1, #0xb]
    // 0xb6efd0: r0 = Container()
    //     0xb6efd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6efd4: stur            x0, [fp, #-0x30]
    // 0xb6efd8: r16 = 40.000000
    //     0xb6efd8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb6efdc: ldr             x16, [x16, #8]
    // 0xb6efe0: r30 = 110.000000
    //     0xb6efe0: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb6efe4: ldr             lr, [lr, #0x770]
    // 0xb6efe8: stp             lr, x16, [SP, #0x10]
    // 0xb6efec: ldur            x16, [fp, #-0x28]
    // 0xb6eff0: ldur            lr, [fp, #-0x20]
    // 0xb6eff4: stp             lr, x16, [SP]
    // 0xb6eff8: mov             x1, x0
    // 0xb6effc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb6effc: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb6f000: ldr             x4, [x4, #0x8c0]
    // 0xb6f004: r0 = Container()
    //     0xb6f004: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6f008: r0 = InkWell()
    //     0xb6f008: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb6f00c: mov             x3, x0
    // 0xb6f010: ldur            x0, [fp, #-0x30]
    // 0xb6f014: stur            x3, [fp, #-0x20]
    // 0xb6f018: StoreField: r3->field_b = r0
    //     0xb6f018: stur            w0, [x3, #0xb]
    // 0xb6f01c: ldur            x2, [fp, #-0x10]
    // 0xb6f020: r1 = Function '<anonymous closure>':.
    //     0xb6f020: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b40] AnonymousClosure: (0xb6f46c), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb6e824)
    //     0xb6f024: ldr             x1, [x1, #0xb40]
    // 0xb6f028: r0 = AllocateClosure()
    //     0xb6f028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6f02c: mov             x1, x0
    // 0xb6f030: ldur            x0, [fp, #-0x20]
    // 0xb6f034: StoreField: r0->field_f = r1
    //     0xb6f034: stur            w1, [x0, #0xf]
    // 0xb6f038: r1 = true
    //     0xb6f038: add             x1, NULL, #0x20  ; true
    // 0xb6f03c: StoreField: r0->field_43 = r1
    //     0xb6f03c: stur            w1, [x0, #0x43]
    // 0xb6f040: r2 = Instance_BoxShape
    //     0xb6f040: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6f044: ldr             x2, [x2, #0x80]
    // 0xb6f048: StoreField: r0->field_47 = r2
    //     0xb6f048: stur            w2, [x0, #0x47]
    // 0xb6f04c: StoreField: r0->field_6f = r1
    //     0xb6f04c: stur            w1, [x0, #0x6f]
    // 0xb6f050: r2 = false
    //     0xb6f050: add             x2, NULL, #0x30  ; false
    // 0xb6f054: StoreField: r0->field_73 = r2
    //     0xb6f054: stur            w2, [x0, #0x73]
    // 0xb6f058: StoreField: r0->field_83 = r1
    //     0xb6f058: stur            w1, [x0, #0x83]
    // 0xb6f05c: StoreField: r0->field_7b = r2
    //     0xb6f05c: stur            w2, [x0, #0x7b]
    // 0xb6f060: r0 = Padding()
    //     0xb6f060: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6f064: mov             x2, x0
    // 0xb6f068: r0 = Instance_EdgeInsets
    //     0xb6f068: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb6f06c: ldr             x0, [x0, #0x1f0]
    // 0xb6f070: stur            x2, [fp, #-0x10]
    // 0xb6f074: StoreField: r2->field_f = r0
    //     0xb6f074: stur            w0, [x2, #0xf]
    // 0xb6f078: ldur            x0, [fp, #-0x20]
    // 0xb6f07c: StoreField: r2->field_b = r0
    //     0xb6f07c: stur            w0, [x2, #0xb]
    // 0xb6f080: ldur            x0, [fp, #-0x18]
    // 0xb6f084: LoadField: r1 = r0->field_b
    //     0xb6f084: ldur            w1, [x0, #0xb]
    // 0xb6f088: LoadField: r3 = r0->field_f
    //     0xb6f088: ldur            w3, [x0, #0xf]
    // 0xb6f08c: DecompressPointer r3
    //     0xb6f08c: add             x3, x3, HEAP, lsl #32
    // 0xb6f090: LoadField: r4 = r3->field_b
    //     0xb6f090: ldur            w4, [x3, #0xb]
    // 0xb6f094: r3 = LoadInt32Instr(r1)
    //     0xb6f094: sbfx            x3, x1, #1, #0x1f
    // 0xb6f098: stur            x3, [fp, #-0x40]
    // 0xb6f09c: r1 = LoadInt32Instr(r4)
    //     0xb6f09c: sbfx            x1, x4, #1, #0x1f
    // 0xb6f0a0: cmp             x3, x1
    // 0xb6f0a4: b.ne            #0xb6f0b0
    // 0xb6f0a8: mov             x1, x0
    // 0xb6f0ac: r0 = _growToNextCapacity()
    //     0xb6f0ac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6f0b0: ldur            x2, [fp, #-0x18]
    // 0xb6f0b4: ldur            x4, [fp, #-8]
    // 0xb6f0b8: ldur            x3, [fp, #-0x40]
    // 0xb6f0bc: add             x0, x3, #1
    // 0xb6f0c0: lsl             x1, x0, #1
    // 0xb6f0c4: StoreField: r2->field_b = r1
    //     0xb6f0c4: stur            w1, [x2, #0xb]
    // 0xb6f0c8: LoadField: r1 = r2->field_f
    //     0xb6f0c8: ldur            w1, [x2, #0xf]
    // 0xb6f0cc: DecompressPointer r1
    //     0xb6f0cc: add             x1, x1, HEAP, lsl #32
    // 0xb6f0d0: ldur            x0, [fp, #-0x10]
    // 0xb6f0d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6f0d4: add             x25, x1, x3, lsl #2
    //     0xb6f0d8: add             x25, x25, #0xf
    //     0xb6f0dc: str             w0, [x25]
    //     0xb6f0e0: tbz             w0, #0, #0xb6f0fc
    //     0xb6f0e4: ldurb           w16, [x1, #-1]
    //     0xb6f0e8: ldurb           w17, [x0, #-1]
    //     0xb6f0ec: and             x16, x17, x16, lsr #2
    //     0xb6f0f0: tst             x16, HEAP, lsr #32
    //     0xb6f0f4: b.eq            #0xb6f0fc
    //     0xb6f0f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6f0fc: r0 = Column()
    //     0xb6f0fc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6f100: r1 = Instance_Axis
    //     0xb6f100: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb6f104: StoreField: r0->field_f = r1
    //     0xb6f104: stur            w1, [x0, #0xf]
    // 0xb6f108: r1 = Instance_MainAxisAlignment
    //     0xb6f108: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6f10c: ldr             x1, [x1, #0xa08]
    // 0xb6f110: StoreField: r0->field_13 = r1
    //     0xb6f110: stur            w1, [x0, #0x13]
    // 0xb6f114: r1 = Instance_MainAxisSize
    //     0xb6f114: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6f118: ldr             x1, [x1, #0xa10]
    // 0xb6f11c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb6f11c: stur            w1, [x0, #0x17]
    // 0xb6f120: ldur            x1, [fp, #-8]
    // 0xb6f124: StoreField: r0->field_1b = r1
    //     0xb6f124: stur            w1, [x0, #0x1b]
    // 0xb6f128: r1 = Instance_VerticalDirection
    //     0xb6f128: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6f12c: ldr             x1, [x1, #0xa20]
    // 0xb6f130: StoreField: r0->field_23 = r1
    //     0xb6f130: stur            w1, [x0, #0x23]
    // 0xb6f134: r1 = Instance_Clip
    //     0xb6f134: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6f138: ldr             x1, [x1, #0x38]
    // 0xb6f13c: StoreField: r0->field_2b = r1
    //     0xb6f13c: stur            w1, [x0, #0x2b]
    // 0xb6f140: StoreField: r0->field_2f = rZR
    //     0xb6f140: stur            xzr, [x0, #0x2f]
    // 0xb6f144: ldur            x1, [fp, #-0x18]
    // 0xb6f148: StoreField: r0->field_b = r1
    //     0xb6f148: stur            w1, [x0, #0xb]
    // 0xb6f14c: LeaveFrame
    //     0xb6f14c: mov             SP, fp
    //     0xb6f150: ldp             fp, lr, [SP], #0x10
    // 0xb6f154: ret
    //     0xb6f154: ret             
    // 0xb6f158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6f158: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6f15c: b               #0xb6e8d0
    // 0xb6f160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f160: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6f164: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f164: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6f168: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f168: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6f16c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb6f16c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb6f170: r9 = _pageController
    //     0xb6f170: add             x9, PP, #0x55, lsl #12  ; [pp+0x55b48] Field <_TestimonialCarouselState@1591405907._pageController@1591405907>: late (offset: 0x14)
    //     0xb6f174: ldr             x9, [x9, #0xb48]
    // 0xb6f178: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xb6f178: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xb6f17c: SaveReg d0
    //     0xb6f17c: str             q0, [SP, #-0x10]!
    // 0xb6f180: r0 = AllocateDouble()
    //     0xb6f180: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6f184: RestoreReg d0
    //     0xb6f184: ldr             q0, [SP], #0x10
    // 0xb6f188: b               #0xb6ec3c
    // 0xb6f18c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f18c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6f190: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f190: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateCardHeight(/* No info */) {
    // ** addr: 0xb6f194, size: 0x2d8
    // 0xb6f194: EnterFrame
    //     0xb6f194: stp             fp, lr, [SP, #-0x10]!
    //     0xb6f198: mov             fp, SP
    // 0xb6f19c: AllocStack(0x20)
    //     0xb6f19c: sub             SP, SP, #0x20
    // 0xb6f1a0: SetupParameters(_TestimonialCarouselState this /* r1 => r3 */)
    //     0xb6f1a0: mov             x3, x1
    // 0xb6f1a4: CheckStackOverflow
    //     0xb6f1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6f1a8: cmp             SP, x16
    //     0xb6f1ac: b.ls            #0xb6f428
    // 0xb6f1b0: LoadField: r0 = r3->field_b
    //     0xb6f1b0: ldur            w0, [x3, #0xb]
    // 0xb6f1b4: DecompressPointer r0
    //     0xb6f1b4: add             x0, x0, HEAP, lsl #32
    // 0xb6f1b8: cmp             w0, NULL
    // 0xb6f1bc: b.eq            #0xb6f430
    // 0xb6f1c0: LoadField: r4 = r0->field_b
    //     0xb6f1c0: ldur            w4, [x0, #0xb]
    // 0xb6f1c4: DecompressPointer r4
    //     0xb6f1c4: add             x4, x4, HEAP, lsl #32
    // 0xb6f1c8: cmp             w4, NULL
    // 0xb6f1cc: b.ne            #0xb6f1d8
    // 0xb6f1d0: r0 = Null
    //     0xb6f1d0: mov             x0, NULL
    // 0xb6f1d4: b               #0xb6f1dc
    // 0xb6f1d8: LoadField: r0 = r4->field_b
    //     0xb6f1d8: ldur            w0, [x4, #0xb]
    // 0xb6f1dc: cmp             w0, NULL
    // 0xb6f1e0: b.ne            #0xb6f1ec
    // 0xb6f1e4: r0 = 0
    //     0xb6f1e4: movz            x0, #0
    // 0xb6f1e8: b               #0xb6f1f4
    // 0xb6f1ec: r1 = LoadInt32Instr(r0)
    //     0xb6f1ec: sbfx            x1, x0, #1, #0x1f
    // 0xb6f1f0: mov             x0, x1
    // 0xb6f1f4: cmp             x2, x0
    // 0xb6f1f8: b.lt            #0xb6f20c
    // 0xb6f1fc: d0 = 400.000000
    //     0xb6f1fc: ldr             d0, [PP, #0x5a28]  ; [pp+0x5a28] IMM: double(400) from 0x4079000000000000
    // 0xb6f200: LeaveFrame
    //     0xb6f200: mov             SP, fp
    //     0xb6f204: ldp             fp, lr, [SP], #0x10
    // 0xb6f208: ret
    //     0xb6f208: ret             
    // 0xb6f20c: cmp             w4, NULL
    // 0xb6f210: b.eq            #0xb6f434
    // 0xb6f214: LoadField: r0 = r4->field_b
    //     0xb6f214: ldur            w0, [x4, #0xb]
    // 0xb6f218: r1 = LoadInt32Instr(r0)
    //     0xb6f218: sbfx            x1, x0, #1, #0x1f
    // 0xb6f21c: mov             x0, x1
    // 0xb6f220: mov             x1, x2
    // 0xb6f224: cmp             x1, x0
    // 0xb6f228: b.hs            #0xb6f438
    // 0xb6f22c: LoadField: r5 = r4->field_f
    //     0xb6f22c: ldur            w5, [x4, #0xf]
    // 0xb6f230: DecompressPointer r5
    //     0xb6f230: add             x5, x5, HEAP, lsl #32
    // 0xb6f234: r0 = BoxInt64Instr(r2)
    //     0xb6f234: sbfiz           x0, x2, #1, #0x1f
    //     0xb6f238: cmp             x2, x0, asr #1
    //     0xb6f23c: b.eq            #0xb6f248
    //     0xb6f240: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6f244: stur            x2, [x0, #7]
    // 0xb6f248: ArrayLoad: r4 = r5[r2]  ; Unknown_4
    //     0xb6f248: add             x16, x5, x2, lsl #2
    //     0xb6f24c: ldur            w4, [x16, #0xf]
    // 0xb6f250: DecompressPointer r4
    //     0xb6f250: add             x4, x4, HEAP, lsl #32
    // 0xb6f254: stur            x4, [fp, #-0x18]
    // 0xb6f258: LoadField: r1 = r4->field_ab
    //     0xb6f258: ldur            w1, [x4, #0xab]
    // 0xb6f25c: DecompressPointer r1
    //     0xb6f25c: add             x1, x1, HEAP, lsl #32
    // 0xb6f260: cmp             w1, NULL
    // 0xb6f264: b.ne            #0xb6f270
    // 0xb6f268: r1 = Null
    //     0xb6f268: mov             x1, NULL
    // 0xb6f26c: b               #0xb6f284
    // 0xb6f270: LoadField: r2 = r1->field_b
    //     0xb6f270: ldur            w2, [x1, #0xb]
    // 0xb6f274: cbnz            w2, #0xb6f280
    // 0xb6f278: r1 = false
    //     0xb6f278: add             x1, NULL, #0x30  ; false
    // 0xb6f27c: b               #0xb6f284
    // 0xb6f280: r1 = true
    //     0xb6f280: add             x1, NULL, #0x20  ; true
    // 0xb6f284: cmp             w1, NULL
    // 0xb6f288: b.ne            #0xb6f294
    // 0xb6f28c: r5 = false
    //     0xb6f28c: add             x5, NULL, #0x30  ; false
    // 0xb6f290: b               #0xb6f298
    // 0xb6f294: mov             x5, x1
    // 0xb6f298: stur            x5, [fp, #-0x10]
    // 0xb6f29c: LoadField: r6 = r3->field_1f
    //     0xb6f29c: ldur            w6, [x3, #0x1f]
    // 0xb6f2a0: DecompressPointer r6
    //     0xb6f2a0: add             x6, x6, HEAP, lsl #32
    // 0xb6f2a4: mov             x1, x6
    // 0xb6f2a8: mov             x2, x0
    // 0xb6f2ac: stur            x6, [fp, #-8]
    // 0xb6f2b0: r0 = _getValueOrData()
    //     0xb6f2b0: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb6f2b4: mov             x1, x0
    // 0xb6f2b8: ldur            x0, [fp, #-8]
    // 0xb6f2bc: LoadField: r2 = r0->field_f
    //     0xb6f2bc: ldur            w2, [x0, #0xf]
    // 0xb6f2c0: DecompressPointer r2
    //     0xb6f2c0: add             x2, x2, HEAP, lsl #32
    // 0xb6f2c4: cmp             w2, w1
    // 0xb6f2c8: b.ne            #0xb6f2d0
    // 0xb6f2cc: r1 = Null
    //     0xb6f2cc: mov             x1, NULL
    // 0xb6f2d0: cmp             w1, NULL
    // 0xb6f2d4: b.ne            #0xb6f2e0
    // 0xb6f2d8: r0 = Null
    //     0xb6f2d8: mov             x0, NULL
    // 0xb6f2dc: b               #0xb6f2e4
    // 0xb6f2e0: r0 = value()
    //     0xb6f2e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6f2e4: cmp             w0, NULL
    // 0xb6f2e8: b.ne            #0xb6f2f4
    // 0xb6f2ec: r2 = false
    //     0xb6f2ec: add             x2, NULL, #0x30  ; false
    // 0xb6f2f0: b               #0xb6f2f8
    // 0xb6f2f4: mov             x2, x0
    // 0xb6f2f8: ldur            x0, [fp, #-0x18]
    // 0xb6f2fc: stur            x2, [fp, #-8]
    // 0xb6f300: LoadField: r1 = r0->field_9f
    //     0xb6f300: ldur            w1, [x0, #0x9f]
    // 0xb6f304: DecompressPointer r1
    //     0xb6f304: add             x1, x1, HEAP, lsl #32
    // 0xb6f308: cmp             w1, NULL
    // 0xb6f30c: b.ne            #0xb6f318
    // 0xb6f310: r0 = Null
    //     0xb6f310: mov             x0, NULL
    // 0xb6f314: b               #0xb6f324
    // 0xb6f318: r0 = trim()
    //     0xb6f318: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb6f31c: LoadField: r1 = r0->field_7
    //     0xb6f31c: ldur            w1, [x0, #7]
    // 0xb6f320: mov             x0, x1
    // 0xb6f324: cmp             w0, NULL
    // 0xb6f328: b.ne            #0xb6f334
    // 0xb6f32c: r1 = 0
    //     0xb6f32c: movz            x1, #0
    // 0xb6f330: b               #0xb6f338
    // 0xb6f334: r1 = LoadInt32Instr(r0)
    //     0xb6f334: sbfx            x1, x0, #1, #0x1f
    // 0xb6f338: ldur            x0, [fp, #-0x10]
    // 0xb6f33c: tbnz            w0, #4, #0xb6f34c
    // 0xb6f340: d0 = 436.000000
    //     0xb6f340: add             x17, PP, #0x52, lsl #12  ; [pp+0x52708] IMM: double(436) from 0x407b400000000000
    //     0xb6f344: ldr             d0, [x17, #0x708]
    // 0xb6f348: b               #0xb6f350
    // 0xb6f34c: d0 = 100.000000
    //     0xb6f34c: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb6f350: ldur            x0, [fp, #-8]
    // 0xb6f354: tbnz            w0, #4, #0xb6f378
    // 0xb6f358: d2 = 40.000000
    //     0xb6f358: ldr             d2, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xb6f35c: d1 = 24.000000
    //     0xb6f35c: fmov            d1, #24.00000000
    // 0xb6f360: scvtf           d3, x1
    // 0xb6f364: fdiv            d4, d3, d2
    // 0xb6f368: fmul            d2, d4, d1
    // 0xb6f36c: fadd            d1, d0, d2
    // 0xb6f370: mov             v0.16b, v1.16b
    // 0xb6f374: b               #0xb6f384
    // 0xb6f378: d1 = 60.000000
    //     0xb6f378: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0xb6f37c: fadd            d2, d0, d1
    // 0xb6f380: mov             v0.16b, v2.16b
    // 0xb6f384: stur            d0, [fp, #-0x20]
    // 0xb6f388: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb6f388: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6f38c: ldr             x0, [x0, #0x1c80]
    //     0xb6f390: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb6f394: cmp             w0, w16
    //     0xb6f398: b.ne            #0xb6f3a4
    //     0xb6f39c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb6f3a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb6f3a4: r0 = GetNavigation.size()
    //     0xb6f3a4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb6f3a8: LoadField: d0 = r0->field_f
    //     0xb6f3a8: ldur            d0, [x0, #0xf]
    // 0xb6f3ac: d1 = 0.950000
    //     0xb6f3ac: add             x17, PP, #0x51, lsl #12  ; [pp+0x51e58] IMM: double(0.95) from 0x3fee666666666666
    //     0xb6f3b0: ldr             d1, [x17, #0xe58]
    // 0xb6f3b4: fmul            d2, d0, d1
    // 0xb6f3b8: ldur            d0, [fp, #-0x20]
    // 0xb6f3bc: r1 = inline_Allocate_Double()
    //     0xb6f3bc: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xb6f3c0: add             x1, x1, #0x10
    //     0xb6f3c4: cmp             x0, x1
    //     0xb6f3c8: b.ls            #0xb6f43c
    //     0xb6f3cc: str             x1, [THR, #0x50]  ; THR::top
    //     0xb6f3d0: sub             x1, x1, #0xf
    //     0xb6f3d4: movz            x0, #0xe15c
    //     0xb6f3d8: movk            x0, #0x3, lsl #16
    //     0xb6f3dc: stur            x0, [x1, #-1]
    // 0xb6f3e0: StoreField: r1->field_7 = d0
    //     0xb6f3e0: stur            d0, [x1, #7]
    // 0xb6f3e4: r3 = inline_Allocate_Double()
    //     0xb6f3e4: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xb6f3e8: add             x3, x3, #0x10
    //     0xb6f3ec: cmp             x0, x3
    //     0xb6f3f0: b.ls            #0xb6f450
    //     0xb6f3f4: str             x3, [THR, #0x50]  ; THR::top
    //     0xb6f3f8: sub             x3, x3, #0xf
    //     0xb6f3fc: movz            x0, #0xe15c
    //     0xb6f400: movk            x0, #0x3, lsl #16
    //     0xb6f404: stur            x0, [x3, #-1]
    // 0xb6f408: StoreField: r3->field_7 = d2
    //     0xb6f408: stur            d2, [x3, #7]
    // 0xb6f40c: r2 = 300.000000
    //     0xb6f40c: add             x2, PP, #0x55, lsl #12  ; [pp+0x55bd8] 300
    //     0xb6f410: ldr             x2, [x2, #0xbd8]
    // 0xb6f414: r0 = clamp()
    //     0xb6f414: bl              #0x748b58  ; [dart:core] _Double::clamp
    // 0xb6f418: LoadField: d0 = r0->field_7
    //     0xb6f418: ldur            d0, [x0, #7]
    // 0xb6f41c: LeaveFrame
    //     0xb6f41c: mov             SP, fp
    //     0xb6f420: ldp             fp, lr, [SP], #0x10
    // 0xb6f424: ret
    //     0xb6f424: ret             
    // 0xb6f428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6f428: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6f42c: b               #0xb6f1b0
    // 0xb6f430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f430: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6f434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6f438: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6f438: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6f43c: stp             q0, q2, [SP, #-0x20]!
    // 0xb6f440: r0 = AllocateDouble()
    //     0xb6f440: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6f444: mov             x1, x0
    // 0xb6f448: ldp             q0, q2, [SP], #0x20
    // 0xb6f44c: b               #0xb6f3e0
    // 0xb6f450: SaveReg d2
    //     0xb6f450: str             q2, [SP, #-0x10]!
    // 0xb6f454: SaveReg r1
    //     0xb6f454: str             x1, [SP, #-8]!
    // 0xb6f458: r0 = AllocateDouble()
    //     0xb6f458: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6f45c: mov             x3, x0
    // 0xb6f460: RestoreReg r1
    //     0xb6f460: ldr             x1, [SP], #8
    // 0xb6f464: RestoreReg d2
    //     0xb6f464: ldr             q2, [SP], #0x10
    // 0xb6f468: b               #0xb6f408
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6f46c, size: 0xb8
    // 0xb6f46c: EnterFrame
    //     0xb6f46c: stp             fp, lr, [SP, #-0x10]!
    //     0xb6f470: mov             fp, SP
    // 0xb6f474: AllocStack(0x38)
    //     0xb6f474: sub             SP, SP, #0x38
    // 0xb6f478: SetupParameters()
    //     0xb6f478: ldr             x0, [fp, #0x10]
    //     0xb6f47c: ldur            w1, [x0, #0x17]
    //     0xb6f480: add             x1, x1, HEAP, lsl #32
    // 0xb6f484: CheckStackOverflow
    //     0xb6f484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6f488: cmp             SP, x16
    //     0xb6f48c: b.ls            #0xb6f518
    // 0xb6f490: LoadField: r0 = r1->field_f
    //     0xb6f490: ldur            w0, [x1, #0xf]
    // 0xb6f494: DecompressPointer r0
    //     0xb6f494: add             x0, x0, HEAP, lsl #32
    // 0xb6f498: LoadField: r1 = r0->field_b
    //     0xb6f498: ldur            w1, [x0, #0xb]
    // 0xb6f49c: DecompressPointer r1
    //     0xb6f49c: add             x1, x1, HEAP, lsl #32
    // 0xb6f4a0: cmp             w1, NULL
    // 0xb6f4a4: b.eq            #0xb6f520
    // 0xb6f4a8: LoadField: r0 = r1->field_1b
    //     0xb6f4a8: ldur            w0, [x1, #0x1b]
    // 0xb6f4ac: DecompressPointer r0
    //     0xb6f4ac: add             x0, x0, HEAP, lsl #32
    // 0xb6f4b0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb6f4b0: ldur            w2, [x1, #0x17]
    // 0xb6f4b4: DecompressPointer r2
    //     0xb6f4b4: add             x2, x2, HEAP, lsl #32
    // 0xb6f4b8: LoadField: r3 = r1->field_f
    //     0xb6f4b8: ldur            w3, [x1, #0xf]
    // 0xb6f4bc: DecompressPointer r3
    //     0xb6f4bc: add             x3, x3, HEAP, lsl #32
    // 0xb6f4c0: LoadField: r4 = r1->field_23
    //     0xb6f4c0: ldur            w4, [x1, #0x23]
    // 0xb6f4c4: DecompressPointer r4
    //     0xb6f4c4: add             x4, x4, HEAP, lsl #32
    // 0xb6f4c8: LoadField: r5 = r1->field_1f
    //     0xb6f4c8: ldur            w5, [x1, #0x1f]
    // 0xb6f4cc: DecompressPointer r5
    //     0xb6f4cc: add             x5, x5, HEAP, lsl #32
    // 0xb6f4d0: LoadField: r6 = r1->field_2b
    //     0xb6f4d0: ldur            w6, [x1, #0x2b]
    // 0xb6f4d4: DecompressPointer r6
    //     0xb6f4d4: add             x6, x6, HEAP, lsl #32
    // 0xb6f4d8: LoadField: r7 = r1->field_27
    //     0xb6f4d8: ldur            w7, [x1, #0x27]
    // 0xb6f4dc: DecompressPointer r7
    //     0xb6f4dc: add             x7, x7, HEAP, lsl #32
    // 0xb6f4e0: stp             x0, x7, [SP, #0x28]
    // 0xb6f4e4: stp             x3, x2, [SP, #0x18]
    // 0xb6f4e8: stp             x5, x4, [SP, #8]
    // 0xb6f4ec: str             x6, [SP]
    // 0xb6f4f0: r4 = 0
    //     0xb6f4f0: movz            x4, #0
    // 0xb6f4f4: ldr             x0, [SP, #0x30]
    // 0xb6f4f8: r16 = UnlinkedCall_0x613b5c
    //     0xb6f4f8: add             x16, PP, #0x55, lsl #12  ; [pp+0x55b50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb6f4fc: add             x16, x16, #0xb50
    // 0xb6f500: ldp             x5, lr, [x16]
    // 0xb6f504: blr             lr
    // 0xb6f508: r0 = Null
    //     0xb6f508: mov             x0, NULL
    // 0xb6f50c: LeaveFrame
    //     0xb6f50c: mov             SP, fp
    //     0xb6f510: ldp             fp, lr, [SP], #0x10
    // 0xb6f514: ret
    //     0xb6f514: ret             
    // 0xb6f518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6f518: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6f51c: b               #0xb6f490
    // 0xb6f520: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f520: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb6f524, size: 0x94
    // 0xb6f524: EnterFrame
    //     0xb6f524: stp             fp, lr, [SP, #-0x10]!
    //     0xb6f528: mov             fp, SP
    // 0xb6f52c: AllocStack(0x8)
    //     0xb6f52c: sub             SP, SP, #8
    // 0xb6f530: SetupParameters()
    //     0xb6f530: ldr             x0, [fp, #0x20]
    //     0xb6f534: ldur            w1, [x0, #0x17]
    //     0xb6f538: add             x1, x1, HEAP, lsl #32
    // 0xb6f53c: CheckStackOverflow
    //     0xb6f53c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6f540: cmp             SP, x16
    //     0xb6f544: b.ls            #0xb6f5ac
    // 0xb6f548: LoadField: r0 = r1->field_f
    //     0xb6f548: ldur            w0, [x1, #0xf]
    // 0xb6f54c: DecompressPointer r0
    //     0xb6f54c: add             x0, x0, HEAP, lsl #32
    // 0xb6f550: stur            x0, [fp, #-8]
    // 0xb6f554: LoadField: r1 = r0->field_b
    //     0xb6f554: ldur            w1, [x0, #0xb]
    // 0xb6f558: DecompressPointer r1
    //     0xb6f558: add             x1, x1, HEAP, lsl #32
    // 0xb6f55c: cmp             w1, NULL
    // 0xb6f560: b.eq            #0xb6f5b4
    // 0xb6f564: LoadField: r2 = r1->field_b
    //     0xb6f564: ldur            w2, [x1, #0xb]
    // 0xb6f568: DecompressPointer r2
    //     0xb6f568: add             x2, x2, HEAP, lsl #32
    // 0xb6f56c: cmp             w2, NULL
    // 0xb6f570: b.ne            #0xb6f588
    // 0xb6f574: r1 = <Entity>
    //     0xb6f574: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xb6f578: ldr             x1, [x1, #0xb68]
    // 0xb6f57c: r2 = 0
    //     0xb6f57c: movz            x2, #0
    // 0xb6f580: r0 = AllocateArray()
    //     0xb6f580: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6f584: mov             x2, x0
    // 0xb6f588: ldr             x0, [fp, #0x10]
    // 0xb6f58c: r3 = LoadInt32Instr(r0)
    //     0xb6f58c: sbfx            x3, x0, #1, #0x1f
    //     0xb6f590: tbz             w0, #0, #0xb6f598
    //     0xb6f594: ldur            x3, [x0, #7]
    // 0xb6f598: ldur            x1, [fp, #-8]
    // 0xb6f59c: r0 = _testimonialWidget()
    //     0xb6f59c: bl              #0xb6f5b8  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget
    // 0xb6f5a0: LeaveFrame
    //     0xb6f5a0: mov             SP, fp
    //     0xb6f5a4: ldp             fp, lr, [SP], #0x10
    // 0xb6f5a8: ret
    //     0xb6f5a8: ret             
    // 0xb6f5ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6f5ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6f5b0: b               #0xb6f548
    // 0xb6f5b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6f5b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialWidget(/* No info */) {
    // ** addr: 0xb6f5b8, size: 0xfac
    // 0xb6f5b8: EnterFrame
    //     0xb6f5b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb6f5bc: mov             fp, SP
    // 0xb6f5c0: AllocStack(0xa8)
    //     0xb6f5c0: sub             SP, SP, #0xa8
    // 0xb6f5c4: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb6f5c4: stur            x1, [fp, #-8]
    //     0xb6f5c8: stur            x2, [fp, #-0x10]
    //     0xb6f5cc: stur            x3, [fp, #-0x18]
    // 0xb6f5d0: CheckStackOverflow
    //     0xb6f5d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6f5d4: cmp             SP, x16
    //     0xb6f5d8: b.ls            #0xb70528
    // 0xb6f5dc: r1 = 2
    //     0xb6f5dc: movz            x1, #0x2
    // 0xb6f5e0: r0 = AllocateContext()
    //     0xb6f5e0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6f5e4: mov             x3, x0
    // 0xb6f5e8: ldur            x2, [fp, #-8]
    // 0xb6f5ec: stur            x3, [fp, #-0x28]
    // 0xb6f5f0: StoreField: r3->field_f = r2
    //     0xb6f5f0: stur            w2, [x3, #0xf]
    // 0xb6f5f4: ldur            x4, [fp, #-0x18]
    // 0xb6f5f8: r0 = BoxInt64Instr(r4)
    //     0xb6f5f8: sbfiz           x0, x4, #1, #0x1f
    //     0xb6f5fc: cmp             x4, x0, asr #1
    //     0xb6f600: b.eq            #0xb6f60c
    //     0xb6f604: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6f608: stur            x4, [x0, #7]
    // 0xb6f60c: mov             x1, x0
    // 0xb6f610: ldur            x0, [fp, #-0x10]
    // 0xb6f614: stur            x1, [fp, #-0x20]
    // 0xb6f618: r4 = LoadClassIdInstr(r0)
    //     0xb6f618: ldur            x4, [x0, #-1]
    //     0xb6f61c: ubfx            x4, x4, #0xc, #0x14
    // 0xb6f620: stp             x1, x0, [SP]
    // 0xb6f624: mov             x0, x4
    // 0xb6f628: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb6f628: sub             lr, x0, #0xb7
    //     0xb6f62c: ldr             lr, [x21, lr, lsl #3]
    //     0xb6f630: blr             lr
    // 0xb6f634: stur            x0, [fp, #-0x38]
    // 0xb6f638: LoadField: r1 = r0->field_ab
    //     0xb6f638: ldur            w1, [x0, #0xab]
    // 0xb6f63c: DecompressPointer r1
    //     0xb6f63c: add             x1, x1, HEAP, lsl #32
    // 0xb6f640: cmp             w1, NULL
    // 0xb6f644: b.ne            #0xb6f650
    // 0xb6f648: r1 = Null
    //     0xb6f648: mov             x1, NULL
    // 0xb6f64c: b               #0xb6f664
    // 0xb6f650: LoadField: r2 = r1->field_b
    //     0xb6f650: ldur            w2, [x1, #0xb]
    // 0xb6f654: cbnz            w2, #0xb6f660
    // 0xb6f658: r1 = false
    //     0xb6f658: add             x1, NULL, #0x30  ; false
    // 0xb6f65c: b               #0xb6f664
    // 0xb6f660: r1 = true
    //     0xb6f660: add             x1, NULL, #0x20  ; true
    // 0xb6f664: cmp             w1, NULL
    // 0xb6f668: b.ne            #0xb6f674
    // 0xb6f66c: r4 = false
    //     0xb6f66c: add             x4, NULL, #0x30  ; false
    // 0xb6f670: b               #0xb6f678
    // 0xb6f674: mov             x4, x1
    // 0xb6f678: ldur            x3, [fp, #-8]
    // 0xb6f67c: stur            x4, [fp, #-0x30]
    // 0xb6f680: LoadField: r5 = r3->field_1f
    //     0xb6f680: ldur            w5, [x3, #0x1f]
    // 0xb6f684: DecompressPointer r5
    //     0xb6f684: add             x5, x5, HEAP, lsl #32
    // 0xb6f688: mov             x1, x5
    // 0xb6f68c: ldur            x2, [fp, #-0x20]
    // 0xb6f690: stur            x5, [fp, #-0x10]
    // 0xb6f694: r0 = _getValueOrData()
    //     0xb6f694: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb6f698: mov             x1, x0
    // 0xb6f69c: ldur            x0, [fp, #-0x10]
    // 0xb6f6a0: LoadField: r2 = r0->field_f
    //     0xb6f6a0: ldur            w2, [x0, #0xf]
    // 0xb6f6a4: DecompressPointer r2
    //     0xb6f6a4: add             x2, x2, HEAP, lsl #32
    // 0xb6f6a8: cmp             w2, w1
    // 0xb6f6ac: b.ne            #0xb6f6b8
    // 0xb6f6b0: r0 = Null
    //     0xb6f6b0: mov             x0, NULL
    // 0xb6f6b4: b               #0xb6f6bc
    // 0xb6f6b8: mov             x0, x1
    // 0xb6f6bc: cmp             w0, NULL
    // 0xb6f6c0: b.ne            #0xb6f700
    // 0xb6f6c4: r1 = <bool>
    //     0xb6f6c4: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xb6f6c8: r0 = RxBool()
    //     0xb6f6c8: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xb6f6cc: mov             x2, x0
    // 0xb6f6d0: r0 = Sentinel
    //     0xb6f6d0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6f6d4: stur            x2, [fp, #-0x10]
    // 0xb6f6d8: StoreField: r2->field_13 = r0
    //     0xb6f6d8: stur            w0, [x2, #0x13]
    // 0xb6f6dc: r0 = true
    //     0xb6f6dc: add             x0, NULL, #0x20  ; true
    // 0xb6f6e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6f6e0: stur            w0, [x2, #0x17]
    // 0xb6f6e4: mov             x1, x2
    // 0xb6f6e8: r0 = RxNotifier()
    //     0xb6f6e8: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xb6f6ec: ldur            x0, [fp, #-0x10]
    // 0xb6f6f0: r2 = false
    //     0xb6f6f0: add             x2, NULL, #0x30  ; false
    // 0xb6f6f4: StoreField: r0->field_13 = r2
    //     0xb6f6f4: stur            w2, [x0, #0x13]
    // 0xb6f6f8: mov             x6, x0
    // 0xb6f6fc: b               #0xb6f708
    // 0xb6f700: r2 = false
    //     0xb6f700: add             x2, NULL, #0x30  ; false
    // 0xb6f704: mov             x6, x0
    // 0xb6f708: ldur            x4, [fp, #-8]
    // 0xb6f70c: ldur            x5, [fp, #-0x28]
    // 0xb6f710: ldur            x3, [fp, #-0x38]
    // 0xb6f714: mov             x0, x6
    // 0xb6f718: stur            x6, [fp, #-0x10]
    // 0xb6f71c: StoreField: r5->field_13 = r0
    //     0xb6f71c: stur            w0, [x5, #0x13]
    //     0xb6f720: ldurb           w16, [x5, #-1]
    //     0xb6f724: ldurb           w17, [x0, #-1]
    //     0xb6f728: and             x16, x17, x16, lsr #2
    //     0xb6f72c: tst             x16, HEAP, lsr #32
    //     0xb6f730: b.eq            #0xb6f738
    //     0xb6f734: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0xb6f738: LoadField: r1 = r4->field_f
    //     0xb6f738: ldur            w1, [x4, #0xf]
    // 0xb6f73c: DecompressPointer r1
    //     0xb6f73c: add             x1, x1, HEAP, lsl #32
    // 0xb6f740: cmp             w1, NULL
    // 0xb6f744: b.eq            #0xb70530
    // 0xb6f748: r0 = of()
    //     0xb6f748: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6f74c: LoadField: r1 = r0->field_5b
    //     0xb6f74c: ldur            w1, [x0, #0x5b]
    // 0xb6f750: DecompressPointer r1
    //     0xb6f750: add             x1, x1, HEAP, lsl #32
    // 0xb6f754: r0 = LoadClassIdInstr(r1)
    //     0xb6f754: ldur            x0, [x1, #-1]
    //     0xb6f758: ubfx            x0, x0, #0xc, #0x14
    // 0xb6f75c: d0 = 0.030000
    //     0xb6f75c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb6f760: ldr             d0, [x17, #0x238]
    // 0xb6f764: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb6f764: sub             lr, x0, #0xffa
    //     0xb6f768: ldr             lr, [x21, lr, lsl #3]
    //     0xb6f76c: blr             lr
    // 0xb6f770: stur            x0, [fp, #-0x20]
    // 0xb6f774: r0 = Radius()
    //     0xb6f774: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6f778: d0 = 12.000000
    //     0xb6f778: fmov            d0, #12.00000000
    // 0xb6f77c: stur            x0, [fp, #-0x40]
    // 0xb6f780: StoreField: r0->field_7 = d0
    //     0xb6f780: stur            d0, [x0, #7]
    // 0xb6f784: StoreField: r0->field_f = d0
    //     0xb6f784: stur            d0, [x0, #0xf]
    // 0xb6f788: r0 = BorderRadius()
    //     0xb6f788: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6f78c: mov             x1, x0
    // 0xb6f790: ldur            x0, [fp, #-0x40]
    // 0xb6f794: stur            x1, [fp, #-0x48]
    // 0xb6f798: StoreField: r1->field_7 = r0
    //     0xb6f798: stur            w0, [x1, #7]
    // 0xb6f79c: StoreField: r1->field_b = r0
    //     0xb6f79c: stur            w0, [x1, #0xb]
    // 0xb6f7a0: StoreField: r1->field_f = r0
    //     0xb6f7a0: stur            w0, [x1, #0xf]
    // 0xb6f7a4: StoreField: r1->field_13 = r0
    //     0xb6f7a4: stur            w0, [x1, #0x13]
    // 0xb6f7a8: r0 = RoundedRectangleBorder()
    //     0xb6f7a8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb6f7ac: mov             x2, x0
    // 0xb6f7b0: ldur            x0, [fp, #-0x48]
    // 0xb6f7b4: stur            x2, [fp, #-0x40]
    // 0xb6f7b8: StoreField: r2->field_b = r0
    //     0xb6f7b8: stur            w0, [x2, #0xb]
    // 0xb6f7bc: r0 = Instance_BorderSide
    //     0xb6f7bc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb6f7c0: ldr             x0, [x0, #0xe20]
    // 0xb6f7c4: StoreField: r2->field_7 = r0
    //     0xb6f7c4: stur            w0, [x2, #7]
    // 0xb6f7c8: ldur            x0, [fp, #-0x38]
    // 0xb6f7cc: LoadField: r1 = r0->field_97
    //     0xb6f7cc: ldur            w1, [x0, #0x97]
    // 0xb6f7d0: DecompressPointer r1
    //     0xb6f7d0: add             x1, x1, HEAP, lsl #32
    // 0xb6f7d4: cmp             w1, NULL
    // 0xb6f7d8: b.ne            #0xb6f7e0
    // 0xb6f7dc: r1 = ""
    //     0xb6f7dc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6f7e0: ldur            x3, [fp, #-8]
    // 0xb6f7e4: r0 = capitalizeFirstWord()
    //     0xb6f7e4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb6f7e8: mov             x2, x0
    // 0xb6f7ec: ldur            x0, [fp, #-8]
    // 0xb6f7f0: stur            x2, [fp, #-0x48]
    // 0xb6f7f4: LoadField: r1 = r0->field_f
    //     0xb6f7f4: ldur            w1, [x0, #0xf]
    // 0xb6f7f8: DecompressPointer r1
    //     0xb6f7f8: add             x1, x1, HEAP, lsl #32
    // 0xb6f7fc: cmp             w1, NULL
    // 0xb6f800: b.eq            #0xb70534
    // 0xb6f804: r0 = of()
    //     0xb6f804: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6f808: LoadField: r1 = r0->field_87
    //     0xb6f808: ldur            w1, [x0, #0x87]
    // 0xb6f80c: DecompressPointer r1
    //     0xb6f80c: add             x1, x1, HEAP, lsl #32
    // 0xb6f810: LoadField: r0 = r1->field_7
    //     0xb6f810: ldur            w0, [x1, #7]
    // 0xb6f814: DecompressPointer r0
    //     0xb6f814: add             x0, x0, HEAP, lsl #32
    // 0xb6f818: r16 = 21.000000
    //     0xb6f818: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb6f81c: ldr             x16, [x16, #0x9b0]
    // 0xb6f820: r30 = Instance_Color
    //     0xb6f820: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6f824: stp             lr, x16, [SP]
    // 0xb6f828: mov             x1, x0
    // 0xb6f82c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6f82c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6f830: ldr             x4, [x4, #0xaa0]
    // 0xb6f834: r0 = copyWith()
    //     0xb6f834: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6f838: stur            x0, [fp, #-0x50]
    // 0xb6f83c: r0 = Text()
    //     0xb6f83c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6f840: mov             x3, x0
    // 0xb6f844: ldur            x0, [fp, #-0x48]
    // 0xb6f848: stur            x3, [fp, #-0x58]
    // 0xb6f84c: StoreField: r3->field_b = r0
    //     0xb6f84c: stur            w0, [x3, #0xb]
    // 0xb6f850: ldur            x0, [fp, #-0x50]
    // 0xb6f854: StoreField: r3->field_13 = r0
    //     0xb6f854: stur            w0, [x3, #0x13]
    // 0xb6f858: r1 = <Widget>
    //     0xb6f858: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6f85c: r2 = 18
    //     0xb6f85c: movz            x2, #0x12
    // 0xb6f860: r0 = AllocateArray()
    //     0xb6f860: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6f864: stur            x0, [fp, #-0x48]
    // 0xb6f868: r16 = Instance_Icon
    //     0xb6f868: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xb6f86c: ldr             x16, [x16, #0xd0]
    // 0xb6f870: StoreField: r0->field_f = r16
    //     0xb6f870: stur            w16, [x0, #0xf]
    // 0xb6f874: r16 = Instance_SizedBox
    //     0xb6f874: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb6f878: ldr             x16, [x16, #0xe98]
    // 0xb6f87c: StoreField: r0->field_13 = r16
    //     0xb6f87c: stur            w16, [x0, #0x13]
    // 0xb6f880: ldur            x2, [fp, #-8]
    // 0xb6f884: LoadField: r1 = r2->field_f
    //     0xb6f884: ldur            w1, [x2, #0xf]
    // 0xb6f888: DecompressPointer r1
    //     0xb6f888: add             x1, x1, HEAP, lsl #32
    // 0xb6f88c: cmp             w1, NULL
    // 0xb6f890: b.eq            #0xb70538
    // 0xb6f894: r0 = of()
    //     0xb6f894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6f898: LoadField: r1 = r0->field_87
    //     0xb6f898: ldur            w1, [x0, #0x87]
    // 0xb6f89c: DecompressPointer r1
    //     0xb6f89c: add             x1, x1, HEAP, lsl #32
    // 0xb6f8a0: LoadField: r0 = r1->field_2b
    //     0xb6f8a0: ldur            w0, [x1, #0x2b]
    // 0xb6f8a4: DecompressPointer r0
    //     0xb6f8a4: add             x0, x0, HEAP, lsl #32
    // 0xb6f8a8: stur            x0, [fp, #-0x50]
    // 0xb6f8ac: r1 = Instance_Color
    //     0xb6f8ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6f8b0: d0 = 0.700000
    //     0xb6f8b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6f8b4: ldr             d0, [x17, #0xf48]
    // 0xb6f8b8: r0 = withOpacity()
    //     0xb6f8b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6f8bc: r16 = 14.000000
    //     0xb6f8bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb6f8c0: ldr             x16, [x16, #0x1d8]
    // 0xb6f8c4: stp             x0, x16, [SP]
    // 0xb6f8c8: ldur            x1, [fp, #-0x50]
    // 0xb6f8cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6f8cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6f8d0: ldr             x4, [x4, #0xaa0]
    // 0xb6f8d4: r0 = copyWith()
    //     0xb6f8d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6f8d8: stur            x0, [fp, #-0x50]
    // 0xb6f8dc: r0 = Text()
    //     0xb6f8dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6f8e0: mov             x1, x0
    // 0xb6f8e4: r0 = "Verified Buyer"
    //     0xb6f8e4: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xb6f8e8: ldr             x0, [x0, #0xd8]
    // 0xb6f8ec: StoreField: r1->field_b = r0
    //     0xb6f8ec: stur            w0, [x1, #0xb]
    // 0xb6f8f0: ldur            x0, [fp, #-0x50]
    // 0xb6f8f4: StoreField: r1->field_13 = r0
    //     0xb6f8f4: stur            w0, [x1, #0x13]
    // 0xb6f8f8: mov             x0, x1
    // 0xb6f8fc: ldur            x1, [fp, #-0x48]
    // 0xb6f900: ArrayStore: r1[2] = r0  ; List_4
    //     0xb6f900: add             x25, x1, #0x17
    //     0xb6f904: str             w0, [x25]
    //     0xb6f908: tbz             w0, #0, #0xb6f924
    //     0xb6f90c: ldurb           w16, [x1, #-1]
    //     0xb6f910: ldurb           w17, [x0, #-1]
    //     0xb6f914: and             x16, x17, x16, lsr #2
    //     0xb6f918: tst             x16, HEAP, lsr #32
    //     0xb6f91c: b.eq            #0xb6f924
    //     0xb6f920: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6f924: r0 = Container()
    //     0xb6f924: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6f928: stur            x0, [fp, #-0x50]
    // 0xb6f92c: r16 = 5.000000
    //     0xb6f92c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb6f930: ldr             x16, [x16, #0xcf0]
    // 0xb6f934: str             x16, [SP]
    // 0xb6f938: mov             x1, x0
    // 0xb6f93c: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xb6f93c: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xb6f940: ldr             x4, [x4, #0xe0]
    // 0xb6f944: r0 = Container()
    //     0xb6f944: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6f948: ldur            x1, [fp, #-0x48]
    // 0xb6f94c: ldur            x0, [fp, #-0x50]
    // 0xb6f950: ArrayStore: r1[3] = r0  ; List_4
    //     0xb6f950: add             x25, x1, #0x1b
    //     0xb6f954: str             w0, [x25]
    //     0xb6f958: tbz             w0, #0, #0xb6f974
    //     0xb6f95c: ldurb           w16, [x1, #-1]
    //     0xb6f960: ldurb           w17, [x0, #-1]
    //     0xb6f964: and             x16, x17, x16, lsr #2
    //     0xb6f968: tst             x16, HEAP, lsr #32
    //     0xb6f96c: b.eq            #0xb6f974
    //     0xb6f970: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6f974: ldur            x2, [fp, #-0x38]
    // 0xb6f978: LoadField: r0 = r2->field_a3
    //     0xb6f978: ldur            w0, [x2, #0xa3]
    // 0xb6f97c: DecompressPointer r0
    //     0xb6f97c: add             x0, x0, HEAP, lsl #32
    // 0xb6f980: cmp             w0, NULL
    // 0xb6f984: b.eq            #0xb6f9a4
    // 0xb6f988: LoadField: r1 = r0->field_7
    //     0xb6f988: ldur            w1, [x0, #7]
    // 0xb6f98c: cbnz            w1, #0xb6f998
    // 0xb6f990: r0 = false
    //     0xb6f990: add             x0, NULL, #0x30  ; false
    // 0xb6f994: b               #0xb6f99c
    // 0xb6f998: r0 = true
    //     0xb6f998: add             x0, NULL, #0x20  ; true
    // 0xb6f99c: mov             x3, x0
    // 0xb6f9a0: b               #0xb6f9a8
    // 0xb6f9a4: r3 = false
    //     0xb6f9a4: add             x3, NULL, #0x30  ; false
    // 0xb6f9a8: ldur            x0, [fp, #-8]
    // 0xb6f9ac: stur            x3, [fp, #-0x50]
    // 0xb6f9b0: LoadField: r1 = r0->field_f
    //     0xb6f9b0: ldur            w1, [x0, #0xf]
    // 0xb6f9b4: DecompressPointer r1
    //     0xb6f9b4: add             x1, x1, HEAP, lsl #32
    // 0xb6f9b8: cmp             w1, NULL
    // 0xb6f9bc: b.eq            #0xb7053c
    // 0xb6f9c0: r0 = of()
    //     0xb6f9c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6f9c4: LoadField: r1 = r0->field_5b
    //     0xb6f9c4: ldur            w1, [x0, #0x5b]
    // 0xb6f9c8: DecompressPointer r1
    //     0xb6f9c8: add             x1, x1, HEAP, lsl #32
    // 0xb6f9cc: stur            x1, [fp, #-0x60]
    // 0xb6f9d0: r0 = BoxDecoration()
    //     0xb6f9d0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6f9d4: mov             x1, x0
    // 0xb6f9d8: ldur            x0, [fp, #-0x60]
    // 0xb6f9dc: stur            x1, [fp, #-0x68]
    // 0xb6f9e0: StoreField: r1->field_7 = r0
    //     0xb6f9e0: stur            w0, [x1, #7]
    // 0xb6f9e4: r0 = Instance_BoxShape
    //     0xb6f9e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb6f9e8: ldr             x0, [x0, #0x970]
    // 0xb6f9ec: StoreField: r1->field_23 = r0
    //     0xb6f9ec: stur            w0, [x1, #0x23]
    // 0xb6f9f0: r0 = Container()
    //     0xb6f9f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6f9f4: stur            x0, [fp, #-0x60]
    // 0xb6f9f8: r16 = Instance_EdgeInsets
    //     0xb6f9f8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xb6f9fc: ldr             x16, [x16, #0x108]
    // 0xb6fa00: r30 = 5.000000
    //     0xb6fa00: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb6fa04: ldr             lr, [lr, #0xcf0]
    // 0xb6fa08: stp             lr, x16, [SP, #0x10]
    // 0xb6fa0c: r16 = 5.000000
    //     0xb6fa0c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb6fa10: ldr             x16, [x16, #0xcf0]
    // 0xb6fa14: ldur            lr, [fp, #-0x68]
    // 0xb6fa18: stp             lr, x16, [SP]
    // 0xb6fa1c: mov             x1, x0
    // 0xb6fa20: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb6fa20: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb6fa24: ldr             x4, [x4, #0x118]
    // 0xb6fa28: r0 = Container()
    //     0xb6fa28: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6fa2c: r0 = Visibility()
    //     0xb6fa2c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb6fa30: mov             x1, x0
    // 0xb6fa34: ldur            x0, [fp, #-0x60]
    // 0xb6fa38: StoreField: r1->field_b = r0
    //     0xb6fa38: stur            w0, [x1, #0xb]
    // 0xb6fa3c: r0 = Instance_SizedBox
    //     0xb6fa3c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb6fa40: StoreField: r1->field_f = r0
    //     0xb6fa40: stur            w0, [x1, #0xf]
    // 0xb6fa44: ldur            x0, [fp, #-0x50]
    // 0xb6fa48: StoreField: r1->field_13 = r0
    //     0xb6fa48: stur            w0, [x1, #0x13]
    // 0xb6fa4c: r2 = false
    //     0xb6fa4c: add             x2, NULL, #0x30  ; false
    // 0xb6fa50: ArrayStore: r1[0] = r2  ; List_4
    //     0xb6fa50: stur            w2, [x1, #0x17]
    // 0xb6fa54: StoreField: r1->field_1b = r2
    //     0xb6fa54: stur            w2, [x1, #0x1b]
    // 0xb6fa58: StoreField: r1->field_1f = r2
    //     0xb6fa58: stur            w2, [x1, #0x1f]
    // 0xb6fa5c: StoreField: r1->field_23 = r2
    //     0xb6fa5c: stur            w2, [x1, #0x23]
    // 0xb6fa60: StoreField: r1->field_27 = r2
    //     0xb6fa60: stur            w2, [x1, #0x27]
    // 0xb6fa64: StoreField: r1->field_2b = r2
    //     0xb6fa64: stur            w2, [x1, #0x2b]
    // 0xb6fa68: mov             x0, x1
    // 0xb6fa6c: ldur            x1, [fp, #-0x48]
    // 0xb6fa70: ArrayStore: r1[4] = r0  ; List_4
    //     0xb6fa70: add             x25, x1, #0x1f
    //     0xb6fa74: str             w0, [x25]
    //     0xb6fa78: tbz             w0, #0, #0xb6fa94
    //     0xb6fa7c: ldurb           w16, [x1, #-1]
    //     0xb6fa80: ldurb           w17, [x0, #-1]
    //     0xb6fa84: and             x16, x17, x16, lsr #2
    //     0xb6fa88: tst             x16, HEAP, lsr #32
    //     0xb6fa8c: b.eq            #0xb6fa94
    //     0xb6fa90: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6fa94: ldur            x0, [fp, #-0x38]
    // 0xb6fa98: LoadField: r1 = r0->field_a3
    //     0xb6fa98: ldur            w1, [x0, #0xa3]
    // 0xb6fa9c: DecompressPointer r1
    //     0xb6fa9c: add             x1, x1, HEAP, lsl #32
    // 0xb6faa0: cmp             w1, NULL
    // 0xb6faa4: b.ne            #0xb6fab0
    // 0xb6faa8: r4 = ""
    //     0xb6faa8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6faac: b               #0xb6fab4
    // 0xb6fab0: mov             x4, x1
    // 0xb6fab4: ldur            x3, [fp, #-8]
    // 0xb6fab8: stur            x4, [fp, #-0x50]
    // 0xb6fabc: LoadField: r1 = r3->field_f
    //     0xb6fabc: ldur            w1, [x3, #0xf]
    // 0xb6fac0: DecompressPointer r1
    //     0xb6fac0: add             x1, x1, HEAP, lsl #32
    // 0xb6fac4: cmp             w1, NULL
    // 0xb6fac8: b.eq            #0xb70540
    // 0xb6facc: r0 = of()
    //     0xb6facc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6fad0: LoadField: r1 = r0->field_87
    //     0xb6fad0: ldur            w1, [x0, #0x87]
    // 0xb6fad4: DecompressPointer r1
    //     0xb6fad4: add             x1, x1, HEAP, lsl #32
    // 0xb6fad8: LoadField: r0 = r1->field_2b
    //     0xb6fad8: ldur            w0, [x1, #0x2b]
    // 0xb6fadc: DecompressPointer r0
    //     0xb6fadc: add             x0, x0, HEAP, lsl #32
    // 0xb6fae0: stur            x0, [fp, #-0x60]
    // 0xb6fae4: r1 = Instance_Color
    //     0xb6fae4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6fae8: d0 = 0.700000
    //     0xb6fae8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6faec: ldr             d0, [x17, #0xf48]
    // 0xb6faf0: r0 = withOpacity()
    //     0xb6faf0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6faf4: r16 = 14.000000
    //     0xb6faf4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb6faf8: ldr             x16, [x16, #0x1d8]
    // 0xb6fafc: stp             x0, x16, [SP]
    // 0xb6fb00: ldur            x1, [fp, #-0x60]
    // 0xb6fb04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6fb04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6fb08: ldr             x4, [x4, #0xaa0]
    // 0xb6fb0c: r0 = copyWith()
    //     0xb6fb0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6fb10: stur            x0, [fp, #-0x60]
    // 0xb6fb14: r0 = Text()
    //     0xb6fb14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6fb18: mov             x1, x0
    // 0xb6fb1c: ldur            x0, [fp, #-0x50]
    // 0xb6fb20: StoreField: r1->field_b = r0
    //     0xb6fb20: stur            w0, [x1, #0xb]
    // 0xb6fb24: ldur            x0, [fp, #-0x60]
    // 0xb6fb28: StoreField: r1->field_13 = r0
    //     0xb6fb28: stur            w0, [x1, #0x13]
    // 0xb6fb2c: mov             x0, x1
    // 0xb6fb30: ldur            x1, [fp, #-0x48]
    // 0xb6fb34: ArrayStore: r1[5] = r0  ; List_4
    //     0xb6fb34: add             x25, x1, #0x23
    //     0xb6fb38: str             w0, [x25]
    //     0xb6fb3c: tbz             w0, #0, #0xb6fb58
    //     0xb6fb40: ldurb           w16, [x1, #-1]
    //     0xb6fb44: ldurb           w17, [x0, #-1]
    //     0xb6fb48: and             x16, x17, x16, lsr #2
    //     0xb6fb4c: tst             x16, HEAP, lsr #32
    //     0xb6fb50: b.eq            #0xb6fb58
    //     0xb6fb54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6fb58: r0 = Container()
    //     0xb6fb58: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6fb5c: stur            x0, [fp, #-0x50]
    // 0xb6fb60: r16 = 5.000000
    //     0xb6fb60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb6fb64: ldr             x16, [x16, #0xcf0]
    // 0xb6fb68: str             x16, [SP]
    // 0xb6fb6c: mov             x1, x0
    // 0xb6fb70: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xb6fb70: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xb6fb74: ldr             x4, [x4, #0xe0]
    // 0xb6fb78: r0 = Container()
    //     0xb6fb78: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6fb7c: ldur            x1, [fp, #-0x48]
    // 0xb6fb80: ldur            x0, [fp, #-0x50]
    // 0xb6fb84: ArrayStore: r1[6] = r0  ; List_4
    //     0xb6fb84: add             x25, x1, #0x27
    //     0xb6fb88: str             w0, [x25]
    //     0xb6fb8c: tbz             w0, #0, #0xb6fba8
    //     0xb6fb90: ldurb           w16, [x1, #-1]
    //     0xb6fb94: ldurb           w17, [x0, #-1]
    //     0xb6fb98: and             x16, x17, x16, lsr #2
    //     0xb6fb9c: tst             x16, HEAP, lsr #32
    //     0xb6fba0: b.eq            #0xb6fba8
    //     0xb6fba4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6fba8: ldur            x0, [fp, #-8]
    // 0xb6fbac: LoadField: r1 = r0->field_f
    //     0xb6fbac: ldur            w1, [x0, #0xf]
    // 0xb6fbb0: DecompressPointer r1
    //     0xb6fbb0: add             x1, x1, HEAP, lsl #32
    // 0xb6fbb4: cmp             w1, NULL
    // 0xb6fbb8: b.eq            #0xb70544
    // 0xb6fbbc: r0 = of()
    //     0xb6fbbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6fbc0: LoadField: r1 = r0->field_5b
    //     0xb6fbc0: ldur            w1, [x0, #0x5b]
    // 0xb6fbc4: DecompressPointer r1
    //     0xb6fbc4: add             x1, x1, HEAP, lsl #32
    // 0xb6fbc8: stur            x1, [fp, #-0x50]
    // 0xb6fbcc: r0 = BoxDecoration()
    //     0xb6fbcc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6fbd0: mov             x1, x0
    // 0xb6fbd4: ldur            x0, [fp, #-0x50]
    // 0xb6fbd8: stur            x1, [fp, #-0x60]
    // 0xb6fbdc: StoreField: r1->field_7 = r0
    //     0xb6fbdc: stur            w0, [x1, #7]
    // 0xb6fbe0: r0 = Instance_BoxShape
    //     0xb6fbe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb6fbe4: ldr             x0, [x0, #0x970]
    // 0xb6fbe8: StoreField: r1->field_23 = r0
    //     0xb6fbe8: stur            w0, [x1, #0x23]
    // 0xb6fbec: r0 = Container()
    //     0xb6fbec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6fbf0: stur            x0, [fp, #-0x50]
    // 0xb6fbf4: r16 = Instance_EdgeInsets
    //     0xb6fbf4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xb6fbf8: ldr             x16, [x16, #0x108]
    // 0xb6fbfc: r30 = 5.000000
    //     0xb6fbfc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb6fc00: ldr             lr, [lr, #0xcf0]
    // 0xb6fc04: stp             lr, x16, [SP, #0x10]
    // 0xb6fc08: r16 = 5.000000
    //     0xb6fc08: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb6fc0c: ldr             x16, [x16, #0xcf0]
    // 0xb6fc10: ldur            lr, [fp, #-0x60]
    // 0xb6fc14: stp             lr, x16, [SP]
    // 0xb6fc18: mov             x1, x0
    // 0xb6fc1c: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb6fc1c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb6fc20: ldr             x4, [x4, #0x118]
    // 0xb6fc24: r0 = Container()
    //     0xb6fc24: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6fc28: ldur            x1, [fp, #-0x48]
    // 0xb6fc2c: ldur            x0, [fp, #-0x50]
    // 0xb6fc30: ArrayStore: r1[7] = r0  ; List_4
    //     0xb6fc30: add             x25, x1, #0x2b
    //     0xb6fc34: str             w0, [x25]
    //     0xb6fc38: tbz             w0, #0, #0xb6fc54
    //     0xb6fc3c: ldurb           w16, [x1, #-1]
    //     0xb6fc40: ldurb           w17, [x0, #-1]
    //     0xb6fc44: and             x16, x17, x16, lsr #2
    //     0xb6fc48: tst             x16, HEAP, lsr #32
    //     0xb6fc4c: b.eq            #0xb6fc54
    //     0xb6fc50: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6fc54: ldur            x2, [fp, #-0x38]
    // 0xb6fc58: LoadField: r0 = r2->field_a7
    //     0xb6fc58: ldur            w0, [x2, #0xa7]
    // 0xb6fc5c: DecompressPointer r0
    //     0xb6fc5c: add             x0, x0, HEAP, lsl #32
    // 0xb6fc60: cmp             w0, NULL
    // 0xb6fc64: b.ne            #0xb6fc70
    // 0xb6fc68: r4 = ""
    //     0xb6fc68: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6fc6c: b               #0xb6fc74
    // 0xb6fc70: mov             x4, x0
    // 0xb6fc74: ldur            x0, [fp, #-8]
    // 0xb6fc78: ldur            x3, [fp, #-0x48]
    // 0xb6fc7c: stur            x4, [fp, #-0x50]
    // 0xb6fc80: LoadField: r1 = r0->field_f
    //     0xb6fc80: ldur            w1, [x0, #0xf]
    // 0xb6fc84: DecompressPointer r1
    //     0xb6fc84: add             x1, x1, HEAP, lsl #32
    // 0xb6fc88: cmp             w1, NULL
    // 0xb6fc8c: b.eq            #0xb70548
    // 0xb6fc90: r0 = of()
    //     0xb6fc90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6fc94: LoadField: r1 = r0->field_87
    //     0xb6fc94: ldur            w1, [x0, #0x87]
    // 0xb6fc98: DecompressPointer r1
    //     0xb6fc98: add             x1, x1, HEAP, lsl #32
    // 0xb6fc9c: LoadField: r0 = r1->field_2b
    //     0xb6fc9c: ldur            w0, [x1, #0x2b]
    // 0xb6fca0: DecompressPointer r0
    //     0xb6fca0: add             x0, x0, HEAP, lsl #32
    // 0xb6fca4: stur            x0, [fp, #-0x60]
    // 0xb6fca8: r1 = Instance_Color
    //     0xb6fca8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6fcac: d0 = 0.700000
    //     0xb6fcac: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6fcb0: ldr             d0, [x17, #0xf48]
    // 0xb6fcb4: r0 = withOpacity()
    //     0xb6fcb4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6fcb8: r16 = 14.000000
    //     0xb6fcb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb6fcbc: ldr             x16, [x16, #0x1d8]
    // 0xb6fcc0: stp             x0, x16, [SP]
    // 0xb6fcc4: ldur            x1, [fp, #-0x60]
    // 0xb6fcc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6fcc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6fccc: ldr             x4, [x4, #0xaa0]
    // 0xb6fcd0: r0 = copyWith()
    //     0xb6fcd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6fcd4: stur            x0, [fp, #-0x60]
    // 0xb6fcd8: r0 = Text()
    //     0xb6fcd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6fcdc: mov             x1, x0
    // 0xb6fce0: ldur            x0, [fp, #-0x50]
    // 0xb6fce4: StoreField: r1->field_b = r0
    //     0xb6fce4: stur            w0, [x1, #0xb]
    // 0xb6fce8: ldur            x0, [fp, #-0x60]
    // 0xb6fcec: StoreField: r1->field_13 = r0
    //     0xb6fcec: stur            w0, [x1, #0x13]
    // 0xb6fcf0: mov             x0, x1
    // 0xb6fcf4: ldur            x1, [fp, #-0x48]
    // 0xb6fcf8: ArrayStore: r1[8] = r0  ; List_4
    //     0xb6fcf8: add             x25, x1, #0x2f
    //     0xb6fcfc: str             w0, [x25]
    //     0xb6fd00: tbz             w0, #0, #0xb6fd1c
    //     0xb6fd04: ldurb           w16, [x1, #-1]
    //     0xb6fd08: ldurb           w17, [x0, #-1]
    //     0xb6fd0c: and             x16, x17, x16, lsr #2
    //     0xb6fd10: tst             x16, HEAP, lsr #32
    //     0xb6fd14: b.eq            #0xb6fd1c
    //     0xb6fd18: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6fd1c: r1 = <Widget>
    //     0xb6fd1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6fd20: r0 = AllocateGrowableArray()
    //     0xb6fd20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6fd24: mov             x1, x0
    // 0xb6fd28: ldur            x0, [fp, #-0x48]
    // 0xb6fd2c: stur            x1, [fp, #-0x50]
    // 0xb6fd30: StoreField: r1->field_f = r0
    //     0xb6fd30: stur            w0, [x1, #0xf]
    // 0xb6fd34: r0 = 18
    //     0xb6fd34: movz            x0, #0x12
    // 0xb6fd38: StoreField: r1->field_b = r0
    //     0xb6fd38: stur            w0, [x1, #0xb]
    // 0xb6fd3c: r0 = Row()
    //     0xb6fd3c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6fd40: mov             x2, x0
    // 0xb6fd44: r0 = Instance_Axis
    //     0xb6fd44: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6fd48: stur            x2, [fp, #-0x48]
    // 0xb6fd4c: StoreField: r2->field_f = r0
    //     0xb6fd4c: stur            w0, [x2, #0xf]
    // 0xb6fd50: r3 = Instance_MainAxisAlignment
    //     0xb6fd50: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6fd54: ldr             x3, [x3, #0xa08]
    // 0xb6fd58: StoreField: r2->field_13 = r3
    //     0xb6fd58: stur            w3, [x2, #0x13]
    // 0xb6fd5c: r4 = Instance_MainAxisSize
    //     0xb6fd5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6fd60: ldr             x4, [x4, #0xa10]
    // 0xb6fd64: ArrayStore: r2[0] = r4  ; List_4
    //     0xb6fd64: stur            w4, [x2, #0x17]
    // 0xb6fd68: r1 = Instance_CrossAxisAlignment
    //     0xb6fd68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb6fd6c: ldr             x1, [x1, #0xa18]
    // 0xb6fd70: StoreField: r2->field_1b = r1
    //     0xb6fd70: stur            w1, [x2, #0x1b]
    // 0xb6fd74: r5 = Instance_VerticalDirection
    //     0xb6fd74: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6fd78: ldr             x5, [x5, #0xa20]
    // 0xb6fd7c: StoreField: r2->field_23 = r5
    //     0xb6fd7c: stur            w5, [x2, #0x23]
    // 0xb6fd80: r6 = Instance_Clip
    //     0xb6fd80: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6fd84: ldr             x6, [x6, #0x38]
    // 0xb6fd88: StoreField: r2->field_2b = r6
    //     0xb6fd88: stur            w6, [x2, #0x2b]
    // 0xb6fd8c: StoreField: r2->field_2f = rZR
    //     0xb6fd8c: stur            xzr, [x2, #0x2f]
    // 0xb6fd90: ldur            x1, [fp, #-0x50]
    // 0xb6fd94: StoreField: r2->field_b = r1
    //     0xb6fd94: stur            w1, [x2, #0xb]
    // 0xb6fd98: ldur            x7, [fp, #-0x38]
    // 0xb6fd9c: LoadField: r1 = r7->field_9b
    //     0xb6fd9c: ldur            w1, [x7, #0x9b]
    // 0xb6fda0: DecompressPointer r1
    //     0xb6fda0: add             x1, x1, HEAP, lsl #32
    // 0xb6fda4: cmp             w1, NULL
    // 0xb6fda8: b.ne            #0xb6fdb4
    // 0xb6fdac: r1 = "0.0"
    //     0xb6fdac: add             x1, PP, #0xe, lsl #12  ; [pp+0xe628] "0.0"
    //     0xb6fdb0: ldr             x1, [x1, #0x628]
    // 0xb6fdb4: r0 = parse()
    //     0xb6fdb4: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb6fdb8: ldur            x2, [fp, #-0x38]
    // 0xb6fdbc: stur            d0, [fp, #-0x88]
    // 0xb6fdc0: LoadField: r0 = r2->field_9b
    //     0xb6fdc0: ldur            w0, [x2, #0x9b]
    // 0xb6fdc4: DecompressPointer r0
    //     0xb6fdc4: add             x0, x0, HEAP, lsl #32
    // 0xb6fdc8: cmp             w0, NULL
    // 0xb6fdcc: b.ne            #0xb6fdd8
    // 0xb6fdd0: r1 = "0"
    //     0xb6fdd0: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xb6fdd4: b               #0xb6fddc
    // 0xb6fdd8: mov             x1, x0
    // 0xb6fddc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb6fddc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb6fde0: r0 = parse()
    //     0xb6fde0: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb6fde4: stur            x0, [fp, #-0x18]
    // 0xb6fde8: r0 = RatingWidget()
    //     0xb6fde8: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xb6fdec: mov             x3, x0
    // 0xb6fdf0: r0 = Instance_Icon
    //     0xb6fdf0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xb6fdf4: ldr             x0, [x0, #0x190]
    // 0xb6fdf8: stur            x3, [fp, #-0x50]
    // 0xb6fdfc: StoreField: r3->field_7 = r0
    //     0xb6fdfc: stur            w0, [x3, #7]
    // 0xb6fe00: r0 = Instance_Icon
    //     0xb6fe00: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xb6fe04: ldr             x0, [x0, #0x198]
    // 0xb6fe08: StoreField: r3->field_b = r0
    //     0xb6fe08: stur            w0, [x3, #0xb]
    // 0xb6fe0c: r0 = Instance_Icon
    //     0xb6fe0c: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xb6fe10: ldr             x0, [x0, #0x1a0]
    // 0xb6fe14: StoreField: r3->field_f = r0
    //     0xb6fe14: stur            w0, [x3, #0xf]
    // 0xb6fe18: r1 = Function '<anonymous closure>':.
    //     0xb6fe18: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b60] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb6fe1c: ldr             x1, [x1, #0xb60]
    // 0xb6fe20: r2 = Null
    //     0xb6fe20: mov             x2, NULL
    // 0xb6fe24: r0 = AllocateClosure()
    //     0xb6fe24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6fe28: stur            x0, [fp, #-0x60]
    // 0xb6fe2c: r0 = RatingBar()
    //     0xb6fe2c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb6fe30: mov             x2, x0
    // 0xb6fe34: ldur            x0, [fp, #-0x60]
    // 0xb6fe38: stur            x2, [fp, #-0x68]
    // 0xb6fe3c: StoreField: r2->field_b = r0
    //     0xb6fe3c: stur            w0, [x2, #0xb]
    // 0xb6fe40: r0 = true
    //     0xb6fe40: add             x0, NULL, #0x20  ; true
    // 0xb6fe44: StoreField: r2->field_1f = r0
    //     0xb6fe44: stur            w0, [x2, #0x1f]
    // 0xb6fe48: r1 = Instance_Axis
    //     0xb6fe48: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6fe4c: StoreField: r2->field_23 = r1
    //     0xb6fe4c: stur            w1, [x2, #0x23]
    // 0xb6fe50: StoreField: r2->field_27 = r0
    //     0xb6fe50: stur            w0, [x2, #0x27]
    // 0xb6fe54: d0 = 2.000000
    //     0xb6fe54: fmov            d0, #2.00000000
    // 0xb6fe58: StoreField: r2->field_2b = d0
    //     0xb6fe58: stur            d0, [x2, #0x2b]
    // 0xb6fe5c: StoreField: r2->field_33 = r0
    //     0xb6fe5c: stur            w0, [x2, #0x33]
    // 0xb6fe60: ldur            d0, [fp, #-0x88]
    // 0xb6fe64: StoreField: r2->field_37 = d0
    //     0xb6fe64: stur            d0, [x2, #0x37]
    // 0xb6fe68: ldur            x1, [fp, #-0x18]
    // 0xb6fe6c: StoreField: r2->field_3f = r1
    //     0xb6fe6c: stur            x1, [x2, #0x3f]
    // 0xb6fe70: r1 = Instance_EdgeInsets
    //     0xb6fe70: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb6fe74: StoreField: r2->field_47 = r1
    //     0xb6fe74: stur            w1, [x2, #0x47]
    // 0xb6fe78: d0 = 18.000000
    //     0xb6fe78: fmov            d0, #18.00000000
    // 0xb6fe7c: StoreField: r2->field_4b = d0
    //     0xb6fe7c: stur            d0, [x2, #0x4b]
    // 0xb6fe80: StoreField: r2->field_53 = rZR
    //     0xb6fe80: stur            xzr, [x2, #0x53]
    // 0xb6fe84: r1 = false
    //     0xb6fe84: add             x1, NULL, #0x30  ; false
    // 0xb6fe88: StoreField: r2->field_5b = r1
    //     0xb6fe88: stur            w1, [x2, #0x5b]
    // 0xb6fe8c: StoreField: r2->field_5f = r1
    //     0xb6fe8c: stur            w1, [x2, #0x5f]
    // 0xb6fe90: ldur            x1, [fp, #-0x50]
    // 0xb6fe94: StoreField: r2->field_67 = r1
    //     0xb6fe94: stur            w1, [x2, #0x67]
    // 0xb6fe98: ldur            x3, [fp, #-0x38]
    // 0xb6fe9c: LoadField: r1 = r3->field_9f
    //     0xb6fe9c: ldur            w1, [x3, #0x9f]
    // 0xb6fea0: DecompressPointer r1
    //     0xb6fea0: add             x1, x1, HEAP, lsl #32
    // 0xb6fea4: cmp             w1, NULL
    // 0xb6fea8: b.ne            #0xb6feb4
    // 0xb6feac: r0 = Null
    //     0xb6feac: mov             x0, NULL
    // 0xb6feb0: b               #0xb6feb8
    // 0xb6feb4: r0 = trim()
    //     0xb6feb4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb6feb8: cmp             w0, NULL
    // 0xb6febc: b.ne            #0xb6fec4
    // 0xb6fec0: r0 = ""
    //     0xb6fec0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6fec4: ldur            x1, [fp, #-0x10]
    // 0xb6fec8: stur            x0, [fp, #-0x50]
    // 0xb6fecc: r0 = value()
    //     0xb6fecc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6fed0: tbnz            w0, #4, #0xb6fedc
    // 0xb6fed4: r0 = Null
    //     0xb6fed4: mov             x0, NULL
    // 0xb6fed8: b               #0xb6fee0
    // 0xb6fedc: r0 = 4
    //     0xb6fedc: movz            x0, #0x4
    // 0xb6fee0: ldur            x1, [fp, #-0x10]
    // 0xb6fee4: stur            x0, [fp, #-0x60]
    // 0xb6fee8: r0 = value()
    //     0xb6fee8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6feec: tbnz            w0, #4, #0xb6fefc
    // 0xb6fef0: r5 = Instance_TextOverflow
    //     0xb6fef0: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xb6fef4: ldr             x5, [x5, #0x3a8]
    // 0xb6fef8: b               #0xb6ff04
    // 0xb6fefc: r5 = Instance_TextOverflow
    //     0xb6fefc: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb6ff00: ldr             x5, [x5, #0xe10]
    // 0xb6ff04: ldur            x4, [fp, #-8]
    // 0xb6ff08: ldur            x3, [fp, #-0x38]
    // 0xb6ff0c: ldur            x2, [fp, #-0x50]
    // 0xb6ff10: ldur            x0, [fp, #-0x60]
    // 0xb6ff14: stur            x5, [fp, #-0x70]
    // 0xb6ff18: LoadField: r1 = r4->field_f
    //     0xb6ff18: ldur            w1, [x4, #0xf]
    // 0xb6ff1c: DecompressPointer r1
    //     0xb6ff1c: add             x1, x1, HEAP, lsl #32
    // 0xb6ff20: cmp             w1, NULL
    // 0xb6ff24: b.eq            #0xb7054c
    // 0xb6ff28: r0 = of()
    //     0xb6ff28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ff2c: LoadField: r1 = r0->field_87
    //     0xb6ff2c: ldur            w1, [x0, #0x87]
    // 0xb6ff30: DecompressPointer r1
    //     0xb6ff30: add             x1, x1, HEAP, lsl #32
    // 0xb6ff34: LoadField: r0 = r1->field_2b
    //     0xb6ff34: ldur            w0, [x1, #0x2b]
    // 0xb6ff38: DecompressPointer r0
    //     0xb6ff38: add             x0, x0, HEAP, lsl #32
    // 0xb6ff3c: LoadField: r1 = r0->field_13
    //     0xb6ff3c: ldur            w1, [x0, #0x13]
    // 0xb6ff40: DecompressPointer r1
    //     0xb6ff40: add             x1, x1, HEAP, lsl #32
    // 0xb6ff44: stur            x1, [fp, #-0x78]
    // 0xb6ff48: r0 = TextStyle()
    //     0xb6ff48: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb6ff4c: mov             x1, x0
    // 0xb6ff50: r0 = true
    //     0xb6ff50: add             x0, NULL, #0x20  ; true
    // 0xb6ff54: stur            x1, [fp, #-0x80]
    // 0xb6ff58: StoreField: r1->field_7 = r0
    //     0xb6ff58: stur            w0, [x1, #7]
    // 0xb6ff5c: r2 = Instance_Color
    //     0xb6ff5c: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6ff60: StoreField: r1->field_b = r2
    //     0xb6ff60: stur            w2, [x1, #0xb]
    // 0xb6ff64: r2 = 12.000000
    //     0xb6ff64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb6ff68: ldr             x2, [x2, #0x9e8]
    // 0xb6ff6c: StoreField: r1->field_1f = r2
    //     0xb6ff6c: stur            w2, [x1, #0x1f]
    // 0xb6ff70: ldur            x2, [fp, #-0x78]
    // 0xb6ff74: StoreField: r1->field_13 = r2
    //     0xb6ff74: stur            w2, [x1, #0x13]
    // 0xb6ff78: r0 = Text()
    //     0xb6ff78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6ff7c: mov             x3, x0
    // 0xb6ff80: ldur            x0, [fp, #-0x50]
    // 0xb6ff84: stur            x3, [fp, #-0x78]
    // 0xb6ff88: StoreField: r3->field_b = r0
    //     0xb6ff88: stur            w0, [x3, #0xb]
    // 0xb6ff8c: ldur            x0, [fp, #-0x80]
    // 0xb6ff90: StoreField: r3->field_13 = r0
    //     0xb6ff90: stur            w0, [x3, #0x13]
    // 0xb6ff94: r0 = Instance_TextAlign
    //     0xb6ff94: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb6ff98: StoreField: r3->field_1b = r0
    //     0xb6ff98: stur            w0, [x3, #0x1b]
    // 0xb6ff9c: ldur            x0, [fp, #-0x70]
    // 0xb6ffa0: StoreField: r3->field_2b = r0
    //     0xb6ffa0: stur            w0, [x3, #0x2b]
    // 0xb6ffa4: ldur            x0, [fp, #-0x60]
    // 0xb6ffa8: StoreField: r3->field_37 = r0
    //     0xb6ffa8: stur            w0, [x3, #0x37]
    // 0xb6ffac: r1 = Null
    //     0xb6ffac: mov             x1, NULL
    // 0xb6ffb0: r2 = 2
    //     0xb6ffb0: movz            x2, #0x2
    // 0xb6ffb4: r0 = AllocateArray()
    //     0xb6ffb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6ffb8: mov             x2, x0
    // 0xb6ffbc: ldur            x0, [fp, #-0x78]
    // 0xb6ffc0: stur            x2, [fp, #-0x50]
    // 0xb6ffc4: StoreField: r2->field_f = r0
    //     0xb6ffc4: stur            w0, [x2, #0xf]
    // 0xb6ffc8: r1 = <Widget>
    //     0xb6ffc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6ffcc: r0 = AllocateGrowableArray()
    //     0xb6ffcc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6ffd0: mov             x2, x0
    // 0xb6ffd4: ldur            x0, [fp, #-0x50]
    // 0xb6ffd8: stur            x2, [fp, #-0x60]
    // 0xb6ffdc: StoreField: r2->field_f = r0
    //     0xb6ffdc: stur            w0, [x2, #0xf]
    // 0xb6ffe0: r0 = 2
    //     0xb6ffe0: movz            x0, #0x2
    // 0xb6ffe4: StoreField: r2->field_b = r0
    //     0xb6ffe4: stur            w0, [x2, #0xb]
    // 0xb6ffe8: ldur            x0, [fp, #-0x38]
    // 0xb6ffec: LoadField: r1 = r0->field_9f
    //     0xb6ffec: ldur            w1, [x0, #0x9f]
    // 0xb6fff0: DecompressPointer r1
    //     0xb6fff0: add             x1, x1, HEAP, lsl #32
    // 0xb6fff4: cmp             w1, NULL
    // 0xb6fff8: b.ne            #0xb70004
    // 0xb6fffc: r0 = Null
    //     0xb6fffc: mov             x0, NULL
    // 0xb70000: b               #0xb70008
    // 0xb70004: r0 = trim()
    //     0xb70004: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb70008: cmp             w0, NULL
    // 0xb7000c: b.ne            #0xb70018
    // 0xb70010: r1 = ""
    //     0xb70010: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb70014: b               #0xb7001c
    // 0xb70018: mov             x1, x0
    // 0xb7001c: ldur            x0, [fp, #-8]
    // 0xb70020: LoadField: r2 = r0->field_f
    //     0xb70020: ldur            w2, [x0, #0xf]
    // 0xb70024: DecompressPointer r2
    //     0xb70024: add             x2, x2, HEAP, lsl #32
    // 0xb70028: cmp             w2, NULL
    // 0xb7002c: b.eq            #0xb70550
    // 0xb70030: r0 = TextExceeds.textExceedsLines()
    //     0xb70030: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xb70034: tbnz            w0, #4, #0xb701c0
    // 0xb70038: ldur            x1, [fp, #-0x10]
    // 0xb7003c: r0 = value()
    //     0xb7003c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb70040: tbnz            w0, #4, #0xb70050
    // 0xb70044: r3 = "Know Less"
    //     0xb70044: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xb70048: ldr             x3, [x3, #0x1d0]
    // 0xb7004c: b               #0xb70058
    // 0xb70050: r3 = "Know more"
    //     0xb70050: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xb70054: ldr             x3, [x3, #0x20]
    // 0xb70058: ldur            x0, [fp, #-8]
    // 0xb7005c: ldur            x2, [fp, #-0x60]
    // 0xb70060: stur            x3, [fp, #-0x10]
    // 0xb70064: LoadField: r1 = r0->field_f
    //     0xb70064: ldur            w1, [x0, #0xf]
    // 0xb70068: DecompressPointer r1
    //     0xb70068: add             x1, x1, HEAP, lsl #32
    // 0xb7006c: cmp             w1, NULL
    // 0xb70070: b.eq            #0xb70554
    // 0xb70074: r0 = of()
    //     0xb70074: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb70078: LoadField: r1 = r0->field_87
    //     0xb70078: ldur            w1, [x0, #0x87]
    // 0xb7007c: DecompressPointer r1
    //     0xb7007c: add             x1, x1, HEAP, lsl #32
    // 0xb70080: LoadField: r0 = r1->field_7
    //     0xb70080: ldur            w0, [x1, #7]
    // 0xb70084: DecompressPointer r0
    //     0xb70084: add             x0, x0, HEAP, lsl #32
    // 0xb70088: ldur            x2, [fp, #-8]
    // 0xb7008c: stur            x0, [fp, #-0x50]
    // 0xb70090: LoadField: r1 = r2->field_f
    //     0xb70090: ldur            w1, [x2, #0xf]
    // 0xb70094: DecompressPointer r1
    //     0xb70094: add             x1, x1, HEAP, lsl #32
    // 0xb70098: cmp             w1, NULL
    // 0xb7009c: b.eq            #0xb70558
    // 0xb700a0: r0 = of()
    //     0xb700a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb700a4: LoadField: r1 = r0->field_5b
    //     0xb700a4: ldur            w1, [x0, #0x5b]
    // 0xb700a8: DecompressPointer r1
    //     0xb700a8: add             x1, x1, HEAP, lsl #32
    // 0xb700ac: r16 = 12.000000
    //     0xb700ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb700b0: ldr             x16, [x16, #0x9e8]
    // 0xb700b4: stp             x1, x16, [SP, #8]
    // 0xb700b8: r16 = Instance_TextDecoration
    //     0xb700b8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb700bc: ldr             x16, [x16, #0x10]
    // 0xb700c0: str             x16, [SP]
    // 0xb700c4: ldur            x1, [fp, #-0x50]
    // 0xb700c8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb700c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb700cc: ldr             x4, [x4, #0xe38]
    // 0xb700d0: r0 = copyWith()
    //     0xb700d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb700d4: stur            x0, [fp, #-0x50]
    // 0xb700d8: r0 = Text()
    //     0xb700d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb700dc: mov             x1, x0
    // 0xb700e0: ldur            x0, [fp, #-0x10]
    // 0xb700e4: stur            x1, [fp, #-0x70]
    // 0xb700e8: StoreField: r1->field_b = r0
    //     0xb700e8: stur            w0, [x1, #0xb]
    // 0xb700ec: ldur            x0, [fp, #-0x50]
    // 0xb700f0: StoreField: r1->field_13 = r0
    //     0xb700f0: stur            w0, [x1, #0x13]
    // 0xb700f4: r0 = Padding()
    //     0xb700f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb700f8: mov             x1, x0
    // 0xb700fc: r0 = Instance_EdgeInsets
    //     0xb700fc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb70100: ldr             x0, [x0, #0x668]
    // 0xb70104: stur            x1, [fp, #-0x10]
    // 0xb70108: StoreField: r1->field_f = r0
    //     0xb70108: stur            w0, [x1, #0xf]
    // 0xb7010c: ldur            x0, [fp, #-0x70]
    // 0xb70110: StoreField: r1->field_b = r0
    //     0xb70110: stur            w0, [x1, #0xb]
    // 0xb70114: r0 = GestureDetector()
    //     0xb70114: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb70118: ldur            x2, [fp, #-0x28]
    // 0xb7011c: r1 = Function '<anonymous closure>':.
    //     0xb7011c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b68] AnonymousClosure: (0xb71058), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget (0xb6f5b8)
    //     0xb70120: ldr             x1, [x1, #0xb68]
    // 0xb70124: stur            x0, [fp, #-0x28]
    // 0xb70128: r0 = AllocateClosure()
    //     0xb70128: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7012c: ldur            x16, [fp, #-0x10]
    // 0xb70130: stp             x16, x0, [SP]
    // 0xb70134: ldur            x1, [fp, #-0x28]
    // 0xb70138: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb70138: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb7013c: ldr             x4, [x4, #0xaf0]
    // 0xb70140: r0 = GestureDetector()
    //     0xb70140: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb70144: ldur            x0, [fp, #-0x60]
    // 0xb70148: LoadField: r1 = r0->field_b
    //     0xb70148: ldur            w1, [x0, #0xb]
    // 0xb7014c: LoadField: r2 = r0->field_f
    //     0xb7014c: ldur            w2, [x0, #0xf]
    // 0xb70150: DecompressPointer r2
    //     0xb70150: add             x2, x2, HEAP, lsl #32
    // 0xb70154: LoadField: r3 = r2->field_b
    //     0xb70154: ldur            w3, [x2, #0xb]
    // 0xb70158: r2 = LoadInt32Instr(r1)
    //     0xb70158: sbfx            x2, x1, #1, #0x1f
    // 0xb7015c: stur            x2, [fp, #-0x18]
    // 0xb70160: r1 = LoadInt32Instr(r3)
    //     0xb70160: sbfx            x1, x3, #1, #0x1f
    // 0xb70164: cmp             x2, x1
    // 0xb70168: b.ne            #0xb70174
    // 0xb7016c: mov             x1, x0
    // 0xb70170: r0 = _growToNextCapacity()
    //     0xb70170: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb70174: ldur            x2, [fp, #-0x60]
    // 0xb70178: ldur            x3, [fp, #-0x18]
    // 0xb7017c: add             x0, x3, #1
    // 0xb70180: lsl             x1, x0, #1
    // 0xb70184: StoreField: r2->field_b = r1
    //     0xb70184: stur            w1, [x2, #0xb]
    // 0xb70188: LoadField: r1 = r2->field_f
    //     0xb70188: ldur            w1, [x2, #0xf]
    // 0xb7018c: DecompressPointer r1
    //     0xb7018c: add             x1, x1, HEAP, lsl #32
    // 0xb70190: ldur            x0, [fp, #-0x28]
    // 0xb70194: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb70194: add             x25, x1, x3, lsl #2
    //     0xb70198: add             x25, x25, #0xf
    //     0xb7019c: str             w0, [x25]
    //     0xb701a0: tbz             w0, #0, #0xb701bc
    //     0xb701a4: ldurb           w16, [x1, #-1]
    //     0xb701a8: ldurb           w17, [x0, #-1]
    //     0xb701ac: and             x16, x17, x16, lsr #2
    //     0xb701b0: tst             x16, HEAP, lsr #32
    //     0xb701b4: b.eq            #0xb701bc
    //     0xb701b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb701bc: b               #0xb701c4
    // 0xb701c0: ldur            x2, [fp, #-0x60]
    // 0xb701c4: ldur            x4, [fp, #-0x30]
    // 0xb701c8: ldur            x3, [fp, #-0x58]
    // 0xb701cc: ldur            x1, [fp, #-0x48]
    // 0xb701d0: ldur            x0, [fp, #-0x68]
    // 0xb701d4: r0 = Column()
    //     0xb701d4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb701d8: mov             x3, x0
    // 0xb701dc: r0 = Instance_Axis
    //     0xb701dc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb701e0: stur            x3, [fp, #-0x10]
    // 0xb701e4: StoreField: r3->field_f = r0
    //     0xb701e4: stur            w0, [x3, #0xf]
    // 0xb701e8: r4 = Instance_MainAxisAlignment
    //     0xb701e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb701ec: ldr             x4, [x4, #0xa08]
    // 0xb701f0: StoreField: r3->field_13 = r4
    //     0xb701f0: stur            w4, [x3, #0x13]
    // 0xb701f4: r1 = Instance_MainAxisSize
    //     0xb701f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb701f8: ldr             x1, [x1, #0xa10]
    // 0xb701fc: ArrayStore: r3[0] = r1  ; List_4
    //     0xb701fc: stur            w1, [x3, #0x17]
    // 0xb70200: r5 = Instance_CrossAxisAlignment
    //     0xb70200: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb70204: ldr             x5, [x5, #0x890]
    // 0xb70208: StoreField: r3->field_1b = r5
    //     0xb70208: stur            w5, [x3, #0x1b]
    // 0xb7020c: r6 = Instance_VerticalDirection
    //     0xb7020c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb70210: ldr             x6, [x6, #0xa20]
    // 0xb70214: StoreField: r3->field_23 = r6
    //     0xb70214: stur            w6, [x3, #0x23]
    // 0xb70218: r7 = Instance_Clip
    //     0xb70218: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7021c: ldr             x7, [x7, #0x38]
    // 0xb70220: StoreField: r3->field_2b = r7
    //     0xb70220: stur            w7, [x3, #0x2b]
    // 0xb70224: StoreField: r3->field_2f = rZR
    //     0xb70224: stur            xzr, [x3, #0x2f]
    // 0xb70228: ldur            x1, [fp, #-0x60]
    // 0xb7022c: StoreField: r3->field_b = r1
    //     0xb7022c: stur            w1, [x3, #0xb]
    // 0xb70230: r1 = Null
    //     0xb70230: mov             x1, NULL
    // 0xb70234: r2 = 16
    //     0xb70234: movz            x2, #0x10
    // 0xb70238: r0 = AllocateArray()
    //     0xb70238: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7023c: mov             x2, x0
    // 0xb70240: ldur            x0, [fp, #-0x58]
    // 0xb70244: stur            x2, [fp, #-0x28]
    // 0xb70248: StoreField: r2->field_f = r0
    //     0xb70248: stur            w0, [x2, #0xf]
    // 0xb7024c: r16 = Instance_SizedBox
    //     0xb7024c: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xb70250: ldr             x16, [x16, #0xd8]
    // 0xb70254: StoreField: r2->field_13 = r16
    //     0xb70254: stur            w16, [x2, #0x13]
    // 0xb70258: ldur            x0, [fp, #-0x48]
    // 0xb7025c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7025c: stur            w0, [x2, #0x17]
    // 0xb70260: r16 = Instance_SizedBox
    //     0xb70260: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xb70264: ldr             x16, [x16, #0xd8]
    // 0xb70268: StoreField: r2->field_1b = r16
    //     0xb70268: stur            w16, [x2, #0x1b]
    // 0xb7026c: ldur            x0, [fp, #-0x68]
    // 0xb70270: StoreField: r2->field_1f = r0
    //     0xb70270: stur            w0, [x2, #0x1f]
    // 0xb70274: r16 = Instance_SizedBox
    //     0xb70274: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb70278: ldr             x16, [x16, #0x9f0]
    // 0xb7027c: StoreField: r2->field_23 = r16
    //     0xb7027c: stur            w16, [x2, #0x23]
    // 0xb70280: ldur            x0, [fp, #-0x10]
    // 0xb70284: StoreField: r2->field_27 = r0
    //     0xb70284: stur            w0, [x2, #0x27]
    // 0xb70288: r16 = Instance_SizedBox
    //     0xb70288: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb7028c: ldr             x16, [x16, #0x9f0]
    // 0xb70290: StoreField: r2->field_2b = r16
    //     0xb70290: stur            w16, [x2, #0x2b]
    // 0xb70294: r1 = <Widget>
    //     0xb70294: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb70298: r0 = AllocateGrowableArray()
    //     0xb70298: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7029c: mov             x3, x0
    // 0xb702a0: ldur            x0, [fp, #-0x28]
    // 0xb702a4: stur            x3, [fp, #-0x10]
    // 0xb702a8: StoreField: r3->field_f = r0
    //     0xb702a8: stur            w0, [x3, #0xf]
    // 0xb702ac: r0 = 16
    //     0xb702ac: movz            x0, #0x10
    // 0xb702b0: StoreField: r3->field_b = r0
    //     0xb702b0: stur            w0, [x3, #0xb]
    // 0xb702b4: ldur            x0, [fp, #-0x30]
    // 0xb702b8: tbnz            w0, #4, #0xb703f8
    // 0xb702bc: ldur            x2, [fp, #-0x38]
    // 0xb702c0: LoadField: r0 = r2->field_ab
    //     0xb702c0: ldur            w0, [x2, #0xab]
    // 0xb702c4: DecompressPointer r0
    //     0xb702c4: add             x0, x0, HEAP, lsl #32
    // 0xb702c8: cmp             w0, NULL
    // 0xb702cc: b.ne            #0xb702d8
    // 0xb702d0: r0 = Null
    //     0xb702d0: mov             x0, NULL
    // 0xb702d4: b               #0xb702e0
    // 0xb702d8: LoadField: r1 = r0->field_b
    //     0xb702d8: ldur            w1, [x0, #0xb]
    // 0xb702dc: mov             x0, x1
    // 0xb702e0: cmp             w0, NULL
    // 0xb702e4: b.ne            #0xb702f0
    // 0xb702e8: r0 = 0
    //     0xb702e8: movz            x0, #0
    // 0xb702ec: b               #0xb702f8
    // 0xb702f0: r1 = LoadInt32Instr(r0)
    //     0xb702f0: sbfx            x1, x0, #1, #0x1f
    // 0xb702f4: mov             x0, x1
    // 0xb702f8: cmp             x0, #3
    // 0xb702fc: b.gt            #0xb7031c
    // 0xb70300: ldur            x1, [fp, #-8]
    // 0xb70304: LoadField: r0 = r1->field_f
    //     0xb70304: ldur            w0, [x1, #0xf]
    // 0xb70308: DecompressPointer r0
    //     0xb70308: add             x0, x0, HEAP, lsl #32
    // 0xb7030c: cmp             w0, NULL
    // 0xb70310: b.eq            #0xb7055c
    // 0xb70314: r0 = _buildImagesRow()
    //     0xb70314: bl              #0xb70d54  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow
    // 0xb70318: b               #0xb70334
    // 0xb7031c: ldur            x1, [fp, #-8]
    // 0xb70320: LoadField: r3 = r1->field_f
    //     0xb70320: ldur            w3, [x1, #0xf]
    // 0xb70324: DecompressPointer r3
    //     0xb70324: add             x3, x3, HEAP, lsl #32
    // 0xb70328: cmp             w3, NULL
    // 0xb7032c: b.eq            #0xb70560
    // 0xb70330: r0 = _buildImagesRowWithMore()
    //     0xb70330: bl              #0xb70564  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore
    // 0xb70334: ldur            x1, [fp, #-0x10]
    // 0xb70338: stur            x0, [fp, #-8]
    // 0xb7033c: r0 = SizedBox()
    //     0xb7033c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb70340: mov             x1, x0
    // 0xb70344: r0 = 120.000000
    //     0xb70344: add             x0, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb70348: ldr             x0, [x0, #0x3a0]
    // 0xb7034c: stur            x1, [fp, #-0x28]
    // 0xb70350: StoreField: r1->field_13 = r0
    //     0xb70350: stur            w0, [x1, #0x13]
    // 0xb70354: ldur            x0, [fp, #-8]
    // 0xb70358: StoreField: r1->field_b = r0
    //     0xb70358: stur            w0, [x1, #0xb]
    // 0xb7035c: r0 = Padding()
    //     0xb7035c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb70360: mov             x2, x0
    // 0xb70364: r0 = Instance_EdgeInsets
    //     0xb70364: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb70368: ldr             x0, [x0, #0x858]
    // 0xb7036c: stur            x2, [fp, #-8]
    // 0xb70370: StoreField: r2->field_f = r0
    //     0xb70370: stur            w0, [x2, #0xf]
    // 0xb70374: ldur            x0, [fp, #-0x28]
    // 0xb70378: StoreField: r2->field_b = r0
    //     0xb70378: stur            w0, [x2, #0xb]
    // 0xb7037c: ldur            x0, [fp, #-0x10]
    // 0xb70380: LoadField: r1 = r0->field_b
    //     0xb70380: ldur            w1, [x0, #0xb]
    // 0xb70384: LoadField: r3 = r0->field_f
    //     0xb70384: ldur            w3, [x0, #0xf]
    // 0xb70388: DecompressPointer r3
    //     0xb70388: add             x3, x3, HEAP, lsl #32
    // 0xb7038c: LoadField: r4 = r3->field_b
    //     0xb7038c: ldur            w4, [x3, #0xb]
    // 0xb70390: r3 = LoadInt32Instr(r1)
    //     0xb70390: sbfx            x3, x1, #1, #0x1f
    // 0xb70394: stur            x3, [fp, #-0x18]
    // 0xb70398: r1 = LoadInt32Instr(r4)
    //     0xb70398: sbfx            x1, x4, #1, #0x1f
    // 0xb7039c: cmp             x3, x1
    // 0xb703a0: b.ne            #0xb703ac
    // 0xb703a4: mov             x1, x0
    // 0xb703a8: r0 = _growToNextCapacity()
    //     0xb703a8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb703ac: ldur            x2, [fp, #-0x10]
    // 0xb703b0: ldur            x3, [fp, #-0x18]
    // 0xb703b4: add             x0, x3, #1
    // 0xb703b8: lsl             x1, x0, #1
    // 0xb703bc: StoreField: r2->field_b = r1
    //     0xb703bc: stur            w1, [x2, #0xb]
    // 0xb703c0: LoadField: r1 = r2->field_f
    //     0xb703c0: ldur            w1, [x2, #0xf]
    // 0xb703c4: DecompressPointer r1
    //     0xb703c4: add             x1, x1, HEAP, lsl #32
    // 0xb703c8: ldur            x0, [fp, #-8]
    // 0xb703cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb703cc: add             x25, x1, x3, lsl #2
    //     0xb703d0: add             x25, x25, #0xf
    //     0xb703d4: str             w0, [x25]
    //     0xb703d8: tbz             w0, #0, #0xb703f4
    //     0xb703dc: ldurb           w16, [x1, #-1]
    //     0xb703e0: ldurb           w17, [x0, #-1]
    //     0xb703e4: and             x16, x17, x16, lsr #2
    //     0xb703e8: tst             x16, HEAP, lsr #32
    //     0xb703ec: b.eq            #0xb703f4
    //     0xb703f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb703f4: b               #0xb703fc
    // 0xb703f8: mov             x2, x3
    // 0xb703fc: ldur            x1, [fp, #-0x20]
    // 0xb70400: ldur            x0, [fp, #-0x40]
    // 0xb70404: r0 = Column()
    //     0xb70404: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb70408: mov             x1, x0
    // 0xb7040c: r0 = Instance_Axis
    //     0xb7040c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb70410: stur            x1, [fp, #-8]
    // 0xb70414: StoreField: r1->field_f = r0
    //     0xb70414: stur            w0, [x1, #0xf]
    // 0xb70418: r0 = Instance_MainAxisAlignment
    //     0xb70418: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7041c: ldr             x0, [x0, #0xa08]
    // 0xb70420: StoreField: r1->field_13 = r0
    //     0xb70420: stur            w0, [x1, #0x13]
    // 0xb70424: r0 = Instance_MainAxisSize
    //     0xb70424: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb70428: ldr             x0, [x0, #0xdd0]
    // 0xb7042c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7042c: stur            w0, [x1, #0x17]
    // 0xb70430: r0 = Instance_CrossAxisAlignment
    //     0xb70430: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb70434: ldr             x0, [x0, #0x890]
    // 0xb70438: StoreField: r1->field_1b = r0
    //     0xb70438: stur            w0, [x1, #0x1b]
    // 0xb7043c: r0 = Instance_VerticalDirection
    //     0xb7043c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb70440: ldr             x0, [x0, #0xa20]
    // 0xb70444: StoreField: r1->field_23 = r0
    //     0xb70444: stur            w0, [x1, #0x23]
    // 0xb70448: r0 = Instance_Clip
    //     0xb70448: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7044c: ldr             x0, [x0, #0x38]
    // 0xb70450: StoreField: r1->field_2b = r0
    //     0xb70450: stur            w0, [x1, #0x2b]
    // 0xb70454: StoreField: r1->field_2f = rZR
    //     0xb70454: stur            xzr, [x1, #0x2f]
    // 0xb70458: ldur            x0, [fp, #-0x10]
    // 0xb7045c: StoreField: r1->field_b = r0
    //     0xb7045c: stur            w0, [x1, #0xb]
    // 0xb70460: r0 = Padding()
    //     0xb70460: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb70464: mov             x1, x0
    // 0xb70468: r0 = Instance_EdgeInsets
    //     0xb70468: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xb7046c: ldr             x0, [x0, #0x560]
    // 0xb70470: stur            x1, [fp, #-0x10]
    // 0xb70474: StoreField: r1->field_f = r0
    //     0xb70474: stur            w0, [x1, #0xf]
    // 0xb70478: ldur            x0, [fp, #-8]
    // 0xb7047c: StoreField: r1->field_b = r0
    //     0xb7047c: stur            w0, [x1, #0xb]
    // 0xb70480: r0 = Card()
    //     0xb70480: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb70484: mov             x1, x0
    // 0xb70488: ldur            x0, [fp, #-0x20]
    // 0xb7048c: stur            x1, [fp, #-8]
    // 0xb70490: StoreField: r1->field_b = r0
    //     0xb70490: stur            w0, [x1, #0xb]
    // 0xb70494: r0 = 0.000000
    //     0xb70494: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb70498: ArrayStore: r1[0] = r0  ; List_4
    //     0xb70498: stur            w0, [x1, #0x17]
    // 0xb7049c: ldur            x0, [fp, #-0x40]
    // 0xb704a0: StoreField: r1->field_1b = r0
    //     0xb704a0: stur            w0, [x1, #0x1b]
    // 0xb704a4: r0 = true
    //     0xb704a4: add             x0, NULL, #0x20  ; true
    // 0xb704a8: StoreField: r1->field_1f = r0
    //     0xb704a8: stur            w0, [x1, #0x1f]
    // 0xb704ac: ldur            x2, [fp, #-0x10]
    // 0xb704b0: StoreField: r1->field_2f = r2
    //     0xb704b0: stur            w2, [x1, #0x2f]
    // 0xb704b4: StoreField: r1->field_2b = r0
    //     0xb704b4: stur            w0, [x1, #0x2b]
    // 0xb704b8: r0 = Instance__CardVariant
    //     0xb704b8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb704bc: ldr             x0, [x0, #0xa68]
    // 0xb704c0: StoreField: r1->field_33 = r0
    //     0xb704c0: stur            w0, [x1, #0x33]
    // 0xb704c4: r0 = Container()
    //     0xb704c4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb704c8: stur            x0, [fp, #-0x10]
    // 0xb704cc: r16 = Instance_EdgeInsets
    //     0xb704cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb704d0: ldr             x16, [x16, #0x980]
    // 0xb704d4: ldur            lr, [fp, #-8]
    // 0xb704d8: stp             lr, x16, [SP]
    // 0xb704dc: mov             x1, x0
    // 0xb704e0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb704e0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb704e4: ldr             x4, [x4, #0x30]
    // 0xb704e8: r0 = Container()
    //     0xb704e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb704ec: r0 = AnimatedContainer()
    //     0xb704ec: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb704f0: stur            x0, [fp, #-8]
    // 0xb704f4: r16 = Instance_Cubic
    //     0xb704f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb704f8: ldr             x16, [x16, #0xaf8]
    // 0xb704fc: str             x16, [SP]
    // 0xb70500: mov             x1, x0
    // 0xb70504: ldur            x2, [fp, #-0x10]
    // 0xb70508: r3 = Instance_Duration
    //     0xb70508: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb7050c: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb7050c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb70510: ldr             x4, [x4, #0xbc8]
    // 0xb70514: r0 = AnimatedContainer()
    //     0xb70514: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb70518: ldur            x0, [fp, #-8]
    // 0xb7051c: LeaveFrame
    //     0xb7051c: mov             SP, fp
    //     0xb70520: ldp             fp, lr, [SP], #0x10
    // 0xb70524: ret
    //     0xb70524: ret             
    // 0xb70528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70528: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7052c: b               #0xb6f5dc
    // 0xb70530: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70530: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70534: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70534: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70538: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7053c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7053c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70540: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7054c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7054c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70550: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70550: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70554: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70554: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70558: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7055c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7055c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb70560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb70560: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildImagesRowWithMore(/* No info */) {
    // ** addr: 0xb70564, size: 0x490
    // 0xb70564: EnterFrame
    //     0xb70564: stp             fp, lr, [SP, #-0x10]!
    //     0xb70568: mov             fp, SP
    // 0xb7056c: AllocStack(0x58)
    //     0xb7056c: sub             SP, SP, #0x58
    // 0xb70570: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb70570: stur            x1, [fp, #-8]
    //     0xb70574: stur            x2, [fp, #-0x10]
    //     0xb70578: stur            x3, [fp, #-0x18]
    // 0xb7057c: CheckStackOverflow
    //     0xb7057c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70580: cmp             SP, x16
    //     0xb70584: b.ls            #0xb709ec
    // 0xb70588: r1 = 3
    //     0xb70588: movz            x1, #0x3
    // 0xb7058c: r0 = AllocateContext()
    //     0xb7058c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb70590: mov             x4, x0
    // 0xb70594: ldur            x0, [fp, #-8]
    // 0xb70598: stur            x4, [fp, #-0x20]
    // 0xb7059c: StoreField: r4->field_f = r0
    //     0xb7059c: stur            w0, [x4, #0xf]
    // 0xb705a0: ldur            x2, [fp, #-0x10]
    // 0xb705a4: StoreField: r4->field_13 = r2
    //     0xb705a4: stur            w2, [x4, #0x13]
    // 0xb705a8: ldur            x1, [fp, #-0x18]
    // 0xb705ac: ArrayStore: r4[0] = r1  ; List_4
    //     0xb705ac: stur            w1, [x4, #0x17]
    // 0xb705b0: mov             x1, x0
    // 0xb705b4: r3 = 0
    //     0xb705b4: movz            x3, #0
    // 0xb705b8: r0 = _buildImageThumbnail()
    //     0xb705b8: bl              #0xb709f4  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xb705bc: stur            x0, [fp, #-0x10]
    // 0xb705c0: r0 = Padding()
    //     0xb705c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb705c4: mov             x1, x0
    // 0xb705c8: r0 = Instance_EdgeInsets
    //     0xb705c8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb705cc: ldr             x0, [x0, #0x550]
    // 0xb705d0: stur            x1, [fp, #-0x18]
    // 0xb705d4: StoreField: r1->field_f = r0
    //     0xb705d4: stur            w0, [x1, #0xf]
    // 0xb705d8: ldur            x2, [fp, #-0x10]
    // 0xb705dc: StoreField: r1->field_b = r2
    //     0xb705dc: stur            w2, [x1, #0xb]
    // 0xb705e0: r0 = GestureDetector()
    //     0xb705e0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb705e4: ldur            x2, [fp, #-0x20]
    // 0xb705e8: r1 = Function '<anonymous closure>':.
    //     0xb705e8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b78] AnonymousClosure: (0xb70cf8), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xb70564)
    //     0xb705ec: ldr             x1, [x1, #0xb78]
    // 0xb705f0: stur            x0, [fp, #-0x10]
    // 0xb705f4: r0 = AllocateClosure()
    //     0xb705f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb705f8: ldur            x16, [fp, #-0x18]
    // 0xb705fc: stp             x16, x0, [SP]
    // 0xb70600: ldur            x1, [fp, #-0x10]
    // 0xb70604: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb70604: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb70608: ldr             x4, [x4, #0xaf0]
    // 0xb7060c: r0 = GestureDetector()
    //     0xb7060c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb70610: ldur            x0, [fp, #-0x20]
    // 0xb70614: LoadField: r2 = r0->field_13
    //     0xb70614: ldur            w2, [x0, #0x13]
    // 0xb70618: DecompressPointer r2
    //     0xb70618: add             x2, x2, HEAP, lsl #32
    // 0xb7061c: ldur            x1, [fp, #-8]
    // 0xb70620: r3 = 1
    //     0xb70620: movz            x3, #0x1
    // 0xb70624: r0 = _buildImageThumbnail()
    //     0xb70624: bl              #0xb709f4  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xb70628: stur            x0, [fp, #-8]
    // 0xb7062c: r0 = Padding()
    //     0xb7062c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb70630: mov             x1, x0
    // 0xb70634: r0 = Instance_EdgeInsets
    //     0xb70634: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb70638: ldr             x0, [x0, #0x550]
    // 0xb7063c: stur            x1, [fp, #-0x18]
    // 0xb70640: StoreField: r1->field_f = r0
    //     0xb70640: stur            w0, [x1, #0xf]
    // 0xb70644: ldur            x0, [fp, #-8]
    // 0xb70648: StoreField: r1->field_b = r0
    //     0xb70648: stur            w0, [x1, #0xb]
    // 0xb7064c: r0 = GestureDetector()
    //     0xb7064c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb70650: ldur            x2, [fp, #-0x20]
    // 0xb70654: r1 = Function '<anonymous closure>':.
    //     0xb70654: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b80] AnonymousClosure: (0xb70c9c), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xb70564)
    //     0xb70658: ldr             x1, [x1, #0xb80]
    // 0xb7065c: stur            x0, [fp, #-8]
    // 0xb70660: r0 = AllocateClosure()
    //     0xb70660: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70664: ldur            x16, [fp, #-0x18]
    // 0xb70668: stp             x16, x0, [SP]
    // 0xb7066c: ldur            x1, [fp, #-8]
    // 0xb70670: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb70670: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb70674: ldr             x4, [x4, #0xaf0]
    // 0xb70678: r0 = GestureDetector()
    //     0xb70678: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb7067c: r1 = Instance_Color
    //     0xb7067c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb70680: d0 = 0.030000
    //     0xb70680: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb70684: ldr             d0, [x17, #0x238]
    // 0xb70688: r0 = withOpacity()
    //     0xb70688: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb7068c: stur            x0, [fp, #-0x18]
    // 0xb70690: r0 = Radius()
    //     0xb70690: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb70694: d0 = 10.000000
    //     0xb70694: fmov            d0, #10.00000000
    // 0xb70698: stur            x0, [fp, #-0x28]
    // 0xb7069c: StoreField: r0->field_7 = d0
    //     0xb7069c: stur            d0, [x0, #7]
    // 0xb706a0: StoreField: r0->field_f = d0
    //     0xb706a0: stur            d0, [x0, #0xf]
    // 0xb706a4: r0 = BorderRadius()
    //     0xb706a4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb706a8: mov             x1, x0
    // 0xb706ac: ldur            x0, [fp, #-0x28]
    // 0xb706b0: stur            x1, [fp, #-0x30]
    // 0xb706b4: StoreField: r1->field_7 = r0
    //     0xb706b4: stur            w0, [x1, #7]
    // 0xb706b8: StoreField: r1->field_b = r0
    //     0xb706b8: stur            w0, [x1, #0xb]
    // 0xb706bc: StoreField: r1->field_f = r0
    //     0xb706bc: stur            w0, [x1, #0xf]
    // 0xb706c0: StoreField: r1->field_13 = r0
    //     0xb706c0: stur            w0, [x1, #0x13]
    // 0xb706c4: r0 = BoxDecoration()
    //     0xb706c4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb706c8: mov             x3, x0
    // 0xb706cc: ldur            x0, [fp, #-0x18]
    // 0xb706d0: stur            x3, [fp, #-0x28]
    // 0xb706d4: StoreField: r3->field_7 = r0
    //     0xb706d4: stur            w0, [x3, #7]
    // 0xb706d8: ldur            x0, [fp, #-0x30]
    // 0xb706dc: StoreField: r3->field_13 = r0
    //     0xb706dc: stur            w0, [x3, #0x13]
    // 0xb706e0: r0 = Instance_BoxShape
    //     0xb706e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb706e4: ldr             x0, [x0, #0x80]
    // 0xb706e8: StoreField: r3->field_23 = r0
    //     0xb706e8: stur            w0, [x3, #0x23]
    // 0xb706ec: r1 = Null
    //     0xb706ec: mov             x1, NULL
    // 0xb706f0: r2 = 4
    //     0xb706f0: movz            x2, #0x4
    // 0xb706f4: r0 = AllocateArray()
    //     0xb706f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb706f8: mov             x2, x0
    // 0xb706fc: r16 = "+"
    //     0xb706fc: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xb70700: StoreField: r2->field_f = r16
    //     0xb70700: stur            w16, [x2, #0xf]
    // 0xb70704: ldur            x3, [fp, #-0x20]
    // 0xb70708: LoadField: r0 = r3->field_13
    //     0xb70708: ldur            w0, [x3, #0x13]
    // 0xb7070c: DecompressPointer r0
    //     0xb7070c: add             x0, x0, HEAP, lsl #32
    // 0xb70710: LoadField: r1 = r0->field_ab
    //     0xb70710: ldur            w1, [x0, #0xab]
    // 0xb70714: DecompressPointer r1
    //     0xb70714: add             x1, x1, HEAP, lsl #32
    // 0xb70718: cmp             w1, NULL
    // 0xb7071c: b.ne            #0xb70728
    // 0xb70720: r0 = Null
    //     0xb70720: mov             x0, NULL
    // 0xb70724: b               #0xb7072c
    // 0xb70728: LoadField: r0 = r1->field_b
    //     0xb70728: ldur            w0, [x1, #0xb]
    // 0xb7072c: cmp             w0, NULL
    // 0xb70730: b.ne            #0xb7073c
    // 0xb70734: r0 = 0
    //     0xb70734: movz            x0, #0
    // 0xb70738: b               #0xb70744
    // 0xb7073c: r1 = LoadInt32Instr(r0)
    //     0xb7073c: sbfx            x1, x0, #1, #0x1f
    // 0xb70740: mov             x0, x1
    // 0xb70744: ldur            x5, [fp, #-0x10]
    // 0xb70748: ldur            x4, [fp, #-8]
    // 0xb7074c: sub             x6, x0, #2
    // 0xb70750: r0 = BoxInt64Instr(r6)
    //     0xb70750: sbfiz           x0, x6, #1, #0x1f
    //     0xb70754: cmp             x6, x0, asr #1
    //     0xb70758: b.eq            #0xb70764
    //     0xb7075c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb70760: stur            x6, [x0, #7]
    // 0xb70764: StoreField: r2->field_13 = r0
    //     0xb70764: stur            w0, [x2, #0x13]
    // 0xb70768: str             x2, [SP]
    // 0xb7076c: r0 = _interpolate()
    //     0xb7076c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb70770: ldur            x2, [fp, #-0x20]
    // 0xb70774: stur            x0, [fp, #-0x18]
    // 0xb70778: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb70778: ldur            w1, [x2, #0x17]
    // 0xb7077c: DecompressPointer r1
    //     0xb7077c: add             x1, x1, HEAP, lsl #32
    // 0xb70780: r0 = of()
    //     0xb70780: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb70784: LoadField: r1 = r0->field_87
    //     0xb70784: ldur            w1, [x0, #0x87]
    // 0xb70788: DecompressPointer r1
    //     0xb70788: add             x1, x1, HEAP, lsl #32
    // 0xb7078c: LoadField: r0 = r1->field_2b
    //     0xb7078c: ldur            w0, [x1, #0x2b]
    // 0xb70790: DecompressPointer r0
    //     0xb70790: add             x0, x0, HEAP, lsl #32
    // 0xb70794: r16 = 12.000000
    //     0xb70794: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb70798: ldr             x16, [x16, #0x9e8]
    // 0xb7079c: r30 = Instance_Color
    //     0xb7079c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb707a0: stp             lr, x16, [SP]
    // 0xb707a4: mov             x1, x0
    // 0xb707a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb707a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb707ac: ldr             x4, [x4, #0xaa0]
    // 0xb707b0: r0 = copyWith()
    //     0xb707b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb707b4: stur            x0, [fp, #-0x30]
    // 0xb707b8: r0 = Text()
    //     0xb707b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb707bc: mov             x2, x0
    // 0xb707c0: ldur            x0, [fp, #-0x18]
    // 0xb707c4: stur            x2, [fp, #-0x38]
    // 0xb707c8: StoreField: r2->field_b = r0
    //     0xb707c8: stur            w0, [x2, #0xb]
    // 0xb707cc: ldur            x0, [fp, #-0x30]
    // 0xb707d0: StoreField: r2->field_13 = r0
    //     0xb707d0: stur            w0, [x2, #0x13]
    // 0xb707d4: ldur            x0, [fp, #-0x20]
    // 0xb707d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb707d8: ldur            w1, [x0, #0x17]
    // 0xb707dc: DecompressPointer r1
    //     0xb707dc: add             x1, x1, HEAP, lsl #32
    // 0xb707e0: r0 = of()
    //     0xb707e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb707e4: LoadField: r1 = r0->field_87
    //     0xb707e4: ldur            w1, [x0, #0x87]
    // 0xb707e8: DecompressPointer r1
    //     0xb707e8: add             x1, x1, HEAP, lsl #32
    // 0xb707ec: LoadField: r0 = r1->field_2b
    //     0xb707ec: ldur            w0, [x1, #0x2b]
    // 0xb707f0: DecompressPointer r0
    //     0xb707f0: add             x0, x0, HEAP, lsl #32
    // 0xb707f4: r16 = 12.000000
    //     0xb707f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb707f8: ldr             x16, [x16, #0x9e8]
    // 0xb707fc: r30 = Instance_Color
    //     0xb707fc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb70800: stp             lr, x16, [SP]
    // 0xb70804: mov             x1, x0
    // 0xb70808: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb70808: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7080c: ldr             x4, [x4, #0xaa0]
    // 0xb70810: r0 = copyWith()
    //     0xb70810: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb70814: stur            x0, [fp, #-0x18]
    // 0xb70818: r0 = Text()
    //     0xb70818: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7081c: mov             x3, x0
    // 0xb70820: r0 = "Photos"
    //     0xb70820: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xb70824: ldr             x0, [x0, #0x260]
    // 0xb70828: stur            x3, [fp, #-0x30]
    // 0xb7082c: StoreField: r3->field_b = r0
    //     0xb7082c: stur            w0, [x3, #0xb]
    // 0xb70830: ldur            x0, [fp, #-0x18]
    // 0xb70834: StoreField: r3->field_13 = r0
    //     0xb70834: stur            w0, [x3, #0x13]
    // 0xb70838: r1 = Null
    //     0xb70838: mov             x1, NULL
    // 0xb7083c: r2 = 4
    //     0xb7083c: movz            x2, #0x4
    // 0xb70840: r0 = AllocateArray()
    //     0xb70840: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb70844: mov             x2, x0
    // 0xb70848: ldur            x0, [fp, #-0x38]
    // 0xb7084c: stur            x2, [fp, #-0x18]
    // 0xb70850: StoreField: r2->field_f = r0
    //     0xb70850: stur            w0, [x2, #0xf]
    // 0xb70854: ldur            x0, [fp, #-0x30]
    // 0xb70858: StoreField: r2->field_13 = r0
    //     0xb70858: stur            w0, [x2, #0x13]
    // 0xb7085c: r1 = <Widget>
    //     0xb7085c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb70860: r0 = AllocateGrowableArray()
    //     0xb70860: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb70864: mov             x1, x0
    // 0xb70868: ldur            x0, [fp, #-0x18]
    // 0xb7086c: stur            x1, [fp, #-0x30]
    // 0xb70870: StoreField: r1->field_f = r0
    //     0xb70870: stur            w0, [x1, #0xf]
    // 0xb70874: r0 = 4
    //     0xb70874: movz            x0, #0x4
    // 0xb70878: StoreField: r1->field_b = r0
    //     0xb70878: stur            w0, [x1, #0xb]
    // 0xb7087c: r0 = Column()
    //     0xb7087c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb70880: mov             x1, x0
    // 0xb70884: r0 = Instance_Axis
    //     0xb70884: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb70888: stur            x1, [fp, #-0x18]
    // 0xb7088c: StoreField: r1->field_f = r0
    //     0xb7088c: stur            w0, [x1, #0xf]
    // 0xb70890: r0 = Instance_MainAxisAlignment
    //     0xb70890: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb70894: ldr             x0, [x0, #0xab0]
    // 0xb70898: StoreField: r1->field_13 = r0
    //     0xb70898: stur            w0, [x1, #0x13]
    // 0xb7089c: r0 = Instance_MainAxisSize
    //     0xb7089c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb708a0: ldr             x0, [x0, #0xa10]
    // 0xb708a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb708a4: stur            w0, [x1, #0x17]
    // 0xb708a8: r2 = Instance_CrossAxisAlignment
    //     0xb708a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb708ac: ldr             x2, [x2, #0xa18]
    // 0xb708b0: StoreField: r1->field_1b = r2
    //     0xb708b0: stur            w2, [x1, #0x1b]
    // 0xb708b4: r3 = Instance_VerticalDirection
    //     0xb708b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb708b8: ldr             x3, [x3, #0xa20]
    // 0xb708bc: StoreField: r1->field_23 = r3
    //     0xb708bc: stur            w3, [x1, #0x23]
    // 0xb708c0: r4 = Instance_Clip
    //     0xb708c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb708c4: ldr             x4, [x4, #0x38]
    // 0xb708c8: StoreField: r1->field_2b = r4
    //     0xb708c8: stur            w4, [x1, #0x2b]
    // 0xb708cc: StoreField: r1->field_2f = rZR
    //     0xb708cc: stur            xzr, [x1, #0x2f]
    // 0xb708d0: ldur            x5, [fp, #-0x30]
    // 0xb708d4: StoreField: r1->field_b = r5
    //     0xb708d4: stur            w5, [x1, #0xb]
    // 0xb708d8: r0 = Container()
    //     0xb708d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb708dc: stur            x0, [fp, #-0x30]
    // 0xb708e0: r16 = 64.000000
    //     0xb708e0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb708e4: ldr             x16, [x16, #0x838]
    // 0xb708e8: r30 = 64.000000
    //     0xb708e8: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb708ec: ldr             lr, [lr, #0x838]
    // 0xb708f0: stp             lr, x16, [SP, #0x10]
    // 0xb708f4: ldur            x16, [fp, #-0x28]
    // 0xb708f8: ldur            lr, [fp, #-0x18]
    // 0xb708fc: stp             lr, x16, [SP]
    // 0xb70900: mov             x1, x0
    // 0xb70904: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb70904: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb70908: ldr             x4, [x4, #0x870]
    // 0xb7090c: r0 = Container()
    //     0xb7090c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb70910: r0 = GestureDetector()
    //     0xb70910: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb70914: ldur            x2, [fp, #-0x20]
    // 0xb70918: r1 = Function '<anonymous closure>':.
    //     0xb70918: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b88] AnonymousClosure: (0xb70b0c), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xb70564)
    //     0xb7091c: ldr             x1, [x1, #0xb88]
    // 0xb70920: stur            x0, [fp, #-0x18]
    // 0xb70924: r0 = AllocateClosure()
    //     0xb70924: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70928: ldur            x16, [fp, #-0x30]
    // 0xb7092c: stp             x16, x0, [SP]
    // 0xb70930: ldur            x1, [fp, #-0x18]
    // 0xb70934: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb70934: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb70938: ldr             x4, [x4, #0xaf0]
    // 0xb7093c: r0 = GestureDetector()
    //     0xb7093c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb70940: r1 = Null
    //     0xb70940: mov             x1, NULL
    // 0xb70944: r2 = 6
    //     0xb70944: movz            x2, #0x6
    // 0xb70948: r0 = AllocateArray()
    //     0xb70948: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7094c: mov             x2, x0
    // 0xb70950: ldur            x0, [fp, #-0x10]
    // 0xb70954: stur            x2, [fp, #-0x20]
    // 0xb70958: StoreField: r2->field_f = r0
    //     0xb70958: stur            w0, [x2, #0xf]
    // 0xb7095c: ldur            x0, [fp, #-8]
    // 0xb70960: StoreField: r2->field_13 = r0
    //     0xb70960: stur            w0, [x2, #0x13]
    // 0xb70964: ldur            x0, [fp, #-0x18]
    // 0xb70968: ArrayStore: r2[0] = r0  ; List_4
    //     0xb70968: stur            w0, [x2, #0x17]
    // 0xb7096c: r1 = <Widget>
    //     0xb7096c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb70970: r0 = AllocateGrowableArray()
    //     0xb70970: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb70974: mov             x1, x0
    // 0xb70978: ldur            x0, [fp, #-0x20]
    // 0xb7097c: stur            x1, [fp, #-8]
    // 0xb70980: StoreField: r1->field_f = r0
    //     0xb70980: stur            w0, [x1, #0xf]
    // 0xb70984: r0 = 6
    //     0xb70984: movz            x0, #0x6
    // 0xb70988: StoreField: r1->field_b = r0
    //     0xb70988: stur            w0, [x1, #0xb]
    // 0xb7098c: r0 = Row()
    //     0xb7098c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb70990: r1 = Instance_Axis
    //     0xb70990: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb70994: StoreField: r0->field_f = r1
    //     0xb70994: stur            w1, [x0, #0xf]
    // 0xb70998: r1 = Instance_MainAxisAlignment
    //     0xb70998: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7099c: ldr             x1, [x1, #0xa08]
    // 0xb709a0: StoreField: r0->field_13 = r1
    //     0xb709a0: stur            w1, [x0, #0x13]
    // 0xb709a4: r1 = Instance_MainAxisSize
    //     0xb709a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb709a8: ldr             x1, [x1, #0xa10]
    // 0xb709ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xb709ac: stur            w1, [x0, #0x17]
    // 0xb709b0: r1 = Instance_CrossAxisAlignment
    //     0xb709b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb709b4: ldr             x1, [x1, #0xa18]
    // 0xb709b8: StoreField: r0->field_1b = r1
    //     0xb709b8: stur            w1, [x0, #0x1b]
    // 0xb709bc: r1 = Instance_VerticalDirection
    //     0xb709bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb709c0: ldr             x1, [x1, #0xa20]
    // 0xb709c4: StoreField: r0->field_23 = r1
    //     0xb709c4: stur            w1, [x0, #0x23]
    // 0xb709c8: r1 = Instance_Clip
    //     0xb709c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb709cc: ldr             x1, [x1, #0x38]
    // 0xb709d0: StoreField: r0->field_2b = r1
    //     0xb709d0: stur            w1, [x0, #0x2b]
    // 0xb709d4: StoreField: r0->field_2f = rZR
    //     0xb709d4: stur            xzr, [x0, #0x2f]
    // 0xb709d8: ldur            x1, [fp, #-8]
    // 0xb709dc: StoreField: r0->field_b = r1
    //     0xb709dc: stur            w1, [x0, #0xb]
    // 0xb709e0: LeaveFrame
    //     0xb709e0: mov             SP, fp
    //     0xb709e4: ldp             fp, lr, [SP], #0x10
    // 0xb709e8: ret
    //     0xb709e8: ret             
    // 0xb709ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb709ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb709f0: b               #0xb70588
  }
  _ _buildImageThumbnail(/* No info */) {
    // ** addr: 0xb709f4, size: 0x118
    // 0xb709f4: EnterFrame
    //     0xb709f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb709f8: mov             fp, SP
    // 0xb709fc: AllocStack(0x40)
    //     0xb709fc: sub             SP, SP, #0x40
    // 0xb70a00: SetupParameters(dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */)
    //     0xb70a00: mov             x0, x2
    //     0xb70a04: mov             x2, x3
    // 0xb70a08: CheckStackOverflow
    //     0xb70a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70a0c: cmp             SP, x16
    //     0xb70a10: b.ls            #0xb70b00
    // 0xb70a14: LoadField: r3 = r0->field_ab
    //     0xb70a14: ldur            w3, [x0, #0xab]
    // 0xb70a18: DecompressPointer r3
    //     0xb70a18: add             x3, x3, HEAP, lsl #32
    // 0xb70a1c: cmp             w3, NULL
    // 0xb70a20: b.ne            #0xb70a2c
    // 0xb70a24: r0 = Null
    //     0xb70a24: mov             x0, NULL
    // 0xb70a28: b               #0xb70a7c
    // 0xb70a2c: LoadField: r0 = r3->field_b
    //     0xb70a2c: ldur            w0, [x3, #0xb]
    // 0xb70a30: r1 = LoadInt32Instr(r0)
    //     0xb70a30: sbfx            x1, x0, #1, #0x1f
    // 0xb70a34: mov             x0, x1
    // 0xb70a38: mov             x1, x2
    // 0xb70a3c: cmp             x1, x0
    // 0xb70a40: b.hs            #0xb70b08
    // 0xb70a44: LoadField: r0 = r3->field_f
    //     0xb70a44: ldur            w0, [x3, #0xf]
    // 0xb70a48: DecompressPointer r0
    //     0xb70a48: add             x0, x0, HEAP, lsl #32
    // 0xb70a4c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb70a4c: add             x16, x0, x2, lsl #2
    //     0xb70a50: ldur            w1, [x16, #0xf]
    // 0xb70a54: DecompressPointer r1
    //     0xb70a54: add             x1, x1, HEAP, lsl #32
    // 0xb70a58: LoadField: r0 = r1->field_7
    //     0xb70a58: ldur            w0, [x1, #7]
    // 0xb70a5c: DecompressPointer r0
    //     0xb70a5c: add             x0, x0, HEAP, lsl #32
    // 0xb70a60: cmp             w0, NULL
    // 0xb70a64: b.ne            #0xb70a70
    // 0xb70a68: r0 = Null
    //     0xb70a68: mov             x0, NULL
    // 0xb70a6c: b               #0xb70a7c
    // 0xb70a70: LoadField: r1 = r0->field_b
    //     0xb70a70: ldur            w1, [x0, #0xb]
    // 0xb70a74: DecompressPointer r1
    //     0xb70a74: add             x1, x1, HEAP, lsl #32
    // 0xb70a78: mov             x0, x1
    // 0xb70a7c: cmp             w0, NULL
    // 0xb70a80: b.ne            #0xb70a88
    // 0xb70a84: r0 = ""
    //     0xb70a84: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb70a88: stur            x0, [fp, #-8]
    // 0xb70a8c: r1 = Function '<anonymous closure>':.
    //     0xb70a8c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b98] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb70a90: ldr             x1, [x1, #0xb98]
    // 0xb70a94: r2 = Null
    //     0xb70a94: mov             x2, NULL
    // 0xb70a98: r0 = AllocateClosure()
    //     0xb70a98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70a9c: r1 = Function '<anonymous closure>':.
    //     0xb70a9c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ba0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb70aa0: ldr             x1, [x1, #0xba0]
    // 0xb70aa4: r2 = Null
    //     0xb70aa4: mov             x2, NULL
    // 0xb70aa8: stur            x0, [fp, #-0x10]
    // 0xb70aac: r0 = AllocateClosure()
    //     0xb70aac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70ab0: stur            x0, [fp, #-0x18]
    // 0xb70ab4: r0 = CachedNetworkImage()
    //     0xb70ab4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb70ab8: stur            x0, [fp, #-0x20]
    // 0xb70abc: r16 = 64.000000
    //     0xb70abc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb70ac0: ldr             x16, [x16, #0x838]
    // 0xb70ac4: r30 = 64.000000
    //     0xb70ac4: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb70ac8: ldr             lr, [lr, #0x838]
    // 0xb70acc: stp             lr, x16, [SP, #0x10]
    // 0xb70ad0: ldur            x16, [fp, #-0x10]
    // 0xb70ad4: ldur            lr, [fp, #-0x18]
    // 0xb70ad8: stp             lr, x16, [SP]
    // 0xb70adc: mov             x1, x0
    // 0xb70ae0: ldur            x2, [fp, #-8]
    // 0xb70ae4: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb70ae4: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb70ae8: ldr             x4, [x4, #0x388]
    // 0xb70aec: r0 = CachedNetworkImage()
    //     0xb70aec: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb70af0: ldur            x0, [fp, #-0x20]
    // 0xb70af4: LeaveFrame
    //     0xb70af4: mov             SP, fp
    //     0xb70af8: ldp             fp, lr, [SP], #0x10
    // 0xb70afc: ret
    //     0xb70afc: ret             
    // 0xb70b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70b00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70b04: b               #0xb70a14
    // 0xb70b08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb70b08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb70b0c, size: 0x5c
    // 0xb70b0c: EnterFrame
    //     0xb70b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb70b10: mov             fp, SP
    // 0xb70b14: ldr             x0, [fp, #0x10]
    // 0xb70b18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb70b18: ldur            w1, [x0, #0x17]
    // 0xb70b1c: DecompressPointer r1
    //     0xb70b1c: add             x1, x1, HEAP, lsl #32
    // 0xb70b20: CheckStackOverflow
    //     0xb70b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70b24: cmp             SP, x16
    //     0xb70b28: b.ls            #0xb70b60
    // 0xb70b2c: LoadField: r0 = r1->field_f
    //     0xb70b2c: ldur            w0, [x1, #0xf]
    // 0xb70b30: DecompressPointer r0
    //     0xb70b30: add             x0, x0, HEAP, lsl #32
    // 0xb70b34: LoadField: r2 = r1->field_13
    //     0xb70b34: ldur            w2, [x1, #0x13]
    // 0xb70b38: DecompressPointer r2
    //     0xb70b38: add             x2, x2, HEAP, lsl #32
    // 0xb70b3c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb70b3c: ldur            w5, [x1, #0x17]
    // 0xb70b40: DecompressPointer r5
    //     0xb70b40: add             x5, x5, HEAP, lsl #32
    // 0xb70b44: mov             x1, x0
    // 0xb70b48: r3 = 2
    //     0xb70b48: movz            x3, #0x2
    // 0xb70b4c: r0 = _openImageViewer()
    //     0xb70b4c: bl              #0xb70b68  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xb70b50: r0 = Null
    //     0xb70b50: mov             x0, NULL
    // 0xb70b54: LeaveFrame
    //     0xb70b54: mov             SP, fp
    //     0xb70b58: ldp             fp, lr, [SP], #0x10
    // 0xb70b5c: ret
    //     0xb70b5c: ret             
    // 0xb70b60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70b60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70b64: b               #0xb70b2c
  }
  _ _openImageViewer(/* No info */) {
    // ** addr: 0xb70b68, size: 0xd0
    // 0xb70b68: EnterFrame
    //     0xb70b68: stp             fp, lr, [SP, #-0x10]!
    //     0xb70b6c: mov             fp, SP
    // 0xb70b70: AllocStack(0x38)
    //     0xb70b70: sub             SP, SP, #0x38
    // 0xb70b74: SetupParameters(_TestimonialCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1, fp-0x18 */)
    //     0xb70b74: mov             x0, x1
    //     0xb70b78: mov             x1, x5
    //     0xb70b7c: stur            x2, [fp, #-8]
    //     0xb70b80: stur            x3, [fp, #-0x10]
    //     0xb70b84: stur            x5, [fp, #-0x18]
    // 0xb70b88: CheckStackOverflow
    //     0xb70b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70b8c: cmp             SP, x16
    //     0xb70b90: b.ls            #0xb70c30
    // 0xb70b94: r1 = 2
    //     0xb70b94: movz            x1, #0x2
    // 0xb70b98: r0 = AllocateContext()
    //     0xb70b98: bl              #0x16f6108  ; AllocateContextStub
    // 0xb70b9c: mov             x2, x0
    // 0xb70ba0: ldur            x0, [fp, #-8]
    // 0xb70ba4: stur            x2, [fp, #-0x20]
    // 0xb70ba8: StoreField: r2->field_f = r0
    //     0xb70ba8: stur            w0, [x2, #0xf]
    // 0xb70bac: ldur            x3, [fp, #-0x10]
    // 0xb70bb0: r0 = BoxInt64Instr(r3)
    //     0xb70bb0: sbfiz           x0, x3, #1, #0x1f
    //     0xb70bb4: cmp             x3, x0, asr #1
    //     0xb70bb8: b.eq            #0xb70bc4
    //     0xb70bbc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb70bc0: stur            x3, [x0, #7]
    // 0xb70bc4: StoreField: r2->field_13 = r0
    //     0xb70bc4: stur            w0, [x2, #0x13]
    // 0xb70bc8: ldur            x1, [fp, #-0x18]
    // 0xb70bcc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb70bcc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb70bd0: r0 = of()
    //     0xb70bd0: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb70bd4: ldur            x2, [fp, #-0x20]
    // 0xb70bd8: r1 = Function '<anonymous closure>':.
    //     0xb70bd8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b90] AnonymousClosure: (0xb70c38), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer (0xb70b68)
    //     0xb70bdc: ldr             x1, [x1, #0xb90]
    // 0xb70be0: stur            x0, [fp, #-8]
    // 0xb70be4: r0 = AllocateClosure()
    //     0xb70be4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70be8: r1 = Null
    //     0xb70be8: mov             x1, NULL
    // 0xb70bec: stur            x0, [fp, #-0x18]
    // 0xb70bf0: r0 = MaterialPageRoute()
    //     0xb70bf0: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb70bf4: mov             x1, x0
    // 0xb70bf8: ldur            x2, [fp, #-0x18]
    // 0xb70bfc: stur            x0, [fp, #-0x18]
    // 0xb70c00: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb70c00: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb70c04: r0 = MaterialPageRoute()
    //     0xb70c04: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb70c08: ldur            x16, [fp, #-8]
    // 0xb70c0c: stp             x16, NULL, [SP, #8]
    // 0xb70c10: ldur            x16, [fp, #-0x18]
    // 0xb70c14: str             x16, [SP]
    // 0xb70c18: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb70c18: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb70c1c: r0 = push()
    //     0xb70c1c: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb70c20: r0 = Null
    //     0xb70c20: mov             x0, NULL
    // 0xb70c24: LeaveFrame
    //     0xb70c24: mov             SP, fp
    //     0xb70c28: ldp             fp, lr, [SP], #0x10
    // 0xb70c2c: ret
    //     0xb70c2c: ret             
    // 0xb70c30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70c30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70c34: b               #0xb70b94
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb70c38, size: 0x64
    // 0xb70c38: EnterFrame
    //     0xb70c38: stp             fp, lr, [SP, #-0x10]!
    //     0xb70c3c: mov             fp, SP
    // 0xb70c40: AllocStack(0x10)
    //     0xb70c40: sub             SP, SP, #0x10
    // 0xb70c44: SetupParameters()
    //     0xb70c44: ldr             x0, [fp, #0x18]
    //     0xb70c48: ldur            w1, [x0, #0x17]
    //     0xb70c4c: add             x1, x1, HEAP, lsl #32
    // 0xb70c50: LoadField: r0 = r1->field_13
    //     0xb70c50: ldur            w0, [x1, #0x13]
    // 0xb70c54: DecompressPointer r0
    //     0xb70c54: add             x0, x0, HEAP, lsl #32
    // 0xb70c58: stur            x0, [fp, #-0x10]
    // 0xb70c5c: LoadField: r2 = r1->field_f
    //     0xb70c5c: ldur            w2, [x1, #0xf]
    // 0xb70c60: DecompressPointer r2
    //     0xb70c60: add             x2, x2, HEAP, lsl #32
    // 0xb70c64: LoadField: r1 = r2->field_ab
    //     0xb70c64: ldur            w1, [x2, #0xab]
    // 0xb70c68: DecompressPointer r1
    //     0xb70c68: add             x1, x1, HEAP, lsl #32
    // 0xb70c6c: stur            x1, [fp, #-8]
    // 0xb70c70: r0 = TestimonialMoreImagesWidget()
    //     0xb70c70: bl              #0xb20434  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb70c74: ldur            x1, [fp, #-8]
    // 0xb70c78: StoreField: r0->field_b = r1
    //     0xb70c78: stur            w1, [x0, #0xb]
    // 0xb70c7c: ldur            x1, [fp, #-0x10]
    // 0xb70c80: r2 = LoadInt32Instr(r1)
    //     0xb70c80: sbfx            x2, x1, #1, #0x1f
    //     0xb70c84: tbz             w1, #0, #0xb70c8c
    //     0xb70c88: ldur            x2, [x1, #7]
    // 0xb70c8c: StoreField: r0->field_f = r2
    //     0xb70c8c: stur            x2, [x0, #0xf]
    // 0xb70c90: LeaveFrame
    //     0xb70c90: mov             SP, fp
    //     0xb70c94: ldp             fp, lr, [SP], #0x10
    // 0xb70c98: ret
    //     0xb70c98: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb70c9c, size: 0x5c
    // 0xb70c9c: EnterFrame
    //     0xb70c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb70ca0: mov             fp, SP
    // 0xb70ca4: ldr             x0, [fp, #0x10]
    // 0xb70ca8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb70ca8: ldur            w1, [x0, #0x17]
    // 0xb70cac: DecompressPointer r1
    //     0xb70cac: add             x1, x1, HEAP, lsl #32
    // 0xb70cb0: CheckStackOverflow
    //     0xb70cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70cb4: cmp             SP, x16
    //     0xb70cb8: b.ls            #0xb70cf0
    // 0xb70cbc: LoadField: r0 = r1->field_f
    //     0xb70cbc: ldur            w0, [x1, #0xf]
    // 0xb70cc0: DecompressPointer r0
    //     0xb70cc0: add             x0, x0, HEAP, lsl #32
    // 0xb70cc4: LoadField: r2 = r1->field_13
    //     0xb70cc4: ldur            w2, [x1, #0x13]
    // 0xb70cc8: DecompressPointer r2
    //     0xb70cc8: add             x2, x2, HEAP, lsl #32
    // 0xb70ccc: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb70ccc: ldur            w5, [x1, #0x17]
    // 0xb70cd0: DecompressPointer r5
    //     0xb70cd0: add             x5, x5, HEAP, lsl #32
    // 0xb70cd4: mov             x1, x0
    // 0xb70cd8: r3 = 1
    //     0xb70cd8: movz            x3, #0x1
    // 0xb70cdc: r0 = _openImageViewer()
    //     0xb70cdc: bl              #0xb70b68  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xb70ce0: r0 = Null
    //     0xb70ce0: mov             x0, NULL
    // 0xb70ce4: LeaveFrame
    //     0xb70ce4: mov             SP, fp
    //     0xb70ce8: ldp             fp, lr, [SP], #0x10
    // 0xb70cec: ret
    //     0xb70cec: ret             
    // 0xb70cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70cf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70cf4: b               #0xb70cbc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb70cf8, size: 0x5c
    // 0xb70cf8: EnterFrame
    //     0xb70cf8: stp             fp, lr, [SP, #-0x10]!
    //     0xb70cfc: mov             fp, SP
    // 0xb70d00: ldr             x0, [fp, #0x10]
    // 0xb70d04: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb70d04: ldur            w1, [x0, #0x17]
    // 0xb70d08: DecompressPointer r1
    //     0xb70d08: add             x1, x1, HEAP, lsl #32
    // 0xb70d0c: CheckStackOverflow
    //     0xb70d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70d10: cmp             SP, x16
    //     0xb70d14: b.ls            #0xb70d4c
    // 0xb70d18: LoadField: r0 = r1->field_f
    //     0xb70d18: ldur            w0, [x1, #0xf]
    // 0xb70d1c: DecompressPointer r0
    //     0xb70d1c: add             x0, x0, HEAP, lsl #32
    // 0xb70d20: LoadField: r2 = r1->field_13
    //     0xb70d20: ldur            w2, [x1, #0x13]
    // 0xb70d24: DecompressPointer r2
    //     0xb70d24: add             x2, x2, HEAP, lsl #32
    // 0xb70d28: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb70d28: ldur            w5, [x1, #0x17]
    // 0xb70d2c: DecompressPointer r5
    //     0xb70d2c: add             x5, x5, HEAP, lsl #32
    // 0xb70d30: mov             x1, x0
    // 0xb70d34: r3 = 0
    //     0xb70d34: movz            x3, #0
    // 0xb70d38: r0 = _openImageViewer()
    //     0xb70d38: bl              #0xb70b68  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xb70d3c: r0 = Null
    //     0xb70d3c: mov             x0, NULL
    // 0xb70d40: LeaveFrame
    //     0xb70d40: mov             SP, fp
    //     0xb70d44: ldp             fp, lr, [SP], #0x10
    // 0xb70d48: ret
    //     0xb70d48: ret             
    // 0xb70d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70d4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70d50: b               #0xb70d18
  }
  _ _buildImagesRow(/* No info */) {
    // ** addr: 0xb70d54, size: 0xdc
    // 0xb70d54: EnterFrame
    //     0xb70d54: stp             fp, lr, [SP, #-0x10]!
    //     0xb70d58: mov             fp, SP
    // 0xb70d5c: AllocStack(0x30)
    //     0xb70d5c: sub             SP, SP, #0x30
    // 0xb70d60: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb70d60: stur            x1, [fp, #-8]
    //     0xb70d64: stur            x2, [fp, #-0x10]
    // 0xb70d68: CheckStackOverflow
    //     0xb70d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70d6c: cmp             SP, x16
    //     0xb70d70: b.ls            #0xb70e28
    // 0xb70d74: r1 = 2
    //     0xb70d74: movz            x1, #0x2
    // 0xb70d78: r0 = AllocateContext()
    //     0xb70d78: bl              #0x16f6108  ; AllocateContextStub
    // 0xb70d7c: mov             x1, x0
    // 0xb70d80: ldur            x0, [fp, #-8]
    // 0xb70d84: StoreField: r1->field_f = r0
    //     0xb70d84: stur            w0, [x1, #0xf]
    // 0xb70d88: ldur            x0, [fp, #-0x10]
    // 0xb70d8c: StoreField: r1->field_13 = r0
    //     0xb70d8c: stur            w0, [x1, #0x13]
    // 0xb70d90: LoadField: r2 = r0->field_ab
    //     0xb70d90: ldur            w2, [x0, #0xab]
    // 0xb70d94: DecompressPointer r2
    //     0xb70d94: add             x2, x2, HEAP, lsl #32
    // 0xb70d98: cmp             w2, NULL
    // 0xb70d9c: b.ne            #0xb70da8
    // 0xb70da0: r0 = Null
    //     0xb70da0: mov             x0, NULL
    // 0xb70da4: b               #0xb70dac
    // 0xb70da8: LoadField: r0 = r2->field_b
    //     0xb70da8: ldur            w0, [x2, #0xb]
    // 0xb70dac: cmp             w0, NULL
    // 0xb70db0: b.ne            #0xb70dbc
    // 0xb70db4: r0 = 0
    //     0xb70db4: movz            x0, #0
    // 0xb70db8: b               #0xb70dc4
    // 0xb70dbc: r2 = LoadInt32Instr(r0)
    //     0xb70dbc: sbfx            x2, x0, #1, #0x1f
    // 0xb70dc0: mov             x0, x2
    // 0xb70dc4: lsl             x3, x0, #1
    // 0xb70dc8: mov             x2, x1
    // 0xb70dcc: stur            x3, [fp, #-8]
    // 0xb70dd0: r1 = Function '<anonymous closure>':.
    //     0xb70dd0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ba8] AnonymousClosure: (0xb70e30), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xb70d54)
    //     0xb70dd4: ldr             x1, [x1, #0xba8]
    // 0xb70dd8: r0 = AllocateClosure()
    //     0xb70dd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70ddc: stur            x0, [fp, #-0x10]
    // 0xb70de0: r0 = ListView()
    //     0xb70de0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb70de4: stur            x0, [fp, #-0x18]
    // 0xb70de8: r16 = true
    //     0xb70de8: add             x16, NULL, #0x20  ; true
    // 0xb70dec: r30 = Instance_Axis
    //     0xb70dec: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb70df0: stp             lr, x16, [SP, #8]
    // 0xb70df4: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb70df4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb70df8: ldr             x16, [x16, #0x1c8]
    // 0xb70dfc: str             x16, [SP]
    // 0xb70e00: mov             x1, x0
    // 0xb70e04: ldur            x2, [fp, #-0x10]
    // 0xb70e08: ldur            x3, [fp, #-8]
    // 0xb70e0c: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb70e0c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52200] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb70e10: ldr             x4, [x4, #0x200]
    // 0xb70e14: r0 = ListView.builder()
    //     0xb70e14: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb70e18: ldur            x0, [fp, #-0x18]
    // 0xb70e1c: LeaveFrame
    //     0xb70e1c: mov             SP, fp
    //     0xb70e20: ldp             fp, lr, [SP], #0x10
    // 0xb70e24: ret
    //     0xb70e24: ret             
    // 0xb70e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70e28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70e2c: b               #0xb70d74
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb70e30, size: 0x1a0
    // 0xb70e30: EnterFrame
    //     0xb70e30: stp             fp, lr, [SP, #-0x10]!
    //     0xb70e34: mov             fp, SP
    // 0xb70e38: AllocStack(0x48)
    //     0xb70e38: sub             SP, SP, #0x48
    // 0xb70e3c: SetupParameters()
    //     0xb70e3c: ldr             x0, [fp, #0x20]
    //     0xb70e40: ldur            w1, [x0, #0x17]
    //     0xb70e44: add             x1, x1, HEAP, lsl #32
    //     0xb70e48: stur            x1, [fp, #-8]
    // 0xb70e4c: CheckStackOverflow
    //     0xb70e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70e50: cmp             SP, x16
    //     0xb70e54: b.ls            #0xb70fc4
    // 0xb70e58: r1 = 2
    //     0xb70e58: movz            x1, #0x2
    // 0xb70e5c: r0 = AllocateContext()
    //     0xb70e5c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb70e60: mov             x3, x0
    // 0xb70e64: ldur            x0, [fp, #-8]
    // 0xb70e68: stur            x3, [fp, #-0x10]
    // 0xb70e6c: StoreField: r3->field_b = r0
    //     0xb70e6c: stur            w0, [x3, #0xb]
    // 0xb70e70: ldr             x1, [fp, #0x18]
    // 0xb70e74: StoreField: r3->field_f = r1
    //     0xb70e74: stur            w1, [x3, #0xf]
    // 0xb70e78: ldr             x1, [fp, #0x10]
    // 0xb70e7c: StoreField: r3->field_13 = r1
    //     0xb70e7c: stur            w1, [x3, #0x13]
    // 0xb70e80: LoadField: r2 = r0->field_13
    //     0xb70e80: ldur            w2, [x0, #0x13]
    // 0xb70e84: DecompressPointer r2
    //     0xb70e84: add             x2, x2, HEAP, lsl #32
    // 0xb70e88: LoadField: r4 = r2->field_ab
    //     0xb70e88: ldur            w4, [x2, #0xab]
    // 0xb70e8c: DecompressPointer r4
    //     0xb70e8c: add             x4, x4, HEAP, lsl #32
    // 0xb70e90: cmp             w4, NULL
    // 0xb70e94: b.ne            #0xb70ea0
    // 0xb70e98: r0 = Null
    //     0xb70e98: mov             x0, NULL
    // 0xb70e9c: b               #0xb70efc
    // 0xb70ea0: LoadField: r0 = r4->field_b
    //     0xb70ea0: ldur            w0, [x4, #0xb]
    // 0xb70ea4: r2 = LoadInt32Instr(r1)
    //     0xb70ea4: sbfx            x2, x1, #1, #0x1f
    //     0xb70ea8: tbz             w1, #0, #0xb70eb0
    //     0xb70eac: ldur            x2, [x1, #7]
    // 0xb70eb0: r1 = LoadInt32Instr(r0)
    //     0xb70eb0: sbfx            x1, x0, #1, #0x1f
    // 0xb70eb4: mov             x0, x1
    // 0xb70eb8: mov             x1, x2
    // 0xb70ebc: cmp             x1, x0
    // 0xb70ec0: b.hs            #0xb70fcc
    // 0xb70ec4: LoadField: r0 = r4->field_f
    //     0xb70ec4: ldur            w0, [x4, #0xf]
    // 0xb70ec8: DecompressPointer r0
    //     0xb70ec8: add             x0, x0, HEAP, lsl #32
    // 0xb70ecc: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb70ecc: add             x16, x0, x2, lsl #2
    //     0xb70ed0: ldur            w1, [x16, #0xf]
    // 0xb70ed4: DecompressPointer r1
    //     0xb70ed4: add             x1, x1, HEAP, lsl #32
    // 0xb70ed8: LoadField: r0 = r1->field_7
    //     0xb70ed8: ldur            w0, [x1, #7]
    // 0xb70edc: DecompressPointer r0
    //     0xb70edc: add             x0, x0, HEAP, lsl #32
    // 0xb70ee0: cmp             w0, NULL
    // 0xb70ee4: b.ne            #0xb70ef0
    // 0xb70ee8: r0 = Null
    //     0xb70ee8: mov             x0, NULL
    // 0xb70eec: b               #0xb70efc
    // 0xb70ef0: LoadField: r1 = r0->field_b
    //     0xb70ef0: ldur            w1, [x0, #0xb]
    // 0xb70ef4: DecompressPointer r1
    //     0xb70ef4: add             x1, x1, HEAP, lsl #32
    // 0xb70ef8: mov             x0, x1
    // 0xb70efc: cmp             w0, NULL
    // 0xb70f00: b.ne            #0xb70f08
    // 0xb70f04: r0 = ""
    //     0xb70f04: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb70f08: stur            x0, [fp, #-8]
    // 0xb70f0c: r1 = Function '<anonymous closure>':.
    //     0xb70f0c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bb0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb70f10: ldr             x1, [x1, #0xbb0]
    // 0xb70f14: r2 = Null
    //     0xb70f14: mov             x2, NULL
    // 0xb70f18: r0 = AllocateClosure()
    //     0xb70f18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70f1c: r1 = Function '<anonymous closure>':.
    //     0xb70f1c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bb8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb70f20: ldr             x1, [x1, #0xbb8]
    // 0xb70f24: r2 = Null
    //     0xb70f24: mov             x2, NULL
    // 0xb70f28: stur            x0, [fp, #-0x18]
    // 0xb70f2c: r0 = AllocateClosure()
    //     0xb70f2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70f30: stur            x0, [fp, #-0x20]
    // 0xb70f34: r0 = CachedNetworkImage()
    //     0xb70f34: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb70f38: stur            x0, [fp, #-0x28]
    // 0xb70f3c: r16 = 64.000000
    //     0xb70f3c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb70f40: ldr             x16, [x16, #0x838]
    // 0xb70f44: r30 = 64.000000
    //     0xb70f44: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb70f48: ldr             lr, [lr, #0x838]
    // 0xb70f4c: stp             lr, x16, [SP, #0x10]
    // 0xb70f50: ldur            x16, [fp, #-0x18]
    // 0xb70f54: ldur            lr, [fp, #-0x20]
    // 0xb70f58: stp             lr, x16, [SP]
    // 0xb70f5c: mov             x1, x0
    // 0xb70f60: ldur            x2, [fp, #-8]
    // 0xb70f64: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb70f64: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb70f68: ldr             x4, [x4, #0x388]
    // 0xb70f6c: r0 = CachedNetworkImage()
    //     0xb70f6c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb70f70: r0 = GestureDetector()
    //     0xb70f70: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb70f74: ldur            x2, [fp, #-0x10]
    // 0xb70f78: r1 = Function '<anonymous closure>':.
    //     0xb70f78: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bc0] AnonymousClosure: (0xb70fd0), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xb70d54)
    //     0xb70f7c: ldr             x1, [x1, #0xbc0]
    // 0xb70f80: stur            x0, [fp, #-8]
    // 0xb70f84: r0 = AllocateClosure()
    //     0xb70f84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb70f88: ldur            x16, [fp, #-0x28]
    // 0xb70f8c: stp             x16, x0, [SP]
    // 0xb70f90: ldur            x1, [fp, #-8]
    // 0xb70f94: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb70f94: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb70f98: ldr             x4, [x4, #0xaf0]
    // 0xb70f9c: r0 = GestureDetector()
    //     0xb70f9c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb70fa0: r0 = Padding()
    //     0xb70fa0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb70fa4: r1 = Instance_EdgeInsets
    //     0xb70fa4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb70fa8: ldr             x1, [x1, #0x550]
    // 0xb70fac: StoreField: r0->field_f = r1
    //     0xb70fac: stur            w1, [x0, #0xf]
    // 0xb70fb0: ldur            x1, [fp, #-8]
    // 0xb70fb4: StoreField: r0->field_b = r1
    //     0xb70fb4: stur            w1, [x0, #0xb]
    // 0xb70fb8: LeaveFrame
    //     0xb70fb8: mov             SP, fp
    //     0xb70fbc: ldp             fp, lr, [SP], #0x10
    // 0xb70fc0: ret
    //     0xb70fc0: ret             
    // 0xb70fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb70fc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb70fc8: b               #0xb70e58
    // 0xb70fcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb70fcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb70fd0, size: 0x88
    // 0xb70fd0: EnterFrame
    //     0xb70fd0: stp             fp, lr, [SP, #-0x10]!
    //     0xb70fd4: mov             fp, SP
    // 0xb70fd8: ldr             x0, [fp, #0x10]
    // 0xb70fdc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb70fdc: ldur            w1, [x0, #0x17]
    // 0xb70fe0: DecompressPointer r1
    //     0xb70fe0: add             x1, x1, HEAP, lsl #32
    // 0xb70fe4: CheckStackOverflow
    //     0xb70fe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb70fe8: cmp             SP, x16
    //     0xb70fec: b.ls            #0xb71050
    // 0xb70ff0: LoadField: r0 = r1->field_b
    //     0xb70ff0: ldur            w0, [x1, #0xb]
    // 0xb70ff4: DecompressPointer r0
    //     0xb70ff4: add             x0, x0, HEAP, lsl #32
    // 0xb70ff8: LoadField: r2 = r0->field_f
    //     0xb70ff8: ldur            w2, [x0, #0xf]
    // 0xb70ffc: DecompressPointer r2
    //     0xb70ffc: add             x2, x2, HEAP, lsl #32
    // 0xb71000: LoadField: r3 = r0->field_13
    //     0xb71000: ldur            w3, [x0, #0x13]
    // 0xb71004: DecompressPointer r3
    //     0xb71004: add             x3, x3, HEAP, lsl #32
    // 0xb71008: LoadField: r0 = r1->field_13
    //     0xb71008: ldur            w0, [x1, #0x13]
    // 0xb7100c: DecompressPointer r0
    //     0xb7100c: add             x0, x0, HEAP, lsl #32
    // 0xb71010: LoadField: r5 = r1->field_f
    //     0xb71010: ldur            w5, [x1, #0xf]
    // 0xb71014: DecompressPointer r5
    //     0xb71014: add             x5, x5, HEAP, lsl #32
    // 0xb71018: r1 = LoadInt32Instr(r0)
    //     0xb71018: sbfx            x1, x0, #1, #0x1f
    //     0xb7101c: tbz             w0, #0, #0xb71024
    //     0xb71020: ldur            x1, [x0, #7]
    // 0xb71024: mov             x16, x3
    // 0xb71028: mov             x3, x2
    // 0xb7102c: mov             x2, x16
    // 0xb71030: mov             x16, x1
    // 0xb71034: mov             x1, x3
    // 0xb71038: mov             x3, x16
    // 0xb7103c: r0 = _openImageViewer()
    //     0xb7103c: bl              #0xb70b68  ; [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xb71040: r0 = Null
    //     0xb71040: mov             x0, NULL
    // 0xb71044: LeaveFrame
    //     0xb71044: mov             SP, fp
    //     0xb71048: ldp             fp, lr, [SP], #0x10
    // 0xb7104c: ret
    //     0xb7104c: ret             
    // 0xb71050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb71050: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb71054: b               #0xb70ff0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb71058, size: 0x8c
    // 0xb71058: EnterFrame
    //     0xb71058: stp             fp, lr, [SP, #-0x10]!
    //     0xb7105c: mov             fp, SP
    // 0xb71060: AllocStack(0x10)
    //     0xb71060: sub             SP, SP, #0x10
    // 0xb71064: SetupParameters()
    //     0xb71064: ldr             x0, [fp, #0x10]
    //     0xb71068: ldur            w2, [x0, #0x17]
    //     0xb7106c: add             x2, x2, HEAP, lsl #32
    //     0xb71070: stur            x2, [fp, #-0x10]
    // 0xb71074: CheckStackOverflow
    //     0xb71074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71078: cmp             SP, x16
    //     0xb7107c: b.ls            #0xb710dc
    // 0xb71080: LoadField: r0 = r2->field_13
    //     0xb71080: ldur            w0, [x2, #0x13]
    // 0xb71084: DecompressPointer r0
    //     0xb71084: add             x0, x0, HEAP, lsl #32
    // 0xb71088: mov             x1, x0
    // 0xb7108c: stur            x0, [fp, #-8]
    // 0xb71090: r0 = value()
    //     0xb71090: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb71094: eor             x2, x0, #0x10
    // 0xb71098: ldur            x1, [fp, #-8]
    // 0xb7109c: r0 = value=()
    //     0xb7109c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb710a0: ldur            x0, [fp, #-0x10]
    // 0xb710a4: LoadField: r3 = r0->field_f
    //     0xb710a4: ldur            w3, [x0, #0xf]
    // 0xb710a8: DecompressPointer r3
    //     0xb710a8: add             x3, x3, HEAP, lsl #32
    // 0xb710ac: stur            x3, [fp, #-8]
    // 0xb710b0: r1 = Function '<anonymous closure>':.
    //     0xb710b0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b70] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb710b4: ldr             x1, [x1, #0xb70]
    // 0xb710b8: r2 = Null
    //     0xb710b8: mov             x2, NULL
    // 0xb710bc: r0 = AllocateClosure()
    //     0xb710bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb710c0: ldur            x1, [fp, #-8]
    // 0xb710c4: mov             x2, x0
    // 0xb710c8: r0 = setState()
    //     0xb710c8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb710cc: r0 = Null
    //     0xb710cc: mov             x0, NULL
    // 0xb710d0: LeaveFrame
    //     0xb710d0: mov             SP, fp
    //     0xb710d4: ldp             fp, lr, [SP], #0x10
    // 0xb710d8: ret
    //     0xb710d8: ret             
    // 0xb710dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb710dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb710e0: b               #0xb71080
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb710e4, size: 0x84
    // 0xb710e4: EnterFrame
    //     0xb710e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb710e8: mov             fp, SP
    // 0xb710ec: AllocStack(0x10)
    //     0xb710ec: sub             SP, SP, #0x10
    // 0xb710f0: SetupParameters()
    //     0xb710f0: ldr             x0, [fp, #0x18]
    //     0xb710f4: ldur            w1, [x0, #0x17]
    //     0xb710f8: add             x1, x1, HEAP, lsl #32
    //     0xb710fc: stur            x1, [fp, #-8]
    // 0xb71100: CheckStackOverflow
    //     0xb71100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71104: cmp             SP, x16
    //     0xb71108: b.ls            #0xb71160
    // 0xb7110c: r1 = 1
    //     0xb7110c: movz            x1, #0x1
    // 0xb71110: r0 = AllocateContext()
    //     0xb71110: bl              #0x16f6108  ; AllocateContextStub
    // 0xb71114: mov             x1, x0
    // 0xb71118: ldur            x0, [fp, #-8]
    // 0xb7111c: StoreField: r1->field_b = r0
    //     0xb7111c: stur            w0, [x1, #0xb]
    // 0xb71120: ldr             x2, [fp, #0x10]
    // 0xb71124: StoreField: r1->field_f = r2
    //     0xb71124: stur            w2, [x1, #0xf]
    // 0xb71128: LoadField: r3 = r0->field_f
    //     0xb71128: ldur            w3, [x0, #0xf]
    // 0xb7112c: DecompressPointer r3
    //     0xb7112c: add             x3, x3, HEAP, lsl #32
    // 0xb71130: mov             x2, x1
    // 0xb71134: stur            x3, [fp, #-0x10]
    // 0xb71138: r1 = Function '<anonymous closure>':.
    //     0xb71138: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bc8] AnonymousClosure: (0xb71168), in [package:customer_app/app/presentation/views/glass/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb6e824)
    //     0xb7113c: ldr             x1, [x1, #0xbc8]
    // 0xb71140: r0 = AllocateClosure()
    //     0xb71140: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71144: ldur            x1, [fp, #-0x10]
    // 0xb71148: mov             x2, x0
    // 0xb7114c: r0 = setState()
    //     0xb7114c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb71150: r0 = Null
    //     0xb71150: mov             x0, NULL
    // 0xb71154: LeaveFrame
    //     0xb71154: mov             SP, fp
    //     0xb71158: ldp             fp, lr, [SP], #0x10
    // 0xb7115c: ret
    //     0xb7115c: ret             
    // 0xb71160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb71160: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb71164: b               #0xb7110c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb71168, size: 0x8c
    // 0xb71168: EnterFrame
    //     0xb71168: stp             fp, lr, [SP, #-0x10]!
    //     0xb7116c: mov             fp, SP
    // 0xb71170: AllocStack(0x8)
    //     0xb71170: sub             SP, SP, #8
    // 0xb71174: SetupParameters()
    //     0xb71174: ldr             x0, [fp, #0x10]
    //     0xb71178: ldur            w1, [x0, #0x17]
    //     0xb7117c: add             x1, x1, HEAP, lsl #32
    // 0xb71180: CheckStackOverflow
    //     0xb71180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71184: cmp             SP, x16
    //     0xb71188: b.ls            #0xb711ec
    // 0xb7118c: LoadField: r0 = r1->field_b
    //     0xb7118c: ldur            w0, [x1, #0xb]
    // 0xb71190: DecompressPointer r0
    //     0xb71190: add             x0, x0, HEAP, lsl #32
    // 0xb71194: LoadField: r2 = r0->field_f
    //     0xb71194: ldur            w2, [x0, #0xf]
    // 0xb71198: DecompressPointer r2
    //     0xb71198: add             x2, x2, HEAP, lsl #32
    // 0xb7119c: LoadField: r0 = r1->field_f
    //     0xb7119c: ldur            w0, [x1, #0xf]
    // 0xb711a0: DecompressPointer r0
    //     0xb711a0: add             x0, x0, HEAP, lsl #32
    // 0xb711a4: r1 = LoadInt32Instr(r0)
    //     0xb711a4: sbfx            x1, x0, #1, #0x1f
    //     0xb711a8: tbz             w0, #0, #0xb711b0
    //     0xb711ac: ldur            x1, [x0, #7]
    // 0xb711b0: ArrayStore: r2[0] = r1  ; List_8
    //     0xb711b0: stur            x1, [x2, #0x17]
    // 0xb711b4: LoadField: r0 = r2->field_1f
    //     0xb711b4: ldur            w0, [x2, #0x1f]
    // 0xb711b8: DecompressPointer r0
    //     0xb711b8: add             x0, x0, HEAP, lsl #32
    // 0xb711bc: stur            x0, [fp, #-8]
    // 0xb711c0: r1 = Function '<anonymous closure>':.
    //     0xb711c0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bd0] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xb711c4: ldr             x1, [x1, #0xbd0]
    // 0xb711c8: r2 = Null
    //     0xb711c8: mov             x2, NULL
    // 0xb711cc: r0 = AllocateClosure()
    //     0xb711cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb711d0: ldur            x1, [fp, #-8]
    // 0xb711d4: mov             x2, x0
    // 0xb711d8: r0 = forEach()
    //     0xb711d8: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xb711dc: r0 = Null
    //     0xb711dc: mov             x0, NULL
    // 0xb711e0: LeaveFrame
    //     0xb711e0: mov             SP, fp
    //     0xb711e4: ldp             fp, lr, [SP], #0x10
    // 0xb711e8: ret
    //     0xb711e8: ret             
    // 0xb711ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb711ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb711f0: b               #0xb7118c
  }
}

// class id: 4072, size: 0x34, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f488, size: 0x84
    // 0xc7f488: EnterFrame
    //     0xc7f488: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f48c: mov             fp, SP
    // 0xc7f490: AllocStack(0x18)
    //     0xc7f490: sub             SP, SP, #0x18
    // 0xc7f494: CheckStackOverflow
    //     0xc7f494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f498: cmp             SP, x16
    //     0xc7f49c: b.ls            #0xc7f504
    // 0xc7f4a0: r1 = <TestimonialCarousel>
    //     0xc7f4a0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48810] TypeArguments: <TestimonialCarousel>
    //     0xc7f4a4: ldr             x1, [x1, #0x810]
    // 0xc7f4a8: r0 = _TestimonialCarouselState()
    //     0xc7f4a8: bl              #0xc7f50c  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x24)
    // 0xc7f4ac: mov             x1, x0
    // 0xc7f4b0: r0 = Sentinel
    //     0xc7f4b0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f4b4: stur            x1, [fp, #-8]
    // 0xc7f4b8: StoreField: r1->field_13 = r0
    //     0xc7f4b8: stur            w0, [x1, #0x13]
    // 0xc7f4bc: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7f4bc: stur            xzr, [x1, #0x17]
    // 0xc7f4c0: r16 = <int, RxBool>
    //     0xc7f4c0: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7f4c4: ldr             x16, [x16, #0x298]
    // 0xc7f4c8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7f4cc: stp             lr, x16, [SP]
    // 0xc7f4d0: r0 = Map._fromLiteral()
    //     0xc7f4d0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7f4d4: ldur            x1, [fp, #-8]
    // 0xc7f4d8: StoreField: r1->field_1f = r0
    //     0xc7f4d8: stur            w0, [x1, #0x1f]
    //     0xc7f4dc: ldurb           w16, [x1, #-1]
    //     0xc7f4e0: ldurb           w17, [x0, #-1]
    //     0xc7f4e4: and             x16, x17, x16, lsr #2
    //     0xc7f4e8: tst             x16, HEAP, lsr #32
    //     0xc7f4ec: b.eq            #0xc7f4f4
    //     0xc7f4f0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7f4f4: mov             x0, x1
    // 0xc7f4f8: LeaveFrame
    //     0xc7f4f8: mov             SP, fp
    //     0xc7f4fc: ldp             fp, lr, [SP], #0x10
    // 0xc7f500: ret
    //     0xc7f500: ret             
    // 0xc7f504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f504: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f508: b               #0xc7f4a0
  }
}
