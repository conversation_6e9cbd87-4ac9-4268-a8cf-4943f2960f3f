// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/single_exchange_product_bottom_sheet.dart

// class id: 1049326, size: 0x8
class :: {
}

// class id: 3393, size: 0x18, field offset: 0x14
class _SingleExchangeProductBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb18c24, size: 0x2110
    // 0xb18c24: EnterFrame
    //     0xb18c24: stp             fp, lr, [SP, #-0x10]!
    //     0xb18c28: mov             fp, SP
    // 0xb18c2c: AllocStack(0xa0)
    //     0xb18c2c: sub             SP, SP, #0xa0
    // 0xb18c30: SetupParameters(_SingleExchangeProductBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xb18c30: stur            x1, [fp, #-8]
    // 0xb18c34: CheckStackOverflow
    //     0xb18c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb18c38: cmp             SP, x16
    //     0xb18c3c: b.ls            #0xb1acec
    // 0xb18c40: r1 = 1
    //     0xb18c40: movz            x1, #0x1
    // 0xb18c44: r0 = AllocateContext()
    //     0xb18c44: bl              #0x16f6108  ; AllocateContextStub
    // 0xb18c48: mov             x1, x0
    // 0xb18c4c: ldur            x0, [fp, #-8]
    // 0xb18c50: stur            x1, [fp, #-0x10]
    // 0xb18c54: StoreField: r1->field_f = r0
    //     0xb18c54: stur            w0, [x1, #0xf]
    // 0xb18c58: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xb18c58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb18c5c: ldr             x0, [x0, #0x1ab0]
    //     0xb18c60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb18c64: cmp             w0, w16
    //     0xb18c68: b.ne            #0xb18c78
    //     0xb18c6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xb18c70: ldr             x2, [x2, #0x60]
    //     0xb18c74: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb18c78: LoadField: r2 = r0->field_87
    //     0xb18c78: ldur            w2, [x0, #0x87]
    // 0xb18c7c: DecompressPointer r2
    //     0xb18c7c: add             x2, x2, HEAP, lsl #32
    // 0xb18c80: stur            x2, [fp, #-0x20]
    // 0xb18c84: LoadField: r0 = r2->field_7
    //     0xb18c84: ldur            w0, [x2, #7]
    // 0xb18c88: DecompressPointer r0
    //     0xb18c88: add             x0, x0, HEAP, lsl #32
    // 0xb18c8c: stur            x0, [fp, #-0x18]
    // 0xb18c90: r16 = Instance_Color
    //     0xb18c90: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb18c94: r30 = 16.000000
    //     0xb18c94: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb18c98: ldr             lr, [lr, #0x188]
    // 0xb18c9c: stp             lr, x16, [SP]
    // 0xb18ca0: mov             x1, x0
    // 0xb18ca4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb18ca4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb18ca8: ldr             x4, [x4, #0x9b8]
    // 0xb18cac: r0 = copyWith()
    //     0xb18cac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb18cb0: stur            x0, [fp, #-0x28]
    // 0xb18cb4: r0 = Text()
    //     0xb18cb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb18cb8: mov             x3, x0
    // 0xb18cbc: r0 = "Ready for Exchange\?"
    //     0xb18cbc: add             x0, PP, #0x58, lsl #12  ; [pp+0x58470] "Ready for Exchange\?"
    //     0xb18cc0: ldr             x0, [x0, #0x470]
    // 0xb18cc4: stur            x3, [fp, #-0x30]
    // 0xb18cc8: StoreField: r3->field_b = r0
    //     0xb18cc8: stur            w0, [x3, #0xb]
    // 0xb18ccc: ldur            x0, [fp, #-0x28]
    // 0xb18cd0: StoreField: r3->field_13 = r0
    //     0xb18cd0: stur            w0, [x3, #0x13]
    // 0xb18cd4: r1 = Function '<anonymous closure>':.
    //     0xb18cd4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58478] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb18cd8: ldr             x1, [x1, #0x478]
    // 0xb18cdc: r2 = Null
    //     0xb18cdc: mov             x2, NULL
    // 0xb18ce0: r0 = AllocateClosure()
    //     0xb18ce0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb18ce4: stur            x0, [fp, #-0x28]
    // 0xb18ce8: r0 = IconButton()
    //     0xb18ce8: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xb18cec: mov             x3, x0
    // 0xb18cf0: ldur            x0, [fp, #-0x28]
    // 0xb18cf4: stur            x3, [fp, #-0x38]
    // 0xb18cf8: StoreField: r3->field_3b = r0
    //     0xb18cf8: stur            w0, [x3, #0x3b]
    // 0xb18cfc: r0 = false
    //     0xb18cfc: add             x0, NULL, #0x30  ; false
    // 0xb18d00: StoreField: r3->field_4f = r0
    //     0xb18d00: stur            w0, [x3, #0x4f]
    // 0xb18d04: r1 = Instance_Icon
    //     0xb18d04: add             x1, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb18d08: ldr             x1, [x1, #0x2b8]
    // 0xb18d0c: StoreField: r3->field_1f = r1
    //     0xb18d0c: stur            w1, [x3, #0x1f]
    // 0xb18d10: r1 = Instance__IconButtonVariant
    //     0xb18d10: add             x1, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xb18d14: ldr             x1, [x1, #0x900]
    // 0xb18d18: StoreField: r3->field_6b = r1
    //     0xb18d18: stur            w1, [x3, #0x6b]
    // 0xb18d1c: r1 = Null
    //     0xb18d1c: mov             x1, NULL
    // 0xb18d20: r2 = 4
    //     0xb18d20: movz            x2, #0x4
    // 0xb18d24: r0 = AllocateArray()
    //     0xb18d24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb18d28: mov             x2, x0
    // 0xb18d2c: ldur            x0, [fp, #-0x30]
    // 0xb18d30: stur            x2, [fp, #-0x28]
    // 0xb18d34: StoreField: r2->field_f = r0
    //     0xb18d34: stur            w0, [x2, #0xf]
    // 0xb18d38: ldur            x0, [fp, #-0x38]
    // 0xb18d3c: StoreField: r2->field_13 = r0
    //     0xb18d3c: stur            w0, [x2, #0x13]
    // 0xb18d40: r1 = <Widget>
    //     0xb18d40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb18d44: r0 = AllocateGrowableArray()
    //     0xb18d44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb18d48: mov             x1, x0
    // 0xb18d4c: ldur            x0, [fp, #-0x28]
    // 0xb18d50: stur            x1, [fp, #-0x30]
    // 0xb18d54: StoreField: r1->field_f = r0
    //     0xb18d54: stur            w0, [x1, #0xf]
    // 0xb18d58: r2 = 4
    //     0xb18d58: movz            x2, #0x4
    // 0xb18d5c: StoreField: r1->field_b = r2
    //     0xb18d5c: stur            w2, [x1, #0xb]
    // 0xb18d60: r0 = Row()
    //     0xb18d60: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb18d64: mov             x1, x0
    // 0xb18d68: r0 = Instance_Axis
    //     0xb18d68: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb18d6c: stur            x1, [fp, #-0x28]
    // 0xb18d70: StoreField: r1->field_f = r0
    //     0xb18d70: stur            w0, [x1, #0xf]
    // 0xb18d74: r2 = Instance_MainAxisAlignment
    //     0xb18d74: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb18d78: ldr             x2, [x2, #0xa8]
    // 0xb18d7c: StoreField: r1->field_13 = r2
    //     0xb18d7c: stur            w2, [x1, #0x13]
    // 0xb18d80: r3 = Instance_MainAxisSize
    //     0xb18d80: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb18d84: ldr             x3, [x3, #0xa10]
    // 0xb18d88: ArrayStore: r1[0] = r3  ; List_4
    //     0xb18d88: stur            w3, [x1, #0x17]
    // 0xb18d8c: r4 = Instance_CrossAxisAlignment
    //     0xb18d8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb18d90: ldr             x4, [x4, #0xa18]
    // 0xb18d94: StoreField: r1->field_1b = r4
    //     0xb18d94: stur            w4, [x1, #0x1b]
    // 0xb18d98: r5 = Instance_VerticalDirection
    //     0xb18d98: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb18d9c: ldr             x5, [x5, #0xa20]
    // 0xb18da0: StoreField: r1->field_23 = r5
    //     0xb18da0: stur            w5, [x1, #0x23]
    // 0xb18da4: r6 = Instance_Clip
    //     0xb18da4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb18da8: ldr             x6, [x6, #0x38]
    // 0xb18dac: StoreField: r1->field_2b = r6
    //     0xb18dac: stur            w6, [x1, #0x2b]
    // 0xb18db0: StoreField: r1->field_2f = rZR
    //     0xb18db0: stur            xzr, [x1, #0x2f]
    // 0xb18db4: ldur            x7, [fp, #-0x30]
    // 0xb18db8: StoreField: r1->field_b = r7
    //     0xb18db8: stur            w7, [x1, #0xb]
    // 0xb18dbc: r0 = Radius()
    //     0xb18dbc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb18dc0: d0 = 15.000000
    //     0xb18dc0: fmov            d0, #15.00000000
    // 0xb18dc4: stur            x0, [fp, #-0x30]
    // 0xb18dc8: StoreField: r0->field_7 = d0
    //     0xb18dc8: stur            d0, [x0, #7]
    // 0xb18dcc: StoreField: r0->field_f = d0
    //     0xb18dcc: stur            d0, [x0, #0xf]
    // 0xb18dd0: r0 = BorderRadius()
    //     0xb18dd0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb18dd4: mov             x1, x0
    // 0xb18dd8: ldur            x0, [fp, #-0x30]
    // 0xb18ddc: stur            x1, [fp, #-0x38]
    // 0xb18de0: StoreField: r1->field_7 = r0
    //     0xb18de0: stur            w0, [x1, #7]
    // 0xb18de4: StoreField: r1->field_b = r0
    //     0xb18de4: stur            w0, [x1, #0xb]
    // 0xb18de8: StoreField: r1->field_f = r0
    //     0xb18de8: stur            w0, [x1, #0xf]
    // 0xb18dec: StoreField: r1->field_13 = r0
    //     0xb18dec: stur            w0, [x1, #0x13]
    // 0xb18df0: r0 = BoxDecoration()
    //     0xb18df0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb18df4: mov             x1, x0
    // 0xb18df8: r0 = Instance_Color
    //     0xb18df8: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb18dfc: stur            x1, [fp, #-0x58]
    // 0xb18e00: StoreField: r1->field_7 = r0
    //     0xb18e00: stur            w0, [x1, #7]
    // 0xb18e04: ldur            x0, [fp, #-0x38]
    // 0xb18e08: StoreField: r1->field_13 = r0
    //     0xb18e08: stur            w0, [x1, #0x13]
    // 0xb18e0c: r0 = Instance_BoxShape
    //     0xb18e0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb18e10: ldr             x0, [x0, #0x80]
    // 0xb18e14: StoreField: r1->field_23 = r0
    //     0xb18e14: stur            w0, [x1, #0x23]
    // 0xb18e18: ldur            x2, [fp, #-8]
    // 0xb18e1c: LoadField: r3 = r2->field_b
    //     0xb18e1c: ldur            w3, [x2, #0xb]
    // 0xb18e20: DecompressPointer r3
    //     0xb18e20: add             x3, x3, HEAP, lsl #32
    // 0xb18e24: stur            x3, [fp, #-0x30]
    // 0xb18e28: cmp             w3, NULL
    // 0xb18e2c: b.eq            #0xb1acf4
    // 0xb18e30: LoadField: r4 = r3->field_2f
    //     0xb18e30: ldur            w4, [x3, #0x2f]
    // 0xb18e34: DecompressPointer r4
    //     0xb18e34: add             x4, x4, HEAP, lsl #32
    // 0xb18e38: LoadField: r5 = r4->field_3f
    //     0xb18e38: ldur            w5, [x4, #0x3f]
    // 0xb18e3c: DecompressPointer r5
    //     0xb18e3c: add             x5, x5, HEAP, lsl #32
    // 0xb18e40: cmp             w5, NULL
    // 0xb18e44: b.ne            #0xb18e50
    // 0xb18e48: r4 = Null
    //     0xb18e48: mov             x4, NULL
    // 0xb18e4c: b               #0xb18e74
    // 0xb18e50: ArrayLoad: r4 = r5[0]  ; List_4
    //     0xb18e50: ldur            w4, [x5, #0x17]
    // 0xb18e54: DecompressPointer r4
    //     0xb18e54: add             x4, x4, HEAP, lsl #32
    // 0xb18e58: cmp             w4, NULL
    // 0xb18e5c: b.ne            #0xb18e68
    // 0xb18e60: r4 = Null
    //     0xb18e60: mov             x4, NULL
    // 0xb18e64: b               #0xb18e74
    // 0xb18e68: LoadField: r6 = r4->field_7
    //     0xb18e68: ldur            w6, [x4, #7]
    // 0xb18e6c: DecompressPointer r6
    //     0xb18e6c: add             x6, x6, HEAP, lsl #32
    // 0xb18e70: mov             x4, x6
    // 0xb18e74: cmp             w4, NULL
    // 0xb18e78: b.ne            #0xb18e84
    // 0xb18e7c: r4 = 0
    //     0xb18e7c: movz            x4, #0
    // 0xb18e80: b               #0xb18e94
    // 0xb18e84: r6 = LoadInt32Instr(r4)
    //     0xb18e84: sbfx            x6, x4, #1, #0x1f
    //     0xb18e88: tbz             w4, #0, #0xb18e90
    //     0xb18e8c: ldur            x6, [x4, #7]
    // 0xb18e90: mov             x4, x6
    // 0xb18e94: stur            x4, [fp, #-0x50]
    // 0xb18e98: cmp             w5, NULL
    // 0xb18e9c: b.ne            #0xb18ea8
    // 0xb18ea0: r6 = Null
    //     0xb18ea0: mov             x6, NULL
    // 0xb18ea4: b               #0xb18ecc
    // 0xb18ea8: ArrayLoad: r6 = r5[0]  ; List_4
    //     0xb18ea8: ldur            w6, [x5, #0x17]
    // 0xb18eac: DecompressPointer r6
    //     0xb18eac: add             x6, x6, HEAP, lsl #32
    // 0xb18eb0: cmp             w6, NULL
    // 0xb18eb4: b.ne            #0xb18ec0
    // 0xb18eb8: r6 = Null
    //     0xb18eb8: mov             x6, NULL
    // 0xb18ebc: b               #0xb18ecc
    // 0xb18ec0: LoadField: r7 = r6->field_b
    //     0xb18ec0: ldur            w7, [x6, #0xb]
    // 0xb18ec4: DecompressPointer r7
    //     0xb18ec4: add             x7, x7, HEAP, lsl #32
    // 0xb18ec8: mov             x6, x7
    // 0xb18ecc: cmp             w6, NULL
    // 0xb18ed0: b.ne            #0xb18edc
    // 0xb18ed4: r6 = 0
    //     0xb18ed4: movz            x6, #0
    // 0xb18ed8: b               #0xb18eec
    // 0xb18edc: r7 = LoadInt32Instr(r6)
    //     0xb18edc: sbfx            x7, x6, #1, #0x1f
    //     0xb18ee0: tbz             w6, #0, #0xb18ee8
    //     0xb18ee4: ldur            x7, [x6, #7]
    // 0xb18ee8: mov             x6, x7
    // 0xb18eec: stur            x6, [fp, #-0x48]
    // 0xb18ef0: cmp             w5, NULL
    // 0xb18ef4: b.ne            #0xb18f00
    // 0xb18ef8: r5 = Null
    //     0xb18ef8: mov             x5, NULL
    // 0xb18efc: b               #0xb18f20
    // 0xb18f00: ArrayLoad: r7 = r5[0]  ; List_4
    //     0xb18f00: ldur            w7, [x5, #0x17]
    // 0xb18f04: DecompressPointer r7
    //     0xb18f04: add             x7, x7, HEAP, lsl #32
    // 0xb18f08: cmp             w7, NULL
    // 0xb18f0c: b.ne            #0xb18f18
    // 0xb18f10: r5 = Null
    //     0xb18f10: mov             x5, NULL
    // 0xb18f14: b               #0xb18f20
    // 0xb18f18: LoadField: r5 = r7->field_f
    //     0xb18f18: ldur            w5, [x7, #0xf]
    // 0xb18f1c: DecompressPointer r5
    //     0xb18f1c: add             x5, x5, HEAP, lsl #32
    // 0xb18f20: cmp             w5, NULL
    // 0xb18f24: b.ne            #0xb18f30
    // 0xb18f28: r5 = 0
    //     0xb18f28: movz            x5, #0
    // 0xb18f2c: b               #0xb18f40
    // 0xb18f30: r7 = LoadInt32Instr(r5)
    //     0xb18f30: sbfx            x7, x5, #1, #0x1f
    //     0xb18f34: tbz             w5, #0, #0xb18f3c
    //     0xb18f38: ldur            x7, [x5, #7]
    // 0xb18f3c: mov             x5, x7
    // 0xb18f40: stur            x5, [fp, #-0x40]
    // 0xb18f44: r0 = Color()
    //     0xb18f44: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb18f48: mov             x1, x0
    // 0xb18f4c: r0 = Instance_ColorSpace
    //     0xb18f4c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb18f50: stur            x1, [fp, #-0x38]
    // 0xb18f54: StoreField: r1->field_27 = r0
    //     0xb18f54: stur            w0, [x1, #0x27]
    // 0xb18f58: d0 = 1.000000
    //     0xb18f58: fmov            d0, #1.00000000
    // 0xb18f5c: StoreField: r1->field_7 = d0
    //     0xb18f5c: stur            d0, [x1, #7]
    // 0xb18f60: ldur            x2, [fp, #-0x50]
    // 0xb18f64: ubfx            x2, x2, #0, #0x20
    // 0xb18f68: and             w3, w2, #0xff
    // 0xb18f6c: ubfx            x3, x3, #0, #0x20
    // 0xb18f70: scvtf           d1, x3
    // 0xb18f74: d2 = 255.000000
    //     0xb18f74: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb18f78: fdiv            d3, d1, d2
    // 0xb18f7c: StoreField: r1->field_f = d3
    //     0xb18f7c: stur            d3, [x1, #0xf]
    // 0xb18f80: ldur            x2, [fp, #-0x48]
    // 0xb18f84: ubfx            x2, x2, #0, #0x20
    // 0xb18f88: and             w3, w2, #0xff
    // 0xb18f8c: ubfx            x3, x3, #0, #0x20
    // 0xb18f90: scvtf           d1, x3
    // 0xb18f94: fdiv            d3, d1, d2
    // 0xb18f98: ArrayStore: r1[0] = d3  ; List_8
    //     0xb18f98: stur            d3, [x1, #0x17]
    // 0xb18f9c: ldur            x2, [fp, #-0x40]
    // 0xb18fa0: ubfx            x2, x2, #0, #0x20
    // 0xb18fa4: and             w3, w2, #0xff
    // 0xb18fa8: ubfx            x3, x3, #0, #0x20
    // 0xb18fac: scvtf           d1, x3
    // 0xb18fb0: fdiv            d3, d1, d2
    // 0xb18fb4: StoreField: r1->field_1f = d3
    //     0xb18fb4: stur            d3, [x1, #0x1f]
    // 0xb18fb8: r0 = BoxDecoration()
    //     0xb18fb8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb18fbc: mov             x1, x0
    // 0xb18fc0: ldur            x0, [fp, #-0x38]
    // 0xb18fc4: stur            x1, [fp, #-0x60]
    // 0xb18fc8: StoreField: r1->field_7 = r0
    //     0xb18fc8: stur            w0, [x1, #7]
    // 0xb18fcc: r0 = Instance_BorderRadius
    //     0xb18fcc: add             x0, PP, #0x56, lsl #12  ; [pp+0x56008] Obj!BorderRadius@d5a361
    //     0xb18fd0: ldr             x0, [x0, #8]
    // 0xb18fd4: StoreField: r1->field_13 = r0
    //     0xb18fd4: stur            w0, [x1, #0x13]
    // 0xb18fd8: r0 = Instance_BoxShape
    //     0xb18fd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb18fdc: ldr             x0, [x0, #0x80]
    // 0xb18fe0: StoreField: r1->field_23 = r0
    //     0xb18fe0: stur            w0, [x1, #0x23]
    // 0xb18fe4: r0 = Radius()
    //     0xb18fe4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb18fe8: d0 = 12.000000
    //     0xb18fe8: fmov            d0, #12.00000000
    // 0xb18fec: stur            x0, [fp, #-0x38]
    // 0xb18ff0: StoreField: r0->field_7 = d0
    //     0xb18ff0: stur            d0, [x0, #7]
    // 0xb18ff4: StoreField: r0->field_f = d0
    //     0xb18ff4: stur            d0, [x0, #0xf]
    // 0xb18ff8: r0 = BorderRadius()
    //     0xb18ff8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb18ffc: mov             x3, x0
    // 0xb19000: ldur            x0, [fp, #-0x38]
    // 0xb19004: stur            x3, [fp, #-0x68]
    // 0xb19008: StoreField: r3->field_7 = r0
    //     0xb19008: stur            w0, [x3, #7]
    // 0xb1900c: StoreField: r3->field_b = r0
    //     0xb1900c: stur            w0, [x3, #0xb]
    // 0xb19010: StoreField: r3->field_f = r0
    //     0xb19010: stur            w0, [x3, #0xf]
    // 0xb19014: StoreField: r3->field_13 = r0
    //     0xb19014: stur            w0, [x3, #0x13]
    // 0xb19018: ldur            x0, [fp, #-0x30]
    // 0xb1901c: LoadField: r1 = r0->field_b
    //     0xb1901c: ldur            w1, [x0, #0xb]
    // 0xb19020: DecompressPointer r1
    //     0xb19020: add             x1, x1, HEAP, lsl #32
    // 0xb19024: cmp             w1, NULL
    // 0xb19028: b.ne            #0xb19034
    // 0xb1902c: r0 = Null
    //     0xb1902c: mov             x0, NULL
    // 0xb19030: b               #0xb1906c
    // 0xb19034: LoadField: r0 = r1->field_b
    //     0xb19034: ldur            w0, [x1, #0xb]
    // 0xb19038: DecompressPointer r0
    //     0xb19038: add             x0, x0, HEAP, lsl #32
    // 0xb1903c: cmp             w0, NULL
    // 0xb19040: b.ne            #0xb1904c
    // 0xb19044: r0 = Null
    //     0xb19044: mov             x0, NULL
    // 0xb19048: b               #0xb1906c
    // 0xb1904c: LoadField: r1 = r0->field_7
    //     0xb1904c: ldur            w1, [x0, #7]
    // 0xb19050: DecompressPointer r1
    //     0xb19050: add             x1, x1, HEAP, lsl #32
    // 0xb19054: cmp             w1, NULL
    // 0xb19058: b.ne            #0xb19064
    // 0xb1905c: r0 = Null
    //     0xb1905c: mov             x0, NULL
    // 0xb19060: b               #0xb1906c
    // 0xb19064: LoadField: r0 = r1->field_7
    //     0xb19064: ldur            w0, [x1, #7]
    // 0xb19068: DecompressPointer r0
    //     0xb19068: add             x0, x0, HEAP, lsl #32
    // 0xb1906c: cmp             w0, NULL
    // 0xb19070: b.ne            #0xb1907c
    // 0xb19074: r4 = ""
    //     0xb19074: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19078: b               #0xb19080
    // 0xb1907c: mov             x4, x0
    // 0xb19080: ldur            x0, [fp, #-8]
    // 0xb19084: stur            x4, [fp, #-0x30]
    // 0xb19088: r1 = Function '<anonymous closure>':.
    //     0xb19088: add             x1, PP, #0x58, lsl #12  ; [pp+0x58480] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb1908c: ldr             x1, [x1, #0x480]
    // 0xb19090: r2 = Null
    //     0xb19090: mov             x2, NULL
    // 0xb19094: r0 = AllocateClosure()
    //     0xb19094: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb19098: stur            x0, [fp, #-0x38]
    // 0xb1909c: r0 = CachedNetworkImage()
    //     0xb1909c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb190a0: stur            x0, [fp, #-0x70]
    // 0xb190a4: r16 = Instance_BoxFit
    //     0xb190a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb190a8: ldr             x16, [x16, #0x118]
    // 0xb190ac: r30 = 56.000000
    //     0xb190ac: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb190b0: ldr             lr, [lr, #0xb78]
    // 0xb190b4: stp             lr, x16, [SP, #0x10]
    // 0xb190b8: r16 = 56.000000
    //     0xb190b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb190bc: ldr             x16, [x16, #0xb78]
    // 0xb190c0: ldur            lr, [fp, #-0x38]
    // 0xb190c4: stp             lr, x16, [SP]
    // 0xb190c8: mov             x1, x0
    // 0xb190cc: ldur            x2, [fp, #-0x30]
    // 0xb190d0: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xb190d0: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xb190d4: ldr             x4, [x4, #0xbf8]
    // 0xb190d8: r0 = CachedNetworkImage()
    //     0xb190d8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb190dc: r0 = ClipRRect()
    //     0xb190dc: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb190e0: mov             x2, x0
    // 0xb190e4: ldur            x0, [fp, #-0x68]
    // 0xb190e8: stur            x2, [fp, #-0x38]
    // 0xb190ec: StoreField: r2->field_f = r0
    //     0xb190ec: stur            w0, [x2, #0xf]
    // 0xb190f0: r0 = Instance_Clip
    //     0xb190f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb190f4: ldr             x0, [x0, #0x138]
    // 0xb190f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb190f8: stur            w0, [x2, #0x17]
    // 0xb190fc: ldur            x1, [fp, #-0x70]
    // 0xb19100: StoreField: r2->field_b = r1
    //     0xb19100: stur            w1, [x2, #0xb]
    // 0xb19104: ldur            x3, [fp, #-8]
    // 0xb19108: LoadField: r1 = r3->field_b
    //     0xb19108: ldur            w1, [x3, #0xb]
    // 0xb1910c: DecompressPointer r1
    //     0xb1910c: add             x1, x1, HEAP, lsl #32
    // 0xb19110: cmp             w1, NULL
    // 0xb19114: b.eq            #0xb1acf8
    // 0xb19118: LoadField: r4 = r1->field_b
    //     0xb19118: ldur            w4, [x1, #0xb]
    // 0xb1911c: DecompressPointer r4
    //     0xb1911c: add             x4, x4, HEAP, lsl #32
    // 0xb19120: cmp             w4, NULL
    // 0xb19124: b.ne            #0xb19130
    // 0xb19128: r1 = Null
    //     0xb19128: mov             x1, NULL
    // 0xb1912c: b               #0xb19168
    // 0xb19130: LoadField: r1 = r4->field_b
    //     0xb19130: ldur            w1, [x4, #0xb]
    // 0xb19134: DecompressPointer r1
    //     0xb19134: add             x1, x1, HEAP, lsl #32
    // 0xb19138: cmp             w1, NULL
    // 0xb1913c: b.ne            #0xb19148
    // 0xb19140: r1 = Null
    //     0xb19140: mov             x1, NULL
    // 0xb19144: b               #0xb19168
    // 0xb19148: LoadField: r4 = r1->field_7
    //     0xb19148: ldur            w4, [x1, #7]
    // 0xb1914c: DecompressPointer r4
    //     0xb1914c: add             x4, x4, HEAP, lsl #32
    // 0xb19150: cmp             w4, NULL
    // 0xb19154: b.ne            #0xb19160
    // 0xb19158: r1 = Null
    //     0xb19158: mov             x1, NULL
    // 0xb1915c: b               #0xb19168
    // 0xb19160: LoadField: r1 = r4->field_b
    //     0xb19160: ldur            w1, [x4, #0xb]
    // 0xb19164: DecompressPointer r1
    //     0xb19164: add             x1, x1, HEAP, lsl #32
    // 0xb19168: cmp             w1, NULL
    // 0xb1916c: b.ne            #0xb19178
    // 0xb19170: r4 = ""
    //     0xb19170: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19174: b               #0xb1917c
    // 0xb19178: mov             x4, x1
    // 0xb1917c: stur            x4, [fp, #-0x30]
    // 0xb19180: r16 = Instance_Color
    //     0xb19180: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb19184: r30 = 12.000000
    //     0xb19184: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19188: ldr             lr, [lr, #0x9e8]
    // 0xb1918c: stp             lr, x16, [SP]
    // 0xb19190: ldur            x1, [fp, #-0x18]
    // 0xb19194: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19194: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19198: ldr             x4, [x4, #0x9b8]
    // 0xb1919c: r0 = copyWith()
    //     0xb1919c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb191a0: stur            x0, [fp, #-0x68]
    // 0xb191a4: r0 = Text()
    //     0xb191a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb191a8: mov             x3, x0
    // 0xb191ac: ldur            x0, [fp, #-0x30]
    // 0xb191b0: stur            x3, [fp, #-0x70]
    // 0xb191b4: StoreField: r3->field_b = r0
    //     0xb191b4: stur            w0, [x3, #0xb]
    // 0xb191b8: ldur            x0, [fp, #-0x68]
    // 0xb191bc: StoreField: r3->field_13 = r0
    //     0xb191bc: stur            w0, [x3, #0x13]
    // 0xb191c0: r0 = 2
    //     0xb191c0: movz            x0, #0x2
    // 0xb191c4: StoreField: r3->field_37 = r0
    //     0xb191c4: stur            w0, [x3, #0x37]
    // 0xb191c8: r1 = Null
    //     0xb191c8: mov             x1, NULL
    // 0xb191cc: r2 = 8
    //     0xb191cc: movz            x2, #0x8
    // 0xb191d0: r0 = AllocateArray()
    //     0xb191d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb191d4: r16 = "Size: "
    //     0xb191d4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb191d8: ldr             x16, [x16, #0xf00]
    // 0xb191dc: StoreField: r0->field_f = r16
    //     0xb191dc: stur            w16, [x0, #0xf]
    // 0xb191e0: ldur            x1, [fp, #-8]
    // 0xb191e4: LoadField: r2 = r1->field_b
    //     0xb191e4: ldur            w2, [x1, #0xb]
    // 0xb191e8: DecompressPointer r2
    //     0xb191e8: add             x2, x2, HEAP, lsl #32
    // 0xb191ec: cmp             w2, NULL
    // 0xb191f0: b.eq            #0xb1acfc
    // 0xb191f4: LoadField: r3 = r2->field_b
    //     0xb191f4: ldur            w3, [x2, #0xb]
    // 0xb191f8: DecompressPointer r3
    //     0xb191f8: add             x3, x3, HEAP, lsl #32
    // 0xb191fc: cmp             w3, NULL
    // 0xb19200: b.ne            #0xb1920c
    // 0xb19204: r2 = Null
    //     0xb19204: mov             x2, NULL
    // 0xb19208: b               #0xb19244
    // 0xb1920c: LoadField: r2 = r3->field_b
    //     0xb1920c: ldur            w2, [x3, #0xb]
    // 0xb19210: DecompressPointer r2
    //     0xb19210: add             x2, x2, HEAP, lsl #32
    // 0xb19214: cmp             w2, NULL
    // 0xb19218: b.ne            #0xb19224
    // 0xb1921c: r2 = Null
    //     0xb1921c: mov             x2, NULL
    // 0xb19220: b               #0xb19244
    // 0xb19224: LoadField: r4 = r2->field_7
    //     0xb19224: ldur            w4, [x2, #7]
    // 0xb19228: DecompressPointer r4
    //     0xb19228: add             x4, x4, HEAP, lsl #32
    // 0xb1922c: cmp             w4, NULL
    // 0xb19230: b.ne            #0xb1923c
    // 0xb19234: r2 = Null
    //     0xb19234: mov             x2, NULL
    // 0xb19238: b               #0xb19244
    // 0xb1923c: LoadField: r2 = r4->field_f
    //     0xb1923c: ldur            w2, [x4, #0xf]
    // 0xb19240: DecompressPointer r2
    //     0xb19240: add             x2, x2, HEAP, lsl #32
    // 0xb19244: cmp             w2, NULL
    // 0xb19248: b.ne            #0xb19250
    // 0xb1924c: r2 = ""
    //     0xb1924c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19250: StoreField: r0->field_13 = r2
    //     0xb19250: stur            w2, [x0, #0x13]
    // 0xb19254: r16 = " / Qty: "
    //     0xb19254: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb19258: ldr             x16, [x16, #0x760]
    // 0xb1925c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb1925c: stur            w16, [x0, #0x17]
    // 0xb19260: cmp             w3, NULL
    // 0xb19264: b.ne            #0xb19270
    // 0xb19268: r2 = Null
    //     0xb19268: mov             x2, NULL
    // 0xb1926c: b               #0xb192a8
    // 0xb19270: LoadField: r2 = r3->field_b
    //     0xb19270: ldur            w2, [x3, #0xb]
    // 0xb19274: DecompressPointer r2
    //     0xb19274: add             x2, x2, HEAP, lsl #32
    // 0xb19278: cmp             w2, NULL
    // 0xb1927c: b.ne            #0xb19288
    // 0xb19280: r2 = Null
    //     0xb19280: mov             x2, NULL
    // 0xb19284: b               #0xb192a8
    // 0xb19288: LoadField: r3 = r2->field_7
    //     0xb19288: ldur            w3, [x2, #7]
    // 0xb1928c: DecompressPointer r3
    //     0xb1928c: add             x3, x3, HEAP, lsl #32
    // 0xb19290: cmp             w3, NULL
    // 0xb19294: b.ne            #0xb192a0
    // 0xb19298: r2 = Null
    //     0xb19298: mov             x2, NULL
    // 0xb1929c: b               #0xb192a8
    // 0xb192a0: LoadField: r2 = r3->field_13
    //     0xb192a0: ldur            w2, [x3, #0x13]
    // 0xb192a4: DecompressPointer r2
    //     0xb192a4: add             x2, x2, HEAP, lsl #32
    // 0xb192a8: cmp             w2, NULL
    // 0xb192ac: b.ne            #0xb192b8
    // 0xb192b0: r3 = ""
    //     0xb192b0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb192b4: b               #0xb192bc
    // 0xb192b8: mov             x3, x2
    // 0xb192bc: ldur            x2, [fp, #-0x20]
    // 0xb192c0: StoreField: r0->field_1b = r3
    //     0xb192c0: stur            w3, [x0, #0x1b]
    // 0xb192c4: str             x0, [SP]
    // 0xb192c8: r0 = _interpolate()
    //     0xb192c8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb192cc: mov             x2, x0
    // 0xb192d0: ldur            x0, [fp, #-0x20]
    // 0xb192d4: stur            x2, [fp, #-0x68]
    // 0xb192d8: LoadField: r3 = r0->field_2b
    //     0xb192d8: ldur            w3, [x0, #0x2b]
    // 0xb192dc: DecompressPointer r3
    //     0xb192dc: add             x3, x3, HEAP, lsl #32
    // 0xb192e0: stur            x3, [fp, #-0x30]
    // 0xb192e4: r16 = Instance_Color
    //     0xb192e4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb192e8: r30 = 12.000000
    //     0xb192e8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb192ec: ldr             lr, [lr, #0x9e8]
    // 0xb192f0: stp             lr, x16, [SP]
    // 0xb192f4: mov             x1, x3
    // 0xb192f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb192f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb192fc: ldr             x4, [x4, #0x9b8]
    // 0xb19300: r0 = copyWith()
    //     0xb19300: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb19304: stur            x0, [fp, #-0x20]
    // 0xb19308: r0 = Text()
    //     0xb19308: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1930c: mov             x2, x0
    // 0xb19310: ldur            x0, [fp, #-0x68]
    // 0xb19314: stur            x2, [fp, #-0x78]
    // 0xb19318: StoreField: r2->field_b = r0
    //     0xb19318: stur            w0, [x2, #0xb]
    // 0xb1931c: ldur            x0, [fp, #-0x20]
    // 0xb19320: StoreField: r2->field_13 = r0
    //     0xb19320: stur            w0, [x2, #0x13]
    // 0xb19324: ldur            x0, [fp, #-8]
    // 0xb19328: LoadField: r1 = r0->field_b
    //     0xb19328: ldur            w1, [x0, #0xb]
    // 0xb1932c: DecompressPointer r1
    //     0xb1932c: add             x1, x1, HEAP, lsl #32
    // 0xb19330: cmp             w1, NULL
    // 0xb19334: b.eq            #0xb1ad00
    // 0xb19338: LoadField: r3 = r1->field_b
    //     0xb19338: ldur            w3, [x1, #0xb]
    // 0xb1933c: DecompressPointer r3
    //     0xb1933c: add             x3, x3, HEAP, lsl #32
    // 0xb19340: cmp             w3, NULL
    // 0xb19344: b.ne            #0xb19350
    // 0xb19348: r1 = Null
    //     0xb19348: mov             x1, NULL
    // 0xb1934c: b               #0xb193a4
    // 0xb19350: LoadField: r1 = r3->field_b
    //     0xb19350: ldur            w1, [x3, #0xb]
    // 0xb19354: DecompressPointer r1
    //     0xb19354: add             x1, x1, HEAP, lsl #32
    // 0xb19358: cmp             w1, NULL
    // 0xb1935c: b.ne            #0xb19368
    // 0xb19360: r1 = Null
    //     0xb19360: mov             x1, NULL
    // 0xb19364: b               #0xb193a4
    // 0xb19368: LoadField: r3 = r1->field_7
    //     0xb19368: ldur            w3, [x1, #7]
    // 0xb1936c: DecompressPointer r3
    //     0xb1936c: add             x3, x3, HEAP, lsl #32
    // 0xb19370: cmp             w3, NULL
    // 0xb19374: b.ne            #0xb19380
    // 0xb19378: r1 = Null
    //     0xb19378: mov             x1, NULL
    // 0xb1937c: b               #0xb193a4
    // 0xb19380: LoadField: r1 = r3->field_1b
    //     0xb19380: ldur            w1, [x3, #0x1b]
    // 0xb19384: DecompressPointer r1
    //     0xb19384: add             x1, x1, HEAP, lsl #32
    // 0xb19388: cmp             w1, NULL
    // 0xb1938c: b.ne            #0xb19398
    // 0xb19390: r1 = Null
    //     0xb19390: mov             x1, NULL
    // 0xb19394: b               #0xb193a4
    // 0xb19398: LoadField: r3 = r1->field_7
    //     0xb19398: ldur            w3, [x1, #7]
    // 0xb1939c: DecompressPointer r3
    //     0xb1939c: add             x3, x3, HEAP, lsl #32
    // 0xb193a0: mov             x1, x3
    // 0xb193a4: cmp             w1, NULL
    // 0xb193a8: b.ne            #0xb193b4
    // 0xb193ac: r5 = ""
    //     0xb193ac: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb193b0: b               #0xb193b8
    // 0xb193b4: mov             x5, x1
    // 0xb193b8: ldur            x4, [fp, #-0x38]
    // 0xb193bc: ldur            x3, [fp, #-0x70]
    // 0xb193c0: stur            x5, [fp, #-0x20]
    // 0xb193c4: r16 = Instance_Color
    //     0xb193c4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb193c8: r30 = 12.000000
    //     0xb193c8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb193cc: ldr             lr, [lr, #0x9e8]
    // 0xb193d0: stp             lr, x16, [SP]
    // 0xb193d4: ldur            x1, [fp, #-0x18]
    // 0xb193d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb193d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb193dc: ldr             x4, [x4, #0x9b8]
    // 0xb193e0: r0 = copyWith()
    //     0xb193e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb193e4: stur            x0, [fp, #-0x68]
    // 0xb193e8: r0 = Text()
    //     0xb193e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb193ec: mov             x3, x0
    // 0xb193f0: ldur            x0, [fp, #-0x20]
    // 0xb193f4: stur            x3, [fp, #-0x80]
    // 0xb193f8: StoreField: r3->field_b = r0
    //     0xb193f8: stur            w0, [x3, #0xb]
    // 0xb193fc: ldur            x0, [fp, #-0x68]
    // 0xb19400: StoreField: r3->field_13 = r0
    //     0xb19400: stur            w0, [x3, #0x13]
    // 0xb19404: r1 = Null
    //     0xb19404: mov             x1, NULL
    // 0xb19408: r2 = 6
    //     0xb19408: movz            x2, #0x6
    // 0xb1940c: r0 = AllocateArray()
    //     0xb1940c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19410: mov             x2, x0
    // 0xb19414: ldur            x0, [fp, #-0x70]
    // 0xb19418: stur            x2, [fp, #-0x20]
    // 0xb1941c: StoreField: r2->field_f = r0
    //     0xb1941c: stur            w0, [x2, #0xf]
    // 0xb19420: ldur            x0, [fp, #-0x78]
    // 0xb19424: StoreField: r2->field_13 = r0
    //     0xb19424: stur            w0, [x2, #0x13]
    // 0xb19428: ldur            x0, [fp, #-0x80]
    // 0xb1942c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1942c: stur            w0, [x2, #0x17]
    // 0xb19430: r1 = <Widget>
    //     0xb19430: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb19434: r0 = AllocateGrowableArray()
    //     0xb19434: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb19438: mov             x1, x0
    // 0xb1943c: ldur            x0, [fp, #-0x20]
    // 0xb19440: stur            x1, [fp, #-0x68]
    // 0xb19444: StoreField: r1->field_f = r0
    //     0xb19444: stur            w0, [x1, #0xf]
    // 0xb19448: r2 = 6
    //     0xb19448: movz            x2, #0x6
    // 0xb1944c: StoreField: r1->field_b = r2
    //     0xb1944c: stur            w2, [x1, #0xb]
    // 0xb19450: r0 = Column()
    //     0xb19450: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb19454: mov             x2, x0
    // 0xb19458: r0 = Instance_Axis
    //     0xb19458: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1945c: stur            x2, [fp, #-0x20]
    // 0xb19460: StoreField: r2->field_f = r0
    //     0xb19460: stur            w0, [x2, #0xf]
    // 0xb19464: r3 = Instance_MainAxisAlignment
    //     0xb19464: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb19468: ldr             x3, [x3, #0xa08]
    // 0xb1946c: StoreField: r2->field_13 = r3
    //     0xb1946c: stur            w3, [x2, #0x13]
    // 0xb19470: r4 = Instance_MainAxisSize
    //     0xb19470: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb19474: ldr             x4, [x4, #0xa10]
    // 0xb19478: ArrayStore: r2[0] = r4  ; List_4
    //     0xb19478: stur            w4, [x2, #0x17]
    // 0xb1947c: r5 = Instance_CrossAxisAlignment
    //     0xb1947c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb19480: ldr             x5, [x5, #0x890]
    // 0xb19484: StoreField: r2->field_1b = r5
    //     0xb19484: stur            w5, [x2, #0x1b]
    // 0xb19488: r6 = Instance_VerticalDirection
    //     0xb19488: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1948c: ldr             x6, [x6, #0xa20]
    // 0xb19490: StoreField: r2->field_23 = r6
    //     0xb19490: stur            w6, [x2, #0x23]
    // 0xb19494: r7 = Instance_Clip
    //     0xb19494: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb19498: ldr             x7, [x7, #0x38]
    // 0xb1949c: StoreField: r2->field_2b = r7
    //     0xb1949c: stur            w7, [x2, #0x2b]
    // 0xb194a0: StoreField: r2->field_2f = rZR
    //     0xb194a0: stur            xzr, [x2, #0x2f]
    // 0xb194a4: ldur            x1, [fp, #-0x68]
    // 0xb194a8: StoreField: r2->field_b = r1
    //     0xb194a8: stur            w1, [x2, #0xb]
    // 0xb194ac: r1 = <FlexParentData>
    //     0xb194ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb194b0: ldr             x1, [x1, #0xe00]
    // 0xb194b4: r0 = Expanded()
    //     0xb194b4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb194b8: mov             x3, x0
    // 0xb194bc: r0 = 1
    //     0xb194bc: movz            x0, #0x1
    // 0xb194c0: stur            x3, [fp, #-0x68]
    // 0xb194c4: StoreField: r3->field_13 = r0
    //     0xb194c4: stur            x0, [x3, #0x13]
    // 0xb194c8: r4 = Instance_FlexFit
    //     0xb194c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb194cc: ldr             x4, [x4, #0xe08]
    // 0xb194d0: StoreField: r3->field_1b = r4
    //     0xb194d0: stur            w4, [x3, #0x1b]
    // 0xb194d4: ldur            x1, [fp, #-0x20]
    // 0xb194d8: StoreField: r3->field_b = r1
    //     0xb194d8: stur            w1, [x3, #0xb]
    // 0xb194dc: r1 = Null
    //     0xb194dc: mov             x1, NULL
    // 0xb194e0: r2 = 6
    //     0xb194e0: movz            x2, #0x6
    // 0xb194e4: r0 = AllocateArray()
    //     0xb194e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb194e8: mov             x2, x0
    // 0xb194ec: ldur            x0, [fp, #-0x38]
    // 0xb194f0: stur            x2, [fp, #-0x20]
    // 0xb194f4: StoreField: r2->field_f = r0
    //     0xb194f4: stur            w0, [x2, #0xf]
    // 0xb194f8: r16 = Instance_SizedBox
    //     0xb194f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb194fc: ldr             x16, [x16, #0xb20]
    // 0xb19500: StoreField: r2->field_13 = r16
    //     0xb19500: stur            w16, [x2, #0x13]
    // 0xb19504: ldur            x0, [fp, #-0x68]
    // 0xb19508: ArrayStore: r2[0] = r0  ; List_4
    //     0xb19508: stur            w0, [x2, #0x17]
    // 0xb1950c: r1 = <Widget>
    //     0xb1950c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb19510: r0 = AllocateGrowableArray()
    //     0xb19510: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb19514: mov             x1, x0
    // 0xb19518: ldur            x0, [fp, #-0x20]
    // 0xb1951c: stur            x1, [fp, #-0x38]
    // 0xb19520: StoreField: r1->field_f = r0
    //     0xb19520: stur            w0, [x1, #0xf]
    // 0xb19524: r2 = 6
    //     0xb19524: movz            x2, #0x6
    // 0xb19528: StoreField: r1->field_b = r2
    //     0xb19528: stur            w2, [x1, #0xb]
    // 0xb1952c: r0 = Row()
    //     0xb1952c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb19530: mov             x1, x0
    // 0xb19534: r0 = Instance_Axis
    //     0xb19534: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb19538: stur            x1, [fp, #-0x20]
    // 0xb1953c: StoreField: r1->field_f = r0
    //     0xb1953c: stur            w0, [x1, #0xf]
    // 0xb19540: r2 = Instance_MainAxisAlignment
    //     0xb19540: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb19544: ldr             x2, [x2, #0xa08]
    // 0xb19548: StoreField: r1->field_13 = r2
    //     0xb19548: stur            w2, [x1, #0x13]
    // 0xb1954c: r3 = Instance_MainAxisSize
    //     0xb1954c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb19550: ldr             x3, [x3, #0xa10]
    // 0xb19554: ArrayStore: r1[0] = r3  ; List_4
    //     0xb19554: stur            w3, [x1, #0x17]
    // 0xb19558: r4 = Instance_CrossAxisAlignment
    //     0xb19558: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1955c: ldr             x4, [x4, #0xa18]
    // 0xb19560: StoreField: r1->field_1b = r4
    //     0xb19560: stur            w4, [x1, #0x1b]
    // 0xb19564: r5 = Instance_VerticalDirection
    //     0xb19564: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb19568: ldr             x5, [x5, #0xa20]
    // 0xb1956c: StoreField: r1->field_23 = r5
    //     0xb1956c: stur            w5, [x1, #0x23]
    // 0xb19570: r6 = Instance_Clip
    //     0xb19570: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb19574: ldr             x6, [x6, #0x38]
    // 0xb19578: StoreField: r1->field_2b = r6
    //     0xb19578: stur            w6, [x1, #0x2b]
    // 0xb1957c: StoreField: r1->field_2f = rZR
    //     0xb1957c: stur            xzr, [x1, #0x2f]
    // 0xb19580: ldur            x7, [fp, #-0x38]
    // 0xb19584: StoreField: r1->field_b = r7
    //     0xb19584: stur            w7, [x1, #0xb]
    // 0xb19588: r0 = Padding()
    //     0xb19588: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1958c: mov             x1, x0
    // 0xb19590: r0 = Instance_EdgeInsets
    //     0xb19590: add             x0, PP, #0x34, lsl #12  ; [pp+0x340b8] Obj!EdgeInsets@d57981
    //     0xb19594: ldr             x0, [x0, #0xb8]
    // 0xb19598: stur            x1, [fp, #-0x38]
    // 0xb1959c: StoreField: r1->field_f = r0
    //     0xb1959c: stur            w0, [x1, #0xf]
    // 0xb195a0: ldur            x0, [fp, #-0x20]
    // 0xb195a4: StoreField: r1->field_b = r0
    //     0xb195a4: stur            w0, [x1, #0xb]
    // 0xb195a8: r0 = Container()
    //     0xb195a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb195ac: stur            x0, [fp, #-0x20]
    // 0xb195b0: ldur            x16, [fp, #-0x60]
    // 0xb195b4: ldur            lr, [fp, #-0x38]
    // 0xb195b8: stp             lr, x16, [SP]
    // 0xb195bc: mov             x1, x0
    // 0xb195c0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb195c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb195c4: ldr             x4, [x4, #0x88]
    // 0xb195c8: r0 = Container()
    //     0xb195c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb195cc: r0 = Radius()
    //     0xb195cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb195d0: d0 = 12.000000
    //     0xb195d0: fmov            d0, #12.00000000
    // 0xb195d4: stur            x0, [fp, #-0x38]
    // 0xb195d8: StoreField: r0->field_7 = d0
    //     0xb195d8: stur            d0, [x0, #7]
    // 0xb195dc: StoreField: r0->field_f = d0
    //     0xb195dc: stur            d0, [x0, #0xf]
    // 0xb195e0: r0 = BorderRadius()
    //     0xb195e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb195e4: mov             x3, x0
    // 0xb195e8: ldur            x0, [fp, #-0x38]
    // 0xb195ec: stur            x3, [fp, #-0x60]
    // 0xb195f0: StoreField: r3->field_7 = r0
    //     0xb195f0: stur            w0, [x3, #7]
    // 0xb195f4: StoreField: r3->field_b = r0
    //     0xb195f4: stur            w0, [x3, #0xb]
    // 0xb195f8: StoreField: r3->field_f = r0
    //     0xb195f8: stur            w0, [x3, #0xf]
    // 0xb195fc: StoreField: r3->field_13 = r0
    //     0xb195fc: stur            w0, [x3, #0x13]
    // 0xb19600: ldur            x0, [fp, #-8]
    // 0xb19604: LoadField: r1 = r0->field_b
    //     0xb19604: ldur            w1, [x0, #0xb]
    // 0xb19608: DecompressPointer r1
    //     0xb19608: add             x1, x1, HEAP, lsl #32
    // 0xb1960c: cmp             w1, NULL
    // 0xb19610: b.eq            #0xb1ad04
    // 0xb19614: LoadField: r2 = r1->field_13
    //     0xb19614: ldur            w2, [x1, #0x13]
    // 0xb19618: DecompressPointer r2
    //     0xb19618: add             x2, x2, HEAP, lsl #32
    // 0xb1961c: cmp             w2, NULL
    // 0xb19620: b.ne            #0xb1962c
    // 0xb19624: r4 = ""
    //     0xb19624: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19628: b               #0xb19630
    // 0xb1962c: mov             x4, x2
    // 0xb19630: stur            x4, [fp, #-0x38]
    // 0xb19634: r1 = Function '<anonymous closure>':.
    //     0xb19634: add             x1, PP, #0x58, lsl #12  ; [pp+0x58488] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb19638: ldr             x1, [x1, #0x488]
    // 0xb1963c: r2 = Null
    //     0xb1963c: mov             x2, NULL
    // 0xb19640: r0 = AllocateClosure()
    //     0xb19640: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb19644: stur            x0, [fp, #-0x68]
    // 0xb19648: r0 = CachedNetworkImage()
    //     0xb19648: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb1964c: stur            x0, [fp, #-0x70]
    // 0xb19650: r16 = Instance_BoxFit
    //     0xb19650: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb19654: ldr             x16, [x16, #0x118]
    // 0xb19658: r30 = 56.000000
    //     0xb19658: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb1965c: ldr             lr, [lr, #0xb78]
    // 0xb19660: stp             lr, x16, [SP, #0x10]
    // 0xb19664: r16 = 56.000000
    //     0xb19664: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb19668: ldr             x16, [x16, #0xb78]
    // 0xb1966c: ldur            lr, [fp, #-0x68]
    // 0xb19670: stp             lr, x16, [SP]
    // 0xb19674: mov             x1, x0
    // 0xb19678: ldur            x2, [fp, #-0x38]
    // 0xb1967c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xb1967c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xb19680: ldr             x4, [x4, #0xbf8]
    // 0xb19684: r0 = CachedNetworkImage()
    //     0xb19684: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb19688: r0 = ClipRRect()
    //     0xb19688: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb1968c: mov             x2, x0
    // 0xb19690: ldur            x0, [fp, #-0x60]
    // 0xb19694: stur            x2, [fp, #-0x68]
    // 0xb19698: StoreField: r2->field_f = r0
    //     0xb19698: stur            w0, [x2, #0xf]
    // 0xb1969c: r0 = Instance_Clip
    //     0xb1969c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb196a0: ldr             x0, [x0, #0x138]
    // 0xb196a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb196a4: stur            w0, [x2, #0x17]
    // 0xb196a8: ldur            x0, [fp, #-0x70]
    // 0xb196ac: StoreField: r2->field_b = r0
    //     0xb196ac: stur            w0, [x2, #0xb]
    // 0xb196b0: ldur            x0, [fp, #-8]
    // 0xb196b4: LoadField: r1 = r0->field_b
    //     0xb196b4: ldur            w1, [x0, #0xb]
    // 0xb196b8: DecompressPointer r1
    //     0xb196b8: add             x1, x1, HEAP, lsl #32
    // 0xb196bc: cmp             w1, NULL
    // 0xb196c0: b.eq            #0xb1ad08
    // 0xb196c4: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb196c4: ldur            w3, [x1, #0x17]
    // 0xb196c8: DecompressPointer r3
    //     0xb196c8: add             x3, x3, HEAP, lsl #32
    // 0xb196cc: cmp             w3, NULL
    // 0xb196d0: b.ne            #0xb196dc
    // 0xb196d4: r1 = Null
    //     0xb196d4: mov             x1, NULL
    // 0xb196d8: b               #0xb196e4
    // 0xb196dc: LoadField: r1 = r3->field_f
    //     0xb196dc: ldur            w1, [x3, #0xf]
    // 0xb196e0: DecompressPointer r1
    //     0xb196e0: add             x1, x1, HEAP, lsl #32
    // 0xb196e4: cmp             w1, NULL
    // 0xb196e8: b.ne            #0xb196f4
    // 0xb196ec: r3 = ""
    //     0xb196ec: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb196f0: b               #0xb196f8
    // 0xb196f4: mov             x3, x1
    // 0xb196f8: stur            x3, [fp, #-0x38]
    // 0xb196fc: r16 = Instance_Color
    //     0xb196fc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb19700: r30 = 12.000000
    //     0xb19700: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19704: ldr             lr, [lr, #0x9e8]
    // 0xb19708: stp             lr, x16, [SP]
    // 0xb1970c: ldur            x1, [fp, #-0x18]
    // 0xb19710: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19710: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19714: ldr             x4, [x4, #0x9b8]
    // 0xb19718: r0 = copyWith()
    //     0xb19718: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1971c: stur            x0, [fp, #-0x60]
    // 0xb19720: r0 = Text()
    //     0xb19720: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb19724: mov             x3, x0
    // 0xb19728: ldur            x0, [fp, #-0x38]
    // 0xb1972c: stur            x3, [fp, #-0x70]
    // 0xb19730: StoreField: r3->field_b = r0
    //     0xb19730: stur            w0, [x3, #0xb]
    // 0xb19734: ldur            x0, [fp, #-0x60]
    // 0xb19738: StoreField: r3->field_13 = r0
    //     0xb19738: stur            w0, [x3, #0x13]
    // 0xb1973c: r0 = 2
    //     0xb1973c: movz            x0, #0x2
    // 0xb19740: StoreField: r3->field_37 = r0
    //     0xb19740: stur            w0, [x3, #0x37]
    // 0xb19744: r1 = Null
    //     0xb19744: mov             x1, NULL
    // 0xb19748: r2 = 8
    //     0xb19748: movz            x2, #0x8
    // 0xb1974c: r0 = AllocateArray()
    //     0xb1974c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19750: r16 = "Size: "
    //     0xb19750: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb19754: ldr             x16, [x16, #0xf00]
    // 0xb19758: StoreField: r0->field_f = r16
    //     0xb19758: stur            w16, [x0, #0xf]
    // 0xb1975c: ldur            x1, [fp, #-8]
    // 0xb19760: LoadField: r2 = r1->field_b
    //     0xb19760: ldur            w2, [x1, #0xb]
    // 0xb19764: DecompressPointer r2
    //     0xb19764: add             x2, x2, HEAP, lsl #32
    // 0xb19768: cmp             w2, NULL
    // 0xb1976c: b.eq            #0xb1ad0c
    // 0xb19770: LoadField: r3 = r2->field_f
    //     0xb19770: ldur            w3, [x2, #0xf]
    // 0xb19774: DecompressPointer r3
    //     0xb19774: add             x3, x3, HEAP, lsl #32
    // 0xb19778: cmp             w3, NULL
    // 0xb1977c: b.ne            #0xb19788
    // 0xb19780: r3 = Null
    //     0xb19780: mov             x3, NULL
    // 0xb19784: b               #0xb19794
    // 0xb19788: LoadField: r4 = r3->field_1f
    //     0xb19788: ldur            w4, [x3, #0x1f]
    // 0xb1978c: DecompressPointer r4
    //     0xb1978c: add             x4, x4, HEAP, lsl #32
    // 0xb19790: mov             x3, x4
    // 0xb19794: cmp             w3, NULL
    // 0xb19798: b.ne            #0xb197a0
    // 0xb1979c: r3 = ""
    //     0xb1979c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb197a0: StoreField: r0->field_13 = r3
    //     0xb197a0: stur            w3, [x0, #0x13]
    // 0xb197a4: r16 = " / Qty: "
    //     0xb197a4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb197a8: ldr             x16, [x16, #0x760]
    // 0xb197ac: ArrayStore: r0[0] = r16  ; List_4
    //     0xb197ac: stur            w16, [x0, #0x17]
    // 0xb197b0: LoadField: r3 = r2->field_1b
    //     0xb197b0: ldur            w3, [x2, #0x1b]
    // 0xb197b4: DecompressPointer r3
    //     0xb197b4: add             x3, x3, HEAP, lsl #32
    // 0xb197b8: StoreField: r0->field_1b = r3
    //     0xb197b8: stur            w3, [x0, #0x1b]
    // 0xb197bc: str             x0, [SP]
    // 0xb197c0: r0 = _interpolate()
    //     0xb197c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb197c4: stur            x0, [fp, #-0x38]
    // 0xb197c8: r16 = Instance_Color
    //     0xb197c8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb197cc: r30 = 12.000000
    //     0xb197cc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb197d0: ldr             lr, [lr, #0x9e8]
    // 0xb197d4: stp             lr, x16, [SP]
    // 0xb197d8: ldur            x1, [fp, #-0x30]
    // 0xb197dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb197dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb197e0: ldr             x4, [x4, #0x9b8]
    // 0xb197e4: r0 = copyWith()
    //     0xb197e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb197e8: stur            x0, [fp, #-0x60]
    // 0xb197ec: r0 = Text()
    //     0xb197ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb197f0: mov             x2, x0
    // 0xb197f4: ldur            x0, [fp, #-0x38]
    // 0xb197f8: stur            x2, [fp, #-0x78]
    // 0xb197fc: StoreField: r2->field_b = r0
    //     0xb197fc: stur            w0, [x2, #0xb]
    // 0xb19800: ldur            x0, [fp, #-0x60]
    // 0xb19804: StoreField: r2->field_13 = r0
    //     0xb19804: stur            w0, [x2, #0x13]
    // 0xb19808: ldur            x0, [fp, #-8]
    // 0xb1980c: LoadField: r1 = r0->field_b
    //     0xb1980c: ldur            w1, [x0, #0xb]
    // 0xb19810: DecompressPointer r1
    //     0xb19810: add             x1, x1, HEAP, lsl #32
    // 0xb19814: cmp             w1, NULL
    // 0xb19818: b.eq            #0xb1ad10
    // 0xb1981c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb1981c: ldur            w3, [x1, #0x17]
    // 0xb19820: DecompressPointer r3
    //     0xb19820: add             x3, x3, HEAP, lsl #32
    // 0xb19824: cmp             w3, NULL
    // 0xb19828: b.ne            #0xb19834
    // 0xb1982c: r1 = Null
    //     0xb1982c: mov             x1, NULL
    // 0xb19830: b               #0xb1983c
    // 0xb19834: LoadField: r1 = r3->field_43
    //     0xb19834: ldur            w1, [x3, #0x43]
    // 0xb19838: DecompressPointer r1
    //     0xb19838: add             x1, x1, HEAP, lsl #32
    // 0xb1983c: cmp             w1, NULL
    // 0xb19840: b.ne            #0xb1984c
    // 0xb19844: r6 = ""
    //     0xb19844: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19848: b               #0xb19850
    // 0xb1984c: mov             x6, x1
    // 0xb19850: ldur            x5, [fp, #-0x20]
    // 0xb19854: ldur            x4, [fp, #-0x68]
    // 0xb19858: ldur            x3, [fp, #-0x70]
    // 0xb1985c: stur            x6, [fp, #-0x38]
    // 0xb19860: r16 = Instance_Color
    //     0xb19860: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb19864: r30 = 12.000000
    //     0xb19864: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19868: ldr             lr, [lr, #0x9e8]
    // 0xb1986c: stp             lr, x16, [SP]
    // 0xb19870: ldur            x1, [fp, #-0x18]
    // 0xb19874: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19874: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19878: ldr             x4, [x4, #0x9b8]
    // 0xb1987c: r0 = copyWith()
    //     0xb1987c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb19880: stur            x0, [fp, #-0x60]
    // 0xb19884: r0 = Text()
    //     0xb19884: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb19888: mov             x3, x0
    // 0xb1988c: ldur            x0, [fp, #-0x38]
    // 0xb19890: stur            x3, [fp, #-0x80]
    // 0xb19894: StoreField: r3->field_b = r0
    //     0xb19894: stur            w0, [x3, #0xb]
    // 0xb19898: ldur            x0, [fp, #-0x60]
    // 0xb1989c: StoreField: r3->field_13 = r0
    //     0xb1989c: stur            w0, [x3, #0x13]
    // 0xb198a0: r1 = Null
    //     0xb198a0: mov             x1, NULL
    // 0xb198a4: r2 = 6
    //     0xb198a4: movz            x2, #0x6
    // 0xb198a8: r0 = AllocateArray()
    //     0xb198a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb198ac: mov             x2, x0
    // 0xb198b0: ldur            x0, [fp, #-0x70]
    // 0xb198b4: stur            x2, [fp, #-0x38]
    // 0xb198b8: StoreField: r2->field_f = r0
    //     0xb198b8: stur            w0, [x2, #0xf]
    // 0xb198bc: ldur            x0, [fp, #-0x78]
    // 0xb198c0: StoreField: r2->field_13 = r0
    //     0xb198c0: stur            w0, [x2, #0x13]
    // 0xb198c4: ldur            x0, [fp, #-0x80]
    // 0xb198c8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb198c8: stur            w0, [x2, #0x17]
    // 0xb198cc: r1 = <Widget>
    //     0xb198cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb198d0: r0 = AllocateGrowableArray()
    //     0xb198d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb198d4: mov             x1, x0
    // 0xb198d8: ldur            x0, [fp, #-0x38]
    // 0xb198dc: stur            x1, [fp, #-0x60]
    // 0xb198e0: StoreField: r1->field_f = r0
    //     0xb198e0: stur            w0, [x1, #0xf]
    // 0xb198e4: r2 = 6
    //     0xb198e4: movz            x2, #0x6
    // 0xb198e8: StoreField: r1->field_b = r2
    //     0xb198e8: stur            w2, [x1, #0xb]
    // 0xb198ec: r0 = Column()
    //     0xb198ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb198f0: mov             x2, x0
    // 0xb198f4: r0 = Instance_Axis
    //     0xb198f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb198f8: stur            x2, [fp, #-0x38]
    // 0xb198fc: StoreField: r2->field_f = r0
    //     0xb198fc: stur            w0, [x2, #0xf]
    // 0xb19900: r3 = Instance_MainAxisAlignment
    //     0xb19900: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb19904: ldr             x3, [x3, #0xa08]
    // 0xb19908: StoreField: r2->field_13 = r3
    //     0xb19908: stur            w3, [x2, #0x13]
    // 0xb1990c: r4 = Instance_MainAxisSize
    //     0xb1990c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb19910: ldr             x4, [x4, #0xa10]
    // 0xb19914: ArrayStore: r2[0] = r4  ; List_4
    //     0xb19914: stur            w4, [x2, #0x17]
    // 0xb19918: r5 = Instance_CrossAxisAlignment
    //     0xb19918: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1991c: ldr             x5, [x5, #0x890]
    // 0xb19920: StoreField: r2->field_1b = r5
    //     0xb19920: stur            w5, [x2, #0x1b]
    // 0xb19924: r6 = Instance_VerticalDirection
    //     0xb19924: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb19928: ldr             x6, [x6, #0xa20]
    // 0xb1992c: StoreField: r2->field_23 = r6
    //     0xb1992c: stur            w6, [x2, #0x23]
    // 0xb19930: r7 = Instance_Clip
    //     0xb19930: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb19934: ldr             x7, [x7, #0x38]
    // 0xb19938: StoreField: r2->field_2b = r7
    //     0xb19938: stur            w7, [x2, #0x2b]
    // 0xb1993c: StoreField: r2->field_2f = rZR
    //     0xb1993c: stur            xzr, [x2, #0x2f]
    // 0xb19940: ldur            x1, [fp, #-0x60]
    // 0xb19944: StoreField: r2->field_b = r1
    //     0xb19944: stur            w1, [x2, #0xb]
    // 0xb19948: r1 = <FlexParentData>
    //     0xb19948: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb1994c: ldr             x1, [x1, #0xe00]
    // 0xb19950: r0 = Expanded()
    //     0xb19950: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb19954: mov             x3, x0
    // 0xb19958: r0 = 1
    //     0xb19958: movz            x0, #0x1
    // 0xb1995c: stur            x3, [fp, #-0x60]
    // 0xb19960: StoreField: r3->field_13 = r0
    //     0xb19960: stur            x0, [x3, #0x13]
    // 0xb19964: r4 = Instance_FlexFit
    //     0xb19964: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb19968: ldr             x4, [x4, #0xe08]
    // 0xb1996c: StoreField: r3->field_1b = r4
    //     0xb1996c: stur            w4, [x3, #0x1b]
    // 0xb19970: ldur            x1, [fp, #-0x38]
    // 0xb19974: StoreField: r3->field_b = r1
    //     0xb19974: stur            w1, [x3, #0xb]
    // 0xb19978: r1 = Null
    //     0xb19978: mov             x1, NULL
    // 0xb1997c: r2 = 6
    //     0xb1997c: movz            x2, #0x6
    // 0xb19980: r0 = AllocateArray()
    //     0xb19980: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19984: mov             x2, x0
    // 0xb19988: ldur            x0, [fp, #-0x68]
    // 0xb1998c: stur            x2, [fp, #-0x38]
    // 0xb19990: StoreField: r2->field_f = r0
    //     0xb19990: stur            w0, [x2, #0xf]
    // 0xb19994: r16 = Instance_SizedBox
    //     0xb19994: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb19998: ldr             x16, [x16, #0xb20]
    // 0xb1999c: StoreField: r2->field_13 = r16
    //     0xb1999c: stur            w16, [x2, #0x13]
    // 0xb199a0: ldur            x0, [fp, #-0x60]
    // 0xb199a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb199a4: stur            w0, [x2, #0x17]
    // 0xb199a8: r1 = <Widget>
    //     0xb199a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb199ac: r0 = AllocateGrowableArray()
    //     0xb199ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb199b0: mov             x1, x0
    // 0xb199b4: ldur            x0, [fp, #-0x38]
    // 0xb199b8: stur            x1, [fp, #-0x60]
    // 0xb199bc: StoreField: r1->field_f = r0
    //     0xb199bc: stur            w0, [x1, #0xf]
    // 0xb199c0: r2 = 6
    //     0xb199c0: movz            x2, #0x6
    // 0xb199c4: StoreField: r1->field_b = r2
    //     0xb199c4: stur            w2, [x1, #0xb]
    // 0xb199c8: r0 = Row()
    //     0xb199c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb199cc: mov             x1, x0
    // 0xb199d0: r0 = Instance_Axis
    //     0xb199d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb199d4: stur            x1, [fp, #-0x38]
    // 0xb199d8: StoreField: r1->field_f = r0
    //     0xb199d8: stur            w0, [x1, #0xf]
    // 0xb199dc: r2 = Instance_MainAxisAlignment
    //     0xb199dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb199e0: ldr             x2, [x2, #0xa08]
    // 0xb199e4: StoreField: r1->field_13 = r2
    //     0xb199e4: stur            w2, [x1, #0x13]
    // 0xb199e8: r3 = Instance_MainAxisSize
    //     0xb199e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb199ec: ldr             x3, [x3, #0xa10]
    // 0xb199f0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb199f0: stur            w3, [x1, #0x17]
    // 0xb199f4: r4 = Instance_CrossAxisAlignment
    //     0xb199f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb199f8: ldr             x4, [x4, #0xa18]
    // 0xb199fc: StoreField: r1->field_1b = r4
    //     0xb199fc: stur            w4, [x1, #0x1b]
    // 0xb19a00: r5 = Instance_VerticalDirection
    //     0xb19a00: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb19a04: ldr             x5, [x5, #0xa20]
    // 0xb19a08: StoreField: r1->field_23 = r5
    //     0xb19a08: stur            w5, [x1, #0x23]
    // 0xb19a0c: r6 = Instance_Clip
    //     0xb19a0c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb19a10: ldr             x6, [x6, #0x38]
    // 0xb19a14: StoreField: r1->field_2b = r6
    //     0xb19a14: stur            w6, [x1, #0x2b]
    // 0xb19a18: StoreField: r1->field_2f = rZR
    //     0xb19a18: stur            xzr, [x1, #0x2f]
    // 0xb19a1c: ldur            x7, [fp, #-0x60]
    // 0xb19a20: StoreField: r1->field_b = r7
    //     0xb19a20: stur            w7, [x1, #0xb]
    // 0xb19a24: r0 = Padding()
    //     0xb19a24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb19a28: mov             x3, x0
    // 0xb19a2c: r0 = Instance_EdgeInsets
    //     0xb19a2c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fc30] Obj!EdgeInsets@d57891
    //     0xb19a30: ldr             x0, [x0, #0xc30]
    // 0xb19a34: stur            x3, [fp, #-0x60]
    // 0xb19a38: StoreField: r3->field_f = r0
    //     0xb19a38: stur            w0, [x3, #0xf]
    // 0xb19a3c: ldur            x0, [fp, #-0x38]
    // 0xb19a40: StoreField: r3->field_b = r0
    //     0xb19a40: stur            w0, [x3, #0xb]
    // 0xb19a44: r1 = Null
    //     0xb19a44: mov             x1, NULL
    // 0xb19a48: r2 = 4
    //     0xb19a48: movz            x2, #0x4
    // 0xb19a4c: r0 = AllocateArray()
    //     0xb19a4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19a50: mov             x2, x0
    // 0xb19a54: ldur            x0, [fp, #-0x20]
    // 0xb19a58: stur            x2, [fp, #-0x38]
    // 0xb19a5c: StoreField: r2->field_f = r0
    //     0xb19a5c: stur            w0, [x2, #0xf]
    // 0xb19a60: ldur            x0, [fp, #-0x60]
    // 0xb19a64: StoreField: r2->field_13 = r0
    //     0xb19a64: stur            w0, [x2, #0x13]
    // 0xb19a68: r1 = <Widget>
    //     0xb19a68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb19a6c: r0 = AllocateGrowableArray()
    //     0xb19a6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb19a70: mov             x1, x0
    // 0xb19a74: ldur            x0, [fp, #-0x38]
    // 0xb19a78: stur            x1, [fp, #-0x20]
    // 0xb19a7c: StoreField: r1->field_f = r0
    //     0xb19a7c: stur            w0, [x1, #0xf]
    // 0xb19a80: r2 = 4
    //     0xb19a80: movz            x2, #0x4
    // 0xb19a84: StoreField: r1->field_b = r2
    //     0xb19a84: stur            w2, [x1, #0xb]
    // 0xb19a88: r0 = Column()
    //     0xb19a88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb19a8c: mov             x1, x0
    // 0xb19a90: r0 = Instance_Axis
    //     0xb19a90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb19a94: stur            x1, [fp, #-0x38]
    // 0xb19a98: StoreField: r1->field_f = r0
    //     0xb19a98: stur            w0, [x1, #0xf]
    // 0xb19a9c: r2 = Instance_MainAxisAlignment
    //     0xb19a9c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb19aa0: ldr             x2, [x2, #0xa08]
    // 0xb19aa4: StoreField: r1->field_13 = r2
    //     0xb19aa4: stur            w2, [x1, #0x13]
    // 0xb19aa8: r3 = Instance_MainAxisSize
    //     0xb19aa8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb19aac: ldr             x3, [x3, #0xa10]
    // 0xb19ab0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb19ab0: stur            w3, [x1, #0x17]
    // 0xb19ab4: r4 = Instance_CrossAxisAlignment
    //     0xb19ab4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb19ab8: ldr             x4, [x4, #0xa18]
    // 0xb19abc: StoreField: r1->field_1b = r4
    //     0xb19abc: stur            w4, [x1, #0x1b]
    // 0xb19ac0: r5 = Instance_VerticalDirection
    //     0xb19ac0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb19ac4: ldr             x5, [x5, #0xa20]
    // 0xb19ac8: StoreField: r1->field_23 = r5
    //     0xb19ac8: stur            w5, [x1, #0x23]
    // 0xb19acc: r6 = Instance_Clip
    //     0xb19acc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb19ad0: ldr             x6, [x6, #0x38]
    // 0xb19ad4: StoreField: r1->field_2b = r6
    //     0xb19ad4: stur            w6, [x1, #0x2b]
    // 0xb19ad8: StoreField: r1->field_2f = rZR
    //     0xb19ad8: stur            xzr, [x1, #0x2f]
    // 0xb19adc: ldur            x7, [fp, #-0x20]
    // 0xb19ae0: StoreField: r1->field_b = r7
    //     0xb19ae0: stur            w7, [x1, #0xb]
    // 0xb19ae4: r0 = SvgPicture()
    //     0xb19ae4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb19ae8: stur            x0, [fp, #-0x20]
    // 0xb19aec: r16 = Instance_BoxFit
    //     0xb19aec: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb19af0: ldr             x16, [x16, #0xb18]
    // 0xb19af4: str             x16, [SP]
    // 0xb19af8: mov             x1, x0
    // 0xb19afc: r2 = "assets/images/product_between_icon.svg"
    //     0xb19afc: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "assets/images/product_between_icon.svg"
    //     0xb19b00: ldr             x2, [x2, #0xf28]
    // 0xb19b04: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb19b04: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb19b08: ldr             x4, [x4, #0xb0]
    // 0xb19b0c: r0 = SvgPicture.asset()
    //     0xb19b0c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb19b10: r1 = <StackParentData>
    //     0xb19b10: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb19b14: ldr             x1, [x1, #0x8e0]
    // 0xb19b18: r0 = Positioned()
    //     0xb19b18: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb19b1c: mov             x3, x0
    // 0xb19b20: r0 = 0.000000
    //     0xb19b20: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb19b24: stur            x3, [fp, #-0x60]
    // 0xb19b28: StoreField: r3->field_13 = r0
    //     0xb19b28: stur            w0, [x3, #0x13]
    // 0xb19b2c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb19b2c: stur            w0, [x3, #0x17]
    // 0xb19b30: StoreField: r3->field_1b = r0
    //     0xb19b30: stur            w0, [x3, #0x1b]
    // 0xb19b34: StoreField: r3->field_1f = r0
    //     0xb19b34: stur            w0, [x3, #0x1f]
    // 0xb19b38: ldur            x0, [fp, #-0x20]
    // 0xb19b3c: StoreField: r3->field_b = r0
    //     0xb19b3c: stur            w0, [x3, #0xb]
    // 0xb19b40: r1 = Null
    //     0xb19b40: mov             x1, NULL
    // 0xb19b44: r2 = 4
    //     0xb19b44: movz            x2, #0x4
    // 0xb19b48: r0 = AllocateArray()
    //     0xb19b48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19b4c: mov             x2, x0
    // 0xb19b50: ldur            x0, [fp, #-0x38]
    // 0xb19b54: stur            x2, [fp, #-0x20]
    // 0xb19b58: StoreField: r2->field_f = r0
    //     0xb19b58: stur            w0, [x2, #0xf]
    // 0xb19b5c: ldur            x0, [fp, #-0x60]
    // 0xb19b60: StoreField: r2->field_13 = r0
    //     0xb19b60: stur            w0, [x2, #0x13]
    // 0xb19b64: r1 = <Widget>
    //     0xb19b64: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb19b68: r0 = AllocateGrowableArray()
    //     0xb19b68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb19b6c: mov             x1, x0
    // 0xb19b70: ldur            x0, [fp, #-0x20]
    // 0xb19b74: stur            x1, [fp, #-0x38]
    // 0xb19b78: StoreField: r1->field_f = r0
    //     0xb19b78: stur            w0, [x1, #0xf]
    // 0xb19b7c: r2 = 4
    //     0xb19b7c: movz            x2, #0x4
    // 0xb19b80: StoreField: r1->field_b = r2
    //     0xb19b80: stur            w2, [x1, #0xb]
    // 0xb19b84: r0 = Stack()
    //     0xb19b84: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb19b88: mov             x2, x0
    // 0xb19b8c: r0 = Instance_Alignment
    //     0xb19b8c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb19b90: ldr             x0, [x0, #0xb10]
    // 0xb19b94: stur            x2, [fp, #-0x20]
    // 0xb19b98: StoreField: r2->field_f = r0
    //     0xb19b98: stur            w0, [x2, #0xf]
    // 0xb19b9c: r0 = Instance_StackFit
    //     0xb19b9c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb19ba0: ldr             x0, [x0, #0xfa8]
    // 0xb19ba4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb19ba4: stur            w0, [x2, #0x17]
    // 0xb19ba8: r0 = Instance_Clip
    //     0xb19ba8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb19bac: ldr             x0, [x0, #0x7e0]
    // 0xb19bb0: StoreField: r2->field_1b = r0
    //     0xb19bb0: stur            w0, [x2, #0x1b]
    // 0xb19bb4: ldur            x0, [fp, #-0x38]
    // 0xb19bb8: StoreField: r2->field_b = r0
    //     0xb19bb8: stur            w0, [x2, #0xb]
    // 0xb19bbc: ldur            x3, [fp, #-8]
    // 0xb19bc0: LoadField: r0 = r3->field_b
    //     0xb19bc0: ldur            w0, [x3, #0xb]
    // 0xb19bc4: DecompressPointer r0
    //     0xb19bc4: add             x0, x0, HEAP, lsl #32
    // 0xb19bc8: cmp             w0, NULL
    // 0xb19bcc: b.eq            #0xb1ad14
    // 0xb19bd0: LoadField: r1 = r0->field_2b
    //     0xb19bd0: ldur            w1, [x0, #0x2b]
    // 0xb19bd4: DecompressPointer r1
    //     0xb19bd4: add             x1, x1, HEAP, lsl #32
    // 0xb19bd8: cmp             w1, NULL
    // 0xb19bdc: b.ne            #0xb19be8
    // 0xb19be0: r0 = Null
    //     0xb19be0: mov             x0, NULL
    // 0xb19be4: b               #0xb19c00
    // 0xb19be8: r0 = LoadClassIdInstr(r1)
    //     0xb19be8: ldur            x0, [x1, #-1]
    //     0xb19bec: ubfx            x0, x0, #0xc, #0x14
    // 0xb19bf0: r0 = GDT[cid_x0 + 0xe517]()
    //     0xb19bf0: movz            x17, #0xe517
    //     0xb19bf4: add             lr, x0, x17
    //     0xb19bf8: ldr             lr, [x21, lr, lsl #3]
    //     0xb19bfc: blr             lr
    // 0xb19c00: cmp             w0, NULL
    // 0xb19c04: b.ne            #0xb19c10
    // 0xb19c08: r1 = false
    //     0xb19c08: add             x1, NULL, #0x30  ; false
    // 0xb19c0c: b               #0xb19c14
    // 0xb19c10: mov             x1, x0
    // 0xb19c14: ldur            x0, [fp, #-8]
    // 0xb19c18: stur            x1, [fp, #-0x68]
    // 0xb19c1c: LoadField: r2 = r0->field_b
    //     0xb19c1c: ldur            w2, [x0, #0xb]
    // 0xb19c20: DecompressPointer r2
    //     0xb19c20: add             x2, x2, HEAP, lsl #32
    // 0xb19c24: cmp             w2, NULL
    // 0xb19c28: b.eq            #0xb1ad18
    // 0xb19c2c: LoadField: r3 = r2->field_2b
    //     0xb19c2c: ldur            w3, [x2, #0x2b]
    // 0xb19c30: DecompressPointer r3
    //     0xb19c30: add             x3, x3, HEAP, lsl #32
    // 0xb19c34: stur            x3, [fp, #-0x60]
    // 0xb19c38: LoadField: r4 = r2->field_27
    //     0xb19c38: ldur            w4, [x2, #0x27]
    // 0xb19c3c: DecompressPointer r4
    //     0xb19c3c: add             x4, x4, HEAP, lsl #32
    // 0xb19c40: stur            x4, [fp, #-0x38]
    // 0xb19c44: r0 = CustomisedStrip()
    //     0xb19c44: bl              #0xa078f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xb19c48: mov             x1, x0
    // 0xb19c4c: ldur            x0, [fp, #-0x60]
    // 0xb19c50: stur            x1, [fp, #-0x70]
    // 0xb19c54: StoreField: r1->field_b = r0
    //     0xb19c54: stur            w0, [x1, #0xb]
    // 0xb19c58: ldur            x0, [fp, #-0x38]
    // 0xb19c5c: StoreField: r1->field_13 = r0
    //     0xb19c5c: stur            w0, [x1, #0x13]
    // 0xb19c60: r0 = Padding()
    //     0xb19c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb19c64: mov             x1, x0
    // 0xb19c68: r0 = Instance_EdgeInsets
    //     0xb19c68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb19c6c: ldr             x0, [x0, #0x668]
    // 0xb19c70: stur            x1, [fp, #-0x38]
    // 0xb19c74: StoreField: r1->field_f = r0
    //     0xb19c74: stur            w0, [x1, #0xf]
    // 0xb19c78: ldur            x2, [fp, #-0x70]
    // 0xb19c7c: StoreField: r1->field_b = r2
    //     0xb19c7c: stur            w2, [x1, #0xb]
    // 0xb19c80: r0 = Visibility()
    //     0xb19c80: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb19c84: mov             x2, x0
    // 0xb19c88: ldur            x0, [fp, #-0x38]
    // 0xb19c8c: stur            x2, [fp, #-0x60]
    // 0xb19c90: StoreField: r2->field_b = r0
    //     0xb19c90: stur            w0, [x2, #0xb]
    // 0xb19c94: r0 = Instance_SizedBox
    //     0xb19c94: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb19c98: StoreField: r2->field_f = r0
    //     0xb19c98: stur            w0, [x2, #0xf]
    // 0xb19c9c: ldur            x0, [fp, #-0x68]
    // 0xb19ca0: StoreField: r2->field_13 = r0
    //     0xb19ca0: stur            w0, [x2, #0x13]
    // 0xb19ca4: r0 = false
    //     0xb19ca4: add             x0, NULL, #0x30  ; false
    // 0xb19ca8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb19ca8: stur            w0, [x2, #0x17]
    // 0xb19cac: StoreField: r2->field_1b = r0
    //     0xb19cac: stur            w0, [x2, #0x1b]
    // 0xb19cb0: StoreField: r2->field_1f = r0
    //     0xb19cb0: stur            w0, [x2, #0x1f]
    // 0xb19cb4: StoreField: r2->field_23 = r0
    //     0xb19cb4: stur            w0, [x2, #0x23]
    // 0xb19cb8: StoreField: r2->field_27 = r0
    //     0xb19cb8: stur            w0, [x2, #0x27]
    // 0xb19cbc: StoreField: r2->field_2b = r0
    //     0xb19cbc: stur            w0, [x2, #0x2b]
    // 0xb19cc0: r16 = Instance_Color
    //     0xb19cc0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb19cc4: r30 = 12.000000
    //     0xb19cc4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19cc8: ldr             lr, [lr, #0x9e8]
    // 0xb19ccc: stp             lr, x16, [SP]
    // 0xb19cd0: ldur            x1, [fp, #-0x30]
    // 0xb19cd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19cd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19cd8: ldr             x4, [x4, #0x9b8]
    // 0xb19cdc: r0 = copyWith()
    //     0xb19cdc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb19ce0: stur            x0, [fp, #-0x38]
    // 0xb19ce4: r0 = TextSpan()
    //     0xb19ce4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb19ce8: mov             x3, x0
    // 0xb19cec: r0 = "• Exchange credit will be added "
    //     0xb19cec: add             x0, PP, #0x53, lsl #12  ; [pp+0x539c8] "• Exchange credit will be added "
    //     0xb19cf0: ldr             x0, [x0, #0x9c8]
    // 0xb19cf4: stur            x3, [fp, #-0x68]
    // 0xb19cf8: StoreField: r3->field_b = r0
    //     0xb19cf8: stur            w0, [x3, #0xb]
    // 0xb19cfc: r0 = Instance__DeferringMouseCursor
    //     0xb19cfc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb19d00: ArrayStore: r3[0] = r0  ; List_4
    //     0xb19d00: stur            w0, [x3, #0x17]
    // 0xb19d04: ldur            x1, [fp, #-0x38]
    // 0xb19d08: StoreField: r3->field_7 = r1
    //     0xb19d08: stur            w1, [x3, #7]
    // 0xb19d0c: r1 = Null
    //     0xb19d0c: mov             x1, NULL
    // 0xb19d10: r2 = 4
    //     0xb19d10: movz            x2, #0x4
    // 0xb19d14: r0 = AllocateArray()
    //     0xb19d14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19d18: r16 = "worth "
    //     0xb19d18: add             x16, PP, #0x53, lsl #12  ; [pp+0x539d0] "worth "
    //     0xb19d1c: ldr             x16, [x16, #0x9d0]
    // 0xb19d20: StoreField: r0->field_f = r16
    //     0xb19d20: stur            w16, [x0, #0xf]
    // 0xb19d24: ldur            x1, [fp, #-8]
    // 0xb19d28: LoadField: r2 = r1->field_b
    //     0xb19d28: ldur            w2, [x1, #0xb]
    // 0xb19d2c: DecompressPointer r2
    //     0xb19d2c: add             x2, x2, HEAP, lsl #32
    // 0xb19d30: cmp             w2, NULL
    // 0xb19d34: b.eq            #0xb1ad1c
    // 0xb19d38: LoadField: r3 = r2->field_b
    //     0xb19d38: ldur            w3, [x2, #0xb]
    // 0xb19d3c: DecompressPointer r3
    //     0xb19d3c: add             x3, x3, HEAP, lsl #32
    // 0xb19d40: cmp             w3, NULL
    // 0xb19d44: b.ne            #0xb19d50
    // 0xb19d48: r2 = Null
    //     0xb19d48: mov             x2, NULL
    // 0xb19d4c: b               #0xb19d88
    // 0xb19d50: LoadField: r2 = r3->field_b
    //     0xb19d50: ldur            w2, [x3, #0xb]
    // 0xb19d54: DecompressPointer r2
    //     0xb19d54: add             x2, x2, HEAP, lsl #32
    // 0xb19d58: cmp             w2, NULL
    // 0xb19d5c: b.ne            #0xb19d68
    // 0xb19d60: r2 = Null
    //     0xb19d60: mov             x2, NULL
    // 0xb19d64: b               #0xb19d88
    // 0xb19d68: LoadField: r3 = r2->field_b
    //     0xb19d68: ldur            w3, [x2, #0xb]
    // 0xb19d6c: DecompressPointer r3
    //     0xb19d6c: add             x3, x3, HEAP, lsl #32
    // 0xb19d70: cmp             w3, NULL
    // 0xb19d74: b.ne            #0xb19d80
    // 0xb19d78: r2 = Null
    //     0xb19d78: mov             x2, NULL
    // 0xb19d7c: b               #0xb19d88
    // 0xb19d80: LoadField: r2 = r3->field_7
    //     0xb19d80: ldur            w2, [x3, #7]
    // 0xb19d84: DecompressPointer r2
    //     0xb19d84: add             x2, x2, HEAP, lsl #32
    // 0xb19d88: cmp             w2, NULL
    // 0xb19d8c: b.ne            #0xb19d98
    // 0xb19d90: r3 = ""
    //     0xb19d90: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19d94: b               #0xb19d9c
    // 0xb19d98: mov             x3, x2
    // 0xb19d9c: ldur            x2, [fp, #-0x68]
    // 0xb19da0: StoreField: r0->field_13 = r3
    //     0xb19da0: stur            w3, [x0, #0x13]
    // 0xb19da4: str             x0, [SP]
    // 0xb19da8: r0 = _interpolate()
    //     0xb19da8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb19dac: stur            x0, [fp, #-0x38]
    // 0xb19db0: r16 = Instance_Color
    //     0xb19db0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb19db4: r30 = 12.000000
    //     0xb19db4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19db8: ldr             lr, [lr, #0x9e8]
    // 0xb19dbc: stp             lr, x16, [SP]
    // 0xb19dc0: ldur            x1, [fp, #-0x30]
    // 0xb19dc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19dc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19dc8: ldr             x4, [x4, #0x9b8]
    // 0xb19dcc: r0 = copyWith()
    //     0xb19dcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb19dd0: stur            x0, [fp, #-0x70]
    // 0xb19dd4: r0 = TextSpan()
    //     0xb19dd4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb19dd8: mov             x3, x0
    // 0xb19ddc: ldur            x0, [fp, #-0x38]
    // 0xb19de0: stur            x3, [fp, #-0x78]
    // 0xb19de4: StoreField: r3->field_b = r0
    //     0xb19de4: stur            w0, [x3, #0xb]
    // 0xb19de8: r0 = Instance__DeferringMouseCursor
    //     0xb19de8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb19dec: ArrayStore: r3[0] = r0  ; List_4
    //     0xb19dec: stur            w0, [x3, #0x17]
    // 0xb19df0: ldur            x1, [fp, #-0x70]
    // 0xb19df4: StoreField: r3->field_7 = r1
    //     0xb19df4: stur            w1, [x3, #7]
    // 0xb19df8: r1 = Null
    //     0xb19df8: mov             x1, NULL
    // 0xb19dfc: r2 = 4
    //     0xb19dfc: movz            x2, #0x4
    // 0xb19e00: r0 = AllocateArray()
    //     0xb19e00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19e04: mov             x2, x0
    // 0xb19e08: ldur            x0, [fp, #-0x68]
    // 0xb19e0c: stur            x2, [fp, #-0x38]
    // 0xb19e10: StoreField: r2->field_f = r0
    //     0xb19e10: stur            w0, [x2, #0xf]
    // 0xb19e14: ldur            x0, [fp, #-0x78]
    // 0xb19e18: StoreField: r2->field_13 = r0
    //     0xb19e18: stur            w0, [x2, #0x13]
    // 0xb19e1c: r1 = <InlineSpan>
    //     0xb19e1c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb19e20: ldr             x1, [x1, #0xe40]
    // 0xb19e24: r0 = AllocateGrowableArray()
    //     0xb19e24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb19e28: mov             x1, x0
    // 0xb19e2c: ldur            x0, [fp, #-0x38]
    // 0xb19e30: stur            x1, [fp, #-0x68]
    // 0xb19e34: StoreField: r1->field_f = r0
    //     0xb19e34: stur            w0, [x1, #0xf]
    // 0xb19e38: r2 = 4
    //     0xb19e38: movz            x2, #0x4
    // 0xb19e3c: StoreField: r1->field_b = r2
    //     0xb19e3c: stur            w2, [x1, #0xb]
    // 0xb19e40: r0 = TextSpan()
    //     0xb19e40: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb19e44: mov             x1, x0
    // 0xb19e48: ldur            x0, [fp, #-0x68]
    // 0xb19e4c: stur            x1, [fp, #-0x38]
    // 0xb19e50: StoreField: r1->field_f = r0
    //     0xb19e50: stur            w0, [x1, #0xf]
    // 0xb19e54: r0 = Instance__DeferringMouseCursor
    //     0xb19e54: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb19e58: ArrayStore: r1[0] = r0  ; List_4
    //     0xb19e58: stur            w0, [x1, #0x17]
    // 0xb19e5c: r0 = RichText()
    //     0xb19e5c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb19e60: mov             x1, x0
    // 0xb19e64: ldur            x2, [fp, #-0x38]
    // 0xb19e68: stur            x0, [fp, #-0x38]
    // 0xb19e6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb19e6c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb19e70: r0 = RichText()
    //     0xb19e70: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb19e74: r0 = Padding()
    //     0xb19e74: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb19e78: mov             x2, x0
    // 0xb19e7c: r0 = Instance_EdgeInsets
    //     0xb19e7c: add             x0, PP, #0x56, lsl #12  ; [pp+0x56028] Obj!EdgeInsets@d59181
    //     0xb19e80: ldr             x0, [x0, #0x28]
    // 0xb19e84: stur            x2, [fp, #-0x68]
    // 0xb19e88: StoreField: r2->field_f = r0
    //     0xb19e88: stur            w0, [x2, #0xf]
    // 0xb19e8c: ldur            x0, [fp, #-0x38]
    // 0xb19e90: StoreField: r2->field_b = r0
    //     0xb19e90: stur            w0, [x2, #0xb]
    // 0xb19e94: r16 = Instance_Color
    //     0xb19e94: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb19e98: r30 = 12.000000
    //     0xb19e98: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19e9c: ldr             lr, [lr, #0x9e8]
    // 0xb19ea0: stp             lr, x16, [SP]
    // 0xb19ea4: ldur            x1, [fp, #-0x30]
    // 0xb19ea8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19ea8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19eac: ldr             x4, [x4, #0x9b8]
    // 0xb19eb0: r0 = copyWith()
    //     0xb19eb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb19eb4: stur            x0, [fp, #-0x38]
    // 0xb19eb8: r0 = TextSpan()
    //     0xb19eb8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb19ebc: mov             x2, x0
    // 0xb19ec0: r0 = "• Exchange any new item by "
    //     0xb19ec0: add             x0, PP, #0x53, lsl #12  ; [pp+0x539e0] "• Exchange any new item by "
    //     0xb19ec4: ldr             x0, [x0, #0x9e0]
    // 0xb19ec8: stur            x2, [fp, #-0x70]
    // 0xb19ecc: StoreField: r2->field_b = r0
    //     0xb19ecc: stur            w0, [x2, #0xb]
    // 0xb19ed0: r0 = Instance__DeferringMouseCursor
    //     0xb19ed0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb19ed4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb19ed4: stur            w0, [x2, #0x17]
    // 0xb19ed8: ldur            x1, [fp, #-0x38]
    // 0xb19edc: StoreField: r2->field_7 = r1
    //     0xb19edc: stur            w1, [x2, #7]
    // 0xb19ee0: ldur            x3, [fp, #-8]
    // 0xb19ee4: LoadField: r1 = r3->field_b
    //     0xb19ee4: ldur            w1, [x3, #0xb]
    // 0xb19ee8: DecompressPointer r1
    //     0xb19ee8: add             x1, x1, HEAP, lsl #32
    // 0xb19eec: cmp             w1, NULL
    // 0xb19ef0: b.eq            #0xb1ad20
    // 0xb19ef4: LoadField: r4 = r1->field_b
    //     0xb19ef4: ldur            w4, [x1, #0xb]
    // 0xb19ef8: DecompressPointer r4
    //     0xb19ef8: add             x4, x4, HEAP, lsl #32
    // 0xb19efc: cmp             w4, NULL
    // 0xb19f00: b.ne            #0xb19f0c
    // 0xb19f04: r1 = Null
    //     0xb19f04: mov             x1, NULL
    // 0xb19f08: b               #0xb19f30
    // 0xb19f0c: LoadField: r1 = r4->field_b
    //     0xb19f0c: ldur            w1, [x4, #0xb]
    // 0xb19f10: DecompressPointer r1
    //     0xb19f10: add             x1, x1, HEAP, lsl #32
    // 0xb19f14: cmp             w1, NULL
    // 0xb19f18: b.ne            #0xb19f24
    // 0xb19f1c: r1 = Null
    //     0xb19f1c: mov             x1, NULL
    // 0xb19f20: b               #0xb19f30
    // 0xb19f24: LoadField: r4 = r1->field_13
    //     0xb19f24: ldur            w4, [x1, #0x13]
    // 0xb19f28: DecompressPointer r4
    //     0xb19f28: add             x4, x4, HEAP, lsl #32
    // 0xb19f2c: mov             x1, x4
    // 0xb19f30: cmp             w1, NULL
    // 0xb19f34: b.ne            #0xb19f40
    // 0xb19f38: r7 = ""
    //     0xb19f38: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb19f3c: b               #0xb19f44
    // 0xb19f40: mov             x7, x1
    // 0xb19f44: ldur            x6, [fp, #-0x20]
    // 0xb19f48: ldur            x5, [fp, #-0x60]
    // 0xb19f4c: ldur            x4, [fp, #-0x68]
    // 0xb19f50: stur            x7, [fp, #-0x38]
    // 0xb19f54: r16 = Instance_Color
    //     0xb19f54: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb19f58: r30 = 12.000000
    //     0xb19f58: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb19f5c: ldr             lr, [lr, #0x9e8]
    // 0xb19f60: stp             lr, x16, [SP]
    // 0xb19f64: ldur            x1, [fp, #-0x30]
    // 0xb19f68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb19f68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb19f6c: ldr             x4, [x4, #0x9b8]
    // 0xb19f70: r0 = copyWith()
    //     0xb19f70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb19f74: stur            x0, [fp, #-0x78]
    // 0xb19f78: r0 = TextSpan()
    //     0xb19f78: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb19f7c: mov             x3, x0
    // 0xb19f80: ldur            x0, [fp, #-0x38]
    // 0xb19f84: stur            x3, [fp, #-0x80]
    // 0xb19f88: StoreField: r3->field_b = r0
    //     0xb19f88: stur            w0, [x3, #0xb]
    // 0xb19f8c: r0 = Instance__DeferringMouseCursor
    //     0xb19f8c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb19f90: ArrayStore: r3[0] = r0  ; List_4
    //     0xb19f90: stur            w0, [x3, #0x17]
    // 0xb19f94: ldur            x1, [fp, #-0x78]
    // 0xb19f98: StoreField: r3->field_7 = r1
    //     0xb19f98: stur            w1, [x3, #7]
    // 0xb19f9c: r1 = Null
    //     0xb19f9c: mov             x1, NULL
    // 0xb19fa0: r2 = 4
    //     0xb19fa0: movz            x2, #0x4
    // 0xb19fa4: r0 = AllocateArray()
    //     0xb19fa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb19fa8: mov             x2, x0
    // 0xb19fac: ldur            x0, [fp, #-0x70]
    // 0xb19fb0: stur            x2, [fp, #-0x38]
    // 0xb19fb4: StoreField: r2->field_f = r0
    //     0xb19fb4: stur            w0, [x2, #0xf]
    // 0xb19fb8: ldur            x0, [fp, #-0x80]
    // 0xb19fbc: StoreField: r2->field_13 = r0
    //     0xb19fbc: stur            w0, [x2, #0x13]
    // 0xb19fc0: r1 = <InlineSpan>
    //     0xb19fc0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb19fc4: ldr             x1, [x1, #0xe40]
    // 0xb19fc8: r0 = AllocateGrowableArray()
    //     0xb19fc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb19fcc: mov             x1, x0
    // 0xb19fd0: ldur            x0, [fp, #-0x38]
    // 0xb19fd4: stur            x1, [fp, #-0x70]
    // 0xb19fd8: StoreField: r1->field_f = r0
    //     0xb19fd8: stur            w0, [x1, #0xf]
    // 0xb19fdc: r2 = 4
    //     0xb19fdc: movz            x2, #0x4
    // 0xb19fe0: StoreField: r1->field_b = r2
    //     0xb19fe0: stur            w2, [x1, #0xb]
    // 0xb19fe4: r0 = TextSpan()
    //     0xb19fe4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb19fe8: mov             x1, x0
    // 0xb19fec: ldur            x0, [fp, #-0x70]
    // 0xb19ff0: stur            x1, [fp, #-0x38]
    // 0xb19ff4: StoreField: r1->field_f = r0
    //     0xb19ff4: stur            w0, [x1, #0xf]
    // 0xb19ff8: r0 = Instance__DeferringMouseCursor
    //     0xb19ff8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb19ffc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb19ffc: stur            w0, [x1, #0x17]
    // 0xb1a000: r0 = RichText()
    //     0xb1a000: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb1a004: mov             x1, x0
    // 0xb1a008: ldur            x2, [fp, #-0x38]
    // 0xb1a00c: stur            x0, [fp, #-0x38]
    // 0xb1a010: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1a010: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1a014: r0 = RichText()
    //     0xb1a014: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb1a018: r0 = Padding()
    //     0xb1a018: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1a01c: mov             x3, x0
    // 0xb1a020: r0 = Instance_EdgeInsets
    //     0xb1a020: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xb1a024: ldr             x0, [x0, #0xf30]
    // 0xb1a028: stur            x3, [fp, #-0x70]
    // 0xb1a02c: StoreField: r3->field_f = r0
    //     0xb1a02c: stur            w0, [x3, #0xf]
    // 0xb1a030: ldur            x0, [fp, #-0x38]
    // 0xb1a034: StoreField: r3->field_b = r0
    //     0xb1a034: stur            w0, [x3, #0xb]
    // 0xb1a038: r1 = Null
    //     0xb1a038: mov             x1, NULL
    // 0xb1a03c: r2 = 8
    //     0xb1a03c: movz            x2, #0x8
    // 0xb1a040: r0 = AllocateArray()
    //     0xb1a040: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1a044: mov             x2, x0
    // 0xb1a048: ldur            x0, [fp, #-0x20]
    // 0xb1a04c: stur            x2, [fp, #-0x38]
    // 0xb1a050: StoreField: r2->field_f = r0
    //     0xb1a050: stur            w0, [x2, #0xf]
    // 0xb1a054: ldur            x0, [fp, #-0x60]
    // 0xb1a058: StoreField: r2->field_13 = r0
    //     0xb1a058: stur            w0, [x2, #0x13]
    // 0xb1a05c: ldur            x0, [fp, #-0x68]
    // 0xb1a060: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1a060: stur            w0, [x2, #0x17]
    // 0xb1a064: ldur            x0, [fp, #-0x70]
    // 0xb1a068: StoreField: r2->field_1b = r0
    //     0xb1a068: stur            w0, [x2, #0x1b]
    // 0xb1a06c: r1 = <Widget>
    //     0xb1a06c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1a070: r0 = AllocateGrowableArray()
    //     0xb1a070: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1a074: mov             x1, x0
    // 0xb1a078: ldur            x0, [fp, #-0x38]
    // 0xb1a07c: stur            x1, [fp, #-0x20]
    // 0xb1a080: StoreField: r1->field_f = r0
    //     0xb1a080: stur            w0, [x1, #0xf]
    // 0xb1a084: r0 = 8
    //     0xb1a084: movz            x0, #0x8
    // 0xb1a088: StoreField: r1->field_b = r0
    //     0xb1a088: stur            w0, [x1, #0xb]
    // 0xb1a08c: r0 = Column()
    //     0xb1a08c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1a090: mov             x1, x0
    // 0xb1a094: r0 = Instance_Axis
    //     0xb1a094: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1a098: stur            x1, [fp, #-0x38]
    // 0xb1a09c: StoreField: r1->field_f = r0
    //     0xb1a09c: stur            w0, [x1, #0xf]
    // 0xb1a0a0: r2 = Instance_MainAxisAlignment
    //     0xb1a0a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1a0a4: ldr             x2, [x2, #0xa08]
    // 0xb1a0a8: StoreField: r1->field_13 = r2
    //     0xb1a0a8: stur            w2, [x1, #0x13]
    // 0xb1a0ac: r3 = Instance_MainAxisSize
    //     0xb1a0ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1a0b0: ldr             x3, [x3, #0xa10]
    // 0xb1a0b4: ArrayStore: r1[0] = r3  ; List_4
    //     0xb1a0b4: stur            w3, [x1, #0x17]
    // 0xb1a0b8: r4 = Instance_CrossAxisAlignment
    //     0xb1a0b8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1a0bc: ldr             x4, [x4, #0x890]
    // 0xb1a0c0: StoreField: r1->field_1b = r4
    //     0xb1a0c0: stur            w4, [x1, #0x1b]
    // 0xb1a0c4: r5 = Instance_VerticalDirection
    //     0xb1a0c4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1a0c8: ldr             x5, [x5, #0xa20]
    // 0xb1a0cc: StoreField: r1->field_23 = r5
    //     0xb1a0cc: stur            w5, [x1, #0x23]
    // 0xb1a0d0: r6 = Instance_Clip
    //     0xb1a0d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1a0d4: ldr             x6, [x6, #0x38]
    // 0xb1a0d8: StoreField: r1->field_2b = r6
    //     0xb1a0d8: stur            w6, [x1, #0x2b]
    // 0xb1a0dc: StoreField: r1->field_2f = rZR
    //     0xb1a0dc: stur            xzr, [x1, #0x2f]
    // 0xb1a0e0: ldur            x7, [fp, #-0x20]
    // 0xb1a0e4: StoreField: r1->field_b = r7
    //     0xb1a0e4: stur            w7, [x1, #0xb]
    // 0xb1a0e8: r0 = Container()
    //     0xb1a0e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1a0ec: stur            x0, [fp, #-0x20]
    // 0xb1a0f0: ldur            x16, [fp, #-0x58]
    // 0xb1a0f4: ldur            lr, [fp, #-0x38]
    // 0xb1a0f8: stp             lr, x16, [SP]
    // 0xb1a0fc: mov             x1, x0
    // 0xb1a100: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb1a100: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb1a104: ldr             x4, [x4, #0x88]
    // 0xb1a108: r0 = Container()
    //     0xb1a108: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1a10c: ldur            x0, [fp, #-8]
    // 0xb1a110: LoadField: r1 = r0->field_b
    //     0xb1a110: ldur            w1, [x0, #0xb]
    // 0xb1a114: DecompressPointer r1
    //     0xb1a114: add             x1, x1, HEAP, lsl #32
    // 0xb1a118: cmp             w1, NULL
    // 0xb1a11c: b.eq            #0xb1ad24
    // 0xb1a120: LoadField: r2 = r1->field_b
    //     0xb1a120: ldur            w2, [x1, #0xb]
    // 0xb1a124: DecompressPointer r2
    //     0xb1a124: add             x2, x2, HEAP, lsl #32
    // 0xb1a128: cmp             w2, NULL
    // 0xb1a12c: b.ne            #0xb1a138
    // 0xb1a130: r1 = Null
    //     0xb1a130: mov             x1, NULL
    // 0xb1a134: b               #0xb1a18c
    // 0xb1a138: LoadField: r1 = r2->field_b
    //     0xb1a138: ldur            w1, [x2, #0xb]
    // 0xb1a13c: DecompressPointer r1
    //     0xb1a13c: add             x1, x1, HEAP, lsl #32
    // 0xb1a140: cmp             w1, NULL
    // 0xb1a144: b.ne            #0xb1a150
    // 0xb1a148: r1 = Null
    //     0xb1a148: mov             x1, NULL
    // 0xb1a14c: b               #0xb1a18c
    // 0xb1a150: LoadField: r2 = r1->field_7
    //     0xb1a150: ldur            w2, [x1, #7]
    // 0xb1a154: DecompressPointer r2
    //     0xb1a154: add             x2, x2, HEAP, lsl #32
    // 0xb1a158: cmp             w2, NULL
    // 0xb1a15c: b.ne            #0xb1a168
    // 0xb1a160: r1 = Null
    //     0xb1a160: mov             x1, NULL
    // 0xb1a164: b               #0xb1a18c
    // 0xb1a168: LoadField: r1 = r2->field_1f
    //     0xb1a168: ldur            w1, [x2, #0x1f]
    // 0xb1a16c: DecompressPointer r1
    //     0xb1a16c: add             x1, x1, HEAP, lsl #32
    // 0xb1a170: cmp             w1, NULL
    // 0xb1a174: b.ne            #0xb1a180
    // 0xb1a178: r1 = Null
    //     0xb1a178: mov             x1, NULL
    // 0xb1a17c: b               #0xb1a18c
    // 0xb1a180: LoadField: r2 = r1->field_7
    //     0xb1a180: ldur            w2, [x1, #7]
    // 0xb1a184: DecompressPointer r2
    //     0xb1a184: add             x2, x2, HEAP, lsl #32
    // 0xb1a188: mov             x1, x2
    // 0xb1a18c: cmp             w1, NULL
    // 0xb1a190: b.ne            #0xb1a19c
    // 0xb1a194: r2 = ""
    //     0xb1a194: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1a198: b               #0xb1a1a0
    // 0xb1a19c: mov             x2, x1
    // 0xb1a1a0: stur            x2, [fp, #-0x38]
    // 0xb1a1a4: r16 = Instance_Color
    //     0xb1a1a4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1a1a8: r30 = 14.000000
    //     0xb1a1a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1a1ac: ldr             lr, [lr, #0x1d8]
    // 0xb1a1b0: stp             lr, x16, [SP]
    // 0xb1a1b4: ldur            x1, [fp, #-0x18]
    // 0xb1a1b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb1a1b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb1a1bc: ldr             x4, [x4, #0x9b8]
    // 0xb1a1c0: r0 = copyWith()
    //     0xb1a1c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1a1c4: stur            x0, [fp, #-0x58]
    // 0xb1a1c8: r0 = Text()
    //     0xb1a1c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1a1cc: mov             x2, x0
    // 0xb1a1d0: ldur            x0, [fp, #-0x38]
    // 0xb1a1d4: stur            x2, [fp, #-0x60]
    // 0xb1a1d8: StoreField: r2->field_b = r0
    //     0xb1a1d8: stur            w0, [x2, #0xb]
    // 0xb1a1dc: ldur            x0, [fp, #-0x58]
    // 0xb1a1e0: StoreField: r2->field_13 = r0
    //     0xb1a1e0: stur            w0, [x2, #0x13]
    // 0xb1a1e4: ldur            x0, [fp, #-8]
    // 0xb1a1e8: LoadField: r1 = r0->field_b
    //     0xb1a1e8: ldur            w1, [x0, #0xb]
    // 0xb1a1ec: DecompressPointer r1
    //     0xb1a1ec: add             x1, x1, HEAP, lsl #32
    // 0xb1a1f0: cmp             w1, NULL
    // 0xb1a1f4: b.eq            #0xb1ad28
    // 0xb1a1f8: LoadField: r3 = r1->field_b
    //     0xb1a1f8: ldur            w3, [x1, #0xb]
    // 0xb1a1fc: DecompressPointer r3
    //     0xb1a1fc: add             x3, x3, HEAP, lsl #32
    // 0xb1a200: cmp             w3, NULL
    // 0xb1a204: b.ne            #0xb1a210
    // 0xb1a208: r1 = Null
    //     0xb1a208: mov             x1, NULL
    // 0xb1a20c: b               #0xb1a264
    // 0xb1a210: LoadField: r1 = r3->field_b
    //     0xb1a210: ldur            w1, [x3, #0xb]
    // 0xb1a214: DecompressPointer r1
    //     0xb1a214: add             x1, x1, HEAP, lsl #32
    // 0xb1a218: cmp             w1, NULL
    // 0xb1a21c: b.ne            #0xb1a228
    // 0xb1a220: r1 = Null
    //     0xb1a220: mov             x1, NULL
    // 0xb1a224: b               #0xb1a264
    // 0xb1a228: LoadField: r3 = r1->field_7
    //     0xb1a228: ldur            w3, [x1, #7]
    // 0xb1a22c: DecompressPointer r3
    //     0xb1a22c: add             x3, x3, HEAP, lsl #32
    // 0xb1a230: cmp             w3, NULL
    // 0xb1a234: b.ne            #0xb1a240
    // 0xb1a238: r1 = Null
    //     0xb1a238: mov             x1, NULL
    // 0xb1a23c: b               #0xb1a264
    // 0xb1a240: LoadField: r1 = r3->field_1f
    //     0xb1a240: ldur            w1, [x3, #0x1f]
    // 0xb1a244: DecompressPointer r1
    //     0xb1a244: add             x1, x1, HEAP, lsl #32
    // 0xb1a248: cmp             w1, NULL
    // 0xb1a24c: b.ne            #0xb1a258
    // 0xb1a250: r1 = Null
    //     0xb1a250: mov             x1, NULL
    // 0xb1a254: b               #0xb1a264
    // 0xb1a258: LoadField: r3 = r1->field_b
    //     0xb1a258: ldur            w3, [x1, #0xb]
    // 0xb1a25c: DecompressPointer r3
    //     0xb1a25c: add             x3, x3, HEAP, lsl #32
    // 0xb1a260: mov             x1, x3
    // 0xb1a264: cmp             w1, NULL
    // 0xb1a268: b.ne            #0xb1a274
    // 0xb1a26c: r5 = ""
    //     0xb1a26c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1a270: b               #0xb1a278
    // 0xb1a274: mov             x5, x1
    // 0xb1a278: ldur            x4, [fp, #-0x28]
    // 0xb1a27c: ldur            x3, [fp, #-0x20]
    // 0xb1a280: stur            x5, [fp, #-0x38]
    // 0xb1a284: r1 = Instance_Color
    //     0xb1a284: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1a288: d0 = 0.700000
    //     0xb1a288: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1a28c: ldr             d0, [x17, #0xf48]
    // 0xb1a290: r0 = withOpacity()
    //     0xb1a290: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1a294: r16 = 12.000000
    //     0xb1a294: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1a298: ldr             x16, [x16, #0x9e8]
    // 0xb1a29c: stp             x16, x0, [SP]
    // 0xb1a2a0: ldur            x1, [fp, #-0x30]
    // 0xb1a2a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb1a2a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb1a2a8: ldr             x4, [x4, #0x9b8]
    // 0xb1a2ac: r0 = copyWith()
    //     0xb1a2ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1a2b0: stur            x0, [fp, #-0x30]
    // 0xb1a2b4: r0 = Text()
    //     0xb1a2b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1a2b8: mov             x3, x0
    // 0xb1a2bc: ldur            x0, [fp, #-0x38]
    // 0xb1a2c0: stur            x3, [fp, #-0x58]
    // 0xb1a2c4: StoreField: r3->field_b = r0
    //     0xb1a2c4: stur            w0, [x3, #0xb]
    // 0xb1a2c8: ldur            x0, [fp, #-0x30]
    // 0xb1a2cc: StoreField: r3->field_13 = r0
    //     0xb1a2cc: stur            w0, [x3, #0x13]
    // 0xb1a2d0: r1 = Null
    //     0xb1a2d0: mov             x1, NULL
    // 0xb1a2d4: r2 = 6
    //     0xb1a2d4: movz            x2, #0x6
    // 0xb1a2d8: r0 = AllocateArray()
    //     0xb1a2d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1a2dc: mov             x2, x0
    // 0xb1a2e0: ldur            x0, [fp, #-0x60]
    // 0xb1a2e4: stur            x2, [fp, #-0x30]
    // 0xb1a2e8: StoreField: r2->field_f = r0
    //     0xb1a2e8: stur            w0, [x2, #0xf]
    // 0xb1a2ec: r16 = Instance_SizedBox
    //     0xb1a2ec: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb1a2f0: ldr             x16, [x16, #0xc70]
    // 0xb1a2f4: StoreField: r2->field_13 = r16
    //     0xb1a2f4: stur            w16, [x2, #0x13]
    // 0xb1a2f8: ldur            x0, [fp, #-0x58]
    // 0xb1a2fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1a2fc: stur            w0, [x2, #0x17]
    // 0xb1a300: r1 = <Widget>
    //     0xb1a300: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1a304: r0 = AllocateGrowableArray()
    //     0xb1a304: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1a308: mov             x1, x0
    // 0xb1a30c: ldur            x0, [fp, #-0x30]
    // 0xb1a310: stur            x1, [fp, #-0x38]
    // 0xb1a314: StoreField: r1->field_f = r0
    //     0xb1a314: stur            w0, [x1, #0xf]
    // 0xb1a318: r2 = 6
    //     0xb1a318: movz            x2, #0x6
    // 0xb1a31c: StoreField: r1->field_b = r2
    //     0xb1a31c: stur            w2, [x1, #0xb]
    // 0xb1a320: r0 = Column()
    //     0xb1a320: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1a324: mov             x3, x0
    // 0xb1a328: r0 = Instance_Axis
    //     0xb1a328: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1a32c: stur            x3, [fp, #-0x30]
    // 0xb1a330: StoreField: r3->field_f = r0
    //     0xb1a330: stur            w0, [x3, #0xf]
    // 0xb1a334: r4 = Instance_MainAxisAlignment
    //     0xb1a334: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1a338: ldr             x4, [x4, #0xa08]
    // 0xb1a33c: StoreField: r3->field_13 = r4
    //     0xb1a33c: stur            w4, [x3, #0x13]
    // 0xb1a340: r5 = Instance_MainAxisSize
    //     0xb1a340: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1a344: ldr             x5, [x5, #0xa10]
    // 0xb1a348: ArrayStore: r3[0] = r5  ; List_4
    //     0xb1a348: stur            w5, [x3, #0x17]
    // 0xb1a34c: r6 = Instance_CrossAxisAlignment
    //     0xb1a34c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1a350: ldr             x6, [x6, #0x890]
    // 0xb1a354: StoreField: r3->field_1b = r6
    //     0xb1a354: stur            w6, [x3, #0x1b]
    // 0xb1a358: r7 = Instance_VerticalDirection
    //     0xb1a358: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1a35c: ldr             x7, [x7, #0xa20]
    // 0xb1a360: StoreField: r3->field_23 = r7
    //     0xb1a360: stur            w7, [x3, #0x23]
    // 0xb1a364: r8 = Instance_Clip
    //     0xb1a364: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1a368: ldr             x8, [x8, #0x38]
    // 0xb1a36c: StoreField: r3->field_2b = r8
    //     0xb1a36c: stur            w8, [x3, #0x2b]
    // 0xb1a370: StoreField: r3->field_2f = rZR
    //     0xb1a370: stur            xzr, [x3, #0x2f]
    // 0xb1a374: ldur            x1, [fp, #-0x38]
    // 0xb1a378: StoreField: r3->field_b = r1
    //     0xb1a378: stur            w1, [x3, #0xb]
    // 0xb1a37c: r1 = Null
    //     0xb1a37c: mov             x1, NULL
    // 0xb1a380: r2 = 10
    //     0xb1a380: movz            x2, #0xa
    // 0xb1a384: r0 = AllocateArray()
    //     0xb1a384: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1a388: mov             x2, x0
    // 0xb1a38c: ldur            x0, [fp, #-0x28]
    // 0xb1a390: stur            x2, [fp, #-0x38]
    // 0xb1a394: StoreField: r2->field_f = r0
    //     0xb1a394: stur            w0, [x2, #0xf]
    // 0xb1a398: ldur            x0, [fp, #-0x20]
    // 0xb1a39c: StoreField: r2->field_13 = r0
    //     0xb1a39c: stur            w0, [x2, #0x13]
    // 0xb1a3a0: r16 = Instance_SizedBox
    //     0xb1a3a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb1a3a4: ldr             x16, [x16, #0x9f0]
    // 0xb1a3a8: ArrayStore: r2[0] = r16  ; List_4
    //     0xb1a3a8: stur            w16, [x2, #0x17]
    // 0xb1a3ac: ldur            x0, [fp, #-0x30]
    // 0xb1a3b0: StoreField: r2->field_1b = r0
    //     0xb1a3b0: stur            w0, [x2, #0x1b]
    // 0xb1a3b4: r16 = Instance_SizedBox
    //     0xb1a3b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb1a3b8: ldr             x16, [x16, #0x9f0]
    // 0xb1a3bc: StoreField: r2->field_1f = r16
    //     0xb1a3bc: stur            w16, [x2, #0x1f]
    // 0xb1a3c0: r1 = <Widget>
    //     0xb1a3c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1a3c4: r0 = AllocateGrowableArray()
    //     0xb1a3c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1a3c8: mov             x1, x0
    // 0xb1a3cc: ldur            x0, [fp, #-0x38]
    // 0xb1a3d0: stur            x1, [fp, #-0x20]
    // 0xb1a3d4: StoreField: r1->field_f = r0
    //     0xb1a3d4: stur            w0, [x1, #0xf]
    // 0xb1a3d8: r0 = 10
    //     0xb1a3d8: movz            x0, #0xa
    // 0xb1a3dc: StoreField: r1->field_b = r0
    //     0xb1a3dc: stur            w0, [x1, #0xb]
    // 0xb1a3e0: r0 = Column()
    //     0xb1a3e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1a3e4: mov             x1, x0
    // 0xb1a3e8: r0 = Instance_Axis
    //     0xb1a3e8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1a3ec: stur            x1, [fp, #-0x28]
    // 0xb1a3f0: StoreField: r1->field_f = r0
    //     0xb1a3f0: stur            w0, [x1, #0xf]
    // 0xb1a3f4: r2 = Instance_MainAxisAlignment
    //     0xb1a3f4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1a3f8: ldr             x2, [x2, #0xa08]
    // 0xb1a3fc: StoreField: r1->field_13 = r2
    //     0xb1a3fc: stur            w2, [x1, #0x13]
    // 0xb1a400: r3 = Instance_MainAxisSize
    //     0xb1a400: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1a404: ldr             x3, [x3, #0xa10]
    // 0xb1a408: ArrayStore: r1[0] = r3  ; List_4
    //     0xb1a408: stur            w3, [x1, #0x17]
    // 0xb1a40c: r4 = Instance_CrossAxisAlignment
    //     0xb1a40c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1a410: ldr             x4, [x4, #0x890]
    // 0xb1a414: StoreField: r1->field_1b = r4
    //     0xb1a414: stur            w4, [x1, #0x1b]
    // 0xb1a418: r5 = Instance_VerticalDirection
    //     0xb1a418: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1a41c: ldr             x5, [x5, #0xa20]
    // 0xb1a420: StoreField: r1->field_23 = r5
    //     0xb1a420: stur            w5, [x1, #0x23]
    // 0xb1a424: r6 = Instance_Clip
    //     0xb1a424: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1a428: ldr             x6, [x6, #0x38]
    // 0xb1a42c: StoreField: r1->field_2b = r6
    //     0xb1a42c: stur            w6, [x1, #0x2b]
    // 0xb1a430: StoreField: r1->field_2f = rZR
    //     0xb1a430: stur            xzr, [x1, #0x2f]
    // 0xb1a434: ldur            x7, [fp, #-0x20]
    // 0xb1a438: StoreField: r1->field_b = r7
    //     0xb1a438: stur            w7, [x1, #0xb]
    // 0xb1a43c: r0 = Padding()
    //     0xb1a43c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1a440: mov             x1, x0
    // 0xb1a444: r0 = Instance_EdgeInsets
    //     0xb1a444: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb1a448: ldr             x0, [x0, #0x668]
    // 0xb1a44c: stur            x1, [fp, #-0x20]
    // 0xb1a450: StoreField: r1->field_f = r0
    //     0xb1a450: stur            w0, [x1, #0xf]
    // 0xb1a454: ldur            x0, [fp, #-0x28]
    // 0xb1a458: StoreField: r1->field_b = r0
    //     0xb1a458: stur            w0, [x1, #0xb]
    // 0xb1a45c: r16 = <EdgeInsets>
    //     0xb1a45c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb1a460: ldr             x16, [x16, #0xda0]
    // 0xb1a464: r30 = Instance_EdgeInsets
    //     0xb1a464: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb1a468: ldr             lr, [lr, #0x1f0]
    // 0xb1a46c: stp             lr, x16, [SP]
    // 0xb1a470: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1a470: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1a474: r0 = all()
    //     0xb1a474: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb1a478: stur            x0, [fp, #-0x28]
    // 0xb1a47c: r16 = <Color>
    //     0xb1a47c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb1a480: ldr             x16, [x16, #0xf80]
    // 0xb1a484: r30 = Instance_Color
    //     0xb1a484: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1a488: stp             lr, x16, [SP]
    // 0xb1a48c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1a48c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1a490: r0 = all()
    //     0xb1a490: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb1a494: r1 = Instance_Color
    //     0xb1a494: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1a498: d0 = 0.080000
    //     0xb1a498: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xb1a49c: ldr             d0, [x17, #0x798]
    // 0xb1a4a0: stur            x0, [fp, #-0x30]
    // 0xb1a4a4: r0 = withOpacity()
    //     0xb1a4a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1a4a8: stur            x0, [fp, #-0x38]
    // 0xb1a4ac: r0 = BorderSide()
    //     0xb1a4ac: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb1a4b0: mov             x1, x0
    // 0xb1a4b4: ldur            x0, [fp, #-0x38]
    // 0xb1a4b8: stur            x1, [fp, #-0x58]
    // 0xb1a4bc: StoreField: r1->field_7 = r0
    //     0xb1a4bc: stur            w0, [x1, #7]
    // 0xb1a4c0: d0 = 1.000000
    //     0xb1a4c0: fmov            d0, #1.00000000
    // 0xb1a4c4: StoreField: r1->field_b = d0
    //     0xb1a4c4: stur            d0, [x1, #0xb]
    // 0xb1a4c8: r0 = Instance_BorderStyle
    //     0xb1a4c8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb1a4cc: ldr             x0, [x0, #0xf68]
    // 0xb1a4d0: StoreField: r1->field_13 = r0
    //     0xb1a4d0: stur            w0, [x1, #0x13]
    // 0xb1a4d4: d1 = -1.000000
    //     0xb1a4d4: fmov            d1, #-1.00000000
    // 0xb1a4d8: ArrayStore: r1[0] = d1  ; List_8
    //     0xb1a4d8: stur            d1, [x1, #0x17]
    // 0xb1a4dc: r0 = Radius()
    //     0xb1a4dc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1a4e0: d0 = 32.000000
    //     0xb1a4e0: add             x17, PP, #0x43, lsl #12  ; [pp+0x436c8] IMM: double(32) from 0x4040000000000000
    //     0xb1a4e4: ldr             d0, [x17, #0x6c8]
    // 0xb1a4e8: stur            x0, [fp, #-0x38]
    // 0xb1a4ec: StoreField: r0->field_7 = d0
    //     0xb1a4ec: stur            d0, [x0, #7]
    // 0xb1a4f0: StoreField: r0->field_f = d0
    //     0xb1a4f0: stur            d0, [x0, #0xf]
    // 0xb1a4f4: r0 = BorderRadius()
    //     0xb1a4f4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1a4f8: mov             x1, x0
    // 0xb1a4fc: ldur            x0, [fp, #-0x38]
    // 0xb1a500: stur            x1, [fp, #-0x60]
    // 0xb1a504: StoreField: r1->field_7 = r0
    //     0xb1a504: stur            w0, [x1, #7]
    // 0xb1a508: StoreField: r1->field_b = r0
    //     0xb1a508: stur            w0, [x1, #0xb]
    // 0xb1a50c: StoreField: r1->field_f = r0
    //     0xb1a50c: stur            w0, [x1, #0xf]
    // 0xb1a510: StoreField: r1->field_13 = r0
    //     0xb1a510: stur            w0, [x1, #0x13]
    // 0xb1a514: r0 = RoundedRectangleBorder()
    //     0xb1a514: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb1a518: mov             x1, x0
    // 0xb1a51c: ldur            x0, [fp, #-0x60]
    // 0xb1a520: StoreField: r1->field_b = r0
    //     0xb1a520: stur            w0, [x1, #0xb]
    // 0xb1a524: ldur            x0, [fp, #-0x58]
    // 0xb1a528: StoreField: r1->field_7 = r0
    //     0xb1a528: stur            w0, [x1, #7]
    // 0xb1a52c: r16 = <RoundedRectangleBorder>
    //     0xb1a52c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb1a530: ldr             x16, [x16, #0xf78]
    // 0xb1a534: stp             x1, x16, [SP]
    // 0xb1a538: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1a538: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1a53c: r0 = all()
    //     0xb1a53c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb1a540: stur            x0, [fp, #-0x38]
    // 0xb1a544: r0 = ButtonStyle()
    //     0xb1a544: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb1a548: mov             x1, x0
    // 0xb1a54c: ldur            x0, [fp, #-0x30]
    // 0xb1a550: stur            x1, [fp, #-0x58]
    // 0xb1a554: StoreField: r1->field_b = r0
    //     0xb1a554: stur            w0, [x1, #0xb]
    // 0xb1a558: ldur            x0, [fp, #-0x28]
    // 0xb1a55c: StoreField: r1->field_23 = r0
    //     0xb1a55c: stur            w0, [x1, #0x23]
    // 0xb1a560: ldur            x0, [fp, #-0x38]
    // 0xb1a564: StoreField: r1->field_43 = r0
    //     0xb1a564: stur            w0, [x1, #0x43]
    // 0xb1a568: r0 = TextButtonThemeData()
    //     0xb1a568: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb1a56c: mov             x2, x0
    // 0xb1a570: ldur            x0, [fp, #-0x58]
    // 0xb1a574: stur            x2, [fp, #-0x28]
    // 0xb1a578: StoreField: r2->field_7 = r0
    //     0xb1a578: stur            w0, [x2, #7]
    // 0xb1a57c: r16 = 14.000000
    //     0xb1a57c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1a580: ldr             x16, [x16, #0x1d8]
    // 0xb1a584: r30 = Instance_Color
    //     0xb1a584: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1a588: stp             lr, x16, [SP]
    // 0xb1a58c: ldur            x1, [fp, #-0x18]
    // 0xb1a590: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1a590: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1a594: ldr             x4, [x4, #0xaa0]
    // 0xb1a598: r0 = copyWith()
    //     0xb1a598: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1a59c: stur            x0, [fp, #-0x30]
    // 0xb1a5a0: r0 = Text()
    //     0xb1a5a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1a5a4: mov             x3, x0
    // 0xb1a5a8: r0 = "No, Buy Separately"
    //     0xb1a5a8: add             x0, PP, #0x56, lsl #12  ; [pp+0x56048] "No, Buy Separately"
    //     0xb1a5ac: ldr             x0, [x0, #0x48]
    // 0xb1a5b0: stur            x3, [fp, #-0x38]
    // 0xb1a5b4: StoreField: r3->field_b = r0
    //     0xb1a5b4: stur            w0, [x3, #0xb]
    // 0xb1a5b8: ldur            x0, [fp, #-0x30]
    // 0xb1a5bc: StoreField: r3->field_13 = r0
    //     0xb1a5bc: stur            w0, [x3, #0x13]
    // 0xb1a5c0: ldur            x2, [fp, #-0x10]
    // 0xb1a5c4: r1 = Function '<anonymous closure>':.
    //     0xb1a5c4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58490] AnonymousClosure: (0xb1ade8), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/single_exchange_product_bottom_sheet.dart] _SingleExchangeProductBottomSheetState::build (0xb18c24)
    //     0xb1a5c8: ldr             x1, [x1, #0x490]
    // 0xb1a5cc: r0 = AllocateClosure()
    //     0xb1a5cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1a5d0: stur            x0, [fp, #-0x30]
    // 0xb1a5d4: r0 = TextButton()
    //     0xb1a5d4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb1a5d8: mov             x1, x0
    // 0xb1a5dc: ldur            x0, [fp, #-0x30]
    // 0xb1a5e0: stur            x1, [fp, #-0x58]
    // 0xb1a5e4: StoreField: r1->field_b = r0
    //     0xb1a5e4: stur            w0, [x1, #0xb]
    // 0xb1a5e8: r0 = false
    //     0xb1a5e8: add             x0, NULL, #0x30  ; false
    // 0xb1a5ec: StoreField: r1->field_27 = r0
    //     0xb1a5ec: stur            w0, [x1, #0x27]
    // 0xb1a5f0: r2 = true
    //     0xb1a5f0: add             x2, NULL, #0x20  ; true
    // 0xb1a5f4: StoreField: r1->field_2f = r2
    //     0xb1a5f4: stur            w2, [x1, #0x2f]
    // 0xb1a5f8: ldur            x3, [fp, #-0x38]
    // 0xb1a5fc: StoreField: r1->field_37 = r3
    //     0xb1a5fc: stur            w3, [x1, #0x37]
    // 0xb1a600: r0 = TextButtonTheme()
    //     0xb1a600: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb1a604: mov             x2, x0
    // 0xb1a608: ldur            x0, [fp, #-0x28]
    // 0xb1a60c: stur            x2, [fp, #-0x30]
    // 0xb1a610: StoreField: r2->field_f = r0
    //     0xb1a610: stur            w0, [x2, #0xf]
    // 0xb1a614: ldur            x0, [fp, #-0x58]
    // 0xb1a618: StoreField: r2->field_b = r0
    //     0xb1a618: stur            w0, [x2, #0xb]
    // 0xb1a61c: r1 = <FlexParentData>
    //     0xb1a61c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb1a620: ldr             x1, [x1, #0xe00]
    // 0xb1a624: r0 = Expanded()
    //     0xb1a624: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb1a628: mov             x1, x0
    // 0xb1a62c: r0 = 1
    //     0xb1a62c: movz            x0, #0x1
    // 0xb1a630: stur            x1, [fp, #-0x28]
    // 0xb1a634: StoreField: r1->field_13 = r0
    //     0xb1a634: stur            x0, [x1, #0x13]
    // 0xb1a638: r2 = Instance_FlexFit
    //     0xb1a638: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb1a63c: ldr             x2, [x2, #0xe08]
    // 0xb1a640: StoreField: r1->field_1b = r2
    //     0xb1a640: stur            w2, [x1, #0x1b]
    // 0xb1a644: ldur            x3, [fp, #-0x30]
    // 0xb1a648: StoreField: r1->field_b = r3
    //     0xb1a648: stur            w3, [x1, #0xb]
    // 0xb1a64c: r16 = <EdgeInsets>
    //     0xb1a64c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb1a650: ldr             x16, [x16, #0xda0]
    // 0xb1a654: r30 = Instance_EdgeInsets
    //     0xb1a654: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb1a658: ldr             lr, [lr, #0x1f0]
    // 0xb1a65c: stp             lr, x16, [SP]
    // 0xb1a660: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1a660: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1a664: r0 = all()
    //     0xb1a664: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb1a668: mov             x1, x0
    // 0xb1a66c: ldur            x0, [fp, #-8]
    // 0xb1a670: stur            x1, [fp, #-0x30]
    // 0xb1a674: LoadField: r2 = r0->field_b
    //     0xb1a674: ldur            w2, [x0, #0xb]
    // 0xb1a678: DecompressPointer r2
    //     0xb1a678: add             x2, x2, HEAP, lsl #32
    // 0xb1a67c: cmp             w2, NULL
    // 0xb1a680: b.eq            #0xb1ad2c
    // 0xb1a684: LoadField: r3 = r2->field_2f
    //     0xb1a684: ldur            w3, [x2, #0x2f]
    // 0xb1a688: DecompressPointer r3
    //     0xb1a688: add             x3, x3, HEAP, lsl #32
    // 0xb1a68c: LoadField: r2 = r3->field_3f
    //     0xb1a68c: ldur            w2, [x3, #0x3f]
    // 0xb1a690: DecompressPointer r2
    //     0xb1a690: add             x2, x2, HEAP, lsl #32
    // 0xb1a694: cmp             w2, NULL
    // 0xb1a698: b.ne            #0xb1a6a4
    // 0xb1a69c: r3 = Null
    //     0xb1a69c: mov             x3, NULL
    // 0xb1a6a0: b               #0xb1a6c8
    // 0xb1a6a4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb1a6a4: ldur            w3, [x2, #0x17]
    // 0xb1a6a8: DecompressPointer r3
    //     0xb1a6a8: add             x3, x3, HEAP, lsl #32
    // 0xb1a6ac: cmp             w3, NULL
    // 0xb1a6b0: b.ne            #0xb1a6bc
    // 0xb1a6b4: r3 = Null
    //     0xb1a6b4: mov             x3, NULL
    // 0xb1a6b8: b               #0xb1a6c8
    // 0xb1a6bc: LoadField: r4 = r3->field_7
    //     0xb1a6bc: ldur            w4, [x3, #7]
    // 0xb1a6c0: DecompressPointer r4
    //     0xb1a6c0: add             x4, x4, HEAP, lsl #32
    // 0xb1a6c4: mov             x3, x4
    // 0xb1a6c8: cmp             w3, NULL
    // 0xb1a6cc: b.ne            #0xb1a6d8
    // 0xb1a6d0: r3 = 0
    //     0xb1a6d0: movz            x3, #0
    // 0xb1a6d4: b               #0xb1a6e8
    // 0xb1a6d8: r4 = LoadInt32Instr(r3)
    //     0xb1a6d8: sbfx            x4, x3, #1, #0x1f
    //     0xb1a6dc: tbz             w3, #0, #0xb1a6e4
    //     0xb1a6e0: ldur            x4, [x3, #7]
    // 0xb1a6e4: mov             x3, x4
    // 0xb1a6e8: stur            x3, [fp, #-0x50]
    // 0xb1a6ec: cmp             w2, NULL
    // 0xb1a6f0: b.ne            #0xb1a6fc
    // 0xb1a6f4: r4 = Null
    //     0xb1a6f4: mov             x4, NULL
    // 0xb1a6f8: b               #0xb1a720
    // 0xb1a6fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb1a6fc: ldur            w4, [x2, #0x17]
    // 0xb1a700: DecompressPointer r4
    //     0xb1a700: add             x4, x4, HEAP, lsl #32
    // 0xb1a704: cmp             w4, NULL
    // 0xb1a708: b.ne            #0xb1a714
    // 0xb1a70c: r4 = Null
    //     0xb1a70c: mov             x4, NULL
    // 0xb1a710: b               #0xb1a720
    // 0xb1a714: LoadField: r5 = r4->field_b
    //     0xb1a714: ldur            w5, [x4, #0xb]
    // 0xb1a718: DecompressPointer r5
    //     0xb1a718: add             x5, x5, HEAP, lsl #32
    // 0xb1a71c: mov             x4, x5
    // 0xb1a720: cmp             w4, NULL
    // 0xb1a724: b.ne            #0xb1a730
    // 0xb1a728: r4 = 0
    //     0xb1a728: movz            x4, #0
    // 0xb1a72c: b               #0xb1a740
    // 0xb1a730: r5 = LoadInt32Instr(r4)
    //     0xb1a730: sbfx            x5, x4, #1, #0x1f
    //     0xb1a734: tbz             w4, #0, #0xb1a73c
    //     0xb1a738: ldur            x5, [x4, #7]
    // 0xb1a73c: mov             x4, x5
    // 0xb1a740: stur            x4, [fp, #-0x48]
    // 0xb1a744: cmp             w2, NULL
    // 0xb1a748: b.ne            #0xb1a754
    // 0xb1a74c: r2 = Null
    //     0xb1a74c: mov             x2, NULL
    // 0xb1a750: b               #0xb1a774
    // 0xb1a754: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb1a754: ldur            w5, [x2, #0x17]
    // 0xb1a758: DecompressPointer r5
    //     0xb1a758: add             x5, x5, HEAP, lsl #32
    // 0xb1a75c: cmp             w5, NULL
    // 0xb1a760: b.ne            #0xb1a76c
    // 0xb1a764: r2 = Null
    //     0xb1a764: mov             x2, NULL
    // 0xb1a768: b               #0xb1a774
    // 0xb1a76c: LoadField: r2 = r5->field_f
    //     0xb1a76c: ldur            w2, [x5, #0xf]
    // 0xb1a770: DecompressPointer r2
    //     0xb1a770: add             x2, x2, HEAP, lsl #32
    // 0xb1a774: cmp             w2, NULL
    // 0xb1a778: b.ne            #0xb1a784
    // 0xb1a77c: r2 = 0
    //     0xb1a77c: movz            x2, #0
    // 0xb1a780: b               #0xb1a794
    // 0xb1a784: r5 = LoadInt32Instr(r2)
    //     0xb1a784: sbfx            x5, x2, #1, #0x1f
    //     0xb1a788: tbz             w2, #0, #0xb1a790
    //     0xb1a78c: ldur            x5, [x2, #7]
    // 0xb1a790: mov             x2, x5
    // 0xb1a794: stur            x2, [fp, #-0x40]
    // 0xb1a798: r0 = Color()
    //     0xb1a798: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb1a79c: mov             x1, x0
    // 0xb1a7a0: r0 = Instance_ColorSpace
    //     0xb1a7a0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb1a7a4: StoreField: r1->field_27 = r0
    //     0xb1a7a4: stur            w0, [x1, #0x27]
    // 0xb1a7a8: d0 = 1.000000
    //     0xb1a7a8: fmov            d0, #1.00000000
    // 0xb1a7ac: StoreField: r1->field_7 = d0
    //     0xb1a7ac: stur            d0, [x1, #7]
    // 0xb1a7b0: ldur            x2, [fp, #-0x50]
    // 0xb1a7b4: ubfx            x2, x2, #0, #0x20
    // 0xb1a7b8: and             w3, w2, #0xff
    // 0xb1a7bc: ubfx            x3, x3, #0, #0x20
    // 0xb1a7c0: scvtf           d1, x3
    // 0xb1a7c4: d2 = 255.000000
    //     0xb1a7c4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb1a7c8: fdiv            d3, d1, d2
    // 0xb1a7cc: StoreField: r1->field_f = d3
    //     0xb1a7cc: stur            d3, [x1, #0xf]
    // 0xb1a7d0: ldur            x2, [fp, #-0x48]
    // 0xb1a7d4: ubfx            x2, x2, #0, #0x20
    // 0xb1a7d8: and             w3, w2, #0xff
    // 0xb1a7dc: ubfx            x3, x3, #0, #0x20
    // 0xb1a7e0: scvtf           d1, x3
    // 0xb1a7e4: fdiv            d3, d1, d2
    // 0xb1a7e8: ArrayStore: r1[0] = d3  ; List_8
    //     0xb1a7e8: stur            d3, [x1, #0x17]
    // 0xb1a7ec: ldur            x2, [fp, #-0x40]
    // 0xb1a7f0: ubfx            x2, x2, #0, #0x20
    // 0xb1a7f4: and             w3, w2, #0xff
    // 0xb1a7f8: ubfx            x3, x3, #0, #0x20
    // 0xb1a7fc: scvtf           d1, x3
    // 0xb1a800: fdiv            d3, d1, d2
    // 0xb1a804: StoreField: r1->field_1f = d3
    //     0xb1a804: stur            d3, [x1, #0x1f]
    // 0xb1a808: r16 = <Color>
    //     0xb1a808: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb1a80c: ldr             x16, [x16, #0xf80]
    // 0xb1a810: stp             x1, x16, [SP]
    // 0xb1a814: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1a814: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1a818: r0 = all()
    //     0xb1a818: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb1a81c: mov             x1, x0
    // 0xb1a820: ldur            x0, [fp, #-8]
    // 0xb1a824: stur            x1, [fp, #-0x38]
    // 0xb1a828: LoadField: r2 = r0->field_b
    //     0xb1a828: ldur            w2, [x0, #0xb]
    // 0xb1a82c: DecompressPointer r2
    //     0xb1a82c: add             x2, x2, HEAP, lsl #32
    // 0xb1a830: cmp             w2, NULL
    // 0xb1a834: b.eq            #0xb1ad30
    // 0xb1a838: LoadField: r0 = r2->field_2f
    //     0xb1a838: ldur            w0, [x2, #0x2f]
    // 0xb1a83c: DecompressPointer r0
    //     0xb1a83c: add             x0, x0, HEAP, lsl #32
    // 0xb1a840: LoadField: r2 = r0->field_3f
    //     0xb1a840: ldur            w2, [x0, #0x3f]
    // 0xb1a844: DecompressPointer r2
    //     0xb1a844: add             x2, x2, HEAP, lsl #32
    // 0xb1a848: cmp             w2, NULL
    // 0xb1a84c: b.ne            #0xb1a858
    // 0xb1a850: r0 = Null
    //     0xb1a850: mov             x0, NULL
    // 0xb1a854: b               #0xb1a87c
    // 0xb1a858: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb1a858: ldur            w0, [x2, #0x17]
    // 0xb1a85c: DecompressPointer r0
    //     0xb1a85c: add             x0, x0, HEAP, lsl #32
    // 0xb1a860: cmp             w0, NULL
    // 0xb1a864: b.ne            #0xb1a870
    // 0xb1a868: r0 = Null
    //     0xb1a868: mov             x0, NULL
    // 0xb1a86c: b               #0xb1a87c
    // 0xb1a870: LoadField: r3 = r0->field_7
    //     0xb1a870: ldur            w3, [x0, #7]
    // 0xb1a874: DecompressPointer r3
    //     0xb1a874: add             x3, x3, HEAP, lsl #32
    // 0xb1a878: mov             x0, x3
    // 0xb1a87c: cmp             w0, NULL
    // 0xb1a880: b.ne            #0xb1a88c
    // 0xb1a884: r0 = 0
    //     0xb1a884: movz            x0, #0
    // 0xb1a888: b               #0xb1a89c
    // 0xb1a88c: r3 = LoadInt32Instr(r0)
    //     0xb1a88c: sbfx            x3, x0, #1, #0x1f
    //     0xb1a890: tbz             w0, #0, #0xb1a898
    //     0xb1a894: ldur            x3, [x0, #7]
    // 0xb1a898: mov             x0, x3
    // 0xb1a89c: stur            x0, [fp, #-0x50]
    // 0xb1a8a0: cmp             w2, NULL
    // 0xb1a8a4: b.ne            #0xb1a8b0
    // 0xb1a8a8: r3 = Null
    //     0xb1a8a8: mov             x3, NULL
    // 0xb1a8ac: b               #0xb1a8d4
    // 0xb1a8b0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb1a8b0: ldur            w3, [x2, #0x17]
    // 0xb1a8b4: DecompressPointer r3
    //     0xb1a8b4: add             x3, x3, HEAP, lsl #32
    // 0xb1a8b8: cmp             w3, NULL
    // 0xb1a8bc: b.ne            #0xb1a8c8
    // 0xb1a8c0: r3 = Null
    //     0xb1a8c0: mov             x3, NULL
    // 0xb1a8c4: b               #0xb1a8d4
    // 0xb1a8c8: LoadField: r4 = r3->field_b
    //     0xb1a8c8: ldur            w4, [x3, #0xb]
    // 0xb1a8cc: DecompressPointer r4
    //     0xb1a8cc: add             x4, x4, HEAP, lsl #32
    // 0xb1a8d0: mov             x3, x4
    // 0xb1a8d4: cmp             w3, NULL
    // 0xb1a8d8: b.ne            #0xb1a8e4
    // 0xb1a8dc: r3 = 0
    //     0xb1a8dc: movz            x3, #0
    // 0xb1a8e0: b               #0xb1a8f4
    // 0xb1a8e4: r4 = LoadInt32Instr(r3)
    //     0xb1a8e4: sbfx            x4, x3, #1, #0x1f
    //     0xb1a8e8: tbz             w3, #0, #0xb1a8f0
    //     0xb1a8ec: ldur            x4, [x3, #7]
    // 0xb1a8f0: mov             x3, x4
    // 0xb1a8f4: stur            x3, [fp, #-0x48]
    // 0xb1a8f8: cmp             w2, NULL
    // 0xb1a8fc: b.ne            #0xb1a908
    // 0xb1a900: r2 = Null
    //     0xb1a900: mov             x2, NULL
    // 0xb1a904: b               #0xb1a928
    // 0xb1a908: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb1a908: ldur            w4, [x2, #0x17]
    // 0xb1a90c: DecompressPointer r4
    //     0xb1a90c: add             x4, x4, HEAP, lsl #32
    // 0xb1a910: cmp             w4, NULL
    // 0xb1a914: b.ne            #0xb1a920
    // 0xb1a918: r2 = Null
    //     0xb1a918: mov             x2, NULL
    // 0xb1a91c: b               #0xb1a928
    // 0xb1a920: LoadField: r2 = r4->field_f
    //     0xb1a920: ldur            w2, [x4, #0xf]
    // 0xb1a924: DecompressPointer r2
    //     0xb1a924: add             x2, x2, HEAP, lsl #32
    // 0xb1a928: cmp             w2, NULL
    // 0xb1a92c: b.ne            #0xb1a938
    // 0xb1a930: r6 = 0
    //     0xb1a930: movz            x6, #0
    // 0xb1a934: b               #0xb1a948
    // 0xb1a938: r4 = LoadInt32Instr(r2)
    //     0xb1a938: sbfx            x4, x2, #1, #0x1f
    //     0xb1a93c: tbz             w2, #0, #0xb1a944
    //     0xb1a940: ldur            x4, [x2, #7]
    // 0xb1a944: mov             x6, x4
    // 0xb1a948: ldur            x5, [fp, #-0x20]
    // 0xb1a94c: ldur            x4, [fp, #-0x28]
    // 0xb1a950: ldur            x2, [fp, #-0x30]
    // 0xb1a954: stur            x6, [fp, #-0x40]
    // 0xb1a958: r0 = Color()
    //     0xb1a958: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb1a95c: mov             x1, x0
    // 0xb1a960: r0 = Instance_ColorSpace
    //     0xb1a960: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb1a964: stur            x1, [fp, #-8]
    // 0xb1a968: StoreField: r1->field_27 = r0
    //     0xb1a968: stur            w0, [x1, #0x27]
    // 0xb1a96c: d0 = 1.000000
    //     0xb1a96c: fmov            d0, #1.00000000
    // 0xb1a970: StoreField: r1->field_7 = d0
    //     0xb1a970: stur            d0, [x1, #7]
    // 0xb1a974: ldur            x0, [fp, #-0x50]
    // 0xb1a978: ubfx            x0, x0, #0, #0x20
    // 0xb1a97c: and             w2, w0, #0xff
    // 0xb1a980: ubfx            x2, x2, #0, #0x20
    // 0xb1a984: scvtf           d1, x2
    // 0xb1a988: d2 = 255.000000
    //     0xb1a988: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb1a98c: fdiv            d3, d1, d2
    // 0xb1a990: StoreField: r1->field_f = d3
    //     0xb1a990: stur            d3, [x1, #0xf]
    // 0xb1a994: ldur            x0, [fp, #-0x48]
    // 0xb1a998: ubfx            x0, x0, #0, #0x20
    // 0xb1a99c: and             w2, w0, #0xff
    // 0xb1a9a0: ubfx            x2, x2, #0, #0x20
    // 0xb1a9a4: scvtf           d1, x2
    // 0xb1a9a8: fdiv            d3, d1, d2
    // 0xb1a9ac: ArrayStore: r1[0] = d3  ; List_8
    //     0xb1a9ac: stur            d3, [x1, #0x17]
    // 0xb1a9b0: ldur            x0, [fp, #-0x40]
    // 0xb1a9b4: ubfx            x0, x0, #0, #0x20
    // 0xb1a9b8: and             w2, w0, #0xff
    // 0xb1a9bc: ubfx            x2, x2, #0, #0x20
    // 0xb1a9c0: scvtf           d1, x2
    // 0xb1a9c4: fdiv            d3, d1, d2
    // 0xb1a9c8: StoreField: r1->field_1f = d3
    //     0xb1a9c8: stur            d3, [x1, #0x1f]
    // 0xb1a9cc: r0 = BorderSide()
    //     0xb1a9cc: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb1a9d0: mov             x1, x0
    // 0xb1a9d4: ldur            x0, [fp, #-8]
    // 0xb1a9d8: stur            x1, [fp, #-0x58]
    // 0xb1a9dc: StoreField: r1->field_7 = r0
    //     0xb1a9dc: stur            w0, [x1, #7]
    // 0xb1a9e0: d0 = 1.000000
    //     0xb1a9e0: fmov            d0, #1.00000000
    // 0xb1a9e4: StoreField: r1->field_b = d0
    //     0xb1a9e4: stur            d0, [x1, #0xb]
    // 0xb1a9e8: r0 = Instance_BorderStyle
    //     0xb1a9e8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb1a9ec: ldr             x0, [x0, #0xf68]
    // 0xb1a9f0: StoreField: r1->field_13 = r0
    //     0xb1a9f0: stur            w0, [x1, #0x13]
    // 0xb1a9f4: d0 = -1.000000
    //     0xb1a9f4: fmov            d0, #-1.00000000
    // 0xb1a9f8: ArrayStore: r1[0] = d0  ; List_8
    //     0xb1a9f8: stur            d0, [x1, #0x17]
    // 0xb1a9fc: r0 = Radius()
    //     0xb1a9fc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1aa00: d0 = 32.000000
    //     0xb1aa00: add             x17, PP, #0x43, lsl #12  ; [pp+0x436c8] IMM: double(32) from 0x4040000000000000
    //     0xb1aa04: ldr             d0, [x17, #0x6c8]
    // 0xb1aa08: stur            x0, [fp, #-8]
    // 0xb1aa0c: StoreField: r0->field_7 = d0
    //     0xb1aa0c: stur            d0, [x0, #7]
    // 0xb1aa10: StoreField: r0->field_f = d0
    //     0xb1aa10: stur            d0, [x0, #0xf]
    // 0xb1aa14: r0 = BorderRadius()
    //     0xb1aa14: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1aa18: mov             x1, x0
    // 0xb1aa1c: ldur            x0, [fp, #-8]
    // 0xb1aa20: stur            x1, [fp, #-0x60]
    // 0xb1aa24: StoreField: r1->field_7 = r0
    //     0xb1aa24: stur            w0, [x1, #7]
    // 0xb1aa28: StoreField: r1->field_b = r0
    //     0xb1aa28: stur            w0, [x1, #0xb]
    // 0xb1aa2c: StoreField: r1->field_f = r0
    //     0xb1aa2c: stur            w0, [x1, #0xf]
    // 0xb1aa30: StoreField: r1->field_13 = r0
    //     0xb1aa30: stur            w0, [x1, #0x13]
    // 0xb1aa34: r0 = RoundedRectangleBorder()
    //     0xb1aa34: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb1aa38: mov             x1, x0
    // 0xb1aa3c: ldur            x0, [fp, #-0x60]
    // 0xb1aa40: StoreField: r1->field_b = r0
    //     0xb1aa40: stur            w0, [x1, #0xb]
    // 0xb1aa44: ldur            x0, [fp, #-0x58]
    // 0xb1aa48: StoreField: r1->field_7 = r0
    //     0xb1aa48: stur            w0, [x1, #7]
    // 0xb1aa4c: r16 = <RoundedRectangleBorder>
    //     0xb1aa4c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb1aa50: ldr             x16, [x16, #0xf78]
    // 0xb1aa54: stp             x1, x16, [SP]
    // 0xb1aa58: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1aa58: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1aa5c: r0 = all()
    //     0xb1aa5c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb1aa60: stur            x0, [fp, #-8]
    // 0xb1aa64: r0 = ButtonStyle()
    //     0xb1aa64: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb1aa68: mov             x1, x0
    // 0xb1aa6c: ldur            x0, [fp, #-0x38]
    // 0xb1aa70: stur            x1, [fp, #-0x58]
    // 0xb1aa74: StoreField: r1->field_b = r0
    //     0xb1aa74: stur            w0, [x1, #0xb]
    // 0xb1aa78: ldur            x0, [fp, #-0x30]
    // 0xb1aa7c: StoreField: r1->field_23 = r0
    //     0xb1aa7c: stur            w0, [x1, #0x23]
    // 0xb1aa80: ldur            x0, [fp, #-8]
    // 0xb1aa84: StoreField: r1->field_43 = r0
    //     0xb1aa84: stur            w0, [x1, #0x43]
    // 0xb1aa88: r0 = TextButtonThemeData()
    //     0xb1aa88: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb1aa8c: mov             x2, x0
    // 0xb1aa90: ldur            x0, [fp, #-0x58]
    // 0xb1aa94: stur            x2, [fp, #-8]
    // 0xb1aa98: StoreField: r2->field_7 = r0
    //     0xb1aa98: stur            w0, [x2, #7]
    // 0xb1aa9c: r16 = 14.000000
    //     0xb1aa9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1aaa0: ldr             x16, [x16, #0x1d8]
    // 0xb1aaa4: r30 = Instance_Color
    //     0xb1aaa4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1aaa8: stp             lr, x16, [SP]
    // 0xb1aaac: ldur            x1, [fp, #-0x18]
    // 0xb1aab0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1aab0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1aab4: ldr             x4, [x4, #0xaa0]
    // 0xb1aab8: r0 = copyWith()
    //     0xb1aab8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1aabc: stur            x0, [fp, #-0x18]
    // 0xb1aac0: r0 = Text()
    //     0xb1aac0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1aac4: mov             x3, x0
    // 0xb1aac8: r0 = "Yes, Exchange"
    //     0xb1aac8: add             x0, PP, #0x56, lsl #12  ; [pp+0x56058] "Yes, Exchange"
    //     0xb1aacc: ldr             x0, [x0, #0x58]
    // 0xb1aad0: stur            x3, [fp, #-0x30]
    // 0xb1aad4: StoreField: r3->field_b = r0
    //     0xb1aad4: stur            w0, [x3, #0xb]
    // 0xb1aad8: ldur            x0, [fp, #-0x18]
    // 0xb1aadc: StoreField: r3->field_13 = r0
    //     0xb1aadc: stur            w0, [x3, #0x13]
    // 0xb1aae0: ldur            x2, [fp, #-0x10]
    // 0xb1aae4: r1 = Function '<anonymous closure>':.
    //     0xb1aae4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58498] AnonymousClosure: (0xb1ad34), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/single_exchange_product_bottom_sheet.dart] _SingleExchangeProductBottomSheetState::build (0xb18c24)
    //     0xb1aae8: ldr             x1, [x1, #0x498]
    // 0xb1aaec: r0 = AllocateClosure()
    //     0xb1aaec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1aaf0: stur            x0, [fp, #-0x10]
    // 0xb1aaf4: r0 = TextButton()
    //     0xb1aaf4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb1aaf8: mov             x1, x0
    // 0xb1aafc: ldur            x0, [fp, #-0x10]
    // 0xb1ab00: stur            x1, [fp, #-0x18]
    // 0xb1ab04: StoreField: r1->field_b = r0
    //     0xb1ab04: stur            w0, [x1, #0xb]
    // 0xb1ab08: r0 = false
    //     0xb1ab08: add             x0, NULL, #0x30  ; false
    // 0xb1ab0c: StoreField: r1->field_27 = r0
    //     0xb1ab0c: stur            w0, [x1, #0x27]
    // 0xb1ab10: r0 = true
    //     0xb1ab10: add             x0, NULL, #0x20  ; true
    // 0xb1ab14: StoreField: r1->field_2f = r0
    //     0xb1ab14: stur            w0, [x1, #0x2f]
    // 0xb1ab18: ldur            x0, [fp, #-0x30]
    // 0xb1ab1c: StoreField: r1->field_37 = r0
    //     0xb1ab1c: stur            w0, [x1, #0x37]
    // 0xb1ab20: r0 = TextButtonTheme()
    //     0xb1ab20: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb1ab24: mov             x2, x0
    // 0xb1ab28: ldur            x0, [fp, #-8]
    // 0xb1ab2c: stur            x2, [fp, #-0x10]
    // 0xb1ab30: StoreField: r2->field_f = r0
    //     0xb1ab30: stur            w0, [x2, #0xf]
    // 0xb1ab34: ldur            x0, [fp, #-0x18]
    // 0xb1ab38: StoreField: r2->field_b = r0
    //     0xb1ab38: stur            w0, [x2, #0xb]
    // 0xb1ab3c: r1 = <FlexParentData>
    //     0xb1ab3c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb1ab40: ldr             x1, [x1, #0xe00]
    // 0xb1ab44: r0 = Expanded()
    //     0xb1ab44: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb1ab48: mov             x3, x0
    // 0xb1ab4c: r0 = 1
    //     0xb1ab4c: movz            x0, #0x1
    // 0xb1ab50: stur            x3, [fp, #-8]
    // 0xb1ab54: StoreField: r3->field_13 = r0
    //     0xb1ab54: stur            x0, [x3, #0x13]
    // 0xb1ab58: r0 = Instance_FlexFit
    //     0xb1ab58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb1ab5c: ldr             x0, [x0, #0xe08]
    // 0xb1ab60: StoreField: r3->field_1b = r0
    //     0xb1ab60: stur            w0, [x3, #0x1b]
    // 0xb1ab64: ldur            x0, [fp, #-0x10]
    // 0xb1ab68: StoreField: r3->field_b = r0
    //     0xb1ab68: stur            w0, [x3, #0xb]
    // 0xb1ab6c: r1 = Null
    //     0xb1ab6c: mov             x1, NULL
    // 0xb1ab70: r2 = 6
    //     0xb1ab70: movz            x2, #0x6
    // 0xb1ab74: r0 = AllocateArray()
    //     0xb1ab74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1ab78: mov             x2, x0
    // 0xb1ab7c: ldur            x0, [fp, #-0x28]
    // 0xb1ab80: stur            x2, [fp, #-0x10]
    // 0xb1ab84: StoreField: r2->field_f = r0
    //     0xb1ab84: stur            w0, [x2, #0xf]
    // 0xb1ab88: r16 = Instance_SizedBox
    //     0xb1ab88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb1ab8c: ldr             x16, [x16, #0x998]
    // 0xb1ab90: StoreField: r2->field_13 = r16
    //     0xb1ab90: stur            w16, [x2, #0x13]
    // 0xb1ab94: ldur            x0, [fp, #-8]
    // 0xb1ab98: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1ab98: stur            w0, [x2, #0x17]
    // 0xb1ab9c: r1 = <Widget>
    //     0xb1ab9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1aba0: r0 = AllocateGrowableArray()
    //     0xb1aba0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1aba4: mov             x1, x0
    // 0xb1aba8: ldur            x0, [fp, #-0x10]
    // 0xb1abac: stur            x1, [fp, #-8]
    // 0xb1abb0: StoreField: r1->field_f = r0
    //     0xb1abb0: stur            w0, [x1, #0xf]
    // 0xb1abb4: r0 = 6
    //     0xb1abb4: movz            x0, #0x6
    // 0xb1abb8: StoreField: r1->field_b = r0
    //     0xb1abb8: stur            w0, [x1, #0xb]
    // 0xb1abbc: r0 = Row()
    //     0xb1abbc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1abc0: mov             x1, x0
    // 0xb1abc4: r0 = Instance_Axis
    //     0xb1abc4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1abc8: stur            x1, [fp, #-0x10]
    // 0xb1abcc: StoreField: r1->field_f = r0
    //     0xb1abcc: stur            w0, [x1, #0xf]
    // 0xb1abd0: r0 = Instance_MainAxisAlignment
    //     0xb1abd0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb1abd4: ldr             x0, [x0, #0xa8]
    // 0xb1abd8: StoreField: r1->field_13 = r0
    //     0xb1abd8: stur            w0, [x1, #0x13]
    // 0xb1abdc: r0 = Instance_MainAxisSize
    //     0xb1abdc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1abe0: ldr             x0, [x0, #0xa10]
    // 0xb1abe4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1abe4: stur            w0, [x1, #0x17]
    // 0xb1abe8: r0 = Instance_CrossAxisAlignment
    //     0xb1abe8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1abec: ldr             x0, [x0, #0xa18]
    // 0xb1abf0: StoreField: r1->field_1b = r0
    //     0xb1abf0: stur            w0, [x1, #0x1b]
    // 0xb1abf4: r0 = Instance_VerticalDirection
    //     0xb1abf4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1abf8: ldr             x0, [x0, #0xa20]
    // 0xb1abfc: StoreField: r1->field_23 = r0
    //     0xb1abfc: stur            w0, [x1, #0x23]
    // 0xb1ac00: r2 = Instance_Clip
    //     0xb1ac00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1ac04: ldr             x2, [x2, #0x38]
    // 0xb1ac08: StoreField: r1->field_2b = r2
    //     0xb1ac08: stur            w2, [x1, #0x2b]
    // 0xb1ac0c: StoreField: r1->field_2f = rZR
    //     0xb1ac0c: stur            xzr, [x1, #0x2f]
    // 0xb1ac10: ldur            x3, [fp, #-8]
    // 0xb1ac14: StoreField: r1->field_b = r3
    //     0xb1ac14: stur            w3, [x1, #0xb]
    // 0xb1ac18: r0 = Container()
    //     0xb1ac18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1ac1c: stur            x0, [fp, #-8]
    // 0xb1ac20: r16 = Instance_Color
    //     0xb1ac20: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1ac24: r30 = Instance_EdgeInsets
    //     0xb1ac24: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xb1ac28: ldr             lr, [lr, #0x560]
    // 0xb1ac2c: stp             lr, x16, [SP, #8]
    // 0xb1ac30: ldur            x16, [fp, #-0x10]
    // 0xb1ac34: str             x16, [SP]
    // 0xb1ac38: mov             x1, x0
    // 0xb1ac3c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, padding, 0x2, null]
    //     0xb1ac3c: add             x4, PP, #0x45, lsl #12  ; [pp+0x45c40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "padding", 0x2, Null]
    //     0xb1ac40: ldr             x4, [x4, #0xc40]
    // 0xb1ac44: r0 = Container()
    //     0xb1ac44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1ac48: r1 = Null
    //     0xb1ac48: mov             x1, NULL
    // 0xb1ac4c: r2 = 4
    //     0xb1ac4c: movz            x2, #0x4
    // 0xb1ac50: r0 = AllocateArray()
    //     0xb1ac50: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1ac54: mov             x2, x0
    // 0xb1ac58: ldur            x0, [fp, #-0x20]
    // 0xb1ac5c: stur            x2, [fp, #-0x10]
    // 0xb1ac60: StoreField: r2->field_f = r0
    //     0xb1ac60: stur            w0, [x2, #0xf]
    // 0xb1ac64: ldur            x0, [fp, #-8]
    // 0xb1ac68: StoreField: r2->field_13 = r0
    //     0xb1ac68: stur            w0, [x2, #0x13]
    // 0xb1ac6c: r1 = <Widget>
    //     0xb1ac6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1ac70: r0 = AllocateGrowableArray()
    //     0xb1ac70: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1ac74: mov             x1, x0
    // 0xb1ac78: ldur            x0, [fp, #-0x10]
    // 0xb1ac7c: stur            x1, [fp, #-8]
    // 0xb1ac80: StoreField: r1->field_f = r0
    //     0xb1ac80: stur            w0, [x1, #0xf]
    // 0xb1ac84: r0 = 4
    //     0xb1ac84: movz            x0, #0x4
    // 0xb1ac88: StoreField: r1->field_b = r0
    //     0xb1ac88: stur            w0, [x1, #0xb]
    // 0xb1ac8c: r0 = Column()
    //     0xb1ac8c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1ac90: r1 = Instance_Axis
    //     0xb1ac90: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1ac94: StoreField: r0->field_f = r1
    //     0xb1ac94: stur            w1, [x0, #0xf]
    // 0xb1ac98: r1 = Instance_MainAxisAlignment
    //     0xb1ac98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1ac9c: ldr             x1, [x1, #0xa08]
    // 0xb1aca0: StoreField: r0->field_13 = r1
    //     0xb1aca0: stur            w1, [x0, #0x13]
    // 0xb1aca4: r1 = Instance_MainAxisSize
    //     0xb1aca4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb1aca8: ldr             x1, [x1, #0xdd0]
    // 0xb1acac: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1acac: stur            w1, [x0, #0x17]
    // 0xb1acb0: r1 = Instance_CrossAxisAlignment
    //     0xb1acb0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1acb4: ldr             x1, [x1, #0x890]
    // 0xb1acb8: StoreField: r0->field_1b = r1
    //     0xb1acb8: stur            w1, [x0, #0x1b]
    // 0xb1acbc: r1 = Instance_VerticalDirection
    //     0xb1acbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1acc0: ldr             x1, [x1, #0xa20]
    // 0xb1acc4: StoreField: r0->field_23 = r1
    //     0xb1acc4: stur            w1, [x0, #0x23]
    // 0xb1acc8: r1 = Instance_Clip
    //     0xb1acc8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1accc: ldr             x1, [x1, #0x38]
    // 0xb1acd0: StoreField: r0->field_2b = r1
    //     0xb1acd0: stur            w1, [x0, #0x2b]
    // 0xb1acd4: StoreField: r0->field_2f = rZR
    //     0xb1acd4: stur            xzr, [x0, #0x2f]
    // 0xb1acd8: ldur            x1, [fp, #-8]
    // 0xb1acdc: StoreField: r0->field_b = r1
    //     0xb1acdc: stur            w1, [x0, #0xb]
    // 0xb1ace0: LeaveFrame
    //     0xb1ace0: mov             SP, fp
    //     0xb1ace4: ldp             fp, lr, [SP], #0x10
    // 0xb1ace8: ret
    //     0xb1ace8: ret             
    // 0xb1acec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1acec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1acf0: b               #0xb18c40
    // 0xb1acf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1acf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1acf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1acf8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1acfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1acfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1ad30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ad30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1ad34, size: 0xb4
    // 0xb1ad34: EnterFrame
    //     0xb1ad34: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ad38: mov             fp, SP
    // 0xb1ad3c: AllocStack(0x10)
    //     0xb1ad3c: sub             SP, SP, #0x10
    // 0xb1ad40: SetupParameters()
    //     0xb1ad40: ldr             x0, [fp, #0x10]
    //     0xb1ad44: ldur            w2, [x0, #0x17]
    //     0xb1ad48: add             x2, x2, HEAP, lsl #32
    //     0xb1ad4c: stur            x2, [fp, #-8]
    // 0xb1ad50: CheckStackOverflow
    //     0xb1ad50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ad54: cmp             SP, x16
    //     0xb1ad58: b.ls            #0xb1addc
    // 0xb1ad5c: LoadField: r0 = r2->field_f
    //     0xb1ad5c: ldur            w0, [x2, #0xf]
    // 0xb1ad60: DecompressPointer r0
    //     0xb1ad60: add             x0, x0, HEAP, lsl #32
    // 0xb1ad64: LoadField: r1 = r0->field_13
    //     0xb1ad64: ldur            w1, [x0, #0x13]
    // 0xb1ad68: DecompressPointer r1
    //     0xb1ad68: add             x1, x1, HEAP, lsl #32
    // 0xb1ad6c: r0 = exchangeBuyNowProceedPostEvent()
    //     0xb1ad6c: bl              #0xa9ab50  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::exchangeBuyNowProceedPostEvent
    // 0xb1ad70: ldur            x0, [fp, #-8]
    // 0xb1ad74: LoadField: r1 = r0->field_f
    //     0xb1ad74: ldur            w1, [x0, #0xf]
    // 0xb1ad78: DecompressPointer r1
    //     0xb1ad78: add             x1, x1, HEAP, lsl #32
    // 0xb1ad7c: LoadField: r2 = r1->field_13
    //     0xb1ad7c: ldur            w2, [x1, #0x13]
    // 0xb1ad80: DecompressPointer r2
    //     0xb1ad80: add             x2, x2, HEAP, lsl #32
    // 0xb1ad84: mov             x1, x2
    // 0xb1ad88: r0 = ctaExchangeCheckoutInitiatedPostEvent()
    //     0xb1ad88: bl              #0xa9a804  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::ctaExchangeCheckoutInitiatedPostEvent
    // 0xb1ad8c: ldur            x0, [fp, #-8]
    // 0xb1ad90: LoadField: r1 = r0->field_f
    //     0xb1ad90: ldur            w1, [x0, #0xf]
    // 0xb1ad94: DecompressPointer r1
    //     0xb1ad94: add             x1, x1, HEAP, lsl #32
    // 0xb1ad98: LoadField: r0 = r1->field_b
    //     0xb1ad98: ldur            w0, [x1, #0xb]
    // 0xb1ad9c: DecompressPointer r0
    //     0xb1ad9c: add             x0, x0, HEAP, lsl #32
    // 0xb1ada0: cmp             w0, NULL
    // 0xb1ada4: b.eq            #0xb1ade4
    // 0xb1ada8: LoadField: r1 = r0->field_23
    //     0xb1ada8: ldur            w1, [x0, #0x23]
    // 0xb1adac: DecompressPointer r1
    //     0xb1adac: add             x1, x1, HEAP, lsl #32
    // 0xb1adb0: str             x1, [SP]
    // 0xb1adb4: r4 = 0
    //     0xb1adb4: movz            x4, #0
    // 0xb1adb8: ldr             x0, [SP]
    // 0xb1adbc: r16 = UnlinkedCall_0x613b5c
    //     0xb1adbc: add             x16, PP, #0x58, lsl #12  ; [pp+0x584a0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1adc0: add             x16, x16, #0x4a0
    // 0xb1adc4: ldp             x5, lr, [x16]
    // 0xb1adc8: blr             lr
    // 0xb1adcc: r0 = Null
    //     0xb1adcc: mov             x0, NULL
    // 0xb1add0: LeaveFrame
    //     0xb1add0: mov             SP, fp
    //     0xb1add4: ldp             fp, lr, [SP], #0x10
    // 0xb1add8: ret
    //     0xb1add8: ret             
    // 0xb1addc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1addc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1ade0: b               #0xb1ad5c
    // 0xb1ade4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1ade4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1ade8, size: 0xbc
    // 0xb1ade8: EnterFrame
    //     0xb1ade8: stp             fp, lr, [SP, #-0x10]!
    //     0xb1adec: mov             fp, SP
    // 0xb1adf0: AllocStack(0x10)
    //     0xb1adf0: sub             SP, SP, #0x10
    // 0xb1adf4: SetupParameters()
    //     0xb1adf4: ldr             x0, [fp, #0x10]
    //     0xb1adf8: ldur            w2, [x0, #0x17]
    //     0xb1adfc: add             x2, x2, HEAP, lsl #32
    //     0xb1ae00: stur            x2, [fp, #-8]
    // 0xb1ae04: CheckStackOverflow
    //     0xb1ae04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ae08: cmp             SP, x16
    //     0xb1ae0c: b.ls            #0xb1ae98
    // 0xb1ae10: LoadField: r0 = r2->field_f
    //     0xb1ae10: ldur            w0, [x2, #0xf]
    // 0xb1ae14: DecompressPointer r0
    //     0xb1ae14: add             x0, x0, HEAP, lsl #32
    // 0xb1ae18: LoadField: r1 = r0->field_13
    //     0xb1ae18: ldur            w1, [x0, #0x13]
    // 0xb1ae1c: DecompressPointer r1
    //     0xb1ae1c: add             x1, x1, HEAP, lsl #32
    // 0xb1ae20: r0 = postBuyNowEvent()
    //     0xb1ae20: bl              #0xa9af10  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::postBuyNowEvent
    // 0xb1ae24: ldur            x0, [fp, #-8]
    // 0xb1ae28: LoadField: r1 = r0->field_f
    //     0xb1ae28: ldur            w1, [x0, #0xf]
    // 0xb1ae2c: DecompressPointer r1
    //     0xb1ae2c: add             x1, x1, HEAP, lsl #32
    // 0xb1ae30: LoadField: r2 = r1->field_13
    //     0xb1ae30: ldur            w2, [x1, #0x13]
    // 0xb1ae34: DecompressPointer r2
    //     0xb1ae34: add             x2, x2, HEAP, lsl #32
    // 0xb1ae38: mov             x1, x2
    // 0xb1ae3c: r2 = "buy_now"
    //     0xb1ae3c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe78] "buy_now"
    //     0xb1ae40: ldr             x2, [x2, #0xe78]
    // 0xb1ae44: r0 = checkoutStartedPostEvent()
    //     0xb1ae44: bl              #0xa9ad70  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::checkoutStartedPostEvent
    // 0xb1ae48: ldur            x0, [fp, #-8]
    // 0xb1ae4c: LoadField: r1 = r0->field_f
    //     0xb1ae4c: ldur            w1, [x0, #0xf]
    // 0xb1ae50: DecompressPointer r1
    //     0xb1ae50: add             x1, x1, HEAP, lsl #32
    // 0xb1ae54: LoadField: r0 = r1->field_b
    //     0xb1ae54: ldur            w0, [x1, #0xb]
    // 0xb1ae58: DecompressPointer r0
    //     0xb1ae58: add             x0, x0, HEAP, lsl #32
    // 0xb1ae5c: cmp             w0, NULL
    // 0xb1ae60: b.eq            #0xb1aea0
    // 0xb1ae64: LoadField: r1 = r0->field_1f
    //     0xb1ae64: ldur            w1, [x0, #0x1f]
    // 0xb1ae68: DecompressPointer r1
    //     0xb1ae68: add             x1, x1, HEAP, lsl #32
    // 0xb1ae6c: str             x1, [SP]
    // 0xb1ae70: r4 = 0
    //     0xb1ae70: movz            x4, #0
    // 0xb1ae74: ldr             x0, [SP]
    // 0xb1ae78: r16 = UnlinkedCall_0x613b5c
    //     0xb1ae78: add             x16, PP, #0x58, lsl #12  ; [pp+0x584b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ae7c: add             x16, x16, #0x4b0
    // 0xb1ae80: ldp             x5, lr, [x16]
    // 0xb1ae84: blr             lr
    // 0xb1ae88: r0 = Null
    //     0xb1ae88: mov             x0, NULL
    // 0xb1ae8c: LeaveFrame
    //     0xb1ae8c: mov             SP, fp
    //     0xb1ae90: ldp             fp, lr, [SP], #0x10
    // 0xb1ae94: ret
    //     0xb1ae94: ret             
    // 0xb1ae98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1ae98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1ae9c: b               #0xb1ae10
    // 0xb1aea0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1aea0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4131, size: 0x34, field offset: 0xc
//   const constructor, 
class SingleExchangeProductBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e2a4, size: 0x74
    // 0xc7e2a4: EnterFrame
    //     0xc7e2a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e2a8: mov             fp, SP
    // 0xc7e2ac: AllocStack(0x10)
    //     0xc7e2ac: sub             SP, SP, #0x10
    // 0xc7e2b0: CheckStackOverflow
    //     0xc7e2b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e2b4: cmp             SP, x16
    //     0xc7e2b8: b.ls            #0xc7e310
    // 0xc7e2bc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc7e2bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7e2c0: ldr             x0, [x0, #0x1c80]
    //     0xc7e2c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7e2c8: cmp             w0, w16
    //     0xc7e2cc: b.ne            #0xc7e2d8
    //     0xc7e2d0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc7e2d4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7e2d8: r16 = <ProductDetailController>
    //     0xc7e2d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xc7e2dc: ldr             x16, [x16, #0xde0]
    // 0xc7e2e0: str             x16, [SP]
    // 0xc7e2e4: r4 = const [0x1, 0, 0, 0, null]
    //     0xc7e2e4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xc7e2e8: r0 = Inst.find()
    //     0xc7e2e8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xc7e2ec: r1 = <SingleExchangeProductBottomSheet>
    //     0xc7e2ec: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c10] TypeArguments: <SingleExchangeProductBottomSheet>
    //     0xc7e2f0: ldr             x1, [x1, #0xc10]
    // 0xc7e2f4: stur            x0, [fp, #-8]
    // 0xc7e2f8: r0 = _SingleExchangeProductBottomSheetState()
    //     0xc7e2f8: bl              #0xc7e318  ; Allocate_SingleExchangeProductBottomSheetStateStub -> _SingleExchangeProductBottomSheetState (size=0x18)
    // 0xc7e2fc: ldur            x1, [fp, #-8]
    // 0xc7e300: StoreField: r0->field_13 = r1
    //     0xc7e300: stur            w1, [x0, #0x13]
    // 0xc7e304: LeaveFrame
    //     0xc7e304: mov             SP, fp
    //     0xc7e308: ldp             fp, lr, [SP], #0x10
    // 0xc7e30c: ret
    //     0xc7e30c: ret             
    // 0xc7e310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e310: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e314: b               #0xc7e2bc
  }
}
