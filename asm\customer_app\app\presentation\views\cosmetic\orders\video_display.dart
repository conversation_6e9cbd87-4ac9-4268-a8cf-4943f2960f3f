// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/video_display.dart

// class id: 1049296, size: 0x8
class :: {
}

// class id: 3412, size: 0x18, field offset: 0x14
class VideoPlayerWidgetState extends State<dynamic> {

  late VideoPlayerController _controller; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93c474, size: 0x14c
    // 0x93c474: EnterFrame
    //     0x93c474: stp             fp, lr, [SP, #-0x10]!
    //     0x93c478: mov             fp, SP
    // 0x93c47c: AllocStack(0x40)
    //     0x93c47c: sub             SP, SP, #0x40
    // 0x93c480: SetupParameters(VideoPlayerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x93c480: stur            x1, [fp, #-8]
    // 0x93c484: CheckStackOverflow
    //     0x93c484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93c488: cmp             SP, x16
    //     0x93c48c: b.ls            #0x93c5b0
    // 0x93c490: r1 = 1
    //     0x93c490: movz            x1, #0x1
    // 0x93c494: r0 = AllocateContext()
    //     0x93c494: bl              #0x16f6108  ; AllocateContextStub
    // 0x93c498: mov             x1, x0
    // 0x93c49c: ldur            x0, [fp, #-8]
    // 0x93c4a0: stur            x1, [fp, #-0x18]
    // 0x93c4a4: StoreField: r1->field_f = r0
    //     0x93c4a4: stur            w0, [x1, #0xf]
    // 0x93c4a8: LoadField: r2 = r0->field_b
    //     0x93c4a8: ldur            w2, [x0, #0xb]
    // 0x93c4ac: DecompressPointer r2
    //     0x93c4ac: add             x2, x2, HEAP, lsl #32
    // 0x93c4b0: cmp             w2, NULL
    // 0x93c4b4: b.eq            #0x93c5b8
    // 0x93c4b8: LoadField: r3 = r2->field_b
    //     0x93c4b8: ldur            w3, [x2, #0xb]
    // 0x93c4bc: DecompressPointer r3
    //     0x93c4bc: add             x3, x3, HEAP, lsl #32
    // 0x93c4c0: stur            x3, [fp, #-0x10]
    // 0x93c4c4: r0 = VideoPlayerOptions()
    //     0x93c4c4: bl              #0x93437c  ; AllocateVideoPlayerOptionsStub -> VideoPlayerOptions (size=0x10)
    // 0x93c4c8: mov             x2, x0
    // 0x93c4cc: r0 = false
    //     0x93c4cc: add             x0, NULL, #0x30  ; false
    // 0x93c4d0: stur            x2, [fp, #-0x20]
    // 0x93c4d4: StoreField: r2->field_b = r0
    //     0x93c4d4: stur            w0, [x2, #0xb]
    // 0x93c4d8: StoreField: r2->field_7 = r0
    //     0x93c4d8: stur            w0, [x2, #7]
    // 0x93c4dc: r1 = <VideoPlayerValue>
    //     0x93c4dc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52f90] TypeArguments: <VideoPlayerValue>
    //     0x93c4e0: ldr             x1, [x1, #0xf90]
    // 0x93c4e4: r0 = VideoPlayerController()
    //     0x93c4e4: bl              #0x8f9c94  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x68)
    // 0x93c4e8: stur            x0, [fp, #-0x28]
    // 0x93c4ec: ldur            x16, [fp, #-0x20]
    // 0x93c4f0: str             x16, [SP]
    // 0x93c4f4: mov             x1, x0
    // 0x93c4f8: ldur            x2, [fp, #-0x10]
    // 0x93c4fc: r4 = const [0, 0x3, 0x1, 0x2, videoPlayerOptions, 0x2, null]
    //     0x93c4fc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52f98] List(7) [0, 0x3, 0x1, 0x2, "videoPlayerOptions", 0x2, Null]
    //     0x93c500: ldr             x4, [x4, #0xf98]
    // 0x93c504: r0 = VideoPlayerController.network()
    //     0x93c504: bl              #0x934250  ; [package:video_player/video_player.dart] VideoPlayerController::VideoPlayerController.network
    // 0x93c508: ldur            x1, [fp, #-0x28]
    // 0x93c50c: r0 = initialize()
    //     0x93c50c: bl              #0x8f78ec  ; [package:video_player/video_player.dart] VideoPlayerController::initialize
    // 0x93c510: ldur            x2, [fp, #-0x18]
    // 0x93c514: r1 = Function '<anonymous closure>':.
    //     0x93c514: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f68] AnonymousClosure: (0x93c5e4), in [package:customer_app/app/presentation/views/cosmetic/orders/video_display.dart] VideoPlayerWidgetState::initState (0x93c474)
    //     0x93c518: ldr             x1, [x1, #0xf68]
    // 0x93c51c: stur            x0, [fp, #-0x10]
    // 0x93c520: r0 = AllocateClosure()
    //     0x93c520: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93c524: r16 = <Null?>
    //     0x93c524: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x93c528: ldur            lr, [fp, #-0x10]
    // 0x93c52c: stp             lr, x16, [SP, #8]
    // 0x93c530: str             x0, [SP]
    // 0x93c534: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x93c534: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x93c538: r0 = then()
    //     0x93c538: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x93c53c: ldur            x0, [fp, #-0x28]
    // 0x93c540: ldur            x3, [fp, #-8]
    // 0x93c544: StoreField: r3->field_13 = r0
    //     0x93c544: stur            w0, [x3, #0x13]
    //     0x93c548: ldurb           w16, [x3, #-1]
    //     0x93c54c: ldurb           w17, [x0, #-1]
    //     0x93c550: and             x16, x17, x16, lsr #2
    //     0x93c554: tst             x16, HEAP, lsr #32
    //     0x93c558: b.eq            #0x93c560
    //     0x93c55c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x93c560: LoadField: r0 = r3->field_b
    //     0x93c560: ldur            w0, [x3, #0xb]
    // 0x93c564: DecompressPointer r0
    //     0x93c564: add             x0, x0, HEAP, lsl #32
    // 0x93c568: cmp             w0, NULL
    // 0x93c56c: b.eq            #0x93c5bc
    // 0x93c570: LoadField: r1 = r0->field_f
    //     0x93c570: ldur            w1, [x0, #0xf]
    // 0x93c574: DecompressPointer r1
    //     0x93c574: add             x1, x1, HEAP, lsl #32
    // 0x93c578: cmp             w1, NULL
    // 0x93c57c: b.eq            #0x93c5a0
    // 0x93c580: tbnz            w1, #4, #0x93c5a0
    // 0x93c584: ldur            x1, [fp, #-0x28]
    // 0x93c588: r2 = true
    //     0x93c588: add             x2, NULL, #0x20  ; true
    // 0x93c58c: r0 = setLooping()
    //     0x93c58c: bl              #0x8faa24  ; [package:video_player/video_player.dart] VideoPlayerController::setLooping
    // 0x93c590: ldur            x0, [fp, #-8]
    // 0x93c594: LoadField: r1 = r0->field_13
    //     0x93c594: ldur            w1, [x0, #0x13]
    // 0x93c598: DecompressPointer r1
    //     0x93c598: add             x1, x1, HEAP, lsl #32
    // 0x93c59c: r0 = play()
    //     0x93c59c: bl              #0x6ec380  ; [package:video_player/video_player.dart] VideoPlayerController::play
    // 0x93c5a0: r0 = Null
    //     0x93c5a0: mov             x0, NULL
    // 0x93c5a4: LeaveFrame
    //     0x93c5a4: mov             SP, fp
    //     0x93c5a8: ldp             fp, lr, [SP], #0x10
    // 0x93c5ac: ret
    //     0x93c5ac: ret             
    // 0x93c5b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93c5b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93c5b4: b               #0x93c490
    // 0x93c5b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93c5b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93c5bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93c5bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x93c5e4, size: 0x64
    // 0x93c5e4: EnterFrame
    //     0x93c5e4: stp             fp, lr, [SP, #-0x10]!
    //     0x93c5e8: mov             fp, SP
    // 0x93c5ec: AllocStack(0x8)
    //     0x93c5ec: sub             SP, SP, #8
    // 0x93c5f0: SetupParameters()
    //     0x93c5f0: ldr             x0, [fp, #0x18]
    //     0x93c5f4: ldur            w1, [x0, #0x17]
    //     0x93c5f8: add             x1, x1, HEAP, lsl #32
    // 0x93c5fc: CheckStackOverflow
    //     0x93c5fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93c600: cmp             SP, x16
    //     0x93c604: b.ls            #0x93c640
    // 0x93c608: LoadField: r0 = r1->field_f
    //     0x93c608: ldur            w0, [x1, #0xf]
    // 0x93c60c: DecompressPointer r0
    //     0x93c60c: add             x0, x0, HEAP, lsl #32
    // 0x93c610: stur            x0, [fp, #-8]
    // 0x93c614: r1 = Function '<anonymous closure>':.
    //     0x93c614: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f70] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x93c618: ldr             x1, [x1, #0xf70]
    // 0x93c61c: r2 = Null
    //     0x93c61c: mov             x2, NULL
    // 0x93c620: r0 = AllocateClosure()
    //     0x93c620: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93c624: ldur            x1, [fp, #-8]
    // 0x93c628: mov             x2, x0
    // 0x93c62c: r0 = setState()
    //     0x93c62c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93c630: r0 = Null
    //     0x93c630: mov             x0, NULL
    // 0x93c634: LeaveFrame
    //     0x93c634: mov             SP, fp
    //     0x93c638: ldp             fp, lr, [SP], #0x10
    // 0x93c63c: ret
    //     0x93c63c: ret             
    // 0x93c640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93c640: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93c644: b               #0x93c608
  }
  _ build(/* No info */) {
    // ** addr: 0xafef94, size: 0x34c
    // 0xafef94: EnterFrame
    //     0xafef94: stp             fp, lr, [SP, #-0x10]!
    //     0xafef98: mov             fp, SP
    // 0xafef9c: AllocStack(0x40)
    //     0xafef9c: sub             SP, SP, #0x40
    // 0xafefa0: SetupParameters(VideoPlayerWidgetState this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0xafefa0: mov             x0, x1
    //     0xafefa4: stur            x1, [fp, #-0x10]
    //     0xafefa8: mov             x1, x2
    // 0xafefac: CheckStackOverflow
    //     0xafefac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafefb0: cmp             SP, x16
    //     0xafefb4: b.ls            #0xaff2c8
    // 0xafefb8: LoadField: r2 = r0->field_13
    //     0xafefb8: ldur            w2, [x0, #0x13]
    // 0xafefbc: DecompressPointer r2
    //     0xafefbc: add             x2, x2, HEAP, lsl #32
    // 0xafefc0: r16 = Sentinel
    //     0xafefc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xafefc4: cmp             w2, w16
    // 0xafefc8: b.eq            #0xaff2d0
    // 0xafefcc: stur            x2, [fp, #-8]
    // 0xafefd0: LoadField: r3 = r2->field_27
    //     0xafefd0: ldur            w3, [x2, #0x27]
    // 0xafefd4: DecompressPointer r3
    //     0xafefd4: add             x3, x3, HEAP, lsl #32
    // 0xafefd8: LoadField: r4 = r3->field_4b
    //     0xafefd8: ldur            w4, [x3, #0x4b]
    // 0xafefdc: DecompressPointer r4
    //     0xafefdc: add             x4, x4, HEAP, lsl #32
    // 0xafefe0: tbnz            w4, #4, #0xaff208
    // 0xafefe4: r0 = Radius()
    //     0xafefe4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafefe8: d0 = 15.000000
    //     0xafefe8: fmov            d0, #15.00000000
    // 0xafefec: stur            x0, [fp, #-0x18]
    // 0xafeff0: StoreField: r0->field_7 = d0
    //     0xafeff0: stur            d0, [x0, #7]
    // 0xafeff4: StoreField: r0->field_f = d0
    //     0xafeff4: stur            d0, [x0, #0xf]
    // 0xafeff8: r0 = BorderRadius()
    //     0xafeff8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafeffc: mov             x1, x0
    // 0xaff000: ldur            x0, [fp, #-0x18]
    // 0xaff004: stur            x1, [fp, #-0x20]
    // 0xaff008: StoreField: r1->field_7 = r0
    //     0xaff008: stur            w0, [x1, #7]
    // 0xaff00c: StoreField: r1->field_b = r0
    //     0xaff00c: stur            w0, [x1, #0xb]
    // 0xaff010: StoreField: r1->field_f = r0
    //     0xaff010: stur            w0, [x1, #0xf]
    // 0xaff014: StoreField: r1->field_13 = r0
    //     0xaff014: stur            w0, [x1, #0x13]
    // 0xaff018: r0 = VideoPlayer()
    //     0xaff018: bl              #0xa6fccc  ; AllocateVideoPlayerStub -> VideoPlayer (size=0x10)
    // 0xaff01c: mov             x3, x0
    // 0xaff020: ldur            x0, [fp, #-8]
    // 0xaff024: stur            x3, [fp, #-0x18]
    // 0xaff028: StoreField: r3->field_b = r0
    //     0xaff028: stur            w0, [x3, #0xb]
    // 0xaff02c: r1 = Null
    //     0xaff02c: mov             x1, NULL
    // 0xaff030: r2 = 2
    //     0xaff030: movz            x2, #0x2
    // 0xaff034: r0 = AllocateArray()
    //     0xaff034: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaff038: mov             x2, x0
    // 0xaff03c: ldur            x0, [fp, #-0x18]
    // 0xaff040: stur            x2, [fp, #-8]
    // 0xaff044: StoreField: r2->field_f = r0
    //     0xaff044: stur            w0, [x2, #0xf]
    // 0xaff048: r1 = <Widget>
    //     0xaff048: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaff04c: r0 = AllocateGrowableArray()
    //     0xaff04c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaff050: mov             x2, x0
    // 0xaff054: ldur            x0, [fp, #-8]
    // 0xaff058: stur            x2, [fp, #-0x18]
    // 0xaff05c: StoreField: r2->field_f = r0
    //     0xaff05c: stur            w0, [x2, #0xf]
    // 0xaff060: r0 = 2
    //     0xaff060: movz            x0, #0x2
    // 0xaff064: StoreField: r2->field_b = r0
    //     0xaff064: stur            w0, [x2, #0xb]
    // 0xaff068: ldur            x0, [fp, #-0x10]
    // 0xaff06c: LoadField: r1 = r0->field_b
    //     0xaff06c: ldur            w1, [x0, #0xb]
    // 0xaff070: DecompressPointer r1
    //     0xaff070: add             x1, x1, HEAP, lsl #32
    // 0xaff074: cmp             w1, NULL
    // 0xaff078: b.eq            #0xaff2dc
    // 0xaff07c: LoadField: r0 = r1->field_f
    //     0xaff07c: ldur            w0, [x1, #0xf]
    // 0xaff080: DecompressPointer r0
    //     0xaff080: add             x0, x0, HEAP, lsl #32
    // 0xaff084: cmp             w0, NULL
    // 0xaff088: b.eq            #0xaff090
    // 0xaff08c: tbz             w0, #4, #0xaff18c
    // 0xaff090: r1 = Instance_Color
    //     0xaff090: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaff094: d0 = 0.300000
    //     0xaff094: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xaff098: ldr             d0, [x17, #0x658]
    // 0xaff09c: r0 = withOpacity()
    //     0xaff09c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaff0a0: stur            x0, [fp, #-8]
    // 0xaff0a4: r0 = BoxDecoration()
    //     0xaff0a4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaff0a8: mov             x1, x0
    // 0xaff0ac: ldur            x0, [fp, #-8]
    // 0xaff0b0: stur            x1, [fp, #-0x10]
    // 0xaff0b4: StoreField: r1->field_7 = r0
    //     0xaff0b4: stur            w0, [x1, #7]
    // 0xaff0b8: r0 = Instance_BoxShape
    //     0xaff0b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xaff0bc: ldr             x0, [x0, #0x970]
    // 0xaff0c0: StoreField: r1->field_23 = r0
    //     0xaff0c0: stur            w0, [x1, #0x23]
    // 0xaff0c4: r0 = Container()
    //     0xaff0c4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaff0c8: stur            x0, [fp, #-8]
    // 0xaff0cc: ldur            x16, [fp, #-0x10]
    // 0xaff0d0: r30 = Instance_EdgeInsets
    //     0xaff0d0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0xaff0d4: ldr             lr, [lr, #0xb30]
    // 0xaff0d8: stp             lr, x16, [SP, #8]
    // 0xaff0dc: r16 = Instance_Icon
    //     0xaff0dc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b38] Obj!Icon@d66431
    //     0xaff0e0: ldr             x16, [x16, #0xb38]
    // 0xaff0e4: str             x16, [SP]
    // 0xaff0e8: mov             x1, x0
    // 0xaff0ec: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xaff0ec: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xaff0f0: ldr             x4, [x4, #0xb40]
    // 0xaff0f4: r0 = Container()
    //     0xaff0f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaff0f8: r1 = <StackParentData>
    //     0xaff0f8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xaff0fc: ldr             x1, [x1, #0x8e0]
    // 0xaff100: r0 = Positioned()
    //     0xaff100: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xaff104: mov             x2, x0
    // 0xaff108: ldur            x0, [fp, #-8]
    // 0xaff10c: stur            x2, [fp, #-0x10]
    // 0xaff110: StoreField: r2->field_b = r0
    //     0xaff110: stur            w0, [x2, #0xb]
    // 0xaff114: ldur            x0, [fp, #-0x18]
    // 0xaff118: LoadField: r1 = r0->field_b
    //     0xaff118: ldur            w1, [x0, #0xb]
    // 0xaff11c: LoadField: r3 = r0->field_f
    //     0xaff11c: ldur            w3, [x0, #0xf]
    // 0xaff120: DecompressPointer r3
    //     0xaff120: add             x3, x3, HEAP, lsl #32
    // 0xaff124: LoadField: r4 = r3->field_b
    //     0xaff124: ldur            w4, [x3, #0xb]
    // 0xaff128: r3 = LoadInt32Instr(r1)
    //     0xaff128: sbfx            x3, x1, #1, #0x1f
    // 0xaff12c: stur            x3, [fp, #-0x28]
    // 0xaff130: r1 = LoadInt32Instr(r4)
    //     0xaff130: sbfx            x1, x4, #1, #0x1f
    // 0xaff134: cmp             x3, x1
    // 0xaff138: b.ne            #0xaff144
    // 0xaff13c: mov             x1, x0
    // 0xaff140: r0 = _growToNextCapacity()
    //     0xaff140: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaff144: ldur            x2, [fp, #-0x18]
    // 0xaff148: ldur            x3, [fp, #-0x28]
    // 0xaff14c: add             x0, x3, #1
    // 0xaff150: lsl             x1, x0, #1
    // 0xaff154: StoreField: r2->field_b = r1
    //     0xaff154: stur            w1, [x2, #0xb]
    // 0xaff158: LoadField: r1 = r2->field_f
    //     0xaff158: ldur            w1, [x2, #0xf]
    // 0xaff15c: DecompressPointer r1
    //     0xaff15c: add             x1, x1, HEAP, lsl #32
    // 0xaff160: ldur            x0, [fp, #-0x10]
    // 0xaff164: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaff164: add             x25, x1, x3, lsl #2
    //     0xaff168: add             x25, x25, #0xf
    //     0xaff16c: str             w0, [x25]
    //     0xaff170: tbz             w0, #0, #0xaff18c
    //     0xaff174: ldurb           w16, [x1, #-1]
    //     0xaff178: ldurb           w17, [x0, #-1]
    //     0xaff17c: and             x16, x17, x16, lsr #2
    //     0xaff180: tst             x16, HEAP, lsr #32
    //     0xaff184: b.eq            #0xaff18c
    //     0xaff188: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaff18c: ldur            x0, [fp, #-0x20]
    // 0xaff190: r0 = Stack()
    //     0xaff190: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xaff194: mov             x1, x0
    // 0xaff198: r0 = Instance_Alignment
    //     0xaff198: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaff19c: ldr             x0, [x0, #0xb10]
    // 0xaff1a0: stur            x1, [fp, #-8]
    // 0xaff1a4: StoreField: r1->field_f = r0
    //     0xaff1a4: stur            w0, [x1, #0xf]
    // 0xaff1a8: r0 = Instance_StackFit
    //     0xaff1a8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xaff1ac: ldr             x0, [x0, #0xfa8]
    // 0xaff1b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xaff1b0: stur            w0, [x1, #0x17]
    // 0xaff1b4: r0 = Instance_Clip
    //     0xaff1b4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xaff1b8: ldr             x0, [x0, #0x7e0]
    // 0xaff1bc: StoreField: r1->field_1b = r0
    //     0xaff1bc: stur            w0, [x1, #0x1b]
    // 0xaff1c0: ldur            x0, [fp, #-0x18]
    // 0xaff1c4: StoreField: r1->field_b = r0
    //     0xaff1c4: stur            w0, [x1, #0xb]
    // 0xaff1c8: r0 = ClipRRect()
    //     0xaff1c8: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xaff1cc: mov             x1, x0
    // 0xaff1d0: ldur            x0, [fp, #-0x20]
    // 0xaff1d4: stur            x1, [fp, #-0x10]
    // 0xaff1d8: StoreField: r1->field_f = r0
    //     0xaff1d8: stur            w0, [x1, #0xf]
    // 0xaff1dc: r0 = Instance_Clip
    //     0xaff1dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xaff1e0: ldr             x0, [x0, #0x138]
    // 0xaff1e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xaff1e4: stur            w0, [x1, #0x17]
    // 0xaff1e8: ldur            x0, [fp, #-8]
    // 0xaff1ec: StoreField: r1->field_b = r0
    //     0xaff1ec: stur            w0, [x1, #0xb]
    // 0xaff1f0: r0 = AspectRatio()
    //     0xaff1f0: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xaff1f4: d0 = 1.000000
    //     0xaff1f4: fmov            d0, #1.00000000
    // 0xaff1f8: StoreField: r0->field_f = d0
    //     0xaff1f8: stur            d0, [x0, #0xf]
    // 0xaff1fc: ldur            x1, [fp, #-0x10]
    // 0xaff200: StoreField: r0->field_b = r1
    //     0xaff200: stur            w1, [x0, #0xb]
    // 0xaff204: b               #0xaff2bc
    // 0xaff208: r0 = Instance_Alignment
    //     0xaff208: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaff20c: ldr             x0, [x0, #0xb10]
    // 0xaff210: r0 = of()
    //     0xaff210: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff214: LoadField: r1 = r0->field_5b
    //     0xaff214: ldur            w1, [x0, #0x5b]
    // 0xaff218: DecompressPointer r1
    //     0xaff218: add             x1, x1, HEAP, lsl #32
    // 0xaff21c: r0 = LoadClassIdInstr(r1)
    //     0xaff21c: ldur            x0, [x1, #-1]
    //     0xaff220: ubfx            x0, x0, #0xc, #0x14
    // 0xaff224: d0 = 0.300000
    //     0xaff224: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xaff228: ldr             d0, [x17, #0x658]
    // 0xaff22c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xaff22c: sub             lr, x0, #0xffa
    //     0xaff230: ldr             lr, [x21, lr, lsl #3]
    //     0xaff234: blr             lr
    // 0xaff238: stur            x0, [fp, #-8]
    // 0xaff23c: r0 = CircularProgressIndicator()
    //     0xaff23c: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xaff240: mov             x1, x0
    // 0xaff244: r0 = Instance__ActivityIndicatorType
    //     0xaff244: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xaff248: ldr             x0, [x0, #0x1b0]
    // 0xaff24c: stur            x1, [fp, #-0x10]
    // 0xaff250: StoreField: r1->field_23 = r0
    //     0xaff250: stur            w0, [x1, #0x23]
    // 0xaff254: ldur            x0, [fp, #-8]
    // 0xaff258: StoreField: r1->field_13 = r0
    //     0xaff258: stur            w0, [x1, #0x13]
    // 0xaff25c: r0 = SizedBox()
    //     0xaff25c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaff260: mov             x1, x0
    // 0xaff264: r0 = 30.000000
    //     0xaff264: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xaff268: ldr             x0, [x0, #0x768]
    // 0xaff26c: stur            x1, [fp, #-8]
    // 0xaff270: StoreField: r1->field_f = r0
    //     0xaff270: stur            w0, [x1, #0xf]
    // 0xaff274: StoreField: r1->field_13 = r0
    //     0xaff274: stur            w0, [x1, #0x13]
    // 0xaff278: ldur            x0, [fp, #-0x10]
    // 0xaff27c: StoreField: r1->field_b = r0
    //     0xaff27c: stur            w0, [x1, #0xb]
    // 0xaff280: r0 = Center()
    //     0xaff280: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaff284: mov             x1, x0
    // 0xaff288: r0 = Instance_Alignment
    //     0xaff288: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaff28c: ldr             x0, [x0, #0xb10]
    // 0xaff290: stur            x1, [fp, #-0x10]
    // 0xaff294: StoreField: r1->field_f = r0
    //     0xaff294: stur            w0, [x1, #0xf]
    // 0xaff298: ldur            x0, [fp, #-8]
    // 0xaff29c: StoreField: r1->field_b = r0
    //     0xaff29c: stur            w0, [x1, #0xb]
    // 0xaff2a0: r0 = SizedBox()
    //     0xaff2a0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaff2a4: r1 = 57.000000
    //     0xaff2a4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0xaff2a8: ldr             x1, [x1, #0xb18]
    // 0xaff2ac: StoreField: r0->field_f = r1
    //     0xaff2ac: stur            w1, [x0, #0xf]
    // 0xaff2b0: StoreField: r0->field_13 = r1
    //     0xaff2b0: stur            w1, [x0, #0x13]
    // 0xaff2b4: ldur            x1, [fp, #-0x10]
    // 0xaff2b8: StoreField: r0->field_b = r1
    //     0xaff2b8: stur            w1, [x0, #0xb]
    // 0xaff2bc: LeaveFrame
    //     0xaff2bc: mov             SP, fp
    //     0xaff2c0: ldp             fp, lr, [SP], #0x10
    // 0xaff2c4: ret
    //     0xaff2c4: ret             
    // 0xaff2c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaff2c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaff2cc: b               #0xafefb8
    // 0xaff2d0: r9 = _controller
    //     0xaff2d0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57f60] Field <VideoPlayerWidgetState._controller@1482182322>: late (offset: 0x14)
    //     0xaff2d4: ldr             x9, [x9, #0xf60]
    // 0xaff2d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaff2d8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaff2dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaff2dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87638, size: 0x54
    // 0xc87638: EnterFrame
    //     0xc87638: stp             fp, lr, [SP, #-0x10]!
    //     0xc8763c: mov             fp, SP
    // 0xc87640: CheckStackOverflow
    //     0xc87640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87644: cmp             SP, x16
    //     0xc87648: b.ls            #0xc87678
    // 0xc8764c: LoadField: r0 = r1->field_13
    //     0xc8764c: ldur            w0, [x1, #0x13]
    // 0xc87650: DecompressPointer r0
    //     0xc87650: add             x0, x0, HEAP, lsl #32
    // 0xc87654: r16 = Sentinel
    //     0xc87654: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87658: cmp             w0, w16
    // 0xc8765c: b.eq            #0xc87680
    // 0xc87660: mov             x1, x0
    // 0xc87664: r0 = dispose()
    //     0xc87664: bl              #0xc75c5c  ; [package:video_player/video_player.dart] VideoPlayerController::dispose
    // 0xc87668: r0 = Null
    //     0xc87668: mov             x0, NULL
    // 0xc8766c: LeaveFrame
    //     0xc8766c: mov             SP, fp
    //     0xc87670: ldp             fp, lr, [SP], #0x10
    // 0xc87674: ret
    //     0xc87674: ret             
    // 0xc87678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87678: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8767c: b               #0xc8764c
    // 0xc87680: r9 = _controller
    //     0xc87680: add             x9, PP, #0x57, lsl #12  ; [pp+0x57f60] Field <VideoPlayerWidgetState._controller@1482182322>: late (offset: 0x14)
    //     0xc87684: ldr             x9, [x9, #0xf60]
    // 0xc87688: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87688: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4149, size: 0x14, field offset: 0xc
class VideoPlayerWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7de04, size: 0x2c
    // 0xc7de04: EnterFrame
    //     0xc7de04: stp             fp, lr, [SP, #-0x10]!
    //     0xc7de08: mov             fp, SP
    // 0xc7de0c: mov             x0, x1
    // 0xc7de10: r1 = <VideoPlayerWidget>
    //     0xc7de10: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b98] TypeArguments: <VideoPlayerWidget>
    //     0xc7de14: ldr             x1, [x1, #0xb98]
    // 0xc7de18: r0 = VideoPlayerWidgetState()
    //     0xc7de18: bl              #0xc7de30  ; AllocateVideoPlayerWidgetStateStub -> VideoPlayerWidgetState (size=0x18)
    // 0xc7de1c: r1 = Sentinel
    //     0xc7de1c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7de20: StoreField: r0->field_13 = r1
    //     0xc7de20: stur            w1, [x0, #0x13]
    // 0xc7de24: LeaveFrame
    //     0xc7de24: mov             SP, fp
    //     0xc7de28: ldp             fp, lr, [SP], #0x10
    // 0xc7de2c: ret
    //     0xc7de2c: ret             
  }
}
