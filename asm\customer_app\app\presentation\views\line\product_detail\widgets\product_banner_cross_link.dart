// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart

// class id: 1049556, size: 0x8
class :: {
}

// class id: 3228, size: 0x14, field offset: 0x14
class _ProductBannerCrossLinkState extends State<dynamic> {

  [closure] CachedNetworkImage <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0xbdd668, size: 0x60
    // 0xbdd668: EnterFrame
    //     0xbdd668: stp             fp, lr, [SP, #-0x10]!
    //     0xbdd66c: mov             fp, SP
    // 0xbdd670: AllocStack(0x18)
    //     0xbdd670: sub             SP, SP, #0x18
    // 0xbdd674: CheckStackOverflow
    //     0xbdd674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdd678: cmp             SP, x16
    //     0xbdd67c: b.ls            #0xbdd6c0
    // 0xbdd680: r0 = ImageHeaders.forImages()
    //     0xbdd680: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbdd684: stur            x0, [fp, #-8]
    // 0xbdd688: r0 = CachedNetworkImage()
    //     0xbdd688: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbdd68c: stur            x0, [fp, #-0x10]
    // 0xbdd690: ldur            x16, [fp, #-8]
    // 0xbdd694: str             x16, [SP]
    // 0xbdd698: mov             x1, x0
    // 0xbdd69c: r2 = "https://d1311wbk6unapo.cloudfront.net/NushopWebsiteAsset/image_placeholder_2.png"
    //     0xbdd69c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c58] "https://d1311wbk6unapo.cloudfront.net/NushopWebsiteAsset/image_placeholder_2.png"
    //     0xbdd6a0: ldr             x2, [x2, #0xc58]
    // 0xbdd6a4: r4 = const [0, 0x3, 0x1, 0x2, httpHeaders, 0x2, null]
    //     0xbdd6a4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52c60] List(7) [0, 0x3, 0x1, 0x2, "httpHeaders", 0x2, Null]
    //     0xbdd6a8: ldr             x4, [x4, #0xc60]
    // 0xbdd6ac: r0 = CachedNetworkImage()
    //     0xbdd6ac: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbdd6b0: ldur            x0, [fp, #-0x10]
    // 0xbdd6b4: LeaveFrame
    //     0xbdd6b4: mov             SP, fp
    //     0xbdd6b8: ldp             fp, lr, [SP], #0x10
    // 0xbdd6bc: ret
    //     0xbdd6bc: ret             
    // 0xbdd6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdd6c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdd6c4: b               #0xbdd680
  }
  _ bannerSlider(/* No info */) {
    // ** addr: 0xbdd6c8, size: 0xc84
    // 0xbdd6c8: EnterFrame
    //     0xbdd6c8: stp             fp, lr, [SP, #-0x10]!
    //     0xbdd6cc: mov             fp, SP
    // 0xbdd6d0: AllocStack(0x98)
    //     0xbdd6d0: sub             SP, SP, #0x98
    // 0xbdd6d4: SetupParameters(_ProductBannerCrossLinkState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbdd6d4: stur            x1, [fp, #-8]
    //     0xbdd6d8: stur            x2, [fp, #-0x10]
    //     0xbdd6dc: stur            x3, [fp, #-0x18]
    // 0xbdd6e0: CheckStackOverflow
    //     0xbdd6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdd6e4: cmp             SP, x16
    //     0xbdd6e8: b.ls            #0xbde2e0
    // 0xbdd6ec: r1 = 2
    //     0xbdd6ec: movz            x1, #0x2
    // 0xbdd6f0: r0 = AllocateContext()
    //     0xbdd6f0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdd6f4: mov             x3, x0
    // 0xbdd6f8: ldur            x2, [fp, #-8]
    // 0xbdd6fc: stur            x3, [fp, #-0x20]
    // 0xbdd700: StoreField: r3->field_f = r2
    //     0xbdd700: stur            w2, [x3, #0xf]
    // 0xbdd704: ldur            x4, [fp, #-0x18]
    // 0xbdd708: r0 = BoxInt64Instr(r4)
    //     0xbdd708: sbfiz           x0, x4, #1, #0x1f
    //     0xbdd70c: cmp             x4, x0, asr #1
    //     0xbdd710: b.eq            #0xbdd71c
    //     0xbdd714: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdd718: stur            x4, [x0, #7]
    // 0xbdd71c: StoreField: r3->field_13 = r0
    //     0xbdd71c: stur            w0, [x3, #0x13]
    // 0xbdd720: ldur            x1, [fp, #-0x10]
    // 0xbdd724: r4 = LoadClassIdInstr(r1)
    //     0xbdd724: ldur            x4, [x1, #-1]
    //     0xbdd728: ubfx            x4, x4, #0xc, #0x14
    // 0xbdd72c: stp             x0, x1, [SP]
    // 0xbdd730: mov             x0, x4
    // 0xbdd734: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdd734: sub             lr, x0, #0xb7
    //     0xbdd738: ldr             lr, [x21, lr, lsl #3]
    //     0xbdd73c: blr             lr
    // 0xbdd740: r17 = 299
    //     0xbdd740: movz            x17, #0x12b
    // 0xbdd744: ldr             w1, [x0, x17]
    // 0xbdd748: DecompressPointer r1
    //     0xbdd748: add             x1, x1, HEAP, lsl #32
    // 0xbdd74c: cmp             w1, NULL
    // 0xbdd750: b.ne            #0xbdd780
    // 0xbdd754: ldur            x3, [fp, #-8]
    // 0xbdd758: ldur            x4, [fp, #-0x10]
    // 0xbdd75c: ldur            x0, [fp, #-0x20]
    // 0xbdd760: r2 = 4
    //     0xbdd760: movz            x2, #0x4
    // 0xbdd764: r5 = Instance_Alignment
    //     0xbdd764: add             x5, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbdd768: ldr             x5, [x5, #0xb10]
    // 0xbdd76c: r7 = Instance_Clip
    //     0xbdd76c: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbdd770: ldr             x7, [x7, #0x7e0]
    // 0xbdd774: r6 = Instance_StackFit
    //     0xbdd774: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbdd778: ldr             x6, [x6, #0xfa8]
    // 0xbdd77c: b               #0xbddab4
    // 0xbdd780: tbnz            w1, #4, #0xbdda8c
    // 0xbdd784: ldur            x0, [fp, #-8]
    // 0xbdd788: ldur            x3, [fp, #-0x10]
    // 0xbdd78c: ldur            x2, [fp, #-0x20]
    // 0xbdd790: LoadField: r1 = r0->field_b
    //     0xbdd790: ldur            w1, [x0, #0xb]
    // 0xbdd794: DecompressPointer r1
    //     0xbdd794: add             x1, x1, HEAP, lsl #32
    // 0xbdd798: cmp             w1, NULL
    // 0xbdd79c: b.eq            #0xbde2e8
    // 0xbdd7a0: LoadField: r4 = r1->field_2f
    //     0xbdd7a0: ldur            w4, [x1, #0x2f]
    // 0xbdd7a4: DecompressPointer r4
    //     0xbdd7a4: add             x4, x4, HEAP, lsl #32
    // 0xbdd7a8: stur            x4, [fp, #-0x28]
    // 0xbdd7ac: LoadField: r1 = r0->field_f
    //     0xbdd7ac: ldur            w1, [x0, #0xf]
    // 0xbdd7b0: DecompressPointer r1
    //     0xbdd7b0: add             x1, x1, HEAP, lsl #32
    // 0xbdd7b4: cmp             w1, NULL
    // 0xbdd7b8: b.eq            #0xbde2ec
    // 0xbdd7bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdd7bc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdd7c0: r0 = _of()
    //     0xbdd7c0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbdd7c4: LoadField: r1 = r0->field_7
    //     0xbdd7c4: ldur            w1, [x0, #7]
    // 0xbdd7c8: DecompressPointer r1
    //     0xbdd7c8: add             x1, x1, HEAP, lsl #32
    // 0xbdd7cc: LoadField: d0 = r1->field_7
    //     0xbdd7cc: ldur            d0, [x1, #7]
    // 0xbdd7d0: stur            d0, [fp, #-0x68]
    // 0xbdd7d4: r0 = ImageHeaders.forImages()
    //     0xbdd7d4: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbdd7d8: mov             x1, x0
    // 0xbdd7dc: ldur            x2, [fp, #-0x20]
    // 0xbdd7e0: stur            x1, [fp, #-0x30]
    // 0xbdd7e4: LoadField: r0 = r2->field_13
    //     0xbdd7e4: ldur            w0, [x2, #0x13]
    // 0xbdd7e8: DecompressPointer r0
    //     0xbdd7e8: add             x0, x0, HEAP, lsl #32
    // 0xbdd7ec: ldur            x3, [fp, #-0x10]
    // 0xbdd7f0: r4 = LoadClassIdInstr(r3)
    //     0xbdd7f0: ldur            x4, [x3, #-1]
    //     0xbdd7f4: ubfx            x4, x4, #0xc, #0x14
    // 0xbdd7f8: stp             x0, x3, [SP]
    // 0xbdd7fc: mov             x0, x4
    // 0xbdd800: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdd800: sub             lr, x0, #0xb7
    //     0xbdd804: ldr             lr, [x21, lr, lsl #3]
    //     0xbdd808: blr             lr
    // 0xbdd80c: LoadField: r1 = r0->field_13
    //     0xbdd80c: ldur            w1, [x0, #0x13]
    // 0xbdd810: DecompressPointer r1
    //     0xbdd810: add             x1, x1, HEAP, lsl #32
    // 0xbdd814: cmp             w1, NULL
    // 0xbdd818: b.ne            #0xbdd824
    // 0xbdd81c: r4 = ""
    //     0xbdd81c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdd820: b               #0xbdd828
    // 0xbdd824: mov             x4, x1
    // 0xbdd828: ldur            x3, [fp, #-0x10]
    // 0xbdd82c: ldur            x0, [fp, #-0x20]
    // 0xbdd830: ldur            d0, [fp, #-0x68]
    // 0xbdd834: stur            x4, [fp, #-0x38]
    // 0xbdd838: r1 = Function '<anonymous closure>':.
    //     0xbdd838: add             x1, PP, #0x52, lsl #12  ; [pp+0x52c08] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdd83c: ldr             x1, [x1, #0xc08]
    // 0xbdd840: r2 = Null
    //     0xbdd840: mov             x2, NULL
    // 0xbdd844: r0 = AllocateClosure()
    //     0xbdd844: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdd848: r1 = Function '<anonymous closure>':.
    //     0xbdd848: add             x1, PP, #0x52, lsl #12  ; [pp+0x52c10] AnonymousClosure: (0xbdd668), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xbdd6c8)
    //     0xbdd84c: ldr             x1, [x1, #0xc10]
    // 0xbdd850: r2 = Null
    //     0xbdd850: mov             x2, NULL
    // 0xbdd854: stur            x0, [fp, #-0x40]
    // 0xbdd858: r0 = AllocateClosure()
    //     0xbdd858: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdd85c: stur            x0, [fp, #-0x48]
    // 0xbdd860: r0 = CachedNetworkImage()
    //     0xbdd860: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbdd864: stur            x0, [fp, #-0x50]
    // 0xbdd868: ldur            x16, [fp, #-0x30]
    // 0xbdd86c: r30 = Instance_BoxFit
    //     0xbdd86c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbdd870: ldr             lr, [lr, #0x118]
    // 0xbdd874: stp             lr, x16, [SP, #0x10]
    // 0xbdd878: ldur            x16, [fp, #-0x40]
    // 0xbdd87c: ldur            lr, [fp, #-0x48]
    // 0xbdd880: stp             lr, x16, [SP]
    // 0xbdd884: mov             x1, x0
    // 0xbdd888: ldur            x2, [fp, #-0x38]
    // 0xbdd88c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xbdd88c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xbdd890: ldr             x4, [x4, #0x828]
    // 0xbdd894: r0 = CachedNetworkImage()
    //     0xbdd894: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbdd898: ldur            d0, [fp, #-0x68]
    // 0xbdd89c: r0 = inline_Allocate_Double()
    //     0xbdd89c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbdd8a0: add             x0, x0, #0x10
    //     0xbdd8a4: cmp             x1, x0
    //     0xbdd8a8: b.ls            #0xbde2f0
    //     0xbdd8ac: str             x0, [THR, #0x50]  ; THR::top
    //     0xbdd8b0: sub             x0, x0, #0xf
    //     0xbdd8b4: movz            x1, #0xe15c
    //     0xbdd8b8: movk            x1, #0x3, lsl #16
    //     0xbdd8bc: stur            x1, [x0, #-1]
    // 0xbdd8c0: StoreField: r0->field_7 = d0
    //     0xbdd8c0: stur            d0, [x0, #7]
    // 0xbdd8c4: stur            x0, [fp, #-0x30]
    // 0xbdd8c8: r0 = Container()
    //     0xbdd8c8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdd8cc: stur            x0, [fp, #-0x38]
    // 0xbdd8d0: ldur            x16, [fp, #-0x28]
    // 0xbdd8d4: ldur            lr, [fp, #-0x30]
    // 0xbdd8d8: stp             lr, x16, [SP, #8]
    // 0xbdd8dc: ldur            x16, [fp, #-0x50]
    // 0xbdd8e0: str             x16, [SP]
    // 0xbdd8e4: mov             x1, x0
    // 0xbdd8e8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbdd8e8: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbdd8ec: ldr             x4, [x4, #0x1b8]
    // 0xbdd8f0: r0 = Container()
    //     0xbdd8f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdd8f4: ldur            x2, [fp, #-0x20]
    // 0xbdd8f8: LoadField: r0 = r2->field_13
    //     0xbdd8f8: ldur            w0, [x2, #0x13]
    // 0xbdd8fc: DecompressPointer r0
    //     0xbdd8fc: add             x0, x0, HEAP, lsl #32
    // 0xbdd900: ldur            x1, [fp, #-0x10]
    // 0xbdd904: r3 = LoadClassIdInstr(r1)
    //     0xbdd904: ldur            x3, [x1, #-1]
    //     0xbdd908: ubfx            x3, x3, #0xc, #0x14
    // 0xbdd90c: stp             x0, x1, [SP]
    // 0xbdd910: mov             x0, x3
    // 0xbdd914: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdd914: sub             lr, x0, #0xb7
    //     0xbdd918: ldr             lr, [x21, lr, lsl #3]
    //     0xbdd91c: blr             lr
    // 0xbdd920: LoadField: r1 = r0->field_7
    //     0xbdd920: ldur            w1, [x0, #7]
    // 0xbdd924: DecompressPointer r1
    //     0xbdd924: add             x1, x1, HEAP, lsl #32
    // 0xbdd928: cmp             w1, NULL
    // 0xbdd92c: b.ne            #0xbdd938
    // 0xbdd930: r4 = ""
    //     0xbdd930: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdd934: b               #0xbdd93c
    // 0xbdd938: mov             x4, x1
    // 0xbdd93c: ldur            x3, [fp, #-8]
    // 0xbdd940: ldur            x0, [fp, #-0x10]
    // 0xbdd944: ldur            x2, [fp, #-0x20]
    // 0xbdd948: stur            x4, [fp, #-0x28]
    // 0xbdd94c: LoadField: r1 = r3->field_f
    //     0xbdd94c: ldur            w1, [x3, #0xf]
    // 0xbdd950: DecompressPointer r1
    //     0xbdd950: add             x1, x1, HEAP, lsl #32
    // 0xbdd954: cmp             w1, NULL
    // 0xbdd958: b.eq            #0xbde300
    // 0xbdd95c: r0 = of()
    //     0xbdd95c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdd960: LoadField: r1 = r0->field_87
    //     0xbdd960: ldur            w1, [x0, #0x87]
    // 0xbdd964: DecompressPointer r1
    //     0xbdd964: add             x1, x1, HEAP, lsl #32
    // 0xbdd968: LoadField: r2 = r1->field_7
    //     0xbdd968: ldur            w2, [x1, #7]
    // 0xbdd96c: DecompressPointer r2
    //     0xbdd96c: add             x2, x2, HEAP, lsl #32
    // 0xbdd970: ldur            x0, [fp, #-0x20]
    // 0xbdd974: stur            x2, [fp, #-0x30]
    // 0xbdd978: LoadField: r1 = r0->field_13
    //     0xbdd978: ldur            w1, [x0, #0x13]
    // 0xbdd97c: DecompressPointer r1
    //     0xbdd97c: add             x1, x1, HEAP, lsl #32
    // 0xbdd980: ldur            x4, [fp, #-0x10]
    // 0xbdd984: r0 = LoadClassIdInstr(r4)
    //     0xbdd984: ldur            x0, [x4, #-1]
    //     0xbdd988: ubfx            x0, x0, #0xc, #0x14
    // 0xbdd98c: stp             x1, x4, [SP]
    // 0xbdd990: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdd990: sub             lr, x0, #0xb7
    //     0xbdd994: ldr             lr, [x21, lr, lsl #3]
    //     0xbdd998: blr             lr
    // 0xbdd99c: r17 = 307
    //     0xbdd99c: movz            x17, #0x133
    // 0xbdd9a0: ldr             w1, [x0, x17]
    // 0xbdd9a4: DecompressPointer r1
    //     0xbdd9a4: add             x1, x1, HEAP, lsl #32
    // 0xbdd9a8: cmp             w1, NULL
    // 0xbdd9ac: b.ne            #0xbdd9b8
    // 0xbdd9b0: r0 = Null
    //     0xbdd9b0: mov             x0, NULL
    // 0xbdd9b4: b               #0xbdd9bc
    // 0xbdd9b8: r0 = ColorExtension.toColor()
    //     0xbdd9b8: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0xbdd9bc: cmp             w0, NULL
    // 0xbdd9c0: b.ne            #0xbdd9cc
    // 0xbdd9c4: r1 = Instance_Color
    //     0xbdd9c4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdd9c8: b               #0xbdd9d0
    // 0xbdd9cc: mov             x1, x0
    // 0xbdd9d0: ldur            x2, [fp, #-0x38]
    // 0xbdd9d4: ldur            x0, [fp, #-0x28]
    // 0xbdd9d8: r16 = 16.000000
    //     0xbdd9d8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbdd9dc: ldr             x16, [x16, #0x188]
    // 0xbdd9e0: stp             x16, x1, [SP]
    // 0xbdd9e4: ldur            x1, [fp, #-0x30]
    // 0xbdd9e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbdd9e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbdd9ec: ldr             x4, [x4, #0x9b8]
    // 0xbdd9f0: r0 = copyWith()
    //     0xbdd9f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdd9f4: stur            x0, [fp, #-0x30]
    // 0xbdd9f8: r0 = Text()
    //     0xbdd9f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdd9fc: mov             x3, x0
    // 0xbdda00: ldur            x0, [fp, #-0x28]
    // 0xbdda04: stur            x3, [fp, #-0x40]
    // 0xbdda08: StoreField: r3->field_b = r0
    //     0xbdda08: stur            w0, [x3, #0xb]
    // 0xbdda0c: ldur            x0, [fp, #-0x30]
    // 0xbdda10: StoreField: r3->field_13 = r0
    //     0xbdda10: stur            w0, [x3, #0x13]
    // 0xbdda14: r1 = Null
    //     0xbdda14: mov             x1, NULL
    // 0xbdda18: r2 = 4
    //     0xbdda18: movz            x2, #0x4
    // 0xbdda1c: r0 = AllocateArray()
    //     0xbdda1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdda20: mov             x2, x0
    // 0xbdda24: ldur            x0, [fp, #-0x38]
    // 0xbdda28: stur            x2, [fp, #-0x28]
    // 0xbdda2c: StoreField: r2->field_f = r0
    //     0xbdda2c: stur            w0, [x2, #0xf]
    // 0xbdda30: ldur            x0, [fp, #-0x40]
    // 0xbdda34: StoreField: r2->field_13 = r0
    //     0xbdda34: stur            w0, [x2, #0x13]
    // 0xbdda38: r1 = <Widget>
    //     0xbdda38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdda3c: r0 = AllocateGrowableArray()
    //     0xbdda3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdda40: mov             x1, x0
    // 0xbdda44: ldur            x0, [fp, #-0x28]
    // 0xbdda48: stur            x1, [fp, #-0x30]
    // 0xbdda4c: StoreField: r1->field_f = r0
    //     0xbdda4c: stur            w0, [x1, #0xf]
    // 0xbdda50: r2 = 4
    //     0xbdda50: movz            x2, #0x4
    // 0xbdda54: StoreField: r1->field_b = r2
    //     0xbdda54: stur            w2, [x1, #0xb]
    // 0xbdda58: r0 = Stack()
    //     0xbdda58: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbdda5c: r5 = Instance_Alignment
    //     0xbdda5c: add             x5, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbdda60: ldr             x5, [x5, #0xb10]
    // 0xbdda64: StoreField: r0->field_f = r5
    //     0xbdda64: stur            w5, [x0, #0xf]
    // 0xbdda68: r6 = Instance_StackFit
    //     0xbdda68: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbdda6c: ldr             x6, [x6, #0xfa8]
    // 0xbdda70: ArrayStore: r0[0] = r6  ; List_4
    //     0xbdda70: stur            w6, [x0, #0x17]
    // 0xbdda74: r7 = Instance_Clip
    //     0xbdda74: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbdda78: ldr             x7, [x7, #0x7e0]
    // 0xbdda7c: StoreField: r0->field_1b = r7
    //     0xbdda7c: stur            w7, [x0, #0x1b]
    // 0xbdda80: ldur            x1, [fp, #-0x30]
    // 0xbdda84: StoreField: r0->field_b = r1
    //     0xbdda84: stur            w1, [x0, #0xb]
    // 0xbdda88: b               #0xbde2d4
    // 0xbdda8c: ldur            x3, [fp, #-8]
    // 0xbdda90: ldur            x4, [fp, #-0x10]
    // 0xbdda94: ldur            x0, [fp, #-0x20]
    // 0xbdda98: r2 = 4
    //     0xbdda98: movz            x2, #0x4
    // 0xbdda9c: r5 = Instance_Alignment
    //     0xbdda9c: add             x5, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbddaa0: ldr             x5, [x5, #0xb10]
    // 0xbddaa4: r7 = Instance_Clip
    //     0xbddaa4: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbddaa8: ldr             x7, [x7, #0x7e0]
    // 0xbddaac: r6 = Instance_StackFit
    //     0xbddaac: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbddab0: ldr             x6, [x6, #0xfa8]
    // 0xbddab4: LoadField: r1 = r3->field_b
    //     0xbddab4: ldur            w1, [x3, #0xb]
    // 0xbddab8: DecompressPointer r1
    //     0xbddab8: add             x1, x1, HEAP, lsl #32
    // 0xbddabc: cmp             w1, NULL
    // 0xbddac0: b.eq            #0xbde304
    // 0xbddac4: LoadField: r8 = r1->field_2f
    //     0xbddac4: ldur            w8, [x1, #0x2f]
    // 0xbddac8: DecompressPointer r8
    //     0xbddac8: add             x8, x8, HEAP, lsl #32
    // 0xbddacc: stur            x8, [fp, #-0x28]
    // 0xbddad0: LoadField: r1 = r3->field_f
    //     0xbddad0: ldur            w1, [x3, #0xf]
    // 0xbddad4: DecompressPointer r1
    //     0xbddad4: add             x1, x1, HEAP, lsl #32
    // 0xbddad8: cmp             w1, NULL
    // 0xbddadc: b.eq            #0xbde308
    // 0xbddae0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbddae0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbddae4: r0 = _of()
    //     0xbddae4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbddae8: LoadField: r1 = r0->field_7
    //     0xbddae8: ldur            w1, [x0, #7]
    // 0xbddaec: DecompressPointer r1
    //     0xbddaec: add             x1, x1, HEAP, lsl #32
    // 0xbddaf0: LoadField: d0 = r1->field_7
    //     0xbddaf0: ldur            d0, [x1, #7]
    // 0xbddaf4: stur            d0, [fp, #-0x68]
    // 0xbddaf8: r0 = ImageHeaders.forImages()
    //     0xbddaf8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbddafc: mov             x1, x0
    // 0xbddb00: ldur            x2, [fp, #-0x20]
    // 0xbddb04: stur            x1, [fp, #-0x30]
    // 0xbddb08: LoadField: r0 = r2->field_13
    //     0xbddb08: ldur            w0, [x2, #0x13]
    // 0xbddb0c: DecompressPointer r0
    //     0xbddb0c: add             x0, x0, HEAP, lsl #32
    // 0xbddb10: ldur            x3, [fp, #-0x10]
    // 0xbddb14: r4 = LoadClassIdInstr(r3)
    //     0xbddb14: ldur            x4, [x3, #-1]
    //     0xbddb18: ubfx            x4, x4, #0xc, #0x14
    // 0xbddb1c: stp             x0, x3, [SP]
    // 0xbddb20: mov             x0, x4
    // 0xbddb24: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbddb24: sub             lr, x0, #0xb7
    //     0xbddb28: ldr             lr, [x21, lr, lsl #3]
    //     0xbddb2c: blr             lr
    // 0xbddb30: LoadField: r1 = r0->field_13
    //     0xbddb30: ldur            w1, [x0, #0x13]
    // 0xbddb34: DecompressPointer r1
    //     0xbddb34: add             x1, x1, HEAP, lsl #32
    // 0xbddb38: cmp             w1, NULL
    // 0xbddb3c: b.ne            #0xbddb48
    // 0xbddb40: r4 = ""
    //     0xbddb40: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbddb44: b               #0xbddb4c
    // 0xbddb48: mov             x4, x1
    // 0xbddb4c: ldur            x3, [fp, #-0x10]
    // 0xbddb50: ldur            x0, [fp, #-0x20]
    // 0xbddb54: ldur            d0, [fp, #-0x68]
    // 0xbddb58: stur            x4, [fp, #-0x38]
    // 0xbddb5c: r1 = Function '<anonymous closure>':.
    //     0xbddb5c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52c18] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbddb60: ldr             x1, [x1, #0xc18]
    // 0xbddb64: r2 = Null
    //     0xbddb64: mov             x2, NULL
    // 0xbddb68: r0 = AllocateClosure()
    //     0xbddb68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbddb6c: r1 = Function '<anonymous closure>':.
    //     0xbddb6c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52c20] AnonymousClosure: (0xbdd668), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xbdd6c8)
    //     0xbddb70: ldr             x1, [x1, #0xc20]
    // 0xbddb74: r2 = Null
    //     0xbddb74: mov             x2, NULL
    // 0xbddb78: stur            x0, [fp, #-0x40]
    // 0xbddb7c: r0 = AllocateClosure()
    //     0xbddb7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbddb80: stur            x0, [fp, #-0x48]
    // 0xbddb84: r0 = CachedNetworkImage()
    //     0xbddb84: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbddb88: stur            x0, [fp, #-0x50]
    // 0xbddb8c: ldur            x16, [fp, #-0x30]
    // 0xbddb90: r30 = Instance_BoxFit
    //     0xbddb90: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbddb94: ldr             lr, [lr, #0x118]
    // 0xbddb98: stp             lr, x16, [SP, #0x10]
    // 0xbddb9c: ldur            x16, [fp, #-0x40]
    // 0xbddba0: ldur            lr, [fp, #-0x48]
    // 0xbddba4: stp             lr, x16, [SP]
    // 0xbddba8: mov             x1, x0
    // 0xbddbac: ldur            x2, [fp, #-0x38]
    // 0xbddbb0: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xbddbb0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xbddbb4: ldr             x4, [x4, #0x828]
    // 0xbddbb8: r0 = CachedNetworkImage()
    //     0xbddbb8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbddbbc: ldur            d0, [fp, #-0x68]
    // 0xbddbc0: r0 = inline_Allocate_Double()
    //     0xbddbc0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbddbc4: add             x0, x0, #0x10
    //     0xbddbc8: cmp             x1, x0
    //     0xbddbcc: b.ls            #0xbde30c
    //     0xbddbd0: str             x0, [THR, #0x50]  ; THR::top
    //     0xbddbd4: sub             x0, x0, #0xf
    //     0xbddbd8: movz            x1, #0xe15c
    //     0xbddbdc: movk            x1, #0x3, lsl #16
    //     0xbddbe0: stur            x1, [x0, #-1]
    // 0xbddbe4: StoreField: r0->field_7 = d0
    //     0xbddbe4: stur            d0, [x0, #7]
    // 0xbddbe8: stur            x0, [fp, #-0x30]
    // 0xbddbec: r0 = Container()
    //     0xbddbec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbddbf0: stur            x0, [fp, #-0x38]
    // 0xbddbf4: ldur            x16, [fp, #-0x28]
    // 0xbddbf8: ldur            lr, [fp, #-0x30]
    // 0xbddbfc: stp             lr, x16, [SP, #0x10]
    // 0xbddc00: r16 = Instance_Color
    //     0xbddc00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbddc04: ldr             x16, [x16, #0x90]
    // 0xbddc08: ldur            lr, [fp, #-0x50]
    // 0xbddc0c: stp             lr, x16, [SP]
    // 0xbddc10: mov             x1, x0
    // 0xbddc14: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbddc14: add             x4, PP, #0x52, lsl #12  ; [pp+0x52c28] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbddc18: ldr             x4, [x4, #0xc28]
    // 0xbddc1c: r0 = Container()
    //     0xbddc1c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbddc20: ldur            x2, [fp, #-0x20]
    // 0xbddc24: LoadField: r0 = r2->field_13
    //     0xbddc24: ldur            w0, [x2, #0x13]
    // 0xbddc28: DecompressPointer r0
    //     0xbddc28: add             x0, x0, HEAP, lsl #32
    // 0xbddc2c: ldur            x1, [fp, #-0x10]
    // 0xbddc30: r3 = LoadClassIdInstr(r1)
    //     0xbddc30: ldur            x3, [x1, #-1]
    //     0xbddc34: ubfx            x3, x3, #0xc, #0x14
    // 0xbddc38: stp             x0, x1, [SP]
    // 0xbddc3c: mov             x0, x3
    // 0xbddc40: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbddc40: sub             lr, x0, #0xb7
    //     0xbddc44: ldr             lr, [x21, lr, lsl #3]
    //     0xbddc48: blr             lr
    // 0xbddc4c: LoadField: r1 = r0->field_7
    //     0xbddc4c: ldur            w1, [x0, #7]
    // 0xbddc50: DecompressPointer r1
    //     0xbddc50: add             x1, x1, HEAP, lsl #32
    // 0xbddc54: cmp             w1, NULL
    // 0xbddc58: b.ne            #0xbddc64
    // 0xbddc5c: r0 = Null
    //     0xbddc5c: mov             x0, NULL
    // 0xbddc60: b               #0xbddc7c
    // 0xbddc64: LoadField: r0 = r1->field_7
    //     0xbddc64: ldur            w0, [x1, #7]
    // 0xbddc68: cbnz            w0, #0xbddc74
    // 0xbddc6c: r1 = false
    //     0xbddc6c: add             x1, NULL, #0x30  ; false
    // 0xbddc70: b               #0xbddc78
    // 0xbddc74: r1 = true
    //     0xbddc74: add             x1, NULL, #0x20  ; true
    // 0xbddc78: mov             x0, x1
    // 0xbddc7c: cmp             w0, NULL
    // 0xbddc80: b.ne            #0xbddc8c
    // 0xbddc84: r1 = false
    //     0xbddc84: add             x1, NULL, #0x30  ; false
    // 0xbddc88: b               #0xbddc90
    // 0xbddc8c: mov             x1, x0
    // 0xbddc90: ldur            x0, [fp, #-0x10]
    // 0xbddc94: ldur            x2, [fp, #-0x20]
    // 0xbddc98: stur            x1, [fp, #-0x28]
    // 0xbddc9c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbddc9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbddca0: ldr             x0, [x0, #0x1c80]
    //     0xbddca4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbddca8: cmp             w0, w16
    //     0xbddcac: b.ne            #0xbddcb8
    //     0xbddcb0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbddcb4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbddcb8: r0 = GetNavigation.size()
    //     0xbddcb8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbddcbc: LoadField: d0 = r0->field_7
    //     0xbddcbc: ldur            d0, [x0, #7]
    // 0xbddcc0: d1 = 0.700000
    //     0xbddcc0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbddcc4: ldr             d1, [x17, #0xf48]
    // 0xbddcc8: fmul            d2, d0, d1
    // 0xbddccc: ldur            x2, [fp, #-0x20]
    // 0xbddcd0: stur            d2, [fp, #-0x68]
    // 0xbddcd4: LoadField: r0 = r2->field_13
    //     0xbddcd4: ldur            w0, [x2, #0x13]
    // 0xbddcd8: DecompressPointer r0
    //     0xbddcd8: add             x0, x0, HEAP, lsl #32
    // 0xbddcdc: ldur            x1, [fp, #-0x10]
    // 0xbddce0: r3 = LoadClassIdInstr(r1)
    //     0xbddce0: ldur            x3, [x1, #-1]
    //     0xbddce4: ubfx            x3, x3, #0xc, #0x14
    // 0xbddce8: stp             x0, x1, [SP]
    // 0xbddcec: mov             x0, x3
    // 0xbddcf0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbddcf0: sub             lr, x0, #0xb7
    //     0xbddcf4: ldr             lr, [x21, lr, lsl #3]
    //     0xbddcf8: blr             lr
    // 0xbddcfc: LoadField: r1 = r0->field_7
    //     0xbddcfc: ldur            w1, [x0, #7]
    // 0xbddd00: DecompressPointer r1
    //     0xbddd00: add             x1, x1, HEAP, lsl #32
    // 0xbddd04: cmp             w1, NULL
    // 0xbddd08: b.ne            #0xbddd14
    // 0xbddd0c: r4 = ""
    //     0xbddd0c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbddd10: b               #0xbddd18
    // 0xbddd14: mov             x4, x1
    // 0xbddd18: ldur            x3, [fp, #-8]
    // 0xbddd1c: ldur            x0, [fp, #-0x10]
    // 0xbddd20: ldur            x2, [fp, #-0x20]
    // 0xbddd24: stur            x4, [fp, #-0x30]
    // 0xbddd28: LoadField: r1 = r3->field_f
    //     0xbddd28: ldur            w1, [x3, #0xf]
    // 0xbddd2c: DecompressPointer r1
    //     0xbddd2c: add             x1, x1, HEAP, lsl #32
    // 0xbddd30: cmp             w1, NULL
    // 0xbddd34: b.eq            #0xbde31c
    // 0xbddd38: r0 = of()
    //     0xbddd38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbddd3c: LoadField: r1 = r0->field_87
    //     0xbddd3c: ldur            w1, [x0, #0x87]
    // 0xbddd40: DecompressPointer r1
    //     0xbddd40: add             x1, x1, HEAP, lsl #32
    // 0xbddd44: LoadField: r0 = r1->field_27
    //     0xbddd44: ldur            w0, [x1, #0x27]
    // 0xbddd48: DecompressPointer r0
    //     0xbddd48: add             x0, x0, HEAP, lsl #32
    // 0xbddd4c: r16 = 16.000000
    //     0xbddd4c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbddd50: ldr             x16, [x16, #0x188]
    // 0xbddd54: r30 = Instance_Color
    //     0xbddd54: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbddd58: stp             lr, x16, [SP]
    // 0xbddd5c: mov             x1, x0
    // 0xbddd60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbddd60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbddd64: ldr             x4, [x4, #0xaa0]
    // 0xbddd68: r0 = copyWith()
    //     0xbddd68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbddd6c: stur            x0, [fp, #-0x40]
    // 0xbddd70: r0 = Text()
    //     0xbddd70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbddd74: mov             x1, x0
    // 0xbddd78: ldur            x0, [fp, #-0x30]
    // 0xbddd7c: stur            x1, [fp, #-0x48]
    // 0xbddd80: StoreField: r1->field_b = r0
    //     0xbddd80: stur            w0, [x1, #0xb]
    // 0xbddd84: ldur            x0, [fp, #-0x40]
    // 0xbddd88: StoreField: r1->field_13 = r0
    //     0xbddd88: stur            w0, [x1, #0x13]
    // 0xbddd8c: r2 = 4
    //     0xbddd8c: movz            x2, #0x4
    // 0xbddd90: StoreField: r1->field_37 = r2
    //     0xbddd90: stur            w2, [x1, #0x37]
    // 0xbddd94: r0 = Padding()
    //     0xbddd94: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbddd98: mov             x1, x0
    // 0xbddd9c: r0 = Instance_EdgeInsets
    //     0xbddd9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbddda0: ldr             x0, [x0, #0x668]
    // 0xbddda4: stur            x1, [fp, #-0x30]
    // 0xbddda8: StoreField: r1->field_f = r0
    //     0xbddda8: stur            w0, [x1, #0xf]
    // 0xbdddac: ldur            x0, [fp, #-0x48]
    // 0xbdddb0: StoreField: r1->field_b = r0
    //     0xbdddb0: stur            w0, [x1, #0xb]
    // 0xbdddb4: ldur            x2, [fp, #-0x20]
    // 0xbdddb8: LoadField: r0 = r2->field_13
    //     0xbdddb8: ldur            w0, [x2, #0x13]
    // 0xbdddbc: DecompressPointer r0
    //     0xbdddbc: add             x0, x0, HEAP, lsl #32
    // 0xbdddc0: ldur            x3, [fp, #-0x10]
    // 0xbdddc4: r4 = LoadClassIdInstr(r3)
    //     0xbdddc4: ldur            x4, [x3, #-1]
    //     0xbdddc8: ubfx            x4, x4, #0xc, #0x14
    // 0xbdddcc: stp             x0, x3, [SP]
    // 0xbdddd0: mov             x0, x4
    // 0xbdddd4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdddd4: sub             lr, x0, #0xb7
    //     0xbdddd8: ldr             lr, [x21, lr, lsl #3]
    //     0xbddddc: blr             lr
    // 0xbddde0: LoadField: r1 = r0->field_f
    //     0xbddde0: ldur            w1, [x0, #0xf]
    // 0xbddde4: DecompressPointer r1
    //     0xbddde4: add             x1, x1, HEAP, lsl #32
    // 0xbddde8: cmp             w1, NULL
    // 0xbdddec: b.ne            #0xbdddf8
    // 0xbdddf0: r0 = Null
    //     0xbdddf0: mov             x0, NULL
    // 0xbdddf4: b               #0xbdde10
    // 0xbdddf8: LoadField: r0 = r1->field_7
    //     0xbdddf8: ldur            w0, [x1, #7]
    // 0xbdddfc: cbnz            w0, #0xbdde08
    // 0xbdde00: r1 = false
    //     0xbdde00: add             x1, NULL, #0x30  ; false
    // 0xbdde04: b               #0xbdde0c
    // 0xbdde08: r1 = true
    //     0xbdde08: add             x1, NULL, #0x20  ; true
    // 0xbdde0c: mov             x0, x1
    // 0xbdde10: cmp             w0, NULL
    // 0xbdde14: b.ne            #0xbdde20
    // 0xbdde18: r3 = false
    //     0xbdde18: add             x3, NULL, #0x30  ; false
    // 0xbdde1c: b               #0xbdde24
    // 0xbdde20: mov             x3, x0
    // 0xbdde24: ldur            x1, [fp, #-8]
    // 0xbdde28: ldur            x0, [fp, #-0x10]
    // 0xbdde2c: ldur            x2, [fp, #-0x20]
    // 0xbdde30: stur            x3, [fp, #-0x40]
    // 0xbdde34: r16 = <EdgeInsets>
    //     0xbdde34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbdde38: ldr             x16, [x16, #0xda0]
    // 0xbdde3c: r30 = Instance_EdgeInsets
    //     0xbdde3c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdde40: ldr             lr, [lr, #0x1f0]
    // 0xbdde44: stp             lr, x16, [SP]
    // 0xbdde48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdde48: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdde4c: r0 = all()
    //     0xbdde4c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdde50: mov             x2, x0
    // 0xbdde54: ldur            x0, [fp, #-8]
    // 0xbdde58: stur            x2, [fp, #-0x48]
    // 0xbdde5c: LoadField: r1 = r0->field_f
    //     0xbdde5c: ldur            w1, [x0, #0xf]
    // 0xbdde60: DecompressPointer r1
    //     0xbdde60: add             x1, x1, HEAP, lsl #32
    // 0xbdde64: cmp             w1, NULL
    // 0xbdde68: b.eq            #0xbde320
    // 0xbdde6c: r0 = of()
    //     0xbdde6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdde70: LoadField: r1 = r0->field_5b
    //     0xbdde70: ldur            w1, [x0, #0x5b]
    // 0xbdde74: DecompressPointer r1
    //     0xbdde74: add             x1, x1, HEAP, lsl #32
    // 0xbdde78: r16 = <Color>
    //     0xbdde78: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbdde7c: ldr             x16, [x16, #0xf80]
    // 0xbdde80: stp             x1, x16, [SP]
    // 0xbdde84: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdde84: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdde88: r0 = all()
    //     0xbdde88: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdde8c: stur            x0, [fp, #-0x50]
    // 0xbdde90: r16 = <RoundedRectangleBorder>
    //     0xbdde90: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbdde94: ldr             x16, [x16, #0xf78]
    // 0xbdde98: r30 = Instance_RoundedRectangleBorder
    //     0xbdde98: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbdde9c: ldr             lr, [lr, #0xd68]
    // 0xbddea0: stp             lr, x16, [SP]
    // 0xbddea4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbddea4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbddea8: r0 = all()
    //     0xbddea8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbddeac: stur            x0, [fp, #-0x58]
    // 0xbddeb0: r0 = ButtonStyle()
    //     0xbddeb0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbddeb4: mov             x1, x0
    // 0xbddeb8: ldur            x0, [fp, #-0x50]
    // 0xbddebc: stur            x1, [fp, #-0x60]
    // 0xbddec0: StoreField: r1->field_b = r0
    //     0xbddec0: stur            w0, [x1, #0xb]
    // 0xbddec4: ldur            x0, [fp, #-0x48]
    // 0xbddec8: StoreField: r1->field_23 = r0
    //     0xbddec8: stur            w0, [x1, #0x23]
    // 0xbddecc: ldur            x0, [fp, #-0x58]
    // 0xbdded0: StoreField: r1->field_43 = r0
    //     0xbdded0: stur            w0, [x1, #0x43]
    // 0xbdded4: r0 = TextButtonThemeData()
    //     0xbdded4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbdded8: mov             x1, x0
    // 0xbddedc: ldur            x0, [fp, #-0x60]
    // 0xbddee0: stur            x1, [fp, #-0x48]
    // 0xbddee4: StoreField: r1->field_7 = r0
    //     0xbddee4: stur            w0, [x1, #7]
    // 0xbddee8: ldur            x2, [fp, #-0x20]
    // 0xbddeec: LoadField: r0 = r2->field_13
    //     0xbddeec: ldur            w0, [x2, #0x13]
    // 0xbddef0: DecompressPointer r0
    //     0xbddef0: add             x0, x0, HEAP, lsl #32
    // 0xbddef4: ldur            x3, [fp, #-0x10]
    // 0xbddef8: r4 = LoadClassIdInstr(r3)
    //     0xbddef8: ldur            x4, [x3, #-1]
    //     0xbddefc: ubfx            x4, x4, #0xc, #0x14
    // 0xbddf00: stp             x0, x3, [SP]
    // 0xbddf04: mov             x0, x4
    // 0xbddf08: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbddf08: sub             lr, x0, #0xb7
    //     0xbddf0c: ldr             lr, [x21, lr, lsl #3]
    //     0xbddf10: blr             lr
    // 0xbddf14: LoadField: r1 = r0->field_f
    //     0xbddf14: ldur            w1, [x0, #0xf]
    // 0xbddf18: DecompressPointer r1
    //     0xbddf18: add             x1, x1, HEAP, lsl #32
    // 0xbddf1c: cmp             w1, NULL
    // 0xbddf20: b.ne            #0xbddf2c
    // 0xbddf24: r6 = ""
    //     0xbddf24: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbddf28: b               #0xbddf30
    // 0xbddf2c: mov             x6, x1
    // 0xbddf30: ldur            x1, [fp, #-8]
    // 0xbddf34: ldur            x5, [fp, #-0x38]
    // 0xbddf38: ldur            x4, [fp, #-0x28]
    // 0xbddf3c: ldur            d0, [fp, #-0x68]
    // 0xbddf40: ldur            x3, [fp, #-0x30]
    // 0xbddf44: ldur            x2, [fp, #-0x40]
    // 0xbddf48: ldur            x0, [fp, #-0x48]
    // 0xbddf4c: stur            x6, [fp, #-0x10]
    // 0xbddf50: LoadField: r7 = r1->field_f
    //     0xbddf50: ldur            w7, [x1, #0xf]
    // 0xbddf54: DecompressPointer r7
    //     0xbddf54: add             x7, x7, HEAP, lsl #32
    // 0xbddf58: cmp             w7, NULL
    // 0xbddf5c: b.eq            #0xbde324
    // 0xbddf60: mov             x1, x7
    // 0xbddf64: r0 = of()
    //     0xbddf64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbddf68: LoadField: r1 = r0->field_87
    //     0xbddf68: ldur            w1, [x0, #0x87]
    // 0xbddf6c: DecompressPointer r1
    //     0xbddf6c: add             x1, x1, HEAP, lsl #32
    // 0xbddf70: LoadField: r0 = r1->field_7
    //     0xbddf70: ldur            w0, [x1, #7]
    // 0xbddf74: DecompressPointer r0
    //     0xbddf74: add             x0, x0, HEAP, lsl #32
    // 0xbddf78: r16 = Instance_Color
    //     0xbddf78: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbddf7c: r30 = 14.000000
    //     0xbddf7c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbddf80: ldr             lr, [lr, #0x1d8]
    // 0xbddf84: stp             lr, x16, [SP]
    // 0xbddf88: mov             x1, x0
    // 0xbddf8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbddf8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbddf90: ldr             x4, [x4, #0x9b8]
    // 0xbddf94: r0 = copyWith()
    //     0xbddf94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbddf98: stur            x0, [fp, #-8]
    // 0xbddf9c: r0 = Text()
    //     0xbddf9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbddfa0: mov             x3, x0
    // 0xbddfa4: ldur            x0, [fp, #-0x10]
    // 0xbddfa8: stur            x3, [fp, #-0x50]
    // 0xbddfac: StoreField: r3->field_b = r0
    //     0xbddfac: stur            w0, [x3, #0xb]
    // 0xbddfb0: ldur            x0, [fp, #-8]
    // 0xbddfb4: StoreField: r3->field_13 = r0
    //     0xbddfb4: stur            w0, [x3, #0x13]
    // 0xbddfb8: ldur            x2, [fp, #-0x20]
    // 0xbddfbc: r1 = Function '<anonymous closure>':.
    //     0xbddfbc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52c30] AnonymousClosure: (0xbde34c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xbdd6c8)
    //     0xbddfc0: ldr             x1, [x1, #0xc30]
    // 0xbddfc4: r0 = AllocateClosure()
    //     0xbddfc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbddfc8: stur            x0, [fp, #-8]
    // 0xbddfcc: r0 = TextButton()
    //     0xbddfcc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbddfd0: mov             x1, x0
    // 0xbddfd4: ldur            x0, [fp, #-8]
    // 0xbddfd8: stur            x1, [fp, #-0x10]
    // 0xbddfdc: StoreField: r1->field_b = r0
    //     0xbddfdc: stur            w0, [x1, #0xb]
    // 0xbddfe0: r0 = false
    //     0xbddfe0: add             x0, NULL, #0x30  ; false
    // 0xbddfe4: StoreField: r1->field_27 = r0
    //     0xbddfe4: stur            w0, [x1, #0x27]
    // 0xbddfe8: r2 = true
    //     0xbddfe8: add             x2, NULL, #0x20  ; true
    // 0xbddfec: StoreField: r1->field_2f = r2
    //     0xbddfec: stur            w2, [x1, #0x2f]
    // 0xbddff0: ldur            x2, [fp, #-0x50]
    // 0xbddff4: StoreField: r1->field_37 = r2
    //     0xbddff4: stur            w2, [x1, #0x37]
    // 0xbddff8: r0 = TextButtonTheme()
    //     0xbddff8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbddffc: mov             x1, x0
    // 0xbde000: ldur            x0, [fp, #-0x48]
    // 0xbde004: stur            x1, [fp, #-8]
    // 0xbde008: StoreField: r1->field_f = r0
    //     0xbde008: stur            w0, [x1, #0xf]
    // 0xbde00c: ldur            x0, [fp, #-0x10]
    // 0xbde010: StoreField: r1->field_b = r0
    //     0xbde010: stur            w0, [x1, #0xb]
    // 0xbde014: r0 = Visibility()
    //     0xbde014: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbde018: mov             x1, x0
    // 0xbde01c: ldur            x0, [fp, #-8]
    // 0xbde020: stur            x1, [fp, #-0x10]
    // 0xbde024: StoreField: r1->field_b = r0
    //     0xbde024: stur            w0, [x1, #0xb]
    // 0xbde028: r0 = Instance_SizedBox
    //     0xbde028: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbde02c: StoreField: r1->field_f = r0
    //     0xbde02c: stur            w0, [x1, #0xf]
    // 0xbde030: ldur            x2, [fp, #-0x40]
    // 0xbde034: StoreField: r1->field_13 = r2
    //     0xbde034: stur            w2, [x1, #0x13]
    // 0xbde038: r2 = false
    //     0xbde038: add             x2, NULL, #0x30  ; false
    // 0xbde03c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbde03c: stur            w2, [x1, #0x17]
    // 0xbde040: StoreField: r1->field_1b = r2
    //     0xbde040: stur            w2, [x1, #0x1b]
    // 0xbde044: StoreField: r1->field_1f = r2
    //     0xbde044: stur            w2, [x1, #0x1f]
    // 0xbde048: StoreField: r1->field_23 = r2
    //     0xbde048: stur            w2, [x1, #0x23]
    // 0xbde04c: StoreField: r1->field_27 = r2
    //     0xbde04c: stur            w2, [x1, #0x27]
    // 0xbde050: StoreField: r1->field_2b = r2
    //     0xbde050: stur            w2, [x1, #0x2b]
    // 0xbde054: r0 = Padding()
    //     0xbde054: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbde058: mov             x3, x0
    // 0xbde05c: r0 = Instance_EdgeInsets
    //     0xbde05c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a18] Obj!EdgeInsets@d58b51
    //     0xbde060: ldr             x0, [x0, #0xa18]
    // 0xbde064: stur            x3, [fp, #-8]
    // 0xbde068: StoreField: r3->field_f = r0
    //     0xbde068: stur            w0, [x3, #0xf]
    // 0xbde06c: ldur            x0, [fp, #-0x10]
    // 0xbde070: StoreField: r3->field_b = r0
    //     0xbde070: stur            w0, [x3, #0xb]
    // 0xbde074: r1 = Null
    //     0xbde074: mov             x1, NULL
    // 0xbde078: r2 = 4
    //     0xbde078: movz            x2, #0x4
    // 0xbde07c: r0 = AllocateArray()
    //     0xbde07c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbde080: mov             x2, x0
    // 0xbde084: ldur            x0, [fp, #-0x30]
    // 0xbde088: stur            x2, [fp, #-0x10]
    // 0xbde08c: StoreField: r2->field_f = r0
    //     0xbde08c: stur            w0, [x2, #0xf]
    // 0xbde090: ldur            x0, [fp, #-8]
    // 0xbde094: StoreField: r2->field_13 = r0
    //     0xbde094: stur            w0, [x2, #0x13]
    // 0xbde098: r1 = <Widget>
    //     0xbde098: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbde09c: r0 = AllocateGrowableArray()
    //     0xbde09c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbde0a0: mov             x1, x0
    // 0xbde0a4: ldur            x0, [fp, #-0x10]
    // 0xbde0a8: stur            x1, [fp, #-8]
    // 0xbde0ac: StoreField: r1->field_f = r0
    //     0xbde0ac: stur            w0, [x1, #0xf]
    // 0xbde0b0: r2 = 4
    //     0xbde0b0: movz            x2, #0x4
    // 0xbde0b4: StoreField: r1->field_b = r2
    //     0xbde0b4: stur            w2, [x1, #0xb]
    // 0xbde0b8: r0 = Column()
    //     0xbde0b8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbde0bc: mov             x3, x0
    // 0xbde0c0: r0 = Instance_Axis
    //     0xbde0c0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbde0c4: stur            x3, [fp, #-0x10]
    // 0xbde0c8: StoreField: r3->field_f = r0
    //     0xbde0c8: stur            w0, [x3, #0xf]
    // 0xbde0cc: r0 = Instance_MainAxisAlignment
    //     0xbde0cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbde0d0: ldr             x0, [x0, #0xab0]
    // 0xbde0d4: StoreField: r3->field_13 = r0
    //     0xbde0d4: stur            w0, [x3, #0x13]
    // 0xbde0d8: r0 = Instance_MainAxisSize
    //     0xbde0d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbde0dc: ldr             x0, [x0, #0xa10]
    // 0xbde0e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbde0e0: stur            w0, [x3, #0x17]
    // 0xbde0e4: r0 = Instance_CrossAxisAlignment
    //     0xbde0e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbde0e8: ldr             x0, [x0, #0xa18]
    // 0xbde0ec: StoreField: r3->field_1b = r0
    //     0xbde0ec: stur            w0, [x3, #0x1b]
    // 0xbde0f0: r0 = Instance_VerticalDirection
    //     0xbde0f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbde0f4: ldr             x0, [x0, #0xa20]
    // 0xbde0f8: StoreField: r3->field_23 = r0
    //     0xbde0f8: stur            w0, [x3, #0x23]
    // 0xbde0fc: r0 = Instance_Clip
    //     0xbde0fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbde100: ldr             x0, [x0, #0x38]
    // 0xbde104: StoreField: r3->field_2b = r0
    //     0xbde104: stur            w0, [x3, #0x2b]
    // 0xbde108: StoreField: r3->field_2f = rZR
    //     0xbde108: stur            xzr, [x3, #0x2f]
    // 0xbde10c: ldur            x0, [fp, #-8]
    // 0xbde110: StoreField: r3->field_b = r0
    //     0xbde110: stur            w0, [x3, #0xb]
    // 0xbde114: r1 = Null
    //     0xbde114: mov             x1, NULL
    // 0xbde118: r2 = 2
    //     0xbde118: movz            x2, #0x2
    // 0xbde11c: r0 = AllocateArray()
    //     0xbde11c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbde120: mov             x2, x0
    // 0xbde124: ldur            x0, [fp, #-0x10]
    // 0xbde128: stur            x2, [fp, #-8]
    // 0xbde12c: StoreField: r2->field_f = r0
    //     0xbde12c: stur            w0, [x2, #0xf]
    // 0xbde130: r1 = <Widget>
    //     0xbde130: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbde134: r0 = AllocateGrowableArray()
    //     0xbde134: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbde138: mov             x1, x0
    // 0xbde13c: ldur            x0, [fp, #-8]
    // 0xbde140: stur            x1, [fp, #-0x10]
    // 0xbde144: StoreField: r1->field_f = r0
    //     0xbde144: stur            w0, [x1, #0xf]
    // 0xbde148: r0 = 2
    //     0xbde148: movz            x0, #0x2
    // 0xbde14c: StoreField: r1->field_b = r0
    //     0xbde14c: stur            w0, [x1, #0xb]
    // 0xbde150: r0 = Stack()
    //     0xbde150: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbde154: mov             x1, x0
    // 0xbde158: r0 = Instance_Alignment
    //     0xbde158: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbde15c: ldr             x0, [x0, #0xb10]
    // 0xbde160: stur            x1, [fp, #-0x20]
    // 0xbde164: StoreField: r1->field_f = r0
    //     0xbde164: stur            w0, [x1, #0xf]
    // 0xbde168: r0 = Instance_StackFit
    //     0xbde168: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbde16c: ldr             x0, [x0, #0xfa8]
    // 0xbde170: ArrayStore: r1[0] = r0  ; List_4
    //     0xbde170: stur            w0, [x1, #0x17]
    // 0xbde174: r2 = Instance_Clip
    //     0xbde174: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbde178: ldr             x2, [x2, #0x7e0]
    // 0xbde17c: StoreField: r1->field_1b = r2
    //     0xbde17c: stur            w2, [x1, #0x1b]
    // 0xbde180: ldur            x3, [fp, #-0x10]
    // 0xbde184: StoreField: r1->field_b = r3
    //     0xbde184: stur            w3, [x1, #0xb]
    // 0xbde188: ldur            d0, [fp, #-0x68]
    // 0xbde18c: r3 = inline_Allocate_Double()
    //     0xbde18c: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xbde190: add             x3, x3, #0x10
    //     0xbde194: cmp             x4, x3
    //     0xbde198: b.ls            #0xbde328
    //     0xbde19c: str             x3, [THR, #0x50]  ; THR::top
    //     0xbde1a0: sub             x3, x3, #0xf
    //     0xbde1a4: movz            x4, #0xe15c
    //     0xbde1a8: movk            x4, #0x3, lsl #16
    //     0xbde1ac: stur            x4, [x3, #-1]
    // 0xbde1b0: StoreField: r3->field_7 = d0
    //     0xbde1b0: stur            d0, [x3, #7]
    // 0xbde1b4: stur            x3, [fp, #-8]
    // 0xbde1b8: r0 = Container()
    //     0xbde1b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbde1bc: stur            x0, [fp, #-0x10]
    // 0xbde1c0: r16 = Instance_Alignment
    //     0xbde1c0: add             x16, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xbde1c4: ldr             x16, [x16, #0xcb0]
    // 0xbde1c8: ldur            lr, [fp, #-8]
    // 0xbde1cc: stp             lr, x16, [SP, #0x20]
    // 0xbde1d0: r16 = 90.000000
    //     0xbde1d0: add             x16, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xbde1d4: ldr             x16, [x16, #0xe68]
    // 0xbde1d8: r30 = Instance_Color
    //     0xbde1d8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbde1dc: stp             lr, x16, [SP, #0x10]
    // 0xbde1e0: r16 = Instance_EdgeInsets
    //     0xbde1e0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xbde1e4: ldr             x16, [x16, #0x850]
    // 0xbde1e8: ldur            lr, [fp, #-0x20]
    // 0xbde1ec: stp             lr, x16, [SP]
    // 0xbde1f0: mov             x1, x0
    // 0xbde1f4: r4 = const [0, 0x7, 0x6, 0x1, alignment, 0x1, child, 0x6, color, 0x4, height, 0x3, padding, 0x5, width, 0x2, null]
    //     0xbde1f4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52c38] List(17) [0, 0x7, 0x6, 0x1, "alignment", 0x1, "child", 0x6, "color", 0x4, "height", 0x3, "padding", 0x5, "width", 0x2, Null]
    //     0xbde1f8: ldr             x4, [x4, #0xc38]
    // 0xbde1fc: r0 = Container()
    //     0xbde1fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbde200: r0 = Padding()
    //     0xbde200: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbde204: mov             x1, x0
    // 0xbde208: r0 = Instance_EdgeInsets
    //     0xbde208: add             x0, PP, #0x52, lsl #12  ; [pp+0x52c40] Obj!EdgeInsets@d596f1
    //     0xbde20c: ldr             x0, [x0, #0xc40]
    // 0xbde210: stur            x1, [fp, #-8]
    // 0xbde214: StoreField: r1->field_f = r0
    //     0xbde214: stur            w0, [x1, #0xf]
    // 0xbde218: ldur            x0, [fp, #-0x10]
    // 0xbde21c: StoreField: r1->field_b = r0
    //     0xbde21c: stur            w0, [x1, #0xb]
    // 0xbde220: r0 = Visibility()
    //     0xbde220: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbde224: mov             x3, x0
    // 0xbde228: ldur            x0, [fp, #-8]
    // 0xbde22c: stur            x3, [fp, #-0x10]
    // 0xbde230: StoreField: r3->field_b = r0
    //     0xbde230: stur            w0, [x3, #0xb]
    // 0xbde234: r0 = Instance_SizedBox
    //     0xbde234: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbde238: StoreField: r3->field_f = r0
    //     0xbde238: stur            w0, [x3, #0xf]
    // 0xbde23c: ldur            x0, [fp, #-0x28]
    // 0xbde240: StoreField: r3->field_13 = r0
    //     0xbde240: stur            w0, [x3, #0x13]
    // 0xbde244: r0 = false
    //     0xbde244: add             x0, NULL, #0x30  ; false
    // 0xbde248: ArrayStore: r3[0] = r0  ; List_4
    //     0xbde248: stur            w0, [x3, #0x17]
    // 0xbde24c: StoreField: r3->field_1b = r0
    //     0xbde24c: stur            w0, [x3, #0x1b]
    // 0xbde250: StoreField: r3->field_1f = r0
    //     0xbde250: stur            w0, [x3, #0x1f]
    // 0xbde254: StoreField: r3->field_23 = r0
    //     0xbde254: stur            w0, [x3, #0x23]
    // 0xbde258: StoreField: r3->field_27 = r0
    //     0xbde258: stur            w0, [x3, #0x27]
    // 0xbde25c: StoreField: r3->field_2b = r0
    //     0xbde25c: stur            w0, [x3, #0x2b]
    // 0xbde260: r1 = Null
    //     0xbde260: mov             x1, NULL
    // 0xbde264: r2 = 4
    //     0xbde264: movz            x2, #0x4
    // 0xbde268: r0 = AllocateArray()
    //     0xbde268: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbde26c: mov             x2, x0
    // 0xbde270: ldur            x0, [fp, #-0x38]
    // 0xbde274: stur            x2, [fp, #-8]
    // 0xbde278: StoreField: r2->field_f = r0
    //     0xbde278: stur            w0, [x2, #0xf]
    // 0xbde27c: ldur            x0, [fp, #-0x10]
    // 0xbde280: StoreField: r2->field_13 = r0
    //     0xbde280: stur            w0, [x2, #0x13]
    // 0xbde284: r1 = <Widget>
    //     0xbde284: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbde288: r0 = AllocateGrowableArray()
    //     0xbde288: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbde28c: mov             x1, x0
    // 0xbde290: ldur            x0, [fp, #-8]
    // 0xbde294: stur            x1, [fp, #-0x10]
    // 0xbde298: StoreField: r1->field_f = r0
    //     0xbde298: stur            w0, [x1, #0xf]
    // 0xbde29c: r0 = 4
    //     0xbde29c: movz            x0, #0x4
    // 0xbde2a0: StoreField: r1->field_b = r0
    //     0xbde2a0: stur            w0, [x1, #0xb]
    // 0xbde2a4: r0 = Stack()
    //     0xbde2a4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbde2a8: r1 = Instance_Alignment
    //     0xbde2a8: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xbde2ac: ldr             x1, [x1, #0xcb0]
    // 0xbde2b0: StoreField: r0->field_f = r1
    //     0xbde2b0: stur            w1, [x0, #0xf]
    // 0xbde2b4: r1 = Instance_StackFit
    //     0xbde2b4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbde2b8: ldr             x1, [x1, #0xfa8]
    // 0xbde2bc: ArrayStore: r0[0] = r1  ; List_4
    //     0xbde2bc: stur            w1, [x0, #0x17]
    // 0xbde2c0: r1 = Instance_Clip
    //     0xbde2c0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbde2c4: ldr             x1, [x1, #0x7e0]
    // 0xbde2c8: StoreField: r0->field_1b = r1
    //     0xbde2c8: stur            w1, [x0, #0x1b]
    // 0xbde2cc: ldur            x1, [fp, #-0x10]
    // 0xbde2d0: StoreField: r0->field_b = r1
    //     0xbde2d0: stur            w1, [x0, #0xb]
    // 0xbde2d4: LeaveFrame
    //     0xbde2d4: mov             SP, fp
    //     0xbde2d8: ldp             fp, lr, [SP], #0x10
    // 0xbde2dc: ret
    //     0xbde2dc: ret             
    // 0xbde2e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbde2e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbde2e4: b               #0xbdd6ec
    // 0xbde2e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde2e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde2ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde2ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde2f0: SaveReg d0
    //     0xbde2f0: str             q0, [SP, #-0x10]!
    // 0xbde2f4: r0 = AllocateDouble()
    //     0xbde2f4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbde2f8: RestoreReg d0
    //     0xbde2f8: ldr             q0, [SP], #0x10
    // 0xbde2fc: b               #0xbdd8c0
    // 0xbde300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde304: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde30c: SaveReg d0
    //     0xbde30c: str             q0, [SP, #-0x10]!
    // 0xbde310: r0 = AllocateDouble()
    //     0xbde310: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbde314: RestoreReg d0
    //     0xbde314: ldr             q0, [SP], #0x10
    // 0xbde318: b               #0xbddbe4
    // 0xbde31c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde31c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde320: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde320: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde324: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbde324: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbde328: SaveReg d0
    //     0xbde328: str             q0, [SP, #-0x10]!
    // 0xbde32c: stp             x1, x2, [SP, #-0x10]!
    // 0xbde330: SaveReg r0
    //     0xbde330: str             x0, [SP, #-8]!
    // 0xbde334: r0 = AllocateDouble()
    //     0xbde334: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbde338: mov             x3, x0
    // 0xbde33c: RestoreReg r0
    //     0xbde33c: ldr             x0, [SP], #8
    // 0xbde340: ldp             x1, x2, [SP], #0x10
    // 0xbde344: RestoreReg d0
    //     0xbde344: ldr             q0, [SP], #0x10
    // 0xbde348: b               #0xbde1b0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbde34c, size: 0xe0
    // 0xbde34c: EnterFrame
    //     0xbde34c: stp             fp, lr, [SP, #-0x10]!
    //     0xbde350: mov             fp, SP
    // 0xbde354: AllocStack(0x20)
    //     0xbde354: sub             SP, SP, #0x20
    // 0xbde358: SetupParameters()
    //     0xbde358: ldr             x0, [fp, #0x10]
    //     0xbde35c: ldur            w1, [x0, #0x17]
    //     0xbde360: add             x1, x1, HEAP, lsl #32
    // 0xbde364: CheckStackOverflow
    //     0xbde364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbde368: cmp             SP, x16
    //     0xbde36c: b.ls            #0xbde41c
    // 0xbde370: LoadField: r0 = r1->field_f
    //     0xbde370: ldur            w0, [x1, #0xf]
    // 0xbde374: DecompressPointer r0
    //     0xbde374: add             x0, x0, HEAP, lsl #32
    // 0xbde378: LoadField: r2 = r0->field_b
    //     0xbde378: ldur            w2, [x0, #0xb]
    // 0xbde37c: DecompressPointer r2
    //     0xbde37c: add             x2, x2, HEAP, lsl #32
    // 0xbde380: cmp             w2, NULL
    // 0xbde384: b.eq            #0xbde424
    // 0xbde388: LoadField: r3 = r2->field_b
    //     0xbde388: ldur            w3, [x2, #0xb]
    // 0xbde38c: DecompressPointer r3
    //     0xbde38c: add             x3, x3, HEAP, lsl #32
    // 0xbde390: LoadField: r0 = r1->field_13
    //     0xbde390: ldur            w0, [x1, #0x13]
    // 0xbde394: DecompressPointer r0
    //     0xbde394: add             x0, x0, HEAP, lsl #32
    // 0xbde398: LoadField: r1 = r3->field_b
    //     0xbde398: ldur            w1, [x3, #0xb]
    // 0xbde39c: r4 = LoadInt32Instr(r0)
    //     0xbde39c: sbfx            x4, x0, #1, #0x1f
    //     0xbde3a0: tbz             w0, #0, #0xbde3a8
    //     0xbde3a4: ldur            x4, [x0, #7]
    // 0xbde3a8: r0 = LoadInt32Instr(r1)
    //     0xbde3a8: sbfx            x0, x1, #1, #0x1f
    // 0xbde3ac: mov             x1, x4
    // 0xbde3b0: cmp             x1, x0
    // 0xbde3b4: b.hs            #0xbde428
    // 0xbde3b8: LoadField: r0 = r3->field_f
    //     0xbde3b8: ldur            w0, [x3, #0xf]
    // 0xbde3bc: DecompressPointer r0
    //     0xbde3bc: add             x0, x0, HEAP, lsl #32
    // 0xbde3c0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbde3c0: add             x16, x0, x4, lsl #2
    //     0xbde3c4: ldur            w1, [x16, #0xf]
    // 0xbde3c8: DecompressPointer r1
    //     0xbde3c8: add             x1, x1, HEAP, lsl #32
    // 0xbde3cc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbde3cc: ldur            w0, [x1, #0x17]
    // 0xbde3d0: DecompressPointer r0
    //     0xbde3d0: add             x0, x0, HEAP, lsl #32
    // 0xbde3d4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbde3d4: ldur            w1, [x2, #0x17]
    // 0xbde3d8: DecompressPointer r1
    //     0xbde3d8: add             x1, x1, HEAP, lsl #32
    // 0xbde3dc: LoadField: r3 = r2->field_23
    //     0xbde3dc: ldur            w3, [x2, #0x23]
    // 0xbde3e0: DecompressPointer r3
    //     0xbde3e0: add             x3, x3, HEAP, lsl #32
    // 0xbde3e4: stp             x0, x3, [SP, #0x10]
    // 0xbde3e8: r16 = "product_page"
    //     0xbde3e8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbde3ec: ldr             x16, [x16, #0x480]
    // 0xbde3f0: stp             x1, x16, [SP]
    // 0xbde3f4: r4 = 0
    //     0xbde3f4: movz            x4, #0
    // 0xbde3f8: ldr             x0, [SP, #0x18]
    // 0xbde3fc: r16 = UnlinkedCall_0x613b5c
    //     0xbde3fc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c48] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbde400: add             x16, x16, #0xc48
    // 0xbde404: ldp             x5, lr, [x16]
    // 0xbde408: blr             lr
    // 0xbde40c: r0 = Null
    //     0xbde40c: mov             x0, NULL
    // 0xbde410: LeaveFrame
    //     0xbde410: mov             SP, fp
    //     0xbde414: ldp             fp, lr, [SP], #0x10
    // 0xbde418: ret
    //     0xbde418: ret             
    // 0xbde41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbde41c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbde420: b               #0xbde370
    // 0xbde424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbde428: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc0030c, size: 0x16c
    // 0xc0030c: EnterFrame
    //     0xc0030c: stp             fp, lr, [SP, #-0x10]!
    //     0xc00310: mov             fp, SP
    // 0xc00314: AllocStack(0x40)
    //     0xc00314: sub             SP, SP, #0x40
    // 0xc00318: SetupParameters(_ProductBannerCrossLinkState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc00318: mov             x0, x1
    //     0xc0031c: stur            x1, [fp, #-8]
    //     0xc00320: mov             x1, x2
    //     0xc00324: stur            x2, [fp, #-0x10]
    // 0xc00328: CheckStackOverflow
    //     0xc00328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0032c: cmp             SP, x16
    //     0xc00330: b.ls            #0xc0046c
    // 0xc00334: r1 = 1
    //     0xc00334: movz            x1, #0x1
    // 0xc00338: r0 = AllocateContext()
    //     0xc00338: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0033c: mov             x2, x0
    // 0xc00340: ldur            x0, [fp, #-8]
    // 0xc00344: stur            x2, [fp, #-0x38]
    // 0xc00348: StoreField: r2->field_f = r0
    //     0xc00348: stur            w0, [x2, #0xf]
    // 0xc0034c: LoadField: r1 = r0->field_b
    //     0xc0034c: ldur            w1, [x0, #0xb]
    // 0xc00350: DecompressPointer r1
    //     0xc00350: add             x1, x1, HEAP, lsl #32
    // 0xc00354: cmp             w1, NULL
    // 0xc00358: b.eq            #0xc00474
    // 0xc0035c: LoadField: r0 = r1->field_b
    //     0xc0035c: ldur            w0, [x1, #0xb]
    // 0xc00360: DecompressPointer r0
    //     0xc00360: add             x0, x0, HEAP, lsl #32
    // 0xc00364: stur            x0, [fp, #-0x30]
    // 0xc00368: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xc00368: ldur            w3, [x1, #0x17]
    // 0xc0036c: DecompressPointer r3
    //     0xc0036c: add             x3, x3, HEAP, lsl #32
    // 0xc00370: stur            x3, [fp, #-0x28]
    // 0xc00374: LoadField: r4 = r1->field_13
    //     0xc00374: ldur            w4, [x1, #0x13]
    // 0xc00378: DecompressPointer r4
    //     0xc00378: add             x4, x4, HEAP, lsl #32
    // 0xc0037c: stur            x4, [fp, #-0x20]
    // 0xc00380: LoadField: r5 = r1->field_f
    //     0xc00380: ldur            w5, [x1, #0xf]
    // 0xc00384: DecompressPointer r5
    //     0xc00384: add             x5, x5, HEAP, lsl #32
    // 0xc00388: stur            x5, [fp, #-0x18]
    // 0xc0038c: LoadField: r6 = r1->field_27
    //     0xc0038c: ldur            w6, [x1, #0x27]
    // 0xc00390: DecompressPointer r6
    //     0xc00390: add             x6, x6, HEAP, lsl #32
    // 0xc00394: ldur            x1, [fp, #-0x10]
    // 0xc00398: stur            x6, [fp, #-8]
    // 0xc0039c: r0 = of()
    //     0xc0039c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc003a0: LoadField: r1 = r0->field_5b
    //     0xc003a0: ldur            w1, [x0, #0x5b]
    // 0xc003a4: DecompressPointer r1
    //     0xc003a4: add             x1, x1, HEAP, lsl #32
    // 0xc003a8: stur            x1, [fp, #-0x10]
    // 0xc003ac: r0 = ProductBannerCrossWidget()
    //     0xc003ac: bl              #0xa79dc0  ; AllocateProductBannerCrossWidgetStub -> ProductBannerCrossWidget (size=0x54)
    // 0xc003b0: d0 = 200.000000
    //     0xc003b0: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xc003b4: ldr             d0, [x17, #0x360]
    // 0xc003b8: stur            x0, [fp, #-0x40]
    // 0xc003bc: StoreField: r0->field_b = d0
    //     0xc003bc: stur            d0, [x0, #0xb]
    // 0xc003c0: r1 = true
    //     0xc003c0: add             x1, NULL, #0x20  ; true
    // 0xc003c4: StoreField: r0->field_13 = r1
    //     0xc003c4: stur            w1, [x0, #0x13]
    // 0xc003c8: r1 = Instance_Duration
    //     0xc003c8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0xc003cc: ldr             x1, [x1, #0xbd8]
    // 0xc003d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xc003d0: stur            w1, [x0, #0x17]
    // 0xc003d4: ldur            x1, [fp, #-0x10]
    // 0xc003d8: StoreField: r0->field_1b = r1
    //     0xc003d8: stur            w1, [x0, #0x1b]
    // 0xc003dc: r1 = Instance_Color
    //     0xc003dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xc003e0: ldr             x1, [x1, #0x90]
    // 0xc003e4: StoreField: r0->field_1f = r1
    //     0xc003e4: stur            w1, [x0, #0x1f]
    // 0xc003e8: d0 = 24.000000
    //     0xc003e8: fmov            d0, #24.00000000
    // 0xc003ec: StoreField: r0->field_27 = d0
    //     0xc003ec: stur            d0, [x0, #0x27]
    // 0xc003f0: ldur            x1, [fp, #-0x30]
    // 0xc003f4: StoreField: r0->field_2f = r1
    //     0xc003f4: stur            w1, [x0, #0x2f]
    // 0xc003f8: ldur            x2, [fp, #-0x38]
    // 0xc003fc: r1 = Function '<anonymous closure>':.
    //     0xc003fc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52be0] AnonymousClosure: (0xc004f8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::build (0xc0030c)
    //     0xc00400: ldr             x1, [x1, #0xbe0]
    // 0xc00404: r0 = AllocateClosure()
    //     0xc00404: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00408: mov             x1, x0
    // 0xc0040c: ldur            x0, [fp, #-0x40]
    // 0xc00410: StoreField: r0->field_33 = r1
    //     0xc00410: stur            w1, [x0, #0x33]
    // 0xc00414: r1 = "product_page"
    //     0xc00414: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xc00418: ldr             x1, [x1, #0x480]
    // 0xc0041c: StoreField: r0->field_37 = r1
    //     0xc0041c: stur            w1, [x0, #0x37]
    // 0xc00420: ldur            x2, [fp, #-0x28]
    // 0xc00424: StoreField: r0->field_3b = r2
    //     0xc00424: stur            w2, [x0, #0x3b]
    // 0xc00428: StoreField: r0->field_3f = r1
    //     0xc00428: stur            w1, [x0, #0x3f]
    // 0xc0042c: ldur            x1, [fp, #-0x20]
    // 0xc00430: StoreField: r0->field_43 = r1
    //     0xc00430: stur            w1, [x0, #0x43]
    // 0xc00434: ldur            x1, [fp, #-0x18]
    // 0xc00438: StoreField: r0->field_47 = r1
    //     0xc00438: stur            w1, [x0, #0x47]
    // 0xc0043c: ldur            x1, [fp, #-8]
    // 0xc00440: StoreField: r0->field_4b = r1
    //     0xc00440: stur            w1, [x0, #0x4b]
    // 0xc00444: ldur            x2, [fp, #-0x38]
    // 0xc00448: r1 = Function '<anonymous closure>':.
    //     0xc00448: add             x1, PP, #0x52, lsl #12  ; [pp+0x52be8] AnonymousClosure: (0xc00478), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::build (0xc0030c)
    //     0xc0044c: ldr             x1, [x1, #0xbe8]
    // 0xc00450: r0 = AllocateClosure()
    //     0xc00450: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00454: mov             x1, x0
    // 0xc00458: ldur            x0, [fp, #-0x40]
    // 0xc0045c: StoreField: r0->field_4f = r1
    //     0xc0045c: stur            w1, [x0, #0x4f]
    // 0xc00460: LeaveFrame
    //     0xc00460: mov             SP, fp
    //     0xc00464: ldp             fp, lr, [SP], #0x10
    // 0xc00468: ret
    //     0xc00468: ret             
    // 0xc0046c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0046c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc00470: b               #0xc00334
    // 0xc00474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, dynamic, int) {
    // ** addr: 0xc00478, size: 0x80
    // 0xc00478: EnterFrame
    //     0xc00478: stp             fp, lr, [SP, #-0x10]!
    //     0xc0047c: mov             fp, SP
    // 0xc00480: AllocStack(0x8)
    //     0xc00480: sub             SP, SP, #8
    // 0xc00484: SetupParameters()
    //     0xc00484: ldr             x0, [fp, #0x20]
    //     0xc00488: ldur            w1, [x0, #0x17]
    //     0xc0048c: add             x1, x1, HEAP, lsl #32
    // 0xc00490: CheckStackOverflow
    //     0xc00490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc00494: cmp             SP, x16
    //     0xc00498: b.ls            #0xc004f0
    // 0xc0049c: LoadField: r3 = r1->field_f
    //     0xc0049c: ldur            w3, [x1, #0xf]
    // 0xc004a0: DecompressPointer r3
    //     0xc004a0: add             x3, x3, HEAP, lsl #32
    // 0xc004a4: ldr             x0, [fp, #0x18]
    // 0xc004a8: stur            x3, [fp, #-8]
    // 0xc004ac: r2 = Null
    //     0xc004ac: mov             x2, NULL
    // 0xc004b0: r1 = Null
    //     0xc004b0: mov             x1, NULL
    // 0xc004b4: r8 = List<WidgetEntity>
    //     0xc004b4: add             x8, PP, #0x52, lsl #12  ; [pp+0x52bf0] Type: List<WidgetEntity>
    //     0xc004b8: ldr             x8, [x8, #0xbf0]
    // 0xc004bc: r3 = Null
    //     0xc004bc: add             x3, PP, #0x52, lsl #12  ; [pp+0x52bf8] Null
    //     0xc004c0: ldr             x3, [x3, #0xbf8]
    // 0xc004c4: r0 = List<WidgetEntity>()
    //     0xc004c4: bl              #0xa7aae4  ; IsType_List<WidgetEntity>_Stub
    // 0xc004c8: ldr             x0, [fp, #0x10]
    // 0xc004cc: r3 = LoadInt32Instr(r0)
    //     0xc004cc: sbfx            x3, x0, #1, #0x1f
    //     0xc004d0: tbz             w0, #0, #0xc004d8
    //     0xc004d4: ldur            x3, [x0, #7]
    // 0xc004d8: ldur            x1, [fp, #-8]
    // 0xc004dc: ldr             x2, [fp, #0x18]
    // 0xc004e0: r0 = bannerSlider()
    //     0xc004e0: bl              #0xbdd6c8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider
    // 0xc004e4: LeaveFrame
    //     0xc004e4: mov             SP, fp
    //     0xc004e8: ldp             fp, lr, [SP], #0x10
    // 0xc004ec: ret
    //     0xc004ec: ret             
    // 0xc004f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc004f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc004f4: b               #0xc0049c
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String) {
    // ** addr: 0xc004f8, size: 0xa4
    // 0xc004f8: EnterFrame
    //     0xc004f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc004fc: mov             fp, SP
    // 0xc00500: AllocStack(0x40)
    //     0xc00500: sub             SP, SP, #0x40
    // 0xc00504: SetupParameters()
    //     0xc00504: ldr             x0, [fp, #0x48]
    //     0xc00508: ldur            w1, [x0, #0x17]
    //     0xc0050c: add             x1, x1, HEAP, lsl #32
    // 0xc00510: CheckStackOverflow
    //     0xc00510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc00514: cmp             SP, x16
    //     0xc00518: b.ls            #0xc00590
    // 0xc0051c: LoadField: r0 = r1->field_f
    //     0xc0051c: ldur            w0, [x1, #0xf]
    // 0xc00520: DecompressPointer r0
    //     0xc00520: add             x0, x0, HEAP, lsl #32
    // 0xc00524: LoadField: r1 = r0->field_b
    //     0xc00524: ldur            w1, [x0, #0xb]
    // 0xc00528: DecompressPointer r1
    //     0xc00528: add             x1, x1, HEAP, lsl #32
    // 0xc0052c: cmp             w1, NULL
    // 0xc00530: b.eq            #0xc00598
    // 0xc00534: LoadField: r0 = r1->field_1f
    //     0xc00534: ldur            w0, [x1, #0x1f]
    // 0xc00538: DecompressPointer r0
    //     0xc00538: add             x0, x0, HEAP, lsl #32
    // 0xc0053c: ldr             x16, [fp, #0x40]
    // 0xc00540: stp             x16, x0, [SP, #0x30]
    // 0xc00544: ldr             x16, [fp, #0x38]
    // 0xc00548: ldr             lr, [fp, #0x30]
    // 0xc0054c: stp             lr, x16, [SP, #0x20]
    // 0xc00550: ldr             x16, [fp, #0x28]
    // 0xc00554: ldr             lr, [fp, #0x20]
    // 0xc00558: stp             lr, x16, [SP, #0x10]
    // 0xc0055c: ldr             x16, [fp, #0x18]
    // 0xc00560: ldr             lr, [fp, #0x10]
    // 0xc00564: stp             lr, x16, [SP]
    // 0xc00568: r4 = 0
    //     0xc00568: movz            x4, #0
    // 0xc0056c: ldr             x0, [SP, #0x38]
    // 0xc00570: r16 = UnlinkedCall_0x613b5c
    //     0xc00570: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc00574: add             x16, x16, #0xc70
    // 0xc00578: ldp             x5, lr, [x16]
    // 0xc0057c: blr             lr
    // 0xc00580: r0 = Null
    //     0xc00580: mov             x0, NULL
    // 0xc00584: LeaveFrame
    //     0xc00584: mov             SP, fp
    //     0xc00588: ldp             fp, lr, [SP], #0x10
    // 0xc0058c: ret
    //     0xc0058c: ret             
    // 0xc00590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc00590: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc00594: b               #0xc0051c
    // 0xc00598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00598: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3975, size: 0x34, field offset: 0xc
//   const constructor, 
class ProductBannerCrossLink extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80f20, size: 0x24
    // 0xc80f20: EnterFrame
    //     0xc80f20: stp             fp, lr, [SP, #-0x10]!
    //     0xc80f24: mov             fp, SP
    // 0xc80f28: mov             x0, x1
    // 0xc80f2c: r1 = <ProductBannerCrossLink>
    //     0xc80f2c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48378] TypeArguments: <ProductBannerCrossLink>
    //     0xc80f30: ldr             x1, [x1, #0x378]
    // 0xc80f34: r0 = _ProductBannerCrossLinkState()
    //     0xc80f34: bl              #0xc80f44  ; Allocate_ProductBannerCrossLinkStateStub -> _ProductBannerCrossLinkState (size=0x14)
    // 0xc80f38: LeaveFrame
    //     0xc80f38: mov             SP, fp
    //     0xc80f3c: ldp             fp, lr, [SP], #0x10
    // 0xc80f40: ret
    //     0xc80f40: ret             
  }
}
