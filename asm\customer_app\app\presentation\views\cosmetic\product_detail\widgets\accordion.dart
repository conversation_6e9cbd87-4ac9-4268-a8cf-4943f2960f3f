// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/accordion.dart

// class id: 1049307, size: 0x8
class :: {
}

// class id: 3410, size: 0x18, field offset: 0x14
class _AccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaffca4, size: 0x3c8
    // 0xaffca4: EnterFrame
    //     0xaffca4: stp             fp, lr, [SP, #-0x10]!
    //     0xaffca8: mov             fp, SP
    // 0xaffcac: AllocStack(0x50)
    //     0xaffcac: sub             SP, SP, #0x50
    // 0xaffcb0: SetupParameters(_AccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaffcb0: mov             x0, x1
    //     0xaffcb4: stur            x1, [fp, #-8]
    //     0xaffcb8: mov             x1, x2
    //     0xaffcbc: stur            x2, [fp, #-0x10]
    // 0xaffcc0: CheckStackOverflow
    //     0xaffcc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaffcc4: cmp             SP, x16
    //     0xaffcc8: b.ls            #0xb00044
    // 0xaffccc: r1 = 1
    //     0xaffccc: movz            x1, #0x1
    // 0xaffcd0: r0 = AllocateContext()
    //     0xaffcd0: bl              #0x16f6108  ; AllocateContextStub
    // 0xaffcd4: mov             x2, x0
    // 0xaffcd8: ldur            x0, [fp, #-8]
    // 0xaffcdc: stur            x2, [fp, #-0x28]
    // 0xaffce0: StoreField: r2->field_f = r0
    //     0xaffce0: stur            w0, [x2, #0xf]
    // 0xaffce4: LoadField: r1 = r0->field_b
    //     0xaffce4: ldur            w1, [x0, #0xb]
    // 0xaffce8: DecompressPointer r1
    //     0xaffce8: add             x1, x1, HEAP, lsl #32
    // 0xaffcec: cmp             w1, NULL
    // 0xaffcf0: b.eq            #0xb0004c
    // 0xaffcf4: LoadField: r3 = r1->field_b
    //     0xaffcf4: ldur            w3, [x1, #0xb]
    // 0xaffcf8: DecompressPointer r3
    //     0xaffcf8: add             x3, x3, HEAP, lsl #32
    // 0xaffcfc: stur            x3, [fp, #-0x20]
    // 0xaffd00: LoadField: r4 = r0->field_13
    //     0xaffd00: ldur            w4, [x0, #0x13]
    // 0xaffd04: DecompressPointer r4
    //     0xaffd04: add             x4, x4, HEAP, lsl #32
    // 0xaffd08: tbnz            w4, #4, #0xaffd18
    // 0xaffd0c: r5 = Instance_IconData
    //     0xaffd0c: add             x5, PP, #0x53, lsl #12  ; [pp+0x53440] Obj!IconData@d55621
    //     0xaffd10: ldr             x5, [x5, #0x440]
    // 0xaffd14: b               #0xaffd20
    // 0xaffd18: r5 = Instance_IconData
    //     0xaffd18: add             x5, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xaffd1c: ldr             x5, [x5, #0x448]
    // 0xaffd20: stur            x5, [fp, #-0x18]
    // 0xaffd24: LoadField: d0 = r1->field_1b
    //     0xaffd24: ldur            d0, [x1, #0x1b]
    // 0xaffd28: stur            d0, [fp, #-0x40]
    // 0xaffd2c: tbnz            w4, #4, #0xaffd48
    // 0xaffd30: ldur            x1, [fp, #-0x10]
    // 0xaffd34: r0 = of()
    //     0xaffd34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaffd38: LoadField: r1 = r0->field_5b
    //     0xaffd38: ldur            w1, [x0, #0x5b]
    // 0xaffd3c: DecompressPointer r1
    //     0xaffd3c: add             x1, x1, HEAP, lsl #32
    // 0xaffd40: mov             x3, x1
    // 0xaffd44: b               #0xaffd5c
    // 0xaffd48: ldur            x1, [fp, #-0x10]
    // 0xaffd4c: r0 = of()
    //     0xaffd4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaffd50: LoadField: r1 = r0->field_5b
    //     0xaffd50: ldur            w1, [x0, #0x5b]
    // 0xaffd54: DecompressPointer r1
    //     0xaffd54: add             x1, x1, HEAP, lsl #32
    // 0xaffd58: mov             x3, x1
    // 0xaffd5c: ldur            x0, [fp, #-8]
    // 0xaffd60: ldur            x1, [fp, #-0x20]
    // 0xaffd64: ldur            x2, [fp, #-0x18]
    // 0xaffd68: ldur            d0, [fp, #-0x40]
    // 0xaffd6c: stur            x3, [fp, #-0x10]
    // 0xaffd70: r0 = Icon()
    //     0xaffd70: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xaffd74: mov             x1, x0
    // 0xaffd78: ldur            x0, [fp, #-0x18]
    // 0xaffd7c: stur            x1, [fp, #-0x30]
    // 0xaffd80: StoreField: r1->field_b = r0
    //     0xaffd80: stur            w0, [x1, #0xb]
    // 0xaffd84: ldur            d0, [fp, #-0x40]
    // 0xaffd88: r0 = inline_Allocate_Double()
    //     0xaffd88: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xaffd8c: add             x0, x0, #0x10
    //     0xaffd90: cmp             x2, x0
    //     0xaffd94: b.ls            #0xb00050
    //     0xaffd98: str             x0, [THR, #0x50]  ; THR::top
    //     0xaffd9c: sub             x0, x0, #0xf
    //     0xaffda0: movz            x2, #0xe15c
    //     0xaffda4: movk            x2, #0x3, lsl #16
    //     0xaffda8: stur            x2, [x0, #-1]
    // 0xaffdac: StoreField: r0->field_7 = d0
    //     0xaffdac: stur            d0, [x0, #7]
    // 0xaffdb0: StoreField: r1->field_f = r0
    //     0xaffdb0: stur            w0, [x1, #0xf]
    // 0xaffdb4: ldur            x0, [fp, #-0x10]
    // 0xaffdb8: StoreField: r1->field_23 = r0
    //     0xaffdb8: stur            w0, [x1, #0x23]
    // 0xaffdbc: ldur            x0, [fp, #-8]
    // 0xaffdc0: LoadField: r2 = r0->field_b
    //     0xaffdc0: ldur            w2, [x0, #0xb]
    // 0xaffdc4: DecompressPointer r2
    //     0xaffdc4: add             x2, x2, HEAP, lsl #32
    // 0xaffdc8: stur            x2, [fp, #-0x10]
    // 0xaffdcc: cmp             w2, NULL
    // 0xaffdd0: b.eq            #0xb00068
    // 0xaffdd4: r0 = RichTextIcon()
    //     0xaffdd4: bl              #0xafd6b8  ; AllocateRichTextIconStub -> RichTextIcon (size=0x14)
    // 0xaffdd8: mov             x1, x0
    // 0xaffddc: ldur            x0, [fp, #-0x30]
    // 0xaffde0: stur            x1, [fp, #-0x18]
    // 0xaffde4: StoreField: r1->field_b = r0
    //     0xaffde4: stur            w0, [x1, #0xb]
    // 0xaffde8: r0 = ""
    //     0xaffde8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaffdec: StoreField: r1->field_f = r0
    //     0xaffdec: stur            w0, [x1, #0xf]
    // 0xaffdf0: r0 = ListTile()
    //     0xaffdf0: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xaffdf4: mov             x3, x0
    // 0xaffdf8: ldur            x0, [fp, #-0x20]
    // 0xaffdfc: stur            x3, [fp, #-0x30]
    // 0xaffe00: StoreField: r3->field_f = r0
    //     0xaffe00: stur            w0, [x3, #0xf]
    // 0xaffe04: ldur            x0, [fp, #-0x18]
    // 0xaffe08: ArrayStore: r3[0] = r0  ; List_4
    //     0xaffe08: stur            w0, [x3, #0x17]
    // 0xaffe0c: r0 = Instance_EdgeInsets
    //     0xaffe0c: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xaffe10: StoreField: r3->field_47 = r0
    //     0xaffe10: stur            w0, [x3, #0x47]
    // 0xaffe14: r0 = true
    //     0xaffe14: add             x0, NULL, #0x20  ; true
    // 0xaffe18: StoreField: r3->field_4b = r0
    //     0xaffe18: stur            w0, [x3, #0x4b]
    // 0xaffe1c: ldur            x2, [fp, #-0x28]
    // 0xaffe20: r1 = Function '<anonymous closure>':.
    //     0xaffe20: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ad68] AnonymousClosure: (0xb0006c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/accordion.dart] _AccordionState::build (0xaffca4)
    //     0xaffe24: ldr             x1, [x1, #0xd68]
    // 0xaffe28: r0 = AllocateClosure()
    //     0xaffe28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaffe2c: mov             x1, x0
    // 0xaffe30: ldur            x0, [fp, #-0x30]
    // 0xaffe34: StoreField: r0->field_4f = r1
    //     0xaffe34: stur            w1, [x0, #0x4f]
    // 0xaffe38: r1 = false
    //     0xaffe38: add             x1, NULL, #0x30  ; false
    // 0xaffe3c: StoreField: r0->field_5f = r1
    //     0xaffe3c: stur            w1, [x0, #0x5f]
    // 0xaffe40: StoreField: r0->field_73 = r1
    //     0xaffe40: stur            w1, [x0, #0x73]
    // 0xaffe44: r1 = 0.000000
    //     0xaffe44: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaffe48: StoreField: r0->field_8b = r1
    //     0xaffe48: stur            w1, [x0, #0x8b]
    // 0xaffe4c: r1 = true
    //     0xaffe4c: add             x1, NULL, #0x20  ; true
    // 0xaffe50: StoreField: r0->field_97 = r1
    //     0xaffe50: stur            w1, [x0, #0x97]
    // 0xaffe54: r1 = Null
    //     0xaffe54: mov             x1, NULL
    // 0xaffe58: r2 = 2
    //     0xaffe58: movz            x2, #0x2
    // 0xaffe5c: r0 = AllocateArray()
    //     0xaffe5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaffe60: mov             x2, x0
    // 0xaffe64: ldur            x0, [fp, #-0x30]
    // 0xaffe68: stur            x2, [fp, #-0x18]
    // 0xaffe6c: StoreField: r2->field_f = r0
    //     0xaffe6c: stur            w0, [x2, #0xf]
    // 0xaffe70: r1 = <Widget>
    //     0xaffe70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaffe74: r0 = AllocateGrowableArray()
    //     0xaffe74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaffe78: mov             x1, x0
    // 0xaffe7c: ldur            x0, [fp, #-0x18]
    // 0xaffe80: stur            x1, [fp, #-0x20]
    // 0xaffe84: StoreField: r1->field_f = r0
    //     0xaffe84: stur            w0, [x1, #0xf]
    // 0xaffe88: r0 = 2
    //     0xaffe88: movz            x0, #0x2
    // 0xaffe8c: StoreField: r1->field_b = r0
    //     0xaffe8c: stur            w0, [x1, #0xb]
    // 0xaffe90: ldur            x0, [fp, #-8]
    // 0xaffe94: LoadField: r2 = r0->field_13
    //     0xaffe94: ldur            w2, [x0, #0x13]
    // 0xaffe98: DecompressPointer r2
    //     0xaffe98: add             x2, x2, HEAP, lsl #32
    // 0xaffe9c: tbnz            w2, #4, #0xafff54
    // 0xaffea0: ldur            x0, [fp, #-0x10]
    // 0xaffea4: LoadField: r2 = r0->field_13
    //     0xaffea4: ldur            w2, [x0, #0x13]
    // 0xaffea8: DecompressPointer r2
    //     0xaffea8: add             x2, x2, HEAP, lsl #32
    // 0xaffeac: stur            x2, [fp, #-8]
    // 0xaffeb0: r0 = Container()
    //     0xaffeb0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaffeb4: stur            x0, [fp, #-0x10]
    // 0xaffeb8: r16 = Instance_EdgeInsets
    //     0xaffeb8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xaffebc: ldr             x16, [x16, #0xf30]
    // 0xaffec0: ldur            lr, [fp, #-8]
    // 0xaffec4: stp             lr, x16, [SP]
    // 0xaffec8: mov             x1, x0
    // 0xaffecc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xaffecc: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xaffed0: ldr             x4, [x4, #0x30]
    // 0xaffed4: r0 = Container()
    //     0xaffed4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaffed8: ldur            x0, [fp, #-0x20]
    // 0xaffedc: LoadField: r1 = r0->field_b
    //     0xaffedc: ldur            w1, [x0, #0xb]
    // 0xaffee0: LoadField: r2 = r0->field_f
    //     0xaffee0: ldur            w2, [x0, #0xf]
    // 0xaffee4: DecompressPointer r2
    //     0xaffee4: add             x2, x2, HEAP, lsl #32
    // 0xaffee8: LoadField: r3 = r2->field_b
    //     0xaffee8: ldur            w3, [x2, #0xb]
    // 0xaffeec: r2 = LoadInt32Instr(r1)
    //     0xaffeec: sbfx            x2, x1, #1, #0x1f
    // 0xaffef0: stur            x2, [fp, #-0x38]
    // 0xaffef4: r1 = LoadInt32Instr(r3)
    //     0xaffef4: sbfx            x1, x3, #1, #0x1f
    // 0xaffef8: cmp             x2, x1
    // 0xaffefc: b.ne            #0xafff08
    // 0xafff00: mov             x1, x0
    // 0xafff04: r0 = _growToNextCapacity()
    //     0xafff04: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafff08: ldur            x2, [fp, #-0x20]
    // 0xafff0c: ldur            x3, [fp, #-0x38]
    // 0xafff10: add             x0, x3, #1
    // 0xafff14: lsl             x1, x0, #1
    // 0xafff18: StoreField: r2->field_b = r1
    //     0xafff18: stur            w1, [x2, #0xb]
    // 0xafff1c: LoadField: r1 = r2->field_f
    //     0xafff1c: ldur            w1, [x2, #0xf]
    // 0xafff20: DecompressPointer r1
    //     0xafff20: add             x1, x1, HEAP, lsl #32
    // 0xafff24: ldur            x0, [fp, #-0x10]
    // 0xafff28: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafff28: add             x25, x1, x3, lsl #2
    //     0xafff2c: add             x25, x25, #0xf
    //     0xafff30: str             w0, [x25]
    //     0xafff34: tbz             w0, #0, #0xafff50
    //     0xafff38: ldurb           w16, [x1, #-1]
    //     0xafff3c: ldurb           w17, [x0, #-1]
    //     0xafff40: and             x16, x17, x16, lsr #2
    //     0xafff44: tst             x16, HEAP, lsr #32
    //     0xafff48: b.eq            #0xafff50
    //     0xafff4c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafff50: b               #0xafffe4
    // 0xafff54: mov             x2, x1
    // 0xafff58: r0 = Container()
    //     0xafff58: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafff5c: mov             x1, x0
    // 0xafff60: stur            x0, [fp, #-8]
    // 0xafff64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xafff64: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xafff68: r0 = Container()
    //     0xafff68: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafff6c: ldur            x0, [fp, #-0x20]
    // 0xafff70: LoadField: r1 = r0->field_b
    //     0xafff70: ldur            w1, [x0, #0xb]
    // 0xafff74: LoadField: r2 = r0->field_f
    //     0xafff74: ldur            w2, [x0, #0xf]
    // 0xafff78: DecompressPointer r2
    //     0xafff78: add             x2, x2, HEAP, lsl #32
    // 0xafff7c: LoadField: r3 = r2->field_b
    //     0xafff7c: ldur            w3, [x2, #0xb]
    // 0xafff80: r2 = LoadInt32Instr(r1)
    //     0xafff80: sbfx            x2, x1, #1, #0x1f
    // 0xafff84: stur            x2, [fp, #-0x38]
    // 0xafff88: r1 = LoadInt32Instr(r3)
    //     0xafff88: sbfx            x1, x3, #1, #0x1f
    // 0xafff8c: cmp             x2, x1
    // 0xafff90: b.ne            #0xafff9c
    // 0xafff94: mov             x1, x0
    // 0xafff98: r0 = _growToNextCapacity()
    //     0xafff98: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafff9c: ldur            x2, [fp, #-0x20]
    // 0xafffa0: ldur            x3, [fp, #-0x38]
    // 0xafffa4: add             x0, x3, #1
    // 0xafffa8: lsl             x1, x0, #1
    // 0xafffac: StoreField: r2->field_b = r1
    //     0xafffac: stur            w1, [x2, #0xb]
    // 0xafffb0: LoadField: r1 = r2->field_f
    //     0xafffb0: ldur            w1, [x2, #0xf]
    // 0xafffb4: DecompressPointer r1
    //     0xafffb4: add             x1, x1, HEAP, lsl #32
    // 0xafffb8: ldur            x0, [fp, #-8]
    // 0xafffbc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafffbc: add             x25, x1, x3, lsl #2
    //     0xafffc0: add             x25, x25, #0xf
    //     0xafffc4: str             w0, [x25]
    //     0xafffc8: tbz             w0, #0, #0xafffe4
    //     0xafffcc: ldurb           w16, [x1, #-1]
    //     0xafffd0: ldurb           w17, [x0, #-1]
    //     0xafffd4: and             x16, x17, x16, lsr #2
    //     0xafffd8: tst             x16, HEAP, lsr #32
    //     0xafffdc: b.eq            #0xafffe4
    //     0xafffe0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafffe4: r0 = Column()
    //     0xafffe4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafffe8: r1 = Instance_Axis
    //     0xafffe8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafffec: StoreField: r0->field_f = r1
    //     0xafffec: stur            w1, [x0, #0xf]
    // 0xaffff0: r1 = Instance_MainAxisAlignment
    //     0xaffff0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaffff4: ldr             x1, [x1, #0xa08]
    // 0xaffff8: StoreField: r0->field_13 = r1
    //     0xaffff8: stur            w1, [x0, #0x13]
    // 0xaffffc: r1 = Instance_MainAxisSize
    //     0xaffffc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb00000: ldr             x1, [x1, #0xa10]
    // 0xb00004: ArrayStore: r0[0] = r1  ; List_4
    //     0xb00004: stur            w1, [x0, #0x17]
    // 0xb00008: r1 = Instance_CrossAxisAlignment
    //     0xb00008: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0000c: ldr             x1, [x1, #0xa18]
    // 0xb00010: StoreField: r0->field_1b = r1
    //     0xb00010: stur            w1, [x0, #0x1b]
    // 0xb00014: r1 = Instance_VerticalDirection
    //     0xb00014: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb00018: ldr             x1, [x1, #0xa20]
    // 0xb0001c: StoreField: r0->field_23 = r1
    //     0xb0001c: stur            w1, [x0, #0x23]
    // 0xb00020: r1 = Instance_Clip
    //     0xb00020: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb00024: ldr             x1, [x1, #0x38]
    // 0xb00028: StoreField: r0->field_2b = r1
    //     0xb00028: stur            w1, [x0, #0x2b]
    // 0xb0002c: StoreField: r0->field_2f = rZR
    //     0xb0002c: stur            xzr, [x0, #0x2f]
    // 0xb00030: ldur            x1, [fp, #-0x20]
    // 0xb00034: StoreField: r0->field_b = r1
    //     0xb00034: stur            w1, [x0, #0xb]
    // 0xb00038: LeaveFrame
    //     0xb00038: mov             SP, fp
    //     0xb0003c: ldp             fp, lr, [SP], #0x10
    // 0xb00040: ret
    //     0xb00040: ret             
    // 0xb00044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb00044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb00048: b               #0xaffccc
    // 0xb0004c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0004c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb00050: SaveReg d0
    //     0xb00050: str             q0, [SP, #-0x10]!
    // 0xb00054: SaveReg r1
    //     0xb00054: str             x1, [SP, #-8]!
    // 0xb00058: r0 = AllocateDouble()
    //     0xb00058: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0005c: RestoreReg r1
    //     0xb0005c: ldr             x1, [SP], #8
    // 0xb00060: RestoreReg d0
    //     0xb00060: ldr             q0, [SP], #0x10
    // 0xb00064: b               #0xaffdac
    // 0xb00068: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb00068: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0006c, size: 0xac
    // 0xb0006c: EnterFrame
    //     0xb0006c: stp             fp, lr, [SP, #-0x10]!
    //     0xb00070: mov             fp, SP
    // 0xb00074: AllocStack(0x18)
    //     0xb00074: sub             SP, SP, #0x18
    // 0xb00078: SetupParameters()
    //     0xb00078: ldr             x0, [fp, #0x10]
    //     0xb0007c: ldur            w3, [x0, #0x17]
    //     0xb00080: add             x3, x3, HEAP, lsl #32
    //     0xb00084: stur            x3, [fp, #-0x10]
    // 0xb00088: CheckStackOverflow
    //     0xb00088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0008c: cmp             SP, x16
    //     0xb00090: b.ls            #0xb0010c
    // 0xb00094: LoadField: r0 = r3->field_f
    //     0xb00094: ldur            w0, [x3, #0xf]
    // 0xb00098: DecompressPointer r0
    //     0xb00098: add             x0, x0, HEAP, lsl #32
    // 0xb0009c: mov             x2, x3
    // 0xb000a0: stur            x0, [fp, #-8]
    // 0xb000a4: r1 = Function '<anonymous closure>':.
    //     0xb000a4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ad70] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xb000a8: ldr             x1, [x1, #0xd70]
    // 0xb000ac: r0 = AllocateClosure()
    //     0xb000ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb000b0: ldur            x1, [fp, #-8]
    // 0xb000b4: mov             x2, x0
    // 0xb000b8: r0 = setState()
    //     0xb000b8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb000bc: ldur            x0, [fp, #-0x10]
    // 0xb000c0: LoadField: r1 = r0->field_f
    //     0xb000c0: ldur            w1, [x0, #0xf]
    // 0xb000c4: DecompressPointer r1
    //     0xb000c4: add             x1, x1, HEAP, lsl #32
    // 0xb000c8: LoadField: r0 = r1->field_b
    //     0xb000c8: ldur            w0, [x1, #0xb]
    // 0xb000cc: DecompressPointer r0
    //     0xb000cc: add             x0, x0, HEAP, lsl #32
    // 0xb000d0: cmp             w0, NULL
    // 0xb000d4: b.eq            #0xb00114
    // 0xb000d8: LoadField: r1 = r0->field_3b
    //     0xb000d8: ldur            w1, [x0, #0x3b]
    // 0xb000dc: DecompressPointer r1
    //     0xb000dc: add             x1, x1, HEAP, lsl #32
    // 0xb000e0: cmp             w1, NULL
    // 0xb000e4: b.eq            #0xb000fc
    // 0xb000e8: str             x1, [SP]
    // 0xb000ec: mov             x0, x1
    // 0xb000f0: ClosureCall
    //     0xb000f0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xb000f4: ldur            x2, [x0, #0x1f]
    //     0xb000f8: blr             x2
    // 0xb000fc: r0 = Null
    //     0xb000fc: mov             x0, NULL
    // 0xb00100: LeaveFrame
    //     0xb00100: mov             SP, fp
    //     0xb00104: ldp             fp, lr, [SP], #0x10
    // 0xb00108: ret
    //     0xb00108: ret             
    // 0xb0010c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0010c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb00110: b               #0xb00094
    // 0xb00114: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb00114: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4147, size: 0x40, field offset: 0xc
//   const constructor, 
class Accordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7de6c, size: 0x2c
    // 0xc7de6c: EnterFrame
    //     0xc7de6c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7de70: mov             fp, SP
    // 0xc7de74: mov             x0, x1
    // 0xc7de78: r1 = <Accordion>
    //     0xc7de78: add             x1, PP, #0x49, lsl #12  ; [pp+0x49160] TypeArguments: <Accordion>
    //     0xc7de7c: ldr             x1, [x1, #0x160]
    // 0xc7de80: r0 = _AccordionState()
    //     0xc7de80: bl              #0xc7de98  ; Allocate_AccordionStateStub -> _AccordionState (size=0x18)
    // 0xc7de84: r1 = false
    //     0xc7de84: add             x1, NULL, #0x30  ; false
    // 0xc7de88: StoreField: r0->field_13 = r1
    //     0xc7de88: stur            w1, [x0, #0x13]
    // 0xc7de8c: LeaveFrame
    //     0xc7de8c: mov             SP, fp
    //     0xc7de90: ldp             fp, lr, [SP], #0x10
    // 0xc7de94: ret
    //     0xc7de94: ret             
  }
}
