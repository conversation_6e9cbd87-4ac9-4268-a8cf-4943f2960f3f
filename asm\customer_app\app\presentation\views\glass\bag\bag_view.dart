// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_view.dart

// class id: 1049345, size: 0x8
class :: {
}

// class id: 4581, size: 0x14, field offset: 0x14
//   const constructor, 
class BagView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1358428, size: 0x64
    // 0x1358428: EnterFrame
    //     0x1358428: stp             fp, lr, [SP, #-0x10]!
    //     0x135842c: mov             fp, SP
    // 0x1358430: AllocStack(0x18)
    //     0x1358430: sub             SP, SP, #0x18
    // 0x1358434: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1358434: stur            x1, [fp, #-8]
    //     0x1358438: stur            x2, [fp, #-0x10]
    // 0x135843c: r1 = 2
    //     0x135843c: movz            x1, #0x2
    // 0x1358440: r0 = AllocateContext()
    //     0x1358440: bl              #0x16f6108  ; AllocateContextStub
    // 0x1358444: mov             x1, x0
    // 0x1358448: ldur            x0, [fp, #-8]
    // 0x135844c: stur            x1, [fp, #-0x18]
    // 0x1358450: StoreField: r1->field_f = r0
    //     0x1358450: stur            w0, [x1, #0xf]
    // 0x1358454: ldur            x0, [fp, #-0x10]
    // 0x1358458: StoreField: r1->field_13 = r0
    //     0x1358458: stur            w0, [x1, #0x13]
    // 0x135845c: r0 = Obx()
    //     0x135845c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1358460: ldur            x2, [fp, #-0x18]
    // 0x1358464: r1 = Function '<anonymous closure>':.
    //     0x1358464: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a68] AnonymousClosure: (0x135848c), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::bottomNavigationBar (0x1358428)
    //     0x1358468: ldr             x1, [x1, #0xa68]
    // 0x135846c: stur            x0, [fp, #-8]
    // 0x1358470: r0 = AllocateClosure()
    //     0x1358470: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358474: mov             x1, x0
    // 0x1358478: ldur            x0, [fp, #-8]
    // 0x135847c: StoreField: r0->field_b = r1
    //     0x135847c: stur            w1, [x0, #0xb]
    // 0x1358480: LeaveFrame
    //     0x1358480: mov             SP, fp
    //     0x1358484: ldp             fp, lr, [SP], #0x10
    // 0x1358488: ret
    //     0x1358488: ret             
  }
  [closure] Visibility <anonymous closure>(dynamic) {
    // ** addr: 0x135848c, size: 0x490
    // 0x135848c: EnterFrame
    //     0x135848c: stp             fp, lr, [SP, #-0x10]!
    //     0x1358490: mov             fp, SP
    // 0x1358494: AllocStack(0x68)
    //     0x1358494: sub             SP, SP, #0x68
    // 0x1358498: SetupParameters()
    //     0x1358498: ldr             x0, [fp, #0x10]
    //     0x135849c: ldur            w2, [x0, #0x17]
    //     0x13584a0: add             x2, x2, HEAP, lsl #32
    //     0x13584a4: stur            x2, [fp, #-8]
    // 0x13584a8: CheckStackOverflow
    //     0x13584a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13584ac: cmp             SP, x16
    //     0x13584b0: b.ls            #0x135890c
    // 0x13584b4: LoadField: r1 = r2->field_f
    //     0x13584b4: ldur            w1, [x2, #0xf]
    // 0x13584b8: DecompressPointer r1
    //     0x13584b8: add             x1, x1, HEAP, lsl #32
    // 0x13584bc: r0 = controller()
    //     0x13584bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13584c0: LoadField: r1 = r0->field_5b
    //     0x13584c0: ldur            w1, [x0, #0x5b]
    // 0x13584c4: DecompressPointer r1
    //     0x13584c4: add             x1, x1, HEAP, lsl #32
    // 0x13584c8: r0 = value()
    //     0x13584c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13584cc: LoadField: r1 = r0->field_b
    //     0x13584cc: ldur            w1, [x0, #0xb]
    // 0x13584d0: DecompressPointer r1
    //     0x13584d0: add             x1, x1, HEAP, lsl #32
    // 0x13584d4: cmp             w1, NULL
    // 0x13584d8: b.ne            #0x13584e4
    // 0x13584dc: r0 = Null
    //     0x13584dc: mov             x0, NULL
    // 0x13584e0: b               #0x135852c
    // 0x13584e4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13584e4: ldur            w0, [x1, #0x17]
    // 0x13584e8: DecompressPointer r0
    //     0x13584e8: add             x0, x0, HEAP, lsl #32
    // 0x13584ec: stur            x0, [fp, #-0x10]
    // 0x13584f0: r1 = Function '<anonymous closure>':.
    //     0x13584f0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a70] AnonymousClosure: (0x12c058c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x13584f4: ldr             x1, [x1, #0xa70]
    // 0x13584f8: r2 = Null
    //     0x13584f8: mov             x2, NULL
    // 0x13584fc: r0 = AllocateClosure()
    //     0x13584fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358500: r1 = Function '<anonymous closure>':.
    //     0x1358500: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a78] AnonymousClosure: (0x12c0574), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x1358504: ldr             x1, [x1, #0xa78]
    // 0x1358508: r2 = Null
    //     0x1358508: mov             x2, NULL
    // 0x135850c: stur            x0, [fp, #-0x18]
    // 0x1358510: r0 = AllocateClosure()
    //     0x1358510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358514: str             x0, [SP]
    // 0x1358518: ldur            x1, [fp, #-0x10]
    // 0x135851c: ldur            x2, [fp, #-0x18]
    // 0x1358520: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x1358520: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x1358524: ldr             x4, [x4, #0xb48]
    // 0x1358528: r0 = firstWhere()
    //     0x1358528: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x135852c: cmp             w0, NULL
    // 0x1358530: b.eq            #0x1358598
    // 0x1358534: LoadField: r1 = r0->field_33
    //     0x1358534: ldur            w1, [x0, #0x33]
    // 0x1358538: DecompressPointer r1
    //     0x1358538: add             x1, x1, HEAP, lsl #32
    // 0x135853c: cmp             w1, NULL
    // 0x1358540: b.eq            #0x1358598
    // 0x1358544: ldur            x2, [fp, #-8]
    // 0x1358548: LoadField: r1 = r2->field_f
    //     0x1358548: ldur            w1, [x2, #0xf]
    // 0x135854c: DecompressPointer r1
    //     0x135854c: add             x1, x1, HEAP, lsl #32
    // 0x1358550: r0 = controller()
    //     0x1358550: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358554: LoadField: r1 = r0->field_5b
    //     0x1358554: ldur            w1, [x0, #0x5b]
    // 0x1358558: DecompressPointer r1
    //     0x1358558: add             x1, x1, HEAP, lsl #32
    // 0x135855c: r0 = value()
    //     0x135855c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358560: LoadField: r1 = r0->field_b
    //     0x1358560: ldur            w1, [x0, #0xb]
    // 0x1358564: DecompressPointer r1
    //     0x1358564: add             x1, x1, HEAP, lsl #32
    // 0x1358568: cmp             w1, NULL
    // 0x135856c: b.ne            #0x1358578
    // 0x1358570: r0 = Null
    //     0x1358570: mov             x0, NULL
    // 0x1358574: b               #0x1358580
    // 0x1358578: LoadField: r0 = r1->field_b
    //     0x1358578: ldur            w0, [x1, #0xb]
    // 0x135857c: DecompressPointer r0
    //     0x135857c: add             x0, x0, HEAP, lsl #32
    // 0x1358580: cbnz            w0, #0x135858c
    // 0x1358584: r1 = false
    //     0x1358584: add             x1, NULL, #0x30  ; false
    // 0x1358588: b               #0x1358590
    // 0x135858c: r1 = true
    //     0x135858c: add             x1, NULL, #0x20  ; true
    // 0x1358590: mov             x0, x1
    // 0x1358594: b               #0x135859c
    // 0x1358598: r0 = false
    //     0x1358598: add             x0, NULL, #0x30  ; false
    // 0x135859c: ldur            x2, [fp, #-8]
    // 0x13585a0: stur            x0, [fp, #-0x10]
    // 0x13585a4: LoadField: r1 = r2->field_f
    //     0x13585a4: ldur            w1, [x2, #0xf]
    // 0x13585a8: DecompressPointer r1
    //     0x13585a8: add             x1, x1, HEAP, lsl #32
    // 0x13585ac: r0 = controller()
    //     0x13585ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13585b0: LoadField: r1 = r0->field_5b
    //     0x13585b0: ldur            w1, [x0, #0x5b]
    // 0x13585b4: DecompressPointer r1
    //     0x13585b4: add             x1, x1, HEAP, lsl #32
    // 0x13585b8: r0 = value()
    //     0x13585b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13585bc: LoadField: r1 = r0->field_b
    //     0x13585bc: ldur            w1, [x0, #0xb]
    // 0x13585c0: DecompressPointer r1
    //     0x13585c0: add             x1, x1, HEAP, lsl #32
    // 0x13585c4: cmp             w1, NULL
    // 0x13585c8: b.ne            #0x13585d4
    // 0x13585cc: r0 = Null
    //     0x13585cc: mov             x0, NULL
    // 0x13585d0: b               #0x13585dc
    // 0x13585d4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13585d4: ldur            w0, [x1, #0x17]
    // 0x13585d8: DecompressPointer r0
    //     0x13585d8: add             x0, x0, HEAP, lsl #32
    // 0x13585dc: cmp             w0, NULL
    // 0x13585e0: b.ne            #0x1358624
    // 0x13585e4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x13585e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13585e8: ldr             x0, [x0]
    //     0x13585ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13585f0: cmp             w0, w16
    //     0x13585f4: b.ne            #0x1358600
    //     0x13585f8: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x13585fc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1358600: r1 = <Catalogue>
    //     0x1358600: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x1358604: ldr             x1, [x1, #0x418]
    // 0x1358608: stur            x0, [fp, #-0x18]
    // 0x135860c: r0 = AllocateGrowableArray()
    //     0x135860c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1358610: mov             x1, x0
    // 0x1358614: ldur            x0, [fp, #-0x18]
    // 0x1358618: StoreField: r1->field_f = r0
    //     0x1358618: stur            w0, [x1, #0xf]
    // 0x135861c: StoreField: r1->field_b = rZR
    //     0x135861c: stur            wzr, [x1, #0xb]
    // 0x1358620: mov             x0, x1
    // 0x1358624: LoadField: r3 = r0->field_7
    //     0x1358624: ldur            w3, [x0, #7]
    // 0x1358628: DecompressPointer r3
    //     0x1358628: add             x3, x3, HEAP, lsl #32
    // 0x135862c: stur            x3, [fp, #-0x38]
    // 0x1358630: LoadField: r1 = r0->field_b
    //     0x1358630: ldur            w1, [x0, #0xb]
    // 0x1358634: r4 = LoadInt32Instr(r1)
    //     0x1358634: sbfx            x4, x1, #1, #0x1f
    // 0x1358638: stur            x4, [fp, #-0x30]
    // 0x135863c: LoadField: r5 = r0->field_f
    //     0x135863c: ldur            w5, [x0, #0xf]
    // 0x1358640: DecompressPointer r5
    //     0x1358640: add             x5, x5, HEAP, lsl #32
    // 0x1358644: stur            x5, [fp, #-0x28]
    // 0x1358648: r0 = 0
    //     0x1358648: movz            x0, #0
    // 0x135864c: CheckStackOverflow
    //     0x135864c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1358650: cmp             SP, x16
    //     0x1358654: b.ls            #0x1358914
    // 0x1358658: cmp             x0, x4
    // 0x135865c: b.ge            #0x1358710
    // 0x1358660: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0x1358660: add             x16, x5, x0, lsl #2
    //     0x1358664: ldur            w6, [x16, #0xf]
    // 0x1358668: DecompressPointer r6
    //     0x1358668: add             x6, x6, HEAP, lsl #32
    // 0x135866c: stur            x6, [fp, #-0x18]
    // 0x1358670: add             x7, x0, #1
    // 0x1358674: stur            x7, [fp, #-0x20]
    // 0x1358678: cmp             w6, NULL
    // 0x135867c: b.ne            #0x13586b0
    // 0x1358680: mov             x0, x6
    // 0x1358684: mov             x2, x3
    // 0x1358688: r1 = Null
    //     0x1358688: mov             x1, NULL
    // 0x135868c: cmp             w2, NULL
    // 0x1358690: b.eq            #0x13586b0
    // 0x1358694: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x1358694: ldur            w4, [x2, #0x17]
    // 0x1358698: DecompressPointer r4
    //     0x1358698: add             x4, x4, HEAP, lsl #32
    // 0x135869c: r8 = X0
    //     0x135869c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x13586a0: LoadField: r9 = r4->field_7
    //     0x13586a0: ldur            x9, [x4, #7]
    // 0x13586a4: r3 = Null
    //     0x13586a4: add             x3, PP, #0x41, lsl #12  ; [pp+0x41a80] Null
    //     0x13586a8: ldr             x3, [x3, #0xa80]
    // 0x13586ac: blr             x9
    // 0x13586b0: ldur            x0, [fp, #-0x18]
    // 0x13586b4: LoadField: r1 = r0->field_63
    //     0x13586b4: ldur            w1, [x0, #0x63]
    // 0x13586b8: DecompressPointer r1
    //     0x13586b8: add             x1, x1, HEAP, lsl #32
    // 0x13586bc: cmp             w1, #2
    // 0x13586c0: b.ne            #0x13586fc
    // 0x13586c4: LoadField: r1 = r0->field_5f
    //     0x13586c4: ldur            w1, [x0, #0x5f]
    // 0x13586c8: DecompressPointer r1
    //     0x13586c8: add             x1, x1, HEAP, lsl #32
    // 0x13586cc: cmp             w1, NULL
    // 0x13586d0: b.ne            #0x13586dc
    // 0x13586d4: r0 = 0
    //     0x13586d4: movz            x0, #0
    // 0x13586d8: b               #0x13586ec
    // 0x13586dc: r2 = LoadInt32Instr(r1)
    //     0x13586dc: sbfx            x2, x1, #1, #0x1f
    //     0x13586e0: tbz             w1, #0, #0x13586e8
    //     0x13586e4: ldur            x2, [x1, #7]
    // 0x13586e8: mov             x0, x2
    // 0x13586ec: cmp             x0, #1
    // 0x13586f0: b.le            #0x13586fc
    // 0x13586f4: r0 = true
    //     0x13586f4: add             x0, NULL, #0x20  ; true
    // 0x13586f8: b               #0x1358714
    // 0x13586fc: ldur            x0, [fp, #-0x20]
    // 0x1358700: ldur            x3, [fp, #-0x38]
    // 0x1358704: ldur            x5, [fp, #-0x28]
    // 0x1358708: ldur            x4, [fp, #-0x30]
    // 0x135870c: b               #0x135864c
    // 0x1358710: r0 = false
    //     0x1358710: add             x0, NULL, #0x30  ; false
    // 0x1358714: ldur            x2, [fp, #-8]
    // 0x1358718: stur            x0, [fp, #-0x18]
    // 0x135871c: LoadField: r1 = r2->field_f
    //     0x135871c: ldur            w1, [x2, #0xf]
    // 0x1358720: DecompressPointer r1
    //     0x1358720: add             x1, x1, HEAP, lsl #32
    // 0x1358724: r0 = controller()
    //     0x1358724: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358728: LoadField: r1 = r0->field_5b
    //     0x1358728: ldur            w1, [x0, #0x5b]
    // 0x135872c: DecompressPointer r1
    //     0x135872c: add             x1, x1, HEAP, lsl #32
    // 0x1358730: r0 = value()
    //     0x1358730: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358734: LoadField: r1 = r0->field_b
    //     0x1358734: ldur            w1, [x0, #0xb]
    // 0x1358738: DecompressPointer r1
    //     0x1358738: add             x1, x1, HEAP, lsl #32
    // 0x135873c: cmp             w1, NULL
    // 0x1358740: b.ne            #0x135874c
    // 0x1358744: r0 = Null
    //     0x1358744: mov             x0, NULL
    // 0x1358748: b               #0x1358768
    // 0x135874c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x135874c: ldur            w0, [x1, #0x17]
    // 0x1358750: DecompressPointer r0
    //     0x1358750: add             x0, x0, HEAP, lsl #32
    // 0x1358754: LoadField: r1 = r0->field_b
    //     0x1358754: ldur            w1, [x0, #0xb]
    // 0x1358758: cbnz            w1, #0x1358764
    // 0x135875c: r0 = false
    //     0x135875c: add             x0, NULL, #0x30  ; false
    // 0x1358760: b               #0x1358768
    // 0x1358764: r0 = true
    //     0x1358764: add             x0, NULL, #0x20  ; true
    // 0x1358768: cmp             w0, NULL
    // 0x135876c: b.ne            #0x1358774
    // 0x1358770: r0 = false
    //     0x1358770: add             x0, NULL, #0x30  ; false
    // 0x1358774: ldur            x2, [fp, #-8]
    // 0x1358778: stur            x0, [fp, #-0x28]
    // 0x135877c: LoadField: r1 = r2->field_f
    //     0x135877c: ldur            w1, [x2, #0xf]
    // 0x1358780: DecompressPointer r1
    //     0x1358780: add             x1, x1, HEAP, lsl #32
    // 0x1358784: r0 = controller()
    //     0x1358784: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358788: LoadField: r1 = r0->field_5b
    //     0x1358788: ldur            w1, [x0, #0x5b]
    // 0x135878c: DecompressPointer r1
    //     0x135878c: add             x1, x1, HEAP, lsl #32
    // 0x1358790: r0 = value()
    //     0x1358790: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358794: LoadField: r2 = r0->field_b
    //     0x1358794: ldur            w2, [x0, #0xb]
    // 0x1358798: DecompressPointer r2
    //     0x1358798: add             x2, x2, HEAP, lsl #32
    // 0x135879c: ldur            x0, [fp, #-8]
    // 0x13587a0: stur            x2, [fp, #-0x38]
    // 0x13587a4: LoadField: r1 = r0->field_f
    //     0x13587a4: ldur            w1, [x0, #0xf]
    // 0x13587a8: DecompressPointer r1
    //     0x13587a8: add             x1, x1, HEAP, lsl #32
    // 0x13587ac: r0 = controller()
    //     0x13587ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13587b0: LoadField: r1 = r0->field_63
    //     0x13587b0: ldur            w1, [x0, #0x63]
    // 0x13587b4: DecompressPointer r1
    //     0x13587b4: add             x1, x1, HEAP, lsl #32
    // 0x13587b8: r0 = value()
    //     0x13587b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13587bc: LoadField: r2 = r0->field_b
    //     0x13587bc: ldur            w2, [x0, #0xb]
    // 0x13587c0: DecompressPointer r2
    //     0x13587c0: add             x2, x2, HEAP, lsl #32
    // 0x13587c4: ldur            x0, [fp, #-8]
    // 0x13587c8: stur            x2, [fp, #-0x40]
    // 0x13587cc: LoadField: r1 = r0->field_f
    //     0x13587cc: ldur            w1, [x0, #0xf]
    // 0x13587d0: DecompressPointer r1
    //     0x13587d0: add             x1, x1, HEAP, lsl #32
    // 0x13587d4: r0 = controller()
    //     0x13587d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13587d8: LoadField: r1 = r0->field_83
    //     0x13587d8: ldur            w1, [x0, #0x83]
    // 0x13587dc: DecompressPointer r1
    //     0x13587dc: add             x1, x1, HEAP, lsl #32
    // 0x13587e0: cmp             w1, NULL
    // 0x13587e4: b.ne            #0x13587f0
    // 0x13587e8: r7 = ""
    //     0x13587e8: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13587ec: b               #0x13587f4
    // 0x13587f0: mov             x7, x1
    // 0x13587f4: ldur            x2, [fp, #-8]
    // 0x13587f8: ldur            x6, [fp, #-0x10]
    // 0x13587fc: ldur            x5, [fp, #-0x18]
    // 0x1358800: ldur            x4, [fp, #-0x28]
    // 0x1358804: ldur            x3, [fp, #-0x38]
    // 0x1358808: ldur            x0, [fp, #-0x40]
    // 0x135880c: stur            x7, [fp, #-0x48]
    // 0x1358810: LoadField: r1 = r2->field_f
    //     0x1358810: ldur            w1, [x2, #0xf]
    // 0x1358814: DecompressPointer r1
    //     0x1358814: add             x1, x1, HEAP, lsl #32
    // 0x1358818: r0 = controller()
    //     0x1358818: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135881c: LoadField: r1 = r0->field_7f
    //     0x135881c: ldur            w1, [x0, #0x7f]
    // 0x1358820: DecompressPointer r1
    //     0x1358820: add             x1, x1, HEAP, lsl #32
    // 0x1358824: r0 = value()
    //     0x1358824: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358828: ldur            x2, [fp, #-8]
    // 0x135882c: stur            x0, [fp, #-0x50]
    // 0x1358830: LoadField: r1 = r2->field_f
    //     0x1358830: ldur            w1, [x2, #0xf]
    // 0x1358834: DecompressPointer r1
    //     0x1358834: add             x1, x1, HEAP, lsl #32
    // 0x1358838: r0 = controller()
    //     0x1358838: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135883c: LoadField: r1 = r0->field_77
    //     0x135883c: ldur            w1, [x0, #0x77]
    // 0x1358840: DecompressPointer r1
    //     0x1358840: add             x1, x1, HEAP, lsl #32
    // 0x1358844: r0 = value()
    //     0x1358844: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358848: stur            x0, [fp, #-0x58]
    // 0x135884c: r0 = BottomView()
    //     0x135884c: bl              #0x135891c  ; AllocateBottomViewStub -> BottomView (size=0x30)
    // 0x1358850: mov             x3, x0
    // 0x1358854: ldur            x0, [fp, #-0x38]
    // 0x1358858: stur            x3, [fp, #-0x60]
    // 0x135885c: StoreField: r3->field_b = r0
    //     0x135885c: stur            w0, [x3, #0xb]
    // 0x1358860: ldur            x0, [fp, #-0x10]
    // 0x1358864: StoreField: r3->field_f = r0
    //     0x1358864: stur            w0, [x3, #0xf]
    // 0x1358868: ldur            x0, [fp, #-0x18]
    // 0x135886c: StoreField: r3->field_13 = r0
    //     0x135886c: stur            w0, [x3, #0x13]
    // 0x1358870: ldur            x0, [fp, #-0x40]
    // 0x1358874: ArrayStore: r3[0] = r0  ; List_4
    //     0x1358874: stur            w0, [x3, #0x17]
    // 0x1358878: ldur            x2, [fp, #-8]
    // 0x135887c: r1 = Function '<anonymous closure>':.
    //     0x135887c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a90] AnonymousClosure: (0x12c00ec), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x1358880: ldr             x1, [x1, #0xa90]
    // 0x1358884: r0 = AllocateClosure()
    //     0x1358884: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358888: mov             x1, x0
    // 0x135888c: ldur            x0, [fp, #-0x60]
    // 0x1358890: StoreField: r0->field_1b = r1
    //     0x1358890: stur            w1, [x0, #0x1b]
    // 0x1358894: ldur            x1, [fp, #-0x48]
    // 0x1358898: StoreField: r0->field_23 = r1
    //     0x1358898: stur            w1, [x0, #0x23]
    // 0x135889c: ldur            x2, [fp, #-8]
    // 0x13588a0: r1 = Function '<anonymous closure>':.
    //     0x13588a0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a98] AnonymousClosure: (0x1358928), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::bottomNavigationBar (0x1358428)
    //     0x13588a4: ldr             x1, [x1, #0xa98]
    // 0x13588a8: r0 = AllocateClosure()
    //     0x13588a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13588ac: mov             x1, x0
    // 0x13588b0: ldur            x0, [fp, #-0x60]
    // 0x13588b4: StoreField: r0->field_1f = r1
    //     0x13588b4: stur            w1, [x0, #0x1f]
    // 0x13588b8: ldur            x1, [fp, #-0x58]
    // 0x13588bc: StoreField: r0->field_2b = r1
    //     0x13588bc: stur            w1, [x0, #0x2b]
    // 0x13588c0: ldur            x1, [fp, #-0x50]
    // 0x13588c4: StoreField: r0->field_27 = r1
    //     0x13588c4: stur            w1, [x0, #0x27]
    // 0x13588c8: r0 = Visibility()
    //     0x13588c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13588cc: ldur            x1, [fp, #-0x60]
    // 0x13588d0: StoreField: r0->field_b = r1
    //     0x13588d0: stur            w1, [x0, #0xb]
    // 0x13588d4: r1 = Instance_SizedBox
    //     0x13588d4: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13588d8: StoreField: r0->field_f = r1
    //     0x13588d8: stur            w1, [x0, #0xf]
    // 0x13588dc: ldur            x1, [fp, #-0x28]
    // 0x13588e0: StoreField: r0->field_13 = r1
    //     0x13588e0: stur            w1, [x0, #0x13]
    // 0x13588e4: r1 = false
    //     0x13588e4: add             x1, NULL, #0x30  ; false
    // 0x13588e8: ArrayStore: r0[0] = r1  ; List_4
    //     0x13588e8: stur            w1, [x0, #0x17]
    // 0x13588ec: StoreField: r0->field_1b = r1
    //     0x13588ec: stur            w1, [x0, #0x1b]
    // 0x13588f0: StoreField: r0->field_1f = r1
    //     0x13588f0: stur            w1, [x0, #0x1f]
    // 0x13588f4: StoreField: r0->field_23 = r1
    //     0x13588f4: stur            w1, [x0, #0x23]
    // 0x13588f8: StoreField: r0->field_27 = r1
    //     0x13588f8: stur            w1, [x0, #0x27]
    // 0x13588fc: StoreField: r0->field_2b = r1
    //     0x13588fc: stur            w1, [x0, #0x2b]
    // 0x1358900: LeaveFrame
    //     0x1358900: mov             SP, fp
    //     0x1358904: ldp             fp, lr, [SP], #0x10
    // 0x1358908: ret
    //     0x1358908: ret             
    // 0x135890c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135890c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1358910: b               #0x13584b4
    // 0x1358914: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1358914: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1358918: b               #0x1358658
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1358928, size: 0x7c
    // 0x1358928: EnterFrame
    //     0x1358928: stp             fp, lr, [SP, #-0x10]!
    //     0x135892c: mov             fp, SP
    // 0x1358930: AllocStack(0x30)
    //     0x1358930: sub             SP, SP, #0x30
    // 0x1358934: SetupParameters()
    //     0x1358934: ldr             x0, [fp, #0x10]
    //     0x1358938: ldur            w2, [x0, #0x17]
    //     0x135893c: add             x2, x2, HEAP, lsl #32
    // 0x1358940: CheckStackOverflow
    //     0x1358940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1358944: cmp             SP, x16
    //     0x1358948: b.ls            #0x135899c
    // 0x135894c: LoadField: r0 = r2->field_13
    //     0x135894c: ldur            w0, [x2, #0x13]
    // 0x1358950: DecompressPointer r0
    //     0x1358950: add             x0, x0, HEAP, lsl #32
    // 0x1358954: stur            x0, [fp, #-8]
    // 0x1358958: r1 = Function '<anonymous closure>':.
    //     0x1358958: add             x1, PP, #0x41, lsl #12  ; [pp+0x41aa0] AnonymousClosure: (0x13589a4), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::bottomNavigationBar (0x1358428)
    //     0x135895c: ldr             x1, [x1, #0xaa0]
    // 0x1358960: r0 = AllocateClosure()
    //     0x1358960: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358964: stp             x0, NULL, [SP, #0x18]
    // 0x1358968: ldur            x16, [fp, #-8]
    // 0x135896c: r30 = true
    //     0x135896c: add             lr, NULL, #0x20  ; true
    // 0x1358970: stp             lr, x16, [SP, #8]
    // 0x1358974: r16 = Instance_RoundedRectangleBorder
    //     0x1358974: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1358978: ldr             x16, [x16, #0xc78]
    // 0x135897c: str             x16, [SP]
    // 0x1358980: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x1358980: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x1358984: ldr             x4, [x4, #0xb20]
    // 0x1358988: r0 = showModalBottomSheet()
    //     0x1358988: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x135898c: r0 = Null
    //     0x135898c: mov             x0, NULL
    // 0x1358990: LeaveFrame
    //     0x1358990: mov             SP, fp
    //     0x1358994: ldp             fp, lr, [SP], #0x10
    // 0x1358998: ret
    //     0x1358998: ret             
    // 0x135899c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135899c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13589a0: b               #0x135894c
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x13589a4, size: 0x260
    // 0x13589a4: EnterFrame
    //     0x13589a4: stp             fp, lr, [SP, #-0x10]!
    //     0x13589a8: mov             fp, SP
    // 0x13589ac: AllocStack(0x58)
    //     0x13589ac: sub             SP, SP, #0x58
    // 0x13589b0: SetupParameters()
    //     0x13589b0: ldr             x0, [fp, #0x18]
    //     0x13589b4: ldur            w2, [x0, #0x17]
    //     0x13589b8: add             x2, x2, HEAP, lsl #32
    //     0x13589bc: stur            x2, [fp, #-8]
    // 0x13589c0: CheckStackOverflow
    //     0x13589c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13589c4: cmp             SP, x16
    //     0x13589c8: b.ls            #0x1358bfc
    // 0x13589cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13589cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13589d0: ldr             x0, [x0, #0x1c80]
    //     0x13589d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13589d8: cmp             w0, w16
    //     0x13589dc: b.ne            #0x13589e8
    //     0x13589e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13589e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13589e8: r0 = GetNavigation.size()
    //     0x13589e8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13589ec: LoadField: d0 = r0->field_f
    //     0x13589ec: ldur            d0, [x0, #0xf]
    // 0x13589f0: d1 = 0.800000
    //     0x13589f0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x13589f4: ldr             d1, [x17, #0xb28]
    // 0x13589f8: fmul            d2, d0, d1
    // 0x13589fc: stur            d2, [fp, #-0x48]
    // 0x1358a00: r0 = BoxConstraints()
    //     0x1358a00: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1358a04: stur            x0, [fp, #-0x10]
    // 0x1358a08: StoreField: r0->field_7 = rZR
    //     0x1358a08: stur            xzr, [x0, #7]
    // 0x1358a0c: d0 = inf
    //     0x1358a0c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1358a10: StoreField: r0->field_f = d0
    //     0x1358a10: stur            d0, [x0, #0xf]
    // 0x1358a14: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1358a14: stur            xzr, [x0, #0x17]
    // 0x1358a18: ldur            d0, [fp, #-0x48]
    // 0x1358a1c: StoreField: r0->field_1f = d0
    //     0x1358a1c: stur            d0, [x0, #0x1f]
    // 0x1358a20: ldur            x2, [fp, #-8]
    // 0x1358a24: LoadField: r1 = r2->field_f
    //     0x1358a24: ldur            w1, [x2, #0xf]
    // 0x1358a28: DecompressPointer r1
    //     0x1358a28: add             x1, x1, HEAP, lsl #32
    // 0x1358a2c: r0 = controller()
    //     0x1358a2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358a30: LoadField: r1 = r0->field_63
    //     0x1358a30: ldur            w1, [x0, #0x63]
    // 0x1358a34: DecompressPointer r1
    //     0x1358a34: add             x1, x1, HEAP, lsl #32
    // 0x1358a38: r0 = value()
    //     0x1358a38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358a3c: LoadField: r2 = r0->field_b
    //     0x1358a3c: ldur            w2, [x0, #0xb]
    // 0x1358a40: DecompressPointer r2
    //     0x1358a40: add             x2, x2, HEAP, lsl #32
    // 0x1358a44: ldur            x0, [fp, #-8]
    // 0x1358a48: stur            x2, [fp, #-0x18]
    // 0x1358a4c: LoadField: r1 = r0->field_f
    //     0x1358a4c: ldur            w1, [x0, #0xf]
    // 0x1358a50: DecompressPointer r1
    //     0x1358a50: add             x1, x1, HEAP, lsl #32
    // 0x1358a54: r0 = controller()
    //     0x1358a54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358a58: LoadField: r1 = r0->field_5b
    //     0x1358a58: ldur            w1, [x0, #0x5b]
    // 0x1358a5c: DecompressPointer r1
    //     0x1358a5c: add             x1, x1, HEAP, lsl #32
    // 0x1358a60: r0 = value()
    //     0x1358a60: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358a64: LoadField: r1 = r0->field_b
    //     0x1358a64: ldur            w1, [x0, #0xb]
    // 0x1358a68: DecompressPointer r1
    //     0x1358a68: add             x1, x1, HEAP, lsl #32
    // 0x1358a6c: cmp             w1, NULL
    // 0x1358a70: b.ne            #0x1358a7c
    // 0x1358a74: r0 = Null
    //     0x1358a74: mov             x0, NULL
    // 0x1358a78: b               #0x1358aa0
    // 0x1358a7c: LoadField: r0 = r1->field_1b
    //     0x1358a7c: ldur            w0, [x1, #0x1b]
    // 0x1358a80: DecompressPointer r0
    //     0x1358a80: add             x0, x0, HEAP, lsl #32
    // 0x1358a84: cmp             w0, NULL
    // 0x1358a88: b.ne            #0x1358a94
    // 0x1358a8c: r0 = Null
    //     0x1358a8c: mov             x0, NULL
    // 0x1358a90: b               #0x1358aa0
    // 0x1358a94: LoadField: r1 = r0->field_7
    //     0x1358a94: ldur            w1, [x0, #7]
    // 0x1358a98: DecompressPointer r1
    //     0x1358a98: add             x1, x1, HEAP, lsl #32
    // 0x1358a9c: mov             x0, x1
    // 0x1358aa0: ldur            x2, [fp, #-8]
    // 0x1358aa4: stur            x0, [fp, #-0x20]
    // 0x1358aa8: LoadField: r1 = r2->field_f
    //     0x1358aa8: ldur            w1, [x2, #0xf]
    // 0x1358aac: DecompressPointer r1
    //     0x1358aac: add             x1, x1, HEAP, lsl #32
    // 0x1358ab0: r0 = controller()
    //     0x1358ab0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358ab4: ldur            x2, [fp, #-8]
    // 0x1358ab8: stur            x0, [fp, #-0x28]
    // 0x1358abc: LoadField: r1 = r2->field_f
    //     0x1358abc: ldur            w1, [x2, #0xf]
    // 0x1358ac0: DecompressPointer r1
    //     0x1358ac0: add             x1, x1, HEAP, lsl #32
    // 0x1358ac4: r0 = controller()
    //     0x1358ac4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358ac8: LoadField: r1 = r0->field_77
    //     0x1358ac8: ldur            w1, [x0, #0x77]
    // 0x1358acc: DecompressPointer r1
    //     0x1358acc: add             x1, x1, HEAP, lsl #32
    // 0x1358ad0: r0 = value()
    //     0x1358ad0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358ad4: ldur            x2, [fp, #-8]
    // 0x1358ad8: stur            x0, [fp, #-0x30]
    // 0x1358adc: LoadField: r1 = r2->field_f
    //     0x1358adc: ldur            w1, [x2, #0xf]
    // 0x1358ae0: DecompressPointer r1
    //     0x1358ae0: add             x1, x1, HEAP, lsl #32
    // 0x1358ae4: r0 = controller()
    //     0x1358ae4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358ae8: LoadField: r1 = r0->field_5b
    //     0x1358ae8: ldur            w1, [x0, #0x5b]
    // 0x1358aec: DecompressPointer r1
    //     0x1358aec: add             x1, x1, HEAP, lsl #32
    // 0x1358af0: r0 = value()
    //     0x1358af0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358af4: LoadField: r1 = r0->field_b
    //     0x1358af4: ldur            w1, [x0, #0xb]
    // 0x1358af8: DecompressPointer r1
    //     0x1358af8: add             x1, x1, HEAP, lsl #32
    // 0x1358afc: cmp             w1, NULL
    // 0x1358b00: b.ne            #0x1358b0c
    // 0x1358b04: r3 = Null
    //     0x1358b04: mov             x3, NULL
    // 0x1358b08: b               #0x1358b18
    // 0x1358b0c: LoadField: r0 = r1->field_1b
    //     0x1358b0c: ldur            w0, [x1, #0x1b]
    // 0x1358b10: DecompressPointer r0
    //     0x1358b10: add             x0, x0, HEAP, lsl #32
    // 0x1358b14: mov             x3, x0
    // 0x1358b18: ldur            x2, [fp, #-0x18]
    // 0x1358b1c: ldur            x1, [fp, #-0x20]
    // 0x1358b20: ldur            x0, [fp, #-0x30]
    // 0x1358b24: stur            x3, [fp, #-0x38]
    // 0x1358b28: r0 = OffersListWidget()
    //     0x1358b28: bl              #0xb4c0b0  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0x1358b2c: mov             x3, x0
    // 0x1358b30: ldur            x0, [fp, #-0x18]
    // 0x1358b34: stur            x3, [fp, #-0x40]
    // 0x1358b38: StoreField: r3->field_b = r0
    //     0x1358b38: stur            w0, [x3, #0xb]
    // 0x1358b3c: ldur            x0, [fp, #-0x20]
    // 0x1358b40: StoreField: r3->field_f = r0
    //     0x1358b40: stur            w0, [x3, #0xf]
    // 0x1358b44: ldur            x2, [fp, #-8]
    // 0x1358b48: r1 = Function '<anonymous closure>':.
    //     0x1358b48: add             x1, PP, #0x41, lsl #12  ; [pp+0x41aa8] AnonymousClosure: (0x1358f48), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1358b4c: ldr             x1, [x1, #0xaa8]
    // 0x1358b50: r0 = AllocateClosure()
    //     0x1358b50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358b54: mov             x1, x0
    // 0x1358b58: ldur            x0, [fp, #-0x40]
    // 0x1358b5c: StoreField: r0->field_13 = r1
    //     0x1358b5c: stur            w1, [x0, #0x13]
    // 0x1358b60: ldur            x2, [fp, #-8]
    // 0x1358b64: r1 = Function '<anonymous closure>':.
    //     0x1358b64: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ab0] AnonymousClosure: (0x1358c04), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1358b68: ldr             x1, [x1, #0xab0]
    // 0x1358b6c: r0 = AllocateClosure()
    //     0x1358b6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358b70: mov             x1, x0
    // 0x1358b74: ldur            x0, [fp, #-0x40]
    // 0x1358b78: ArrayStore: r0[0] = r1  ; List_4
    //     0x1358b78: stur            w1, [x0, #0x17]
    // 0x1358b7c: r1 = true
    //     0x1358b7c: add             x1, NULL, #0x20  ; true
    // 0x1358b80: StoreField: r0->field_1b = r1
    //     0x1358b80: stur            w1, [x0, #0x1b]
    // 0x1358b84: r1 = "bag"
    //     0x1358b84: add             x1, PP, #0xb, lsl #12  ; [pp+0xb460] "bag"
    //     0x1358b88: ldr             x1, [x1, #0x460]
    // 0x1358b8c: StoreField: r0->field_23 = r1
    //     0x1358b8c: stur            w1, [x0, #0x23]
    // 0x1358b90: ldur            x2, [fp, #-0x28]
    // 0x1358b94: r1 = Function 'postEvents':.
    //     0x1358b94: add             x1, PP, #0x32, lsl #12  ; [pp+0x321b0] AnonymousClosure: (0x89c5cc), in [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents (0x8608dc)
    //     0x1358b98: ldr             x1, [x1, #0x1b0]
    // 0x1358b9c: r0 = AllocateClosure()
    //     0x1358b9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358ba0: mov             x1, x0
    // 0x1358ba4: ldur            x0, [fp, #-0x40]
    // 0x1358ba8: StoreField: r0->field_1f = r1
    //     0x1358ba8: stur            w1, [x0, #0x1f]
    // 0x1358bac: r1 = "false"
    //     0x1358bac: add             x1, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0x1358bb0: ldr             x1, [x1, #0xed8]
    // 0x1358bb4: StoreField: r0->field_27 = r1
    //     0x1358bb4: stur            w1, [x0, #0x27]
    // 0x1358bb8: ldur            x1, [fp, #-0x30]
    // 0x1358bbc: StoreField: r0->field_2b = r1
    //     0x1358bbc: stur            w1, [x0, #0x2b]
    // 0x1358bc0: ldur            x1, [fp, #-0x38]
    // 0x1358bc4: StoreField: r0->field_2f = r1
    //     0x1358bc4: stur            w1, [x0, #0x2f]
    // 0x1358bc8: r0 = Container()
    //     0x1358bc8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1358bcc: stur            x0, [fp, #-8]
    // 0x1358bd0: ldur            x16, [fp, #-0x10]
    // 0x1358bd4: ldur            lr, [fp, #-0x40]
    // 0x1358bd8: stp             lr, x16, [SP]
    // 0x1358bdc: mov             x1, x0
    // 0x1358be0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, constraints, 0x1, null]
    //     0x1358be0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b30] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "constraints", 0x1, Null]
    //     0x1358be4: ldr             x4, [x4, #0xb30]
    // 0x1358be8: r0 = Container()
    //     0x1358be8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1358bec: ldur            x0, [fp, #-8]
    // 0x1358bf0: LeaveFrame
    //     0x1358bf0: mov             SP, fp
    //     0x1358bf4: ldp             fp, lr, [SP], #0x10
    // 0x1358bf8: ret
    //     0x1358bf8: ret             
    // 0x1358bfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1358bfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1358c00: b               #0x13589cc
  }
  [closure] Null <anonymous closure>(dynamic, AddToBagRequest) {
    // ** addr: 0x137af74, size: 0x54
    // 0x137af74: EnterFrame
    //     0x137af74: stp             fp, lr, [SP, #-0x10]!
    //     0x137af78: mov             fp, SP
    // 0x137af7c: ldr             x0, [fp, #0x18]
    // 0x137af80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x137af80: ldur            w1, [x0, #0x17]
    // 0x137af84: DecompressPointer r1
    //     0x137af84: add             x1, x1, HEAP, lsl #32
    // 0x137af88: CheckStackOverflow
    //     0x137af88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137af8c: cmp             SP, x16
    //     0x137af90: b.ls            #0x137afc0
    // 0x137af94: LoadField: r0 = r1->field_f
    //     0x137af94: ldur            w0, [x1, #0xf]
    // 0x137af98: DecompressPointer r0
    //     0x137af98: add             x0, x0, HEAP, lsl #32
    // 0x137af9c: mov             x1, x0
    // 0x137afa0: r0 = controller()
    //     0x137afa0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137afa4: mov             x1, x0
    // 0x137afa8: ldr             x2, [fp, #0x10]
    // 0x137afac: r0 = addToBag()
    //     0x137afac: bl              #0x13597f4  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::addToBag
    // 0x137afb0: r0 = Null
    //     0x137afb0: mov             x0, NULL
    // 0x137afb4: LeaveFrame
    //     0x137afb4: mov             SP, fp
    //     0x137afb8: ldp             fp, lr, [SP], #0x10
    // 0x137afbc: ret
    //     0x137afbc: ret             
    // 0x137afc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137afc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137afc4: b               #0x137af94
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x137afc8, size: 0x36c
    // 0x137afc8: EnterFrame
    //     0x137afc8: stp             fp, lr, [SP, #-0x10]!
    //     0x137afcc: mov             fp, SP
    // 0x137afd0: AllocStack(0x40)
    //     0x137afd0: sub             SP, SP, #0x40
    // 0x137afd4: SetupParameters()
    //     0x137afd4: ldr             x0, [fp, #0x10]
    //     0x137afd8: ldur            w2, [x0, #0x17]
    //     0x137afdc: add             x2, x2, HEAP, lsl #32
    //     0x137afe0: stur            x2, [fp, #-8]
    // 0x137afe4: CheckStackOverflow
    //     0x137afe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137afe8: cmp             SP, x16
    //     0x137afec: b.ls            #0x137b32c
    // 0x137aff0: LoadField: r1 = r2->field_f
    //     0x137aff0: ldur            w1, [x2, #0xf]
    // 0x137aff4: DecompressPointer r1
    //     0x137aff4: add             x1, x1, HEAP, lsl #32
    // 0x137aff8: r0 = controller()
    //     0x137aff8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137affc: LoadField: r1 = r0->field_5b
    //     0x137affc: ldur            w1, [x0, #0x5b]
    // 0x137b000: DecompressPointer r1
    //     0x137b000: add             x1, x1, HEAP, lsl #32
    // 0x137b004: r0 = value()
    //     0x137b004: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b008: LoadField: r1 = r0->field_b
    //     0x137b008: ldur            w1, [x0, #0xb]
    // 0x137b00c: DecompressPointer r1
    //     0x137b00c: add             x1, x1, HEAP, lsl #32
    // 0x137b010: cmp             w1, NULL
    // 0x137b014: b.ne            #0x137b020
    // 0x137b018: r0 = Null
    //     0x137b018: mov             x0, NULL
    // 0x137b01c: b               #0x137b03c
    // 0x137b020: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x137b020: ldur            w0, [x1, #0x17]
    // 0x137b024: DecompressPointer r0
    //     0x137b024: add             x0, x0, HEAP, lsl #32
    // 0x137b028: LoadField: r1 = r0->field_b
    //     0x137b028: ldur            w1, [x0, #0xb]
    // 0x137b02c: cbnz            w1, #0x137b038
    // 0x137b030: r0 = false
    //     0x137b030: add             x0, NULL, #0x30  ; false
    // 0x137b034: b               #0x137b03c
    // 0x137b038: r0 = true
    //     0x137b038: add             x0, NULL, #0x20  ; true
    // 0x137b03c: cmp             w0, NULL
    // 0x137b040: b.eq            #0x137b2b4
    // 0x137b044: tbnz            w0, #4, #0x137b2b4
    // 0x137b048: ldur            x2, [fp, #-8]
    // 0x137b04c: LoadField: r1 = r2->field_f
    //     0x137b04c: ldur            w1, [x2, #0xf]
    // 0x137b050: DecompressPointer r1
    //     0x137b050: add             x1, x1, HEAP, lsl #32
    // 0x137b054: r0 = controller()
    //     0x137b054: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b058: LoadField: r1 = r0->field_5b
    //     0x137b058: ldur            w1, [x0, #0x5b]
    // 0x137b05c: DecompressPointer r1
    //     0x137b05c: add             x1, x1, HEAP, lsl #32
    // 0x137b060: r0 = value()
    //     0x137b060: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b064: LoadField: r2 = r0->field_b
    //     0x137b064: ldur            w2, [x0, #0xb]
    // 0x137b068: DecompressPointer r2
    //     0x137b068: add             x2, x2, HEAP, lsl #32
    // 0x137b06c: ldur            x0, [fp, #-8]
    // 0x137b070: stur            x2, [fp, #-0x10]
    // 0x137b074: LoadField: r1 = r0->field_f
    //     0x137b074: ldur            w1, [x0, #0xf]
    // 0x137b078: DecompressPointer r1
    //     0x137b078: add             x1, x1, HEAP, lsl #32
    // 0x137b07c: r0 = controller()
    //     0x137b07c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b080: LoadField: r1 = r0->field_63
    //     0x137b080: ldur            w1, [x0, #0x63]
    // 0x137b084: DecompressPointer r1
    //     0x137b084: add             x1, x1, HEAP, lsl #32
    // 0x137b088: r0 = value()
    //     0x137b088: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b08c: ldur            x2, [fp, #-8]
    // 0x137b090: LoadField: r1 = r2->field_f
    //     0x137b090: ldur            w1, [x2, #0xf]
    // 0x137b094: DecompressPointer r1
    //     0x137b094: add             x1, x1, HEAP, lsl #32
    // 0x137b098: r0 = controller()
    //     0x137b098: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b09c: LoadField: r2 = r0->field_87
    //     0x137b09c: ldur            w2, [x0, #0x87]
    // 0x137b0a0: DecompressPointer r2
    //     0x137b0a0: add             x2, x2, HEAP, lsl #32
    // 0x137b0a4: ldur            x0, [fp, #-8]
    // 0x137b0a8: stur            x2, [fp, #-0x18]
    // 0x137b0ac: LoadField: r1 = r0->field_f
    //     0x137b0ac: ldur            w1, [x0, #0xf]
    // 0x137b0b0: DecompressPointer r1
    //     0x137b0b0: add             x1, x1, HEAP, lsl #32
    // 0x137b0b4: r0 = controller()
    //     0x137b0b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b0b8: LoadField: r1 = r0->field_6f
    //     0x137b0b8: ldur            w1, [x0, #0x6f]
    // 0x137b0bc: DecompressPointer r1
    //     0x137b0bc: add             x1, x1, HEAP, lsl #32
    // 0x137b0c0: r0 = value()
    //     0x137b0c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b0c4: LoadField: r1 = r0->field_b
    //     0x137b0c4: ldur            w1, [x0, #0xb]
    // 0x137b0c8: DecompressPointer r1
    //     0x137b0c8: add             x1, x1, HEAP, lsl #32
    // 0x137b0cc: cmp             w1, NULL
    // 0x137b0d0: b.ne            #0x137b0dc
    // 0x137b0d4: r0 = Null
    //     0x137b0d4: mov             x0, NULL
    // 0x137b0d8: b               #0x137b0e4
    // 0x137b0dc: LoadField: r0 = r1->field_7
    //     0x137b0dc: ldur            w0, [x1, #7]
    // 0x137b0e0: DecompressPointer r0
    //     0x137b0e0: add             x0, x0, HEAP, lsl #32
    // 0x137b0e4: cmp             w0, NULL
    // 0x137b0e8: b.ne            #0x137b0f4
    // 0x137b0ec: r4 = ""
    //     0x137b0ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137b0f0: b               #0x137b0f8
    // 0x137b0f4: mov             x4, x0
    // 0x137b0f8: ldur            x3, [fp, #-8]
    // 0x137b0fc: mov             x0, x4
    // 0x137b100: stur            x4, [fp, #-0x20]
    // 0x137b104: r2 = Null
    //     0x137b104: mov             x2, NULL
    // 0x137b108: r1 = Null
    //     0x137b108: mov             x1, NULL
    // 0x137b10c: r4 = 60
    //     0x137b10c: movz            x4, #0x3c
    // 0x137b110: branchIfSmi(r0, 0x137b11c)
    //     0x137b110: tbz             w0, #0, #0x137b11c
    // 0x137b114: r4 = LoadClassIdInstr(r0)
    //     0x137b114: ldur            x4, [x0, #-1]
    //     0x137b118: ubfx            x4, x4, #0xc, #0x14
    // 0x137b11c: sub             x4, x4, #0x5e
    // 0x137b120: cmp             x4, #1
    // 0x137b124: b.ls            #0x137b138
    // 0x137b128: r8 = String
    //     0x137b128: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x137b12c: r3 = Null
    //     0x137b12c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41ac8] Null
    //     0x137b130: ldr             x3, [x3, #0xac8]
    // 0x137b134: r0 = String()
    //     0x137b134: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x137b138: ldur            x2, [fp, #-8]
    // 0x137b13c: LoadField: r1 = r2->field_f
    //     0x137b13c: ldur            w1, [x2, #0xf]
    // 0x137b140: DecompressPointer r1
    //     0x137b140: add             x1, x1, HEAP, lsl #32
    // 0x137b144: r0 = controller()
    //     0x137b144: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b148: LoadField: r1 = r0->field_83
    //     0x137b148: ldur            w1, [x0, #0x83]
    // 0x137b14c: DecompressPointer r1
    //     0x137b14c: add             x1, x1, HEAP, lsl #32
    // 0x137b150: cmp             w1, NULL
    // 0x137b154: b.ne            #0x137b160
    // 0x137b158: r5 = ""
    //     0x137b158: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137b15c: b               #0x137b164
    // 0x137b160: mov             x5, x1
    // 0x137b164: ldur            x2, [fp, #-8]
    // 0x137b168: ldur            x4, [fp, #-0x10]
    // 0x137b16c: ldur            x3, [fp, #-0x18]
    // 0x137b170: ldur            x0, [fp, #-0x20]
    // 0x137b174: stur            x5, [fp, #-0x28]
    // 0x137b178: LoadField: r1 = r2->field_f
    //     0x137b178: ldur            w1, [x2, #0xf]
    // 0x137b17c: DecompressPointer r1
    //     0x137b17c: add             x1, x1, HEAP, lsl #32
    // 0x137b180: r0 = controller()
    //     0x137b180: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b184: LoadField: r2 = r0->field_6b
    //     0x137b184: ldur            w2, [x0, #0x6b]
    // 0x137b188: DecompressPointer r2
    //     0x137b188: add             x2, x2, HEAP, lsl #32
    // 0x137b18c: ldur            x0, [fp, #-8]
    // 0x137b190: stur            x2, [fp, #-0x30]
    // 0x137b194: LoadField: r1 = r0->field_f
    //     0x137b194: ldur            w1, [x0, #0xf]
    // 0x137b198: DecompressPointer r1
    //     0x137b198: add             x1, x1, HEAP, lsl #32
    // 0x137b19c: r0 = controller()
    //     0x137b19c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b1a0: LoadField: r1 = r0->field_ab
    //     0x137b1a0: ldur            w1, [x0, #0xab]
    // 0x137b1a4: DecompressPointer r1
    //     0x137b1a4: add             x1, x1, HEAP, lsl #32
    // 0x137b1a8: r0 = value()
    //     0x137b1a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b1ac: stur            x0, [fp, #-0x38]
    // 0x137b1b0: r0 = BagItemViewWidget()
    //     0x137b1b0: bl              #0x137b334  ; AllocateBagItemViewWidgetStub -> BagItemViewWidget (size=0x40)
    // 0x137b1b4: mov             x3, x0
    // 0x137b1b8: ldur            x0, [fp, #-0x10]
    // 0x137b1bc: stur            x3, [fp, #-0x40]
    // 0x137b1c0: StoreField: r3->field_b = r0
    //     0x137b1c0: stur            w0, [x3, #0xb]
    // 0x137b1c4: ldur            x2, [fp, #-8]
    // 0x137b1c8: r1 = Function '<anonymous closure>':.
    //     0x137b1c8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ad8] AnonymousClosure: (0x137bbb4), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b1cc: ldr             x1, [x1, #0xad8]
    // 0x137b1d0: r0 = AllocateClosure()
    //     0x137b1d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b1d4: mov             x1, x0
    // 0x137b1d8: ldur            x0, [fp, #-0x40]
    // 0x137b1dc: StoreField: r0->field_f = r1
    //     0x137b1dc: stur            w1, [x0, #0xf]
    // 0x137b1e0: ldur            x2, [fp, #-8]
    // 0x137b1e4: r1 = Function '<anonymous closure>':.
    //     0x137b1e4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ae0] AnonymousClosure: (0x137b7cc), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b1e8: ldr             x1, [x1, #0xae0]
    // 0x137b1ec: r0 = AllocateClosure()
    //     0x137b1ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b1f0: mov             x1, x0
    // 0x137b1f4: ldur            x0, [fp, #-0x40]
    // 0x137b1f8: StoreField: r0->field_13 = r1
    //     0x137b1f8: stur            w1, [x0, #0x13]
    // 0x137b1fc: ldur            x1, [fp, #-0x38]
    // 0x137b200: StoreField: r0->field_2b = r1
    //     0x137b200: stur            w1, [x0, #0x2b]
    // 0x137b204: ldur            x2, [fp, #-8]
    // 0x137b208: r1 = Function '<anonymous closure>':.
    //     0x137b208: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ae8] AnonymousClosure: (0x135a4e4), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x137b20c: ldr             x1, [x1, #0xae8]
    // 0x137b210: r0 = AllocateClosure()
    //     0x137b210: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b214: mov             x1, x0
    // 0x137b218: ldur            x0, [fp, #-0x40]
    // 0x137b21c: ArrayStore: r0[0] = r1  ; List_4
    //     0x137b21c: stur            w1, [x0, #0x17]
    // 0x137b220: ldur            x2, [fp, #-8]
    // 0x137b224: r1 = Function '<anonymous closure>':.
    //     0x137b224: add             x1, PP, #0x41, lsl #12  ; [pp+0x41af0] AnonymousClosure: (0x135a3e8), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x137b228: ldr             x1, [x1, #0xaf0]
    // 0x137b22c: r0 = AllocateClosure()
    //     0x137b22c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b230: mov             x1, x0
    // 0x137b234: ldur            x0, [fp, #-0x40]
    // 0x137b238: StoreField: r0->field_1b = r1
    //     0x137b238: stur            w1, [x0, #0x1b]
    // 0x137b23c: ldur            x2, [fp, #-8]
    // 0x137b240: r1 = Function '<anonymous closure>':.
    //     0x137b240: add             x1, PP, #0x41, lsl #12  ; [pp+0x41af8] AnonymousClosure: (0x137b65c), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b244: ldr             x1, [x1, #0xaf8]
    // 0x137b248: r0 = AllocateClosure()
    //     0x137b248: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b24c: mov             x1, x0
    // 0x137b250: ldur            x0, [fp, #-0x40]
    // 0x137b254: StoreField: r0->field_1f = r1
    //     0x137b254: stur            w1, [x0, #0x1f]
    // 0x137b258: ldur            x2, [fp, #-8]
    // 0x137b25c: r1 = Function '<anonymous closure>':.
    //     0x137b25c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b00] AnonymousClosure: (0x137af74), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b260: ldr             x1, [x1, #0xb00]
    // 0x137b264: r0 = AllocateClosure()
    //     0x137b264: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b268: mov             x1, x0
    // 0x137b26c: ldur            x0, [fp, #-0x40]
    // 0x137b270: StoreField: r0->field_23 = r1
    //     0x137b270: stur            w1, [x0, #0x23]
    // 0x137b274: ldur            x2, [fp, #-8]
    // 0x137b278: r1 = Function '<anonymous closure>':.
    //     0x137b278: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b08] AnonymousClosure: (0x137b340), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b27c: ldr             x1, [x1, #0xb08]
    // 0x137b280: r0 = AllocateClosure()
    //     0x137b280: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b284: mov             x1, x0
    // 0x137b288: ldur            x0, [fp, #-0x40]
    // 0x137b28c: StoreField: r0->field_27 = r1
    //     0x137b28c: stur            w1, [x0, #0x27]
    // 0x137b290: ldur            x1, [fp, #-0x20]
    // 0x137b294: StoreField: r0->field_2f = r1
    //     0x137b294: stur            w1, [x0, #0x2f]
    // 0x137b298: ldur            x1, [fp, #-0x28]
    // 0x137b29c: StoreField: r0->field_33 = r1
    //     0x137b29c: stur            w1, [x0, #0x33]
    // 0x137b2a0: ldur            x1, [fp, #-0x30]
    // 0x137b2a4: StoreField: r0->field_37 = r1
    //     0x137b2a4: stur            w1, [x0, #0x37]
    // 0x137b2a8: ldur            x1, [fp, #-0x18]
    // 0x137b2ac: StoreField: r0->field_3b = r1
    //     0x137b2ac: stur            w1, [x0, #0x3b]
    // 0x137b2b0: b               #0x137b320
    // 0x137b2b4: ldur            x0, [fp, #-8]
    // 0x137b2b8: LoadField: r1 = r0->field_f
    //     0x137b2b8: ldur            w1, [x0, #0xf]
    // 0x137b2bc: DecompressPointer r1
    //     0x137b2bc: add             x1, x1, HEAP, lsl #32
    // 0x137b2c0: r0 = controller()
    //     0x137b2c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b2c4: LoadField: r1 = r0->field_5b
    //     0x137b2c4: ldur            w1, [x0, #0x5b]
    // 0x137b2c8: DecompressPointer r1
    //     0x137b2c8: add             x1, x1, HEAP, lsl #32
    // 0x137b2cc: r0 = value()
    //     0x137b2cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b2d0: LoadField: r1 = r0->field_b
    //     0x137b2d0: ldur            w1, [x0, #0xb]
    // 0x137b2d4: DecompressPointer r1
    //     0x137b2d4: add             x1, x1, HEAP, lsl #32
    // 0x137b2d8: cmp             w1, NULL
    // 0x137b2dc: b.ne            #0x137b2fc
    // 0x137b2e0: r0 = Container()
    //     0x137b2e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x137b2e4: mov             x1, x0
    // 0x137b2e8: stur            x0, [fp, #-8]
    // 0x137b2ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x137b2ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x137b2f0: r0 = Container()
    //     0x137b2f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x137b2f4: ldur            x0, [fp, #-8]
    // 0x137b2f8: b               #0x137b304
    // 0x137b2fc: r0 = Instance_EmptyBagWidget
    //     0x137b2fc: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e718] Obj!EmptyBagWidget@d66d81
    //     0x137b300: ldr             x0, [x0, #0x718]
    // 0x137b304: stur            x0, [fp, #-8]
    // 0x137b308: r0 = Center()
    //     0x137b308: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x137b30c: r1 = Instance_Alignment
    //     0x137b30c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x137b310: ldr             x1, [x1, #0xb10]
    // 0x137b314: StoreField: r0->field_f = r1
    //     0x137b314: stur            w1, [x0, #0xf]
    // 0x137b318: ldur            x1, [fp, #-8]
    // 0x137b31c: StoreField: r0->field_b = r1
    //     0x137b31c: stur            w1, [x0, #0xb]
    // 0x137b320: LeaveFrame
    //     0x137b320: mov             SP, fp
    //     0x137b324: ldp             fp, lr, [SP], #0x10
    // 0x137b328: ret
    //     0x137b328: ret             
    // 0x137b32c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137b32c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137b330: b               #0x137aff0
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String) {
    // ** addr: 0x137b340, size: 0xb8
    // 0x137b340: EnterFrame
    //     0x137b340: stp             fp, lr, [SP, #-0x10]!
    //     0x137b344: mov             fp, SP
    // 0x137b348: AllocStack(0x38)
    //     0x137b348: sub             SP, SP, #0x38
    // 0x137b34c: SetupParameters()
    //     0x137b34c: ldr             x0, [fp, #0x30]
    //     0x137b350: ldur            w1, [x0, #0x17]
    //     0x137b354: add             x1, x1, HEAP, lsl #32
    //     0x137b358: stur            x1, [fp, #-8]
    // 0x137b35c: CheckStackOverflow
    //     0x137b35c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137b360: cmp             SP, x16
    //     0x137b364: b.ls            #0x137b3f0
    // 0x137b368: r1 = 4
    //     0x137b368: movz            x1, #0x4
    // 0x137b36c: r0 = AllocateContext()
    //     0x137b36c: bl              #0x16f6108  ; AllocateContextStub
    // 0x137b370: mov             x1, x0
    // 0x137b374: ldur            x0, [fp, #-8]
    // 0x137b378: StoreField: r1->field_b = r0
    //     0x137b378: stur            w0, [x1, #0xb]
    // 0x137b37c: ldr             x2, [fp, #0x28]
    // 0x137b380: StoreField: r1->field_f = r2
    //     0x137b380: stur            w2, [x1, #0xf]
    // 0x137b384: ldr             x2, [fp, #0x20]
    // 0x137b388: StoreField: r1->field_13 = r2
    //     0x137b388: stur            w2, [x1, #0x13]
    // 0x137b38c: ldr             x2, [fp, #0x18]
    // 0x137b390: ArrayStore: r1[0] = r2  ; List_4
    //     0x137b390: stur            w2, [x1, #0x17]
    // 0x137b394: ldr             x2, [fp, #0x10]
    // 0x137b398: StoreField: r1->field_1b = r2
    //     0x137b398: stur            w2, [x1, #0x1b]
    // 0x137b39c: LoadField: r3 = r0->field_13
    //     0x137b39c: ldur            w3, [x0, #0x13]
    // 0x137b3a0: DecompressPointer r3
    //     0x137b3a0: add             x3, x3, HEAP, lsl #32
    // 0x137b3a4: mov             x2, x1
    // 0x137b3a8: stur            x3, [fp, #-0x10]
    // 0x137b3ac: r1 = Function '<anonymous closure>':.
    //     0x137b3ac: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b10] AnonymousClosure: (0x137b3f8), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b3b0: ldr             x1, [x1, #0xb10]
    // 0x137b3b4: r0 = AllocateClosure()
    //     0x137b3b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b3b8: stp             x0, NULL, [SP, #0x18]
    // 0x137b3bc: ldur            x16, [fp, #-0x10]
    // 0x137b3c0: r30 = Instance_RoundedRectangleBorder
    //     0x137b3c0: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x137b3c4: ldr             lr, [lr, #0xc78]
    // 0x137b3c8: stp             lr, x16, [SP, #8]
    // 0x137b3cc: r16 = true
    //     0x137b3cc: add             x16, NULL, #0x20  ; true
    // 0x137b3d0: str             x16, [SP]
    // 0x137b3d4: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x137b3d4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x137b3d8: ldr             x4, [x4, #0xd70]
    // 0x137b3dc: r0 = showModalBottomSheet()
    //     0x137b3dc: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x137b3e0: r0 = Null
    //     0x137b3e0: mov             x0, NULL
    // 0x137b3e4: LeaveFrame
    //     0x137b3e4: mov             SP, fp
    //     0x137b3e8: ldp             fp, lr, [SP], #0x10
    // 0x137b3ec: ret
    //     0x137b3ec: ret             
    // 0x137b3f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137b3f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137b3f4: b               #0x137b368
  }
  [closure] CancelOrderConfirmBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x137b3f8, size: 0x264
    // 0x137b3f8: EnterFrame
    //     0x137b3f8: stp             fp, lr, [SP, #-0x10]!
    //     0x137b3fc: mov             fp, SP
    // 0x137b400: AllocStack(0x38)
    //     0x137b400: sub             SP, SP, #0x38
    // 0x137b404: SetupParameters()
    //     0x137b404: ldr             x0, [fp, #0x18]
    //     0x137b408: ldur            w2, [x0, #0x17]
    //     0x137b40c: add             x2, x2, HEAP, lsl #32
    //     0x137b410: stur            x2, [fp, #-8]
    // 0x137b414: CheckStackOverflow
    //     0x137b414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137b418: cmp             SP, x16
    //     0x137b41c: b.ls            #0x137b654
    // 0x137b420: ldr             x1, [fp, #0x10]
    // 0x137b424: r0 = of()
    //     0x137b424: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137b428: LoadField: r1 = r0->field_87
    //     0x137b428: ldur            w1, [x0, #0x87]
    // 0x137b42c: DecompressPointer r1
    //     0x137b42c: add             x1, x1, HEAP, lsl #32
    // 0x137b430: LoadField: r0 = r1->field_2b
    //     0x137b430: ldur            w0, [x1, #0x2b]
    // 0x137b434: DecompressPointer r0
    //     0x137b434: add             x0, x0, HEAP, lsl #32
    // 0x137b438: stur            x0, [fp, #-0x10]
    // 0x137b43c: r1 = Instance_Color
    //     0x137b43c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x137b440: d0 = 0.400000
    //     0x137b440: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x137b444: r0 = withOpacity()
    //     0x137b444: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x137b448: r16 = 12.000000
    //     0x137b448: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x137b44c: ldr             x16, [x16, #0x9e8]
    // 0x137b450: stp             x0, x16, [SP]
    // 0x137b454: ldur            x1, [fp, #-0x10]
    // 0x137b458: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x137b458: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x137b45c: ldr             x4, [x4, #0xaa0]
    // 0x137b460: r0 = copyWith()
    //     0x137b460: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x137b464: stur            x0, [fp, #-0x10]
    // 0x137b468: r0 = Radius()
    //     0x137b468: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x137b46c: d0 = 12.000000
    //     0x137b46c: fmov            d0, #12.00000000
    // 0x137b470: stur            x0, [fp, #-0x18]
    // 0x137b474: StoreField: r0->field_7 = d0
    //     0x137b474: stur            d0, [x0, #7]
    // 0x137b478: StoreField: r0->field_f = d0
    //     0x137b478: stur            d0, [x0, #0xf]
    // 0x137b47c: r0 = BorderRadius()
    //     0x137b47c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x137b480: mov             x1, x0
    // 0x137b484: ldur            x0, [fp, #-0x18]
    // 0x137b488: stur            x1, [fp, #-0x20]
    // 0x137b48c: StoreField: r1->field_7 = r0
    //     0x137b48c: stur            w0, [x1, #7]
    // 0x137b490: StoreField: r1->field_b = r0
    //     0x137b490: stur            w0, [x1, #0xb]
    // 0x137b494: StoreField: r1->field_f = r0
    //     0x137b494: stur            w0, [x1, #0xf]
    // 0x137b498: StoreField: r1->field_13 = r0
    //     0x137b498: stur            w0, [x1, #0x13]
    // 0x137b49c: r0 = OutlineInputBorder()
    //     0x137b49c: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x137b4a0: mov             x1, x0
    // 0x137b4a4: ldur            x0, [fp, #-0x20]
    // 0x137b4a8: stur            x1, [fp, #-0x18]
    // 0x137b4ac: StoreField: r1->field_13 = r0
    //     0x137b4ac: stur            w0, [x1, #0x13]
    // 0x137b4b0: d0 = 4.000000
    //     0x137b4b0: fmov            d0, #4.00000000
    // 0x137b4b4: StoreField: r1->field_b = d0
    //     0x137b4b4: stur            d0, [x1, #0xb]
    // 0x137b4b8: r0 = Instance_BorderSide
    //     0x137b4b8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x137b4bc: ldr             x0, [x0, #0x118]
    // 0x137b4c0: StoreField: r1->field_7 = r0
    //     0x137b4c0: stur            w0, [x1, #7]
    // 0x137b4c4: r0 = Radius()
    //     0x137b4c4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x137b4c8: d0 = 12.000000
    //     0x137b4c8: fmov            d0, #12.00000000
    // 0x137b4cc: stur            x0, [fp, #-0x20]
    // 0x137b4d0: StoreField: r0->field_7 = d0
    //     0x137b4d0: stur            d0, [x0, #7]
    // 0x137b4d4: StoreField: r0->field_f = d0
    //     0x137b4d4: stur            d0, [x0, #0xf]
    // 0x137b4d8: r0 = BorderRadius()
    //     0x137b4d8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x137b4dc: mov             x1, x0
    // 0x137b4e0: ldur            x0, [fp, #-0x20]
    // 0x137b4e4: stur            x1, [fp, #-0x28]
    // 0x137b4e8: StoreField: r1->field_7 = r0
    //     0x137b4e8: stur            w0, [x1, #7]
    // 0x137b4ec: StoreField: r1->field_b = r0
    //     0x137b4ec: stur            w0, [x1, #0xb]
    // 0x137b4f0: StoreField: r1->field_f = r0
    //     0x137b4f0: stur            w0, [x1, #0xf]
    // 0x137b4f4: StoreField: r1->field_13 = r0
    //     0x137b4f4: stur            w0, [x1, #0x13]
    // 0x137b4f8: r0 = OutlineInputBorder()
    //     0x137b4f8: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x137b4fc: mov             x1, x0
    // 0x137b500: ldur            x0, [fp, #-0x28]
    // 0x137b504: stur            x1, [fp, #-0x20]
    // 0x137b508: StoreField: r1->field_13 = r0
    //     0x137b508: stur            w0, [x1, #0x13]
    // 0x137b50c: d0 = 4.000000
    //     0x137b50c: fmov            d0, #4.00000000
    // 0x137b510: StoreField: r1->field_b = d0
    //     0x137b510: stur            d0, [x1, #0xb]
    // 0x137b514: r0 = Instance_BorderSide
    //     0x137b514: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x137b518: ldr             x0, [x0, #0x118]
    // 0x137b51c: StoreField: r1->field_7 = r0
    //     0x137b51c: stur            w0, [x1, #7]
    // 0x137b520: r0 = InputDecoration()
    //     0x137b520: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0x137b524: mov             x2, x0
    // 0x137b528: r0 = "Mention the reason"
    //     0x137b528: add             x0, PP, #0x36, lsl #12  ; [pp+0x36120] "Mention the reason"
    //     0x137b52c: ldr             x0, [x0, #0x120]
    // 0x137b530: stur            x2, [fp, #-0x28]
    // 0x137b534: StoreField: r2->field_13 = r0
    //     0x137b534: stur            w0, [x2, #0x13]
    // 0x137b538: ldur            x0, [fp, #-0x10]
    // 0x137b53c: ArrayStore: r2[0] = r0  ; List_4
    //     0x137b53c: stur            w0, [x2, #0x17]
    // 0x137b540: r0 = true
    //     0x137b540: add             x0, NULL, #0x20  ; true
    // 0x137b544: StoreField: r2->field_47 = r0
    //     0x137b544: stur            w0, [x2, #0x47]
    // 0x137b548: StoreField: r2->field_4b = r0
    //     0x137b548: stur            w0, [x2, #0x4b]
    // 0x137b54c: ldur            x1, [fp, #-0x20]
    // 0x137b550: StoreField: r2->field_c3 = r1
    //     0x137b550: stur            w1, [x2, #0xc3]
    // 0x137b554: ldur            x1, [fp, #-0x18]
    // 0x137b558: StoreField: r2->field_cf = r1
    //     0x137b558: stur            w1, [x2, #0xcf]
    // 0x137b55c: StoreField: r2->field_d7 = r0
    //     0x137b55c: stur            w0, [x2, #0xd7]
    // 0x137b560: ldr             x1, [fp, #0x10]
    // 0x137b564: r0 = of()
    //     0x137b564: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x137b568: LoadField: r1 = r0->field_5b
    //     0x137b568: ldur            w1, [x0, #0x5b]
    // 0x137b56c: DecompressPointer r1
    //     0x137b56c: add             x1, x1, HEAP, lsl #32
    // 0x137b570: stur            x1, [fp, #-0x10]
    // 0x137b574: r0 = BorderSide()
    //     0x137b574: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x137b578: mov             x1, x0
    // 0x137b57c: ldur            x0, [fp, #-0x10]
    // 0x137b580: stur            x1, [fp, #-0x18]
    // 0x137b584: StoreField: r1->field_7 = r0
    //     0x137b584: stur            w0, [x1, #7]
    // 0x137b588: d0 = 1.000000
    //     0x137b588: fmov            d0, #1.00000000
    // 0x137b58c: StoreField: r1->field_b = d0
    //     0x137b58c: stur            d0, [x1, #0xb]
    // 0x137b590: r0 = Instance_BorderStyle
    //     0x137b590: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x137b594: ldr             x0, [x0, #0xf68]
    // 0x137b598: StoreField: r1->field_13 = r0
    //     0x137b598: stur            w0, [x1, #0x13]
    // 0x137b59c: d0 = -1.000000
    //     0x137b59c: fmov            d0, #-1.00000000
    // 0x137b5a0: ArrayStore: r1[0] = d0  ; List_8
    //     0x137b5a0: stur            d0, [x1, #0x17]
    // 0x137b5a4: r0 = Radius()
    //     0x137b5a4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x137b5a8: d0 = 30.000000
    //     0x137b5a8: fmov            d0, #30.00000000
    // 0x137b5ac: stur            x0, [fp, #-0x10]
    // 0x137b5b0: StoreField: r0->field_7 = d0
    //     0x137b5b0: stur            d0, [x0, #7]
    // 0x137b5b4: StoreField: r0->field_f = d0
    //     0x137b5b4: stur            d0, [x0, #0xf]
    // 0x137b5b8: r0 = BorderRadius()
    //     0x137b5b8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x137b5bc: mov             x1, x0
    // 0x137b5c0: ldur            x0, [fp, #-0x10]
    // 0x137b5c4: stur            x1, [fp, #-0x20]
    // 0x137b5c8: StoreField: r1->field_7 = r0
    //     0x137b5c8: stur            w0, [x1, #7]
    // 0x137b5cc: StoreField: r1->field_b = r0
    //     0x137b5cc: stur            w0, [x1, #0xb]
    // 0x137b5d0: StoreField: r1->field_f = r0
    //     0x137b5d0: stur            w0, [x1, #0xf]
    // 0x137b5d4: StoreField: r1->field_13 = r0
    //     0x137b5d4: stur            w0, [x1, #0x13]
    // 0x137b5d8: r0 = CancelOrderConfirmBottomSheet()
    //     0x137b5d8: bl              #0x8b1964  ; AllocateCancelOrderConfirmBottomSheetStub -> CancelOrderConfirmBottomSheet (size=0x34)
    // 0x137b5dc: mov             x3, x0
    // 0x137b5e0: r0 = "Are you sure want to remove this item\?"
    //     0x137b5e0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7c0] "Are you sure want to remove this item\?"
    //     0x137b5e4: ldr             x0, [x0, #0x7c0]
    // 0x137b5e8: stur            x3, [fp, #-0x10]
    // 0x137b5ec: StoreField: r3->field_f = r0
    //     0x137b5ec: stur            w0, [x3, #0xf]
    // 0x137b5f0: r0 = "Remove item"
    //     0x137b5f0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7c8] "Remove item"
    //     0x137b5f4: ldr             x0, [x0, #0x7c8]
    // 0x137b5f8: StoreField: r3->field_13 = r0
    //     0x137b5f8: stur            w0, [x3, #0x13]
    // 0x137b5fc: r0 = "Remove"
    //     0x137b5fc: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0x137b600: ldr             x0, [x0, #0x7d0]
    // 0x137b604: ArrayStore: r3[0] = r0  ; List_4
    //     0x137b604: stur            w0, [x3, #0x17]
    // 0x137b608: ldur            x2, [fp, #-8]
    // 0x137b60c: r1 = Function '<anonymous closure>':.
    //     0x137b60c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b18] AnonymousClosure: (0x135a814), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x137b610: ldr             x1, [x1, #0xb18]
    // 0x137b614: r0 = AllocateClosure()
    //     0x137b614: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b618: mov             x1, x0
    // 0x137b61c: ldur            x0, [fp, #-0x10]
    // 0x137b620: StoreField: r0->field_1b = r1
    //     0x137b620: stur            w1, [x0, #0x1b]
    // 0x137b624: ldur            x1, [fp, #-0x20]
    // 0x137b628: StoreField: r0->field_1f = r1
    //     0x137b628: stur            w1, [x0, #0x1f]
    // 0x137b62c: ldur            x1, [fp, #-0x28]
    // 0x137b630: StoreField: r0->field_23 = r1
    //     0x137b630: stur            w1, [x0, #0x23]
    // 0x137b634: ldur            x1, [fp, #-0x18]
    // 0x137b638: StoreField: r0->field_27 = r1
    //     0x137b638: stur            w1, [x0, #0x27]
    // 0x137b63c: r1 = "Go, Back"
    //     0x137b63c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fe08] "Go, Back"
    //     0x137b640: ldr             x1, [x1, #0xe08]
    // 0x137b644: StoreField: r0->field_2b = r1
    //     0x137b644: stur            w1, [x0, #0x2b]
    // 0x137b648: LeaveFrame
    //     0x137b648: mov             SP, fp
    //     0x137b64c: ldp             fp, lr, [SP], #0x10
    // 0x137b650: ret
    //     0x137b650: ret             
    // 0x137b654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137b654: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137b658: b               #0x137b420
  }
  [closure] Null <anonymous closure>(dynamic, int?, Catalogue?) {
    // ** addr: 0x137b65c, size: 0xa8
    // 0x137b65c: EnterFrame
    //     0x137b65c: stp             fp, lr, [SP, #-0x10]!
    //     0x137b660: mov             fp, SP
    // 0x137b664: AllocStack(0x38)
    //     0x137b664: sub             SP, SP, #0x38
    // 0x137b668: SetupParameters()
    //     0x137b668: ldr             x0, [fp, #0x20]
    //     0x137b66c: ldur            w1, [x0, #0x17]
    //     0x137b670: add             x1, x1, HEAP, lsl #32
    //     0x137b674: stur            x1, [fp, #-8]
    // 0x137b678: CheckStackOverflow
    //     0x137b678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137b67c: cmp             SP, x16
    //     0x137b680: b.ls            #0x137b6fc
    // 0x137b684: r1 = 2
    //     0x137b684: movz            x1, #0x2
    // 0x137b688: r0 = AllocateContext()
    //     0x137b688: bl              #0x16f6108  ; AllocateContextStub
    // 0x137b68c: mov             x1, x0
    // 0x137b690: ldur            x0, [fp, #-8]
    // 0x137b694: StoreField: r1->field_b = r0
    //     0x137b694: stur            w0, [x1, #0xb]
    // 0x137b698: ldr             x2, [fp, #0x18]
    // 0x137b69c: StoreField: r1->field_f = r2
    //     0x137b69c: stur            w2, [x1, #0xf]
    // 0x137b6a0: ldr             x2, [fp, #0x10]
    // 0x137b6a4: StoreField: r1->field_13 = r2
    //     0x137b6a4: stur            w2, [x1, #0x13]
    // 0x137b6a8: LoadField: r3 = r0->field_13
    //     0x137b6a8: ldur            w3, [x0, #0x13]
    // 0x137b6ac: DecompressPointer r3
    //     0x137b6ac: add             x3, x3, HEAP, lsl #32
    // 0x137b6b0: mov             x2, x1
    // 0x137b6b4: stur            x3, [fp, #-0x10]
    // 0x137b6b8: r1 = Function '<anonymous closure>':.
    //     0x137b6b8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b20] AnonymousClosure: (0x137b704), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x137b6bc: ldr             x1, [x1, #0xb20]
    // 0x137b6c0: r0 = AllocateClosure()
    //     0x137b6c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b6c4: stp             x0, NULL, [SP, #0x18]
    // 0x137b6c8: ldur            x16, [fp, #-0x10]
    // 0x137b6cc: r30 = Instance_RoundedRectangleBorder
    //     0x137b6cc: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x137b6d0: ldr             lr, [lr, #0xc78]
    // 0x137b6d4: stp             lr, x16, [SP, #8]
    // 0x137b6d8: r16 = true
    //     0x137b6d8: add             x16, NULL, #0x20  ; true
    // 0x137b6dc: str             x16, [SP]
    // 0x137b6e0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x137b6e0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x137b6e4: ldr             x4, [x4, #0xd70]
    // 0x137b6e8: r0 = showModalBottomSheet()
    //     0x137b6e8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x137b6ec: r0 = Null
    //     0x137b6ec: mov             x0, NULL
    // 0x137b6f0: LeaveFrame
    //     0x137b6f0: mov             SP, fp
    //     0x137b6f4: ldp             fp, lr, [SP], #0x10
    // 0x137b6f8: ret
    //     0x137b6f8: ret             
    // 0x137b6fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137b6fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137b700: b               #0x137b684
  }
  [closure] BagBottomSheetToChoose <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x137b704, size: 0xbc
    // 0x137b704: EnterFrame
    //     0x137b704: stp             fp, lr, [SP, #-0x10]!
    //     0x137b708: mov             fp, SP
    // 0x137b70c: AllocStack(0x28)
    //     0x137b70c: sub             SP, SP, #0x28
    // 0x137b710: SetupParameters()
    //     0x137b710: ldr             x0, [fp, #0x18]
    //     0x137b714: ldur            w2, [x0, #0x17]
    //     0x137b718: add             x2, x2, HEAP, lsl #32
    //     0x137b71c: stur            x2, [fp, #-0x20]
    // 0x137b720: LoadField: r0 = r2->field_f
    //     0x137b720: ldur            w0, [x2, #0xf]
    // 0x137b724: DecompressPointer r0
    //     0x137b724: add             x0, x0, HEAP, lsl #32
    // 0x137b728: stur            x0, [fp, #-0x18]
    // 0x137b72c: cmp             w0, NULL
    // 0x137b730: b.ne            #0x137b73c
    // 0x137b734: r1 = 1
    //     0x137b734: movz            x1, #0x1
    // 0x137b738: b               #0x137b748
    // 0x137b73c: r1 = LoadInt32Instr(r0)
    //     0x137b73c: sbfx            x1, x0, #1, #0x1f
    //     0x137b740: tbz             w0, #0, #0x137b748
    //     0x137b744: ldur            x1, [x0, #7]
    // 0x137b748: stur            x1, [fp, #-0x10]
    // 0x137b74c: LoadField: r3 = r2->field_13
    //     0x137b74c: ldur            w3, [x2, #0x13]
    // 0x137b750: DecompressPointer r3
    //     0x137b750: add             x3, x3, HEAP, lsl #32
    // 0x137b754: stur            x3, [fp, #-8]
    // 0x137b758: r0 = BagBottomSheetToChoose()
    //     0x137b758: bl              #0x137b7c0  ; AllocateBagBottomSheetToChooseStub -> BagBottomSheetToChoose (size=0x24)
    // 0x137b75c: mov             x3, x0
    // 0x137b760: ldur            x0, [fp, #-0x10]
    // 0x137b764: stur            x3, [fp, #-0x28]
    // 0x137b768: StoreField: r3->field_b = r0
    //     0x137b768: stur            x0, [x3, #0xb]
    // 0x137b76c: ldur            x0, [fp, #-0x18]
    // 0x137b770: StoreField: r3->field_13 = r0
    //     0x137b770: stur            w0, [x3, #0x13]
    // 0x137b774: ldur            x0, [fp, #-8]
    // 0x137b778: ArrayStore: r3[0] = r0  ; List_4
    //     0x137b778: stur            w0, [x3, #0x17]
    // 0x137b77c: ldur            x2, [fp, #-0x20]
    // 0x137b780: r1 = Function '<anonymous closure>':.
    //     0x137b780: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b28] AnonymousClosure: (0x1359d88), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x137b784: ldr             x1, [x1, #0xb28]
    // 0x137b788: r0 = AllocateClosure()
    //     0x137b788: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b78c: mov             x1, x0
    // 0x137b790: ldur            x0, [fp, #-0x28]
    // 0x137b794: StoreField: r0->field_1b = r1
    //     0x137b794: stur            w1, [x0, #0x1b]
    // 0x137b798: ldur            x2, [fp, #-0x20]
    // 0x137b79c: r1 = Function '<anonymous closure>':.
    //     0x137b79c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b30] AnonymousClosure: (0x1359d30), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x137b7a0: ldr             x1, [x1, #0xb30]
    // 0x137b7a4: r0 = AllocateClosure()
    //     0x137b7a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137b7a8: mov             x1, x0
    // 0x137b7ac: ldur            x0, [fp, #-0x28]
    // 0x137b7b0: StoreField: r0->field_1f = r1
    //     0x137b7b0: stur            w1, [x0, #0x1f]
    // 0x137b7b4: LeaveFrame
    //     0x137b7b4: mov             SP, fp
    //     0x137b7b8: ldp             fp, lr, [SP], #0x10
    // 0x137b7bc: ret
    //     0x137b7bc: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x137b7cc, size: 0x3e8
    // 0x137b7cc: EnterFrame
    //     0x137b7cc: stp             fp, lr, [SP, #-0x10]!
    //     0x137b7d0: mov             fp, SP
    // 0x137b7d4: AllocStack(0x48)
    //     0x137b7d4: sub             SP, SP, #0x48
    // 0x137b7d8: SetupParameters()
    //     0x137b7d8: ldr             x0, [fp, #0x10]
    //     0x137b7dc: ldur            w2, [x0, #0x17]
    //     0x137b7e0: add             x2, x2, HEAP, lsl #32
    //     0x137b7e4: stur            x2, [fp, #-8]
    // 0x137b7e8: CheckStackOverflow
    //     0x137b7e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137b7ec: cmp             SP, x16
    //     0x137b7f0: b.ls            #0x137bb94
    // 0x137b7f4: LoadField: r1 = r2->field_f
    //     0x137b7f4: ldur            w1, [x2, #0xf]
    // 0x137b7f8: DecompressPointer r1
    //     0x137b7f8: add             x1, x1, HEAP, lsl #32
    // 0x137b7fc: r0 = controller()
    //     0x137b7fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b800: mov             x2, x0
    // 0x137b804: ldur            x0, [fp, #-8]
    // 0x137b808: stur            x2, [fp, #-0x10]
    // 0x137b80c: LoadField: r1 = r0->field_f
    //     0x137b80c: ldur            w1, [x0, #0xf]
    // 0x137b810: DecompressPointer r1
    //     0x137b810: add             x1, x1, HEAP, lsl #32
    // 0x137b814: r0 = controller()
    //     0x137b814: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b818: LoadField: r1 = r0->field_5b
    //     0x137b818: ldur            w1, [x0, #0x5b]
    // 0x137b81c: DecompressPointer r1
    //     0x137b81c: add             x1, x1, HEAP, lsl #32
    // 0x137b820: r0 = value()
    //     0x137b820: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b824: LoadField: r1 = r0->field_b
    //     0x137b824: ldur            w1, [x0, #0xb]
    // 0x137b828: DecompressPointer r1
    //     0x137b828: add             x1, x1, HEAP, lsl #32
    // 0x137b82c: cmp             w1, NULL
    // 0x137b830: b.ne            #0x137b83c
    // 0x137b834: r0 = Null
    //     0x137b834: mov             x0, NULL
    // 0x137b838: b               #0x137b860
    // 0x137b83c: LoadField: r0 = r1->field_2f
    //     0x137b83c: ldur            w0, [x1, #0x2f]
    // 0x137b840: DecompressPointer r0
    //     0x137b840: add             x0, x0, HEAP, lsl #32
    // 0x137b844: cmp             w0, NULL
    // 0x137b848: b.ne            #0x137b854
    // 0x137b84c: r0 = Null
    //     0x137b84c: mov             x0, NULL
    // 0x137b850: b               #0x137b860
    // 0x137b854: LoadField: r1 = r0->field_23
    //     0x137b854: ldur            w1, [x0, #0x23]
    // 0x137b858: DecompressPointer r1
    //     0x137b858: add             x1, x1, HEAP, lsl #32
    // 0x137b85c: mov             x0, x1
    // 0x137b860: cmp             w0, NULL
    // 0x137b864: b.ne            #0x137b86c
    // 0x137b868: r0 = ""
    //     0x137b868: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137b86c: ldur            x2, [fp, #-8]
    // 0x137b870: ldur            x1, [fp, #-0x10]
    // 0x137b874: StoreField: r1->field_67 = r0
    //     0x137b874: stur            w0, [x1, #0x67]
    //     0x137b878: ldurb           w16, [x1, #-1]
    //     0x137b87c: ldurb           w17, [x0, #-1]
    //     0x137b880: and             x16, x17, x16, lsr #2
    //     0x137b884: tst             x16, HEAP, lsr #32
    //     0x137b888: b.eq            #0x137b890
    //     0x137b88c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x137b890: LoadField: r1 = r2->field_f
    //     0x137b890: ldur            w1, [x2, #0xf]
    // 0x137b894: DecompressPointer r1
    //     0x137b894: add             x1, x1, HEAP, lsl #32
    // 0x137b898: r0 = controller()
    //     0x137b898: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b89c: mov             x1, x0
    // 0x137b8a0: r0 = true
    //     0x137b8a0: add             x0, NULL, #0x20  ; true
    // 0x137b8a4: StoreField: r1->field_93 = r0
    //     0x137b8a4: stur            w0, [x1, #0x93]
    // 0x137b8a8: ldur            x0, [fp, #-8]
    // 0x137b8ac: LoadField: r1 = r0->field_f
    //     0x137b8ac: ldur            w1, [x0, #0xf]
    // 0x137b8b0: DecompressPointer r1
    //     0x137b8b0: add             x1, x1, HEAP, lsl #32
    // 0x137b8b4: r0 = controller()
    //     0x137b8b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b8b8: mov             x1, x0
    // 0x137b8bc: r0 = false
    //     0x137b8bc: add             x0, NULL, #0x30  ; false
    // 0x137b8c0: StoreField: r1->field_8f = r0
    //     0x137b8c0: stur            w0, [x1, #0x8f]
    // 0x137b8c4: ldur            x0, [fp, #-8]
    // 0x137b8c8: LoadField: r1 = r0->field_f
    //     0x137b8c8: ldur            w1, [x0, #0xf]
    // 0x137b8cc: DecompressPointer r1
    //     0x137b8cc: add             x1, x1, HEAP, lsl #32
    // 0x137b8d0: r0 = controller()
    //     0x137b8d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b8d4: mov             x2, x0
    // 0x137b8d8: ldur            x0, [fp, #-8]
    // 0x137b8dc: stur            x2, [fp, #-0x10]
    // 0x137b8e0: LoadField: r1 = r0->field_f
    //     0x137b8e0: ldur            w1, [x0, #0xf]
    // 0x137b8e4: DecompressPointer r1
    //     0x137b8e4: add             x1, x1, HEAP, lsl #32
    // 0x137b8e8: r0 = controller()
    //     0x137b8e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b8ec: LoadField: r2 = r0->field_67
    //     0x137b8ec: ldur            w2, [x0, #0x67]
    // 0x137b8f0: DecompressPointer r2
    //     0x137b8f0: add             x2, x2, HEAP, lsl #32
    // 0x137b8f4: ldur            x1, [fp, #-0x10]
    // 0x137b8f8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x137b8f8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x137b8fc: r0 = getBag()
    //     0x137b8fc: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x137b900: ldur            x0, [fp, #-8]
    // 0x137b904: LoadField: r1 = r0->field_f
    //     0x137b904: ldur            w1, [x0, #0xf]
    // 0x137b908: DecompressPointer r1
    //     0x137b908: add             x1, x1, HEAP, lsl #32
    // 0x137b90c: r0 = controller()
    //     0x137b90c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b910: mov             x2, x0
    // 0x137b914: ldur            x0, [fp, #-8]
    // 0x137b918: stur            x2, [fp, #-0x10]
    // 0x137b91c: LoadField: r1 = r0->field_f
    //     0x137b91c: ldur            w1, [x0, #0xf]
    // 0x137b920: DecompressPointer r1
    //     0x137b920: add             x1, x1, HEAP, lsl #32
    // 0x137b924: r0 = controller()
    //     0x137b924: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b928: LoadField: r1 = r0->field_5b
    //     0x137b928: ldur            w1, [x0, #0x5b]
    // 0x137b92c: DecompressPointer r1
    //     0x137b92c: add             x1, x1, HEAP, lsl #32
    // 0x137b930: r0 = value()
    //     0x137b930: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b934: LoadField: r1 = r0->field_b
    //     0x137b934: ldur            w1, [x0, #0xb]
    // 0x137b938: DecompressPointer r1
    //     0x137b938: add             x1, x1, HEAP, lsl #32
    // 0x137b93c: cmp             w1, NULL
    // 0x137b940: b.ne            #0x137b94c
    // 0x137b944: r0 = Null
    //     0x137b944: mov             x0, NULL
    // 0x137b948: b               #0x137b96c
    // 0x137b94c: LoadField: r0 = r1->field_7
    //     0x137b94c: ldur            w0, [x1, #7]
    // 0x137b950: DecompressPointer r0
    //     0x137b950: add             x0, x0, HEAP, lsl #32
    // 0x137b954: cmp             w0, NULL
    // 0x137b958: b.ne            #0x137b964
    // 0x137b95c: r0 = Null
    //     0x137b95c: mov             x0, NULL
    // 0x137b960: b               #0x137b96c
    // 0x137b964: stp             x0, NULL, [SP]
    // 0x137b968: r0 = _Double.fromInteger()
    //     0x137b968: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x137b96c: cmp             w0, NULL
    // 0x137b970: b.ne            #0x137b97c
    // 0x137b974: d0 = 0.000000
    //     0x137b974: eor             v0.16b, v0.16b, v0.16b
    // 0x137b978: b               #0x137b980
    // 0x137b97c: LoadField: d0 = r0->field_7
    //     0x137b97c: ldur            d0, [x0, #7]
    // 0x137b980: ldur            x0, [fp, #-8]
    // 0x137b984: stur            d0, [fp, #-0x30]
    // 0x137b988: LoadField: r1 = r0->field_f
    //     0x137b988: ldur            w1, [x0, #0xf]
    // 0x137b98c: DecompressPointer r1
    //     0x137b98c: add             x1, x1, HEAP, lsl #32
    // 0x137b990: r0 = controller()
    //     0x137b990: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b994: LoadField: r1 = r0->field_5b
    //     0x137b994: ldur            w1, [x0, #0x5b]
    // 0x137b998: DecompressPointer r1
    //     0x137b998: add             x1, x1, HEAP, lsl #32
    // 0x137b99c: r0 = value()
    //     0x137b99c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137b9a0: LoadField: r1 = r0->field_b
    //     0x137b9a0: ldur            w1, [x0, #0xb]
    // 0x137b9a4: DecompressPointer r1
    //     0x137b9a4: add             x1, x1, HEAP, lsl #32
    // 0x137b9a8: cmp             w1, NULL
    // 0x137b9ac: b.ne            #0x137b9b8
    // 0x137b9b0: r0 = Null
    //     0x137b9b0: mov             x0, NULL
    // 0x137b9b4: b               #0x137b9c0
    // 0x137b9b8: LoadField: r0 = r1->field_b
    //     0x137b9b8: ldur            w0, [x1, #0xb]
    // 0x137b9bc: DecompressPointer r0
    //     0x137b9bc: add             x0, x0, HEAP, lsl #32
    // 0x137b9c0: cmp             w0, NULL
    // 0x137b9c4: b.ne            #0x137b9d0
    // 0x137b9c8: r2 = 0
    //     0x137b9c8: movz            x2, #0
    // 0x137b9cc: b               #0x137b9e0
    // 0x137b9d0: r1 = LoadInt32Instr(r0)
    //     0x137b9d0: sbfx            x1, x0, #1, #0x1f
    //     0x137b9d4: tbz             w0, #0, #0x137b9dc
    //     0x137b9d8: ldur            x1, [x0, #7]
    // 0x137b9dc: mov             x2, x1
    // 0x137b9e0: ldur            x0, [fp, #-8]
    // 0x137b9e4: stur            x2, [fp, #-0x18]
    // 0x137b9e8: LoadField: r1 = r0->field_f
    //     0x137b9e8: ldur            w1, [x0, #0xf]
    // 0x137b9ec: DecompressPointer r1
    //     0x137b9ec: add             x1, x1, HEAP, lsl #32
    // 0x137b9f0: r0 = controller()
    //     0x137b9f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137b9f4: LoadField: r1 = r0->field_5b
    //     0x137b9f4: ldur            w1, [x0, #0x5b]
    // 0x137b9f8: DecompressPointer r1
    //     0x137b9f8: add             x1, x1, HEAP, lsl #32
    // 0x137b9fc: r0 = value()
    //     0x137b9fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137ba00: LoadField: r1 = r0->field_b
    //     0x137ba00: ldur            w1, [x0, #0xb]
    // 0x137ba04: DecompressPointer r1
    //     0x137ba04: add             x1, x1, HEAP, lsl #32
    // 0x137ba08: cmp             w1, NULL
    // 0x137ba0c: b.ne            #0x137ba18
    // 0x137ba10: r2 = Null
    //     0x137ba10: mov             x2, NULL
    // 0x137ba14: b               #0x137ba40
    // 0x137ba18: LoadField: r0 = r1->field_2f
    //     0x137ba18: ldur            w0, [x1, #0x2f]
    // 0x137ba1c: DecompressPointer r0
    //     0x137ba1c: add             x0, x0, HEAP, lsl #32
    // 0x137ba20: cmp             w0, NULL
    // 0x137ba24: b.ne            #0x137ba30
    // 0x137ba28: r0 = Null
    //     0x137ba28: mov             x0, NULL
    // 0x137ba2c: b               #0x137ba3c
    // 0x137ba30: LoadField: r1 = r0->field_27
    //     0x137ba30: ldur            w1, [x0, #0x27]
    // 0x137ba34: DecompressPointer r1
    //     0x137ba34: add             x1, x1, HEAP, lsl #32
    // 0x137ba38: mov             x0, x1
    // 0x137ba3c: mov             x2, x0
    // 0x137ba40: ldur            x0, [fp, #-8]
    // 0x137ba44: stur            x2, [fp, #-0x20]
    // 0x137ba48: LoadField: r1 = r0->field_f
    //     0x137ba48: ldur            w1, [x0, #0xf]
    // 0x137ba4c: DecompressPointer r1
    //     0x137ba4c: add             x1, x1, HEAP, lsl #32
    // 0x137ba50: r0 = controller()
    //     0x137ba50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137ba54: LoadField: r1 = r0->field_5b
    //     0x137ba54: ldur            w1, [x0, #0x5b]
    // 0x137ba58: DecompressPointer r1
    //     0x137ba58: add             x1, x1, HEAP, lsl #32
    // 0x137ba5c: r0 = value()
    //     0x137ba5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137ba60: LoadField: r1 = r0->field_b
    //     0x137ba60: ldur            w1, [x0, #0xb]
    // 0x137ba64: DecompressPointer r1
    //     0x137ba64: add             x1, x1, HEAP, lsl #32
    // 0x137ba68: cmp             w1, NULL
    // 0x137ba6c: b.ne            #0x137ba78
    // 0x137ba70: r0 = Null
    //     0x137ba70: mov             x0, NULL
    // 0x137ba74: b               #0x137bac0
    // 0x137ba78: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x137ba78: ldur            w0, [x1, #0x17]
    // 0x137ba7c: DecompressPointer r0
    //     0x137ba7c: add             x0, x0, HEAP, lsl #32
    // 0x137ba80: stur            x0, [fp, #-8]
    // 0x137ba84: r1 = Function '<anonymous closure>':.
    //     0x137ba84: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b38] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x137ba88: ldr             x1, [x1, #0xb38]
    // 0x137ba8c: r2 = Null
    //     0x137ba8c: mov             x2, NULL
    // 0x137ba90: r0 = AllocateClosure()
    //     0x137ba90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137ba94: r16 = <String?>
    //     0x137ba94: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x137ba98: ldur            lr, [fp, #-8]
    // 0x137ba9c: stp             lr, x16, [SP, #8]
    // 0x137baa0: str             x0, [SP]
    // 0x137baa4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x137baa4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x137baa8: r0 = map()
    //     0x137baa8: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x137baac: r16 = ", "
    //     0x137baac: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x137bab0: str             x16, [SP]
    // 0x137bab4: mov             x1, x0
    // 0x137bab8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x137bab8: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x137babc: r0 = join()
    //     0x137babc: bl              #0x784778  ; [dart:_internal] ListIterable::join
    // 0x137bac0: cmp             w0, NULL
    // 0x137bac4: b.ne            #0x137bad0
    // 0x137bac8: r2 = ""
    //     0x137bac8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137bacc: b               #0x137bad4
    // 0x137bad0: mov             x2, x0
    // 0x137bad4: ldur            d0, [fp, #-0x30]
    // 0x137bad8: ldur            x1, [fp, #-0x18]
    // 0x137badc: ldur            x0, [fp, #-0x20]
    // 0x137bae0: stur            x2, [fp, #-8]
    // 0x137bae4: r0 = EventData()
    //     0x137bae4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x137bae8: mov             x2, x0
    // 0x137baec: r0 = "bag_page"
    //     0x137baec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] "bag_page"
    //     0x137baf0: ldr             x0, [x0, #0x68]
    // 0x137baf4: stur            x2, [fp, #-0x28]
    // 0x137baf8: StoreField: r2->field_13 = r0
    //     0x137baf8: stur            w0, [x2, #0x13]
    // 0x137bafc: ldur            d0, [fp, #-0x30]
    // 0x137bb00: r0 = inline_Allocate_Double()
    //     0x137bb00: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x137bb04: add             x0, x0, #0x10
    //     0x137bb08: cmp             x1, x0
    //     0x137bb0c: b.ls            #0x137bb9c
    //     0x137bb10: str             x0, [THR, #0x50]  ; THR::top
    //     0x137bb14: sub             x0, x0, #0xf
    //     0x137bb18: movz            x1, #0xe15c
    //     0x137bb1c: movk            x1, #0x3, lsl #16
    //     0x137bb20: stur            x1, [x0, #-1]
    // 0x137bb24: StoreField: r0->field_7 = d0
    //     0x137bb24: stur            d0, [x0, #7]
    // 0x137bb28: StoreField: r2->field_3f = r0
    //     0x137bb28: stur            w0, [x2, #0x3f]
    // 0x137bb2c: ldur            x3, [fp, #-0x18]
    // 0x137bb30: r0 = BoxInt64Instr(r3)
    //     0x137bb30: sbfiz           x0, x3, #1, #0x1f
    //     0x137bb34: cmp             x3, x0, asr #1
    //     0x137bb38: b.eq            #0x137bb44
    //     0x137bb3c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x137bb40: stur            x3, [x0, #7]
    // 0x137bb44: StoreField: r2->field_43 = r0
    //     0x137bb44: stur            w0, [x2, #0x43]
    // 0x137bb48: ldur            x0, [fp, #-8]
    // 0x137bb4c: StoreField: r2->field_47 = r0
    //     0x137bb4c: stur            w0, [x2, #0x47]
    // 0x137bb50: ldur            x0, [fp, #-0x20]
    // 0x137bb54: r17 = 283
    //     0x137bb54: movz            x17, #0x11b
    // 0x137bb58: str             w0, [x2, x17]
    // 0x137bb5c: r0 = EventsRequest()
    //     0x137bb5c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x137bb60: mov             x1, x0
    // 0x137bb64: r0 = "free_gift_removed"
    //     0x137bb64: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e800] "free_gift_removed"
    //     0x137bb68: ldr             x0, [x0, #0x800]
    // 0x137bb6c: StoreField: r1->field_7 = r0
    //     0x137bb6c: stur            w0, [x1, #7]
    // 0x137bb70: ldur            x0, [fp, #-0x28]
    // 0x137bb74: StoreField: r1->field_b = r0
    //     0x137bb74: stur            w0, [x1, #0xb]
    // 0x137bb78: mov             x2, x1
    // 0x137bb7c: ldur            x1, [fp, #-0x10]
    // 0x137bb80: r0 = postEvents()
    //     0x137bb80: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x137bb84: r0 = Null
    //     0x137bb84: mov             x0, NULL
    // 0x137bb88: LeaveFrame
    //     0x137bb88: mov             SP, fp
    //     0x137bb8c: ldp             fp, lr, [SP], #0x10
    // 0x137bb90: ret
    //     0x137bb90: ret             
    // 0x137bb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137bb94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137bb98: b               #0x137b7f4
    // 0x137bb9c: SaveReg d0
    //     0x137bb9c: str             q0, [SP, #-0x10]!
    // 0x137bba0: SaveReg r2
    //     0x137bba0: str             x2, [SP, #-8]!
    // 0x137bba4: r0 = AllocateDouble()
    //     0x137bba4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x137bba8: RestoreReg r2
    //     0x137bba8: ldr             x2, [SP], #8
    // 0x137bbac: RestoreReg d0
    //     0x137bbac: ldr             q0, [SP], #0x10
    // 0x137bbb0: b               #0x137bb24
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x137bbb4, size: 0x3e8
    // 0x137bbb4: EnterFrame
    //     0x137bbb4: stp             fp, lr, [SP, #-0x10]!
    //     0x137bbb8: mov             fp, SP
    // 0x137bbbc: AllocStack(0x48)
    //     0x137bbbc: sub             SP, SP, #0x48
    // 0x137bbc0: SetupParameters()
    //     0x137bbc0: ldr             x0, [fp, #0x10]
    //     0x137bbc4: ldur            w2, [x0, #0x17]
    //     0x137bbc8: add             x2, x2, HEAP, lsl #32
    //     0x137bbcc: stur            x2, [fp, #-8]
    // 0x137bbd0: CheckStackOverflow
    //     0x137bbd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x137bbd4: cmp             SP, x16
    //     0x137bbd8: b.ls            #0x137bf7c
    // 0x137bbdc: LoadField: r1 = r2->field_f
    //     0x137bbdc: ldur            w1, [x2, #0xf]
    // 0x137bbe0: DecompressPointer r1
    //     0x137bbe0: add             x1, x1, HEAP, lsl #32
    // 0x137bbe4: r0 = controller()
    //     0x137bbe4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bbe8: mov             x2, x0
    // 0x137bbec: ldur            x0, [fp, #-8]
    // 0x137bbf0: stur            x2, [fp, #-0x10]
    // 0x137bbf4: LoadField: r1 = r0->field_f
    //     0x137bbf4: ldur            w1, [x0, #0xf]
    // 0x137bbf8: DecompressPointer r1
    //     0x137bbf8: add             x1, x1, HEAP, lsl #32
    // 0x137bbfc: r0 = controller()
    //     0x137bbfc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bc00: LoadField: r1 = r0->field_5b
    //     0x137bc00: ldur            w1, [x0, #0x5b]
    // 0x137bc04: DecompressPointer r1
    //     0x137bc04: add             x1, x1, HEAP, lsl #32
    // 0x137bc08: r0 = value()
    //     0x137bc08: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137bc0c: LoadField: r1 = r0->field_b
    //     0x137bc0c: ldur            w1, [x0, #0xb]
    // 0x137bc10: DecompressPointer r1
    //     0x137bc10: add             x1, x1, HEAP, lsl #32
    // 0x137bc14: cmp             w1, NULL
    // 0x137bc18: b.ne            #0x137bc24
    // 0x137bc1c: r0 = Null
    //     0x137bc1c: mov             x0, NULL
    // 0x137bc20: b               #0x137bc48
    // 0x137bc24: LoadField: r0 = r1->field_2f
    //     0x137bc24: ldur            w0, [x1, #0x2f]
    // 0x137bc28: DecompressPointer r0
    //     0x137bc28: add             x0, x0, HEAP, lsl #32
    // 0x137bc2c: cmp             w0, NULL
    // 0x137bc30: b.ne            #0x137bc3c
    // 0x137bc34: r0 = Null
    //     0x137bc34: mov             x0, NULL
    // 0x137bc38: b               #0x137bc48
    // 0x137bc3c: LoadField: r1 = r0->field_23
    //     0x137bc3c: ldur            w1, [x0, #0x23]
    // 0x137bc40: DecompressPointer r1
    //     0x137bc40: add             x1, x1, HEAP, lsl #32
    // 0x137bc44: mov             x0, x1
    // 0x137bc48: cmp             w0, NULL
    // 0x137bc4c: b.ne            #0x137bc54
    // 0x137bc50: r0 = ""
    //     0x137bc50: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137bc54: ldur            x2, [fp, #-8]
    // 0x137bc58: ldur            x1, [fp, #-0x10]
    // 0x137bc5c: StoreField: r1->field_67 = r0
    //     0x137bc5c: stur            w0, [x1, #0x67]
    //     0x137bc60: ldurb           w16, [x1, #-1]
    //     0x137bc64: ldurb           w17, [x0, #-1]
    //     0x137bc68: and             x16, x17, x16, lsr #2
    //     0x137bc6c: tst             x16, HEAP, lsr #32
    //     0x137bc70: b.eq            #0x137bc78
    //     0x137bc74: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x137bc78: LoadField: r1 = r2->field_f
    //     0x137bc78: ldur            w1, [x2, #0xf]
    // 0x137bc7c: DecompressPointer r1
    //     0x137bc7c: add             x1, x1, HEAP, lsl #32
    // 0x137bc80: r0 = controller()
    //     0x137bc80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bc84: mov             x1, x0
    // 0x137bc88: r0 = true
    //     0x137bc88: add             x0, NULL, #0x20  ; true
    // 0x137bc8c: StoreField: r1->field_8f = r0
    //     0x137bc8c: stur            w0, [x1, #0x8f]
    // 0x137bc90: ldur            x0, [fp, #-8]
    // 0x137bc94: LoadField: r1 = r0->field_f
    //     0x137bc94: ldur            w1, [x0, #0xf]
    // 0x137bc98: DecompressPointer r1
    //     0x137bc98: add             x1, x1, HEAP, lsl #32
    // 0x137bc9c: r0 = controller()
    //     0x137bc9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bca0: mov             x1, x0
    // 0x137bca4: r0 = false
    //     0x137bca4: add             x0, NULL, #0x30  ; false
    // 0x137bca8: StoreField: r1->field_93 = r0
    //     0x137bca8: stur            w0, [x1, #0x93]
    // 0x137bcac: ldur            x0, [fp, #-8]
    // 0x137bcb0: LoadField: r1 = r0->field_f
    //     0x137bcb0: ldur            w1, [x0, #0xf]
    // 0x137bcb4: DecompressPointer r1
    //     0x137bcb4: add             x1, x1, HEAP, lsl #32
    // 0x137bcb8: r0 = controller()
    //     0x137bcb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bcbc: mov             x2, x0
    // 0x137bcc0: ldur            x0, [fp, #-8]
    // 0x137bcc4: stur            x2, [fp, #-0x10]
    // 0x137bcc8: LoadField: r1 = r0->field_f
    //     0x137bcc8: ldur            w1, [x0, #0xf]
    // 0x137bccc: DecompressPointer r1
    //     0x137bccc: add             x1, x1, HEAP, lsl #32
    // 0x137bcd0: r0 = controller()
    //     0x137bcd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bcd4: LoadField: r2 = r0->field_67
    //     0x137bcd4: ldur            w2, [x0, #0x67]
    // 0x137bcd8: DecompressPointer r2
    //     0x137bcd8: add             x2, x2, HEAP, lsl #32
    // 0x137bcdc: ldur            x1, [fp, #-0x10]
    // 0x137bce0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x137bce0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x137bce4: r0 = getBag()
    //     0x137bce4: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x137bce8: ldur            x0, [fp, #-8]
    // 0x137bcec: LoadField: r1 = r0->field_f
    //     0x137bcec: ldur            w1, [x0, #0xf]
    // 0x137bcf0: DecompressPointer r1
    //     0x137bcf0: add             x1, x1, HEAP, lsl #32
    // 0x137bcf4: r0 = controller()
    //     0x137bcf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bcf8: mov             x2, x0
    // 0x137bcfc: ldur            x0, [fp, #-8]
    // 0x137bd00: stur            x2, [fp, #-0x10]
    // 0x137bd04: LoadField: r1 = r0->field_f
    //     0x137bd04: ldur            w1, [x0, #0xf]
    // 0x137bd08: DecompressPointer r1
    //     0x137bd08: add             x1, x1, HEAP, lsl #32
    // 0x137bd0c: r0 = controller()
    //     0x137bd0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bd10: LoadField: r1 = r0->field_5b
    //     0x137bd10: ldur            w1, [x0, #0x5b]
    // 0x137bd14: DecompressPointer r1
    //     0x137bd14: add             x1, x1, HEAP, lsl #32
    // 0x137bd18: r0 = value()
    //     0x137bd18: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137bd1c: LoadField: r1 = r0->field_b
    //     0x137bd1c: ldur            w1, [x0, #0xb]
    // 0x137bd20: DecompressPointer r1
    //     0x137bd20: add             x1, x1, HEAP, lsl #32
    // 0x137bd24: cmp             w1, NULL
    // 0x137bd28: b.ne            #0x137bd34
    // 0x137bd2c: r0 = Null
    //     0x137bd2c: mov             x0, NULL
    // 0x137bd30: b               #0x137bd54
    // 0x137bd34: LoadField: r0 = r1->field_7
    //     0x137bd34: ldur            w0, [x1, #7]
    // 0x137bd38: DecompressPointer r0
    //     0x137bd38: add             x0, x0, HEAP, lsl #32
    // 0x137bd3c: cmp             w0, NULL
    // 0x137bd40: b.ne            #0x137bd4c
    // 0x137bd44: r0 = Null
    //     0x137bd44: mov             x0, NULL
    // 0x137bd48: b               #0x137bd54
    // 0x137bd4c: stp             x0, NULL, [SP]
    // 0x137bd50: r0 = _Double.fromInteger()
    //     0x137bd50: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x137bd54: cmp             w0, NULL
    // 0x137bd58: b.ne            #0x137bd64
    // 0x137bd5c: d0 = 0.000000
    //     0x137bd5c: eor             v0.16b, v0.16b, v0.16b
    // 0x137bd60: b               #0x137bd68
    // 0x137bd64: LoadField: d0 = r0->field_7
    //     0x137bd64: ldur            d0, [x0, #7]
    // 0x137bd68: ldur            x0, [fp, #-8]
    // 0x137bd6c: stur            d0, [fp, #-0x30]
    // 0x137bd70: LoadField: r1 = r0->field_f
    //     0x137bd70: ldur            w1, [x0, #0xf]
    // 0x137bd74: DecompressPointer r1
    //     0x137bd74: add             x1, x1, HEAP, lsl #32
    // 0x137bd78: r0 = controller()
    //     0x137bd78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bd7c: LoadField: r1 = r0->field_5b
    //     0x137bd7c: ldur            w1, [x0, #0x5b]
    // 0x137bd80: DecompressPointer r1
    //     0x137bd80: add             x1, x1, HEAP, lsl #32
    // 0x137bd84: r0 = value()
    //     0x137bd84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137bd88: LoadField: r1 = r0->field_b
    //     0x137bd88: ldur            w1, [x0, #0xb]
    // 0x137bd8c: DecompressPointer r1
    //     0x137bd8c: add             x1, x1, HEAP, lsl #32
    // 0x137bd90: cmp             w1, NULL
    // 0x137bd94: b.ne            #0x137bda0
    // 0x137bd98: r0 = Null
    //     0x137bd98: mov             x0, NULL
    // 0x137bd9c: b               #0x137bda8
    // 0x137bda0: LoadField: r0 = r1->field_b
    //     0x137bda0: ldur            w0, [x1, #0xb]
    // 0x137bda4: DecompressPointer r0
    //     0x137bda4: add             x0, x0, HEAP, lsl #32
    // 0x137bda8: cmp             w0, NULL
    // 0x137bdac: b.ne            #0x137bdb8
    // 0x137bdb0: r2 = 0
    //     0x137bdb0: movz            x2, #0
    // 0x137bdb4: b               #0x137bdc8
    // 0x137bdb8: r1 = LoadInt32Instr(r0)
    //     0x137bdb8: sbfx            x1, x0, #1, #0x1f
    //     0x137bdbc: tbz             w0, #0, #0x137bdc4
    //     0x137bdc0: ldur            x1, [x0, #7]
    // 0x137bdc4: mov             x2, x1
    // 0x137bdc8: ldur            x0, [fp, #-8]
    // 0x137bdcc: stur            x2, [fp, #-0x18]
    // 0x137bdd0: LoadField: r1 = r0->field_f
    //     0x137bdd0: ldur            w1, [x0, #0xf]
    // 0x137bdd4: DecompressPointer r1
    //     0x137bdd4: add             x1, x1, HEAP, lsl #32
    // 0x137bdd8: r0 = controller()
    //     0x137bdd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137bddc: LoadField: r1 = r0->field_5b
    //     0x137bddc: ldur            w1, [x0, #0x5b]
    // 0x137bde0: DecompressPointer r1
    //     0x137bde0: add             x1, x1, HEAP, lsl #32
    // 0x137bde4: r0 = value()
    //     0x137bde4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137bde8: LoadField: r1 = r0->field_b
    //     0x137bde8: ldur            w1, [x0, #0xb]
    // 0x137bdec: DecompressPointer r1
    //     0x137bdec: add             x1, x1, HEAP, lsl #32
    // 0x137bdf0: cmp             w1, NULL
    // 0x137bdf4: b.ne            #0x137be00
    // 0x137bdf8: r2 = Null
    //     0x137bdf8: mov             x2, NULL
    // 0x137bdfc: b               #0x137be28
    // 0x137be00: LoadField: r0 = r1->field_2f
    //     0x137be00: ldur            w0, [x1, #0x2f]
    // 0x137be04: DecompressPointer r0
    //     0x137be04: add             x0, x0, HEAP, lsl #32
    // 0x137be08: cmp             w0, NULL
    // 0x137be0c: b.ne            #0x137be18
    // 0x137be10: r0 = Null
    //     0x137be10: mov             x0, NULL
    // 0x137be14: b               #0x137be24
    // 0x137be18: LoadField: r1 = r0->field_27
    //     0x137be18: ldur            w1, [x0, #0x27]
    // 0x137be1c: DecompressPointer r1
    //     0x137be1c: add             x1, x1, HEAP, lsl #32
    // 0x137be20: mov             x0, x1
    // 0x137be24: mov             x2, x0
    // 0x137be28: ldur            x0, [fp, #-8]
    // 0x137be2c: stur            x2, [fp, #-0x20]
    // 0x137be30: LoadField: r1 = r0->field_f
    //     0x137be30: ldur            w1, [x0, #0xf]
    // 0x137be34: DecompressPointer r1
    //     0x137be34: add             x1, x1, HEAP, lsl #32
    // 0x137be38: r0 = controller()
    //     0x137be38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x137be3c: LoadField: r1 = r0->field_5b
    //     0x137be3c: ldur            w1, [x0, #0x5b]
    // 0x137be40: DecompressPointer r1
    //     0x137be40: add             x1, x1, HEAP, lsl #32
    // 0x137be44: r0 = value()
    //     0x137be44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x137be48: LoadField: r1 = r0->field_b
    //     0x137be48: ldur            w1, [x0, #0xb]
    // 0x137be4c: DecompressPointer r1
    //     0x137be4c: add             x1, x1, HEAP, lsl #32
    // 0x137be50: cmp             w1, NULL
    // 0x137be54: b.ne            #0x137be60
    // 0x137be58: r0 = Null
    //     0x137be58: mov             x0, NULL
    // 0x137be5c: b               #0x137bea8
    // 0x137be60: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x137be60: ldur            w0, [x1, #0x17]
    // 0x137be64: DecompressPointer r0
    //     0x137be64: add             x0, x0, HEAP, lsl #32
    // 0x137be68: stur            x0, [fp, #-8]
    // 0x137be6c: r1 = Function '<anonymous closure>':.
    //     0x137be6c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b40] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x137be70: ldr             x1, [x1, #0xb40]
    // 0x137be74: r2 = Null
    //     0x137be74: mov             x2, NULL
    // 0x137be78: r0 = AllocateClosure()
    //     0x137be78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x137be7c: r16 = <String?>
    //     0x137be7c: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x137be80: ldur            lr, [fp, #-8]
    // 0x137be84: stp             lr, x16, [SP, #8]
    // 0x137be88: str             x0, [SP]
    // 0x137be8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x137be8c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x137be90: r0 = map()
    //     0x137be90: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x137be94: r16 = ", "
    //     0x137be94: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x137be98: str             x16, [SP]
    // 0x137be9c: mov             x1, x0
    // 0x137bea0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x137bea0: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x137bea4: r0 = join()
    //     0x137bea4: bl              #0x784778  ; [dart:_internal] ListIterable::join
    // 0x137bea8: cmp             w0, NULL
    // 0x137beac: b.ne            #0x137beb8
    // 0x137beb0: r2 = ""
    //     0x137beb0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x137beb4: b               #0x137bebc
    // 0x137beb8: mov             x2, x0
    // 0x137bebc: ldur            d0, [fp, #-0x30]
    // 0x137bec0: ldur            x1, [fp, #-0x18]
    // 0x137bec4: ldur            x0, [fp, #-0x20]
    // 0x137bec8: stur            x2, [fp, #-8]
    // 0x137becc: r0 = EventData()
    //     0x137becc: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x137bed0: mov             x2, x0
    // 0x137bed4: r0 = "bag_page"
    //     0x137bed4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] "bag_page"
    //     0x137bed8: ldr             x0, [x0, #0x68]
    // 0x137bedc: stur            x2, [fp, #-0x28]
    // 0x137bee0: StoreField: r2->field_13 = r0
    //     0x137bee0: stur            w0, [x2, #0x13]
    // 0x137bee4: ldur            d0, [fp, #-0x30]
    // 0x137bee8: r0 = inline_Allocate_Double()
    //     0x137bee8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x137beec: add             x0, x0, #0x10
    //     0x137bef0: cmp             x1, x0
    //     0x137bef4: b.ls            #0x137bf84
    //     0x137bef8: str             x0, [THR, #0x50]  ; THR::top
    //     0x137befc: sub             x0, x0, #0xf
    //     0x137bf00: movz            x1, #0xe15c
    //     0x137bf04: movk            x1, #0x3, lsl #16
    //     0x137bf08: stur            x1, [x0, #-1]
    // 0x137bf0c: StoreField: r0->field_7 = d0
    //     0x137bf0c: stur            d0, [x0, #7]
    // 0x137bf10: StoreField: r2->field_3f = r0
    //     0x137bf10: stur            w0, [x2, #0x3f]
    // 0x137bf14: ldur            x3, [fp, #-0x18]
    // 0x137bf18: r0 = BoxInt64Instr(r3)
    //     0x137bf18: sbfiz           x0, x3, #1, #0x1f
    //     0x137bf1c: cmp             x3, x0, asr #1
    //     0x137bf20: b.eq            #0x137bf2c
    //     0x137bf24: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x137bf28: stur            x3, [x0, #7]
    // 0x137bf2c: StoreField: r2->field_43 = r0
    //     0x137bf2c: stur            w0, [x2, #0x43]
    // 0x137bf30: ldur            x0, [fp, #-8]
    // 0x137bf34: StoreField: r2->field_47 = r0
    //     0x137bf34: stur            w0, [x2, #0x47]
    // 0x137bf38: ldur            x0, [fp, #-0x20]
    // 0x137bf3c: r17 = 283
    //     0x137bf3c: movz            x17, #0x11b
    // 0x137bf40: str             w0, [x2, x17]
    // 0x137bf44: r0 = EventsRequest()
    //     0x137bf44: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x137bf48: mov             x1, x0
    // 0x137bf4c: r0 = "free_gift_added"
    //     0x137bf4c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e810] "free_gift_added"
    //     0x137bf50: ldr             x0, [x0, #0x810]
    // 0x137bf54: StoreField: r1->field_7 = r0
    //     0x137bf54: stur            w0, [x1, #7]
    // 0x137bf58: ldur            x0, [fp, #-0x28]
    // 0x137bf5c: StoreField: r1->field_b = r0
    //     0x137bf5c: stur            w0, [x1, #0xb]
    // 0x137bf60: mov             x2, x1
    // 0x137bf64: ldur            x1, [fp, #-0x10]
    // 0x137bf68: r0 = postEvents()
    //     0x137bf68: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x137bf6c: r0 = Null
    //     0x137bf6c: mov             x0, NULL
    // 0x137bf70: LeaveFrame
    //     0x137bf70: mov             SP, fp
    //     0x137bf74: ldp             fp, lr, [SP], #0x10
    // 0x137bf78: ret
    //     0x137bf78: ret             
    // 0x137bf7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x137bf7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x137bf80: b               #0x137bbdc
    // 0x137bf84: SaveReg d0
    //     0x137bf84: str             q0, [SP, #-0x10]!
    // 0x137bf88: SaveReg r2
    //     0x137bf88: str             x2, [SP, #-8]!
    // 0x137bf8c: r0 = AllocateDouble()
    //     0x137bf8c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x137bf90: RestoreReg r2
    //     0x137bf90: ldr             x2, [SP], #8
    // 0x137bf94: RestoreReg d0
    //     0x137bf94: ldr             q0, [SP], #0x10
    // 0x137bf98: b               #0x137bf0c
  }
  _ body(/* No info */) {
    // ** addr: 0x14d2e60, size: 0x94
    // 0x14d2e60: EnterFrame
    //     0x14d2e60: stp             fp, lr, [SP, #-0x10]!
    //     0x14d2e64: mov             fp, SP
    // 0x14d2e68: AllocStack(0x18)
    //     0x14d2e68: sub             SP, SP, #0x18
    // 0x14d2e6c: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d2e6c: stur            x1, [fp, #-8]
    //     0x14d2e70: stur            x2, [fp, #-0x10]
    // 0x14d2e74: r1 = 2
    //     0x14d2e74: movz            x1, #0x2
    // 0x14d2e78: r0 = AllocateContext()
    //     0x14d2e78: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d2e7c: mov             x1, x0
    // 0x14d2e80: ldur            x0, [fp, #-8]
    // 0x14d2e84: stur            x1, [fp, #-0x18]
    // 0x14d2e88: StoreField: r1->field_f = r0
    //     0x14d2e88: stur            w0, [x1, #0xf]
    // 0x14d2e8c: ldur            x0, [fp, #-0x10]
    // 0x14d2e90: StoreField: r1->field_13 = r0
    //     0x14d2e90: stur            w0, [x1, #0x13]
    // 0x14d2e94: r0 = Obx()
    //     0x14d2e94: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d2e98: ldur            x2, [fp, #-0x18]
    // 0x14d2e9c: r1 = Function '<anonymous closure>':.
    //     0x14d2e9c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ab8] AnonymousClosure: (0x137afc8), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x14d2ea0: ldr             x1, [x1, #0xab8]
    // 0x14d2ea4: stur            x0, [fp, #-8]
    // 0x14d2ea8: r0 = AllocateClosure()
    //     0x14d2ea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2eac: mov             x1, x0
    // 0x14d2eb0: ldur            x0, [fp, #-8]
    // 0x14d2eb4: StoreField: r0->field_b = r1
    //     0x14d2eb4: stur            w1, [x0, #0xb]
    // 0x14d2eb8: r0 = WillPopScope()
    //     0x14d2eb8: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d2ebc: mov             x3, x0
    // 0x14d2ec0: ldur            x0, [fp, #-8]
    // 0x14d2ec4: stur            x3, [fp, #-0x10]
    // 0x14d2ec8: StoreField: r3->field_b = r0
    //     0x14d2ec8: stur            w0, [x3, #0xb]
    // 0x14d2ecc: ldur            x2, [fp, #-0x18]
    // 0x14d2ed0: r1 = Function '<anonymous closure>':.
    //     0x14d2ed0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ac0] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x14d2ed4: ldr             x1, [x1, #0xac0]
    // 0x14d2ed8: r0 = AllocateClosure()
    //     0x14d2ed8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2edc: mov             x1, x0
    // 0x14d2ee0: ldur            x0, [fp, #-0x10]
    // 0x14d2ee4: StoreField: r0->field_f = r1
    //     0x14d2ee4: stur            w1, [x0, #0xf]
    // 0x14d2ee8: LeaveFrame
    //     0x14d2ee8: mov             SP, fp
    //     0x14d2eec: ldp             fp, lr, [SP], #0x10
    // 0x14d2ef0: ret
    //     0x14d2ef0: ret             
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15cb290, size: 0x42c
    // 0x15cb290: EnterFrame
    //     0x15cb290: stp             fp, lr, [SP, #-0x10]!
    //     0x15cb294: mov             fp, SP
    // 0x15cb298: AllocStack(0x48)
    //     0x15cb298: sub             SP, SP, #0x48
    // 0x15cb29c: SetupParameters()
    //     0x15cb29c: ldr             x0, [fp, #0x10]
    //     0x15cb2a0: ldur            w2, [x0, #0x17]
    //     0x15cb2a4: add             x2, x2, HEAP, lsl #32
    //     0x15cb2a8: stur            x2, [fp, #-8]
    // 0x15cb2ac: CheckStackOverflow
    //     0x15cb2ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cb2b0: cmp             SP, x16
    //     0x15cb2b4: b.ls            #0x15cb6b4
    // 0x15cb2b8: LoadField: r1 = r2->field_f
    //     0x15cb2b8: ldur            w1, [x2, #0xf]
    // 0x15cb2bc: DecompressPointer r1
    //     0x15cb2bc: add             x1, x1, HEAP, lsl #32
    // 0x15cb2c0: r0 = controller()
    //     0x15cb2c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb2c4: LoadField: r1 = r0->field_73
    //     0x15cb2c4: ldur            w1, [x0, #0x73]
    // 0x15cb2c8: DecompressPointer r1
    //     0x15cb2c8: add             x1, x1, HEAP, lsl #32
    // 0x15cb2cc: r0 = value()
    //     0x15cb2cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb2d0: LoadField: r1 = r0->field_3f
    //     0x15cb2d0: ldur            w1, [x0, #0x3f]
    // 0x15cb2d4: DecompressPointer r1
    //     0x15cb2d4: add             x1, x1, HEAP, lsl #32
    // 0x15cb2d8: cmp             w1, NULL
    // 0x15cb2dc: b.ne            #0x15cb2e8
    // 0x15cb2e0: r0 = Null
    //     0x15cb2e0: mov             x0, NULL
    // 0x15cb2e4: b               #0x15cb2f0
    // 0x15cb2e8: LoadField: r0 = r1->field_f
    //     0x15cb2e8: ldur            w0, [x1, #0xf]
    // 0x15cb2ec: DecompressPointer r0
    //     0x15cb2ec: add             x0, x0, HEAP, lsl #32
    // 0x15cb2f0: r1 = LoadClassIdInstr(r0)
    //     0x15cb2f0: ldur            x1, [x0, #-1]
    //     0x15cb2f4: ubfx            x1, x1, #0xc, #0x14
    // 0x15cb2f8: r16 = "image_text"
    //     0x15cb2f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cb2fc: ldr             x16, [x16, #0xa88]
    // 0x15cb300: stp             x16, x0, [SP]
    // 0x15cb304: mov             x0, x1
    // 0x15cb308: mov             lr, x0
    // 0x15cb30c: ldr             lr, [x21, lr, lsl #3]
    // 0x15cb310: blr             lr
    // 0x15cb314: tbnz            w0, #4, #0x15cb320
    // 0x15cb318: r2 = true
    //     0x15cb318: add             x2, NULL, #0x20  ; true
    // 0x15cb31c: b               #0x15cb380
    // 0x15cb320: ldur            x0, [fp, #-8]
    // 0x15cb324: LoadField: r1 = r0->field_f
    //     0x15cb324: ldur            w1, [x0, #0xf]
    // 0x15cb328: DecompressPointer r1
    //     0x15cb328: add             x1, x1, HEAP, lsl #32
    // 0x15cb32c: r0 = controller()
    //     0x15cb32c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb330: LoadField: r1 = r0->field_73
    //     0x15cb330: ldur            w1, [x0, #0x73]
    // 0x15cb334: DecompressPointer r1
    //     0x15cb334: add             x1, x1, HEAP, lsl #32
    // 0x15cb338: r0 = value()
    //     0x15cb338: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb33c: LoadField: r1 = r0->field_3f
    //     0x15cb33c: ldur            w1, [x0, #0x3f]
    // 0x15cb340: DecompressPointer r1
    //     0x15cb340: add             x1, x1, HEAP, lsl #32
    // 0x15cb344: cmp             w1, NULL
    // 0x15cb348: b.ne            #0x15cb354
    // 0x15cb34c: r0 = Null
    //     0x15cb34c: mov             x0, NULL
    // 0x15cb350: b               #0x15cb35c
    // 0x15cb354: LoadField: r0 = r1->field_f
    //     0x15cb354: ldur            w0, [x1, #0xf]
    // 0x15cb358: DecompressPointer r0
    //     0x15cb358: add             x0, x0, HEAP, lsl #32
    // 0x15cb35c: r1 = LoadClassIdInstr(r0)
    //     0x15cb35c: ldur            x1, [x0, #-1]
    //     0x15cb360: ubfx            x1, x1, #0xc, #0x14
    // 0x15cb364: r16 = "image"
    //     0x15cb364: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15cb368: stp             x16, x0, [SP]
    // 0x15cb36c: mov             x0, x1
    // 0x15cb370: mov             lr, x0
    // 0x15cb374: ldr             lr, [x21, lr, lsl #3]
    // 0x15cb378: blr             lr
    // 0x15cb37c: mov             x2, x0
    // 0x15cb380: ldur            x0, [fp, #-8]
    // 0x15cb384: stur            x2, [fp, #-0x10]
    // 0x15cb388: LoadField: r1 = r0->field_f
    //     0x15cb388: ldur            w1, [x0, #0xf]
    // 0x15cb38c: DecompressPointer r1
    //     0x15cb38c: add             x1, x1, HEAP, lsl #32
    // 0x15cb390: r0 = controller()
    //     0x15cb390: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb394: LoadField: r1 = r0->field_73
    //     0x15cb394: ldur            w1, [x0, #0x73]
    // 0x15cb398: DecompressPointer r1
    //     0x15cb398: add             x1, x1, HEAP, lsl #32
    // 0x15cb39c: r0 = value()
    //     0x15cb39c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb3a0: LoadField: r1 = r0->field_27
    //     0x15cb3a0: ldur            w1, [x0, #0x27]
    // 0x15cb3a4: DecompressPointer r1
    //     0x15cb3a4: add             x1, x1, HEAP, lsl #32
    // 0x15cb3a8: cmp             w1, NULL
    // 0x15cb3ac: b.ne            #0x15cb3b8
    // 0x15cb3b0: r2 = ""
    //     0x15cb3b0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cb3b4: b               #0x15cb3bc
    // 0x15cb3b8: mov             x2, x1
    // 0x15cb3bc: ldur            x0, [fp, #-8]
    // 0x15cb3c0: ldur            x1, [fp, #-0x10]
    // 0x15cb3c4: stur            x2, [fp, #-0x18]
    // 0x15cb3c8: r0 = ImageHeaders.forImages()
    //     0x15cb3c8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15cb3cc: stur            x0, [fp, #-0x20]
    // 0x15cb3d0: r0 = CachedNetworkImage()
    //     0x15cb3d0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15cb3d4: stur            x0, [fp, #-0x28]
    // 0x15cb3d8: ldur            x16, [fp, #-0x20]
    // 0x15cb3dc: r30 = Instance_BoxFit
    //     0x15cb3dc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cb3e0: ldr             lr, [lr, #0xb18]
    // 0x15cb3e4: stp             lr, x16, [SP, #0x10]
    // 0x15cb3e8: r16 = 50.000000
    //     0x15cb3e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cb3ec: ldr             x16, [x16, #0xa90]
    // 0x15cb3f0: r30 = 50.000000
    //     0x15cb3f0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cb3f4: ldr             lr, [lr, #0xa90]
    // 0x15cb3f8: stp             lr, x16, [SP]
    // 0x15cb3fc: mov             x1, x0
    // 0x15cb400: ldur            x2, [fp, #-0x18]
    // 0x15cb404: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15cb404: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15cb408: ldr             x4, [x4, #0xa98]
    // 0x15cb40c: r0 = CachedNetworkImage()
    //     0x15cb40c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15cb410: r0 = Visibility()
    //     0x15cb410: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cb414: mov             x2, x0
    // 0x15cb418: ldur            x0, [fp, #-0x28]
    // 0x15cb41c: stur            x2, [fp, #-0x18]
    // 0x15cb420: StoreField: r2->field_b = r0
    //     0x15cb420: stur            w0, [x2, #0xb]
    // 0x15cb424: r0 = Instance_SizedBox
    //     0x15cb424: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cb428: StoreField: r2->field_f = r0
    //     0x15cb428: stur            w0, [x2, #0xf]
    // 0x15cb42c: ldur            x1, [fp, #-0x10]
    // 0x15cb430: StoreField: r2->field_13 = r1
    //     0x15cb430: stur            w1, [x2, #0x13]
    // 0x15cb434: r3 = false
    //     0x15cb434: add             x3, NULL, #0x30  ; false
    // 0x15cb438: ArrayStore: r2[0] = r3  ; List_4
    //     0x15cb438: stur            w3, [x2, #0x17]
    // 0x15cb43c: StoreField: r2->field_1b = r3
    //     0x15cb43c: stur            w3, [x2, #0x1b]
    // 0x15cb440: StoreField: r2->field_1f = r3
    //     0x15cb440: stur            w3, [x2, #0x1f]
    // 0x15cb444: StoreField: r2->field_23 = r3
    //     0x15cb444: stur            w3, [x2, #0x23]
    // 0x15cb448: StoreField: r2->field_27 = r3
    //     0x15cb448: stur            w3, [x2, #0x27]
    // 0x15cb44c: StoreField: r2->field_2b = r3
    //     0x15cb44c: stur            w3, [x2, #0x2b]
    // 0x15cb450: ldur            x4, [fp, #-8]
    // 0x15cb454: LoadField: r1 = r4->field_f
    //     0x15cb454: ldur            w1, [x4, #0xf]
    // 0x15cb458: DecompressPointer r1
    //     0x15cb458: add             x1, x1, HEAP, lsl #32
    // 0x15cb45c: r0 = controller()
    //     0x15cb45c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb460: LoadField: r1 = r0->field_73
    //     0x15cb460: ldur            w1, [x0, #0x73]
    // 0x15cb464: DecompressPointer r1
    //     0x15cb464: add             x1, x1, HEAP, lsl #32
    // 0x15cb468: r0 = value()
    //     0x15cb468: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb46c: LoadField: r1 = r0->field_3f
    //     0x15cb46c: ldur            w1, [x0, #0x3f]
    // 0x15cb470: DecompressPointer r1
    //     0x15cb470: add             x1, x1, HEAP, lsl #32
    // 0x15cb474: cmp             w1, NULL
    // 0x15cb478: b.ne            #0x15cb484
    // 0x15cb47c: r0 = Null
    //     0x15cb47c: mov             x0, NULL
    // 0x15cb480: b               #0x15cb48c
    // 0x15cb484: LoadField: r0 = r1->field_f
    //     0x15cb484: ldur            w0, [x1, #0xf]
    // 0x15cb488: DecompressPointer r0
    //     0x15cb488: add             x0, x0, HEAP, lsl #32
    // 0x15cb48c: r1 = LoadClassIdInstr(r0)
    //     0x15cb48c: ldur            x1, [x0, #-1]
    //     0x15cb490: ubfx            x1, x1, #0xc, #0x14
    // 0x15cb494: r16 = "image_text"
    //     0x15cb494: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cb498: ldr             x16, [x16, #0xa88]
    // 0x15cb49c: stp             x16, x0, [SP]
    // 0x15cb4a0: mov             x0, x1
    // 0x15cb4a4: mov             lr, x0
    // 0x15cb4a8: ldr             lr, [x21, lr, lsl #3]
    // 0x15cb4ac: blr             lr
    // 0x15cb4b0: tbnz            w0, #4, #0x15cb4bc
    // 0x15cb4b4: r2 = true
    //     0x15cb4b4: add             x2, NULL, #0x20  ; true
    // 0x15cb4b8: b               #0x15cb51c
    // 0x15cb4bc: ldur            x0, [fp, #-8]
    // 0x15cb4c0: LoadField: r1 = r0->field_f
    //     0x15cb4c0: ldur            w1, [x0, #0xf]
    // 0x15cb4c4: DecompressPointer r1
    //     0x15cb4c4: add             x1, x1, HEAP, lsl #32
    // 0x15cb4c8: r0 = controller()
    //     0x15cb4c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb4cc: LoadField: r1 = r0->field_73
    //     0x15cb4cc: ldur            w1, [x0, #0x73]
    // 0x15cb4d0: DecompressPointer r1
    //     0x15cb4d0: add             x1, x1, HEAP, lsl #32
    // 0x15cb4d4: r0 = value()
    //     0x15cb4d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb4d8: LoadField: r1 = r0->field_3f
    //     0x15cb4d8: ldur            w1, [x0, #0x3f]
    // 0x15cb4dc: DecompressPointer r1
    //     0x15cb4dc: add             x1, x1, HEAP, lsl #32
    // 0x15cb4e0: cmp             w1, NULL
    // 0x15cb4e4: b.ne            #0x15cb4f0
    // 0x15cb4e8: r0 = Null
    //     0x15cb4e8: mov             x0, NULL
    // 0x15cb4ec: b               #0x15cb4f8
    // 0x15cb4f0: LoadField: r0 = r1->field_f
    //     0x15cb4f0: ldur            w0, [x1, #0xf]
    // 0x15cb4f4: DecompressPointer r0
    //     0x15cb4f4: add             x0, x0, HEAP, lsl #32
    // 0x15cb4f8: r1 = LoadClassIdInstr(r0)
    //     0x15cb4f8: ldur            x1, [x0, #-1]
    //     0x15cb4fc: ubfx            x1, x1, #0xc, #0x14
    // 0x15cb500: r16 = "text"
    //     0x15cb500: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15cb504: stp             x16, x0, [SP]
    // 0x15cb508: mov             x0, x1
    // 0x15cb50c: mov             lr, x0
    // 0x15cb510: ldr             lr, [x21, lr, lsl #3]
    // 0x15cb514: blr             lr
    // 0x15cb518: mov             x2, x0
    // 0x15cb51c: ldur            x0, [fp, #-8]
    // 0x15cb520: stur            x2, [fp, #-0x10]
    // 0x15cb524: LoadField: r1 = r0->field_f
    //     0x15cb524: ldur            w1, [x0, #0xf]
    // 0x15cb528: DecompressPointer r1
    //     0x15cb528: add             x1, x1, HEAP, lsl #32
    // 0x15cb52c: r0 = controller()
    //     0x15cb52c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb530: LoadField: r1 = r0->field_73
    //     0x15cb530: ldur            w1, [x0, #0x73]
    // 0x15cb534: DecompressPointer r1
    //     0x15cb534: add             x1, x1, HEAP, lsl #32
    // 0x15cb538: r0 = value()
    //     0x15cb538: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb53c: LoadField: r1 = r0->field_2b
    //     0x15cb53c: ldur            w1, [x0, #0x2b]
    // 0x15cb540: DecompressPointer r1
    //     0x15cb540: add             x1, x1, HEAP, lsl #32
    // 0x15cb544: cmp             w1, NULL
    // 0x15cb548: b.ne            #0x15cb554
    // 0x15cb54c: r4 = ""
    //     0x15cb54c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cb550: b               #0x15cb558
    // 0x15cb554: mov             x4, x1
    // 0x15cb558: ldur            x0, [fp, #-8]
    // 0x15cb55c: ldur            x3, [fp, #-0x18]
    // 0x15cb560: ldur            x2, [fp, #-0x10]
    // 0x15cb564: stur            x4, [fp, #-0x20]
    // 0x15cb568: LoadField: r1 = r0->field_13
    //     0x15cb568: ldur            w1, [x0, #0x13]
    // 0x15cb56c: DecompressPointer r1
    //     0x15cb56c: add             x1, x1, HEAP, lsl #32
    // 0x15cb570: r0 = of()
    //     0x15cb570: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cb574: LoadField: r1 = r0->field_87
    //     0x15cb574: ldur            w1, [x0, #0x87]
    // 0x15cb578: DecompressPointer r1
    //     0x15cb578: add             x1, x1, HEAP, lsl #32
    // 0x15cb57c: LoadField: r0 = r1->field_2b
    //     0x15cb57c: ldur            w0, [x1, #0x2b]
    // 0x15cb580: DecompressPointer r0
    //     0x15cb580: add             x0, x0, HEAP, lsl #32
    // 0x15cb584: r16 = 16.000000
    //     0x15cb584: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15cb588: ldr             x16, [x16, #0x188]
    // 0x15cb58c: r30 = Instance_Color
    //     0x15cb58c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15cb590: stp             lr, x16, [SP]
    // 0x15cb594: mov             x1, x0
    // 0x15cb598: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15cb598: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15cb59c: ldr             x4, [x4, #0xaa0]
    // 0x15cb5a0: r0 = copyWith()
    //     0x15cb5a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cb5a4: stur            x0, [fp, #-8]
    // 0x15cb5a8: r0 = Text()
    //     0x15cb5a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cb5ac: mov             x1, x0
    // 0x15cb5b0: ldur            x0, [fp, #-0x20]
    // 0x15cb5b4: stur            x1, [fp, #-0x28]
    // 0x15cb5b8: StoreField: r1->field_b = r0
    //     0x15cb5b8: stur            w0, [x1, #0xb]
    // 0x15cb5bc: ldur            x0, [fp, #-8]
    // 0x15cb5c0: StoreField: r1->field_13 = r0
    //     0x15cb5c0: stur            w0, [x1, #0x13]
    // 0x15cb5c4: r0 = Visibility()
    //     0x15cb5c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cb5c8: mov             x3, x0
    // 0x15cb5cc: ldur            x0, [fp, #-0x28]
    // 0x15cb5d0: stur            x3, [fp, #-8]
    // 0x15cb5d4: StoreField: r3->field_b = r0
    //     0x15cb5d4: stur            w0, [x3, #0xb]
    // 0x15cb5d8: r0 = Instance_SizedBox
    //     0x15cb5d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cb5dc: StoreField: r3->field_f = r0
    //     0x15cb5dc: stur            w0, [x3, #0xf]
    // 0x15cb5e0: ldur            x0, [fp, #-0x10]
    // 0x15cb5e4: StoreField: r3->field_13 = r0
    //     0x15cb5e4: stur            w0, [x3, #0x13]
    // 0x15cb5e8: r0 = false
    //     0x15cb5e8: add             x0, NULL, #0x30  ; false
    // 0x15cb5ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x15cb5ec: stur            w0, [x3, #0x17]
    // 0x15cb5f0: StoreField: r3->field_1b = r0
    //     0x15cb5f0: stur            w0, [x3, #0x1b]
    // 0x15cb5f4: StoreField: r3->field_1f = r0
    //     0x15cb5f4: stur            w0, [x3, #0x1f]
    // 0x15cb5f8: StoreField: r3->field_23 = r0
    //     0x15cb5f8: stur            w0, [x3, #0x23]
    // 0x15cb5fc: StoreField: r3->field_27 = r0
    //     0x15cb5fc: stur            w0, [x3, #0x27]
    // 0x15cb600: StoreField: r3->field_2b = r0
    //     0x15cb600: stur            w0, [x3, #0x2b]
    // 0x15cb604: r1 = Null
    //     0x15cb604: mov             x1, NULL
    // 0x15cb608: r2 = 6
    //     0x15cb608: movz            x2, #0x6
    // 0x15cb60c: r0 = AllocateArray()
    //     0x15cb60c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15cb610: mov             x2, x0
    // 0x15cb614: ldur            x0, [fp, #-0x18]
    // 0x15cb618: stur            x2, [fp, #-0x10]
    // 0x15cb61c: StoreField: r2->field_f = r0
    //     0x15cb61c: stur            w0, [x2, #0xf]
    // 0x15cb620: r16 = Instance_SizedBox
    //     0x15cb620: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15cb624: ldr             x16, [x16, #0xaa8]
    // 0x15cb628: StoreField: r2->field_13 = r16
    //     0x15cb628: stur            w16, [x2, #0x13]
    // 0x15cb62c: ldur            x0, [fp, #-8]
    // 0x15cb630: ArrayStore: r2[0] = r0  ; List_4
    //     0x15cb630: stur            w0, [x2, #0x17]
    // 0x15cb634: r1 = <Widget>
    //     0x15cb634: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15cb638: r0 = AllocateGrowableArray()
    //     0x15cb638: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15cb63c: mov             x1, x0
    // 0x15cb640: ldur            x0, [fp, #-0x10]
    // 0x15cb644: stur            x1, [fp, #-8]
    // 0x15cb648: StoreField: r1->field_f = r0
    //     0x15cb648: stur            w0, [x1, #0xf]
    // 0x15cb64c: r0 = 6
    //     0x15cb64c: movz            x0, #0x6
    // 0x15cb650: StoreField: r1->field_b = r0
    //     0x15cb650: stur            w0, [x1, #0xb]
    // 0x15cb654: r0 = Row()
    //     0x15cb654: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15cb658: r1 = Instance_Axis
    //     0x15cb658: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15cb65c: StoreField: r0->field_f = r1
    //     0x15cb65c: stur            w1, [x0, #0xf]
    // 0x15cb660: r1 = Instance_MainAxisAlignment
    //     0x15cb660: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15cb664: ldr             x1, [x1, #0xab0]
    // 0x15cb668: StoreField: r0->field_13 = r1
    //     0x15cb668: stur            w1, [x0, #0x13]
    // 0x15cb66c: r1 = Instance_MainAxisSize
    //     0x15cb66c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15cb670: ldr             x1, [x1, #0xa10]
    // 0x15cb674: ArrayStore: r0[0] = r1  ; List_4
    //     0x15cb674: stur            w1, [x0, #0x17]
    // 0x15cb678: r1 = Instance_CrossAxisAlignment
    //     0x15cb678: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15cb67c: ldr             x1, [x1, #0xa18]
    // 0x15cb680: StoreField: r0->field_1b = r1
    //     0x15cb680: stur            w1, [x0, #0x1b]
    // 0x15cb684: r1 = Instance_VerticalDirection
    //     0x15cb684: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15cb688: ldr             x1, [x1, #0xa20]
    // 0x15cb68c: StoreField: r0->field_23 = r1
    //     0x15cb68c: stur            w1, [x0, #0x23]
    // 0x15cb690: r1 = Instance_Clip
    //     0x15cb690: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15cb694: ldr             x1, [x1, #0x38]
    // 0x15cb698: StoreField: r0->field_2b = r1
    //     0x15cb698: stur            w1, [x0, #0x2b]
    // 0x15cb69c: StoreField: r0->field_2f = rZR
    //     0x15cb69c: stur            xzr, [x0, #0x2f]
    // 0x15cb6a0: ldur            x1, [fp, #-8]
    // 0x15cb6a4: StoreField: r0->field_b = r1
    //     0x15cb6a4: stur            w1, [x0, #0xb]
    // 0x15cb6a8: LeaveFrame
    //     0x15cb6a8: mov             SP, fp
    //     0x15cb6ac: ldp             fp, lr, [SP], #0x10
    // 0x15cb6b0: ret
    //     0x15cb6b0: ret             
    // 0x15cb6b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cb6b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cb6b8: b               #0x15cb2b8
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15df434, size: 0x2b4
    // 0x15df434: EnterFrame
    //     0x15df434: stp             fp, lr, [SP, #-0x10]!
    //     0x15df438: mov             fp, SP
    // 0x15df43c: AllocStack(0x30)
    //     0x15df43c: sub             SP, SP, #0x30
    // 0x15df440: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15df440: stur            x1, [fp, #-8]
    //     0x15df444: stur            x2, [fp, #-0x10]
    // 0x15df448: CheckStackOverflow
    //     0x15df448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15df44c: cmp             SP, x16
    //     0x15df450: b.ls            #0x15df6e0
    // 0x15df454: r1 = 2
    //     0x15df454: movz            x1, #0x2
    // 0x15df458: r0 = AllocateContext()
    //     0x15df458: bl              #0x16f6108  ; AllocateContextStub
    // 0x15df45c: ldur            x1, [fp, #-8]
    // 0x15df460: stur            x0, [fp, #-0x18]
    // 0x15df464: StoreField: r0->field_f = r1
    //     0x15df464: stur            w1, [x0, #0xf]
    // 0x15df468: ldur            x2, [fp, #-0x10]
    // 0x15df46c: StoreField: r0->field_13 = r2
    //     0x15df46c: stur            w2, [x0, #0x13]
    // 0x15df470: r0 = Obx()
    //     0x15df470: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15df474: ldur            x2, [fp, #-0x18]
    // 0x15df478: r1 = Function '<anonymous closure>':.
    //     0x15df478: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b48] AnonymousClosure: (0x15cb290), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::appBar (0x15df434)
    //     0x15df47c: ldr             x1, [x1, #0xb48]
    // 0x15df480: stur            x0, [fp, #-0x10]
    // 0x15df484: r0 = AllocateClosure()
    //     0x15df484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df488: mov             x1, x0
    // 0x15df48c: ldur            x0, [fp, #-0x10]
    // 0x15df490: StoreField: r0->field_b = r1
    //     0x15df490: stur            w1, [x0, #0xb]
    // 0x15df494: ldur            x1, [fp, #-8]
    // 0x15df498: r0 = controller()
    //     0x15df498: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df49c: LoadField: r1 = r0->field_b3
    //     0x15df49c: ldur            w1, [x0, #0xb3]
    // 0x15df4a0: DecompressPointer r1
    //     0x15df4a0: add             x1, x1, HEAP, lsl #32
    // 0x15df4a4: r0 = value()
    //     0x15df4a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df4a8: tbnz            w0, #4, #0x15df540
    // 0x15df4ac: ldur            x2, [fp, #-0x18]
    // 0x15df4b0: LoadField: r1 = r2->field_13
    //     0x15df4b0: ldur            w1, [x2, #0x13]
    // 0x15df4b4: DecompressPointer r1
    //     0x15df4b4: add             x1, x1, HEAP, lsl #32
    // 0x15df4b8: r0 = of()
    //     0x15df4b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df4bc: LoadField: r1 = r0->field_5b
    //     0x15df4bc: ldur            w1, [x0, #0x5b]
    // 0x15df4c0: DecompressPointer r1
    //     0x15df4c0: add             x1, x1, HEAP, lsl #32
    // 0x15df4c4: stur            x1, [fp, #-8]
    // 0x15df4c8: r0 = ColorFilter()
    //     0x15df4c8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15df4cc: mov             x1, x0
    // 0x15df4d0: ldur            x0, [fp, #-8]
    // 0x15df4d4: stur            x1, [fp, #-0x20]
    // 0x15df4d8: StoreField: r1->field_7 = r0
    //     0x15df4d8: stur            w0, [x1, #7]
    // 0x15df4dc: r0 = Instance_BlendMode
    //     0x15df4dc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15df4e0: ldr             x0, [x0, #0xb30]
    // 0x15df4e4: StoreField: r1->field_b = r0
    //     0x15df4e4: stur            w0, [x1, #0xb]
    // 0x15df4e8: r2 = 1
    //     0x15df4e8: movz            x2, #0x1
    // 0x15df4ec: StoreField: r1->field_13 = r2
    //     0x15df4ec: stur            x2, [x1, #0x13]
    // 0x15df4f0: r0 = SvgPicture()
    //     0x15df4f0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15df4f4: stur            x0, [fp, #-8]
    // 0x15df4f8: ldur            x16, [fp, #-0x20]
    // 0x15df4fc: str             x16, [SP]
    // 0x15df500: mov             x1, x0
    // 0x15df504: r2 = "assets/images/search.svg"
    //     0x15df504: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15df508: ldr             x2, [x2, #0xa30]
    // 0x15df50c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15df50c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15df510: ldr             x4, [x4, #0xa38]
    // 0x15df514: r0 = SvgPicture.asset()
    //     0x15df514: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15df518: r0 = Align()
    //     0x15df518: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15df51c: r3 = Instance_Alignment
    //     0x15df51c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15df520: ldr             x3, [x3, #0xb10]
    // 0x15df524: StoreField: r0->field_f = r3
    //     0x15df524: stur            w3, [x0, #0xf]
    // 0x15df528: r4 = 1.000000
    //     0x15df528: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15df52c: StoreField: r0->field_13 = r4
    //     0x15df52c: stur            w4, [x0, #0x13]
    // 0x15df530: ArrayStore: r0[0] = r4  ; List_4
    //     0x15df530: stur            w4, [x0, #0x17]
    // 0x15df534: ldur            x1, [fp, #-8]
    // 0x15df538: StoreField: r0->field_b = r1
    //     0x15df538: stur            w1, [x0, #0xb]
    // 0x15df53c: b               #0x15df5f0
    // 0x15df540: ldur            x5, [fp, #-0x18]
    // 0x15df544: r4 = 1.000000
    //     0x15df544: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15df548: r0 = Instance_BlendMode
    //     0x15df548: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15df54c: ldr             x0, [x0, #0xb30]
    // 0x15df550: r3 = Instance_Alignment
    //     0x15df550: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15df554: ldr             x3, [x3, #0xb10]
    // 0x15df558: r2 = 1
    //     0x15df558: movz            x2, #0x1
    // 0x15df55c: LoadField: r1 = r5->field_13
    //     0x15df55c: ldur            w1, [x5, #0x13]
    // 0x15df560: DecompressPointer r1
    //     0x15df560: add             x1, x1, HEAP, lsl #32
    // 0x15df564: r0 = of()
    //     0x15df564: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df568: LoadField: r1 = r0->field_5b
    //     0x15df568: ldur            w1, [x0, #0x5b]
    // 0x15df56c: DecompressPointer r1
    //     0x15df56c: add             x1, x1, HEAP, lsl #32
    // 0x15df570: stur            x1, [fp, #-8]
    // 0x15df574: r0 = ColorFilter()
    //     0x15df574: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15df578: mov             x1, x0
    // 0x15df57c: ldur            x0, [fp, #-8]
    // 0x15df580: stur            x1, [fp, #-0x20]
    // 0x15df584: StoreField: r1->field_7 = r0
    //     0x15df584: stur            w0, [x1, #7]
    // 0x15df588: r0 = Instance_BlendMode
    //     0x15df588: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15df58c: ldr             x0, [x0, #0xb30]
    // 0x15df590: StoreField: r1->field_b = r0
    //     0x15df590: stur            w0, [x1, #0xb]
    // 0x15df594: r0 = 1
    //     0x15df594: movz            x0, #0x1
    // 0x15df598: StoreField: r1->field_13 = r0
    //     0x15df598: stur            x0, [x1, #0x13]
    // 0x15df59c: r0 = SvgPicture()
    //     0x15df59c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15df5a0: stur            x0, [fp, #-8]
    // 0x15df5a4: ldur            x16, [fp, #-0x20]
    // 0x15df5a8: str             x16, [SP]
    // 0x15df5ac: mov             x1, x0
    // 0x15df5b0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15df5b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15df5b4: ldr             x2, [x2, #0xa40]
    // 0x15df5b8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15df5b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15df5bc: ldr             x4, [x4, #0xa38]
    // 0x15df5c0: r0 = SvgPicture.asset()
    //     0x15df5c0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15df5c4: r0 = Align()
    //     0x15df5c4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15df5c8: mov             x1, x0
    // 0x15df5cc: r0 = Instance_Alignment
    //     0x15df5cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15df5d0: ldr             x0, [x0, #0xb10]
    // 0x15df5d4: StoreField: r1->field_f = r0
    //     0x15df5d4: stur            w0, [x1, #0xf]
    // 0x15df5d8: r0 = 1.000000
    //     0x15df5d8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15df5dc: StoreField: r1->field_13 = r0
    //     0x15df5dc: stur            w0, [x1, #0x13]
    // 0x15df5e0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15df5e0: stur            w0, [x1, #0x17]
    // 0x15df5e4: ldur            x0, [fp, #-8]
    // 0x15df5e8: StoreField: r1->field_b = r0
    //     0x15df5e8: stur            w0, [x1, #0xb]
    // 0x15df5ec: mov             x0, x1
    // 0x15df5f0: stur            x0, [fp, #-8]
    // 0x15df5f4: r0 = InkWell()
    //     0x15df5f4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15df5f8: mov             x3, x0
    // 0x15df5fc: ldur            x0, [fp, #-8]
    // 0x15df600: stur            x3, [fp, #-0x20]
    // 0x15df604: StoreField: r3->field_b = r0
    //     0x15df604: stur            w0, [x3, #0xb]
    // 0x15df608: ldur            x2, [fp, #-0x18]
    // 0x15df60c: r1 = Function '<anonymous closure>':.
    //     0x15df60c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b50] AnonymousClosure: (0x15cb1c4), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::appBar (0x15e64a0)
    //     0x15df610: ldr             x1, [x1, #0xb50]
    // 0x15df614: r0 = AllocateClosure()
    //     0x15df614: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df618: ldur            x2, [fp, #-0x20]
    // 0x15df61c: StoreField: r2->field_f = r0
    //     0x15df61c: stur            w0, [x2, #0xf]
    // 0x15df620: r0 = true
    //     0x15df620: add             x0, NULL, #0x20  ; true
    // 0x15df624: StoreField: r2->field_43 = r0
    //     0x15df624: stur            w0, [x2, #0x43]
    // 0x15df628: r1 = Instance_BoxShape
    //     0x15df628: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15df62c: ldr             x1, [x1, #0x80]
    // 0x15df630: StoreField: r2->field_47 = r1
    //     0x15df630: stur            w1, [x2, #0x47]
    // 0x15df634: StoreField: r2->field_6f = r0
    //     0x15df634: stur            w0, [x2, #0x6f]
    // 0x15df638: r1 = false
    //     0x15df638: add             x1, NULL, #0x30  ; false
    // 0x15df63c: StoreField: r2->field_73 = r1
    //     0x15df63c: stur            w1, [x2, #0x73]
    // 0x15df640: StoreField: r2->field_83 = r0
    //     0x15df640: stur            w0, [x2, #0x83]
    // 0x15df644: StoreField: r2->field_7b = r1
    //     0x15df644: stur            w1, [x2, #0x7b]
    // 0x15df648: r0 = Obx()
    //     0x15df648: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15df64c: ldur            x2, [fp, #-0x18]
    // 0x15df650: r1 = Function '<anonymous closure>':.
    //     0x15df650: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b58] AnonymousClosure: (0x15df6e8), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::appBar (0x15df434)
    //     0x15df654: ldr             x1, [x1, #0xb58]
    // 0x15df658: stur            x0, [fp, #-8]
    // 0x15df65c: r0 = AllocateClosure()
    //     0x15df65c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df660: mov             x1, x0
    // 0x15df664: ldur            x0, [fp, #-8]
    // 0x15df668: StoreField: r0->field_b = r1
    //     0x15df668: stur            w1, [x0, #0xb]
    // 0x15df66c: r1 = Null
    //     0x15df66c: mov             x1, NULL
    // 0x15df670: r2 = 2
    //     0x15df670: movz            x2, #0x2
    // 0x15df674: r0 = AllocateArray()
    //     0x15df674: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15df678: mov             x2, x0
    // 0x15df67c: ldur            x0, [fp, #-8]
    // 0x15df680: stur            x2, [fp, #-0x18]
    // 0x15df684: StoreField: r2->field_f = r0
    //     0x15df684: stur            w0, [x2, #0xf]
    // 0x15df688: r1 = <Widget>
    //     0x15df688: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15df68c: r0 = AllocateGrowableArray()
    //     0x15df68c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15df690: mov             x1, x0
    // 0x15df694: ldur            x0, [fp, #-0x18]
    // 0x15df698: stur            x1, [fp, #-8]
    // 0x15df69c: StoreField: r1->field_f = r0
    //     0x15df69c: stur            w0, [x1, #0xf]
    // 0x15df6a0: r0 = 2
    //     0x15df6a0: movz            x0, #0x2
    // 0x15df6a4: StoreField: r1->field_b = r0
    //     0x15df6a4: stur            w0, [x1, #0xb]
    // 0x15df6a8: r0 = AppBar()
    //     0x15df6a8: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15df6ac: stur            x0, [fp, #-0x18]
    // 0x15df6b0: ldur            x16, [fp, #-0x10]
    // 0x15df6b4: ldur            lr, [fp, #-8]
    // 0x15df6b8: stp             lr, x16, [SP]
    // 0x15df6bc: mov             x1, x0
    // 0x15df6c0: ldur            x2, [fp, #-0x20]
    // 0x15df6c4: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15df6c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15df6c8: ldr             x4, [x4, #0xa58]
    // 0x15df6cc: r0 = AppBar()
    //     0x15df6cc: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15df6d0: ldur            x0, [fp, #-0x18]
    // 0x15df6d4: LeaveFrame
    //     0x15df6d4: mov             SP, fp
    //     0x15df6d8: ldp             fp, lr, [SP], #0x10
    // 0x15df6dc: ret
    //     0x15df6dc: ret             
    // 0x15df6e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15df6e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15df6e4: b               #0x15df454
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15df6e8, size: 0x33c
    // 0x15df6e8: EnterFrame
    //     0x15df6e8: stp             fp, lr, [SP, #-0x10]!
    //     0x15df6ec: mov             fp, SP
    // 0x15df6f0: AllocStack(0x58)
    //     0x15df6f0: sub             SP, SP, #0x58
    // 0x15df6f4: SetupParameters()
    //     0x15df6f4: ldr             x0, [fp, #0x10]
    //     0x15df6f8: ldur            w2, [x0, #0x17]
    //     0x15df6fc: add             x2, x2, HEAP, lsl #32
    //     0x15df700: stur            x2, [fp, #-8]
    // 0x15df704: CheckStackOverflow
    //     0x15df704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15df708: cmp             SP, x16
    //     0x15df70c: b.ls            #0x15dfa1c
    // 0x15df710: LoadField: r1 = r2->field_f
    //     0x15df710: ldur            w1, [x2, #0xf]
    // 0x15df714: DecompressPointer r1
    //     0x15df714: add             x1, x1, HEAP, lsl #32
    // 0x15df718: r0 = controller()
    //     0x15df718: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df71c: LoadField: r1 = r0->field_73
    //     0x15df71c: ldur            w1, [x0, #0x73]
    // 0x15df720: DecompressPointer r1
    //     0x15df720: add             x1, x1, HEAP, lsl #32
    // 0x15df724: r0 = value()
    //     0x15df724: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df728: LoadField: r1 = r0->field_1f
    //     0x15df728: ldur            w1, [x0, #0x1f]
    // 0x15df72c: DecompressPointer r1
    //     0x15df72c: add             x1, x1, HEAP, lsl #32
    // 0x15df730: cmp             w1, NULL
    // 0x15df734: b.ne            #0x15df740
    // 0x15df738: r0 = Null
    //     0x15df738: mov             x0, NULL
    // 0x15df73c: b               #0x15df748
    // 0x15df740: LoadField: r0 = r1->field_7
    //     0x15df740: ldur            w0, [x1, #7]
    // 0x15df744: DecompressPointer r0
    //     0x15df744: add             x0, x0, HEAP, lsl #32
    // 0x15df748: cmp             w0, NULL
    // 0x15df74c: b.ne            #0x15df758
    // 0x15df750: r0 = false
    //     0x15df750: add             x0, NULL, #0x30  ; false
    // 0x15df754: b               #0x15df984
    // 0x15df758: tbnz            w0, #4, #0x15df980
    // 0x15df75c: ldur            x0, [fp, #-8]
    // 0x15df760: LoadField: r1 = r0->field_f
    //     0x15df760: ldur            w1, [x0, #0xf]
    // 0x15df764: DecompressPointer r1
    //     0x15df764: add             x1, x1, HEAP, lsl #32
    // 0x15df768: r0 = controller()
    //     0x15df768: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df76c: LoadField: r1 = r0->field_b7
    //     0x15df76c: ldur            w1, [x0, #0xb7]
    // 0x15df770: DecompressPointer r1
    //     0x15df770: add             x1, x1, HEAP, lsl #32
    // 0x15df774: r0 = value()
    //     0x15df774: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df778: tbnz            w0, #4, #0x15df7b0
    // 0x15df77c: ldur            x0, [fp, #-8]
    // 0x15df780: LoadField: r1 = r0->field_f
    //     0x15df780: ldur            w1, [x0, #0xf]
    // 0x15df784: DecompressPointer r1
    //     0x15df784: add             x1, x1, HEAP, lsl #32
    // 0x15df788: r0 = controller()
    //     0x15df788: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df78c: LoadField: r1 = r0->field_bb
    //     0x15df78c: ldur            w1, [x0, #0xbb]
    // 0x15df790: DecompressPointer r1
    //     0x15df790: add             x1, x1, HEAP, lsl #32
    // 0x15df794: r0 = value()
    //     0x15df794: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df798: cmp             w0, NULL
    // 0x15df79c: r16 = true
    //     0x15df79c: add             x16, NULL, #0x20  ; true
    // 0x15df7a0: r17 = false
    //     0x15df7a0: add             x17, NULL, #0x30  ; false
    // 0x15df7a4: csel            x1, x16, x17, ne
    // 0x15df7a8: mov             x2, x1
    // 0x15df7ac: b               #0x15df7b4
    // 0x15df7b0: r2 = false
    //     0x15df7b0: add             x2, NULL, #0x30  ; false
    // 0x15df7b4: ldur            x0, [fp, #-8]
    // 0x15df7b8: stur            x2, [fp, #-0x10]
    // 0x15df7bc: LoadField: r1 = r0->field_13
    //     0x15df7bc: ldur            w1, [x0, #0x13]
    // 0x15df7c0: DecompressPointer r1
    //     0x15df7c0: add             x1, x1, HEAP, lsl #32
    // 0x15df7c4: r0 = of()
    //     0x15df7c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df7c8: LoadField: r2 = r0->field_5b
    //     0x15df7c8: ldur            w2, [x0, #0x5b]
    // 0x15df7cc: DecompressPointer r2
    //     0x15df7cc: add             x2, x2, HEAP, lsl #32
    // 0x15df7d0: ldur            x0, [fp, #-8]
    // 0x15df7d4: stur            x2, [fp, #-0x18]
    // 0x15df7d8: LoadField: r1 = r0->field_f
    //     0x15df7d8: ldur            w1, [x0, #0xf]
    // 0x15df7dc: DecompressPointer r1
    //     0x15df7dc: add             x1, x1, HEAP, lsl #32
    // 0x15df7e0: r0 = controller()
    //     0x15df7e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df7e4: LoadField: r1 = r0->field_bb
    //     0x15df7e4: ldur            w1, [x0, #0xbb]
    // 0x15df7e8: DecompressPointer r1
    //     0x15df7e8: add             x1, x1, HEAP, lsl #32
    // 0x15df7ec: r0 = value()
    //     0x15df7ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df7f0: cmp             w0, NULL
    // 0x15df7f4: r16 = true
    //     0x15df7f4: add             x16, NULL, #0x20  ; true
    // 0x15df7f8: r17 = false
    //     0x15df7f8: add             x17, NULL, #0x30  ; false
    // 0x15df7fc: csel            x2, x16, x17, ne
    // 0x15df800: ldur            x0, [fp, #-8]
    // 0x15df804: stur            x2, [fp, #-0x20]
    // 0x15df808: LoadField: r1 = r0->field_f
    //     0x15df808: ldur            w1, [x0, #0xf]
    // 0x15df80c: DecompressPointer r1
    //     0x15df80c: add             x1, x1, HEAP, lsl #32
    // 0x15df810: r0 = controller()
    //     0x15df810: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df814: LoadField: r1 = r0->field_bb
    //     0x15df814: ldur            w1, [x0, #0xbb]
    // 0x15df818: DecompressPointer r1
    //     0x15df818: add             x1, x1, HEAP, lsl #32
    // 0x15df81c: r0 = value()
    //     0x15df81c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df820: str             x0, [SP]
    // 0x15df824: r0 = _interpolateSingle()
    //     0x15df824: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15df828: mov             x2, x0
    // 0x15df82c: ldur            x0, [fp, #-8]
    // 0x15df830: stur            x2, [fp, #-0x28]
    // 0x15df834: LoadField: r1 = r0->field_13
    //     0x15df834: ldur            w1, [x0, #0x13]
    // 0x15df838: DecompressPointer r1
    //     0x15df838: add             x1, x1, HEAP, lsl #32
    // 0x15df83c: r0 = of()
    //     0x15df83c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df840: LoadField: r1 = r0->field_87
    //     0x15df840: ldur            w1, [x0, #0x87]
    // 0x15df844: DecompressPointer r1
    //     0x15df844: add             x1, x1, HEAP, lsl #32
    // 0x15df848: LoadField: r0 = r1->field_27
    //     0x15df848: ldur            w0, [x1, #0x27]
    // 0x15df84c: DecompressPointer r0
    //     0x15df84c: add             x0, x0, HEAP, lsl #32
    // 0x15df850: r16 = Instance_Color
    //     0x15df850: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15df854: str             x16, [SP]
    // 0x15df858: mov             x1, x0
    // 0x15df85c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15df85c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15df860: ldr             x4, [x4, #0xf40]
    // 0x15df864: r0 = copyWith()
    //     0x15df864: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15df868: stur            x0, [fp, #-0x30]
    // 0x15df86c: r0 = Text()
    //     0x15df86c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15df870: mov             x2, x0
    // 0x15df874: ldur            x0, [fp, #-0x28]
    // 0x15df878: stur            x2, [fp, #-0x38]
    // 0x15df87c: StoreField: r2->field_b = r0
    //     0x15df87c: stur            w0, [x2, #0xb]
    // 0x15df880: ldur            x0, [fp, #-0x30]
    // 0x15df884: StoreField: r2->field_13 = r0
    //     0x15df884: stur            w0, [x2, #0x13]
    // 0x15df888: ldur            x0, [fp, #-8]
    // 0x15df88c: LoadField: r1 = r0->field_13
    //     0x15df88c: ldur            w1, [x0, #0x13]
    // 0x15df890: DecompressPointer r1
    //     0x15df890: add             x1, x1, HEAP, lsl #32
    // 0x15df894: r0 = of()
    //     0x15df894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df898: LoadField: r1 = r0->field_5b
    //     0x15df898: ldur            w1, [x0, #0x5b]
    // 0x15df89c: DecompressPointer r1
    //     0x15df89c: add             x1, x1, HEAP, lsl #32
    // 0x15df8a0: stur            x1, [fp, #-8]
    // 0x15df8a4: r0 = ColorFilter()
    //     0x15df8a4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15df8a8: mov             x1, x0
    // 0x15df8ac: ldur            x0, [fp, #-8]
    // 0x15df8b0: stur            x1, [fp, #-0x28]
    // 0x15df8b4: StoreField: r1->field_7 = r0
    //     0x15df8b4: stur            w0, [x1, #7]
    // 0x15df8b8: r0 = Instance_BlendMode
    //     0x15df8b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15df8bc: ldr             x0, [x0, #0xb30]
    // 0x15df8c0: StoreField: r1->field_b = r0
    //     0x15df8c0: stur            w0, [x1, #0xb]
    // 0x15df8c4: r0 = 1
    //     0x15df8c4: movz            x0, #0x1
    // 0x15df8c8: StoreField: r1->field_13 = r0
    //     0x15df8c8: stur            x0, [x1, #0x13]
    // 0x15df8cc: r0 = SvgPicture()
    //     0x15df8cc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15df8d0: stur            x0, [fp, #-8]
    // 0x15df8d4: r16 = Instance_BoxFit
    //     0x15df8d4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15df8d8: ldr             x16, [x16, #0xb18]
    // 0x15df8dc: r30 = 24.000000
    //     0x15df8dc: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15df8e0: ldr             lr, [lr, #0xba8]
    // 0x15df8e4: stp             lr, x16, [SP, #0x10]
    // 0x15df8e8: r16 = 24.000000
    //     0x15df8e8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15df8ec: ldr             x16, [x16, #0xba8]
    // 0x15df8f0: ldur            lr, [fp, #-0x28]
    // 0x15df8f4: stp             lr, x16, [SP]
    // 0x15df8f8: mov             x1, x0
    // 0x15df8fc: r2 = "assets/images/shopping_bag.svg"
    //     0x15df8fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15df900: ldr             x2, [x2, #0xa60]
    // 0x15df904: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15df904: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15df908: ldr             x4, [x4, #0xa68]
    // 0x15df90c: r0 = SvgPicture.asset()
    //     0x15df90c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15df910: r0 = Badge()
    //     0x15df910: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15df914: mov             x1, x0
    // 0x15df918: ldur            x0, [fp, #-0x18]
    // 0x15df91c: stur            x1, [fp, #-0x28]
    // 0x15df920: StoreField: r1->field_b = r0
    //     0x15df920: stur            w0, [x1, #0xb]
    // 0x15df924: ldur            x0, [fp, #-0x38]
    // 0x15df928: StoreField: r1->field_27 = r0
    //     0x15df928: stur            w0, [x1, #0x27]
    // 0x15df92c: ldur            x0, [fp, #-0x20]
    // 0x15df930: StoreField: r1->field_2b = r0
    //     0x15df930: stur            w0, [x1, #0x2b]
    // 0x15df934: ldur            x0, [fp, #-8]
    // 0x15df938: StoreField: r1->field_2f = r0
    //     0x15df938: stur            w0, [x1, #0x2f]
    // 0x15df93c: r0 = Visibility()
    //     0x15df93c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15df940: mov             x1, x0
    // 0x15df944: ldur            x0, [fp, #-0x28]
    // 0x15df948: StoreField: r1->field_b = r0
    //     0x15df948: stur            w0, [x1, #0xb]
    // 0x15df94c: r0 = Instance_SizedBox
    //     0x15df94c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15df950: StoreField: r1->field_f = r0
    //     0x15df950: stur            w0, [x1, #0xf]
    // 0x15df954: ldur            x0, [fp, #-0x10]
    // 0x15df958: StoreField: r1->field_13 = r0
    //     0x15df958: stur            w0, [x1, #0x13]
    // 0x15df95c: r0 = false
    //     0x15df95c: add             x0, NULL, #0x30  ; false
    // 0x15df960: ArrayStore: r1[0] = r0  ; List_4
    //     0x15df960: stur            w0, [x1, #0x17]
    // 0x15df964: StoreField: r1->field_1b = r0
    //     0x15df964: stur            w0, [x1, #0x1b]
    // 0x15df968: StoreField: r1->field_1f = r0
    //     0x15df968: stur            w0, [x1, #0x1f]
    // 0x15df96c: StoreField: r1->field_23 = r0
    //     0x15df96c: stur            w0, [x1, #0x23]
    // 0x15df970: StoreField: r1->field_27 = r0
    //     0x15df970: stur            w0, [x1, #0x27]
    // 0x15df974: StoreField: r1->field_2b = r0
    //     0x15df974: stur            w0, [x1, #0x2b]
    // 0x15df978: mov             x0, x1
    // 0x15df97c: b               #0x15df99c
    // 0x15df980: r0 = false
    //     0x15df980: add             x0, NULL, #0x30  ; false
    // 0x15df984: r0 = Container()
    //     0x15df984: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15df988: mov             x1, x0
    // 0x15df98c: stur            x0, [fp, #-8]
    // 0x15df990: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15df990: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15df994: r0 = Container()
    //     0x15df994: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15df998: ldur            x0, [fp, #-8]
    // 0x15df99c: stur            x0, [fp, #-8]
    // 0x15df9a0: r0 = InkWell()
    //     0x15df9a0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15df9a4: mov             x3, x0
    // 0x15df9a8: ldur            x0, [fp, #-8]
    // 0x15df9ac: stur            x3, [fp, #-0x10]
    // 0x15df9b0: StoreField: r3->field_b = r0
    //     0x15df9b0: stur            w0, [x3, #0xb]
    // 0x15df9b4: r1 = Function '<anonymous closure>':.
    //     0x15df9b4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b60] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15df9b8: ldr             x1, [x1, #0xb60]
    // 0x15df9bc: r2 = Null
    //     0x15df9bc: mov             x2, NULL
    // 0x15df9c0: r0 = AllocateClosure()
    //     0x15df9c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df9c4: mov             x1, x0
    // 0x15df9c8: ldur            x0, [fp, #-0x10]
    // 0x15df9cc: StoreField: r0->field_f = r1
    //     0x15df9cc: stur            w1, [x0, #0xf]
    // 0x15df9d0: r1 = true
    //     0x15df9d0: add             x1, NULL, #0x20  ; true
    // 0x15df9d4: StoreField: r0->field_43 = r1
    //     0x15df9d4: stur            w1, [x0, #0x43]
    // 0x15df9d8: r2 = Instance_BoxShape
    //     0x15df9d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15df9dc: ldr             x2, [x2, #0x80]
    // 0x15df9e0: StoreField: r0->field_47 = r2
    //     0x15df9e0: stur            w2, [x0, #0x47]
    // 0x15df9e4: StoreField: r0->field_6f = r1
    //     0x15df9e4: stur            w1, [x0, #0x6f]
    // 0x15df9e8: r2 = false
    //     0x15df9e8: add             x2, NULL, #0x30  ; false
    // 0x15df9ec: StoreField: r0->field_73 = r2
    //     0x15df9ec: stur            w2, [x0, #0x73]
    // 0x15df9f0: StoreField: r0->field_83 = r1
    //     0x15df9f0: stur            w1, [x0, #0x83]
    // 0x15df9f4: StoreField: r0->field_7b = r2
    //     0x15df9f4: stur            w2, [x0, #0x7b]
    // 0x15df9f8: r0 = Padding()
    //     0x15df9f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15df9fc: r1 = Instance_EdgeInsets
    //     0x15df9fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15dfa00: ldr             x1, [x1, #0xa78]
    // 0x15dfa04: StoreField: r0->field_f = r1
    //     0x15dfa04: stur            w1, [x0, #0xf]
    // 0x15dfa08: ldur            x1, [fp, #-0x10]
    // 0x15dfa0c: StoreField: r0->field_b = r1
    //     0x15dfa0c: stur            w1, [x0, #0xb]
    // 0x15dfa10: LeaveFrame
    //     0x15dfa10: mov             SP, fp
    //     0x15dfa14: ldp             fp, lr, [SP], #0x10
    // 0x15dfa18: ret
    //     0x15dfa18: ret             
    // 0x15dfa1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dfa1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dfa20: b               #0x15df710
  }
}
