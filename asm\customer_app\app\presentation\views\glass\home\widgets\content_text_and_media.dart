// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/content_text_and_media.dart

// class id: 1049401, size: 0x8
class :: {
}

// class id: 3338, size: 0x14, field offset: 0x14
class _ContentTextAndMediaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb5fed0, size: 0x170
    // 0xb5fed0: EnterFrame
    //     0xb5fed0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5fed4: mov             fp, SP
    // 0xb5fed8: AllocStack(0x28)
    //     0xb5fed8: sub             SP, SP, #0x28
    // 0xb5fedc: SetupParameters(_ContentTextAndMediaState this /* r1 => r1, fp-0x8 */)
    //     0xb5fedc: stur            x1, [fp, #-8]
    // 0xb5fee0: CheckStackOverflow
    //     0xb5fee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5fee4: cmp             SP, x16
    //     0xb5fee8: b.ls            #0xb60034
    // 0xb5feec: r1 = 1
    //     0xb5feec: movz            x1, #0x1
    // 0xb5fef0: r0 = AllocateContext()
    //     0xb5fef0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5fef4: mov             x1, x0
    // 0xb5fef8: ldur            x0, [fp, #-8]
    // 0xb5fefc: StoreField: r1->field_f = r0
    //     0xb5fefc: stur            w0, [x1, #0xf]
    // 0xb5ff00: LoadField: r2 = r0->field_b
    //     0xb5ff00: ldur            w2, [x0, #0xb]
    // 0xb5ff04: DecompressPointer r2
    //     0xb5ff04: add             x2, x2, HEAP, lsl #32
    // 0xb5ff08: cmp             w2, NULL
    // 0xb5ff0c: b.eq            #0xb6003c
    // 0xb5ff10: LoadField: r0 = r2->field_b
    //     0xb5ff10: ldur            w0, [x2, #0xb]
    // 0xb5ff14: DecompressPointer r0
    //     0xb5ff14: add             x0, x0, HEAP, lsl #32
    // 0xb5ff18: cmp             w0, NULL
    // 0xb5ff1c: b.ne            #0xb5ff28
    // 0xb5ff20: r3 = Null
    //     0xb5ff20: mov             x3, NULL
    // 0xb5ff24: b               #0xb5ff30
    // 0xb5ff28: LoadField: r2 = r0->field_b
    //     0xb5ff28: ldur            w2, [x0, #0xb]
    // 0xb5ff2c: mov             x3, x2
    // 0xb5ff30: mov             x2, x1
    // 0xb5ff34: stur            x3, [fp, #-8]
    // 0xb5ff38: r1 = Function '<anonymous closure>':.
    //     0xb5ff38: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d30] AnonymousClosure: (0xb60060), in [package:customer_app/app/presentation/views/glass/home/<USER>/content_text_and_media.dart] _ContentTextAndMediaState::build (0xb5fed0)
    //     0xb5ff3c: ldr             x1, [x1, #0xd30]
    // 0xb5ff40: r0 = AllocateClosure()
    //     0xb5ff40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ff44: stur            x0, [fp, #-0x10]
    // 0xb5ff48: r0 = ListView()
    //     0xb5ff48: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb5ff4c: stur            x0, [fp, #-0x18]
    // 0xb5ff50: r16 = true
    //     0xb5ff50: add             x16, NULL, #0x20  ; true
    // 0xb5ff54: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb5ff54: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb5ff58: ldr             lr, [lr, #0x1c8]
    // 0xb5ff5c: stp             lr, x16, [SP]
    // 0xb5ff60: mov             x1, x0
    // 0xb5ff64: ldur            x2, [fp, #-0x10]
    // 0xb5ff68: ldur            x3, [fp, #-8]
    // 0xb5ff6c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb5ff6c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb5ff70: ldr             x4, [x4, #8]
    // 0xb5ff74: r0 = ListView.builder()
    //     0xb5ff74: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb5ff78: r1 = Null
    //     0xb5ff78: mov             x1, NULL
    // 0xb5ff7c: r2 = 2
    //     0xb5ff7c: movz            x2, #0x2
    // 0xb5ff80: r0 = AllocateArray()
    //     0xb5ff80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5ff84: mov             x2, x0
    // 0xb5ff88: ldur            x0, [fp, #-0x18]
    // 0xb5ff8c: stur            x2, [fp, #-8]
    // 0xb5ff90: StoreField: r2->field_f = r0
    //     0xb5ff90: stur            w0, [x2, #0xf]
    // 0xb5ff94: r1 = <Widget>
    //     0xb5ff94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5ff98: r0 = AllocateGrowableArray()
    //     0xb5ff98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5ff9c: mov             x1, x0
    // 0xb5ffa0: ldur            x0, [fp, #-8]
    // 0xb5ffa4: stur            x1, [fp, #-0x10]
    // 0xb5ffa8: StoreField: r1->field_f = r0
    //     0xb5ffa8: stur            w0, [x1, #0xf]
    // 0xb5ffac: r0 = 2
    //     0xb5ffac: movz            x0, #0x2
    // 0xb5ffb0: StoreField: r1->field_b = r0
    //     0xb5ffb0: stur            w0, [x1, #0xb]
    // 0xb5ffb4: r0 = Column()
    //     0xb5ffb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5ffb8: mov             x1, x0
    // 0xb5ffbc: r0 = Instance_Axis
    //     0xb5ffbc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5ffc0: stur            x1, [fp, #-8]
    // 0xb5ffc4: StoreField: r1->field_f = r0
    //     0xb5ffc4: stur            w0, [x1, #0xf]
    // 0xb5ffc8: r0 = Instance_MainAxisAlignment
    //     0xb5ffc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5ffcc: ldr             x0, [x0, #0xa08]
    // 0xb5ffd0: StoreField: r1->field_13 = r0
    //     0xb5ffd0: stur            w0, [x1, #0x13]
    // 0xb5ffd4: r0 = Instance_MainAxisSize
    //     0xb5ffd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5ffd8: ldr             x0, [x0, #0xa10]
    // 0xb5ffdc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5ffdc: stur            w0, [x1, #0x17]
    // 0xb5ffe0: r0 = Instance_CrossAxisAlignment
    //     0xb5ffe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5ffe4: ldr             x0, [x0, #0x890]
    // 0xb5ffe8: StoreField: r1->field_1b = r0
    //     0xb5ffe8: stur            w0, [x1, #0x1b]
    // 0xb5ffec: r0 = Instance_VerticalDirection
    //     0xb5ffec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5fff0: ldr             x0, [x0, #0xa20]
    // 0xb5fff4: StoreField: r1->field_23 = r0
    //     0xb5fff4: stur            w0, [x1, #0x23]
    // 0xb5fff8: r0 = Instance_Clip
    //     0xb5fff8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5fffc: ldr             x0, [x0, #0x38]
    // 0xb60000: StoreField: r1->field_2b = r0
    //     0xb60000: stur            w0, [x1, #0x2b]
    // 0xb60004: StoreField: r1->field_2f = rZR
    //     0xb60004: stur            xzr, [x1, #0x2f]
    // 0xb60008: ldur            x0, [fp, #-0x10]
    // 0xb6000c: StoreField: r1->field_b = r0
    //     0xb6000c: stur            w0, [x1, #0xb]
    // 0xb60010: r0 = Padding()
    //     0xb60010: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb60014: r1 = Instance_EdgeInsets
    //     0xb60014: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb60018: ldr             x1, [x1, #0x240]
    // 0xb6001c: StoreField: r0->field_f = r1
    //     0xb6001c: stur            w1, [x0, #0xf]
    // 0xb60020: ldur            x1, [fp, #-8]
    // 0xb60024: StoreField: r0->field_b = r1
    //     0xb60024: stur            w1, [x0, #0xb]
    // 0xb60028: LeaveFrame
    //     0xb60028: mov             SP, fp
    //     0xb6002c: ldp             fp, lr, [SP], #0x10
    // 0xb60030: ret
    //     0xb60030: ret             
    // 0xb60034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb60034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb60038: b               #0xb5feec
    // 0xb6003c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6003c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb60060, size: 0x15f0
    // 0xb60060: EnterFrame
    //     0xb60060: stp             fp, lr, [SP, #-0x10]!
    //     0xb60064: mov             fp, SP
    // 0xb60068: AllocStack(0x78)
    //     0xb60068: sub             SP, SP, #0x78
    // 0xb6006c: SetupParameters()
    //     0xb6006c: ldr             x0, [fp, #0x20]
    //     0xb60070: ldur            w2, [x0, #0x17]
    //     0xb60074: add             x2, x2, HEAP, lsl #32
    //     0xb60078: stur            x2, [fp, #-8]
    // 0xb6007c: CheckStackOverflow
    //     0xb6007c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb60080: cmp             SP, x16
    //     0xb60084: b.ls            #0xb615e0
    // 0xb60088: LoadField: r0 = r2->field_f
    //     0xb60088: ldur            w0, [x2, #0xf]
    // 0xb6008c: DecompressPointer r0
    //     0xb6008c: add             x0, x0, HEAP, lsl #32
    // 0xb60090: LoadField: r1 = r0->field_b
    //     0xb60090: ldur            w1, [x0, #0xb]
    // 0xb60094: DecompressPointer r1
    //     0xb60094: add             x1, x1, HEAP, lsl #32
    // 0xb60098: cmp             w1, NULL
    // 0xb6009c: b.eq            #0xb615e8
    // 0xb600a0: LoadField: r3 = r1->field_b
    //     0xb600a0: ldur            w3, [x1, #0xb]
    // 0xb600a4: DecompressPointer r3
    //     0xb600a4: add             x3, x3, HEAP, lsl #32
    // 0xb600a8: cmp             w3, NULL
    // 0xb600ac: b.ne            #0xb600bc
    // 0xb600b0: ldr             x4, [fp, #0x10]
    // 0xb600b4: r0 = Null
    //     0xb600b4: mov             x0, NULL
    // 0xb600b8: b               #0xb6011c
    // 0xb600bc: ldr             x4, [fp, #0x10]
    // 0xb600c0: LoadField: r0 = r3->field_b
    //     0xb600c0: ldur            w0, [x3, #0xb]
    // 0xb600c4: r5 = LoadInt32Instr(r4)
    //     0xb600c4: sbfx            x5, x4, #1, #0x1f
    //     0xb600c8: tbz             w4, #0, #0xb600d0
    //     0xb600cc: ldur            x5, [x4, #7]
    // 0xb600d0: r1 = LoadInt32Instr(r0)
    //     0xb600d0: sbfx            x1, x0, #1, #0x1f
    // 0xb600d4: mov             x0, x1
    // 0xb600d8: mov             x1, x5
    // 0xb600dc: cmp             x1, x0
    // 0xb600e0: b.hs            #0xb615ec
    // 0xb600e4: LoadField: r0 = r3->field_f
    //     0xb600e4: ldur            w0, [x3, #0xf]
    // 0xb600e8: DecompressPointer r0
    //     0xb600e8: add             x0, x0, HEAP, lsl #32
    // 0xb600ec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb600ec: add             x16, x0, x5, lsl #2
    //     0xb600f0: ldur            w1, [x16, #0xf]
    // 0xb600f4: DecompressPointer r1
    //     0xb600f4: add             x1, x1, HEAP, lsl #32
    // 0xb600f8: LoadField: r0 = r1->field_27
    //     0xb600f8: ldur            w0, [x1, #0x27]
    // 0xb600fc: DecompressPointer r0
    //     0xb600fc: add             x0, x0, HEAP, lsl #32
    // 0xb60100: cmp             w0, NULL
    // 0xb60104: b.ne            #0xb60110
    // 0xb60108: r0 = Null
    //     0xb60108: mov             x0, NULL
    // 0xb6010c: b               #0xb6011c
    // 0xb60110: LoadField: r1 = r0->field_f
    //     0xb60110: ldur            w1, [x0, #0xf]
    // 0xb60114: DecompressPointer r1
    //     0xb60114: add             x1, x1, HEAP, lsl #32
    // 0xb60118: mov             x0, x1
    // 0xb6011c: r1 = LoadClassIdInstr(r0)
    //     0xb6011c: ldur            x1, [x0, #-1]
    //     0xb60120: ubfx            x1, x1, #0xc, #0x14
    // 0xb60124: r16 = "LEFT"
    //     0xb60124: add             x16, PP, #0x53, lsl #12  ; [pp+0x53770] "LEFT"
    //     0xb60128: ldr             x16, [x16, #0x770]
    // 0xb6012c: stp             x16, x0, [SP]
    // 0xb60130: mov             x0, x1
    // 0xb60134: mov             lr, x0
    // 0xb60138: ldr             lr, [x21, lr, lsl #3]
    // 0xb6013c: blr             lr
    // 0xb60140: tbnz            w0, #4, #0xb60b4c
    // 0xb60144: ldur            x0, [fp, #-8]
    // 0xb60148: r0 = Radius()
    //     0xb60148: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6014c: d0 = 20.000000
    //     0xb6014c: fmov            d0, #20.00000000
    // 0xb60150: stur            x0, [fp, #-0x10]
    // 0xb60154: StoreField: r0->field_7 = d0
    //     0xb60154: stur            d0, [x0, #7]
    // 0xb60158: StoreField: r0->field_f = d0
    //     0xb60158: stur            d0, [x0, #0xf]
    // 0xb6015c: r0 = BorderRadius()
    //     0xb6015c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb60160: mov             x3, x0
    // 0xb60164: ldur            x0, [fp, #-0x10]
    // 0xb60168: stur            x3, [fp, #-0x18]
    // 0xb6016c: StoreField: r3->field_7 = r0
    //     0xb6016c: stur            w0, [x3, #7]
    // 0xb60170: StoreField: r3->field_b = r0
    //     0xb60170: stur            w0, [x3, #0xb]
    // 0xb60174: StoreField: r3->field_f = r0
    //     0xb60174: stur            w0, [x3, #0xf]
    // 0xb60178: StoreField: r3->field_13 = r0
    //     0xb60178: stur            w0, [x3, #0x13]
    // 0xb6017c: ldur            x4, [fp, #-8]
    // 0xb60180: LoadField: r0 = r4->field_f
    //     0xb60180: ldur            w0, [x4, #0xf]
    // 0xb60184: DecompressPointer r0
    //     0xb60184: add             x0, x0, HEAP, lsl #32
    // 0xb60188: LoadField: r1 = r0->field_b
    //     0xb60188: ldur            w1, [x0, #0xb]
    // 0xb6018c: DecompressPointer r1
    //     0xb6018c: add             x1, x1, HEAP, lsl #32
    // 0xb60190: cmp             w1, NULL
    // 0xb60194: b.eq            #0xb615f0
    // 0xb60198: LoadField: r2 = r1->field_b
    //     0xb60198: ldur            w2, [x1, #0xb]
    // 0xb6019c: DecompressPointer r2
    //     0xb6019c: add             x2, x2, HEAP, lsl #32
    // 0xb601a0: cmp             w2, NULL
    // 0xb601a4: b.ne            #0xb601b4
    // 0xb601a8: ldr             x5, [fp, #0x10]
    // 0xb601ac: r0 = Null
    //     0xb601ac: mov             x0, NULL
    // 0xb601b0: b               #0xb601f8
    // 0xb601b4: ldr             x5, [fp, #0x10]
    // 0xb601b8: LoadField: r0 = r2->field_b
    //     0xb601b8: ldur            w0, [x2, #0xb]
    // 0xb601bc: r6 = LoadInt32Instr(r5)
    //     0xb601bc: sbfx            x6, x5, #1, #0x1f
    //     0xb601c0: tbz             w5, #0, #0xb601c8
    //     0xb601c4: ldur            x6, [x5, #7]
    // 0xb601c8: r1 = LoadInt32Instr(r0)
    //     0xb601c8: sbfx            x1, x0, #1, #0x1f
    // 0xb601cc: mov             x0, x1
    // 0xb601d0: mov             x1, x6
    // 0xb601d4: cmp             x1, x0
    // 0xb601d8: b.hs            #0xb615f4
    // 0xb601dc: LoadField: r0 = r2->field_f
    //     0xb601dc: ldur            w0, [x2, #0xf]
    // 0xb601e0: DecompressPointer r0
    //     0xb601e0: add             x0, x0, HEAP, lsl #32
    // 0xb601e4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb601e4: add             x16, x0, x6, lsl #2
    //     0xb601e8: ldur            w1, [x16, #0xf]
    // 0xb601ec: DecompressPointer r1
    //     0xb601ec: add             x1, x1, HEAP, lsl #32
    // 0xb601f0: LoadField: r0 = r1->field_13
    //     0xb601f0: ldur            w0, [x1, #0x13]
    // 0xb601f4: DecompressPointer r0
    //     0xb601f4: add             x0, x0, HEAP, lsl #32
    // 0xb601f8: cmp             w0, NULL
    // 0xb601fc: b.ne            #0xb60204
    // 0xb60200: r0 = ""
    //     0xb60200: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb60204: stur            x0, [fp, #-0x10]
    // 0xb60208: r1 = Function '<anonymous closure>':.
    //     0xb60208: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d38] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb6020c: ldr             x1, [x1, #0xd38]
    // 0xb60210: r2 = Null
    //     0xb60210: mov             x2, NULL
    // 0xb60214: r0 = AllocateClosure()
    //     0xb60214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb60218: r1 = Function '<anonymous closure>':.
    //     0xb60218: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d40] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb6021c: ldr             x1, [x1, #0xd40]
    // 0xb60220: r2 = Null
    //     0xb60220: mov             x2, NULL
    // 0xb60224: stur            x0, [fp, #-0x20]
    // 0xb60228: r0 = AllocateClosure()
    //     0xb60228: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6022c: stur            x0, [fp, #-0x28]
    // 0xb60230: r0 = CachedNetworkImage()
    //     0xb60230: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb60234: stur            x0, [fp, #-0x30]
    // 0xb60238: r16 = 180.000000
    //     0xb60238: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c88] 180
    //     0xb6023c: ldr             x16, [x16, #0xc88]
    // 0xb60240: r30 = 150.000000
    //     0xb60240: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb60244: ldr             lr, [lr, #0x690]
    // 0xb60248: stp             lr, x16, [SP, #0x18]
    // 0xb6024c: ldur            x16, [fp, #-0x20]
    // 0xb60250: ldur            lr, [fp, #-0x28]
    // 0xb60254: stp             lr, x16, [SP, #8]
    // 0xb60258: r16 = Instance_BoxFit
    //     0xb60258: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb6025c: ldr             x16, [x16, #0x118]
    // 0xb60260: str             x16, [SP]
    // 0xb60264: mov             x1, x0
    // 0xb60268: ldur            x2, [fp, #-0x10]
    // 0xb6026c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb6026c: add             x4, PP, #0x54, lsl #12  ; [pp+0x542b0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb60270: ldr             x4, [x4, #0x2b0]
    // 0xb60274: r0 = CachedNetworkImage()
    //     0xb60274: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb60278: r0 = ClipRRect()
    //     0xb60278: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb6027c: mov             x2, x0
    // 0xb60280: ldur            x0, [fp, #-0x18]
    // 0xb60284: stur            x2, [fp, #-0x10]
    // 0xb60288: StoreField: r2->field_f = r0
    //     0xb60288: stur            w0, [x2, #0xf]
    // 0xb6028c: r3 = Instance_Clip
    //     0xb6028c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb60290: ldr             x3, [x3, #0x138]
    // 0xb60294: ArrayStore: r2[0] = r3  ; List_4
    //     0xb60294: stur            w3, [x2, #0x17]
    // 0xb60298: ldur            x0, [fp, #-0x30]
    // 0xb6029c: StoreField: r2->field_b = r0
    //     0xb6029c: stur            w0, [x2, #0xb]
    // 0xb602a0: r1 = <FlexParentData>
    //     0xb602a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb602a4: ldr             x1, [x1, #0xe00]
    // 0xb602a8: r0 = Expanded()
    //     0xb602a8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb602ac: mov             x3, x0
    // 0xb602b0: r2 = 2
    //     0xb602b0: movz            x2, #0x2
    // 0xb602b4: stur            x3, [fp, #-0x18]
    // 0xb602b8: StoreField: r3->field_13 = r2
    //     0xb602b8: stur            x2, [x3, #0x13]
    // 0xb602bc: r4 = Instance_FlexFit
    //     0xb602bc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb602c0: ldr             x4, [x4, #0xe08]
    // 0xb602c4: StoreField: r3->field_1b = r4
    //     0xb602c4: stur            w4, [x3, #0x1b]
    // 0xb602c8: ldur            x0, [fp, #-0x10]
    // 0xb602cc: StoreField: r3->field_b = r0
    //     0xb602cc: stur            w0, [x3, #0xb]
    // 0xb602d0: ldur            x5, [fp, #-8]
    // 0xb602d4: LoadField: r0 = r5->field_f
    //     0xb602d4: ldur            w0, [x5, #0xf]
    // 0xb602d8: DecompressPointer r0
    //     0xb602d8: add             x0, x0, HEAP, lsl #32
    // 0xb602dc: LoadField: r1 = r0->field_b
    //     0xb602dc: ldur            w1, [x0, #0xb]
    // 0xb602e0: DecompressPointer r1
    //     0xb602e0: add             x1, x1, HEAP, lsl #32
    // 0xb602e4: cmp             w1, NULL
    // 0xb602e8: b.eq            #0xb615f8
    // 0xb602ec: LoadField: r6 = r1->field_b
    //     0xb602ec: ldur            w6, [x1, #0xb]
    // 0xb602f0: DecompressPointer r6
    //     0xb602f0: add             x6, x6, HEAP, lsl #32
    // 0xb602f4: cmp             w6, NULL
    // 0xb602f8: b.ne            #0xb60308
    // 0xb602fc: ldr             x7, [fp, #0x10]
    // 0xb60300: r0 = Null
    //     0xb60300: mov             x0, NULL
    // 0xb60304: b               #0xb6034c
    // 0xb60308: ldr             x7, [fp, #0x10]
    // 0xb6030c: LoadField: r0 = r6->field_b
    //     0xb6030c: ldur            w0, [x6, #0xb]
    // 0xb60310: r8 = LoadInt32Instr(r7)
    //     0xb60310: sbfx            x8, x7, #1, #0x1f
    //     0xb60314: tbz             w7, #0, #0xb6031c
    //     0xb60318: ldur            x8, [x7, #7]
    // 0xb6031c: r1 = LoadInt32Instr(r0)
    //     0xb6031c: sbfx            x1, x0, #1, #0x1f
    // 0xb60320: mov             x0, x1
    // 0xb60324: mov             x1, x8
    // 0xb60328: cmp             x1, x0
    // 0xb6032c: b.hs            #0xb615fc
    // 0xb60330: LoadField: r0 = r6->field_f
    //     0xb60330: ldur            w0, [x6, #0xf]
    // 0xb60334: DecompressPointer r0
    //     0xb60334: add             x0, x0, HEAP, lsl #32
    // 0xb60338: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb60338: add             x16, x0, x8, lsl #2
    //     0xb6033c: ldur            w1, [x16, #0xf]
    // 0xb60340: DecompressPointer r1
    //     0xb60340: add             x1, x1, HEAP, lsl #32
    // 0xb60344: LoadField: r0 = r1->field_7
    //     0xb60344: ldur            w0, [x1, #7]
    // 0xb60348: DecompressPointer r0
    //     0xb60348: add             x0, x0, HEAP, lsl #32
    // 0xb6034c: cmp             w0, NULL
    // 0xb60350: b.ne            #0xb60358
    // 0xb60354: r0 = ""
    //     0xb60354: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb60358: ldr             x1, [fp, #0x18]
    // 0xb6035c: stur            x0, [fp, #-0x10]
    // 0xb60360: r0 = of()
    //     0xb60360: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb60364: LoadField: r1 = r0->field_87
    //     0xb60364: ldur            w1, [x0, #0x87]
    // 0xb60368: DecompressPointer r1
    //     0xb60368: add             x1, x1, HEAP, lsl #32
    // 0xb6036c: LoadField: r0 = r1->field_7
    //     0xb6036c: ldur            w0, [x1, #7]
    // 0xb60370: DecompressPointer r0
    //     0xb60370: add             x0, x0, HEAP, lsl #32
    // 0xb60374: stur            x0, [fp, #-0x20]
    // 0xb60378: r1 = Instance_Color
    //     0xb60378: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6037c: d0 = 0.700000
    //     0xb6037c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb60380: ldr             d0, [x17, #0xf48]
    // 0xb60384: r0 = withOpacity()
    //     0xb60384: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb60388: r16 = 16.000000
    //     0xb60388: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6038c: ldr             x16, [x16, #0x188]
    // 0xb60390: stp             x0, x16, [SP]
    // 0xb60394: ldur            x1, [fp, #-0x20]
    // 0xb60398: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb60398: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6039c: ldr             x4, [x4, #0xaa0]
    // 0xb603a0: r0 = copyWith()
    //     0xb603a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb603a4: stur            x0, [fp, #-0x20]
    // 0xb603a8: r0 = Text()
    //     0xb603a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb603ac: mov             x3, x0
    // 0xb603b0: ldur            x0, [fp, #-0x10]
    // 0xb603b4: stur            x3, [fp, #-0x28]
    // 0xb603b8: StoreField: r3->field_b = r0
    //     0xb603b8: stur            w0, [x3, #0xb]
    // 0xb603bc: ldur            x0, [fp, #-0x20]
    // 0xb603c0: StoreField: r3->field_13 = r0
    //     0xb603c0: stur            w0, [x3, #0x13]
    // 0xb603c4: r1 = Null
    //     0xb603c4: mov             x1, NULL
    // 0xb603c8: r2 = 2
    //     0xb603c8: movz            x2, #0x2
    // 0xb603cc: r0 = AllocateArray()
    //     0xb603cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb603d0: mov             x2, x0
    // 0xb603d4: ldur            x0, [fp, #-0x28]
    // 0xb603d8: stur            x2, [fp, #-0x10]
    // 0xb603dc: StoreField: r2->field_f = r0
    //     0xb603dc: stur            w0, [x2, #0xf]
    // 0xb603e0: r1 = <Widget>
    //     0xb603e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb603e4: r0 = AllocateGrowableArray()
    //     0xb603e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb603e8: mov             x2, x0
    // 0xb603ec: ldur            x0, [fp, #-0x10]
    // 0xb603f0: stur            x2, [fp, #-0x20]
    // 0xb603f4: StoreField: r2->field_f = r0
    //     0xb603f4: stur            w0, [x2, #0xf]
    // 0xb603f8: r4 = 2
    //     0xb603f8: movz            x4, #0x2
    // 0xb603fc: StoreField: r2->field_b = r4
    //     0xb603fc: stur            w4, [x2, #0xb]
    // 0xb60400: ldur            x3, [fp, #-8]
    // 0xb60404: LoadField: r0 = r3->field_f
    //     0xb60404: ldur            w0, [x3, #0xf]
    // 0xb60408: DecompressPointer r0
    //     0xb60408: add             x0, x0, HEAP, lsl #32
    // 0xb6040c: LoadField: r1 = r0->field_b
    //     0xb6040c: ldur            w1, [x0, #0xb]
    // 0xb60410: DecompressPointer r1
    //     0xb60410: add             x1, x1, HEAP, lsl #32
    // 0xb60414: cmp             w1, NULL
    // 0xb60418: b.eq            #0xb61600
    // 0xb6041c: LoadField: r4 = r1->field_b
    //     0xb6041c: ldur            w4, [x1, #0xb]
    // 0xb60420: DecompressPointer r4
    //     0xb60420: add             x4, x4, HEAP, lsl #32
    // 0xb60424: cmp             w4, NULL
    // 0xb60428: b.ne            #0xb60438
    // 0xb6042c: ldr             x5, [fp, #0x10]
    // 0xb60430: r0 = Null
    //     0xb60430: mov             x0, NULL
    // 0xb60434: b               #0xb604a0
    // 0xb60438: ldr             x5, [fp, #0x10]
    // 0xb6043c: LoadField: r0 = r4->field_b
    //     0xb6043c: ldur            w0, [x4, #0xb]
    // 0xb60440: r6 = LoadInt32Instr(r5)
    //     0xb60440: sbfx            x6, x5, #1, #0x1f
    //     0xb60444: tbz             w5, #0, #0xb6044c
    //     0xb60448: ldur            x6, [x5, #7]
    // 0xb6044c: r1 = LoadInt32Instr(r0)
    //     0xb6044c: sbfx            x1, x0, #1, #0x1f
    // 0xb60450: mov             x0, x1
    // 0xb60454: mov             x1, x6
    // 0xb60458: cmp             x1, x0
    // 0xb6045c: b.hs            #0xb61604
    // 0xb60460: LoadField: r0 = r4->field_f
    //     0xb60460: ldur            w0, [x4, #0xf]
    // 0xb60464: DecompressPointer r0
    //     0xb60464: add             x0, x0, HEAP, lsl #32
    // 0xb60468: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb60468: add             x16, x0, x6, lsl #2
    //     0xb6046c: ldur            w1, [x16, #0xf]
    // 0xb60470: DecompressPointer r1
    //     0xb60470: add             x1, x1, HEAP, lsl #32
    // 0xb60474: LoadField: r0 = r1->field_b
    //     0xb60474: ldur            w0, [x1, #0xb]
    // 0xb60478: DecompressPointer r0
    //     0xb60478: add             x0, x0, HEAP, lsl #32
    // 0xb6047c: cmp             w0, NULL
    // 0xb60480: b.ne            #0xb6048c
    // 0xb60484: r0 = Null
    //     0xb60484: mov             x0, NULL
    // 0xb60488: b               #0xb604a0
    // 0xb6048c: LoadField: r1 = r0->field_7
    //     0xb6048c: ldur            w1, [x0, #7]
    // 0xb60490: cbnz            w1, #0xb6049c
    // 0xb60494: r0 = false
    //     0xb60494: add             x0, NULL, #0x30  ; false
    // 0xb60498: b               #0xb604a0
    // 0xb6049c: r0 = true
    //     0xb6049c: add             x0, NULL, #0x20  ; true
    // 0xb604a0: cmp             w0, NULL
    // 0xb604a4: b.eq            #0xb60660
    // 0xb604a8: tbnz            w0, #4, #0xb60660
    // 0xb604ac: cmp             w4, NULL
    // 0xb604b0: b.ne            #0xb604bc
    // 0xb604b4: r0 = Null
    //     0xb604b4: mov             x0, NULL
    // 0xb604b8: b               #0xb604fc
    // 0xb604bc: LoadField: r0 = r4->field_b
    //     0xb604bc: ldur            w0, [x4, #0xb]
    // 0xb604c0: r6 = LoadInt32Instr(r5)
    //     0xb604c0: sbfx            x6, x5, #1, #0x1f
    //     0xb604c4: tbz             w5, #0, #0xb604cc
    //     0xb604c8: ldur            x6, [x5, #7]
    // 0xb604cc: r1 = LoadInt32Instr(r0)
    //     0xb604cc: sbfx            x1, x0, #1, #0x1f
    // 0xb604d0: mov             x0, x1
    // 0xb604d4: mov             x1, x6
    // 0xb604d8: cmp             x1, x0
    // 0xb604dc: b.hs            #0xb61608
    // 0xb604e0: LoadField: r0 = r4->field_f
    //     0xb604e0: ldur            w0, [x4, #0xf]
    // 0xb604e4: DecompressPointer r0
    //     0xb604e4: add             x0, x0, HEAP, lsl #32
    // 0xb604e8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb604e8: add             x16, x0, x6, lsl #2
    //     0xb604ec: ldur            w1, [x16, #0xf]
    // 0xb604f0: DecompressPointer r1
    //     0xb604f0: add             x1, x1, HEAP, lsl #32
    // 0xb604f4: LoadField: r0 = r1->field_b
    //     0xb604f4: ldur            w0, [x1, #0xb]
    // 0xb604f8: DecompressPointer r0
    //     0xb604f8: add             x0, x0, HEAP, lsl #32
    // 0xb604fc: cmp             w0, NULL
    // 0xb60500: b.ne            #0xb6050c
    // 0xb60504: r1 = ""
    //     0xb60504: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb60508: b               #0xb60510
    // 0xb6050c: mov             x1, x0
    // 0xb60510: r0 = parse()
    //     0xb60510: bl              #0x9a0ae4  ; [package:html/parser.dart] ::parse
    // 0xb60514: mov             x1, x0
    // 0xb60518: r0 = documentElement()
    //     0xb60518: bl              #0x99fd68  ; [package:html/dom.dart] Document::documentElement
    // 0xb6051c: cmp             w0, NULL
    // 0xb60520: b.ne            #0xb6052c
    // 0xb60524: r1 = Null
    //     0xb60524: mov             x1, NULL
    // 0xb60528: b               #0xb6053c
    // 0xb6052c: mov             x1, x0
    // 0xb60530: r2 = "body"
    //     0xb60530: ldr             x2, [PP, #0x7b88]  ; [pp+0x7b88] "body"
    // 0xb60534: r0 = querySelector()
    //     0xb60534: bl              #0x99fd9c  ; [package:html/src/query_selector.dart] ::querySelector
    // 0xb60538: mov             x1, x0
    // 0xb6053c: ldur            x0, [fp, #-0x20]
    // 0xb60540: cmp             w1, NULL
    // 0xb60544: b.eq            #0xb6160c
    // 0xb60548: r0 = _getText()
    //     0xb60548: bl              #0x99f830  ; [package:html/dom.dart] ::_getText
    // 0xb6054c: ldr             x1, [fp, #0x18]
    // 0xb60550: stur            x0, [fp, #-0x10]
    // 0xb60554: r0 = of()
    //     0xb60554: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb60558: LoadField: r1 = r0->field_87
    //     0xb60558: ldur            w1, [x0, #0x87]
    // 0xb6055c: DecompressPointer r1
    //     0xb6055c: add             x1, x1, HEAP, lsl #32
    // 0xb60560: LoadField: r0 = r1->field_2b
    //     0xb60560: ldur            w0, [x1, #0x2b]
    // 0xb60564: DecompressPointer r0
    //     0xb60564: add             x0, x0, HEAP, lsl #32
    // 0xb60568: stur            x0, [fp, #-0x28]
    // 0xb6056c: r1 = Instance_Color
    //     0xb6056c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb60570: d0 = 0.400000
    //     0xb60570: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb60574: r0 = withOpacity()
    //     0xb60574: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb60578: r16 = 12.000000
    //     0xb60578: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb6057c: ldr             x16, [x16, #0x9e8]
    // 0xb60580: stp             x16, x0, [SP]
    // 0xb60584: ldur            x1, [fp, #-0x28]
    // 0xb60588: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb60588: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb6058c: ldr             x4, [x4, #0x9b8]
    // 0xb60590: r0 = copyWith()
    //     0xb60590: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb60594: stur            x0, [fp, #-0x28]
    // 0xb60598: r0 = Text()
    //     0xb60598: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6059c: mov             x1, x0
    // 0xb605a0: ldur            x0, [fp, #-0x10]
    // 0xb605a4: stur            x1, [fp, #-0x30]
    // 0xb605a8: StoreField: r1->field_b = r0
    //     0xb605a8: stur            w0, [x1, #0xb]
    // 0xb605ac: ldur            x0, [fp, #-0x28]
    // 0xb605b0: StoreField: r1->field_13 = r0
    //     0xb605b0: stur            w0, [x1, #0x13]
    // 0xb605b4: r2 = Instance_TextOverflow
    //     0xb605b4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb605b8: ldr             x2, [x2, #0xe10]
    // 0xb605bc: StoreField: r1->field_2b = r2
    //     0xb605bc: stur            w2, [x1, #0x2b]
    // 0xb605c0: r2 = 4
    //     0xb605c0: movz            x2, #0x4
    // 0xb605c4: StoreField: r1->field_37 = r2
    //     0xb605c4: stur            w2, [x1, #0x37]
    // 0xb605c8: r0 = Padding()
    //     0xb605c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb605cc: mov             x2, x0
    // 0xb605d0: r0 = Instance_EdgeInsets
    //     0xb605d0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb605d4: ldr             x0, [x0, #0x668]
    // 0xb605d8: stur            x2, [fp, #-0x10]
    // 0xb605dc: StoreField: r2->field_f = r0
    //     0xb605dc: stur            w0, [x2, #0xf]
    // 0xb605e0: ldur            x1, [fp, #-0x30]
    // 0xb605e4: StoreField: r2->field_b = r1
    //     0xb605e4: stur            w1, [x2, #0xb]
    // 0xb605e8: ldur            x3, [fp, #-0x20]
    // 0xb605ec: LoadField: r1 = r3->field_b
    //     0xb605ec: ldur            w1, [x3, #0xb]
    // 0xb605f0: LoadField: r4 = r3->field_f
    //     0xb605f0: ldur            w4, [x3, #0xf]
    // 0xb605f4: DecompressPointer r4
    //     0xb605f4: add             x4, x4, HEAP, lsl #32
    // 0xb605f8: LoadField: r5 = r4->field_b
    //     0xb605f8: ldur            w5, [x4, #0xb]
    // 0xb605fc: r4 = LoadInt32Instr(r1)
    //     0xb605fc: sbfx            x4, x1, #1, #0x1f
    // 0xb60600: stur            x4, [fp, #-0x38]
    // 0xb60604: r1 = LoadInt32Instr(r5)
    //     0xb60604: sbfx            x1, x5, #1, #0x1f
    // 0xb60608: cmp             x4, x1
    // 0xb6060c: b.ne            #0xb60618
    // 0xb60610: mov             x1, x3
    // 0xb60614: r0 = _growToNextCapacity()
    //     0xb60614: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb60618: ldur            x2, [fp, #-0x20]
    // 0xb6061c: ldur            x3, [fp, #-0x38]
    // 0xb60620: add             x0, x3, #1
    // 0xb60624: lsl             x1, x0, #1
    // 0xb60628: StoreField: r2->field_b = r1
    //     0xb60628: stur            w1, [x2, #0xb]
    // 0xb6062c: LoadField: r1 = r2->field_f
    //     0xb6062c: ldur            w1, [x2, #0xf]
    // 0xb60630: DecompressPointer r1
    //     0xb60630: add             x1, x1, HEAP, lsl #32
    // 0xb60634: ldur            x0, [fp, #-0x10]
    // 0xb60638: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb60638: add             x25, x1, x3, lsl #2
    //     0xb6063c: add             x25, x25, #0xf
    //     0xb60640: str             w0, [x25]
    //     0xb60644: tbz             w0, #0, #0xb60660
    //     0xb60648: ldurb           w16, [x1, #-1]
    //     0xb6064c: ldurb           w17, [x0, #-1]
    //     0xb60650: and             x16, x17, x16, lsr #2
    //     0xb60654: tst             x16, HEAP, lsr #32
    //     0xb60658: b.eq            #0xb60660
    //     0xb6065c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb60660: ldur            x3, [fp, #-8]
    // 0xb60664: LoadField: r0 = r3->field_f
    //     0xb60664: ldur            w0, [x3, #0xf]
    // 0xb60668: DecompressPointer r0
    //     0xb60668: add             x0, x0, HEAP, lsl #32
    // 0xb6066c: LoadField: r1 = r0->field_b
    //     0xb6066c: ldur            w1, [x0, #0xb]
    // 0xb60670: DecompressPointer r1
    //     0xb60670: add             x1, x1, HEAP, lsl #32
    // 0xb60674: cmp             w1, NULL
    // 0xb60678: b.eq            #0xb61610
    // 0xb6067c: LoadField: r4 = r1->field_b
    //     0xb6067c: ldur            w4, [x1, #0xb]
    // 0xb60680: DecompressPointer r4
    //     0xb60680: add             x4, x4, HEAP, lsl #32
    // 0xb60684: cmp             w4, NULL
    // 0xb60688: b.ne            #0xb60698
    // 0xb6068c: ldr             x5, [fp, #0x10]
    // 0xb60690: r0 = Null
    //     0xb60690: mov             x0, NULL
    // 0xb60694: b               #0xb60700
    // 0xb60698: ldr             x5, [fp, #0x10]
    // 0xb6069c: LoadField: r0 = r4->field_b
    //     0xb6069c: ldur            w0, [x4, #0xb]
    // 0xb606a0: r6 = LoadInt32Instr(r5)
    //     0xb606a0: sbfx            x6, x5, #1, #0x1f
    //     0xb606a4: tbz             w5, #0, #0xb606ac
    //     0xb606a8: ldur            x6, [x5, #7]
    // 0xb606ac: r1 = LoadInt32Instr(r0)
    //     0xb606ac: sbfx            x1, x0, #1, #0x1f
    // 0xb606b0: mov             x0, x1
    // 0xb606b4: mov             x1, x6
    // 0xb606b8: cmp             x1, x0
    // 0xb606bc: b.hs            #0xb61614
    // 0xb606c0: LoadField: r0 = r4->field_f
    //     0xb606c0: ldur            w0, [x4, #0xf]
    // 0xb606c4: DecompressPointer r0
    //     0xb606c4: add             x0, x0, HEAP, lsl #32
    // 0xb606c8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb606c8: add             x16, x0, x6, lsl #2
    //     0xb606cc: ldur            w1, [x16, #0xf]
    // 0xb606d0: DecompressPointer r1
    //     0xb606d0: add             x1, x1, HEAP, lsl #32
    // 0xb606d4: LoadField: r0 = r1->field_f
    //     0xb606d4: ldur            w0, [x1, #0xf]
    // 0xb606d8: DecompressPointer r0
    //     0xb606d8: add             x0, x0, HEAP, lsl #32
    // 0xb606dc: cmp             w0, NULL
    // 0xb606e0: b.ne            #0xb606ec
    // 0xb606e4: r0 = Null
    //     0xb606e4: mov             x0, NULL
    // 0xb606e8: b               #0xb60700
    // 0xb606ec: LoadField: r1 = r0->field_7
    //     0xb606ec: ldur            w1, [x0, #7]
    // 0xb606f0: cbnz            w1, #0xb606fc
    // 0xb606f4: r0 = false
    //     0xb606f4: add             x0, NULL, #0x30  ; false
    // 0xb606f8: b               #0xb60700
    // 0xb606fc: r0 = true
    //     0xb606fc: add             x0, NULL, #0x20  ; true
    // 0xb60700: cmp             w0, NULL
    // 0xb60704: b.ne            #0xb6070c
    // 0xb60708: r0 = false
    //     0xb60708: add             x0, NULL, #0x30  ; false
    // 0xb6070c: ldr             x1, [fp, #0x18]
    // 0xb60710: stur            x0, [fp, #-0x10]
    // 0xb60714: r0 = of()
    //     0xb60714: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb60718: r17 = 307
    //     0xb60718: movz            x17, #0x133
    // 0xb6071c: ldr             w1, [x0, x17]
    // 0xb60720: DecompressPointer r1
    //     0xb60720: add             x1, x1, HEAP, lsl #32
    // 0xb60724: stur            x1, [fp, #-0x28]
    // 0xb60728: r16 = <EdgeInsets>
    //     0xb60728: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb6072c: ldr             x16, [x16, #0xda0]
    // 0xb60730: r30 = Instance_EdgeInsets
    //     0xb60730: add             lr, PP, #0x55, lsl #12  ; [pp+0x55d48] Obj!EdgeInsets@d58ee1
    //     0xb60734: ldr             lr, [lr, #0xd48]
    // 0xb60738: stp             lr, x16, [SP]
    // 0xb6073c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6073c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb60740: r0 = all()
    //     0xb60740: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb60744: stur            x0, [fp, #-0x30]
    // 0xb60748: r0 = Radius()
    //     0xb60748: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6074c: d0 = 20.000000
    //     0xb6074c: fmov            d0, #20.00000000
    // 0xb60750: stur            x0, [fp, #-0x40]
    // 0xb60754: StoreField: r0->field_7 = d0
    //     0xb60754: stur            d0, [x0, #7]
    // 0xb60758: StoreField: r0->field_f = d0
    //     0xb60758: stur            d0, [x0, #0xf]
    // 0xb6075c: r0 = BorderRadius()
    //     0xb6075c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb60760: mov             x1, x0
    // 0xb60764: ldur            x0, [fp, #-0x40]
    // 0xb60768: stur            x1, [fp, #-0x48]
    // 0xb6076c: StoreField: r1->field_7 = r0
    //     0xb6076c: stur            w0, [x1, #7]
    // 0xb60770: StoreField: r1->field_b = r0
    //     0xb60770: stur            w0, [x1, #0xb]
    // 0xb60774: StoreField: r1->field_f = r0
    //     0xb60774: stur            w0, [x1, #0xf]
    // 0xb60778: StoreField: r1->field_13 = r0
    //     0xb60778: stur            w0, [x1, #0x13]
    // 0xb6077c: r0 = RoundedRectangleBorder()
    //     0xb6077c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb60780: mov             x1, x0
    // 0xb60784: ldur            x0, [fp, #-0x48]
    // 0xb60788: StoreField: r1->field_b = r0
    //     0xb60788: stur            w0, [x1, #0xb]
    // 0xb6078c: r5 = Instance_BorderSide
    //     0xb6078c: add             x5, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb60790: ldr             x5, [x5, #0xe20]
    // 0xb60794: StoreField: r1->field_7 = r5
    //     0xb60794: stur            w5, [x1, #7]
    // 0xb60798: r16 = <RoundedRectangleBorder>
    //     0xb60798: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb6079c: ldr             x16, [x16, #0xf78]
    // 0xb607a0: stp             x1, x16, [SP]
    // 0xb607a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb607a4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb607a8: r0 = all()
    //     0xb607a8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb607ac: stur            x0, [fp, #-0x40]
    // 0xb607b0: r0 = ButtonStyle()
    //     0xb607b0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb607b4: mov             x2, x0
    // 0xb607b8: ldur            x0, [fp, #-0x30]
    // 0xb607bc: stur            x2, [fp, #-0x48]
    // 0xb607c0: StoreField: r2->field_23 = r0
    //     0xb607c0: stur            w0, [x2, #0x23]
    // 0xb607c4: ldur            x0, [fp, #-0x40]
    // 0xb607c8: StoreField: r2->field_43 = r0
    //     0xb607c8: stur            w0, [x2, #0x43]
    // 0xb607cc: ldur            x6, [fp, #-8]
    // 0xb607d0: LoadField: r0 = r6->field_f
    //     0xb607d0: ldur            w0, [x6, #0xf]
    // 0xb607d4: DecompressPointer r0
    //     0xb607d4: add             x0, x0, HEAP, lsl #32
    // 0xb607d8: LoadField: r1 = r0->field_b
    //     0xb607d8: ldur            w1, [x0, #0xb]
    // 0xb607dc: DecompressPointer r1
    //     0xb607dc: add             x1, x1, HEAP, lsl #32
    // 0xb607e0: cmp             w1, NULL
    // 0xb607e4: b.eq            #0xb61618
    // 0xb607e8: LoadField: r3 = r1->field_b
    //     0xb607e8: ldur            w3, [x1, #0xb]
    // 0xb607ec: DecompressPointer r3
    //     0xb607ec: add             x3, x3, HEAP, lsl #32
    // 0xb607f0: cmp             w3, NULL
    // 0xb607f4: b.ne            #0xb60800
    // 0xb607f8: r0 = Null
    //     0xb607f8: mov             x0, NULL
    // 0xb607fc: b               #0xb60844
    // 0xb60800: ldr             x7, [fp, #0x10]
    // 0xb60804: LoadField: r0 = r3->field_b
    //     0xb60804: ldur            w0, [x3, #0xb]
    // 0xb60808: r4 = LoadInt32Instr(r7)
    //     0xb60808: sbfx            x4, x7, #1, #0x1f
    //     0xb6080c: tbz             w7, #0, #0xb60814
    //     0xb60810: ldur            x4, [x7, #7]
    // 0xb60814: r1 = LoadInt32Instr(r0)
    //     0xb60814: sbfx            x1, x0, #1, #0x1f
    // 0xb60818: mov             x0, x1
    // 0xb6081c: mov             x1, x4
    // 0xb60820: cmp             x1, x0
    // 0xb60824: b.hs            #0xb6161c
    // 0xb60828: LoadField: r0 = r3->field_f
    //     0xb60828: ldur            w0, [x3, #0xf]
    // 0xb6082c: DecompressPointer r0
    //     0xb6082c: add             x0, x0, HEAP, lsl #32
    // 0xb60830: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb60830: add             x16, x0, x4, lsl #2
    //     0xb60834: ldur            w1, [x16, #0xf]
    // 0xb60838: DecompressPointer r1
    //     0xb60838: add             x1, x1, HEAP, lsl #32
    // 0xb6083c: LoadField: r0 = r1->field_f
    //     0xb6083c: ldur            w0, [x1, #0xf]
    // 0xb60840: DecompressPointer r0
    //     0xb60840: add             x0, x0, HEAP, lsl #32
    // 0xb60844: cmp             w0, NULL
    // 0xb60848: b.ne            #0xb60850
    // 0xb6084c: r0 = ""
    //     0xb6084c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb60850: ldr             x1, [fp, #0x18]
    // 0xb60854: stur            x0, [fp, #-0x30]
    // 0xb60858: r0 = of()
    //     0xb60858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6085c: LoadField: r1 = r0->field_87
    //     0xb6085c: ldur            w1, [x0, #0x87]
    // 0xb60860: DecompressPointer r1
    //     0xb60860: add             x1, x1, HEAP, lsl #32
    // 0xb60864: LoadField: r0 = r1->field_2f
    //     0xb60864: ldur            w0, [x1, #0x2f]
    // 0xb60868: DecompressPointer r0
    //     0xb60868: add             x0, x0, HEAP, lsl #32
    // 0xb6086c: cmp             w0, NULL
    // 0xb60870: b.ne            #0xb6087c
    // 0xb60874: r5 = Null
    //     0xb60874: mov             x5, NULL
    // 0xb60878: b               #0xb608a0
    // 0xb6087c: r16 = Instance_Color
    //     0xb6087c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb60880: r30 = 14.000000
    //     0xb60880: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb60884: ldr             lr, [lr, #0x1d8]
    // 0xb60888: stp             lr, x16, [SP]
    // 0xb6088c: mov             x1, x0
    // 0xb60890: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb60890: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb60894: ldr             x4, [x4, #0x9b8]
    // 0xb60898: r0 = copyWith()
    //     0xb60898: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6089c: mov             x5, x0
    // 0xb608a0: ldur            x4, [fp, #-0x10]
    // 0xb608a4: ldur            x2, [fp, #-0x28]
    // 0xb608a8: ldur            x0, [fp, #-0x48]
    // 0xb608ac: ldur            x1, [fp, #-0x30]
    // 0xb608b0: ldur            x3, [fp, #-0x20]
    // 0xb608b4: stur            x5, [fp, #-0x40]
    // 0xb608b8: r0 = Text()
    //     0xb608b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb608bc: mov             x3, x0
    // 0xb608c0: ldur            x0, [fp, #-0x30]
    // 0xb608c4: stur            x3, [fp, #-0x50]
    // 0xb608c8: StoreField: r3->field_b = r0
    //     0xb608c8: stur            w0, [x3, #0xb]
    // 0xb608cc: ldur            x0, [fp, #-0x40]
    // 0xb608d0: StoreField: r3->field_13 = r0
    //     0xb608d0: stur            w0, [x3, #0x13]
    // 0xb608d4: r1 = Function '<anonymous closure>':.
    //     0xb608d4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d50] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb608d8: ldr             x1, [x1, #0xd50]
    // 0xb608dc: r2 = Null
    //     0xb608dc: mov             x2, NULL
    // 0xb608e0: r0 = AllocateClosure()
    //     0xb608e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb608e4: stur            x0, [fp, #-0x30]
    // 0xb608e8: r0 = TextButton()
    //     0xb608e8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb608ec: mov             x1, x0
    // 0xb608f0: ldur            x0, [fp, #-0x30]
    // 0xb608f4: stur            x1, [fp, #-0x40]
    // 0xb608f8: StoreField: r1->field_b = r0
    //     0xb608f8: stur            w0, [x1, #0xb]
    // 0xb608fc: ldur            x0, [fp, #-0x48]
    // 0xb60900: StoreField: r1->field_1b = r0
    //     0xb60900: stur            w0, [x1, #0x1b]
    // 0xb60904: r0 = false
    //     0xb60904: add             x0, NULL, #0x30  ; false
    // 0xb60908: StoreField: r1->field_27 = r0
    //     0xb60908: stur            w0, [x1, #0x27]
    // 0xb6090c: r8 = true
    //     0xb6090c: add             x8, NULL, #0x20  ; true
    // 0xb60910: StoreField: r1->field_2f = r8
    //     0xb60910: stur            w8, [x1, #0x2f]
    // 0xb60914: ldur            x2, [fp, #-0x50]
    // 0xb60918: StoreField: r1->field_37 = r2
    //     0xb60918: stur            w2, [x1, #0x37]
    // 0xb6091c: r0 = TextButtonTheme()
    //     0xb6091c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb60920: mov             x1, x0
    // 0xb60924: ldur            x0, [fp, #-0x28]
    // 0xb60928: stur            x1, [fp, #-0x30]
    // 0xb6092c: StoreField: r1->field_f = r0
    //     0xb6092c: stur            w0, [x1, #0xf]
    // 0xb60930: ldur            x0, [fp, #-0x40]
    // 0xb60934: StoreField: r1->field_b = r0
    //     0xb60934: stur            w0, [x1, #0xb]
    // 0xb60938: r0 = Padding()
    //     0xb60938: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6093c: r9 = Instance_EdgeInsets
    //     0xb6093c: add             x9, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb60940: ldr             x9, [x9, #0x668]
    // 0xb60944: stur            x0, [fp, #-0x28]
    // 0xb60948: StoreField: r0->field_f = r9
    //     0xb60948: stur            w9, [x0, #0xf]
    // 0xb6094c: ldur            x1, [fp, #-0x30]
    // 0xb60950: StoreField: r0->field_b = r1
    //     0xb60950: stur            w1, [x0, #0xb]
    // 0xb60954: r0 = Visibility()
    //     0xb60954: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb60958: mov             x2, x0
    // 0xb6095c: ldur            x0, [fp, #-0x28]
    // 0xb60960: stur            x2, [fp, #-0x30]
    // 0xb60964: StoreField: r2->field_b = r0
    //     0xb60964: stur            w0, [x2, #0xb]
    // 0xb60968: r10 = Instance_SizedBox
    //     0xb60968: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb6096c: StoreField: r2->field_f = r10
    //     0xb6096c: stur            w10, [x2, #0xf]
    // 0xb60970: ldur            x0, [fp, #-0x10]
    // 0xb60974: StoreField: r2->field_13 = r0
    //     0xb60974: stur            w0, [x2, #0x13]
    // 0xb60978: r11 = false
    //     0xb60978: add             x11, NULL, #0x30  ; false
    // 0xb6097c: ArrayStore: r2[0] = r11  ; List_4
    //     0xb6097c: stur            w11, [x2, #0x17]
    // 0xb60980: StoreField: r2->field_1b = r11
    //     0xb60980: stur            w11, [x2, #0x1b]
    // 0xb60984: StoreField: r2->field_1f = r11
    //     0xb60984: stur            w11, [x2, #0x1f]
    // 0xb60988: StoreField: r2->field_23 = r11
    //     0xb60988: stur            w11, [x2, #0x23]
    // 0xb6098c: StoreField: r2->field_27 = r11
    //     0xb6098c: stur            w11, [x2, #0x27]
    // 0xb60990: StoreField: r2->field_2b = r11
    //     0xb60990: stur            w11, [x2, #0x2b]
    // 0xb60994: ldur            x0, [fp, #-0x20]
    // 0xb60998: LoadField: r1 = r0->field_b
    //     0xb60998: ldur            w1, [x0, #0xb]
    // 0xb6099c: LoadField: r3 = r0->field_f
    //     0xb6099c: ldur            w3, [x0, #0xf]
    // 0xb609a0: DecompressPointer r3
    //     0xb609a0: add             x3, x3, HEAP, lsl #32
    // 0xb609a4: LoadField: r4 = r3->field_b
    //     0xb609a4: ldur            w4, [x3, #0xb]
    // 0xb609a8: r3 = LoadInt32Instr(r1)
    //     0xb609a8: sbfx            x3, x1, #1, #0x1f
    // 0xb609ac: stur            x3, [fp, #-0x38]
    // 0xb609b0: r1 = LoadInt32Instr(r4)
    //     0xb609b0: sbfx            x1, x4, #1, #0x1f
    // 0xb609b4: cmp             x3, x1
    // 0xb609b8: b.ne            #0xb609c4
    // 0xb609bc: mov             x1, x0
    // 0xb609c0: r0 = _growToNextCapacity()
    //     0xb609c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb609c4: ldur            x4, [fp, #-0x18]
    // 0xb609c8: ldur            x2, [fp, #-0x20]
    // 0xb609cc: ldur            x3, [fp, #-0x38]
    // 0xb609d0: add             x0, x3, #1
    // 0xb609d4: lsl             x1, x0, #1
    // 0xb609d8: StoreField: r2->field_b = r1
    //     0xb609d8: stur            w1, [x2, #0xb]
    // 0xb609dc: LoadField: r1 = r2->field_f
    //     0xb609dc: ldur            w1, [x2, #0xf]
    // 0xb609e0: DecompressPointer r1
    //     0xb609e0: add             x1, x1, HEAP, lsl #32
    // 0xb609e4: ldur            x0, [fp, #-0x30]
    // 0xb609e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb609e8: add             x25, x1, x3, lsl #2
    //     0xb609ec: add             x25, x25, #0xf
    //     0xb609f0: str             w0, [x25]
    //     0xb609f4: tbz             w0, #0, #0xb60a10
    //     0xb609f8: ldurb           w16, [x1, #-1]
    //     0xb609fc: ldurb           w17, [x0, #-1]
    //     0xb60a00: and             x16, x17, x16, lsr #2
    //     0xb60a04: tst             x16, HEAP, lsr #32
    //     0xb60a08: b.eq            #0xb60a10
    //     0xb60a0c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb60a10: r0 = Column()
    //     0xb60a10: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb60a14: r12 = Instance_Axis
    //     0xb60a14: ldr             x12, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb60a18: stur            x0, [fp, #-0x10]
    // 0xb60a1c: StoreField: r0->field_f = r12
    //     0xb60a1c: stur            w12, [x0, #0xf]
    // 0xb60a20: r13 = Instance_MainAxisAlignment
    //     0xb60a20: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb60a24: ldr             x13, [x13, #0xab0]
    // 0xb60a28: StoreField: r0->field_13 = r13
    //     0xb60a28: stur            w13, [x0, #0x13]
    // 0xb60a2c: r1 = Instance_MainAxisSize
    //     0xb60a2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb60a30: ldr             x1, [x1, #0xa10]
    // 0xb60a34: ArrayStore: r0[0] = r1  ; List_4
    //     0xb60a34: stur            w1, [x0, #0x17]
    // 0xb60a38: r14 = Instance_CrossAxisAlignment
    //     0xb60a38: add             x14, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb60a3c: ldr             x14, [x14, #0x890]
    // 0xb60a40: StoreField: r0->field_1b = r14
    //     0xb60a40: stur            w14, [x0, #0x1b]
    // 0xb60a44: r2 = Instance_VerticalDirection
    //     0xb60a44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb60a48: ldr             x2, [x2, #0xa20]
    // 0xb60a4c: StoreField: r0->field_23 = r2
    //     0xb60a4c: stur            w2, [x0, #0x23]
    // 0xb60a50: r3 = Instance_Clip
    //     0xb60a50: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb60a54: ldr             x3, [x3, #0x38]
    // 0xb60a58: StoreField: r0->field_2b = r3
    //     0xb60a58: stur            w3, [x0, #0x2b]
    // 0xb60a5c: StoreField: r0->field_2f = rZR
    //     0xb60a5c: stur            xzr, [x0, #0x2f]
    // 0xb60a60: ldur            x4, [fp, #-0x20]
    // 0xb60a64: StoreField: r0->field_b = r4
    //     0xb60a64: stur            w4, [x0, #0xb]
    // 0xb60a68: r0 = Padding()
    //     0xb60a68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb60a6c: r19 = Instance_EdgeInsets
    //     0xb60a6c: add             x19, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb60a70: ldr             x19, [x19, #0xa78]
    // 0xb60a74: stur            x0, [fp, #-0x20]
    // 0xb60a78: StoreField: r0->field_f = r19
    //     0xb60a78: stur            w19, [x0, #0xf]
    // 0xb60a7c: ldur            x1, [fp, #-0x10]
    // 0xb60a80: StoreField: r0->field_b = r1
    //     0xb60a80: stur            w1, [x0, #0xb]
    // 0xb60a84: r1 = <FlexParentData>
    //     0xb60a84: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb60a88: ldr             x1, [x1, #0xe00]
    // 0xb60a8c: r0 = Expanded()
    //     0xb60a8c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb60a90: r20 = 2
    //     0xb60a90: movz            x20, #0x2
    // 0xb60a94: stur            x0, [fp, #-0x10]
    // 0xb60a98: StoreField: r0->field_13 = r20
    //     0xb60a98: stur            x20, [x0, #0x13]
    // 0xb60a9c: r23 = Instance_FlexFit
    //     0xb60a9c: add             x23, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb60aa0: ldr             x23, [x23, #0xe08]
    // 0xb60aa4: StoreField: r0->field_1b = r23
    //     0xb60aa4: stur            w23, [x0, #0x1b]
    // 0xb60aa8: ldur            x1, [fp, #-0x20]
    // 0xb60aac: StoreField: r0->field_b = r1
    //     0xb60aac: stur            w1, [x0, #0xb]
    // 0xb60ab0: r1 = Null
    //     0xb60ab0: mov             x1, NULL
    // 0xb60ab4: r2 = 4
    //     0xb60ab4: movz            x2, #0x4
    // 0xb60ab8: r0 = AllocateArray()
    //     0xb60ab8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb60abc: mov             x2, x0
    // 0xb60ac0: ldur            x0, [fp, #-0x18]
    // 0xb60ac4: stur            x2, [fp, #-0x20]
    // 0xb60ac8: StoreField: r2->field_f = r0
    //     0xb60ac8: stur            w0, [x2, #0xf]
    // 0xb60acc: ldur            x0, [fp, #-0x10]
    // 0xb60ad0: StoreField: r2->field_13 = r0
    //     0xb60ad0: stur            w0, [x2, #0x13]
    // 0xb60ad4: r1 = <Widget>
    //     0xb60ad4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb60ad8: r0 = AllocateGrowableArray()
    //     0xb60ad8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb60adc: mov             x1, x0
    // 0xb60ae0: ldur            x0, [fp, #-0x20]
    // 0xb60ae4: stur            x1, [fp, #-0x10]
    // 0xb60ae8: StoreField: r1->field_f = r0
    //     0xb60ae8: stur            w0, [x1, #0xf]
    // 0xb60aec: r24 = 4
    //     0xb60aec: movz            x24, #0x4
    // 0xb60af0: StoreField: r1->field_b = r24
    //     0xb60af0: stur            w24, [x1, #0xb]
    // 0xb60af4: r0 = Row()
    //     0xb60af4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb60af8: r25 = Instance_Axis
    //     0xb60af8: ldr             x25, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb60afc: StoreField: r0->field_f = r25
    //     0xb60afc: stur            w25, [x0, #0xf]
    // 0xb60b00: r1 = Instance_MainAxisAlignment
    //     0xb60b00: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb60b04: ldr             x1, [x1, #0xa8]
    // 0xb60b08: StoreField: r0->field_13 = r1
    //     0xb60b08: stur            w1, [x0, #0x13]
    // 0xb60b0c: r1 = Instance_MainAxisSize
    //     0xb60b0c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb60b10: ldr             x1, [x1, #0xa10]
    // 0xb60b14: ArrayStore: r0[0] = r1  ; List_4
    //     0xb60b14: stur            w1, [x0, #0x17]
    // 0xb60b18: r1 = Instance_CrossAxisAlignment
    //     0xb60b18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb60b1c: ldr             x1, [x1, #0xa18]
    // 0xb60b20: StoreField: r0->field_1b = r1
    //     0xb60b20: stur            w1, [x0, #0x1b]
    // 0xb60b24: r1 = Instance_VerticalDirection
    //     0xb60b24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb60b28: ldr             x1, [x1, #0xa20]
    // 0xb60b2c: StoreField: r0->field_23 = r1
    //     0xb60b2c: stur            w1, [x0, #0x23]
    // 0xb60b30: r1 = Instance_Clip
    //     0xb60b30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb60b34: ldr             x1, [x1, #0x38]
    // 0xb60b38: StoreField: r0->field_2b = r1
    //     0xb60b38: stur            w1, [x0, #0x2b]
    // 0xb60b3c: StoreField: r0->field_2f = rZR
    //     0xb60b3c: stur            xzr, [x0, #0x2f]
    // 0xb60b40: ldur            x1, [fp, #-0x10]
    // 0xb60b44: StoreField: r0->field_b = r1
    //     0xb60b44: stur            w1, [x0, #0xb]
    // 0xb60b48: b               #0xb615b8
    // 0xb60b4c: ldr             x7, [fp, #0x10]
    // 0xb60b50: ldur            x6, [fp, #-8]
    // 0xb60b54: r8 = true
    //     0xb60b54: add             x8, NULL, #0x20  ; true
    // 0xb60b58: r9 = Instance_EdgeInsets
    //     0xb60b58: add             x9, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb60b5c: ldr             x9, [x9, #0x668]
    // 0xb60b60: r14 = Instance_CrossAxisAlignment
    //     0xb60b60: add             x14, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb60b64: ldr             x14, [x14, #0x890]
    // 0xb60b68: r13 = Instance_MainAxisAlignment
    //     0xb60b68: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb60b6c: ldr             x13, [x13, #0xab0]
    // 0xb60b70: r19 = Instance_EdgeInsets
    //     0xb60b70: add             x19, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb60b74: ldr             x19, [x19, #0xa78]
    // 0xb60b78: r24 = 4
    //     0xb60b78: movz            x24, #0x4
    // 0xb60b7c: r1 = Instance_MainAxisAlignment
    //     0xb60b7c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb60b80: ldr             x1, [x1, #0xa8]
    // 0xb60b84: r11 = false
    //     0xb60b84: add             x11, NULL, #0x30  ; false
    // 0xb60b88: r2 = Instance_TextOverflow
    //     0xb60b88: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb60b8c: ldr             x2, [x2, #0xe10]
    // 0xb60b90: r3 = Instance_Clip
    //     0xb60b90: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb60b94: ldr             x3, [x3, #0x138]
    // 0xb60b98: r23 = Instance_FlexFit
    //     0xb60b98: add             x23, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb60b9c: ldr             x23, [x23, #0xe08]
    // 0xb60ba0: r4 = 2
    //     0xb60ba0: movz            x4, #0x2
    // 0xb60ba4: r25 = Instance_Axis
    //     0xb60ba4: ldr             x25, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb60ba8: r10 = Instance_SizedBox
    //     0xb60ba8: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb60bac: r12 = Instance_Axis
    //     0xb60bac: ldr             x12, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb60bb0: r5 = Instance_BorderSide
    //     0xb60bb0: add             x5, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb60bb4: ldr             x5, [x5, #0xe20]
    // 0xb60bb8: d0 = 20.000000
    //     0xb60bb8: fmov            d0, #20.00000000
    // 0xb60bbc: r20 = 2
    //     0xb60bbc: movz            x20, #0x2
    // 0xb60bc0: LoadField: r0 = r6->field_f
    //     0xb60bc0: ldur            w0, [x6, #0xf]
    // 0xb60bc4: DecompressPointer r0
    //     0xb60bc4: add             x0, x0, HEAP, lsl #32
    // 0xb60bc8: LoadField: r2 = r0->field_b
    //     0xb60bc8: ldur            w2, [x0, #0xb]
    // 0xb60bcc: DecompressPointer r2
    //     0xb60bcc: add             x2, x2, HEAP, lsl #32
    // 0xb60bd0: cmp             w2, NULL
    // 0xb60bd4: b.eq            #0xb61620
    // 0xb60bd8: LoadField: r0 = r2->field_b
    //     0xb60bd8: ldur            w0, [x2, #0xb]
    // 0xb60bdc: DecompressPointer r0
    //     0xb60bdc: add             x0, x0, HEAP, lsl #32
    // 0xb60be0: cmp             w0, NULL
    // 0xb60be4: b.ne            #0xb60bf0
    // 0xb60be8: r0 = Null
    //     0xb60be8: mov             x0, NULL
    // 0xb60bec: b               #0xb60c34
    // 0xb60bf0: LoadField: r2 = r0->field_b
    //     0xb60bf0: ldur            w2, [x0, #0xb]
    // 0xb60bf4: r3 = LoadInt32Instr(r7)
    //     0xb60bf4: sbfx            x3, x7, #1, #0x1f
    //     0xb60bf8: tbz             w7, #0, #0xb60c00
    //     0xb60bfc: ldur            x3, [x7, #7]
    // 0xb60c00: r4 = LoadInt32Instr(r2)
    //     0xb60c00: sbfx            x4, x2, #1, #0x1f
    // 0xb60c04: mov             x2, x0
    // 0xb60c08: mov             x0, x4
    // 0xb60c0c: mov             x1, x3
    // 0xb60c10: cmp             x1, x0
    // 0xb60c14: b.hs            #0xb61624
    // 0xb60c18: LoadField: r0 = r2->field_f
    //     0xb60c18: ldur            w0, [x2, #0xf]
    // 0xb60c1c: DecompressPointer r0
    //     0xb60c1c: add             x0, x0, HEAP, lsl #32
    // 0xb60c20: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb60c20: add             x16, x0, x3, lsl #2
    //     0xb60c24: ldur            w1, [x16, #0xf]
    // 0xb60c28: DecompressPointer r1
    //     0xb60c28: add             x1, x1, HEAP, lsl #32
    // 0xb60c2c: LoadField: r0 = r1->field_7
    //     0xb60c2c: ldur            w0, [x1, #7]
    // 0xb60c30: DecompressPointer r0
    //     0xb60c30: add             x0, x0, HEAP, lsl #32
    // 0xb60c34: cmp             w0, NULL
    // 0xb60c38: b.ne            #0xb60c40
    // 0xb60c3c: r0 = ""
    //     0xb60c3c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb60c40: ldr             x1, [fp, #0x18]
    // 0xb60c44: stur            x0, [fp, #-0x10]
    // 0xb60c48: r0 = of()
    //     0xb60c48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb60c4c: LoadField: r1 = r0->field_87
    //     0xb60c4c: ldur            w1, [x0, #0x87]
    // 0xb60c50: DecompressPointer r1
    //     0xb60c50: add             x1, x1, HEAP, lsl #32
    // 0xb60c54: LoadField: r0 = r1->field_7
    //     0xb60c54: ldur            w0, [x1, #7]
    // 0xb60c58: DecompressPointer r0
    //     0xb60c58: add             x0, x0, HEAP, lsl #32
    // 0xb60c5c: stur            x0, [fp, #-0x18]
    // 0xb60c60: r1 = Instance_Color
    //     0xb60c60: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb60c64: d0 = 0.700000
    //     0xb60c64: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb60c68: ldr             d0, [x17, #0xf48]
    // 0xb60c6c: r0 = withOpacity()
    //     0xb60c6c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb60c70: r16 = 16.000000
    //     0xb60c70: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb60c74: ldr             x16, [x16, #0x188]
    // 0xb60c78: stp             x0, x16, [SP]
    // 0xb60c7c: ldur            x1, [fp, #-0x18]
    // 0xb60c80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb60c80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb60c84: ldr             x4, [x4, #0xaa0]
    // 0xb60c88: r0 = copyWith()
    //     0xb60c88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb60c8c: stur            x0, [fp, #-0x18]
    // 0xb60c90: r0 = Text()
    //     0xb60c90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb60c94: mov             x3, x0
    // 0xb60c98: ldur            x0, [fp, #-0x10]
    // 0xb60c9c: stur            x3, [fp, #-0x20]
    // 0xb60ca0: StoreField: r3->field_b = r0
    //     0xb60ca0: stur            w0, [x3, #0xb]
    // 0xb60ca4: ldur            x0, [fp, #-0x18]
    // 0xb60ca8: StoreField: r3->field_13 = r0
    //     0xb60ca8: stur            w0, [x3, #0x13]
    // 0xb60cac: r1 = Null
    //     0xb60cac: mov             x1, NULL
    // 0xb60cb0: r2 = 2
    //     0xb60cb0: movz            x2, #0x2
    // 0xb60cb4: r0 = AllocateArray()
    //     0xb60cb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb60cb8: mov             x2, x0
    // 0xb60cbc: ldur            x0, [fp, #-0x20]
    // 0xb60cc0: stur            x2, [fp, #-0x10]
    // 0xb60cc4: StoreField: r2->field_f = r0
    //     0xb60cc4: stur            w0, [x2, #0xf]
    // 0xb60cc8: r1 = <Widget>
    //     0xb60cc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb60ccc: r0 = AllocateGrowableArray()
    //     0xb60ccc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb60cd0: mov             x2, x0
    // 0xb60cd4: ldur            x0, [fp, #-0x10]
    // 0xb60cd8: stur            x2, [fp, #-0x18]
    // 0xb60cdc: StoreField: r2->field_f = r0
    //     0xb60cdc: stur            w0, [x2, #0xf]
    // 0xb60ce0: r0 = 2
    //     0xb60ce0: movz            x0, #0x2
    // 0xb60ce4: StoreField: r2->field_b = r0
    //     0xb60ce4: stur            w0, [x2, #0xb]
    // 0xb60ce8: ldur            x3, [fp, #-8]
    // 0xb60cec: LoadField: r0 = r3->field_f
    //     0xb60cec: ldur            w0, [x3, #0xf]
    // 0xb60cf0: DecompressPointer r0
    //     0xb60cf0: add             x0, x0, HEAP, lsl #32
    // 0xb60cf4: LoadField: r1 = r0->field_b
    //     0xb60cf4: ldur            w1, [x0, #0xb]
    // 0xb60cf8: DecompressPointer r1
    //     0xb60cf8: add             x1, x1, HEAP, lsl #32
    // 0xb60cfc: cmp             w1, NULL
    // 0xb60d00: b.eq            #0xb61628
    // 0xb60d04: LoadField: r4 = r1->field_b
    //     0xb60d04: ldur            w4, [x1, #0xb]
    // 0xb60d08: DecompressPointer r4
    //     0xb60d08: add             x4, x4, HEAP, lsl #32
    // 0xb60d0c: cmp             w4, NULL
    // 0xb60d10: b.ne            #0xb60d20
    // 0xb60d14: ldr             x5, [fp, #0x10]
    // 0xb60d18: r0 = Null
    //     0xb60d18: mov             x0, NULL
    // 0xb60d1c: b               #0xb60d88
    // 0xb60d20: ldr             x5, [fp, #0x10]
    // 0xb60d24: LoadField: r0 = r4->field_b
    //     0xb60d24: ldur            w0, [x4, #0xb]
    // 0xb60d28: r6 = LoadInt32Instr(r5)
    //     0xb60d28: sbfx            x6, x5, #1, #0x1f
    //     0xb60d2c: tbz             w5, #0, #0xb60d34
    //     0xb60d30: ldur            x6, [x5, #7]
    // 0xb60d34: r1 = LoadInt32Instr(r0)
    //     0xb60d34: sbfx            x1, x0, #1, #0x1f
    // 0xb60d38: mov             x0, x1
    // 0xb60d3c: mov             x1, x6
    // 0xb60d40: cmp             x1, x0
    // 0xb60d44: b.hs            #0xb6162c
    // 0xb60d48: LoadField: r0 = r4->field_f
    //     0xb60d48: ldur            w0, [x4, #0xf]
    // 0xb60d4c: DecompressPointer r0
    //     0xb60d4c: add             x0, x0, HEAP, lsl #32
    // 0xb60d50: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb60d50: add             x16, x0, x6, lsl #2
    //     0xb60d54: ldur            w1, [x16, #0xf]
    // 0xb60d58: DecompressPointer r1
    //     0xb60d58: add             x1, x1, HEAP, lsl #32
    // 0xb60d5c: LoadField: r0 = r1->field_b
    //     0xb60d5c: ldur            w0, [x1, #0xb]
    // 0xb60d60: DecompressPointer r0
    //     0xb60d60: add             x0, x0, HEAP, lsl #32
    // 0xb60d64: cmp             w0, NULL
    // 0xb60d68: b.ne            #0xb60d74
    // 0xb60d6c: r0 = Null
    //     0xb60d6c: mov             x0, NULL
    // 0xb60d70: b               #0xb60d88
    // 0xb60d74: LoadField: r1 = r0->field_7
    //     0xb60d74: ldur            w1, [x0, #7]
    // 0xb60d78: cbnz            w1, #0xb60d84
    // 0xb60d7c: r0 = false
    //     0xb60d7c: add             x0, NULL, #0x30  ; false
    // 0xb60d80: b               #0xb60d88
    // 0xb60d84: r0 = true
    //     0xb60d84: add             x0, NULL, #0x20  ; true
    // 0xb60d88: cmp             w0, NULL
    // 0xb60d8c: b.eq            #0xb60f28
    // 0xb60d90: tbnz            w0, #4, #0xb60f28
    // 0xb60d94: cmp             w4, NULL
    // 0xb60d98: b.ne            #0xb60da4
    // 0xb60d9c: r0 = Null
    //     0xb60d9c: mov             x0, NULL
    // 0xb60da0: b               #0xb60de4
    // 0xb60da4: LoadField: r0 = r4->field_b
    //     0xb60da4: ldur            w0, [x4, #0xb]
    // 0xb60da8: r6 = LoadInt32Instr(r5)
    //     0xb60da8: sbfx            x6, x5, #1, #0x1f
    //     0xb60dac: tbz             w5, #0, #0xb60db4
    //     0xb60db0: ldur            x6, [x5, #7]
    // 0xb60db4: r1 = LoadInt32Instr(r0)
    //     0xb60db4: sbfx            x1, x0, #1, #0x1f
    // 0xb60db8: mov             x0, x1
    // 0xb60dbc: mov             x1, x6
    // 0xb60dc0: cmp             x1, x0
    // 0xb60dc4: b.hs            #0xb61630
    // 0xb60dc8: LoadField: r0 = r4->field_f
    //     0xb60dc8: ldur            w0, [x4, #0xf]
    // 0xb60dcc: DecompressPointer r0
    //     0xb60dcc: add             x0, x0, HEAP, lsl #32
    // 0xb60dd0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb60dd0: add             x16, x0, x6, lsl #2
    //     0xb60dd4: ldur            w1, [x16, #0xf]
    // 0xb60dd8: DecompressPointer r1
    //     0xb60dd8: add             x1, x1, HEAP, lsl #32
    // 0xb60ddc: LoadField: r0 = r1->field_b
    //     0xb60ddc: ldur            w0, [x1, #0xb]
    // 0xb60de0: DecompressPointer r0
    //     0xb60de0: add             x0, x0, HEAP, lsl #32
    // 0xb60de4: cmp             w0, NULL
    // 0xb60de8: b.ne            #0xb60df4
    // 0xb60dec: r1 = ""
    //     0xb60dec: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb60df0: b               #0xb60df8
    // 0xb60df4: mov             x1, x0
    // 0xb60df8: r0 = parse()
    //     0xb60df8: bl              #0x9a0ae4  ; [package:html/parser.dart] ::parse
    // 0xb60dfc: mov             x1, x0
    // 0xb60e00: r0 = body()
    //     0xb60e00: bl              #0x99fd18  ; [package:html/dom.dart] Document::body
    // 0xb60e04: cmp             w0, NULL
    // 0xb60e08: b.eq            #0xb61634
    // 0xb60e0c: mov             x1, x0
    // 0xb60e10: r0 = _getText()
    //     0xb60e10: bl              #0x99f830  ; [package:html/dom.dart] ::_getText
    // 0xb60e14: ldr             x1, [fp, #0x18]
    // 0xb60e18: stur            x0, [fp, #-0x10]
    // 0xb60e1c: r0 = of()
    //     0xb60e1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb60e20: LoadField: r1 = r0->field_87
    //     0xb60e20: ldur            w1, [x0, #0x87]
    // 0xb60e24: DecompressPointer r1
    //     0xb60e24: add             x1, x1, HEAP, lsl #32
    // 0xb60e28: LoadField: r0 = r1->field_2b
    //     0xb60e28: ldur            w0, [x1, #0x2b]
    // 0xb60e2c: DecompressPointer r0
    //     0xb60e2c: add             x0, x0, HEAP, lsl #32
    // 0xb60e30: stur            x0, [fp, #-0x20]
    // 0xb60e34: r1 = Instance_Color
    //     0xb60e34: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb60e38: d0 = 0.400000
    //     0xb60e38: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb60e3c: r0 = withOpacity()
    //     0xb60e3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb60e40: r16 = 12.000000
    //     0xb60e40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb60e44: ldr             x16, [x16, #0x9e8]
    // 0xb60e48: stp             x16, x0, [SP]
    // 0xb60e4c: ldur            x1, [fp, #-0x20]
    // 0xb60e50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb60e50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb60e54: ldr             x4, [x4, #0x9b8]
    // 0xb60e58: r0 = copyWith()
    //     0xb60e58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb60e5c: stur            x0, [fp, #-0x20]
    // 0xb60e60: r0 = Text()
    //     0xb60e60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb60e64: mov             x1, x0
    // 0xb60e68: ldur            x0, [fp, #-0x10]
    // 0xb60e6c: stur            x1, [fp, #-0x28]
    // 0xb60e70: StoreField: r1->field_b = r0
    //     0xb60e70: stur            w0, [x1, #0xb]
    // 0xb60e74: ldur            x0, [fp, #-0x20]
    // 0xb60e78: StoreField: r1->field_13 = r0
    //     0xb60e78: stur            w0, [x1, #0x13]
    // 0xb60e7c: r0 = Instance_TextOverflow
    //     0xb60e7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb60e80: ldr             x0, [x0, #0xe10]
    // 0xb60e84: StoreField: r1->field_2b = r0
    //     0xb60e84: stur            w0, [x1, #0x2b]
    // 0xb60e88: r2 = 4
    //     0xb60e88: movz            x2, #0x4
    // 0xb60e8c: StoreField: r1->field_37 = r2
    //     0xb60e8c: stur            w2, [x1, #0x37]
    // 0xb60e90: r0 = Padding()
    //     0xb60e90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb60e94: mov             x2, x0
    // 0xb60e98: r0 = Instance_EdgeInsets
    //     0xb60e98: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb60e9c: ldr             x0, [x0, #0x668]
    // 0xb60ea0: stur            x2, [fp, #-0x10]
    // 0xb60ea4: StoreField: r2->field_f = r0
    //     0xb60ea4: stur            w0, [x2, #0xf]
    // 0xb60ea8: ldur            x1, [fp, #-0x28]
    // 0xb60eac: StoreField: r2->field_b = r1
    //     0xb60eac: stur            w1, [x2, #0xb]
    // 0xb60eb0: ldur            x3, [fp, #-0x18]
    // 0xb60eb4: LoadField: r1 = r3->field_b
    //     0xb60eb4: ldur            w1, [x3, #0xb]
    // 0xb60eb8: LoadField: r4 = r3->field_f
    //     0xb60eb8: ldur            w4, [x3, #0xf]
    // 0xb60ebc: DecompressPointer r4
    //     0xb60ebc: add             x4, x4, HEAP, lsl #32
    // 0xb60ec0: LoadField: r5 = r4->field_b
    //     0xb60ec0: ldur            w5, [x4, #0xb]
    // 0xb60ec4: r4 = LoadInt32Instr(r1)
    //     0xb60ec4: sbfx            x4, x1, #1, #0x1f
    // 0xb60ec8: stur            x4, [fp, #-0x38]
    // 0xb60ecc: r1 = LoadInt32Instr(r5)
    //     0xb60ecc: sbfx            x1, x5, #1, #0x1f
    // 0xb60ed0: cmp             x4, x1
    // 0xb60ed4: b.ne            #0xb60ee0
    // 0xb60ed8: mov             x1, x3
    // 0xb60edc: r0 = _growToNextCapacity()
    //     0xb60edc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb60ee0: ldur            x2, [fp, #-0x18]
    // 0xb60ee4: ldur            x3, [fp, #-0x38]
    // 0xb60ee8: add             x0, x3, #1
    // 0xb60eec: lsl             x1, x0, #1
    // 0xb60ef0: StoreField: r2->field_b = r1
    //     0xb60ef0: stur            w1, [x2, #0xb]
    // 0xb60ef4: LoadField: r1 = r2->field_f
    //     0xb60ef4: ldur            w1, [x2, #0xf]
    // 0xb60ef8: DecompressPointer r1
    //     0xb60ef8: add             x1, x1, HEAP, lsl #32
    // 0xb60efc: ldur            x0, [fp, #-0x10]
    // 0xb60f00: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb60f00: add             x25, x1, x3, lsl #2
    //     0xb60f04: add             x25, x25, #0xf
    //     0xb60f08: str             w0, [x25]
    //     0xb60f0c: tbz             w0, #0, #0xb60f28
    //     0xb60f10: ldurb           w16, [x1, #-1]
    //     0xb60f14: ldurb           w17, [x0, #-1]
    //     0xb60f18: and             x16, x17, x16, lsr #2
    //     0xb60f1c: tst             x16, HEAP, lsr #32
    //     0xb60f20: b.eq            #0xb60f28
    //     0xb60f24: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb60f28: ldur            x3, [fp, #-8]
    // 0xb60f2c: LoadField: r0 = r3->field_f
    //     0xb60f2c: ldur            w0, [x3, #0xf]
    // 0xb60f30: DecompressPointer r0
    //     0xb60f30: add             x0, x0, HEAP, lsl #32
    // 0xb60f34: LoadField: r1 = r0->field_b
    //     0xb60f34: ldur            w1, [x0, #0xb]
    // 0xb60f38: DecompressPointer r1
    //     0xb60f38: add             x1, x1, HEAP, lsl #32
    // 0xb60f3c: cmp             w1, NULL
    // 0xb60f40: b.eq            #0xb61638
    // 0xb60f44: LoadField: r4 = r1->field_b
    //     0xb60f44: ldur            w4, [x1, #0xb]
    // 0xb60f48: DecompressPointer r4
    //     0xb60f48: add             x4, x4, HEAP, lsl #32
    // 0xb60f4c: cmp             w4, NULL
    // 0xb60f50: b.ne            #0xb60f60
    // 0xb60f54: ldr             x5, [fp, #0x10]
    // 0xb60f58: r0 = Null
    //     0xb60f58: mov             x0, NULL
    // 0xb60f5c: b               #0xb60fc8
    // 0xb60f60: ldr             x5, [fp, #0x10]
    // 0xb60f64: LoadField: r0 = r4->field_b
    //     0xb60f64: ldur            w0, [x4, #0xb]
    // 0xb60f68: r6 = LoadInt32Instr(r5)
    //     0xb60f68: sbfx            x6, x5, #1, #0x1f
    //     0xb60f6c: tbz             w5, #0, #0xb60f74
    //     0xb60f70: ldur            x6, [x5, #7]
    // 0xb60f74: r1 = LoadInt32Instr(r0)
    //     0xb60f74: sbfx            x1, x0, #1, #0x1f
    // 0xb60f78: mov             x0, x1
    // 0xb60f7c: mov             x1, x6
    // 0xb60f80: cmp             x1, x0
    // 0xb60f84: b.hs            #0xb6163c
    // 0xb60f88: LoadField: r0 = r4->field_f
    //     0xb60f88: ldur            w0, [x4, #0xf]
    // 0xb60f8c: DecompressPointer r0
    //     0xb60f8c: add             x0, x0, HEAP, lsl #32
    // 0xb60f90: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb60f90: add             x16, x0, x6, lsl #2
    //     0xb60f94: ldur            w1, [x16, #0xf]
    // 0xb60f98: DecompressPointer r1
    //     0xb60f98: add             x1, x1, HEAP, lsl #32
    // 0xb60f9c: LoadField: r0 = r1->field_f
    //     0xb60f9c: ldur            w0, [x1, #0xf]
    // 0xb60fa0: DecompressPointer r0
    //     0xb60fa0: add             x0, x0, HEAP, lsl #32
    // 0xb60fa4: cmp             w0, NULL
    // 0xb60fa8: b.ne            #0xb60fb4
    // 0xb60fac: r0 = Null
    //     0xb60fac: mov             x0, NULL
    // 0xb60fb0: b               #0xb60fc8
    // 0xb60fb4: LoadField: r1 = r0->field_7
    //     0xb60fb4: ldur            w1, [x0, #7]
    // 0xb60fb8: cbnz            w1, #0xb60fc4
    // 0xb60fbc: r0 = false
    //     0xb60fbc: add             x0, NULL, #0x30  ; false
    // 0xb60fc0: b               #0xb60fc8
    // 0xb60fc4: r0 = true
    //     0xb60fc4: add             x0, NULL, #0x20  ; true
    // 0xb60fc8: cmp             w0, NULL
    // 0xb60fcc: b.ne            #0xb60fd4
    // 0xb60fd0: r0 = false
    //     0xb60fd0: add             x0, NULL, #0x30  ; false
    // 0xb60fd4: ldr             x1, [fp, #0x18]
    // 0xb60fd8: stur            x0, [fp, #-0x10]
    // 0xb60fdc: r0 = of()
    //     0xb60fdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb60fe0: r17 = 307
    //     0xb60fe0: movz            x17, #0x133
    // 0xb60fe4: ldr             w1, [x0, x17]
    // 0xb60fe8: DecompressPointer r1
    //     0xb60fe8: add             x1, x1, HEAP, lsl #32
    // 0xb60fec: stur            x1, [fp, #-0x20]
    // 0xb60ff0: r16 = <EdgeInsets>
    //     0xb60ff0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb60ff4: ldr             x16, [x16, #0xda0]
    // 0xb60ff8: r30 = Instance_EdgeInsets
    //     0xb60ff8: add             lr, PP, #0x55, lsl #12  ; [pp+0x55d48] Obj!EdgeInsets@d58ee1
    //     0xb60ffc: ldr             lr, [lr, #0xd48]
    // 0xb61000: stp             lr, x16, [SP]
    // 0xb61004: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb61004: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb61008: r0 = all()
    //     0xb61008: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb6100c: stur            x0, [fp, #-0x28]
    // 0xb61010: r0 = Radius()
    //     0xb61010: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb61014: d0 = 20.000000
    //     0xb61014: fmov            d0, #20.00000000
    // 0xb61018: stur            x0, [fp, #-0x30]
    // 0xb6101c: StoreField: r0->field_7 = d0
    //     0xb6101c: stur            d0, [x0, #7]
    // 0xb61020: StoreField: r0->field_f = d0
    //     0xb61020: stur            d0, [x0, #0xf]
    // 0xb61024: r0 = BorderRadius()
    //     0xb61024: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb61028: mov             x1, x0
    // 0xb6102c: ldur            x0, [fp, #-0x30]
    // 0xb61030: stur            x1, [fp, #-0x40]
    // 0xb61034: StoreField: r1->field_7 = r0
    //     0xb61034: stur            w0, [x1, #7]
    // 0xb61038: StoreField: r1->field_b = r0
    //     0xb61038: stur            w0, [x1, #0xb]
    // 0xb6103c: StoreField: r1->field_f = r0
    //     0xb6103c: stur            w0, [x1, #0xf]
    // 0xb61040: StoreField: r1->field_13 = r0
    //     0xb61040: stur            w0, [x1, #0x13]
    // 0xb61044: r0 = RoundedRectangleBorder()
    //     0xb61044: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb61048: mov             x1, x0
    // 0xb6104c: ldur            x0, [fp, #-0x40]
    // 0xb61050: StoreField: r1->field_b = r0
    //     0xb61050: stur            w0, [x1, #0xb]
    // 0xb61054: r0 = Instance_BorderSide
    //     0xb61054: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb61058: ldr             x0, [x0, #0xe20]
    // 0xb6105c: StoreField: r1->field_7 = r0
    //     0xb6105c: stur            w0, [x1, #7]
    // 0xb61060: r16 = <RoundedRectangleBorder>
    //     0xb61060: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb61064: ldr             x16, [x16, #0xf78]
    // 0xb61068: stp             x1, x16, [SP]
    // 0xb6106c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6106c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb61070: r0 = all()
    //     0xb61070: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb61074: stur            x0, [fp, #-0x30]
    // 0xb61078: r0 = ButtonStyle()
    //     0xb61078: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb6107c: mov             x2, x0
    // 0xb61080: ldur            x0, [fp, #-0x28]
    // 0xb61084: stur            x2, [fp, #-0x40]
    // 0xb61088: StoreField: r2->field_23 = r0
    //     0xb61088: stur            w0, [x2, #0x23]
    // 0xb6108c: ldur            x0, [fp, #-0x30]
    // 0xb61090: StoreField: r2->field_43 = r0
    //     0xb61090: stur            w0, [x2, #0x43]
    // 0xb61094: ldur            x3, [fp, #-8]
    // 0xb61098: LoadField: r0 = r3->field_f
    //     0xb61098: ldur            w0, [x3, #0xf]
    // 0xb6109c: DecompressPointer r0
    //     0xb6109c: add             x0, x0, HEAP, lsl #32
    // 0xb610a0: LoadField: r1 = r0->field_b
    //     0xb610a0: ldur            w1, [x0, #0xb]
    // 0xb610a4: DecompressPointer r1
    //     0xb610a4: add             x1, x1, HEAP, lsl #32
    // 0xb610a8: cmp             w1, NULL
    // 0xb610ac: b.eq            #0xb61640
    // 0xb610b0: LoadField: r4 = r1->field_b
    //     0xb610b0: ldur            w4, [x1, #0xb]
    // 0xb610b4: DecompressPointer r4
    //     0xb610b4: add             x4, x4, HEAP, lsl #32
    // 0xb610b8: cmp             w4, NULL
    // 0xb610bc: b.ne            #0xb610cc
    // 0xb610c0: ldr             x5, [fp, #0x10]
    // 0xb610c4: r0 = Null
    //     0xb610c4: mov             x0, NULL
    // 0xb610c8: b               #0xb61110
    // 0xb610cc: ldr             x5, [fp, #0x10]
    // 0xb610d0: LoadField: r0 = r4->field_b
    //     0xb610d0: ldur            w0, [x4, #0xb]
    // 0xb610d4: r6 = LoadInt32Instr(r5)
    //     0xb610d4: sbfx            x6, x5, #1, #0x1f
    //     0xb610d8: tbz             w5, #0, #0xb610e0
    //     0xb610dc: ldur            x6, [x5, #7]
    // 0xb610e0: r1 = LoadInt32Instr(r0)
    //     0xb610e0: sbfx            x1, x0, #1, #0x1f
    // 0xb610e4: mov             x0, x1
    // 0xb610e8: mov             x1, x6
    // 0xb610ec: cmp             x1, x0
    // 0xb610f0: b.hs            #0xb61644
    // 0xb610f4: LoadField: r0 = r4->field_f
    //     0xb610f4: ldur            w0, [x4, #0xf]
    // 0xb610f8: DecompressPointer r0
    //     0xb610f8: add             x0, x0, HEAP, lsl #32
    // 0xb610fc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb610fc: add             x16, x0, x6, lsl #2
    //     0xb61100: ldur            w1, [x16, #0xf]
    // 0xb61104: DecompressPointer r1
    //     0xb61104: add             x1, x1, HEAP, lsl #32
    // 0xb61108: LoadField: r0 = r1->field_f
    //     0xb61108: ldur            w0, [x1, #0xf]
    // 0xb6110c: DecompressPointer r0
    //     0xb6110c: add             x0, x0, HEAP, lsl #32
    // 0xb61110: cmp             w0, NULL
    // 0xb61114: b.ne            #0xb6111c
    // 0xb61118: r0 = ""
    //     0xb61118: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6111c: ldr             x1, [fp, #0x18]
    // 0xb61120: stur            x0, [fp, #-0x28]
    // 0xb61124: r0 = of()
    //     0xb61124: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb61128: LoadField: r1 = r0->field_87
    //     0xb61128: ldur            w1, [x0, #0x87]
    // 0xb6112c: DecompressPointer r1
    //     0xb6112c: add             x1, x1, HEAP, lsl #32
    // 0xb61130: LoadField: r0 = r1->field_2f
    //     0xb61130: ldur            w0, [x1, #0x2f]
    // 0xb61134: DecompressPointer r0
    //     0xb61134: add             x0, x0, HEAP, lsl #32
    // 0xb61138: cmp             w0, NULL
    // 0xb6113c: b.ne            #0xb61148
    // 0xb61140: r5 = Null
    //     0xb61140: mov             x5, NULL
    // 0xb61144: b               #0xb6116c
    // 0xb61148: r16 = Instance_Color
    //     0xb61148: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6114c: r30 = 14.000000
    //     0xb6114c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb61150: ldr             lr, [lr, #0x1d8]
    // 0xb61154: stp             lr, x16, [SP]
    // 0xb61158: mov             x1, x0
    // 0xb6115c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb6115c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb61160: ldr             x4, [x4, #0x9b8]
    // 0xb61164: r0 = copyWith()
    //     0xb61164: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb61168: mov             x5, x0
    // 0xb6116c: ldur            x4, [fp, #-0x10]
    // 0xb61170: ldur            x2, [fp, #-0x20]
    // 0xb61174: ldur            x0, [fp, #-0x40]
    // 0xb61178: ldur            x1, [fp, #-0x28]
    // 0xb6117c: ldur            x3, [fp, #-0x18]
    // 0xb61180: stur            x5, [fp, #-0x30]
    // 0xb61184: r0 = Text()
    //     0xb61184: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb61188: mov             x3, x0
    // 0xb6118c: ldur            x0, [fp, #-0x28]
    // 0xb61190: stur            x3, [fp, #-0x48]
    // 0xb61194: StoreField: r3->field_b = r0
    //     0xb61194: stur            w0, [x3, #0xb]
    // 0xb61198: ldur            x0, [fp, #-0x30]
    // 0xb6119c: StoreField: r3->field_13 = r0
    //     0xb6119c: stur            w0, [x3, #0x13]
    // 0xb611a0: r1 = Function '<anonymous closure>':.
    //     0xb611a0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d58] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb611a4: ldr             x1, [x1, #0xd58]
    // 0xb611a8: r2 = Null
    //     0xb611a8: mov             x2, NULL
    // 0xb611ac: r0 = AllocateClosure()
    //     0xb611ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb611b0: stur            x0, [fp, #-0x28]
    // 0xb611b4: r0 = TextButton()
    //     0xb611b4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb611b8: mov             x1, x0
    // 0xb611bc: ldur            x0, [fp, #-0x28]
    // 0xb611c0: stur            x1, [fp, #-0x30]
    // 0xb611c4: StoreField: r1->field_b = r0
    //     0xb611c4: stur            w0, [x1, #0xb]
    // 0xb611c8: ldur            x0, [fp, #-0x40]
    // 0xb611cc: StoreField: r1->field_1b = r0
    //     0xb611cc: stur            w0, [x1, #0x1b]
    // 0xb611d0: r0 = false
    //     0xb611d0: add             x0, NULL, #0x30  ; false
    // 0xb611d4: StoreField: r1->field_27 = r0
    //     0xb611d4: stur            w0, [x1, #0x27]
    // 0xb611d8: r2 = true
    //     0xb611d8: add             x2, NULL, #0x20  ; true
    // 0xb611dc: StoreField: r1->field_2f = r2
    //     0xb611dc: stur            w2, [x1, #0x2f]
    // 0xb611e0: ldur            x2, [fp, #-0x48]
    // 0xb611e4: StoreField: r1->field_37 = r2
    //     0xb611e4: stur            w2, [x1, #0x37]
    // 0xb611e8: r0 = TextButtonTheme()
    //     0xb611e8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb611ec: mov             x1, x0
    // 0xb611f0: ldur            x0, [fp, #-0x20]
    // 0xb611f4: stur            x1, [fp, #-0x28]
    // 0xb611f8: StoreField: r1->field_f = r0
    //     0xb611f8: stur            w0, [x1, #0xf]
    // 0xb611fc: ldur            x0, [fp, #-0x30]
    // 0xb61200: StoreField: r1->field_b = r0
    //     0xb61200: stur            w0, [x1, #0xb]
    // 0xb61204: r0 = Padding()
    //     0xb61204: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb61208: mov             x1, x0
    // 0xb6120c: r0 = Instance_EdgeInsets
    //     0xb6120c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb61210: ldr             x0, [x0, #0x668]
    // 0xb61214: stur            x1, [fp, #-0x20]
    // 0xb61218: StoreField: r1->field_f = r0
    //     0xb61218: stur            w0, [x1, #0xf]
    // 0xb6121c: ldur            x0, [fp, #-0x28]
    // 0xb61220: StoreField: r1->field_b = r0
    //     0xb61220: stur            w0, [x1, #0xb]
    // 0xb61224: r0 = Visibility()
    //     0xb61224: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb61228: mov             x2, x0
    // 0xb6122c: ldur            x0, [fp, #-0x20]
    // 0xb61230: stur            x2, [fp, #-0x28]
    // 0xb61234: StoreField: r2->field_b = r0
    //     0xb61234: stur            w0, [x2, #0xb]
    // 0xb61238: r0 = Instance_SizedBox
    //     0xb61238: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb6123c: StoreField: r2->field_f = r0
    //     0xb6123c: stur            w0, [x2, #0xf]
    // 0xb61240: ldur            x0, [fp, #-0x10]
    // 0xb61244: StoreField: r2->field_13 = r0
    //     0xb61244: stur            w0, [x2, #0x13]
    // 0xb61248: r0 = false
    //     0xb61248: add             x0, NULL, #0x30  ; false
    // 0xb6124c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6124c: stur            w0, [x2, #0x17]
    // 0xb61250: StoreField: r2->field_1b = r0
    //     0xb61250: stur            w0, [x2, #0x1b]
    // 0xb61254: StoreField: r2->field_1f = r0
    //     0xb61254: stur            w0, [x2, #0x1f]
    // 0xb61258: StoreField: r2->field_23 = r0
    //     0xb61258: stur            w0, [x2, #0x23]
    // 0xb6125c: StoreField: r2->field_27 = r0
    //     0xb6125c: stur            w0, [x2, #0x27]
    // 0xb61260: StoreField: r2->field_2b = r0
    //     0xb61260: stur            w0, [x2, #0x2b]
    // 0xb61264: ldur            x0, [fp, #-0x18]
    // 0xb61268: LoadField: r1 = r0->field_b
    //     0xb61268: ldur            w1, [x0, #0xb]
    // 0xb6126c: LoadField: r3 = r0->field_f
    //     0xb6126c: ldur            w3, [x0, #0xf]
    // 0xb61270: DecompressPointer r3
    //     0xb61270: add             x3, x3, HEAP, lsl #32
    // 0xb61274: LoadField: r4 = r3->field_b
    //     0xb61274: ldur            w4, [x3, #0xb]
    // 0xb61278: r3 = LoadInt32Instr(r1)
    //     0xb61278: sbfx            x3, x1, #1, #0x1f
    // 0xb6127c: stur            x3, [fp, #-0x38]
    // 0xb61280: r1 = LoadInt32Instr(r4)
    //     0xb61280: sbfx            x1, x4, #1, #0x1f
    // 0xb61284: cmp             x3, x1
    // 0xb61288: b.ne            #0xb61294
    // 0xb6128c: mov             x1, x0
    // 0xb61290: r0 = _growToNextCapacity()
    //     0xb61290: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb61294: ldur            x4, [fp, #-8]
    // 0xb61298: ldur            x2, [fp, #-0x18]
    // 0xb6129c: ldur            x3, [fp, #-0x38]
    // 0xb612a0: add             x0, x3, #1
    // 0xb612a4: lsl             x1, x0, #1
    // 0xb612a8: StoreField: r2->field_b = r1
    //     0xb612a8: stur            w1, [x2, #0xb]
    // 0xb612ac: LoadField: r1 = r2->field_f
    //     0xb612ac: ldur            w1, [x2, #0xf]
    // 0xb612b0: DecompressPointer r1
    //     0xb612b0: add             x1, x1, HEAP, lsl #32
    // 0xb612b4: ldur            x0, [fp, #-0x28]
    // 0xb612b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb612b8: add             x25, x1, x3, lsl #2
    //     0xb612bc: add             x25, x25, #0xf
    //     0xb612c0: str             w0, [x25]
    //     0xb612c4: tbz             w0, #0, #0xb612e0
    //     0xb612c8: ldurb           w16, [x1, #-1]
    //     0xb612cc: ldurb           w17, [x0, #-1]
    //     0xb612d0: and             x16, x17, x16, lsr #2
    //     0xb612d4: tst             x16, HEAP, lsr #32
    //     0xb612d8: b.eq            #0xb612e0
    //     0xb612dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb612e0: r0 = Column()
    //     0xb612e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb612e4: mov             x1, x0
    // 0xb612e8: r0 = Instance_Axis
    //     0xb612e8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb612ec: stur            x1, [fp, #-0x10]
    // 0xb612f0: StoreField: r1->field_f = r0
    //     0xb612f0: stur            w0, [x1, #0xf]
    // 0xb612f4: r0 = Instance_MainAxisAlignment
    //     0xb612f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb612f8: ldr             x0, [x0, #0xab0]
    // 0xb612fc: StoreField: r1->field_13 = r0
    //     0xb612fc: stur            w0, [x1, #0x13]
    // 0xb61300: r0 = Instance_MainAxisSize
    //     0xb61300: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb61304: ldr             x0, [x0, #0xa10]
    // 0xb61308: ArrayStore: r1[0] = r0  ; List_4
    //     0xb61308: stur            w0, [x1, #0x17]
    // 0xb6130c: r2 = Instance_CrossAxisAlignment
    //     0xb6130c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb61310: ldr             x2, [x2, #0x890]
    // 0xb61314: StoreField: r1->field_1b = r2
    //     0xb61314: stur            w2, [x1, #0x1b]
    // 0xb61318: r2 = Instance_VerticalDirection
    //     0xb61318: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6131c: ldr             x2, [x2, #0xa20]
    // 0xb61320: StoreField: r1->field_23 = r2
    //     0xb61320: stur            w2, [x1, #0x23]
    // 0xb61324: r3 = Instance_Clip
    //     0xb61324: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb61328: ldr             x3, [x3, #0x38]
    // 0xb6132c: StoreField: r1->field_2b = r3
    //     0xb6132c: stur            w3, [x1, #0x2b]
    // 0xb61330: StoreField: r1->field_2f = rZR
    //     0xb61330: stur            xzr, [x1, #0x2f]
    // 0xb61334: ldur            x4, [fp, #-0x18]
    // 0xb61338: StoreField: r1->field_b = r4
    //     0xb61338: stur            w4, [x1, #0xb]
    // 0xb6133c: r0 = Padding()
    //     0xb6133c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb61340: mov             x2, x0
    // 0xb61344: r0 = Instance_EdgeInsets
    //     0xb61344: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb61348: ldr             x0, [x0, #0xa78]
    // 0xb6134c: stur            x2, [fp, #-0x18]
    // 0xb61350: StoreField: r2->field_f = r0
    //     0xb61350: stur            w0, [x2, #0xf]
    // 0xb61354: ldur            x0, [fp, #-0x10]
    // 0xb61358: StoreField: r2->field_b = r0
    //     0xb61358: stur            w0, [x2, #0xb]
    // 0xb6135c: r1 = <FlexParentData>
    //     0xb6135c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb61360: ldr             x1, [x1, #0xe00]
    // 0xb61364: r0 = Expanded()
    //     0xb61364: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb61368: mov             x1, x0
    // 0xb6136c: r0 = 2
    //     0xb6136c: movz            x0, #0x2
    // 0xb61370: stur            x1, [fp, #-0x10]
    // 0xb61374: StoreField: r1->field_13 = r0
    //     0xb61374: stur            x0, [x1, #0x13]
    // 0xb61378: r2 = Instance_FlexFit
    //     0xb61378: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb6137c: ldr             x2, [x2, #0xe08]
    // 0xb61380: StoreField: r1->field_1b = r2
    //     0xb61380: stur            w2, [x1, #0x1b]
    // 0xb61384: ldur            x3, [fp, #-0x18]
    // 0xb61388: StoreField: r1->field_b = r3
    //     0xb61388: stur            w3, [x1, #0xb]
    // 0xb6138c: r0 = Radius()
    //     0xb6138c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb61390: d0 = 20.000000
    //     0xb61390: fmov            d0, #20.00000000
    // 0xb61394: stur            x0, [fp, #-0x18]
    // 0xb61398: StoreField: r0->field_7 = d0
    //     0xb61398: stur            d0, [x0, #7]
    // 0xb6139c: StoreField: r0->field_f = d0
    //     0xb6139c: stur            d0, [x0, #0xf]
    // 0xb613a0: r0 = BorderRadius()
    //     0xb613a0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb613a4: mov             x3, x0
    // 0xb613a8: ldur            x0, [fp, #-0x18]
    // 0xb613ac: stur            x3, [fp, #-0x20]
    // 0xb613b0: StoreField: r3->field_7 = r0
    //     0xb613b0: stur            w0, [x3, #7]
    // 0xb613b4: StoreField: r3->field_b = r0
    //     0xb613b4: stur            w0, [x3, #0xb]
    // 0xb613b8: StoreField: r3->field_f = r0
    //     0xb613b8: stur            w0, [x3, #0xf]
    // 0xb613bc: StoreField: r3->field_13 = r0
    //     0xb613bc: stur            w0, [x3, #0x13]
    // 0xb613c0: ldur            x0, [fp, #-8]
    // 0xb613c4: LoadField: r1 = r0->field_f
    //     0xb613c4: ldur            w1, [x0, #0xf]
    // 0xb613c8: DecompressPointer r1
    //     0xb613c8: add             x1, x1, HEAP, lsl #32
    // 0xb613cc: LoadField: r0 = r1->field_b
    //     0xb613cc: ldur            w0, [x1, #0xb]
    // 0xb613d0: DecompressPointer r0
    //     0xb613d0: add             x0, x0, HEAP, lsl #32
    // 0xb613d4: cmp             w0, NULL
    // 0xb613d8: b.eq            #0xb61648
    // 0xb613dc: LoadField: r2 = r0->field_b
    //     0xb613dc: ldur            w2, [x0, #0xb]
    // 0xb613e0: DecompressPointer r2
    //     0xb613e0: add             x2, x2, HEAP, lsl #32
    // 0xb613e4: cmp             w2, NULL
    // 0xb613e8: b.ne            #0xb613f4
    // 0xb613ec: r0 = Null
    //     0xb613ec: mov             x0, NULL
    // 0xb613f0: b               #0xb61434
    // 0xb613f4: ldr             x0, [fp, #0x10]
    // 0xb613f8: LoadField: r1 = r2->field_b
    //     0xb613f8: ldur            w1, [x2, #0xb]
    // 0xb613fc: r4 = LoadInt32Instr(r0)
    //     0xb613fc: sbfx            x4, x0, #1, #0x1f
    //     0xb61400: tbz             w0, #0, #0xb61408
    //     0xb61404: ldur            x4, [x0, #7]
    // 0xb61408: r0 = LoadInt32Instr(r1)
    //     0xb61408: sbfx            x0, x1, #1, #0x1f
    // 0xb6140c: mov             x1, x4
    // 0xb61410: cmp             x1, x0
    // 0xb61414: b.hs            #0xb6164c
    // 0xb61418: LoadField: r0 = r2->field_f
    //     0xb61418: ldur            w0, [x2, #0xf]
    // 0xb6141c: DecompressPointer r0
    //     0xb6141c: add             x0, x0, HEAP, lsl #32
    // 0xb61420: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb61420: add             x16, x0, x4, lsl #2
    //     0xb61424: ldur            w1, [x16, #0xf]
    // 0xb61428: DecompressPointer r1
    //     0xb61428: add             x1, x1, HEAP, lsl #32
    // 0xb6142c: LoadField: r0 = r1->field_13
    //     0xb6142c: ldur            w0, [x1, #0x13]
    // 0xb61430: DecompressPointer r0
    //     0xb61430: add             x0, x0, HEAP, lsl #32
    // 0xb61434: cmp             w0, NULL
    // 0xb61438: b.ne            #0xb61444
    // 0xb6143c: r4 = ""
    //     0xb6143c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb61440: b               #0xb61448
    // 0xb61444: mov             x4, x0
    // 0xb61448: ldur            x0, [fp, #-0x10]
    // 0xb6144c: stur            x4, [fp, #-8]
    // 0xb61450: r1 = Function '<anonymous closure>':.
    //     0xb61450: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d60] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb61454: ldr             x1, [x1, #0xd60]
    // 0xb61458: r2 = Null
    //     0xb61458: mov             x2, NULL
    // 0xb6145c: r0 = AllocateClosure()
    //     0xb6145c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb61460: r1 = Function '<anonymous closure>':.
    //     0xb61460: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d68] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb61464: ldr             x1, [x1, #0xd68]
    // 0xb61468: r2 = Null
    //     0xb61468: mov             x2, NULL
    // 0xb6146c: stur            x0, [fp, #-0x18]
    // 0xb61470: r0 = AllocateClosure()
    //     0xb61470: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb61474: stur            x0, [fp, #-0x28]
    // 0xb61478: r0 = CachedNetworkImage()
    //     0xb61478: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb6147c: stur            x0, [fp, #-0x30]
    // 0xb61480: r16 = 180.000000
    //     0xb61480: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c88] 180
    //     0xb61484: ldr             x16, [x16, #0xc88]
    // 0xb61488: r30 = 150.000000
    //     0xb61488: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb6148c: ldr             lr, [lr, #0x690]
    // 0xb61490: stp             lr, x16, [SP, #0x18]
    // 0xb61494: ldur            x16, [fp, #-0x18]
    // 0xb61498: ldur            lr, [fp, #-0x28]
    // 0xb6149c: stp             lr, x16, [SP, #8]
    // 0xb614a0: r16 = Instance_BoxFit
    //     0xb614a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb614a4: ldr             x16, [x16, #0x118]
    // 0xb614a8: str             x16, [SP]
    // 0xb614ac: mov             x1, x0
    // 0xb614b0: ldur            x2, [fp, #-8]
    // 0xb614b4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb614b4: add             x4, PP, #0x54, lsl #12  ; [pp+0x542b0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb614b8: ldr             x4, [x4, #0x2b0]
    // 0xb614bc: r0 = CachedNetworkImage()
    //     0xb614bc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb614c0: r0 = ClipRRect()
    //     0xb614c0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb614c4: mov             x2, x0
    // 0xb614c8: ldur            x0, [fp, #-0x20]
    // 0xb614cc: stur            x2, [fp, #-8]
    // 0xb614d0: StoreField: r2->field_f = r0
    //     0xb614d0: stur            w0, [x2, #0xf]
    // 0xb614d4: r0 = Instance_Clip
    //     0xb614d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb614d8: ldr             x0, [x0, #0x138]
    // 0xb614dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb614dc: stur            w0, [x2, #0x17]
    // 0xb614e0: ldur            x0, [fp, #-0x30]
    // 0xb614e4: StoreField: r2->field_b = r0
    //     0xb614e4: stur            w0, [x2, #0xb]
    // 0xb614e8: r1 = <FlexParentData>
    //     0xb614e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb614ec: ldr             x1, [x1, #0xe00]
    // 0xb614f0: r0 = Expanded()
    //     0xb614f0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb614f4: mov             x3, x0
    // 0xb614f8: r0 = 2
    //     0xb614f8: movz            x0, #0x2
    // 0xb614fc: stur            x3, [fp, #-0x18]
    // 0xb61500: StoreField: r3->field_13 = r0
    //     0xb61500: stur            x0, [x3, #0x13]
    // 0xb61504: r0 = Instance_FlexFit
    //     0xb61504: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb61508: ldr             x0, [x0, #0xe08]
    // 0xb6150c: StoreField: r3->field_1b = r0
    //     0xb6150c: stur            w0, [x3, #0x1b]
    // 0xb61510: ldur            x0, [fp, #-8]
    // 0xb61514: StoreField: r3->field_b = r0
    //     0xb61514: stur            w0, [x3, #0xb]
    // 0xb61518: r1 = Null
    //     0xb61518: mov             x1, NULL
    // 0xb6151c: r2 = 4
    //     0xb6151c: movz            x2, #0x4
    // 0xb61520: r0 = AllocateArray()
    //     0xb61520: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb61524: mov             x2, x0
    // 0xb61528: ldur            x0, [fp, #-0x10]
    // 0xb6152c: stur            x2, [fp, #-8]
    // 0xb61530: StoreField: r2->field_f = r0
    //     0xb61530: stur            w0, [x2, #0xf]
    // 0xb61534: ldur            x0, [fp, #-0x18]
    // 0xb61538: StoreField: r2->field_13 = r0
    //     0xb61538: stur            w0, [x2, #0x13]
    // 0xb6153c: r1 = <Widget>
    //     0xb6153c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb61540: r0 = AllocateGrowableArray()
    //     0xb61540: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb61544: mov             x1, x0
    // 0xb61548: ldur            x0, [fp, #-8]
    // 0xb6154c: stur            x1, [fp, #-0x10]
    // 0xb61550: StoreField: r1->field_f = r0
    //     0xb61550: stur            w0, [x1, #0xf]
    // 0xb61554: r0 = 4
    //     0xb61554: movz            x0, #0x4
    // 0xb61558: StoreField: r1->field_b = r0
    //     0xb61558: stur            w0, [x1, #0xb]
    // 0xb6155c: r0 = Row()
    //     0xb6155c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb61560: mov             x1, x0
    // 0xb61564: r0 = Instance_Axis
    //     0xb61564: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb61568: StoreField: r1->field_f = r0
    //     0xb61568: stur            w0, [x1, #0xf]
    // 0xb6156c: r0 = Instance_MainAxisAlignment
    //     0xb6156c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb61570: ldr             x0, [x0, #0xa8]
    // 0xb61574: StoreField: r1->field_13 = r0
    //     0xb61574: stur            w0, [x1, #0x13]
    // 0xb61578: r0 = Instance_MainAxisSize
    //     0xb61578: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6157c: ldr             x0, [x0, #0xa10]
    // 0xb61580: ArrayStore: r1[0] = r0  ; List_4
    //     0xb61580: stur            w0, [x1, #0x17]
    // 0xb61584: r0 = Instance_CrossAxisAlignment
    //     0xb61584: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb61588: ldr             x0, [x0, #0xa18]
    // 0xb6158c: StoreField: r1->field_1b = r0
    //     0xb6158c: stur            w0, [x1, #0x1b]
    // 0xb61590: r0 = Instance_VerticalDirection
    //     0xb61590: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb61594: ldr             x0, [x0, #0xa20]
    // 0xb61598: StoreField: r1->field_23 = r0
    //     0xb61598: stur            w0, [x1, #0x23]
    // 0xb6159c: r0 = Instance_Clip
    //     0xb6159c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb615a0: ldr             x0, [x0, #0x38]
    // 0xb615a4: StoreField: r1->field_2b = r0
    //     0xb615a4: stur            w0, [x1, #0x2b]
    // 0xb615a8: StoreField: r1->field_2f = rZR
    //     0xb615a8: stur            xzr, [x1, #0x2f]
    // 0xb615ac: ldur            x0, [fp, #-0x10]
    // 0xb615b0: StoreField: r1->field_b = r0
    //     0xb615b0: stur            w0, [x1, #0xb]
    // 0xb615b4: mov             x0, x1
    // 0xb615b8: stur            x0, [fp, #-8]
    // 0xb615bc: r0 = Padding()
    //     0xb615bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb615c0: r1 = Instance_EdgeInsets
    //     0xb615c0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb615c4: ldr             x1, [x1, #0x878]
    // 0xb615c8: StoreField: r0->field_f = r1
    //     0xb615c8: stur            w1, [x0, #0xf]
    // 0xb615cc: ldur            x1, [fp, #-8]
    // 0xb615d0: StoreField: r0->field_b = r1
    //     0xb615d0: stur            w1, [x0, #0xb]
    // 0xb615d4: LeaveFrame
    //     0xb615d4: mov             SP, fp
    //     0xb615d8: ldp             fp, lr, [SP], #0x10
    // 0xb615dc: ret
    //     0xb615dc: ret             
    // 0xb615e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb615e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb615e4: b               #0xb60088
    // 0xb615e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb615e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb615ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb615ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb615f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb615f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb615f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb615f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb615f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb615f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb615fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb615fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61600: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61600: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb61604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb61604: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61608: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb61608: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6160c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6160c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb61610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61610: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb61614: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb61614: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61618: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6161c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6161c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61620: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb61620: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb61624: r0 = RangeErrorSharedWithFPURegs()
    //     0xb61624: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xb61628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61628: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6162c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6162c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb61630: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61634: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb61638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61638: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6163c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6163c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61640: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61640: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb61644: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb61644: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb61648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb61648: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6164c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6164c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4078, size: 0x10, field offset: 0xc
//   const constructor, 
class ContentTextAndMedia extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f2c8, size: 0x24
    // 0xc7f2c8: EnterFrame
    //     0xc7f2c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f2cc: mov             fp, SP
    // 0xc7f2d0: mov             x0, x1
    // 0xc7f2d4: r1 = <ContentTextAndMedia>
    //     0xc7f2d4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48828] TypeArguments: <ContentTextAndMedia>
    //     0xc7f2d8: ldr             x1, [x1, #0x828]
    // 0xc7f2dc: r0 = _ContentTextAndMediaState()
    //     0xc7f2dc: bl              #0xc7f2ec  ; Allocate_ContentTextAndMediaStateStub -> _ContentTextAndMediaState (size=0x14)
    // 0xc7f2e0: LeaveFrame
    //     0xc7f2e0: mov             SP, fp
    //     0xc7f2e4: ldp             fp, lr, [SP], #0x10
    // 0xc7f2e8: ret
    //     0xc7f2e8: ret             
  }
}
