// lib: , url: package:customer_app/app/presentation/views/line/post_order/order_detail/cancel_return_order_bottom_sheet.dart

// class id: 1049541, size: 0x8
class :: {
}

// class id: 3233, size: 0x14, field offset: 0x14
class _CancelReturnOrderBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbfb9d8, size: 0x884
    // 0xbfb9d8: EnterFrame
    //     0xbfb9d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbfb9dc: mov             fp, SP
    // 0xbfb9e0: AllocStack(0x60)
    //     0xbfb9e0: sub             SP, SP, #0x60
    // 0xbfb9e4: SetupParameters(_CancelReturnOrderBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbfb9e4: mov             x0, x1
    //     0xbfb9e8: stur            x1, [fp, #-8]
    //     0xbfb9ec: mov             x1, x2
    //     0xbfb9f0: stur            x2, [fp, #-0x10]
    // 0xbfb9f4: CheckStackOverflow
    //     0xbfb9f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfb9f8: cmp             SP, x16
    //     0xbfb9fc: b.ls            #0xbfc248
    // 0xbfba00: r1 = 2
    //     0xbfba00: movz            x1, #0x2
    // 0xbfba04: r0 = AllocateContext()
    //     0xbfba04: bl              #0x16f6108  ; AllocateContextStub
    // 0xbfba08: mov             x2, x0
    // 0xbfba0c: ldur            x0, [fp, #-8]
    // 0xbfba10: stur            x2, [fp, #-0x20]
    // 0xbfba14: StoreField: r2->field_f = r0
    //     0xbfba14: stur            w0, [x2, #0xf]
    // 0xbfba18: ldur            x1, [fp, #-0x10]
    // 0xbfba1c: StoreField: r2->field_13 = r1
    //     0xbfba1c: stur            w1, [x2, #0x13]
    // 0xbfba20: LoadField: r3 = r0->field_b
    //     0xbfba20: ldur            w3, [x0, #0xb]
    // 0xbfba24: DecompressPointer r3
    //     0xbfba24: add             x3, x3, HEAP, lsl #32
    // 0xbfba28: cmp             w3, NULL
    // 0xbfba2c: b.eq            #0xbfc250
    // 0xbfba30: LoadField: r4 = r3->field_b
    //     0xbfba30: ldur            w4, [x3, #0xb]
    // 0xbfba34: DecompressPointer r4
    //     0xbfba34: add             x4, x4, HEAP, lsl #32
    // 0xbfba38: cmp             w4, NULL
    // 0xbfba3c: b.ne            #0xbfba48
    // 0xbfba40: r3 = Null
    //     0xbfba40: mov             x3, NULL
    // 0xbfba44: b               #0xbfba50
    // 0xbfba48: LoadField: r3 = r4->field_7
    //     0xbfba48: ldur            w3, [x4, #7]
    // 0xbfba4c: DecompressPointer r3
    //     0xbfba4c: add             x3, x3, HEAP, lsl #32
    // 0xbfba50: cmp             w3, NULL
    // 0xbfba54: b.ne            #0xbfba5c
    // 0xbfba58: r3 = ""
    //     0xbfba58: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfba5c: stur            x3, [fp, #-0x18]
    // 0xbfba60: r0 = of()
    //     0xbfba60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfba64: LoadField: r1 = r0->field_87
    //     0xbfba64: ldur            w1, [x0, #0x87]
    // 0xbfba68: DecompressPointer r1
    //     0xbfba68: add             x1, x1, HEAP, lsl #32
    // 0xbfba6c: LoadField: r0 = r1->field_7
    //     0xbfba6c: ldur            w0, [x1, #7]
    // 0xbfba70: DecompressPointer r0
    //     0xbfba70: add             x0, x0, HEAP, lsl #32
    // 0xbfba74: r16 = Instance_Color
    //     0xbfba74: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfba78: r30 = 16.000000
    //     0xbfba78: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbfba7c: ldr             lr, [lr, #0x188]
    // 0xbfba80: stp             lr, x16, [SP]
    // 0xbfba84: mov             x1, x0
    // 0xbfba88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfba88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfba8c: ldr             x4, [x4, #0x9b8]
    // 0xbfba90: r0 = copyWith()
    //     0xbfba90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfba94: stur            x0, [fp, #-0x10]
    // 0xbfba98: r0 = Text()
    //     0xbfba98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfba9c: mov             x1, x0
    // 0xbfbaa0: ldur            x0, [fp, #-0x18]
    // 0xbfbaa4: stur            x1, [fp, #-0x28]
    // 0xbfbaa8: StoreField: r1->field_b = r0
    //     0xbfbaa8: stur            w0, [x1, #0xb]
    // 0xbfbaac: ldur            x0, [fp, #-0x10]
    // 0xbfbab0: StoreField: r1->field_13 = r0
    //     0xbfbab0: stur            w0, [x1, #0x13]
    // 0xbfbab4: r0 = SvgPicture()
    //     0xbfbab4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbfbab8: mov             x1, x0
    // 0xbfbabc: r2 = "assets/images/x.svg"
    //     0xbfbabc: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbfbac0: ldr             x2, [x2, #0x5e8]
    // 0xbfbac4: stur            x0, [fp, #-0x10]
    // 0xbfbac8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbfbac8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbfbacc: r0 = SvgPicture.asset()
    //     0xbfbacc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbfbad0: r0 = InkWell()
    //     0xbfbad0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbfbad4: mov             x3, x0
    // 0xbfbad8: ldur            x0, [fp, #-0x10]
    // 0xbfbadc: stur            x3, [fp, #-0x18]
    // 0xbfbae0: StoreField: r3->field_b = r0
    //     0xbfbae0: stur            w0, [x3, #0xb]
    // 0xbfbae4: ldur            x2, [fp, #-0x20]
    // 0xbfbae8: r1 = Function '<anonymous closure>':.
    //     0xbfbae8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c130] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbfbaec: ldr             x1, [x1, #0x130]
    // 0xbfbaf0: r0 = AllocateClosure()
    //     0xbfbaf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfbaf4: mov             x1, x0
    // 0xbfbaf8: ldur            x0, [fp, #-0x18]
    // 0xbfbafc: StoreField: r0->field_f = r1
    //     0xbfbafc: stur            w1, [x0, #0xf]
    // 0xbfbb00: r3 = true
    //     0xbfbb00: add             x3, NULL, #0x20  ; true
    // 0xbfbb04: StoreField: r0->field_43 = r3
    //     0xbfbb04: stur            w3, [x0, #0x43]
    // 0xbfbb08: r1 = Instance_BoxShape
    //     0xbfbb08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfbb0c: ldr             x1, [x1, #0x80]
    // 0xbfbb10: StoreField: r0->field_47 = r1
    //     0xbfbb10: stur            w1, [x0, #0x47]
    // 0xbfbb14: StoreField: r0->field_6f = r3
    //     0xbfbb14: stur            w3, [x0, #0x6f]
    // 0xbfbb18: r4 = false
    //     0xbfbb18: add             x4, NULL, #0x30  ; false
    // 0xbfbb1c: StoreField: r0->field_73 = r4
    //     0xbfbb1c: stur            w4, [x0, #0x73]
    // 0xbfbb20: StoreField: r0->field_83 = r3
    //     0xbfbb20: stur            w3, [x0, #0x83]
    // 0xbfbb24: StoreField: r0->field_7b = r4
    //     0xbfbb24: stur            w4, [x0, #0x7b]
    // 0xbfbb28: r1 = Null
    //     0xbfbb28: mov             x1, NULL
    // 0xbfbb2c: r2 = 4
    //     0xbfbb2c: movz            x2, #0x4
    // 0xbfbb30: r0 = AllocateArray()
    //     0xbfbb30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfbb34: mov             x2, x0
    // 0xbfbb38: ldur            x0, [fp, #-0x28]
    // 0xbfbb3c: stur            x2, [fp, #-0x10]
    // 0xbfbb40: StoreField: r2->field_f = r0
    //     0xbfbb40: stur            w0, [x2, #0xf]
    // 0xbfbb44: ldur            x0, [fp, #-0x18]
    // 0xbfbb48: StoreField: r2->field_13 = r0
    //     0xbfbb48: stur            w0, [x2, #0x13]
    // 0xbfbb4c: r1 = <Widget>
    //     0xbfbb4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfbb50: r0 = AllocateGrowableArray()
    //     0xbfbb50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfbb54: mov             x1, x0
    // 0xbfbb58: ldur            x0, [fp, #-0x10]
    // 0xbfbb5c: stur            x1, [fp, #-0x18]
    // 0xbfbb60: StoreField: r1->field_f = r0
    //     0xbfbb60: stur            w0, [x1, #0xf]
    // 0xbfbb64: r0 = 4
    //     0xbfbb64: movz            x0, #0x4
    // 0xbfbb68: StoreField: r1->field_b = r0
    //     0xbfbb68: stur            w0, [x1, #0xb]
    // 0xbfbb6c: r0 = Row()
    //     0xbfbb6c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfbb70: mov             x2, x0
    // 0xbfbb74: r0 = Instance_Axis
    //     0xbfbb74: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfbb78: stur            x2, [fp, #-0x28]
    // 0xbfbb7c: StoreField: r2->field_f = r0
    //     0xbfbb7c: stur            w0, [x2, #0xf]
    // 0xbfbb80: r3 = Instance_MainAxisAlignment
    //     0xbfbb80: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbfbb84: ldr             x3, [x3, #0xa8]
    // 0xbfbb88: StoreField: r2->field_13 = r3
    //     0xbfbb88: stur            w3, [x2, #0x13]
    // 0xbfbb8c: r4 = Instance_MainAxisSize
    //     0xbfbb8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfbb90: ldr             x4, [x4, #0xa10]
    // 0xbfbb94: ArrayStore: r2[0] = r4  ; List_4
    //     0xbfbb94: stur            w4, [x2, #0x17]
    // 0xbfbb98: r1 = Instance_CrossAxisAlignment
    //     0xbfbb98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfbb9c: ldr             x1, [x1, #0xa18]
    // 0xbfbba0: StoreField: r2->field_1b = r1
    //     0xbfbba0: stur            w1, [x2, #0x1b]
    // 0xbfbba4: r5 = Instance_VerticalDirection
    //     0xbfbba4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfbba8: ldr             x5, [x5, #0xa20]
    // 0xbfbbac: StoreField: r2->field_23 = r5
    //     0xbfbbac: stur            w5, [x2, #0x23]
    // 0xbfbbb0: r6 = Instance_Clip
    //     0xbfbbb0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfbbb4: ldr             x6, [x6, #0x38]
    // 0xbfbbb8: StoreField: r2->field_2b = r6
    //     0xbfbbb8: stur            w6, [x2, #0x2b]
    // 0xbfbbbc: StoreField: r2->field_2f = rZR
    //     0xbfbbbc: stur            xzr, [x2, #0x2f]
    // 0xbfbbc0: ldur            x1, [fp, #-0x18]
    // 0xbfbbc4: StoreField: r2->field_b = r1
    //     0xbfbbc4: stur            w1, [x2, #0xb]
    // 0xbfbbc8: ldur            x7, [fp, #-8]
    // 0xbfbbcc: LoadField: r1 = r7->field_b
    //     0xbfbbcc: ldur            w1, [x7, #0xb]
    // 0xbfbbd0: DecompressPointer r1
    //     0xbfbbd0: add             x1, x1, HEAP, lsl #32
    // 0xbfbbd4: cmp             w1, NULL
    // 0xbfbbd8: b.eq            #0xbfc254
    // 0xbfbbdc: LoadField: r8 = r1->field_b
    //     0xbfbbdc: ldur            w8, [x1, #0xb]
    // 0xbfbbe0: DecompressPointer r8
    //     0xbfbbe0: add             x8, x8, HEAP, lsl #32
    // 0xbfbbe4: cmp             w8, NULL
    // 0xbfbbe8: b.ne            #0xbfbbf4
    // 0xbfbbec: r1 = Null
    //     0xbfbbec: mov             x1, NULL
    // 0xbfbbf0: b               #0xbfbbfc
    // 0xbfbbf4: LoadField: r1 = r8->field_f
    //     0xbfbbf4: ldur            w1, [x8, #0xf]
    // 0xbfbbf8: DecompressPointer r1
    //     0xbfbbf8: add             x1, x1, HEAP, lsl #32
    // 0xbfbbfc: cmp             w1, NULL
    // 0xbfbc00: b.ne            #0xbfbc0c
    // 0xbfbc04: r9 = ""
    //     0xbfbc04: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfbc08: b               #0xbfbc10
    // 0xbfbc0c: mov             x9, x1
    // 0xbfbc10: ldur            x8, [fp, #-0x20]
    // 0xbfbc14: stur            x9, [fp, #-0x10]
    // 0xbfbc18: LoadField: r1 = r8->field_13
    //     0xbfbc18: ldur            w1, [x8, #0x13]
    // 0xbfbc1c: DecompressPointer r1
    //     0xbfbc1c: add             x1, x1, HEAP, lsl #32
    // 0xbfbc20: r0 = of()
    //     0xbfbc20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfbc24: LoadField: r1 = r0->field_87
    //     0xbfbc24: ldur            w1, [x0, #0x87]
    // 0xbfbc28: DecompressPointer r1
    //     0xbfbc28: add             x1, x1, HEAP, lsl #32
    // 0xbfbc2c: LoadField: r0 = r1->field_2b
    //     0xbfbc2c: ldur            w0, [x1, #0x2b]
    // 0xbfbc30: DecompressPointer r0
    //     0xbfbc30: add             x0, x0, HEAP, lsl #32
    // 0xbfbc34: stur            x0, [fp, #-0x18]
    // 0xbfbc38: r1 = Instance_Color
    //     0xbfbc38: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfbc3c: d0 = 0.700000
    //     0xbfbc3c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbfbc40: ldr             d0, [x17, #0xf48]
    // 0xbfbc44: r0 = withOpacity()
    //     0xbfbc44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfbc48: r16 = 12.000000
    //     0xbfbc48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfbc4c: ldr             x16, [x16, #0x9e8]
    // 0xbfbc50: stp             x0, x16, [SP]
    // 0xbfbc54: ldur            x1, [fp, #-0x18]
    // 0xbfbc58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfbc58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfbc5c: ldr             x4, [x4, #0xaa0]
    // 0xbfbc60: r0 = copyWith()
    //     0xbfbc60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfbc64: stur            x0, [fp, #-0x18]
    // 0xbfbc68: r0 = HtmlWidget()
    //     0xbfbc68: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xbfbc6c: mov             x1, x0
    // 0xbfbc70: ldur            x0, [fp, #-0x10]
    // 0xbfbc74: stur            x1, [fp, #-0x30]
    // 0xbfbc78: StoreField: r1->field_1f = r0
    //     0xbfbc78: stur            w0, [x1, #0x1f]
    // 0xbfbc7c: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xbfbc7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xbfbc80: ldr             x0, [x0, #0x1e0]
    // 0xbfbc84: StoreField: r1->field_23 = r0
    //     0xbfbc84: stur            w0, [x1, #0x23]
    // 0xbfbc88: r0 = Instance_ColumnMode
    //     0xbfbc88: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xbfbc8c: ldr             x0, [x0, #0x1e8]
    // 0xbfbc90: StoreField: r1->field_3b = r0
    //     0xbfbc90: stur            w0, [x1, #0x3b]
    // 0xbfbc94: ldur            x0, [fp, #-0x18]
    // 0xbfbc98: StoreField: r1->field_3f = r0
    //     0xbfbc98: stur            w0, [x1, #0x3f]
    // 0xbfbc9c: r16 = <EdgeInsets>
    //     0xbfbc9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbfbca0: ldr             x16, [x16, #0xda0]
    // 0xbfbca4: r30 = Instance_EdgeInsets
    //     0xbfbca4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbfbca8: ldr             lr, [lr, #0x1f0]
    // 0xbfbcac: stp             lr, x16, [SP]
    // 0xbfbcb0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfbcb0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfbcb4: r0 = all()
    //     0xbfbcb4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfbcb8: ldur            x2, [fp, #-0x20]
    // 0xbfbcbc: stur            x0, [fp, #-0x10]
    // 0xbfbcc0: LoadField: r1 = r2->field_13
    //     0xbfbcc0: ldur            w1, [x2, #0x13]
    // 0xbfbcc4: DecompressPointer r1
    //     0xbfbcc4: add             x1, x1, HEAP, lsl #32
    // 0xbfbcc8: r0 = of()
    //     0xbfbcc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfbccc: LoadField: r1 = r0->field_5b
    //     0xbfbccc: ldur            w1, [x0, #0x5b]
    // 0xbfbcd0: DecompressPointer r1
    //     0xbfbcd0: add             x1, x1, HEAP, lsl #32
    // 0xbfbcd4: stur            x1, [fp, #-0x18]
    // 0xbfbcd8: r0 = BorderSide()
    //     0xbfbcd8: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbfbcdc: mov             x1, x0
    // 0xbfbce0: ldur            x0, [fp, #-0x18]
    // 0xbfbce4: stur            x1, [fp, #-0x38]
    // 0xbfbce8: StoreField: r1->field_7 = r0
    //     0xbfbce8: stur            w0, [x1, #7]
    // 0xbfbcec: d0 = 1.000000
    //     0xbfbcec: fmov            d0, #1.00000000
    // 0xbfbcf0: StoreField: r1->field_b = d0
    //     0xbfbcf0: stur            d0, [x1, #0xb]
    // 0xbfbcf4: r0 = Instance_BorderStyle
    //     0xbfbcf4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbfbcf8: ldr             x0, [x0, #0xf68]
    // 0xbfbcfc: StoreField: r1->field_13 = r0
    //     0xbfbcfc: stur            w0, [x1, #0x13]
    // 0xbfbd00: d0 = -1.000000
    //     0xbfbd00: fmov            d0, #-1.00000000
    // 0xbfbd04: ArrayStore: r1[0] = d0  ; List_8
    //     0xbfbd04: stur            d0, [x1, #0x17]
    // 0xbfbd08: r0 = RoundedRectangleBorder()
    //     0xbfbd08: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbfbd0c: mov             x1, x0
    // 0xbfbd10: r0 = Instance_BorderRadius
    //     0xbfbd10: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbfbd14: ldr             x0, [x0, #0xf70]
    // 0xbfbd18: StoreField: r1->field_b = r0
    //     0xbfbd18: stur            w0, [x1, #0xb]
    // 0xbfbd1c: ldur            x0, [fp, #-0x38]
    // 0xbfbd20: StoreField: r1->field_7 = r0
    //     0xbfbd20: stur            w0, [x1, #7]
    // 0xbfbd24: r16 = <RoundedRectangleBorder>
    //     0xbfbd24: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbfbd28: ldr             x16, [x16, #0xf78]
    // 0xbfbd2c: stp             x1, x16, [SP]
    // 0xbfbd30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfbd30: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfbd34: r0 = all()
    //     0xbfbd34: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfbd38: stur            x0, [fp, #-0x18]
    // 0xbfbd3c: r0 = ButtonStyle()
    //     0xbfbd3c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbfbd40: mov             x1, x0
    // 0xbfbd44: ldur            x0, [fp, #-0x10]
    // 0xbfbd48: stur            x1, [fp, #-0x38]
    // 0xbfbd4c: StoreField: r1->field_23 = r0
    //     0xbfbd4c: stur            w0, [x1, #0x23]
    // 0xbfbd50: ldur            x0, [fp, #-0x18]
    // 0xbfbd54: StoreField: r1->field_43 = r0
    //     0xbfbd54: stur            w0, [x1, #0x43]
    // 0xbfbd58: r0 = TextButtonThemeData()
    //     0xbfbd58: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbfbd5c: mov             x2, x0
    // 0xbfbd60: ldur            x0, [fp, #-0x38]
    // 0xbfbd64: stur            x2, [fp, #-0x10]
    // 0xbfbd68: StoreField: r2->field_7 = r0
    //     0xbfbd68: stur            w0, [x2, #7]
    // 0xbfbd6c: ldur            x0, [fp, #-0x20]
    // 0xbfbd70: LoadField: r1 = r0->field_13
    //     0xbfbd70: ldur            w1, [x0, #0x13]
    // 0xbfbd74: DecompressPointer r1
    //     0xbfbd74: add             x1, x1, HEAP, lsl #32
    // 0xbfbd78: r0 = of()
    //     0xbfbd78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfbd7c: LoadField: r1 = r0->field_87
    //     0xbfbd7c: ldur            w1, [x0, #0x87]
    // 0xbfbd80: DecompressPointer r1
    //     0xbfbd80: add             x1, x1, HEAP, lsl #32
    // 0xbfbd84: LoadField: r0 = r1->field_7
    //     0xbfbd84: ldur            w0, [x1, #7]
    // 0xbfbd88: DecompressPointer r0
    //     0xbfbd88: add             x0, x0, HEAP, lsl #32
    // 0xbfbd8c: ldur            x2, [fp, #-0x20]
    // 0xbfbd90: stur            x0, [fp, #-0x18]
    // 0xbfbd94: LoadField: r1 = r2->field_13
    //     0xbfbd94: ldur            w1, [x2, #0x13]
    // 0xbfbd98: DecompressPointer r1
    //     0xbfbd98: add             x1, x1, HEAP, lsl #32
    // 0xbfbd9c: r0 = of()
    //     0xbfbd9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfbda0: LoadField: r1 = r0->field_5b
    //     0xbfbda0: ldur            w1, [x0, #0x5b]
    // 0xbfbda4: DecompressPointer r1
    //     0xbfbda4: add             x1, x1, HEAP, lsl #32
    // 0xbfbda8: r16 = 14.000000
    //     0xbfbda8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbfbdac: ldr             x16, [x16, #0x1d8]
    // 0xbfbdb0: stp             x1, x16, [SP]
    // 0xbfbdb4: ldur            x1, [fp, #-0x18]
    // 0xbfbdb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfbdb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfbdbc: ldr             x4, [x4, #0xaa0]
    // 0xbfbdc0: r0 = copyWith()
    //     0xbfbdc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfbdc4: stur            x0, [fp, #-0x18]
    // 0xbfbdc8: r0 = Text()
    //     0xbfbdc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfbdcc: mov             x3, x0
    // 0xbfbdd0: r0 = "GO BACK"
    //     0xbfbdd0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36138] "GO BACK"
    //     0xbfbdd4: ldr             x0, [x0, #0x138]
    // 0xbfbdd8: stur            x3, [fp, #-0x38]
    // 0xbfbddc: StoreField: r3->field_b = r0
    //     0xbfbddc: stur            w0, [x3, #0xb]
    // 0xbfbde0: ldur            x0, [fp, #-0x18]
    // 0xbfbde4: StoreField: r3->field_13 = r0
    //     0xbfbde4: stur            w0, [x3, #0x13]
    // 0xbfbde8: r1 = Function '<anonymous closure>':.
    //     0xbfbde8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c138] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbfbdec: ldr             x1, [x1, #0x138]
    // 0xbfbdf0: r2 = Null
    //     0xbfbdf0: mov             x2, NULL
    // 0xbfbdf4: r0 = AllocateClosure()
    //     0xbfbdf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfbdf8: stur            x0, [fp, #-0x18]
    // 0xbfbdfc: r0 = TextButton()
    //     0xbfbdfc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbfbe00: mov             x1, x0
    // 0xbfbe04: ldur            x0, [fp, #-0x18]
    // 0xbfbe08: stur            x1, [fp, #-0x40]
    // 0xbfbe0c: StoreField: r1->field_b = r0
    //     0xbfbe0c: stur            w0, [x1, #0xb]
    // 0xbfbe10: r0 = false
    //     0xbfbe10: add             x0, NULL, #0x30  ; false
    // 0xbfbe14: StoreField: r1->field_27 = r0
    //     0xbfbe14: stur            w0, [x1, #0x27]
    // 0xbfbe18: r2 = true
    //     0xbfbe18: add             x2, NULL, #0x20  ; true
    // 0xbfbe1c: StoreField: r1->field_2f = r2
    //     0xbfbe1c: stur            w2, [x1, #0x2f]
    // 0xbfbe20: ldur            x3, [fp, #-0x38]
    // 0xbfbe24: StoreField: r1->field_37 = r3
    //     0xbfbe24: stur            w3, [x1, #0x37]
    // 0xbfbe28: r0 = TextButtonTheme()
    //     0xbfbe28: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbfbe2c: mov             x2, x0
    // 0xbfbe30: ldur            x0, [fp, #-0x10]
    // 0xbfbe34: stur            x2, [fp, #-0x18]
    // 0xbfbe38: StoreField: r2->field_f = r0
    //     0xbfbe38: stur            w0, [x2, #0xf]
    // 0xbfbe3c: ldur            x0, [fp, #-0x40]
    // 0xbfbe40: StoreField: r2->field_b = r0
    //     0xbfbe40: stur            w0, [x2, #0xb]
    // 0xbfbe44: r1 = <FlexParentData>
    //     0xbfbe44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbfbe48: ldr             x1, [x1, #0xe00]
    // 0xbfbe4c: r0 = Flexible()
    //     0xbfbe4c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbfbe50: mov             x1, x0
    // 0xbfbe54: r0 = 1
    //     0xbfbe54: movz            x0, #0x1
    // 0xbfbe58: stur            x1, [fp, #-0x10]
    // 0xbfbe5c: StoreField: r1->field_13 = r0
    //     0xbfbe5c: stur            x0, [x1, #0x13]
    // 0xbfbe60: r2 = Instance_FlexFit
    //     0xbfbe60: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbfbe64: ldr             x2, [x2, #0xe08]
    // 0xbfbe68: StoreField: r1->field_1b = r2
    //     0xbfbe68: stur            w2, [x1, #0x1b]
    // 0xbfbe6c: ldur            x3, [fp, #-0x18]
    // 0xbfbe70: StoreField: r1->field_b = r3
    //     0xbfbe70: stur            w3, [x1, #0xb]
    // 0xbfbe74: r16 = <EdgeInsets>
    //     0xbfbe74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbfbe78: ldr             x16, [x16, #0xda0]
    // 0xbfbe7c: r30 = Instance_EdgeInsets
    //     0xbfbe7c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbfbe80: ldr             lr, [lr, #0x1f0]
    // 0xbfbe84: stp             lr, x16, [SP]
    // 0xbfbe88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfbe88: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfbe8c: r0 = all()
    //     0xbfbe8c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfbe90: ldur            x2, [fp, #-0x20]
    // 0xbfbe94: stur            x0, [fp, #-0x18]
    // 0xbfbe98: LoadField: r1 = r2->field_13
    //     0xbfbe98: ldur            w1, [x2, #0x13]
    // 0xbfbe9c: DecompressPointer r1
    //     0xbfbe9c: add             x1, x1, HEAP, lsl #32
    // 0xbfbea0: r0 = of()
    //     0xbfbea0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfbea4: LoadField: r1 = r0->field_5b
    //     0xbfbea4: ldur            w1, [x0, #0x5b]
    // 0xbfbea8: DecompressPointer r1
    //     0xbfbea8: add             x1, x1, HEAP, lsl #32
    // 0xbfbeac: r16 = <Color>
    //     0xbfbeac: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbfbeb0: ldr             x16, [x16, #0xf80]
    // 0xbfbeb4: stp             x1, x16, [SP]
    // 0xbfbeb8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfbeb8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfbebc: r0 = all()
    //     0xbfbebc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfbec0: stur            x0, [fp, #-0x38]
    // 0xbfbec4: r16 = <RoundedRectangleBorder>
    //     0xbfbec4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbfbec8: ldr             x16, [x16, #0xf78]
    // 0xbfbecc: r30 = Instance_RoundedRectangleBorder
    //     0xbfbecc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbfbed0: ldr             lr, [lr, #0xd68]
    // 0xbfbed4: stp             lr, x16, [SP]
    // 0xbfbed8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfbed8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfbedc: r0 = all()
    //     0xbfbedc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbfbee0: stur            x0, [fp, #-0x40]
    // 0xbfbee4: r0 = ButtonStyle()
    //     0xbfbee4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbfbee8: mov             x1, x0
    // 0xbfbeec: ldur            x0, [fp, #-0x38]
    // 0xbfbef0: stur            x1, [fp, #-0x48]
    // 0xbfbef4: StoreField: r1->field_b = r0
    //     0xbfbef4: stur            w0, [x1, #0xb]
    // 0xbfbef8: ldur            x0, [fp, #-0x18]
    // 0xbfbefc: StoreField: r1->field_23 = r0
    //     0xbfbefc: stur            w0, [x1, #0x23]
    // 0xbfbf00: ldur            x0, [fp, #-0x40]
    // 0xbfbf04: StoreField: r1->field_43 = r0
    //     0xbfbf04: stur            w0, [x1, #0x43]
    // 0xbfbf08: r0 = TextButtonThemeData()
    //     0xbfbf08: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbfbf0c: mov             x1, x0
    // 0xbfbf10: ldur            x0, [fp, #-0x48]
    // 0xbfbf14: stur            x1, [fp, #-0x18]
    // 0xbfbf18: StoreField: r1->field_7 = r0
    //     0xbfbf18: stur            w0, [x1, #7]
    // 0xbfbf1c: ldur            x0, [fp, #-8]
    // 0xbfbf20: LoadField: r2 = r0->field_b
    //     0xbfbf20: ldur            w2, [x0, #0xb]
    // 0xbfbf24: DecompressPointer r2
    //     0xbfbf24: add             x2, x2, HEAP, lsl #32
    // 0xbfbf28: cmp             w2, NULL
    // 0xbfbf2c: b.eq            #0xbfc258
    // 0xbfbf30: LoadField: r0 = r2->field_b
    //     0xbfbf30: ldur            w0, [x2, #0xb]
    // 0xbfbf34: DecompressPointer r0
    //     0xbfbf34: add             x0, x0, HEAP, lsl #32
    // 0xbfbf38: cmp             w0, NULL
    // 0xbfbf3c: b.ne            #0xbfbf48
    // 0xbfbf40: r0 = Null
    //     0xbfbf40: mov             x0, NULL
    // 0xbfbf44: b               #0xbfbf54
    // 0xbfbf48: LoadField: r2 = r0->field_b
    //     0xbfbf48: ldur            w2, [x0, #0xb]
    // 0xbfbf4c: DecompressPointer r2
    //     0xbfbf4c: add             x2, x2, HEAP, lsl #32
    // 0xbfbf50: mov             x0, x2
    // 0xbfbf54: cmp             w0, NULL
    // 0xbfbf58: b.ne            #0xbfbf60
    // 0xbfbf5c: r0 = ""
    //     0xbfbf5c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfbf60: ldur            x2, [fp, #-0x20]
    // 0xbfbf64: ldur            x5, [fp, #-0x28]
    // 0xbfbf68: ldur            x4, [fp, #-0x30]
    // 0xbfbf6c: ldur            x3, [fp, #-0x10]
    // 0xbfbf70: r6 = LoadClassIdInstr(r0)
    //     0xbfbf70: ldur            x6, [x0, #-1]
    //     0xbfbf74: ubfx            x6, x6, #0xc, #0x14
    // 0xbfbf78: str             x0, [SP]
    // 0xbfbf7c: mov             x0, x6
    // 0xbfbf80: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbfbf80: sub             lr, x0, #1, lsl #12
    //     0xbfbf84: ldr             lr, [x21, lr, lsl #3]
    //     0xbfbf88: blr             lr
    // 0xbfbf8c: ldur            x2, [fp, #-0x20]
    // 0xbfbf90: stur            x0, [fp, #-8]
    // 0xbfbf94: LoadField: r1 = r2->field_13
    //     0xbfbf94: ldur            w1, [x2, #0x13]
    // 0xbfbf98: DecompressPointer r1
    //     0xbfbf98: add             x1, x1, HEAP, lsl #32
    // 0xbfbf9c: r0 = of()
    //     0xbfbf9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfbfa0: LoadField: r1 = r0->field_87
    //     0xbfbfa0: ldur            w1, [x0, #0x87]
    // 0xbfbfa4: DecompressPointer r1
    //     0xbfbfa4: add             x1, x1, HEAP, lsl #32
    // 0xbfbfa8: LoadField: r0 = r1->field_7
    //     0xbfbfa8: ldur            w0, [x1, #7]
    // 0xbfbfac: DecompressPointer r0
    //     0xbfbfac: add             x0, x0, HEAP, lsl #32
    // 0xbfbfb0: r16 = 14.000000
    //     0xbfbfb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbfbfb4: ldr             x16, [x16, #0x1d8]
    // 0xbfbfb8: r30 = Instance_Color
    //     0xbfbfb8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfbfbc: stp             lr, x16, [SP]
    // 0xbfbfc0: mov             x1, x0
    // 0xbfbfc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfbfc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfbfc8: ldr             x4, [x4, #0xaa0]
    // 0xbfbfcc: r0 = copyWith()
    //     0xbfbfcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfbfd0: stur            x0, [fp, #-0x38]
    // 0xbfbfd4: r0 = Text()
    //     0xbfbfd4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfbfd8: mov             x3, x0
    // 0xbfbfdc: ldur            x0, [fp, #-8]
    // 0xbfbfe0: stur            x3, [fp, #-0x40]
    // 0xbfbfe4: StoreField: r3->field_b = r0
    //     0xbfbfe4: stur            w0, [x3, #0xb]
    // 0xbfbfe8: ldur            x0, [fp, #-0x38]
    // 0xbfbfec: StoreField: r3->field_13 = r0
    //     0xbfbfec: stur            w0, [x3, #0x13]
    // 0xbfbff0: r0 = Instance_TextAlign
    //     0xbfbff0: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbfbff4: StoreField: r3->field_1b = r0
    //     0xbfbff4: stur            w0, [x3, #0x1b]
    // 0xbfbff8: ldur            x2, [fp, #-0x20]
    // 0xbfbffc: r1 = Function '<anonymous closure>':.
    //     0xbfbffc: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c140] AnonymousClosure: (0xbfc25c), in [package:customer_app/app/presentation/views/line/post_order/order_detail/cancel_return_order_bottom_sheet.dart] _CancelReturnOrderBottomSheetState::build (0xbfb9d8)
    //     0xbfc000: ldr             x1, [x1, #0x140]
    // 0xbfc004: r0 = AllocateClosure()
    //     0xbfc004: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfc008: stur            x0, [fp, #-8]
    // 0xbfc00c: r0 = TextButton()
    //     0xbfc00c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbfc010: mov             x1, x0
    // 0xbfc014: ldur            x0, [fp, #-8]
    // 0xbfc018: stur            x1, [fp, #-0x20]
    // 0xbfc01c: StoreField: r1->field_b = r0
    //     0xbfc01c: stur            w0, [x1, #0xb]
    // 0xbfc020: r0 = false
    //     0xbfc020: add             x0, NULL, #0x30  ; false
    // 0xbfc024: StoreField: r1->field_27 = r0
    //     0xbfc024: stur            w0, [x1, #0x27]
    // 0xbfc028: r0 = true
    //     0xbfc028: add             x0, NULL, #0x20  ; true
    // 0xbfc02c: StoreField: r1->field_2f = r0
    //     0xbfc02c: stur            w0, [x1, #0x2f]
    // 0xbfc030: ldur            x0, [fp, #-0x40]
    // 0xbfc034: StoreField: r1->field_37 = r0
    //     0xbfc034: stur            w0, [x1, #0x37]
    // 0xbfc038: r0 = TextButtonTheme()
    //     0xbfc038: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbfc03c: mov             x2, x0
    // 0xbfc040: ldur            x0, [fp, #-0x18]
    // 0xbfc044: stur            x2, [fp, #-8]
    // 0xbfc048: StoreField: r2->field_f = r0
    //     0xbfc048: stur            w0, [x2, #0xf]
    // 0xbfc04c: ldur            x0, [fp, #-0x20]
    // 0xbfc050: StoreField: r2->field_b = r0
    //     0xbfc050: stur            w0, [x2, #0xb]
    // 0xbfc054: r1 = <FlexParentData>
    //     0xbfc054: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbfc058: ldr             x1, [x1, #0xe00]
    // 0xbfc05c: r0 = Flexible()
    //     0xbfc05c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbfc060: mov             x3, x0
    // 0xbfc064: r0 = 1
    //     0xbfc064: movz            x0, #0x1
    // 0xbfc068: stur            x3, [fp, #-0x18]
    // 0xbfc06c: StoreField: r3->field_13 = r0
    //     0xbfc06c: stur            x0, [x3, #0x13]
    // 0xbfc070: r0 = Instance_FlexFit
    //     0xbfc070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbfc074: ldr             x0, [x0, #0xe08]
    // 0xbfc078: StoreField: r3->field_1b = r0
    //     0xbfc078: stur            w0, [x3, #0x1b]
    // 0xbfc07c: ldur            x0, [fp, #-8]
    // 0xbfc080: StoreField: r3->field_b = r0
    //     0xbfc080: stur            w0, [x3, #0xb]
    // 0xbfc084: r1 = Null
    //     0xbfc084: mov             x1, NULL
    // 0xbfc088: r2 = 6
    //     0xbfc088: movz            x2, #0x6
    // 0xbfc08c: r0 = AllocateArray()
    //     0xbfc08c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc090: mov             x2, x0
    // 0xbfc094: ldur            x0, [fp, #-0x10]
    // 0xbfc098: stur            x2, [fp, #-8]
    // 0xbfc09c: StoreField: r2->field_f = r0
    //     0xbfc09c: stur            w0, [x2, #0xf]
    // 0xbfc0a0: r16 = Instance_SizedBox
    //     0xbfc0a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbfc0a4: ldr             x16, [x16, #0xb20]
    // 0xbfc0a8: StoreField: r2->field_13 = r16
    //     0xbfc0a8: stur            w16, [x2, #0x13]
    // 0xbfc0ac: ldur            x0, [fp, #-0x18]
    // 0xbfc0b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfc0b0: stur            w0, [x2, #0x17]
    // 0xbfc0b4: r1 = <Widget>
    //     0xbfc0b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc0b8: r0 = AllocateGrowableArray()
    //     0xbfc0b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc0bc: mov             x1, x0
    // 0xbfc0c0: ldur            x0, [fp, #-8]
    // 0xbfc0c4: stur            x1, [fp, #-0x10]
    // 0xbfc0c8: StoreField: r1->field_f = r0
    //     0xbfc0c8: stur            w0, [x1, #0xf]
    // 0xbfc0cc: r0 = 6
    //     0xbfc0cc: movz            x0, #0x6
    // 0xbfc0d0: StoreField: r1->field_b = r0
    //     0xbfc0d0: stur            w0, [x1, #0xb]
    // 0xbfc0d4: r0 = Row()
    //     0xbfc0d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfc0d8: mov             x1, x0
    // 0xbfc0dc: r0 = Instance_Axis
    //     0xbfc0dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfc0e0: stur            x1, [fp, #-8]
    // 0xbfc0e4: StoreField: r1->field_f = r0
    //     0xbfc0e4: stur            w0, [x1, #0xf]
    // 0xbfc0e8: r0 = Instance_MainAxisAlignment
    //     0xbfc0e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbfc0ec: ldr             x0, [x0, #0xa8]
    // 0xbfc0f0: StoreField: r1->field_13 = r0
    //     0xbfc0f0: stur            w0, [x1, #0x13]
    // 0xbfc0f4: r0 = Instance_MainAxisSize
    //     0xbfc0f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfc0f8: ldr             x0, [x0, #0xa10]
    // 0xbfc0fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfc0fc: stur            w0, [x1, #0x17]
    // 0xbfc100: r0 = Instance_CrossAxisAlignment
    //     0xbfc100: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfc104: ldr             x0, [x0, #0x890]
    // 0xbfc108: StoreField: r1->field_1b = r0
    //     0xbfc108: stur            w0, [x1, #0x1b]
    // 0xbfc10c: r2 = Instance_VerticalDirection
    //     0xbfc10c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfc110: ldr             x2, [x2, #0xa20]
    // 0xbfc114: StoreField: r1->field_23 = r2
    //     0xbfc114: stur            w2, [x1, #0x23]
    // 0xbfc118: r3 = Instance_Clip
    //     0xbfc118: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfc11c: ldr             x3, [x3, #0x38]
    // 0xbfc120: StoreField: r1->field_2b = r3
    //     0xbfc120: stur            w3, [x1, #0x2b]
    // 0xbfc124: StoreField: r1->field_2f = rZR
    //     0xbfc124: stur            xzr, [x1, #0x2f]
    // 0xbfc128: ldur            x4, [fp, #-0x10]
    // 0xbfc12c: StoreField: r1->field_b = r4
    //     0xbfc12c: stur            w4, [x1, #0xb]
    // 0xbfc130: r0 = Padding()
    //     0xbfc130: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfc134: mov             x3, x0
    // 0xbfc138: r0 = Instance_EdgeInsets
    //     0xbfc138: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbfc13c: ldr             x0, [x0, #0x858]
    // 0xbfc140: stur            x3, [fp, #-0x10]
    // 0xbfc144: StoreField: r3->field_f = r0
    //     0xbfc144: stur            w0, [x3, #0xf]
    // 0xbfc148: ldur            x0, [fp, #-8]
    // 0xbfc14c: StoreField: r3->field_b = r0
    //     0xbfc14c: stur            w0, [x3, #0xb]
    // 0xbfc150: r1 = Null
    //     0xbfc150: mov             x1, NULL
    // 0xbfc154: r2 = 8
    //     0xbfc154: movz            x2, #0x8
    // 0xbfc158: r0 = AllocateArray()
    //     0xbfc158: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfc15c: mov             x2, x0
    // 0xbfc160: ldur            x0, [fp, #-0x28]
    // 0xbfc164: stur            x2, [fp, #-8]
    // 0xbfc168: StoreField: r2->field_f = r0
    //     0xbfc168: stur            w0, [x2, #0xf]
    // 0xbfc16c: r16 = Instance_SizedBox
    //     0xbfc16c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbfc170: ldr             x16, [x16, #0x578]
    // 0xbfc174: StoreField: r2->field_13 = r16
    //     0xbfc174: stur            w16, [x2, #0x13]
    // 0xbfc178: ldur            x0, [fp, #-0x30]
    // 0xbfc17c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfc17c: stur            w0, [x2, #0x17]
    // 0xbfc180: ldur            x0, [fp, #-0x10]
    // 0xbfc184: StoreField: r2->field_1b = r0
    //     0xbfc184: stur            w0, [x2, #0x1b]
    // 0xbfc188: r1 = <Widget>
    //     0xbfc188: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfc18c: r0 = AllocateGrowableArray()
    //     0xbfc18c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfc190: mov             x1, x0
    // 0xbfc194: ldur            x0, [fp, #-8]
    // 0xbfc198: stur            x1, [fp, #-0x10]
    // 0xbfc19c: StoreField: r1->field_f = r0
    //     0xbfc19c: stur            w0, [x1, #0xf]
    // 0xbfc1a0: r0 = 8
    //     0xbfc1a0: movz            x0, #0x8
    // 0xbfc1a4: StoreField: r1->field_b = r0
    //     0xbfc1a4: stur            w0, [x1, #0xb]
    // 0xbfc1a8: r0 = Column()
    //     0xbfc1a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfc1ac: mov             x1, x0
    // 0xbfc1b0: r0 = Instance_Axis
    //     0xbfc1b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfc1b4: stur            x1, [fp, #-8]
    // 0xbfc1b8: StoreField: r1->field_f = r0
    //     0xbfc1b8: stur            w0, [x1, #0xf]
    // 0xbfc1bc: r0 = Instance_MainAxisAlignment
    //     0xbfc1bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfc1c0: ldr             x0, [x0, #0xa08]
    // 0xbfc1c4: StoreField: r1->field_13 = r0
    //     0xbfc1c4: stur            w0, [x1, #0x13]
    // 0xbfc1c8: r0 = Instance_MainAxisSize
    //     0xbfc1c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbfc1cc: ldr             x0, [x0, #0xdd0]
    // 0xbfc1d0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfc1d0: stur            w0, [x1, #0x17]
    // 0xbfc1d4: r0 = Instance_CrossAxisAlignment
    //     0xbfc1d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfc1d8: ldr             x0, [x0, #0x890]
    // 0xbfc1dc: StoreField: r1->field_1b = r0
    //     0xbfc1dc: stur            w0, [x1, #0x1b]
    // 0xbfc1e0: r0 = Instance_VerticalDirection
    //     0xbfc1e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfc1e4: ldr             x0, [x0, #0xa20]
    // 0xbfc1e8: StoreField: r1->field_23 = r0
    //     0xbfc1e8: stur            w0, [x1, #0x23]
    // 0xbfc1ec: r0 = Instance_Clip
    //     0xbfc1ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfc1f0: ldr             x0, [x0, #0x38]
    // 0xbfc1f4: StoreField: r1->field_2b = r0
    //     0xbfc1f4: stur            w0, [x1, #0x2b]
    // 0xbfc1f8: StoreField: r1->field_2f = rZR
    //     0xbfc1f8: stur            xzr, [x1, #0x2f]
    // 0xbfc1fc: ldur            x0, [fp, #-0x10]
    // 0xbfc200: StoreField: r1->field_b = r0
    //     0xbfc200: stur            w0, [x1, #0xb]
    // 0xbfc204: r0 = Container()
    //     0xbfc204: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfc208: stur            x0, [fp, #-0x10]
    // 0xbfc20c: r16 = Instance_BoxDecoration
    //     0xbfc20c: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c148] Obj!BoxDecoration@d64bf1
    //     0xbfc210: ldr             x16, [x16, #0x148]
    // 0xbfc214: r30 = Instance_EdgeInsets
    //     0xbfc214: add             lr, PP, #0x55, lsl #12  ; [pp+0x55a40] Obj!EdgeInsets@d58311
    //     0xbfc218: ldr             lr, [lr, #0xa40]
    // 0xbfc21c: stp             lr, x16, [SP, #8]
    // 0xbfc220: ldur            x16, [fp, #-8]
    // 0xbfc224: str             x16, [SP]
    // 0xbfc228: mov             x1, x0
    // 0xbfc22c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xbfc22c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xbfc230: ldr             x4, [x4, #0xb40]
    // 0xbfc234: r0 = Container()
    //     0xbfc234: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfc238: ldur            x0, [fp, #-0x10]
    // 0xbfc23c: LeaveFrame
    //     0xbfc23c: mov             SP, fp
    //     0xbfc240: ldp             fp, lr, [SP], #0x10
    // 0xbfc244: ret
    //     0xbfc244: ret             
    // 0xbfc248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfc248: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfc24c: b               #0xbfba00
    // 0xbfc250: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfc250: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfc254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfc254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfc258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfc258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbfc25c, size: 0xa8
    // 0xbfc25c: EnterFrame
    //     0xbfc25c: stp             fp, lr, [SP, #-0x10]!
    //     0xbfc260: mov             fp, SP
    // 0xbfc264: AllocStack(0x10)
    //     0xbfc264: sub             SP, SP, #0x10
    // 0xbfc268: SetupParameters()
    //     0xbfc268: ldr             x0, [fp, #0x10]
    //     0xbfc26c: ldur            w1, [x0, #0x17]
    //     0xbfc270: add             x1, x1, HEAP, lsl #32
    // 0xbfc274: CheckStackOverflow
    //     0xbfc274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfc278: cmp             SP, x16
    //     0xbfc27c: b.ls            #0xbfc2f8
    // 0xbfc280: LoadField: r0 = r1->field_f
    //     0xbfc280: ldur            w0, [x1, #0xf]
    // 0xbfc284: DecompressPointer r0
    //     0xbfc284: add             x0, x0, HEAP, lsl #32
    // 0xbfc288: LoadField: r1 = r0->field_b
    //     0xbfc288: ldur            w1, [x0, #0xb]
    // 0xbfc28c: DecompressPointer r1
    //     0xbfc28c: add             x1, x1, HEAP, lsl #32
    // 0xbfc290: cmp             w1, NULL
    // 0xbfc294: b.eq            #0xbfc300
    // 0xbfc298: LoadField: r0 = r1->field_13
    //     0xbfc298: ldur            w0, [x1, #0x13]
    // 0xbfc29c: DecompressPointer r0
    //     0xbfc29c: add             x0, x0, HEAP, lsl #32
    // 0xbfc2a0: LoadField: r2 = r1->field_f
    //     0xbfc2a0: ldur            w2, [x1, #0xf]
    // 0xbfc2a4: DecompressPointer r2
    //     0xbfc2a4: add             x2, x2, HEAP, lsl #32
    // 0xbfc2a8: stp             x0, x2, [SP]
    // 0xbfc2ac: r4 = 0
    //     0xbfc2ac: movz            x4, #0
    // 0xbfc2b0: ldr             x0, [SP, #8]
    // 0xbfc2b4: r5 = UnlinkedCall_0x613b5c
    //     0xbfc2b4: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c150] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbfc2b8: ldp             x5, lr, [x16, #0x150]
    // 0xbfc2bc: blr             lr
    // 0xbfc2c0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbfc2c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbfc2c4: ldr             x0, [x0, #0x1c80]
    //     0xbfc2c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbfc2cc: cmp             w0, w16
    //     0xbfc2d0: b.ne            #0xbfc2dc
    //     0xbfc2d4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbfc2d8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbfc2dc: str             NULL, [SP]
    // 0xbfc2e0: r4 = const [0x1, 0, 0, 0, null]
    //     0xbfc2e0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbfc2e4: r0 = GetNavigation.back()
    //     0xbfc2e4: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbfc2e8: r0 = Null
    //     0xbfc2e8: mov             x0, NULL
    // 0xbfc2ec: LeaveFrame
    //     0xbfc2ec: mov             SP, fp
    //     0xbfc2f0: ldp             fp, lr, [SP], #0x10
    // 0xbfc2f4: ret
    //     0xbfc2f4: ret             
    // 0xbfc2f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfc2f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfc2fc: b               #0xbfc280
    // 0xbfc300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfc300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3980, size: 0x18, field offset: 0xc
//   const constructor, 
class CancelReturnOrderBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80e18, size: 0x24
    // 0xc80e18: EnterFrame
    //     0xc80e18: stp             fp, lr, [SP, #-0x10]!
    //     0xc80e1c: mov             fp, SP
    // 0xc80e20: mov             x0, x1
    // 0xc80e24: r1 = <CancelReturnOrderBottomSheet>
    //     0xc80e24: add             x1, PP, #0x49, lsl #12  ; [pp+0x493b8] TypeArguments: <CancelReturnOrderBottomSheet>
    //     0xc80e28: ldr             x1, [x1, #0x3b8]
    // 0xc80e2c: r0 = _CancelReturnOrderBottomSheetState()
    //     0xc80e2c: bl              #0xc80e3c  ; Allocate_CancelReturnOrderBottomSheetStateStub -> _CancelReturnOrderBottomSheetState (size=0x14)
    // 0xc80e30: LeaveFrame
    //     0xc80e30: mov             SP, fp
    //     0xc80e34: ldp             fp, lr, [SP], #0x10
    // 0xc80e38: ret
    //     0xc80e38: ret             
  }
}
