// lib: , url: package:customer_app/app/presentation/views/glass/main/main_page.dart

// class id: 1049411, size: 0x8
class :: {
}

// class id: 3328, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __MainPageState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ activate(/* No info */) {
    // ** addr: 0x7f5268, size: 0x30
    // 0x7f5268: EnterFrame
    //     0x7f5268: stp             fp, lr, [SP, #-0x10]!
    //     0x7f526c: mov             fp, SP
    // 0x7f5270: CheckStackOverflow
    //     0x7f5270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f5274: cmp             SP, x16
    //     0x7f5278: b.ls            #0x7f5290
    // 0x7f527c: r0 = _updateTickerModeNotifier()
    //     0x7f527c: bl              #0x7f52b8  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] __MainPageState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f5280: r0 = Null
    //     0x7f5280: mov             x0, NULL
    // 0x7f5284: LeaveFrame
    //     0x7f5284: mov             SP, fp
    //     0x7f5288: ldp             fp, lr, [SP], #0x10
    // 0x7f528c: ret
    //     0x7f528c: ret             
    // 0x7f5290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f5290: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5294: b               #0x7f527c
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x7f52b8, size: 0x124
    // 0x7f52b8: EnterFrame
    //     0x7f52b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7f52bc: mov             fp, SP
    // 0x7f52c0: AllocStack(0x18)
    //     0x7f52c0: sub             SP, SP, #0x18
    // 0x7f52c4: SetupParameters(__MainPageState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x7f52c4: mov             x2, x1
    //     0x7f52c8: stur            x1, [fp, #-8]
    // 0x7f52cc: CheckStackOverflow
    //     0x7f52cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f52d0: cmp             SP, x16
    //     0x7f52d4: b.ls            #0x7f53d0
    // 0x7f52d8: LoadField: r1 = r2->field_f
    //     0x7f52d8: ldur            w1, [x2, #0xf]
    // 0x7f52dc: DecompressPointer r1
    //     0x7f52dc: add             x1, x1, HEAP, lsl #32
    // 0x7f52e0: cmp             w1, NULL
    // 0x7f52e4: b.eq            #0x7f53d8
    // 0x7f52e8: r0 = getNotifier()
    //     0x7f52e8: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x7f52ec: mov             x3, x0
    // 0x7f52f0: ldur            x0, [fp, #-8]
    // 0x7f52f4: stur            x3, [fp, #-0x18]
    // 0x7f52f8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x7f52f8: ldur            w4, [x0, #0x17]
    // 0x7f52fc: DecompressPointer r4
    //     0x7f52fc: add             x4, x4, HEAP, lsl #32
    // 0x7f5300: stur            x4, [fp, #-0x10]
    // 0x7f5304: cmp             w3, w4
    // 0x7f5308: b.ne            #0x7f531c
    // 0x7f530c: r0 = Null
    //     0x7f530c: mov             x0, NULL
    // 0x7f5310: LeaveFrame
    //     0x7f5310: mov             SP, fp
    //     0x7f5314: ldp             fp, lr, [SP], #0x10
    // 0x7f5318: ret
    //     0x7f5318: ret             
    // 0x7f531c: cmp             w4, NULL
    // 0x7f5320: b.eq            #0x7f5364
    // 0x7f5324: mov             x2, x0
    // 0x7f5328: r1 = Function '_updateTicker@356311458':.
    //     0x7f5328: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c730] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f532c: ldr             x1, [x1, #0x730]
    // 0x7f5330: r0 = AllocateClosure()
    //     0x7f5330: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f5334: ldur            x1, [fp, #-0x10]
    // 0x7f5338: r2 = LoadClassIdInstr(r1)
    //     0x7f5338: ldur            x2, [x1, #-1]
    //     0x7f533c: ubfx            x2, x2, #0xc, #0x14
    // 0x7f5340: mov             x16, x0
    // 0x7f5344: mov             x0, x2
    // 0x7f5348: mov             x2, x16
    // 0x7f534c: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x7f534c: movz            x17, #0xdc2b
    //     0x7f5350: add             lr, x0, x17
    //     0x7f5354: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5358: blr             lr
    // 0x7f535c: ldur            x0, [fp, #-8]
    // 0x7f5360: ldur            x3, [fp, #-0x18]
    // 0x7f5364: mov             x2, x0
    // 0x7f5368: r1 = Function '_updateTicker@356311458':.
    //     0x7f5368: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c730] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f536c: ldr             x1, [x1, #0x730]
    // 0x7f5370: r0 = AllocateClosure()
    //     0x7f5370: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f5374: ldur            x3, [fp, #-0x18]
    // 0x7f5378: r1 = LoadClassIdInstr(r3)
    //     0x7f5378: ldur            x1, [x3, #-1]
    //     0x7f537c: ubfx            x1, x1, #0xc, #0x14
    // 0x7f5380: mov             x2, x0
    // 0x7f5384: mov             x0, x1
    // 0x7f5388: mov             x1, x3
    // 0x7f538c: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x7f538c: movz            x17, #0xdc71
    //     0x7f5390: add             lr, x0, x17
    //     0x7f5394: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5398: blr             lr
    // 0x7f539c: ldur            x0, [fp, #-0x18]
    // 0x7f53a0: ldur            x1, [fp, #-8]
    // 0x7f53a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f53a4: stur            w0, [x1, #0x17]
    //     0x7f53a8: ldurb           w16, [x1, #-1]
    //     0x7f53ac: ldurb           w17, [x0, #-1]
    //     0x7f53b0: and             x16, x17, x16, lsr #2
    //     0x7f53b4: tst             x16, HEAP, lsr #32
    //     0x7f53b8: b.eq            #0x7f53c0
    //     0x7f53bc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x7f53c0: r0 = Null
    //     0x7f53c0: mov             x0, NULL
    // 0x7f53c4: LeaveFrame
    //     0x7f53c4: mov             SP, fp
    //     0x7f53c8: ldp             fp, lr, [SP], #0x10
    // 0x7f53cc: ret
    //     0x7f53cc: ret             
    // 0x7f53d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f53d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f53d4: b               #0x7f52d8
    // 0x7f53d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f53d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87c78, size: 0x94
    // 0xc87c78: EnterFrame
    //     0xc87c78: stp             fp, lr, [SP, #-0x10]!
    //     0xc87c7c: mov             fp, SP
    // 0xc87c80: AllocStack(0x10)
    //     0xc87c80: sub             SP, SP, #0x10
    // 0xc87c84: SetupParameters(__MainPageState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc87c84: mov             x0, x1
    //     0xc87c88: stur            x1, [fp, #-0x10]
    // 0xc87c8c: CheckStackOverflow
    //     0xc87c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87c90: cmp             SP, x16
    //     0xc87c94: b.ls            #0xc87d04
    // 0xc87c98: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc87c98: ldur            w3, [x0, #0x17]
    // 0xc87c9c: DecompressPointer r3
    //     0xc87c9c: add             x3, x3, HEAP, lsl #32
    // 0xc87ca0: stur            x3, [fp, #-8]
    // 0xc87ca4: cmp             w3, NULL
    // 0xc87ca8: b.ne            #0xc87cb4
    // 0xc87cac: mov             x1, x0
    // 0xc87cb0: b               #0xc87cf0
    // 0xc87cb4: mov             x2, x0
    // 0xc87cb8: r1 = Function '_updateTicker@356311458':.
    //     0xc87cb8: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c730] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc87cbc: ldr             x1, [x1, #0x730]
    // 0xc87cc0: r0 = AllocateClosure()
    //     0xc87cc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc87cc4: ldur            x1, [fp, #-8]
    // 0xc87cc8: r2 = LoadClassIdInstr(r1)
    //     0xc87cc8: ldur            x2, [x1, #-1]
    //     0xc87ccc: ubfx            x2, x2, #0xc, #0x14
    // 0xc87cd0: mov             x16, x0
    // 0xc87cd4: mov             x0, x2
    // 0xc87cd8: mov             x2, x16
    // 0xc87cdc: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc87cdc: movz            x17, #0xdc2b
    //     0xc87ce0: add             lr, x0, x17
    //     0xc87ce4: ldr             lr, [x21, lr, lsl #3]
    //     0xc87ce8: blr             lr
    // 0xc87cec: ldur            x1, [fp, #-0x10]
    // 0xc87cf0: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc87cf0: stur            NULL, [x1, #0x17]
    // 0xc87cf4: r0 = Null
    //     0xc87cf4: mov             x0, NULL
    // 0xc87cf8: LeaveFrame
    //     0xc87cf8: mov             SP, fp
    //     0xc87cfc: ldp             fp, lr, [SP], #0x10
    // 0xc87d00: ret
    //     0xc87d00: ret             
    // 0xc87d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87d04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87d08: b               #0xc87c98
  }
}

// class id: 3329, size: 0x54, field offset: 0x1c
class _MainPageState extends __MainPageState&State&SingleTickerProviderStateMixin {

  static late final List<Widget> _pages; // offset: 0xe70

  _ initState(/* No info */) {
    // ** addr: 0x942584, size: 0x194
    // 0x942584: EnterFrame
    //     0x942584: stp             fp, lr, [SP, #-0x10]!
    //     0x942588: mov             fp, SP
    // 0x94258c: AllocStack(0x20)
    //     0x94258c: sub             SP, SP, #0x20
    // 0x942590: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0x942590: stur            x1, [fp, #-8]
    // 0x942594: CheckStackOverflow
    //     0x942594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942598: cmp             SP, x16
    //     0x94259c: b.ls            #0x942710
    // 0x9425a0: r1 = 1
    //     0x9425a0: movz            x1, #0x1
    // 0x9425a4: r0 = AllocateContext()
    //     0x9425a4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9425a8: ldur            x1, [fp, #-8]
    // 0x9425ac: stur            x0, [fp, #-0x10]
    // 0x9425b0: StoreField: r0->field_f = r1
    //     0x9425b0: stur            w1, [x0, #0xf]
    // 0x9425b4: r0 = InitLateStaticField(0xbec) // [package:app_links/src/app_links.dart] AppLinks::_instance
    //     0x9425b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9425b8: ldr             x0, [x0, #0x17d8]
    //     0x9425bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9425c0: cmp             w0, w16
    //     0x9425c4: b.ne            #0x9425d4
    //     0x9425c8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e8] Field <AppLinks._instance@597120238>: static late final (offset: 0xbec)
    //     0x9425cc: ldr             x2, [x2, #0x7e8]
    //     0x9425d0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9425d4: mov             x1, x0
    // 0x9425d8: ldur            x2, [fp, #-8]
    // 0x9425dc: StoreField: r2->field_43 = r0
    //     0x9425dc: stur            w0, [x2, #0x43]
    //     0x9425e0: ldurb           w16, [x2, #-1]
    //     0x9425e4: ldurb           w17, [x0, #-1]
    //     0x9425e8: and             x16, x17, x16, lsr #2
    //     0x9425ec: tst             x16, HEAP, lsr #32
    //     0x9425f0: b.eq            #0x9425f8
    //     0x9425f4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9425f8: r0 = uriLinkStream()
    //     0x9425f8: bl              #0x914518  ; [package:app_links/src/app_links.dart] AppLinks::uriLinkStream
    // 0x9425fc: ldur            x2, [fp, #-0x10]
    // 0x942600: r1 = Function '<anonymous closure>':.
    //     0x942600: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc58] AnonymousClosure: (0x943070), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::initState (0x942584)
    //     0x942604: ldr             x1, [x1, #0xc58]
    // 0x942608: stur            x0, [fp, #-0x10]
    // 0x94260c: r0 = AllocateClosure()
    //     0x94260c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x942610: ldur            x1, [fp, #-0x10]
    // 0x942614: mov             x2, x0
    // 0x942618: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x942618: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x94261c: r0 = listen()
    //     0x94261c: bl              #0x163918c  ; [dart:async] _StreamImpl::listen
    // 0x942620: ldur            x1, [fp, #-8]
    // 0x942624: StoreField: r1->field_47 = r0
    //     0x942624: stur            w0, [x1, #0x47]
    //     0x942628: ldurb           w16, [x1, #-1]
    //     0x94262c: ldurb           w17, [x0, #-1]
    //     0x942630: and             x16, x17, x16, lsr #2
    //     0x942634: tst             x16, HEAP, lsr #32
    //     0x942638: b.eq            #0x942640
    //     0x94263c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x942640: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x942640: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x942644: ldr             x0, [x0, #0x1c80]
    //     0x942648: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x94264c: cmp             w0, w16
    //     0x942650: b.ne            #0x94265c
    //     0x942654: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x942658: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x94265c: r0 = GetNavigation.arguments()
    //     0x94265c: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x942660: cmp             w0, NULL
    // 0x942664: b.eq            #0x9426e4
    // 0x942668: ldur            x1, [fp, #-8]
    // 0x94266c: r16 = "selected_bottom_index"
    //     0x94266c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb478] "selected_bottom_index"
    //     0x942670: ldr             x16, [x16, #0x478]
    // 0x942674: stp             x16, x0, [SP]
    // 0x942678: r4 = 0
    //     0x942678: movz            x4, #0
    // 0x94267c: ldr             x0, [SP, #8]
    // 0x942680: r16 = UnlinkedCall_0x613b5c
    //     0x942680: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dc60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x942684: add             x16, x16, #0xc60
    // 0x942688: ldp             x5, lr, [x16]
    // 0x94268c: blr             lr
    // 0x942690: mov             x3, x0
    // 0x942694: r2 = Null
    //     0x942694: mov             x2, NULL
    // 0x942698: r1 = Null
    //     0x942698: mov             x1, NULL
    // 0x94269c: stur            x3, [fp, #-0x10]
    // 0x9426a0: branchIfSmi(r0, 0x9426c8)
    //     0x9426a0: tbz             w0, #0, #0x9426c8
    // 0x9426a4: r4 = LoadClassIdInstr(r0)
    //     0x9426a4: ldur            x4, [x0, #-1]
    //     0x9426a8: ubfx            x4, x4, #0xc, #0x14
    // 0x9426ac: sub             x4, x4, #0x3c
    // 0x9426b0: cmp             x4, #1
    // 0x9426b4: b.ls            #0x9426c8
    // 0x9426b8: r8 = int
    //     0x9426b8: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x9426bc: r3 = Null
    //     0x9426bc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc70] Null
    //     0x9426c0: ldr             x3, [x3, #0xc70]
    // 0x9426c4: r0 = int()
    //     0x9426c4: bl              #0x16fc548  ; IsType_int_Stub
    // 0x9426c8: ldur            x0, [fp, #-0x10]
    // 0x9426cc: r1 = LoadInt32Instr(r0)
    //     0x9426cc: sbfx            x1, x0, #1, #0x1f
    //     0x9426d0: tbz             w0, #0, #0x9426d8
    //     0x9426d4: ldur            x1, [x0, #7]
    // 0x9426d8: ldur            x0, [fp, #-8]
    // 0x9426dc: StoreField: r0->field_1b = r1
    //     0x9426dc: stur            x1, [x0, #0x1b]
    // 0x9426e0: b               #0x9426e8
    // 0x9426e4: ldur            x0, [fp, #-8]
    // 0x9426e8: LoadField: r1 = r0->field_1b
    //     0x9426e8: ldur            x1, [x0, #0x1b]
    // 0x9426ec: cmp             x1, #1
    // 0x9426f0: b.ne            #0x942700
    // 0x9426f4: mov             x1, x0
    // 0x9426f8: r2 = 2
    //     0x9426f8: movz            x2, #0x2
    // 0x9426fc: r0 = _onItemTapped()
    //     0x9426fc: bl              #0x942718  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x942700: r0 = Null
    //     0x942700: mov             x0, NULL
    // 0x942704: LeaveFrame
    //     0x942704: mov             SP, fp
    //     0x942708: ldp             fp, lr, [SP], #0x10
    // 0x94270c: ret
    //     0x94270c: ret             
    // 0x942710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942710: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942714: b               #0x9425a0
  }
  _ _onItemTapped(/* No info */) {
    // ** addr: 0x942718, size: 0x70
    // 0x942718: EnterFrame
    //     0x942718: stp             fp, lr, [SP, #-0x10]!
    //     0x94271c: mov             fp, SP
    // 0x942720: AllocStack(0x10)
    //     0x942720: sub             SP, SP, #0x10
    // 0x942724: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x942724: stur            x1, [fp, #-8]
    //     0x942728: stur            x2, [fp, #-0x10]
    // 0x94272c: CheckStackOverflow
    //     0x94272c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942730: cmp             SP, x16
    //     0x942734: b.ls            #0x942780
    // 0x942738: r1 = 2
    //     0x942738: movz            x1, #0x2
    // 0x94273c: r0 = AllocateContext()
    //     0x94273c: bl              #0x16f6108  ; AllocateContextStub
    // 0x942740: mov             x1, x0
    // 0x942744: ldur            x0, [fp, #-8]
    // 0x942748: StoreField: r1->field_f = r0
    //     0x942748: stur            w0, [x1, #0xf]
    // 0x94274c: ldur            x2, [fp, #-0x10]
    // 0x942750: StoreField: r1->field_13 = r2
    //     0x942750: stur            w2, [x1, #0x13]
    // 0x942754: mov             x2, x1
    // 0x942758: r1 = Function '<anonymous closure>':.
    //     0x942758: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2db88] AnonymousClosure: (0x9427c4), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped (0x942718)
    //     0x94275c: ldr             x1, [x1, #0xb88]
    // 0x942760: r0 = AllocateClosure()
    //     0x942760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x942764: ldur            x1, [fp, #-8]
    // 0x942768: mov             x2, x0
    // 0x94276c: r0 = setState()
    //     0x94276c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x942770: r0 = Null
    //     0x942770: mov             x0, NULL
    // 0x942774: LeaveFrame
    //     0x942774: mov             SP, fp
    //     0x942778: ldp             fp, lr, [SP], #0x10
    // 0x94277c: ret
    //     0x94277c: ret             
    // 0x942780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942780: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942784: b               #0x942738
  }
  [closure] void _onItemTapped(dynamic, int) {
    // ** addr: 0x942788, size: 0x3c
    // 0x942788: EnterFrame
    //     0x942788: stp             fp, lr, [SP, #-0x10]!
    //     0x94278c: mov             fp, SP
    // 0x942790: ldr             x0, [fp, #0x18]
    // 0x942794: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x942794: ldur            w1, [x0, #0x17]
    // 0x942798: DecompressPointer r1
    //     0x942798: add             x1, x1, HEAP, lsl #32
    // 0x94279c: CheckStackOverflow
    //     0x94279c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9427a0: cmp             SP, x16
    //     0x9427a4: b.ls            #0x9427bc
    // 0x9427a8: ldr             x2, [fp, #0x10]
    // 0x9427ac: r0 = _onItemTapped()
    //     0x9427ac: bl              #0x942718  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x9427b0: LeaveFrame
    //     0x9427b0: mov             SP, fp
    //     0x9427b4: ldp             fp, lr, [SP], #0x10
    // 0x9427b8: ret
    //     0x9427b8: ret             
    // 0x9427bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9427bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9427c0: b               #0x9427a8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9427c4, size: 0x2ac
    // 0x9427c4: EnterFrame
    //     0x9427c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9427c8: mov             fp, SP
    // 0x9427cc: AllocStack(0x38)
    //     0x9427cc: sub             SP, SP, #0x38
    // 0x9427d0: SetupParameters()
    //     0x9427d0: ldr             x0, [fp, #0x10]
    //     0x9427d4: ldur            w2, [x0, #0x17]
    //     0x9427d8: add             x2, x2, HEAP, lsl #32
    //     0x9427dc: stur            x2, [fp, #-8]
    // 0x9427e0: CheckStackOverflow
    //     0x9427e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9427e4: cmp             SP, x16
    //     0x9427e8: b.ls            #0x942a60
    // 0x9427ec: LoadField: r0 = r2->field_f
    //     0x9427ec: ldur            w0, [x2, #0xf]
    // 0x9427f0: DecompressPointer r0
    //     0x9427f0: add             x0, x0, HEAP, lsl #32
    // 0x9427f4: LoadField: r1 = r2->field_13
    //     0x9427f4: ldur            w1, [x2, #0x13]
    // 0x9427f8: DecompressPointer r1
    //     0x9427f8: add             x1, x1, HEAP, lsl #32
    // 0x9427fc: r3 = LoadInt32Instr(r1)
    //     0x9427fc: sbfx            x3, x1, #1, #0x1f
    //     0x942800: tbz             w1, #0, #0x942808
    //     0x942804: ldur            x3, [x1, #7]
    // 0x942808: StoreField: r0->field_1b = r3
    //     0x942808: stur            x3, [x0, #0x1b]
    // 0x94280c: cbnz            x3, #0x9428ac
    // 0x942810: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0x942810: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x942814: ldr             x0, [x0, #0x1ce0]
    //     0x942818: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x94281c: cmp             w0, w16
    //     0x942820: b.ne            #0x942830
    //     0x942824: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0x942828: ldr             x2, [x2, #0xb30]
    //     0x94282c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x942830: mov             x2, x0
    // 0x942834: LoadField: r0 = r2->field_b
    //     0x942834: ldur            w0, [x2, #0xb]
    // 0x942838: r1 = LoadInt32Instr(r0)
    //     0x942838: sbfx            x1, x0, #1, #0x1f
    // 0x94283c: mov             x0, x1
    // 0x942840: r1 = 0
    //     0x942840: movz            x1, #0
    // 0x942844: cmp             x1, x0
    // 0x942848: b.hs            #0x942a68
    // 0x94284c: LoadField: r0 = r2->field_f
    //     0x94284c: ldur            w0, [x2, #0xf]
    // 0x942850: DecompressPointer r0
    //     0x942850: add             x0, x0, HEAP, lsl #32
    // 0x942854: LoadField: r3 = r0->field_f
    //     0x942854: ldur            w3, [x0, #0xf]
    // 0x942858: DecompressPointer r3
    //     0x942858: add             x3, x3, HEAP, lsl #32
    // 0x94285c: mov             x0, x3
    // 0x942860: stur            x3, [fp, #-0x10]
    // 0x942864: r2 = Null
    //     0x942864: mov             x2, NULL
    // 0x942868: r1 = Null
    //     0x942868: mov             x1, NULL
    // 0x94286c: r4 = 60
    //     0x94286c: movz            x4, #0x3c
    // 0x942870: branchIfSmi(r0, 0x94287c)
    //     0x942870: tbz             w0, #0, #0x94287c
    // 0x942874: r4 = LoadClassIdInstr(r0)
    //     0x942874: ldur            x4, [x0, #-1]
    //     0x942878: ubfx            x4, x4, #0xc, #0x14
    // 0x94287c: r17 = 4566
    //     0x94287c: movz            x17, #0x11d6
    // 0x942880: cmp             x4, x17
    // 0x942884: b.eq            #0x94289c
    // 0x942888: r8 = HomePage
    //     0x942888: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db90] Type: HomePage
    //     0x94288c: ldr             x8, [x8, #0xb90]
    // 0x942890: r3 = Null
    //     0x942890: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db98] Null
    //     0x942894: ldr             x3, [x3, #0xb98]
    // 0x942898: r0 = DefaultTypeTest()
    //     0x942898: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x94289c: ldur            x1, [fp, #-0x10]
    // 0x9428a0: r0 = controller()
    //     0x9428a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9428a4: mov             x1, x0
    // 0x9428a8: r0 = onRefreshPage()
    //     0x9428a8: bl              #0x908a78  ; [package:customer_app/app/presentation/controllers/home/<USER>
    // 0x9428ac: ldur            x2, [fp, #-8]
    // 0x9428b0: LoadField: r0 = r2->field_13
    //     0x9428b0: ldur            w0, [x2, #0x13]
    // 0x9428b4: DecompressPointer r0
    //     0x9428b4: add             x0, x0, HEAP, lsl #32
    // 0x9428b8: r1 = LoadInt32Instr(r0)
    //     0x9428b8: sbfx            x1, x0, #1, #0x1f
    //     0x9428bc: tbz             w0, #0, #0x9428c4
    //     0x9428c0: ldur            x1, [x0, #7]
    // 0x9428c4: cmp             x1, #1
    // 0x9428c8: b.ne            #0x942a04
    // 0x9428cc: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0x9428cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9428d0: ldr             x0, [x0, #0x1ce0]
    //     0x9428d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9428d8: cmp             w0, w16
    //     0x9428dc: b.ne            #0x9428ec
    //     0x9428e0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0x9428e4: ldr             x2, [x2, #0xb30]
    //     0x9428e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9428ec: mov             x2, x0
    // 0x9428f0: LoadField: r0 = r2->field_b
    //     0x9428f0: ldur            w0, [x2, #0xb]
    // 0x9428f4: r1 = LoadInt32Instr(r0)
    //     0x9428f4: sbfx            x1, x0, #1, #0x1f
    // 0x9428f8: mov             x0, x1
    // 0x9428fc: r1 = 1
    //     0x9428fc: movz            x1, #0x1
    // 0x942900: cmp             x1, x0
    // 0x942904: b.hs            #0x942a6c
    // 0x942908: LoadField: r0 = r2->field_f
    //     0x942908: ldur            w0, [x2, #0xf]
    // 0x94290c: DecompressPointer r0
    //     0x94290c: add             x0, x0, HEAP, lsl #32
    // 0x942910: LoadField: r3 = r0->field_13
    //     0x942910: ldur            w3, [x0, #0x13]
    // 0x942914: DecompressPointer r3
    //     0x942914: add             x3, x3, HEAP, lsl #32
    // 0x942918: mov             x0, x3
    // 0x94291c: stur            x3, [fp, #-0x10]
    // 0x942920: r2 = Null
    //     0x942920: mov             x2, NULL
    // 0x942924: r1 = Null
    //     0x942924: mov             x1, NULL
    // 0x942928: r4 = 60
    //     0x942928: movz            x4, #0x3c
    // 0x94292c: branchIfSmi(r0, 0x942938)
    //     0x94292c: tbz             w0, #0, #0x942938
    // 0x942930: r4 = LoadClassIdInstr(r0)
    //     0x942930: ldur            x4, [x0, #-1]
    //     0x942934: ubfx            x4, x4, #0xc, #0x14
    // 0x942938: r17 = 4563
    //     0x942938: movz            x17, #0x11d3
    // 0x94293c: cmp             x4, x17
    // 0x942940: b.eq            #0x942958
    // 0x942944: r8 = OrdersView
    //     0x942944: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0x942948: ldr             x8, [x8, #0xb38]
    // 0x94294c: r3 = Null
    //     0x94294c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dba8] Null
    //     0x942950: ldr             x3, [x3, #0xba8]
    // 0x942954: r0 = DefaultTypeTest()
    //     0x942954: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x942958: ldur            x1, [fp, #-0x10]
    // 0x94295c: r0 = controller()
    //     0x94295c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x942960: mov             x1, x0
    // 0x942964: r0 = getBagCount()
    //     0x942964: bl              #0x8a92ac  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getBagCount
    // 0x942968: ldur            x0, [fp, #-8]
    // 0x94296c: LoadField: r1 = r0->field_f
    //     0x94296c: ldur            w1, [x0, #0xf]
    // 0x942970: DecompressPointer r1
    //     0x942970: add             x1, x1, HEAP, lsl #32
    // 0x942974: LoadField: r2 = r1->field_3f
    //     0x942974: ldur            w2, [x1, #0x3f]
    // 0x942978: DecompressPointer r2
    //     0x942978: add             x2, x2, HEAP, lsl #32
    // 0x94297c: LoadField: r1 = r2->field_4b
    //     0x94297c: ldur            w1, [x2, #0x4b]
    // 0x942980: DecompressPointer r1
    //     0x942980: add             x1, x1, HEAP, lsl #32
    // 0x942984: r16 = ""
    //     0x942984: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x942988: str             x16, [SP]
    // 0x94298c: r2 = "token"
    //     0x94298c: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x942990: ldr             x2, [x2, #0x958]
    // 0x942994: r4 = const [0, 0x3, 0x1, 0x2, defaultValue, 0x2, null]
    //     0x942994: add             x4, PP, #0x11, lsl #12  ; [pp+0x11f48] List(7) [0, 0x3, 0x1, 0x2, "defaultValue", 0x2, Null]
    //     0x942998: ldr             x4, [x4, #0xf48]
    // 0x94299c: r0 = getString()
    //     0x94299c: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x9429a0: stur            x0, [fp, #-0x18]
    // 0x9429a4: LoadField: r3 = r0->field_7
    //     0x9429a4: ldur            w3, [x0, #7]
    // 0x9429a8: DecompressPointer r3
    //     0x9429a8: add             x3, x3, HEAP, lsl #32
    // 0x9429ac: ldur            x2, [fp, #-8]
    // 0x9429b0: stur            x3, [fp, #-0x10]
    // 0x9429b4: r1 = Function '<anonymous closure>':.
    //     0x9429b4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dbb8] AnonymousClosure: (0x942be4), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped (0x942718)
    //     0x9429b8: ldr             x1, [x1, #0xbb8]
    // 0x9429bc: r0 = AllocateClosure()
    //     0x9429bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9429c0: ldur            x2, [fp, #-0x10]
    // 0x9429c4: mov             x3, x0
    // 0x9429c8: r1 = Null
    //     0x9429c8: mov             x1, NULL
    // 0x9429cc: stur            x3, [fp, #-0x10]
    // 0x9429d0: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0x9429d0: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0x9429d4: ldr             x8, [x8, #0xce8]
    // 0x9429d8: LoadField: r9 = r8->field_7
    //     0x9429d8: ldur            x9, [x8, #7]
    // 0x9429dc: r3 = Null
    //     0x9429dc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dbc0] Null
    //     0x9429e0: ldr             x3, [x3, #0xbc0]
    // 0x9429e4: blr             x9
    // 0x9429e8: ldur            x16, [fp, #-0x18]
    // 0x9429ec: stp             x16, NULL, [SP, #0x10]
    // 0x9429f0: ldur            x16, [fp, #-0x10]
    // 0x9429f4: stp             NULL, x16, [SP]
    // 0x9429f8: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x9429f8: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x9429fc: r0 = then()
    //     0x9429fc: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x942a00: b               #0x942a50
    // 0x942a04: cmp             x1, #2
    // 0x942a08: b.ne            #0x942a50
    // 0x942a0c: ldur            x2, [fp, #-8]
    // 0x942a10: LoadField: r0 = r2->field_f
    //     0x942a10: ldur            w0, [x2, #0xf]
    // 0x942a14: DecompressPointer r0
    //     0x942a14: add             x0, x0, HEAP, lsl #32
    // 0x942a18: LoadField: r1 = r0->field_4b
    //     0x942a18: ldur            w1, [x0, #0x4b]
    // 0x942a1c: DecompressPointer r1
    //     0x942a1c: add             x1, x1, HEAP, lsl #32
    // 0x942a20: r0 = getConnectivityType()
    //     0x942a20: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x942a24: ldur            x2, [fp, #-8]
    // 0x942a28: r1 = Function '<anonymous closure>':.
    //     0x942a28: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dbd0] AnonymousClosure: (0x942a70), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped (0x942718)
    //     0x942a2c: ldr             x1, [x1, #0xbd0]
    // 0x942a30: stur            x0, [fp, #-8]
    // 0x942a34: r0 = AllocateClosure()
    //     0x942a34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x942a38: r16 = <Null?>
    //     0x942a38: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x942a3c: ldur            lr, [fp, #-8]
    // 0x942a40: stp             lr, x16, [SP, #8]
    // 0x942a44: str             x0, [SP]
    // 0x942a48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x942a48: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x942a4c: r0 = then()
    //     0x942a4c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x942a50: r0 = Null
    //     0x942a50: mov             x0, NULL
    // 0x942a54: LeaveFrame
    //     0x942a54: mov             SP, fp
    //     0x942a58: ldp             fp, lr, [SP], #0x10
    // 0x942a5c: ret
    //     0x942a5c: ret             
    // 0x942a60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942a60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942a64: b               #0x9427ec
    // 0x942a68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942a68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x942a6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942a6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x942a70, size: 0x174
    // 0x942a70: EnterFrame
    //     0x942a70: stp             fp, lr, [SP, #-0x10]!
    //     0x942a74: mov             fp, SP
    // 0x942a78: AllocStack(0x10)
    //     0x942a78: sub             SP, SP, #0x10
    // 0x942a7c: SetupParameters()
    //     0x942a7c: ldr             x0, [fp, #0x18]
    //     0x942a80: ldur            w1, [x0, #0x17]
    //     0x942a84: add             x1, x1, HEAP, lsl #32
    // 0x942a88: CheckStackOverflow
    //     0x942a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942a8c: cmp             SP, x16
    //     0x942a90: b.ls            #0x942bd4
    // 0x942a94: LoadField: r0 = r1->field_f
    //     0x942a94: ldur            w0, [x1, #0xf]
    // 0x942a98: DecompressPointer r0
    //     0x942a98: add             x0, x0, HEAP, lsl #32
    // 0x942a9c: ldr             x1, [fp, #0x10]
    // 0x942aa0: StoreField: r0->field_4f = r1
    //     0x942aa0: stur            w1, [x0, #0x4f]
    // 0x942aa4: tbnz            w1, #4, #0x942bc4
    // 0x942aa8: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0x942aa8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x942aac: ldr             x0, [x0, #0x1ce0]
    //     0x942ab0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x942ab4: cmp             w0, w16
    //     0x942ab8: b.ne            #0x942ac8
    //     0x942abc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0x942ac0: ldr             x2, [x2, #0xb30]
    //     0x942ac4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x942ac8: mov             x3, x0
    // 0x942acc: stur            x3, [fp, #-0x10]
    // 0x942ad0: LoadField: r0 = r3->field_b
    //     0x942ad0: ldur            w0, [x3, #0xb]
    // 0x942ad4: r1 = LoadInt32Instr(r0)
    //     0x942ad4: sbfx            x1, x0, #1, #0x1f
    // 0x942ad8: mov             x0, x1
    // 0x942adc: r1 = 2
    //     0x942adc: movz            x1, #0x2
    // 0x942ae0: cmp             x1, x0
    // 0x942ae4: b.hs            #0x942bdc
    // 0x942ae8: LoadField: r0 = r3->field_f
    //     0x942ae8: ldur            w0, [x3, #0xf]
    // 0x942aec: DecompressPointer r0
    //     0x942aec: add             x0, x0, HEAP, lsl #32
    // 0x942af0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x942af0: ldur            w4, [x0, #0x17]
    // 0x942af4: DecompressPointer r4
    //     0x942af4: add             x4, x4, HEAP, lsl #32
    // 0x942af8: mov             x0, x4
    // 0x942afc: stur            x4, [fp, #-8]
    // 0x942b00: r2 = Null
    //     0x942b00: mov             x2, NULL
    // 0x942b04: r1 = Null
    //     0x942b04: mov             x1, NULL
    // 0x942b08: r4 = 60
    //     0x942b08: movz            x4, #0x3c
    // 0x942b0c: branchIfSmi(r0, 0x942b18)
    //     0x942b0c: tbz             w0, #0, #0x942b18
    // 0x942b10: r4 = LoadClassIdInstr(r0)
    //     0x942b10: ldur            x4, [x0, #-1]
    //     0x942b14: ubfx            x4, x4, #0xc, #0x14
    // 0x942b18: r17 = 4553
    //     0x942b18: movz            x17, #0x11c9
    // 0x942b1c: cmp             x4, x17
    // 0x942b20: b.eq            #0x942b38
    // 0x942b24: r8 = ProfileView
    //     0x942b24: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dbd8] Type: ProfileView
    //     0x942b28: ldr             x8, [x8, #0xbd8]
    // 0x942b2c: r3 = Null
    //     0x942b2c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dbe0] Null
    //     0x942b30: ldr             x3, [x3, #0xbe0]
    // 0x942b34: r0 = DefaultTypeTest()
    //     0x942b34: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x942b38: ldur            x1, [fp, #-8]
    // 0x942b3c: r0 = controller()
    //     0x942b3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x942b40: mov             x1, x0
    // 0x942b44: r0 = checkLoginStatus()
    //     0x942b44: bl              #0x913d8c  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::checkLoginStatus
    // 0x942b48: ldur            x2, [fp, #-0x10]
    // 0x942b4c: LoadField: r0 = r2->field_b
    //     0x942b4c: ldur            w0, [x2, #0xb]
    // 0x942b50: r1 = LoadInt32Instr(r0)
    //     0x942b50: sbfx            x1, x0, #1, #0x1f
    // 0x942b54: mov             x0, x1
    // 0x942b58: r1 = 2
    //     0x942b58: movz            x1, #0x2
    // 0x942b5c: cmp             x1, x0
    // 0x942b60: b.hs            #0x942be0
    // 0x942b64: LoadField: r0 = r2->field_f
    //     0x942b64: ldur            w0, [x2, #0xf]
    // 0x942b68: DecompressPointer r0
    //     0x942b68: add             x0, x0, HEAP, lsl #32
    // 0x942b6c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x942b6c: ldur            w3, [x0, #0x17]
    // 0x942b70: DecompressPointer r3
    //     0x942b70: add             x3, x3, HEAP, lsl #32
    // 0x942b74: mov             x0, x3
    // 0x942b78: stur            x3, [fp, #-8]
    // 0x942b7c: r2 = Null
    //     0x942b7c: mov             x2, NULL
    // 0x942b80: r1 = Null
    //     0x942b80: mov             x1, NULL
    // 0x942b84: r4 = 60
    //     0x942b84: movz            x4, #0x3c
    // 0x942b88: branchIfSmi(r0, 0x942b94)
    //     0x942b88: tbz             w0, #0, #0x942b94
    // 0x942b8c: r4 = LoadClassIdInstr(r0)
    //     0x942b8c: ldur            x4, [x0, #-1]
    //     0x942b90: ubfx            x4, x4, #0xc, #0x14
    // 0x942b94: r17 = 4553
    //     0x942b94: movz            x17, #0x11c9
    // 0x942b98: cmp             x4, x17
    // 0x942b9c: b.eq            #0x942bb4
    // 0x942ba0: r8 = ProfileView
    //     0x942ba0: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dbd8] Type: ProfileView
    //     0x942ba4: ldr             x8, [x8, #0xbd8]
    // 0x942ba8: r3 = Null
    //     0x942ba8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dbf0] Null
    //     0x942bac: ldr             x3, [x3, #0xbf0]
    // 0x942bb0: r0 = DefaultTypeTest()
    //     0x942bb0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x942bb4: ldur            x1, [fp, #-8]
    // 0x942bb8: r0 = controller()
    //     0x942bb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x942bbc: mov             x1, x0
    // 0x942bc0: r0 = getBagCount()
    //     0x942bc0: bl              #0x9134f0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::getBagCount
    // 0x942bc4: r0 = Null
    //     0x942bc4: mov             x0, NULL
    // 0x942bc8: LeaveFrame
    //     0x942bc8: mov             SP, fp
    //     0x942bcc: ldp             fp, lr, [SP], #0x10
    // 0x942bd0: ret
    //     0x942bd0: ret             
    // 0x942bd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942bd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942bd8: b               #0x942a94
    // 0x942bdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942bdc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x942be0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942be0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Set<Set<dynamic>> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x942be4, size: 0x1c8
    // 0x942be4: EnterFrame
    //     0x942be4: stp             fp, lr, [SP, #-0x10]!
    //     0x942be8: mov             fp, SP
    // 0x942bec: AllocStack(0x30)
    //     0x942bec: sub             SP, SP, #0x30
    // 0x942bf0: SetupParameters()
    //     0x942bf0: ldr             x0, [fp, #0x18]
    //     0x942bf4: ldur            w2, [x0, #0x17]
    //     0x942bf8: add             x2, x2, HEAP, lsl #32
    //     0x942bfc: stur            x2, [fp, #-8]
    // 0x942c00: CheckStackOverflow
    //     0x942c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942c04: cmp             SP, x16
    //     0x942c08: b.ls            #0x942da4
    // 0x942c0c: r1 = <Set>
    //     0x942c0c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24550] TypeArguments: <Set>
    //     0x942c10: ldr             x1, [x1, #0x550]
    // 0x942c14: r0 = _Set()
    //     0x942c14: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x942c18: mov             x1, x0
    // 0x942c1c: r0 = _Uint32List
    //     0x942c1c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x942c20: stur            x1, [fp, #-0x10]
    // 0x942c24: StoreField: r1->field_1b = r0
    //     0x942c24: stur            w0, [x1, #0x1b]
    // 0x942c28: StoreField: r1->field_b = rZR
    //     0x942c28: stur            wzr, [x1, #0xb]
    // 0x942c2c: r2 = const []
    //     0x942c2c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x942c30: StoreField: r1->field_f = r2
    //     0x942c30: stur            w2, [x1, #0xf]
    // 0x942c34: StoreField: r1->field_13 = rZR
    //     0x942c34: stur            wzr, [x1, #0x13]
    // 0x942c38: ArrayStore: r1[0] = rZR  ; List_4
    //     0x942c38: stur            wzr, [x1, #0x17]
    // 0x942c3c: ldr             x16, [fp, #0x10]
    // 0x942c40: str             x16, [SP]
    // 0x942c44: r4 = 0
    //     0x942c44: movz            x4, #0
    // 0x942c48: ldr             x0, [SP]
    // 0x942c4c: r16 = UnlinkedCall_0x613b5c
    //     0x942c4c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dc00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x942c50: add             x16, x16, #0xc00
    // 0x942c54: ldp             x5, lr, [x16]
    // 0x942c58: blr             lr
    // 0x942c5c: mov             x3, x0
    // 0x942c60: r2 = Null
    //     0x942c60: mov             x2, NULL
    // 0x942c64: r1 = Null
    //     0x942c64: mov             x1, NULL
    // 0x942c68: stur            x3, [fp, #-0x18]
    // 0x942c6c: r4 = 60
    //     0x942c6c: movz            x4, #0x3c
    // 0x942c70: branchIfSmi(r0, 0x942c7c)
    //     0x942c70: tbz             w0, #0, #0x942c7c
    // 0x942c74: r4 = LoadClassIdInstr(r0)
    //     0x942c74: ldur            x4, [x0, #-1]
    //     0x942c78: ubfx            x4, x4, #0xc, #0x14
    // 0x942c7c: cmp             x4, #0x3f
    // 0x942c80: b.eq            #0x942c94
    // 0x942c84: r8 = bool
    //     0x942c84: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x942c88: r3 = Null
    //     0x942c88: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc10] Null
    //     0x942c8c: ldr             x3, [x3, #0xc10]
    // 0x942c90: r0 = bool()
    //     0x942c90: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x942c94: ldur            x0, [fp, #-0x18]
    // 0x942c98: tbnz            w0, #4, #0x942cf8
    // 0x942c9c: ldur            x2, [fp, #-8]
    // 0x942ca0: r1 = Null
    //     0x942ca0: mov             x1, NULL
    // 0x942ca4: r0 = _Set()
    //     0x942ca4: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x942ca8: mov             x2, x0
    // 0x942cac: r0 = _Uint32List
    //     0x942cac: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x942cb0: stur            x2, [fp, #-0x18]
    // 0x942cb4: StoreField: r2->field_1b = r0
    //     0x942cb4: stur            w0, [x2, #0x1b]
    // 0x942cb8: StoreField: r2->field_b = rZR
    //     0x942cb8: stur            wzr, [x2, #0xb]
    // 0x942cbc: r3 = const []
    //     0x942cbc: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x942cc0: StoreField: r2->field_f = r3
    //     0x942cc0: stur            w3, [x2, #0xf]
    // 0x942cc4: StoreField: r2->field_13 = rZR
    //     0x942cc4: stur            wzr, [x2, #0x13]
    // 0x942cc8: ArrayStore: r2[0] = rZR  ; List_4
    //     0x942cc8: stur            wzr, [x2, #0x17]
    // 0x942ccc: ldur            x4, [fp, #-8]
    // 0x942cd0: LoadField: r1 = r4->field_f
    //     0x942cd0: ldur            w1, [x4, #0xf]
    // 0x942cd4: DecompressPointer r1
    //     0x942cd4: add             x1, x1, HEAP, lsl #32
    // 0x942cd8: r0 = openLoginAwait()
    //     0x942cd8: bl              #0x942dac  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::openLoginAwait
    // 0x942cdc: ldur            x1, [fp, #-0x18]
    // 0x942ce0: mov             x2, x0
    // 0x942ce4: r0 = add()
    //     0x942ce4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x942ce8: ldur            x1, [fp, #-0x10]
    // 0x942cec: ldur            x2, [fp, #-0x18]
    // 0x942cf0: r0 = add()
    //     0x942cf0: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x942cf4: b               #0x942d94
    // 0x942cf8: ldur            x4, [fp, #-8]
    // 0x942cfc: r0 = _Uint32List
    //     0x942cfc: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x942d00: r3 = const []
    //     0x942d00: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x942d04: r1 = <Future<Null?>>
    //     0x942d04: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc88] TypeArguments: <Future<Null?>>
    //     0x942d08: ldr             x1, [x1, #0xc88]
    // 0x942d0c: r0 = _Set()
    //     0x942d0c: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x942d10: mov             x2, x0
    // 0x942d14: r0 = _Uint32List
    //     0x942d14: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x942d18: stur            x2, [fp, #-0x18]
    // 0x942d1c: StoreField: r2->field_1b = r0
    //     0x942d1c: stur            w0, [x2, #0x1b]
    // 0x942d20: StoreField: r2->field_b = rZR
    //     0x942d20: stur            wzr, [x2, #0xb]
    // 0x942d24: r0 = const []
    //     0x942d24: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x942d28: StoreField: r2->field_f = r0
    //     0x942d28: stur            w0, [x2, #0xf]
    // 0x942d2c: StoreField: r2->field_13 = rZR
    //     0x942d2c: stur            wzr, [x2, #0x13]
    // 0x942d30: ArrayStore: r2[0] = rZR  ; List_4
    //     0x942d30: stur            wzr, [x2, #0x17]
    // 0x942d34: ldur            x0, [fp, #-8]
    // 0x942d38: LoadField: r1 = r0->field_f
    //     0x942d38: ldur            w1, [x0, #0xf]
    // 0x942d3c: DecompressPointer r1
    //     0x942d3c: add             x1, x1, HEAP, lsl #32
    // 0x942d40: LoadField: r3 = r1->field_4b
    //     0x942d40: ldur            w3, [x1, #0x4b]
    // 0x942d44: DecompressPointer r3
    //     0x942d44: add             x3, x3, HEAP, lsl #32
    // 0x942d48: mov             x1, x3
    // 0x942d4c: r0 = getConnectivityType()
    //     0x942d4c: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x942d50: ldur            x2, [fp, #-8]
    // 0x942d54: r1 = Function '<anonymous closure>':.
    //     0x942d54: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc20] AnonymousClosure: (0x942e84), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped (0x942718)
    //     0x942d58: ldr             x1, [x1, #0xc20]
    // 0x942d5c: stur            x0, [fp, #-8]
    // 0x942d60: r0 = AllocateClosure()
    //     0x942d60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x942d64: r16 = <Null?>
    //     0x942d64: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x942d68: ldur            lr, [fp, #-8]
    // 0x942d6c: stp             lr, x16, [SP, #8]
    // 0x942d70: str             x0, [SP]
    // 0x942d74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x942d74: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x942d78: r0 = then()
    //     0x942d78: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x942d7c: ldur            x1, [fp, #-0x18]
    // 0x942d80: mov             x2, x0
    // 0x942d84: r0 = add()
    //     0x942d84: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x942d88: ldur            x1, [fp, #-0x10]
    // 0x942d8c: ldur            x2, [fp, #-0x18]
    // 0x942d90: r0 = add()
    //     0x942d90: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x942d94: ldur            x0, [fp, #-0x10]
    // 0x942d98: LeaveFrame
    //     0x942d98: mov             SP, fp
    //     0x942d9c: ldp             fp, lr, [SP], #0x10
    // 0x942da0: ret
    //     0x942da0: ret             
    // 0x942da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942da4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942da8: b               #0x942c0c
  }
  _ openLoginAwait(/* No info */) async {
    // ** addr: 0x942dac, size: 0xd8
    // 0x942dac: EnterFrame
    //     0x942dac: stp             fp, lr, [SP, #-0x10]!
    //     0x942db0: mov             fp, SP
    // 0x942db4: AllocStack(0x10)
    //     0x942db4: sub             SP, SP, #0x10
    // 0x942db8: SetupParameters(_MainPageState this /* r1 => r1, fp-0x10 */)
    //     0x942db8: stur            NULL, [fp, #-8]
    //     0x942dbc: stur            x1, [fp, #-0x10]
    // 0x942dc0: CheckStackOverflow
    //     0x942dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942dc4: cmp             SP, x16
    //     0x942dc8: b.ls            #0x942e78
    // 0x942dcc: InitAsync() -> Future
    //     0x942dcc: mov             x0, NULL
    //     0x942dd0: bl              #0x6326e0  ; InitAsyncStub
    // 0x942dd4: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0x942dd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x942dd8: ldr             x0, [x0, #0x1ce0]
    //     0x942ddc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x942de0: cmp             w0, w16
    //     0x942de4: b.ne            #0x942df4
    //     0x942de8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0x942dec: ldr             x2, [x2, #0xb30]
    //     0x942df0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x942df4: mov             x2, x0
    // 0x942df8: LoadField: r0 = r2->field_b
    //     0x942df8: ldur            w0, [x2, #0xb]
    // 0x942dfc: r1 = LoadInt32Instr(r0)
    //     0x942dfc: sbfx            x1, x0, #1, #0x1f
    // 0x942e00: mov             x0, x1
    // 0x942e04: r1 = 1
    //     0x942e04: movz            x1, #0x1
    // 0x942e08: cmp             x1, x0
    // 0x942e0c: b.hs            #0x942e80
    // 0x942e10: LoadField: r0 = r2->field_f
    //     0x942e10: ldur            w0, [x2, #0xf]
    // 0x942e14: DecompressPointer r0
    //     0x942e14: add             x0, x0, HEAP, lsl #32
    // 0x942e18: LoadField: r3 = r0->field_13
    //     0x942e18: ldur            w3, [x0, #0x13]
    // 0x942e1c: DecompressPointer r3
    //     0x942e1c: add             x3, x3, HEAP, lsl #32
    // 0x942e20: mov             x0, x3
    // 0x942e24: stur            x3, [fp, #-0x10]
    // 0x942e28: r2 = Null
    //     0x942e28: mov             x2, NULL
    // 0x942e2c: r1 = Null
    //     0x942e2c: mov             x1, NULL
    // 0x942e30: r4 = 60
    //     0x942e30: movz            x4, #0x3c
    // 0x942e34: branchIfSmi(r0, 0x942e40)
    //     0x942e34: tbz             w0, #0, #0x942e40
    // 0x942e38: r4 = LoadClassIdInstr(r0)
    //     0x942e38: ldur            x4, [x0, #-1]
    //     0x942e3c: ubfx            x4, x4, #0xc, #0x14
    // 0x942e40: r17 = 4563
    //     0x942e40: movz            x17, #0x11d3
    // 0x942e44: cmp             x4, x17
    // 0x942e48: b.eq            #0x942e60
    // 0x942e4c: r8 = OrdersView
    //     0x942e4c: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0x942e50: ldr             x8, [x8, #0xb38]
    // 0x942e54: r3 = Null
    //     0x942e54: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc48] Null
    //     0x942e58: ldr             x3, [x3, #0xc48]
    // 0x942e5c: r0 = DefaultTypeTest()
    //     0x942e5c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x942e60: ldur            x1, [fp, #-0x10]
    // 0x942e64: r0 = controller()
    //     0x942e64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x942e68: mov             x1, x0
    // 0x942e6c: r0 = openLoginAwait()
    //     0x942e6c: bl              #0x8a55a0  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::openLoginAwait
    // 0x942e70: r0 = Null
    //     0x942e70: mov             x0, NULL
    // 0x942e74: r0 = ReturnAsyncNotFuture()
    //     0x942e74: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x942e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942e78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942e7c: b               #0x942dcc
    // 0x942e80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942e80: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x942e84, size: 0x178
    // 0x942e84: EnterFrame
    //     0x942e84: stp             fp, lr, [SP, #-0x10]!
    //     0x942e88: mov             fp, SP
    // 0x942e8c: AllocStack(0x10)
    //     0x942e8c: sub             SP, SP, #0x10
    // 0x942e90: SetupParameters()
    //     0x942e90: ldr             x0, [fp, #0x18]
    //     0x942e94: ldur            w1, [x0, #0x17]
    //     0x942e98: add             x1, x1, HEAP, lsl #32
    // 0x942e9c: CheckStackOverflow
    //     0x942e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942ea0: cmp             SP, x16
    //     0x942ea4: b.ls            #0x942fec
    // 0x942ea8: LoadField: r0 = r1->field_f
    //     0x942ea8: ldur            w0, [x1, #0xf]
    // 0x942eac: DecompressPointer r0
    //     0x942eac: add             x0, x0, HEAP, lsl #32
    // 0x942eb0: ldr             x1, [fp, #0x10]
    // 0x942eb4: StoreField: r0->field_4f = r1
    //     0x942eb4: stur            w1, [x0, #0x4f]
    // 0x942eb8: tbnz            w1, #4, #0x942fdc
    // 0x942ebc: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0x942ebc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x942ec0: ldr             x0, [x0, #0x1ce0]
    //     0x942ec4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x942ec8: cmp             w0, w16
    //     0x942ecc: b.ne            #0x942edc
    //     0x942ed0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0x942ed4: ldr             x2, [x2, #0xb30]
    //     0x942ed8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x942edc: mov             x3, x0
    // 0x942ee0: stur            x3, [fp, #-0x10]
    // 0x942ee4: LoadField: r0 = r3->field_b
    //     0x942ee4: ldur            w0, [x3, #0xb]
    // 0x942ee8: r1 = LoadInt32Instr(r0)
    //     0x942ee8: sbfx            x1, x0, #1, #0x1f
    // 0x942eec: mov             x0, x1
    // 0x942ef0: r1 = 1
    //     0x942ef0: movz            x1, #0x1
    // 0x942ef4: cmp             x1, x0
    // 0x942ef8: b.hs            #0x942ff4
    // 0x942efc: LoadField: r0 = r3->field_f
    //     0x942efc: ldur            w0, [x3, #0xf]
    // 0x942f00: DecompressPointer r0
    //     0x942f00: add             x0, x0, HEAP, lsl #32
    // 0x942f04: LoadField: r4 = r0->field_13
    //     0x942f04: ldur            w4, [x0, #0x13]
    // 0x942f08: DecompressPointer r4
    //     0x942f08: add             x4, x4, HEAP, lsl #32
    // 0x942f0c: mov             x0, x4
    // 0x942f10: stur            x4, [fp, #-8]
    // 0x942f14: r2 = Null
    //     0x942f14: mov             x2, NULL
    // 0x942f18: r1 = Null
    //     0x942f18: mov             x1, NULL
    // 0x942f1c: r4 = 60
    //     0x942f1c: movz            x4, #0x3c
    // 0x942f20: branchIfSmi(r0, 0x942f2c)
    //     0x942f20: tbz             w0, #0, #0x942f2c
    // 0x942f24: r4 = LoadClassIdInstr(r0)
    //     0x942f24: ldur            x4, [x0, #-1]
    //     0x942f28: ubfx            x4, x4, #0xc, #0x14
    // 0x942f2c: r17 = 4563
    //     0x942f2c: movz            x17, #0x11d3
    // 0x942f30: cmp             x4, x17
    // 0x942f34: b.eq            #0x942f4c
    // 0x942f38: r8 = OrdersView
    //     0x942f38: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0x942f3c: ldr             x8, [x8, #0xb38]
    // 0x942f40: r3 = Null
    //     0x942f40: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc28] Null
    //     0x942f44: ldr             x3, [x3, #0xc28]
    // 0x942f48: r0 = DefaultTypeTest()
    //     0x942f48: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x942f4c: ldur            x1, [fp, #-8]
    // 0x942f50: r0 = controller()
    //     0x942f50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x942f54: LoadField: r1 = r0->field_4f
    //     0x942f54: ldur            w1, [x0, #0x4f]
    // 0x942f58: DecompressPointer r1
    //     0x942f58: add             x1, x1, HEAP, lsl #32
    // 0x942f5c: r0 = initRefresh()
    //     0x942f5c: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x942f60: ldur            x2, [fp, #-0x10]
    // 0x942f64: LoadField: r0 = r2->field_b
    //     0x942f64: ldur            w0, [x2, #0xb]
    // 0x942f68: r1 = LoadInt32Instr(r0)
    //     0x942f68: sbfx            x1, x0, #1, #0x1f
    // 0x942f6c: mov             x0, x1
    // 0x942f70: r1 = 1
    //     0x942f70: movz            x1, #0x1
    // 0x942f74: cmp             x1, x0
    // 0x942f78: b.hs            #0x942ff8
    // 0x942f7c: LoadField: r0 = r2->field_f
    //     0x942f7c: ldur            w0, [x2, #0xf]
    // 0x942f80: DecompressPointer r0
    //     0x942f80: add             x0, x0, HEAP, lsl #32
    // 0x942f84: LoadField: r3 = r0->field_13
    //     0x942f84: ldur            w3, [x0, #0x13]
    // 0x942f88: DecompressPointer r3
    //     0x942f88: add             x3, x3, HEAP, lsl #32
    // 0x942f8c: mov             x0, x3
    // 0x942f90: stur            x3, [fp, #-8]
    // 0x942f94: r2 = Null
    //     0x942f94: mov             x2, NULL
    // 0x942f98: r1 = Null
    //     0x942f98: mov             x1, NULL
    // 0x942f9c: r4 = 60
    //     0x942f9c: movz            x4, #0x3c
    // 0x942fa0: branchIfSmi(r0, 0x942fac)
    //     0x942fa0: tbz             w0, #0, #0x942fac
    // 0x942fa4: r4 = LoadClassIdInstr(r0)
    //     0x942fa4: ldur            x4, [x0, #-1]
    //     0x942fa8: ubfx            x4, x4, #0xc, #0x14
    // 0x942fac: r17 = 4563
    //     0x942fac: movz            x17, #0x11d3
    // 0x942fb0: cmp             x4, x17
    // 0x942fb4: b.eq            #0x942fcc
    // 0x942fb8: r8 = OrdersView
    //     0x942fb8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0x942fbc: ldr             x8, [x8, #0xb38]
    // 0x942fc0: r3 = Null
    //     0x942fc0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc38] Null
    //     0x942fc4: ldr             x3, [x3, #0xc38]
    // 0x942fc8: r0 = DefaultTypeTest()
    //     0x942fc8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x942fcc: ldur            x1, [fp, #-8]
    // 0x942fd0: r0 = controller()
    //     0x942fd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x942fd4: mov             x1, x0
    // 0x942fd8: r0 = getOrders()
    //     0x942fd8: bl              #0x8a5968  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getOrders
    // 0x942fdc: r0 = Null
    //     0x942fdc: mov             x0, NULL
    // 0x942fe0: LeaveFrame
    //     0x942fe0: mov             SP, fp
    //     0x942fe4: ldp             fp, lr, [SP], #0x10
    // 0x942fe8: ret
    //     0x942fe8: ret             
    // 0x942fec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942fec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942ff0: b               #0x942ea8
    // 0x942ff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942ff4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x942ff8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942ff8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<Widget> _pages() {
    // ** addr: 0x942ffc, size: 0x74
    // 0x942ffc: EnterFrame
    //     0x942ffc: stp             fp, lr, [SP, #-0x10]!
    //     0x943000: mov             fp, SP
    // 0x943004: AllocStack(0x8)
    //     0x943004: sub             SP, SP, #8
    // 0x943008: r0 = 8
    //     0x943008: movz            x0, #0x8
    // 0x94300c: mov             x2, x0
    // 0x943010: r1 = Null
    //     0x943010: mov             x1, NULL
    // 0x943014: r0 = AllocateArray()
    //     0x943014: bl              #0x16f7198  ; AllocateArrayStub
    // 0x943018: stur            x0, [fp, #-8]
    // 0x94301c: r16 = Instance_HomePage
    //     0x94301c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb18] Obj!HomePage@d67341
    //     0x943020: ldr             x16, [x16, #0xb18]
    // 0x943024: StoreField: r0->field_f = r16
    //     0x943024: stur            w16, [x0, #0xf]
    // 0x943028: r16 = Instance_OrdersView
    //     0x943028: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae0] Obj!OrdersView@d67301
    //     0x94302c: ldr             x16, [x16, #0xae0]
    // 0x943030: StoreField: r0->field_13 = r16
    //     0x943030: stur            w16, [x0, #0x13]
    // 0x943034: r16 = Instance_ProfileView
    //     0x943034: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] Obj!ProfileView@d671e1
    //     0x943038: ldr             x16, [x16, #0xae8]
    // 0x94303c: ArrayStore: r0[0] = r16  ; List_4
    //     0x94303c: stur            w16, [x0, #0x17]
    // 0x943040: r16 = Instance_BrowseView
    //     0x943040: add             x16, PP, #0xd, lsl #12  ; [pp+0xdaa8] Obj!BrowseView@d674e1
    //     0x943044: ldr             x16, [x16, #0xaa8]
    // 0x943048: StoreField: r0->field_1b = r16
    //     0x943048: stur            w16, [x0, #0x1b]
    // 0x94304c: r1 = <Widget>
    //     0x94304c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x943050: r0 = AllocateGrowableArray()
    //     0x943050: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x943054: ldur            x1, [fp, #-8]
    // 0x943058: StoreField: r0->field_f = r1
    //     0x943058: stur            w1, [x0, #0xf]
    // 0x94305c: r1 = 8
    //     0x94305c: movz            x1, #0x8
    // 0x943060: StoreField: r0->field_b = r1
    //     0x943060: stur            w1, [x0, #0xb]
    // 0x943064: LeaveFrame
    //     0x943064: mov             SP, fp
    //     0x943068: ldp             fp, lr, [SP], #0x10
    // 0x94306c: ret
    //     0x94306c: ret             
  }
  [closure] void <anonymous closure>(dynamic, Uri) {
    // ** addr: 0x943070, size: 0xc4
    // 0x943070: EnterFrame
    //     0x943070: stp             fp, lr, [SP, #-0x10]!
    //     0x943074: mov             fp, SP
    // 0x943078: AllocStack(0x10)
    //     0x943078: sub             SP, SP, #0x10
    // 0x94307c: SetupParameters()
    //     0x94307c: ldr             x0, [fp, #0x18]
    //     0x943080: ldur            w1, [x0, #0x17]
    //     0x943084: add             x1, x1, HEAP, lsl #32
    //     0x943088: stur            x1, [fp, #-8]
    // 0x94308c: CheckStackOverflow
    //     0x94308c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943090: cmp             SP, x16
    //     0x943094: b.ls            #0x94312c
    // 0x943098: r0 = InitLateStaticField(0x678) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x943098: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94309c: ldr             x0, [x0, #0xcf0]
    //     0x9430a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9430a4: cmp             w0, w16
    //     0x9430a8: b.ne            #0x9430b4
    //     0x9430ac: ldr             x2, [PP, #0x880]  ; [pp+0x880] Field <::.debugPrint>: static late (offset: 0x678)
    //     0x9430b0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x9430b4: r1 = Null
    //     0x9430b4: mov             x1, NULL
    // 0x9430b8: r2 = 4
    //     0x9430b8: movz            x2, #0x4
    // 0x9430bc: r0 = AllocateArray()
    //     0x9430bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9430c0: r16 = "onAppLink: "
    //     0x9430c0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d818] "onAppLink: "
    //     0x9430c4: ldr             x16, [x16, #0x818]
    // 0x9430c8: StoreField: r0->field_f = r16
    //     0x9430c8: stur            w16, [x0, #0xf]
    // 0x9430cc: ldr             x1, [fp, #0x10]
    // 0x9430d0: StoreField: r0->field_13 = r1
    //     0x9430d0: stur            w1, [x0, #0x13]
    // 0x9430d4: str             x0, [SP]
    // 0x9430d8: r0 = _interpolate()
    //     0x9430d8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9430dc: str             NULL, [SP]
    // 0x9430e0: mov             x1, x0
    // 0x9430e4: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x9430e4: ldr             x4, [PP, #0x890]  ; [pp+0x890] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x9430e8: r0 = debugPrintThrottled()
    //     0x9430e8: bl              #0x6368ec  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x9430ec: ldur            x0, [fp, #-8]
    // 0x9430f0: LoadField: r1 = r0->field_f
    //     0x9430f0: ldur            w1, [x0, #0xf]
    // 0x9430f4: DecompressPointer r1
    //     0x9430f4: add             x1, x1, HEAP, lsl #32
    // 0x9430f8: ldr             x0, [fp, #0x10]
    // 0x9430fc: StoreField: r1->field_23 = r0
    //     0x9430fc: stur            w0, [x1, #0x23]
    //     0x943100: ldurb           w16, [x1, #-1]
    //     0x943104: ldurb           w17, [x0, #-1]
    //     0x943108: and             x16, x17, x16, lsr #2
    //     0x94310c: tst             x16, HEAP, lsr #32
    //     0x943110: b.eq            #0x943118
    //     0x943114: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x943118: r0 = _handleInitialUri()
    //     0x943118: bl              #0x943134  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_handleInitialUri
    // 0x94311c: r0 = Null
    //     0x94311c: mov             x0, NULL
    // 0x943120: LeaveFrame
    //     0x943120: mov             SP, fp
    //     0x943124: ldp             fp, lr, [SP], #0x10
    // 0x943128: ret
    //     0x943128: ret             
    // 0x94312c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94312c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943130: b               #0x943098
  }
  _ _handleInitialUri(/* No info */) async {
    // ** addr: 0x943134, size: 0x298
    // 0x943134: EnterFrame
    //     0x943134: stp             fp, lr, [SP, #-0x10]!
    //     0x943138: mov             fp, SP
    // 0x94313c: AllocStack(0xb8)
    //     0x94313c: sub             SP, SP, #0xb8
    // 0x943140: SetupParameters(_MainPageState this /* r1 => r1, fp-0x70 */)
    //     0x943140: stur            NULL, [fp, #-8]
    //     0x943144: stur            x1, [fp, #-0x70]
    // 0x943148: CheckStackOverflow
    //     0x943148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94314c: cmp             SP, x16
    //     0x943150: b.ls            #0x9433c4
    // 0x943154: r1 = 2
    //     0x943154: movz            x1, #0x2
    // 0x943158: r0 = AllocateContext()
    //     0x943158: bl              #0x16f6108  ; AllocateContextStub
    // 0x94315c: mov             x2, x0
    // 0x943160: ldur            x1, [fp, #-0x70]
    // 0x943164: stur            x2, [fp, #-0x78]
    // 0x943168: StoreField: r2->field_f = r1
    //     0x943168: stur            w1, [x2, #0xf]
    // 0x94316c: InitAsync() -> Future<void?>
    //     0x94316c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x943170: bl              #0x6326e0  ; InitAsyncStub
    // 0x943174: ldur            x0, [fp, #-0x70]
    // 0x943178: LoadField: r2 = r0->field_23
    //     0x943178: ldur            w2, [x0, #0x23]
    // 0x94317c: DecompressPointer r2
    //     0x94317c: add             x2, x2, HEAP, lsl #32
    // 0x943180: cmp             w2, NULL
    // 0x943184: b.ne            #0x943194
    // 0x943188: mov             x1, x0
    // 0x94318c: r0 = setDefaultUtmParameters()
    //     0x94318c: bl              #0x916c2c  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::setDefaultUtmParameters
    // 0x943190: b               #0x94319c
    // 0x943194: ldur            x1, [fp, #-0x70]
    // 0x943198: r0 = setUtmParameters()
    //     0x943198: bl              #0x91690c  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::setUtmParameters
    // 0x94319c: ldur            x2, [fp, #-0x70]
    // 0x9431a0: LoadField: r0 = r2->field_f
    //     0x9431a0: ldur            w0, [x2, #0xf]
    // 0x9431a4: DecompressPointer r0
    //     0x9431a4: add             x0, x0, HEAP, lsl #32
    // 0x9431a8: cmp             w0, NULL
    // 0x9431ac: b.ne            #0x9431b8
    // 0x9431b0: r0 = Null
    //     0x9431b0: mov             x0, NULL
    // 0x9431b4: r0 = ReturnAsyncNotFuture()
    //     0x9431b4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x9431b8: LoadField: r3 = r2->field_23
    //     0x9431b8: ldur            w3, [x2, #0x23]
    // 0x9431bc: DecompressPointer r3
    //     0x9431bc: add             x3, x3, HEAP, lsl #32
    // 0x9431c0: stur            x3, [fp, #-0x80]
    // 0x9431c4: cmp             w3, NULL
    // 0x9431c8: b.eq            #0x9433ac
    // 0x9431cc: r0 = LoadClassIdInstr(r3)
    //     0x9431cc: ldur            x0, [x3, #-1]
    //     0x9431d0: ubfx            x0, x0, #0xc, #0x14
    // 0x9431d4: mov             x1, x3
    // 0x9431d8: r0 = GDT[cid_x0 + -0xf32]()
    //     0x9431d8: sub             lr, x0, #0xf32
    //     0x9431dc: ldr             lr, [x21, lr, lsl #3]
    //     0x9431e0: blr             lr
    // 0x9431e4: tbnz            w0, #4, #0x9432f4
    // 0x9431e8: ldur            x2, [fp, #-0x70]
    // 0x9431ec: LoadField: r3 = r2->field_23
    //     0x9431ec: ldur            w3, [x2, #0x23]
    // 0x9431f0: DecompressPointer r3
    //     0x9431f0: add             x3, x3, HEAP, lsl #32
    // 0x9431f4: stur            x3, [fp, #-0x80]
    // 0x9431f8: cmp             w3, NULL
    // 0x9431fc: b.ne            #0x94320c
    // 0x943200: mov             x3, x2
    // 0x943204: r5 = Null
    //     0x943204: mov             x5, NULL
    // 0x943208: b               #0x94322c
    // 0x94320c: r0 = LoadClassIdInstr(r3)
    //     0x94320c: ldur            x0, [x3, #-1]
    //     0x943210: ubfx            x0, x0, #0xc, #0x14
    // 0x943214: mov             x1, x3
    // 0x943218: r0 = GDT[cid_x0 + -0xfd1]()
    //     0x943218: sub             lr, x0, #0xfd1
    //     0x94321c: ldr             lr, [x21, lr, lsl #3]
    //     0x943220: blr             lr
    // 0x943224: mov             x5, x0
    // 0x943228: ldur            x3, [fp, #-0x70]
    // 0x94322c: ldur            x4, [fp, #-0x78]
    // 0x943230: mov             x0, x5
    // 0x943234: stur            x5, [fp, #-0x88]
    // 0x943238: StoreField: r4->field_13 = r0
    //     0x943238: stur            w0, [x4, #0x13]
    //     0x94323c: ldurb           w16, [x4, #-1]
    //     0x943240: ldurb           w17, [x0, #-1]
    //     0x943244: and             x16, x17, x16, lsr #2
    //     0x943248: tst             x16, HEAP, lsr #32
    //     0x94324c: b.eq            #0x943254
    //     0x943250: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x943254: LoadField: r0 = r3->field_3f
    //     0x943254: ldur            w0, [x3, #0x3f]
    // 0x943258: DecompressPointer r0
    //     0x943258: add             x0, x0, HEAP, lsl #32
    // 0x94325c: LoadField: r6 = r0->field_4b
    //     0x94325c: ldur            w6, [x0, #0x4b]
    // 0x943260: DecompressPointer r6
    //     0x943260: add             x6, x6, HEAP, lsl #32
    // 0x943264: stur            x6, [fp, #-0x80]
    // 0x943268: r16 = ""
    //     0x943268: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94326c: str             x16, [SP]
    // 0x943270: mov             x1, x6
    // 0x943274: r2 = "token"
    //     0x943274: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x943278: ldr             x2, [x2, #0x958]
    // 0x94327c: r4 = const [0, 0x3, 0x1, 0x2, defaultValue, 0x2, null]
    //     0x94327c: add             x4, PP, #0x11, lsl #12  ; [pp+0x11f48] List(7) [0, 0x3, 0x1, 0x2, "defaultValue", 0x2, Null]
    //     0x943280: ldr             x4, [x4, #0xf48]
    // 0x943284: r0 = getString()
    //     0x943284: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x943288: ldur            x2, [fp, #-0x78]
    // 0x94328c: r1 = Function '<anonymous closure>':.
    //     0x94328c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc80] AnonymousClosure: (0x943b24), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_handleInitialUri (0x943134)
    //     0x943290: ldr             x1, [x1, #0xc80]
    // 0x943294: stur            x0, [fp, #-0x80]
    // 0x943298: r0 = AllocateClosure()
    //     0x943298: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94329c: mov             x4, x0
    // 0x9432a0: ldur            x3, [fp, #-0x80]
    // 0x9432a4: stur            x4, [fp, #-0x98]
    // 0x9432a8: LoadField: r5 = r3->field_7
    //     0x9432a8: ldur            w5, [x3, #7]
    // 0x9432ac: DecompressPointer r5
    //     0x9432ac: add             x5, x5, HEAP, lsl #32
    // 0x9432b0: mov             x0, x4
    // 0x9432b4: mov             x2, x5
    // 0x9432b8: stur            x5, [fp, #-0x90]
    // 0x9432bc: r1 = Null
    //     0x9432bc: mov             x1, NULL
    // 0x9432c0: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0x9432c0: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0x9432c4: ldr             x8, [x8, #0xce8]
    // 0x9432c8: LoadField: r9 = r8->field_7
    //     0x9432c8: ldur            x9, [x8, #7]
    // 0x9432cc: r3 = Null
    //     0x9432cc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc88] Null
    //     0x9432d0: ldr             x3, [x3, #0xc88]
    // 0x9432d4: blr             x9
    // 0x9432d8: ldur            x16, [fp, #-0x80]
    // 0x9432dc: stp             x16, NULL, [SP, #0x10]
    // 0x9432e0: ldur            x16, [fp, #-0x98]
    // 0x9432e4: stp             NULL, x16, [SP]
    // 0x9432e8: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x9432e8: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x9432ec: r0 = then()
    //     0x9432ec: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x9432f0: b               #0x9433ac
    // 0x9432f4: ldur            x1, [fp, #-0x70]
    // 0x9432f8: r0 = openUrlInApp()
    //     0x9432f8: bl              #0x9433cc  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x9432fc: b               #0x9433ac
    // 0x943300: sub             SP, fp, #0xb8
    // 0x943304: mov             x4, x0
    // 0x943308: mov             x3, x1
    // 0x94330c: stur            x0, [fp, #-0x78]
    // 0x943310: stur            x1, [fp, #-0x80]
    // 0x943314: r0 = 60
    //     0x943314: movz            x0, #0x3c
    // 0x943318: branchIfSmi(r4, 0x943324)
    //     0x943318: tbz             w4, #0, #0x943324
    // 0x94331c: r0 = LoadClassIdInstr(r4)
    //     0x94331c: ldur            x0, [x4, #-1]
    //     0x943320: ubfx            x0, x0, #0xc, #0x14
    // 0x943324: cmp             x0, #0x6ee
    // 0x943328: b.ne            #0x943334
    // 0x94332c: r0 = Null
    //     0x94332c: mov             x0, NULL
    // 0x943330: r0 = ReturnAsyncNotFuture()
    //     0x943330: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x943334: mov             x0, x4
    // 0x943338: r2 = Null
    //     0x943338: mov             x2, NULL
    // 0x94333c: r1 = Null
    //     0x94333c: mov             x1, NULL
    // 0x943340: cmp             w0, NULL
    // 0x943344: b.eq            #0x943380
    // 0x943348: branchIfSmi(r0, 0x943380)
    //     0x943348: tbz             w0, #0, #0x943380
    // 0x94334c: r3 = LoadClassIdInstr(r0)
    //     0x94334c: ldur            x3, [x0, #-1]
    //     0x943350: ubfx            x3, x3, #0xc, #0x14
    // 0x943354: sub             x3, x3, #0xcf
    // 0x943358: cmp             x3, #1
    // 0x94335c: b.ls            #0x943388
    // 0x943360: cmp             x3, #0x217
    // 0x943364: b.eq            #0x943388
    // 0x943368: sub             x3, x3, #0x36f
    // 0x94336c: cmp             x3, #1
    // 0x943370: b.ls            #0x943388
    // 0x943374: r17 = 5658
    //     0x943374: movz            x17, #0x161a
    // 0x943378: cmp             x3, x17
    // 0x94337c: b.eq            #0x943388
    // 0x943380: r0 = false
    //     0x943380: add             x0, NULL, #0x30  ; false
    // 0x943384: b               #0x94338c
    // 0x943388: r0 = true
    //     0x943388: add             x0, NULL, #0x20  ; true
    // 0x94338c: tbnz            w0, #4, #0x9433b4
    // 0x943390: ldur            x0, [fp, #-0x70]
    // 0x943394: LoadField: r1 = r0->field_f
    //     0x943394: ldur            w1, [x0, #0xf]
    // 0x943398: DecompressPointer r1
    //     0x943398: add             x1, x1, HEAP, lsl #32
    // 0x94339c: cmp             w1, NULL
    // 0x9433a0: b.ne            #0x9433ac
    // 0x9433a4: r0 = Null
    //     0x9433a4: mov             x0, NULL
    // 0x9433a8: r0 = ReturnAsyncNotFuture()
    //     0x9433a8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x9433ac: r0 = Null
    //     0x9433ac: mov             x0, NULL
    // 0x9433b0: r0 = ReturnAsyncNotFuture()
    //     0x9433b0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x9433b4: ldur            x0, [fp, #-0x78]
    // 0x9433b8: ldur            x1, [fp, #-0x80]
    // 0x9433bc: r0 = ReThrow()
    //     0x9433bc: bl              #0x16f53f4  ; ReThrowStub
    // 0x9433c0: brk             #0
    // 0x9433c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9433c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9433c8: b               #0x943154
  }
  _ openUrlInApp(/* No info */) {
    // ** addr: 0x9433cc, size: 0x758
    // 0x9433cc: EnterFrame
    //     0x9433cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9433d0: mov             fp, SP
    // 0x9433d4: AllocStack(0x40)
    //     0x9433d4: sub             SP, SP, #0x40
    // 0x9433d8: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0x9433d8: stur            x1, [fp, #-8]
    // 0x9433dc: CheckStackOverflow
    //     0x9433dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9433e0: cmp             SP, x16
    //     0x9433e4: b.ls            #0x943b0c
    // 0x9433e8: r1 = 1
    //     0x9433e8: movz            x1, #0x1
    // 0x9433ec: r0 = AllocateContext()
    //     0x9433ec: bl              #0x16f6108  ; AllocateContextStub
    // 0x9433f0: mov             x3, x0
    // 0x9433f4: ldur            x2, [fp, #-8]
    // 0x9433f8: stur            x3, [fp, #-0x10]
    // 0x9433fc: StoreField: r3->field_f = r2
    //     0x9433fc: stur            w2, [x3, #0xf]
    // 0x943400: LoadField: r1 = r2->field_23
    //     0x943400: ldur            w1, [x2, #0x23]
    // 0x943404: DecompressPointer r1
    //     0x943404: add             x1, x1, HEAP, lsl #32
    // 0x943408: cmp             w1, NULL
    // 0x94340c: b.ne            #0x943418
    // 0x943410: r0 = Null
    //     0x943410: mov             x0, NULL
    // 0x943414: b               #0x943458
    // 0x943418: r0 = LoadClassIdInstr(r1)
    //     0x943418: ldur            x0, [x1, #-1]
    //     0x94341c: ubfx            x0, x0, #0xc, #0x14
    // 0x943420: r0 = GDT[cid_x0 + -0x1000]()
    //     0x943420: sub             lr, x0, #1, lsl #12
    //     0x943424: ldr             lr, [x21, lr, lsl #3]
    //     0x943428: blr             lr
    // 0x94342c: r1 = LoadClassIdInstr(r0)
    //     0x94342c: ldur            x1, [x0, #-1]
    //     0x943430: ubfx            x1, x1, #0xc, #0x14
    // 0x943434: mov             x16, x0
    // 0x943438: mov             x0, x1
    // 0x94343c: mov             x1, x16
    // 0x943440: r2 = "/catalogue/"
    //     0x943440: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da58] "/catalogue/"
    //     0x943444: ldr             x2, [x2, #0xa58]
    // 0x943448: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x943448: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x94344c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x94344c: sub             lr, x0, #0xffe
    //     0x943450: ldr             lr, [x21, lr, lsl #3]
    //     0x943454: blr             lr
    // 0x943458: cmp             w0, NULL
    // 0x94345c: b.ne            #0x943468
    // 0x943460: ldur            x2, [fp, #-8]
    // 0x943464: b               #0x943680
    // 0x943468: tbnz            w0, #4, #0x94367c
    // 0x94346c: ldur            x2, [fp, #-8]
    // 0x943470: LoadField: r1 = r2->field_23
    //     0x943470: ldur            w1, [x2, #0x23]
    // 0x943474: DecompressPointer r1
    //     0x943474: add             x1, x1, HEAP, lsl #32
    // 0x943478: cmp             w1, NULL
    // 0x94347c: b.ne            #0x943488
    // 0x943480: r0 = Null
    //     0x943480: mov             x0, NULL
    // 0x943484: b               #0x94349c
    // 0x943488: r0 = LoadClassIdInstr(r1)
    //     0x943488: ldur            x0, [x1, #-1]
    //     0x94348c: ubfx            x0, x0, #0xc, #0x14
    // 0x943490: r0 = GDT[cid_x0 + -0xea5]()
    //     0x943490: sub             lr, x0, #0xea5
    //     0x943494: ldr             lr, [x21, lr, lsl #3]
    //     0x943498: blr             lr
    // 0x94349c: stur            x0, [fp, #-0x18]
    // 0x9434a0: cmp             w0, NULL
    // 0x9434a4: b.ne            #0x9434b0
    // 0x9434a8: r2 = Null
    //     0x9434a8: mov             x2, NULL
    // 0x9434ac: b               #0x9434e0
    // 0x9434b0: mov             x1, x0
    // 0x9434b4: r2 = "catalogue"
    //     0x9434b4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da60] "catalogue"
    //     0x9434b8: ldr             x2, [x2, #0xa60]
    // 0x9434bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9434bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9434c0: r0 = indexOf()
    //     0x9434c0: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x9434c4: mov             x2, x0
    // 0x9434c8: r0 = BoxInt64Instr(r2)
    //     0x9434c8: sbfiz           x0, x2, #1, #0x1f
    //     0x9434cc: cmp             x2, x0, asr #1
    //     0x9434d0: b.eq            #0x9434dc
    //     0x9434d4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9434d8: stur            x2, [x0, #7]
    // 0x9434dc: mov             x2, x0
    // 0x9434e0: cmp             w2, NULL
    // 0x9434e4: b.eq            #0x9435bc
    // 0x9434e8: ldur            x3, [fp, #-0x18]
    // 0x9434ec: cmp             w3, NULL
    // 0x9434f0: b.ne            #0x9434fc
    // 0x9434f4: r0 = Null
    //     0x9434f4: mov             x0, NULL
    // 0x9434f8: b               #0x943530
    // 0x9434fc: r0 = LoadInt32Instr(r2)
    //     0x9434fc: sbfx            x0, x2, #1, #0x1f
    //     0x943500: tbz             w2, #0, #0x943508
    //     0x943504: ldur            x0, [x2, #7]
    // 0x943508: add             x4, x0, #2
    // 0x94350c: LoadField: r0 = r3->field_b
    //     0x94350c: ldur            w0, [x3, #0xb]
    // 0x943510: r1 = LoadInt32Instr(r0)
    //     0x943510: sbfx            x1, x0, #1, #0x1f
    // 0x943514: mov             x0, x1
    // 0x943518: mov             x1, x4
    // 0x94351c: cmp             x1, x0
    // 0x943520: b.hs            #0x943b14
    // 0x943524: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0x943524: add             x16, x3, x4, lsl #2
    //     0x943528: ldur            w0, [x16, #0xf]
    // 0x94352c: DecompressPointer r0
    //     0x94352c: add             x0, x0, HEAP, lsl #32
    // 0x943530: ldur            x4, [fp, #-8]
    // 0x943534: StoreField: r4->field_37 = r0
    //     0x943534: stur            w0, [x4, #0x37]
    //     0x943538: tbz             w0, #0, #0x943554
    //     0x94353c: ldurb           w16, [x4, #-1]
    //     0x943540: ldurb           w17, [x0, #-1]
    //     0x943544: and             x16, x17, x16, lsr #2
    //     0x943548: tst             x16, HEAP, lsr #32
    //     0x94354c: b.eq            #0x943554
    //     0x943550: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x943554: cmp             w3, NULL
    // 0x943558: b.ne            #0x943564
    // 0x94355c: r0 = Null
    //     0x94355c: mov             x0, NULL
    // 0x943560: b               #0x943598
    // 0x943564: r0 = LoadInt32Instr(r2)
    //     0x943564: sbfx            x0, x2, #1, #0x1f
    //     0x943568: tbz             w2, #0, #0x943570
    //     0x94356c: ldur            x0, [x2, #7]
    // 0x943570: add             x2, x0, #1
    // 0x943574: LoadField: r0 = r3->field_b
    //     0x943574: ldur            w0, [x3, #0xb]
    // 0x943578: r1 = LoadInt32Instr(r0)
    //     0x943578: sbfx            x1, x0, #1, #0x1f
    // 0x94357c: mov             x0, x1
    // 0x943580: mov             x1, x2
    // 0x943584: cmp             x1, x0
    // 0x943588: b.hs            #0x943b18
    // 0x94358c: ArrayLoad: r0 = r3[r2]  ; Unknown_4
    //     0x94358c: add             x16, x3, x2, lsl #2
    //     0x943590: ldur            w0, [x16, #0xf]
    // 0x943594: DecompressPointer r0
    //     0x943594: add             x0, x0, HEAP, lsl #32
    // 0x943598: StoreField: r4->field_33 = r0
    //     0x943598: stur            w0, [x4, #0x33]
    //     0x94359c: tbz             w0, #0, #0x9435b8
    //     0x9435a0: ldurb           w16, [x4, #-1]
    //     0x9435a4: ldurb           w17, [x0, #-1]
    //     0x9435a8: and             x16, x17, x16, lsr #2
    //     0x9435ac: tst             x16, HEAP, lsr #32
    //     0x9435b0: b.eq            #0x9435b8
    //     0x9435b4: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x9435b8: b               #0x9435c0
    // 0x9435bc: ldur            x4, [fp, #-8]
    // 0x9435c0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x9435c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9435c4: ldr             x0, [x0, #0x1c80]
    //     0x9435c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9435cc: cmp             w0, w16
    //     0x9435d0: b.ne            #0x9435dc
    //     0x9435d4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x9435d8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9435dc: r1 = Null
    //     0x9435dc: mov             x1, NULL
    // 0x9435e0: r2 = 16
    //     0x9435e0: movz            x2, #0x10
    // 0x9435e4: r0 = AllocateArray()
    //     0x9435e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9435e8: r16 = "short_id"
    //     0x9435e8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x9435ec: ldr             x16, [x16, #0x488]
    // 0x9435f0: StoreField: r0->field_f = r16
    //     0x9435f0: stur            w16, [x0, #0xf]
    // 0x9435f4: ldur            x2, [fp, #-8]
    // 0x9435f8: LoadField: r1 = r2->field_33
    //     0x9435f8: ldur            w1, [x2, #0x33]
    // 0x9435fc: DecompressPointer r1
    //     0x9435fc: add             x1, x1, HEAP, lsl #32
    // 0x943600: StoreField: r0->field_13 = r1
    //     0x943600: stur            w1, [x0, #0x13]
    // 0x943604: r16 = "sku_id"
    //     0x943604: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x943608: ldr             x16, [x16, #0x498]
    // 0x94360c: ArrayStore: r0[0] = r16  ; List_4
    //     0x94360c: stur            w16, [x0, #0x17]
    // 0x943610: LoadField: r1 = r2->field_37
    //     0x943610: ldur            w1, [x2, #0x37]
    // 0x943614: DecompressPointer r1
    //     0x943614: add             x1, x1, HEAP, lsl #32
    // 0x943618: StoreField: r0->field_1b = r1
    //     0x943618: stur            w1, [x0, #0x1b]
    // 0x94361c: r16 = "previousScreenSource"
    //     0x94361c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x943620: ldr             x16, [x16, #0x448]
    // 0x943624: StoreField: r0->field_1f = r16
    //     0x943624: stur            w16, [x0, #0x1f]
    // 0x943628: r16 = "Launch page"
    //     0x943628: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2da68] "Launch page"
    //     0x94362c: ldr             x16, [x16, #0xa68]
    // 0x943630: StoreField: r0->field_23 = r16
    //     0x943630: stur            w16, [x0, #0x23]
    // 0x943634: r16 = "screenSource"
    //     0x943634: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x943638: ldr             x16, [x16, #0x450]
    // 0x94363c: StoreField: r0->field_27 = r16
    //     0x94363c: stur            w16, [x0, #0x27]
    // 0x943640: r16 = "App Launching"
    //     0x943640: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2da70] "App Launching"
    //     0x943644: ldr             x16, [x16, #0xa70]
    // 0x943648: StoreField: r0->field_2b = r16
    //     0x943648: stur            w16, [x0, #0x2b]
    // 0x94364c: r16 = <String, String?>
    //     0x94364c: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x943650: ldr             x16, [x16, #0x3c8]
    // 0x943654: stp             x0, x16, [SP]
    // 0x943658: r0 = Map._fromLiteral()
    //     0x943658: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x94365c: r16 = "/product-detail"
    //     0x94365c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x943660: ldr             x16, [x16, #0x4a8]
    // 0x943664: stp             x16, NULL, [SP, #8]
    // 0x943668: str             x0, [SP]
    // 0x94366c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x94366c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x943670: ldr             x4, [x4, #0x438]
    // 0x943674: r0 = GetNavigation.toNamed()
    //     0x943674: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x943678: b               #0x943afc
    // 0x94367c: ldur            x2, [fp, #-8]
    // 0x943680: LoadField: r1 = r2->field_23
    //     0x943680: ldur            w1, [x2, #0x23]
    // 0x943684: DecompressPointer r1
    //     0x943684: add             x1, x1, HEAP, lsl #32
    // 0x943688: cmp             w1, NULL
    // 0x94368c: b.ne            #0x943698
    // 0x943690: r0 = Null
    //     0x943690: mov             x0, NULL
    // 0x943694: b               #0x9436d8
    // 0x943698: r0 = LoadClassIdInstr(r1)
    //     0x943698: ldur            x0, [x1, #-1]
    //     0x94369c: ubfx            x0, x0, #0xc, #0x14
    // 0x9436a0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9436a0: sub             lr, x0, #1, lsl #12
    //     0x9436a4: ldr             lr, [x21, lr, lsl #3]
    //     0x9436a8: blr             lr
    // 0x9436ac: r1 = LoadClassIdInstr(r0)
    //     0x9436ac: ldur            x1, [x0, #-1]
    //     0x9436b0: ubfx            x1, x1, #0xc, #0x14
    // 0x9436b4: mov             x16, x0
    // 0x9436b8: mov             x0, x1
    // 0x9436bc: mov             x1, x16
    // 0x9436c0: r2 = "/collection/"
    //     0x9436c0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da78] "/collection/"
    //     0x9436c4: ldr             x2, [x2, #0xa78]
    // 0x9436c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9436c8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9436cc: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9436cc: sub             lr, x0, #0xffe
    //     0x9436d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9436d4: blr             lr
    // 0x9436d8: cmp             w0, NULL
    // 0x9436dc: b.ne            #0x9436e8
    // 0x9436e0: ldur            x2, [fp, #-8]
    // 0x9436e4: b               #0x9437e0
    // 0x9436e8: tbnz            w0, #4, #0x9437dc
    // 0x9436ec: ldur            x2, [fp, #-8]
    // 0x9436f0: LoadField: r1 = r2->field_23
    //     0x9436f0: ldur            w1, [x2, #0x23]
    // 0x9436f4: DecompressPointer r1
    //     0x9436f4: add             x1, x1, HEAP, lsl #32
    // 0x9436f8: cmp             w1, NULL
    // 0x9436fc: b.ne            #0x943708
    // 0x943700: r0 = Null
    //     0x943700: mov             x0, NULL
    // 0x943704: b               #0x94371c
    // 0x943708: r0 = LoadClassIdInstr(r1)
    //     0x943708: ldur            x0, [x1, #-1]
    //     0x94370c: ubfx            x0, x0, #0xc, #0x14
    // 0x943710: r0 = GDT[cid_x0 + -0x1000]()
    //     0x943710: sub             lr, x0, #1, lsl #12
    //     0x943714: ldr             lr, [x21, lr, lsl #3]
    //     0x943718: blr             lr
    // 0x94371c: stur            x0, [fp, #-0x18]
    // 0x943720: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x943720: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x943724: ldr             x0, [x0, #0x1c80]
    //     0x943728: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x94372c: cmp             w0, w16
    //     0x943730: b.ne            #0x94373c
    //     0x943734: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x943738: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x94373c: r1 = Null
    //     0x94373c: mov             x1, NULL
    // 0x943740: r2 = 12
    //     0x943740: movz            x2, #0xc
    // 0x943744: r0 = AllocateArray()
    //     0x943744: bl              #0x16f7198  ; AllocateArrayStub
    // 0x943748: stur            x0, [fp, #-0x20]
    // 0x94374c: r16 = "link"
    //     0x94374c: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x943750: StoreField: r0->field_f = r16
    //     0x943750: stur            w16, [x0, #0xf]
    // 0x943754: ldur            x16, [fp, #-0x18]
    // 0x943758: str             x16, [SP]
    // 0x94375c: r0 = _interpolateSingle()
    //     0x94375c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x943760: ldur            x1, [fp, #-0x20]
    // 0x943764: ArrayStore: r1[1] = r0  ; List_4
    //     0x943764: add             x25, x1, #0x13
    //     0x943768: str             w0, [x25]
    //     0x94376c: tbz             w0, #0, #0x943788
    //     0x943770: ldurb           w16, [x1, #-1]
    //     0x943774: ldurb           w17, [x0, #-1]
    //     0x943778: and             x16, x17, x16, lsr #2
    //     0x94377c: tst             x16, HEAP, lsr #32
    //     0x943780: b.eq            #0x943788
    //     0x943784: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x943788: ldur            x0, [fp, #-0x20]
    // 0x94378c: r16 = "previousScreenSource"
    //     0x94378c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x943790: ldr             x16, [x16, #0x448]
    // 0x943794: ArrayStore: r0[0] = r16  ; List_4
    //     0x943794: stur            w16, [x0, #0x17]
    // 0x943798: StoreField: r0->field_1b = rNULL
    //     0x943798: stur            NULL, [x0, #0x1b]
    // 0x94379c: r16 = "screenSource"
    //     0x94379c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x9437a0: ldr             x16, [x16, #0x450]
    // 0x9437a4: StoreField: r0->field_1f = r16
    //     0x9437a4: stur            w16, [x0, #0x1f]
    // 0x9437a8: StoreField: r0->field_23 = rNULL
    //     0x9437a8: stur            NULL, [x0, #0x23]
    // 0x9437ac: r16 = <String, String?>
    //     0x9437ac: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x9437b0: ldr             x16, [x16, #0x3c8]
    // 0x9437b4: stp             x0, x16, [SP]
    // 0x9437b8: r0 = Map._fromLiteral()
    //     0x9437b8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x9437bc: r16 = "/collection"
    //     0x9437bc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x9437c0: ldr             x16, [x16, #0x458]
    // 0x9437c4: stp             x16, NULL, [SP, #8]
    // 0x9437c8: str             x0, [SP]
    // 0x9437cc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x9437cc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x9437d0: ldr             x4, [x4, #0x438]
    // 0x9437d4: r0 = GetNavigation.toNamed()
    //     0x9437d4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x9437d8: b               #0x943afc
    // 0x9437dc: ldur            x2, [fp, #-8]
    // 0x9437e0: LoadField: r1 = r2->field_23
    //     0x9437e0: ldur            w1, [x2, #0x23]
    // 0x9437e4: DecompressPointer r1
    //     0x9437e4: add             x1, x1, HEAP, lsl #32
    // 0x9437e8: cmp             w1, NULL
    // 0x9437ec: b.ne            #0x9437f8
    // 0x9437f0: r0 = Null
    //     0x9437f0: mov             x0, NULL
    // 0x9437f4: b               #0x943838
    // 0x9437f8: r0 = LoadClassIdInstr(r1)
    //     0x9437f8: ldur            x0, [x1, #-1]
    //     0x9437fc: ubfx            x0, x0, #0xc, #0x14
    // 0x943800: r0 = GDT[cid_x0 + -0x1000]()
    //     0x943800: sub             lr, x0, #1, lsl #12
    //     0x943804: ldr             lr, [x21, lr, lsl #3]
    //     0x943808: blr             lr
    // 0x94380c: r1 = LoadClassIdInstr(r0)
    //     0x94380c: ldur            x1, [x0, #-1]
    //     0x943810: ubfx            x1, x1, #0xc, #0x14
    // 0x943814: mov             x16, x0
    // 0x943818: mov             x0, x1
    // 0x94381c: mov             x1, x16
    // 0x943820: r2 = "/bag"
    //     0x943820: add             x2, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x943824: ldr             x2, [x2, #0x468]
    // 0x943828: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x943828: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x94382c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x94382c: sub             lr, x0, #0xffe
    //     0x943830: ldr             lr, [x21, lr, lsl #3]
    //     0x943834: blr             lr
    // 0x943838: cmp             w0, NULL
    // 0x94383c: b.ne            #0x943848
    // 0x943840: ldur            x2, [fp, #-8]
    // 0x943844: b               #0x943a70
    // 0x943848: tbnz            w0, #4, #0x943a6c
    // 0x94384c: ldur            x2, [fp, #-8]
    // 0x943850: LoadField: r1 = r2->field_23
    //     0x943850: ldur            w1, [x2, #0x23]
    // 0x943854: DecompressPointer r1
    //     0x943854: add             x1, x1, HEAP, lsl #32
    // 0x943858: cmp             w1, NULL
    // 0x94385c: b.ne            #0x943868
    // 0x943860: r0 = Null
    //     0x943860: mov             x0, NULL
    // 0x943864: b               #0x9438a8
    // 0x943868: r0 = LoadClassIdInstr(r1)
    //     0x943868: ldur            x0, [x1, #-1]
    //     0x94386c: ubfx            x0, x0, #0xc, #0x14
    // 0x943870: r0 = GDT[cid_x0 + -0x1000]()
    //     0x943870: sub             lr, x0, #1, lsl #12
    //     0x943874: ldr             lr, [x21, lr, lsl #3]
    //     0x943878: blr             lr
    // 0x94387c: mov             x1, x0
    // 0x943880: r2 = "/"
    //     0x943880: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x943884: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x943884: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x943888: r0 = lastIndexOf()
    //     0x943888: bl              #0x6394bc  ; [dart:core] _StringBase::lastIndexOf
    // 0x94388c: mov             x2, x0
    // 0x943890: r0 = BoxInt64Instr(r2)
    //     0x943890: sbfiz           x0, x2, #1, #0x1f
    //     0x943894: cmp             x2, x0, asr #1
    //     0x943898: b.eq            #0x9438a4
    //     0x94389c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9438a0: stur            x2, [x0, #7]
    // 0x9438a4: ldur            x2, [fp, #-8]
    // 0x9438a8: StoreField: r2->field_2b = r0
    //     0x9438a8: stur            w0, [x2, #0x2b]
    //     0x9438ac: tbz             w0, #0, #0x9438c8
    //     0x9438b0: ldurb           w16, [x2, #-1]
    //     0x9438b4: ldurb           w17, [x0, #-1]
    //     0x9438b8: and             x16, x17, x16, lsr #2
    //     0x9438bc: tst             x16, HEAP, lsr #32
    //     0x9438c0: b.eq            #0x9438c8
    //     0x9438c4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9438c8: LoadField: r1 = r2->field_23
    //     0x9438c8: ldur            w1, [x2, #0x23]
    // 0x9438cc: DecompressPointer r1
    //     0x9438cc: add             x1, x1, HEAP, lsl #32
    // 0x9438d0: cmp             w1, NULL
    // 0x9438d4: b.ne            #0x9438e0
    // 0x9438d8: r0 = Null
    //     0x9438d8: mov             x0, NULL
    // 0x9438dc: b               #0x943960
    // 0x9438e0: r0 = LoadClassIdInstr(r1)
    //     0x9438e0: ldur            x0, [x1, #-1]
    //     0x9438e4: ubfx            x0, x0, #0xc, #0x14
    // 0x9438e8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9438e8: sub             lr, x0, #1, lsl #12
    //     0x9438ec: ldr             lr, [x21, lr, lsl #3]
    //     0x9438f0: blr             lr
    // 0x9438f4: mov             x2, x0
    // 0x9438f8: ldur            x3, [fp, #-8]
    // 0x9438fc: LoadField: r0 = r3->field_2b
    //     0x9438fc: ldur            w0, [x3, #0x2b]
    // 0x943900: DecompressPointer r0
    //     0x943900: add             x0, x0, HEAP, lsl #32
    // 0x943904: cmp             w0, NULL
    // 0x943908: b.eq            #0x943b1c
    // 0x94390c: r1 = LoadInt32Instr(r0)
    //     0x94390c: sbfx            x1, x0, #1, #0x1f
    //     0x943910: tbz             w0, #0, #0x943918
    //     0x943914: ldur            x1, [x0, #7]
    // 0x943918: sub             x4, x1, #1
    // 0x94391c: r0 = BoxInt64Instr(r4)
    //     0x94391c: sbfiz           x0, x4, #1, #0x1f
    //     0x943920: cmp             x4, x0, asr #1
    //     0x943924: b.eq            #0x943930
    //     0x943928: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94392c: stur            x4, [x0, #7]
    // 0x943930: str             x0, [SP]
    // 0x943934: mov             x1, x2
    // 0x943938: r2 = "/"
    //     0x943938: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x94393c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x94393c: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x943940: r0 = lastIndexOf()
    //     0x943940: bl              #0x6394bc  ; [dart:core] _StringBase::lastIndexOf
    // 0x943944: mov             x2, x0
    // 0x943948: r0 = BoxInt64Instr(r2)
    //     0x943948: sbfiz           x0, x2, #1, #0x1f
    //     0x94394c: cmp             x2, x0, asr #1
    //     0x943950: b.eq            #0x94395c
    //     0x943954: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x943958: stur            x2, [x0, #7]
    // 0x94395c: ldur            x2, [fp, #-8]
    // 0x943960: StoreField: r2->field_2f = r0
    //     0x943960: stur            w0, [x2, #0x2f]
    //     0x943964: tbz             w0, #0, #0x943980
    //     0x943968: ldurb           w16, [x2, #-1]
    //     0x94396c: ldurb           w17, [x0, #-1]
    //     0x943970: and             x16, x17, x16, lsr #2
    //     0x943974: tst             x16, HEAP, lsr #32
    //     0x943978: b.eq            #0x943980
    //     0x94397c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x943980: LoadField: r1 = r2->field_23
    //     0x943980: ldur            w1, [x2, #0x23]
    // 0x943984: DecompressPointer r1
    //     0x943984: add             x1, x1, HEAP, lsl #32
    // 0x943988: cmp             w1, NULL
    // 0x94398c: b.eq            #0x943a38
    // 0x943990: r0 = LoadClassIdInstr(r1)
    //     0x943990: ldur            x0, [x1, #-1]
    //     0x943994: ubfx            x0, x0, #0xc, #0x14
    // 0x943998: r0 = GDT[cid_x0 + -0x1000]()
    //     0x943998: sub             lr, x0, #1, lsl #12
    //     0x94399c: ldr             lr, [x21, lr, lsl #3]
    //     0x9439a0: blr             lr
    // 0x9439a4: mov             x3, x0
    // 0x9439a8: ldur            x2, [fp, #-8]
    // 0x9439ac: stur            x3, [fp, #-0x18]
    // 0x9439b0: LoadField: r0 = r2->field_2f
    //     0x9439b0: ldur            w0, [x2, #0x2f]
    // 0x9439b4: DecompressPointer r0
    //     0x9439b4: add             x0, x0, HEAP, lsl #32
    // 0x9439b8: cmp             w0, NULL
    // 0x9439bc: b.eq            #0x943b20
    // 0x9439c0: r1 = LoadInt32Instr(r0)
    //     0x9439c0: sbfx            x1, x0, #1, #0x1f
    //     0x9439c4: tbz             w0, #0, #0x9439cc
    //     0x9439c8: ldur            x1, [x0, #7]
    // 0x9439cc: add             x4, x1, #1
    // 0x9439d0: stur            x4, [fp, #-0x28]
    // 0x9439d4: LoadField: r1 = r2->field_23
    //     0x9439d4: ldur            w1, [x2, #0x23]
    // 0x9439d8: DecompressPointer r1
    //     0x9439d8: add             x1, x1, HEAP, lsl #32
    // 0x9439dc: cmp             w1, NULL
    // 0x9439e0: b.ne            #0x9439f0
    // 0x9439e4: mov             x2, x4
    // 0x9439e8: r3 = Null
    //     0x9439e8: mov             x3, NULL
    // 0x9439ec: b               #0x943a10
    // 0x9439f0: r0 = LoadClassIdInstr(r1)
    //     0x9439f0: ldur            x0, [x1, #-1]
    //     0x9439f4: ubfx            x0, x0, #0xc, #0x14
    // 0x9439f8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9439f8: sub             lr, x0, #1, lsl #12
    //     0x9439fc: ldr             lr, [x21, lr, lsl #3]
    //     0x943a00: blr             lr
    // 0x943a04: LoadField: r1 = r0->field_7
    //     0x943a04: ldur            w1, [x0, #7]
    // 0x943a08: mov             x3, x1
    // 0x943a0c: ldur            x2, [fp, #-0x28]
    // 0x943a10: r0 = BoxInt64Instr(r2)
    //     0x943a10: sbfiz           x0, x2, #1, #0x1f
    //     0x943a14: cmp             x2, x0, asr #1
    //     0x943a18: b.eq            #0x943a24
    //     0x943a1c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x943a20: stur            x2, [x0, #7]
    // 0x943a24: str             x3, [SP]
    // 0x943a28: ldur            x1, [fp, #-0x18]
    // 0x943a2c: mov             x2, x0
    // 0x943a30: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x943a30: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x943a34: r0 = substring()
    //     0x943a34: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0x943a38: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x943a38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x943a3c: ldr             x0, [x0, #0x1c80]
    //     0x943a40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x943a44: cmp             w0, w16
    //     0x943a48: b.ne            #0x943a54
    //     0x943a4c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x943a50: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x943a54: r16 = "/bag"
    //     0x943a54: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x943a58: ldr             x16, [x16, #0x468]
    // 0x943a5c: stp             x16, NULL, [SP]
    // 0x943a60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x943a60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x943a64: r0 = GetNavigation.toNamed()
    //     0x943a64: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x943a68: b               #0x943afc
    // 0x943a6c: ldur            x2, [fp, #-8]
    // 0x943a70: LoadField: r1 = r2->field_23
    //     0x943a70: ldur            w1, [x2, #0x23]
    // 0x943a74: DecompressPointer r1
    //     0x943a74: add             x1, x1, HEAP, lsl #32
    // 0x943a78: cmp             w1, NULL
    // 0x943a7c: b.ne            #0x943a88
    // 0x943a80: r0 = Null
    //     0x943a80: mov             x0, NULL
    // 0x943a84: b               #0x943ac8
    // 0x943a88: r0 = LoadClassIdInstr(r1)
    //     0x943a88: ldur            x0, [x1, #-1]
    //     0x943a8c: ubfx            x0, x0, #0xc, #0x14
    // 0x943a90: r0 = GDT[cid_x0 + -0x1000]()
    //     0x943a90: sub             lr, x0, #1, lsl #12
    //     0x943a94: ldr             lr, [x21, lr, lsl #3]
    //     0x943a98: blr             lr
    // 0x943a9c: r1 = LoadClassIdInstr(r0)
    //     0x943a9c: ldur            x1, [x0, #-1]
    //     0x943aa0: ubfx            x1, x1, #0xc, #0x14
    // 0x943aa4: mov             x16, x0
    // 0x943aa8: mov             x0, x1
    // 0x943aac: mov             x1, x16
    // 0x943ab0: r2 = "/orders"
    //     0x943ab0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da80] "/orders"
    //     0x943ab4: ldr             x2, [x2, #0xa80]
    // 0x943ab8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x943ab8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x943abc: r0 = GDT[cid_x0 + -0xffe]()
    //     0x943abc: sub             lr, x0, #0xffe
    //     0x943ac0: ldr             lr, [x21, lr, lsl #3]
    //     0x943ac4: blr             lr
    // 0x943ac8: cmp             w0, NULL
    // 0x943acc: b.eq            #0x943afc
    // 0x943ad0: tbnz            w0, #4, #0x943afc
    // 0x943ad4: ldur            x2, [fp, #-0x10]
    // 0x943ad8: r1 = Function '<anonymous closure>':.
    //     0x943ad8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc98] AnonymousClosure: (0x915664), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp (0x915688)
    //     0x943adc: ldr             x1, [x1, #0xc98]
    // 0x943ae0: r0 = AllocateClosure()
    //     0x943ae0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x943ae4: ldur            x1, [fp, #-8]
    // 0x943ae8: mov             x2, x0
    // 0x943aec: r0 = setState()
    //     0x943aec: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x943af0: ldur            x1, [fp, #-8]
    // 0x943af4: r2 = 2
    //     0x943af4: movz            x2, #0x2
    // 0x943af8: r0 = _onItemTapped()
    //     0x943af8: bl              #0x942718  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x943afc: r0 = Null
    //     0x943afc: mov             x0, NULL
    // 0x943b00: LeaveFrame
    //     0x943b00: mov             SP, fp
    //     0x943b04: ldp             fp, lr, [SP], #0x10
    // 0x943b08: ret
    //     0x943b08: ret             
    // 0x943b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943b0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943b10: b               #0x9433e8
    // 0x943b14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x943b14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x943b18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x943b18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x943b1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x943b1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x943b20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x943b20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Set<Set<void>>> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x943b24, size: 0x320
    // 0x943b24: EnterFrame
    //     0x943b24: stp             fp, lr, [SP, #-0x10]!
    //     0x943b28: mov             fp, SP
    // 0x943b2c: AllocStack(0x48)
    //     0x943b2c: sub             SP, SP, #0x48
    // 0x943b30: SetupParameters(_MainPageState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x943b30: stur            NULL, [fp, #-8]
    //     0x943b34: movz            x0, #0
    //     0x943b38: add             x1, fp, w0, sxtw #2
    //     0x943b3c: ldr             x1, [x1, #0x18]
    //     0x943b40: add             x2, fp, w0, sxtw #2
    //     0x943b44: ldr             x2, [x2, #0x10]
    //     0x943b48: stur            x2, [fp, #-0x18]
    //     0x943b4c: ldur            w3, [x1, #0x17]
    //     0x943b50: add             x3, x3, HEAP, lsl #32
    //     0x943b54: stur            x3, [fp, #-0x10]
    // 0x943b58: CheckStackOverflow
    //     0x943b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943b5c: cmp             SP, x16
    //     0x943b60: b.ls            #0x943e3c
    // 0x943b64: InitAsync() -> Future<Set<Set<void?>>>
    //     0x943b64: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d720] TypeArguments: <Set<Set<void?>>>
    //     0x943b68: ldr             x0, [x0, #0x720]
    //     0x943b6c: bl              #0x6326e0  ; InitAsyncStub
    // 0x943b70: r1 = <Set<void?>>
    //     0x943b70: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d730] TypeArguments: <Set<void?>>
    //     0x943b74: ldr             x1, [x1, #0x730]
    // 0x943b78: r0 = _Set()
    //     0x943b78: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x943b7c: mov             x2, x0
    // 0x943b80: r1 = _Uint32List
    //     0x943b80: ldr             x1, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943b84: stur            x2, [fp, #-0x20]
    // 0x943b88: StoreField: r2->field_1b = r1
    //     0x943b88: stur            w1, [x2, #0x1b]
    // 0x943b8c: StoreField: r2->field_b = rZR
    //     0x943b8c: stur            wzr, [x2, #0xb]
    // 0x943b90: r3 = const []
    //     0x943b90: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943b94: StoreField: r2->field_f = r3
    //     0x943b94: stur            w3, [x2, #0xf]
    // 0x943b98: StoreField: r2->field_13 = rZR
    //     0x943b98: stur            wzr, [x2, #0x13]
    // 0x943b9c: ArrayStore: r2[0] = rZR  ; List_4
    //     0x943b9c: stur            wzr, [x2, #0x17]
    // 0x943ba0: ldur            x0, [fp, #-0x18]
    // 0x943ba4: r4 = 60
    //     0x943ba4: movz            x4, #0x3c
    // 0x943ba8: branchIfSmi(r0, 0x943bb4)
    //     0x943ba8: tbz             w0, #0, #0x943bb4
    // 0x943bac: r4 = LoadClassIdInstr(r0)
    //     0x943bac: ldur            x4, [x0, #-1]
    //     0x943bb0: ubfx            x4, x4, #0xc, #0x14
    // 0x943bb4: r16 = ""
    //     0x943bb4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x943bb8: stp             x16, x0, [SP]
    // 0x943bbc: mov             x0, x4
    // 0x943bc0: mov             lr, x0
    // 0x943bc4: ldr             lr, [x21, lr, lsl #3]
    // 0x943bc8: blr             lr
    // 0x943bcc: tbz             w0, #4, #0x943c2c
    // 0x943bd0: ldur            x0, [fp, #-0x10]
    // 0x943bd4: r1 = <void?>
    //     0x943bd4: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x943bd8: r0 = _Set()
    //     0x943bd8: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x943bdc: mov             x2, x0
    // 0x943be0: r0 = _Uint32List
    //     0x943be0: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943be4: stur            x2, [fp, #-0x18]
    // 0x943be8: StoreField: r2->field_1b = r0
    //     0x943be8: stur            w0, [x2, #0x1b]
    // 0x943bec: StoreField: r2->field_b = rZR
    //     0x943bec: stur            wzr, [x2, #0xb]
    // 0x943bf0: r3 = const []
    //     0x943bf0: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943bf4: StoreField: r2->field_f = r3
    //     0x943bf4: stur            w3, [x2, #0xf]
    // 0x943bf8: StoreField: r2->field_13 = rZR
    //     0x943bf8: stur            wzr, [x2, #0x13]
    // 0x943bfc: ArrayStore: r2[0] = rZR  ; List_4
    //     0x943bfc: stur            wzr, [x2, #0x17]
    // 0x943c00: ldur            x4, [fp, #-0x10]
    // 0x943c04: LoadField: r1 = r4->field_f
    //     0x943c04: ldur            w1, [x4, #0xf]
    // 0x943c08: DecompressPointer r1
    //     0x943c08: add             x1, x1, HEAP, lsl #32
    // 0x943c0c: r0 = openUrlInApp()
    //     0x943c0c: bl              #0x9433cc  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x943c10: ldur            x1, [fp, #-0x18]
    // 0x943c14: r2 = Null
    //     0x943c14: mov             x2, NULL
    // 0x943c18: r0 = add()
    //     0x943c18: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943c1c: ldur            x1, [fp, #-0x20]
    // 0x943c20: ldur            x2, [fp, #-0x18]
    // 0x943c24: r0 = add()
    //     0x943c24: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943c28: b               #0x943e34
    // 0x943c2c: ldur            x4, [fp, #-0x10]
    // 0x943c30: r0 = _Uint32List
    //     0x943c30: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943c34: r3 = const []
    //     0x943c34: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943c38: r1 = <Set<void?>>
    //     0x943c38: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d730] TypeArguments: <Set<void?>>
    //     0x943c3c: ldr             x1, [x1, #0x730]
    // 0x943c40: r0 = _Set()
    //     0x943c40: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x943c44: mov             x4, x0
    // 0x943c48: r3 = _Uint32List
    //     0x943c48: ldr             x3, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943c4c: stur            x4, [fp, #-0x28]
    // 0x943c50: StoreField: r4->field_1b = r3
    //     0x943c50: stur            w3, [x4, #0x1b]
    // 0x943c54: StoreField: r4->field_b = rZR
    //     0x943c54: stur            wzr, [x4, #0xb]
    // 0x943c58: r5 = const []
    //     0x943c58: ldr             x5, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943c5c: StoreField: r4->field_f = r5
    //     0x943c5c: stur            w5, [x4, #0xf]
    // 0x943c60: StoreField: r4->field_13 = rZR
    //     0x943c60: stur            wzr, [x4, #0x13]
    // 0x943c64: ArrayStore: r4[0] = rZR  ; List_4
    //     0x943c64: stur            wzr, [x4, #0x17]
    // 0x943c68: ldur            x6, [fp, #-0x10]
    // 0x943c6c: LoadField: r7 = r6->field_13
    //     0x943c6c: ldur            w7, [x6, #0x13]
    // 0x943c70: DecompressPointer r7
    //     0x943c70: add             x7, x7, HEAP, lsl #32
    // 0x943c74: stur            x7, [fp, #-0x18]
    // 0x943c78: cmp             w7, NULL
    // 0x943c7c: b.ne            #0x943c88
    // 0x943c80: r0 = Null
    //     0x943c80: mov             x0, NULL
    // 0x943c84: b               #0x943ccc
    // 0x943c88: r0 = LoadClassIdInstr(r7)
    //     0x943c88: ldur            x0, [x7, #-1]
    //     0x943c8c: ubfx            x0, x0, #0xc, #0x14
    // 0x943c90: mov             x1, x7
    // 0x943c94: r2 = "auto_login_otp"
    //     0x943c94: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d838] "auto_login_otp"
    //     0x943c98: ldr             x2, [x2, #0x838]
    // 0x943c9c: r0 = GDT[cid_x0 + -0xfe]()
    //     0x943c9c: sub             lr, x0, #0xfe
    //     0x943ca0: ldr             lr, [x21, lr, lsl #3]
    //     0x943ca4: blr             lr
    // 0x943ca8: cmp             w0, NULL
    // 0x943cac: b.ne            #0x943cb8
    // 0x943cb0: r0 = Null
    //     0x943cb0: mov             x0, NULL
    // 0x943cb4: b               #0x943ccc
    // 0x943cb8: LoadField: r1 = r0->field_7
    //     0x943cb8: ldur            w1, [x0, #7]
    // 0x943cbc: cbnz            w1, #0x943cc8
    // 0x943cc0: r0 = false
    //     0x943cc0: add             x0, NULL, #0x30  ; false
    // 0x943cc4: b               #0x943ccc
    // 0x943cc8: r0 = true
    //     0x943cc8: add             x0, NULL, #0x20  ; true
    // 0x943ccc: cmp             w0, NULL
    // 0x943cd0: b.ne            #0x943ce4
    // 0x943cd4: ldur            x3, [fp, #-0x10]
    // 0x943cd8: r0 = _Uint32List
    //     0x943cd8: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943cdc: r2 = const []
    //     0x943cdc: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943ce0: b               #0x943dd4
    // 0x943ce4: tbnz            w0, #4, #0x943dc8
    // 0x943ce8: ldur            x0, [fp, #-0x10]
    // 0x943cec: ldur            x2, [fp, #-0x18]
    // 0x943cf0: r1 = <void?>
    //     0x943cf0: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x943cf4: r0 = _Set()
    //     0x943cf4: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x943cf8: mov             x3, x0
    // 0x943cfc: r0 = _Uint32List
    //     0x943cfc: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943d00: stur            x3, [fp, #-0x38]
    // 0x943d04: StoreField: r3->field_1b = r0
    //     0x943d04: stur            w0, [x3, #0x1b]
    // 0x943d08: StoreField: r3->field_b = rZR
    //     0x943d08: stur            wzr, [x3, #0xb]
    // 0x943d0c: r2 = const []
    //     0x943d0c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943d10: StoreField: r3->field_f = r2
    //     0x943d10: stur            w2, [x3, #0xf]
    // 0x943d14: StoreField: r3->field_13 = rZR
    //     0x943d14: stur            wzr, [x3, #0x13]
    // 0x943d18: ArrayStore: r3[0] = rZR  ; List_4
    //     0x943d18: stur            wzr, [x3, #0x17]
    // 0x943d1c: ldur            x4, [fp, #-0x10]
    // 0x943d20: LoadField: r0 = r4->field_f
    //     0x943d20: ldur            w0, [x4, #0xf]
    // 0x943d24: DecompressPointer r0
    //     0x943d24: add             x0, x0, HEAP, lsl #32
    // 0x943d28: LoadField: r5 = r0->field_3b
    //     0x943d28: ldur            w5, [x0, #0x3b]
    // 0x943d2c: DecompressPointer r5
    //     0x943d2c: add             x5, x5, HEAP, lsl #32
    // 0x943d30: ldur            x1, [fp, #-0x18]
    // 0x943d34: stur            x5, [fp, #-0x30]
    // 0x943d38: cmp             w1, NULL
    // 0x943d3c: b.ne            #0x943d48
    // 0x943d40: r0 = Null
    //     0x943d40: mov             x0, NULL
    // 0x943d44: b               #0x943d64
    // 0x943d48: r0 = LoadClassIdInstr(r1)
    //     0x943d48: ldur            x0, [x1, #-1]
    //     0x943d4c: ubfx            x0, x0, #0xc, #0x14
    // 0x943d50: r2 = "auto_login_otp"
    //     0x943d50: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d838] "auto_login_otp"
    //     0x943d54: ldr             x2, [x2, #0x838]
    // 0x943d58: r0 = GDT[cid_x0 + -0xfe]()
    //     0x943d58: sub             lr, x0, #0xfe
    //     0x943d5c: ldr             lr, [x21, lr, lsl #3]
    //     0x943d60: blr             lr
    // 0x943d64: cmp             w0, NULL
    // 0x943d68: b.ne            #0x943d74
    // 0x943d6c: r2 = ""
    //     0x943d6c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x943d70: b               #0x943d78
    // 0x943d74: mov             x2, x0
    // 0x943d78: ldur            x0, [fp, #-0x10]
    // 0x943d7c: ldur            x1, [fp, #-0x30]
    // 0x943d80: r0 = verifyAutoLoginOtp()
    //     0x943d80: bl              #0x916fa0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::verifyAutoLoginOtp
    // 0x943d84: mov             x1, x0
    // 0x943d88: stur            x1, [fp, #-0x18]
    // 0x943d8c: r0 = Await()
    //     0x943d8c: bl              #0x63248c  ; AwaitStub
    // 0x943d90: ldur            x1, [fp, #-0x38]
    // 0x943d94: mov             x2, x0
    // 0x943d98: r0 = add()
    //     0x943d98: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943d9c: ldur            x3, [fp, #-0x10]
    // 0x943da0: LoadField: r1 = r3->field_f
    //     0x943da0: ldur            w1, [x3, #0xf]
    // 0x943da4: DecompressPointer r1
    //     0x943da4: add             x1, x1, HEAP, lsl #32
    // 0x943da8: r0 = openUrlInApp()
    //     0x943da8: bl              #0x9433cc  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x943dac: ldur            x1, [fp, #-0x38]
    // 0x943db0: r2 = Null
    //     0x943db0: mov             x2, NULL
    // 0x943db4: r0 = add()
    //     0x943db4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943db8: ldur            x1, [fp, #-0x28]
    // 0x943dbc: ldur            x2, [fp, #-0x38]
    // 0x943dc0: r0 = add()
    //     0x943dc0: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943dc4: b               #0x943e28
    // 0x943dc8: ldur            x3, [fp, #-0x10]
    // 0x943dcc: r0 = _Uint32List
    //     0x943dcc: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943dd0: r2 = const []
    //     0x943dd0: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943dd4: r1 = <void?>
    //     0x943dd4: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x943dd8: r0 = _Set()
    //     0x943dd8: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x943ddc: mov             x2, x0
    // 0x943de0: r0 = _Uint32List
    //     0x943de0: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x943de4: stur            x2, [fp, #-0x18]
    // 0x943de8: StoreField: r2->field_1b = r0
    //     0x943de8: stur            w0, [x2, #0x1b]
    // 0x943dec: StoreField: r2->field_b = rZR
    //     0x943dec: stur            wzr, [x2, #0xb]
    // 0x943df0: r0 = const []
    //     0x943df0: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x943df4: StoreField: r2->field_f = r0
    //     0x943df4: stur            w0, [x2, #0xf]
    // 0x943df8: StoreField: r2->field_13 = rZR
    //     0x943df8: stur            wzr, [x2, #0x13]
    // 0x943dfc: ArrayStore: r2[0] = rZR  ; List_4
    //     0x943dfc: stur            wzr, [x2, #0x17]
    // 0x943e00: ldur            x0, [fp, #-0x10]
    // 0x943e04: LoadField: r1 = r0->field_f
    //     0x943e04: ldur            w1, [x0, #0xf]
    // 0x943e08: DecompressPointer r1
    //     0x943e08: add             x1, x1, HEAP, lsl #32
    // 0x943e0c: r0 = openUrlInApp()
    //     0x943e0c: bl              #0x9433cc  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x943e10: ldur            x1, [fp, #-0x18]
    // 0x943e14: r2 = Null
    //     0x943e14: mov             x2, NULL
    // 0x943e18: r0 = add()
    //     0x943e18: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943e1c: ldur            x1, [fp, #-0x28]
    // 0x943e20: ldur            x2, [fp, #-0x18]
    // 0x943e24: r0 = add()
    //     0x943e24: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943e28: ldur            x1, [fp, #-0x20]
    // 0x943e2c: ldur            x2, [fp, #-0x28]
    // 0x943e30: r0 = add()
    //     0x943e30: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x943e34: ldur            x0, [fp, #-0x20]
    // 0x943e38: r0 = ReturnAsyncNotFuture()
    //     0x943e38: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x943e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943e3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943e40: b               #0x943b64
  }
  _ build(/* No info */) {
    // ** addr: 0xb71c24, size: 0x58
    // 0xb71c24: EnterFrame
    //     0xb71c24: stp             fp, lr, [SP, #-0x10]!
    //     0xb71c28: mov             fp, SP
    // 0xb71c2c: AllocStack(0x10)
    //     0xb71c2c: sub             SP, SP, #0x10
    // 0xb71c30: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0xb71c30: stur            x1, [fp, #-8]
    // 0xb71c34: r1 = 1
    //     0xb71c34: movz            x1, #0x1
    // 0xb71c38: r0 = AllocateContext()
    //     0xb71c38: bl              #0x16f6108  ; AllocateContextStub
    // 0xb71c3c: mov             x1, x0
    // 0xb71c40: ldur            x0, [fp, #-8]
    // 0xb71c44: stur            x1, [fp, #-0x10]
    // 0xb71c48: StoreField: r1->field_f = r0
    //     0xb71c48: stur            w0, [x1, #0xf]
    // 0xb71c4c: r0 = Obx()
    //     0xb71c4c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb71c50: ldur            x2, [fp, #-0x10]
    // 0xb71c54: r1 = Function '<anonymous closure>':.
    //     0xb71c54: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2db28] AnonymousClosure: (0xb71c7c), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::build (0xb71c24)
    //     0xb71c58: ldr             x1, [x1, #0xb28]
    // 0xb71c5c: stur            x0, [fp, #-8]
    // 0xb71c60: r0 = AllocateClosure()
    //     0xb71c60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb71c64: mov             x1, x0
    // 0xb71c68: ldur            x0, [fp, #-8]
    // 0xb71c6c: StoreField: r0->field_b = r1
    //     0xb71c6c: stur            w1, [x0, #0xb]
    // 0xb71c70: LeaveFrame
    //     0xb71c70: mov             SP, fp
    //     0xb71c74: ldp             fp, lr, [SP], #0x10
    // 0xb71c78: ret
    //     0xb71c78: ret             
  }
  [closure] Theme <anonymous closure>(dynamic) {
    // ** addr: 0xb71c7c, size: 0x13c
    // 0xb71c7c: EnterFrame
    //     0xb71c7c: stp             fp, lr, [SP, #-0x10]!
    //     0xb71c80: mov             fp, SP
    // 0xb71c84: AllocStack(0x20)
    //     0xb71c84: sub             SP, SP, #0x20
    // 0xb71c88: SetupParameters()
    //     0xb71c88: ldr             x0, [fp, #0x10]
    //     0xb71c8c: ldur            w2, [x0, #0x17]
    //     0xb71c90: add             x2, x2, HEAP, lsl #32
    //     0xb71c94: stur            x2, [fp, #-8]
    // 0xb71c98: CheckStackOverflow
    //     0xb71c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71c9c: cmp             SP, x16
    //     0xb71ca0: b.ls            #0xb71dac
    // 0xb71ca4: LoadField: r0 = r2->field_f
    //     0xb71ca4: ldur            w0, [x2, #0xf]
    // 0xb71ca8: DecompressPointer r0
    //     0xb71ca8: add             x0, x0, HEAP, lsl #32
    // 0xb71cac: LoadField: r1 = r0->field_3f
    //     0xb71cac: ldur            w1, [x0, #0x3f]
    // 0xb71cb0: DecompressPointer r1
    //     0xb71cb0: add             x1, x1, HEAP, lsl #32
    // 0xb71cb4: LoadField: r0 = r1->field_5b
    //     0xb71cb4: ldur            w0, [x1, #0x5b]
    // 0xb71cb8: DecompressPointer r0
    //     0xb71cb8: add             x0, x0, HEAP, lsl #32
    // 0xb71cbc: mov             x1, x0
    // 0xb71cc0: r0 = value()
    //     0xb71cc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb71cc4: stur            x0, [fp, #-0x10]
    // 0xb71cc8: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0xb71cc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb71ccc: ldr             x0, [x0, #0x1ce0]
    //     0xb71cd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb71cd4: cmp             w0, w16
    //     0xb71cd8: b.ne            #0xb71ce8
    //     0xb71cdc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0xb71ce0: ldr             x2, [x2, #0xb30]
    //     0xb71ce4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb71ce8: mov             x2, x0
    // 0xb71cec: ldur            x0, [fp, #-8]
    // 0xb71cf0: LoadField: r3 = r0->field_f
    //     0xb71cf0: ldur            w3, [x0, #0xf]
    // 0xb71cf4: DecompressPointer r3
    //     0xb71cf4: add             x3, x3, HEAP, lsl #32
    // 0xb71cf8: stur            x3, [fp, #-0x18]
    // 0xb71cfc: LoadField: r4 = r3->field_1b
    //     0xb71cfc: ldur            x4, [x3, #0x1b]
    // 0xb71d00: LoadField: r0 = r2->field_b
    //     0xb71d00: ldur            w0, [x2, #0xb]
    // 0xb71d04: r1 = LoadInt32Instr(r0)
    //     0xb71d04: sbfx            x1, x0, #1, #0x1f
    // 0xb71d08: mov             x0, x1
    // 0xb71d0c: mov             x1, x4
    // 0xb71d10: cmp             x1, x0
    // 0xb71d14: b.hs            #0xb71db4
    // 0xb71d18: LoadField: r0 = r2->field_f
    //     0xb71d18: ldur            w0, [x2, #0xf]
    // 0xb71d1c: DecompressPointer r0
    //     0xb71d1c: add             x0, x0, HEAP, lsl #32
    // 0xb71d20: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb71d20: add             x16, x0, x4, lsl #2
    //     0xb71d24: ldur            w1, [x16, #0xf]
    // 0xb71d28: DecompressPointer r1
    //     0xb71d28: add             x1, x1, HEAP, lsl #32
    // 0xb71d2c: stur            x1, [fp, #-8]
    // 0xb71d30: r0 = Center()
    //     0xb71d30: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb71d34: mov             x2, x0
    // 0xb71d38: r0 = Instance_Alignment
    //     0xb71d38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb71d3c: ldr             x0, [x0, #0xb10]
    // 0xb71d40: stur            x2, [fp, #-0x20]
    // 0xb71d44: StoreField: r2->field_f = r0
    //     0xb71d44: stur            w0, [x2, #0xf]
    // 0xb71d48: ldur            x0, [fp, #-8]
    // 0xb71d4c: StoreField: r2->field_b = r0
    //     0xb71d4c: stur            w0, [x2, #0xb]
    // 0xb71d50: ldur            x1, [fp, #-0x18]
    // 0xb71d54: r0 = buildBottomNavigationMenu()
    //     0xb71d54: bl              #0xb71db8  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::buildBottomNavigationMenu
    // 0xb71d58: stur            x0, [fp, #-8]
    // 0xb71d5c: r0 = Scaffold()
    //     0xb71d5c: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb71d60: mov             x1, x0
    // 0xb71d64: ldur            x0, [fp, #-0x20]
    // 0xb71d68: stur            x1, [fp, #-0x18]
    // 0xb71d6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb71d6c: stur            w0, [x1, #0x17]
    // 0xb71d70: ldur            x0, [fp, #-8]
    // 0xb71d74: StoreField: r1->field_37 = r0
    //     0xb71d74: stur            w0, [x1, #0x37]
    // 0xb71d78: r0 = true
    //     0xb71d78: add             x0, NULL, #0x20  ; true
    // 0xb71d7c: StoreField: r1->field_43 = r0
    //     0xb71d7c: stur            w0, [x1, #0x43]
    // 0xb71d80: r0 = false
    //     0xb71d80: add             x0, NULL, #0x30  ; false
    // 0xb71d84: StoreField: r1->field_b = r0
    //     0xb71d84: stur            w0, [x1, #0xb]
    // 0xb71d88: StoreField: r1->field_f = r0
    //     0xb71d88: stur            w0, [x1, #0xf]
    // 0xb71d8c: r0 = Theme()
    //     0xb71d8c: bl              #0x796f30  ; AllocateThemeStub -> Theme (size=0x14)
    // 0xb71d90: ldur            x1, [fp, #-0x10]
    // 0xb71d94: StoreField: r0->field_b = r1
    //     0xb71d94: stur            w1, [x0, #0xb]
    // 0xb71d98: ldur            x1, [fp, #-0x18]
    // 0xb71d9c: StoreField: r0->field_f = r1
    //     0xb71d9c: stur            w1, [x0, #0xf]
    // 0xb71da0: LeaveFrame
    //     0xb71da0: mov             SP, fp
    //     0xb71da4: ldp             fp, lr, [SP], #0x10
    // 0xb71da8: ret
    //     0xb71da8: ret             
    // 0xb71dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb71dac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb71db0: b               #0xb71ca4
    // 0xb71db4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb71db4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ buildBottomNavigationMenu(/* No info */) {
    // ** addr: 0xb71db8, size: 0x1424
    // 0xb71db8: EnterFrame
    //     0xb71db8: stp             fp, lr, [SP, #-0x10]!
    //     0xb71dbc: mov             fp, SP
    // 0xb71dc0: AllocStack(0x70)
    //     0xb71dc0: sub             SP, SP, #0x70
    // 0xb71dc4: SetupParameters(_MainPageState this /* r1 => r2, fp-0x10 */)
    //     0xb71dc4: mov             x2, x1
    //     0xb71dc8: stur            x1, [fp, #-0x10]
    // 0xb71dcc: CheckStackOverflow
    //     0xb71dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb71dd0: cmp             SP, x16
    //     0xb71dd4: b.ls            #0xb731bc
    // 0xb71dd8: LoadField: r0 = r2->field_1b
    //     0xb71dd8: ldur            x0, [x2, #0x1b]
    // 0xb71ddc: stur            x0, [fp, #-8]
    // 0xb71de0: r0 = SvgPicture()
    //     0xb71de0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb71de4: stur            x0, [fp, #-0x18]
    // 0xb71de8: r16 = "home"
    //     0xb71de8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb470] "home"
    //     0xb71dec: ldr             x16, [x16, #0x470]
    // 0xb71df0: r30 = Instance_BoxFit
    //     0xb71df0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb71df4: ldr             lr, [lr, #0xb18]
    // 0xb71df8: stp             lr, x16, [SP]
    // 0xb71dfc: mov             x1, x0
    // 0xb71e00: r2 = "assets/images/home.svg"
    //     0xb71e00: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "assets/images/home.svg"
    //     0xb71e04: ldr             x2, [x2, #0xb20]
    // 0xb71e08: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb71e08: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb71e0c: ldr             x4, [x4, #0xb28]
    // 0xb71e10: r0 = SvgPicture.asset()
    //     0xb71e10: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb71e14: ldur            x2, [fp, #-0x10]
    // 0xb71e18: LoadField: r0 = r2->field_3f
    //     0xb71e18: ldur            w0, [x2, #0x3f]
    // 0xb71e1c: DecompressPointer r0
    //     0xb71e1c: add             x0, x0, HEAP, lsl #32
    // 0xb71e20: LoadField: r1 = r0->field_57
    //     0xb71e20: ldur            w1, [x0, #0x57]
    // 0xb71e24: DecompressPointer r1
    //     0xb71e24: add             x1, x1, HEAP, lsl #32
    // 0xb71e28: r0 = value()
    //     0xb71e28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb71e2c: LoadField: r1 = r0->field_b
    //     0xb71e2c: ldur            w1, [x0, #0xb]
    // 0xb71e30: DecompressPointer r1
    //     0xb71e30: add             x1, x1, HEAP, lsl #32
    // 0xb71e34: cmp             w1, NULL
    // 0xb71e38: b.ne            #0xb71e44
    // 0xb71e3c: r0 = Null
    //     0xb71e3c: mov             x0, NULL
    // 0xb71e40: b               #0xb71e7c
    // 0xb71e44: LoadField: r0 = r1->field_57
    //     0xb71e44: ldur            w0, [x1, #0x57]
    // 0xb71e48: DecompressPointer r0
    //     0xb71e48: add             x0, x0, HEAP, lsl #32
    // 0xb71e4c: cmp             w0, NULL
    // 0xb71e50: b.ne            #0xb71e5c
    // 0xb71e54: r0 = Null
    //     0xb71e54: mov             x0, NULL
    // 0xb71e58: b               #0xb71e7c
    // 0xb71e5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb71e5c: ldur            w1, [x0, #0x17]
    // 0xb71e60: DecompressPointer r1
    //     0xb71e60: add             x1, x1, HEAP, lsl #32
    // 0xb71e64: cmp             w1, NULL
    // 0xb71e68: b.ne            #0xb71e74
    // 0xb71e6c: r0 = Null
    //     0xb71e6c: mov             x0, NULL
    // 0xb71e70: b               #0xb71e7c
    // 0xb71e74: LoadField: r0 = r1->field_7
    //     0xb71e74: ldur            w0, [x1, #7]
    // 0xb71e78: DecompressPointer r0
    //     0xb71e78: add             x0, x0, HEAP, lsl #32
    // 0xb71e7c: cmp             w0, NULL
    // 0xb71e80: b.ne            #0xb71e8c
    // 0xb71e84: r0 = 0
    //     0xb71e84: movz            x0, #0
    // 0xb71e88: b               #0xb71e9c
    // 0xb71e8c: r1 = LoadInt32Instr(r0)
    //     0xb71e8c: sbfx            x1, x0, #1, #0x1f
    //     0xb71e90: tbz             w0, #0, #0xb71e98
    //     0xb71e94: ldur            x1, [x0, #7]
    // 0xb71e98: mov             x0, x1
    // 0xb71e9c: ldur            x2, [fp, #-0x10]
    // 0xb71ea0: stur            x0, [fp, #-0x20]
    // 0xb71ea4: LoadField: r1 = r2->field_3f
    //     0xb71ea4: ldur            w1, [x2, #0x3f]
    // 0xb71ea8: DecompressPointer r1
    //     0xb71ea8: add             x1, x1, HEAP, lsl #32
    // 0xb71eac: LoadField: r3 = r1->field_57
    //     0xb71eac: ldur            w3, [x1, #0x57]
    // 0xb71eb0: DecompressPointer r3
    //     0xb71eb0: add             x3, x3, HEAP, lsl #32
    // 0xb71eb4: mov             x1, x3
    // 0xb71eb8: r0 = value()
    //     0xb71eb8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb71ebc: LoadField: r1 = r0->field_b
    //     0xb71ebc: ldur            w1, [x0, #0xb]
    // 0xb71ec0: DecompressPointer r1
    //     0xb71ec0: add             x1, x1, HEAP, lsl #32
    // 0xb71ec4: cmp             w1, NULL
    // 0xb71ec8: b.ne            #0xb71ed4
    // 0xb71ecc: r0 = Null
    //     0xb71ecc: mov             x0, NULL
    // 0xb71ed0: b               #0xb71f0c
    // 0xb71ed4: LoadField: r0 = r1->field_57
    //     0xb71ed4: ldur            w0, [x1, #0x57]
    // 0xb71ed8: DecompressPointer r0
    //     0xb71ed8: add             x0, x0, HEAP, lsl #32
    // 0xb71edc: cmp             w0, NULL
    // 0xb71ee0: b.ne            #0xb71eec
    // 0xb71ee4: r0 = Null
    //     0xb71ee4: mov             x0, NULL
    // 0xb71ee8: b               #0xb71f0c
    // 0xb71eec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb71eec: ldur            w1, [x0, #0x17]
    // 0xb71ef0: DecompressPointer r1
    //     0xb71ef0: add             x1, x1, HEAP, lsl #32
    // 0xb71ef4: cmp             w1, NULL
    // 0xb71ef8: b.ne            #0xb71f04
    // 0xb71efc: r0 = Null
    //     0xb71efc: mov             x0, NULL
    // 0xb71f00: b               #0xb71f0c
    // 0xb71f04: LoadField: r0 = r1->field_b
    //     0xb71f04: ldur            w0, [x1, #0xb]
    // 0xb71f08: DecompressPointer r0
    //     0xb71f08: add             x0, x0, HEAP, lsl #32
    // 0xb71f0c: cmp             w0, NULL
    // 0xb71f10: b.ne            #0xb71f1c
    // 0xb71f14: r0 = 0
    //     0xb71f14: movz            x0, #0
    // 0xb71f18: b               #0xb71f2c
    // 0xb71f1c: r1 = LoadInt32Instr(r0)
    //     0xb71f1c: sbfx            x1, x0, #1, #0x1f
    //     0xb71f20: tbz             w0, #0, #0xb71f28
    //     0xb71f24: ldur            x1, [x0, #7]
    // 0xb71f28: mov             x0, x1
    // 0xb71f2c: ldur            x2, [fp, #-0x10]
    // 0xb71f30: stur            x0, [fp, #-0x28]
    // 0xb71f34: LoadField: r1 = r2->field_3f
    //     0xb71f34: ldur            w1, [x2, #0x3f]
    // 0xb71f38: DecompressPointer r1
    //     0xb71f38: add             x1, x1, HEAP, lsl #32
    // 0xb71f3c: LoadField: r3 = r1->field_57
    //     0xb71f3c: ldur            w3, [x1, #0x57]
    // 0xb71f40: DecompressPointer r3
    //     0xb71f40: add             x3, x3, HEAP, lsl #32
    // 0xb71f44: mov             x1, x3
    // 0xb71f48: r0 = value()
    //     0xb71f48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb71f4c: LoadField: r1 = r0->field_b
    //     0xb71f4c: ldur            w1, [x0, #0xb]
    // 0xb71f50: DecompressPointer r1
    //     0xb71f50: add             x1, x1, HEAP, lsl #32
    // 0xb71f54: cmp             w1, NULL
    // 0xb71f58: b.ne            #0xb71f64
    // 0xb71f5c: r0 = Null
    //     0xb71f5c: mov             x0, NULL
    // 0xb71f60: b               #0xb71f9c
    // 0xb71f64: LoadField: r0 = r1->field_57
    //     0xb71f64: ldur            w0, [x1, #0x57]
    // 0xb71f68: DecompressPointer r0
    //     0xb71f68: add             x0, x0, HEAP, lsl #32
    // 0xb71f6c: cmp             w0, NULL
    // 0xb71f70: b.ne            #0xb71f7c
    // 0xb71f74: r0 = Null
    //     0xb71f74: mov             x0, NULL
    // 0xb71f78: b               #0xb71f9c
    // 0xb71f7c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb71f7c: ldur            w1, [x0, #0x17]
    // 0xb71f80: DecompressPointer r1
    //     0xb71f80: add             x1, x1, HEAP, lsl #32
    // 0xb71f84: cmp             w1, NULL
    // 0xb71f88: b.ne            #0xb71f94
    // 0xb71f8c: r0 = Null
    //     0xb71f8c: mov             x0, NULL
    // 0xb71f90: b               #0xb71f9c
    // 0xb71f94: LoadField: r0 = r1->field_f
    //     0xb71f94: ldur            w0, [x1, #0xf]
    // 0xb71f98: DecompressPointer r0
    //     0xb71f98: add             x0, x0, HEAP, lsl #32
    // 0xb71f9c: cmp             w0, NULL
    // 0xb71fa0: b.ne            #0xb71fac
    // 0xb71fa4: r1 = 0
    //     0xb71fa4: movz            x1, #0
    // 0xb71fa8: b               #0xb71fb8
    // 0xb71fac: r1 = LoadInt32Instr(r0)
    //     0xb71fac: sbfx            x1, x0, #1, #0x1f
    //     0xb71fb0: tbz             w0, #0, #0xb71fb8
    //     0xb71fb4: ldur            x1, [x0, #7]
    // 0xb71fb8: ldur            x2, [fp, #-0x10]
    // 0xb71fbc: ldur            x0, [fp, #-0x18]
    // 0xb71fc0: stur            x1, [fp, #-0x30]
    // 0xb71fc4: r0 = Color()
    //     0xb71fc4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb71fc8: mov             x1, x0
    // 0xb71fcc: r0 = Instance_ColorSpace
    //     0xb71fcc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb71fd0: stur            x1, [fp, #-0x38]
    // 0xb71fd4: StoreField: r1->field_27 = r0
    //     0xb71fd4: stur            w0, [x1, #0x27]
    // 0xb71fd8: d0 = 1.000000
    //     0xb71fd8: fmov            d0, #1.00000000
    // 0xb71fdc: StoreField: r1->field_7 = d0
    //     0xb71fdc: stur            d0, [x1, #7]
    // 0xb71fe0: ldur            x2, [fp, #-0x20]
    // 0xb71fe4: ubfx            x2, x2, #0, #0x20
    // 0xb71fe8: and             w3, w2, #0xff
    // 0xb71fec: ubfx            x3, x3, #0, #0x20
    // 0xb71ff0: scvtf           d1, x3
    // 0xb71ff4: d2 = 255.000000
    //     0xb71ff4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb71ff8: fdiv            d3, d1, d2
    // 0xb71ffc: StoreField: r1->field_f = d3
    //     0xb71ffc: stur            d3, [x1, #0xf]
    // 0xb72000: ldur            x2, [fp, #-0x28]
    // 0xb72004: ubfx            x2, x2, #0, #0x20
    // 0xb72008: and             w3, w2, #0xff
    // 0xb7200c: ubfx            x3, x3, #0, #0x20
    // 0xb72010: scvtf           d1, x3
    // 0xb72014: fdiv            d3, d1, d2
    // 0xb72018: ArrayStore: r1[0] = d3  ; List_8
    //     0xb72018: stur            d3, [x1, #0x17]
    // 0xb7201c: ldur            x2, [fp, #-0x30]
    // 0xb72020: ubfx            x2, x2, #0, #0x20
    // 0xb72024: and             w3, w2, #0xff
    // 0xb72028: ubfx            x3, x3, #0, #0x20
    // 0xb7202c: scvtf           d1, x3
    // 0xb72030: fdiv            d3, d1, d2
    // 0xb72034: StoreField: r1->field_1f = d3
    //     0xb72034: stur            d3, [x1, #0x1f]
    // 0xb72038: r0 = ColorFilter()
    //     0xb72038: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb7203c: mov             x1, x0
    // 0xb72040: ldur            x0, [fp, #-0x38]
    // 0xb72044: stur            x1, [fp, #-0x40]
    // 0xb72048: StoreField: r1->field_7 = r0
    //     0xb72048: stur            w0, [x1, #7]
    // 0xb7204c: r0 = Instance_BlendMode
    //     0xb7204c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb72050: ldr             x0, [x0, #0xb30]
    // 0xb72054: StoreField: r1->field_b = r0
    //     0xb72054: stur            w0, [x1, #0xb]
    // 0xb72058: r2 = 1
    //     0xb72058: movz            x2, #0x1
    // 0xb7205c: StoreField: r1->field_13 = r2
    //     0xb7205c: stur            x2, [x1, #0x13]
    // 0xb72060: r0 = SvgPicture()
    //     0xb72060: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb72064: stur            x0, [fp, #-0x38]
    // 0xb72068: r16 = "home"
    //     0xb72068: add             x16, PP, #0xb, lsl #12  ; [pp+0xb470] "home"
    //     0xb7206c: ldr             x16, [x16, #0x470]
    // 0xb72070: r30 = Instance_BoxFit
    //     0xb72070: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb72074: ldr             lr, [lr, #0xb18]
    // 0xb72078: stp             lr, x16, [SP, #8]
    // 0xb7207c: ldur            x16, [fp, #-0x40]
    // 0xb72080: str             x16, [SP]
    // 0xb72084: mov             x1, x0
    // 0xb72088: r2 = "assets/images/home.svg"
    //     0xb72088: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "assets/images/home.svg"
    //     0xb7208c: ldr             x2, [x2, #0xb20]
    // 0xb72090: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb72090: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb72094: ldr             x4, [x4, #0xb38]
    // 0xb72098: r0 = SvgPicture.asset()
    //     0xb72098: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7209c: r0 = BottomNavigationBarItem()
    //     0xb7209c: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xb720a0: mov             x2, x0
    // 0xb720a4: ldur            x0, [fp, #-0x18]
    // 0xb720a8: stur            x2, [fp, #-0x40]
    // 0xb720ac: StoreField: r2->field_b = r0
    //     0xb720ac: stur            w0, [x2, #0xb]
    // 0xb720b0: r0 = "Home"
    //     0xb720b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb40] "Home"
    //     0xb720b4: ldr             x0, [x0, #0xb40]
    // 0xb720b8: StoreField: r2->field_13 = r0
    //     0xb720b8: stur            w0, [x2, #0x13]
    // 0xb720bc: ldur            x0, [fp, #-0x38]
    // 0xb720c0: StoreField: r2->field_f = r0
    //     0xb720c0: stur            w0, [x2, #0xf]
    // 0xb720c4: ldur            x0, [fp, #-0x10]
    // 0xb720c8: LoadField: r1 = r0->field_3f
    //     0xb720c8: ldur            w1, [x0, #0x3f]
    // 0xb720cc: DecompressPointer r1
    //     0xb720cc: add             x1, x1, HEAP, lsl #32
    // 0xb720d0: LoadField: r3 = r1->field_57
    //     0xb720d0: ldur            w3, [x1, #0x57]
    // 0xb720d4: DecompressPointer r3
    //     0xb720d4: add             x3, x3, HEAP, lsl #32
    // 0xb720d8: mov             x1, x3
    // 0xb720dc: r0 = value()
    //     0xb720dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb720e0: LoadField: r1 = r0->field_b
    //     0xb720e0: ldur            w1, [x0, #0xb]
    // 0xb720e4: DecompressPointer r1
    //     0xb720e4: add             x1, x1, HEAP, lsl #32
    // 0xb720e8: cmp             w1, NULL
    // 0xb720ec: b.ne            #0xb720f8
    // 0xb720f0: r0 = Null
    //     0xb720f0: mov             x0, NULL
    // 0xb720f4: b               #0xb72130
    // 0xb720f8: LoadField: r0 = r1->field_57
    //     0xb720f8: ldur            w0, [x1, #0x57]
    // 0xb720fc: DecompressPointer r0
    //     0xb720fc: add             x0, x0, HEAP, lsl #32
    // 0xb72100: cmp             w0, NULL
    // 0xb72104: b.ne            #0xb72110
    // 0xb72108: r0 = Null
    //     0xb72108: mov             x0, NULL
    // 0xb7210c: b               #0xb72130
    // 0xb72110: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72110: ldur            w1, [x0, #0x17]
    // 0xb72114: DecompressPointer r1
    //     0xb72114: add             x1, x1, HEAP, lsl #32
    // 0xb72118: cmp             w1, NULL
    // 0xb7211c: b.ne            #0xb72128
    // 0xb72120: r0 = Null
    //     0xb72120: mov             x0, NULL
    // 0xb72124: b               #0xb72130
    // 0xb72128: LoadField: r0 = r1->field_7
    //     0xb72128: ldur            w0, [x1, #7]
    // 0xb7212c: DecompressPointer r0
    //     0xb7212c: add             x0, x0, HEAP, lsl #32
    // 0xb72130: cmp             w0, NULL
    // 0xb72134: b.ne            #0xb72140
    // 0xb72138: r0 = 0
    //     0xb72138: movz            x0, #0
    // 0xb7213c: b               #0xb72150
    // 0xb72140: r1 = LoadInt32Instr(r0)
    //     0xb72140: sbfx            x1, x0, #1, #0x1f
    //     0xb72144: tbz             w0, #0, #0xb7214c
    //     0xb72148: ldur            x1, [x0, #7]
    // 0xb7214c: mov             x0, x1
    // 0xb72150: ldur            x2, [fp, #-0x10]
    // 0xb72154: stur            x0, [fp, #-0x20]
    // 0xb72158: LoadField: r1 = r2->field_3f
    //     0xb72158: ldur            w1, [x2, #0x3f]
    // 0xb7215c: DecompressPointer r1
    //     0xb7215c: add             x1, x1, HEAP, lsl #32
    // 0xb72160: LoadField: r3 = r1->field_57
    //     0xb72160: ldur            w3, [x1, #0x57]
    // 0xb72164: DecompressPointer r3
    //     0xb72164: add             x3, x3, HEAP, lsl #32
    // 0xb72168: mov             x1, x3
    // 0xb7216c: r0 = value()
    //     0xb7216c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72170: LoadField: r1 = r0->field_b
    //     0xb72170: ldur            w1, [x0, #0xb]
    // 0xb72174: DecompressPointer r1
    //     0xb72174: add             x1, x1, HEAP, lsl #32
    // 0xb72178: cmp             w1, NULL
    // 0xb7217c: b.ne            #0xb72188
    // 0xb72180: r0 = Null
    //     0xb72180: mov             x0, NULL
    // 0xb72184: b               #0xb721c0
    // 0xb72188: LoadField: r0 = r1->field_57
    //     0xb72188: ldur            w0, [x1, #0x57]
    // 0xb7218c: DecompressPointer r0
    //     0xb7218c: add             x0, x0, HEAP, lsl #32
    // 0xb72190: cmp             w0, NULL
    // 0xb72194: b.ne            #0xb721a0
    // 0xb72198: r0 = Null
    //     0xb72198: mov             x0, NULL
    // 0xb7219c: b               #0xb721c0
    // 0xb721a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb721a0: ldur            w1, [x0, #0x17]
    // 0xb721a4: DecompressPointer r1
    //     0xb721a4: add             x1, x1, HEAP, lsl #32
    // 0xb721a8: cmp             w1, NULL
    // 0xb721ac: b.ne            #0xb721b8
    // 0xb721b0: r0 = Null
    //     0xb721b0: mov             x0, NULL
    // 0xb721b4: b               #0xb721c0
    // 0xb721b8: LoadField: r0 = r1->field_b
    //     0xb721b8: ldur            w0, [x1, #0xb]
    // 0xb721bc: DecompressPointer r0
    //     0xb721bc: add             x0, x0, HEAP, lsl #32
    // 0xb721c0: cmp             w0, NULL
    // 0xb721c4: b.ne            #0xb721d0
    // 0xb721c8: r0 = 0
    //     0xb721c8: movz            x0, #0
    // 0xb721cc: b               #0xb721e0
    // 0xb721d0: r1 = LoadInt32Instr(r0)
    //     0xb721d0: sbfx            x1, x0, #1, #0x1f
    //     0xb721d4: tbz             w0, #0, #0xb721dc
    //     0xb721d8: ldur            x1, [x0, #7]
    // 0xb721dc: mov             x0, x1
    // 0xb721e0: ldur            x2, [fp, #-0x10]
    // 0xb721e4: stur            x0, [fp, #-0x28]
    // 0xb721e8: LoadField: r1 = r2->field_3f
    //     0xb721e8: ldur            w1, [x2, #0x3f]
    // 0xb721ec: DecompressPointer r1
    //     0xb721ec: add             x1, x1, HEAP, lsl #32
    // 0xb721f0: LoadField: r3 = r1->field_57
    //     0xb721f0: ldur            w3, [x1, #0x57]
    // 0xb721f4: DecompressPointer r3
    //     0xb721f4: add             x3, x3, HEAP, lsl #32
    // 0xb721f8: mov             x1, x3
    // 0xb721fc: r0 = value()
    //     0xb721fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72200: LoadField: r1 = r0->field_b
    //     0xb72200: ldur            w1, [x0, #0xb]
    // 0xb72204: DecompressPointer r1
    //     0xb72204: add             x1, x1, HEAP, lsl #32
    // 0xb72208: cmp             w1, NULL
    // 0xb7220c: b.ne            #0xb72218
    // 0xb72210: r0 = Null
    //     0xb72210: mov             x0, NULL
    // 0xb72214: b               #0xb72250
    // 0xb72218: LoadField: r0 = r1->field_57
    //     0xb72218: ldur            w0, [x1, #0x57]
    // 0xb7221c: DecompressPointer r0
    //     0xb7221c: add             x0, x0, HEAP, lsl #32
    // 0xb72220: cmp             w0, NULL
    // 0xb72224: b.ne            #0xb72230
    // 0xb72228: r0 = Null
    //     0xb72228: mov             x0, NULL
    // 0xb7222c: b               #0xb72250
    // 0xb72230: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72230: ldur            w1, [x0, #0x17]
    // 0xb72234: DecompressPointer r1
    //     0xb72234: add             x1, x1, HEAP, lsl #32
    // 0xb72238: cmp             w1, NULL
    // 0xb7223c: b.ne            #0xb72248
    // 0xb72240: r0 = Null
    //     0xb72240: mov             x0, NULL
    // 0xb72244: b               #0xb72250
    // 0xb72248: LoadField: r0 = r1->field_f
    //     0xb72248: ldur            w0, [x1, #0xf]
    // 0xb7224c: DecompressPointer r0
    //     0xb7224c: add             x0, x0, HEAP, lsl #32
    // 0xb72250: cmp             w0, NULL
    // 0xb72254: b.ne            #0xb72260
    // 0xb72258: r0 = 0
    //     0xb72258: movz            x0, #0
    // 0xb7225c: b               #0xb72270
    // 0xb72260: r1 = LoadInt32Instr(r0)
    //     0xb72260: sbfx            x1, x0, #1, #0x1f
    //     0xb72264: tbz             w0, #0, #0xb7226c
    //     0xb72268: ldur            x1, [x0, #7]
    // 0xb7226c: mov             x0, x1
    // 0xb72270: ldur            x2, [fp, #-0x10]
    // 0xb72274: stur            x0, [fp, #-0x30]
    // 0xb72278: r0 = Color()
    //     0xb72278: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb7227c: mov             x1, x0
    // 0xb72280: r0 = Instance_ColorSpace
    //     0xb72280: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb72284: stur            x1, [fp, #-0x18]
    // 0xb72288: StoreField: r1->field_27 = r0
    //     0xb72288: stur            w0, [x1, #0x27]
    // 0xb7228c: d0 = 1.000000
    //     0xb7228c: fmov            d0, #1.00000000
    // 0xb72290: StoreField: r1->field_7 = d0
    //     0xb72290: stur            d0, [x1, #7]
    // 0xb72294: ldur            x2, [fp, #-0x20]
    // 0xb72298: ubfx            x2, x2, #0, #0x20
    // 0xb7229c: and             w3, w2, #0xff
    // 0xb722a0: ubfx            x3, x3, #0, #0x20
    // 0xb722a4: scvtf           d1, x3
    // 0xb722a8: d2 = 255.000000
    //     0xb722a8: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb722ac: fdiv            d3, d1, d2
    // 0xb722b0: StoreField: r1->field_f = d3
    //     0xb722b0: stur            d3, [x1, #0xf]
    // 0xb722b4: ldur            x2, [fp, #-0x28]
    // 0xb722b8: ubfx            x2, x2, #0, #0x20
    // 0xb722bc: and             w3, w2, #0xff
    // 0xb722c0: ubfx            x3, x3, #0, #0x20
    // 0xb722c4: scvtf           d1, x3
    // 0xb722c8: fdiv            d3, d1, d2
    // 0xb722cc: ArrayStore: r1[0] = d3  ; List_8
    //     0xb722cc: stur            d3, [x1, #0x17]
    // 0xb722d0: ldur            x2, [fp, #-0x30]
    // 0xb722d4: ubfx            x2, x2, #0, #0x20
    // 0xb722d8: and             w3, w2, #0xff
    // 0xb722dc: ubfx            x3, x3, #0, #0x20
    // 0xb722e0: scvtf           d1, x3
    // 0xb722e4: fdiv            d3, d1, d2
    // 0xb722e8: StoreField: r1->field_1f = d3
    //     0xb722e8: stur            d3, [x1, #0x1f]
    // 0xb722ec: r0 = InitLateStaticField(0xe70) // [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_pages
    //     0xb722ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb722f0: ldr             x0, [x0, #0x1ce0]
    //     0xb722f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb722f8: cmp             w0, w16
    //     0xb722fc: b.ne            #0xb7230c
    //     0xb72300: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db30] Field <_MainPageState@1595367332._pages@1595367332>: static late final (offset: 0xe70)
    //     0xb72304: ldr             x2, [x2, #0xb30]
    //     0xb72308: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb7230c: mov             x3, x0
    // 0xb72310: stur            x3, [fp, #-0x48]
    // 0xb72314: LoadField: r0 = r3->field_b
    //     0xb72314: ldur            w0, [x3, #0xb]
    // 0xb72318: r1 = LoadInt32Instr(r0)
    //     0xb72318: sbfx            x1, x0, #1, #0x1f
    // 0xb7231c: mov             x0, x1
    // 0xb72320: r1 = 1
    //     0xb72320: movz            x1, #0x1
    // 0xb72324: cmp             x1, x0
    // 0xb72328: b.hs            #0xb731c4
    // 0xb7232c: LoadField: r0 = r3->field_f
    //     0xb7232c: ldur            w0, [x3, #0xf]
    // 0xb72330: DecompressPointer r0
    //     0xb72330: add             x0, x0, HEAP, lsl #32
    // 0xb72334: LoadField: r4 = r0->field_13
    //     0xb72334: ldur            w4, [x0, #0x13]
    // 0xb72338: DecompressPointer r4
    //     0xb72338: add             x4, x4, HEAP, lsl #32
    // 0xb7233c: mov             x0, x4
    // 0xb72340: stur            x4, [fp, #-0x38]
    // 0xb72344: r2 = Null
    //     0xb72344: mov             x2, NULL
    // 0xb72348: r1 = Null
    //     0xb72348: mov             x1, NULL
    // 0xb7234c: r4 = 60
    //     0xb7234c: movz            x4, #0x3c
    // 0xb72350: branchIfSmi(r0, 0xb7235c)
    //     0xb72350: tbz             w0, #0, #0xb7235c
    // 0xb72354: r4 = LoadClassIdInstr(r0)
    //     0xb72354: ldur            x4, [x0, #-1]
    //     0xb72358: ubfx            x4, x4, #0xc, #0x14
    // 0xb7235c: r17 = 4563
    //     0xb7235c: movz            x17, #0x11d3
    // 0xb72360: cmp             x4, x17
    // 0xb72364: b.eq            #0xb7237c
    // 0xb72368: r8 = OrdersView
    //     0xb72368: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0xb7236c: ldr             x8, [x8, #0xb38]
    // 0xb72370: r3 = Null
    //     0xb72370: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db40] Null
    //     0xb72374: ldr             x3, [x3, #0xb40]
    // 0xb72378: r0 = DefaultTypeTest()
    //     0xb72378: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xb7237c: ldur            x1, [fp, #-0x38]
    // 0xb72380: r0 = controller()
    //     0xb72380: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb72384: LoadField: r1 = r0->field_5b
    //     0xb72384: ldur            w1, [x0, #0x5b]
    // 0xb72388: DecompressPointer r1
    //     0xb72388: add             x1, x1, HEAP, lsl #32
    // 0xb7238c: r0 = value()
    //     0xb7238c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72390: ldur            x3, [fp, #-0x48]
    // 0xb72394: LoadField: r0 = r3->field_b
    //     0xb72394: ldur            w0, [x3, #0xb]
    // 0xb72398: r1 = LoadInt32Instr(r0)
    //     0xb72398: sbfx            x1, x0, #1, #0x1f
    // 0xb7239c: mov             x0, x1
    // 0xb723a0: r1 = 1
    //     0xb723a0: movz            x1, #0x1
    // 0xb723a4: cmp             x1, x0
    // 0xb723a8: b.hs            #0xb731c8
    // 0xb723ac: LoadField: r0 = r3->field_f
    //     0xb723ac: ldur            w0, [x3, #0xf]
    // 0xb723b0: DecompressPointer r0
    //     0xb723b0: add             x0, x0, HEAP, lsl #32
    // 0xb723b4: LoadField: r4 = r0->field_13
    //     0xb723b4: ldur            w4, [x0, #0x13]
    // 0xb723b8: DecompressPointer r4
    //     0xb723b8: add             x4, x4, HEAP, lsl #32
    // 0xb723bc: mov             x0, x4
    // 0xb723c0: stur            x4, [fp, #-0x38]
    // 0xb723c4: r2 = Null
    //     0xb723c4: mov             x2, NULL
    // 0xb723c8: r1 = Null
    //     0xb723c8: mov             x1, NULL
    // 0xb723cc: r4 = 60
    //     0xb723cc: movz            x4, #0x3c
    // 0xb723d0: branchIfSmi(r0, 0xb723dc)
    //     0xb723d0: tbz             w0, #0, #0xb723dc
    // 0xb723d4: r4 = LoadClassIdInstr(r0)
    //     0xb723d4: ldur            x4, [x0, #-1]
    //     0xb723d8: ubfx            x4, x4, #0xc, #0x14
    // 0xb723dc: r17 = 4563
    //     0xb723dc: movz            x17, #0x11d3
    // 0xb723e0: cmp             x4, x17
    // 0xb723e4: b.eq            #0xb723fc
    // 0xb723e8: r8 = OrdersView
    //     0xb723e8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0xb723ec: ldr             x8, [x8, #0xb38]
    // 0xb723f0: r3 = Null
    //     0xb723f0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db50] Null
    //     0xb723f4: ldr             x3, [x3, #0xb50]
    // 0xb723f8: r0 = DefaultTypeTest()
    //     0xb723f8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xb723fc: ldur            x1, [fp, #-0x38]
    // 0xb72400: r0 = controller()
    //     0xb72400: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb72404: LoadField: r1 = r0->field_5b
    //     0xb72404: ldur            w1, [x0, #0x5b]
    // 0xb72408: DecompressPointer r1
    //     0xb72408: add             x1, x1, HEAP, lsl #32
    // 0xb7240c: r0 = value()
    //     0xb7240c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72410: ldur            x2, [fp, #-0x10]
    // 0xb72414: LoadField: r1 = r2->field_f
    //     0xb72414: ldur            w1, [x2, #0xf]
    // 0xb72418: DecompressPointer r1
    //     0xb72418: add             x1, x1, HEAP, lsl #32
    // 0xb7241c: cmp             w1, NULL
    // 0xb72420: b.eq            #0xb731cc
    // 0xb72424: r0 = of()
    //     0xb72424: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb72428: LoadField: r1 = r0->field_87
    //     0xb72428: ldur            w1, [x0, #0x87]
    // 0xb7242c: DecompressPointer r1
    //     0xb7242c: add             x1, x1, HEAP, lsl #32
    // 0xb72430: LoadField: r0 = r1->field_27
    //     0xb72430: ldur            w0, [x1, #0x27]
    // 0xb72434: DecompressPointer r0
    //     0xb72434: add             x0, x0, HEAP, lsl #32
    // 0xb72438: r16 = Instance_Color
    //     0xb72438: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7243c: str             x16, [SP]
    // 0xb72440: mov             x1, x0
    // 0xb72444: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb72444: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb72448: ldr             x4, [x4, #0xf40]
    // 0xb7244c: r0 = copyWith()
    //     0xb7244c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb72450: stur            x0, [fp, #-0x38]
    // 0xb72454: r0 = Text()
    //     0xb72454: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb72458: mov             x1, x0
    // 0xb7245c: r0 = ""
    //     0xb7245c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb72460: stur            x1, [fp, #-0x50]
    // 0xb72464: StoreField: r1->field_b = r0
    //     0xb72464: stur            w0, [x1, #0xb]
    // 0xb72468: ldur            x2, [fp, #-0x38]
    // 0xb7246c: StoreField: r1->field_13 = r2
    //     0xb7246c: stur            w2, [x1, #0x13]
    // 0xb72470: r0 = SvgPicture()
    //     0xb72470: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb72474: stur            x0, [fp, #-0x38]
    // 0xb72478: r16 = "user"
    //     0xb72478: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb70] "user"
    //     0xb7247c: ldr             x16, [x16, #0xb70]
    // 0xb72480: r30 = Instance_BoxFit
    //     0xb72480: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb72484: ldr             lr, [lr, #0xb18]
    // 0xb72488: stp             lr, x16, [SP]
    // 0xb7248c: mov             x1, x0
    // 0xb72490: r2 = "assets/images/user.svg"
    //     0xb72490: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb78] "assets/images/user.svg"
    //     0xb72494: ldr             x2, [x2, #0xb78]
    // 0xb72498: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb72498: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb7249c: ldr             x4, [x4, #0xb28]
    // 0xb724a0: r0 = SvgPicture.asset()
    //     0xb724a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb724a4: r0 = Badge()
    //     0xb724a4: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0xb724a8: mov             x2, x0
    // 0xb724ac: ldur            x0, [fp, #-0x18]
    // 0xb724b0: stur            x2, [fp, #-0x58]
    // 0xb724b4: StoreField: r2->field_b = r0
    //     0xb724b4: stur            w0, [x2, #0xb]
    // 0xb724b8: ldur            x0, [fp, #-0x50]
    // 0xb724bc: StoreField: r2->field_27 = r0
    //     0xb724bc: stur            w0, [x2, #0x27]
    // 0xb724c0: r0 = false
    //     0xb724c0: add             x0, NULL, #0x30  ; false
    // 0xb724c4: StoreField: r2->field_2b = r0
    //     0xb724c4: stur            w0, [x2, #0x2b]
    // 0xb724c8: ldur            x1, [fp, #-0x38]
    // 0xb724cc: StoreField: r2->field_2f = r1
    //     0xb724cc: stur            w1, [x2, #0x2f]
    // 0xb724d0: ldur            x3, [fp, #-0x10]
    // 0xb724d4: LoadField: r1 = r3->field_3f
    //     0xb724d4: ldur            w1, [x3, #0x3f]
    // 0xb724d8: DecompressPointer r1
    //     0xb724d8: add             x1, x1, HEAP, lsl #32
    // 0xb724dc: LoadField: r4 = r1->field_57
    //     0xb724dc: ldur            w4, [x1, #0x57]
    // 0xb724e0: DecompressPointer r4
    //     0xb724e0: add             x4, x4, HEAP, lsl #32
    // 0xb724e4: mov             x1, x4
    // 0xb724e8: r0 = value()
    //     0xb724e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb724ec: LoadField: r1 = r0->field_b
    //     0xb724ec: ldur            w1, [x0, #0xb]
    // 0xb724f0: DecompressPointer r1
    //     0xb724f0: add             x1, x1, HEAP, lsl #32
    // 0xb724f4: cmp             w1, NULL
    // 0xb724f8: b.ne            #0xb72504
    // 0xb724fc: r0 = Null
    //     0xb724fc: mov             x0, NULL
    // 0xb72500: b               #0xb7253c
    // 0xb72504: LoadField: r0 = r1->field_57
    //     0xb72504: ldur            w0, [x1, #0x57]
    // 0xb72508: DecompressPointer r0
    //     0xb72508: add             x0, x0, HEAP, lsl #32
    // 0xb7250c: cmp             w0, NULL
    // 0xb72510: b.ne            #0xb7251c
    // 0xb72514: r0 = Null
    //     0xb72514: mov             x0, NULL
    // 0xb72518: b               #0xb7253c
    // 0xb7251c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb7251c: ldur            w1, [x0, #0x17]
    // 0xb72520: DecompressPointer r1
    //     0xb72520: add             x1, x1, HEAP, lsl #32
    // 0xb72524: cmp             w1, NULL
    // 0xb72528: b.ne            #0xb72534
    // 0xb7252c: r0 = Null
    //     0xb7252c: mov             x0, NULL
    // 0xb72530: b               #0xb7253c
    // 0xb72534: LoadField: r0 = r1->field_7
    //     0xb72534: ldur            w0, [x1, #7]
    // 0xb72538: DecompressPointer r0
    //     0xb72538: add             x0, x0, HEAP, lsl #32
    // 0xb7253c: cmp             w0, NULL
    // 0xb72540: b.ne            #0xb7254c
    // 0xb72544: r0 = 0
    //     0xb72544: movz            x0, #0
    // 0xb72548: b               #0xb7255c
    // 0xb7254c: r1 = LoadInt32Instr(r0)
    //     0xb7254c: sbfx            x1, x0, #1, #0x1f
    //     0xb72550: tbz             w0, #0, #0xb72558
    //     0xb72554: ldur            x1, [x0, #7]
    // 0xb72558: mov             x0, x1
    // 0xb7255c: ldur            x2, [fp, #-0x10]
    // 0xb72560: stur            x0, [fp, #-0x20]
    // 0xb72564: LoadField: r1 = r2->field_3f
    //     0xb72564: ldur            w1, [x2, #0x3f]
    // 0xb72568: DecompressPointer r1
    //     0xb72568: add             x1, x1, HEAP, lsl #32
    // 0xb7256c: LoadField: r3 = r1->field_57
    //     0xb7256c: ldur            w3, [x1, #0x57]
    // 0xb72570: DecompressPointer r3
    //     0xb72570: add             x3, x3, HEAP, lsl #32
    // 0xb72574: mov             x1, x3
    // 0xb72578: r0 = value()
    //     0xb72578: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb7257c: LoadField: r1 = r0->field_b
    //     0xb7257c: ldur            w1, [x0, #0xb]
    // 0xb72580: DecompressPointer r1
    //     0xb72580: add             x1, x1, HEAP, lsl #32
    // 0xb72584: cmp             w1, NULL
    // 0xb72588: b.ne            #0xb72594
    // 0xb7258c: r0 = Null
    //     0xb7258c: mov             x0, NULL
    // 0xb72590: b               #0xb725cc
    // 0xb72594: LoadField: r0 = r1->field_57
    //     0xb72594: ldur            w0, [x1, #0x57]
    // 0xb72598: DecompressPointer r0
    //     0xb72598: add             x0, x0, HEAP, lsl #32
    // 0xb7259c: cmp             w0, NULL
    // 0xb725a0: b.ne            #0xb725ac
    // 0xb725a4: r0 = Null
    //     0xb725a4: mov             x0, NULL
    // 0xb725a8: b               #0xb725cc
    // 0xb725ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb725ac: ldur            w1, [x0, #0x17]
    // 0xb725b0: DecompressPointer r1
    //     0xb725b0: add             x1, x1, HEAP, lsl #32
    // 0xb725b4: cmp             w1, NULL
    // 0xb725b8: b.ne            #0xb725c4
    // 0xb725bc: r0 = Null
    //     0xb725bc: mov             x0, NULL
    // 0xb725c0: b               #0xb725cc
    // 0xb725c4: LoadField: r0 = r1->field_b
    //     0xb725c4: ldur            w0, [x1, #0xb]
    // 0xb725c8: DecompressPointer r0
    //     0xb725c8: add             x0, x0, HEAP, lsl #32
    // 0xb725cc: cmp             w0, NULL
    // 0xb725d0: b.ne            #0xb725dc
    // 0xb725d4: r0 = 0
    //     0xb725d4: movz            x0, #0
    // 0xb725d8: b               #0xb725ec
    // 0xb725dc: r1 = LoadInt32Instr(r0)
    //     0xb725dc: sbfx            x1, x0, #1, #0x1f
    //     0xb725e0: tbz             w0, #0, #0xb725e8
    //     0xb725e4: ldur            x1, [x0, #7]
    // 0xb725e8: mov             x0, x1
    // 0xb725ec: ldur            x2, [fp, #-0x10]
    // 0xb725f0: stur            x0, [fp, #-0x28]
    // 0xb725f4: LoadField: r1 = r2->field_3f
    //     0xb725f4: ldur            w1, [x2, #0x3f]
    // 0xb725f8: DecompressPointer r1
    //     0xb725f8: add             x1, x1, HEAP, lsl #32
    // 0xb725fc: LoadField: r3 = r1->field_57
    //     0xb725fc: ldur            w3, [x1, #0x57]
    // 0xb72600: DecompressPointer r3
    //     0xb72600: add             x3, x3, HEAP, lsl #32
    // 0xb72604: mov             x1, x3
    // 0xb72608: r0 = value()
    //     0xb72608: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb7260c: LoadField: r1 = r0->field_b
    //     0xb7260c: ldur            w1, [x0, #0xb]
    // 0xb72610: DecompressPointer r1
    //     0xb72610: add             x1, x1, HEAP, lsl #32
    // 0xb72614: cmp             w1, NULL
    // 0xb72618: b.ne            #0xb72624
    // 0xb7261c: r0 = Null
    //     0xb7261c: mov             x0, NULL
    // 0xb72620: b               #0xb7265c
    // 0xb72624: LoadField: r0 = r1->field_57
    //     0xb72624: ldur            w0, [x1, #0x57]
    // 0xb72628: DecompressPointer r0
    //     0xb72628: add             x0, x0, HEAP, lsl #32
    // 0xb7262c: cmp             w0, NULL
    // 0xb72630: b.ne            #0xb7263c
    // 0xb72634: r0 = Null
    //     0xb72634: mov             x0, NULL
    // 0xb72638: b               #0xb7265c
    // 0xb7263c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb7263c: ldur            w1, [x0, #0x17]
    // 0xb72640: DecompressPointer r1
    //     0xb72640: add             x1, x1, HEAP, lsl #32
    // 0xb72644: cmp             w1, NULL
    // 0xb72648: b.ne            #0xb72654
    // 0xb7264c: r0 = Null
    //     0xb7264c: mov             x0, NULL
    // 0xb72650: b               #0xb7265c
    // 0xb72654: LoadField: r0 = r1->field_f
    //     0xb72654: ldur            w0, [x1, #0xf]
    // 0xb72658: DecompressPointer r0
    //     0xb72658: add             x0, x0, HEAP, lsl #32
    // 0xb7265c: cmp             w0, NULL
    // 0xb72660: b.ne            #0xb7266c
    // 0xb72664: r1 = 0
    //     0xb72664: movz            x1, #0
    // 0xb72668: b               #0xb72678
    // 0xb7266c: r1 = LoadInt32Instr(r0)
    //     0xb7266c: sbfx            x1, x0, #1, #0x1f
    //     0xb72670: tbz             w0, #0, #0xb72678
    //     0xb72674: ldur            x1, [x0, #7]
    // 0xb72678: ldur            x2, [fp, #-0x10]
    // 0xb7267c: ldur            x0, [fp, #-0x48]
    // 0xb72680: stur            x1, [fp, #-0x30]
    // 0xb72684: r0 = Color()
    //     0xb72684: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb72688: mov             x4, x0
    // 0xb7268c: r3 = Instance_ColorSpace
    //     0xb7268c: ldr             x3, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb72690: stur            x4, [fp, #-0x38]
    // 0xb72694: StoreField: r4->field_27 = r3
    //     0xb72694: stur            w3, [x4, #0x27]
    // 0xb72698: d0 = 1.000000
    //     0xb72698: fmov            d0, #1.00000000
    // 0xb7269c: StoreField: r4->field_7 = d0
    //     0xb7269c: stur            d0, [x4, #7]
    // 0xb726a0: ldur            x0, [fp, #-0x20]
    // 0xb726a4: ubfx            x0, x0, #0, #0x20
    // 0xb726a8: and             w1, w0, #0xff
    // 0xb726ac: ubfx            x1, x1, #0, #0x20
    // 0xb726b0: scvtf           d1, x1
    // 0xb726b4: d2 = 255.000000
    //     0xb726b4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb726b8: fdiv            d3, d1, d2
    // 0xb726bc: StoreField: r4->field_f = d3
    //     0xb726bc: stur            d3, [x4, #0xf]
    // 0xb726c0: ldur            x0, [fp, #-0x28]
    // 0xb726c4: ubfx            x0, x0, #0, #0x20
    // 0xb726c8: and             w1, w0, #0xff
    // 0xb726cc: ubfx            x1, x1, #0, #0x20
    // 0xb726d0: scvtf           d1, x1
    // 0xb726d4: fdiv            d3, d1, d2
    // 0xb726d8: ArrayStore: r4[0] = d3  ; List_8
    //     0xb726d8: stur            d3, [x4, #0x17]
    // 0xb726dc: ldur            x0, [fp, #-0x30]
    // 0xb726e0: ubfx            x0, x0, #0, #0x20
    // 0xb726e4: and             w1, w0, #0xff
    // 0xb726e8: ubfx            x1, x1, #0, #0x20
    // 0xb726ec: scvtf           d1, x1
    // 0xb726f0: fdiv            d3, d1, d2
    // 0xb726f4: StoreField: r4->field_1f = d3
    //     0xb726f4: stur            d3, [x4, #0x1f]
    // 0xb726f8: ldur            x5, [fp, #-0x48]
    // 0xb726fc: LoadField: r0 = r5->field_b
    //     0xb726fc: ldur            w0, [x5, #0xb]
    // 0xb72700: r1 = LoadInt32Instr(r0)
    //     0xb72700: sbfx            x1, x0, #1, #0x1f
    // 0xb72704: mov             x0, x1
    // 0xb72708: r1 = 1
    //     0xb72708: movz            x1, #0x1
    // 0xb7270c: cmp             x1, x0
    // 0xb72710: b.hs            #0xb731d0
    // 0xb72714: LoadField: r0 = r5->field_f
    //     0xb72714: ldur            w0, [x5, #0xf]
    // 0xb72718: DecompressPointer r0
    //     0xb72718: add             x0, x0, HEAP, lsl #32
    // 0xb7271c: LoadField: r6 = r0->field_13
    //     0xb7271c: ldur            w6, [x0, #0x13]
    // 0xb72720: DecompressPointer r6
    //     0xb72720: add             x6, x6, HEAP, lsl #32
    // 0xb72724: mov             x0, x6
    // 0xb72728: stur            x6, [fp, #-0x18]
    // 0xb7272c: r2 = Null
    //     0xb7272c: mov             x2, NULL
    // 0xb72730: r1 = Null
    //     0xb72730: mov             x1, NULL
    // 0xb72734: r4 = 60
    //     0xb72734: movz            x4, #0x3c
    // 0xb72738: branchIfSmi(r0, 0xb72744)
    //     0xb72738: tbz             w0, #0, #0xb72744
    // 0xb7273c: r4 = LoadClassIdInstr(r0)
    //     0xb7273c: ldur            x4, [x0, #-1]
    //     0xb72740: ubfx            x4, x4, #0xc, #0x14
    // 0xb72744: r17 = 4563
    //     0xb72744: movz            x17, #0x11d3
    // 0xb72748: cmp             x4, x17
    // 0xb7274c: b.eq            #0xb72764
    // 0xb72750: r8 = OrdersView
    //     0xb72750: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0xb72754: ldr             x8, [x8, #0xb38]
    // 0xb72758: r3 = Null
    //     0xb72758: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db60] Null
    //     0xb7275c: ldr             x3, [x3, #0xb60]
    // 0xb72760: r0 = DefaultTypeTest()
    //     0xb72760: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xb72764: ldur            x1, [fp, #-0x18]
    // 0xb72768: r0 = controller()
    //     0xb72768: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb7276c: LoadField: r1 = r0->field_5b
    //     0xb7276c: ldur            w1, [x0, #0x5b]
    // 0xb72770: DecompressPointer r1
    //     0xb72770: add             x1, x1, HEAP, lsl #32
    // 0xb72774: r0 = value()
    //     0xb72774: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72778: ldur            x2, [fp, #-0x48]
    // 0xb7277c: LoadField: r0 = r2->field_b
    //     0xb7277c: ldur            w0, [x2, #0xb]
    // 0xb72780: r1 = LoadInt32Instr(r0)
    //     0xb72780: sbfx            x1, x0, #1, #0x1f
    // 0xb72784: mov             x0, x1
    // 0xb72788: r1 = 1
    //     0xb72788: movz            x1, #0x1
    // 0xb7278c: cmp             x1, x0
    // 0xb72790: b.hs            #0xb731d4
    // 0xb72794: LoadField: r0 = r2->field_f
    //     0xb72794: ldur            w0, [x2, #0xf]
    // 0xb72798: DecompressPointer r0
    //     0xb72798: add             x0, x0, HEAP, lsl #32
    // 0xb7279c: LoadField: r3 = r0->field_13
    //     0xb7279c: ldur            w3, [x0, #0x13]
    // 0xb727a0: DecompressPointer r3
    //     0xb727a0: add             x3, x3, HEAP, lsl #32
    // 0xb727a4: mov             x0, x3
    // 0xb727a8: stur            x3, [fp, #-0x18]
    // 0xb727ac: r2 = Null
    //     0xb727ac: mov             x2, NULL
    // 0xb727b0: r1 = Null
    //     0xb727b0: mov             x1, NULL
    // 0xb727b4: r4 = 60
    //     0xb727b4: movz            x4, #0x3c
    // 0xb727b8: branchIfSmi(r0, 0xb727c4)
    //     0xb727b8: tbz             w0, #0, #0xb727c4
    // 0xb727bc: r4 = LoadClassIdInstr(r0)
    //     0xb727bc: ldur            x4, [x0, #-1]
    //     0xb727c0: ubfx            x4, x4, #0xc, #0x14
    // 0xb727c4: r17 = 4563
    //     0xb727c4: movz            x17, #0x11d3
    // 0xb727c8: cmp             x4, x17
    // 0xb727cc: b.eq            #0xb727e4
    // 0xb727d0: r8 = OrdersView
    //     0xb727d0: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db38] Type: OrdersView
    //     0xb727d4: ldr             x8, [x8, #0xb38]
    // 0xb727d8: r3 = Null
    //     0xb727d8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db70] Null
    //     0xb727dc: ldr             x3, [x3, #0xb70]
    // 0xb727e0: r0 = DefaultTypeTest()
    //     0xb727e0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xb727e4: ldur            x1, [fp, #-0x18]
    // 0xb727e8: r0 = controller()
    //     0xb727e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb727ec: LoadField: r1 = r0->field_5b
    //     0xb727ec: ldur            w1, [x0, #0x5b]
    // 0xb727f0: DecompressPointer r1
    //     0xb727f0: add             x1, x1, HEAP, lsl #32
    // 0xb727f4: r0 = value()
    //     0xb727f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb727f8: ldur            x2, [fp, #-0x10]
    // 0xb727fc: LoadField: r1 = r2->field_f
    //     0xb727fc: ldur            w1, [x2, #0xf]
    // 0xb72800: DecompressPointer r1
    //     0xb72800: add             x1, x1, HEAP, lsl #32
    // 0xb72804: cmp             w1, NULL
    // 0xb72808: b.eq            #0xb731d8
    // 0xb7280c: r0 = of()
    //     0xb7280c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb72810: LoadField: r1 = r0->field_87
    //     0xb72810: ldur            w1, [x0, #0x87]
    // 0xb72814: DecompressPointer r1
    //     0xb72814: add             x1, x1, HEAP, lsl #32
    // 0xb72818: LoadField: r0 = r1->field_27
    //     0xb72818: ldur            w0, [x1, #0x27]
    // 0xb7281c: DecompressPointer r0
    //     0xb7281c: add             x0, x0, HEAP, lsl #32
    // 0xb72820: r16 = Instance_Color
    //     0xb72820: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb72824: str             x16, [SP]
    // 0xb72828: mov             x1, x0
    // 0xb7282c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb7282c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb72830: ldr             x4, [x4, #0xf40]
    // 0xb72834: r0 = copyWith()
    //     0xb72834: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb72838: stur            x0, [fp, #-0x18]
    // 0xb7283c: r0 = Text()
    //     0xb7283c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb72840: mov             x2, x0
    // 0xb72844: r0 = ""
    //     0xb72844: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb72848: stur            x2, [fp, #-0x48]
    // 0xb7284c: StoreField: r2->field_b = r0
    //     0xb7284c: stur            w0, [x2, #0xb]
    // 0xb72850: ldur            x0, [fp, #-0x18]
    // 0xb72854: StoreField: r2->field_13 = r0
    //     0xb72854: stur            w0, [x2, #0x13]
    // 0xb72858: ldur            x0, [fp, #-0x10]
    // 0xb7285c: LoadField: r1 = r0->field_3f
    //     0xb7285c: ldur            w1, [x0, #0x3f]
    // 0xb72860: DecompressPointer r1
    //     0xb72860: add             x1, x1, HEAP, lsl #32
    // 0xb72864: LoadField: r3 = r1->field_57
    //     0xb72864: ldur            w3, [x1, #0x57]
    // 0xb72868: DecompressPointer r3
    //     0xb72868: add             x3, x3, HEAP, lsl #32
    // 0xb7286c: mov             x1, x3
    // 0xb72870: r0 = value()
    //     0xb72870: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72874: LoadField: r1 = r0->field_b
    //     0xb72874: ldur            w1, [x0, #0xb]
    // 0xb72878: DecompressPointer r1
    //     0xb72878: add             x1, x1, HEAP, lsl #32
    // 0xb7287c: cmp             w1, NULL
    // 0xb72880: b.ne            #0xb7288c
    // 0xb72884: r0 = Null
    //     0xb72884: mov             x0, NULL
    // 0xb72888: b               #0xb728c4
    // 0xb7288c: LoadField: r0 = r1->field_57
    //     0xb7288c: ldur            w0, [x1, #0x57]
    // 0xb72890: DecompressPointer r0
    //     0xb72890: add             x0, x0, HEAP, lsl #32
    // 0xb72894: cmp             w0, NULL
    // 0xb72898: b.ne            #0xb728a4
    // 0xb7289c: r0 = Null
    //     0xb7289c: mov             x0, NULL
    // 0xb728a0: b               #0xb728c4
    // 0xb728a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb728a4: ldur            w1, [x0, #0x17]
    // 0xb728a8: DecompressPointer r1
    //     0xb728a8: add             x1, x1, HEAP, lsl #32
    // 0xb728ac: cmp             w1, NULL
    // 0xb728b0: b.ne            #0xb728bc
    // 0xb728b4: r0 = Null
    //     0xb728b4: mov             x0, NULL
    // 0xb728b8: b               #0xb728c4
    // 0xb728bc: LoadField: r0 = r1->field_7
    //     0xb728bc: ldur            w0, [x1, #7]
    // 0xb728c0: DecompressPointer r0
    //     0xb728c0: add             x0, x0, HEAP, lsl #32
    // 0xb728c4: cmp             w0, NULL
    // 0xb728c8: b.ne            #0xb728d4
    // 0xb728cc: r0 = 0
    //     0xb728cc: movz            x0, #0
    // 0xb728d0: b               #0xb728e4
    // 0xb728d4: r1 = LoadInt32Instr(r0)
    //     0xb728d4: sbfx            x1, x0, #1, #0x1f
    //     0xb728d8: tbz             w0, #0, #0xb728e0
    //     0xb728dc: ldur            x1, [x0, #7]
    // 0xb728e0: mov             x0, x1
    // 0xb728e4: ldur            x2, [fp, #-0x10]
    // 0xb728e8: stur            x0, [fp, #-0x20]
    // 0xb728ec: LoadField: r1 = r2->field_3f
    //     0xb728ec: ldur            w1, [x2, #0x3f]
    // 0xb728f0: DecompressPointer r1
    //     0xb728f0: add             x1, x1, HEAP, lsl #32
    // 0xb728f4: LoadField: r3 = r1->field_57
    //     0xb728f4: ldur            w3, [x1, #0x57]
    // 0xb728f8: DecompressPointer r3
    //     0xb728f8: add             x3, x3, HEAP, lsl #32
    // 0xb728fc: mov             x1, x3
    // 0xb72900: r0 = value()
    //     0xb72900: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72904: LoadField: r1 = r0->field_b
    //     0xb72904: ldur            w1, [x0, #0xb]
    // 0xb72908: DecompressPointer r1
    //     0xb72908: add             x1, x1, HEAP, lsl #32
    // 0xb7290c: cmp             w1, NULL
    // 0xb72910: b.ne            #0xb7291c
    // 0xb72914: r0 = Null
    //     0xb72914: mov             x0, NULL
    // 0xb72918: b               #0xb72954
    // 0xb7291c: LoadField: r0 = r1->field_57
    //     0xb7291c: ldur            w0, [x1, #0x57]
    // 0xb72920: DecompressPointer r0
    //     0xb72920: add             x0, x0, HEAP, lsl #32
    // 0xb72924: cmp             w0, NULL
    // 0xb72928: b.ne            #0xb72934
    // 0xb7292c: r0 = Null
    //     0xb7292c: mov             x0, NULL
    // 0xb72930: b               #0xb72954
    // 0xb72934: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72934: ldur            w1, [x0, #0x17]
    // 0xb72938: DecompressPointer r1
    //     0xb72938: add             x1, x1, HEAP, lsl #32
    // 0xb7293c: cmp             w1, NULL
    // 0xb72940: b.ne            #0xb7294c
    // 0xb72944: r0 = Null
    //     0xb72944: mov             x0, NULL
    // 0xb72948: b               #0xb72954
    // 0xb7294c: LoadField: r0 = r1->field_b
    //     0xb7294c: ldur            w0, [x1, #0xb]
    // 0xb72950: DecompressPointer r0
    //     0xb72950: add             x0, x0, HEAP, lsl #32
    // 0xb72954: cmp             w0, NULL
    // 0xb72958: b.ne            #0xb72964
    // 0xb7295c: r0 = 0
    //     0xb7295c: movz            x0, #0
    // 0xb72960: b               #0xb72974
    // 0xb72964: r1 = LoadInt32Instr(r0)
    //     0xb72964: sbfx            x1, x0, #1, #0x1f
    //     0xb72968: tbz             w0, #0, #0xb72970
    //     0xb7296c: ldur            x1, [x0, #7]
    // 0xb72970: mov             x0, x1
    // 0xb72974: ldur            x2, [fp, #-0x10]
    // 0xb72978: stur            x0, [fp, #-0x28]
    // 0xb7297c: LoadField: r1 = r2->field_3f
    //     0xb7297c: ldur            w1, [x2, #0x3f]
    // 0xb72980: DecompressPointer r1
    //     0xb72980: add             x1, x1, HEAP, lsl #32
    // 0xb72984: LoadField: r3 = r1->field_57
    //     0xb72984: ldur            w3, [x1, #0x57]
    // 0xb72988: DecompressPointer r3
    //     0xb72988: add             x3, x3, HEAP, lsl #32
    // 0xb7298c: mov             x1, x3
    // 0xb72990: r0 = value()
    //     0xb72990: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72994: LoadField: r1 = r0->field_b
    //     0xb72994: ldur            w1, [x0, #0xb]
    // 0xb72998: DecompressPointer r1
    //     0xb72998: add             x1, x1, HEAP, lsl #32
    // 0xb7299c: cmp             w1, NULL
    // 0xb729a0: b.ne            #0xb729ac
    // 0xb729a4: r0 = Null
    //     0xb729a4: mov             x0, NULL
    // 0xb729a8: b               #0xb729e4
    // 0xb729ac: LoadField: r0 = r1->field_57
    //     0xb729ac: ldur            w0, [x1, #0x57]
    // 0xb729b0: DecompressPointer r0
    //     0xb729b0: add             x0, x0, HEAP, lsl #32
    // 0xb729b4: cmp             w0, NULL
    // 0xb729b8: b.ne            #0xb729c4
    // 0xb729bc: r0 = Null
    //     0xb729bc: mov             x0, NULL
    // 0xb729c0: b               #0xb729e4
    // 0xb729c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb729c4: ldur            w1, [x0, #0x17]
    // 0xb729c8: DecompressPointer r1
    //     0xb729c8: add             x1, x1, HEAP, lsl #32
    // 0xb729cc: cmp             w1, NULL
    // 0xb729d0: b.ne            #0xb729dc
    // 0xb729d4: r0 = Null
    //     0xb729d4: mov             x0, NULL
    // 0xb729d8: b               #0xb729e4
    // 0xb729dc: LoadField: r0 = r1->field_f
    //     0xb729dc: ldur            w0, [x1, #0xf]
    // 0xb729e0: DecompressPointer r0
    //     0xb729e0: add             x0, x0, HEAP, lsl #32
    // 0xb729e4: cmp             w0, NULL
    // 0xb729e8: b.ne            #0xb729f4
    // 0xb729ec: r4 = 0
    //     0xb729ec: movz            x4, #0
    // 0xb729f0: b               #0xb72a04
    // 0xb729f4: r1 = LoadInt32Instr(r0)
    //     0xb729f4: sbfx            x1, x0, #1, #0x1f
    //     0xb729f8: tbz             w0, #0, #0xb72a00
    //     0xb729fc: ldur            x1, [x0, #7]
    // 0xb72a00: mov             x4, x1
    // 0xb72a04: ldur            x2, [fp, #-0x10]
    // 0xb72a08: ldur            x3, [fp, #-0x58]
    // 0xb72a0c: ldur            x1, [fp, #-0x38]
    // 0xb72a10: ldur            x0, [fp, #-0x48]
    // 0xb72a14: stur            x4, [fp, #-0x30]
    // 0xb72a18: r0 = Color()
    //     0xb72a18: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb72a1c: mov             x1, x0
    // 0xb72a20: r0 = Instance_ColorSpace
    //     0xb72a20: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb72a24: stur            x1, [fp, #-0x18]
    // 0xb72a28: StoreField: r1->field_27 = r0
    //     0xb72a28: stur            w0, [x1, #0x27]
    // 0xb72a2c: d0 = 1.000000
    //     0xb72a2c: fmov            d0, #1.00000000
    // 0xb72a30: StoreField: r1->field_7 = d0
    //     0xb72a30: stur            d0, [x1, #7]
    // 0xb72a34: ldur            x2, [fp, #-0x20]
    // 0xb72a38: ubfx            x2, x2, #0, #0x20
    // 0xb72a3c: and             w3, w2, #0xff
    // 0xb72a40: ubfx            x3, x3, #0, #0x20
    // 0xb72a44: scvtf           d1, x3
    // 0xb72a48: d2 = 255.000000
    //     0xb72a48: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb72a4c: fdiv            d3, d1, d2
    // 0xb72a50: StoreField: r1->field_f = d3
    //     0xb72a50: stur            d3, [x1, #0xf]
    // 0xb72a54: ldur            x2, [fp, #-0x28]
    // 0xb72a58: ubfx            x2, x2, #0, #0x20
    // 0xb72a5c: and             w3, w2, #0xff
    // 0xb72a60: ubfx            x3, x3, #0, #0x20
    // 0xb72a64: scvtf           d1, x3
    // 0xb72a68: fdiv            d3, d1, d2
    // 0xb72a6c: ArrayStore: r1[0] = d3  ; List_8
    //     0xb72a6c: stur            d3, [x1, #0x17]
    // 0xb72a70: ldur            x2, [fp, #-0x30]
    // 0xb72a74: ubfx            x2, x2, #0, #0x20
    // 0xb72a78: and             w3, w2, #0xff
    // 0xb72a7c: ubfx            x3, x3, #0, #0x20
    // 0xb72a80: scvtf           d1, x3
    // 0xb72a84: fdiv            d3, d1, d2
    // 0xb72a88: StoreField: r1->field_1f = d3
    //     0xb72a88: stur            d3, [x1, #0x1f]
    // 0xb72a8c: r0 = ColorFilter()
    //     0xb72a8c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb72a90: mov             x1, x0
    // 0xb72a94: ldur            x0, [fp, #-0x18]
    // 0xb72a98: stur            x1, [fp, #-0x50]
    // 0xb72a9c: StoreField: r1->field_7 = r0
    //     0xb72a9c: stur            w0, [x1, #7]
    // 0xb72aa0: r0 = Instance_BlendMode
    //     0xb72aa0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb72aa4: ldr             x0, [x0, #0xb30]
    // 0xb72aa8: StoreField: r1->field_b = r0
    //     0xb72aa8: stur            w0, [x1, #0xb]
    // 0xb72aac: r2 = 1
    //     0xb72aac: movz            x2, #0x1
    // 0xb72ab0: StoreField: r1->field_13 = r2
    //     0xb72ab0: stur            x2, [x1, #0x13]
    // 0xb72ab4: r0 = SvgPicture()
    //     0xb72ab4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb72ab8: stur            x0, [fp, #-0x18]
    // 0xb72abc: r16 = "user"
    //     0xb72abc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb70] "user"
    //     0xb72ac0: ldr             x16, [x16, #0xb70]
    // 0xb72ac4: r30 = Instance_BoxFit
    //     0xb72ac4: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb72ac8: ldr             lr, [lr, #0xb18]
    // 0xb72acc: stp             lr, x16, [SP, #8]
    // 0xb72ad0: ldur            x16, [fp, #-0x50]
    // 0xb72ad4: str             x16, [SP]
    // 0xb72ad8: mov             x1, x0
    // 0xb72adc: r2 = "assets/images/user.svg"
    //     0xb72adc: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb78] "assets/images/user.svg"
    //     0xb72ae0: ldr             x2, [x2, #0xb78]
    // 0xb72ae4: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb72ae4: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb72ae8: ldr             x4, [x4, #0xb38]
    // 0xb72aec: r0 = SvgPicture.asset()
    //     0xb72aec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb72af0: r0 = Badge()
    //     0xb72af0: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0xb72af4: mov             x1, x0
    // 0xb72af8: ldur            x0, [fp, #-0x38]
    // 0xb72afc: stur            x1, [fp, #-0x50]
    // 0xb72b00: StoreField: r1->field_b = r0
    //     0xb72b00: stur            w0, [x1, #0xb]
    // 0xb72b04: ldur            x0, [fp, #-0x48]
    // 0xb72b08: StoreField: r1->field_27 = r0
    //     0xb72b08: stur            w0, [x1, #0x27]
    // 0xb72b0c: r0 = false
    //     0xb72b0c: add             x0, NULL, #0x30  ; false
    // 0xb72b10: StoreField: r1->field_2b = r0
    //     0xb72b10: stur            w0, [x1, #0x2b]
    // 0xb72b14: ldur            x0, [fp, #-0x18]
    // 0xb72b18: StoreField: r1->field_2f = r0
    //     0xb72b18: stur            w0, [x1, #0x2f]
    // 0xb72b1c: r0 = BottomNavigationBarItem()
    //     0xb72b1c: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xb72b20: mov             x1, x0
    // 0xb72b24: ldur            x0, [fp, #-0x58]
    // 0xb72b28: stur            x1, [fp, #-0x18]
    // 0xb72b2c: StoreField: r1->field_b = r0
    //     0xb72b2c: stur            w0, [x1, #0xb]
    // 0xb72b30: r0 = "Order"
    //     0xb72b30: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cba0] "Order"
    //     0xb72b34: ldr             x0, [x0, #0xba0]
    // 0xb72b38: StoreField: r1->field_13 = r0
    //     0xb72b38: stur            w0, [x1, #0x13]
    // 0xb72b3c: r0 = "1"
    //     0xb72b3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xb72b40: ldr             x0, [x0, #0xba8]
    // 0xb72b44: StoreField: r1->field_1b = r0
    //     0xb72b44: stur            w0, [x1, #0x1b]
    // 0xb72b48: ldur            x0, [fp, #-0x50]
    // 0xb72b4c: StoreField: r1->field_f = r0
    //     0xb72b4c: stur            w0, [x1, #0xf]
    // 0xb72b50: r0 = SvgPicture()
    //     0xb72b50: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb72b54: stur            x0, [fp, #-0x38]
    // 0xb72b58: r16 = "profile"
    //     0xb72b58: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbb0] "profile"
    //     0xb72b5c: ldr             x16, [x16, #0xbb0]
    // 0xb72b60: r30 = Instance_BoxFit
    //     0xb72b60: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb72b64: ldr             lr, [lr, #0xb18]
    // 0xb72b68: stp             lr, x16, [SP]
    // 0xb72b6c: mov             x1, x0
    // 0xb72b70: r2 = "assets/images/profile_circle.svg"
    //     0xb72b70: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbb8] "assets/images/profile_circle.svg"
    //     0xb72b74: ldr             x2, [x2, #0xbb8]
    // 0xb72b78: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb72b78: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb72b7c: ldr             x4, [x4, #0xb28]
    // 0xb72b80: r0 = SvgPicture.asset()
    //     0xb72b80: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb72b84: ldur            x2, [fp, #-0x10]
    // 0xb72b88: LoadField: r0 = r2->field_3f
    //     0xb72b88: ldur            w0, [x2, #0x3f]
    // 0xb72b8c: DecompressPointer r0
    //     0xb72b8c: add             x0, x0, HEAP, lsl #32
    // 0xb72b90: LoadField: r1 = r0->field_57
    //     0xb72b90: ldur            w1, [x0, #0x57]
    // 0xb72b94: DecompressPointer r1
    //     0xb72b94: add             x1, x1, HEAP, lsl #32
    // 0xb72b98: r0 = value()
    //     0xb72b98: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72b9c: LoadField: r1 = r0->field_b
    //     0xb72b9c: ldur            w1, [x0, #0xb]
    // 0xb72ba0: DecompressPointer r1
    //     0xb72ba0: add             x1, x1, HEAP, lsl #32
    // 0xb72ba4: cmp             w1, NULL
    // 0xb72ba8: b.ne            #0xb72bb4
    // 0xb72bac: r0 = Null
    //     0xb72bac: mov             x0, NULL
    // 0xb72bb0: b               #0xb72bec
    // 0xb72bb4: LoadField: r0 = r1->field_57
    //     0xb72bb4: ldur            w0, [x1, #0x57]
    // 0xb72bb8: DecompressPointer r0
    //     0xb72bb8: add             x0, x0, HEAP, lsl #32
    // 0xb72bbc: cmp             w0, NULL
    // 0xb72bc0: b.ne            #0xb72bcc
    // 0xb72bc4: r0 = Null
    //     0xb72bc4: mov             x0, NULL
    // 0xb72bc8: b               #0xb72bec
    // 0xb72bcc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72bcc: ldur            w1, [x0, #0x17]
    // 0xb72bd0: DecompressPointer r1
    //     0xb72bd0: add             x1, x1, HEAP, lsl #32
    // 0xb72bd4: cmp             w1, NULL
    // 0xb72bd8: b.ne            #0xb72be4
    // 0xb72bdc: r0 = Null
    //     0xb72bdc: mov             x0, NULL
    // 0xb72be0: b               #0xb72bec
    // 0xb72be4: LoadField: r0 = r1->field_7
    //     0xb72be4: ldur            w0, [x1, #7]
    // 0xb72be8: DecompressPointer r0
    //     0xb72be8: add             x0, x0, HEAP, lsl #32
    // 0xb72bec: cmp             w0, NULL
    // 0xb72bf0: b.ne            #0xb72bfc
    // 0xb72bf4: r0 = 0
    //     0xb72bf4: movz            x0, #0
    // 0xb72bf8: b               #0xb72c0c
    // 0xb72bfc: r1 = LoadInt32Instr(r0)
    //     0xb72bfc: sbfx            x1, x0, #1, #0x1f
    //     0xb72c00: tbz             w0, #0, #0xb72c08
    //     0xb72c04: ldur            x1, [x0, #7]
    // 0xb72c08: mov             x0, x1
    // 0xb72c0c: ldur            x2, [fp, #-0x10]
    // 0xb72c10: stur            x0, [fp, #-0x20]
    // 0xb72c14: LoadField: r1 = r2->field_3f
    //     0xb72c14: ldur            w1, [x2, #0x3f]
    // 0xb72c18: DecompressPointer r1
    //     0xb72c18: add             x1, x1, HEAP, lsl #32
    // 0xb72c1c: LoadField: r3 = r1->field_57
    //     0xb72c1c: ldur            w3, [x1, #0x57]
    // 0xb72c20: DecompressPointer r3
    //     0xb72c20: add             x3, x3, HEAP, lsl #32
    // 0xb72c24: mov             x1, x3
    // 0xb72c28: r0 = value()
    //     0xb72c28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72c2c: LoadField: r1 = r0->field_b
    //     0xb72c2c: ldur            w1, [x0, #0xb]
    // 0xb72c30: DecompressPointer r1
    //     0xb72c30: add             x1, x1, HEAP, lsl #32
    // 0xb72c34: cmp             w1, NULL
    // 0xb72c38: b.ne            #0xb72c44
    // 0xb72c3c: r0 = Null
    //     0xb72c3c: mov             x0, NULL
    // 0xb72c40: b               #0xb72c7c
    // 0xb72c44: LoadField: r0 = r1->field_57
    //     0xb72c44: ldur            w0, [x1, #0x57]
    // 0xb72c48: DecompressPointer r0
    //     0xb72c48: add             x0, x0, HEAP, lsl #32
    // 0xb72c4c: cmp             w0, NULL
    // 0xb72c50: b.ne            #0xb72c5c
    // 0xb72c54: r0 = Null
    //     0xb72c54: mov             x0, NULL
    // 0xb72c58: b               #0xb72c7c
    // 0xb72c5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72c5c: ldur            w1, [x0, #0x17]
    // 0xb72c60: DecompressPointer r1
    //     0xb72c60: add             x1, x1, HEAP, lsl #32
    // 0xb72c64: cmp             w1, NULL
    // 0xb72c68: b.ne            #0xb72c74
    // 0xb72c6c: r0 = Null
    //     0xb72c6c: mov             x0, NULL
    // 0xb72c70: b               #0xb72c7c
    // 0xb72c74: LoadField: r0 = r1->field_b
    //     0xb72c74: ldur            w0, [x1, #0xb]
    // 0xb72c78: DecompressPointer r0
    //     0xb72c78: add             x0, x0, HEAP, lsl #32
    // 0xb72c7c: cmp             w0, NULL
    // 0xb72c80: b.ne            #0xb72c8c
    // 0xb72c84: r0 = 0
    //     0xb72c84: movz            x0, #0
    // 0xb72c88: b               #0xb72c9c
    // 0xb72c8c: r1 = LoadInt32Instr(r0)
    //     0xb72c8c: sbfx            x1, x0, #1, #0x1f
    //     0xb72c90: tbz             w0, #0, #0xb72c98
    //     0xb72c94: ldur            x1, [x0, #7]
    // 0xb72c98: mov             x0, x1
    // 0xb72c9c: ldur            x2, [fp, #-0x10]
    // 0xb72ca0: stur            x0, [fp, #-0x28]
    // 0xb72ca4: LoadField: r1 = r2->field_3f
    //     0xb72ca4: ldur            w1, [x2, #0x3f]
    // 0xb72ca8: DecompressPointer r1
    //     0xb72ca8: add             x1, x1, HEAP, lsl #32
    // 0xb72cac: LoadField: r3 = r1->field_57
    //     0xb72cac: ldur            w3, [x1, #0x57]
    // 0xb72cb0: DecompressPointer r3
    //     0xb72cb0: add             x3, x3, HEAP, lsl #32
    // 0xb72cb4: mov             x1, x3
    // 0xb72cb8: r0 = value()
    //     0xb72cb8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72cbc: LoadField: r1 = r0->field_b
    //     0xb72cbc: ldur            w1, [x0, #0xb]
    // 0xb72cc0: DecompressPointer r1
    //     0xb72cc0: add             x1, x1, HEAP, lsl #32
    // 0xb72cc4: cmp             w1, NULL
    // 0xb72cc8: b.ne            #0xb72cd4
    // 0xb72ccc: r0 = Null
    //     0xb72ccc: mov             x0, NULL
    // 0xb72cd0: b               #0xb72d0c
    // 0xb72cd4: LoadField: r0 = r1->field_57
    //     0xb72cd4: ldur            w0, [x1, #0x57]
    // 0xb72cd8: DecompressPointer r0
    //     0xb72cd8: add             x0, x0, HEAP, lsl #32
    // 0xb72cdc: cmp             w0, NULL
    // 0xb72ce0: b.ne            #0xb72cec
    // 0xb72ce4: r0 = Null
    //     0xb72ce4: mov             x0, NULL
    // 0xb72ce8: b               #0xb72d0c
    // 0xb72cec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72cec: ldur            w1, [x0, #0x17]
    // 0xb72cf0: DecompressPointer r1
    //     0xb72cf0: add             x1, x1, HEAP, lsl #32
    // 0xb72cf4: cmp             w1, NULL
    // 0xb72cf8: b.ne            #0xb72d04
    // 0xb72cfc: r0 = Null
    //     0xb72cfc: mov             x0, NULL
    // 0xb72d00: b               #0xb72d0c
    // 0xb72d04: LoadField: r0 = r1->field_f
    //     0xb72d04: ldur            w0, [x1, #0xf]
    // 0xb72d08: DecompressPointer r0
    //     0xb72d08: add             x0, x0, HEAP, lsl #32
    // 0xb72d0c: cmp             w0, NULL
    // 0xb72d10: b.ne            #0xb72d1c
    // 0xb72d14: r1 = 0
    //     0xb72d14: movz            x1, #0
    // 0xb72d18: b               #0xb72d28
    // 0xb72d1c: r1 = LoadInt32Instr(r0)
    //     0xb72d1c: sbfx            x1, x0, #1, #0x1f
    //     0xb72d20: tbz             w0, #0, #0xb72d28
    //     0xb72d24: ldur            x1, [x0, #7]
    // 0xb72d28: ldur            x2, [fp, #-0x10]
    // 0xb72d2c: ldur            x0, [fp, #-0x38]
    // 0xb72d30: stur            x1, [fp, #-0x30]
    // 0xb72d34: r0 = Color()
    //     0xb72d34: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb72d38: mov             x1, x0
    // 0xb72d3c: r0 = Instance_ColorSpace
    //     0xb72d3c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb72d40: stur            x1, [fp, #-0x48]
    // 0xb72d44: StoreField: r1->field_27 = r0
    //     0xb72d44: stur            w0, [x1, #0x27]
    // 0xb72d48: d0 = 1.000000
    //     0xb72d48: fmov            d0, #1.00000000
    // 0xb72d4c: StoreField: r1->field_7 = d0
    //     0xb72d4c: stur            d0, [x1, #7]
    // 0xb72d50: ldur            x2, [fp, #-0x20]
    // 0xb72d54: ubfx            x2, x2, #0, #0x20
    // 0xb72d58: and             w3, w2, #0xff
    // 0xb72d5c: ubfx            x3, x3, #0, #0x20
    // 0xb72d60: scvtf           d1, x3
    // 0xb72d64: d2 = 255.000000
    //     0xb72d64: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb72d68: fdiv            d3, d1, d2
    // 0xb72d6c: StoreField: r1->field_f = d3
    //     0xb72d6c: stur            d3, [x1, #0xf]
    // 0xb72d70: ldur            x2, [fp, #-0x28]
    // 0xb72d74: ubfx            x2, x2, #0, #0x20
    // 0xb72d78: and             w3, w2, #0xff
    // 0xb72d7c: ubfx            x3, x3, #0, #0x20
    // 0xb72d80: scvtf           d1, x3
    // 0xb72d84: fdiv            d3, d1, d2
    // 0xb72d88: ArrayStore: r1[0] = d3  ; List_8
    //     0xb72d88: stur            d3, [x1, #0x17]
    // 0xb72d8c: ldur            x2, [fp, #-0x30]
    // 0xb72d90: ubfx            x2, x2, #0, #0x20
    // 0xb72d94: and             w3, w2, #0xff
    // 0xb72d98: ubfx            x3, x3, #0, #0x20
    // 0xb72d9c: scvtf           d1, x3
    // 0xb72da0: fdiv            d3, d1, d2
    // 0xb72da4: StoreField: r1->field_1f = d3
    //     0xb72da4: stur            d3, [x1, #0x1f]
    // 0xb72da8: r0 = ColorFilter()
    //     0xb72da8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb72dac: mov             x1, x0
    // 0xb72db0: ldur            x0, [fp, #-0x48]
    // 0xb72db4: stur            x1, [fp, #-0x50]
    // 0xb72db8: StoreField: r1->field_7 = r0
    //     0xb72db8: stur            w0, [x1, #7]
    // 0xb72dbc: r0 = Instance_BlendMode
    //     0xb72dbc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb72dc0: ldr             x0, [x0, #0xb30]
    // 0xb72dc4: StoreField: r1->field_b = r0
    //     0xb72dc4: stur            w0, [x1, #0xb]
    // 0xb72dc8: r2 = 1
    //     0xb72dc8: movz            x2, #0x1
    // 0xb72dcc: StoreField: r1->field_13 = r2
    //     0xb72dcc: stur            x2, [x1, #0x13]
    // 0xb72dd0: r0 = SvgPicture()
    //     0xb72dd0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb72dd4: stur            x0, [fp, #-0x48]
    // 0xb72dd8: r16 = "profile"
    //     0xb72dd8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbb0] "profile"
    //     0xb72ddc: ldr             x16, [x16, #0xbb0]
    // 0xb72de0: r30 = Instance_BoxFit
    //     0xb72de0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb72de4: ldr             lr, [lr, #0xb18]
    // 0xb72de8: stp             lr, x16, [SP, #8]
    // 0xb72dec: ldur            x16, [fp, #-0x50]
    // 0xb72df0: str             x16, [SP]
    // 0xb72df4: mov             x1, x0
    // 0xb72df8: r2 = "assets/images/profile_circle.svg"
    //     0xb72df8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbb8] "assets/images/profile_circle.svg"
    //     0xb72dfc: ldr             x2, [x2, #0xbb8]
    // 0xb72e00: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb72e00: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb72e04: ldr             x4, [x4, #0xb38]
    // 0xb72e08: r0 = SvgPicture.asset()
    //     0xb72e08: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb72e0c: r0 = BottomNavigationBarItem()
    //     0xb72e0c: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xb72e10: mov             x1, x0
    // 0xb72e14: ldur            x0, [fp, #-0x38]
    // 0xb72e18: stur            x1, [fp, #-0x50]
    // 0xb72e1c: StoreField: r1->field_b = r0
    //     0xb72e1c: stur            w0, [x1, #0xb]
    // 0xb72e20: r0 = "Profile"
    //     0xb72e20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cbc0] "Profile"
    //     0xb72e24: ldr             x0, [x0, #0xbc0]
    // 0xb72e28: StoreField: r1->field_13 = r0
    //     0xb72e28: stur            w0, [x1, #0x13]
    // 0xb72e2c: ldur            x0, [fp, #-0x48]
    // 0xb72e30: StoreField: r1->field_f = r0
    //     0xb72e30: stur            w0, [x1, #0xf]
    // 0xb72e34: r0 = SvgPicture()
    //     0xb72e34: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb72e38: stur            x0, [fp, #-0x38]
    // 0xb72e3c: r16 = "menu"
    //     0xb72e3c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbc8] "menu"
    //     0xb72e40: ldr             x16, [x16, #0xbc8]
    // 0xb72e44: r30 = Instance_BoxFit
    //     0xb72e44: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb72e48: ldr             lr, [lr, #0xb18]
    // 0xb72e4c: stp             lr, x16, [SP]
    // 0xb72e50: mov             x1, x0
    // 0xb72e54: r2 = "assets/images/menu.svg"
    //     0xb72e54: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbd0] "assets/images/menu.svg"
    //     0xb72e58: ldr             x2, [x2, #0xbd0]
    // 0xb72e5c: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb72e5c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb72e60: ldr             x4, [x4, #0xb28]
    // 0xb72e64: r0 = SvgPicture.asset()
    //     0xb72e64: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb72e68: ldur            x2, [fp, #-0x10]
    // 0xb72e6c: LoadField: r0 = r2->field_3f
    //     0xb72e6c: ldur            w0, [x2, #0x3f]
    // 0xb72e70: DecompressPointer r0
    //     0xb72e70: add             x0, x0, HEAP, lsl #32
    // 0xb72e74: LoadField: r1 = r0->field_57
    //     0xb72e74: ldur            w1, [x0, #0x57]
    // 0xb72e78: DecompressPointer r1
    //     0xb72e78: add             x1, x1, HEAP, lsl #32
    // 0xb72e7c: r0 = value()
    //     0xb72e7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72e80: LoadField: r1 = r0->field_b
    //     0xb72e80: ldur            w1, [x0, #0xb]
    // 0xb72e84: DecompressPointer r1
    //     0xb72e84: add             x1, x1, HEAP, lsl #32
    // 0xb72e88: cmp             w1, NULL
    // 0xb72e8c: b.ne            #0xb72e98
    // 0xb72e90: r0 = Null
    //     0xb72e90: mov             x0, NULL
    // 0xb72e94: b               #0xb72ed0
    // 0xb72e98: LoadField: r0 = r1->field_57
    //     0xb72e98: ldur            w0, [x1, #0x57]
    // 0xb72e9c: DecompressPointer r0
    //     0xb72e9c: add             x0, x0, HEAP, lsl #32
    // 0xb72ea0: cmp             w0, NULL
    // 0xb72ea4: b.ne            #0xb72eb0
    // 0xb72ea8: r0 = Null
    //     0xb72ea8: mov             x0, NULL
    // 0xb72eac: b               #0xb72ed0
    // 0xb72eb0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72eb0: ldur            w1, [x0, #0x17]
    // 0xb72eb4: DecompressPointer r1
    //     0xb72eb4: add             x1, x1, HEAP, lsl #32
    // 0xb72eb8: cmp             w1, NULL
    // 0xb72ebc: b.ne            #0xb72ec8
    // 0xb72ec0: r0 = Null
    //     0xb72ec0: mov             x0, NULL
    // 0xb72ec4: b               #0xb72ed0
    // 0xb72ec8: LoadField: r0 = r1->field_7
    //     0xb72ec8: ldur            w0, [x1, #7]
    // 0xb72ecc: DecompressPointer r0
    //     0xb72ecc: add             x0, x0, HEAP, lsl #32
    // 0xb72ed0: cmp             w0, NULL
    // 0xb72ed4: b.ne            #0xb72ee0
    // 0xb72ed8: r0 = 0
    //     0xb72ed8: movz            x0, #0
    // 0xb72edc: b               #0xb72ef0
    // 0xb72ee0: r1 = LoadInt32Instr(r0)
    //     0xb72ee0: sbfx            x1, x0, #1, #0x1f
    //     0xb72ee4: tbz             w0, #0, #0xb72eec
    //     0xb72ee8: ldur            x1, [x0, #7]
    // 0xb72eec: mov             x0, x1
    // 0xb72ef0: ldur            x2, [fp, #-0x10]
    // 0xb72ef4: stur            x0, [fp, #-0x20]
    // 0xb72ef8: LoadField: r1 = r2->field_3f
    //     0xb72ef8: ldur            w1, [x2, #0x3f]
    // 0xb72efc: DecompressPointer r1
    //     0xb72efc: add             x1, x1, HEAP, lsl #32
    // 0xb72f00: LoadField: r3 = r1->field_57
    //     0xb72f00: ldur            w3, [x1, #0x57]
    // 0xb72f04: DecompressPointer r3
    //     0xb72f04: add             x3, x3, HEAP, lsl #32
    // 0xb72f08: mov             x1, x3
    // 0xb72f0c: r0 = value()
    //     0xb72f0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72f10: LoadField: r1 = r0->field_b
    //     0xb72f10: ldur            w1, [x0, #0xb]
    // 0xb72f14: DecompressPointer r1
    //     0xb72f14: add             x1, x1, HEAP, lsl #32
    // 0xb72f18: cmp             w1, NULL
    // 0xb72f1c: b.ne            #0xb72f28
    // 0xb72f20: r0 = Null
    //     0xb72f20: mov             x0, NULL
    // 0xb72f24: b               #0xb72f60
    // 0xb72f28: LoadField: r0 = r1->field_57
    //     0xb72f28: ldur            w0, [x1, #0x57]
    // 0xb72f2c: DecompressPointer r0
    //     0xb72f2c: add             x0, x0, HEAP, lsl #32
    // 0xb72f30: cmp             w0, NULL
    // 0xb72f34: b.ne            #0xb72f40
    // 0xb72f38: r0 = Null
    //     0xb72f38: mov             x0, NULL
    // 0xb72f3c: b               #0xb72f60
    // 0xb72f40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72f40: ldur            w1, [x0, #0x17]
    // 0xb72f44: DecompressPointer r1
    //     0xb72f44: add             x1, x1, HEAP, lsl #32
    // 0xb72f48: cmp             w1, NULL
    // 0xb72f4c: b.ne            #0xb72f58
    // 0xb72f50: r0 = Null
    //     0xb72f50: mov             x0, NULL
    // 0xb72f54: b               #0xb72f60
    // 0xb72f58: LoadField: r0 = r1->field_b
    //     0xb72f58: ldur            w0, [x1, #0xb]
    // 0xb72f5c: DecompressPointer r0
    //     0xb72f5c: add             x0, x0, HEAP, lsl #32
    // 0xb72f60: cmp             w0, NULL
    // 0xb72f64: b.ne            #0xb72f70
    // 0xb72f68: r0 = 0
    //     0xb72f68: movz            x0, #0
    // 0xb72f6c: b               #0xb72f80
    // 0xb72f70: r1 = LoadInt32Instr(r0)
    //     0xb72f70: sbfx            x1, x0, #1, #0x1f
    //     0xb72f74: tbz             w0, #0, #0xb72f7c
    //     0xb72f78: ldur            x1, [x0, #7]
    // 0xb72f7c: mov             x0, x1
    // 0xb72f80: ldur            x2, [fp, #-0x10]
    // 0xb72f84: stur            x0, [fp, #-0x28]
    // 0xb72f88: LoadField: r1 = r2->field_3f
    //     0xb72f88: ldur            w1, [x2, #0x3f]
    // 0xb72f8c: DecompressPointer r1
    //     0xb72f8c: add             x1, x1, HEAP, lsl #32
    // 0xb72f90: LoadField: r3 = r1->field_57
    //     0xb72f90: ldur            w3, [x1, #0x57]
    // 0xb72f94: DecompressPointer r3
    //     0xb72f94: add             x3, x3, HEAP, lsl #32
    // 0xb72f98: mov             x1, x3
    // 0xb72f9c: r0 = value()
    //     0xb72f9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb72fa0: LoadField: r1 = r0->field_b
    //     0xb72fa0: ldur            w1, [x0, #0xb]
    // 0xb72fa4: DecompressPointer r1
    //     0xb72fa4: add             x1, x1, HEAP, lsl #32
    // 0xb72fa8: cmp             w1, NULL
    // 0xb72fac: b.ne            #0xb72fb8
    // 0xb72fb0: r0 = Null
    //     0xb72fb0: mov             x0, NULL
    // 0xb72fb4: b               #0xb72ff0
    // 0xb72fb8: LoadField: r0 = r1->field_57
    //     0xb72fb8: ldur            w0, [x1, #0x57]
    // 0xb72fbc: DecompressPointer r0
    //     0xb72fbc: add             x0, x0, HEAP, lsl #32
    // 0xb72fc0: cmp             w0, NULL
    // 0xb72fc4: b.ne            #0xb72fd0
    // 0xb72fc8: r0 = Null
    //     0xb72fc8: mov             x0, NULL
    // 0xb72fcc: b               #0xb72ff0
    // 0xb72fd0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb72fd0: ldur            w1, [x0, #0x17]
    // 0xb72fd4: DecompressPointer r1
    //     0xb72fd4: add             x1, x1, HEAP, lsl #32
    // 0xb72fd8: cmp             w1, NULL
    // 0xb72fdc: b.ne            #0xb72fe8
    // 0xb72fe0: r0 = Null
    //     0xb72fe0: mov             x0, NULL
    // 0xb72fe4: b               #0xb72ff0
    // 0xb72fe8: LoadField: r0 = r1->field_f
    //     0xb72fe8: ldur            w0, [x1, #0xf]
    // 0xb72fec: DecompressPointer r0
    //     0xb72fec: add             x0, x0, HEAP, lsl #32
    // 0xb72ff0: cmp             w0, NULL
    // 0xb72ff4: b.ne            #0xb73000
    // 0xb72ff8: r4 = 0
    //     0xb72ff8: movz            x4, #0
    // 0xb72ffc: b               #0xb73010
    // 0xb73000: r1 = LoadInt32Instr(r0)
    //     0xb73000: sbfx            x1, x0, #1, #0x1f
    //     0xb73004: tbz             w0, #0, #0xb7300c
    //     0xb73008: ldur            x1, [x0, #7]
    // 0xb7300c: mov             x4, x1
    // 0xb73010: ldur            x3, [fp, #-0x40]
    // 0xb73014: ldur            x2, [fp, #-0x18]
    // 0xb73018: ldur            x1, [fp, #-0x50]
    // 0xb7301c: ldur            x0, [fp, #-0x38]
    // 0xb73020: stur            x4, [fp, #-0x30]
    // 0xb73024: r0 = Color()
    //     0xb73024: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb73028: mov             x1, x0
    // 0xb7302c: r0 = Instance_ColorSpace
    //     0xb7302c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb73030: stur            x1, [fp, #-0x48]
    // 0xb73034: StoreField: r1->field_27 = r0
    //     0xb73034: stur            w0, [x1, #0x27]
    // 0xb73038: d0 = 1.000000
    //     0xb73038: fmov            d0, #1.00000000
    // 0xb7303c: StoreField: r1->field_7 = d0
    //     0xb7303c: stur            d0, [x1, #7]
    // 0xb73040: ldur            x0, [fp, #-0x20]
    // 0xb73044: ubfx            x0, x0, #0, #0x20
    // 0xb73048: and             w2, w0, #0xff
    // 0xb7304c: ubfx            x2, x2, #0, #0x20
    // 0xb73050: scvtf           d0, x2
    // 0xb73054: d1 = 255.000000
    //     0xb73054: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb73058: fdiv            d2, d0, d1
    // 0xb7305c: StoreField: r1->field_f = d2
    //     0xb7305c: stur            d2, [x1, #0xf]
    // 0xb73060: ldur            x0, [fp, #-0x28]
    // 0xb73064: ubfx            x0, x0, #0, #0x20
    // 0xb73068: and             w2, w0, #0xff
    // 0xb7306c: ubfx            x2, x2, #0, #0x20
    // 0xb73070: scvtf           d0, x2
    // 0xb73074: fdiv            d2, d0, d1
    // 0xb73078: ArrayStore: r1[0] = d2  ; List_8
    //     0xb73078: stur            d2, [x1, #0x17]
    // 0xb7307c: ldur            x0, [fp, #-0x30]
    // 0xb73080: ubfx            x0, x0, #0, #0x20
    // 0xb73084: and             w2, w0, #0xff
    // 0xb73088: ubfx            x2, x2, #0, #0x20
    // 0xb7308c: scvtf           d0, x2
    // 0xb73090: fdiv            d2, d0, d1
    // 0xb73094: StoreField: r1->field_1f = d2
    //     0xb73094: stur            d2, [x1, #0x1f]
    // 0xb73098: r0 = ColorFilter()
    //     0xb73098: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb7309c: mov             x1, x0
    // 0xb730a0: ldur            x0, [fp, #-0x48]
    // 0xb730a4: stur            x1, [fp, #-0x58]
    // 0xb730a8: StoreField: r1->field_7 = r0
    //     0xb730a8: stur            w0, [x1, #7]
    // 0xb730ac: r0 = Instance_BlendMode
    //     0xb730ac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb730b0: ldr             x0, [x0, #0xb30]
    // 0xb730b4: StoreField: r1->field_b = r0
    //     0xb730b4: stur            w0, [x1, #0xb]
    // 0xb730b8: r0 = 1
    //     0xb730b8: movz            x0, #0x1
    // 0xb730bc: StoreField: r1->field_13 = r0
    //     0xb730bc: stur            x0, [x1, #0x13]
    // 0xb730c0: r0 = SvgPicture()
    //     0xb730c0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb730c4: stur            x0, [fp, #-0x48]
    // 0xb730c8: r16 = "menu"
    //     0xb730c8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbc8] "menu"
    //     0xb730cc: ldr             x16, [x16, #0xbc8]
    // 0xb730d0: r30 = Instance_BoxFit
    //     0xb730d0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb730d4: ldr             lr, [lr, #0xb18]
    // 0xb730d8: stp             lr, x16, [SP, #8]
    // 0xb730dc: ldur            x16, [fp, #-0x58]
    // 0xb730e0: str             x16, [SP]
    // 0xb730e4: mov             x1, x0
    // 0xb730e8: r2 = "assets/images/menu.svg"
    //     0xb730e8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbd0] "assets/images/menu.svg"
    //     0xb730ec: ldr             x2, [x2, #0xbd0]
    // 0xb730f0: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb730f0: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb730f4: ldr             x4, [x4, #0xb38]
    // 0xb730f8: r0 = SvgPicture.asset()
    //     0xb730f8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb730fc: r0 = BottomNavigationBarItem()
    //     0xb730fc: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xb73100: mov             x3, x0
    // 0xb73104: ldur            x0, [fp, #-0x38]
    // 0xb73108: stur            x3, [fp, #-0x58]
    // 0xb7310c: StoreField: r3->field_b = r0
    //     0xb7310c: stur            w0, [x3, #0xb]
    // 0xb73110: r0 = "Browse"
    //     0xb73110: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cbd8] "Browse"
    //     0xb73114: ldr             x0, [x0, #0xbd8]
    // 0xb73118: StoreField: r3->field_13 = r0
    //     0xb73118: stur            w0, [x3, #0x13]
    // 0xb7311c: ldur            x0, [fp, #-0x48]
    // 0xb73120: StoreField: r3->field_f = r0
    //     0xb73120: stur            w0, [x3, #0xf]
    // 0xb73124: r1 = Null
    //     0xb73124: mov             x1, NULL
    // 0xb73128: r2 = 8
    //     0xb73128: movz            x2, #0x8
    // 0xb7312c: r0 = AllocateArray()
    //     0xb7312c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb73130: mov             x2, x0
    // 0xb73134: ldur            x0, [fp, #-0x40]
    // 0xb73138: stur            x2, [fp, #-0x38]
    // 0xb7313c: StoreField: r2->field_f = r0
    //     0xb7313c: stur            w0, [x2, #0xf]
    // 0xb73140: ldur            x0, [fp, #-0x18]
    // 0xb73144: StoreField: r2->field_13 = r0
    //     0xb73144: stur            w0, [x2, #0x13]
    // 0xb73148: ldur            x0, [fp, #-0x50]
    // 0xb7314c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7314c: stur            w0, [x2, #0x17]
    // 0xb73150: ldur            x0, [fp, #-0x58]
    // 0xb73154: StoreField: r2->field_1b = r0
    //     0xb73154: stur            w0, [x2, #0x1b]
    // 0xb73158: r1 = <BottomNavigationBarItem>
    //     0xb73158: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbe0] TypeArguments: <BottomNavigationBarItem>
    //     0xb7315c: ldr             x1, [x1, #0xbe0]
    // 0xb73160: r0 = AllocateGrowableArray()
    //     0xb73160: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb73164: mov             x3, x0
    // 0xb73168: ldur            x0, [fp, #-0x38]
    // 0xb7316c: stur            x3, [fp, #-0x18]
    // 0xb73170: StoreField: r3->field_f = r0
    //     0xb73170: stur            w0, [x3, #0xf]
    // 0xb73174: r0 = 8
    //     0xb73174: movz            x0, #0x8
    // 0xb73178: StoreField: r3->field_b = r0
    //     0xb73178: stur            w0, [x3, #0xb]
    // 0xb7317c: ldur            x2, [fp, #-0x10]
    // 0xb73180: r1 = Function '_onItemTapped@1595367332':.
    //     0xb73180: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2db80] AnonymousClosure: (0x942788), in [package:customer_app/app/presentation/views/glass/main/main_page.dart] _MainPageState::_onItemTapped (0x942718)
    //     0xb73184: ldr             x1, [x1, #0xb80]
    // 0xb73188: r0 = AllocateClosure()
    //     0xb73188: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7318c: stur            x0, [fp, #-0x10]
    // 0xb73190: r0 = BottomNavigationBar()
    //     0xb73190: bl              #0xa688fc  ; AllocateBottomNavigationBarStub -> BottomNavigationBar (size=0x70)
    // 0xb73194: mov             x1, x0
    // 0xb73198: ldur            x2, [fp, #-8]
    // 0xb7319c: ldur            x3, [fp, #-0x18]
    // 0xb731a0: ldur            x5, [fp, #-0x10]
    // 0xb731a4: stur            x0, [fp, #-0x10]
    // 0xb731a8: r0 = BottomNavigationBar()
    //     0xb731a8: bl              #0xa68858  ; [package:flutter/src/material/bottom_navigation_bar.dart] BottomNavigationBar::BottomNavigationBar
    // 0xb731ac: ldur            x0, [fp, #-0x10]
    // 0xb731b0: LeaveFrame
    //     0xb731b0: mov             SP, fp
    //     0xb731b4: ldp             fp, lr, [SP], #0x10
    // 0xb731b8: ret
    //     0xb731b8: ret             
    // 0xb731bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb731bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb731c0: b               #0xb71dd8
    // 0xb731c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb731c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb731c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb731c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb731cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb731cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb731d0: r0 = RangeErrorSharedWithFPURegs()
    //     0xb731d0: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xb731d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb731d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb731d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb731d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87bf0, size: 0x88
    // 0xc87bf0: EnterFrame
    //     0xc87bf0: stp             fp, lr, [SP, #-0x10]!
    //     0xc87bf4: mov             fp, SP
    // 0xc87bf8: AllocStack(0x10)
    //     0xc87bf8: sub             SP, SP, #0x10
    // 0xc87bfc: SetupParameters(_MainPageState this /* r1 => r1, fp-0x10 */)
    //     0xc87bfc: stur            x1, [fp, #-0x10]
    // 0xc87c00: CheckStackOverflow
    //     0xc87c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87c04: cmp             SP, x16
    //     0xc87c08: b.ls            #0xc87c70
    // 0xc87c0c: LoadField: r0 = r1->field_3f
    //     0xc87c0c: ldur            w0, [x1, #0x3f]
    // 0xc87c10: DecompressPointer r0
    //     0xc87c10: add             x0, x0, HEAP, lsl #32
    // 0xc87c14: LoadField: r2 = r0->field_4b
    //     0xc87c14: ldur            w2, [x0, #0x4b]
    // 0xc87c18: DecompressPointer r2
    //     0xc87c18: add             x2, x2, HEAP, lsl #32
    // 0xc87c1c: stur            x2, [fp, #-8]
    // 0xc87c20: r0 = UtmData()
    //     0xc87c20: bl              #0x8939f0  ; AllocateUtmDataStub -> UtmData (size=0x1c)
    // 0xc87c24: ldur            x1, [fp, #-8]
    // 0xc87c28: mov             x2, x0
    // 0xc87c2c: r0 = setUtmData()
    //     0xc87c2c: bl              #0x916b50  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setUtmData
    // 0xc87c30: ldur            x2, [fp, #-0x10]
    // 0xc87c34: LoadField: r1 = r2->field_47
    //     0xc87c34: ldur            w1, [x2, #0x47]
    // 0xc87c38: DecompressPointer r1
    //     0xc87c38: add             x1, x1, HEAP, lsl #32
    // 0xc87c3c: cmp             w1, NULL
    // 0xc87c40: b.eq            #0xc87c58
    // 0xc87c44: r0 = LoadClassIdInstr(r1)
    //     0xc87c44: ldur            x0, [x1, #-1]
    //     0xc87c48: ubfx            x0, x0, #0xc, #0x14
    // 0xc87c4c: r0 = GDT[cid_x0 + -0xe14]()
    //     0xc87c4c: sub             lr, x0, #0xe14
    //     0xc87c50: ldr             lr, [x21, lr, lsl #3]
    //     0xc87c54: blr             lr
    // 0xc87c58: ldur            x1, [fp, #-0x10]
    // 0xc87c5c: r0 = dispose()
    //     0xc87c5c: bl              #0xc87c78  ; [package:customer_app/app/presentation/views/glass/main/main_page.dart] __MainPageState&State&SingleTickerProviderStateMixin::dispose
    // 0xc87c60: r0 = Null
    //     0xc87c60: mov             x0, NULL
    // 0xc87c64: LeaveFrame
    //     0xc87c64: mov             SP, fp
    //     0xc87c68: ldp             fp, lr, [SP], #0x10
    // 0xc87c6c: ret
    //     0xc87c6c: ret             
    // 0xc87c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87c70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87c74: b               #0xc87c0c
  }
}

// class id: 4070, size: 0xc, field offset: 0xc
//   const constructor, 
class MainPage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f554, size: 0x48
    // 0xc7f554: EnterFrame
    //     0xc7f554: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f558: mov             fp, SP
    // 0xc7f55c: AllocStack(0x8)
    //     0xc7f55c: sub             SP, SP, #8
    // 0xc7f560: CheckStackOverflow
    //     0xc7f560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f564: cmp             SP, x16
    //     0xc7f568: b.ls            #0xc7f594
    // 0xc7f56c: r1 = <MainPage>
    //     0xc7f56c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ae78] TypeArguments: <MainPage>
    //     0xc7f570: ldr             x1, [x1, #0xe78]
    // 0xc7f574: r0 = _MainPageState()
    //     0xc7f574: bl              #0xc7f59c  ; Allocate_MainPageStateStub -> _MainPageState (size=0x54)
    // 0xc7f578: mov             x1, x0
    // 0xc7f57c: stur            x0, [fp, #-8]
    // 0xc7f580: r0 = _MainPageState()
    //     0xc7f580: bl              #0xc7be60  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::_MainPageState
    // 0xc7f584: ldur            x0, [fp, #-8]
    // 0xc7f588: LeaveFrame
    //     0xc7f588: mov             SP, fp
    //     0xc7f58c: ldp             fp, lr, [SP], #0x10
    // 0xc7f590: ret
    //     0xc7f590: ret             
    // 0xc7f594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f594: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f598: b               #0xc7f56c
  }
}
