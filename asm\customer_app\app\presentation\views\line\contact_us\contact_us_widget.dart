// lib: , url: package:customer_app/app/presentation/views/line/contact_us/contact_us_widget.dart

// class id: 1049504, size: 0x8
class :: {
}

// class id: 4539, size: 0x14, field offset: 0x14
//   const constructor, 
class ContactUsWidget extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x1504c18, size: 0xcd8
    // 0x1504c18: EnterFrame
    //     0x1504c18: stp             fp, lr, [SP, #-0x10]!
    //     0x1504c1c: mov             fp, SP
    // 0x1504c20: AllocStack(0x50)
    //     0x1504c20: sub             SP, SP, #0x50
    // 0x1504c24: SetupParameters(ContactUsWidget this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x1504c24: mov             x0, x2
    //     0x1504c28: stur            x2, [fp, #-0x10]
    //     0x1504c2c: mov             x2, x1
    //     0x1504c30: stur            x1, [fp, #-8]
    // 0x1504c34: CheckStackOverflow
    //     0x1504c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1504c38: cmp             SP, x16
    //     0x1504c3c: b.ls            #0x15058e8
    // 0x1504c40: mov             x1, x2
    // 0x1504c44: r0 = controller()
    //     0x1504c44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504c48: LoadField: r1 = r0->field_4b
    //     0x1504c48: ldur            w1, [x0, #0x4b]
    // 0x1504c4c: DecompressPointer r1
    //     0x1504c4c: add             x1, x1, HEAP, lsl #32
    // 0x1504c50: r0 = value()
    //     0x1504c50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1504c54: LoadField: r1 = r0->field_5f
    //     0x1504c54: ldur            w1, [x0, #0x5f]
    // 0x1504c58: DecompressPointer r1
    //     0x1504c58: add             x1, x1, HEAP, lsl #32
    // 0x1504c5c: cmp             w1, NULL
    // 0x1504c60: b.ne            #0x1504c6c
    // 0x1504c64: r0 = Null
    //     0x1504c64: mov             x0, NULL
    // 0x1504c68: b               #0x1504c74
    // 0x1504c6c: LoadField: r0 = r1->field_7
    //     0x1504c6c: ldur            w0, [x1, #7]
    // 0x1504c70: DecompressPointer r0
    //     0x1504c70: add             x0, x0, HEAP, lsl #32
    // 0x1504c74: cmp             w0, NULL
    // 0x1504c78: r16 = true
    //     0x1504c78: add             x16, NULL, #0x20  ; true
    // 0x1504c7c: r17 = false
    //     0x1504c7c: add             x17, NULL, #0x30  ; false
    // 0x1504c80: csel            x2, x16, x17, ne
    // 0x1504c84: ldur            x1, [fp, #-0x10]
    // 0x1504c88: stur            x2, [fp, #-0x18]
    // 0x1504c8c: r0 = of()
    //     0x1504c8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1504c90: LoadField: r1 = r0->field_87
    //     0x1504c90: ldur            w1, [x0, #0x87]
    // 0x1504c94: DecompressPointer r1
    //     0x1504c94: add             x1, x1, HEAP, lsl #32
    // 0x1504c98: LoadField: r0 = r1->field_2b
    //     0x1504c98: ldur            w0, [x1, #0x2b]
    // 0x1504c9c: DecompressPointer r0
    //     0x1504c9c: add             x0, x0, HEAP, lsl #32
    // 0x1504ca0: r16 = Instance_Color
    //     0x1504ca0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1504ca4: r30 = 14.000000
    //     0x1504ca4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1504ca8: ldr             lr, [lr, #0x1d8]
    // 0x1504cac: stp             lr, x16, [SP]
    // 0x1504cb0: mov             x1, x0
    // 0x1504cb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1504cb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1504cb8: ldr             x4, [x4, #0x9b8]
    // 0x1504cbc: r0 = copyWith()
    //     0x1504cbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1504cc0: stur            x0, [fp, #-0x20]
    // 0x1504cc4: r0 = TextSpan()
    //     0x1504cc4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1504cc8: mov             x2, x0
    // 0x1504ccc: r0 = "Call: + 91 - "
    //     0x1504ccc: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb28] "Call: + 91 - "
    //     0x1504cd0: ldr             x0, [x0, #0xb28]
    // 0x1504cd4: stur            x2, [fp, #-0x28]
    // 0x1504cd8: StoreField: r2->field_b = r0
    //     0x1504cd8: stur            w0, [x2, #0xb]
    // 0x1504cdc: r0 = Instance__DeferringMouseCursor
    //     0x1504cdc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1504ce0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1504ce0: stur            w0, [x2, #0x17]
    // 0x1504ce4: ldur            x1, [fp, #-0x20]
    // 0x1504ce8: StoreField: r2->field_7 = r1
    //     0x1504ce8: stur            w1, [x2, #7]
    // 0x1504cec: ldur            x1, [fp, #-8]
    // 0x1504cf0: r0 = controller()
    //     0x1504cf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504cf4: LoadField: r1 = r0->field_4b
    //     0x1504cf4: ldur            w1, [x0, #0x4b]
    // 0x1504cf8: DecompressPointer r1
    //     0x1504cf8: add             x1, x1, HEAP, lsl #32
    // 0x1504cfc: r0 = value()
    //     0x1504cfc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1504d00: LoadField: r1 = r0->field_5f
    //     0x1504d00: ldur            w1, [x0, #0x5f]
    // 0x1504d04: DecompressPointer r1
    //     0x1504d04: add             x1, x1, HEAP, lsl #32
    // 0x1504d08: cmp             w1, NULL
    // 0x1504d0c: b.ne            #0x1504d18
    // 0x1504d10: r2 = Null
    //     0x1504d10: mov             x2, NULL
    // 0x1504d14: b               #0x1504d24
    // 0x1504d18: LoadField: r0 = r1->field_7
    //     0x1504d18: ldur            w0, [x1, #7]
    // 0x1504d1c: DecompressPointer r0
    //     0x1504d1c: add             x0, x0, HEAP, lsl #32
    // 0x1504d20: mov             x2, x0
    // 0x1504d24: ldur            x1, [fp, #-0x18]
    // 0x1504d28: ldur            x0, [fp, #-0x28]
    // 0x1504d2c: str             x2, [SP]
    // 0x1504d30: r0 = _interpolateSingle()
    //     0x1504d30: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x1504d34: ldur            x1, [fp, #-0x10]
    // 0x1504d38: stur            x0, [fp, #-0x20]
    // 0x1504d3c: r0 = of()
    //     0x1504d3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1504d40: LoadField: r1 = r0->field_87
    //     0x1504d40: ldur            w1, [x0, #0x87]
    // 0x1504d44: DecompressPointer r1
    //     0x1504d44: add             x1, x1, HEAP, lsl #32
    // 0x1504d48: LoadField: r0 = r1->field_2b
    //     0x1504d48: ldur            w0, [x1, #0x2b]
    // 0x1504d4c: DecompressPointer r0
    //     0x1504d4c: add             x0, x0, HEAP, lsl #32
    // 0x1504d50: stur            x0, [fp, #-0x30]
    // 0x1504d54: r1 = Instance_Color
    //     0x1504d54: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1504d58: d0 = 0.700000
    //     0x1504d58: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1504d5c: ldr             d0, [x17, #0xf48]
    // 0x1504d60: r0 = withOpacity()
    //     0x1504d60: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1504d64: r16 = 14.000000
    //     0x1504d64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1504d68: ldr             x16, [x16, #0x1d8]
    // 0x1504d6c: stp             x16, x0, [SP]
    // 0x1504d70: ldur            x1, [fp, #-0x30]
    // 0x1504d74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1504d74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1504d78: ldr             x4, [x4, #0x9b8]
    // 0x1504d7c: r0 = copyWith()
    //     0x1504d7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1504d80: stur            x0, [fp, #-0x30]
    // 0x1504d84: r0 = TextSpan()
    //     0x1504d84: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1504d88: mov             x3, x0
    // 0x1504d8c: ldur            x0, [fp, #-0x20]
    // 0x1504d90: stur            x3, [fp, #-0x38]
    // 0x1504d94: StoreField: r3->field_b = r0
    //     0x1504d94: stur            w0, [x3, #0xb]
    // 0x1504d98: r0 = Instance__DeferringMouseCursor
    //     0x1504d98: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1504d9c: ArrayStore: r3[0] = r0  ; List_4
    //     0x1504d9c: stur            w0, [x3, #0x17]
    // 0x1504da0: ldur            x1, [fp, #-0x30]
    // 0x1504da4: StoreField: r3->field_7 = r1
    //     0x1504da4: stur            w1, [x3, #7]
    // 0x1504da8: r1 = Null
    //     0x1504da8: mov             x1, NULL
    // 0x1504dac: r2 = 4
    //     0x1504dac: movz            x2, #0x4
    // 0x1504db0: r0 = AllocateArray()
    //     0x1504db0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1504db4: mov             x2, x0
    // 0x1504db8: ldur            x0, [fp, #-0x28]
    // 0x1504dbc: stur            x2, [fp, #-0x20]
    // 0x1504dc0: StoreField: r2->field_f = r0
    //     0x1504dc0: stur            w0, [x2, #0xf]
    // 0x1504dc4: ldur            x0, [fp, #-0x38]
    // 0x1504dc8: StoreField: r2->field_13 = r0
    //     0x1504dc8: stur            w0, [x2, #0x13]
    // 0x1504dcc: r1 = <InlineSpan>
    //     0x1504dcc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1504dd0: ldr             x1, [x1, #0xe40]
    // 0x1504dd4: r0 = AllocateGrowableArray()
    //     0x1504dd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1504dd8: mov             x1, x0
    // 0x1504ddc: ldur            x0, [fp, #-0x20]
    // 0x1504de0: stur            x1, [fp, #-0x28]
    // 0x1504de4: StoreField: r1->field_f = r0
    //     0x1504de4: stur            w0, [x1, #0xf]
    // 0x1504de8: r2 = 4
    //     0x1504de8: movz            x2, #0x4
    // 0x1504dec: StoreField: r1->field_b = r2
    //     0x1504dec: stur            w2, [x1, #0xb]
    // 0x1504df0: r0 = TextSpan()
    //     0x1504df0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1504df4: mov             x1, x0
    // 0x1504df8: ldur            x0, [fp, #-0x28]
    // 0x1504dfc: stur            x1, [fp, #-0x20]
    // 0x1504e00: StoreField: r1->field_f = r0
    //     0x1504e00: stur            w0, [x1, #0xf]
    // 0x1504e04: r0 = Instance__DeferringMouseCursor
    //     0x1504e04: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1504e08: ArrayStore: r1[0] = r0  ; List_4
    //     0x1504e08: stur            w0, [x1, #0x17]
    // 0x1504e0c: r0 = RichText()
    //     0x1504e0c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1504e10: mov             x1, x0
    // 0x1504e14: ldur            x2, [fp, #-0x20]
    // 0x1504e18: stur            x0, [fp, #-0x20]
    // 0x1504e1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1504e1c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1504e20: r0 = RichText()
    //     0x1504e20: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1504e24: r0 = Visibility()
    //     0x1504e24: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1504e28: mov             x3, x0
    // 0x1504e2c: ldur            x0, [fp, #-0x20]
    // 0x1504e30: stur            x3, [fp, #-0x28]
    // 0x1504e34: StoreField: r3->field_b = r0
    //     0x1504e34: stur            w0, [x3, #0xb]
    // 0x1504e38: r0 = Instance_SizedBox
    //     0x1504e38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1504e3c: StoreField: r3->field_f = r0
    //     0x1504e3c: stur            w0, [x3, #0xf]
    // 0x1504e40: ldur            x1, [fp, #-0x18]
    // 0x1504e44: StoreField: r3->field_13 = r1
    //     0x1504e44: stur            w1, [x3, #0x13]
    // 0x1504e48: r4 = false
    //     0x1504e48: add             x4, NULL, #0x30  ; false
    // 0x1504e4c: ArrayStore: r3[0] = r4  ; List_4
    //     0x1504e4c: stur            w4, [x3, #0x17]
    // 0x1504e50: StoreField: r3->field_1b = r4
    //     0x1504e50: stur            w4, [x3, #0x1b]
    // 0x1504e54: StoreField: r3->field_1f = r4
    //     0x1504e54: stur            w4, [x3, #0x1f]
    // 0x1504e58: StoreField: r3->field_23 = r4
    //     0x1504e58: stur            w4, [x3, #0x23]
    // 0x1504e5c: StoreField: r3->field_27 = r4
    //     0x1504e5c: stur            w4, [x3, #0x27]
    // 0x1504e60: StoreField: r3->field_2b = r4
    //     0x1504e60: stur            w4, [x3, #0x2b]
    // 0x1504e64: r1 = <Widget>
    //     0x1504e64: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1504e68: r2 = 18
    //     0x1504e68: movz            x2, #0x12
    // 0x1504e6c: r0 = AllocateArray()
    //     0x1504e6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1504e70: mov             x2, x0
    // 0x1504e74: ldur            x0, [fp, #-0x28]
    // 0x1504e78: stur            x2, [fp, #-0x18]
    // 0x1504e7c: StoreField: r2->field_f = r0
    //     0x1504e7c: stur            w0, [x2, #0xf]
    // 0x1504e80: r16 = Instance_SizedBox
    //     0x1504e80: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x1504e84: ldr             x16, [x16, #0x9f0]
    // 0x1504e88: StoreField: r2->field_13 = r16
    //     0x1504e88: stur            w16, [x2, #0x13]
    // 0x1504e8c: ldur            x1, [fp, #-8]
    // 0x1504e90: r0 = controller()
    //     0x1504e90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504e94: LoadField: r1 = r0->field_4b
    //     0x1504e94: ldur            w1, [x0, #0x4b]
    // 0x1504e98: DecompressPointer r1
    //     0x1504e98: add             x1, x1, HEAP, lsl #32
    // 0x1504e9c: r0 = value()
    //     0x1504e9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1504ea0: LoadField: r1 = r0->field_5f
    //     0x1504ea0: ldur            w1, [x0, #0x5f]
    // 0x1504ea4: DecompressPointer r1
    //     0x1504ea4: add             x1, x1, HEAP, lsl #32
    // 0x1504ea8: cmp             w1, NULL
    // 0x1504eac: b.ne            #0x1504eb8
    // 0x1504eb0: r0 = Null
    //     0x1504eb0: mov             x0, NULL
    // 0x1504eb4: b               #0x1504ec0
    // 0x1504eb8: LoadField: r0 = r1->field_b
    //     0x1504eb8: ldur            w0, [x1, #0xb]
    // 0x1504ebc: DecompressPointer r0
    //     0x1504ebc: add             x0, x0, HEAP, lsl #32
    // 0x1504ec0: cmp             w0, NULL
    // 0x1504ec4: r16 = true
    //     0x1504ec4: add             x16, NULL, #0x20  ; true
    // 0x1504ec8: r17 = false
    //     0x1504ec8: add             x17, NULL, #0x30  ; false
    // 0x1504ecc: csel            x2, x16, x17, ne
    // 0x1504ed0: ldur            x1, [fp, #-0x10]
    // 0x1504ed4: stur            x2, [fp, #-0x20]
    // 0x1504ed8: r0 = of()
    //     0x1504ed8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1504edc: LoadField: r1 = r0->field_87
    //     0x1504edc: ldur            w1, [x0, #0x87]
    // 0x1504ee0: DecompressPointer r1
    //     0x1504ee0: add             x1, x1, HEAP, lsl #32
    // 0x1504ee4: LoadField: r0 = r1->field_2b
    //     0x1504ee4: ldur            w0, [x1, #0x2b]
    // 0x1504ee8: DecompressPointer r0
    //     0x1504ee8: add             x0, x0, HEAP, lsl #32
    // 0x1504eec: r16 = Instance_Color
    //     0x1504eec: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1504ef0: r30 = 14.000000
    //     0x1504ef0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1504ef4: ldr             lr, [lr, #0x1d8]
    // 0x1504ef8: stp             lr, x16, [SP]
    // 0x1504efc: mov             x1, x0
    // 0x1504f00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1504f00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1504f04: ldr             x4, [x4, #0x9b8]
    // 0x1504f08: r0 = copyWith()
    //     0x1504f08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1504f0c: stur            x0, [fp, #-0x28]
    // 0x1504f10: r0 = TextSpan()
    //     0x1504f10: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1504f14: mov             x2, x0
    // 0x1504f18: r0 = "WhatsApp: + 91 - "
    //     0x1504f18: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb30] "WhatsApp: + 91 - "
    //     0x1504f1c: ldr             x0, [x0, #0xb30]
    // 0x1504f20: stur            x2, [fp, #-0x30]
    // 0x1504f24: StoreField: r2->field_b = r0
    //     0x1504f24: stur            w0, [x2, #0xb]
    // 0x1504f28: r0 = Instance__DeferringMouseCursor
    //     0x1504f28: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1504f2c: ArrayStore: r2[0] = r0  ; List_4
    //     0x1504f2c: stur            w0, [x2, #0x17]
    // 0x1504f30: ldur            x1, [fp, #-0x28]
    // 0x1504f34: StoreField: r2->field_7 = r1
    //     0x1504f34: stur            w1, [x2, #7]
    // 0x1504f38: ldur            x1, [fp, #-8]
    // 0x1504f3c: r0 = controller()
    //     0x1504f3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504f40: LoadField: r1 = r0->field_4b
    //     0x1504f40: ldur            w1, [x0, #0x4b]
    // 0x1504f44: DecompressPointer r1
    //     0x1504f44: add             x1, x1, HEAP, lsl #32
    // 0x1504f48: r0 = value()
    //     0x1504f48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1504f4c: LoadField: r1 = r0->field_5f
    //     0x1504f4c: ldur            w1, [x0, #0x5f]
    // 0x1504f50: DecompressPointer r1
    //     0x1504f50: add             x1, x1, HEAP, lsl #32
    // 0x1504f54: cmp             w1, NULL
    // 0x1504f58: b.ne            #0x1504f64
    // 0x1504f5c: r3 = Null
    //     0x1504f5c: mov             x3, NULL
    // 0x1504f60: b               #0x1504f70
    // 0x1504f64: LoadField: r0 = r1->field_b
    //     0x1504f64: ldur            w0, [x1, #0xb]
    // 0x1504f68: DecompressPointer r0
    //     0x1504f68: add             x0, x0, HEAP, lsl #32
    // 0x1504f6c: mov             x3, x0
    // 0x1504f70: ldur            x2, [fp, #-0x18]
    // 0x1504f74: ldur            x1, [fp, #-0x20]
    // 0x1504f78: ldur            x0, [fp, #-0x30]
    // 0x1504f7c: str             x3, [SP]
    // 0x1504f80: r0 = _interpolateSingle()
    //     0x1504f80: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x1504f84: ldur            x1, [fp, #-0x10]
    // 0x1504f88: stur            x0, [fp, #-0x28]
    // 0x1504f8c: r0 = of()
    //     0x1504f8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1504f90: LoadField: r1 = r0->field_87
    //     0x1504f90: ldur            w1, [x0, #0x87]
    // 0x1504f94: DecompressPointer r1
    //     0x1504f94: add             x1, x1, HEAP, lsl #32
    // 0x1504f98: LoadField: r0 = r1->field_2b
    //     0x1504f98: ldur            w0, [x1, #0x2b]
    // 0x1504f9c: DecompressPointer r0
    //     0x1504f9c: add             x0, x0, HEAP, lsl #32
    // 0x1504fa0: stur            x0, [fp, #-0x38]
    // 0x1504fa4: r1 = Instance_Color
    //     0x1504fa4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1504fa8: d0 = 0.700000
    //     0x1504fa8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1504fac: ldr             d0, [x17, #0xf48]
    // 0x1504fb0: r0 = withOpacity()
    //     0x1504fb0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1504fb4: r16 = 14.000000
    //     0x1504fb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1504fb8: ldr             x16, [x16, #0x1d8]
    // 0x1504fbc: stp             x16, x0, [SP]
    // 0x1504fc0: ldur            x1, [fp, #-0x38]
    // 0x1504fc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1504fc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1504fc8: ldr             x4, [x4, #0x9b8]
    // 0x1504fcc: r0 = copyWith()
    //     0x1504fcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1504fd0: stur            x0, [fp, #-0x38]
    // 0x1504fd4: r0 = TextSpan()
    //     0x1504fd4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1504fd8: mov             x3, x0
    // 0x1504fdc: ldur            x0, [fp, #-0x28]
    // 0x1504fe0: stur            x3, [fp, #-0x40]
    // 0x1504fe4: StoreField: r3->field_b = r0
    //     0x1504fe4: stur            w0, [x3, #0xb]
    // 0x1504fe8: r0 = Instance__DeferringMouseCursor
    //     0x1504fe8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1504fec: ArrayStore: r3[0] = r0  ; List_4
    //     0x1504fec: stur            w0, [x3, #0x17]
    // 0x1504ff0: ldur            x1, [fp, #-0x38]
    // 0x1504ff4: StoreField: r3->field_7 = r1
    //     0x1504ff4: stur            w1, [x3, #7]
    // 0x1504ff8: r1 = Null
    //     0x1504ff8: mov             x1, NULL
    // 0x1504ffc: r2 = 4
    //     0x1504ffc: movz            x2, #0x4
    // 0x1505000: r0 = AllocateArray()
    //     0x1505000: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1505004: mov             x2, x0
    // 0x1505008: ldur            x0, [fp, #-0x30]
    // 0x150500c: stur            x2, [fp, #-0x28]
    // 0x1505010: StoreField: r2->field_f = r0
    //     0x1505010: stur            w0, [x2, #0xf]
    // 0x1505014: ldur            x0, [fp, #-0x40]
    // 0x1505018: StoreField: r2->field_13 = r0
    //     0x1505018: stur            w0, [x2, #0x13]
    // 0x150501c: r1 = <InlineSpan>
    //     0x150501c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1505020: ldr             x1, [x1, #0xe40]
    // 0x1505024: r0 = AllocateGrowableArray()
    //     0x1505024: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1505028: mov             x1, x0
    // 0x150502c: ldur            x0, [fp, #-0x28]
    // 0x1505030: stur            x1, [fp, #-0x30]
    // 0x1505034: StoreField: r1->field_f = r0
    //     0x1505034: stur            w0, [x1, #0xf]
    // 0x1505038: r2 = 4
    //     0x1505038: movz            x2, #0x4
    // 0x150503c: StoreField: r1->field_b = r2
    //     0x150503c: stur            w2, [x1, #0xb]
    // 0x1505040: r0 = TextSpan()
    //     0x1505040: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1505044: mov             x1, x0
    // 0x1505048: ldur            x0, [fp, #-0x30]
    // 0x150504c: stur            x1, [fp, #-0x28]
    // 0x1505050: StoreField: r1->field_f = r0
    //     0x1505050: stur            w0, [x1, #0xf]
    // 0x1505054: r0 = Instance__DeferringMouseCursor
    //     0x1505054: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1505058: ArrayStore: r1[0] = r0  ; List_4
    //     0x1505058: stur            w0, [x1, #0x17]
    // 0x150505c: r0 = RichText()
    //     0x150505c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1505060: mov             x1, x0
    // 0x1505064: ldur            x2, [fp, #-0x28]
    // 0x1505068: stur            x0, [fp, #-0x28]
    // 0x150506c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x150506c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1505070: r0 = RichText()
    //     0x1505070: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1505074: r0 = Visibility()
    //     0x1505074: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1505078: mov             x1, x0
    // 0x150507c: ldur            x0, [fp, #-0x28]
    // 0x1505080: StoreField: r1->field_b = r0
    //     0x1505080: stur            w0, [x1, #0xb]
    // 0x1505084: r2 = Instance_SizedBox
    //     0x1505084: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1505088: StoreField: r1->field_f = r2
    //     0x1505088: stur            w2, [x1, #0xf]
    // 0x150508c: ldur            x0, [fp, #-0x20]
    // 0x1505090: StoreField: r1->field_13 = r0
    //     0x1505090: stur            w0, [x1, #0x13]
    // 0x1505094: r3 = false
    //     0x1505094: add             x3, NULL, #0x30  ; false
    // 0x1505098: ArrayStore: r1[0] = r3  ; List_4
    //     0x1505098: stur            w3, [x1, #0x17]
    // 0x150509c: StoreField: r1->field_1b = r3
    //     0x150509c: stur            w3, [x1, #0x1b]
    // 0x15050a0: StoreField: r1->field_1f = r3
    //     0x15050a0: stur            w3, [x1, #0x1f]
    // 0x15050a4: StoreField: r1->field_23 = r3
    //     0x15050a4: stur            w3, [x1, #0x23]
    // 0x15050a8: StoreField: r1->field_27 = r3
    //     0x15050a8: stur            w3, [x1, #0x27]
    // 0x15050ac: StoreField: r1->field_2b = r3
    //     0x15050ac: stur            w3, [x1, #0x2b]
    // 0x15050b0: mov             x0, x1
    // 0x15050b4: ldur            x1, [fp, #-0x18]
    // 0x15050b8: ArrayStore: r1[2] = r0  ; List_4
    //     0x15050b8: add             x25, x1, #0x17
    //     0x15050bc: str             w0, [x25]
    //     0x15050c0: tbz             w0, #0, #0x15050dc
    //     0x15050c4: ldurb           w16, [x1, #-1]
    //     0x15050c8: ldurb           w17, [x0, #-1]
    //     0x15050cc: and             x16, x17, x16, lsr #2
    //     0x15050d0: tst             x16, HEAP, lsr #32
    //     0x15050d4: b.eq            #0x15050dc
    //     0x15050d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15050dc: ldur            x0, [fp, #-0x18]
    // 0x15050e0: r16 = Instance_SizedBox
    //     0x15050e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x15050e4: ldr             x16, [x16, #0x9f0]
    // 0x15050e8: StoreField: r0->field_1b = r16
    //     0x15050e8: stur            w16, [x0, #0x1b]
    // 0x15050ec: ldur            x1, [fp, #-8]
    // 0x15050f0: r0 = controller()
    //     0x15050f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15050f4: LoadField: r1 = r0->field_4b
    //     0x15050f4: ldur            w1, [x0, #0x4b]
    // 0x15050f8: DecompressPointer r1
    //     0x15050f8: add             x1, x1, HEAP, lsl #32
    // 0x15050fc: r0 = value()
    //     0x15050fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1505100: LoadField: r1 = r0->field_5f
    //     0x1505100: ldur            w1, [x0, #0x5f]
    // 0x1505104: DecompressPointer r1
    //     0x1505104: add             x1, x1, HEAP, lsl #32
    // 0x1505108: cmp             w1, NULL
    // 0x150510c: b.ne            #0x1505118
    // 0x1505110: r0 = Null
    //     0x1505110: mov             x0, NULL
    // 0x1505114: b               #0x1505120
    // 0x1505118: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1505118: ldur            w0, [x1, #0x17]
    // 0x150511c: DecompressPointer r0
    //     0x150511c: add             x0, x0, HEAP, lsl #32
    // 0x1505120: cmp             w0, NULL
    // 0x1505124: r16 = true
    //     0x1505124: add             x16, NULL, #0x20  ; true
    // 0x1505128: r17 = false
    //     0x1505128: add             x17, NULL, #0x30  ; false
    // 0x150512c: csel            x2, x16, x17, ne
    // 0x1505130: ldur            x1, [fp, #-0x10]
    // 0x1505134: stur            x2, [fp, #-0x20]
    // 0x1505138: r0 = of()
    //     0x1505138: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150513c: LoadField: r1 = r0->field_87
    //     0x150513c: ldur            w1, [x0, #0x87]
    // 0x1505140: DecompressPointer r1
    //     0x1505140: add             x1, x1, HEAP, lsl #32
    // 0x1505144: LoadField: r0 = r1->field_2b
    //     0x1505144: ldur            w0, [x1, #0x2b]
    // 0x1505148: DecompressPointer r0
    //     0x1505148: add             x0, x0, HEAP, lsl #32
    // 0x150514c: r16 = Instance_Color
    //     0x150514c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1505150: r30 = 14.000000
    //     0x1505150: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1505154: ldr             lr, [lr, #0x1d8]
    // 0x1505158: stp             lr, x16, [SP]
    // 0x150515c: mov             x1, x0
    // 0x1505160: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1505160: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1505164: ldr             x4, [x4, #0x9b8]
    // 0x1505168: r0 = copyWith()
    //     0x1505168: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150516c: stur            x0, [fp, #-0x28]
    // 0x1505170: r0 = TextSpan()
    //     0x1505170: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1505174: mov             x2, x0
    // 0x1505178: r0 = "Customer Support Time: "
    //     0x1505178: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb38] "Customer Support Time: "
    //     0x150517c: ldr             x0, [x0, #0xb38]
    // 0x1505180: stur            x2, [fp, #-0x30]
    // 0x1505184: StoreField: r2->field_b = r0
    //     0x1505184: stur            w0, [x2, #0xb]
    // 0x1505188: r0 = Instance__DeferringMouseCursor
    //     0x1505188: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x150518c: ArrayStore: r2[0] = r0  ; List_4
    //     0x150518c: stur            w0, [x2, #0x17]
    // 0x1505190: ldur            x1, [fp, #-0x28]
    // 0x1505194: StoreField: r2->field_7 = r1
    //     0x1505194: stur            w1, [x2, #7]
    // 0x1505198: ldur            x1, [fp, #-8]
    // 0x150519c: r0 = controller()
    //     0x150519c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15051a0: LoadField: r1 = r0->field_4b
    //     0x15051a0: ldur            w1, [x0, #0x4b]
    // 0x15051a4: DecompressPointer r1
    //     0x15051a4: add             x1, x1, HEAP, lsl #32
    // 0x15051a8: r0 = value()
    //     0x15051a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15051ac: LoadField: r1 = r0->field_5f
    //     0x15051ac: ldur            w1, [x0, #0x5f]
    // 0x15051b0: DecompressPointer r1
    //     0x15051b0: add             x1, x1, HEAP, lsl #32
    // 0x15051b4: cmp             w1, NULL
    // 0x15051b8: b.ne            #0x15051c4
    // 0x15051bc: r3 = Null
    //     0x15051bc: mov             x3, NULL
    // 0x15051c0: b               #0x15051d0
    // 0x15051c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x15051c4: ldur            w0, [x1, #0x17]
    // 0x15051c8: DecompressPointer r0
    //     0x15051c8: add             x0, x0, HEAP, lsl #32
    // 0x15051cc: mov             x3, x0
    // 0x15051d0: ldur            x2, [fp, #-0x18]
    // 0x15051d4: ldur            x1, [fp, #-0x20]
    // 0x15051d8: ldur            x0, [fp, #-0x30]
    // 0x15051dc: str             x3, [SP]
    // 0x15051e0: r0 = _interpolateSingle()
    //     0x15051e0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15051e4: ldur            x1, [fp, #-0x10]
    // 0x15051e8: stur            x0, [fp, #-0x28]
    // 0x15051ec: r0 = of()
    //     0x15051ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15051f0: LoadField: r1 = r0->field_87
    //     0x15051f0: ldur            w1, [x0, #0x87]
    // 0x15051f4: DecompressPointer r1
    //     0x15051f4: add             x1, x1, HEAP, lsl #32
    // 0x15051f8: LoadField: r0 = r1->field_2b
    //     0x15051f8: ldur            w0, [x1, #0x2b]
    // 0x15051fc: DecompressPointer r0
    //     0x15051fc: add             x0, x0, HEAP, lsl #32
    // 0x1505200: stur            x0, [fp, #-0x38]
    // 0x1505204: r1 = Instance_Color
    //     0x1505204: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1505208: d0 = 0.700000
    //     0x1505208: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x150520c: ldr             d0, [x17, #0xf48]
    // 0x1505210: r0 = withOpacity()
    //     0x1505210: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1505214: r16 = 14.000000
    //     0x1505214: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1505218: ldr             x16, [x16, #0x1d8]
    // 0x150521c: stp             x16, x0, [SP]
    // 0x1505220: ldur            x1, [fp, #-0x38]
    // 0x1505224: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1505224: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1505228: ldr             x4, [x4, #0x9b8]
    // 0x150522c: r0 = copyWith()
    //     0x150522c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1505230: stur            x0, [fp, #-0x38]
    // 0x1505234: r0 = TextSpan()
    //     0x1505234: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1505238: mov             x3, x0
    // 0x150523c: ldur            x0, [fp, #-0x28]
    // 0x1505240: stur            x3, [fp, #-0x40]
    // 0x1505244: StoreField: r3->field_b = r0
    //     0x1505244: stur            w0, [x3, #0xb]
    // 0x1505248: r0 = Instance__DeferringMouseCursor
    //     0x1505248: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x150524c: ArrayStore: r3[0] = r0  ; List_4
    //     0x150524c: stur            w0, [x3, #0x17]
    // 0x1505250: ldur            x1, [fp, #-0x38]
    // 0x1505254: StoreField: r3->field_7 = r1
    //     0x1505254: stur            w1, [x3, #7]
    // 0x1505258: r1 = Null
    //     0x1505258: mov             x1, NULL
    // 0x150525c: r2 = 4
    //     0x150525c: movz            x2, #0x4
    // 0x1505260: r0 = AllocateArray()
    //     0x1505260: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1505264: mov             x2, x0
    // 0x1505268: ldur            x0, [fp, #-0x30]
    // 0x150526c: stur            x2, [fp, #-0x28]
    // 0x1505270: StoreField: r2->field_f = r0
    //     0x1505270: stur            w0, [x2, #0xf]
    // 0x1505274: ldur            x0, [fp, #-0x40]
    // 0x1505278: StoreField: r2->field_13 = r0
    //     0x1505278: stur            w0, [x2, #0x13]
    // 0x150527c: r1 = <InlineSpan>
    //     0x150527c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1505280: ldr             x1, [x1, #0xe40]
    // 0x1505284: r0 = AllocateGrowableArray()
    //     0x1505284: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1505288: mov             x1, x0
    // 0x150528c: ldur            x0, [fp, #-0x28]
    // 0x1505290: stur            x1, [fp, #-0x30]
    // 0x1505294: StoreField: r1->field_f = r0
    //     0x1505294: stur            w0, [x1, #0xf]
    // 0x1505298: r2 = 4
    //     0x1505298: movz            x2, #0x4
    // 0x150529c: StoreField: r1->field_b = r2
    //     0x150529c: stur            w2, [x1, #0xb]
    // 0x15052a0: r0 = TextSpan()
    //     0x15052a0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x15052a4: mov             x1, x0
    // 0x15052a8: ldur            x0, [fp, #-0x30]
    // 0x15052ac: stur            x1, [fp, #-0x28]
    // 0x15052b0: StoreField: r1->field_f = r0
    //     0x15052b0: stur            w0, [x1, #0xf]
    // 0x15052b4: r0 = Instance__DeferringMouseCursor
    //     0x15052b4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x15052b8: ArrayStore: r1[0] = r0  ; List_4
    //     0x15052b8: stur            w0, [x1, #0x17]
    // 0x15052bc: r0 = RichText()
    //     0x15052bc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x15052c0: mov             x1, x0
    // 0x15052c4: ldur            x2, [fp, #-0x28]
    // 0x15052c8: stur            x0, [fp, #-0x28]
    // 0x15052cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15052cc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15052d0: r0 = RichText()
    //     0x15052d0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x15052d4: r0 = Visibility()
    //     0x15052d4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15052d8: mov             x1, x0
    // 0x15052dc: ldur            x0, [fp, #-0x28]
    // 0x15052e0: StoreField: r1->field_b = r0
    //     0x15052e0: stur            w0, [x1, #0xb]
    // 0x15052e4: r2 = Instance_SizedBox
    //     0x15052e4: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15052e8: StoreField: r1->field_f = r2
    //     0x15052e8: stur            w2, [x1, #0xf]
    // 0x15052ec: ldur            x0, [fp, #-0x20]
    // 0x15052f0: StoreField: r1->field_13 = r0
    //     0x15052f0: stur            w0, [x1, #0x13]
    // 0x15052f4: r3 = false
    //     0x15052f4: add             x3, NULL, #0x30  ; false
    // 0x15052f8: ArrayStore: r1[0] = r3  ; List_4
    //     0x15052f8: stur            w3, [x1, #0x17]
    // 0x15052fc: StoreField: r1->field_1b = r3
    //     0x15052fc: stur            w3, [x1, #0x1b]
    // 0x1505300: StoreField: r1->field_1f = r3
    //     0x1505300: stur            w3, [x1, #0x1f]
    // 0x1505304: StoreField: r1->field_23 = r3
    //     0x1505304: stur            w3, [x1, #0x23]
    // 0x1505308: StoreField: r1->field_27 = r3
    //     0x1505308: stur            w3, [x1, #0x27]
    // 0x150530c: StoreField: r1->field_2b = r3
    //     0x150530c: stur            w3, [x1, #0x2b]
    // 0x1505310: mov             x0, x1
    // 0x1505314: ldur            x1, [fp, #-0x18]
    // 0x1505318: ArrayStore: r1[4] = r0  ; List_4
    //     0x1505318: add             x25, x1, #0x1f
    //     0x150531c: str             w0, [x25]
    //     0x1505320: tbz             w0, #0, #0x150533c
    //     0x1505324: ldurb           w16, [x1, #-1]
    //     0x1505328: ldurb           w17, [x0, #-1]
    //     0x150532c: and             x16, x17, x16, lsr #2
    //     0x1505330: tst             x16, HEAP, lsr #32
    //     0x1505334: b.eq            #0x150533c
    //     0x1505338: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x150533c: ldur            x0, [fp, #-0x18]
    // 0x1505340: r16 = Instance_SizedBox
    //     0x1505340: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x1505344: ldr             x16, [x16, #0x9f0]
    // 0x1505348: StoreField: r0->field_23 = r16
    //     0x1505348: stur            w16, [x0, #0x23]
    // 0x150534c: ldur            x1, [fp, #-8]
    // 0x1505350: r0 = controller()
    //     0x1505350: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1505354: LoadField: r1 = r0->field_4b
    //     0x1505354: ldur            w1, [x0, #0x4b]
    // 0x1505358: DecompressPointer r1
    //     0x1505358: add             x1, x1, HEAP, lsl #32
    // 0x150535c: r0 = value()
    //     0x150535c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1505360: LoadField: r1 = r0->field_5f
    //     0x1505360: ldur            w1, [x0, #0x5f]
    // 0x1505364: DecompressPointer r1
    //     0x1505364: add             x1, x1, HEAP, lsl #32
    // 0x1505368: cmp             w1, NULL
    // 0x150536c: b.ne            #0x1505378
    // 0x1505370: r0 = Null
    //     0x1505370: mov             x0, NULL
    // 0x1505374: b               #0x1505380
    // 0x1505378: LoadField: r0 = r1->field_f
    //     0x1505378: ldur            w0, [x1, #0xf]
    // 0x150537c: DecompressPointer r0
    //     0x150537c: add             x0, x0, HEAP, lsl #32
    // 0x1505380: cmp             w0, NULL
    // 0x1505384: r16 = true
    //     0x1505384: add             x16, NULL, #0x20  ; true
    // 0x1505388: r17 = false
    //     0x1505388: add             x17, NULL, #0x30  ; false
    // 0x150538c: csel            x2, x16, x17, ne
    // 0x1505390: ldur            x1, [fp, #-0x10]
    // 0x1505394: stur            x2, [fp, #-0x20]
    // 0x1505398: r0 = of()
    //     0x1505398: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150539c: LoadField: r1 = r0->field_87
    //     0x150539c: ldur            w1, [x0, #0x87]
    // 0x15053a0: DecompressPointer r1
    //     0x15053a0: add             x1, x1, HEAP, lsl #32
    // 0x15053a4: LoadField: r0 = r1->field_2b
    //     0x15053a4: ldur            w0, [x1, #0x2b]
    // 0x15053a8: DecompressPointer r0
    //     0x15053a8: add             x0, x0, HEAP, lsl #32
    // 0x15053ac: r16 = Instance_Color
    //     0x15053ac: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15053b0: r30 = 14.000000
    //     0x15053b0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x15053b4: ldr             lr, [lr, #0x1d8]
    // 0x15053b8: stp             lr, x16, [SP]
    // 0x15053bc: mov             x1, x0
    // 0x15053c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15053c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15053c4: ldr             x4, [x4, #0x9b8]
    // 0x15053c8: r0 = copyWith()
    //     0x15053c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15053cc: stur            x0, [fp, #-0x28]
    // 0x15053d0: r0 = TextSpan()
    //     0x15053d0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x15053d4: mov             x2, x0
    // 0x15053d8: r0 = "Email: "
    //     0x15053d8: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb40] "Email: "
    //     0x15053dc: ldr             x0, [x0, #0xb40]
    // 0x15053e0: stur            x2, [fp, #-0x30]
    // 0x15053e4: StoreField: r2->field_b = r0
    //     0x15053e4: stur            w0, [x2, #0xb]
    // 0x15053e8: r0 = Instance__DeferringMouseCursor
    //     0x15053e8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x15053ec: ArrayStore: r2[0] = r0  ; List_4
    //     0x15053ec: stur            w0, [x2, #0x17]
    // 0x15053f0: ldur            x1, [fp, #-0x28]
    // 0x15053f4: StoreField: r2->field_7 = r1
    //     0x15053f4: stur            w1, [x2, #7]
    // 0x15053f8: ldur            x1, [fp, #-8]
    // 0x15053fc: r0 = controller()
    //     0x15053fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1505400: LoadField: r1 = r0->field_4b
    //     0x1505400: ldur            w1, [x0, #0x4b]
    // 0x1505404: DecompressPointer r1
    //     0x1505404: add             x1, x1, HEAP, lsl #32
    // 0x1505408: r0 = value()
    //     0x1505408: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150540c: LoadField: r1 = r0->field_5f
    //     0x150540c: ldur            w1, [x0, #0x5f]
    // 0x1505410: DecompressPointer r1
    //     0x1505410: add             x1, x1, HEAP, lsl #32
    // 0x1505414: cmp             w1, NULL
    // 0x1505418: b.ne            #0x1505424
    // 0x150541c: r3 = Null
    //     0x150541c: mov             x3, NULL
    // 0x1505420: b               #0x1505430
    // 0x1505424: LoadField: r0 = r1->field_f
    //     0x1505424: ldur            w0, [x1, #0xf]
    // 0x1505428: DecompressPointer r0
    //     0x1505428: add             x0, x0, HEAP, lsl #32
    // 0x150542c: mov             x3, x0
    // 0x1505430: ldur            x2, [fp, #-0x18]
    // 0x1505434: ldur            x1, [fp, #-0x20]
    // 0x1505438: ldur            x0, [fp, #-0x30]
    // 0x150543c: str             x3, [SP]
    // 0x1505440: r0 = _interpolateSingle()
    //     0x1505440: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x1505444: ldur            x1, [fp, #-0x10]
    // 0x1505448: stur            x0, [fp, #-0x28]
    // 0x150544c: r0 = of()
    //     0x150544c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1505450: LoadField: r1 = r0->field_87
    //     0x1505450: ldur            w1, [x0, #0x87]
    // 0x1505454: DecompressPointer r1
    //     0x1505454: add             x1, x1, HEAP, lsl #32
    // 0x1505458: LoadField: r0 = r1->field_2b
    //     0x1505458: ldur            w0, [x1, #0x2b]
    // 0x150545c: DecompressPointer r0
    //     0x150545c: add             x0, x0, HEAP, lsl #32
    // 0x1505460: stur            x0, [fp, #-0x38]
    // 0x1505464: r1 = Instance_Color
    //     0x1505464: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1505468: d0 = 0.700000
    //     0x1505468: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x150546c: ldr             d0, [x17, #0xf48]
    // 0x1505470: r0 = withOpacity()
    //     0x1505470: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1505474: r16 = 14.000000
    //     0x1505474: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1505478: ldr             x16, [x16, #0x1d8]
    // 0x150547c: stp             x16, x0, [SP]
    // 0x1505480: ldur            x1, [fp, #-0x38]
    // 0x1505484: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1505484: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1505488: ldr             x4, [x4, #0x9b8]
    // 0x150548c: r0 = copyWith()
    //     0x150548c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1505490: stur            x0, [fp, #-0x38]
    // 0x1505494: r0 = TextSpan()
    //     0x1505494: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1505498: mov             x3, x0
    // 0x150549c: ldur            x0, [fp, #-0x28]
    // 0x15054a0: stur            x3, [fp, #-0x40]
    // 0x15054a4: StoreField: r3->field_b = r0
    //     0x15054a4: stur            w0, [x3, #0xb]
    // 0x15054a8: r0 = Instance__DeferringMouseCursor
    //     0x15054a8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x15054ac: ArrayStore: r3[0] = r0  ; List_4
    //     0x15054ac: stur            w0, [x3, #0x17]
    // 0x15054b0: ldur            x1, [fp, #-0x38]
    // 0x15054b4: StoreField: r3->field_7 = r1
    //     0x15054b4: stur            w1, [x3, #7]
    // 0x15054b8: r1 = Null
    //     0x15054b8: mov             x1, NULL
    // 0x15054bc: r2 = 4
    //     0x15054bc: movz            x2, #0x4
    // 0x15054c0: r0 = AllocateArray()
    //     0x15054c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15054c4: mov             x2, x0
    // 0x15054c8: ldur            x0, [fp, #-0x30]
    // 0x15054cc: stur            x2, [fp, #-0x28]
    // 0x15054d0: StoreField: r2->field_f = r0
    //     0x15054d0: stur            w0, [x2, #0xf]
    // 0x15054d4: ldur            x0, [fp, #-0x40]
    // 0x15054d8: StoreField: r2->field_13 = r0
    //     0x15054d8: stur            w0, [x2, #0x13]
    // 0x15054dc: r1 = <InlineSpan>
    //     0x15054dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x15054e0: ldr             x1, [x1, #0xe40]
    // 0x15054e4: r0 = AllocateGrowableArray()
    //     0x15054e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15054e8: mov             x1, x0
    // 0x15054ec: ldur            x0, [fp, #-0x28]
    // 0x15054f0: stur            x1, [fp, #-0x30]
    // 0x15054f4: StoreField: r1->field_f = r0
    //     0x15054f4: stur            w0, [x1, #0xf]
    // 0x15054f8: r2 = 4
    //     0x15054f8: movz            x2, #0x4
    // 0x15054fc: StoreField: r1->field_b = r2
    //     0x15054fc: stur            w2, [x1, #0xb]
    // 0x1505500: r0 = TextSpan()
    //     0x1505500: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1505504: mov             x1, x0
    // 0x1505508: ldur            x0, [fp, #-0x30]
    // 0x150550c: stur            x1, [fp, #-0x28]
    // 0x1505510: StoreField: r1->field_f = r0
    //     0x1505510: stur            w0, [x1, #0xf]
    // 0x1505514: r0 = Instance__DeferringMouseCursor
    //     0x1505514: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1505518: ArrayStore: r1[0] = r0  ; List_4
    //     0x1505518: stur            w0, [x1, #0x17]
    // 0x150551c: r0 = RichText()
    //     0x150551c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1505520: mov             x1, x0
    // 0x1505524: ldur            x2, [fp, #-0x28]
    // 0x1505528: stur            x0, [fp, #-0x28]
    // 0x150552c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x150552c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1505530: r0 = RichText()
    //     0x1505530: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1505534: r0 = Visibility()
    //     0x1505534: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1505538: mov             x1, x0
    // 0x150553c: ldur            x0, [fp, #-0x28]
    // 0x1505540: StoreField: r1->field_b = r0
    //     0x1505540: stur            w0, [x1, #0xb]
    // 0x1505544: r2 = Instance_SizedBox
    //     0x1505544: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1505548: StoreField: r1->field_f = r2
    //     0x1505548: stur            w2, [x1, #0xf]
    // 0x150554c: ldur            x0, [fp, #-0x20]
    // 0x1505550: StoreField: r1->field_13 = r0
    //     0x1505550: stur            w0, [x1, #0x13]
    // 0x1505554: r3 = false
    //     0x1505554: add             x3, NULL, #0x30  ; false
    // 0x1505558: ArrayStore: r1[0] = r3  ; List_4
    //     0x1505558: stur            w3, [x1, #0x17]
    // 0x150555c: StoreField: r1->field_1b = r3
    //     0x150555c: stur            w3, [x1, #0x1b]
    // 0x1505560: StoreField: r1->field_1f = r3
    //     0x1505560: stur            w3, [x1, #0x1f]
    // 0x1505564: StoreField: r1->field_23 = r3
    //     0x1505564: stur            w3, [x1, #0x23]
    // 0x1505568: StoreField: r1->field_27 = r3
    //     0x1505568: stur            w3, [x1, #0x27]
    // 0x150556c: StoreField: r1->field_2b = r3
    //     0x150556c: stur            w3, [x1, #0x2b]
    // 0x1505570: mov             x0, x1
    // 0x1505574: ldur            x1, [fp, #-0x18]
    // 0x1505578: ArrayStore: r1[6] = r0  ; List_4
    //     0x1505578: add             x25, x1, #0x27
    //     0x150557c: str             w0, [x25]
    //     0x1505580: tbz             w0, #0, #0x150559c
    //     0x1505584: ldurb           w16, [x1, #-1]
    //     0x1505588: ldurb           w17, [x0, #-1]
    //     0x150558c: and             x16, x17, x16, lsr #2
    //     0x1505590: tst             x16, HEAP, lsr #32
    //     0x1505594: b.eq            #0x150559c
    //     0x1505598: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x150559c: ldur            x0, [fp, #-0x18]
    // 0x15055a0: r16 = Instance_SizedBox
    //     0x15055a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x15055a4: ldr             x16, [x16, #0x9f0]
    // 0x15055a8: StoreField: r0->field_2b = r16
    //     0x15055a8: stur            w16, [x0, #0x2b]
    // 0x15055ac: ldur            x1, [fp, #-8]
    // 0x15055b0: r0 = controller()
    //     0x15055b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15055b4: LoadField: r1 = r0->field_4b
    //     0x15055b4: ldur            w1, [x0, #0x4b]
    // 0x15055b8: DecompressPointer r1
    //     0x15055b8: add             x1, x1, HEAP, lsl #32
    // 0x15055bc: r0 = value()
    //     0x15055bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15055c0: LoadField: r1 = r0->field_5f
    //     0x15055c0: ldur            w1, [x0, #0x5f]
    // 0x15055c4: DecompressPointer r1
    //     0x15055c4: add             x1, x1, HEAP, lsl #32
    // 0x15055c8: cmp             w1, NULL
    // 0x15055cc: b.ne            #0x15055d8
    // 0x15055d0: r0 = Null
    //     0x15055d0: mov             x0, NULL
    // 0x15055d4: b               #0x15055e0
    // 0x15055d8: LoadField: r0 = r1->field_13
    //     0x15055d8: ldur            w0, [x1, #0x13]
    // 0x15055dc: DecompressPointer r0
    //     0x15055dc: add             x0, x0, HEAP, lsl #32
    // 0x15055e0: cmp             w0, NULL
    // 0x15055e4: r16 = true
    //     0x15055e4: add             x16, NULL, #0x20  ; true
    // 0x15055e8: r17 = false
    //     0x15055e8: add             x17, NULL, #0x30  ; false
    // 0x15055ec: csel            x2, x16, x17, ne
    // 0x15055f0: ldur            x1, [fp, #-0x10]
    // 0x15055f4: stur            x2, [fp, #-0x20]
    // 0x15055f8: r0 = of()
    //     0x15055f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15055fc: LoadField: r1 = r0->field_87
    //     0x15055fc: ldur            w1, [x0, #0x87]
    // 0x1505600: DecompressPointer r1
    //     0x1505600: add             x1, x1, HEAP, lsl #32
    // 0x1505604: LoadField: r0 = r1->field_2b
    //     0x1505604: ldur            w0, [x1, #0x2b]
    // 0x1505608: DecompressPointer r0
    //     0x1505608: add             x0, x0, HEAP, lsl #32
    // 0x150560c: r16 = Instance_Color
    //     0x150560c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1505610: r30 = 14.000000
    //     0x1505610: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1505614: ldr             lr, [lr, #0x1d8]
    // 0x1505618: stp             lr, x16, [SP]
    // 0x150561c: mov             x1, x0
    // 0x1505620: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1505620: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1505624: ldr             x4, [x4, #0x9b8]
    // 0x1505628: r0 = copyWith()
    //     0x1505628: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150562c: stur            x0, [fp, #-0x28]
    // 0x1505630: r0 = Text()
    //     0x1505630: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1505634: mov             x2, x0
    // 0x1505638: r0 = "Address: "
    //     0x1505638: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb48] "Address: "
    //     0x150563c: ldr             x0, [x0, #0xb48]
    // 0x1505640: stur            x2, [fp, #-0x30]
    // 0x1505644: StoreField: r2->field_b = r0
    //     0x1505644: stur            w0, [x2, #0xb]
    // 0x1505648: ldur            x0, [fp, #-0x28]
    // 0x150564c: StoreField: r2->field_13 = r0
    //     0x150564c: stur            w0, [x2, #0x13]
    // 0x1505650: ldur            x1, [fp, #-8]
    // 0x1505654: r0 = controller()
    //     0x1505654: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1505658: mov             x1, x0
    // 0x150565c: r0 = configData()
    //     0x150565c: bl              #0x13187d8  ; [package:customer_app/app/presentation/controllers/browse/browse_controller.dart] BrowseController::configData
    // 0x1505660: LoadField: r1 = r0->field_5f
    //     0x1505660: ldur            w1, [x0, #0x5f]
    // 0x1505664: DecompressPointer r1
    //     0x1505664: add             x1, x1, HEAP, lsl #32
    // 0x1505668: cmp             w1, NULL
    // 0x150566c: b.ne            #0x1505678
    // 0x1505670: r3 = Null
    //     0x1505670: mov             x3, NULL
    // 0x1505674: b               #0x1505684
    // 0x1505678: LoadField: r0 = r1->field_13
    //     0x1505678: ldur            w0, [x1, #0x13]
    // 0x150567c: DecompressPointer r0
    //     0x150567c: add             x0, x0, HEAP, lsl #32
    // 0x1505680: mov             x3, x0
    // 0x1505684: ldur            x2, [fp, #-0x18]
    // 0x1505688: ldur            x1, [fp, #-0x20]
    // 0x150568c: ldur            x0, [fp, #-0x30]
    // 0x1505690: str             x3, [SP]
    // 0x1505694: r0 = _interpolateSingle()
    //     0x1505694: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x1505698: ldur            x1, [fp, #-0x10]
    // 0x150569c: stur            x0, [fp, #-8]
    // 0x15056a0: r0 = of()
    //     0x15056a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15056a4: LoadField: r1 = r0->field_87
    //     0x15056a4: ldur            w1, [x0, #0x87]
    // 0x15056a8: DecompressPointer r1
    //     0x15056a8: add             x1, x1, HEAP, lsl #32
    // 0x15056ac: LoadField: r0 = r1->field_2b
    //     0x15056ac: ldur            w0, [x1, #0x2b]
    // 0x15056b0: DecompressPointer r0
    //     0x15056b0: add             x0, x0, HEAP, lsl #32
    // 0x15056b4: stur            x0, [fp, #-0x10]
    // 0x15056b8: r1 = Instance_Color
    //     0x15056b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15056bc: d0 = 0.700000
    //     0x15056bc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x15056c0: ldr             d0, [x17, #0xf48]
    // 0x15056c4: r0 = withOpacity()
    //     0x15056c4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x15056c8: r16 = 14.000000
    //     0x15056c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x15056cc: ldr             x16, [x16, #0x1d8]
    // 0x15056d0: stp             x16, x0, [SP]
    // 0x15056d4: ldur            x1, [fp, #-0x10]
    // 0x15056d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15056d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15056dc: ldr             x4, [x4, #0x9b8]
    // 0x15056e0: r0 = copyWith()
    //     0x15056e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15056e4: stur            x0, [fp, #-0x10]
    // 0x15056e8: r0 = Text()
    //     0x15056e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15056ec: mov             x2, x0
    // 0x15056f0: ldur            x0, [fp, #-8]
    // 0x15056f4: stur            x2, [fp, #-0x28]
    // 0x15056f8: StoreField: r2->field_b = r0
    //     0x15056f8: stur            w0, [x2, #0xb]
    // 0x15056fc: ldur            x0, [fp, #-0x10]
    // 0x1505700: StoreField: r2->field_13 = r0
    //     0x1505700: stur            w0, [x2, #0x13]
    // 0x1505704: r1 = <FlexParentData>
    //     0x1505704: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1505708: ldr             x1, [x1, #0xe00]
    // 0x150570c: r0 = Flexible()
    //     0x150570c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1505710: mov             x3, x0
    // 0x1505714: r0 = 1
    //     0x1505714: movz            x0, #0x1
    // 0x1505718: stur            x3, [fp, #-8]
    // 0x150571c: StoreField: r3->field_13 = r0
    //     0x150571c: stur            x0, [x3, #0x13]
    // 0x1505720: r0 = Instance_FlexFit
    //     0x1505720: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x1505724: ldr             x0, [x0, #0xe20]
    // 0x1505728: StoreField: r3->field_1b = r0
    //     0x1505728: stur            w0, [x3, #0x1b]
    // 0x150572c: ldur            x0, [fp, #-0x28]
    // 0x1505730: StoreField: r3->field_b = r0
    //     0x1505730: stur            w0, [x3, #0xb]
    // 0x1505734: r1 = Null
    //     0x1505734: mov             x1, NULL
    // 0x1505738: r2 = 4
    //     0x1505738: movz            x2, #0x4
    // 0x150573c: r0 = AllocateArray()
    //     0x150573c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1505740: mov             x2, x0
    // 0x1505744: ldur            x0, [fp, #-0x30]
    // 0x1505748: stur            x2, [fp, #-0x10]
    // 0x150574c: StoreField: r2->field_f = r0
    //     0x150574c: stur            w0, [x2, #0xf]
    // 0x1505750: ldur            x0, [fp, #-8]
    // 0x1505754: StoreField: r2->field_13 = r0
    //     0x1505754: stur            w0, [x2, #0x13]
    // 0x1505758: r1 = <Widget>
    //     0x1505758: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x150575c: r0 = AllocateGrowableArray()
    //     0x150575c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1505760: mov             x1, x0
    // 0x1505764: ldur            x0, [fp, #-0x10]
    // 0x1505768: stur            x1, [fp, #-8]
    // 0x150576c: StoreField: r1->field_f = r0
    //     0x150576c: stur            w0, [x1, #0xf]
    // 0x1505770: r0 = 4
    //     0x1505770: movz            x0, #0x4
    // 0x1505774: StoreField: r1->field_b = r0
    //     0x1505774: stur            w0, [x1, #0xb]
    // 0x1505778: r0 = Row()
    //     0x1505778: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x150577c: mov             x1, x0
    // 0x1505780: r0 = Instance_Axis
    //     0x1505780: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1505784: stur            x1, [fp, #-0x10]
    // 0x1505788: StoreField: r1->field_f = r0
    //     0x1505788: stur            w0, [x1, #0xf]
    // 0x150578c: r0 = Instance_MainAxisAlignment
    //     0x150578c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1505790: ldr             x0, [x0, #0xa08]
    // 0x1505794: StoreField: r1->field_13 = r0
    //     0x1505794: stur            w0, [x1, #0x13]
    // 0x1505798: r2 = Instance_MainAxisSize
    //     0x1505798: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x150579c: ldr             x2, [x2, #0xa10]
    // 0x15057a0: ArrayStore: r1[0] = r2  ; List_4
    //     0x15057a0: stur            w2, [x1, #0x17]
    // 0x15057a4: r3 = Instance_CrossAxisAlignment
    //     0x15057a4: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bb50] Obj!CrossAxisAlignment@d733c1
    //     0x15057a8: ldr             x3, [x3, #0xb50]
    // 0x15057ac: StoreField: r1->field_1b = r3
    //     0x15057ac: stur            w3, [x1, #0x1b]
    // 0x15057b0: r3 = Instance_VerticalDirection
    //     0x15057b0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15057b4: ldr             x3, [x3, #0xa20]
    // 0x15057b8: StoreField: r1->field_23 = r3
    //     0x15057b8: stur            w3, [x1, #0x23]
    // 0x15057bc: r4 = Instance_TextBaseline
    //     0x15057bc: add             x4, PP, #0x3b, lsl #12  ; [pp+0x3bb58] Obj!TextBaseline@d76621
    //     0x15057c0: ldr             x4, [x4, #0xb58]
    // 0x15057c4: StoreField: r1->field_27 = r4
    //     0x15057c4: stur            w4, [x1, #0x27]
    // 0x15057c8: r4 = Instance_Clip
    //     0x15057c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15057cc: ldr             x4, [x4, #0x38]
    // 0x15057d0: StoreField: r1->field_2b = r4
    //     0x15057d0: stur            w4, [x1, #0x2b]
    // 0x15057d4: StoreField: r1->field_2f = rZR
    //     0x15057d4: stur            xzr, [x1, #0x2f]
    // 0x15057d8: ldur            x5, [fp, #-8]
    // 0x15057dc: StoreField: r1->field_b = r5
    //     0x15057dc: stur            w5, [x1, #0xb]
    // 0x15057e0: r0 = Visibility()
    //     0x15057e0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15057e4: mov             x1, x0
    // 0x15057e8: ldur            x0, [fp, #-0x10]
    // 0x15057ec: StoreField: r1->field_b = r0
    //     0x15057ec: stur            w0, [x1, #0xb]
    // 0x15057f0: r0 = Instance_SizedBox
    //     0x15057f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15057f4: StoreField: r1->field_f = r0
    //     0x15057f4: stur            w0, [x1, #0xf]
    // 0x15057f8: ldur            x0, [fp, #-0x20]
    // 0x15057fc: StoreField: r1->field_13 = r0
    //     0x15057fc: stur            w0, [x1, #0x13]
    // 0x1505800: r0 = false
    //     0x1505800: add             x0, NULL, #0x30  ; false
    // 0x1505804: ArrayStore: r1[0] = r0  ; List_4
    //     0x1505804: stur            w0, [x1, #0x17]
    // 0x1505808: StoreField: r1->field_1b = r0
    //     0x1505808: stur            w0, [x1, #0x1b]
    // 0x150580c: StoreField: r1->field_1f = r0
    //     0x150580c: stur            w0, [x1, #0x1f]
    // 0x1505810: StoreField: r1->field_23 = r0
    //     0x1505810: stur            w0, [x1, #0x23]
    // 0x1505814: StoreField: r1->field_27 = r0
    //     0x1505814: stur            w0, [x1, #0x27]
    // 0x1505818: StoreField: r1->field_2b = r0
    //     0x1505818: stur            w0, [x1, #0x2b]
    // 0x150581c: mov             x0, x1
    // 0x1505820: ldur            x1, [fp, #-0x18]
    // 0x1505824: ArrayStore: r1[8] = r0  ; List_4
    //     0x1505824: add             x25, x1, #0x2f
    //     0x1505828: str             w0, [x25]
    //     0x150582c: tbz             w0, #0, #0x1505848
    //     0x1505830: ldurb           w16, [x1, #-1]
    //     0x1505834: ldurb           w17, [x0, #-1]
    //     0x1505838: and             x16, x17, x16, lsr #2
    //     0x150583c: tst             x16, HEAP, lsr #32
    //     0x1505840: b.eq            #0x1505848
    //     0x1505844: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1505848: r1 = <Widget>
    //     0x1505848: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x150584c: r0 = AllocateGrowableArray()
    //     0x150584c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1505850: mov             x1, x0
    // 0x1505854: ldur            x0, [fp, #-0x18]
    // 0x1505858: stur            x1, [fp, #-8]
    // 0x150585c: StoreField: r1->field_f = r0
    //     0x150585c: stur            w0, [x1, #0xf]
    // 0x1505860: r0 = 18
    //     0x1505860: movz            x0, #0x12
    // 0x1505864: StoreField: r1->field_b = r0
    //     0x1505864: stur            w0, [x1, #0xb]
    // 0x1505868: r0 = Column()
    //     0x1505868: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x150586c: mov             x1, x0
    // 0x1505870: r0 = Instance_Axis
    //     0x1505870: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1505874: stur            x1, [fp, #-0x10]
    // 0x1505878: StoreField: r1->field_f = r0
    //     0x1505878: stur            w0, [x1, #0xf]
    // 0x150587c: r0 = Instance_MainAxisAlignment
    //     0x150587c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1505880: ldr             x0, [x0, #0xa08]
    // 0x1505884: StoreField: r1->field_13 = r0
    //     0x1505884: stur            w0, [x1, #0x13]
    // 0x1505888: r0 = Instance_MainAxisSize
    //     0x1505888: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x150588c: ldr             x0, [x0, #0xa10]
    // 0x1505890: ArrayStore: r1[0] = r0  ; List_4
    //     0x1505890: stur            w0, [x1, #0x17]
    // 0x1505894: r0 = Instance_CrossAxisAlignment
    //     0x1505894: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1505898: ldr             x0, [x0, #0x890]
    // 0x150589c: StoreField: r1->field_1b = r0
    //     0x150589c: stur            w0, [x1, #0x1b]
    // 0x15058a0: r0 = Instance_VerticalDirection
    //     0x15058a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15058a4: ldr             x0, [x0, #0xa20]
    // 0x15058a8: StoreField: r1->field_23 = r0
    //     0x15058a8: stur            w0, [x1, #0x23]
    // 0x15058ac: r0 = Instance_Clip
    //     0x15058ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15058b0: ldr             x0, [x0, #0x38]
    // 0x15058b4: StoreField: r1->field_2b = r0
    //     0x15058b4: stur            w0, [x1, #0x2b]
    // 0x15058b8: StoreField: r1->field_2f = rZR
    //     0x15058b8: stur            xzr, [x1, #0x2f]
    // 0x15058bc: ldur            x0, [fp, #-8]
    // 0x15058c0: StoreField: r1->field_b = r0
    //     0x15058c0: stur            w0, [x1, #0xb]
    // 0x15058c4: r0 = Padding()
    //     0x15058c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15058c8: r1 = Instance_EdgeInsets
    //     0x15058c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x15058cc: ldr             x1, [x1, #0x1f0]
    // 0x15058d0: StoreField: r0->field_f = r1
    //     0x15058d0: stur            w1, [x0, #0xf]
    // 0x15058d4: ldur            x1, [fp, #-0x10]
    // 0x15058d8: StoreField: r0->field_b = r1
    //     0x15058d8: stur            w1, [x0, #0xb]
    // 0x15058dc: LeaveFrame
    //     0x15058dc: mov             SP, fp
    //     0x15058e0: ldp             fp, lr, [SP], #0x10
    // 0x15058e4: ret
    //     0x15058e4: ret             
    // 0x15058e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15058e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15058ec: b               #0x1504c40
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e9a14, size: 0x19c
    // 0x15e9a14: EnterFrame
    //     0x15e9a14: stp             fp, lr, [SP, #-0x10]!
    //     0x15e9a18: mov             fp, SP
    // 0x15e9a1c: AllocStack(0x28)
    //     0x15e9a1c: sub             SP, SP, #0x28
    // 0x15e9a20: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x15e9a20: mov             x0, x2
    //     0x15e9a24: stur            x2, [fp, #-8]
    // 0x15e9a28: CheckStackOverflow
    //     0x15e9a28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e9a2c: cmp             SP, x16
    //     0x15e9a30: b.ls            #0x15e9ba8
    // 0x15e9a34: mov             x1, x0
    // 0x15e9a38: r0 = of()
    //     0x15e9a38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e9a3c: LoadField: r1 = r0->field_87
    //     0x15e9a3c: ldur            w1, [x0, #0x87]
    // 0x15e9a40: DecompressPointer r1
    //     0x15e9a40: add             x1, x1, HEAP, lsl #32
    // 0x15e9a44: LoadField: r0 = r1->field_27
    //     0x15e9a44: ldur            w0, [x1, #0x27]
    // 0x15e9a48: DecompressPointer r0
    //     0x15e9a48: add             x0, x0, HEAP, lsl #32
    // 0x15e9a4c: r16 = Instance_Color
    //     0x15e9a4c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e9a50: r30 = 21.000000
    //     0x15e9a50: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x15e9a54: ldr             lr, [lr, #0x9b0]
    // 0x15e9a58: stp             lr, x16, [SP]
    // 0x15e9a5c: mov             x1, x0
    // 0x15e9a60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15e9a60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15e9a64: ldr             x4, [x4, #0x9b8]
    // 0x15e9a68: r0 = copyWith()
    //     0x15e9a68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e9a6c: stur            x0, [fp, #-0x10]
    // 0x15e9a70: r0 = Text()
    //     0x15e9a70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e9a74: mov             x2, x0
    // 0x15e9a78: r0 = "Contact Us"
    //     0x15e9a78: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ca8] "Contact Us"
    //     0x15e9a7c: ldr             x0, [x0, #0xca8]
    // 0x15e9a80: stur            x2, [fp, #-0x18]
    // 0x15e9a84: StoreField: r2->field_b = r0
    //     0x15e9a84: stur            w0, [x2, #0xb]
    // 0x15e9a88: ldur            x0, [fp, #-0x10]
    // 0x15e9a8c: StoreField: r2->field_13 = r0
    //     0x15e9a8c: stur            w0, [x2, #0x13]
    // 0x15e9a90: ldur            x1, [fp, #-8]
    // 0x15e9a94: r0 = of()
    //     0x15e9a94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e9a98: LoadField: r1 = r0->field_5b
    //     0x15e9a98: ldur            w1, [x0, #0x5b]
    // 0x15e9a9c: DecompressPointer r1
    //     0x15e9a9c: add             x1, x1, HEAP, lsl #32
    // 0x15e9aa0: stur            x1, [fp, #-8]
    // 0x15e9aa4: r0 = ColorFilter()
    //     0x15e9aa4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e9aa8: mov             x1, x0
    // 0x15e9aac: ldur            x0, [fp, #-8]
    // 0x15e9ab0: stur            x1, [fp, #-0x10]
    // 0x15e9ab4: StoreField: r1->field_7 = r0
    //     0x15e9ab4: stur            w0, [x1, #7]
    // 0x15e9ab8: r0 = Instance_BlendMode
    //     0x15e9ab8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9abc: ldr             x0, [x0, #0xb30]
    // 0x15e9ac0: StoreField: r1->field_b = r0
    //     0x15e9ac0: stur            w0, [x1, #0xb]
    // 0x15e9ac4: r0 = 1
    //     0x15e9ac4: movz            x0, #0x1
    // 0x15e9ac8: StoreField: r1->field_13 = r0
    //     0x15e9ac8: stur            x0, [x1, #0x13]
    // 0x15e9acc: r0 = SvgPicture()
    //     0x15e9acc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e9ad0: stur            x0, [fp, #-8]
    // 0x15e9ad4: ldur            x16, [fp, #-0x10]
    // 0x15e9ad8: str             x16, [SP]
    // 0x15e9adc: mov             x1, x0
    // 0x15e9ae0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e9ae0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e9ae4: ldr             x2, [x2, #0xa40]
    // 0x15e9ae8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e9ae8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e9aec: ldr             x4, [x4, #0xa38]
    // 0x15e9af0: r0 = SvgPicture.asset()
    //     0x15e9af0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e9af4: r0 = Align()
    //     0x15e9af4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e9af8: mov             x1, x0
    // 0x15e9afc: r0 = Instance_Alignment
    //     0x15e9afc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e9b00: ldr             x0, [x0, #0xb10]
    // 0x15e9b04: stur            x1, [fp, #-0x10]
    // 0x15e9b08: StoreField: r1->field_f = r0
    //     0x15e9b08: stur            w0, [x1, #0xf]
    // 0x15e9b0c: r0 = 1.000000
    //     0x15e9b0c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9b10: StoreField: r1->field_13 = r0
    //     0x15e9b10: stur            w0, [x1, #0x13]
    // 0x15e9b14: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e9b14: stur            w0, [x1, #0x17]
    // 0x15e9b18: ldur            x0, [fp, #-8]
    // 0x15e9b1c: StoreField: r1->field_b = r0
    //     0x15e9b1c: stur            w0, [x1, #0xb]
    // 0x15e9b20: r0 = InkWell()
    //     0x15e9b20: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e9b24: mov             x3, x0
    // 0x15e9b28: ldur            x0, [fp, #-0x10]
    // 0x15e9b2c: stur            x3, [fp, #-8]
    // 0x15e9b30: StoreField: r3->field_b = r0
    //     0x15e9b30: stur            w0, [x3, #0xb]
    // 0x15e9b34: r1 = Function '<anonymous closure>':.
    //     0x15e9b34: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bb60] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e9b38: ldr             x1, [x1, #0xb60]
    // 0x15e9b3c: r2 = Null
    //     0x15e9b3c: mov             x2, NULL
    // 0x15e9b40: r0 = AllocateClosure()
    //     0x15e9b40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e9b44: ldur            x2, [fp, #-8]
    // 0x15e9b48: StoreField: r2->field_f = r0
    //     0x15e9b48: stur            w0, [x2, #0xf]
    // 0x15e9b4c: r0 = true
    //     0x15e9b4c: add             x0, NULL, #0x20  ; true
    // 0x15e9b50: StoreField: r2->field_43 = r0
    //     0x15e9b50: stur            w0, [x2, #0x43]
    // 0x15e9b54: r1 = Instance_BoxShape
    //     0x15e9b54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e9b58: ldr             x1, [x1, #0x80]
    // 0x15e9b5c: StoreField: r2->field_47 = r1
    //     0x15e9b5c: stur            w1, [x2, #0x47]
    // 0x15e9b60: StoreField: r2->field_6f = r0
    //     0x15e9b60: stur            w0, [x2, #0x6f]
    // 0x15e9b64: r1 = false
    //     0x15e9b64: add             x1, NULL, #0x30  ; false
    // 0x15e9b68: StoreField: r2->field_73 = r1
    //     0x15e9b68: stur            w1, [x2, #0x73]
    // 0x15e9b6c: StoreField: r2->field_83 = r0
    //     0x15e9b6c: stur            w0, [x2, #0x83]
    // 0x15e9b70: StoreField: r2->field_7b = r1
    //     0x15e9b70: stur            w1, [x2, #0x7b]
    // 0x15e9b74: r0 = AppBar()
    //     0x15e9b74: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e9b78: stur            x0, [fp, #-0x10]
    // 0x15e9b7c: ldur            x16, [fp, #-0x18]
    // 0x15e9b80: str             x16, [SP]
    // 0x15e9b84: mov             x1, x0
    // 0x15e9b88: ldur            x2, [fp, #-8]
    // 0x15e9b8c: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e9b8c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e9b90: ldr             x4, [x4, #0xf00]
    // 0x15e9b94: r0 = AppBar()
    //     0x15e9b94: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e9b98: ldur            x0, [fp, #-0x10]
    // 0x15e9b9c: LeaveFrame
    //     0x15e9b9c: mov             SP, fp
    //     0x15e9ba0: ldp             fp, lr, [SP], #0x10
    // 0x15e9ba4: ret
    //     0x15e9ba4: ret             
    // 0x15e9ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e9ba8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e9bac: b               #0x15e9a34
  }
}
