// lib: , url: package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart

// class id: 1049516, size: 0x8
class :: {
}

// class id: 4534, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeReturnIntermediateScreen extends BaseView<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x131cffc, size: 0xd0
    // 0x131cffc: EnterFrame
    //     0x131cffc: stp             fp, lr, [SP, #-0x10]!
    //     0x131d000: mov             fp, SP
    // 0x131d004: AllocStack(0x10)
    //     0x131d004: sub             SP, SP, #0x10
    // 0x131d008: SetupParameters()
    //     0x131d008: ldr             x0, [fp, #0x10]
    //     0x131d00c: ldur            w3, [x0, #0x17]
    //     0x131d010: add             x3, x3, HEAP, lsl #32
    //     0x131d014: stur            x3, [fp, #-8]
    // 0x131d018: CheckStackOverflow
    //     0x131d018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131d01c: cmp             SP, x16
    //     0x131d020: b.ls            #0x131d0c4
    // 0x131d024: LoadField: r1 = r3->field_f
    //     0x131d024: ldur            w1, [x3, #0xf]
    // 0x131d028: DecompressPointer r1
    //     0x131d028: add             x1, x1, HEAP, lsl #32
    // 0x131d02c: r2 = false
    //     0x131d02c: add             x2, NULL, #0x30  ; false
    // 0x131d030: r0 = showLoading()
    //     0x131d030: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x131d034: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x131d034: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x131d038: ldr             x0, [x0, #0x1c80]
    //     0x131d03c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131d040: cmp             w0, w16
    //     0x131d044: b.ne            #0x131d050
    //     0x131d048: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x131d04c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131d050: str             NULL, [SP]
    // 0x131d054: r4 = const [0x1, 0, 0, 0, null]
    //     0x131d054: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x131d058: r0 = GetNavigation.back()
    //     0x131d058: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x131d05c: ldur            x0, [fp, #-8]
    // 0x131d060: LoadField: r1 = r0->field_f
    //     0x131d060: ldur            w1, [x0, #0xf]
    // 0x131d064: DecompressPointer r1
    //     0x131d064: add             x1, x1, HEAP, lsl #32
    // 0x131d068: r0 = controller()
    //     0x131d068: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d06c: mov             x1, x0
    // 0x131d070: r2 = "return_exchange_continue"
    //     0x131d070: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x131d074: ldr             x2, [x2, #0x718]
    // 0x131d078: r0 = ctaPostEvent()
    //     0x131d078: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x131d07c: ldur            x0, [fp, #-8]
    // 0x131d080: LoadField: r1 = r0->field_f
    //     0x131d080: ldur            w1, [x0, #0xf]
    // 0x131d084: DecompressPointer r1
    //     0x131d084: add             x1, x1, HEAP, lsl #32
    // 0x131d088: r0 = controller()
    //     0x131d088: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d08c: mov             x1, x0
    // 0x131d090: r0 = navigateToScreen()
    //     0x131d090: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x131d094: ldur            x0, [fp, #-8]
    // 0x131d098: LoadField: r1 = r0->field_f
    //     0x131d098: ldur            w1, [x0, #0xf]
    // 0x131d09c: DecompressPointer r1
    //     0x131d09c: add             x1, x1, HEAP, lsl #32
    // 0x131d0a0: r0 = controller()
    //     0x131d0a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d0a4: mov             x1, x0
    // 0x131d0a8: r2 = "return_free_gift_continue_clicked"
    //     0x131d0a8: add             x2, PP, #0x36, lsl #12  ; [pp+0x36238] "return_free_gift_continue_clicked"
    //     0x131d0ac: ldr             x2, [x2, #0x238]
    // 0x131d0b0: r0 = ctaPostEvent()
    //     0x131d0b0: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x131d0b4: r0 = Null
    //     0x131d0b4: mov             x0, NULL
    // 0x131d0b8: LeaveFrame
    //     0x131d0b8: mov             SP, fp
    //     0x131d0bc: ldp             fp, lr, [SP], #0x10
    // 0x131d0c0: ret
    //     0x131d0c0: ret             
    // 0x131d0c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131d0c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131d0c8: b               #0x131d024
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x131d0cc, size: 0x1b8
    // 0x131d0cc: EnterFrame
    //     0x131d0cc: stp             fp, lr, [SP, #-0x10]!
    //     0x131d0d0: mov             fp, SP
    // 0x131d0d4: AllocStack(0x28)
    //     0x131d0d4: sub             SP, SP, #0x28
    // 0x131d0d8: SetupParameters()
    //     0x131d0d8: ldr             x0, [fp, #0x18]
    //     0x131d0dc: ldur            w2, [x0, #0x17]
    //     0x131d0e0: add             x2, x2, HEAP, lsl #32
    //     0x131d0e4: stur            x2, [fp, #-8]
    // 0x131d0e8: CheckStackOverflow
    //     0x131d0e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131d0ec: cmp             SP, x16
    //     0x131d0f0: b.ls            #0x131d27c
    // 0x131d0f4: LoadField: r1 = r2->field_f
    //     0x131d0f4: ldur            w1, [x2, #0xf]
    // 0x131d0f8: DecompressPointer r1
    //     0x131d0f8: add             x1, x1, HEAP, lsl #32
    // 0x131d0fc: r0 = controller()
    //     0x131d0fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d100: LoadField: r1 = r0->field_4f
    //     0x131d100: ldur            w1, [x0, #0x4f]
    // 0x131d104: DecompressPointer r1
    //     0x131d104: add             x1, x1, HEAP, lsl #32
    // 0x131d108: r0 = value()
    //     0x131d108: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d10c: LoadField: r1 = r0->field_b
    //     0x131d10c: ldur            w1, [x0, #0xb]
    // 0x131d110: DecompressPointer r1
    //     0x131d110: add             x1, x1, HEAP, lsl #32
    // 0x131d114: cmp             w1, NULL
    // 0x131d118: b.ne            #0x131d124
    // 0x131d11c: d0 = inf
    //     0x131d11c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d120: b               #0x131d18c
    // 0x131d124: LoadField: r0 = r1->field_3f
    //     0x131d124: ldur            w0, [x1, #0x3f]
    // 0x131d128: DecompressPointer r0
    //     0x131d128: add             x0, x0, HEAP, lsl #32
    // 0x131d12c: cmp             w0, NULL
    // 0x131d130: b.eq            #0x131d188
    // 0x131d134: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x131d134: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x131d138: ldr             x0, [x0, #0x1c80]
    //     0x131d13c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131d140: cmp             w0, w16
    //     0x131d144: b.ne            #0x131d150
    //     0x131d148: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x131d14c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131d150: r0 = GetNavigation.size()
    //     0x131d150: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x131d154: LoadField: d0 = r0->field_f
    //     0x131d154: ldur            d0, [x0, #0xf]
    // 0x131d158: d1 = 0.430000
    //     0x131d158: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x131d15c: ldr             d1, [x17, #0xb8]
    // 0x131d160: fmul            d2, d0, d1
    // 0x131d164: stur            d2, [fp, #-0x28]
    // 0x131d168: r0 = BoxConstraints()
    //     0x131d168: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x131d16c: StoreField: r0->field_7 = rZR
    //     0x131d16c: stur            xzr, [x0, #7]
    // 0x131d170: d0 = inf
    //     0x131d170: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d174: StoreField: r0->field_f = d0
    //     0x131d174: stur            d0, [x0, #0xf]
    // 0x131d178: ArrayStore: r0[0] = rZR  ; List_8
    //     0x131d178: stur            xzr, [x0, #0x17]
    // 0x131d17c: ldur            d0, [fp, #-0x28]
    // 0x131d180: StoreField: r0->field_1f = d0
    //     0x131d180: stur            d0, [x0, #0x1f]
    // 0x131d184: b               #0x131d1dc
    // 0x131d188: d0 = inf
    //     0x131d188: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d18c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x131d18c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x131d190: ldr             x0, [x0, #0x1c80]
    //     0x131d194: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131d198: cmp             w0, w16
    //     0x131d19c: b.ne            #0x131d1a8
    //     0x131d1a0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x131d1a4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131d1a8: r0 = GetNavigation.size()
    //     0x131d1a8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x131d1ac: LoadField: d0 = r0->field_f
    //     0x131d1ac: ldur            d0, [x0, #0xf]
    // 0x131d1b0: d1 = 0.300000
    //     0x131d1b0: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x131d1b4: ldr             d1, [x17, #0x658]
    // 0x131d1b8: fmul            d2, d0, d1
    // 0x131d1bc: stur            d2, [fp, #-0x28]
    // 0x131d1c0: r0 = BoxConstraints()
    //     0x131d1c0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x131d1c4: StoreField: r0->field_7 = rZR
    //     0x131d1c4: stur            xzr, [x0, #7]
    // 0x131d1c8: d0 = inf
    //     0x131d1c8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d1cc: StoreField: r0->field_f = d0
    //     0x131d1cc: stur            d0, [x0, #0xf]
    // 0x131d1d0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x131d1d0: stur            xzr, [x0, #0x17]
    // 0x131d1d4: ldur            d0, [fp, #-0x28]
    // 0x131d1d8: StoreField: r0->field_1f = d0
    //     0x131d1d8: stur            d0, [x0, #0x1f]
    // 0x131d1dc: ldur            x2, [fp, #-8]
    // 0x131d1e0: stur            x0, [fp, #-0x10]
    // 0x131d1e4: LoadField: r1 = r2->field_f
    //     0x131d1e4: ldur            w1, [x2, #0xf]
    // 0x131d1e8: DecompressPointer r1
    //     0x131d1e8: add             x1, x1, HEAP, lsl #32
    // 0x131d1ec: r0 = controller()
    //     0x131d1ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d1f0: LoadField: r1 = r0->field_4f
    //     0x131d1f0: ldur            w1, [x0, #0x4f]
    // 0x131d1f4: DecompressPointer r1
    //     0x131d1f4: add             x1, x1, HEAP, lsl #32
    // 0x131d1f8: r0 = value()
    //     0x131d1f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d1fc: LoadField: r3 = r0->field_b
    //     0x131d1fc: ldur            w3, [x0, #0xb]
    // 0x131d200: DecompressPointer r3
    //     0x131d200: add             x3, x3, HEAP, lsl #32
    // 0x131d204: ldur            x2, [fp, #-8]
    // 0x131d208: stur            x3, [fp, #-0x18]
    // 0x131d20c: r1 = Function '<anonymous closure>':.
    //     0x131d20c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37af8] AnonymousClosure: (0x131cffc), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x131d210: ldr             x1, [x1, #0xaf8]
    // 0x131d214: r0 = AllocateClosure()
    //     0x131d214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131d218: stur            x0, [fp, #-8]
    // 0x131d21c: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x131d21c: bl              #0x8b104c  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x131d220: mov             x1, x0
    // 0x131d224: ldur            x0, [fp, #-8]
    // 0x131d228: stur            x1, [fp, #-0x20]
    // 0x131d22c: StoreField: r1->field_b = r0
    //     0x131d22c: stur            w0, [x1, #0xb]
    // 0x131d230: r0 = "Confirm Return"
    //     0x131d230: add             x0, PP, #0x36, lsl #12  ; [pp+0x36220] "Confirm Return"
    //     0x131d234: ldr             x0, [x0, #0x220]
    // 0x131d238: StoreField: r1->field_13 = r0
    //     0x131d238: stur            w0, [x1, #0x13]
    // 0x131d23c: r0 = "Returning this product requires returning the free gift as well"
    //     0x131d23c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36228] "Returning this product requires returning the free gift as well"
    //     0x131d240: ldr             x0, [x0, #0x228]
    // 0x131d244: ArrayStore: r1[0] = r0  ; List_4
    //     0x131d244: stur            w0, [x1, #0x17]
    // 0x131d248: r0 = "return_order_intermediate"
    //     0x131d248: add             x0, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0x131d24c: ldr             x0, [x0, #0xb00]
    // 0x131d250: StoreField: r1->field_f = r0
    //     0x131d250: stur            w0, [x1, #0xf]
    // 0x131d254: ldur            x0, [fp, #-0x18]
    // 0x131d258: StoreField: r1->field_23 = r0
    //     0x131d258: stur            w0, [x1, #0x23]
    // 0x131d25c: r0 = ConstrainedBox()
    //     0x131d25c: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x131d260: ldur            x1, [fp, #-0x10]
    // 0x131d264: StoreField: r0->field_f = r1
    //     0x131d264: stur            w1, [x0, #0xf]
    // 0x131d268: ldur            x1, [fp, #-0x20]
    // 0x131d26c: StoreField: r0->field_b = r1
    //     0x131d26c: stur            w1, [x0, #0xb]
    // 0x131d270: LeaveFrame
    //     0x131d270: mov             SP, fp
    //     0x131d274: ldp             fp, lr, [SP], #0x10
    // 0x131d278: ret
    //     0x131d278: ret             
    // 0x131d27c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131d27c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131d280: b               #0x131d0f4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x131d284, size: 0x384
    // 0x131d284: EnterFrame
    //     0x131d284: stp             fp, lr, [SP, #-0x10]!
    //     0x131d288: mov             fp, SP
    // 0x131d28c: AllocStack(0x38)
    //     0x131d28c: sub             SP, SP, #0x38
    // 0x131d290: SetupParameters()
    //     0x131d290: ldr             x0, [fp, #0x10]
    //     0x131d294: ldur            w2, [x0, #0x17]
    //     0x131d298: add             x2, x2, HEAP, lsl #32
    //     0x131d29c: stur            x2, [fp, #-8]
    // 0x131d2a0: CheckStackOverflow
    //     0x131d2a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131d2a4: cmp             SP, x16
    //     0x131d2a8: b.ls            #0x131d600
    // 0x131d2ac: LoadField: r1 = r2->field_f
    //     0x131d2ac: ldur            w1, [x2, #0xf]
    // 0x131d2b0: DecompressPointer r1
    //     0x131d2b0: add             x1, x1, HEAP, lsl #32
    // 0x131d2b4: r0 = controller()
    //     0x131d2b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d2b8: mov             x1, x0
    // 0x131d2bc: r0 = couponType()
    //     0x131d2bc: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x131d2c0: LoadField: r1 = r0->field_b
    //     0x131d2c0: ldur            w1, [x0, #0xb]
    // 0x131d2c4: DecompressPointer r1
    //     0x131d2c4: add             x1, x1, HEAP, lsl #32
    // 0x131d2c8: cmp             w1, NULL
    // 0x131d2cc: b.ne            #0x131d2d8
    // 0x131d2d0: ldur            x2, [fp, #-8]
    // 0x131d2d4: b               #0x131d48c
    // 0x131d2d8: LoadField: r0 = r1->field_7
    //     0x131d2d8: ldur            w0, [x1, #7]
    // 0x131d2dc: DecompressPointer r0
    //     0x131d2dc: add             x0, x0, HEAP, lsl #32
    // 0x131d2e0: cmp             w0, NULL
    // 0x131d2e4: b.eq            #0x131d488
    // 0x131d2e8: ldur            x2, [fp, #-8]
    // 0x131d2ec: LoadField: r1 = r2->field_f
    //     0x131d2ec: ldur            w1, [x2, #0xf]
    // 0x131d2f0: DecompressPointer r1
    //     0x131d2f0: add             x1, x1, HEAP, lsl #32
    // 0x131d2f4: r0 = controller()
    //     0x131d2f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d2f8: LoadField: r1 = r0->field_73
    //     0x131d2f8: ldur            w1, [x0, #0x73]
    // 0x131d2fc: DecompressPointer r1
    //     0x131d2fc: add             x1, x1, HEAP, lsl #32
    // 0x131d300: r0 = value()
    //     0x131d300: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d304: r1 = LoadClassIdInstr(r0)
    //     0x131d304: ldur            x1, [x0, #-1]
    //     0x131d308: ubfx            x1, x1, #0xc, #0x14
    // 0x131d30c: r16 = "return"
    //     0x131d30c: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x131d310: ldr             x16, [x16, #0x9b8]
    // 0x131d314: stp             x16, x0, [SP]
    // 0x131d318: mov             x0, x1
    // 0x131d31c: mov             lr, x0
    // 0x131d320: ldr             lr, [x21, lr, lsl #3]
    // 0x131d324: blr             lr
    // 0x131d328: tbnz            w0, #4, #0x131d41c
    // 0x131d32c: ldur            x2, [fp, #-8]
    // 0x131d330: LoadField: r1 = r2->field_f
    //     0x131d330: ldur            w1, [x2, #0xf]
    // 0x131d334: DecompressPointer r1
    //     0x131d334: add             x1, x1, HEAP, lsl #32
    // 0x131d338: r0 = controller()
    //     0x131d338: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d33c: LoadField: r1 = r0->field_4f
    //     0x131d33c: ldur            w1, [x0, #0x4f]
    // 0x131d340: DecompressPointer r1
    //     0x131d340: add             x1, x1, HEAP, lsl #32
    // 0x131d344: r0 = value()
    //     0x131d344: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d348: LoadField: r1 = r0->field_b
    //     0x131d348: ldur            w1, [x0, #0xb]
    // 0x131d34c: DecompressPointer r1
    //     0x131d34c: add             x1, x1, HEAP, lsl #32
    // 0x131d350: cmp             w1, NULL
    // 0x131d354: b.ne            #0x131d360
    // 0x131d358: r0 = Null
    //     0x131d358: mov             x0, NULL
    // 0x131d35c: b               #0x131d384
    // 0x131d360: LoadField: r0 = r1->field_3f
    //     0x131d360: ldur            w0, [x1, #0x3f]
    // 0x131d364: DecompressPointer r0
    //     0x131d364: add             x0, x0, HEAP, lsl #32
    // 0x131d368: cmp             w0, NULL
    // 0x131d36c: b.ne            #0x131d378
    // 0x131d370: r0 = Null
    //     0x131d370: mov             x0, NULL
    // 0x131d374: b               #0x131d384
    // 0x131d378: LoadField: r1 = r0->field_2b
    //     0x131d378: ldur            w1, [x0, #0x2b]
    // 0x131d37c: DecompressPointer r1
    //     0x131d37c: add             x1, x1, HEAP, lsl #32
    // 0x131d380: mov             x0, x1
    // 0x131d384: cmp             w0, NULL
    // 0x131d388: b.ne            #0x131d394
    // 0x131d38c: ldur            x2, [fp, #-8]
    // 0x131d390: b               #0x131d3e4
    // 0x131d394: tbnz            w0, #4, #0x131d3e0
    // 0x131d398: ldur            x2, [fp, #-8]
    // 0x131d39c: LoadField: r0 = r2->field_13
    //     0x131d39c: ldur            w0, [x2, #0x13]
    // 0x131d3a0: DecompressPointer r0
    //     0x131d3a0: add             x0, x0, HEAP, lsl #32
    // 0x131d3a4: stur            x0, [fp, #-0x10]
    // 0x131d3a8: r1 = Function '<anonymous closure>':.
    //     0x131d3a8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37ae0] AnonymousClosure: (0x131d684), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x131d3ac: ldr             x1, [x1, #0xae0]
    // 0x131d3b0: r0 = AllocateClosure()
    //     0x131d3b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131d3b4: stp             x0, NULL, [SP, #0x18]
    // 0x131d3b8: ldur            x16, [fp, #-0x10]
    // 0x131d3bc: r30 = Instance_RoundedRectangleBorder
    //     0x131d3bc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x131d3c0: ldr             lr, [lr, #0xd68]
    // 0x131d3c4: stp             lr, x16, [SP, #8]
    // 0x131d3c8: r16 = true
    //     0x131d3c8: add             x16, NULL, #0x20  ; true
    // 0x131d3cc: str             x16, [SP]
    // 0x131d3d0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x131d3d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x131d3d4: ldr             x4, [x4, #0xd70]
    // 0x131d3d8: r0 = showModalBottomSheet()
    //     0x131d3d8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x131d3dc: b               #0x131d5f0
    // 0x131d3e0: ldur            x2, [fp, #-8]
    // 0x131d3e4: LoadField: r1 = r2->field_f
    //     0x131d3e4: ldur            w1, [x2, #0xf]
    // 0x131d3e8: DecompressPointer r1
    //     0x131d3e8: add             x1, x1, HEAP, lsl #32
    // 0x131d3ec: r0 = controller()
    //     0x131d3ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d3f0: mov             x1, x0
    // 0x131d3f4: r2 = "return_exchange_continue"
    //     0x131d3f4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x131d3f8: ldr             x2, [x2, #0x718]
    // 0x131d3fc: r0 = ctaPostEvent()
    //     0x131d3fc: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x131d400: ldur            x2, [fp, #-8]
    // 0x131d404: LoadField: r1 = r2->field_f
    //     0x131d404: ldur            w1, [x2, #0xf]
    // 0x131d408: DecompressPointer r1
    //     0x131d408: add             x1, x1, HEAP, lsl #32
    // 0x131d40c: r0 = controller()
    //     0x131d40c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d410: mov             x1, x0
    // 0x131d414: r0 = navigateToScreen()
    //     0x131d414: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x131d418: b               #0x131d5f0
    // 0x131d41c: ldur            x2, [fp, #-8]
    // 0x131d420: LoadField: r1 = r2->field_f
    //     0x131d420: ldur            w1, [x2, #0xf]
    // 0x131d424: DecompressPointer r1
    //     0x131d424: add             x1, x1, HEAP, lsl #32
    // 0x131d428: r0 = controller()
    //     0x131d428: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d42c: mov             x1, x0
    // 0x131d430: r2 = "CONTINUE"
    //     0x131d430: add             x2, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x131d434: ldr             x2, [x2, #0xac8]
    // 0x131d438: r0 = exchangePopupPostEvent()
    //     0x131d438: bl              #0x1318d64  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::exchangePopupPostEvent
    // 0x131d43c: ldur            x2, [fp, #-8]
    // 0x131d440: LoadField: r0 = r2->field_13
    //     0x131d440: ldur            w0, [x2, #0x13]
    // 0x131d444: DecompressPointer r0
    //     0x131d444: add             x0, x0, HEAP, lsl #32
    // 0x131d448: stur            x0, [fp, #-0x10]
    // 0x131d44c: r1 = Function '<anonymous closure>':.
    //     0x131d44c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37ae8] AnonymousClosure: (0x131d608), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x131d450: ldr             x1, [x1, #0xae8]
    // 0x131d454: r2 = Null
    //     0x131d454: mov             x2, NULL
    // 0x131d458: r0 = AllocateClosure()
    //     0x131d458: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131d45c: stp             x0, NULL, [SP, #0x18]
    // 0x131d460: ldur            x16, [fp, #-0x10]
    // 0x131d464: r30 = Instance_RoundedRectangleBorder
    //     0x131d464: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x131d468: ldr             lr, [lr, #0xd68]
    // 0x131d46c: stp             lr, x16, [SP, #8]
    // 0x131d470: r16 = true
    //     0x131d470: add             x16, NULL, #0x20  ; true
    // 0x131d474: str             x16, [SP]
    // 0x131d478: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x131d478: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x131d47c: ldr             x4, [x4, #0xd70]
    // 0x131d480: r0 = showModalBottomSheet()
    //     0x131d480: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x131d484: b               #0x131d5f0
    // 0x131d488: ldur            x2, [fp, #-8]
    // 0x131d48c: LoadField: r1 = r2->field_f
    //     0x131d48c: ldur            w1, [x2, #0xf]
    // 0x131d490: DecompressPointer r1
    //     0x131d490: add             x1, x1, HEAP, lsl #32
    // 0x131d494: r0 = controller()
    //     0x131d494: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d498: LoadField: r1 = r0->field_73
    //     0x131d498: ldur            w1, [x0, #0x73]
    // 0x131d49c: DecompressPointer r1
    //     0x131d49c: add             x1, x1, HEAP, lsl #32
    // 0x131d4a0: r0 = value()
    //     0x131d4a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d4a4: r1 = LoadClassIdInstr(r0)
    //     0x131d4a4: ldur            x1, [x0, #-1]
    //     0x131d4a8: ubfx            x1, x1, #0xc, #0x14
    // 0x131d4ac: r16 = "return"
    //     0x131d4ac: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x131d4b0: ldr             x16, [x16, #0x9b8]
    // 0x131d4b4: stp             x16, x0, [SP]
    // 0x131d4b8: mov             x0, x1
    // 0x131d4bc: mov             lr, x0
    // 0x131d4c0: ldr             lr, [x21, lr, lsl #3]
    // 0x131d4c4: blr             lr
    // 0x131d4c8: tbnz            w0, #4, #0x131d5b8
    // 0x131d4cc: ldur            x2, [fp, #-8]
    // 0x131d4d0: LoadField: r1 = r2->field_f
    //     0x131d4d0: ldur            w1, [x2, #0xf]
    // 0x131d4d4: DecompressPointer r1
    //     0x131d4d4: add             x1, x1, HEAP, lsl #32
    // 0x131d4d8: r0 = controller()
    //     0x131d4d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d4dc: mov             x1, x0
    // 0x131d4e0: r0 = exchangeCheckoutResponse()
    //     0x131d4e0: bl              #0x8fc9bc  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_controller.dart] ExchangeCheckoutController::exchangeCheckoutResponse
    // 0x131d4e4: LoadField: r1 = r0->field_b
    //     0x131d4e4: ldur            w1, [x0, #0xb]
    // 0x131d4e8: DecompressPointer r1
    //     0x131d4e8: add             x1, x1, HEAP, lsl #32
    // 0x131d4ec: cmp             w1, NULL
    // 0x131d4f0: b.ne            #0x131d4fc
    // 0x131d4f4: r0 = Null
    //     0x131d4f4: mov             x0, NULL
    // 0x131d4f8: b               #0x131d520
    // 0x131d4fc: LoadField: r0 = r1->field_3f
    //     0x131d4fc: ldur            w0, [x1, #0x3f]
    // 0x131d500: DecompressPointer r0
    //     0x131d500: add             x0, x0, HEAP, lsl #32
    // 0x131d504: cmp             w0, NULL
    // 0x131d508: b.ne            #0x131d514
    // 0x131d50c: r0 = Null
    //     0x131d50c: mov             x0, NULL
    // 0x131d510: b               #0x131d520
    // 0x131d514: LoadField: r1 = r0->field_2b
    //     0x131d514: ldur            w1, [x0, #0x2b]
    // 0x131d518: DecompressPointer r1
    //     0x131d518: add             x1, x1, HEAP, lsl #32
    // 0x131d51c: mov             x0, x1
    // 0x131d520: cmp             w0, NULL
    // 0x131d524: b.ne            #0x131d530
    // 0x131d528: ldur            x2, [fp, #-8]
    // 0x131d52c: b               #0x131d580
    // 0x131d530: tbnz            w0, #4, #0x131d57c
    // 0x131d534: ldur            x2, [fp, #-8]
    // 0x131d538: LoadField: r0 = r2->field_13
    //     0x131d538: ldur            w0, [x2, #0x13]
    // 0x131d53c: DecompressPointer r0
    //     0x131d53c: add             x0, x0, HEAP, lsl #32
    // 0x131d540: stur            x0, [fp, #-0x10]
    // 0x131d544: r1 = Function '<anonymous closure>':.
    //     0x131d544: add             x1, PP, #0x37, lsl #12  ; [pp+0x37af0] AnonymousClosure: (0x131d0cc), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x131d548: ldr             x1, [x1, #0xaf0]
    // 0x131d54c: r0 = AllocateClosure()
    //     0x131d54c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131d550: stp             x0, NULL, [SP, #0x18]
    // 0x131d554: ldur            x16, [fp, #-0x10]
    // 0x131d558: r30 = Instance_RoundedRectangleBorder
    //     0x131d558: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x131d55c: ldr             lr, [lr, #0xd68]
    // 0x131d560: stp             lr, x16, [SP, #8]
    // 0x131d564: r16 = true
    //     0x131d564: add             x16, NULL, #0x20  ; true
    // 0x131d568: str             x16, [SP]
    // 0x131d56c: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x131d56c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x131d570: ldr             x4, [x4, #0xd70]
    // 0x131d574: r0 = showModalBottomSheet()
    //     0x131d574: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x131d578: b               #0x131d5f0
    // 0x131d57c: ldur            x2, [fp, #-8]
    // 0x131d580: LoadField: r1 = r2->field_f
    //     0x131d580: ldur            w1, [x2, #0xf]
    // 0x131d584: DecompressPointer r1
    //     0x131d584: add             x1, x1, HEAP, lsl #32
    // 0x131d588: r0 = controller()
    //     0x131d588: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d58c: mov             x1, x0
    // 0x131d590: r2 = "return_exchange_continue"
    //     0x131d590: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x131d594: ldr             x2, [x2, #0x718]
    // 0x131d598: r0 = ctaPostEvent()
    //     0x131d598: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x131d59c: ldur            x0, [fp, #-8]
    // 0x131d5a0: LoadField: r1 = r0->field_f
    //     0x131d5a0: ldur            w1, [x0, #0xf]
    // 0x131d5a4: DecompressPointer r1
    //     0x131d5a4: add             x1, x1, HEAP, lsl #32
    // 0x131d5a8: r0 = controller()
    //     0x131d5a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d5ac: mov             x1, x0
    // 0x131d5b0: r0 = navigateToScreen()
    //     0x131d5b0: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x131d5b4: b               #0x131d5f0
    // 0x131d5b8: ldur            x0, [fp, #-8]
    // 0x131d5bc: LoadField: r1 = r0->field_f
    //     0x131d5bc: ldur            w1, [x0, #0xf]
    // 0x131d5c0: DecompressPointer r1
    //     0x131d5c0: add             x1, x1, HEAP, lsl #32
    // 0x131d5c4: r0 = controller()
    //     0x131d5c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d5c8: mov             x1, x0
    // 0x131d5cc: r2 = "return_exchange_continue"
    //     0x131d5cc: add             x2, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x131d5d0: ldr             x2, [x2, #0x718]
    // 0x131d5d4: r0 = ctaPostEvent()
    //     0x131d5d4: bl              #0x131c814  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::ctaPostEvent
    // 0x131d5d8: ldur            x0, [fp, #-8]
    // 0x131d5dc: LoadField: r1 = r0->field_f
    //     0x131d5dc: ldur            w1, [x0, #0xf]
    // 0x131d5e0: DecompressPointer r1
    //     0x131d5e0: add             x1, x1, HEAP, lsl #32
    // 0x131d5e4: r0 = controller()
    //     0x131d5e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d5e8: mov             x1, x0
    // 0x131d5ec: r0 = navigateToScreen()
    //     0x131d5ec: bl              #0x131911c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_intermediate_controller.dart] ExchangeController::navigateToScreen
    // 0x131d5f0: r0 = Null
    //     0x131d5f0: mov             x0, NULL
    // 0x131d5f4: LeaveFrame
    //     0x131d5f4: mov             SP, fp
    //     0x131d5f8: ldp             fp, lr, [SP], #0x10
    // 0x131d5fc: ret
    //     0x131d5fc: ret             
    // 0x131d600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131d600: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131d604: b               #0x131d2ac
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x131d608, size: 0x7c
    // 0x131d608: EnterFrame
    //     0x131d608: stp             fp, lr, [SP, #-0x10]!
    //     0x131d60c: mov             fp, SP
    // 0x131d610: AllocStack(0x10)
    //     0x131d610: sub             SP, SP, #0x10
    // 0x131d614: CheckStackOverflow
    //     0x131d614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131d618: cmp             SP, x16
    //     0x131d61c: b.ls            #0x131d67c
    // 0x131d620: ldr             x1, [fp, #0x10]
    // 0x131d624: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x131d624: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x131d628: r0 = _of()
    //     0x131d628: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x131d62c: LoadField: r1 = r0->field_23
    //     0x131d62c: ldur            w1, [x0, #0x23]
    // 0x131d630: DecompressPointer r1
    //     0x131d630: add             x1, x1, HEAP, lsl #32
    // 0x131d634: LoadField: d0 = r1->field_1f
    //     0x131d634: ldur            d0, [x1, #0x1f]
    // 0x131d638: stur            d0, [fp, #-0x10]
    // 0x131d63c: r0 = EdgeInsets()
    //     0x131d63c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x131d640: stur            x0, [fp, #-8]
    // 0x131d644: StoreField: r0->field_7 = rZR
    //     0x131d644: stur            xzr, [x0, #7]
    // 0x131d648: StoreField: r0->field_f = rZR
    //     0x131d648: stur            xzr, [x0, #0xf]
    // 0x131d64c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x131d64c: stur            xzr, [x0, #0x17]
    // 0x131d650: ldur            d0, [fp, #-0x10]
    // 0x131d654: StoreField: r0->field_1f = d0
    //     0x131d654: stur            d0, [x0, #0x1f]
    // 0x131d658: r0 = Padding()
    //     0x131d658: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x131d65c: ldur            x1, [fp, #-8]
    // 0x131d660: StoreField: r0->field_f = r1
    //     0x131d660: stur            w1, [x0, #0xf]
    // 0x131d664: r1 = Instance_SizedBox
    //     0x131d664: add             x1, PP, #0x37, lsl #12  ; [pp+0x37b08] Obj!SizedBox@d68181
    //     0x131d668: ldr             x1, [x1, #0xb08]
    // 0x131d66c: StoreField: r0->field_b = r1
    //     0x131d66c: stur            w1, [x0, #0xb]
    // 0x131d670: LeaveFrame
    //     0x131d670: mov             SP, fp
    //     0x131d674: ldp             fp, lr, [SP], #0x10
    // 0x131d678: ret
    //     0x131d678: ret             
    // 0x131d67c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131d67c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131d680: b               #0x131d620
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x131d684, size: 0x1b8
    // 0x131d684: EnterFrame
    //     0x131d684: stp             fp, lr, [SP, #-0x10]!
    //     0x131d688: mov             fp, SP
    // 0x131d68c: AllocStack(0x28)
    //     0x131d68c: sub             SP, SP, #0x28
    // 0x131d690: SetupParameters()
    //     0x131d690: ldr             x0, [fp, #0x18]
    //     0x131d694: ldur            w2, [x0, #0x17]
    //     0x131d698: add             x2, x2, HEAP, lsl #32
    //     0x131d69c: stur            x2, [fp, #-8]
    // 0x131d6a0: CheckStackOverflow
    //     0x131d6a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131d6a4: cmp             SP, x16
    //     0x131d6a8: b.ls            #0x131d834
    // 0x131d6ac: LoadField: r1 = r2->field_f
    //     0x131d6ac: ldur            w1, [x2, #0xf]
    // 0x131d6b0: DecompressPointer r1
    //     0x131d6b0: add             x1, x1, HEAP, lsl #32
    // 0x131d6b4: r0 = controller()
    //     0x131d6b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d6b8: LoadField: r1 = r0->field_4f
    //     0x131d6b8: ldur            w1, [x0, #0x4f]
    // 0x131d6bc: DecompressPointer r1
    //     0x131d6bc: add             x1, x1, HEAP, lsl #32
    // 0x131d6c0: r0 = value()
    //     0x131d6c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d6c4: LoadField: r1 = r0->field_b
    //     0x131d6c4: ldur            w1, [x0, #0xb]
    // 0x131d6c8: DecompressPointer r1
    //     0x131d6c8: add             x1, x1, HEAP, lsl #32
    // 0x131d6cc: cmp             w1, NULL
    // 0x131d6d0: b.ne            #0x131d6dc
    // 0x131d6d4: d0 = inf
    //     0x131d6d4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d6d8: b               #0x131d744
    // 0x131d6dc: LoadField: r0 = r1->field_3f
    //     0x131d6dc: ldur            w0, [x1, #0x3f]
    // 0x131d6e0: DecompressPointer r0
    //     0x131d6e0: add             x0, x0, HEAP, lsl #32
    // 0x131d6e4: cmp             w0, NULL
    // 0x131d6e8: b.eq            #0x131d740
    // 0x131d6ec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x131d6ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x131d6f0: ldr             x0, [x0, #0x1c80]
    //     0x131d6f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131d6f8: cmp             w0, w16
    //     0x131d6fc: b.ne            #0x131d708
    //     0x131d700: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x131d704: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131d708: r0 = GetNavigation.size()
    //     0x131d708: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x131d70c: LoadField: d0 = r0->field_f
    //     0x131d70c: ldur            d0, [x0, #0xf]
    // 0x131d710: d1 = 0.430000
    //     0x131d710: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x131d714: ldr             d1, [x17, #0xb8]
    // 0x131d718: fmul            d2, d0, d1
    // 0x131d71c: stur            d2, [fp, #-0x28]
    // 0x131d720: r0 = BoxConstraints()
    //     0x131d720: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x131d724: StoreField: r0->field_7 = rZR
    //     0x131d724: stur            xzr, [x0, #7]
    // 0x131d728: d0 = inf
    //     0x131d728: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d72c: StoreField: r0->field_f = d0
    //     0x131d72c: stur            d0, [x0, #0xf]
    // 0x131d730: ArrayStore: r0[0] = rZR  ; List_8
    //     0x131d730: stur            xzr, [x0, #0x17]
    // 0x131d734: ldur            d0, [fp, #-0x28]
    // 0x131d738: StoreField: r0->field_1f = d0
    //     0x131d738: stur            d0, [x0, #0x1f]
    // 0x131d73c: b               #0x131d794
    // 0x131d740: d0 = inf
    //     0x131d740: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d744: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x131d744: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x131d748: ldr             x0, [x0, #0x1c80]
    //     0x131d74c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131d750: cmp             w0, w16
    //     0x131d754: b.ne            #0x131d760
    //     0x131d758: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x131d75c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131d760: r0 = GetNavigation.size()
    //     0x131d760: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x131d764: LoadField: d0 = r0->field_f
    //     0x131d764: ldur            d0, [x0, #0xf]
    // 0x131d768: d1 = 0.300000
    //     0x131d768: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x131d76c: ldr             d1, [x17, #0x658]
    // 0x131d770: fmul            d2, d0, d1
    // 0x131d774: stur            d2, [fp, #-0x28]
    // 0x131d778: r0 = BoxConstraints()
    //     0x131d778: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x131d77c: StoreField: r0->field_7 = rZR
    //     0x131d77c: stur            xzr, [x0, #7]
    // 0x131d780: d0 = inf
    //     0x131d780: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x131d784: StoreField: r0->field_f = d0
    //     0x131d784: stur            d0, [x0, #0xf]
    // 0x131d788: ArrayStore: r0[0] = rZR  ; List_8
    //     0x131d788: stur            xzr, [x0, #0x17]
    // 0x131d78c: ldur            d0, [fp, #-0x28]
    // 0x131d790: StoreField: r0->field_1f = d0
    //     0x131d790: stur            d0, [x0, #0x1f]
    // 0x131d794: ldur            x2, [fp, #-8]
    // 0x131d798: stur            x0, [fp, #-0x10]
    // 0x131d79c: LoadField: r1 = r2->field_f
    //     0x131d79c: ldur            w1, [x2, #0xf]
    // 0x131d7a0: DecompressPointer r1
    //     0x131d7a0: add             x1, x1, HEAP, lsl #32
    // 0x131d7a4: r0 = controller()
    //     0x131d7a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131d7a8: LoadField: r1 = r0->field_4f
    //     0x131d7a8: ldur            w1, [x0, #0x4f]
    // 0x131d7ac: DecompressPointer r1
    //     0x131d7ac: add             x1, x1, HEAP, lsl #32
    // 0x131d7b0: r0 = value()
    //     0x131d7b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131d7b4: LoadField: r3 = r0->field_b
    //     0x131d7b4: ldur            w3, [x0, #0xb]
    // 0x131d7b8: DecompressPointer r3
    //     0x131d7b8: add             x3, x3, HEAP, lsl #32
    // 0x131d7bc: ldur            x2, [fp, #-8]
    // 0x131d7c0: stur            x3, [fp, #-0x18]
    // 0x131d7c4: r1 = Function '<anonymous closure>':.
    //     0x131d7c4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37b10] AnonymousClosure: (0x131cffc), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x131d7c8: ldr             x1, [x1, #0xb10]
    // 0x131d7cc: r0 = AllocateClosure()
    //     0x131d7cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131d7d0: stur            x0, [fp, #-8]
    // 0x131d7d4: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x131d7d4: bl              #0x8b104c  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x131d7d8: mov             x1, x0
    // 0x131d7dc: ldur            x0, [fp, #-8]
    // 0x131d7e0: stur            x1, [fp, #-0x20]
    // 0x131d7e4: StoreField: r1->field_b = r0
    //     0x131d7e4: stur            w0, [x1, #0xb]
    // 0x131d7e8: r0 = "Confirm Return"
    //     0x131d7e8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36220] "Confirm Return"
    //     0x131d7ec: ldr             x0, [x0, #0x220]
    // 0x131d7f0: StoreField: r1->field_13 = r0
    //     0x131d7f0: stur            w0, [x1, #0x13]
    // 0x131d7f4: r0 = "Returning this product requires returning the free gift as well"
    //     0x131d7f4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36228] "Returning this product requires returning the free gift as well"
    //     0x131d7f8: ldr             x0, [x0, #0x228]
    // 0x131d7fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x131d7fc: stur            w0, [x1, #0x17]
    // 0x131d800: r0 = "return_order_intermediate"
    //     0x131d800: add             x0, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0x131d804: ldr             x0, [x0, #0xb00]
    // 0x131d808: StoreField: r1->field_f = r0
    //     0x131d808: stur            w0, [x1, #0xf]
    // 0x131d80c: ldur            x0, [fp, #-0x18]
    // 0x131d810: StoreField: r1->field_23 = r0
    //     0x131d810: stur            w0, [x1, #0x23]
    // 0x131d814: r0 = ConstrainedBox()
    //     0x131d814: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x131d818: ldur            x1, [fp, #-0x10]
    // 0x131d81c: StoreField: r0->field_f = r1
    //     0x131d81c: stur            w1, [x0, #0xf]
    // 0x131d820: ldur            x1, [fp, #-0x20]
    // 0x131d824: StoreField: r0->field_b = r1
    //     0x131d824: stur            w1, [x0, #0xb]
    // 0x131d828: LeaveFrame
    //     0x131d828: mov             SP, fp
    //     0x131d82c: ldp             fp, lr, [SP], #0x10
    // 0x131d830: ret
    //     0x131d830: ret             
    // 0x131d834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131d834: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131d838: b               #0x131d6ac
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x13691a0, size: 0x1a0
    // 0x13691a0: EnterFrame
    //     0x13691a0: stp             fp, lr, [SP, #-0x10]!
    //     0x13691a4: mov             fp, SP
    // 0x13691a8: AllocStack(0x28)
    //     0x13691a8: sub             SP, SP, #0x28
    // 0x13691ac: SetupParameters(ExchangeReturnIntermediateScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x13691ac: mov             x0, x1
    //     0x13691b0: stur            x1, [fp, #-8]
    //     0x13691b4: mov             x1, x2
    //     0x13691b8: stur            x2, [fp, #-0x10]
    // 0x13691bc: CheckStackOverflow
    //     0x13691bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13691c0: cmp             SP, x16
    //     0x13691c4: b.ls            #0x1369338
    // 0x13691c8: r1 = 2
    //     0x13691c8: movz            x1, #0x2
    // 0x13691cc: r0 = AllocateContext()
    //     0x13691cc: bl              #0x16f6108  ; AllocateContextStub
    // 0x13691d0: mov             x2, x0
    // 0x13691d4: ldur            x0, [fp, #-8]
    // 0x13691d8: stur            x2, [fp, #-0x18]
    // 0x13691dc: StoreField: r2->field_f = r0
    //     0x13691dc: stur            w0, [x2, #0xf]
    // 0x13691e0: ldur            x0, [fp, #-0x10]
    // 0x13691e4: StoreField: r2->field_13 = r0
    //     0x13691e4: stur            w0, [x2, #0x13]
    // 0x13691e8: mov             x1, x0
    // 0x13691ec: r0 = of()
    //     0x13691ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13691f0: r17 = 307
    //     0x13691f0: movz            x17, #0x133
    // 0x13691f4: ldr             w2, [x0, x17]
    // 0x13691f8: DecompressPointer r2
    //     0x13691f8: add             x2, x2, HEAP, lsl #32
    // 0x13691fc: ldur            x1, [fp, #-0x10]
    // 0x1369200: stur            x2, [fp, #-8]
    // 0x1369204: r0 = of()
    //     0x1369204: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1369208: LoadField: r1 = r0->field_87
    //     0x1369208: ldur            w1, [x0, #0x87]
    // 0x136920c: DecompressPointer r1
    //     0x136920c: add             x1, x1, HEAP, lsl #32
    // 0x1369210: LoadField: r0 = r1->field_2b
    //     0x1369210: ldur            w0, [x1, #0x2b]
    // 0x1369214: DecompressPointer r0
    //     0x1369214: add             x0, x0, HEAP, lsl #32
    // 0x1369218: r16 = Instance_Color
    //     0x1369218: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x136921c: str             x16, [SP]
    // 0x1369220: mov             x1, x0
    // 0x1369224: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x1369224: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x1369228: ldr             x4, [x4, #0xf40]
    // 0x136922c: r0 = copyWith()
    //     0x136922c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1369230: stur            x0, [fp, #-0x10]
    // 0x1369234: r0 = Text()
    //     0x1369234: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1369238: mov             x3, x0
    // 0x136923c: r0 = "CONTINUE"
    //     0x136923c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x1369240: ldr             x0, [x0, #0xac8]
    // 0x1369244: stur            x3, [fp, #-0x20]
    // 0x1369248: StoreField: r3->field_b = r0
    //     0x1369248: stur            w0, [x3, #0xb]
    // 0x136924c: ldur            x0, [fp, #-0x10]
    // 0x1369250: StoreField: r3->field_13 = r0
    //     0x1369250: stur            w0, [x3, #0x13]
    // 0x1369254: ldur            x2, [fp, #-0x18]
    // 0x1369258: r1 = Function '<anonymous closure>':.
    //     0x1369258: add             x1, PP, #0x37, lsl #12  ; [pp+0x37ad0] AnonymousClosure: (0x131d284), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::bottomNavigationBar (0x13691a0)
    //     0x136925c: ldr             x1, [x1, #0xad0]
    // 0x1369260: r0 = AllocateClosure()
    //     0x1369260: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1369264: stur            x0, [fp, #-0x10]
    // 0x1369268: r0 = TextButton()
    //     0x1369268: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x136926c: mov             x1, x0
    // 0x1369270: ldur            x0, [fp, #-0x10]
    // 0x1369274: stur            x1, [fp, #-0x18]
    // 0x1369278: StoreField: r1->field_b = r0
    //     0x1369278: stur            w0, [x1, #0xb]
    // 0x136927c: r0 = false
    //     0x136927c: add             x0, NULL, #0x30  ; false
    // 0x1369280: StoreField: r1->field_27 = r0
    //     0x1369280: stur            w0, [x1, #0x27]
    // 0x1369284: r0 = true
    //     0x1369284: add             x0, NULL, #0x20  ; true
    // 0x1369288: StoreField: r1->field_2f = r0
    //     0x1369288: stur            w0, [x1, #0x2f]
    // 0x136928c: ldur            x2, [fp, #-0x20]
    // 0x1369290: StoreField: r1->field_37 = r2
    //     0x1369290: stur            w2, [x1, #0x37]
    // 0x1369294: r0 = TextButtonTheme()
    //     0x1369294: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1369298: mov             x1, x0
    // 0x136929c: ldur            x0, [fp, #-8]
    // 0x13692a0: stur            x1, [fp, #-0x10]
    // 0x13692a4: StoreField: r1->field_f = r0
    //     0x13692a4: stur            w0, [x1, #0xf]
    // 0x13692a8: ldur            x0, [fp, #-0x18]
    // 0x13692ac: StoreField: r1->field_b = r0
    //     0x13692ac: stur            w0, [x1, #0xb]
    // 0x13692b0: r0 = SizedBox()
    //     0x13692b0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13692b4: mov             x1, x0
    // 0x13692b8: r0 = inf
    //     0x13692b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x13692bc: ldr             x0, [x0, #0x9f8]
    // 0x13692c0: stur            x1, [fp, #-8]
    // 0x13692c4: StoreField: r1->field_f = r0
    //     0x13692c4: stur            w0, [x1, #0xf]
    // 0x13692c8: r0 = 44.000000
    //     0x13692c8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0x13692cc: ldr             x0, [x0, #0xad8]
    // 0x13692d0: StoreField: r1->field_13 = r0
    //     0x13692d0: stur            w0, [x1, #0x13]
    // 0x13692d4: ldur            x0, [fp, #-0x10]
    // 0x13692d8: StoreField: r1->field_b = r0
    //     0x13692d8: stur            w0, [x1, #0xb]
    // 0x13692dc: r0 = Padding()
    //     0x13692dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13692e0: mov             x1, x0
    // 0x13692e4: r0 = Instance_EdgeInsets
    //     0x13692e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13692e8: ldr             x0, [x0, #0x1f0]
    // 0x13692ec: stur            x1, [fp, #-0x10]
    // 0x13692f0: StoreField: r1->field_f = r0
    //     0x13692f0: stur            w0, [x1, #0xf]
    // 0x13692f4: ldur            x0, [fp, #-8]
    // 0x13692f8: StoreField: r1->field_b = r0
    //     0x13692f8: stur            w0, [x1, #0xb]
    // 0x13692fc: r0 = Card()
    //     0x13692fc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x1369300: r1 = Instance_RoundedRectangleBorder
    //     0x1369300: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1369304: ldr             x1, [x1, #0xd68]
    // 0x1369308: StoreField: r0->field_1b = r1
    //     0x1369308: stur            w1, [x0, #0x1b]
    // 0x136930c: r1 = true
    //     0x136930c: add             x1, NULL, #0x20  ; true
    // 0x1369310: StoreField: r0->field_1f = r1
    //     0x1369310: stur            w1, [x0, #0x1f]
    // 0x1369314: ldur            x2, [fp, #-0x10]
    // 0x1369318: StoreField: r0->field_2f = r2
    //     0x1369318: stur            w2, [x0, #0x2f]
    // 0x136931c: StoreField: r0->field_2b = r1
    //     0x136931c: stur            w1, [x0, #0x2b]
    // 0x1369320: r1 = Instance__CardVariant
    //     0x1369320: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x1369324: ldr             x1, [x1, #0xa68]
    // 0x1369328: StoreField: r0->field_33 = r1
    //     0x1369328: stur            w1, [x0, #0x33]
    // 0x136932c: LeaveFrame
    //     0x136932c: mov             SP, fp
    //     0x1369330: ldp             fp, lr, [SP], #0x10
    // 0x1369334: ret
    //     0x1369334: ret             
    // 0x1369338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1369338: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x136933c: b               #0x13691c8
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x13cf540, size: 0x144
    // 0x13cf540: EnterFrame
    //     0x13cf540: stp             fp, lr, [SP, #-0x10]!
    //     0x13cf544: mov             fp, SP
    // 0x13cf548: AllocStack(0x18)
    //     0x13cf548: sub             SP, SP, #0x18
    // 0x13cf54c: SetupParameters()
    //     0x13cf54c: ldr             x0, [fp, #0x18]
    //     0x13cf550: ldur            w2, [x0, #0x17]
    //     0x13cf554: add             x2, x2, HEAP, lsl #32
    //     0x13cf558: stur            x2, [fp, #-0x10]
    // 0x13cf55c: CheckStackOverflow
    //     0x13cf55c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cf560: cmp             SP, x16
    //     0x13cf564: b.ls            #0x13cf678
    // 0x13cf568: LoadField: r0 = r2->field_b
    //     0x13cf568: ldur            w0, [x2, #0xb]
    // 0x13cf56c: DecompressPointer r0
    //     0x13cf56c: add             x0, x0, HEAP, lsl #32
    // 0x13cf570: stur            x0, [fp, #-8]
    // 0x13cf574: LoadField: r1 = r0->field_f
    //     0x13cf574: ldur            w1, [x0, #0xf]
    // 0x13cf578: DecompressPointer r1
    //     0x13cf578: add             x1, x1, HEAP, lsl #32
    // 0x13cf57c: r0 = controller()
    //     0x13cf57c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf580: LoadField: r1 = r0->field_73
    //     0x13cf580: ldur            w1, [x0, #0x73]
    // 0x13cf584: DecompressPointer r1
    //     0x13cf584: add             x1, x1, HEAP, lsl #32
    // 0x13cf588: ldr             x0, [fp, #0x10]
    // 0x13cf58c: cmp             w0, NULL
    // 0x13cf590: b.ne            #0x13cf59c
    // 0x13cf594: r2 = ""
    //     0x13cf594: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cf598: b               #0x13cf5a0
    // 0x13cf59c: mov             x2, x0
    // 0x13cf5a0: ldur            x0, [fp, #-8]
    // 0x13cf5a4: r0 = value=()
    //     0x13cf5a4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13cf5a8: ldur            x0, [fp, #-8]
    // 0x13cf5ac: LoadField: r1 = r0->field_f
    //     0x13cf5ac: ldur            w1, [x0, #0xf]
    // 0x13cf5b0: DecompressPointer r1
    //     0x13cf5b0: add             x1, x1, HEAP, lsl #32
    // 0x13cf5b4: r0 = controller()
    //     0x13cf5b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf5b8: LoadField: r2 = r0->field_77
    //     0x13cf5b8: ldur            w2, [x0, #0x77]
    // 0x13cf5bc: DecompressPointer r2
    //     0x13cf5bc: add             x2, x2, HEAP, lsl #32
    // 0x13cf5c0: ldur            x0, [fp, #-8]
    // 0x13cf5c4: stur            x2, [fp, #-0x18]
    // 0x13cf5c8: LoadField: r1 = r0->field_f
    //     0x13cf5c8: ldur            w1, [x0, #0xf]
    // 0x13cf5cc: DecompressPointer r1
    //     0x13cf5cc: add             x1, x1, HEAP, lsl #32
    // 0x13cf5d0: r0 = controller()
    //     0x13cf5d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf5d4: LoadField: r1 = r0->field_4f
    //     0x13cf5d4: ldur            w1, [x0, #0x4f]
    // 0x13cf5d8: DecompressPointer r1
    //     0x13cf5d8: add             x1, x1, HEAP, lsl #32
    // 0x13cf5dc: r0 = value()
    //     0x13cf5dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cf5e0: LoadField: r1 = r0->field_b
    //     0x13cf5e0: ldur            w1, [x0, #0xb]
    // 0x13cf5e4: DecompressPointer r1
    //     0x13cf5e4: add             x1, x1, HEAP, lsl #32
    // 0x13cf5e8: cmp             w1, NULL
    // 0x13cf5ec: b.ne            #0x13cf5f8
    // 0x13cf5f0: r0 = Null
    //     0x13cf5f0: mov             x0, NULL
    // 0x13cf5f4: b               #0x13cf64c
    // 0x13cf5f8: ldur            x0, [fp, #-0x10]
    // 0x13cf5fc: LoadField: r2 = r1->field_f
    //     0x13cf5fc: ldur            w2, [x1, #0xf]
    // 0x13cf600: DecompressPointer r2
    //     0x13cf600: add             x2, x2, HEAP, lsl #32
    // 0x13cf604: LoadField: r1 = r0->field_13
    //     0x13cf604: ldur            w1, [x0, #0x13]
    // 0x13cf608: DecompressPointer r1
    //     0x13cf608: add             x1, x1, HEAP, lsl #32
    // 0x13cf60c: LoadField: r0 = r2->field_b
    //     0x13cf60c: ldur            w0, [x2, #0xb]
    // 0x13cf610: r3 = LoadInt32Instr(r1)
    //     0x13cf610: sbfx            x3, x1, #1, #0x1f
    //     0x13cf614: tbz             w1, #0, #0x13cf61c
    //     0x13cf618: ldur            x3, [x1, #7]
    // 0x13cf61c: r1 = LoadInt32Instr(r0)
    //     0x13cf61c: sbfx            x1, x0, #1, #0x1f
    // 0x13cf620: mov             x0, x1
    // 0x13cf624: mov             x1, x3
    // 0x13cf628: cmp             x1, x0
    // 0x13cf62c: b.hs            #0x13cf680
    // 0x13cf630: LoadField: r0 = r2->field_f
    //     0x13cf630: ldur            w0, [x2, #0xf]
    // 0x13cf634: DecompressPointer r0
    //     0x13cf634: add             x0, x0, HEAP, lsl #32
    // 0x13cf638: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x13cf638: add             x16, x0, x3, lsl #2
    //     0x13cf63c: ldur            w1, [x16, #0xf]
    // 0x13cf640: DecompressPointer r1
    //     0x13cf640: add             x1, x1, HEAP, lsl #32
    // 0x13cf644: LoadField: r0 = r1->field_f
    //     0x13cf644: ldur            w0, [x1, #0xf]
    // 0x13cf648: DecompressPointer r0
    //     0x13cf648: add             x0, x0, HEAP, lsl #32
    // 0x13cf64c: cmp             w0, NULL
    // 0x13cf650: b.ne            #0x13cf65c
    // 0x13cf654: r2 = ""
    //     0x13cf654: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cf658: b               #0x13cf660
    // 0x13cf65c: mov             x2, x0
    // 0x13cf660: ldur            x1, [fp, #-0x18]
    // 0x13cf664: r0 = value=()
    //     0x13cf664: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13cf668: r0 = Null
    //     0x13cf668: mov             x0, NULL
    // 0x13cf66c: LeaveFrame
    //     0x13cf66c: mov             SP, fp
    //     0x13cf670: ldp             fp, lr, [SP], #0x10
    // 0x13cf674: ret
    //     0x13cf674: ret             
    // 0x13cf678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cf678: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cf67c: b               #0x13cf568
    // 0x13cf680: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cf680: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic) {
    // ** addr: 0x13cf684, size: 0x644
    // 0x13cf684: EnterFrame
    //     0x13cf684: stp             fp, lr, [SP, #-0x10]!
    //     0x13cf688: mov             fp, SP
    // 0x13cf68c: AllocStack(0x48)
    //     0x13cf68c: sub             SP, SP, #0x48
    // 0x13cf690: SetupParameters()
    //     0x13cf690: ldr             x0, [fp, #0x10]
    //     0x13cf694: ldur            w2, [x0, #0x17]
    //     0x13cf698: add             x2, x2, HEAP, lsl #32
    //     0x13cf69c: stur            x2, [fp, #-0x10]
    // 0x13cf6a0: CheckStackOverflow
    //     0x13cf6a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cf6a4: cmp             SP, x16
    //     0x13cf6a8: b.ls            #0x13cfcb0
    // 0x13cf6ac: LoadField: r0 = r2->field_b
    //     0x13cf6ac: ldur            w0, [x2, #0xb]
    // 0x13cf6b0: DecompressPointer r0
    //     0x13cf6b0: add             x0, x0, HEAP, lsl #32
    // 0x13cf6b4: stur            x0, [fp, #-8]
    // 0x13cf6b8: LoadField: r1 = r0->field_f
    //     0x13cf6b8: ldur            w1, [x0, #0xf]
    // 0x13cf6bc: DecompressPointer r1
    //     0x13cf6bc: add             x1, x1, HEAP, lsl #32
    // 0x13cf6c0: r0 = controller()
    //     0x13cf6c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf6c4: LoadField: r1 = r0->field_73
    //     0x13cf6c4: ldur            w1, [x0, #0x73]
    // 0x13cf6c8: DecompressPointer r1
    //     0x13cf6c8: add             x1, x1, HEAP, lsl #32
    // 0x13cf6cc: r0 = value()
    //     0x13cf6cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cf6d0: mov             x2, x0
    // 0x13cf6d4: ldur            x0, [fp, #-8]
    // 0x13cf6d8: stur            x2, [fp, #-0x18]
    // 0x13cf6dc: LoadField: r1 = r0->field_f
    //     0x13cf6dc: ldur            w1, [x0, #0xf]
    // 0x13cf6e0: DecompressPointer r1
    //     0x13cf6e0: add             x1, x1, HEAP, lsl #32
    // 0x13cf6e4: r0 = controller()
    //     0x13cf6e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf6e8: LoadField: r1 = r0->field_4f
    //     0x13cf6e8: ldur            w1, [x0, #0x4f]
    // 0x13cf6ec: DecompressPointer r1
    //     0x13cf6ec: add             x1, x1, HEAP, lsl #32
    // 0x13cf6f0: r0 = value()
    //     0x13cf6f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cf6f4: LoadField: r1 = r0->field_b
    //     0x13cf6f4: ldur            w1, [x0, #0xb]
    // 0x13cf6f8: DecompressPointer r1
    //     0x13cf6f8: add             x1, x1, HEAP, lsl #32
    // 0x13cf6fc: cmp             w1, NULL
    // 0x13cf700: b.ne            #0x13cf710
    // 0x13cf704: ldur            x2, [fp, #-0x10]
    // 0x13cf708: r1 = Null
    //     0x13cf708: mov             x1, NULL
    // 0x13cf70c: b               #0x13cf764
    // 0x13cf710: ldur            x2, [fp, #-0x10]
    // 0x13cf714: LoadField: r3 = r1->field_f
    //     0x13cf714: ldur            w3, [x1, #0xf]
    // 0x13cf718: DecompressPointer r3
    //     0x13cf718: add             x3, x3, HEAP, lsl #32
    // 0x13cf71c: LoadField: r0 = r2->field_13
    //     0x13cf71c: ldur            w0, [x2, #0x13]
    // 0x13cf720: DecompressPointer r0
    //     0x13cf720: add             x0, x0, HEAP, lsl #32
    // 0x13cf724: LoadField: r1 = r3->field_b
    //     0x13cf724: ldur            w1, [x3, #0xb]
    // 0x13cf728: r4 = LoadInt32Instr(r0)
    //     0x13cf728: sbfx            x4, x0, #1, #0x1f
    //     0x13cf72c: tbz             w0, #0, #0x13cf734
    //     0x13cf730: ldur            x4, [x0, #7]
    // 0x13cf734: r0 = LoadInt32Instr(r1)
    //     0x13cf734: sbfx            x0, x1, #1, #0x1f
    // 0x13cf738: mov             x1, x4
    // 0x13cf73c: cmp             x1, x0
    // 0x13cf740: b.hs            #0x13cfcb8
    // 0x13cf744: LoadField: r0 = r3->field_f
    //     0x13cf744: ldur            w0, [x3, #0xf]
    // 0x13cf748: DecompressPointer r0
    //     0x13cf748: add             x0, x0, HEAP, lsl #32
    // 0x13cf74c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cf74c: add             x16, x0, x4, lsl #2
    //     0x13cf750: ldur            w1, [x16, #0xf]
    // 0x13cf754: DecompressPointer r1
    //     0x13cf754: add             x1, x1, HEAP, lsl #32
    // 0x13cf758: LoadField: r0 = r1->field_b
    //     0x13cf758: ldur            w0, [x1, #0xb]
    // 0x13cf75c: DecompressPointer r0
    //     0x13cf75c: add             x0, x0, HEAP, lsl #32
    // 0x13cf760: mov             x1, x0
    // 0x13cf764: ldur            x0, [fp, #-0x18]
    // 0x13cf768: r3 = LoadClassIdInstr(r0)
    //     0x13cf768: ldur            x3, [x0, #-1]
    //     0x13cf76c: ubfx            x3, x3, #0xc, #0x14
    // 0x13cf770: stp             x1, x0, [SP]
    // 0x13cf774: mov             x0, x3
    // 0x13cf778: mov             lr, x0
    // 0x13cf77c: ldr             lr, [x21, lr, lsl #3]
    // 0x13cf780: blr             lr
    // 0x13cf784: tbnz            w0, #4, #0x13cf7a0
    // 0x13cf788: r1 = Instance_Color
    //     0x13cf788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cf78c: d0 = 0.030000
    //     0x13cf78c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x13cf790: ldr             d0, [x17, #0x238]
    // 0x13cf794: r0 = withOpacity()
    //     0x13cf794: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cf798: mov             x1, x0
    // 0x13cf79c: b               #0x13cf7a4
    // 0x13cf7a0: r1 = Instance_Color
    //     0x13cf7a0: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13cf7a4: ldur            x0, [fp, #-8]
    // 0x13cf7a8: stur            x1, [fp, #-0x18]
    // 0x13cf7ac: r0 = BoxDecoration()
    //     0x13cf7ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13cf7b0: mov             x2, x0
    // 0x13cf7b4: ldur            x0, [fp, #-0x18]
    // 0x13cf7b8: stur            x2, [fp, #-0x20]
    // 0x13cf7bc: StoreField: r2->field_7 = r0
    //     0x13cf7bc: stur            w0, [x2, #7]
    // 0x13cf7c0: r0 = Instance_BoxShape
    //     0x13cf7c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13cf7c4: ldr             x0, [x0, #0x80]
    // 0x13cf7c8: StoreField: r2->field_23 = r0
    //     0x13cf7c8: stur            w0, [x2, #0x23]
    // 0x13cf7cc: ldur            x0, [fp, #-8]
    // 0x13cf7d0: LoadField: r1 = r0->field_f
    //     0x13cf7d0: ldur            w1, [x0, #0xf]
    // 0x13cf7d4: DecompressPointer r1
    //     0x13cf7d4: add             x1, x1, HEAP, lsl #32
    // 0x13cf7d8: r0 = controller()
    //     0x13cf7d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf7dc: LoadField: r1 = r0->field_4f
    //     0x13cf7dc: ldur            w1, [x0, #0x4f]
    // 0x13cf7e0: DecompressPointer r1
    //     0x13cf7e0: add             x1, x1, HEAP, lsl #32
    // 0x13cf7e4: r0 = value()
    //     0x13cf7e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cf7e8: LoadField: r1 = r0->field_b
    //     0x13cf7e8: ldur            w1, [x0, #0xb]
    // 0x13cf7ec: DecompressPointer r1
    //     0x13cf7ec: add             x1, x1, HEAP, lsl #32
    // 0x13cf7f0: cmp             w1, NULL
    // 0x13cf7f4: b.ne            #0x13cf804
    // 0x13cf7f8: ldur            x2, [fp, #-0x10]
    // 0x13cf7fc: r0 = Null
    //     0x13cf7fc: mov             x0, NULL
    // 0x13cf800: b               #0x13cf854
    // 0x13cf804: ldur            x2, [fp, #-0x10]
    // 0x13cf808: LoadField: r3 = r1->field_f
    //     0x13cf808: ldur            w3, [x1, #0xf]
    // 0x13cf80c: DecompressPointer r3
    //     0x13cf80c: add             x3, x3, HEAP, lsl #32
    // 0x13cf810: LoadField: r0 = r2->field_13
    //     0x13cf810: ldur            w0, [x2, #0x13]
    // 0x13cf814: DecompressPointer r0
    //     0x13cf814: add             x0, x0, HEAP, lsl #32
    // 0x13cf818: LoadField: r1 = r3->field_b
    //     0x13cf818: ldur            w1, [x3, #0xb]
    // 0x13cf81c: r4 = LoadInt32Instr(r0)
    //     0x13cf81c: sbfx            x4, x0, #1, #0x1f
    //     0x13cf820: tbz             w0, #0, #0x13cf828
    //     0x13cf824: ldur            x4, [x0, #7]
    // 0x13cf828: r0 = LoadInt32Instr(r1)
    //     0x13cf828: sbfx            x0, x1, #1, #0x1f
    // 0x13cf82c: mov             x1, x4
    // 0x13cf830: cmp             x1, x0
    // 0x13cf834: b.hs            #0x13cfcbc
    // 0x13cf838: LoadField: r0 = r3->field_f
    //     0x13cf838: ldur            w0, [x3, #0xf]
    // 0x13cf83c: DecompressPointer r0
    //     0x13cf83c: add             x0, x0, HEAP, lsl #32
    // 0x13cf840: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cf840: add             x16, x0, x4, lsl #2
    //     0x13cf844: ldur            w1, [x16, #0xf]
    // 0x13cf848: DecompressPointer r1
    //     0x13cf848: add             x1, x1, HEAP, lsl #32
    // 0x13cf84c: LoadField: r0 = r1->field_f
    //     0x13cf84c: ldur            w0, [x1, #0xf]
    // 0x13cf850: DecompressPointer r0
    //     0x13cf850: add             x0, x0, HEAP, lsl #32
    // 0x13cf854: cmp             w0, NULL
    // 0x13cf858: b.ne            #0x13cf864
    // 0x13cf85c: r3 = ""
    //     0x13cf85c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cf860: b               #0x13cf868
    // 0x13cf864: mov             x3, x0
    // 0x13cf868: ldur            x0, [fp, #-8]
    // 0x13cf86c: stur            x3, [fp, #-0x18]
    // 0x13cf870: LoadField: r1 = r2->field_f
    //     0x13cf870: ldur            w1, [x2, #0xf]
    // 0x13cf874: DecompressPointer r1
    //     0x13cf874: add             x1, x1, HEAP, lsl #32
    // 0x13cf878: r0 = of()
    //     0x13cf878: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cf87c: LoadField: r1 = r0->field_87
    //     0x13cf87c: ldur            w1, [x0, #0x87]
    // 0x13cf880: DecompressPointer r1
    //     0x13cf880: add             x1, x1, HEAP, lsl #32
    // 0x13cf884: LoadField: r0 = r1->field_7
    //     0x13cf884: ldur            w0, [x1, #7]
    // 0x13cf888: DecompressPointer r0
    //     0x13cf888: add             x0, x0, HEAP, lsl #32
    // 0x13cf88c: stur            x0, [fp, #-0x28]
    // 0x13cf890: r1 = Instance_Color
    //     0x13cf890: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cf894: d0 = 0.700000
    //     0x13cf894: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13cf898: ldr             d0, [x17, #0xf48]
    // 0x13cf89c: r0 = withOpacity()
    //     0x13cf89c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cf8a0: r16 = 12.000000
    //     0x13cf8a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cf8a4: ldr             x16, [x16, #0x9e8]
    // 0x13cf8a8: stp             x16, x0, [SP]
    // 0x13cf8ac: ldur            x1, [fp, #-0x28]
    // 0x13cf8b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cf8b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cf8b4: ldr             x4, [x4, #0x9b8]
    // 0x13cf8b8: r0 = copyWith()
    //     0x13cf8b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cf8bc: stur            x0, [fp, #-0x28]
    // 0x13cf8c0: r0 = Text()
    //     0x13cf8c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cf8c4: mov             x2, x0
    // 0x13cf8c8: ldur            x0, [fp, #-0x18]
    // 0x13cf8cc: stur            x2, [fp, #-0x30]
    // 0x13cf8d0: StoreField: r2->field_b = r0
    //     0x13cf8d0: stur            w0, [x2, #0xb]
    // 0x13cf8d4: ldur            x0, [fp, #-0x28]
    // 0x13cf8d8: StoreField: r2->field_13 = r0
    //     0x13cf8d8: stur            w0, [x2, #0x13]
    // 0x13cf8dc: ldur            x0, [fp, #-8]
    // 0x13cf8e0: LoadField: r1 = r0->field_f
    //     0x13cf8e0: ldur            w1, [x0, #0xf]
    // 0x13cf8e4: DecompressPointer r1
    //     0x13cf8e4: add             x1, x1, HEAP, lsl #32
    // 0x13cf8e8: r0 = controller()
    //     0x13cf8e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cf8ec: LoadField: r1 = r0->field_4f
    //     0x13cf8ec: ldur            w1, [x0, #0x4f]
    // 0x13cf8f0: DecompressPointer r1
    //     0x13cf8f0: add             x1, x1, HEAP, lsl #32
    // 0x13cf8f4: r0 = value()
    //     0x13cf8f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cf8f8: LoadField: r1 = r0->field_b
    //     0x13cf8f8: ldur            w1, [x0, #0xb]
    // 0x13cf8fc: DecompressPointer r1
    //     0x13cf8fc: add             x1, x1, HEAP, lsl #32
    // 0x13cf900: cmp             w1, NULL
    // 0x13cf904: b.ne            #0x13cf914
    // 0x13cf908: ldur            x3, [fp, #-0x10]
    // 0x13cf90c: r0 = Null
    //     0x13cf90c: mov             x0, NULL
    // 0x13cf910: b               #0x13cf980
    // 0x13cf914: ldur            x3, [fp, #-0x10]
    // 0x13cf918: LoadField: r2 = r1->field_f
    //     0x13cf918: ldur            w2, [x1, #0xf]
    // 0x13cf91c: DecompressPointer r2
    //     0x13cf91c: add             x2, x2, HEAP, lsl #32
    // 0x13cf920: LoadField: r0 = r3->field_13
    //     0x13cf920: ldur            w0, [x3, #0x13]
    // 0x13cf924: DecompressPointer r0
    //     0x13cf924: add             x0, x0, HEAP, lsl #32
    // 0x13cf928: LoadField: r1 = r2->field_b
    //     0x13cf928: ldur            w1, [x2, #0xb]
    // 0x13cf92c: r4 = LoadInt32Instr(r0)
    //     0x13cf92c: sbfx            x4, x0, #1, #0x1f
    //     0x13cf930: tbz             w0, #0, #0x13cf938
    //     0x13cf934: ldur            x4, [x0, #7]
    // 0x13cf938: r0 = LoadInt32Instr(r1)
    //     0x13cf938: sbfx            x0, x1, #1, #0x1f
    // 0x13cf93c: mov             x1, x4
    // 0x13cf940: cmp             x1, x0
    // 0x13cf944: b.hs            #0x13cfcc0
    // 0x13cf948: LoadField: r0 = r2->field_f
    //     0x13cf948: ldur            w0, [x2, #0xf]
    // 0x13cf94c: DecompressPointer r0
    //     0x13cf94c: add             x0, x0, HEAP, lsl #32
    // 0x13cf950: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cf950: add             x16, x0, x4, lsl #2
    //     0x13cf954: ldur            w1, [x16, #0xf]
    // 0x13cf958: DecompressPointer r1
    //     0x13cf958: add             x1, x1, HEAP, lsl #32
    // 0x13cf95c: LoadField: r0 = r1->field_13
    //     0x13cf95c: ldur            w0, [x1, #0x13]
    // 0x13cf960: DecompressPointer r0
    //     0x13cf960: add             x0, x0, HEAP, lsl #32
    // 0x13cf964: cmp             w0, NULL
    // 0x13cf968: b.ne            #0x13cf974
    // 0x13cf96c: r0 = Null
    //     0x13cf96c: mov             x0, NULL
    // 0x13cf970: b               #0x13cf980
    // 0x13cf974: LoadField: r1 = r0->field_7
    //     0x13cf974: ldur            w1, [x0, #7]
    // 0x13cf978: DecompressPointer r1
    //     0x13cf978: add             x1, x1, HEAP, lsl #32
    // 0x13cf97c: mov             x0, x1
    // 0x13cf980: cmp             w0, NULL
    // 0x13cf984: b.ne            #0x13cf990
    // 0x13cf988: r5 = ""
    //     0x13cf988: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cf98c: b               #0x13cf994
    // 0x13cf990: mov             x5, x0
    // 0x13cf994: ldur            x4, [fp, #-8]
    // 0x13cf998: ldur            x0, [fp, #-0x30]
    // 0x13cf99c: stur            x5, [fp, #-0x18]
    // 0x13cf9a0: r1 = Null
    //     0x13cf9a0: mov             x1, NULL
    // 0x13cf9a4: r2 = 4
    //     0x13cf9a4: movz            x2, #0x4
    // 0x13cf9a8: r0 = AllocateArray()
    //     0x13cf9a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cf9ac: mov             x1, x0
    // 0x13cf9b0: ldur            x0, [fp, #-0x18]
    // 0x13cf9b4: StoreField: r1->field_f = r0
    //     0x13cf9b4: stur            w0, [x1, #0xf]
    // 0x13cf9b8: r16 = " Charge"
    //     0x13cf9b8: add             x16, PP, #0x38, lsl #12  ; [pp+0x38040] " Charge"
    //     0x13cf9bc: ldr             x16, [x16, #0x40]
    // 0x13cf9c0: StoreField: r1->field_13 = r16
    //     0x13cf9c0: stur            w16, [x1, #0x13]
    // 0x13cf9c4: str             x1, [SP]
    // 0x13cf9c8: r0 = _interpolate()
    //     0x13cf9c8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13cf9cc: ldur            x2, [fp, #-0x10]
    // 0x13cf9d0: stur            x0, [fp, #-0x18]
    // 0x13cf9d4: LoadField: r1 = r2->field_f
    //     0x13cf9d4: ldur            w1, [x2, #0xf]
    // 0x13cf9d8: DecompressPointer r1
    //     0x13cf9d8: add             x1, x1, HEAP, lsl #32
    // 0x13cf9dc: r0 = of()
    //     0x13cf9dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cf9e0: LoadField: r1 = r0->field_87
    //     0x13cf9e0: ldur            w1, [x0, #0x87]
    // 0x13cf9e4: DecompressPointer r1
    //     0x13cf9e4: add             x1, x1, HEAP, lsl #32
    // 0x13cf9e8: LoadField: r0 = r1->field_7
    //     0x13cf9e8: ldur            w0, [x1, #7]
    // 0x13cf9ec: DecompressPointer r0
    //     0x13cf9ec: add             x0, x0, HEAP, lsl #32
    // 0x13cf9f0: stur            x0, [fp, #-0x28]
    // 0x13cf9f4: r1 = Instance_Color
    //     0x13cf9f4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cf9f8: d0 = 0.700000
    //     0x13cf9f8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13cf9fc: ldr             d0, [x17, #0xf48]
    // 0x13cfa00: r0 = withOpacity()
    //     0x13cfa00: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cfa04: r16 = 12.000000
    //     0x13cfa04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cfa08: ldr             x16, [x16, #0x9e8]
    // 0x13cfa0c: stp             x16, x0, [SP]
    // 0x13cfa10: ldur            x1, [fp, #-0x28]
    // 0x13cfa14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cfa14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cfa18: ldr             x4, [x4, #0x9b8]
    // 0x13cfa1c: r0 = copyWith()
    //     0x13cfa1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cfa20: stur            x0, [fp, #-0x28]
    // 0x13cfa24: r0 = Text()
    //     0x13cfa24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cfa28: mov             x3, x0
    // 0x13cfa2c: ldur            x0, [fp, #-0x18]
    // 0x13cfa30: stur            x3, [fp, #-0x38]
    // 0x13cfa34: StoreField: r3->field_b = r0
    //     0x13cfa34: stur            w0, [x3, #0xb]
    // 0x13cfa38: ldur            x0, [fp, #-0x28]
    // 0x13cfa3c: StoreField: r3->field_13 = r0
    //     0x13cfa3c: stur            w0, [x3, #0x13]
    // 0x13cfa40: r1 = Null
    //     0x13cfa40: mov             x1, NULL
    // 0x13cfa44: r2 = 6
    //     0x13cfa44: movz            x2, #0x6
    // 0x13cfa48: r0 = AllocateArray()
    //     0x13cfa48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cfa4c: mov             x2, x0
    // 0x13cfa50: ldur            x0, [fp, #-0x30]
    // 0x13cfa54: stur            x2, [fp, #-0x18]
    // 0x13cfa58: StoreField: r2->field_f = r0
    //     0x13cfa58: stur            w0, [x2, #0xf]
    // 0x13cfa5c: r16 = Instance_Spacer
    //     0x13cfa5c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x13cfa60: ldr             x16, [x16, #0xf0]
    // 0x13cfa64: StoreField: r2->field_13 = r16
    //     0x13cfa64: stur            w16, [x2, #0x13]
    // 0x13cfa68: ldur            x0, [fp, #-0x38]
    // 0x13cfa6c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cfa6c: stur            w0, [x2, #0x17]
    // 0x13cfa70: r1 = <Widget>
    //     0x13cfa70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cfa74: r0 = AllocateGrowableArray()
    //     0x13cfa74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cfa78: mov             x1, x0
    // 0x13cfa7c: ldur            x0, [fp, #-0x18]
    // 0x13cfa80: stur            x1, [fp, #-0x28]
    // 0x13cfa84: StoreField: r1->field_f = r0
    //     0x13cfa84: stur            w0, [x1, #0xf]
    // 0x13cfa88: r0 = 6
    //     0x13cfa88: movz            x0, #0x6
    // 0x13cfa8c: StoreField: r1->field_b = r0
    //     0x13cfa8c: stur            w0, [x1, #0xb]
    // 0x13cfa90: r0 = Row()
    //     0x13cfa90: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13cfa94: mov             x2, x0
    // 0x13cfa98: r0 = Instance_Axis
    //     0x13cfa98: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13cfa9c: stur            x2, [fp, #-0x18]
    // 0x13cfaa0: StoreField: r2->field_f = r0
    //     0x13cfaa0: stur            w0, [x2, #0xf]
    // 0x13cfaa4: r0 = Instance_MainAxisAlignment
    //     0x13cfaa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cfaa8: ldr             x0, [x0, #0xa08]
    // 0x13cfaac: StoreField: r2->field_13 = r0
    //     0x13cfaac: stur            w0, [x2, #0x13]
    // 0x13cfab0: r0 = Instance_MainAxisSize
    //     0x13cfab0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13cfab4: ldr             x0, [x0, #0xa10]
    // 0x13cfab8: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cfab8: stur            w0, [x2, #0x17]
    // 0x13cfabc: r0 = Instance_CrossAxisAlignment
    //     0x13cfabc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13cfac0: ldr             x0, [x0, #0xa18]
    // 0x13cfac4: StoreField: r2->field_1b = r0
    //     0x13cfac4: stur            w0, [x2, #0x1b]
    // 0x13cfac8: r0 = Instance_VerticalDirection
    //     0x13cfac8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cfacc: ldr             x0, [x0, #0xa20]
    // 0x13cfad0: StoreField: r2->field_23 = r0
    //     0x13cfad0: stur            w0, [x2, #0x23]
    // 0x13cfad4: r0 = Instance_Clip
    //     0x13cfad4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13cfad8: ldr             x0, [x0, #0x38]
    // 0x13cfadc: StoreField: r2->field_2b = r0
    //     0x13cfadc: stur            w0, [x2, #0x2b]
    // 0x13cfae0: StoreField: r2->field_2f = rZR
    //     0x13cfae0: stur            xzr, [x2, #0x2f]
    // 0x13cfae4: ldur            x0, [fp, #-0x28]
    // 0x13cfae8: StoreField: r2->field_b = r0
    //     0x13cfae8: stur            w0, [x2, #0xb]
    // 0x13cfaec: ldur            x0, [fp, #-0x10]
    // 0x13cfaf0: LoadField: r1 = r0->field_f
    //     0x13cfaf0: ldur            w1, [x0, #0xf]
    // 0x13cfaf4: DecompressPointer r1
    //     0x13cfaf4: add             x1, x1, HEAP, lsl #32
    // 0x13cfaf8: r0 = of()
    //     0x13cfaf8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cfafc: LoadField: r2 = r0->field_5b
    //     0x13cfafc: ldur            w2, [x0, #0x5b]
    // 0x13cfb00: DecompressPointer r2
    //     0x13cfb00: add             x2, x2, HEAP, lsl #32
    // 0x13cfb04: ldur            x0, [fp, #-8]
    // 0x13cfb08: stur            x2, [fp, #-0x28]
    // 0x13cfb0c: LoadField: r1 = r0->field_f
    //     0x13cfb0c: ldur            w1, [x0, #0xf]
    // 0x13cfb10: DecompressPointer r1
    //     0x13cfb10: add             x1, x1, HEAP, lsl #32
    // 0x13cfb14: r0 = controller()
    //     0x13cfb14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cfb18: LoadField: r1 = r0->field_4f
    //     0x13cfb18: ldur            w1, [x0, #0x4f]
    // 0x13cfb1c: DecompressPointer r1
    //     0x13cfb1c: add             x1, x1, HEAP, lsl #32
    // 0x13cfb20: r0 = value()
    //     0x13cfb20: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cfb24: LoadField: r1 = r0->field_b
    //     0x13cfb24: ldur            w1, [x0, #0xb]
    // 0x13cfb28: DecompressPointer r1
    //     0x13cfb28: add             x1, x1, HEAP, lsl #32
    // 0x13cfb2c: cmp             w1, NULL
    // 0x13cfb30: b.ne            #0x13cfb40
    // 0x13cfb34: ldur            x2, [fp, #-0x10]
    // 0x13cfb38: r0 = Null
    //     0x13cfb38: mov             x0, NULL
    // 0x13cfb3c: b               #0x13cfb90
    // 0x13cfb40: ldur            x2, [fp, #-0x10]
    // 0x13cfb44: LoadField: r3 = r1->field_f
    //     0x13cfb44: ldur            w3, [x1, #0xf]
    // 0x13cfb48: DecompressPointer r3
    //     0x13cfb48: add             x3, x3, HEAP, lsl #32
    // 0x13cfb4c: LoadField: r0 = r2->field_13
    //     0x13cfb4c: ldur            w0, [x2, #0x13]
    // 0x13cfb50: DecompressPointer r0
    //     0x13cfb50: add             x0, x0, HEAP, lsl #32
    // 0x13cfb54: LoadField: r1 = r3->field_b
    //     0x13cfb54: ldur            w1, [x3, #0xb]
    // 0x13cfb58: r4 = LoadInt32Instr(r0)
    //     0x13cfb58: sbfx            x4, x0, #1, #0x1f
    //     0x13cfb5c: tbz             w0, #0, #0x13cfb64
    //     0x13cfb60: ldur            x4, [x0, #7]
    // 0x13cfb64: r0 = LoadInt32Instr(r1)
    //     0x13cfb64: sbfx            x0, x1, #1, #0x1f
    // 0x13cfb68: mov             x1, x4
    // 0x13cfb6c: cmp             x1, x0
    // 0x13cfb70: b.hs            #0x13cfcc4
    // 0x13cfb74: LoadField: r0 = r3->field_f
    //     0x13cfb74: ldur            w0, [x3, #0xf]
    // 0x13cfb78: DecompressPointer r0
    //     0x13cfb78: add             x0, x0, HEAP, lsl #32
    // 0x13cfb7c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cfb7c: add             x16, x0, x4, lsl #2
    //     0x13cfb80: ldur            w1, [x16, #0xf]
    // 0x13cfb84: DecompressPointer r1
    //     0x13cfb84: add             x1, x1, HEAP, lsl #32
    // 0x13cfb88: LoadField: r0 = r1->field_b
    //     0x13cfb88: ldur            w0, [x1, #0xb]
    // 0x13cfb8c: DecompressPointer r0
    //     0x13cfb8c: add             x0, x0, HEAP, lsl #32
    // 0x13cfb90: cmp             w0, NULL
    // 0x13cfb94: b.ne            #0x13cfba0
    // 0x13cfb98: r4 = ""
    //     0x13cfb98: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cfb9c: b               #0x13cfba4
    // 0x13cfba0: mov             x4, x0
    // 0x13cfba4: ldur            x1, [fp, #-8]
    // 0x13cfba8: ldur            x3, [fp, #-0x18]
    // 0x13cfbac: ldur            x0, [fp, #-0x28]
    // 0x13cfbb0: stur            x4, [fp, #-0x30]
    // 0x13cfbb4: LoadField: r5 = r1->field_f
    //     0x13cfbb4: ldur            w5, [x1, #0xf]
    // 0x13cfbb8: DecompressPointer r5
    //     0x13cfbb8: add             x5, x5, HEAP, lsl #32
    // 0x13cfbbc: mov             x1, x5
    // 0x13cfbc0: r0 = controller()
    //     0x13cfbc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cfbc4: LoadField: r1 = r0->field_73
    //     0x13cfbc4: ldur            w1, [x0, #0x73]
    // 0x13cfbc8: DecompressPointer r1
    //     0x13cfbc8: add             x1, x1, HEAP, lsl #32
    // 0x13cfbcc: r0 = value()
    //     0x13cfbcc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cfbd0: r1 = <String>
    //     0x13cfbd0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x13cfbd4: stur            x0, [fp, #-8]
    // 0x13cfbd8: r0 = RadioListTile()
    //     0x13cfbd8: bl              #0x997b1c  ; AllocateRadioListTileStub -> RadioListTile<X0> (size=0xa0)
    // 0x13cfbdc: mov             x3, x0
    // 0x13cfbe0: ldur            x0, [fp, #-0x30]
    // 0x13cfbe4: stur            x3, [fp, #-0x38]
    // 0x13cfbe8: StoreField: r3->field_f = r0
    //     0x13cfbe8: stur            w0, [x3, #0xf]
    // 0x13cfbec: ldur            x0, [fp, #-8]
    // 0x13cfbf0: StoreField: r3->field_13 = r0
    //     0x13cfbf0: stur            w0, [x3, #0x13]
    // 0x13cfbf4: ldur            x2, [fp, #-0x10]
    // 0x13cfbf8: r1 = Function '<anonymous closure>':.
    //     0x13cfbf8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38048] AnonymousClosure: (0x13cf540), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x1505aa4)
    //     0x13cfbfc: ldr             x1, [x1, #0x48]
    // 0x13cfc00: r0 = AllocateClosure()
    //     0x13cfc00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cfc04: mov             x1, x0
    // 0x13cfc08: ldur            x0, [fp, #-0x38]
    // 0x13cfc0c: ArrayStore: r0[0] = r1  ; List_4
    //     0x13cfc0c: stur            w1, [x0, #0x17]
    // 0x13cfc10: r1 = false
    //     0x13cfc10: add             x1, NULL, #0x30  ; false
    // 0x13cfc14: StoreField: r0->field_1f = r1
    //     0x13cfc14: stur            w1, [x0, #0x1f]
    // 0x13cfc18: ldur            x2, [fp, #-0x28]
    // 0x13cfc1c: StoreField: r0->field_23 = r2
    //     0x13cfc1c: stur            w2, [x0, #0x23]
    // 0x13cfc20: ldur            x2, [fp, #-0x18]
    // 0x13cfc24: StoreField: r0->field_3b = r2
    //     0x13cfc24: stur            w2, [x0, #0x3b]
    // 0x13cfc28: r2 = true
    //     0x13cfc28: add             x2, NULL, #0x20  ; true
    // 0x13cfc2c: StoreField: r0->field_4b = r2
    //     0x13cfc2c: stur            w2, [x0, #0x4b]
    // 0x13cfc30: StoreField: r0->field_4f = r1
    //     0x13cfc30: stur            w1, [x0, #0x4f]
    // 0x13cfc34: StoreField: r0->field_57 = r1
    //     0x13cfc34: stur            w1, [x0, #0x57]
    // 0x13cfc38: r2 = Instance_EdgeInsets
    //     0x13cfc38: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x13cfc3c: StoreField: r0->field_5b = r2
    //     0x13cfc3c: stur            w2, [x0, #0x5b]
    // 0x13cfc40: d0 = 1.000000
    //     0x13cfc40: fmov            d0, #1.00000000
    // 0x13cfc44: StoreField: r0->field_8b = d0
    //     0x13cfc44: stur            d0, [x0, #0x8b]
    // 0x13cfc48: StoreField: r0->field_83 = r1
    //     0x13cfc48: stur            w1, [x0, #0x83]
    // 0x13cfc4c: r2 = Instance__RadioType
    //     0x13cfc4c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38050] Obj!_RadioType@d74141
    //     0x13cfc50: ldr             x2, [x2, #0x50]
    // 0x13cfc54: StoreField: r0->field_7b = r2
    //     0x13cfc54: stur            w2, [x0, #0x7b]
    // 0x13cfc58: StoreField: r0->field_87 = r1
    //     0x13cfc58: stur            w1, [x0, #0x87]
    // 0x13cfc5c: r0 = Padding()
    //     0x13cfc5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cfc60: mov             x1, x0
    // 0x13cfc64: r0 = Instance_EdgeInsets
    //     0x13cfc64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0x13cfc68: ldr             x0, [x0, #0xd48]
    // 0x13cfc6c: stur            x1, [fp, #-8]
    // 0x13cfc70: StoreField: r1->field_f = r0
    //     0x13cfc70: stur            w0, [x1, #0xf]
    // 0x13cfc74: ldur            x0, [fp, #-0x38]
    // 0x13cfc78: StoreField: r1->field_b = r0
    //     0x13cfc78: stur            w0, [x1, #0xb]
    // 0x13cfc7c: r0 = Container()
    //     0x13cfc7c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13cfc80: stur            x0, [fp, #-0x10]
    // 0x13cfc84: ldur            x16, [fp, #-0x20]
    // 0x13cfc88: ldur            lr, [fp, #-8]
    // 0x13cfc8c: stp             lr, x16, [SP]
    // 0x13cfc90: mov             x1, x0
    // 0x13cfc94: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13cfc94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13cfc98: ldr             x4, [x4, #0x88]
    // 0x13cfc9c: r0 = Container()
    //     0x13cfc9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13cfca0: ldur            x0, [fp, #-0x10]
    // 0x13cfca4: LeaveFrame
    //     0x13cfca4: mov             SP, fp
    //     0x13cfca8: ldp             fp, lr, [SP], #0x10
    // 0x13cfcac: ret
    //     0x13cfcac: ret             
    // 0x13cfcb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cfcb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cfcb4: b               #0x13cf6ac
    // 0x13cfcb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cfcb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13cfcbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cfcbc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13cfcc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cfcc0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13cfcc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cfcc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x13cfcc8, size: 0x1a8
    // 0x13cfcc8: EnterFrame
    //     0x13cfcc8: stp             fp, lr, [SP, #-0x10]!
    //     0x13cfccc: mov             fp, SP
    // 0x13cfcd0: AllocStack(0x18)
    //     0x13cfcd0: sub             SP, SP, #0x18
    // 0x13cfcd4: SetupParameters()
    //     0x13cfcd4: ldr             x0, [fp, #0x20]
    //     0x13cfcd8: ldur            w1, [x0, #0x17]
    //     0x13cfcdc: add             x1, x1, HEAP, lsl #32
    //     0x13cfce0: stur            x1, [fp, #-8]
    // 0x13cfce4: CheckStackOverflow
    //     0x13cfce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cfce8: cmp             SP, x16
    //     0x13cfcec: b.ls            #0x13cfe64
    // 0x13cfcf0: r1 = 2
    //     0x13cfcf0: movz            x1, #0x2
    // 0x13cfcf4: r0 = AllocateContext()
    //     0x13cfcf4: bl              #0x16f6108  ; AllocateContextStub
    // 0x13cfcf8: mov             x2, x0
    // 0x13cfcfc: ldur            x0, [fp, #-8]
    // 0x13cfd00: stur            x2, [fp, #-0x10]
    // 0x13cfd04: StoreField: r2->field_b = r0
    //     0x13cfd04: stur            w0, [x2, #0xb]
    // 0x13cfd08: ldr             x1, [fp, #0x18]
    // 0x13cfd0c: StoreField: r2->field_f = r1
    //     0x13cfd0c: stur            w1, [x2, #0xf]
    // 0x13cfd10: ldr             x3, [fp, #0x10]
    // 0x13cfd14: StoreField: r2->field_13 = r3
    //     0x13cfd14: stur            w3, [x2, #0x13]
    // 0x13cfd18: LoadField: r1 = r0->field_f
    //     0x13cfd18: ldur            w1, [x0, #0xf]
    // 0x13cfd1c: DecompressPointer r1
    //     0x13cfd1c: add             x1, x1, HEAP, lsl #32
    // 0x13cfd20: r0 = controller()
    //     0x13cfd20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cfd24: LoadField: r1 = r0->field_4f
    //     0x13cfd24: ldur            w1, [x0, #0x4f]
    // 0x13cfd28: DecompressPointer r1
    //     0x13cfd28: add             x1, x1, HEAP, lsl #32
    // 0x13cfd2c: r0 = value()
    //     0x13cfd2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cfd30: LoadField: r1 = r0->field_b
    //     0x13cfd30: ldur            w1, [x0, #0xb]
    // 0x13cfd34: DecompressPointer r1
    //     0x13cfd34: add             x1, x1, HEAP, lsl #32
    // 0x13cfd38: cmp             w1, NULL
    // 0x13cfd3c: b.ne            #0x13cfd48
    // 0x13cfd40: r0 = Null
    //     0x13cfd40: mov             x0, NULL
    // 0x13cfd44: b               #0x13cfd90
    // 0x13cfd48: ldr             x0, [fp, #0x10]
    // 0x13cfd4c: LoadField: r2 = r1->field_f
    //     0x13cfd4c: ldur            w2, [x1, #0xf]
    // 0x13cfd50: DecompressPointer r2
    //     0x13cfd50: add             x2, x2, HEAP, lsl #32
    // 0x13cfd54: LoadField: r1 = r2->field_b
    //     0x13cfd54: ldur            w1, [x2, #0xb]
    // 0x13cfd58: r3 = LoadInt32Instr(r0)
    //     0x13cfd58: sbfx            x3, x0, #1, #0x1f
    //     0x13cfd5c: tbz             w0, #0, #0x13cfd64
    //     0x13cfd60: ldur            x3, [x0, #7]
    // 0x13cfd64: r0 = LoadInt32Instr(r1)
    //     0x13cfd64: sbfx            x0, x1, #1, #0x1f
    // 0x13cfd68: mov             x1, x3
    // 0x13cfd6c: cmp             x1, x0
    // 0x13cfd70: b.hs            #0x13cfe6c
    // 0x13cfd74: LoadField: r0 = r2->field_f
    //     0x13cfd74: ldur            w0, [x2, #0xf]
    // 0x13cfd78: DecompressPointer r0
    //     0x13cfd78: add             x0, x0, HEAP, lsl #32
    // 0x13cfd7c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x13cfd7c: add             x16, x0, x3, lsl #2
    //     0x13cfd80: ldur            w1, [x16, #0xf]
    // 0x13cfd84: DecompressPointer r1
    //     0x13cfd84: add             x1, x1, HEAP, lsl #32
    // 0x13cfd88: LoadField: r0 = r1->field_7
    //     0x13cfd88: ldur            w0, [x1, #7]
    // 0x13cfd8c: DecompressPointer r0
    //     0x13cfd8c: add             x0, x0, HEAP, lsl #32
    // 0x13cfd90: cmp             w0, NULL
    // 0x13cfd94: b.eq            #0x13cfd9c
    // 0x13cfd98: tbz             w0, #4, #0x13cfdc0
    // 0x13cfd9c: r0 = Container()
    //     0x13cfd9c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13cfda0: mov             x1, x0
    // 0x13cfda4: stur            x0, [fp, #-0x18]
    // 0x13cfda8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13cfda8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13cfdac: r0 = Container()
    //     0x13cfdac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13cfdb0: ldur            x0, [fp, #-0x18]
    // 0x13cfdb4: LeaveFrame
    //     0x13cfdb4: mov             SP, fp
    //     0x13cfdb8: ldp             fp, lr, [SP], #0x10
    // 0x13cfdbc: ret
    //     0x13cfdbc: ret             
    // 0x13cfdc0: ldur            x0, [fp, #-8]
    // 0x13cfdc4: LoadField: r1 = r0->field_f
    //     0x13cfdc4: ldur            w1, [x0, #0xf]
    // 0x13cfdc8: DecompressPointer r1
    //     0x13cfdc8: add             x1, x1, HEAP, lsl #32
    // 0x13cfdcc: r0 = controller()
    //     0x13cfdcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cfdd0: LoadField: r2 = r0->field_73
    //     0x13cfdd0: ldur            w2, [x0, #0x73]
    // 0x13cfdd4: DecompressPointer r2
    //     0x13cfdd4: add             x2, x2, HEAP, lsl #32
    // 0x13cfdd8: ldur            x0, [fp, #-8]
    // 0x13cfddc: stur            x2, [fp, #-0x18]
    // 0x13cfde0: LoadField: r1 = r0->field_f
    //     0x13cfde0: ldur            w1, [x0, #0xf]
    // 0x13cfde4: DecompressPointer r1
    //     0x13cfde4: add             x1, x1, HEAP, lsl #32
    // 0x13cfde8: r0 = controller()
    //     0x13cfde8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cfdec: LoadField: r1 = r0->field_4f
    //     0x13cfdec: ldur            w1, [x0, #0x4f]
    // 0x13cfdf0: DecompressPointer r1
    //     0x13cfdf0: add             x1, x1, HEAP, lsl #32
    // 0x13cfdf4: r0 = value()
    //     0x13cfdf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cfdf8: LoadField: r1 = r0->field_b
    //     0x13cfdf8: ldur            w1, [x0, #0xb]
    // 0x13cfdfc: DecompressPointer r1
    //     0x13cfdfc: add             x1, x1, HEAP, lsl #32
    // 0x13cfe00: cmp             w1, NULL
    // 0x13cfe04: b.ne            #0x13cfe10
    // 0x13cfe08: r0 = Null
    //     0x13cfe08: mov             x0, NULL
    // 0x13cfe0c: b               #0x13cfe18
    // 0x13cfe10: LoadField: r0 = r1->field_13
    //     0x13cfe10: ldur            w0, [x1, #0x13]
    // 0x13cfe14: DecompressPointer r0
    //     0x13cfe14: add             x0, x0, HEAP, lsl #32
    // 0x13cfe18: cmp             w0, NULL
    // 0x13cfe1c: b.ne            #0x13cfe28
    // 0x13cfe20: r2 = ""
    //     0x13cfe20: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cfe24: b               #0x13cfe2c
    // 0x13cfe28: mov             x2, x0
    // 0x13cfe2c: ldur            x1, [fp, #-0x18]
    // 0x13cfe30: r0 = value=()
    //     0x13cfe30: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13cfe34: r0 = Obx()
    //     0x13cfe34: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13cfe38: ldur            x2, [fp, #-0x10]
    // 0x13cfe3c: r1 = Function '<anonymous closure>':.
    //     0x13cfe3c: add             x1, PP, #0x38, lsl #12  ; [pp+0x38038] AnonymousClosure: (0x13cf684), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x1505aa4)
    //     0x13cfe40: ldr             x1, [x1, #0x38]
    // 0x13cfe44: stur            x0, [fp, #-8]
    // 0x13cfe48: r0 = AllocateClosure()
    //     0x13cfe48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cfe4c: mov             x1, x0
    // 0x13cfe50: ldur            x0, [fp, #-8]
    // 0x13cfe54: StoreField: r0->field_b = r1
    //     0x13cfe54: stur            w1, [x0, #0xb]
    // 0x13cfe58: LeaveFrame
    //     0x13cfe58: mov             SP, fp
    //     0x13cfe5c: ldp             fp, lr, [SP], #0x10
    // 0x13cfe60: ret
    //     0x13cfe60: ret             
    // 0x13cfe64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cfe64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cfe68: b               #0x13cfcf0
    // 0x13cfe6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cfe6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x13cfe70, size: 0xb8c
    // 0x13cfe70: EnterFrame
    //     0x13cfe70: stp             fp, lr, [SP, #-0x10]!
    //     0x13cfe74: mov             fp, SP
    // 0x13cfe78: AllocStack(0x70)
    //     0x13cfe78: sub             SP, SP, #0x70
    // 0x13cfe7c: SetupParameters()
    //     0x13cfe7c: ldr             x0, [fp, #0x10]
    //     0x13cfe80: ldur            w2, [x0, #0x17]
    //     0x13cfe84: add             x2, x2, HEAP, lsl #32
    //     0x13cfe88: stur            x2, [fp, #-8]
    // 0x13cfe8c: CheckStackOverflow
    //     0x13cfe8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cfe90: cmp             SP, x16
    //     0x13cfe94: b.ls            #0x13d09d8
    // 0x13cfe98: r1 = Instance_Color
    //     0x13cfe98: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cfe9c: d0 = 0.100000
    //     0x13cfe9c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13cfea0: r0 = withOpacity()
    //     0x13cfea0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cfea4: mov             x2, x0
    // 0x13cfea8: r1 = Null
    //     0x13cfea8: mov             x1, NULL
    // 0x13cfeac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13cfeac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13cfeb0: r0 = Border.all()
    //     0x13cfeb0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13cfeb4: stur            x0, [fp, #-0x10]
    // 0x13cfeb8: r0 = BoxDecoration()
    //     0x13cfeb8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13cfebc: mov             x2, x0
    // 0x13cfec0: ldur            x0, [fp, #-0x10]
    // 0x13cfec4: stur            x2, [fp, #-0x18]
    // 0x13cfec8: StoreField: r2->field_f = r0
    //     0x13cfec8: stur            w0, [x2, #0xf]
    // 0x13cfecc: r0 = Instance_BoxShape
    //     0x13cfecc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13cfed0: ldr             x0, [x0, #0x80]
    // 0x13cfed4: StoreField: r2->field_23 = r0
    //     0x13cfed4: stur            w0, [x2, #0x23]
    // 0x13cfed8: ldur            x3, [fp, #-8]
    // 0x13cfedc: LoadField: r1 = r3->field_13
    //     0x13cfedc: ldur            w1, [x3, #0x13]
    // 0x13cfee0: DecompressPointer r1
    //     0x13cfee0: add             x1, x1, HEAP, lsl #32
    // 0x13cfee4: r0 = of()
    //     0x13cfee4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cfee8: LoadField: r1 = r0->field_87
    //     0x13cfee8: ldur            w1, [x0, #0x87]
    // 0x13cfeec: DecompressPointer r1
    //     0x13cfeec: add             x1, x1, HEAP, lsl #32
    // 0x13cfef0: LoadField: r0 = r1->field_7
    //     0x13cfef0: ldur            w0, [x1, #7]
    // 0x13cfef4: DecompressPointer r0
    //     0x13cfef4: add             x0, x0, HEAP, lsl #32
    // 0x13cfef8: r16 = Instance_Color
    //     0x13cfef8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cfefc: r30 = 21.000000
    //     0x13cfefc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x13cff00: ldr             lr, [lr, #0x9b0]
    // 0x13cff04: stp             lr, x16, [SP]
    // 0x13cff08: mov             x1, x0
    // 0x13cff0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cff0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cff10: ldr             x4, [x4, #0x9b8]
    // 0x13cff14: r0 = copyWith()
    //     0x13cff14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cff18: stur            x0, [fp, #-0x10]
    // 0x13cff1c: r0 = Text()
    //     0x13cff1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cff20: mov             x2, x0
    // 0x13cff24: r0 = "WANT TO EXCHANGE \?"
    //     0x13cff24: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ff0] "WANT TO EXCHANGE \?"
    //     0x13cff28: ldr             x0, [x0, #0xff0]
    // 0x13cff2c: stur            x2, [fp, #-0x20]
    // 0x13cff30: StoreField: r2->field_b = r0
    //     0x13cff30: stur            w0, [x2, #0xb]
    // 0x13cff34: ldur            x0, [fp, #-0x10]
    // 0x13cff38: StoreField: r2->field_13 = r0
    //     0x13cff38: stur            w0, [x2, #0x13]
    // 0x13cff3c: ldur            x0, [fp, #-8]
    // 0x13cff40: LoadField: r1 = r0->field_13
    //     0x13cff40: ldur            w1, [x0, #0x13]
    // 0x13cff44: DecompressPointer r1
    //     0x13cff44: add             x1, x1, HEAP, lsl #32
    // 0x13cff48: r0 = of()
    //     0x13cff48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cff4c: LoadField: r1 = r0->field_87
    //     0x13cff4c: ldur            w1, [x0, #0x87]
    // 0x13cff50: DecompressPointer r1
    //     0x13cff50: add             x1, x1, HEAP, lsl #32
    // 0x13cff54: LoadField: r0 = r1->field_2b
    //     0x13cff54: ldur            w0, [x1, #0x2b]
    // 0x13cff58: DecompressPointer r0
    //     0x13cff58: add             x0, x0, HEAP, lsl #32
    // 0x13cff5c: stur            x0, [fp, #-0x10]
    // 0x13cff60: r1 = Instance_Color
    //     0x13cff60: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cff64: d0 = 0.700000
    //     0x13cff64: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13cff68: ldr             d0, [x17, #0xf48]
    // 0x13cff6c: r0 = withOpacity()
    //     0x13cff6c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cff70: r16 = 12.000000
    //     0x13cff70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cff74: ldr             x16, [x16, #0x9e8]
    // 0x13cff78: stp             x16, x0, [SP]
    // 0x13cff7c: ldur            x1, [fp, #-0x10]
    // 0x13cff80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cff80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cff84: ldr             x4, [x4, #0x9b8]
    // 0x13cff88: r0 = copyWith()
    //     0x13cff88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cff8c: stur            x0, [fp, #-0x10]
    // 0x13cff90: r0 = Text()
    //     0x13cff90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cff94: mov             x3, x0
    // 0x13cff98: r0 = "Like something else\? No worries, just exchange"
    //     0x13cff98: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ff8] "Like something else\? No worries, just exchange"
    //     0x13cff9c: ldr             x0, [x0, #0xff8]
    // 0x13cffa0: stur            x3, [fp, #-0x28]
    // 0x13cffa4: StoreField: r3->field_b = r0
    //     0x13cffa4: stur            w0, [x3, #0xb]
    // 0x13cffa8: ldur            x0, [fp, #-0x10]
    // 0x13cffac: StoreField: r3->field_13 = r0
    //     0x13cffac: stur            w0, [x3, #0x13]
    // 0x13cffb0: r1 = <Widget>
    //     0x13cffb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cffb4: r2 = 30
    //     0x13cffb4: movz            x2, #0x1e
    // 0x13cffb8: r0 = _GrowableList()
    //     0x13cffb8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x13cffbc: stur            x0, [fp, #-0x10]
    // 0x13cffc0: r2 = 0
    //     0x13cffc0: movz            x2, #0
    // 0x13cffc4: stur            x2, [fp, #-0x30]
    // 0x13cffc8: CheckStackOverflow
    //     0x13cffc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cffcc: cmp             SP, x16
    //     0x13cffd0: b.ls            #0x13d09e0
    // 0x13cffd4: LoadField: r1 = r0->field_b
    //     0x13cffd4: ldur            w1, [x0, #0xb]
    // 0x13cffd8: r3 = LoadInt32Instr(r1)
    //     0x13cffd8: sbfx            x3, x1, #1, #0x1f
    // 0x13cffdc: cmp             x2, x3
    // 0x13cffe0: b.ge            #0x13d00d4
    // 0x13cffe4: tbnz            w2, #0, #0x13cfff8
    // 0x13cffe8: mov             x1, x2
    // 0x13cffec: r2 = Instance_Color
    //     0x13cffec: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x13cfff0: ldr             x2, [x2, #0xf88]
    // 0x13cfff4: b               #0x13d0010
    // 0x13cfff8: r1 = Instance_Color
    //     0x13cfff8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cfffc: d0 = 0.100000
    //     0x13cfffc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13d0000: r0 = withOpacity()
    //     0x13d0000: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13d0004: mov             x2, x0
    // 0x13d0008: ldur            x0, [fp, #-0x10]
    // 0x13d000c: ldur            x1, [fp, #-0x30]
    // 0x13d0010: stur            x2, [fp, #-0x38]
    // 0x13d0014: r0 = Container()
    //     0x13d0014: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13d0018: stur            x0, [fp, #-0x40]
    // 0x13d001c: ldur            x16, [fp, #-0x38]
    // 0x13d0020: r30 = 1.000000
    //     0x13d0020: ldr             lr, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x13d0024: stp             lr, x16, [SP]
    // 0x13d0028: mov             x1, x0
    // 0x13d002c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, height, 0x2, null]
    //     0x13d002c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd30] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "height", 0x2, Null]
    //     0x13d0030: ldr             x4, [x4, #0xd30]
    // 0x13d0034: r0 = Container()
    //     0x13d0034: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13d0038: r1 = <FlexParentData>
    //     0x13d0038: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13d003c: ldr             x1, [x1, #0xe00]
    // 0x13d0040: r0 = Expanded()
    //     0x13d0040: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x13d0044: mov             x2, x0
    // 0x13d0048: r0 = 1
    //     0x13d0048: movz            x0, #0x1
    // 0x13d004c: stur            x2, [fp, #-0x38]
    // 0x13d0050: StoreField: r2->field_13 = r0
    //     0x13d0050: stur            x0, [x2, #0x13]
    // 0x13d0054: r3 = Instance_FlexFit
    //     0x13d0054: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13d0058: ldr             x3, [x3, #0xe08]
    // 0x13d005c: StoreField: r2->field_1b = r3
    //     0x13d005c: stur            w3, [x2, #0x1b]
    // 0x13d0060: ldur            x1, [fp, #-0x40]
    // 0x13d0064: StoreField: r2->field_b = r1
    //     0x13d0064: stur            w1, [x2, #0xb]
    // 0x13d0068: mov             x1, x2
    // 0x13d006c: r0 = _NativeScene._()
    //     0x13d006c: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0x13d0070: ldur            x3, [fp, #-0x10]
    // 0x13d0074: LoadField: r0 = r3->field_b
    //     0x13d0074: ldur            w0, [x3, #0xb]
    // 0x13d0078: r1 = LoadInt32Instr(r0)
    //     0x13d0078: sbfx            x1, x0, #1, #0x1f
    // 0x13d007c: mov             x0, x1
    // 0x13d0080: ldur            x1, [fp, #-0x30]
    // 0x13d0084: cmp             x1, x0
    // 0x13d0088: b.hs            #0x13d09e8
    // 0x13d008c: LoadField: r1 = r3->field_f
    //     0x13d008c: ldur            w1, [x3, #0xf]
    // 0x13d0090: DecompressPointer r1
    //     0x13d0090: add             x1, x1, HEAP, lsl #32
    // 0x13d0094: ldur            x0, [fp, #-0x38]
    // 0x13d0098: ldur            x2, [fp, #-0x30]
    // 0x13d009c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x13d009c: add             x25, x1, x2, lsl #2
    //     0x13d00a0: add             x25, x25, #0xf
    //     0x13d00a4: str             w0, [x25]
    //     0x13d00a8: tbz             w0, #0, #0x13d00c4
    //     0x13d00ac: ldurb           w16, [x1, #-1]
    //     0x13d00b0: ldurb           w17, [x0, #-1]
    //     0x13d00b4: and             x16, x17, x16, lsr #2
    //     0x13d00b8: tst             x16, HEAP, lsr #32
    //     0x13d00bc: b.eq            #0x13d00c4
    //     0x13d00c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13d00c4: add             x0, x2, #1
    // 0x13d00c8: mov             x2, x0
    // 0x13d00cc: mov             x0, x3
    // 0x13d00d0: b               #0x13cffc4
    // 0x13d00d4: ldur            x2, [fp, #-8]
    // 0x13d00d8: mov             x3, x0
    // 0x13d00dc: r0 = Row()
    //     0x13d00dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13d00e0: mov             x1, x0
    // 0x13d00e4: r0 = Instance_Axis
    //     0x13d00e4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13d00e8: stur            x1, [fp, #-0x38]
    // 0x13d00ec: StoreField: r1->field_f = r0
    //     0x13d00ec: stur            w0, [x1, #0xf]
    // 0x13d00f0: r0 = Instance_MainAxisAlignment
    //     0x13d00f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13d00f4: ldr             x0, [x0, #0xa08]
    // 0x13d00f8: StoreField: r1->field_13 = r0
    //     0x13d00f8: stur            w0, [x1, #0x13]
    // 0x13d00fc: r2 = Instance_MainAxisSize
    //     0x13d00fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13d0100: ldr             x2, [x2, #0xa10]
    // 0x13d0104: ArrayStore: r1[0] = r2  ; List_4
    //     0x13d0104: stur            w2, [x1, #0x17]
    // 0x13d0108: r3 = Instance_CrossAxisAlignment
    //     0x13d0108: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13d010c: ldr             x3, [x3, #0xa18]
    // 0x13d0110: StoreField: r1->field_1b = r3
    //     0x13d0110: stur            w3, [x1, #0x1b]
    // 0x13d0114: r4 = Instance_VerticalDirection
    //     0x13d0114: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13d0118: ldr             x4, [x4, #0xa20]
    // 0x13d011c: StoreField: r1->field_23 = r4
    //     0x13d011c: stur            w4, [x1, #0x23]
    // 0x13d0120: r5 = Instance_Clip
    //     0x13d0120: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13d0124: ldr             x5, [x5, #0x38]
    // 0x13d0128: StoreField: r1->field_2b = r5
    //     0x13d0128: stur            w5, [x1, #0x2b]
    // 0x13d012c: StoreField: r1->field_2f = rZR
    //     0x13d012c: stur            xzr, [x1, #0x2f]
    // 0x13d0130: ldur            x6, [fp, #-0x10]
    // 0x13d0134: StoreField: r1->field_b = r6
    //     0x13d0134: stur            w6, [x1, #0xb]
    // 0x13d0138: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13d0138: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13d013c: ldr             x0, [x0, #0x1c80]
    //     0x13d0140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13d0144: cmp             w0, w16
    //     0x13d0148: b.ne            #0x13d0154
    //     0x13d014c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13d0150: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13d0154: r0 = GetNavigation.size()
    //     0x13d0154: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13d0158: LoadField: d0 = r0->field_f
    //     0x13d0158: ldur            d0, [x0, #0xf]
    // 0x13d015c: d1 = 0.150000
    //     0x13d015c: ldr             d1, [PP, #0x5788]  ; [pp+0x5788] IMM: double(0.15) from 0x3fc3333333333333
    // 0x13d0160: fmul            d2, d0, d1
    // 0x13d0164: ldur            x2, [fp, #-8]
    // 0x13d0168: stur            d2, [fp, #-0x50]
    // 0x13d016c: LoadField: r1 = r2->field_f
    //     0x13d016c: ldur            w1, [x2, #0xf]
    // 0x13d0170: DecompressPointer r1
    //     0x13d0170: add             x1, x1, HEAP, lsl #32
    // 0x13d0174: r0 = controller()
    //     0x13d0174: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13d0178: LoadField: r1 = r0->field_4f
    //     0x13d0178: ldur            w1, [x0, #0x4f]
    // 0x13d017c: DecompressPointer r1
    //     0x13d017c: add             x1, x1, HEAP, lsl #32
    // 0x13d0180: r0 = value()
    //     0x13d0180: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13d0184: LoadField: r1 = r0->field_b
    //     0x13d0184: ldur            w1, [x0, #0xb]
    // 0x13d0188: DecompressPointer r1
    //     0x13d0188: add             x1, x1, HEAP, lsl #32
    // 0x13d018c: cmp             w1, NULL
    // 0x13d0190: b.ne            #0x13d019c
    // 0x13d0194: r6 = Null
    //     0x13d0194: mov             x6, NULL
    // 0x13d0198: b               #0x13d01ac
    // 0x13d019c: LoadField: r0 = r1->field_7
    //     0x13d019c: ldur            w0, [x1, #7]
    // 0x13d01a0: DecompressPointer r0
    //     0x13d01a0: add             x0, x0, HEAP, lsl #32
    // 0x13d01a4: LoadField: r1 = r0->field_b
    //     0x13d01a4: ldur            w1, [x0, #0xb]
    // 0x13d01a8: mov             x6, x1
    // 0x13d01ac: ldur            x0, [fp, #-8]
    // 0x13d01b0: ldur            x5, [fp, #-0x20]
    // 0x13d01b4: ldur            x4, [fp, #-0x28]
    // 0x13d01b8: ldur            x3, [fp, #-0x38]
    // 0x13d01bc: ldur            d0, [fp, #-0x50]
    // 0x13d01c0: mov             x2, x0
    // 0x13d01c4: stur            x6, [fp, #-0x10]
    // 0x13d01c8: r1 = Function '<anonymous closure>':.
    //     0x13d01c8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38000] AnonymousClosure: (0x13d09fc), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x1505aa4)
    //     0x13d01cc: ldr             x1, [x1]
    // 0x13d01d0: r0 = AllocateClosure()
    //     0x13d01d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13d01d4: stur            x0, [fp, #-0x40]
    // 0x13d01d8: r0 = ListView()
    //     0x13d01d8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x13d01dc: stur            x0, [fp, #-0x48]
    // 0x13d01e0: r16 = true
    //     0x13d01e0: add             x16, NULL, #0x20  ; true
    // 0x13d01e4: r30 = Instance_NeverScrollableScrollPhysics
    //     0x13d01e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x13d01e8: ldr             lr, [lr, #0x1c8]
    // 0x13d01ec: stp             lr, x16, [SP]
    // 0x13d01f0: mov             x1, x0
    // 0x13d01f4: ldur            x2, [fp, #-0x40]
    // 0x13d01f8: ldur            x3, [fp, #-0x10]
    // 0x13d01fc: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x13d01fc: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x13d0200: ldr             x4, [x4, #8]
    // 0x13d0204: r0 = ListView.builder()
    //     0x13d0204: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x13d0208: ldur            d0, [fp, #-0x50]
    // 0x13d020c: r0 = inline_Allocate_Double()
    //     0x13d020c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13d0210: add             x0, x0, #0x10
    //     0x13d0214: cmp             x1, x0
    //     0x13d0218: b.ls            #0x13d09ec
    //     0x13d021c: str             x0, [THR, #0x50]  ; THR::top
    //     0x13d0220: sub             x0, x0, #0xf
    //     0x13d0224: movz            x1, #0xe15c
    //     0x13d0228: movk            x1, #0x3, lsl #16
    //     0x13d022c: stur            x1, [x0, #-1]
    // 0x13d0230: StoreField: r0->field_7 = d0
    //     0x13d0230: stur            d0, [x0, #7]
    // 0x13d0234: stur            x0, [fp, #-0x10]
    // 0x13d0238: r0 = SizedBox()
    //     0x13d0238: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13d023c: mov             x3, x0
    // 0x13d0240: ldur            x0, [fp, #-0x10]
    // 0x13d0244: stur            x3, [fp, #-0x40]
    // 0x13d0248: StoreField: r3->field_13 = r0
    //     0x13d0248: stur            w0, [x3, #0x13]
    // 0x13d024c: ldur            x0, [fp, #-0x48]
    // 0x13d0250: StoreField: r3->field_b = r0
    //     0x13d0250: stur            w0, [x3, #0xb]
    // 0x13d0254: r1 = Null
    //     0x13d0254: mov             x1, NULL
    // 0x13d0258: r2 = 14
    //     0x13d0258: movz            x2, #0xe
    // 0x13d025c: r0 = AllocateArray()
    //     0x13d025c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13d0260: mov             x2, x0
    // 0x13d0264: ldur            x0, [fp, #-0x20]
    // 0x13d0268: stur            x2, [fp, #-0x10]
    // 0x13d026c: StoreField: r2->field_f = r0
    //     0x13d026c: stur            w0, [x2, #0xf]
    // 0x13d0270: r16 = Instance_SizedBox
    //     0x13d0270: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x13d0274: ldr             x16, [x16, #0xc70]
    // 0x13d0278: StoreField: r2->field_13 = r16
    //     0x13d0278: stur            w16, [x2, #0x13]
    // 0x13d027c: ldur            x0, [fp, #-0x28]
    // 0x13d0280: ArrayStore: r2[0] = r0  ; List_4
    //     0x13d0280: stur            w0, [x2, #0x17]
    // 0x13d0284: r16 = Instance_SizedBox
    //     0x13d0284: add             x16, PP, #0x38, lsl #12  ; [pp+0x38010] Obj!SizedBox@d681e1
    //     0x13d0288: ldr             x16, [x16, #0x10]
    // 0x13d028c: StoreField: r2->field_1b = r16
    //     0x13d028c: stur            w16, [x2, #0x1b]
    // 0x13d0290: ldur            x0, [fp, #-0x38]
    // 0x13d0294: StoreField: r2->field_1f = r0
    //     0x13d0294: stur            w0, [x2, #0x1f]
    // 0x13d0298: r16 = Instance_SizedBox
    //     0x13d0298: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x13d029c: ldr             x16, [x16, #0x9f0]
    // 0x13d02a0: StoreField: r2->field_23 = r16
    //     0x13d02a0: stur            w16, [x2, #0x23]
    // 0x13d02a4: ldur            x0, [fp, #-0x40]
    // 0x13d02a8: StoreField: r2->field_27 = r0
    //     0x13d02a8: stur            w0, [x2, #0x27]
    // 0x13d02ac: r1 = <Widget>
    //     0x13d02ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13d02b0: r0 = AllocateGrowableArray()
    //     0x13d02b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13d02b4: mov             x1, x0
    // 0x13d02b8: ldur            x0, [fp, #-0x10]
    // 0x13d02bc: stur            x1, [fp, #-0x20]
    // 0x13d02c0: StoreField: r1->field_f = r0
    //     0x13d02c0: stur            w0, [x1, #0xf]
    // 0x13d02c4: r0 = 14
    //     0x13d02c4: movz            x0, #0xe
    // 0x13d02c8: StoreField: r1->field_b = r0
    //     0x13d02c8: stur            w0, [x1, #0xb]
    // 0x13d02cc: r0 = Column()
    //     0x13d02cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13d02d0: mov             x1, x0
    // 0x13d02d4: r0 = Instance_Axis
    //     0x13d02d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13d02d8: stur            x1, [fp, #-0x10]
    // 0x13d02dc: StoreField: r1->field_f = r0
    //     0x13d02dc: stur            w0, [x1, #0xf]
    // 0x13d02e0: r2 = Instance_MainAxisAlignment
    //     0x13d02e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13d02e4: ldr             x2, [x2, #0xa08]
    // 0x13d02e8: StoreField: r1->field_13 = r2
    //     0x13d02e8: stur            w2, [x1, #0x13]
    // 0x13d02ec: r3 = Instance_MainAxisSize
    //     0x13d02ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13d02f0: ldr             x3, [x3, #0xa10]
    // 0x13d02f4: ArrayStore: r1[0] = r3  ; List_4
    //     0x13d02f4: stur            w3, [x1, #0x17]
    // 0x13d02f8: r4 = Instance_CrossAxisAlignment
    //     0x13d02f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13d02fc: ldr             x4, [x4, #0xa18]
    // 0x13d0300: StoreField: r1->field_1b = r4
    //     0x13d0300: stur            w4, [x1, #0x1b]
    // 0x13d0304: r4 = Instance_VerticalDirection
    //     0x13d0304: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13d0308: ldr             x4, [x4, #0xa20]
    // 0x13d030c: StoreField: r1->field_23 = r4
    //     0x13d030c: stur            w4, [x1, #0x23]
    // 0x13d0310: r5 = Instance_Clip
    //     0x13d0310: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13d0314: ldr             x5, [x5, #0x38]
    // 0x13d0318: StoreField: r1->field_2b = r5
    //     0x13d0318: stur            w5, [x1, #0x2b]
    // 0x13d031c: StoreField: r1->field_2f = rZR
    //     0x13d031c: stur            xzr, [x1, #0x2f]
    // 0x13d0320: ldur            x6, [fp, #-0x20]
    // 0x13d0324: StoreField: r1->field_b = r6
    //     0x13d0324: stur            w6, [x1, #0xb]
    // 0x13d0328: r0 = Container()
    //     0x13d0328: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13d032c: stur            x0, [fp, #-0x20]
    // 0x13d0330: r16 = Instance_EdgeInsets
    //     0x13d0330: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x13d0334: ldr             x16, [x16, #0xf98]
    // 0x13d0338: r30 = inf
    //     0x13d0338: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x13d033c: ldr             lr, [lr, #0x9f8]
    // 0x13d0340: stp             lr, x16, [SP, #0x10]
    // 0x13d0344: ldur            x16, [fp, #-0x18]
    // 0x13d0348: ldur            lr, [fp, #-0x10]
    // 0x13d034c: stp             lr, x16, [SP]
    // 0x13d0350: mov             x1, x0
    // 0x13d0354: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x13d0354: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x13d0358: ldr             x4, [x4, #0x18]
    // 0x13d035c: r0 = Container()
    //     0x13d035c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13d0360: r0 = Padding()
    //     0x13d0360: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13d0364: mov             x2, x0
    // 0x13d0368: r0 = Instance_EdgeInsets
    //     0x13d0368: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x13d036c: ldr             x0, [x0, #0xb0]
    // 0x13d0370: stur            x2, [fp, #-0x10]
    // 0x13d0374: StoreField: r2->field_f = r0
    //     0x13d0374: stur            w0, [x2, #0xf]
    // 0x13d0378: ldur            x1, [fp, #-0x20]
    // 0x13d037c: StoreField: r2->field_b = r1
    //     0x13d037c: stur            w1, [x2, #0xb]
    // 0x13d0380: ldur            x3, [fp, #-8]
    // 0x13d0384: LoadField: r1 = r3->field_13
    //     0x13d0384: ldur            w1, [x3, #0x13]
    // 0x13d0388: DecompressPointer r1
    //     0x13d0388: add             x1, x1, HEAP, lsl #32
    // 0x13d038c: r0 = of()
    //     0x13d038c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13d0390: LoadField: r1 = r0->field_87
    //     0x13d0390: ldur            w1, [x0, #0x87]
    // 0x13d0394: DecompressPointer r1
    //     0x13d0394: add             x1, x1, HEAP, lsl #32
    // 0x13d0398: LoadField: r0 = r1->field_7
    //     0x13d0398: ldur            w0, [x1, #7]
    // 0x13d039c: DecompressPointer r0
    //     0x13d039c: add             x0, x0, HEAP, lsl #32
    // 0x13d03a0: stur            x0, [fp, #-0x18]
    // 0x13d03a4: r1 = Instance_Color
    //     0x13d03a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13d03a8: d0 = 0.700000
    //     0x13d03a8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13d03ac: ldr             d0, [x17, #0xf48]
    // 0x13d03b0: r0 = withOpacity()
    //     0x13d03b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13d03b4: r16 = 14.000000
    //     0x13d03b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13d03b8: ldr             x16, [x16, #0x1d8]
    // 0x13d03bc: stp             x16, x0, [SP]
    // 0x13d03c0: ldur            x1, [fp, #-0x18]
    // 0x13d03c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13d03c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13d03c8: ldr             x4, [x4, #0x9b8]
    // 0x13d03cc: r0 = copyWith()
    //     0x13d03cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13d03d0: stur            x0, [fp, #-0x18]
    // 0x13d03d4: r0 = TextSpan()
    //     0x13d03d4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13d03d8: mov             x2, x0
    // 0x13d03dc: r0 = "Select Issue Detail"
    //     0x13d03dc: add             x0, PP, #0x38, lsl #12  ; [pp+0x38020] "Select Issue Detail"
    //     0x13d03e0: ldr             x0, [x0, #0x20]
    // 0x13d03e4: stur            x2, [fp, #-0x20]
    // 0x13d03e8: StoreField: r2->field_b = r0
    //     0x13d03e8: stur            w0, [x2, #0xb]
    // 0x13d03ec: r0 = Instance__DeferringMouseCursor
    //     0x13d03ec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13d03f0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13d03f0: stur            w0, [x2, #0x17]
    // 0x13d03f4: ldur            x1, [fp, #-0x18]
    // 0x13d03f8: StoreField: r2->field_7 = r1
    //     0x13d03f8: stur            w1, [x2, #7]
    // 0x13d03fc: ldur            x3, [fp, #-8]
    // 0x13d0400: LoadField: r1 = r3->field_13
    //     0x13d0400: ldur            w1, [x3, #0x13]
    // 0x13d0404: DecompressPointer r1
    //     0x13d0404: add             x1, x1, HEAP, lsl #32
    // 0x13d0408: r0 = of()
    //     0x13d0408: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13d040c: LoadField: r1 = r0->field_87
    //     0x13d040c: ldur            w1, [x0, #0x87]
    // 0x13d0410: DecompressPointer r1
    //     0x13d0410: add             x1, x1, HEAP, lsl #32
    // 0x13d0414: LoadField: r0 = r1->field_33
    //     0x13d0414: ldur            w0, [x1, #0x33]
    // 0x13d0418: DecompressPointer r0
    //     0x13d0418: add             x0, x0, HEAP, lsl #32
    // 0x13d041c: cmp             w0, NULL
    // 0x13d0420: b.ne            #0x13d042c
    // 0x13d0424: r1 = Null
    //     0x13d0424: mov             x1, NULL
    // 0x13d0428: b               #0x13d044c
    // 0x13d042c: r16 = Instance_Color
    //     0x13d042c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x13d0430: ldr             x16, [x16, #0x50]
    // 0x13d0434: str             x16, [SP]
    // 0x13d0438: mov             x1, x0
    // 0x13d043c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x13d043c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x13d0440: ldr             x4, [x4, #0xf40]
    // 0x13d0444: r0 = copyWith()
    //     0x13d0444: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13d0448: mov             x1, x0
    // 0x13d044c: ldur            x2, [fp, #-8]
    // 0x13d0450: ldur            x0, [fp, #-0x20]
    // 0x13d0454: stur            x1, [fp, #-0x18]
    // 0x13d0458: r0 = TextSpan()
    //     0x13d0458: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13d045c: mov             x3, x0
    // 0x13d0460: r0 = " *"
    //     0x13d0460: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0x13d0464: ldr             x0, [x0, #0xfc8]
    // 0x13d0468: stur            x3, [fp, #-0x28]
    // 0x13d046c: StoreField: r3->field_b = r0
    //     0x13d046c: stur            w0, [x3, #0xb]
    // 0x13d0470: r0 = Instance__DeferringMouseCursor
    //     0x13d0470: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13d0474: ArrayStore: r3[0] = r0  ; List_4
    //     0x13d0474: stur            w0, [x3, #0x17]
    // 0x13d0478: ldur            x1, [fp, #-0x18]
    // 0x13d047c: StoreField: r3->field_7 = r1
    //     0x13d047c: stur            w1, [x3, #7]
    // 0x13d0480: r1 = Null
    //     0x13d0480: mov             x1, NULL
    // 0x13d0484: r2 = 4
    //     0x13d0484: movz            x2, #0x4
    // 0x13d0488: r0 = AllocateArray()
    //     0x13d0488: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13d048c: mov             x2, x0
    // 0x13d0490: ldur            x0, [fp, #-0x20]
    // 0x13d0494: stur            x2, [fp, #-0x18]
    // 0x13d0498: StoreField: r2->field_f = r0
    //     0x13d0498: stur            w0, [x2, #0xf]
    // 0x13d049c: ldur            x0, [fp, #-0x28]
    // 0x13d04a0: StoreField: r2->field_13 = r0
    //     0x13d04a0: stur            w0, [x2, #0x13]
    // 0x13d04a4: r1 = <InlineSpan>
    //     0x13d04a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x13d04a8: ldr             x1, [x1, #0xe40]
    // 0x13d04ac: r0 = AllocateGrowableArray()
    //     0x13d04ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13d04b0: mov             x1, x0
    // 0x13d04b4: ldur            x0, [fp, #-0x18]
    // 0x13d04b8: stur            x1, [fp, #-0x20]
    // 0x13d04bc: StoreField: r1->field_f = r0
    //     0x13d04bc: stur            w0, [x1, #0xf]
    // 0x13d04c0: r2 = 4
    //     0x13d04c0: movz            x2, #0x4
    // 0x13d04c4: StoreField: r1->field_b = r2
    //     0x13d04c4: stur            w2, [x1, #0xb]
    // 0x13d04c8: r0 = TextSpan()
    //     0x13d04c8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13d04cc: mov             x1, x0
    // 0x13d04d0: ldur            x0, [fp, #-0x20]
    // 0x13d04d4: stur            x1, [fp, #-0x18]
    // 0x13d04d8: StoreField: r1->field_f = r0
    //     0x13d04d8: stur            w0, [x1, #0xf]
    // 0x13d04dc: r0 = Instance__DeferringMouseCursor
    //     0x13d04dc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13d04e0: ArrayStore: r1[0] = r0  ; List_4
    //     0x13d04e0: stur            w0, [x1, #0x17]
    // 0x13d04e4: r0 = RichText()
    //     0x13d04e4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x13d04e8: mov             x1, x0
    // 0x13d04ec: ldur            x2, [fp, #-0x18]
    // 0x13d04f0: stur            x0, [fp, #-0x18]
    // 0x13d04f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13d04f4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13d04f8: r0 = RichText()
    //     0x13d04f8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x13d04fc: r1 = Instance_Color
    //     0x13d04fc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13d0500: d0 = 0.100000
    //     0x13d0500: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13d0504: r0 = withOpacity()
    //     0x13d0504: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13d0508: mov             x2, x0
    // 0x13d050c: r1 = Null
    //     0x13d050c: mov             x1, NULL
    // 0x13d0510: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13d0510: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13d0514: r0 = Border.all()
    //     0x13d0514: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13d0518: stur            x0, [fp, #-0x20]
    // 0x13d051c: r0 = BoxDecoration()
    //     0x13d051c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13d0520: mov             x2, x0
    // 0x13d0524: ldur            x0, [fp, #-0x20]
    // 0x13d0528: stur            x2, [fp, #-0x28]
    // 0x13d052c: StoreField: r2->field_f = r0
    //     0x13d052c: stur            w0, [x2, #0xf]
    // 0x13d0530: r0 = Instance_BoxShape
    //     0x13d0530: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13d0534: ldr             x0, [x0, #0x80]
    // 0x13d0538: StoreField: r2->field_23 = r0
    //     0x13d0538: stur            w0, [x2, #0x23]
    // 0x13d053c: ldur            x0, [fp, #-8]
    // 0x13d0540: LoadField: r1 = r0->field_f
    //     0x13d0540: ldur            w1, [x0, #0xf]
    // 0x13d0544: DecompressPointer r1
    //     0x13d0544: add             x1, x1, HEAP, lsl #32
    // 0x13d0548: r0 = controller()
    //     0x13d0548: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13d054c: LoadField: r1 = r0->field_4f
    //     0x13d054c: ldur            w1, [x0, #0x4f]
    // 0x13d0550: DecompressPointer r1
    //     0x13d0550: add             x1, x1, HEAP, lsl #32
    // 0x13d0554: r0 = value()
    //     0x13d0554: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13d0558: LoadField: r1 = r0->field_b
    //     0x13d0558: ldur            w1, [x0, #0xb]
    // 0x13d055c: DecompressPointer r1
    //     0x13d055c: add             x1, x1, HEAP, lsl #32
    // 0x13d0560: cmp             w1, NULL
    // 0x13d0564: b.ne            #0x13d0570
    // 0x13d0568: r0 = Null
    //     0x13d0568: mov             x0, NULL
    // 0x13d056c: b               #0x13d0580
    // 0x13d0570: LoadField: r0 = r1->field_f
    //     0x13d0570: ldur            w0, [x1, #0xf]
    // 0x13d0574: DecompressPointer r0
    //     0x13d0574: add             x0, x0, HEAP, lsl #32
    // 0x13d0578: LoadField: r1 = r0->field_b
    //     0x13d0578: ldur            w1, [x0, #0xb]
    // 0x13d057c: mov             x0, x1
    // 0x13d0580: cmp             w0, NULL
    // 0x13d0584: b.ne            #0x13d0590
    // 0x13d0588: r1 = 0
    //     0x13d0588: movz            x1, #0
    // 0x13d058c: b               #0x13d0594
    // 0x13d0590: r1 = LoadInt32Instr(r0)
    //     0x13d0590: sbfx            x1, x0, #1, #0x1f
    // 0x13d0594: ldur            x0, [fp, #-8]
    // 0x13d0598: ldur            x3, [fp, #-0x18]
    // 0x13d059c: lsl             x4, x1, #1
    // 0x13d05a0: mov             x2, x0
    // 0x13d05a4: stur            x4, [fp, #-0x20]
    // 0x13d05a8: r1 = Function '<anonymous closure>':.
    //     0x13d05a8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38028] AnonymousClosure: (0x13cfcc8), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x1505aa4)
    //     0x13d05ac: ldr             x1, [x1, #0x28]
    // 0x13d05b0: r0 = AllocateClosure()
    //     0x13d05b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13d05b4: stur            x0, [fp, #-0x38]
    // 0x13d05b8: r0 = ListView()
    //     0x13d05b8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x13d05bc: stur            x0, [fp, #-0x40]
    // 0x13d05c0: r16 = true
    //     0x13d05c0: add             x16, NULL, #0x20  ; true
    // 0x13d05c4: r30 = Instance_NeverScrollableScrollPhysics
    //     0x13d05c4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x13d05c8: ldr             lr, [lr, #0x1c8]
    // 0x13d05cc: stp             lr, x16, [SP]
    // 0x13d05d0: mov             x1, x0
    // 0x13d05d4: ldur            x2, [fp, #-0x38]
    // 0x13d05d8: ldur            x3, [fp, #-0x20]
    // 0x13d05dc: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x13d05dc: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x13d05e0: ldr             x4, [x4, #8]
    // 0x13d05e4: r0 = ListView.builder()
    //     0x13d05e4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x13d05e8: r0 = Container()
    //     0x13d05e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13d05ec: stur            x0, [fp, #-0x20]
    // 0x13d05f0: ldur            x16, [fp, #-0x28]
    // 0x13d05f4: ldur            lr, [fp, #-0x40]
    // 0x13d05f8: stp             lr, x16, [SP]
    // 0x13d05fc: mov             x1, x0
    // 0x13d0600: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13d0600: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13d0604: ldr             x4, [x4, #0x88]
    // 0x13d0608: r0 = Container()
    //     0x13d0608: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13d060c: r0 = Padding()
    //     0x13d060c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13d0610: mov             x3, x0
    // 0x13d0614: r0 = Instance_EdgeInsets
    //     0x13d0614: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x13d0618: ldr             x0, [x0, #0xa00]
    // 0x13d061c: stur            x3, [fp, #-0x28]
    // 0x13d0620: StoreField: r3->field_f = r0
    //     0x13d0620: stur            w0, [x3, #0xf]
    // 0x13d0624: ldur            x0, [fp, #-0x20]
    // 0x13d0628: StoreField: r3->field_b = r0
    //     0x13d0628: stur            w0, [x3, #0xb]
    // 0x13d062c: r1 = Null
    //     0x13d062c: mov             x1, NULL
    // 0x13d0630: r2 = 4
    //     0x13d0630: movz            x2, #0x4
    // 0x13d0634: r0 = AllocateArray()
    //     0x13d0634: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13d0638: mov             x2, x0
    // 0x13d063c: ldur            x0, [fp, #-0x18]
    // 0x13d0640: stur            x2, [fp, #-0x20]
    // 0x13d0644: StoreField: r2->field_f = r0
    //     0x13d0644: stur            w0, [x2, #0xf]
    // 0x13d0648: ldur            x0, [fp, #-0x28]
    // 0x13d064c: StoreField: r2->field_13 = r0
    //     0x13d064c: stur            w0, [x2, #0x13]
    // 0x13d0650: r1 = <Widget>
    //     0x13d0650: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13d0654: r0 = AllocateGrowableArray()
    //     0x13d0654: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13d0658: mov             x1, x0
    // 0x13d065c: ldur            x0, [fp, #-0x20]
    // 0x13d0660: stur            x1, [fp, #-0x18]
    // 0x13d0664: StoreField: r1->field_f = r0
    //     0x13d0664: stur            w0, [x1, #0xf]
    // 0x13d0668: r2 = 4
    //     0x13d0668: movz            x2, #0x4
    // 0x13d066c: StoreField: r1->field_b = r2
    //     0x13d066c: stur            w2, [x1, #0xb]
    // 0x13d0670: r0 = Column()
    //     0x13d0670: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13d0674: mov             x1, x0
    // 0x13d0678: r0 = Instance_Axis
    //     0x13d0678: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13d067c: stur            x1, [fp, #-0x20]
    // 0x13d0680: StoreField: r1->field_f = r0
    //     0x13d0680: stur            w0, [x1, #0xf]
    // 0x13d0684: r2 = Instance_MainAxisAlignment
    //     0x13d0684: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13d0688: ldr             x2, [x2, #0xa08]
    // 0x13d068c: StoreField: r1->field_13 = r2
    //     0x13d068c: stur            w2, [x1, #0x13]
    // 0x13d0690: r3 = Instance_MainAxisSize
    //     0x13d0690: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13d0694: ldr             x3, [x3, #0xa10]
    // 0x13d0698: ArrayStore: r1[0] = r3  ; List_4
    //     0x13d0698: stur            w3, [x1, #0x17]
    // 0x13d069c: r4 = Instance_CrossAxisAlignment
    //     0x13d069c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13d06a0: ldr             x4, [x4, #0x890]
    // 0x13d06a4: StoreField: r1->field_1b = r4
    //     0x13d06a4: stur            w4, [x1, #0x1b]
    // 0x13d06a8: r5 = Instance_VerticalDirection
    //     0x13d06a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13d06ac: ldr             x5, [x5, #0xa20]
    // 0x13d06b0: StoreField: r1->field_23 = r5
    //     0x13d06b0: stur            w5, [x1, #0x23]
    // 0x13d06b4: r6 = Instance_Clip
    //     0x13d06b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13d06b8: ldr             x6, [x6, #0x38]
    // 0x13d06bc: StoreField: r1->field_2b = r6
    //     0x13d06bc: stur            w6, [x1, #0x2b]
    // 0x13d06c0: StoreField: r1->field_2f = rZR
    //     0x13d06c0: stur            xzr, [x1, #0x2f]
    // 0x13d06c4: ldur            x7, [fp, #-0x18]
    // 0x13d06c8: StoreField: r1->field_b = r7
    //     0x13d06c8: stur            w7, [x1, #0xb]
    // 0x13d06cc: r0 = Padding()
    //     0x13d06cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13d06d0: mov             x2, x0
    // 0x13d06d4: r0 = Instance_EdgeInsets
    //     0x13d06d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x13d06d8: ldr             x0, [x0, #0xb0]
    // 0x13d06dc: stur            x2, [fp, #-0x18]
    // 0x13d06e0: StoreField: r2->field_f = r0
    //     0x13d06e0: stur            w0, [x2, #0xf]
    // 0x13d06e4: ldur            x0, [fp, #-0x20]
    // 0x13d06e8: StoreField: r2->field_b = r0
    //     0x13d06e8: stur            w0, [x2, #0xb]
    // 0x13d06ec: ldur            x0, [fp, #-8]
    // 0x13d06f0: LoadField: r1 = r0->field_13
    //     0x13d06f0: ldur            w1, [x0, #0x13]
    // 0x13d06f4: DecompressPointer r1
    //     0x13d06f4: add             x1, x1, HEAP, lsl #32
    // 0x13d06f8: r0 = of()
    //     0x13d06f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13d06fc: LoadField: r1 = r0->field_87
    //     0x13d06fc: ldur            w1, [x0, #0x87]
    // 0x13d0700: DecompressPointer r1
    //     0x13d0700: add             x1, x1, HEAP, lsl #32
    // 0x13d0704: LoadField: r0 = r1->field_7
    //     0x13d0704: ldur            w0, [x1, #7]
    // 0x13d0708: DecompressPointer r0
    //     0x13d0708: add             x0, x0, HEAP, lsl #32
    // 0x13d070c: r16 = Instance_Color
    //     0x13d070c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x13d0710: ldr             x16, [x16, #0x50]
    // 0x13d0714: r30 = 14.000000
    //     0x13d0714: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13d0718: ldr             lr, [lr, #0x1d8]
    // 0x13d071c: stp             lr, x16, [SP]
    // 0x13d0720: mov             x1, x0
    // 0x13d0724: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13d0724: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13d0728: ldr             x4, [x4, #0x9b8]
    // 0x13d072c: r0 = copyWith()
    //     0x13d072c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13d0730: stur            x0, [fp, #-0x20]
    // 0x13d0734: r0 = Text()
    //     0x13d0734: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13d0738: mov             x2, x0
    // 0x13d073c: r0 = "Please Note:"
    //     0x13d073c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38030] "Please Note:"
    //     0x13d0740: ldr             x0, [x0, #0x30]
    // 0x13d0744: stur            x2, [fp, #-0x28]
    // 0x13d0748: StoreField: r2->field_b = r0
    //     0x13d0748: stur            w0, [x2, #0xb]
    // 0x13d074c: ldur            x0, [fp, #-0x20]
    // 0x13d0750: StoreField: r2->field_13 = r0
    //     0x13d0750: stur            w0, [x2, #0x13]
    // 0x13d0754: ldur            x0, [fp, #-8]
    // 0x13d0758: LoadField: r1 = r0->field_f
    //     0x13d0758: ldur            w1, [x0, #0xf]
    // 0x13d075c: DecompressPointer r1
    //     0x13d075c: add             x1, x1, HEAP, lsl #32
    // 0x13d0760: r0 = controller()
    //     0x13d0760: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13d0764: LoadField: r1 = r0->field_4f
    //     0x13d0764: ldur            w1, [x0, #0x4f]
    // 0x13d0768: DecompressPointer r1
    //     0x13d0768: add             x1, x1, HEAP, lsl #32
    // 0x13d076c: r0 = value()
    //     0x13d076c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13d0770: LoadField: r1 = r0->field_b
    //     0x13d0770: ldur            w1, [x0, #0xb]
    // 0x13d0774: DecompressPointer r1
    //     0x13d0774: add             x1, x1, HEAP, lsl #32
    // 0x13d0778: cmp             w1, NULL
    // 0x13d077c: b.ne            #0x13d0788
    // 0x13d0780: r0 = Null
    //     0x13d0780: mov             x0, NULL
    // 0x13d0784: b               #0x13d0790
    // 0x13d0788: LoadField: r0 = r1->field_b
    //     0x13d0788: ldur            w0, [x1, #0xb]
    // 0x13d078c: DecompressPointer r0
    //     0x13d078c: add             x0, x0, HEAP, lsl #32
    // 0x13d0790: cmp             w0, NULL
    // 0x13d0794: b.ne            #0x13d07a0
    // 0x13d0798: r4 = ""
    //     0x13d0798: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13d079c: b               #0x13d07a4
    // 0x13d07a0: mov             x4, x0
    // 0x13d07a4: ldur            x1, [fp, #-8]
    // 0x13d07a8: ldur            x3, [fp, #-0x10]
    // 0x13d07ac: ldur            x2, [fp, #-0x18]
    // 0x13d07b0: ldur            x0, [fp, #-0x28]
    // 0x13d07b4: stur            x4, [fp, #-0x20]
    // 0x13d07b8: LoadField: r5 = r1->field_13
    //     0x13d07b8: ldur            w5, [x1, #0x13]
    // 0x13d07bc: DecompressPointer r5
    //     0x13d07bc: add             x5, x5, HEAP, lsl #32
    // 0x13d07c0: mov             x1, x5
    // 0x13d07c4: r0 = of()
    //     0x13d07c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13d07c8: LoadField: r1 = r0->field_87
    //     0x13d07c8: ldur            w1, [x0, #0x87]
    // 0x13d07cc: DecompressPointer r1
    //     0x13d07cc: add             x1, x1, HEAP, lsl #32
    // 0x13d07d0: LoadField: r0 = r1->field_2b
    //     0x13d07d0: ldur            w0, [x1, #0x2b]
    // 0x13d07d4: DecompressPointer r0
    //     0x13d07d4: add             x0, x0, HEAP, lsl #32
    // 0x13d07d8: stur            x0, [fp, #-8]
    // 0x13d07dc: r1 = Instance_Color
    //     0x13d07dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13d07e0: d0 = 0.700000
    //     0x13d07e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13d07e4: ldr             d0, [x17, #0xf48]
    // 0x13d07e8: r0 = withOpacity()
    //     0x13d07e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13d07ec: r16 = 12.000000
    //     0x13d07ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13d07f0: ldr             x16, [x16, #0x9e8]
    // 0x13d07f4: stp             x16, x0, [SP]
    // 0x13d07f8: ldur            x1, [fp, #-8]
    // 0x13d07fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13d07fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13d0800: ldr             x4, [x4, #0x9b8]
    // 0x13d0804: r0 = copyWith()
    //     0x13d0804: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13d0808: stur            x0, [fp, #-8]
    // 0x13d080c: r0 = Text()
    //     0x13d080c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13d0810: mov             x3, x0
    // 0x13d0814: ldur            x0, [fp, #-0x20]
    // 0x13d0818: stur            x3, [fp, #-0x38]
    // 0x13d081c: StoreField: r3->field_b = r0
    //     0x13d081c: stur            w0, [x3, #0xb]
    // 0x13d0820: ldur            x0, [fp, #-8]
    // 0x13d0824: StoreField: r3->field_13 = r0
    //     0x13d0824: stur            w0, [x3, #0x13]
    // 0x13d0828: r1 = Null
    //     0x13d0828: mov             x1, NULL
    // 0x13d082c: r2 = 4
    //     0x13d082c: movz            x2, #0x4
    // 0x13d0830: r0 = AllocateArray()
    //     0x13d0830: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13d0834: mov             x2, x0
    // 0x13d0838: ldur            x0, [fp, #-0x28]
    // 0x13d083c: stur            x2, [fp, #-8]
    // 0x13d0840: StoreField: r2->field_f = r0
    //     0x13d0840: stur            w0, [x2, #0xf]
    // 0x13d0844: ldur            x0, [fp, #-0x38]
    // 0x13d0848: StoreField: r2->field_13 = r0
    //     0x13d0848: stur            w0, [x2, #0x13]
    // 0x13d084c: r1 = <Widget>
    //     0x13d084c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13d0850: r0 = AllocateGrowableArray()
    //     0x13d0850: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13d0854: mov             x1, x0
    // 0x13d0858: ldur            x0, [fp, #-8]
    // 0x13d085c: stur            x1, [fp, #-0x20]
    // 0x13d0860: StoreField: r1->field_f = r0
    //     0x13d0860: stur            w0, [x1, #0xf]
    // 0x13d0864: r0 = 4
    //     0x13d0864: movz            x0, #0x4
    // 0x13d0868: StoreField: r1->field_b = r0
    //     0x13d0868: stur            w0, [x1, #0xb]
    // 0x13d086c: r0 = Column()
    //     0x13d086c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13d0870: mov             x3, x0
    // 0x13d0874: r0 = Instance_Axis
    //     0x13d0874: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13d0878: stur            x3, [fp, #-8]
    // 0x13d087c: StoreField: r3->field_f = r0
    //     0x13d087c: stur            w0, [x3, #0xf]
    // 0x13d0880: r4 = Instance_MainAxisAlignment
    //     0x13d0880: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13d0884: ldr             x4, [x4, #0xa08]
    // 0x13d0888: StoreField: r3->field_13 = r4
    //     0x13d0888: stur            w4, [x3, #0x13]
    // 0x13d088c: r5 = Instance_MainAxisSize
    //     0x13d088c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13d0890: ldr             x5, [x5, #0xa10]
    // 0x13d0894: ArrayStore: r3[0] = r5  ; List_4
    //     0x13d0894: stur            w5, [x3, #0x17]
    // 0x13d0898: r6 = Instance_CrossAxisAlignment
    //     0x13d0898: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13d089c: ldr             x6, [x6, #0x890]
    // 0x13d08a0: StoreField: r3->field_1b = r6
    //     0x13d08a0: stur            w6, [x3, #0x1b]
    // 0x13d08a4: r7 = Instance_VerticalDirection
    //     0x13d08a4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13d08a8: ldr             x7, [x7, #0xa20]
    // 0x13d08ac: StoreField: r3->field_23 = r7
    //     0x13d08ac: stur            w7, [x3, #0x23]
    // 0x13d08b0: r8 = Instance_Clip
    //     0x13d08b0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13d08b4: ldr             x8, [x8, #0x38]
    // 0x13d08b8: StoreField: r3->field_2b = r8
    //     0x13d08b8: stur            w8, [x3, #0x2b]
    // 0x13d08bc: StoreField: r3->field_2f = rZR
    //     0x13d08bc: stur            xzr, [x3, #0x2f]
    // 0x13d08c0: ldur            x1, [fp, #-0x20]
    // 0x13d08c4: StoreField: r3->field_b = r1
    //     0x13d08c4: stur            w1, [x3, #0xb]
    // 0x13d08c8: r1 = Null
    //     0x13d08c8: mov             x1, NULL
    // 0x13d08cc: r2 = 6
    //     0x13d08cc: movz            x2, #0x6
    // 0x13d08d0: r0 = AllocateArray()
    //     0x13d08d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13d08d4: mov             x2, x0
    // 0x13d08d8: ldur            x0, [fp, #-0x10]
    // 0x13d08dc: stur            x2, [fp, #-0x20]
    // 0x13d08e0: StoreField: r2->field_f = r0
    //     0x13d08e0: stur            w0, [x2, #0xf]
    // 0x13d08e4: ldur            x0, [fp, #-0x18]
    // 0x13d08e8: StoreField: r2->field_13 = r0
    //     0x13d08e8: stur            w0, [x2, #0x13]
    // 0x13d08ec: ldur            x0, [fp, #-8]
    // 0x13d08f0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13d08f0: stur            w0, [x2, #0x17]
    // 0x13d08f4: r1 = <Widget>
    //     0x13d08f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13d08f8: r0 = AllocateGrowableArray()
    //     0x13d08f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13d08fc: mov             x1, x0
    // 0x13d0900: ldur            x0, [fp, #-0x20]
    // 0x13d0904: stur            x1, [fp, #-8]
    // 0x13d0908: StoreField: r1->field_f = r0
    //     0x13d0908: stur            w0, [x1, #0xf]
    // 0x13d090c: r0 = 6
    //     0x13d090c: movz            x0, #0x6
    // 0x13d0910: StoreField: r1->field_b = r0
    //     0x13d0910: stur            w0, [x1, #0xb]
    // 0x13d0914: r0 = Column()
    //     0x13d0914: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13d0918: mov             x1, x0
    // 0x13d091c: r0 = Instance_Axis
    //     0x13d091c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13d0920: stur            x1, [fp, #-0x10]
    // 0x13d0924: StoreField: r1->field_f = r0
    //     0x13d0924: stur            w0, [x1, #0xf]
    // 0x13d0928: r2 = Instance_MainAxisAlignment
    //     0x13d0928: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13d092c: ldr             x2, [x2, #0xa08]
    // 0x13d0930: StoreField: r1->field_13 = r2
    //     0x13d0930: stur            w2, [x1, #0x13]
    // 0x13d0934: r2 = Instance_MainAxisSize
    //     0x13d0934: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13d0938: ldr             x2, [x2, #0xa10]
    // 0x13d093c: ArrayStore: r1[0] = r2  ; List_4
    //     0x13d093c: stur            w2, [x1, #0x17]
    // 0x13d0940: r2 = Instance_CrossAxisAlignment
    //     0x13d0940: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13d0944: ldr             x2, [x2, #0x890]
    // 0x13d0948: StoreField: r1->field_1b = r2
    //     0x13d0948: stur            w2, [x1, #0x1b]
    // 0x13d094c: r2 = Instance_VerticalDirection
    //     0x13d094c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13d0950: ldr             x2, [x2, #0xa20]
    // 0x13d0954: StoreField: r1->field_23 = r2
    //     0x13d0954: stur            w2, [x1, #0x23]
    // 0x13d0958: r2 = Instance_Clip
    //     0x13d0958: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13d095c: ldr             x2, [x2, #0x38]
    // 0x13d0960: StoreField: r1->field_2b = r2
    //     0x13d0960: stur            w2, [x1, #0x2b]
    // 0x13d0964: StoreField: r1->field_2f = rZR
    //     0x13d0964: stur            xzr, [x1, #0x2f]
    // 0x13d0968: ldur            x2, [fp, #-8]
    // 0x13d096c: StoreField: r1->field_b = r2
    //     0x13d096c: stur            w2, [x1, #0xb]
    // 0x13d0970: r0 = Padding()
    //     0x13d0970: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13d0974: mov             x1, x0
    // 0x13d0978: r0 = Instance_EdgeInsets
    //     0x13d0978: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13d097c: ldr             x0, [x0, #0x1f0]
    // 0x13d0980: stur            x1, [fp, #-8]
    // 0x13d0984: StoreField: r1->field_f = r0
    //     0x13d0984: stur            w0, [x1, #0xf]
    // 0x13d0988: ldur            x0, [fp, #-0x10]
    // 0x13d098c: StoreField: r1->field_b = r0
    //     0x13d098c: stur            w0, [x1, #0xb]
    // 0x13d0990: r0 = SingleChildScrollView()
    //     0x13d0990: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x13d0994: r1 = Instance_Axis
    //     0x13d0994: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13d0998: StoreField: r0->field_b = r1
    //     0x13d0998: stur            w1, [x0, #0xb]
    // 0x13d099c: r1 = false
    //     0x13d099c: add             x1, NULL, #0x30  ; false
    // 0x13d09a0: StoreField: r0->field_f = r1
    //     0x13d09a0: stur            w1, [x0, #0xf]
    // 0x13d09a4: ldur            x1, [fp, #-8]
    // 0x13d09a8: StoreField: r0->field_23 = r1
    //     0x13d09a8: stur            w1, [x0, #0x23]
    // 0x13d09ac: r1 = Instance_DragStartBehavior
    //     0x13d09ac: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x13d09b0: StoreField: r0->field_27 = r1
    //     0x13d09b0: stur            w1, [x0, #0x27]
    // 0x13d09b4: r1 = Instance_Clip
    //     0x13d09b4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x13d09b8: ldr             x1, [x1, #0x7e0]
    // 0x13d09bc: StoreField: r0->field_2b = r1
    //     0x13d09bc: stur            w1, [x0, #0x2b]
    // 0x13d09c0: r1 = Instance_HitTestBehavior
    //     0x13d09c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x13d09c4: ldr             x1, [x1, #0x288]
    // 0x13d09c8: StoreField: r0->field_2f = r1
    //     0x13d09c8: stur            w1, [x0, #0x2f]
    // 0x13d09cc: LeaveFrame
    //     0x13d09cc: mov             SP, fp
    //     0x13d09d0: ldp             fp, lr, [SP], #0x10
    // 0x13d09d4: ret
    //     0x13d09d4: ret             
    // 0x13d09d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13d09d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13d09dc: b               #0x13cfe98
    // 0x13d09e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13d09e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13d09e4: b               #0x13cffd4
    // 0x13d09e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13d09e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13d09ec: SaveReg d0
    //     0x13d09ec: str             q0, [SP, #-0x10]!
    // 0x13d09f0: r0 = AllocateDouble()
    //     0x13d09f0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13d09f4: RestoreReg d0
    //     0x13d09f4: ldr             q0, [SP], #0x10
    // 0x13d09f8: b               #0x13d0230
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x13d09fc, size: 0x284
    // 0x13d09fc: EnterFrame
    //     0x13d09fc: stp             fp, lr, [SP, #-0x10]!
    //     0x13d0a00: mov             fp, SP
    // 0x13d0a04: AllocStack(0x48)
    //     0x13d0a04: sub             SP, SP, #0x48
    // 0x13d0a08: SetupParameters()
    //     0x13d0a08: ldr             x0, [fp, #0x20]
    //     0x13d0a0c: ldur            w1, [x0, #0x17]
    //     0x13d0a10: add             x1, x1, HEAP, lsl #32
    //     0x13d0a14: stur            x1, [fp, #-8]
    // 0x13d0a18: CheckStackOverflow
    //     0x13d0a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13d0a1c: cmp             SP, x16
    //     0x13d0a20: b.ls            #0x13d0c74
    // 0x13d0a24: r0 = Radius()
    //     0x13d0a24: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13d0a28: d0 = 100.000000
    //     0x13d0a28: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0x13d0a2c: stur            x0, [fp, #-0x10]
    // 0x13d0a30: StoreField: r0->field_7 = d0
    //     0x13d0a30: stur            d0, [x0, #7]
    // 0x13d0a34: StoreField: r0->field_f = d0
    //     0x13d0a34: stur            d0, [x0, #0xf]
    // 0x13d0a38: r0 = BorderRadius()
    //     0x13d0a38: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13d0a3c: mov             x2, x0
    // 0x13d0a40: ldur            x0, [fp, #-0x10]
    // 0x13d0a44: stur            x2, [fp, #-0x18]
    // 0x13d0a48: StoreField: r2->field_7 = r0
    //     0x13d0a48: stur            w0, [x2, #7]
    // 0x13d0a4c: StoreField: r2->field_b = r0
    //     0x13d0a4c: stur            w0, [x2, #0xb]
    // 0x13d0a50: StoreField: r2->field_f = r0
    //     0x13d0a50: stur            w0, [x2, #0xf]
    // 0x13d0a54: StoreField: r2->field_13 = r0
    //     0x13d0a54: stur            w0, [x2, #0x13]
    // 0x13d0a58: r1 = Instance_Color
    //     0x13d0a58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13d0a5c: d0 = 0.030000
    //     0x13d0a5c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x13d0a60: ldr             d0, [x17, #0x238]
    // 0x13d0a64: r0 = withOpacity()
    //     0x13d0a64: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13d0a68: stur            x0, [fp, #-0x10]
    // 0x13d0a6c: r0 = BoxDecoration()
    //     0x13d0a6c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13d0a70: mov             x1, x0
    // 0x13d0a74: ldur            x0, [fp, #-0x10]
    // 0x13d0a78: stur            x1, [fp, #-0x20]
    // 0x13d0a7c: StoreField: r1->field_7 = r0
    //     0x13d0a7c: stur            w0, [x1, #7]
    // 0x13d0a80: ldur            x0, [fp, #-0x18]
    // 0x13d0a84: StoreField: r1->field_13 = r0
    //     0x13d0a84: stur            w0, [x1, #0x13]
    // 0x13d0a88: r0 = Instance_BoxShape
    //     0x13d0a88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13d0a8c: ldr             x0, [x0, #0x80]
    // 0x13d0a90: StoreField: r1->field_23 = r0
    //     0x13d0a90: stur            w0, [x1, #0x23]
    // 0x13d0a94: r0 = SvgPicture()
    //     0x13d0a94: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x13d0a98: stur            x0, [fp, #-0x10]
    // 0x13d0a9c: r16 = Instance_BoxFit
    //     0x13d0a9c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x13d0aa0: ldr             x16, [x16, #0xb18]
    // 0x13d0aa4: str             x16, [SP]
    // 0x13d0aa8: mov             x1, x0
    // 0x13d0aac: r2 = "assets/images/exchange_check_icon.svg"
    //     0x13d0aac: add             x2, PP, #0x38, lsl #12  ; [pp+0x38058] "assets/images/exchange_check_icon.svg"
    //     0x13d0ab0: ldr             x2, [x2, #0x58]
    // 0x13d0ab4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0x13d0ab4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0x13d0ab8: ldr             x4, [x4, #0xb0]
    // 0x13d0abc: r0 = SvgPicture.asset()
    //     0x13d0abc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x13d0ac0: r0 = Container()
    //     0x13d0ac0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13d0ac4: stur            x0, [fp, #-0x18]
    // 0x13d0ac8: r16 = 40.000000
    //     0x13d0ac8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0x13d0acc: ldr             x16, [x16, #8]
    // 0x13d0ad0: r30 = 40.000000
    //     0x13d0ad0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0x13d0ad4: ldr             lr, [lr, #8]
    // 0x13d0ad8: stp             lr, x16, [SP, #0x18]
    // 0x13d0adc: r16 = Instance_EdgeInsets
    //     0x13d0adc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x13d0ae0: ldr             x16, [x16, #0x980]
    // 0x13d0ae4: ldur            lr, [fp, #-0x20]
    // 0x13d0ae8: stp             lr, x16, [SP, #8]
    // 0x13d0aec: ldur            x16, [fp, #-0x10]
    // 0x13d0af0: str             x16, [SP]
    // 0x13d0af4: mov             x1, x0
    // 0x13d0af8: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x2, padding, 0x3, width, 0x1, null]
    //     0x13d0af8: add             x4, PP, #0x38, lsl #12  ; [pp+0x38060] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0x13d0afc: ldr             x4, [x4, #0x60]
    // 0x13d0b00: r0 = Container()
    //     0x13d0b00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13d0b04: ldur            x0, [fp, #-8]
    // 0x13d0b08: LoadField: r1 = r0->field_f
    //     0x13d0b08: ldur            w1, [x0, #0xf]
    // 0x13d0b0c: DecompressPointer r1
    //     0x13d0b0c: add             x1, x1, HEAP, lsl #32
    // 0x13d0b10: r0 = controller()
    //     0x13d0b10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13d0b14: LoadField: r1 = r0->field_4f
    //     0x13d0b14: ldur            w1, [x0, #0x4f]
    // 0x13d0b18: DecompressPointer r1
    //     0x13d0b18: add             x1, x1, HEAP, lsl #32
    // 0x13d0b1c: r0 = value()
    //     0x13d0b1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13d0b20: LoadField: r1 = r0->field_b
    //     0x13d0b20: ldur            w1, [x0, #0xb]
    // 0x13d0b24: DecompressPointer r1
    //     0x13d0b24: add             x1, x1, HEAP, lsl #32
    // 0x13d0b28: cmp             w1, NULL
    // 0x13d0b2c: b.ne            #0x13d0b38
    // 0x13d0b30: r0 = Null
    //     0x13d0b30: mov             x0, NULL
    // 0x13d0b34: b               #0x13d0b7c
    // 0x13d0b38: ldr             x0, [fp, #0x10]
    // 0x13d0b3c: LoadField: r2 = r1->field_7
    //     0x13d0b3c: ldur            w2, [x1, #7]
    // 0x13d0b40: DecompressPointer r2
    //     0x13d0b40: add             x2, x2, HEAP, lsl #32
    // 0x13d0b44: LoadField: r1 = r2->field_b
    //     0x13d0b44: ldur            w1, [x2, #0xb]
    // 0x13d0b48: r3 = LoadInt32Instr(r0)
    //     0x13d0b48: sbfx            x3, x0, #1, #0x1f
    //     0x13d0b4c: tbz             w0, #0, #0x13d0b54
    //     0x13d0b50: ldur            x3, [x0, #7]
    // 0x13d0b54: r0 = LoadInt32Instr(r1)
    //     0x13d0b54: sbfx            x0, x1, #1, #0x1f
    // 0x13d0b58: mov             x1, x3
    // 0x13d0b5c: cmp             x1, x0
    // 0x13d0b60: b.hs            #0x13d0c7c
    // 0x13d0b64: LoadField: r0 = r2->field_f
    //     0x13d0b64: ldur            w0, [x2, #0xf]
    // 0x13d0b68: DecompressPointer r0
    //     0x13d0b68: add             x0, x0, HEAP, lsl #32
    // 0x13d0b6c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x13d0b6c: add             x16, x0, x3, lsl #2
    //     0x13d0b70: ldur            w1, [x16, #0xf]
    // 0x13d0b74: DecompressPointer r1
    //     0x13d0b74: add             x1, x1, HEAP, lsl #32
    // 0x13d0b78: mov             x0, x1
    // 0x13d0b7c: cmp             w0, NULL
    // 0x13d0b80: b.ne            #0x13d0b88
    // 0x13d0b84: r0 = ""
    //     0x13d0b84: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13d0b88: ldr             x1, [fp, #0x18]
    // 0x13d0b8c: stur            x0, [fp, #-8]
    // 0x13d0b90: r0 = of()
    //     0x13d0b90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13d0b94: LoadField: r1 = r0->field_87
    //     0x13d0b94: ldur            w1, [x0, #0x87]
    // 0x13d0b98: DecompressPointer r1
    //     0x13d0b98: add             x1, x1, HEAP, lsl #32
    // 0x13d0b9c: LoadField: r0 = r1->field_33
    //     0x13d0b9c: ldur            w0, [x1, #0x33]
    // 0x13d0ba0: DecompressPointer r0
    //     0x13d0ba0: add             x0, x0, HEAP, lsl #32
    // 0x13d0ba4: stur            x0, [fp, #-0x10]
    // 0x13d0ba8: cmp             w0, NULL
    // 0x13d0bac: b.ne            #0x13d0bb8
    // 0x13d0bb0: r2 = Null
    //     0x13d0bb0: mov             x2, NULL
    // 0x13d0bb4: b               #0x13d0be8
    // 0x13d0bb8: r1 = Instance_Color
    //     0x13d0bb8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13d0bbc: d0 = 0.700000
    //     0x13d0bbc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13d0bc0: ldr             d0, [x17, #0xf48]
    // 0x13d0bc4: r0 = withOpacity()
    //     0x13d0bc4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13d0bc8: r16 = 12.000000
    //     0x13d0bc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13d0bcc: ldr             x16, [x16, #0x9e8]
    // 0x13d0bd0: stp             x0, x16, [SP]
    // 0x13d0bd4: ldur            x1, [fp, #-0x10]
    // 0x13d0bd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13d0bd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13d0bdc: ldr             x4, [x4, #0xaa0]
    // 0x13d0be0: r0 = copyWith()
    //     0x13d0be0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13d0be4: mov             x2, x0
    // 0x13d0be8: ldur            x1, [fp, #-0x18]
    // 0x13d0bec: ldur            x0, [fp, #-8]
    // 0x13d0bf0: stur            x2, [fp, #-0x10]
    // 0x13d0bf4: r0 = HtmlWidget()
    //     0x13d0bf4: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0x13d0bf8: mov             x1, x0
    // 0x13d0bfc: ldur            x0, [fp, #-8]
    // 0x13d0c00: stur            x1, [fp, #-0x20]
    // 0x13d0c04: StoreField: r1->field_1f = r0
    //     0x13d0c04: stur            w0, [x1, #0x1f]
    // 0x13d0c08: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0x13d0c08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0x13d0c0c: ldr             x0, [x0, #0x1e0]
    // 0x13d0c10: StoreField: r1->field_23 = r0
    //     0x13d0c10: stur            w0, [x1, #0x23]
    // 0x13d0c14: r0 = Instance_ColumnMode
    //     0x13d0c14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0x13d0c18: ldr             x0, [x0, #0x1e8]
    // 0x13d0c1c: StoreField: r1->field_3b = r0
    //     0x13d0c1c: stur            w0, [x1, #0x3b]
    // 0x13d0c20: ldur            x0, [fp, #-0x10]
    // 0x13d0c24: StoreField: r1->field_3f = r0
    //     0x13d0c24: stur            w0, [x1, #0x3f]
    // 0x13d0c28: r0 = ListTile()
    //     0x13d0c28: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0x13d0c2c: ldur            x1, [fp, #-0x18]
    // 0x13d0c30: StoreField: r0->field_b = r1
    //     0x13d0c30: stur            w1, [x0, #0xb]
    // 0x13d0c34: ldur            x1, [fp, #-0x20]
    // 0x13d0c38: StoreField: r0->field_f = r1
    //     0x13d0c38: stur            w1, [x0, #0xf]
    // 0x13d0c3c: r1 = Instance_EdgeInsets
    //     0x13d0c3c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x13d0c40: StoreField: r0->field_47 = r1
    //     0x13d0c40: stur            w1, [x0, #0x47]
    // 0x13d0c44: r1 = true
    //     0x13d0c44: add             x1, NULL, #0x20  ; true
    // 0x13d0c48: StoreField: r0->field_4b = r1
    //     0x13d0c48: stur            w1, [x0, #0x4b]
    // 0x13d0c4c: r2 = false
    //     0x13d0c4c: add             x2, NULL, #0x30  ; false
    // 0x13d0c50: StoreField: r0->field_5f = r2
    //     0x13d0c50: stur            w2, [x0, #0x5f]
    // 0x13d0c54: StoreField: r0->field_73 = r2
    //     0x13d0c54: stur            w2, [x0, #0x73]
    // 0x13d0c58: r2 = 0.000000
    //     0x13d0c58: ldr             x2, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x13d0c5c: StoreField: r0->field_87 = r2
    //     0x13d0c5c: stur            w2, [x0, #0x87]
    // 0x13d0c60: StoreField: r0->field_8b = r2
    //     0x13d0c60: stur            w2, [x0, #0x8b]
    // 0x13d0c64: StoreField: r0->field_97 = r1
    //     0x13d0c64: stur            w1, [x0, #0x97]
    // 0x13d0c68: LeaveFrame
    //     0x13d0c68: mov             SP, fp
    //     0x13d0c6c: ldp             fp, lr, [SP], #0x10
    // 0x13d0c70: ret
    //     0x13d0c70: ret             
    // 0x13d0c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13d0c74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13d0c78: b               #0x13d0a24
    // 0x13d0c7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13d0c7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ body(/* No info */) {
    // ** addr: 0x1505aa4, size: 0x64
    // 0x1505aa4: EnterFrame
    //     0x1505aa4: stp             fp, lr, [SP, #-0x10]!
    //     0x1505aa8: mov             fp, SP
    // 0x1505aac: AllocStack(0x18)
    //     0x1505aac: sub             SP, SP, #0x18
    // 0x1505ab0: SetupParameters(ExchangeReturnIntermediateScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1505ab0: stur            x1, [fp, #-8]
    //     0x1505ab4: stur            x2, [fp, #-0x10]
    // 0x1505ab8: r1 = 2
    //     0x1505ab8: movz            x1, #0x2
    // 0x1505abc: r0 = AllocateContext()
    //     0x1505abc: bl              #0x16f6108  ; AllocateContextStub
    // 0x1505ac0: mov             x1, x0
    // 0x1505ac4: ldur            x0, [fp, #-8]
    // 0x1505ac8: stur            x1, [fp, #-0x18]
    // 0x1505acc: StoreField: r1->field_f = r0
    //     0x1505acc: stur            w0, [x1, #0xf]
    // 0x1505ad0: ldur            x0, [fp, #-0x10]
    // 0x1505ad4: StoreField: r1->field_13 = r0
    //     0x1505ad4: stur            w0, [x1, #0x13]
    // 0x1505ad8: r0 = Obx()
    //     0x1505ad8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1505adc: ldur            x2, [fp, #-0x18]
    // 0x1505ae0: r1 = Function '<anonymous closure>':.
    //     0x1505ae0: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fe8] AnonymousClosure: (0x13cfe70), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::body (0x1505aa4)
    //     0x1505ae4: ldr             x1, [x1, #0xfe8]
    // 0x1505ae8: stur            x0, [fp, #-8]
    // 0x1505aec: r0 = AllocateClosure()
    //     0x1505aec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505af0: mov             x1, x0
    // 0x1505af4: ldur            x0, [fp, #-8]
    // 0x1505af8: StoreField: r0->field_b = r1
    //     0x1505af8: stur            w1, [x0, #0xb]
    // 0x1505afc: LeaveFrame
    //     0x1505afc: mov             SP, fp
    //     0x1505b00: ldp             fp, lr, [SP], #0x10
    // 0x1505b04: ret
    //     0x1505b04: ret             
  }
  [closure] Text <anonymous closure>(dynamic) {
    // ** addr: 0x15d0660, size: 0xe4
    // 0x15d0660: EnterFrame
    //     0x15d0660: stp             fp, lr, [SP, #-0x10]!
    //     0x15d0664: mov             fp, SP
    // 0x15d0668: AllocStack(0x20)
    //     0x15d0668: sub             SP, SP, #0x20
    // 0x15d066c: SetupParameters()
    //     0x15d066c: ldr             x0, [fp, #0x10]
    //     0x15d0670: ldur            w2, [x0, #0x17]
    //     0x15d0674: add             x2, x2, HEAP, lsl #32
    //     0x15d0678: stur            x2, [fp, #-8]
    // 0x15d067c: CheckStackOverflow
    //     0x15d067c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d0680: cmp             SP, x16
    //     0x15d0684: b.ls            #0x15d073c
    // 0x15d0688: LoadField: r1 = r2->field_f
    //     0x15d0688: ldur            w1, [x2, #0xf]
    // 0x15d068c: DecompressPointer r1
    //     0x15d068c: add             x1, x1, HEAP, lsl #32
    // 0x15d0690: r0 = controller()
    //     0x15d0690: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d0694: LoadField: r1 = r0->field_4f
    //     0x15d0694: ldur            w1, [x0, #0x4f]
    // 0x15d0698: DecompressPointer r1
    //     0x15d0698: add             x1, x1, HEAP, lsl #32
    // 0x15d069c: r0 = value()
    //     0x15d069c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d06a0: LoadField: r1 = r0->field_b
    //     0x15d06a0: ldur            w1, [x0, #0xb]
    // 0x15d06a4: DecompressPointer r1
    //     0x15d06a4: add             x1, x1, HEAP, lsl #32
    // 0x15d06a8: cmp             w1, NULL
    // 0x15d06ac: b.ne            #0x15d06b8
    // 0x15d06b0: r0 = Null
    //     0x15d06b0: mov             x0, NULL
    // 0x15d06b4: b               #0x15d06c0
    // 0x15d06b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x15d06b8: ldur            w0, [x1, #0x17]
    // 0x15d06bc: DecompressPointer r0
    //     0x15d06bc: add             x0, x0, HEAP, lsl #32
    // 0x15d06c0: cmp             w0, NULL
    // 0x15d06c4: b.ne            #0x15d06d0
    // 0x15d06c8: r2 = ""
    //     0x15d06c8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d06cc: b               #0x15d06d4
    // 0x15d06d0: mov             x2, x0
    // 0x15d06d4: ldur            x0, [fp, #-8]
    // 0x15d06d8: stur            x2, [fp, #-0x10]
    // 0x15d06dc: LoadField: r1 = r0->field_13
    //     0x15d06dc: ldur            w1, [x0, #0x13]
    // 0x15d06e0: DecompressPointer r1
    //     0x15d06e0: add             x1, x1, HEAP, lsl #32
    // 0x15d06e4: r0 = of()
    //     0x15d06e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d06e8: LoadField: r1 = r0->field_87
    //     0x15d06e8: ldur            w1, [x0, #0x87]
    // 0x15d06ec: DecompressPointer r1
    //     0x15d06ec: add             x1, x1, HEAP, lsl #32
    // 0x15d06f0: LoadField: r0 = r1->field_7
    //     0x15d06f0: ldur            w0, [x1, #7]
    // 0x15d06f4: DecompressPointer r0
    //     0x15d06f4: add             x0, x0, HEAP, lsl #32
    // 0x15d06f8: r16 = Instance_Color
    //     0x15d06f8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15d06fc: r30 = 16.000000
    //     0x15d06fc: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15d0700: ldr             lr, [lr, #0x188]
    // 0x15d0704: stp             lr, x16, [SP]
    // 0x15d0708: mov             x1, x0
    // 0x15d070c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15d070c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15d0710: ldr             x4, [x4, #0x9b8]
    // 0x15d0714: r0 = copyWith()
    //     0x15d0714: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d0718: stur            x0, [fp, #-8]
    // 0x15d071c: r0 = Text()
    //     0x15d071c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d0720: ldur            x1, [fp, #-0x10]
    // 0x15d0724: StoreField: r0->field_b = r1
    //     0x15d0724: stur            w1, [x0, #0xb]
    // 0x15d0728: ldur            x1, [fp, #-8]
    // 0x15d072c: StoreField: r0->field_13 = r1
    //     0x15d072c: stur            w1, [x0, #0x13]
    // 0x15d0730: LeaveFrame
    //     0x15d0730: mov             SP, fp
    //     0x15d0734: ldp             fp, lr, [SP], #0x10
    // 0x15d0738: ret
    //     0x15d0738: ret             
    // 0x15d073c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d073c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d0740: b               #0x15d0688
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15ea09c, size: 0x18c
    // 0x15ea09c: EnterFrame
    //     0x15ea09c: stp             fp, lr, [SP, #-0x10]!
    //     0x15ea0a0: mov             fp, SP
    // 0x15ea0a4: AllocStack(0x28)
    //     0x15ea0a4: sub             SP, SP, #0x28
    // 0x15ea0a8: SetupParameters(ExchangeReturnIntermediateScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15ea0a8: mov             x0, x1
    //     0x15ea0ac: stur            x1, [fp, #-8]
    //     0x15ea0b0: mov             x1, x2
    //     0x15ea0b4: stur            x2, [fp, #-0x10]
    // 0x15ea0b8: CheckStackOverflow
    //     0x15ea0b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ea0bc: cmp             SP, x16
    //     0x15ea0c0: b.ls            #0x15ea220
    // 0x15ea0c4: r1 = 2
    //     0x15ea0c4: movz            x1, #0x2
    // 0x15ea0c8: r0 = AllocateContext()
    //     0x15ea0c8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15ea0cc: mov             x1, x0
    // 0x15ea0d0: ldur            x0, [fp, #-8]
    // 0x15ea0d4: stur            x1, [fp, #-0x18]
    // 0x15ea0d8: StoreField: r1->field_f = r0
    //     0x15ea0d8: stur            w0, [x1, #0xf]
    // 0x15ea0dc: ldur            x0, [fp, #-0x10]
    // 0x15ea0e0: StoreField: r1->field_13 = r0
    //     0x15ea0e0: stur            w0, [x1, #0x13]
    // 0x15ea0e4: r0 = Obx()
    //     0x15ea0e4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15ea0e8: ldur            x2, [fp, #-0x18]
    // 0x15ea0ec: r1 = Function '<anonymous closure>':.
    //     0x15ea0ec: add             x1, PP, #0x38, lsl #12  ; [pp+0x38068] AnonymousClosure: (0x15d0660), in [package:customer_app/app/presentation/views/line/exchange/exchange_return_intermediate_screen.dart] ExchangeReturnIntermediateScreen::appBar (0x15ea09c)
    //     0x15ea0f0: ldr             x1, [x1, #0x68]
    // 0x15ea0f4: stur            x0, [fp, #-8]
    // 0x15ea0f8: r0 = AllocateClosure()
    //     0x15ea0f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ea0fc: mov             x1, x0
    // 0x15ea100: ldur            x0, [fp, #-8]
    // 0x15ea104: StoreField: r0->field_b = r1
    //     0x15ea104: stur            w1, [x0, #0xb]
    // 0x15ea108: ldur            x1, [fp, #-0x10]
    // 0x15ea10c: r0 = of()
    //     0x15ea10c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ea110: LoadField: r1 = r0->field_5b
    //     0x15ea110: ldur            w1, [x0, #0x5b]
    // 0x15ea114: DecompressPointer r1
    //     0x15ea114: add             x1, x1, HEAP, lsl #32
    // 0x15ea118: stur            x1, [fp, #-0x10]
    // 0x15ea11c: r0 = ColorFilter()
    //     0x15ea11c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ea120: mov             x1, x0
    // 0x15ea124: ldur            x0, [fp, #-0x10]
    // 0x15ea128: stur            x1, [fp, #-0x20]
    // 0x15ea12c: StoreField: r1->field_7 = r0
    //     0x15ea12c: stur            w0, [x1, #7]
    // 0x15ea130: r0 = Instance_BlendMode
    //     0x15ea130: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ea134: ldr             x0, [x0, #0xb30]
    // 0x15ea138: StoreField: r1->field_b = r0
    //     0x15ea138: stur            w0, [x1, #0xb]
    // 0x15ea13c: r0 = 1
    //     0x15ea13c: movz            x0, #0x1
    // 0x15ea140: StoreField: r1->field_13 = r0
    //     0x15ea140: stur            x0, [x1, #0x13]
    // 0x15ea144: r0 = SvgPicture()
    //     0x15ea144: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ea148: stur            x0, [fp, #-0x10]
    // 0x15ea14c: ldur            x16, [fp, #-0x20]
    // 0x15ea150: str             x16, [SP]
    // 0x15ea154: mov             x1, x0
    // 0x15ea158: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ea158: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ea15c: ldr             x2, [x2, #0xa40]
    // 0x15ea160: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ea160: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ea164: ldr             x4, [x4, #0xa38]
    // 0x15ea168: r0 = SvgPicture.asset()
    //     0x15ea168: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ea16c: r0 = Align()
    //     0x15ea16c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ea170: mov             x1, x0
    // 0x15ea174: r0 = Instance_Alignment
    //     0x15ea174: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ea178: ldr             x0, [x0, #0xb10]
    // 0x15ea17c: stur            x1, [fp, #-0x20]
    // 0x15ea180: StoreField: r1->field_f = r0
    //     0x15ea180: stur            w0, [x1, #0xf]
    // 0x15ea184: r0 = 1.000000
    //     0x15ea184: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ea188: StoreField: r1->field_13 = r0
    //     0x15ea188: stur            w0, [x1, #0x13]
    // 0x15ea18c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ea18c: stur            w0, [x1, #0x17]
    // 0x15ea190: ldur            x0, [fp, #-0x10]
    // 0x15ea194: StoreField: r1->field_b = r0
    //     0x15ea194: stur            w0, [x1, #0xb]
    // 0x15ea198: r0 = InkWell()
    //     0x15ea198: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ea19c: mov             x3, x0
    // 0x15ea1a0: ldur            x0, [fp, #-0x20]
    // 0x15ea1a4: stur            x3, [fp, #-0x10]
    // 0x15ea1a8: StoreField: r3->field_b = r0
    //     0x15ea1a8: stur            w0, [x3, #0xb]
    // 0x15ea1ac: ldur            x2, [fp, #-0x18]
    // 0x15ea1b0: r1 = Function '<anonymous closure>':.
    //     0x15ea1b0: add             x1, PP, #0x38, lsl #12  ; [pp+0x38070] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15ea1b4: ldr             x1, [x1, #0x70]
    // 0x15ea1b8: r0 = AllocateClosure()
    //     0x15ea1b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ea1bc: ldur            x2, [fp, #-0x10]
    // 0x15ea1c0: StoreField: r2->field_f = r0
    //     0x15ea1c0: stur            w0, [x2, #0xf]
    // 0x15ea1c4: r0 = true
    //     0x15ea1c4: add             x0, NULL, #0x20  ; true
    // 0x15ea1c8: StoreField: r2->field_43 = r0
    //     0x15ea1c8: stur            w0, [x2, #0x43]
    // 0x15ea1cc: r1 = Instance_BoxShape
    //     0x15ea1cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ea1d0: ldr             x1, [x1, #0x80]
    // 0x15ea1d4: StoreField: r2->field_47 = r1
    //     0x15ea1d4: stur            w1, [x2, #0x47]
    // 0x15ea1d8: StoreField: r2->field_6f = r0
    //     0x15ea1d8: stur            w0, [x2, #0x6f]
    // 0x15ea1dc: r1 = false
    //     0x15ea1dc: add             x1, NULL, #0x30  ; false
    // 0x15ea1e0: StoreField: r2->field_73 = r1
    //     0x15ea1e0: stur            w1, [x2, #0x73]
    // 0x15ea1e4: StoreField: r2->field_83 = r0
    //     0x15ea1e4: stur            w0, [x2, #0x83]
    // 0x15ea1e8: StoreField: r2->field_7b = r1
    //     0x15ea1e8: stur            w1, [x2, #0x7b]
    // 0x15ea1ec: r0 = AppBar()
    //     0x15ea1ec: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ea1f0: stur            x0, [fp, #-0x18]
    // 0x15ea1f4: ldur            x16, [fp, #-8]
    // 0x15ea1f8: str             x16, [SP]
    // 0x15ea1fc: mov             x1, x0
    // 0x15ea200: ldur            x2, [fp, #-0x10]
    // 0x15ea204: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15ea204: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15ea208: ldr             x4, [x4, #0xf00]
    // 0x15ea20c: r0 = AppBar()
    //     0x15ea20c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ea210: ldur            x0, [fp, #-0x18]
    // 0x15ea214: LeaveFrame
    //     0x15ea214: mov             SP, fp
    //     0x15ea218: ldp             fp, lr, [SP], #0x10
    // 0x15ea21c: ret
    //     0x15ea21c: ret             
    // 0x15ea220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ea220: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ea224: b               #0x15ea0c4
  }
}
