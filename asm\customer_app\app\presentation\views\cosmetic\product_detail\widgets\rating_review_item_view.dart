// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart

// class id: 1049320, size: 0x8
class :: {
}

// class id: 3397, size: 0x14, field offset: 0x14
class _RatingReviewItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb1267c, size: 0x2818
    // 0xb1267c: EnterFrame
    //     0xb1267c: stp             fp, lr, [SP, #-0x10]!
    //     0xb12680: mov             fp, SP
    // 0xb12684: AllocStack(0x80)
    //     0xb12684: sub             SP, SP, #0x80
    // 0xb12688: SetupParameters(_RatingReviewItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb12688: stur            x1, [fp, #-8]
    //     0xb1268c: stur            x2, [fp, #-0x10]
    // 0xb12690: CheckStackOverflow
    //     0xb12690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12694: cmp             SP, x16
    //     0xb12698: b.ls            #0xb14e4c
    // 0xb1269c: r1 = 2
    //     0xb1269c: movz            x1, #0x2
    // 0xb126a0: r0 = AllocateContext()
    //     0xb126a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb126a4: mov             x2, x0
    // 0xb126a8: ldur            x1, [fp, #-8]
    // 0xb126ac: stur            x2, [fp, #-0x18]
    // 0xb126b0: StoreField: r2->field_f = r1
    //     0xb126b0: stur            w1, [x2, #0xf]
    // 0xb126b4: ldur            x0, [fp, #-0x10]
    // 0xb126b8: StoreField: r2->field_13 = r0
    //     0xb126b8: stur            w0, [x2, #0x13]
    // 0xb126bc: LoadField: r0 = r1->field_b
    //     0xb126bc: ldur            w0, [x1, #0xb]
    // 0xb126c0: DecompressPointer r0
    //     0xb126c0: add             x0, x0, HEAP, lsl #32
    // 0xb126c4: cmp             w0, NULL
    // 0xb126c8: b.eq            #0xb14e54
    // 0xb126cc: LoadField: r3 = r0->field_f
    //     0xb126cc: ldur            w3, [x0, #0xf]
    // 0xb126d0: DecompressPointer r3
    //     0xb126d0: add             x3, x3, HEAP, lsl #32
    // 0xb126d4: LoadField: r4 = r3->field_b
    //     0xb126d4: ldur            w4, [x3, #0xb]
    // 0xb126d8: DecompressPointer r4
    //     0xb126d8: add             x4, x4, HEAP, lsl #32
    // 0xb126dc: cmp             w4, NULL
    // 0xb126e0: b.eq            #0xb14e38
    // 0xb126e4: LoadField: r3 = r4->field_f
    //     0xb126e4: ldur            w3, [x4, #0xf]
    // 0xb126e8: DecompressPointer r3
    //     0xb126e8: add             x3, x3, HEAP, lsl #32
    // 0xb126ec: cmp             w3, NULL
    // 0xb126f0: b.ne            #0xb126fc
    // 0xb126f4: r3 = Null
    //     0xb126f4: mov             x3, NULL
    // 0xb126f8: b               #0xb12708
    // 0xb126fc: LoadField: r4 = r3->field_13
    //     0xb126fc: ldur            w4, [x3, #0x13]
    // 0xb12700: DecompressPointer r4
    //     0xb12700: add             x4, x4, HEAP, lsl #32
    // 0xb12704: mov             x3, x4
    // 0xb12708: cbnz            w3, #0xb12714
    // 0xb1270c: r4 = false
    //     0xb1270c: add             x4, NULL, #0x30  ; false
    // 0xb12710: b               #0xb12718
    // 0xb12714: r4 = true
    //     0xb12714: add             x4, NULL, #0x20  ; true
    // 0xb12718: stur            x4, [fp, #-0x10]
    // 0xb1271c: LoadField: r3 = r0->field_13
    //     0xb1271c: ldur            w3, [x0, #0x13]
    // 0xb12720: DecompressPointer r3
    //     0xb12720: add             x3, x3, HEAP, lsl #32
    // 0xb12724: cmp             w3, NULL
    // 0xb12728: b.ne            #0xb12734
    // 0xb1272c: r0 = ""
    //     0xb1272c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb12730: b               #0xb12738
    // 0xb12734: mov             x0, x3
    // 0xb12738: r3 = LoadClassIdInstr(r0)
    //     0xb12738: ldur            x3, [x0, #-1]
    //     0xb1273c: ubfx            x3, x3, #0xc, #0x14
    // 0xb12740: str             x0, [SP]
    // 0xb12744: mov             x0, x3
    // 0xb12748: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb12748: sub             lr, x0, #1, lsl #12
    //     0xb1274c: ldr             lr, [x21, lr, lsl #3]
    //     0xb12750: blr             lr
    // 0xb12754: ldur            x2, [fp, #-0x18]
    // 0xb12758: stur            x0, [fp, #-0x20]
    // 0xb1275c: LoadField: r1 = r2->field_13
    //     0xb1275c: ldur            w1, [x2, #0x13]
    // 0xb12760: DecompressPointer r1
    //     0xb12760: add             x1, x1, HEAP, lsl #32
    // 0xb12764: r0 = of()
    //     0xb12764: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb12768: LoadField: r1 = r0->field_87
    //     0xb12768: ldur            w1, [x0, #0x87]
    // 0xb1276c: DecompressPointer r1
    //     0xb1276c: add             x1, x1, HEAP, lsl #32
    // 0xb12770: LoadField: r0 = r1->field_7
    //     0xb12770: ldur            w0, [x1, #7]
    // 0xb12774: DecompressPointer r0
    //     0xb12774: add             x0, x0, HEAP, lsl #32
    // 0xb12778: r16 = 21.000000
    //     0xb12778: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb1277c: ldr             x16, [x16, #0x9b0]
    // 0xb12780: r30 = Instance_Color
    //     0xb12780: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb12784: stp             lr, x16, [SP]
    // 0xb12788: mov             x1, x0
    // 0xb1278c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1278c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb12790: ldr             x4, [x4, #0xaa0]
    // 0xb12794: r0 = copyWith()
    //     0xb12794: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb12798: stur            x0, [fp, #-0x28]
    // 0xb1279c: r0 = Text()
    //     0xb1279c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb127a0: mov             x1, x0
    // 0xb127a4: ldur            x0, [fp, #-0x20]
    // 0xb127a8: stur            x1, [fp, #-0x30]
    // 0xb127ac: StoreField: r1->field_b = r0
    //     0xb127ac: stur            w0, [x1, #0xb]
    // 0xb127b0: ldur            x0, [fp, #-0x28]
    // 0xb127b4: StoreField: r1->field_13 = r0
    //     0xb127b4: stur            w0, [x1, #0x13]
    // 0xb127b8: r0 = Center()
    //     0xb127b8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb127bc: mov             x2, x0
    // 0xb127c0: r1 = Instance_Alignment
    //     0xb127c0: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb127c4: ldr             x1, [x1, #0xb10]
    // 0xb127c8: stur            x2, [fp, #-0x20]
    // 0xb127cc: StoreField: r2->field_f = r1
    //     0xb127cc: stur            w1, [x2, #0xf]
    // 0xb127d0: ldur            x0, [fp, #-0x30]
    // 0xb127d4: StoreField: r2->field_b = r0
    //     0xb127d4: stur            w0, [x2, #0xb]
    // 0xb127d8: ldur            x3, [fp, #-8]
    // 0xb127dc: LoadField: r0 = r3->field_b
    //     0xb127dc: ldur            w0, [x3, #0xb]
    // 0xb127e0: DecompressPointer r0
    //     0xb127e0: add             x0, x0, HEAP, lsl #32
    // 0xb127e4: cmp             w0, NULL
    // 0xb127e8: b.eq            #0xb14e58
    // 0xb127ec: LoadField: r4 = r0->field_f
    //     0xb127ec: ldur            w4, [x0, #0xf]
    // 0xb127f0: DecompressPointer r4
    //     0xb127f0: add             x4, x4, HEAP, lsl #32
    // 0xb127f4: LoadField: r0 = r4->field_b
    //     0xb127f4: ldur            w0, [x4, #0xb]
    // 0xb127f8: DecompressPointer r0
    //     0xb127f8: add             x0, x0, HEAP, lsl #32
    // 0xb127fc: cmp             w0, NULL
    // 0xb12800: b.ne            #0xb1280c
    // 0xb12804: r0 = Null
    //     0xb12804: mov             x0, NULL
    // 0xb12808: b               #0xb12850
    // 0xb1280c: LoadField: r4 = r0->field_f
    //     0xb1280c: ldur            w4, [x0, #0xf]
    // 0xb12810: DecompressPointer r4
    //     0xb12810: add             x4, x4, HEAP, lsl #32
    // 0xb12814: cmp             w4, NULL
    // 0xb12818: b.ne            #0xb12824
    // 0xb1281c: r0 = Null
    //     0xb1281c: mov             x0, NULL
    // 0xb12820: b               #0xb12850
    // 0xb12824: LoadField: r0 = r4->field_f
    //     0xb12824: ldur            w0, [x4, #0xf]
    // 0xb12828: DecompressPointer r0
    //     0xb12828: add             x0, x0, HEAP, lsl #32
    // 0xb1282c: r4 = LoadClassIdInstr(r0)
    //     0xb1282c: ldur            x4, [x0, #-1]
    //     0xb12830: ubfx            x4, x4, #0xc, #0x14
    // 0xb12834: str             x0, [SP]
    // 0xb12838: mov             x0, x4
    // 0xb1283c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb1283c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb12840: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb12840: movz            x17, #0x2700
    //     0xb12844: add             lr, x0, x17
    //     0xb12848: ldr             lr, [x21, lr, lsl #3]
    //     0xb1284c: blr             lr
    // 0xb12850: cmp             w0, NULL
    // 0xb12854: b.ne            #0xb12860
    // 0xb12858: r3 = ""
    //     0xb12858: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1285c: b               #0xb12864
    // 0xb12860: mov             x3, x0
    // 0xb12864: ldur            x0, [fp, #-8]
    // 0xb12868: ldur            x2, [fp, #-0x18]
    // 0xb1286c: stur            x3, [fp, #-0x28]
    // 0xb12870: LoadField: r1 = r2->field_13
    //     0xb12870: ldur            w1, [x2, #0x13]
    // 0xb12874: DecompressPointer r1
    //     0xb12874: add             x1, x1, HEAP, lsl #32
    // 0xb12878: r0 = of()
    //     0xb12878: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1287c: LoadField: r1 = r0->field_87
    //     0xb1287c: ldur            w1, [x0, #0x87]
    // 0xb12880: DecompressPointer r1
    //     0xb12880: add             x1, x1, HEAP, lsl #32
    // 0xb12884: LoadField: r0 = r1->field_23
    //     0xb12884: ldur            w0, [x1, #0x23]
    // 0xb12888: DecompressPointer r0
    //     0xb12888: add             x0, x0, HEAP, lsl #32
    // 0xb1288c: r16 = Instance_Color
    //     0xb1288c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb12890: r30 = 32.000000
    //     0xb12890: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb12894: ldr             lr, [lr, #0x848]
    // 0xb12898: stp             lr, x16, [SP]
    // 0xb1289c: mov             x1, x0
    // 0xb128a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb128a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb128a4: ldr             x4, [x4, #0x9b8]
    // 0xb128a8: r0 = copyWith()
    //     0xb128a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb128ac: stur            x0, [fp, #-0x30]
    // 0xb128b0: r0 = Text()
    //     0xb128b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb128b4: mov             x1, x0
    // 0xb128b8: ldur            x0, [fp, #-0x28]
    // 0xb128bc: stur            x1, [fp, #-0x38]
    // 0xb128c0: StoreField: r1->field_b = r0
    //     0xb128c0: stur            w0, [x1, #0xb]
    // 0xb128c4: ldur            x0, [fp, #-0x30]
    // 0xb128c8: StoreField: r1->field_13 = r0
    //     0xb128c8: stur            w0, [x1, #0x13]
    // 0xb128cc: r0 = Padding()
    //     0xb128cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb128d0: mov             x2, x0
    // 0xb128d4: r0 = Instance_EdgeInsets
    //     0xb128d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0xb128d8: ldr             x0, [x0, #0x850]
    // 0xb128dc: stur            x2, [fp, #-0x28]
    // 0xb128e0: StoreField: r2->field_f = r0
    //     0xb128e0: stur            w0, [x2, #0xf]
    // 0xb128e4: ldur            x0, [fp, #-0x38]
    // 0xb128e8: StoreField: r2->field_b = r0
    //     0xb128e8: stur            w0, [x2, #0xb]
    // 0xb128ec: ldur            x0, [fp, #-8]
    // 0xb128f0: LoadField: r1 = r0->field_b
    //     0xb128f0: ldur            w1, [x0, #0xb]
    // 0xb128f4: DecompressPointer r1
    //     0xb128f4: add             x1, x1, HEAP, lsl #32
    // 0xb128f8: cmp             w1, NULL
    // 0xb128fc: b.eq            #0xb14e5c
    // 0xb12900: LoadField: r3 = r1->field_f
    //     0xb12900: ldur            w3, [x1, #0xf]
    // 0xb12904: DecompressPointer r3
    //     0xb12904: add             x3, x3, HEAP, lsl #32
    // 0xb12908: LoadField: r1 = r3->field_b
    //     0xb12908: ldur            w1, [x3, #0xb]
    // 0xb1290c: DecompressPointer r1
    //     0xb1290c: add             x1, x1, HEAP, lsl #32
    // 0xb12910: cmp             w1, NULL
    // 0xb12914: b.ne            #0xb12920
    // 0xb12918: r3 = Null
    //     0xb12918: mov             x3, NULL
    // 0xb1291c: b               #0xb12944
    // 0xb12920: LoadField: r3 = r1->field_f
    //     0xb12920: ldur            w3, [x1, #0xf]
    // 0xb12924: DecompressPointer r3
    //     0xb12924: add             x3, x3, HEAP, lsl #32
    // 0xb12928: cmp             w3, NULL
    // 0xb1292c: b.ne            #0xb12938
    // 0xb12930: r3 = Null
    //     0xb12930: mov             x3, NULL
    // 0xb12934: b               #0xb12944
    // 0xb12938: LoadField: r4 = r3->field_f
    //     0xb12938: ldur            w4, [x3, #0xf]
    // 0xb1293c: DecompressPointer r4
    //     0xb1293c: add             x4, x4, HEAP, lsl #32
    // 0xb12940: mov             x3, x4
    // 0xb12944: cmp             w3, NULL
    // 0xb12948: b.ne            #0xb12954
    // 0xb1294c: d1 = 0.000000
    //     0xb1294c: eor             v1.16b, v1.16b, v1.16b
    // 0xb12950: b               #0xb1295c
    // 0xb12954: LoadField: d0 = r3->field_7
    //     0xb12954: ldur            d0, [x3, #7]
    // 0xb12958: mov             v1.16b, v0.16b
    // 0xb1295c: d0 = 4.000000
    //     0xb1295c: fmov            d0, #4.00000000
    // 0xb12960: fcmp            d1, d0
    // 0xb12964: b.lt            #0xb1297c
    // 0xb12968: mov             x1, x0
    // 0xb1296c: mov             x0, x2
    // 0xb12970: r2 = Instance_Color
    //     0xb12970: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb12974: ldr             x2, [x2, #0x858]
    // 0xb12978: b               #0xb12a64
    // 0xb1297c: cmp             w1, NULL
    // 0xb12980: b.ne            #0xb1298c
    // 0xb12984: r3 = Null
    //     0xb12984: mov             x3, NULL
    // 0xb12988: b               #0xb129b0
    // 0xb1298c: LoadField: r3 = r1->field_f
    //     0xb1298c: ldur            w3, [x1, #0xf]
    // 0xb12990: DecompressPointer r3
    //     0xb12990: add             x3, x3, HEAP, lsl #32
    // 0xb12994: cmp             w3, NULL
    // 0xb12998: b.ne            #0xb129a4
    // 0xb1299c: r3 = Null
    //     0xb1299c: mov             x3, NULL
    // 0xb129a0: b               #0xb129b0
    // 0xb129a4: LoadField: r4 = r3->field_f
    //     0xb129a4: ldur            w4, [x3, #0xf]
    // 0xb129a8: DecompressPointer r4
    //     0xb129a8: add             x4, x4, HEAP, lsl #32
    // 0xb129ac: mov             x3, x4
    // 0xb129b0: cmp             w3, NULL
    // 0xb129b4: b.ne            #0xb129c0
    // 0xb129b8: d1 = 0.000000
    //     0xb129b8: eor             v1.16b, v1.16b, v1.16b
    // 0xb129bc: b               #0xb129c8
    // 0xb129c0: LoadField: d0 = r3->field_7
    //     0xb129c0: ldur            d0, [x3, #7]
    // 0xb129c4: mov             v1.16b, v0.16b
    // 0xb129c8: d0 = 3.500000
    //     0xb129c8: fmov            d0, #3.50000000
    // 0xb129cc: fcmp            d1, d0
    // 0xb129d0: b.lt            #0xb129ec
    // 0xb129d4: r1 = Instance_Color
    //     0xb129d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb129d8: ldr             x1, [x1, #0x858]
    // 0xb129dc: d0 = 0.700000
    //     0xb129dc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb129e0: ldr             d0, [x17, #0xf48]
    // 0xb129e4: r0 = withOpacity()
    //     0xb129e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb129e8: b               #0xb12a58
    // 0xb129ec: cmp             w1, NULL
    // 0xb129f0: b.ne            #0xb129fc
    // 0xb129f4: r0 = Null
    //     0xb129f4: mov             x0, NULL
    // 0xb129f8: b               #0xb12a20
    // 0xb129fc: LoadField: r0 = r1->field_f
    //     0xb129fc: ldur            w0, [x1, #0xf]
    // 0xb12a00: DecompressPointer r0
    //     0xb12a00: add             x0, x0, HEAP, lsl #32
    // 0xb12a04: cmp             w0, NULL
    // 0xb12a08: b.ne            #0xb12a14
    // 0xb12a0c: r0 = Null
    //     0xb12a0c: mov             x0, NULL
    // 0xb12a10: b               #0xb12a20
    // 0xb12a14: LoadField: r1 = r0->field_f
    //     0xb12a14: ldur            w1, [x0, #0xf]
    // 0xb12a18: DecompressPointer r1
    //     0xb12a18: add             x1, x1, HEAP, lsl #32
    // 0xb12a1c: mov             x0, x1
    // 0xb12a20: cmp             w0, NULL
    // 0xb12a24: b.ne            #0xb12a30
    // 0xb12a28: d1 = 0.000000
    //     0xb12a28: eor             v1.16b, v1.16b, v1.16b
    // 0xb12a2c: b               #0xb12a38
    // 0xb12a30: LoadField: d0 = r0->field_7
    //     0xb12a30: ldur            d0, [x0, #7]
    // 0xb12a34: mov             v1.16b, v0.16b
    // 0xb12a38: d0 = 2.000000
    //     0xb12a38: fmov            d0, #2.00000000
    // 0xb12a3c: fcmp            d1, d0
    // 0xb12a40: b.lt            #0xb12a50
    // 0xb12a44: r0 = Instance_Color
    //     0xb12a44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb12a48: ldr             x0, [x0, #0x860]
    // 0xb12a4c: b               #0xb12a58
    // 0xb12a50: r0 = Instance_Color
    //     0xb12a50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb12a54: ldr             x0, [x0, #0x50]
    // 0xb12a58: mov             x2, x0
    // 0xb12a5c: ldur            x1, [fp, #-8]
    // 0xb12a60: ldur            x0, [fp, #-0x28]
    // 0xb12a64: stur            x2, [fp, #-0x30]
    // 0xb12a68: r0 = ColorFilter()
    //     0xb12a68: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb12a6c: mov             x1, x0
    // 0xb12a70: ldur            x0, [fp, #-0x30]
    // 0xb12a74: stur            x1, [fp, #-0x38]
    // 0xb12a78: StoreField: r1->field_7 = r0
    //     0xb12a78: stur            w0, [x1, #7]
    // 0xb12a7c: r0 = Instance_BlendMode
    //     0xb12a7c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb12a80: ldr             x0, [x0, #0xb30]
    // 0xb12a84: StoreField: r1->field_b = r0
    //     0xb12a84: stur            w0, [x1, #0xb]
    // 0xb12a88: r0 = 1
    //     0xb12a88: movz            x0, #0x1
    // 0xb12a8c: StoreField: r1->field_13 = r0
    //     0xb12a8c: stur            x0, [x1, #0x13]
    // 0xb12a90: r0 = SvgPicture()
    //     0xb12a90: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb12a94: stur            x0, [fp, #-0x30]
    // 0xb12a98: ldur            x16, [fp, #-0x38]
    // 0xb12a9c: str             x16, [SP]
    // 0xb12aa0: mov             x1, x0
    // 0xb12aa4: r2 = "assets/images/big_green_star.svg"
    //     0xb12aa4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f868] "assets/images/big_green_star.svg"
    //     0xb12aa8: ldr             x2, [x2, #0x868]
    // 0xb12aac: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb12aac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb12ab0: ldr             x4, [x4, #0xa38]
    // 0xb12ab4: r0 = SvgPicture.asset()
    //     0xb12ab4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb12ab8: r1 = Null
    //     0xb12ab8: mov             x1, NULL
    // 0xb12abc: r2 = 4
    //     0xb12abc: movz            x2, #0x4
    // 0xb12ac0: r0 = AllocateArray()
    //     0xb12ac0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb12ac4: mov             x2, x0
    // 0xb12ac8: ldur            x0, [fp, #-0x28]
    // 0xb12acc: stur            x2, [fp, #-0x38]
    // 0xb12ad0: StoreField: r2->field_f = r0
    //     0xb12ad0: stur            w0, [x2, #0xf]
    // 0xb12ad4: ldur            x0, [fp, #-0x30]
    // 0xb12ad8: StoreField: r2->field_13 = r0
    //     0xb12ad8: stur            w0, [x2, #0x13]
    // 0xb12adc: r1 = <Widget>
    //     0xb12adc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb12ae0: r0 = AllocateGrowableArray()
    //     0xb12ae0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb12ae4: mov             x1, x0
    // 0xb12ae8: ldur            x0, [fp, #-0x38]
    // 0xb12aec: stur            x1, [fp, #-0x28]
    // 0xb12af0: StoreField: r1->field_f = r0
    //     0xb12af0: stur            w0, [x1, #0xf]
    // 0xb12af4: r2 = 4
    //     0xb12af4: movz            x2, #0x4
    // 0xb12af8: StoreField: r1->field_b = r2
    //     0xb12af8: stur            w2, [x1, #0xb]
    // 0xb12afc: r0 = Row()
    //     0xb12afc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb12b00: mov             x3, x0
    // 0xb12b04: r0 = Instance_Axis
    //     0xb12b04: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb12b08: stur            x3, [fp, #-0x30]
    // 0xb12b0c: StoreField: r3->field_f = r0
    //     0xb12b0c: stur            w0, [x3, #0xf]
    // 0xb12b10: r4 = Instance_MainAxisAlignment
    //     0xb12b10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb12b14: ldr             x4, [x4, #0xa08]
    // 0xb12b18: StoreField: r3->field_13 = r4
    //     0xb12b18: stur            w4, [x3, #0x13]
    // 0xb12b1c: r5 = Instance_MainAxisSize
    //     0xb12b1c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb12b20: ldr             x5, [x5, #0xa10]
    // 0xb12b24: ArrayStore: r3[0] = r5  ; List_4
    //     0xb12b24: stur            w5, [x3, #0x17]
    // 0xb12b28: r6 = Instance_CrossAxisAlignment
    //     0xb12b28: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb12b2c: ldr             x6, [x6, #0xa18]
    // 0xb12b30: StoreField: r3->field_1b = r6
    //     0xb12b30: stur            w6, [x3, #0x1b]
    // 0xb12b34: r7 = Instance_VerticalDirection
    //     0xb12b34: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb12b38: ldr             x7, [x7, #0xa20]
    // 0xb12b3c: StoreField: r3->field_23 = r7
    //     0xb12b3c: stur            w7, [x3, #0x23]
    // 0xb12b40: r8 = Instance_Clip
    //     0xb12b40: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb12b44: ldr             x8, [x8, #0x38]
    // 0xb12b48: StoreField: r3->field_2b = r8
    //     0xb12b48: stur            w8, [x3, #0x2b]
    // 0xb12b4c: StoreField: r3->field_2f = rZR
    //     0xb12b4c: stur            xzr, [x3, #0x2f]
    // 0xb12b50: ldur            x1, [fp, #-0x28]
    // 0xb12b54: StoreField: r3->field_b = r1
    //     0xb12b54: stur            w1, [x3, #0xb]
    // 0xb12b58: r1 = Null
    //     0xb12b58: mov             x1, NULL
    // 0xb12b5c: r2 = 2
    //     0xb12b5c: movz            x2, #0x2
    // 0xb12b60: r0 = AllocateArray()
    //     0xb12b60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb12b64: mov             x2, x0
    // 0xb12b68: ldur            x0, [fp, #-0x30]
    // 0xb12b6c: stur            x2, [fp, #-0x28]
    // 0xb12b70: StoreField: r2->field_f = r0
    //     0xb12b70: stur            w0, [x2, #0xf]
    // 0xb12b74: r1 = <Widget>
    //     0xb12b74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb12b78: r0 = AllocateGrowableArray()
    //     0xb12b78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb12b7c: mov             x3, x0
    // 0xb12b80: ldur            x0, [fp, #-0x28]
    // 0xb12b84: stur            x3, [fp, #-0x30]
    // 0xb12b88: StoreField: r3->field_f = r0
    //     0xb12b88: stur            w0, [x3, #0xf]
    // 0xb12b8c: r0 = 2
    //     0xb12b8c: movz            x0, #0x2
    // 0xb12b90: StoreField: r3->field_b = r0
    //     0xb12b90: stur            w0, [x3, #0xb]
    // 0xb12b94: ldur            x0, [fp, #-8]
    // 0xb12b98: LoadField: r1 = r0->field_b
    //     0xb12b98: ldur            w1, [x0, #0xb]
    // 0xb12b9c: DecompressPointer r1
    //     0xb12b9c: add             x1, x1, HEAP, lsl #32
    // 0xb12ba0: cmp             w1, NULL
    // 0xb12ba4: b.eq            #0xb14e60
    // 0xb12ba8: LoadField: r2 = r1->field_f
    //     0xb12ba8: ldur            w2, [x1, #0xf]
    // 0xb12bac: DecompressPointer r2
    //     0xb12bac: add             x2, x2, HEAP, lsl #32
    // 0xb12bb0: LoadField: r1 = r2->field_b
    //     0xb12bb0: ldur            w1, [x2, #0xb]
    // 0xb12bb4: DecompressPointer r1
    //     0xb12bb4: add             x1, x1, HEAP, lsl #32
    // 0xb12bb8: cmp             w1, NULL
    // 0xb12bbc: b.eq            #0xb12bdc
    // 0xb12bc0: LoadField: r2 = r1->field_f
    //     0xb12bc0: ldur            w2, [x1, #0xf]
    // 0xb12bc4: DecompressPointer r2
    //     0xb12bc4: add             x2, x2, HEAP, lsl #32
    // 0xb12bc8: cmp             w2, NULL
    // 0xb12bcc: b.eq            #0xb12bdc
    // 0xb12bd0: LoadField: r4 = r2->field_13
    //     0xb12bd0: ldur            w4, [x2, #0x13]
    // 0xb12bd4: DecompressPointer r4
    //     0xb12bd4: add             x4, x4, HEAP, lsl #32
    // 0xb12bd8: cbz             w4, #0xb12d3c
    // 0xb12bdc: cmp             w1, NULL
    // 0xb12be0: b.ne            #0xb12bec
    // 0xb12be4: r1 = Null
    //     0xb12be4: mov             x1, NULL
    // 0xb12be8: b               #0xb12c0c
    // 0xb12bec: LoadField: r2 = r1->field_f
    //     0xb12bec: ldur            w2, [x1, #0xf]
    // 0xb12bf0: DecompressPointer r2
    //     0xb12bf0: add             x2, x2, HEAP, lsl #32
    // 0xb12bf4: cmp             w2, NULL
    // 0xb12bf8: b.ne            #0xb12c04
    // 0xb12bfc: r1 = Null
    //     0xb12bfc: mov             x1, NULL
    // 0xb12c00: b               #0xb12c0c
    // 0xb12c04: LoadField: r1 = r2->field_13
    //     0xb12c04: ldur            w1, [x2, #0x13]
    // 0xb12c08: DecompressPointer r1
    //     0xb12c08: add             x1, x1, HEAP, lsl #32
    // 0xb12c0c: cmp             w1, NULL
    // 0xb12c10: b.ne            #0xb12c1c
    // 0xb12c14: r5 = ""
    //     0xb12c14: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb12c18: b               #0xb12c20
    // 0xb12c1c: mov             x5, x1
    // 0xb12c20: ldur            x4, [fp, #-0x18]
    // 0xb12c24: stur            x5, [fp, #-0x28]
    // 0xb12c28: r1 = Null
    //     0xb12c28: mov             x1, NULL
    // 0xb12c2c: r2 = 4
    //     0xb12c2c: movz            x2, #0x4
    // 0xb12c30: r0 = AllocateArray()
    //     0xb12c30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb12c34: mov             x1, x0
    // 0xb12c38: ldur            x0, [fp, #-0x28]
    // 0xb12c3c: StoreField: r1->field_f = r0
    //     0xb12c3c: stur            w0, [x1, #0xf]
    // 0xb12c40: r16 = " Ratings"
    //     0xb12c40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f870] " Ratings"
    //     0xb12c44: ldr             x16, [x16, #0x870]
    // 0xb12c48: StoreField: r1->field_13 = r16
    //     0xb12c48: stur            w16, [x1, #0x13]
    // 0xb12c4c: str             x1, [SP]
    // 0xb12c50: r0 = _interpolate()
    //     0xb12c50: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb12c54: ldur            x2, [fp, #-0x18]
    // 0xb12c58: stur            x0, [fp, #-0x28]
    // 0xb12c5c: LoadField: r1 = r2->field_13
    //     0xb12c5c: ldur            w1, [x2, #0x13]
    // 0xb12c60: DecompressPointer r1
    //     0xb12c60: add             x1, x1, HEAP, lsl #32
    // 0xb12c64: r0 = of()
    //     0xb12c64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb12c68: LoadField: r1 = r0->field_87
    //     0xb12c68: ldur            w1, [x0, #0x87]
    // 0xb12c6c: DecompressPointer r1
    //     0xb12c6c: add             x1, x1, HEAP, lsl #32
    // 0xb12c70: LoadField: r0 = r1->field_2b
    //     0xb12c70: ldur            w0, [x1, #0x2b]
    // 0xb12c74: DecompressPointer r0
    //     0xb12c74: add             x0, x0, HEAP, lsl #32
    // 0xb12c78: stur            x0, [fp, #-0x38]
    // 0xb12c7c: r1 = Instance_Color
    //     0xb12c7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb12c80: d0 = 0.850000
    //     0xb12c80: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xb12c84: ldr             d0, [x17, #0x878]
    // 0xb12c88: r0 = withOpacity()
    //     0xb12c88: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb12c8c: r16 = 10.000000
    //     0xb12c8c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb12c90: stp             x0, x16, [SP]
    // 0xb12c94: ldur            x1, [fp, #-0x38]
    // 0xb12c98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb12c98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb12c9c: ldr             x4, [x4, #0xaa0]
    // 0xb12ca0: r0 = copyWith()
    //     0xb12ca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb12ca4: stur            x0, [fp, #-0x38]
    // 0xb12ca8: r0 = Text()
    //     0xb12ca8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb12cac: mov             x2, x0
    // 0xb12cb0: ldur            x0, [fp, #-0x28]
    // 0xb12cb4: stur            x2, [fp, #-0x48]
    // 0xb12cb8: StoreField: r2->field_b = r0
    //     0xb12cb8: stur            w0, [x2, #0xb]
    // 0xb12cbc: ldur            x0, [fp, #-0x38]
    // 0xb12cc0: StoreField: r2->field_13 = r0
    //     0xb12cc0: stur            w0, [x2, #0x13]
    // 0xb12cc4: ldur            x0, [fp, #-0x30]
    // 0xb12cc8: LoadField: r1 = r0->field_b
    //     0xb12cc8: ldur            w1, [x0, #0xb]
    // 0xb12ccc: LoadField: r3 = r0->field_f
    //     0xb12ccc: ldur            w3, [x0, #0xf]
    // 0xb12cd0: DecompressPointer r3
    //     0xb12cd0: add             x3, x3, HEAP, lsl #32
    // 0xb12cd4: LoadField: r4 = r3->field_b
    //     0xb12cd4: ldur            w4, [x3, #0xb]
    // 0xb12cd8: r3 = LoadInt32Instr(r1)
    //     0xb12cd8: sbfx            x3, x1, #1, #0x1f
    // 0xb12cdc: stur            x3, [fp, #-0x40]
    // 0xb12ce0: r1 = LoadInt32Instr(r4)
    //     0xb12ce0: sbfx            x1, x4, #1, #0x1f
    // 0xb12ce4: cmp             x3, x1
    // 0xb12ce8: b.ne            #0xb12cf4
    // 0xb12cec: mov             x1, x0
    // 0xb12cf0: r0 = _growToNextCapacity()
    //     0xb12cf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb12cf4: ldur            x3, [fp, #-0x30]
    // 0xb12cf8: ldur            x2, [fp, #-0x40]
    // 0xb12cfc: add             x0, x2, #1
    // 0xb12d00: lsl             x1, x0, #1
    // 0xb12d04: StoreField: r3->field_b = r1
    //     0xb12d04: stur            w1, [x3, #0xb]
    // 0xb12d08: LoadField: r1 = r3->field_f
    //     0xb12d08: ldur            w1, [x3, #0xf]
    // 0xb12d0c: DecompressPointer r1
    //     0xb12d0c: add             x1, x1, HEAP, lsl #32
    // 0xb12d10: ldur            x0, [fp, #-0x48]
    // 0xb12d14: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb12d14: add             x25, x1, x2, lsl #2
    //     0xb12d18: add             x25, x25, #0xf
    //     0xb12d1c: str             w0, [x25]
    //     0xb12d20: tbz             w0, #0, #0xb12d3c
    //     0xb12d24: ldurb           w16, [x1, #-1]
    //     0xb12d28: ldurb           w17, [x0, #-1]
    //     0xb12d2c: and             x16, x17, x16, lsr #2
    //     0xb12d30: tst             x16, HEAP, lsr #32
    //     0xb12d34: b.eq            #0xb12d3c
    //     0xb12d38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb12d3c: ldur            x0, [fp, #-8]
    // 0xb12d40: LoadField: r1 = r0->field_b
    //     0xb12d40: ldur            w1, [x0, #0xb]
    // 0xb12d44: DecompressPointer r1
    //     0xb12d44: add             x1, x1, HEAP, lsl #32
    // 0xb12d48: cmp             w1, NULL
    // 0xb12d4c: b.eq            #0xb14e64
    // 0xb12d50: LoadField: r2 = r1->field_f
    //     0xb12d50: ldur            w2, [x1, #0xf]
    // 0xb12d54: DecompressPointer r2
    //     0xb12d54: add             x2, x2, HEAP, lsl #32
    // 0xb12d58: LoadField: r4 = r2->field_b
    //     0xb12d58: ldur            w4, [x2, #0xb]
    // 0xb12d5c: DecompressPointer r4
    //     0xb12d5c: add             x4, x4, HEAP, lsl #32
    // 0xb12d60: stur            x4, [fp, #-0x28]
    // 0xb12d64: cmp             w4, NULL
    // 0xb12d68: b.eq            #0xb12d88
    // 0xb12d6c: LoadField: r1 = r4->field_f
    //     0xb12d6c: ldur            w1, [x4, #0xf]
    // 0xb12d70: DecompressPointer r1
    //     0xb12d70: add             x1, x1, HEAP, lsl #32
    // 0xb12d74: cmp             w1, NULL
    // 0xb12d78: b.eq            #0xb12d88
    // 0xb12d7c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb12d7c: ldur            w2, [x1, #0x17]
    // 0xb12d80: DecompressPointer r2
    //     0xb12d80: add             x2, x2, HEAP, lsl #32
    // 0xb12d84: cbz             w2, #0xb12ef4
    // 0xb12d88: r1 = Null
    //     0xb12d88: mov             x1, NULL
    // 0xb12d8c: r2 = 6
    //     0xb12d8c: movz            x2, #0x6
    // 0xb12d90: r0 = AllocateArray()
    //     0xb12d90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb12d94: r16 = "& "
    //     0xb12d94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f880] "& "
    //     0xb12d98: ldr             x16, [x16, #0x880]
    // 0xb12d9c: StoreField: r0->field_f = r16
    //     0xb12d9c: stur            w16, [x0, #0xf]
    // 0xb12da0: ldur            x1, [fp, #-0x28]
    // 0xb12da4: cmp             w1, NULL
    // 0xb12da8: b.ne            #0xb12db4
    // 0xb12dac: r1 = Null
    //     0xb12dac: mov             x1, NULL
    // 0xb12db0: b               #0xb12dd4
    // 0xb12db4: LoadField: r2 = r1->field_f
    //     0xb12db4: ldur            w2, [x1, #0xf]
    // 0xb12db8: DecompressPointer r2
    //     0xb12db8: add             x2, x2, HEAP, lsl #32
    // 0xb12dbc: cmp             w2, NULL
    // 0xb12dc0: b.ne            #0xb12dcc
    // 0xb12dc4: r1 = Null
    //     0xb12dc4: mov             x1, NULL
    // 0xb12dc8: b               #0xb12dd4
    // 0xb12dcc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb12dcc: ldur            w1, [x2, #0x17]
    // 0xb12dd0: DecompressPointer r1
    //     0xb12dd0: add             x1, x1, HEAP, lsl #32
    // 0xb12dd4: cmp             w1, NULL
    // 0xb12dd8: b.ne            #0xb12de4
    // 0xb12ddc: r3 = ""
    //     0xb12ddc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb12de0: b               #0xb12de8
    // 0xb12de4: mov             x3, x1
    // 0xb12de8: ldur            x2, [fp, #-0x18]
    // 0xb12dec: ldur            x1, [fp, #-0x30]
    // 0xb12df0: StoreField: r0->field_13 = r3
    //     0xb12df0: stur            w3, [x0, #0x13]
    // 0xb12df4: r16 = " Reviews"
    //     0xb12df4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f888] " Reviews"
    //     0xb12df8: ldr             x16, [x16, #0x888]
    // 0xb12dfc: ArrayStore: r0[0] = r16  ; List_4
    //     0xb12dfc: stur            w16, [x0, #0x17]
    // 0xb12e00: str             x0, [SP]
    // 0xb12e04: r0 = _interpolate()
    //     0xb12e04: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb12e08: ldur            x2, [fp, #-0x18]
    // 0xb12e0c: stur            x0, [fp, #-0x28]
    // 0xb12e10: LoadField: r1 = r2->field_13
    //     0xb12e10: ldur            w1, [x2, #0x13]
    // 0xb12e14: DecompressPointer r1
    //     0xb12e14: add             x1, x1, HEAP, lsl #32
    // 0xb12e18: r0 = of()
    //     0xb12e18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb12e1c: LoadField: r1 = r0->field_87
    //     0xb12e1c: ldur            w1, [x0, #0x87]
    // 0xb12e20: DecompressPointer r1
    //     0xb12e20: add             x1, x1, HEAP, lsl #32
    // 0xb12e24: LoadField: r0 = r1->field_2b
    //     0xb12e24: ldur            w0, [x1, #0x2b]
    // 0xb12e28: DecompressPointer r0
    //     0xb12e28: add             x0, x0, HEAP, lsl #32
    // 0xb12e2c: stur            x0, [fp, #-0x38]
    // 0xb12e30: r1 = Instance_Color
    //     0xb12e30: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb12e34: d0 = 0.850000
    //     0xb12e34: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xb12e38: ldr             d0, [x17, #0x878]
    // 0xb12e3c: r0 = withOpacity()
    //     0xb12e3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb12e40: r16 = 10.000000
    //     0xb12e40: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb12e44: stp             x0, x16, [SP]
    // 0xb12e48: ldur            x1, [fp, #-0x38]
    // 0xb12e4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb12e4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb12e50: ldr             x4, [x4, #0xaa0]
    // 0xb12e54: r0 = copyWith()
    //     0xb12e54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb12e58: stur            x0, [fp, #-0x38]
    // 0xb12e5c: r0 = Text()
    //     0xb12e5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb12e60: mov             x2, x0
    // 0xb12e64: ldur            x0, [fp, #-0x28]
    // 0xb12e68: stur            x2, [fp, #-0x48]
    // 0xb12e6c: StoreField: r2->field_b = r0
    //     0xb12e6c: stur            w0, [x2, #0xb]
    // 0xb12e70: ldur            x0, [fp, #-0x38]
    // 0xb12e74: StoreField: r2->field_13 = r0
    //     0xb12e74: stur            w0, [x2, #0x13]
    // 0xb12e78: ldur            x0, [fp, #-0x30]
    // 0xb12e7c: LoadField: r1 = r0->field_b
    //     0xb12e7c: ldur            w1, [x0, #0xb]
    // 0xb12e80: LoadField: r3 = r0->field_f
    //     0xb12e80: ldur            w3, [x0, #0xf]
    // 0xb12e84: DecompressPointer r3
    //     0xb12e84: add             x3, x3, HEAP, lsl #32
    // 0xb12e88: LoadField: r4 = r3->field_b
    //     0xb12e88: ldur            w4, [x3, #0xb]
    // 0xb12e8c: r3 = LoadInt32Instr(r1)
    //     0xb12e8c: sbfx            x3, x1, #1, #0x1f
    // 0xb12e90: stur            x3, [fp, #-0x40]
    // 0xb12e94: r1 = LoadInt32Instr(r4)
    //     0xb12e94: sbfx            x1, x4, #1, #0x1f
    // 0xb12e98: cmp             x3, x1
    // 0xb12e9c: b.ne            #0xb12ea8
    // 0xb12ea0: mov             x1, x0
    // 0xb12ea4: r0 = _growToNextCapacity()
    //     0xb12ea4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb12ea8: ldur            x2, [fp, #-0x30]
    // 0xb12eac: ldur            x3, [fp, #-0x40]
    // 0xb12eb0: add             x0, x3, #1
    // 0xb12eb4: lsl             x1, x0, #1
    // 0xb12eb8: StoreField: r2->field_b = r1
    //     0xb12eb8: stur            w1, [x2, #0xb]
    // 0xb12ebc: LoadField: r1 = r2->field_f
    //     0xb12ebc: ldur            w1, [x2, #0xf]
    // 0xb12ec0: DecompressPointer r1
    //     0xb12ec0: add             x1, x1, HEAP, lsl #32
    // 0xb12ec4: ldur            x0, [fp, #-0x48]
    // 0xb12ec8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb12ec8: add             x25, x1, x3, lsl #2
    //     0xb12ecc: add             x25, x25, #0xf
    //     0xb12ed0: str             w0, [x25]
    //     0xb12ed4: tbz             w0, #0, #0xb12ef0
    //     0xb12ed8: ldurb           w16, [x1, #-1]
    //     0xb12edc: ldurb           w17, [x0, #-1]
    //     0xb12ee0: and             x16, x17, x16, lsr #2
    //     0xb12ee4: tst             x16, HEAP, lsr #32
    //     0xb12ee8: b.eq            #0xb12ef0
    //     0xb12eec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb12ef0: b               #0xb12ef8
    // 0xb12ef4: mov             x2, x3
    // 0xb12ef8: ldur            x1, [fp, #-8]
    // 0xb12efc: LoadField: r0 = r1->field_b
    //     0xb12efc: ldur            w0, [x1, #0xb]
    // 0xb12f00: DecompressPointer r0
    //     0xb12f00: add             x0, x0, HEAP, lsl #32
    // 0xb12f04: cmp             w0, NULL
    // 0xb12f08: b.eq            #0xb14e68
    // 0xb12f0c: LoadField: r3 = r0->field_f
    //     0xb12f0c: ldur            w3, [x0, #0xf]
    // 0xb12f10: DecompressPointer r3
    //     0xb12f10: add             x3, x3, HEAP, lsl #32
    // 0xb12f14: LoadField: r0 = r3->field_b
    //     0xb12f14: ldur            w0, [x3, #0xb]
    // 0xb12f18: DecompressPointer r0
    //     0xb12f18: add             x0, x0, HEAP, lsl #32
    // 0xb12f1c: cmp             w0, NULL
    // 0xb12f20: b.ne            #0xb12f2c
    // 0xb12f24: r0 = Null
    //     0xb12f24: mov             x0, NULL
    // 0xb12f28: b               #0xb12f4c
    // 0xb12f2c: LoadField: r3 = r0->field_f
    //     0xb12f2c: ldur            w3, [x0, #0xf]
    // 0xb12f30: DecompressPointer r3
    //     0xb12f30: add             x3, x3, HEAP, lsl #32
    // 0xb12f34: cmp             w3, NULL
    // 0xb12f38: b.ne            #0xb12f44
    // 0xb12f3c: r0 = Null
    //     0xb12f3c: mov             x0, NULL
    // 0xb12f40: b               #0xb12f4c
    // 0xb12f44: LoadField: r0 = r3->field_b
    //     0xb12f44: ldur            w0, [x3, #0xb]
    // 0xb12f48: DecompressPointer r0
    //     0xb12f48: add             x0, x0, HEAP, lsl #32
    // 0xb12f4c: cmp             w0, NULL
    // 0xb12f50: b.ne            #0xb12f58
    // 0xb12f54: r0 = false
    //     0xb12f54: add             x0, NULL, #0x30  ; false
    // 0xb12f58: stur            x0, [fp, #-0x28]
    // 0xb12f5c: r0 = SvgPicture()
    //     0xb12f5c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb12f60: mov             x1, x0
    // 0xb12f64: r2 = "assets/images/shopdeck-tag.svg"
    //     0xb12f64: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd38] "assets/images/shopdeck-tag.svg"
    //     0xb12f68: ldr             x2, [x2, #0xd38]
    // 0xb12f6c: stur            x0, [fp, #-0x38]
    // 0xb12f70: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb12f70: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb12f74: r0 = SvgPicture.asset()
    //     0xb12f74: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb12f78: r0 = InkWell()
    //     0xb12f78: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb12f7c: mov             x3, x0
    // 0xb12f80: ldur            x0, [fp, #-0x38]
    // 0xb12f84: stur            x3, [fp, #-0x48]
    // 0xb12f88: StoreField: r3->field_b = r0
    //     0xb12f88: stur            w0, [x3, #0xb]
    // 0xb12f8c: ldur            x2, [fp, #-0x18]
    // 0xb12f90: r1 = Function '<anonymous closure>':.
    //     0xb12f90: add             x1, PP, #0x57, lsl #12  ; [pp+0x57940] AnonymousClosure: (0xb15d10), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb12f94: ldr             x1, [x1, #0x940]
    // 0xb12f98: r0 = AllocateClosure()
    //     0xb12f98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb12f9c: mov             x1, x0
    // 0xb12fa0: ldur            x0, [fp, #-0x48]
    // 0xb12fa4: StoreField: r0->field_f = r1
    //     0xb12fa4: stur            w1, [x0, #0xf]
    // 0xb12fa8: r1 = true
    //     0xb12fa8: add             x1, NULL, #0x20  ; true
    // 0xb12fac: StoreField: r0->field_43 = r1
    //     0xb12fac: stur            w1, [x0, #0x43]
    // 0xb12fb0: r2 = Instance_BoxShape
    //     0xb12fb0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb12fb4: ldr             x2, [x2, #0x80]
    // 0xb12fb8: StoreField: r0->field_47 = r2
    //     0xb12fb8: stur            w2, [x0, #0x47]
    // 0xb12fbc: StoreField: r0->field_6f = r1
    //     0xb12fbc: stur            w1, [x0, #0x6f]
    // 0xb12fc0: r3 = false
    //     0xb12fc0: add             x3, NULL, #0x30  ; false
    // 0xb12fc4: StoreField: r0->field_73 = r3
    //     0xb12fc4: stur            w3, [x0, #0x73]
    // 0xb12fc8: StoreField: r0->field_83 = r1
    //     0xb12fc8: stur            w1, [x0, #0x83]
    // 0xb12fcc: StoreField: r0->field_7b = r3
    //     0xb12fcc: stur            w3, [x0, #0x7b]
    // 0xb12fd0: r0 = Padding()
    //     0xb12fd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb12fd4: mov             x1, x0
    // 0xb12fd8: r0 = Instance_EdgeInsets
    //     0xb12fd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb12fdc: ldr             x0, [x0, #0x990]
    // 0xb12fe0: stur            x1, [fp, #-0x38]
    // 0xb12fe4: StoreField: r1->field_f = r0
    //     0xb12fe4: stur            w0, [x1, #0xf]
    // 0xb12fe8: ldur            x0, [fp, #-0x48]
    // 0xb12fec: StoreField: r1->field_b = r0
    //     0xb12fec: stur            w0, [x1, #0xb]
    // 0xb12ff0: r0 = Visibility()
    //     0xb12ff0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb12ff4: mov             x2, x0
    // 0xb12ff8: ldur            x0, [fp, #-0x38]
    // 0xb12ffc: stur            x2, [fp, #-0x48]
    // 0xb13000: StoreField: r2->field_b = r0
    //     0xb13000: stur            w0, [x2, #0xb]
    // 0xb13004: r0 = Instance_SizedBox
    //     0xb13004: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb13008: StoreField: r2->field_f = r0
    //     0xb13008: stur            w0, [x2, #0xf]
    // 0xb1300c: ldur            x1, [fp, #-0x28]
    // 0xb13010: StoreField: r2->field_13 = r1
    //     0xb13010: stur            w1, [x2, #0x13]
    // 0xb13014: r3 = false
    //     0xb13014: add             x3, NULL, #0x30  ; false
    // 0xb13018: ArrayStore: r2[0] = r3  ; List_4
    //     0xb13018: stur            w3, [x2, #0x17]
    // 0xb1301c: StoreField: r2->field_1b = r3
    //     0xb1301c: stur            w3, [x2, #0x1b]
    // 0xb13020: StoreField: r2->field_1f = r3
    //     0xb13020: stur            w3, [x2, #0x1f]
    // 0xb13024: StoreField: r2->field_23 = r3
    //     0xb13024: stur            w3, [x2, #0x23]
    // 0xb13028: StoreField: r2->field_27 = r3
    //     0xb13028: stur            w3, [x2, #0x27]
    // 0xb1302c: StoreField: r2->field_2b = r3
    //     0xb1302c: stur            w3, [x2, #0x2b]
    // 0xb13030: ldur            x4, [fp, #-0x30]
    // 0xb13034: LoadField: r1 = r4->field_b
    //     0xb13034: ldur            w1, [x4, #0xb]
    // 0xb13038: LoadField: r5 = r4->field_f
    //     0xb13038: ldur            w5, [x4, #0xf]
    // 0xb1303c: DecompressPointer r5
    //     0xb1303c: add             x5, x5, HEAP, lsl #32
    // 0xb13040: LoadField: r6 = r5->field_b
    //     0xb13040: ldur            w6, [x5, #0xb]
    // 0xb13044: r5 = LoadInt32Instr(r1)
    //     0xb13044: sbfx            x5, x1, #1, #0x1f
    // 0xb13048: stur            x5, [fp, #-0x40]
    // 0xb1304c: r1 = LoadInt32Instr(r6)
    //     0xb1304c: sbfx            x1, x6, #1, #0x1f
    // 0xb13050: cmp             x5, x1
    // 0xb13054: b.ne            #0xb13060
    // 0xb13058: mov             x1, x4
    // 0xb1305c: r0 = _growToNextCapacity()
    //     0xb1305c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb13060: ldur            x4, [fp, #-8]
    // 0xb13064: ldur            x5, [fp, #-0x18]
    // 0xb13068: ldur            x2, [fp, #-0x30]
    // 0xb1306c: ldur            x3, [fp, #-0x40]
    // 0xb13070: add             x0, x3, #1
    // 0xb13074: lsl             x1, x0, #1
    // 0xb13078: StoreField: r2->field_b = r1
    //     0xb13078: stur            w1, [x2, #0xb]
    // 0xb1307c: LoadField: r1 = r2->field_f
    //     0xb1307c: ldur            w1, [x2, #0xf]
    // 0xb13080: DecompressPointer r1
    //     0xb13080: add             x1, x1, HEAP, lsl #32
    // 0xb13084: ldur            x0, [fp, #-0x48]
    // 0xb13088: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb13088: add             x25, x1, x3, lsl #2
    //     0xb1308c: add             x25, x25, #0xf
    //     0xb13090: str             w0, [x25]
    //     0xb13094: tbz             w0, #0, #0xb130b0
    //     0xb13098: ldurb           w16, [x1, #-1]
    //     0xb1309c: ldurb           w17, [x0, #-1]
    //     0xb130a0: and             x16, x17, x16, lsr #2
    //     0xb130a4: tst             x16, HEAP, lsr #32
    //     0xb130a8: b.eq            #0xb130b0
    //     0xb130ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb130b0: r0 = Column()
    //     0xb130b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb130b4: mov             x4, x0
    // 0xb130b8: r0 = Instance_Axis
    //     0xb130b8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb130bc: stur            x4, [fp, #-0x28]
    // 0xb130c0: StoreField: r4->field_f = r0
    //     0xb130c0: stur            w0, [x4, #0xf]
    // 0xb130c4: r6 = Instance_MainAxisAlignment
    //     0xb130c4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb130c8: ldr             x6, [x6, #0xa08]
    // 0xb130cc: StoreField: r4->field_13 = r6
    //     0xb130cc: stur            w6, [x4, #0x13]
    // 0xb130d0: r7 = Instance_MainAxisSize
    //     0xb130d0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb130d4: ldr             x7, [x7, #0xa10]
    // 0xb130d8: ArrayStore: r4[0] = r7  ; List_4
    //     0xb130d8: stur            w7, [x4, #0x17]
    // 0xb130dc: r8 = Instance_CrossAxisAlignment
    //     0xb130dc: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb130e0: ldr             x8, [x8, #0x890]
    // 0xb130e4: StoreField: r4->field_1b = r8
    //     0xb130e4: stur            w8, [x4, #0x1b]
    // 0xb130e8: r9 = Instance_VerticalDirection
    //     0xb130e8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb130ec: ldr             x9, [x9, #0xa20]
    // 0xb130f0: StoreField: r4->field_23 = r9
    //     0xb130f0: stur            w9, [x4, #0x23]
    // 0xb130f4: r10 = Instance_Clip
    //     0xb130f4: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb130f8: ldr             x10, [x10, #0x38]
    // 0xb130fc: StoreField: r4->field_2b = r10
    //     0xb130fc: stur            w10, [x4, #0x2b]
    // 0xb13100: StoreField: r4->field_2f = rZR
    //     0xb13100: stur            xzr, [x4, #0x2f]
    // 0xb13104: ldur            x1, [fp, #-0x30]
    // 0xb13108: StoreField: r4->field_b = r1
    //     0xb13108: stur            w1, [x4, #0xb]
    // 0xb1310c: ldur            x11, [fp, #-0x18]
    // 0xb13110: LoadField: r2 = r11->field_13
    //     0xb13110: ldur            w2, [x11, #0x13]
    // 0xb13114: DecompressPointer r2
    //     0xb13114: add             x2, x2, HEAP, lsl #32
    // 0xb13118: ldur            x12, [fp, #-8]
    // 0xb1311c: LoadField: r1 = r12->field_b
    //     0xb1311c: ldur            w1, [x12, #0xb]
    // 0xb13120: DecompressPointer r1
    //     0xb13120: add             x1, x1, HEAP, lsl #32
    // 0xb13124: cmp             w1, NULL
    // 0xb13128: b.eq            #0xb14e6c
    // 0xb1312c: LoadField: r3 = r1->field_f
    //     0xb1312c: ldur            w3, [x1, #0xf]
    // 0xb13130: DecompressPointer r3
    //     0xb13130: add             x3, x3, HEAP, lsl #32
    // 0xb13134: LoadField: r1 = r3->field_b
    //     0xb13134: ldur            w1, [x3, #0xb]
    // 0xb13138: DecompressPointer r1
    //     0xb13138: add             x1, x1, HEAP, lsl #32
    // 0xb1313c: cmp             w1, NULL
    // 0xb13140: b.ne            #0xb1314c
    // 0xb13144: r3 = Null
    //     0xb13144: mov             x3, NULL
    // 0xb13148: b               #0xb13184
    // 0xb1314c: LoadField: r3 = r1->field_f
    //     0xb1314c: ldur            w3, [x1, #0xf]
    // 0xb13150: DecompressPointer r3
    //     0xb13150: add             x3, x3, HEAP, lsl #32
    // 0xb13154: cmp             w3, NULL
    // 0xb13158: b.ne            #0xb13164
    // 0xb1315c: r3 = Null
    //     0xb1315c: mov             x3, NULL
    // 0xb13160: b               #0xb13184
    // 0xb13164: LoadField: r5 = r3->field_1b
    //     0xb13164: ldur            w5, [x3, #0x1b]
    // 0xb13168: DecompressPointer r5
    //     0xb13168: add             x5, x5, HEAP, lsl #32
    // 0xb1316c: cmp             w5, NULL
    // 0xb13170: b.ne            #0xb1317c
    // 0xb13174: r3 = Null
    //     0xb13174: mov             x3, NULL
    // 0xb13178: b               #0xb13184
    // 0xb1317c: LoadField: r3 = r5->field_7
    //     0xb1317c: ldur            w3, [x5, #7]
    // 0xb13180: DecompressPointer r3
    //     0xb13180: add             x3, x3, HEAP, lsl #32
    // 0xb13184: cmp             w3, NULL
    // 0xb13188: b.ne            #0xb13194
    // 0xb1318c: r3 = 0
    //     0xb1318c: movz            x3, #0
    // 0xb13190: b               #0xb131a4
    // 0xb13194: r5 = LoadInt32Instr(r3)
    //     0xb13194: sbfx            x5, x3, #1, #0x1f
    //     0xb13198: tbz             w3, #0, #0xb131a0
    //     0xb1319c: ldur            x5, [x3, #7]
    // 0xb131a0: mov             x3, x5
    // 0xb131a4: cmp             w1, NULL
    // 0xb131a8: b.ne            #0xb131b4
    // 0xb131ac: r5 = Null
    //     0xb131ac: mov             x5, NULL
    // 0xb131b0: b               #0xb131ec
    // 0xb131b4: LoadField: r5 = r1->field_f
    //     0xb131b4: ldur            w5, [x1, #0xf]
    // 0xb131b8: DecompressPointer r5
    //     0xb131b8: add             x5, x5, HEAP, lsl #32
    // 0xb131bc: cmp             w5, NULL
    // 0xb131c0: b.ne            #0xb131cc
    // 0xb131c4: r5 = Null
    //     0xb131c4: mov             x5, NULL
    // 0xb131c8: b               #0xb131ec
    // 0xb131cc: LoadField: r13 = r5->field_1b
    //     0xb131cc: ldur            w13, [x5, #0x1b]
    // 0xb131d0: DecompressPointer r13
    //     0xb131d0: add             x13, x13, HEAP, lsl #32
    // 0xb131d4: cmp             w13, NULL
    // 0xb131d8: b.ne            #0xb131e4
    // 0xb131dc: r5 = Null
    //     0xb131dc: mov             x5, NULL
    // 0xb131e0: b               #0xb131ec
    // 0xb131e4: LoadField: r5 = r13->field_7
    //     0xb131e4: ldur            w5, [x13, #7]
    // 0xb131e8: DecompressPointer r5
    //     0xb131e8: add             x5, x5, HEAP, lsl #32
    // 0xb131ec: cmp             w5, NULL
    // 0xb131f0: b.ne            #0xb131fc
    // 0xb131f4: r5 = 0
    //     0xb131f4: movz            x5, #0
    // 0xb131f8: b               #0xb1320c
    // 0xb131fc: r13 = LoadInt32Instr(r5)
    //     0xb131fc: sbfx            x13, x5, #1, #0x1f
    //     0xb13200: tbz             w5, #0, #0xb13208
    //     0xb13204: ldur            x13, [x5, #7]
    // 0xb13208: mov             x5, x13
    // 0xb1320c: cmp             w1, NULL
    // 0xb13210: b.ne            #0xb1321c
    // 0xb13214: r13 = Null
    //     0xb13214: mov             x13, NULL
    // 0xb13218: b               #0xb13254
    // 0xb1321c: LoadField: r13 = r1->field_f
    //     0xb1321c: ldur            w13, [x1, #0xf]
    // 0xb13220: DecompressPointer r13
    //     0xb13220: add             x13, x13, HEAP, lsl #32
    // 0xb13224: cmp             w13, NULL
    // 0xb13228: b.ne            #0xb13234
    // 0xb1322c: r13 = Null
    //     0xb1322c: mov             x13, NULL
    // 0xb13230: b               #0xb13254
    // 0xb13234: LoadField: r14 = r13->field_1b
    //     0xb13234: ldur            w14, [x13, #0x1b]
    // 0xb13238: DecompressPointer r14
    //     0xb13238: add             x14, x14, HEAP, lsl #32
    // 0xb1323c: cmp             w14, NULL
    // 0xb13240: b.ne            #0xb1324c
    // 0xb13244: r13 = Null
    //     0xb13244: mov             x13, NULL
    // 0xb13248: b               #0xb13254
    // 0xb1324c: LoadField: r13 = r14->field_b
    //     0xb1324c: ldur            w13, [x14, #0xb]
    // 0xb13250: DecompressPointer r13
    //     0xb13250: add             x13, x13, HEAP, lsl #32
    // 0xb13254: cmp             w13, NULL
    // 0xb13258: b.ne            #0xb13264
    // 0xb1325c: r13 = 0
    //     0xb1325c: movz            x13, #0
    // 0xb13260: b               #0xb13274
    // 0xb13264: r14 = LoadInt32Instr(r13)
    //     0xb13264: sbfx            x14, x13, #1, #0x1f
    //     0xb13268: tbz             w13, #0, #0xb13270
    //     0xb1326c: ldur            x14, [x13, #7]
    // 0xb13270: mov             x13, x14
    // 0xb13274: add             x14, x5, x13
    // 0xb13278: cmp             w1, NULL
    // 0xb1327c: b.ne            #0xb13288
    // 0xb13280: r5 = Null
    //     0xb13280: mov             x5, NULL
    // 0xb13284: b               #0xb132c0
    // 0xb13288: LoadField: r5 = r1->field_f
    //     0xb13288: ldur            w5, [x1, #0xf]
    // 0xb1328c: DecompressPointer r5
    //     0xb1328c: add             x5, x5, HEAP, lsl #32
    // 0xb13290: cmp             w5, NULL
    // 0xb13294: b.ne            #0xb132a0
    // 0xb13298: r5 = Null
    //     0xb13298: mov             x5, NULL
    // 0xb1329c: b               #0xb132c0
    // 0xb132a0: LoadField: r13 = r5->field_1b
    //     0xb132a0: ldur            w13, [x5, #0x1b]
    // 0xb132a4: DecompressPointer r13
    //     0xb132a4: add             x13, x13, HEAP, lsl #32
    // 0xb132a8: cmp             w13, NULL
    // 0xb132ac: b.ne            #0xb132b8
    // 0xb132b0: r5 = Null
    //     0xb132b0: mov             x5, NULL
    // 0xb132b4: b               #0xb132c0
    // 0xb132b8: LoadField: r5 = r13->field_f
    //     0xb132b8: ldur            w5, [x13, #0xf]
    // 0xb132bc: DecompressPointer r5
    //     0xb132bc: add             x5, x5, HEAP, lsl #32
    // 0xb132c0: cmp             w5, NULL
    // 0xb132c4: b.ne            #0xb132d0
    // 0xb132c8: r5 = 0
    //     0xb132c8: movz            x5, #0
    // 0xb132cc: b               #0xb132e0
    // 0xb132d0: r13 = LoadInt32Instr(r5)
    //     0xb132d0: sbfx            x13, x5, #1, #0x1f
    //     0xb132d4: tbz             w5, #0, #0xb132dc
    //     0xb132d8: ldur            x13, [x5, #7]
    // 0xb132dc: mov             x5, x13
    // 0xb132e0: add             x13, x14, x5
    // 0xb132e4: cmp             w1, NULL
    // 0xb132e8: b.ne            #0xb132f4
    // 0xb132ec: r5 = Null
    //     0xb132ec: mov             x5, NULL
    // 0xb132f0: b               #0xb1332c
    // 0xb132f4: LoadField: r5 = r1->field_f
    //     0xb132f4: ldur            w5, [x1, #0xf]
    // 0xb132f8: DecompressPointer r5
    //     0xb132f8: add             x5, x5, HEAP, lsl #32
    // 0xb132fc: cmp             w5, NULL
    // 0xb13300: b.ne            #0xb1330c
    // 0xb13304: r5 = Null
    //     0xb13304: mov             x5, NULL
    // 0xb13308: b               #0xb1332c
    // 0xb1330c: LoadField: r14 = r5->field_1b
    //     0xb1330c: ldur            w14, [x5, #0x1b]
    // 0xb13310: DecompressPointer r14
    //     0xb13310: add             x14, x14, HEAP, lsl #32
    // 0xb13314: cmp             w14, NULL
    // 0xb13318: b.ne            #0xb13324
    // 0xb1331c: r5 = Null
    //     0xb1331c: mov             x5, NULL
    // 0xb13320: b               #0xb1332c
    // 0xb13324: LoadField: r5 = r14->field_13
    //     0xb13324: ldur            w5, [x14, #0x13]
    // 0xb13328: DecompressPointer r5
    //     0xb13328: add             x5, x5, HEAP, lsl #32
    // 0xb1332c: cmp             w5, NULL
    // 0xb13330: b.ne            #0xb1333c
    // 0xb13334: r5 = 0
    //     0xb13334: movz            x5, #0
    // 0xb13338: b               #0xb1334c
    // 0xb1333c: r14 = LoadInt32Instr(r5)
    //     0xb1333c: sbfx            x14, x5, #1, #0x1f
    //     0xb13340: tbz             w5, #0, #0xb13348
    //     0xb13344: ldur            x14, [x5, #7]
    // 0xb13348: mov             x5, x14
    // 0xb1334c: add             x14, x13, x5
    // 0xb13350: cmp             w1, NULL
    // 0xb13354: b.ne            #0xb13360
    // 0xb13358: r5 = Null
    //     0xb13358: mov             x5, NULL
    // 0xb1335c: b               #0xb13398
    // 0xb13360: LoadField: r5 = r1->field_f
    //     0xb13360: ldur            w5, [x1, #0xf]
    // 0xb13364: DecompressPointer r5
    //     0xb13364: add             x5, x5, HEAP, lsl #32
    // 0xb13368: cmp             w5, NULL
    // 0xb1336c: b.ne            #0xb13378
    // 0xb13370: r5 = Null
    //     0xb13370: mov             x5, NULL
    // 0xb13374: b               #0xb13398
    // 0xb13378: LoadField: r13 = r5->field_1b
    //     0xb13378: ldur            w13, [x5, #0x1b]
    // 0xb1337c: DecompressPointer r13
    //     0xb1337c: add             x13, x13, HEAP, lsl #32
    // 0xb13380: cmp             w13, NULL
    // 0xb13384: b.ne            #0xb13390
    // 0xb13388: r5 = Null
    //     0xb13388: mov             x5, NULL
    // 0xb1338c: b               #0xb13398
    // 0xb13390: ArrayLoad: r5 = r13[0]  ; List_4
    //     0xb13390: ldur            w5, [x13, #0x17]
    // 0xb13394: DecompressPointer r5
    //     0xb13394: add             x5, x5, HEAP, lsl #32
    // 0xb13398: cmp             w5, NULL
    // 0xb1339c: b.ne            #0xb133a8
    // 0xb133a0: r5 = 0
    //     0xb133a0: movz            x5, #0
    // 0xb133a4: b               #0xb133b8
    // 0xb133a8: r13 = LoadInt32Instr(r5)
    //     0xb133a8: sbfx            x13, x5, #1, #0x1f
    //     0xb133ac: tbz             w5, #0, #0xb133b4
    //     0xb133b0: ldur            x13, [x5, #7]
    // 0xb133b4: mov             x5, x13
    // 0xb133b8: add             x13, x14, x5
    // 0xb133bc: scvtf           d0, x3
    // 0xb133c0: scvtf           d1, x13
    // 0xb133c4: fdiv            d2, d0, d1
    // 0xb133c8: cmp             w1, NULL
    // 0xb133cc: b.ne            #0xb133d8
    // 0xb133d0: r1 = Null
    //     0xb133d0: mov             x1, NULL
    // 0xb133d4: b               #0xb13414
    // 0xb133d8: LoadField: r3 = r1->field_f
    //     0xb133d8: ldur            w3, [x1, #0xf]
    // 0xb133dc: DecompressPointer r3
    //     0xb133dc: add             x3, x3, HEAP, lsl #32
    // 0xb133e0: cmp             w3, NULL
    // 0xb133e4: b.ne            #0xb133f0
    // 0xb133e8: r1 = Null
    //     0xb133e8: mov             x1, NULL
    // 0xb133ec: b               #0xb13414
    // 0xb133f0: LoadField: r1 = r3->field_1b
    //     0xb133f0: ldur            w1, [x3, #0x1b]
    // 0xb133f4: DecompressPointer r1
    //     0xb133f4: add             x1, x1, HEAP, lsl #32
    // 0xb133f8: cmp             w1, NULL
    // 0xb133fc: b.ne            #0xb13408
    // 0xb13400: r1 = Null
    //     0xb13400: mov             x1, NULL
    // 0xb13404: b               #0xb13414
    // 0xb13408: LoadField: r3 = r1->field_7
    //     0xb13408: ldur            w3, [x1, #7]
    // 0xb1340c: DecompressPointer r3
    //     0xb1340c: add             x3, x3, HEAP, lsl #32
    // 0xb13410: mov             x1, x3
    // 0xb13414: cmp             w1, NULL
    // 0xb13418: b.ne            #0xb13424
    // 0xb1341c: r5 = 0
    //     0xb1341c: movz            x5, #0
    // 0xb13420: b               #0xb13434
    // 0xb13424: r3 = LoadInt32Instr(r1)
    //     0xb13424: sbfx            x3, x1, #1, #0x1f
    //     0xb13428: tbz             w1, #0, #0xb13430
    //     0xb1342c: ldur            x3, [x1, #7]
    // 0xb13430: mov             x5, x3
    // 0xb13434: mov             x1, x12
    // 0xb13438: mov             v0.16b, v2.16b
    // 0xb1343c: r3 = "5"
    //     0xb1343c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f898] "5"
    //     0xb13440: ldr             x3, [x3, #0x898]
    // 0xb13444: r0 = chartRow()
    //     0xb13444: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb13448: mov             x4, x0
    // 0xb1344c: ldur            x0, [fp, #-0x18]
    // 0xb13450: stur            x4, [fp, #-0x30]
    // 0xb13454: LoadField: r2 = r0->field_13
    //     0xb13454: ldur            w2, [x0, #0x13]
    // 0xb13458: DecompressPointer r2
    //     0xb13458: add             x2, x2, HEAP, lsl #32
    // 0xb1345c: ldur            x6, [fp, #-8]
    // 0xb13460: LoadField: r1 = r6->field_b
    //     0xb13460: ldur            w1, [x6, #0xb]
    // 0xb13464: DecompressPointer r1
    //     0xb13464: add             x1, x1, HEAP, lsl #32
    // 0xb13468: cmp             w1, NULL
    // 0xb1346c: b.eq            #0xb14e70
    // 0xb13470: LoadField: r3 = r1->field_f
    //     0xb13470: ldur            w3, [x1, #0xf]
    // 0xb13474: DecompressPointer r3
    //     0xb13474: add             x3, x3, HEAP, lsl #32
    // 0xb13478: LoadField: r1 = r3->field_b
    //     0xb13478: ldur            w1, [x3, #0xb]
    // 0xb1347c: DecompressPointer r1
    //     0xb1347c: add             x1, x1, HEAP, lsl #32
    // 0xb13480: cmp             w1, NULL
    // 0xb13484: b.ne            #0xb13490
    // 0xb13488: r3 = Null
    //     0xb13488: mov             x3, NULL
    // 0xb1348c: b               #0xb134c8
    // 0xb13490: LoadField: r3 = r1->field_f
    //     0xb13490: ldur            w3, [x1, #0xf]
    // 0xb13494: DecompressPointer r3
    //     0xb13494: add             x3, x3, HEAP, lsl #32
    // 0xb13498: cmp             w3, NULL
    // 0xb1349c: b.ne            #0xb134a8
    // 0xb134a0: r3 = Null
    //     0xb134a0: mov             x3, NULL
    // 0xb134a4: b               #0xb134c8
    // 0xb134a8: LoadField: r5 = r3->field_1b
    //     0xb134a8: ldur            w5, [x3, #0x1b]
    // 0xb134ac: DecompressPointer r5
    //     0xb134ac: add             x5, x5, HEAP, lsl #32
    // 0xb134b0: cmp             w5, NULL
    // 0xb134b4: b.ne            #0xb134c0
    // 0xb134b8: r3 = Null
    //     0xb134b8: mov             x3, NULL
    // 0xb134bc: b               #0xb134c8
    // 0xb134c0: LoadField: r3 = r5->field_b
    //     0xb134c0: ldur            w3, [x5, #0xb]
    // 0xb134c4: DecompressPointer r3
    //     0xb134c4: add             x3, x3, HEAP, lsl #32
    // 0xb134c8: cmp             w3, NULL
    // 0xb134cc: b.ne            #0xb134d8
    // 0xb134d0: r3 = 0
    //     0xb134d0: movz            x3, #0
    // 0xb134d4: b               #0xb134e8
    // 0xb134d8: r5 = LoadInt32Instr(r3)
    //     0xb134d8: sbfx            x5, x3, #1, #0x1f
    //     0xb134dc: tbz             w3, #0, #0xb134e4
    //     0xb134e0: ldur            x5, [x3, #7]
    // 0xb134e4: mov             x3, x5
    // 0xb134e8: cmp             w1, NULL
    // 0xb134ec: b.ne            #0xb134f8
    // 0xb134f0: r5 = Null
    //     0xb134f0: mov             x5, NULL
    // 0xb134f4: b               #0xb13530
    // 0xb134f8: LoadField: r5 = r1->field_f
    //     0xb134f8: ldur            w5, [x1, #0xf]
    // 0xb134fc: DecompressPointer r5
    //     0xb134fc: add             x5, x5, HEAP, lsl #32
    // 0xb13500: cmp             w5, NULL
    // 0xb13504: b.ne            #0xb13510
    // 0xb13508: r5 = Null
    //     0xb13508: mov             x5, NULL
    // 0xb1350c: b               #0xb13530
    // 0xb13510: LoadField: r7 = r5->field_1b
    //     0xb13510: ldur            w7, [x5, #0x1b]
    // 0xb13514: DecompressPointer r7
    //     0xb13514: add             x7, x7, HEAP, lsl #32
    // 0xb13518: cmp             w7, NULL
    // 0xb1351c: b.ne            #0xb13528
    // 0xb13520: r5 = Null
    //     0xb13520: mov             x5, NULL
    // 0xb13524: b               #0xb13530
    // 0xb13528: LoadField: r5 = r7->field_7
    //     0xb13528: ldur            w5, [x7, #7]
    // 0xb1352c: DecompressPointer r5
    //     0xb1352c: add             x5, x5, HEAP, lsl #32
    // 0xb13530: cmp             w5, NULL
    // 0xb13534: b.ne            #0xb13540
    // 0xb13538: r5 = 0
    //     0xb13538: movz            x5, #0
    // 0xb1353c: b               #0xb13550
    // 0xb13540: r7 = LoadInt32Instr(r5)
    //     0xb13540: sbfx            x7, x5, #1, #0x1f
    //     0xb13544: tbz             w5, #0, #0xb1354c
    //     0xb13548: ldur            x7, [x5, #7]
    // 0xb1354c: mov             x5, x7
    // 0xb13550: cmp             w1, NULL
    // 0xb13554: b.ne            #0xb13560
    // 0xb13558: r7 = Null
    //     0xb13558: mov             x7, NULL
    // 0xb1355c: b               #0xb13598
    // 0xb13560: LoadField: r7 = r1->field_f
    //     0xb13560: ldur            w7, [x1, #0xf]
    // 0xb13564: DecompressPointer r7
    //     0xb13564: add             x7, x7, HEAP, lsl #32
    // 0xb13568: cmp             w7, NULL
    // 0xb1356c: b.ne            #0xb13578
    // 0xb13570: r7 = Null
    //     0xb13570: mov             x7, NULL
    // 0xb13574: b               #0xb13598
    // 0xb13578: LoadField: r8 = r7->field_1b
    //     0xb13578: ldur            w8, [x7, #0x1b]
    // 0xb1357c: DecompressPointer r8
    //     0xb1357c: add             x8, x8, HEAP, lsl #32
    // 0xb13580: cmp             w8, NULL
    // 0xb13584: b.ne            #0xb13590
    // 0xb13588: r7 = Null
    //     0xb13588: mov             x7, NULL
    // 0xb1358c: b               #0xb13598
    // 0xb13590: LoadField: r7 = r8->field_b
    //     0xb13590: ldur            w7, [x8, #0xb]
    // 0xb13594: DecompressPointer r7
    //     0xb13594: add             x7, x7, HEAP, lsl #32
    // 0xb13598: cmp             w7, NULL
    // 0xb1359c: b.ne            #0xb135a8
    // 0xb135a0: r7 = 0
    //     0xb135a0: movz            x7, #0
    // 0xb135a4: b               #0xb135b8
    // 0xb135a8: r8 = LoadInt32Instr(r7)
    //     0xb135a8: sbfx            x8, x7, #1, #0x1f
    //     0xb135ac: tbz             w7, #0, #0xb135b4
    //     0xb135b0: ldur            x8, [x7, #7]
    // 0xb135b4: mov             x7, x8
    // 0xb135b8: add             x8, x5, x7
    // 0xb135bc: cmp             w1, NULL
    // 0xb135c0: b.ne            #0xb135cc
    // 0xb135c4: r5 = Null
    //     0xb135c4: mov             x5, NULL
    // 0xb135c8: b               #0xb13604
    // 0xb135cc: LoadField: r5 = r1->field_f
    //     0xb135cc: ldur            w5, [x1, #0xf]
    // 0xb135d0: DecompressPointer r5
    //     0xb135d0: add             x5, x5, HEAP, lsl #32
    // 0xb135d4: cmp             w5, NULL
    // 0xb135d8: b.ne            #0xb135e4
    // 0xb135dc: r5 = Null
    //     0xb135dc: mov             x5, NULL
    // 0xb135e0: b               #0xb13604
    // 0xb135e4: LoadField: r7 = r5->field_1b
    //     0xb135e4: ldur            w7, [x5, #0x1b]
    // 0xb135e8: DecompressPointer r7
    //     0xb135e8: add             x7, x7, HEAP, lsl #32
    // 0xb135ec: cmp             w7, NULL
    // 0xb135f0: b.ne            #0xb135fc
    // 0xb135f4: r5 = Null
    //     0xb135f4: mov             x5, NULL
    // 0xb135f8: b               #0xb13604
    // 0xb135fc: LoadField: r5 = r7->field_f
    //     0xb135fc: ldur            w5, [x7, #0xf]
    // 0xb13600: DecompressPointer r5
    //     0xb13600: add             x5, x5, HEAP, lsl #32
    // 0xb13604: cmp             w5, NULL
    // 0xb13608: b.ne            #0xb13614
    // 0xb1360c: r5 = 0
    //     0xb1360c: movz            x5, #0
    // 0xb13610: b               #0xb13624
    // 0xb13614: r7 = LoadInt32Instr(r5)
    //     0xb13614: sbfx            x7, x5, #1, #0x1f
    //     0xb13618: tbz             w5, #0, #0xb13620
    //     0xb1361c: ldur            x7, [x5, #7]
    // 0xb13620: mov             x5, x7
    // 0xb13624: add             x7, x8, x5
    // 0xb13628: cmp             w1, NULL
    // 0xb1362c: b.ne            #0xb13638
    // 0xb13630: r5 = Null
    //     0xb13630: mov             x5, NULL
    // 0xb13634: b               #0xb13670
    // 0xb13638: LoadField: r5 = r1->field_f
    //     0xb13638: ldur            w5, [x1, #0xf]
    // 0xb1363c: DecompressPointer r5
    //     0xb1363c: add             x5, x5, HEAP, lsl #32
    // 0xb13640: cmp             w5, NULL
    // 0xb13644: b.ne            #0xb13650
    // 0xb13648: r5 = Null
    //     0xb13648: mov             x5, NULL
    // 0xb1364c: b               #0xb13670
    // 0xb13650: LoadField: r8 = r5->field_1b
    //     0xb13650: ldur            w8, [x5, #0x1b]
    // 0xb13654: DecompressPointer r8
    //     0xb13654: add             x8, x8, HEAP, lsl #32
    // 0xb13658: cmp             w8, NULL
    // 0xb1365c: b.ne            #0xb13668
    // 0xb13660: r5 = Null
    //     0xb13660: mov             x5, NULL
    // 0xb13664: b               #0xb13670
    // 0xb13668: LoadField: r5 = r8->field_13
    //     0xb13668: ldur            w5, [x8, #0x13]
    // 0xb1366c: DecompressPointer r5
    //     0xb1366c: add             x5, x5, HEAP, lsl #32
    // 0xb13670: cmp             w5, NULL
    // 0xb13674: b.ne            #0xb13680
    // 0xb13678: r5 = 0
    //     0xb13678: movz            x5, #0
    // 0xb1367c: b               #0xb13690
    // 0xb13680: r8 = LoadInt32Instr(r5)
    //     0xb13680: sbfx            x8, x5, #1, #0x1f
    //     0xb13684: tbz             w5, #0, #0xb1368c
    //     0xb13688: ldur            x8, [x5, #7]
    // 0xb1368c: mov             x5, x8
    // 0xb13690: add             x8, x7, x5
    // 0xb13694: cmp             w1, NULL
    // 0xb13698: b.ne            #0xb136a4
    // 0xb1369c: r5 = Null
    //     0xb1369c: mov             x5, NULL
    // 0xb136a0: b               #0xb136dc
    // 0xb136a4: LoadField: r5 = r1->field_f
    //     0xb136a4: ldur            w5, [x1, #0xf]
    // 0xb136a8: DecompressPointer r5
    //     0xb136a8: add             x5, x5, HEAP, lsl #32
    // 0xb136ac: cmp             w5, NULL
    // 0xb136b0: b.ne            #0xb136bc
    // 0xb136b4: r5 = Null
    //     0xb136b4: mov             x5, NULL
    // 0xb136b8: b               #0xb136dc
    // 0xb136bc: LoadField: r7 = r5->field_1b
    //     0xb136bc: ldur            w7, [x5, #0x1b]
    // 0xb136c0: DecompressPointer r7
    //     0xb136c0: add             x7, x7, HEAP, lsl #32
    // 0xb136c4: cmp             w7, NULL
    // 0xb136c8: b.ne            #0xb136d4
    // 0xb136cc: r5 = Null
    //     0xb136cc: mov             x5, NULL
    // 0xb136d0: b               #0xb136dc
    // 0xb136d4: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb136d4: ldur            w5, [x7, #0x17]
    // 0xb136d8: DecompressPointer r5
    //     0xb136d8: add             x5, x5, HEAP, lsl #32
    // 0xb136dc: cmp             w5, NULL
    // 0xb136e0: b.ne            #0xb136ec
    // 0xb136e4: r5 = 0
    //     0xb136e4: movz            x5, #0
    // 0xb136e8: b               #0xb136fc
    // 0xb136ec: r7 = LoadInt32Instr(r5)
    //     0xb136ec: sbfx            x7, x5, #1, #0x1f
    //     0xb136f0: tbz             w5, #0, #0xb136f8
    //     0xb136f4: ldur            x7, [x5, #7]
    // 0xb136f8: mov             x5, x7
    // 0xb136fc: add             x7, x8, x5
    // 0xb13700: scvtf           d0, x3
    // 0xb13704: scvtf           d1, x7
    // 0xb13708: fdiv            d2, d0, d1
    // 0xb1370c: cmp             w1, NULL
    // 0xb13710: b.ne            #0xb1371c
    // 0xb13714: r1 = Null
    //     0xb13714: mov             x1, NULL
    // 0xb13718: b               #0xb13758
    // 0xb1371c: LoadField: r3 = r1->field_f
    //     0xb1371c: ldur            w3, [x1, #0xf]
    // 0xb13720: DecompressPointer r3
    //     0xb13720: add             x3, x3, HEAP, lsl #32
    // 0xb13724: cmp             w3, NULL
    // 0xb13728: b.ne            #0xb13734
    // 0xb1372c: r1 = Null
    //     0xb1372c: mov             x1, NULL
    // 0xb13730: b               #0xb13758
    // 0xb13734: LoadField: r1 = r3->field_1b
    //     0xb13734: ldur            w1, [x3, #0x1b]
    // 0xb13738: DecompressPointer r1
    //     0xb13738: add             x1, x1, HEAP, lsl #32
    // 0xb1373c: cmp             w1, NULL
    // 0xb13740: b.ne            #0xb1374c
    // 0xb13744: r1 = Null
    //     0xb13744: mov             x1, NULL
    // 0xb13748: b               #0xb13758
    // 0xb1374c: LoadField: r3 = r1->field_b
    //     0xb1374c: ldur            w3, [x1, #0xb]
    // 0xb13750: DecompressPointer r3
    //     0xb13750: add             x3, x3, HEAP, lsl #32
    // 0xb13754: mov             x1, x3
    // 0xb13758: cmp             w1, NULL
    // 0xb1375c: b.ne            #0xb13768
    // 0xb13760: r5 = 0
    //     0xb13760: movz            x5, #0
    // 0xb13764: b               #0xb13778
    // 0xb13768: r3 = LoadInt32Instr(r1)
    //     0xb13768: sbfx            x3, x1, #1, #0x1f
    //     0xb1376c: tbz             w1, #0, #0xb13774
    //     0xb13770: ldur            x3, [x1, #7]
    // 0xb13774: mov             x5, x3
    // 0xb13778: mov             x1, x6
    // 0xb1377c: mov             v0.16b, v2.16b
    // 0xb13780: r3 = "4"
    //     0xb13780: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] "4"
    //     0xb13784: ldr             x3, [x3, #0x8a0]
    // 0xb13788: r0 = chartRow()
    //     0xb13788: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb1378c: mov             x4, x0
    // 0xb13790: ldur            x0, [fp, #-0x18]
    // 0xb13794: stur            x4, [fp, #-0x38]
    // 0xb13798: LoadField: r2 = r0->field_13
    //     0xb13798: ldur            w2, [x0, #0x13]
    // 0xb1379c: DecompressPointer r2
    //     0xb1379c: add             x2, x2, HEAP, lsl #32
    // 0xb137a0: ldur            x6, [fp, #-8]
    // 0xb137a4: LoadField: r1 = r6->field_b
    //     0xb137a4: ldur            w1, [x6, #0xb]
    // 0xb137a8: DecompressPointer r1
    //     0xb137a8: add             x1, x1, HEAP, lsl #32
    // 0xb137ac: cmp             w1, NULL
    // 0xb137b0: b.eq            #0xb14e74
    // 0xb137b4: LoadField: r3 = r1->field_f
    //     0xb137b4: ldur            w3, [x1, #0xf]
    // 0xb137b8: DecompressPointer r3
    //     0xb137b8: add             x3, x3, HEAP, lsl #32
    // 0xb137bc: LoadField: r1 = r3->field_b
    //     0xb137bc: ldur            w1, [x3, #0xb]
    // 0xb137c0: DecompressPointer r1
    //     0xb137c0: add             x1, x1, HEAP, lsl #32
    // 0xb137c4: cmp             w1, NULL
    // 0xb137c8: b.ne            #0xb137d4
    // 0xb137cc: r3 = Null
    //     0xb137cc: mov             x3, NULL
    // 0xb137d0: b               #0xb1380c
    // 0xb137d4: LoadField: r3 = r1->field_f
    //     0xb137d4: ldur            w3, [x1, #0xf]
    // 0xb137d8: DecompressPointer r3
    //     0xb137d8: add             x3, x3, HEAP, lsl #32
    // 0xb137dc: cmp             w3, NULL
    // 0xb137e0: b.ne            #0xb137ec
    // 0xb137e4: r3 = Null
    //     0xb137e4: mov             x3, NULL
    // 0xb137e8: b               #0xb1380c
    // 0xb137ec: LoadField: r5 = r3->field_1b
    //     0xb137ec: ldur            w5, [x3, #0x1b]
    // 0xb137f0: DecompressPointer r5
    //     0xb137f0: add             x5, x5, HEAP, lsl #32
    // 0xb137f4: cmp             w5, NULL
    // 0xb137f8: b.ne            #0xb13804
    // 0xb137fc: r3 = Null
    //     0xb137fc: mov             x3, NULL
    // 0xb13800: b               #0xb1380c
    // 0xb13804: LoadField: r3 = r5->field_f
    //     0xb13804: ldur            w3, [x5, #0xf]
    // 0xb13808: DecompressPointer r3
    //     0xb13808: add             x3, x3, HEAP, lsl #32
    // 0xb1380c: cmp             w3, NULL
    // 0xb13810: b.ne            #0xb1381c
    // 0xb13814: r3 = 0
    //     0xb13814: movz            x3, #0
    // 0xb13818: b               #0xb1382c
    // 0xb1381c: r5 = LoadInt32Instr(r3)
    //     0xb1381c: sbfx            x5, x3, #1, #0x1f
    //     0xb13820: tbz             w3, #0, #0xb13828
    //     0xb13824: ldur            x5, [x3, #7]
    // 0xb13828: mov             x3, x5
    // 0xb1382c: cmp             w1, NULL
    // 0xb13830: b.ne            #0xb1383c
    // 0xb13834: r5 = Null
    //     0xb13834: mov             x5, NULL
    // 0xb13838: b               #0xb13874
    // 0xb1383c: LoadField: r5 = r1->field_f
    //     0xb1383c: ldur            w5, [x1, #0xf]
    // 0xb13840: DecompressPointer r5
    //     0xb13840: add             x5, x5, HEAP, lsl #32
    // 0xb13844: cmp             w5, NULL
    // 0xb13848: b.ne            #0xb13854
    // 0xb1384c: r5 = Null
    //     0xb1384c: mov             x5, NULL
    // 0xb13850: b               #0xb13874
    // 0xb13854: LoadField: r7 = r5->field_1b
    //     0xb13854: ldur            w7, [x5, #0x1b]
    // 0xb13858: DecompressPointer r7
    //     0xb13858: add             x7, x7, HEAP, lsl #32
    // 0xb1385c: cmp             w7, NULL
    // 0xb13860: b.ne            #0xb1386c
    // 0xb13864: r5 = Null
    //     0xb13864: mov             x5, NULL
    // 0xb13868: b               #0xb13874
    // 0xb1386c: LoadField: r5 = r7->field_7
    //     0xb1386c: ldur            w5, [x7, #7]
    // 0xb13870: DecompressPointer r5
    //     0xb13870: add             x5, x5, HEAP, lsl #32
    // 0xb13874: cmp             w5, NULL
    // 0xb13878: b.ne            #0xb13884
    // 0xb1387c: r5 = 0
    //     0xb1387c: movz            x5, #0
    // 0xb13880: b               #0xb13894
    // 0xb13884: r7 = LoadInt32Instr(r5)
    //     0xb13884: sbfx            x7, x5, #1, #0x1f
    //     0xb13888: tbz             w5, #0, #0xb13890
    //     0xb1388c: ldur            x7, [x5, #7]
    // 0xb13890: mov             x5, x7
    // 0xb13894: cmp             w1, NULL
    // 0xb13898: b.ne            #0xb138a4
    // 0xb1389c: r7 = Null
    //     0xb1389c: mov             x7, NULL
    // 0xb138a0: b               #0xb138dc
    // 0xb138a4: LoadField: r7 = r1->field_f
    //     0xb138a4: ldur            w7, [x1, #0xf]
    // 0xb138a8: DecompressPointer r7
    //     0xb138a8: add             x7, x7, HEAP, lsl #32
    // 0xb138ac: cmp             w7, NULL
    // 0xb138b0: b.ne            #0xb138bc
    // 0xb138b4: r7 = Null
    //     0xb138b4: mov             x7, NULL
    // 0xb138b8: b               #0xb138dc
    // 0xb138bc: LoadField: r8 = r7->field_1b
    //     0xb138bc: ldur            w8, [x7, #0x1b]
    // 0xb138c0: DecompressPointer r8
    //     0xb138c0: add             x8, x8, HEAP, lsl #32
    // 0xb138c4: cmp             w8, NULL
    // 0xb138c8: b.ne            #0xb138d4
    // 0xb138cc: r7 = Null
    //     0xb138cc: mov             x7, NULL
    // 0xb138d0: b               #0xb138dc
    // 0xb138d4: LoadField: r7 = r8->field_b
    //     0xb138d4: ldur            w7, [x8, #0xb]
    // 0xb138d8: DecompressPointer r7
    //     0xb138d8: add             x7, x7, HEAP, lsl #32
    // 0xb138dc: cmp             w7, NULL
    // 0xb138e0: b.ne            #0xb138ec
    // 0xb138e4: r7 = 0
    //     0xb138e4: movz            x7, #0
    // 0xb138e8: b               #0xb138fc
    // 0xb138ec: r8 = LoadInt32Instr(r7)
    //     0xb138ec: sbfx            x8, x7, #1, #0x1f
    //     0xb138f0: tbz             w7, #0, #0xb138f8
    //     0xb138f4: ldur            x8, [x7, #7]
    // 0xb138f8: mov             x7, x8
    // 0xb138fc: add             x8, x5, x7
    // 0xb13900: cmp             w1, NULL
    // 0xb13904: b.ne            #0xb13910
    // 0xb13908: r5 = Null
    //     0xb13908: mov             x5, NULL
    // 0xb1390c: b               #0xb13948
    // 0xb13910: LoadField: r5 = r1->field_f
    //     0xb13910: ldur            w5, [x1, #0xf]
    // 0xb13914: DecompressPointer r5
    //     0xb13914: add             x5, x5, HEAP, lsl #32
    // 0xb13918: cmp             w5, NULL
    // 0xb1391c: b.ne            #0xb13928
    // 0xb13920: r5 = Null
    //     0xb13920: mov             x5, NULL
    // 0xb13924: b               #0xb13948
    // 0xb13928: LoadField: r7 = r5->field_1b
    //     0xb13928: ldur            w7, [x5, #0x1b]
    // 0xb1392c: DecompressPointer r7
    //     0xb1392c: add             x7, x7, HEAP, lsl #32
    // 0xb13930: cmp             w7, NULL
    // 0xb13934: b.ne            #0xb13940
    // 0xb13938: r5 = Null
    //     0xb13938: mov             x5, NULL
    // 0xb1393c: b               #0xb13948
    // 0xb13940: LoadField: r5 = r7->field_f
    //     0xb13940: ldur            w5, [x7, #0xf]
    // 0xb13944: DecompressPointer r5
    //     0xb13944: add             x5, x5, HEAP, lsl #32
    // 0xb13948: cmp             w5, NULL
    // 0xb1394c: b.ne            #0xb13958
    // 0xb13950: r5 = 0
    //     0xb13950: movz            x5, #0
    // 0xb13954: b               #0xb13968
    // 0xb13958: r7 = LoadInt32Instr(r5)
    //     0xb13958: sbfx            x7, x5, #1, #0x1f
    //     0xb1395c: tbz             w5, #0, #0xb13964
    //     0xb13960: ldur            x7, [x5, #7]
    // 0xb13964: mov             x5, x7
    // 0xb13968: add             x7, x8, x5
    // 0xb1396c: cmp             w1, NULL
    // 0xb13970: b.ne            #0xb1397c
    // 0xb13974: r5 = Null
    //     0xb13974: mov             x5, NULL
    // 0xb13978: b               #0xb139b4
    // 0xb1397c: LoadField: r5 = r1->field_f
    //     0xb1397c: ldur            w5, [x1, #0xf]
    // 0xb13980: DecompressPointer r5
    //     0xb13980: add             x5, x5, HEAP, lsl #32
    // 0xb13984: cmp             w5, NULL
    // 0xb13988: b.ne            #0xb13994
    // 0xb1398c: r5 = Null
    //     0xb1398c: mov             x5, NULL
    // 0xb13990: b               #0xb139b4
    // 0xb13994: LoadField: r8 = r5->field_1b
    //     0xb13994: ldur            w8, [x5, #0x1b]
    // 0xb13998: DecompressPointer r8
    //     0xb13998: add             x8, x8, HEAP, lsl #32
    // 0xb1399c: cmp             w8, NULL
    // 0xb139a0: b.ne            #0xb139ac
    // 0xb139a4: r5 = Null
    //     0xb139a4: mov             x5, NULL
    // 0xb139a8: b               #0xb139b4
    // 0xb139ac: LoadField: r5 = r8->field_13
    //     0xb139ac: ldur            w5, [x8, #0x13]
    // 0xb139b0: DecompressPointer r5
    //     0xb139b0: add             x5, x5, HEAP, lsl #32
    // 0xb139b4: cmp             w5, NULL
    // 0xb139b8: b.ne            #0xb139c4
    // 0xb139bc: r5 = 0
    //     0xb139bc: movz            x5, #0
    // 0xb139c0: b               #0xb139d4
    // 0xb139c4: r8 = LoadInt32Instr(r5)
    //     0xb139c4: sbfx            x8, x5, #1, #0x1f
    //     0xb139c8: tbz             w5, #0, #0xb139d0
    //     0xb139cc: ldur            x8, [x5, #7]
    // 0xb139d0: mov             x5, x8
    // 0xb139d4: add             x8, x7, x5
    // 0xb139d8: cmp             w1, NULL
    // 0xb139dc: b.ne            #0xb139e8
    // 0xb139e0: r5 = Null
    //     0xb139e0: mov             x5, NULL
    // 0xb139e4: b               #0xb13a20
    // 0xb139e8: LoadField: r5 = r1->field_f
    //     0xb139e8: ldur            w5, [x1, #0xf]
    // 0xb139ec: DecompressPointer r5
    //     0xb139ec: add             x5, x5, HEAP, lsl #32
    // 0xb139f0: cmp             w5, NULL
    // 0xb139f4: b.ne            #0xb13a00
    // 0xb139f8: r5 = Null
    //     0xb139f8: mov             x5, NULL
    // 0xb139fc: b               #0xb13a20
    // 0xb13a00: LoadField: r7 = r5->field_1b
    //     0xb13a00: ldur            w7, [x5, #0x1b]
    // 0xb13a04: DecompressPointer r7
    //     0xb13a04: add             x7, x7, HEAP, lsl #32
    // 0xb13a08: cmp             w7, NULL
    // 0xb13a0c: b.ne            #0xb13a18
    // 0xb13a10: r5 = Null
    //     0xb13a10: mov             x5, NULL
    // 0xb13a14: b               #0xb13a20
    // 0xb13a18: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb13a18: ldur            w5, [x7, #0x17]
    // 0xb13a1c: DecompressPointer r5
    //     0xb13a1c: add             x5, x5, HEAP, lsl #32
    // 0xb13a20: cmp             w5, NULL
    // 0xb13a24: b.ne            #0xb13a30
    // 0xb13a28: r5 = 0
    //     0xb13a28: movz            x5, #0
    // 0xb13a2c: b               #0xb13a40
    // 0xb13a30: r7 = LoadInt32Instr(r5)
    //     0xb13a30: sbfx            x7, x5, #1, #0x1f
    //     0xb13a34: tbz             w5, #0, #0xb13a3c
    //     0xb13a38: ldur            x7, [x5, #7]
    // 0xb13a3c: mov             x5, x7
    // 0xb13a40: add             x7, x8, x5
    // 0xb13a44: scvtf           d0, x3
    // 0xb13a48: scvtf           d1, x7
    // 0xb13a4c: fdiv            d2, d0, d1
    // 0xb13a50: cmp             w1, NULL
    // 0xb13a54: b.ne            #0xb13a60
    // 0xb13a58: r1 = Null
    //     0xb13a58: mov             x1, NULL
    // 0xb13a5c: b               #0xb13a9c
    // 0xb13a60: LoadField: r3 = r1->field_f
    //     0xb13a60: ldur            w3, [x1, #0xf]
    // 0xb13a64: DecompressPointer r3
    //     0xb13a64: add             x3, x3, HEAP, lsl #32
    // 0xb13a68: cmp             w3, NULL
    // 0xb13a6c: b.ne            #0xb13a78
    // 0xb13a70: r1 = Null
    //     0xb13a70: mov             x1, NULL
    // 0xb13a74: b               #0xb13a9c
    // 0xb13a78: LoadField: r1 = r3->field_1b
    //     0xb13a78: ldur            w1, [x3, #0x1b]
    // 0xb13a7c: DecompressPointer r1
    //     0xb13a7c: add             x1, x1, HEAP, lsl #32
    // 0xb13a80: cmp             w1, NULL
    // 0xb13a84: b.ne            #0xb13a90
    // 0xb13a88: r1 = Null
    //     0xb13a88: mov             x1, NULL
    // 0xb13a8c: b               #0xb13a9c
    // 0xb13a90: LoadField: r3 = r1->field_f
    //     0xb13a90: ldur            w3, [x1, #0xf]
    // 0xb13a94: DecompressPointer r3
    //     0xb13a94: add             x3, x3, HEAP, lsl #32
    // 0xb13a98: mov             x1, x3
    // 0xb13a9c: cmp             w1, NULL
    // 0xb13aa0: b.ne            #0xb13aac
    // 0xb13aa4: r5 = 0
    //     0xb13aa4: movz            x5, #0
    // 0xb13aa8: b               #0xb13abc
    // 0xb13aac: r3 = LoadInt32Instr(r1)
    //     0xb13aac: sbfx            x3, x1, #1, #0x1f
    //     0xb13ab0: tbz             w1, #0, #0xb13ab8
    //     0xb13ab4: ldur            x3, [x1, #7]
    // 0xb13ab8: mov             x5, x3
    // 0xb13abc: mov             x1, x6
    // 0xb13ac0: mov             v0.16b, v2.16b
    // 0xb13ac4: r3 = "3"
    //     0xb13ac4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] "3"
    //     0xb13ac8: ldr             x3, [x3, #0x8a8]
    // 0xb13acc: r0 = chartRow()
    //     0xb13acc: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb13ad0: mov             x4, x0
    // 0xb13ad4: ldur            x0, [fp, #-0x18]
    // 0xb13ad8: stur            x4, [fp, #-0x48]
    // 0xb13adc: LoadField: r2 = r0->field_13
    //     0xb13adc: ldur            w2, [x0, #0x13]
    // 0xb13ae0: DecompressPointer r2
    //     0xb13ae0: add             x2, x2, HEAP, lsl #32
    // 0xb13ae4: ldur            x6, [fp, #-8]
    // 0xb13ae8: LoadField: r1 = r6->field_b
    //     0xb13ae8: ldur            w1, [x6, #0xb]
    // 0xb13aec: DecompressPointer r1
    //     0xb13aec: add             x1, x1, HEAP, lsl #32
    // 0xb13af0: cmp             w1, NULL
    // 0xb13af4: b.eq            #0xb14e78
    // 0xb13af8: LoadField: r3 = r1->field_f
    //     0xb13af8: ldur            w3, [x1, #0xf]
    // 0xb13afc: DecompressPointer r3
    //     0xb13afc: add             x3, x3, HEAP, lsl #32
    // 0xb13b00: LoadField: r1 = r3->field_b
    //     0xb13b00: ldur            w1, [x3, #0xb]
    // 0xb13b04: DecompressPointer r1
    //     0xb13b04: add             x1, x1, HEAP, lsl #32
    // 0xb13b08: cmp             w1, NULL
    // 0xb13b0c: b.ne            #0xb13b18
    // 0xb13b10: r3 = Null
    //     0xb13b10: mov             x3, NULL
    // 0xb13b14: b               #0xb13b50
    // 0xb13b18: LoadField: r3 = r1->field_f
    //     0xb13b18: ldur            w3, [x1, #0xf]
    // 0xb13b1c: DecompressPointer r3
    //     0xb13b1c: add             x3, x3, HEAP, lsl #32
    // 0xb13b20: cmp             w3, NULL
    // 0xb13b24: b.ne            #0xb13b30
    // 0xb13b28: r3 = Null
    //     0xb13b28: mov             x3, NULL
    // 0xb13b2c: b               #0xb13b50
    // 0xb13b30: LoadField: r5 = r3->field_1b
    //     0xb13b30: ldur            w5, [x3, #0x1b]
    // 0xb13b34: DecompressPointer r5
    //     0xb13b34: add             x5, x5, HEAP, lsl #32
    // 0xb13b38: cmp             w5, NULL
    // 0xb13b3c: b.ne            #0xb13b48
    // 0xb13b40: r3 = Null
    //     0xb13b40: mov             x3, NULL
    // 0xb13b44: b               #0xb13b50
    // 0xb13b48: LoadField: r3 = r5->field_13
    //     0xb13b48: ldur            w3, [x5, #0x13]
    // 0xb13b4c: DecompressPointer r3
    //     0xb13b4c: add             x3, x3, HEAP, lsl #32
    // 0xb13b50: cmp             w3, NULL
    // 0xb13b54: b.ne            #0xb13b60
    // 0xb13b58: r3 = 0
    //     0xb13b58: movz            x3, #0
    // 0xb13b5c: b               #0xb13b70
    // 0xb13b60: r5 = LoadInt32Instr(r3)
    //     0xb13b60: sbfx            x5, x3, #1, #0x1f
    //     0xb13b64: tbz             w3, #0, #0xb13b6c
    //     0xb13b68: ldur            x5, [x3, #7]
    // 0xb13b6c: mov             x3, x5
    // 0xb13b70: cmp             w1, NULL
    // 0xb13b74: b.ne            #0xb13b80
    // 0xb13b78: r5 = Null
    //     0xb13b78: mov             x5, NULL
    // 0xb13b7c: b               #0xb13bb8
    // 0xb13b80: LoadField: r5 = r1->field_f
    //     0xb13b80: ldur            w5, [x1, #0xf]
    // 0xb13b84: DecompressPointer r5
    //     0xb13b84: add             x5, x5, HEAP, lsl #32
    // 0xb13b88: cmp             w5, NULL
    // 0xb13b8c: b.ne            #0xb13b98
    // 0xb13b90: r5 = Null
    //     0xb13b90: mov             x5, NULL
    // 0xb13b94: b               #0xb13bb8
    // 0xb13b98: LoadField: r7 = r5->field_1b
    //     0xb13b98: ldur            w7, [x5, #0x1b]
    // 0xb13b9c: DecompressPointer r7
    //     0xb13b9c: add             x7, x7, HEAP, lsl #32
    // 0xb13ba0: cmp             w7, NULL
    // 0xb13ba4: b.ne            #0xb13bb0
    // 0xb13ba8: r5 = Null
    //     0xb13ba8: mov             x5, NULL
    // 0xb13bac: b               #0xb13bb8
    // 0xb13bb0: LoadField: r5 = r7->field_7
    //     0xb13bb0: ldur            w5, [x7, #7]
    // 0xb13bb4: DecompressPointer r5
    //     0xb13bb4: add             x5, x5, HEAP, lsl #32
    // 0xb13bb8: cmp             w5, NULL
    // 0xb13bbc: b.ne            #0xb13bc8
    // 0xb13bc0: r5 = 0
    //     0xb13bc0: movz            x5, #0
    // 0xb13bc4: b               #0xb13bd8
    // 0xb13bc8: r7 = LoadInt32Instr(r5)
    //     0xb13bc8: sbfx            x7, x5, #1, #0x1f
    //     0xb13bcc: tbz             w5, #0, #0xb13bd4
    //     0xb13bd0: ldur            x7, [x5, #7]
    // 0xb13bd4: mov             x5, x7
    // 0xb13bd8: cmp             w1, NULL
    // 0xb13bdc: b.ne            #0xb13be8
    // 0xb13be0: r7 = Null
    //     0xb13be0: mov             x7, NULL
    // 0xb13be4: b               #0xb13c20
    // 0xb13be8: LoadField: r7 = r1->field_f
    //     0xb13be8: ldur            w7, [x1, #0xf]
    // 0xb13bec: DecompressPointer r7
    //     0xb13bec: add             x7, x7, HEAP, lsl #32
    // 0xb13bf0: cmp             w7, NULL
    // 0xb13bf4: b.ne            #0xb13c00
    // 0xb13bf8: r7 = Null
    //     0xb13bf8: mov             x7, NULL
    // 0xb13bfc: b               #0xb13c20
    // 0xb13c00: LoadField: r8 = r7->field_1b
    //     0xb13c00: ldur            w8, [x7, #0x1b]
    // 0xb13c04: DecompressPointer r8
    //     0xb13c04: add             x8, x8, HEAP, lsl #32
    // 0xb13c08: cmp             w8, NULL
    // 0xb13c0c: b.ne            #0xb13c18
    // 0xb13c10: r7 = Null
    //     0xb13c10: mov             x7, NULL
    // 0xb13c14: b               #0xb13c20
    // 0xb13c18: LoadField: r7 = r8->field_b
    //     0xb13c18: ldur            w7, [x8, #0xb]
    // 0xb13c1c: DecompressPointer r7
    //     0xb13c1c: add             x7, x7, HEAP, lsl #32
    // 0xb13c20: cmp             w7, NULL
    // 0xb13c24: b.ne            #0xb13c30
    // 0xb13c28: r7 = 0
    //     0xb13c28: movz            x7, #0
    // 0xb13c2c: b               #0xb13c40
    // 0xb13c30: r8 = LoadInt32Instr(r7)
    //     0xb13c30: sbfx            x8, x7, #1, #0x1f
    //     0xb13c34: tbz             w7, #0, #0xb13c3c
    //     0xb13c38: ldur            x8, [x7, #7]
    // 0xb13c3c: mov             x7, x8
    // 0xb13c40: add             x8, x5, x7
    // 0xb13c44: cmp             w1, NULL
    // 0xb13c48: b.ne            #0xb13c54
    // 0xb13c4c: r5 = Null
    //     0xb13c4c: mov             x5, NULL
    // 0xb13c50: b               #0xb13c8c
    // 0xb13c54: LoadField: r5 = r1->field_f
    //     0xb13c54: ldur            w5, [x1, #0xf]
    // 0xb13c58: DecompressPointer r5
    //     0xb13c58: add             x5, x5, HEAP, lsl #32
    // 0xb13c5c: cmp             w5, NULL
    // 0xb13c60: b.ne            #0xb13c6c
    // 0xb13c64: r5 = Null
    //     0xb13c64: mov             x5, NULL
    // 0xb13c68: b               #0xb13c8c
    // 0xb13c6c: LoadField: r7 = r5->field_1b
    //     0xb13c6c: ldur            w7, [x5, #0x1b]
    // 0xb13c70: DecompressPointer r7
    //     0xb13c70: add             x7, x7, HEAP, lsl #32
    // 0xb13c74: cmp             w7, NULL
    // 0xb13c78: b.ne            #0xb13c84
    // 0xb13c7c: r5 = Null
    //     0xb13c7c: mov             x5, NULL
    // 0xb13c80: b               #0xb13c8c
    // 0xb13c84: LoadField: r5 = r7->field_f
    //     0xb13c84: ldur            w5, [x7, #0xf]
    // 0xb13c88: DecompressPointer r5
    //     0xb13c88: add             x5, x5, HEAP, lsl #32
    // 0xb13c8c: cmp             w5, NULL
    // 0xb13c90: b.ne            #0xb13c9c
    // 0xb13c94: r5 = 0
    //     0xb13c94: movz            x5, #0
    // 0xb13c98: b               #0xb13cac
    // 0xb13c9c: r7 = LoadInt32Instr(r5)
    //     0xb13c9c: sbfx            x7, x5, #1, #0x1f
    //     0xb13ca0: tbz             w5, #0, #0xb13ca8
    //     0xb13ca4: ldur            x7, [x5, #7]
    // 0xb13ca8: mov             x5, x7
    // 0xb13cac: add             x7, x8, x5
    // 0xb13cb0: cmp             w1, NULL
    // 0xb13cb4: b.ne            #0xb13cc0
    // 0xb13cb8: r5 = Null
    //     0xb13cb8: mov             x5, NULL
    // 0xb13cbc: b               #0xb13cf8
    // 0xb13cc0: LoadField: r5 = r1->field_f
    //     0xb13cc0: ldur            w5, [x1, #0xf]
    // 0xb13cc4: DecompressPointer r5
    //     0xb13cc4: add             x5, x5, HEAP, lsl #32
    // 0xb13cc8: cmp             w5, NULL
    // 0xb13ccc: b.ne            #0xb13cd8
    // 0xb13cd0: r5 = Null
    //     0xb13cd0: mov             x5, NULL
    // 0xb13cd4: b               #0xb13cf8
    // 0xb13cd8: LoadField: r8 = r5->field_1b
    //     0xb13cd8: ldur            w8, [x5, #0x1b]
    // 0xb13cdc: DecompressPointer r8
    //     0xb13cdc: add             x8, x8, HEAP, lsl #32
    // 0xb13ce0: cmp             w8, NULL
    // 0xb13ce4: b.ne            #0xb13cf0
    // 0xb13ce8: r5 = Null
    //     0xb13ce8: mov             x5, NULL
    // 0xb13cec: b               #0xb13cf8
    // 0xb13cf0: LoadField: r5 = r8->field_13
    //     0xb13cf0: ldur            w5, [x8, #0x13]
    // 0xb13cf4: DecompressPointer r5
    //     0xb13cf4: add             x5, x5, HEAP, lsl #32
    // 0xb13cf8: cmp             w5, NULL
    // 0xb13cfc: b.ne            #0xb13d08
    // 0xb13d00: r5 = 0
    //     0xb13d00: movz            x5, #0
    // 0xb13d04: b               #0xb13d18
    // 0xb13d08: r8 = LoadInt32Instr(r5)
    //     0xb13d08: sbfx            x8, x5, #1, #0x1f
    //     0xb13d0c: tbz             w5, #0, #0xb13d14
    //     0xb13d10: ldur            x8, [x5, #7]
    // 0xb13d14: mov             x5, x8
    // 0xb13d18: add             x8, x7, x5
    // 0xb13d1c: cmp             w1, NULL
    // 0xb13d20: b.ne            #0xb13d2c
    // 0xb13d24: r5 = Null
    //     0xb13d24: mov             x5, NULL
    // 0xb13d28: b               #0xb13d64
    // 0xb13d2c: LoadField: r5 = r1->field_f
    //     0xb13d2c: ldur            w5, [x1, #0xf]
    // 0xb13d30: DecompressPointer r5
    //     0xb13d30: add             x5, x5, HEAP, lsl #32
    // 0xb13d34: cmp             w5, NULL
    // 0xb13d38: b.ne            #0xb13d44
    // 0xb13d3c: r5 = Null
    //     0xb13d3c: mov             x5, NULL
    // 0xb13d40: b               #0xb13d64
    // 0xb13d44: LoadField: r7 = r5->field_1b
    //     0xb13d44: ldur            w7, [x5, #0x1b]
    // 0xb13d48: DecompressPointer r7
    //     0xb13d48: add             x7, x7, HEAP, lsl #32
    // 0xb13d4c: cmp             w7, NULL
    // 0xb13d50: b.ne            #0xb13d5c
    // 0xb13d54: r5 = Null
    //     0xb13d54: mov             x5, NULL
    // 0xb13d58: b               #0xb13d64
    // 0xb13d5c: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb13d5c: ldur            w5, [x7, #0x17]
    // 0xb13d60: DecompressPointer r5
    //     0xb13d60: add             x5, x5, HEAP, lsl #32
    // 0xb13d64: cmp             w5, NULL
    // 0xb13d68: b.ne            #0xb13d74
    // 0xb13d6c: r5 = 0
    //     0xb13d6c: movz            x5, #0
    // 0xb13d70: b               #0xb13d84
    // 0xb13d74: r7 = LoadInt32Instr(r5)
    //     0xb13d74: sbfx            x7, x5, #1, #0x1f
    //     0xb13d78: tbz             w5, #0, #0xb13d80
    //     0xb13d7c: ldur            x7, [x5, #7]
    // 0xb13d80: mov             x5, x7
    // 0xb13d84: add             x7, x8, x5
    // 0xb13d88: scvtf           d0, x3
    // 0xb13d8c: scvtf           d1, x7
    // 0xb13d90: fdiv            d2, d0, d1
    // 0xb13d94: cmp             w1, NULL
    // 0xb13d98: b.ne            #0xb13da4
    // 0xb13d9c: r1 = Null
    //     0xb13d9c: mov             x1, NULL
    // 0xb13da0: b               #0xb13de0
    // 0xb13da4: LoadField: r3 = r1->field_f
    //     0xb13da4: ldur            w3, [x1, #0xf]
    // 0xb13da8: DecompressPointer r3
    //     0xb13da8: add             x3, x3, HEAP, lsl #32
    // 0xb13dac: cmp             w3, NULL
    // 0xb13db0: b.ne            #0xb13dbc
    // 0xb13db4: r1 = Null
    //     0xb13db4: mov             x1, NULL
    // 0xb13db8: b               #0xb13de0
    // 0xb13dbc: LoadField: r1 = r3->field_1b
    //     0xb13dbc: ldur            w1, [x3, #0x1b]
    // 0xb13dc0: DecompressPointer r1
    //     0xb13dc0: add             x1, x1, HEAP, lsl #32
    // 0xb13dc4: cmp             w1, NULL
    // 0xb13dc8: b.ne            #0xb13dd4
    // 0xb13dcc: r1 = Null
    //     0xb13dcc: mov             x1, NULL
    // 0xb13dd0: b               #0xb13de0
    // 0xb13dd4: LoadField: r3 = r1->field_13
    //     0xb13dd4: ldur            w3, [x1, #0x13]
    // 0xb13dd8: DecompressPointer r3
    //     0xb13dd8: add             x3, x3, HEAP, lsl #32
    // 0xb13ddc: mov             x1, x3
    // 0xb13de0: cmp             w1, NULL
    // 0xb13de4: b.ne            #0xb13df0
    // 0xb13de8: r5 = 0
    //     0xb13de8: movz            x5, #0
    // 0xb13dec: b               #0xb13e00
    // 0xb13df0: r3 = LoadInt32Instr(r1)
    //     0xb13df0: sbfx            x3, x1, #1, #0x1f
    //     0xb13df4: tbz             w1, #0, #0xb13dfc
    //     0xb13df8: ldur            x3, [x1, #7]
    // 0xb13dfc: mov             x5, x3
    // 0xb13e00: mov             x1, x6
    // 0xb13e04: mov             v0.16b, v2.16b
    // 0xb13e08: r3 = "2"
    //     0xb13e08: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] "2"
    //     0xb13e0c: ldr             x3, [x3, #0x8b0]
    // 0xb13e10: r0 = chartRow()
    //     0xb13e10: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb13e14: mov             x4, x0
    // 0xb13e18: ldur            x0, [fp, #-0x18]
    // 0xb13e1c: stur            x4, [fp, #-0x50]
    // 0xb13e20: LoadField: r2 = r0->field_13
    //     0xb13e20: ldur            w2, [x0, #0x13]
    // 0xb13e24: DecompressPointer r2
    //     0xb13e24: add             x2, x2, HEAP, lsl #32
    // 0xb13e28: ldur            x6, [fp, #-8]
    // 0xb13e2c: LoadField: r1 = r6->field_b
    //     0xb13e2c: ldur            w1, [x6, #0xb]
    // 0xb13e30: DecompressPointer r1
    //     0xb13e30: add             x1, x1, HEAP, lsl #32
    // 0xb13e34: cmp             w1, NULL
    // 0xb13e38: b.eq            #0xb14e7c
    // 0xb13e3c: LoadField: r3 = r1->field_f
    //     0xb13e3c: ldur            w3, [x1, #0xf]
    // 0xb13e40: DecompressPointer r3
    //     0xb13e40: add             x3, x3, HEAP, lsl #32
    // 0xb13e44: LoadField: r1 = r3->field_b
    //     0xb13e44: ldur            w1, [x3, #0xb]
    // 0xb13e48: DecompressPointer r1
    //     0xb13e48: add             x1, x1, HEAP, lsl #32
    // 0xb13e4c: cmp             w1, NULL
    // 0xb13e50: b.ne            #0xb13e5c
    // 0xb13e54: r3 = Null
    //     0xb13e54: mov             x3, NULL
    // 0xb13e58: b               #0xb13e94
    // 0xb13e5c: LoadField: r3 = r1->field_f
    //     0xb13e5c: ldur            w3, [x1, #0xf]
    // 0xb13e60: DecompressPointer r3
    //     0xb13e60: add             x3, x3, HEAP, lsl #32
    // 0xb13e64: cmp             w3, NULL
    // 0xb13e68: b.ne            #0xb13e74
    // 0xb13e6c: r3 = Null
    //     0xb13e6c: mov             x3, NULL
    // 0xb13e70: b               #0xb13e94
    // 0xb13e74: LoadField: r5 = r3->field_1b
    //     0xb13e74: ldur            w5, [x3, #0x1b]
    // 0xb13e78: DecompressPointer r5
    //     0xb13e78: add             x5, x5, HEAP, lsl #32
    // 0xb13e7c: cmp             w5, NULL
    // 0xb13e80: b.ne            #0xb13e8c
    // 0xb13e84: r3 = Null
    //     0xb13e84: mov             x3, NULL
    // 0xb13e88: b               #0xb13e94
    // 0xb13e8c: ArrayLoad: r3 = r5[0]  ; List_4
    //     0xb13e8c: ldur            w3, [x5, #0x17]
    // 0xb13e90: DecompressPointer r3
    //     0xb13e90: add             x3, x3, HEAP, lsl #32
    // 0xb13e94: cmp             w3, NULL
    // 0xb13e98: b.ne            #0xb13ea4
    // 0xb13e9c: r3 = 0
    //     0xb13e9c: movz            x3, #0
    // 0xb13ea0: b               #0xb13eb4
    // 0xb13ea4: r5 = LoadInt32Instr(r3)
    //     0xb13ea4: sbfx            x5, x3, #1, #0x1f
    //     0xb13ea8: tbz             w3, #0, #0xb13eb0
    //     0xb13eac: ldur            x5, [x3, #7]
    // 0xb13eb0: mov             x3, x5
    // 0xb13eb4: cmp             w1, NULL
    // 0xb13eb8: b.ne            #0xb13ec4
    // 0xb13ebc: r5 = Null
    //     0xb13ebc: mov             x5, NULL
    // 0xb13ec0: b               #0xb13efc
    // 0xb13ec4: LoadField: r5 = r1->field_f
    //     0xb13ec4: ldur            w5, [x1, #0xf]
    // 0xb13ec8: DecompressPointer r5
    //     0xb13ec8: add             x5, x5, HEAP, lsl #32
    // 0xb13ecc: cmp             w5, NULL
    // 0xb13ed0: b.ne            #0xb13edc
    // 0xb13ed4: r5 = Null
    //     0xb13ed4: mov             x5, NULL
    // 0xb13ed8: b               #0xb13efc
    // 0xb13edc: LoadField: r7 = r5->field_1b
    //     0xb13edc: ldur            w7, [x5, #0x1b]
    // 0xb13ee0: DecompressPointer r7
    //     0xb13ee0: add             x7, x7, HEAP, lsl #32
    // 0xb13ee4: cmp             w7, NULL
    // 0xb13ee8: b.ne            #0xb13ef4
    // 0xb13eec: r5 = Null
    //     0xb13eec: mov             x5, NULL
    // 0xb13ef0: b               #0xb13efc
    // 0xb13ef4: LoadField: r5 = r7->field_7
    //     0xb13ef4: ldur            w5, [x7, #7]
    // 0xb13ef8: DecompressPointer r5
    //     0xb13ef8: add             x5, x5, HEAP, lsl #32
    // 0xb13efc: cmp             w5, NULL
    // 0xb13f00: b.ne            #0xb13f0c
    // 0xb13f04: r5 = 0
    //     0xb13f04: movz            x5, #0
    // 0xb13f08: b               #0xb13f1c
    // 0xb13f0c: r7 = LoadInt32Instr(r5)
    //     0xb13f0c: sbfx            x7, x5, #1, #0x1f
    //     0xb13f10: tbz             w5, #0, #0xb13f18
    //     0xb13f14: ldur            x7, [x5, #7]
    // 0xb13f18: mov             x5, x7
    // 0xb13f1c: cmp             w1, NULL
    // 0xb13f20: b.ne            #0xb13f2c
    // 0xb13f24: r7 = Null
    //     0xb13f24: mov             x7, NULL
    // 0xb13f28: b               #0xb13f64
    // 0xb13f2c: LoadField: r7 = r1->field_f
    //     0xb13f2c: ldur            w7, [x1, #0xf]
    // 0xb13f30: DecompressPointer r7
    //     0xb13f30: add             x7, x7, HEAP, lsl #32
    // 0xb13f34: cmp             w7, NULL
    // 0xb13f38: b.ne            #0xb13f44
    // 0xb13f3c: r7 = Null
    //     0xb13f3c: mov             x7, NULL
    // 0xb13f40: b               #0xb13f64
    // 0xb13f44: LoadField: r8 = r7->field_1b
    //     0xb13f44: ldur            w8, [x7, #0x1b]
    // 0xb13f48: DecompressPointer r8
    //     0xb13f48: add             x8, x8, HEAP, lsl #32
    // 0xb13f4c: cmp             w8, NULL
    // 0xb13f50: b.ne            #0xb13f5c
    // 0xb13f54: r7 = Null
    //     0xb13f54: mov             x7, NULL
    // 0xb13f58: b               #0xb13f64
    // 0xb13f5c: LoadField: r7 = r8->field_b
    //     0xb13f5c: ldur            w7, [x8, #0xb]
    // 0xb13f60: DecompressPointer r7
    //     0xb13f60: add             x7, x7, HEAP, lsl #32
    // 0xb13f64: cmp             w7, NULL
    // 0xb13f68: b.ne            #0xb13f74
    // 0xb13f6c: r7 = 0
    //     0xb13f6c: movz            x7, #0
    // 0xb13f70: b               #0xb13f84
    // 0xb13f74: r8 = LoadInt32Instr(r7)
    //     0xb13f74: sbfx            x8, x7, #1, #0x1f
    //     0xb13f78: tbz             w7, #0, #0xb13f80
    //     0xb13f7c: ldur            x8, [x7, #7]
    // 0xb13f80: mov             x7, x8
    // 0xb13f84: add             x8, x5, x7
    // 0xb13f88: cmp             w1, NULL
    // 0xb13f8c: b.ne            #0xb13f98
    // 0xb13f90: r5 = Null
    //     0xb13f90: mov             x5, NULL
    // 0xb13f94: b               #0xb13fd0
    // 0xb13f98: LoadField: r5 = r1->field_f
    //     0xb13f98: ldur            w5, [x1, #0xf]
    // 0xb13f9c: DecompressPointer r5
    //     0xb13f9c: add             x5, x5, HEAP, lsl #32
    // 0xb13fa0: cmp             w5, NULL
    // 0xb13fa4: b.ne            #0xb13fb0
    // 0xb13fa8: r5 = Null
    //     0xb13fa8: mov             x5, NULL
    // 0xb13fac: b               #0xb13fd0
    // 0xb13fb0: LoadField: r7 = r5->field_1b
    //     0xb13fb0: ldur            w7, [x5, #0x1b]
    // 0xb13fb4: DecompressPointer r7
    //     0xb13fb4: add             x7, x7, HEAP, lsl #32
    // 0xb13fb8: cmp             w7, NULL
    // 0xb13fbc: b.ne            #0xb13fc8
    // 0xb13fc0: r5 = Null
    //     0xb13fc0: mov             x5, NULL
    // 0xb13fc4: b               #0xb13fd0
    // 0xb13fc8: LoadField: r5 = r7->field_f
    //     0xb13fc8: ldur            w5, [x7, #0xf]
    // 0xb13fcc: DecompressPointer r5
    //     0xb13fcc: add             x5, x5, HEAP, lsl #32
    // 0xb13fd0: cmp             w5, NULL
    // 0xb13fd4: b.ne            #0xb13fe0
    // 0xb13fd8: r5 = 0
    //     0xb13fd8: movz            x5, #0
    // 0xb13fdc: b               #0xb13ff0
    // 0xb13fe0: r7 = LoadInt32Instr(r5)
    //     0xb13fe0: sbfx            x7, x5, #1, #0x1f
    //     0xb13fe4: tbz             w5, #0, #0xb13fec
    //     0xb13fe8: ldur            x7, [x5, #7]
    // 0xb13fec: mov             x5, x7
    // 0xb13ff0: add             x7, x8, x5
    // 0xb13ff4: cmp             w1, NULL
    // 0xb13ff8: b.ne            #0xb14004
    // 0xb13ffc: r5 = Null
    //     0xb13ffc: mov             x5, NULL
    // 0xb14000: b               #0xb1403c
    // 0xb14004: LoadField: r5 = r1->field_f
    //     0xb14004: ldur            w5, [x1, #0xf]
    // 0xb14008: DecompressPointer r5
    //     0xb14008: add             x5, x5, HEAP, lsl #32
    // 0xb1400c: cmp             w5, NULL
    // 0xb14010: b.ne            #0xb1401c
    // 0xb14014: r5 = Null
    //     0xb14014: mov             x5, NULL
    // 0xb14018: b               #0xb1403c
    // 0xb1401c: LoadField: r8 = r5->field_1b
    //     0xb1401c: ldur            w8, [x5, #0x1b]
    // 0xb14020: DecompressPointer r8
    //     0xb14020: add             x8, x8, HEAP, lsl #32
    // 0xb14024: cmp             w8, NULL
    // 0xb14028: b.ne            #0xb14034
    // 0xb1402c: r5 = Null
    //     0xb1402c: mov             x5, NULL
    // 0xb14030: b               #0xb1403c
    // 0xb14034: LoadField: r5 = r8->field_13
    //     0xb14034: ldur            w5, [x8, #0x13]
    // 0xb14038: DecompressPointer r5
    //     0xb14038: add             x5, x5, HEAP, lsl #32
    // 0xb1403c: cmp             w5, NULL
    // 0xb14040: b.ne            #0xb1404c
    // 0xb14044: r5 = 0
    //     0xb14044: movz            x5, #0
    // 0xb14048: b               #0xb1405c
    // 0xb1404c: r8 = LoadInt32Instr(r5)
    //     0xb1404c: sbfx            x8, x5, #1, #0x1f
    //     0xb14050: tbz             w5, #0, #0xb14058
    //     0xb14054: ldur            x8, [x5, #7]
    // 0xb14058: mov             x5, x8
    // 0xb1405c: add             x8, x7, x5
    // 0xb14060: cmp             w1, NULL
    // 0xb14064: b.ne            #0xb14070
    // 0xb14068: r5 = Null
    //     0xb14068: mov             x5, NULL
    // 0xb1406c: b               #0xb140a8
    // 0xb14070: LoadField: r5 = r1->field_f
    //     0xb14070: ldur            w5, [x1, #0xf]
    // 0xb14074: DecompressPointer r5
    //     0xb14074: add             x5, x5, HEAP, lsl #32
    // 0xb14078: cmp             w5, NULL
    // 0xb1407c: b.ne            #0xb14088
    // 0xb14080: r5 = Null
    //     0xb14080: mov             x5, NULL
    // 0xb14084: b               #0xb140a8
    // 0xb14088: LoadField: r7 = r5->field_1b
    //     0xb14088: ldur            w7, [x5, #0x1b]
    // 0xb1408c: DecompressPointer r7
    //     0xb1408c: add             x7, x7, HEAP, lsl #32
    // 0xb14090: cmp             w7, NULL
    // 0xb14094: b.ne            #0xb140a0
    // 0xb14098: r5 = Null
    //     0xb14098: mov             x5, NULL
    // 0xb1409c: b               #0xb140a8
    // 0xb140a0: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb140a0: ldur            w5, [x7, #0x17]
    // 0xb140a4: DecompressPointer r5
    //     0xb140a4: add             x5, x5, HEAP, lsl #32
    // 0xb140a8: cmp             w5, NULL
    // 0xb140ac: b.ne            #0xb140b8
    // 0xb140b0: r5 = 0
    //     0xb140b0: movz            x5, #0
    // 0xb140b4: b               #0xb140c8
    // 0xb140b8: r7 = LoadInt32Instr(r5)
    //     0xb140b8: sbfx            x7, x5, #1, #0x1f
    //     0xb140bc: tbz             w5, #0, #0xb140c4
    //     0xb140c0: ldur            x7, [x5, #7]
    // 0xb140c4: mov             x5, x7
    // 0xb140c8: add             x7, x8, x5
    // 0xb140cc: scvtf           d0, x3
    // 0xb140d0: scvtf           d1, x7
    // 0xb140d4: fdiv            d2, d0, d1
    // 0xb140d8: cmp             w1, NULL
    // 0xb140dc: b.ne            #0xb140e8
    // 0xb140e0: r1 = Null
    //     0xb140e0: mov             x1, NULL
    // 0xb140e4: b               #0xb14124
    // 0xb140e8: LoadField: r3 = r1->field_f
    //     0xb140e8: ldur            w3, [x1, #0xf]
    // 0xb140ec: DecompressPointer r3
    //     0xb140ec: add             x3, x3, HEAP, lsl #32
    // 0xb140f0: cmp             w3, NULL
    // 0xb140f4: b.ne            #0xb14100
    // 0xb140f8: r1 = Null
    //     0xb140f8: mov             x1, NULL
    // 0xb140fc: b               #0xb14124
    // 0xb14100: LoadField: r1 = r3->field_1b
    //     0xb14100: ldur            w1, [x3, #0x1b]
    // 0xb14104: DecompressPointer r1
    //     0xb14104: add             x1, x1, HEAP, lsl #32
    // 0xb14108: cmp             w1, NULL
    // 0xb1410c: b.ne            #0xb14118
    // 0xb14110: r1 = Null
    //     0xb14110: mov             x1, NULL
    // 0xb14114: b               #0xb14124
    // 0xb14118: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb14118: ldur            w3, [x1, #0x17]
    // 0xb1411c: DecompressPointer r3
    //     0xb1411c: add             x3, x3, HEAP, lsl #32
    // 0xb14120: mov             x1, x3
    // 0xb14124: cmp             w1, NULL
    // 0xb14128: b.ne            #0xb14134
    // 0xb1412c: r5 = 0
    //     0xb1412c: movz            x5, #0
    // 0xb14130: b               #0xb14144
    // 0xb14134: r3 = LoadInt32Instr(r1)
    //     0xb14134: sbfx            x3, x1, #1, #0x1f
    //     0xb14138: tbz             w1, #0, #0xb14140
    //     0xb1413c: ldur            x3, [x1, #7]
    // 0xb14140: mov             x5, x3
    // 0xb14144: ldur            x11, [fp, #-0x20]
    // 0xb14148: ldur            x10, [fp, #-0x28]
    // 0xb1414c: ldur            x9, [fp, #-0x30]
    // 0xb14150: ldur            x8, [fp, #-0x38]
    // 0xb14154: ldur            x7, [fp, #-0x48]
    // 0xb14158: ldur            x12, [fp, #-0x10]
    // 0xb1415c: mov             x1, x6
    // 0xb14160: mov             v0.16b, v2.16b
    // 0xb14164: r3 = "1"
    //     0xb14164: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xb14168: ldr             x3, [x3, #0xba8]
    // 0xb1416c: r0 = chartRow()
    //     0xb1416c: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb14170: r1 = Null
    //     0xb14170: mov             x1, NULL
    // 0xb14174: r2 = 14
    //     0xb14174: movz            x2, #0xe
    // 0xb14178: stur            x0, [fp, #-0x58]
    // 0xb1417c: r0 = AllocateArray()
    //     0xb1417c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb14180: stur            x0, [fp, #-0x60]
    // 0xb14184: r16 = Instance_SizedBox
    //     0xb14184: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb14188: ldr             x16, [x16, #0x8b8]
    // 0xb1418c: StoreField: r0->field_f = r16
    //     0xb1418c: stur            w16, [x0, #0xf]
    // 0xb14190: ldur            x1, [fp, #-0x30]
    // 0xb14194: StoreField: r0->field_13 = r1
    //     0xb14194: stur            w1, [x0, #0x13]
    // 0xb14198: ldur            x1, [fp, #-0x38]
    // 0xb1419c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1419c: stur            w1, [x0, #0x17]
    // 0xb141a0: ldur            x1, [fp, #-0x48]
    // 0xb141a4: StoreField: r0->field_1b = r1
    //     0xb141a4: stur            w1, [x0, #0x1b]
    // 0xb141a8: ldur            x1, [fp, #-0x50]
    // 0xb141ac: StoreField: r0->field_1f = r1
    //     0xb141ac: stur            w1, [x0, #0x1f]
    // 0xb141b0: ldur            x1, [fp, #-0x58]
    // 0xb141b4: StoreField: r0->field_23 = r1
    //     0xb141b4: stur            w1, [x0, #0x23]
    // 0xb141b8: r16 = Instance_SizedBox
    //     0xb141b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb141bc: ldr             x16, [x16, #0x8b8]
    // 0xb141c0: StoreField: r0->field_27 = r16
    //     0xb141c0: stur            w16, [x0, #0x27]
    // 0xb141c4: r1 = <Widget>
    //     0xb141c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb141c8: r0 = AllocateGrowableArray()
    //     0xb141c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb141cc: mov             x1, x0
    // 0xb141d0: ldur            x0, [fp, #-0x60]
    // 0xb141d4: stur            x1, [fp, #-0x30]
    // 0xb141d8: StoreField: r1->field_f = r0
    //     0xb141d8: stur            w0, [x1, #0xf]
    // 0xb141dc: r0 = 14
    //     0xb141dc: movz            x0, #0xe
    // 0xb141e0: StoreField: r1->field_b = r0
    //     0xb141e0: stur            w0, [x1, #0xb]
    // 0xb141e4: r0 = Column()
    //     0xb141e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb141e8: mov             x1, x0
    // 0xb141ec: r0 = Instance_Axis
    //     0xb141ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb141f0: stur            x1, [fp, #-0x38]
    // 0xb141f4: StoreField: r1->field_f = r0
    //     0xb141f4: stur            w0, [x1, #0xf]
    // 0xb141f8: r2 = Instance_MainAxisAlignment
    //     0xb141f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb141fc: ldr             x2, [x2, #0xa08]
    // 0xb14200: StoreField: r1->field_13 = r2
    //     0xb14200: stur            w2, [x1, #0x13]
    // 0xb14204: r3 = Instance_MainAxisSize
    //     0xb14204: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb14208: ldr             x3, [x3, #0xa10]
    // 0xb1420c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb1420c: stur            w3, [x1, #0x17]
    // 0xb14210: r4 = Instance_CrossAxisAlignment
    //     0xb14210: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb14214: ldr             x4, [x4, #0x890]
    // 0xb14218: StoreField: r1->field_1b = r4
    //     0xb14218: stur            w4, [x1, #0x1b]
    // 0xb1421c: r5 = Instance_VerticalDirection
    //     0xb1421c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb14220: ldr             x5, [x5, #0xa20]
    // 0xb14224: StoreField: r1->field_23 = r5
    //     0xb14224: stur            w5, [x1, #0x23]
    // 0xb14228: r6 = Instance_Clip
    //     0xb14228: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1422c: ldr             x6, [x6, #0x38]
    // 0xb14230: StoreField: r1->field_2b = r6
    //     0xb14230: stur            w6, [x1, #0x2b]
    // 0xb14234: StoreField: r1->field_2f = rZR
    //     0xb14234: stur            xzr, [x1, #0x2f]
    // 0xb14238: ldur            x7, [fp, #-0x30]
    // 0xb1423c: StoreField: r1->field_b = r7
    //     0xb1423c: stur            w7, [x1, #0xb]
    // 0xb14240: r0 = InkWell()
    //     0xb14240: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb14244: mov             x3, x0
    // 0xb14248: ldur            x0, [fp, #-0x38]
    // 0xb1424c: stur            x3, [fp, #-0x30]
    // 0xb14250: StoreField: r3->field_b = r0
    //     0xb14250: stur            w0, [x3, #0xb]
    // 0xb14254: ldur            x2, [fp, #-0x18]
    // 0xb14258: r1 = Function '<anonymous closure>':.
    //     0xb14258: add             x1, PP, #0x57, lsl #12  ; [pp+0x57948] AnonymousClosure: (0xb15c30), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb1425c: ldr             x1, [x1, #0x948]
    // 0xb14260: r0 = AllocateClosure()
    //     0xb14260: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb14264: mov             x1, x0
    // 0xb14268: ldur            x0, [fp, #-0x30]
    // 0xb1426c: StoreField: r0->field_f = r1
    //     0xb1426c: stur            w1, [x0, #0xf]
    // 0xb14270: r3 = true
    //     0xb14270: add             x3, NULL, #0x20  ; true
    // 0xb14274: StoreField: r0->field_43 = r3
    //     0xb14274: stur            w3, [x0, #0x43]
    // 0xb14278: r4 = Instance_BoxShape
    //     0xb14278: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1427c: ldr             x4, [x4, #0x80]
    // 0xb14280: StoreField: r0->field_47 = r4
    //     0xb14280: stur            w4, [x0, #0x47]
    // 0xb14284: StoreField: r0->field_6f = r3
    //     0xb14284: stur            w3, [x0, #0x6f]
    // 0xb14288: r5 = false
    //     0xb14288: add             x5, NULL, #0x30  ; false
    // 0xb1428c: StoreField: r0->field_73 = r5
    //     0xb1428c: stur            w5, [x0, #0x73]
    // 0xb14290: StoreField: r0->field_83 = r3
    //     0xb14290: stur            w3, [x0, #0x83]
    // 0xb14294: StoreField: r0->field_7b = r5
    //     0xb14294: stur            w5, [x0, #0x7b]
    // 0xb14298: r1 = Null
    //     0xb14298: mov             x1, NULL
    // 0xb1429c: r2 = 4
    //     0xb1429c: movz            x2, #0x4
    // 0xb142a0: r0 = AllocateArray()
    //     0xb142a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb142a4: mov             x2, x0
    // 0xb142a8: ldur            x0, [fp, #-0x28]
    // 0xb142ac: stur            x2, [fp, #-0x38]
    // 0xb142b0: StoreField: r2->field_f = r0
    //     0xb142b0: stur            w0, [x2, #0xf]
    // 0xb142b4: ldur            x0, [fp, #-0x30]
    // 0xb142b8: StoreField: r2->field_13 = r0
    //     0xb142b8: stur            w0, [x2, #0x13]
    // 0xb142bc: r1 = <Widget>
    //     0xb142bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb142c0: r0 = AllocateGrowableArray()
    //     0xb142c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb142c4: mov             x1, x0
    // 0xb142c8: ldur            x0, [fp, #-0x38]
    // 0xb142cc: stur            x1, [fp, #-0x28]
    // 0xb142d0: StoreField: r1->field_f = r0
    //     0xb142d0: stur            w0, [x1, #0xf]
    // 0xb142d4: r2 = 4
    //     0xb142d4: movz            x2, #0x4
    // 0xb142d8: StoreField: r1->field_b = r2
    //     0xb142d8: stur            w2, [x1, #0xb]
    // 0xb142dc: r0 = Row()
    //     0xb142dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb142e0: mov             x1, x0
    // 0xb142e4: r0 = Instance_Axis
    //     0xb142e4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb142e8: stur            x1, [fp, #-0x30]
    // 0xb142ec: StoreField: r1->field_f = r0
    //     0xb142ec: stur            w0, [x1, #0xf]
    // 0xb142f0: r0 = Instance_MainAxisAlignment
    //     0xb142f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb142f4: ldr             x0, [x0, #0xa8]
    // 0xb142f8: StoreField: r1->field_13 = r0
    //     0xb142f8: stur            w0, [x1, #0x13]
    // 0xb142fc: r0 = Instance_MainAxisSize
    //     0xb142fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb14300: ldr             x0, [x0, #0xa10]
    // 0xb14304: ArrayStore: r1[0] = r0  ; List_4
    //     0xb14304: stur            w0, [x1, #0x17]
    // 0xb14308: r2 = Instance_CrossAxisAlignment
    //     0xb14308: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1430c: ldr             x2, [x2, #0xa18]
    // 0xb14310: StoreField: r1->field_1b = r2
    //     0xb14310: stur            w2, [x1, #0x1b]
    // 0xb14314: r3 = Instance_VerticalDirection
    //     0xb14314: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb14318: ldr             x3, [x3, #0xa20]
    // 0xb1431c: StoreField: r1->field_23 = r3
    //     0xb1431c: stur            w3, [x1, #0x23]
    // 0xb14320: r4 = Instance_Clip
    //     0xb14320: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb14324: ldr             x4, [x4, #0x38]
    // 0xb14328: StoreField: r1->field_2b = r4
    //     0xb14328: stur            w4, [x1, #0x2b]
    // 0xb1432c: StoreField: r1->field_2f = rZR
    //     0xb1432c: stur            xzr, [x1, #0x2f]
    // 0xb14330: ldur            x5, [fp, #-0x28]
    // 0xb14334: StoreField: r1->field_b = r5
    //     0xb14334: stur            w5, [x1, #0xb]
    // 0xb14338: r0 = Padding()
    //     0xb14338: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1433c: mov             x1, x0
    // 0xb14340: r0 = Instance_EdgeInsets
    //     0xb14340: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb14344: ldr             x0, [x0, #0x1f0]
    // 0xb14348: stur            x1, [fp, #-0x28]
    // 0xb1434c: StoreField: r1->field_f = r0
    //     0xb1434c: stur            w0, [x1, #0xf]
    // 0xb14350: ldur            x0, [fp, #-0x30]
    // 0xb14354: StoreField: r1->field_b = r0
    //     0xb14354: stur            w0, [x1, #0xb]
    // 0xb14358: r0 = Card()
    //     0xb14358: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb1435c: mov             x3, x0
    // 0xb14360: r0 = 0.000000
    //     0xb14360: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb14364: stur            x3, [fp, #-0x30]
    // 0xb14368: ArrayStore: r3[0] = r0  ; List_4
    //     0xb14368: stur            w0, [x3, #0x17]
    // 0xb1436c: r0 = true
    //     0xb1436c: add             x0, NULL, #0x20  ; true
    // 0xb14370: StoreField: r3->field_1f = r0
    //     0xb14370: stur            w0, [x3, #0x1f]
    // 0xb14374: ldur            x1, [fp, #-0x28]
    // 0xb14378: StoreField: r3->field_2f = r1
    //     0xb14378: stur            w1, [x3, #0x2f]
    // 0xb1437c: StoreField: r3->field_2b = r0
    //     0xb1437c: stur            w0, [x3, #0x2b]
    // 0xb14380: r1 = Instance__CardVariant
    //     0xb14380: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb14384: ldr             x1, [x1, #0xa68]
    // 0xb14388: StoreField: r3->field_33 = r1
    //     0xb14388: stur            w1, [x3, #0x33]
    // 0xb1438c: r1 = Null
    //     0xb1438c: mov             x1, NULL
    // 0xb14390: r2 = 6
    //     0xb14390: movz            x2, #0x6
    // 0xb14394: r0 = AllocateArray()
    //     0xb14394: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb14398: mov             x2, x0
    // 0xb1439c: ldur            x0, [fp, #-0x20]
    // 0xb143a0: stur            x2, [fp, #-0x28]
    // 0xb143a4: StoreField: r2->field_f = r0
    //     0xb143a4: stur            w0, [x2, #0xf]
    // 0xb143a8: r16 = Instance_SizedBox
    //     0xb143a8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb143ac: ldr             x16, [x16, #0x578]
    // 0xb143b0: StoreField: r2->field_13 = r16
    //     0xb143b0: stur            w16, [x2, #0x13]
    // 0xb143b4: ldur            x0, [fp, #-0x30]
    // 0xb143b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb143b8: stur            w0, [x2, #0x17]
    // 0xb143bc: r1 = <Widget>
    //     0xb143bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb143c0: r0 = AllocateGrowableArray()
    //     0xb143c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb143c4: mov             x1, x0
    // 0xb143c8: ldur            x0, [fp, #-0x28]
    // 0xb143cc: stur            x1, [fp, #-0x20]
    // 0xb143d0: StoreField: r1->field_f = r0
    //     0xb143d0: stur            w0, [x1, #0xf]
    // 0xb143d4: r2 = 6
    //     0xb143d4: movz            x2, #0x6
    // 0xb143d8: StoreField: r1->field_b = r2
    //     0xb143d8: stur            w2, [x1, #0xb]
    // 0xb143dc: r0 = Column()
    //     0xb143dc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb143e0: mov             x1, x0
    // 0xb143e4: r0 = Instance_Axis
    //     0xb143e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb143e8: stur            x1, [fp, #-0x28]
    // 0xb143ec: StoreField: r1->field_f = r0
    //     0xb143ec: stur            w0, [x1, #0xf]
    // 0xb143f0: r2 = Instance_MainAxisAlignment
    //     0xb143f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb143f4: ldr             x2, [x2, #0xa08]
    // 0xb143f8: StoreField: r1->field_13 = r2
    //     0xb143f8: stur            w2, [x1, #0x13]
    // 0xb143fc: r3 = Instance_MainAxisSize
    //     0xb143fc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb14400: ldr             x3, [x3, #0xa10]
    // 0xb14404: ArrayStore: r1[0] = r3  ; List_4
    //     0xb14404: stur            w3, [x1, #0x17]
    // 0xb14408: r4 = Instance_CrossAxisAlignment
    //     0xb14408: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1440c: ldr             x4, [x4, #0x890]
    // 0xb14410: StoreField: r1->field_1b = r4
    //     0xb14410: stur            w4, [x1, #0x1b]
    // 0xb14414: r4 = Instance_VerticalDirection
    //     0xb14414: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb14418: ldr             x4, [x4, #0xa20]
    // 0xb1441c: StoreField: r1->field_23 = r4
    //     0xb1441c: stur            w4, [x1, #0x23]
    // 0xb14420: r5 = Instance_Clip
    //     0xb14420: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb14424: ldr             x5, [x5, #0x38]
    // 0xb14428: StoreField: r1->field_2b = r5
    //     0xb14428: stur            w5, [x1, #0x2b]
    // 0xb1442c: StoreField: r1->field_2f = rZR
    //     0xb1442c: stur            xzr, [x1, #0x2f]
    // 0xb14430: ldur            x6, [fp, #-0x20]
    // 0xb14434: StoreField: r1->field_b = r6
    //     0xb14434: stur            w6, [x1, #0xb]
    // 0xb14438: r0 = Visibility()
    //     0xb14438: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb1443c: mov             x2, x0
    // 0xb14440: ldur            x0, [fp, #-0x28]
    // 0xb14444: stur            x2, [fp, #-0x20]
    // 0xb14448: StoreField: r2->field_b = r0
    //     0xb14448: stur            w0, [x2, #0xb]
    // 0xb1444c: r0 = Instance_SizedBox
    //     0xb1444c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb14450: StoreField: r2->field_f = r0
    //     0xb14450: stur            w0, [x2, #0xf]
    // 0xb14454: ldur            x1, [fp, #-0x10]
    // 0xb14458: StoreField: r2->field_13 = r1
    //     0xb14458: stur            w1, [x2, #0x13]
    // 0xb1445c: r3 = false
    //     0xb1445c: add             x3, NULL, #0x30  ; false
    // 0xb14460: ArrayStore: r2[0] = r3  ; List_4
    //     0xb14460: stur            w3, [x2, #0x17]
    // 0xb14464: StoreField: r2->field_1b = r3
    //     0xb14464: stur            w3, [x2, #0x1b]
    // 0xb14468: StoreField: r2->field_1f = r3
    //     0xb14468: stur            w3, [x2, #0x1f]
    // 0xb1446c: StoreField: r2->field_23 = r3
    //     0xb1446c: stur            w3, [x2, #0x23]
    // 0xb14470: StoreField: r2->field_27 = r3
    //     0xb14470: stur            w3, [x2, #0x27]
    // 0xb14474: StoreField: r2->field_2b = r3
    //     0xb14474: stur            w3, [x2, #0x2b]
    // 0xb14478: ldur            x4, [fp, #-8]
    // 0xb1447c: LoadField: r1 = r4->field_b
    //     0xb1447c: ldur            w1, [x4, #0xb]
    // 0xb14480: DecompressPointer r1
    //     0xb14480: add             x1, x1, HEAP, lsl #32
    // 0xb14484: cmp             w1, NULL
    // 0xb14488: b.eq            #0xb14e80
    // 0xb1448c: LoadField: r5 = r1->field_f
    //     0xb1448c: ldur            w5, [x1, #0xf]
    // 0xb14490: DecompressPointer r5
    //     0xb14490: add             x5, x5, HEAP, lsl #32
    // 0xb14494: LoadField: r1 = r5->field_b
    //     0xb14494: ldur            w1, [x5, #0xb]
    // 0xb14498: DecompressPointer r1
    //     0xb14498: add             x1, x1, HEAP, lsl #32
    // 0xb1449c: cmp             w1, NULL
    // 0xb144a0: b.ne            #0xb144ac
    // 0xb144a4: r1 = Null
    //     0xb144a4: mov             x1, NULL
    // 0xb144a8: b               #0xb144f0
    // 0xb144ac: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb144ac: ldur            w5, [x1, #0x17]
    // 0xb144b0: DecompressPointer r5
    //     0xb144b0: add             x5, x5, HEAP, lsl #32
    // 0xb144b4: cmp             w5, NULL
    // 0xb144b8: b.ne            #0xb144c4
    // 0xb144bc: r1 = Null
    //     0xb144bc: mov             x1, NULL
    // 0xb144c0: b               #0xb144f0
    // 0xb144c4: LoadField: r1 = r5->field_f
    //     0xb144c4: ldur            w1, [x5, #0xf]
    // 0xb144c8: DecompressPointer r1
    //     0xb144c8: add             x1, x1, HEAP, lsl #32
    // 0xb144cc: cmp             w1, NULL
    // 0xb144d0: b.ne            #0xb144dc
    // 0xb144d4: r1 = Null
    //     0xb144d4: mov             x1, NULL
    // 0xb144d8: b               #0xb144f0
    // 0xb144dc: LoadField: r5 = r1->field_b
    //     0xb144dc: ldur            w5, [x1, #0xb]
    // 0xb144e0: cbnz            w5, #0xb144ec
    // 0xb144e4: r1 = false
    //     0xb144e4: add             x1, NULL, #0x30  ; false
    // 0xb144e8: b               #0xb144f0
    // 0xb144ec: r1 = true
    //     0xb144ec: add             x1, NULL, #0x20  ; true
    // 0xb144f0: cmp             w1, NULL
    // 0xb144f4: b.ne            #0xb14500
    // 0xb144f8: r6 = false
    //     0xb144f8: add             x6, NULL, #0x30  ; false
    // 0xb144fc: b               #0xb14504
    // 0xb14500: mov             x6, x1
    // 0xb14504: ldur            x5, [fp, #-0x18]
    // 0xb14508: stur            x6, [fp, #-0x10]
    // 0xb1450c: LoadField: r1 = r5->field_13
    //     0xb1450c: ldur            w1, [x5, #0x13]
    // 0xb14510: DecompressPointer r1
    //     0xb14510: add             x1, x1, HEAP, lsl #32
    // 0xb14514: r0 = of()
    //     0xb14514: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb14518: LoadField: r1 = r0->field_87
    //     0xb14518: ldur            w1, [x0, #0x87]
    // 0xb1451c: DecompressPointer r1
    //     0xb1451c: add             x1, x1, HEAP, lsl #32
    // 0xb14520: LoadField: r0 = r1->field_2f
    //     0xb14520: ldur            w0, [x1, #0x2f]
    // 0xb14524: DecompressPointer r0
    //     0xb14524: add             x0, x0, HEAP, lsl #32
    // 0xb14528: cmp             w0, NULL
    // 0xb1452c: b.ne            #0xb14538
    // 0xb14530: r1 = Null
    //     0xb14530: mov             x1, NULL
    // 0xb14534: b               #0xb1455c
    // 0xb14538: r16 = Instance_Color
    //     0xb14538: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1453c: r30 = 14.000000
    //     0xb1453c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb14540: ldr             lr, [lr, #0x1d8]
    // 0xb14544: stp             lr, x16, [SP]
    // 0xb14548: mov             x1, x0
    // 0xb1454c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb1454c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb14550: ldr             x4, [x4, #0x9b8]
    // 0xb14554: r0 = copyWith()
    //     0xb14554: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb14558: mov             x1, x0
    // 0xb1455c: ldur            x0, [fp, #-8]
    // 0xb14560: stur            x1, [fp, #-0x28]
    // 0xb14564: r0 = Text()
    //     0xb14564: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb14568: mov             x3, x0
    // 0xb1456c: r0 = "Real images from customers"
    //     0xb1456c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0xb14570: ldr             x0, [x0, #0x88]
    // 0xb14574: stur            x3, [fp, #-0x30]
    // 0xb14578: StoreField: r3->field_b = r0
    //     0xb14578: stur            w0, [x3, #0xb]
    // 0xb1457c: ldur            x0, [fp, #-0x28]
    // 0xb14580: StoreField: r3->field_13 = r0
    //     0xb14580: stur            w0, [x3, #0x13]
    // 0xb14584: ldur            x0, [fp, #-8]
    // 0xb14588: LoadField: r1 = r0->field_b
    //     0xb14588: ldur            w1, [x0, #0xb]
    // 0xb1458c: DecompressPointer r1
    //     0xb1458c: add             x1, x1, HEAP, lsl #32
    // 0xb14590: cmp             w1, NULL
    // 0xb14594: b.eq            #0xb14e84
    // 0xb14598: LoadField: r2 = r1->field_f
    //     0xb14598: ldur            w2, [x1, #0xf]
    // 0xb1459c: DecompressPointer r2
    //     0xb1459c: add             x2, x2, HEAP, lsl #32
    // 0xb145a0: LoadField: r1 = r2->field_b
    //     0xb145a0: ldur            w1, [x2, #0xb]
    // 0xb145a4: DecompressPointer r1
    //     0xb145a4: add             x1, x1, HEAP, lsl #32
    // 0xb145a8: cmp             w1, NULL
    // 0xb145ac: b.ne            #0xb145b8
    // 0xb145b0: r0 = Null
    //     0xb145b0: mov             x0, NULL
    // 0xb145b4: b               #0xb14618
    // 0xb145b8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb145b8: ldur            w2, [x1, #0x17]
    // 0xb145bc: DecompressPointer r2
    //     0xb145bc: add             x2, x2, HEAP, lsl #32
    // 0xb145c0: cmp             w2, NULL
    // 0xb145c4: b.ne            #0xb145d0
    // 0xb145c8: r0 = Null
    //     0xb145c8: mov             x0, NULL
    // 0xb145cc: b               #0xb14618
    // 0xb145d0: LoadField: r4 = r2->field_f
    //     0xb145d0: ldur            w4, [x2, #0xf]
    // 0xb145d4: DecompressPointer r4
    //     0xb145d4: add             x4, x4, HEAP, lsl #32
    // 0xb145d8: stur            x4, [fp, #-0x28]
    // 0xb145dc: cmp             w4, NULL
    // 0xb145e0: b.ne            #0xb145ec
    // 0xb145e4: r0 = Null
    //     0xb145e4: mov             x0, NULL
    // 0xb145e8: b               #0xb14618
    // 0xb145ec: r1 = Function '<anonymous closure>':.
    //     0xb145ec: add             x1, PP, #0x57, lsl #12  ; [pp+0x57950] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xb145f0: ldr             x1, [x1, #0x950]
    // 0xb145f4: r2 = Null
    //     0xb145f4: mov             x2, NULL
    // 0xb145f8: r0 = AllocateClosure()
    //     0xb145f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb145fc: ldur            x16, [fp, #-0x28]
    // 0xb14600: stp             x16, NULL, [SP, #8]
    // 0xb14604: str             x0, [SP]
    // 0xb14608: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb14608: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1460c: r0 = expand()
    //     0xb1460c: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xb14610: str             x0, [SP]
    // 0xb14614: r0 = length()
    //     0xb14614: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xb14618: cmp             w0, NULL
    // 0xb1461c: b.ne            #0xb14628
    // 0xb14620: r0 = 0
    //     0xb14620: movz            x0, #0
    // 0xb14624: b               #0xb14638
    // 0xb14628: r1 = LoadInt32Instr(r0)
    //     0xb14628: sbfx            x1, x0, #1, #0x1f
    //     0xb1462c: tbz             w0, #0, #0xb14634
    //     0xb14630: ldur            x1, [x0, #7]
    // 0xb14634: mov             x0, x1
    // 0xb14638: cmp             x0, #5
    // 0xb1463c: b.le            #0xb14648
    // 0xb14640: r5 = 5
    //     0xb14640: movz            x5, #0x5
    // 0xb14644: b               #0xb14700
    // 0xb14648: ldur            x0, [fp, #-8]
    // 0xb1464c: LoadField: r1 = r0->field_b
    //     0xb1464c: ldur            w1, [x0, #0xb]
    // 0xb14650: DecompressPointer r1
    //     0xb14650: add             x1, x1, HEAP, lsl #32
    // 0xb14654: cmp             w1, NULL
    // 0xb14658: b.eq            #0xb14e88
    // 0xb1465c: LoadField: r2 = r1->field_f
    //     0xb1465c: ldur            w2, [x1, #0xf]
    // 0xb14660: DecompressPointer r2
    //     0xb14660: add             x2, x2, HEAP, lsl #32
    // 0xb14664: LoadField: r1 = r2->field_b
    //     0xb14664: ldur            w1, [x2, #0xb]
    // 0xb14668: DecompressPointer r1
    //     0xb14668: add             x1, x1, HEAP, lsl #32
    // 0xb1466c: cmp             w1, NULL
    // 0xb14670: b.ne            #0xb1467c
    // 0xb14674: r0 = Null
    //     0xb14674: mov             x0, NULL
    // 0xb14678: b               #0xb146dc
    // 0xb1467c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb1467c: ldur            w2, [x1, #0x17]
    // 0xb14680: DecompressPointer r2
    //     0xb14680: add             x2, x2, HEAP, lsl #32
    // 0xb14684: cmp             w2, NULL
    // 0xb14688: b.ne            #0xb14694
    // 0xb1468c: r0 = Null
    //     0xb1468c: mov             x0, NULL
    // 0xb14690: b               #0xb146dc
    // 0xb14694: LoadField: r3 = r2->field_f
    //     0xb14694: ldur            w3, [x2, #0xf]
    // 0xb14698: DecompressPointer r3
    //     0xb14698: add             x3, x3, HEAP, lsl #32
    // 0xb1469c: stur            x3, [fp, #-0x28]
    // 0xb146a0: cmp             w3, NULL
    // 0xb146a4: b.ne            #0xb146b0
    // 0xb146a8: r0 = Null
    //     0xb146a8: mov             x0, NULL
    // 0xb146ac: b               #0xb146dc
    // 0xb146b0: r1 = Function '<anonymous closure>':.
    //     0xb146b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57958] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xb146b4: ldr             x1, [x1, #0x958]
    // 0xb146b8: r2 = Null
    //     0xb146b8: mov             x2, NULL
    // 0xb146bc: r0 = AllocateClosure()
    //     0xb146bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb146c0: ldur            x16, [fp, #-0x28]
    // 0xb146c4: stp             x16, NULL, [SP, #8]
    // 0xb146c8: str             x0, [SP]
    // 0xb146cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb146cc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb146d0: r0 = expand()
    //     0xb146d0: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xb146d4: str             x0, [SP]
    // 0xb146d8: r0 = length()
    //     0xb146d8: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xb146dc: cmp             w0, NULL
    // 0xb146e0: b.ne            #0xb146ec
    // 0xb146e4: r0 = 0
    //     0xb146e4: movz            x0, #0
    // 0xb146e8: b               #0xb146fc
    // 0xb146ec: r1 = LoadInt32Instr(r0)
    //     0xb146ec: sbfx            x1, x0, #1, #0x1f
    //     0xb146f0: tbz             w0, #0, #0xb146f8
    //     0xb146f4: ldur            x1, [x0, #7]
    // 0xb146f8: mov             x0, x1
    // 0xb146fc: mov             x5, x0
    // 0xb14700: ldur            x0, [fp, #-8]
    // 0xb14704: ldur            x4, [fp, #-0x10]
    // 0xb14708: ldur            x3, [fp, #-0x30]
    // 0xb1470c: ldur            x2, [fp, #-0x18]
    // 0xb14710: stur            x5, [fp, #-0x40]
    // 0xb14714: r1 = Function '<anonymous closure>':.
    //     0xb14714: add             x1, PP, #0x57, lsl #12  ; [pp+0x57960] AnonymousClosure: (0xb15004), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb14718: ldr             x1, [x1, #0x960]
    // 0xb1471c: r0 = AllocateClosure()
    //     0xb1471c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb14720: r1 = Function '<anonymous closure>':.
    //     0xb14720: add             x1, PP, #0x57, lsl #12  ; [pp+0x57968] AnonymousClosure: (0x9ba768), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb14724: ldr             x1, [x1, #0x968]
    // 0xb14728: r2 = Null
    //     0xb14728: mov             x2, NULL
    // 0xb1472c: stur            x0, [fp, #-0x28]
    // 0xb14730: r0 = AllocateClosure()
    //     0xb14730: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb14734: stur            x0, [fp, #-0x38]
    // 0xb14738: r0 = ListView()
    //     0xb14738: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1473c: stur            x0, [fp, #-0x48]
    // 0xb14740: r16 = true
    //     0xb14740: add             x16, NULL, #0x20  ; true
    // 0xb14744: r30 = Instance_Axis
    //     0xb14744: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb14748: stp             lr, x16, [SP]
    // 0xb1474c: mov             x1, x0
    // 0xb14750: ldur            x2, [fp, #-0x28]
    // 0xb14754: ldur            x3, [fp, #-0x40]
    // 0xb14758: ldur            x5, [fp, #-0x38]
    // 0xb1475c: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xb1475c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb14760: ldr             x4, [x4, #0x8e8]
    // 0xb14764: r0 = ListView.separated()
    //     0xb14764: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb14768: r0 = SizedBox()
    //     0xb14768: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1476c: mov             x3, x0
    // 0xb14770: r0 = 60.000000
    //     0xb14770: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb14774: ldr             x0, [x0, #0x110]
    // 0xb14778: stur            x3, [fp, #-0x28]
    // 0xb1477c: StoreField: r3->field_13 = r0
    //     0xb1477c: stur            w0, [x3, #0x13]
    // 0xb14780: ldur            x0, [fp, #-0x48]
    // 0xb14784: StoreField: r3->field_b = r0
    //     0xb14784: stur            w0, [x3, #0xb]
    // 0xb14788: r1 = Null
    //     0xb14788: mov             x1, NULL
    // 0xb1478c: r2 = 8
    //     0xb1478c: movz            x2, #0x8
    // 0xb14790: r0 = AllocateArray()
    //     0xb14790: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb14794: mov             x2, x0
    // 0xb14798: ldur            x0, [fp, #-0x30]
    // 0xb1479c: stur            x2, [fp, #-0x38]
    // 0xb147a0: StoreField: r2->field_f = r0
    //     0xb147a0: stur            w0, [x2, #0xf]
    // 0xb147a4: r16 = Instance_SizedBox
    //     0xb147a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb147a8: ldr             x16, [x16, #0x8f0]
    // 0xb147ac: StoreField: r2->field_13 = r16
    //     0xb147ac: stur            w16, [x2, #0x13]
    // 0xb147b0: ldur            x0, [fp, #-0x28]
    // 0xb147b4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb147b4: stur            w0, [x2, #0x17]
    // 0xb147b8: r16 = Instance_SizedBox
    //     0xb147b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb147bc: ldr             x16, [x16, #0x8f0]
    // 0xb147c0: StoreField: r2->field_1b = r16
    //     0xb147c0: stur            w16, [x2, #0x1b]
    // 0xb147c4: r1 = <Widget>
    //     0xb147c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb147c8: r0 = AllocateGrowableArray()
    //     0xb147c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb147cc: mov             x1, x0
    // 0xb147d0: ldur            x0, [fp, #-0x38]
    // 0xb147d4: stur            x1, [fp, #-0x28]
    // 0xb147d8: StoreField: r1->field_f = r0
    //     0xb147d8: stur            w0, [x1, #0xf]
    // 0xb147dc: r2 = 8
    //     0xb147dc: movz            x2, #0x8
    // 0xb147e0: StoreField: r1->field_b = r2
    //     0xb147e0: stur            w2, [x1, #0xb]
    // 0xb147e4: r0 = Column()
    //     0xb147e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb147e8: mov             x1, x0
    // 0xb147ec: r0 = Instance_Axis
    //     0xb147ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb147f0: stur            x1, [fp, #-0x30]
    // 0xb147f4: StoreField: r1->field_f = r0
    //     0xb147f4: stur            w0, [x1, #0xf]
    // 0xb147f8: r2 = Instance_MainAxisAlignment
    //     0xb147f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb147fc: ldr             x2, [x2, #0xa08]
    // 0xb14800: StoreField: r1->field_13 = r2
    //     0xb14800: stur            w2, [x1, #0x13]
    // 0xb14804: r3 = Instance_MainAxisSize
    //     0xb14804: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb14808: ldr             x3, [x3, #0xa10]
    // 0xb1480c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb1480c: stur            w3, [x1, #0x17]
    // 0xb14810: r4 = Instance_CrossAxisAlignment
    //     0xb14810: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb14814: ldr             x4, [x4, #0xa18]
    // 0xb14818: StoreField: r1->field_1b = r4
    //     0xb14818: stur            w4, [x1, #0x1b]
    // 0xb1481c: r5 = Instance_VerticalDirection
    //     0xb1481c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb14820: ldr             x5, [x5, #0xa20]
    // 0xb14824: StoreField: r1->field_23 = r5
    //     0xb14824: stur            w5, [x1, #0x23]
    // 0xb14828: r6 = Instance_Clip
    //     0xb14828: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1482c: ldr             x6, [x6, #0x38]
    // 0xb14830: StoreField: r1->field_2b = r6
    //     0xb14830: stur            w6, [x1, #0x2b]
    // 0xb14834: StoreField: r1->field_2f = rZR
    //     0xb14834: stur            xzr, [x1, #0x2f]
    // 0xb14838: ldur            x7, [fp, #-0x28]
    // 0xb1483c: StoreField: r1->field_b = r7
    //     0xb1483c: stur            w7, [x1, #0xb]
    // 0xb14840: r0 = Padding()
    //     0xb14840: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb14844: mov             x1, x0
    // 0xb14848: r0 = Instance_EdgeInsets
    //     0xb14848: add             x0, PP, #0x52, lsl #12  ; [pp+0x52468] Obj!EdgeInsets@d58a01
    //     0xb1484c: ldr             x0, [x0, #0x468]
    // 0xb14850: stur            x1, [fp, #-0x28]
    // 0xb14854: StoreField: r1->field_f = r0
    //     0xb14854: stur            w0, [x1, #0xf]
    // 0xb14858: ldur            x0, [fp, #-0x30]
    // 0xb1485c: StoreField: r1->field_b = r0
    //     0xb1485c: stur            w0, [x1, #0xb]
    // 0xb14860: r0 = Visibility()
    //     0xb14860: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb14864: mov             x3, x0
    // 0xb14868: ldur            x0, [fp, #-0x28]
    // 0xb1486c: stur            x3, [fp, #-0x48]
    // 0xb14870: StoreField: r3->field_b = r0
    //     0xb14870: stur            w0, [x3, #0xb]
    // 0xb14874: r0 = Instance_SizedBox
    //     0xb14874: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb14878: StoreField: r3->field_f = r0
    //     0xb14878: stur            w0, [x3, #0xf]
    // 0xb1487c: ldur            x1, [fp, #-0x10]
    // 0xb14880: StoreField: r3->field_13 = r1
    //     0xb14880: stur            w1, [x3, #0x13]
    // 0xb14884: r4 = false
    //     0xb14884: add             x4, NULL, #0x30  ; false
    // 0xb14888: ArrayStore: r3[0] = r4  ; List_4
    //     0xb14888: stur            w4, [x3, #0x17]
    // 0xb1488c: StoreField: r3->field_1b = r4
    //     0xb1488c: stur            w4, [x3, #0x1b]
    // 0xb14890: StoreField: r3->field_1f = r4
    //     0xb14890: stur            w4, [x3, #0x1f]
    // 0xb14894: StoreField: r3->field_23 = r4
    //     0xb14894: stur            w4, [x3, #0x23]
    // 0xb14898: StoreField: r3->field_27 = r4
    //     0xb14898: stur            w4, [x3, #0x27]
    // 0xb1489c: StoreField: r3->field_2b = r4
    //     0xb1489c: stur            w4, [x3, #0x2b]
    // 0xb148a0: ldur            x5, [fp, #-8]
    // 0xb148a4: LoadField: r1 = r5->field_b
    //     0xb148a4: ldur            w1, [x5, #0xb]
    // 0xb148a8: DecompressPointer r1
    //     0xb148a8: add             x1, x1, HEAP, lsl #32
    // 0xb148ac: cmp             w1, NULL
    // 0xb148b0: b.eq            #0xb14e8c
    // 0xb148b4: LoadField: r6 = r1->field_f
    //     0xb148b4: ldur            w6, [x1, #0xf]
    // 0xb148b8: DecompressPointer r6
    //     0xb148b8: add             x6, x6, HEAP, lsl #32
    // 0xb148bc: stur            x6, [fp, #-0x38]
    // 0xb148c0: LoadField: r7 = r6->field_b
    //     0xb148c0: ldur            w7, [x6, #0xb]
    // 0xb148c4: DecompressPointer r7
    //     0xb148c4: add             x7, x7, HEAP, lsl #32
    // 0xb148c8: stur            x7, [fp, #-0x30]
    // 0xb148cc: cmp             w7, NULL
    // 0xb148d0: b.ne            #0xb148dc
    // 0xb148d4: r2 = Null
    //     0xb148d4: mov             x2, NULL
    // 0xb148d8: b               #0xb148f8
    // 0xb148dc: LoadField: r2 = r7->field_13
    //     0xb148dc: ldur            w2, [x7, #0x13]
    // 0xb148e0: DecompressPointer r2
    //     0xb148e0: add             x2, x2, HEAP, lsl #32
    // 0xb148e4: LoadField: r8 = r2->field_b
    //     0xb148e4: ldur            w8, [x2, #0xb]
    // 0xb148e8: cbnz            w8, #0xb148f4
    // 0xb148ec: r2 = false
    //     0xb148ec: add             x2, NULL, #0x30  ; false
    // 0xb148f0: b               #0xb148f8
    // 0xb148f4: r2 = true
    //     0xb148f4: add             x2, NULL, #0x20  ; true
    // 0xb148f8: cmp             w2, NULL
    // 0xb148fc: b.ne            #0xb14908
    // 0xb14900: r8 = false
    //     0xb14900: add             x8, NULL, #0x30  ; false
    // 0xb14904: b               #0xb1490c
    // 0xb14908: mov             x8, x2
    // 0xb1490c: stur            x8, [fp, #-0x28]
    // 0xb14910: LoadField: r9 = r1->field_1f
    //     0xb14910: ldur            w9, [x1, #0x1f]
    // 0xb14914: DecompressPointer r9
    //     0xb14914: add             x9, x9, HEAP, lsl #32
    // 0xb14918: ldur            x2, [fp, #-0x18]
    // 0xb1491c: stur            x9, [fp, #-0x10]
    // 0xb14920: r1 = Function '<anonymous closure>':.
    //     0xb14920: add             x1, PP, #0x57, lsl #12  ; [pp+0x57970] AnonymousClosure: (0xa8c4e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xb14924: ldr             x1, [x1, #0x970]
    // 0xb14928: r0 = AllocateClosure()
    //     0xb14928: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1492c: stur            x0, [fp, #-0x50]
    // 0xb14930: r0 = ReviewWidget()
    //     0xb14930: bl              #0xb14eb8  ; AllocateReviewWidgetStub -> ReviewWidget (size=0x1c)
    // 0xb14934: mov             x3, x0
    // 0xb14938: ldur            x0, [fp, #-0x50]
    // 0xb1493c: stur            x3, [fp, #-0x58]
    // 0xb14940: StoreField: r3->field_f = r0
    //     0xb14940: stur            w0, [x3, #0xf]
    // 0xb14944: ldur            x0, [fp, #-0x30]
    // 0xb14948: StoreField: r3->field_b = r0
    //     0xb14948: stur            w0, [x3, #0xb]
    // 0xb1494c: ldur            x0, [fp, #-0x10]
    // 0xb14950: StoreField: r3->field_13 = r0
    //     0xb14950: stur            w0, [x3, #0x13]
    // 0xb14954: ldur            x2, [fp, #-0x18]
    // 0xb14958: r1 = Function '<anonymous closure>':.
    //     0xb14958: add             x1, PP, #0x57, lsl #12  ; [pp+0x57978] AnonymousClosure: (0xb14f78), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb1495c: ldr             x1, [x1, #0x978]
    // 0xb14960: r0 = AllocateClosure()
    //     0xb14960: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb14964: mov             x1, x0
    // 0xb14968: ldur            x0, [fp, #-0x58]
    // 0xb1496c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1496c: stur            w1, [x0, #0x17]
    // 0xb14970: r1 = Null
    //     0xb14970: mov             x1, NULL
    // 0xb14974: r2 = 4
    //     0xb14974: movz            x2, #0x4
    // 0xb14978: r0 = AllocateArray()
    //     0xb14978: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1497c: mov             x2, x0
    // 0xb14980: ldur            x0, [fp, #-0x58]
    // 0xb14984: stur            x2, [fp, #-0x10]
    // 0xb14988: StoreField: r2->field_f = r0
    //     0xb14988: stur            w0, [x2, #0xf]
    // 0xb1498c: r16 = Instance_SizedBox
    //     0xb1498c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb14990: ldr             x16, [x16, #0x578]
    // 0xb14994: StoreField: r2->field_13 = r16
    //     0xb14994: stur            w16, [x2, #0x13]
    // 0xb14998: r1 = <Widget>
    //     0xb14998: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1499c: r0 = AllocateGrowableArray()
    //     0xb1499c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb149a0: mov             x2, x0
    // 0xb149a4: ldur            x0, [fp, #-0x10]
    // 0xb149a8: stur            x2, [fp, #-0x30]
    // 0xb149ac: StoreField: r2->field_f = r0
    //     0xb149ac: stur            w0, [x2, #0xf]
    // 0xb149b0: r0 = 4
    //     0xb149b0: movz            x0, #0x4
    // 0xb149b4: StoreField: r2->field_b = r0
    //     0xb149b4: stur            w0, [x2, #0xb]
    // 0xb149b8: ldur            x0, [fp, #-0x38]
    // 0xb149bc: LoadField: r1 = r0->field_f
    //     0xb149bc: ldur            w1, [x0, #0xf]
    // 0xb149c0: DecompressPointer r1
    //     0xb149c0: add             x1, x1, HEAP, lsl #32
    // 0xb149c4: cmp             w1, NULL
    // 0xb149c8: b.ne            #0xb149d4
    // 0xb149cc: r0 = Null
    //     0xb149cc: mov             x0, NULL
    // 0xb149d0: b               #0xb149dc
    // 0xb149d4: LoadField: r0 = r1->field_f
    //     0xb149d4: ldur            w0, [x1, #0xf]
    // 0xb149d8: DecompressPointer r0
    //     0xb149d8: add             x0, x0, HEAP, lsl #32
    // 0xb149dc: cmp             w0, NULL
    // 0xb149e0: b.ne            #0xb149ec
    // 0xb149e4: r0 = 0
    //     0xb149e4: movz            x0, #0
    // 0xb149e8: b               #0xb149fc
    // 0xb149ec: r1 = LoadInt32Instr(r0)
    //     0xb149ec: sbfx            x1, x0, #1, #0x1f
    //     0xb149f0: tbz             w0, #0, #0xb149f8
    //     0xb149f4: ldur            x1, [x0, #7]
    // 0xb149f8: mov             x0, x1
    // 0xb149fc: cmp             x0, #3
    // 0xb14a00: b.lt            #0xb14cc0
    // 0xb14a04: ldur            x0, [fp, #-8]
    // 0xb14a08: ldur            x3, [fp, #-0x18]
    // 0xb14a0c: LoadField: r1 = r3->field_13
    //     0xb14a0c: ldur            w1, [x3, #0x13]
    // 0xb14a10: DecompressPointer r1
    //     0xb14a10: add             x1, x1, HEAP, lsl #32
    // 0xb14a14: r0 = of()
    //     0xb14a14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb14a18: LoadField: r1 = r0->field_5b
    //     0xb14a18: ldur            w1, [x0, #0x5b]
    // 0xb14a1c: DecompressPointer r1
    //     0xb14a1c: add             x1, x1, HEAP, lsl #32
    // 0xb14a20: stur            x1, [fp, #-0x10]
    // 0xb14a24: r0 = Radius()
    //     0xb14a24: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb14a28: d0 = 30.000000
    //     0xb14a28: fmov            d0, #30.00000000
    // 0xb14a2c: stur            x0, [fp, #-0x38]
    // 0xb14a30: StoreField: r0->field_7 = d0
    //     0xb14a30: stur            d0, [x0, #7]
    // 0xb14a34: StoreField: r0->field_f = d0
    //     0xb14a34: stur            d0, [x0, #0xf]
    // 0xb14a38: r0 = BorderRadius()
    //     0xb14a38: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb14a3c: mov             x1, x0
    // 0xb14a40: ldur            x0, [fp, #-0x38]
    // 0xb14a44: stur            x1, [fp, #-0x50]
    // 0xb14a48: StoreField: r1->field_7 = r0
    //     0xb14a48: stur            w0, [x1, #7]
    // 0xb14a4c: StoreField: r1->field_b = r0
    //     0xb14a4c: stur            w0, [x1, #0xb]
    // 0xb14a50: StoreField: r1->field_f = r0
    //     0xb14a50: stur            w0, [x1, #0xf]
    // 0xb14a54: StoreField: r1->field_13 = r0
    //     0xb14a54: stur            w0, [x1, #0x13]
    // 0xb14a58: r0 = BoxDecoration()
    //     0xb14a58: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb14a5c: mov             x3, x0
    // 0xb14a60: ldur            x0, [fp, #-0x10]
    // 0xb14a64: stur            x3, [fp, #-0x38]
    // 0xb14a68: StoreField: r3->field_7 = r0
    //     0xb14a68: stur            w0, [x3, #7]
    // 0xb14a6c: ldur            x0, [fp, #-0x50]
    // 0xb14a70: StoreField: r3->field_13 = r0
    //     0xb14a70: stur            w0, [x3, #0x13]
    // 0xb14a74: r0 = Instance_BoxShape
    //     0xb14a74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb14a78: ldr             x0, [x0, #0x80]
    // 0xb14a7c: StoreField: r3->field_23 = r0
    //     0xb14a7c: stur            w0, [x3, #0x23]
    // 0xb14a80: r1 = Null
    //     0xb14a80: mov             x1, NULL
    // 0xb14a84: r2 = 6
    //     0xb14a84: movz            x2, #0x6
    // 0xb14a88: r0 = AllocateArray()
    //     0xb14a88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb14a8c: r16 = "View All Reviews ("
    //     0xb14a8c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52480] "View All Reviews ("
    //     0xb14a90: ldr             x16, [x16, #0x480]
    // 0xb14a94: StoreField: r0->field_f = r16
    //     0xb14a94: stur            w16, [x0, #0xf]
    // 0xb14a98: ldur            x1, [fp, #-8]
    // 0xb14a9c: LoadField: r2 = r1->field_b
    //     0xb14a9c: ldur            w2, [x1, #0xb]
    // 0xb14aa0: DecompressPointer r2
    //     0xb14aa0: add             x2, x2, HEAP, lsl #32
    // 0xb14aa4: cmp             w2, NULL
    // 0xb14aa8: b.eq            #0xb14e90
    // 0xb14aac: LoadField: r1 = r2->field_f
    //     0xb14aac: ldur            w1, [x2, #0xf]
    // 0xb14ab0: DecompressPointer r1
    //     0xb14ab0: add             x1, x1, HEAP, lsl #32
    // 0xb14ab4: LoadField: r2 = r1->field_f
    //     0xb14ab4: ldur            w2, [x1, #0xf]
    // 0xb14ab8: DecompressPointer r2
    //     0xb14ab8: add             x2, x2, HEAP, lsl #32
    // 0xb14abc: cmp             w2, NULL
    // 0xb14ac0: b.ne            #0xb14acc
    // 0xb14ac4: r1 = Null
    //     0xb14ac4: mov             x1, NULL
    // 0xb14ac8: b               #0xb14ad4
    // 0xb14acc: LoadField: r1 = r2->field_f
    //     0xb14acc: ldur            w1, [x2, #0xf]
    // 0xb14ad0: DecompressPointer r1
    //     0xb14ad0: add             x1, x1, HEAP, lsl #32
    // 0xb14ad4: cmp             w1, NULL
    // 0xb14ad8: b.ne            #0xb14ae0
    // 0xb14adc: r1 = ""
    //     0xb14adc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb14ae0: ldur            x2, [fp, #-0x18]
    // 0xb14ae4: StoreField: r0->field_13 = r1
    //     0xb14ae4: stur            w1, [x0, #0x13]
    // 0xb14ae8: r16 = ")"
    //     0xb14ae8: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb14aec: ArrayStore: r0[0] = r16  ; List_4
    //     0xb14aec: stur            w16, [x0, #0x17]
    // 0xb14af0: str             x0, [SP]
    // 0xb14af4: r0 = _interpolate()
    //     0xb14af4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb14af8: ldur            x2, [fp, #-0x18]
    // 0xb14afc: stur            x0, [fp, #-8]
    // 0xb14b00: LoadField: r1 = r2->field_13
    //     0xb14b00: ldur            w1, [x2, #0x13]
    // 0xb14b04: DecompressPointer r1
    //     0xb14b04: add             x1, x1, HEAP, lsl #32
    // 0xb14b08: r0 = of()
    //     0xb14b08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb14b0c: LoadField: r1 = r0->field_87
    //     0xb14b0c: ldur            w1, [x0, #0x87]
    // 0xb14b10: DecompressPointer r1
    //     0xb14b10: add             x1, x1, HEAP, lsl #32
    // 0xb14b14: LoadField: r0 = r1->field_b
    //     0xb14b14: ldur            w0, [x1, #0xb]
    // 0xb14b18: DecompressPointer r0
    //     0xb14b18: add             x0, x0, HEAP, lsl #32
    // 0xb14b1c: cmp             w0, NULL
    // 0xb14b20: b.ne            #0xb14b2c
    // 0xb14b24: r2 = Null
    //     0xb14b24: mov             x2, NULL
    // 0xb14b28: b               #0xb14b50
    // 0xb14b2c: r16 = Instance_Color
    //     0xb14b2c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb14b30: r30 = 14.000000
    //     0xb14b30: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb14b34: ldr             lr, [lr, #0x1d8]
    // 0xb14b38: stp             lr, x16, [SP]
    // 0xb14b3c: mov             x1, x0
    // 0xb14b40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb14b40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb14b44: ldr             x4, [x4, #0x9b8]
    // 0xb14b48: r0 = copyWith()
    //     0xb14b48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb14b4c: mov             x2, x0
    // 0xb14b50: ldur            x0, [fp, #-8]
    // 0xb14b54: ldur            x1, [fp, #-0x30]
    // 0xb14b58: stur            x2, [fp, #-0x10]
    // 0xb14b5c: r0 = Text()
    //     0xb14b5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb14b60: mov             x1, x0
    // 0xb14b64: ldur            x0, [fp, #-8]
    // 0xb14b68: stur            x1, [fp, #-0x50]
    // 0xb14b6c: StoreField: r1->field_b = r0
    //     0xb14b6c: stur            w0, [x1, #0xb]
    // 0xb14b70: ldur            x0, [fp, #-0x10]
    // 0xb14b74: StoreField: r1->field_13 = r0
    //     0xb14b74: stur            w0, [x1, #0x13]
    // 0xb14b78: r0 = Center()
    //     0xb14b78: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb14b7c: mov             x1, x0
    // 0xb14b80: r0 = Instance_Alignment
    //     0xb14b80: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb14b84: ldr             x0, [x0, #0xb10]
    // 0xb14b88: stur            x1, [fp, #-8]
    // 0xb14b8c: StoreField: r1->field_f = r0
    //     0xb14b8c: stur            w0, [x1, #0xf]
    // 0xb14b90: ldur            x0, [fp, #-0x50]
    // 0xb14b94: StoreField: r1->field_b = r0
    //     0xb14b94: stur            w0, [x1, #0xb]
    // 0xb14b98: r0 = Container()
    //     0xb14b98: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb14b9c: stur            x0, [fp, #-0x10]
    // 0xb14ba0: ldur            x16, [fp, #-0x38]
    // 0xb14ba4: r30 = inf
    //     0xb14ba4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb14ba8: ldr             lr, [lr, #0x9f8]
    // 0xb14bac: stp             lr, x16, [SP, #0x10]
    // 0xb14bb0: r16 = 40.000000
    //     0xb14bb0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb14bb4: ldr             x16, [x16, #8]
    // 0xb14bb8: ldur            lr, [fp, #-8]
    // 0xb14bbc: stp             lr, x16, [SP]
    // 0xb14bc0: mov             x1, x0
    // 0xb14bc4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0xb14bc4: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0xb14bc8: ldr             x4, [x4, #0x5d0]
    // 0xb14bcc: r0 = Container()
    //     0xb14bcc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb14bd0: r0 = InkWell()
    //     0xb14bd0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb14bd4: mov             x3, x0
    // 0xb14bd8: ldur            x0, [fp, #-0x10]
    // 0xb14bdc: stur            x3, [fp, #-8]
    // 0xb14be0: StoreField: r3->field_b = r0
    //     0xb14be0: stur            w0, [x3, #0xb]
    // 0xb14be4: ldur            x2, [fp, #-0x18]
    // 0xb14be8: r1 = Function '<anonymous closure>':.
    //     0xb14be8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57980] AnonymousClosure: (0xb14ee8), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb14bec: ldr             x1, [x1, #0x980]
    // 0xb14bf0: r0 = AllocateClosure()
    //     0xb14bf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb14bf4: mov             x1, x0
    // 0xb14bf8: ldur            x0, [fp, #-8]
    // 0xb14bfc: StoreField: r0->field_f = r1
    //     0xb14bfc: stur            w1, [x0, #0xf]
    // 0xb14c00: r1 = true
    //     0xb14c00: add             x1, NULL, #0x20  ; true
    // 0xb14c04: StoreField: r0->field_43 = r1
    //     0xb14c04: stur            w1, [x0, #0x43]
    // 0xb14c08: r2 = Instance_BoxShape
    //     0xb14c08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb14c0c: ldr             x2, [x2, #0x80]
    // 0xb14c10: StoreField: r0->field_47 = r2
    //     0xb14c10: stur            w2, [x0, #0x47]
    // 0xb14c14: StoreField: r0->field_6f = r1
    //     0xb14c14: stur            w1, [x0, #0x6f]
    // 0xb14c18: r2 = false
    //     0xb14c18: add             x2, NULL, #0x30  ; false
    // 0xb14c1c: StoreField: r0->field_73 = r2
    //     0xb14c1c: stur            w2, [x0, #0x73]
    // 0xb14c20: StoreField: r0->field_83 = r1
    //     0xb14c20: stur            w1, [x0, #0x83]
    // 0xb14c24: StoreField: r0->field_7b = r2
    //     0xb14c24: stur            w2, [x0, #0x7b]
    // 0xb14c28: r0 = Padding()
    //     0xb14c28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb14c2c: mov             x2, x0
    // 0xb14c30: r0 = Instance_EdgeInsets
    //     0xb14c30: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb14c34: ldr             x0, [x0, #0x240]
    // 0xb14c38: stur            x2, [fp, #-0x10]
    // 0xb14c3c: StoreField: r2->field_f = r0
    //     0xb14c3c: stur            w0, [x2, #0xf]
    // 0xb14c40: ldur            x0, [fp, #-8]
    // 0xb14c44: StoreField: r2->field_b = r0
    //     0xb14c44: stur            w0, [x2, #0xb]
    // 0xb14c48: ldur            x0, [fp, #-0x30]
    // 0xb14c4c: LoadField: r1 = r0->field_b
    //     0xb14c4c: ldur            w1, [x0, #0xb]
    // 0xb14c50: LoadField: r3 = r0->field_f
    //     0xb14c50: ldur            w3, [x0, #0xf]
    // 0xb14c54: DecompressPointer r3
    //     0xb14c54: add             x3, x3, HEAP, lsl #32
    // 0xb14c58: LoadField: r4 = r3->field_b
    //     0xb14c58: ldur            w4, [x3, #0xb]
    // 0xb14c5c: r3 = LoadInt32Instr(r1)
    //     0xb14c5c: sbfx            x3, x1, #1, #0x1f
    // 0xb14c60: stur            x3, [fp, #-0x40]
    // 0xb14c64: r1 = LoadInt32Instr(r4)
    //     0xb14c64: sbfx            x1, x4, #1, #0x1f
    // 0xb14c68: cmp             x3, x1
    // 0xb14c6c: b.ne            #0xb14c78
    // 0xb14c70: mov             x1, x0
    // 0xb14c74: r0 = _growToNextCapacity()
    //     0xb14c74: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb14c78: ldur            x2, [fp, #-0x30]
    // 0xb14c7c: ldur            x3, [fp, #-0x40]
    // 0xb14c80: add             x0, x3, #1
    // 0xb14c84: lsl             x1, x0, #1
    // 0xb14c88: StoreField: r2->field_b = r1
    //     0xb14c88: stur            w1, [x2, #0xb]
    // 0xb14c8c: LoadField: r1 = r2->field_f
    //     0xb14c8c: ldur            w1, [x2, #0xf]
    // 0xb14c90: DecompressPointer r1
    //     0xb14c90: add             x1, x1, HEAP, lsl #32
    // 0xb14c94: ldur            x0, [fp, #-0x10]
    // 0xb14c98: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb14c98: add             x25, x1, x3, lsl #2
    //     0xb14c9c: add             x25, x25, #0xf
    //     0xb14ca0: str             w0, [x25]
    //     0xb14ca4: tbz             w0, #0, #0xb14cc0
    //     0xb14ca8: ldurb           w16, [x1, #-1]
    //     0xb14cac: ldurb           w17, [x0, #-1]
    //     0xb14cb0: and             x16, x17, x16, lsr #2
    //     0xb14cb4: tst             x16, HEAP, lsr #32
    //     0xb14cb8: b.eq            #0xb14cc0
    //     0xb14cbc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb14cc0: ldur            x3, [fp, #-0x20]
    // 0xb14cc4: ldur            x0, [fp, #-0x48]
    // 0xb14cc8: ldur            x1, [fp, #-0x28]
    // 0xb14ccc: r0 = Column()
    //     0xb14ccc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb14cd0: mov             x1, x0
    // 0xb14cd4: r0 = Instance_Axis
    //     0xb14cd4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb14cd8: stur            x1, [fp, #-8]
    // 0xb14cdc: StoreField: r1->field_f = r0
    //     0xb14cdc: stur            w0, [x1, #0xf]
    // 0xb14ce0: r2 = Instance_MainAxisAlignment
    //     0xb14ce0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb14ce4: ldr             x2, [x2, #0xa08]
    // 0xb14ce8: StoreField: r1->field_13 = r2
    //     0xb14ce8: stur            w2, [x1, #0x13]
    // 0xb14cec: r3 = Instance_MainAxisSize
    //     0xb14cec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb14cf0: ldr             x3, [x3, #0xa10]
    // 0xb14cf4: ArrayStore: r1[0] = r3  ; List_4
    //     0xb14cf4: stur            w3, [x1, #0x17]
    // 0xb14cf8: r4 = Instance_CrossAxisAlignment
    //     0xb14cf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb14cfc: ldr             x4, [x4, #0xa18]
    // 0xb14d00: StoreField: r1->field_1b = r4
    //     0xb14d00: stur            w4, [x1, #0x1b]
    // 0xb14d04: r5 = Instance_VerticalDirection
    //     0xb14d04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb14d08: ldr             x5, [x5, #0xa20]
    // 0xb14d0c: StoreField: r1->field_23 = r5
    //     0xb14d0c: stur            w5, [x1, #0x23]
    // 0xb14d10: r6 = Instance_Clip
    //     0xb14d10: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb14d14: ldr             x6, [x6, #0x38]
    // 0xb14d18: StoreField: r1->field_2b = r6
    //     0xb14d18: stur            w6, [x1, #0x2b]
    // 0xb14d1c: StoreField: r1->field_2f = rZR
    //     0xb14d1c: stur            xzr, [x1, #0x2f]
    // 0xb14d20: ldur            x7, [fp, #-0x30]
    // 0xb14d24: StoreField: r1->field_b = r7
    //     0xb14d24: stur            w7, [x1, #0xb]
    // 0xb14d28: r0 = Visibility()
    //     0xb14d28: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb14d2c: mov             x3, x0
    // 0xb14d30: ldur            x0, [fp, #-8]
    // 0xb14d34: stur            x3, [fp, #-0x10]
    // 0xb14d38: StoreField: r3->field_b = r0
    //     0xb14d38: stur            w0, [x3, #0xb]
    // 0xb14d3c: r0 = Instance_SizedBox
    //     0xb14d3c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb14d40: StoreField: r3->field_f = r0
    //     0xb14d40: stur            w0, [x3, #0xf]
    // 0xb14d44: ldur            x0, [fp, #-0x28]
    // 0xb14d48: StoreField: r3->field_13 = r0
    //     0xb14d48: stur            w0, [x3, #0x13]
    // 0xb14d4c: r0 = false
    //     0xb14d4c: add             x0, NULL, #0x30  ; false
    // 0xb14d50: ArrayStore: r3[0] = r0  ; List_4
    //     0xb14d50: stur            w0, [x3, #0x17]
    // 0xb14d54: StoreField: r3->field_1b = r0
    //     0xb14d54: stur            w0, [x3, #0x1b]
    // 0xb14d58: StoreField: r3->field_1f = r0
    //     0xb14d58: stur            w0, [x3, #0x1f]
    // 0xb14d5c: StoreField: r3->field_23 = r0
    //     0xb14d5c: stur            w0, [x3, #0x23]
    // 0xb14d60: StoreField: r3->field_27 = r0
    //     0xb14d60: stur            w0, [x3, #0x27]
    // 0xb14d64: StoreField: r3->field_2b = r0
    //     0xb14d64: stur            w0, [x3, #0x2b]
    // 0xb14d68: r1 = Null
    //     0xb14d68: mov             x1, NULL
    // 0xb14d6c: r2 = 8
    //     0xb14d6c: movz            x2, #0x8
    // 0xb14d70: r0 = AllocateArray()
    //     0xb14d70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb14d74: mov             x2, x0
    // 0xb14d78: ldur            x0, [fp, #-0x20]
    // 0xb14d7c: stur            x2, [fp, #-8]
    // 0xb14d80: StoreField: r2->field_f = r0
    //     0xb14d80: stur            w0, [x2, #0xf]
    // 0xb14d84: r16 = Instance_SizedBox
    //     0xb14d84: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0xb14d88: ldr             x16, [x16, #0xd68]
    // 0xb14d8c: StoreField: r2->field_13 = r16
    //     0xb14d8c: stur            w16, [x2, #0x13]
    // 0xb14d90: ldur            x0, [fp, #-0x48]
    // 0xb14d94: ArrayStore: r2[0] = r0  ; List_4
    //     0xb14d94: stur            w0, [x2, #0x17]
    // 0xb14d98: ldur            x0, [fp, #-0x10]
    // 0xb14d9c: StoreField: r2->field_1b = r0
    //     0xb14d9c: stur            w0, [x2, #0x1b]
    // 0xb14da0: r1 = <Widget>
    //     0xb14da0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb14da4: r0 = AllocateGrowableArray()
    //     0xb14da4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb14da8: mov             x1, x0
    // 0xb14dac: ldur            x0, [fp, #-8]
    // 0xb14db0: stur            x1, [fp, #-0x10]
    // 0xb14db4: StoreField: r1->field_f = r0
    //     0xb14db4: stur            w0, [x1, #0xf]
    // 0xb14db8: r0 = 8
    //     0xb14db8: movz            x0, #0x8
    // 0xb14dbc: StoreField: r1->field_b = r0
    //     0xb14dbc: stur            w0, [x1, #0xb]
    // 0xb14dc0: r0 = Column()
    //     0xb14dc0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb14dc4: mov             x1, x0
    // 0xb14dc8: r0 = Instance_Axis
    //     0xb14dc8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb14dcc: stur            x1, [fp, #-8]
    // 0xb14dd0: StoreField: r1->field_f = r0
    //     0xb14dd0: stur            w0, [x1, #0xf]
    // 0xb14dd4: r0 = Instance_MainAxisAlignment
    //     0xb14dd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb14dd8: ldr             x0, [x0, #0xa08]
    // 0xb14ddc: StoreField: r1->field_13 = r0
    //     0xb14ddc: stur            w0, [x1, #0x13]
    // 0xb14de0: r0 = Instance_MainAxisSize
    //     0xb14de0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb14de4: ldr             x0, [x0, #0xa10]
    // 0xb14de8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb14de8: stur            w0, [x1, #0x17]
    // 0xb14dec: r0 = Instance_CrossAxisAlignment
    //     0xb14dec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb14df0: ldr             x0, [x0, #0xa18]
    // 0xb14df4: StoreField: r1->field_1b = r0
    //     0xb14df4: stur            w0, [x1, #0x1b]
    // 0xb14df8: r0 = Instance_VerticalDirection
    //     0xb14df8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb14dfc: ldr             x0, [x0, #0xa20]
    // 0xb14e00: StoreField: r1->field_23 = r0
    //     0xb14e00: stur            w0, [x1, #0x23]
    // 0xb14e04: r0 = Instance_Clip
    //     0xb14e04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb14e08: ldr             x0, [x0, #0x38]
    // 0xb14e0c: StoreField: r1->field_2b = r0
    //     0xb14e0c: stur            w0, [x1, #0x2b]
    // 0xb14e10: StoreField: r1->field_2f = rZR
    //     0xb14e10: stur            xzr, [x1, #0x2f]
    // 0xb14e14: ldur            x0, [fp, #-0x10]
    // 0xb14e18: StoreField: r1->field_b = r0
    //     0xb14e18: stur            w0, [x1, #0xb]
    // 0xb14e1c: r0 = Padding()
    //     0xb14e1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb14e20: r1 = Instance_EdgeInsets
    //     0xb14e20: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb14e24: ldr             x1, [x1, #0xf30]
    // 0xb14e28: StoreField: r0->field_f = r1
    //     0xb14e28: stur            w1, [x0, #0xf]
    // 0xb14e2c: ldur            x1, [fp, #-8]
    // 0xb14e30: StoreField: r0->field_b = r1
    //     0xb14e30: stur            w1, [x0, #0xb]
    // 0xb14e34: b               #0xb14e40
    // 0xb14e38: r0 = Instance_Center
    //     0xb14e38: add             x0, PP, #0x32, lsl #12  ; [pp+0x326f0] Obj!Center@d68321
    //     0xb14e3c: ldr             x0, [x0, #0x6f0]
    // 0xb14e40: LeaveFrame
    //     0xb14e40: mov             SP, fp
    //     0xb14e44: ldp             fp, lr, [SP], #0x10
    // 0xb14e48: ret
    //     0xb14e48: ret             
    // 0xb14e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14e4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14e50: b               #0xb1269c
    // 0xb14e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14e90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14e90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb14ee8, size: 0x90
    // 0xb14ee8: EnterFrame
    //     0xb14ee8: stp             fp, lr, [SP, #-0x10]!
    //     0xb14eec: mov             fp, SP
    // 0xb14ef0: AllocStack(0x20)
    //     0xb14ef0: sub             SP, SP, #0x20
    // 0xb14ef4: SetupParameters()
    //     0xb14ef4: ldr             x0, [fp, #0x10]
    //     0xb14ef8: ldur            w1, [x0, #0x17]
    //     0xb14efc: add             x1, x1, HEAP, lsl #32
    // 0xb14f00: CheckStackOverflow
    //     0xb14f00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14f04: cmp             SP, x16
    //     0xb14f08: b.ls            #0xb14f6c
    // 0xb14f0c: LoadField: r0 = r1->field_f
    //     0xb14f0c: ldur            w0, [x1, #0xf]
    // 0xb14f10: DecompressPointer r0
    //     0xb14f10: add             x0, x0, HEAP, lsl #32
    // 0xb14f14: LoadField: r1 = r0->field_b
    //     0xb14f14: ldur            w1, [x0, #0xb]
    // 0xb14f18: DecompressPointer r1
    //     0xb14f18: add             x1, x1, HEAP, lsl #32
    // 0xb14f1c: cmp             w1, NULL
    // 0xb14f20: b.eq            #0xb14f74
    // 0xb14f24: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb14f24: ldur            w0, [x1, #0x17]
    // 0xb14f28: DecompressPointer r0
    //     0xb14f28: add             x0, x0, HEAP, lsl #32
    // 0xb14f2c: r16 = "view_all"
    //     0xb14f2c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xb14f30: ldr             x16, [x16, #0xba0]
    // 0xb14f34: stp             x16, x0, [SP, #0x10]
    // 0xb14f38: r16 = ""
    //     0xb14f38: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb14f3c: r30 = false
    //     0xb14f3c: add             lr, NULL, #0x30  ; false
    // 0xb14f40: stp             lr, x16, [SP]
    // 0xb14f44: r4 = 0
    //     0xb14f44: movz            x4, #0
    // 0xb14f48: ldr             x0, [SP, #0x18]
    // 0xb14f4c: r16 = UnlinkedCall_0x613b5c
    //     0xb14f4c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57988] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb14f50: add             x16, x16, #0x988
    // 0xb14f54: ldp             x5, lr, [x16]
    // 0xb14f58: blr             lr
    // 0xb14f5c: r0 = Null
    //     0xb14f5c: mov             x0, NULL
    // 0xb14f60: LeaveFrame
    //     0xb14f60: mov             SP, fp
    //     0xb14f64: ldp             fp, lr, [SP], #0x10
    // 0xb14f68: ret
    //     0xb14f68: ret             
    // 0xb14f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14f6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14f70: b               #0xb14f0c
    // 0xb14f74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14f74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String) {
    // ** addr: 0xb14f78, size: 0x8c
    // 0xb14f78: EnterFrame
    //     0xb14f78: stp             fp, lr, [SP, #-0x10]!
    //     0xb14f7c: mov             fp, SP
    // 0xb14f80: AllocStack(0x20)
    //     0xb14f80: sub             SP, SP, #0x20
    // 0xb14f84: SetupParameters()
    //     0xb14f84: ldr             x0, [fp, #0x20]
    //     0xb14f88: ldur            w1, [x0, #0x17]
    //     0xb14f8c: add             x1, x1, HEAP, lsl #32
    // 0xb14f90: CheckStackOverflow
    //     0xb14f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14f94: cmp             SP, x16
    //     0xb14f98: b.ls            #0xb14ff8
    // 0xb14f9c: LoadField: r0 = r1->field_f
    //     0xb14f9c: ldur            w0, [x1, #0xf]
    // 0xb14fa0: DecompressPointer r0
    //     0xb14fa0: add             x0, x0, HEAP, lsl #32
    // 0xb14fa4: LoadField: r1 = r0->field_b
    //     0xb14fa4: ldur            w1, [x0, #0xb]
    // 0xb14fa8: DecompressPointer r1
    //     0xb14fa8: add             x1, x1, HEAP, lsl #32
    // 0xb14fac: cmp             w1, NULL
    // 0xb14fb0: b.eq            #0xb15000
    // 0xb14fb4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb14fb4: ldur            w0, [x1, #0x17]
    // 0xb14fb8: DecompressPointer r0
    //     0xb14fb8: add             x0, x0, HEAP, lsl #32
    // 0xb14fbc: ldr             x16, [fp, #0x18]
    // 0xb14fc0: stp             x16, x0, [SP, #0x10]
    // 0xb14fc4: ldr             x16, [fp, #0x10]
    // 0xb14fc8: r30 = false
    //     0xb14fc8: add             lr, NULL, #0x30  ; false
    // 0xb14fcc: stp             lr, x16, [SP]
    // 0xb14fd0: r4 = 0
    //     0xb14fd0: movz            x4, #0
    // 0xb14fd4: ldr             x0, [SP, #0x18]
    // 0xb14fd8: r16 = UnlinkedCall_0x613b5c
    //     0xb14fd8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57998] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb14fdc: add             x16, x16, #0x998
    // 0xb14fe0: ldp             x5, lr, [x16]
    // 0xb14fe4: blr             lr
    // 0xb14fe8: r0 = Null
    //     0xb14fe8: mov             x0, NULL
    // 0xb14fec: LeaveFrame
    //     0xb14fec: mov             SP, fp
    //     0xb14ff0: ldp             fp, lr, [SP], #0x10
    // 0xb14ff4: ret
    //     0xb14ff4: ret             
    // 0xb14ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14ff8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14ffc: b               #0xb14f9c
    // 0xb15000: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15000: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb15004, size: 0x7d0
    // 0xb15004: EnterFrame
    //     0xb15004: stp             fp, lr, [SP, #-0x10]!
    //     0xb15008: mov             fp, SP
    // 0xb1500c: AllocStack(0x68)
    //     0xb1500c: sub             SP, SP, #0x68
    // 0xb15010: SetupParameters()
    //     0xb15010: ldr             x0, [fp, #0x20]
    //     0xb15014: ldur            w1, [x0, #0x17]
    //     0xb15018: add             x1, x1, HEAP, lsl #32
    //     0xb1501c: stur            x1, [fp, #-8]
    // 0xb15020: CheckStackOverflow
    //     0xb15020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15024: cmp             SP, x16
    //     0xb15028: b.ls            #0xb157c0
    // 0xb1502c: r1 = 3
    //     0xb1502c: movz            x1, #0x3
    // 0xb15030: r0 = AllocateContext()
    //     0xb15030: bl              #0x16f6108  ; AllocateContextStub
    // 0xb15034: mov             x3, x0
    // 0xb15038: ldur            x0, [fp, #-8]
    // 0xb1503c: stur            x3, [fp, #-0x18]
    // 0xb15040: StoreField: r3->field_b = r0
    //     0xb15040: stur            w0, [x3, #0xb]
    // 0xb15044: ldr             x4, [fp, #0x18]
    // 0xb15048: StoreField: r3->field_f = r4
    //     0xb15048: stur            w4, [x3, #0xf]
    // 0xb1504c: ldr             x5, [fp, #0x10]
    // 0xb15050: StoreField: r3->field_13 = r5
    //     0xb15050: stur            w5, [x3, #0x13]
    // 0xb15054: LoadField: r1 = r0->field_f
    //     0xb15054: ldur            w1, [x0, #0xf]
    // 0xb15058: DecompressPointer r1
    //     0xb15058: add             x1, x1, HEAP, lsl #32
    // 0xb1505c: LoadField: r2 = r1->field_b
    //     0xb1505c: ldur            w2, [x1, #0xb]
    // 0xb15060: DecompressPointer r2
    //     0xb15060: add             x2, x2, HEAP, lsl #32
    // 0xb15064: cmp             w2, NULL
    // 0xb15068: b.eq            #0xb157c8
    // 0xb1506c: LoadField: r1 = r2->field_f
    //     0xb1506c: ldur            w1, [x2, #0xf]
    // 0xb15070: DecompressPointer r1
    //     0xb15070: add             x1, x1, HEAP, lsl #32
    // 0xb15074: LoadField: r2 = r1->field_b
    //     0xb15074: ldur            w2, [x1, #0xb]
    // 0xb15078: DecompressPointer r2
    //     0xb15078: add             x2, x2, HEAP, lsl #32
    // 0xb1507c: cmp             w2, NULL
    // 0xb15080: b.ne            #0xb1508c
    // 0xb15084: r2 = Null
    //     0xb15084: mov             x2, NULL
    // 0xb15088: b               #0xb150f4
    // 0xb1508c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb1508c: ldur            w1, [x2, #0x17]
    // 0xb15090: DecompressPointer r1
    //     0xb15090: add             x1, x1, HEAP, lsl #32
    // 0xb15094: cmp             w1, NULL
    // 0xb15098: b.ne            #0xb150a4
    // 0xb1509c: r0 = Null
    //     0xb1509c: mov             x0, NULL
    // 0xb150a0: b               #0xb150f0
    // 0xb150a4: LoadField: r6 = r1->field_f
    //     0xb150a4: ldur            w6, [x1, #0xf]
    // 0xb150a8: DecompressPointer r6
    //     0xb150a8: add             x6, x6, HEAP, lsl #32
    // 0xb150ac: stur            x6, [fp, #-0x10]
    // 0xb150b0: cmp             w6, NULL
    // 0xb150b4: b.ne            #0xb150c0
    // 0xb150b8: r0 = Null
    //     0xb150b8: mov             x0, NULL
    // 0xb150bc: b               #0xb150f0
    // 0xb150c0: r1 = Function '<anonymous closure>':.
    //     0xb150c0: add             x1, PP, #0x57, lsl #12  ; [pp+0x579a8] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xb150c4: ldr             x1, [x1, #0x9a8]
    // 0xb150c8: r2 = Null
    //     0xb150c8: mov             x2, NULL
    // 0xb150cc: r0 = AllocateClosure()
    //     0xb150cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb150d0: ldur            x16, [fp, #-0x10]
    // 0xb150d4: stp             x16, NULL, [SP, #8]
    // 0xb150d8: str             x0, [SP]
    // 0xb150dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb150dc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb150e0: r0 = expand()
    //     0xb150e0: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xb150e4: mov             x1, x0
    // 0xb150e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb150e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb150ec: r0 = toList()
    //     0xb150ec: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0xb150f0: mov             x2, x0
    // 0xb150f4: cmp             w2, NULL
    // 0xb150f8: b.ne            #0xb15108
    // 0xb150fc: ldr             x3, [fp, #0x10]
    // 0xb15100: r1 = Null
    //     0xb15100: mov             x1, NULL
    // 0xb15104: b               #0xb15144
    // 0xb15108: ldr             x3, [fp, #0x10]
    // 0xb1510c: LoadField: r0 = r2->field_b
    //     0xb1510c: ldur            w0, [x2, #0xb]
    // 0xb15110: r4 = LoadInt32Instr(r3)
    //     0xb15110: sbfx            x4, x3, #1, #0x1f
    //     0xb15114: tbz             w3, #0, #0xb1511c
    //     0xb15118: ldur            x4, [x3, #7]
    // 0xb1511c: r1 = LoadInt32Instr(r0)
    //     0xb1511c: sbfx            x1, x0, #1, #0x1f
    // 0xb15120: mov             x0, x1
    // 0xb15124: mov             x1, x4
    // 0xb15128: cmp             x1, x0
    // 0xb1512c: b.hs            #0xb157cc
    // 0xb15130: LoadField: r0 = r2->field_f
    //     0xb15130: ldur            w0, [x2, #0xf]
    // 0xb15134: DecompressPointer r0
    //     0xb15134: add             x0, x0, HEAP, lsl #32
    // 0xb15138: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb15138: add             x16, x0, x4, lsl #2
    //     0xb1513c: ldur            w1, [x16, #0xf]
    // 0xb15140: DecompressPointer r1
    //     0xb15140: add             x1, x1, HEAP, lsl #32
    // 0xb15144: ldur            x2, [fp, #-0x18]
    // 0xb15148: mov             x0, x1
    // 0xb1514c: stur            x1, [fp, #-0x10]
    // 0xb15150: ArrayStore: r2[0] = r0  ; List_4
    //     0xb15150: stur            w0, [x2, #0x17]
    //     0xb15154: tbz             w0, #0, #0xb15170
    //     0xb15158: ldurb           w16, [x2, #-1]
    //     0xb1515c: ldurb           w17, [x0, #-1]
    //     0xb15160: and             x16, x17, x16, lsr #2
    //     0xb15164: tst             x16, HEAP, lsr #32
    //     0xb15168: b.eq            #0xb15170
    //     0xb1516c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb15170: r0 = LoadInt32Instr(r3)
    //     0xb15170: sbfx            x0, x3, #1, #0x1f
    //     0xb15174: tbz             w3, #0, #0xb1517c
    //     0xb15178: ldur            x0, [x3, #7]
    // 0xb1517c: cmp             x0, #4
    // 0xb15180: b.ge            #0xb153d0
    // 0xb15184: str             x1, [SP]
    // 0xb15188: r4 = 0
    //     0xb15188: movz            x4, #0
    // 0xb1518c: ldr             x0, [SP]
    // 0xb15190: r16 = UnlinkedCall_0x613b5c
    //     0xb15190: add             x16, PP, #0x57, lsl #12  ; [pp+0x579b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15194: add             x16, x16, #0x9b0
    // 0xb15198: ldp             x5, lr, [x16]
    // 0xb1519c: blr             lr
    // 0xb151a0: r1 = 60
    //     0xb151a0: movz            x1, #0x3c
    // 0xb151a4: branchIfSmi(r0, 0xb151b0)
    //     0xb151a4: tbz             w0, #0, #0xb151b0
    // 0xb151a8: r1 = LoadClassIdInstr(r0)
    //     0xb151a8: ldur            x1, [x0, #-1]
    //     0xb151ac: ubfx            x1, x1, #0xc, #0x14
    // 0xb151b0: r16 = "image"
    //     0xb151b0: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb151b4: stp             x16, x0, [SP]
    // 0xb151b8: mov             x0, x1
    // 0xb151bc: mov             lr, x0
    // 0xb151c0: ldr             lr, [x21, lr, lsl #3]
    // 0xb151c4: blr             lr
    // 0xb151c8: tbnz            w0, #4, #0xb152f8
    // 0xb151cc: r0 = Radius()
    //     0xb151cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb151d0: d0 = 12.000000
    //     0xb151d0: fmov            d0, #12.00000000
    // 0xb151d4: stur            x0, [fp, #-0x20]
    // 0xb151d8: StoreField: r0->field_7 = d0
    //     0xb151d8: stur            d0, [x0, #7]
    // 0xb151dc: StoreField: r0->field_f = d0
    //     0xb151dc: stur            d0, [x0, #0xf]
    // 0xb151e0: r0 = BorderRadius()
    //     0xb151e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb151e4: mov             x1, x0
    // 0xb151e8: ldur            x0, [fp, #-0x20]
    // 0xb151ec: stur            x1, [fp, #-0x28]
    // 0xb151f0: StoreField: r1->field_7 = r0
    //     0xb151f0: stur            w0, [x1, #7]
    // 0xb151f4: StoreField: r1->field_b = r0
    //     0xb151f4: stur            w0, [x1, #0xb]
    // 0xb151f8: StoreField: r1->field_f = r0
    //     0xb151f8: stur            w0, [x1, #0xf]
    // 0xb151fc: StoreField: r1->field_13 = r0
    //     0xb151fc: stur            w0, [x1, #0x13]
    // 0xb15200: ldur            x16, [fp, #-0x10]
    // 0xb15204: str             x16, [SP]
    // 0xb15208: r4 = 0
    //     0xb15208: movz            x4, #0
    // 0xb1520c: ldr             x0, [SP]
    // 0xb15210: r16 = UnlinkedCall_0x613b5c
    //     0xb15210: add             x16, PP, #0x57, lsl #12  ; [pp+0x579c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15214: add             x16, x16, #0x9c0
    // 0xb15218: ldp             x5, lr, [x16]
    // 0xb1521c: blr             lr
    // 0xb15220: mov             x3, x0
    // 0xb15224: r2 = Null
    //     0xb15224: mov             x2, NULL
    // 0xb15228: r1 = Null
    //     0xb15228: mov             x1, NULL
    // 0xb1522c: stur            x3, [fp, #-0x20]
    // 0xb15230: r4 = 60
    //     0xb15230: movz            x4, #0x3c
    // 0xb15234: branchIfSmi(r0, 0xb15240)
    //     0xb15234: tbz             w0, #0, #0xb15240
    // 0xb15238: r4 = LoadClassIdInstr(r0)
    //     0xb15238: ldur            x4, [x0, #-1]
    //     0xb1523c: ubfx            x4, x4, #0xc, #0x14
    // 0xb15240: sub             x4, x4, #0x5e
    // 0xb15244: cmp             x4, #1
    // 0xb15248: b.ls            #0xb1525c
    // 0xb1524c: r8 = String
    //     0xb1524c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb15250: r3 = Null
    //     0xb15250: add             x3, PP, #0x57, lsl #12  ; [pp+0x579d0] Null
    //     0xb15254: ldr             x3, [x3, #0x9d0]
    // 0xb15258: r0 = String()
    //     0xb15258: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1525c: r1 = Function '<anonymous closure>':.
    //     0xb1525c: add             x1, PP, #0x57, lsl #12  ; [pp+0x579e0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb15260: ldr             x1, [x1, #0x9e0]
    // 0xb15264: r2 = Null
    //     0xb15264: mov             x2, NULL
    // 0xb15268: r0 = AllocateClosure()
    //     0xb15268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1526c: r1 = Function '<anonymous closure>':.
    //     0xb1526c: add             x1, PP, #0x57, lsl #12  ; [pp+0x579e8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb15270: ldr             x1, [x1, #0x9e8]
    // 0xb15274: r2 = Null
    //     0xb15274: mov             x2, NULL
    // 0xb15278: stur            x0, [fp, #-0x30]
    // 0xb1527c: r0 = AllocateClosure()
    //     0xb1527c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb15280: stur            x0, [fp, #-0x38]
    // 0xb15284: r0 = CachedNetworkImage()
    //     0xb15284: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb15288: stur            x0, [fp, #-0x40]
    // 0xb1528c: r16 = 60.000000
    //     0xb1528c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb15290: ldr             x16, [x16, #0x110]
    // 0xb15294: r30 = 60.000000
    //     0xb15294: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb15298: ldr             lr, [lr, #0x110]
    // 0xb1529c: stp             lr, x16, [SP, #0x18]
    // 0xb152a0: r16 = Instance_BoxFit
    //     0xb152a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb152a4: ldr             x16, [x16, #0x118]
    // 0xb152a8: ldur            lr, [fp, #-0x30]
    // 0xb152ac: stp             lr, x16, [SP, #8]
    // 0xb152b0: ldur            x16, [fp, #-0x38]
    // 0xb152b4: str             x16, [SP]
    // 0xb152b8: mov             x1, x0
    // 0xb152bc: ldur            x2, [fp, #-0x20]
    // 0xb152c0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb152c0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb152c4: ldr             x4, [x4, #0xc28]
    // 0xb152c8: r0 = CachedNetworkImage()
    //     0xb152c8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb152cc: r0 = ClipRRect()
    //     0xb152cc: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb152d0: mov             x1, x0
    // 0xb152d4: ldur            x0, [fp, #-0x28]
    // 0xb152d8: StoreField: r1->field_f = r0
    //     0xb152d8: stur            w0, [x1, #0xf]
    // 0xb152dc: r0 = Instance_Clip
    //     0xb152dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb152e0: ldr             x0, [x0, #0x138]
    // 0xb152e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb152e4: stur            w0, [x1, #0x17]
    // 0xb152e8: ldur            x0, [fp, #-0x40]
    // 0xb152ec: StoreField: r1->field_b = r0
    //     0xb152ec: stur            w0, [x1, #0xb]
    // 0xb152f0: mov             x0, x1
    // 0xb152f4: b               #0xb15368
    // 0xb152f8: ldur            x16, [fp, #-0x10]
    // 0xb152fc: str             x16, [SP]
    // 0xb15300: r4 = 0
    //     0xb15300: movz            x4, #0
    // 0xb15304: ldr             x0, [SP]
    // 0xb15308: r16 = UnlinkedCall_0x613b5c
    //     0xb15308: add             x16, PP, #0x57, lsl #12  ; [pp+0x579f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1530c: add             x16, x16, #0x9f0
    // 0xb15310: ldp             x5, lr, [x16]
    // 0xb15314: blr             lr
    // 0xb15318: mov             x3, x0
    // 0xb1531c: r2 = Null
    //     0xb1531c: mov             x2, NULL
    // 0xb15320: r1 = Null
    //     0xb15320: mov             x1, NULL
    // 0xb15324: stur            x3, [fp, #-0x20]
    // 0xb15328: r4 = 60
    //     0xb15328: movz            x4, #0x3c
    // 0xb1532c: branchIfSmi(r0, 0xb15338)
    //     0xb1532c: tbz             w0, #0, #0xb15338
    // 0xb15330: r4 = LoadClassIdInstr(r0)
    //     0xb15330: ldur            x4, [x0, #-1]
    //     0xb15334: ubfx            x4, x4, #0xc, #0x14
    // 0xb15338: sub             x4, x4, #0x5e
    // 0xb1533c: cmp             x4, #1
    // 0xb15340: b.ls            #0xb15354
    // 0xb15344: r8 = String
    //     0xb15344: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb15348: r3 = Null
    //     0xb15348: add             x3, PP, #0x57, lsl #12  ; [pp+0x57a00] Null
    //     0xb1534c: ldr             x3, [x3, #0xa00]
    // 0xb15350: r0 = String()
    //     0xb15350: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb15354: r0 = VideoPlayerWidget()
    //     0xb15354: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb15358: mov             x1, x0
    // 0xb1535c: ldur            x0, [fp, #-0x20]
    // 0xb15360: StoreField: r1->field_b = r0
    //     0xb15360: stur            w0, [x1, #0xb]
    // 0xb15364: mov             x0, x1
    // 0xb15368: stur            x0, [fp, #-0x20]
    // 0xb1536c: r0 = InkWell()
    //     0xb1536c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb15370: mov             x3, x0
    // 0xb15374: ldur            x0, [fp, #-0x20]
    // 0xb15378: stur            x3, [fp, #-0x28]
    // 0xb1537c: StoreField: r3->field_b = r0
    //     0xb1537c: stur            w0, [x3, #0xb]
    // 0xb15380: ldur            x2, [fp, #-0x18]
    // 0xb15384: r1 = Function '<anonymous closure>':.
    //     0xb15384: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a10] AnonymousClosure: (0xb1591c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb15388: ldr             x1, [x1, #0xa10]
    // 0xb1538c: r0 = AllocateClosure()
    //     0xb1538c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb15390: mov             x1, x0
    // 0xb15394: ldur            x0, [fp, #-0x28]
    // 0xb15398: StoreField: r0->field_f = r1
    //     0xb15398: stur            w1, [x0, #0xf]
    // 0xb1539c: r1 = true
    //     0xb1539c: add             x1, NULL, #0x20  ; true
    // 0xb153a0: StoreField: r0->field_43 = r1
    //     0xb153a0: stur            w1, [x0, #0x43]
    // 0xb153a4: r2 = Instance_BoxShape
    //     0xb153a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb153a8: ldr             x2, [x2, #0x80]
    // 0xb153ac: StoreField: r0->field_47 = r2
    //     0xb153ac: stur            w2, [x0, #0x47]
    // 0xb153b0: StoreField: r0->field_6f = r1
    //     0xb153b0: stur            w1, [x0, #0x6f]
    // 0xb153b4: r3 = false
    //     0xb153b4: add             x3, NULL, #0x30  ; false
    // 0xb153b8: StoreField: r0->field_73 = r3
    //     0xb153b8: stur            w3, [x0, #0x73]
    // 0xb153bc: StoreField: r0->field_83 = r1
    //     0xb153bc: stur            w1, [x0, #0x83]
    // 0xb153c0: StoreField: r0->field_7b = r3
    //     0xb153c0: stur            w3, [x0, #0x7b]
    // 0xb153c4: LeaveFrame
    //     0xb153c4: mov             SP, fp
    //     0xb153c8: ldp             fp, lr, [SP], #0x10
    // 0xb153cc: ret
    //     0xb153cc: ret             
    // 0xb153d0: ldur            x4, [fp, #-8]
    // 0xb153d4: r1 = true
    //     0xb153d4: add             x1, NULL, #0x20  ; true
    // 0xb153d8: r3 = false
    //     0xb153d8: add             x3, NULL, #0x30  ; false
    // 0xb153dc: r2 = Instance_BoxShape
    //     0xb153dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb153e0: ldr             x2, [x2, #0x80]
    // 0xb153e4: r0 = Instance_Clip
    //     0xb153e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb153e8: ldr             x0, [x0, #0x138]
    // 0xb153ec: d0 = 12.000000
    //     0xb153ec: fmov            d0, #12.00000000
    // 0xb153f0: r0 = Radius()
    //     0xb153f0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb153f4: d0 = 12.000000
    //     0xb153f4: fmov            d0, #12.00000000
    // 0xb153f8: stur            x0, [fp, #-0x20]
    // 0xb153fc: StoreField: r0->field_7 = d0
    //     0xb153fc: stur            d0, [x0, #7]
    // 0xb15400: StoreField: r0->field_f = d0
    //     0xb15400: stur            d0, [x0, #0xf]
    // 0xb15404: r0 = BorderRadius()
    //     0xb15404: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb15408: mov             x1, x0
    // 0xb1540c: ldur            x0, [fp, #-0x20]
    // 0xb15410: stur            x1, [fp, #-0x28]
    // 0xb15414: StoreField: r1->field_7 = r0
    //     0xb15414: stur            w0, [x1, #7]
    // 0xb15418: StoreField: r1->field_b = r0
    //     0xb15418: stur            w0, [x1, #0xb]
    // 0xb1541c: StoreField: r1->field_f = r0
    //     0xb1541c: stur            w0, [x1, #0xf]
    // 0xb15420: StoreField: r1->field_13 = r0
    //     0xb15420: stur            w0, [x1, #0x13]
    // 0xb15424: ldur            x16, [fp, #-0x10]
    // 0xb15428: str             x16, [SP]
    // 0xb1542c: r4 = 0
    //     0xb1542c: movz            x4, #0
    // 0xb15430: ldr             x0, [SP]
    // 0xb15434: r16 = UnlinkedCall_0x613b5c
    //     0xb15434: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a18] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15438: add             x16, x16, #0xa18
    // 0xb1543c: ldp             x5, lr, [x16]
    // 0xb15440: blr             lr
    // 0xb15444: mov             x3, x0
    // 0xb15448: r2 = Null
    //     0xb15448: mov             x2, NULL
    // 0xb1544c: r1 = Null
    //     0xb1544c: mov             x1, NULL
    // 0xb15450: stur            x3, [fp, #-0x10]
    // 0xb15454: r4 = 60
    //     0xb15454: movz            x4, #0x3c
    // 0xb15458: branchIfSmi(r0, 0xb15464)
    //     0xb15458: tbz             w0, #0, #0xb15464
    // 0xb1545c: r4 = LoadClassIdInstr(r0)
    //     0xb1545c: ldur            x4, [x0, #-1]
    //     0xb15460: ubfx            x4, x4, #0xc, #0x14
    // 0xb15464: sub             x4, x4, #0x5e
    // 0xb15468: cmp             x4, #1
    // 0xb1546c: b.ls            #0xb15480
    // 0xb15470: r8 = String
    //     0xb15470: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb15474: r3 = Null
    //     0xb15474: add             x3, PP, #0x57, lsl #12  ; [pp+0x57a28] Null
    //     0xb15478: ldr             x3, [x3, #0xa28]
    // 0xb1547c: r0 = String()
    //     0xb1547c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb15480: r1 = Function '<anonymous closure>':.
    //     0xb15480: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a38] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb15484: ldr             x1, [x1, #0xa38]
    // 0xb15488: r2 = Null
    //     0xb15488: mov             x2, NULL
    // 0xb1548c: r0 = AllocateClosure()
    //     0xb1548c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb15490: r1 = Function '<anonymous closure>':.
    //     0xb15490: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a40] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb15494: ldr             x1, [x1, #0xa40]
    // 0xb15498: r2 = Null
    //     0xb15498: mov             x2, NULL
    // 0xb1549c: stur            x0, [fp, #-0x20]
    // 0xb154a0: r0 = AllocateClosure()
    //     0xb154a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb154a4: stur            x0, [fp, #-0x30]
    // 0xb154a8: r0 = CachedNetworkImage()
    //     0xb154a8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb154ac: stur            x0, [fp, #-0x38]
    // 0xb154b0: r16 = 60.000000
    //     0xb154b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb154b4: ldr             x16, [x16, #0x110]
    // 0xb154b8: r30 = 60.000000
    //     0xb154b8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb154bc: ldr             lr, [lr, #0x110]
    // 0xb154c0: stp             lr, x16, [SP, #0x18]
    // 0xb154c4: r16 = Instance_BoxFit
    //     0xb154c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb154c8: ldr             x16, [x16, #0x118]
    // 0xb154cc: ldur            lr, [fp, #-0x20]
    // 0xb154d0: stp             lr, x16, [SP, #8]
    // 0xb154d4: ldur            x16, [fp, #-0x30]
    // 0xb154d8: str             x16, [SP]
    // 0xb154dc: mov             x1, x0
    // 0xb154e0: ldur            x2, [fp, #-0x10]
    // 0xb154e4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb154e4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb154e8: ldr             x4, [x4, #0xc28]
    // 0xb154ec: r0 = CachedNetworkImage()
    //     0xb154ec: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb154f0: r0 = ClipRRect()
    //     0xb154f0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb154f4: mov             x1, x0
    // 0xb154f8: ldur            x0, [fp, #-0x28]
    // 0xb154fc: stur            x1, [fp, #-0x10]
    // 0xb15500: StoreField: r1->field_f = r0
    //     0xb15500: stur            w0, [x1, #0xf]
    // 0xb15504: r0 = Instance_Clip
    //     0xb15504: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb15508: ldr             x0, [x0, #0x138]
    // 0xb1550c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1550c: stur            w0, [x1, #0x17]
    // 0xb15510: ldur            x0, [fp, #-0x38]
    // 0xb15514: StoreField: r1->field_b = r0
    //     0xb15514: stur            w0, [x1, #0xb]
    // 0xb15518: r0 = Radius()
    //     0xb15518: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1551c: d0 = 12.000000
    //     0xb1551c: fmov            d0, #12.00000000
    // 0xb15520: stur            x0, [fp, #-0x20]
    // 0xb15524: StoreField: r0->field_7 = d0
    //     0xb15524: stur            d0, [x0, #7]
    // 0xb15528: StoreField: r0->field_f = d0
    //     0xb15528: stur            d0, [x0, #0xf]
    // 0xb1552c: r0 = BorderRadius()
    //     0xb1552c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb15530: mov             x2, x0
    // 0xb15534: ldur            x0, [fp, #-0x20]
    // 0xb15538: stur            x2, [fp, #-0x28]
    // 0xb1553c: StoreField: r2->field_7 = r0
    //     0xb1553c: stur            w0, [x2, #7]
    // 0xb15540: StoreField: r2->field_b = r0
    //     0xb15540: stur            w0, [x2, #0xb]
    // 0xb15544: StoreField: r2->field_f = r0
    //     0xb15544: stur            w0, [x2, #0xf]
    // 0xb15548: StoreField: r2->field_13 = r0
    //     0xb15548: stur            w0, [x2, #0x13]
    // 0xb1554c: r1 = Instance_Color
    //     0xb1554c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb15550: d0 = 0.600000
    //     0xb15550: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xb15554: r0 = withOpacity()
    //     0xb15554: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb15558: stur            x0, [fp, #-0x20]
    // 0xb1555c: r0 = BoxDecoration()
    //     0xb1555c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb15560: mov             x3, x0
    // 0xb15564: ldur            x0, [fp, #-0x20]
    // 0xb15568: stur            x3, [fp, #-0x30]
    // 0xb1556c: StoreField: r3->field_7 = r0
    //     0xb1556c: stur            w0, [x3, #7]
    // 0xb15570: ldur            x0, [fp, #-0x28]
    // 0xb15574: StoreField: r3->field_13 = r0
    //     0xb15574: stur            w0, [x3, #0x13]
    // 0xb15578: r0 = Instance_BoxShape
    //     0xb15578: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1557c: ldr             x0, [x0, #0x80]
    // 0xb15580: StoreField: r3->field_23 = r0
    //     0xb15580: stur            w0, [x3, #0x23]
    // 0xb15584: r1 = Null
    //     0xb15584: mov             x1, NULL
    // 0xb15588: r2 = 4
    //     0xb15588: movz            x2, #0x4
    // 0xb1558c: r0 = AllocateArray()
    //     0xb1558c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb15590: mov             x2, x0
    // 0xb15594: r16 = "+"
    //     0xb15594: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xb15598: StoreField: r2->field_f = r16
    //     0xb15598: stur            w16, [x2, #0xf]
    // 0xb1559c: ldur            x0, [fp, #-8]
    // 0xb155a0: LoadField: r1 = r0->field_f
    //     0xb155a0: ldur            w1, [x0, #0xf]
    // 0xb155a4: DecompressPointer r1
    //     0xb155a4: add             x1, x1, HEAP, lsl #32
    // 0xb155a8: LoadField: r0 = r1->field_b
    //     0xb155a8: ldur            w0, [x1, #0xb]
    // 0xb155ac: DecompressPointer r0
    //     0xb155ac: add             x0, x0, HEAP, lsl #32
    // 0xb155b0: cmp             w0, NULL
    // 0xb155b4: b.eq            #0xb157d0
    // 0xb155b8: LoadField: r1 = r0->field_f
    //     0xb155b8: ldur            w1, [x0, #0xf]
    // 0xb155bc: DecompressPointer r1
    //     0xb155bc: add             x1, x1, HEAP, lsl #32
    // 0xb155c0: LoadField: r0 = r1->field_b
    //     0xb155c0: ldur            w0, [x1, #0xb]
    // 0xb155c4: DecompressPointer r0
    //     0xb155c4: add             x0, x0, HEAP, lsl #32
    // 0xb155c8: cmp             w0, NULL
    // 0xb155cc: b.ne            #0xb155d8
    // 0xb155d0: r0 = Null
    //     0xb155d0: mov             x0, NULL
    // 0xb155d4: b               #0xb155f8
    // 0xb155d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb155d8: ldur            w1, [x0, #0x17]
    // 0xb155dc: DecompressPointer r1
    //     0xb155dc: add             x1, x1, HEAP, lsl #32
    // 0xb155e0: cmp             w1, NULL
    // 0xb155e4: b.ne            #0xb155f0
    // 0xb155e8: r0 = Null
    //     0xb155e8: mov             x0, NULL
    // 0xb155ec: b               #0xb155f8
    // 0xb155f0: LoadField: r0 = r1->field_b
    //     0xb155f0: ldur            w0, [x1, #0xb]
    // 0xb155f4: DecompressPointer r0
    //     0xb155f4: add             x0, x0, HEAP, lsl #32
    // 0xb155f8: cmp             w0, NULL
    // 0xb155fc: b.ne            #0xb15608
    // 0xb15600: r0 = 0
    //     0xb15600: movz            x0, #0
    // 0xb15604: b               #0xb15618
    // 0xb15608: r1 = LoadInt32Instr(r0)
    //     0xb15608: sbfx            x1, x0, #1, #0x1f
    //     0xb1560c: tbz             w0, #0, #0xb15614
    //     0xb15610: ldur            x1, [x0, #7]
    // 0xb15614: mov             x0, x1
    // 0xb15618: ldur            x3, [fp, #-0x10]
    // 0xb1561c: sub             x4, x0, #4
    // 0xb15620: r0 = BoxInt64Instr(r4)
    //     0xb15620: sbfiz           x0, x4, #1, #0x1f
    //     0xb15624: cmp             x4, x0, asr #1
    //     0xb15628: b.eq            #0xb15634
    //     0xb1562c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb15630: stur            x4, [x0, #7]
    // 0xb15634: StoreField: r2->field_13 = r0
    //     0xb15634: stur            w0, [x2, #0x13]
    // 0xb15638: str             x2, [SP]
    // 0xb1563c: r0 = _interpolate()
    //     0xb1563c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb15640: ldr             x1, [fp, #0x18]
    // 0xb15644: stur            x0, [fp, #-8]
    // 0xb15648: r0 = of()
    //     0xb15648: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1564c: LoadField: r1 = r0->field_87
    //     0xb1564c: ldur            w1, [x0, #0x87]
    // 0xb15650: DecompressPointer r1
    //     0xb15650: add             x1, x1, HEAP, lsl #32
    // 0xb15654: LoadField: r0 = r1->field_7
    //     0xb15654: ldur            w0, [x1, #7]
    // 0xb15658: DecompressPointer r0
    //     0xb15658: add             x0, x0, HEAP, lsl #32
    // 0xb1565c: r16 = 12.000000
    //     0xb1565c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb15660: ldr             x16, [x16, #0x9e8]
    // 0xb15664: r30 = Instance_Color
    //     0xb15664: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb15668: stp             lr, x16, [SP]
    // 0xb1566c: mov             x1, x0
    // 0xb15670: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb15670: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb15674: ldr             x4, [x4, #0xaa0]
    // 0xb15678: r0 = copyWith()
    //     0xb15678: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1567c: stur            x0, [fp, #-0x20]
    // 0xb15680: r0 = Text()
    //     0xb15680: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb15684: mov             x1, x0
    // 0xb15688: ldur            x0, [fp, #-8]
    // 0xb1568c: stur            x1, [fp, #-0x28]
    // 0xb15690: StoreField: r1->field_b = r0
    //     0xb15690: stur            w0, [x1, #0xb]
    // 0xb15694: ldur            x0, [fp, #-0x20]
    // 0xb15698: StoreField: r1->field_13 = r0
    //     0xb15698: stur            w0, [x1, #0x13]
    // 0xb1569c: r0 = Container()
    //     0xb1569c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb156a0: stur            x0, [fp, #-8]
    // 0xb156a4: r16 = 60.000000
    //     0xb156a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb156a8: ldr             x16, [x16, #0x110]
    // 0xb156ac: r30 = 60.000000
    //     0xb156ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb156b0: ldr             lr, [lr, #0x110]
    // 0xb156b4: stp             lr, x16, [SP, #0x18]
    // 0xb156b8: ldur            x16, [fp, #-0x30]
    // 0xb156bc: r30 = Instance_Alignment
    //     0xb156bc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb156c0: ldr             lr, [lr, #0xb10]
    // 0xb156c4: stp             lr, x16, [SP, #8]
    // 0xb156c8: ldur            x16, [fp, #-0x28]
    // 0xb156cc: str             x16, [SP]
    // 0xb156d0: mov             x1, x0
    // 0xb156d4: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb156d4: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eb90] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb156d8: ldr             x4, [x4, #0xb90]
    // 0xb156dc: r0 = Container()
    //     0xb156dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb156e0: r1 = Null
    //     0xb156e0: mov             x1, NULL
    // 0xb156e4: r2 = 4
    //     0xb156e4: movz            x2, #0x4
    // 0xb156e8: r0 = AllocateArray()
    //     0xb156e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb156ec: mov             x2, x0
    // 0xb156f0: ldur            x0, [fp, #-0x10]
    // 0xb156f4: stur            x2, [fp, #-0x20]
    // 0xb156f8: StoreField: r2->field_f = r0
    //     0xb156f8: stur            w0, [x2, #0xf]
    // 0xb156fc: ldur            x0, [fp, #-8]
    // 0xb15700: StoreField: r2->field_13 = r0
    //     0xb15700: stur            w0, [x2, #0x13]
    // 0xb15704: r1 = <Widget>
    //     0xb15704: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb15708: r0 = AllocateGrowableArray()
    //     0xb15708: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1570c: mov             x1, x0
    // 0xb15710: ldur            x0, [fp, #-0x20]
    // 0xb15714: stur            x1, [fp, #-8]
    // 0xb15718: StoreField: r1->field_f = r0
    //     0xb15718: stur            w0, [x1, #0xf]
    // 0xb1571c: r0 = 4
    //     0xb1571c: movz            x0, #0x4
    // 0xb15720: StoreField: r1->field_b = r0
    //     0xb15720: stur            w0, [x1, #0xb]
    // 0xb15724: r0 = Stack()
    //     0xb15724: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb15728: mov             x1, x0
    // 0xb1572c: r0 = Instance_Alignment
    //     0xb1572c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb15730: ldr             x0, [x0, #0xb10]
    // 0xb15734: stur            x1, [fp, #-0x10]
    // 0xb15738: StoreField: r1->field_f = r0
    //     0xb15738: stur            w0, [x1, #0xf]
    // 0xb1573c: r0 = Instance_StackFit
    //     0xb1573c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb15740: ldr             x0, [x0, #0xfa8]
    // 0xb15744: ArrayStore: r1[0] = r0  ; List_4
    //     0xb15744: stur            w0, [x1, #0x17]
    // 0xb15748: r0 = Instance_Clip
    //     0xb15748: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb1574c: ldr             x0, [x0, #0x7e0]
    // 0xb15750: StoreField: r1->field_1b = r0
    //     0xb15750: stur            w0, [x1, #0x1b]
    // 0xb15754: ldur            x0, [fp, #-8]
    // 0xb15758: StoreField: r1->field_b = r0
    //     0xb15758: stur            w0, [x1, #0xb]
    // 0xb1575c: r0 = InkWell()
    //     0xb1575c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb15760: mov             x3, x0
    // 0xb15764: ldur            x0, [fp, #-0x10]
    // 0xb15768: stur            x3, [fp, #-8]
    // 0xb1576c: StoreField: r3->field_b = r0
    //     0xb1576c: stur            w0, [x3, #0xb]
    // 0xb15770: ldur            x2, [fp, #-0x18]
    // 0xb15774: r1 = Function '<anonymous closure>':.
    //     0xb15774: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a48] AnonymousClosure: (0xb157e0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb15778: ldr             x1, [x1, #0xa48]
    // 0xb1577c: r0 = AllocateClosure()
    //     0xb1577c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb15780: mov             x1, x0
    // 0xb15784: ldur            x0, [fp, #-8]
    // 0xb15788: StoreField: r0->field_f = r1
    //     0xb15788: stur            w1, [x0, #0xf]
    // 0xb1578c: r1 = true
    //     0xb1578c: add             x1, NULL, #0x20  ; true
    // 0xb15790: StoreField: r0->field_43 = r1
    //     0xb15790: stur            w1, [x0, #0x43]
    // 0xb15794: r2 = Instance_BoxShape
    //     0xb15794: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb15798: ldr             x2, [x2, #0x80]
    // 0xb1579c: StoreField: r0->field_47 = r2
    //     0xb1579c: stur            w2, [x0, #0x47]
    // 0xb157a0: StoreField: r0->field_6f = r1
    //     0xb157a0: stur            w1, [x0, #0x6f]
    // 0xb157a4: r2 = false
    //     0xb157a4: add             x2, NULL, #0x30  ; false
    // 0xb157a8: StoreField: r0->field_73 = r2
    //     0xb157a8: stur            w2, [x0, #0x73]
    // 0xb157ac: StoreField: r0->field_83 = r1
    //     0xb157ac: stur            w1, [x0, #0x83]
    // 0xb157b0: StoreField: r0->field_7b = r2
    //     0xb157b0: stur            w2, [x0, #0x7b]
    // 0xb157b4: LeaveFrame
    //     0xb157b4: mov             SP, fp
    //     0xb157b8: ldp             fp, lr, [SP], #0x10
    // 0xb157bc: ret
    //     0xb157bc: ret             
    // 0xb157c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb157c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb157c4: b               #0xb1502c
    // 0xb157c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb157c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb157cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb157cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb157d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb157d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb157e0, size: 0x13c
    // 0xb157e0: EnterFrame
    //     0xb157e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb157e4: mov             fp, SP
    // 0xb157e8: AllocStack(0x28)
    //     0xb157e8: sub             SP, SP, #0x28
    // 0xb157ec: SetupParameters()
    //     0xb157ec: ldr             x0, [fp, #0x10]
    //     0xb157f0: ldur            w1, [x0, #0x17]
    //     0xb157f4: add             x1, x1, HEAP, lsl #32
    // 0xb157f8: CheckStackOverflow
    //     0xb157f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb157fc: cmp             SP, x16
    //     0xb15800: b.ls            #0xb1590c
    // 0xb15804: LoadField: r0 = r1->field_b
    //     0xb15804: ldur            w0, [x1, #0xb]
    // 0xb15808: DecompressPointer r0
    //     0xb15808: add             x0, x0, HEAP, lsl #32
    // 0xb1580c: stur            x0, [fp, #-8]
    // 0xb15810: LoadField: r1 = r0->field_f
    //     0xb15810: ldur            w1, [x0, #0xf]
    // 0xb15814: DecompressPointer r1
    //     0xb15814: add             x1, x1, HEAP, lsl #32
    // 0xb15818: LoadField: r2 = r1->field_b
    //     0xb15818: ldur            w2, [x1, #0xb]
    // 0xb1581c: DecompressPointer r2
    //     0xb1581c: add             x2, x2, HEAP, lsl #32
    // 0xb15820: cmp             w2, NULL
    // 0xb15824: b.eq            #0xb15914
    // 0xb15828: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb15828: ldur            w1, [x2, #0x17]
    // 0xb1582c: DecompressPointer r1
    //     0xb1582c: add             x1, x1, HEAP, lsl #32
    // 0xb15830: r16 = "more_media"
    //     0xb15830: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc48] "more_media"
    //     0xb15834: ldr             x16, [x16, #0xc48]
    // 0xb15838: stp             x16, x1, [SP, #0x10]
    // 0xb1583c: r16 = ""
    //     0xb1583c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb15840: r30 = false
    //     0xb15840: add             lr, NULL, #0x30  ; false
    // 0xb15844: stp             lr, x16, [SP]
    // 0xb15848: r4 = 0
    //     0xb15848: movz            x4, #0
    // 0xb1584c: ldr             x0, [SP, #0x18]
    // 0xb15850: r16 = UnlinkedCall_0x613b5c
    //     0xb15850: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15854: add             x16, x16, #0xa50
    // 0xb15858: ldp             x5, lr, [x16]
    // 0xb1585c: blr             lr
    // 0xb15860: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb15860: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb15864: ldr             x0, [x0, #0x1c80]
    //     0xb15868: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1586c: cmp             w0, w16
    //     0xb15870: b.ne            #0xb1587c
    //     0xb15874: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb15878: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb1587c: r1 = Null
    //     0xb1587c: mov             x1, NULL
    // 0xb15880: r2 = 8
    //     0xb15880: movz            x2, #0x8
    // 0xb15884: r0 = AllocateArray()
    //     0xb15884: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb15888: r16 = "product_short_id"
    //     0xb15888: add             x16, PP, #0xb, lsl #12  ; [pp+0xb490] "product_short_id"
    //     0xb1588c: ldr             x16, [x16, #0x490]
    // 0xb15890: StoreField: r0->field_f = r16
    //     0xb15890: stur            w16, [x0, #0xf]
    // 0xb15894: ldur            x1, [fp, #-8]
    // 0xb15898: LoadField: r2 = r1->field_f
    //     0xb15898: ldur            w2, [x1, #0xf]
    // 0xb1589c: DecompressPointer r2
    //     0xb1589c: add             x2, x2, HEAP, lsl #32
    // 0xb158a0: LoadField: r1 = r2->field_b
    //     0xb158a0: ldur            w1, [x2, #0xb]
    // 0xb158a4: DecompressPointer r1
    //     0xb158a4: add             x1, x1, HEAP, lsl #32
    // 0xb158a8: cmp             w1, NULL
    // 0xb158ac: b.eq            #0xb15918
    // 0xb158b0: LoadField: r2 = r1->field_b
    //     0xb158b0: ldur            w2, [x1, #0xb]
    // 0xb158b4: DecompressPointer r2
    //     0xb158b4: add             x2, x2, HEAP, lsl #32
    // 0xb158b8: StoreField: r0->field_13 = r2
    //     0xb158b8: stur            w2, [x0, #0x13]
    // 0xb158bc: r16 = "is_product_page"
    //     0xb158bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f368] "is_product_page"
    //     0xb158c0: ldr             x16, [x16, #0x368]
    // 0xb158c4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb158c4: stur            w16, [x0, #0x17]
    // 0xb158c8: r16 = true
    //     0xb158c8: add             x16, NULL, #0x20  ; true
    // 0xb158cc: StoreField: r0->field_1b = r16
    //     0xb158cc: stur            w16, [x0, #0x1b]
    // 0xb158d0: r16 = <String, Object?>
    //     0xb158d0: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xb158d4: ldr             x16, [x16, #0xc28]
    // 0xb158d8: stp             x0, x16, [SP]
    // 0xb158dc: r0 = Map._fromLiteral()
    //     0xb158dc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb158e0: r16 = "/rating_review_media_screen"
    //     0xb158e0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9c8] "/rating_review_media_screen"
    //     0xb158e4: ldr             x16, [x16, #0x9c8]
    // 0xb158e8: stp             x16, NULL, [SP, #8]
    // 0xb158ec: str             x0, [SP]
    // 0xb158f0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb158f0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb158f4: ldr             x4, [x4, #0x438]
    // 0xb158f8: r0 = GetNavigation.toNamed()
    //     0xb158f8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb158fc: r0 = Null
    //     0xb158fc: mov             x0, NULL
    // 0xb15900: LeaveFrame
    //     0xb15900: mov             SP, fp
    //     0xb15904: ldp             fp, lr, [SP], #0x10
    // 0xb15908: ret
    //     0xb15908: ret             
    // 0xb1590c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1590c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15910: b               #0xb15804
    // 0xb15914: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15914: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb15918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15918: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1591c, size: 0x128
    // 0xb1591c: EnterFrame
    //     0xb1591c: stp             fp, lr, [SP, #-0x10]!
    //     0xb15920: mov             fp, SP
    // 0xb15924: AllocStack(0x30)
    //     0xb15924: sub             SP, SP, #0x30
    // 0xb15928: SetupParameters()
    //     0xb15928: ldr             x0, [fp, #0x10]
    //     0xb1592c: ldur            w2, [x0, #0x17]
    //     0xb15930: add             x2, x2, HEAP, lsl #32
    //     0xb15934: stur            x2, [fp, #-0x10]
    // 0xb15938: CheckStackOverflow
    //     0xb15938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1593c: cmp             SP, x16
    //     0xb15940: b.ls            #0xb15a38
    // 0xb15944: LoadField: r0 = r2->field_b
    //     0xb15944: ldur            w0, [x2, #0xb]
    // 0xb15948: DecompressPointer r0
    //     0xb15948: add             x0, x0, HEAP, lsl #32
    // 0xb1594c: LoadField: r1 = r0->field_f
    //     0xb1594c: ldur            w1, [x0, #0xf]
    // 0xb15950: DecompressPointer r1
    //     0xb15950: add             x1, x1, HEAP, lsl #32
    // 0xb15954: LoadField: r0 = r1->field_b
    //     0xb15954: ldur            w0, [x1, #0xb]
    // 0xb15958: DecompressPointer r0
    //     0xb15958: add             x0, x0, HEAP, lsl #32
    // 0xb1595c: stur            x0, [fp, #-8]
    // 0xb15960: cmp             w0, NULL
    // 0xb15964: b.eq            #0xb15a40
    // 0xb15968: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb15968: ldur            w1, [x2, #0x17]
    // 0xb1596c: DecompressPointer r1
    //     0xb1596c: add             x1, x1, HEAP, lsl #32
    // 0xb15970: str             x1, [SP]
    // 0xb15974: r4 = 0
    //     0xb15974: movz            x4, #0
    // 0xb15978: ldr             x0, [SP]
    // 0xb1597c: r16 = UnlinkedCall_0x613b5c
    //     0xb1597c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15980: add             x16, x16, #0xa60
    // 0xb15984: ldp             x5, lr, [x16]
    // 0xb15988: blr             lr
    // 0xb1598c: mov             x1, x0
    // 0xb15990: ldur            x0, [fp, #-8]
    // 0xb15994: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb15994: ldur            w2, [x0, #0x17]
    // 0xb15998: DecompressPointer r2
    //     0xb15998: add             x2, x2, HEAP, lsl #32
    // 0xb1599c: r16 = "single_media"
    //     0xb1599c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xb159a0: ldr             x16, [x16, #0xab0]
    // 0xb159a4: stp             x16, x2, [SP, #0x10]
    // 0xb159a8: r16 = false
    //     0xb159a8: add             x16, NULL, #0x30  ; false
    // 0xb159ac: stp             x16, x1, [SP]
    // 0xb159b0: r4 = 0
    //     0xb159b0: movz            x4, #0
    // 0xb159b4: ldr             x0, [SP, #0x18]
    // 0xb159b8: r16 = UnlinkedCall_0x613b5c
    //     0xb159b8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb159bc: add             x16, x16, #0xa70
    // 0xb159c0: ldp             x5, lr, [x16]
    // 0xb159c4: blr             lr
    // 0xb159c8: ldur            x2, [fp, #-0x10]
    // 0xb159cc: LoadField: r1 = r2->field_f
    //     0xb159cc: ldur            w1, [x2, #0xf]
    // 0xb159d0: DecompressPointer r1
    //     0xb159d0: add             x1, x1, HEAP, lsl #32
    // 0xb159d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb159d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb159d8: r0 = of()
    //     0xb159d8: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb159dc: ldur            x2, [fp, #-0x10]
    // 0xb159e0: r1 = Function '<anonymous closure>':.
    //     0xb159e0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a80] AnonymousClosure: (0xb15a44), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb1267c)
    //     0xb159e4: ldr             x1, [x1, #0xa80]
    // 0xb159e8: stur            x0, [fp, #-8]
    // 0xb159ec: r0 = AllocateClosure()
    //     0xb159ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb159f0: r1 = Null
    //     0xb159f0: mov             x1, NULL
    // 0xb159f4: stur            x0, [fp, #-0x10]
    // 0xb159f8: r0 = MaterialPageRoute()
    //     0xb159f8: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb159fc: mov             x1, x0
    // 0xb15a00: ldur            x2, [fp, #-0x10]
    // 0xb15a04: stur            x0, [fp, #-0x10]
    // 0xb15a08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb15a08: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb15a0c: r0 = MaterialPageRoute()
    //     0xb15a0c: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb15a10: ldur            x16, [fp, #-8]
    // 0xb15a14: stp             x16, NULL, [SP, #8]
    // 0xb15a18: ldur            x16, [fp, #-0x10]
    // 0xb15a1c: str             x16, [SP]
    // 0xb15a20: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb15a20: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb15a24: r0 = push()
    //     0xb15a24: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb15a28: r0 = Null
    //     0xb15a28: mov             x0, NULL
    // 0xb15a2c: LeaveFrame
    //     0xb15a2c: mov             SP, fp
    //     0xb15a30: ldp             fp, lr, [SP], #0x10
    // 0xb15a34: ret
    //     0xb15a34: ret             
    // 0xb15a38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15a38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15a3c: b               #0xb15944
    // 0xb15a40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15a40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb15a44, size: 0x1e0
    // 0xb15a44: EnterFrame
    //     0xb15a44: stp             fp, lr, [SP, #-0x10]!
    //     0xb15a48: mov             fp, SP
    // 0xb15a4c: AllocStack(0x38)
    //     0xb15a4c: sub             SP, SP, #0x38
    // 0xb15a50: SetupParameters()
    //     0xb15a50: ldr             x0, [fp, #0x18]
    //     0xb15a54: ldur            w3, [x0, #0x17]
    //     0xb15a58: add             x3, x3, HEAP, lsl #32
    //     0xb15a5c: stur            x3, [fp, #-0x10]
    // 0xb15a60: CheckStackOverflow
    //     0xb15a60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15a64: cmp             SP, x16
    //     0xb15a68: b.ls            #0xb15c14
    // 0xb15a6c: LoadField: r0 = r3->field_b
    //     0xb15a6c: ldur            w0, [x3, #0xb]
    // 0xb15a70: DecompressPointer r0
    //     0xb15a70: add             x0, x0, HEAP, lsl #32
    // 0xb15a74: stur            x0, [fp, #-8]
    // 0xb15a78: LoadField: r1 = r0->field_f
    //     0xb15a78: ldur            w1, [x0, #0xf]
    // 0xb15a7c: DecompressPointer r1
    //     0xb15a7c: add             x1, x1, HEAP, lsl #32
    // 0xb15a80: LoadField: r2 = r1->field_b
    //     0xb15a80: ldur            w2, [x1, #0xb]
    // 0xb15a84: DecompressPointer r2
    //     0xb15a84: add             x2, x2, HEAP, lsl #32
    // 0xb15a88: cmp             w2, NULL
    // 0xb15a8c: b.eq            #0xb15c1c
    // 0xb15a90: LoadField: r1 = r2->field_f
    //     0xb15a90: ldur            w1, [x2, #0xf]
    // 0xb15a94: DecompressPointer r1
    //     0xb15a94: add             x1, x1, HEAP, lsl #32
    // 0xb15a98: LoadField: r2 = r1->field_b
    //     0xb15a98: ldur            w2, [x1, #0xb]
    // 0xb15a9c: DecompressPointer r2
    //     0xb15a9c: add             x2, x2, HEAP, lsl #32
    // 0xb15aa0: cmp             w2, NULL
    // 0xb15aa4: b.ne            #0xb15ab0
    // 0xb15aa8: r1 = Null
    //     0xb15aa8: mov             x1, NULL
    // 0xb15aac: b               #0xb15ad4
    // 0xb15ab0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb15ab0: ldur            w1, [x2, #0x17]
    // 0xb15ab4: DecompressPointer r1
    //     0xb15ab4: add             x1, x1, HEAP, lsl #32
    // 0xb15ab8: cmp             w1, NULL
    // 0xb15abc: b.ne            #0xb15ac8
    // 0xb15ac0: r1 = Null
    //     0xb15ac0: mov             x1, NULL
    // 0xb15ac4: b               #0xb15ad4
    // 0xb15ac8: LoadField: r2 = r1->field_f
    //     0xb15ac8: ldur            w2, [x1, #0xf]
    // 0xb15acc: DecompressPointer r2
    //     0xb15acc: add             x2, x2, HEAP, lsl #32
    // 0xb15ad0: mov             x1, x2
    // 0xb15ad4: cmp             w1, NULL
    // 0xb15ad8: b.ne            #0xb15af0
    // 0xb15adc: r1 = <ReviewRatingEntity>
    //     0xb15adc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0xb15ae0: ldr             x1, [x1, #0x150]
    // 0xb15ae4: r2 = 0
    //     0xb15ae4: movz            x2, #0
    // 0xb15ae8: r0 = _GrowableList()
    //     0xb15ae8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb15aec: mov             x1, x0
    // 0xb15af0: ldur            x2, [fp, #-0x10]
    // 0xb15af4: ldur            x0, [fp, #-8]
    // 0xb15af8: stur            x1, [fp, #-0x20]
    // 0xb15afc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb15afc: ldur            w3, [x2, #0x17]
    // 0xb15b00: DecompressPointer r3
    //     0xb15b00: add             x3, x3, HEAP, lsl #32
    // 0xb15b04: stur            x3, [fp, #-0x18]
    // 0xb15b08: str             x3, [SP]
    // 0xb15b0c: r4 = 0
    //     0xb15b0c: movz            x4, #0
    // 0xb15b10: ldr             x0, [SP]
    // 0xb15b14: r16 = UnlinkedCall_0x613b5c
    //     0xb15b14: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a88] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15b18: add             x16, x16, #0xa88
    // 0xb15b1c: ldp             x5, lr, [x16]
    // 0xb15b20: blr             lr
    // 0xb15b24: stur            x0, [fp, #-0x28]
    // 0xb15b28: ldur            x16, [fp, #-0x18]
    // 0xb15b2c: str             x16, [SP]
    // 0xb15b30: r4 = 0
    //     0xb15b30: movz            x4, #0
    // 0xb15b34: ldr             x0, [SP]
    // 0xb15b38: r16 = UnlinkedCall_0x613b5c
    //     0xb15b38: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15b3c: add             x16, x16, #0xa98
    // 0xb15b40: ldp             x5, lr, [x16]
    // 0xb15b44: blr             lr
    // 0xb15b48: mov             x3, x0
    // 0xb15b4c: r2 = Null
    //     0xb15b4c: mov             x2, NULL
    // 0xb15b50: r1 = Null
    //     0xb15b50: mov             x1, NULL
    // 0xb15b54: stur            x3, [fp, #-0x18]
    // 0xb15b58: branchIfSmi(r0, 0xb15b80)
    //     0xb15b58: tbz             w0, #0, #0xb15b80
    // 0xb15b5c: r4 = LoadClassIdInstr(r0)
    //     0xb15b5c: ldur            x4, [x0, #-1]
    //     0xb15b60: ubfx            x4, x4, #0xc, #0x14
    // 0xb15b64: sub             x4, x4, #0x3c
    // 0xb15b68: cmp             x4, #1
    // 0xb15b6c: b.ls            #0xb15b80
    // 0xb15b70: r8 = int?
    //     0xb15b70: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0xb15b74: r3 = Null
    //     0xb15b74: add             x3, PP, #0x57, lsl #12  ; [pp+0x57aa8] Null
    //     0xb15b78: ldr             x3, [x3, #0xaa8]
    // 0xb15b7c: r0 = int?()
    //     0xb15b7c: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0xb15b80: ldur            x0, [fp, #-8]
    // 0xb15b84: LoadField: r1 = r0->field_f
    //     0xb15b84: ldur            w1, [x0, #0xf]
    // 0xb15b88: DecompressPointer r1
    //     0xb15b88: add             x1, x1, HEAP, lsl #32
    // 0xb15b8c: LoadField: r0 = r1->field_b
    //     0xb15b8c: ldur            w0, [x1, #0xb]
    // 0xb15b90: DecompressPointer r0
    //     0xb15b90: add             x0, x0, HEAP, lsl #32
    // 0xb15b94: cmp             w0, NULL
    // 0xb15b98: b.eq            #0xb15c20
    // 0xb15b9c: LoadField: r1 = r0->field_1f
    //     0xb15b9c: ldur            w1, [x0, #0x1f]
    // 0xb15ba0: DecompressPointer r1
    //     0xb15ba0: add             x1, x1, HEAP, lsl #32
    // 0xb15ba4: stur            x1, [fp, #-8]
    // 0xb15ba8: r0 = RatingReviewAllMediaOnTapImage()
    //     0xb15ba8: bl              #0xb15c24  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0xb15bac: mov             x3, x0
    // 0xb15bb0: ldur            x0, [fp, #-0x20]
    // 0xb15bb4: stur            x3, [fp, #-0x30]
    // 0xb15bb8: StoreField: r3->field_f = r0
    //     0xb15bb8: stur            w0, [x3, #0xf]
    // 0xb15bbc: ldur            x0, [fp, #-0x28]
    // 0xb15bc0: r1 = LoadInt32Instr(r0)
    //     0xb15bc0: sbfx            x1, x0, #1, #0x1f
    //     0xb15bc4: tbz             w0, #0, #0xb15bcc
    //     0xb15bc8: ldur            x1, [x0, #7]
    // 0xb15bcc: StoreField: r3->field_13 = r1
    //     0xb15bcc: stur            x1, [x3, #0x13]
    // 0xb15bd0: ldur            x0, [fp, #-0x18]
    // 0xb15bd4: StoreField: r3->field_1b = r0
    //     0xb15bd4: stur            w0, [x3, #0x1b]
    // 0xb15bd8: r0 = "direct_image"
    //     0xb15bd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xb15bdc: ldr             x0, [x0, #0xc98]
    // 0xb15be0: StoreField: r3->field_b = r0
    //     0xb15be0: stur            w0, [x3, #0xb]
    // 0xb15be4: ldur            x2, [fp, #-0x10]
    // 0xb15be8: r1 = Function '<anonymous closure>':.
    //     0xb15be8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ab8] AnonymousClosure: (0xa8d1a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xb15bec: ldr             x1, [x1, #0xab8]
    // 0xb15bf0: r0 = AllocateClosure()
    //     0xb15bf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb15bf4: mov             x1, x0
    // 0xb15bf8: ldur            x0, [fp, #-0x30]
    // 0xb15bfc: StoreField: r0->field_1f = r1
    //     0xb15bfc: stur            w1, [x0, #0x1f]
    // 0xb15c00: ldur            x1, [fp, #-8]
    // 0xb15c04: StoreField: r0->field_23 = r1
    //     0xb15c04: stur            w1, [x0, #0x23]
    // 0xb15c08: LeaveFrame
    //     0xb15c08: mov             SP, fp
    //     0xb15c0c: ldp             fp, lr, [SP], #0x10
    // 0xb15c10: ret
    //     0xb15c10: ret             
    // 0xb15c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15c14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15c18: b               #0xb15a6c
    // 0xb15c1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15c1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb15c20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15c20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb15c30, size: 0xe0
    // 0xb15c30: EnterFrame
    //     0xb15c30: stp             fp, lr, [SP, #-0x10]!
    //     0xb15c34: mov             fp, SP
    // 0xb15c38: AllocStack(0x20)
    //     0xb15c38: sub             SP, SP, #0x20
    // 0xb15c3c: SetupParameters()
    //     0xb15c3c: ldr             x0, [fp, #0x10]
    //     0xb15c40: ldur            w1, [x0, #0x17]
    //     0xb15c44: add             x1, x1, HEAP, lsl #32
    // 0xb15c48: CheckStackOverflow
    //     0xb15c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15c4c: cmp             SP, x16
    //     0xb15c50: b.ls            #0xb15d04
    // 0xb15c54: LoadField: r0 = r1->field_f
    //     0xb15c54: ldur            w0, [x1, #0xf]
    // 0xb15c58: DecompressPointer r0
    //     0xb15c58: add             x0, x0, HEAP, lsl #32
    // 0xb15c5c: LoadField: r1 = r0->field_b
    //     0xb15c5c: ldur            w1, [x0, #0xb]
    // 0xb15c60: DecompressPointer r1
    //     0xb15c60: add             x1, x1, HEAP, lsl #32
    // 0xb15c64: cmp             w1, NULL
    // 0xb15c68: b.eq            #0xb15d0c
    // 0xb15c6c: LoadField: r0 = r1->field_f
    //     0xb15c6c: ldur            w0, [x1, #0xf]
    // 0xb15c70: DecompressPointer r0
    //     0xb15c70: add             x0, x0, HEAP, lsl #32
    // 0xb15c74: LoadField: r2 = r0->field_f
    //     0xb15c74: ldur            w2, [x0, #0xf]
    // 0xb15c78: DecompressPointer r2
    //     0xb15c78: add             x2, x2, HEAP, lsl #32
    // 0xb15c7c: cmp             w2, NULL
    // 0xb15c80: b.ne            #0xb15c8c
    // 0xb15c84: r0 = Null
    //     0xb15c84: mov             x0, NULL
    // 0xb15c88: b               #0xb15c94
    // 0xb15c8c: LoadField: r0 = r2->field_f
    //     0xb15c8c: ldur            w0, [x2, #0xf]
    // 0xb15c90: DecompressPointer r0
    //     0xb15c90: add             x0, x0, HEAP, lsl #32
    // 0xb15c94: cmp             w0, NULL
    // 0xb15c98: b.ne            #0xb15ca4
    // 0xb15c9c: r0 = 0
    //     0xb15c9c: movz            x0, #0
    // 0xb15ca0: b               #0xb15cb4
    // 0xb15ca4: r2 = LoadInt32Instr(r0)
    //     0xb15ca4: sbfx            x2, x0, #1, #0x1f
    //     0xb15ca8: tbz             w0, #0, #0xb15cb0
    //     0xb15cac: ldur            x2, [x0, #7]
    // 0xb15cb0: mov             x0, x2
    // 0xb15cb4: cmp             x0, #3
    // 0xb15cb8: b.lt            #0xb15cf4
    // 0xb15cbc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb15cbc: ldur            w0, [x1, #0x17]
    // 0xb15cc0: DecompressPointer r0
    //     0xb15cc0: add             x0, x0, HEAP, lsl #32
    // 0xb15cc4: r16 = "view_all"
    //     0xb15cc4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xb15cc8: ldr             x16, [x16, #0xba0]
    // 0xb15ccc: stp             x16, x0, [SP, #0x10]
    // 0xb15cd0: r16 = ""
    //     0xb15cd0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb15cd4: r30 = true
    //     0xb15cd4: add             lr, NULL, #0x20  ; true
    // 0xb15cd8: stp             lr, x16, [SP]
    // 0xb15cdc: r4 = 0
    //     0xb15cdc: movz            x4, #0
    // 0xb15ce0: ldr             x0, [SP, #0x18]
    // 0xb15ce4: r16 = UnlinkedCall_0x613b5c
    //     0xb15ce4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ac0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15ce8: add             x16, x16, #0xac0
    // 0xb15cec: ldp             x5, lr, [x16]
    // 0xb15cf0: blr             lr
    // 0xb15cf4: r0 = Null
    //     0xb15cf4: mov             x0, NULL
    // 0xb15cf8: LeaveFrame
    //     0xb15cf8: mov             SP, fp
    //     0xb15cfc: ldp             fp, lr, [SP], #0x10
    // 0xb15d00: ret
    //     0xb15d00: ret             
    // 0xb15d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15d04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15d08: b               #0xb15c54
    // 0xb15d0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15d0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb15d10, size: 0xdc
    // 0xb15d10: EnterFrame
    //     0xb15d10: stp             fp, lr, [SP, #-0x10]!
    //     0xb15d14: mov             fp, SP
    // 0xb15d18: AllocStack(0x38)
    //     0xb15d18: sub             SP, SP, #0x38
    // 0xb15d1c: SetupParameters()
    //     0xb15d1c: ldr             x0, [fp, #0x10]
    //     0xb15d20: ldur            w1, [x0, #0x17]
    //     0xb15d24: add             x1, x1, HEAP, lsl #32
    //     0xb15d28: stur            x1, [fp, #-8]
    // 0xb15d2c: CheckStackOverflow
    //     0xb15d2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15d30: cmp             SP, x16
    //     0xb15d34: b.ls            #0xb15de0
    // 0xb15d38: LoadField: r0 = r1->field_f
    //     0xb15d38: ldur            w0, [x1, #0xf]
    // 0xb15d3c: DecompressPointer r0
    //     0xb15d3c: add             x0, x0, HEAP, lsl #32
    // 0xb15d40: LoadField: r2 = r0->field_b
    //     0xb15d40: ldur            w2, [x0, #0xb]
    // 0xb15d44: DecompressPointer r2
    //     0xb15d44: add             x2, x2, HEAP, lsl #32
    // 0xb15d48: cmp             w2, NULL
    // 0xb15d4c: b.eq            #0xb15de8
    // 0xb15d50: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb15d50: ldur            w0, [x2, #0x17]
    // 0xb15d54: DecompressPointer r0
    //     0xb15d54: add             x0, x0, HEAP, lsl #32
    // 0xb15d58: r16 = "trusted_badge"
    //     0xb15d58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "trusted_badge"
    //     0xb15d5c: ldr             x16, [x16, #0xd58]
    // 0xb15d60: stp             x16, x0, [SP, #0x10]
    // 0xb15d64: r16 = ""
    //     0xb15d64: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb15d68: r30 = false
    //     0xb15d68: add             lr, NULL, #0x30  ; false
    // 0xb15d6c: stp             lr, x16, [SP]
    // 0xb15d70: r4 = 0
    //     0xb15d70: movz            x4, #0
    // 0xb15d74: ldr             x0, [SP, #0x18]
    // 0xb15d78: r16 = UnlinkedCall_0x613b5c
    //     0xb15d78: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ad0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb15d7c: add             x16, x16, #0xad0
    // 0xb15d80: ldp             x5, lr, [x16]
    // 0xb15d84: blr             lr
    // 0xb15d88: ldur            x0, [fp, #-8]
    // 0xb15d8c: LoadField: r3 = r0->field_13
    //     0xb15d8c: ldur            w3, [x0, #0x13]
    // 0xb15d90: DecompressPointer r3
    //     0xb15d90: add             x3, x3, HEAP, lsl #32
    // 0xb15d94: stur            x3, [fp, #-0x10]
    // 0xb15d98: r1 = Function '<anonymous closure>':.
    //     0xb15d98: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ae0] AnonymousClosure: (0x999958), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xb15d9c: ldr             x1, [x1, #0xae0]
    // 0xb15da0: r2 = Null
    //     0xb15da0: mov             x2, NULL
    // 0xb15da4: r0 = AllocateClosure()
    //     0xb15da4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb15da8: stp             x0, NULL, [SP, #0x18]
    // 0xb15dac: ldur            x16, [fp, #-0x10]
    // 0xb15db0: r30 = Instance_RoundedRectangleBorder
    //     0xb15db0: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec08] Obj!RoundedRectangleBorder@d5abe1
    //     0xb15db4: ldr             lr, [lr, #0xc08]
    // 0xb15db8: stp             lr, x16, [SP, #8]
    // 0xb15dbc: r16 = true
    //     0xb15dbc: add             x16, NULL, #0x20  ; true
    // 0xb15dc0: str             x16, [SP]
    // 0xb15dc4: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0xb15dc4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0xb15dc8: ldr             x4, [x4, #0xd70]
    // 0xb15dcc: r0 = showModalBottomSheet()
    //     0xb15dcc: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb15dd0: r0 = Null
    //     0xb15dd0: mov             x0, NULL
    // 0xb15dd4: LeaveFrame
    //     0xb15dd4: mov             SP, fp
    //     0xb15dd8: ldp             fp, lr, [SP], #0x10
    // 0xb15ddc: ret
    //     0xb15ddc: ret             
    // 0xb15de0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15de0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15de4: b               #0xb15d38
    // 0xb15de8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb15de8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4135, size: 0x24, field offset: 0xc
//   const constructor, 
class RatingReviewItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e188, size: 0x24
    // 0xc7e188: EnterFrame
    //     0xc7e188: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e18c: mov             fp, SP
    // 0xc7e190: mov             x0, x1
    // 0xc7e194: r1 = <RatingReviewItemView>
    //     0xc7e194: add             x1, PP, #0x48, lsl #12  ; [pp+0x48af0] TypeArguments: <RatingReviewItemView>
    //     0xc7e198: ldr             x1, [x1, #0xaf0]
    // 0xc7e19c: r0 = _RatingReviewItemViewState()
    //     0xc7e19c: bl              #0xc7e1ac  ; Allocate_RatingReviewItemViewStateStub -> _RatingReviewItemViewState (size=0x14)
    // 0xc7e1a0: LeaveFrame
    //     0xc7e1a0: mov             SP, fp
    //     0xc7e1a4: ldp             fp, lr, [SP], #0x10
    // 0xc7e1a8: ret
    //     0xc7e1a8: ret             
  }
}
