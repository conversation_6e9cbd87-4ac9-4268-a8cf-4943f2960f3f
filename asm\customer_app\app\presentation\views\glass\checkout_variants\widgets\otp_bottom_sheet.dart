// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart

// class id: 1049375, size: 0x8
class :: {
}

// class id: 3355, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class __OtpBottomSheetState&State&CodeAutoFill extends State<dynamic>
     with CodeAutoFill {
}

// class id: 3356, size: 0x24, field offset: 0x14
class _OtpBottomSheetState extends __OtpBottomSheetState&State&CodeAutoFill {

  _ initState(/* No info */) {
    // ** addr: 0x94138c, size: 0x30
    // 0x94138c: EnterFrame
    //     0x94138c: stp             fp, lr, [SP, #-0x10]!
    //     0x941390: mov             fp, SP
    // 0x941394: CheckStackOverflow
    //     0x941394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941398: cmp             SP, x16
    //     0x94139c: b.ls            #0x9413b4
    // 0x9413a0: r0 = registerOtpListenListener()
    //     0x9413a0: bl              #0x905b7c  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::registerOtpListenListener
    // 0x9413a4: r0 = Null
    //     0x9413a4: mov             x0, NULL
    // 0x9413a8: LeaveFrame
    //     0x9413a8: mov             SP, fp
    //     0x9413ac: ldp             fp, lr, [SP], #0x10
    // 0x9413b0: ret
    //     0x9413b0: ret             
    // 0x9413b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9413b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9413b8: b               #0x9413a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa28738, size: 0x4c
    // 0xa28738: ldr             x1, [SP]
    // 0xa2873c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2873c: ldur            w2, [x1, #0x17]
    // 0xa28740: DecompressPointer r2
    //     0xa28740: add             x2, x2, HEAP, lsl #32
    // 0xa28744: LoadField: r1 = r2->field_f
    //     0xa28744: ldur            w1, [x2, #0xf]
    // 0xa28748: DecompressPointer r1
    //     0xa28748: add             x1, x1, HEAP, lsl #32
    // 0xa2874c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa2874c: ldur            w2, [x1, #0x17]
    // 0xa28750: DecompressPointer r2
    //     0xa28750: add             x2, x2, HEAP, lsl #32
    // 0xa28754: LoadField: r3 = r2->field_7
    //     0xa28754: ldur            w3, [x2, #7]
    // 0xa28758: cmp             w3, #8
    // 0xa2875c: b.ne            #0xa28774
    // 0xa28760: cbnz            w3, #0xa2876c
    // 0xa28764: r2 = false
    //     0xa28764: add             x2, NULL, #0x30  ; false
    // 0xa28768: b               #0xa28770
    // 0xa2876c: r2 = true
    //     0xa2876c: add             x2, NULL, #0x20  ; true
    // 0xa28770: b               #0xa28778
    // 0xa28774: r2 = false
    //     0xa28774: add             x2, NULL, #0x30  ; false
    // 0xa28778: StoreField: r1->field_1b = r2
    //     0xa28778: stur            w2, [x1, #0x1b]
    // 0xa2877c: r0 = Null
    //     0xa2877c: mov             x0, NULL
    // 0xa28780: ret
    //     0xa28780: ret             
  }
  _ validateAddress(/* No info */) {
    // ** addr: 0xa28784, size: 0x6c
    // 0xa28784: EnterFrame
    //     0xa28784: stp             fp, lr, [SP, #-0x10]!
    //     0xa28788: mov             fp, SP
    // 0xa2878c: AllocStack(0x8)
    //     0xa2878c: sub             SP, SP, #8
    // 0xa28790: SetupParameters(_OtpBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xa28790: stur            x1, [fp, #-8]
    // 0xa28794: CheckStackOverflow
    //     0xa28794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa28798: cmp             SP, x16
    //     0xa2879c: b.ls            #0xa287e8
    // 0xa287a0: r1 = 1
    //     0xa287a0: movz            x1, #0x1
    // 0xa287a4: r0 = AllocateContext()
    //     0xa287a4: bl              #0x16f6108  ; AllocateContextStub
    // 0xa287a8: mov             x1, x0
    // 0xa287ac: ldur            x0, [fp, #-8]
    // 0xa287b0: StoreField: r1->field_f = r0
    //     0xa287b0: stur            w0, [x1, #0xf]
    // 0xa287b4: mov             x2, x1
    // 0xa287b8: r1 = Function '<anonymous closure>':.
    //     0xa287b8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56758] AnonymousClosure: (0xa28738), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::validateAddress (0xa28784)
    //     0xa287bc: ldr             x1, [x1, #0x758]
    // 0xa287c0: r0 = AllocateClosure()
    //     0xa287c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa287c4: ldur            x1, [fp, #-8]
    // 0xa287c8: mov             x2, x0
    // 0xa287cc: r0 = setState()
    //     0xa287cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa287d0: ldur            x1, [fp, #-8]
    // 0xa287d4: LoadField: r0 = r1->field_1b
    //     0xa287d4: ldur            w0, [x1, #0x1b]
    // 0xa287d8: DecompressPointer r0
    //     0xa287d8: add             x0, x0, HEAP, lsl #32
    // 0xa287dc: LeaveFrame
    //     0xa287dc: mov             SP, fp
    //     0xa287e0: ldp             fp, lr, [SP], #0x10
    // 0xa287e4: ret
    //     0xa287e4: ret             
    // 0xa287e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa287e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa287ec: b               #0xa287a0
  }
  _ build(/* No info */) {
    // ** addr: 0xb4c494, size: 0xa28
    // 0xb4c494: EnterFrame
    //     0xb4c494: stp             fp, lr, [SP, #-0x10]!
    //     0xb4c498: mov             fp, SP
    // 0xb4c49c: AllocStack(0x88)
    //     0xb4c49c: sub             SP, SP, #0x88
    // 0xb4c4a0: SetupParameters(_OtpBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb4c4a0: mov             x0, x1
    //     0xb4c4a4: stur            x1, [fp, #-8]
    //     0xb4c4a8: mov             x1, x2
    //     0xb4c4ac: stur            x2, [fp, #-0x10]
    // 0xb4c4b0: CheckStackOverflow
    //     0xb4c4b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4c4b4: cmp             SP, x16
    //     0xb4c4b8: b.ls            #0xb4ce94
    // 0xb4c4bc: r1 = 2
    //     0xb4c4bc: movz            x1, #0x2
    // 0xb4c4c0: r0 = AllocateContext()
    //     0xb4c4c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4c4c4: mov             x2, x0
    // 0xb4c4c8: ldur            x0, [fp, #-8]
    // 0xb4c4cc: stur            x2, [fp, #-0x18]
    // 0xb4c4d0: StoreField: r2->field_f = r0
    //     0xb4c4d0: stur            w0, [x2, #0xf]
    // 0xb4c4d4: ldur            x1, [fp, #-0x10]
    // 0xb4c4d8: StoreField: r2->field_13 = r1
    //     0xb4c4d8: stur            w1, [x2, #0x13]
    // 0xb4c4dc: r0 = of()
    //     0xb4c4dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c4e0: LoadField: r1 = r0->field_87
    //     0xb4c4e0: ldur            w1, [x0, #0x87]
    // 0xb4c4e4: DecompressPointer r1
    //     0xb4c4e4: add             x1, x1, HEAP, lsl #32
    // 0xb4c4e8: LoadField: r0 = r1->field_7
    //     0xb4c4e8: ldur            w0, [x1, #7]
    // 0xb4c4ec: DecompressPointer r0
    //     0xb4c4ec: add             x0, x0, HEAP, lsl #32
    // 0xb4c4f0: r16 = Instance_Color
    //     0xb4c4f0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4c4f4: r30 = 16.000000
    //     0xb4c4f4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4c4f8: ldr             lr, [lr, #0x188]
    // 0xb4c4fc: stp             lr, x16, [SP]
    // 0xb4c500: mov             x1, x0
    // 0xb4c504: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4c504: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4c508: ldr             x4, [x4, #0x9b8]
    // 0xb4c50c: r0 = copyWith()
    //     0xb4c50c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4c510: stur            x0, [fp, #-0x10]
    // 0xb4c514: r0 = Text()
    //     0xb4c514: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4c518: mov             x1, x0
    // 0xb4c51c: r0 = "Enter OTP to confirm"
    //     0xb4c51c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ee8] "Enter OTP to confirm"
    //     0xb4c520: ldr             x0, [x0, #0xee8]
    // 0xb4c524: stur            x1, [fp, #-0x20]
    // 0xb4c528: StoreField: r1->field_b = r0
    //     0xb4c528: stur            w0, [x1, #0xb]
    // 0xb4c52c: ldur            x0, [fp, #-0x10]
    // 0xb4c530: StoreField: r1->field_13 = r0
    //     0xb4c530: stur            w0, [x1, #0x13]
    // 0xb4c534: r0 = Padding()
    //     0xb4c534: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4c538: mov             x3, x0
    // 0xb4c53c: r0 = Instance_EdgeInsets
    //     0xb4c53c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb4c540: ldr             x0, [x0, #0x668]
    // 0xb4c544: stur            x3, [fp, #-0x10]
    // 0xb4c548: StoreField: r3->field_f = r0
    //     0xb4c548: stur            w0, [x3, #0xf]
    // 0xb4c54c: ldur            x1, [fp, #-0x20]
    // 0xb4c550: StoreField: r3->field_b = r1
    //     0xb4c550: stur            w1, [x3, #0xb]
    // 0xb4c554: r1 = Null
    //     0xb4c554: mov             x1, NULL
    // 0xb4c558: r2 = 4
    //     0xb4c558: movz            x2, #0x4
    // 0xb4c55c: r0 = AllocateArray()
    //     0xb4c55c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4c560: r16 = "Otp sent to "
    //     0xb4c560: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ef0] "Otp sent to "
    //     0xb4c564: ldr             x16, [x16, #0xef0]
    // 0xb4c568: StoreField: r0->field_f = r16
    //     0xb4c568: stur            w16, [x0, #0xf]
    // 0xb4c56c: ldur            x1, [fp, #-8]
    // 0xb4c570: LoadField: r2 = r1->field_b
    //     0xb4c570: ldur            w2, [x1, #0xb]
    // 0xb4c574: DecompressPointer r2
    //     0xb4c574: add             x2, x2, HEAP, lsl #32
    // 0xb4c578: cmp             w2, NULL
    // 0xb4c57c: b.eq            #0xb4ce9c
    // 0xb4c580: LoadField: r3 = r2->field_13
    //     0xb4c580: ldur            w3, [x2, #0x13]
    // 0xb4c584: DecompressPointer r3
    //     0xb4c584: add             x3, x3, HEAP, lsl #32
    // 0xb4c588: StoreField: r0->field_13 = r3
    //     0xb4c588: stur            w3, [x0, #0x13]
    // 0xb4c58c: str             x0, [SP]
    // 0xb4c590: r0 = _interpolate()
    //     0xb4c590: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4c594: ldur            x2, [fp, #-0x18]
    // 0xb4c598: stur            x0, [fp, #-0x20]
    // 0xb4c59c: LoadField: r1 = r2->field_13
    //     0xb4c59c: ldur            w1, [x2, #0x13]
    // 0xb4c5a0: DecompressPointer r1
    //     0xb4c5a0: add             x1, x1, HEAP, lsl #32
    // 0xb4c5a4: r0 = of()
    //     0xb4c5a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c5a8: LoadField: r1 = r0->field_87
    //     0xb4c5a8: ldur            w1, [x0, #0x87]
    // 0xb4c5ac: DecompressPointer r1
    //     0xb4c5ac: add             x1, x1, HEAP, lsl #32
    // 0xb4c5b0: LoadField: r0 = r1->field_2b
    //     0xb4c5b0: ldur            w0, [x1, #0x2b]
    // 0xb4c5b4: DecompressPointer r0
    //     0xb4c5b4: add             x0, x0, HEAP, lsl #32
    // 0xb4c5b8: r16 = Instance_Color
    //     0xb4c5b8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4c5bc: r30 = 12.000000
    //     0xb4c5bc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4c5c0: ldr             lr, [lr, #0x9e8]
    // 0xb4c5c4: stp             lr, x16, [SP]
    // 0xb4c5c8: mov             x1, x0
    // 0xb4c5cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4c5cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4c5d0: ldr             x4, [x4, #0x9b8]
    // 0xb4c5d4: r0 = copyWith()
    //     0xb4c5d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4c5d8: stur            x0, [fp, #-0x28]
    // 0xb4c5dc: r0 = Text()
    //     0xb4c5dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4c5e0: mov             x2, x0
    // 0xb4c5e4: ldur            x0, [fp, #-0x20]
    // 0xb4c5e8: stur            x2, [fp, #-0x30]
    // 0xb4c5ec: StoreField: r2->field_b = r0
    //     0xb4c5ec: stur            w0, [x2, #0xb]
    // 0xb4c5f0: ldur            x0, [fp, #-0x28]
    // 0xb4c5f4: StoreField: r2->field_13 = r0
    //     0xb4c5f4: stur            w0, [x2, #0x13]
    // 0xb4c5f8: ldur            x0, [fp, #-0x18]
    // 0xb4c5fc: LoadField: r1 = r0->field_13
    //     0xb4c5fc: ldur            w1, [x0, #0x13]
    // 0xb4c600: DecompressPointer r1
    //     0xb4c600: add             x1, x1, HEAP, lsl #32
    // 0xb4c604: r0 = of()
    //     0xb4c604: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c608: LoadField: r1 = r0->field_87
    //     0xb4c608: ldur            w1, [x0, #0x87]
    // 0xb4c60c: DecompressPointer r1
    //     0xb4c60c: add             x1, x1, HEAP, lsl #32
    // 0xb4c610: LoadField: r0 = r1->field_2b
    //     0xb4c610: ldur            w0, [x1, #0x2b]
    // 0xb4c614: DecompressPointer r0
    //     0xb4c614: add             x0, x0, HEAP, lsl #32
    // 0xb4c618: r16 = Instance_Color
    //     0xb4c618: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4c61c: r30 = 14.000000
    //     0xb4c61c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4c620: ldr             lr, [lr, #0x1d8]
    // 0xb4c624: stp             lr, x16, [SP, #8]
    // 0xb4c628: r16 = Instance_TextDecoration
    //     0xb4c628: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb4c62c: ldr             x16, [x16, #0x10]
    // 0xb4c630: str             x16, [SP]
    // 0xb4c634: mov             x1, x0
    // 0xb4c638: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xb4c638: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xb4c63c: ldr             x4, [x4, #0x7c8]
    // 0xb4c640: r0 = copyWith()
    //     0xb4c640: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4c644: stur            x0, [fp, #-0x20]
    // 0xb4c648: r0 = Text()
    //     0xb4c648: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4c64c: mov             x1, x0
    // 0xb4c650: r0 = "Edit"
    //     0xb4c650: add             x0, PP, #0x56, lsl #12  ; [pp+0x56620] "Edit"
    //     0xb4c654: ldr             x0, [x0, #0x620]
    // 0xb4c658: stur            x1, [fp, #-0x28]
    // 0xb4c65c: StoreField: r1->field_b = r0
    //     0xb4c65c: stur            w0, [x1, #0xb]
    // 0xb4c660: ldur            x0, [fp, #-0x20]
    // 0xb4c664: StoreField: r1->field_13 = r0
    //     0xb4c664: stur            w0, [x1, #0x13]
    // 0xb4c668: r0 = InkWell()
    //     0xb4c668: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb4c66c: mov             x3, x0
    // 0xb4c670: ldur            x0, [fp, #-0x28]
    // 0xb4c674: stur            x3, [fp, #-0x20]
    // 0xb4c678: StoreField: r3->field_b = r0
    //     0xb4c678: stur            w0, [x3, #0xb]
    // 0xb4c67c: ldur            x2, [fp, #-0x18]
    // 0xb4c680: r1 = Function '<anonymous closure>':.
    //     0xb4c680: add             x1, PP, #0x56, lsl #12  ; [pp+0x566a8] AnonymousClosure: (0xb4db58), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4c684: ldr             x1, [x1, #0x6a8]
    // 0xb4c688: r0 = AllocateClosure()
    //     0xb4c688: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4c68c: mov             x1, x0
    // 0xb4c690: ldur            x0, [fp, #-0x20]
    // 0xb4c694: StoreField: r0->field_f = r1
    //     0xb4c694: stur            w1, [x0, #0xf]
    // 0xb4c698: r1 = true
    //     0xb4c698: add             x1, NULL, #0x20  ; true
    // 0xb4c69c: StoreField: r0->field_43 = r1
    //     0xb4c69c: stur            w1, [x0, #0x43]
    // 0xb4c6a0: r2 = Instance_BoxShape
    //     0xb4c6a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4c6a4: ldr             x2, [x2, #0x80]
    // 0xb4c6a8: StoreField: r0->field_47 = r2
    //     0xb4c6a8: stur            w2, [x0, #0x47]
    // 0xb4c6ac: StoreField: r0->field_6f = r1
    //     0xb4c6ac: stur            w1, [x0, #0x6f]
    // 0xb4c6b0: r2 = false
    //     0xb4c6b0: add             x2, NULL, #0x30  ; false
    // 0xb4c6b4: StoreField: r0->field_73 = r2
    //     0xb4c6b4: stur            w2, [x0, #0x73]
    // 0xb4c6b8: StoreField: r0->field_83 = r1
    //     0xb4c6b8: stur            w1, [x0, #0x83]
    // 0xb4c6bc: StoreField: r0->field_7b = r2
    //     0xb4c6bc: stur            w2, [x0, #0x7b]
    // 0xb4c6c0: r0 = Padding()
    //     0xb4c6c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4c6c4: mov             x3, x0
    // 0xb4c6c8: r0 = Instance_EdgeInsets
    //     0xb4c6c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xb4c6cc: ldr             x0, [x0, #0xe60]
    // 0xb4c6d0: stur            x3, [fp, #-0x28]
    // 0xb4c6d4: StoreField: r3->field_f = r0
    //     0xb4c6d4: stur            w0, [x3, #0xf]
    // 0xb4c6d8: ldur            x0, [fp, #-0x20]
    // 0xb4c6dc: StoreField: r3->field_b = r0
    //     0xb4c6dc: stur            w0, [x3, #0xb]
    // 0xb4c6e0: r1 = Null
    //     0xb4c6e0: mov             x1, NULL
    // 0xb4c6e4: r2 = 4
    //     0xb4c6e4: movz            x2, #0x4
    // 0xb4c6e8: r0 = AllocateArray()
    //     0xb4c6e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4c6ec: mov             x2, x0
    // 0xb4c6f0: ldur            x0, [fp, #-0x30]
    // 0xb4c6f4: stur            x2, [fp, #-0x20]
    // 0xb4c6f8: StoreField: r2->field_f = r0
    //     0xb4c6f8: stur            w0, [x2, #0xf]
    // 0xb4c6fc: ldur            x0, [fp, #-0x28]
    // 0xb4c700: StoreField: r2->field_13 = r0
    //     0xb4c700: stur            w0, [x2, #0x13]
    // 0xb4c704: r1 = <Widget>
    //     0xb4c704: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4c708: r0 = AllocateGrowableArray()
    //     0xb4c708: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4c70c: mov             x1, x0
    // 0xb4c710: ldur            x0, [fp, #-0x20]
    // 0xb4c714: stur            x1, [fp, #-0x28]
    // 0xb4c718: StoreField: r1->field_f = r0
    //     0xb4c718: stur            w0, [x1, #0xf]
    // 0xb4c71c: r2 = 4
    //     0xb4c71c: movz            x2, #0x4
    // 0xb4c720: StoreField: r1->field_b = r2
    //     0xb4c720: stur            w2, [x1, #0xb]
    // 0xb4c724: r0 = Row()
    //     0xb4c724: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4c728: mov             x1, x0
    // 0xb4c72c: r0 = Instance_Axis
    //     0xb4c72c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4c730: stur            x1, [fp, #-0x20]
    // 0xb4c734: StoreField: r1->field_f = r0
    //     0xb4c734: stur            w0, [x1, #0xf]
    // 0xb4c738: r2 = Instance_MainAxisAlignment
    //     0xb4c738: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4c73c: ldr             x2, [x2, #0xa08]
    // 0xb4c740: StoreField: r1->field_13 = r2
    //     0xb4c740: stur            w2, [x1, #0x13]
    // 0xb4c744: r2 = Instance_MainAxisSize
    //     0xb4c744: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4c748: ldr             x2, [x2, #0xa10]
    // 0xb4c74c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb4c74c: stur            w2, [x1, #0x17]
    // 0xb4c750: r2 = Instance_CrossAxisAlignment
    //     0xb4c750: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4c754: ldr             x2, [x2, #0xa18]
    // 0xb4c758: StoreField: r1->field_1b = r2
    //     0xb4c758: stur            w2, [x1, #0x1b]
    // 0xb4c75c: r2 = Instance_VerticalDirection
    //     0xb4c75c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4c760: ldr             x2, [x2, #0xa20]
    // 0xb4c764: StoreField: r1->field_23 = r2
    //     0xb4c764: stur            w2, [x1, #0x23]
    // 0xb4c768: r3 = Instance_Clip
    //     0xb4c768: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4c76c: ldr             x3, [x3, #0x38]
    // 0xb4c770: StoreField: r1->field_2b = r3
    //     0xb4c770: stur            w3, [x1, #0x2b]
    // 0xb4c774: StoreField: r1->field_2f = rZR
    //     0xb4c774: stur            xzr, [x1, #0x2f]
    // 0xb4c778: ldur            x4, [fp, #-0x28]
    // 0xb4c77c: StoreField: r1->field_b = r4
    //     0xb4c77c: stur            w4, [x1, #0xb]
    // 0xb4c780: r0 = Padding()
    //     0xb4c780: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4c784: mov             x1, x0
    // 0xb4c788: r0 = Instance_EdgeInsets
    //     0xb4c788: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xb4c78c: ldr             x0, [x0, #0xe18]
    // 0xb4c790: stur            x1, [fp, #-0x28]
    // 0xb4c794: StoreField: r1->field_f = r0
    //     0xb4c794: stur            w0, [x1, #0xf]
    // 0xb4c798: ldur            x0, [fp, #-0x20]
    // 0xb4c79c: StoreField: r1->field_b = r0
    //     0xb4c79c: stur            w0, [x1, #0xb]
    // 0xb4c7a0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb4c7a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4c7a4: ldr             x0, [x0, #0x1c80]
    //     0xb4c7a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4c7ac: cmp             w0, w16
    //     0xb4c7b0: b.ne            #0xb4c7bc
    //     0xb4c7b4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb4c7b8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4c7bc: r0 = GetNavigation.width()
    //     0xb4c7bc: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xb4c7c0: stur            d0, [fp, #-0x58]
    // 0xb4c7c4: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb4c7c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4c7c8: ldr             x0, [x0, #0x1530]
    //     0xb4c7cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4c7d0: cmp             w0, w16
    //     0xb4c7d4: b.ne            #0xb4c7e4
    //     0xb4c7d8: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb4c7dc: ldr             x2, [x2, #0x120]
    //     0xb4c7e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4c7e4: stur            x0, [fp, #-0x20]
    // 0xb4c7e8: r16 = "[0-9]"
    //     0xb4c7e8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xb4c7ec: ldr             x16, [x16, #0x128]
    // 0xb4c7f0: stp             x16, NULL, [SP, #0x20]
    // 0xb4c7f4: r16 = false
    //     0xb4c7f4: add             x16, NULL, #0x30  ; false
    // 0xb4c7f8: r30 = true
    //     0xb4c7f8: add             lr, NULL, #0x20  ; true
    // 0xb4c7fc: stp             lr, x16, [SP, #0x10]
    // 0xb4c800: r16 = false
    //     0xb4c800: add             x16, NULL, #0x30  ; false
    // 0xb4c804: r30 = false
    //     0xb4c804: add             lr, NULL, #0x30  ; false
    // 0xb4c808: stp             lr, x16, [SP]
    // 0xb4c80c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb4c80c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb4c810: r0 = _RegExp()
    //     0xb4c810: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb4c814: stur            x0, [fp, #-0x30]
    // 0xb4c818: r0 = FilteringTextInputFormatter()
    //     0xb4c818: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb4c81c: mov             x3, x0
    // 0xb4c820: ldur            x0, [fp, #-0x30]
    // 0xb4c824: stur            x3, [fp, #-0x38]
    // 0xb4c828: StoreField: r3->field_b = r0
    //     0xb4c828: stur            w0, [x3, #0xb]
    // 0xb4c82c: r0 = true
    //     0xb4c82c: add             x0, NULL, #0x20  ; true
    // 0xb4c830: StoreField: r3->field_7 = r0
    //     0xb4c830: stur            w0, [x3, #7]
    // 0xb4c834: r1 = ""
    //     0xb4c834: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4c838: StoreField: r3->field_f = r1
    //     0xb4c838: stur            w1, [x3, #0xf]
    // 0xb4c83c: r1 = Null
    //     0xb4c83c: mov             x1, NULL
    // 0xb4c840: r2 = 4
    //     0xb4c840: movz            x2, #0x4
    // 0xb4c844: r0 = AllocateArray()
    //     0xb4c844: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4c848: mov             x2, x0
    // 0xb4c84c: ldur            x0, [fp, #-0x20]
    // 0xb4c850: stur            x2, [fp, #-0x30]
    // 0xb4c854: StoreField: r2->field_f = r0
    //     0xb4c854: stur            w0, [x2, #0xf]
    // 0xb4c858: ldur            x0, [fp, #-0x38]
    // 0xb4c85c: StoreField: r2->field_13 = r0
    //     0xb4c85c: stur            w0, [x2, #0x13]
    // 0xb4c860: r1 = <TextInputFormatter>
    //     0xb4c860: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb4c864: ldr             x1, [x1, #0x7b0]
    // 0xb4c868: r0 = AllocateGrowableArray()
    //     0xb4c868: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4c86c: mov             x2, x0
    // 0xb4c870: ldur            x0, [fp, #-0x30]
    // 0xb4c874: stur            x2, [fp, #-0x20]
    // 0xb4c878: StoreField: r2->field_f = r0
    //     0xb4c878: stur            w0, [x2, #0xf]
    // 0xb4c87c: r0 = 4
    //     0xb4c87c: movz            x0, #0x4
    // 0xb4c880: StoreField: r2->field_b = r0
    //     0xb4c880: stur            w0, [x2, #0xb]
    // 0xb4c884: ldur            x0, [fp, #-0x18]
    // 0xb4c888: LoadField: r1 = r0->field_13
    //     0xb4c888: ldur            w1, [x0, #0x13]
    // 0xb4c88c: DecompressPointer r1
    //     0xb4c88c: add             x1, x1, HEAP, lsl #32
    // 0xb4c890: r0 = of()
    //     0xb4c890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c894: LoadField: r1 = r0->field_5b
    //     0xb4c894: ldur            w1, [x0, #0x5b]
    // 0xb4c898: DecompressPointer r1
    //     0xb4c898: add             x1, x1, HEAP, lsl #32
    // 0xb4c89c: stur            x1, [fp, #-0x30]
    // 0xb4c8a0: r0 = Cursor()
    //     0xb4c8a0: bl              #0xa09868  ; AllocateCursorStub -> Cursor (size=0x3c)
    // 0xb4c8a4: d0 = 1.000000
    //     0xb4c8a4: fmov            d0, #1.00000000
    // 0xb4c8a8: stur            x0, [fp, #-0x38]
    // 0xb4c8ac: StoreField: r0->field_7 = d0
    //     0xb4c8ac: stur            d0, [x0, #7]
    // 0xb4c8b0: d0 = 27.000000
    //     0xb4c8b0: fmov            d0, #27.00000000
    // 0xb4c8b4: StoreField: r0->field_f = d0
    //     0xb4c8b4: stur            d0, [x0, #0xf]
    // 0xb4c8b8: r1 = Instance_Radius
    //     0xb4c8b8: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0xb4c8bc: ldr             x1, [x1, #0xb48]
    // 0xb4c8c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4c8c0: stur            w1, [x0, #0x17]
    // 0xb4c8c4: ldur            x1, [fp, #-0x30]
    // 0xb4c8c8: StoreField: r0->field_1b = r1
    //     0xb4c8c8: stur            w1, [x0, #0x1b]
    // 0xb4c8cc: r1 = Instance_Duration
    //     0xb4c8cc: add             x1, PP, #0xa, lsl #12  ; [pp+0xa058] Obj!Duration@d777a1
    //     0xb4c8d0: ldr             x1, [x1, #0x58]
    // 0xb4c8d4: StoreField: r0->field_1f = r1
    //     0xb4c8d4: stur            w1, [x0, #0x1f]
    // 0xb4c8d8: r1 = Instance_Duration
    //     0xb4c8d8: ldr             x1, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb4c8dc: StoreField: r0->field_23 = r1
    //     0xb4c8dc: stur            w1, [x0, #0x23]
    // 0xb4c8e0: r1 = Instance_Duration
    //     0xb4c8e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4b8] Obj!Duration@d77851
    //     0xb4c8e4: ldr             x1, [x1, #0x4b8]
    // 0xb4c8e8: StoreField: r0->field_27 = r1
    //     0xb4c8e8: stur            w1, [x0, #0x27]
    // 0xb4c8ec: r1 = Instance_Orientation
    //     0xb4c8ec: add             x1, PP, #0x37, lsl #12  ; [pp+0x37198] Obj!Orientation@d70241
    //     0xb4c8f0: ldr             x1, [x1, #0x198]
    // 0xb4c8f4: StoreField: r0->field_2f = r1
    //     0xb4c8f4: stur            w1, [x0, #0x2f]
    // 0xb4c8f8: StoreField: r0->field_33 = rZR
    //     0xb4c8f8: stur            xzr, [x0, #0x33]
    // 0xb4c8fc: r2 = true
    //     0xb4c8fc: add             x2, NULL, #0x20  ; true
    // 0xb4c900: StoreField: r0->field_2b = r2
    //     0xb4c900: stur            w2, [x0, #0x2b]
    // 0xb4c904: ldur            x3, [fp, #-0x18]
    // 0xb4c908: LoadField: r1 = r3->field_13
    //     0xb4c908: ldur            w1, [x3, #0x13]
    // 0xb4c90c: DecompressPointer r1
    //     0xb4c90c: add             x1, x1, HEAP, lsl #32
    // 0xb4c910: r0 = of()
    //     0xb4c910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c914: LoadField: r1 = r0->field_5b
    //     0xb4c914: ldur            w1, [x0, #0x5b]
    // 0xb4c918: DecompressPointer r1
    //     0xb4c918: add             x1, x1, HEAP, lsl #32
    // 0xb4c91c: r0 = LoadClassIdInstr(r1)
    //     0xb4c91c: ldur            x0, [x1, #-1]
    //     0xb4c920: ubfx            x0, x0, #0xc, #0x14
    // 0xb4c924: d0 = 0.100000
    //     0xb4c924: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4c928: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4c928: sub             lr, x0, #0xffa
    //     0xb4c92c: ldr             lr, [x21, lr, lsl #3]
    //     0xb4c930: blr             lr
    // 0xb4c934: r1 = <Color>
    //     0xb4c934: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb4c938: ldr             x1, [x1, #0xf80]
    // 0xb4c93c: stur            x0, [fp, #-0x30]
    // 0xb4c940: r0 = FixedColorBuilder()
    //     0xb4c940: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0xb4c944: mov             x2, x0
    // 0xb4c948: ldur            x0, [fp, #-0x30]
    // 0xb4c94c: stur            x2, [fp, #-0x40]
    // 0xb4c950: StoreField: r2->field_b = r0
    //     0xb4c950: stur            w0, [x2, #0xb]
    // 0xb4c954: ldur            x0, [fp, #-0x18]
    // 0xb4c958: LoadField: r1 = r0->field_13
    //     0xb4c958: ldur            w1, [x0, #0x13]
    // 0xb4c95c: DecompressPointer r1
    //     0xb4c95c: add             x1, x1, HEAP, lsl #32
    // 0xb4c960: r0 = of()
    //     0xb4c960: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c964: LoadField: r1 = r0->field_87
    //     0xb4c964: ldur            w1, [x0, #0x87]
    // 0xb4c968: DecompressPointer r1
    //     0xb4c968: add             x1, x1, HEAP, lsl #32
    // 0xb4c96c: LoadField: r0 = r1->field_2b
    //     0xb4c96c: ldur            w0, [x1, #0x2b]
    // 0xb4c970: DecompressPointer r0
    //     0xb4c970: add             x0, x0, HEAP, lsl #32
    // 0xb4c974: ldur            x2, [fp, #-0x18]
    // 0xb4c978: stur            x0, [fp, #-0x30]
    // 0xb4c97c: LoadField: r1 = r2->field_13
    //     0xb4c97c: ldur            w1, [x2, #0x13]
    // 0xb4c980: DecompressPointer r1
    //     0xb4c980: add             x1, x1, HEAP, lsl #32
    // 0xb4c984: r0 = of()
    //     0xb4c984: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4c988: LoadField: r1 = r0->field_5b
    //     0xb4c988: ldur            w1, [x0, #0x5b]
    // 0xb4c98c: DecompressPointer r1
    //     0xb4c98c: add             x1, x1, HEAP, lsl #32
    // 0xb4c990: r16 = 16.000000
    //     0xb4c990: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4c994: ldr             x16, [x16, #0x188]
    // 0xb4c998: stp             x1, x16, [SP]
    // 0xb4c99c: ldur            x1, [fp, #-0x30]
    // 0xb4c9a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4c9a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4c9a4: ldr             x4, [x4, #0xaa0]
    // 0xb4c9a8: r0 = copyWith()
    //     0xb4c9a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4c9ac: r1 = Instance_Color
    //     0xb4c9ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4c9b0: d0 = 0.700000
    //     0xb4c9b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4c9b4: ldr             d0, [x17, #0xf48]
    // 0xb4c9b8: stur            x0, [fp, #-0x30]
    // 0xb4c9bc: r0 = withOpacity()
    //     0xb4c9bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4c9c0: stur            x0, [fp, #-0x48]
    // 0xb4c9c4: r0 = TextStyle()
    //     0xb4c9c4: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb4c9c8: mov             x1, x0
    // 0xb4c9cc: r0 = true
    //     0xb4c9cc: add             x0, NULL, #0x20  ; true
    // 0xb4c9d0: stur            x1, [fp, #-0x50]
    // 0xb4c9d4: StoreField: r1->field_7 = r0
    //     0xb4c9d4: stur            w0, [x1, #7]
    // 0xb4c9d8: ldur            x2, [fp, #-0x48]
    // 0xb4c9dc: StoreField: r1->field_b = r2
    //     0xb4c9dc: stur            w2, [x1, #0xb]
    // 0xb4c9e0: r2 = 20.000000
    //     0xb4c9e0: add             x2, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb4c9e4: ldr             x2, [x2, #0xac8]
    // 0xb4c9e8: StoreField: r1->field_1f = r2
    //     0xb4c9e8: stur            w2, [x1, #0x1f]
    // 0xb4c9ec: r0 = BoxLooseDecoration()
    //     0xb4c9ec: bl              #0xb4d24c  ; AllocateBoxLooseDecorationStub -> BoxLooseDecoration (size=0x48)
    // 0xb4c9f0: stur            x0, [fp, #-0x48]
    // 0xb4c9f4: r16 = 12.000000
    //     0xb4c9f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4c9f8: ldr             x16, [x16, #0x9e8]
    // 0xb4c9fc: r30 = 1.500000
    //     0xb4c9fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd18] 1.5
    //     0xb4ca00: ldr             lr, [lr, #0xd18]
    // 0xb4ca04: stp             lr, x16, [SP, #0x20]
    // 0xb4ca08: r16 = Instance_Radius
    //     0xb4ca08: add             x16, PP, #0x40, lsl #12  ; [pp+0x40080] Obj!Radius@d6bee1
    //     0xb4ca0c: ldr             x16, [x16, #0x80]
    // 0xb4ca10: r30 = Instance_FixedColorBuilder
    //     0xb4ca10: add             lr, PP, #0x40, lsl #12  ; [pp+0x402f8] Obj!FixedColorBuilder@d53ab1
    //     0xb4ca14: ldr             lr, [lr, #0x2f8]
    // 0xb4ca18: stp             lr, x16, [SP, #0x10]
    // 0xb4ca1c: r16 = "----"
    //     0xb4ca1c: add             x16, PP, #0x56, lsl #12  ; [pp+0x566b0] "----"
    //     0xb4ca20: ldr             x16, [x16, #0x6b0]
    // 0xb4ca24: ldur            lr, [fp, #-0x50]
    // 0xb4ca28: stp             lr, x16, [SP]
    // 0xb4ca2c: mov             x1, x0
    // 0xb4ca30: ldur            x2, [fp, #-0x40]
    // 0xb4ca34: ldur            x3, [fp, #-0x30]
    // 0xb4ca38: r4 = const [0, 0x9, 0x6, 0x3, bgColorBuilder, 0x6, gapSpace, 0x3, hintText, 0x7, hintTextStyle, 0x8, radius, 0x5, strokeWidth, 0x4, null]
    //     0xb4ca38: add             x4, PP, #0x56, lsl #12  ; [pp+0x566b8] List(17) [0, 0x9, 0x6, 0x3, "bgColorBuilder", 0x6, "gapSpace", 0x3, "hintText", 0x7, "hintTextStyle", 0x8, "radius", 0x5, "strokeWidth", 0x4, Null]
    //     0xb4ca3c: ldr             x4, [x4, #0x6b8]
    // 0xb4ca40: r0 = BoxLooseDecoration()
    //     0xb4ca40: bl              #0xb4cebc  ; [package:pin_input_text_field/src/decoration/pin_decoration.dart] BoxLooseDecoration::BoxLooseDecoration
    // 0xb4ca44: ldur            x0, [fp, #-8]
    // 0xb4ca48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb4ca48: ldur            w1, [x0, #0x17]
    // 0xb4ca4c: DecompressPointer r1
    //     0xb4ca4c: add             x1, x1, HEAP, lsl #32
    // 0xb4ca50: stur            x1, [fp, #-0x30]
    // 0xb4ca54: r0 = PinFieldAutoFill()
    //     0xb4ca54: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0xb4ca58: mov             x3, x0
    // 0xb4ca5c: r0 = Instance_TextInputType
    //     0xb4ca5c: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb4ca60: ldr             x0, [x0, #0x1a0]
    // 0xb4ca64: stur            x3, [fp, #-0x40]
    // 0xb4ca68: StoreField: r3->field_33 = r0
    //     0xb4ca68: stur            w0, [x3, #0x33]
    // 0xb4ca6c: r0 = Instance_TextInputAction
    //     0xb4ca6c: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0xb4ca70: StoreField: r3->field_37 = r0
    //     0xb4ca70: stur            w0, [x3, #0x37]
    // 0xb4ca74: ldur            x0, [fp, #-0x38]
    // 0xb4ca78: StoreField: r3->field_2f = r0
    //     0xb4ca78: stur            w0, [x3, #0x2f]
    // 0xb4ca7c: ldur            x0, [fp, #-0x20]
    // 0xb4ca80: StoreField: r3->field_47 = r0
    //     0xb4ca80: stur            w0, [x3, #0x47]
    // 0xb4ca84: r0 = true
    //     0xb4ca84: add             x0, NULL, #0x20  ; true
    // 0xb4ca88: StoreField: r3->field_3b = r0
    //     0xb4ca88: stur            w0, [x3, #0x3b]
    // 0xb4ca8c: StoreField: r3->field_3f = r0
    //     0xb4ca8c: stur            w0, [x3, #0x3f]
    // 0xb4ca90: ldur            x1, [fp, #-0x48]
    // 0xb4ca94: StoreField: r3->field_27 = r1
    //     0xb4ca94: stur            w1, [x3, #0x27]
    // 0xb4ca98: ldur            x2, [fp, #-0x18]
    // 0xb4ca9c: r1 = Function '<anonymous closure>':.
    //     0xb4ca9c: add             x1, PP, #0x56, lsl #12  ; [pp+0x566c0] AnonymousClosure: (0xb4daf0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4caa0: ldr             x1, [x1, #0x6c0]
    // 0xb4caa4: r0 = AllocateClosure()
    //     0xb4caa4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4caa8: mov             x1, x0
    // 0xb4caac: ldur            x0, [fp, #-0x40]
    // 0xb4cab0: StoreField: r0->field_1f = r1
    //     0xb4cab0: stur            w1, [x0, #0x1f]
    // 0xb4cab4: ldur            x2, [fp, #-0x18]
    // 0xb4cab8: r1 = Function '<anonymous closure>':.
    //     0xb4cab8: add             x1, PP, #0x56, lsl #12  ; [pp+0x566c8] AnonymousClosure: (0xb4d9d0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4cabc: ldr             x1, [x1, #0x6c8]
    // 0xb4cac0: r0 = AllocateClosure()
    //     0xb4cac0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4cac4: mov             x1, x0
    // 0xb4cac8: ldur            x0, [fp, #-0x40]
    // 0xb4cacc: StoreField: r0->field_23 = r1
    //     0xb4cacc: stur            w1, [x0, #0x23]
    // 0xb4cad0: ldur            x1, [fp, #-0x30]
    // 0xb4cad4: StoreField: r0->field_1b = r1
    //     0xb4cad4: stur            w1, [x0, #0x1b]
    // 0xb4cad8: r1 = false
    //     0xb4cad8: add             x1, NULL, #0x30  ; false
    // 0xb4cadc: StoreField: r0->field_13 = r1
    //     0xb4cadc: stur            w1, [x0, #0x13]
    // 0xb4cae0: r2 = 4
    //     0xb4cae0: movz            x2, #0x4
    // 0xb4cae4: StoreField: r0->field_b = r2
    //     0xb4cae4: stur            x2, [x0, #0xb]
    // 0xb4cae8: ldur            d0, [fp, #-0x58]
    // 0xb4caec: r2 = inline_Allocate_Double()
    //     0xb4caec: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb4caf0: add             x2, x2, #0x10
    //     0xb4caf4: cmp             x3, x2
    //     0xb4caf8: b.ls            #0xb4cea0
    //     0xb4cafc: str             x2, [THR, #0x50]  ; THR::top
    //     0xb4cb00: sub             x2, x2, #0xf
    //     0xb4cb04: movz            x3, #0xe15c
    //     0xb4cb08: movk            x3, #0x3, lsl #16
    //     0xb4cb0c: stur            x3, [x2, #-1]
    // 0xb4cb10: StoreField: r2->field_7 = d0
    //     0xb4cb10: stur            d0, [x2, #7]
    // 0xb4cb14: stur            x2, [fp, #-0x20]
    // 0xb4cb18: r0 = Container()
    //     0xb4cb18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4cb1c: stur            x0, [fp, #-0x30]
    // 0xb4cb20: r16 = Instance_EdgeInsets
    //     0xb4cb20: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f18] Obj!EdgeInsets@d57ec1
    //     0xb4cb24: ldr             x16, [x16, #0xf18]
    // 0xb4cb28: ldur            lr, [fp, #-0x20]
    // 0xb4cb2c: stp             lr, x16, [SP, #8]
    // 0xb4cb30: ldur            x16, [fp, #-0x40]
    // 0xb4cb34: str             x16, [SP]
    // 0xb4cb38: mov             x1, x0
    // 0xb4cb3c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb4cb3c: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb4cb40: ldr             x4, [x4, #0x1b8]
    // 0xb4cb44: r0 = Container()
    //     0xb4cb44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4cb48: r0 = Padding()
    //     0xb4cb48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4cb4c: mov             x3, x0
    // 0xb4cb50: r0 = Instance_EdgeInsets
    //     0xb4cb50: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f20] Obj!EdgeInsets@d57e91
    //     0xb4cb54: ldr             x0, [x0, #0xf20]
    // 0xb4cb58: stur            x3, [fp, #-0x38]
    // 0xb4cb5c: StoreField: r3->field_f = r0
    //     0xb4cb5c: stur            w0, [x3, #0xf]
    // 0xb4cb60: ldur            x0, [fp, #-0x30]
    // 0xb4cb64: StoreField: r3->field_b = r0
    //     0xb4cb64: stur            w0, [x3, #0xb]
    // 0xb4cb68: ldur            x0, [fp, #-8]
    // 0xb4cb6c: LoadField: r4 = r0->field_1f
    //     0xb4cb6c: ldur            w4, [x0, #0x1f]
    // 0xb4cb70: DecompressPointer r4
    //     0xb4cb70: add             x4, x4, HEAP, lsl #32
    // 0xb4cb74: ldur            x2, [fp, #-0x18]
    // 0xb4cb78: stur            x4, [fp, #-0x20]
    // 0xb4cb7c: r1 = Function '<anonymous closure>':.
    //     0xb4cb7c: add             x1, PP, #0x56, lsl #12  ; [pp+0x566d0] AnonymousClosure: (0xb4d2e8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4cb80: ldr             x1, [x1, #0x6d0]
    // 0xb4cb84: r0 = AllocateClosure()
    //     0xb4cb84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4cb88: r1 = <int, AsyncSnapshot<int>, int>
    //     0xb4cb88: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f30] TypeArguments: <int, AsyncSnapshot<int>, int>
    //     0xb4cb8c: ldr             x1, [x1, #0xf30]
    // 0xb4cb90: stur            x0, [fp, #-0x30]
    // 0xb4cb94: r0 = StreamBuilder()
    //     0xb4cb94: bl              #0xa09838  ; AllocateStreamBuilderStub -> StreamBuilder<C2X0> (size=0x1c)
    // 0xb4cb98: mov             x1, x0
    // 0xb4cb9c: ldur            x0, [fp, #-0x30]
    // 0xb4cba0: stur            x1, [fp, #-0x40]
    // 0xb4cba4: StoreField: r1->field_13 = r0
    //     0xb4cba4: stur            w0, [x1, #0x13]
    // 0xb4cba8: ldur            x0, [fp, #-0x20]
    // 0xb4cbac: StoreField: r1->field_f = r0
    //     0xb4cbac: stur            w0, [x1, #0xf]
    // 0xb4cbb0: r0 = Padding()
    //     0xb4cbb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4cbb4: mov             x2, x0
    // 0xb4cbb8: r0 = Instance_EdgeInsets
    //     0xb4cbb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb4cbbc: ldr             x0, [x0, #0x668]
    // 0xb4cbc0: stur            x2, [fp, #-0x20]
    // 0xb4cbc4: StoreField: r2->field_f = r0
    //     0xb4cbc4: stur            w0, [x2, #0xf]
    // 0xb4cbc8: ldur            x1, [fp, #-0x40]
    // 0xb4cbcc: StoreField: r2->field_b = r1
    //     0xb4cbcc: stur            w1, [x2, #0xb]
    // 0xb4cbd0: ldur            x1, [fp, #-8]
    // 0xb4cbd4: LoadField: r3 = r1->field_1b
    //     0xb4cbd4: ldur            w3, [x1, #0x1b]
    // 0xb4cbd8: DecompressPointer r3
    //     0xb4cbd8: add             x3, x3, HEAP, lsl #32
    // 0xb4cbdc: tbnz            w3, #4, #0xb4cc00
    // 0xb4cbe0: ldur            x3, [fp, #-0x18]
    // 0xb4cbe4: LoadField: r1 = r3->field_13
    //     0xb4cbe4: ldur            w1, [x3, #0x13]
    // 0xb4cbe8: DecompressPointer r1
    //     0xb4cbe8: add             x1, x1, HEAP, lsl #32
    // 0xb4cbec: r0 = of()
    //     0xb4cbec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4cbf0: LoadField: r1 = r0->field_5b
    //     0xb4cbf0: ldur            w1, [x0, #0x5b]
    // 0xb4cbf4: DecompressPointer r1
    //     0xb4cbf4: add             x1, x1, HEAP, lsl #32
    // 0xb4cbf8: mov             x5, x1
    // 0xb4cbfc: b               #0xb4cc34
    // 0xb4cc00: ldur            x2, [fp, #-0x18]
    // 0xb4cc04: LoadField: r1 = r2->field_13
    //     0xb4cc04: ldur            w1, [x2, #0x13]
    // 0xb4cc08: DecompressPointer r1
    //     0xb4cc08: add             x1, x1, HEAP, lsl #32
    // 0xb4cc0c: r0 = of()
    //     0xb4cc0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4cc10: LoadField: r1 = r0->field_5b
    //     0xb4cc10: ldur            w1, [x0, #0x5b]
    // 0xb4cc14: DecompressPointer r1
    //     0xb4cc14: add             x1, x1, HEAP, lsl #32
    // 0xb4cc18: r0 = LoadClassIdInstr(r1)
    //     0xb4cc18: ldur            x0, [x1, #-1]
    //     0xb4cc1c: ubfx            x0, x0, #0xc, #0x14
    // 0xb4cc20: d0 = 0.400000
    //     0xb4cc20: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4cc24: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4cc24: sub             lr, x0, #0xffa
    //     0xb4cc28: ldr             lr, [x21, lr, lsl #3]
    //     0xb4cc2c: blr             lr
    // 0xb4cc30: mov             x5, x0
    // 0xb4cc34: ldur            x2, [fp, #-0x18]
    // 0xb4cc38: ldur            x4, [fp, #-0x10]
    // 0xb4cc3c: ldur            x3, [fp, #-0x28]
    // 0xb4cc40: ldur            x1, [fp, #-0x38]
    // 0xb4cc44: ldur            x0, [fp, #-0x20]
    // 0xb4cc48: r16 = <Color>
    //     0xb4cc48: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb4cc4c: ldr             x16, [x16, #0xf80]
    // 0xb4cc50: stp             x5, x16, [SP]
    // 0xb4cc54: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4cc54: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4cc58: r0 = all()
    //     0xb4cc58: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4cc5c: stur            x0, [fp, #-8]
    // 0xb4cc60: r16 = <RoundedRectangleBorder>
    //     0xb4cc60: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb4cc64: ldr             x16, [x16, #0xf78]
    // 0xb4cc68: r30 = Instance_RoundedRectangleBorder
    //     0xb4cc68: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb4cc6c: ldr             lr, [lr, #0x888]
    // 0xb4cc70: stp             lr, x16, [SP]
    // 0xb4cc74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4cc74: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4cc78: r0 = all()
    //     0xb4cc78: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4cc7c: stur            x0, [fp, #-0x30]
    // 0xb4cc80: r0 = ButtonStyle()
    //     0xb4cc80: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb4cc84: mov             x1, x0
    // 0xb4cc88: ldur            x0, [fp, #-8]
    // 0xb4cc8c: stur            x1, [fp, #-0x40]
    // 0xb4cc90: StoreField: r1->field_b = r0
    //     0xb4cc90: stur            w0, [x1, #0xb]
    // 0xb4cc94: ldur            x0, [fp, #-0x30]
    // 0xb4cc98: StoreField: r1->field_43 = r0
    //     0xb4cc98: stur            w0, [x1, #0x43]
    // 0xb4cc9c: r0 = TextButtonThemeData()
    //     0xb4cc9c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb4cca0: mov             x2, x0
    // 0xb4cca4: ldur            x0, [fp, #-0x40]
    // 0xb4cca8: stur            x2, [fp, #-8]
    // 0xb4ccac: StoreField: r2->field_7 = r0
    //     0xb4ccac: stur            w0, [x2, #7]
    // 0xb4ccb0: ldur            x0, [fp, #-0x18]
    // 0xb4ccb4: LoadField: r1 = r0->field_13
    //     0xb4ccb4: ldur            w1, [x0, #0x13]
    // 0xb4ccb8: DecompressPointer r1
    //     0xb4ccb8: add             x1, x1, HEAP, lsl #32
    // 0xb4ccbc: r0 = of()
    //     0xb4ccbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4ccc0: LoadField: r1 = r0->field_87
    //     0xb4ccc0: ldur            w1, [x0, #0x87]
    // 0xb4ccc4: DecompressPointer r1
    //     0xb4ccc4: add             x1, x1, HEAP, lsl #32
    // 0xb4ccc8: LoadField: r0 = r1->field_7
    //     0xb4ccc8: ldur            w0, [x1, #7]
    // 0xb4cccc: DecompressPointer r0
    //     0xb4cccc: add             x0, x0, HEAP, lsl #32
    // 0xb4ccd0: r16 = 16.000000
    //     0xb4ccd0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4ccd4: ldr             x16, [x16, #0x188]
    // 0xb4ccd8: r30 = Instance_Color
    //     0xb4ccd8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4ccdc: stp             lr, x16, [SP]
    // 0xb4cce0: mov             x1, x0
    // 0xb4cce4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4cce4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4cce8: ldr             x4, [x4, #0xaa0]
    // 0xb4ccec: r0 = copyWith()
    //     0xb4ccec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4ccf0: stur            x0, [fp, #-0x30]
    // 0xb4ccf4: r0 = Text()
    //     0xb4ccf4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4ccf8: mov             x3, x0
    // 0xb4ccfc: r0 = "Confirm"
    //     0xb4ccfc: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cec0] "Confirm"
    //     0xb4cd00: ldr             x0, [x0, #0xec0]
    // 0xb4cd04: stur            x3, [fp, #-0x40]
    // 0xb4cd08: StoreField: r3->field_b = r0
    //     0xb4cd08: stur            w0, [x3, #0xb]
    // 0xb4cd0c: ldur            x0, [fp, #-0x30]
    // 0xb4cd10: StoreField: r3->field_13 = r0
    //     0xb4cd10: stur            w0, [x3, #0x13]
    // 0xb4cd14: ldur            x2, [fp, #-0x18]
    // 0xb4cd18: r1 = Function '<anonymous closure>':.
    //     0xb4cd18: add             x1, PP, #0x56, lsl #12  ; [pp+0x566d8] AnonymousClosure: (0xb4d258), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4cd1c: ldr             x1, [x1, #0x6d8]
    // 0xb4cd20: r0 = AllocateClosure()
    //     0xb4cd20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4cd24: stur            x0, [fp, #-0x18]
    // 0xb4cd28: r0 = TextButton()
    //     0xb4cd28: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb4cd2c: mov             x1, x0
    // 0xb4cd30: ldur            x0, [fp, #-0x18]
    // 0xb4cd34: stur            x1, [fp, #-0x30]
    // 0xb4cd38: StoreField: r1->field_b = r0
    //     0xb4cd38: stur            w0, [x1, #0xb]
    // 0xb4cd3c: r0 = false
    //     0xb4cd3c: add             x0, NULL, #0x30  ; false
    // 0xb4cd40: StoreField: r1->field_27 = r0
    //     0xb4cd40: stur            w0, [x1, #0x27]
    // 0xb4cd44: r0 = true
    //     0xb4cd44: add             x0, NULL, #0x20  ; true
    // 0xb4cd48: StoreField: r1->field_2f = r0
    //     0xb4cd48: stur            w0, [x1, #0x2f]
    // 0xb4cd4c: ldur            x0, [fp, #-0x40]
    // 0xb4cd50: StoreField: r1->field_37 = r0
    //     0xb4cd50: stur            w0, [x1, #0x37]
    // 0xb4cd54: r0 = TextButtonTheme()
    //     0xb4cd54: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb4cd58: mov             x1, x0
    // 0xb4cd5c: ldur            x0, [fp, #-8]
    // 0xb4cd60: stur            x1, [fp, #-0x18]
    // 0xb4cd64: StoreField: r1->field_f = r0
    //     0xb4cd64: stur            w0, [x1, #0xf]
    // 0xb4cd68: ldur            x0, [fp, #-0x30]
    // 0xb4cd6c: StoreField: r1->field_b = r0
    //     0xb4cd6c: stur            w0, [x1, #0xb]
    // 0xb4cd70: r0 = SizedBox()
    //     0xb4cd70: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb4cd74: mov             x1, x0
    // 0xb4cd78: r0 = inf
    //     0xb4cd78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb4cd7c: ldr             x0, [x0, #0x9f8]
    // 0xb4cd80: stur            x1, [fp, #-8]
    // 0xb4cd84: StoreField: r1->field_f = r0
    //     0xb4cd84: stur            w0, [x1, #0xf]
    // 0xb4cd88: r0 = 44.000000
    //     0xb4cd88: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb4cd8c: ldr             x0, [x0, #0xad8]
    // 0xb4cd90: StoreField: r1->field_13 = r0
    //     0xb4cd90: stur            w0, [x1, #0x13]
    // 0xb4cd94: ldur            x0, [fp, #-0x18]
    // 0xb4cd98: StoreField: r1->field_b = r0
    //     0xb4cd98: stur            w0, [x1, #0xb]
    // 0xb4cd9c: r0 = Padding()
    //     0xb4cd9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4cda0: mov             x3, x0
    // 0xb4cda4: r0 = Instance_EdgeInsets
    //     0xb4cda4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb4cda8: ldr             x0, [x0, #0x668]
    // 0xb4cdac: stur            x3, [fp, #-0x18]
    // 0xb4cdb0: StoreField: r3->field_f = r0
    //     0xb4cdb0: stur            w0, [x3, #0xf]
    // 0xb4cdb4: ldur            x0, [fp, #-8]
    // 0xb4cdb8: StoreField: r3->field_b = r0
    //     0xb4cdb8: stur            w0, [x3, #0xb]
    // 0xb4cdbc: r1 = Null
    //     0xb4cdbc: mov             x1, NULL
    // 0xb4cdc0: r2 = 10
    //     0xb4cdc0: movz            x2, #0xa
    // 0xb4cdc4: r0 = AllocateArray()
    //     0xb4cdc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4cdc8: mov             x2, x0
    // 0xb4cdcc: ldur            x0, [fp, #-0x10]
    // 0xb4cdd0: stur            x2, [fp, #-8]
    // 0xb4cdd4: StoreField: r2->field_f = r0
    //     0xb4cdd4: stur            w0, [x2, #0xf]
    // 0xb4cdd8: ldur            x0, [fp, #-0x28]
    // 0xb4cddc: StoreField: r2->field_13 = r0
    //     0xb4cddc: stur            w0, [x2, #0x13]
    // 0xb4cde0: ldur            x0, [fp, #-0x38]
    // 0xb4cde4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4cde4: stur            w0, [x2, #0x17]
    // 0xb4cde8: ldur            x0, [fp, #-0x20]
    // 0xb4cdec: StoreField: r2->field_1b = r0
    //     0xb4cdec: stur            w0, [x2, #0x1b]
    // 0xb4cdf0: ldur            x0, [fp, #-0x18]
    // 0xb4cdf4: StoreField: r2->field_1f = r0
    //     0xb4cdf4: stur            w0, [x2, #0x1f]
    // 0xb4cdf8: r1 = <Widget>
    //     0xb4cdf8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4cdfc: r0 = AllocateGrowableArray()
    //     0xb4cdfc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4ce00: mov             x1, x0
    // 0xb4ce04: ldur            x0, [fp, #-8]
    // 0xb4ce08: stur            x1, [fp, #-0x10]
    // 0xb4ce0c: StoreField: r1->field_f = r0
    //     0xb4ce0c: stur            w0, [x1, #0xf]
    // 0xb4ce10: r0 = 10
    //     0xb4ce10: movz            x0, #0xa
    // 0xb4ce14: StoreField: r1->field_b = r0
    //     0xb4ce14: stur            w0, [x1, #0xb]
    // 0xb4ce18: r0 = Wrap()
    //     0xb4ce18: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xb4ce1c: mov             x1, x0
    // 0xb4ce20: r0 = Instance_Axis
    //     0xb4ce20: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4ce24: stur            x1, [fp, #-8]
    // 0xb4ce28: StoreField: r1->field_f = r0
    //     0xb4ce28: stur            w0, [x1, #0xf]
    // 0xb4ce2c: r0 = Instance_WrapAlignment
    //     0xb4ce2c: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xb4ce30: ldr             x0, [x0, #0x6e8]
    // 0xb4ce34: StoreField: r1->field_13 = r0
    //     0xb4ce34: stur            w0, [x1, #0x13]
    // 0xb4ce38: ArrayStore: r1[0] = rZR  ; List_8
    //     0xb4ce38: stur            xzr, [x1, #0x17]
    // 0xb4ce3c: StoreField: r1->field_1f = r0
    //     0xb4ce3c: stur            w0, [x1, #0x1f]
    // 0xb4ce40: StoreField: r1->field_23 = rZR
    //     0xb4ce40: stur            xzr, [x1, #0x23]
    // 0xb4ce44: r0 = Instance_WrapCrossAlignment
    //     0xb4ce44: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xb4ce48: ldr             x0, [x0, #0x6f0]
    // 0xb4ce4c: StoreField: r1->field_2b = r0
    //     0xb4ce4c: stur            w0, [x1, #0x2b]
    // 0xb4ce50: r0 = Instance_VerticalDirection
    //     0xb4ce50: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4ce54: ldr             x0, [x0, #0xa20]
    // 0xb4ce58: StoreField: r1->field_33 = r0
    //     0xb4ce58: stur            w0, [x1, #0x33]
    // 0xb4ce5c: r0 = Instance_Clip
    //     0xb4ce5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4ce60: ldr             x0, [x0, #0x38]
    // 0xb4ce64: StoreField: r1->field_37 = r0
    //     0xb4ce64: stur            w0, [x1, #0x37]
    // 0xb4ce68: ldur            x0, [fp, #-0x10]
    // 0xb4ce6c: StoreField: r1->field_b = r0
    //     0xb4ce6c: stur            w0, [x1, #0xb]
    // 0xb4ce70: r0 = Padding()
    //     0xb4ce70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4ce74: r1 = Instance_EdgeInsets
    //     0xb4ce74: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f340] Obj!EdgeInsets@d58161
    //     0xb4ce78: ldr             x1, [x1, #0x340]
    // 0xb4ce7c: StoreField: r0->field_f = r1
    //     0xb4ce7c: stur            w1, [x0, #0xf]
    // 0xb4ce80: ldur            x1, [fp, #-8]
    // 0xb4ce84: StoreField: r0->field_b = r1
    //     0xb4ce84: stur            w1, [x0, #0xb]
    // 0xb4ce88: LeaveFrame
    //     0xb4ce88: mov             SP, fp
    //     0xb4ce8c: ldp             fp, lr, [SP], #0x10
    // 0xb4ce90: ret
    //     0xb4ce90: ret             
    // 0xb4ce94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4ce94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4ce98: b               #0xb4c4bc
    // 0xb4ce9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4ce9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4cea0: SaveReg d0
    //     0xb4cea0: str             q0, [SP, #-0x10]!
    // 0xb4cea4: stp             x0, x1, [SP, #-0x10]!
    // 0xb4cea8: r0 = AllocateDouble()
    //     0xb4cea8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb4ceac: mov             x2, x0
    // 0xb4ceb0: ldp             x0, x1, [SP], #0x10
    // 0xb4ceb4: RestoreReg d0
    //     0xb4ceb4: ldr             q0, [SP], #0x10
    // 0xb4ceb8: b               #0xb4cb10
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4d258, size: 0x90
    // 0xb4d258: EnterFrame
    //     0xb4d258: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d25c: mov             fp, SP
    // 0xb4d260: AllocStack(0x10)
    //     0xb4d260: sub             SP, SP, #0x10
    // 0xb4d264: SetupParameters()
    //     0xb4d264: ldr             x0, [fp, #0x10]
    //     0xb4d268: ldur            w1, [x0, #0x17]
    //     0xb4d26c: add             x1, x1, HEAP, lsl #32
    // 0xb4d270: CheckStackOverflow
    //     0xb4d270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d274: cmp             SP, x16
    //     0xb4d278: b.ls            #0xb4d2dc
    // 0xb4d27c: LoadField: r0 = r1->field_f
    //     0xb4d27c: ldur            w0, [x1, #0xf]
    // 0xb4d280: DecompressPointer r0
    //     0xb4d280: add             x0, x0, HEAP, lsl #32
    // 0xb4d284: LoadField: r1 = r0->field_1b
    //     0xb4d284: ldur            w1, [x0, #0x1b]
    // 0xb4d288: DecompressPointer r1
    //     0xb4d288: add             x1, x1, HEAP, lsl #32
    // 0xb4d28c: tbnz            w1, #4, #0xb4d2cc
    // 0xb4d290: LoadField: r1 = r0->field_b
    //     0xb4d290: ldur            w1, [x0, #0xb]
    // 0xb4d294: DecompressPointer r1
    //     0xb4d294: add             x1, x1, HEAP, lsl #32
    // 0xb4d298: cmp             w1, NULL
    // 0xb4d29c: b.eq            #0xb4d2e4
    // 0xb4d2a0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb4d2a0: ldur            w2, [x0, #0x17]
    // 0xb4d2a4: DecompressPointer r2
    //     0xb4d2a4: add             x2, x2, HEAP, lsl #32
    // 0xb4d2a8: LoadField: r0 = r1->field_b
    //     0xb4d2a8: ldur            w0, [x1, #0xb]
    // 0xb4d2ac: DecompressPointer r0
    //     0xb4d2ac: add             x0, x0, HEAP, lsl #32
    // 0xb4d2b0: stp             x2, x0, [SP]
    // 0xb4d2b4: r4 = 0
    //     0xb4d2b4: movz            x4, #0
    // 0xb4d2b8: ldr             x0, [SP, #8]
    // 0xb4d2bc: r16 = UnlinkedCall_0x613b5c
    //     0xb4d2bc: add             x16, PP, #0x56, lsl #12  ; [pp+0x566e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4d2c0: add             x16, x16, #0x6e0
    // 0xb4d2c4: ldp             x5, lr, [x16]
    // 0xb4d2c8: blr             lr
    // 0xb4d2cc: r0 = Null
    //     0xb4d2cc: mov             x0, NULL
    // 0xb4d2d0: LeaveFrame
    //     0xb4d2d0: mov             SP, fp
    //     0xb4d2d4: ldp             fp, lr, [SP], #0x10
    // 0xb4d2d8: ret
    //     0xb4d2d8: ret             
    // 0xb4d2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4d2dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4d2e0: b               #0xb4d27c
    // 0xb4d2e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4d2e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<int>) {
    // ** addr: 0xb4d2e8, size: 0x4cc
    // 0xb4d2e8: EnterFrame
    //     0xb4d2e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d2ec: mov             fp, SP
    // 0xb4d2f0: AllocStack(0x40)
    //     0xb4d2f0: sub             SP, SP, #0x40
    // 0xb4d2f4: SetupParameters()
    //     0xb4d2f4: ldr             x0, [fp, #0x20]
    //     0xb4d2f8: ldur            w2, [x0, #0x17]
    //     0xb4d2fc: add             x2, x2, HEAP, lsl #32
    //     0xb4d300: stur            x2, [fp, #-8]
    // 0xb4d304: CheckStackOverflow
    //     0xb4d304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d308: cmp             SP, x16
    //     0xb4d30c: b.ls            #0xb4d7ac
    // 0xb4d310: ldr             x0, [fp, #0x10]
    // 0xb4d314: LoadField: r1 = r0->field_f
    //     0xb4d314: ldur            w1, [x0, #0xf]
    // 0xb4d318: DecompressPointer r1
    //     0xb4d318: add             x1, x1, HEAP, lsl #32
    // 0xb4d31c: stur            x1, [fp, #-0x30]
    // 0xb4d320: cmp             w1, NULL
    // 0xb4d324: b.ne            #0xb4d4e8
    // 0xb4d328: LoadField: r0 = r2->field_f
    //     0xb4d328: ldur            w0, [x2, #0xf]
    // 0xb4d32c: DecompressPointer r0
    //     0xb4d32c: add             x0, x0, HEAP, lsl #32
    // 0xb4d330: LoadField: r1 = r0->field_1b
    //     0xb4d330: ldur            w1, [x0, #0x1b]
    // 0xb4d334: DecompressPointer r1
    //     0xb4d334: add             x1, x1, HEAP, lsl #32
    // 0xb4d338: tbnz            w1, #4, #0xb4d354
    // 0xb4d33c: ldr             x1, [fp, #0x18]
    // 0xb4d340: r0 = of()
    //     0xb4d340: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4d344: LoadField: r1 = r0->field_5b
    //     0xb4d344: ldur            w1, [x0, #0x5b]
    // 0xb4d348: DecompressPointer r1
    //     0xb4d348: add             x1, x1, HEAP, lsl #32
    // 0xb4d34c: mov             x0, x1
    // 0xb4d350: b               #0xb4d37c
    // 0xb4d354: ldr             x1, [fp, #0x18]
    // 0xb4d358: r0 = of()
    //     0xb4d358: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4d35c: LoadField: r1 = r0->field_5b
    //     0xb4d35c: ldur            w1, [x0, #0x5b]
    // 0xb4d360: DecompressPointer r1
    //     0xb4d360: add             x1, x1, HEAP, lsl #32
    // 0xb4d364: r0 = LoadClassIdInstr(r1)
    //     0xb4d364: ldur            x0, [x1, #-1]
    //     0xb4d368: ubfx            x0, x0, #0xc, #0x14
    // 0xb4d36c: d0 = 0.400000
    //     0xb4d36c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4d370: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4d370: sub             lr, x0, #0xffa
    //     0xb4d374: ldr             lr, [x21, lr, lsl #3]
    //     0xb4d378: blr             lr
    // 0xb4d37c: r16 = <Color>
    //     0xb4d37c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb4d380: ldr             x16, [x16, #0xf80]
    // 0xb4d384: stp             x0, x16, [SP]
    // 0xb4d388: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4d388: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4d38c: r0 = all()
    //     0xb4d38c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4d390: stur            x0, [fp, #-0x10]
    // 0xb4d394: r16 = <RoundedRectangleBorder>
    //     0xb4d394: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb4d398: ldr             x16, [x16, #0xf78]
    // 0xb4d39c: r30 = Instance_RoundedRectangleBorder
    //     0xb4d39c: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb4d3a0: ldr             lr, [lr, #0x888]
    // 0xb4d3a4: stp             lr, x16, [SP]
    // 0xb4d3a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4d3a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4d3ac: r0 = all()
    //     0xb4d3ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4d3b0: stur            x0, [fp, #-0x18]
    // 0xb4d3b4: r0 = ButtonStyle()
    //     0xb4d3b4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb4d3b8: mov             x1, x0
    // 0xb4d3bc: ldur            x0, [fp, #-0x10]
    // 0xb4d3c0: stur            x1, [fp, #-0x20]
    // 0xb4d3c4: StoreField: r1->field_b = r0
    //     0xb4d3c4: stur            w0, [x1, #0xb]
    // 0xb4d3c8: ldur            x0, [fp, #-0x18]
    // 0xb4d3cc: StoreField: r1->field_43 = r0
    //     0xb4d3cc: stur            w0, [x1, #0x43]
    // 0xb4d3d0: r0 = TextButtonThemeData()
    //     0xb4d3d0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb4d3d4: mov             x2, x0
    // 0xb4d3d8: ldur            x0, [fp, #-0x20]
    // 0xb4d3dc: stur            x2, [fp, #-0x10]
    // 0xb4d3e0: StoreField: r2->field_7 = r0
    //     0xb4d3e0: stur            w0, [x2, #7]
    // 0xb4d3e4: ldr             x1, [fp, #0x18]
    // 0xb4d3e8: r0 = of()
    //     0xb4d3e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4d3ec: LoadField: r1 = r0->field_87
    //     0xb4d3ec: ldur            w1, [x0, #0x87]
    // 0xb4d3f0: DecompressPointer r1
    //     0xb4d3f0: add             x1, x1, HEAP, lsl #32
    // 0xb4d3f4: LoadField: r0 = r1->field_7
    //     0xb4d3f4: ldur            w0, [x1, #7]
    // 0xb4d3f8: DecompressPointer r0
    //     0xb4d3f8: add             x0, x0, HEAP, lsl #32
    // 0xb4d3fc: r16 = 16.000000
    //     0xb4d3fc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4d400: ldr             x16, [x16, #0x188]
    // 0xb4d404: r30 = Instance_Color
    //     0xb4d404: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4d408: stp             lr, x16, [SP]
    // 0xb4d40c: mov             x1, x0
    // 0xb4d410: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4d410: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4d414: ldr             x4, [x4, #0xaa0]
    // 0xb4d418: r0 = copyWith()
    //     0xb4d418: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4d41c: stur            x0, [fp, #-0x18]
    // 0xb4d420: r0 = Text()
    //     0xb4d420: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4d424: mov             x3, x0
    // 0xb4d428: r0 = "Resend OTP (30s)"
    //     0xb4d428: add             x0, PP, #0x56, lsl #12  ; [pp+0x566f0] "Resend OTP (30s)"
    //     0xb4d42c: ldr             x0, [x0, #0x6f0]
    // 0xb4d430: stur            x3, [fp, #-0x20]
    // 0xb4d434: StoreField: r3->field_b = r0
    //     0xb4d434: stur            w0, [x3, #0xb]
    // 0xb4d438: ldur            x0, [fp, #-0x18]
    // 0xb4d43c: StoreField: r3->field_13 = r0
    //     0xb4d43c: stur            w0, [x3, #0x13]
    // 0xb4d440: ldur            x2, [fp, #-8]
    // 0xb4d444: r1 = Function '<anonymous closure>':.
    //     0xb4d444: add             x1, PP, #0x56, lsl #12  ; [pp+0x566f8] AnonymousClosure: (0xb4d940), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4d448: ldr             x1, [x1, #0x6f8]
    // 0xb4d44c: r0 = AllocateClosure()
    //     0xb4d44c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4d450: stur            x0, [fp, #-0x18]
    // 0xb4d454: r0 = TextButton()
    //     0xb4d454: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb4d458: mov             x1, x0
    // 0xb4d45c: ldur            x0, [fp, #-0x18]
    // 0xb4d460: stur            x1, [fp, #-0x28]
    // 0xb4d464: StoreField: r1->field_b = r0
    //     0xb4d464: stur            w0, [x1, #0xb]
    // 0xb4d468: r2 = false
    //     0xb4d468: add             x2, NULL, #0x30  ; false
    // 0xb4d46c: StoreField: r1->field_27 = r2
    //     0xb4d46c: stur            w2, [x1, #0x27]
    // 0xb4d470: r3 = true
    //     0xb4d470: add             x3, NULL, #0x20  ; true
    // 0xb4d474: StoreField: r1->field_2f = r3
    //     0xb4d474: stur            w3, [x1, #0x2f]
    // 0xb4d478: ldur            x0, [fp, #-0x20]
    // 0xb4d47c: StoreField: r1->field_37 = r0
    //     0xb4d47c: stur            w0, [x1, #0x37]
    // 0xb4d480: r0 = TextButtonTheme()
    //     0xb4d480: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb4d484: mov             x1, x0
    // 0xb4d488: ldur            x0, [fp, #-0x10]
    // 0xb4d48c: stur            x1, [fp, #-0x18]
    // 0xb4d490: StoreField: r1->field_f = r0
    //     0xb4d490: stur            w0, [x1, #0xf]
    // 0xb4d494: ldur            x0, [fp, #-0x28]
    // 0xb4d498: StoreField: r1->field_b = r0
    //     0xb4d498: stur            w0, [x1, #0xb]
    // 0xb4d49c: r0 = SizedBox()
    //     0xb4d49c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb4d4a0: r4 = inf
    //     0xb4d4a0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb4d4a4: ldr             x4, [x4, #0x9f8]
    // 0xb4d4a8: stur            x0, [fp, #-0x10]
    // 0xb4d4ac: StoreField: r0->field_f = r4
    //     0xb4d4ac: stur            w4, [x0, #0xf]
    // 0xb4d4b0: r5 = 44.000000
    //     0xb4d4b0: add             x5, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb4d4b4: ldr             x5, [x5, #0xad8]
    // 0xb4d4b8: StoreField: r0->field_13 = r5
    //     0xb4d4b8: stur            w5, [x0, #0x13]
    // 0xb4d4bc: ldur            x1, [fp, #-0x18]
    // 0xb4d4c0: StoreField: r0->field_b = r1
    //     0xb4d4c0: stur            w1, [x0, #0xb]
    // 0xb4d4c4: r0 = Padding()
    //     0xb4d4c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4d4c8: r6 = Instance_EdgeInsets
    //     0xb4d4c8: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xb4d4cc: ldr             x6, [x6, #0xb0]
    // 0xb4d4d0: StoreField: r0->field_f = r6
    //     0xb4d4d0: stur            w6, [x0, #0xf]
    // 0xb4d4d4: ldur            x1, [fp, #-0x10]
    // 0xb4d4d8: StoreField: r0->field_b = r1
    //     0xb4d4d8: stur            w1, [x0, #0xb]
    // 0xb4d4dc: LeaveFrame
    //     0xb4d4dc: mov             SP, fp
    //     0xb4d4e0: ldp             fp, lr, [SP], #0x10
    // 0xb4d4e4: ret
    //     0xb4d4e4: ret             
    // 0xb4d4e8: r3 = true
    //     0xb4d4e8: add             x3, NULL, #0x20  ; true
    // 0xb4d4ec: r4 = inf
    //     0xb4d4ec: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb4d4f0: ldr             x4, [x4, #0x9f8]
    // 0xb4d4f4: r5 = 44.000000
    //     0xb4d4f4: add             x5, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb4d4f8: ldr             x5, [x5, #0xad8]
    // 0xb4d4fc: r6 = Instance_EdgeInsets
    //     0xb4d4fc: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xb4d500: ldr             x6, [x6, #0xb0]
    // 0xb4d504: r2 = false
    //     0xb4d504: add             x2, NULL, #0x30  ; false
    // 0xb4d508: r0 = 60
    //     0xb4d508: movz            x0, #0x3c
    // 0xb4d50c: branchIfSmi(r1, 0xb4d518)
    //     0xb4d50c: tbz             w1, #0, #0xb4d518
    // 0xb4d510: r0 = LoadClassIdInstr(r1)
    //     0xb4d510: ldur            x0, [x1, #-1]
    //     0xb4d514: ubfx            x0, x0, #0xc, #0x14
    // 0xb4d518: stp             xzr, x1, [SP]
    // 0xb4d51c: r0 = GDT[cid_x0 + -0xff5]()
    //     0xb4d51c: sub             lr, x0, #0xff5
    //     0xb4d520: ldr             lr, [x21, lr, lsl #3]
    //     0xb4d524: blr             lr
    // 0xb4d528: stur            x0, [fp, #-0x10]
    // 0xb4d52c: tbnz            w0, #4, #0xb4d548
    // 0xb4d530: ldur            x3, [fp, #-8]
    // 0xb4d534: r2 = true
    //     0xb4d534: add             x2, NULL, #0x20  ; true
    // 0xb4d538: LoadField: r1 = r3->field_f
    //     0xb4d538: ldur            w1, [x3, #0xf]
    // 0xb4d53c: DecompressPointer r1
    //     0xb4d53c: add             x1, x1, HEAP, lsl #32
    // 0xb4d540: StoreField: r1->field_13 = r2
    //     0xb4d540: stur            w2, [x1, #0x13]
    // 0xb4d544: b               #0xb4d550
    // 0xb4d548: ldur            x3, [fp, #-8]
    // 0xb4d54c: r2 = true
    //     0xb4d54c: add             x2, NULL, #0x20  ; true
    // 0xb4d550: tbnz            w0, #4, #0xb4d584
    // 0xb4d554: ldr             x1, [fp, #0x18]
    // 0xb4d558: r0 = of()
    //     0xb4d558: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4d55c: LoadField: r1 = r0->field_5b
    //     0xb4d55c: ldur            w1, [x0, #0x5b]
    // 0xb4d560: DecompressPointer r1
    //     0xb4d560: add             x1, x1, HEAP, lsl #32
    // 0xb4d564: r0 = LoadClassIdInstr(r1)
    //     0xb4d564: ldur            x0, [x1, #-1]
    //     0xb4d568: ubfx            x0, x0, #0xc, #0x14
    // 0xb4d56c: d0 = 0.030000
    //     0xb4d56c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb4d570: ldr             d0, [x17, #0x238]
    // 0xb4d574: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4d574: sub             lr, x0, #0xffa
    //     0xb4d578: ldr             lr, [x21, lr, lsl #3]
    //     0xb4d57c: blr             lr
    // 0xb4d580: b               #0xb4d5ac
    // 0xb4d584: ldr             x1, [fp, #0x18]
    // 0xb4d588: r0 = of()
    //     0xb4d588: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4d58c: LoadField: r1 = r0->field_5b
    //     0xb4d58c: ldur            w1, [x0, #0x5b]
    // 0xb4d590: DecompressPointer r1
    //     0xb4d590: add             x1, x1, HEAP, lsl #32
    // 0xb4d594: r0 = LoadClassIdInstr(r1)
    //     0xb4d594: ldur            x0, [x1, #-1]
    //     0xb4d598: ubfx            x0, x0, #0xc, #0x14
    // 0xb4d59c: d0 = 0.400000
    //     0xb4d59c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4d5a0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4d5a0: sub             lr, x0, #0xffa
    //     0xb4d5a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb4d5a8: blr             lr
    // 0xb4d5ac: ldur            x2, [fp, #-8]
    // 0xb4d5b0: r16 = <Color>
    //     0xb4d5b0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb4d5b4: ldr             x16, [x16, #0xf80]
    // 0xb4d5b8: stp             x0, x16, [SP]
    // 0xb4d5bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4d5bc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4d5c0: r0 = all()
    //     0xb4d5c0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4d5c4: stur            x0, [fp, #-0x18]
    // 0xb4d5c8: r16 = <RoundedRectangleBorder>
    //     0xb4d5c8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb4d5cc: ldr             x16, [x16, #0xf78]
    // 0xb4d5d0: r30 = Instance_RoundedRectangleBorder
    //     0xb4d5d0: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb4d5d4: ldr             lr, [lr, #0x888]
    // 0xb4d5d8: stp             lr, x16, [SP]
    // 0xb4d5dc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4d5dc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4d5e0: r0 = all()
    //     0xb4d5e0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4d5e4: stur            x0, [fp, #-0x20]
    // 0xb4d5e8: r0 = ButtonStyle()
    //     0xb4d5e8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb4d5ec: mov             x1, x0
    // 0xb4d5f0: ldur            x0, [fp, #-0x18]
    // 0xb4d5f4: stur            x1, [fp, #-0x28]
    // 0xb4d5f8: StoreField: r1->field_b = r0
    //     0xb4d5f8: stur            w0, [x1, #0xb]
    // 0xb4d5fc: ldur            x0, [fp, #-0x20]
    // 0xb4d600: StoreField: r1->field_43 = r0
    //     0xb4d600: stur            w0, [x1, #0x43]
    // 0xb4d604: r0 = TextButtonThemeData()
    //     0xb4d604: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb4d608: mov             x3, x0
    // 0xb4d60c: ldur            x0, [fp, #-0x28]
    // 0xb4d610: stur            x3, [fp, #-0x18]
    // 0xb4d614: StoreField: r3->field_7 = r0
    //     0xb4d614: stur            w0, [x3, #7]
    // 0xb4d618: ldur            x2, [fp, #-8]
    // 0xb4d61c: LoadField: r0 = r2->field_f
    //     0xb4d61c: ldur            w0, [x2, #0xf]
    // 0xb4d620: DecompressPointer r0
    //     0xb4d620: add             x0, x0, HEAP, lsl #32
    // 0xb4d624: LoadField: r1 = r0->field_13
    //     0xb4d624: ldur            w1, [x0, #0x13]
    // 0xb4d628: DecompressPointer r1
    //     0xb4d628: add             x1, x1, HEAP, lsl #32
    // 0xb4d62c: tbnz            w1, #4, #0xb4d64c
    // 0xb4d630: ldur            x0, [fp, #-0x10]
    // 0xb4d634: tbnz            w0, #4, #0xb4d64c
    // 0xb4d638: r1 = Function '<anonymous closure>':.
    //     0xb4d638: add             x1, PP, #0x56, lsl #12  ; [pp+0x56700] AnonymousClosure: (0xb4d7b4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xb4c494)
    //     0xb4d63c: ldr             x1, [x1, #0x700]
    // 0xb4d640: r0 = AllocateClosure()
    //     0xb4d640: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4d644: mov             x3, x0
    // 0xb4d648: b               #0xb4d650
    // 0xb4d64c: r3 = Null
    //     0xb4d64c: mov             x3, NULL
    // 0xb4d650: ldur            x0, [fp, #-0x10]
    // 0xb4d654: stur            x3, [fp, #-8]
    // 0xb4d658: r1 = Null
    //     0xb4d658: mov             x1, NULL
    // 0xb4d65c: r2 = 6
    //     0xb4d65c: movz            x2, #0x6
    // 0xb4d660: r0 = AllocateArray()
    //     0xb4d660: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4d664: r16 = "Resend OTP ("
    //     0xb4d664: add             x16, PP, #0x56, lsl #12  ; [pp+0x56708] "Resend OTP ("
    //     0xb4d668: ldr             x16, [x16, #0x708]
    // 0xb4d66c: StoreField: r0->field_f = r16
    //     0xb4d66c: stur            w16, [x0, #0xf]
    // 0xb4d670: ldur            x1, [fp, #-0x10]
    // 0xb4d674: tbnz            w1, #4, #0xb4d680
    // 0xb4d678: r2 = "0"
    //     0xb4d678: ldr             x2, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xb4d67c: b               #0xb4d684
    // 0xb4d680: ldur            x2, [fp, #-0x30]
    // 0xb4d684: StoreField: r0->field_13 = r2
    //     0xb4d684: stur            w2, [x0, #0x13]
    // 0xb4d688: r16 = "s)"
    //     0xb4d688: add             x16, PP, #0x56, lsl #12  ; [pp+0x56710] "s)"
    //     0xb4d68c: ldr             x16, [x16, #0x710]
    // 0xb4d690: ArrayStore: r0[0] = r16  ; List_4
    //     0xb4d690: stur            w16, [x0, #0x17]
    // 0xb4d694: str             x0, [SP]
    // 0xb4d698: r0 = _interpolate()
    //     0xb4d698: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4d69c: ldr             x1, [fp, #0x18]
    // 0xb4d6a0: stur            x0, [fp, #-0x20]
    // 0xb4d6a4: r0 = of()
    //     0xb4d6a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4d6a8: LoadField: r1 = r0->field_87
    //     0xb4d6a8: ldur            w1, [x0, #0x87]
    // 0xb4d6ac: DecompressPointer r1
    //     0xb4d6ac: add             x1, x1, HEAP, lsl #32
    // 0xb4d6b0: LoadField: r0 = r1->field_7
    //     0xb4d6b0: ldur            w0, [x1, #7]
    // 0xb4d6b4: DecompressPointer r0
    //     0xb4d6b4: add             x0, x0, HEAP, lsl #32
    // 0xb4d6b8: ldur            x1, [fp, #-0x10]
    // 0xb4d6bc: tbnz            w1, #4, #0xb4d6c8
    // 0xb4d6c0: r1 = Instance_Color
    //     0xb4d6c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4d6c4: b               #0xb4d6cc
    // 0xb4d6c8: r1 = Instance_Color
    //     0xb4d6c8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4d6cc: ldur            x4, [fp, #-0x18]
    // 0xb4d6d0: ldur            x3, [fp, #-8]
    // 0xb4d6d4: ldur            x2, [fp, #-0x20]
    // 0xb4d6d8: r16 = 16.000000
    //     0xb4d6d8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4d6dc: ldr             x16, [x16, #0x188]
    // 0xb4d6e0: stp             x1, x16, [SP]
    // 0xb4d6e4: mov             x1, x0
    // 0xb4d6e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4d6e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4d6ec: ldr             x4, [x4, #0xaa0]
    // 0xb4d6f0: r0 = copyWith()
    //     0xb4d6f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4d6f4: stur            x0, [fp, #-0x10]
    // 0xb4d6f8: r0 = Text()
    //     0xb4d6f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4d6fc: mov             x1, x0
    // 0xb4d700: ldur            x0, [fp, #-0x20]
    // 0xb4d704: stur            x1, [fp, #-0x28]
    // 0xb4d708: StoreField: r1->field_b = r0
    //     0xb4d708: stur            w0, [x1, #0xb]
    // 0xb4d70c: ldur            x0, [fp, #-0x10]
    // 0xb4d710: StoreField: r1->field_13 = r0
    //     0xb4d710: stur            w0, [x1, #0x13]
    // 0xb4d714: r0 = TextButton()
    //     0xb4d714: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb4d718: mov             x1, x0
    // 0xb4d71c: ldur            x0, [fp, #-8]
    // 0xb4d720: stur            x1, [fp, #-0x10]
    // 0xb4d724: StoreField: r1->field_b = r0
    //     0xb4d724: stur            w0, [x1, #0xb]
    // 0xb4d728: r0 = false
    //     0xb4d728: add             x0, NULL, #0x30  ; false
    // 0xb4d72c: StoreField: r1->field_27 = r0
    //     0xb4d72c: stur            w0, [x1, #0x27]
    // 0xb4d730: r0 = true
    //     0xb4d730: add             x0, NULL, #0x20  ; true
    // 0xb4d734: StoreField: r1->field_2f = r0
    //     0xb4d734: stur            w0, [x1, #0x2f]
    // 0xb4d738: ldur            x0, [fp, #-0x28]
    // 0xb4d73c: StoreField: r1->field_37 = r0
    //     0xb4d73c: stur            w0, [x1, #0x37]
    // 0xb4d740: r0 = TextButtonTheme()
    //     0xb4d740: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb4d744: mov             x1, x0
    // 0xb4d748: ldur            x0, [fp, #-0x18]
    // 0xb4d74c: stur            x1, [fp, #-8]
    // 0xb4d750: StoreField: r1->field_f = r0
    //     0xb4d750: stur            w0, [x1, #0xf]
    // 0xb4d754: ldur            x0, [fp, #-0x10]
    // 0xb4d758: StoreField: r1->field_b = r0
    //     0xb4d758: stur            w0, [x1, #0xb]
    // 0xb4d75c: r0 = SizedBox()
    //     0xb4d75c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb4d760: mov             x1, x0
    // 0xb4d764: r0 = inf
    //     0xb4d764: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb4d768: ldr             x0, [x0, #0x9f8]
    // 0xb4d76c: stur            x1, [fp, #-0x10]
    // 0xb4d770: StoreField: r1->field_f = r0
    //     0xb4d770: stur            w0, [x1, #0xf]
    // 0xb4d774: r0 = 44.000000
    //     0xb4d774: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb4d778: ldr             x0, [x0, #0xad8]
    // 0xb4d77c: StoreField: r1->field_13 = r0
    //     0xb4d77c: stur            w0, [x1, #0x13]
    // 0xb4d780: ldur            x0, [fp, #-8]
    // 0xb4d784: StoreField: r1->field_b = r0
    //     0xb4d784: stur            w0, [x1, #0xb]
    // 0xb4d788: r0 = Padding()
    //     0xb4d788: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4d78c: r1 = Instance_EdgeInsets
    //     0xb4d78c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xb4d790: ldr             x1, [x1, #0xb0]
    // 0xb4d794: StoreField: r0->field_f = r1
    //     0xb4d794: stur            w1, [x0, #0xf]
    // 0xb4d798: ldur            x1, [fp, #-0x10]
    // 0xb4d79c: StoreField: r0->field_b = r1
    //     0xb4d79c: stur            w1, [x0, #0xb]
    // 0xb4d7a0: LeaveFrame
    //     0xb4d7a0: mov             SP, fp
    //     0xb4d7a4: ldp             fp, lr, [SP], #0x10
    // 0xb4d7a8: ret
    //     0xb4d7a8: ret             
    // 0xb4d7ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4d7ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4d7b0: b               #0xb4d310
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4d7b4, size: 0x48
    // 0xb4d7b4: EnterFrame
    //     0xb4d7b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d7b8: mov             fp, SP
    // 0xb4d7bc: ldr             x0, [fp, #0x10]
    // 0xb4d7c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb4d7c0: ldur            w1, [x0, #0x17]
    // 0xb4d7c4: DecompressPointer r1
    //     0xb4d7c4: add             x1, x1, HEAP, lsl #32
    // 0xb4d7c8: CheckStackOverflow
    //     0xb4d7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d7cc: cmp             SP, x16
    //     0xb4d7d0: b.ls            #0xb4d7f4
    // 0xb4d7d4: LoadField: r0 = r1->field_f
    //     0xb4d7d4: ldur            w0, [x1, #0xf]
    // 0xb4d7d8: DecompressPointer r0
    //     0xb4d7d8: add             x0, x0, HEAP, lsl #32
    // 0xb4d7dc: mov             x1, x0
    // 0xb4d7e0: r0 = _resendCode()
    //     0xb4d7e0: bl              #0xb4d7fc  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::_resendCode
    // 0xb4d7e4: r0 = Null
    //     0xb4d7e4: mov             x0, NULL
    // 0xb4d7e8: LeaveFrame
    //     0xb4d7e8: mov             SP, fp
    //     0xb4d7ec: ldp             fp, lr, [SP], #0x10
    // 0xb4d7f0: ret
    //     0xb4d7f0: ret             
    // 0xb4d7f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4d7f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4d7f8: b               #0xb4d7d4
  }
  _ _resendCode(/* No info */) {
    // ** addr: 0xb4d7fc, size: 0x64
    // 0xb4d7fc: EnterFrame
    //     0xb4d7fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d800: mov             fp, SP
    // 0xb4d804: AllocStack(0x8)
    //     0xb4d804: sub             SP, SP, #8
    // 0xb4d808: SetupParameters(_OtpBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xb4d808: stur            x1, [fp, #-8]
    // 0xb4d80c: CheckStackOverflow
    //     0xb4d80c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d810: cmp             SP, x16
    //     0xb4d814: b.ls            #0xb4d858
    // 0xb4d818: r1 = 1
    //     0xb4d818: movz            x1, #0x1
    // 0xb4d81c: r0 = AllocateContext()
    //     0xb4d81c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4d820: mov             x1, x0
    // 0xb4d824: ldur            x0, [fp, #-8]
    // 0xb4d828: StoreField: r1->field_f = r0
    //     0xb4d828: stur            w0, [x1, #0xf]
    // 0xb4d82c: mov             x2, x1
    // 0xb4d830: r1 = Function '<anonymous closure>':.
    //     0xb4d830: add             x1, PP, #0x56, lsl #12  ; [pp+0x56718] AnonymousClosure: (0xb4d860), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::_resendCode (0xb4d7fc)
    //     0xb4d834: ldr             x1, [x1, #0x718]
    // 0xb4d838: r0 = AllocateClosure()
    //     0xb4d838: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4d83c: ldur            x1, [fp, #-8]
    // 0xb4d840: mov             x2, x0
    // 0xb4d844: r0 = setState()
    //     0xb4d844: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb4d848: r0 = Null
    //     0xb4d848: mov             x0, NULL
    // 0xb4d84c: LeaveFrame
    //     0xb4d84c: mov             SP, fp
    //     0xb4d850: ldp             fp, lr, [SP], #0x10
    // 0xb4d854: ret
    //     0xb4d854: ret             
    // 0xb4d858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4d858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4d85c: b               #0xb4d818
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4d860, size: 0xe0
    // 0xb4d860: EnterFrame
    //     0xb4d860: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d864: mov             fp, SP
    // 0xb4d868: AllocStack(0x18)
    //     0xb4d868: sub             SP, SP, #0x18
    // 0xb4d86c: SetupParameters()
    //     0xb4d86c: add             x0, NULL, #0x30  ; false
    //     0xb4d870: ldr             x1, [fp, #0x10]
    //     0xb4d874: ldur            w2, [x1, #0x17]
    //     0xb4d878: add             x2, x2, HEAP, lsl #32
    //     0xb4d87c: stur            x2, [fp, #-8]
    // 0xb4d86c: r0 = false
    // 0xb4d880: CheckStackOverflow
    //     0xb4d880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d884: cmp             SP, x16
    //     0xb4d888: b.ls            #0xb4d934
    // 0xb4d88c: LoadField: r1 = r2->field_f
    //     0xb4d88c: ldur            w1, [x2, #0xf]
    // 0xb4d890: DecompressPointer r1
    //     0xb4d890: add             x1, x1, HEAP, lsl #32
    // 0xb4d894: StoreField: r1->field_13 = r0
    //     0xb4d894: stur            w0, [x1, #0x13]
    // 0xb4d898: LoadField: r0 = r1->field_b
    //     0xb4d898: ldur            w0, [x1, #0xb]
    // 0xb4d89c: DecompressPointer r0
    //     0xb4d89c: add             x0, x0, HEAP, lsl #32
    // 0xb4d8a0: cmp             w0, NULL
    // 0xb4d8a4: b.eq            #0xb4d93c
    // 0xb4d8a8: LoadField: r1 = r0->field_f
    //     0xb4d8a8: ldur            w1, [x0, #0xf]
    // 0xb4d8ac: DecompressPointer r1
    //     0xb4d8ac: add             x1, x1, HEAP, lsl #32
    // 0xb4d8b0: str             x1, [SP]
    // 0xb4d8b4: r4 = 0
    //     0xb4d8b4: movz            x4, #0
    // 0xb4d8b8: ldr             x0, [SP]
    // 0xb4d8bc: r16 = UnlinkedCall_0x613b5c
    //     0xb4d8bc: add             x16, PP, #0x56, lsl #12  ; [pp+0x56720] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4d8c0: add             x16, x16, #0x720
    // 0xb4d8c4: ldp             x5, lr, [x16]
    // 0xb4d8c8: blr             lr
    // 0xb4d8cc: ldur            x0, [fp, #-8]
    // 0xb4d8d0: LoadField: r3 = r0->field_f
    //     0xb4d8d0: ldur            w3, [x0, #0xf]
    // 0xb4d8d4: DecompressPointer r3
    //     0xb4d8d4: add             x3, x3, HEAP, lsl #32
    // 0xb4d8d8: stur            x3, [fp, #-0x10]
    // 0xb4d8dc: r1 = Function '<anonymous closure>':.
    //     0xb4d8dc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56730] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xb4d8e0: ldr             x1, [x1, #0x730]
    // 0xb4d8e4: r2 = Null
    //     0xb4d8e4: mov             x2, NULL
    // 0xb4d8e8: r0 = AllocateClosure()
    //     0xb4d8e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4d8ec: mov             x2, x0
    // 0xb4d8f0: r1 = <int>
    //     0xb4d8f0: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb4d8f4: r0 = Stream.periodic()
    //     0xb4d8f4: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xb4d8f8: mov             x1, x0
    // 0xb4d8fc: r2 = 30
    //     0xb4d8fc: movz            x2, #0x1e
    // 0xb4d900: r0 = take()
    //     0xb4d900: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xb4d904: ldur            x1, [fp, #-0x10]
    // 0xb4d908: StoreField: r1->field_1f = r0
    //     0xb4d908: stur            w0, [x1, #0x1f]
    //     0xb4d90c: ldurb           w16, [x1, #-1]
    //     0xb4d910: ldurb           w17, [x0, #-1]
    //     0xb4d914: and             x16, x17, x16, lsr #2
    //     0xb4d918: tst             x16, HEAP, lsr #32
    //     0xb4d91c: b.eq            #0xb4d924
    //     0xb4d920: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb4d924: r0 = Null
    //     0xb4d924: mov             x0, NULL
    // 0xb4d928: LeaveFrame
    //     0xb4d928: mov             SP, fp
    //     0xb4d92c: ldp             fp, lr, [SP], #0x10
    // 0xb4d930: ret
    //     0xb4d930: ret             
    // 0xb4d934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4d934: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4d938: b               #0xb4d88c
    // 0xb4d93c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4d93c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4d940, size: 0x90
    // 0xb4d940: EnterFrame
    //     0xb4d940: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d944: mov             fp, SP
    // 0xb4d948: AllocStack(0x10)
    //     0xb4d948: sub             SP, SP, #0x10
    // 0xb4d94c: SetupParameters()
    //     0xb4d94c: ldr             x0, [fp, #0x10]
    //     0xb4d950: ldur            w1, [x0, #0x17]
    //     0xb4d954: add             x1, x1, HEAP, lsl #32
    // 0xb4d958: CheckStackOverflow
    //     0xb4d958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d95c: cmp             SP, x16
    //     0xb4d960: b.ls            #0xb4d9c4
    // 0xb4d964: LoadField: r0 = r1->field_f
    //     0xb4d964: ldur            w0, [x1, #0xf]
    // 0xb4d968: DecompressPointer r0
    //     0xb4d968: add             x0, x0, HEAP, lsl #32
    // 0xb4d96c: LoadField: r1 = r0->field_1b
    //     0xb4d96c: ldur            w1, [x0, #0x1b]
    // 0xb4d970: DecompressPointer r1
    //     0xb4d970: add             x1, x1, HEAP, lsl #32
    // 0xb4d974: tbnz            w1, #4, #0xb4d9b4
    // 0xb4d978: LoadField: r1 = r0->field_b
    //     0xb4d978: ldur            w1, [x0, #0xb]
    // 0xb4d97c: DecompressPointer r1
    //     0xb4d97c: add             x1, x1, HEAP, lsl #32
    // 0xb4d980: cmp             w1, NULL
    // 0xb4d984: b.eq            #0xb4d9cc
    // 0xb4d988: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb4d988: ldur            w2, [x0, #0x17]
    // 0xb4d98c: DecompressPointer r2
    //     0xb4d98c: add             x2, x2, HEAP, lsl #32
    // 0xb4d990: LoadField: r0 = r1->field_b
    //     0xb4d990: ldur            w0, [x1, #0xb]
    // 0xb4d994: DecompressPointer r0
    //     0xb4d994: add             x0, x0, HEAP, lsl #32
    // 0xb4d998: stp             x2, x0, [SP]
    // 0xb4d99c: r4 = 0
    //     0xb4d99c: movz            x4, #0
    // 0xb4d9a0: ldr             x0, [SP, #8]
    // 0xb4d9a4: r16 = UnlinkedCall_0x613b5c
    //     0xb4d9a4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56738] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4d9a8: add             x16, x16, #0x738
    // 0xb4d9ac: ldp             x5, lr, [x16]
    // 0xb4d9b0: blr             lr
    // 0xb4d9b4: r0 = Null
    //     0xb4d9b4: mov             x0, NULL
    // 0xb4d9b8: LeaveFrame
    //     0xb4d9b8: mov             SP, fp
    //     0xb4d9bc: ldp             fp, lr, [SP], #0x10
    // 0xb4d9c0: ret
    //     0xb4d9c0: ret             
    // 0xb4d9c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4d9c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4d9c8: b               #0xb4d964
    // 0xb4d9cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4d9cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb4d9d0, size: 0x120
    // 0xb4d9d0: EnterFrame
    //     0xb4d9d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb4d9d4: mov             fp, SP
    // 0xb4d9d8: AllocStack(0x28)
    //     0xb4d9d8: sub             SP, SP, #0x28
    // 0xb4d9dc: SetupParameters()
    //     0xb4d9dc: ldr             x0, [fp, #0x18]
    //     0xb4d9e0: ldur            w2, [x0, #0x17]
    //     0xb4d9e4: add             x2, x2, HEAP, lsl #32
    //     0xb4d9e8: stur            x2, [fp, #-8]
    // 0xb4d9ec: CheckStackOverflow
    //     0xb4d9ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4d9f0: cmp             SP, x16
    //     0xb4d9f4: b.ls            #0xb4dae4
    // 0xb4d9f8: LoadField: r1 = r2->field_f
    //     0xb4d9f8: ldur            w1, [x2, #0xf]
    // 0xb4d9fc: DecompressPointer r1
    //     0xb4d9fc: add             x1, x1, HEAP, lsl #32
    // 0xb4da00: ldr             x3, [fp, #0x10]
    // 0xb4da04: cmp             w3, NULL
    // 0xb4da08: b.ne            #0xb4da14
    // 0xb4da0c: r0 = ""
    //     0xb4da0c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4da10: b               #0xb4da18
    // 0xb4da14: mov             x0, x3
    // 0xb4da18: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4da18: stur            w0, [x1, #0x17]
    //     0xb4da1c: ldurb           w16, [x1, #-1]
    //     0xb4da20: ldurb           w17, [x0, #-1]
    //     0xb4da24: and             x16, x17, x16, lsr #2
    //     0xb4da28: tst             x16, HEAP, lsr #32
    //     0xb4da2c: b.eq            #0xb4da34
    //     0xb4da30: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb4da34: r0 = validateAddress()
    //     0xb4da34: bl              #0xa28784  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::validateAddress
    // 0xb4da38: ldr             x0, [fp, #0x10]
    // 0xb4da3c: cmp             w0, NULL
    // 0xb4da40: b.eq            #0xb4dad4
    // 0xb4da44: LoadField: r1 = r0->field_7
    //     0xb4da44: ldur            w1, [x0, #7]
    // 0xb4da48: cmp             w1, #8
    // 0xb4da4c: b.ne            #0xb4dad4
    // 0xb4da50: ldur            x0, [fp, #-8]
    // 0xb4da54: LoadField: r1 = r0->field_13
    //     0xb4da54: ldur            w1, [x0, #0x13]
    // 0xb4da58: DecompressPointer r1
    //     0xb4da58: add             x1, x1, HEAP, lsl #32
    // 0xb4da5c: r0 = of()
    //     0xb4da5c: bl              #0x81ef68  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0xb4da60: stur            x0, [fp, #-0x10]
    // 0xb4da64: r0 = FocusNode()
    //     0xb4da64: bl              #0x8182fc  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xb4da68: mov             x1, x0
    // 0xb4da6c: stur            x0, [fp, #-0x18]
    // 0xb4da70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb4da70: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb4da74: r0 = FocusNode()
    //     0xb4da74: bl              #0x695c10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xb4da78: ldur            x16, [fp, #-0x18]
    // 0xb4da7c: str             x16, [SP]
    // 0xb4da80: ldur            x1, [fp, #-0x10]
    // 0xb4da84: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb4da84: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb4da88: r0 = requestFocus()
    //     0xb4da88: bl              #0x6595f4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xb4da8c: ldur            x0, [fp, #-8]
    // 0xb4da90: LoadField: r1 = r0->field_f
    //     0xb4da90: ldur            w1, [x0, #0xf]
    // 0xb4da94: DecompressPointer r1
    //     0xb4da94: add             x1, x1, HEAP, lsl #32
    // 0xb4da98: LoadField: r0 = r1->field_b
    //     0xb4da98: ldur            w0, [x1, #0xb]
    // 0xb4da9c: DecompressPointer r0
    //     0xb4da9c: add             x0, x0, HEAP, lsl #32
    // 0xb4daa0: cmp             w0, NULL
    // 0xb4daa4: b.eq            #0xb4daec
    // 0xb4daa8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb4daa8: ldur            w2, [x1, #0x17]
    // 0xb4daac: DecompressPointer r2
    //     0xb4daac: add             x2, x2, HEAP, lsl #32
    // 0xb4dab0: LoadField: r1 = r0->field_b
    //     0xb4dab0: ldur            w1, [x0, #0xb]
    // 0xb4dab4: DecompressPointer r1
    //     0xb4dab4: add             x1, x1, HEAP, lsl #32
    // 0xb4dab8: stp             x2, x1, [SP]
    // 0xb4dabc: r4 = 0
    //     0xb4dabc: movz            x4, #0
    // 0xb4dac0: ldr             x0, [SP, #8]
    // 0xb4dac4: r16 = UnlinkedCall_0x613b5c
    //     0xb4dac4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56748] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4dac8: add             x16, x16, #0x748
    // 0xb4dacc: ldp             x5, lr, [x16]
    // 0xb4dad0: blr             lr
    // 0xb4dad4: r0 = Null
    //     0xb4dad4: mov             x0, NULL
    // 0xb4dad8: LeaveFrame
    //     0xb4dad8: mov             SP, fp
    //     0xb4dadc: ldp             fp, lr, [SP], #0x10
    // 0xb4dae0: ret
    //     0xb4dae0: ret             
    // 0xb4dae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4dae4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4dae8: b               #0xb4d9f8
    // 0xb4daec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4daec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0xb4daf0, size: 0x68
    // 0xb4daf0: EnterFrame
    //     0xb4daf0: stp             fp, lr, [SP, #-0x10]!
    //     0xb4daf4: mov             fp, SP
    // 0xb4daf8: ldr             x0, [fp, #0x18]
    // 0xb4dafc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb4dafc: ldur            w1, [x0, #0x17]
    // 0xb4db00: DecompressPointer r1
    //     0xb4db00: add             x1, x1, HEAP, lsl #32
    // 0xb4db04: CheckStackOverflow
    //     0xb4db04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4db08: cmp             SP, x16
    //     0xb4db0c: b.ls            #0xb4db50
    // 0xb4db10: LoadField: r2 = r1->field_f
    //     0xb4db10: ldur            w2, [x1, #0xf]
    // 0xb4db14: DecompressPointer r2
    //     0xb4db14: add             x2, x2, HEAP, lsl #32
    // 0xb4db18: ldr             x0, [fp, #0x10]
    // 0xb4db1c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4db1c: stur            w0, [x2, #0x17]
    //     0xb4db20: ldurb           w16, [x2, #-1]
    //     0xb4db24: ldurb           w17, [x0, #-1]
    //     0xb4db28: and             x16, x17, x16, lsr #2
    //     0xb4db2c: tst             x16, HEAP, lsr #32
    //     0xb4db30: b.eq            #0xb4db38
    //     0xb4db34: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb4db38: mov             x1, x2
    // 0xb4db3c: r0 = validateAddress()
    //     0xb4db3c: bl              #0xa28784  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::validateAddress
    // 0xb4db40: r0 = Null
    //     0xb4db40: mov             x0, NULL
    // 0xb4db44: LeaveFrame
    //     0xb4db44: mov             SP, fp
    //     0xb4db48: ldp             fp, lr, [SP], #0x10
    // 0xb4db4c: ret
    //     0xb4db4c: ret             
    // 0xb4db50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4db50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4db54: b               #0xb4db10
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4db58, size: 0x7c
    // 0xb4db58: EnterFrame
    //     0xb4db58: stp             fp, lr, [SP, #-0x10]!
    //     0xb4db5c: mov             fp, SP
    // 0xb4db60: AllocStack(0x8)
    //     0xb4db60: sub             SP, SP, #8
    // 0xb4db64: SetupParameters()
    //     0xb4db64: ldr             x0, [fp, #0x10]
    //     0xb4db68: ldur            w1, [x0, #0x17]
    //     0xb4db6c: add             x1, x1, HEAP, lsl #32
    // 0xb4db70: CheckStackOverflow
    //     0xb4db70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4db74: cmp             SP, x16
    //     0xb4db78: b.ls            #0xb4dbc8
    // 0xb4db7c: LoadField: r0 = r1->field_f
    //     0xb4db7c: ldur            w0, [x1, #0xf]
    // 0xb4db80: DecompressPointer r0
    //     0xb4db80: add             x0, x0, HEAP, lsl #32
    // 0xb4db84: LoadField: r1 = r0->field_b
    //     0xb4db84: ldur            w1, [x0, #0xb]
    // 0xb4db88: DecompressPointer r1
    //     0xb4db88: add             x1, x1, HEAP, lsl #32
    // 0xb4db8c: cmp             w1, NULL
    // 0xb4db90: b.eq            #0xb4dbd0
    // 0xb4db94: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb4db94: ldur            w0, [x1, #0x17]
    // 0xb4db98: DecompressPointer r0
    //     0xb4db98: add             x0, x0, HEAP, lsl #32
    // 0xb4db9c: str             x0, [SP]
    // 0xb4dba0: r4 = 0
    //     0xb4dba0: movz            x4, #0
    // 0xb4dba4: ldr             x0, [SP]
    // 0xb4dba8: r16 = UnlinkedCall_0x613b5c
    //     0xb4dba8: add             x16, PP, #0x56, lsl #12  ; [pp+0x56760] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4dbac: add             x16, x16, #0x760
    // 0xb4dbb0: ldp             x5, lr, [x16]
    // 0xb4dbb4: blr             lr
    // 0xb4dbb8: r0 = Null
    //     0xb4dbb8: mov             x0, NULL
    // 0xb4dbbc: LeaveFrame
    //     0xb4dbbc: mov             SP, fp
    //     0xb4dbc0: ldp             fp, lr, [SP], #0x10
    // 0xb4dbc4: ret
    //     0xb4dbc4: ret             
    // 0xb4dbc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4dbc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4dbcc: b               #0xb4db7c
    // 0xb4dbd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4dbd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _OtpBottomSheetState(/* No info */) {
    // ** addr: 0xc7ee50, size: 0x9c
    // 0xc7ee50: EnterFrame
    //     0xc7ee50: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ee54: mov             fp, SP
    // 0xc7ee58: AllocStack(0x8)
    //     0xc7ee58: sub             SP, SP, #8
    // 0xc7ee5c: r2 = false
    //     0xc7ee5c: add             x2, NULL, #0x30  ; false
    // 0xc7ee60: r0 = ""
    //     0xc7ee60: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc7ee64: mov             x3, x1
    // 0xc7ee68: stur            x1, [fp, #-8]
    // 0xc7ee6c: CheckStackOverflow
    //     0xc7ee6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7ee70: cmp             SP, x16
    //     0xc7ee74: b.ls            #0xc7eee4
    // 0xc7ee78: StoreField: r3->field_13 = r2
    //     0xc7ee78: stur            w2, [x3, #0x13]
    // 0xc7ee7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xc7ee7c: stur            w0, [x3, #0x17]
    // 0xc7ee80: StoreField: r3->field_1b = r2
    //     0xc7ee80: stur            w2, [x3, #0x1b]
    // 0xc7ee84: r1 = Function '<anonymous closure>':.
    //     0xc7ee84: add             x1, PP, #0x48, lsl #12  ; [pp+0x489f8] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xc7ee88: ldr             x1, [x1, #0x9f8]
    // 0xc7ee8c: r2 = Null
    //     0xc7ee8c: mov             x2, NULL
    // 0xc7ee90: r0 = AllocateClosure()
    //     0xc7ee90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc7ee94: mov             x2, x0
    // 0xc7ee98: r1 = <int>
    //     0xc7ee98: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc7ee9c: r0 = Stream.periodic()
    //     0xc7ee9c: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xc7eea0: mov             x1, x0
    // 0xc7eea4: r2 = 30
    //     0xc7eea4: movz            x2, #0x1e
    // 0xc7eea8: r0 = take()
    //     0xc7eea8: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xc7eeac: ldur            x1, [fp, #-8]
    // 0xc7eeb0: StoreField: r1->field_1f = r0
    //     0xc7eeb0: stur            w0, [x1, #0x1f]
    //     0xc7eeb4: ldurb           w16, [x1, #-1]
    //     0xc7eeb8: ldurb           w17, [x0, #-1]
    //     0xc7eebc: and             x16, x17, x16, lsr #2
    //     0xc7eec0: tst             x16, HEAP, lsr #32
    //     0xc7eec4: b.eq            #0xc7eecc
    //     0xc7eec8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7eecc: r1 = Null
    //     0xc7eecc: mov             x1, NULL
    // 0xc7eed0: r0 = SmsAutoFill()
    //     0xc7eed0: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0xc7eed4: r0 = Null
    //     0xc7eed4: mov             x0, NULL
    // 0xc7eed8: LeaveFrame
    //     0xc7eed8: mov             SP, fp
    //     0xc7eedc: ldp             fp, lr, [SP], #0x10
    // 0xc7eee0: ret
    //     0xc7eee0: ret             
    // 0xc7eee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7eee4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7eee8: b               #0xc7ee78
  }
}

// class id: 4095, size: 0x1c, field offset: 0xc
//   const constructor, 
class OtpBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ee08, size: 0x48
    // 0xc7ee08: EnterFrame
    //     0xc7ee08: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ee0c: mov             fp, SP
    // 0xc7ee10: AllocStack(0x8)
    //     0xc7ee10: sub             SP, SP, #8
    // 0xc7ee14: CheckStackOverflow
    //     0xc7ee14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7ee18: cmp             SP, x16
    //     0xc7ee1c: b.ls            #0xc7ee48
    // 0xc7ee20: r1 = <OtpBottomSheet>
    //     0xc7ee20: add             x1, PP, #0x48, lsl #12  ; [pp+0x489f0] TypeArguments: <OtpBottomSheet>
    //     0xc7ee24: ldr             x1, [x1, #0x9f0]
    // 0xc7ee28: r0 = _OtpBottomSheetState()
    //     0xc7ee28: bl              #0xc7eeec  ; Allocate_OtpBottomSheetStateStub -> _OtpBottomSheetState (size=0x24)
    // 0xc7ee2c: mov             x1, x0
    // 0xc7ee30: stur            x0, [fp, #-8]
    // 0xc7ee34: r0 = _OtpBottomSheetState()
    //     0xc7ee34: bl              #0xc7ee50  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::_OtpBottomSheetState
    // 0xc7ee38: ldur            x0, [fp, #-8]
    // 0xc7ee3c: LeaveFrame
    //     0xc7ee3c: mov             SP, fp
    //     0xc7ee40: ldp             fp, lr, [SP], #0x10
    // 0xc7ee44: ret
    //     0xc7ee44: ret             
    // 0xc7ee48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7ee48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7ee4c: b               #0xc7ee20
  }
}
