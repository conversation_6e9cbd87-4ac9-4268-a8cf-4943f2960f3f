// lib: , url: package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart

// class id: 1049381, size: 0x8
class :: {
}

// class id: 3350, size: 0x34, field offset: 0x14
class _CollectionFilterState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x941750, size: 0x610
    // 0x941750: EnterFrame
    //     0x941750: stp             fp, lr, [SP, #-0x10]!
    //     0x941754: mov             fp, SP
    // 0x941758: AllocStack(0x50)
    //     0x941758: sub             SP, SP, #0x50
    // 0x94175c: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */)
    //     0x94175c: mov             x0, x1
    //     0x941760: stur            x1, [fp, #-8]
    // 0x941764: CheckStackOverflow
    //     0x941764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941768: cmp             SP, x16
    //     0x94176c: b.ls            #0x941d30
    // 0x941770: LoadField: r1 = r0->field_b
    //     0x941770: ldur            w1, [x0, #0xb]
    // 0x941774: DecompressPointer r1
    //     0x941774: add             x1, x1, HEAP, lsl #32
    // 0x941778: cmp             w1, NULL
    // 0x94177c: b.eq            #0x941d38
    // 0x941780: LoadField: r2 = r1->field_f
    //     0x941780: ldur            w2, [x1, #0xf]
    // 0x941784: DecompressPointer r2
    //     0x941784: add             x2, x2, HEAP, lsl #32
    // 0x941788: LoadField: r1 = r2->field_7
    //     0x941788: ldur            w1, [x2, #7]
    // 0x94178c: DecompressPointer r1
    //     0x94178c: add             x1, x1, HEAP, lsl #32
    // 0x941790: cmp             w1, NULL
    // 0x941794: b.eq            #0x941864
    // 0x941798: LoadField: r2 = r0->field_13
    //     0x941798: ldur            w2, [x0, #0x13]
    // 0x94179c: DecompressPointer r2
    //     0x94179c: add             x2, x2, HEAP, lsl #32
    // 0x9417a0: mov             x16, x1
    // 0x9417a4: mov             x1, x2
    // 0x9417a8: mov             x2, x16
    // 0x9417ac: r0 = addAll()
    //     0x9417ac: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x9417b0: ldur            x0, [fp, #-8]
    // 0x9417b4: LoadField: r1 = r0->field_1b
    //     0x9417b4: ldur            w1, [x0, #0x1b]
    // 0x9417b8: DecompressPointer r1
    //     0x9417b8: add             x1, x1, HEAP, lsl #32
    // 0x9417bc: stur            x1, [fp, #-0x10]
    // 0x9417c0: LoadField: r2 = r0->field_b
    //     0x9417c0: ldur            w2, [x0, #0xb]
    // 0x9417c4: DecompressPointer r2
    //     0x9417c4: add             x2, x2, HEAP, lsl #32
    // 0x9417c8: cmp             w2, NULL
    // 0x9417cc: b.eq            #0x941d3c
    // 0x9417d0: LoadField: r3 = r2->field_f
    //     0x9417d0: ldur            w3, [x2, #0xf]
    // 0x9417d4: DecompressPointer r3
    //     0x9417d4: add             x3, x3, HEAP, lsl #32
    // 0x9417d8: LoadField: r2 = r3->field_7
    //     0x9417d8: ldur            w2, [x3, #7]
    // 0x9417dc: DecompressPointer r2
    //     0x9417dc: add             x2, x2, HEAP, lsl #32
    // 0x9417e0: cmp             w2, NULL
    // 0x9417e4: b.ne            #0x941824
    // 0x9417e8: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x9417e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9417ec: ldr             x0, [x0]
    //     0x9417f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9417f4: cmp             w0, w16
    //     0x9417f8: b.ne            #0x941804
    //     0x9417fc: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x941800: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x941804: r1 = <String>
    //     0x941804: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x941808: stur            x0, [fp, #-0x18]
    // 0x94180c: r0 = AllocateGrowableArray()
    //     0x94180c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x941810: mov             x1, x0
    // 0x941814: ldur            x0, [fp, #-0x18]
    // 0x941818: StoreField: r1->field_f = r0
    //     0x941818: stur            w0, [x1, #0xf]
    // 0x94181c: StoreField: r1->field_b = rZR
    //     0x94181c: stur            wzr, [x1, #0xb]
    // 0x941820: mov             x2, x1
    // 0x941824: ldur            x0, [fp, #-8]
    // 0x941828: ldur            x1, [fp, #-0x10]
    // 0x94182c: r0 = addAll()
    //     0x94182c: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x941830: ldur            x3, [fp, #-8]
    // 0x941834: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x941834: ldur            w1, [x3, #0x17]
    // 0x941838: DecompressPointer r1
    //     0x941838: add             x1, x1, HEAP, lsl #32
    // 0x94183c: LoadField: r0 = r3->field_1b
    //     0x94183c: ldur            w0, [x3, #0x1b]
    // 0x941840: DecompressPointer r0
    //     0x941840: add             x0, x0, HEAP, lsl #32
    // 0x941844: StoreField: r1->field_7 = r0
    //     0x941844: stur            w0, [x1, #7]
    //     0x941848: ldurb           w16, [x1, #-1]
    //     0x94184c: ldurb           w17, [x0, #-1]
    //     0x941850: and             x16, x17, x16, lsr #2
    //     0x941854: tst             x16, HEAP, lsr #32
    //     0x941858: b.eq            #0x941860
    //     0x94185c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x941860: b               #0x941868
    // 0x941864: mov             x3, x0
    // 0x941868: LoadField: r0 = r3->field_b
    //     0x941868: ldur            w0, [x3, #0xb]
    // 0x94186c: DecompressPointer r0
    //     0x94186c: add             x0, x0, HEAP, lsl #32
    // 0x941870: cmp             w0, NULL
    // 0x941874: b.eq            #0x941d40
    // 0x941878: LoadField: r1 = r0->field_f
    //     0x941878: ldur            w1, [x0, #0xf]
    // 0x94187c: DecompressPointer r1
    //     0x94187c: add             x1, x1, HEAP, lsl #32
    // 0x941880: LoadField: r2 = r1->field_b
    //     0x941880: ldur            w2, [x1, #0xb]
    // 0x941884: DecompressPointer r2
    //     0x941884: add             x2, x2, HEAP, lsl #32
    // 0x941888: cmp             w2, NULL
    // 0x94188c: b.eq            #0x94194c
    // 0x941890: LoadField: r1 = r3->field_13
    //     0x941890: ldur            w1, [x3, #0x13]
    // 0x941894: DecompressPointer r1
    //     0x941894: add             x1, x1, HEAP, lsl #32
    // 0x941898: r0 = addAll()
    //     0x941898: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x94189c: ldur            x0, [fp, #-8]
    // 0x9418a0: LoadField: r1 = r0->field_2b
    //     0x9418a0: ldur            w1, [x0, #0x2b]
    // 0x9418a4: DecompressPointer r1
    //     0x9418a4: add             x1, x1, HEAP, lsl #32
    // 0x9418a8: stur            x1, [fp, #-0x10]
    // 0x9418ac: LoadField: r2 = r0->field_b
    //     0x9418ac: ldur            w2, [x0, #0xb]
    // 0x9418b0: DecompressPointer r2
    //     0x9418b0: add             x2, x2, HEAP, lsl #32
    // 0x9418b4: cmp             w2, NULL
    // 0x9418b8: b.eq            #0x941d44
    // 0x9418bc: LoadField: r3 = r2->field_f
    //     0x9418bc: ldur            w3, [x2, #0xf]
    // 0x9418c0: DecompressPointer r3
    //     0x9418c0: add             x3, x3, HEAP, lsl #32
    // 0x9418c4: LoadField: r2 = r3->field_b
    //     0x9418c4: ldur            w2, [x3, #0xb]
    // 0x9418c8: DecompressPointer r2
    //     0x9418c8: add             x2, x2, HEAP, lsl #32
    // 0x9418cc: cmp             w2, NULL
    // 0x9418d0: b.ne            #0x941910
    // 0x9418d4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x9418d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9418d8: ldr             x0, [x0]
    //     0x9418dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9418e0: cmp             w0, w16
    //     0x9418e4: b.ne            #0x9418f0
    //     0x9418e8: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x9418ec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9418f0: r1 = <String>
    //     0x9418f0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x9418f4: stur            x0, [fp, #-0x18]
    // 0x9418f8: r0 = AllocateGrowableArray()
    //     0x9418f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9418fc: mov             x1, x0
    // 0x941900: ldur            x0, [fp, #-0x18]
    // 0x941904: StoreField: r1->field_f = r0
    //     0x941904: stur            w0, [x1, #0xf]
    // 0x941908: StoreField: r1->field_b = rZR
    //     0x941908: stur            wzr, [x1, #0xb]
    // 0x94190c: mov             x2, x1
    // 0x941910: ldur            x0, [fp, #-8]
    // 0x941914: ldur            x1, [fp, #-0x10]
    // 0x941918: r0 = addAll()
    //     0x941918: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x94191c: ldur            x3, [fp, #-8]
    // 0x941920: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x941920: ldur            w1, [x3, #0x17]
    // 0x941924: DecompressPointer r1
    //     0x941924: add             x1, x1, HEAP, lsl #32
    // 0x941928: LoadField: r0 = r3->field_2b
    //     0x941928: ldur            w0, [x3, #0x2b]
    // 0x94192c: DecompressPointer r0
    //     0x94192c: add             x0, x0, HEAP, lsl #32
    // 0x941930: StoreField: r1->field_f = r0
    //     0x941930: stur            w0, [x1, #0xf]
    //     0x941934: ldurb           w16, [x1, #-1]
    //     0x941938: ldurb           w17, [x0, #-1]
    //     0x94193c: and             x16, x17, x16, lsr #2
    //     0x941940: tst             x16, HEAP, lsr #32
    //     0x941944: b.eq            #0x94194c
    //     0x941948: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94194c: LoadField: r0 = r3->field_b
    //     0x94194c: ldur            w0, [x3, #0xb]
    // 0x941950: DecompressPointer r0
    //     0x941950: add             x0, x0, HEAP, lsl #32
    // 0x941954: cmp             w0, NULL
    // 0x941958: b.eq            #0x941d48
    // 0x94195c: LoadField: r1 = r0->field_f
    //     0x94195c: ldur            w1, [x0, #0xf]
    // 0x941960: DecompressPointer r1
    //     0x941960: add             x1, x1, HEAP, lsl #32
    // 0x941964: LoadField: r2 = r1->field_f
    //     0x941964: ldur            w2, [x1, #0xf]
    // 0x941968: DecompressPointer r2
    //     0x941968: add             x2, x2, HEAP, lsl #32
    // 0x94196c: cmp             w2, NULL
    // 0x941970: b.eq            #0x941a34
    // 0x941974: LoadField: r1 = r3->field_13
    //     0x941974: ldur            w1, [x3, #0x13]
    // 0x941978: DecompressPointer r1
    //     0x941978: add             x1, x1, HEAP, lsl #32
    // 0x94197c: r0 = addAll()
    //     0x94197c: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x941980: ldur            x0, [fp, #-8]
    // 0x941984: LoadField: r1 = r0->field_23
    //     0x941984: ldur            w1, [x0, #0x23]
    // 0x941988: DecompressPointer r1
    //     0x941988: add             x1, x1, HEAP, lsl #32
    // 0x94198c: stur            x1, [fp, #-0x10]
    // 0x941990: LoadField: r2 = r0->field_b
    //     0x941990: ldur            w2, [x0, #0xb]
    // 0x941994: DecompressPointer r2
    //     0x941994: add             x2, x2, HEAP, lsl #32
    // 0x941998: cmp             w2, NULL
    // 0x94199c: b.eq            #0x941d4c
    // 0x9419a0: LoadField: r3 = r2->field_f
    //     0x9419a0: ldur            w3, [x2, #0xf]
    // 0x9419a4: DecompressPointer r3
    //     0x9419a4: add             x3, x3, HEAP, lsl #32
    // 0x9419a8: LoadField: r2 = r3->field_f
    //     0x9419a8: ldur            w2, [x3, #0xf]
    // 0x9419ac: DecompressPointer r2
    //     0x9419ac: add             x2, x2, HEAP, lsl #32
    // 0x9419b0: cmp             w2, NULL
    // 0x9419b4: b.ne            #0x9419f4
    // 0x9419b8: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x9419b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9419bc: ldr             x0, [x0]
    //     0x9419c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9419c4: cmp             w0, w16
    //     0x9419c8: b.ne            #0x9419d4
    //     0x9419cc: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x9419d0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9419d4: r1 = <String>
    //     0x9419d4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x9419d8: stur            x0, [fp, #-0x18]
    // 0x9419dc: r0 = AllocateGrowableArray()
    //     0x9419dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9419e0: mov             x1, x0
    // 0x9419e4: ldur            x0, [fp, #-0x18]
    // 0x9419e8: StoreField: r1->field_f = r0
    //     0x9419e8: stur            w0, [x1, #0xf]
    // 0x9419ec: StoreField: r1->field_b = rZR
    //     0x9419ec: stur            wzr, [x1, #0xb]
    // 0x9419f0: mov             x2, x1
    // 0x9419f4: ldur            x0, [fp, #-8]
    // 0x9419f8: ldur            x1, [fp, #-0x10]
    // 0x9419fc: r0 = addAll()
    //     0x9419fc: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x941a00: ldur            x1, [fp, #-8]
    // 0x941a04: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x941a04: ldur            w2, [x1, #0x17]
    // 0x941a08: DecompressPointer r2
    //     0x941a08: add             x2, x2, HEAP, lsl #32
    // 0x941a0c: LoadField: r0 = r1->field_23
    //     0x941a0c: ldur            w0, [x1, #0x23]
    // 0x941a10: DecompressPointer r0
    //     0x941a10: add             x0, x0, HEAP, lsl #32
    // 0x941a14: StoreField: r2->field_b = r0
    //     0x941a14: stur            w0, [x2, #0xb]
    //     0x941a18: ldurb           w16, [x2, #-1]
    //     0x941a1c: ldurb           w17, [x0, #-1]
    //     0x941a20: and             x16, x17, x16, lsr #2
    //     0x941a24: tst             x16, HEAP, lsr #32
    //     0x941a28: b.eq            #0x941a30
    //     0x941a2c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x941a30: b               #0x941a38
    // 0x941a34: mov             x1, x3
    // 0x941a38: LoadField: r0 = r1->field_b
    //     0x941a38: ldur            w0, [x1, #0xb]
    // 0x941a3c: DecompressPointer r0
    //     0x941a3c: add             x0, x0, HEAP, lsl #32
    // 0x941a40: cmp             w0, NULL
    // 0x941a44: b.eq            #0x941d50
    // 0x941a48: LoadField: r2 = r0->field_f
    //     0x941a48: ldur            w2, [x0, #0xf]
    // 0x941a4c: DecompressPointer r2
    //     0x941a4c: add             x2, x2, HEAP, lsl #32
    // 0x941a50: LoadField: r0 = r2->field_13
    //     0x941a50: ldur            w0, [x2, #0x13]
    // 0x941a54: DecompressPointer r0
    //     0x941a54: add             x0, x0, HEAP, lsl #32
    // 0x941a58: cmp             w0, NULL
    // 0x941a5c: b.eq            #0x941d00
    // 0x941a60: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x941a60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x941a64: ldr             x0, [x0]
    //     0x941a68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x941a6c: cmp             w0, w16
    //     0x941a70: b.ne            #0x941a7c
    //     0x941a74: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x941a78: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x941a7c: r1 = <String>
    //     0x941a7c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x941a80: stur            x0, [fp, #-0x10]
    // 0x941a84: r0 = AllocateGrowableArray()
    //     0x941a84: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x941a88: mov             x2, x0
    // 0x941a8c: ldur            x0, [fp, #-0x10]
    // 0x941a90: stur            x2, [fp, #-0x18]
    // 0x941a94: StoreField: r2->field_f = r0
    //     0x941a94: stur            w0, [x2, #0xf]
    // 0x941a98: StoreField: r2->field_b = rZR
    //     0x941a98: stur            wzr, [x2, #0xb]
    // 0x941a9c: ldur            x3, [fp, #-8]
    // 0x941aa0: LoadField: r1 = r3->field_b
    //     0x941aa0: ldur            w1, [x3, #0xb]
    // 0x941aa4: DecompressPointer r1
    //     0x941aa4: add             x1, x1, HEAP, lsl #32
    // 0x941aa8: cmp             w1, NULL
    // 0x941aac: b.eq            #0x941d54
    // 0x941ab0: LoadField: r4 = r1->field_f
    //     0x941ab0: ldur            w4, [x1, #0xf]
    // 0x941ab4: DecompressPointer r4
    //     0x941ab4: add             x4, x4, HEAP, lsl #32
    // 0x941ab8: LoadField: r1 = r4->field_13
    //     0x941ab8: ldur            w1, [x4, #0x13]
    // 0x941abc: DecompressPointer r1
    //     0x941abc: add             x1, x1, HEAP, lsl #32
    // 0x941ac0: cmp             w1, NULL
    // 0x941ac4: b.ne            #0x941aec
    // 0x941ac8: r1 = <PriceRangeFilter>
    //     0x941ac8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb08] TypeArguments: <PriceRangeFilter>
    //     0x941acc: ldr             x1, [x1, #0xb08]
    // 0x941ad0: r0 = AllocateGrowableArray()
    //     0x941ad0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x941ad4: mov             x1, x0
    // 0x941ad8: ldur            x0, [fp, #-0x10]
    // 0x941adc: StoreField: r1->field_f = r0
    //     0x941adc: stur            w0, [x1, #0xf]
    // 0x941ae0: StoreField: r1->field_b = rZR
    //     0x941ae0: stur            wzr, [x1, #0xb]
    // 0x941ae4: mov             x3, x1
    // 0x941ae8: b               #0x941af0
    // 0x941aec: mov             x3, x1
    // 0x941af0: stur            x3, [fp, #-0x38]
    // 0x941af4: LoadField: r4 = r3->field_7
    //     0x941af4: ldur            w4, [x3, #7]
    // 0x941af8: DecompressPointer r4
    //     0x941af8: add             x4, x4, HEAP, lsl #32
    // 0x941afc: stur            x4, [fp, #-0x30]
    // 0x941b00: LoadField: r0 = r3->field_b
    //     0x941b00: ldur            w0, [x3, #0xb]
    // 0x941b04: r5 = LoadInt32Instr(r0)
    //     0x941b04: sbfx            x5, x0, #1, #0x1f
    // 0x941b08: stur            x5, [fp, #-0x28]
    // 0x941b0c: r0 = 0
    //     0x941b0c: movz            x0, #0
    // 0x941b10: ldur            x7, [fp, #-8]
    // 0x941b14: ldur            x6, [fp, #-0x18]
    // 0x941b18: CheckStackOverflow
    //     0x941b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941b1c: cmp             SP, x16
    //     0x941b20: b.ls            #0x941d58
    // 0x941b24: LoadField: r1 = r3->field_b
    //     0x941b24: ldur            w1, [x3, #0xb]
    // 0x941b28: r2 = LoadInt32Instr(r1)
    //     0x941b28: sbfx            x2, x1, #1, #0x1f
    // 0x941b2c: cmp             x5, x2
    // 0x941b30: b.ne            #0x941d10
    // 0x941b34: cmp             x0, x2
    // 0x941b38: b.ge            #0x941cbc
    // 0x941b3c: LoadField: r1 = r3->field_f
    //     0x941b3c: ldur            w1, [x3, #0xf]
    // 0x941b40: DecompressPointer r1
    //     0x941b40: add             x1, x1, HEAP, lsl #32
    // 0x941b44: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x941b44: add             x16, x1, x0, lsl #2
    //     0x941b48: ldur            w8, [x16, #0xf]
    // 0x941b4c: DecompressPointer r8
    //     0x941b4c: add             x8, x8, HEAP, lsl #32
    // 0x941b50: stur            x8, [fp, #-0x10]
    // 0x941b54: add             x9, x0, #1
    // 0x941b58: stur            x9, [fp, #-0x20]
    // 0x941b5c: cmp             w8, NULL
    // 0x941b60: b.ne            #0x941b94
    // 0x941b64: mov             x0, x8
    // 0x941b68: mov             x2, x4
    // 0x941b6c: r1 = Null
    //     0x941b6c: mov             x1, NULL
    // 0x941b70: cmp             w2, NULL
    // 0x941b74: b.eq            #0x941b94
    // 0x941b78: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x941b78: ldur            w4, [x2, #0x17]
    // 0x941b7c: DecompressPointer r4
    //     0x941b7c: add             x4, x4, HEAP, lsl #32
    // 0x941b80: r8 = X0
    //     0x941b80: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x941b84: LoadField: r9 = r4->field_7
    //     0x941b84: ldur            x9, [x4, #7]
    // 0x941b88: r3 = Null
    //     0x941b88: add             x3, PP, #0x56, lsl #12  ; [pp+0x565b8] Null
    //     0x941b8c: ldr             x3, [x3, #0x5b8]
    // 0x941b90: blr             x9
    // 0x941b94: ldur            x0, [fp, #-0x10]
    // 0x941b98: LoadField: r3 = r0->field_7
    //     0x941b98: ldur            w3, [x0, #7]
    // 0x941b9c: DecompressPointer r3
    //     0x941b9c: add             x3, x3, HEAP, lsl #32
    // 0x941ba0: stur            x3, [fp, #-0x40]
    // 0x941ba4: r1 = Null
    //     0x941ba4: mov             x1, NULL
    // 0x941ba8: r2 = 6
    //     0x941ba8: movz            x2, #0x6
    // 0x941bac: r0 = AllocateArray()
    //     0x941bac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x941bb0: mov             x1, x0
    // 0x941bb4: ldur            x0, [fp, #-0x40]
    // 0x941bb8: StoreField: r1->field_f = r0
    //     0x941bb8: stur            w0, [x1, #0xf]
    // 0x941bbc: r16 = " - "
    //     0x941bbc: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0x941bc0: ldr             x16, [x16, #0xc08]
    // 0x941bc4: StoreField: r1->field_13 = r16
    //     0x941bc4: stur            w16, [x1, #0x13]
    // 0x941bc8: ldur            x2, [fp, #-0x10]
    // 0x941bcc: LoadField: r3 = r2->field_b
    //     0x941bcc: ldur            w3, [x2, #0xb]
    // 0x941bd0: DecompressPointer r3
    //     0x941bd0: add             x3, x3, HEAP, lsl #32
    // 0x941bd4: cmp             w3, NULL
    // 0x941bd8: b.ne            #0x941be4
    // 0x941bdc: r3 = "above"
    //     0x941bdc: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0x941be0: ldr             x3, [x3, #0xc10]
    // 0x941be4: ldur            x0, [fp, #-0x18]
    // 0x941be8: ArrayStore: r1[0] = r3  ; List_4
    //     0x941be8: stur            w3, [x1, #0x17]
    // 0x941bec: str             x1, [SP]
    // 0x941bf0: r0 = _interpolate()
    //     0x941bf0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x941bf4: mov             x2, x0
    // 0x941bf8: ldur            x0, [fp, #-0x18]
    // 0x941bfc: stur            x2, [fp, #-0x40]
    // 0x941c00: LoadField: r1 = r0->field_b
    //     0x941c00: ldur            w1, [x0, #0xb]
    // 0x941c04: LoadField: r3 = r0->field_f
    //     0x941c04: ldur            w3, [x0, #0xf]
    // 0x941c08: DecompressPointer r3
    //     0x941c08: add             x3, x3, HEAP, lsl #32
    // 0x941c0c: LoadField: r4 = r3->field_b
    //     0x941c0c: ldur            w4, [x3, #0xb]
    // 0x941c10: r3 = LoadInt32Instr(r1)
    //     0x941c10: sbfx            x3, x1, #1, #0x1f
    // 0x941c14: stur            x3, [fp, #-0x48]
    // 0x941c18: r1 = LoadInt32Instr(r4)
    //     0x941c18: sbfx            x1, x4, #1, #0x1f
    // 0x941c1c: cmp             x3, x1
    // 0x941c20: b.ne            #0x941c2c
    // 0x941c24: mov             x1, x0
    // 0x941c28: r0 = _growToNextCapacity()
    //     0x941c28: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x941c2c: ldur            x4, [fp, #-8]
    // 0x941c30: ldur            x2, [fp, #-0x18]
    // 0x941c34: ldur            x3, [fp, #-0x48]
    // 0x941c38: add             x0, x3, #1
    // 0x941c3c: lsl             x1, x0, #1
    // 0x941c40: StoreField: r2->field_b = r1
    //     0x941c40: stur            w1, [x2, #0xb]
    // 0x941c44: LoadField: r1 = r2->field_f
    //     0x941c44: ldur            w1, [x2, #0xf]
    // 0x941c48: DecompressPointer r1
    //     0x941c48: add             x1, x1, HEAP, lsl #32
    // 0x941c4c: ldur            x0, [fp, #-0x40]
    // 0x941c50: ArrayStore: r1[r3] = r0  ; List_4
    //     0x941c50: add             x25, x1, x3, lsl #2
    //     0x941c54: add             x25, x25, #0xf
    //     0x941c58: str             w0, [x25]
    //     0x941c5c: tbz             w0, #0, #0x941c78
    //     0x941c60: ldurb           w16, [x1, #-1]
    //     0x941c64: ldurb           w17, [x0, #-1]
    //     0x941c68: and             x16, x17, x16, lsr #2
    //     0x941c6c: tst             x16, HEAP, lsr #32
    //     0x941c70: b.eq            #0x941c78
    //     0x941c74: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x941c78: LoadField: r1 = r4->field_27
    //     0x941c78: ldur            w1, [x4, #0x27]
    // 0x941c7c: DecompressPointer r1
    //     0x941c7c: add             x1, x1, HEAP, lsl #32
    // 0x941c80: stur            x1, [fp, #-0x40]
    // 0x941c84: ldur            x16, [fp, #-0x10]
    // 0x941c88: str             x16, [SP]
    // 0x941c8c: r0 = hashCode()
    //     0x941c8c: bl              #0x153c88c  ; [package:customer_app/app/data/models/collection/filter_response.dart] PriceRangeFilter::hashCode
    // 0x941c90: r3 = LoadInt32Instr(r0)
    //     0x941c90: sbfx            x3, x0, #1, #0x1f
    //     0x941c94: tbz             w0, #0, #0x941c9c
    //     0x941c98: ldur            x3, [x0, #7]
    // 0x941c9c: ldur            x1, [fp, #-0x40]
    // 0x941ca0: ldur            x2, [fp, #-0x10]
    // 0x941ca4: r0 = _add()
    //     0x941ca4: bl              #0x69d2b0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x941ca8: ldur            x0, [fp, #-0x20]
    // 0x941cac: ldur            x3, [fp, #-0x38]
    // 0x941cb0: ldur            x4, [fp, #-0x30]
    // 0x941cb4: ldur            x5, [fp, #-0x28]
    // 0x941cb8: b               #0x941b10
    // 0x941cbc: mov             x1, x7
    // 0x941cc0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x941cc0: ldur            w2, [x1, #0x17]
    // 0x941cc4: DecompressPointer r2
    //     0x941cc4: add             x2, x2, HEAP, lsl #32
    // 0x941cc8: LoadField: r0 = r1->field_27
    //     0x941cc8: ldur            w0, [x1, #0x27]
    // 0x941ccc: DecompressPointer r0
    //     0x941ccc: add             x0, x0, HEAP, lsl #32
    // 0x941cd0: StoreField: r2->field_13 = r0
    //     0x941cd0: stur            w0, [x2, #0x13]
    //     0x941cd4: ldurb           w16, [x2, #-1]
    //     0x941cd8: ldurb           w17, [x0, #-1]
    //     0x941cdc: and             x16, x17, x16, lsr #2
    //     0x941ce0: tst             x16, HEAP, lsr #32
    //     0x941ce4: b.eq            #0x941cec
    //     0x941ce8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x941cec: LoadField: r0 = r1->field_13
    //     0x941cec: ldur            w0, [x1, #0x13]
    // 0x941cf0: DecompressPointer r0
    //     0x941cf0: add             x0, x0, HEAP, lsl #32
    // 0x941cf4: mov             x1, x0
    // 0x941cf8: ldur            x2, [fp, #-0x18]
    // 0x941cfc: r0 = addAll()
    //     0x941cfc: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x941d00: r0 = Null
    //     0x941d00: mov             x0, NULL
    // 0x941d04: LeaveFrame
    //     0x941d04: mov             SP, fp
    //     0x941d08: ldp             fp, lr, [SP], #0x10
    // 0x941d0c: ret
    //     0x941d0c: ret             
    // 0x941d10: mov             x0, x3
    // 0x941d14: r0 = ConcurrentModificationError()
    //     0x941d14: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x941d18: mov             x1, x0
    // 0x941d1c: ldur            x0, [fp, #-0x38]
    // 0x941d20: StoreField: r1->field_b = r0
    //     0x941d20: stur            w0, [x1, #0xb]
    // 0x941d24: mov             x0, x1
    // 0x941d28: r0 = Throw()
    //     0x941d28: bl              #0x16f5420  ; ThrowStub
    // 0x941d2c: brk             #0
    // 0x941d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941d30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941d34: b               #0x941770
    // 0x941d38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941d54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941d58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941d5c: b               #0x941b24
  }
  _ build(/* No info */) {
    // ** addr: 0xb53bd4, size: 0x11b8
    // 0xb53bd4: EnterFrame
    //     0xb53bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xb53bd8: mov             fp, SP
    // 0xb53bdc: AllocStack(0x68)
    //     0xb53bdc: sub             SP, SP, #0x68
    // 0xb53be0: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb53be0: mov             x0, x1
    //     0xb53be4: stur            x1, [fp, #-8]
    //     0xb53be8: mov             x1, x2
    //     0xb53bec: stur            x2, [fp, #-0x10]
    // 0xb53bf0: CheckStackOverflow
    //     0xb53bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53bf4: cmp             SP, x16
    //     0xb53bf8: b.ls            #0xb54d5c
    // 0xb53bfc: r1 = 2
    //     0xb53bfc: movz            x1, #0x2
    // 0xb53c00: r0 = AllocateContext()
    //     0xb53c00: bl              #0x16f6108  ; AllocateContextStub
    // 0xb53c04: mov             x1, x0
    // 0xb53c08: ldur            x0, [fp, #-8]
    // 0xb53c0c: stur            x1, [fp, #-0x38]
    // 0xb53c10: StoreField: r1->field_f = r0
    //     0xb53c10: stur            w0, [x1, #0xf]
    // 0xb53c14: ldur            x2, [fp, #-0x10]
    // 0xb53c18: StoreField: r1->field_13 = r2
    //     0xb53c18: stur            w2, [x1, #0x13]
    // 0xb53c1c: LoadField: r3 = r0->field_2f
    //     0xb53c1c: ldur            w3, [x0, #0x2f]
    // 0xb53c20: DecompressPointer r3
    //     0xb53c20: add             x3, x3, HEAP, lsl #32
    // 0xb53c24: tbnz            w3, #4, #0xb53c30
    // 0xb53c28: r4 = Instance_Brightness
    //     0xb53c28: ldr             x4, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xb53c2c: b               #0xb53c34
    // 0xb53c30: r4 = Instance_Brightness
    //     0xb53c30: ldr             x4, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xb53c34: stur            x4, [fp, #-0x30]
    // 0xb53c38: tbnz            w3, #4, #0xb53c44
    // 0xb53c3c: r5 = Instance_Brightness
    //     0xb53c3c: ldr             x5, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xb53c40: b               #0xb53c48
    // 0xb53c44: r5 = Instance_Brightness
    //     0xb53c44: ldr             x5, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xb53c48: stur            x5, [fp, #-0x28]
    // 0xb53c4c: tbnz            w3, #4, #0xb53c58
    // 0xb53c50: r6 = Instance_Color
    //     0xb53c50: ldr             x6, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb53c54: b               #0xb53c5c
    // 0xb53c58: r6 = Instance_Color
    //     0xb53c58: ldr             x6, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb53c5c: stur            x6, [fp, #-0x20]
    // 0xb53c60: tbnz            w3, #4, #0xb53c6c
    // 0xb53c64: r3 = Instance_Brightness
    //     0xb53c64: ldr             x3, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xb53c68: b               #0xb53c70
    // 0xb53c6c: r3 = Instance_Brightness
    //     0xb53c6c: ldr             x3, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xb53c70: stur            x3, [fp, #-0x18]
    // 0xb53c74: r0 = SystemUiOverlayStyle()
    //     0xb53c74: bl              #0x6e633c  ; AllocateSystemUiOverlayStyleStub -> SystemUiOverlayStyle (size=0x28)
    // 0xb53c78: mov             x1, x0
    // 0xb53c7c: ldur            x0, [fp, #-0x20]
    // 0xb53c80: stur            x1, [fp, #-0x40]
    // 0xb53c84: StoreField: r1->field_7 = r0
    //     0xb53c84: stur            w0, [x1, #7]
    // 0xb53c88: r0 = Instance_Color
    //     0xb53c88: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb53c8c: ldr             x0, [x0, #0xf88]
    // 0xb53c90: StoreField: r1->field_b = r0
    //     0xb53c90: stur            w0, [x1, #0xb]
    // 0xb53c94: ldur            x2, [fp, #-0x18]
    // 0xb53c98: StoreField: r1->field_f = r2
    //     0xb53c98: stur            w2, [x1, #0xf]
    // 0xb53c9c: r2 = false
    //     0xb53c9c: add             x2, NULL, #0x30  ; false
    // 0xb53ca0: StoreField: r1->field_13 = r2
    //     0xb53ca0: stur            w2, [x1, #0x13]
    // 0xb53ca4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb53ca4: stur            w0, [x1, #0x17]
    // 0xb53ca8: ldur            x0, [fp, #-0x28]
    // 0xb53cac: StoreField: r1->field_1b = r0
    //     0xb53cac: stur            w0, [x1, #0x1b]
    // 0xb53cb0: ldur            x0, [fp, #-0x30]
    // 0xb53cb4: StoreField: r1->field_1f = r0
    //     0xb53cb4: stur            w0, [x1, #0x1f]
    // 0xb53cb8: StoreField: r1->field_23 = r2
    //     0xb53cb8: stur            w2, [x1, #0x23]
    // 0xb53cbc: r0 = InkWell()
    //     0xb53cbc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb53cc0: mov             x3, x0
    // 0xb53cc4: r0 = Instance_Icon
    //     0xb53cc4: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb53cc8: ldr             x0, [x0, #0x2b8]
    // 0xb53ccc: stur            x3, [fp, #-0x18]
    // 0xb53cd0: StoreField: r3->field_b = r0
    //     0xb53cd0: stur            w0, [x3, #0xb]
    // 0xb53cd4: r1 = Function '<anonymous closure>':.
    //     0xb53cd4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56520] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb53cd8: ldr             x1, [x1, #0x520]
    // 0xb53cdc: r2 = Null
    //     0xb53cdc: mov             x2, NULL
    // 0xb53ce0: r0 = AllocateClosure()
    //     0xb53ce0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb53ce4: mov             x1, x0
    // 0xb53ce8: ldur            x0, [fp, #-0x18]
    // 0xb53cec: StoreField: r0->field_f = r1
    //     0xb53cec: stur            w1, [x0, #0xf]
    // 0xb53cf0: r2 = true
    //     0xb53cf0: add             x2, NULL, #0x20  ; true
    // 0xb53cf4: StoreField: r0->field_43 = r2
    //     0xb53cf4: stur            w2, [x0, #0x43]
    // 0xb53cf8: r3 = Instance_BoxShape
    //     0xb53cf8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb53cfc: ldr             x3, [x3, #0x80]
    // 0xb53d00: StoreField: r0->field_47 = r3
    //     0xb53d00: stur            w3, [x0, #0x47]
    // 0xb53d04: StoreField: r0->field_6f = r2
    //     0xb53d04: stur            w2, [x0, #0x6f]
    // 0xb53d08: r4 = false
    //     0xb53d08: add             x4, NULL, #0x30  ; false
    // 0xb53d0c: StoreField: r0->field_73 = r4
    //     0xb53d0c: stur            w4, [x0, #0x73]
    // 0xb53d10: StoreField: r0->field_83 = r2
    //     0xb53d10: stur            w2, [x0, #0x83]
    // 0xb53d14: StoreField: r0->field_7b = r4
    //     0xb53d14: stur            w4, [x0, #0x7b]
    // 0xb53d18: ldur            x1, [fp, #-0x10]
    // 0xb53d1c: r0 = of()
    //     0xb53d1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb53d20: LoadField: r1 = r0->field_87
    //     0xb53d20: ldur            w1, [x0, #0x87]
    // 0xb53d24: DecompressPointer r1
    //     0xb53d24: add             x1, x1, HEAP, lsl #32
    // 0xb53d28: LoadField: r0 = r1->field_7
    //     0xb53d28: ldur            w0, [x1, #7]
    // 0xb53d2c: DecompressPointer r0
    //     0xb53d2c: add             x0, x0, HEAP, lsl #32
    // 0xb53d30: r16 = 16.000000
    //     0xb53d30: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb53d34: ldr             x16, [x16, #0x188]
    // 0xb53d38: r30 = Instance_Color
    //     0xb53d38: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb53d3c: stp             lr, x16, [SP]
    // 0xb53d40: mov             x1, x0
    // 0xb53d44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb53d44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb53d48: ldr             x4, [x4, #0xaa0]
    // 0xb53d4c: r0 = copyWith()
    //     0xb53d4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb53d50: stur            x0, [fp, #-0x10]
    // 0xb53d54: r0 = Text()
    //     0xb53d54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb53d58: mov             x3, x0
    // 0xb53d5c: r0 = "Filters"
    //     0xb53d5c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40bb0] "Filters"
    //     0xb53d60: ldr             x0, [x0, #0xbb0]
    // 0xb53d64: stur            x3, [fp, #-0x20]
    // 0xb53d68: StoreField: r3->field_b = r0
    //     0xb53d68: stur            w0, [x3, #0xb]
    // 0xb53d6c: ldur            x0, [fp, #-0x10]
    // 0xb53d70: StoreField: r3->field_13 = r0
    //     0xb53d70: stur            w0, [x3, #0x13]
    // 0xb53d74: r1 = Null
    //     0xb53d74: mov             x1, NULL
    // 0xb53d78: r2 = 4
    //     0xb53d78: movz            x2, #0x4
    // 0xb53d7c: r0 = AllocateArray()
    //     0xb53d7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb53d80: mov             x2, x0
    // 0xb53d84: ldur            x0, [fp, #-0x18]
    // 0xb53d88: stur            x2, [fp, #-0x10]
    // 0xb53d8c: StoreField: r2->field_f = r0
    //     0xb53d8c: stur            w0, [x2, #0xf]
    // 0xb53d90: ldur            x0, [fp, #-0x20]
    // 0xb53d94: StoreField: r2->field_13 = r0
    //     0xb53d94: stur            w0, [x2, #0x13]
    // 0xb53d98: r1 = <Widget>
    //     0xb53d98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb53d9c: r0 = AllocateGrowableArray()
    //     0xb53d9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb53da0: mov             x1, x0
    // 0xb53da4: ldur            x0, [fp, #-0x10]
    // 0xb53da8: stur            x1, [fp, #-0x18]
    // 0xb53dac: StoreField: r1->field_f = r0
    //     0xb53dac: stur            w0, [x1, #0xf]
    // 0xb53db0: r2 = 4
    //     0xb53db0: movz            x2, #0x4
    // 0xb53db4: StoreField: r1->field_b = r2
    //     0xb53db4: stur            w2, [x1, #0xb]
    // 0xb53db8: r0 = Row()
    //     0xb53db8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb53dbc: mov             x1, x0
    // 0xb53dc0: r0 = Instance_Axis
    //     0xb53dc0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb53dc4: stur            x1, [fp, #-0x10]
    // 0xb53dc8: StoreField: r1->field_f = r0
    //     0xb53dc8: stur            w0, [x1, #0xf]
    // 0xb53dcc: r2 = Instance_MainAxisAlignment
    //     0xb53dcc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb53dd0: ldr             x2, [x2, #0xd10]
    // 0xb53dd4: StoreField: r1->field_13 = r2
    //     0xb53dd4: stur            w2, [x1, #0x13]
    // 0xb53dd8: r2 = Instance_MainAxisSize
    //     0xb53dd8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb53ddc: ldr             x2, [x2, #0xa10]
    // 0xb53de0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb53de0: stur            w2, [x1, #0x17]
    // 0xb53de4: r3 = Instance_CrossAxisAlignment
    //     0xb53de4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb53de8: ldr             x3, [x3, #0xa18]
    // 0xb53dec: StoreField: r1->field_1b = r3
    //     0xb53dec: stur            w3, [x1, #0x1b]
    // 0xb53df0: r4 = Instance_VerticalDirection
    //     0xb53df0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb53df4: ldr             x4, [x4, #0xa20]
    // 0xb53df8: StoreField: r1->field_23 = r4
    //     0xb53df8: stur            w4, [x1, #0x23]
    // 0xb53dfc: r5 = Instance_Clip
    //     0xb53dfc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb53e00: ldr             x5, [x5, #0x38]
    // 0xb53e04: StoreField: r1->field_2b = r5
    //     0xb53e04: stur            w5, [x1, #0x2b]
    // 0xb53e08: StoreField: r1->field_2f = rZR
    //     0xb53e08: stur            xzr, [x1, #0x2f]
    // 0xb53e0c: ldur            x6, [fp, #-0x18]
    // 0xb53e10: StoreField: r1->field_b = r6
    //     0xb53e10: stur            w6, [x1, #0xb]
    // 0xb53e14: r0 = SizedBox()
    //     0xb53e14: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb53e18: mov             x2, x0
    // 0xb53e1c: r0 = 100.000000
    //     0xb53e1c: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb53e20: stur            x2, [fp, #-0x18]
    // 0xb53e24: StoreField: r2->field_f = r0
    //     0xb53e24: stur            w0, [x2, #0xf]
    // 0xb53e28: ldur            x0, [fp, #-0x10]
    // 0xb53e2c: StoreField: r2->field_b = r0
    //     0xb53e2c: stur            w0, [x2, #0xb]
    // 0xb53e30: ldur            x0, [fp, #-0x38]
    // 0xb53e34: LoadField: r1 = r0->field_13
    //     0xb53e34: ldur            w1, [x0, #0x13]
    // 0xb53e38: DecompressPointer r1
    //     0xb53e38: add             x1, x1, HEAP, lsl #32
    // 0xb53e3c: r0 = of()
    //     0xb53e3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb53e40: LoadField: r1 = r0->field_5b
    //     0xb53e40: ldur            w1, [x0, #0x5b]
    // 0xb53e44: DecompressPointer r1
    //     0xb53e44: add             x1, x1, HEAP, lsl #32
    // 0xb53e48: r0 = LoadClassIdInstr(r1)
    //     0xb53e48: ldur            x0, [x1, #-1]
    //     0xb53e4c: ubfx            x0, x0, #0xc, #0x14
    // 0xb53e50: d0 = 0.030000
    //     0xb53e50: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb53e54: ldr             d0, [x17, #0x238]
    // 0xb53e58: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb53e58: sub             lr, x0, #0xffa
    //     0xb53e5c: ldr             lr, [x21, lr, lsl #3]
    //     0xb53e60: blr             lr
    // 0xb53e64: stur            x0, [fp, #-0x10]
    // 0xb53e68: r0 = Radius()
    //     0xb53e68: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb53e6c: d0 = 20.000000
    //     0xb53e6c: fmov            d0, #20.00000000
    // 0xb53e70: stur            x0, [fp, #-0x20]
    // 0xb53e74: StoreField: r0->field_7 = d0
    //     0xb53e74: stur            d0, [x0, #7]
    // 0xb53e78: StoreField: r0->field_f = d0
    //     0xb53e78: stur            d0, [x0, #0xf]
    // 0xb53e7c: r0 = BorderRadius()
    //     0xb53e7c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb53e80: mov             x1, x0
    // 0xb53e84: ldur            x0, [fp, #-0x20]
    // 0xb53e88: stur            x1, [fp, #-0x28]
    // 0xb53e8c: StoreField: r1->field_7 = r0
    //     0xb53e8c: stur            w0, [x1, #7]
    // 0xb53e90: StoreField: r1->field_b = r0
    //     0xb53e90: stur            w0, [x1, #0xb]
    // 0xb53e94: StoreField: r1->field_f = r0
    //     0xb53e94: stur            w0, [x1, #0xf]
    // 0xb53e98: StoreField: r1->field_13 = r0
    //     0xb53e98: stur            w0, [x1, #0x13]
    // 0xb53e9c: r0 = BoxDecoration()
    //     0xb53e9c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb53ea0: mov             x2, x0
    // 0xb53ea4: ldur            x0, [fp, #-0x10]
    // 0xb53ea8: stur            x2, [fp, #-0x20]
    // 0xb53eac: StoreField: r2->field_7 = r0
    //     0xb53eac: stur            w0, [x2, #7]
    // 0xb53eb0: ldur            x0, [fp, #-0x28]
    // 0xb53eb4: StoreField: r2->field_13 = r0
    //     0xb53eb4: stur            w0, [x2, #0x13]
    // 0xb53eb8: r0 = Instance_BoxShape
    //     0xb53eb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb53ebc: ldr             x0, [x0, #0x80]
    // 0xb53ec0: StoreField: r2->field_23 = r0
    //     0xb53ec0: stur            w0, [x2, #0x23]
    // 0xb53ec4: r1 = "clear all"
    //     0xb53ec4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56528] "clear all"
    //     0xb53ec8: ldr             x1, [x1, #0x528]
    // 0xb53ecc: r0 = capitalizeFirstWord()
    //     0xb53ecc: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb53ed0: ldur            x2, [fp, #-0x38]
    // 0xb53ed4: stur            x0, [fp, #-0x10]
    // 0xb53ed8: LoadField: r1 = r2->field_13
    //     0xb53ed8: ldur            w1, [x2, #0x13]
    // 0xb53edc: DecompressPointer r1
    //     0xb53edc: add             x1, x1, HEAP, lsl #32
    // 0xb53ee0: r0 = of()
    //     0xb53ee0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb53ee4: LoadField: r1 = r0->field_87
    //     0xb53ee4: ldur            w1, [x0, #0x87]
    // 0xb53ee8: DecompressPointer r1
    //     0xb53ee8: add             x1, x1, HEAP, lsl #32
    // 0xb53eec: LoadField: r0 = r1->field_7
    //     0xb53eec: ldur            w0, [x1, #7]
    // 0xb53ef0: DecompressPointer r0
    //     0xb53ef0: add             x0, x0, HEAP, lsl #32
    // 0xb53ef4: ldur            x2, [fp, #-0x38]
    // 0xb53ef8: stur            x0, [fp, #-0x28]
    // 0xb53efc: LoadField: r1 = r2->field_13
    //     0xb53efc: ldur            w1, [x2, #0x13]
    // 0xb53f00: DecompressPointer r1
    //     0xb53f00: add             x1, x1, HEAP, lsl #32
    // 0xb53f04: r0 = of()
    //     0xb53f04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb53f08: LoadField: r1 = r0->field_5b
    //     0xb53f08: ldur            w1, [x0, #0x5b]
    // 0xb53f0c: DecompressPointer r1
    //     0xb53f0c: add             x1, x1, HEAP, lsl #32
    // 0xb53f10: r0 = LoadClassIdInstr(r1)
    //     0xb53f10: ldur            x0, [x1, #-1]
    //     0xb53f14: ubfx            x0, x0, #0xc, #0x14
    // 0xb53f18: d0 = 0.700000
    //     0xb53f18: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb53f1c: ldr             d0, [x17, #0xf48]
    // 0xb53f20: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb53f20: sub             lr, x0, #0xffa
    //     0xb53f24: ldr             lr, [x21, lr, lsl #3]
    //     0xb53f28: blr             lr
    // 0xb53f2c: r16 = 14.000000
    //     0xb53f2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb53f30: ldr             x16, [x16, #0x1d8]
    // 0xb53f34: stp             x0, x16, [SP]
    // 0xb53f38: ldur            x1, [fp, #-0x28]
    // 0xb53f3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb53f3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb53f40: ldr             x4, [x4, #0xaa0]
    // 0xb53f44: r0 = copyWith()
    //     0xb53f44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb53f48: stur            x0, [fp, #-0x28]
    // 0xb53f4c: r0 = Text()
    //     0xb53f4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb53f50: mov             x1, x0
    // 0xb53f54: ldur            x0, [fp, #-0x10]
    // 0xb53f58: stur            x1, [fp, #-0x30]
    // 0xb53f5c: StoreField: r1->field_b = r0
    //     0xb53f5c: stur            w0, [x1, #0xb]
    // 0xb53f60: ldur            x0, [fp, #-0x28]
    // 0xb53f64: StoreField: r1->field_13 = r0
    //     0xb53f64: stur            w0, [x1, #0x13]
    // 0xb53f68: r0 = Center()
    //     0xb53f68: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb53f6c: mov             x1, x0
    // 0xb53f70: r0 = Instance_Alignment
    //     0xb53f70: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb53f74: ldr             x0, [x0, #0xb10]
    // 0xb53f78: stur            x1, [fp, #-0x10]
    // 0xb53f7c: StoreField: r1->field_f = r0
    //     0xb53f7c: stur            w0, [x1, #0xf]
    // 0xb53f80: ldur            x0, [fp, #-0x30]
    // 0xb53f84: StoreField: r1->field_b = r0
    //     0xb53f84: stur            w0, [x1, #0xb]
    // 0xb53f88: r0 = Container()
    //     0xb53f88: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb53f8c: stur            x0, [fp, #-0x28]
    // 0xb53f90: r16 = 28.000000
    //     0xb53f90: add             x16, PP, #0x52, lsl #12  ; [pp+0x52600] 28
    //     0xb53f94: ldr             x16, [x16, #0x600]
    // 0xb53f98: r30 = Instance_EdgeInsets
    //     0xb53f98: add             lr, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xb53f9c: ldr             lr, [lr, #0x50]
    // 0xb53fa0: stp             lr, x16, [SP, #0x10]
    // 0xb53fa4: ldur            x16, [fp, #-0x20]
    // 0xb53fa8: ldur            lr, [fp, #-0x10]
    // 0xb53fac: stp             lr, x16, [SP]
    // 0xb53fb0: mov             x1, x0
    // 0xb53fb4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, padding, 0x2, null]
    //     0xb53fb4: add             x4, PP, #0x56, lsl #12  ; [pp+0x56530] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xb53fb8: ldr             x4, [x4, #0x530]
    // 0xb53fbc: r0 = Container()
    //     0xb53fbc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb53fc0: r0 = InkWell()
    //     0xb53fc0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb53fc4: mov             x3, x0
    // 0xb53fc8: ldur            x0, [fp, #-0x28]
    // 0xb53fcc: stur            x3, [fp, #-0x10]
    // 0xb53fd0: StoreField: r3->field_b = r0
    //     0xb53fd0: stur            w0, [x3, #0xb]
    // 0xb53fd4: ldur            x2, [fp, #-0x38]
    // 0xb53fd8: r1 = Function '<anonymous closure>':.
    //     0xb53fd8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56538] AnonymousClosure: (0xb56488), in [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xb53bd4)
    //     0xb53fdc: ldr             x1, [x1, #0x538]
    // 0xb53fe0: r0 = AllocateClosure()
    //     0xb53fe0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb53fe4: mov             x1, x0
    // 0xb53fe8: ldur            x0, [fp, #-0x10]
    // 0xb53fec: StoreField: r0->field_f = r1
    //     0xb53fec: stur            w1, [x0, #0xf]
    // 0xb53ff0: r3 = true
    //     0xb53ff0: add             x3, NULL, #0x20  ; true
    // 0xb53ff4: StoreField: r0->field_43 = r3
    //     0xb53ff4: stur            w3, [x0, #0x43]
    // 0xb53ff8: r1 = Instance_BoxShape
    //     0xb53ff8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb53ffc: ldr             x1, [x1, #0x80]
    // 0xb54000: StoreField: r0->field_47 = r1
    //     0xb54000: stur            w1, [x0, #0x47]
    // 0xb54004: StoreField: r0->field_6f = r3
    //     0xb54004: stur            w3, [x0, #0x6f]
    // 0xb54008: r4 = false
    //     0xb54008: add             x4, NULL, #0x30  ; false
    // 0xb5400c: StoreField: r0->field_73 = r4
    //     0xb5400c: stur            w4, [x0, #0x73]
    // 0xb54010: StoreField: r0->field_83 = r3
    //     0xb54010: stur            w3, [x0, #0x83]
    // 0xb54014: StoreField: r0->field_7b = r4
    //     0xb54014: stur            w4, [x0, #0x7b]
    // 0xb54018: r1 = Null
    //     0xb54018: mov             x1, NULL
    // 0xb5401c: r2 = 4
    //     0xb5401c: movz            x2, #0x4
    // 0xb54020: r0 = AllocateArray()
    //     0xb54020: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb54024: mov             x2, x0
    // 0xb54028: ldur            x0, [fp, #-0x18]
    // 0xb5402c: stur            x2, [fp, #-0x20]
    // 0xb54030: StoreField: r2->field_f = r0
    //     0xb54030: stur            w0, [x2, #0xf]
    // 0xb54034: ldur            x0, [fp, #-0x10]
    // 0xb54038: StoreField: r2->field_13 = r0
    //     0xb54038: stur            w0, [x2, #0x13]
    // 0xb5403c: r1 = <Widget>
    //     0xb5403c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb54040: r0 = AllocateGrowableArray()
    //     0xb54040: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb54044: mov             x1, x0
    // 0xb54048: ldur            x0, [fp, #-0x20]
    // 0xb5404c: stur            x1, [fp, #-0x10]
    // 0xb54050: StoreField: r1->field_f = r0
    //     0xb54050: stur            w0, [x1, #0xf]
    // 0xb54054: r0 = 4
    //     0xb54054: movz            x0, #0x4
    // 0xb54058: StoreField: r1->field_b = r0
    //     0xb54058: stur            w0, [x1, #0xb]
    // 0xb5405c: r0 = Row()
    //     0xb5405c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb54060: mov             x1, x0
    // 0xb54064: r0 = Instance_Axis
    //     0xb54064: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb54068: stur            x1, [fp, #-0x18]
    // 0xb5406c: StoreField: r1->field_f = r0
    //     0xb5406c: stur            w0, [x1, #0xf]
    // 0xb54070: r0 = Instance_MainAxisAlignment
    //     0xb54070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb54074: ldr             x0, [x0, #0xa8]
    // 0xb54078: StoreField: r1->field_13 = r0
    //     0xb54078: stur            w0, [x1, #0x13]
    // 0xb5407c: r0 = Instance_MainAxisSize
    //     0xb5407c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb54080: ldr             x0, [x0, #0xa10]
    // 0xb54084: ArrayStore: r1[0] = r0  ; List_4
    //     0xb54084: stur            w0, [x1, #0x17]
    // 0xb54088: r2 = Instance_CrossAxisAlignment
    //     0xb54088: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5408c: ldr             x2, [x2, #0xa18]
    // 0xb54090: StoreField: r1->field_1b = r2
    //     0xb54090: stur            w2, [x1, #0x1b]
    // 0xb54094: r3 = Instance_VerticalDirection
    //     0xb54094: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb54098: ldr             x3, [x3, #0xa20]
    // 0xb5409c: StoreField: r1->field_23 = r3
    //     0xb5409c: stur            w3, [x1, #0x23]
    // 0xb540a0: r4 = Instance_Clip
    //     0xb540a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb540a4: ldr             x4, [x4, #0x38]
    // 0xb540a8: StoreField: r1->field_2b = r4
    //     0xb540a8: stur            w4, [x1, #0x2b]
    // 0xb540ac: StoreField: r1->field_2f = rZR
    //     0xb540ac: stur            xzr, [x1, #0x2f]
    // 0xb540b0: ldur            x5, [fp, #-0x10]
    // 0xb540b4: StoreField: r1->field_b = r5
    //     0xb540b4: stur            w5, [x1, #0xb]
    // 0xb540b8: r0 = Padding()
    //     0xb540b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb540bc: mov             x3, x0
    // 0xb540c0: r0 = Instance_EdgeInsets
    //     0xb540c0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xb540c4: ldr             x0, [x0, #0x560]
    // 0xb540c8: stur            x3, [fp, #-0x10]
    // 0xb540cc: StoreField: r3->field_f = r0
    //     0xb540cc: stur            w0, [x3, #0xf]
    // 0xb540d0: ldur            x0, [fp, #-0x18]
    // 0xb540d4: StoreField: r3->field_b = r0
    //     0xb540d4: stur            w0, [x3, #0xb]
    // 0xb540d8: r1 = <Widget>
    //     0xb540d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb540dc: r2 = 36
    //     0xb540dc: movz            x2, #0x24
    // 0xb540e0: r0 = AllocateArray()
    //     0xb540e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb540e4: mov             x2, x0
    // 0xb540e8: ldur            x0, [fp, #-0x10]
    // 0xb540ec: stur            x2, [fp, #-0x18]
    // 0xb540f0: StoreField: r2->field_f = r0
    //     0xb540f0: stur            w0, [x2, #0xf]
    // 0xb540f4: r16 = Instance_SizedBox
    //     0xb540f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb540f8: ldr             x16, [x16, #0x8f0]
    // 0xb540fc: StoreField: r2->field_13 = r16
    //     0xb540fc: stur            w16, [x2, #0x13]
    // 0xb54100: ldur            x0, [fp, #-0x38]
    // 0xb54104: LoadField: r1 = r0->field_13
    //     0xb54104: ldur            w1, [x0, #0x13]
    // 0xb54108: DecompressPointer r1
    //     0xb54108: add             x1, x1, HEAP, lsl #32
    // 0xb5410c: r0 = of()
    //     0xb5410c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54110: LoadField: r1 = r0->field_87
    //     0xb54110: ldur            w1, [x0, #0x87]
    // 0xb54114: DecompressPointer r1
    //     0xb54114: add             x1, x1, HEAP, lsl #32
    // 0xb54118: LoadField: r0 = r1->field_7
    //     0xb54118: ldur            w0, [x1, #7]
    // 0xb5411c: DecompressPointer r0
    //     0xb5411c: add             x0, x0, HEAP, lsl #32
    // 0xb54120: r16 = 14.000000
    //     0xb54120: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb54124: ldr             x16, [x16, #0x1d8]
    // 0xb54128: r30 = Instance_Color
    //     0xb54128: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5412c: stp             lr, x16, [SP]
    // 0xb54130: mov             x1, x0
    // 0xb54134: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54134: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54138: ldr             x4, [x4, #0xaa0]
    // 0xb5413c: r0 = copyWith()
    //     0xb5413c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb54140: stur            x0, [fp, #-0x10]
    // 0xb54144: r0 = Text()
    //     0xb54144: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb54148: mov             x3, x0
    // 0xb5414c: r0 = "Product Type"
    //     0xb5414c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d08] "Product Type"
    //     0xb54150: ldr             x0, [x0, #0xd08]
    // 0xb54154: stur            x3, [fp, #-0x20]
    // 0xb54158: StoreField: r3->field_b = r0
    //     0xb54158: stur            w0, [x3, #0xb]
    // 0xb5415c: ldur            x0, [fp, #-0x10]
    // 0xb54160: StoreField: r3->field_13 = r0
    //     0xb54160: stur            w0, [x3, #0x13]
    // 0xb54164: ldur            x0, [fp, #-8]
    // 0xb54168: LoadField: r1 = r0->field_b
    //     0xb54168: ldur            w1, [x0, #0xb]
    // 0xb5416c: DecompressPointer r1
    //     0xb5416c: add             x1, x1, HEAP, lsl #32
    // 0xb54170: cmp             w1, NULL
    // 0xb54174: b.eq            #0xb54d64
    // 0xb54178: LoadField: r2 = r1->field_b
    //     0xb54178: ldur            w2, [x1, #0xb]
    // 0xb5417c: DecompressPointer r2
    //     0xb5417c: add             x2, x2, HEAP, lsl #32
    // 0xb54180: cmp             w2, NULL
    // 0xb54184: b.ne            #0xb54190
    // 0xb54188: r6 = Null
    //     0xb54188: mov             x6, NULL
    // 0xb5418c: b               #0xb541b4
    // 0xb54190: LoadField: r1 = r2->field_7
    //     0xb54190: ldur            w1, [x2, #7]
    // 0xb54194: DecompressPointer r1
    //     0xb54194: add             x1, x1, HEAP, lsl #32
    // 0xb54198: cmp             w1, NULL
    // 0xb5419c: b.ne            #0xb541a8
    // 0xb541a0: r1 = Null
    //     0xb541a0: mov             x1, NULL
    // 0xb541a4: b               #0xb541b0
    // 0xb541a8: LoadField: r2 = r1->field_b
    //     0xb541a8: ldur            w2, [x1, #0xb]
    // 0xb541ac: mov             x1, x2
    // 0xb541b0: mov             x6, x1
    // 0xb541b4: ldur            x5, [fp, #-0x38]
    // 0xb541b8: ldur            x4, [fp, #-0x18]
    // 0xb541bc: mov             x2, x5
    // 0xb541c0: stur            x6, [fp, #-0x10]
    // 0xb541c4: r1 = Function '<anonymous closure>':.
    //     0xb541c4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56540] AnonymousClosure: (0xb560e8), in [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xb53bd4)
    //     0xb541c8: ldr             x1, [x1, #0x540]
    // 0xb541cc: r0 = AllocateClosure()
    //     0xb541cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb541d0: stur            x0, [fp, #-0x28]
    // 0xb541d4: r0 = ListView()
    //     0xb541d4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb541d8: stur            x0, [fp, #-0x30]
    // 0xb541dc: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb541dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb541e0: ldr             x16, [x16, #0x1c8]
    // 0xb541e4: r30 = true
    //     0xb541e4: add             lr, NULL, #0x20  ; true
    // 0xb541e8: stp             lr, x16, [SP]
    // 0xb541ec: mov             x1, x0
    // 0xb541f0: ldur            x2, [fp, #-0x28]
    // 0xb541f4: ldur            x3, [fp, #-0x10]
    // 0xb541f8: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xb541f8: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb541fc: ldr             x4, [x4, #0xd18]
    // 0xb54200: r0 = ListView.builder()
    //     0xb54200: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb54204: r0 = Padding()
    //     0xb54204: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb54208: mov             x2, x0
    // 0xb5420c: r0 = Instance_EdgeInsets
    //     0xb5420c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb54210: ldr             x0, [x0, #0xd48]
    // 0xb54214: stur            x2, [fp, #-0x10]
    // 0xb54218: StoreField: r2->field_f = r0
    //     0xb54218: stur            w0, [x2, #0xf]
    // 0xb5421c: ldur            x1, [fp, #-0x30]
    // 0xb54220: StoreField: r2->field_b = r1
    //     0xb54220: stur            w1, [x2, #0xb]
    // 0xb54224: ldur            x3, [fp, #-0x38]
    // 0xb54228: LoadField: r1 = r3->field_13
    //     0xb54228: ldur            w1, [x3, #0x13]
    // 0xb5422c: DecompressPointer r1
    //     0xb5422c: add             x1, x1, HEAP, lsl #32
    // 0xb54230: r0 = of()
    //     0xb54230: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54234: LoadField: r1 = r0->field_87
    //     0xb54234: ldur            w1, [x0, #0x87]
    // 0xb54238: DecompressPointer r1
    //     0xb54238: add             x1, x1, HEAP, lsl #32
    // 0xb5423c: LoadField: r0 = r1->field_27
    //     0xb5423c: ldur            w0, [x1, #0x27]
    // 0xb54240: DecompressPointer r0
    //     0xb54240: add             x0, x0, HEAP, lsl #32
    // 0xb54244: ldur            x2, [fp, #-0x38]
    // 0xb54248: stur            x0, [fp, #-0x28]
    // 0xb5424c: LoadField: r1 = r2->field_13
    //     0xb5424c: ldur            w1, [x2, #0x13]
    // 0xb54250: DecompressPointer r1
    //     0xb54250: add             x1, x1, HEAP, lsl #32
    // 0xb54254: r0 = of()
    //     0xb54254: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54258: LoadField: r1 = r0->field_5b
    //     0xb54258: ldur            w1, [x0, #0x5b]
    // 0xb5425c: DecompressPointer r1
    //     0xb5425c: add             x1, x1, HEAP, lsl #32
    // 0xb54260: r0 = LoadClassIdInstr(r1)
    //     0xb54260: ldur            x0, [x1, #-1]
    //     0xb54264: ubfx            x0, x0, #0xc, #0x14
    // 0xb54268: d0 = 0.400000
    //     0xb54268: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb5426c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb5426c: sub             lr, x0, #0xffa
    //     0xb54270: ldr             lr, [x21, lr, lsl #3]
    //     0xb54274: blr             lr
    // 0xb54278: r16 = 16.000000
    //     0xb54278: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5427c: ldr             x16, [x16, #0x188]
    // 0xb54280: stp             x0, x16, [SP]
    // 0xb54284: ldur            x1, [fp, #-0x28]
    // 0xb54288: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54288: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5428c: ldr             x4, [x4, #0xaa0]
    // 0xb54290: r0 = copyWith()
    //     0xb54290: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb54294: r0 = Accordion()
    //     0xb54294: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb54298: mov             x1, x0
    // 0xb5429c: ldur            x0, [fp, #-0x20]
    // 0xb542a0: StoreField: r1->field_b = r0
    //     0xb542a0: stur            w0, [x1, #0xb]
    // 0xb542a4: ldur            x0, [fp, #-0x10]
    // 0xb542a8: StoreField: r1->field_13 = r0
    //     0xb542a8: stur            w0, [x1, #0x13]
    // 0xb542ac: r2 = false
    //     0xb542ac: add             x2, NULL, #0x30  ; false
    // 0xb542b0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb542b0: stur            w2, [x1, #0x17]
    // 0xb542b4: d0 = 25.000000
    //     0xb542b4: fmov            d0, #25.00000000
    // 0xb542b8: StoreField: r1->field_1b = d0
    //     0xb542b8: stur            d0, [x1, #0x1b]
    // 0xb542bc: r3 = true
    //     0xb542bc: add             x3, NULL, #0x20  ; true
    // 0xb542c0: StoreField: r1->field_23 = r3
    //     0xb542c0: stur            w3, [x1, #0x23]
    // 0xb542c4: mov             x0, x1
    // 0xb542c8: ldur            x1, [fp, #-0x18]
    // 0xb542cc: ArrayStore: r1[2] = r0  ; List_4
    //     0xb542cc: add             x25, x1, #0x17
    //     0xb542d0: str             w0, [x25]
    //     0xb542d4: tbz             w0, #0, #0xb542f0
    //     0xb542d8: ldurb           w16, [x1, #-1]
    //     0xb542dc: ldurb           w17, [x0, #-1]
    //     0xb542e0: and             x16, x17, x16, lsr #2
    //     0xb542e4: tst             x16, HEAP, lsr #32
    //     0xb542e8: b.eq            #0xb542f0
    //     0xb542ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb542f0: ldur            x0, [fp, #-0x18]
    // 0xb542f4: r16 = Instance_SizedBox
    //     0xb542f4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb542f8: ldr             x16, [x16, #0x568]
    // 0xb542fc: StoreField: r0->field_1b = r16
    //     0xb542fc: stur            w16, [x0, #0x1b]
    // 0xb54300: r16 = Instance_Padding
    //     0xb54300: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xb54304: ldr             x16, [x16, #0xd20]
    // 0xb54308: StoreField: r0->field_1f = r16
    //     0xb54308: stur            w16, [x0, #0x1f]
    // 0xb5430c: r16 = Instance_SizedBox
    //     0xb5430c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb54310: ldr             x16, [x16, #0x568]
    // 0xb54314: StoreField: r0->field_23 = r16
    //     0xb54314: stur            w16, [x0, #0x23]
    // 0xb54318: ldur            x4, [fp, #-0x38]
    // 0xb5431c: LoadField: r1 = r4->field_13
    //     0xb5431c: ldur            w1, [x4, #0x13]
    // 0xb54320: DecompressPointer r1
    //     0xb54320: add             x1, x1, HEAP, lsl #32
    // 0xb54324: r0 = of()
    //     0xb54324: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54328: LoadField: r1 = r0->field_87
    //     0xb54328: ldur            w1, [x0, #0x87]
    // 0xb5432c: DecompressPointer r1
    //     0xb5432c: add             x1, x1, HEAP, lsl #32
    // 0xb54330: LoadField: r0 = r1->field_7
    //     0xb54330: ldur            w0, [x1, #7]
    // 0xb54334: DecompressPointer r0
    //     0xb54334: add             x0, x0, HEAP, lsl #32
    // 0xb54338: r16 = 14.000000
    //     0xb54338: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5433c: ldr             x16, [x16, #0x1d8]
    // 0xb54340: r30 = Instance_Color
    //     0xb54340: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb54344: stp             lr, x16, [SP]
    // 0xb54348: mov             x1, x0
    // 0xb5434c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5434c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54350: ldr             x4, [x4, #0xaa0]
    // 0xb54354: r0 = copyWith()
    //     0xb54354: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb54358: stur            x0, [fp, #-0x10]
    // 0xb5435c: r0 = Text()
    //     0xb5435c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb54360: mov             x3, x0
    // 0xb54364: r0 = "Size"
    //     0xb54364: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xb54368: ldr             x0, [x0, #0x730]
    // 0xb5436c: stur            x3, [fp, #-0x20]
    // 0xb54370: StoreField: r3->field_b = r0
    //     0xb54370: stur            w0, [x3, #0xb]
    // 0xb54374: ldur            x0, [fp, #-0x10]
    // 0xb54378: StoreField: r3->field_13 = r0
    //     0xb54378: stur            w0, [x3, #0x13]
    // 0xb5437c: ldur            x0, [fp, #-8]
    // 0xb54380: LoadField: r1 = r0->field_b
    //     0xb54380: ldur            w1, [x0, #0xb]
    // 0xb54384: DecompressPointer r1
    //     0xb54384: add             x1, x1, HEAP, lsl #32
    // 0xb54388: cmp             w1, NULL
    // 0xb5438c: b.eq            #0xb54d68
    // 0xb54390: LoadField: r2 = r1->field_b
    //     0xb54390: ldur            w2, [x1, #0xb]
    // 0xb54394: DecompressPointer r2
    //     0xb54394: add             x2, x2, HEAP, lsl #32
    // 0xb54398: cmp             w2, NULL
    // 0xb5439c: b.ne            #0xb543a8
    // 0xb543a0: r6 = Null
    //     0xb543a0: mov             x6, NULL
    // 0xb543a4: b               #0xb543cc
    // 0xb543a8: LoadField: r1 = r2->field_f
    //     0xb543a8: ldur            w1, [x2, #0xf]
    // 0xb543ac: DecompressPointer r1
    //     0xb543ac: add             x1, x1, HEAP, lsl #32
    // 0xb543b0: cmp             w1, NULL
    // 0xb543b4: b.ne            #0xb543c0
    // 0xb543b8: r1 = Null
    //     0xb543b8: mov             x1, NULL
    // 0xb543bc: b               #0xb543c8
    // 0xb543c0: LoadField: r2 = r1->field_b
    //     0xb543c0: ldur            w2, [x1, #0xb]
    // 0xb543c4: mov             x1, x2
    // 0xb543c8: mov             x6, x1
    // 0xb543cc: ldur            x5, [fp, #-0x38]
    // 0xb543d0: ldur            x4, [fp, #-0x18]
    // 0xb543d4: mov             x2, x5
    // 0xb543d8: stur            x6, [fp, #-0x10]
    // 0xb543dc: r1 = Function '<anonymous closure>':.
    //     0xb543dc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56548] AnonymousClosure: (0xb55d78), in [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xb53bd4)
    //     0xb543e0: ldr             x1, [x1, #0x548]
    // 0xb543e4: r0 = AllocateClosure()
    //     0xb543e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb543e8: stur            x0, [fp, #-0x28]
    // 0xb543ec: r0 = ListView()
    //     0xb543ec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb543f0: stur            x0, [fp, #-0x30]
    // 0xb543f4: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb543f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb543f8: ldr             x16, [x16, #0x1c8]
    // 0xb543fc: r30 = true
    //     0xb543fc: add             lr, NULL, #0x20  ; true
    // 0xb54400: stp             lr, x16, [SP]
    // 0xb54404: mov             x1, x0
    // 0xb54408: ldur            x2, [fp, #-0x28]
    // 0xb5440c: ldur            x3, [fp, #-0x10]
    // 0xb54410: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xb54410: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb54414: ldr             x4, [x4, #0xd18]
    // 0xb54418: r0 = ListView.builder()
    //     0xb54418: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb5441c: r0 = Padding()
    //     0xb5441c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb54420: mov             x2, x0
    // 0xb54424: r0 = Instance_EdgeInsets
    //     0xb54424: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb54428: ldr             x0, [x0, #0xd48]
    // 0xb5442c: stur            x2, [fp, #-0x10]
    // 0xb54430: StoreField: r2->field_f = r0
    //     0xb54430: stur            w0, [x2, #0xf]
    // 0xb54434: ldur            x1, [fp, #-0x30]
    // 0xb54438: StoreField: r2->field_b = r1
    //     0xb54438: stur            w1, [x2, #0xb]
    // 0xb5443c: ldur            x3, [fp, #-0x38]
    // 0xb54440: LoadField: r1 = r3->field_13
    //     0xb54440: ldur            w1, [x3, #0x13]
    // 0xb54444: DecompressPointer r1
    //     0xb54444: add             x1, x1, HEAP, lsl #32
    // 0xb54448: r0 = of()
    //     0xb54448: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5444c: LoadField: r1 = r0->field_87
    //     0xb5444c: ldur            w1, [x0, #0x87]
    // 0xb54450: DecompressPointer r1
    //     0xb54450: add             x1, x1, HEAP, lsl #32
    // 0xb54454: LoadField: r0 = r1->field_7
    //     0xb54454: ldur            w0, [x1, #7]
    // 0xb54458: DecompressPointer r0
    //     0xb54458: add             x0, x0, HEAP, lsl #32
    // 0xb5445c: stur            x0, [fp, #-0x28]
    // 0xb54460: r1 = Instance_Color
    //     0xb54460: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb54464: d0 = 0.700000
    //     0xb54464: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb54468: ldr             d0, [x17, #0xf48]
    // 0xb5446c: r0 = withOpacity()
    //     0xb5446c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb54470: r16 = 16.000000
    //     0xb54470: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb54474: ldr             x16, [x16, #0x188]
    // 0xb54478: stp             x0, x16, [SP]
    // 0xb5447c: ldur            x1, [fp, #-0x28]
    // 0xb54480: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54480: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54484: ldr             x4, [x4, #0xaa0]
    // 0xb54488: r0 = copyWith()
    //     0xb54488: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5448c: r0 = Accordion()
    //     0xb5448c: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb54490: mov             x1, x0
    // 0xb54494: ldur            x0, [fp, #-0x20]
    // 0xb54498: StoreField: r1->field_b = r0
    //     0xb54498: stur            w0, [x1, #0xb]
    // 0xb5449c: ldur            x0, [fp, #-0x10]
    // 0xb544a0: StoreField: r1->field_13 = r0
    //     0xb544a0: stur            w0, [x1, #0x13]
    // 0xb544a4: r2 = false
    //     0xb544a4: add             x2, NULL, #0x30  ; false
    // 0xb544a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb544a8: stur            w2, [x1, #0x17]
    // 0xb544ac: d0 = 25.000000
    //     0xb544ac: fmov            d0, #25.00000000
    // 0xb544b0: StoreField: r1->field_1b = d0
    //     0xb544b0: stur            d0, [x1, #0x1b]
    // 0xb544b4: r3 = true
    //     0xb544b4: add             x3, NULL, #0x20  ; true
    // 0xb544b8: StoreField: r1->field_23 = r3
    //     0xb544b8: stur            w3, [x1, #0x23]
    // 0xb544bc: mov             x0, x1
    // 0xb544c0: ldur            x1, [fp, #-0x18]
    // 0xb544c4: ArrayStore: r1[6] = r0  ; List_4
    //     0xb544c4: add             x25, x1, #0x27
    //     0xb544c8: str             w0, [x25]
    //     0xb544cc: tbz             w0, #0, #0xb544e8
    //     0xb544d0: ldurb           w16, [x1, #-1]
    //     0xb544d4: ldurb           w17, [x0, #-1]
    //     0xb544d8: and             x16, x17, x16, lsr #2
    //     0xb544dc: tst             x16, HEAP, lsr #32
    //     0xb544e0: b.eq            #0xb544e8
    //     0xb544e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb544e8: ldur            x0, [fp, #-0x18]
    // 0xb544ec: r16 = Instance_SizedBox
    //     0xb544ec: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb544f0: ldr             x16, [x16, #0x568]
    // 0xb544f4: StoreField: r0->field_2b = r16
    //     0xb544f4: stur            w16, [x0, #0x2b]
    // 0xb544f8: r16 = Instance_Padding
    //     0xb544f8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xb544fc: ldr             x16, [x16, #0xd20]
    // 0xb54500: StoreField: r0->field_2f = r16
    //     0xb54500: stur            w16, [x0, #0x2f]
    // 0xb54504: r16 = Instance_SizedBox
    //     0xb54504: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb54508: ldr             x16, [x16, #0x568]
    // 0xb5450c: StoreField: r0->field_33 = r16
    //     0xb5450c: stur            w16, [x0, #0x33]
    // 0xb54510: ldur            x4, [fp, #-0x38]
    // 0xb54514: LoadField: r1 = r4->field_13
    //     0xb54514: ldur            w1, [x4, #0x13]
    // 0xb54518: DecompressPointer r1
    //     0xb54518: add             x1, x1, HEAP, lsl #32
    // 0xb5451c: r0 = of()
    //     0xb5451c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54520: LoadField: r1 = r0->field_87
    //     0xb54520: ldur            w1, [x0, #0x87]
    // 0xb54524: DecompressPointer r1
    //     0xb54524: add             x1, x1, HEAP, lsl #32
    // 0xb54528: LoadField: r0 = r1->field_7
    //     0xb54528: ldur            w0, [x1, #7]
    // 0xb5452c: DecompressPointer r0
    //     0xb5452c: add             x0, x0, HEAP, lsl #32
    // 0xb54530: r16 = 14.000000
    //     0xb54530: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb54534: ldr             x16, [x16, #0x1d8]
    // 0xb54538: r30 = Instance_Color
    //     0xb54538: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5453c: stp             lr, x16, [SP]
    // 0xb54540: mov             x1, x0
    // 0xb54544: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54544: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54548: ldr             x4, [x4, #0xaa0]
    // 0xb5454c: r0 = copyWith()
    //     0xb5454c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb54550: stur            x0, [fp, #-0x10]
    // 0xb54554: r0 = Text()
    //     0xb54554: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb54558: mov             x3, x0
    // 0xb5455c: r0 = "Colors"
    //     0xb5455c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d30] "Colors"
    //     0xb54560: ldr             x0, [x0, #0xd30]
    // 0xb54564: stur            x3, [fp, #-0x20]
    // 0xb54568: StoreField: r3->field_b = r0
    //     0xb54568: stur            w0, [x3, #0xb]
    // 0xb5456c: ldur            x0, [fp, #-0x10]
    // 0xb54570: StoreField: r3->field_13 = r0
    //     0xb54570: stur            w0, [x3, #0x13]
    // 0xb54574: ldur            x0, [fp, #-8]
    // 0xb54578: LoadField: r1 = r0->field_b
    //     0xb54578: ldur            w1, [x0, #0xb]
    // 0xb5457c: DecompressPointer r1
    //     0xb5457c: add             x1, x1, HEAP, lsl #32
    // 0xb54580: cmp             w1, NULL
    // 0xb54584: b.eq            #0xb54d6c
    // 0xb54588: LoadField: r2 = r1->field_b
    //     0xb54588: ldur            w2, [x1, #0xb]
    // 0xb5458c: DecompressPointer r2
    //     0xb5458c: add             x2, x2, HEAP, lsl #32
    // 0xb54590: cmp             w2, NULL
    // 0xb54594: b.ne            #0xb545a0
    // 0xb54598: r6 = Null
    //     0xb54598: mov             x6, NULL
    // 0xb5459c: b               #0xb545c4
    // 0xb545a0: LoadField: r1 = r2->field_b
    //     0xb545a0: ldur            w1, [x2, #0xb]
    // 0xb545a4: DecompressPointer r1
    //     0xb545a4: add             x1, x1, HEAP, lsl #32
    // 0xb545a8: cmp             w1, NULL
    // 0xb545ac: b.ne            #0xb545b8
    // 0xb545b0: r1 = Null
    //     0xb545b0: mov             x1, NULL
    // 0xb545b4: b               #0xb545c0
    // 0xb545b8: LoadField: r2 = r1->field_b
    //     0xb545b8: ldur            w2, [x1, #0xb]
    // 0xb545bc: mov             x1, x2
    // 0xb545c0: mov             x6, x1
    // 0xb545c4: ldur            x5, [fp, #-0x38]
    // 0xb545c8: ldur            x4, [fp, #-0x18]
    // 0xb545cc: mov             x2, x5
    // 0xb545d0: stur            x6, [fp, #-0x10]
    // 0xb545d4: r1 = Function '<anonymous closure>':.
    //     0xb545d4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56550] AnonymousClosure: (0xb559ec), in [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xb53bd4)
    //     0xb545d8: ldr             x1, [x1, #0x550]
    // 0xb545dc: r0 = AllocateClosure()
    //     0xb545dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb545e0: stur            x0, [fp, #-0x28]
    // 0xb545e4: r0 = ListView()
    //     0xb545e4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb545e8: stur            x0, [fp, #-0x30]
    // 0xb545ec: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb545ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb545f0: ldr             x16, [x16, #0x1c8]
    // 0xb545f4: r30 = true
    //     0xb545f4: add             lr, NULL, #0x20  ; true
    // 0xb545f8: stp             lr, x16, [SP]
    // 0xb545fc: mov             x1, x0
    // 0xb54600: ldur            x2, [fp, #-0x28]
    // 0xb54604: ldur            x3, [fp, #-0x10]
    // 0xb54608: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xb54608: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb5460c: ldr             x4, [x4, #0xd18]
    // 0xb54610: r0 = ListView.builder()
    //     0xb54610: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb54614: r0 = Padding()
    //     0xb54614: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb54618: mov             x2, x0
    // 0xb5461c: r0 = Instance_EdgeInsets
    //     0xb5461c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb54620: ldr             x0, [x0, #0xd48]
    // 0xb54624: stur            x2, [fp, #-0x10]
    // 0xb54628: StoreField: r2->field_f = r0
    //     0xb54628: stur            w0, [x2, #0xf]
    // 0xb5462c: ldur            x1, [fp, #-0x30]
    // 0xb54630: StoreField: r2->field_b = r1
    //     0xb54630: stur            w1, [x2, #0xb]
    // 0xb54634: ldur            x3, [fp, #-0x38]
    // 0xb54638: LoadField: r1 = r3->field_13
    //     0xb54638: ldur            w1, [x3, #0x13]
    // 0xb5463c: DecompressPointer r1
    //     0xb5463c: add             x1, x1, HEAP, lsl #32
    // 0xb54640: r0 = of()
    //     0xb54640: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54644: LoadField: r1 = r0->field_87
    //     0xb54644: ldur            w1, [x0, #0x87]
    // 0xb54648: DecompressPointer r1
    //     0xb54648: add             x1, x1, HEAP, lsl #32
    // 0xb5464c: LoadField: r0 = r1->field_7
    //     0xb5464c: ldur            w0, [x1, #7]
    // 0xb54650: DecompressPointer r0
    //     0xb54650: add             x0, x0, HEAP, lsl #32
    // 0xb54654: ldur            x2, [fp, #-0x38]
    // 0xb54658: stur            x0, [fp, #-0x28]
    // 0xb5465c: LoadField: r1 = r2->field_13
    //     0xb5465c: ldur            w1, [x2, #0x13]
    // 0xb54660: DecompressPointer r1
    //     0xb54660: add             x1, x1, HEAP, lsl #32
    // 0xb54664: r0 = of()
    //     0xb54664: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54668: LoadField: r1 = r0->field_5b
    //     0xb54668: ldur            w1, [x0, #0x5b]
    // 0xb5466c: DecompressPointer r1
    //     0xb5466c: add             x1, x1, HEAP, lsl #32
    // 0xb54670: r0 = LoadClassIdInstr(r1)
    //     0xb54670: ldur            x0, [x1, #-1]
    //     0xb54674: ubfx            x0, x0, #0xc, #0x14
    // 0xb54678: d0 = 0.700000
    //     0xb54678: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5467c: ldr             d0, [x17, #0xf48]
    // 0xb54680: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb54680: sub             lr, x0, #0xffa
    //     0xb54684: ldr             lr, [x21, lr, lsl #3]
    //     0xb54688: blr             lr
    // 0xb5468c: r16 = 16.000000
    //     0xb5468c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb54690: ldr             x16, [x16, #0x188]
    // 0xb54694: stp             x0, x16, [SP]
    // 0xb54698: ldur            x1, [fp, #-0x28]
    // 0xb5469c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5469c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb546a0: ldr             x4, [x4, #0xaa0]
    // 0xb546a4: r0 = copyWith()
    //     0xb546a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb546a8: r0 = Accordion()
    //     0xb546a8: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb546ac: mov             x1, x0
    // 0xb546b0: ldur            x0, [fp, #-0x20]
    // 0xb546b4: StoreField: r1->field_b = r0
    //     0xb546b4: stur            w0, [x1, #0xb]
    // 0xb546b8: ldur            x0, [fp, #-0x10]
    // 0xb546bc: StoreField: r1->field_13 = r0
    //     0xb546bc: stur            w0, [x1, #0x13]
    // 0xb546c0: r2 = false
    //     0xb546c0: add             x2, NULL, #0x30  ; false
    // 0xb546c4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb546c4: stur            w2, [x1, #0x17]
    // 0xb546c8: d0 = 25.000000
    //     0xb546c8: fmov            d0, #25.00000000
    // 0xb546cc: StoreField: r1->field_1b = d0
    //     0xb546cc: stur            d0, [x1, #0x1b]
    // 0xb546d0: r3 = true
    //     0xb546d0: add             x3, NULL, #0x20  ; true
    // 0xb546d4: StoreField: r1->field_23 = r3
    //     0xb546d4: stur            w3, [x1, #0x23]
    // 0xb546d8: mov             x0, x1
    // 0xb546dc: ldur            x1, [fp, #-0x18]
    // 0xb546e0: ArrayStore: r1[10] = r0  ; List_4
    //     0xb546e0: add             x25, x1, #0x37
    //     0xb546e4: str             w0, [x25]
    //     0xb546e8: tbz             w0, #0, #0xb54704
    //     0xb546ec: ldurb           w16, [x1, #-1]
    //     0xb546f0: ldurb           w17, [x0, #-1]
    //     0xb546f4: and             x16, x17, x16, lsr #2
    //     0xb546f8: tst             x16, HEAP, lsr #32
    //     0xb546fc: b.eq            #0xb54704
    //     0xb54700: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb54704: ldur            x0, [fp, #-0x18]
    // 0xb54708: r16 = Instance_SizedBox
    //     0xb54708: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb5470c: ldr             x16, [x16, #0x568]
    // 0xb54710: StoreField: r0->field_3b = r16
    //     0xb54710: stur            w16, [x0, #0x3b]
    // 0xb54714: r16 = Instance_Padding
    //     0xb54714: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xb54718: ldr             x16, [x16, #0xd20]
    // 0xb5471c: StoreField: r0->field_3f = r16
    //     0xb5471c: stur            w16, [x0, #0x3f]
    // 0xb54720: r16 = Instance_SizedBox
    //     0xb54720: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb54724: ldr             x16, [x16, #0x568]
    // 0xb54728: StoreField: r0->field_43 = r16
    //     0xb54728: stur            w16, [x0, #0x43]
    // 0xb5472c: ldur            x4, [fp, #-0x38]
    // 0xb54730: LoadField: r1 = r4->field_13
    //     0xb54730: ldur            w1, [x4, #0x13]
    // 0xb54734: DecompressPointer r1
    //     0xb54734: add             x1, x1, HEAP, lsl #32
    // 0xb54738: r0 = of()
    //     0xb54738: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5473c: LoadField: r1 = r0->field_87
    //     0xb5473c: ldur            w1, [x0, #0x87]
    // 0xb54740: DecompressPointer r1
    //     0xb54740: add             x1, x1, HEAP, lsl #32
    // 0xb54744: LoadField: r0 = r1->field_7
    //     0xb54744: ldur            w0, [x1, #7]
    // 0xb54748: DecompressPointer r0
    //     0xb54748: add             x0, x0, HEAP, lsl #32
    // 0xb5474c: r16 = 14.000000
    //     0xb5474c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb54750: ldr             x16, [x16, #0x1d8]
    // 0xb54754: r30 = Instance_Color
    //     0xb54754: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb54758: stp             lr, x16, [SP]
    // 0xb5475c: mov             x1, x0
    // 0xb54760: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54760: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54764: ldr             x4, [x4, #0xaa0]
    // 0xb54768: r0 = copyWith()
    //     0xb54768: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5476c: stur            x0, [fp, #-0x10]
    // 0xb54770: r0 = Text()
    //     0xb54770: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb54774: mov             x3, x0
    // 0xb54778: r0 = "Price Range"
    //     0xb54778: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d40] "Price Range"
    //     0xb5477c: ldr             x0, [x0, #0xd40]
    // 0xb54780: stur            x3, [fp, #-0x20]
    // 0xb54784: StoreField: r3->field_b = r0
    //     0xb54784: stur            w0, [x3, #0xb]
    // 0xb54788: ldur            x0, [fp, #-0x10]
    // 0xb5478c: StoreField: r3->field_13 = r0
    //     0xb5478c: stur            w0, [x3, #0x13]
    // 0xb54790: ldur            x0, [fp, #-8]
    // 0xb54794: LoadField: r1 = r0->field_b
    //     0xb54794: ldur            w1, [x0, #0xb]
    // 0xb54798: DecompressPointer r1
    //     0xb54798: add             x1, x1, HEAP, lsl #32
    // 0xb5479c: cmp             w1, NULL
    // 0xb547a0: b.eq            #0xb54d70
    // 0xb547a4: LoadField: r2 = r1->field_b
    //     0xb547a4: ldur            w2, [x1, #0xb]
    // 0xb547a8: DecompressPointer r2
    //     0xb547a8: add             x2, x2, HEAP, lsl #32
    // 0xb547ac: cmp             w2, NULL
    // 0xb547b0: b.ne            #0xb547bc
    // 0xb547b4: r7 = Null
    //     0xb547b4: mov             x7, NULL
    // 0xb547b8: b               #0xb547e0
    // 0xb547bc: LoadField: r1 = r2->field_13
    //     0xb547bc: ldur            w1, [x2, #0x13]
    // 0xb547c0: DecompressPointer r1
    //     0xb547c0: add             x1, x1, HEAP, lsl #32
    // 0xb547c4: cmp             w1, NULL
    // 0xb547c8: b.ne            #0xb547d4
    // 0xb547cc: r1 = Null
    //     0xb547cc: mov             x1, NULL
    // 0xb547d0: b               #0xb547dc
    // 0xb547d4: LoadField: r2 = r1->field_b
    //     0xb547d4: ldur            w2, [x1, #0xb]
    // 0xb547d8: mov             x1, x2
    // 0xb547dc: mov             x7, x1
    // 0xb547e0: ldur            x5, [fp, #-0x38]
    // 0xb547e4: ldur            x6, [fp, #-0x40]
    // 0xb547e8: ldur            x4, [fp, #-0x18]
    // 0xb547ec: mov             x2, x5
    // 0xb547f0: stur            x7, [fp, #-0x10]
    // 0xb547f4: r1 = Function '<anonymous closure>':.
    //     0xb547f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56558] AnonymousClosure: (0xb54d8c), in [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xb53bd4)
    //     0xb547f8: ldr             x1, [x1, #0x558]
    // 0xb547fc: r0 = AllocateClosure()
    //     0xb547fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb54800: stur            x0, [fp, #-0x28]
    // 0xb54804: r0 = ListView()
    //     0xb54804: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb54808: stur            x0, [fp, #-0x30]
    // 0xb5480c: r16 = true
    //     0xb5480c: add             x16, NULL, #0x20  ; true
    // 0xb54810: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb54810: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb54814: ldr             lr, [lr, #0x1c8]
    // 0xb54818: stp             lr, x16, [SP]
    // 0xb5481c: mov             x1, x0
    // 0xb54820: ldur            x2, [fp, #-0x28]
    // 0xb54824: ldur            x3, [fp, #-0x10]
    // 0xb54828: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb54828: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb5482c: ldr             x4, [x4, #8]
    // 0xb54830: r0 = ListView.builder()
    //     0xb54830: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb54834: r0 = Padding()
    //     0xb54834: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb54838: mov             x2, x0
    // 0xb5483c: r0 = Instance_EdgeInsets
    //     0xb5483c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb54840: ldr             x0, [x0, #0xd48]
    // 0xb54844: stur            x2, [fp, #-0x10]
    // 0xb54848: StoreField: r2->field_f = r0
    //     0xb54848: stur            w0, [x2, #0xf]
    // 0xb5484c: ldur            x0, [fp, #-0x30]
    // 0xb54850: StoreField: r2->field_b = r0
    //     0xb54850: stur            w0, [x2, #0xb]
    // 0xb54854: ldur            x0, [fp, #-0x38]
    // 0xb54858: LoadField: r1 = r0->field_13
    //     0xb54858: ldur            w1, [x0, #0x13]
    // 0xb5485c: DecompressPointer r1
    //     0xb5485c: add             x1, x1, HEAP, lsl #32
    // 0xb54860: r0 = of()
    //     0xb54860: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54864: LoadField: r1 = r0->field_87
    //     0xb54864: ldur            w1, [x0, #0x87]
    // 0xb54868: DecompressPointer r1
    //     0xb54868: add             x1, x1, HEAP, lsl #32
    // 0xb5486c: LoadField: r0 = r1->field_27
    //     0xb5486c: ldur            w0, [x1, #0x27]
    // 0xb54870: DecompressPointer r0
    //     0xb54870: add             x0, x0, HEAP, lsl #32
    // 0xb54874: ldur            x2, [fp, #-0x38]
    // 0xb54878: stur            x0, [fp, #-0x28]
    // 0xb5487c: LoadField: r1 = r2->field_13
    //     0xb5487c: ldur            w1, [x2, #0x13]
    // 0xb54880: DecompressPointer r1
    //     0xb54880: add             x1, x1, HEAP, lsl #32
    // 0xb54884: r0 = of()
    //     0xb54884: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54888: LoadField: r1 = r0->field_5b
    //     0xb54888: ldur            w1, [x0, #0x5b]
    // 0xb5488c: DecompressPointer r1
    //     0xb5488c: add             x1, x1, HEAP, lsl #32
    // 0xb54890: r0 = LoadClassIdInstr(r1)
    //     0xb54890: ldur            x0, [x1, #-1]
    //     0xb54894: ubfx            x0, x0, #0xc, #0x14
    // 0xb54898: d0 = 0.700000
    //     0xb54898: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5489c: ldr             d0, [x17, #0xf48]
    // 0xb548a0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb548a0: sub             lr, x0, #0xffa
    //     0xb548a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb548a8: blr             lr
    // 0xb548ac: r16 = 16.000000
    //     0xb548ac: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb548b0: ldr             x16, [x16, #0x188]
    // 0xb548b4: stp             x0, x16, [SP]
    // 0xb548b8: ldur            x1, [fp, #-0x28]
    // 0xb548bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb548bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb548c0: ldr             x4, [x4, #0xaa0]
    // 0xb548c4: r0 = copyWith()
    //     0xb548c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb548c8: r0 = Accordion()
    //     0xb548c8: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb548cc: mov             x1, x0
    // 0xb548d0: ldur            x0, [fp, #-0x20]
    // 0xb548d4: StoreField: r1->field_b = r0
    //     0xb548d4: stur            w0, [x1, #0xb]
    // 0xb548d8: ldur            x0, [fp, #-0x10]
    // 0xb548dc: StoreField: r1->field_13 = r0
    //     0xb548dc: stur            w0, [x1, #0x13]
    // 0xb548e0: r2 = false
    //     0xb548e0: add             x2, NULL, #0x30  ; false
    // 0xb548e4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb548e4: stur            w2, [x1, #0x17]
    // 0xb548e8: d0 = 25.000000
    //     0xb548e8: fmov            d0, #25.00000000
    // 0xb548ec: StoreField: r1->field_1b = d0
    //     0xb548ec: stur            d0, [x1, #0x1b]
    // 0xb548f0: r3 = true
    //     0xb548f0: add             x3, NULL, #0x20  ; true
    // 0xb548f4: StoreField: r1->field_23 = r3
    //     0xb548f4: stur            w3, [x1, #0x23]
    // 0xb548f8: mov             x0, x1
    // 0xb548fc: ldur            x1, [fp, #-0x18]
    // 0xb54900: ArrayStore: r1[14] = r0  ; List_4
    //     0xb54900: add             x25, x1, #0x47
    //     0xb54904: str             w0, [x25]
    //     0xb54908: tbz             w0, #0, #0xb54924
    //     0xb5490c: ldurb           w16, [x1, #-1]
    //     0xb54910: ldurb           w17, [x0, #-1]
    //     0xb54914: and             x16, x17, x16, lsr #2
    //     0xb54918: tst             x16, HEAP, lsr #32
    //     0xb5491c: b.eq            #0xb54924
    //     0xb54920: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb54924: ldur            x0, [fp, #-0x18]
    // 0xb54928: r16 = Instance_SizedBox
    //     0xb54928: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb5492c: ldr             x16, [x16, #0x568]
    // 0xb54930: StoreField: r0->field_4b = r16
    //     0xb54930: stur            w16, [x0, #0x4b]
    // 0xb54934: r16 = Instance_Padding
    //     0xb54934: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xb54938: ldr             x16, [x16, #0xd20]
    // 0xb5493c: StoreField: r0->field_4f = r16
    //     0xb5493c: stur            w16, [x0, #0x4f]
    // 0xb54940: r16 = Instance_SizedBox
    //     0xb54940: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb54944: ldr             x16, [x16, #0x568]
    // 0xb54948: StoreField: r0->field_53 = r16
    //     0xb54948: stur            w16, [x0, #0x53]
    // 0xb5494c: r1 = <Widget>
    //     0xb5494c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb54950: r0 = AllocateGrowableArray()
    //     0xb54950: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb54954: mov             x1, x0
    // 0xb54958: ldur            x0, [fp, #-0x18]
    // 0xb5495c: stur            x1, [fp, #-0x10]
    // 0xb54960: StoreField: r1->field_f = r0
    //     0xb54960: stur            w0, [x1, #0xf]
    // 0xb54964: r0 = 36
    //     0xb54964: movz            x0, #0x24
    // 0xb54968: StoreField: r1->field_b = r0
    //     0xb54968: stur            w0, [x1, #0xb]
    // 0xb5496c: r0 = Column()
    //     0xb5496c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb54970: mov             x1, x0
    // 0xb54974: r0 = Instance_Axis
    //     0xb54974: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb54978: stur            x1, [fp, #-0x18]
    // 0xb5497c: StoreField: r1->field_f = r0
    //     0xb5497c: stur            w0, [x1, #0xf]
    // 0xb54980: r2 = Instance_MainAxisAlignment
    //     0xb54980: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb54984: ldr             x2, [x2, #0xa08]
    // 0xb54988: StoreField: r1->field_13 = r2
    //     0xb54988: stur            w2, [x1, #0x13]
    // 0xb5498c: r2 = Instance_MainAxisSize
    //     0xb5498c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb54990: ldr             x2, [x2, #0xa10]
    // 0xb54994: ArrayStore: r1[0] = r2  ; List_4
    //     0xb54994: stur            w2, [x1, #0x17]
    // 0xb54998: r2 = Instance_CrossAxisAlignment
    //     0xb54998: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5499c: ldr             x2, [x2, #0xa18]
    // 0xb549a0: StoreField: r1->field_1b = r2
    //     0xb549a0: stur            w2, [x1, #0x1b]
    // 0xb549a4: r2 = Instance_VerticalDirection
    //     0xb549a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb549a8: ldr             x2, [x2, #0xa20]
    // 0xb549ac: StoreField: r1->field_23 = r2
    //     0xb549ac: stur            w2, [x1, #0x23]
    // 0xb549b0: r2 = Instance_Clip
    //     0xb549b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb549b4: ldr             x2, [x2, #0x38]
    // 0xb549b8: StoreField: r1->field_2b = r2
    //     0xb549b8: stur            w2, [x1, #0x2b]
    // 0xb549bc: StoreField: r1->field_2f = rZR
    //     0xb549bc: stur            xzr, [x1, #0x2f]
    // 0xb549c0: ldur            x2, [fp, #-0x10]
    // 0xb549c4: StoreField: r1->field_b = r2
    //     0xb549c4: stur            w2, [x1, #0xb]
    // 0xb549c8: r0 = SingleChildScrollView()
    //     0xb549c8: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb549cc: mov             x1, x0
    // 0xb549d0: r0 = Instance_Axis
    //     0xb549d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb549d4: stur            x1, [fp, #-0x10]
    // 0xb549d8: StoreField: r1->field_b = r0
    //     0xb549d8: stur            w0, [x1, #0xb]
    // 0xb549dc: r0 = false
    //     0xb549dc: add             x0, NULL, #0x30  ; false
    // 0xb549e0: StoreField: r1->field_f = r0
    //     0xb549e0: stur            w0, [x1, #0xf]
    // 0xb549e4: ldur            x2, [fp, #-0x18]
    // 0xb549e8: StoreField: r1->field_23 = r2
    //     0xb549e8: stur            w2, [x1, #0x23]
    // 0xb549ec: r2 = Instance_DragStartBehavior
    //     0xb549ec: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb549f0: StoreField: r1->field_27 = r2
    //     0xb549f0: stur            w2, [x1, #0x27]
    // 0xb549f4: r2 = Instance_Clip
    //     0xb549f4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb549f8: ldr             x2, [x2, #0x7e0]
    // 0xb549fc: StoreField: r1->field_2b = r2
    //     0xb549fc: stur            w2, [x1, #0x2b]
    // 0xb54a00: r2 = Instance_HitTestBehavior
    //     0xb54a00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb54a04: ldr             x2, [x2, #0x288]
    // 0xb54a08: StoreField: r1->field_2f = r2
    //     0xb54a08: stur            w2, [x1, #0x2f]
    // 0xb54a0c: r0 = SafeArea()
    //     0xb54a0c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb54a10: mov             x2, x0
    // 0xb54a14: r0 = true
    //     0xb54a14: add             x0, NULL, #0x20  ; true
    // 0xb54a18: stur            x2, [fp, #-0x18]
    // 0xb54a1c: StoreField: r2->field_b = r0
    //     0xb54a1c: stur            w0, [x2, #0xb]
    // 0xb54a20: StoreField: r2->field_f = r0
    //     0xb54a20: stur            w0, [x2, #0xf]
    // 0xb54a24: StoreField: r2->field_13 = r0
    //     0xb54a24: stur            w0, [x2, #0x13]
    // 0xb54a28: ArrayStore: r2[0] = r0  ; List_4
    //     0xb54a28: stur            w0, [x2, #0x17]
    // 0xb54a2c: r1 = Instance_EdgeInsets
    //     0xb54a2c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb54a30: StoreField: r2->field_1b = r1
    //     0xb54a30: stur            w1, [x2, #0x1b]
    // 0xb54a34: r3 = false
    //     0xb54a34: add             x3, NULL, #0x30  ; false
    // 0xb54a38: StoreField: r2->field_1f = r3
    //     0xb54a38: stur            w3, [x2, #0x1f]
    // 0xb54a3c: ldur            x1, [fp, #-0x10]
    // 0xb54a40: StoreField: r2->field_23 = r1
    //     0xb54a40: stur            w1, [x2, #0x23]
    // 0xb54a44: ldur            x1, [fp, #-8]
    // 0xb54a48: LoadField: r4 = r1->field_13
    //     0xb54a48: ldur            w4, [x1, #0x13]
    // 0xb54a4c: DecompressPointer r4
    //     0xb54a4c: add             x4, x4, HEAP, lsl #32
    // 0xb54a50: LoadField: r1 = r4->field_b
    //     0xb54a50: ldur            w1, [x4, #0xb]
    // 0xb54a54: cbnz            w1, #0xb54a60
    // 0xb54a58: r4 = false
    //     0xb54a58: add             x4, NULL, #0x30  ; false
    // 0xb54a5c: b               #0xb54a64
    // 0xb54a60: r4 = true
    //     0xb54a60: add             x4, NULL, #0x20  ; true
    // 0xb54a64: ldur            x5, [fp, #-0x38]
    // 0xb54a68: stur            x4, [fp, #-8]
    // 0xb54a6c: LoadField: r1 = r5->field_13
    //     0xb54a6c: ldur            w1, [x5, #0x13]
    // 0xb54a70: DecompressPointer r1
    //     0xb54a70: add             x1, x1, HEAP, lsl #32
    // 0xb54a74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb54a74: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb54a78: r0 = _of()
    //     0xb54a78: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb54a7c: LoadField: r1 = r0->field_7
    //     0xb54a7c: ldur            w1, [x0, #7]
    // 0xb54a80: DecompressPointer r1
    //     0xb54a80: add             x1, x1, HEAP, lsl #32
    // 0xb54a84: LoadField: d0 = r1->field_7
    //     0xb54a84: ldur            d0, [x1, #7]
    // 0xb54a88: stur            d0, [fp, #-0x48]
    // 0xb54a8c: r16 = <EdgeInsets>
    //     0xb54a8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb54a90: ldr             x16, [x16, #0xda0]
    // 0xb54a94: r30 = Instance_EdgeInsets
    //     0xb54a94: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb54a98: ldr             lr, [lr, #0x1f0]
    // 0xb54a9c: stp             lr, x16, [SP]
    // 0xb54aa0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb54aa0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb54aa4: r0 = all()
    //     0xb54aa4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb54aa8: ldur            x2, [fp, #-0x38]
    // 0xb54aac: stur            x0, [fp, #-0x10]
    // 0xb54ab0: LoadField: r1 = r2->field_13
    //     0xb54ab0: ldur            w1, [x2, #0x13]
    // 0xb54ab4: DecompressPointer r1
    //     0xb54ab4: add             x1, x1, HEAP, lsl #32
    // 0xb54ab8: r0 = of()
    //     0xb54ab8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54abc: LoadField: r1 = r0->field_5b
    //     0xb54abc: ldur            w1, [x0, #0x5b]
    // 0xb54ac0: DecompressPointer r1
    //     0xb54ac0: add             x1, x1, HEAP, lsl #32
    // 0xb54ac4: r16 = <Color>
    //     0xb54ac4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb54ac8: ldr             x16, [x16, #0xf80]
    // 0xb54acc: stp             x1, x16, [SP]
    // 0xb54ad0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb54ad0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb54ad4: r0 = all()
    //     0xb54ad4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb54ad8: stur            x0, [fp, #-0x20]
    // 0xb54adc: r0 = Radius()
    //     0xb54adc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb54ae0: d0 = 30.000000
    //     0xb54ae0: fmov            d0, #30.00000000
    // 0xb54ae4: stur            x0, [fp, #-0x28]
    // 0xb54ae8: StoreField: r0->field_7 = d0
    //     0xb54ae8: stur            d0, [x0, #7]
    // 0xb54aec: StoreField: r0->field_f = d0
    //     0xb54aec: stur            d0, [x0, #0xf]
    // 0xb54af0: r0 = BorderRadius()
    //     0xb54af0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb54af4: mov             x1, x0
    // 0xb54af8: ldur            x0, [fp, #-0x28]
    // 0xb54afc: stur            x1, [fp, #-0x30]
    // 0xb54b00: StoreField: r1->field_7 = r0
    //     0xb54b00: stur            w0, [x1, #7]
    // 0xb54b04: StoreField: r1->field_b = r0
    //     0xb54b04: stur            w0, [x1, #0xb]
    // 0xb54b08: StoreField: r1->field_f = r0
    //     0xb54b08: stur            w0, [x1, #0xf]
    // 0xb54b0c: StoreField: r1->field_13 = r0
    //     0xb54b0c: stur            w0, [x1, #0x13]
    // 0xb54b10: r0 = RoundedRectangleBorder()
    //     0xb54b10: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb54b14: mov             x1, x0
    // 0xb54b18: ldur            x0, [fp, #-0x30]
    // 0xb54b1c: StoreField: r1->field_b = r0
    //     0xb54b1c: stur            w0, [x1, #0xb]
    // 0xb54b20: r0 = Instance_BorderSide
    //     0xb54b20: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb54b24: ldr             x0, [x0, #0xe20]
    // 0xb54b28: StoreField: r1->field_7 = r0
    //     0xb54b28: stur            w0, [x1, #7]
    // 0xb54b2c: r16 = <RoundedRectangleBorder>
    //     0xb54b2c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb54b30: ldr             x16, [x16, #0xf78]
    // 0xb54b34: stp             x1, x16, [SP]
    // 0xb54b38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb54b38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb54b3c: r0 = all()
    //     0xb54b3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb54b40: stur            x0, [fp, #-0x28]
    // 0xb54b44: r0 = ButtonStyle()
    //     0xb54b44: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb54b48: mov             x1, x0
    // 0xb54b4c: ldur            x0, [fp, #-0x20]
    // 0xb54b50: stur            x1, [fp, #-0x30]
    // 0xb54b54: StoreField: r1->field_b = r0
    //     0xb54b54: stur            w0, [x1, #0xb]
    // 0xb54b58: ldur            x0, [fp, #-0x10]
    // 0xb54b5c: StoreField: r1->field_23 = r0
    //     0xb54b5c: stur            w0, [x1, #0x23]
    // 0xb54b60: ldur            x0, [fp, #-0x28]
    // 0xb54b64: StoreField: r1->field_43 = r0
    //     0xb54b64: stur            w0, [x1, #0x43]
    // 0xb54b68: r0 = TextButtonThemeData()
    //     0xb54b68: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb54b6c: mov             x2, x0
    // 0xb54b70: ldur            x0, [fp, #-0x30]
    // 0xb54b74: stur            x2, [fp, #-0x10]
    // 0xb54b78: StoreField: r2->field_7 = r0
    //     0xb54b78: stur            w0, [x2, #7]
    // 0xb54b7c: r1 = "show result"
    //     0xb54b7c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56560] "show result"
    //     0xb54b80: ldr             x1, [x1, #0x560]
    // 0xb54b84: r0 = capitalizeFirstWord()
    //     0xb54b84: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb54b88: ldur            x2, [fp, #-0x38]
    // 0xb54b8c: stur            x0, [fp, #-0x20]
    // 0xb54b90: LoadField: r1 = r2->field_13
    //     0xb54b90: ldur            w1, [x2, #0x13]
    // 0xb54b94: DecompressPointer r1
    //     0xb54b94: add             x1, x1, HEAP, lsl #32
    // 0xb54b98: r0 = of()
    //     0xb54b98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54b9c: LoadField: r1 = r0->field_87
    //     0xb54b9c: ldur            w1, [x0, #0x87]
    // 0xb54ba0: DecompressPointer r1
    //     0xb54ba0: add             x1, x1, HEAP, lsl #32
    // 0xb54ba4: LoadField: r0 = r1->field_7
    //     0xb54ba4: ldur            w0, [x1, #7]
    // 0xb54ba8: DecompressPointer r0
    //     0xb54ba8: add             x0, x0, HEAP, lsl #32
    // 0xb54bac: r16 = 16.000000
    //     0xb54bac: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb54bb0: ldr             x16, [x16, #0x188]
    // 0xb54bb4: r30 = Instance_Color
    //     0xb54bb4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb54bb8: stp             lr, x16, [SP]
    // 0xb54bbc: mov             x1, x0
    // 0xb54bc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54bc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54bc4: ldr             x4, [x4, #0xaa0]
    // 0xb54bc8: r0 = copyWith()
    //     0xb54bc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb54bcc: stur            x0, [fp, #-0x28]
    // 0xb54bd0: r0 = Text()
    //     0xb54bd0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb54bd4: mov             x3, x0
    // 0xb54bd8: ldur            x0, [fp, #-0x20]
    // 0xb54bdc: stur            x3, [fp, #-0x30]
    // 0xb54be0: StoreField: r3->field_b = r0
    //     0xb54be0: stur            w0, [x3, #0xb]
    // 0xb54be4: ldur            x0, [fp, #-0x28]
    // 0xb54be8: StoreField: r3->field_13 = r0
    //     0xb54be8: stur            w0, [x3, #0x13]
    // 0xb54bec: ldur            x2, [fp, #-0x38]
    // 0xb54bf0: r1 = Function '<anonymous closure>':.
    //     0xb54bf0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56568] AnonymousClosure: (0xa2da14), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xb54bf4: ldr             x1, [x1, #0x568]
    // 0xb54bf8: r0 = AllocateClosure()
    //     0xb54bf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb54bfc: stur            x0, [fp, #-0x20]
    // 0xb54c00: r0 = TextButton()
    //     0xb54c00: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb54c04: mov             x1, x0
    // 0xb54c08: ldur            x0, [fp, #-0x20]
    // 0xb54c0c: stur            x1, [fp, #-0x28]
    // 0xb54c10: StoreField: r1->field_b = r0
    //     0xb54c10: stur            w0, [x1, #0xb]
    // 0xb54c14: r0 = false
    //     0xb54c14: add             x0, NULL, #0x30  ; false
    // 0xb54c18: StoreField: r1->field_27 = r0
    //     0xb54c18: stur            w0, [x1, #0x27]
    // 0xb54c1c: r2 = true
    //     0xb54c1c: add             x2, NULL, #0x20  ; true
    // 0xb54c20: StoreField: r1->field_2f = r2
    //     0xb54c20: stur            w2, [x1, #0x2f]
    // 0xb54c24: ldur            x3, [fp, #-0x30]
    // 0xb54c28: StoreField: r1->field_37 = r3
    //     0xb54c28: stur            w3, [x1, #0x37]
    // 0xb54c2c: r0 = TextButtonTheme()
    //     0xb54c2c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb54c30: mov             x1, x0
    // 0xb54c34: ldur            x0, [fp, #-0x10]
    // 0xb54c38: stur            x1, [fp, #-0x20]
    // 0xb54c3c: StoreField: r1->field_f = r0
    //     0xb54c3c: stur            w0, [x1, #0xf]
    // 0xb54c40: ldur            x0, [fp, #-0x28]
    // 0xb54c44: StoreField: r1->field_b = r0
    //     0xb54c44: stur            w0, [x1, #0xb]
    // 0xb54c48: r0 = Padding()
    //     0xb54c48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb54c4c: mov             x1, x0
    // 0xb54c50: r0 = Instance_EdgeInsets
    //     0xb54c50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb54c54: ldr             x0, [x0, #0x1f0]
    // 0xb54c58: stur            x1, [fp, #-0x28]
    // 0xb54c5c: StoreField: r1->field_f = r0
    //     0xb54c5c: stur            w0, [x1, #0xf]
    // 0xb54c60: ldur            x0, [fp, #-0x20]
    // 0xb54c64: StoreField: r1->field_b = r0
    //     0xb54c64: stur            w0, [x1, #0xb]
    // 0xb54c68: ldur            d0, [fp, #-0x48]
    // 0xb54c6c: r0 = inline_Allocate_Double()
    //     0xb54c6c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb54c70: add             x0, x0, #0x10
    //     0xb54c74: cmp             x2, x0
    //     0xb54c78: b.ls            #0xb54d74
    //     0xb54c7c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb54c80: sub             x0, x0, #0xf
    //     0xb54c84: movz            x2, #0xe15c
    //     0xb54c88: movk            x2, #0x3, lsl #16
    //     0xb54c8c: stur            x2, [x0, #-1]
    // 0xb54c90: StoreField: r0->field_7 = d0
    //     0xb54c90: stur            d0, [x0, #7]
    // 0xb54c94: stur            x0, [fp, #-0x10]
    // 0xb54c98: r0 = SizedBox()
    //     0xb54c98: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb54c9c: mov             x1, x0
    // 0xb54ca0: ldur            x0, [fp, #-0x10]
    // 0xb54ca4: stur            x1, [fp, #-0x20]
    // 0xb54ca8: StoreField: r1->field_f = r0
    //     0xb54ca8: stur            w0, [x1, #0xf]
    // 0xb54cac: ldur            x0, [fp, #-0x28]
    // 0xb54cb0: StoreField: r1->field_b = r0
    //     0xb54cb0: stur            w0, [x1, #0xb]
    // 0xb54cb4: r0 = Visibility()
    //     0xb54cb4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb54cb8: mov             x1, x0
    // 0xb54cbc: ldur            x0, [fp, #-0x20]
    // 0xb54cc0: stur            x1, [fp, #-0x10]
    // 0xb54cc4: StoreField: r1->field_b = r0
    //     0xb54cc4: stur            w0, [x1, #0xb]
    // 0xb54cc8: r0 = Instance_SizedBox
    //     0xb54cc8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb54ccc: StoreField: r1->field_f = r0
    //     0xb54ccc: stur            w0, [x1, #0xf]
    // 0xb54cd0: ldur            x0, [fp, #-8]
    // 0xb54cd4: StoreField: r1->field_13 = r0
    //     0xb54cd4: stur            w0, [x1, #0x13]
    // 0xb54cd8: r0 = false
    //     0xb54cd8: add             x0, NULL, #0x30  ; false
    // 0xb54cdc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb54cdc: stur            w0, [x1, #0x17]
    // 0xb54ce0: StoreField: r1->field_1b = r0
    //     0xb54ce0: stur            w0, [x1, #0x1b]
    // 0xb54ce4: StoreField: r1->field_1f = r0
    //     0xb54ce4: stur            w0, [x1, #0x1f]
    // 0xb54ce8: StoreField: r1->field_23 = r0
    //     0xb54ce8: stur            w0, [x1, #0x23]
    // 0xb54cec: StoreField: r1->field_27 = r0
    //     0xb54cec: stur            w0, [x1, #0x27]
    // 0xb54cf0: StoreField: r1->field_2b = r0
    //     0xb54cf0: stur            w0, [x1, #0x2b]
    // 0xb54cf4: r0 = Scaffold()
    //     0xb54cf4: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb54cf8: mov             x2, x0
    // 0xb54cfc: ldur            x0, [fp, #-0x18]
    // 0xb54d00: stur            x2, [fp, #-8]
    // 0xb54d04: ArrayStore: r2[0] = r0  ; List_4
    //     0xb54d04: stur            w0, [x2, #0x17]
    // 0xb54d08: ldur            x0, [fp, #-0x10]
    // 0xb54d0c: StoreField: r2->field_1b = r0
    //     0xb54d0c: stur            w0, [x2, #0x1b]
    // 0xb54d10: r0 = Instance__CenterFloatFabLocation
    //     0xb54d10: add             x0, PP, #0x56, lsl #12  ; [pp+0x56570] Obj!_CenterFloatFabLocation@d5ade1
    //     0xb54d14: ldr             x0, [x0, #0x570]
    // 0xb54d18: StoreField: r2->field_1f = r0
    //     0xb54d18: stur            w0, [x2, #0x1f]
    // 0xb54d1c: r0 = true
    //     0xb54d1c: add             x0, NULL, #0x20  ; true
    // 0xb54d20: StoreField: r2->field_43 = r0
    //     0xb54d20: stur            w0, [x2, #0x43]
    // 0xb54d24: r1 = false
    //     0xb54d24: add             x1, NULL, #0x30  ; false
    // 0xb54d28: StoreField: r2->field_b = r1
    //     0xb54d28: stur            w1, [x2, #0xb]
    // 0xb54d2c: StoreField: r2->field_f = r1
    //     0xb54d2c: stur            w1, [x2, #0xf]
    // 0xb54d30: r1 = <SystemUiOverlayStyle>
    //     0xb54d30: ldr             x1, [PP, #0x2848]  ; [pp+0x2848] TypeArguments: <SystemUiOverlayStyle>
    // 0xb54d34: r0 = AnnotatedRegion()
    //     0xb54d34: bl              #0xa2da08  ; AllocateAnnotatedRegionStub -> AnnotatedRegion<X0> (size=0x1c)
    // 0xb54d38: ldur            x1, [fp, #-0x40]
    // 0xb54d3c: StoreField: r0->field_13 = r1
    //     0xb54d3c: stur            w1, [x0, #0x13]
    // 0xb54d40: r1 = true
    //     0xb54d40: add             x1, NULL, #0x20  ; true
    // 0xb54d44: ArrayStore: r0[0] = r1  ; List_4
    //     0xb54d44: stur            w1, [x0, #0x17]
    // 0xb54d48: ldur            x1, [fp, #-8]
    // 0xb54d4c: StoreField: r0->field_b = r1
    //     0xb54d4c: stur            w1, [x0, #0xb]
    // 0xb54d50: LeaveFrame
    //     0xb54d50: mov             SP, fp
    //     0xb54d54: ldp             fp, lr, [SP], #0x10
    // 0xb54d58: ret
    //     0xb54d58: ret             
    // 0xb54d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb54d5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb54d60: b               #0xb53bfc
    // 0xb54d64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb54d64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb54d68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb54d68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb54d6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb54d6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb54d70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb54d70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb54d74: SaveReg d0
    //     0xb54d74: str             q0, [SP, #-0x10]!
    // 0xb54d78: SaveReg r1
    //     0xb54d78: str             x1, [SP, #-8]!
    // 0xb54d7c: r0 = AllocateDouble()
    //     0xb54d7c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb54d80: RestoreReg r1
    //     0xb54d80: ldr             x1, [SP], #8
    // 0xb54d84: RestoreReg d0
    //     0xb54d84: ldr             q0, [SP], #0x10
    // 0xb54d88: b               #0xb54c90
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb54d8c, size: 0x46c
    // 0xb54d8c: EnterFrame
    //     0xb54d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb54d90: mov             fp, SP
    // 0xb54d94: AllocStack(0x40)
    //     0xb54d94: sub             SP, SP, #0x40
    // 0xb54d98: SetupParameters()
    //     0xb54d98: ldr             x0, [fp, #0x20]
    //     0xb54d9c: ldur            w4, [x0, #0x17]
    //     0xb54da0: add             x4, x4, HEAP, lsl #32
    //     0xb54da4: stur            x4, [fp, #-0x10]
    // 0xb54da8: CheckStackOverflow
    //     0xb54da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb54dac: cmp             SP, x16
    //     0xb54db0: b.ls            #0xb551d4
    // 0xb54db4: LoadField: r1 = r4->field_f
    //     0xb54db4: ldur            w1, [x4, #0xf]
    // 0xb54db8: DecompressPointer r1
    //     0xb54db8: add             x1, x1, HEAP, lsl #32
    // 0xb54dbc: LoadField: r0 = r1->field_b
    //     0xb54dbc: ldur            w0, [x1, #0xb]
    // 0xb54dc0: DecompressPointer r0
    //     0xb54dc0: add             x0, x0, HEAP, lsl #32
    // 0xb54dc4: cmp             w0, NULL
    // 0xb54dc8: b.eq            #0xb551dc
    // 0xb54dcc: LoadField: r2 = r0->field_b
    //     0xb54dcc: ldur            w2, [x0, #0xb]
    // 0xb54dd0: DecompressPointer r2
    //     0xb54dd0: add             x2, x2, HEAP, lsl #32
    // 0xb54dd4: ldr             x0, [fp, #0x10]
    // 0xb54dd8: r6 = LoadInt32Instr(r0)
    //     0xb54dd8: sbfx            x6, x0, #1, #0x1f
    //     0xb54ddc: tbz             w0, #0, #0xb54de4
    //     0xb54de0: ldur            x6, [x0, #7]
    // 0xb54de4: mov             x5, x6
    // 0xb54de8: stur            x6, [fp, #-8]
    // 0xb54dec: r3 = Instance_ItemFilterType
    //     0xb54dec: add             x3, PP, #0x56, lsl #12  ; [pp+0x56578] Obj!ItemFilterType@d75321
    //     0xb54df0: ldr             x3, [x3, #0x578]
    // 0xb54df4: r0 = _productBuildItem()
    //     0xb54df4: bl              #0xb551f8  ; [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xb54df8: mov             x3, x0
    // 0xb54dfc: ldur            x2, [fp, #-0x10]
    // 0xb54e00: stur            x3, [fp, #-0x18]
    // 0xb54e04: LoadField: r0 = r2->field_f
    //     0xb54e04: ldur            w0, [x2, #0xf]
    // 0xb54e08: DecompressPointer r0
    //     0xb54e08: add             x0, x0, HEAP, lsl #32
    // 0xb54e0c: LoadField: r1 = r0->field_b
    //     0xb54e0c: ldur            w1, [x0, #0xb]
    // 0xb54e10: DecompressPointer r1
    //     0xb54e10: add             x1, x1, HEAP, lsl #32
    // 0xb54e14: cmp             w1, NULL
    // 0xb54e18: b.eq            #0xb551e0
    // 0xb54e1c: LoadField: r0 = r1->field_b
    //     0xb54e1c: ldur            w0, [x1, #0xb]
    // 0xb54e20: DecompressPointer r0
    //     0xb54e20: add             x0, x0, HEAP, lsl #32
    // 0xb54e24: cmp             w0, NULL
    // 0xb54e28: b.ne            #0xb54e34
    // 0xb54e2c: r0 = Null
    //     0xb54e2c: mov             x0, NULL
    // 0xb54e30: b               #0xb54eb0
    // 0xb54e34: LoadField: r4 = r0->field_13
    //     0xb54e34: ldur            w4, [x0, #0x13]
    // 0xb54e38: DecompressPointer r4
    //     0xb54e38: add             x4, x4, HEAP, lsl #32
    // 0xb54e3c: cmp             w4, NULL
    // 0xb54e40: b.ne            #0xb54e4c
    // 0xb54e44: r0 = Null
    //     0xb54e44: mov             x0, NULL
    // 0xb54e48: b               #0xb54eb0
    // 0xb54e4c: ldur            x5, [fp, #-8]
    // 0xb54e50: LoadField: r0 = r4->field_b
    //     0xb54e50: ldur            w0, [x4, #0xb]
    // 0xb54e54: r1 = LoadInt32Instr(r0)
    //     0xb54e54: sbfx            x1, x0, #1, #0x1f
    // 0xb54e58: mov             x0, x1
    // 0xb54e5c: mov             x1, x5
    // 0xb54e60: cmp             x1, x0
    // 0xb54e64: b.hs            #0xb551e4
    // 0xb54e68: LoadField: r0 = r4->field_f
    //     0xb54e68: ldur            w0, [x4, #0xf]
    // 0xb54e6c: DecompressPointer r0
    //     0xb54e6c: add             x0, x0, HEAP, lsl #32
    // 0xb54e70: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb54e70: add             x16, x0, x5, lsl #2
    //     0xb54e74: ldur            w1, [x16, #0xf]
    // 0xb54e78: DecompressPointer r1
    //     0xb54e78: add             x1, x1, HEAP, lsl #32
    // 0xb54e7c: LoadField: r0 = r1->field_7
    //     0xb54e7c: ldur            w0, [x1, #7]
    // 0xb54e80: DecompressPointer r0
    //     0xb54e80: add             x0, x0, HEAP, lsl #32
    // 0xb54e84: r1 = 60
    //     0xb54e84: movz            x1, #0x3c
    // 0xb54e88: branchIfSmi(r0, 0xb54e94)
    //     0xb54e88: tbz             w0, #0, #0xb54e94
    // 0xb54e8c: r1 = LoadClassIdInstr(r0)
    //     0xb54e8c: ldur            x1, [x0, #-1]
    //     0xb54e90: ubfx            x1, x1, #0xc, #0x14
    // 0xb54e94: str             x0, [SP]
    // 0xb54e98: mov             x0, x1
    // 0xb54e9c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb54e9c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb54ea0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb54ea0: movz            x17, #0x2700
    //     0xb54ea4: add             lr, x0, x17
    //     0xb54ea8: ldr             lr, [x21, lr, lsl #3]
    //     0xb54eac: blr             lr
    // 0xb54eb0: cmp             w0, NULL
    // 0xb54eb4: b.ne            #0xb54ec0
    // 0xb54eb8: r3 = ""
    //     0xb54eb8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb54ebc: b               #0xb54ec4
    // 0xb54ec0: mov             x3, x0
    // 0xb54ec4: ldur            x0, [fp, #-0x10]
    // 0xb54ec8: stur            x3, [fp, #-0x20]
    // 0xb54ecc: r1 = Null
    //     0xb54ecc: mov             x1, NULL
    // 0xb54ed0: r2 = 6
    //     0xb54ed0: movz            x2, #0x6
    // 0xb54ed4: r0 = AllocateArray()
    //     0xb54ed4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb54ed8: mov             x2, x0
    // 0xb54edc: ldur            x0, [fp, #-0x20]
    // 0xb54ee0: StoreField: r2->field_f = r0
    //     0xb54ee0: stur            w0, [x2, #0xf]
    // 0xb54ee4: r16 = " - "
    //     0xb54ee4: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xb54ee8: ldr             x16, [x16, #0xc08]
    // 0xb54eec: StoreField: r2->field_13 = r16
    //     0xb54eec: stur            w16, [x2, #0x13]
    // 0xb54ef0: ldur            x3, [fp, #-0x10]
    // 0xb54ef4: LoadField: r0 = r3->field_f
    //     0xb54ef4: ldur            w0, [x3, #0xf]
    // 0xb54ef8: DecompressPointer r0
    //     0xb54ef8: add             x0, x0, HEAP, lsl #32
    // 0xb54efc: LoadField: r1 = r0->field_b
    //     0xb54efc: ldur            w1, [x0, #0xb]
    // 0xb54f00: DecompressPointer r1
    //     0xb54f00: add             x1, x1, HEAP, lsl #32
    // 0xb54f04: cmp             w1, NULL
    // 0xb54f08: b.eq            #0xb551e8
    // 0xb54f0c: LoadField: r0 = r1->field_b
    //     0xb54f0c: ldur            w0, [x1, #0xb]
    // 0xb54f10: DecompressPointer r0
    //     0xb54f10: add             x0, x0, HEAP, lsl #32
    // 0xb54f14: cmp             w0, NULL
    // 0xb54f18: b.ne            #0xb54f28
    // 0xb54f1c: ldur            x5, [fp, #-8]
    // 0xb54f20: r0 = Null
    //     0xb54f20: mov             x0, NULL
    // 0xb54f24: b               #0xb54f7c
    // 0xb54f28: LoadField: r4 = r0->field_13
    //     0xb54f28: ldur            w4, [x0, #0x13]
    // 0xb54f2c: DecompressPointer r4
    //     0xb54f2c: add             x4, x4, HEAP, lsl #32
    // 0xb54f30: cmp             w4, NULL
    // 0xb54f34: b.ne            #0xb54f44
    // 0xb54f38: ldur            x5, [fp, #-8]
    // 0xb54f3c: r0 = Null
    //     0xb54f3c: mov             x0, NULL
    // 0xb54f40: b               #0xb54f7c
    // 0xb54f44: ldur            x5, [fp, #-8]
    // 0xb54f48: LoadField: r0 = r4->field_b
    //     0xb54f48: ldur            w0, [x4, #0xb]
    // 0xb54f4c: r1 = LoadInt32Instr(r0)
    //     0xb54f4c: sbfx            x1, x0, #1, #0x1f
    // 0xb54f50: mov             x0, x1
    // 0xb54f54: mov             x1, x5
    // 0xb54f58: cmp             x1, x0
    // 0xb54f5c: b.hs            #0xb551ec
    // 0xb54f60: LoadField: r0 = r4->field_f
    //     0xb54f60: ldur            w0, [x4, #0xf]
    // 0xb54f64: DecompressPointer r0
    //     0xb54f64: add             x0, x0, HEAP, lsl #32
    // 0xb54f68: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb54f68: add             x16, x0, x5, lsl #2
    //     0xb54f6c: ldur            w1, [x16, #0xf]
    // 0xb54f70: DecompressPointer r1
    //     0xb54f70: add             x1, x1, HEAP, lsl #32
    // 0xb54f74: LoadField: r0 = r1->field_b
    //     0xb54f74: ldur            w0, [x1, #0xb]
    // 0xb54f78: DecompressPointer r0
    //     0xb54f78: add             x0, x0, HEAP, lsl #32
    // 0xb54f7c: cmp             w0, NULL
    // 0xb54f80: b.ne            #0xb54f8c
    // 0xb54f84: r0 = "above"
    //     0xb54f84: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xb54f88: ldr             x0, [x0, #0xc10]
    // 0xb54f8c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb54f8c: stur            w0, [x2, #0x17]
    // 0xb54f90: str             x2, [SP]
    // 0xb54f94: r0 = _interpolate()
    //     0xb54f94: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb54f98: ldr             x1, [fp, #0x18]
    // 0xb54f9c: stur            x0, [fp, #-0x20]
    // 0xb54fa0: r0 = of()
    //     0xb54fa0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb54fa4: LoadField: r1 = r0->field_87
    //     0xb54fa4: ldur            w1, [x0, #0x87]
    // 0xb54fa8: DecompressPointer r1
    //     0xb54fa8: add             x1, x1, HEAP, lsl #32
    // 0xb54fac: LoadField: r0 = r1->field_2b
    //     0xb54fac: ldur            w0, [x1, #0x2b]
    // 0xb54fb0: DecompressPointer r0
    //     0xb54fb0: add             x0, x0, HEAP, lsl #32
    // 0xb54fb4: r16 = 14.000000
    //     0xb54fb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb54fb8: ldr             x16, [x16, #0x1d8]
    // 0xb54fbc: r30 = Instance_Color
    //     0xb54fbc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb54fc0: stp             lr, x16, [SP]
    // 0xb54fc4: mov             x1, x0
    // 0xb54fc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb54fc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb54fcc: ldr             x4, [x4, #0xaa0]
    // 0xb54fd0: r0 = copyWith()
    //     0xb54fd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb54fd4: stur            x0, [fp, #-0x28]
    // 0xb54fd8: r0 = Text()
    //     0xb54fd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb54fdc: mov             x2, x0
    // 0xb54fe0: ldur            x0, [fp, #-0x20]
    // 0xb54fe4: stur            x2, [fp, #-0x30]
    // 0xb54fe8: StoreField: r2->field_b = r0
    //     0xb54fe8: stur            w0, [x2, #0xb]
    // 0xb54fec: ldur            x0, [fp, #-0x28]
    // 0xb54ff0: StoreField: r2->field_13 = r0
    //     0xb54ff0: stur            w0, [x2, #0x13]
    // 0xb54ff4: ldur            x0, [fp, #-0x10]
    // 0xb54ff8: LoadField: r1 = r0->field_f
    //     0xb54ff8: ldur            w1, [x0, #0xf]
    // 0xb54ffc: DecompressPointer r1
    //     0xb54ffc: add             x1, x1, HEAP, lsl #32
    // 0xb55000: LoadField: r0 = r1->field_b
    //     0xb55000: ldur            w0, [x1, #0xb]
    // 0xb55004: DecompressPointer r0
    //     0xb55004: add             x0, x0, HEAP, lsl #32
    // 0xb55008: cmp             w0, NULL
    // 0xb5500c: b.eq            #0xb551f0
    // 0xb55010: LoadField: r1 = r0->field_b
    //     0xb55010: ldur            w1, [x0, #0xb]
    // 0xb55014: DecompressPointer r1
    //     0xb55014: add             x1, x1, HEAP, lsl #32
    // 0xb55018: cmp             w1, NULL
    // 0xb5501c: b.ne            #0xb55028
    // 0xb55020: r0 = Null
    //     0xb55020: mov             x0, NULL
    // 0xb55024: b               #0xb550a4
    // 0xb55028: LoadField: r3 = r1->field_13
    //     0xb55028: ldur            w3, [x1, #0x13]
    // 0xb5502c: DecompressPointer r3
    //     0xb5502c: add             x3, x3, HEAP, lsl #32
    // 0xb55030: cmp             w3, NULL
    // 0xb55034: b.ne            #0xb55040
    // 0xb55038: r0 = Null
    //     0xb55038: mov             x0, NULL
    // 0xb5503c: b               #0xb550a4
    // 0xb55040: ldur            x4, [fp, #-8]
    // 0xb55044: LoadField: r0 = r3->field_b
    //     0xb55044: ldur            w0, [x3, #0xb]
    // 0xb55048: r1 = LoadInt32Instr(r0)
    //     0xb55048: sbfx            x1, x0, #1, #0x1f
    // 0xb5504c: mov             x0, x1
    // 0xb55050: mov             x1, x4
    // 0xb55054: cmp             x1, x0
    // 0xb55058: b.hs            #0xb551f4
    // 0xb5505c: LoadField: r0 = r3->field_f
    //     0xb5505c: ldur            w0, [x3, #0xf]
    // 0xb55060: DecompressPointer r0
    //     0xb55060: add             x0, x0, HEAP, lsl #32
    // 0xb55064: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb55064: add             x16, x0, x4, lsl #2
    //     0xb55068: ldur            w1, [x16, #0xf]
    // 0xb5506c: DecompressPointer r1
    //     0xb5506c: add             x1, x1, HEAP, lsl #32
    // 0xb55070: LoadField: r0 = r1->field_f
    //     0xb55070: ldur            w0, [x1, #0xf]
    // 0xb55074: DecompressPointer r0
    //     0xb55074: add             x0, x0, HEAP, lsl #32
    // 0xb55078: r1 = 60
    //     0xb55078: movz            x1, #0x3c
    // 0xb5507c: branchIfSmi(r0, 0xb55088)
    //     0xb5507c: tbz             w0, #0, #0xb55088
    // 0xb55080: r1 = LoadClassIdInstr(r0)
    //     0xb55080: ldur            x1, [x0, #-1]
    //     0xb55084: ubfx            x1, x1, #0xc, #0x14
    // 0xb55088: str             x0, [SP]
    // 0xb5508c: mov             x0, x1
    // 0xb55090: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb55090: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb55094: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb55094: movz            x17, #0x2700
    //     0xb55098: add             lr, x0, x17
    //     0xb5509c: ldr             lr, [x21, lr, lsl #3]
    //     0xb550a0: blr             lr
    // 0xb550a4: cmp             w0, NULL
    // 0xb550a8: b.ne            #0xb550b4
    // 0xb550ac: r3 = ""
    //     0xb550ac: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb550b0: b               #0xb550b8
    // 0xb550b4: mov             x3, x0
    // 0xb550b8: ldur            x2, [fp, #-0x18]
    // 0xb550bc: ldur            x0, [fp, #-0x30]
    // 0xb550c0: ldr             x1, [fp, #0x18]
    // 0xb550c4: stur            x3, [fp, #-0x10]
    // 0xb550c8: r0 = of()
    //     0xb550c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb550cc: LoadField: r1 = r0->field_87
    //     0xb550cc: ldur            w1, [x0, #0x87]
    // 0xb550d0: DecompressPointer r1
    //     0xb550d0: add             x1, x1, HEAP, lsl #32
    // 0xb550d4: LoadField: r0 = r1->field_2b
    //     0xb550d4: ldur            w0, [x1, #0x2b]
    // 0xb550d8: DecompressPointer r0
    //     0xb550d8: add             x0, x0, HEAP, lsl #32
    // 0xb550dc: r16 = 14.000000
    //     0xb550dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb550e0: ldr             x16, [x16, #0x1d8]
    // 0xb550e4: r30 = Instance_Color
    //     0xb550e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb550e8: stp             lr, x16, [SP]
    // 0xb550ec: mov             x1, x0
    // 0xb550f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb550f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb550f4: ldr             x4, [x4, #0xaa0]
    // 0xb550f8: r0 = copyWith()
    //     0xb550f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb550fc: stur            x0, [fp, #-0x20]
    // 0xb55100: r0 = Text()
    //     0xb55100: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb55104: mov             x3, x0
    // 0xb55108: ldur            x0, [fp, #-0x10]
    // 0xb5510c: stur            x3, [fp, #-0x28]
    // 0xb55110: StoreField: r3->field_b = r0
    //     0xb55110: stur            w0, [x3, #0xb]
    // 0xb55114: ldur            x0, [fp, #-0x20]
    // 0xb55118: StoreField: r3->field_13 = r0
    //     0xb55118: stur            w0, [x3, #0x13]
    // 0xb5511c: r1 = Null
    //     0xb5511c: mov             x1, NULL
    // 0xb55120: r2 = 8
    //     0xb55120: movz            x2, #0x8
    // 0xb55124: r0 = AllocateArray()
    //     0xb55124: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb55128: mov             x2, x0
    // 0xb5512c: ldur            x0, [fp, #-0x18]
    // 0xb55130: stur            x2, [fp, #-0x10]
    // 0xb55134: StoreField: r2->field_f = r0
    //     0xb55134: stur            w0, [x2, #0xf]
    // 0xb55138: ldur            x0, [fp, #-0x30]
    // 0xb5513c: StoreField: r2->field_13 = r0
    //     0xb5513c: stur            w0, [x2, #0x13]
    // 0xb55140: r16 = Instance_Spacer
    //     0xb55140: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb55144: ldr             x16, [x16, #0xf0]
    // 0xb55148: ArrayStore: r2[0] = r16  ; List_4
    //     0xb55148: stur            w16, [x2, #0x17]
    // 0xb5514c: ldur            x0, [fp, #-0x28]
    // 0xb55150: StoreField: r2->field_1b = r0
    //     0xb55150: stur            w0, [x2, #0x1b]
    // 0xb55154: r1 = <Widget>
    //     0xb55154: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb55158: r0 = AllocateGrowableArray()
    //     0xb55158: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5515c: mov             x1, x0
    // 0xb55160: ldur            x0, [fp, #-0x10]
    // 0xb55164: stur            x1, [fp, #-0x18]
    // 0xb55168: StoreField: r1->field_f = r0
    //     0xb55168: stur            w0, [x1, #0xf]
    // 0xb5516c: r0 = 8
    //     0xb5516c: movz            x0, #0x8
    // 0xb55170: StoreField: r1->field_b = r0
    //     0xb55170: stur            w0, [x1, #0xb]
    // 0xb55174: r0 = Row()
    //     0xb55174: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb55178: r1 = Instance_Axis
    //     0xb55178: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5517c: StoreField: r0->field_f = r1
    //     0xb5517c: stur            w1, [x0, #0xf]
    // 0xb55180: r1 = Instance_MainAxisAlignment
    //     0xb55180: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb55184: ldr             x1, [x1, #0xa08]
    // 0xb55188: StoreField: r0->field_13 = r1
    //     0xb55188: stur            w1, [x0, #0x13]
    // 0xb5518c: r1 = Instance_MainAxisSize
    //     0xb5518c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb55190: ldr             x1, [x1, #0xa10]
    // 0xb55194: ArrayStore: r0[0] = r1  ; List_4
    //     0xb55194: stur            w1, [x0, #0x17]
    // 0xb55198: r1 = Instance_CrossAxisAlignment
    //     0xb55198: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5519c: ldr             x1, [x1, #0xa18]
    // 0xb551a0: StoreField: r0->field_1b = r1
    //     0xb551a0: stur            w1, [x0, #0x1b]
    // 0xb551a4: r1 = Instance_VerticalDirection
    //     0xb551a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb551a8: ldr             x1, [x1, #0xa20]
    // 0xb551ac: StoreField: r0->field_23 = r1
    //     0xb551ac: stur            w1, [x0, #0x23]
    // 0xb551b0: r1 = Instance_Clip
    //     0xb551b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb551b4: ldr             x1, [x1, #0x38]
    // 0xb551b8: StoreField: r0->field_2b = r1
    //     0xb551b8: stur            w1, [x0, #0x2b]
    // 0xb551bc: StoreField: r0->field_2f = rZR
    //     0xb551bc: stur            xzr, [x0, #0x2f]
    // 0xb551c0: ldur            x1, [fp, #-0x18]
    // 0xb551c4: StoreField: r0->field_b = r1
    //     0xb551c4: stur            w1, [x0, #0xb]
    // 0xb551c8: LeaveFrame
    //     0xb551c8: mov             SP, fp
    //     0xb551cc: ldp             fp, lr, [SP], #0x10
    // 0xb551d0: ret
    //     0xb551d0: ret             
    // 0xb551d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb551d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb551d8: b               #0xb54db4
    // 0xb551dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb551dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb551e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb551e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb551e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb551e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb551e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb551e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb551ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb551ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb551f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb551f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb551f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb551f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _productBuildItem(/* No info */) {
    // ** addr: 0xb551f8, size: 0x704
    // 0xb551f8: EnterFrame
    //     0xb551f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb551fc: mov             fp, SP
    // 0xb55200: AllocStack(0x38)
    //     0xb55200: sub             SP, SP, #0x38
    // 0xb55204: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xb55204: mov             x0, x1
    //     0xb55208: stur            x1, [fp, #-8]
    //     0xb5520c: mov             x1, x5
    //     0xb55210: stur            x2, [fp, #-0x10]
    //     0xb55214: stur            x3, [fp, #-0x18]
    //     0xb55218: stur            x5, [fp, #-0x20]
    // 0xb5521c: CheckStackOverflow
    //     0xb5521c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb55220: cmp             SP, x16
    //     0xb55224: b.ls            #0xb558c8
    // 0xb55228: r1 = 3
    //     0xb55228: movz            x1, #0x3
    // 0xb5522c: r0 = AllocateContext()
    //     0xb5522c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb55230: mov             x4, x0
    // 0xb55234: ldur            x3, [fp, #-8]
    // 0xb55238: stur            x4, [fp, #-0x28]
    // 0xb5523c: StoreField: r4->field_f = r3
    //     0xb5523c: stur            w3, [x4, #0xf]
    // 0xb55240: ldur            x0, [fp, #-0x18]
    // 0xb55244: StoreField: r4->field_13 = r0
    //     0xb55244: stur            w0, [x4, #0x13]
    // 0xb55248: r1 = ""
    //     0xb55248: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5524c: ArrayStore: r4[0] = r1  ; List_4
    //     0xb5524c: stur            w1, [x4, #0x17]
    // 0xb55250: LoadField: r1 = r0->field_7
    //     0xb55250: ldur            x1, [x0, #7]
    // 0xb55254: cmp             x1, #1
    // 0xb55258: b.gt            #0xb55498
    // 0xb5525c: cmp             x1, #0
    // 0xb55260: b.gt            #0xb55380
    // 0xb55264: ldur            x5, [fp, #-0x10]
    // 0xb55268: LoadField: r2 = r3->field_13
    //     0xb55268: ldur            w2, [x3, #0x13]
    // 0xb5526c: DecompressPointer r2
    //     0xb5526c: add             x2, x2, HEAP, lsl #32
    // 0xb55270: cmp             w5, NULL
    // 0xb55274: b.ne            #0xb55284
    // 0xb55278: ldur            x6, [fp, #-0x20]
    // 0xb5527c: r0 = Null
    //     0xb5527c: mov             x0, NULL
    // 0xb55280: b               #0xb552d8
    // 0xb55284: LoadField: r3 = r5->field_7
    //     0xb55284: ldur            w3, [x5, #7]
    // 0xb55288: DecompressPointer r3
    //     0xb55288: add             x3, x3, HEAP, lsl #32
    // 0xb5528c: cmp             w3, NULL
    // 0xb55290: b.ne            #0xb552a0
    // 0xb55294: ldur            x6, [fp, #-0x20]
    // 0xb55298: r0 = Null
    //     0xb55298: mov             x0, NULL
    // 0xb5529c: b               #0xb552d8
    // 0xb552a0: ldur            x6, [fp, #-0x20]
    // 0xb552a4: LoadField: r0 = r3->field_b
    //     0xb552a4: ldur            w0, [x3, #0xb]
    // 0xb552a8: r1 = LoadInt32Instr(r0)
    //     0xb552a8: sbfx            x1, x0, #1, #0x1f
    // 0xb552ac: mov             x0, x1
    // 0xb552b0: mov             x1, x6
    // 0xb552b4: cmp             x1, x0
    // 0xb552b8: b.hs            #0xb558d0
    // 0xb552bc: LoadField: r0 = r3->field_f
    //     0xb552bc: ldur            w0, [x3, #0xf]
    // 0xb552c0: DecompressPointer r0
    //     0xb552c0: add             x0, x0, HEAP, lsl #32
    // 0xb552c4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb552c4: add             x16, x0, x6, lsl #2
    //     0xb552c8: ldur            w1, [x16, #0xf]
    // 0xb552cc: DecompressPointer r1
    //     0xb552cc: add             x1, x1, HEAP, lsl #32
    // 0xb552d0: LoadField: r0 = r1->field_7
    //     0xb552d0: ldur            w0, [x1, #7]
    // 0xb552d4: DecompressPointer r0
    //     0xb552d4: add             x0, x0, HEAP, lsl #32
    // 0xb552d8: mov             x1, x2
    // 0xb552dc: mov             x2, x0
    // 0xb552e0: r0 = contains()
    //     0xb552e0: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb552e4: mov             x2, x0
    // 0xb552e8: ldur            x4, [fp, #-0x10]
    // 0xb552ec: cmp             w4, NULL
    // 0xb552f0: b.ne            #0xb552fc
    // 0xb552f4: r0 = Null
    //     0xb552f4: mov             x0, NULL
    // 0xb552f8: b               #0xb5534c
    // 0xb552fc: LoadField: r3 = r4->field_7
    //     0xb552fc: ldur            w3, [x4, #7]
    // 0xb55300: DecompressPointer r3
    //     0xb55300: add             x3, x3, HEAP, lsl #32
    // 0xb55304: cmp             w3, NULL
    // 0xb55308: b.ne            #0xb55314
    // 0xb5530c: r0 = Null
    //     0xb5530c: mov             x0, NULL
    // 0xb55310: b               #0xb5534c
    // 0xb55314: ldur            x5, [fp, #-0x20]
    // 0xb55318: LoadField: r0 = r3->field_b
    //     0xb55318: ldur            w0, [x3, #0xb]
    // 0xb5531c: r1 = LoadInt32Instr(r0)
    //     0xb5531c: sbfx            x1, x0, #1, #0x1f
    // 0xb55320: mov             x0, x1
    // 0xb55324: mov             x1, x5
    // 0xb55328: cmp             x1, x0
    // 0xb5532c: b.hs            #0xb558d4
    // 0xb55330: LoadField: r0 = r3->field_f
    //     0xb55330: ldur            w0, [x3, #0xf]
    // 0xb55334: DecompressPointer r0
    //     0xb55334: add             x0, x0, HEAP, lsl #32
    // 0xb55338: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55338: add             x16, x0, x5, lsl #2
    //     0xb5533c: ldur            w1, [x16, #0xf]
    // 0xb55340: DecompressPointer r1
    //     0xb55340: add             x1, x1, HEAP, lsl #32
    // 0xb55344: LoadField: r0 = r1->field_7
    //     0xb55344: ldur            w0, [x1, #7]
    // 0xb55348: DecompressPointer r0
    //     0xb55348: add             x0, x0, HEAP, lsl #32
    // 0xb5534c: cmp             w0, NULL
    // 0xb55350: b.ne            #0xb55358
    // 0xb55354: r0 = ""
    //     0xb55354: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55358: ldur            x6, [fp, #-0x28]
    // 0xb5535c: ArrayStore: r6[0] = r0  ; List_4
    //     0xb5535c: stur            w0, [x6, #0x17]
    //     0xb55360: ldurb           w16, [x6, #-1]
    //     0xb55364: ldurb           w17, [x0, #-1]
    //     0xb55368: and             x16, x17, x16, lsr #2
    //     0xb5536c: tst             x16, HEAP, lsr #32
    //     0xb55370: b.eq            #0xb55378
    //     0xb55374: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xb55378: mov             x0, x2
    // 0xb5537c: b               #0xb55824
    // 0xb55380: mov             x6, x4
    // 0xb55384: ldur            x4, [fp, #-0x10]
    // 0xb55388: ldur            x5, [fp, #-0x20]
    // 0xb5538c: LoadField: r2 = r3->field_13
    //     0xb5538c: ldur            w2, [x3, #0x13]
    // 0xb55390: DecompressPointer r2
    //     0xb55390: add             x2, x2, HEAP, lsl #32
    // 0xb55394: cmp             w4, NULL
    // 0xb55398: b.ne            #0xb553a4
    // 0xb5539c: r0 = Null
    //     0xb5539c: mov             x0, NULL
    // 0xb553a0: b               #0xb553f0
    // 0xb553a4: LoadField: r3 = r4->field_f
    //     0xb553a4: ldur            w3, [x4, #0xf]
    // 0xb553a8: DecompressPointer r3
    //     0xb553a8: add             x3, x3, HEAP, lsl #32
    // 0xb553ac: cmp             w3, NULL
    // 0xb553b0: b.ne            #0xb553bc
    // 0xb553b4: r0 = Null
    //     0xb553b4: mov             x0, NULL
    // 0xb553b8: b               #0xb553f0
    // 0xb553bc: LoadField: r0 = r3->field_b
    //     0xb553bc: ldur            w0, [x3, #0xb]
    // 0xb553c0: r1 = LoadInt32Instr(r0)
    //     0xb553c0: sbfx            x1, x0, #1, #0x1f
    // 0xb553c4: mov             x0, x1
    // 0xb553c8: mov             x1, x5
    // 0xb553cc: cmp             x1, x0
    // 0xb553d0: b.hs            #0xb558d8
    // 0xb553d4: LoadField: r0 = r3->field_f
    //     0xb553d4: ldur            w0, [x3, #0xf]
    // 0xb553d8: DecompressPointer r0
    //     0xb553d8: add             x0, x0, HEAP, lsl #32
    // 0xb553dc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb553dc: add             x16, x0, x5, lsl #2
    //     0xb553e0: ldur            w1, [x16, #0xf]
    // 0xb553e4: DecompressPointer r1
    //     0xb553e4: add             x1, x1, HEAP, lsl #32
    // 0xb553e8: LoadField: r0 = r1->field_7
    //     0xb553e8: ldur            w0, [x1, #7]
    // 0xb553ec: DecompressPointer r0
    //     0xb553ec: add             x0, x0, HEAP, lsl #32
    // 0xb553f0: mov             x1, x2
    // 0xb553f4: mov             x2, x0
    // 0xb553f8: r0 = contains()
    //     0xb553f8: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb553fc: mov             x2, x0
    // 0xb55400: ldur            x4, [fp, #-0x10]
    // 0xb55404: cmp             w4, NULL
    // 0xb55408: b.ne            #0xb55414
    // 0xb5540c: r0 = Null
    //     0xb5540c: mov             x0, NULL
    // 0xb55410: b               #0xb55464
    // 0xb55414: LoadField: r3 = r4->field_f
    //     0xb55414: ldur            w3, [x4, #0xf]
    // 0xb55418: DecompressPointer r3
    //     0xb55418: add             x3, x3, HEAP, lsl #32
    // 0xb5541c: cmp             w3, NULL
    // 0xb55420: b.ne            #0xb5542c
    // 0xb55424: r0 = Null
    //     0xb55424: mov             x0, NULL
    // 0xb55428: b               #0xb55464
    // 0xb5542c: ldur            x5, [fp, #-0x20]
    // 0xb55430: LoadField: r0 = r3->field_b
    //     0xb55430: ldur            w0, [x3, #0xb]
    // 0xb55434: r1 = LoadInt32Instr(r0)
    //     0xb55434: sbfx            x1, x0, #1, #0x1f
    // 0xb55438: mov             x0, x1
    // 0xb5543c: mov             x1, x5
    // 0xb55440: cmp             x1, x0
    // 0xb55444: b.hs            #0xb558dc
    // 0xb55448: LoadField: r0 = r3->field_f
    //     0xb55448: ldur            w0, [x3, #0xf]
    // 0xb5544c: DecompressPointer r0
    //     0xb5544c: add             x0, x0, HEAP, lsl #32
    // 0xb55450: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55450: add             x16, x0, x5, lsl #2
    //     0xb55454: ldur            w1, [x16, #0xf]
    // 0xb55458: DecompressPointer r1
    //     0xb55458: add             x1, x1, HEAP, lsl #32
    // 0xb5545c: LoadField: r0 = r1->field_7
    //     0xb5545c: ldur            w0, [x1, #7]
    // 0xb55460: DecompressPointer r0
    //     0xb55460: add             x0, x0, HEAP, lsl #32
    // 0xb55464: cmp             w0, NULL
    // 0xb55468: b.ne            #0xb55470
    // 0xb5546c: r0 = ""
    //     0xb5546c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55470: ldur            x6, [fp, #-0x28]
    // 0xb55474: ArrayStore: r6[0] = r0  ; List_4
    //     0xb55474: stur            w0, [x6, #0x17]
    //     0xb55478: ldurb           w16, [x6, #-1]
    //     0xb5547c: ldurb           w17, [x0, #-1]
    //     0xb55480: and             x16, x17, x16, lsr #2
    //     0xb55484: tst             x16, HEAP, lsr #32
    //     0xb55488: b.eq            #0xb55490
    //     0xb5548c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xb55490: mov             x0, x2
    // 0xb55494: b               #0xb55824
    // 0xb55498: mov             x6, x4
    // 0xb5549c: ldur            x4, [fp, #-0x10]
    // 0xb554a0: ldur            x5, [fp, #-0x20]
    // 0xb554a4: cmp             x1, #2
    // 0xb554a8: b.gt            #0xb555b8
    // 0xb554ac: LoadField: r2 = r3->field_13
    //     0xb554ac: ldur            w2, [x3, #0x13]
    // 0xb554b0: DecompressPointer r2
    //     0xb554b0: add             x2, x2, HEAP, lsl #32
    // 0xb554b4: cmp             w4, NULL
    // 0xb554b8: b.ne            #0xb554c4
    // 0xb554bc: r0 = Null
    //     0xb554bc: mov             x0, NULL
    // 0xb554c0: b               #0xb55510
    // 0xb554c4: LoadField: r3 = r4->field_b
    //     0xb554c4: ldur            w3, [x4, #0xb]
    // 0xb554c8: DecompressPointer r3
    //     0xb554c8: add             x3, x3, HEAP, lsl #32
    // 0xb554cc: cmp             w3, NULL
    // 0xb554d0: b.ne            #0xb554dc
    // 0xb554d4: r0 = Null
    //     0xb554d4: mov             x0, NULL
    // 0xb554d8: b               #0xb55510
    // 0xb554dc: LoadField: r0 = r3->field_b
    //     0xb554dc: ldur            w0, [x3, #0xb]
    // 0xb554e0: r1 = LoadInt32Instr(r0)
    //     0xb554e0: sbfx            x1, x0, #1, #0x1f
    // 0xb554e4: mov             x0, x1
    // 0xb554e8: mov             x1, x5
    // 0xb554ec: cmp             x1, x0
    // 0xb554f0: b.hs            #0xb558e0
    // 0xb554f4: LoadField: r0 = r3->field_f
    //     0xb554f4: ldur            w0, [x3, #0xf]
    // 0xb554f8: DecompressPointer r0
    //     0xb554f8: add             x0, x0, HEAP, lsl #32
    // 0xb554fc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb554fc: add             x16, x0, x5, lsl #2
    //     0xb55500: ldur            w1, [x16, #0xf]
    // 0xb55504: DecompressPointer r1
    //     0xb55504: add             x1, x1, HEAP, lsl #32
    // 0xb55508: LoadField: r0 = r1->field_7
    //     0xb55508: ldur            w0, [x1, #7]
    // 0xb5550c: DecompressPointer r0
    //     0xb5550c: add             x0, x0, HEAP, lsl #32
    // 0xb55510: mov             x1, x2
    // 0xb55514: mov             x2, x0
    // 0xb55518: r0 = contains()
    //     0xb55518: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb5551c: mov             x2, x0
    // 0xb55520: ldur            x4, [fp, #-0x10]
    // 0xb55524: cmp             w4, NULL
    // 0xb55528: b.ne            #0xb55534
    // 0xb5552c: r0 = Null
    //     0xb5552c: mov             x0, NULL
    // 0xb55530: b               #0xb55584
    // 0xb55534: LoadField: r3 = r4->field_b
    //     0xb55534: ldur            w3, [x4, #0xb]
    // 0xb55538: DecompressPointer r3
    //     0xb55538: add             x3, x3, HEAP, lsl #32
    // 0xb5553c: cmp             w3, NULL
    // 0xb55540: b.ne            #0xb5554c
    // 0xb55544: r0 = Null
    //     0xb55544: mov             x0, NULL
    // 0xb55548: b               #0xb55584
    // 0xb5554c: ldur            x5, [fp, #-0x20]
    // 0xb55550: LoadField: r0 = r3->field_b
    //     0xb55550: ldur            w0, [x3, #0xb]
    // 0xb55554: r1 = LoadInt32Instr(r0)
    //     0xb55554: sbfx            x1, x0, #1, #0x1f
    // 0xb55558: mov             x0, x1
    // 0xb5555c: mov             x1, x5
    // 0xb55560: cmp             x1, x0
    // 0xb55564: b.hs            #0xb558e4
    // 0xb55568: LoadField: r0 = r3->field_f
    //     0xb55568: ldur            w0, [x3, #0xf]
    // 0xb5556c: DecompressPointer r0
    //     0xb5556c: add             x0, x0, HEAP, lsl #32
    // 0xb55570: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55570: add             x16, x0, x5, lsl #2
    //     0xb55574: ldur            w1, [x16, #0xf]
    // 0xb55578: DecompressPointer r1
    //     0xb55578: add             x1, x1, HEAP, lsl #32
    // 0xb5557c: LoadField: r0 = r1->field_7
    //     0xb5557c: ldur            w0, [x1, #7]
    // 0xb55580: DecompressPointer r0
    //     0xb55580: add             x0, x0, HEAP, lsl #32
    // 0xb55584: cmp             w0, NULL
    // 0xb55588: b.ne            #0xb55590
    // 0xb5558c: r0 = ""
    //     0xb5558c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55590: ldur            x6, [fp, #-0x28]
    // 0xb55594: ArrayStore: r6[0] = r0  ; List_4
    //     0xb55594: stur            w0, [x6, #0x17]
    //     0xb55598: ldurb           w16, [x6, #-1]
    //     0xb5559c: ldurb           w17, [x0, #-1]
    //     0xb555a0: and             x16, x17, x16, lsr #2
    //     0xb555a4: tst             x16, HEAP, lsr #32
    //     0xb555a8: b.eq            #0xb555b0
    //     0xb555ac: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xb555b0: mov             x0, x2
    // 0xb555b4: b               #0xb55824
    // 0xb555b8: cmp             w4, NULL
    // 0xb555bc: b.ne            #0xb555c8
    // 0xb555c0: r0 = Null
    //     0xb555c0: mov             x0, NULL
    // 0xb555c4: b               #0xb55614
    // 0xb555c8: LoadField: r2 = r4->field_13
    //     0xb555c8: ldur            w2, [x4, #0x13]
    // 0xb555cc: DecompressPointer r2
    //     0xb555cc: add             x2, x2, HEAP, lsl #32
    // 0xb555d0: cmp             w2, NULL
    // 0xb555d4: b.ne            #0xb555e0
    // 0xb555d8: r0 = Null
    //     0xb555d8: mov             x0, NULL
    // 0xb555dc: b               #0xb55614
    // 0xb555e0: LoadField: r0 = r2->field_b
    //     0xb555e0: ldur            w0, [x2, #0xb]
    // 0xb555e4: r1 = LoadInt32Instr(r0)
    //     0xb555e4: sbfx            x1, x0, #1, #0x1f
    // 0xb555e8: mov             x0, x1
    // 0xb555ec: mov             x1, x5
    // 0xb555f0: cmp             x1, x0
    // 0xb555f4: b.hs            #0xb558e8
    // 0xb555f8: LoadField: r0 = r2->field_f
    //     0xb555f8: ldur            w0, [x2, #0xf]
    // 0xb555fc: DecompressPointer r0
    //     0xb555fc: add             x0, x0, HEAP, lsl #32
    // 0xb55600: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55600: add             x16, x0, x5, lsl #2
    //     0xb55604: ldur            w1, [x16, #0xf]
    // 0xb55608: DecompressPointer r1
    //     0xb55608: add             x1, x1, HEAP, lsl #32
    // 0xb5560c: LoadField: r0 = r1->field_7
    //     0xb5560c: ldur            w0, [x1, #7]
    // 0xb55610: DecompressPointer r0
    //     0xb55610: add             x0, x0, HEAP, lsl #32
    // 0xb55614: stur            x0, [fp, #-0x18]
    // 0xb55618: r1 = Null
    //     0xb55618: mov             x1, NULL
    // 0xb5561c: r2 = 6
    //     0xb5561c: movz            x2, #0x6
    // 0xb55620: r0 = AllocateArray()
    //     0xb55620: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb55624: mov             x2, x0
    // 0xb55628: ldur            x0, [fp, #-0x18]
    // 0xb5562c: StoreField: r2->field_f = r0
    //     0xb5562c: stur            w0, [x2, #0xf]
    // 0xb55630: r16 = " - "
    //     0xb55630: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xb55634: ldr             x16, [x16, #0xc08]
    // 0xb55638: StoreField: r2->field_13 = r16
    //     0xb55638: stur            w16, [x2, #0x13]
    // 0xb5563c: ldur            x3, [fp, #-0x10]
    // 0xb55640: cmp             w3, NULL
    // 0xb55644: b.ne            #0xb55654
    // 0xb55648: ldur            x5, [fp, #-0x20]
    // 0xb5564c: r0 = Null
    //     0xb5564c: mov             x0, NULL
    // 0xb55650: b               #0xb556a8
    // 0xb55654: LoadField: r4 = r3->field_13
    //     0xb55654: ldur            w4, [x3, #0x13]
    // 0xb55658: DecompressPointer r4
    //     0xb55658: add             x4, x4, HEAP, lsl #32
    // 0xb5565c: cmp             w4, NULL
    // 0xb55660: b.ne            #0xb55670
    // 0xb55664: ldur            x5, [fp, #-0x20]
    // 0xb55668: r0 = Null
    //     0xb55668: mov             x0, NULL
    // 0xb5566c: b               #0xb556a8
    // 0xb55670: ldur            x5, [fp, #-0x20]
    // 0xb55674: LoadField: r0 = r4->field_b
    //     0xb55674: ldur            w0, [x4, #0xb]
    // 0xb55678: r1 = LoadInt32Instr(r0)
    //     0xb55678: sbfx            x1, x0, #1, #0x1f
    // 0xb5567c: mov             x0, x1
    // 0xb55680: mov             x1, x5
    // 0xb55684: cmp             x1, x0
    // 0xb55688: b.hs            #0xb558ec
    // 0xb5568c: LoadField: r0 = r4->field_f
    //     0xb5568c: ldur            w0, [x4, #0xf]
    // 0xb55690: DecompressPointer r0
    //     0xb55690: add             x0, x0, HEAP, lsl #32
    // 0xb55694: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55694: add             x16, x0, x5, lsl #2
    //     0xb55698: ldur            w1, [x16, #0xf]
    // 0xb5569c: DecompressPointer r1
    //     0xb5569c: add             x1, x1, HEAP, lsl #32
    // 0xb556a0: LoadField: r0 = r1->field_b
    //     0xb556a0: ldur            w0, [x1, #0xb]
    // 0xb556a4: DecompressPointer r0
    //     0xb556a4: add             x0, x0, HEAP, lsl #32
    // 0xb556a8: cmp             w0, NULL
    // 0xb556ac: b.ne            #0xb556b8
    // 0xb556b0: r0 = "above"
    //     0xb556b0: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xb556b4: ldr             x0, [x0, #0xc10]
    // 0xb556b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb556b8: stur            w0, [x2, #0x17]
    // 0xb556bc: str             x2, [SP]
    // 0xb556c0: r0 = _interpolate()
    //     0xb556c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb556c4: mov             x4, x0
    // 0xb556c8: ldur            x3, [fp, #-0x10]
    // 0xb556cc: stur            x4, [fp, #-0x30]
    // 0xb556d0: cmp             w3, NULL
    // 0xb556d4: b.ne            #0xb556e4
    // 0xb556d8: ldur            x5, [fp, #-0x20]
    // 0xb556dc: r0 = Null
    //     0xb556dc: mov             x0, NULL
    // 0xb556e0: b               #0xb55738
    // 0xb556e4: LoadField: r2 = r3->field_13
    //     0xb556e4: ldur            w2, [x3, #0x13]
    // 0xb556e8: DecompressPointer r2
    //     0xb556e8: add             x2, x2, HEAP, lsl #32
    // 0xb556ec: cmp             w2, NULL
    // 0xb556f0: b.ne            #0xb55700
    // 0xb556f4: ldur            x5, [fp, #-0x20]
    // 0xb556f8: r0 = Null
    //     0xb556f8: mov             x0, NULL
    // 0xb556fc: b               #0xb55738
    // 0xb55700: ldur            x5, [fp, #-0x20]
    // 0xb55704: LoadField: r0 = r2->field_b
    //     0xb55704: ldur            w0, [x2, #0xb]
    // 0xb55708: r1 = LoadInt32Instr(r0)
    //     0xb55708: sbfx            x1, x0, #1, #0x1f
    // 0xb5570c: mov             x0, x1
    // 0xb55710: mov             x1, x5
    // 0xb55714: cmp             x1, x0
    // 0xb55718: b.hs            #0xb558f0
    // 0xb5571c: LoadField: r0 = r2->field_f
    //     0xb5571c: ldur            w0, [x2, #0xf]
    // 0xb55720: DecompressPointer r0
    //     0xb55720: add             x0, x0, HEAP, lsl #32
    // 0xb55724: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55724: add             x16, x0, x5, lsl #2
    //     0xb55728: ldur            w1, [x16, #0xf]
    // 0xb5572c: DecompressPointer r1
    //     0xb5572c: add             x1, x1, HEAP, lsl #32
    // 0xb55730: LoadField: r0 = r1->field_7
    //     0xb55730: ldur            w0, [x1, #7]
    // 0xb55734: DecompressPointer r0
    //     0xb55734: add             x0, x0, HEAP, lsl #32
    // 0xb55738: stur            x0, [fp, #-0x18]
    // 0xb5573c: r1 = Null
    //     0xb5573c: mov             x1, NULL
    // 0xb55740: r2 = 6
    //     0xb55740: movz            x2, #0x6
    // 0xb55744: r0 = AllocateArray()
    //     0xb55744: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb55748: mov             x2, x0
    // 0xb5574c: ldur            x0, [fp, #-0x18]
    // 0xb55750: StoreField: r2->field_f = r0
    //     0xb55750: stur            w0, [x2, #0xf]
    // 0xb55754: r16 = " - "
    //     0xb55754: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xb55758: ldr             x16, [x16, #0xc08]
    // 0xb5575c: StoreField: r2->field_13 = r16
    //     0xb5575c: stur            w16, [x2, #0x13]
    // 0xb55760: ldur            x0, [fp, #-0x10]
    // 0xb55764: cmp             w0, NULL
    // 0xb55768: b.ne            #0xb55774
    // 0xb5576c: r0 = Null
    //     0xb5576c: mov             x0, NULL
    // 0xb55770: b               #0xb557c4
    // 0xb55774: LoadField: r3 = r0->field_13
    //     0xb55774: ldur            w3, [x0, #0x13]
    // 0xb55778: DecompressPointer r3
    //     0xb55778: add             x3, x3, HEAP, lsl #32
    // 0xb5577c: cmp             w3, NULL
    // 0xb55780: b.ne            #0xb5578c
    // 0xb55784: r0 = Null
    //     0xb55784: mov             x0, NULL
    // 0xb55788: b               #0xb557c4
    // 0xb5578c: ldur            x4, [fp, #-0x20]
    // 0xb55790: LoadField: r0 = r3->field_b
    //     0xb55790: ldur            w0, [x3, #0xb]
    // 0xb55794: r1 = LoadInt32Instr(r0)
    //     0xb55794: sbfx            x1, x0, #1, #0x1f
    // 0xb55798: mov             x0, x1
    // 0xb5579c: mov             x1, x4
    // 0xb557a0: cmp             x1, x0
    // 0xb557a4: b.hs            #0xb558f4
    // 0xb557a8: LoadField: r0 = r3->field_f
    //     0xb557a8: ldur            w0, [x3, #0xf]
    // 0xb557ac: DecompressPointer r0
    //     0xb557ac: add             x0, x0, HEAP, lsl #32
    // 0xb557b0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb557b0: add             x16, x0, x4, lsl #2
    //     0xb557b4: ldur            w1, [x16, #0xf]
    // 0xb557b8: DecompressPointer r1
    //     0xb557b8: add             x1, x1, HEAP, lsl #32
    // 0xb557bc: LoadField: r0 = r1->field_b
    //     0xb557bc: ldur            w0, [x1, #0xb]
    // 0xb557c0: DecompressPointer r0
    //     0xb557c0: add             x0, x0, HEAP, lsl #32
    // 0xb557c4: cmp             w0, NULL
    // 0xb557c8: b.ne            #0xb557d8
    // 0xb557cc: r3 = "above"
    //     0xb557cc: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xb557d0: ldr             x3, [x3, #0xc10]
    // 0xb557d4: b               #0xb557dc
    // 0xb557d8: mov             x3, x0
    // 0xb557dc: ldur            x0, [fp, #-8]
    // 0xb557e0: ldur            x1, [fp, #-0x28]
    // 0xb557e4: ArrayStore: r2[0] = r3  ; List_4
    //     0xb557e4: stur            w3, [x2, #0x17]
    // 0xb557e8: str             x2, [SP]
    // 0xb557ec: r0 = _interpolate()
    //     0xb557ec: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb557f0: ldur            x3, [fp, #-0x28]
    // 0xb557f4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb557f4: stur            w0, [x3, #0x17]
    //     0xb557f8: ldurb           w16, [x3, #-1]
    //     0xb557fc: ldurb           w17, [x0, #-1]
    //     0xb55800: and             x16, x17, x16, lsr #2
    //     0xb55804: tst             x16, HEAP, lsr #32
    //     0xb55808: b.eq            #0xb55810
    //     0xb5580c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb55810: ldur            x0, [fp, #-8]
    // 0xb55814: LoadField: r1 = r0->field_13
    //     0xb55814: ldur            w1, [x0, #0x13]
    // 0xb55818: DecompressPointer r1
    //     0xb55818: add             x1, x1, HEAP, lsl #32
    // 0xb5581c: ldur            x2, [fp, #-0x30]
    // 0xb55820: r0 = contains()
    //     0xb55820: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb55824: stur            x0, [fp, #-8]
    // 0xb55828: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb55828: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb5582c: ldr             x0, [x0, #0x1c80]
    //     0xb55830: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb55834: cmp             w0, w16
    //     0xb55838: b.ne            #0xb55844
    //     0xb5583c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb55840: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb55844: r0 = GetNavigation.context()
    //     0xb55844: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xb55848: cmp             w0, NULL
    // 0xb5584c: b.eq            #0xb558f8
    // 0xb55850: mov             x1, x0
    // 0xb55854: r0 = of()
    //     0xb55854: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb55858: LoadField: r1 = r0->field_5b
    //     0xb55858: ldur            w1, [x0, #0x5b]
    // 0xb5585c: DecompressPointer r1
    //     0xb5585c: add             x1, x1, HEAP, lsl #32
    // 0xb55860: stur            x1, [fp, #-0x10]
    // 0xb55864: r0 = Checkbox()
    //     0xb55864: bl              #0xa2e5f0  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xb55868: mov             x3, x0
    // 0xb5586c: ldur            x0, [fp, #-8]
    // 0xb55870: stur            x3, [fp, #-0x18]
    // 0xb55874: StoreField: r3->field_b = r0
    //     0xb55874: stur            w0, [x3, #0xb]
    // 0xb55878: r0 = false
    //     0xb55878: add             x0, NULL, #0x30  ; false
    // 0xb5587c: StoreField: r3->field_23 = r0
    //     0xb5587c: stur            w0, [x3, #0x23]
    // 0xb55880: ldur            x2, [fp, #-0x28]
    // 0xb55884: r1 = Function '<anonymous closure>':.
    //     0xb55884: add             x1, PP, #0x56, lsl #12  ; [pp+0x56580] AnonymousClosure: (0xb558fc), in [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem (0xb551f8)
    //     0xb55888: ldr             x1, [x1, #0x580]
    // 0xb5588c: r0 = AllocateClosure()
    //     0xb5588c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb55890: mov             x1, x0
    // 0xb55894: ldur            x0, [fp, #-0x18]
    // 0xb55898: StoreField: r0->field_f = r1
    //     0xb55898: stur            w1, [x0, #0xf]
    // 0xb5589c: ldur            x1, [fp, #-0x10]
    // 0xb558a0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb558a0: stur            w1, [x0, #0x17]
    // 0xb558a4: r1 = false
    //     0xb558a4: add             x1, NULL, #0x30  ; false
    // 0xb558a8: StoreField: r0->field_43 = r1
    //     0xb558a8: stur            w1, [x0, #0x43]
    // 0xb558ac: StoreField: r0->field_4f = r1
    //     0xb558ac: stur            w1, [x0, #0x4f]
    // 0xb558b0: r1 = Instance__CheckboxType
    //     0xb558b0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d80] Obj!_CheckboxType@d74601
    //     0xb558b4: ldr             x1, [x1, #0xd80]
    // 0xb558b8: StoreField: r0->field_57 = r1
    //     0xb558b8: stur            w1, [x0, #0x57]
    // 0xb558bc: LeaveFrame
    //     0xb558bc: mov             SP, fp
    //     0xb558c0: ldp             fp, lr, [SP], #0x10
    // 0xb558c4: ret
    //     0xb558c4: ret             
    // 0xb558c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb558c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb558cc: b               #0xb55228
    // 0xb558d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558d0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb558f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb558f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb558f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool?) {
    // ** addr: 0xb558fc, size: 0x68
    // 0xb558fc: EnterFrame
    //     0xb558fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb55900: mov             fp, SP
    // 0xb55904: ldr             x0, [fp, #0x18]
    // 0xb55908: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb55908: ldur            w1, [x0, #0x17]
    // 0xb5590c: DecompressPointer r1
    //     0xb5590c: add             x1, x1, HEAP, lsl #32
    // 0xb55910: CheckStackOverflow
    //     0xb55910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb55914: cmp             SP, x16
    //     0xb55918: b.ls            #0xb55958
    // 0xb5591c: LoadField: r0 = r1->field_f
    //     0xb5591c: ldur            w0, [x1, #0xf]
    // 0xb55920: DecompressPointer r0
    //     0xb55920: add             x0, x0, HEAP, lsl #32
    // 0xb55924: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb55924: ldur            w2, [x1, #0x17]
    // 0xb55928: DecompressPointer r2
    //     0xb55928: add             x2, x2, HEAP, lsl #32
    // 0xb5592c: ldr             x3, [fp, #0x10]
    // 0xb55930: cmp             w3, NULL
    // 0xb55934: b.eq            #0xb55960
    // 0xb55938: LoadField: r5 = r1->field_13
    //     0xb55938: ldur            w5, [x1, #0x13]
    // 0xb5593c: DecompressPointer r5
    //     0xb5593c: add             x5, x5, HEAP, lsl #32
    // 0xb55940: mov             x1, x0
    // 0xb55944: r0 = _onItemCheckedChange()
    //     0xb55944: bl              #0xb55964  ; [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::_onItemCheckedChange
    // 0xb55948: r0 = Null
    //     0xb55948: mov             x0, NULL
    // 0xb5594c: LeaveFrame
    //     0xb5594c: mov             SP, fp
    //     0xb55950: ldp             fp, lr, [SP], #0x10
    // 0xb55954: ret
    //     0xb55954: ret             
    // 0xb55958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb55958: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5595c: b               #0xb5591c
    // 0xb55960: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb55960: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _onItemCheckedChange(/* No info */) {
    // ** addr: 0xb55964, size: 0x88
    // 0xb55964: EnterFrame
    //     0xb55964: stp             fp, lr, [SP, #-0x10]!
    //     0xb55968: mov             fp, SP
    // 0xb5596c: AllocStack(0x20)
    //     0xb5596c: sub             SP, SP, #0x20
    // 0xb55970: SetupParameters(_CollectionFilterState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xb55970: stur            x1, [fp, #-8]
    //     0xb55974: stur            x2, [fp, #-0x10]
    //     0xb55978: stur            x3, [fp, #-0x18]
    //     0xb5597c: stur            x5, [fp, #-0x20]
    // 0xb55980: CheckStackOverflow
    //     0xb55980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb55984: cmp             SP, x16
    //     0xb55988: b.ls            #0xb559e4
    // 0xb5598c: r1 = 4
    //     0xb5598c: movz            x1, #0x4
    // 0xb55990: r0 = AllocateContext()
    //     0xb55990: bl              #0x16f6108  ; AllocateContextStub
    // 0xb55994: mov             x1, x0
    // 0xb55998: ldur            x0, [fp, #-8]
    // 0xb5599c: StoreField: r1->field_f = r0
    //     0xb5599c: stur            w0, [x1, #0xf]
    // 0xb559a0: ldur            x2, [fp, #-0x10]
    // 0xb559a4: StoreField: r1->field_13 = r2
    //     0xb559a4: stur            w2, [x1, #0x13]
    // 0xb559a8: ldur            x2, [fp, #-0x18]
    // 0xb559ac: ArrayStore: r1[0] = r2  ; List_4
    //     0xb559ac: stur            w2, [x1, #0x17]
    // 0xb559b0: ldur            x2, [fp, #-0x20]
    // 0xb559b4: StoreField: r1->field_1b = r2
    //     0xb559b4: stur            w2, [x1, #0x1b]
    // 0xb559b8: mov             x2, x1
    // 0xb559bc: r1 = Function '<anonymous closure>':.
    //     0xb559bc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56588] AnonymousClosure: (0xa2e6ec), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_onItemCheckedChange (0xa2ed14)
    //     0xb559c0: ldr             x1, [x1, #0x588]
    // 0xb559c4: r0 = AllocateClosure()
    //     0xb559c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb559c8: ldur            x1, [fp, #-8]
    // 0xb559cc: mov             x2, x0
    // 0xb559d0: r0 = setState()
    //     0xb559d0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb559d4: r0 = Null
    //     0xb559d4: mov             x0, NULL
    // 0xb559d8: LeaveFrame
    //     0xb559d8: mov             SP, fp
    //     0xb559dc: ldp             fp, lr, [SP], #0x10
    // 0xb559e0: ret
    //     0xb559e0: ret             
    // 0xb559e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb559e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb559e8: b               #0xb5598c
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb559ec, size: 0x38c
    // 0xb559ec: EnterFrame
    //     0xb559ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb559f0: mov             fp, SP
    // 0xb559f4: AllocStack(0x40)
    //     0xb559f4: sub             SP, SP, #0x40
    // 0xb559f8: SetupParameters()
    //     0xb559f8: ldr             x0, [fp, #0x20]
    //     0xb559fc: ldur            w4, [x0, #0x17]
    //     0xb55a00: add             x4, x4, HEAP, lsl #32
    //     0xb55a04: stur            x4, [fp, #-0x10]
    // 0xb55a08: CheckStackOverflow
    //     0xb55a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb55a0c: cmp             SP, x16
    //     0xb55a10: b.ls            #0xb55d5c
    // 0xb55a14: LoadField: r1 = r4->field_f
    //     0xb55a14: ldur            w1, [x4, #0xf]
    // 0xb55a18: DecompressPointer r1
    //     0xb55a18: add             x1, x1, HEAP, lsl #32
    // 0xb55a1c: LoadField: r0 = r1->field_b
    //     0xb55a1c: ldur            w0, [x1, #0xb]
    // 0xb55a20: DecompressPointer r0
    //     0xb55a20: add             x0, x0, HEAP, lsl #32
    // 0xb55a24: cmp             w0, NULL
    // 0xb55a28: b.eq            #0xb55d64
    // 0xb55a2c: LoadField: r2 = r0->field_b
    //     0xb55a2c: ldur            w2, [x0, #0xb]
    // 0xb55a30: DecompressPointer r2
    //     0xb55a30: add             x2, x2, HEAP, lsl #32
    // 0xb55a34: ldr             x0, [fp, #0x10]
    // 0xb55a38: r6 = LoadInt32Instr(r0)
    //     0xb55a38: sbfx            x6, x0, #1, #0x1f
    //     0xb55a3c: tbz             w0, #0, #0xb55a44
    //     0xb55a40: ldur            x6, [x0, #7]
    // 0xb55a44: mov             x5, x6
    // 0xb55a48: stur            x6, [fp, #-8]
    // 0xb55a4c: r3 = Instance_ItemFilterType
    //     0xb55a4c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56590] Obj!ItemFilterType@d75341
    //     0xb55a50: ldr             x3, [x3, #0x590]
    // 0xb55a54: r0 = _productBuildItem()
    //     0xb55a54: bl              #0xb551f8  ; [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xb55a58: mov             x3, x0
    // 0xb55a5c: ldur            x2, [fp, #-0x10]
    // 0xb55a60: stur            x3, [fp, #-0x20]
    // 0xb55a64: LoadField: r0 = r2->field_f
    //     0xb55a64: ldur            w0, [x2, #0xf]
    // 0xb55a68: DecompressPointer r0
    //     0xb55a68: add             x0, x0, HEAP, lsl #32
    // 0xb55a6c: LoadField: r1 = r0->field_b
    //     0xb55a6c: ldur            w1, [x0, #0xb]
    // 0xb55a70: DecompressPointer r1
    //     0xb55a70: add             x1, x1, HEAP, lsl #32
    // 0xb55a74: cmp             w1, NULL
    // 0xb55a78: b.eq            #0xb55d68
    // 0xb55a7c: LoadField: r0 = r1->field_b
    //     0xb55a7c: ldur            w0, [x1, #0xb]
    // 0xb55a80: DecompressPointer r0
    //     0xb55a80: add             x0, x0, HEAP, lsl #32
    // 0xb55a84: cmp             w0, NULL
    // 0xb55a88: b.ne            #0xb55a98
    // 0xb55a8c: ldur            x5, [fp, #-8]
    // 0xb55a90: r0 = Null
    //     0xb55a90: mov             x0, NULL
    // 0xb55a94: b               #0xb55aec
    // 0xb55a98: LoadField: r4 = r0->field_b
    //     0xb55a98: ldur            w4, [x0, #0xb]
    // 0xb55a9c: DecompressPointer r4
    //     0xb55a9c: add             x4, x4, HEAP, lsl #32
    // 0xb55aa0: cmp             w4, NULL
    // 0xb55aa4: b.ne            #0xb55ab4
    // 0xb55aa8: ldur            x5, [fp, #-8]
    // 0xb55aac: r0 = Null
    //     0xb55aac: mov             x0, NULL
    // 0xb55ab0: b               #0xb55aec
    // 0xb55ab4: ldur            x5, [fp, #-8]
    // 0xb55ab8: LoadField: r0 = r4->field_b
    //     0xb55ab8: ldur            w0, [x4, #0xb]
    // 0xb55abc: r1 = LoadInt32Instr(r0)
    //     0xb55abc: sbfx            x1, x0, #1, #0x1f
    // 0xb55ac0: mov             x0, x1
    // 0xb55ac4: mov             x1, x5
    // 0xb55ac8: cmp             x1, x0
    // 0xb55acc: b.hs            #0xb55d6c
    // 0xb55ad0: LoadField: r0 = r4->field_f
    //     0xb55ad0: ldur            w0, [x4, #0xf]
    // 0xb55ad4: DecompressPointer r0
    //     0xb55ad4: add             x0, x0, HEAP, lsl #32
    // 0xb55ad8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55ad8: add             x16, x0, x5, lsl #2
    //     0xb55adc: ldur            w1, [x16, #0xf]
    // 0xb55ae0: DecompressPointer r1
    //     0xb55ae0: add             x1, x1, HEAP, lsl #32
    // 0xb55ae4: LoadField: r0 = r1->field_7
    //     0xb55ae4: ldur            w0, [x1, #7]
    // 0xb55ae8: DecompressPointer r0
    //     0xb55ae8: add             x0, x0, HEAP, lsl #32
    // 0xb55aec: cmp             w0, NULL
    // 0xb55af0: b.ne            #0xb55af8
    // 0xb55af4: r0 = ""
    //     0xb55af4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55af8: ldr             x1, [fp, #0x18]
    // 0xb55afc: stur            x0, [fp, #-0x18]
    // 0xb55b00: r0 = of()
    //     0xb55b00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb55b04: LoadField: r1 = r0->field_87
    //     0xb55b04: ldur            w1, [x0, #0x87]
    // 0xb55b08: DecompressPointer r1
    //     0xb55b08: add             x1, x1, HEAP, lsl #32
    // 0xb55b0c: LoadField: r0 = r1->field_2b
    //     0xb55b0c: ldur            w0, [x1, #0x2b]
    // 0xb55b10: DecompressPointer r0
    //     0xb55b10: add             x0, x0, HEAP, lsl #32
    // 0xb55b14: r16 = 14.000000
    //     0xb55b14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb55b18: ldr             x16, [x16, #0x1d8]
    // 0xb55b1c: r30 = Instance_Color
    //     0xb55b1c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb55b20: stp             lr, x16, [SP]
    // 0xb55b24: mov             x1, x0
    // 0xb55b28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb55b28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb55b2c: ldr             x4, [x4, #0xaa0]
    // 0xb55b30: r0 = copyWith()
    //     0xb55b30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb55b34: stur            x0, [fp, #-0x28]
    // 0xb55b38: r0 = Text()
    //     0xb55b38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb55b3c: mov             x2, x0
    // 0xb55b40: ldur            x0, [fp, #-0x18]
    // 0xb55b44: stur            x2, [fp, #-0x30]
    // 0xb55b48: StoreField: r2->field_b = r0
    //     0xb55b48: stur            w0, [x2, #0xb]
    // 0xb55b4c: ldur            x0, [fp, #-0x28]
    // 0xb55b50: StoreField: r2->field_13 = r0
    //     0xb55b50: stur            w0, [x2, #0x13]
    // 0xb55b54: ldur            x0, [fp, #-0x10]
    // 0xb55b58: LoadField: r1 = r0->field_f
    //     0xb55b58: ldur            w1, [x0, #0xf]
    // 0xb55b5c: DecompressPointer r1
    //     0xb55b5c: add             x1, x1, HEAP, lsl #32
    // 0xb55b60: LoadField: r0 = r1->field_b
    //     0xb55b60: ldur            w0, [x1, #0xb]
    // 0xb55b64: DecompressPointer r0
    //     0xb55b64: add             x0, x0, HEAP, lsl #32
    // 0xb55b68: cmp             w0, NULL
    // 0xb55b6c: b.eq            #0xb55d70
    // 0xb55b70: LoadField: r1 = r0->field_b
    //     0xb55b70: ldur            w1, [x0, #0xb]
    // 0xb55b74: DecompressPointer r1
    //     0xb55b74: add             x1, x1, HEAP, lsl #32
    // 0xb55b78: cmp             w1, NULL
    // 0xb55b7c: b.ne            #0xb55b88
    // 0xb55b80: r0 = Null
    //     0xb55b80: mov             x0, NULL
    // 0xb55b84: b               #0xb55c04
    // 0xb55b88: LoadField: r3 = r1->field_b
    //     0xb55b88: ldur            w3, [x1, #0xb]
    // 0xb55b8c: DecompressPointer r3
    //     0xb55b8c: add             x3, x3, HEAP, lsl #32
    // 0xb55b90: cmp             w3, NULL
    // 0xb55b94: b.ne            #0xb55ba0
    // 0xb55b98: r0 = Null
    //     0xb55b98: mov             x0, NULL
    // 0xb55b9c: b               #0xb55c04
    // 0xb55ba0: ldur            x4, [fp, #-8]
    // 0xb55ba4: LoadField: r0 = r3->field_b
    //     0xb55ba4: ldur            w0, [x3, #0xb]
    // 0xb55ba8: r1 = LoadInt32Instr(r0)
    //     0xb55ba8: sbfx            x1, x0, #1, #0x1f
    // 0xb55bac: mov             x0, x1
    // 0xb55bb0: mov             x1, x4
    // 0xb55bb4: cmp             x1, x0
    // 0xb55bb8: b.hs            #0xb55d74
    // 0xb55bbc: LoadField: r0 = r3->field_f
    //     0xb55bbc: ldur            w0, [x3, #0xf]
    // 0xb55bc0: DecompressPointer r0
    //     0xb55bc0: add             x0, x0, HEAP, lsl #32
    // 0xb55bc4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb55bc4: add             x16, x0, x4, lsl #2
    //     0xb55bc8: ldur            w1, [x16, #0xf]
    // 0xb55bcc: DecompressPointer r1
    //     0xb55bcc: add             x1, x1, HEAP, lsl #32
    // 0xb55bd0: LoadField: r0 = r1->field_b
    //     0xb55bd0: ldur            w0, [x1, #0xb]
    // 0xb55bd4: DecompressPointer r0
    //     0xb55bd4: add             x0, x0, HEAP, lsl #32
    // 0xb55bd8: r1 = 60
    //     0xb55bd8: movz            x1, #0x3c
    // 0xb55bdc: branchIfSmi(r0, 0xb55be8)
    //     0xb55bdc: tbz             w0, #0, #0xb55be8
    // 0xb55be0: r1 = LoadClassIdInstr(r0)
    //     0xb55be0: ldur            x1, [x0, #-1]
    //     0xb55be4: ubfx            x1, x1, #0xc, #0x14
    // 0xb55be8: str             x0, [SP]
    // 0xb55bec: mov             x0, x1
    // 0xb55bf0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb55bf0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb55bf4: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb55bf4: movz            x17, #0x2700
    //     0xb55bf8: add             lr, x0, x17
    //     0xb55bfc: ldr             lr, [x21, lr, lsl #3]
    //     0xb55c00: blr             lr
    // 0xb55c04: cmp             w0, NULL
    // 0xb55c08: b.ne            #0xb55c14
    // 0xb55c0c: r3 = ""
    //     0xb55c0c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55c10: b               #0xb55c18
    // 0xb55c14: mov             x3, x0
    // 0xb55c18: ldur            x2, [fp, #-0x20]
    // 0xb55c1c: ldur            x0, [fp, #-0x30]
    // 0xb55c20: ldr             x1, [fp, #0x18]
    // 0xb55c24: stur            x3, [fp, #-0x10]
    // 0xb55c28: r0 = of()
    //     0xb55c28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb55c2c: LoadField: r1 = r0->field_87
    //     0xb55c2c: ldur            w1, [x0, #0x87]
    // 0xb55c30: DecompressPointer r1
    //     0xb55c30: add             x1, x1, HEAP, lsl #32
    // 0xb55c34: LoadField: r0 = r1->field_2b
    //     0xb55c34: ldur            w0, [x1, #0x2b]
    // 0xb55c38: DecompressPointer r0
    //     0xb55c38: add             x0, x0, HEAP, lsl #32
    // 0xb55c3c: ldr             x1, [fp, #0x18]
    // 0xb55c40: stur            x0, [fp, #-0x18]
    // 0xb55c44: r0 = of()
    //     0xb55c44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb55c48: LoadField: r1 = r0->field_5b
    //     0xb55c48: ldur            w1, [x0, #0x5b]
    // 0xb55c4c: DecompressPointer r1
    //     0xb55c4c: add             x1, x1, HEAP, lsl #32
    // 0xb55c50: r0 = LoadClassIdInstr(r1)
    //     0xb55c50: ldur            x0, [x1, #-1]
    //     0xb55c54: ubfx            x0, x0, #0xc, #0x14
    // 0xb55c58: d0 = 0.400000
    //     0xb55c58: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb55c5c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb55c5c: sub             lr, x0, #0xffa
    //     0xb55c60: ldr             lr, [x21, lr, lsl #3]
    //     0xb55c64: blr             lr
    // 0xb55c68: r16 = 12.000000
    //     0xb55c68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb55c6c: ldr             x16, [x16, #0x9e8]
    // 0xb55c70: stp             x0, x16, [SP]
    // 0xb55c74: ldur            x1, [fp, #-0x18]
    // 0xb55c78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb55c78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb55c7c: ldr             x4, [x4, #0xaa0]
    // 0xb55c80: r0 = copyWith()
    //     0xb55c80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb55c84: stur            x0, [fp, #-0x18]
    // 0xb55c88: r0 = Text()
    //     0xb55c88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb55c8c: mov             x3, x0
    // 0xb55c90: ldur            x0, [fp, #-0x10]
    // 0xb55c94: stur            x3, [fp, #-0x28]
    // 0xb55c98: StoreField: r3->field_b = r0
    //     0xb55c98: stur            w0, [x3, #0xb]
    // 0xb55c9c: ldur            x0, [fp, #-0x18]
    // 0xb55ca0: StoreField: r3->field_13 = r0
    //     0xb55ca0: stur            w0, [x3, #0x13]
    // 0xb55ca4: r1 = Null
    //     0xb55ca4: mov             x1, NULL
    // 0xb55ca8: r2 = 8
    //     0xb55ca8: movz            x2, #0x8
    // 0xb55cac: r0 = AllocateArray()
    //     0xb55cac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb55cb0: mov             x2, x0
    // 0xb55cb4: ldur            x0, [fp, #-0x20]
    // 0xb55cb8: stur            x2, [fp, #-0x10]
    // 0xb55cbc: StoreField: r2->field_f = r0
    //     0xb55cbc: stur            w0, [x2, #0xf]
    // 0xb55cc0: ldur            x0, [fp, #-0x30]
    // 0xb55cc4: StoreField: r2->field_13 = r0
    //     0xb55cc4: stur            w0, [x2, #0x13]
    // 0xb55cc8: r16 = Instance_Spacer
    //     0xb55cc8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb55ccc: ldr             x16, [x16, #0xf0]
    // 0xb55cd0: ArrayStore: r2[0] = r16  ; List_4
    //     0xb55cd0: stur            w16, [x2, #0x17]
    // 0xb55cd4: ldur            x0, [fp, #-0x28]
    // 0xb55cd8: StoreField: r2->field_1b = r0
    //     0xb55cd8: stur            w0, [x2, #0x1b]
    // 0xb55cdc: r1 = <Widget>
    //     0xb55cdc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb55ce0: r0 = AllocateGrowableArray()
    //     0xb55ce0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb55ce4: mov             x1, x0
    // 0xb55ce8: ldur            x0, [fp, #-0x10]
    // 0xb55cec: stur            x1, [fp, #-0x18]
    // 0xb55cf0: StoreField: r1->field_f = r0
    //     0xb55cf0: stur            w0, [x1, #0xf]
    // 0xb55cf4: r0 = 8
    //     0xb55cf4: movz            x0, #0x8
    // 0xb55cf8: StoreField: r1->field_b = r0
    //     0xb55cf8: stur            w0, [x1, #0xb]
    // 0xb55cfc: r0 = Row()
    //     0xb55cfc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb55d00: r1 = Instance_Axis
    //     0xb55d00: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb55d04: StoreField: r0->field_f = r1
    //     0xb55d04: stur            w1, [x0, #0xf]
    // 0xb55d08: r1 = Instance_MainAxisAlignment
    //     0xb55d08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb55d0c: ldr             x1, [x1, #0xa08]
    // 0xb55d10: StoreField: r0->field_13 = r1
    //     0xb55d10: stur            w1, [x0, #0x13]
    // 0xb55d14: r1 = Instance_MainAxisSize
    //     0xb55d14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb55d18: ldr             x1, [x1, #0xa10]
    // 0xb55d1c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb55d1c: stur            w1, [x0, #0x17]
    // 0xb55d20: r1 = Instance_CrossAxisAlignment
    //     0xb55d20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb55d24: ldr             x1, [x1, #0xa18]
    // 0xb55d28: StoreField: r0->field_1b = r1
    //     0xb55d28: stur            w1, [x0, #0x1b]
    // 0xb55d2c: r1 = Instance_VerticalDirection
    //     0xb55d2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb55d30: ldr             x1, [x1, #0xa20]
    // 0xb55d34: StoreField: r0->field_23 = r1
    //     0xb55d34: stur            w1, [x0, #0x23]
    // 0xb55d38: r1 = Instance_Clip
    //     0xb55d38: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb55d3c: ldr             x1, [x1, #0x38]
    // 0xb55d40: StoreField: r0->field_2b = r1
    //     0xb55d40: stur            w1, [x0, #0x2b]
    // 0xb55d44: StoreField: r0->field_2f = rZR
    //     0xb55d44: stur            xzr, [x0, #0x2f]
    // 0xb55d48: ldur            x1, [fp, #-0x18]
    // 0xb55d4c: StoreField: r0->field_b = r1
    //     0xb55d4c: stur            w1, [x0, #0xb]
    // 0xb55d50: LeaveFrame
    //     0xb55d50: mov             SP, fp
    //     0xb55d54: ldp             fp, lr, [SP], #0x10
    // 0xb55d58: ret
    //     0xb55d58: ret             
    // 0xb55d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb55d5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb55d60: b               #0xb55a14
    // 0xb55d64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb55d64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb55d68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb55d68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb55d6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb55d6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb55d70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb55d70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb55d74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb55d74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb55d78, size: 0x370
    // 0xb55d78: EnterFrame
    //     0xb55d78: stp             fp, lr, [SP, #-0x10]!
    //     0xb55d7c: mov             fp, SP
    // 0xb55d80: AllocStack(0x40)
    //     0xb55d80: sub             SP, SP, #0x40
    // 0xb55d84: SetupParameters()
    //     0xb55d84: ldr             x0, [fp, #0x20]
    //     0xb55d88: ldur            w4, [x0, #0x17]
    //     0xb55d8c: add             x4, x4, HEAP, lsl #32
    //     0xb55d90: stur            x4, [fp, #-0x10]
    // 0xb55d94: CheckStackOverflow
    //     0xb55d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb55d98: cmp             SP, x16
    //     0xb55d9c: b.ls            #0xb560cc
    // 0xb55da0: LoadField: r1 = r4->field_f
    //     0xb55da0: ldur            w1, [x4, #0xf]
    // 0xb55da4: DecompressPointer r1
    //     0xb55da4: add             x1, x1, HEAP, lsl #32
    // 0xb55da8: LoadField: r0 = r1->field_b
    //     0xb55da8: ldur            w0, [x1, #0xb]
    // 0xb55dac: DecompressPointer r0
    //     0xb55dac: add             x0, x0, HEAP, lsl #32
    // 0xb55db0: cmp             w0, NULL
    // 0xb55db4: b.eq            #0xb560d4
    // 0xb55db8: LoadField: r2 = r0->field_b
    //     0xb55db8: ldur            w2, [x0, #0xb]
    // 0xb55dbc: DecompressPointer r2
    //     0xb55dbc: add             x2, x2, HEAP, lsl #32
    // 0xb55dc0: ldr             x0, [fp, #0x10]
    // 0xb55dc4: r6 = LoadInt32Instr(r0)
    //     0xb55dc4: sbfx            x6, x0, #1, #0x1f
    //     0xb55dc8: tbz             w0, #0, #0xb55dd0
    //     0xb55dcc: ldur            x6, [x0, #7]
    // 0xb55dd0: mov             x5, x6
    // 0xb55dd4: stur            x6, [fp, #-8]
    // 0xb55dd8: r3 = Instance_ItemFilterType
    //     0xb55dd8: add             x3, PP, #0x56, lsl #12  ; [pp+0x56598] Obj!ItemFilterType@d75361
    //     0xb55ddc: ldr             x3, [x3, #0x598]
    // 0xb55de0: r0 = _productBuildItem()
    //     0xb55de0: bl              #0xb551f8  ; [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xb55de4: mov             x3, x0
    // 0xb55de8: ldur            x2, [fp, #-0x10]
    // 0xb55dec: stur            x3, [fp, #-0x20]
    // 0xb55df0: LoadField: r0 = r2->field_f
    //     0xb55df0: ldur            w0, [x2, #0xf]
    // 0xb55df4: DecompressPointer r0
    //     0xb55df4: add             x0, x0, HEAP, lsl #32
    // 0xb55df8: LoadField: r1 = r0->field_b
    //     0xb55df8: ldur            w1, [x0, #0xb]
    // 0xb55dfc: DecompressPointer r1
    //     0xb55dfc: add             x1, x1, HEAP, lsl #32
    // 0xb55e00: cmp             w1, NULL
    // 0xb55e04: b.eq            #0xb560d8
    // 0xb55e08: LoadField: r0 = r1->field_b
    //     0xb55e08: ldur            w0, [x1, #0xb]
    // 0xb55e0c: DecompressPointer r0
    //     0xb55e0c: add             x0, x0, HEAP, lsl #32
    // 0xb55e10: cmp             w0, NULL
    // 0xb55e14: b.ne            #0xb55e24
    // 0xb55e18: ldur            x5, [fp, #-8]
    // 0xb55e1c: r0 = Null
    //     0xb55e1c: mov             x0, NULL
    // 0xb55e20: b               #0xb55e78
    // 0xb55e24: LoadField: r4 = r0->field_f
    //     0xb55e24: ldur            w4, [x0, #0xf]
    // 0xb55e28: DecompressPointer r4
    //     0xb55e28: add             x4, x4, HEAP, lsl #32
    // 0xb55e2c: cmp             w4, NULL
    // 0xb55e30: b.ne            #0xb55e40
    // 0xb55e34: ldur            x5, [fp, #-8]
    // 0xb55e38: r0 = Null
    //     0xb55e38: mov             x0, NULL
    // 0xb55e3c: b               #0xb55e78
    // 0xb55e40: ldur            x5, [fp, #-8]
    // 0xb55e44: LoadField: r0 = r4->field_b
    //     0xb55e44: ldur            w0, [x4, #0xb]
    // 0xb55e48: r1 = LoadInt32Instr(r0)
    //     0xb55e48: sbfx            x1, x0, #1, #0x1f
    // 0xb55e4c: mov             x0, x1
    // 0xb55e50: mov             x1, x5
    // 0xb55e54: cmp             x1, x0
    // 0xb55e58: b.hs            #0xb560dc
    // 0xb55e5c: LoadField: r0 = r4->field_f
    //     0xb55e5c: ldur            w0, [x4, #0xf]
    // 0xb55e60: DecompressPointer r0
    //     0xb55e60: add             x0, x0, HEAP, lsl #32
    // 0xb55e64: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb55e64: add             x16, x0, x5, lsl #2
    //     0xb55e68: ldur            w1, [x16, #0xf]
    // 0xb55e6c: DecompressPointer r1
    //     0xb55e6c: add             x1, x1, HEAP, lsl #32
    // 0xb55e70: LoadField: r0 = r1->field_7
    //     0xb55e70: ldur            w0, [x1, #7]
    // 0xb55e74: DecompressPointer r0
    //     0xb55e74: add             x0, x0, HEAP, lsl #32
    // 0xb55e78: cmp             w0, NULL
    // 0xb55e7c: b.ne            #0xb55e84
    // 0xb55e80: r0 = ""
    //     0xb55e80: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55e84: ldr             x1, [fp, #0x18]
    // 0xb55e88: stur            x0, [fp, #-0x18]
    // 0xb55e8c: r0 = of()
    //     0xb55e8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb55e90: LoadField: r1 = r0->field_87
    //     0xb55e90: ldur            w1, [x0, #0x87]
    // 0xb55e94: DecompressPointer r1
    //     0xb55e94: add             x1, x1, HEAP, lsl #32
    // 0xb55e98: LoadField: r0 = r1->field_2b
    //     0xb55e98: ldur            w0, [x1, #0x2b]
    // 0xb55e9c: DecompressPointer r0
    //     0xb55e9c: add             x0, x0, HEAP, lsl #32
    // 0xb55ea0: r16 = 14.000000
    //     0xb55ea0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb55ea4: ldr             x16, [x16, #0x1d8]
    // 0xb55ea8: r30 = Instance_Color
    //     0xb55ea8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb55eac: stp             lr, x16, [SP]
    // 0xb55eb0: mov             x1, x0
    // 0xb55eb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb55eb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb55eb8: ldr             x4, [x4, #0xaa0]
    // 0xb55ebc: r0 = copyWith()
    //     0xb55ebc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb55ec0: stur            x0, [fp, #-0x28]
    // 0xb55ec4: r0 = Text()
    //     0xb55ec4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb55ec8: mov             x2, x0
    // 0xb55ecc: ldur            x0, [fp, #-0x18]
    // 0xb55ed0: stur            x2, [fp, #-0x30]
    // 0xb55ed4: StoreField: r2->field_b = r0
    //     0xb55ed4: stur            w0, [x2, #0xb]
    // 0xb55ed8: ldur            x0, [fp, #-0x28]
    // 0xb55edc: StoreField: r2->field_13 = r0
    //     0xb55edc: stur            w0, [x2, #0x13]
    // 0xb55ee0: ldur            x0, [fp, #-0x10]
    // 0xb55ee4: LoadField: r1 = r0->field_f
    //     0xb55ee4: ldur            w1, [x0, #0xf]
    // 0xb55ee8: DecompressPointer r1
    //     0xb55ee8: add             x1, x1, HEAP, lsl #32
    // 0xb55eec: LoadField: r0 = r1->field_b
    //     0xb55eec: ldur            w0, [x1, #0xb]
    // 0xb55ef0: DecompressPointer r0
    //     0xb55ef0: add             x0, x0, HEAP, lsl #32
    // 0xb55ef4: cmp             w0, NULL
    // 0xb55ef8: b.eq            #0xb560e0
    // 0xb55efc: LoadField: r1 = r0->field_b
    //     0xb55efc: ldur            w1, [x0, #0xb]
    // 0xb55f00: DecompressPointer r1
    //     0xb55f00: add             x1, x1, HEAP, lsl #32
    // 0xb55f04: cmp             w1, NULL
    // 0xb55f08: b.ne            #0xb55f14
    // 0xb55f0c: r0 = Null
    //     0xb55f0c: mov             x0, NULL
    // 0xb55f10: b               #0xb55f90
    // 0xb55f14: LoadField: r3 = r1->field_f
    //     0xb55f14: ldur            w3, [x1, #0xf]
    // 0xb55f18: DecompressPointer r3
    //     0xb55f18: add             x3, x3, HEAP, lsl #32
    // 0xb55f1c: cmp             w3, NULL
    // 0xb55f20: b.ne            #0xb55f2c
    // 0xb55f24: r0 = Null
    //     0xb55f24: mov             x0, NULL
    // 0xb55f28: b               #0xb55f90
    // 0xb55f2c: ldur            x4, [fp, #-8]
    // 0xb55f30: LoadField: r0 = r3->field_b
    //     0xb55f30: ldur            w0, [x3, #0xb]
    // 0xb55f34: r1 = LoadInt32Instr(r0)
    //     0xb55f34: sbfx            x1, x0, #1, #0x1f
    // 0xb55f38: mov             x0, x1
    // 0xb55f3c: mov             x1, x4
    // 0xb55f40: cmp             x1, x0
    // 0xb55f44: b.hs            #0xb560e4
    // 0xb55f48: LoadField: r0 = r3->field_f
    //     0xb55f48: ldur            w0, [x3, #0xf]
    // 0xb55f4c: DecompressPointer r0
    //     0xb55f4c: add             x0, x0, HEAP, lsl #32
    // 0xb55f50: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb55f50: add             x16, x0, x4, lsl #2
    //     0xb55f54: ldur            w1, [x16, #0xf]
    // 0xb55f58: DecompressPointer r1
    //     0xb55f58: add             x1, x1, HEAP, lsl #32
    // 0xb55f5c: LoadField: r0 = r1->field_b
    //     0xb55f5c: ldur            w0, [x1, #0xb]
    // 0xb55f60: DecompressPointer r0
    //     0xb55f60: add             x0, x0, HEAP, lsl #32
    // 0xb55f64: r1 = 60
    //     0xb55f64: movz            x1, #0x3c
    // 0xb55f68: branchIfSmi(r0, 0xb55f74)
    //     0xb55f68: tbz             w0, #0, #0xb55f74
    // 0xb55f6c: r1 = LoadClassIdInstr(r0)
    //     0xb55f6c: ldur            x1, [x0, #-1]
    //     0xb55f70: ubfx            x1, x1, #0xc, #0x14
    // 0xb55f74: str             x0, [SP]
    // 0xb55f78: mov             x0, x1
    // 0xb55f7c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb55f7c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb55f80: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb55f80: movz            x17, #0x2700
    //     0xb55f84: add             lr, x0, x17
    //     0xb55f88: ldr             lr, [x21, lr, lsl #3]
    //     0xb55f8c: blr             lr
    // 0xb55f90: cmp             w0, NULL
    // 0xb55f94: b.ne            #0xb55fa0
    // 0xb55f98: r3 = ""
    //     0xb55f98: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb55f9c: b               #0xb55fa4
    // 0xb55fa0: mov             x3, x0
    // 0xb55fa4: ldur            x2, [fp, #-0x20]
    // 0xb55fa8: ldur            x0, [fp, #-0x30]
    // 0xb55fac: ldr             x1, [fp, #0x18]
    // 0xb55fb0: stur            x3, [fp, #-0x10]
    // 0xb55fb4: r0 = of()
    //     0xb55fb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb55fb8: LoadField: r1 = r0->field_87
    //     0xb55fb8: ldur            w1, [x0, #0x87]
    // 0xb55fbc: DecompressPointer r1
    //     0xb55fbc: add             x1, x1, HEAP, lsl #32
    // 0xb55fc0: LoadField: r0 = r1->field_2b
    //     0xb55fc0: ldur            w0, [x1, #0x2b]
    // 0xb55fc4: DecompressPointer r0
    //     0xb55fc4: add             x0, x0, HEAP, lsl #32
    // 0xb55fc8: stur            x0, [fp, #-0x18]
    // 0xb55fcc: r1 = Instance_Color
    //     0xb55fcc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb55fd0: d0 = 0.400000
    //     0xb55fd0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb55fd4: r0 = withOpacity()
    //     0xb55fd4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb55fd8: r16 = 12.000000
    //     0xb55fd8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb55fdc: ldr             x16, [x16, #0x9e8]
    // 0xb55fe0: stp             x0, x16, [SP]
    // 0xb55fe4: ldur            x1, [fp, #-0x18]
    // 0xb55fe8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb55fe8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb55fec: ldr             x4, [x4, #0xaa0]
    // 0xb55ff0: r0 = copyWith()
    //     0xb55ff0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb55ff4: stur            x0, [fp, #-0x18]
    // 0xb55ff8: r0 = Text()
    //     0xb55ff8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb55ffc: mov             x3, x0
    // 0xb56000: ldur            x0, [fp, #-0x10]
    // 0xb56004: stur            x3, [fp, #-0x28]
    // 0xb56008: StoreField: r3->field_b = r0
    //     0xb56008: stur            w0, [x3, #0xb]
    // 0xb5600c: ldur            x0, [fp, #-0x18]
    // 0xb56010: StoreField: r3->field_13 = r0
    //     0xb56010: stur            w0, [x3, #0x13]
    // 0xb56014: r1 = Null
    //     0xb56014: mov             x1, NULL
    // 0xb56018: r2 = 8
    //     0xb56018: movz            x2, #0x8
    // 0xb5601c: r0 = AllocateArray()
    //     0xb5601c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb56020: mov             x2, x0
    // 0xb56024: ldur            x0, [fp, #-0x20]
    // 0xb56028: stur            x2, [fp, #-0x10]
    // 0xb5602c: StoreField: r2->field_f = r0
    //     0xb5602c: stur            w0, [x2, #0xf]
    // 0xb56030: ldur            x0, [fp, #-0x30]
    // 0xb56034: StoreField: r2->field_13 = r0
    //     0xb56034: stur            w0, [x2, #0x13]
    // 0xb56038: r16 = Instance_Spacer
    //     0xb56038: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb5603c: ldr             x16, [x16, #0xf0]
    // 0xb56040: ArrayStore: r2[0] = r16  ; List_4
    //     0xb56040: stur            w16, [x2, #0x17]
    // 0xb56044: ldur            x0, [fp, #-0x28]
    // 0xb56048: StoreField: r2->field_1b = r0
    //     0xb56048: stur            w0, [x2, #0x1b]
    // 0xb5604c: r1 = <Widget>
    //     0xb5604c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb56050: r0 = AllocateGrowableArray()
    //     0xb56050: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb56054: mov             x1, x0
    // 0xb56058: ldur            x0, [fp, #-0x10]
    // 0xb5605c: stur            x1, [fp, #-0x18]
    // 0xb56060: StoreField: r1->field_f = r0
    //     0xb56060: stur            w0, [x1, #0xf]
    // 0xb56064: r0 = 8
    //     0xb56064: movz            x0, #0x8
    // 0xb56068: StoreField: r1->field_b = r0
    //     0xb56068: stur            w0, [x1, #0xb]
    // 0xb5606c: r0 = Row()
    //     0xb5606c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb56070: r1 = Instance_Axis
    //     0xb56070: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb56074: StoreField: r0->field_f = r1
    //     0xb56074: stur            w1, [x0, #0xf]
    // 0xb56078: r1 = Instance_MainAxisAlignment
    //     0xb56078: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5607c: ldr             x1, [x1, #0xa08]
    // 0xb56080: StoreField: r0->field_13 = r1
    //     0xb56080: stur            w1, [x0, #0x13]
    // 0xb56084: r1 = Instance_MainAxisSize
    //     0xb56084: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb56088: ldr             x1, [x1, #0xa10]
    // 0xb5608c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5608c: stur            w1, [x0, #0x17]
    // 0xb56090: r1 = Instance_CrossAxisAlignment
    //     0xb56090: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb56094: ldr             x1, [x1, #0xa18]
    // 0xb56098: StoreField: r0->field_1b = r1
    //     0xb56098: stur            w1, [x0, #0x1b]
    // 0xb5609c: r1 = Instance_VerticalDirection
    //     0xb5609c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb560a0: ldr             x1, [x1, #0xa20]
    // 0xb560a4: StoreField: r0->field_23 = r1
    //     0xb560a4: stur            w1, [x0, #0x23]
    // 0xb560a8: r1 = Instance_Clip
    //     0xb560a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb560ac: ldr             x1, [x1, #0x38]
    // 0xb560b0: StoreField: r0->field_2b = r1
    //     0xb560b0: stur            w1, [x0, #0x2b]
    // 0xb560b4: StoreField: r0->field_2f = rZR
    //     0xb560b4: stur            xzr, [x0, #0x2f]
    // 0xb560b8: ldur            x1, [fp, #-0x18]
    // 0xb560bc: StoreField: r0->field_b = r1
    //     0xb560bc: stur            w1, [x0, #0xb]
    // 0xb560c0: LeaveFrame
    //     0xb560c0: mov             SP, fp
    //     0xb560c4: ldp             fp, lr, [SP], #0x10
    // 0xb560c8: ret
    //     0xb560c8: ret             
    // 0xb560cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb560cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb560d0: b               #0xb55da0
    // 0xb560d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb560d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb560d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb560d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb560dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb560dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb560e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb560e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb560e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb560e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb560e8, size: 0x3a0
    // 0xb560e8: EnterFrame
    //     0xb560e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb560ec: mov             fp, SP
    // 0xb560f0: AllocStack(0x40)
    //     0xb560f0: sub             SP, SP, #0x40
    // 0xb560f4: SetupParameters()
    //     0xb560f4: ldr             x0, [fp, #0x20]
    //     0xb560f8: ldur            w4, [x0, #0x17]
    //     0xb560fc: add             x4, x4, HEAP, lsl #32
    //     0xb56100: stur            x4, [fp, #-0x10]
    // 0xb56104: CheckStackOverflow
    //     0xb56104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56108: cmp             SP, x16
    //     0xb5610c: b.ls            #0xb5646c
    // 0xb56110: LoadField: r1 = r4->field_f
    //     0xb56110: ldur            w1, [x4, #0xf]
    // 0xb56114: DecompressPointer r1
    //     0xb56114: add             x1, x1, HEAP, lsl #32
    // 0xb56118: LoadField: r0 = r1->field_b
    //     0xb56118: ldur            w0, [x1, #0xb]
    // 0xb5611c: DecompressPointer r0
    //     0xb5611c: add             x0, x0, HEAP, lsl #32
    // 0xb56120: cmp             w0, NULL
    // 0xb56124: b.eq            #0xb56474
    // 0xb56128: LoadField: r2 = r0->field_b
    //     0xb56128: ldur            w2, [x0, #0xb]
    // 0xb5612c: DecompressPointer r2
    //     0xb5612c: add             x2, x2, HEAP, lsl #32
    // 0xb56130: ldr             x0, [fp, #0x10]
    // 0xb56134: r6 = LoadInt32Instr(r0)
    //     0xb56134: sbfx            x6, x0, #1, #0x1f
    //     0xb56138: tbz             w0, #0, #0xb56140
    //     0xb5613c: ldur            x6, [x0, #7]
    // 0xb56140: mov             x5, x6
    // 0xb56144: stur            x6, [fp, #-8]
    // 0xb56148: r3 = Instance_ItemFilterType
    //     0xb56148: add             x3, PP, #0x56, lsl #12  ; [pp+0x565a0] Obj!ItemFilterType@d75381
    //     0xb5614c: ldr             x3, [x3, #0x5a0]
    // 0xb56150: r0 = _productBuildItem()
    //     0xb56150: bl              #0xb551f8  ; [package:customer_app/app/presentation/views/glass/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xb56154: mov             x3, x0
    // 0xb56158: ldur            x2, [fp, #-0x10]
    // 0xb5615c: stur            x3, [fp, #-0x20]
    // 0xb56160: LoadField: r0 = r2->field_f
    //     0xb56160: ldur            w0, [x2, #0xf]
    // 0xb56164: DecompressPointer r0
    //     0xb56164: add             x0, x0, HEAP, lsl #32
    // 0xb56168: LoadField: r1 = r0->field_b
    //     0xb56168: ldur            w1, [x0, #0xb]
    // 0xb5616c: DecompressPointer r1
    //     0xb5616c: add             x1, x1, HEAP, lsl #32
    // 0xb56170: cmp             w1, NULL
    // 0xb56174: b.eq            #0xb56478
    // 0xb56178: LoadField: r0 = r1->field_b
    //     0xb56178: ldur            w0, [x1, #0xb]
    // 0xb5617c: DecompressPointer r0
    //     0xb5617c: add             x0, x0, HEAP, lsl #32
    // 0xb56180: cmp             w0, NULL
    // 0xb56184: b.ne            #0xb56194
    // 0xb56188: ldur            x5, [fp, #-8]
    // 0xb5618c: r0 = Null
    //     0xb5618c: mov             x0, NULL
    // 0xb56190: b               #0xb561e8
    // 0xb56194: LoadField: r4 = r0->field_7
    //     0xb56194: ldur            w4, [x0, #7]
    // 0xb56198: DecompressPointer r4
    //     0xb56198: add             x4, x4, HEAP, lsl #32
    // 0xb5619c: cmp             w4, NULL
    // 0xb561a0: b.ne            #0xb561b0
    // 0xb561a4: ldur            x5, [fp, #-8]
    // 0xb561a8: r0 = Null
    //     0xb561a8: mov             x0, NULL
    // 0xb561ac: b               #0xb561e8
    // 0xb561b0: ldur            x5, [fp, #-8]
    // 0xb561b4: LoadField: r0 = r4->field_b
    //     0xb561b4: ldur            w0, [x4, #0xb]
    // 0xb561b8: r1 = LoadInt32Instr(r0)
    //     0xb561b8: sbfx            x1, x0, #1, #0x1f
    // 0xb561bc: mov             x0, x1
    // 0xb561c0: mov             x1, x5
    // 0xb561c4: cmp             x1, x0
    // 0xb561c8: b.hs            #0xb5647c
    // 0xb561cc: LoadField: r0 = r4->field_f
    //     0xb561cc: ldur            w0, [x4, #0xf]
    // 0xb561d0: DecompressPointer r0
    //     0xb561d0: add             x0, x0, HEAP, lsl #32
    // 0xb561d4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb561d4: add             x16, x0, x5, lsl #2
    //     0xb561d8: ldur            w1, [x16, #0xf]
    // 0xb561dc: DecompressPointer r1
    //     0xb561dc: add             x1, x1, HEAP, lsl #32
    // 0xb561e0: LoadField: r0 = r1->field_f
    //     0xb561e0: ldur            w0, [x1, #0xf]
    // 0xb561e4: DecompressPointer r0
    //     0xb561e4: add             x0, x0, HEAP, lsl #32
    // 0xb561e8: cmp             w0, NULL
    // 0xb561ec: b.ne            #0xb561f4
    // 0xb561f0: r0 = ""
    //     0xb561f0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb561f4: ldr             x1, [fp, #0x18]
    // 0xb561f8: stur            x0, [fp, #-0x18]
    // 0xb561fc: r0 = of()
    //     0xb561fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb56200: LoadField: r1 = r0->field_87
    //     0xb56200: ldur            w1, [x0, #0x87]
    // 0xb56204: DecompressPointer r1
    //     0xb56204: add             x1, x1, HEAP, lsl #32
    // 0xb56208: LoadField: r0 = r1->field_2b
    //     0xb56208: ldur            w0, [x1, #0x2b]
    // 0xb5620c: DecompressPointer r0
    //     0xb5620c: add             x0, x0, HEAP, lsl #32
    // 0xb56210: r16 = 14.000000
    //     0xb56210: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb56214: ldr             x16, [x16, #0x1d8]
    // 0xb56218: r30 = Instance_Color
    //     0xb56218: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5621c: stp             lr, x16, [SP]
    // 0xb56220: mov             x1, x0
    // 0xb56224: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb56224: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb56228: ldr             x4, [x4, #0xaa0]
    // 0xb5622c: r0 = copyWith()
    //     0xb5622c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb56230: stur            x0, [fp, #-0x28]
    // 0xb56234: r0 = Text()
    //     0xb56234: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb56238: mov             x2, x0
    // 0xb5623c: ldur            x0, [fp, #-0x18]
    // 0xb56240: stur            x2, [fp, #-0x30]
    // 0xb56244: StoreField: r2->field_b = r0
    //     0xb56244: stur            w0, [x2, #0xb]
    // 0xb56248: ldur            x0, [fp, #-0x28]
    // 0xb5624c: StoreField: r2->field_13 = r0
    //     0xb5624c: stur            w0, [x2, #0x13]
    // 0xb56250: r1 = <FlexParentData>
    //     0xb56250: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb56254: ldr             x1, [x1, #0xe00]
    // 0xb56258: r0 = Flexible()
    //     0xb56258: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb5625c: mov             x2, x0
    // 0xb56260: r0 = 1
    //     0xb56260: movz            x0, #0x1
    // 0xb56264: stur            x2, [fp, #-0x18]
    // 0xb56268: StoreField: r2->field_13 = r0
    //     0xb56268: stur            x0, [x2, #0x13]
    // 0xb5626c: r0 = Instance_FlexFit
    //     0xb5626c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb56270: ldr             x0, [x0, #0xe08]
    // 0xb56274: StoreField: r2->field_1b = r0
    //     0xb56274: stur            w0, [x2, #0x1b]
    // 0xb56278: ldur            x0, [fp, #-0x30]
    // 0xb5627c: StoreField: r2->field_b = r0
    //     0xb5627c: stur            w0, [x2, #0xb]
    // 0xb56280: ldur            x0, [fp, #-0x10]
    // 0xb56284: LoadField: r1 = r0->field_f
    //     0xb56284: ldur            w1, [x0, #0xf]
    // 0xb56288: DecompressPointer r1
    //     0xb56288: add             x1, x1, HEAP, lsl #32
    // 0xb5628c: LoadField: r0 = r1->field_b
    //     0xb5628c: ldur            w0, [x1, #0xb]
    // 0xb56290: DecompressPointer r0
    //     0xb56290: add             x0, x0, HEAP, lsl #32
    // 0xb56294: cmp             w0, NULL
    // 0xb56298: b.eq            #0xb56480
    // 0xb5629c: LoadField: r1 = r0->field_b
    //     0xb5629c: ldur            w1, [x0, #0xb]
    // 0xb562a0: DecompressPointer r1
    //     0xb562a0: add             x1, x1, HEAP, lsl #32
    // 0xb562a4: cmp             w1, NULL
    // 0xb562a8: b.ne            #0xb562b4
    // 0xb562ac: r0 = Null
    //     0xb562ac: mov             x0, NULL
    // 0xb562b0: b               #0xb56330
    // 0xb562b4: LoadField: r3 = r1->field_7
    //     0xb562b4: ldur            w3, [x1, #7]
    // 0xb562b8: DecompressPointer r3
    //     0xb562b8: add             x3, x3, HEAP, lsl #32
    // 0xb562bc: cmp             w3, NULL
    // 0xb562c0: b.ne            #0xb562cc
    // 0xb562c4: r0 = Null
    //     0xb562c4: mov             x0, NULL
    // 0xb562c8: b               #0xb56330
    // 0xb562cc: ldur            x4, [fp, #-8]
    // 0xb562d0: LoadField: r0 = r3->field_b
    //     0xb562d0: ldur            w0, [x3, #0xb]
    // 0xb562d4: r1 = LoadInt32Instr(r0)
    //     0xb562d4: sbfx            x1, x0, #1, #0x1f
    // 0xb562d8: mov             x0, x1
    // 0xb562dc: mov             x1, x4
    // 0xb562e0: cmp             x1, x0
    // 0xb562e4: b.hs            #0xb56484
    // 0xb562e8: LoadField: r0 = r3->field_f
    //     0xb562e8: ldur            w0, [x3, #0xf]
    // 0xb562ec: DecompressPointer r0
    //     0xb562ec: add             x0, x0, HEAP, lsl #32
    // 0xb562f0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb562f0: add             x16, x0, x4, lsl #2
    //     0xb562f4: ldur            w1, [x16, #0xf]
    // 0xb562f8: DecompressPointer r1
    //     0xb562f8: add             x1, x1, HEAP, lsl #32
    // 0xb562fc: LoadField: r0 = r1->field_b
    //     0xb562fc: ldur            w0, [x1, #0xb]
    // 0xb56300: DecompressPointer r0
    //     0xb56300: add             x0, x0, HEAP, lsl #32
    // 0xb56304: r1 = 60
    //     0xb56304: movz            x1, #0x3c
    // 0xb56308: branchIfSmi(r0, 0xb56314)
    //     0xb56308: tbz             w0, #0, #0xb56314
    // 0xb5630c: r1 = LoadClassIdInstr(r0)
    //     0xb5630c: ldur            x1, [x0, #-1]
    //     0xb56310: ubfx            x1, x1, #0xc, #0x14
    // 0xb56314: str             x0, [SP]
    // 0xb56318: mov             x0, x1
    // 0xb5631c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb5631c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb56320: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb56320: movz            x17, #0x2700
    //     0xb56324: add             lr, x0, x17
    //     0xb56328: ldr             lr, [x21, lr, lsl #3]
    //     0xb5632c: blr             lr
    // 0xb56330: cmp             w0, NULL
    // 0xb56334: b.ne            #0xb56340
    // 0xb56338: r3 = ""
    //     0xb56338: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5633c: b               #0xb56344
    // 0xb56340: mov             x3, x0
    // 0xb56344: ldur            x2, [fp, #-0x20]
    // 0xb56348: ldur            x0, [fp, #-0x18]
    // 0xb5634c: ldr             x1, [fp, #0x18]
    // 0xb56350: stur            x3, [fp, #-0x10]
    // 0xb56354: r0 = of()
    //     0xb56354: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb56358: LoadField: r1 = r0->field_87
    //     0xb56358: ldur            w1, [x0, #0x87]
    // 0xb5635c: DecompressPointer r1
    //     0xb5635c: add             x1, x1, HEAP, lsl #32
    // 0xb56360: LoadField: r0 = r1->field_2b
    //     0xb56360: ldur            w0, [x1, #0x2b]
    // 0xb56364: DecompressPointer r0
    //     0xb56364: add             x0, x0, HEAP, lsl #32
    // 0xb56368: stur            x0, [fp, #-0x28]
    // 0xb5636c: r1 = Instance_Color
    //     0xb5636c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb56370: d0 = 0.400000
    //     0xb56370: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb56374: r0 = withOpacity()
    //     0xb56374: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb56378: r16 = 12.000000
    //     0xb56378: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5637c: ldr             x16, [x16, #0x9e8]
    // 0xb56380: stp             x0, x16, [SP]
    // 0xb56384: ldur            x1, [fp, #-0x28]
    // 0xb56388: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb56388: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5638c: ldr             x4, [x4, #0xaa0]
    // 0xb56390: r0 = copyWith()
    //     0xb56390: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb56394: stur            x0, [fp, #-0x28]
    // 0xb56398: r0 = Text()
    //     0xb56398: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5639c: mov             x3, x0
    // 0xb563a0: ldur            x0, [fp, #-0x10]
    // 0xb563a4: stur            x3, [fp, #-0x30]
    // 0xb563a8: StoreField: r3->field_b = r0
    //     0xb563a8: stur            w0, [x3, #0xb]
    // 0xb563ac: ldur            x0, [fp, #-0x28]
    // 0xb563b0: StoreField: r3->field_13 = r0
    //     0xb563b0: stur            w0, [x3, #0x13]
    // 0xb563b4: r1 = Null
    //     0xb563b4: mov             x1, NULL
    // 0xb563b8: r2 = 8
    //     0xb563b8: movz            x2, #0x8
    // 0xb563bc: r0 = AllocateArray()
    //     0xb563bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb563c0: mov             x2, x0
    // 0xb563c4: ldur            x0, [fp, #-0x20]
    // 0xb563c8: stur            x2, [fp, #-0x10]
    // 0xb563cc: StoreField: r2->field_f = r0
    //     0xb563cc: stur            w0, [x2, #0xf]
    // 0xb563d0: ldur            x0, [fp, #-0x18]
    // 0xb563d4: StoreField: r2->field_13 = r0
    //     0xb563d4: stur            w0, [x2, #0x13]
    // 0xb563d8: r16 = Instance_Spacer
    //     0xb563d8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb563dc: ldr             x16, [x16, #0xf0]
    // 0xb563e0: ArrayStore: r2[0] = r16  ; List_4
    //     0xb563e0: stur            w16, [x2, #0x17]
    // 0xb563e4: ldur            x0, [fp, #-0x30]
    // 0xb563e8: StoreField: r2->field_1b = r0
    //     0xb563e8: stur            w0, [x2, #0x1b]
    // 0xb563ec: r1 = <Widget>
    //     0xb563ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb563f0: r0 = AllocateGrowableArray()
    //     0xb563f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb563f4: mov             x1, x0
    // 0xb563f8: ldur            x0, [fp, #-0x10]
    // 0xb563fc: stur            x1, [fp, #-0x18]
    // 0xb56400: StoreField: r1->field_f = r0
    //     0xb56400: stur            w0, [x1, #0xf]
    // 0xb56404: r0 = 8
    //     0xb56404: movz            x0, #0x8
    // 0xb56408: StoreField: r1->field_b = r0
    //     0xb56408: stur            w0, [x1, #0xb]
    // 0xb5640c: r0 = Row()
    //     0xb5640c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb56410: r1 = Instance_Axis
    //     0xb56410: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb56414: StoreField: r0->field_f = r1
    //     0xb56414: stur            w1, [x0, #0xf]
    // 0xb56418: r1 = Instance_MainAxisAlignment
    //     0xb56418: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5641c: ldr             x1, [x1, #0xa08]
    // 0xb56420: StoreField: r0->field_13 = r1
    //     0xb56420: stur            w1, [x0, #0x13]
    // 0xb56424: r1 = Instance_MainAxisSize
    //     0xb56424: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb56428: ldr             x1, [x1, #0xa10]
    // 0xb5642c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5642c: stur            w1, [x0, #0x17]
    // 0xb56430: r1 = Instance_CrossAxisAlignment
    //     0xb56430: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb56434: ldr             x1, [x1, #0xa18]
    // 0xb56438: StoreField: r0->field_1b = r1
    //     0xb56438: stur            w1, [x0, #0x1b]
    // 0xb5643c: r1 = Instance_VerticalDirection
    //     0xb5643c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb56440: ldr             x1, [x1, #0xa20]
    // 0xb56444: StoreField: r0->field_23 = r1
    //     0xb56444: stur            w1, [x0, #0x23]
    // 0xb56448: r1 = Instance_Clip
    //     0xb56448: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5644c: ldr             x1, [x1, #0x38]
    // 0xb56450: StoreField: r0->field_2b = r1
    //     0xb56450: stur            w1, [x0, #0x2b]
    // 0xb56454: StoreField: r0->field_2f = rZR
    //     0xb56454: stur            xzr, [x0, #0x2f]
    // 0xb56458: ldur            x1, [fp, #-0x18]
    // 0xb5645c: StoreField: r0->field_b = r1
    //     0xb5645c: stur            w1, [x0, #0xb]
    // 0xb56460: LeaveFrame
    //     0xb56460: mov             SP, fp
    //     0xb56464: ldp             fp, lr, [SP], #0x10
    // 0xb56468: ret
    //     0xb56468: ret             
    // 0xb5646c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5646c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56470: b               #0xb56110
    // 0xb56474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb56474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb56478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb56478: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5647c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5647c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb56480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb56480: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb56484: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb56484: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb56488, size: 0x98
    // 0xb56488: EnterFrame
    //     0xb56488: stp             fp, lr, [SP, #-0x10]!
    //     0xb5648c: mov             fp, SP
    // 0xb56490: AllocStack(0x10)
    //     0xb56490: sub             SP, SP, #0x10
    // 0xb56494: SetupParameters()
    //     0xb56494: ldr             x0, [fp, #0x10]
    //     0xb56498: ldur            w2, [x0, #0x17]
    //     0xb5649c: add             x2, x2, HEAP, lsl #32
    //     0xb564a0: stur            x2, [fp, #-8]
    // 0xb564a4: CheckStackOverflow
    //     0xb564a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb564a8: cmp             SP, x16
    //     0xb564ac: b.ls            #0xb56514
    // 0xb564b0: LoadField: r0 = r2->field_f
    //     0xb564b0: ldur            w0, [x2, #0xf]
    // 0xb564b4: DecompressPointer r0
    //     0xb564b4: add             x0, x0, HEAP, lsl #32
    // 0xb564b8: LoadField: r1 = r0->field_13
    //     0xb564b8: ldur            w1, [x0, #0x13]
    // 0xb564bc: DecompressPointer r1
    //     0xb564bc: add             x1, x1, HEAP, lsl #32
    // 0xb564c0: r0 = clear()
    //     0xb564c0: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb564c4: ldur            x0, [fp, #-8]
    // 0xb564c8: LoadField: r1 = r0->field_f
    //     0xb564c8: ldur            w1, [x0, #0xf]
    // 0xb564cc: DecompressPointer r1
    //     0xb564cc: add             x1, x1, HEAP, lsl #32
    // 0xb564d0: LoadField: r0 = r1->field_b
    //     0xb564d0: ldur            w0, [x1, #0xb]
    // 0xb564d4: DecompressPointer r0
    //     0xb564d4: add             x0, x0, HEAP, lsl #32
    // 0xb564d8: cmp             w0, NULL
    // 0xb564dc: b.eq            #0xb5651c
    // 0xb564e0: LoadField: r1 = r0->field_13
    //     0xb564e0: ldur            w1, [x0, #0x13]
    // 0xb564e4: DecompressPointer r1
    //     0xb564e4: add             x1, x1, HEAP, lsl #32
    // 0xb564e8: str             x1, [SP]
    // 0xb564ec: r4 = 0
    //     0xb564ec: movz            x4, #0
    // 0xb564f0: ldr             x0, [SP]
    // 0xb564f4: r16 = UnlinkedCall_0x613b5c
    //     0xb564f4: add             x16, PP, #0x56, lsl #12  ; [pp+0x565a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb564f8: add             x16, x16, #0x5a8
    // 0xb564fc: ldp             x5, lr, [x16]
    // 0xb56500: blr             lr
    // 0xb56504: r0 = Null
    //     0xb56504: mov             x0, NULL
    // 0xb56508: LeaveFrame
    //     0xb56508: mov             SP, fp
    //     0xb5650c: ldp             fp, lr, [SP], #0x10
    // 0xb56510: ret
    //     0xb56510: ret             
    // 0xb56514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56518: b               #0xb564b0
    // 0xb5651c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5651c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4090, size: 0x18, field offset: 0xc
//   const constructor, 
class CollectionFilter extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7efc8, size: 0x48
    // 0xc7efc8: EnterFrame
    //     0xc7efc8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7efcc: mov             fp, SP
    // 0xc7efd0: AllocStack(0x8)
    //     0xc7efd0: sub             SP, SP, #8
    // 0xc7efd4: CheckStackOverflow
    //     0xc7efd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7efd8: cmp             SP, x16
    //     0xc7efdc: b.ls            #0xc7f008
    // 0xc7efe0: r1 = <CollectionFilter>
    //     0xc7efe0: add             x1, PP, #0x48, lsl #12  ; [pp+0x489c0] TypeArguments: <CollectionFilter>
    //     0xc7efe4: ldr             x1, [x1, #0x9c0]
    // 0xc7efe8: r0 = _CollectionFilterState()
    //     0xc7efe8: bl              #0xc7f010  ; Allocate_CollectionFilterStateStub -> _CollectionFilterState (size=0x34)
    // 0xc7efec: mov             x1, x0
    // 0xc7eff0: stur            x0, [fp, #-8]
    // 0xc7eff4: r0 = _CollectionFilterState()
    //     0xc7eff4: bl              #0xc7b32c  ; [package:customer_app/app/presentation/views/basic/collections/widgets/collection_filter.dart] _CollectionFilterState::_CollectionFilterState
    // 0xc7eff8: ldur            x0, [fp, #-8]
    // 0xc7effc: LeaveFrame
    //     0xc7effc: mov             SP, fp
    //     0xc7f000: ldp             fp, lr, [SP], #0x10
    // 0xc7f004: ret
    //     0xc7f004: ret             
    // 0xc7f008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f00c: b               #0xc7efe0
  }
}

// class id: 7082, size: 0x14, field offset: 0x14
enum ItemFilterType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
