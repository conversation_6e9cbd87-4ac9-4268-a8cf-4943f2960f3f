// lib: , url: package:customer_app/app/presentation/views/glass/login/login_view.dart

// class id: 1049410, size: 0x8
class :: {
}

// class id: 4564, size: 0x20, field offset: 0x14
class LoginView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x13612d8, size: 0x64
    // 0x13612d8: EnterFrame
    //     0x13612d8: stp             fp, lr, [SP, #-0x10]!
    //     0x13612dc: mov             fp, SP
    // 0x13612e0: AllocStack(0x18)
    //     0x13612e0: sub             SP, SP, #0x18
    // 0x13612e4: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x13612e4: stur            x1, [fp, #-8]
    //     0x13612e8: stur            x2, [fp, #-0x10]
    // 0x13612ec: r1 = 2
    //     0x13612ec: movz            x1, #0x2
    // 0x13612f0: r0 = AllocateContext()
    //     0x13612f0: bl              #0x16f6108  ; AllocateContextStub
    // 0x13612f4: mov             x1, x0
    // 0x13612f8: ldur            x0, [fp, #-8]
    // 0x13612fc: stur            x1, [fp, #-0x18]
    // 0x1361300: StoreField: r1->field_f = r0
    //     0x1361300: stur            w0, [x1, #0xf]
    // 0x1361304: ldur            x0, [fp, #-0x10]
    // 0x1361308: StoreField: r1->field_13 = r0
    //     0x1361308: stur            w0, [x1, #0x13]
    // 0x136130c: r0 = Obx()
    //     0x136130c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1361310: ldur            x2, [fp, #-0x18]
    // 0x1361314: r1 = Function '<anonymous closure>':.
    //     0x1361314: add             x1, PP, #0x40, lsl #12  ; [pp+0x40268] AnonymousClosure: (0x136133c), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::bottomNavigationBar (0x13612d8)
    //     0x1361318: ldr             x1, [x1, #0x268]
    // 0x136131c: stur            x0, [fp, #-8]
    // 0x1361320: r0 = AllocateClosure()
    //     0x1361320: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1361324: mov             x1, x0
    // 0x1361328: ldur            x0, [fp, #-8]
    // 0x136132c: StoreField: r0->field_b = r1
    //     0x136132c: stur            w1, [x0, #0xb]
    // 0x1361330: LeaveFrame
    //     0x1361330: mov             SP, fp
    //     0x1361334: ldp             fp, lr, [SP], #0x10
    // 0x1361338: ret
    //     0x1361338: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x136133c, size: 0x2c8
    // 0x136133c: EnterFrame
    //     0x136133c: stp             fp, lr, [SP, #-0x10]!
    //     0x1361340: mov             fp, SP
    // 0x1361344: AllocStack(0x58)
    //     0x1361344: sub             SP, SP, #0x58
    // 0x1361348: SetupParameters()
    //     0x1361348: ldr             x0, [fp, #0x10]
    //     0x136134c: ldur            w2, [x0, #0x17]
    //     0x1361350: add             x2, x2, HEAP, lsl #32
    //     0x1361354: stur            x2, [fp, #-8]
    // 0x1361358: CheckStackOverflow
    //     0x1361358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x136135c: cmp             SP, x16
    //     0x1361360: b.ls            #0x13615fc
    // 0x1361364: LoadField: r1 = r2->field_13
    //     0x1361364: ldur            w1, [x2, #0x13]
    // 0x1361368: DecompressPointer r1
    //     0x1361368: add             x1, x1, HEAP, lsl #32
    // 0x136136c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x136136c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1361370: r0 = _of()
    //     0x1361370: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1361374: LoadField: r1 = r0->field_23
    //     0x1361374: ldur            w1, [x0, #0x23]
    // 0x1361378: DecompressPointer r1
    //     0x1361378: add             x1, x1, HEAP, lsl #32
    // 0x136137c: LoadField: d0 = r1->field_1f
    //     0x136137c: ldur            d0, [x1, #0x1f]
    // 0x1361380: stur            d0, [fp, #-0x40]
    // 0x1361384: r0 = EdgeInsets()
    //     0x1361384: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1361388: stur            x0, [fp, #-0x10]
    // 0x136138c: StoreField: r0->field_7 = rZR
    //     0x136138c: stur            xzr, [x0, #7]
    // 0x1361390: StoreField: r0->field_f = rZR
    //     0x1361390: stur            xzr, [x0, #0xf]
    // 0x1361394: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1361394: stur            xzr, [x0, #0x17]
    // 0x1361398: ldur            d0, [fp, #-0x40]
    // 0x136139c: StoreField: r0->field_1f = d0
    //     0x136139c: stur            d0, [x0, #0x1f]
    // 0x13613a0: ldur            x2, [fp, #-8]
    // 0x13613a4: LoadField: r1 = r2->field_f
    //     0x13613a4: ldur            w1, [x2, #0xf]
    // 0x13613a8: DecompressPointer r1
    //     0x13613a8: add             x1, x1, HEAP, lsl #32
    // 0x13613ac: r0 = controller()
    //     0x13613ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13613b0: LoadField: r1 = r0->field_8b
    //     0x13613b0: ldur            w1, [x0, #0x8b]
    // 0x13613b4: DecompressPointer r1
    //     0x13613b4: add             x1, x1, HEAP, lsl #32
    // 0x13613b8: r0 = value()
    //     0x13613b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13613bc: cmp             w0, NULL
    // 0x13613c0: b.ne            #0x13613cc
    // 0x13613c4: r1 = true
    //     0x13613c4: add             x1, NULL, #0x20  ; true
    // 0x13613c8: b               #0x13613d0
    // 0x13613cc: mov             x1, x0
    // 0x13613d0: ldur            x2, [fp, #-8]
    // 0x13613d4: ldur            x0, [fp, #-0x10]
    // 0x13613d8: eor             x3, x1, #0x10
    // 0x13613dc: stur            x3, [fp, #-0x18]
    // 0x13613e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13613e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13613e4: ldr             x0, [x0, #0x1c80]
    //     0x13613e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13613ec: cmp             w0, w16
    //     0x13613f0: b.ne            #0x13613fc
    //     0x13613f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13613f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13613fc: r0 = GetNavigation.width()
    //     0x13613fc: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x1361400: stur            d0, [fp, #-0x40]
    // 0x1361404: r0 = GetNavigation.size()
    //     0x1361404: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1361408: LoadField: d0 = r0->field_f
    //     0x1361408: ldur            d0, [x0, #0xf]
    // 0x136140c: d1 = 0.060000
    //     0x136140c: add             x17, PP, #0x36, lsl #12  ; [pp+0x36f28] IMM: double(0.06) from 0x3faeb851eb851eb8
    //     0x1361410: ldr             d1, [x17, #0xf28]
    // 0x1361414: fmul            d2, d0, d1
    // 0x1361418: ldur            x2, [fp, #-8]
    // 0x136141c: stur            d2, [fp, #-0x48]
    // 0x1361420: LoadField: r1 = r2->field_13
    //     0x1361420: ldur            w1, [x2, #0x13]
    // 0x1361424: DecompressPointer r1
    //     0x1361424: add             x1, x1, HEAP, lsl #32
    // 0x1361428: r0 = of()
    //     0x1361428: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x136142c: LoadField: r2 = r0->field_5b
    //     0x136142c: ldur            w2, [x0, #0x5b]
    // 0x1361430: DecompressPointer r2
    //     0x1361430: add             x2, x2, HEAP, lsl #32
    // 0x1361434: stur            x2, [fp, #-0x20]
    // 0x1361438: r1 = "confirm number"
    //     0x1361438: add             x1, PP, #0x40, lsl #12  ; [pp+0x40270] "confirm number"
    //     0x136143c: ldr             x1, [x1, #0x270]
    // 0x1361440: r0 = capitalizeFirstWord()
    //     0x1361440: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x1361444: ldur            x2, [fp, #-8]
    // 0x1361448: stur            x0, [fp, #-0x28]
    // 0x136144c: LoadField: r1 = r2->field_13
    //     0x136144c: ldur            w1, [x2, #0x13]
    // 0x1361450: DecompressPointer r1
    //     0x1361450: add             x1, x1, HEAP, lsl #32
    // 0x1361454: r0 = of()
    //     0x1361454: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1361458: LoadField: r1 = r0->field_87
    //     0x1361458: ldur            w1, [x0, #0x87]
    // 0x136145c: DecompressPointer r1
    //     0x136145c: add             x1, x1, HEAP, lsl #32
    // 0x1361460: LoadField: r0 = r1->field_7
    //     0x1361460: ldur            w0, [x1, #7]
    // 0x1361464: DecompressPointer r0
    //     0x1361464: add             x0, x0, HEAP, lsl #32
    // 0x1361468: r16 = 16.000000
    //     0x1361468: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x136146c: ldr             x16, [x16, #0x188]
    // 0x1361470: r30 = Instance_Color
    //     0x1361470: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1361474: stp             lr, x16, [SP]
    // 0x1361478: mov             x1, x0
    // 0x136147c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x136147c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1361480: ldr             x4, [x4, #0xaa0]
    // 0x1361484: r0 = copyWith()
    //     0x1361484: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1361488: stur            x0, [fp, #-0x30]
    // 0x136148c: r0 = Text()
    //     0x136148c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1361490: mov             x3, x0
    // 0x1361494: ldur            x0, [fp, #-0x28]
    // 0x1361498: stur            x3, [fp, #-0x38]
    // 0x136149c: StoreField: r3->field_b = r0
    //     0x136149c: stur            w0, [x3, #0xb]
    // 0x13614a0: ldur            x0, [fp, #-0x30]
    // 0x13614a4: StoreField: r3->field_13 = r0
    //     0x13614a4: stur            w0, [x3, #0x13]
    // 0x13614a8: ldur            x2, [fp, #-8]
    // 0x13614ac: r1 = Function '<anonymous closure>':.
    //     0x13614ac: add             x1, PP, #0x40, lsl #12  ; [pp+0x40278] AnonymousClosure: (0x1361604), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::bottomNavigationBar (0x13612d8)
    //     0x13614b0: ldr             x1, [x1, #0x278]
    // 0x13614b4: r0 = AllocateClosure()
    //     0x13614b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13614b8: stur            x0, [fp, #-8]
    // 0x13614bc: r0 = MaterialButton()
    //     0x13614bc: bl              #0x131db78  ; AllocateMaterialButtonStub -> MaterialButton (size=0x88)
    // 0x13614c0: mov             x1, x0
    // 0x13614c4: ldur            x0, [fp, #-8]
    // 0x13614c8: stur            x1, [fp, #-0x28]
    // 0x13614cc: StoreField: r1->field_b = r0
    //     0x13614cc: stur            w0, [x1, #0xb]
    // 0x13614d0: r0 = Instance_Color
    //     0x13614d0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13614d4: StoreField: r1->field_1f = r0
    //     0x13614d4: stur            w0, [x1, #0x1f]
    // 0x13614d8: ldur            x0, [fp, #-0x20]
    // 0x13614dc: StoreField: r1->field_23 = r0
    //     0x13614dc: stur            w0, [x1, #0x23]
    // 0x13614e0: r0 = Instance_RoundedRectangleBorder
    //     0x13614e0: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x13614e4: ldr             x0, [x0, #0x888]
    // 0x13614e8: StoreField: r1->field_5b = r0
    //     0x13614e8: stur            w0, [x1, #0x5b]
    // 0x13614ec: r0 = Instance_Clip
    //     0x13614ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13614f0: ldr             x0, [x0, #0x38]
    // 0x13614f4: StoreField: r1->field_5f = r0
    //     0x13614f4: stur            w0, [x1, #0x5f]
    // 0x13614f8: r0 = false
    //     0x13614f8: add             x0, NULL, #0x30  ; false
    // 0x13614fc: StoreField: r1->field_67 = r0
    //     0x13614fc: stur            w0, [x1, #0x67]
    // 0x1361500: ldur            d0, [fp, #-0x40]
    // 0x1361504: StoreField: r1->field_73 = d0
    //     0x1361504: stur            d0, [x1, #0x73]
    // 0x1361508: ldur            d0, [fp, #-0x48]
    // 0x136150c: StoreField: r1->field_7b = d0
    //     0x136150c: stur            d0, [x1, #0x7b]
    // 0x1361510: r2 = true
    //     0x1361510: add             x2, NULL, #0x20  ; true
    // 0x1361514: StoreField: r1->field_83 = r2
    //     0x1361514: stur            w2, [x1, #0x83]
    // 0x1361518: ldur            x2, [fp, #-0x38]
    // 0x136151c: StoreField: r1->field_4f = r2
    //     0x136151c: stur            w2, [x1, #0x4f]
    // 0x1361520: r2 = Instance_ValueKey
    //     0x1361520: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f40] Obj!ValueKey<String>@d5b381
    //     0x1361524: ldr             x2, [x2, #0xf40]
    // 0x1361528: StoreField: r1->field_7 = r2
    //     0x1361528: stur            w2, [x1, #7]
    // 0x136152c: r0 = Padding()
    //     0x136152c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1361530: mov             x1, x0
    // 0x1361534: r0 = Instance_EdgeInsets
    //     0x1361534: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x1361538: ldr             x0, [x0, #0xf98]
    // 0x136153c: stur            x1, [fp, #-8]
    // 0x1361540: StoreField: r1->field_f = r0
    //     0x1361540: stur            w0, [x1, #0xf]
    // 0x1361544: ldur            x0, [fp, #-0x28]
    // 0x1361548: StoreField: r1->field_b = r0
    //     0x1361548: stur            w0, [x1, #0xb]
    // 0x136154c: r0 = Container()
    //     0x136154c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1361550: stur            x0, [fp, #-0x20]
    // 0x1361554: r16 = Instance_BoxDecoration
    //     0x1361554: add             x16, PP, #0x40, lsl #12  ; [pp+0x40280] Obj!BoxDecoration@d64ce1
    //     0x1361558: ldr             x16, [x16, #0x280]
    // 0x136155c: ldur            lr, [fp, #-8]
    // 0x1361560: stp             lr, x16, [SP]
    // 0x1361564: mov             x1, x0
    // 0x1361568: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1361568: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x136156c: ldr             x4, [x4, #0x88]
    // 0x1361570: r0 = Container()
    //     0x1361570: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1361574: r0 = Visibility()
    //     0x1361574: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1361578: mov             x1, x0
    // 0x136157c: ldur            x0, [fp, #-0x20]
    // 0x1361580: stur            x1, [fp, #-8]
    // 0x1361584: StoreField: r1->field_b = r0
    //     0x1361584: stur            w0, [x1, #0xb]
    // 0x1361588: r0 = Instance_SizedBox
    //     0x1361588: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x136158c: StoreField: r1->field_f = r0
    //     0x136158c: stur            w0, [x1, #0xf]
    // 0x1361590: ldur            x0, [fp, #-0x18]
    // 0x1361594: StoreField: r1->field_13 = r0
    //     0x1361594: stur            w0, [x1, #0x13]
    // 0x1361598: r0 = false
    //     0x1361598: add             x0, NULL, #0x30  ; false
    // 0x136159c: ArrayStore: r1[0] = r0  ; List_4
    //     0x136159c: stur            w0, [x1, #0x17]
    // 0x13615a0: StoreField: r1->field_1b = r0
    //     0x13615a0: stur            w0, [x1, #0x1b]
    // 0x13615a4: StoreField: r1->field_1f = r0
    //     0x13615a4: stur            w0, [x1, #0x1f]
    // 0x13615a8: StoreField: r1->field_23 = r0
    //     0x13615a8: stur            w0, [x1, #0x23]
    // 0x13615ac: StoreField: r1->field_27 = r0
    //     0x13615ac: stur            w0, [x1, #0x27]
    // 0x13615b0: StoreField: r1->field_2b = r0
    //     0x13615b0: stur            w0, [x1, #0x2b]
    // 0x13615b4: r0 = Container()
    //     0x13615b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13615b8: stur            x0, [fp, #-0x18]
    // 0x13615bc: r16 = Instance_BoxDecoration
    //     0x13615bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f48] Obj!BoxDecoration@d64cb1
    //     0x13615c0: ldr             x16, [x16, #0xf48]
    // 0x13615c4: ldur            lr, [fp, #-8]
    // 0x13615c8: stp             lr, x16, [SP]
    // 0x13615cc: mov             x1, x0
    // 0x13615d0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13615d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13615d4: ldr             x4, [x4, #0x88]
    // 0x13615d8: r0 = Container()
    //     0x13615d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13615dc: r0 = Padding()
    //     0x13615dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13615e0: ldur            x1, [fp, #-0x10]
    // 0x13615e4: StoreField: r0->field_f = r1
    //     0x13615e4: stur            w1, [x0, #0xf]
    // 0x13615e8: ldur            x1, [fp, #-0x18]
    // 0x13615ec: StoreField: r0->field_b = r1
    //     0x13615ec: stur            w1, [x0, #0xb]
    // 0x13615f0: LeaveFrame
    //     0x13615f0: mov             SP, fp
    //     0x13615f4: ldp             fp, lr, [SP], #0x10
    // 0x13615f8: ret
    //     0x13615f8: ret             
    // 0x13615fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13615fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1361600: b               #0x1361364
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1361604, size: 0x48
    // 0x1361604: EnterFrame
    //     0x1361604: stp             fp, lr, [SP, #-0x10]!
    //     0x1361608: mov             fp, SP
    // 0x136160c: ldr             x0, [fp, #0x10]
    // 0x1361610: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1361610: ldur            w1, [x0, #0x17]
    // 0x1361614: DecompressPointer r1
    //     0x1361614: add             x1, x1, HEAP, lsl #32
    // 0x1361618: CheckStackOverflow
    //     0x1361618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x136161c: cmp             SP, x16
    //     0x1361620: b.ls            #0x1361644
    // 0x1361624: LoadField: r0 = r1->field_f
    //     0x1361624: ldur            w0, [x1, #0xf]
    // 0x1361628: DecompressPointer r0
    //     0x1361628: add             x0, x0, HEAP, lsl #32
    // 0x136162c: mov             x1, x0
    // 0x1361630: r0 = onPhoneSubmitted()
    //     0x1361630: bl              #0x131dbcc  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onPhoneSubmitted
    // 0x1361634: r0 = Null
    //     0x1361634: mov             x0, NULL
    // 0x1361638: LeaveFrame
    //     0x1361638: mov             SP, fp
    //     0x136163c: ldp             fp, lr, [SP], #0x10
    // 0x1361640: ret
    //     0x1361640: ret             
    // 0x1361644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1361644: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1361648: b               #0x1361624
  }
  _ body(/* No info */) {
    // ** addr: 0x14ee06c, size: 0xc4
    // 0x14ee06c: EnterFrame
    //     0x14ee06c: stp             fp, lr, [SP, #-0x10]!
    //     0x14ee070: mov             fp, SP
    // 0x14ee074: AllocStack(0x18)
    //     0x14ee074: sub             SP, SP, #0x18
    // 0x14ee078: SetupParameters(LoginView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14ee078: mov             x0, x1
    //     0x14ee07c: stur            x1, [fp, #-8]
    //     0x14ee080: stur            x2, [fp, #-0x10]
    // 0x14ee084: r1 = 2
    //     0x14ee084: movz            x1, #0x2
    // 0x14ee088: r0 = AllocateContext()
    //     0x14ee088: bl              #0x16f6108  ; AllocateContextStub
    // 0x14ee08c: ldur            x2, [fp, #-8]
    // 0x14ee090: stur            x0, [fp, #-0x18]
    // 0x14ee094: StoreField: r0->field_f = r2
    //     0x14ee094: stur            w2, [x0, #0xf]
    // 0x14ee098: ldur            x1, [fp, #-0x10]
    // 0x14ee09c: StoreField: r0->field_13 = r1
    //     0x14ee09c: stur            w1, [x0, #0x13]
    // 0x14ee0a0: r0 = Obx()
    //     0x14ee0a0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14ee0a4: ldur            x2, [fp, #-0x18]
    // 0x14ee0a8: r1 = Function '<anonymous closure>':.
    //     0x14ee0a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40288] AnonymousClosure: (0x14ee168), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::body (0x14ee06c)
    //     0x14ee0ac: ldr             x1, [x1, #0x288]
    // 0x14ee0b0: stur            x0, [fp, #-0x10]
    // 0x14ee0b4: r0 = AllocateClosure()
    //     0x14ee0b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ee0b8: mov             x1, x0
    // 0x14ee0bc: ldur            x0, [fp, #-0x10]
    // 0x14ee0c0: StoreField: r0->field_b = r1
    //     0x14ee0c0: stur            w1, [x0, #0xb]
    // 0x14ee0c4: r0 = WillPopScope()
    //     0x14ee0c4: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14ee0c8: mov             x3, x0
    // 0x14ee0cc: ldur            x0, [fp, #-0x10]
    // 0x14ee0d0: stur            x3, [fp, #-0x18]
    // 0x14ee0d4: StoreField: r3->field_b = r0
    //     0x14ee0d4: stur            w0, [x3, #0xb]
    // 0x14ee0d8: ldur            x2, [fp, #-8]
    // 0x14ee0dc: r1 = Function 'onBackPress':.
    //     0x14ee0dc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40290] AnonymousClosure: (0x14ee130), in [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onBackPress (0x1401b54)
    //     0x14ee0e0: ldr             x1, [x1, #0x290]
    // 0x14ee0e4: r0 = AllocateClosure()
    //     0x14ee0e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ee0e8: mov             x1, x0
    // 0x14ee0ec: ldur            x0, [fp, #-0x18]
    // 0x14ee0f0: StoreField: r0->field_f = r1
    //     0x14ee0f0: stur            w1, [x0, #0xf]
    // 0x14ee0f4: r0 = SafeArea()
    //     0x14ee0f4: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14ee0f8: r1 = true
    //     0x14ee0f8: add             x1, NULL, #0x20  ; true
    // 0x14ee0fc: StoreField: r0->field_b = r1
    //     0x14ee0fc: stur            w1, [x0, #0xb]
    // 0x14ee100: StoreField: r0->field_f = r1
    //     0x14ee100: stur            w1, [x0, #0xf]
    // 0x14ee104: StoreField: r0->field_13 = r1
    //     0x14ee104: stur            w1, [x0, #0x13]
    // 0x14ee108: ArrayStore: r0[0] = r1  ; List_4
    //     0x14ee108: stur            w1, [x0, #0x17]
    // 0x14ee10c: r1 = Instance_EdgeInsets
    //     0x14ee10c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14ee110: StoreField: r0->field_1b = r1
    //     0x14ee110: stur            w1, [x0, #0x1b]
    // 0x14ee114: r1 = false
    //     0x14ee114: add             x1, NULL, #0x30  ; false
    // 0x14ee118: StoreField: r0->field_1f = r1
    //     0x14ee118: stur            w1, [x0, #0x1f]
    // 0x14ee11c: ldur            x1, [fp, #-0x18]
    // 0x14ee120: StoreField: r0->field_23 = r1
    //     0x14ee120: stur            w1, [x0, #0x23]
    // 0x14ee124: LeaveFrame
    //     0x14ee124: mov             SP, fp
    //     0x14ee128: ldp             fp, lr, [SP], #0x10
    // 0x14ee12c: ret
    //     0x14ee12c: ret             
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14ee130, size: 0x38
    // 0x14ee130: EnterFrame
    //     0x14ee130: stp             fp, lr, [SP, #-0x10]!
    //     0x14ee134: mov             fp, SP
    // 0x14ee138: ldr             x0, [fp, #0x10]
    // 0x14ee13c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14ee13c: ldur            w1, [x0, #0x17]
    // 0x14ee140: DecompressPointer r1
    //     0x14ee140: add             x1, x1, HEAP, lsl #32
    // 0x14ee144: CheckStackOverflow
    //     0x14ee144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ee148: cmp             SP, x16
    //     0x14ee14c: b.ls            #0x14ee160
    // 0x14ee150: r0 = onBackPress()
    //     0x14ee150: bl              #0x1401b54  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onBackPress
    // 0x14ee154: LeaveFrame
    //     0x14ee154: mov             SP, fp
    //     0x14ee158: ldp             fp, lr, [SP], #0x10
    // 0x14ee15c: ret
    //     0x14ee15c: ret             
    // 0x14ee160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ee160: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ee164: b               #0x14ee150
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14ee168, size: 0x428
    // 0x14ee168: EnterFrame
    //     0x14ee168: stp             fp, lr, [SP, #-0x10]!
    //     0x14ee16c: mov             fp, SP
    // 0x14ee170: AllocStack(0x58)
    //     0x14ee170: sub             SP, SP, #0x58
    // 0x14ee174: SetupParameters()
    //     0x14ee174: ldr             x0, [fp, #0x10]
    //     0x14ee178: ldur            w2, [x0, #0x17]
    //     0x14ee17c: add             x2, x2, HEAP, lsl #32
    //     0x14ee180: stur            x2, [fp, #-8]
    // 0x14ee184: CheckStackOverflow
    //     0x14ee184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ee188: cmp             SP, x16
    //     0x14ee18c: b.ls            #0x14ee588
    // 0x14ee190: LoadField: r1 = r2->field_f
    //     0x14ee190: ldur            w1, [x2, #0xf]
    // 0x14ee194: DecompressPointer r1
    //     0x14ee194: add             x1, x1, HEAP, lsl #32
    // 0x14ee198: r0 = controller()
    //     0x14ee198: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee19c: LoadField: r1 = r0->field_6b
    //     0x14ee19c: ldur            w1, [x0, #0x6b]
    // 0x14ee1a0: DecompressPointer r1
    //     0x14ee1a0: add             x1, x1, HEAP, lsl #32
    // 0x14ee1a4: r0 = value()
    //     0x14ee1a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee1a8: tbnz            w0, #4, #0x14ee1c8
    // 0x14ee1ac: r0 = Container()
    //     0x14ee1ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ee1b0: mov             x1, x0
    // 0x14ee1b4: stur            x0, [fp, #-0x10]
    // 0x14ee1b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14ee1b8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14ee1bc: r0 = Container()
    //     0x14ee1bc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ee1c0: ldur            x0, [fp, #-0x10]
    // 0x14ee1c4: b               #0x14ee57c
    // 0x14ee1c8: ldur            x0, [fp, #-8]
    // 0x14ee1cc: LoadField: r1 = r0->field_f
    //     0x14ee1cc: ldur            w1, [x0, #0xf]
    // 0x14ee1d0: DecompressPointer r1
    //     0x14ee1d0: add             x1, x1, HEAP, lsl #32
    // 0x14ee1d4: LoadField: r2 = r0->field_13
    //     0x14ee1d4: ldur            w2, [x0, #0x13]
    // 0x14ee1d8: DecompressPointer r2
    //     0x14ee1d8: add             x2, x2, HEAP, lsl #32
    // 0x14ee1dc: r0 = _getGlassThemeLoginForm()
    //     0x14ee1dc: bl              #0x14ee590  ; [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::_getGlassThemeLoginForm
    // 0x14ee1e0: ldur            x2, [fp, #-8]
    // 0x14ee1e4: stur            x0, [fp, #-0x10]
    // 0x14ee1e8: LoadField: r1 = r2->field_f
    //     0x14ee1e8: ldur            w1, [x2, #0xf]
    // 0x14ee1ec: DecompressPointer r1
    //     0x14ee1ec: add             x1, x1, HEAP, lsl #32
    // 0x14ee1f0: r0 = controller()
    //     0x14ee1f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee1f4: LoadField: r1 = r0->field_8b
    //     0x14ee1f4: ldur            w1, [x0, #0x8b]
    // 0x14ee1f8: DecompressPointer r1
    //     0x14ee1f8: add             x1, x1, HEAP, lsl #32
    // 0x14ee1fc: r0 = value()
    //     0x14ee1fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee200: cmp             w0, NULL
    // 0x14ee204: b.ne            #0x14ee20c
    // 0x14ee208: r0 = true
    //     0x14ee208: add             x0, NULL, #0x20  ; true
    // 0x14ee20c: ldur            x2, [fp, #-8]
    // 0x14ee210: stur            x0, [fp, #-0x18]
    // 0x14ee214: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14ee214: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14ee218: ldr             x0, [x0, #0x1c80]
    //     0x14ee21c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14ee220: cmp             w0, w16
    //     0x14ee224: b.ne            #0x14ee230
    //     0x14ee228: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14ee22c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14ee230: r0 = GetNavigation.width()
    //     0x14ee230: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x14ee234: stur            d0, [fp, #-0x40]
    // 0x14ee238: r0 = GetNavigation.size()
    //     0x14ee238: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14ee23c: LoadField: d0 = r0->field_f
    //     0x14ee23c: ldur            d0, [x0, #0xf]
    // 0x14ee240: d1 = 0.060000
    //     0x14ee240: add             x17, PP, #0x36, lsl #12  ; [pp+0x36f28] IMM: double(0.06) from 0x3faeb851eb851eb8
    //     0x14ee244: ldr             d1, [x17, #0xf28]
    // 0x14ee248: fmul            d2, d0, d1
    // 0x14ee24c: ldur            x2, [fp, #-8]
    // 0x14ee250: stur            d2, [fp, #-0x48]
    // 0x14ee254: LoadField: r1 = r2->field_13
    //     0x14ee254: ldur            w1, [x2, #0x13]
    // 0x14ee258: DecompressPointer r1
    //     0x14ee258: add             x1, x1, HEAP, lsl #32
    // 0x14ee25c: r0 = of()
    //     0x14ee25c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ee260: LoadField: r2 = r0->field_5b
    //     0x14ee260: ldur            w2, [x0, #0x5b]
    // 0x14ee264: DecompressPointer r2
    //     0x14ee264: add             x2, x2, HEAP, lsl #32
    // 0x14ee268: ldur            x0, [fp, #-8]
    // 0x14ee26c: stur            x2, [fp, #-0x20]
    // 0x14ee270: LoadField: r1 = r0->field_f
    //     0x14ee270: ldur            w1, [x0, #0xf]
    // 0x14ee274: DecompressPointer r1
    //     0x14ee274: add             x1, x1, HEAP, lsl #32
    // 0x14ee278: r0 = controller()
    //     0x14ee278: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee27c: LoadField: r1 = r0->field_8f
    //     0x14ee27c: ldur            w1, [x0, #0x8f]
    // 0x14ee280: DecompressPointer r1
    //     0x14ee280: add             x1, x1, HEAP, lsl #32
    // 0x14ee284: r0 = value()
    //     0x14ee284: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee288: cmp             w0, NULL
    // 0x14ee28c: b.eq            #0x14ee2ac
    // 0x14ee290: tbnz            w0, #4, #0x14ee2ac
    // 0x14ee294: ldur            x2, [fp, #-8]
    // 0x14ee298: r1 = Function '<anonymous closure>':.
    //     0x14ee298: add             x1, PP, #0x40, lsl #12  ; [pp+0x40298] AnonymousClosure: (0x14ef504), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::body (0x14ee06c)
    //     0x14ee29c: ldr             x1, [x1, #0x298]
    // 0x14ee2a0: r0 = AllocateClosure()
    //     0x14ee2a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ee2a4: mov             x5, x0
    // 0x14ee2a8: b               #0x14ee2b0
    // 0x14ee2ac: r5 = Null
    //     0x14ee2ac: mov             x5, NULL
    // 0x14ee2b0: ldur            x2, [fp, #-8]
    // 0x14ee2b4: ldur            x4, [fp, #-0x10]
    // 0x14ee2b8: ldur            x3, [fp, #-0x18]
    // 0x14ee2bc: ldur            d1, [fp, #-0x40]
    // 0x14ee2c0: ldur            d0, [fp, #-0x48]
    // 0x14ee2c4: ldur            x0, [fp, #-0x20]
    // 0x14ee2c8: stur            x5, [fp, #-0x28]
    // 0x14ee2cc: r1 = "verify"
    //     0x14ee2cc: add             x1, PP, #0x40, lsl #12  ; [pp+0x402a0] "verify"
    //     0x14ee2d0: ldr             x1, [x1, #0x2a0]
    // 0x14ee2d4: r0 = capitalizeFirstWord()
    //     0x14ee2d4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14ee2d8: mov             x2, x0
    // 0x14ee2dc: ldur            x0, [fp, #-8]
    // 0x14ee2e0: stur            x2, [fp, #-0x30]
    // 0x14ee2e4: LoadField: r1 = r0->field_13
    //     0x14ee2e4: ldur            w1, [x0, #0x13]
    // 0x14ee2e8: DecompressPointer r1
    //     0x14ee2e8: add             x1, x1, HEAP, lsl #32
    // 0x14ee2ec: r0 = of()
    //     0x14ee2ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ee2f0: LoadField: r1 = r0->field_87
    //     0x14ee2f0: ldur            w1, [x0, #0x87]
    // 0x14ee2f4: DecompressPointer r1
    //     0x14ee2f4: add             x1, x1, HEAP, lsl #32
    // 0x14ee2f8: LoadField: r0 = r1->field_7
    //     0x14ee2f8: ldur            w0, [x1, #7]
    // 0x14ee2fc: DecompressPointer r0
    //     0x14ee2fc: add             x0, x0, HEAP, lsl #32
    // 0x14ee300: r16 = 16.000000
    //     0x14ee300: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14ee304: ldr             x16, [x16, #0x188]
    // 0x14ee308: r30 = Instance_Color
    //     0x14ee308: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14ee30c: stp             lr, x16, [SP]
    // 0x14ee310: mov             x1, x0
    // 0x14ee314: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14ee314: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14ee318: ldr             x4, [x4, #0xaa0]
    // 0x14ee31c: r0 = copyWith()
    //     0x14ee31c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ee320: stur            x0, [fp, #-8]
    // 0x14ee324: r0 = Text()
    //     0x14ee324: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ee328: mov             x1, x0
    // 0x14ee32c: ldur            x0, [fp, #-0x30]
    // 0x14ee330: stur            x1, [fp, #-0x38]
    // 0x14ee334: StoreField: r1->field_b = r0
    //     0x14ee334: stur            w0, [x1, #0xb]
    // 0x14ee338: ldur            x0, [fp, #-8]
    // 0x14ee33c: StoreField: r1->field_13 = r0
    //     0x14ee33c: stur            w0, [x1, #0x13]
    // 0x14ee340: r0 = MaterialButton()
    //     0x14ee340: bl              #0x131db78  ; AllocateMaterialButtonStub -> MaterialButton (size=0x88)
    // 0x14ee344: mov             x1, x0
    // 0x14ee348: ldur            x0, [fp, #-0x28]
    // 0x14ee34c: stur            x1, [fp, #-8]
    // 0x14ee350: StoreField: r1->field_b = r0
    //     0x14ee350: stur            w0, [x1, #0xb]
    // 0x14ee354: r0 = Instance_Color
    //     0x14ee354: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14ee358: StoreField: r1->field_1f = r0
    //     0x14ee358: stur            w0, [x1, #0x1f]
    // 0x14ee35c: ldur            x0, [fp, #-0x20]
    // 0x14ee360: StoreField: r1->field_23 = r0
    //     0x14ee360: stur            w0, [x1, #0x23]
    // 0x14ee364: r0 = Instance_Color
    //     0x14ee364: add             x0, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0x14ee368: ldr             x0, [x0, #0x7b8]
    // 0x14ee36c: StoreField: r1->field_27 = r0
    //     0x14ee36c: stur            w0, [x1, #0x27]
    // 0x14ee370: r0 = Instance_RoundedRectangleBorder
    //     0x14ee370: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x14ee374: ldr             x0, [x0, #0x888]
    // 0x14ee378: StoreField: r1->field_5b = r0
    //     0x14ee378: stur            w0, [x1, #0x5b]
    // 0x14ee37c: r0 = Instance_Clip
    //     0x14ee37c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ee380: ldr             x0, [x0, #0x38]
    // 0x14ee384: StoreField: r1->field_5f = r0
    //     0x14ee384: stur            w0, [x1, #0x5f]
    // 0x14ee388: r2 = false
    //     0x14ee388: add             x2, NULL, #0x30  ; false
    // 0x14ee38c: StoreField: r1->field_67 = r2
    //     0x14ee38c: stur            w2, [x1, #0x67]
    // 0x14ee390: ldur            d0, [fp, #-0x40]
    // 0x14ee394: StoreField: r1->field_73 = d0
    //     0x14ee394: stur            d0, [x1, #0x73]
    // 0x14ee398: ldur            d0, [fp, #-0x48]
    // 0x14ee39c: StoreField: r1->field_7b = d0
    //     0x14ee39c: stur            d0, [x1, #0x7b]
    // 0x14ee3a0: r3 = true
    //     0x14ee3a0: add             x3, NULL, #0x20  ; true
    // 0x14ee3a4: StoreField: r1->field_83 = r3
    //     0x14ee3a4: stur            w3, [x1, #0x83]
    // 0x14ee3a8: ldur            x3, [fp, #-0x38]
    // 0x14ee3ac: StoreField: r1->field_4f = r3
    //     0x14ee3ac: stur            w3, [x1, #0x4f]
    // 0x14ee3b0: r3 = Instance_ValueKey
    //     0x14ee3b0: add             x3, PP, #0x37, lsl #12  ; [pp+0x37098] Obj!ValueKey<String>@d5b3c1
    //     0x14ee3b4: ldr             x3, [x3, #0x98]
    // 0x14ee3b8: StoreField: r1->field_7 = r3
    //     0x14ee3b8: stur            w3, [x1, #7]
    // 0x14ee3bc: r0 = Padding()
    //     0x14ee3bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ee3c0: mov             x1, x0
    // 0x14ee3c4: r0 = Instance_EdgeInsets
    //     0x14ee3c4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x14ee3c8: ldr             x0, [x0, #0xf98]
    // 0x14ee3cc: stur            x1, [fp, #-0x20]
    // 0x14ee3d0: StoreField: r1->field_f = r0
    //     0x14ee3d0: stur            w0, [x1, #0xf]
    // 0x14ee3d4: ldur            x0, [fp, #-8]
    // 0x14ee3d8: StoreField: r1->field_b = r0
    //     0x14ee3d8: stur            w0, [x1, #0xb]
    // 0x14ee3dc: r0 = Container()
    //     0x14ee3dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ee3e0: stur            x0, [fp, #-8]
    // 0x14ee3e4: r16 = Instance_BoxDecoration
    //     0x14ee3e4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40280] Obj!BoxDecoration@d64ce1
    //     0x14ee3e8: ldr             x16, [x16, #0x280]
    // 0x14ee3ec: ldur            lr, [fp, #-0x20]
    // 0x14ee3f0: stp             lr, x16, [SP]
    // 0x14ee3f4: mov             x1, x0
    // 0x14ee3f8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14ee3f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14ee3fc: ldr             x4, [x4, #0x88]
    // 0x14ee400: r0 = Container()
    //     0x14ee400: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ee404: r0 = Visibility()
    //     0x14ee404: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14ee408: mov             x3, x0
    // 0x14ee40c: ldur            x0, [fp, #-8]
    // 0x14ee410: stur            x3, [fp, #-0x20]
    // 0x14ee414: StoreField: r3->field_b = r0
    //     0x14ee414: stur            w0, [x3, #0xb]
    // 0x14ee418: r0 = Instance_SizedBox
    //     0x14ee418: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14ee41c: StoreField: r3->field_f = r0
    //     0x14ee41c: stur            w0, [x3, #0xf]
    // 0x14ee420: ldur            x0, [fp, #-0x18]
    // 0x14ee424: StoreField: r3->field_13 = r0
    //     0x14ee424: stur            w0, [x3, #0x13]
    // 0x14ee428: r0 = false
    //     0x14ee428: add             x0, NULL, #0x30  ; false
    // 0x14ee42c: ArrayStore: r3[0] = r0  ; List_4
    //     0x14ee42c: stur            w0, [x3, #0x17]
    // 0x14ee430: StoreField: r3->field_1b = r0
    //     0x14ee430: stur            w0, [x3, #0x1b]
    // 0x14ee434: StoreField: r3->field_1f = r0
    //     0x14ee434: stur            w0, [x3, #0x1f]
    // 0x14ee438: StoreField: r3->field_23 = r0
    //     0x14ee438: stur            w0, [x3, #0x23]
    // 0x14ee43c: StoreField: r3->field_27 = r0
    //     0x14ee43c: stur            w0, [x3, #0x27]
    // 0x14ee440: StoreField: r3->field_2b = r0
    //     0x14ee440: stur            w0, [x3, #0x2b]
    // 0x14ee444: r1 = Null
    //     0x14ee444: mov             x1, NULL
    // 0x14ee448: r2 = 2
    //     0x14ee448: movz            x2, #0x2
    // 0x14ee44c: r0 = AllocateArray()
    //     0x14ee44c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ee450: mov             x2, x0
    // 0x14ee454: ldur            x0, [fp, #-0x20]
    // 0x14ee458: stur            x2, [fp, #-8]
    // 0x14ee45c: StoreField: r2->field_f = r0
    //     0x14ee45c: stur            w0, [x2, #0xf]
    // 0x14ee460: r1 = <Widget>
    //     0x14ee460: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ee464: r0 = AllocateGrowableArray()
    //     0x14ee464: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ee468: mov             x1, x0
    // 0x14ee46c: ldur            x0, [fp, #-8]
    // 0x14ee470: stur            x1, [fp, #-0x18]
    // 0x14ee474: StoreField: r1->field_f = r0
    //     0x14ee474: stur            w0, [x1, #0xf]
    // 0x14ee478: r0 = 2
    //     0x14ee478: movz            x0, #0x2
    // 0x14ee47c: StoreField: r1->field_b = r0
    //     0x14ee47c: stur            w0, [x1, #0xb]
    // 0x14ee480: r0 = Column()
    //     0x14ee480: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14ee484: mov             x2, x0
    // 0x14ee488: r0 = Instance_Axis
    //     0x14ee488: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14ee48c: stur            x2, [fp, #-8]
    // 0x14ee490: StoreField: r2->field_f = r0
    //     0x14ee490: stur            w0, [x2, #0xf]
    // 0x14ee494: r0 = Instance_MainAxisAlignment
    //     0x14ee494: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ee498: ldr             x0, [x0, #0xa08]
    // 0x14ee49c: StoreField: r2->field_13 = r0
    //     0x14ee49c: stur            w0, [x2, #0x13]
    // 0x14ee4a0: r0 = Instance_MainAxisSize
    //     0x14ee4a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ee4a4: ldr             x0, [x0, #0xa10]
    // 0x14ee4a8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ee4a8: stur            w0, [x2, #0x17]
    // 0x14ee4ac: r0 = Instance_CrossAxisAlignment
    //     0x14ee4ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14ee4b0: ldr             x0, [x0, #0xa18]
    // 0x14ee4b4: StoreField: r2->field_1b = r0
    //     0x14ee4b4: stur            w0, [x2, #0x1b]
    // 0x14ee4b8: r0 = Instance_VerticalDirection
    //     0x14ee4b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ee4bc: ldr             x0, [x0, #0xa20]
    // 0x14ee4c0: StoreField: r2->field_23 = r0
    //     0x14ee4c0: stur            w0, [x2, #0x23]
    // 0x14ee4c4: r0 = Instance_Clip
    //     0x14ee4c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ee4c8: ldr             x0, [x0, #0x38]
    // 0x14ee4cc: StoreField: r2->field_2b = r0
    //     0x14ee4cc: stur            w0, [x2, #0x2b]
    // 0x14ee4d0: StoreField: r2->field_2f = rZR
    //     0x14ee4d0: stur            xzr, [x2, #0x2f]
    // 0x14ee4d4: ldur            x0, [fp, #-0x18]
    // 0x14ee4d8: StoreField: r2->field_b = r0
    //     0x14ee4d8: stur            w0, [x2, #0xb]
    // 0x14ee4dc: r1 = <StackParentData>
    //     0x14ee4dc: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14ee4e0: ldr             x1, [x1, #0x8e0]
    // 0x14ee4e4: r0 = Positioned()
    //     0x14ee4e4: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14ee4e8: mov             x3, x0
    // 0x14ee4ec: r0 = 0.000000
    //     0x14ee4ec: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14ee4f0: stur            x3, [fp, #-0x18]
    // 0x14ee4f4: StoreField: r3->field_13 = r0
    //     0x14ee4f4: stur            w0, [x3, #0x13]
    // 0x14ee4f8: StoreField: r3->field_1b = r0
    //     0x14ee4f8: stur            w0, [x3, #0x1b]
    // 0x14ee4fc: StoreField: r3->field_1f = r0
    //     0x14ee4fc: stur            w0, [x3, #0x1f]
    // 0x14ee500: ldur            x0, [fp, #-8]
    // 0x14ee504: StoreField: r3->field_b = r0
    //     0x14ee504: stur            w0, [x3, #0xb]
    // 0x14ee508: r1 = Null
    //     0x14ee508: mov             x1, NULL
    // 0x14ee50c: r2 = 4
    //     0x14ee50c: movz            x2, #0x4
    // 0x14ee510: r0 = AllocateArray()
    //     0x14ee510: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ee514: mov             x2, x0
    // 0x14ee518: ldur            x0, [fp, #-0x10]
    // 0x14ee51c: stur            x2, [fp, #-8]
    // 0x14ee520: StoreField: r2->field_f = r0
    //     0x14ee520: stur            w0, [x2, #0xf]
    // 0x14ee524: ldur            x0, [fp, #-0x18]
    // 0x14ee528: StoreField: r2->field_13 = r0
    //     0x14ee528: stur            w0, [x2, #0x13]
    // 0x14ee52c: r1 = <Widget>
    //     0x14ee52c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ee530: r0 = AllocateGrowableArray()
    //     0x14ee530: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ee534: mov             x1, x0
    // 0x14ee538: ldur            x0, [fp, #-8]
    // 0x14ee53c: stur            x1, [fp, #-0x10]
    // 0x14ee540: StoreField: r1->field_f = r0
    //     0x14ee540: stur            w0, [x1, #0xf]
    // 0x14ee544: r0 = 4
    //     0x14ee544: movz            x0, #0x4
    // 0x14ee548: StoreField: r1->field_b = r0
    //     0x14ee548: stur            w0, [x1, #0xb]
    // 0x14ee54c: r0 = Stack()
    //     0x14ee54c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14ee550: r1 = Instance_AlignmentDirectional
    //     0x14ee550: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0x14ee554: ldr             x1, [x1, #0xd08]
    // 0x14ee558: StoreField: r0->field_f = r1
    //     0x14ee558: stur            w1, [x0, #0xf]
    // 0x14ee55c: r1 = Instance_StackFit
    //     0x14ee55c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14ee560: ldr             x1, [x1, #0xfa8]
    // 0x14ee564: ArrayStore: r0[0] = r1  ; List_4
    //     0x14ee564: stur            w1, [x0, #0x17]
    // 0x14ee568: r1 = Instance_Clip
    //     0x14ee568: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14ee56c: ldr             x1, [x1, #0x7e0]
    // 0x14ee570: StoreField: r0->field_1b = r1
    //     0x14ee570: stur            w1, [x0, #0x1b]
    // 0x14ee574: ldur            x1, [fp, #-0x10]
    // 0x14ee578: StoreField: r0->field_b = r1
    //     0x14ee578: stur            w1, [x0, #0xb]
    // 0x14ee57c: LeaveFrame
    //     0x14ee57c: mov             SP, fp
    //     0x14ee580: ldp             fp, lr, [SP], #0x10
    // 0x14ee584: ret
    //     0x14ee584: ret             
    // 0x14ee588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ee588: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ee58c: b               #0x14ee190
  }
  _ _getGlassThemeLoginForm(/* No info */) {
    // ** addr: 0x14ee590, size: 0xa8
    // 0x14ee590: EnterFrame
    //     0x14ee590: stp             fp, lr, [SP, #-0x10]!
    //     0x14ee594: mov             fp, SP
    // 0x14ee598: AllocStack(0x18)
    //     0x14ee598: sub             SP, SP, #0x18
    // 0x14ee59c: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14ee59c: stur            x1, [fp, #-8]
    //     0x14ee5a0: stur            x2, [fp, #-0x10]
    // 0x14ee5a4: CheckStackOverflow
    //     0x14ee5a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ee5a8: cmp             SP, x16
    //     0x14ee5ac: b.ls            #0x14ee630
    // 0x14ee5b0: r1 = 2
    //     0x14ee5b0: movz            x1, #0x2
    // 0x14ee5b4: r0 = AllocateContext()
    //     0x14ee5b4: bl              #0x16f6108  ; AllocateContextStub
    // 0x14ee5b8: mov             x2, x0
    // 0x14ee5bc: ldur            x0, [fp, #-8]
    // 0x14ee5c0: stur            x2, [fp, #-0x18]
    // 0x14ee5c4: StoreField: r2->field_f = r0
    //     0x14ee5c4: stur            w0, [x2, #0xf]
    // 0x14ee5c8: ldur            x1, [fp, #-0x10]
    // 0x14ee5cc: StoreField: r2->field_13 = r1
    //     0x14ee5cc: stur            w1, [x2, #0x13]
    // 0x14ee5d0: mov             x1, x0
    // 0x14ee5d4: r0 = controller()
    //     0x14ee5d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee5d8: LoadField: r1 = r0->field_8b
    //     0x14ee5d8: ldur            w1, [x0, #0x8b]
    // 0x14ee5dc: DecompressPointer r1
    //     0x14ee5dc: add             x1, x1, HEAP, lsl #32
    // 0x14ee5e0: r0 = value()
    //     0x14ee5e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee5e4: cmp             w0, NULL
    // 0x14ee5e8: b.eq            #0x14ee5f0
    // 0x14ee5ec: tbnz            w0, #4, #0x14ee600
    // 0x14ee5f0: ldur            x1, [fp, #-8]
    // 0x14ee5f4: r0 = controller()
    //     0x14ee5f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee5f8: mov             x1, x0
    // 0x14ee5fc: r0 = startTimer()
    //     0x14ee5fc: bl              #0x1402244  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::startTimer
    // 0x14ee600: r0 = Obx()
    //     0x14ee600: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14ee604: ldur            x2, [fp, #-0x18]
    // 0x14ee608: r1 = Function '<anonymous closure>':.
    //     0x14ee608: add             x1, PP, #0x40, lsl #12  ; [pp+0x402a8] AnonymousClosure: (0x14ee638), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::_getGlassThemeLoginForm (0x14ee590)
    //     0x14ee60c: ldr             x1, [x1, #0x2a8]
    // 0x14ee610: stur            x0, [fp, #-8]
    // 0x14ee614: r0 = AllocateClosure()
    //     0x14ee614: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ee618: mov             x1, x0
    // 0x14ee61c: ldur            x0, [fp, #-8]
    // 0x14ee620: StoreField: r0->field_b = r1
    //     0x14ee620: stur            w1, [x0, #0xb]
    // 0x14ee624: LeaveFrame
    //     0x14ee624: mov             SP, fp
    //     0x14ee628: ldp             fp, lr, [SP], #0x10
    // 0x14ee62c: ret
    //     0x14ee62c: ret             
    // 0x14ee630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ee630: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ee634: b               #0x14ee5b0
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x14ee638, size: 0xd78
    // 0x14ee638: EnterFrame
    //     0x14ee638: stp             fp, lr, [SP, #-0x10]!
    //     0x14ee63c: mov             fp, SP
    // 0x14ee640: AllocStack(0xd0)
    //     0x14ee640: sub             SP, SP, #0xd0
    // 0x14ee644: SetupParameters()
    //     0x14ee644: ldr             x0, [fp, #0x10]
    //     0x14ee648: ldur            w2, [x0, #0x17]
    //     0x14ee64c: add             x2, x2, HEAP, lsl #32
    //     0x14ee650: stur            x2, [fp, #-8]
    // 0x14ee654: CheckStackOverflow
    //     0x14ee654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ee658: cmp             SP, x16
    //     0x14ee65c: b.ls            #0x14ef38c
    // 0x14ee660: LoadField: r1 = r2->field_f
    //     0x14ee660: ldur            w1, [x2, #0xf]
    // 0x14ee664: DecompressPointer r1
    //     0x14ee664: add             x1, x1, HEAP, lsl #32
    // 0x14ee668: r0 = controller()
    //     0x14ee668: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee66c: LoadField: r1 = r0->field_8b
    //     0x14ee66c: ldur            w1, [x0, #0x8b]
    // 0x14ee670: DecompressPointer r1
    //     0x14ee670: add             x1, x1, HEAP, lsl #32
    // 0x14ee674: r0 = value()
    //     0x14ee674: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee678: cmp             w0, NULL
    // 0x14ee67c: b.eq            #0x14ee694
    // 0x14ee680: tbz             w0, #4, #0x14ee694
    // 0x14ee684: r1 = "enter mobile number"
    //     0x14ee684: add             x1, PP, #0x40, lsl #12  ; [pp+0x402b0] "enter mobile number"
    //     0x14ee688: ldr             x1, [x1, #0x2b0]
    // 0x14ee68c: r0 = capitalizeFirstWord()
    //     0x14ee68c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14ee690: b               #0x14ee69c
    // 0x14ee694: r0 = "Enter OTP"
    //     0x14ee694: add             x0, PP, #0x40, lsl #12  ; [pp+0x402b8] "Enter OTP"
    //     0x14ee698: ldr             x0, [x0, #0x2b8]
    // 0x14ee69c: ldur            x2, [fp, #-8]
    // 0x14ee6a0: stur            x0, [fp, #-0x10]
    // 0x14ee6a4: LoadField: r1 = r2->field_13
    //     0x14ee6a4: ldur            w1, [x2, #0x13]
    // 0x14ee6a8: DecompressPointer r1
    //     0x14ee6a8: add             x1, x1, HEAP, lsl #32
    // 0x14ee6ac: r0 = of()
    //     0x14ee6ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ee6b0: LoadField: r1 = r0->field_87
    //     0x14ee6b0: ldur            w1, [x0, #0x87]
    // 0x14ee6b4: DecompressPointer r1
    //     0x14ee6b4: add             x1, x1, HEAP, lsl #32
    // 0x14ee6b8: LoadField: r0 = r1->field_7
    //     0x14ee6b8: ldur            w0, [x1, #7]
    // 0x14ee6bc: DecompressPointer r0
    //     0x14ee6bc: add             x0, x0, HEAP, lsl #32
    // 0x14ee6c0: r16 = 16.000000
    //     0x14ee6c0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14ee6c4: ldr             x16, [x16, #0x188]
    // 0x14ee6c8: r30 = Instance_Color
    //     0x14ee6c8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ee6cc: stp             lr, x16, [SP]
    // 0x14ee6d0: mov             x1, x0
    // 0x14ee6d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14ee6d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14ee6d8: ldr             x4, [x4, #0xaa0]
    // 0x14ee6dc: r0 = copyWith()
    //     0x14ee6dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ee6e0: stur            x0, [fp, #-0x18]
    // 0x14ee6e4: r0 = Text()
    //     0x14ee6e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ee6e8: mov             x1, x0
    // 0x14ee6ec: ldur            x0, [fp, #-0x10]
    // 0x14ee6f0: stur            x1, [fp, #-0x20]
    // 0x14ee6f4: StoreField: r1->field_b = r0
    //     0x14ee6f4: stur            w0, [x1, #0xb]
    // 0x14ee6f8: ldur            x0, [fp, #-0x18]
    // 0x14ee6fc: StoreField: r1->field_13 = r0
    //     0x14ee6fc: stur            w0, [x1, #0x13]
    // 0x14ee700: r0 = Padding()
    //     0x14ee700: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ee704: mov             x2, x0
    // 0x14ee708: r0 = Instance_EdgeInsets
    //     0x14ee708: add             x0, PP, #0x40, lsl #12  ; [pp+0x402c0] Obj!EdgeInsets@d59e11
    //     0x14ee70c: ldr             x0, [x0, #0x2c0]
    // 0x14ee710: stur            x2, [fp, #-0x10]
    // 0x14ee714: StoreField: r2->field_f = r0
    //     0x14ee714: stur            w0, [x2, #0xf]
    // 0x14ee718: ldur            x0, [fp, #-0x20]
    // 0x14ee71c: StoreField: r2->field_b = r0
    //     0x14ee71c: stur            w0, [x2, #0xb]
    // 0x14ee720: ldur            x0, [fp, #-8]
    // 0x14ee724: LoadField: r1 = r0->field_f
    //     0x14ee724: ldur            w1, [x0, #0xf]
    // 0x14ee728: DecompressPointer r1
    //     0x14ee728: add             x1, x1, HEAP, lsl #32
    // 0x14ee72c: r0 = controller()
    //     0x14ee72c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee730: LoadField: r1 = r0->field_8b
    //     0x14ee730: ldur            w1, [x0, #0x8b]
    // 0x14ee734: DecompressPointer r1
    //     0x14ee734: add             x1, x1, HEAP, lsl #32
    // 0x14ee738: r0 = value()
    //     0x14ee738: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee73c: cmp             w0, NULL
    // 0x14ee740: b.ne            #0x14ee74c
    // 0x14ee744: r3 = true
    //     0x14ee744: add             x3, NULL, #0x20  ; true
    // 0x14ee748: b               #0x14ee750
    // 0x14ee74c: mov             x3, x0
    // 0x14ee750: ldur            x0, [fp, #-8]
    // 0x14ee754: stur            x3, [fp, #-0x18]
    // 0x14ee758: r1 = Null
    //     0x14ee758: mov             x1, NULL
    // 0x14ee75c: r2 = 4
    //     0x14ee75c: movz            x2, #0x4
    // 0x14ee760: r0 = AllocateArray()
    //     0x14ee760: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ee764: r16 = "A 4 digit code has been sent on "
    //     0x14ee764: add             x16, PP, #0x37, lsl #12  ; [pp+0x37110] "A 4 digit code has been sent on "
    //     0x14ee768: ldr             x16, [x16, #0x110]
    // 0x14ee76c: StoreField: r0->field_f = r16
    //     0x14ee76c: stur            w16, [x0, #0xf]
    // 0x14ee770: ldur            x2, [fp, #-8]
    // 0x14ee774: LoadField: r1 = r2->field_f
    //     0x14ee774: ldur            w1, [x2, #0xf]
    // 0x14ee778: DecompressPointer r1
    //     0x14ee778: add             x1, x1, HEAP, lsl #32
    // 0x14ee77c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x14ee77c: ldur            w3, [x1, #0x17]
    // 0x14ee780: DecompressPointer r3
    //     0x14ee780: add             x3, x3, HEAP, lsl #32
    // 0x14ee784: StoreField: r0->field_13 = r3
    //     0x14ee784: stur            w3, [x0, #0x13]
    // 0x14ee788: str             x0, [SP]
    // 0x14ee78c: r0 = _interpolate()
    //     0x14ee78c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14ee790: ldur            x2, [fp, #-8]
    // 0x14ee794: stur            x0, [fp, #-0x20]
    // 0x14ee798: LoadField: r1 = r2->field_13
    //     0x14ee798: ldur            w1, [x2, #0x13]
    // 0x14ee79c: DecompressPointer r1
    //     0x14ee79c: add             x1, x1, HEAP, lsl #32
    // 0x14ee7a0: r0 = of()
    //     0x14ee7a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ee7a4: LoadField: r1 = r0->field_87
    //     0x14ee7a4: ldur            w1, [x0, #0x87]
    // 0x14ee7a8: DecompressPointer r1
    //     0x14ee7a8: add             x1, x1, HEAP, lsl #32
    // 0x14ee7ac: LoadField: r0 = r1->field_2b
    //     0x14ee7ac: ldur            w0, [x1, #0x2b]
    // 0x14ee7b0: DecompressPointer r0
    //     0x14ee7b0: add             x0, x0, HEAP, lsl #32
    // 0x14ee7b4: stur            x0, [fp, #-0x28]
    // 0x14ee7b8: r1 = Instance_Color
    //     0x14ee7b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ee7bc: d0 = 0.400000
    //     0x14ee7bc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14ee7c0: r0 = withOpacity()
    //     0x14ee7c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14ee7c4: r16 = 14.000000
    //     0x14ee7c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14ee7c8: ldr             x16, [x16, #0x1d8]
    // 0x14ee7cc: stp             x0, x16, [SP]
    // 0x14ee7d0: ldur            x1, [fp, #-0x28]
    // 0x14ee7d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14ee7d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14ee7d8: ldr             x4, [x4, #0xaa0]
    // 0x14ee7dc: r0 = copyWith()
    //     0x14ee7dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ee7e0: stur            x0, [fp, #-0x28]
    // 0x14ee7e4: r0 = Text()
    //     0x14ee7e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ee7e8: mov             x1, x0
    // 0x14ee7ec: ldur            x0, [fp, #-0x20]
    // 0x14ee7f0: stur            x1, [fp, #-0x30]
    // 0x14ee7f4: StoreField: r1->field_b = r0
    //     0x14ee7f4: stur            w0, [x1, #0xb]
    // 0x14ee7f8: ldur            x0, [fp, #-0x28]
    // 0x14ee7fc: StoreField: r1->field_13 = r0
    //     0x14ee7fc: stur            w0, [x1, #0x13]
    // 0x14ee800: r0 = Padding()
    //     0x14ee800: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ee804: mov             x1, x0
    // 0x14ee808: r0 = Instance_EdgeInsets
    //     0x14ee808: add             x0, PP, #0x40, lsl #12  ; [pp+0x402c8] Obj!EdgeInsets@d59de1
    //     0x14ee80c: ldr             x0, [x0, #0x2c8]
    // 0x14ee810: stur            x1, [fp, #-0x20]
    // 0x14ee814: StoreField: r1->field_f = r0
    //     0x14ee814: stur            w0, [x1, #0xf]
    // 0x14ee818: ldur            x0, [fp, #-0x30]
    // 0x14ee81c: StoreField: r1->field_b = r0
    //     0x14ee81c: stur            w0, [x1, #0xb]
    // 0x14ee820: r0 = Visibility()
    //     0x14ee820: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14ee824: mov             x2, x0
    // 0x14ee828: ldur            x0, [fp, #-0x20]
    // 0x14ee82c: stur            x2, [fp, #-0x28]
    // 0x14ee830: StoreField: r2->field_b = r0
    //     0x14ee830: stur            w0, [x2, #0xb]
    // 0x14ee834: r0 = Instance_SizedBox
    //     0x14ee834: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14ee838: StoreField: r2->field_f = r0
    //     0x14ee838: stur            w0, [x2, #0xf]
    // 0x14ee83c: ldur            x1, [fp, #-0x18]
    // 0x14ee840: StoreField: r2->field_13 = r1
    //     0x14ee840: stur            w1, [x2, #0x13]
    // 0x14ee844: r3 = false
    //     0x14ee844: add             x3, NULL, #0x30  ; false
    // 0x14ee848: ArrayStore: r2[0] = r3  ; List_4
    //     0x14ee848: stur            w3, [x2, #0x17]
    // 0x14ee84c: StoreField: r2->field_1b = r3
    //     0x14ee84c: stur            w3, [x2, #0x1b]
    // 0x14ee850: StoreField: r2->field_1f = r3
    //     0x14ee850: stur            w3, [x2, #0x1f]
    // 0x14ee854: StoreField: r2->field_23 = r3
    //     0x14ee854: stur            w3, [x2, #0x23]
    // 0x14ee858: StoreField: r2->field_27 = r3
    //     0x14ee858: stur            w3, [x2, #0x27]
    // 0x14ee85c: StoreField: r2->field_2b = r3
    //     0x14ee85c: stur            w3, [x2, #0x2b]
    // 0x14ee860: ldur            x4, [fp, #-8]
    // 0x14ee864: LoadField: r1 = r4->field_f
    //     0x14ee864: ldur            w1, [x4, #0xf]
    // 0x14ee868: DecompressPointer r1
    //     0x14ee868: add             x1, x1, HEAP, lsl #32
    // 0x14ee86c: r0 = controller()
    //     0x14ee86c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ee870: LoadField: r1 = r0->field_8b
    //     0x14ee870: ldur            w1, [x0, #0x8b]
    // 0x14ee874: DecompressPointer r1
    //     0x14ee874: add             x1, x1, HEAP, lsl #32
    // 0x14ee878: r0 = value()
    //     0x14ee878: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ee87c: cmp             w0, NULL
    // 0x14ee880: b.ne            #0x14ee888
    // 0x14ee884: r0 = true
    //     0x14ee884: add             x0, NULL, #0x20  ; true
    // 0x14ee888: ldur            x2, [fp, #-8]
    // 0x14ee88c: eor             x1, x0, #0x10
    // 0x14ee890: stur            x1, [fp, #-0x30]
    // 0x14ee894: LoadField: r0 = r2->field_f
    //     0x14ee894: ldur            w0, [x2, #0xf]
    // 0x14ee898: DecompressPointer r0
    //     0x14ee898: add             x0, x0, HEAP, lsl #32
    // 0x14ee89c: stur            x0, [fp, #-0x20]
    // 0x14ee8a0: LoadField: r3 = r0->field_13
    //     0x14ee8a0: ldur            w3, [x0, #0x13]
    // 0x14ee8a4: DecompressPointer r3
    //     0x14ee8a4: add             x3, x3, HEAP, lsl #32
    // 0x14ee8a8: stur            x3, [fp, #-0x18]
    // 0x14ee8ac: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0x14ee8ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14ee8b0: ldr             x0, [x0, #0x1530]
    //     0x14ee8b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14ee8b8: cmp             w0, w16
    //     0x14ee8bc: b.ne            #0x14ee8cc
    //     0x14ee8c0: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0x14ee8c4: ldr             x2, [x2, #0x120]
    //     0x14ee8c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14ee8cc: stur            x0, [fp, #-0x38]
    // 0x14ee8d0: r16 = "[0-9]"
    //     0x14ee8d0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0x14ee8d4: ldr             x16, [x16, #0x128]
    // 0x14ee8d8: stp             x16, NULL, [SP, #0x20]
    // 0x14ee8dc: r16 = false
    //     0x14ee8dc: add             x16, NULL, #0x30  ; false
    // 0x14ee8e0: r30 = true
    //     0x14ee8e0: add             lr, NULL, #0x20  ; true
    // 0x14ee8e4: stp             lr, x16, [SP, #0x10]
    // 0x14ee8e8: r16 = false
    //     0x14ee8e8: add             x16, NULL, #0x30  ; false
    // 0x14ee8ec: r30 = false
    //     0x14ee8ec: add             lr, NULL, #0x30  ; false
    // 0x14ee8f0: stp             lr, x16, [SP]
    // 0x14ee8f4: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x14ee8f4: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x14ee8f8: r0 = _RegExp()
    //     0x14ee8f8: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x14ee8fc: stur            x0, [fp, #-0x40]
    // 0x14ee900: r0 = FilteringTextInputFormatter()
    //     0x14ee900: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0x14ee904: mov             x1, x0
    // 0x14ee908: ldur            x0, [fp, #-0x40]
    // 0x14ee90c: stur            x1, [fp, #-0x48]
    // 0x14ee910: StoreField: r1->field_b = r0
    //     0x14ee910: stur            w0, [x1, #0xb]
    // 0x14ee914: r0 = true
    //     0x14ee914: add             x0, NULL, #0x20  ; true
    // 0x14ee918: StoreField: r1->field_7 = r0
    //     0x14ee918: stur            w0, [x1, #7]
    // 0x14ee91c: r2 = ""
    //     0x14ee91c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14ee920: StoreField: r1->field_f = r2
    //     0x14ee920: stur            w2, [x1, #0xf]
    // 0x14ee924: r0 = LengthLimitingTextInputFormatter()
    //     0x14ee924: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14ee928: mov             x3, x0
    // 0x14ee92c: r0 = 20
    //     0x14ee92c: movz            x0, #0x14
    // 0x14ee930: stur            x3, [fp, #-0x40]
    // 0x14ee934: StoreField: r3->field_7 = r0
    //     0x14ee934: stur            w0, [x3, #7]
    // 0x14ee938: r1 = Null
    //     0x14ee938: mov             x1, NULL
    // 0x14ee93c: r2 = 6
    //     0x14ee93c: movz            x2, #0x6
    // 0x14ee940: r0 = AllocateArray()
    //     0x14ee940: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ee944: mov             x2, x0
    // 0x14ee948: ldur            x0, [fp, #-0x38]
    // 0x14ee94c: stur            x2, [fp, #-0x50]
    // 0x14ee950: StoreField: r2->field_f = r0
    //     0x14ee950: stur            w0, [x2, #0xf]
    // 0x14ee954: ldur            x1, [fp, #-0x48]
    // 0x14ee958: StoreField: r2->field_13 = r1
    //     0x14ee958: stur            w1, [x2, #0x13]
    // 0x14ee95c: ldur            x1, [fp, #-0x40]
    // 0x14ee960: ArrayStore: r2[0] = r1  ; List_4
    //     0x14ee960: stur            w1, [x2, #0x17]
    // 0x14ee964: r1 = <TextInputFormatter>
    //     0x14ee964: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14ee968: ldr             x1, [x1, #0x7b0]
    // 0x14ee96c: r0 = AllocateGrowableArray()
    //     0x14ee96c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ee970: mov             x2, x0
    // 0x14ee974: ldur            x0, [fp, #-0x50]
    // 0x14ee978: stur            x2, [fp, #-0x40]
    // 0x14ee97c: StoreField: r2->field_f = r0
    //     0x14ee97c: stur            w0, [x2, #0xf]
    // 0x14ee980: r0 = 6
    //     0x14ee980: movz            x0, #0x6
    // 0x14ee984: StoreField: r2->field_b = r0
    //     0x14ee984: stur            w0, [x2, #0xb]
    // 0x14ee988: ldur            x0, [fp, #-8]
    // 0x14ee98c: LoadField: r1 = r0->field_13
    //     0x14ee98c: ldur            w1, [x0, #0x13]
    // 0x14ee990: DecompressPointer r1
    //     0x14ee990: add             x1, x1, HEAP, lsl #32
    // 0x14ee994: r0 = of()
    //     0x14ee994: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ee998: LoadField: r1 = r0->field_87
    //     0x14ee998: ldur            w1, [x0, #0x87]
    // 0x14ee99c: DecompressPointer r1
    //     0x14ee99c: add             x1, x1, HEAP, lsl #32
    // 0x14ee9a0: LoadField: r0 = r1->field_2b
    //     0x14ee9a0: ldur            w0, [x1, #0x2b]
    // 0x14ee9a4: DecompressPointer r0
    //     0x14ee9a4: add             x0, x0, HEAP, lsl #32
    // 0x14ee9a8: ldur            x2, [fp, #-8]
    // 0x14ee9ac: stur            x0, [fp, #-0x48]
    // 0x14ee9b0: LoadField: r1 = r2->field_13
    //     0x14ee9b0: ldur            w1, [x2, #0x13]
    // 0x14ee9b4: DecompressPointer r1
    //     0x14ee9b4: add             x1, x1, HEAP, lsl #32
    // 0x14ee9b8: r0 = of()
    //     0x14ee9b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ee9bc: LoadField: r1 = r0->field_5b
    //     0x14ee9bc: ldur            w1, [x0, #0x5b]
    // 0x14ee9c0: DecompressPointer r1
    //     0x14ee9c0: add             x1, x1, HEAP, lsl #32
    // 0x14ee9c4: r16 = 12.000000
    //     0x14ee9c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14ee9c8: ldr             x16, [x16, #0x9e8]
    // 0x14ee9cc: stp             x16, x1, [SP]
    // 0x14ee9d0: ldur            x1, [fp, #-0x48]
    // 0x14ee9d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14ee9d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14ee9d8: ldr             x4, [x4, #0x9b8]
    // 0x14ee9dc: r0 = copyWith()
    //     0x14ee9dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ee9e0: ldur            x2, [fp, #-8]
    // 0x14ee9e4: stur            x0, [fp, #-0x48]
    // 0x14ee9e8: LoadField: r1 = r2->field_13
    //     0x14ee9e8: ldur            w1, [x2, #0x13]
    // 0x14ee9ec: DecompressPointer r1
    //     0x14ee9ec: add             x1, x1, HEAP, lsl #32
    // 0x14ee9f0: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0x14ee9f0: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0x14ee9f4: ldur            x2, [fp, #-8]
    // 0x14ee9f8: stur            x0, [fp, #-0x50]
    // 0x14ee9fc: LoadField: r1 = r2->field_13
    //     0x14ee9fc: ldur            w1, [x2, #0x13]
    // 0x14eea00: DecompressPointer r1
    //     0x14eea00: add             x1, x1, HEAP, lsl #32
    // 0x14eea04: r0 = of()
    //     0x14eea04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14eea08: LoadField: r1 = r0->field_87
    //     0x14eea08: ldur            w1, [x0, #0x87]
    // 0x14eea0c: DecompressPointer r1
    //     0x14eea0c: add             x1, x1, HEAP, lsl #32
    // 0x14eea10: LoadField: r0 = r1->field_2b
    //     0x14eea10: ldur            w0, [x1, #0x2b]
    // 0x14eea14: DecompressPointer r0
    //     0x14eea14: add             x0, x0, HEAP, lsl #32
    // 0x14eea18: stur            x0, [fp, #-0x58]
    // 0x14eea1c: r1 = Instance_Color
    //     0x14eea1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14eea20: d0 = 0.400000
    //     0x14eea20: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14eea24: r0 = withOpacity()
    //     0x14eea24: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14eea28: r16 = 12.000000
    //     0x14eea28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14eea2c: ldr             x16, [x16, #0x9e8]
    // 0x14eea30: stp             x0, x16, [SP]
    // 0x14eea34: ldur            x1, [fp, #-0x58]
    // 0x14eea38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14eea38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14eea3c: ldr             x4, [x4, #0xaa0]
    // 0x14eea40: r0 = copyWith()
    //     0x14eea40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14eea44: ldur            x2, [fp, #-8]
    // 0x14eea48: stur            x0, [fp, #-0x58]
    // 0x14eea4c: LoadField: r1 = r2->field_f
    //     0x14eea4c: ldur            w1, [x2, #0xf]
    // 0x14eea50: DecompressPointer r1
    //     0x14eea50: add             x1, x1, HEAP, lsl #32
    // 0x14eea54: r0 = controller()
    //     0x14eea54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eea58: LoadField: r1 = r0->field_bb
    //     0x14eea58: ldur            w1, [x0, #0xbb]
    // 0x14eea5c: DecompressPointer r1
    //     0x14eea5c: add             x1, x1, HEAP, lsl #32
    // 0x14eea60: r0 = value()
    //     0x14eea60: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14eea64: tbnz            w0, #4, #0x14eeafc
    // 0x14eea68: ldur            x2, [fp, #-8]
    // 0x14eea6c: LoadField: r1 = r2->field_f
    //     0x14eea6c: ldur            w1, [x2, #0xf]
    // 0x14eea70: DecompressPointer r1
    //     0x14eea70: add             x1, x1, HEAP, lsl #32
    // 0x14eea74: r0 = controller()
    //     0x14eea74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eea78: LoadField: r1 = r0->field_b7
    //     0x14eea78: ldur            w1, [x0, #0xb7]
    // 0x14eea7c: DecompressPointer r1
    //     0x14eea7c: add             x1, x1, HEAP, lsl #32
    // 0x14eea80: r0 = value()
    //     0x14eea80: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14eea84: tbnz            w0, #4, #0x14eea94
    // 0x14eea88: r0 = Instance_IconData
    //     0x14eea88: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0x14eea8c: ldr             x0, [x0, #0x130]
    // 0x14eea90: b               #0x14eea9c
    // 0x14eea94: r0 = Instance_IconData
    //     0x14eea94: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0x14eea98: ldr             x0, [x0, #0x138]
    // 0x14eea9c: ldur            x2, [fp, #-8]
    // 0x14eeaa0: stur            x0, [fp, #-0x60]
    // 0x14eeaa4: LoadField: r1 = r2->field_f
    //     0x14eeaa4: ldur            w1, [x2, #0xf]
    // 0x14eeaa8: DecompressPointer r1
    //     0x14eeaa8: add             x1, x1, HEAP, lsl #32
    // 0x14eeaac: r0 = controller()
    //     0x14eeaac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eeab0: LoadField: r1 = r0->field_b7
    //     0x14eeab0: ldur            w1, [x0, #0xb7]
    // 0x14eeab4: DecompressPointer r1
    //     0x14eeab4: add             x1, x1, HEAP, lsl #32
    // 0x14eeab8: r0 = value()
    //     0x14eeab8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14eeabc: tbnz            w0, #4, #0x14eeacc
    // 0x14eeac0: r1 = Instance_Color
    //     0x14eeac0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14eeac4: ldr             x1, [x1, #0x858]
    // 0x14eeac8: b               #0x14eead4
    // 0x14eeacc: r1 = Instance_Color
    //     0x14eeacc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14eead0: ldr             x1, [x1, #0x50]
    // 0x14eead4: ldur            x0, [fp, #-0x60]
    // 0x14eead8: stur            x1, [fp, #-0x68]
    // 0x14eeadc: r0 = Icon()
    //     0x14eeadc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14eeae0: mov             x1, x0
    // 0x14eeae4: ldur            x0, [fp, #-0x60]
    // 0x14eeae8: StoreField: r1->field_b = r0
    //     0x14eeae8: stur            w0, [x1, #0xb]
    // 0x14eeaec: ldur            x0, [fp, #-0x68]
    // 0x14eeaf0: StoreField: r1->field_23 = r0
    //     0x14eeaf0: stur            w0, [x1, #0x23]
    // 0x14eeaf4: mov             x3, x1
    // 0x14eeaf8: b               #0x14eeb00
    // 0x14eeafc: r3 = Null
    //     0x14eeafc: mov             x3, NULL
    // 0x14eeb00: ldur            x2, [fp, #-8]
    // 0x14eeb04: ldur            x0, [fp, #-0x30]
    // 0x14eeb08: ldur            x1, [fp, #-0x18]
    // 0x14eeb0c: stur            x3, [fp, #-0x60]
    // 0x14eeb10: r0 = SvgPicture()
    //     0x14eeb10: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14eeb14: mov             x1, x0
    // 0x14eeb18: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0x14eeb18: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0x14eeb1c: ldr             x2, [x2, #0x140]
    // 0x14eeb20: stur            x0, [fp, #-0x68]
    // 0x14eeb24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14eeb24: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14eeb28: r0 = SvgPicture.asset()
    //     0x14eeb28: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14eeb2c: ldur            x2, [fp, #-8]
    // 0x14eeb30: LoadField: r1 = r2->field_13
    //     0x14eeb30: ldur            w1, [x2, #0x13]
    // 0x14eeb34: DecompressPointer r1
    //     0x14eeb34: add             x1, x1, HEAP, lsl #32
    // 0x14eeb38: r0 = of()
    //     0x14eeb38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14eeb3c: LoadField: r1 = r0->field_87
    //     0x14eeb3c: ldur            w1, [x0, #0x87]
    // 0x14eeb40: DecompressPointer r1
    //     0x14eeb40: add             x1, x1, HEAP, lsl #32
    // 0x14eeb44: LoadField: r0 = r1->field_2b
    //     0x14eeb44: ldur            w0, [x1, #0x2b]
    // 0x14eeb48: DecompressPointer r0
    //     0x14eeb48: add             x0, x0, HEAP, lsl #32
    // 0x14eeb4c: ldur            x2, [fp, #-8]
    // 0x14eeb50: stur            x0, [fp, #-0x70]
    // 0x14eeb54: LoadField: r1 = r2->field_13
    //     0x14eeb54: ldur            w1, [x2, #0x13]
    // 0x14eeb58: DecompressPointer r1
    //     0x14eeb58: add             x1, x1, HEAP, lsl #32
    // 0x14eeb5c: r0 = of()
    //     0x14eeb5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14eeb60: LoadField: r1 = r0->field_5b
    //     0x14eeb60: ldur            w1, [x0, #0x5b]
    // 0x14eeb64: DecompressPointer r1
    //     0x14eeb64: add             x1, x1, HEAP, lsl #32
    // 0x14eeb68: r16 = 12.000000
    //     0x14eeb68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14eeb6c: ldr             x16, [x16, #0x9e8]
    // 0x14eeb70: stp             x1, x16, [SP]
    // 0x14eeb74: ldur            x1, [fp, #-0x70]
    // 0x14eeb78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14eeb78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14eeb7c: ldr             x4, [x4, #0xaa0]
    // 0x14eeb80: r0 = copyWith()
    //     0x14eeb80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14eeb84: stur            x0, [fp, #-0x70]
    // 0x14eeb88: r0 = Text()
    //     0x14eeb88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14eeb8c: mov             x3, x0
    // 0x14eeb90: r0 = "+91 "
    //     0x14eeb90: add             x0, PP, #0x37, lsl #12  ; [pp+0x37148] "+91 "
    //     0x14eeb94: ldr             x0, [x0, #0x148]
    // 0x14eeb98: stur            x3, [fp, #-0x78]
    // 0x14eeb9c: StoreField: r3->field_b = r0
    //     0x14eeb9c: stur            w0, [x3, #0xb]
    // 0x14eeba0: ldur            x0, [fp, #-0x70]
    // 0x14eeba4: StoreField: r3->field_13 = r0
    //     0x14eeba4: stur            w0, [x3, #0x13]
    // 0x14eeba8: r1 = Null
    //     0x14eeba8: mov             x1, NULL
    // 0x14eebac: r2 = 4
    //     0x14eebac: movz            x2, #0x4
    // 0x14eebb0: r0 = AllocateArray()
    //     0x14eebb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14eebb4: mov             x2, x0
    // 0x14eebb8: ldur            x0, [fp, #-0x68]
    // 0x14eebbc: stur            x2, [fp, #-0x70]
    // 0x14eebc0: StoreField: r2->field_f = r0
    //     0x14eebc0: stur            w0, [x2, #0xf]
    // 0x14eebc4: ldur            x0, [fp, #-0x78]
    // 0x14eebc8: StoreField: r2->field_13 = r0
    //     0x14eebc8: stur            w0, [x2, #0x13]
    // 0x14eebcc: r1 = <Widget>
    //     0x14eebcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14eebd0: r0 = AllocateGrowableArray()
    //     0x14eebd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14eebd4: mov             x1, x0
    // 0x14eebd8: ldur            x0, [fp, #-0x70]
    // 0x14eebdc: stur            x1, [fp, #-0x68]
    // 0x14eebe0: StoreField: r1->field_f = r0
    //     0x14eebe0: stur            w0, [x1, #0xf]
    // 0x14eebe4: r2 = 4
    //     0x14eebe4: movz            x2, #0x4
    // 0x14eebe8: StoreField: r1->field_b = r2
    //     0x14eebe8: stur            w2, [x1, #0xb]
    // 0x14eebec: r0 = Row()
    //     0x14eebec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14eebf0: mov             x1, x0
    // 0x14eebf4: r0 = Instance_Axis
    //     0x14eebf4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14eebf8: stur            x1, [fp, #-0x70]
    // 0x14eebfc: StoreField: r1->field_f = r0
    //     0x14eebfc: stur            w0, [x1, #0xf]
    // 0x14eec00: r0 = Instance_MainAxisAlignment
    //     0x14eec00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14eec04: ldr             x0, [x0, #0xa08]
    // 0x14eec08: StoreField: r1->field_13 = r0
    //     0x14eec08: stur            w0, [x1, #0x13]
    // 0x14eec0c: r2 = Instance_MainAxisSize
    //     0x14eec0c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14eec10: ldr             x2, [x2, #0xdd0]
    // 0x14eec14: ArrayStore: r1[0] = r2  ; List_4
    //     0x14eec14: stur            w2, [x1, #0x17]
    // 0x14eec18: r2 = Instance_CrossAxisAlignment
    //     0x14eec18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14eec1c: ldr             x2, [x2, #0xa18]
    // 0x14eec20: StoreField: r1->field_1b = r2
    //     0x14eec20: stur            w2, [x1, #0x1b]
    // 0x14eec24: r2 = Instance_VerticalDirection
    //     0x14eec24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14eec28: ldr             x2, [x2, #0xa20]
    // 0x14eec2c: StoreField: r1->field_23 = r2
    //     0x14eec2c: stur            w2, [x1, #0x23]
    // 0x14eec30: r3 = Instance_Clip
    //     0x14eec30: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14eec34: ldr             x3, [x3, #0x38]
    // 0x14eec38: StoreField: r1->field_2b = r3
    //     0x14eec38: stur            w3, [x1, #0x2b]
    // 0x14eec3c: StoreField: r1->field_2f = rZR
    //     0x14eec3c: stur            xzr, [x1, #0x2f]
    // 0x14eec40: ldur            x4, [fp, #-0x68]
    // 0x14eec44: StoreField: r1->field_b = r4
    //     0x14eec44: stur            w4, [x1, #0xb]
    // 0x14eec48: r0 = Padding()
    //     0x14eec48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14eec4c: mov             x1, x0
    // 0x14eec50: r0 = Instance_EdgeInsets
    //     0x14eec50: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x14eec54: ldr             x0, [x0, #0xc40]
    // 0x14eec58: stur            x1, [fp, #-0x68]
    // 0x14eec5c: StoreField: r1->field_f = r0
    //     0x14eec5c: stur            w0, [x1, #0xf]
    // 0x14eec60: ldur            x0, [fp, #-0x70]
    // 0x14eec64: StoreField: r1->field_b = r0
    //     0x14eec64: stur            w0, [x1, #0xb]
    // 0x14eec68: r0 = Align()
    //     0x14eec68: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14eec6c: mov             x1, x0
    // 0x14eec70: r0 = Instance_Alignment
    //     0x14eec70: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14eec74: ldr             x0, [x0, #0xb10]
    // 0x14eec78: stur            x1, [fp, #-0x70]
    // 0x14eec7c: StoreField: r1->field_f = r0
    //     0x14eec7c: stur            w0, [x1, #0xf]
    // 0x14eec80: r0 = 1.000000
    //     0x14eec80: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14eec84: StoreField: r1->field_13 = r0
    //     0x14eec84: stur            w0, [x1, #0x13]
    // 0x14eec88: ArrayStore: r1[0] = r0  ; List_4
    //     0x14eec88: stur            w0, [x1, #0x17]
    // 0x14eec8c: ldur            x0, [fp, #-0x68]
    // 0x14eec90: StoreField: r1->field_b = r0
    //     0x14eec90: stur            w0, [x1, #0xb]
    // 0x14eec94: r0 = Padding()
    //     0x14eec94: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14eec98: mov             x1, x0
    // 0x14eec9c: r0 = Instance_EdgeInsets
    //     0x14eec9c: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0x14eeca0: ldr             x0, [x0, #0xe0]
    // 0x14eeca4: StoreField: r1->field_f = r0
    //     0x14eeca4: stur            w0, [x1, #0xf]
    // 0x14eeca8: ldur            x0, [fp, #-0x70]
    // 0x14eecac: StoreField: r1->field_b = r0
    //     0x14eecac: stur            w0, [x1, #0xb]
    // 0x14eecb0: r16 = Instance_EdgeInsets
    //     0x14eecb0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x14eecb4: ldr             x16, [x16, #0xa78]
    // 0x14eecb8: r30 = "Enter Whatsapp no."
    //     0x14eecb8: add             lr, PP, #0x37, lsl #12  ; [pp+0x37150] "Enter Whatsapp no."
    //     0x14eecbc: ldr             lr, [lr, #0x150]
    // 0x14eecc0: stp             lr, x16, [SP, #0x18]
    // 0x14eecc4: ldur            x16, [fp, #-0x58]
    // 0x14eecc8: ldur            lr, [fp, #-0x60]
    // 0x14eeccc: stp             lr, x16, [SP, #8]
    // 0x14eecd0: str             x1, [SP]
    // 0x14eecd4: ldur            x1, [fp, #-0x50]
    // 0x14eecd8: r4 = const [0, 0x6, 0x5, 0x1, contentPadding, 0x1, hintStyle, 0x3, hintText, 0x2, prefixIcon, 0x5, suffixIcon, 0x4, null]
    //     0x14eecd8: add             x4, PP, #0x40, lsl #12  ; [pp+0x402d0] List(15) [0, 0x6, 0x5, 0x1, "contentPadding", 0x1, "hintStyle", 0x3, "hintText", 0x2, "prefixIcon", 0x5, "suffixIcon", 0x4, Null]
    //     0x14eecdc: ldr             x4, [x4, #0x2d0]
    // 0x14eece0: r0 = copyWith()
    //     0x14eece0: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x14eece4: ldur            x2, [fp, #-0x20]
    // 0x14eece8: r1 = Function '_validatePhoneNumber@1594053671':.
    //     0x14eece8: add             x1, PP, #0x40, lsl #12  ; [pp+0x402d8] AnonymousClosure: (0x14ef4c8), in [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validatePhoneNumber (0x1404d40)
    //     0x14eecec: ldr             x1, [x1, #0x2d8]
    // 0x14eecf0: stur            x0, [fp, #-0x20]
    // 0x14eecf4: r0 = AllocateClosure()
    //     0x14eecf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14eecf8: ldur            x2, [fp, #-8]
    // 0x14eecfc: r1 = Function '<anonymous closure>':.
    //     0x14eecfc: add             x1, PP, #0x40, lsl #12  ; [pp+0x402e0] AnonymousClosure: (0x14ef450), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::_getGlassThemeLoginForm (0x14ee590)
    //     0x14eed00: ldr             x1, [x1, #0x2e0]
    // 0x14eed04: stur            x0, [fp, #-0x50]
    // 0x14eed08: r0 = AllocateClosure()
    //     0x14eed08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14eed0c: ldur            x2, [fp, #-8]
    // 0x14eed10: r1 = Function '<anonymous closure>':.
    //     0x14eed10: add             x1, PP, #0x40, lsl #12  ; [pp+0x402e8] AnonymousClosure: (0x14ef3b0), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::_getGlassThemeLoginForm (0x14ee590)
    //     0x14eed14: ldr             x1, [x1, #0x2e8]
    // 0x14eed18: stur            x0, [fp, #-0x58]
    // 0x14eed1c: r0 = AllocateClosure()
    //     0x14eed1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14eed20: r1 = <String>
    //     0x14eed20: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14eed24: stur            x0, [fp, #-0x60]
    // 0x14eed28: r0 = TextFormField()
    //     0x14eed28: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x14eed2c: stur            x0, [fp, #-0x68]
    // 0x14eed30: ldur            x16, [fp, #-0x50]
    // 0x14eed34: r30 = true
    //     0x14eed34: add             lr, NULL, #0x20  ; true
    // 0x14eed38: stp             lr, x16, [SP, #0x40]
    // 0x14eed3c: r16 = Instance_AutovalidateMode
    //     0x14eed3c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14eed40: ldr             x16, [x16, #0x7e8]
    // 0x14eed44: r30 = false
    //     0x14eed44: add             lr, NULL, #0x30  ; false
    // 0x14eed48: stp             lr, x16, [SP, #0x30]
    // 0x14eed4c: ldur            x16, [fp, #-0x58]
    // 0x14eed50: ldur            lr, [fp, #-0x40]
    // 0x14eed54: stp             lr, x16, [SP, #0x20]
    // 0x14eed58: r16 = Instance_TextInputType
    //     0x14eed58: add             x16, PP, #0x37, lsl #12  ; [pp+0x37178] Obj!TextInputType@d55be1
    //     0x14eed5c: ldr             x16, [x16, #0x178]
    // 0x14eed60: r30 = 2
    //     0x14eed60: movz            lr, #0x2
    // 0x14eed64: stp             lr, x16, [SP, #0x10]
    // 0x14eed68: ldur            x16, [fp, #-0x60]
    // 0x14eed6c: ldur            lr, [fp, #-0x48]
    // 0x14eed70: stp             lr, x16, [SP]
    // 0x14eed74: mov             x1, x0
    // 0x14eed78: ldur            x2, [fp, #-0x20]
    // 0x14eed7c: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x3, autovalidateMode, 0x4, enableSuggestions, 0x5, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0x9, onChanged, 0xa, onFieldSubmitted, 0x6, style, 0xb, validator, 0x2, null]
    //     0x14eed7c: add             x4, PP, #0x40, lsl #12  ; [pp+0x402f0] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x3, "autovalidateMode", 0x4, "enableSuggestions", 0x5, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0x9, "onChanged", 0xa, "onFieldSubmitted", 0x6, "style", 0xb, "validator", 0x2, Null]
    //     0x14eed80: ldr             x4, [x4, #0x2f0]
    // 0x14eed84: r0 = TextFormField()
    //     0x14eed84: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14eed88: r0 = Form()
    //     0x14eed88: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14eed8c: mov             x1, x0
    // 0x14eed90: ldur            x0, [fp, #-0x68]
    // 0x14eed94: stur            x1, [fp, #-0x20]
    // 0x14eed98: StoreField: r1->field_b = r0
    //     0x14eed98: stur            w0, [x1, #0xb]
    // 0x14eed9c: r0 = Instance_AutovalidateMode
    //     0x14eed9c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14eeda0: ldr             x0, [x0, #0x800]
    // 0x14eeda4: StoreField: r1->field_23 = r0
    //     0x14eeda4: stur            w0, [x1, #0x23]
    // 0x14eeda8: ldur            x0, [fp, #-0x18]
    // 0x14eedac: StoreField: r1->field_7 = r0
    //     0x14eedac: stur            w0, [x1, #7]
    // 0x14eedb0: r0 = Padding()
    //     0x14eedb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14eedb4: mov             x1, x0
    // 0x14eedb8: r0 = Instance_EdgeInsets
    //     0x14eedb8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14eedbc: ldr             x0, [x0, #0x1f0]
    // 0x14eedc0: stur            x1, [fp, #-0x18]
    // 0x14eedc4: StoreField: r1->field_f = r0
    //     0x14eedc4: stur            w0, [x1, #0xf]
    // 0x14eedc8: ldur            x0, [fp, #-0x20]
    // 0x14eedcc: StoreField: r1->field_b = r0
    //     0x14eedcc: stur            w0, [x1, #0xb]
    // 0x14eedd0: r0 = Visibility()
    //     0x14eedd0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14eedd4: mov             x2, x0
    // 0x14eedd8: ldur            x0, [fp, #-0x18]
    // 0x14eeddc: stur            x2, [fp, #-0x20]
    // 0x14eede0: StoreField: r2->field_b = r0
    //     0x14eede0: stur            w0, [x2, #0xb]
    // 0x14eede4: r0 = Instance_SizedBox
    //     0x14eede4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14eede8: StoreField: r2->field_f = r0
    //     0x14eede8: stur            w0, [x2, #0xf]
    // 0x14eedec: ldur            x1, [fp, #-0x30]
    // 0x14eedf0: StoreField: r2->field_13 = r1
    //     0x14eedf0: stur            w1, [x2, #0x13]
    // 0x14eedf4: r3 = false
    //     0x14eedf4: add             x3, NULL, #0x30  ; false
    // 0x14eedf8: ArrayStore: r2[0] = r3  ; List_4
    //     0x14eedf8: stur            w3, [x2, #0x17]
    // 0x14eedfc: StoreField: r2->field_1b = r3
    //     0x14eedfc: stur            w3, [x2, #0x1b]
    // 0x14eee00: StoreField: r2->field_1f = r3
    //     0x14eee00: stur            w3, [x2, #0x1f]
    // 0x14eee04: StoreField: r2->field_23 = r3
    //     0x14eee04: stur            w3, [x2, #0x23]
    // 0x14eee08: StoreField: r2->field_27 = r3
    //     0x14eee08: stur            w3, [x2, #0x27]
    // 0x14eee0c: StoreField: r2->field_2b = r3
    //     0x14eee0c: stur            w3, [x2, #0x2b]
    // 0x14eee10: ldur            x4, [fp, #-8]
    // 0x14eee14: LoadField: r1 = r4->field_f
    //     0x14eee14: ldur            w1, [x4, #0xf]
    // 0x14eee18: DecompressPointer r1
    //     0x14eee18: add             x1, x1, HEAP, lsl #32
    // 0x14eee1c: r0 = controller()
    //     0x14eee1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eee20: LoadField: r1 = r0->field_8b
    //     0x14eee20: ldur            w1, [x0, #0x8b]
    // 0x14eee24: DecompressPointer r1
    //     0x14eee24: add             x1, x1, HEAP, lsl #32
    // 0x14eee28: r0 = value()
    //     0x14eee28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14eee2c: cmp             w0, NULL
    // 0x14eee30: b.ne            #0x14eee3c
    // 0x14eee34: r1 = true
    //     0x14eee34: add             x1, NULL, #0x20  ; true
    // 0x14eee38: b               #0x14eee40
    // 0x14eee3c: mov             x1, x0
    // 0x14eee40: ldur            x2, [fp, #-8]
    // 0x14eee44: ldur            x0, [fp, #-0x38]
    // 0x14eee48: stur            x1, [fp, #-0x18]
    // 0x14eee4c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14eee4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14eee50: ldr             x0, [x0, #0x1c80]
    //     0x14eee54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14eee58: cmp             w0, w16
    //     0x14eee5c: b.ne            #0x14eee68
    //     0x14eee60: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14eee64: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14eee68: r0 = GetNavigation.width()
    //     0x14eee68: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x14eee6c: stur            d0, [fp, #-0x80]
    // 0x14eee70: r16 = "[0-9]"
    //     0x14eee70: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0x14eee74: ldr             x16, [x16, #0x128]
    // 0x14eee78: stp             x16, NULL, [SP, #0x20]
    // 0x14eee7c: r16 = false
    //     0x14eee7c: add             x16, NULL, #0x30  ; false
    // 0x14eee80: r30 = true
    //     0x14eee80: add             lr, NULL, #0x20  ; true
    // 0x14eee84: stp             lr, x16, [SP, #0x10]
    // 0x14eee88: r16 = false
    //     0x14eee88: add             x16, NULL, #0x30  ; false
    // 0x14eee8c: r30 = false
    //     0x14eee8c: add             lr, NULL, #0x30  ; false
    // 0x14eee90: stp             lr, x16, [SP]
    // 0x14eee94: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x14eee94: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x14eee98: r0 = _RegExp()
    //     0x14eee98: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x14eee9c: stur            x0, [fp, #-0x30]
    // 0x14eeea0: r0 = FilteringTextInputFormatter()
    //     0x14eeea0: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0x14eeea4: mov             x3, x0
    // 0x14eeea8: ldur            x0, [fp, #-0x30]
    // 0x14eeeac: stur            x3, [fp, #-0x40]
    // 0x14eeeb0: StoreField: r3->field_b = r0
    //     0x14eeeb0: stur            w0, [x3, #0xb]
    // 0x14eeeb4: r0 = true
    //     0x14eeeb4: add             x0, NULL, #0x20  ; true
    // 0x14eeeb8: StoreField: r3->field_7 = r0
    //     0x14eeeb8: stur            w0, [x3, #7]
    // 0x14eeebc: r1 = ""
    //     0x14eeebc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14eeec0: StoreField: r3->field_f = r1
    //     0x14eeec0: stur            w1, [x3, #0xf]
    // 0x14eeec4: r1 = Null
    //     0x14eeec4: mov             x1, NULL
    // 0x14eeec8: r2 = 4
    //     0x14eeec8: movz            x2, #0x4
    // 0x14eeecc: r0 = AllocateArray()
    //     0x14eeecc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14eeed0: mov             x2, x0
    // 0x14eeed4: ldur            x0, [fp, #-0x38]
    // 0x14eeed8: stur            x2, [fp, #-0x30]
    // 0x14eeedc: StoreField: r2->field_f = r0
    //     0x14eeedc: stur            w0, [x2, #0xf]
    // 0x14eeee0: ldur            x0, [fp, #-0x40]
    // 0x14eeee4: StoreField: r2->field_13 = r0
    //     0x14eeee4: stur            w0, [x2, #0x13]
    // 0x14eeee8: r1 = <TextInputFormatter>
    //     0x14eeee8: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14eeeec: ldr             x1, [x1, #0x7b0]
    // 0x14eeef0: r0 = AllocateGrowableArray()
    //     0x14eeef0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14eeef4: mov             x2, x0
    // 0x14eeef8: ldur            x0, [fp, #-0x30]
    // 0x14eeefc: stur            x2, [fp, #-0x38]
    // 0x14eef00: StoreField: r2->field_f = r0
    //     0x14eef00: stur            w0, [x2, #0xf]
    // 0x14eef04: r0 = 4
    //     0x14eef04: movz            x0, #0x4
    // 0x14eef08: StoreField: r2->field_b = r0
    //     0x14eef08: stur            w0, [x2, #0xb]
    // 0x14eef0c: ldur            x0, [fp, #-8]
    // 0x14eef10: LoadField: r1 = r0->field_13
    //     0x14eef10: ldur            w1, [x0, #0x13]
    // 0x14eef14: DecompressPointer r1
    //     0x14eef14: add             x1, x1, HEAP, lsl #32
    // 0x14eef18: r0 = of()
    //     0x14eef18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14eef1c: LoadField: r1 = r0->field_87
    //     0x14eef1c: ldur            w1, [x0, #0x87]
    // 0x14eef20: DecompressPointer r1
    //     0x14eef20: add             x1, x1, HEAP, lsl #32
    // 0x14eef24: LoadField: r0 = r1->field_27
    //     0x14eef24: ldur            w0, [x1, #0x27]
    // 0x14eef28: DecompressPointer r0
    //     0x14eef28: add             x0, x0, HEAP, lsl #32
    // 0x14eef2c: r16 = 16.000000
    //     0x14eef2c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14eef30: ldr             x16, [x16, #0x188]
    // 0x14eef34: r30 = Instance_Color
    //     0x14eef34: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14eef38: stp             lr, x16, [SP]
    // 0x14eef3c: mov             x1, x0
    // 0x14eef40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14eef40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14eef44: ldr             x4, [x4, #0xaa0]
    // 0x14eef48: r0 = copyWith()
    //     0x14eef48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14eef4c: ldur            x2, [fp, #-8]
    // 0x14eef50: stur            x0, [fp, #-0x30]
    // 0x14eef54: LoadField: r1 = r2->field_13
    //     0x14eef54: ldur            w1, [x2, #0x13]
    // 0x14eef58: DecompressPointer r1
    //     0x14eef58: add             x1, x1, HEAP, lsl #32
    // 0x14eef5c: r0 = of()
    //     0x14eef5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14eef60: LoadField: r1 = r0->field_5b
    //     0x14eef60: ldur            w1, [x0, #0x5b]
    // 0x14eef64: DecompressPointer r1
    //     0x14eef64: add             x1, x1, HEAP, lsl #32
    // 0x14eef68: r0 = LoadClassIdInstr(r1)
    //     0x14eef68: ldur            x0, [x1, #-1]
    //     0x14eef6c: ubfx            x0, x0, #0xc, #0x14
    // 0x14eef70: d0 = 0.300000
    //     0x14eef70: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14eef74: ldr             d0, [x17, #0x658]
    // 0x14eef78: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14eef78: sub             lr, x0, #0xffa
    //     0x14eef7c: ldr             lr, [x21, lr, lsl #3]
    //     0x14eef80: blr             lr
    // 0x14eef84: r1 = <Color>
    //     0x14eef84: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x14eef88: ldr             x1, [x1, #0xf80]
    // 0x14eef8c: stur            x0, [fp, #-0x40]
    // 0x14eef90: r0 = FixedColorBuilder()
    //     0x14eef90: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0x14eef94: mov             x1, x0
    // 0x14eef98: ldur            x0, [fp, #-0x40]
    // 0x14eef9c: stur            x1, [fp, #-0x48]
    // 0x14eefa0: StoreField: r1->field_b = r0
    //     0x14eefa0: stur            w0, [x1, #0xb]
    // 0x14eefa4: r0 = BoxLooseDecoration()
    //     0x14eefa4: bl              #0xb4d24c  ; AllocateBoxLooseDecorationStub -> BoxLooseDecoration (size=0x48)
    // 0x14eefa8: stur            x0, [fp, #-0x40]
    // 0x14eefac: r16 = Instance_FixedColorBuilder
    //     0x14eefac: add             x16, PP, #0x40, lsl #12  ; [pp+0x402f8] Obj!FixedColorBuilder@d53ab1
    //     0x14eefb0: ldr             x16, [x16, #0x2f8]
    // 0x14eefb4: str             x16, [SP]
    // 0x14eefb8: mov             x1, x0
    // 0x14eefbc: ldur            x2, [fp, #-0x48]
    // 0x14eefc0: ldur            x3, [fp, #-0x30]
    // 0x14eefc4: r4 = const [0, 0x4, 0x1, 0x3, bgColorBuilder, 0x3, null]
    //     0x14eefc4: add             x4, PP, #0x40, lsl #12  ; [pp+0x40300] List(7) [0, 0x4, 0x1, 0x3, "bgColorBuilder", 0x3, Null]
    //     0x14eefc8: ldr             x4, [x4, #0x300]
    // 0x14eefcc: r0 = BoxLooseDecoration()
    //     0x14eefcc: bl              #0xb4cebc  ; [package:pin_input_text_field/src/decoration/pin_decoration.dart] BoxLooseDecoration::BoxLooseDecoration
    // 0x14eefd0: ldur            x2, [fp, #-8]
    // 0x14eefd4: LoadField: r1 = r2->field_f
    //     0x14eefd4: ldur            w1, [x2, #0xf]
    // 0x14eefd8: DecompressPointer r1
    //     0x14eefd8: add             x1, x1, HEAP, lsl #32
    // 0x14eefdc: r0 = controller()
    //     0x14eefdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eefe0: LoadField: r1 = r0->field_7f
    //     0x14eefe0: ldur            w1, [x0, #0x7f]
    // 0x14eefe4: DecompressPointer r1
    //     0x14eefe4: add             x1, x1, HEAP, lsl #32
    // 0x14eefe8: r0 = value()
    //     0x14eefe8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14eefec: stur            x0, [fp, #-0x30]
    // 0x14eeff0: r0 = PinFieldAutoFill()
    //     0x14eeff0: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0x14eeff4: mov             x3, x0
    // 0x14eeff8: r0 = Instance_TextInputType
    //     0x14eeff8: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0x14eeffc: ldr             x0, [x0, #0x1a0]
    // 0x14ef000: stur            x3, [fp, #-0x48]
    // 0x14ef004: StoreField: r3->field_33 = r0
    //     0x14ef004: stur            w0, [x3, #0x33]
    // 0x14ef008: r0 = Instance_TextInputAction
    //     0x14ef008: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0x14ef00c: StoreField: r3->field_37 = r0
    //     0x14ef00c: stur            w0, [x3, #0x37]
    // 0x14ef010: ldur            x0, [fp, #-0x38]
    // 0x14ef014: StoreField: r3->field_47 = r0
    //     0x14ef014: stur            w0, [x3, #0x47]
    // 0x14ef018: r0 = true
    //     0x14ef018: add             x0, NULL, #0x20  ; true
    // 0x14ef01c: StoreField: r3->field_3b = r0
    //     0x14ef01c: stur            w0, [x3, #0x3b]
    // 0x14ef020: StoreField: r3->field_3f = r0
    //     0x14ef020: stur            w0, [x3, #0x3f]
    // 0x14ef024: ldur            x1, [fp, #-0x40]
    // 0x14ef028: StoreField: r3->field_27 = r1
    //     0x14ef028: stur            w1, [x3, #0x27]
    // 0x14ef02c: ldur            x2, [fp, #-8]
    // 0x14ef030: r1 = Function '<anonymous closure>':.
    //     0x14ef030: add             x1, PP, #0x40, lsl #12  ; [pp+0x40308] AnonymousClosure: (0x1404280), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x14ef034: ldr             x1, [x1, #0x308]
    // 0x14ef038: r0 = AllocateClosure()
    //     0x14ef038: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef03c: mov             x1, x0
    // 0x14ef040: ldur            x0, [fp, #-0x48]
    // 0x14ef044: StoreField: r0->field_1f = r1
    //     0x14ef044: stur            w1, [x0, #0x1f]
    // 0x14ef048: ldur            x2, [fp, #-8]
    // 0x14ef04c: r1 = Function '<anonymous closure>':.
    //     0x14ef04c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40310] AnonymousClosure: (0x14040e0), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x14ef050: ldr             x1, [x1, #0x310]
    // 0x14ef054: r0 = AllocateClosure()
    //     0x14ef054: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef058: mov             x1, x0
    // 0x14ef05c: ldur            x0, [fp, #-0x48]
    // 0x14ef060: StoreField: r0->field_23 = r1
    //     0x14ef060: stur            w1, [x0, #0x23]
    // 0x14ef064: ldur            x1, [fp, #-0x30]
    // 0x14ef068: StoreField: r0->field_1b = r1
    //     0x14ef068: stur            w1, [x0, #0x1b]
    // 0x14ef06c: r1 = false
    //     0x14ef06c: add             x1, NULL, #0x30  ; false
    // 0x14ef070: StoreField: r0->field_13 = r1
    //     0x14ef070: stur            w1, [x0, #0x13]
    // 0x14ef074: r2 = 4
    //     0x14ef074: movz            x2, #0x4
    // 0x14ef078: StoreField: r0->field_b = r2
    //     0x14ef078: stur            x2, [x0, #0xb]
    // 0x14ef07c: ldur            d0, [fp, #-0x80]
    // 0x14ef080: r2 = inline_Allocate_Double()
    //     0x14ef080: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x14ef084: add             x2, x2, #0x10
    //     0x14ef088: cmp             x3, x2
    //     0x14ef08c: b.ls            #0x14ef394
    //     0x14ef090: str             x2, [THR, #0x50]  ; THR::top
    //     0x14ef094: sub             x2, x2, #0xf
    //     0x14ef098: movz            x3, #0xe15c
    //     0x14ef09c: movk            x3, #0x3, lsl #16
    //     0x14ef0a0: stur            x3, [x2, #-1]
    // 0x14ef0a4: StoreField: r2->field_7 = d0
    //     0x14ef0a4: stur            d0, [x2, #7]
    // 0x14ef0a8: stur            x2, [fp, #-0x30]
    // 0x14ef0ac: r0 = Container()
    //     0x14ef0ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ef0b0: stur            x0, [fp, #-0x38]
    // 0x14ef0b4: r16 = Instance_EdgeInsets
    //     0x14ef0b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x14ef0b8: ldr             x16, [x16, #0xe68]
    // 0x14ef0bc: ldur            lr, [fp, #-0x30]
    // 0x14ef0c0: stp             lr, x16, [SP, #8]
    // 0x14ef0c4: ldur            x16, [fp, #-0x48]
    // 0x14ef0c8: str             x16, [SP]
    // 0x14ef0cc: mov             x1, x0
    // 0x14ef0d0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0x14ef0d0: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0x14ef0d4: ldr             x4, [x4, #0x1b8]
    // 0x14ef0d8: r0 = Container()
    //     0x14ef0d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ef0dc: r0 = Visibility()
    //     0x14ef0dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14ef0e0: mov             x2, x0
    // 0x14ef0e4: ldur            x0, [fp, #-0x38]
    // 0x14ef0e8: stur            x2, [fp, #-0x30]
    // 0x14ef0ec: StoreField: r2->field_b = r0
    //     0x14ef0ec: stur            w0, [x2, #0xb]
    // 0x14ef0f0: r0 = Instance_SizedBox
    //     0x14ef0f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14ef0f4: StoreField: r2->field_f = r0
    //     0x14ef0f4: stur            w0, [x2, #0xf]
    // 0x14ef0f8: ldur            x1, [fp, #-0x18]
    // 0x14ef0fc: StoreField: r2->field_13 = r1
    //     0x14ef0fc: stur            w1, [x2, #0x13]
    // 0x14ef100: r3 = false
    //     0x14ef100: add             x3, NULL, #0x30  ; false
    // 0x14ef104: ArrayStore: r2[0] = r3  ; List_4
    //     0x14ef104: stur            w3, [x2, #0x17]
    // 0x14ef108: StoreField: r2->field_1b = r3
    //     0x14ef108: stur            w3, [x2, #0x1b]
    // 0x14ef10c: StoreField: r2->field_1f = r3
    //     0x14ef10c: stur            w3, [x2, #0x1f]
    // 0x14ef110: StoreField: r2->field_23 = r3
    //     0x14ef110: stur            w3, [x2, #0x23]
    // 0x14ef114: StoreField: r2->field_27 = r3
    //     0x14ef114: stur            w3, [x2, #0x27]
    // 0x14ef118: StoreField: r2->field_2b = r3
    //     0x14ef118: stur            w3, [x2, #0x2b]
    // 0x14ef11c: ldur            x4, [fp, #-8]
    // 0x14ef120: LoadField: r1 = r4->field_f
    //     0x14ef120: ldur            w1, [x4, #0xf]
    // 0x14ef124: DecompressPointer r1
    //     0x14ef124: add             x1, x1, HEAP, lsl #32
    // 0x14ef128: r0 = controller()
    //     0x14ef128: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef12c: LoadField: r1 = r0->field_8b
    //     0x14ef12c: ldur            w1, [x0, #0x8b]
    // 0x14ef130: DecompressPointer r1
    //     0x14ef130: add             x1, x1, HEAP, lsl #32
    // 0x14ef134: r0 = value()
    //     0x14ef134: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ef138: cmp             w0, NULL
    // 0x14ef13c: b.ne            #0x14ef144
    // 0x14ef140: r0 = true
    //     0x14ef140: add             x0, NULL, #0x20  ; true
    // 0x14ef144: ldur            x2, [fp, #-8]
    // 0x14ef148: stur            x0, [fp, #-0x18]
    // 0x14ef14c: LoadField: r1 = r2->field_13
    //     0x14ef14c: ldur            w1, [x2, #0x13]
    // 0x14ef150: DecompressPointer r1
    //     0x14ef150: add             x1, x1, HEAP, lsl #32
    // 0x14ef154: r0 = of()
    //     0x14ef154: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ef158: LoadField: r1 = r0->field_87
    //     0x14ef158: ldur            w1, [x0, #0x87]
    // 0x14ef15c: DecompressPointer r1
    //     0x14ef15c: add             x1, x1, HEAP, lsl #32
    // 0x14ef160: LoadField: r0 = r1->field_2b
    //     0x14ef160: ldur            w0, [x1, #0x2b]
    // 0x14ef164: DecompressPointer r0
    //     0x14ef164: add             x0, x0, HEAP, lsl #32
    // 0x14ef168: ldur            x2, [fp, #-8]
    // 0x14ef16c: stur            x0, [fp, #-0x38]
    // 0x14ef170: LoadField: r1 = r2->field_f
    //     0x14ef170: ldur            w1, [x2, #0xf]
    // 0x14ef174: DecompressPointer r1
    //     0x14ef174: add             x1, x1, HEAP, lsl #32
    // 0x14ef178: r0 = controller()
    //     0x14ef178: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef17c: LoadField: r1 = r0->field_97
    //     0x14ef17c: ldur            w1, [x0, #0x97]
    // 0x14ef180: DecompressPointer r1
    //     0x14ef180: add             x1, x1, HEAP, lsl #32
    // 0x14ef184: r0 = value()
    //     0x14ef184: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ef188: cmp             w0, NULL
    // 0x14ef18c: b.eq            #0x14ef19c
    // 0x14ef190: tbnz            w0, #4, #0x14ef19c
    // 0x14ef194: r1 = Instance_Color
    //     0x14ef194: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ef198: b               #0x14ef1ac
    // 0x14ef19c: r1 = Instance_Color
    //     0x14ef19c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ef1a0: d0 = 0.400000
    //     0x14ef1a0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14ef1a4: r0 = withOpacity()
    //     0x14ef1a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14ef1a8: mov             x1, x0
    // 0x14ef1ac: ldur            x5, [fp, #-0x10]
    // 0x14ef1b0: ldur            x4, [fp, #-0x28]
    // 0x14ef1b4: ldur            x3, [fp, #-0x20]
    // 0x14ef1b8: ldur            x2, [fp, #-0x30]
    // 0x14ef1bc: ldur            x0, [fp, #-0x18]
    // 0x14ef1c0: r16 = 14.000000
    //     0x14ef1c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14ef1c4: ldr             x16, [x16, #0x1d8]
    // 0x14ef1c8: stp             x16, x1, [SP]
    // 0x14ef1cc: ldur            x1, [fp, #-0x38]
    // 0x14ef1d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14ef1d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14ef1d4: ldr             x4, [x4, #0x9b8]
    // 0x14ef1d8: r0 = copyWith()
    //     0x14ef1d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ef1dc: stur            x0, [fp, #-0x38]
    // 0x14ef1e0: r0 = Text()
    //     0x14ef1e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ef1e4: mov             x1, x0
    // 0x14ef1e8: r0 = "Resend OTP"
    //     0x14ef1e8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40318] "Resend OTP"
    //     0x14ef1ec: ldr             x0, [x0, #0x318]
    // 0x14ef1f0: stur            x1, [fp, #-0x40]
    // 0x14ef1f4: StoreField: r1->field_b = r0
    //     0x14ef1f4: stur            w0, [x1, #0xb]
    // 0x14ef1f8: ldur            x0, [fp, #-0x38]
    // 0x14ef1fc: StoreField: r1->field_13 = r0
    //     0x14ef1fc: stur            w0, [x1, #0x13]
    // 0x14ef200: r0 = InkWell()
    //     0x14ef200: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14ef204: mov             x3, x0
    // 0x14ef208: ldur            x0, [fp, #-0x40]
    // 0x14ef20c: stur            x3, [fp, #-0x38]
    // 0x14ef210: StoreField: r3->field_b = r0
    //     0x14ef210: stur            w0, [x3, #0xb]
    // 0x14ef214: ldur            x2, [fp, #-8]
    // 0x14ef218: r1 = Function '<anonymous closure>':.
    //     0x14ef218: add             x1, PP, #0x40, lsl #12  ; [pp+0x40320] AnonymousClosure: (0x140313c), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x14ef21c: ldr             x1, [x1, #0x320]
    // 0x14ef220: r0 = AllocateClosure()
    //     0x14ef220: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ef224: mov             x1, x0
    // 0x14ef228: ldur            x0, [fp, #-0x38]
    // 0x14ef22c: StoreField: r0->field_f = r1
    //     0x14ef22c: stur            w1, [x0, #0xf]
    // 0x14ef230: r1 = true
    //     0x14ef230: add             x1, NULL, #0x20  ; true
    // 0x14ef234: StoreField: r0->field_43 = r1
    //     0x14ef234: stur            w1, [x0, #0x43]
    // 0x14ef238: r2 = Instance_BoxShape
    //     0x14ef238: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14ef23c: ldr             x2, [x2, #0x80]
    // 0x14ef240: StoreField: r0->field_47 = r2
    //     0x14ef240: stur            w2, [x0, #0x47]
    // 0x14ef244: StoreField: r0->field_6f = r1
    //     0x14ef244: stur            w1, [x0, #0x6f]
    // 0x14ef248: r2 = false
    //     0x14ef248: add             x2, NULL, #0x30  ; false
    // 0x14ef24c: StoreField: r0->field_73 = r2
    //     0x14ef24c: stur            w2, [x0, #0x73]
    // 0x14ef250: StoreField: r0->field_83 = r1
    //     0x14ef250: stur            w1, [x0, #0x83]
    // 0x14ef254: StoreField: r0->field_7b = r2
    //     0x14ef254: stur            w2, [x0, #0x7b]
    // 0x14ef258: r0 = Visibility()
    //     0x14ef258: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14ef25c: mov             x1, x0
    // 0x14ef260: ldur            x0, [fp, #-0x38]
    // 0x14ef264: stur            x1, [fp, #-8]
    // 0x14ef268: StoreField: r1->field_b = r0
    //     0x14ef268: stur            w0, [x1, #0xb]
    // 0x14ef26c: r0 = Instance_SizedBox
    //     0x14ef26c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14ef270: StoreField: r1->field_f = r0
    //     0x14ef270: stur            w0, [x1, #0xf]
    // 0x14ef274: ldur            x0, [fp, #-0x18]
    // 0x14ef278: StoreField: r1->field_13 = r0
    //     0x14ef278: stur            w0, [x1, #0x13]
    // 0x14ef27c: r0 = false
    //     0x14ef27c: add             x0, NULL, #0x30  ; false
    // 0x14ef280: ArrayStore: r1[0] = r0  ; List_4
    //     0x14ef280: stur            w0, [x1, #0x17]
    // 0x14ef284: StoreField: r1->field_1b = r0
    //     0x14ef284: stur            w0, [x1, #0x1b]
    // 0x14ef288: StoreField: r1->field_1f = r0
    //     0x14ef288: stur            w0, [x1, #0x1f]
    // 0x14ef28c: StoreField: r1->field_23 = r0
    //     0x14ef28c: stur            w0, [x1, #0x23]
    // 0x14ef290: StoreField: r1->field_27 = r0
    //     0x14ef290: stur            w0, [x1, #0x27]
    // 0x14ef294: StoreField: r1->field_2b = r0
    //     0x14ef294: stur            w0, [x1, #0x2b]
    // 0x14ef298: r0 = Padding()
    //     0x14ef298: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ef29c: mov             x3, x0
    // 0x14ef2a0: r0 = Instance_EdgeInsets
    //     0x14ef2a0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x14ef2a4: ldr             x0, [x0, #0xa78]
    // 0x14ef2a8: stur            x3, [fp, #-0x18]
    // 0x14ef2ac: StoreField: r3->field_f = r0
    //     0x14ef2ac: stur            w0, [x3, #0xf]
    // 0x14ef2b0: ldur            x0, [fp, #-8]
    // 0x14ef2b4: StoreField: r3->field_b = r0
    //     0x14ef2b4: stur            w0, [x3, #0xb]
    // 0x14ef2b8: r1 = Null
    //     0x14ef2b8: mov             x1, NULL
    // 0x14ef2bc: r2 = 14
    //     0x14ef2bc: movz            x2, #0xe
    // 0x14ef2c0: r0 = AllocateArray()
    //     0x14ef2c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ef2c4: mov             x2, x0
    // 0x14ef2c8: ldur            x0, [fp, #-0x10]
    // 0x14ef2cc: stur            x2, [fp, #-8]
    // 0x14ef2d0: StoreField: r2->field_f = r0
    //     0x14ef2d0: stur            w0, [x2, #0xf]
    // 0x14ef2d4: ldur            x0, [fp, #-0x28]
    // 0x14ef2d8: StoreField: r2->field_13 = r0
    //     0x14ef2d8: stur            w0, [x2, #0x13]
    // 0x14ef2dc: ldur            x0, [fp, #-0x20]
    // 0x14ef2e0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ef2e0: stur            w0, [x2, #0x17]
    // 0x14ef2e4: ldur            x0, [fp, #-0x30]
    // 0x14ef2e8: StoreField: r2->field_1b = r0
    //     0x14ef2e8: stur            w0, [x2, #0x1b]
    // 0x14ef2ec: r16 = Instance_SizedBox
    //     0x14ef2ec: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0x14ef2f0: ldr             x16, [x16, #0x328]
    // 0x14ef2f4: StoreField: r2->field_1f = r16
    //     0x14ef2f4: stur            w16, [x2, #0x1f]
    // 0x14ef2f8: ldur            x0, [fp, #-0x18]
    // 0x14ef2fc: StoreField: r2->field_23 = r0
    //     0x14ef2fc: stur            w0, [x2, #0x23]
    // 0x14ef300: r16 = Instance_SizedBox
    //     0x14ef300: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14ef304: ldr             x16, [x16, #0x9f0]
    // 0x14ef308: StoreField: r2->field_27 = r16
    //     0x14ef308: stur            w16, [x2, #0x27]
    // 0x14ef30c: r1 = <Widget>
    //     0x14ef30c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ef310: r0 = AllocateGrowableArray()
    //     0x14ef310: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ef314: mov             x1, x0
    // 0x14ef318: ldur            x0, [fp, #-8]
    // 0x14ef31c: stur            x1, [fp, #-0x10]
    // 0x14ef320: StoreField: r1->field_f = r0
    //     0x14ef320: stur            w0, [x1, #0xf]
    // 0x14ef324: r0 = 14
    //     0x14ef324: movz            x0, #0xe
    // 0x14ef328: StoreField: r1->field_b = r0
    //     0x14ef328: stur            w0, [x1, #0xb]
    // 0x14ef32c: r0 = Column()
    //     0x14ef32c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14ef330: r1 = Instance_Axis
    //     0x14ef330: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14ef334: StoreField: r0->field_f = r1
    //     0x14ef334: stur            w1, [x0, #0xf]
    // 0x14ef338: r1 = Instance_MainAxisAlignment
    //     0x14ef338: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ef33c: ldr             x1, [x1, #0xa08]
    // 0x14ef340: StoreField: r0->field_13 = r1
    //     0x14ef340: stur            w1, [x0, #0x13]
    // 0x14ef344: r1 = Instance_MainAxisSize
    //     0x14ef344: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ef348: ldr             x1, [x1, #0xa10]
    // 0x14ef34c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14ef34c: stur            w1, [x0, #0x17]
    // 0x14ef350: r1 = Instance_CrossAxisAlignment
    //     0x14ef350: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14ef354: ldr             x1, [x1, #0x890]
    // 0x14ef358: StoreField: r0->field_1b = r1
    //     0x14ef358: stur            w1, [x0, #0x1b]
    // 0x14ef35c: r1 = Instance_VerticalDirection
    //     0x14ef35c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ef360: ldr             x1, [x1, #0xa20]
    // 0x14ef364: StoreField: r0->field_23 = r1
    //     0x14ef364: stur            w1, [x0, #0x23]
    // 0x14ef368: r1 = Instance_Clip
    //     0x14ef368: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ef36c: ldr             x1, [x1, #0x38]
    // 0x14ef370: StoreField: r0->field_2b = r1
    //     0x14ef370: stur            w1, [x0, #0x2b]
    // 0x14ef374: StoreField: r0->field_2f = rZR
    //     0x14ef374: stur            xzr, [x0, #0x2f]
    // 0x14ef378: ldur            x1, [fp, #-0x10]
    // 0x14ef37c: StoreField: r0->field_b = r1
    //     0x14ef37c: stur            w1, [x0, #0xb]
    // 0x14ef380: LeaveFrame
    //     0x14ef380: mov             SP, fp
    //     0x14ef384: ldp             fp, lr, [SP], #0x10
    // 0x14ef388: ret
    //     0x14ef388: ret             
    // 0x14ef38c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef38c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef390: b               #0x14ee660
    // 0x14ef394: SaveReg d0
    //     0x14ef394: str             q0, [SP, #-0x10]!
    // 0x14ef398: stp             x0, x1, [SP, #-0x10]!
    // 0x14ef39c: r0 = AllocateDouble()
    //     0x14ef39c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14ef3a0: mov             x2, x0
    // 0x14ef3a4: ldp             x0, x1, [SP], #0x10
    // 0x14ef3a8: RestoreReg d0
    //     0x14ef3a8: ldr             q0, [SP], #0x10
    // 0x14ef3ac: b               #0x14ef0a4
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14ef3b0, size: 0xa0
    // 0x14ef3b0: EnterFrame
    //     0x14ef3b0: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef3b4: mov             fp, SP
    // 0x14ef3b8: AllocStack(0x8)
    //     0x14ef3b8: sub             SP, SP, #8
    // 0x14ef3bc: SetupParameters()
    //     0x14ef3bc: ldr             x0, [fp, #0x18]
    //     0x14ef3c0: ldur            w2, [x0, #0x17]
    //     0x14ef3c4: add             x2, x2, HEAP, lsl #32
    //     0x14ef3c8: stur            x2, [fp, #-8]
    // 0x14ef3cc: CheckStackOverflow
    //     0x14ef3cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef3d0: cmp             SP, x16
    //     0x14ef3d4: b.ls            #0x14ef448
    // 0x14ef3d8: LoadField: r1 = r2->field_f
    //     0x14ef3d8: ldur            w1, [x2, #0xf]
    // 0x14ef3dc: DecompressPointer r1
    //     0x14ef3dc: add             x1, x1, HEAP, lsl #32
    // 0x14ef3e0: ldr             x0, [fp, #0x10]
    // 0x14ef3e4: cmp             w0, NULL
    // 0x14ef3e8: b.ne            #0x14ef3f0
    // 0x14ef3ec: r0 = "0"
    //     0x14ef3ec: ldr             x0, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x14ef3f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x14ef3f0: stur            w0, [x1, #0x17]
    //     0x14ef3f4: ldurb           w16, [x1, #-1]
    //     0x14ef3f8: ldurb           w17, [x0, #-1]
    //     0x14ef3fc: and             x16, x17, x16, lsr #2
    //     0x14ef400: tst             x16, HEAP, lsr #32
    //     0x14ef404: b.eq            #0x14ef40c
    //     0x14ef408: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14ef40c: r0 = controller()
    //     0x14ef40c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ef410: LoadField: r1 = r0->field_bb
    //     0x14ef410: ldur            w1, [x0, #0xbb]
    // 0x14ef414: DecompressPointer r1
    //     0x14ef414: add             x1, x1, HEAP, lsl #32
    // 0x14ef418: r2 = true
    //     0x14ef418: add             x2, NULL, #0x20  ; true
    // 0x14ef41c: r0 = value=()
    //     0x14ef41c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14ef420: ldur            x0, [fp, #-8]
    // 0x14ef424: LoadField: r1 = r0->field_f
    //     0x14ef424: ldur            w1, [x0, #0xf]
    // 0x14ef428: DecompressPointer r1
    //     0x14ef428: add             x1, x1, HEAP, lsl #32
    // 0x14ef42c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x14ef42c: ldur            w2, [x1, #0x17]
    // 0x14ef430: DecompressPointer r2
    //     0x14ef430: add             x2, x2, HEAP, lsl #32
    // 0x14ef434: r0 = _validateInput()
    //     0x14ef434: bl              #0x14054b8  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validateInput
    // 0x14ef438: r0 = Null
    //     0x14ef438: mov             x0, NULL
    // 0x14ef43c: LeaveFrame
    //     0x14ef43c: mov             SP, fp
    //     0x14ef440: ldp             fp, lr, [SP], #0x10
    // 0x14ef444: ret
    //     0x14ef444: ret             
    // 0x14ef448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef448: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef44c: b               #0x14ef3d8
  }
  [closure] Future<void> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x14ef450, size: 0x78
    // 0x14ef450: EnterFrame
    //     0x14ef450: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef454: mov             fp, SP
    // 0x14ef458: AllocStack(0x18)
    //     0x14ef458: sub             SP, SP, #0x18
    // 0x14ef45c: SetupParameters(LoginView this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x14ef45c: stur            NULL, [fp, #-8]
    //     0x14ef460: movz            x0, #0
    //     0x14ef464: add             x1, fp, w0, sxtw #2
    //     0x14ef468: ldr             x1, [x1, #0x18]
    //     0x14ef46c: add             x2, fp, w0, sxtw #2
    //     0x14ef470: ldr             x2, [x2, #0x10]
    //     0x14ef474: stur            x2, [fp, #-0x18]
    //     0x14ef478: ldur            w3, [x1, #0x17]
    //     0x14ef47c: add             x3, x3, HEAP, lsl #32
    //     0x14ef480: stur            x3, [fp, #-0x10]
    // 0x14ef484: CheckStackOverflow
    //     0x14ef484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef488: cmp             SP, x16
    //     0x14ef48c: b.ls            #0x14ef4c0
    // 0x14ef490: InitAsync() -> Future<void?>
    //     0x14ef490: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14ef494: bl              #0x6326e0  ; InitAsyncStub
    // 0x14ef498: ldur            x0, [fp, #-0x18]
    // 0x14ef49c: LoadField: r1 = r0->field_7
    //     0x14ef49c: ldur            w1, [x0, #7]
    // 0x14ef4a0: cmp             w1, #0x14
    // 0x14ef4a4: b.ne            #0x14ef4b8
    // 0x14ef4a8: ldur            x0, [fp, #-0x10]
    // 0x14ef4ac: LoadField: r1 = r0->field_f
    //     0x14ef4ac: ldur            w1, [x0, #0xf]
    // 0x14ef4b0: DecompressPointer r1
    //     0x14ef4b0: add             x1, x1, HEAP, lsl #32
    // 0x14ef4b4: r0 = onPhoneSubmitted()
    //     0x14ef4b4: bl              #0x131dbcc  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onPhoneSubmitted
    // 0x14ef4b8: r0 = Null
    //     0x14ef4b8: mov             x0, NULL
    // 0x14ef4bc: r0 = ReturnAsyncNotFuture()
    //     0x14ef4bc: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14ef4c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef4c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef4c4: b               #0x14ef490
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0x14ef4c8, size: 0x3c
    // 0x14ef4c8: EnterFrame
    //     0x14ef4c8: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef4cc: mov             fp, SP
    // 0x14ef4d0: ldr             x0, [fp, #0x18]
    // 0x14ef4d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14ef4d4: ldur            w1, [x0, #0x17]
    // 0x14ef4d8: DecompressPointer r1
    //     0x14ef4d8: add             x1, x1, HEAP, lsl #32
    // 0x14ef4dc: CheckStackOverflow
    //     0x14ef4dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef4e0: cmp             SP, x16
    //     0x14ef4e4: b.ls            #0x14ef4fc
    // 0x14ef4e8: ldr             x2, [fp, #0x10]
    // 0x14ef4ec: r0 = _validatePhoneNumber()
    //     0x14ef4ec: bl              #0x1404d40  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validatePhoneNumber
    // 0x14ef4f0: LeaveFrame
    //     0x14ef4f0: mov             SP, fp
    //     0x14ef4f4: ldp             fp, lr, [SP], #0x10
    // 0x14ef4f8: ret
    //     0x14ef4f8: ret             
    // 0x14ef4fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef4fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef500: b               #0x14ef4e8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14ef504, size: 0x48
    // 0x14ef504: EnterFrame
    //     0x14ef504: stp             fp, lr, [SP, #-0x10]!
    //     0x14ef508: mov             fp, SP
    // 0x14ef50c: ldr             x0, [fp, #0x10]
    // 0x14ef510: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14ef510: ldur            w1, [x0, #0x17]
    // 0x14ef514: DecompressPointer r1
    //     0x14ef514: add             x1, x1, HEAP, lsl #32
    // 0x14ef518: CheckStackOverflow
    //     0x14ef518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ef51c: cmp             SP, x16
    //     0x14ef520: b.ls            #0x14ef544
    // 0x14ef524: LoadField: r0 = r1->field_f
    //     0x14ef524: ldur            w0, [x1, #0xf]
    // 0x14ef528: DecompressPointer r0
    //     0x14ef528: add             x0, x0, HEAP, lsl #32
    // 0x14ef52c: mov             x1, x0
    // 0x14ef530: r0 = onOtpSubmit()
    //     0x14ef530: bl              #0x14056a0  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onOtpSubmit
    // 0x14ef534: r0 = Null
    //     0x14ef534: mov             x0, NULL
    // 0x14ef538: LeaveFrame
    //     0x14ef538: mov             SP, fp
    //     0x14ef53c: ldp             fp, lr, [SP], #0x10
    // 0x14ef540: ret
    //     0x14ef540: ret             
    // 0x14ef544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ef544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ef548: b               #0x14ef524
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15d22bc, size: 0x420
    // 0x15d22bc: EnterFrame
    //     0x15d22bc: stp             fp, lr, [SP, #-0x10]!
    //     0x15d22c0: mov             fp, SP
    // 0x15d22c4: AllocStack(0x40)
    //     0x15d22c4: sub             SP, SP, #0x40
    // 0x15d22c8: SetupParameters()
    //     0x15d22c8: ldr             x0, [fp, #0x10]
    //     0x15d22cc: ldur            w2, [x0, #0x17]
    //     0x15d22d0: add             x2, x2, HEAP, lsl #32
    //     0x15d22d4: stur            x2, [fp, #-8]
    // 0x15d22d8: CheckStackOverflow
    //     0x15d22d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d22dc: cmp             SP, x16
    //     0x15d22e0: b.ls            #0x15d26d4
    // 0x15d22e4: LoadField: r1 = r2->field_f
    //     0x15d22e4: ldur            w1, [x2, #0xf]
    // 0x15d22e8: DecompressPointer r1
    //     0x15d22e8: add             x1, x1, HEAP, lsl #32
    // 0x15d22ec: r0 = controller()
    //     0x15d22ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d22f0: LoadField: r1 = r0->field_7b
    //     0x15d22f0: ldur            w1, [x0, #0x7b]
    // 0x15d22f4: DecompressPointer r1
    //     0x15d22f4: add             x1, x1, HEAP, lsl #32
    // 0x15d22f8: r0 = value()
    //     0x15d22f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d22fc: LoadField: r1 = r0->field_3f
    //     0x15d22fc: ldur            w1, [x0, #0x3f]
    // 0x15d2300: DecompressPointer r1
    //     0x15d2300: add             x1, x1, HEAP, lsl #32
    // 0x15d2304: cmp             w1, NULL
    // 0x15d2308: b.ne            #0x15d2314
    // 0x15d230c: r0 = Null
    //     0x15d230c: mov             x0, NULL
    // 0x15d2310: b               #0x15d231c
    // 0x15d2314: LoadField: r0 = r1->field_f
    //     0x15d2314: ldur            w0, [x1, #0xf]
    // 0x15d2318: DecompressPointer r0
    //     0x15d2318: add             x0, x0, HEAP, lsl #32
    // 0x15d231c: r1 = LoadClassIdInstr(r0)
    //     0x15d231c: ldur            x1, [x0, #-1]
    //     0x15d2320: ubfx            x1, x1, #0xc, #0x14
    // 0x15d2324: r16 = "image_text"
    //     0x15d2324: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15d2328: ldr             x16, [x16, #0xa88]
    // 0x15d232c: stp             x16, x0, [SP]
    // 0x15d2330: mov             x0, x1
    // 0x15d2334: mov             lr, x0
    // 0x15d2338: ldr             lr, [x21, lr, lsl #3]
    // 0x15d233c: blr             lr
    // 0x15d2340: tbnz            w0, #4, #0x15d234c
    // 0x15d2344: r2 = true
    //     0x15d2344: add             x2, NULL, #0x20  ; true
    // 0x15d2348: b               #0x15d23ac
    // 0x15d234c: ldur            x0, [fp, #-8]
    // 0x15d2350: LoadField: r1 = r0->field_f
    //     0x15d2350: ldur            w1, [x0, #0xf]
    // 0x15d2354: DecompressPointer r1
    //     0x15d2354: add             x1, x1, HEAP, lsl #32
    // 0x15d2358: r0 = controller()
    //     0x15d2358: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d235c: LoadField: r1 = r0->field_7b
    //     0x15d235c: ldur            w1, [x0, #0x7b]
    // 0x15d2360: DecompressPointer r1
    //     0x15d2360: add             x1, x1, HEAP, lsl #32
    // 0x15d2364: r0 = value()
    //     0x15d2364: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d2368: LoadField: r1 = r0->field_3f
    //     0x15d2368: ldur            w1, [x0, #0x3f]
    // 0x15d236c: DecompressPointer r1
    //     0x15d236c: add             x1, x1, HEAP, lsl #32
    // 0x15d2370: cmp             w1, NULL
    // 0x15d2374: b.ne            #0x15d2380
    // 0x15d2378: r0 = Null
    //     0x15d2378: mov             x0, NULL
    // 0x15d237c: b               #0x15d2388
    // 0x15d2380: LoadField: r0 = r1->field_f
    //     0x15d2380: ldur            w0, [x1, #0xf]
    // 0x15d2384: DecompressPointer r0
    //     0x15d2384: add             x0, x0, HEAP, lsl #32
    // 0x15d2388: r1 = LoadClassIdInstr(r0)
    //     0x15d2388: ldur            x1, [x0, #-1]
    //     0x15d238c: ubfx            x1, x1, #0xc, #0x14
    // 0x15d2390: r16 = "image"
    //     0x15d2390: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15d2394: stp             x16, x0, [SP]
    // 0x15d2398: mov             x0, x1
    // 0x15d239c: mov             lr, x0
    // 0x15d23a0: ldr             lr, [x21, lr, lsl #3]
    // 0x15d23a4: blr             lr
    // 0x15d23a8: mov             x2, x0
    // 0x15d23ac: ldur            x0, [fp, #-8]
    // 0x15d23b0: stur            x2, [fp, #-0x10]
    // 0x15d23b4: LoadField: r1 = r0->field_f
    //     0x15d23b4: ldur            w1, [x0, #0xf]
    // 0x15d23b8: DecompressPointer r1
    //     0x15d23b8: add             x1, x1, HEAP, lsl #32
    // 0x15d23bc: r0 = controller()
    //     0x15d23bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d23c0: LoadField: r1 = r0->field_7b
    //     0x15d23c0: ldur            w1, [x0, #0x7b]
    // 0x15d23c4: DecompressPointer r1
    //     0x15d23c4: add             x1, x1, HEAP, lsl #32
    // 0x15d23c8: r0 = value()
    //     0x15d23c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d23cc: LoadField: r1 = r0->field_27
    //     0x15d23cc: ldur            w1, [x0, #0x27]
    // 0x15d23d0: DecompressPointer r1
    //     0x15d23d0: add             x1, x1, HEAP, lsl #32
    // 0x15d23d4: cmp             w1, NULL
    // 0x15d23d8: b.ne            #0x15d23e4
    // 0x15d23dc: r2 = ""
    //     0x15d23dc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d23e0: b               #0x15d23e8
    // 0x15d23e4: mov             x2, x1
    // 0x15d23e8: ldur            x0, [fp, #-8]
    // 0x15d23ec: ldur            x1, [fp, #-0x10]
    // 0x15d23f0: stur            x2, [fp, #-0x18]
    // 0x15d23f4: r0 = CachedNetworkImage()
    //     0x15d23f4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15d23f8: stur            x0, [fp, #-0x20]
    // 0x15d23fc: r16 = Instance_BoxFit
    //     0x15d23fc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d2400: ldr             x16, [x16, #0xb18]
    // 0x15d2404: r30 = 50.000000
    //     0x15d2404: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15d2408: ldr             lr, [lr, #0xa90]
    // 0x15d240c: stp             lr, x16, [SP, #8]
    // 0x15d2410: r16 = 50.000000
    //     0x15d2410: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15d2414: ldr             x16, [x16, #0xa90]
    // 0x15d2418: str             x16, [SP]
    // 0x15d241c: mov             x1, x0
    // 0x15d2420: ldur            x2, [fp, #-0x18]
    // 0x15d2424: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d2424: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d2428: ldr             x4, [x4, #0x8e0]
    // 0x15d242c: r0 = CachedNetworkImage()
    //     0x15d242c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15d2430: r0 = Visibility()
    //     0x15d2430: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d2434: mov             x2, x0
    // 0x15d2438: ldur            x0, [fp, #-0x20]
    // 0x15d243c: stur            x2, [fp, #-0x18]
    // 0x15d2440: StoreField: r2->field_b = r0
    //     0x15d2440: stur            w0, [x2, #0xb]
    // 0x15d2444: r0 = Instance_SizedBox
    //     0x15d2444: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d2448: StoreField: r2->field_f = r0
    //     0x15d2448: stur            w0, [x2, #0xf]
    // 0x15d244c: ldur            x1, [fp, #-0x10]
    // 0x15d2450: StoreField: r2->field_13 = r1
    //     0x15d2450: stur            w1, [x2, #0x13]
    // 0x15d2454: r3 = false
    //     0x15d2454: add             x3, NULL, #0x30  ; false
    // 0x15d2458: ArrayStore: r2[0] = r3  ; List_4
    //     0x15d2458: stur            w3, [x2, #0x17]
    // 0x15d245c: StoreField: r2->field_1b = r3
    //     0x15d245c: stur            w3, [x2, #0x1b]
    // 0x15d2460: StoreField: r2->field_1f = r3
    //     0x15d2460: stur            w3, [x2, #0x1f]
    // 0x15d2464: StoreField: r2->field_23 = r3
    //     0x15d2464: stur            w3, [x2, #0x23]
    // 0x15d2468: StoreField: r2->field_27 = r3
    //     0x15d2468: stur            w3, [x2, #0x27]
    // 0x15d246c: StoreField: r2->field_2b = r3
    //     0x15d246c: stur            w3, [x2, #0x2b]
    // 0x15d2470: ldur            x4, [fp, #-8]
    // 0x15d2474: LoadField: r1 = r4->field_f
    //     0x15d2474: ldur            w1, [x4, #0xf]
    // 0x15d2478: DecompressPointer r1
    //     0x15d2478: add             x1, x1, HEAP, lsl #32
    // 0x15d247c: r0 = controller()
    //     0x15d247c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2480: LoadField: r1 = r0->field_7b
    //     0x15d2480: ldur            w1, [x0, #0x7b]
    // 0x15d2484: DecompressPointer r1
    //     0x15d2484: add             x1, x1, HEAP, lsl #32
    // 0x15d2488: r0 = value()
    //     0x15d2488: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d248c: LoadField: r1 = r0->field_3f
    //     0x15d248c: ldur            w1, [x0, #0x3f]
    // 0x15d2490: DecompressPointer r1
    //     0x15d2490: add             x1, x1, HEAP, lsl #32
    // 0x15d2494: cmp             w1, NULL
    // 0x15d2498: b.ne            #0x15d24a4
    // 0x15d249c: r0 = Null
    //     0x15d249c: mov             x0, NULL
    // 0x15d24a0: b               #0x15d24ac
    // 0x15d24a4: LoadField: r0 = r1->field_f
    //     0x15d24a4: ldur            w0, [x1, #0xf]
    // 0x15d24a8: DecompressPointer r0
    //     0x15d24a8: add             x0, x0, HEAP, lsl #32
    // 0x15d24ac: r1 = LoadClassIdInstr(r0)
    //     0x15d24ac: ldur            x1, [x0, #-1]
    //     0x15d24b0: ubfx            x1, x1, #0xc, #0x14
    // 0x15d24b4: r16 = "image_text"
    //     0x15d24b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15d24b8: ldr             x16, [x16, #0xa88]
    // 0x15d24bc: stp             x16, x0, [SP]
    // 0x15d24c0: mov             x0, x1
    // 0x15d24c4: mov             lr, x0
    // 0x15d24c8: ldr             lr, [x21, lr, lsl #3]
    // 0x15d24cc: blr             lr
    // 0x15d24d0: tbnz            w0, #4, #0x15d24dc
    // 0x15d24d4: r2 = true
    //     0x15d24d4: add             x2, NULL, #0x20  ; true
    // 0x15d24d8: b               #0x15d253c
    // 0x15d24dc: ldur            x0, [fp, #-8]
    // 0x15d24e0: LoadField: r1 = r0->field_f
    //     0x15d24e0: ldur            w1, [x0, #0xf]
    // 0x15d24e4: DecompressPointer r1
    //     0x15d24e4: add             x1, x1, HEAP, lsl #32
    // 0x15d24e8: r0 = controller()
    //     0x15d24e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d24ec: LoadField: r1 = r0->field_7b
    //     0x15d24ec: ldur            w1, [x0, #0x7b]
    // 0x15d24f0: DecompressPointer r1
    //     0x15d24f0: add             x1, x1, HEAP, lsl #32
    // 0x15d24f4: r0 = value()
    //     0x15d24f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d24f8: LoadField: r1 = r0->field_3f
    //     0x15d24f8: ldur            w1, [x0, #0x3f]
    // 0x15d24fc: DecompressPointer r1
    //     0x15d24fc: add             x1, x1, HEAP, lsl #32
    // 0x15d2500: cmp             w1, NULL
    // 0x15d2504: b.ne            #0x15d2510
    // 0x15d2508: r0 = Null
    //     0x15d2508: mov             x0, NULL
    // 0x15d250c: b               #0x15d2518
    // 0x15d2510: LoadField: r0 = r1->field_f
    //     0x15d2510: ldur            w0, [x1, #0xf]
    // 0x15d2514: DecompressPointer r0
    //     0x15d2514: add             x0, x0, HEAP, lsl #32
    // 0x15d2518: r1 = LoadClassIdInstr(r0)
    //     0x15d2518: ldur            x1, [x0, #-1]
    //     0x15d251c: ubfx            x1, x1, #0xc, #0x14
    // 0x15d2520: r16 = "text"
    //     0x15d2520: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15d2524: stp             x16, x0, [SP]
    // 0x15d2528: mov             x0, x1
    // 0x15d252c: mov             lr, x0
    // 0x15d2530: ldr             lr, [x21, lr, lsl #3]
    // 0x15d2534: blr             lr
    // 0x15d2538: mov             x2, x0
    // 0x15d253c: ldur            x0, [fp, #-8]
    // 0x15d2540: stur            x2, [fp, #-0x10]
    // 0x15d2544: LoadField: r1 = r0->field_f
    //     0x15d2544: ldur            w1, [x0, #0xf]
    // 0x15d2548: DecompressPointer r1
    //     0x15d2548: add             x1, x1, HEAP, lsl #32
    // 0x15d254c: r0 = controller()
    //     0x15d254c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2550: LoadField: r1 = r0->field_7b
    //     0x15d2550: ldur            w1, [x0, #0x7b]
    // 0x15d2554: DecompressPointer r1
    //     0x15d2554: add             x1, x1, HEAP, lsl #32
    // 0x15d2558: r0 = value()
    //     0x15d2558: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d255c: LoadField: r1 = r0->field_2b
    //     0x15d255c: ldur            w1, [x0, #0x2b]
    // 0x15d2560: DecompressPointer r1
    //     0x15d2560: add             x1, x1, HEAP, lsl #32
    // 0x15d2564: cmp             w1, NULL
    // 0x15d2568: b.ne            #0x15d2574
    // 0x15d256c: r4 = ""
    //     0x15d256c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d2570: b               #0x15d2578
    // 0x15d2574: mov             x4, x1
    // 0x15d2578: ldur            x0, [fp, #-8]
    // 0x15d257c: ldur            x3, [fp, #-0x18]
    // 0x15d2580: ldur            x2, [fp, #-0x10]
    // 0x15d2584: stur            x4, [fp, #-0x20]
    // 0x15d2588: LoadField: r1 = r0->field_13
    //     0x15d2588: ldur            w1, [x0, #0x13]
    // 0x15d258c: DecompressPointer r1
    //     0x15d258c: add             x1, x1, HEAP, lsl #32
    // 0x15d2590: r0 = of()
    //     0x15d2590: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d2594: LoadField: r1 = r0->field_87
    //     0x15d2594: ldur            w1, [x0, #0x87]
    // 0x15d2598: DecompressPointer r1
    //     0x15d2598: add             x1, x1, HEAP, lsl #32
    // 0x15d259c: LoadField: r0 = r1->field_2b
    //     0x15d259c: ldur            w0, [x1, #0x2b]
    // 0x15d25a0: DecompressPointer r0
    //     0x15d25a0: add             x0, x0, HEAP, lsl #32
    // 0x15d25a4: r16 = 16.000000
    //     0x15d25a4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15d25a8: ldr             x16, [x16, #0x188]
    // 0x15d25ac: r30 = Instance_Color
    //     0x15d25ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15d25b0: stp             lr, x16, [SP]
    // 0x15d25b4: mov             x1, x0
    // 0x15d25b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15d25b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15d25bc: ldr             x4, [x4, #0xaa0]
    // 0x15d25c0: r0 = copyWith()
    //     0x15d25c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d25c4: stur            x0, [fp, #-8]
    // 0x15d25c8: r0 = Text()
    //     0x15d25c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d25cc: mov             x1, x0
    // 0x15d25d0: ldur            x0, [fp, #-0x20]
    // 0x15d25d4: stur            x1, [fp, #-0x28]
    // 0x15d25d8: StoreField: r1->field_b = r0
    //     0x15d25d8: stur            w0, [x1, #0xb]
    // 0x15d25dc: ldur            x0, [fp, #-8]
    // 0x15d25e0: StoreField: r1->field_13 = r0
    //     0x15d25e0: stur            w0, [x1, #0x13]
    // 0x15d25e4: r0 = Visibility()
    //     0x15d25e4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d25e8: mov             x3, x0
    // 0x15d25ec: ldur            x0, [fp, #-0x28]
    // 0x15d25f0: stur            x3, [fp, #-8]
    // 0x15d25f4: StoreField: r3->field_b = r0
    //     0x15d25f4: stur            w0, [x3, #0xb]
    // 0x15d25f8: r0 = Instance_SizedBox
    //     0x15d25f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d25fc: StoreField: r3->field_f = r0
    //     0x15d25fc: stur            w0, [x3, #0xf]
    // 0x15d2600: ldur            x0, [fp, #-0x10]
    // 0x15d2604: StoreField: r3->field_13 = r0
    //     0x15d2604: stur            w0, [x3, #0x13]
    // 0x15d2608: r0 = false
    //     0x15d2608: add             x0, NULL, #0x30  ; false
    // 0x15d260c: ArrayStore: r3[0] = r0  ; List_4
    //     0x15d260c: stur            w0, [x3, #0x17]
    // 0x15d2610: StoreField: r3->field_1b = r0
    //     0x15d2610: stur            w0, [x3, #0x1b]
    // 0x15d2614: StoreField: r3->field_1f = r0
    //     0x15d2614: stur            w0, [x3, #0x1f]
    // 0x15d2618: StoreField: r3->field_23 = r0
    //     0x15d2618: stur            w0, [x3, #0x23]
    // 0x15d261c: StoreField: r3->field_27 = r0
    //     0x15d261c: stur            w0, [x3, #0x27]
    // 0x15d2620: StoreField: r3->field_2b = r0
    //     0x15d2620: stur            w0, [x3, #0x2b]
    // 0x15d2624: r1 = Null
    //     0x15d2624: mov             x1, NULL
    // 0x15d2628: r2 = 6
    //     0x15d2628: movz            x2, #0x6
    // 0x15d262c: r0 = AllocateArray()
    //     0x15d262c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d2630: mov             x2, x0
    // 0x15d2634: ldur            x0, [fp, #-0x18]
    // 0x15d2638: stur            x2, [fp, #-0x10]
    // 0x15d263c: StoreField: r2->field_f = r0
    //     0x15d263c: stur            w0, [x2, #0xf]
    // 0x15d2640: r16 = Instance_SizedBox
    //     0x15d2640: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15d2644: ldr             x16, [x16, #0xaa8]
    // 0x15d2648: StoreField: r2->field_13 = r16
    //     0x15d2648: stur            w16, [x2, #0x13]
    // 0x15d264c: ldur            x0, [fp, #-8]
    // 0x15d2650: ArrayStore: r2[0] = r0  ; List_4
    //     0x15d2650: stur            w0, [x2, #0x17]
    // 0x15d2654: r1 = <Widget>
    //     0x15d2654: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15d2658: r0 = AllocateGrowableArray()
    //     0x15d2658: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15d265c: mov             x1, x0
    // 0x15d2660: ldur            x0, [fp, #-0x10]
    // 0x15d2664: stur            x1, [fp, #-8]
    // 0x15d2668: StoreField: r1->field_f = r0
    //     0x15d2668: stur            w0, [x1, #0xf]
    // 0x15d266c: r0 = 6
    //     0x15d266c: movz            x0, #0x6
    // 0x15d2670: StoreField: r1->field_b = r0
    //     0x15d2670: stur            w0, [x1, #0xb]
    // 0x15d2674: r0 = Row()
    //     0x15d2674: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15d2678: r1 = Instance_Axis
    //     0x15d2678: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15d267c: StoreField: r0->field_f = r1
    //     0x15d267c: stur            w1, [x0, #0xf]
    // 0x15d2680: r1 = Instance_MainAxisAlignment
    //     0x15d2680: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15d2684: ldr             x1, [x1, #0xab0]
    // 0x15d2688: StoreField: r0->field_13 = r1
    //     0x15d2688: stur            w1, [x0, #0x13]
    // 0x15d268c: r1 = Instance_MainAxisSize
    //     0x15d268c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15d2690: ldr             x1, [x1, #0xa10]
    // 0x15d2694: ArrayStore: r0[0] = r1  ; List_4
    //     0x15d2694: stur            w1, [x0, #0x17]
    // 0x15d2698: r1 = Instance_CrossAxisAlignment
    //     0x15d2698: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15d269c: ldr             x1, [x1, #0xa18]
    // 0x15d26a0: StoreField: r0->field_1b = r1
    //     0x15d26a0: stur            w1, [x0, #0x1b]
    // 0x15d26a4: r1 = Instance_VerticalDirection
    //     0x15d26a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15d26a8: ldr             x1, [x1, #0xa20]
    // 0x15d26ac: StoreField: r0->field_23 = r1
    //     0x15d26ac: stur            w1, [x0, #0x23]
    // 0x15d26b0: r1 = Instance_Clip
    //     0x15d26b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15d26b4: ldr             x1, [x1, #0x38]
    // 0x15d26b8: StoreField: r0->field_2b = r1
    //     0x15d26b8: stur            w1, [x0, #0x2b]
    // 0x15d26bc: StoreField: r0->field_2f = rZR
    //     0x15d26bc: stur            xzr, [x0, #0x2f]
    // 0x15d26c0: ldur            x1, [fp, #-8]
    // 0x15d26c4: StoreField: r0->field_b = r1
    //     0x15d26c4: stur            w1, [x0, #0xb]
    // 0x15d26c8: LeaveFrame
    //     0x15d26c8: mov             SP, fp
    //     0x15d26cc: ldp             fp, lr, [SP], #0x10
    // 0x15d26d0: ret
    //     0x15d26d0: ret             
    // 0x15d26d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d26d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d26d8: b               #0x15d22e4
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e26c0, size: 0x2b4
    // 0x15e26c0: EnterFrame
    //     0x15e26c0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e26c4: mov             fp, SP
    // 0x15e26c8: AllocStack(0x30)
    //     0x15e26c8: sub             SP, SP, #0x30
    // 0x15e26cc: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e26cc: stur            x1, [fp, #-8]
    //     0x15e26d0: stur            x2, [fp, #-0x10]
    // 0x15e26d4: CheckStackOverflow
    //     0x15e26d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e26d8: cmp             SP, x16
    //     0x15e26dc: b.ls            #0x15e296c
    // 0x15e26e0: r1 = 2
    //     0x15e26e0: movz            x1, #0x2
    // 0x15e26e4: r0 = AllocateContext()
    //     0x15e26e4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e26e8: ldur            x1, [fp, #-8]
    // 0x15e26ec: stur            x0, [fp, #-0x18]
    // 0x15e26f0: StoreField: r0->field_f = r1
    //     0x15e26f0: stur            w1, [x0, #0xf]
    // 0x15e26f4: ldur            x2, [fp, #-0x10]
    // 0x15e26f8: StoreField: r0->field_13 = r2
    //     0x15e26f8: stur            w2, [x0, #0x13]
    // 0x15e26fc: r0 = Obx()
    //     0x15e26fc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e2700: ldur            x2, [fp, #-0x18]
    // 0x15e2704: r1 = Function '<anonymous closure>':.
    //     0x15e2704: add             x1, PP, #0x40, lsl #12  ; [pp+0x40330] AnonymousClosure: (0x15d22bc), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::appBar (0x15e26c0)
    //     0x15e2708: ldr             x1, [x1, #0x330]
    // 0x15e270c: stur            x0, [fp, #-0x10]
    // 0x15e2710: r0 = AllocateClosure()
    //     0x15e2710: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e2714: mov             x1, x0
    // 0x15e2718: ldur            x0, [fp, #-0x10]
    // 0x15e271c: StoreField: r0->field_b = r1
    //     0x15e271c: stur            w1, [x0, #0xb]
    // 0x15e2720: ldur            x1, [fp, #-8]
    // 0x15e2724: r0 = controller()
    //     0x15e2724: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e2728: LoadField: r1 = r0->field_bf
    //     0x15e2728: ldur            w1, [x0, #0xbf]
    // 0x15e272c: DecompressPointer r1
    //     0x15e272c: add             x1, x1, HEAP, lsl #32
    // 0x15e2730: r0 = value()
    //     0x15e2730: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e2734: tbnz            w0, #4, #0x15e27cc
    // 0x15e2738: ldur            x2, [fp, #-0x18]
    // 0x15e273c: LoadField: r1 = r2->field_13
    //     0x15e273c: ldur            w1, [x2, #0x13]
    // 0x15e2740: DecompressPointer r1
    //     0x15e2740: add             x1, x1, HEAP, lsl #32
    // 0x15e2744: r0 = of()
    //     0x15e2744: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e2748: LoadField: r1 = r0->field_5b
    //     0x15e2748: ldur            w1, [x0, #0x5b]
    // 0x15e274c: DecompressPointer r1
    //     0x15e274c: add             x1, x1, HEAP, lsl #32
    // 0x15e2750: stur            x1, [fp, #-8]
    // 0x15e2754: r0 = ColorFilter()
    //     0x15e2754: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e2758: mov             x1, x0
    // 0x15e275c: ldur            x0, [fp, #-8]
    // 0x15e2760: stur            x1, [fp, #-0x20]
    // 0x15e2764: StoreField: r1->field_7 = r0
    //     0x15e2764: stur            w0, [x1, #7]
    // 0x15e2768: r0 = Instance_BlendMode
    //     0x15e2768: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e276c: ldr             x0, [x0, #0xb30]
    // 0x15e2770: StoreField: r1->field_b = r0
    //     0x15e2770: stur            w0, [x1, #0xb]
    // 0x15e2774: r2 = 1
    //     0x15e2774: movz            x2, #0x1
    // 0x15e2778: StoreField: r1->field_13 = r2
    //     0x15e2778: stur            x2, [x1, #0x13]
    // 0x15e277c: r0 = SvgPicture()
    //     0x15e277c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e2780: stur            x0, [fp, #-8]
    // 0x15e2784: ldur            x16, [fp, #-0x20]
    // 0x15e2788: str             x16, [SP]
    // 0x15e278c: mov             x1, x0
    // 0x15e2790: r2 = "assets/images/search.svg"
    //     0x15e2790: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e2794: ldr             x2, [x2, #0xa30]
    // 0x15e2798: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e2798: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e279c: ldr             x4, [x4, #0xa38]
    // 0x15e27a0: r0 = SvgPicture.asset()
    //     0x15e27a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e27a4: r0 = Align()
    //     0x15e27a4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e27a8: r3 = Instance_Alignment
    //     0x15e27a8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e27ac: ldr             x3, [x3, #0xb10]
    // 0x15e27b0: StoreField: r0->field_f = r3
    //     0x15e27b0: stur            w3, [x0, #0xf]
    // 0x15e27b4: r4 = 1.000000
    //     0x15e27b4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e27b8: StoreField: r0->field_13 = r4
    //     0x15e27b8: stur            w4, [x0, #0x13]
    // 0x15e27bc: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e27bc: stur            w4, [x0, #0x17]
    // 0x15e27c0: ldur            x1, [fp, #-8]
    // 0x15e27c4: StoreField: r0->field_b = r1
    //     0x15e27c4: stur            w1, [x0, #0xb]
    // 0x15e27c8: b               #0x15e287c
    // 0x15e27cc: ldur            x5, [fp, #-0x18]
    // 0x15e27d0: r4 = 1.000000
    //     0x15e27d0: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e27d4: r0 = Instance_BlendMode
    //     0x15e27d4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e27d8: ldr             x0, [x0, #0xb30]
    // 0x15e27dc: r3 = Instance_Alignment
    //     0x15e27dc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e27e0: ldr             x3, [x3, #0xb10]
    // 0x15e27e4: r2 = 1
    //     0x15e27e4: movz            x2, #0x1
    // 0x15e27e8: LoadField: r1 = r5->field_13
    //     0x15e27e8: ldur            w1, [x5, #0x13]
    // 0x15e27ec: DecompressPointer r1
    //     0x15e27ec: add             x1, x1, HEAP, lsl #32
    // 0x15e27f0: r0 = of()
    //     0x15e27f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e27f4: LoadField: r1 = r0->field_5b
    //     0x15e27f4: ldur            w1, [x0, #0x5b]
    // 0x15e27f8: DecompressPointer r1
    //     0x15e27f8: add             x1, x1, HEAP, lsl #32
    // 0x15e27fc: stur            x1, [fp, #-8]
    // 0x15e2800: r0 = ColorFilter()
    //     0x15e2800: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e2804: mov             x1, x0
    // 0x15e2808: ldur            x0, [fp, #-8]
    // 0x15e280c: stur            x1, [fp, #-0x20]
    // 0x15e2810: StoreField: r1->field_7 = r0
    //     0x15e2810: stur            w0, [x1, #7]
    // 0x15e2814: r0 = Instance_BlendMode
    //     0x15e2814: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e2818: ldr             x0, [x0, #0xb30]
    // 0x15e281c: StoreField: r1->field_b = r0
    //     0x15e281c: stur            w0, [x1, #0xb]
    // 0x15e2820: r0 = 1
    //     0x15e2820: movz            x0, #0x1
    // 0x15e2824: StoreField: r1->field_13 = r0
    //     0x15e2824: stur            x0, [x1, #0x13]
    // 0x15e2828: r0 = SvgPicture()
    //     0x15e2828: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e282c: stur            x0, [fp, #-8]
    // 0x15e2830: ldur            x16, [fp, #-0x20]
    // 0x15e2834: str             x16, [SP]
    // 0x15e2838: mov             x1, x0
    // 0x15e283c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e283c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e2840: ldr             x2, [x2, #0xa40]
    // 0x15e2844: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e2844: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e2848: ldr             x4, [x4, #0xa38]
    // 0x15e284c: r0 = SvgPicture.asset()
    //     0x15e284c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e2850: r0 = Align()
    //     0x15e2850: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e2854: mov             x1, x0
    // 0x15e2858: r0 = Instance_Alignment
    //     0x15e2858: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e285c: ldr             x0, [x0, #0xb10]
    // 0x15e2860: StoreField: r1->field_f = r0
    //     0x15e2860: stur            w0, [x1, #0xf]
    // 0x15e2864: r0 = 1.000000
    //     0x15e2864: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e2868: StoreField: r1->field_13 = r0
    //     0x15e2868: stur            w0, [x1, #0x13]
    // 0x15e286c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e286c: stur            w0, [x1, #0x17]
    // 0x15e2870: ldur            x0, [fp, #-8]
    // 0x15e2874: StoreField: r1->field_b = r0
    //     0x15e2874: stur            w0, [x1, #0xb]
    // 0x15e2878: mov             x0, x1
    // 0x15e287c: stur            x0, [fp, #-8]
    // 0x15e2880: r0 = InkWell()
    //     0x15e2880: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e2884: mov             x3, x0
    // 0x15e2888: ldur            x0, [fp, #-8]
    // 0x15e288c: stur            x3, [fp, #-0x20]
    // 0x15e2890: StoreField: r3->field_b = r0
    //     0x15e2890: stur            w0, [x3, #0xb]
    // 0x15e2894: ldur            x2, [fp, #-0x18]
    // 0x15e2898: r1 = Function '<anonymous closure>':.
    //     0x15e2898: add             x1, PP, #0x40, lsl #12  ; [pp+0x40338] AnonymousClosure: (0x15e2d4c), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::appBar (0x15e26c0)
    //     0x15e289c: ldr             x1, [x1, #0x338]
    // 0x15e28a0: r0 = AllocateClosure()
    //     0x15e28a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e28a4: ldur            x2, [fp, #-0x20]
    // 0x15e28a8: StoreField: r2->field_f = r0
    //     0x15e28a8: stur            w0, [x2, #0xf]
    // 0x15e28ac: r0 = true
    //     0x15e28ac: add             x0, NULL, #0x20  ; true
    // 0x15e28b0: StoreField: r2->field_43 = r0
    //     0x15e28b0: stur            w0, [x2, #0x43]
    // 0x15e28b4: r1 = Instance_BoxShape
    //     0x15e28b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e28b8: ldr             x1, [x1, #0x80]
    // 0x15e28bc: StoreField: r2->field_47 = r1
    //     0x15e28bc: stur            w1, [x2, #0x47]
    // 0x15e28c0: StoreField: r2->field_6f = r0
    //     0x15e28c0: stur            w0, [x2, #0x6f]
    // 0x15e28c4: r1 = false
    //     0x15e28c4: add             x1, NULL, #0x30  ; false
    // 0x15e28c8: StoreField: r2->field_73 = r1
    //     0x15e28c8: stur            w1, [x2, #0x73]
    // 0x15e28cc: StoreField: r2->field_83 = r0
    //     0x15e28cc: stur            w0, [x2, #0x83]
    // 0x15e28d0: StoreField: r2->field_7b = r1
    //     0x15e28d0: stur            w1, [x2, #0x7b]
    // 0x15e28d4: r0 = Obx()
    //     0x15e28d4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e28d8: ldur            x2, [fp, #-0x18]
    // 0x15e28dc: r1 = Function '<anonymous closure>':.
    //     0x15e28dc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40340] AnonymousClosure: (0x15e2974), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::appBar (0x15e26c0)
    //     0x15e28e0: ldr             x1, [x1, #0x340]
    // 0x15e28e4: stur            x0, [fp, #-8]
    // 0x15e28e8: r0 = AllocateClosure()
    //     0x15e28e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e28ec: mov             x1, x0
    // 0x15e28f0: ldur            x0, [fp, #-8]
    // 0x15e28f4: StoreField: r0->field_b = r1
    //     0x15e28f4: stur            w1, [x0, #0xb]
    // 0x15e28f8: r1 = Null
    //     0x15e28f8: mov             x1, NULL
    // 0x15e28fc: r2 = 2
    //     0x15e28fc: movz            x2, #0x2
    // 0x15e2900: r0 = AllocateArray()
    //     0x15e2900: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e2904: mov             x2, x0
    // 0x15e2908: ldur            x0, [fp, #-8]
    // 0x15e290c: stur            x2, [fp, #-0x18]
    // 0x15e2910: StoreField: r2->field_f = r0
    //     0x15e2910: stur            w0, [x2, #0xf]
    // 0x15e2914: r1 = <Widget>
    //     0x15e2914: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e2918: r0 = AllocateGrowableArray()
    //     0x15e2918: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e291c: mov             x1, x0
    // 0x15e2920: ldur            x0, [fp, #-0x18]
    // 0x15e2924: stur            x1, [fp, #-8]
    // 0x15e2928: StoreField: r1->field_f = r0
    //     0x15e2928: stur            w0, [x1, #0xf]
    // 0x15e292c: r0 = 2
    //     0x15e292c: movz            x0, #0x2
    // 0x15e2930: StoreField: r1->field_b = r0
    //     0x15e2930: stur            w0, [x1, #0xb]
    // 0x15e2934: r0 = AppBar()
    //     0x15e2934: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e2938: stur            x0, [fp, #-0x18]
    // 0x15e293c: ldur            x16, [fp, #-0x10]
    // 0x15e2940: ldur            lr, [fp, #-8]
    // 0x15e2944: stp             lr, x16, [SP]
    // 0x15e2948: mov             x1, x0
    // 0x15e294c: ldur            x2, [fp, #-0x20]
    // 0x15e2950: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e2950: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e2954: ldr             x4, [x4, #0xa58]
    // 0x15e2958: r0 = AppBar()
    //     0x15e2958: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e295c: ldur            x0, [fp, #-0x18]
    // 0x15e2960: LeaveFrame
    //     0x15e2960: mov             SP, fp
    //     0x15e2964: ldp             fp, lr, [SP], #0x10
    // 0x15e2968: ret
    //     0x15e2968: ret             
    // 0x15e296c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e296c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e2970: b               #0x15e26e0
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e2974, size: 0x2fc
    // 0x15e2974: EnterFrame
    //     0x15e2974: stp             fp, lr, [SP, #-0x10]!
    //     0x15e2978: mov             fp, SP
    // 0x15e297c: AllocStack(0x58)
    //     0x15e297c: sub             SP, SP, #0x58
    // 0x15e2980: SetupParameters()
    //     0x15e2980: ldr             x0, [fp, #0x10]
    //     0x15e2984: ldur            w2, [x0, #0x17]
    //     0x15e2988: add             x2, x2, HEAP, lsl #32
    //     0x15e298c: stur            x2, [fp, #-8]
    // 0x15e2990: CheckStackOverflow
    //     0x15e2990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e2994: cmp             SP, x16
    //     0x15e2998: b.ls            #0x15e2c68
    // 0x15e299c: LoadField: r1 = r2->field_f
    //     0x15e299c: ldur            w1, [x2, #0xf]
    // 0x15e29a0: DecompressPointer r1
    //     0x15e29a0: add             x1, x1, HEAP, lsl #32
    // 0x15e29a4: r0 = controller()
    //     0x15e29a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e29a8: LoadField: r1 = r0->field_7b
    //     0x15e29a8: ldur            w1, [x0, #0x7b]
    // 0x15e29ac: DecompressPointer r1
    //     0x15e29ac: add             x1, x1, HEAP, lsl #32
    // 0x15e29b0: r0 = value()
    //     0x15e29b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e29b4: LoadField: r1 = r0->field_1f
    //     0x15e29b4: ldur            w1, [x0, #0x1f]
    // 0x15e29b8: DecompressPointer r1
    //     0x15e29b8: add             x1, x1, HEAP, lsl #32
    // 0x15e29bc: cmp             w1, NULL
    // 0x15e29c0: b.ne            #0x15e29cc
    // 0x15e29c4: r0 = Null
    //     0x15e29c4: mov             x0, NULL
    // 0x15e29c8: b               #0x15e29d4
    // 0x15e29cc: LoadField: r0 = r1->field_7
    //     0x15e29cc: ldur            w0, [x1, #7]
    // 0x15e29d0: DecompressPointer r0
    //     0x15e29d0: add             x0, x0, HEAP, lsl #32
    // 0x15e29d4: cmp             w0, NULL
    // 0x15e29d8: b.ne            #0x15e29e4
    // 0x15e29dc: r0 = false
    //     0x15e29dc: add             x0, NULL, #0x30  ; false
    // 0x15e29e0: b               #0x15e2bd0
    // 0x15e29e4: tbnz            w0, #4, #0x15e2bcc
    // 0x15e29e8: ldur            x2, [fp, #-8]
    // 0x15e29ec: LoadField: r1 = r2->field_f
    //     0x15e29ec: ldur            w1, [x2, #0xf]
    // 0x15e29f0: DecompressPointer r1
    //     0x15e29f0: add             x1, x1, HEAP, lsl #32
    // 0x15e29f4: r0 = controller()
    //     0x15e29f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e29f8: LoadField: r1 = r0->field_c3
    //     0x15e29f8: ldur            w1, [x0, #0xc3]
    // 0x15e29fc: DecompressPointer r1
    //     0x15e29fc: add             x1, x1, HEAP, lsl #32
    // 0x15e2a00: r0 = value()
    //     0x15e2a00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e2a04: ldur            x2, [fp, #-8]
    // 0x15e2a08: stur            x0, [fp, #-0x10]
    // 0x15e2a0c: LoadField: r1 = r2->field_13
    //     0x15e2a0c: ldur            w1, [x2, #0x13]
    // 0x15e2a10: DecompressPointer r1
    //     0x15e2a10: add             x1, x1, HEAP, lsl #32
    // 0x15e2a14: r0 = of()
    //     0x15e2a14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e2a18: LoadField: r2 = r0->field_5b
    //     0x15e2a18: ldur            w2, [x0, #0x5b]
    // 0x15e2a1c: DecompressPointer r2
    //     0x15e2a1c: add             x2, x2, HEAP, lsl #32
    // 0x15e2a20: ldur            x0, [fp, #-8]
    // 0x15e2a24: stur            x2, [fp, #-0x18]
    // 0x15e2a28: LoadField: r1 = r0->field_f
    //     0x15e2a28: ldur            w1, [x0, #0xf]
    // 0x15e2a2c: DecompressPointer r1
    //     0x15e2a2c: add             x1, x1, HEAP, lsl #32
    // 0x15e2a30: r0 = controller()
    //     0x15e2a30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e2a34: LoadField: r1 = r0->field_c7
    //     0x15e2a34: ldur            w1, [x0, #0xc7]
    // 0x15e2a38: DecompressPointer r1
    //     0x15e2a38: add             x1, x1, HEAP, lsl #32
    // 0x15e2a3c: r0 = value()
    //     0x15e2a3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e2a40: cmp             w0, NULL
    // 0x15e2a44: r16 = true
    //     0x15e2a44: add             x16, NULL, #0x20  ; true
    // 0x15e2a48: r17 = false
    //     0x15e2a48: add             x17, NULL, #0x30  ; false
    // 0x15e2a4c: csel            x2, x16, x17, ne
    // 0x15e2a50: ldur            x0, [fp, #-8]
    // 0x15e2a54: stur            x2, [fp, #-0x20]
    // 0x15e2a58: LoadField: r1 = r0->field_f
    //     0x15e2a58: ldur            w1, [x0, #0xf]
    // 0x15e2a5c: DecompressPointer r1
    //     0x15e2a5c: add             x1, x1, HEAP, lsl #32
    // 0x15e2a60: r0 = controller()
    //     0x15e2a60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e2a64: LoadField: r1 = r0->field_c7
    //     0x15e2a64: ldur            w1, [x0, #0xc7]
    // 0x15e2a68: DecompressPointer r1
    //     0x15e2a68: add             x1, x1, HEAP, lsl #32
    // 0x15e2a6c: r0 = value()
    //     0x15e2a6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e2a70: str             x0, [SP]
    // 0x15e2a74: r0 = _interpolateSingle()
    //     0x15e2a74: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e2a78: ldur            x2, [fp, #-8]
    // 0x15e2a7c: stur            x0, [fp, #-0x28]
    // 0x15e2a80: LoadField: r1 = r2->field_13
    //     0x15e2a80: ldur            w1, [x2, #0x13]
    // 0x15e2a84: DecompressPointer r1
    //     0x15e2a84: add             x1, x1, HEAP, lsl #32
    // 0x15e2a88: r0 = of()
    //     0x15e2a88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e2a8c: LoadField: r1 = r0->field_87
    //     0x15e2a8c: ldur            w1, [x0, #0x87]
    // 0x15e2a90: DecompressPointer r1
    //     0x15e2a90: add             x1, x1, HEAP, lsl #32
    // 0x15e2a94: LoadField: r0 = r1->field_27
    //     0x15e2a94: ldur            w0, [x1, #0x27]
    // 0x15e2a98: DecompressPointer r0
    //     0x15e2a98: add             x0, x0, HEAP, lsl #32
    // 0x15e2a9c: r16 = Instance_Color
    //     0x15e2a9c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e2aa0: str             x16, [SP]
    // 0x15e2aa4: mov             x1, x0
    // 0x15e2aa8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e2aa8: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e2aac: ldr             x4, [x4, #0xf40]
    // 0x15e2ab0: r0 = copyWith()
    //     0x15e2ab0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e2ab4: stur            x0, [fp, #-0x30]
    // 0x15e2ab8: r0 = Text()
    //     0x15e2ab8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e2abc: mov             x2, x0
    // 0x15e2ac0: ldur            x0, [fp, #-0x28]
    // 0x15e2ac4: stur            x2, [fp, #-0x38]
    // 0x15e2ac8: StoreField: r2->field_b = r0
    //     0x15e2ac8: stur            w0, [x2, #0xb]
    // 0x15e2acc: ldur            x0, [fp, #-0x30]
    // 0x15e2ad0: StoreField: r2->field_13 = r0
    //     0x15e2ad0: stur            w0, [x2, #0x13]
    // 0x15e2ad4: ldur            x0, [fp, #-8]
    // 0x15e2ad8: LoadField: r1 = r0->field_13
    //     0x15e2ad8: ldur            w1, [x0, #0x13]
    // 0x15e2adc: DecompressPointer r1
    //     0x15e2adc: add             x1, x1, HEAP, lsl #32
    // 0x15e2ae0: r0 = of()
    //     0x15e2ae0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e2ae4: LoadField: r1 = r0->field_5b
    //     0x15e2ae4: ldur            w1, [x0, #0x5b]
    // 0x15e2ae8: DecompressPointer r1
    //     0x15e2ae8: add             x1, x1, HEAP, lsl #32
    // 0x15e2aec: stur            x1, [fp, #-0x28]
    // 0x15e2af0: r0 = ColorFilter()
    //     0x15e2af0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e2af4: mov             x1, x0
    // 0x15e2af8: ldur            x0, [fp, #-0x28]
    // 0x15e2afc: stur            x1, [fp, #-0x30]
    // 0x15e2b00: StoreField: r1->field_7 = r0
    //     0x15e2b00: stur            w0, [x1, #7]
    // 0x15e2b04: r0 = Instance_BlendMode
    //     0x15e2b04: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e2b08: ldr             x0, [x0, #0xb30]
    // 0x15e2b0c: StoreField: r1->field_b = r0
    //     0x15e2b0c: stur            w0, [x1, #0xb]
    // 0x15e2b10: r0 = 1
    //     0x15e2b10: movz            x0, #0x1
    // 0x15e2b14: StoreField: r1->field_13 = r0
    //     0x15e2b14: stur            x0, [x1, #0x13]
    // 0x15e2b18: r0 = SvgPicture()
    //     0x15e2b18: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e2b1c: stur            x0, [fp, #-0x28]
    // 0x15e2b20: r16 = Instance_BoxFit
    //     0x15e2b20: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e2b24: ldr             x16, [x16, #0xb18]
    // 0x15e2b28: r30 = 24.000000
    //     0x15e2b28: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e2b2c: ldr             lr, [lr, #0xba8]
    // 0x15e2b30: stp             lr, x16, [SP, #0x10]
    // 0x15e2b34: r16 = 24.000000
    //     0x15e2b34: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e2b38: ldr             x16, [x16, #0xba8]
    // 0x15e2b3c: ldur            lr, [fp, #-0x30]
    // 0x15e2b40: stp             lr, x16, [SP]
    // 0x15e2b44: mov             x1, x0
    // 0x15e2b48: r2 = "assets/images/shopping_bag.svg"
    //     0x15e2b48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e2b4c: ldr             x2, [x2, #0xa60]
    // 0x15e2b50: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e2b50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e2b54: ldr             x4, [x4, #0xa68]
    // 0x15e2b58: r0 = SvgPicture.asset()
    //     0x15e2b58: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e2b5c: r0 = Badge()
    //     0x15e2b5c: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e2b60: mov             x1, x0
    // 0x15e2b64: ldur            x0, [fp, #-0x18]
    // 0x15e2b68: stur            x1, [fp, #-0x30]
    // 0x15e2b6c: StoreField: r1->field_b = r0
    //     0x15e2b6c: stur            w0, [x1, #0xb]
    // 0x15e2b70: ldur            x0, [fp, #-0x38]
    // 0x15e2b74: StoreField: r1->field_27 = r0
    //     0x15e2b74: stur            w0, [x1, #0x27]
    // 0x15e2b78: ldur            x0, [fp, #-0x20]
    // 0x15e2b7c: StoreField: r1->field_2b = r0
    //     0x15e2b7c: stur            w0, [x1, #0x2b]
    // 0x15e2b80: ldur            x0, [fp, #-0x28]
    // 0x15e2b84: StoreField: r1->field_2f = r0
    //     0x15e2b84: stur            w0, [x1, #0x2f]
    // 0x15e2b88: r0 = Visibility()
    //     0x15e2b88: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e2b8c: mov             x1, x0
    // 0x15e2b90: ldur            x0, [fp, #-0x30]
    // 0x15e2b94: StoreField: r1->field_b = r0
    //     0x15e2b94: stur            w0, [x1, #0xb]
    // 0x15e2b98: r0 = Instance_SizedBox
    //     0x15e2b98: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e2b9c: StoreField: r1->field_f = r0
    //     0x15e2b9c: stur            w0, [x1, #0xf]
    // 0x15e2ba0: ldur            x0, [fp, #-0x10]
    // 0x15e2ba4: StoreField: r1->field_13 = r0
    //     0x15e2ba4: stur            w0, [x1, #0x13]
    // 0x15e2ba8: r0 = false
    //     0x15e2ba8: add             x0, NULL, #0x30  ; false
    // 0x15e2bac: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e2bac: stur            w0, [x1, #0x17]
    // 0x15e2bb0: StoreField: r1->field_1b = r0
    //     0x15e2bb0: stur            w0, [x1, #0x1b]
    // 0x15e2bb4: StoreField: r1->field_1f = r0
    //     0x15e2bb4: stur            w0, [x1, #0x1f]
    // 0x15e2bb8: StoreField: r1->field_23 = r0
    //     0x15e2bb8: stur            w0, [x1, #0x23]
    // 0x15e2bbc: StoreField: r1->field_27 = r0
    //     0x15e2bbc: stur            w0, [x1, #0x27]
    // 0x15e2bc0: StoreField: r1->field_2b = r0
    //     0x15e2bc0: stur            w0, [x1, #0x2b]
    // 0x15e2bc4: mov             x0, x1
    // 0x15e2bc8: b               #0x15e2be8
    // 0x15e2bcc: r0 = false
    //     0x15e2bcc: add             x0, NULL, #0x30  ; false
    // 0x15e2bd0: r0 = Container()
    //     0x15e2bd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e2bd4: mov             x1, x0
    // 0x15e2bd8: stur            x0, [fp, #-0x10]
    // 0x15e2bdc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e2bdc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e2be0: r0 = Container()
    //     0x15e2be0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e2be4: ldur            x0, [fp, #-0x10]
    // 0x15e2be8: stur            x0, [fp, #-0x10]
    // 0x15e2bec: r0 = InkWell()
    //     0x15e2bec: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e2bf0: mov             x3, x0
    // 0x15e2bf4: ldur            x0, [fp, #-0x10]
    // 0x15e2bf8: stur            x3, [fp, #-0x18]
    // 0x15e2bfc: StoreField: r3->field_b = r0
    //     0x15e2bfc: stur            w0, [x3, #0xb]
    // 0x15e2c00: ldur            x2, [fp, #-8]
    // 0x15e2c04: r1 = Function '<anonymous closure>':.
    //     0x15e2c04: add             x1, PP, #0x40, lsl #12  ; [pp+0x40348] AnonymousClosure: (0x15e2c70), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::appBar (0x15e26c0)
    //     0x15e2c08: ldr             x1, [x1, #0x348]
    // 0x15e2c0c: r0 = AllocateClosure()
    //     0x15e2c0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e2c10: mov             x1, x0
    // 0x15e2c14: ldur            x0, [fp, #-0x18]
    // 0x15e2c18: StoreField: r0->field_f = r1
    //     0x15e2c18: stur            w1, [x0, #0xf]
    // 0x15e2c1c: r1 = true
    //     0x15e2c1c: add             x1, NULL, #0x20  ; true
    // 0x15e2c20: StoreField: r0->field_43 = r1
    //     0x15e2c20: stur            w1, [x0, #0x43]
    // 0x15e2c24: r2 = Instance_BoxShape
    //     0x15e2c24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e2c28: ldr             x2, [x2, #0x80]
    // 0x15e2c2c: StoreField: r0->field_47 = r2
    //     0x15e2c2c: stur            w2, [x0, #0x47]
    // 0x15e2c30: StoreField: r0->field_6f = r1
    //     0x15e2c30: stur            w1, [x0, #0x6f]
    // 0x15e2c34: r2 = false
    //     0x15e2c34: add             x2, NULL, #0x30  ; false
    // 0x15e2c38: StoreField: r0->field_73 = r2
    //     0x15e2c38: stur            w2, [x0, #0x73]
    // 0x15e2c3c: StoreField: r0->field_83 = r1
    //     0x15e2c3c: stur            w1, [x0, #0x83]
    // 0x15e2c40: StoreField: r0->field_7b = r2
    //     0x15e2c40: stur            w2, [x0, #0x7b]
    // 0x15e2c44: r0 = Padding()
    //     0x15e2c44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e2c48: r1 = Instance_EdgeInsets
    //     0x15e2c48: add             x1, PP, #0x37, lsl #12  ; [pp+0x37290] Obj!EdgeInsets@d5a111
    //     0x15e2c4c: ldr             x1, [x1, #0x290]
    // 0x15e2c50: StoreField: r0->field_f = r1
    //     0x15e2c50: stur            w1, [x0, #0xf]
    // 0x15e2c54: ldur            x1, [fp, #-0x18]
    // 0x15e2c58: StoreField: r0->field_b = r1
    //     0x15e2c58: stur            w1, [x0, #0xb]
    // 0x15e2c5c: LeaveFrame
    //     0x15e2c5c: mov             SP, fp
    //     0x15e2c60: ldp             fp, lr, [SP], #0x10
    // 0x15e2c64: ret
    //     0x15e2c64: ret             
    // 0x15e2c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e2c68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e2c6c: b               #0x15e299c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e2c70, size: 0xdc
    // 0x15e2c70: EnterFrame
    //     0x15e2c70: stp             fp, lr, [SP, #-0x10]!
    //     0x15e2c74: mov             fp, SP
    // 0x15e2c78: AllocStack(0x28)
    //     0x15e2c78: sub             SP, SP, #0x28
    // 0x15e2c7c: SetupParameters()
    //     0x15e2c7c: ldr             x0, [fp, #0x10]
    //     0x15e2c80: ldur            w2, [x0, #0x17]
    //     0x15e2c84: add             x2, x2, HEAP, lsl #32
    //     0x15e2c88: stur            x2, [fp, #-8]
    // 0x15e2c8c: CheckStackOverflow
    //     0x15e2c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e2c90: cmp             SP, x16
    //     0x15e2c94: b.ls            #0x15e2d44
    // 0x15e2c98: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e2c98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e2c9c: ldr             x0, [x0, #0x1c80]
    //     0x15e2ca0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e2ca4: cmp             w0, w16
    //     0x15e2ca8: b.ne            #0x15e2cb4
    //     0x15e2cac: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e2cb0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e2cb4: r1 = Null
    //     0x15e2cb4: mov             x1, NULL
    // 0x15e2cb8: r2 = 4
    //     0x15e2cb8: movz            x2, #0x4
    // 0x15e2cbc: r0 = AllocateArray()
    //     0x15e2cbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e2cc0: r16 = "previousScreenSource"
    //     0x15e2cc0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15e2cc4: ldr             x16, [x16, #0x448]
    // 0x15e2cc8: StoreField: r0->field_f = r16
    //     0x15e2cc8: stur            w16, [x0, #0xf]
    // 0x15e2ccc: r16 = "login_page"
    //     0x15e2ccc: add             x16, PP, #0x37, lsl #12  ; [pp+0x37298] "login_page"
    //     0x15e2cd0: ldr             x16, [x16, #0x298]
    // 0x15e2cd4: StoreField: r0->field_13 = r16
    //     0x15e2cd4: stur            w16, [x0, #0x13]
    // 0x15e2cd8: r16 = <String, String>
    //     0x15e2cd8: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15e2cdc: ldr             x16, [x16, #0x788]
    // 0x15e2ce0: stp             x0, x16, [SP]
    // 0x15e2ce4: r0 = Map._fromLiteral()
    //     0x15e2ce4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15e2ce8: r16 = "/bag"
    //     0x15e2ce8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15e2cec: ldr             x16, [x16, #0x468]
    // 0x15e2cf0: stp             x16, NULL, [SP, #8]
    // 0x15e2cf4: str             x0, [SP]
    // 0x15e2cf8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15e2cf8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15e2cfc: ldr             x4, [x4, #0x438]
    // 0x15e2d00: r0 = GetNavigation.toNamed()
    //     0x15e2d00: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e2d04: stur            x0, [fp, #-0x10]
    // 0x15e2d08: cmp             w0, NULL
    // 0x15e2d0c: b.eq            #0x15e2d34
    // 0x15e2d10: ldur            x2, [fp, #-8]
    // 0x15e2d14: r1 = Function '<anonymous closure>':.
    //     0x15e2d14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40350] AnonymousClosure: (0x15d1de4), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15e2d18: ldr             x1, [x1, #0x350]
    // 0x15e2d1c: r0 = AllocateClosure()
    //     0x15e2d1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e2d20: ldur            x16, [fp, #-0x10]
    // 0x15e2d24: stp             x16, NULL, [SP, #8]
    // 0x15e2d28: str             x0, [SP]
    // 0x15e2d2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15e2d2c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15e2d30: r0 = then()
    //     0x15e2d30: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15e2d34: r0 = Null
    //     0x15e2d34: mov             x0, NULL
    // 0x15e2d38: LeaveFrame
    //     0x15e2d38: mov             SP, fp
    //     0x15e2d3c: ldp             fp, lr, [SP], #0x10
    // 0x15e2d40: ret
    //     0x15e2d40: ret             
    // 0x15e2d44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e2d44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e2d48: b               #0x15e2c98
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e2d4c, size: 0xb4
    // 0x15e2d4c: EnterFrame
    //     0x15e2d4c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e2d50: mov             fp, SP
    // 0x15e2d54: AllocStack(0x18)
    //     0x15e2d54: sub             SP, SP, #0x18
    // 0x15e2d58: SetupParameters()
    //     0x15e2d58: ldr             x0, [fp, #0x10]
    //     0x15e2d5c: ldur            w3, [x0, #0x17]
    //     0x15e2d60: add             x3, x3, HEAP, lsl #32
    //     0x15e2d64: stur            x3, [fp, #-8]
    // 0x15e2d68: CheckStackOverflow
    //     0x15e2d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e2d6c: cmp             SP, x16
    //     0x15e2d70: b.ls            #0x15e2df8
    // 0x15e2d74: LoadField: r1 = r3->field_f
    //     0x15e2d74: ldur            w1, [x3, #0xf]
    // 0x15e2d78: DecompressPointer r1
    //     0x15e2d78: add             x1, x1, HEAP, lsl #32
    // 0x15e2d7c: r2 = false
    //     0x15e2d7c: add             x2, NULL, #0x30  ; false
    // 0x15e2d80: r0 = showLoading()
    //     0x15e2d80: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e2d84: ldur            x0, [fp, #-8]
    // 0x15e2d88: LoadField: r1 = r0->field_f
    //     0x15e2d88: ldur            w1, [x0, #0xf]
    // 0x15e2d8c: DecompressPointer r1
    //     0x15e2d8c: add             x1, x1, HEAP, lsl #32
    // 0x15e2d90: r0 = controller()
    //     0x15e2d90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e2d94: LoadField: r1 = r0->field_bf
    //     0x15e2d94: ldur            w1, [x0, #0xbf]
    // 0x15e2d98: DecompressPointer r1
    //     0x15e2d98: add             x1, x1, HEAP, lsl #32
    // 0x15e2d9c: r0 = value()
    //     0x15e2d9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e2da0: tbnz            w0, #4, #0x15e2dd8
    // 0x15e2da4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e2da4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e2da8: ldr             x0, [x0, #0x1c80]
    //     0x15e2dac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e2db0: cmp             w0, w16
    //     0x15e2db4: b.ne            #0x15e2dc0
    //     0x15e2db8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e2dbc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e2dc0: r16 = "/search"
    //     0x15e2dc0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e2dc4: ldr             x16, [x16, #0x838]
    // 0x15e2dc8: stp             x16, NULL, [SP]
    // 0x15e2dcc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e2dcc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e2dd0: r0 = GetNavigation.toNamed()
    //     0x15e2dd0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e2dd4: b               #0x15e2de8
    // 0x15e2dd8: ldur            x0, [fp, #-8]
    // 0x15e2ddc: LoadField: r1 = r0->field_f
    //     0x15e2ddc: ldur            w1, [x0, #0xf]
    // 0x15e2de0: DecompressPointer r1
    //     0x15e2de0: add             x1, x1, HEAP, lsl #32
    // 0x15e2de4: r0 = onBackPress()
    //     0x15e2de4: bl              #0x1401b54  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onBackPress
    // 0x15e2de8: r0 = Null
    //     0x15e2de8: mov             x0, NULL
    // 0x15e2dec: LeaveFrame
    //     0x15e2dec: mov             SP, fp
    //     0x15e2df0: ldp             fp, lr, [SP], #0x10
    // 0x15e2df4: ret
    //     0x15e2df4: ret             
    // 0x15e2df8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e2df8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e2dfc: b               #0x15e2d74
  }
}
