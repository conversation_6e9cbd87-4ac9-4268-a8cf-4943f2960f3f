// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/bottom_view.dart

// class id: 1049224, size: 0x8
class :: {
}

// class id: 3468, size: 0x14, field offset: 0x14
class _BottomViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xab57ac, size: 0x8b4
    // 0xab57ac: EnterFrame
    //     0xab57ac: stp             fp, lr, [SP, #-0x10]!
    //     0xab57b0: mov             fp, SP
    // 0xab57b4: AllocStack(0x60)
    //     0xab57b4: sub             SP, SP, #0x60
    // 0xab57b8: SetupParameters(_BottomViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xab57b8: mov             x0, x1
    //     0xab57bc: stur            x1, [fp, #-8]
    //     0xab57c0: mov             x1, x2
    //     0xab57c4: stur            x2, [fp, #-0x10]
    // 0xab57c8: CheckStackOverflow
    //     0xab57c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab57cc: cmp             SP, x16
    //     0xab57d0: b.ls            #0xab6048
    // 0xab57d4: r1 = 1
    //     0xab57d4: movz            x1, #0x1
    // 0xab57d8: r0 = AllocateContext()
    //     0xab57d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xab57dc: mov             x3, x0
    // 0xab57e0: ldur            x0, [fp, #-8]
    // 0xab57e4: stur            x3, [fp, #-0x18]
    // 0xab57e8: StoreField: r3->field_f = r0
    //     0xab57e8: stur            w0, [x3, #0xf]
    // 0xab57ec: r1 = Null
    //     0xab57ec: mov             x1, NULL
    // 0xab57f0: r2 = 6
    //     0xab57f0: movz            x2, #0x6
    // 0xab57f4: r0 = AllocateArray()
    //     0xab57f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab57f8: r16 = "Subtotal ("
    //     0xab57f8: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e0] "Subtotal ("
    //     0xab57fc: ldr             x16, [x16, #0x4e0]
    // 0xab5800: StoreField: r0->field_f = r16
    //     0xab5800: stur            w16, [x0, #0xf]
    // 0xab5804: ldur            x1, [fp, #-8]
    // 0xab5808: LoadField: r2 = r1->field_b
    //     0xab5808: ldur            w2, [x1, #0xb]
    // 0xab580c: DecompressPointer r2
    //     0xab580c: add             x2, x2, HEAP, lsl #32
    // 0xab5810: cmp             w2, NULL
    // 0xab5814: b.eq            #0xab6050
    // 0xab5818: LoadField: r3 = r2->field_b
    //     0xab5818: ldur            w3, [x2, #0xb]
    // 0xab581c: DecompressPointer r3
    //     0xab581c: add             x3, x3, HEAP, lsl #32
    // 0xab5820: cmp             w3, NULL
    // 0xab5824: b.ne            #0xab5830
    // 0xab5828: r2 = Null
    //     0xab5828: mov             x2, NULL
    // 0xab582c: b               #0xab5838
    // 0xab5830: LoadField: r2 = r3->field_b
    //     0xab5830: ldur            w2, [x3, #0xb]
    // 0xab5834: DecompressPointer r2
    //     0xab5834: add             x2, x2, HEAP, lsl #32
    // 0xab5838: StoreField: r0->field_13 = r2
    //     0xab5838: stur            w2, [x0, #0x13]
    // 0xab583c: r16 = " item)"
    //     0xab583c: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e8] " item)"
    //     0xab5840: ldr             x16, [x16, #0x4e8]
    // 0xab5844: ArrayStore: r0[0] = r16  ; List_4
    //     0xab5844: stur            w16, [x0, #0x17]
    // 0xab5848: str             x0, [SP]
    // 0xab584c: r0 = _interpolate()
    //     0xab584c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xab5850: ldur            x1, [fp, #-0x10]
    // 0xab5854: stur            x0, [fp, #-0x20]
    // 0xab5858: r0 = of()
    //     0xab5858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab585c: LoadField: r1 = r0->field_87
    //     0xab585c: ldur            w1, [x0, #0x87]
    // 0xab5860: DecompressPointer r1
    //     0xab5860: add             x1, x1, HEAP, lsl #32
    // 0xab5864: LoadField: r0 = r1->field_2b
    //     0xab5864: ldur            w0, [x1, #0x2b]
    // 0xab5868: DecompressPointer r0
    //     0xab5868: add             x0, x0, HEAP, lsl #32
    // 0xab586c: stur            x0, [fp, #-0x28]
    // 0xab5870: r1 = Instance_Color
    //     0xab5870: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab5874: d0 = 0.700000
    //     0xab5874: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xab5878: ldr             d0, [x17, #0xf48]
    // 0xab587c: r0 = withOpacity()
    //     0xab587c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xab5880: r16 = 16.000000
    //     0xab5880: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5884: ldr             x16, [x16, #0x188]
    // 0xab5888: stp             x0, x16, [SP]
    // 0xab588c: ldur            x1, [fp, #-0x28]
    // 0xab5890: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5890: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5894: ldr             x4, [x4, #0xaa0]
    // 0xab5898: r0 = copyWith()
    //     0xab5898: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab589c: stur            x0, [fp, #-0x28]
    // 0xab58a0: r0 = Text()
    //     0xab58a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab58a4: mov             x2, x0
    // 0xab58a8: ldur            x0, [fp, #-0x20]
    // 0xab58ac: stur            x2, [fp, #-0x30]
    // 0xab58b0: StoreField: r2->field_b = r0
    //     0xab58b0: stur            w0, [x2, #0xb]
    // 0xab58b4: ldur            x0, [fp, #-0x28]
    // 0xab58b8: StoreField: r2->field_13 = r0
    //     0xab58b8: stur            w0, [x2, #0x13]
    // 0xab58bc: ldur            x0, [fp, #-8]
    // 0xab58c0: LoadField: r1 = r0->field_b
    //     0xab58c0: ldur            w1, [x0, #0xb]
    // 0xab58c4: DecompressPointer r1
    //     0xab58c4: add             x1, x1, HEAP, lsl #32
    // 0xab58c8: cmp             w1, NULL
    // 0xab58cc: b.eq            #0xab6054
    // 0xab58d0: LoadField: r3 = r1->field_b
    //     0xab58d0: ldur            w3, [x1, #0xb]
    // 0xab58d4: DecompressPointer r3
    //     0xab58d4: add             x3, x3, HEAP, lsl #32
    // 0xab58d8: cmp             w3, NULL
    // 0xab58dc: b.ne            #0xab58e8
    // 0xab58e0: r1 = Null
    //     0xab58e0: mov             x1, NULL
    // 0xab58e4: b               #0xab58f0
    // 0xab58e8: LoadField: r1 = r3->field_13
    //     0xab58e8: ldur            w1, [x3, #0x13]
    // 0xab58ec: DecompressPointer r1
    //     0xab58ec: add             x1, x1, HEAP, lsl #32
    // 0xab58f0: cmp             w1, NULL
    // 0xab58f4: b.ne            #0xab5900
    // 0xab58f8: r3 = ""
    //     0xab58f8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab58fc: b               #0xab5904
    // 0xab5900: mov             x3, x1
    // 0xab5904: ldur            x1, [fp, #-0x10]
    // 0xab5908: stur            x3, [fp, #-0x20]
    // 0xab590c: r0 = of()
    //     0xab590c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5910: LoadField: r1 = r0->field_87
    //     0xab5910: ldur            w1, [x0, #0x87]
    // 0xab5914: DecompressPointer r1
    //     0xab5914: add             x1, x1, HEAP, lsl #32
    // 0xab5918: LoadField: r0 = r1->field_7
    //     0xab5918: ldur            w0, [x1, #7]
    // 0xab591c: DecompressPointer r0
    //     0xab591c: add             x0, x0, HEAP, lsl #32
    // 0xab5920: r16 = Instance_Color
    //     0xab5920: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab5924: r30 = 16.000000
    //     0xab5924: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5928: ldr             lr, [lr, #0x188]
    // 0xab592c: stp             lr, x16, [SP]
    // 0xab5930: mov             x1, x0
    // 0xab5934: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab5934: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab5938: ldr             x4, [x4, #0x9b8]
    // 0xab593c: r0 = copyWith()
    //     0xab593c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5940: stur            x0, [fp, #-0x28]
    // 0xab5944: r0 = Text()
    //     0xab5944: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab5948: mov             x3, x0
    // 0xab594c: ldur            x0, [fp, #-0x20]
    // 0xab5950: stur            x3, [fp, #-0x38]
    // 0xab5954: StoreField: r3->field_b = r0
    //     0xab5954: stur            w0, [x3, #0xb]
    // 0xab5958: ldur            x0, [fp, #-0x28]
    // 0xab595c: StoreField: r3->field_13 = r0
    //     0xab595c: stur            w0, [x3, #0x13]
    // 0xab5960: r1 = Null
    //     0xab5960: mov             x1, NULL
    // 0xab5964: r2 = 4
    //     0xab5964: movz            x2, #0x4
    // 0xab5968: r0 = AllocateArray()
    //     0xab5968: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab596c: mov             x2, x0
    // 0xab5970: ldur            x0, [fp, #-0x30]
    // 0xab5974: stur            x2, [fp, #-0x20]
    // 0xab5978: StoreField: r2->field_f = r0
    //     0xab5978: stur            w0, [x2, #0xf]
    // 0xab597c: ldur            x0, [fp, #-0x38]
    // 0xab5980: StoreField: r2->field_13 = r0
    //     0xab5980: stur            w0, [x2, #0x13]
    // 0xab5984: r1 = <Widget>
    //     0xab5984: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab5988: r0 = AllocateGrowableArray()
    //     0xab5988: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab598c: mov             x1, x0
    // 0xab5990: ldur            x0, [fp, #-0x20]
    // 0xab5994: stur            x1, [fp, #-0x28]
    // 0xab5998: StoreField: r1->field_f = r0
    //     0xab5998: stur            w0, [x1, #0xf]
    // 0xab599c: r0 = 4
    //     0xab599c: movz            x0, #0x4
    // 0xab59a0: StoreField: r1->field_b = r0
    //     0xab59a0: stur            w0, [x1, #0xb]
    // 0xab59a4: r0 = Row()
    //     0xab59a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab59a8: mov             x1, x0
    // 0xab59ac: r0 = Instance_Axis
    //     0xab59ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab59b0: stur            x1, [fp, #-0x30]
    // 0xab59b4: StoreField: r1->field_f = r0
    //     0xab59b4: stur            w0, [x1, #0xf]
    // 0xab59b8: r0 = Instance_MainAxisAlignment
    //     0xab59b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xab59bc: ldr             x0, [x0, #0xa8]
    // 0xab59c0: StoreField: r1->field_13 = r0
    //     0xab59c0: stur            w0, [x1, #0x13]
    // 0xab59c4: r2 = Instance_MainAxisSize
    //     0xab59c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab59c8: ldr             x2, [x2, #0xa10]
    // 0xab59cc: ArrayStore: r1[0] = r2  ; List_4
    //     0xab59cc: stur            w2, [x1, #0x17]
    // 0xab59d0: r0 = Instance_CrossAxisAlignment
    //     0xab59d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab59d4: ldr             x0, [x0, #0xa18]
    // 0xab59d8: StoreField: r1->field_1b = r0
    //     0xab59d8: stur            w0, [x1, #0x1b]
    // 0xab59dc: r3 = Instance_VerticalDirection
    //     0xab59dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab59e0: ldr             x3, [x3, #0xa20]
    // 0xab59e4: StoreField: r1->field_23 = r3
    //     0xab59e4: stur            w3, [x1, #0x23]
    // 0xab59e8: r4 = Instance_Clip
    //     0xab59e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab59ec: ldr             x4, [x4, #0x38]
    // 0xab59f0: StoreField: r1->field_2b = r4
    //     0xab59f0: stur            w4, [x1, #0x2b]
    // 0xab59f4: StoreField: r1->field_2f = rZR
    //     0xab59f4: stur            xzr, [x1, #0x2f]
    // 0xab59f8: ldur            x0, [fp, #-0x28]
    // 0xab59fc: StoreField: r1->field_b = r0
    //     0xab59fc: stur            w0, [x1, #0xb]
    // 0xab5a00: ldur            x5, [fp, #-8]
    // 0xab5a04: LoadField: r0 = r5->field_b
    //     0xab5a04: ldur            w0, [x5, #0xb]
    // 0xab5a08: DecompressPointer r0
    //     0xab5a08: add             x0, x0, HEAP, lsl #32
    // 0xab5a0c: cmp             w0, NULL
    // 0xab5a10: b.eq            #0xab6058
    // 0xab5a14: LoadField: r6 = r0->field_b
    //     0xab5a14: ldur            w6, [x0, #0xb]
    // 0xab5a18: DecompressPointer r6
    //     0xab5a18: add             x6, x6, HEAP, lsl #32
    // 0xab5a1c: cmp             w6, NULL
    // 0xab5a20: b.ne            #0xab5a2c
    // 0xab5a24: r0 = Null
    //     0xab5a24: mov             x0, NULL
    // 0xab5a28: b               #0xab5a58
    // 0xab5a2c: LoadField: r0 = r6->field_f
    //     0xab5a2c: ldur            w0, [x6, #0xf]
    // 0xab5a30: DecompressPointer r0
    //     0xab5a30: add             x0, x0, HEAP, lsl #32
    // 0xab5a34: cmp             w0, NULL
    // 0xab5a38: b.ne            #0xab5a44
    // 0xab5a3c: r0 = Null
    //     0xab5a3c: mov             x0, NULL
    // 0xab5a40: b               #0xab5a58
    // 0xab5a44: LoadField: r7 = r0->field_7
    //     0xab5a44: ldur            w7, [x0, #7]
    // 0xab5a48: cbnz            w7, #0xab5a54
    // 0xab5a4c: r0 = false
    //     0xab5a4c: add             x0, NULL, #0x30  ; false
    // 0xab5a50: b               #0xab5a58
    // 0xab5a54: r0 = true
    //     0xab5a54: add             x0, NULL, #0x20  ; true
    // 0xab5a58: cmp             w0, NULL
    // 0xab5a5c: b.ne            #0xab5a68
    // 0xab5a60: r7 = false
    //     0xab5a60: add             x7, NULL, #0x30  ; false
    // 0xab5a64: b               #0xab5a6c
    // 0xab5a68: mov             x7, x0
    // 0xab5a6c: stur            x7, [fp, #-0x20]
    // 0xab5a70: cmp             w6, NULL
    // 0xab5a74: b.ne            #0xab5a80
    // 0xab5a78: r0 = Null
    //     0xab5a78: mov             x0, NULL
    // 0xab5a7c: b               #0xab5aac
    // 0xab5a80: LoadField: r0 = r6->field_f
    //     0xab5a80: ldur            w0, [x6, #0xf]
    // 0xab5a84: DecompressPointer r0
    //     0xab5a84: add             x0, x0, HEAP, lsl #32
    // 0xab5a88: r6 = LoadClassIdInstr(r0)
    //     0xab5a88: ldur            x6, [x0, #-1]
    //     0xab5a8c: ubfx            x6, x6, #0xc, #0x14
    // 0xab5a90: str             x0, [SP]
    // 0xab5a94: mov             x0, x6
    // 0xab5a98: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xab5a98: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xab5a9c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xab5a9c: movz            x17, #0x2700
    //     0xab5aa0: add             lr, x0, x17
    //     0xab5aa4: ldr             lr, [x21, lr, lsl #3]
    //     0xab5aa8: blr             lr
    // 0xab5aac: cmp             w0, NULL
    // 0xab5ab0: b.ne            #0xab5abc
    // 0xab5ab4: r3 = ""
    //     0xab5ab4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab5ab8: b               #0xab5ac0
    // 0xab5abc: mov             x3, x0
    // 0xab5ac0: ldur            x0, [fp, #-8]
    // 0xab5ac4: ldur            x2, [fp, #-0x20]
    // 0xab5ac8: ldur            x1, [fp, #-0x10]
    // 0xab5acc: stur            x3, [fp, #-0x28]
    // 0xab5ad0: r0 = of()
    //     0xab5ad0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5ad4: LoadField: r1 = r0->field_87
    //     0xab5ad4: ldur            w1, [x0, #0x87]
    // 0xab5ad8: DecompressPointer r1
    //     0xab5ad8: add             x1, x1, HEAP, lsl #32
    // 0xab5adc: LoadField: r0 = r1->field_7
    //     0xab5adc: ldur            w0, [x1, #7]
    // 0xab5ae0: DecompressPointer r0
    //     0xab5ae0: add             x0, x0, HEAP, lsl #32
    // 0xab5ae4: r16 = Instance_Color
    //     0xab5ae4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xab5ae8: ldr             x16, [x16, #0x858]
    // 0xab5aec: r30 = 16.000000
    //     0xab5aec: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5af0: ldr             lr, [lr, #0x188]
    // 0xab5af4: stp             lr, x16, [SP]
    // 0xab5af8: mov             x1, x0
    // 0xab5afc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab5afc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab5b00: ldr             x4, [x4, #0x9b8]
    // 0xab5b04: r0 = copyWith()
    //     0xab5b04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5b08: stur            x0, [fp, #-0x38]
    // 0xab5b0c: r0 = Text()
    //     0xab5b0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab5b10: mov             x1, x0
    // 0xab5b14: ldur            x0, [fp, #-0x28]
    // 0xab5b18: stur            x1, [fp, #-0x40]
    // 0xab5b1c: StoreField: r1->field_b = r0
    //     0xab5b1c: stur            w0, [x1, #0xb]
    // 0xab5b20: ldur            x0, [fp, #-0x38]
    // 0xab5b24: StoreField: r1->field_13 = r0
    //     0xab5b24: stur            w0, [x1, #0x13]
    // 0xab5b28: r0 = Padding()
    //     0xab5b28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab5b2c: mov             x1, x0
    // 0xab5b30: r0 = Instance_EdgeInsets
    //     0xab5b30: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xab5b34: ldr             x0, [x0, #0x770]
    // 0xab5b38: stur            x1, [fp, #-0x28]
    // 0xab5b3c: StoreField: r1->field_f = r0
    //     0xab5b3c: stur            w0, [x1, #0xf]
    // 0xab5b40: ldur            x0, [fp, #-0x40]
    // 0xab5b44: StoreField: r1->field_b = r0
    //     0xab5b44: stur            w0, [x1, #0xb]
    // 0xab5b48: r0 = Visibility()
    //     0xab5b48: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xab5b4c: mov             x1, x0
    // 0xab5b50: ldur            x0, [fp, #-0x28]
    // 0xab5b54: stur            x1, [fp, #-0x38]
    // 0xab5b58: StoreField: r1->field_b = r0
    //     0xab5b58: stur            w0, [x1, #0xb]
    // 0xab5b5c: r0 = Instance_SizedBox
    //     0xab5b5c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xab5b60: StoreField: r1->field_f = r0
    //     0xab5b60: stur            w0, [x1, #0xf]
    // 0xab5b64: ldur            x0, [fp, #-0x20]
    // 0xab5b68: StoreField: r1->field_13 = r0
    //     0xab5b68: stur            w0, [x1, #0x13]
    // 0xab5b6c: r0 = false
    //     0xab5b6c: add             x0, NULL, #0x30  ; false
    // 0xab5b70: ArrayStore: r1[0] = r0  ; List_4
    //     0xab5b70: stur            w0, [x1, #0x17]
    // 0xab5b74: StoreField: r1->field_1b = r0
    //     0xab5b74: stur            w0, [x1, #0x1b]
    // 0xab5b78: StoreField: r1->field_1f = r0
    //     0xab5b78: stur            w0, [x1, #0x1f]
    // 0xab5b7c: StoreField: r1->field_23 = r0
    //     0xab5b7c: stur            w0, [x1, #0x23]
    // 0xab5b80: StoreField: r1->field_27 = r0
    //     0xab5b80: stur            w0, [x1, #0x27]
    // 0xab5b84: StoreField: r1->field_2b = r0
    //     0xab5b84: stur            w0, [x1, #0x2b]
    // 0xab5b88: r16 = <EdgeInsets>
    //     0xab5b88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xab5b8c: ldr             x16, [x16, #0xda0]
    // 0xab5b90: r30 = Instance_EdgeInsets
    //     0xab5b90: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xab5b94: ldr             lr, [lr, #0x1f0]
    // 0xab5b98: stp             lr, x16, [SP]
    // 0xab5b9c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab5b9c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab5ba0: r0 = all()
    //     0xab5ba0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab5ba4: ldur            x1, [fp, #-0x10]
    // 0xab5ba8: stur            x0, [fp, #-0x20]
    // 0xab5bac: r0 = of()
    //     0xab5bac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5bb0: LoadField: r1 = r0->field_5b
    //     0xab5bb0: ldur            w1, [x0, #0x5b]
    // 0xab5bb4: DecompressPointer r1
    //     0xab5bb4: add             x1, x1, HEAP, lsl #32
    // 0xab5bb8: r16 = <Color>
    //     0xab5bb8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xab5bbc: ldr             x16, [x16, #0xf80]
    // 0xab5bc0: stp             x1, x16, [SP]
    // 0xab5bc4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab5bc4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab5bc8: r0 = all()
    //     0xab5bc8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab5bcc: stur            x0, [fp, #-0x28]
    // 0xab5bd0: r0 = Radius()
    //     0xab5bd0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xab5bd4: d0 = 20.000000
    //     0xab5bd4: fmov            d0, #20.00000000
    // 0xab5bd8: stur            x0, [fp, #-0x40]
    // 0xab5bdc: StoreField: r0->field_7 = d0
    //     0xab5bdc: stur            d0, [x0, #7]
    // 0xab5be0: StoreField: r0->field_f = d0
    //     0xab5be0: stur            d0, [x0, #0xf]
    // 0xab5be4: r0 = BorderRadius()
    //     0xab5be4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xab5be8: mov             x1, x0
    // 0xab5bec: ldur            x0, [fp, #-0x40]
    // 0xab5bf0: stur            x1, [fp, #-0x48]
    // 0xab5bf4: StoreField: r1->field_7 = r0
    //     0xab5bf4: stur            w0, [x1, #7]
    // 0xab5bf8: StoreField: r1->field_b = r0
    //     0xab5bf8: stur            w0, [x1, #0xb]
    // 0xab5bfc: StoreField: r1->field_f = r0
    //     0xab5bfc: stur            w0, [x1, #0xf]
    // 0xab5c00: StoreField: r1->field_13 = r0
    //     0xab5c00: stur            w0, [x1, #0x13]
    // 0xab5c04: r0 = RoundedRectangleBorder()
    //     0xab5c04: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xab5c08: mov             x1, x0
    // 0xab5c0c: ldur            x0, [fp, #-0x48]
    // 0xab5c10: StoreField: r1->field_b = r0
    //     0xab5c10: stur            w0, [x1, #0xb]
    // 0xab5c14: r0 = Instance_BorderSide
    //     0xab5c14: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xab5c18: ldr             x0, [x0, #0xe20]
    // 0xab5c1c: StoreField: r1->field_7 = r0
    //     0xab5c1c: stur            w0, [x1, #7]
    // 0xab5c20: r16 = <RoundedRectangleBorder>
    //     0xab5c20: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xab5c24: ldr             x16, [x16, #0xf78]
    // 0xab5c28: stp             x1, x16, [SP]
    // 0xab5c2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab5c2c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab5c30: r0 = all()
    //     0xab5c30: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab5c34: stur            x0, [fp, #-0x40]
    // 0xab5c38: r0 = ButtonStyle()
    //     0xab5c38: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xab5c3c: mov             x1, x0
    // 0xab5c40: ldur            x0, [fp, #-0x28]
    // 0xab5c44: stur            x1, [fp, #-0x48]
    // 0xab5c48: StoreField: r1->field_b = r0
    //     0xab5c48: stur            w0, [x1, #0xb]
    // 0xab5c4c: ldur            x0, [fp, #-0x20]
    // 0xab5c50: StoreField: r1->field_23 = r0
    //     0xab5c50: stur            w0, [x1, #0x23]
    // 0xab5c54: ldur            x0, [fp, #-0x40]
    // 0xab5c58: StoreField: r1->field_43 = r0
    //     0xab5c58: stur            w0, [x1, #0x43]
    // 0xab5c5c: r0 = TextButtonThemeData()
    //     0xab5c5c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xab5c60: mov             x3, x0
    // 0xab5c64: ldur            x0, [fp, #-0x48]
    // 0xab5c68: stur            x3, [fp, #-0x28]
    // 0xab5c6c: StoreField: r3->field_7 = r0
    //     0xab5c6c: stur            w0, [x3, #7]
    // 0xab5c70: ldur            x0, [fp, #-8]
    // 0xab5c74: LoadField: r4 = r0->field_b
    //     0xab5c74: ldur            w4, [x0, #0xb]
    // 0xab5c78: DecompressPointer r4
    //     0xab5c78: add             x4, x4, HEAP, lsl #32
    // 0xab5c7c: stur            x4, [fp, #-0x20]
    // 0xab5c80: cmp             w4, NULL
    // 0xab5c84: b.eq            #0xab605c
    // 0xab5c88: LoadField: r0 = r4->field_13
    //     0xab5c88: ldur            w0, [x4, #0x13]
    // 0xab5c8c: DecompressPointer r0
    //     0xab5c8c: add             x0, x0, HEAP, lsl #32
    // 0xab5c90: tbz             w0, #4, #0xab5cac
    // 0xab5c94: ldur            x2, [fp, #-0x18]
    // 0xab5c98: r1 = Function '<anonymous closure>':.
    //     0xab5c98: add             x1, PP, #0x59, lsl #12  ; [pp+0x593f8] AnonymousClosure: (0xab6084), in [package:customer_app/app/presentation/views/cosmetic/bag/bottom_view.dart] _BottomViewState::build (0xab57ac)
    //     0xab5c9c: ldr             x1, [x1, #0x3f8]
    // 0xab5ca0: r0 = AllocateClosure()
    //     0xab5ca0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab5ca4: mov             x2, x0
    // 0xab5ca8: b               #0xab5cb0
    // 0xab5cac: r2 = Null
    //     0xab5cac: mov             x2, NULL
    // 0xab5cb0: ldur            x0, [fp, #-0x20]
    // 0xab5cb4: stur            x2, [fp, #-8]
    // 0xab5cb8: LoadField: r1 = r0->field_b
    //     0xab5cb8: ldur            w1, [x0, #0xb]
    // 0xab5cbc: DecompressPointer r1
    //     0xab5cbc: add             x1, x1, HEAP, lsl #32
    // 0xab5cc0: cmp             w1, NULL
    // 0xab5cc4: b.eq            #0xab5cd4
    // 0xab5cc8: LoadField: r3 = r1->field_b
    //     0xab5cc8: ldur            w3, [x1, #0xb]
    // 0xab5ccc: DecompressPointer r3
    //     0xab5ccc: add             x3, x3, HEAP, lsl #32
    // 0xab5cd0: cbz             w3, #0xab5d4c
    // 0xab5cd4: LoadField: r1 = r0->field_f
    //     0xab5cd4: ldur            w1, [x0, #0xf]
    // 0xab5cd8: DecompressPointer r1
    //     0xab5cd8: add             x1, x1, HEAP, lsl #32
    // 0xab5cdc: tbz             w1, #4, #0xab5d4c
    // 0xab5ce0: r1 = "checkout"
    //     0xab5ce0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c4e8] "checkout"
    //     0xab5ce4: ldr             x1, [x1, #0x4e8]
    // 0xab5ce8: r0 = capitalizeFirstWord()
    //     0xab5ce8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab5cec: ldur            x1, [fp, #-0x10]
    // 0xab5cf0: stur            x0, [fp, #-0x18]
    // 0xab5cf4: r0 = of()
    //     0xab5cf4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5cf8: LoadField: r1 = r0->field_87
    //     0xab5cf8: ldur            w1, [x0, #0x87]
    // 0xab5cfc: DecompressPointer r1
    //     0xab5cfc: add             x1, x1, HEAP, lsl #32
    // 0xab5d00: LoadField: r0 = r1->field_7
    //     0xab5d00: ldur            w0, [x1, #7]
    // 0xab5d04: DecompressPointer r0
    //     0xab5d04: add             x0, x0, HEAP, lsl #32
    // 0xab5d08: r16 = 16.000000
    //     0xab5d08: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5d0c: ldr             x16, [x16, #0x188]
    // 0xab5d10: r30 = Instance_Color
    //     0xab5d10: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab5d14: stp             lr, x16, [SP]
    // 0xab5d18: mov             x1, x0
    // 0xab5d1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5d1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5d20: ldr             x4, [x4, #0xaa0]
    // 0xab5d24: r0 = copyWith()
    //     0xab5d24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5d28: stur            x0, [fp, #-0x40]
    // 0xab5d2c: r0 = Text()
    //     0xab5d2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab5d30: mov             x1, x0
    // 0xab5d34: ldur            x0, [fp, #-0x18]
    // 0xab5d38: StoreField: r1->field_b = r0
    //     0xab5d38: stur            w0, [x1, #0xb]
    // 0xab5d3c: ldur            x0, [fp, #-0x40]
    // 0xab5d40: StoreField: r1->field_13 = r0
    //     0xab5d40: stur            w0, [x1, #0x13]
    // 0xab5d44: mov             x4, x1
    // 0xab5d48: b               #0xab5ec0
    // 0xab5d4c: LoadField: r1 = r0->field_f
    //     0xab5d4c: ldur            w1, [x0, #0xf]
    // 0xab5d50: DecompressPointer r1
    //     0xab5d50: add             x1, x1, HEAP, lsl #32
    // 0xab5d54: tbnz            w1, #4, #0xab5dd4
    // 0xab5d58: LoadField: r2 = r0->field_1b
    //     0xab5d58: ldur            w2, [x0, #0x1b]
    // 0xab5d5c: DecompressPointer r2
    //     0xab5d5c: add             x2, x2, HEAP, lsl #32
    // 0xab5d60: LoadField: r3 = r2->field_7
    //     0xab5d60: ldur            w3, [x2, #7]
    // 0xab5d64: cbnz            w3, #0xab5dd4
    // 0xab5d68: r1 = "checkout with in-stock product"
    //     0xab5d68: add             x1, PP, #0x57, lsl #12  ; [pp+0x57200] "checkout with in-stock product"
    //     0xab5d6c: ldr             x1, [x1, #0x200]
    // 0xab5d70: r0 = capitalizeFirstWord()
    //     0xab5d70: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab5d74: ldur            x1, [fp, #-0x10]
    // 0xab5d78: stur            x0, [fp, #-0x18]
    // 0xab5d7c: r0 = of()
    //     0xab5d7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5d80: LoadField: r1 = r0->field_87
    //     0xab5d80: ldur            w1, [x0, #0x87]
    // 0xab5d84: DecompressPointer r1
    //     0xab5d84: add             x1, x1, HEAP, lsl #32
    // 0xab5d88: LoadField: r0 = r1->field_7
    //     0xab5d88: ldur            w0, [x1, #7]
    // 0xab5d8c: DecompressPointer r0
    //     0xab5d8c: add             x0, x0, HEAP, lsl #32
    // 0xab5d90: r16 = 16.000000
    //     0xab5d90: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5d94: ldr             x16, [x16, #0x188]
    // 0xab5d98: r30 = Instance_Color
    //     0xab5d98: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab5d9c: stp             lr, x16, [SP]
    // 0xab5da0: mov             x1, x0
    // 0xab5da4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5da4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5da8: ldr             x4, [x4, #0xaa0]
    // 0xab5dac: r0 = copyWith()
    //     0xab5dac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5db0: stur            x0, [fp, #-0x40]
    // 0xab5db4: r0 = Text()
    //     0xab5db4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab5db8: mov             x1, x0
    // 0xab5dbc: ldur            x0, [fp, #-0x18]
    // 0xab5dc0: StoreField: r1->field_b = r0
    //     0xab5dc0: stur            w0, [x1, #0xb]
    // 0xab5dc4: ldur            x0, [fp, #-0x40]
    // 0xab5dc8: StoreField: r1->field_13 = r0
    //     0xab5dc8: stur            w0, [x1, #0x13]
    // 0xab5dcc: mov             x0, x1
    // 0xab5dd0: b               #0xab5ebc
    // 0xab5dd4: tbnz            w1, #4, #0xab5e54
    // 0xab5dd8: LoadField: r1 = r0->field_1b
    //     0xab5dd8: ldur            w1, [x0, #0x1b]
    // 0xab5ddc: DecompressPointer r1
    //     0xab5ddc: add             x1, x1, HEAP, lsl #32
    // 0xab5de0: LoadField: r0 = r1->field_7
    //     0xab5de0: ldur            w0, [x1, #7]
    // 0xab5de4: cbz             w0, #0xab5e54
    // 0xab5de8: r1 = "checkout"
    //     0xab5de8: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c4e8] "checkout"
    //     0xab5dec: ldr             x1, [x1, #0x4e8]
    // 0xab5df0: r0 = capitalizeFirstWord()
    //     0xab5df0: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab5df4: ldur            x1, [fp, #-0x10]
    // 0xab5df8: stur            x0, [fp, #-0x18]
    // 0xab5dfc: r0 = of()
    //     0xab5dfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5e00: LoadField: r1 = r0->field_87
    //     0xab5e00: ldur            w1, [x0, #0x87]
    // 0xab5e04: DecompressPointer r1
    //     0xab5e04: add             x1, x1, HEAP, lsl #32
    // 0xab5e08: LoadField: r0 = r1->field_7
    //     0xab5e08: ldur            w0, [x1, #7]
    // 0xab5e0c: DecompressPointer r0
    //     0xab5e0c: add             x0, x0, HEAP, lsl #32
    // 0xab5e10: r16 = 16.000000
    //     0xab5e10: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5e14: ldr             x16, [x16, #0x188]
    // 0xab5e18: r30 = Instance_Color
    //     0xab5e18: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab5e1c: stp             lr, x16, [SP]
    // 0xab5e20: mov             x1, x0
    // 0xab5e24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5e24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5e28: ldr             x4, [x4, #0xaa0]
    // 0xab5e2c: r0 = copyWith()
    //     0xab5e2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5e30: stur            x0, [fp, #-0x20]
    // 0xab5e34: r0 = Text()
    //     0xab5e34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab5e38: mov             x1, x0
    // 0xab5e3c: ldur            x0, [fp, #-0x18]
    // 0xab5e40: StoreField: r1->field_b = r0
    //     0xab5e40: stur            w0, [x1, #0xb]
    // 0xab5e44: ldur            x0, [fp, #-0x20]
    // 0xab5e48: StoreField: r1->field_13 = r0
    //     0xab5e48: stur            w0, [x1, #0x13]
    // 0xab5e4c: mov             x0, x1
    // 0xab5e50: b               #0xab5ebc
    // 0xab5e54: r1 = "clear bag"
    //     0xab5e54: add             x1, PP, #0x57, lsl #12  ; [pp+0x57208] "clear bag"
    //     0xab5e58: ldr             x1, [x1, #0x208]
    // 0xab5e5c: r0 = capitalizeFirstWord()
    //     0xab5e5c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab5e60: ldur            x1, [fp, #-0x10]
    // 0xab5e64: stur            x0, [fp, #-0x10]
    // 0xab5e68: r0 = of()
    //     0xab5e68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab5e6c: LoadField: r1 = r0->field_87
    //     0xab5e6c: ldur            w1, [x0, #0x87]
    // 0xab5e70: DecompressPointer r1
    //     0xab5e70: add             x1, x1, HEAP, lsl #32
    // 0xab5e74: LoadField: r0 = r1->field_7
    //     0xab5e74: ldur            w0, [x1, #7]
    // 0xab5e78: DecompressPointer r0
    //     0xab5e78: add             x0, x0, HEAP, lsl #32
    // 0xab5e7c: r16 = 16.000000
    //     0xab5e7c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab5e80: ldr             x16, [x16, #0x188]
    // 0xab5e84: r30 = Instance_Color
    //     0xab5e84: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab5e88: stp             lr, x16, [SP]
    // 0xab5e8c: mov             x1, x0
    // 0xab5e90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab5e90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab5e94: ldr             x4, [x4, #0xaa0]
    // 0xab5e98: r0 = copyWith()
    //     0xab5e98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab5e9c: stur            x0, [fp, #-0x18]
    // 0xab5ea0: r0 = Text()
    //     0xab5ea0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab5ea4: mov             x1, x0
    // 0xab5ea8: ldur            x0, [fp, #-0x10]
    // 0xab5eac: StoreField: r1->field_b = r0
    //     0xab5eac: stur            w0, [x1, #0xb]
    // 0xab5eb0: ldur            x0, [fp, #-0x18]
    // 0xab5eb4: StoreField: r1->field_13 = r0
    //     0xab5eb4: stur            w0, [x1, #0x13]
    // 0xab5eb8: mov             x0, x1
    // 0xab5ebc: mov             x4, x0
    // 0xab5ec0: ldur            x3, [fp, #-0x30]
    // 0xab5ec4: ldur            x2, [fp, #-0x38]
    // 0xab5ec8: ldur            x1, [fp, #-0x28]
    // 0xab5ecc: ldur            x0, [fp, #-8]
    // 0xab5ed0: stur            x4, [fp, #-0x10]
    // 0xab5ed4: r0 = TextButton()
    //     0xab5ed4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xab5ed8: mov             x1, x0
    // 0xab5edc: ldur            x0, [fp, #-8]
    // 0xab5ee0: stur            x1, [fp, #-0x18]
    // 0xab5ee4: StoreField: r1->field_b = r0
    //     0xab5ee4: stur            w0, [x1, #0xb]
    // 0xab5ee8: r0 = false
    //     0xab5ee8: add             x0, NULL, #0x30  ; false
    // 0xab5eec: StoreField: r1->field_27 = r0
    //     0xab5eec: stur            w0, [x1, #0x27]
    // 0xab5ef0: r0 = true
    //     0xab5ef0: add             x0, NULL, #0x20  ; true
    // 0xab5ef4: StoreField: r1->field_2f = r0
    //     0xab5ef4: stur            w0, [x1, #0x2f]
    // 0xab5ef8: ldur            x0, [fp, #-0x10]
    // 0xab5efc: StoreField: r1->field_37 = r0
    //     0xab5efc: stur            w0, [x1, #0x37]
    // 0xab5f00: r0 = TextButtonTheme()
    //     0xab5f00: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xab5f04: mov             x1, x0
    // 0xab5f08: ldur            x0, [fp, #-0x28]
    // 0xab5f0c: stur            x1, [fp, #-8]
    // 0xab5f10: StoreField: r1->field_f = r0
    //     0xab5f10: stur            w0, [x1, #0xf]
    // 0xab5f14: ldur            x0, [fp, #-0x18]
    // 0xab5f18: StoreField: r1->field_b = r0
    //     0xab5f18: stur            w0, [x1, #0xb]
    // 0xab5f1c: r0 = SizedBox()
    //     0xab5f1c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xab5f20: mov             x3, x0
    // 0xab5f24: r0 = inf
    //     0xab5f24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xab5f28: ldr             x0, [x0, #0x9f8]
    // 0xab5f2c: stur            x3, [fp, #-0x10]
    // 0xab5f30: StoreField: r3->field_f = r0
    //     0xab5f30: stur            w0, [x3, #0xf]
    // 0xab5f34: ldur            x0, [fp, #-8]
    // 0xab5f38: StoreField: r3->field_b = r0
    //     0xab5f38: stur            w0, [x3, #0xb]
    // 0xab5f3c: r1 = Null
    //     0xab5f3c: mov             x1, NULL
    // 0xab5f40: r2 = 6
    //     0xab5f40: movz            x2, #0x6
    // 0xab5f44: r0 = AllocateArray()
    //     0xab5f44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab5f48: mov             x2, x0
    // 0xab5f4c: ldur            x0, [fp, #-0x30]
    // 0xab5f50: stur            x2, [fp, #-8]
    // 0xab5f54: StoreField: r2->field_f = r0
    //     0xab5f54: stur            w0, [x2, #0xf]
    // 0xab5f58: ldur            x0, [fp, #-0x38]
    // 0xab5f5c: StoreField: r2->field_13 = r0
    //     0xab5f5c: stur            w0, [x2, #0x13]
    // 0xab5f60: ldur            x0, [fp, #-0x10]
    // 0xab5f64: ArrayStore: r2[0] = r0  ; List_4
    //     0xab5f64: stur            w0, [x2, #0x17]
    // 0xab5f68: r1 = <Widget>
    //     0xab5f68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab5f6c: r0 = AllocateGrowableArray()
    //     0xab5f6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab5f70: mov             x1, x0
    // 0xab5f74: ldur            x0, [fp, #-8]
    // 0xab5f78: stur            x1, [fp, #-0x10]
    // 0xab5f7c: StoreField: r1->field_f = r0
    //     0xab5f7c: stur            w0, [x1, #0xf]
    // 0xab5f80: r0 = 6
    //     0xab5f80: movz            x0, #0x6
    // 0xab5f84: StoreField: r1->field_b = r0
    //     0xab5f84: stur            w0, [x1, #0xb]
    // 0xab5f88: r0 = Column()
    //     0xab5f88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xab5f8c: mov             x1, x0
    // 0xab5f90: r0 = Instance_Axis
    //     0xab5f90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xab5f94: stur            x1, [fp, #-8]
    // 0xab5f98: StoreField: r1->field_f = r0
    //     0xab5f98: stur            w0, [x1, #0xf]
    // 0xab5f9c: r0 = Instance_MainAxisAlignment
    //     0xab5f9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab5fa0: ldr             x0, [x0, #0xa08]
    // 0xab5fa4: StoreField: r1->field_13 = r0
    //     0xab5fa4: stur            w0, [x1, #0x13]
    // 0xab5fa8: r0 = Instance_MainAxisSize
    //     0xab5fa8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab5fac: ldr             x0, [x0, #0xa10]
    // 0xab5fb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xab5fb0: stur            w0, [x1, #0x17]
    // 0xab5fb4: r0 = Instance_CrossAxisAlignment
    //     0xab5fb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xab5fb8: ldr             x0, [x0, #0x890]
    // 0xab5fbc: StoreField: r1->field_1b = r0
    //     0xab5fbc: stur            w0, [x1, #0x1b]
    // 0xab5fc0: r0 = Instance_VerticalDirection
    //     0xab5fc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab5fc4: ldr             x0, [x0, #0xa20]
    // 0xab5fc8: StoreField: r1->field_23 = r0
    //     0xab5fc8: stur            w0, [x1, #0x23]
    // 0xab5fcc: r0 = Instance_Clip
    //     0xab5fcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab5fd0: ldr             x0, [x0, #0x38]
    // 0xab5fd4: StoreField: r1->field_2b = r0
    //     0xab5fd4: stur            w0, [x1, #0x2b]
    // 0xab5fd8: StoreField: r1->field_2f = rZR
    //     0xab5fd8: stur            xzr, [x1, #0x2f]
    // 0xab5fdc: ldur            x0, [fp, #-0x10]
    // 0xab5fe0: StoreField: r1->field_b = r0
    //     0xab5fe0: stur            w0, [x1, #0xb]
    // 0xab5fe4: r0 = Padding()
    //     0xab5fe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab5fe8: mov             x1, x0
    // 0xab5fec: r0 = Instance_EdgeInsets
    //     0xab5fec: add             x0, PP, #0x41, lsl #12  ; [pp+0x41f38] Obj!EdgeInsets@d58c71
    //     0xab5ff0: ldr             x0, [x0, #0xf38]
    // 0xab5ff4: stur            x1, [fp, #-0x10]
    // 0xab5ff8: StoreField: r1->field_f = r0
    //     0xab5ff8: stur            w0, [x1, #0xf]
    // 0xab5ffc: ldur            x0, [fp, #-8]
    // 0xab6000: StoreField: r1->field_b = r0
    //     0xab6000: stur            w0, [x1, #0xb]
    // 0xab6004: r0 = Container()
    //     0xab6004: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab6008: stur            x0, [fp, #-8]
    // 0xab600c: r16 = Instance_BoxDecoration
    //     0xab600c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec50] Obj!BoxDecoration@d64921
    //     0xab6010: ldr             x16, [x16, #0xc50]
    // 0xab6014: r30 = 122.000000
    //     0xab6014: add             lr, PP, #0x59, lsl #12  ; [pp+0x59400] 122
    //     0xab6018: ldr             lr, [lr, #0x400]
    // 0xab601c: stp             lr, x16, [SP, #8]
    // 0xab6020: ldur            x16, [fp, #-0x10]
    // 0xab6024: str             x16, [SP]
    // 0xab6028: mov             x1, x0
    // 0xab602c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, height, 0x2, null]
    //     0xab602c: add             x4, PP, #0x59, lsl #12  ; [pp+0x59408] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "height", 0x2, Null]
    //     0xab6030: ldr             x4, [x4, #0x408]
    // 0xab6034: r0 = Container()
    //     0xab6034: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab6038: ldur            x0, [fp, #-8]
    // 0xab603c: LeaveFrame
    //     0xab603c: mov             SP, fp
    //     0xab6040: ldp             fp, lr, [SP], #0x10
    // 0xab6044: ret
    //     0xab6044: ret             
    // 0xab6048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6048: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab604c: b               #0xab57d4
    // 0xab6050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6050: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6054: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6058: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab605c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab605c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xab6084, size: 0xa0
    // 0xab6084: EnterFrame
    //     0xab6084: stp             fp, lr, [SP, #-0x10]!
    //     0xab6088: mov             fp, SP
    // 0xab608c: AllocStack(0x10)
    //     0xab608c: sub             SP, SP, #0x10
    // 0xab6090: SetupParameters()
    //     0xab6090: ldr             x0, [fp, #0x10]
    //     0xab6094: ldur            w1, [x0, #0x17]
    //     0xab6098: add             x1, x1, HEAP, lsl #32
    // 0xab609c: CheckStackOverflow
    //     0xab609c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab60a0: cmp             SP, x16
    //     0xab60a4: b.ls            #0xab6118
    // 0xab60a8: LoadField: r0 = r1->field_f
    //     0xab60a8: ldur            w0, [x1, #0xf]
    // 0xab60ac: DecompressPointer r0
    //     0xab60ac: add             x0, x0, HEAP, lsl #32
    // 0xab60b0: LoadField: r1 = r0->field_b
    //     0xab60b0: ldur            w1, [x0, #0xb]
    // 0xab60b4: DecompressPointer r1
    //     0xab60b4: add             x1, x1, HEAP, lsl #32
    // 0xab60b8: cmp             w1, NULL
    // 0xab60bc: b.eq            #0xab6120
    // 0xab60c0: LoadField: r0 = r1->field_b
    //     0xab60c0: ldur            w0, [x1, #0xb]
    // 0xab60c4: DecompressPointer r0
    //     0xab60c4: add             x0, x0, HEAP, lsl #32
    // 0xab60c8: cmp             w0, NULL
    // 0xab60cc: b.ne            #0xab60d8
    // 0xab60d0: r0 = Null
    //     0xab60d0: mov             x0, NULL
    // 0xab60d4: b               #0xab60e4
    // 0xab60d8: LoadField: r2 = r0->field_b
    //     0xab60d8: ldur            w2, [x0, #0xb]
    // 0xab60dc: DecompressPointer r2
    //     0xab60dc: add             x2, x2, HEAP, lsl #32
    // 0xab60e0: mov             x0, x2
    // 0xab60e4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xab60e4: ldur            w2, [x1, #0x17]
    // 0xab60e8: DecompressPointer r2
    //     0xab60e8: add             x2, x2, HEAP, lsl #32
    // 0xab60ec: stp             x0, x2, [SP]
    // 0xab60f0: r4 = 0
    //     0xab60f0: movz            x4, #0
    // 0xab60f4: ldr             x0, [SP, #8]
    // 0xab60f8: r16 = UnlinkedCall_0x613b5c
    //     0xab60f8: add             x16, PP, #0x59, lsl #12  ; [pp+0x59410] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xab60fc: add             x16, x16, #0x410
    // 0xab6100: ldp             x5, lr, [x16]
    // 0xab6104: blr             lr
    // 0xab6108: r0 = Null
    //     0xab6108: mov             x0, NULL
    // 0xab610c: LeaveFrame
    //     0xab610c: mov             SP, fp
    //     0xab6110: ldp             fp, lr, [SP], #0x10
    // 0xab6114: ret
    //     0xab6114: ret             
    // 0xab6118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6118: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab611c: b               #0xab60a8
    // 0xab6120: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6120: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4201, size: 0x20, field offset: 0xc
//   const constructor, 
class BottomView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7cf68, size: 0x24
    // 0xc7cf68: EnterFrame
    //     0xc7cf68: stp             fp, lr, [SP, #-0x10]!
    //     0xc7cf6c: mov             fp, SP
    // 0xc7cf70: mov             x0, x1
    // 0xc7cf74: r1 = <BottomView>
    //     0xc7cf74: add             x1, PP, #0x48, lsl #12  ; [pp+0x48de8] TypeArguments: <BottomView>
    //     0xc7cf78: ldr             x1, [x1, #0xde8]
    // 0xc7cf7c: r0 = _BottomViewState()
    //     0xc7cf7c: bl              #0xc7cf8c  ; Allocate_BottomViewStateStub -> _BottomViewState (size=0x14)
    // 0xc7cf80: LeaveFrame
    //     0xc7cf80: mov             SP, fp
    //     0xc7cf84: ldp             fp, lr, [SP], #0x10
    // 0xc7cf88: ret
    //     0xc7cf88: ret             
  }
}
