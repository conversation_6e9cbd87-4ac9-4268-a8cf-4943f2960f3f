// lib: , url: package:customer_app/app/presentation/custom_widgets/product_detail/sale_activity_item_view.dart

// class id: 1049085, size: 0x8
class :: {
}

// class id: 3574, size: 0x20, field offset: 0x14
class _SaleActivityItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0x9acbf8, size: 0x674
    // 0x9acbf8: EnterFrame
    //     0x9acbf8: stp             fp, lr, [SP, #-0x10]!
    //     0x9acbfc: mov             fp, SP
    // 0x9acc00: AllocStack(0x80)
    //     0x9acc00: sub             SP, SP, #0x80
    // 0x9acc04: SetupParameters(_SaleActivityItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x9acc04: mov             x0, x1
    //     0x9acc08: stur            x1, [fp, #-8]
    //     0x9acc0c: mov             x1, x2
    //     0x9acc10: stur            x2, [fp, #-0x10]
    // 0x9acc14: CheckStackOverflow
    //     0x9acc14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9acc18: cmp             SP, x16
    //     0x9acc1c: b.ls            #0x9ad220
    // 0x9acc20: r1 = 1
    //     0x9acc20: movz            x1, #0x1
    // 0x9acc24: r0 = AllocateContext()
    //     0x9acc24: bl              #0x16f6108  ; AllocateContextStub
    // 0x9acc28: mov             x3, x0
    // 0x9acc2c: ldur            x0, [fp, #-8]
    // 0x9acc30: stur            x3, [fp, #-0x28]
    // 0x9acc34: StoreField: r3->field_f = r0
    //     0x9acc34: stur            w0, [x3, #0xf]
    // 0x9acc38: LoadField: r1 = r0->field_b
    //     0x9acc38: ldur            w1, [x0, #0xb]
    // 0x9acc3c: DecompressPointer r1
    //     0x9acc3c: add             x1, x1, HEAP, lsl #32
    // 0x9acc40: cmp             w1, NULL
    // 0x9acc44: b.eq            #0x9ad228
    // 0x9acc48: LoadField: r4 = r1->field_1f
    //     0x9acc48: ldur            w4, [x1, #0x1f]
    // 0x9acc4c: DecompressPointer r4
    //     0x9acc4c: add             x4, x4, HEAP, lsl #32
    // 0x9acc50: stur            x4, [fp, #-0x20]
    // 0x9acc54: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9acc54: ldur            w2, [x1, #0x17]
    // 0x9acc58: DecompressPointer r2
    //     0x9acc58: add             x2, x2, HEAP, lsl #32
    // 0x9acc5c: LoadField: r1 = r2->field_7
    //     0x9acc5c: ldur            w1, [x2, #7]
    // 0x9acc60: DecompressPointer r1
    //     0x9acc60: add             x1, x1, HEAP, lsl #32
    // 0x9acc64: cmp             w1, NULL
    // 0x9acc68: b.ne            #0x9acc74
    // 0x9acc6c: r1 = Instance_TitleAlignment
    //     0x9acc6c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x9acc70: ldr             x1, [x1, #0x518]
    // 0x9acc74: r16 = Instance_TitleAlignment
    //     0x9acc74: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x9acc78: ldr             x16, [x16, #0x520]
    // 0x9acc7c: cmp             w1, w16
    // 0x9acc80: b.ne            #0x9acc90
    // 0x9acc84: r5 = Instance_CrossAxisAlignment
    //     0x9acc84: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x9acc88: ldr             x5, [x5, #0xc68]
    // 0x9acc8c: b               #0x9accb4
    // 0x9acc90: r16 = Instance_TitleAlignment
    //     0x9acc90: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x9acc94: ldr             x16, [x16, #0x518]
    // 0x9acc98: cmp             w1, w16
    // 0x9acc9c: b.ne            #0x9accac
    // 0x9acca0: r5 = Instance_CrossAxisAlignment
    //     0x9acca0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9acca4: ldr             x5, [x5, #0x890]
    // 0x9acca8: b               #0x9accb4
    // 0x9accac: r5 = Instance_CrossAxisAlignment
    //     0x9accac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9accb0: ldr             x5, [x5, #0xa18]
    // 0x9accb4: stur            x5, [fp, #-0x18]
    // 0x9accb8: r1 = <Widget>
    //     0x9accb8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9accbc: r2 = 0
    //     0x9accbc: movz            x2, #0
    // 0x9accc0: r0 = _GrowableList()
    //     0x9accc0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x9accc4: mov             x2, x0
    // 0x9accc8: ldur            x0, [fp, #-8]
    // 0x9acccc: stur            x2, [fp, #-0x38]
    // 0x9accd0: LoadField: r1 = r0->field_b
    //     0x9accd0: ldur            w1, [x0, #0xb]
    // 0x9accd4: DecompressPointer r1
    //     0x9accd4: add             x1, x1, HEAP, lsl #32
    // 0x9accd8: cmp             w1, NULL
    // 0x9accdc: b.eq            #0x9ad22c
    // 0x9acce0: LoadField: r3 = r1->field_b
    //     0x9acce0: ldur            w3, [x1, #0xb]
    // 0x9acce4: DecompressPointer r3
    //     0x9acce4: add             x3, x3, HEAP, lsl #32
    // 0x9acce8: LoadField: r4 = r3->field_f
    //     0x9acce8: ldur            w4, [x3, #0xf]
    // 0x9accec: DecompressPointer r4
    //     0x9accec: add             x4, x4, HEAP, lsl #32
    // 0x9accf0: cmp             w4, NULL
    // 0x9accf4: b.ne            #0x9acd00
    // 0x9accf8: r3 = Null
    //     0x9accf8: mov             x3, NULL
    // 0x9accfc: b               #0x9acd18
    // 0x9acd00: LoadField: r3 = r4->field_7
    //     0x9acd00: ldur            w3, [x4, #7]
    // 0x9acd04: cbnz            w3, #0x9acd10
    // 0x9acd08: r4 = false
    //     0x9acd08: add             x4, NULL, #0x30  ; false
    // 0x9acd0c: b               #0x9acd14
    // 0x9acd10: r4 = true
    //     0x9acd10: add             x4, NULL, #0x20  ; true
    // 0x9acd14: mov             x3, x4
    // 0x9acd18: cmp             w3, NULL
    // 0x9acd1c: b.eq            #0x9ace08
    // 0x9acd20: tbnz            w3, #4, #0x9ace08
    // 0x9acd24: LoadField: r3 = r1->field_f
    //     0x9acd24: ldur            w3, [x1, #0xf]
    // 0x9acd28: DecompressPointer r3
    //     0x9acd28: add             x3, x3, HEAP, lsl #32
    // 0x9acd2c: cmp             w3, NULL
    // 0x9acd30: b.ne            #0x9acd38
    // 0x9acd34: r3 = ""
    //     0x9acd34: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9acd38: ldur            x1, [fp, #-0x10]
    // 0x9acd3c: stur            x3, [fp, #-0x30]
    // 0x9acd40: r0 = of()
    //     0x9acd40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9acd44: LoadField: r1 = r0->field_87
    //     0x9acd44: ldur            w1, [x0, #0x87]
    // 0x9acd48: DecompressPointer r1
    //     0x9acd48: add             x1, x1, HEAP, lsl #32
    // 0x9acd4c: LoadField: r0 = r1->field_27
    //     0x9acd4c: ldur            w0, [x1, #0x27]
    // 0x9acd50: DecompressPointer r0
    //     0x9acd50: add             x0, x0, HEAP, lsl #32
    // 0x9acd54: r16 = 21.000000
    //     0x9acd54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x9acd58: ldr             x16, [x16, #0x9b0]
    // 0x9acd5c: str             x16, [SP]
    // 0x9acd60: mov             x1, x0
    // 0x9acd64: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x9acd64: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x9acd68: ldr             x4, [x4, #0x798]
    // 0x9acd6c: r0 = copyWith()
    //     0x9acd6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9acd70: stur            x0, [fp, #-0x40]
    // 0x9acd74: r0 = Text()
    //     0x9acd74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9acd78: mov             x2, x0
    // 0x9acd7c: ldur            x0, [fp, #-0x30]
    // 0x9acd80: stur            x2, [fp, #-0x50]
    // 0x9acd84: StoreField: r2->field_b = r0
    //     0x9acd84: stur            w0, [x2, #0xb]
    // 0x9acd88: ldur            x0, [fp, #-0x40]
    // 0x9acd8c: StoreField: r2->field_13 = r0
    //     0x9acd8c: stur            w0, [x2, #0x13]
    // 0x9acd90: ldur            x0, [fp, #-0x38]
    // 0x9acd94: LoadField: r1 = r0->field_b
    //     0x9acd94: ldur            w1, [x0, #0xb]
    // 0x9acd98: LoadField: r3 = r0->field_f
    //     0x9acd98: ldur            w3, [x0, #0xf]
    // 0x9acd9c: DecompressPointer r3
    //     0x9acd9c: add             x3, x3, HEAP, lsl #32
    // 0x9acda0: LoadField: r4 = r3->field_b
    //     0x9acda0: ldur            w4, [x3, #0xb]
    // 0x9acda4: r3 = LoadInt32Instr(r1)
    //     0x9acda4: sbfx            x3, x1, #1, #0x1f
    // 0x9acda8: stur            x3, [fp, #-0x48]
    // 0x9acdac: r1 = LoadInt32Instr(r4)
    //     0x9acdac: sbfx            x1, x4, #1, #0x1f
    // 0x9acdb0: cmp             x3, x1
    // 0x9acdb4: b.ne            #0x9acdc0
    // 0x9acdb8: mov             x1, x0
    // 0x9acdbc: r0 = _growToNextCapacity()
    //     0x9acdbc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9acdc0: ldur            x2, [fp, #-0x38]
    // 0x9acdc4: ldur            x3, [fp, #-0x48]
    // 0x9acdc8: add             x0, x3, #1
    // 0x9acdcc: lsl             x1, x0, #1
    // 0x9acdd0: StoreField: r2->field_b = r1
    //     0x9acdd0: stur            w1, [x2, #0xb]
    // 0x9acdd4: LoadField: r1 = r2->field_f
    //     0x9acdd4: ldur            w1, [x2, #0xf]
    // 0x9acdd8: DecompressPointer r1
    //     0x9acdd8: add             x1, x1, HEAP, lsl #32
    // 0x9acddc: ldur            x0, [fp, #-0x50]
    // 0x9acde0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9acde0: add             x25, x1, x3, lsl #2
    //     0x9acde4: add             x25, x25, #0xf
    //     0x9acde8: str             w0, [x25]
    //     0x9acdec: tbz             w0, #0, #0x9ace08
    //     0x9acdf0: ldurb           w16, [x1, #-1]
    //     0x9acdf4: ldurb           w17, [x0, #-1]
    //     0x9acdf8: and             x16, x17, x16, lsr #2
    //     0x9acdfc: tst             x16, HEAP, lsr #32
    //     0x9ace00: b.eq            #0x9ace08
    //     0x9ace04: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9ace08: ldur            x0, [fp, #-8]
    // 0x9ace0c: ldur            x1, [fp, #-0x10]
    // 0x9ace10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9ace10: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9ace14: r0 = _of()
    //     0x9ace14: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x9ace18: LoadField: r1 = r0->field_7
    //     0x9ace18: ldur            w1, [x0, #7]
    // 0x9ace1c: DecompressPointer r1
    //     0x9ace1c: add             x1, x1, HEAP, lsl #32
    // 0x9ace20: LoadField: d0 = r1->field_f
    //     0x9ace20: ldur            d0, [x1, #0xf]
    // 0x9ace24: d1 = 0.150000
    //     0x9ace24: ldr             d1, [PP, #0x5788]  ; [pp+0x5788] IMM: double(0.15) from 0x3fc3333333333333
    // 0x9ace28: fmul            d2, d0, d1
    // 0x9ace2c: ldur            x1, [fp, #-0x10]
    // 0x9ace30: stur            d2, [fp, #-0x68]
    // 0x9ace34: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9ace34: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9ace38: r0 = _of()
    //     0x9ace38: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x9ace3c: LoadField: r1 = r0->field_7
    //     0x9ace3c: ldur            w1, [x0, #7]
    // 0x9ace40: DecompressPointer r1
    //     0x9ace40: add             x1, x1, HEAP, lsl #32
    // 0x9ace44: LoadField: d0 = r1->field_7
    //     0x9ace44: ldur            d0, [x1, #7]
    // 0x9ace48: ldur            x0, [fp, #-8]
    // 0x9ace4c: stur            d0, [fp, #-0x70]
    // 0x9ace50: LoadField: r1 = r0->field_b
    //     0x9ace50: ldur            w1, [x0, #0xb]
    // 0x9ace54: DecompressPointer r1
    //     0x9ace54: add             x1, x1, HEAP, lsl #32
    // 0x9ace58: cmp             w1, NULL
    // 0x9ace5c: b.eq            #0x9ad230
    // 0x9ace60: LoadField: r2 = r1->field_b
    //     0x9ace60: ldur            w2, [x1, #0xb]
    // 0x9ace64: DecompressPointer r2
    //     0x9ace64: add             x2, x2, HEAP, lsl #32
    // 0x9ace68: LoadField: r1 = r2->field_23
    //     0x9ace68: ldur            w1, [x2, #0x23]
    // 0x9ace6c: DecompressPointer r1
    //     0x9ace6c: add             x1, x1, HEAP, lsl #32
    // 0x9ace70: LoadField: r3 = r1->field_b
    //     0x9ace70: ldur            w3, [x1, #0xb]
    // 0x9ace74: stur            x3, [fp, #-0x40]
    // 0x9ace78: LoadField: r4 = r0->field_13
    //     0x9ace78: ldur            w4, [x0, #0x13]
    // 0x9ace7c: DecompressPointer r4
    //     0x9ace7c: add             x4, x4, HEAP, lsl #32
    // 0x9ace80: r16 = Sentinel
    //     0x9ace80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ace84: cmp             w4, w16
    // 0x9ace88: b.eq            #0x9ad234
    // 0x9ace8c: ldur            x2, [fp, #-0x28]
    // 0x9ace90: stur            x4, [fp, #-0x30]
    // 0x9ace94: r1 = Function '<anonymous closure>':.
    //     0x9ace94: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb28] AnonymousClosure: (0x9adea8), in [package:customer_app/app/presentation/custom_widgets/product_detail/sale_activity_item_view.dart] _SaleActivityItemViewState::build (0x9acbf8)
    //     0x9ace98: ldr             x1, [x1, #0xb28]
    // 0x9ace9c: r0 = AllocateClosure()
    //     0x9ace9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9acea0: ldur            x2, [fp, #-0x28]
    // 0x9acea4: r1 = Function '<anonymous closure>':.
    //     0x9acea4: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb30] AnonymousClosure: (0x9ad290), in [package:customer_app/app/presentation/custom_widgets/product_detail/sale_activity_item_view.dart] _SaleActivityItemViewState::build (0x9acbf8)
    //     0x9acea8: ldr             x1, [x1, #0xb30]
    // 0x9aceac: stur            x0, [fp, #-0x28]
    // 0x9aceb0: r0 = AllocateClosure()
    //     0x9aceb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9aceb4: stur            x0, [fp, #-0x50]
    // 0x9aceb8: r0 = PageView()
    //     0x9aceb8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0x9acebc: stur            x0, [fp, #-0x58]
    // 0x9acec0: r16 = Instance_BouncingScrollPhysics
    //     0x9acec0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x9acec4: ldr             x16, [x16, #0x890]
    // 0x9acec8: ldur            lr, [fp, #-0x30]
    // 0x9acecc: stp             lr, x16, [SP]
    // 0x9aced0: mov             x1, x0
    // 0x9aced4: ldur            x2, [fp, #-0x50]
    // 0x9aced8: ldur            x3, [fp, #-0x40]
    // 0x9acedc: ldur            x5, [fp, #-0x28]
    // 0x9acee0: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0x9acee0: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0x9acee4: ldr             x4, [x4, #0xe40]
    // 0x9acee8: r0 = PageView.builder()
    //     0x9acee8: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0x9aceec: ldur            d0, [fp, #-0x70]
    // 0x9acef0: r0 = inline_Allocate_Double()
    //     0x9acef0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9acef4: add             x0, x0, #0x10
    //     0x9acef8: cmp             x1, x0
    //     0x9acefc: b.ls            #0x9ad240
    //     0x9acf00: str             x0, [THR, #0x50]  ; THR::top
    //     0x9acf04: sub             x0, x0, #0xf
    //     0x9acf08: movz            x1, #0xe15c
    //     0x9acf0c: movk            x1, #0x3, lsl #16
    //     0x9acf10: stur            x1, [x0, #-1]
    // 0x9acf14: StoreField: r0->field_7 = d0
    //     0x9acf14: stur            d0, [x0, #7]
    // 0x9acf18: stur            x0, [fp, #-0x28]
    // 0x9acf1c: r0 = SizedBox()
    //     0x9acf1c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9acf20: mov             x2, x0
    // 0x9acf24: ldur            x0, [fp, #-0x28]
    // 0x9acf28: stur            x2, [fp, #-0x30]
    // 0x9acf2c: StoreField: r2->field_f = r0
    //     0x9acf2c: stur            w0, [x2, #0xf]
    // 0x9acf30: ldur            d0, [fp, #-0x68]
    // 0x9acf34: r0 = inline_Allocate_Double()
    //     0x9acf34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9acf38: add             x0, x0, #0x10
    //     0x9acf3c: cmp             x1, x0
    //     0x9acf40: b.ls            #0x9ad250
    //     0x9acf44: str             x0, [THR, #0x50]  ; THR::top
    //     0x9acf48: sub             x0, x0, #0xf
    //     0x9acf4c: movz            x1, #0xe15c
    //     0x9acf50: movk            x1, #0x3, lsl #16
    //     0x9acf54: stur            x1, [x0, #-1]
    // 0x9acf58: StoreField: r0->field_7 = d0
    //     0x9acf58: stur            d0, [x0, #7]
    // 0x9acf5c: StoreField: r2->field_13 = r0
    //     0x9acf5c: stur            w0, [x2, #0x13]
    // 0x9acf60: ldur            x0, [fp, #-0x58]
    // 0x9acf64: StoreField: r2->field_b = r0
    //     0x9acf64: stur            w0, [x2, #0xb]
    // 0x9acf68: ldur            x0, [fp, #-0x38]
    // 0x9acf6c: LoadField: r1 = r0->field_b
    //     0x9acf6c: ldur            w1, [x0, #0xb]
    // 0x9acf70: LoadField: r3 = r0->field_f
    //     0x9acf70: ldur            w3, [x0, #0xf]
    // 0x9acf74: DecompressPointer r3
    //     0x9acf74: add             x3, x3, HEAP, lsl #32
    // 0x9acf78: LoadField: r4 = r3->field_b
    //     0x9acf78: ldur            w4, [x3, #0xb]
    // 0x9acf7c: r3 = LoadInt32Instr(r1)
    //     0x9acf7c: sbfx            x3, x1, #1, #0x1f
    // 0x9acf80: stur            x3, [fp, #-0x48]
    // 0x9acf84: r1 = LoadInt32Instr(r4)
    //     0x9acf84: sbfx            x1, x4, #1, #0x1f
    // 0x9acf88: cmp             x3, x1
    // 0x9acf8c: b.ne            #0x9acf98
    // 0x9acf90: mov             x1, x0
    // 0x9acf94: r0 = _growToNextCapacity()
    //     0x9acf94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9acf98: ldur            x4, [fp, #-8]
    // 0x9acf9c: ldur            x2, [fp, #-0x38]
    // 0x9acfa0: ldur            x3, [fp, #-0x48]
    // 0x9acfa4: add             x0, x3, #1
    // 0x9acfa8: lsl             x1, x0, #1
    // 0x9acfac: StoreField: r2->field_b = r1
    //     0x9acfac: stur            w1, [x2, #0xb]
    // 0x9acfb0: LoadField: r1 = r2->field_f
    //     0x9acfb0: ldur            w1, [x2, #0xf]
    // 0x9acfb4: DecompressPointer r1
    //     0x9acfb4: add             x1, x1, HEAP, lsl #32
    // 0x9acfb8: ldur            x0, [fp, #-0x30]
    // 0x9acfbc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9acfbc: add             x25, x1, x3, lsl #2
    //     0x9acfc0: add             x25, x25, #0xf
    //     0x9acfc4: str             w0, [x25]
    //     0x9acfc8: tbz             w0, #0, #0x9acfe4
    //     0x9acfcc: ldurb           w16, [x1, #-1]
    //     0x9acfd0: ldurb           w17, [x0, #-1]
    //     0x9acfd4: and             x16, x17, x16, lsr #2
    //     0x9acfd8: tst             x16, HEAP, lsr #32
    //     0x9acfdc: b.eq            #0x9acfe4
    //     0x9acfe0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9acfe4: LoadField: r0 = r4->field_b
    //     0x9acfe4: ldur            w0, [x4, #0xb]
    // 0x9acfe8: DecompressPointer r0
    //     0x9acfe8: add             x0, x0, HEAP, lsl #32
    // 0x9acfec: cmp             w0, NULL
    // 0x9acff0: b.eq            #0x9ad268
    // 0x9acff4: LoadField: r1 = r0->field_b
    //     0x9acff4: ldur            w1, [x0, #0xb]
    // 0x9acff8: DecompressPointer r1
    //     0x9acff8: add             x1, x1, HEAP, lsl #32
    // 0x9acffc: LoadField: r0 = r1->field_23
    //     0x9acffc: ldur            w0, [x1, #0x23]
    // 0x9ad000: DecompressPointer r0
    //     0x9ad000: add             x0, x0, HEAP, lsl #32
    // 0x9ad004: LoadField: r3 = r0->field_b
    //     0x9ad004: ldur            w3, [x0, #0xb]
    // 0x9ad008: stur            x3, [fp, #-0x28]
    // 0x9ad00c: r0 = LoadInt32Instr(r3)
    //     0x9ad00c: sbfx            x0, x3, #1, #0x1f
    // 0x9ad010: cmp             x0, #1
    // 0x9ad014: b.le            #0x9ad1a0
    // 0x9ad018: ArrayLoad: r0 = r4[0]  ; List_8
    //     0x9ad018: ldur            x0, [x4, #0x17]
    // 0x9ad01c: ldur            x1, [fp, #-0x10]
    // 0x9ad020: stur            x0, [fp, #-0x48]
    // 0x9ad024: r0 = of()
    //     0x9ad024: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ad028: LoadField: r1 = r0->field_5b
    //     0x9ad028: ldur            w1, [x0, #0x5b]
    // 0x9ad02c: DecompressPointer r1
    //     0x9ad02c: add             x1, x1, HEAP, lsl #32
    // 0x9ad030: ldur            x0, [fp, #-0x28]
    // 0x9ad034: stur            x1, [fp, #-8]
    // 0x9ad038: r2 = LoadInt32Instr(r0)
    //     0x9ad038: sbfx            x2, x0, #1, #0x1f
    // 0x9ad03c: stur            x2, [fp, #-0x60]
    // 0x9ad040: r0 = CarouselIndicator()
    //     0x9ad040: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0x9ad044: mov             x3, x0
    // 0x9ad048: ldur            x0, [fp, #-0x60]
    // 0x9ad04c: stur            x3, [fp, #-0x10]
    // 0x9ad050: StoreField: r3->field_b = r0
    //     0x9ad050: stur            x0, [x3, #0xb]
    // 0x9ad054: ldur            x0, [fp, #-0x48]
    // 0x9ad058: StoreField: r3->field_13 = r0
    //     0x9ad058: stur            x0, [x3, #0x13]
    // 0x9ad05c: ldur            x0, [fp, #-8]
    // 0x9ad060: StoreField: r3->field_1b = r0
    //     0x9ad060: stur            w0, [x3, #0x1b]
    // 0x9ad064: r0 = Instance_Color
    //     0x9ad064: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x9ad068: ldr             x0, [x0, #0x90]
    // 0x9ad06c: StoreField: r3->field_1f = r0
    //     0x9ad06c: stur            w0, [x3, #0x1f]
    // 0x9ad070: r1 = Null
    //     0x9ad070: mov             x1, NULL
    // 0x9ad074: r2 = 2
    //     0x9ad074: movz            x2, #0x2
    // 0x9ad078: r0 = AllocateArray()
    //     0x9ad078: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ad07c: mov             x2, x0
    // 0x9ad080: ldur            x0, [fp, #-0x10]
    // 0x9ad084: stur            x2, [fp, #-8]
    // 0x9ad088: StoreField: r2->field_f = r0
    //     0x9ad088: stur            w0, [x2, #0xf]
    // 0x9ad08c: r1 = <Widget>
    //     0x9ad08c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ad090: r0 = AllocateGrowableArray()
    //     0x9ad090: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ad094: mov             x1, x0
    // 0x9ad098: ldur            x0, [fp, #-8]
    // 0x9ad09c: stur            x1, [fp, #-0x10]
    // 0x9ad0a0: StoreField: r1->field_f = r0
    //     0x9ad0a0: stur            w0, [x1, #0xf]
    // 0x9ad0a4: r0 = 2
    //     0x9ad0a4: movz            x0, #0x2
    // 0x9ad0a8: StoreField: r1->field_b = r0
    //     0x9ad0a8: stur            w0, [x1, #0xb]
    // 0x9ad0ac: r0 = Row()
    //     0x9ad0ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9ad0b0: mov             x1, x0
    // 0x9ad0b4: r0 = Instance_Axis
    //     0x9ad0b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9ad0b8: stur            x1, [fp, #-8]
    // 0x9ad0bc: StoreField: r1->field_f = r0
    //     0x9ad0bc: stur            w0, [x1, #0xf]
    // 0x9ad0c0: r0 = Instance_MainAxisAlignment
    //     0x9ad0c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9ad0c4: ldr             x0, [x0, #0xab0]
    // 0x9ad0c8: StoreField: r1->field_13 = r0
    //     0x9ad0c8: stur            w0, [x1, #0x13]
    // 0x9ad0cc: r0 = Instance_MainAxisSize
    //     0x9ad0cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ad0d0: ldr             x0, [x0, #0xa10]
    // 0x9ad0d4: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ad0d4: stur            w0, [x1, #0x17]
    // 0x9ad0d8: r0 = Instance_CrossAxisAlignment
    //     0x9ad0d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9ad0dc: ldr             x0, [x0, #0xa18]
    // 0x9ad0e0: StoreField: r1->field_1b = r0
    //     0x9ad0e0: stur            w0, [x1, #0x1b]
    // 0x9ad0e4: r0 = Instance_VerticalDirection
    //     0x9ad0e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ad0e8: ldr             x0, [x0, #0xa20]
    // 0x9ad0ec: StoreField: r1->field_23 = r0
    //     0x9ad0ec: stur            w0, [x1, #0x23]
    // 0x9ad0f0: r2 = Instance_Clip
    //     0x9ad0f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ad0f4: ldr             x2, [x2, #0x38]
    // 0x9ad0f8: StoreField: r1->field_2b = r2
    //     0x9ad0f8: stur            w2, [x1, #0x2b]
    // 0x9ad0fc: StoreField: r1->field_2f = rZR
    //     0x9ad0fc: stur            xzr, [x1, #0x2f]
    // 0x9ad100: ldur            x3, [fp, #-0x10]
    // 0x9ad104: StoreField: r1->field_b = r3
    //     0x9ad104: stur            w3, [x1, #0xb]
    // 0x9ad108: r0 = Padding()
    //     0x9ad108: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ad10c: mov             x2, x0
    // 0x9ad110: r0 = Instance_EdgeInsets
    //     0x9ad110: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x9ad114: ldr             x0, [x0, #0xa00]
    // 0x9ad118: stur            x2, [fp, #-0x10]
    // 0x9ad11c: StoreField: r2->field_f = r0
    //     0x9ad11c: stur            w0, [x2, #0xf]
    // 0x9ad120: ldur            x0, [fp, #-8]
    // 0x9ad124: StoreField: r2->field_b = r0
    //     0x9ad124: stur            w0, [x2, #0xb]
    // 0x9ad128: ldur            x0, [fp, #-0x38]
    // 0x9ad12c: LoadField: r1 = r0->field_b
    //     0x9ad12c: ldur            w1, [x0, #0xb]
    // 0x9ad130: LoadField: r3 = r0->field_f
    //     0x9ad130: ldur            w3, [x0, #0xf]
    // 0x9ad134: DecompressPointer r3
    //     0x9ad134: add             x3, x3, HEAP, lsl #32
    // 0x9ad138: LoadField: r4 = r3->field_b
    //     0x9ad138: ldur            w4, [x3, #0xb]
    // 0x9ad13c: r3 = LoadInt32Instr(r1)
    //     0x9ad13c: sbfx            x3, x1, #1, #0x1f
    // 0x9ad140: stur            x3, [fp, #-0x48]
    // 0x9ad144: r1 = LoadInt32Instr(r4)
    //     0x9ad144: sbfx            x1, x4, #1, #0x1f
    // 0x9ad148: cmp             x3, x1
    // 0x9ad14c: b.ne            #0x9ad158
    // 0x9ad150: mov             x1, x0
    // 0x9ad154: r0 = _growToNextCapacity()
    //     0x9ad154: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9ad158: ldur            x2, [fp, #-0x38]
    // 0x9ad15c: ldur            x3, [fp, #-0x48]
    // 0x9ad160: add             x0, x3, #1
    // 0x9ad164: lsl             x1, x0, #1
    // 0x9ad168: StoreField: r2->field_b = r1
    //     0x9ad168: stur            w1, [x2, #0xb]
    // 0x9ad16c: LoadField: r1 = r2->field_f
    //     0x9ad16c: ldur            w1, [x2, #0xf]
    // 0x9ad170: DecompressPointer r1
    //     0x9ad170: add             x1, x1, HEAP, lsl #32
    // 0x9ad174: ldur            x0, [fp, #-0x10]
    // 0x9ad178: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9ad178: add             x25, x1, x3, lsl #2
    //     0x9ad17c: add             x25, x25, #0xf
    //     0x9ad180: str             w0, [x25]
    //     0x9ad184: tbz             w0, #0, #0x9ad1a0
    //     0x9ad188: ldurb           w16, [x1, #-1]
    //     0x9ad18c: ldurb           w17, [x0, #-1]
    //     0x9ad190: and             x16, x17, x16, lsr #2
    //     0x9ad194: tst             x16, HEAP, lsr #32
    //     0x9ad198: b.eq            #0x9ad1a0
    //     0x9ad19c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9ad1a0: ldur            x0, [fp, #-0x20]
    // 0x9ad1a4: ldur            x1, [fp, #-0x18]
    // 0x9ad1a8: r0 = Column()
    //     0x9ad1a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9ad1ac: mov             x1, x0
    // 0x9ad1b0: r0 = Instance_Axis
    //     0x9ad1b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9ad1b4: stur            x1, [fp, #-8]
    // 0x9ad1b8: StoreField: r1->field_f = r0
    //     0x9ad1b8: stur            w0, [x1, #0xf]
    // 0x9ad1bc: r0 = Instance_MainAxisAlignment
    //     0x9ad1bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9ad1c0: ldr             x0, [x0, #0xa08]
    // 0x9ad1c4: StoreField: r1->field_13 = r0
    //     0x9ad1c4: stur            w0, [x1, #0x13]
    // 0x9ad1c8: r0 = Instance_MainAxisSize
    //     0x9ad1c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x9ad1cc: ldr             x0, [x0, #0xdd0]
    // 0x9ad1d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ad1d0: stur            w0, [x1, #0x17]
    // 0x9ad1d4: ldur            x0, [fp, #-0x18]
    // 0x9ad1d8: StoreField: r1->field_1b = r0
    //     0x9ad1d8: stur            w0, [x1, #0x1b]
    // 0x9ad1dc: r0 = Instance_VerticalDirection
    //     0x9ad1dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ad1e0: ldr             x0, [x0, #0xa20]
    // 0x9ad1e4: StoreField: r1->field_23 = r0
    //     0x9ad1e4: stur            w0, [x1, #0x23]
    // 0x9ad1e8: r0 = Instance_Clip
    //     0x9ad1e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ad1ec: ldr             x0, [x0, #0x38]
    // 0x9ad1f0: StoreField: r1->field_2b = r0
    //     0x9ad1f0: stur            w0, [x1, #0x2b]
    // 0x9ad1f4: StoreField: r1->field_2f = rZR
    //     0x9ad1f4: stur            xzr, [x1, #0x2f]
    // 0x9ad1f8: ldur            x0, [fp, #-0x38]
    // 0x9ad1fc: StoreField: r1->field_b = r0
    //     0x9ad1fc: stur            w0, [x1, #0xb]
    // 0x9ad200: r0 = Padding()
    //     0x9ad200: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ad204: ldur            x1, [fp, #-0x20]
    // 0x9ad208: StoreField: r0->field_f = r1
    //     0x9ad208: stur            w1, [x0, #0xf]
    // 0x9ad20c: ldur            x1, [fp, #-8]
    // 0x9ad210: StoreField: r0->field_b = r1
    //     0x9ad210: stur            w1, [x0, #0xb]
    // 0x9ad214: LeaveFrame
    //     0x9ad214: mov             SP, fp
    //     0x9ad218: ldp             fp, lr, [SP], #0x10
    // 0x9ad21c: ret
    //     0x9ad21c: ret             
    // 0x9ad220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ad220: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ad224: b               #0x9acc20
    // 0x9ad228: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ad228: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ad22c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ad22c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ad230: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9ad230: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0x9ad234: r9 = _pageController
    //     0x9ad234: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5bb38] Field <_SaleActivityItemViewState@1252150816._pageController@1252150816>: late (offset: 0x14)
    //     0x9ad238: ldr             x9, [x9, #0xb38]
    // 0x9ad23c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9ad23c: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9ad240: SaveReg d0
    //     0x9ad240: str             q0, [SP, #-0x10]!
    // 0x9ad244: r0 = AllocateDouble()
    //     0x9ad244: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9ad248: RestoreReg d0
    //     0x9ad248: ldr             q0, [SP], #0x10
    // 0x9ad24c: b               #0x9acf14
    // 0x9ad250: SaveReg d0
    //     0x9ad250: str             q0, [SP, #-0x10]!
    // 0x9ad254: SaveReg r2
    //     0x9ad254: str             x2, [SP, #-8]!
    // 0x9ad258: r0 = AllocateDouble()
    //     0x9ad258: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9ad25c: RestoreReg r2
    //     0x9ad25c: ldr             x2, [SP], #8
    // 0x9ad260: RestoreReg d0
    //     0x9ad260: ldr             q0, [SP], #0x10
    // 0x9ad264: b               #0x9acf58
    // 0x9ad268: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ad268: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9ad290, size: 0x7c
    // 0x9ad290: EnterFrame
    //     0x9ad290: stp             fp, lr, [SP, #-0x10]!
    //     0x9ad294: mov             fp, SP
    // 0x9ad298: ldr             x0, [fp, #0x20]
    // 0x9ad29c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9ad29c: ldur            w1, [x0, #0x17]
    // 0x9ad2a0: DecompressPointer r1
    //     0x9ad2a0: add             x1, x1, HEAP, lsl #32
    // 0x9ad2a4: CheckStackOverflow
    //     0x9ad2a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ad2a8: cmp             SP, x16
    //     0x9ad2ac: b.ls            #0x9ad300
    // 0x9ad2b0: LoadField: r0 = r1->field_f
    //     0x9ad2b0: ldur            w0, [x1, #0xf]
    // 0x9ad2b4: DecompressPointer r0
    //     0x9ad2b4: add             x0, x0, HEAP, lsl #32
    // 0x9ad2b8: LoadField: r1 = r0->field_b
    //     0x9ad2b8: ldur            w1, [x0, #0xb]
    // 0x9ad2bc: DecompressPointer r1
    //     0x9ad2bc: add             x1, x1, HEAP, lsl #32
    // 0x9ad2c0: cmp             w1, NULL
    // 0x9ad2c4: b.eq            #0x9ad308
    // 0x9ad2c8: LoadField: r2 = r1->field_b
    //     0x9ad2c8: ldur            w2, [x1, #0xb]
    // 0x9ad2cc: DecompressPointer r2
    //     0x9ad2cc: add             x2, x2, HEAP, lsl #32
    // 0x9ad2d0: LoadField: r1 = r2->field_23
    //     0x9ad2d0: ldur            w1, [x2, #0x23]
    // 0x9ad2d4: DecompressPointer r1
    //     0x9ad2d4: add             x1, x1, HEAP, lsl #32
    // 0x9ad2d8: ldr             x2, [fp, #0x10]
    // 0x9ad2dc: r3 = LoadInt32Instr(r2)
    //     0x9ad2dc: sbfx            x3, x2, #1, #0x1f
    //     0x9ad2e0: tbz             w2, #0, #0x9ad2e8
    //     0x9ad2e4: ldur            x3, [x2, #7]
    // 0x9ad2e8: mov             x2, x1
    // 0x9ad2ec: mov             x1, x0
    // 0x9ad2f0: r0 = slider()
    //     0x9ad2f0: bl              #0x9ad30c  ; [package:customer_app/app/presentation/custom_widgets/product_detail/sale_activity_item_view.dart] _SaleActivityItemViewState::slider
    // 0x9ad2f4: LeaveFrame
    //     0x9ad2f4: mov             SP, fp
    //     0x9ad2f8: ldp             fp, lr, [SP], #0x10
    // 0x9ad2fc: ret
    //     0x9ad2fc: ret             
    // 0x9ad300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ad300: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ad304: b               #0x9ad2b0
    // 0x9ad308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ad308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ slider(/* No info */) {
    // ** addr: 0x9ad30c, size: 0x81c
    // 0x9ad30c: EnterFrame
    //     0x9ad30c: stp             fp, lr, [SP, #-0x10]!
    //     0x9ad310: mov             fp, SP
    // 0x9ad314: AllocStack(0x68)
    //     0x9ad314: sub             SP, SP, #0x68
    // 0x9ad318: SetupParameters(_SaleActivityItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x9ad318: mov             x0, x1
    //     0x9ad31c: stur            x1, [fp, #-8]
    //     0x9ad320: mov             x1, x3
    //     0x9ad324: stur            x2, [fp, #-0x10]
    //     0x9ad328: stur            x3, [fp, #-0x18]
    // 0x9ad32c: CheckStackOverflow
    //     0x9ad32c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ad330: cmp             SP, x16
    //     0x9ad334: b.ls            #0x9adafc
    // 0x9ad338: r1 = 3
    //     0x9ad338: movz            x1, #0x3
    // 0x9ad33c: r0 = AllocateContext()
    //     0x9ad33c: bl              #0x16f6108  ; AllocateContextStub
    // 0x9ad340: mov             x3, x0
    // 0x9ad344: ldur            x2, [fp, #-8]
    // 0x9ad348: stur            x3, [fp, #-0x38]
    // 0x9ad34c: StoreField: r3->field_f = r2
    //     0x9ad34c: stur            w2, [x3, #0xf]
    // 0x9ad350: ldur            x4, [fp, #-0x10]
    // 0x9ad354: StoreField: r3->field_13 = r4
    //     0x9ad354: stur            w4, [x3, #0x13]
    // 0x9ad358: ldur            x5, [fp, #-0x18]
    // 0x9ad35c: r0 = BoxInt64Instr(r5)
    //     0x9ad35c: sbfiz           x0, x5, #1, #0x1f
    //     0x9ad360: cmp             x5, x0, asr #1
    //     0x9ad364: b.eq            #0x9ad370
    //     0x9ad368: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9ad36c: stur            x5, [x0, #7]
    // 0x9ad370: ArrayStore: r3[0] = r0  ; List_4
    //     0x9ad370: stur            w0, [x3, #0x17]
    // 0x9ad374: LoadField: r0 = r2->field_b
    //     0x9ad374: ldur            w0, [x2, #0xb]
    // 0x9ad378: DecompressPointer r0
    //     0x9ad378: add             x0, x0, HEAP, lsl #32
    // 0x9ad37c: cmp             w0, NULL
    // 0x9ad380: b.eq            #0x9adb04
    // 0x9ad384: LoadField: r1 = r0->field_23
    //     0x9ad384: ldur            w1, [x0, #0x23]
    // 0x9ad388: DecompressPointer r1
    //     0x9ad388: add             x1, x1, HEAP, lsl #32
    // 0x9ad38c: stur            x1, [fp, #-0x30]
    // 0x9ad390: LoadField: r6 = r0->field_27
    //     0x9ad390: ldur            w6, [x0, #0x27]
    // 0x9ad394: DecompressPointer r6
    //     0x9ad394: add             x6, x6, HEAP, lsl #32
    // 0x9ad398: stur            x6, [fp, #-0x28]
    // 0x9ad39c: LoadField: r7 = r0->field_2b
    //     0x9ad39c: ldur            w7, [x0, #0x2b]
    // 0x9ad3a0: DecompressPointer r7
    //     0x9ad3a0: add             x7, x7, HEAP, lsl #32
    // 0x9ad3a4: stur            x7, [fp, #-0x20]
    // 0x9ad3a8: r0 = BoxDecoration()
    //     0x9ad3a8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9ad3ac: mov             x2, x0
    // 0x9ad3b0: ldur            x0, [fp, #-0x20]
    // 0x9ad3b4: stur            x2, [fp, #-0x40]
    // 0x9ad3b8: StoreField: r2->field_7 = r0
    //     0x9ad3b8: stur            w0, [x2, #7]
    // 0x9ad3bc: ldur            x0, [fp, #-0x28]
    // 0x9ad3c0: StoreField: r2->field_f = r0
    //     0x9ad3c0: stur            w0, [x2, #0xf]
    // 0x9ad3c4: ldur            x3, [fp, #-0x30]
    // 0x9ad3c8: StoreField: r2->field_13 = r3
    //     0x9ad3c8: stur            w3, [x2, #0x13]
    // 0x9ad3cc: r4 = Instance_BoxShape
    //     0x9ad3cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9ad3d0: ldr             x4, [x4, #0x80]
    // 0x9ad3d4: StoreField: r2->field_23 = r4
    //     0x9ad3d4: stur            w4, [x2, #0x23]
    // 0x9ad3d8: ldur            x5, [fp, #-0x10]
    // 0x9ad3dc: LoadField: r0 = r5->field_b
    //     0x9ad3dc: ldur            w0, [x5, #0xb]
    // 0x9ad3e0: r1 = LoadInt32Instr(r0)
    //     0x9ad3e0: sbfx            x1, x0, #1, #0x1f
    // 0x9ad3e4: mov             x0, x1
    // 0x9ad3e8: ldur            x1, [fp, #-0x18]
    // 0x9ad3ec: cmp             x1, x0
    // 0x9ad3f0: b.hs            #0x9adb08
    // 0x9ad3f4: LoadField: r0 = r5->field_f
    //     0x9ad3f4: ldur            w0, [x5, #0xf]
    // 0x9ad3f8: DecompressPointer r0
    //     0x9ad3f8: add             x0, x0, HEAP, lsl #32
    // 0x9ad3fc: ldur            x1, [fp, #-0x18]
    // 0x9ad400: ArrayLoad: r5 = r0[r1]  ; Unknown_4
    //     0x9ad400: add             x16, x0, x1, lsl #2
    //     0x9ad404: ldur            w5, [x16, #0xf]
    // 0x9ad408: DecompressPointer r5
    //     0x9ad408: add             x5, x5, HEAP, lsl #32
    // 0x9ad40c: LoadField: r0 = r5->field_ab
    //     0x9ad40c: ldur            w0, [x5, #0xab]
    // 0x9ad410: DecompressPointer r0
    //     0x9ad410: add             x0, x0, HEAP, lsl #32
    // 0x9ad414: cmp             w0, NULL
    // 0x9ad418: b.ne            #0x9ad424
    // 0x9ad41c: r1 = ""
    //     0x9ad41c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ad420: b               #0x9ad428
    // 0x9ad424: mov             x1, x0
    // 0x9ad428: ldur            x0, [fp, #-0x38]
    // 0x9ad42c: stur            x1, [fp, #-0x10]
    // 0x9ad430: r0 = CachedNetworkImage()
    //     0x9ad430: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x9ad434: stur            x0, [fp, #-0x20]
    // 0x9ad438: r16 = 64.000000
    //     0x9ad438: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0x9ad43c: ldr             x16, [x16, #0x838]
    // 0x9ad440: r30 = 64.000000
    //     0x9ad440: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0x9ad444: ldr             lr, [lr, #0x838]
    // 0x9ad448: stp             lr, x16, [SP, #8]
    // 0x9ad44c: r16 = Instance_BoxFit
    //     0x9ad44c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x9ad450: ldr             x16, [x16, #0xb18]
    // 0x9ad454: str             x16, [SP]
    // 0x9ad458: mov             x1, x0
    // 0x9ad45c: ldur            x2, [fp, #-0x10]
    // 0x9ad460: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0x9ad460: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0x9ad464: ldr             x4, [x4, #0xb40]
    // 0x9ad468: r0 = CachedNetworkImage()
    //     0x9ad468: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x9ad46c: r0 = ClipRRect()
    //     0x9ad46c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x9ad470: mov             x2, x0
    // 0x9ad474: ldur            x0, [fp, #-0x30]
    // 0x9ad478: stur            x2, [fp, #-0x28]
    // 0x9ad47c: StoreField: r2->field_f = r0
    //     0x9ad47c: stur            w0, [x2, #0xf]
    // 0x9ad480: r0 = Instance_Clip
    //     0x9ad480: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x9ad484: ldr             x0, [x0, #0x138]
    // 0x9ad488: ArrayStore: r2[0] = r0  ; List_4
    //     0x9ad488: stur            w0, [x2, #0x17]
    // 0x9ad48c: ldur            x0, [fp, #-0x20]
    // 0x9ad490: StoreField: r2->field_b = r0
    //     0x9ad490: stur            w0, [x2, #0xb]
    // 0x9ad494: ldur            x3, [fp, #-0x38]
    // 0x9ad498: LoadField: r4 = r3->field_13
    //     0x9ad498: ldur            w4, [x3, #0x13]
    // 0x9ad49c: DecompressPointer r4
    //     0x9ad49c: add             x4, x4, HEAP, lsl #32
    // 0x9ad4a0: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x9ad4a0: ldur            w0, [x3, #0x17]
    // 0x9ad4a4: DecompressPointer r0
    //     0x9ad4a4: add             x0, x0, HEAP, lsl #32
    // 0x9ad4a8: LoadField: r1 = r4->field_b
    //     0x9ad4a8: ldur            w1, [x4, #0xb]
    // 0x9ad4ac: r5 = LoadInt32Instr(r0)
    //     0x9ad4ac: sbfx            x5, x0, #1, #0x1f
    //     0x9ad4b0: tbz             w0, #0, #0x9ad4b8
    //     0x9ad4b4: ldur            x5, [x0, #7]
    // 0x9ad4b8: r0 = LoadInt32Instr(r1)
    //     0x9ad4b8: sbfx            x0, x1, #1, #0x1f
    // 0x9ad4bc: mov             x1, x5
    // 0x9ad4c0: cmp             x1, x0
    // 0x9ad4c4: b.hs            #0x9adb0c
    // 0x9ad4c8: LoadField: r0 = r4->field_f
    //     0x9ad4c8: ldur            w0, [x4, #0xf]
    // 0x9ad4cc: DecompressPointer r0
    //     0x9ad4cc: add             x0, x0, HEAP, lsl #32
    // 0x9ad4d0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9ad4d0: add             x16, x0, x5, lsl #2
    //     0x9ad4d4: ldur            w1, [x16, #0xf]
    // 0x9ad4d8: DecompressPointer r1
    //     0x9ad4d8: add             x1, x1, HEAP, lsl #32
    // 0x9ad4dc: LoadField: r0 = r1->field_7
    //     0x9ad4dc: ldur            w0, [x1, #7]
    // 0x9ad4e0: DecompressPointer r0
    //     0x9ad4e0: add             x0, x0, HEAP, lsl #32
    // 0x9ad4e4: cmp             w0, NULL
    // 0x9ad4e8: b.ne            #0x9ad4f4
    // 0x9ad4ec: r4 = ""
    //     0x9ad4ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ad4f0: b               #0x9ad4f8
    // 0x9ad4f4: mov             x4, x0
    // 0x9ad4f8: ldur            x0, [fp, #-8]
    // 0x9ad4fc: stur            x4, [fp, #-0x10]
    // 0x9ad500: LoadField: r1 = r0->field_f
    //     0x9ad500: ldur            w1, [x0, #0xf]
    // 0x9ad504: DecompressPointer r1
    //     0x9ad504: add             x1, x1, HEAP, lsl #32
    // 0x9ad508: cmp             w1, NULL
    // 0x9ad50c: b.eq            #0x9adb10
    // 0x9ad510: r0 = of()
    //     0x9ad510: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ad514: LoadField: r1 = r0->field_87
    //     0x9ad514: ldur            w1, [x0, #0x87]
    // 0x9ad518: DecompressPointer r1
    //     0x9ad518: add             x1, x1, HEAP, lsl #32
    // 0x9ad51c: LoadField: r0 = r1->field_2b
    //     0x9ad51c: ldur            w0, [x1, #0x2b]
    // 0x9ad520: DecompressPointer r0
    //     0x9ad520: add             x0, x0, HEAP, lsl #32
    // 0x9ad524: stur            x0, [fp, #-0x20]
    // 0x9ad528: r1 = Instance_Color
    //     0x9ad528: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9ad52c: d0 = 0.700000
    //     0x9ad52c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9ad530: ldr             d0, [x17, #0xf48]
    // 0x9ad534: r0 = withOpacity()
    //     0x9ad534: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9ad538: r16 = 12.000000
    //     0x9ad538: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9ad53c: ldr             x16, [x16, #0x9e8]
    // 0x9ad540: stp             x0, x16, [SP]
    // 0x9ad544: ldur            x1, [fp, #-0x20]
    // 0x9ad548: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ad548: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ad54c: ldr             x4, [x4, #0xaa0]
    // 0x9ad550: r0 = copyWith()
    //     0x9ad550: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ad554: stur            x0, [fp, #-0x20]
    // 0x9ad558: r0 = Text()
    //     0x9ad558: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ad55c: mov             x2, x0
    // 0x9ad560: ldur            x0, [fp, #-0x10]
    // 0x9ad564: stur            x2, [fp, #-0x30]
    // 0x9ad568: StoreField: r2->field_b = r0
    //     0x9ad568: stur            w0, [x2, #0xb]
    // 0x9ad56c: ldur            x0, [fp, #-0x20]
    // 0x9ad570: StoreField: r2->field_13 = r0
    //     0x9ad570: stur            w0, [x2, #0x13]
    // 0x9ad574: r3 = Instance_TextOverflow
    //     0x9ad574: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x9ad578: ldr             x3, [x3, #0xe10]
    // 0x9ad57c: StoreField: r2->field_2b = r3
    //     0x9ad57c: stur            w3, [x2, #0x2b]
    // 0x9ad580: r4 = 2
    //     0x9ad580: movz            x4, #0x2
    // 0x9ad584: StoreField: r2->field_37 = r4
    //     0x9ad584: stur            w4, [x2, #0x37]
    // 0x9ad588: ldur            x5, [fp, #-0x38]
    // 0x9ad58c: LoadField: r6 = r5->field_13
    //     0x9ad58c: ldur            w6, [x5, #0x13]
    // 0x9ad590: DecompressPointer r6
    //     0x9ad590: add             x6, x6, HEAP, lsl #32
    // 0x9ad594: ArrayLoad: r0 = r5[0]  ; List_4
    //     0x9ad594: ldur            w0, [x5, #0x17]
    // 0x9ad598: DecompressPointer r0
    //     0x9ad598: add             x0, x0, HEAP, lsl #32
    // 0x9ad59c: LoadField: r1 = r6->field_b
    //     0x9ad59c: ldur            w1, [x6, #0xb]
    // 0x9ad5a0: r7 = LoadInt32Instr(r0)
    //     0x9ad5a0: sbfx            x7, x0, #1, #0x1f
    //     0x9ad5a4: tbz             w0, #0, #0x9ad5ac
    //     0x9ad5a8: ldur            x7, [x0, #7]
    // 0x9ad5ac: r0 = LoadInt32Instr(r1)
    //     0x9ad5ac: sbfx            x0, x1, #1, #0x1f
    // 0x9ad5b0: mov             x1, x7
    // 0x9ad5b4: cmp             x1, x0
    // 0x9ad5b8: b.hs            #0x9adb14
    // 0x9ad5bc: LoadField: r0 = r6->field_f
    //     0x9ad5bc: ldur            w0, [x6, #0xf]
    // 0x9ad5c0: DecompressPointer r0
    //     0x9ad5c0: add             x0, x0, HEAP, lsl #32
    // 0x9ad5c4: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x9ad5c4: add             x16, x0, x7, lsl #2
    //     0x9ad5c8: ldur            w1, [x16, #0xf]
    // 0x9ad5cc: DecompressPointer r1
    //     0x9ad5cc: add             x1, x1, HEAP, lsl #32
    // 0x9ad5d0: LoadField: r0 = r1->field_af
    //     0x9ad5d0: ldur            w0, [x1, #0xaf]
    // 0x9ad5d4: DecompressPointer r0
    //     0x9ad5d4: add             x0, x0, HEAP, lsl #32
    // 0x9ad5d8: cmp             w0, NULL
    // 0x9ad5dc: b.ne            #0x9ad5e8
    // 0x9ad5e0: r6 = ""
    //     0x9ad5e0: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ad5e4: b               #0x9ad5ec
    // 0x9ad5e8: mov             x6, x0
    // 0x9ad5ec: ldur            x0, [fp, #-8]
    // 0x9ad5f0: stur            x6, [fp, #-0x10]
    // 0x9ad5f4: LoadField: r1 = r0->field_f
    //     0x9ad5f4: ldur            w1, [x0, #0xf]
    // 0x9ad5f8: DecompressPointer r1
    //     0x9ad5f8: add             x1, x1, HEAP, lsl #32
    // 0x9ad5fc: cmp             w1, NULL
    // 0x9ad600: b.eq            #0x9adb18
    // 0x9ad604: r0 = of()
    //     0x9ad604: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ad608: LoadField: r1 = r0->field_87
    //     0x9ad608: ldur            w1, [x0, #0x87]
    // 0x9ad60c: DecompressPointer r1
    //     0x9ad60c: add             x1, x1, HEAP, lsl #32
    // 0x9ad610: LoadField: r0 = r1->field_7
    //     0x9ad610: ldur            w0, [x1, #7]
    // 0x9ad614: DecompressPointer r0
    //     0x9ad614: add             x0, x0, HEAP, lsl #32
    // 0x9ad618: stur            x0, [fp, #-0x20]
    // 0x9ad61c: r1 = Instance_Color
    //     0x9ad61c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9ad620: d0 = 0.700000
    //     0x9ad620: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9ad624: ldr             d0, [x17, #0xf48]
    // 0x9ad628: r0 = withOpacity()
    //     0x9ad628: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9ad62c: r16 = 14.000000
    //     0x9ad62c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x9ad630: ldr             x16, [x16, #0x1d8]
    // 0x9ad634: stp             x0, x16, [SP]
    // 0x9ad638: ldur            x1, [fp, #-0x20]
    // 0x9ad63c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ad63c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ad640: ldr             x4, [x4, #0xaa0]
    // 0x9ad644: r0 = copyWith()
    //     0x9ad644: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ad648: stur            x0, [fp, #-0x20]
    // 0x9ad64c: r0 = Text()
    //     0x9ad64c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ad650: mov             x1, x0
    // 0x9ad654: ldur            x0, [fp, #-0x10]
    // 0x9ad658: stur            x1, [fp, #-0x48]
    // 0x9ad65c: StoreField: r1->field_b = r0
    //     0x9ad65c: stur            w0, [x1, #0xb]
    // 0x9ad660: ldur            x0, [fp, #-0x20]
    // 0x9ad664: StoreField: r1->field_13 = r0
    //     0x9ad664: stur            w0, [x1, #0x13]
    // 0x9ad668: r0 = Instance_TextOverflow
    //     0x9ad668: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x9ad66c: ldr             x0, [x0, #0xe10]
    // 0x9ad670: StoreField: r1->field_2b = r0
    //     0x9ad670: stur            w0, [x1, #0x2b]
    // 0x9ad674: r2 = 2
    //     0x9ad674: movz            x2, #0x2
    // 0x9ad678: StoreField: r1->field_37 = r2
    //     0x9ad678: stur            w2, [x1, #0x37]
    // 0x9ad67c: r0 = Padding()
    //     0x9ad67c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ad680: mov             x3, x0
    // 0x9ad684: r2 = Instance_EdgeInsets
    //     0x9ad684: add             x2, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x9ad688: ldr             x2, [x2, #0x770]
    // 0x9ad68c: stur            x3, [fp, #-0x20]
    // 0x9ad690: StoreField: r3->field_f = r2
    //     0x9ad690: stur            w2, [x3, #0xf]
    // 0x9ad694: ldur            x0, [fp, #-0x48]
    // 0x9ad698: StoreField: r3->field_b = r0
    //     0x9ad698: stur            w0, [x3, #0xb]
    // 0x9ad69c: ldur            x4, [fp, #-0x38]
    // 0x9ad6a0: LoadField: r5 = r4->field_13
    //     0x9ad6a0: ldur            w5, [x4, #0x13]
    // 0x9ad6a4: DecompressPointer r5
    //     0x9ad6a4: add             x5, x5, HEAP, lsl #32
    // 0x9ad6a8: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x9ad6a8: ldur            w0, [x4, #0x17]
    // 0x9ad6ac: DecompressPointer r0
    //     0x9ad6ac: add             x0, x0, HEAP, lsl #32
    // 0x9ad6b0: LoadField: r1 = r5->field_b
    //     0x9ad6b0: ldur            w1, [x5, #0xb]
    // 0x9ad6b4: r6 = LoadInt32Instr(r0)
    //     0x9ad6b4: sbfx            x6, x0, #1, #0x1f
    //     0x9ad6b8: tbz             w0, #0, #0x9ad6c0
    //     0x9ad6bc: ldur            x6, [x0, #7]
    // 0x9ad6c0: r0 = LoadInt32Instr(r1)
    //     0x9ad6c0: sbfx            x0, x1, #1, #0x1f
    // 0x9ad6c4: mov             x1, x6
    // 0x9ad6c8: cmp             x1, x0
    // 0x9ad6cc: b.hs            #0x9adb1c
    // 0x9ad6d0: LoadField: r0 = r5->field_f
    //     0x9ad6d0: ldur            w0, [x5, #0xf]
    // 0x9ad6d4: DecompressPointer r0
    //     0x9ad6d4: add             x0, x0, HEAP, lsl #32
    // 0x9ad6d8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x9ad6d8: add             x16, x0, x6, lsl #2
    //     0x9ad6dc: ldur            w1, [x16, #0xf]
    // 0x9ad6e0: DecompressPointer r1
    //     0x9ad6e0: add             x1, x1, HEAP, lsl #32
    // 0x9ad6e4: LoadField: r0 = r1->field_b3
    //     0x9ad6e4: ldur            w0, [x1, #0xb3]
    // 0x9ad6e8: DecompressPointer r0
    //     0x9ad6e8: add             x0, x0, HEAP, lsl #32
    // 0x9ad6ec: cmp             w0, NULL
    // 0x9ad6f0: b.ne            #0x9ad6fc
    // 0x9ad6f4: r7 = ""
    //     0x9ad6f4: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ad6f8: b               #0x9ad700
    // 0x9ad6fc: mov             x7, x0
    // 0x9ad700: ldur            x5, [fp, #-8]
    // 0x9ad704: ldur            x6, [fp, #-0x28]
    // 0x9ad708: ldur            x0, [fp, #-0x30]
    // 0x9ad70c: stur            x7, [fp, #-0x10]
    // 0x9ad710: LoadField: r1 = r5->field_f
    //     0x9ad710: ldur            w1, [x5, #0xf]
    // 0x9ad714: DecompressPointer r1
    //     0x9ad714: add             x1, x1, HEAP, lsl #32
    // 0x9ad718: cmp             w1, NULL
    // 0x9ad71c: b.eq            #0x9adb20
    // 0x9ad720: r0 = of()
    //     0x9ad720: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ad724: LoadField: r1 = r0->field_87
    //     0x9ad724: ldur            w1, [x0, #0x87]
    // 0x9ad728: DecompressPointer r1
    //     0x9ad728: add             x1, x1, HEAP, lsl #32
    // 0x9ad72c: LoadField: r0 = r1->field_7
    //     0x9ad72c: ldur            w0, [x1, #7]
    // 0x9ad730: DecompressPointer r0
    //     0x9ad730: add             x0, x0, HEAP, lsl #32
    // 0x9ad734: r16 = 12.000000
    //     0x9ad734: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9ad738: ldr             x16, [x16, #0x9e8]
    // 0x9ad73c: r30 = Instance_Color
    //     0x9ad73c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9ad740: ldr             lr, [lr, #0x858]
    // 0x9ad744: stp             lr, x16, [SP]
    // 0x9ad748: mov             x1, x0
    // 0x9ad74c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ad74c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ad750: ldr             x4, [x4, #0xaa0]
    // 0x9ad754: r0 = copyWith()
    //     0x9ad754: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ad758: stur            x0, [fp, #-0x48]
    // 0x9ad75c: r0 = Text()
    //     0x9ad75c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ad760: mov             x1, x0
    // 0x9ad764: ldur            x0, [fp, #-0x10]
    // 0x9ad768: stur            x1, [fp, #-0x50]
    // 0x9ad76c: StoreField: r1->field_b = r0
    //     0x9ad76c: stur            w0, [x1, #0xb]
    // 0x9ad770: ldur            x0, [fp, #-0x48]
    // 0x9ad774: StoreField: r1->field_13 = r0
    //     0x9ad774: stur            w0, [x1, #0x13]
    // 0x9ad778: r0 = Instance_TextOverflow
    //     0x9ad778: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x9ad77c: ldr             x0, [x0, #0xe10]
    // 0x9ad780: StoreField: r1->field_2b = r0
    //     0x9ad780: stur            w0, [x1, #0x2b]
    // 0x9ad784: r0 = 2
    //     0x9ad784: movz            x0, #0x2
    // 0x9ad788: StoreField: r1->field_37 = r0
    //     0x9ad788: stur            w0, [x1, #0x37]
    // 0x9ad78c: r0 = Padding()
    //     0x9ad78c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ad790: mov             x3, x0
    // 0x9ad794: r0 = Instance_EdgeInsets
    //     0x9ad794: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x9ad798: ldr             x0, [x0, #0x770]
    // 0x9ad79c: stur            x3, [fp, #-0x10]
    // 0x9ad7a0: StoreField: r3->field_f = r0
    //     0x9ad7a0: stur            w0, [x3, #0xf]
    // 0x9ad7a4: ldur            x0, [fp, #-0x50]
    // 0x9ad7a8: StoreField: r3->field_b = r0
    //     0x9ad7a8: stur            w0, [x3, #0xb]
    // 0x9ad7ac: r1 = Null
    //     0x9ad7ac: mov             x1, NULL
    // 0x9ad7b0: r2 = 6
    //     0x9ad7b0: movz            x2, #0x6
    // 0x9ad7b4: r0 = AllocateArray()
    //     0x9ad7b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ad7b8: mov             x2, x0
    // 0x9ad7bc: ldur            x0, [fp, #-0x30]
    // 0x9ad7c0: stur            x2, [fp, #-0x48]
    // 0x9ad7c4: StoreField: r2->field_f = r0
    //     0x9ad7c4: stur            w0, [x2, #0xf]
    // 0x9ad7c8: ldur            x0, [fp, #-0x20]
    // 0x9ad7cc: StoreField: r2->field_13 = r0
    //     0x9ad7cc: stur            w0, [x2, #0x13]
    // 0x9ad7d0: ldur            x0, [fp, #-0x10]
    // 0x9ad7d4: ArrayStore: r2[0] = r0  ; List_4
    //     0x9ad7d4: stur            w0, [x2, #0x17]
    // 0x9ad7d8: r1 = <Widget>
    //     0x9ad7d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ad7dc: r0 = AllocateGrowableArray()
    //     0x9ad7dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ad7e0: mov             x1, x0
    // 0x9ad7e4: ldur            x0, [fp, #-0x48]
    // 0x9ad7e8: stur            x1, [fp, #-0x10]
    // 0x9ad7ec: StoreField: r1->field_f = r0
    //     0x9ad7ec: stur            w0, [x1, #0xf]
    // 0x9ad7f0: r2 = 6
    //     0x9ad7f0: movz            x2, #0x6
    // 0x9ad7f4: StoreField: r1->field_b = r2
    //     0x9ad7f4: stur            w2, [x1, #0xb]
    // 0x9ad7f8: r0 = Column()
    //     0x9ad7f8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9ad7fc: mov             x1, x0
    // 0x9ad800: r0 = Instance_Axis
    //     0x9ad800: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9ad804: stur            x1, [fp, #-0x20]
    // 0x9ad808: StoreField: r1->field_f = r0
    //     0x9ad808: stur            w0, [x1, #0xf]
    // 0x9ad80c: r0 = Instance_MainAxisAlignment
    //     0x9ad80c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9ad810: ldr             x0, [x0, #0xab0]
    // 0x9ad814: StoreField: r1->field_13 = r0
    //     0x9ad814: stur            w0, [x1, #0x13]
    // 0x9ad818: r2 = Instance_MainAxisSize
    //     0x9ad818: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ad81c: ldr             x2, [x2, #0xa10]
    // 0x9ad820: ArrayStore: r1[0] = r2  ; List_4
    //     0x9ad820: stur            w2, [x1, #0x17]
    // 0x9ad824: r3 = Instance_CrossAxisAlignment
    //     0x9ad824: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9ad828: ldr             x3, [x3, #0x890]
    // 0x9ad82c: StoreField: r1->field_1b = r3
    //     0x9ad82c: stur            w3, [x1, #0x1b]
    // 0x9ad830: r4 = Instance_VerticalDirection
    //     0x9ad830: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ad834: ldr             x4, [x4, #0xa20]
    // 0x9ad838: StoreField: r1->field_23 = r4
    //     0x9ad838: stur            w4, [x1, #0x23]
    // 0x9ad83c: r5 = Instance_Clip
    //     0x9ad83c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ad840: ldr             x5, [x5, #0x38]
    // 0x9ad844: StoreField: r1->field_2b = r5
    //     0x9ad844: stur            w5, [x1, #0x2b]
    // 0x9ad848: StoreField: r1->field_2f = rZR
    //     0x9ad848: stur            xzr, [x1, #0x2f]
    // 0x9ad84c: ldur            x6, [fp, #-0x10]
    // 0x9ad850: StoreField: r1->field_b = r6
    //     0x9ad850: stur            w6, [x1, #0xb]
    // 0x9ad854: r0 = Padding()
    //     0x9ad854: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ad858: mov             x2, x0
    // 0x9ad85c: r0 = Instance_EdgeInsets
    //     0x9ad85c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x9ad860: ldr             x0, [x0, #0xa78]
    // 0x9ad864: stur            x2, [fp, #-0x10]
    // 0x9ad868: StoreField: r2->field_f = r0
    //     0x9ad868: stur            w0, [x2, #0xf]
    // 0x9ad86c: ldur            x0, [fp, #-0x20]
    // 0x9ad870: StoreField: r2->field_b = r0
    //     0x9ad870: stur            w0, [x2, #0xb]
    // 0x9ad874: r1 = <FlexParentData>
    //     0x9ad874: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x9ad878: ldr             x1, [x1, #0xe00]
    // 0x9ad87c: r0 = Expanded()
    //     0x9ad87c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x9ad880: mov             x2, x0
    // 0x9ad884: r0 = 1
    //     0x9ad884: movz            x0, #0x1
    // 0x9ad888: stur            x2, [fp, #-0x20]
    // 0x9ad88c: StoreField: r2->field_13 = r0
    //     0x9ad88c: stur            x0, [x2, #0x13]
    // 0x9ad890: r0 = Instance_FlexFit
    //     0x9ad890: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x9ad894: ldr             x0, [x0, #0xe08]
    // 0x9ad898: StoreField: r2->field_1b = r0
    //     0x9ad898: stur            w0, [x2, #0x1b]
    // 0x9ad89c: ldur            x0, [fp, #-0x10]
    // 0x9ad8a0: StoreField: r2->field_b = r0
    //     0x9ad8a0: stur            w0, [x2, #0xb]
    // 0x9ad8a4: ldur            x0, [fp, #-8]
    // 0x9ad8a8: LoadField: r1 = r0->field_f
    //     0x9ad8a8: ldur            w1, [x0, #0xf]
    // 0x9ad8ac: DecompressPointer r1
    //     0x9ad8ac: add             x1, x1, HEAP, lsl #32
    // 0x9ad8b0: cmp             w1, NULL
    // 0x9ad8b4: b.eq            #0x9adb24
    // 0x9ad8b8: r0 = of()
    //     0x9ad8b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ad8bc: LoadField: r1 = r0->field_87
    //     0x9ad8bc: ldur            w1, [x0, #0x87]
    // 0x9ad8c0: DecompressPointer r1
    //     0x9ad8c0: add             x1, x1, HEAP, lsl #32
    // 0x9ad8c4: LoadField: r0 = r1->field_7
    //     0x9ad8c4: ldur            w0, [x1, #7]
    // 0x9ad8c8: DecompressPointer r0
    //     0x9ad8c8: add             x0, x0, HEAP, lsl #32
    // 0x9ad8cc: r16 = 12.000000
    //     0x9ad8cc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9ad8d0: ldr             x16, [x16, #0x9e8]
    // 0x9ad8d4: r30 = Instance_Color
    //     0x9ad8d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9ad8d8: ldr             lr, [lr, #0x858]
    // 0x9ad8dc: stp             lr, x16, [SP]
    // 0x9ad8e0: mov             x1, x0
    // 0x9ad8e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ad8e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ad8e8: ldr             x4, [x4, #0xaa0]
    // 0x9ad8ec: r0 = copyWith()
    //     0x9ad8ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ad8f0: stur            x0, [fp, #-8]
    // 0x9ad8f4: r0 = Text()
    //     0x9ad8f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ad8f8: mov             x1, x0
    // 0x9ad8fc: r0 = "VIEW"
    //     0x9ad8fc: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb40] "VIEW"
    //     0x9ad900: ldr             x0, [x0, #0xb40]
    // 0x9ad904: stur            x1, [fp, #-0x10]
    // 0x9ad908: StoreField: r1->field_b = r0
    //     0x9ad908: stur            w0, [x1, #0xb]
    // 0x9ad90c: ldur            x0, [fp, #-8]
    // 0x9ad910: StoreField: r1->field_13 = r0
    //     0x9ad910: stur            w0, [x1, #0x13]
    // 0x9ad914: r0 = InkWell()
    //     0x9ad914: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9ad918: mov             x3, x0
    // 0x9ad91c: ldur            x0, [fp, #-0x10]
    // 0x9ad920: stur            x3, [fp, #-8]
    // 0x9ad924: StoreField: r3->field_b = r0
    //     0x9ad924: stur            w0, [x3, #0xb]
    // 0x9ad928: ldur            x2, [fp, #-0x38]
    // 0x9ad92c: r1 = Function '<anonymous closure>':.
    //     0x9ad92c: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb48] AnonymousClosure: (0x9add94), in [package:customer_app/app/presentation/custom_widgets/product_detail/sale_activity_item_view.dart] _SaleActivityItemViewState::slider (0x9ad30c)
    //     0x9ad930: ldr             x1, [x1, #0xb48]
    // 0x9ad934: r0 = AllocateClosure()
    //     0x9ad934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ad938: mov             x1, x0
    // 0x9ad93c: ldur            x0, [fp, #-8]
    // 0x9ad940: StoreField: r0->field_f = r1
    //     0x9ad940: stur            w1, [x0, #0xf]
    // 0x9ad944: r1 = true
    //     0x9ad944: add             x1, NULL, #0x20  ; true
    // 0x9ad948: StoreField: r0->field_43 = r1
    //     0x9ad948: stur            w1, [x0, #0x43]
    // 0x9ad94c: r2 = Instance_BoxShape
    //     0x9ad94c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9ad950: ldr             x2, [x2, #0x80]
    // 0x9ad954: StoreField: r0->field_47 = r2
    //     0x9ad954: stur            w2, [x0, #0x47]
    // 0x9ad958: StoreField: r0->field_6f = r1
    //     0x9ad958: stur            w1, [x0, #0x6f]
    // 0x9ad95c: r2 = false
    //     0x9ad95c: add             x2, NULL, #0x30  ; false
    // 0x9ad960: StoreField: r0->field_73 = r2
    //     0x9ad960: stur            w2, [x0, #0x73]
    // 0x9ad964: StoreField: r0->field_83 = r1
    //     0x9ad964: stur            w1, [x0, #0x83]
    // 0x9ad968: StoreField: r0->field_7b = r2
    //     0x9ad968: stur            w2, [x0, #0x7b]
    // 0x9ad96c: r0 = Center()
    //     0x9ad96c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9ad970: mov             x3, x0
    // 0x9ad974: r0 = Instance_Alignment
    //     0x9ad974: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ad978: ldr             x0, [x0, #0xb10]
    // 0x9ad97c: stur            x3, [fp, #-0x10]
    // 0x9ad980: StoreField: r3->field_f = r0
    //     0x9ad980: stur            w0, [x3, #0xf]
    // 0x9ad984: ldur            x1, [fp, #-8]
    // 0x9ad988: StoreField: r3->field_b = r1
    //     0x9ad988: stur            w1, [x3, #0xb]
    // 0x9ad98c: r1 = Null
    //     0x9ad98c: mov             x1, NULL
    // 0x9ad990: r2 = 6
    //     0x9ad990: movz            x2, #0x6
    // 0x9ad994: r0 = AllocateArray()
    //     0x9ad994: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ad998: mov             x2, x0
    // 0x9ad99c: ldur            x0, [fp, #-0x28]
    // 0x9ad9a0: stur            x2, [fp, #-8]
    // 0x9ad9a4: StoreField: r2->field_f = r0
    //     0x9ad9a4: stur            w0, [x2, #0xf]
    // 0x9ad9a8: ldur            x0, [fp, #-0x20]
    // 0x9ad9ac: StoreField: r2->field_13 = r0
    //     0x9ad9ac: stur            w0, [x2, #0x13]
    // 0x9ad9b0: ldur            x0, [fp, #-0x10]
    // 0x9ad9b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x9ad9b4: stur            w0, [x2, #0x17]
    // 0x9ad9b8: r1 = <Widget>
    //     0x9ad9b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ad9bc: r0 = AllocateGrowableArray()
    //     0x9ad9bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ad9c0: mov             x1, x0
    // 0x9ad9c4: ldur            x0, [fp, #-8]
    // 0x9ad9c8: stur            x1, [fp, #-0x10]
    // 0x9ad9cc: StoreField: r1->field_f = r0
    //     0x9ad9cc: stur            w0, [x1, #0xf]
    // 0x9ad9d0: r0 = 6
    //     0x9ad9d0: movz            x0, #0x6
    // 0x9ad9d4: StoreField: r1->field_b = r0
    //     0x9ad9d4: stur            w0, [x1, #0xb]
    // 0x9ad9d8: r0 = Row()
    //     0x9ad9d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9ad9dc: mov             x1, x0
    // 0x9ad9e0: r0 = Instance_Axis
    //     0x9ad9e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9ad9e4: stur            x1, [fp, #-8]
    // 0x9ad9e8: StoreField: r1->field_f = r0
    //     0x9ad9e8: stur            w0, [x1, #0xf]
    // 0x9ad9ec: r0 = Instance_MainAxisAlignment
    //     0x9ad9ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9ad9f0: ldr             x0, [x0, #0xab0]
    // 0x9ad9f4: StoreField: r1->field_13 = r0
    //     0x9ad9f4: stur            w0, [x1, #0x13]
    // 0x9ad9f8: r0 = Instance_MainAxisSize
    //     0x9ad9f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ad9fc: ldr             x0, [x0, #0xa10]
    // 0x9ada00: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ada00: stur            w0, [x1, #0x17]
    // 0x9ada04: r0 = Instance_CrossAxisAlignment
    //     0x9ada04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9ada08: ldr             x0, [x0, #0x890]
    // 0x9ada0c: StoreField: r1->field_1b = r0
    //     0x9ada0c: stur            w0, [x1, #0x1b]
    // 0x9ada10: r0 = Instance_VerticalDirection
    //     0x9ada10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ada14: ldr             x0, [x0, #0xa20]
    // 0x9ada18: StoreField: r1->field_23 = r0
    //     0x9ada18: stur            w0, [x1, #0x23]
    // 0x9ada1c: r0 = Instance_Clip
    //     0x9ada1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ada20: ldr             x0, [x0, #0x38]
    // 0x9ada24: StoreField: r1->field_2b = r0
    //     0x9ada24: stur            w0, [x1, #0x2b]
    // 0x9ada28: StoreField: r1->field_2f = rZR
    //     0x9ada28: stur            xzr, [x1, #0x2f]
    // 0x9ada2c: ldur            x0, [fp, #-0x10]
    // 0x9ada30: StoreField: r1->field_b = r0
    //     0x9ada30: stur            w0, [x1, #0xb]
    // 0x9ada34: r0 = Padding()
    //     0x9ada34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ada38: mov             x1, x0
    // 0x9ada3c: r0 = Instance_EdgeInsets
    //     0x9ada3c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9ada40: ldr             x0, [x0, #0x1f0]
    // 0x9ada44: stur            x1, [fp, #-0x10]
    // 0x9ada48: StoreField: r1->field_f = r0
    //     0x9ada48: stur            w0, [x1, #0xf]
    // 0x9ada4c: ldur            x0, [fp, #-8]
    // 0x9ada50: StoreField: r1->field_b = r0
    //     0x9ada50: stur            w0, [x1, #0xb]
    // 0x9ada54: r0 = Center()
    //     0x9ada54: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9ada58: mov             x1, x0
    // 0x9ada5c: r0 = Instance_Alignment
    //     0x9ada5c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ada60: ldr             x0, [x0, #0xb10]
    // 0x9ada64: stur            x1, [fp, #-8]
    // 0x9ada68: StoreField: r1->field_f = r0
    //     0x9ada68: stur            w0, [x1, #0xf]
    // 0x9ada6c: ldur            x0, [fp, #-0x10]
    // 0x9ada70: StoreField: r1->field_b = r0
    //     0x9ada70: stur            w0, [x1, #0xb]
    // 0x9ada74: r0 = Container()
    //     0x9ada74: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9ada78: stur            x0, [fp, #-0x10]
    // 0x9ada7c: ldur            x16, [fp, #-0x40]
    // 0x9ada80: ldur            lr, [fp, #-8]
    // 0x9ada84: stp             lr, x16, [SP]
    // 0x9ada88: mov             x1, x0
    // 0x9ada8c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x9ada8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x9ada90: ldr             x4, [x4, #0x88]
    // 0x9ada94: r0 = Container()
    //     0x9ada94: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9ada98: r0 = Padding()
    //     0x9ada98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ada9c: mov             x1, x0
    // 0x9adaa0: r0 = Instance_EdgeInsets
    //     0x9adaa0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0x9adaa4: ldr             x0, [x0, #0x100]
    // 0x9adaa8: stur            x1, [fp, #-8]
    // 0x9adaac: StoreField: r1->field_f = r0
    //     0x9adaac: stur            w0, [x1, #0xf]
    // 0x9adab0: ldur            x0, [fp, #-0x10]
    // 0x9adab4: StoreField: r1->field_b = r0
    //     0x9adab4: stur            w0, [x1, #0xb]
    // 0x9adab8: r0 = AnimatedContainer()
    //     0x9adab8: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0x9adabc: stur            x0, [fp, #-0x10]
    // 0x9adac0: r16 = Instance_Cubic
    //     0x9adac0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0x9adac4: ldr             x16, [x16, #0xaf8]
    // 0x9adac8: r30 = Instance_EdgeInsets
    //     0x9adac8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0x9adacc: ldr             lr, [lr, #0xb30]
    // 0x9adad0: stp             lr, x16, [SP]
    // 0x9adad4: mov             x1, x0
    // 0x9adad8: ldur            x2, [fp, #-8]
    // 0x9adadc: r3 = Instance_Duration
    //     0x9adadc: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0x9adae0: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, margin, 0x4, null]
    //     0x9adae0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52218] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "margin", 0x4, Null]
    //     0x9adae4: ldr             x4, [x4, #0x218]
    // 0x9adae8: r0 = AnimatedContainer()
    //     0x9adae8: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0x9adaec: ldur            x0, [fp, #-0x10]
    // 0x9adaf0: LeaveFrame
    //     0x9adaf0: mov             SP, fp
    //     0x9adaf4: ldp             fp, lr, [SP], #0x10
    // 0x9adaf8: ret
    //     0x9adaf8: ret             
    // 0x9adafc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9adafc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9adb00: b               #0x9ad338
    // 0x9adb04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9adb04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9adb08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9adb08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9adb0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9adb0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9adb10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9adb10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9adb14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9adb14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9adb18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9adb18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9adb1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9adb1c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9adb20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9adb20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9adb24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9adb24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9add94, size: 0x114
    // 0x9add94: EnterFrame
    //     0x9add94: stp             fp, lr, [SP, #-0x10]!
    //     0x9add98: mov             fp, SP
    // 0x9add9c: AllocStack(0x28)
    //     0x9add9c: sub             SP, SP, #0x28
    // 0x9adda0: SetupParameters()
    //     0x9adda0: ldr             x0, [fp, #0x10]
    //     0x9adda4: ldur            w1, [x0, #0x17]
    //     0x9adda8: add             x1, x1, HEAP, lsl #32
    // 0x9addac: CheckStackOverflow
    //     0x9addac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9addb0: cmp             SP, x16
    //     0x9addb4: b.ls            #0x9ade98
    // 0x9addb8: LoadField: r0 = r1->field_f
    //     0x9addb8: ldur            w0, [x1, #0xf]
    // 0x9addbc: DecompressPointer r0
    //     0x9addbc: add             x0, x0, HEAP, lsl #32
    // 0x9addc0: LoadField: r2 = r0->field_b
    //     0x9addc0: ldur            w2, [x0, #0xb]
    // 0x9addc4: DecompressPointer r2
    //     0x9addc4: add             x2, x2, HEAP, lsl #32
    // 0x9addc8: cmp             w2, NULL
    // 0x9addcc: b.eq            #0x9adea0
    // 0x9addd0: LoadField: r3 = r1->field_13
    //     0x9addd0: ldur            w3, [x1, #0x13]
    // 0x9addd4: DecompressPointer r3
    //     0x9addd4: add             x3, x3, HEAP, lsl #32
    // 0x9addd8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9addd8: ldur            w0, [x1, #0x17]
    // 0x9adddc: DecompressPointer r0
    //     0x9adddc: add             x0, x0, HEAP, lsl #32
    // 0x9adde0: LoadField: r1 = r3->field_b
    //     0x9adde0: ldur            w1, [x3, #0xb]
    // 0x9adde4: r4 = LoadInt32Instr(r0)
    //     0x9adde4: sbfx            x4, x0, #1, #0x1f
    //     0x9adde8: tbz             w0, #0, #0x9addf0
    //     0x9addec: ldur            x4, [x0, #7]
    // 0x9addf0: r0 = LoadInt32Instr(r1)
    //     0x9addf0: sbfx            x0, x1, #1, #0x1f
    // 0x9addf4: mov             x1, x4
    // 0x9addf8: cmp             x1, x0
    // 0x9addfc: b.hs            #0x9adea4
    // 0x9ade00: LoadField: r0 = r3->field_f
    //     0x9ade00: ldur            w0, [x3, #0xf]
    // 0x9ade04: DecompressPointer r0
    //     0x9ade04: add             x0, x0, HEAP, lsl #32
    // 0x9ade08: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9ade08: add             x16, x0, x4, lsl #2
    //     0x9ade0c: ldur            w1, [x16, #0xf]
    // 0x9ade10: DecompressPointer r1
    //     0x9ade10: add             x1, x1, HEAP, lsl #32
    // 0x9ade14: r17 = 287
    //     0x9ade14: movz            x17, #0x11f
    // 0x9ade18: ldr             w0, [x1, x17]
    // 0x9ade1c: DecompressPointer r0
    //     0x9ade1c: add             x0, x0, HEAP, lsl #32
    // 0x9ade20: cmp             w0, NULL
    // 0x9ade24: b.ne            #0x9ade2c
    // 0x9ade28: r0 = ""
    //     0x9ade28: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ade2c: r17 = 291
    //     0x9ade2c: movz            x17, #0x123
    // 0x9ade30: ldr             w3, [x1, x17]
    // 0x9ade34: DecompressPointer r3
    //     0x9ade34: add             x3, x3, HEAP, lsl #32
    // 0x9ade38: cmp             w3, NULL
    // 0x9ade3c: b.ne            #0x9ade48
    // 0x9ade40: r1 = ""
    //     0x9ade40: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ade44: b               #0x9ade4c
    // 0x9ade48: mov             x1, x3
    // 0x9ade4c: LoadField: r3 = r2->field_13
    //     0x9ade4c: ldur            w3, [x2, #0x13]
    // 0x9ade50: DecompressPointer r3
    //     0x9ade50: add             x3, x3, HEAP, lsl #32
    // 0x9ade54: LoadField: r4 = r2->field_2f
    //     0x9ade54: ldur            w4, [x2, #0x2f]
    // 0x9ade58: DecompressPointer r4
    //     0x9ade58: add             x4, x4, HEAP, lsl #32
    // 0x9ade5c: stp             x0, x4, [SP, #0x18]
    // 0x9ade60: r16 = "product_page"
    //     0x9ade60: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x9ade64: ldr             x16, [x16, #0x480]
    // 0x9ade68: stp             x16, x1, [SP, #8]
    // 0x9ade6c: str             x3, [SP]
    // 0x9ade70: r4 = 0
    //     0x9ade70: movz            x4, #0
    // 0x9ade74: ldr             x0, [SP, #0x20]
    // 0x9ade78: r16 = UnlinkedCall_0x613b5c
    //     0x9ade78: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bb50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9ade7c: add             x16, x16, #0xb50
    // 0x9ade80: ldp             x5, lr, [x16]
    // 0x9ade84: blr             lr
    // 0x9ade88: r0 = Null
    //     0x9ade88: mov             x0, NULL
    // 0x9ade8c: LeaveFrame
    //     0x9ade8c: mov             SP, fp
    //     0x9ade90: ldp             fp, lr, [SP], #0x10
    // 0x9ade94: ret
    //     0x9ade94: ret             
    // 0x9ade98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ade98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ade9c: b               #0x9addb8
    // 0x9adea0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9adea0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9adea4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9adea4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x9adea8, size: 0x84
    // 0x9adea8: EnterFrame
    //     0x9adea8: stp             fp, lr, [SP, #-0x10]!
    //     0x9adeac: mov             fp, SP
    // 0x9adeb0: AllocStack(0x10)
    //     0x9adeb0: sub             SP, SP, #0x10
    // 0x9adeb4: SetupParameters()
    //     0x9adeb4: ldr             x0, [fp, #0x18]
    //     0x9adeb8: ldur            w1, [x0, #0x17]
    //     0x9adebc: add             x1, x1, HEAP, lsl #32
    //     0x9adec0: stur            x1, [fp, #-8]
    // 0x9adec4: CheckStackOverflow
    //     0x9adec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9adec8: cmp             SP, x16
    //     0x9adecc: b.ls            #0x9adf24
    // 0x9aded0: r1 = 1
    //     0x9aded0: movz            x1, #0x1
    // 0x9aded4: r0 = AllocateContext()
    //     0x9aded4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9aded8: mov             x1, x0
    // 0x9adedc: ldur            x0, [fp, #-8]
    // 0x9adee0: StoreField: r1->field_b = r0
    //     0x9adee0: stur            w0, [x1, #0xb]
    // 0x9adee4: ldr             x2, [fp, #0x10]
    // 0x9adee8: StoreField: r1->field_f = r2
    //     0x9adee8: stur            w2, [x1, #0xf]
    // 0x9adeec: LoadField: r3 = r0->field_f
    //     0x9adeec: ldur            w3, [x0, #0xf]
    // 0x9adef0: DecompressPointer r3
    //     0x9adef0: add             x3, x3, HEAP, lsl #32
    // 0x9adef4: mov             x2, x1
    // 0x9adef8: stur            x3, [fp, #-0x10]
    // 0x9adefc: r1 = Function '<anonymous closure>':.
    //     0x9adefc: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb60] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0x9adf00: ldr             x1, [x1, #0xb60]
    // 0x9adf04: r0 = AllocateClosure()
    //     0x9adf04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9adf08: ldur            x1, [fp, #-0x10]
    // 0x9adf0c: mov             x2, x0
    // 0x9adf10: r0 = setState()
    //     0x9adf10: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9adf14: r0 = Null
    //     0x9adf14: mov             x0, NULL
    // 0x9adf18: LeaveFrame
    //     0x9adf18: mov             SP, fp
    //     0x9adf1c: ldp             fp, lr, [SP], #0x10
    // 0x9adf20: ret
    //     0x9adf20: ret             
    // 0x9adf24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9adf24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9adf28: b               #0x9aded0
  }
}

// class id: 4301, size: 0x34, field offset: 0xc
//   const constructor, 
class SaleActivityItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79e4c, size: 0x30
    // 0xc79e4c: EnterFrame
    //     0xc79e4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc79e50: mov             fp, SP
    // 0xc79e54: mov             x0, x1
    // 0xc79e58: r1 = <SaleActivityItemView>
    //     0xc79e58: add             x1, PP, #0x49, lsl #12  ; [pp+0x492b0] TypeArguments: <SaleActivityItemView>
    //     0xc79e5c: ldr             x1, [x1, #0x2b0]
    // 0xc79e60: r0 = _SaleActivityItemViewState()
    //     0xc79e60: bl              #0xc79e7c  ; Allocate_SaleActivityItemViewStateStub -> _SaleActivityItemViewState (size=0x20)
    // 0xc79e64: r1 = Sentinel
    //     0xc79e64: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc79e68: StoreField: r0->field_13 = r1
    //     0xc79e68: stur            w1, [x0, #0x13]
    // 0xc79e6c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc79e6c: stur            xzr, [x0, #0x17]
    // 0xc79e70: LeaveFrame
    //     0xc79e70: mov             SP, fp
    //     0xc79e74: ldp             fp, lr, [SP], #0x10
    // 0xc79e78: ret
    //     0xc79e78: ret             
  }
}
