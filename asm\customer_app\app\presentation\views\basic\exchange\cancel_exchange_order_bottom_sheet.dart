// lib: , url: package:customer_app/app/presentation/views/basic/exchange/cancel_exchange_order_bottom_sheet.dart

// class id: 1049146, size: 0x8
class :: {
}

// class id: 3522, size: 0x14, field offset: 0x14
class _CancelExchangeBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa40894, size: 0x8f4
    // 0xa40894: EnterFrame
    //     0xa40894: stp             fp, lr, [SP, #-0x10]!
    //     0xa40898: mov             fp, SP
    // 0xa4089c: AllocStack(0x58)
    //     0xa4089c: sub             SP, SP, #0x58
    // 0xa408a0: SetupParameters(_CancelExchangeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa408a0: mov             x0, x1
    //     0xa408a4: stur            x1, [fp, #-8]
    //     0xa408a8: mov             x1, x2
    //     0xa408ac: stur            x2, [fp, #-0x10]
    // 0xa408b0: CheckStackOverflow
    //     0xa408b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa408b4: cmp             SP, x16
    //     0xa408b8: b.ls            #0xa41174
    // 0xa408bc: r1 = 2
    //     0xa408bc: movz            x1, #0x2
    // 0xa408c0: r0 = AllocateContext()
    //     0xa408c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xa408c4: mov             x2, x0
    // 0xa408c8: ldur            x0, [fp, #-8]
    // 0xa408cc: stur            x2, [fp, #-0x20]
    // 0xa408d0: StoreField: r2->field_f = r0
    //     0xa408d0: stur            w0, [x2, #0xf]
    // 0xa408d4: ldur            x1, [fp, #-0x10]
    // 0xa408d8: StoreField: r2->field_13 = r1
    //     0xa408d8: stur            w1, [x2, #0x13]
    // 0xa408dc: LoadField: r3 = r0->field_b
    //     0xa408dc: ldur            w3, [x0, #0xb]
    // 0xa408e0: DecompressPointer r3
    //     0xa408e0: add             x3, x3, HEAP, lsl #32
    // 0xa408e4: cmp             w3, NULL
    // 0xa408e8: b.eq            #0xa4117c
    // 0xa408ec: LoadField: r4 = r3->field_b
    //     0xa408ec: ldur            w4, [x3, #0xb]
    // 0xa408f0: DecompressPointer r4
    //     0xa408f0: add             x4, x4, HEAP, lsl #32
    // 0xa408f4: LoadField: r3 = r4->field_7
    //     0xa408f4: ldur            w3, [x4, #7]
    // 0xa408f8: DecompressPointer r3
    //     0xa408f8: add             x3, x3, HEAP, lsl #32
    // 0xa408fc: cmp             w3, NULL
    // 0xa40900: b.ne            #0xa40908
    // 0xa40904: r3 = ""
    //     0xa40904: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa40908: stur            x3, [fp, #-0x18]
    // 0xa4090c: r0 = of()
    //     0xa4090c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40910: LoadField: r1 = r0->field_87
    //     0xa40910: ldur            w1, [x0, #0x87]
    // 0xa40914: DecompressPointer r1
    //     0xa40914: add             x1, x1, HEAP, lsl #32
    // 0xa40918: LoadField: r0 = r1->field_7
    //     0xa40918: ldur            w0, [x1, #7]
    // 0xa4091c: DecompressPointer r0
    //     0xa4091c: add             x0, x0, HEAP, lsl #32
    // 0xa40920: r16 = Instance_Color
    //     0xa40920: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa40924: r30 = 16.000000
    //     0xa40924: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa40928: ldr             lr, [lr, #0x188]
    // 0xa4092c: stp             lr, x16, [SP]
    // 0xa40930: mov             x1, x0
    // 0xa40934: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa40934: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa40938: ldr             x4, [x4, #0x9b8]
    // 0xa4093c: r0 = copyWith()
    //     0xa4093c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa40940: stur            x0, [fp, #-0x10]
    // 0xa40944: r0 = Text()
    //     0xa40944: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa40948: mov             x1, x0
    // 0xa4094c: ldur            x0, [fp, #-0x18]
    // 0xa40950: stur            x1, [fp, #-0x28]
    // 0xa40954: StoreField: r1->field_b = r0
    //     0xa40954: stur            w0, [x1, #0xb]
    // 0xa40958: ldur            x0, [fp, #-0x10]
    // 0xa4095c: StoreField: r1->field_13 = r0
    //     0xa4095c: stur            w0, [x1, #0x13]
    // 0xa40960: r0 = SvgPicture()
    //     0xa40960: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa40964: mov             x1, x0
    // 0xa40968: r2 = "assets/images/x.svg"
    //     0xa40968: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xa4096c: ldr             x2, [x2, #0x5e8]
    // 0xa40970: stur            x0, [fp, #-0x10]
    // 0xa40974: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa40974: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa40978: r0 = SvgPicture.asset()
    //     0xa40978: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa4097c: r0 = InkWell()
    //     0xa4097c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa40980: mov             x3, x0
    // 0xa40984: ldur            x0, [fp, #-0x10]
    // 0xa40988: stur            x3, [fp, #-0x18]
    // 0xa4098c: StoreField: r3->field_b = r0
    //     0xa4098c: stur            w0, [x3, #0xb]
    // 0xa40990: ldur            x2, [fp, #-0x20]
    // 0xa40994: r1 = Function '<anonymous closure>':.
    //     0xa40994: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a7c0] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xa40998: ldr             x1, [x1, #0x7c0]
    // 0xa4099c: r0 = AllocateClosure()
    //     0xa4099c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa409a0: mov             x1, x0
    // 0xa409a4: ldur            x0, [fp, #-0x18]
    // 0xa409a8: StoreField: r0->field_f = r1
    //     0xa409a8: stur            w1, [x0, #0xf]
    // 0xa409ac: r3 = true
    //     0xa409ac: add             x3, NULL, #0x20  ; true
    // 0xa409b0: StoreField: r0->field_43 = r3
    //     0xa409b0: stur            w3, [x0, #0x43]
    // 0xa409b4: r1 = Instance_BoxShape
    //     0xa409b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa409b8: ldr             x1, [x1, #0x80]
    // 0xa409bc: StoreField: r0->field_47 = r1
    //     0xa409bc: stur            w1, [x0, #0x47]
    // 0xa409c0: StoreField: r0->field_6f = r3
    //     0xa409c0: stur            w3, [x0, #0x6f]
    // 0xa409c4: r4 = false
    //     0xa409c4: add             x4, NULL, #0x30  ; false
    // 0xa409c8: StoreField: r0->field_73 = r4
    //     0xa409c8: stur            w4, [x0, #0x73]
    // 0xa409cc: StoreField: r0->field_83 = r3
    //     0xa409cc: stur            w3, [x0, #0x83]
    // 0xa409d0: StoreField: r0->field_7b = r4
    //     0xa409d0: stur            w4, [x0, #0x7b]
    // 0xa409d4: r1 = Null
    //     0xa409d4: mov             x1, NULL
    // 0xa409d8: r2 = 4
    //     0xa409d8: movz            x2, #0x4
    // 0xa409dc: r0 = AllocateArray()
    //     0xa409dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa409e0: mov             x2, x0
    // 0xa409e4: ldur            x0, [fp, #-0x28]
    // 0xa409e8: stur            x2, [fp, #-0x10]
    // 0xa409ec: StoreField: r2->field_f = r0
    //     0xa409ec: stur            w0, [x2, #0xf]
    // 0xa409f0: ldur            x0, [fp, #-0x18]
    // 0xa409f4: StoreField: r2->field_13 = r0
    //     0xa409f4: stur            w0, [x2, #0x13]
    // 0xa409f8: r1 = <Widget>
    //     0xa409f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa409fc: r0 = AllocateGrowableArray()
    //     0xa409fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa40a00: mov             x1, x0
    // 0xa40a04: ldur            x0, [fp, #-0x10]
    // 0xa40a08: stur            x1, [fp, #-0x18]
    // 0xa40a0c: StoreField: r1->field_f = r0
    //     0xa40a0c: stur            w0, [x1, #0xf]
    // 0xa40a10: r0 = 4
    //     0xa40a10: movz            x0, #0x4
    // 0xa40a14: StoreField: r1->field_b = r0
    //     0xa40a14: stur            w0, [x1, #0xb]
    // 0xa40a18: r0 = Row()
    //     0xa40a18: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa40a1c: mov             x2, x0
    // 0xa40a20: r0 = Instance_Axis
    //     0xa40a20: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa40a24: stur            x2, [fp, #-0x28]
    // 0xa40a28: StoreField: r2->field_f = r0
    //     0xa40a28: stur            w0, [x2, #0xf]
    // 0xa40a2c: r3 = Instance_MainAxisAlignment
    //     0xa40a2c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa40a30: ldr             x3, [x3, #0xa8]
    // 0xa40a34: StoreField: r2->field_13 = r3
    //     0xa40a34: stur            w3, [x2, #0x13]
    // 0xa40a38: r4 = Instance_MainAxisSize
    //     0xa40a38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa40a3c: ldr             x4, [x4, #0xa10]
    // 0xa40a40: ArrayStore: r2[0] = r4  ; List_4
    //     0xa40a40: stur            w4, [x2, #0x17]
    // 0xa40a44: r1 = Instance_CrossAxisAlignment
    //     0xa40a44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa40a48: ldr             x1, [x1, #0xa18]
    // 0xa40a4c: StoreField: r2->field_1b = r1
    //     0xa40a4c: stur            w1, [x2, #0x1b]
    // 0xa40a50: r5 = Instance_VerticalDirection
    //     0xa40a50: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa40a54: ldr             x5, [x5, #0xa20]
    // 0xa40a58: StoreField: r2->field_23 = r5
    //     0xa40a58: stur            w5, [x2, #0x23]
    // 0xa40a5c: r6 = Instance_Clip
    //     0xa40a5c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa40a60: ldr             x6, [x6, #0x38]
    // 0xa40a64: StoreField: r2->field_2b = r6
    //     0xa40a64: stur            w6, [x2, #0x2b]
    // 0xa40a68: StoreField: r2->field_2f = rZR
    //     0xa40a68: stur            xzr, [x2, #0x2f]
    // 0xa40a6c: ldur            x1, [fp, #-0x18]
    // 0xa40a70: StoreField: r2->field_b = r1
    //     0xa40a70: stur            w1, [x2, #0xb]
    // 0xa40a74: ldur            x7, [fp, #-8]
    // 0xa40a78: LoadField: r1 = r7->field_b
    //     0xa40a78: ldur            w1, [x7, #0xb]
    // 0xa40a7c: DecompressPointer r1
    //     0xa40a7c: add             x1, x1, HEAP, lsl #32
    // 0xa40a80: cmp             w1, NULL
    // 0xa40a84: b.eq            #0xa41180
    // 0xa40a88: LoadField: r8 = r1->field_b
    //     0xa40a88: ldur            w8, [x1, #0xb]
    // 0xa40a8c: DecompressPointer r8
    //     0xa40a8c: add             x8, x8, HEAP, lsl #32
    // 0xa40a90: LoadField: r1 = r8->field_f
    //     0xa40a90: ldur            w1, [x8, #0xf]
    // 0xa40a94: DecompressPointer r1
    //     0xa40a94: add             x1, x1, HEAP, lsl #32
    // 0xa40a98: cmp             w1, NULL
    // 0xa40a9c: b.ne            #0xa40aa8
    // 0xa40aa0: r9 = ""
    //     0xa40aa0: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa40aa4: b               #0xa40aac
    // 0xa40aa8: mov             x9, x1
    // 0xa40aac: ldur            x8, [fp, #-0x20]
    // 0xa40ab0: stur            x9, [fp, #-0x10]
    // 0xa40ab4: LoadField: r1 = r8->field_13
    //     0xa40ab4: ldur            w1, [x8, #0x13]
    // 0xa40ab8: DecompressPointer r1
    //     0xa40ab8: add             x1, x1, HEAP, lsl #32
    // 0xa40abc: r0 = of()
    //     0xa40abc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40ac0: LoadField: r1 = r0->field_87
    //     0xa40ac0: ldur            w1, [x0, #0x87]
    // 0xa40ac4: DecompressPointer r1
    //     0xa40ac4: add             x1, x1, HEAP, lsl #32
    // 0xa40ac8: LoadField: r0 = r1->field_2b
    //     0xa40ac8: ldur            w0, [x1, #0x2b]
    // 0xa40acc: DecompressPointer r0
    //     0xa40acc: add             x0, x0, HEAP, lsl #32
    // 0xa40ad0: stur            x0, [fp, #-0x18]
    // 0xa40ad4: r1 = Instance_Color
    //     0xa40ad4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa40ad8: d0 = 0.700000
    //     0xa40ad8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa40adc: ldr             d0, [x17, #0xf48]
    // 0xa40ae0: r0 = withOpacity()
    //     0xa40ae0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa40ae4: r16 = 12.000000
    //     0xa40ae4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa40ae8: ldr             x16, [x16, #0x9e8]
    // 0xa40aec: stp             x0, x16, [SP]
    // 0xa40af0: ldur            x1, [fp, #-0x18]
    // 0xa40af4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa40af4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa40af8: ldr             x4, [x4, #0xaa0]
    // 0xa40afc: r0 = copyWith()
    //     0xa40afc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa40b00: stur            x0, [fp, #-0x18]
    // 0xa40b04: r0 = HtmlWidget()
    //     0xa40b04: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xa40b08: mov             x1, x0
    // 0xa40b0c: ldur            x0, [fp, #-0x10]
    // 0xa40b10: stur            x1, [fp, #-0x30]
    // 0xa40b14: StoreField: r1->field_1f = r0
    //     0xa40b14: stur            w0, [x1, #0x1f]
    // 0xa40b18: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xa40b18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xa40b1c: ldr             x0, [x0, #0x1e0]
    // 0xa40b20: StoreField: r1->field_23 = r0
    //     0xa40b20: stur            w0, [x1, #0x23]
    // 0xa40b24: r0 = Instance_ColumnMode
    //     0xa40b24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xa40b28: ldr             x0, [x0, #0x1e8]
    // 0xa40b2c: StoreField: r1->field_3b = r0
    //     0xa40b2c: stur            w0, [x1, #0x3b]
    // 0xa40b30: ldur            x0, [fp, #-0x18]
    // 0xa40b34: StoreField: r1->field_3f = r0
    //     0xa40b34: stur            w0, [x1, #0x3f]
    // 0xa40b38: r16 = <EdgeInsets>
    //     0xa40b38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa40b3c: ldr             x16, [x16, #0xda0]
    // 0xa40b40: r30 = Instance_EdgeInsets
    //     0xa40b40: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa40b44: ldr             lr, [lr, #0x1f0]
    // 0xa40b48: stp             lr, x16, [SP]
    // 0xa40b4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa40b4c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa40b50: r0 = all()
    //     0xa40b50: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa40b54: stur            x0, [fp, #-0x10]
    // 0xa40b58: r16 = <Color>
    //     0xa40b58: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa40b5c: ldr             x16, [x16, #0xf80]
    // 0xa40b60: r30 = Instance_Color
    //     0xa40b60: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa40b64: stp             lr, x16, [SP]
    // 0xa40b68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa40b68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa40b6c: r0 = all()
    //     0xa40b6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa40b70: stur            x0, [fp, #-0x18]
    // 0xa40b74: r0 = Radius()
    //     0xa40b74: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa40b78: d0 = 12.000000
    //     0xa40b78: fmov            d0, #12.00000000
    // 0xa40b7c: stur            x0, [fp, #-0x38]
    // 0xa40b80: StoreField: r0->field_7 = d0
    //     0xa40b80: stur            d0, [x0, #7]
    // 0xa40b84: StoreField: r0->field_f = d0
    //     0xa40b84: stur            d0, [x0, #0xf]
    // 0xa40b88: r0 = BorderRadius()
    //     0xa40b88: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa40b8c: mov             x2, x0
    // 0xa40b90: ldur            x0, [fp, #-0x38]
    // 0xa40b94: stur            x2, [fp, #-0x40]
    // 0xa40b98: StoreField: r2->field_7 = r0
    //     0xa40b98: stur            w0, [x2, #7]
    // 0xa40b9c: StoreField: r2->field_b = r0
    //     0xa40b9c: stur            w0, [x2, #0xb]
    // 0xa40ba0: StoreField: r2->field_f = r0
    //     0xa40ba0: stur            w0, [x2, #0xf]
    // 0xa40ba4: StoreField: r2->field_13 = r0
    //     0xa40ba4: stur            w0, [x2, #0x13]
    // 0xa40ba8: ldur            x0, [fp, #-0x20]
    // 0xa40bac: LoadField: r1 = r0->field_13
    //     0xa40bac: ldur            w1, [x0, #0x13]
    // 0xa40bb0: DecompressPointer r1
    //     0xa40bb0: add             x1, x1, HEAP, lsl #32
    // 0xa40bb4: r0 = of()
    //     0xa40bb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40bb8: LoadField: r1 = r0->field_5b
    //     0xa40bb8: ldur            w1, [x0, #0x5b]
    // 0xa40bbc: DecompressPointer r1
    //     0xa40bbc: add             x1, x1, HEAP, lsl #32
    // 0xa40bc0: stur            x1, [fp, #-0x38]
    // 0xa40bc4: r0 = BorderSide()
    //     0xa40bc4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xa40bc8: mov             x1, x0
    // 0xa40bcc: ldur            x0, [fp, #-0x38]
    // 0xa40bd0: stur            x1, [fp, #-0x48]
    // 0xa40bd4: StoreField: r1->field_7 = r0
    //     0xa40bd4: stur            w0, [x1, #7]
    // 0xa40bd8: d0 = 1.000000
    //     0xa40bd8: fmov            d0, #1.00000000
    // 0xa40bdc: StoreField: r1->field_b = d0
    //     0xa40bdc: stur            d0, [x1, #0xb]
    // 0xa40be0: r0 = Instance_BorderStyle
    //     0xa40be0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xa40be4: ldr             x0, [x0, #0xf68]
    // 0xa40be8: StoreField: r1->field_13 = r0
    //     0xa40be8: stur            w0, [x1, #0x13]
    // 0xa40bec: d0 = -1.000000
    //     0xa40bec: fmov            d0, #-1.00000000
    // 0xa40bf0: ArrayStore: r1[0] = d0  ; List_8
    //     0xa40bf0: stur            d0, [x1, #0x17]
    // 0xa40bf4: r0 = RoundedRectangleBorder()
    //     0xa40bf4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa40bf8: mov             x1, x0
    // 0xa40bfc: ldur            x0, [fp, #-0x40]
    // 0xa40c00: StoreField: r1->field_b = r0
    //     0xa40c00: stur            w0, [x1, #0xb]
    // 0xa40c04: ldur            x0, [fp, #-0x48]
    // 0xa40c08: StoreField: r1->field_7 = r0
    //     0xa40c08: stur            w0, [x1, #7]
    // 0xa40c0c: r16 = <RoundedRectangleBorder>
    //     0xa40c0c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa40c10: ldr             x16, [x16, #0xf78]
    // 0xa40c14: stp             x1, x16, [SP]
    // 0xa40c18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa40c18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa40c1c: r0 = all()
    //     0xa40c1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa40c20: stur            x0, [fp, #-0x38]
    // 0xa40c24: r0 = ButtonStyle()
    //     0xa40c24: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa40c28: mov             x1, x0
    // 0xa40c2c: ldur            x0, [fp, #-0x18]
    // 0xa40c30: stur            x1, [fp, #-0x40]
    // 0xa40c34: StoreField: r1->field_b = r0
    //     0xa40c34: stur            w0, [x1, #0xb]
    // 0xa40c38: ldur            x0, [fp, #-0x10]
    // 0xa40c3c: StoreField: r1->field_23 = r0
    //     0xa40c3c: stur            w0, [x1, #0x23]
    // 0xa40c40: ldur            x0, [fp, #-0x38]
    // 0xa40c44: StoreField: r1->field_43 = r0
    //     0xa40c44: stur            w0, [x1, #0x43]
    // 0xa40c48: r0 = TextButtonThemeData()
    //     0xa40c48: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa40c4c: mov             x2, x0
    // 0xa40c50: ldur            x0, [fp, #-0x40]
    // 0xa40c54: stur            x2, [fp, #-0x10]
    // 0xa40c58: StoreField: r2->field_7 = r0
    //     0xa40c58: stur            w0, [x2, #7]
    // 0xa40c5c: r1 = "go back"
    //     0xa40c5c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a28] "go back"
    //     0xa40c60: ldr             x1, [x1, #0xa28]
    // 0xa40c64: r0 = capitalizeFirstWord()
    //     0xa40c64: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa40c68: ldur            x2, [fp, #-0x20]
    // 0xa40c6c: stur            x0, [fp, #-0x18]
    // 0xa40c70: LoadField: r1 = r2->field_13
    //     0xa40c70: ldur            w1, [x2, #0x13]
    // 0xa40c74: DecompressPointer r1
    //     0xa40c74: add             x1, x1, HEAP, lsl #32
    // 0xa40c78: r0 = of()
    //     0xa40c78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40c7c: LoadField: r1 = r0->field_87
    //     0xa40c7c: ldur            w1, [x0, #0x87]
    // 0xa40c80: DecompressPointer r1
    //     0xa40c80: add             x1, x1, HEAP, lsl #32
    // 0xa40c84: LoadField: r0 = r1->field_7
    //     0xa40c84: ldur            w0, [x1, #7]
    // 0xa40c88: DecompressPointer r0
    //     0xa40c88: add             x0, x0, HEAP, lsl #32
    // 0xa40c8c: ldur            x2, [fp, #-0x20]
    // 0xa40c90: stur            x0, [fp, #-0x38]
    // 0xa40c94: LoadField: r1 = r2->field_13
    //     0xa40c94: ldur            w1, [x2, #0x13]
    // 0xa40c98: DecompressPointer r1
    //     0xa40c98: add             x1, x1, HEAP, lsl #32
    // 0xa40c9c: r0 = of()
    //     0xa40c9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40ca0: LoadField: r1 = r0->field_5b
    //     0xa40ca0: ldur            w1, [x0, #0x5b]
    // 0xa40ca4: DecompressPointer r1
    //     0xa40ca4: add             x1, x1, HEAP, lsl #32
    // 0xa40ca8: r16 = 14.000000
    //     0xa40ca8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa40cac: ldr             x16, [x16, #0x1d8]
    // 0xa40cb0: stp             x1, x16, [SP]
    // 0xa40cb4: ldur            x1, [fp, #-0x38]
    // 0xa40cb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa40cb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa40cbc: ldr             x4, [x4, #0xaa0]
    // 0xa40cc0: r0 = copyWith()
    //     0xa40cc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa40cc4: stur            x0, [fp, #-0x38]
    // 0xa40cc8: r0 = Text()
    //     0xa40cc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa40ccc: mov             x3, x0
    // 0xa40cd0: ldur            x0, [fp, #-0x18]
    // 0xa40cd4: stur            x3, [fp, #-0x40]
    // 0xa40cd8: StoreField: r3->field_b = r0
    //     0xa40cd8: stur            w0, [x3, #0xb]
    // 0xa40cdc: ldur            x0, [fp, #-0x38]
    // 0xa40ce0: StoreField: r3->field_13 = r0
    //     0xa40ce0: stur            w0, [x3, #0x13]
    // 0xa40ce4: r1 = Function '<anonymous closure>':.
    //     0xa40ce4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a7c8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa40ce8: ldr             x1, [x1, #0x7c8]
    // 0xa40cec: r2 = Null
    //     0xa40cec: mov             x2, NULL
    // 0xa40cf0: r0 = AllocateClosure()
    //     0xa40cf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa40cf4: stur            x0, [fp, #-0x18]
    // 0xa40cf8: r0 = TextButton()
    //     0xa40cf8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa40cfc: mov             x1, x0
    // 0xa40d00: ldur            x0, [fp, #-0x18]
    // 0xa40d04: stur            x1, [fp, #-0x38]
    // 0xa40d08: StoreField: r1->field_b = r0
    //     0xa40d08: stur            w0, [x1, #0xb]
    // 0xa40d0c: r0 = false
    //     0xa40d0c: add             x0, NULL, #0x30  ; false
    // 0xa40d10: StoreField: r1->field_27 = r0
    //     0xa40d10: stur            w0, [x1, #0x27]
    // 0xa40d14: r2 = true
    //     0xa40d14: add             x2, NULL, #0x20  ; true
    // 0xa40d18: StoreField: r1->field_2f = r2
    //     0xa40d18: stur            w2, [x1, #0x2f]
    // 0xa40d1c: ldur            x3, [fp, #-0x40]
    // 0xa40d20: StoreField: r1->field_37 = r3
    //     0xa40d20: stur            w3, [x1, #0x37]
    // 0xa40d24: r0 = TextButtonTheme()
    //     0xa40d24: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa40d28: mov             x2, x0
    // 0xa40d2c: ldur            x0, [fp, #-0x10]
    // 0xa40d30: stur            x2, [fp, #-0x18]
    // 0xa40d34: StoreField: r2->field_f = r0
    //     0xa40d34: stur            w0, [x2, #0xf]
    // 0xa40d38: ldur            x0, [fp, #-0x38]
    // 0xa40d3c: StoreField: r2->field_b = r0
    //     0xa40d3c: stur            w0, [x2, #0xb]
    // 0xa40d40: r1 = <FlexParentData>
    //     0xa40d40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa40d44: ldr             x1, [x1, #0xe00]
    // 0xa40d48: r0 = Expanded()
    //     0xa40d48: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa40d4c: mov             x1, x0
    // 0xa40d50: r0 = 1
    //     0xa40d50: movz            x0, #0x1
    // 0xa40d54: stur            x1, [fp, #-0x10]
    // 0xa40d58: StoreField: r1->field_13 = r0
    //     0xa40d58: stur            x0, [x1, #0x13]
    // 0xa40d5c: r2 = Instance_FlexFit
    //     0xa40d5c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa40d60: ldr             x2, [x2, #0xe08]
    // 0xa40d64: StoreField: r1->field_1b = r2
    //     0xa40d64: stur            w2, [x1, #0x1b]
    // 0xa40d68: ldur            x3, [fp, #-0x18]
    // 0xa40d6c: StoreField: r1->field_b = r3
    //     0xa40d6c: stur            w3, [x1, #0xb]
    // 0xa40d70: r16 = <EdgeInsets>
    //     0xa40d70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa40d74: ldr             x16, [x16, #0xda0]
    // 0xa40d78: r30 = Instance_EdgeInsets
    //     0xa40d78: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa40d7c: ldr             lr, [lr, #0x1f0]
    // 0xa40d80: stp             lr, x16, [SP]
    // 0xa40d84: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa40d84: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa40d88: r0 = all()
    //     0xa40d88: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa40d8c: ldur            x2, [fp, #-0x20]
    // 0xa40d90: stur            x0, [fp, #-0x18]
    // 0xa40d94: LoadField: r1 = r2->field_13
    //     0xa40d94: ldur            w1, [x2, #0x13]
    // 0xa40d98: DecompressPointer r1
    //     0xa40d98: add             x1, x1, HEAP, lsl #32
    // 0xa40d9c: r0 = of()
    //     0xa40d9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40da0: LoadField: r1 = r0->field_5b
    //     0xa40da0: ldur            w1, [x0, #0x5b]
    // 0xa40da4: DecompressPointer r1
    //     0xa40da4: add             x1, x1, HEAP, lsl #32
    // 0xa40da8: r16 = <Color>
    //     0xa40da8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa40dac: ldr             x16, [x16, #0xf80]
    // 0xa40db0: stp             x1, x16, [SP]
    // 0xa40db4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa40db4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa40db8: r0 = all()
    //     0xa40db8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa40dbc: stur            x0, [fp, #-0x38]
    // 0xa40dc0: r0 = Radius()
    //     0xa40dc0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa40dc4: d0 = 12.000000
    //     0xa40dc4: fmov            d0, #12.00000000
    // 0xa40dc8: stur            x0, [fp, #-0x40]
    // 0xa40dcc: StoreField: r0->field_7 = d0
    //     0xa40dcc: stur            d0, [x0, #7]
    // 0xa40dd0: StoreField: r0->field_f = d0
    //     0xa40dd0: stur            d0, [x0, #0xf]
    // 0xa40dd4: r0 = BorderRadius()
    //     0xa40dd4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa40dd8: mov             x1, x0
    // 0xa40ddc: ldur            x0, [fp, #-0x40]
    // 0xa40de0: stur            x1, [fp, #-0x48]
    // 0xa40de4: StoreField: r1->field_7 = r0
    //     0xa40de4: stur            w0, [x1, #7]
    // 0xa40de8: StoreField: r1->field_b = r0
    //     0xa40de8: stur            w0, [x1, #0xb]
    // 0xa40dec: StoreField: r1->field_f = r0
    //     0xa40dec: stur            w0, [x1, #0xf]
    // 0xa40df0: StoreField: r1->field_13 = r0
    //     0xa40df0: stur            w0, [x1, #0x13]
    // 0xa40df4: r0 = RoundedRectangleBorder()
    //     0xa40df4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa40df8: mov             x1, x0
    // 0xa40dfc: ldur            x0, [fp, #-0x48]
    // 0xa40e00: StoreField: r1->field_b = r0
    //     0xa40e00: stur            w0, [x1, #0xb]
    // 0xa40e04: r0 = Instance_BorderSide
    //     0xa40e04: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa40e08: ldr             x0, [x0, #0xe20]
    // 0xa40e0c: StoreField: r1->field_7 = r0
    //     0xa40e0c: stur            w0, [x1, #7]
    // 0xa40e10: r16 = <RoundedRectangleBorder>
    //     0xa40e10: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa40e14: ldr             x16, [x16, #0xf78]
    // 0xa40e18: stp             x1, x16, [SP]
    // 0xa40e1c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa40e1c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa40e20: r0 = all()
    //     0xa40e20: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa40e24: stur            x0, [fp, #-0x40]
    // 0xa40e28: r0 = ButtonStyle()
    //     0xa40e28: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa40e2c: mov             x1, x0
    // 0xa40e30: ldur            x0, [fp, #-0x38]
    // 0xa40e34: stur            x1, [fp, #-0x48]
    // 0xa40e38: StoreField: r1->field_b = r0
    //     0xa40e38: stur            w0, [x1, #0xb]
    // 0xa40e3c: ldur            x0, [fp, #-0x18]
    // 0xa40e40: StoreField: r1->field_23 = r0
    //     0xa40e40: stur            w0, [x1, #0x23]
    // 0xa40e44: ldur            x0, [fp, #-0x40]
    // 0xa40e48: StoreField: r1->field_43 = r0
    //     0xa40e48: stur            w0, [x1, #0x43]
    // 0xa40e4c: r0 = TextButtonThemeData()
    //     0xa40e4c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa40e50: mov             x2, x0
    // 0xa40e54: ldur            x0, [fp, #-0x48]
    // 0xa40e58: stur            x2, [fp, #-0x18]
    // 0xa40e5c: StoreField: r2->field_7 = r0
    //     0xa40e5c: stur            w0, [x2, #7]
    // 0xa40e60: ldur            x0, [fp, #-8]
    // 0xa40e64: LoadField: r1 = r0->field_b
    //     0xa40e64: ldur            w1, [x0, #0xb]
    // 0xa40e68: DecompressPointer r1
    //     0xa40e68: add             x1, x1, HEAP, lsl #32
    // 0xa40e6c: cmp             w1, NULL
    // 0xa40e70: b.eq            #0xa41184
    // 0xa40e74: LoadField: r0 = r1->field_b
    //     0xa40e74: ldur            w0, [x1, #0xb]
    // 0xa40e78: DecompressPointer r0
    //     0xa40e78: add             x0, x0, HEAP, lsl #32
    // 0xa40e7c: LoadField: r1 = r0->field_b
    //     0xa40e7c: ldur            w1, [x0, #0xb]
    // 0xa40e80: DecompressPointer r1
    //     0xa40e80: add             x1, x1, HEAP, lsl #32
    // 0xa40e84: cmp             w1, NULL
    // 0xa40e88: b.ne            #0xa40e90
    // 0xa40e8c: r1 = ""
    //     0xa40e8c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa40e90: ldur            x0, [fp, #-0x20]
    // 0xa40e94: ldur            x5, [fp, #-0x28]
    // 0xa40e98: ldur            x4, [fp, #-0x30]
    // 0xa40e9c: ldur            x3, [fp, #-0x10]
    // 0xa40ea0: r0 = capitalizeFirstWord()
    //     0xa40ea0: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa40ea4: ldur            x2, [fp, #-0x20]
    // 0xa40ea8: stur            x0, [fp, #-8]
    // 0xa40eac: LoadField: r1 = r2->field_13
    //     0xa40eac: ldur            w1, [x2, #0x13]
    // 0xa40eb0: DecompressPointer r1
    //     0xa40eb0: add             x1, x1, HEAP, lsl #32
    // 0xa40eb4: r0 = of()
    //     0xa40eb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa40eb8: LoadField: r1 = r0->field_87
    //     0xa40eb8: ldur            w1, [x0, #0x87]
    // 0xa40ebc: DecompressPointer r1
    //     0xa40ebc: add             x1, x1, HEAP, lsl #32
    // 0xa40ec0: LoadField: r0 = r1->field_7
    //     0xa40ec0: ldur            w0, [x1, #7]
    // 0xa40ec4: DecompressPointer r0
    //     0xa40ec4: add             x0, x0, HEAP, lsl #32
    // 0xa40ec8: r16 = 14.000000
    //     0xa40ec8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa40ecc: ldr             x16, [x16, #0x1d8]
    // 0xa40ed0: r30 = Instance_Color
    //     0xa40ed0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa40ed4: stp             lr, x16, [SP]
    // 0xa40ed8: mov             x1, x0
    // 0xa40edc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa40edc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa40ee0: ldr             x4, [x4, #0xaa0]
    // 0xa40ee4: r0 = copyWith()
    //     0xa40ee4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa40ee8: stur            x0, [fp, #-0x38]
    // 0xa40eec: r0 = Text()
    //     0xa40eec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa40ef0: mov             x3, x0
    // 0xa40ef4: ldur            x0, [fp, #-8]
    // 0xa40ef8: stur            x3, [fp, #-0x40]
    // 0xa40efc: StoreField: r3->field_b = r0
    //     0xa40efc: stur            w0, [x3, #0xb]
    // 0xa40f00: ldur            x0, [fp, #-0x38]
    // 0xa40f04: StoreField: r3->field_13 = r0
    //     0xa40f04: stur            w0, [x3, #0x13]
    // 0xa40f08: r0 = Instance_TextAlign
    //     0xa40f08: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa40f0c: StoreField: r3->field_1b = r0
    //     0xa40f0c: stur            w0, [x3, #0x1b]
    // 0xa40f10: ldur            x2, [fp, #-0x20]
    // 0xa40f14: r1 = Function '<anonymous closure>':.
    //     0xa40f14: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a7d0] AnonymousClosure: (0xa411ac), in [package:customer_app/app/presentation/views/basic/exchange/cancel_exchange_order_bottom_sheet.dart] _CancelExchangeBottomSheetState::build (0xa40894)
    //     0xa40f18: ldr             x1, [x1, #0x7d0]
    // 0xa40f1c: r0 = AllocateClosure()
    //     0xa40f1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa40f20: stur            x0, [fp, #-8]
    // 0xa40f24: r0 = TextButton()
    //     0xa40f24: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa40f28: mov             x1, x0
    // 0xa40f2c: ldur            x0, [fp, #-8]
    // 0xa40f30: stur            x1, [fp, #-0x20]
    // 0xa40f34: StoreField: r1->field_b = r0
    //     0xa40f34: stur            w0, [x1, #0xb]
    // 0xa40f38: r0 = false
    //     0xa40f38: add             x0, NULL, #0x30  ; false
    // 0xa40f3c: StoreField: r1->field_27 = r0
    //     0xa40f3c: stur            w0, [x1, #0x27]
    // 0xa40f40: r0 = true
    //     0xa40f40: add             x0, NULL, #0x20  ; true
    // 0xa40f44: StoreField: r1->field_2f = r0
    //     0xa40f44: stur            w0, [x1, #0x2f]
    // 0xa40f48: ldur            x0, [fp, #-0x40]
    // 0xa40f4c: StoreField: r1->field_37 = r0
    //     0xa40f4c: stur            w0, [x1, #0x37]
    // 0xa40f50: r0 = TextButtonTheme()
    //     0xa40f50: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa40f54: mov             x2, x0
    // 0xa40f58: ldur            x0, [fp, #-0x18]
    // 0xa40f5c: stur            x2, [fp, #-8]
    // 0xa40f60: StoreField: r2->field_f = r0
    //     0xa40f60: stur            w0, [x2, #0xf]
    // 0xa40f64: ldur            x0, [fp, #-0x20]
    // 0xa40f68: StoreField: r2->field_b = r0
    //     0xa40f68: stur            w0, [x2, #0xb]
    // 0xa40f6c: r1 = <FlexParentData>
    //     0xa40f6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa40f70: ldr             x1, [x1, #0xe00]
    // 0xa40f74: r0 = Expanded()
    //     0xa40f74: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa40f78: mov             x3, x0
    // 0xa40f7c: r0 = 1
    //     0xa40f7c: movz            x0, #0x1
    // 0xa40f80: stur            x3, [fp, #-0x18]
    // 0xa40f84: StoreField: r3->field_13 = r0
    //     0xa40f84: stur            x0, [x3, #0x13]
    // 0xa40f88: r0 = Instance_FlexFit
    //     0xa40f88: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa40f8c: ldr             x0, [x0, #0xe08]
    // 0xa40f90: StoreField: r3->field_1b = r0
    //     0xa40f90: stur            w0, [x3, #0x1b]
    // 0xa40f94: ldur            x0, [fp, #-8]
    // 0xa40f98: StoreField: r3->field_b = r0
    //     0xa40f98: stur            w0, [x3, #0xb]
    // 0xa40f9c: r1 = Null
    //     0xa40f9c: mov             x1, NULL
    // 0xa40fa0: r2 = 6
    //     0xa40fa0: movz            x2, #0x6
    // 0xa40fa4: r0 = AllocateArray()
    //     0xa40fa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa40fa8: mov             x2, x0
    // 0xa40fac: ldur            x0, [fp, #-0x10]
    // 0xa40fb0: stur            x2, [fp, #-8]
    // 0xa40fb4: StoreField: r2->field_f = r0
    //     0xa40fb4: stur            w0, [x2, #0xf]
    // 0xa40fb8: r16 = Instance_SizedBox
    //     0xa40fb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xa40fbc: ldr             x16, [x16, #0xb20]
    // 0xa40fc0: StoreField: r2->field_13 = r16
    //     0xa40fc0: stur            w16, [x2, #0x13]
    // 0xa40fc4: ldur            x0, [fp, #-0x18]
    // 0xa40fc8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa40fc8: stur            w0, [x2, #0x17]
    // 0xa40fcc: r1 = <Widget>
    //     0xa40fcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa40fd0: r0 = AllocateGrowableArray()
    //     0xa40fd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa40fd4: mov             x1, x0
    // 0xa40fd8: ldur            x0, [fp, #-8]
    // 0xa40fdc: stur            x1, [fp, #-0x10]
    // 0xa40fe0: StoreField: r1->field_f = r0
    //     0xa40fe0: stur            w0, [x1, #0xf]
    // 0xa40fe4: r0 = 6
    //     0xa40fe4: movz            x0, #0x6
    // 0xa40fe8: StoreField: r1->field_b = r0
    //     0xa40fe8: stur            w0, [x1, #0xb]
    // 0xa40fec: r0 = Row()
    //     0xa40fec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa40ff0: mov             x1, x0
    // 0xa40ff4: r0 = Instance_Axis
    //     0xa40ff4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa40ff8: stur            x1, [fp, #-8]
    // 0xa40ffc: StoreField: r1->field_f = r0
    //     0xa40ffc: stur            w0, [x1, #0xf]
    // 0xa41000: r0 = Instance_MainAxisAlignment
    //     0xa41000: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa41004: ldr             x0, [x0, #0xa8]
    // 0xa41008: StoreField: r1->field_13 = r0
    //     0xa41008: stur            w0, [x1, #0x13]
    // 0xa4100c: r0 = Instance_MainAxisSize
    //     0xa4100c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa41010: ldr             x0, [x0, #0xa10]
    // 0xa41014: ArrayStore: r1[0] = r0  ; List_4
    //     0xa41014: stur            w0, [x1, #0x17]
    // 0xa41018: r0 = Instance_CrossAxisAlignment
    //     0xa41018: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa4101c: ldr             x0, [x0, #0x890]
    // 0xa41020: StoreField: r1->field_1b = r0
    //     0xa41020: stur            w0, [x1, #0x1b]
    // 0xa41024: r2 = Instance_VerticalDirection
    //     0xa41024: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa41028: ldr             x2, [x2, #0xa20]
    // 0xa4102c: StoreField: r1->field_23 = r2
    //     0xa4102c: stur            w2, [x1, #0x23]
    // 0xa41030: r3 = Instance_Clip
    //     0xa41030: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa41034: ldr             x3, [x3, #0x38]
    // 0xa41038: StoreField: r1->field_2b = r3
    //     0xa41038: stur            w3, [x1, #0x2b]
    // 0xa4103c: StoreField: r1->field_2f = rZR
    //     0xa4103c: stur            xzr, [x1, #0x2f]
    // 0xa41040: ldur            x4, [fp, #-0x10]
    // 0xa41044: StoreField: r1->field_b = r4
    //     0xa41044: stur            w4, [x1, #0xb]
    // 0xa41048: r0 = Padding()
    //     0xa41048: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa4104c: mov             x3, x0
    // 0xa41050: r0 = Instance_EdgeInsets
    //     0xa41050: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xa41054: ldr             x0, [x0, #0x858]
    // 0xa41058: stur            x3, [fp, #-0x10]
    // 0xa4105c: StoreField: r3->field_f = r0
    //     0xa4105c: stur            w0, [x3, #0xf]
    // 0xa41060: ldur            x0, [fp, #-8]
    // 0xa41064: StoreField: r3->field_b = r0
    //     0xa41064: stur            w0, [x3, #0xb]
    // 0xa41068: r1 = Null
    //     0xa41068: mov             x1, NULL
    // 0xa4106c: r2 = 8
    //     0xa4106c: movz            x2, #0x8
    // 0xa41070: r0 = AllocateArray()
    //     0xa41070: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa41074: mov             x2, x0
    // 0xa41078: ldur            x0, [fp, #-0x28]
    // 0xa4107c: stur            x2, [fp, #-8]
    // 0xa41080: StoreField: r2->field_f = r0
    //     0xa41080: stur            w0, [x2, #0xf]
    // 0xa41084: r16 = Instance_SizedBox
    //     0xa41084: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xa41088: ldr             x16, [x16, #0x578]
    // 0xa4108c: StoreField: r2->field_13 = r16
    //     0xa4108c: stur            w16, [x2, #0x13]
    // 0xa41090: ldur            x0, [fp, #-0x30]
    // 0xa41094: ArrayStore: r2[0] = r0  ; List_4
    //     0xa41094: stur            w0, [x2, #0x17]
    // 0xa41098: ldur            x0, [fp, #-0x10]
    // 0xa4109c: StoreField: r2->field_1b = r0
    //     0xa4109c: stur            w0, [x2, #0x1b]
    // 0xa410a0: r1 = <Widget>
    //     0xa410a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa410a4: r0 = AllocateGrowableArray()
    //     0xa410a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa410a8: mov             x1, x0
    // 0xa410ac: ldur            x0, [fp, #-8]
    // 0xa410b0: stur            x1, [fp, #-0x10]
    // 0xa410b4: StoreField: r1->field_f = r0
    //     0xa410b4: stur            w0, [x1, #0xf]
    // 0xa410b8: r0 = 8
    //     0xa410b8: movz            x0, #0x8
    // 0xa410bc: StoreField: r1->field_b = r0
    //     0xa410bc: stur            w0, [x1, #0xb]
    // 0xa410c0: r0 = Column()
    //     0xa410c0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa410c4: mov             x1, x0
    // 0xa410c8: r0 = Instance_Axis
    //     0xa410c8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa410cc: stur            x1, [fp, #-8]
    // 0xa410d0: StoreField: r1->field_f = r0
    //     0xa410d0: stur            w0, [x1, #0xf]
    // 0xa410d4: r0 = Instance_MainAxisAlignment
    //     0xa410d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa410d8: ldr             x0, [x0, #0xa08]
    // 0xa410dc: StoreField: r1->field_13 = r0
    //     0xa410dc: stur            w0, [x1, #0x13]
    // 0xa410e0: r0 = Instance_MainAxisSize
    //     0xa410e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xa410e4: ldr             x0, [x0, #0xdd0]
    // 0xa410e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa410e8: stur            w0, [x1, #0x17]
    // 0xa410ec: r0 = Instance_CrossAxisAlignment
    //     0xa410ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa410f0: ldr             x0, [x0, #0x890]
    // 0xa410f4: StoreField: r1->field_1b = r0
    //     0xa410f4: stur            w0, [x1, #0x1b]
    // 0xa410f8: r0 = Instance_VerticalDirection
    //     0xa410f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa410fc: ldr             x0, [x0, #0xa20]
    // 0xa41100: StoreField: r1->field_23 = r0
    //     0xa41100: stur            w0, [x1, #0x23]
    // 0xa41104: r0 = Instance_Clip
    //     0xa41104: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa41108: ldr             x0, [x0, #0x38]
    // 0xa4110c: StoreField: r1->field_2b = r0
    //     0xa4110c: stur            w0, [x1, #0x2b]
    // 0xa41110: StoreField: r1->field_2f = rZR
    //     0xa41110: stur            xzr, [x1, #0x2f]
    // 0xa41114: ldur            x0, [fp, #-0x10]
    // 0xa41118: StoreField: r1->field_b = r0
    //     0xa41118: stur            w0, [x1, #0xb]
    // 0xa4111c: r0 = Padding()
    //     0xa4111c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa41120: mov             x1, x0
    // 0xa41124: r0 = Instance_EdgeInsets
    //     0xa41124: add             x0, PP, #0x55, lsl #12  ; [pp+0x55a40] Obj!EdgeInsets@d58311
    //     0xa41128: ldr             x0, [x0, #0xa40]
    // 0xa4112c: stur            x1, [fp, #-0x10]
    // 0xa41130: StoreField: r1->field_f = r0
    //     0xa41130: stur            w0, [x1, #0xf]
    // 0xa41134: ldur            x0, [fp, #-8]
    // 0xa41138: StoreField: r1->field_b = r0
    //     0xa41138: stur            w0, [x1, #0xb]
    // 0xa4113c: r0 = Container()
    //     0xa4113c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa41140: stur            x0, [fp, #-8]
    // 0xa41144: r16 = Instance_BoxDecoration
    //     0xa41144: add             x16, PP, #0x55, lsl #12  ; [pp+0x55fe0] Obj!BoxDecoration@d64981
    //     0xa41148: ldr             x16, [x16, #0xfe0]
    // 0xa4114c: ldur            lr, [fp, #-0x10]
    // 0xa41150: stp             lr, x16, [SP]
    // 0xa41154: mov             x1, x0
    // 0xa41158: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa41158: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa4115c: ldr             x4, [x4, #0x88]
    // 0xa41160: r0 = Container()
    //     0xa41160: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa41164: ldur            x0, [fp, #-8]
    // 0xa41168: LeaveFrame
    //     0xa41168: mov             SP, fp
    //     0xa4116c: ldp             fp, lr, [SP], #0x10
    // 0xa41170: ret
    //     0xa41170: ret             
    // 0xa41174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa41174: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa41178: b               #0xa408bc
    // 0xa4117c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4117c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa41180: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa41180: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa41184: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa41184: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa411ac, size: 0xac
    // 0xa411ac: EnterFrame
    //     0xa411ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa411b0: mov             fp, SP
    // 0xa411b4: AllocStack(0x10)
    //     0xa411b4: sub             SP, SP, #0x10
    // 0xa411b8: SetupParameters()
    //     0xa411b8: ldr             x0, [fp, #0x10]
    //     0xa411bc: ldur            w1, [x0, #0x17]
    //     0xa411c0: add             x1, x1, HEAP, lsl #32
    // 0xa411c4: CheckStackOverflow
    //     0xa411c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa411c8: cmp             SP, x16
    //     0xa411cc: b.ls            #0xa4124c
    // 0xa411d0: LoadField: r0 = r1->field_f
    //     0xa411d0: ldur            w0, [x1, #0xf]
    // 0xa411d4: DecompressPointer r0
    //     0xa411d4: add             x0, x0, HEAP, lsl #32
    // 0xa411d8: LoadField: r1 = r0->field_b
    //     0xa411d8: ldur            w1, [x0, #0xb]
    // 0xa411dc: DecompressPointer r1
    //     0xa411dc: add             x1, x1, HEAP, lsl #32
    // 0xa411e0: cmp             w1, NULL
    // 0xa411e4: b.eq            #0xa41254
    // 0xa411e8: LoadField: r0 = r1->field_13
    //     0xa411e8: ldur            w0, [x1, #0x13]
    // 0xa411ec: DecompressPointer r0
    //     0xa411ec: add             x0, x0, HEAP, lsl #32
    // 0xa411f0: LoadField: r2 = r1->field_f
    //     0xa411f0: ldur            w2, [x1, #0xf]
    // 0xa411f4: DecompressPointer r2
    //     0xa411f4: add             x2, x2, HEAP, lsl #32
    // 0xa411f8: stp             x0, x2, [SP]
    // 0xa411fc: r4 = 0
    //     0xa411fc: movz            x4, #0
    // 0xa41200: ldr             x0, [SP, #8]
    // 0xa41204: r16 = UnlinkedCall_0x613b5c
    //     0xa41204: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a7d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa41208: add             x16, x16, #0x7d8
    // 0xa4120c: ldp             x5, lr, [x16]
    // 0xa41210: blr             lr
    // 0xa41214: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa41214: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa41218: ldr             x0, [x0, #0x1c80]
    //     0xa4121c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa41220: cmp             w0, w16
    //     0xa41224: b.ne            #0xa41230
    //     0xa41228: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4122c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa41230: str             NULL, [SP]
    // 0xa41234: r4 = const [0x1, 0, 0, 0, null]
    //     0xa41234: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa41238: r0 = GetNavigation.back()
    //     0xa41238: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa4123c: r0 = Null
    //     0xa4123c: mov             x0, NULL
    // 0xa41240: LeaveFrame
    //     0xa41240: mov             SP, fp
    //     0xa41244: ldp             fp, lr, [SP], #0x10
    // 0xa41248: ret
    //     0xa41248: ret             
    // 0xa4124c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4124c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa41250: b               #0xa411d0
    // 0xa41254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa41254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4251, size: 0x18, field offset: 0xc
//   const constructor, 
class CancelExchangeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7b9d4, size: 0x24
    // 0xc7b9d4: EnterFrame
    //     0xc7b9d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7b9d8: mov             fp, SP
    // 0xc7b9dc: mov             x0, x1
    // 0xc7b9e0: r1 = <CancelExchangeBottomSheet>
    //     0xc7b9e0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49070] TypeArguments: <CancelExchangeBottomSheet>
    //     0xc7b9e4: ldr             x1, [x1, #0x70]
    // 0xc7b9e8: r0 = _CancelExchangeBottomSheetState()
    //     0xc7b9e8: bl              #0xc7b9f8  ; Allocate_CancelExchangeBottomSheetStateStub -> _CancelExchangeBottomSheetState (size=0x14)
    // 0xc7b9ec: LeaveFrame
    //     0xc7b9ec: mov             SP, fp
    //     0xc7b9f0: ldp             fp, lr, [SP], #0x10
    // 0xc7b9f4: ret
    //     0xc7b9f4: ret             
  }
}
