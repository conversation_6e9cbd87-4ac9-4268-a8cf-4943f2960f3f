// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart

// class id: 1049295, size: 0x8
class :: {
}

// class id: 4595, size: 0x14, field offset: 0x14
//   const constructor, 
class RatingReviewOrderPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1351a68, size: 0x64
    // 0x1351a68: EnterFrame
    //     0x1351a68: stp             fp, lr, [SP, #-0x10]!
    //     0x1351a6c: mov             fp, SP
    // 0x1351a70: AllocStack(0x18)
    //     0x1351a70: sub             SP, SP, #0x18
    // 0x1351a74: SetupParameters(RatingReviewOrderPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1351a74: stur            x1, [fp, #-8]
    //     0x1351a78: stur            x2, [fp, #-0x10]
    // 0x1351a7c: r1 = 2
    //     0x1351a7c: movz            x1, #0x2
    // 0x1351a80: r0 = AllocateContext()
    //     0x1351a80: bl              #0x16f6108  ; AllocateContextStub
    // 0x1351a84: mov             x1, x0
    // 0x1351a88: ldur            x0, [fp, #-8]
    // 0x1351a8c: stur            x1, [fp, #-0x18]
    // 0x1351a90: StoreField: r1->field_f = r0
    //     0x1351a90: stur            w0, [x1, #0xf]
    // 0x1351a94: ldur            x0, [fp, #-0x10]
    // 0x1351a98: StoreField: r1->field_13 = r0
    //     0x1351a98: stur            w0, [x1, #0x13]
    // 0x1351a9c: r0 = Obx()
    //     0x1351a9c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1351aa0: ldur            x2, [fp, #-0x18]
    // 0x1351aa4: r1 = Function '<anonymous closure>':.
    //     0x1351aa4: add             x1, PP, #0x42, lsl #12  ; [pp+0x42fe8] AnonymousClosure: (0x1351acc), in [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::bottomNavigationBar (0x1351a68)
    //     0x1351aa8: ldr             x1, [x1, #0xfe8]
    // 0x1351aac: stur            x0, [fp, #-8]
    // 0x1351ab0: r0 = AllocateClosure()
    //     0x1351ab0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1351ab4: mov             x1, x0
    // 0x1351ab8: ldur            x0, [fp, #-8]
    // 0x1351abc: StoreField: r0->field_b = r1
    //     0x1351abc: stur            w1, [x0, #0xb]
    // 0x1351ac0: LeaveFrame
    //     0x1351ac0: mov             SP, fp
    //     0x1351ac4: ldp             fp, lr, [SP], #0x10
    // 0x1351ac8: ret
    //     0x1351ac8: ret             
  }
  [closure] Wrap <anonymous closure>(dynamic) {
    // ** addr: 0x1351acc, size: 0x6c0
    // 0x1351acc: EnterFrame
    //     0x1351acc: stp             fp, lr, [SP, #-0x10]!
    //     0x1351ad0: mov             fp, SP
    // 0x1351ad4: AllocStack(0x50)
    //     0x1351ad4: sub             SP, SP, #0x50
    // 0x1351ad8: SetupParameters()
    //     0x1351ad8: ldr             x0, [fp, #0x10]
    //     0x1351adc: ldur            w2, [x0, #0x17]
    //     0x1351ae0: add             x2, x2, HEAP, lsl #32
    //     0x1351ae4: stur            x2, [fp, #-8]
    // 0x1351ae8: CheckStackOverflow
    //     0x1351ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1351aec: cmp             SP, x16
    //     0x1351af0: b.ls            #0x1352184
    // 0x1351af4: r16 = <EdgeInsets>
    //     0x1351af4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1351af8: ldr             x16, [x16, #0xda0]
    // 0x1351afc: r30 = Instance_EdgeInsets
    //     0x1351afc: add             lr, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0x1351b00: ldr             lr, [lr, #0x670]
    // 0x1351b04: stp             lr, x16, [SP]
    // 0x1351b08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1351b08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1351b0c: r0 = all()
    //     0x1351b0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1351b10: ldur            x2, [fp, #-8]
    // 0x1351b14: stur            x0, [fp, #-0x10]
    // 0x1351b18: LoadField: r1 = r2->field_f
    //     0x1351b18: ldur            w1, [x2, #0xf]
    // 0x1351b1c: DecompressPointer r1
    //     0x1351b1c: add             x1, x1, HEAP, lsl #32
    // 0x1351b20: r0 = controller()
    //     0x1351b20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351b24: LoadField: r1 = r0->field_c3
    //     0x1351b24: ldur            w1, [x0, #0xc3]
    // 0x1351b28: DecompressPointer r1
    //     0x1351b28: add             x1, x1, HEAP, lsl #32
    // 0x1351b2c: r0 = value()
    //     0x1351b2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351b30: LoadField: d0 = r0->field_7
    //     0x1351b30: ldur            d0, [x0, #7]
    // 0x1351b34: d1 = 0.000000
    //     0x1351b34: eor             v1.16b, v1.16b, v1.16b
    // 0x1351b38: fcmp            d0, d1
    // 0x1351b3c: b.eq            #0x1351d28
    // 0x1351b40: ldur            x2, [fp, #-8]
    // 0x1351b44: LoadField: r1 = r2->field_f
    //     0x1351b44: ldur            w1, [x2, #0xf]
    // 0x1351b48: DecompressPointer r1
    //     0x1351b48: add             x1, x1, HEAP, lsl #32
    // 0x1351b4c: r0 = controller()
    //     0x1351b4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351b50: LoadField: r1 = r0->field_53
    //     0x1351b50: ldur            w1, [x0, #0x53]
    // 0x1351b54: DecompressPointer r1
    //     0x1351b54: add             x1, x1, HEAP, lsl #32
    // 0x1351b58: r0 = value()
    //     0x1351b58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351b5c: LoadField: r1 = r0->field_3f
    //     0x1351b5c: ldur            w1, [x0, #0x3f]
    // 0x1351b60: DecompressPointer r1
    //     0x1351b60: add             x1, x1, HEAP, lsl #32
    // 0x1351b64: cmp             w1, NULL
    // 0x1351b68: b.ne            #0x1351b74
    // 0x1351b6c: r0 = Null
    //     0x1351b6c: mov             x0, NULL
    // 0x1351b70: b               #0x1351b98
    // 0x1351b74: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1351b74: ldur            w0, [x1, #0x17]
    // 0x1351b78: DecompressPointer r0
    //     0x1351b78: add             x0, x0, HEAP, lsl #32
    // 0x1351b7c: cmp             w0, NULL
    // 0x1351b80: b.ne            #0x1351b8c
    // 0x1351b84: r0 = Null
    //     0x1351b84: mov             x0, NULL
    // 0x1351b88: b               #0x1351b98
    // 0x1351b8c: LoadField: r1 = r0->field_7
    //     0x1351b8c: ldur            w1, [x0, #7]
    // 0x1351b90: DecompressPointer r1
    //     0x1351b90: add             x1, x1, HEAP, lsl #32
    // 0x1351b94: mov             x0, x1
    // 0x1351b98: cmp             w0, NULL
    // 0x1351b9c: b.ne            #0x1351ba8
    // 0x1351ba0: r0 = 0
    //     0x1351ba0: movz            x0, #0
    // 0x1351ba4: b               #0x1351bb8
    // 0x1351ba8: r1 = LoadInt32Instr(r0)
    //     0x1351ba8: sbfx            x1, x0, #1, #0x1f
    //     0x1351bac: tbz             w0, #0, #0x1351bb4
    //     0x1351bb0: ldur            x1, [x0, #7]
    // 0x1351bb4: mov             x0, x1
    // 0x1351bb8: ldur            x2, [fp, #-8]
    // 0x1351bbc: stur            x0, [fp, #-0x18]
    // 0x1351bc0: LoadField: r1 = r2->field_f
    //     0x1351bc0: ldur            w1, [x2, #0xf]
    // 0x1351bc4: DecompressPointer r1
    //     0x1351bc4: add             x1, x1, HEAP, lsl #32
    // 0x1351bc8: r0 = controller()
    //     0x1351bc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351bcc: LoadField: r1 = r0->field_53
    //     0x1351bcc: ldur            w1, [x0, #0x53]
    // 0x1351bd0: DecompressPointer r1
    //     0x1351bd0: add             x1, x1, HEAP, lsl #32
    // 0x1351bd4: r0 = value()
    //     0x1351bd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351bd8: LoadField: r1 = r0->field_3f
    //     0x1351bd8: ldur            w1, [x0, #0x3f]
    // 0x1351bdc: DecompressPointer r1
    //     0x1351bdc: add             x1, x1, HEAP, lsl #32
    // 0x1351be0: cmp             w1, NULL
    // 0x1351be4: b.ne            #0x1351bf0
    // 0x1351be8: r0 = Null
    //     0x1351be8: mov             x0, NULL
    // 0x1351bec: b               #0x1351c14
    // 0x1351bf0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1351bf0: ldur            w0, [x1, #0x17]
    // 0x1351bf4: DecompressPointer r0
    //     0x1351bf4: add             x0, x0, HEAP, lsl #32
    // 0x1351bf8: cmp             w0, NULL
    // 0x1351bfc: b.ne            #0x1351c08
    // 0x1351c00: r0 = Null
    //     0x1351c00: mov             x0, NULL
    // 0x1351c04: b               #0x1351c14
    // 0x1351c08: LoadField: r1 = r0->field_b
    //     0x1351c08: ldur            w1, [x0, #0xb]
    // 0x1351c0c: DecompressPointer r1
    //     0x1351c0c: add             x1, x1, HEAP, lsl #32
    // 0x1351c10: mov             x0, x1
    // 0x1351c14: cmp             w0, NULL
    // 0x1351c18: b.ne            #0x1351c24
    // 0x1351c1c: r0 = 0
    //     0x1351c1c: movz            x0, #0
    // 0x1351c20: b               #0x1351c34
    // 0x1351c24: r1 = LoadInt32Instr(r0)
    //     0x1351c24: sbfx            x1, x0, #1, #0x1f
    //     0x1351c28: tbz             w0, #0, #0x1351c30
    //     0x1351c2c: ldur            x1, [x0, #7]
    // 0x1351c30: mov             x0, x1
    // 0x1351c34: ldur            x2, [fp, #-8]
    // 0x1351c38: stur            x0, [fp, #-0x20]
    // 0x1351c3c: LoadField: r1 = r2->field_f
    //     0x1351c3c: ldur            w1, [x2, #0xf]
    // 0x1351c40: DecompressPointer r1
    //     0x1351c40: add             x1, x1, HEAP, lsl #32
    // 0x1351c44: r0 = controller()
    //     0x1351c44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351c48: LoadField: r1 = r0->field_53
    //     0x1351c48: ldur            w1, [x0, #0x53]
    // 0x1351c4c: DecompressPointer r1
    //     0x1351c4c: add             x1, x1, HEAP, lsl #32
    // 0x1351c50: r0 = value()
    //     0x1351c50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351c54: LoadField: r1 = r0->field_3f
    //     0x1351c54: ldur            w1, [x0, #0x3f]
    // 0x1351c58: DecompressPointer r1
    //     0x1351c58: add             x1, x1, HEAP, lsl #32
    // 0x1351c5c: cmp             w1, NULL
    // 0x1351c60: b.ne            #0x1351c6c
    // 0x1351c64: r0 = Null
    //     0x1351c64: mov             x0, NULL
    // 0x1351c68: b               #0x1351c90
    // 0x1351c6c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1351c6c: ldur            w0, [x1, #0x17]
    // 0x1351c70: DecompressPointer r0
    //     0x1351c70: add             x0, x0, HEAP, lsl #32
    // 0x1351c74: cmp             w0, NULL
    // 0x1351c78: b.ne            #0x1351c84
    // 0x1351c7c: r0 = Null
    //     0x1351c7c: mov             x0, NULL
    // 0x1351c80: b               #0x1351c90
    // 0x1351c84: LoadField: r1 = r0->field_f
    //     0x1351c84: ldur            w1, [x0, #0xf]
    // 0x1351c88: DecompressPointer r1
    //     0x1351c88: add             x1, x1, HEAP, lsl #32
    // 0x1351c8c: mov             x0, x1
    // 0x1351c90: cmp             w0, NULL
    // 0x1351c94: b.ne            #0x1351ca0
    // 0x1351c98: r0 = 0
    //     0x1351c98: movz            x0, #0
    // 0x1351c9c: b               #0x1351cb0
    // 0x1351ca0: r1 = LoadInt32Instr(r0)
    //     0x1351ca0: sbfx            x1, x0, #1, #0x1f
    //     0x1351ca4: tbz             w0, #0, #0x1351cac
    //     0x1351ca8: ldur            x1, [x0, #7]
    // 0x1351cac: mov             x0, x1
    // 0x1351cb0: stur            x0, [fp, #-0x28]
    // 0x1351cb4: r0 = Color()
    //     0x1351cb4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1351cb8: mov             x1, x0
    // 0x1351cbc: r0 = Instance_ColorSpace
    //     0x1351cbc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1351cc0: StoreField: r1->field_27 = r0
    //     0x1351cc0: stur            w0, [x1, #0x27]
    // 0x1351cc4: d0 = 1.000000
    //     0x1351cc4: fmov            d0, #1.00000000
    // 0x1351cc8: StoreField: r1->field_7 = d0
    //     0x1351cc8: stur            d0, [x1, #7]
    // 0x1351ccc: ldur            x0, [fp, #-0x18]
    // 0x1351cd0: ubfx            x0, x0, #0, #0x20
    // 0x1351cd4: and             w2, w0, #0xff
    // 0x1351cd8: ubfx            x2, x2, #0, #0x20
    // 0x1351cdc: scvtf           d0, x2
    // 0x1351ce0: d1 = 255.000000
    //     0x1351ce0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1351ce4: fdiv            d2, d0, d1
    // 0x1351ce8: StoreField: r1->field_f = d2
    //     0x1351ce8: stur            d2, [x1, #0xf]
    // 0x1351cec: ldur            x0, [fp, #-0x20]
    // 0x1351cf0: ubfx            x0, x0, #0, #0x20
    // 0x1351cf4: and             w2, w0, #0xff
    // 0x1351cf8: ubfx            x2, x2, #0, #0x20
    // 0x1351cfc: scvtf           d0, x2
    // 0x1351d00: fdiv            d2, d0, d1
    // 0x1351d04: ArrayStore: r1[0] = d2  ; List_8
    //     0x1351d04: stur            d2, [x1, #0x17]
    // 0x1351d08: ldur            x0, [fp, #-0x28]
    // 0x1351d0c: ubfx            x0, x0, #0, #0x20
    // 0x1351d10: and             w2, w0, #0xff
    // 0x1351d14: ubfx            x2, x2, #0, #0x20
    // 0x1351d18: scvtf           d0, x2
    // 0x1351d1c: fdiv            d2, d0, d1
    // 0x1351d20: StoreField: r1->field_1f = d2
    //     0x1351d20: stur            d2, [x1, #0x1f]
    // 0x1351d24: b               #0x1351f18
    // 0x1351d28: ldur            x2, [fp, #-8]
    // 0x1351d2c: r0 = Instance_ColorSpace
    //     0x1351d2c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1351d30: d1 = 255.000000
    //     0x1351d30: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1351d34: LoadField: r1 = r2->field_f
    //     0x1351d34: ldur            w1, [x2, #0xf]
    // 0x1351d38: DecompressPointer r1
    //     0x1351d38: add             x1, x1, HEAP, lsl #32
    // 0x1351d3c: r0 = controller()
    //     0x1351d3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351d40: LoadField: r1 = r0->field_53
    //     0x1351d40: ldur            w1, [x0, #0x53]
    // 0x1351d44: DecompressPointer r1
    //     0x1351d44: add             x1, x1, HEAP, lsl #32
    // 0x1351d48: r0 = value()
    //     0x1351d48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351d4c: LoadField: r1 = r0->field_3f
    //     0x1351d4c: ldur            w1, [x0, #0x3f]
    // 0x1351d50: DecompressPointer r1
    //     0x1351d50: add             x1, x1, HEAP, lsl #32
    // 0x1351d54: cmp             w1, NULL
    // 0x1351d58: b.ne            #0x1351d64
    // 0x1351d5c: r0 = Null
    //     0x1351d5c: mov             x0, NULL
    // 0x1351d60: b               #0x1351d88
    // 0x1351d64: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1351d64: ldur            w0, [x1, #0x17]
    // 0x1351d68: DecompressPointer r0
    //     0x1351d68: add             x0, x0, HEAP, lsl #32
    // 0x1351d6c: cmp             w0, NULL
    // 0x1351d70: b.ne            #0x1351d7c
    // 0x1351d74: r0 = Null
    //     0x1351d74: mov             x0, NULL
    // 0x1351d78: b               #0x1351d88
    // 0x1351d7c: LoadField: r1 = r0->field_7
    //     0x1351d7c: ldur            w1, [x0, #7]
    // 0x1351d80: DecompressPointer r1
    //     0x1351d80: add             x1, x1, HEAP, lsl #32
    // 0x1351d84: mov             x0, x1
    // 0x1351d88: cmp             w0, NULL
    // 0x1351d8c: b.ne            #0x1351d98
    // 0x1351d90: r0 = 0
    //     0x1351d90: movz            x0, #0
    // 0x1351d94: b               #0x1351da8
    // 0x1351d98: r1 = LoadInt32Instr(r0)
    //     0x1351d98: sbfx            x1, x0, #1, #0x1f
    //     0x1351d9c: tbz             w0, #0, #0x1351da4
    //     0x1351da0: ldur            x1, [x0, #7]
    // 0x1351da4: mov             x0, x1
    // 0x1351da8: ldur            x2, [fp, #-8]
    // 0x1351dac: stur            x0, [fp, #-0x18]
    // 0x1351db0: LoadField: r1 = r2->field_f
    //     0x1351db0: ldur            w1, [x2, #0xf]
    // 0x1351db4: DecompressPointer r1
    //     0x1351db4: add             x1, x1, HEAP, lsl #32
    // 0x1351db8: r0 = controller()
    //     0x1351db8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351dbc: LoadField: r1 = r0->field_53
    //     0x1351dbc: ldur            w1, [x0, #0x53]
    // 0x1351dc0: DecompressPointer r1
    //     0x1351dc0: add             x1, x1, HEAP, lsl #32
    // 0x1351dc4: r0 = value()
    //     0x1351dc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351dc8: LoadField: r1 = r0->field_3f
    //     0x1351dc8: ldur            w1, [x0, #0x3f]
    // 0x1351dcc: DecompressPointer r1
    //     0x1351dcc: add             x1, x1, HEAP, lsl #32
    // 0x1351dd0: cmp             w1, NULL
    // 0x1351dd4: b.ne            #0x1351de0
    // 0x1351dd8: r0 = Null
    //     0x1351dd8: mov             x0, NULL
    // 0x1351ddc: b               #0x1351e04
    // 0x1351de0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1351de0: ldur            w0, [x1, #0x17]
    // 0x1351de4: DecompressPointer r0
    //     0x1351de4: add             x0, x0, HEAP, lsl #32
    // 0x1351de8: cmp             w0, NULL
    // 0x1351dec: b.ne            #0x1351df8
    // 0x1351df0: r0 = Null
    //     0x1351df0: mov             x0, NULL
    // 0x1351df4: b               #0x1351e04
    // 0x1351df8: LoadField: r1 = r0->field_b
    //     0x1351df8: ldur            w1, [x0, #0xb]
    // 0x1351dfc: DecompressPointer r1
    //     0x1351dfc: add             x1, x1, HEAP, lsl #32
    // 0x1351e00: mov             x0, x1
    // 0x1351e04: cmp             w0, NULL
    // 0x1351e08: b.ne            #0x1351e14
    // 0x1351e0c: r0 = 0
    //     0x1351e0c: movz            x0, #0
    // 0x1351e10: b               #0x1351e24
    // 0x1351e14: r1 = LoadInt32Instr(r0)
    //     0x1351e14: sbfx            x1, x0, #1, #0x1f
    //     0x1351e18: tbz             w0, #0, #0x1351e20
    //     0x1351e1c: ldur            x1, [x0, #7]
    // 0x1351e20: mov             x0, x1
    // 0x1351e24: ldur            x2, [fp, #-8]
    // 0x1351e28: stur            x0, [fp, #-0x20]
    // 0x1351e2c: LoadField: r1 = r2->field_f
    //     0x1351e2c: ldur            w1, [x2, #0xf]
    // 0x1351e30: DecompressPointer r1
    //     0x1351e30: add             x1, x1, HEAP, lsl #32
    // 0x1351e34: r0 = controller()
    //     0x1351e34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1351e38: LoadField: r1 = r0->field_53
    //     0x1351e38: ldur            w1, [x0, #0x53]
    // 0x1351e3c: DecompressPointer r1
    //     0x1351e3c: add             x1, x1, HEAP, lsl #32
    // 0x1351e40: r0 = value()
    //     0x1351e40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1351e44: LoadField: r1 = r0->field_3f
    //     0x1351e44: ldur            w1, [x0, #0x3f]
    // 0x1351e48: DecompressPointer r1
    //     0x1351e48: add             x1, x1, HEAP, lsl #32
    // 0x1351e4c: cmp             w1, NULL
    // 0x1351e50: b.ne            #0x1351e5c
    // 0x1351e54: r0 = Null
    //     0x1351e54: mov             x0, NULL
    // 0x1351e58: b               #0x1351e80
    // 0x1351e5c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1351e5c: ldur            w0, [x1, #0x17]
    // 0x1351e60: DecompressPointer r0
    //     0x1351e60: add             x0, x0, HEAP, lsl #32
    // 0x1351e64: cmp             w0, NULL
    // 0x1351e68: b.ne            #0x1351e74
    // 0x1351e6c: r0 = Null
    //     0x1351e6c: mov             x0, NULL
    // 0x1351e70: b               #0x1351e80
    // 0x1351e74: LoadField: r1 = r0->field_f
    //     0x1351e74: ldur            w1, [x0, #0xf]
    // 0x1351e78: DecompressPointer r1
    //     0x1351e78: add             x1, x1, HEAP, lsl #32
    // 0x1351e7c: mov             x0, x1
    // 0x1351e80: cmp             w0, NULL
    // 0x1351e84: b.ne            #0x1351e90
    // 0x1351e88: r0 = 0
    //     0x1351e88: movz            x0, #0
    // 0x1351e8c: b               #0x1351ea0
    // 0x1351e90: r1 = LoadInt32Instr(r0)
    //     0x1351e90: sbfx            x1, x0, #1, #0x1f
    //     0x1351e94: tbz             w0, #0, #0x1351e9c
    //     0x1351e98: ldur            x1, [x0, #7]
    // 0x1351e9c: mov             x0, x1
    // 0x1351ea0: stur            x0, [fp, #-0x28]
    // 0x1351ea4: r0 = Color()
    //     0x1351ea4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1351ea8: mov             x1, x0
    // 0x1351eac: r0 = Instance_ColorSpace
    //     0x1351eac: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1351eb0: StoreField: r1->field_27 = r0
    //     0x1351eb0: stur            w0, [x1, #0x27]
    // 0x1351eb4: d0 = 0.300000
    //     0x1351eb4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x1351eb8: ldr             d0, [x17, #0x658]
    // 0x1351ebc: StoreField: r1->field_7 = d0
    //     0x1351ebc: stur            d0, [x1, #7]
    // 0x1351ec0: ldur            x0, [fp, #-0x18]
    // 0x1351ec4: ubfx            x0, x0, #0, #0x20
    // 0x1351ec8: and             w2, w0, #0xff
    // 0x1351ecc: ubfx            x2, x2, #0, #0x20
    // 0x1351ed0: scvtf           d0, x2
    // 0x1351ed4: d1 = 255.000000
    //     0x1351ed4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1351ed8: fdiv            d2, d0, d1
    // 0x1351edc: StoreField: r1->field_f = d2
    //     0x1351edc: stur            d2, [x1, #0xf]
    // 0x1351ee0: ldur            x0, [fp, #-0x20]
    // 0x1351ee4: ubfx            x0, x0, #0, #0x20
    // 0x1351ee8: and             w2, w0, #0xff
    // 0x1351eec: ubfx            x2, x2, #0, #0x20
    // 0x1351ef0: scvtf           d0, x2
    // 0x1351ef4: fdiv            d2, d0, d1
    // 0x1351ef8: ArrayStore: r1[0] = d2  ; List_8
    //     0x1351ef8: stur            d2, [x1, #0x17]
    // 0x1351efc: ldur            x0, [fp, #-0x28]
    // 0x1351f00: ubfx            x0, x0, #0, #0x20
    // 0x1351f04: and             w2, w0, #0xff
    // 0x1351f08: ubfx            x2, x2, #0, #0x20
    // 0x1351f0c: scvtf           d0, x2
    // 0x1351f10: fdiv            d2, d0, d1
    // 0x1351f14: StoreField: r1->field_1f = d2
    //     0x1351f14: stur            d2, [x1, #0x1f]
    // 0x1351f18: ldur            x2, [fp, #-8]
    // 0x1351f1c: ldur            x0, [fp, #-0x10]
    // 0x1351f20: r16 = <Color>
    //     0x1351f20: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1351f24: ldr             x16, [x16, #0xf80]
    // 0x1351f28: stp             x1, x16, [SP]
    // 0x1351f2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1351f2c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1351f30: r0 = all()
    //     0x1351f30: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1351f34: stur            x0, [fp, #-0x30]
    // 0x1351f38: r0 = Radius()
    //     0x1351f38: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1351f3c: d0 = 30.000000
    //     0x1351f3c: fmov            d0, #30.00000000
    // 0x1351f40: stur            x0, [fp, #-0x38]
    // 0x1351f44: StoreField: r0->field_7 = d0
    //     0x1351f44: stur            d0, [x0, #7]
    // 0x1351f48: StoreField: r0->field_f = d0
    //     0x1351f48: stur            d0, [x0, #0xf]
    // 0x1351f4c: r0 = BorderRadius()
    //     0x1351f4c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1351f50: mov             x1, x0
    // 0x1351f54: ldur            x0, [fp, #-0x38]
    // 0x1351f58: stur            x1, [fp, #-0x40]
    // 0x1351f5c: StoreField: r1->field_7 = r0
    //     0x1351f5c: stur            w0, [x1, #7]
    // 0x1351f60: StoreField: r1->field_b = r0
    //     0x1351f60: stur            w0, [x1, #0xb]
    // 0x1351f64: StoreField: r1->field_f = r0
    //     0x1351f64: stur            w0, [x1, #0xf]
    // 0x1351f68: StoreField: r1->field_13 = r0
    //     0x1351f68: stur            w0, [x1, #0x13]
    // 0x1351f6c: r0 = RoundedRectangleBorder()
    //     0x1351f6c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1351f70: mov             x1, x0
    // 0x1351f74: ldur            x0, [fp, #-0x40]
    // 0x1351f78: StoreField: r1->field_b = r0
    //     0x1351f78: stur            w0, [x1, #0xb]
    // 0x1351f7c: r0 = Instance_BorderSide
    //     0x1351f7c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1351f80: ldr             x0, [x0, #0xe20]
    // 0x1351f84: StoreField: r1->field_7 = r0
    //     0x1351f84: stur            w0, [x1, #7]
    // 0x1351f88: r16 = <RoundedRectangleBorder>
    //     0x1351f88: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1351f8c: ldr             x16, [x16, #0xf78]
    // 0x1351f90: stp             x1, x16, [SP]
    // 0x1351f94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1351f94: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1351f98: r0 = all()
    //     0x1351f98: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1351f9c: stur            x0, [fp, #-0x38]
    // 0x1351fa0: r0 = ButtonStyle()
    //     0x1351fa0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1351fa4: mov             x1, x0
    // 0x1351fa8: ldur            x0, [fp, #-0x30]
    // 0x1351fac: stur            x1, [fp, #-0x40]
    // 0x1351fb0: StoreField: r1->field_b = r0
    //     0x1351fb0: stur            w0, [x1, #0xb]
    // 0x1351fb4: ldur            x0, [fp, #-0x10]
    // 0x1351fb8: StoreField: r1->field_23 = r0
    //     0x1351fb8: stur            w0, [x1, #0x23]
    // 0x1351fbc: ldur            x0, [fp, #-0x38]
    // 0x1351fc0: StoreField: r1->field_43 = r0
    //     0x1351fc0: stur            w0, [x1, #0x43]
    // 0x1351fc4: r0 = TextButtonThemeData()
    //     0x1351fc4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1351fc8: mov             x2, x0
    // 0x1351fcc: ldur            x0, [fp, #-0x40]
    // 0x1351fd0: stur            x2, [fp, #-0x10]
    // 0x1351fd4: StoreField: r2->field_7 = r0
    //     0x1351fd4: stur            w0, [x2, #7]
    // 0x1351fd8: ldur            x0, [fp, #-8]
    // 0x1351fdc: LoadField: r1 = r0->field_13
    //     0x1351fdc: ldur            w1, [x0, #0x13]
    // 0x1351fe0: DecompressPointer r1
    //     0x1351fe0: add             x1, x1, HEAP, lsl #32
    // 0x1351fe4: r0 = of()
    //     0x1351fe4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1351fe8: LoadField: r1 = r0->field_87
    //     0x1351fe8: ldur            w1, [x0, #0x87]
    // 0x1351fec: DecompressPointer r1
    //     0x1351fec: add             x1, x1, HEAP, lsl #32
    // 0x1351ff0: LoadField: r0 = r1->field_7
    //     0x1351ff0: ldur            w0, [x1, #7]
    // 0x1351ff4: DecompressPointer r0
    //     0x1351ff4: add             x0, x0, HEAP, lsl #32
    // 0x1351ff8: r16 = 16.000000
    //     0x1351ff8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1351ffc: ldr             x16, [x16, #0x188]
    // 0x1352000: r30 = Instance_Color
    //     0x1352000: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1352004: stp             lr, x16, [SP]
    // 0x1352008: mov             x1, x0
    // 0x135200c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x135200c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1352010: ldr             x4, [x4, #0xaa0]
    // 0x1352014: r0 = copyWith()
    //     0x1352014: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1352018: stur            x0, [fp, #-0x30]
    // 0x135201c: r0 = Text()
    //     0x135201c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1352020: mov             x3, x0
    // 0x1352024: r0 = "Submit"
    //     0x1352024: add             x0, PP, #0x40, lsl #12  ; [pp+0x40028] "Submit"
    //     0x1352028: ldr             x0, [x0, #0x28]
    // 0x135202c: stur            x3, [fp, #-0x38]
    // 0x1352030: StoreField: r3->field_b = r0
    //     0x1352030: stur            w0, [x3, #0xb]
    // 0x1352034: ldur            x0, [fp, #-0x30]
    // 0x1352038: StoreField: r3->field_13 = r0
    //     0x1352038: stur            w0, [x3, #0x13]
    // 0x135203c: ldur            x2, [fp, #-8]
    // 0x1352040: r1 = Function '<anonymous closure>':.
    //     0x1352040: add             x1, PP, #0x42, lsl #12  ; [pp+0x42ff0] AnonymousClosure: (0x131efa4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::bottomNavigationBar (0x1369680)
    //     0x1352044: ldr             x1, [x1, #0xff0]
    // 0x1352048: r0 = AllocateClosure()
    //     0x1352048: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135204c: stur            x0, [fp, #-8]
    // 0x1352050: r0 = TextButton()
    //     0x1352050: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1352054: mov             x1, x0
    // 0x1352058: ldur            x0, [fp, #-8]
    // 0x135205c: stur            x1, [fp, #-0x30]
    // 0x1352060: StoreField: r1->field_b = r0
    //     0x1352060: stur            w0, [x1, #0xb]
    // 0x1352064: r0 = false
    //     0x1352064: add             x0, NULL, #0x30  ; false
    // 0x1352068: StoreField: r1->field_27 = r0
    //     0x1352068: stur            w0, [x1, #0x27]
    // 0x135206c: r0 = true
    //     0x135206c: add             x0, NULL, #0x20  ; true
    // 0x1352070: StoreField: r1->field_2f = r0
    //     0x1352070: stur            w0, [x1, #0x2f]
    // 0x1352074: ldur            x0, [fp, #-0x38]
    // 0x1352078: StoreField: r1->field_37 = r0
    //     0x1352078: stur            w0, [x1, #0x37]
    // 0x135207c: r0 = Instance_ValueKey
    //     0x135207c: add             x0, PP, #0x42, lsl #12  ; [pp+0x42ff8] Obj!ValueKey<String>@d5b391
    //     0x1352080: ldr             x0, [x0, #0xff8]
    // 0x1352084: StoreField: r1->field_7 = r0
    //     0x1352084: stur            w0, [x1, #7]
    // 0x1352088: r0 = TextButtonTheme()
    //     0x1352088: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x135208c: mov             x1, x0
    // 0x1352090: ldur            x0, [fp, #-0x10]
    // 0x1352094: stur            x1, [fp, #-8]
    // 0x1352098: StoreField: r1->field_f = r0
    //     0x1352098: stur            w0, [x1, #0xf]
    // 0x135209c: ldur            x0, [fp, #-0x30]
    // 0x13520a0: StoreField: r1->field_b = r0
    //     0x13520a0: stur            w0, [x1, #0xb]
    // 0x13520a4: r0 = SizedBox()
    //     0x13520a4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13520a8: mov             x1, x0
    // 0x13520ac: r0 = inf
    //     0x13520ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x13520b0: ldr             x0, [x0, #0x9f8]
    // 0x13520b4: stur            x1, [fp, #-0x10]
    // 0x13520b8: StoreField: r1->field_f = r0
    //     0x13520b8: stur            w0, [x1, #0xf]
    // 0x13520bc: ldur            x0, [fp, #-8]
    // 0x13520c0: StoreField: r1->field_b = r0
    //     0x13520c0: stur            w0, [x1, #0xb]
    // 0x13520c4: r0 = Padding()
    //     0x13520c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13520c8: mov             x3, x0
    // 0x13520cc: r0 = Instance_EdgeInsets
    //     0x13520cc: add             x0, PP, #0x36, lsl #12  ; [pp+0x366d8] Obj!EdgeInsets@d59a81
    //     0x13520d0: ldr             x0, [x0, #0x6d8]
    // 0x13520d4: stur            x3, [fp, #-8]
    // 0x13520d8: StoreField: r3->field_f = r0
    //     0x13520d8: stur            w0, [x3, #0xf]
    // 0x13520dc: ldur            x0, [fp, #-0x10]
    // 0x13520e0: StoreField: r3->field_b = r0
    //     0x13520e0: stur            w0, [x3, #0xb]
    // 0x13520e4: r1 = Null
    //     0x13520e4: mov             x1, NULL
    // 0x13520e8: r2 = 4
    //     0x13520e8: movz            x2, #0x4
    // 0x13520ec: r0 = AllocateArray()
    //     0x13520ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13520f0: stur            x0, [fp, #-0x10]
    // 0x13520f4: r16 = Instance_Padding
    //     0x13520f4: add             x16, PP, #0x36, lsl #12  ; [pp+0x366e0] Obj!Padding@d68561
    //     0x13520f8: ldr             x16, [x16, #0x6e0]
    // 0x13520fc: StoreField: r0->field_f = r16
    //     0x13520fc: stur            w16, [x0, #0xf]
    // 0x1352100: ldur            x1, [fp, #-8]
    // 0x1352104: StoreField: r0->field_13 = r1
    //     0x1352104: stur            w1, [x0, #0x13]
    // 0x1352108: r1 = <Widget>
    //     0x1352108: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x135210c: r0 = AllocateGrowableArray()
    //     0x135210c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1352110: mov             x1, x0
    // 0x1352114: ldur            x0, [fp, #-0x10]
    // 0x1352118: stur            x1, [fp, #-8]
    // 0x135211c: StoreField: r1->field_f = r0
    //     0x135211c: stur            w0, [x1, #0xf]
    // 0x1352120: r0 = 4
    //     0x1352120: movz            x0, #0x4
    // 0x1352124: StoreField: r1->field_b = r0
    //     0x1352124: stur            w0, [x1, #0xb]
    // 0x1352128: r0 = Wrap()
    //     0x1352128: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x135212c: r1 = Instance_Axis
    //     0x135212c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1352130: StoreField: r0->field_f = r1
    //     0x1352130: stur            w1, [x0, #0xf]
    // 0x1352134: r1 = Instance_WrapAlignment
    //     0x1352134: add             x1, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x1352138: ldr             x1, [x1, #0x6e8]
    // 0x135213c: StoreField: r0->field_13 = r1
    //     0x135213c: stur            w1, [x0, #0x13]
    // 0x1352140: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1352140: stur            xzr, [x0, #0x17]
    // 0x1352144: StoreField: r0->field_1f = r1
    //     0x1352144: stur            w1, [x0, #0x1f]
    // 0x1352148: StoreField: r0->field_23 = rZR
    //     0x1352148: stur            xzr, [x0, #0x23]
    // 0x135214c: r1 = Instance_WrapCrossAlignment
    //     0x135214c: add             x1, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x1352150: ldr             x1, [x1, #0x6f0]
    // 0x1352154: StoreField: r0->field_2b = r1
    //     0x1352154: stur            w1, [x0, #0x2b]
    // 0x1352158: r1 = Instance_VerticalDirection
    //     0x1352158: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x135215c: ldr             x1, [x1, #0xa20]
    // 0x1352160: StoreField: r0->field_33 = r1
    //     0x1352160: stur            w1, [x0, #0x33]
    // 0x1352164: r1 = Instance_Clip
    //     0x1352164: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1352168: ldr             x1, [x1, #0x38]
    // 0x135216c: StoreField: r0->field_37 = r1
    //     0x135216c: stur            w1, [x0, #0x37]
    // 0x1352170: ldur            x1, [fp, #-8]
    // 0x1352174: StoreField: r0->field_b = r1
    //     0x1352174: stur            w1, [x0, #0xb]
    // 0x1352178: LeaveFrame
    //     0x1352178: mov             SP, fp
    //     0x135217c: ldp             fp, lr, [SP], #0x10
    // 0x1352180: ret
    //     0x1352180: ret             
    // 0x1352184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1352184: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1352188: b               #0x1351af4
  }
  _ body(/* No info */) {
    // ** addr: 0x14a8868, size: 0x94
    // 0x14a8868: EnterFrame
    //     0x14a8868: stp             fp, lr, [SP, #-0x10]!
    //     0x14a886c: mov             fp, SP
    // 0x14a8870: AllocStack(0x18)
    //     0x14a8870: sub             SP, SP, #0x18
    // 0x14a8874: SetupParameters(RatingReviewOrderPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14a8874: mov             x0, x1
    //     0x14a8878: stur            x1, [fp, #-8]
    //     0x14a887c: stur            x2, [fp, #-0x10]
    // 0x14a8880: r1 = 2
    //     0x14a8880: movz            x1, #0x2
    // 0x14a8884: r0 = AllocateContext()
    //     0x14a8884: bl              #0x16f6108  ; AllocateContextStub
    // 0x14a8888: ldur            x2, [fp, #-8]
    // 0x14a888c: stur            x0, [fp, #-0x18]
    // 0x14a8890: StoreField: r0->field_f = r2
    //     0x14a8890: stur            w2, [x0, #0xf]
    // 0x14a8894: ldur            x1, [fp, #-0x10]
    // 0x14a8898: StoreField: r0->field_13 = r1
    //     0x14a8898: stur            w1, [x0, #0x13]
    // 0x14a889c: r0 = Obx()
    //     0x14a889c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14a88a0: ldur            x2, [fp, #-0x18]
    // 0x14a88a4: r1 = Function '<anonymous closure>':.
    //     0x14a88a4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43000] AnonymousClosure: (0x14a8934), in [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14a8868)
    //     0x14a88a8: ldr             x1, [x1]
    // 0x14a88ac: stur            x0, [fp, #-0x10]
    // 0x14a88b0: r0 = AllocateClosure()
    //     0x14a88b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a88b4: mov             x1, x0
    // 0x14a88b8: ldur            x0, [fp, #-0x10]
    // 0x14a88bc: StoreField: r0->field_b = r1
    //     0x14a88bc: stur            w1, [x0, #0xb]
    // 0x14a88c0: r0 = WillPopScope()
    //     0x14a88c0: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14a88c4: mov             x3, x0
    // 0x14a88c8: ldur            x0, [fp, #-0x10]
    // 0x14a88cc: stur            x3, [fp, #-0x18]
    // 0x14a88d0: StoreField: r3->field_b = r0
    //     0x14a88d0: stur            w0, [x3, #0xb]
    // 0x14a88d4: ldur            x2, [fp, #-8]
    // 0x14a88d8: r1 = Function '_onBackPress@1481292561':.
    //     0x14a88d8: add             x1, PP, #0x43, lsl #12  ; [pp+0x43008] AnonymousClosure: (0x14a88fc), in [package:customer_app/app/presentation/views/basic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_onBackPress (0x14071f8)
    //     0x14a88dc: ldr             x1, [x1, #8]
    // 0x14a88e0: r0 = AllocateClosure()
    //     0x14a88e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a88e4: mov             x1, x0
    // 0x14a88e8: ldur            x0, [fp, #-0x18]
    // 0x14a88ec: StoreField: r0->field_f = r1
    //     0x14a88ec: stur            w1, [x0, #0xf]
    // 0x14a88f0: LeaveFrame
    //     0x14a88f0: mov             SP, fp
    //     0x14a88f4: ldp             fp, lr, [SP], #0x10
    // 0x14a88f8: ret
    //     0x14a88f8: ret             
  }
  [closure] Future<bool> _onBackPress(dynamic) {
    // ** addr: 0x14a88fc, size: 0x38
    // 0x14a88fc: EnterFrame
    //     0x14a88fc: stp             fp, lr, [SP, #-0x10]!
    //     0x14a8900: mov             fp, SP
    // 0x14a8904: ldr             x0, [fp, #0x10]
    // 0x14a8908: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14a8908: ldur            w1, [x0, #0x17]
    // 0x14a890c: DecompressPointer r1
    //     0x14a890c: add             x1, x1, HEAP, lsl #32
    // 0x14a8910: CheckStackOverflow
    //     0x14a8910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a8914: cmp             SP, x16
    //     0x14a8918: b.ls            #0x14a892c
    // 0x14a891c: r0 = _onBackPress()
    //     0x14a891c: bl              #0x14071f8  ; [package:customer_app/app/presentation/views/basic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_onBackPress
    // 0x14a8920: LeaveFrame
    //     0x14a8920: mov             SP, fp
    //     0x14a8924: ldp             fp, lr, [SP], #0x10
    // 0x14a8928: ret
    //     0x14a8928: ret             
    // 0x14a892c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a892c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a8930: b               #0x14a891c
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x14a8934, size: 0x1498
    // 0x14a8934: EnterFrame
    //     0x14a8934: stp             fp, lr, [SP, #-0x10]!
    //     0x14a8938: mov             fp, SP
    // 0x14a893c: AllocStack(0xd0)
    //     0x14a893c: sub             SP, SP, #0xd0
    // 0x14a8940: SetupParameters()
    //     0x14a8940: ldr             x0, [fp, #0x10]
    //     0x14a8944: ldur            w2, [x0, #0x17]
    //     0x14a8948: add             x2, x2, HEAP, lsl #32
    //     0x14a894c: stur            x2, [fp, #-8]
    // 0x14a8950: CheckStackOverflow
    //     0x14a8950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a8954: cmp             SP, x16
    //     0x14a8958: b.ls            #0x14a9db4
    // 0x14a895c: r0 = Obx()
    //     0x14a895c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14a8960: ldur            x2, [fp, #-8]
    // 0x14a8964: r1 = Function '<anonymous closure>':.
    //     0x14a8964: add             x1, PP, #0x43, lsl #12  ; [pp+0x43010] AnonymousClosure: (0x14aaa5c), in [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14a8868)
    //     0x14a8968: ldr             x1, [x1, #0x10]
    // 0x14a896c: stur            x0, [fp, #-0x10]
    // 0x14a8970: r0 = AllocateClosure()
    //     0x14a8970: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8974: mov             x1, x0
    // 0x14a8978: ldur            x0, [fp, #-0x10]
    // 0x14a897c: StoreField: r0->field_b = r1
    //     0x14a897c: stur            w1, [x0, #0xb]
    // 0x14a8980: r0 = Radius()
    //     0x14a8980: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14a8984: d0 = 15.000000
    //     0x14a8984: fmov            d0, #15.00000000
    // 0x14a8988: stur            x0, [fp, #-0x18]
    // 0x14a898c: StoreField: r0->field_7 = d0
    //     0x14a898c: stur            d0, [x0, #7]
    // 0x14a8990: StoreField: r0->field_f = d0
    //     0x14a8990: stur            d0, [x0, #0xf]
    // 0x14a8994: r0 = BorderRadius()
    //     0x14a8994: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14a8998: mov             x1, x0
    // 0x14a899c: ldur            x0, [fp, #-0x18]
    // 0x14a89a0: stur            x1, [fp, #-0x20]
    // 0x14a89a4: StoreField: r1->field_7 = r0
    //     0x14a89a4: stur            w0, [x1, #7]
    // 0x14a89a8: StoreField: r1->field_b = r0
    //     0x14a89a8: stur            w0, [x1, #0xb]
    // 0x14a89ac: StoreField: r1->field_f = r0
    //     0x14a89ac: stur            w0, [x1, #0xf]
    // 0x14a89b0: StoreField: r1->field_13 = r0
    //     0x14a89b0: stur            w0, [x1, #0x13]
    // 0x14a89b4: r0 = BoxDecoration()
    //     0x14a89b4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14a89b8: mov             x1, x0
    // 0x14a89bc: r0 = Instance_Color
    //     0x14a89bc: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a89c0: stur            x1, [fp, #-0x18]
    // 0x14a89c4: StoreField: r1->field_7 = r0
    //     0x14a89c4: stur            w0, [x1, #7]
    // 0x14a89c8: ldur            x2, [fp, #-0x20]
    // 0x14a89cc: StoreField: r1->field_13 = r2
    //     0x14a89cc: stur            w2, [x1, #0x13]
    // 0x14a89d0: r2 = Instance_BoxShape
    //     0x14a89d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a89d4: ldr             x2, [x2, #0x80]
    // 0x14a89d8: StoreField: r1->field_23 = r2
    //     0x14a89d8: stur            w2, [x1, #0x23]
    // 0x14a89dc: r0 = Radius()
    //     0x14a89dc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14a89e0: d0 = 15.000000
    //     0x14a89e0: fmov            d0, #15.00000000
    // 0x14a89e4: stur            x0, [fp, #-0x20]
    // 0x14a89e8: StoreField: r0->field_7 = d0
    //     0x14a89e8: stur            d0, [x0, #7]
    // 0x14a89ec: StoreField: r0->field_f = d0
    //     0x14a89ec: stur            d0, [x0, #0xf]
    // 0x14a89f0: r0 = BorderRadius()
    //     0x14a89f0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14a89f4: mov             x2, x0
    // 0x14a89f8: ldur            x0, [fp, #-0x20]
    // 0x14a89fc: stur            x2, [fp, #-0x28]
    // 0x14a8a00: StoreField: r2->field_7 = r0
    //     0x14a8a00: stur            w0, [x2, #7]
    // 0x14a8a04: StoreField: r2->field_b = r0
    //     0x14a8a04: stur            w0, [x2, #0xb]
    // 0x14a8a08: StoreField: r2->field_f = r0
    //     0x14a8a08: stur            w0, [x2, #0xf]
    // 0x14a8a0c: StoreField: r2->field_13 = r0
    //     0x14a8a0c: stur            w0, [x2, #0x13]
    // 0x14a8a10: ldur            x0, [fp, #-8]
    // 0x14a8a14: LoadField: r1 = r0->field_f
    //     0x14a8a14: ldur            w1, [x0, #0xf]
    // 0x14a8a18: DecompressPointer r1
    //     0x14a8a18: add             x1, x1, HEAP, lsl #32
    // 0x14a8a1c: r0 = controller()
    //     0x14a8a1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8a20: LoadField: r1 = r0->field_4b
    //     0x14a8a20: ldur            w1, [x0, #0x4b]
    // 0x14a8a24: DecompressPointer r1
    //     0x14a8a24: add             x1, x1, HEAP, lsl #32
    // 0x14a8a28: r0 = value()
    //     0x14a8a28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a8a2c: LoadField: r1 = r0->field_b
    //     0x14a8a2c: ldur            w1, [x0, #0xb]
    // 0x14a8a30: DecompressPointer r1
    //     0x14a8a30: add             x1, x1, HEAP, lsl #32
    // 0x14a8a34: cmp             w1, NULL
    // 0x14a8a38: b.ne            #0x14a8a44
    // 0x14a8a3c: r0 = Null
    //     0x14a8a3c: mov             x0, NULL
    // 0x14a8a40: b               #0x14a8a4c
    // 0x14a8a44: LoadField: r0 = r1->field_f
    //     0x14a8a44: ldur            w0, [x1, #0xf]
    // 0x14a8a48: DecompressPointer r0
    //     0x14a8a48: add             x0, x0, HEAP, lsl #32
    // 0x14a8a4c: cmp             w0, NULL
    // 0x14a8a50: b.ne            #0x14a8a5c
    // 0x14a8a54: r4 = ""
    //     0x14a8a54: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a8a58: b               #0x14a8a60
    // 0x14a8a5c: mov             x4, x0
    // 0x14a8a60: ldur            x3, [fp, #-8]
    // 0x14a8a64: ldur            x0, [fp, #-0x28]
    // 0x14a8a68: stur            x4, [fp, #-0x20]
    // 0x14a8a6c: r1 = Function '<anonymous closure>':.
    //     0x14a8a6c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43018] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x14a8a70: ldr             x1, [x1, #0x18]
    // 0x14a8a74: r2 = Null
    //     0x14a8a74: mov             x2, NULL
    // 0x14a8a78: r0 = AllocateClosure()
    //     0x14a8a78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8a7c: stur            x0, [fp, #-0x30]
    // 0x14a8a80: r0 = CachedNetworkImage()
    //     0x14a8a80: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14a8a84: stur            x0, [fp, #-0x38]
    // 0x14a8a88: r16 = 56.000000
    //     0x14a8a88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x14a8a8c: ldr             x16, [x16, #0xb78]
    // 0x14a8a90: r30 = 56.000000
    //     0x14a8a90: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x14a8a94: ldr             lr, [lr, #0xb78]
    // 0x14a8a98: stp             lr, x16, [SP, #0x10]
    // 0x14a8a9c: ldur            x16, [fp, #-0x30]
    // 0x14a8aa0: r30 = Instance_BoxFit
    //     0x14a8aa0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14a8aa4: ldr             lr, [lr, #0x118]
    // 0x14a8aa8: stp             lr, x16, [SP]
    // 0x14a8aac: mov             x1, x0
    // 0x14a8ab0: ldur            x2, [fp, #-0x20]
    // 0x14a8ab4: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, height, 0x2, width, 0x3, null]
    //     0x14a8ab4: add             x4, PP, #0x40, lsl #12  ; [pp+0x40060] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "height", 0x2, "width", 0x3, Null]
    //     0x14a8ab8: ldr             x4, [x4, #0x60]
    // 0x14a8abc: r0 = CachedNetworkImage()
    //     0x14a8abc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14a8ac0: r0 = ClipRRect()
    //     0x14a8ac0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14a8ac4: mov             x2, x0
    // 0x14a8ac8: ldur            x0, [fp, #-0x28]
    // 0x14a8acc: stur            x2, [fp, #-0x20]
    // 0x14a8ad0: StoreField: r2->field_f = r0
    //     0x14a8ad0: stur            w0, [x2, #0xf]
    // 0x14a8ad4: r0 = Instance_Clip
    //     0x14a8ad4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14a8ad8: ldr             x0, [x0, #0x138]
    // 0x14a8adc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14a8adc: stur            w0, [x2, #0x17]
    // 0x14a8ae0: ldur            x0, [fp, #-0x38]
    // 0x14a8ae4: StoreField: r2->field_b = r0
    //     0x14a8ae4: stur            w0, [x2, #0xb]
    // 0x14a8ae8: ldur            x0, [fp, #-8]
    // 0x14a8aec: LoadField: r1 = r0->field_f
    //     0x14a8aec: ldur            w1, [x0, #0xf]
    // 0x14a8af0: DecompressPointer r1
    //     0x14a8af0: add             x1, x1, HEAP, lsl #32
    // 0x14a8af4: r0 = controller()
    //     0x14a8af4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8af8: LoadField: r1 = r0->field_4b
    //     0x14a8af8: ldur            w1, [x0, #0x4b]
    // 0x14a8afc: DecompressPointer r1
    //     0x14a8afc: add             x1, x1, HEAP, lsl #32
    // 0x14a8b00: r0 = value()
    //     0x14a8b00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a8b04: LoadField: r1 = r0->field_b
    //     0x14a8b04: ldur            w1, [x0, #0xb]
    // 0x14a8b08: DecompressPointer r1
    //     0x14a8b08: add             x1, x1, HEAP, lsl #32
    // 0x14a8b0c: cmp             w1, NULL
    // 0x14a8b10: b.ne            #0x14a8b1c
    // 0x14a8b14: r0 = Null
    //     0x14a8b14: mov             x0, NULL
    // 0x14a8b18: b               #0x14a8b24
    // 0x14a8b1c: LoadField: r0 = r1->field_13
    //     0x14a8b1c: ldur            w0, [x1, #0x13]
    // 0x14a8b20: DecompressPointer r0
    //     0x14a8b20: add             x0, x0, HEAP, lsl #32
    // 0x14a8b24: cmp             w0, NULL
    // 0x14a8b28: b.ne            #0x14a8b34
    // 0x14a8b2c: r3 = ""
    //     0x14a8b2c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a8b30: b               #0x14a8b38
    // 0x14a8b34: mov             x3, x0
    // 0x14a8b38: ldur            x2, [fp, #-8]
    // 0x14a8b3c: ldur            x0, [fp, #-0x20]
    // 0x14a8b40: stur            x3, [fp, #-0x28]
    // 0x14a8b44: LoadField: r1 = r2->field_13
    //     0x14a8b44: ldur            w1, [x2, #0x13]
    // 0x14a8b48: DecompressPointer r1
    //     0x14a8b48: add             x1, x1, HEAP, lsl #32
    // 0x14a8b4c: r0 = of()
    //     0x14a8b4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a8b50: LoadField: r1 = r0->field_87
    //     0x14a8b50: ldur            w1, [x0, #0x87]
    // 0x14a8b54: DecompressPointer r1
    //     0x14a8b54: add             x1, x1, HEAP, lsl #32
    // 0x14a8b58: LoadField: r0 = r1->field_7
    //     0x14a8b58: ldur            w0, [x1, #7]
    // 0x14a8b5c: DecompressPointer r0
    //     0x14a8b5c: add             x0, x0, HEAP, lsl #32
    // 0x14a8b60: r16 = 14.000000
    //     0x14a8b60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14a8b64: ldr             x16, [x16, #0x1d8]
    // 0x14a8b68: r30 = Instance_Color
    //     0x14a8b68: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a8b6c: stp             lr, x16, [SP]
    // 0x14a8b70: mov             x1, x0
    // 0x14a8b74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a8b74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a8b78: ldr             x4, [x4, #0xaa0]
    // 0x14a8b7c: r0 = copyWith()
    //     0x14a8b7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a8b80: stur            x0, [fp, #-0x30]
    // 0x14a8b84: r0 = Text()
    //     0x14a8b84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a8b88: mov             x1, x0
    // 0x14a8b8c: ldur            x0, [fp, #-0x28]
    // 0x14a8b90: stur            x1, [fp, #-0x38]
    // 0x14a8b94: StoreField: r1->field_b = r0
    //     0x14a8b94: stur            w0, [x1, #0xb]
    // 0x14a8b98: ldur            x0, [fp, #-0x30]
    // 0x14a8b9c: StoreField: r1->field_13 = r0
    //     0x14a8b9c: stur            w0, [x1, #0x13]
    // 0x14a8ba0: r0 = Instance_TextOverflow
    //     0x14a8ba0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14a8ba4: ldr             x0, [x0, #0xe10]
    // 0x14a8ba8: StoreField: r1->field_2b = r0
    //     0x14a8ba8: stur            w0, [x1, #0x2b]
    // 0x14a8bac: r2 = 4
    //     0x14a8bac: movz            x2, #0x4
    // 0x14a8bb0: StoreField: r1->field_37 = r2
    //     0x14a8bb0: stur            w2, [x1, #0x37]
    // 0x14a8bb4: r0 = Padding()
    //     0x14a8bb4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a8bb8: mov             x2, x0
    // 0x14a8bbc: r0 = Instance_EdgeInsets
    //     0x14a8bbc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x14a8bc0: ldr             x0, [x0, #0x778]
    // 0x14a8bc4: stur            x2, [fp, #-0x28]
    // 0x14a8bc8: StoreField: r2->field_f = r0
    //     0x14a8bc8: stur            w0, [x2, #0xf]
    // 0x14a8bcc: ldur            x0, [fp, #-0x38]
    // 0x14a8bd0: StoreField: r2->field_b = r0
    //     0x14a8bd0: stur            w0, [x2, #0xb]
    // 0x14a8bd4: ldur            x0, [fp, #-8]
    // 0x14a8bd8: LoadField: r1 = r0->field_f
    //     0x14a8bd8: ldur            w1, [x0, #0xf]
    // 0x14a8bdc: DecompressPointer r1
    //     0x14a8bdc: add             x1, x1, HEAP, lsl #32
    // 0x14a8be0: r0 = controller()
    //     0x14a8be0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8be4: LoadField: r1 = r0->field_c3
    //     0x14a8be4: ldur            w1, [x0, #0xc3]
    // 0x14a8be8: DecompressPointer r1
    //     0x14a8be8: add             x1, x1, HEAP, lsl #32
    // 0x14a8bec: r0 = value()
    //     0x14a8bec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a8bf0: ldur            x2, [fp, #-8]
    // 0x14a8bf4: r1 = Function '<anonymous closure>':.
    //     0x14a8bf4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43020] AnonymousClosure: (0x140de64), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x14a8bf8: ldr             x1, [x1, #0x20]
    // 0x14a8bfc: stur            x0, [fp, #-0x30]
    // 0x14a8c00: r0 = AllocateClosure()
    //     0x14a8c00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8c04: stur            x0, [fp, #-0x38]
    // 0x14a8c08: r0 = RatingBar()
    //     0x14a8c08: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0x14a8c0c: mov             x3, x0
    // 0x14a8c10: ldur            x0, [fp, #-0x38]
    // 0x14a8c14: stur            x3, [fp, #-0x40]
    // 0x14a8c18: StoreField: r3->field_b = r0
    //     0x14a8c18: stur            w0, [x3, #0xb]
    // 0x14a8c1c: r0 = false
    //     0x14a8c1c: add             x0, NULL, #0x30  ; false
    // 0x14a8c20: StoreField: r3->field_1f = r0
    //     0x14a8c20: stur            w0, [x3, #0x1f]
    // 0x14a8c24: r4 = Instance_Axis
    //     0x14a8c24: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a8c28: StoreField: r3->field_23 = r4
    //     0x14a8c28: stur            w4, [x3, #0x23]
    // 0x14a8c2c: r5 = true
    //     0x14a8c2c: add             x5, NULL, #0x20  ; true
    // 0x14a8c30: StoreField: r3->field_27 = r5
    //     0x14a8c30: stur            w5, [x3, #0x27]
    // 0x14a8c34: d0 = 2.000000
    //     0x14a8c34: fmov            d0, #2.00000000
    // 0x14a8c38: StoreField: r3->field_2b = d0
    //     0x14a8c38: stur            d0, [x3, #0x2b]
    // 0x14a8c3c: StoreField: r3->field_33 = r0
    //     0x14a8c3c: stur            w0, [x3, #0x33]
    // 0x14a8c40: ldur            x1, [fp, #-0x30]
    // 0x14a8c44: LoadField: d0 = r1->field_7
    //     0x14a8c44: ldur            d0, [x1, #7]
    // 0x14a8c48: StoreField: r3->field_37 = d0
    //     0x14a8c48: stur            d0, [x3, #0x37]
    // 0x14a8c4c: r1 = 5
    //     0x14a8c4c: movz            x1, #0x5
    // 0x14a8c50: StoreField: r3->field_3f = r1
    //     0x14a8c50: stur            x1, [x3, #0x3f]
    // 0x14a8c54: r1 = Instance_EdgeInsets
    //     0x14a8c54: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0x14a8c58: ldr             x1, [x1, #0xa68]
    // 0x14a8c5c: StoreField: r3->field_47 = r1
    //     0x14a8c5c: stur            w1, [x3, #0x47]
    // 0x14a8c60: d0 = 30.000000
    //     0x14a8c60: fmov            d0, #30.00000000
    // 0x14a8c64: StoreField: r3->field_4b = d0
    //     0x14a8c64: stur            d0, [x3, #0x4b]
    // 0x14a8c68: d0 = 1.000000
    //     0x14a8c68: fmov            d0, #1.00000000
    // 0x14a8c6c: StoreField: r3->field_53 = d0
    //     0x14a8c6c: stur            d0, [x3, #0x53]
    // 0x14a8c70: StoreField: r3->field_5b = r0
    //     0x14a8c70: stur            w0, [x3, #0x5b]
    // 0x14a8c74: StoreField: r3->field_5f = r0
    //     0x14a8c74: stur            w0, [x3, #0x5f]
    // 0x14a8c78: ldur            x2, [fp, #-8]
    // 0x14a8c7c: r1 = Function '<anonymous closure>':.
    //     0x14a8c7c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43028] AnonymousClosure: (0x140dcc0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x14a8c80: ldr             x1, [x1, #0x28]
    // 0x14a8c84: r0 = AllocateClosure()
    //     0x14a8c84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8c88: mov             x1, x0
    // 0x14a8c8c: ldur            x0, [fp, #-0x40]
    // 0x14a8c90: StoreField: r0->field_63 = r1
    //     0x14a8c90: stur            w1, [x0, #0x63]
    // 0x14a8c94: r1 = Null
    //     0x14a8c94: mov             x1, NULL
    // 0x14a8c98: r2 = 4
    //     0x14a8c98: movz            x2, #0x4
    // 0x14a8c9c: r0 = AllocateArray()
    //     0x14a8c9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a8ca0: mov             x2, x0
    // 0x14a8ca4: ldur            x0, [fp, #-0x28]
    // 0x14a8ca8: stur            x2, [fp, #-0x30]
    // 0x14a8cac: StoreField: r2->field_f = r0
    //     0x14a8cac: stur            w0, [x2, #0xf]
    // 0x14a8cb0: ldur            x0, [fp, #-0x40]
    // 0x14a8cb4: StoreField: r2->field_13 = r0
    //     0x14a8cb4: stur            w0, [x2, #0x13]
    // 0x14a8cb8: r1 = <Widget>
    //     0x14a8cb8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a8cbc: r0 = AllocateGrowableArray()
    //     0x14a8cbc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a8cc0: mov             x1, x0
    // 0x14a8cc4: ldur            x0, [fp, #-0x30]
    // 0x14a8cc8: stur            x1, [fp, #-0x28]
    // 0x14a8ccc: StoreField: r1->field_f = r0
    //     0x14a8ccc: stur            w0, [x1, #0xf]
    // 0x14a8cd0: r2 = 4
    //     0x14a8cd0: movz            x2, #0x4
    // 0x14a8cd4: StoreField: r1->field_b = r2
    //     0x14a8cd4: stur            w2, [x1, #0xb]
    // 0x14a8cd8: r0 = Column()
    //     0x14a8cd8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14a8cdc: mov             x1, x0
    // 0x14a8ce0: r0 = Instance_Axis
    //     0x14a8ce0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a8ce4: stur            x1, [fp, #-0x30]
    // 0x14a8ce8: StoreField: r1->field_f = r0
    //     0x14a8ce8: stur            w0, [x1, #0xf]
    // 0x14a8cec: r2 = Instance_MainAxisAlignment
    //     0x14a8cec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a8cf0: ldr             x2, [x2, #0xa08]
    // 0x14a8cf4: StoreField: r1->field_13 = r2
    //     0x14a8cf4: stur            w2, [x1, #0x13]
    // 0x14a8cf8: r3 = Instance_MainAxisSize
    //     0x14a8cf8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a8cfc: ldr             x3, [x3, #0xa10]
    // 0x14a8d00: ArrayStore: r1[0] = r3  ; List_4
    //     0x14a8d00: stur            w3, [x1, #0x17]
    // 0x14a8d04: r4 = Instance_CrossAxisAlignment
    //     0x14a8d04: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14a8d08: ldr             x4, [x4, #0x890]
    // 0x14a8d0c: StoreField: r1->field_1b = r4
    //     0x14a8d0c: stur            w4, [x1, #0x1b]
    // 0x14a8d10: r5 = Instance_VerticalDirection
    //     0x14a8d10: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a8d14: ldr             x5, [x5, #0xa20]
    // 0x14a8d18: StoreField: r1->field_23 = r5
    //     0x14a8d18: stur            w5, [x1, #0x23]
    // 0x14a8d1c: r6 = Instance_Clip
    //     0x14a8d1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a8d20: ldr             x6, [x6, #0x38]
    // 0x14a8d24: StoreField: r1->field_2b = r6
    //     0x14a8d24: stur            w6, [x1, #0x2b]
    // 0x14a8d28: StoreField: r1->field_2f = rZR
    //     0x14a8d28: stur            xzr, [x1, #0x2f]
    // 0x14a8d2c: ldur            x7, [fp, #-0x28]
    // 0x14a8d30: StoreField: r1->field_b = r7
    //     0x14a8d30: stur            w7, [x1, #0xb]
    // 0x14a8d34: r0 = Padding()
    //     0x14a8d34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a8d38: mov             x2, x0
    // 0x14a8d3c: r0 = Instance_EdgeInsets
    //     0x14a8d3c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x14a8d40: ldr             x0, [x0, #0xa78]
    // 0x14a8d44: stur            x2, [fp, #-0x28]
    // 0x14a8d48: StoreField: r2->field_f = r0
    //     0x14a8d48: stur            w0, [x2, #0xf]
    // 0x14a8d4c: ldur            x0, [fp, #-0x30]
    // 0x14a8d50: StoreField: r2->field_b = r0
    //     0x14a8d50: stur            w0, [x2, #0xb]
    // 0x14a8d54: r1 = <FlexParentData>
    //     0x14a8d54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14a8d58: ldr             x1, [x1, #0xe00]
    // 0x14a8d5c: r0 = Expanded()
    //     0x14a8d5c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14a8d60: mov             x3, x0
    // 0x14a8d64: r0 = 1
    //     0x14a8d64: movz            x0, #0x1
    // 0x14a8d68: stur            x3, [fp, #-0x30]
    // 0x14a8d6c: StoreField: r3->field_13 = r0
    //     0x14a8d6c: stur            x0, [x3, #0x13]
    // 0x14a8d70: r4 = Instance_FlexFit
    //     0x14a8d70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14a8d74: ldr             x4, [x4, #0xe08]
    // 0x14a8d78: StoreField: r3->field_1b = r4
    //     0x14a8d78: stur            w4, [x3, #0x1b]
    // 0x14a8d7c: ldur            x1, [fp, #-0x28]
    // 0x14a8d80: StoreField: r3->field_b = r1
    //     0x14a8d80: stur            w1, [x3, #0xb]
    // 0x14a8d84: r1 = Null
    //     0x14a8d84: mov             x1, NULL
    // 0x14a8d88: r2 = 4
    //     0x14a8d88: movz            x2, #0x4
    // 0x14a8d8c: r0 = AllocateArray()
    //     0x14a8d8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a8d90: mov             x2, x0
    // 0x14a8d94: ldur            x0, [fp, #-0x20]
    // 0x14a8d98: stur            x2, [fp, #-0x28]
    // 0x14a8d9c: StoreField: r2->field_f = r0
    //     0x14a8d9c: stur            w0, [x2, #0xf]
    // 0x14a8da0: ldur            x0, [fp, #-0x30]
    // 0x14a8da4: StoreField: r2->field_13 = r0
    //     0x14a8da4: stur            w0, [x2, #0x13]
    // 0x14a8da8: r1 = <Widget>
    //     0x14a8da8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a8dac: r0 = AllocateGrowableArray()
    //     0x14a8dac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a8db0: mov             x1, x0
    // 0x14a8db4: ldur            x0, [fp, #-0x28]
    // 0x14a8db8: stur            x1, [fp, #-0x20]
    // 0x14a8dbc: StoreField: r1->field_f = r0
    //     0x14a8dbc: stur            w0, [x1, #0xf]
    // 0x14a8dc0: r2 = 4
    //     0x14a8dc0: movz            x2, #0x4
    // 0x14a8dc4: StoreField: r1->field_b = r2
    //     0x14a8dc4: stur            w2, [x1, #0xb]
    // 0x14a8dc8: r0 = Row()
    //     0x14a8dc8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14a8dcc: mov             x1, x0
    // 0x14a8dd0: r0 = Instance_Axis
    //     0x14a8dd0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a8dd4: stur            x1, [fp, #-0x28]
    // 0x14a8dd8: StoreField: r1->field_f = r0
    //     0x14a8dd8: stur            w0, [x1, #0xf]
    // 0x14a8ddc: r2 = Instance_MainAxisAlignment
    //     0x14a8ddc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a8de0: ldr             x2, [x2, #0xa08]
    // 0x14a8de4: StoreField: r1->field_13 = r2
    //     0x14a8de4: stur            w2, [x1, #0x13]
    // 0x14a8de8: r3 = Instance_MainAxisSize
    //     0x14a8de8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a8dec: ldr             x3, [x3, #0xa10]
    // 0x14a8df0: ArrayStore: r1[0] = r3  ; List_4
    //     0x14a8df0: stur            w3, [x1, #0x17]
    // 0x14a8df4: r4 = Instance_CrossAxisAlignment
    //     0x14a8df4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14a8df8: ldr             x4, [x4, #0x890]
    // 0x14a8dfc: StoreField: r1->field_1b = r4
    //     0x14a8dfc: stur            w4, [x1, #0x1b]
    // 0x14a8e00: r5 = Instance_VerticalDirection
    //     0x14a8e00: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a8e04: ldr             x5, [x5, #0xa20]
    // 0x14a8e08: StoreField: r1->field_23 = r5
    //     0x14a8e08: stur            w5, [x1, #0x23]
    // 0x14a8e0c: r6 = Instance_Clip
    //     0x14a8e0c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a8e10: ldr             x6, [x6, #0x38]
    // 0x14a8e14: StoreField: r1->field_2b = r6
    //     0x14a8e14: stur            w6, [x1, #0x2b]
    // 0x14a8e18: StoreField: r1->field_2f = rZR
    //     0x14a8e18: stur            xzr, [x1, #0x2f]
    // 0x14a8e1c: ldur            x7, [fp, #-0x20]
    // 0x14a8e20: StoreField: r1->field_b = r7
    //     0x14a8e20: stur            w7, [x1, #0xb]
    // 0x14a8e24: r0 = Padding()
    //     0x14a8e24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a8e28: mov             x1, x0
    // 0x14a8e2c: r0 = Instance_EdgeInsets
    //     0x14a8e2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14a8e30: ldr             x0, [x0, #0x1f0]
    // 0x14a8e34: stur            x1, [fp, #-0x20]
    // 0x14a8e38: StoreField: r1->field_f = r0
    //     0x14a8e38: stur            w0, [x1, #0xf]
    // 0x14a8e3c: ldur            x0, [fp, #-0x28]
    // 0x14a8e40: StoreField: r1->field_b = r0
    //     0x14a8e40: stur            w0, [x1, #0xb]
    // 0x14a8e44: r0 = Container()
    //     0x14a8e44: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a8e48: stur            x0, [fp, #-0x28]
    // 0x14a8e4c: ldur            x16, [fp, #-0x18]
    // 0x14a8e50: ldur            lr, [fp, #-0x20]
    // 0x14a8e54: stp             lr, x16, [SP]
    // 0x14a8e58: mov             x1, x0
    // 0x14a8e5c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14a8e5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14a8e60: ldr             x4, [x4, #0x88]
    // 0x14a8e64: r0 = Container()
    //     0x14a8e64: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a8e68: r0 = Padding()
    //     0x14a8e68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a8e6c: mov             x3, x0
    // 0x14a8e70: r0 = Instance_EdgeInsets
    //     0x14a8e70: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0x14a8e74: ldr             x0, [x0, #0xf30]
    // 0x14a8e78: stur            x3, [fp, #-0x18]
    // 0x14a8e7c: StoreField: r3->field_f = r0
    //     0x14a8e7c: stur            w0, [x3, #0xf]
    // 0x14a8e80: ldur            x0, [fp, #-0x28]
    // 0x14a8e84: StoreField: r3->field_b = r0
    //     0x14a8e84: stur            w0, [x3, #0xb]
    // 0x14a8e88: r1 = Null
    //     0x14a8e88: mov             x1, NULL
    // 0x14a8e8c: r2 = 4
    //     0x14a8e8c: movz            x2, #0x4
    // 0x14a8e90: r0 = AllocateArray()
    //     0x14a8e90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a8e94: stur            x0, [fp, #-0x20]
    // 0x14a8e98: r16 = "Purchased on: "
    //     0x14a8e98: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a80] "Purchased on: "
    //     0x14a8e9c: ldr             x16, [x16, #0xa80]
    // 0x14a8ea0: StoreField: r0->field_f = r16
    //     0x14a8ea0: stur            w16, [x0, #0xf]
    // 0x14a8ea4: ldur            x2, [fp, #-8]
    // 0x14a8ea8: LoadField: r1 = r2->field_f
    //     0x14a8ea8: ldur            w1, [x2, #0xf]
    // 0x14a8eac: DecompressPointer r1
    //     0x14a8eac: add             x1, x1, HEAP, lsl #32
    // 0x14a8eb0: r0 = controller()
    //     0x14a8eb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8eb4: LoadField: r1 = r0->field_4b
    //     0x14a8eb4: ldur            w1, [x0, #0x4b]
    // 0x14a8eb8: DecompressPointer r1
    //     0x14a8eb8: add             x1, x1, HEAP, lsl #32
    // 0x14a8ebc: r0 = value()
    //     0x14a8ebc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a8ec0: LoadField: r1 = r0->field_b
    //     0x14a8ec0: ldur            w1, [x0, #0xb]
    // 0x14a8ec4: DecompressPointer r1
    //     0x14a8ec4: add             x1, x1, HEAP, lsl #32
    // 0x14a8ec8: cmp             w1, NULL
    // 0x14a8ecc: b.ne            #0x14a8ed8
    // 0x14a8ed0: r0 = Null
    //     0x14a8ed0: mov             x0, NULL
    // 0x14a8ed4: b               #0x14a8ee0
    // 0x14a8ed8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14a8ed8: ldur            w0, [x1, #0x17]
    // 0x14a8edc: DecompressPointer r0
    //     0x14a8edc: add             x0, x0, HEAP, lsl #32
    // 0x14a8ee0: cmp             w0, NULL
    // 0x14a8ee4: b.ne            #0x14a8eec
    // 0x14a8ee8: r0 = ""
    //     0x14a8ee8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a8eec: ldur            x2, [fp, #-8]
    // 0x14a8ef0: ldur            x1, [fp, #-0x20]
    // 0x14a8ef4: ArrayStore: r1[1] = r0  ; List_4
    //     0x14a8ef4: add             x25, x1, #0x13
    //     0x14a8ef8: str             w0, [x25]
    //     0x14a8efc: tbz             w0, #0, #0x14a8f18
    //     0x14a8f00: ldurb           w16, [x1, #-1]
    //     0x14a8f04: ldurb           w17, [x0, #-1]
    //     0x14a8f08: and             x16, x17, x16, lsr #2
    //     0x14a8f0c: tst             x16, HEAP, lsr #32
    //     0x14a8f10: b.eq            #0x14a8f18
    //     0x14a8f14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14a8f18: ldur            x16, [fp, #-0x20]
    // 0x14a8f1c: str             x16, [SP]
    // 0x14a8f20: r0 = _interpolate()
    //     0x14a8f20: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14a8f24: ldur            x2, [fp, #-8]
    // 0x14a8f28: stur            x0, [fp, #-0x20]
    // 0x14a8f2c: LoadField: r1 = r2->field_13
    //     0x14a8f2c: ldur            w1, [x2, #0x13]
    // 0x14a8f30: DecompressPointer r1
    //     0x14a8f30: add             x1, x1, HEAP, lsl #32
    // 0x14a8f34: r0 = of()
    //     0x14a8f34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a8f38: LoadField: r1 = r0->field_87
    //     0x14a8f38: ldur            w1, [x0, #0x87]
    // 0x14a8f3c: DecompressPointer r1
    //     0x14a8f3c: add             x1, x1, HEAP, lsl #32
    // 0x14a8f40: LoadField: r0 = r1->field_2b
    //     0x14a8f40: ldur            w0, [x1, #0x2b]
    // 0x14a8f44: DecompressPointer r0
    //     0x14a8f44: add             x0, x0, HEAP, lsl #32
    // 0x14a8f48: stur            x0, [fp, #-0x28]
    // 0x14a8f4c: r1 = Instance_Color
    //     0x14a8f4c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a8f50: d0 = 0.400000
    //     0x14a8f50: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14a8f54: r0 = withOpacity()
    //     0x14a8f54: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a8f58: r16 = 12.000000
    //     0x14a8f58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a8f5c: ldr             x16, [x16, #0x9e8]
    // 0x14a8f60: stp             x0, x16, [SP]
    // 0x14a8f64: ldur            x1, [fp, #-0x28]
    // 0x14a8f68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a8f68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a8f6c: ldr             x4, [x4, #0xaa0]
    // 0x14a8f70: r0 = copyWith()
    //     0x14a8f70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a8f74: stur            x0, [fp, #-0x28]
    // 0x14a8f78: r0 = Text()
    //     0x14a8f78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a8f7c: mov             x1, x0
    // 0x14a8f80: ldur            x0, [fp, #-0x20]
    // 0x14a8f84: stur            x1, [fp, #-0x30]
    // 0x14a8f88: StoreField: r1->field_b = r0
    //     0x14a8f88: stur            w0, [x1, #0xb]
    // 0x14a8f8c: ldur            x0, [fp, #-0x28]
    // 0x14a8f90: StoreField: r1->field_13 = r0
    //     0x14a8f90: stur            w0, [x1, #0x13]
    // 0x14a8f94: r0 = Padding()
    //     0x14a8f94: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a8f98: mov             x2, x0
    // 0x14a8f9c: r0 = Instance_EdgeInsets
    //     0x14a8f9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x14a8fa0: ldr             x0, [x0, #0x668]
    // 0x14a8fa4: stur            x2, [fp, #-0x20]
    // 0x14a8fa8: StoreField: r2->field_f = r0
    //     0x14a8fa8: stur            w0, [x2, #0xf]
    // 0x14a8fac: ldur            x1, [fp, #-0x30]
    // 0x14a8fb0: StoreField: r2->field_b = r1
    //     0x14a8fb0: stur            w1, [x2, #0xb]
    // 0x14a8fb4: ldur            x3, [fp, #-8]
    // 0x14a8fb8: LoadField: r1 = r3->field_13
    //     0x14a8fb8: ldur            w1, [x3, #0x13]
    // 0x14a8fbc: DecompressPointer r1
    //     0x14a8fbc: add             x1, x1, HEAP, lsl #32
    // 0x14a8fc0: r0 = of()
    //     0x14a8fc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a8fc4: LoadField: r1 = r0->field_87
    //     0x14a8fc4: ldur            w1, [x0, #0x87]
    // 0x14a8fc8: DecompressPointer r1
    //     0x14a8fc8: add             x1, x1, HEAP, lsl #32
    // 0x14a8fcc: LoadField: r0 = r1->field_7
    //     0x14a8fcc: ldur            w0, [x1, #7]
    // 0x14a8fd0: DecompressPointer r0
    //     0x14a8fd0: add             x0, x0, HEAP, lsl #32
    // 0x14a8fd4: r16 = 16.000000
    //     0x14a8fd4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14a8fd8: ldr             x16, [x16, #0x188]
    // 0x14a8fdc: r30 = Instance_Color
    //     0x14a8fdc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a8fe0: stp             lr, x16, [SP]
    // 0x14a8fe4: mov             x1, x0
    // 0x14a8fe8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a8fe8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a8fec: ldr             x4, [x4, #0xaa0]
    // 0x14a8ff0: r0 = copyWith()
    //     0x14a8ff0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a8ff4: stur            x0, [fp, #-0x28]
    // 0x14a8ff8: r0 = Text()
    //     0x14a8ff8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a8ffc: mov             x2, x0
    // 0x14a9000: r0 = "Share photos & video :"
    //     0x14a9000: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a88] "Share photos & video :"
    //     0x14a9004: ldr             x0, [x0, #0xa88]
    // 0x14a9008: stur            x2, [fp, #-0x30]
    // 0x14a900c: StoreField: r2->field_b = r0
    //     0x14a900c: stur            w0, [x2, #0xb]
    // 0x14a9010: ldur            x0, [fp, #-0x28]
    // 0x14a9014: StoreField: r2->field_13 = r0
    //     0x14a9014: stur            w0, [x2, #0x13]
    // 0x14a9018: ldur            x0, [fp, #-8]
    // 0x14a901c: LoadField: r1 = r0->field_13
    //     0x14a901c: ldur            w1, [x0, #0x13]
    // 0x14a9020: DecompressPointer r1
    //     0x14a9020: add             x1, x1, HEAP, lsl #32
    // 0x14a9024: r0 = of()
    //     0x14a9024: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a9028: LoadField: r1 = r0->field_87
    //     0x14a9028: ldur            w1, [x0, #0x87]
    // 0x14a902c: DecompressPointer r1
    //     0x14a902c: add             x1, x1, HEAP, lsl #32
    // 0x14a9030: LoadField: r0 = r1->field_2b
    //     0x14a9030: ldur            w0, [x1, #0x2b]
    // 0x14a9034: DecompressPointer r0
    //     0x14a9034: add             x0, x0, HEAP, lsl #32
    // 0x14a9038: stur            x0, [fp, #-0x28]
    // 0x14a903c: r1 = Instance_Color
    //     0x14a903c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a9040: d0 = 0.500000
    //     0x14a9040: fmov            d0, #0.50000000
    // 0x14a9044: r0 = withOpacity()
    //     0x14a9044: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a9048: r16 = 12.000000
    //     0x14a9048: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a904c: ldr             x16, [x16, #0x9e8]
    // 0x14a9050: stp             x0, x16, [SP]
    // 0x14a9054: ldur            x1, [fp, #-0x28]
    // 0x14a9058: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a9058: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a905c: ldr             x4, [x4, #0xaa0]
    // 0x14a9060: r0 = copyWith()
    //     0x14a9060: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a9064: stur            x0, [fp, #-0x28]
    // 0x14a9068: r0 = Text()
    //     0x14a9068: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a906c: mov             x1, x0
    // 0x14a9070: r0 = "Capture and add your product experience like Unboxing, Product Usage, etc. (max 100 MB)"
    //     0x14a9070: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a90] "Capture and add your product experience like Unboxing, Product Usage, etc. (max 100 MB)"
    //     0x14a9074: ldr             x0, [x0, #0xa90]
    // 0x14a9078: stur            x1, [fp, #-0x38]
    // 0x14a907c: StoreField: r1->field_b = r0
    //     0x14a907c: stur            w0, [x1, #0xb]
    // 0x14a9080: ldur            x0, [fp, #-0x28]
    // 0x14a9084: StoreField: r1->field_13 = r0
    //     0x14a9084: stur            w0, [x1, #0x13]
    // 0x14a9088: r0 = Padding()
    //     0x14a9088: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a908c: mov             x3, x0
    // 0x14a9090: r0 = Instance_EdgeInsets
    //     0x14a9090: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x14a9094: ldr             x0, [x0, #0x770]
    // 0x14a9098: stur            x3, [fp, #-0x28]
    // 0x14a909c: StoreField: r3->field_f = r0
    //     0x14a909c: stur            w0, [x3, #0xf]
    // 0x14a90a0: ldur            x0, [fp, #-0x38]
    // 0x14a90a4: StoreField: r3->field_b = r0
    //     0x14a90a4: stur            w0, [x3, #0xb]
    // 0x14a90a8: r1 = Null
    //     0x14a90a8: mov             x1, NULL
    // 0x14a90ac: r2 = 4
    //     0x14a90ac: movz            x2, #0x4
    // 0x14a90b0: r0 = AllocateArray()
    //     0x14a90b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a90b4: mov             x2, x0
    // 0x14a90b8: ldur            x0, [fp, #-0x30]
    // 0x14a90bc: stur            x2, [fp, #-0x38]
    // 0x14a90c0: StoreField: r2->field_f = r0
    //     0x14a90c0: stur            w0, [x2, #0xf]
    // 0x14a90c4: ldur            x0, [fp, #-0x28]
    // 0x14a90c8: StoreField: r2->field_13 = r0
    //     0x14a90c8: stur            w0, [x2, #0x13]
    // 0x14a90cc: r1 = <Widget>
    //     0x14a90cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a90d0: r0 = AllocateGrowableArray()
    //     0x14a90d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a90d4: mov             x1, x0
    // 0x14a90d8: ldur            x0, [fp, #-0x38]
    // 0x14a90dc: stur            x1, [fp, #-0x28]
    // 0x14a90e0: StoreField: r1->field_f = r0
    //     0x14a90e0: stur            w0, [x1, #0xf]
    // 0x14a90e4: r2 = 4
    //     0x14a90e4: movz            x2, #0x4
    // 0x14a90e8: StoreField: r1->field_b = r2
    //     0x14a90e8: stur            w2, [x1, #0xb]
    // 0x14a90ec: r0 = Column()
    //     0x14a90ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14a90f0: mov             x1, x0
    // 0x14a90f4: r0 = Instance_Axis
    //     0x14a90f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a90f8: stur            x1, [fp, #-0x30]
    // 0x14a90fc: StoreField: r1->field_f = r0
    //     0x14a90fc: stur            w0, [x1, #0xf]
    // 0x14a9100: r2 = Instance_MainAxisAlignment
    //     0x14a9100: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a9104: ldr             x2, [x2, #0xa08]
    // 0x14a9108: StoreField: r1->field_13 = r2
    //     0x14a9108: stur            w2, [x1, #0x13]
    // 0x14a910c: r3 = Instance_MainAxisSize
    //     0x14a910c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a9110: ldr             x3, [x3, #0xa10]
    // 0x14a9114: ArrayStore: r1[0] = r3  ; List_4
    //     0x14a9114: stur            w3, [x1, #0x17]
    // 0x14a9118: r4 = Instance_CrossAxisAlignment
    //     0x14a9118: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14a911c: ldr             x4, [x4, #0x890]
    // 0x14a9120: StoreField: r1->field_1b = r4
    //     0x14a9120: stur            w4, [x1, #0x1b]
    // 0x14a9124: r5 = Instance_VerticalDirection
    //     0x14a9124: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a9128: ldr             x5, [x5, #0xa20]
    // 0x14a912c: StoreField: r1->field_23 = r5
    //     0x14a912c: stur            w5, [x1, #0x23]
    // 0x14a9130: r6 = Instance_Clip
    //     0x14a9130: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a9134: ldr             x6, [x6, #0x38]
    // 0x14a9138: StoreField: r1->field_2b = r6
    //     0x14a9138: stur            w6, [x1, #0x2b]
    // 0x14a913c: StoreField: r1->field_2f = rZR
    //     0x14a913c: stur            xzr, [x1, #0x2f]
    // 0x14a9140: ldur            x7, [fp, #-0x28]
    // 0x14a9144: StoreField: r1->field_b = r7
    //     0x14a9144: stur            w7, [x1, #0xb]
    // 0x14a9148: r0 = Padding()
    //     0x14a9148: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a914c: mov             x2, x0
    // 0x14a9150: r0 = Instance_EdgeInsets
    //     0x14a9150: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0x14a9154: ldr             x0, [x0, #0xa98]
    // 0x14a9158: stur            x2, [fp, #-0x28]
    // 0x14a915c: StoreField: r2->field_f = r0
    //     0x14a915c: stur            w0, [x2, #0xf]
    // 0x14a9160: ldur            x1, [fp, #-0x30]
    // 0x14a9164: StoreField: r2->field_b = r1
    //     0x14a9164: stur            w1, [x2, #0xb]
    // 0x14a9168: ldur            x3, [fp, #-8]
    // 0x14a916c: LoadField: r1 = r3->field_f
    //     0x14a916c: ldur            w1, [x3, #0xf]
    // 0x14a9170: DecompressPointer r1
    //     0x14a9170: add             x1, x1, HEAP, lsl #32
    // 0x14a9174: r0 = controller()
    //     0x14a9174: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a9178: LoadField: r1 = r0->field_97
    //     0x14a9178: ldur            w1, [x0, #0x97]
    // 0x14a917c: DecompressPointer r1
    //     0x14a917c: add             x1, x1, HEAP, lsl #32
    // 0x14a9180: r0 = value()
    //     0x14a9180: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14a9184: r1 = LoadClassIdInstr(r0)
    //     0x14a9184: ldur            x1, [x0, #-1]
    //     0x14a9188: ubfx            x1, x1, #0xc, #0x14
    // 0x14a918c: str             x0, [SP]
    // 0x14a9190: mov             x0, x1
    // 0x14a9194: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14a9194: movz            x17, #0xc898
    //     0x14a9198: add             lr, x0, x17
    //     0x14a919c: ldr             lr, [x21, lr, lsl #3]
    //     0x14a91a0: blr             lr
    // 0x14a91a4: cbnz            w0, #0x14a9440
    // 0x14a91a8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14a91a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14a91ac: ldr             x0, [x0, #0x1c80]
    //     0x14a91b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14a91b4: cmp             w0, w16
    //     0x14a91b8: b.ne            #0x14a91c4
    //     0x14a91bc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14a91c0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14a91c4: r0 = GetNavigation.size()
    //     0x14a91c4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14a91c8: LoadField: d0 = r0->field_7
    //     0x14a91c8: ldur            d0, [x0, #7]
    // 0x14a91cc: stur            d0, [fp, #-0x80]
    // 0x14a91d0: r0 = Radius()
    //     0x14a91d0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14a91d4: d0 = 20.000000
    //     0x14a91d4: fmov            d0, #20.00000000
    // 0x14a91d8: stur            x0, [fp, #-0x30]
    // 0x14a91dc: StoreField: r0->field_7 = d0
    //     0x14a91dc: stur            d0, [x0, #7]
    // 0x14a91e0: StoreField: r0->field_f = d0
    //     0x14a91e0: stur            d0, [x0, #0xf]
    // 0x14a91e4: r0 = BorderRadius()
    //     0x14a91e4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14a91e8: mov             x1, x0
    // 0x14a91ec: ldur            x0, [fp, #-0x30]
    // 0x14a91f0: stur            x1, [fp, #-0x38]
    // 0x14a91f4: StoreField: r1->field_7 = r0
    //     0x14a91f4: stur            w0, [x1, #7]
    // 0x14a91f8: StoreField: r1->field_b = r0
    //     0x14a91f8: stur            w0, [x1, #0xb]
    // 0x14a91fc: StoreField: r1->field_f = r0
    //     0x14a91fc: stur            w0, [x1, #0xf]
    // 0x14a9200: StoreField: r1->field_13 = r0
    //     0x14a9200: stur            w0, [x1, #0x13]
    // 0x14a9204: r0 = BoxDecoration()
    //     0x14a9204: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14a9208: mov             x2, x0
    // 0x14a920c: r0 = Instance_Color
    //     0x14a920c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a9210: stur            x2, [fp, #-0x30]
    // 0x14a9214: StoreField: r2->field_7 = r0
    //     0x14a9214: stur            w0, [x2, #7]
    // 0x14a9218: ldur            x1, [fp, #-0x38]
    // 0x14a921c: StoreField: r2->field_13 = r1
    //     0x14a921c: stur            w1, [x2, #0x13]
    // 0x14a9220: r3 = Instance_BoxShape
    //     0x14a9220: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a9224: ldr             x3, [x3, #0x80]
    // 0x14a9228: StoreField: r2->field_23 = r3
    //     0x14a9228: stur            w3, [x2, #0x23]
    // 0x14a922c: r1 = Instance_MaterialColor
    //     0x14a922c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x14a9230: ldr             x1, [x1, #0xdc0]
    // 0x14a9234: d0 = 0.300000
    //     0x14a9234: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14a9238: ldr             d0, [x17, #0x658]
    // 0x14a923c: r0 = withOpacity()
    //     0x14a923c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a9240: r1 = Instance_Color
    //     0x14a9240: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a9244: d0 = 0.200000
    //     0x14a9244: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0x14a9248: stur            x0, [fp, #-0x38]
    // 0x14a924c: r0 = withOpacity()
    //     0x14a924c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a9250: stur            x0, [fp, #-0x40]
    // 0x14a9254: r0 = Icon()
    //     0x14a9254: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14a9258: mov             x3, x0
    // 0x14a925c: r0 = Instance_IconData
    //     0x14a925c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36aa0] Obj!IconData@d555c1
    //     0x14a9260: ldr             x0, [x0, #0xaa0]
    // 0x14a9264: stur            x3, [fp, #-0x48]
    // 0x14a9268: StoreField: r3->field_b = r0
    //     0x14a9268: stur            w0, [x3, #0xb]
    // 0x14a926c: r0 = 25.000000
    //     0x14a926c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x14a9270: ldr             x0, [x0, #0x98]
    // 0x14a9274: StoreField: r3->field_f = r0
    //     0x14a9274: stur            w0, [x3, #0xf]
    // 0x14a9278: ldur            x0, [fp, #-0x40]
    // 0x14a927c: StoreField: r3->field_23 = r0
    //     0x14a927c: stur            w0, [x3, #0x23]
    // 0x14a9280: r1 = Null
    //     0x14a9280: mov             x1, NULL
    // 0x14a9284: r2 = 4
    //     0x14a9284: movz            x2, #0x4
    // 0x14a9288: r0 = AllocateArray()
    //     0x14a9288: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a928c: mov             x2, x0
    // 0x14a9290: ldur            x0, [fp, #-0x48]
    // 0x14a9294: stur            x2, [fp, #-0x40]
    // 0x14a9298: StoreField: r2->field_f = r0
    //     0x14a9298: stur            w0, [x2, #0xf]
    // 0x14a929c: r16 = Instance_Icon
    //     0x14a929c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36aa8] Obj!Icon@d66331
    //     0x14a92a0: ldr             x16, [x16, #0xaa8]
    // 0x14a92a4: StoreField: r2->field_13 = r16
    //     0x14a92a4: stur            w16, [x2, #0x13]
    // 0x14a92a8: r1 = <Widget>
    //     0x14a92a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a92ac: r0 = AllocateGrowableArray()
    //     0x14a92ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a92b0: mov             x1, x0
    // 0x14a92b4: ldur            x0, [fp, #-0x40]
    // 0x14a92b8: stur            x1, [fp, #-0x48]
    // 0x14a92bc: StoreField: r1->field_f = r0
    //     0x14a92bc: stur            w0, [x1, #0xf]
    // 0x14a92c0: r0 = 4
    //     0x14a92c0: movz            x0, #0x4
    // 0x14a92c4: StoreField: r1->field_b = r0
    //     0x14a92c4: stur            w0, [x1, #0xb]
    // 0x14a92c8: r0 = Stack()
    //     0x14a92c8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14a92cc: mov             x1, x0
    // 0x14a92d0: r0 = Instance_Alignment
    //     0x14a92d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x14a92d4: ldr             x0, [x0, #0xa28]
    // 0x14a92d8: stur            x1, [fp, #-0x40]
    // 0x14a92dc: StoreField: r1->field_f = r0
    //     0x14a92dc: stur            w0, [x1, #0xf]
    // 0x14a92e0: r0 = Instance_StackFit
    //     0x14a92e0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14a92e4: ldr             x0, [x0, #0xfa8]
    // 0x14a92e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14a92e8: stur            w0, [x1, #0x17]
    // 0x14a92ec: r0 = Instance_Clip
    //     0x14a92ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14a92f0: ldr             x0, [x0, #0x7e0]
    // 0x14a92f4: StoreField: r1->field_1b = r0
    //     0x14a92f4: stur            w0, [x1, #0x1b]
    // 0x14a92f8: ldur            x2, [fp, #-0x48]
    // 0x14a92fc: StoreField: r1->field_b = r2
    //     0x14a92fc: stur            w2, [x1, #0xb]
    // 0x14a9300: r0 = Center()
    //     0x14a9300: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14a9304: mov             x1, x0
    // 0x14a9308: r0 = Instance_Alignment
    //     0x14a9308: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14a930c: ldr             x0, [x0, #0xb10]
    // 0x14a9310: stur            x1, [fp, #-0x48]
    // 0x14a9314: StoreField: r1->field_f = r0
    //     0x14a9314: stur            w0, [x1, #0xf]
    // 0x14a9318: ldur            x0, [fp, #-0x40]
    // 0x14a931c: StoreField: r1->field_b = r0
    //     0x14a931c: stur            w0, [x1, #0xb]
    // 0x14a9320: r0 = DottedBorder()
    //     0x14a9320: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0x14a9324: stur            x0, [fp, #-0x40]
    // 0x14a9328: r16 = Instance_BorderType
    //     0x14a9328: add             x16, PP, #0x40, lsl #12  ; [pp+0x40078] Obj!BorderType@d750a1
    //     0x14a932c: ldr             x16, [x16, #0x78]
    // 0x14a9330: r30 = Instance_Radius
    //     0x14a9330: add             lr, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0x14a9334: ldr             lr, [lr, #0x758]
    // 0x14a9338: stp             lr, x16, [SP]
    // 0x14a933c: mov             x1, x0
    // 0x14a9340: ldur            x2, [fp, #-0x48]
    // 0x14a9344: ldur            x3, [fp, #-0x38]
    // 0x14a9348: r5 = const [5.0, 5.0]
    //     0x14a9348: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0x14a934c: ldr             x5, [x5, #0xab0]
    // 0x14a9350: d0 = 1.000000
    //     0x14a9350: fmov            d0, #1.00000000
    // 0x14a9354: r4 = const [0, 0x7, 0x2, 0x5, borderType, 0x5, radius, 0x6, null]
    //     0x14a9354: add             x4, PP, #0x40, lsl #12  ; [pp+0x40088] List(9) [0, 0x7, 0x2, 0x5, "borderType", 0x5, "radius", 0x6, Null]
    //     0x14a9358: ldr             x4, [x4, #0x88]
    // 0x14a935c: r0 = DottedBorder()
    //     0x14a935c: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0x14a9360: ldur            d0, [fp, #-0x80]
    // 0x14a9364: r0 = inline_Allocate_Double()
    //     0x14a9364: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x14a9368: add             x0, x0, #0x10
    //     0x14a936c: cmp             x1, x0
    //     0x14a9370: b.ls            #0x14a9dbc
    //     0x14a9374: str             x0, [THR, #0x50]  ; THR::top
    //     0x14a9378: sub             x0, x0, #0xf
    //     0x14a937c: movz            x1, #0xe15c
    //     0x14a9380: movk            x1, #0x3, lsl #16
    //     0x14a9384: stur            x1, [x0, #-1]
    // 0x14a9388: StoreField: r0->field_7 = d0
    //     0x14a9388: stur            d0, [x0, #7]
    // 0x14a938c: stur            x0, [fp, #-0x38]
    // 0x14a9390: r0 = Container()
    //     0x14a9390: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a9394: stur            x0, [fp, #-0x48]
    // 0x14a9398: ldur            x16, [fp, #-0x38]
    // 0x14a939c: r30 = 60.000000
    //     0x14a939c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14a93a0: ldr             lr, [lr, #0x110]
    // 0x14a93a4: stp             lr, x16, [SP, #0x10]
    // 0x14a93a8: ldur            x16, [fp, #-0x30]
    // 0x14a93ac: ldur            lr, [fp, #-0x40]
    // 0x14a93b0: stp             lr, x16, [SP]
    // 0x14a93b4: mov             x1, x0
    // 0x14a93b8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14a93b8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14a93bc: ldr             x4, [x4, #0x870]
    // 0x14a93c0: r0 = Container()
    //     0x14a93c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a93c4: r0 = Padding()
    //     0x14a93c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a93c8: mov             x1, x0
    // 0x14a93cc: r0 = Instance_EdgeInsets
    //     0x14a93cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x14a93d0: ldr             x0, [x0, #0xdf8]
    // 0x14a93d4: stur            x1, [fp, #-0x30]
    // 0x14a93d8: StoreField: r1->field_f = r0
    //     0x14a93d8: stur            w0, [x1, #0xf]
    // 0x14a93dc: ldur            x0, [fp, #-0x48]
    // 0x14a93e0: StoreField: r1->field_b = r0
    //     0x14a93e0: stur            w0, [x1, #0xb]
    // 0x14a93e4: r0 = InkWell()
    //     0x14a93e4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14a93e8: mov             x3, x0
    // 0x14a93ec: ldur            x0, [fp, #-0x30]
    // 0x14a93f0: stur            x3, [fp, #-0x38]
    // 0x14a93f4: StoreField: r3->field_b = r0
    //     0x14a93f4: stur            w0, [x3, #0xb]
    // 0x14a93f8: ldur            x2, [fp, #-8]
    // 0x14a93fc: r1 = Function '<anonymous closure>':.
    //     0x14a93fc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43030] AnonymousClosure: (0x140dbb0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x14a9400: ldr             x1, [x1, #0x30]
    // 0x14a9404: r0 = AllocateClosure()
    //     0x14a9404: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a9408: mov             x1, x0
    // 0x14a940c: ldur            x0, [fp, #-0x38]
    // 0x14a9410: StoreField: r0->field_f = r1
    //     0x14a9410: stur            w1, [x0, #0xf]
    // 0x14a9414: r1 = true
    //     0x14a9414: add             x1, NULL, #0x20  ; true
    // 0x14a9418: StoreField: r0->field_43 = r1
    //     0x14a9418: stur            w1, [x0, #0x43]
    // 0x14a941c: r2 = Instance_BoxShape
    //     0x14a941c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a9420: ldr             x2, [x2, #0x80]
    // 0x14a9424: StoreField: r0->field_47 = r2
    //     0x14a9424: stur            w2, [x0, #0x47]
    // 0x14a9428: StoreField: r0->field_6f = r1
    //     0x14a9428: stur            w1, [x0, #0x6f]
    // 0x14a942c: r3 = false
    //     0x14a942c: add             x3, NULL, #0x30  ; false
    // 0x14a9430: StoreField: r0->field_73 = r3
    //     0x14a9430: stur            w3, [x0, #0x73]
    // 0x14a9434: StoreField: r0->field_83 = r1
    //     0x14a9434: stur            w1, [x0, #0x83]
    // 0x14a9438: StoreField: r0->field_7b = r3
    //     0x14a9438: stur            w3, [x0, #0x7b]
    // 0x14a943c: b               #0x14a95ac
    // 0x14a9440: ldur            x0, [fp, #-8]
    // 0x14a9444: r3 = false
    //     0x14a9444: add             x3, NULL, #0x30  ; false
    // 0x14a9448: r2 = Instance_BoxShape
    //     0x14a9448: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a944c: ldr             x2, [x2, #0x80]
    // 0x14a9450: LoadField: r1 = r0->field_f
    //     0x14a9450: ldur            w1, [x0, #0xf]
    // 0x14a9454: DecompressPointer r1
    //     0x14a9454: add             x1, x1, HEAP, lsl #32
    // 0x14a9458: r0 = controller()
    //     0x14a9458: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a945c: LoadField: r1 = r0->field_97
    //     0x14a945c: ldur            w1, [x0, #0x97]
    // 0x14a9460: DecompressPointer r1
    //     0x14a9460: add             x1, x1, HEAP, lsl #32
    // 0x14a9464: r0 = value()
    //     0x14a9464: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14a9468: r1 = LoadClassIdInstr(r0)
    //     0x14a9468: ldur            x1, [x0, #-1]
    //     0x14a946c: ubfx            x1, x1, #0xc, #0x14
    // 0x14a9470: str             x0, [SP]
    // 0x14a9474: mov             x0, x1
    // 0x14a9478: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14a9478: movz            x17, #0xc898
    //     0x14a947c: add             lr, x0, x17
    //     0x14a9480: ldr             lr, [x21, lr, lsl #3]
    //     0x14a9484: blr             lr
    // 0x14a9488: r1 = LoadInt32Instr(r0)
    //     0x14a9488: sbfx            x1, x0, #1, #0x1f
    //     0x14a948c: tbz             w0, #0, #0x14a9494
    //     0x14a9490: ldur            x1, [x0, #7]
    // 0x14a9494: cmp             x1, #4
    // 0x14a9498: b.ge            #0x14a94f0
    // 0x14a949c: ldur            x2, [fp, #-8]
    // 0x14a94a0: LoadField: r1 = r2->field_f
    //     0x14a94a0: ldur            w1, [x2, #0xf]
    // 0x14a94a4: DecompressPointer r1
    //     0x14a94a4: add             x1, x1, HEAP, lsl #32
    // 0x14a94a8: r0 = controller()
    //     0x14a94a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a94ac: LoadField: r1 = r0->field_97
    //     0x14a94ac: ldur            w1, [x0, #0x97]
    // 0x14a94b0: DecompressPointer r1
    //     0x14a94b0: add             x1, x1, HEAP, lsl #32
    // 0x14a94b4: r0 = value()
    //     0x14a94b4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14a94b8: r1 = LoadClassIdInstr(r0)
    //     0x14a94b8: ldur            x1, [x0, #-1]
    //     0x14a94bc: ubfx            x1, x1, #0xc, #0x14
    // 0x14a94c0: str             x0, [SP]
    // 0x14a94c4: mov             x0, x1
    // 0x14a94c8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14a94c8: movz            x17, #0xc898
    //     0x14a94cc: add             lr, x0, x17
    //     0x14a94d0: ldr             lr, [x21, lr, lsl #3]
    //     0x14a94d4: blr             lr
    // 0x14a94d8: r1 = LoadInt32Instr(r0)
    //     0x14a94d8: sbfx            x1, x0, #1, #0x1f
    //     0x14a94dc: tbz             w0, #0, #0x14a94e4
    //     0x14a94e0: ldur            x1, [x0, #7]
    // 0x14a94e4: add             x0, x1, #1
    // 0x14a94e8: mov             x3, x0
    // 0x14a94ec: b               #0x14a953c
    // 0x14a94f0: ldur            x2, [fp, #-8]
    // 0x14a94f4: LoadField: r1 = r2->field_f
    //     0x14a94f4: ldur            w1, [x2, #0xf]
    // 0x14a94f8: DecompressPointer r1
    //     0x14a94f8: add             x1, x1, HEAP, lsl #32
    // 0x14a94fc: r0 = controller()
    //     0x14a94fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a9500: LoadField: r1 = r0->field_97
    //     0x14a9500: ldur            w1, [x0, #0x97]
    // 0x14a9504: DecompressPointer r1
    //     0x14a9504: add             x1, x1, HEAP, lsl #32
    // 0x14a9508: r0 = value()
    //     0x14a9508: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14a950c: r1 = LoadClassIdInstr(r0)
    //     0x14a950c: ldur            x1, [x0, #-1]
    //     0x14a9510: ubfx            x1, x1, #0xc, #0x14
    // 0x14a9514: str             x0, [SP]
    // 0x14a9518: mov             x0, x1
    // 0x14a951c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14a951c: movz            x17, #0xc898
    //     0x14a9520: add             lr, x0, x17
    //     0x14a9524: ldr             lr, [x21, lr, lsl #3]
    //     0x14a9528: blr             lr
    // 0x14a952c: r1 = LoadInt32Instr(r0)
    //     0x14a952c: sbfx            x1, x0, #1, #0x1f
    //     0x14a9530: tbz             w0, #0, #0x14a9538
    //     0x14a9534: ldur            x1, [x0, #7]
    // 0x14a9538: mov             x3, x1
    // 0x14a953c: stur            x3, [fp, #-0x50]
    // 0x14a9540: r1 = Function '<anonymous closure>':.
    //     0x14a9540: add             x1, PP, #0x43, lsl #12  ; [pp+0x43038] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14a9544: ldr             x1, [x1, #0x38]
    // 0x14a9548: r2 = Null
    //     0x14a9548: mov             x2, NULL
    // 0x14a954c: r0 = AllocateClosure()
    //     0x14a954c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a9550: ldur            x2, [fp, #-8]
    // 0x14a9554: r1 = Function '<anonymous closure>':.
    //     0x14a9554: add             x1, PP, #0x43, lsl #12  ; [pp+0x43040] AnonymousClosure: (0x14a9dcc), in [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14a8868)
    //     0x14a9558: ldr             x1, [x1, #0x40]
    // 0x14a955c: stur            x0, [fp, #-0x30]
    // 0x14a9560: r0 = AllocateClosure()
    //     0x14a9560: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a9564: stur            x0, [fp, #-0x38]
    // 0x14a9568: r0 = ListView()
    //     0x14a9568: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14a956c: stur            x0, [fp, #-0x40]
    // 0x14a9570: r16 = Instance_BouncingScrollPhysics
    //     0x14a9570: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x14a9574: ldr             x16, [x16, #0x890]
    // 0x14a9578: r30 = true
    //     0x14a9578: add             lr, NULL, #0x20  ; true
    // 0x14a957c: stp             lr, x16, [SP, #0x10]
    // 0x14a9580: r16 = true
    //     0x14a9580: add             x16, NULL, #0x20  ; true
    // 0x14a9584: r30 = Instance_Axis
    //     0x14a9584: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a9588: stp             lr, x16, [SP]
    // 0x14a958c: mov             x1, x0
    // 0x14a9590: ldur            x2, [fp, #-0x38]
    // 0x14a9594: ldur            x3, [fp, #-0x50]
    // 0x14a9598: ldur            x5, [fp, #-0x30]
    // 0x14a959c: r4 = const [0, 0x8, 0x4, 0x4, physics, 0x4, primary, 0x6, scrollDirection, 0x7, shrinkWrap, 0x5, null]
    //     0x14a959c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36ad0] List(13) [0, 0x8, 0x4, 0x4, "physics", 0x4, "primary", 0x6, "scrollDirection", 0x7, "shrinkWrap", 0x5, Null]
    //     0x14a95a0: ldr             x4, [x4, #0xad0]
    // 0x14a95a4: r0 = ListView.separated()
    //     0x14a95a4: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14a95a8: ldur            x0, [fp, #-0x40]
    // 0x14a95ac: ldur            x2, [fp, #-8]
    // 0x14a95b0: stur            x0, [fp, #-0x30]
    // 0x14a95b4: r0 = SizedBox()
    //     0x14a95b4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14a95b8: mov             x1, x0
    // 0x14a95bc: r0 = 60.000000
    //     0x14a95bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14a95c0: ldr             x0, [x0, #0x110]
    // 0x14a95c4: stur            x1, [fp, #-0x38]
    // 0x14a95c8: StoreField: r1->field_13 = r0
    //     0x14a95c8: stur            w0, [x1, #0x13]
    // 0x14a95cc: ldur            x0, [fp, #-0x30]
    // 0x14a95d0: StoreField: r1->field_b = r0
    //     0x14a95d0: stur            w0, [x1, #0xb]
    // 0x14a95d4: r0 = Padding()
    //     0x14a95d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a95d8: mov             x2, x0
    // 0x14a95dc: r0 = Instance_EdgeInsets
    //     0x14a95dc: add             x0, PP, #0x34, lsl #12  ; [pp+0x340b8] Obj!EdgeInsets@d57981
    //     0x14a95e0: ldr             x0, [x0, #0xb8]
    // 0x14a95e4: stur            x2, [fp, #-0x30]
    // 0x14a95e8: StoreField: r2->field_f = r0
    //     0x14a95e8: stur            w0, [x2, #0xf]
    // 0x14a95ec: ldur            x0, [fp, #-0x38]
    // 0x14a95f0: StoreField: r2->field_b = r0
    //     0x14a95f0: stur            w0, [x2, #0xb]
    // 0x14a95f4: ldur            x0, [fp, #-8]
    // 0x14a95f8: LoadField: r1 = r0->field_13
    //     0x14a95f8: ldur            w1, [x0, #0x13]
    // 0x14a95fc: DecompressPointer r1
    //     0x14a95fc: add             x1, x1, HEAP, lsl #32
    // 0x14a9600: r0 = of()
    //     0x14a9600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a9604: LoadField: r1 = r0->field_87
    //     0x14a9604: ldur            w1, [x0, #0x87]
    // 0x14a9608: DecompressPointer r1
    //     0x14a9608: add             x1, x1, HEAP, lsl #32
    // 0x14a960c: LoadField: r0 = r1->field_7
    //     0x14a960c: ldur            w0, [x1, #7]
    // 0x14a9610: DecompressPointer r0
    //     0x14a9610: add             x0, x0, HEAP, lsl #32
    // 0x14a9614: r16 = 16.000000
    //     0x14a9614: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14a9618: ldr             x16, [x16, #0x188]
    // 0x14a961c: r30 = Instance_Color
    //     0x14a961c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a9620: stp             lr, x16, [SP]
    // 0x14a9624: mov             x1, x0
    // 0x14a9628: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a9628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a962c: ldr             x4, [x4, #0xaa0]
    // 0x14a9630: r0 = copyWith()
    //     0x14a9630: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a9634: stur            x0, [fp, #-0x38]
    // 0x14a9638: r0 = Text()
    //     0x14a9638: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a963c: mov             x1, x0
    // 0x14a9640: r0 = "Write a review :"
    //     0x14a9640: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ad8] "Write a review :"
    //     0x14a9644: ldr             x0, [x0, #0xad8]
    // 0x14a9648: stur            x1, [fp, #-0x40]
    // 0x14a964c: StoreField: r1->field_b = r0
    //     0x14a964c: stur            w0, [x1, #0xb]
    // 0x14a9650: ldur            x0, [fp, #-0x38]
    // 0x14a9654: StoreField: r1->field_13 = r0
    //     0x14a9654: stur            w0, [x1, #0x13]
    // 0x14a9658: r0 = Padding()
    //     0x14a9658: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a965c: mov             x2, x0
    // 0x14a9660: r0 = Instance_EdgeInsets
    //     0x14a9660: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x14a9664: ldr             x0, [x0, #0x668]
    // 0x14a9668: stur            x2, [fp, #-0x38]
    // 0x14a966c: StoreField: r2->field_f = r0
    //     0x14a966c: stur            w0, [x2, #0xf]
    // 0x14a9670: ldur            x0, [fp, #-0x40]
    // 0x14a9674: StoreField: r2->field_b = r0
    //     0x14a9674: stur            w0, [x2, #0xb]
    // 0x14a9678: ldur            x0, [fp, #-8]
    // 0x14a967c: LoadField: r1 = r0->field_f
    //     0x14a967c: ldur            w1, [x0, #0xf]
    // 0x14a9680: DecompressPointer r1
    //     0x14a9680: add             x1, x1, HEAP, lsl #32
    // 0x14a9684: r0 = controller()
    //     0x14a9684: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a9688: LoadField: r1 = r0->field_8b
    //     0x14a9688: ldur            w1, [x0, #0x8b]
    // 0x14a968c: DecompressPointer r1
    //     0x14a968c: add             x1, x1, HEAP, lsl #32
    // 0x14a9690: stur            x1, [fp, #-0x40]
    // 0x14a9694: r0 = LengthLimitingTextInputFormatter()
    //     0x14a9694: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14a9698: mov             x3, x0
    // 0x14a969c: r0 = 4000
    //     0x14a969c: movz            x0, #0xfa0
    // 0x14a96a0: stur            x3, [fp, #-0x48]
    // 0x14a96a4: StoreField: r3->field_7 = r0
    //     0x14a96a4: stur            w0, [x3, #7]
    // 0x14a96a8: r1 = Null
    //     0x14a96a8: mov             x1, NULL
    // 0x14a96ac: r2 = 2
    //     0x14a96ac: movz            x2, #0x2
    // 0x14a96b0: r0 = AllocateArray()
    //     0x14a96b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a96b4: mov             x2, x0
    // 0x14a96b8: ldur            x0, [fp, #-0x48]
    // 0x14a96bc: stur            x2, [fp, #-0x58]
    // 0x14a96c0: StoreField: r2->field_f = r0
    //     0x14a96c0: stur            w0, [x2, #0xf]
    // 0x14a96c4: r1 = <TextInputFormatter>
    //     0x14a96c4: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14a96c8: ldr             x1, [x1, #0x7b0]
    // 0x14a96cc: r0 = AllocateGrowableArray()
    //     0x14a96cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a96d0: mov             x2, x0
    // 0x14a96d4: ldur            x0, [fp, #-0x58]
    // 0x14a96d8: stur            x2, [fp, #-0x48]
    // 0x14a96dc: StoreField: r2->field_f = r0
    //     0x14a96dc: stur            w0, [x2, #0xf]
    // 0x14a96e0: r0 = 2
    //     0x14a96e0: movz            x0, #0x2
    // 0x14a96e4: StoreField: r2->field_b = r0
    //     0x14a96e4: stur            w0, [x2, #0xb]
    // 0x14a96e8: ldur            x0, [fp, #-8]
    // 0x14a96ec: LoadField: r1 = r0->field_13
    //     0x14a96ec: ldur            w1, [x0, #0x13]
    // 0x14a96f0: DecompressPointer r1
    //     0x14a96f0: add             x1, x1, HEAP, lsl #32
    // 0x14a96f4: r0 = of()
    //     0x14a96f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a96f8: LoadField: r1 = r0->field_87
    //     0x14a96f8: ldur            w1, [x0, #0x87]
    // 0x14a96fc: DecompressPointer r1
    //     0x14a96fc: add             x1, x1, HEAP, lsl #32
    // 0x14a9700: LoadField: r0 = r1->field_2b
    //     0x14a9700: ldur            w0, [x1, #0x2b]
    // 0x14a9704: DecompressPointer r0
    //     0x14a9704: add             x0, x0, HEAP, lsl #32
    // 0x14a9708: ldur            x2, [fp, #-8]
    // 0x14a970c: stur            x0, [fp, #-0x58]
    // 0x14a9710: LoadField: r1 = r2->field_13
    //     0x14a9710: ldur            w1, [x2, #0x13]
    // 0x14a9714: DecompressPointer r1
    //     0x14a9714: add             x1, x1, HEAP, lsl #32
    // 0x14a9718: r0 = of()
    //     0x14a9718: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a971c: LoadField: r1 = r0->field_5b
    //     0x14a971c: ldur            w1, [x0, #0x5b]
    // 0x14a9720: DecompressPointer r1
    //     0x14a9720: add             x1, x1, HEAP, lsl #32
    // 0x14a9724: str             x1, [SP]
    // 0x14a9728: ldur            x1, [fp, #-0x58]
    // 0x14a972c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x14a972c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x14a9730: ldr             x4, [x4, #0xf40]
    // 0x14a9734: r0 = copyWith()
    //     0x14a9734: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a9738: ldur            x2, [fp, #-8]
    // 0x14a973c: stur            x0, [fp, #-0x58]
    // 0x14a9740: LoadField: r1 = r2->field_f
    //     0x14a9740: ldur            w1, [x2, #0xf]
    // 0x14a9744: DecompressPointer r1
    //     0x14a9744: add             x1, x1, HEAP, lsl #32
    // 0x14a9748: r0 = controller()
    //     0x14a9748: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a974c: LoadField: r2 = r0->field_8f
    //     0x14a974c: ldur            w2, [x0, #0x8f]
    // 0x14a9750: DecompressPointer r2
    //     0x14a9750: add             x2, x2, HEAP, lsl #32
    // 0x14a9754: ldur            x0, [fp, #-8]
    // 0x14a9758: stur            x2, [fp, #-0x60]
    // 0x14a975c: LoadField: r1 = r0->field_13
    //     0x14a975c: ldur            w1, [x0, #0x13]
    // 0x14a9760: DecompressPointer r1
    //     0x14a9760: add             x1, x1, HEAP, lsl #32
    // 0x14a9764: r0 = getTextFormFieldInputDecorationCircle()
    //     0x14a9764: bl              #0xac2dd8  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircle
    // 0x14a9768: ldur            x2, [fp, #-8]
    // 0x14a976c: stur            x0, [fp, #-0x68]
    // 0x14a9770: LoadField: r1 = r2->field_13
    //     0x14a9770: ldur            w1, [x2, #0x13]
    // 0x14a9774: DecompressPointer r1
    //     0x14a9774: add             x1, x1, HEAP, lsl #32
    // 0x14a9778: r0 = of()
    //     0x14a9778: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a977c: LoadField: r1 = r0->field_87
    //     0x14a977c: ldur            w1, [x0, #0x87]
    // 0x14a9780: DecompressPointer r1
    //     0x14a9780: add             x1, x1, HEAP, lsl #32
    // 0x14a9784: LoadField: r0 = r1->field_2b
    //     0x14a9784: ldur            w0, [x1, #0x2b]
    // 0x14a9788: DecompressPointer r0
    //     0x14a9788: add             x0, x0, HEAP, lsl #32
    // 0x14a978c: stur            x0, [fp, #-0x70]
    // 0x14a9790: r1 = Instance_Color
    //     0x14a9790: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a9794: d0 = 0.400000
    //     0x14a9794: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14a9798: r0 = withOpacity()
    //     0x14a9798: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a979c: r16 = 12.000000
    //     0x14a979c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a97a0: ldr             x16, [x16, #0x9e8]
    // 0x14a97a4: stp             x16, x0, [SP]
    // 0x14a97a8: ldur            x1, [fp, #-0x70]
    // 0x14a97ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14a97ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14a97b0: ldr             x4, [x4, #0x9b8]
    // 0x14a97b4: r0 = copyWith()
    //     0x14a97b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a97b8: ldur            x2, [fp, #-8]
    // 0x14a97bc: stur            x0, [fp, #-0x70]
    // 0x14a97c0: LoadField: r1 = r2->field_13
    //     0x14a97c0: ldur            w1, [x2, #0x13]
    // 0x14a97c4: DecompressPointer r1
    //     0x14a97c4: add             x1, x1, HEAP, lsl #32
    // 0x14a97c8: r0 = of()
    //     0x14a97c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a97cc: LoadField: r1 = r0->field_87
    //     0x14a97cc: ldur            w1, [x0, #0x87]
    // 0x14a97d0: DecompressPointer r1
    //     0x14a97d0: add             x1, x1, HEAP, lsl #32
    // 0x14a97d4: LoadField: r0 = r1->field_2b
    //     0x14a97d4: ldur            w0, [x1, #0x2b]
    // 0x14a97d8: DecompressPointer r0
    //     0x14a97d8: add             x0, x0, HEAP, lsl #32
    // 0x14a97dc: r16 = Instance_Color
    //     0x14a97dc: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x14a97e0: ldr             x16, [x16, #0x7c0]
    // 0x14a97e4: r30 = 12.000000
    //     0x14a97e4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a97e8: ldr             lr, [lr, #0x9e8]
    // 0x14a97ec: stp             lr, x16, [SP]
    // 0x14a97f0: mov             x1, x0
    // 0x14a97f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14a97f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14a97f8: ldr             x4, [x4, #0x9b8]
    // 0x14a97fc: r0 = copyWith()
    //     0x14a97fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a9800: r16 = "How you use the product. Things that are great about it. Things that aren’t great about it."
    //     0x14a9800: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ae0] "How you use the product. Things that are great about it. Things that aren’t great about it."
    //     0x14a9804: ldr             x16, [x16, #0xae0]
    // 0x14a9808: ldur            lr, [fp, #-0x70]
    // 0x14a980c: stp             lr, x16, [SP, #8]
    // 0x14a9810: str             x0, [SP]
    // 0x14a9814: ldur            x1, [fp, #-0x68]
    // 0x14a9818: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0x14a9818: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0x14a981c: ldr             x4, [x4, #0xfe8]
    // 0x14a9820: r0 = copyWith()
    //     0x14a9820: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x14a9824: ldur            x2, [fp, #-8]
    // 0x14a9828: r1 = Function '<anonymous closure>':.
    //     0x14a9828: add             x1, PP, #0x43, lsl #12  ; [pp+0x43048] AnonymousClosure: (0x14086c8), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14f0f10)
    //     0x14a982c: ldr             x1, [x1, #0x48]
    // 0x14a9830: stur            x0, [fp, #-0x68]
    // 0x14a9834: r0 = AllocateClosure()
    //     0x14a9834: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a9838: r1 = <String>
    //     0x14a9838: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14a983c: stur            x0, [fp, #-0x70]
    // 0x14a9840: r0 = TextFormField()
    //     0x14a9840: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x14a9844: stur            x0, [fp, #-0x78]
    // 0x14a9848: r16 = false
    //     0x14a9848: add             x16, NULL, #0x30  ; false
    // 0x14a984c: r30 = Instance_AutovalidateMode
    //     0x14a984c: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14a9850: ldr             lr, [lr, #0x7e8]
    // 0x14a9854: stp             lr, x16, [SP, #0x40]
    // 0x14a9858: ldur            x16, [fp, #-0x48]
    // 0x14a985c: r30 = Instance_TextInputType
    //     0x14a985c: add             lr, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x14a9860: ldr             lr, [lr, #0x7f0]
    // 0x14a9864: stp             lr, x16, [SP, #0x30]
    // 0x14a9868: r16 = Instance_TextInputAction
    //     0x14a9868: ldr             x16, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0x14a986c: r30 = 6
    //     0x14a986c: movz            lr, #0x6
    // 0x14a9870: stp             lr, x16, [SP, #0x20]
    // 0x14a9874: r16 = 10
    //     0x14a9874: movz            x16, #0xa
    // 0x14a9878: ldur            lr, [fp, #-0x58]
    // 0x14a987c: stp             lr, x16, [SP, #0x10]
    // 0x14a9880: ldur            x16, [fp, #-0x60]
    // 0x14a9884: ldur            lr, [fp, #-0x70]
    // 0x14a9888: stp             lr, x16, [SP]
    // 0x14a988c: mov             x1, x0
    // 0x14a9890: ldur            x2, [fp, #-0x68]
    // 0x14a9894: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x3, controller, 0xa, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, textInputAction, 0x6, null]
    //     0x14a9894: add             x4, PP, #0x36, lsl #12  ; [pp+0x36af0] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x3, "controller", 0xa, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "textInputAction", 0x6, Null]
    //     0x14a9898: ldr             x4, [x4, #0xaf0]
    // 0x14a989c: r0 = TextFormField()
    //     0x14a989c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14a98a0: r0 = Form()
    //     0x14a98a0: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14a98a4: mov             x1, x0
    // 0x14a98a8: ldur            x0, [fp, #-0x78]
    // 0x14a98ac: stur            x1, [fp, #-0x48]
    // 0x14a98b0: StoreField: r1->field_b = r0
    //     0x14a98b0: stur            w0, [x1, #0xb]
    // 0x14a98b4: r0 = Instance_AutovalidateMode
    //     0x14a98b4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14a98b8: ldr             x0, [x0, #0x800]
    // 0x14a98bc: StoreField: r1->field_23 = r0
    //     0x14a98bc: stur            w0, [x1, #0x23]
    // 0x14a98c0: ldur            x0, [fp, #-0x40]
    // 0x14a98c4: StoreField: r1->field_7 = r0
    //     0x14a98c4: stur            w0, [x1, #7]
    // 0x14a98c8: r0 = Padding()
    //     0x14a98c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a98cc: mov             x2, x0
    // 0x14a98d0: r0 = Instance_EdgeInsets
    //     0x14a98d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0x14a98d4: ldr             x0, [x0, #0xe18]
    // 0x14a98d8: stur            x2, [fp, #-0x40]
    // 0x14a98dc: StoreField: r2->field_f = r0
    //     0x14a98dc: stur            w0, [x2, #0xf]
    // 0x14a98e0: ldur            x0, [fp, #-0x48]
    // 0x14a98e4: StoreField: r2->field_b = r0
    //     0x14a98e4: stur            w0, [x2, #0xb]
    // 0x14a98e8: ldur            x0, [fp, #-8]
    // 0x14a98ec: LoadField: r1 = r0->field_f
    //     0x14a98ec: ldur            w1, [x0, #0xf]
    // 0x14a98f0: DecompressPointer r1
    //     0x14a98f0: add             x1, x1, HEAP, lsl #32
    // 0x14a98f4: r0 = controller()
    //     0x14a98f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a98f8: LoadField: r1 = r0->field_4b
    //     0x14a98f8: ldur            w1, [x0, #0x4b]
    // 0x14a98fc: DecompressPointer r1
    //     0x14a98fc: add             x1, x1, HEAP, lsl #32
    // 0x14a9900: r0 = value()
    //     0x14a9900: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a9904: LoadField: r1 = r0->field_b
    //     0x14a9904: ldur            w1, [x0, #0xb]
    // 0x14a9908: DecompressPointer r1
    //     0x14a9908: add             x1, x1, HEAP, lsl #32
    // 0x14a990c: cmp             w1, NULL
    // 0x14a9910: b.ne            #0x14a991c
    // 0x14a9914: r0 = Null
    //     0x14a9914: mov             x0, NULL
    // 0x14a9918: b               #0x14a9964
    // 0x14a991c: LoadField: r0 = r1->field_1f
    //     0x14a991c: ldur            w0, [x1, #0x1f]
    // 0x14a9920: DecompressPointer r0
    //     0x14a9920: add             x0, x0, HEAP, lsl #32
    // 0x14a9924: cmp             w0, NULL
    // 0x14a9928: b.ne            #0x14a9934
    // 0x14a992c: r0 = Null
    //     0x14a992c: mov             x0, NULL
    // 0x14a9930: b               #0x14a9964
    // 0x14a9934: LoadField: r1 = r0->field_2f
    //     0x14a9934: ldur            w1, [x0, #0x2f]
    // 0x14a9938: DecompressPointer r1
    //     0x14a9938: add             x1, x1, HEAP, lsl #32
    // 0x14a993c: cmp             w1, NULL
    // 0x14a9940: b.ne            #0x14a994c
    // 0x14a9944: r0 = Null
    //     0x14a9944: mov             x0, NULL
    // 0x14a9948: b               #0x14a9964
    // 0x14a994c: LoadField: r0 = r1->field_7
    //     0x14a994c: ldur            w0, [x1, #7]
    // 0x14a9950: cbnz            w0, #0x14a995c
    // 0x14a9954: r1 = false
    //     0x14a9954: add             x1, NULL, #0x30  ; false
    // 0x14a9958: b               #0x14a9960
    // 0x14a995c: r1 = true
    //     0x14a995c: add             x1, NULL, #0x20  ; true
    // 0x14a9960: mov             x0, x1
    // 0x14a9964: cmp             w0, NULL
    // 0x14a9968: b.ne            #0x14a9974
    // 0x14a996c: r1 = false
    //     0x14a996c: add             x1, NULL, #0x30  ; false
    // 0x14a9970: b               #0x14a9978
    // 0x14a9974: mov             x1, x0
    // 0x14a9978: ldur            x0, [fp, #-8]
    // 0x14a997c: stur            x1, [fp, #-0x48]
    // 0x14a9980: r0 = Radius()
    //     0x14a9980: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14a9984: d0 = 20.000000
    //     0x14a9984: fmov            d0, #20.00000000
    // 0x14a9988: stur            x0, [fp, #-0x58]
    // 0x14a998c: StoreField: r0->field_7 = d0
    //     0x14a998c: stur            d0, [x0, #7]
    // 0x14a9990: StoreField: r0->field_f = d0
    //     0x14a9990: stur            d0, [x0, #0xf]
    // 0x14a9994: r0 = BorderRadius()
    //     0x14a9994: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14a9998: mov             x1, x0
    // 0x14a999c: ldur            x0, [fp, #-0x58]
    // 0x14a99a0: stur            x1, [fp, #-0x60]
    // 0x14a99a4: StoreField: r1->field_7 = r0
    //     0x14a99a4: stur            w0, [x1, #7]
    // 0x14a99a8: StoreField: r1->field_b = r0
    //     0x14a99a8: stur            w0, [x1, #0xb]
    // 0x14a99ac: StoreField: r1->field_f = r0
    //     0x14a99ac: stur            w0, [x1, #0xf]
    // 0x14a99b0: StoreField: r1->field_13 = r0
    //     0x14a99b0: stur            w0, [x1, #0x13]
    // 0x14a99b4: r0 = BoxDecoration()
    //     0x14a99b4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14a99b8: mov             x2, x0
    // 0x14a99bc: r0 = Instance_Color
    //     0x14a99bc: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a99c0: stur            x2, [fp, #-0x58]
    // 0x14a99c4: StoreField: r2->field_7 = r0
    //     0x14a99c4: stur            w0, [x2, #7]
    // 0x14a99c8: ldur            x0, [fp, #-0x60]
    // 0x14a99cc: StoreField: r2->field_13 = r0
    //     0x14a99cc: stur            w0, [x2, #0x13]
    // 0x14a99d0: r0 = Instance_BoxShape
    //     0x14a99d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a99d4: ldr             x0, [x0, #0x80]
    // 0x14a99d8: StoreField: r2->field_23 = r0
    //     0x14a99d8: stur            w0, [x2, #0x23]
    // 0x14a99dc: r1 = Instance_Color
    //     0x14a99dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a99e0: d0 = 0.500000
    //     0x14a99e0: fmov            d0, #0.50000000
    // 0x14a99e4: r0 = withOpacity()
    //     0x14a99e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a99e8: stur            x0, [fp, #-0x60]
    // 0x14a99ec: r0 = Icon()
    //     0x14a99ec: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14a99f0: mov             x2, x0
    // 0x14a99f4: r0 = Instance_IconData
    //     0x14a99f4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36af8] Obj!IconData@d55421
    //     0x14a99f8: ldr             x0, [x0, #0xaf8]
    // 0x14a99fc: stur            x2, [fp, #-0x68]
    // 0x14a9a00: StoreField: r2->field_b = r0
    //     0x14a9a00: stur            w0, [x2, #0xb]
    // 0x14a9a04: ldur            x0, [fp, #-0x60]
    // 0x14a9a08: StoreField: r2->field_23 = r0
    //     0x14a9a08: stur            w0, [x2, #0x23]
    // 0x14a9a0c: ldur            x0, [fp, #-8]
    // 0x14a9a10: LoadField: r1 = r0->field_f
    //     0x14a9a10: ldur            w1, [x0, #0xf]
    // 0x14a9a14: DecompressPointer r1
    //     0x14a9a14: add             x1, x1, HEAP, lsl #32
    // 0x14a9a18: r0 = controller()
    //     0x14a9a18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a9a1c: LoadField: r1 = r0->field_4b
    //     0x14a9a1c: ldur            w1, [x0, #0x4b]
    // 0x14a9a20: DecompressPointer r1
    //     0x14a9a20: add             x1, x1, HEAP, lsl #32
    // 0x14a9a24: r0 = value()
    //     0x14a9a24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a9a28: LoadField: r1 = r0->field_b
    //     0x14a9a28: ldur            w1, [x0, #0xb]
    // 0x14a9a2c: DecompressPointer r1
    //     0x14a9a2c: add             x1, x1, HEAP, lsl #32
    // 0x14a9a30: cmp             w1, NULL
    // 0x14a9a34: b.ne            #0x14a9a40
    // 0x14a9a38: r0 = Null
    //     0x14a9a38: mov             x0, NULL
    // 0x14a9a3c: b               #0x14a9a64
    // 0x14a9a40: LoadField: r0 = r1->field_1f
    //     0x14a9a40: ldur            w0, [x1, #0x1f]
    // 0x14a9a44: DecompressPointer r0
    //     0x14a9a44: add             x0, x0, HEAP, lsl #32
    // 0x14a9a48: cmp             w0, NULL
    // 0x14a9a4c: b.ne            #0x14a9a58
    // 0x14a9a50: r0 = Null
    //     0x14a9a50: mov             x0, NULL
    // 0x14a9a54: b               #0x14a9a64
    // 0x14a9a58: LoadField: r1 = r0->field_2f
    //     0x14a9a58: ldur            w1, [x0, #0x2f]
    // 0x14a9a5c: DecompressPointer r1
    //     0x14a9a5c: add             x1, x1, HEAP, lsl #32
    // 0x14a9a60: mov             x0, x1
    // 0x14a9a64: cmp             w0, NULL
    // 0x14a9a68: b.ne            #0x14a9a74
    // 0x14a9a6c: r10 = ""
    //     0x14a9a6c: ldr             x10, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a9a70: b               #0x14a9a78
    // 0x14a9a74: mov             x10, x0
    // 0x14a9a78: ldur            x1, [fp, #-8]
    // 0x14a9a7c: ldur            x9, [fp, #-0x10]
    // 0x14a9a80: ldur            x8, [fp, #-0x18]
    // 0x14a9a84: ldur            x7, [fp, #-0x20]
    // 0x14a9a88: ldur            x6, [fp, #-0x28]
    // 0x14a9a8c: ldur            x5, [fp, #-0x30]
    // 0x14a9a90: ldur            x4, [fp, #-0x38]
    // 0x14a9a94: ldur            x3, [fp, #-0x40]
    // 0x14a9a98: ldur            x2, [fp, #-0x48]
    // 0x14a9a9c: ldur            x0, [fp, #-0x68]
    // 0x14a9aa0: stur            x10, [fp, #-0x60]
    // 0x14a9aa4: LoadField: r11 = r1->field_13
    //     0x14a9aa4: ldur            w11, [x1, #0x13]
    // 0x14a9aa8: DecompressPointer r11
    //     0x14a9aa8: add             x11, x11, HEAP, lsl #32
    // 0x14a9aac: mov             x1, x11
    // 0x14a9ab0: r0 = of()
    //     0x14a9ab0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a9ab4: LoadField: r1 = r0->field_87
    //     0x14a9ab4: ldur            w1, [x0, #0x87]
    // 0x14a9ab8: DecompressPointer r1
    //     0x14a9ab8: add             x1, x1, HEAP, lsl #32
    // 0x14a9abc: LoadField: r0 = r1->field_2b
    //     0x14a9abc: ldur            w0, [x1, #0x2b]
    // 0x14a9ac0: DecompressPointer r0
    //     0x14a9ac0: add             x0, x0, HEAP, lsl #32
    // 0x14a9ac4: stur            x0, [fp, #-8]
    // 0x14a9ac8: r1 = Instance_Color
    //     0x14a9ac8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a9acc: d0 = 0.500000
    //     0x14a9acc: fmov            d0, #0.50000000
    // 0x14a9ad0: r0 = withOpacity()
    //     0x14a9ad0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a9ad4: r16 = 12.000000
    //     0x14a9ad4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a9ad8: ldr             x16, [x16, #0x9e8]
    // 0x14a9adc: stp             x0, x16, [SP]
    // 0x14a9ae0: ldur            x1, [fp, #-8]
    // 0x14a9ae4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a9ae4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a9ae8: ldr             x4, [x4, #0xaa0]
    // 0x14a9aec: r0 = copyWith()
    //     0x14a9aec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a9af0: stur            x0, [fp, #-8]
    // 0x14a9af4: r0 = Text()
    //     0x14a9af4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a9af8: mov             x2, x0
    // 0x14a9afc: ldur            x0, [fp, #-0x60]
    // 0x14a9b00: stur            x2, [fp, #-0x70]
    // 0x14a9b04: StoreField: r2->field_b = r0
    //     0x14a9b04: stur            w0, [x2, #0xb]
    // 0x14a9b08: ldur            x0, [fp, #-8]
    // 0x14a9b0c: StoreField: r2->field_13 = r0
    //     0x14a9b0c: stur            w0, [x2, #0x13]
    // 0x14a9b10: r1 = <FlexParentData>
    //     0x14a9b10: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14a9b14: ldr             x1, [x1, #0xe00]
    // 0x14a9b18: r0 = Expanded()
    //     0x14a9b18: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14a9b1c: mov             x3, x0
    // 0x14a9b20: r0 = 1
    //     0x14a9b20: movz            x0, #0x1
    // 0x14a9b24: stur            x3, [fp, #-8]
    // 0x14a9b28: StoreField: r3->field_13 = r0
    //     0x14a9b28: stur            x0, [x3, #0x13]
    // 0x14a9b2c: r0 = Instance_FlexFit
    //     0x14a9b2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14a9b30: ldr             x0, [x0, #0xe08]
    // 0x14a9b34: StoreField: r3->field_1b = r0
    //     0x14a9b34: stur            w0, [x3, #0x1b]
    // 0x14a9b38: ldur            x0, [fp, #-0x70]
    // 0x14a9b3c: StoreField: r3->field_b = r0
    //     0x14a9b3c: stur            w0, [x3, #0xb]
    // 0x14a9b40: r1 = Null
    //     0x14a9b40: mov             x1, NULL
    // 0x14a9b44: r2 = 6
    //     0x14a9b44: movz            x2, #0x6
    // 0x14a9b48: r0 = AllocateArray()
    //     0x14a9b48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a9b4c: mov             x2, x0
    // 0x14a9b50: ldur            x0, [fp, #-0x68]
    // 0x14a9b54: stur            x2, [fp, #-0x60]
    // 0x14a9b58: StoreField: r2->field_f = r0
    //     0x14a9b58: stur            w0, [x2, #0xf]
    // 0x14a9b5c: r16 = Instance_SizedBox
    //     0x14a9b5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14a9b60: ldr             x16, [x16, #0xb20]
    // 0x14a9b64: StoreField: r2->field_13 = r16
    //     0x14a9b64: stur            w16, [x2, #0x13]
    // 0x14a9b68: ldur            x0, [fp, #-8]
    // 0x14a9b6c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14a9b6c: stur            w0, [x2, #0x17]
    // 0x14a9b70: r1 = <Widget>
    //     0x14a9b70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a9b74: r0 = AllocateGrowableArray()
    //     0x14a9b74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a9b78: mov             x1, x0
    // 0x14a9b7c: ldur            x0, [fp, #-0x60]
    // 0x14a9b80: stur            x1, [fp, #-8]
    // 0x14a9b84: StoreField: r1->field_f = r0
    //     0x14a9b84: stur            w0, [x1, #0xf]
    // 0x14a9b88: r0 = 6
    //     0x14a9b88: movz            x0, #0x6
    // 0x14a9b8c: StoreField: r1->field_b = r0
    //     0x14a9b8c: stur            w0, [x1, #0xb]
    // 0x14a9b90: r0 = Row()
    //     0x14a9b90: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14a9b94: mov             x1, x0
    // 0x14a9b98: r0 = Instance_Axis
    //     0x14a9b98: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a9b9c: stur            x1, [fp, #-0x60]
    // 0x14a9ba0: StoreField: r1->field_f = r0
    //     0x14a9ba0: stur            w0, [x1, #0xf]
    // 0x14a9ba4: r0 = Instance_MainAxisAlignment
    //     0x14a9ba4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a9ba8: ldr             x0, [x0, #0xa08]
    // 0x14a9bac: StoreField: r1->field_13 = r0
    //     0x14a9bac: stur            w0, [x1, #0x13]
    // 0x14a9bb0: r2 = Instance_MainAxisSize
    //     0x14a9bb0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a9bb4: ldr             x2, [x2, #0xa10]
    // 0x14a9bb8: ArrayStore: r1[0] = r2  ; List_4
    //     0x14a9bb8: stur            w2, [x1, #0x17]
    // 0x14a9bbc: r3 = Instance_CrossAxisAlignment
    //     0x14a9bbc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14a9bc0: ldr             x3, [x3, #0xa18]
    // 0x14a9bc4: StoreField: r1->field_1b = r3
    //     0x14a9bc4: stur            w3, [x1, #0x1b]
    // 0x14a9bc8: r3 = Instance_VerticalDirection
    //     0x14a9bc8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a9bcc: ldr             x3, [x3, #0xa20]
    // 0x14a9bd0: StoreField: r1->field_23 = r3
    //     0x14a9bd0: stur            w3, [x1, #0x23]
    // 0x14a9bd4: r4 = Instance_Clip
    //     0x14a9bd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a9bd8: ldr             x4, [x4, #0x38]
    // 0x14a9bdc: StoreField: r1->field_2b = r4
    //     0x14a9bdc: stur            w4, [x1, #0x2b]
    // 0x14a9be0: StoreField: r1->field_2f = rZR
    //     0x14a9be0: stur            xzr, [x1, #0x2f]
    // 0x14a9be4: ldur            x5, [fp, #-8]
    // 0x14a9be8: StoreField: r1->field_b = r5
    //     0x14a9be8: stur            w5, [x1, #0xb]
    // 0x14a9bec: r0 = Container()
    //     0x14a9bec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a9bf0: stur            x0, [fp, #-8]
    // 0x14a9bf4: r16 = Instance_EdgeInsets
    //     0x14a9bf4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14a9bf8: ldr             x16, [x16, #0x980]
    // 0x14a9bfc: ldur            lr, [fp, #-0x58]
    // 0x14a9c00: stp             lr, x16, [SP, #8]
    // 0x14a9c04: ldur            x16, [fp, #-0x60]
    // 0x14a9c08: str             x16, [SP]
    // 0x14a9c0c: mov             x1, x0
    // 0x14a9c10: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x14a9c10: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x14a9c14: ldr             x4, [x4, #0x610]
    // 0x14a9c18: r0 = Container()
    //     0x14a9c18: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a9c1c: r0 = Padding()
    //     0x14a9c1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a9c20: mov             x1, x0
    // 0x14a9c24: r0 = Instance_EdgeInsets
    //     0x14a9c24: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0x14a9c28: ldr             x0, [x0, #0xa98]
    // 0x14a9c2c: stur            x1, [fp, #-0x58]
    // 0x14a9c30: StoreField: r1->field_f = r0
    //     0x14a9c30: stur            w0, [x1, #0xf]
    // 0x14a9c34: ldur            x0, [fp, #-8]
    // 0x14a9c38: StoreField: r1->field_b = r0
    //     0x14a9c38: stur            w0, [x1, #0xb]
    // 0x14a9c3c: r0 = Visibility()
    //     0x14a9c3c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14a9c40: mov             x3, x0
    // 0x14a9c44: ldur            x0, [fp, #-0x58]
    // 0x14a9c48: stur            x3, [fp, #-8]
    // 0x14a9c4c: StoreField: r3->field_b = r0
    //     0x14a9c4c: stur            w0, [x3, #0xb]
    // 0x14a9c50: r0 = Instance_SizedBox
    //     0x14a9c50: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14a9c54: StoreField: r3->field_f = r0
    //     0x14a9c54: stur            w0, [x3, #0xf]
    // 0x14a9c58: ldur            x0, [fp, #-0x48]
    // 0x14a9c5c: StoreField: r3->field_13 = r0
    //     0x14a9c5c: stur            w0, [x3, #0x13]
    // 0x14a9c60: r0 = false
    //     0x14a9c60: add             x0, NULL, #0x30  ; false
    // 0x14a9c64: ArrayStore: r3[0] = r0  ; List_4
    //     0x14a9c64: stur            w0, [x3, #0x17]
    // 0x14a9c68: StoreField: r3->field_1b = r0
    //     0x14a9c68: stur            w0, [x3, #0x1b]
    // 0x14a9c6c: StoreField: r3->field_1f = r0
    //     0x14a9c6c: stur            w0, [x3, #0x1f]
    // 0x14a9c70: StoreField: r3->field_23 = r0
    //     0x14a9c70: stur            w0, [x3, #0x23]
    // 0x14a9c74: StoreField: r3->field_27 = r0
    //     0x14a9c74: stur            w0, [x3, #0x27]
    // 0x14a9c78: StoreField: r3->field_2b = r0
    //     0x14a9c78: stur            w0, [x3, #0x2b]
    // 0x14a9c7c: r1 = Null
    //     0x14a9c7c: mov             x1, NULL
    // 0x14a9c80: r2 = 16
    //     0x14a9c80: movz            x2, #0x10
    // 0x14a9c84: r0 = AllocateArray()
    //     0x14a9c84: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a9c88: mov             x2, x0
    // 0x14a9c8c: ldur            x0, [fp, #-0x10]
    // 0x14a9c90: stur            x2, [fp, #-0x48]
    // 0x14a9c94: StoreField: r2->field_f = r0
    //     0x14a9c94: stur            w0, [x2, #0xf]
    // 0x14a9c98: ldur            x0, [fp, #-0x18]
    // 0x14a9c9c: StoreField: r2->field_13 = r0
    //     0x14a9c9c: stur            w0, [x2, #0x13]
    // 0x14a9ca0: ldur            x0, [fp, #-0x20]
    // 0x14a9ca4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14a9ca4: stur            w0, [x2, #0x17]
    // 0x14a9ca8: ldur            x0, [fp, #-0x28]
    // 0x14a9cac: StoreField: r2->field_1b = r0
    //     0x14a9cac: stur            w0, [x2, #0x1b]
    // 0x14a9cb0: ldur            x0, [fp, #-0x30]
    // 0x14a9cb4: StoreField: r2->field_1f = r0
    //     0x14a9cb4: stur            w0, [x2, #0x1f]
    // 0x14a9cb8: ldur            x0, [fp, #-0x38]
    // 0x14a9cbc: StoreField: r2->field_23 = r0
    //     0x14a9cbc: stur            w0, [x2, #0x23]
    // 0x14a9cc0: ldur            x0, [fp, #-0x40]
    // 0x14a9cc4: StoreField: r2->field_27 = r0
    //     0x14a9cc4: stur            w0, [x2, #0x27]
    // 0x14a9cc8: ldur            x0, [fp, #-8]
    // 0x14a9ccc: StoreField: r2->field_2b = r0
    //     0x14a9ccc: stur            w0, [x2, #0x2b]
    // 0x14a9cd0: r1 = <Widget>
    //     0x14a9cd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a9cd4: r0 = AllocateGrowableArray()
    //     0x14a9cd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a9cd8: mov             x1, x0
    // 0x14a9cdc: ldur            x0, [fp, #-0x48]
    // 0x14a9ce0: stur            x1, [fp, #-8]
    // 0x14a9ce4: StoreField: r1->field_f = r0
    //     0x14a9ce4: stur            w0, [x1, #0xf]
    // 0x14a9ce8: r0 = 16
    //     0x14a9ce8: movz            x0, #0x10
    // 0x14a9cec: StoreField: r1->field_b = r0
    //     0x14a9cec: stur            w0, [x1, #0xb]
    // 0x14a9cf0: r0 = Column()
    //     0x14a9cf0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14a9cf4: mov             x1, x0
    // 0x14a9cf8: r0 = Instance_Axis
    //     0x14a9cf8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a9cfc: stur            x1, [fp, #-0x10]
    // 0x14a9d00: StoreField: r1->field_f = r0
    //     0x14a9d00: stur            w0, [x1, #0xf]
    // 0x14a9d04: r2 = Instance_MainAxisAlignment
    //     0x14a9d04: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a9d08: ldr             x2, [x2, #0xa08]
    // 0x14a9d0c: StoreField: r1->field_13 = r2
    //     0x14a9d0c: stur            w2, [x1, #0x13]
    // 0x14a9d10: r2 = Instance_MainAxisSize
    //     0x14a9d10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a9d14: ldr             x2, [x2, #0xa10]
    // 0x14a9d18: ArrayStore: r1[0] = r2  ; List_4
    //     0x14a9d18: stur            w2, [x1, #0x17]
    // 0x14a9d1c: r2 = Instance_CrossAxisAlignment
    //     0x14a9d1c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14a9d20: ldr             x2, [x2, #0x890]
    // 0x14a9d24: StoreField: r1->field_1b = r2
    //     0x14a9d24: stur            w2, [x1, #0x1b]
    // 0x14a9d28: r2 = Instance_VerticalDirection
    //     0x14a9d28: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a9d2c: ldr             x2, [x2, #0xa20]
    // 0x14a9d30: StoreField: r1->field_23 = r2
    //     0x14a9d30: stur            w2, [x1, #0x23]
    // 0x14a9d34: r2 = Instance_Clip
    //     0x14a9d34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a9d38: ldr             x2, [x2, #0x38]
    // 0x14a9d3c: StoreField: r1->field_2b = r2
    //     0x14a9d3c: stur            w2, [x1, #0x2b]
    // 0x14a9d40: StoreField: r1->field_2f = rZR
    //     0x14a9d40: stur            xzr, [x1, #0x2f]
    // 0x14a9d44: ldur            x2, [fp, #-8]
    // 0x14a9d48: StoreField: r1->field_b = r2
    //     0x14a9d48: stur            w2, [x1, #0xb]
    // 0x14a9d4c: r0 = SingleChildScrollView()
    //     0x14a9d4c: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14a9d50: mov             x1, x0
    // 0x14a9d54: r0 = Instance_Axis
    //     0x14a9d54: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a9d58: stur            x1, [fp, #-8]
    // 0x14a9d5c: StoreField: r1->field_b = r0
    //     0x14a9d5c: stur            w0, [x1, #0xb]
    // 0x14a9d60: r0 = false
    //     0x14a9d60: add             x0, NULL, #0x30  ; false
    // 0x14a9d64: StoreField: r1->field_f = r0
    //     0x14a9d64: stur            w0, [x1, #0xf]
    // 0x14a9d68: ldur            x0, [fp, #-0x10]
    // 0x14a9d6c: StoreField: r1->field_23 = r0
    //     0x14a9d6c: stur            w0, [x1, #0x23]
    // 0x14a9d70: r0 = Instance_DragStartBehavior
    //     0x14a9d70: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14a9d74: StoreField: r1->field_27 = r0
    //     0x14a9d74: stur            w0, [x1, #0x27]
    // 0x14a9d78: r0 = Instance_Clip
    //     0x14a9d78: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14a9d7c: ldr             x0, [x0, #0x7e0]
    // 0x14a9d80: StoreField: r1->field_2b = r0
    //     0x14a9d80: stur            w0, [x1, #0x2b]
    // 0x14a9d84: r0 = Instance_HitTestBehavior
    //     0x14a9d84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14a9d88: ldr             x0, [x0, #0x288]
    // 0x14a9d8c: StoreField: r1->field_2f = r0
    //     0x14a9d8c: stur            w0, [x1, #0x2f]
    // 0x14a9d90: r0 = Padding()
    //     0x14a9d90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a9d94: r1 = Instance_EdgeInsets
    //     0x14a9d94: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0x14a9d98: ldr             x1, [x1, #0xb00]
    // 0x14a9d9c: StoreField: r0->field_f = r1
    //     0x14a9d9c: stur            w1, [x0, #0xf]
    // 0x14a9da0: ldur            x1, [fp, #-8]
    // 0x14a9da4: StoreField: r0->field_b = r1
    //     0x14a9da4: stur            w1, [x0, #0xb]
    // 0x14a9da8: LeaveFrame
    //     0x14a9da8: mov             SP, fp
    //     0x14a9dac: ldp             fp, lr, [SP], #0x10
    // 0x14a9db0: ret
    //     0x14a9db0: ret             
    // 0x14a9db4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a9db4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a9db8: b               #0x14a895c
    // 0x14a9dbc: SaveReg d0
    //     0x14a9dbc: str             q0, [SP, #-0x10]!
    // 0x14a9dc0: r0 = AllocateDouble()
    //     0x14a9dc0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14a9dc4: RestoreReg d0
    //     0x14a9dc4: ldr             q0, [SP], #0x10
    // 0x14a9dc8: b               #0x14a9388
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14a9dcc, size: 0xc90
    // 0x14a9dcc: EnterFrame
    //     0x14a9dcc: stp             fp, lr, [SP, #-0x10]!
    //     0x14a9dd0: mov             fp, SP
    // 0x14a9dd4: AllocStack(0x60)
    //     0x14a9dd4: sub             SP, SP, #0x60
    // 0x14a9dd8: SetupParameters()
    //     0x14a9dd8: ldr             x0, [fp, #0x20]
    //     0x14a9ddc: ldur            w1, [x0, #0x17]
    //     0x14a9de0: add             x1, x1, HEAP, lsl #32
    //     0x14a9de4: stur            x1, [fp, #-8]
    // 0x14a9de8: CheckStackOverflow
    //     0x14a9de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a9dec: cmp             SP, x16
    //     0x14a9df0: b.ls            #0x14aaa54
    // 0x14a9df4: r1 = 1
    //     0x14a9df4: movz            x1, #0x1
    // 0x14a9df8: r0 = AllocateContext()
    //     0x14a9df8: bl              #0x16f6108  ; AllocateContextStub
    // 0x14a9dfc: mov             x2, x0
    // 0x14a9e00: ldur            x0, [fp, #-8]
    // 0x14a9e04: stur            x2, [fp, #-0x10]
    // 0x14a9e08: StoreField: r2->field_b = r0
    //     0x14a9e08: stur            w0, [x2, #0xb]
    // 0x14a9e0c: ldr             x3, [fp, #0x10]
    // 0x14a9e10: StoreField: r2->field_f = r3
    //     0x14a9e10: stur            w3, [x2, #0xf]
    // 0x14a9e14: LoadField: r1 = r0->field_f
    //     0x14a9e14: ldur            w1, [x0, #0xf]
    // 0x14a9e18: DecompressPointer r1
    //     0x14a9e18: add             x1, x1, HEAP, lsl #32
    // 0x14a9e1c: r0 = controller()
    //     0x14a9e1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a9e20: LoadField: r1 = r0->field_97
    //     0x14a9e20: ldur            w1, [x0, #0x97]
    // 0x14a9e24: DecompressPointer r1
    //     0x14a9e24: add             x1, x1, HEAP, lsl #32
    // 0x14a9e28: r0 = value()
    //     0x14a9e28: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14a9e2c: r1 = LoadClassIdInstr(r0)
    //     0x14a9e2c: ldur            x1, [x0, #-1]
    //     0x14a9e30: ubfx            x1, x1, #0xc, #0x14
    // 0x14a9e34: str             x0, [SP]
    // 0x14a9e38: mov             x0, x1
    // 0x14a9e3c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14a9e3c: movz            x17, #0xc898
    //     0x14a9e40: add             lr, x0, x17
    //     0x14a9e44: ldr             lr, [x21, lr, lsl #3]
    //     0x14a9e48: blr             lr
    // 0x14a9e4c: mov             x1, x0
    // 0x14a9e50: ldr             x0, [fp, #0x10]
    // 0x14a9e54: r2 = LoadInt32Instr(r0)
    //     0x14a9e54: sbfx            x2, x0, #1, #0x1f
    //     0x14a9e58: tbz             w0, #0, #0x14a9e60
    //     0x14a9e5c: ldur            x2, [x0, #7]
    // 0x14a9e60: r0 = LoadInt32Instr(r1)
    //     0x14a9e60: sbfx            x0, x1, #1, #0x1f
    //     0x14a9e64: tbz             w1, #0, #0x14a9e6c
    //     0x14a9e68: ldur            x0, [x1, #7]
    // 0x14a9e6c: cmp             x2, x0
    // 0x14a9e70: b.ne            #0x14aa048
    // 0x14a9e74: ldur            x0, [fp, #-8]
    // 0x14a9e78: LoadField: r1 = r0->field_f
    //     0x14a9e78: ldur            w1, [x0, #0xf]
    // 0x14a9e7c: DecompressPointer r1
    //     0x14a9e7c: add             x1, x1, HEAP, lsl #32
    // 0x14a9e80: r0 = controller()
    //     0x14a9e80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a9e84: LoadField: r1 = r0->field_97
    //     0x14a9e84: ldur            w1, [x0, #0x97]
    // 0x14a9e88: DecompressPointer r1
    //     0x14a9e88: add             x1, x1, HEAP, lsl #32
    // 0x14a9e8c: r0 = value()
    //     0x14a9e8c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14a9e90: r1 = LoadClassIdInstr(r0)
    //     0x14a9e90: ldur            x1, [x0, #-1]
    //     0x14a9e94: ubfx            x1, x1, #0xc, #0x14
    // 0x14a9e98: str             x0, [SP]
    // 0x14a9e9c: mov             x0, x1
    // 0x14a9ea0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14a9ea0: movz            x17, #0xc898
    //     0x14a9ea4: add             lr, x0, x17
    //     0x14a9ea8: ldr             lr, [x21, lr, lsl #3]
    //     0x14a9eac: blr             lr
    // 0x14a9eb0: r1 = LoadInt32Instr(r0)
    //     0x14a9eb0: sbfx            x1, x0, #1, #0x1f
    //     0x14a9eb4: tbz             w0, #0, #0x14a9ebc
    //     0x14a9eb8: ldur            x1, [x0, #7]
    // 0x14a9ebc: cmp             x1, #4
    // 0x14a9ec0: b.ge            #0x14aa038
    // 0x14a9ec4: r0 = Radius()
    //     0x14a9ec4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14a9ec8: d0 = 15.000000
    //     0x14a9ec8: fmov            d0, #15.00000000
    // 0x14a9ecc: stur            x0, [fp, #-0x18]
    // 0x14a9ed0: StoreField: r0->field_7 = d0
    //     0x14a9ed0: stur            d0, [x0, #7]
    // 0x14a9ed4: StoreField: r0->field_f = d0
    //     0x14a9ed4: stur            d0, [x0, #0xf]
    // 0x14a9ed8: r0 = BorderRadius()
    //     0x14a9ed8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14a9edc: mov             x1, x0
    // 0x14a9ee0: ldur            x0, [fp, #-0x18]
    // 0x14a9ee4: stur            x1, [fp, #-0x20]
    // 0x14a9ee8: StoreField: r1->field_7 = r0
    //     0x14a9ee8: stur            w0, [x1, #7]
    // 0x14a9eec: StoreField: r1->field_b = r0
    //     0x14a9eec: stur            w0, [x1, #0xb]
    // 0x14a9ef0: StoreField: r1->field_f = r0
    //     0x14a9ef0: stur            w0, [x1, #0xf]
    // 0x14a9ef4: StoreField: r1->field_13 = r0
    //     0x14a9ef4: stur            w0, [x1, #0x13]
    // 0x14a9ef8: r0 = BoxDecoration()
    //     0x14a9ef8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14a9efc: mov             x2, x0
    // 0x14a9f00: r0 = Instance_Color
    //     0x14a9f00: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a9f04: stur            x2, [fp, #-0x18]
    // 0x14a9f08: StoreField: r2->field_7 = r0
    //     0x14a9f08: stur            w0, [x2, #7]
    // 0x14a9f0c: ldur            x0, [fp, #-0x20]
    // 0x14a9f10: StoreField: r2->field_13 = r0
    //     0x14a9f10: stur            w0, [x2, #0x13]
    // 0x14a9f14: r0 = Instance_BoxShape
    //     0x14a9f14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a9f18: ldr             x0, [x0, #0x80]
    // 0x14a9f1c: StoreField: r2->field_23 = r0
    //     0x14a9f1c: stur            w0, [x2, #0x23]
    // 0x14a9f20: r1 = Instance_MaterialColor
    //     0x14a9f20: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x14a9f24: ldr             x1, [x1, #0xdc0]
    // 0x14a9f28: d0 = 0.300000
    //     0x14a9f28: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14a9f2c: ldr             d0, [x17, #0x658]
    // 0x14a9f30: r0 = withOpacity()
    //     0x14a9f30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a9f34: stur            x0, [fp, #-0x20]
    // 0x14a9f38: r0 = DottedBorder()
    //     0x14a9f38: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0x14a9f3c: stur            x0, [fp, #-0x28]
    // 0x14a9f40: r16 = Instance_BorderType
    //     0x14a9f40: add             x16, PP, #0x40, lsl #12  ; [pp+0x40078] Obj!BorderType@d750a1
    //     0x14a9f44: ldr             x16, [x16, #0x78]
    // 0x14a9f48: r30 = Instance_Radius
    //     0x14a9f48: add             lr, PP, #0x40, lsl #12  ; [pp+0x40080] Obj!Radius@d6bee1
    //     0x14a9f4c: ldr             lr, [lr, #0x80]
    // 0x14a9f50: stp             lr, x16, [SP]
    // 0x14a9f54: mov             x1, x0
    // 0x14a9f58: ldur            x3, [fp, #-0x20]
    // 0x14a9f5c: r2 = Instance_Center
    //     0x14a9f5c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43050] Obj!Center@d68381
    //     0x14a9f60: ldr             x2, [x2, #0x50]
    // 0x14a9f64: r5 = const [5.0, 5.0]
    //     0x14a9f64: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0x14a9f68: ldr             x5, [x5, #0xab0]
    // 0x14a9f6c: d0 = 1.000000
    //     0x14a9f6c: fmov            d0, #1.00000000
    // 0x14a9f70: r4 = const [0, 0x7, 0x2, 0x5, borderType, 0x5, radius, 0x6, null]
    //     0x14a9f70: add             x4, PP, #0x40, lsl #12  ; [pp+0x40088] List(9) [0, 0x7, 0x2, 0x5, "borderType", 0x5, "radius", 0x6, Null]
    //     0x14a9f74: ldr             x4, [x4, #0x88]
    // 0x14a9f78: r0 = DottedBorder()
    //     0x14a9f78: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0x14a9f7c: r0 = Container()
    //     0x14a9f7c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a9f80: stur            x0, [fp, #-0x20]
    // 0x14a9f84: r16 = 57.000000
    //     0x14a9f84: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x14a9f88: ldr             x16, [x16, #0xb18]
    // 0x14a9f8c: r30 = 57.000000
    //     0x14a9f8c: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x14a9f90: ldr             lr, [lr, #0xb18]
    // 0x14a9f94: stp             lr, x16, [SP, #0x10]
    // 0x14a9f98: ldur            x16, [fp, #-0x18]
    // 0x14a9f9c: ldur            lr, [fp, #-0x28]
    // 0x14a9fa0: stp             lr, x16, [SP]
    // 0x14a9fa4: mov             x1, x0
    // 0x14a9fa8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x14a9fa8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x14a9fac: ldr             x4, [x4, #0x8c0]
    // 0x14a9fb0: r0 = Container()
    //     0x14a9fb0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a9fb4: r0 = Padding()
    //     0x14a9fb4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a9fb8: mov             x1, x0
    // 0x14a9fbc: r0 = Instance_EdgeInsets
    //     0x14a9fbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x14a9fc0: ldr             x0, [x0, #0xdf8]
    // 0x14a9fc4: stur            x1, [fp, #-0x18]
    // 0x14a9fc8: StoreField: r1->field_f = r0
    //     0x14a9fc8: stur            w0, [x1, #0xf]
    // 0x14a9fcc: ldur            x0, [fp, #-0x20]
    // 0x14a9fd0: StoreField: r1->field_b = r0
    //     0x14a9fd0: stur            w0, [x1, #0xb]
    // 0x14a9fd4: r0 = InkWell()
    //     0x14a9fd4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14a9fd8: mov             x3, x0
    // 0x14a9fdc: ldur            x0, [fp, #-0x18]
    // 0x14a9fe0: stur            x3, [fp, #-0x20]
    // 0x14a9fe4: StoreField: r3->field_b = r0
    //     0x14a9fe4: stur            w0, [x3, #0xb]
    // 0x14a9fe8: ldur            x2, [fp, #-0x10]
    // 0x14a9fec: r1 = Function '<anonymous closure>':.
    //     0x14a9fec: add             x1, PP, #0x43, lsl #12  ; [pp+0x43058] AnonymousClosure: (0x140bcc4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x14a9ff0: ldr             x1, [x1, #0x58]
    // 0x14a9ff4: r0 = AllocateClosure()
    //     0x14a9ff4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a9ff8: mov             x1, x0
    // 0x14a9ffc: ldur            x0, [fp, #-0x20]
    // 0x14aa000: StoreField: r0->field_f = r1
    //     0x14aa000: stur            w1, [x0, #0xf]
    // 0x14aa004: r1 = true
    //     0x14aa004: add             x1, NULL, #0x20  ; true
    // 0x14aa008: StoreField: r0->field_43 = r1
    //     0x14aa008: stur            w1, [x0, #0x43]
    // 0x14aa00c: r2 = Instance_BoxShape
    //     0x14aa00c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14aa010: ldr             x2, [x2, #0x80]
    // 0x14aa014: StoreField: r0->field_47 = r2
    //     0x14aa014: stur            w2, [x0, #0x47]
    // 0x14aa018: StoreField: r0->field_6f = r1
    //     0x14aa018: stur            w1, [x0, #0x6f]
    // 0x14aa01c: r2 = false
    //     0x14aa01c: add             x2, NULL, #0x30  ; false
    // 0x14aa020: StoreField: r0->field_73 = r2
    //     0x14aa020: stur            w2, [x0, #0x73]
    // 0x14aa024: StoreField: r0->field_83 = r1
    //     0x14aa024: stur            w1, [x0, #0x83]
    // 0x14aa028: StoreField: r0->field_7b = r2
    //     0x14aa028: stur            w2, [x0, #0x7b]
    // 0x14aa02c: LeaveFrame
    //     0x14aa02c: mov             SP, fp
    //     0x14aa030: ldp             fp, lr, [SP], #0x10
    // 0x14aa034: ret
    //     0x14aa034: ret             
    // 0x14aa038: r0 = Instance_EdgeInsets
    //     0x14aa038: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x14aa03c: ldr             x0, [x0, #0xdf8]
    // 0x14aa040: d0 = 15.000000
    //     0x14aa040: fmov            d0, #15.00000000
    // 0x14aa044: b               #0x14aa054
    // 0x14aa048: r0 = Instance_EdgeInsets
    //     0x14aa048: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x14aa04c: ldr             x0, [x0, #0xdf8]
    // 0x14aa050: d0 = 15.000000
    //     0x14aa050: fmov            d0, #15.00000000
    // 0x14aa054: ldur            x3, [fp, #-8]
    // 0x14aa058: ldur            x4, [fp, #-0x10]
    // 0x14aa05c: r1 = <Widget>
    //     0x14aa05c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14aa060: r2 = 0
    //     0x14aa060: movz            x2, #0
    // 0x14aa064: r0 = _GrowableList()
    //     0x14aa064: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x14aa068: mov             x2, x0
    // 0x14aa06c: ldur            x0, [fp, #-8]
    // 0x14aa070: stur            x2, [fp, #-0x18]
    // 0x14aa074: LoadField: r1 = r0->field_f
    //     0x14aa074: ldur            w1, [x0, #0xf]
    // 0x14aa078: DecompressPointer r1
    //     0x14aa078: add             x1, x1, HEAP, lsl #32
    // 0x14aa07c: r0 = controller()
    //     0x14aa07c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa080: LoadField: r1 = r0->field_97
    //     0x14aa080: ldur            w1, [x0, #0x97]
    // 0x14aa084: DecompressPointer r1
    //     0x14aa084: add             x1, x1, HEAP, lsl #32
    // 0x14aa088: ldur            x2, [fp, #-0x10]
    // 0x14aa08c: LoadField: r0 = r2->field_f
    //     0x14aa08c: ldur            w0, [x2, #0xf]
    // 0x14aa090: DecompressPointer r0
    //     0x14aa090: add             x0, x0, HEAP, lsl #32
    // 0x14aa094: stur            x0, [fp, #-0x20]
    // 0x14aa098: r0 = value()
    //     0x14aa098: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa09c: r1 = LoadClassIdInstr(r0)
    //     0x14aa09c: ldur            x1, [x0, #-1]
    //     0x14aa0a0: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa0a4: ldur            x16, [fp, #-0x20]
    // 0x14aa0a8: stp             x16, x0, [SP]
    // 0x14aa0ac: mov             x0, x1
    // 0x14aa0b0: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa0b0: sub             lr, x0, #0xb7
    //     0x14aa0b4: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa0b8: blr             lr
    // 0x14aa0bc: LoadField: r1 = r0->field_f
    //     0x14aa0bc: ldur            w1, [x0, #0xf]
    // 0x14aa0c0: DecompressPointer r1
    //     0x14aa0c0: add             x1, x1, HEAP, lsl #32
    // 0x14aa0c4: cmp             w1, NULL
    // 0x14aa0c8: b.ne            #0x14aa440
    // 0x14aa0cc: ldur            x0, [fp, #-8]
    // 0x14aa0d0: ldur            x2, [fp, #-0x10]
    // 0x14aa0d4: r0 = Radius()
    //     0x14aa0d4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14aa0d8: d0 = 15.000000
    //     0x14aa0d8: fmov            d0, #15.00000000
    // 0x14aa0dc: stur            x0, [fp, #-0x20]
    // 0x14aa0e0: StoreField: r0->field_7 = d0
    //     0x14aa0e0: stur            d0, [x0, #7]
    // 0x14aa0e4: StoreField: r0->field_f = d0
    //     0x14aa0e4: stur            d0, [x0, #0xf]
    // 0x14aa0e8: r0 = BorderRadius()
    //     0x14aa0e8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14aa0ec: mov             x2, x0
    // 0x14aa0f0: ldur            x0, [fp, #-0x20]
    // 0x14aa0f4: stur            x2, [fp, #-0x28]
    // 0x14aa0f8: StoreField: r2->field_7 = r0
    //     0x14aa0f8: stur            w0, [x2, #7]
    // 0x14aa0fc: StoreField: r2->field_b = r0
    //     0x14aa0fc: stur            w0, [x2, #0xb]
    // 0x14aa100: StoreField: r2->field_f = r0
    //     0x14aa100: stur            w0, [x2, #0xf]
    // 0x14aa104: StoreField: r2->field_13 = r0
    //     0x14aa104: stur            w0, [x2, #0x13]
    // 0x14aa108: ldur            x0, [fp, #-8]
    // 0x14aa10c: LoadField: r1 = r0->field_f
    //     0x14aa10c: ldur            w1, [x0, #0xf]
    // 0x14aa110: DecompressPointer r1
    //     0x14aa110: add             x1, x1, HEAP, lsl #32
    // 0x14aa114: r0 = controller()
    //     0x14aa114: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa118: LoadField: r1 = r0->field_97
    //     0x14aa118: ldur            w1, [x0, #0x97]
    // 0x14aa11c: DecompressPointer r1
    //     0x14aa11c: add             x1, x1, HEAP, lsl #32
    // 0x14aa120: ldur            x2, [fp, #-0x10]
    // 0x14aa124: LoadField: r0 = r2->field_f
    //     0x14aa124: ldur            w0, [x2, #0xf]
    // 0x14aa128: DecompressPointer r0
    //     0x14aa128: add             x0, x0, HEAP, lsl #32
    // 0x14aa12c: stur            x0, [fp, #-0x20]
    // 0x14aa130: r0 = value()
    //     0x14aa130: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa134: r1 = LoadClassIdInstr(r0)
    //     0x14aa134: ldur            x1, [x0, #-1]
    //     0x14aa138: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa13c: ldur            x16, [fp, #-0x20]
    // 0x14aa140: stp             x16, x0, [SP]
    // 0x14aa144: mov             x0, x1
    // 0x14aa148: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa148: sub             lr, x0, #0xb7
    //     0x14aa14c: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa150: blr             lr
    // 0x14aa154: LoadField: r1 = r0->field_13
    //     0x14aa154: ldur            w1, [x0, #0x13]
    // 0x14aa158: DecompressPointer r1
    //     0x14aa158: add             x1, x1, HEAP, lsl #32
    // 0x14aa15c: cmp             w1, NULL
    // 0x14aa160: b.ne            #0x14aa170
    // 0x14aa164: r4 = 0
    //     0x14aa164: movz            x4, #0
    // 0x14aa168: r0 = AllocateUint8Array()
    //     0x14aa168: bl              #0x16f6e7c  ; AllocateUint8ArrayStub
    // 0x14aa16c: mov             x1, x0
    // 0x14aa170: ldur            x0, [fp, #-8]
    // 0x14aa174: ldur            x2, [fp, #-0x10]
    // 0x14aa178: stur            x1, [fp, #-0x20]
    // 0x14aa17c: r0 = Image()
    //     0x14aa17c: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x14aa180: mov             x1, x0
    // 0x14aa184: ldur            x2, [fp, #-0x20]
    // 0x14aa188: d0 = 57.000000
    //     0x14aa188: add             x17, PP, #0x36, lsl #12  ; [pp+0x36b28] IMM: double(57) from 0x404c800000000000
    //     0x14aa18c: ldr             d0, [x17, #0xb28]
    // 0x14aa190: d1 = 57.000000
    //     0x14aa190: add             x17, PP, #0x36, lsl #12  ; [pp+0x36b28] IMM: double(57) from 0x404c800000000000
    //     0x14aa194: ldr             d1, [x17, #0xb28]
    // 0x14aa198: stur            x0, [fp, #-0x20]
    // 0x14aa19c: r0 = Image.memory()
    //     0x14aa19c: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x14aa1a0: r1 = Null
    //     0x14aa1a0: mov             x1, NULL
    // 0x14aa1a4: r2 = 2
    //     0x14aa1a4: movz            x2, #0x2
    // 0x14aa1a8: r0 = AllocateArray()
    //     0x14aa1a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14aa1ac: mov             x2, x0
    // 0x14aa1b0: ldur            x0, [fp, #-0x20]
    // 0x14aa1b4: stur            x2, [fp, #-0x30]
    // 0x14aa1b8: StoreField: r2->field_f = r0
    //     0x14aa1b8: stur            w0, [x2, #0xf]
    // 0x14aa1bc: r1 = <Widget>
    //     0x14aa1bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14aa1c0: r0 = AllocateGrowableArray()
    //     0x14aa1c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14aa1c4: mov             x2, x0
    // 0x14aa1c8: ldur            x0, [fp, #-0x30]
    // 0x14aa1cc: stur            x2, [fp, #-0x20]
    // 0x14aa1d0: StoreField: r2->field_f = r0
    //     0x14aa1d0: stur            w0, [x2, #0xf]
    // 0x14aa1d4: r0 = 2
    //     0x14aa1d4: movz            x0, #0x2
    // 0x14aa1d8: StoreField: r2->field_b = r0
    //     0x14aa1d8: stur            w0, [x2, #0xb]
    // 0x14aa1dc: ldur            x0, [fp, #-8]
    // 0x14aa1e0: LoadField: r1 = r0->field_f
    //     0x14aa1e0: ldur            w1, [x0, #0xf]
    // 0x14aa1e4: DecompressPointer r1
    //     0x14aa1e4: add             x1, x1, HEAP, lsl #32
    // 0x14aa1e8: r0 = controller()
    //     0x14aa1e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa1ec: LoadField: r1 = r0->field_97
    //     0x14aa1ec: ldur            w1, [x0, #0x97]
    // 0x14aa1f0: DecompressPointer r1
    //     0x14aa1f0: add             x1, x1, HEAP, lsl #32
    // 0x14aa1f4: ldur            x2, [fp, #-0x10]
    // 0x14aa1f8: LoadField: r0 = r2->field_f
    //     0x14aa1f8: ldur            w0, [x2, #0xf]
    // 0x14aa1fc: DecompressPointer r0
    //     0x14aa1fc: add             x0, x0, HEAP, lsl #32
    // 0x14aa200: stur            x0, [fp, #-0x30]
    // 0x14aa204: r0 = value()
    //     0x14aa204: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa208: r1 = LoadClassIdInstr(r0)
    //     0x14aa208: ldur            x1, [x0, #-1]
    //     0x14aa20c: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa210: ldur            x16, [fp, #-0x30]
    // 0x14aa214: stp             x16, x0, [SP]
    // 0x14aa218: mov             x0, x1
    // 0x14aa21c: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa21c: sub             lr, x0, #0xb7
    //     0x14aa220: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa224: blr             lr
    // 0x14aa228: LoadField: r1 = r0->field_b
    //     0x14aa228: ldur            w1, [x0, #0xb]
    // 0x14aa22c: DecompressPointer r1
    //     0x14aa22c: add             x1, x1, HEAP, lsl #32
    // 0x14aa230: r0 = LoadClassIdInstr(r1)
    //     0x14aa230: ldur            x0, [x1, #-1]
    //     0x14aa234: ubfx            x0, x0, #0xc, #0x14
    // 0x14aa238: r16 = "video_local"
    //     0x14aa238: add             x16, PP, #0x36, lsl #12  ; [pp+0x369c0] "video_local"
    //     0x14aa23c: ldr             x16, [x16, #0x9c0]
    // 0x14aa240: stp             x16, x1, [SP]
    // 0x14aa244: mov             lr, x0
    // 0x14aa248: ldr             lr, [x21, lr, lsl #3]
    // 0x14aa24c: blr             lr
    // 0x14aa250: tbnz            w0, #4, #0x14aa358
    // 0x14aa254: ldur            x0, [fp, #-0x20]
    // 0x14aa258: r1 = Instance_Color
    //     0x14aa258: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14aa25c: d0 = 0.300000
    //     0x14aa25c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14aa260: ldr             d0, [x17, #0x658]
    // 0x14aa264: r0 = withOpacity()
    //     0x14aa264: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14aa268: stur            x0, [fp, #-0x30]
    // 0x14aa26c: r0 = BoxDecoration()
    //     0x14aa26c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14aa270: mov             x1, x0
    // 0x14aa274: ldur            x0, [fp, #-0x30]
    // 0x14aa278: stur            x1, [fp, #-0x38]
    // 0x14aa27c: StoreField: r1->field_7 = r0
    //     0x14aa27c: stur            w0, [x1, #7]
    // 0x14aa280: r0 = Instance_BoxShape
    //     0x14aa280: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14aa284: ldr             x0, [x0, #0x970]
    // 0x14aa288: StoreField: r1->field_23 = r0
    //     0x14aa288: stur            w0, [x1, #0x23]
    // 0x14aa28c: r0 = Container()
    //     0x14aa28c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14aa290: stur            x0, [fp, #-0x30]
    // 0x14aa294: ldur            x16, [fp, #-0x38]
    // 0x14aa298: r30 = Instance_EdgeInsets
    //     0x14aa298: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0x14aa29c: ldr             lr, [lr, #0xb30]
    // 0x14aa2a0: stp             lr, x16, [SP, #8]
    // 0x14aa2a4: r16 = Instance_Icon
    //     0x14aa2a4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b38] Obj!Icon@d66431
    //     0x14aa2a8: ldr             x16, [x16, #0xb38]
    // 0x14aa2ac: str             x16, [SP]
    // 0x14aa2b0: mov             x1, x0
    // 0x14aa2b4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x14aa2b4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x14aa2b8: ldr             x4, [x4, #0xb40]
    // 0x14aa2bc: r0 = Container()
    //     0x14aa2bc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14aa2c0: r1 = <StackParentData>
    //     0x14aa2c0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14aa2c4: ldr             x1, [x1, #0x8e0]
    // 0x14aa2c8: r0 = Positioned()
    //     0x14aa2c8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14aa2cc: mov             x2, x0
    // 0x14aa2d0: ldur            x0, [fp, #-0x30]
    // 0x14aa2d4: stur            x2, [fp, #-0x38]
    // 0x14aa2d8: StoreField: r2->field_b = r0
    //     0x14aa2d8: stur            w0, [x2, #0xb]
    // 0x14aa2dc: ldur            x0, [fp, #-0x20]
    // 0x14aa2e0: LoadField: r1 = r0->field_b
    //     0x14aa2e0: ldur            w1, [x0, #0xb]
    // 0x14aa2e4: LoadField: r3 = r0->field_f
    //     0x14aa2e4: ldur            w3, [x0, #0xf]
    // 0x14aa2e8: DecompressPointer r3
    //     0x14aa2e8: add             x3, x3, HEAP, lsl #32
    // 0x14aa2ec: LoadField: r4 = r3->field_b
    //     0x14aa2ec: ldur            w4, [x3, #0xb]
    // 0x14aa2f0: r3 = LoadInt32Instr(r1)
    //     0x14aa2f0: sbfx            x3, x1, #1, #0x1f
    // 0x14aa2f4: stur            x3, [fp, #-0x40]
    // 0x14aa2f8: r1 = LoadInt32Instr(r4)
    //     0x14aa2f8: sbfx            x1, x4, #1, #0x1f
    // 0x14aa2fc: cmp             x3, x1
    // 0x14aa300: b.ne            #0x14aa30c
    // 0x14aa304: mov             x1, x0
    // 0x14aa308: r0 = _growToNextCapacity()
    //     0x14aa308: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14aa30c: ldur            x2, [fp, #-0x20]
    // 0x14aa310: ldur            x3, [fp, #-0x40]
    // 0x14aa314: add             x0, x3, #1
    // 0x14aa318: lsl             x1, x0, #1
    // 0x14aa31c: StoreField: r2->field_b = r1
    //     0x14aa31c: stur            w1, [x2, #0xb]
    // 0x14aa320: LoadField: r1 = r2->field_f
    //     0x14aa320: ldur            w1, [x2, #0xf]
    // 0x14aa324: DecompressPointer r1
    //     0x14aa324: add             x1, x1, HEAP, lsl #32
    // 0x14aa328: ldur            x0, [fp, #-0x38]
    // 0x14aa32c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14aa32c: add             x25, x1, x3, lsl #2
    //     0x14aa330: add             x25, x25, #0xf
    //     0x14aa334: str             w0, [x25]
    //     0x14aa338: tbz             w0, #0, #0x14aa354
    //     0x14aa33c: ldurb           w16, [x1, #-1]
    //     0x14aa340: ldurb           w17, [x0, #-1]
    //     0x14aa344: and             x16, x17, x16, lsr #2
    //     0x14aa348: tst             x16, HEAP, lsr #32
    //     0x14aa34c: b.eq            #0x14aa354
    //     0x14aa350: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14aa354: b               #0x14aa35c
    // 0x14aa358: ldur            x2, [fp, #-0x20]
    // 0x14aa35c: ldur            x1, [fp, #-0x18]
    // 0x14aa360: ldur            x0, [fp, #-0x28]
    // 0x14aa364: r0 = Stack()
    //     0x14aa364: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14aa368: mov             x1, x0
    // 0x14aa36c: r0 = Instance_Alignment
    //     0x14aa36c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14aa370: ldr             x0, [x0, #0xb10]
    // 0x14aa374: stur            x1, [fp, #-0x30]
    // 0x14aa378: StoreField: r1->field_f = r0
    //     0x14aa378: stur            w0, [x1, #0xf]
    // 0x14aa37c: r0 = Instance_StackFit
    //     0x14aa37c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14aa380: ldr             x0, [x0, #0xfa8]
    // 0x14aa384: ArrayStore: r1[0] = r0  ; List_4
    //     0x14aa384: stur            w0, [x1, #0x17]
    // 0x14aa388: r2 = Instance_Clip
    //     0x14aa388: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14aa38c: ldr             x2, [x2, #0x7e0]
    // 0x14aa390: StoreField: r1->field_1b = r2
    //     0x14aa390: stur            w2, [x1, #0x1b]
    // 0x14aa394: ldur            x3, [fp, #-0x20]
    // 0x14aa398: StoreField: r1->field_b = r3
    //     0x14aa398: stur            w3, [x1, #0xb]
    // 0x14aa39c: r0 = ClipRRect()
    //     0x14aa39c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14aa3a0: mov             x2, x0
    // 0x14aa3a4: ldur            x0, [fp, #-0x28]
    // 0x14aa3a8: stur            x2, [fp, #-0x20]
    // 0x14aa3ac: StoreField: r2->field_f = r0
    //     0x14aa3ac: stur            w0, [x2, #0xf]
    // 0x14aa3b0: r0 = Instance_Clip
    //     0x14aa3b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14aa3b4: ldr             x0, [x0, #0x138]
    // 0x14aa3b8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14aa3b8: stur            w0, [x2, #0x17]
    // 0x14aa3bc: ldur            x1, [fp, #-0x30]
    // 0x14aa3c0: StoreField: r2->field_b = r1
    //     0x14aa3c0: stur            w1, [x2, #0xb]
    // 0x14aa3c4: ldur            x3, [fp, #-0x18]
    // 0x14aa3c8: LoadField: r1 = r3->field_b
    //     0x14aa3c8: ldur            w1, [x3, #0xb]
    // 0x14aa3cc: LoadField: r4 = r3->field_f
    //     0x14aa3cc: ldur            w4, [x3, #0xf]
    // 0x14aa3d0: DecompressPointer r4
    //     0x14aa3d0: add             x4, x4, HEAP, lsl #32
    // 0x14aa3d4: LoadField: r5 = r4->field_b
    //     0x14aa3d4: ldur            w5, [x4, #0xb]
    // 0x14aa3d8: r4 = LoadInt32Instr(r1)
    //     0x14aa3d8: sbfx            x4, x1, #1, #0x1f
    // 0x14aa3dc: stur            x4, [fp, #-0x40]
    // 0x14aa3e0: r1 = LoadInt32Instr(r5)
    //     0x14aa3e0: sbfx            x1, x5, #1, #0x1f
    // 0x14aa3e4: cmp             x4, x1
    // 0x14aa3e8: b.ne            #0x14aa3f4
    // 0x14aa3ec: mov             x1, x3
    // 0x14aa3f0: r0 = _growToNextCapacity()
    //     0x14aa3f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14aa3f4: ldur            x2, [fp, #-0x18]
    // 0x14aa3f8: ldur            x3, [fp, #-0x40]
    // 0x14aa3fc: add             x0, x3, #1
    // 0x14aa400: lsl             x1, x0, #1
    // 0x14aa404: StoreField: r2->field_b = r1
    //     0x14aa404: stur            w1, [x2, #0xb]
    // 0x14aa408: LoadField: r1 = r2->field_f
    //     0x14aa408: ldur            w1, [x2, #0xf]
    // 0x14aa40c: DecompressPointer r1
    //     0x14aa40c: add             x1, x1, HEAP, lsl #32
    // 0x14aa410: ldur            x0, [fp, #-0x20]
    // 0x14aa414: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14aa414: add             x25, x1, x3, lsl #2
    //     0x14aa418: add             x25, x25, #0xf
    //     0x14aa41c: str             w0, [x25]
    //     0x14aa420: tbz             w0, #0, #0x14aa43c
    //     0x14aa424: ldurb           w16, [x1, #-1]
    //     0x14aa428: ldurb           w17, [x0, #-1]
    //     0x14aa42c: and             x16, x17, x16, lsr #2
    //     0x14aa430: tst             x16, HEAP, lsr #32
    //     0x14aa434: b.eq            #0x14aa43c
    //     0x14aa438: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14aa43c: b               #0x14aa444
    // 0x14aa440: ldur            x2, [fp, #-0x18]
    // 0x14aa444: ldur            x3, [fp, #-8]
    // 0x14aa448: ldur            x0, [fp, #-0x10]
    // 0x14aa44c: LoadField: r1 = r3->field_f
    //     0x14aa44c: ldur            w1, [x3, #0xf]
    // 0x14aa450: DecompressPointer r1
    //     0x14aa450: add             x1, x1, HEAP, lsl #32
    // 0x14aa454: r0 = controller()
    //     0x14aa454: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa458: LoadField: r1 = r0->field_97
    //     0x14aa458: ldur            w1, [x0, #0x97]
    // 0x14aa45c: DecompressPointer r1
    //     0x14aa45c: add             x1, x1, HEAP, lsl #32
    // 0x14aa460: ldur            x2, [fp, #-0x10]
    // 0x14aa464: LoadField: r0 = r2->field_f
    //     0x14aa464: ldur            w0, [x2, #0xf]
    // 0x14aa468: DecompressPointer r0
    //     0x14aa468: add             x0, x0, HEAP, lsl #32
    // 0x14aa46c: stur            x0, [fp, #-0x20]
    // 0x14aa470: r0 = value()
    //     0x14aa470: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa474: r1 = LoadClassIdInstr(r0)
    //     0x14aa474: ldur            x1, [x0, #-1]
    //     0x14aa478: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa47c: ldur            x16, [fp, #-0x20]
    // 0x14aa480: stp             x16, x0, [SP]
    // 0x14aa484: mov             x0, x1
    // 0x14aa488: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa488: sub             lr, x0, #0xb7
    //     0x14aa48c: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa490: blr             lr
    // 0x14aa494: LoadField: r1 = r0->field_f
    //     0x14aa494: ldur            w1, [x0, #0xf]
    // 0x14aa498: DecompressPointer r1
    //     0x14aa498: add             x1, x1, HEAP, lsl #32
    // 0x14aa49c: cmp             w1, NULL
    // 0x14aa4a0: b.eq            #0x14aa62c
    // 0x14aa4a4: ldur            x0, [fp, #-8]
    // 0x14aa4a8: ldur            x2, [fp, #-0x10]
    // 0x14aa4ac: LoadField: r1 = r0->field_f
    //     0x14aa4ac: ldur            w1, [x0, #0xf]
    // 0x14aa4b0: DecompressPointer r1
    //     0x14aa4b0: add             x1, x1, HEAP, lsl #32
    // 0x14aa4b4: r0 = controller()
    //     0x14aa4b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa4b8: LoadField: r1 = r0->field_97
    //     0x14aa4b8: ldur            w1, [x0, #0x97]
    // 0x14aa4bc: DecompressPointer r1
    //     0x14aa4bc: add             x1, x1, HEAP, lsl #32
    // 0x14aa4c0: ldur            x2, [fp, #-0x10]
    // 0x14aa4c4: LoadField: r0 = r2->field_f
    //     0x14aa4c4: ldur            w0, [x2, #0xf]
    // 0x14aa4c8: DecompressPointer r0
    //     0x14aa4c8: add             x0, x0, HEAP, lsl #32
    // 0x14aa4cc: stur            x0, [fp, #-0x20]
    // 0x14aa4d0: r0 = value()
    //     0x14aa4d0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa4d4: r1 = LoadClassIdInstr(r0)
    //     0x14aa4d4: ldur            x1, [x0, #-1]
    //     0x14aa4d8: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa4dc: ldur            x16, [fp, #-0x20]
    // 0x14aa4e0: stp             x16, x0, [SP]
    // 0x14aa4e4: mov             x0, x1
    // 0x14aa4e8: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa4e8: sub             lr, x0, #0xb7
    //     0x14aa4ec: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa4f0: blr             lr
    // 0x14aa4f4: LoadField: r1 = r0->field_b
    //     0x14aa4f4: ldur            w1, [x0, #0xb]
    // 0x14aa4f8: DecompressPointer r1
    //     0x14aa4f8: add             x1, x1, HEAP, lsl #32
    // 0x14aa4fc: r0 = LoadClassIdInstr(r1)
    //     0x14aa4fc: ldur            x0, [x1, #-1]
    //     0x14aa500: ubfx            x0, x0, #0xc, #0x14
    // 0x14aa504: r16 = "video"
    //     0x14aa504: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x14aa508: ldr             x16, [x16, #0xb50]
    // 0x14aa50c: stp             x16, x1, [SP]
    // 0x14aa510: mov             lr, x0
    // 0x14aa514: ldr             lr, [x21, lr, lsl #3]
    // 0x14aa518: blr             lr
    // 0x14aa51c: tbnz            w0, #4, #0x14aa624
    // 0x14aa520: ldur            x0, [fp, #-8]
    // 0x14aa524: ldur            x2, [fp, #-0x10]
    // 0x14aa528: LoadField: r1 = r0->field_f
    //     0x14aa528: ldur            w1, [x0, #0xf]
    // 0x14aa52c: DecompressPointer r1
    //     0x14aa52c: add             x1, x1, HEAP, lsl #32
    // 0x14aa530: r0 = controller()
    //     0x14aa530: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa534: LoadField: r1 = r0->field_97
    //     0x14aa534: ldur            w1, [x0, #0x97]
    // 0x14aa538: DecompressPointer r1
    //     0x14aa538: add             x1, x1, HEAP, lsl #32
    // 0x14aa53c: ldur            x2, [fp, #-0x10]
    // 0x14aa540: LoadField: r0 = r2->field_f
    //     0x14aa540: ldur            w0, [x2, #0xf]
    // 0x14aa544: DecompressPointer r0
    //     0x14aa544: add             x0, x0, HEAP, lsl #32
    // 0x14aa548: stur            x0, [fp, #-0x20]
    // 0x14aa54c: r0 = value()
    //     0x14aa54c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa550: r1 = LoadClassIdInstr(r0)
    //     0x14aa550: ldur            x1, [x0, #-1]
    //     0x14aa554: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa558: ldur            x16, [fp, #-0x20]
    // 0x14aa55c: stp             x16, x0, [SP]
    // 0x14aa560: mov             x0, x1
    // 0x14aa564: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa564: sub             lr, x0, #0xb7
    //     0x14aa568: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa56c: blr             lr
    // 0x14aa570: LoadField: r1 = r0->field_f
    //     0x14aa570: ldur            w1, [x0, #0xf]
    // 0x14aa574: DecompressPointer r1
    //     0x14aa574: add             x1, x1, HEAP, lsl #32
    // 0x14aa578: cmp             w1, NULL
    // 0x14aa57c: b.ne            #0x14aa588
    // 0x14aa580: r0 = ""
    //     0x14aa580: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14aa584: b               #0x14aa58c
    // 0x14aa588: mov             x0, x1
    // 0x14aa58c: ldur            x1, [fp, #-0x18]
    // 0x14aa590: stur            x0, [fp, #-0x20]
    // 0x14aa594: r0 = VideoPlayerWidget()
    //     0x14aa594: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x14aa598: mov             x2, x0
    // 0x14aa59c: ldur            x0, [fp, #-0x20]
    // 0x14aa5a0: stur            x2, [fp, #-0x28]
    // 0x14aa5a4: StoreField: r2->field_b = r0
    //     0x14aa5a4: stur            w0, [x2, #0xb]
    // 0x14aa5a8: ldur            x0, [fp, #-0x18]
    // 0x14aa5ac: LoadField: r1 = r0->field_b
    //     0x14aa5ac: ldur            w1, [x0, #0xb]
    // 0x14aa5b0: LoadField: r3 = r0->field_f
    //     0x14aa5b0: ldur            w3, [x0, #0xf]
    // 0x14aa5b4: DecompressPointer r3
    //     0x14aa5b4: add             x3, x3, HEAP, lsl #32
    // 0x14aa5b8: LoadField: r4 = r3->field_b
    //     0x14aa5b8: ldur            w4, [x3, #0xb]
    // 0x14aa5bc: r3 = LoadInt32Instr(r1)
    //     0x14aa5bc: sbfx            x3, x1, #1, #0x1f
    // 0x14aa5c0: stur            x3, [fp, #-0x40]
    // 0x14aa5c4: r1 = LoadInt32Instr(r4)
    //     0x14aa5c4: sbfx            x1, x4, #1, #0x1f
    // 0x14aa5c8: cmp             x3, x1
    // 0x14aa5cc: b.ne            #0x14aa5d8
    // 0x14aa5d0: mov             x1, x0
    // 0x14aa5d4: r0 = _growToNextCapacity()
    //     0x14aa5d4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14aa5d8: ldur            x2, [fp, #-0x18]
    // 0x14aa5dc: ldur            x3, [fp, #-0x40]
    // 0x14aa5e0: add             x0, x3, #1
    // 0x14aa5e4: lsl             x1, x0, #1
    // 0x14aa5e8: StoreField: r2->field_b = r1
    //     0x14aa5e8: stur            w1, [x2, #0xb]
    // 0x14aa5ec: LoadField: r1 = r2->field_f
    //     0x14aa5ec: ldur            w1, [x2, #0xf]
    // 0x14aa5f0: DecompressPointer r1
    //     0x14aa5f0: add             x1, x1, HEAP, lsl #32
    // 0x14aa5f4: ldur            x0, [fp, #-0x28]
    // 0x14aa5f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14aa5f8: add             x25, x1, x3, lsl #2
    //     0x14aa5fc: add             x25, x25, #0xf
    //     0x14aa600: str             w0, [x25]
    //     0x14aa604: tbz             w0, #0, #0x14aa620
    //     0x14aa608: ldurb           w16, [x1, #-1]
    //     0x14aa60c: ldurb           w17, [x0, #-1]
    //     0x14aa610: and             x16, x17, x16, lsr #2
    //     0x14aa614: tst             x16, HEAP, lsr #32
    //     0x14aa618: b.eq            #0x14aa620
    //     0x14aa61c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14aa620: b               #0x14aa630
    // 0x14aa624: ldur            x2, [fp, #-0x18]
    // 0x14aa628: b               #0x14aa630
    // 0x14aa62c: ldur            x2, [fp, #-0x18]
    // 0x14aa630: ldur            x3, [fp, #-8]
    // 0x14aa634: ldur            x0, [fp, #-0x10]
    // 0x14aa638: LoadField: r1 = r3->field_f
    //     0x14aa638: ldur            w1, [x3, #0xf]
    // 0x14aa63c: DecompressPointer r1
    //     0x14aa63c: add             x1, x1, HEAP, lsl #32
    // 0x14aa640: r0 = controller()
    //     0x14aa640: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa644: LoadField: r1 = r0->field_97
    //     0x14aa644: ldur            w1, [x0, #0x97]
    // 0x14aa648: DecompressPointer r1
    //     0x14aa648: add             x1, x1, HEAP, lsl #32
    // 0x14aa64c: ldur            x2, [fp, #-0x10]
    // 0x14aa650: LoadField: r0 = r2->field_f
    //     0x14aa650: ldur            w0, [x2, #0xf]
    // 0x14aa654: DecompressPointer r0
    //     0x14aa654: add             x0, x0, HEAP, lsl #32
    // 0x14aa658: stur            x0, [fp, #-0x20]
    // 0x14aa65c: r0 = value()
    //     0x14aa65c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa660: r1 = LoadClassIdInstr(r0)
    //     0x14aa660: ldur            x1, [x0, #-1]
    //     0x14aa664: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa668: ldur            x16, [fp, #-0x20]
    // 0x14aa66c: stp             x16, x0, [SP]
    // 0x14aa670: mov             x0, x1
    // 0x14aa674: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa674: sub             lr, x0, #0xb7
    //     0x14aa678: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa67c: blr             lr
    // 0x14aa680: LoadField: r1 = r0->field_f
    //     0x14aa680: ldur            w1, [x0, #0xf]
    // 0x14aa684: DecompressPointer r1
    //     0x14aa684: add             x1, x1, HEAP, lsl #32
    // 0x14aa688: cmp             w1, NULL
    // 0x14aa68c: b.eq            #0x14aa8a0
    // 0x14aa690: ldur            x0, [fp, #-8]
    // 0x14aa694: ldur            x2, [fp, #-0x10]
    // 0x14aa698: LoadField: r1 = r0->field_f
    //     0x14aa698: ldur            w1, [x0, #0xf]
    // 0x14aa69c: DecompressPointer r1
    //     0x14aa69c: add             x1, x1, HEAP, lsl #32
    // 0x14aa6a0: r0 = controller()
    //     0x14aa6a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa6a4: LoadField: r1 = r0->field_97
    //     0x14aa6a4: ldur            w1, [x0, #0x97]
    // 0x14aa6a8: DecompressPointer r1
    //     0x14aa6a8: add             x1, x1, HEAP, lsl #32
    // 0x14aa6ac: ldur            x2, [fp, #-0x10]
    // 0x14aa6b0: LoadField: r0 = r2->field_f
    //     0x14aa6b0: ldur            w0, [x2, #0xf]
    // 0x14aa6b4: DecompressPointer r0
    //     0x14aa6b4: add             x0, x0, HEAP, lsl #32
    // 0x14aa6b8: stur            x0, [fp, #-0x20]
    // 0x14aa6bc: r0 = value()
    //     0x14aa6bc: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa6c0: r1 = LoadClassIdInstr(r0)
    //     0x14aa6c0: ldur            x1, [x0, #-1]
    //     0x14aa6c4: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa6c8: ldur            x16, [fp, #-0x20]
    // 0x14aa6cc: stp             x16, x0, [SP]
    // 0x14aa6d0: mov             x0, x1
    // 0x14aa6d4: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa6d4: sub             lr, x0, #0xb7
    //     0x14aa6d8: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa6dc: blr             lr
    // 0x14aa6e0: LoadField: r1 = r0->field_b
    //     0x14aa6e0: ldur            w1, [x0, #0xb]
    // 0x14aa6e4: DecompressPointer r1
    //     0x14aa6e4: add             x1, x1, HEAP, lsl #32
    // 0x14aa6e8: r0 = LoadClassIdInstr(r1)
    //     0x14aa6e8: ldur            x0, [x1, #-1]
    //     0x14aa6ec: ubfx            x0, x0, #0xc, #0x14
    // 0x14aa6f0: r16 = "image"
    //     0x14aa6f0: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x14aa6f4: stp             x16, x1, [SP]
    // 0x14aa6f8: mov             lr, x0
    // 0x14aa6fc: ldr             lr, [x21, lr, lsl #3]
    // 0x14aa700: blr             lr
    // 0x14aa704: tbnz            w0, #4, #0x14aa898
    // 0x14aa708: ldur            x0, [fp, #-8]
    // 0x14aa70c: ldur            x2, [fp, #-0x10]
    // 0x14aa710: r0 = Radius()
    //     0x14aa710: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14aa714: d0 = 15.000000
    //     0x14aa714: fmov            d0, #15.00000000
    // 0x14aa718: stur            x0, [fp, #-0x20]
    // 0x14aa71c: StoreField: r0->field_7 = d0
    //     0x14aa71c: stur            d0, [x0, #7]
    // 0x14aa720: StoreField: r0->field_f = d0
    //     0x14aa720: stur            d0, [x0, #0xf]
    // 0x14aa724: r0 = BorderRadius()
    //     0x14aa724: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14aa728: mov             x2, x0
    // 0x14aa72c: ldur            x0, [fp, #-0x20]
    // 0x14aa730: stur            x2, [fp, #-0x28]
    // 0x14aa734: StoreField: r2->field_7 = r0
    //     0x14aa734: stur            w0, [x2, #7]
    // 0x14aa738: StoreField: r2->field_b = r0
    //     0x14aa738: stur            w0, [x2, #0xb]
    // 0x14aa73c: StoreField: r2->field_f = r0
    //     0x14aa73c: stur            w0, [x2, #0xf]
    // 0x14aa740: StoreField: r2->field_13 = r0
    //     0x14aa740: stur            w0, [x2, #0x13]
    // 0x14aa744: ldur            x0, [fp, #-8]
    // 0x14aa748: LoadField: r1 = r0->field_f
    //     0x14aa748: ldur            w1, [x0, #0xf]
    // 0x14aa74c: DecompressPointer r1
    //     0x14aa74c: add             x1, x1, HEAP, lsl #32
    // 0x14aa750: r0 = controller()
    //     0x14aa750: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aa754: LoadField: r1 = r0->field_97
    //     0x14aa754: ldur            w1, [x0, #0x97]
    // 0x14aa758: DecompressPointer r1
    //     0x14aa758: add             x1, x1, HEAP, lsl #32
    // 0x14aa75c: ldur            x2, [fp, #-0x10]
    // 0x14aa760: LoadField: r0 = r2->field_f
    //     0x14aa760: ldur            w0, [x2, #0xf]
    // 0x14aa764: DecompressPointer r0
    //     0x14aa764: add             x0, x0, HEAP, lsl #32
    // 0x14aa768: stur            x0, [fp, #-8]
    // 0x14aa76c: r0 = value()
    //     0x14aa76c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14aa770: r1 = LoadClassIdInstr(r0)
    //     0x14aa770: ldur            x1, [x0, #-1]
    //     0x14aa774: ubfx            x1, x1, #0xc, #0x14
    // 0x14aa778: ldur            x16, [fp, #-8]
    // 0x14aa77c: stp             x16, x0, [SP]
    // 0x14aa780: mov             x0, x1
    // 0x14aa784: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14aa784: sub             lr, x0, #0xb7
    //     0x14aa788: ldr             lr, [x21, lr, lsl #3]
    //     0x14aa78c: blr             lr
    // 0x14aa790: LoadField: r1 = r0->field_f
    //     0x14aa790: ldur            w1, [x0, #0xf]
    // 0x14aa794: DecompressPointer r1
    //     0x14aa794: add             x1, x1, HEAP, lsl #32
    // 0x14aa798: cmp             w1, NULL
    // 0x14aa79c: b.ne            #0x14aa7a8
    // 0x14aa7a0: r2 = ""
    //     0x14aa7a0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14aa7a4: b               #0x14aa7ac
    // 0x14aa7a8: mov             x2, x1
    // 0x14aa7ac: ldur            x1, [fp, #-0x18]
    // 0x14aa7b0: ldur            x0, [fp, #-0x28]
    // 0x14aa7b4: stur            x2, [fp, #-8]
    // 0x14aa7b8: r0 = Image()
    //     0x14aa7b8: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x14aa7bc: stur            x0, [fp, #-0x20]
    // 0x14aa7c0: r16 = 57.000000
    //     0x14aa7c0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x14aa7c4: ldr             x16, [x16, #0xb18]
    // 0x14aa7c8: r30 = 57.000000
    //     0x14aa7c8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x14aa7cc: ldr             lr, [lr, #0xb18]
    // 0x14aa7d0: stp             lr, x16, [SP, #8]
    // 0x14aa7d4: r16 = Instance_BoxFit
    //     0x14aa7d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14aa7d8: ldr             x16, [x16, #0x118]
    // 0x14aa7dc: str             x16, [SP]
    // 0x14aa7e0: mov             x1, x0
    // 0x14aa7e4: ldur            x2, [fp, #-8]
    // 0x14aa7e8: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0x14aa7e8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b48] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0x14aa7ec: ldr             x4, [x4, #0xb48]
    // 0x14aa7f0: r0 = Image.network()
    //     0x14aa7f0: bl              #0x802090  ; [package:flutter/src/widgets/image.dart] Image::Image.network
    // 0x14aa7f4: r0 = ClipRRect()
    //     0x14aa7f4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14aa7f8: mov             x2, x0
    // 0x14aa7fc: ldur            x0, [fp, #-0x28]
    // 0x14aa800: stur            x2, [fp, #-8]
    // 0x14aa804: StoreField: r2->field_f = r0
    //     0x14aa804: stur            w0, [x2, #0xf]
    // 0x14aa808: r0 = Instance_Clip
    //     0x14aa808: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14aa80c: ldr             x0, [x0, #0x138]
    // 0x14aa810: ArrayStore: r2[0] = r0  ; List_4
    //     0x14aa810: stur            w0, [x2, #0x17]
    // 0x14aa814: ldur            x0, [fp, #-0x20]
    // 0x14aa818: StoreField: r2->field_b = r0
    //     0x14aa818: stur            w0, [x2, #0xb]
    // 0x14aa81c: ldur            x0, [fp, #-0x18]
    // 0x14aa820: LoadField: r1 = r0->field_b
    //     0x14aa820: ldur            w1, [x0, #0xb]
    // 0x14aa824: LoadField: r3 = r0->field_f
    //     0x14aa824: ldur            w3, [x0, #0xf]
    // 0x14aa828: DecompressPointer r3
    //     0x14aa828: add             x3, x3, HEAP, lsl #32
    // 0x14aa82c: LoadField: r4 = r3->field_b
    //     0x14aa82c: ldur            w4, [x3, #0xb]
    // 0x14aa830: r3 = LoadInt32Instr(r1)
    //     0x14aa830: sbfx            x3, x1, #1, #0x1f
    // 0x14aa834: stur            x3, [fp, #-0x40]
    // 0x14aa838: r1 = LoadInt32Instr(r4)
    //     0x14aa838: sbfx            x1, x4, #1, #0x1f
    // 0x14aa83c: cmp             x3, x1
    // 0x14aa840: b.ne            #0x14aa84c
    // 0x14aa844: mov             x1, x0
    // 0x14aa848: r0 = _growToNextCapacity()
    //     0x14aa848: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14aa84c: ldur            x2, [fp, #-0x18]
    // 0x14aa850: ldur            x3, [fp, #-0x40]
    // 0x14aa854: add             x0, x3, #1
    // 0x14aa858: lsl             x1, x0, #1
    // 0x14aa85c: StoreField: r2->field_b = r1
    //     0x14aa85c: stur            w1, [x2, #0xb]
    // 0x14aa860: LoadField: r1 = r2->field_f
    //     0x14aa860: ldur            w1, [x2, #0xf]
    // 0x14aa864: DecompressPointer r1
    //     0x14aa864: add             x1, x1, HEAP, lsl #32
    // 0x14aa868: ldur            x0, [fp, #-8]
    // 0x14aa86c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14aa86c: add             x25, x1, x3, lsl #2
    //     0x14aa870: add             x25, x25, #0xf
    //     0x14aa874: str             w0, [x25]
    //     0x14aa878: tbz             w0, #0, #0x14aa894
    //     0x14aa87c: ldurb           w16, [x1, #-1]
    //     0x14aa880: ldurb           w17, [x0, #-1]
    //     0x14aa884: and             x16, x17, x16, lsr #2
    //     0x14aa888: tst             x16, HEAP, lsr #32
    //     0x14aa88c: b.eq            #0x14aa894
    //     0x14aa890: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14aa894: b               #0x14aa8a4
    // 0x14aa898: ldur            x2, [fp, #-0x18]
    // 0x14aa89c: b               #0x14aa8a4
    // 0x14aa8a0: ldur            x2, [fp, #-0x18]
    // 0x14aa8a4: ldr             x1, [fp, #0x18]
    // 0x14aa8a8: r0 = of()
    //     0x14aa8a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14aa8ac: LoadField: r2 = r0->field_5b
    //     0x14aa8ac: ldur            w2, [x0, #0x5b]
    // 0x14aa8b0: DecompressPointer r2
    //     0x14aa8b0: add             x2, x2, HEAP, lsl #32
    // 0x14aa8b4: r16 = 1.000000
    //     0x14aa8b4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14aa8b8: str             x16, [SP]
    // 0x14aa8bc: r1 = Null
    //     0x14aa8bc: mov             x1, NULL
    // 0x14aa8c0: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14aa8c0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14aa8c4: ldr             x4, [x4, #0x108]
    // 0x14aa8c8: r0 = Border.all()
    //     0x14aa8c8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14aa8cc: ldr             x1, [fp, #0x18]
    // 0x14aa8d0: stur            x0, [fp, #-8]
    // 0x14aa8d4: r0 = of()
    //     0x14aa8d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14aa8d8: LoadField: r1 = r0->field_5b
    //     0x14aa8d8: ldur            w1, [x0, #0x5b]
    // 0x14aa8dc: DecompressPointer r1
    //     0x14aa8dc: add             x1, x1, HEAP, lsl #32
    // 0x14aa8e0: stur            x1, [fp, #-0x20]
    // 0x14aa8e4: r0 = BoxDecoration()
    //     0x14aa8e4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14aa8e8: mov             x1, x0
    // 0x14aa8ec: ldur            x0, [fp, #-0x20]
    // 0x14aa8f0: stur            x1, [fp, #-0x28]
    // 0x14aa8f4: StoreField: r1->field_7 = r0
    //     0x14aa8f4: stur            w0, [x1, #7]
    // 0x14aa8f8: ldur            x0, [fp, #-8]
    // 0x14aa8fc: StoreField: r1->field_f = r0
    //     0x14aa8fc: stur            w0, [x1, #0xf]
    // 0x14aa900: r0 = Instance_BoxShape
    //     0x14aa900: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14aa904: ldr             x0, [x0, #0x970]
    // 0x14aa908: StoreField: r1->field_23 = r0
    //     0x14aa908: stur            w0, [x1, #0x23]
    // 0x14aa90c: r0 = Container()
    //     0x14aa90c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14aa910: stur            x0, [fp, #-8]
    // 0x14aa914: ldur            x16, [fp, #-0x28]
    // 0x14aa918: r30 = Instance_Icon
    //     0x14aa918: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b50] Obj!Icon@d669b1
    //     0x14aa91c: ldr             lr, [lr, #0xb50]
    // 0x14aa920: stp             lr, x16, [SP]
    // 0x14aa924: mov             x1, x0
    // 0x14aa928: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14aa928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14aa92c: ldr             x4, [x4, #0x88]
    // 0x14aa930: r0 = Container()
    //     0x14aa930: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14aa934: r0 = GestureDetector()
    //     0x14aa934: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14aa938: ldur            x2, [fp, #-0x10]
    // 0x14aa93c: r1 = Function '<anonymous closure>':.
    //     0x14aa93c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43060] AnonymousClosure: (0x140a92c), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x14aa940: ldr             x1, [x1, #0x60]
    // 0x14aa944: stur            x0, [fp, #-0x10]
    // 0x14aa948: r0 = AllocateClosure()
    //     0x14aa948: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14aa94c: ldur            x16, [fp, #-8]
    // 0x14aa950: stp             x16, x0, [SP]
    // 0x14aa954: ldur            x1, [fp, #-0x10]
    // 0x14aa958: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x14aa958: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x14aa95c: ldr             x4, [x4, #0xaf0]
    // 0x14aa960: r0 = GestureDetector()
    //     0x14aa960: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14aa964: r1 = <StackParentData>
    //     0x14aa964: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14aa968: ldr             x1, [x1, #0x8e0]
    // 0x14aa96c: r0 = Positioned()
    //     0x14aa96c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14aa970: mov             x2, x0
    // 0x14aa974: ldur            x0, [fp, #-0x10]
    // 0x14aa978: stur            x2, [fp, #-8]
    // 0x14aa97c: StoreField: r2->field_b = r0
    //     0x14aa97c: stur            w0, [x2, #0xb]
    // 0x14aa980: ldur            x0, [fp, #-0x18]
    // 0x14aa984: LoadField: r1 = r0->field_b
    //     0x14aa984: ldur            w1, [x0, #0xb]
    // 0x14aa988: LoadField: r3 = r0->field_f
    //     0x14aa988: ldur            w3, [x0, #0xf]
    // 0x14aa98c: DecompressPointer r3
    //     0x14aa98c: add             x3, x3, HEAP, lsl #32
    // 0x14aa990: LoadField: r4 = r3->field_b
    //     0x14aa990: ldur            w4, [x3, #0xb]
    // 0x14aa994: r3 = LoadInt32Instr(r1)
    //     0x14aa994: sbfx            x3, x1, #1, #0x1f
    // 0x14aa998: stur            x3, [fp, #-0x40]
    // 0x14aa99c: r1 = LoadInt32Instr(r4)
    //     0x14aa99c: sbfx            x1, x4, #1, #0x1f
    // 0x14aa9a0: cmp             x3, x1
    // 0x14aa9a4: b.ne            #0x14aa9b0
    // 0x14aa9a8: mov             x1, x0
    // 0x14aa9ac: r0 = _growToNextCapacity()
    //     0x14aa9ac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14aa9b0: ldur            x2, [fp, #-0x18]
    // 0x14aa9b4: ldur            x3, [fp, #-0x40]
    // 0x14aa9b8: add             x0, x3, #1
    // 0x14aa9bc: lsl             x1, x0, #1
    // 0x14aa9c0: StoreField: r2->field_b = r1
    //     0x14aa9c0: stur            w1, [x2, #0xb]
    // 0x14aa9c4: LoadField: r1 = r2->field_f
    //     0x14aa9c4: ldur            w1, [x2, #0xf]
    // 0x14aa9c8: DecompressPointer r1
    //     0x14aa9c8: add             x1, x1, HEAP, lsl #32
    // 0x14aa9cc: ldur            x0, [fp, #-8]
    // 0x14aa9d0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14aa9d0: add             x25, x1, x3, lsl #2
    //     0x14aa9d4: add             x25, x25, #0xf
    //     0x14aa9d8: str             w0, [x25]
    //     0x14aa9dc: tbz             w0, #0, #0x14aa9f8
    //     0x14aa9e0: ldurb           w16, [x1, #-1]
    //     0x14aa9e4: ldurb           w17, [x0, #-1]
    //     0x14aa9e8: and             x16, x17, x16, lsr #2
    //     0x14aa9ec: tst             x16, HEAP, lsr #32
    //     0x14aa9f0: b.eq            #0x14aa9f8
    //     0x14aa9f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14aa9f8: r0 = Stack()
    //     0x14aa9f8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14aa9fc: mov             x1, x0
    // 0x14aaa00: r0 = Instance_Alignment
    //     0x14aaa00: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14aaa04: ldr             x0, [x0, #0x950]
    // 0x14aaa08: stur            x1, [fp, #-8]
    // 0x14aaa0c: StoreField: r1->field_f = r0
    //     0x14aaa0c: stur            w0, [x1, #0xf]
    // 0x14aaa10: r0 = Instance_StackFit
    //     0x14aaa10: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14aaa14: ldr             x0, [x0, #0xfa8]
    // 0x14aaa18: ArrayStore: r1[0] = r0  ; List_4
    //     0x14aaa18: stur            w0, [x1, #0x17]
    // 0x14aaa1c: r0 = Instance_Clip
    //     0x14aaa1c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14aaa20: ldr             x0, [x0, #0x7e0]
    // 0x14aaa24: StoreField: r1->field_1b = r0
    //     0x14aaa24: stur            w0, [x1, #0x1b]
    // 0x14aaa28: ldur            x0, [fp, #-0x18]
    // 0x14aaa2c: StoreField: r1->field_b = r0
    //     0x14aaa2c: stur            w0, [x1, #0xb]
    // 0x14aaa30: r0 = Padding()
    //     0x14aaa30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14aaa34: r1 = Instance_EdgeInsets
    //     0x14aaa34: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x14aaa38: ldr             x1, [x1, #0xdf8]
    // 0x14aaa3c: StoreField: r0->field_f = r1
    //     0x14aaa3c: stur            w1, [x0, #0xf]
    // 0x14aaa40: ldur            x1, [fp, #-8]
    // 0x14aaa44: StoreField: r0->field_b = r1
    //     0x14aaa44: stur            w1, [x0, #0xb]
    // 0x14aaa48: LeaveFrame
    //     0x14aaa48: mov             SP, fp
    //     0x14aaa4c: ldp             fp, lr, [SP], #0x10
    // 0x14aaa50: ret
    //     0x14aaa50: ret             
    // 0x14aaa54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14aaa54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14aaa58: b               #0x14a9df4
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x14aaa5c, size: 0x164
    // 0x14aaa5c: EnterFrame
    //     0x14aaa5c: stp             fp, lr, [SP, #-0x10]!
    //     0x14aaa60: mov             fp, SP
    // 0x14aaa64: AllocStack(0x20)
    //     0x14aaa64: sub             SP, SP, #0x20
    // 0x14aaa68: SetupParameters()
    //     0x14aaa68: ldr             x0, [fp, #0x10]
    //     0x14aaa6c: ldur            w2, [x0, #0x17]
    //     0x14aaa70: add             x2, x2, HEAP, lsl #32
    //     0x14aaa74: stur            x2, [fp, #-8]
    // 0x14aaa78: CheckStackOverflow
    //     0x14aaa78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14aaa7c: cmp             SP, x16
    //     0x14aaa80: b.ls            #0x14aabb4
    // 0x14aaa84: LoadField: r1 = r2->field_f
    //     0x14aaa84: ldur            w1, [x2, #0xf]
    // 0x14aaa88: DecompressPointer r1
    //     0x14aaa88: add             x1, x1, HEAP, lsl #32
    // 0x14aaa8c: r0 = controller()
    //     0x14aaa8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aaa90: LoadField: r1 = r0->field_db
    //     0x14aaa90: ldur            w1, [x0, #0xdb]
    // 0x14aaa94: DecompressPointer r1
    //     0x14aaa94: add             x1, x1, HEAP, lsl #32
    // 0x14aaa98: r0 = value()
    //     0x14aaa98: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14aaa9c: tbnz            w0, #4, #0x14aaba4
    // 0x14aaaa0: ldur            x2, [fp, #-8]
    // 0x14aaaa4: LoadField: r1 = r2->field_f
    //     0x14aaaa4: ldur            w1, [x2, #0xf]
    // 0x14aaaa8: DecompressPointer r1
    //     0x14aaaa8: add             x1, x1, HEAP, lsl #32
    // 0x14aaaac: r0 = controller()
    //     0x14aaaac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aaab0: LoadField: r1 = r0->field_db
    //     0x14aaab0: ldur            w1, [x0, #0xdb]
    // 0x14aaab4: DecompressPointer r1
    //     0x14aaab4: add             x1, x1, HEAP, lsl #32
    // 0x14aaab8: r2 = false
    //     0x14aaab8: add             x2, NULL, #0x30  ; false
    // 0x14aaabc: r0 = value=()
    //     0x14aaabc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14aaac0: r0 = LoadStaticField(0x878)
    //     0x14aaac0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14aaac4: ldr             x0, [x0, #0x10f0]
    // 0x14aaac8: cmp             w0, NULL
    // 0x14aaacc: b.eq            #0x14aabbc
    // 0x14aaad0: LoadField: r3 = r0->field_53
    //     0x14aaad0: ldur            w3, [x0, #0x53]
    // 0x14aaad4: DecompressPointer r3
    //     0x14aaad4: add             x3, x3, HEAP, lsl #32
    // 0x14aaad8: stur            x3, [fp, #-0x18]
    // 0x14aaadc: LoadField: r0 = r3->field_7
    //     0x14aaadc: ldur            w0, [x3, #7]
    // 0x14aaae0: DecompressPointer r0
    //     0x14aaae0: add             x0, x0, HEAP, lsl #32
    // 0x14aaae4: ldur            x2, [fp, #-8]
    // 0x14aaae8: stur            x0, [fp, #-0x10]
    // 0x14aaaec: r1 = Function '<anonymous closure>':.
    //     0x14aaaec: add             x1, PP, #0x43, lsl #12  ; [pp+0x43068] AnonymousClosure: (0x14aabc0), in [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14a8868)
    //     0x14aaaf0: ldr             x1, [x1, #0x68]
    // 0x14aaaf4: r0 = AllocateClosure()
    //     0x14aaaf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14aaaf8: ldur            x2, [fp, #-0x10]
    // 0x14aaafc: mov             x3, x0
    // 0x14aab00: r1 = Null
    //     0x14aab00: mov             x1, NULL
    // 0x14aab04: stur            x3, [fp, #-8]
    // 0x14aab08: cmp             w2, NULL
    // 0x14aab0c: b.eq            #0x14aab2c
    // 0x14aab10: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x14aab10: ldur            w4, [x2, #0x17]
    // 0x14aab14: DecompressPointer r4
    //     0x14aab14: add             x4, x4, HEAP, lsl #32
    // 0x14aab18: r8 = X0
    //     0x14aab18: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x14aab1c: LoadField: r9 = r4->field_7
    //     0x14aab1c: ldur            x9, [x4, #7]
    // 0x14aab20: r3 = Null
    //     0x14aab20: add             x3, PP, #0x43, lsl #12  ; [pp+0x43070] Null
    //     0x14aab24: ldr             x3, [x3, #0x70]
    // 0x14aab28: blr             x9
    // 0x14aab2c: ldur            x0, [fp, #-0x18]
    // 0x14aab30: LoadField: r1 = r0->field_b
    //     0x14aab30: ldur            w1, [x0, #0xb]
    // 0x14aab34: LoadField: r2 = r0->field_f
    //     0x14aab34: ldur            w2, [x0, #0xf]
    // 0x14aab38: DecompressPointer r2
    //     0x14aab38: add             x2, x2, HEAP, lsl #32
    // 0x14aab3c: LoadField: r3 = r2->field_b
    //     0x14aab3c: ldur            w3, [x2, #0xb]
    // 0x14aab40: r2 = LoadInt32Instr(r1)
    //     0x14aab40: sbfx            x2, x1, #1, #0x1f
    // 0x14aab44: stur            x2, [fp, #-0x20]
    // 0x14aab48: r1 = LoadInt32Instr(r3)
    //     0x14aab48: sbfx            x1, x3, #1, #0x1f
    // 0x14aab4c: cmp             x2, x1
    // 0x14aab50: b.ne            #0x14aab5c
    // 0x14aab54: mov             x1, x0
    // 0x14aab58: r0 = _growToNextCapacity()
    //     0x14aab58: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14aab5c: ldur            x2, [fp, #-0x18]
    // 0x14aab60: ldur            x3, [fp, #-0x20]
    // 0x14aab64: add             x4, x3, #1
    // 0x14aab68: lsl             x5, x4, #1
    // 0x14aab6c: StoreField: r2->field_b = r5
    //     0x14aab6c: stur            w5, [x2, #0xb]
    // 0x14aab70: LoadField: r1 = r2->field_f
    //     0x14aab70: ldur            w1, [x2, #0xf]
    // 0x14aab74: DecompressPointer r1
    //     0x14aab74: add             x1, x1, HEAP, lsl #32
    // 0x14aab78: ldur            x0, [fp, #-8]
    // 0x14aab7c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14aab7c: add             x25, x1, x3, lsl #2
    //     0x14aab80: add             x25, x25, #0xf
    //     0x14aab84: str             w0, [x25]
    //     0x14aab88: tbz             w0, #0, #0x14aaba4
    //     0x14aab8c: ldurb           w16, [x1, #-1]
    //     0x14aab90: ldurb           w17, [x0, #-1]
    //     0x14aab94: and             x16, x17, x16, lsr #2
    //     0x14aab98: tst             x16, HEAP, lsr #32
    //     0x14aab9c: b.eq            #0x14aaba4
    //     0x14aaba0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14aaba4: r0 = Instance_SizedBox
    //     0x14aaba4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14aaba8: LeaveFrame
    //     0x14aaba8: mov             SP, fp
    //     0x14aabac: ldp             fp, lr, [SP], #0x10
    // 0x14aabb0: ret
    //     0x14aabb0: ret             
    // 0x14aabb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14aabb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14aabb8: b               #0x14aaa84
    // 0x14aabbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14aabbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x14aabc0, size: 0x48
    // 0x14aabc0: EnterFrame
    //     0x14aabc0: stp             fp, lr, [SP, #-0x10]!
    //     0x14aabc4: mov             fp, SP
    // 0x14aabc8: ldr             x0, [fp, #0x18]
    // 0x14aabcc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14aabcc: ldur            w1, [x0, #0x17]
    // 0x14aabd0: DecompressPointer r1
    //     0x14aabd0: add             x1, x1, HEAP, lsl #32
    // 0x14aabd4: CheckStackOverflow
    //     0x14aabd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14aabd8: cmp             SP, x16
    //     0x14aabdc: b.ls            #0x14aac00
    // 0x14aabe0: LoadField: r0 = r1->field_f
    //     0x14aabe0: ldur            w0, [x1, #0xf]
    // 0x14aabe4: DecompressPointer r0
    //     0x14aabe4: add             x0, x0, HEAP, lsl #32
    // 0x14aabe8: mov             x1, x0
    // 0x14aabec: r0 = _showRatingSuccessBottomSheet()
    //     0x14aabec: bl              #0x14aac08  ; [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet
    // 0x14aabf0: r0 = Null
    //     0x14aabf0: mov             x0, NULL
    // 0x14aabf4: LeaveFrame
    //     0x14aabf4: mov             SP, fp
    //     0x14aabf8: ldp             fp, lr, [SP], #0x10
    // 0x14aabfc: ret
    //     0x14aabfc: ret             
    // 0x14aac00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14aac00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14aac04: b               #0x14aabe0
  }
  _ _showRatingSuccessBottomSheet(/* No info */) {
    // ** addr: 0x14aac08, size: 0xc0
    // 0x14aac08: EnterFrame
    //     0x14aac08: stp             fp, lr, [SP, #-0x10]!
    //     0x14aac0c: mov             fp, SP
    // 0x14aac10: AllocStack(0x38)
    //     0x14aac10: sub             SP, SP, #0x38
    // 0x14aac14: SetupParameters(RatingReviewOrderPage this /* r1 => r1, fp-0x8 */)
    //     0x14aac14: stur            x1, [fp, #-8]
    // 0x14aac18: CheckStackOverflow
    //     0x14aac18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14aac1c: cmp             SP, x16
    //     0x14aac20: b.ls            #0x14aacbc
    // 0x14aac24: r1 = 1
    //     0x14aac24: movz            x1, #0x1
    // 0x14aac28: r0 = AllocateContext()
    //     0x14aac28: bl              #0x16f6108  ; AllocateContextStub
    // 0x14aac2c: ldur            x1, [fp, #-8]
    // 0x14aac30: stur            x0, [fp, #-0x10]
    // 0x14aac34: StoreField: r0->field_f = r1
    //     0x14aac34: stur            w1, [x0, #0xf]
    // 0x14aac38: r0 = controller()
    //     0x14aac38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aac3c: mov             x1, x0
    // 0x14aac40: r0 = false
    //     0x14aac40: add             x0, NULL, #0x30  ; false
    // 0x14aac44: StoreField: r1->field_c7 = r0
    //     0x14aac44: stur            w0, [x1, #0xc7]
    // 0x14aac48: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14aac48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14aac4c: ldr             x0, [x0, #0x1c80]
    //     0x14aac50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14aac54: cmp             w0, w16
    //     0x14aac58: b.ne            #0x14aac64
    //     0x14aac5c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14aac60: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14aac64: r0 = GetNavigation.context()
    //     0x14aac64: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x14aac68: stur            x0, [fp, #-8]
    // 0x14aac6c: cmp             w0, NULL
    // 0x14aac70: b.eq            #0x14aacc4
    // 0x14aac74: ldur            x2, [fp, #-0x10]
    // 0x14aac78: r1 = Function '<anonymous closure>':.
    //     0x14aac78: add             x1, PP, #0x43, lsl #12  ; [pp+0x43080] AnonymousClosure: (0x14aacc8), in [package:customer_app/app/presentation/views/cosmetic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet (0x14aac08)
    //     0x14aac7c: ldr             x1, [x1, #0x80]
    // 0x14aac80: r0 = AllocateClosure()
    //     0x14aac80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14aac84: stp             x0, NULL, [SP, #0x18]
    // 0x14aac88: ldur            x16, [fp, #-8]
    // 0x14aac8c: r30 = true
    //     0x14aac8c: add             lr, NULL, #0x20  ; true
    // 0x14aac90: stp             lr, x16, [SP, #8]
    // 0x14aac94: r16 = Instance_Color
    //     0x14aac94: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14aac98: ldr             x16, [x16, #0xf88]
    // 0x14aac9c: str             x16, [SP]
    // 0x14aaca0: r4 = const [0x1, 0x4, 0x4, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, null]
    //     0x14aaca0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36be8] List(9) [0x1, 0x4, 0x4, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, Null]
    //     0x14aaca4: ldr             x4, [x4, #0xbe8]
    // 0x14aaca8: r0 = showModalBottomSheet()
    //     0x14aaca8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14aacac: r0 = Null
    //     0x14aacac: mov             x0, NULL
    // 0x14aacb0: LeaveFrame
    //     0x14aacb0: mov             SP, fp
    //     0x14aacb4: ldp             fp, lr, [SP], #0x10
    // 0x14aacb8: ret
    //     0x14aacb8: ret             
    // 0x14aacbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14aacbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14aacc0: b               #0x14aac24
    // 0x14aacc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14aacc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewSuccessBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14aacc8, size: 0x110
    // 0x14aacc8: EnterFrame
    //     0x14aacc8: stp             fp, lr, [SP, #-0x10]!
    //     0x14aaccc: mov             fp, SP
    // 0x14aacd0: AllocStack(0x30)
    //     0x14aacd0: sub             SP, SP, #0x30
    // 0x14aacd4: SetupParameters()
    //     0x14aacd4: ldr             x0, [fp, #0x18]
    //     0x14aacd8: ldur            w2, [x0, #0x17]
    //     0x14aacdc: add             x2, x2, HEAP, lsl #32
    //     0x14aace0: stur            x2, [fp, #-8]
    // 0x14aace4: CheckStackOverflow
    //     0x14aace4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14aace8: cmp             SP, x16
    //     0x14aacec: b.ls            #0x14aadd0
    // 0x14aacf0: LoadField: r1 = r2->field_f
    //     0x14aacf0: ldur            w1, [x2, #0xf]
    // 0x14aacf4: DecompressPointer r1
    //     0x14aacf4: add             x1, x1, HEAP, lsl #32
    // 0x14aacf8: r0 = controller()
    //     0x14aacf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aacfc: LoadField: r1 = r0->field_4b
    //     0x14aacfc: ldur            w1, [x0, #0x4b]
    // 0x14aad00: DecompressPointer r1
    //     0x14aad00: add             x1, x1, HEAP, lsl #32
    // 0x14aad04: r0 = value()
    //     0x14aad04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14aad08: LoadField: r1 = r0->field_b
    //     0x14aad08: ldur            w1, [x0, #0xb]
    // 0x14aad0c: DecompressPointer r1
    //     0x14aad0c: add             x1, x1, HEAP, lsl #32
    // 0x14aad10: cmp             w1, NULL
    // 0x14aad14: b.ne            #0x14aad20
    // 0x14aad18: r0 = Null
    //     0x14aad18: mov             x0, NULL
    // 0x14aad1c: b               #0x14aad58
    // 0x14aad20: LoadField: r0 = r1->field_1f
    //     0x14aad20: ldur            w0, [x1, #0x1f]
    // 0x14aad24: DecompressPointer r0
    //     0x14aad24: add             x0, x0, HEAP, lsl #32
    // 0x14aad28: cmp             w0, NULL
    // 0x14aad2c: b.ne            #0x14aad38
    // 0x14aad30: r0 = Null
    //     0x14aad30: mov             x0, NULL
    // 0x14aad34: b               #0x14aad58
    // 0x14aad38: LoadField: r1 = r0->field_13
    //     0x14aad38: ldur            w1, [x0, #0x13]
    // 0x14aad3c: DecompressPointer r1
    //     0x14aad3c: add             x1, x1, HEAP, lsl #32
    // 0x14aad40: cmp             w1, NULL
    // 0x14aad44: b.ne            #0x14aad50
    // 0x14aad48: r0 = Null
    //     0x14aad48: mov             x0, NULL
    // 0x14aad4c: b               #0x14aad58
    // 0x14aad50: stp             x1, NULL, [SP]
    // 0x14aad54: r0 = _Double.fromInteger()
    //     0x14aad54: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x14aad58: cmp             w0, NULL
    // 0x14aad5c: b.ne            #0x14aad68
    // 0x14aad60: d0 = 0.000000
    //     0x14aad60: eor             v0.16b, v0.16b, v0.16b
    // 0x14aad64: b               #0x14aad6c
    // 0x14aad68: LoadField: d0 = r0->field_7
    //     0x14aad68: ldur            d0, [x0, #7]
    // 0x14aad6c: ldur            x2, [fp, #-8]
    // 0x14aad70: stur            d0, [fp, #-0x20]
    // 0x14aad74: LoadField: r1 = r2->field_f
    //     0x14aad74: ldur            w1, [x2, #0xf]
    // 0x14aad78: DecompressPointer r1
    //     0x14aad78: add             x1, x1, HEAP, lsl #32
    // 0x14aad7c: r0 = controller()
    //     0x14aad7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14aad80: LoadField: r1 = r0->field_4b
    //     0x14aad80: ldur            w1, [x0, #0x4b]
    // 0x14aad84: DecompressPointer r1
    //     0x14aad84: add             x1, x1, HEAP, lsl #32
    // 0x14aad88: r0 = value()
    //     0x14aad88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14aad8c: stur            x0, [fp, #-0x10]
    // 0x14aad90: r0 = RatingReviewSuccessBottomSheet()
    //     0x14aad90: bl              #0x140e25c  ; AllocateRatingReviewSuccessBottomSheetStub -> RatingReviewSuccessBottomSheet (size=0x1c)
    // 0x14aad94: ldur            d0, [fp, #-0x20]
    // 0x14aad98: stur            x0, [fp, #-0x18]
    // 0x14aad9c: StoreField: r0->field_b = d0
    //     0x14aad9c: stur            d0, [x0, #0xb]
    // 0x14aada0: ldur            x1, [fp, #-0x10]
    // 0x14aada4: StoreField: r0->field_13 = r1
    //     0x14aada4: stur            w1, [x0, #0x13]
    // 0x14aada8: ldur            x2, [fp, #-8]
    // 0x14aadac: r1 = Function '<anonymous closure>':.
    //     0x14aadac: add             x1, PP, #0x43, lsl #12  ; [pp+0x43088] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x14aadb0: ldr             x1, [x1, #0x88]
    // 0x14aadb4: r0 = AllocateClosure()
    //     0x14aadb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14aadb8: mov             x1, x0
    // 0x14aadbc: ldur            x0, [fp, #-0x18]
    // 0x14aadc0: ArrayStore: r0[0] = r1  ; List_4
    //     0x14aadc0: stur            w1, [x0, #0x17]
    // 0x14aadc4: LeaveFrame
    //     0x14aadc4: mov             SP, fp
    //     0x14aadc8: ldp             fp, lr, [SP], #0x10
    // 0x14aadcc: ret
    //     0x14aadcc: ret             
    // 0x14aadd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14aadd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14aadd4: b               #0x14aacf0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dc420, size: 0x1bc
    // 0x15dc420: EnterFrame
    //     0x15dc420: stp             fp, lr, [SP, #-0x10]!
    //     0x15dc424: mov             fp, SP
    // 0x15dc428: AllocStack(0x30)
    //     0x15dc428: sub             SP, SP, #0x30
    // 0x15dc42c: SetupParameters(RatingReviewOrderPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15dc42c: mov             x0, x1
    //     0x15dc430: stur            x1, [fp, #-8]
    //     0x15dc434: mov             x1, x2
    //     0x15dc438: stur            x2, [fp, #-0x10]
    // 0x15dc43c: CheckStackOverflow
    //     0x15dc43c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dc440: cmp             SP, x16
    //     0x15dc444: b.ls            #0x15dc5d4
    // 0x15dc448: r1 = 1
    //     0x15dc448: movz            x1, #0x1
    // 0x15dc44c: r0 = AllocateContext()
    //     0x15dc44c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15dc450: mov             x2, x0
    // 0x15dc454: ldur            x0, [fp, #-8]
    // 0x15dc458: stur            x2, [fp, #-0x18]
    // 0x15dc45c: StoreField: r2->field_f = r0
    //     0x15dc45c: stur            w0, [x2, #0xf]
    // 0x15dc460: ldur            x1, [fp, #-0x10]
    // 0x15dc464: r0 = of()
    //     0x15dc464: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dc468: LoadField: r1 = r0->field_87
    //     0x15dc468: ldur            w1, [x0, #0x87]
    // 0x15dc46c: DecompressPointer r1
    //     0x15dc46c: add             x1, x1, HEAP, lsl #32
    // 0x15dc470: LoadField: r0 = r1->field_7
    //     0x15dc470: ldur            w0, [x1, #7]
    // 0x15dc474: DecompressPointer r0
    //     0x15dc474: add             x0, x0, HEAP, lsl #32
    // 0x15dc478: r16 = Instance_Color
    //     0x15dc478: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15dc47c: r30 = 16.000000
    //     0x15dc47c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15dc480: ldr             lr, [lr, #0x188]
    // 0x15dc484: stp             lr, x16, [SP]
    // 0x15dc488: mov             x1, x0
    // 0x15dc48c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15dc48c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15dc490: ldr             x4, [x4, #0x9b8]
    // 0x15dc494: r0 = copyWith()
    //     0x15dc494: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15dc498: stur            x0, [fp, #-8]
    // 0x15dc49c: r0 = Text()
    //     0x15dc49c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15dc4a0: mov             x2, x0
    // 0x15dc4a4: r0 = "Review Product"
    //     0x15dc4a4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c20] "Review Product"
    //     0x15dc4a8: ldr             x0, [x0, #0xc20]
    // 0x15dc4ac: stur            x2, [fp, #-0x20]
    // 0x15dc4b0: StoreField: r2->field_b = r0
    //     0x15dc4b0: stur            w0, [x2, #0xb]
    // 0x15dc4b4: ldur            x0, [fp, #-8]
    // 0x15dc4b8: StoreField: r2->field_13 = r0
    //     0x15dc4b8: stur            w0, [x2, #0x13]
    // 0x15dc4bc: ldur            x1, [fp, #-0x10]
    // 0x15dc4c0: r0 = of()
    //     0x15dc4c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dc4c4: LoadField: r1 = r0->field_5b
    //     0x15dc4c4: ldur            w1, [x0, #0x5b]
    // 0x15dc4c8: DecompressPointer r1
    //     0x15dc4c8: add             x1, x1, HEAP, lsl #32
    // 0x15dc4cc: stur            x1, [fp, #-8]
    // 0x15dc4d0: r0 = ColorFilter()
    //     0x15dc4d0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dc4d4: mov             x1, x0
    // 0x15dc4d8: ldur            x0, [fp, #-8]
    // 0x15dc4dc: stur            x1, [fp, #-0x10]
    // 0x15dc4e0: StoreField: r1->field_7 = r0
    //     0x15dc4e0: stur            w0, [x1, #7]
    // 0x15dc4e4: r0 = Instance_BlendMode
    //     0x15dc4e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dc4e8: ldr             x0, [x0, #0xb30]
    // 0x15dc4ec: StoreField: r1->field_b = r0
    //     0x15dc4ec: stur            w0, [x1, #0xb]
    // 0x15dc4f0: r0 = 1
    //     0x15dc4f0: movz            x0, #0x1
    // 0x15dc4f4: StoreField: r1->field_13 = r0
    //     0x15dc4f4: stur            x0, [x1, #0x13]
    // 0x15dc4f8: r0 = SvgPicture()
    //     0x15dc4f8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dc4fc: stur            x0, [fp, #-8]
    // 0x15dc500: ldur            x16, [fp, #-0x10]
    // 0x15dc504: str             x16, [SP]
    // 0x15dc508: mov             x1, x0
    // 0x15dc50c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15dc50c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15dc510: ldr             x2, [x2, #0xa40]
    // 0x15dc514: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dc514: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dc518: ldr             x4, [x4, #0xa38]
    // 0x15dc51c: r0 = SvgPicture.asset()
    //     0x15dc51c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dc520: r0 = Align()
    //     0x15dc520: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dc524: mov             x1, x0
    // 0x15dc528: r0 = Instance_Alignment
    //     0x15dc528: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dc52c: ldr             x0, [x0, #0xb10]
    // 0x15dc530: stur            x1, [fp, #-0x10]
    // 0x15dc534: StoreField: r1->field_f = r0
    //     0x15dc534: stur            w0, [x1, #0xf]
    // 0x15dc538: r0 = 1.000000
    //     0x15dc538: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dc53c: StoreField: r1->field_13 = r0
    //     0x15dc53c: stur            w0, [x1, #0x13]
    // 0x15dc540: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dc540: stur            w0, [x1, #0x17]
    // 0x15dc544: ldur            x0, [fp, #-8]
    // 0x15dc548: StoreField: r1->field_b = r0
    //     0x15dc548: stur            w0, [x1, #0xb]
    // 0x15dc54c: r0 = InkWell()
    //     0x15dc54c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dc550: mov             x3, x0
    // 0x15dc554: ldur            x0, [fp, #-0x10]
    // 0x15dc558: stur            x3, [fp, #-8]
    // 0x15dc55c: StoreField: r3->field_b = r0
    //     0x15dc55c: stur            w0, [x3, #0xb]
    // 0x15dc560: ldur            x2, [fp, #-0x18]
    // 0x15dc564: r1 = Function '<anonymous closure>':.
    //     0x15dc564: add             x1, PP, #0x43, lsl #12  ; [pp+0x43090] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15dc568: ldr             x1, [x1, #0x90]
    // 0x15dc56c: r0 = AllocateClosure()
    //     0x15dc56c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dc570: ldur            x2, [fp, #-8]
    // 0x15dc574: StoreField: r2->field_f = r0
    //     0x15dc574: stur            w0, [x2, #0xf]
    // 0x15dc578: r0 = true
    //     0x15dc578: add             x0, NULL, #0x20  ; true
    // 0x15dc57c: StoreField: r2->field_43 = r0
    //     0x15dc57c: stur            w0, [x2, #0x43]
    // 0x15dc580: r1 = Instance_BoxShape
    //     0x15dc580: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dc584: ldr             x1, [x1, #0x80]
    // 0x15dc588: StoreField: r2->field_47 = r1
    //     0x15dc588: stur            w1, [x2, #0x47]
    // 0x15dc58c: StoreField: r2->field_6f = r0
    //     0x15dc58c: stur            w0, [x2, #0x6f]
    // 0x15dc590: r1 = false
    //     0x15dc590: add             x1, NULL, #0x30  ; false
    // 0x15dc594: StoreField: r2->field_73 = r1
    //     0x15dc594: stur            w1, [x2, #0x73]
    // 0x15dc598: StoreField: r2->field_83 = r0
    //     0x15dc598: stur            w0, [x2, #0x83]
    // 0x15dc59c: StoreField: r2->field_7b = r1
    //     0x15dc59c: stur            w1, [x2, #0x7b]
    // 0x15dc5a0: r0 = AppBar()
    //     0x15dc5a0: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dc5a4: stur            x0, [fp, #-0x10]
    // 0x15dc5a8: ldur            x16, [fp, #-0x20]
    // 0x15dc5ac: str             x16, [SP]
    // 0x15dc5b0: mov             x1, x0
    // 0x15dc5b4: ldur            x2, [fp, #-8]
    // 0x15dc5b8: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15dc5b8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15dc5bc: ldr             x4, [x4, #0xf00]
    // 0x15dc5c0: r0 = AppBar()
    //     0x15dc5c0: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dc5c4: ldur            x0, [fp, #-0x10]
    // 0x15dc5c8: LeaveFrame
    //     0x15dc5c8: mov             SP, fp
    //     0x15dc5cc: ldp             fp, lr, [SP], #0x10
    // 0x15dc5d0: ret
    //     0x15dc5d0: ret             
    // 0x15dc5d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dc5d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dc5d8: b               #0x15dc448
  }
}
