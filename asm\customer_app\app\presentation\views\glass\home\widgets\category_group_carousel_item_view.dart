// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart

// class id: 1049399, size: 0x8
class :: {
}

// class id: 3340, size: 0x2c, field offset: 0x14
class _CategoryGroupCarouselItemViewState extends State<dynamic> {

  late double _viewportHeight; // offset: 0x28
  late PageController _pageLineController; // offset: 0x14
  late TextStyle _titleStyle; // offset: 0x24
  late double _imageSize; // offset: 0x20

  _ build(/* No info */) {
    // ** addr: 0xb5df50, size: 0x3b4
    // 0xb5df50: EnterFrame
    //     0xb5df50: stp             fp, lr, [SP, #-0x10]!
    //     0xb5df54: mov             fp, SP
    // 0xb5df58: AllocStack(0x60)
    //     0xb5df58: sub             SP, SP, #0x60
    // 0xb5df5c: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb5df5c: mov             x0, x1
    //     0xb5df60: stur            x1, [fp, #-8]
    //     0xb5df64: mov             x1, x2
    //     0xb5df68: stur            x2, [fp, #-0x10]
    // 0xb5df6c: CheckStackOverflow
    //     0xb5df6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5df70: cmp             SP, x16
    //     0xb5df74: b.ls            #0xb5e2b0
    // 0xb5df78: r1 = 1
    //     0xb5df78: movz            x1, #0x1
    // 0xb5df7c: r0 = AllocateContext()
    //     0xb5df7c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5df80: mov             x3, x0
    // 0xb5df84: ldur            x0, [fp, #-8]
    // 0xb5df88: stur            x3, [fp, #-0x30]
    // 0xb5df8c: StoreField: r3->field_f = r0
    //     0xb5df8c: stur            w0, [x3, #0xf]
    // 0xb5df90: LoadField: r1 = r0->field_b
    //     0xb5df90: ldur            w1, [x0, #0xb]
    // 0xb5df94: DecompressPointer r1
    //     0xb5df94: add             x1, x1, HEAP, lsl #32
    // 0xb5df98: cmp             w1, NULL
    // 0xb5df9c: b.eq            #0xb5e2b8
    // 0xb5dfa0: LoadField: r2 = r1->field_b
    //     0xb5dfa0: ldur            w2, [x1, #0xb]
    // 0xb5dfa4: DecompressPointer r2
    //     0xb5dfa4: add             x2, x2, HEAP, lsl #32
    // 0xb5dfa8: cmp             w2, NULL
    // 0xb5dfac: b.ne            #0xb5dfb8
    // 0xb5dfb0: r1 = Null
    //     0xb5dfb0: mov             x1, NULL
    // 0xb5dfb4: b               #0xb5dfbc
    // 0xb5dfb8: LoadField: r1 = r2->field_b
    //     0xb5dfb8: ldur            w1, [x2, #0xb]
    // 0xb5dfbc: cmp             w1, NULL
    // 0xb5dfc0: b.ne            #0xb5dfcc
    // 0xb5dfc4: r1 = 0
    //     0xb5dfc4: movz            x1, #0
    // 0xb5dfc8: b               #0xb5dfd4
    // 0xb5dfcc: r2 = LoadInt32Instr(r1)
    //     0xb5dfcc: sbfx            x2, x1, #1, #0x1f
    // 0xb5dfd0: mov             x1, x2
    // 0xb5dfd4: d0 = 4.000000
    //     0xb5dfd4: fmov            d0, #4.00000000
    // 0xb5dfd8: scvtf           d1, x1
    // 0xb5dfdc: fdiv            d2, d1, d0
    // 0xb5dfe0: fcmp            d2, d2
    // 0xb5dfe4: b.vs            #0xb5e2bc
    // 0xb5dfe8: fcvtps          x4, d2
    // 0xb5dfec: asr             x16, x4, #0x1e
    // 0xb5dff0: cmp             x16, x4, asr #63
    // 0xb5dff4: b.ne            #0xb5e2bc
    // 0xb5dff8: lsl             x4, x4, #1
    // 0xb5dffc: stur            x4, [fp, #-0x28]
    // 0xb5e000: LoadField: r5 = r0->field_27
    //     0xb5e000: ldur            w5, [x0, #0x27]
    // 0xb5e004: DecompressPointer r5
    //     0xb5e004: add             x5, x5, HEAP, lsl #32
    // 0xb5e008: r16 = Sentinel
    //     0xb5e008: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5e00c: cmp             w5, w16
    // 0xb5e010: b.eq            #0xb5e2e8
    // 0xb5e014: stur            x5, [fp, #-0x20]
    // 0xb5e018: LoadField: r6 = r0->field_13
    //     0xb5e018: ldur            w6, [x0, #0x13]
    // 0xb5e01c: DecompressPointer r6
    //     0xb5e01c: add             x6, x6, HEAP, lsl #32
    // 0xb5e020: r16 = Sentinel
    //     0xb5e020: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5e024: cmp             w6, w16
    // 0xb5e028: b.eq            #0xb5e2f4
    // 0xb5e02c: mov             x2, x3
    // 0xb5e030: stur            x6, [fp, #-0x18]
    // 0xb5e034: r1 = Function '<anonymous closure>':.
    //     0xb5e034: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ed0] AnonymousClosure: (0xb5ea70), in [package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::build (0xb5df50)
    //     0xb5e038: ldr             x1, [x1, #0xed0]
    // 0xb5e03c: r0 = AllocateClosure()
    //     0xb5e03c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5e040: ldur            x2, [fp, #-0x30]
    // 0xb5e044: r1 = Function '<anonymous closure>':.
    //     0xb5e044: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ed8] AnonymousClosure: (0xb5e324), in [package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::build (0xb5df50)
    //     0xb5e048: ldr             x1, [x1, #0xed8]
    // 0xb5e04c: stur            x0, [fp, #-0x30]
    // 0xb5e050: r0 = AllocateClosure()
    //     0xb5e050: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5e054: stur            x0, [fp, #-0x38]
    // 0xb5e058: r0 = PageView()
    //     0xb5e058: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb5e05c: stur            x0, [fp, #-0x40]
    // 0xb5e060: r16 = Instance_BouncingScrollPhysics
    //     0xb5e060: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb5e064: ldr             x16, [x16, #0x890]
    // 0xb5e068: ldur            lr, [fp, #-0x18]
    // 0xb5e06c: stp             lr, x16, [SP]
    // 0xb5e070: mov             x1, x0
    // 0xb5e074: ldur            x2, [fp, #-0x38]
    // 0xb5e078: ldur            x3, [fp, #-0x28]
    // 0xb5e07c: ldur            x5, [fp, #-0x30]
    // 0xb5e080: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb5e080: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb5e084: ldr             x4, [x4, #0xe40]
    // 0xb5e088: r0 = PageView.builder()
    //     0xb5e088: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb5e08c: r0 = SizedBox()
    //     0xb5e08c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb5e090: mov             x3, x0
    // 0xb5e094: ldur            x0, [fp, #-0x20]
    // 0xb5e098: stur            x3, [fp, #-0x18]
    // 0xb5e09c: StoreField: r3->field_13 = r0
    //     0xb5e09c: stur            w0, [x3, #0x13]
    // 0xb5e0a0: ldur            x0, [fp, #-0x40]
    // 0xb5e0a4: StoreField: r3->field_b = r0
    //     0xb5e0a4: stur            w0, [x3, #0xb]
    // 0xb5e0a8: r1 = Null
    //     0xb5e0a8: mov             x1, NULL
    // 0xb5e0ac: r2 = 2
    //     0xb5e0ac: movz            x2, #0x2
    // 0xb5e0b0: r0 = AllocateArray()
    //     0xb5e0b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5e0b4: mov             x2, x0
    // 0xb5e0b8: ldur            x0, [fp, #-0x18]
    // 0xb5e0bc: stur            x2, [fp, #-0x20]
    // 0xb5e0c0: StoreField: r2->field_f = r0
    //     0xb5e0c0: stur            w0, [x2, #0xf]
    // 0xb5e0c4: r1 = <Widget>
    //     0xb5e0c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5e0c8: r0 = AllocateGrowableArray()
    //     0xb5e0c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5e0cc: mov             x2, x0
    // 0xb5e0d0: ldur            x0, [fp, #-0x20]
    // 0xb5e0d4: stur            x2, [fp, #-0x18]
    // 0xb5e0d8: StoreField: r2->field_f = r0
    //     0xb5e0d8: stur            w0, [x2, #0xf]
    // 0xb5e0dc: r0 = 2
    //     0xb5e0dc: movz            x0, #0x2
    // 0xb5e0e0: StoreField: r2->field_b = r0
    //     0xb5e0e0: stur            w0, [x2, #0xb]
    // 0xb5e0e4: ldur            x0, [fp, #-8]
    // 0xb5e0e8: LoadField: r1 = r0->field_b
    //     0xb5e0e8: ldur            w1, [x0, #0xb]
    // 0xb5e0ec: DecompressPointer r1
    //     0xb5e0ec: add             x1, x1, HEAP, lsl #32
    // 0xb5e0f0: cmp             w1, NULL
    // 0xb5e0f4: b.eq            #0xb5e300
    // 0xb5e0f8: LoadField: r3 = r1->field_b
    //     0xb5e0f8: ldur            w3, [x1, #0xb]
    // 0xb5e0fc: DecompressPointer r3
    //     0xb5e0fc: add             x3, x3, HEAP, lsl #32
    // 0xb5e100: cmp             w3, NULL
    // 0xb5e104: b.ne            #0xb5e110
    // 0xb5e108: r1 = Null
    //     0xb5e108: mov             x1, NULL
    // 0xb5e10c: b               #0xb5e114
    // 0xb5e110: LoadField: r1 = r3->field_b
    //     0xb5e110: ldur            w1, [x3, #0xb]
    // 0xb5e114: cmp             w1, NULL
    // 0xb5e118: b.ne            #0xb5e124
    // 0xb5e11c: r1 = 0
    //     0xb5e11c: movz            x1, #0
    // 0xb5e120: b               #0xb5e12c
    // 0xb5e124: r3 = LoadInt32Instr(r1)
    //     0xb5e124: sbfx            x3, x1, #1, #0x1f
    // 0xb5e128: mov             x1, x3
    // 0xb5e12c: cmp             x1, #4
    // 0xb5e130: b.le            #0xb5e230
    // 0xb5e134: ldur            x3, [fp, #-0x28]
    // 0xb5e138: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xb5e138: ldur            x4, [x0, #0x17]
    // 0xb5e13c: ldur            x1, [fp, #-0x10]
    // 0xb5e140: stur            x4, [fp, #-0x48]
    // 0xb5e144: r0 = of()
    //     0xb5e144: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5e148: LoadField: r1 = r0->field_5b
    //     0xb5e148: ldur            w1, [x0, #0x5b]
    // 0xb5e14c: DecompressPointer r1
    //     0xb5e14c: add             x1, x1, HEAP, lsl #32
    // 0xb5e150: ldur            x0, [fp, #-0x28]
    // 0xb5e154: stur            x1, [fp, #-8]
    // 0xb5e158: r2 = LoadInt32Instr(r0)
    //     0xb5e158: sbfx            x2, x0, #1, #0x1f
    //     0xb5e15c: tbz             w0, #0, #0xb5e164
    //     0xb5e160: ldur            x2, [x0, #7]
    // 0xb5e164: stur            x2, [fp, #-0x50]
    // 0xb5e168: r0 = CarouselIndicator()
    //     0xb5e168: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb5e16c: mov             x1, x0
    // 0xb5e170: ldur            x0, [fp, #-0x50]
    // 0xb5e174: stur            x1, [fp, #-0x10]
    // 0xb5e178: StoreField: r1->field_b = r0
    //     0xb5e178: stur            x0, [x1, #0xb]
    // 0xb5e17c: ldur            x0, [fp, #-0x48]
    // 0xb5e180: StoreField: r1->field_13 = r0
    //     0xb5e180: stur            x0, [x1, #0x13]
    // 0xb5e184: ldur            x0, [fp, #-8]
    // 0xb5e188: StoreField: r1->field_1b = r0
    //     0xb5e188: stur            w0, [x1, #0x1b]
    // 0xb5e18c: r0 = Instance_Color
    //     0xb5e18c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb5e190: ldr             x0, [x0, #0x90]
    // 0xb5e194: StoreField: r1->field_1f = r0
    //     0xb5e194: stur            w0, [x1, #0x1f]
    // 0xb5e198: r0 = Padding()
    //     0xb5e198: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5e19c: mov             x2, x0
    // 0xb5e1a0: r0 = Instance_EdgeInsets
    //     0xb5e1a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb5e1a4: ldr             x0, [x0, #0x990]
    // 0xb5e1a8: stur            x2, [fp, #-8]
    // 0xb5e1ac: StoreField: r2->field_f = r0
    //     0xb5e1ac: stur            w0, [x2, #0xf]
    // 0xb5e1b0: ldur            x0, [fp, #-0x10]
    // 0xb5e1b4: StoreField: r2->field_b = r0
    //     0xb5e1b4: stur            w0, [x2, #0xb]
    // 0xb5e1b8: ldur            x0, [fp, #-0x18]
    // 0xb5e1bc: LoadField: r1 = r0->field_b
    //     0xb5e1bc: ldur            w1, [x0, #0xb]
    // 0xb5e1c0: LoadField: r3 = r0->field_f
    //     0xb5e1c0: ldur            w3, [x0, #0xf]
    // 0xb5e1c4: DecompressPointer r3
    //     0xb5e1c4: add             x3, x3, HEAP, lsl #32
    // 0xb5e1c8: LoadField: r4 = r3->field_b
    //     0xb5e1c8: ldur            w4, [x3, #0xb]
    // 0xb5e1cc: r3 = LoadInt32Instr(r1)
    //     0xb5e1cc: sbfx            x3, x1, #1, #0x1f
    // 0xb5e1d0: stur            x3, [fp, #-0x48]
    // 0xb5e1d4: r1 = LoadInt32Instr(r4)
    //     0xb5e1d4: sbfx            x1, x4, #1, #0x1f
    // 0xb5e1d8: cmp             x3, x1
    // 0xb5e1dc: b.ne            #0xb5e1e8
    // 0xb5e1e0: mov             x1, x0
    // 0xb5e1e4: r0 = _growToNextCapacity()
    //     0xb5e1e4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5e1e8: ldur            x2, [fp, #-0x18]
    // 0xb5e1ec: ldur            x3, [fp, #-0x48]
    // 0xb5e1f0: add             x0, x3, #1
    // 0xb5e1f4: lsl             x1, x0, #1
    // 0xb5e1f8: StoreField: r2->field_b = r1
    //     0xb5e1f8: stur            w1, [x2, #0xb]
    // 0xb5e1fc: LoadField: r1 = r2->field_f
    //     0xb5e1fc: ldur            w1, [x2, #0xf]
    // 0xb5e200: DecompressPointer r1
    //     0xb5e200: add             x1, x1, HEAP, lsl #32
    // 0xb5e204: ldur            x0, [fp, #-8]
    // 0xb5e208: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5e208: add             x25, x1, x3, lsl #2
    //     0xb5e20c: add             x25, x25, #0xf
    //     0xb5e210: str             w0, [x25]
    //     0xb5e214: tbz             w0, #0, #0xb5e230
    //     0xb5e218: ldurb           w16, [x1, #-1]
    //     0xb5e21c: ldurb           w17, [x0, #-1]
    //     0xb5e220: and             x16, x17, x16, lsr #2
    //     0xb5e224: tst             x16, HEAP, lsr #32
    //     0xb5e228: b.eq            #0xb5e230
    //     0xb5e22c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5e230: r0 = Column()
    //     0xb5e230: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5e234: mov             x1, x0
    // 0xb5e238: r0 = Instance_Axis
    //     0xb5e238: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5e23c: stur            x1, [fp, #-8]
    // 0xb5e240: StoreField: r1->field_f = r0
    //     0xb5e240: stur            w0, [x1, #0xf]
    // 0xb5e244: r0 = Instance_MainAxisAlignment
    //     0xb5e244: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5e248: ldr             x0, [x0, #0xa08]
    // 0xb5e24c: StoreField: r1->field_13 = r0
    //     0xb5e24c: stur            w0, [x1, #0x13]
    // 0xb5e250: r0 = Instance_MainAxisSize
    //     0xb5e250: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5e254: ldr             x0, [x0, #0xa10]
    // 0xb5e258: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5e258: stur            w0, [x1, #0x17]
    // 0xb5e25c: r0 = Instance_CrossAxisAlignment
    //     0xb5e25c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5e260: ldr             x0, [x0, #0xa18]
    // 0xb5e264: StoreField: r1->field_1b = r0
    //     0xb5e264: stur            w0, [x1, #0x1b]
    // 0xb5e268: r0 = Instance_VerticalDirection
    //     0xb5e268: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5e26c: ldr             x0, [x0, #0xa20]
    // 0xb5e270: StoreField: r1->field_23 = r0
    //     0xb5e270: stur            w0, [x1, #0x23]
    // 0xb5e274: r0 = Instance_Clip
    //     0xb5e274: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5e278: ldr             x0, [x0, #0x38]
    // 0xb5e27c: StoreField: r1->field_2b = r0
    //     0xb5e27c: stur            w0, [x1, #0x2b]
    // 0xb5e280: StoreField: r1->field_2f = rZR
    //     0xb5e280: stur            xzr, [x1, #0x2f]
    // 0xb5e284: ldur            x0, [fp, #-0x18]
    // 0xb5e288: StoreField: r1->field_b = r0
    //     0xb5e288: stur            w0, [x1, #0xb]
    // 0xb5e28c: r0 = Padding()
    //     0xb5e28c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5e290: r1 = Instance_EdgeInsets
    //     0xb5e290: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb5e294: ldr             x1, [x1, #0x110]
    // 0xb5e298: StoreField: r0->field_f = r1
    //     0xb5e298: stur            w1, [x0, #0xf]
    // 0xb5e29c: ldur            x1, [fp, #-8]
    // 0xb5e2a0: StoreField: r0->field_b = r1
    //     0xb5e2a0: stur            w1, [x0, #0xb]
    // 0xb5e2a4: LeaveFrame
    //     0xb5e2a4: mov             SP, fp
    //     0xb5e2a8: ldp             fp, lr, [SP], #0x10
    // 0xb5e2ac: ret
    //     0xb5e2ac: ret             
    // 0xb5e2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5e2b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5e2b4: b               #0xb5df78
    // 0xb5e2b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5e2b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5e2bc: SaveReg d2
    //     0xb5e2bc: str             q2, [SP, #-0x10]!
    // 0xb5e2c0: stp             x0, x3, [SP, #-0x10]!
    // 0xb5e2c4: d0 = 0.000000
    //     0xb5e2c4: fmov            d0, d2
    // 0xb5e2c8: r0 = 64
    //     0xb5e2c8: movz            x0, #0x40
    // 0xb5e2cc: r30 = DoubleToIntegerStub
    //     0xb5e2cc: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb5e2d0: LoadField: r30 = r30->field_7
    //     0xb5e2d0: ldur            lr, [lr, #7]
    // 0xb5e2d4: blr             lr
    // 0xb5e2d8: mov             x4, x0
    // 0xb5e2dc: ldp             x0, x3, [SP], #0x10
    // 0xb5e2e0: RestoreReg d2
    //     0xb5e2e0: ldr             q2, [SP], #0x10
    // 0xb5e2e4: b               #0xb5dffc
    // 0xb5e2e8: r9 = _viewportHeight
    //     0xb5e2e8: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ee0] Field <_CategoryGroupCarouselItemViewState@1586474453._viewportHeight@1586474453>: late (offset: 0x28)
    //     0xb5e2ec: ldr             x9, [x9, #0xee0]
    // 0xb5e2f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb5e2f0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb5e2f4: r9 = _pageLineController
    //     0xb5e2f4: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ee8] Field <_CategoryGroupCarouselItemViewState@1586474453._pageLineController@1586474453>: late (offset: 0x14)
    //     0xb5e2f8: ldr             x9, [x9, #0xee8]
    // 0xb5e2fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb5e2fc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb5e300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5e300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RepaintBoundary <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb5e324, size: 0x68
    // 0xb5e324: EnterFrame
    //     0xb5e324: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e328: mov             fp, SP
    // 0xb5e32c: AllocStack(0x8)
    //     0xb5e32c: sub             SP, SP, #8
    // 0xb5e330: SetupParameters()
    //     0xb5e330: ldr             x0, [fp, #0x20]
    //     0xb5e334: ldur            w1, [x0, #0x17]
    //     0xb5e338: add             x1, x1, HEAP, lsl #32
    // 0xb5e33c: CheckStackOverflow
    //     0xb5e33c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5e340: cmp             SP, x16
    //     0xb5e344: b.ls            #0xb5e384
    // 0xb5e348: LoadField: r0 = r1->field_f
    //     0xb5e348: ldur            w0, [x1, #0xf]
    // 0xb5e34c: DecompressPointer r0
    //     0xb5e34c: add             x0, x0, HEAP, lsl #32
    // 0xb5e350: ldr             x1, [fp, #0x10]
    // 0xb5e354: r2 = LoadInt32Instr(r1)
    //     0xb5e354: sbfx            x2, x1, #1, #0x1f
    //     0xb5e358: tbz             w1, #0, #0xb5e360
    //     0xb5e35c: ldur            x2, [x1, #7]
    // 0xb5e360: mov             x1, x0
    // 0xb5e364: r0 = _buildCarouselPage()
    //     0xb5e364: bl              #0xb5e38c  ; [package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage
    // 0xb5e368: stur            x0, [fp, #-8]
    // 0xb5e36c: r0 = RepaintBoundary()
    //     0xb5e36c: bl              #0xa4360c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xb5e370: ldur            x1, [fp, #-8]
    // 0xb5e374: StoreField: r0->field_b = r1
    //     0xb5e374: stur            w1, [x0, #0xb]
    // 0xb5e378: LeaveFrame
    //     0xb5e378: mov             SP, fp
    //     0xb5e37c: ldp             fp, lr, [SP], #0x10
    // 0xb5e380: ret
    //     0xb5e380: ret             
    // 0xb5e384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5e384: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5e388: b               #0xb5e348
  }
  _ _buildCarouselPage(/* No info */) {
    // ** addr: 0xb5e38c, size: 0x1c0
    // 0xb5e38c: EnterFrame
    //     0xb5e38c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e390: mov             fp, SP
    // 0xb5e394: AllocStack(0x38)
    //     0xb5e394: sub             SP, SP, #0x38
    // 0xb5e398: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb5e398: stur            x1, [fp, #-8]
    //     0xb5e39c: stur            x2, [fp, #-0x10]
    // 0xb5e3a0: CheckStackOverflow
    //     0xb5e3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5e3a4: cmp             SP, x16
    //     0xb5e3a8: b.ls            #0xb5e53c
    // 0xb5e3ac: r1 = 1
    //     0xb5e3ac: movz            x1, #0x1
    // 0xb5e3b0: r0 = AllocateContext()
    //     0xb5e3b0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5e3b4: mov             x5, x0
    // 0xb5e3b8: ldur            x4, [fp, #-8]
    // 0xb5e3bc: stur            x5, [fp, #-0x20]
    // 0xb5e3c0: StoreField: r5->field_f = r4
    //     0xb5e3c0: stur            w4, [x5, #0xf]
    // 0xb5e3c4: ldur            x0, [fp, #-0x10]
    // 0xb5e3c8: lsl             x6, x0, #2
    // 0xb5e3cc: stur            x6, [fp, #-0x18]
    // 0xb5e3d0: add             x2, x6, #4
    // 0xb5e3d4: LoadField: r0 = r4->field_b
    //     0xb5e3d4: ldur            w0, [x4, #0xb]
    // 0xb5e3d8: DecompressPointer r0
    //     0xb5e3d8: add             x0, x0, HEAP, lsl #32
    // 0xb5e3dc: cmp             w0, NULL
    // 0xb5e3e0: b.eq            #0xb5e544
    // 0xb5e3e4: LoadField: r1 = r0->field_b
    //     0xb5e3e4: ldur            w1, [x0, #0xb]
    // 0xb5e3e8: DecompressPointer r1
    //     0xb5e3e8: add             x1, x1, HEAP, lsl #32
    // 0xb5e3ec: cmp             w1, NULL
    // 0xb5e3f0: b.ne            #0xb5e3fc
    // 0xb5e3f4: r0 = Null
    //     0xb5e3f4: mov             x0, NULL
    // 0xb5e3f8: b               #0xb5e400
    // 0xb5e3fc: LoadField: r0 = r1->field_b
    //     0xb5e3fc: ldur            w0, [x1, #0xb]
    // 0xb5e400: cmp             w0, NULL
    // 0xb5e404: b.ne            #0xb5e410
    // 0xb5e408: r3 = 0
    //     0xb5e408: movz            x3, #0
    // 0xb5e40c: b               #0xb5e418
    // 0xb5e410: r1 = LoadInt32Instr(r0)
    //     0xb5e410: sbfx            x1, x0, #1, #0x1f
    // 0xb5e414: mov             x3, x1
    // 0xb5e418: r0 = BoxInt64Instr(r2)
    //     0xb5e418: sbfiz           x0, x2, #1, #0x1f
    //     0xb5e41c: cmp             x2, x0, asr #1
    //     0xb5e420: b.eq            #0xb5e42c
    //     0xb5e424: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5e428: stur            x2, [x0, #7]
    // 0xb5e42c: lsl             x1, x3, #1
    // 0xb5e430: mov             x3, x1
    // 0xb5e434: mov             x1, x0
    // 0xb5e438: r2 = 0
    //     0xb5e438: movz            x2, #0
    // 0xb5e43c: r0 = clamp()
    //     0xb5e43c: bl              #0x6b3958  ; [dart:core] _IntegerImplementation::clamp
    // 0xb5e440: mov             x1, x0
    // 0xb5e444: ldur            x0, [fp, #-8]
    // 0xb5e448: LoadField: r2 = r0->field_b
    //     0xb5e448: ldur            w2, [x0, #0xb]
    // 0xb5e44c: DecompressPointer r2
    //     0xb5e44c: add             x2, x2, HEAP, lsl #32
    // 0xb5e450: cmp             w2, NULL
    // 0xb5e454: b.eq            #0xb5e548
    // 0xb5e458: LoadField: r0 = r2->field_b
    //     0xb5e458: ldur            w0, [x2, #0xb]
    // 0xb5e45c: DecompressPointer r0
    //     0xb5e45c: add             x0, x0, HEAP, lsl #32
    // 0xb5e460: cmp             w0, NULL
    // 0xb5e464: b.ne            #0xb5e470
    // 0xb5e468: r0 = Null
    //     0xb5e468: mov             x0, NULL
    // 0xb5e46c: b               #0xb5e484
    // 0xb5e470: str             x1, [SP]
    // 0xb5e474: mov             x1, x0
    // 0xb5e478: ldur            x2, [fp, #-0x18]
    // 0xb5e47c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb5e47c: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb5e480: r0 = sublist()
    //     0xb5e480: bl              #0x71da80  ; [dart:core] _GrowableList::sublist
    // 0xb5e484: cmp             w0, NULL
    // 0xb5e488: b.ne            #0xb5e49c
    // 0xb5e48c: r1 = <Entity>
    //     0xb5e48c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xb5e490: ldr             x1, [x1, #0xb68]
    // 0xb5e494: r2 = 0
    //     0xb5e494: movz            x2, #0
    // 0xb5e498: r0 = _GrowableList()
    //     0xb5e498: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb5e49c: ldur            x2, [fp, #-0x20]
    // 0xb5e4a0: stur            x0, [fp, #-8]
    // 0xb5e4a4: r1 = Function '<anonymous closure>':.
    //     0xb5e4a4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ef0] AnonymousClosure: (0xb5e54c), in [package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage (0xb5e38c)
    //     0xb5e4a8: ldr             x1, [x1, #0xef0]
    // 0xb5e4ac: r0 = AllocateClosure()
    //     0xb5e4ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5e4b0: r16 = <GestureDetector>
    //     0xb5e4b0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53890] TypeArguments: <GestureDetector>
    //     0xb5e4b4: ldr             x16, [x16, #0x890]
    // 0xb5e4b8: ldur            lr, [fp, #-8]
    // 0xb5e4bc: stp             lr, x16, [SP, #8]
    // 0xb5e4c0: str             x0, [SP]
    // 0xb5e4c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb5e4c4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb5e4c8: r0 = map()
    //     0xb5e4c8: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xb5e4cc: mov             x1, x0
    // 0xb5e4d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5e4d0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5e4d4: r0 = toList()
    //     0xb5e4d4: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xb5e4d8: stur            x0, [fp, #-8]
    // 0xb5e4dc: r0 = Row()
    //     0xb5e4dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5e4e0: r1 = Instance_Axis
    //     0xb5e4e0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5e4e4: StoreField: r0->field_f = r1
    //     0xb5e4e4: stur            w1, [x0, #0xf]
    // 0xb5e4e8: r1 = Instance_MainAxisAlignment
    //     0xb5e4e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5e4ec: ldr             x1, [x1, #0xa08]
    // 0xb5e4f0: StoreField: r0->field_13 = r1
    //     0xb5e4f0: stur            w1, [x0, #0x13]
    // 0xb5e4f4: r1 = Instance_MainAxisSize
    //     0xb5e4f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5e4f8: ldr             x1, [x1, #0xa10]
    // 0xb5e4fc: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5e4fc: stur            w1, [x0, #0x17]
    // 0xb5e500: r1 = Instance_CrossAxisAlignment
    //     0xb5e500: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5e504: ldr             x1, [x1, #0xa18]
    // 0xb5e508: StoreField: r0->field_1b = r1
    //     0xb5e508: stur            w1, [x0, #0x1b]
    // 0xb5e50c: r1 = Instance_VerticalDirection
    //     0xb5e50c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5e510: ldr             x1, [x1, #0xa20]
    // 0xb5e514: StoreField: r0->field_23 = r1
    //     0xb5e514: stur            w1, [x0, #0x23]
    // 0xb5e518: r1 = Instance_Clip
    //     0xb5e518: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5e51c: ldr             x1, [x1, #0x38]
    // 0xb5e520: StoreField: r0->field_2b = r1
    //     0xb5e520: stur            w1, [x0, #0x2b]
    // 0xb5e524: StoreField: r0->field_2f = rZR
    //     0xb5e524: stur            xzr, [x0, #0x2f]
    // 0xb5e528: ldur            x1, [fp, #-8]
    // 0xb5e52c: StoreField: r0->field_b = r1
    //     0xb5e52c: stur            w1, [x0, #0xb]
    // 0xb5e530: LeaveFrame
    //     0xb5e530: mov             SP, fp
    //     0xb5e534: ldp             fp, lr, [SP], #0x10
    // 0xb5e538: ret
    //     0xb5e538: ret             
    // 0xb5e53c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5e53c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5e540: b               #0xb5e3ac
    // 0xb5e544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5e544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5e548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5e548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, Entity) {
    // ** addr: 0xb5e54c, size: 0x320
    // 0xb5e54c: EnterFrame
    //     0xb5e54c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e550: mov             fp, SP
    // 0xb5e554: AllocStack(0x48)
    //     0xb5e554: sub             SP, SP, #0x48
    // 0xb5e558: SetupParameters()
    //     0xb5e558: ldr             x0, [fp, #0x18]
    //     0xb5e55c: ldur            w1, [x0, #0x17]
    //     0xb5e560: add             x1, x1, HEAP, lsl #32
    //     0xb5e564: stur            x1, [fp, #-8]
    // 0xb5e568: CheckStackOverflow
    //     0xb5e568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5e56c: cmp             SP, x16
    //     0xb5e570: b.ls            #0xb5e83c
    // 0xb5e574: r1 = 1
    //     0xb5e574: movz            x1, #0x1
    // 0xb5e578: r0 = AllocateContext()
    //     0xb5e578: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5e57c: mov             x2, x0
    // 0xb5e580: ldur            x0, [fp, #-8]
    // 0xb5e584: stur            x2, [fp, #-0x10]
    // 0xb5e588: StoreField: r2->field_b = r0
    //     0xb5e588: stur            w0, [x2, #0xb]
    // 0xb5e58c: ldr             x1, [fp, #0x10]
    // 0xb5e590: StoreField: r2->field_f = r1
    //     0xb5e590: stur            w1, [x2, #0xf]
    // 0xb5e594: LoadField: r1 = r0->field_f
    //     0xb5e594: ldur            w1, [x0, #0xf]
    // 0xb5e598: DecompressPointer r1
    //     0xb5e598: add             x1, x1, HEAP, lsl #32
    // 0xb5e59c: LoadField: r3 = r1->field_f
    //     0xb5e59c: ldur            w3, [x1, #0xf]
    // 0xb5e5a0: DecompressPointer r3
    //     0xb5e5a0: add             x3, x3, HEAP, lsl #32
    // 0xb5e5a4: cmp             w3, NULL
    // 0xb5e5a8: b.eq            #0xb5e844
    // 0xb5e5ac: mov             x1, x3
    // 0xb5e5b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5e5b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5e5b4: r0 = _of()
    //     0xb5e5b4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb5e5b8: LoadField: r1 = r0->field_7
    //     0xb5e5b8: ldur            w1, [x0, #7]
    // 0xb5e5bc: DecompressPointer r1
    //     0xb5e5bc: add             x1, x1, HEAP, lsl #32
    // 0xb5e5c0: LoadField: d0 = r1->field_7
    //     0xb5e5c0: ldur            d0, [x1, #7]
    // 0xb5e5c4: d1 = 4.000000
    //     0xb5e5c4: fmov            d1, #4.00000000
    // 0xb5e5c8: fdiv            d2, d0, d1
    // 0xb5e5cc: d0 = 16.000000
    //     0xb5e5cc: fmov            d0, #16.00000000
    // 0xb5e5d0: fsub            d1, d2, d0
    // 0xb5e5d4: ldur            x0, [fp, #-8]
    // 0xb5e5d8: stur            d1, [fp, #-0x30]
    // 0xb5e5dc: LoadField: r1 = r0->field_f
    //     0xb5e5dc: ldur            w1, [x0, #0xf]
    // 0xb5e5e0: DecompressPointer r1
    //     0xb5e5e0: add             x1, x1, HEAP, lsl #32
    // 0xb5e5e4: ldur            x3, [fp, #-0x10]
    // 0xb5e5e8: LoadField: r2 = r3->field_f
    //     0xb5e5e8: ldur            w2, [x3, #0xf]
    // 0xb5e5ec: DecompressPointer r2
    //     0xb5e5ec: add             x2, x2, HEAP, lsl #32
    // 0xb5e5f0: LoadField: r4 = r2->field_13
    //     0xb5e5f0: ldur            w4, [x2, #0x13]
    // 0xb5e5f4: DecompressPointer r4
    //     0xb5e5f4: add             x4, x4, HEAP, lsl #32
    // 0xb5e5f8: mov             x2, x4
    // 0xb5e5fc: r0 = _buildImageContainer()
    //     0xb5e5fc: bl              #0xb5e86c  ; [package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildImageContainer
    // 0xb5e600: stur            x0, [fp, #-0x18]
    // 0xb5e604: r0 = Padding()
    //     0xb5e604: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5e608: mov             x1, x0
    // 0xb5e60c: r0 = Instance_EdgeInsets
    //     0xb5e60c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xb5e610: ldr             x0, [x0, #0x100]
    // 0xb5e614: stur            x1, [fp, #-0x20]
    // 0xb5e618: StoreField: r1->field_f = r0
    //     0xb5e618: stur            w0, [x1, #0xf]
    // 0xb5e61c: ldur            x0, [fp, #-0x18]
    // 0xb5e620: StoreField: r1->field_b = r0
    //     0xb5e620: stur            w0, [x1, #0xb]
    // 0xb5e624: ldur            x2, [fp, #-0x10]
    // 0xb5e628: LoadField: r0 = r2->field_f
    //     0xb5e628: ldur            w0, [x2, #0xf]
    // 0xb5e62c: DecompressPointer r0
    //     0xb5e62c: add             x0, x0, HEAP, lsl #32
    // 0xb5e630: LoadField: r3 = r0->field_7
    //     0xb5e630: ldur            w3, [x0, #7]
    // 0xb5e634: DecompressPointer r3
    //     0xb5e634: add             x3, x3, HEAP, lsl #32
    // 0xb5e638: cmp             w3, NULL
    // 0xb5e63c: b.ne            #0xb5e648
    // 0xb5e640: r0 = Null
    //     0xb5e640: mov             x0, NULL
    // 0xb5e644: b               #0xb5e660
    // 0xb5e648: r0 = LoadClassIdInstr(r3)
    //     0xb5e648: ldur            x0, [x3, #-1]
    //     0xb5e64c: ubfx            x0, x0, #0xc, #0x14
    // 0xb5e650: str             x3, [SP]
    // 0xb5e654: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb5e654: sub             lr, x0, #1, lsl #12
    //     0xb5e658: ldr             lr, [x21, lr, lsl #3]
    //     0xb5e65c: blr             lr
    // 0xb5e660: cmp             w0, NULL
    // 0xb5e664: b.ne            #0xb5e670
    // 0xb5e668: r2 = ""
    //     0xb5e668: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5e66c: b               #0xb5e674
    // 0xb5e670: mov             x2, x0
    // 0xb5e674: ldur            x1, [fp, #-8]
    // 0xb5e678: ldur            d0, [fp, #-0x30]
    // 0xb5e67c: ldur            x0, [fp, #-0x20]
    // 0xb5e680: stur            x2, [fp, #-0x18]
    // 0xb5e684: LoadField: r3 = r1->field_f
    //     0xb5e684: ldur            w3, [x1, #0xf]
    // 0xb5e688: DecompressPointer r3
    //     0xb5e688: add             x3, x3, HEAP, lsl #32
    // 0xb5e68c: LoadField: r1 = r3->field_23
    //     0xb5e68c: ldur            w1, [x3, #0x23]
    // 0xb5e690: DecompressPointer r1
    //     0xb5e690: add             x1, x1, HEAP, lsl #32
    // 0xb5e694: r16 = Sentinel
    //     0xb5e694: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5e698: cmp             w1, w16
    // 0xb5e69c: b.eq            #0xb5e848
    // 0xb5e6a0: stur            x1, [fp, #-8]
    // 0xb5e6a4: r0 = Text()
    //     0xb5e6a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5e6a8: mov             x1, x0
    // 0xb5e6ac: ldur            x0, [fp, #-0x18]
    // 0xb5e6b0: stur            x1, [fp, #-0x28]
    // 0xb5e6b4: StoreField: r1->field_b = r0
    //     0xb5e6b4: stur            w0, [x1, #0xb]
    // 0xb5e6b8: ldur            x0, [fp, #-8]
    // 0xb5e6bc: StoreField: r1->field_13 = r0
    //     0xb5e6bc: stur            w0, [x1, #0x13]
    // 0xb5e6c0: r0 = Instance_TextAlign
    //     0xb5e6c0: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb5e6c4: StoreField: r1->field_1b = r0
    //     0xb5e6c4: stur            w0, [x1, #0x1b]
    // 0xb5e6c8: r0 = Instance_TextOverflow
    //     0xb5e6c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb5e6cc: ldr             x0, [x0, #0xe10]
    // 0xb5e6d0: StoreField: r1->field_2b = r0
    //     0xb5e6d0: stur            w0, [x1, #0x2b]
    // 0xb5e6d4: r2 = 4
    //     0xb5e6d4: movz            x2, #0x4
    // 0xb5e6d8: StoreField: r1->field_37 = r2
    //     0xb5e6d8: stur            w2, [x1, #0x37]
    // 0xb5e6dc: r0 = Padding()
    //     0xb5e6dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5e6e0: mov             x3, x0
    // 0xb5e6e4: r0 = Instance_EdgeInsets
    //     0xb5e6e4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb5e6e8: ldr             x0, [x0, #0x770]
    // 0xb5e6ec: stur            x3, [fp, #-8]
    // 0xb5e6f0: StoreField: r3->field_f = r0
    //     0xb5e6f0: stur            w0, [x3, #0xf]
    // 0xb5e6f4: ldur            x0, [fp, #-0x28]
    // 0xb5e6f8: StoreField: r3->field_b = r0
    //     0xb5e6f8: stur            w0, [x3, #0xb]
    // 0xb5e6fc: r1 = Null
    //     0xb5e6fc: mov             x1, NULL
    // 0xb5e700: r2 = 4
    //     0xb5e700: movz            x2, #0x4
    // 0xb5e704: r0 = AllocateArray()
    //     0xb5e704: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5e708: mov             x2, x0
    // 0xb5e70c: ldur            x0, [fp, #-0x20]
    // 0xb5e710: stur            x2, [fp, #-0x18]
    // 0xb5e714: StoreField: r2->field_f = r0
    //     0xb5e714: stur            w0, [x2, #0xf]
    // 0xb5e718: ldur            x0, [fp, #-8]
    // 0xb5e71c: StoreField: r2->field_13 = r0
    //     0xb5e71c: stur            w0, [x2, #0x13]
    // 0xb5e720: r1 = <Widget>
    //     0xb5e720: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5e724: r0 = AllocateGrowableArray()
    //     0xb5e724: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5e728: mov             x1, x0
    // 0xb5e72c: ldur            x0, [fp, #-0x18]
    // 0xb5e730: stur            x1, [fp, #-8]
    // 0xb5e734: StoreField: r1->field_f = r0
    //     0xb5e734: stur            w0, [x1, #0xf]
    // 0xb5e738: r0 = 4
    //     0xb5e738: movz            x0, #0x4
    // 0xb5e73c: StoreField: r1->field_b = r0
    //     0xb5e73c: stur            w0, [x1, #0xb]
    // 0xb5e740: r0 = Column()
    //     0xb5e740: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5e744: mov             x1, x0
    // 0xb5e748: r0 = Instance_Axis
    //     0xb5e748: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5e74c: stur            x1, [fp, #-0x18]
    // 0xb5e750: StoreField: r1->field_f = r0
    //     0xb5e750: stur            w0, [x1, #0xf]
    // 0xb5e754: r0 = Instance_MainAxisAlignment
    //     0xb5e754: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5e758: ldr             x0, [x0, #0xa08]
    // 0xb5e75c: StoreField: r1->field_13 = r0
    //     0xb5e75c: stur            w0, [x1, #0x13]
    // 0xb5e760: r0 = Instance_MainAxisSize
    //     0xb5e760: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5e764: ldr             x0, [x0, #0xa10]
    // 0xb5e768: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5e768: stur            w0, [x1, #0x17]
    // 0xb5e76c: r0 = Instance_CrossAxisAlignment
    //     0xb5e76c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5e770: ldr             x0, [x0, #0xa18]
    // 0xb5e774: StoreField: r1->field_1b = r0
    //     0xb5e774: stur            w0, [x1, #0x1b]
    // 0xb5e778: r0 = Instance_VerticalDirection
    //     0xb5e778: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5e77c: ldr             x0, [x0, #0xa20]
    // 0xb5e780: StoreField: r1->field_23 = r0
    //     0xb5e780: stur            w0, [x1, #0x23]
    // 0xb5e784: r0 = Instance_Clip
    //     0xb5e784: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5e788: ldr             x0, [x0, #0x38]
    // 0xb5e78c: StoreField: r1->field_2b = r0
    //     0xb5e78c: stur            w0, [x1, #0x2b]
    // 0xb5e790: StoreField: r1->field_2f = rZR
    //     0xb5e790: stur            xzr, [x1, #0x2f]
    // 0xb5e794: ldur            x0, [fp, #-8]
    // 0xb5e798: StoreField: r1->field_b = r0
    //     0xb5e798: stur            w0, [x1, #0xb]
    // 0xb5e79c: ldur            d0, [fp, #-0x30]
    // 0xb5e7a0: r0 = inline_Allocate_Double()
    //     0xb5e7a0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb5e7a4: add             x0, x0, #0x10
    //     0xb5e7a8: cmp             x2, x0
    //     0xb5e7ac: b.ls            #0xb5e854
    //     0xb5e7b0: str             x0, [THR, #0x50]  ; THR::top
    //     0xb5e7b4: sub             x0, x0, #0xf
    //     0xb5e7b8: movz            x2, #0xe15c
    //     0xb5e7bc: movk            x2, #0x3, lsl #16
    //     0xb5e7c0: stur            x2, [x0, #-1]
    // 0xb5e7c4: StoreField: r0->field_7 = d0
    //     0xb5e7c4: stur            d0, [x0, #7]
    // 0xb5e7c8: stur            x0, [fp, #-8]
    // 0xb5e7cc: r0 = Container()
    //     0xb5e7cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5e7d0: stur            x0, [fp, #-0x20]
    // 0xb5e7d4: ldur            x16, [fp, #-8]
    // 0xb5e7d8: r30 = Instance_EdgeInsets
    //     0xb5e7d8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xb5e7dc: ldr             lr, [lr, #0xa68]
    // 0xb5e7e0: stp             lr, x16, [SP, #8]
    // 0xb5e7e4: ldur            x16, [fp, #-0x18]
    // 0xb5e7e8: str             x16, [SP]
    // 0xb5e7ec: mov             x1, x0
    // 0xb5e7f0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xb5e7f0: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xb5e7f4: ldr             x4, [x4, #0x628]
    // 0xb5e7f8: r0 = Container()
    //     0xb5e7f8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5e7fc: r0 = GestureDetector()
    //     0xb5e7fc: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb5e800: ldur            x2, [fp, #-0x10]
    // 0xb5e804: r1 = Function '<anonymous closure>':.
    //     0xb5e804: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ef8] AnonymousClosure: (0xb5e9a0), in [package:customer_app/app/presentation/views/glass/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage (0xb5e38c)
    //     0xb5e808: ldr             x1, [x1, #0xef8]
    // 0xb5e80c: stur            x0, [fp, #-8]
    // 0xb5e810: r0 = AllocateClosure()
    //     0xb5e810: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5e814: ldur            x16, [fp, #-0x20]
    // 0xb5e818: stp             x16, x0, [SP]
    // 0xb5e81c: ldur            x1, [fp, #-8]
    // 0xb5e820: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb5e820: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb5e824: ldr             x4, [x4, #0xaf0]
    // 0xb5e828: r0 = GestureDetector()
    //     0xb5e828: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb5e82c: ldur            x0, [fp, #-8]
    // 0xb5e830: LeaveFrame
    //     0xb5e830: mov             SP, fp
    //     0xb5e834: ldp             fp, lr, [SP], #0x10
    // 0xb5e838: ret
    //     0xb5e838: ret             
    // 0xb5e83c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5e83c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5e840: b               #0xb5e574
    // 0xb5e844: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5e844: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5e848: r9 = _titleStyle
    //     0xb5e848: add             x9, PP, #0x55, lsl #12  ; [pp+0x55f00] Field <_CategoryGroupCarouselItemViewState@1586474453._titleStyle@1586474453>: late (offset: 0x24)
    //     0xb5e84c: ldr             x9, [x9, #0xf00]
    // 0xb5e850: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xb5e850: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xb5e854: SaveReg d0
    //     0xb5e854: str             q0, [SP, #-0x10]!
    // 0xb5e858: SaveReg r1
    //     0xb5e858: str             x1, [SP, #-8]!
    // 0xb5e85c: r0 = AllocateDouble()
    //     0xb5e85c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb5e860: RestoreReg r1
    //     0xb5e860: ldr             x1, [SP], #8
    // 0xb5e864: RestoreReg d0
    //     0xb5e864: ldr             q0, [SP], #0x10
    // 0xb5e868: b               #0xb5e7c4
  }
  _ _buildImageContainer(/* No info */) {
    // ** addr: 0xb5e86c, size: 0x134
    // 0xb5e86c: EnterFrame
    //     0xb5e86c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e870: mov             fp, SP
    // 0xb5e874: AllocStack(0x30)
    //     0xb5e874: sub             SP, SP, #0x30
    // 0xb5e878: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb5e878: stur            x2, [fp, #-0x10]
    // 0xb5e87c: CheckStackOverflow
    //     0xb5e87c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5e880: cmp             SP, x16
    //     0xb5e884: b.ls            #0xb5e980
    // 0xb5e888: cmp             w2, NULL
    // 0xb5e88c: b.eq            #0xb5e898
    // 0xb5e890: LoadField: r0 = r2->field_7
    //     0xb5e890: ldur            w0, [x2, #7]
    // 0xb5e894: cbnz            w0, #0xb5e8f4
    // 0xb5e898: LoadField: r0 = r1->field_1f
    //     0xb5e898: ldur            w0, [x1, #0x1f]
    // 0xb5e89c: DecompressPointer r0
    //     0xb5e89c: add             x0, x0, HEAP, lsl #32
    // 0xb5e8a0: r16 = Sentinel
    //     0xb5e8a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5e8a4: cmp             w0, w16
    // 0xb5e8a8: b.eq            #0xb5e988
    // 0xb5e8ac: r0 = Container()
    //     0xb5e8ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5e8b0: stur            x0, [fp, #-8]
    // 0xb5e8b4: r16 = 84.000000
    //     0xb5e8b4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xb5e8b8: ldr             x16, [x16, #0xf90]
    // 0xb5e8bc: r30 = 84.000000
    //     0xb5e8bc: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xb5e8c0: ldr             lr, [lr, #0xf90]
    // 0xb5e8c4: stp             lr, x16, [SP, #8]
    // 0xb5e8c8: r16 = Instance_BoxDecoration
    //     0xb5e8c8: add             x16, PP, #0x53, lsl #12  ; [pp+0x538b8] Obj!BoxDecoration@d649b1
    //     0xb5e8cc: ldr             x16, [x16, #0x8b8]
    // 0xb5e8d0: str             x16, [SP]
    // 0xb5e8d4: mov             x1, x0
    // 0xb5e8d8: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb5e8d8: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb5e8dc: ldr             x4, [x4, #0x468]
    // 0xb5e8e0: r0 = Container()
    //     0xb5e8e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5e8e4: ldur            x0, [fp, #-8]
    // 0xb5e8e8: LeaveFrame
    //     0xb5e8e8: mov             SP, fp
    //     0xb5e8ec: ldp             fp, lr, [SP], #0x10
    // 0xb5e8f0: ret
    //     0xb5e8f0: ret             
    // 0xb5e8f4: LoadField: r0 = r1->field_1f
    //     0xb5e8f4: ldur            w0, [x1, #0x1f]
    // 0xb5e8f8: DecompressPointer r0
    //     0xb5e8f8: add             x0, x0, HEAP, lsl #32
    // 0xb5e8fc: r16 = Sentinel
    //     0xb5e8fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5e900: cmp             w0, w16
    // 0xb5e904: b.eq            #0xb5e994
    // 0xb5e908: r0 = ImageHeaders.forImages()
    //     0xb5e908: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xb5e90c: r1 = <CachedNetworkImageProvider>
    //     0xb5e90c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb10] TypeArguments: <CachedNetworkImageProvider>
    //     0xb5e910: ldr             x1, [x1, #0xb10]
    // 0xb5e914: stur            x0, [fp, #-8]
    // 0xb5e918: r0 = CachedNetworkImageProvider()
    //     0xb5e918: bl              #0x859df8  ; AllocateCachedNetworkImageProviderStub -> CachedNetworkImageProvider (size=0x34)
    // 0xb5e91c: mov             x1, x0
    // 0xb5e920: ldur            x0, [fp, #-0x10]
    // 0xb5e924: stur            x1, [fp, #-0x18]
    // 0xb5e928: StoreField: r1->field_f = r0
    //     0xb5e928: stur            w0, [x1, #0xf]
    // 0xb5e92c: r0 = 336
    //     0xb5e92c: movz            x0, #0x150
    // 0xb5e930: StoreField: r1->field_27 = r0
    //     0xb5e930: stur            w0, [x1, #0x27]
    // 0xb5e934: StoreField: r1->field_2b = r0
    //     0xb5e934: stur            w0, [x1, #0x2b]
    // 0xb5e938: d0 = 1.000000
    //     0xb5e938: fmov            d0, #1.00000000
    // 0xb5e93c: ArrayStore: r1[0] = d0  ; List_8
    //     0xb5e93c: stur            d0, [x1, #0x17]
    // 0xb5e940: ldur            x0, [fp, #-8]
    // 0xb5e944: StoreField: r1->field_23 = r0
    //     0xb5e944: stur            w0, [x1, #0x23]
    // 0xb5e948: r0 = Instance_ImageRenderMethodForWeb
    //     0xb5e948: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb18] Obj!ImageRenderMethodForWeb@d75981
    //     0xb5e94c: ldr             x0, [x0, #0xb18]
    // 0xb5e950: StoreField: r1->field_2f = r0
    //     0xb5e950: stur            w0, [x1, #0x2f]
    // 0xb5e954: r0 = CircleAvatar()
    //     0xb5e954: bl              #0xa43c2c  ; AllocateCircleAvatarStub -> CircleAvatar (size=0x38)
    // 0xb5e958: r1 = Instance_Color
    //     0xb5e958: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb5e95c: ldr             x1, [x1, #0xf88]
    // 0xb5e960: StoreField: r0->field_f = r1
    //     0xb5e960: stur            w1, [x0, #0xf]
    // 0xb5e964: ldur            x1, [fp, #-0x18]
    // 0xb5e968: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5e968: stur            w1, [x0, #0x17]
    // 0xb5e96c: d0 = 42.000000
    //     0xb5e96c: ldr             d0, [PP, #0x5ae0]  ; [pp+0x5ae0] IMM: double(42) from 0x4045000000000000
    // 0xb5e970: StoreField: r0->field_27 = d0
    //     0xb5e970: stur            d0, [x0, #0x27]
    // 0xb5e974: LeaveFrame
    //     0xb5e974: mov             SP, fp
    //     0xb5e978: ldp             fp, lr, [SP], #0x10
    // 0xb5e97c: ret
    //     0xb5e97c: ret             
    // 0xb5e980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5e980: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5e984: b               #0xb5e888
    // 0xb5e988: r9 = _imageSize
    //     0xb5e988: add             x9, PP, #0x55, lsl #12  ; [pp+0x55f18] Field <_CategoryGroupCarouselItemViewState@1586474453._imageSize@1586474453>: late (offset: 0x20)
    //     0xb5e98c: ldr             x9, [x9, #0xf18]
    // 0xb5e990: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb5e990: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb5e994: r9 = _imageSize
    //     0xb5e994: add             x9, PP, #0x55, lsl #12  ; [pp+0x55f18] Field <_CategoryGroupCarouselItemViewState@1586474453._imageSize@1586474453>: late (offset: 0x20)
    //     0xb5e998: ldr             x9, [x9, #0xf18]
    // 0xb5e99c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb5e99c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb5e9a0, size: 0xd0
    // 0xb5e9a0: EnterFrame
    //     0xb5e9a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e9a4: mov             fp, SP
    // 0xb5e9a8: AllocStack(0x40)
    //     0xb5e9a8: sub             SP, SP, #0x40
    // 0xb5e9ac: SetupParameters()
    //     0xb5e9ac: ldr             x0, [fp, #0x10]
    //     0xb5e9b0: ldur            w1, [x0, #0x17]
    //     0xb5e9b4: add             x1, x1, HEAP, lsl #32
    // 0xb5e9b8: CheckStackOverflow
    //     0xb5e9b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5e9bc: cmp             SP, x16
    //     0xb5e9c0: b.ls            #0xb5ea64
    // 0xb5e9c4: LoadField: r0 = r1->field_b
    //     0xb5e9c4: ldur            w0, [x1, #0xb]
    // 0xb5e9c8: DecompressPointer r0
    //     0xb5e9c8: add             x0, x0, HEAP, lsl #32
    // 0xb5e9cc: LoadField: r2 = r0->field_f
    //     0xb5e9cc: ldur            w2, [x0, #0xf]
    // 0xb5e9d0: DecompressPointer r2
    //     0xb5e9d0: add             x2, x2, HEAP, lsl #32
    // 0xb5e9d4: LoadField: r0 = r2->field_b
    //     0xb5e9d4: ldur            w0, [x2, #0xb]
    // 0xb5e9d8: DecompressPointer r0
    //     0xb5e9d8: add             x0, x0, HEAP, lsl #32
    // 0xb5e9dc: cmp             w0, NULL
    // 0xb5e9e0: b.eq            #0xb5ea6c
    // 0xb5e9e4: LoadField: r2 = r0->field_13
    //     0xb5e9e4: ldur            w2, [x0, #0x13]
    // 0xb5e9e8: DecompressPointer r2
    //     0xb5e9e8: add             x2, x2, HEAP, lsl #32
    // 0xb5e9ec: LoadField: r3 = r0->field_f
    //     0xb5e9ec: ldur            w3, [x0, #0xf]
    // 0xb5e9f0: DecompressPointer r3
    //     0xb5e9f0: add             x3, x3, HEAP, lsl #32
    // 0xb5e9f4: LoadField: r4 = r0->field_23
    //     0xb5e9f4: ldur            w4, [x0, #0x23]
    // 0xb5e9f8: DecompressPointer r4
    //     0xb5e9f8: add             x4, x4, HEAP, lsl #32
    // 0xb5e9fc: LoadField: r5 = r0->field_1b
    //     0xb5e9fc: ldur            w5, [x0, #0x1b]
    // 0xb5ea00: DecompressPointer r5
    //     0xb5ea00: add             x5, x5, HEAP, lsl #32
    // 0xb5ea04: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xb5ea04: ldur            w6, [x0, #0x17]
    // 0xb5ea08: DecompressPointer r6
    //     0xb5ea08: add             x6, x6, HEAP, lsl #32
    // 0xb5ea0c: LoadField: r7 = r0->field_27
    //     0xb5ea0c: ldur            w7, [x0, #0x27]
    // 0xb5ea10: DecompressPointer r7
    //     0xb5ea10: add             x7, x7, HEAP, lsl #32
    // 0xb5ea14: LoadField: r8 = r1->field_f
    //     0xb5ea14: ldur            w8, [x1, #0xf]
    // 0xb5ea18: DecompressPointer r8
    //     0xb5ea18: add             x8, x8, HEAP, lsl #32
    // 0xb5ea1c: ArrayLoad: r1 = r8[0]  ; List_4
    //     0xb5ea1c: ldur            w1, [x8, #0x17]
    // 0xb5ea20: DecompressPointer r1
    //     0xb5ea20: add             x1, x1, HEAP, lsl #32
    // 0xb5ea24: LoadField: r8 = r0->field_1f
    //     0xb5ea24: ldur            w8, [x0, #0x1f]
    // 0xb5ea28: DecompressPointer r8
    //     0xb5ea28: add             x8, x8, HEAP, lsl #32
    // 0xb5ea2c: stp             x2, x8, [SP, #0x30]
    // 0xb5ea30: stp             x4, x3, [SP, #0x20]
    // 0xb5ea34: stp             x6, x5, [SP, #0x10]
    // 0xb5ea38: stp             x1, x7, [SP]
    // 0xb5ea3c: r4 = 0
    //     0xb5ea3c: movz            x4, #0
    // 0xb5ea40: ldr             x0, [SP, #0x38]
    // 0xb5ea44: r16 = UnlinkedCall_0x613b5c
    //     0xb5ea44: add             x16, PP, #0x55, lsl #12  ; [pp+0x55f08] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5ea48: add             x16, x16, #0xf08
    // 0xb5ea4c: ldp             x5, lr, [x16]
    // 0xb5ea50: blr             lr
    // 0xb5ea54: r0 = Null
    //     0xb5ea54: mov             x0, NULL
    // 0xb5ea58: LeaveFrame
    //     0xb5ea58: mov             SP, fp
    //     0xb5ea5c: ldp             fp, lr, [SP], #0x10
    // 0xb5ea60: ret
    //     0xb5ea60: ret             
    // 0xb5ea64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5ea64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5ea68: b               #0xb5e9c4
    // 0xb5ea6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ea6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb5ea70, size: 0x84
    // 0xb5ea70: EnterFrame
    //     0xb5ea70: stp             fp, lr, [SP, #-0x10]!
    //     0xb5ea74: mov             fp, SP
    // 0xb5ea78: AllocStack(0x10)
    //     0xb5ea78: sub             SP, SP, #0x10
    // 0xb5ea7c: SetupParameters()
    //     0xb5ea7c: ldr             x0, [fp, #0x18]
    //     0xb5ea80: ldur            w1, [x0, #0x17]
    //     0xb5ea84: add             x1, x1, HEAP, lsl #32
    //     0xb5ea88: stur            x1, [fp, #-8]
    // 0xb5ea8c: CheckStackOverflow
    //     0xb5ea8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5ea90: cmp             SP, x16
    //     0xb5ea94: b.ls            #0xb5eaec
    // 0xb5ea98: r1 = 1
    //     0xb5ea98: movz            x1, #0x1
    // 0xb5ea9c: r0 = AllocateContext()
    //     0xb5ea9c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5eaa0: mov             x1, x0
    // 0xb5eaa4: ldur            x0, [fp, #-8]
    // 0xb5eaa8: StoreField: r1->field_b = r0
    //     0xb5eaa8: stur            w0, [x1, #0xb]
    // 0xb5eaac: ldr             x2, [fp, #0x10]
    // 0xb5eab0: StoreField: r1->field_f = r2
    //     0xb5eab0: stur            w2, [x1, #0xf]
    // 0xb5eab4: LoadField: r3 = r0->field_f
    //     0xb5eab4: ldur            w3, [x0, #0xf]
    // 0xb5eab8: DecompressPointer r3
    //     0xb5eab8: add             x3, x3, HEAP, lsl #32
    // 0xb5eabc: mov             x2, x1
    // 0xb5eac0: stur            x3, [fp, #-0x10]
    // 0xb5eac4: r1 = Function '<anonymous closure>':.
    //     0xb5eac4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f20] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xb5eac8: ldr             x1, [x1, #0xf20]
    // 0xb5eacc: r0 = AllocateClosure()
    //     0xb5eacc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ead0: ldur            x1, [fp, #-0x10]
    // 0xb5ead4: mov             x2, x0
    // 0xb5ead8: r0 = setState()
    //     0xb5ead8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb5eadc: r0 = Null
    //     0xb5eadc: mov             x0, NULL
    // 0xb5eae0: LeaveFrame
    //     0xb5eae0: mov             SP, fp
    //     0xb5eae4: ldp             fp, lr, [SP], #0x10
    // 0xb5eae8: ret
    //     0xb5eae8: ret             
    // 0xb5eaec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5eaec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5eaf0: b               #0xb5ea98
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0xc71098, size: 0x160
    // 0xc71098: EnterFrame
    //     0xc71098: stp             fp, lr, [SP, #-0x10]!
    //     0xc7109c: mov             fp, SP
    // 0xc710a0: AllocStack(0x18)
    //     0xc710a0: sub             SP, SP, #0x18
    // 0xc710a4: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc710a4: mov             x0, x1
    //     0xc710a8: stur            x1, [fp, #-8]
    // 0xc710ac: CheckStackOverflow
    //     0xc710ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc710b0: cmp             SP, x16
    //     0xc710b4: b.ls            #0xc711d8
    // 0xc710b8: LoadField: r1 = r0->field_f
    //     0xc710b8: ldur            w1, [x0, #0xf]
    // 0xc710bc: DecompressPointer r1
    //     0xc710bc: add             x1, x1, HEAP, lsl #32
    // 0xc710c0: cmp             w1, NULL
    // 0xc710c4: b.eq            #0xc711e0
    // 0xc710c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc710c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc710cc: r0 = _of()
    //     0xc710cc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc710d0: LoadField: r1 = r0->field_7
    //     0xc710d0: ldur            w1, [x0, #7]
    // 0xc710d4: DecompressPointer r1
    //     0xc710d4: add             x1, x1, HEAP, lsl #32
    // 0xc710d8: LoadField: d0 = r1->field_f
    //     0xc710d8: ldur            d0, [x1, #0xf]
    // 0xc710dc: d1 = 0.200000
    //     0xc710dc: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xc710e0: fmul            d2, d0, d1
    // 0xc710e4: r0 = inline_Allocate_Double()
    //     0xc710e4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc710e8: add             x0, x0, #0x10
    //     0xc710ec: cmp             x1, x0
    //     0xc710f0: b.ls            #0xc711e4
    //     0xc710f4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc710f8: sub             x0, x0, #0xf
    //     0xc710fc: movz            x1, #0xe15c
    //     0xc71100: movk            x1, #0x3, lsl #16
    //     0xc71104: stur            x1, [x0, #-1]
    // 0xc71108: StoreField: r0->field_7 = d2
    //     0xc71108: stur            d2, [x0, #7]
    // 0xc7110c: ldur            x2, [fp, #-8]
    // 0xc71110: StoreField: r2->field_27 = r0
    //     0xc71110: stur            w0, [x2, #0x27]
    //     0xc71114: ldurb           w16, [x2, #-1]
    //     0xc71118: ldurb           w17, [x0, #-1]
    //     0xc7111c: and             x16, x17, x16, lsr #2
    //     0xc71120: tst             x16, HEAP, lsr #32
    //     0xc71124: b.eq            #0xc7112c
    //     0xc71128: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc7112c: r0 = 84.000000
    //     0xc7112c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xc71130: ldr             x0, [x0, #0xf90]
    // 0xc71134: StoreField: r2->field_1f = r0
    //     0xc71134: stur            w0, [x2, #0x1f]
    // 0xc71138: LoadField: r1 = r2->field_f
    //     0xc71138: ldur            w1, [x2, #0xf]
    // 0xc7113c: DecompressPointer r1
    //     0xc7113c: add             x1, x1, HEAP, lsl #32
    // 0xc71140: cmp             w1, NULL
    // 0xc71144: b.eq            #0xc711f4
    // 0xc71148: r0 = of()
    //     0xc71148: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc7114c: LoadField: r1 = r0->field_87
    //     0xc7114c: ldur            w1, [x0, #0x87]
    // 0xc71150: DecompressPointer r1
    //     0xc71150: add             x1, x1, HEAP, lsl #32
    // 0xc71154: LoadField: r0 = r1->field_33
    //     0xc71154: ldur            w0, [x1, #0x33]
    // 0xc71158: DecompressPointer r0
    //     0xc71158: add             x0, x0, HEAP, lsl #32
    // 0xc7115c: cmp             w0, NULL
    // 0xc71160: b.ne            #0xc7116c
    // 0xc71164: r1 = Null
    //     0xc71164: mov             x1, NULL
    // 0xc71168: b               #0xc71190
    // 0xc7116c: r16 = 12.000000
    //     0xc7116c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc71170: ldr             x16, [x16, #0x9e8]
    // 0xc71174: r30 = Instance_Color
    //     0xc71174: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc71178: stp             lr, x16, [SP]
    // 0xc7117c: mov             x1, x0
    // 0xc71180: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc71180: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc71184: ldr             x4, [x4, #0xaa0]
    // 0xc71188: r0 = copyWith()
    //     0xc71188: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc7118c: mov             x1, x0
    // 0xc71190: cmp             w1, NULL
    // 0xc71194: b.ne            #0xc711a4
    // 0xc71198: r0 = Instance_TextStyle
    //     0xc71198: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xc7119c: ldr             x0, [x0, #0x9b0]
    // 0xc711a0: b               #0xc711a8
    // 0xc711a4: mov             x0, x1
    // 0xc711a8: ldur            x1, [fp, #-8]
    // 0xc711ac: StoreField: r1->field_23 = r0
    //     0xc711ac: stur            w0, [x1, #0x23]
    //     0xc711b0: ldurb           w16, [x1, #-1]
    //     0xc711b4: ldurb           w17, [x0, #-1]
    //     0xc711b8: and             x16, x17, x16, lsr #2
    //     0xc711bc: tst             x16, HEAP, lsr #32
    //     0xc711c0: b.eq            #0xc711c8
    //     0xc711c4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc711c8: r0 = Null
    //     0xc711c8: mov             x0, NULL
    // 0xc711cc: LeaveFrame
    //     0xc711cc: mov             SP, fp
    //     0xc711d0: ldp             fp, lr, [SP], #0x10
    // 0xc711d4: ret
    //     0xc711d4: ret             
    // 0xc711d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc711d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc711dc: b               #0xc710b8
    // 0xc711e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc711e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc711e4: SaveReg d2
    //     0xc711e4: str             q2, [SP, #-0x10]!
    // 0xc711e8: r0 = AllocateDouble()
    //     0xc711e8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc711ec: RestoreReg d2
    //     0xc711ec: ldr             q2, [SP], #0x10
    // 0xc711f0: b               #0xc71108
    // 0xc711f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc711f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87978, size: 0x54
    // 0xc87978: EnterFrame
    //     0xc87978: stp             fp, lr, [SP, #-0x10]!
    //     0xc8797c: mov             fp, SP
    // 0xc87980: CheckStackOverflow
    //     0xc87980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87984: cmp             SP, x16
    //     0xc87988: b.ls            #0xc879b8
    // 0xc8798c: LoadField: r0 = r1->field_13
    //     0xc8798c: ldur            w0, [x1, #0x13]
    // 0xc87990: DecompressPointer r0
    //     0xc87990: add             x0, x0, HEAP, lsl #32
    // 0xc87994: r16 = Sentinel
    //     0xc87994: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87998: cmp             w0, w16
    // 0xc8799c: b.eq            #0xc879c0
    // 0xc879a0: mov             x1, x0
    // 0xc879a4: r0 = dispose()
    //     0xc879a4: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc879a8: r0 = Null
    //     0xc879a8: mov             x0, NULL
    // 0xc879ac: LeaveFrame
    //     0xc879ac: mov             SP, fp
    //     0xc879b0: ldp             fp, lr, [SP], #0x10
    // 0xc879b4: ret
    //     0xc879b4: ret             
    // 0xc879b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc879b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc879bc: b               #0xc8798c
    // 0xc879c0: r9 = _pageLineController
    //     0xc879c0: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ee8] Field <_CategoryGroupCarouselItemViewState@1586474453._pageLineController@1586474453>: late (offset: 0x14)
    //     0xc879c4: ldr             x9, [x9, #0xee8]
    // 0xc879c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc879c8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4080, size: 0x2c, field offset: 0xc
//   const constructor, 
class CategoryGroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f244, size: 0x3c
    // 0xc7f244: EnterFrame
    //     0xc7f244: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f248: mov             fp, SP
    // 0xc7f24c: mov             x0, x1
    // 0xc7f250: r1 = <CategoryGroupCarouselItemView>
    //     0xc7f250: add             x1, PP, #0x48, lsl #12  ; [pp+0x48838] TypeArguments: <CategoryGroupCarouselItemView>
    //     0xc7f254: ldr             x1, [x1, #0x838]
    // 0xc7f258: r0 = _CategoryGroupCarouselItemViewState()
    //     0xc7f258: bl              #0xc7f280  ; Allocate_CategoryGroupCarouselItemViewStateStub -> _CategoryGroupCarouselItemViewState (size=0x2c)
    // 0xc7f25c: r1 = Sentinel
    //     0xc7f25c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f260: StoreField: r0->field_13 = r1
    //     0xc7f260: stur            w1, [x0, #0x13]
    // 0xc7f264: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7f264: stur            xzr, [x0, #0x17]
    // 0xc7f268: StoreField: r0->field_1f = r1
    //     0xc7f268: stur            w1, [x0, #0x1f]
    // 0xc7f26c: StoreField: r0->field_23 = r1
    //     0xc7f26c: stur            w1, [x0, #0x23]
    // 0xc7f270: StoreField: r0->field_27 = r1
    //     0xc7f270: stur            w1, [x0, #0x27]
    // 0xc7f274: LeaveFrame
    //     0xc7f274: mov             SP, fp
    //     0xc7f278: ldp             fp, lr, [SP], #0x10
    // 0xc7f27c: ret
    //     0xc7f27c: ret             
  }
}
