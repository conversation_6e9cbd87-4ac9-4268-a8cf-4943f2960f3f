// lib: , url: package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart

// class id: 1049158, size: 0x8
class :: {
}

// class id: 3516, size: 0x14, field offset: 0x14
class _CustomizedBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa46418, size: 0xc08
    // 0xa46418: EnterFrame
    //     0xa46418: stp             fp, lr, [SP, #-0x10]!
    //     0xa4641c: mov             fp, SP
    // 0xa46420: AllocStack(0x70)
    //     0xa46420: sub             SP, SP, #0x70
    // 0xa46424: SetupParameters(_CustomizedBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa46424: stur            x1, [fp, #-8]
    //     0xa46428: stur            x2, [fp, #-0x10]
    // 0xa4642c: CheckStackOverflow
    //     0xa4642c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46430: cmp             SP, x16
    //     0xa46434: b.ls            #0xa47008
    // 0xa46438: r1 = 2
    //     0xa46438: movz            x1, #0x2
    // 0xa4643c: r0 = AllocateContext()
    //     0xa4643c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa46440: mov             x1, x0
    // 0xa46444: ldur            x0, [fp, #-8]
    // 0xa46448: stur            x1, [fp, #-0x18]
    // 0xa4644c: StoreField: r1->field_f = r0
    //     0xa4644c: stur            w0, [x1, #0xf]
    // 0xa46450: ldur            x2, [fp, #-0x10]
    // 0xa46454: StoreField: r1->field_13 = r2
    //     0xa46454: stur            w2, [x1, #0x13]
    // 0xa46458: LoadField: r2 = r0->field_b
    //     0xa46458: ldur            w2, [x0, #0xb]
    // 0xa4645c: DecompressPointer r2
    //     0xa4645c: add             x2, x2, HEAP, lsl #32
    // 0xa46460: cmp             w2, NULL
    // 0xa46464: b.eq            #0xa47010
    // 0xa46468: LoadField: r3 = r2->field_b
    //     0xa46468: ldur            w3, [x2, #0xb]
    // 0xa4646c: DecompressPointer r3
    //     0xa4646c: add             x3, x3, HEAP, lsl #32
    // 0xa46470: LoadField: r2 = r3->field_b
    //     0xa46470: ldur            w2, [x3, #0xb]
    // 0xa46474: DecompressPointer r2
    //     0xa46474: add             x2, x2, HEAP, lsl #32
    // 0xa46478: cmp             w2, NULL
    // 0xa4647c: b.ne            #0xa46488
    // 0xa46480: r2 = Null
    //     0xa46480: mov             x2, NULL
    // 0xa46484: b               #0xa46494
    // 0xa46488: LoadField: r3 = r2->field_23
    //     0xa46488: ldur            w3, [x2, #0x23]
    // 0xa4648c: DecompressPointer r3
    //     0xa4648c: add             x3, x3, HEAP, lsl #32
    // 0xa46490: mov             x2, x3
    // 0xa46494: cmp             w2, NULL
    // 0xa46498: b.ne            #0xa464a0
    // 0xa4649c: r2 = ""
    //     0xa4649c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa464a0: stur            x2, [fp, #-0x10]
    // 0xa464a4: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xa464a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa464a8: ldr             x0, [x0, #0x1ab0]
    //     0xa464ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa464b0: cmp             w0, w16
    //     0xa464b4: b.ne            #0xa464c4
    //     0xa464b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xa464bc: ldr             x2, [x2, #0x60]
    //     0xa464c0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa464c4: LoadField: r2 = r0->field_87
    //     0xa464c4: ldur            w2, [x0, #0x87]
    // 0xa464c8: DecompressPointer r2
    //     0xa464c8: add             x2, x2, HEAP, lsl #32
    // 0xa464cc: stur            x2, [fp, #-0x28]
    // 0xa464d0: LoadField: r0 = r2->field_7
    //     0xa464d0: ldur            w0, [x2, #7]
    // 0xa464d4: DecompressPointer r0
    //     0xa464d4: add             x0, x0, HEAP, lsl #32
    // 0xa464d8: stur            x0, [fp, #-0x20]
    // 0xa464dc: r16 = 16.000000
    //     0xa464dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa464e0: ldr             x16, [x16, #0x188]
    // 0xa464e4: r30 = Instance_Color
    //     0xa464e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa464e8: stp             lr, x16, [SP]
    // 0xa464ec: mov             x1, x0
    // 0xa464f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa464f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa464f4: ldr             x4, [x4, #0xaa0]
    // 0xa464f8: r0 = copyWith()
    //     0xa464f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa464fc: stur            x0, [fp, #-0x30]
    // 0xa46500: r0 = Text()
    //     0xa46500: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa46504: mov             x2, x0
    // 0xa46508: ldur            x0, [fp, #-0x10]
    // 0xa4650c: stur            x2, [fp, #-0x38]
    // 0xa46510: StoreField: r2->field_b = r0
    //     0xa46510: stur            w0, [x2, #0xb]
    // 0xa46514: ldur            x0, [fp, #-0x30]
    // 0xa46518: StoreField: r2->field_13 = r0
    //     0xa46518: stur            w0, [x2, #0x13]
    // 0xa4651c: r0 = 4
    //     0xa4651c: movz            x0, #0x4
    // 0xa46520: StoreField: r2->field_37 = r0
    //     0xa46520: stur            w0, [x2, #0x37]
    // 0xa46524: r1 = <FlexParentData>
    //     0xa46524: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa46528: ldr             x1, [x1, #0xe00]
    // 0xa4652c: r0 = Expanded()
    //     0xa4652c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa46530: mov             x1, x0
    // 0xa46534: r0 = 1
    //     0xa46534: movz            x0, #0x1
    // 0xa46538: stur            x1, [fp, #-0x10]
    // 0xa4653c: StoreField: r1->field_13 = r0
    //     0xa4653c: stur            x0, [x1, #0x13]
    // 0xa46540: r2 = Instance_FlexFit
    //     0xa46540: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa46544: ldr             x2, [x2, #0xe08]
    // 0xa46548: StoreField: r1->field_1b = r2
    //     0xa46548: stur            w2, [x1, #0x1b]
    // 0xa4654c: ldur            x3, [fp, #-0x38]
    // 0xa46550: StoreField: r1->field_b = r3
    //     0xa46550: stur            w3, [x1, #0xb]
    // 0xa46554: r0 = InkWell()
    //     0xa46554: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa46558: mov             x3, x0
    // 0xa4655c: r0 = Instance_Icon
    //     0xa4655c: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xa46560: ldr             x0, [x0, #0x2b8]
    // 0xa46564: stur            x3, [fp, #-0x30]
    // 0xa46568: StoreField: r3->field_b = r0
    //     0xa46568: stur            w0, [x3, #0xb]
    // 0xa4656c: r1 = Function '<anonymous closure>':.
    //     0xa4656c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab78] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa46570: ldr             x1, [x1, #0xb78]
    // 0xa46574: r2 = Null
    //     0xa46574: mov             x2, NULL
    // 0xa46578: r0 = AllocateClosure()
    //     0xa46578: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4657c: mov             x1, x0
    // 0xa46580: ldur            x0, [fp, #-0x30]
    // 0xa46584: StoreField: r0->field_f = r1
    //     0xa46584: stur            w1, [x0, #0xf]
    // 0xa46588: r3 = true
    //     0xa46588: add             x3, NULL, #0x20  ; true
    // 0xa4658c: StoreField: r0->field_43 = r3
    //     0xa4658c: stur            w3, [x0, #0x43]
    // 0xa46590: r1 = Instance_BoxShape
    //     0xa46590: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa46594: ldr             x1, [x1, #0x80]
    // 0xa46598: StoreField: r0->field_47 = r1
    //     0xa46598: stur            w1, [x0, #0x47]
    // 0xa4659c: StoreField: r0->field_6f = r3
    //     0xa4659c: stur            w3, [x0, #0x6f]
    // 0xa465a0: r4 = false
    //     0xa465a0: add             x4, NULL, #0x30  ; false
    // 0xa465a4: StoreField: r0->field_73 = r4
    //     0xa465a4: stur            w4, [x0, #0x73]
    // 0xa465a8: StoreField: r0->field_83 = r3
    //     0xa465a8: stur            w3, [x0, #0x83]
    // 0xa465ac: StoreField: r0->field_7b = r4
    //     0xa465ac: stur            w4, [x0, #0x7b]
    // 0xa465b0: r1 = Null
    //     0xa465b0: mov             x1, NULL
    // 0xa465b4: r2 = 4
    //     0xa465b4: movz            x2, #0x4
    // 0xa465b8: r0 = AllocateArray()
    //     0xa465b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa465bc: mov             x2, x0
    // 0xa465c0: ldur            x0, [fp, #-0x10]
    // 0xa465c4: stur            x2, [fp, #-0x38]
    // 0xa465c8: StoreField: r2->field_f = r0
    //     0xa465c8: stur            w0, [x2, #0xf]
    // 0xa465cc: ldur            x0, [fp, #-0x30]
    // 0xa465d0: StoreField: r2->field_13 = r0
    //     0xa465d0: stur            w0, [x2, #0x13]
    // 0xa465d4: r1 = <Widget>
    //     0xa465d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa465d8: r0 = AllocateGrowableArray()
    //     0xa465d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa465dc: mov             x1, x0
    // 0xa465e0: ldur            x0, [fp, #-0x38]
    // 0xa465e4: stur            x1, [fp, #-0x10]
    // 0xa465e8: StoreField: r1->field_f = r0
    //     0xa465e8: stur            w0, [x1, #0xf]
    // 0xa465ec: r0 = 4
    //     0xa465ec: movz            x0, #0x4
    // 0xa465f0: StoreField: r1->field_b = r0
    //     0xa465f0: stur            w0, [x1, #0xb]
    // 0xa465f4: r0 = Row()
    //     0xa465f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa465f8: mov             x1, x0
    // 0xa465fc: r0 = Instance_Axis
    //     0xa465fc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa46600: stur            x1, [fp, #-0x30]
    // 0xa46604: StoreField: r1->field_f = r0
    //     0xa46604: stur            w0, [x1, #0xf]
    // 0xa46608: r2 = Instance_MainAxisAlignment
    //     0xa46608: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa4660c: ldr             x2, [x2, #0xa8]
    // 0xa46610: StoreField: r1->field_13 = r2
    //     0xa46610: stur            w2, [x1, #0x13]
    // 0xa46614: r2 = Instance_MainAxisSize
    //     0xa46614: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa46618: ldr             x2, [x2, #0xa10]
    // 0xa4661c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4661c: stur            w2, [x1, #0x17]
    // 0xa46620: r3 = Instance_CrossAxisAlignment
    //     0xa46620: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa46624: ldr             x3, [x3, #0xa18]
    // 0xa46628: StoreField: r1->field_1b = r3
    //     0xa46628: stur            w3, [x1, #0x1b]
    // 0xa4662c: r4 = Instance_VerticalDirection
    //     0xa4662c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa46630: ldr             x4, [x4, #0xa20]
    // 0xa46634: StoreField: r1->field_23 = r4
    //     0xa46634: stur            w4, [x1, #0x23]
    // 0xa46638: r5 = Instance_Clip
    //     0xa46638: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa4663c: ldr             x5, [x5, #0x38]
    // 0xa46640: StoreField: r1->field_2b = r5
    //     0xa46640: stur            w5, [x1, #0x2b]
    // 0xa46644: StoreField: r1->field_2f = rZR
    //     0xa46644: stur            xzr, [x1, #0x2f]
    // 0xa46648: ldur            x6, [fp, #-0x10]
    // 0xa4664c: StoreField: r1->field_b = r6
    //     0xa4664c: stur            w6, [x1, #0xb]
    // 0xa46650: r16 = <EdgeInsets>
    //     0xa46650: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa46654: ldr             x16, [x16, #0xda0]
    // 0xa46658: r30 = Instance_EdgeInsets
    //     0xa46658: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa4665c: ldr             lr, [lr, #0x1f0]
    // 0xa46660: stp             lr, x16, [SP]
    // 0xa46664: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa46664: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa46668: r0 = all()
    //     0xa46668: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa4666c: stur            x0, [fp, #-0x10]
    // 0xa46670: r0 = Radius()
    //     0xa46670: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa46674: d0 = 12.000000
    //     0xa46674: fmov            d0, #12.00000000
    // 0xa46678: stur            x0, [fp, #-0x38]
    // 0xa4667c: StoreField: r0->field_7 = d0
    //     0xa4667c: stur            d0, [x0, #7]
    // 0xa46680: StoreField: r0->field_f = d0
    //     0xa46680: stur            d0, [x0, #0xf]
    // 0xa46684: r0 = BorderRadius()
    //     0xa46684: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa46688: mov             x1, x0
    // 0xa4668c: ldur            x0, [fp, #-0x38]
    // 0xa46690: stur            x1, [fp, #-0x58]
    // 0xa46694: StoreField: r1->field_7 = r0
    //     0xa46694: stur            w0, [x1, #7]
    // 0xa46698: StoreField: r1->field_b = r0
    //     0xa46698: stur            w0, [x1, #0xb]
    // 0xa4669c: StoreField: r1->field_f = r0
    //     0xa4669c: stur            w0, [x1, #0xf]
    // 0xa466a0: StoreField: r1->field_13 = r0
    //     0xa466a0: stur            w0, [x1, #0x13]
    // 0xa466a4: ldur            x0, [fp, #-8]
    // 0xa466a8: LoadField: r2 = r0->field_b
    //     0xa466a8: ldur            w2, [x0, #0xb]
    // 0xa466ac: DecompressPointer r2
    //     0xa466ac: add             x2, x2, HEAP, lsl #32
    // 0xa466b0: cmp             w2, NULL
    // 0xa466b4: b.eq            #0xa47014
    // 0xa466b8: LoadField: r3 = r2->field_33
    //     0xa466b8: ldur            w3, [x2, #0x33]
    // 0xa466bc: DecompressPointer r3
    //     0xa466bc: add             x3, x3, HEAP, lsl #32
    // 0xa466c0: LoadField: r2 = r3->field_3f
    //     0xa466c0: ldur            w2, [x3, #0x3f]
    // 0xa466c4: DecompressPointer r2
    //     0xa466c4: add             x2, x2, HEAP, lsl #32
    // 0xa466c8: cmp             w2, NULL
    // 0xa466cc: b.ne            #0xa466d8
    // 0xa466d0: r3 = Null
    //     0xa466d0: mov             x3, NULL
    // 0xa466d4: b               #0xa466fc
    // 0xa466d8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa466d8: ldur            w3, [x2, #0x17]
    // 0xa466dc: DecompressPointer r3
    //     0xa466dc: add             x3, x3, HEAP, lsl #32
    // 0xa466e0: cmp             w3, NULL
    // 0xa466e4: b.ne            #0xa466f0
    // 0xa466e8: r3 = Null
    //     0xa466e8: mov             x3, NULL
    // 0xa466ec: b               #0xa466fc
    // 0xa466f0: LoadField: r4 = r3->field_7
    //     0xa466f0: ldur            w4, [x3, #7]
    // 0xa466f4: DecompressPointer r4
    //     0xa466f4: add             x4, x4, HEAP, lsl #32
    // 0xa466f8: mov             x3, x4
    // 0xa466fc: cmp             w3, NULL
    // 0xa46700: b.ne            #0xa4670c
    // 0xa46704: r3 = 0
    //     0xa46704: movz            x3, #0
    // 0xa46708: b               #0xa4671c
    // 0xa4670c: r4 = LoadInt32Instr(r3)
    //     0xa4670c: sbfx            x4, x3, #1, #0x1f
    //     0xa46710: tbz             w3, #0, #0xa46718
    //     0xa46714: ldur            x4, [x3, #7]
    // 0xa46718: mov             x3, x4
    // 0xa4671c: stur            x3, [fp, #-0x50]
    // 0xa46720: cmp             w2, NULL
    // 0xa46724: b.ne            #0xa46730
    // 0xa46728: r4 = Null
    //     0xa46728: mov             x4, NULL
    // 0xa4672c: b               #0xa46754
    // 0xa46730: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa46730: ldur            w4, [x2, #0x17]
    // 0xa46734: DecompressPointer r4
    //     0xa46734: add             x4, x4, HEAP, lsl #32
    // 0xa46738: cmp             w4, NULL
    // 0xa4673c: b.ne            #0xa46748
    // 0xa46740: r4 = Null
    //     0xa46740: mov             x4, NULL
    // 0xa46744: b               #0xa46754
    // 0xa46748: LoadField: r5 = r4->field_b
    //     0xa46748: ldur            w5, [x4, #0xb]
    // 0xa4674c: DecompressPointer r5
    //     0xa4674c: add             x5, x5, HEAP, lsl #32
    // 0xa46750: mov             x4, x5
    // 0xa46754: cmp             w4, NULL
    // 0xa46758: b.ne            #0xa46764
    // 0xa4675c: r4 = 0
    //     0xa4675c: movz            x4, #0
    // 0xa46760: b               #0xa46774
    // 0xa46764: r5 = LoadInt32Instr(r4)
    //     0xa46764: sbfx            x5, x4, #1, #0x1f
    //     0xa46768: tbz             w4, #0, #0xa46770
    //     0xa4676c: ldur            x5, [x4, #7]
    // 0xa46770: mov             x4, x5
    // 0xa46774: stur            x4, [fp, #-0x48]
    // 0xa46778: cmp             w2, NULL
    // 0xa4677c: b.ne            #0xa46788
    // 0xa46780: r2 = Null
    //     0xa46780: mov             x2, NULL
    // 0xa46784: b               #0xa467a8
    // 0xa46788: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xa46788: ldur            w5, [x2, #0x17]
    // 0xa4678c: DecompressPointer r5
    //     0xa4678c: add             x5, x5, HEAP, lsl #32
    // 0xa46790: cmp             w5, NULL
    // 0xa46794: b.ne            #0xa467a0
    // 0xa46798: r2 = Null
    //     0xa46798: mov             x2, NULL
    // 0xa4679c: b               #0xa467a8
    // 0xa467a0: LoadField: r2 = r5->field_f
    //     0xa467a0: ldur            w2, [x5, #0xf]
    // 0xa467a4: DecompressPointer r2
    //     0xa467a4: add             x2, x2, HEAP, lsl #32
    // 0xa467a8: cmp             w2, NULL
    // 0xa467ac: b.ne            #0xa467b8
    // 0xa467b0: r5 = 0
    //     0xa467b0: movz            x5, #0
    // 0xa467b4: b               #0xa467c4
    // 0xa467b8: r5 = LoadInt32Instr(r2)
    //     0xa467b8: sbfx            x5, x2, #1, #0x1f
    //     0xa467bc: tbz             w2, #0, #0xa467c4
    //     0xa467c0: ldur            x5, [x2, #7]
    // 0xa467c4: ldur            x2, [fp, #-0x10]
    // 0xa467c8: stur            x5, [fp, #-0x40]
    // 0xa467cc: r0 = Color()
    //     0xa467cc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa467d0: mov             x1, x0
    // 0xa467d4: r0 = Instance_ColorSpace
    //     0xa467d4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa467d8: stur            x1, [fp, #-0x38]
    // 0xa467dc: StoreField: r1->field_27 = r0
    //     0xa467dc: stur            w0, [x1, #0x27]
    // 0xa467e0: d0 = 1.000000
    //     0xa467e0: fmov            d0, #1.00000000
    // 0xa467e4: StoreField: r1->field_7 = d0
    //     0xa467e4: stur            d0, [x1, #7]
    // 0xa467e8: ldur            x2, [fp, #-0x50]
    // 0xa467ec: ubfx            x2, x2, #0, #0x20
    // 0xa467f0: and             w3, w2, #0xff
    // 0xa467f4: ubfx            x3, x3, #0, #0x20
    // 0xa467f8: scvtf           d1, x3
    // 0xa467fc: d2 = 255.000000
    //     0xa467fc: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa46800: fdiv            d3, d1, d2
    // 0xa46804: StoreField: r1->field_f = d3
    //     0xa46804: stur            d3, [x1, #0xf]
    // 0xa46808: ldur            x2, [fp, #-0x48]
    // 0xa4680c: ubfx            x2, x2, #0, #0x20
    // 0xa46810: and             w3, w2, #0xff
    // 0xa46814: ubfx            x3, x3, #0, #0x20
    // 0xa46818: scvtf           d1, x3
    // 0xa4681c: fdiv            d3, d1, d2
    // 0xa46820: ArrayStore: r1[0] = d3  ; List_8
    //     0xa46820: stur            d3, [x1, #0x17]
    // 0xa46824: ldur            x2, [fp, #-0x40]
    // 0xa46828: ubfx            x2, x2, #0, #0x20
    // 0xa4682c: and             w3, w2, #0xff
    // 0xa46830: ubfx            x3, x3, #0, #0x20
    // 0xa46834: scvtf           d1, x3
    // 0xa46838: fdiv            d3, d1, d2
    // 0xa4683c: StoreField: r1->field_1f = d3
    //     0xa4683c: stur            d3, [x1, #0x1f]
    // 0xa46840: r0 = BorderSide()
    //     0xa46840: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xa46844: mov             x1, x0
    // 0xa46848: ldur            x0, [fp, #-0x38]
    // 0xa4684c: stur            x1, [fp, #-0x60]
    // 0xa46850: StoreField: r1->field_7 = r0
    //     0xa46850: stur            w0, [x1, #7]
    // 0xa46854: d0 = 1.000000
    //     0xa46854: fmov            d0, #1.00000000
    // 0xa46858: StoreField: r1->field_b = d0
    //     0xa46858: stur            d0, [x1, #0xb]
    // 0xa4685c: r0 = Instance_BorderStyle
    //     0xa4685c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xa46860: ldr             x0, [x0, #0xf68]
    // 0xa46864: StoreField: r1->field_13 = r0
    //     0xa46864: stur            w0, [x1, #0x13]
    // 0xa46868: d1 = -1.000000
    //     0xa46868: fmov            d1, #-1.00000000
    // 0xa4686c: ArrayStore: r1[0] = d1  ; List_8
    //     0xa4686c: stur            d1, [x1, #0x17]
    // 0xa46870: r0 = RoundedRectangleBorder()
    //     0xa46870: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa46874: mov             x1, x0
    // 0xa46878: ldur            x0, [fp, #-0x58]
    // 0xa4687c: StoreField: r1->field_b = r0
    //     0xa4687c: stur            w0, [x1, #0xb]
    // 0xa46880: ldur            x0, [fp, #-0x60]
    // 0xa46884: StoreField: r1->field_7 = r0
    //     0xa46884: stur            w0, [x1, #7]
    // 0xa46888: r16 = <RoundedRectangleBorder>
    //     0xa46888: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa4688c: ldr             x16, [x16, #0xf78]
    // 0xa46890: stp             x1, x16, [SP]
    // 0xa46894: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa46894: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa46898: r0 = all()
    //     0xa46898: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa4689c: stur            x0, [fp, #-0x38]
    // 0xa468a0: r0 = ButtonStyle()
    //     0xa468a0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa468a4: mov             x1, x0
    // 0xa468a8: ldur            x0, [fp, #-0x10]
    // 0xa468ac: stur            x1, [fp, #-0x58]
    // 0xa468b0: StoreField: r1->field_23 = r0
    //     0xa468b0: stur            w0, [x1, #0x23]
    // 0xa468b4: ldur            x0, [fp, #-0x38]
    // 0xa468b8: StoreField: r1->field_43 = r0
    //     0xa468b8: stur            w0, [x1, #0x43]
    // 0xa468bc: r0 = TextButtonThemeData()
    //     0xa468bc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa468c0: mov             x1, x0
    // 0xa468c4: ldur            x0, [fp, #-0x58]
    // 0xa468c8: stur            x1, [fp, #-0x10]
    // 0xa468cc: StoreField: r1->field_7 = r0
    //     0xa468cc: stur            w0, [x1, #7]
    // 0xa468d0: ldur            x0, [fp, #-8]
    // 0xa468d4: LoadField: r2 = r0->field_b
    //     0xa468d4: ldur            w2, [x0, #0xb]
    // 0xa468d8: DecompressPointer r2
    //     0xa468d8: add             x2, x2, HEAP, lsl #32
    // 0xa468dc: cmp             w2, NULL
    // 0xa468e0: b.eq            #0xa47018
    // 0xa468e4: LoadField: r3 = r2->field_33
    //     0xa468e4: ldur            w3, [x2, #0x33]
    // 0xa468e8: DecompressPointer r3
    //     0xa468e8: add             x3, x3, HEAP, lsl #32
    // 0xa468ec: LoadField: r2 = r3->field_3f
    //     0xa468ec: ldur            w2, [x3, #0x3f]
    // 0xa468f0: DecompressPointer r2
    //     0xa468f0: add             x2, x2, HEAP, lsl #32
    // 0xa468f4: cmp             w2, NULL
    // 0xa468f8: b.ne            #0xa46904
    // 0xa468fc: r3 = Null
    //     0xa468fc: mov             x3, NULL
    // 0xa46900: b               #0xa46928
    // 0xa46904: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa46904: ldur            w3, [x2, #0x17]
    // 0xa46908: DecompressPointer r3
    //     0xa46908: add             x3, x3, HEAP, lsl #32
    // 0xa4690c: cmp             w3, NULL
    // 0xa46910: b.ne            #0xa4691c
    // 0xa46914: r3 = Null
    //     0xa46914: mov             x3, NULL
    // 0xa46918: b               #0xa46928
    // 0xa4691c: LoadField: r4 = r3->field_7
    //     0xa4691c: ldur            w4, [x3, #7]
    // 0xa46920: DecompressPointer r4
    //     0xa46920: add             x4, x4, HEAP, lsl #32
    // 0xa46924: mov             x3, x4
    // 0xa46928: cmp             w3, NULL
    // 0xa4692c: b.ne            #0xa46938
    // 0xa46930: r3 = 0
    //     0xa46930: movz            x3, #0
    // 0xa46934: b               #0xa46948
    // 0xa46938: r4 = LoadInt32Instr(r3)
    //     0xa46938: sbfx            x4, x3, #1, #0x1f
    //     0xa4693c: tbz             w3, #0, #0xa46944
    //     0xa46940: ldur            x4, [x3, #7]
    // 0xa46944: mov             x3, x4
    // 0xa46948: stur            x3, [fp, #-0x50]
    // 0xa4694c: cmp             w2, NULL
    // 0xa46950: b.ne            #0xa4695c
    // 0xa46954: r4 = Null
    //     0xa46954: mov             x4, NULL
    // 0xa46958: b               #0xa46980
    // 0xa4695c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa4695c: ldur            w4, [x2, #0x17]
    // 0xa46960: DecompressPointer r4
    //     0xa46960: add             x4, x4, HEAP, lsl #32
    // 0xa46964: cmp             w4, NULL
    // 0xa46968: b.ne            #0xa46974
    // 0xa4696c: r4 = Null
    //     0xa4696c: mov             x4, NULL
    // 0xa46970: b               #0xa46980
    // 0xa46974: LoadField: r5 = r4->field_b
    //     0xa46974: ldur            w5, [x4, #0xb]
    // 0xa46978: DecompressPointer r5
    //     0xa46978: add             x5, x5, HEAP, lsl #32
    // 0xa4697c: mov             x4, x5
    // 0xa46980: cmp             w4, NULL
    // 0xa46984: b.ne            #0xa46990
    // 0xa46988: r4 = 0
    //     0xa46988: movz            x4, #0
    // 0xa4698c: b               #0xa469a0
    // 0xa46990: r5 = LoadInt32Instr(r4)
    //     0xa46990: sbfx            x5, x4, #1, #0x1f
    //     0xa46994: tbz             w4, #0, #0xa4699c
    //     0xa46998: ldur            x5, [x4, #7]
    // 0xa4699c: mov             x4, x5
    // 0xa469a0: stur            x4, [fp, #-0x48]
    // 0xa469a4: cmp             w2, NULL
    // 0xa469a8: b.ne            #0xa469b4
    // 0xa469ac: r2 = Null
    //     0xa469ac: mov             x2, NULL
    // 0xa469b0: b               #0xa469d4
    // 0xa469b4: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xa469b4: ldur            w5, [x2, #0x17]
    // 0xa469b8: DecompressPointer r5
    //     0xa469b8: add             x5, x5, HEAP, lsl #32
    // 0xa469bc: cmp             w5, NULL
    // 0xa469c0: b.ne            #0xa469cc
    // 0xa469c4: r2 = Null
    //     0xa469c4: mov             x2, NULL
    // 0xa469c8: b               #0xa469d4
    // 0xa469cc: LoadField: r2 = r5->field_f
    //     0xa469cc: ldur            w2, [x5, #0xf]
    // 0xa469d0: DecompressPointer r2
    //     0xa469d0: add             x2, x2, HEAP, lsl #32
    // 0xa469d4: cmp             w2, NULL
    // 0xa469d8: b.ne            #0xa469e4
    // 0xa469dc: r2 = 0
    //     0xa469dc: movz            x2, #0
    // 0xa469e0: b               #0xa469f4
    // 0xa469e4: r5 = LoadInt32Instr(r2)
    //     0xa469e4: sbfx            x5, x2, #1, #0x1f
    //     0xa469e8: tbz             w2, #0, #0xa469f0
    //     0xa469ec: ldur            x5, [x2, #7]
    // 0xa469f0: mov             x2, x5
    // 0xa469f4: stur            x2, [fp, #-0x40]
    // 0xa469f8: r0 = Color()
    //     0xa469f8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa469fc: mov             x1, x0
    // 0xa46a00: r0 = Instance_ColorSpace
    //     0xa46a00: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa46a04: StoreField: r1->field_27 = r0
    //     0xa46a04: stur            w0, [x1, #0x27]
    // 0xa46a08: d0 = 1.000000
    //     0xa46a08: fmov            d0, #1.00000000
    // 0xa46a0c: StoreField: r1->field_7 = d0
    //     0xa46a0c: stur            d0, [x1, #7]
    // 0xa46a10: ldur            x2, [fp, #-0x50]
    // 0xa46a14: ubfx            x2, x2, #0, #0x20
    // 0xa46a18: and             w3, w2, #0xff
    // 0xa46a1c: ubfx            x3, x3, #0, #0x20
    // 0xa46a20: scvtf           d1, x3
    // 0xa46a24: d2 = 255.000000
    //     0xa46a24: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa46a28: fdiv            d3, d1, d2
    // 0xa46a2c: StoreField: r1->field_f = d3
    //     0xa46a2c: stur            d3, [x1, #0xf]
    // 0xa46a30: ldur            x2, [fp, #-0x48]
    // 0xa46a34: ubfx            x2, x2, #0, #0x20
    // 0xa46a38: and             w3, w2, #0xff
    // 0xa46a3c: ubfx            x3, x3, #0, #0x20
    // 0xa46a40: scvtf           d1, x3
    // 0xa46a44: fdiv            d3, d1, d2
    // 0xa46a48: ArrayStore: r1[0] = d3  ; List_8
    //     0xa46a48: stur            d3, [x1, #0x17]
    // 0xa46a4c: ldur            x2, [fp, #-0x40]
    // 0xa46a50: ubfx            x2, x2, #0, #0x20
    // 0xa46a54: and             w3, w2, #0xff
    // 0xa46a58: ubfx            x3, x3, #0, #0x20
    // 0xa46a5c: scvtf           d1, x3
    // 0xa46a60: fdiv            d3, d1, d2
    // 0xa46a64: StoreField: r1->field_1f = d3
    //     0xa46a64: stur            d3, [x1, #0x1f]
    // 0xa46a68: r16 = 16.000000
    //     0xa46a68: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa46a6c: ldr             x16, [x16, #0x188]
    // 0xa46a70: stp             x1, x16, [SP]
    // 0xa46a74: ldur            x1, [fp, #-0x20]
    // 0xa46a78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa46a78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa46a7c: ldr             x4, [x4, #0xaa0]
    // 0xa46a80: r0 = copyWith()
    //     0xa46a80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa46a84: stur            x0, [fp, #-0x20]
    // 0xa46a88: r0 = Text()
    //     0xa46a88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa46a8c: mov             x3, x0
    // 0xa46a90: r0 = "Skip"
    //     0xa46a90: add             x0, PP, #0x58, lsl #12  ; [pp+0x587f8] "Skip"
    //     0xa46a94: ldr             x0, [x0, #0x7f8]
    // 0xa46a98: stur            x3, [fp, #-0x38]
    // 0xa46a9c: StoreField: r3->field_b = r0
    //     0xa46a9c: stur            w0, [x3, #0xb]
    // 0xa46aa0: ldur            x0, [fp, #-0x20]
    // 0xa46aa4: StoreField: r3->field_13 = r0
    //     0xa46aa4: stur            w0, [x3, #0x13]
    // 0xa46aa8: ldur            x2, [fp, #-0x18]
    // 0xa46aac: r1 = Function '<anonymous closure>':.
    //     0xa46aac: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab80] AnonymousClosure: (0xa47708), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa46ab0: ldr             x1, [x1, #0xb80]
    // 0xa46ab4: r0 = AllocateClosure()
    //     0xa46ab4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa46ab8: stur            x0, [fp, #-0x20]
    // 0xa46abc: r0 = TextButton()
    //     0xa46abc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa46ac0: mov             x1, x0
    // 0xa46ac4: ldur            x0, [fp, #-0x20]
    // 0xa46ac8: stur            x1, [fp, #-0x58]
    // 0xa46acc: StoreField: r1->field_b = r0
    //     0xa46acc: stur            w0, [x1, #0xb]
    // 0xa46ad0: r0 = false
    //     0xa46ad0: add             x0, NULL, #0x30  ; false
    // 0xa46ad4: StoreField: r1->field_27 = r0
    //     0xa46ad4: stur            w0, [x1, #0x27]
    // 0xa46ad8: r2 = true
    //     0xa46ad8: add             x2, NULL, #0x20  ; true
    // 0xa46adc: StoreField: r1->field_2f = r2
    //     0xa46adc: stur            w2, [x1, #0x2f]
    // 0xa46ae0: ldur            x3, [fp, #-0x38]
    // 0xa46ae4: StoreField: r1->field_37 = r3
    //     0xa46ae4: stur            w3, [x1, #0x37]
    // 0xa46ae8: r0 = TextButtonTheme()
    //     0xa46ae8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa46aec: mov             x2, x0
    // 0xa46af0: ldur            x0, [fp, #-0x10]
    // 0xa46af4: stur            x2, [fp, #-0x20]
    // 0xa46af8: StoreField: r2->field_f = r0
    //     0xa46af8: stur            w0, [x2, #0xf]
    // 0xa46afc: ldur            x0, [fp, #-0x58]
    // 0xa46b00: StoreField: r2->field_b = r0
    //     0xa46b00: stur            w0, [x2, #0xb]
    // 0xa46b04: r1 = <FlexParentData>
    //     0xa46b04: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa46b08: ldr             x1, [x1, #0xe00]
    // 0xa46b0c: r0 = Expanded()
    //     0xa46b0c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa46b10: mov             x1, x0
    // 0xa46b14: r0 = 1
    //     0xa46b14: movz            x0, #0x1
    // 0xa46b18: stur            x1, [fp, #-0x10]
    // 0xa46b1c: StoreField: r1->field_13 = r0
    //     0xa46b1c: stur            x0, [x1, #0x13]
    // 0xa46b20: r2 = Instance_FlexFit
    //     0xa46b20: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa46b24: ldr             x2, [x2, #0xe08]
    // 0xa46b28: StoreField: r1->field_1b = r2
    //     0xa46b28: stur            w2, [x1, #0x1b]
    // 0xa46b2c: ldur            x3, [fp, #-0x20]
    // 0xa46b30: StoreField: r1->field_b = r3
    //     0xa46b30: stur            w3, [x1, #0xb]
    // 0xa46b34: r16 = <EdgeInsets>
    //     0xa46b34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa46b38: ldr             x16, [x16, #0xda0]
    // 0xa46b3c: r30 = Instance_EdgeInsets
    //     0xa46b3c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa46b40: ldr             lr, [lr, #0x1f0]
    // 0xa46b44: stp             lr, x16, [SP]
    // 0xa46b48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa46b48: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa46b4c: r0 = all()
    //     0xa46b4c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa46b50: mov             x1, x0
    // 0xa46b54: ldur            x0, [fp, #-8]
    // 0xa46b58: stur            x1, [fp, #-0x20]
    // 0xa46b5c: LoadField: r2 = r0->field_b
    //     0xa46b5c: ldur            w2, [x0, #0xb]
    // 0xa46b60: DecompressPointer r2
    //     0xa46b60: add             x2, x2, HEAP, lsl #32
    // 0xa46b64: cmp             w2, NULL
    // 0xa46b68: b.eq            #0xa4701c
    // 0xa46b6c: LoadField: r0 = r2->field_33
    //     0xa46b6c: ldur            w0, [x2, #0x33]
    // 0xa46b70: DecompressPointer r0
    //     0xa46b70: add             x0, x0, HEAP, lsl #32
    // 0xa46b74: LoadField: r2 = r0->field_3f
    //     0xa46b74: ldur            w2, [x0, #0x3f]
    // 0xa46b78: DecompressPointer r2
    //     0xa46b78: add             x2, x2, HEAP, lsl #32
    // 0xa46b7c: cmp             w2, NULL
    // 0xa46b80: b.ne            #0xa46b8c
    // 0xa46b84: r0 = Null
    //     0xa46b84: mov             x0, NULL
    // 0xa46b88: b               #0xa46bb0
    // 0xa46b8c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa46b8c: ldur            w0, [x2, #0x17]
    // 0xa46b90: DecompressPointer r0
    //     0xa46b90: add             x0, x0, HEAP, lsl #32
    // 0xa46b94: cmp             w0, NULL
    // 0xa46b98: b.ne            #0xa46ba4
    // 0xa46b9c: r0 = Null
    //     0xa46b9c: mov             x0, NULL
    // 0xa46ba0: b               #0xa46bb0
    // 0xa46ba4: LoadField: r3 = r0->field_7
    //     0xa46ba4: ldur            w3, [x0, #7]
    // 0xa46ba8: DecompressPointer r3
    //     0xa46ba8: add             x3, x3, HEAP, lsl #32
    // 0xa46bac: mov             x0, x3
    // 0xa46bb0: cmp             w0, NULL
    // 0xa46bb4: b.ne            #0xa46bc0
    // 0xa46bb8: r0 = 0
    //     0xa46bb8: movz            x0, #0
    // 0xa46bbc: b               #0xa46bd0
    // 0xa46bc0: r3 = LoadInt32Instr(r0)
    //     0xa46bc0: sbfx            x3, x0, #1, #0x1f
    //     0xa46bc4: tbz             w0, #0, #0xa46bcc
    //     0xa46bc8: ldur            x3, [x0, #7]
    // 0xa46bcc: mov             x0, x3
    // 0xa46bd0: stur            x0, [fp, #-0x50]
    // 0xa46bd4: cmp             w2, NULL
    // 0xa46bd8: b.ne            #0xa46be4
    // 0xa46bdc: r3 = Null
    //     0xa46bdc: mov             x3, NULL
    // 0xa46be0: b               #0xa46c08
    // 0xa46be4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa46be4: ldur            w3, [x2, #0x17]
    // 0xa46be8: DecompressPointer r3
    //     0xa46be8: add             x3, x3, HEAP, lsl #32
    // 0xa46bec: cmp             w3, NULL
    // 0xa46bf0: b.ne            #0xa46bfc
    // 0xa46bf4: r3 = Null
    //     0xa46bf4: mov             x3, NULL
    // 0xa46bf8: b               #0xa46c08
    // 0xa46bfc: LoadField: r4 = r3->field_b
    //     0xa46bfc: ldur            w4, [x3, #0xb]
    // 0xa46c00: DecompressPointer r4
    //     0xa46c00: add             x4, x4, HEAP, lsl #32
    // 0xa46c04: mov             x3, x4
    // 0xa46c08: cmp             w3, NULL
    // 0xa46c0c: b.ne            #0xa46c18
    // 0xa46c10: r3 = 0
    //     0xa46c10: movz            x3, #0
    // 0xa46c14: b               #0xa46c28
    // 0xa46c18: r4 = LoadInt32Instr(r3)
    //     0xa46c18: sbfx            x4, x3, #1, #0x1f
    //     0xa46c1c: tbz             w3, #0, #0xa46c24
    //     0xa46c20: ldur            x4, [x3, #7]
    // 0xa46c24: mov             x3, x4
    // 0xa46c28: stur            x3, [fp, #-0x48]
    // 0xa46c2c: cmp             w2, NULL
    // 0xa46c30: b.ne            #0xa46c3c
    // 0xa46c34: r2 = Null
    //     0xa46c34: mov             x2, NULL
    // 0xa46c38: b               #0xa46c5c
    // 0xa46c3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa46c3c: ldur            w4, [x2, #0x17]
    // 0xa46c40: DecompressPointer r4
    //     0xa46c40: add             x4, x4, HEAP, lsl #32
    // 0xa46c44: cmp             w4, NULL
    // 0xa46c48: b.ne            #0xa46c54
    // 0xa46c4c: r2 = Null
    //     0xa46c4c: mov             x2, NULL
    // 0xa46c50: b               #0xa46c5c
    // 0xa46c54: LoadField: r2 = r4->field_f
    //     0xa46c54: ldur            w2, [x4, #0xf]
    // 0xa46c58: DecompressPointer r2
    //     0xa46c58: add             x2, x2, HEAP, lsl #32
    // 0xa46c5c: cmp             w2, NULL
    // 0xa46c60: b.ne            #0xa46c6c
    // 0xa46c64: r6 = 0
    //     0xa46c64: movz            x6, #0
    // 0xa46c68: b               #0xa46c7c
    // 0xa46c6c: r4 = LoadInt32Instr(r2)
    //     0xa46c6c: sbfx            x4, x2, #1, #0x1f
    //     0xa46c70: tbz             w2, #0, #0xa46c78
    //     0xa46c74: ldur            x4, [x2, #7]
    // 0xa46c78: mov             x6, x4
    // 0xa46c7c: ldur            x5, [fp, #-0x28]
    // 0xa46c80: ldur            x4, [fp, #-0x30]
    // 0xa46c84: ldur            x2, [fp, #-0x10]
    // 0xa46c88: stur            x6, [fp, #-0x40]
    // 0xa46c8c: r0 = Color()
    //     0xa46c8c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa46c90: mov             x1, x0
    // 0xa46c94: r0 = Instance_ColorSpace
    //     0xa46c94: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa46c98: StoreField: r1->field_27 = r0
    //     0xa46c98: stur            w0, [x1, #0x27]
    // 0xa46c9c: d0 = 1.000000
    //     0xa46c9c: fmov            d0, #1.00000000
    // 0xa46ca0: StoreField: r1->field_7 = d0
    //     0xa46ca0: stur            d0, [x1, #7]
    // 0xa46ca4: ldur            x0, [fp, #-0x50]
    // 0xa46ca8: ubfx            x0, x0, #0, #0x20
    // 0xa46cac: and             w2, w0, #0xff
    // 0xa46cb0: ubfx            x2, x2, #0, #0x20
    // 0xa46cb4: scvtf           d0, x2
    // 0xa46cb8: d1 = 255.000000
    //     0xa46cb8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa46cbc: fdiv            d2, d0, d1
    // 0xa46cc0: StoreField: r1->field_f = d2
    //     0xa46cc0: stur            d2, [x1, #0xf]
    // 0xa46cc4: ldur            x0, [fp, #-0x48]
    // 0xa46cc8: ubfx            x0, x0, #0, #0x20
    // 0xa46ccc: and             w2, w0, #0xff
    // 0xa46cd0: ubfx            x2, x2, #0, #0x20
    // 0xa46cd4: scvtf           d0, x2
    // 0xa46cd8: fdiv            d2, d0, d1
    // 0xa46cdc: ArrayStore: r1[0] = d2  ; List_8
    //     0xa46cdc: stur            d2, [x1, #0x17]
    // 0xa46ce0: ldur            x0, [fp, #-0x40]
    // 0xa46ce4: ubfx            x0, x0, #0, #0x20
    // 0xa46ce8: and             w2, w0, #0xff
    // 0xa46cec: ubfx            x2, x2, #0, #0x20
    // 0xa46cf0: scvtf           d0, x2
    // 0xa46cf4: fdiv            d2, d0, d1
    // 0xa46cf8: StoreField: r1->field_1f = d2
    //     0xa46cf8: stur            d2, [x1, #0x1f]
    // 0xa46cfc: r16 = <Color>
    //     0xa46cfc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa46d00: ldr             x16, [x16, #0xf80]
    // 0xa46d04: stp             x1, x16, [SP]
    // 0xa46d08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa46d08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa46d0c: r0 = all()
    //     0xa46d0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa46d10: stur            x0, [fp, #-8]
    // 0xa46d14: r0 = Radius()
    //     0xa46d14: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa46d18: d0 = 12.000000
    //     0xa46d18: fmov            d0, #12.00000000
    // 0xa46d1c: stur            x0, [fp, #-0x38]
    // 0xa46d20: StoreField: r0->field_7 = d0
    //     0xa46d20: stur            d0, [x0, #7]
    // 0xa46d24: StoreField: r0->field_f = d0
    //     0xa46d24: stur            d0, [x0, #0xf]
    // 0xa46d28: r0 = BorderRadius()
    //     0xa46d28: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa46d2c: mov             x1, x0
    // 0xa46d30: ldur            x0, [fp, #-0x38]
    // 0xa46d34: stur            x1, [fp, #-0x58]
    // 0xa46d38: StoreField: r1->field_7 = r0
    //     0xa46d38: stur            w0, [x1, #7]
    // 0xa46d3c: StoreField: r1->field_b = r0
    //     0xa46d3c: stur            w0, [x1, #0xb]
    // 0xa46d40: StoreField: r1->field_f = r0
    //     0xa46d40: stur            w0, [x1, #0xf]
    // 0xa46d44: StoreField: r1->field_13 = r0
    //     0xa46d44: stur            w0, [x1, #0x13]
    // 0xa46d48: r0 = RoundedRectangleBorder()
    //     0xa46d48: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa46d4c: mov             x1, x0
    // 0xa46d50: ldur            x0, [fp, #-0x58]
    // 0xa46d54: StoreField: r1->field_b = r0
    //     0xa46d54: stur            w0, [x1, #0xb]
    // 0xa46d58: r0 = Instance_BorderSide
    //     0xa46d58: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa46d5c: ldr             x0, [x0, #0xe20]
    // 0xa46d60: StoreField: r1->field_7 = r0
    //     0xa46d60: stur            w0, [x1, #7]
    // 0xa46d64: r16 = <RoundedRectangleBorder>
    //     0xa46d64: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa46d68: ldr             x16, [x16, #0xf78]
    // 0xa46d6c: stp             x1, x16, [SP]
    // 0xa46d70: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa46d70: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa46d74: r0 = all()
    //     0xa46d74: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa46d78: stur            x0, [fp, #-0x38]
    // 0xa46d7c: r0 = ButtonStyle()
    //     0xa46d7c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa46d80: mov             x1, x0
    // 0xa46d84: ldur            x0, [fp, #-8]
    // 0xa46d88: stur            x1, [fp, #-0x58]
    // 0xa46d8c: StoreField: r1->field_b = r0
    //     0xa46d8c: stur            w0, [x1, #0xb]
    // 0xa46d90: ldur            x0, [fp, #-0x20]
    // 0xa46d94: StoreField: r1->field_23 = r0
    //     0xa46d94: stur            w0, [x1, #0x23]
    // 0xa46d98: ldur            x0, [fp, #-0x38]
    // 0xa46d9c: StoreField: r1->field_43 = r0
    //     0xa46d9c: stur            w0, [x1, #0x43]
    // 0xa46da0: r0 = TextButtonThemeData()
    //     0xa46da0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa46da4: mov             x2, x0
    // 0xa46da8: ldur            x0, [fp, #-0x58]
    // 0xa46dac: stur            x2, [fp, #-8]
    // 0xa46db0: StoreField: r2->field_7 = r0
    //     0xa46db0: stur            w0, [x2, #7]
    // 0xa46db4: ldur            x0, [fp, #-0x28]
    // 0xa46db8: LoadField: r1 = r0->field_27
    //     0xa46db8: ldur            w1, [x0, #0x27]
    // 0xa46dbc: DecompressPointer r1
    //     0xa46dbc: add             x1, x1, HEAP, lsl #32
    // 0xa46dc0: r16 = 16.000000
    //     0xa46dc0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa46dc4: ldr             x16, [x16, #0x188]
    // 0xa46dc8: r30 = Instance_Color
    //     0xa46dc8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa46dcc: stp             lr, x16, [SP]
    // 0xa46dd0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa46dd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa46dd4: ldr             x4, [x4, #0xaa0]
    // 0xa46dd8: r0 = copyWith()
    //     0xa46dd8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa46ddc: stur            x0, [fp, #-0x20]
    // 0xa46de0: r0 = Text()
    //     0xa46de0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa46de4: mov             x3, x0
    // 0xa46de8: r0 = "Yes,Continue"
    //     0xa46de8: add             x0, PP, #0x5a, lsl #12  ; [pp+0x5ab88] "Yes,Continue"
    //     0xa46dec: ldr             x0, [x0, #0xb88]
    // 0xa46df0: stur            x3, [fp, #-0x28]
    // 0xa46df4: StoreField: r3->field_b = r0
    //     0xa46df4: stur            w0, [x3, #0xb]
    // 0xa46df8: ldur            x0, [fp, #-0x20]
    // 0xa46dfc: StoreField: r3->field_13 = r0
    //     0xa46dfc: stur            w0, [x3, #0x13]
    // 0xa46e00: ldur            x2, [fp, #-0x18]
    // 0xa46e04: r1 = Function '<anonymous closure>':.
    //     0xa46e04: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab90] AnonymousClosure: (0xa47044), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa46e08: ldr             x1, [x1, #0xb90]
    // 0xa46e0c: r0 = AllocateClosure()
    //     0xa46e0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa46e10: stur            x0, [fp, #-0x18]
    // 0xa46e14: r0 = TextButton()
    //     0xa46e14: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa46e18: mov             x1, x0
    // 0xa46e1c: ldur            x0, [fp, #-0x18]
    // 0xa46e20: stur            x1, [fp, #-0x20]
    // 0xa46e24: StoreField: r1->field_b = r0
    //     0xa46e24: stur            w0, [x1, #0xb]
    // 0xa46e28: r0 = false
    //     0xa46e28: add             x0, NULL, #0x30  ; false
    // 0xa46e2c: StoreField: r1->field_27 = r0
    //     0xa46e2c: stur            w0, [x1, #0x27]
    // 0xa46e30: r0 = true
    //     0xa46e30: add             x0, NULL, #0x20  ; true
    // 0xa46e34: StoreField: r1->field_2f = r0
    //     0xa46e34: stur            w0, [x1, #0x2f]
    // 0xa46e38: ldur            x0, [fp, #-0x28]
    // 0xa46e3c: StoreField: r1->field_37 = r0
    //     0xa46e3c: stur            w0, [x1, #0x37]
    // 0xa46e40: r0 = TextButtonTheme()
    //     0xa46e40: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa46e44: mov             x2, x0
    // 0xa46e48: ldur            x0, [fp, #-8]
    // 0xa46e4c: stur            x2, [fp, #-0x18]
    // 0xa46e50: StoreField: r2->field_f = r0
    //     0xa46e50: stur            w0, [x2, #0xf]
    // 0xa46e54: ldur            x0, [fp, #-0x20]
    // 0xa46e58: StoreField: r2->field_b = r0
    //     0xa46e58: stur            w0, [x2, #0xb]
    // 0xa46e5c: r1 = <FlexParentData>
    //     0xa46e5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa46e60: ldr             x1, [x1, #0xe00]
    // 0xa46e64: r0 = Expanded()
    //     0xa46e64: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa46e68: mov             x3, x0
    // 0xa46e6c: r0 = 1
    //     0xa46e6c: movz            x0, #0x1
    // 0xa46e70: stur            x3, [fp, #-8]
    // 0xa46e74: StoreField: r3->field_13 = r0
    //     0xa46e74: stur            x0, [x3, #0x13]
    // 0xa46e78: r0 = Instance_FlexFit
    //     0xa46e78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa46e7c: ldr             x0, [x0, #0xe08]
    // 0xa46e80: StoreField: r3->field_1b = r0
    //     0xa46e80: stur            w0, [x3, #0x1b]
    // 0xa46e84: ldur            x0, [fp, #-0x18]
    // 0xa46e88: StoreField: r3->field_b = r0
    //     0xa46e88: stur            w0, [x3, #0xb]
    // 0xa46e8c: r1 = Null
    //     0xa46e8c: mov             x1, NULL
    // 0xa46e90: r2 = 6
    //     0xa46e90: movz            x2, #0x6
    // 0xa46e94: r0 = AllocateArray()
    //     0xa46e94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa46e98: mov             x2, x0
    // 0xa46e9c: ldur            x0, [fp, #-0x10]
    // 0xa46ea0: stur            x2, [fp, #-0x18]
    // 0xa46ea4: StoreField: r2->field_f = r0
    //     0xa46ea4: stur            w0, [x2, #0xf]
    // 0xa46ea8: r16 = Instance_SizedBox
    //     0xa46ea8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xa46eac: ldr             x16, [x16, #0x998]
    // 0xa46eb0: StoreField: r2->field_13 = r16
    //     0xa46eb0: stur            w16, [x2, #0x13]
    // 0xa46eb4: ldur            x0, [fp, #-8]
    // 0xa46eb8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa46eb8: stur            w0, [x2, #0x17]
    // 0xa46ebc: r1 = <Widget>
    //     0xa46ebc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa46ec0: r0 = AllocateGrowableArray()
    //     0xa46ec0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa46ec4: mov             x1, x0
    // 0xa46ec8: ldur            x0, [fp, #-0x18]
    // 0xa46ecc: stur            x1, [fp, #-8]
    // 0xa46ed0: StoreField: r1->field_f = r0
    //     0xa46ed0: stur            w0, [x1, #0xf]
    // 0xa46ed4: r2 = 6
    //     0xa46ed4: movz            x2, #0x6
    // 0xa46ed8: StoreField: r1->field_b = r2
    //     0xa46ed8: stur            w2, [x1, #0xb]
    // 0xa46edc: r0 = Row()
    //     0xa46edc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa46ee0: mov             x3, x0
    // 0xa46ee4: r0 = Instance_Axis
    //     0xa46ee4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa46ee8: stur            x3, [fp, #-0x10]
    // 0xa46eec: StoreField: r3->field_f = r0
    //     0xa46eec: stur            w0, [x3, #0xf]
    // 0xa46ef0: r0 = Instance_MainAxisAlignment
    //     0xa46ef0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa46ef4: ldr             x0, [x0, #0xd10]
    // 0xa46ef8: StoreField: r3->field_13 = r0
    //     0xa46ef8: stur            w0, [x3, #0x13]
    // 0xa46efc: r0 = Instance_MainAxisSize
    //     0xa46efc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa46f00: ldr             x0, [x0, #0xa10]
    // 0xa46f04: ArrayStore: r3[0] = r0  ; List_4
    //     0xa46f04: stur            w0, [x3, #0x17]
    // 0xa46f08: r0 = Instance_CrossAxisAlignment
    //     0xa46f08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa46f0c: ldr             x0, [x0, #0xa18]
    // 0xa46f10: StoreField: r3->field_1b = r0
    //     0xa46f10: stur            w0, [x3, #0x1b]
    // 0xa46f14: r0 = Instance_VerticalDirection
    //     0xa46f14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa46f18: ldr             x0, [x0, #0xa20]
    // 0xa46f1c: StoreField: r3->field_23 = r0
    //     0xa46f1c: stur            w0, [x3, #0x23]
    // 0xa46f20: r4 = Instance_Clip
    //     0xa46f20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa46f24: ldr             x4, [x4, #0x38]
    // 0xa46f28: StoreField: r3->field_2b = r4
    //     0xa46f28: stur            w4, [x3, #0x2b]
    // 0xa46f2c: StoreField: r3->field_2f = rZR
    //     0xa46f2c: stur            xzr, [x3, #0x2f]
    // 0xa46f30: ldur            x1, [fp, #-8]
    // 0xa46f34: StoreField: r3->field_b = r1
    //     0xa46f34: stur            w1, [x3, #0xb]
    // 0xa46f38: r1 = Null
    //     0xa46f38: mov             x1, NULL
    // 0xa46f3c: r2 = 6
    //     0xa46f3c: movz            x2, #0x6
    // 0xa46f40: r0 = AllocateArray()
    //     0xa46f40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa46f44: mov             x2, x0
    // 0xa46f48: ldur            x0, [fp, #-0x30]
    // 0xa46f4c: stur            x2, [fp, #-8]
    // 0xa46f50: StoreField: r2->field_f = r0
    //     0xa46f50: stur            w0, [x2, #0xf]
    // 0xa46f54: r16 = Instance_SizedBox
    //     0xa46f54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xa46f58: ldr             x16, [x16, #0x9f0]
    // 0xa46f5c: StoreField: r2->field_13 = r16
    //     0xa46f5c: stur            w16, [x2, #0x13]
    // 0xa46f60: ldur            x0, [fp, #-0x10]
    // 0xa46f64: ArrayStore: r2[0] = r0  ; List_4
    //     0xa46f64: stur            w0, [x2, #0x17]
    // 0xa46f68: r1 = <Widget>
    //     0xa46f68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa46f6c: r0 = AllocateGrowableArray()
    //     0xa46f6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa46f70: mov             x1, x0
    // 0xa46f74: ldur            x0, [fp, #-8]
    // 0xa46f78: stur            x1, [fp, #-0x10]
    // 0xa46f7c: StoreField: r1->field_f = r0
    //     0xa46f7c: stur            w0, [x1, #0xf]
    // 0xa46f80: r0 = 6
    //     0xa46f80: movz            x0, #0x6
    // 0xa46f84: StoreField: r1->field_b = r0
    //     0xa46f84: stur            w0, [x1, #0xb]
    // 0xa46f88: r0 = Column()
    //     0xa46f88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa46f8c: mov             x1, x0
    // 0xa46f90: r0 = Instance_Axis
    //     0xa46f90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa46f94: stur            x1, [fp, #-8]
    // 0xa46f98: StoreField: r1->field_f = r0
    //     0xa46f98: stur            w0, [x1, #0xf]
    // 0xa46f9c: r0 = Instance_MainAxisAlignment
    //     0xa46f9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa46fa0: ldr             x0, [x0, #0xa08]
    // 0xa46fa4: StoreField: r1->field_13 = r0
    //     0xa46fa4: stur            w0, [x1, #0x13]
    // 0xa46fa8: r0 = Instance_MainAxisSize
    //     0xa46fa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xa46fac: ldr             x0, [x0, #0xdd0]
    // 0xa46fb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa46fb0: stur            w0, [x1, #0x17]
    // 0xa46fb4: r0 = Instance_CrossAxisAlignment
    //     0xa46fb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa46fb8: ldr             x0, [x0, #0x890]
    // 0xa46fbc: StoreField: r1->field_1b = r0
    //     0xa46fbc: stur            w0, [x1, #0x1b]
    // 0xa46fc0: r0 = Instance_VerticalDirection
    //     0xa46fc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa46fc4: ldr             x0, [x0, #0xa20]
    // 0xa46fc8: StoreField: r1->field_23 = r0
    //     0xa46fc8: stur            w0, [x1, #0x23]
    // 0xa46fcc: r0 = Instance_Clip
    //     0xa46fcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa46fd0: ldr             x0, [x0, #0x38]
    // 0xa46fd4: StoreField: r1->field_2b = r0
    //     0xa46fd4: stur            w0, [x1, #0x2b]
    // 0xa46fd8: StoreField: r1->field_2f = rZR
    //     0xa46fd8: stur            xzr, [x1, #0x2f]
    // 0xa46fdc: ldur            x0, [fp, #-0x10]
    // 0xa46fe0: StoreField: r1->field_b = r0
    //     0xa46fe0: stur            w0, [x1, #0xb]
    // 0xa46fe4: r0 = Padding()
    //     0xa46fe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa46fe8: r1 = Instance_EdgeInsets
    //     0xa46fe8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa46fec: ldr             x1, [x1, #0x1f0]
    // 0xa46ff0: StoreField: r0->field_f = r1
    //     0xa46ff0: stur            w1, [x0, #0xf]
    // 0xa46ff4: ldur            x1, [fp, #-8]
    // 0xa46ff8: StoreField: r0->field_b = r1
    //     0xa46ff8: stur            w1, [x0, #0xb]
    // 0xa46ffc: LeaveFrame
    //     0xa46ffc: mov             SP, fp
    //     0xa47000: ldp             fp, lr, [SP], #0x10
    // 0xa47004: ret
    //     0xa47004: ret             
    // 0xa47008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4700c: b               #0xa46438
    // 0xa47010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4701c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4701c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa47044, size: 0x314
    // 0xa47044: EnterFrame
    //     0xa47044: stp             fp, lr, [SP, #-0x10]!
    //     0xa47048: mov             fp, SP
    // 0xa4704c: AllocStack(0x28)
    //     0xa4704c: sub             SP, SP, #0x28
    // 0xa47050: SetupParameters()
    //     0xa47050: ldr             x0, [fp, #0x10]
    //     0xa47054: ldur            w2, [x0, #0x17]
    //     0xa47058: add             x2, x2, HEAP, lsl #32
    //     0xa4705c: stur            x2, [fp, #-8]
    // 0xa47060: CheckStackOverflow
    //     0xa47060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47064: cmp             SP, x16
    //     0xa47068: b.ls            #0xa4734c
    // 0xa4706c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4706c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa47070: ldr             x0, [x0, #0x1c80]
    //     0xa47074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa47078: cmp             w0, w16
    //     0xa4707c: b.ne            #0xa47088
    //     0xa47080: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa47084: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa47088: str             NULL, [SP]
    // 0xa4708c: r4 = const [0x1, 0, 0, 0, null]
    //     0xa4708c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa47090: r0 = GetNavigation.back()
    //     0xa47090: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa47094: r1 = Null
    //     0xa47094: mov             x1, NULL
    // 0xa47098: r2 = 44
    //     0xa47098: movz            x2, #0x2c
    // 0xa4709c: r0 = AllocateArray()
    //     0xa4709c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa470a0: mov             x2, x0
    // 0xa470a4: r16 = "customizedResponse"
    //     0xa470a4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] "customizedResponse"
    //     0xa470a8: ldr             x16, [x16, #0x38]
    // 0xa470ac: StoreField: r2->field_f = r16
    //     0xa470ac: stur            w16, [x2, #0xf]
    // 0xa470b0: ldur            x3, [fp, #-8]
    // 0xa470b4: LoadField: r0 = r3->field_f
    //     0xa470b4: ldur            w0, [x3, #0xf]
    // 0xa470b8: DecompressPointer r0
    //     0xa470b8: add             x0, x0, HEAP, lsl #32
    // 0xa470bc: LoadField: r4 = r0->field_b
    //     0xa470bc: ldur            w4, [x0, #0xb]
    // 0xa470c0: DecompressPointer r4
    //     0xa470c0: add             x4, x4, HEAP, lsl #32
    // 0xa470c4: cmp             w4, NULL
    // 0xa470c8: b.eq            #0xa47354
    // 0xa470cc: LoadField: r0 = r4->field_b
    //     0xa470cc: ldur            w0, [x4, #0xb]
    // 0xa470d0: DecompressPointer r0
    //     0xa470d0: add             x0, x0, HEAP, lsl #32
    // 0xa470d4: StoreField: r2->field_13 = r0
    //     0xa470d4: stur            w0, [x2, #0x13]
    // 0xa470d8: r16 = "productId"
    //     0xa470d8: ldr             x16, [PP, #0x3970]  ; [pp+0x3970] "productId"
    // 0xa470dc: ArrayStore: r2[0] = r16  ; List_4
    //     0xa470dc: stur            w16, [x2, #0x17]
    // 0xa470e0: LoadField: r0 = r4->field_f
    //     0xa470e0: ldur            w0, [x4, #0xf]
    // 0xa470e4: DecompressPointer r0
    //     0xa470e4: add             x0, x0, HEAP, lsl #32
    // 0xa470e8: StoreField: r2->field_1b = r0
    //     0xa470e8: stur            w0, [x2, #0x1b]
    // 0xa470ec: r16 = "skuId"
    //     0xa470ec: add             x16, PP, #0x30, lsl #12  ; [pp+0x30040] "skuId"
    //     0xa470f0: ldr             x16, [x16, #0x40]
    // 0xa470f4: StoreField: r2->field_1f = r16
    //     0xa470f4: stur            w16, [x2, #0x1f]
    // 0xa470f8: LoadField: r0 = r4->field_13
    //     0xa470f8: ldur            w0, [x4, #0x13]
    // 0xa470fc: DecompressPointer r0
    //     0xa470fc: add             x0, x0, HEAP, lsl #32
    // 0xa47100: StoreField: r2->field_23 = r0
    //     0xa47100: stur            w0, [x2, #0x23]
    // 0xa47104: r16 = "customisedId"
    //     0xa47104: add             x16, PP, #0x30, lsl #12  ; [pp+0x30048] "customisedId"
    //     0xa47108: ldr             x16, [x16, #0x48]
    // 0xa4710c: StoreField: r2->field_27 = r16
    //     0xa4710c: stur            w16, [x2, #0x27]
    // 0xa47110: r16 = ""
    //     0xa47110: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa47114: StoreField: r2->field_2b = r16
    //     0xa47114: stur            w16, [x2, #0x2b]
    // 0xa47118: r16 = "addTypeValue"
    //     0xa47118: add             x16, PP, #0x30, lsl #12  ; [pp+0x30050] "addTypeValue"
    //     0xa4711c: ldr             x16, [x16, #0x50]
    // 0xa47120: StoreField: r2->field_2f = r16
    //     0xa47120: stur            w16, [x2, #0x2f]
    // 0xa47124: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xa47124: ldur            x5, [x4, #0x17]
    // 0xa47128: r0 = BoxInt64Instr(r5)
    //     0xa47128: sbfiz           x0, x5, #1, #0x1f
    //     0xa4712c: cmp             x5, x0, asr #1
    //     0xa47130: b.eq            #0xa4713c
    //     0xa47134: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47138: stur            x5, [x0, #7]
    // 0xa4713c: mov             x1, x2
    // 0xa47140: ArrayStore: r1[9] = r0  ; List_4
    //     0xa47140: add             x25, x1, #0x33
    //     0xa47144: str             w0, [x25]
    //     0xa47148: tbz             w0, #0, #0xa47164
    //     0xa4714c: ldurb           w16, [x1, #-1]
    //     0xa47150: ldurb           w17, [x0, #-1]
    //     0xa47154: and             x16, x17, x16, lsr #2
    //     0xa47158: tst             x16, HEAP, lsr #32
    //     0xa4715c: b.eq            #0xa47164
    //     0xa47160: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa47164: r16 = "sellingPrice"
    //     0xa47164: add             x16, PP, #0x30, lsl #12  ; [pp+0x30058] "sellingPrice"
    //     0xa47168: ldr             x16, [x16, #0x58]
    // 0xa4716c: StoreField: r2->field_37 = r16
    //     0xa4716c: stur            w16, [x2, #0x37]
    // 0xa47170: LoadField: r5 = r4->field_27
    //     0xa47170: ldur            x5, [x4, #0x27]
    // 0xa47174: r0 = BoxInt64Instr(r5)
    //     0xa47174: sbfiz           x0, x5, #1, #0x1f
    //     0xa47178: cmp             x5, x0, asr #1
    //     0xa4717c: b.eq            #0xa47188
    //     0xa47180: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47184: stur            x5, [x0, #7]
    // 0xa47188: mov             x1, x2
    // 0xa4718c: ArrayStore: r1[11] = r0  ; List_4
    //     0xa4718c: add             x25, x1, #0x3b
    //     0xa47190: str             w0, [x25]
    //     0xa47194: tbz             w0, #0, #0xa471b0
    //     0xa47198: ldurb           w16, [x1, #-1]
    //     0xa4719c: ldurb           w17, [x0, #-1]
    //     0xa471a0: and             x16, x17, x16, lsr #2
    //     0xa471a4: tst             x16, HEAP, lsr #32
    //     0xa471a8: b.eq            #0xa471b0
    //     0xa471ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa471b0: r16 = "comingFrom"
    //     0xa471b0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30060] "comingFrom"
    //     0xa471b4: ldr             x16, [x16, #0x60]
    // 0xa471b8: StoreField: r2->field_3f = r16
    //     0xa471b8: stur            w16, [x2, #0x3f]
    // 0xa471bc: LoadField: r0 = r4->field_2f
    //     0xa471bc: ldur            w0, [x4, #0x2f]
    // 0xa471c0: DecompressPointer r0
    //     0xa471c0: add             x0, x0, HEAP, lsl #32
    // 0xa471c4: mov             x1, x2
    // 0xa471c8: ArrayStore: r1[13] = r0  ; List_4
    //     0xa471c8: add             x25, x1, #0x43
    //     0xa471cc: str             w0, [x25]
    //     0xa471d0: tbz             w0, #0, #0xa471ec
    //     0xa471d4: ldurb           w16, [x1, #-1]
    //     0xa471d8: ldurb           w17, [x0, #-1]
    //     0xa471dc: and             x16, x17, x16, lsr #2
    //     0xa471e0: tst             x16, HEAP, lsr #32
    //     0xa471e4: b.eq            #0xa471ec
    //     0xa471e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa471ec: r16 = "skuData"
    //     0xa471ec: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] "skuData"
    //     0xa471f0: ldr             x16, [x16, #0x68]
    // 0xa471f4: StoreField: r2->field_47 = r16
    //     0xa471f4: stur            w16, [x2, #0x47]
    // 0xa471f8: LoadField: r0 = r4->field_37
    //     0xa471f8: ldur            w0, [x4, #0x37]
    // 0xa471fc: DecompressPointer r0
    //     0xa471fc: add             x0, x0, HEAP, lsl #32
    // 0xa47200: mov             x1, x2
    // 0xa47204: ArrayStore: r1[15] = r0  ; List_4
    //     0xa47204: add             x25, x1, #0x4b
    //     0xa47208: str             w0, [x25]
    //     0xa4720c: tbz             w0, #0, #0xa47228
    //     0xa47210: ldurb           w16, [x1, #-1]
    //     0xa47214: ldurb           w17, [x0, #-1]
    //     0xa47218: and             x16, x17, x16, lsr #2
    //     0xa4721c: tst             x16, HEAP, lsr #32
    //     0xa47220: b.eq            #0xa47228
    //     0xa47224: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa47228: r16 = "productTitle"
    //     0xa47228: add             x16, PP, #0x30, lsl #12  ; [pp+0x30070] "productTitle"
    //     0xa4722c: ldr             x16, [x16, #0x70]
    // 0xa47230: StoreField: r2->field_4f = r16
    //     0xa47230: stur            w16, [x2, #0x4f]
    // 0xa47234: LoadField: r0 = r4->field_3b
    //     0xa47234: ldur            w0, [x4, #0x3b]
    // 0xa47238: DecompressPointer r0
    //     0xa47238: add             x0, x0, HEAP, lsl #32
    // 0xa4723c: mov             x1, x2
    // 0xa47240: ArrayStore: r1[17] = r0  ; List_4
    //     0xa47240: add             x25, x1, #0x53
    //     0xa47244: str             w0, [x25]
    //     0xa47248: tbz             w0, #0, #0xa47264
    //     0xa4724c: ldurb           w16, [x1, #-1]
    //     0xa47250: ldurb           w17, [x0, #-1]
    //     0xa47254: and             x16, x17, x16, lsr #2
    //     0xa47258: tst             x16, HEAP, lsr #32
    //     0xa4725c: b.eq            #0xa47264
    //     0xa47260: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa47264: r16 = "productImageUrl"
    //     0xa47264: add             x16, PP, #0x30, lsl #12  ; [pp+0x30078] "productImageUrl"
    //     0xa47268: ldr             x16, [x16, #0x78]
    // 0xa4726c: StoreField: r2->field_57 = r16
    //     0xa4726c: stur            w16, [x2, #0x57]
    // 0xa47270: LoadField: r0 = r4->field_3f
    //     0xa47270: ldur            w0, [x4, #0x3f]
    // 0xa47274: DecompressPointer r0
    //     0xa47274: add             x0, x0, HEAP, lsl #32
    // 0xa47278: mov             x1, x2
    // 0xa4727c: ArrayStore: r1[19] = r0  ; List_4
    //     0xa4727c: add             x25, x1, #0x5b
    //     0xa47280: str             w0, [x25]
    //     0xa47284: tbz             w0, #0, #0xa472a0
    //     0xa47288: ldurb           w16, [x1, #-1]
    //     0xa4728c: ldurb           w17, [x0, #-1]
    //     0xa47290: and             x16, x17, x16, lsr #2
    //     0xa47294: tst             x16, HEAP, lsr #32
    //     0xa47298: b.eq            #0xa472a0
    //     0xa4729c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa472a0: r16 = "checkout_variant_response"
    //     0xa472a0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30080] "checkout_variant_response"
    //     0xa472a4: ldr             x16, [x16, #0x80]
    // 0xa472a8: StoreField: r2->field_5f = r16
    //     0xa472a8: stur            w16, [x2, #0x5f]
    // 0xa472ac: LoadField: r0 = r4->field_4b
    //     0xa472ac: ldur            w0, [x4, #0x4b]
    // 0xa472b0: DecompressPointer r0
    //     0xa472b0: add             x0, x0, HEAP, lsl #32
    // 0xa472b4: mov             x1, x2
    // 0xa472b8: ArrayStore: r1[21] = r0  ; List_4
    //     0xa472b8: add             x25, x1, #0x63
    //     0xa472bc: str             w0, [x25]
    //     0xa472c0: tbz             w0, #0, #0xa472dc
    //     0xa472c4: ldurb           w16, [x1, #-1]
    //     0xa472c8: ldurb           w17, [x0, #-1]
    //     0xa472cc: and             x16, x17, x16, lsr #2
    //     0xa472d0: tst             x16, HEAP, lsr #32
    //     0xa472d4: b.eq            #0xa472dc
    //     0xa472d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa472dc: r16 = <String, Object?>
    //     0xa472dc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa472e0: ldr             x16, [x16, #0xc28]
    // 0xa472e4: stp             x2, x16, [SP]
    // 0xa472e8: r0 = Map._fromLiteral()
    //     0xa472e8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa472ec: r16 = "/customization"
    //     0xa472ec: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8a8] "/customization"
    //     0xa472f0: ldr             x16, [x16, #0x8a8]
    // 0xa472f4: stp             x16, NULL, [SP, #8]
    // 0xa472f8: str             x0, [SP]
    // 0xa472fc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa472fc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa47300: ldr             x4, [x4, #0x438]
    // 0xa47304: r0 = GetNavigation.toNamed()
    //     0xa47304: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa47308: stur            x0, [fp, #-0x10]
    // 0xa4730c: cmp             w0, NULL
    // 0xa47310: b.eq            #0xa4733c
    // 0xa47314: ldur            x2, [fp, #-8]
    // 0xa47318: r1 = Function '<anonymous closure>':.
    //     0xa47318: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ab98] AnonymousClosure: (0xa47358), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa4731c: ldr             x1, [x1, #0xb98]
    // 0xa47320: r0 = AllocateClosure()
    //     0xa47320: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa47324: r16 = <Null?>
    //     0xa47324: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa47328: ldur            lr, [fp, #-0x10]
    // 0xa4732c: stp             lr, x16, [SP, #8]
    // 0xa47330: str             x0, [SP]
    // 0xa47334: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa47334: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa47338: r0 = then()
    //     0xa47338: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa4733c: r0 = Null
    //     0xa4733c: mov             x0, NULL
    // 0xa47340: LeaveFrame
    //     0xa47340: mov             SP, fp
    //     0xa47344: ldp             fp, lr, [SP], #0x10
    // 0xa47348: ret
    //     0xa47348: ret             
    // 0xa4734c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4734c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47350: b               #0xa4706c
    // 0xa47354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47354: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa47708, size: 0x758
    // 0xa47708: EnterFrame
    //     0xa47708: stp             fp, lr, [SP, #-0x10]!
    //     0xa4770c: mov             fp, SP
    // 0xa47710: AllocStack(0x78)
    //     0xa47710: sub             SP, SP, #0x78
    // 0xa47714: SetupParameters()
    //     0xa47714: ldr             x0, [fp, #0x10]
    //     0xa47718: ldur            w1, [x0, #0x17]
    //     0xa4771c: add             x1, x1, HEAP, lsl #32
    //     0xa47720: stur            x1, [fp, #-8]
    // 0xa47724: CheckStackOverflow
    //     0xa47724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47728: cmp             SP, x16
    //     0xa4772c: b.ls            #0xa47e34
    // 0xa47730: r1 = 1
    //     0xa47730: movz            x1, #0x1
    // 0xa47734: r0 = AllocateContext()
    //     0xa47734: bl              #0x16f6108  ; AllocateContextStub
    // 0xa47738: mov             x1, x0
    // 0xa4773c: ldur            x0, [fp, #-8]
    // 0xa47740: stur            x1, [fp, #-0x10]
    // 0xa47744: StoreField: r1->field_b = r0
    //     0xa47744: stur            w0, [x1, #0xb]
    // 0xa47748: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa47748: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4774c: ldr             x0, [x0, #0x1c80]
    //     0xa47750: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa47754: cmp             w0, w16
    //     0xa47758: b.ne            #0xa47764
    //     0xa4775c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa47760: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa47764: str             NULL, [SP]
    // 0xa47768: r4 = const [0x1, 0, 0, 0, null]
    //     0xa47768: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa4776c: r0 = GetNavigation.back()
    //     0xa4776c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa47770: ldur            x2, [fp, #-0x10]
    // 0xa47774: r0 = Sentinel
    //     0xa47774: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa47778: StoreField: r2->field_f = r0
    //     0xa47778: stur            w0, [x2, #0xf]
    // 0xa4777c: ldur            x0, [fp, #-8]
    // 0xa47780: LoadField: r1 = r0->field_f
    //     0xa47780: ldur            w1, [x0, #0xf]
    // 0xa47784: DecompressPointer r1
    //     0xa47784: add             x1, x1, HEAP, lsl #32
    // 0xa47788: LoadField: r3 = r1->field_b
    //     0xa47788: ldur            w3, [x1, #0xb]
    // 0xa4778c: DecompressPointer r3
    //     0xa4778c: add             x3, x3, HEAP, lsl #32
    // 0xa47790: cmp             w3, NULL
    // 0xa47794: b.eq            #0xa47e3c
    // 0xa47798: LoadField: r1 = r3->field_23
    //     0xa47798: ldur            w1, [x3, #0x23]
    // 0xa4779c: DecompressPointer r1
    //     0xa4779c: add             x1, x1, HEAP, lsl #32
    // 0xa477a0: r16 = "home_page"
    //     0xa477a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xa477a4: ldr             x16, [x16, #0xe60]
    // 0xa477a8: stp             x16, x1, [SP]
    // 0xa477ac: r0 = ==()
    //     0xa477ac: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xa477b0: tbnz            w0, #4, #0xa477fc
    // 0xa477b4: ldur            x2, [fp, #-0x10]
    // 0xa477b8: r16 = <HomeController>
    //     0xa477b8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xa477bc: ldr             x16, [x16, #0xf98]
    // 0xa477c0: str             x16, [SP]
    // 0xa477c4: r4 = const [0x1, 0, 0, 0, null]
    //     0xa477c4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa477c8: r0 = Inst.find()
    //     0xa477c8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa477cc: mov             x1, x0
    // 0xa477d0: ldur            x2, [fp, #-0x10]
    // 0xa477d4: StoreField: r2->field_f = r0
    //     0xa477d4: stur            w0, [x2, #0xf]
    //     0xa477d8: ldurb           w16, [x2, #-1]
    //     0xa477dc: ldurb           w17, [x0, #-1]
    //     0xa477e0: and             x16, x17, x16, lsr #2
    //     0xa477e4: tst             x16, HEAP, lsr #32
    //     0xa477e8: b.eq            #0xa477f0
    //     0xa477ec: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa477f0: r3 = "product_card"
    //     0xa477f0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xa477f4: ldr             x3, [x3, #0xc28]
    // 0xa477f8: b               #0xa4795c
    // 0xa477fc: ldur            x0, [fp, #-8]
    // 0xa47800: ldur            x2, [fp, #-0x10]
    // 0xa47804: LoadField: r1 = r0->field_f
    //     0xa47804: ldur            w1, [x0, #0xf]
    // 0xa47808: DecompressPointer r1
    //     0xa47808: add             x1, x1, HEAP, lsl #32
    // 0xa4780c: LoadField: r3 = r1->field_b
    //     0xa4780c: ldur            w3, [x1, #0xb]
    // 0xa47810: DecompressPointer r3
    //     0xa47810: add             x3, x3, HEAP, lsl #32
    // 0xa47814: cmp             w3, NULL
    // 0xa47818: b.eq            #0xa47e40
    // 0xa4781c: LoadField: r1 = r3->field_23
    //     0xa4781c: ldur            w1, [x3, #0x23]
    // 0xa47820: DecompressPointer r1
    //     0xa47820: add             x1, x1, HEAP, lsl #32
    // 0xa47824: r16 = "collection_page"
    //     0xa47824: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xa47828: ldr             x16, [x16, #0x118]
    // 0xa4782c: stp             x16, x1, [SP]
    // 0xa47830: r0 = ==()
    //     0xa47830: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xa47834: tbnz            w0, #4, #0xa47884
    // 0xa47838: ldur            x2, [fp, #-0x10]
    // 0xa4783c: r16 = <CollectionsController>
    //     0xa4783c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xa47840: ldr             x16, [x16, #0xb00]
    // 0xa47844: str             x16, [SP]
    // 0xa47848: r4 = const [0x1, 0, 0, 0, null]
    //     0xa47848: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa4784c: r0 = Inst.find()
    //     0xa4784c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa47850: mov             x1, x0
    // 0xa47854: ldur            x2, [fp, #-0x10]
    // 0xa47858: StoreField: r2->field_f = r0
    //     0xa47858: stur            w0, [x2, #0xf]
    //     0xa4785c: ldurb           w16, [x2, #-1]
    //     0xa47860: ldurb           w17, [x0, #-1]
    //     0xa47864: and             x16, x17, x16, lsr #2
    //     0xa47868: tst             x16, HEAP, lsr #32
    //     0xa4786c: b.eq            #0xa47874
    //     0xa47870: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa47874: mov             x0, x1
    // 0xa47878: r1 = "product_card"
    //     0xa47878: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xa4787c: ldr             x1, [x1, #0xc28]
    // 0xa47880: b               #0xa47954
    // 0xa47884: ldur            x0, [fp, #-8]
    // 0xa47888: ldur            x2, [fp, #-0x10]
    // 0xa4788c: LoadField: r1 = r0->field_f
    //     0xa4788c: ldur            w1, [x0, #0xf]
    // 0xa47890: DecompressPointer r1
    //     0xa47890: add             x1, x1, HEAP, lsl #32
    // 0xa47894: LoadField: r3 = r1->field_b
    //     0xa47894: ldur            w3, [x1, #0xb]
    // 0xa47898: DecompressPointer r3
    //     0xa47898: add             x3, x3, HEAP, lsl #32
    // 0xa4789c: cmp             w3, NULL
    // 0xa478a0: b.eq            #0xa47e44
    // 0xa478a4: LoadField: r1 = r3->field_23
    //     0xa478a4: ldur            w1, [x3, #0x23]
    // 0xa478a8: DecompressPointer r1
    //     0xa478a8: add             x1, x1, HEAP, lsl #32
    // 0xa478ac: r16 = "product_page"
    //     0xa478ac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa478b0: ldr             x16, [x16, #0x480]
    // 0xa478b4: stp             x16, x1, [SP]
    // 0xa478b8: r0 = ==()
    //     0xa478b8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xa478bc: tbnz            w0, #4, #0xa4790c
    // 0xa478c0: ldur            x2, [fp, #-0x10]
    // 0xa478c4: r16 = <ProductDetailController>
    //     0xa478c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xa478c8: ldr             x16, [x16, #0xde0]
    // 0xa478cc: str             x16, [SP]
    // 0xa478d0: r4 = const [0x1, 0, 0, 0, null]
    //     0xa478d0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa478d4: r0 = Inst.find()
    //     0xa478d4: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa478d8: mov             x1, x0
    // 0xa478dc: ldur            x2, [fp, #-0x10]
    // 0xa478e0: StoreField: r2->field_f = r0
    //     0xa478e0: stur            w0, [x2, #0xf]
    //     0xa478e4: ldurb           w16, [x2, #-1]
    //     0xa478e8: ldurb           w17, [x0, #-1]
    //     0xa478ec: and             x16, x17, x16, lsr #2
    //     0xa478f0: tst             x16, HEAP, lsr #32
    //     0xa478f4: b.eq            #0xa478fc
    //     0xa478f8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa478fc: mov             x0, x1
    // 0xa47900: r1 = "product_page"
    //     0xa47900: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa47904: ldr             x1, [x1, #0x480]
    // 0xa47908: b               #0xa47954
    // 0xa4790c: ldur            x2, [fp, #-0x10]
    // 0xa47910: r16 = <HomeController>
    //     0xa47910: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xa47914: ldr             x16, [x16, #0xf98]
    // 0xa47918: str             x16, [SP]
    // 0xa4791c: r4 = const [0x1, 0, 0, 0, null]
    //     0xa4791c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa47920: r0 = Inst.find()
    //     0xa47920: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa47924: mov             x1, x0
    // 0xa47928: ldur            x2, [fp, #-0x10]
    // 0xa4792c: StoreField: r2->field_f = r0
    //     0xa4792c: stur            w0, [x2, #0xf]
    //     0xa47930: ldurb           w16, [x2, #-1]
    //     0xa47934: ldurb           w17, [x0, #-1]
    //     0xa47938: and             x16, x17, x16, lsr #2
    //     0xa4793c: tst             x16, HEAP, lsr #32
    //     0xa47940: b.eq            #0xa47948
    //     0xa47944: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa47948: mov             x0, x1
    // 0xa4794c: r1 = "product_card"
    //     0xa4794c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xa47950: ldr             x1, [x1, #0xc28]
    // 0xa47954: mov             x3, x1
    // 0xa47958: mov             x1, x0
    // 0xa4795c: ldur            x0, [fp, #-8]
    // 0xa47960: stur            x3, [fp, #-0x18]
    // 0xa47964: str             x1, [SP]
    // 0xa47968: r4 = 0
    //     0xa47968: movz            x4, #0
    // 0xa4796c: ldr             x0, [SP]
    // 0xa47970: r16 = UnlinkedCall_0x613b5c
    //     0xa47970: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5aba0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa47974: add             x16, x16, #0xba0
    // 0xa47978: ldp             x5, lr, [x16]
    // 0xa4797c: blr             lr
    // 0xa47980: mov             x3, x0
    // 0xa47984: ldur            x2, [fp, #-8]
    // 0xa47988: stur            x3, [fp, #-0x28]
    // 0xa4798c: LoadField: r0 = r2->field_f
    //     0xa4798c: ldur            w0, [x2, #0xf]
    // 0xa47990: DecompressPointer r0
    //     0xa47990: add             x0, x0, HEAP, lsl #32
    // 0xa47994: LoadField: r1 = r0->field_b
    //     0xa47994: ldur            w1, [x0, #0xb]
    // 0xa47998: DecompressPointer r1
    //     0xa47998: add             x1, x1, HEAP, lsl #32
    // 0xa4799c: cmp             w1, NULL
    // 0xa479a0: b.eq            #0xa47e48
    // 0xa479a4: LoadField: r4 = r1->field_f
    //     0xa479a4: ldur            w4, [x1, #0xf]
    // 0xa479a8: DecompressPointer r4
    //     0xa479a8: add             x4, x4, HEAP, lsl #32
    // 0xa479ac: stur            x4, [fp, #-0x20]
    // 0xa479b0: LoadField: r5 = r1->field_27
    //     0xa479b0: ldur            x5, [x1, #0x27]
    // 0xa479b4: r0 = BoxInt64Instr(r5)
    //     0xa479b4: sbfiz           x0, x5, #1, #0x1f
    //     0xa479b8: cmp             x5, x0, asr #1
    //     0xa479bc: b.eq            #0xa479c8
    //     0xa479c0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa479c4: stur            x5, [x0, #7]
    // 0xa479c8: stp             x0, NULL, [SP]
    // 0xa479cc: r0 = _Double.fromInteger()
    //     0xa479cc: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa479d0: mov             x3, x0
    // 0xa479d4: ldur            x0, [fp, #-0x20]
    // 0xa479d8: r2 = Null
    //     0xa479d8: mov             x2, NULL
    // 0xa479dc: r1 = Null
    //     0xa479dc: mov             x1, NULL
    // 0xa479e0: stur            x3, [fp, #-0x30]
    // 0xa479e4: r4 = LoadClassIdInstr(r0)
    //     0xa479e4: ldur            x4, [x0, #-1]
    //     0xa479e8: ubfx            x4, x4, #0xc, #0x14
    // 0xa479ec: sub             x4, x4, #0x5e
    // 0xa479f0: cmp             x4, #1
    // 0xa479f4: b.ls            #0xa47a08
    // 0xa479f8: r8 = String
    //     0xa479f8: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa479fc: r3 = Null
    //     0xa479fc: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5abb0] Null
    //     0xa47a00: ldr             x3, [x3, #0xbb0]
    // 0xa47a04: r0 = String()
    //     0xa47a04: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa47a08: ldur            x16, [fp, #-0x20]
    // 0xa47a0c: stp             x16, NULL, [SP, #0x18]
    // 0xa47a10: r16 = "Product"
    //     0xa47a10: add             x16, PP, #0x56, lsl #12  ; [pp+0x563e0] "Product"
    //     0xa47a14: ldr             x16, [x16, #0x3e0]
    // 0xa47a18: r30 = "INR"
    //     0xa47a18: add             lr, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0xa47a1c: ldr             lr, [lr, #0x4c0]
    // 0xa47a20: stp             lr, x16, [SP, #8]
    // 0xa47a24: ldur            x16, [fp, #-0x30]
    // 0xa47a28: str             x16, [SP]
    // 0xa47a2c: ldur            x1, [fp, #-0x28]
    // 0xa47a30: r4 = const [0, 0x6, 0x5, 0x1, content, 0x1, currency, 0x4, id, 0x2, price, 0x5, type, 0x3, null]
    //     0xa47a30: add             x4, PP, #0x56, lsl #12  ; [pp+0x563e8] List(15) [0, 0x6, 0x5, 0x1, "content", 0x1, "currency", 0x4, "id", 0x2, "price", 0x5, "type", 0x3, Null]
    //     0xa47a34: ldr             x4, [x4, #0x3e8]
    // 0xa47a38: r0 = logAddToCart()
    //     0xa47a38: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0xa47a3c: ldur            x1, [fp, #-8]
    // 0xa47a40: LoadField: r0 = r1->field_f
    //     0xa47a40: ldur            w0, [x1, #0xf]
    // 0xa47a44: DecompressPointer r0
    //     0xa47a44: add             x0, x0, HEAP, lsl #32
    // 0xa47a48: LoadField: r2 = r0->field_b
    //     0xa47a48: ldur            w2, [x0, #0xb]
    // 0xa47a4c: DecompressPointer r2
    //     0xa47a4c: add             x2, x2, HEAP, lsl #32
    // 0xa47a50: cmp             w2, NULL
    // 0xa47a54: b.eq            #0xa47e4c
    // 0xa47a58: LoadField: r0 = r2->field_2f
    //     0xa47a58: ldur            w0, [x2, #0x2f]
    // 0xa47a5c: DecompressPointer r0
    //     0xa47a5c: add             x0, x0, HEAP, lsl #32
    // 0xa47a60: r2 = LoadClassIdInstr(r0)
    //     0xa47a60: ldur            x2, [x0, #-1]
    //     0xa47a64: ubfx            x2, x2, #0xc, #0x14
    // 0xa47a68: r16 = "add_to_bag"
    //     0xa47a68: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xa47a6c: ldr             x16, [x16, #0xa38]
    // 0xa47a70: stp             x16, x0, [SP]
    // 0xa47a74: mov             x0, x2
    // 0xa47a78: mov             lr, x0
    // 0xa47a7c: ldr             lr, [x21, lr, lsl #3]
    // 0xa47a80: blr             lr
    // 0xa47a84: tbnz            w0, #4, #0xa47c10
    // 0xa47a88: ldur            x2, [fp, #-0x10]
    // 0xa47a8c: LoadField: r3 = r2->field_f
    //     0xa47a8c: ldur            w3, [x2, #0xf]
    // 0xa47a90: DecompressPointer r3
    //     0xa47a90: add             x3, x3, HEAP, lsl #32
    // 0xa47a94: stur            x3, [fp, #-0x38]
    // 0xa47a98: r16 = Sentinel
    //     0xa47a98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa47a9c: cmp             w3, w16
    // 0xa47aa0: b.eq            #0xa47e04
    // 0xa47aa4: ldur            x4, [fp, #-8]
    // 0xa47aa8: ldur            x5, [fp, #-0x18]
    // 0xa47aac: LoadField: r0 = r4->field_f
    //     0xa47aac: ldur            w0, [x4, #0xf]
    // 0xa47ab0: DecompressPointer r0
    //     0xa47ab0: add             x0, x0, HEAP, lsl #32
    // 0xa47ab4: LoadField: r1 = r0->field_b
    //     0xa47ab4: ldur            w1, [x0, #0xb]
    // 0xa47ab8: DecompressPointer r1
    //     0xa47ab8: add             x1, x1, HEAP, lsl #32
    // 0xa47abc: cmp             w1, NULL
    // 0xa47ac0: b.eq            #0xa47e50
    // 0xa47ac4: LoadField: r6 = r1->field_23
    //     0xa47ac4: ldur            w6, [x1, #0x23]
    // 0xa47ac8: DecompressPointer r6
    //     0xa47ac8: add             x6, x6, HEAP, lsl #32
    // 0xa47acc: stur            x6, [fp, #-0x30]
    // 0xa47ad0: LoadField: r7 = r1->field_f
    //     0xa47ad0: ldur            w7, [x1, #0xf]
    // 0xa47ad4: DecompressPointer r7
    //     0xa47ad4: add             x7, x7, HEAP, lsl #32
    // 0xa47ad8: stur            x7, [fp, #-0x28]
    // 0xa47adc: LoadField: r8 = r1->field_13
    //     0xa47adc: ldur            w8, [x1, #0x13]
    // 0xa47ae0: DecompressPointer r8
    //     0xa47ae0: add             x8, x8, HEAP, lsl #32
    // 0xa47ae4: stur            x8, [fp, #-0x20]
    // 0xa47ae8: LoadField: r9 = r1->field_27
    //     0xa47ae8: ldur            x9, [x1, #0x27]
    // 0xa47aec: r0 = BoxInt64Instr(r9)
    //     0xa47aec: sbfiz           x0, x9, #1, #0x1f
    //     0xa47af0: cmp             x9, x0, asr #1
    //     0xa47af4: b.eq            #0xa47b00
    //     0xa47af8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47afc: stur            x9, [x0, #7]
    // 0xa47b00: stp             x0, NULL, [SP]
    // 0xa47b04: r0 = _Double.fromInteger()
    //     0xa47b04: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa47b08: stur            x0, [fp, #-0x40]
    // 0xa47b0c: r0 = EventData()
    //     0xa47b0c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xa47b10: mov             x1, x0
    // 0xa47b14: ldur            x0, [fp, #-0x30]
    // 0xa47b18: stur            x1, [fp, #-0x48]
    // 0xa47b1c: StoreField: r1->field_13 = r0
    //     0xa47b1c: stur            w0, [x1, #0x13]
    // 0xa47b20: ldur            x0, [fp, #-0x40]
    // 0xa47b24: StoreField: r1->field_2f = r0
    //     0xa47b24: stur            w0, [x1, #0x2f]
    // 0xa47b28: ldur            x0, [fp, #-0x28]
    // 0xa47b2c: StoreField: r1->field_33 = r0
    //     0xa47b2c: stur            w0, [x1, #0x33]
    // 0xa47b30: ldur            x2, [fp, #-0x18]
    // 0xa47b34: StoreField: r1->field_3b = r2
    //     0xa47b34: stur            w2, [x1, #0x3b]
    // 0xa47b38: StoreField: r1->field_87 = r2
    //     0xa47b38: stur            w2, [x1, #0x87]
    // 0xa47b3c: ldur            x0, [fp, #-0x20]
    // 0xa47b40: StoreField: r1->field_8f = r0
    //     0xa47b40: stur            w0, [x1, #0x8f]
    // 0xa47b44: r0 = EventsRequest()
    //     0xa47b44: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xa47b48: mov             x1, x0
    // 0xa47b4c: r0 = "add_to_bag_clicked"
    //     0xa47b4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xa47b50: ldr             x0, [x0, #0xec8]
    // 0xa47b54: StoreField: r1->field_7 = r0
    //     0xa47b54: stur            w0, [x1, #7]
    // 0xa47b58: ldur            x0, [fp, #-0x48]
    // 0xa47b5c: StoreField: r1->field_b = r0
    //     0xa47b5c: stur            w0, [x1, #0xb]
    // 0xa47b60: ldur            x16, [fp, #-0x38]
    // 0xa47b64: stp             x1, x16, [SP]
    // 0xa47b68: r0 = postEvents()
    //     0xa47b68: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xa47b6c: ldur            x3, [fp, #-0x10]
    // 0xa47b70: LoadField: r0 = r3->field_f
    //     0xa47b70: ldur            w0, [x3, #0xf]
    // 0xa47b74: DecompressPointer r0
    //     0xa47b74: add             x0, x0, HEAP, lsl #32
    // 0xa47b78: stur            x0, [fp, #-0x30]
    // 0xa47b7c: r16 = Sentinel
    //     0xa47b7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa47b80: cmp             w0, w16
    // 0xa47b84: b.eq            #0xa47e14
    // 0xa47b88: ldur            x4, [fp, #-8]
    // 0xa47b8c: LoadField: r1 = r4->field_f
    //     0xa47b8c: ldur            w1, [x4, #0xf]
    // 0xa47b90: DecompressPointer r1
    //     0xa47b90: add             x1, x1, HEAP, lsl #32
    // 0xa47b94: LoadField: r2 = r1->field_b
    //     0xa47b94: ldur            w2, [x1, #0xb]
    // 0xa47b98: DecompressPointer r2
    //     0xa47b98: add             x2, x2, HEAP, lsl #32
    // 0xa47b9c: cmp             w2, NULL
    // 0xa47ba0: b.eq            #0xa47e54
    // 0xa47ba4: LoadField: r1 = r2->field_f
    //     0xa47ba4: ldur            w1, [x2, #0xf]
    // 0xa47ba8: DecompressPointer r1
    //     0xa47ba8: add             x1, x1, HEAP, lsl #32
    // 0xa47bac: stur            x1, [fp, #-0x28]
    // 0xa47bb0: LoadField: r3 = r2->field_13
    //     0xa47bb0: ldur            w3, [x2, #0x13]
    // 0xa47bb4: DecompressPointer r3
    //     0xa47bb4: add             x3, x3, HEAP, lsl #32
    // 0xa47bb8: stur            x3, [fp, #-0x20]
    // 0xa47bbc: ArrayLoad: r4 = r2[0]  ; List_8
    //     0xa47bbc: ldur            x4, [x2, #0x17]
    // 0xa47bc0: stur            x4, [fp, #-0x50]
    // 0xa47bc4: r0 = AddToBagRequest()
    //     0xa47bc4: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0xa47bc8: mov             x1, x0
    // 0xa47bcc: ldur            x0, [fp, #-0x28]
    // 0xa47bd0: StoreField: r1->field_7 = r0
    //     0xa47bd0: stur            w0, [x1, #7]
    // 0xa47bd4: ldur            x0, [fp, #-0x20]
    // 0xa47bd8: StoreField: r1->field_b = r0
    //     0xa47bd8: stur            w0, [x1, #0xb]
    // 0xa47bdc: ldur            x0, [fp, #-0x50]
    // 0xa47be0: StoreField: r1->field_f = r0
    //     0xa47be0: stur            x0, [x1, #0xf]
    // 0xa47be4: ldur            x16, [fp, #-0x30]
    // 0xa47be8: stp             x1, x16, [SP, #8]
    // 0xa47bec: r16 = false
    //     0xa47bec: add             x16, NULL, #0x30  ; false
    // 0xa47bf0: str             x16, [SP]
    // 0xa47bf4: r4 = 0
    //     0xa47bf4: movz            x4, #0
    // 0xa47bf8: ldr             x0, [SP, #0x10]
    // 0xa47bfc: r16 = UnlinkedCall_0x613b5c
    //     0xa47bfc: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5abc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa47c00: add             x16, x16, #0xbc0
    // 0xa47c04: ldp             x5, lr, [x16]
    // 0xa47c08: blr             lr
    // 0xa47c0c: b               #0xa47df4
    // 0xa47c10: ldur            x4, [fp, #-8]
    // 0xa47c14: ldur            x3, [fp, #-0x10]
    // 0xa47c18: ldur            x2, [fp, #-0x18]
    // 0xa47c1c: LoadField: r5 = r3->field_f
    //     0xa47c1c: ldur            w5, [x3, #0xf]
    // 0xa47c20: DecompressPointer r5
    //     0xa47c20: add             x5, x5, HEAP, lsl #32
    // 0xa47c24: stur            x5, [fp, #-0x38]
    // 0xa47c28: r16 = Sentinel
    //     0xa47c28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa47c2c: cmp             w5, w16
    // 0xa47c30: b.eq            #0xa47e24
    // 0xa47c34: LoadField: r0 = r4->field_f
    //     0xa47c34: ldur            w0, [x4, #0xf]
    // 0xa47c38: DecompressPointer r0
    //     0xa47c38: add             x0, x0, HEAP, lsl #32
    // 0xa47c3c: LoadField: r1 = r0->field_b
    //     0xa47c3c: ldur            w1, [x0, #0xb]
    // 0xa47c40: DecompressPointer r1
    //     0xa47c40: add             x1, x1, HEAP, lsl #32
    // 0xa47c44: cmp             w1, NULL
    // 0xa47c48: b.eq            #0xa47e58
    // 0xa47c4c: LoadField: r6 = r1->field_23
    //     0xa47c4c: ldur            w6, [x1, #0x23]
    // 0xa47c50: DecompressPointer r6
    //     0xa47c50: add             x6, x6, HEAP, lsl #32
    // 0xa47c54: stur            x6, [fp, #-0x30]
    // 0xa47c58: LoadField: r7 = r1->field_f
    //     0xa47c58: ldur            w7, [x1, #0xf]
    // 0xa47c5c: DecompressPointer r7
    //     0xa47c5c: add             x7, x7, HEAP, lsl #32
    // 0xa47c60: stur            x7, [fp, #-0x28]
    // 0xa47c64: LoadField: r8 = r1->field_13
    //     0xa47c64: ldur            w8, [x1, #0x13]
    // 0xa47c68: DecompressPointer r8
    //     0xa47c68: add             x8, x8, HEAP, lsl #32
    // 0xa47c6c: stur            x8, [fp, #-0x20]
    // 0xa47c70: LoadField: r9 = r1->field_27
    //     0xa47c70: ldur            x9, [x1, #0x27]
    // 0xa47c74: r0 = BoxInt64Instr(r9)
    //     0xa47c74: sbfiz           x0, x9, #1, #0x1f
    //     0xa47c78: cmp             x9, x0, asr #1
    //     0xa47c7c: b.eq            #0xa47c88
    //     0xa47c80: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47c84: stur            x9, [x0, #7]
    // 0xa47c88: stp             x0, NULL, [SP]
    // 0xa47c8c: r0 = _Double.fromInteger()
    //     0xa47c8c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa47c90: stur            x0, [fp, #-0x40]
    // 0xa47c94: r0 = EventData()
    //     0xa47c94: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xa47c98: mov             x1, x0
    // 0xa47c9c: ldur            x0, [fp, #-0x30]
    // 0xa47ca0: stur            x1, [fp, #-0x48]
    // 0xa47ca4: StoreField: r1->field_13 = r0
    //     0xa47ca4: stur            w0, [x1, #0x13]
    // 0xa47ca8: ldur            x0, [fp, #-0x40]
    // 0xa47cac: StoreField: r1->field_2f = r0
    //     0xa47cac: stur            w0, [x1, #0x2f]
    // 0xa47cb0: ldur            x0, [fp, #-0x28]
    // 0xa47cb4: StoreField: r1->field_33 = r0
    //     0xa47cb4: stur            w0, [x1, #0x33]
    // 0xa47cb8: ldur            x0, [fp, #-0x18]
    // 0xa47cbc: StoreField: r1->field_3b = r0
    //     0xa47cbc: stur            w0, [x1, #0x3b]
    // 0xa47cc0: StoreField: r1->field_87 = r0
    //     0xa47cc0: stur            w0, [x1, #0x87]
    // 0xa47cc4: ldur            x0, [fp, #-0x20]
    // 0xa47cc8: StoreField: r1->field_8f = r0
    //     0xa47cc8: stur            w0, [x1, #0x8f]
    // 0xa47ccc: r0 = EventsRequest()
    //     0xa47ccc: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xa47cd0: mov             x1, x0
    // 0xa47cd4: r0 = "buy_now_clicked"
    //     0xa47cd4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31be0] "buy_now_clicked"
    //     0xa47cd8: ldr             x0, [x0, #0xbe0]
    // 0xa47cdc: StoreField: r1->field_7 = r0
    //     0xa47cdc: stur            w0, [x1, #7]
    // 0xa47ce0: ldur            x0, [fp, #-0x48]
    // 0xa47ce4: StoreField: r1->field_b = r0
    //     0xa47ce4: stur            w0, [x1, #0xb]
    // 0xa47ce8: ldur            x16, [fp, #-0x38]
    // 0xa47cec: stp             x1, x16, [SP]
    // 0xa47cf0: r0 = postEvents()
    //     0xa47cf0: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xa47cf4: ldur            x0, [fp, #-8]
    // 0xa47cf8: LoadField: r1 = r0->field_f
    //     0xa47cf8: ldur            w1, [x0, #0xf]
    // 0xa47cfc: DecompressPointer r1
    //     0xa47cfc: add             x1, x1, HEAP, lsl #32
    // 0xa47d00: LoadField: r2 = r1->field_b
    //     0xa47d00: ldur            w2, [x1, #0xb]
    // 0xa47d04: DecompressPointer r2
    //     0xa47d04: add             x2, x2, HEAP, lsl #32
    // 0xa47d08: cmp             w2, NULL
    // 0xa47d0c: b.eq            #0xa47e5c
    // 0xa47d10: LoadField: r1 = r2->field_43
    //     0xa47d10: ldur            w1, [x2, #0x43]
    // 0xa47d14: DecompressPointer r1
    //     0xa47d14: add             x1, x1, HEAP, lsl #32
    // 0xa47d18: cmp             w1, NULL
    // 0xa47d1c: b.ne            #0xa47d28
    // 0xa47d20: r1 = Null
    //     0xa47d20: mov             x1, NULL
    // 0xa47d24: b               #0xa47d3c
    // 0xa47d28: LoadField: r2 = r1->field_7
    //     0xa47d28: ldur            w2, [x1, #7]
    // 0xa47d2c: cbnz            w2, #0xa47d38
    // 0xa47d30: r1 = false
    //     0xa47d30: add             x1, NULL, #0x30  ; false
    // 0xa47d34: b               #0xa47d3c
    // 0xa47d38: r1 = true
    //     0xa47d38: add             x1, NULL, #0x20  ; true
    // 0xa47d3c: cmp             w1, NULL
    // 0xa47d40: b.eq            #0xa47d90
    // 0xa47d44: tbnz            w1, #4, #0xa47d90
    // 0xa47d48: LoadField: r3 = r0->field_13
    //     0xa47d48: ldur            w3, [x0, #0x13]
    // 0xa47d4c: DecompressPointer r3
    //     0xa47d4c: add             x3, x3, HEAP, lsl #32
    // 0xa47d50: ldur            x2, [fp, #-0x10]
    // 0xa47d54: stur            x3, [fp, #-0x18]
    // 0xa47d58: r1 = Function '<anonymous closure>':.
    //     0xa47d58: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5abd0] AnonymousClosure: (0xa48820), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa47d5c: ldr             x1, [x1, #0xbd0]
    // 0xa47d60: r0 = AllocateClosure()
    //     0xa47d60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa47d64: stp             x0, NULL, [SP, #0x18]
    // 0xa47d68: ldur            x16, [fp, #-0x18]
    // 0xa47d6c: r30 = true
    //     0xa47d6c: add             lr, NULL, #0x20  ; true
    // 0xa47d70: stp             lr, x16, [SP, #8]
    // 0xa47d74: r16 = Instance_RoundedRectangleBorder
    //     0xa47d74: add             x16, PP, #0x42, lsl #12  ; [pp+0x42a80] Obj!RoundedRectangleBorder@d5abb1
    //     0xa47d78: ldr             x16, [x16, #0xa80]
    // 0xa47d7c: str             x16, [SP]
    // 0xa47d80: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xa47d80: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xa47d84: ldr             x4, [x4, #0xb20]
    // 0xa47d88: r0 = showModalBottomSheet()
    //     0xa47d88: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xa47d8c: b               #0xa47df4
    // 0xa47d90: r16 = PreferenceManager
    //     0xa47d90: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xa47d94: ldr             x16, [x16, #0x878]
    // 0xa47d98: str             x16, [SP]
    // 0xa47d9c: r0 = toString()
    //     0xa47d9c: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xa47da0: r16 = <PreferenceManager>
    //     0xa47da0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xa47da4: ldr             x16, [x16, #0x880]
    // 0xa47da8: stp             x0, x16, [SP]
    // 0xa47dac: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xa47dac: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xa47db0: r0 = Inst.find()
    //     0xa47db0: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa47db4: mov             x1, x0
    // 0xa47db8: r2 = "token"
    //     0xa47db8: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xa47dbc: ldr             x2, [x2, #0x958]
    // 0xa47dc0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa47dc0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa47dc4: r0 = getString()
    //     0xa47dc4: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xa47dc8: ldur            x2, [fp, #-0x10]
    // 0xa47dcc: r1 = Function '<anonymous closure>':.
    //     0xa47dcc: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5abd8] AnonymousClosure: (0xa47e60), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa47dd0: ldr             x1, [x1, #0xbd8]
    // 0xa47dd4: stur            x0, [fp, #-8]
    // 0xa47dd8: r0 = AllocateClosure()
    //     0xa47dd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa47ddc: r16 = <Null?>
    //     0xa47ddc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa47de0: ldur            lr, [fp, #-8]
    // 0xa47de4: stp             lr, x16, [SP, #8]
    // 0xa47de8: str             x0, [SP]
    // 0xa47dec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa47dec: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa47df0: r0 = then()
    //     0xa47df0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa47df4: r0 = Null
    //     0xa47df4: mov             x0, NULL
    // 0xa47df8: LeaveFrame
    //     0xa47df8: mov             SP, fp
    //     0xa47dfc: ldp             fp, lr, [SP], #0x10
    // 0xa47e00: ret
    //     0xa47e00: ret             
    // 0xa47e04: r16 = "controller"
    //     0xa47e04: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa47e08: str             x16, [SP]
    // 0xa47e0c: r0 = _throwLocalNotInitialized()
    //     0xa47e0c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa47e10: brk             #0
    // 0xa47e14: r16 = "controller"
    //     0xa47e14: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa47e18: str             x16, [SP]
    // 0xa47e1c: r0 = _throwLocalNotInitialized()
    //     0xa47e1c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa47e20: brk             #0
    // 0xa47e24: r16 = "controller"
    //     0xa47e24: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa47e28: str             x16, [SP]
    // 0xa47e2c: r0 = _throwLocalNotInitialized()
    //     0xa47e2c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa47e30: brk             #0
    // 0xa47e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47e34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47e38: b               #0xa47730
    // 0xa47e3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa47e5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa47e5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xa47e60, size: 0x9c0
    // 0xa47e60: EnterFrame
    //     0xa47e60: stp             fp, lr, [SP, #-0x10]!
    //     0xa47e64: mov             fp, SP
    // 0xa47e68: AllocStack(0x50)
    //     0xa47e68: sub             SP, SP, #0x50
    // 0xa47e6c: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xa47e6c: stur            NULL, [fp, #-8]
    //     0xa47e70: movz            x0, #0
    //     0xa47e74: add             x1, fp, w0, sxtw #2
    //     0xa47e78: ldr             x1, [x1, #0x18]
    //     0xa47e7c: add             x2, fp, w0, sxtw #2
    //     0xa47e80: ldr             x2, [x2, #0x10]
    //     0xa47e84: stur            x2, [fp, #-0x18]
    //     0xa47e88: ldur            w3, [x1, #0x17]
    //     0xa47e8c: add             x3, x3, HEAP, lsl #32
    //     0xa47e90: stur            x3, [fp, #-0x10]
    // 0xa47e94: CheckStackOverflow
    //     0xa47e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47e98: cmp             SP, x16
    //     0xa47e9c: b.ls            #0xa48804
    // 0xa47ea0: InitAsync() -> Future<Null?>
    //     0xa47ea0: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xa47ea4: bl              #0x6326e0  ; InitAsyncStub
    // 0xa47ea8: ldur            x0, [fp, #-0x18]
    // 0xa47eac: r1 = LoadClassIdInstr(r0)
    //     0xa47eac: ldur            x1, [x0, #-1]
    //     0xa47eb0: ubfx            x1, x1, #0xc, #0x14
    // 0xa47eb4: r16 = ""
    //     0xa47eb4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa47eb8: stp             x16, x0, [SP]
    // 0xa47ebc: mov             x0, x1
    // 0xa47ec0: mov             lr, x0
    // 0xa47ec4: ldr             lr, [x21, lr, lsl #3]
    // 0xa47ec8: blr             lr
    // 0xa47ecc: tbz             w0, #4, #0xa48594
    // 0xa47ed0: ldur            x2, [fp, #-0x10]
    // 0xa47ed4: LoadField: r3 = r2->field_b
    //     0xa47ed4: ldur            w3, [x2, #0xb]
    // 0xa47ed8: DecompressPointer r3
    //     0xa47ed8: add             x3, x3, HEAP, lsl #32
    // 0xa47edc: stur            x3, [fp, #-0x28]
    // 0xa47ee0: LoadField: r0 = r3->field_f
    //     0xa47ee0: ldur            w0, [x3, #0xf]
    // 0xa47ee4: DecompressPointer r0
    //     0xa47ee4: add             x0, x0, HEAP, lsl #32
    // 0xa47ee8: LoadField: r1 = r0->field_b
    //     0xa47ee8: ldur            w1, [x0, #0xb]
    // 0xa47eec: DecompressPointer r1
    //     0xa47eec: add             x1, x1, HEAP, lsl #32
    // 0xa47ef0: cmp             w1, NULL
    // 0xa47ef4: b.eq            #0xa4880c
    // 0xa47ef8: LoadField: r4 = r1->field_f
    //     0xa47ef8: ldur            w4, [x1, #0xf]
    // 0xa47efc: DecompressPointer r4
    //     0xa47efc: add             x4, x4, HEAP, lsl #32
    // 0xa47f00: stur            x4, [fp, #-0x18]
    // 0xa47f04: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xa47f04: ldur            x5, [x1, #0x17]
    // 0xa47f08: stur            x5, [fp, #-0x20]
    // 0xa47f0c: LoadField: r0 = r1->field_27
    //     0xa47f0c: ldur            x0, [x1, #0x27]
    // 0xa47f10: mul             x6, x0, x5
    // 0xa47f14: r0 = BoxInt64Instr(r6)
    //     0xa47f14: sbfiz           x0, x6, #1, #0x1f
    //     0xa47f18: cmp             x6, x0, asr #1
    //     0xa47f1c: b.eq            #0xa47f28
    //     0xa47f20: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47f24: stur            x6, [x0, #7]
    // 0xa47f28: stp             x0, NULL, [SP]
    // 0xa47f2c: r0 = _Double.fromInteger()
    //     0xa47f2c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa47f30: stur            x0, [fp, #-0x30]
    // 0xa47f34: r0 = CheckoutEventData()
    //     0xa47f34: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa47f38: mov             x2, x0
    // 0xa47f3c: ldur            x0, [fp, #-0x30]
    // 0xa47f40: stur            x2, [fp, #-0x38]
    // 0xa47f44: StoreField: r2->field_7 = r0
    //     0xa47f44: stur            w0, [x2, #7]
    // 0xa47f48: ldur            x3, [fp, #-0x20]
    // 0xa47f4c: r0 = BoxInt64Instr(r3)
    //     0xa47f4c: sbfiz           x0, x3, #1, #0x1f
    //     0xa47f50: cmp             x3, x0, asr #1
    //     0xa47f54: b.eq            #0xa47f60
    //     0xa47f58: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47f5c: stur            x3, [x0, #7]
    // 0xa47f60: StoreField: r2->field_b = r0
    //     0xa47f60: stur            w0, [x2, #0xb]
    // 0xa47f64: ldur            x0, [fp, #-0x18]
    // 0xa47f68: StoreField: r2->field_f = r0
    //     0xa47f68: stur            w0, [x2, #0xf]
    // 0xa47f6c: ldur            x0, [fp, #-0x10]
    // 0xa47f70: LoadField: r1 = r0->field_f
    //     0xa47f70: ldur            w1, [x0, #0xf]
    // 0xa47f74: DecompressPointer r1
    //     0xa47f74: add             x1, x1, HEAP, lsl #32
    // 0xa47f78: r16 = Sentinel
    //     0xa47f78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa47f7c: cmp             w1, w16
    // 0xa47f80: b.eq            #0xa487a4
    // 0xa47f84: str             x1, [SP]
    // 0xa47f88: r4 = 0
    //     0xa47f88: movz            x4, #0
    // 0xa47f8c: ldr             x0, [SP]
    // 0xa47f90: r16 = UnlinkedCall_0x613b5c
    //     0xa47f90: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5abe0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa47f94: add             x16, x16, #0xbe0
    // 0xa47f98: ldp             x5, lr, [x16]
    // 0xa47f9c: blr             lr
    // 0xa47fa0: LoadField: r1 = r0->field_1b
    //     0xa47fa0: ldur            w1, [x0, #0x1b]
    // 0xa47fa4: DecompressPointer r1
    //     0xa47fa4: add             x1, x1, HEAP, lsl #32
    // 0xa47fa8: cmp             w1, NULL
    // 0xa47fac: b.ne            #0xa47fb8
    // 0xa47fb0: r0 = Null
    //     0xa47fb0: mov             x0, NULL
    // 0xa47fb4: b               #0xa47fd0
    // 0xa47fb8: LoadField: r0 = r1->field_b
    //     0xa47fb8: ldur            w0, [x1, #0xb]
    // 0xa47fbc: cbz             w0, #0xa47fc8
    // 0xa47fc0: r1 = false
    //     0xa47fc0: add             x1, NULL, #0x30  ; false
    // 0xa47fc4: b               #0xa47fcc
    // 0xa47fc8: r1 = true
    //     0xa47fc8: add             x1, NULL, #0x20  ; true
    // 0xa47fcc: mov             x0, x1
    // 0xa47fd0: cmp             w0, NULL
    // 0xa47fd4: b.eq            #0xa47fdc
    // 0xa47fd8: tbz             w0, #4, #0xa48084
    // 0xa47fdc: ldur            x0, [fp, #-0x10]
    // 0xa47fe0: LoadField: r1 = r0->field_f
    //     0xa47fe0: ldur            w1, [x0, #0xf]
    // 0xa47fe4: DecompressPointer r1
    //     0xa47fe4: add             x1, x1, HEAP, lsl #32
    // 0xa47fe8: r16 = Sentinel
    //     0xa47fe8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa47fec: cmp             w1, w16
    // 0xa47ff0: b.eq            #0xa487b4
    // 0xa47ff4: str             x1, [SP]
    // 0xa47ff8: r4 = 0
    //     0xa47ff8: movz            x4, #0
    // 0xa47ffc: ldr             x0, [SP]
    // 0xa48000: r16 = UnlinkedCall_0x613b5c
    //     0xa48000: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5abf0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa48004: add             x16, x16, #0xbf0
    // 0xa48008: ldp             x5, lr, [x16]
    // 0xa4800c: blr             lr
    // 0xa48010: LoadField: r1 = r0->field_1b
    //     0xa48010: ldur            w1, [x0, #0x1b]
    // 0xa48014: DecompressPointer r1
    //     0xa48014: add             x1, x1, HEAP, lsl #32
    // 0xa48018: cmp             w1, NULL
    // 0xa4801c: b.ne            #0xa48028
    // 0xa48020: r0 = Null
    //     0xa48020: mov             x0, NULL
    // 0xa48024: b               #0xa4806c
    // 0xa48028: r0 = first()
    //     0xa48028: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xa4802c: cmp             w0, NULL
    // 0xa48030: b.ne            #0xa4803c
    // 0xa48034: r0 = Null
    //     0xa48034: mov             x0, NULL
    // 0xa48038: b               #0xa4806c
    // 0xa4803c: LoadField: r1 = r0->field_13
    //     0xa4803c: ldur            w1, [x0, #0x13]
    // 0xa48040: DecompressPointer r1
    //     0xa48040: add             x1, x1, HEAP, lsl #32
    // 0xa48044: cmp             w1, NULL
    // 0xa48048: b.ne            #0xa48054
    // 0xa4804c: r0 = Null
    //     0xa4804c: mov             x0, NULL
    // 0xa48050: b               #0xa4806c
    // 0xa48054: LoadField: r0 = r1->field_7
    //     0xa48054: ldur            w0, [x1, #7]
    // 0xa48058: cbz             w0, #0xa48064
    // 0xa4805c: r1 = false
    //     0xa4805c: add             x1, NULL, #0x30  ; false
    // 0xa48060: b               #0xa48068
    // 0xa48064: r1 = true
    //     0xa48064: add             x1, NULL, #0x20  ; true
    // 0xa48068: mov             x0, x1
    // 0xa4806c: cmp             w0, NULL
    // 0xa48070: b.ne            #0xa48080
    // 0xa48074: ldur            x2, [fp, #-0x10]
    // 0xa48078: ldur            x0, [fp, #-0x28]
    // 0xa4807c: b               #0xa48314
    // 0xa48080: tbnz            w0, #4, #0xa4830c
    // 0xa48084: ldur            x0, [fp, #-0x10]
    // 0xa48088: ldur            x1, [fp, #-0x28]
    // 0xa4808c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4808c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48090: ldr             x0, [x0, #0x1c80]
    //     0xa48094: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa48098: cmp             w0, w16
    //     0xa4809c: b.ne            #0xa480a8
    //     0xa480a0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa480a4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa480a8: r1 = Null
    //     0xa480a8: mov             x1, NULL
    // 0xa480ac: r2 = 40
    //     0xa480ac: movz            x2, #0x28
    // 0xa480b0: r0 = AllocateArray()
    //     0xa480b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa480b4: mov             x2, x0
    // 0xa480b8: stur            x2, [fp, #-0x18]
    // 0xa480bc: r16 = "couponCode"
    //     0xa480bc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa480c0: ldr             x16, [x16, #0x310]
    // 0xa480c4: StoreField: r2->field_f = r16
    //     0xa480c4: stur            w16, [x2, #0xf]
    // 0xa480c8: r16 = ""
    //     0xa480c8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa480cc: StoreField: r2->field_13 = r16
    //     0xa480cc: stur            w16, [x2, #0x13]
    // 0xa480d0: r16 = "product_id"
    //     0xa480d0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa480d4: ldr             x16, [x16, #0x9b8]
    // 0xa480d8: ArrayStore: r2[0] = r16  ; List_4
    //     0xa480d8: stur            w16, [x2, #0x17]
    // 0xa480dc: ldur            x0, [fp, #-0x28]
    // 0xa480e0: LoadField: r1 = r0->field_f
    //     0xa480e0: ldur            w1, [x0, #0xf]
    // 0xa480e4: DecompressPointer r1
    //     0xa480e4: add             x1, x1, HEAP, lsl #32
    // 0xa480e8: LoadField: r3 = r1->field_b
    //     0xa480e8: ldur            w3, [x1, #0xb]
    // 0xa480ec: DecompressPointer r3
    //     0xa480ec: add             x3, x3, HEAP, lsl #32
    // 0xa480f0: cmp             w3, NULL
    // 0xa480f4: b.eq            #0xa48810
    // 0xa480f8: LoadField: r0 = r3->field_f
    //     0xa480f8: ldur            w0, [x3, #0xf]
    // 0xa480fc: DecompressPointer r0
    //     0xa480fc: add             x0, x0, HEAP, lsl #32
    // 0xa48100: StoreField: r2->field_1b = r0
    //     0xa48100: stur            w0, [x2, #0x1b]
    // 0xa48104: r16 = "sku_id"
    //     0xa48104: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa48108: ldr             x16, [x16, #0x498]
    // 0xa4810c: StoreField: r2->field_1f = r16
    //     0xa4810c: stur            w16, [x2, #0x1f]
    // 0xa48110: LoadField: r0 = r3->field_13
    //     0xa48110: ldur            w0, [x3, #0x13]
    // 0xa48114: DecompressPointer r0
    //     0xa48114: add             x0, x0, HEAP, lsl #32
    // 0xa48118: StoreField: r2->field_23 = r0
    //     0xa48118: stur            w0, [x2, #0x23]
    // 0xa4811c: r16 = "quantity"
    //     0xa4811c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa48120: ldr             x16, [x16, #0x428]
    // 0xa48124: StoreField: r2->field_27 = r16
    //     0xa48124: stur            w16, [x2, #0x27]
    // 0xa48128: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa48128: ldur            x4, [x3, #0x17]
    // 0xa4812c: r0 = BoxInt64Instr(r4)
    //     0xa4812c: sbfiz           x0, x4, #1, #0x1f
    //     0xa48130: cmp             x4, x0, asr #1
    //     0xa48134: b.eq            #0xa48140
    //     0xa48138: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4813c: stur            x4, [x0, #7]
    // 0xa48140: mov             x1, x2
    // 0xa48144: ArrayStore: r1[7] = r0  ; List_4
    //     0xa48144: add             x25, x1, #0x2b
    //     0xa48148: str             w0, [x25]
    //     0xa4814c: tbz             w0, #0, #0xa48168
    //     0xa48150: ldurb           w16, [x1, #-1]
    //     0xa48154: ldurb           w17, [x0, #-1]
    //     0xa48158: and             x16, x17, x16, lsr #2
    //     0xa4815c: tst             x16, HEAP, lsr #32
    //     0xa48160: b.eq            #0xa48168
    //     0xa48164: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48168: r16 = "checkout_event_data"
    //     0xa48168: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4816c: ldr             x16, [x16, #0xd50]
    // 0xa48170: StoreField: r2->field_2f = r16
    //     0xa48170: stur            w16, [x2, #0x2f]
    // 0xa48174: mov             x1, x2
    // 0xa48178: ldur            x0, [fp, #-0x38]
    // 0xa4817c: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4817c: add             x25, x1, #0x33
    //     0xa48180: str             w0, [x25]
    //     0xa48184: tbz             w0, #0, #0xa481a0
    //     0xa48188: ldurb           w16, [x1, #-1]
    //     0xa4818c: ldurb           w17, [x0, #-1]
    //     0xa48190: and             x16, x17, x16, lsr #2
    //     0xa48194: tst             x16, HEAP, lsr #32
    //     0xa48198: b.eq            #0xa481a0
    //     0xa4819c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa481a0: r16 = "previousScreenSource"
    //     0xa481a0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa481a4: ldr             x16, [x16, #0x448]
    // 0xa481a8: StoreField: r2->field_37 = r16
    //     0xa481a8: stur            w16, [x2, #0x37]
    // 0xa481ac: r16 = "product_page"
    //     0xa481ac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa481b0: ldr             x16, [x16, #0x480]
    // 0xa481b4: StoreField: r2->field_3b = r16
    //     0xa481b4: stur            w16, [x2, #0x3b]
    // 0xa481b8: r16 = "is_skipped_address"
    //     0xa481b8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa481bc: ldr             x16, [x16, #0xb80]
    // 0xa481c0: StoreField: r2->field_3f = r16
    //     0xa481c0: stur            w16, [x2, #0x3f]
    // 0xa481c4: r16 = true
    //     0xa481c4: add             x16, NULL, #0x20  ; true
    // 0xa481c8: StoreField: r2->field_43 = r16
    //     0xa481c8: stur            w16, [x2, #0x43]
    // 0xa481cc: r16 = "coming_from"
    //     0xa481cc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa481d0: ldr             x16, [x16, #0x328]
    // 0xa481d4: StoreField: r2->field_47 = r16
    //     0xa481d4: stur            w16, [x2, #0x47]
    // 0xa481d8: LoadField: r0 = r3->field_2f
    //     0xa481d8: ldur            w0, [x3, #0x2f]
    // 0xa481dc: DecompressPointer r0
    //     0xa481dc: add             x0, x0, HEAP, lsl #32
    // 0xa481e0: mov             x1, x2
    // 0xa481e4: ArrayStore: r1[15] = r0  ; List_4
    //     0xa481e4: add             x25, x1, #0x4b
    //     0xa481e8: str             w0, [x25]
    //     0xa481ec: tbz             w0, #0, #0xa48208
    //     0xa481f0: ldurb           w16, [x1, #-1]
    //     0xa481f4: ldurb           w17, [x0, #-1]
    //     0xa481f8: and             x16, x17, x16, lsr #2
    //     0xa481fc: tst             x16, HEAP, lsr #32
    //     0xa48200: b.eq            #0xa48208
    //     0xa48204: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48208: r16 = "checkout_id"
    //     0xa48208: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa4820c: ldr             x16, [x16, #0xb88]
    // 0xa48210: StoreField: r2->field_4f = r16
    //     0xa48210: stur            w16, [x2, #0x4f]
    // 0xa48214: ldur            x0, [fp, #-0x10]
    // 0xa48218: LoadField: r1 = r0->field_f
    //     0xa48218: ldur            w1, [x0, #0xf]
    // 0xa4821c: DecompressPointer r1
    //     0xa4821c: add             x1, x1, HEAP, lsl #32
    // 0xa48220: r16 = Sentinel
    //     0xa48220: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa48224: cmp             w1, w16
    // 0xa48228: b.eq            #0xa487c4
    // 0xa4822c: str             x1, [SP]
    // 0xa48230: r4 = 0
    //     0xa48230: movz            x4, #0
    // 0xa48234: ldr             x0, [SP]
    // 0xa48238: r16 = UnlinkedCall_0x613b5c
    //     0xa48238: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4823c: add             x16, x16, #0xc00
    // 0xa48240: ldp             x5, lr, [x16]
    // 0xa48244: blr             lr
    // 0xa48248: ldur            x1, [fp, #-0x18]
    // 0xa4824c: ArrayStore: r1[17] = r0  ; List_4
    //     0xa4824c: add             x25, x1, #0x53
    //     0xa48250: str             w0, [x25]
    //     0xa48254: tbz             w0, #0, #0xa48270
    //     0xa48258: ldurb           w16, [x1, #-1]
    //     0xa4825c: ldurb           w17, [x0, #-1]
    //     0xa48260: and             x16, x17, x16, lsr #2
    //     0xa48264: tst             x16, HEAP, lsr #32
    //     0xa48268: b.eq            #0xa48270
    //     0xa4826c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48270: ldur            x1, [fp, #-0x18]
    // 0xa48274: r16 = "user_data"
    //     0xa48274: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa48278: ldr             x16, [x16, #0x58]
    // 0xa4827c: StoreField: r1->field_57 = r16
    //     0xa4827c: stur            w16, [x1, #0x57]
    // 0xa48280: ldur            x2, [fp, #-0x10]
    // 0xa48284: LoadField: r0 = r2->field_f
    //     0xa48284: ldur            w0, [x2, #0xf]
    // 0xa48288: DecompressPointer r0
    //     0xa48288: add             x0, x0, HEAP, lsl #32
    // 0xa4828c: r16 = Sentinel
    //     0xa4828c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa48290: cmp             w0, w16
    // 0xa48294: b.eq            #0xa487d4
    // 0xa48298: str             x0, [SP]
    // 0xa4829c: r4 = 0
    //     0xa4829c: movz            x4, #0
    // 0xa482a0: ldr             x0, [SP]
    // 0xa482a4: r16 = UnlinkedCall_0x613b5c
    //     0xa482a4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa482a8: add             x16, x16, #0xc10
    // 0xa482ac: ldp             x5, lr, [x16]
    // 0xa482b0: blr             lr
    // 0xa482b4: ldur            x1, [fp, #-0x18]
    // 0xa482b8: ArrayStore: r1[19] = r0  ; List_4
    //     0xa482b8: add             x25, x1, #0x5b
    //     0xa482bc: str             w0, [x25]
    //     0xa482c0: tbz             w0, #0, #0xa482dc
    //     0xa482c4: ldurb           w16, [x1, #-1]
    //     0xa482c8: ldurb           w17, [x0, #-1]
    //     0xa482cc: and             x16, x17, x16, lsr #2
    //     0xa482d0: tst             x16, HEAP, lsr #32
    //     0xa482d4: b.eq            #0xa482dc
    //     0xa482d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa482dc: r16 = <String, dynamic>
    //     0xa482dc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa482e0: ldur            lr, [fp, #-0x18]
    // 0xa482e4: stp             lr, x16, [SP]
    // 0xa482e8: r0 = Map._fromLiteral()
    //     0xa482e8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa482ec: r16 = "/checkout_request_address_page"
    //     0xa482ec: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xa482f0: ldr             x16, [x16, #0x9e8]
    // 0xa482f4: stp             x16, NULL, [SP, #8]
    // 0xa482f8: str             x0, [SP]
    // 0xa482fc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa482fc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa48300: ldr             x4, [x4, #0x438]
    // 0xa48304: r0 = GetNavigation.toNamed()
    //     0xa48304: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa48308: b               #0xa4879c
    // 0xa4830c: ldur            x2, [fp, #-0x10]
    // 0xa48310: ldur            x0, [fp, #-0x28]
    // 0xa48314: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa48314: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48318: ldr             x0, [x0, #0x1c80]
    //     0xa4831c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa48320: cmp             w0, w16
    //     0xa48324: b.ne            #0xa48330
    //     0xa48328: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4832c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa48330: r1 = Null
    //     0xa48330: mov             x1, NULL
    // 0xa48334: r2 = 40
    //     0xa48334: movz            x2, #0x28
    // 0xa48338: r0 = AllocateArray()
    //     0xa48338: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4833c: mov             x2, x0
    // 0xa48340: stur            x2, [fp, #-0x18]
    // 0xa48344: r16 = "couponCode"
    //     0xa48344: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa48348: ldr             x16, [x16, #0x310]
    // 0xa4834c: StoreField: r2->field_f = r16
    //     0xa4834c: stur            w16, [x2, #0xf]
    // 0xa48350: r16 = ""
    //     0xa48350: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa48354: StoreField: r2->field_13 = r16
    //     0xa48354: stur            w16, [x2, #0x13]
    // 0xa48358: r16 = "product_id"
    //     0xa48358: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4835c: ldr             x16, [x16, #0x9b8]
    // 0xa48360: ArrayStore: r2[0] = r16  ; List_4
    //     0xa48360: stur            w16, [x2, #0x17]
    // 0xa48364: ldur            x0, [fp, #-0x28]
    // 0xa48368: LoadField: r1 = r0->field_f
    //     0xa48368: ldur            w1, [x0, #0xf]
    // 0xa4836c: DecompressPointer r1
    //     0xa4836c: add             x1, x1, HEAP, lsl #32
    // 0xa48370: LoadField: r3 = r1->field_b
    //     0xa48370: ldur            w3, [x1, #0xb]
    // 0xa48374: DecompressPointer r3
    //     0xa48374: add             x3, x3, HEAP, lsl #32
    // 0xa48378: cmp             w3, NULL
    // 0xa4837c: b.eq            #0xa48814
    // 0xa48380: LoadField: r0 = r3->field_f
    //     0xa48380: ldur            w0, [x3, #0xf]
    // 0xa48384: DecompressPointer r0
    //     0xa48384: add             x0, x0, HEAP, lsl #32
    // 0xa48388: StoreField: r2->field_1b = r0
    //     0xa48388: stur            w0, [x2, #0x1b]
    // 0xa4838c: r16 = "sku_id"
    //     0xa4838c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa48390: ldr             x16, [x16, #0x498]
    // 0xa48394: StoreField: r2->field_1f = r16
    //     0xa48394: stur            w16, [x2, #0x1f]
    // 0xa48398: LoadField: r0 = r3->field_13
    //     0xa48398: ldur            w0, [x3, #0x13]
    // 0xa4839c: DecompressPointer r0
    //     0xa4839c: add             x0, x0, HEAP, lsl #32
    // 0xa483a0: StoreField: r2->field_23 = r0
    //     0xa483a0: stur            w0, [x2, #0x23]
    // 0xa483a4: r16 = "quantity"
    //     0xa483a4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa483a8: ldr             x16, [x16, #0x428]
    // 0xa483ac: StoreField: r2->field_27 = r16
    //     0xa483ac: stur            w16, [x2, #0x27]
    // 0xa483b0: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa483b0: ldur            x4, [x3, #0x17]
    // 0xa483b4: r0 = BoxInt64Instr(r4)
    //     0xa483b4: sbfiz           x0, x4, #1, #0x1f
    //     0xa483b8: cmp             x4, x0, asr #1
    //     0xa483bc: b.eq            #0xa483c8
    //     0xa483c0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa483c4: stur            x4, [x0, #7]
    // 0xa483c8: mov             x1, x2
    // 0xa483cc: ArrayStore: r1[7] = r0  ; List_4
    //     0xa483cc: add             x25, x1, #0x2b
    //     0xa483d0: str             w0, [x25]
    //     0xa483d4: tbz             w0, #0, #0xa483f0
    //     0xa483d8: ldurb           w16, [x1, #-1]
    //     0xa483dc: ldurb           w17, [x0, #-1]
    //     0xa483e0: and             x16, x17, x16, lsr #2
    //     0xa483e4: tst             x16, HEAP, lsr #32
    //     0xa483e8: b.eq            #0xa483f0
    //     0xa483ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa483f0: r16 = "checkout_event_data"
    //     0xa483f0: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa483f4: ldr             x16, [x16, #0xd50]
    // 0xa483f8: StoreField: r2->field_2f = r16
    //     0xa483f8: stur            w16, [x2, #0x2f]
    // 0xa483fc: mov             x1, x2
    // 0xa48400: ldur            x0, [fp, #-0x38]
    // 0xa48404: ArrayStore: r1[9] = r0  ; List_4
    //     0xa48404: add             x25, x1, #0x33
    //     0xa48408: str             w0, [x25]
    //     0xa4840c: tbz             w0, #0, #0xa48428
    //     0xa48410: ldurb           w16, [x1, #-1]
    //     0xa48414: ldurb           w17, [x0, #-1]
    //     0xa48418: and             x16, x17, x16, lsr #2
    //     0xa4841c: tst             x16, HEAP, lsr #32
    //     0xa48420: b.eq            #0xa48428
    //     0xa48424: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48428: r16 = "previousScreenSource"
    //     0xa48428: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4842c: ldr             x16, [x16, #0x448]
    // 0xa48430: StoreField: r2->field_37 = r16
    //     0xa48430: stur            w16, [x2, #0x37]
    // 0xa48434: r16 = "product_page"
    //     0xa48434: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa48438: ldr             x16, [x16, #0x480]
    // 0xa4843c: StoreField: r2->field_3b = r16
    //     0xa4843c: stur            w16, [x2, #0x3b]
    // 0xa48440: r16 = "is_skipped_address"
    //     0xa48440: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa48444: ldr             x16, [x16, #0xb80]
    // 0xa48448: StoreField: r2->field_3f = r16
    //     0xa48448: stur            w16, [x2, #0x3f]
    // 0xa4844c: r16 = true
    //     0xa4844c: add             x16, NULL, #0x20  ; true
    // 0xa48450: StoreField: r2->field_43 = r16
    //     0xa48450: stur            w16, [x2, #0x43]
    // 0xa48454: r16 = "coming_from"
    //     0xa48454: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa48458: ldr             x16, [x16, #0x328]
    // 0xa4845c: StoreField: r2->field_47 = r16
    //     0xa4845c: stur            w16, [x2, #0x47]
    // 0xa48460: LoadField: r0 = r3->field_2f
    //     0xa48460: ldur            w0, [x3, #0x2f]
    // 0xa48464: DecompressPointer r0
    //     0xa48464: add             x0, x0, HEAP, lsl #32
    // 0xa48468: mov             x1, x2
    // 0xa4846c: ArrayStore: r1[15] = r0  ; List_4
    //     0xa4846c: add             x25, x1, #0x4b
    //     0xa48470: str             w0, [x25]
    //     0xa48474: tbz             w0, #0, #0xa48490
    //     0xa48478: ldurb           w16, [x1, #-1]
    //     0xa4847c: ldurb           w17, [x0, #-1]
    //     0xa48480: and             x16, x17, x16, lsr #2
    //     0xa48484: tst             x16, HEAP, lsr #32
    //     0xa48488: b.eq            #0xa48490
    //     0xa4848c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48490: r16 = "checkout_id"
    //     0xa48490: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa48494: ldr             x16, [x16, #0xb88]
    // 0xa48498: StoreField: r2->field_4f = r16
    //     0xa48498: stur            w16, [x2, #0x4f]
    // 0xa4849c: ldur            x0, [fp, #-0x10]
    // 0xa484a0: LoadField: r1 = r0->field_f
    //     0xa484a0: ldur            w1, [x0, #0xf]
    // 0xa484a4: DecompressPointer r1
    //     0xa484a4: add             x1, x1, HEAP, lsl #32
    // 0xa484a8: r16 = Sentinel
    //     0xa484a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa484ac: cmp             w1, w16
    // 0xa484b0: b.eq            #0xa487e4
    // 0xa484b4: str             x1, [SP]
    // 0xa484b8: r4 = 0
    //     0xa484b8: movz            x4, #0
    // 0xa484bc: ldr             x0, [SP]
    // 0xa484c0: r16 = UnlinkedCall_0x613b5c
    //     0xa484c0: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa484c4: add             x16, x16, #0xc20
    // 0xa484c8: ldp             x5, lr, [x16]
    // 0xa484cc: blr             lr
    // 0xa484d0: ldur            x1, [fp, #-0x18]
    // 0xa484d4: ArrayStore: r1[17] = r0  ; List_4
    //     0xa484d4: add             x25, x1, #0x53
    //     0xa484d8: str             w0, [x25]
    //     0xa484dc: tbz             w0, #0, #0xa484f8
    //     0xa484e0: ldurb           w16, [x1, #-1]
    //     0xa484e4: ldurb           w17, [x0, #-1]
    //     0xa484e8: and             x16, x17, x16, lsr #2
    //     0xa484ec: tst             x16, HEAP, lsr #32
    //     0xa484f0: b.eq            #0xa484f8
    //     0xa484f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa484f8: ldur            x1, [fp, #-0x18]
    // 0xa484fc: r16 = "user_data"
    //     0xa484fc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa48500: ldr             x16, [x16, #0x58]
    // 0xa48504: StoreField: r1->field_57 = r16
    //     0xa48504: stur            w16, [x1, #0x57]
    // 0xa48508: ldur            x0, [fp, #-0x10]
    // 0xa4850c: LoadField: r2 = r0->field_f
    //     0xa4850c: ldur            w2, [x0, #0xf]
    // 0xa48510: DecompressPointer r2
    //     0xa48510: add             x2, x2, HEAP, lsl #32
    // 0xa48514: r16 = Sentinel
    //     0xa48514: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa48518: cmp             w2, w16
    // 0xa4851c: b.eq            #0xa487f4
    // 0xa48520: str             x2, [SP]
    // 0xa48524: r4 = 0
    //     0xa48524: movz            x4, #0
    // 0xa48528: ldr             x0, [SP]
    // 0xa4852c: r16 = UnlinkedCall_0x613b5c
    //     0xa4852c: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa48530: add             x16, x16, #0xc30
    // 0xa48534: ldp             x5, lr, [x16]
    // 0xa48538: blr             lr
    // 0xa4853c: ldur            x1, [fp, #-0x18]
    // 0xa48540: ArrayStore: r1[19] = r0  ; List_4
    //     0xa48540: add             x25, x1, #0x5b
    //     0xa48544: str             w0, [x25]
    //     0xa48548: tbz             w0, #0, #0xa48564
    //     0xa4854c: ldurb           w16, [x1, #-1]
    //     0xa48550: ldurb           w17, [x0, #-1]
    //     0xa48554: and             x16, x17, x16, lsr #2
    //     0xa48558: tst             x16, HEAP, lsr #32
    //     0xa4855c: b.eq            #0xa48564
    //     0xa48560: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48564: r16 = <String, dynamic>
    //     0xa48564: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa48568: ldur            lr, [fp, #-0x18]
    // 0xa4856c: stp             lr, x16, [SP]
    // 0xa48570: r0 = Map._fromLiteral()
    //     0xa48570: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa48574: r16 = "/checkout_order_summary_page"
    //     0xa48574: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xa48578: ldr             x16, [x16, #0x9d8]
    // 0xa4857c: stp             x16, NULL, [SP, #8]
    // 0xa48580: str             x0, [SP]
    // 0xa48584: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa48584: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa48588: ldr             x4, [x4, #0x438]
    // 0xa4858c: r0 = GetNavigation.toNamed()
    //     0xa4858c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa48590: b               #0xa4879c
    // 0xa48594: ldur            x0, [fp, #-0x10]
    // 0xa48598: LoadField: r2 = r0->field_b
    //     0xa48598: ldur            w2, [x0, #0xb]
    // 0xa4859c: DecompressPointer r2
    //     0xa4859c: add             x2, x2, HEAP, lsl #32
    // 0xa485a0: stur            x2, [fp, #-0x18]
    // 0xa485a4: LoadField: r0 = r2->field_f
    //     0xa485a4: ldur            w0, [x2, #0xf]
    // 0xa485a8: DecompressPointer r0
    //     0xa485a8: add             x0, x0, HEAP, lsl #32
    // 0xa485ac: LoadField: r1 = r0->field_b
    //     0xa485ac: ldur            w1, [x0, #0xb]
    // 0xa485b0: DecompressPointer r1
    //     0xa485b0: add             x1, x1, HEAP, lsl #32
    // 0xa485b4: cmp             w1, NULL
    // 0xa485b8: b.eq            #0xa48818
    // 0xa485bc: LoadField: r3 = r1->field_f
    //     0xa485bc: ldur            w3, [x1, #0xf]
    // 0xa485c0: DecompressPointer r3
    //     0xa485c0: add             x3, x3, HEAP, lsl #32
    // 0xa485c4: stur            x3, [fp, #-0x10]
    // 0xa485c8: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xa485c8: ldur            x4, [x1, #0x17]
    // 0xa485cc: stur            x4, [fp, #-0x20]
    // 0xa485d0: LoadField: r0 = r1->field_27
    //     0xa485d0: ldur            x0, [x1, #0x27]
    // 0xa485d4: mul             x5, x0, x4
    // 0xa485d8: r0 = BoxInt64Instr(r5)
    //     0xa485d8: sbfiz           x0, x5, #1, #0x1f
    //     0xa485dc: cmp             x5, x0, asr #1
    //     0xa485e0: b.eq            #0xa485ec
    //     0xa485e4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa485e8: stur            x5, [x0, #7]
    // 0xa485ec: stp             x0, NULL, [SP]
    // 0xa485f0: r0 = _Double.fromInteger()
    //     0xa485f0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa485f4: stur            x0, [fp, #-0x28]
    // 0xa485f8: r0 = CheckoutEventData()
    //     0xa485f8: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa485fc: mov             x2, x0
    // 0xa48600: ldur            x0, [fp, #-0x28]
    // 0xa48604: stur            x2, [fp, #-0x30]
    // 0xa48608: StoreField: r2->field_7 = r0
    //     0xa48608: stur            w0, [x2, #7]
    // 0xa4860c: ldur            x3, [fp, #-0x20]
    // 0xa48610: r0 = BoxInt64Instr(r3)
    //     0xa48610: sbfiz           x0, x3, #1, #0x1f
    //     0xa48614: cmp             x3, x0, asr #1
    //     0xa48618: b.eq            #0xa48624
    //     0xa4861c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48620: stur            x3, [x0, #7]
    // 0xa48624: StoreField: r2->field_b = r0
    //     0xa48624: stur            w0, [x2, #0xb]
    // 0xa48628: ldur            x0, [fp, #-0x10]
    // 0xa4862c: StoreField: r2->field_f = r0
    //     0xa4862c: stur            w0, [x2, #0xf]
    // 0xa48630: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa48630: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48634: ldr             x0, [x0, #0x1c80]
    //     0xa48638: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4863c: cmp             w0, w16
    //     0xa48640: b.ne            #0xa4864c
    //     0xa48644: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa48648: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4864c: r1 = Null
    //     0xa4864c: mov             x1, NULL
    // 0xa48650: r2 = 28
    //     0xa48650: movz            x2, #0x1c
    // 0xa48654: r0 = AllocateArray()
    //     0xa48654: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa48658: mov             x2, x0
    // 0xa4865c: r16 = "previousScreenSource"
    //     0xa4865c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa48660: ldr             x16, [x16, #0x448]
    // 0xa48664: StoreField: r2->field_f = r16
    //     0xa48664: stur            w16, [x2, #0xf]
    // 0xa48668: r16 = "product_page"
    //     0xa48668: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4866c: ldr             x16, [x16, #0x480]
    // 0xa48670: StoreField: r2->field_13 = r16
    //     0xa48670: stur            w16, [x2, #0x13]
    // 0xa48674: r16 = "checkout_event_data"
    //     0xa48674: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa48678: ldr             x16, [x16, #0xd50]
    // 0xa4867c: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4867c: stur            w16, [x2, #0x17]
    // 0xa48680: ldur            x0, [fp, #-0x30]
    // 0xa48684: StoreField: r2->field_1b = r0
    //     0xa48684: stur            w0, [x2, #0x1b]
    // 0xa48688: r16 = "product_id"
    //     0xa48688: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4868c: ldr             x16, [x16, #0x9b8]
    // 0xa48690: StoreField: r2->field_1f = r16
    //     0xa48690: stur            w16, [x2, #0x1f]
    // 0xa48694: ldur            x0, [fp, #-0x18]
    // 0xa48698: LoadField: r1 = r0->field_f
    //     0xa48698: ldur            w1, [x0, #0xf]
    // 0xa4869c: DecompressPointer r1
    //     0xa4869c: add             x1, x1, HEAP, lsl #32
    // 0xa486a0: LoadField: r3 = r1->field_b
    //     0xa486a0: ldur            w3, [x1, #0xb]
    // 0xa486a4: DecompressPointer r3
    //     0xa486a4: add             x3, x3, HEAP, lsl #32
    // 0xa486a8: cmp             w3, NULL
    // 0xa486ac: b.eq            #0xa4881c
    // 0xa486b0: LoadField: r0 = r3->field_f
    //     0xa486b0: ldur            w0, [x3, #0xf]
    // 0xa486b4: DecompressPointer r0
    //     0xa486b4: add             x0, x0, HEAP, lsl #32
    // 0xa486b8: StoreField: r2->field_23 = r0
    //     0xa486b8: stur            w0, [x2, #0x23]
    // 0xa486bc: r16 = "sku_id"
    //     0xa486bc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa486c0: ldr             x16, [x16, #0x498]
    // 0xa486c4: StoreField: r2->field_27 = r16
    //     0xa486c4: stur            w16, [x2, #0x27]
    // 0xa486c8: LoadField: r0 = r3->field_13
    //     0xa486c8: ldur            w0, [x3, #0x13]
    // 0xa486cc: DecompressPointer r0
    //     0xa486cc: add             x0, x0, HEAP, lsl #32
    // 0xa486d0: StoreField: r2->field_2b = r0
    //     0xa486d0: stur            w0, [x2, #0x2b]
    // 0xa486d4: r16 = "quantity"
    //     0xa486d4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa486d8: ldr             x16, [x16, #0x428]
    // 0xa486dc: StoreField: r2->field_2f = r16
    //     0xa486dc: stur            w16, [x2, #0x2f]
    // 0xa486e0: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa486e0: ldur            x4, [x3, #0x17]
    // 0xa486e4: r0 = BoxInt64Instr(r4)
    //     0xa486e4: sbfiz           x0, x4, #1, #0x1f
    //     0xa486e8: cmp             x4, x0, asr #1
    //     0xa486ec: b.eq            #0xa486f8
    //     0xa486f0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa486f4: stur            x4, [x0, #7]
    // 0xa486f8: mov             x1, x2
    // 0xa486fc: ArrayStore: r1[9] = r0  ; List_4
    //     0xa486fc: add             x25, x1, #0x33
    //     0xa48700: str             w0, [x25]
    //     0xa48704: tbz             w0, #0, #0xa48720
    //     0xa48708: ldurb           w16, [x1, #-1]
    //     0xa4870c: ldurb           w17, [x0, #-1]
    //     0xa48710: and             x16, x17, x16, lsr #2
    //     0xa48714: tst             x16, HEAP, lsr #32
    //     0xa48718: b.eq            #0xa48720
    //     0xa4871c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48720: r16 = "coming_from"
    //     0xa48720: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa48724: ldr             x16, [x16, #0x328]
    // 0xa48728: StoreField: r2->field_37 = r16
    //     0xa48728: stur            w16, [x2, #0x37]
    // 0xa4872c: LoadField: r0 = r3->field_2f
    //     0xa4872c: ldur            w0, [x3, #0x2f]
    // 0xa48730: DecompressPointer r0
    //     0xa48730: add             x0, x0, HEAP, lsl #32
    // 0xa48734: mov             x1, x2
    // 0xa48738: ArrayStore: r1[11] = r0  ; List_4
    //     0xa48738: add             x25, x1, #0x3b
    //     0xa4873c: str             w0, [x25]
    //     0xa48740: tbz             w0, #0, #0xa4875c
    //     0xa48744: ldurb           w16, [x1, #-1]
    //     0xa48748: ldurb           w17, [x0, #-1]
    //     0xa4874c: and             x16, x17, x16, lsr #2
    //     0xa48750: tst             x16, HEAP, lsr #32
    //     0xa48754: b.eq            #0xa4875c
    //     0xa48758: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4875c: r16 = "is_skipped_address"
    //     0xa4875c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa48760: ldr             x16, [x16, #0xb80]
    // 0xa48764: StoreField: r2->field_3f = r16
    //     0xa48764: stur            w16, [x2, #0x3f]
    // 0xa48768: r16 = true
    //     0xa48768: add             x16, NULL, #0x20  ; true
    // 0xa4876c: StoreField: r2->field_43 = r16
    //     0xa4876c: stur            w16, [x2, #0x43]
    // 0xa48770: r16 = <String, Object?>
    //     0xa48770: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa48774: ldr             x16, [x16, #0xc28]
    // 0xa48778: stp             x2, x16, [SP]
    // 0xa4877c: r0 = Map._fromLiteral()
    //     0xa4877c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa48780: r16 = "/checkout_request_number_page"
    //     0xa48780: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xa48784: ldr             x16, [x16, #0x9f8]
    // 0xa48788: stp             x16, NULL, [SP, #8]
    // 0xa4878c: str             x0, [SP]
    // 0xa48790: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa48790: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa48794: ldr             x4, [x4, #0x438]
    // 0xa48798: r0 = GetNavigation.toNamed()
    //     0xa48798: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4879c: r0 = Null
    //     0xa4879c: mov             x0, NULL
    // 0xa487a0: r0 = ReturnAsyncNotFuture()
    //     0xa487a0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xa487a4: r16 = "controller"
    //     0xa487a4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa487a8: str             x16, [SP]
    // 0xa487ac: r0 = _throwLocalNotInitialized()
    //     0xa487ac: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa487b0: brk             #0
    // 0xa487b4: r16 = "controller"
    //     0xa487b4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa487b8: str             x16, [SP]
    // 0xa487bc: r0 = _throwLocalNotInitialized()
    //     0xa487bc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa487c0: brk             #0
    // 0xa487c4: r16 = "controller"
    //     0xa487c4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa487c8: str             x16, [SP]
    // 0xa487cc: r0 = _throwLocalNotInitialized()
    //     0xa487cc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa487d0: brk             #0
    // 0xa487d4: r16 = "controller"
    //     0xa487d4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa487d8: str             x16, [SP]
    // 0xa487dc: r0 = _throwLocalNotInitialized()
    //     0xa487dc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa487e0: brk             #0
    // 0xa487e4: r16 = "controller"
    //     0xa487e4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa487e8: str             x16, [SP]
    // 0xa487ec: r0 = _throwLocalNotInitialized()
    //     0xa487ec: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa487f0: brk             #0
    // 0xa487f4: r16 = "controller"
    //     0xa487f4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa487f8: str             x16, [SP]
    // 0xa487fc: r0 = _throwLocalNotInitialized()
    //     0xa487fc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa48800: brk             #0
    // 0xa48804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48804: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48808: b               #0xa47ea0
    // 0xa4880c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4880c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa48810: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48810: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa48814: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48814: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa48818: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48818: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4881c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4881c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleExchangeProductBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa48820, size: 0x1b8
    // 0xa48820: EnterFrame
    //     0xa48820: stp             fp, lr, [SP, #-0x10]!
    //     0xa48824: mov             fp, SP
    // 0xa48828: AllocStack(0x48)
    //     0xa48828: sub             SP, SP, #0x48
    // 0xa4882c: SetupParameters()
    //     0xa4882c: ldr             x0, [fp, #0x18]
    //     0xa48830: ldur            w2, [x0, #0x17]
    //     0xa48834: add             x2, x2, HEAP, lsl #32
    //     0xa48838: stur            x2, [fp, #-0x28]
    // 0xa4883c: CheckStackOverflow
    //     0xa4883c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48840: cmp             SP, x16
    //     0xa48844: b.ls            #0xa489cc
    // 0xa48848: LoadField: r0 = r2->field_b
    //     0xa48848: ldur            w0, [x2, #0xb]
    // 0xa4884c: DecompressPointer r0
    //     0xa4884c: add             x0, x0, HEAP, lsl #32
    // 0xa48850: LoadField: r1 = r0->field_f
    //     0xa48850: ldur            w1, [x0, #0xf]
    // 0xa48854: DecompressPointer r1
    //     0xa48854: add             x1, x1, HEAP, lsl #32
    // 0xa48858: LoadField: r0 = r1->field_b
    //     0xa48858: ldur            w0, [x1, #0xb]
    // 0xa4885c: DecompressPointer r0
    //     0xa4885c: add             x0, x0, HEAP, lsl #32
    // 0xa48860: cmp             w0, NULL
    // 0xa48864: b.eq            #0xa489d4
    // 0xa48868: LoadField: r3 = r0->field_47
    //     0xa48868: ldur            w3, [x0, #0x47]
    // 0xa4886c: DecompressPointer r3
    //     0xa4886c: add             x3, x3, HEAP, lsl #32
    // 0xa48870: stur            x3, [fp, #-0x20]
    // 0xa48874: LoadField: r4 = r0->field_37
    //     0xa48874: ldur            w4, [x0, #0x37]
    // 0xa48878: DecompressPointer r4
    //     0xa48878: add             x4, x4, HEAP, lsl #32
    // 0xa4887c: stur            x4, [fp, #-0x18]
    // 0xa48880: LoadField: r5 = r0->field_3b
    //     0xa48880: ldur            w5, [x0, #0x3b]
    // 0xa48884: DecompressPointer r5
    //     0xa48884: add             x5, x5, HEAP, lsl #32
    // 0xa48888: stur            x5, [fp, #-0x10]
    // 0xa4888c: LoadField: r6 = r0->field_3f
    //     0xa4888c: ldur            w6, [x0, #0x3f]
    // 0xa48890: DecompressPointer r6
    //     0xa48890: add             x6, x6, HEAP, lsl #32
    // 0xa48894: stur            x6, [fp, #-8]
    // 0xa48898: ArrayLoad: r7 = r0[0]  ; List_8
    //     0xa48898: ldur            x7, [x0, #0x17]
    // 0xa4889c: r0 = BoxInt64Instr(r7)
    //     0xa4889c: sbfiz           x0, x7, #1, #0x1f
    //     0xa488a0: cmp             x7, x0, asr #1
    //     0xa488a4: b.eq            #0xa488b0
    //     0xa488a8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa488ac: stur            x7, [x0, #7]
    // 0xa488b0: r1 = 60
    //     0xa488b0: movz            x1, #0x3c
    // 0xa488b4: branchIfSmi(r0, 0xa488c0)
    //     0xa488b4: tbz             w0, #0, #0xa488c0
    // 0xa488b8: r1 = LoadClassIdInstr(r0)
    //     0xa488b8: ldur            x1, [x0, #-1]
    //     0xa488bc: ubfx            x1, x1, #0xc, #0x14
    // 0xa488c0: str             x0, [SP]
    // 0xa488c4: mov             x0, x1
    // 0xa488c8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa488c8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa488cc: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa488cc: movz            x17, #0x2700
    //     0xa488d0: add             lr, x0, x17
    //     0xa488d4: ldr             lr, [x21, lr, lsl #3]
    //     0xa488d8: blr             lr
    // 0xa488dc: ldur            x2, [fp, #-0x28]
    // 0xa488e0: stur            x0, [fp, #-0x30]
    // 0xa488e4: LoadField: r1 = r2->field_f
    //     0xa488e4: ldur            w1, [x2, #0xf]
    // 0xa488e8: DecompressPointer r1
    //     0xa488e8: add             x1, x1, HEAP, lsl #32
    // 0xa488ec: r16 = Sentinel
    //     0xa488ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa488f0: cmp             w1, w16
    // 0xa488f4: b.eq            #0xa489bc
    // 0xa488f8: ldur            x3, [fp, #-0x20]
    // 0xa488fc: ldur            x4, [fp, #-0x18]
    // 0xa48900: ldur            x5, [fp, #-0x10]
    // 0xa48904: ldur            x6, [fp, #-8]
    // 0xa48908: str             x1, [SP]
    // 0xa4890c: r4 = 0
    //     0xa4890c: movz            x4, #0
    // 0xa48910: ldr             x0, [SP]
    // 0xa48914: r16 = UnlinkedCall_0x613b5c
    //     0xa48914: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa48918: add             x16, x16, #0xc40
    // 0xa4891c: ldp             x5, lr, [x16]
    // 0xa48920: blr             lr
    // 0xa48924: stur            x0, [fp, #-0x38]
    // 0xa48928: r0 = SingleExchangeProductBottomSheet()
    //     0xa48928: bl              #0xa489d8  ; AllocateSingleExchangeProductBottomSheetStub -> SingleExchangeProductBottomSheet (size=0x34)
    // 0xa4892c: mov             x3, x0
    // 0xa48930: ldur            x0, [fp, #-0x20]
    // 0xa48934: stur            x3, [fp, #-0x40]
    // 0xa48938: StoreField: r3->field_b = r0
    //     0xa48938: stur            w0, [x3, #0xb]
    // 0xa4893c: ldur            x0, [fp, #-0x18]
    // 0xa48940: StoreField: r3->field_f = r0
    //     0xa48940: stur            w0, [x3, #0xf]
    // 0xa48944: ldur            x0, [fp, #-0x10]
    // 0xa48948: ArrayStore: r3[0] = r0  ; List_4
    //     0xa48948: stur            w0, [x3, #0x17]
    // 0xa4894c: ldur            x0, [fp, #-8]
    // 0xa48950: StoreField: r3->field_13 = r0
    //     0xa48950: stur            w0, [x3, #0x13]
    // 0xa48954: ldur            x0, [fp, #-0x30]
    // 0xa48958: StoreField: r3->field_1b = r0
    //     0xa48958: stur            w0, [x3, #0x1b]
    // 0xa4895c: ldur            x2, [fp, #-0x28]
    // 0xa48960: r1 = Function '<anonymous closure>':.
    //     0xa48960: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ac50] AnonymousClosure: (0xa4ae40), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa48964: ldr             x1, [x1, #0xc50]
    // 0xa48968: r0 = AllocateClosure()
    //     0xa48968: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4896c: mov             x1, x0
    // 0xa48970: ldur            x0, [fp, #-0x40]
    // 0xa48974: StoreField: r0->field_1f = r1
    //     0xa48974: stur            w1, [x0, #0x1f]
    // 0xa48978: ldur            x2, [fp, #-0x28]
    // 0xa4897c: r1 = Function '<anonymous closure>':.
    //     0xa4897c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ac58] AnonymousClosure: (0xa48a08), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa48980: ldr             x1, [x1, #0xc58]
    // 0xa48984: r0 = AllocateClosure()
    //     0xa48984: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa48988: mov             x1, x0
    // 0xa4898c: ldur            x0, [fp, #-0x40]
    // 0xa48990: StoreField: r0->field_23 = r1
    //     0xa48990: stur            w1, [x0, #0x23]
    // 0xa48994: r1 = const []
    //     0xa48994: add             x1, PP, #0x56, lsl #12  ; [pp+0x56490] List<ProductCustomisation>(0)
    //     0xa48998: ldr             x1, [x1, #0x490]
    // 0xa4899c: StoreField: r0->field_2b = r1
    //     0xa4899c: stur            w1, [x0, #0x2b]
    // 0xa489a0: r1 = ""
    //     0xa489a0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa489a4: StoreField: r0->field_27 = r1
    //     0xa489a4: stur            w1, [x0, #0x27]
    // 0xa489a8: ldur            x1, [fp, #-0x38]
    // 0xa489ac: StoreField: r0->field_2f = r1
    //     0xa489ac: stur            w1, [x0, #0x2f]
    // 0xa489b0: LeaveFrame
    //     0xa489b0: mov             SP, fp
    //     0xa489b4: ldp             fp, lr, [SP], #0x10
    // 0xa489b8: ret
    //     0xa489b8: ret             
    // 0xa489bc: r16 = "controller"
    //     0xa489bc: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa489c0: str             x16, [SP]
    // 0xa489c4: r0 = _throwLocalNotInitialized()
    //     0xa489c4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa489c8: brk             #0
    // 0xa489cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa489cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa489d0: b               #0xa48848
    // 0xa489d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa489d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xa48a08, size: 0xcc
    // 0xa48a08: EnterFrame
    //     0xa48a08: stp             fp, lr, [SP, #-0x10]!
    //     0xa48a0c: mov             fp, SP
    // 0xa48a10: AllocStack(0x20)
    //     0xa48a10: sub             SP, SP, #0x20
    // 0xa48a14: SetupParameters()
    //     0xa48a14: ldr             x0, [fp, #0x10]
    //     0xa48a18: ldur            w2, [x0, #0x17]
    //     0xa48a1c: add             x2, x2, HEAP, lsl #32
    //     0xa48a20: stur            x2, [fp, #-8]
    // 0xa48a24: CheckStackOverflow
    //     0xa48a24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48a28: cmp             SP, x16
    //     0xa48a2c: b.ls            #0xa48acc
    // 0xa48a30: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa48a30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48a34: ldr             x0, [x0, #0x1c80]
    //     0xa48a38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa48a3c: cmp             w0, w16
    //     0xa48a40: b.ne            #0xa48a4c
    //     0xa48a44: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa48a48: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa48a4c: str             NULL, [SP]
    // 0xa48a50: r4 = const [0x1, 0, 0, 0, null]
    //     0xa48a50: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa48a54: r0 = GetNavigation.back()
    //     0xa48a54: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa48a58: r16 = PreferenceManager
    //     0xa48a58: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xa48a5c: ldr             x16, [x16, #0x878]
    // 0xa48a60: str             x16, [SP]
    // 0xa48a64: r0 = toString()
    //     0xa48a64: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xa48a68: r16 = <PreferenceManager>
    //     0xa48a68: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xa48a6c: ldr             x16, [x16, #0x880]
    // 0xa48a70: stp             x0, x16, [SP]
    // 0xa48a74: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xa48a74: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xa48a78: r0 = Inst.find()
    //     0xa48a78: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa48a7c: mov             x1, x0
    // 0xa48a80: r2 = "token"
    //     0xa48a80: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xa48a84: ldr             x2, [x2, #0x958]
    // 0xa48a88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa48a88: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa48a8c: r0 = getString()
    //     0xa48a8c: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xa48a90: ldur            x2, [fp, #-8]
    // 0xa48a94: r1 = Function '<anonymous closure>':.
    //     0xa48a94: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ac60] AnonymousClosure: (0xa48ad4), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa48a98: ldr             x1, [x1, #0xc60]
    // 0xa48a9c: stur            x0, [fp, #-8]
    // 0xa48aa0: r0 = AllocateClosure()
    //     0xa48aa0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa48aa4: r16 = <Null?>
    //     0xa48aa4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa48aa8: ldur            lr, [fp, #-8]
    // 0xa48aac: stp             lr, x16, [SP, #8]
    // 0xa48ab0: str             x0, [SP]
    // 0xa48ab4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa48ab4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa48ab8: r0 = then()
    //     0xa48ab8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa48abc: r0 = Null
    //     0xa48abc: mov             x0, NULL
    // 0xa48ac0: LeaveFrame
    //     0xa48ac0: mov             SP, fp
    //     0xa48ac4: ldp             fp, lr, [SP], #0x10
    // 0xa48ac8: ret
    //     0xa48ac8: ret             
    // 0xa48acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48acc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48ad0: b               #0xa48a30
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xa4ae40, size: 0xcc
    // 0xa4ae40: EnterFrame
    //     0xa4ae40: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ae44: mov             fp, SP
    // 0xa4ae48: AllocStack(0x20)
    //     0xa4ae48: sub             SP, SP, #0x20
    // 0xa4ae4c: SetupParameters()
    //     0xa4ae4c: ldr             x0, [fp, #0x10]
    //     0xa4ae50: ldur            w2, [x0, #0x17]
    //     0xa4ae54: add             x2, x2, HEAP, lsl #32
    //     0xa4ae58: stur            x2, [fp, #-8]
    // 0xa4ae5c: CheckStackOverflow
    //     0xa4ae5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ae60: cmp             SP, x16
    //     0xa4ae64: b.ls            #0xa4af04
    // 0xa4ae68: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4ae68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4ae6c: ldr             x0, [x0, #0x1c80]
    //     0xa4ae70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4ae74: cmp             w0, w16
    //     0xa4ae78: b.ne            #0xa4ae84
    //     0xa4ae7c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4ae80: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4ae84: str             NULL, [SP]
    // 0xa4ae88: r4 = const [0x1, 0, 0, 0, null]
    //     0xa4ae88: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa4ae8c: r0 = GetNavigation.back()
    //     0xa4ae8c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa4ae90: r16 = PreferenceManager
    //     0xa4ae90: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xa4ae94: ldr             x16, [x16, #0x878]
    // 0xa4ae98: str             x16, [SP]
    // 0xa4ae9c: r0 = toString()
    //     0xa4ae9c: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xa4aea0: r16 = <PreferenceManager>
    //     0xa4aea0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xa4aea4: ldr             x16, [x16, #0x880]
    // 0xa4aea8: stp             x0, x16, [SP]
    // 0xa4aeac: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xa4aeac: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xa4aeb0: r0 = Inst.find()
    //     0xa4aeb0: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa4aeb4: mov             x1, x0
    // 0xa4aeb8: r2 = "token"
    //     0xa4aeb8: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xa4aebc: ldr             x2, [x2, #0x958]
    // 0xa4aec0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4aec0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4aec4: r0 = getString()
    //     0xa4aec4: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xa4aec8: ldur            x2, [fp, #-8]
    // 0xa4aecc: r1 = Function '<anonymous closure>':.
    //     0xa4aecc: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ac68] AnonymousClosure: (0xa4af0c), in [package:customer_app/app/presentation/views/basic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xa46418)
    //     0xa4aed0: ldr             x1, [x1, #0xc68]
    // 0xa4aed4: stur            x0, [fp, #-8]
    // 0xa4aed8: r0 = AllocateClosure()
    //     0xa4aed8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4aedc: r16 = <Null?>
    //     0xa4aedc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa4aee0: ldur            lr, [fp, #-8]
    // 0xa4aee4: stp             lr, x16, [SP, #8]
    // 0xa4aee8: str             x0, [SP]
    // 0xa4aeec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa4aeec: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa4aef0: r0 = then()
    //     0xa4aef0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa4aef4: r0 = Null
    //     0xa4aef4: mov             x0, NULL
    // 0xa4aef8: LeaveFrame
    //     0xa4aef8: mov             SP, fp
    //     0xa4aefc: ldp             fp, lr, [SP], #0x10
    // 0xa4af00: ret
    //     0xa4af00: ret             
    // 0xa4af04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4af04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4af08: b               #0xa4ae68
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xa4af0c, size: 0x9f8
    // 0xa4af0c: EnterFrame
    //     0xa4af0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4af10: mov             fp, SP
    // 0xa4af14: AllocStack(0x50)
    //     0xa4af14: sub             SP, SP, #0x50
    // 0xa4af18: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xa4af18: stur            NULL, [fp, #-8]
    //     0xa4af1c: movz            x0, #0
    //     0xa4af20: add             x1, fp, w0, sxtw #2
    //     0xa4af24: ldr             x1, [x1, #0x18]
    //     0xa4af28: add             x2, fp, w0, sxtw #2
    //     0xa4af2c: ldr             x2, [x2, #0x10]
    //     0xa4af30: stur            x2, [fp, #-0x18]
    //     0xa4af34: ldur            w3, [x1, #0x17]
    //     0xa4af38: add             x3, x3, HEAP, lsl #32
    //     0xa4af3c: stur            x3, [fp, #-0x10]
    // 0xa4af40: CheckStackOverflow
    //     0xa4af40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4af44: cmp             SP, x16
    //     0xa4af48: b.ls            #0xa4b8e8
    // 0xa4af4c: InitAsync() -> Future<Null?>
    //     0xa4af4c: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xa4af50: bl              #0x6326e0  ; InitAsyncStub
    // 0xa4af54: ldur            x0, [fp, #-0x18]
    // 0xa4af58: r1 = LoadClassIdInstr(r0)
    //     0xa4af58: ldur            x1, [x0, #-1]
    //     0xa4af5c: ubfx            x1, x1, #0xc, #0x14
    // 0xa4af60: r16 = ""
    //     0xa4af60: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4af64: stp             x16, x0, [SP]
    // 0xa4af68: mov             x0, x1
    // 0xa4af6c: mov             lr, x0
    // 0xa4af70: ldr             lr, [x21, lr, lsl #3]
    // 0xa4af74: blr             lr
    // 0xa4af78: tbz             w0, #4, #0xa4b640
    // 0xa4af7c: ldur            x2, [fp, #-0x10]
    // 0xa4af80: LoadField: r3 = r2->field_b
    //     0xa4af80: ldur            w3, [x2, #0xb]
    // 0xa4af84: DecompressPointer r3
    //     0xa4af84: add             x3, x3, HEAP, lsl #32
    // 0xa4af88: stur            x3, [fp, #-0x28]
    // 0xa4af8c: LoadField: r0 = r3->field_f
    //     0xa4af8c: ldur            w0, [x3, #0xf]
    // 0xa4af90: DecompressPointer r0
    //     0xa4af90: add             x0, x0, HEAP, lsl #32
    // 0xa4af94: LoadField: r1 = r0->field_b
    //     0xa4af94: ldur            w1, [x0, #0xb]
    // 0xa4af98: DecompressPointer r1
    //     0xa4af98: add             x1, x1, HEAP, lsl #32
    // 0xa4af9c: cmp             w1, NULL
    // 0xa4afa0: b.eq            #0xa4b8f0
    // 0xa4afa4: LoadField: r4 = r1->field_f
    //     0xa4afa4: ldur            w4, [x1, #0xf]
    // 0xa4afa8: DecompressPointer r4
    //     0xa4afa8: add             x4, x4, HEAP, lsl #32
    // 0xa4afac: stur            x4, [fp, #-0x18]
    // 0xa4afb0: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xa4afb0: ldur            x5, [x1, #0x17]
    // 0xa4afb4: stur            x5, [fp, #-0x20]
    // 0xa4afb8: LoadField: r0 = r1->field_27
    //     0xa4afb8: ldur            x0, [x1, #0x27]
    // 0xa4afbc: mul             x6, x0, x5
    // 0xa4afc0: r0 = BoxInt64Instr(r6)
    //     0xa4afc0: sbfiz           x0, x6, #1, #0x1f
    //     0xa4afc4: cmp             x6, x0, asr #1
    //     0xa4afc8: b.eq            #0xa4afd4
    //     0xa4afcc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4afd0: stur            x6, [x0, #7]
    // 0xa4afd4: stp             x0, NULL, [SP]
    // 0xa4afd8: r0 = _Double.fromInteger()
    //     0xa4afd8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa4afdc: stur            x0, [fp, #-0x30]
    // 0xa4afe0: r0 = CheckoutEventData()
    //     0xa4afe0: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa4afe4: mov             x2, x0
    // 0xa4afe8: ldur            x0, [fp, #-0x30]
    // 0xa4afec: stur            x2, [fp, #-0x38]
    // 0xa4aff0: StoreField: r2->field_7 = r0
    //     0xa4aff0: stur            w0, [x2, #7]
    // 0xa4aff4: ldur            x3, [fp, #-0x20]
    // 0xa4aff8: r0 = BoxInt64Instr(r3)
    //     0xa4aff8: sbfiz           x0, x3, #1, #0x1f
    //     0xa4affc: cmp             x3, x0, asr #1
    //     0xa4b000: b.eq            #0xa4b00c
    //     0xa4b004: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b008: stur            x3, [x0, #7]
    // 0xa4b00c: StoreField: r2->field_b = r0
    //     0xa4b00c: stur            w0, [x2, #0xb]
    // 0xa4b010: ldur            x0, [fp, #-0x18]
    // 0xa4b014: StoreField: r2->field_f = r0
    //     0xa4b014: stur            w0, [x2, #0xf]
    // 0xa4b018: ldur            x0, [fp, #-0x10]
    // 0xa4b01c: LoadField: r1 = r0->field_f
    //     0xa4b01c: ldur            w1, [x0, #0xf]
    // 0xa4b020: DecompressPointer r1
    //     0xa4b020: add             x1, x1, HEAP, lsl #32
    // 0xa4b024: r16 = Sentinel
    //     0xa4b024: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b028: cmp             w1, w16
    // 0xa4b02c: b.eq            #0xa4b888
    // 0xa4b030: str             x1, [SP]
    // 0xa4b034: r4 = 0
    //     0xa4b034: movz            x4, #0
    // 0xa4b038: ldr             x0, [SP]
    // 0xa4b03c: r16 = UnlinkedCall_0x613b5c
    //     0xa4b03c: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4b040: add             x16, x16, #0xc70
    // 0xa4b044: ldp             x5, lr, [x16]
    // 0xa4b048: blr             lr
    // 0xa4b04c: LoadField: r1 = r0->field_1b
    //     0xa4b04c: ldur            w1, [x0, #0x1b]
    // 0xa4b050: DecompressPointer r1
    //     0xa4b050: add             x1, x1, HEAP, lsl #32
    // 0xa4b054: cmp             w1, NULL
    // 0xa4b058: b.ne            #0xa4b064
    // 0xa4b05c: r0 = Null
    //     0xa4b05c: mov             x0, NULL
    // 0xa4b060: b               #0xa4b07c
    // 0xa4b064: LoadField: r0 = r1->field_b
    //     0xa4b064: ldur            w0, [x1, #0xb]
    // 0xa4b068: cbz             w0, #0xa4b074
    // 0xa4b06c: r1 = false
    //     0xa4b06c: add             x1, NULL, #0x30  ; false
    // 0xa4b070: b               #0xa4b078
    // 0xa4b074: r1 = true
    //     0xa4b074: add             x1, NULL, #0x20  ; true
    // 0xa4b078: mov             x0, x1
    // 0xa4b07c: cmp             w0, NULL
    // 0xa4b080: b.eq            #0xa4b088
    // 0xa4b084: tbz             w0, #4, #0xa4b130
    // 0xa4b088: ldur            x0, [fp, #-0x10]
    // 0xa4b08c: LoadField: r1 = r0->field_f
    //     0xa4b08c: ldur            w1, [x0, #0xf]
    // 0xa4b090: DecompressPointer r1
    //     0xa4b090: add             x1, x1, HEAP, lsl #32
    // 0xa4b094: r16 = Sentinel
    //     0xa4b094: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b098: cmp             w1, w16
    // 0xa4b09c: b.eq            #0xa4b898
    // 0xa4b0a0: str             x1, [SP]
    // 0xa4b0a4: r4 = 0
    //     0xa4b0a4: movz            x4, #0
    // 0xa4b0a8: ldr             x0, [SP]
    // 0xa4b0ac: r16 = UnlinkedCall_0x613b5c
    //     0xa4b0ac: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4b0b0: add             x16, x16, #0xc80
    // 0xa4b0b4: ldp             x5, lr, [x16]
    // 0xa4b0b8: blr             lr
    // 0xa4b0bc: LoadField: r1 = r0->field_1b
    //     0xa4b0bc: ldur            w1, [x0, #0x1b]
    // 0xa4b0c0: DecompressPointer r1
    //     0xa4b0c0: add             x1, x1, HEAP, lsl #32
    // 0xa4b0c4: cmp             w1, NULL
    // 0xa4b0c8: b.ne            #0xa4b0d4
    // 0xa4b0cc: r0 = Null
    //     0xa4b0cc: mov             x0, NULL
    // 0xa4b0d0: b               #0xa4b118
    // 0xa4b0d4: r0 = first()
    //     0xa4b0d4: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xa4b0d8: cmp             w0, NULL
    // 0xa4b0dc: b.ne            #0xa4b0e8
    // 0xa4b0e0: r0 = Null
    //     0xa4b0e0: mov             x0, NULL
    // 0xa4b0e4: b               #0xa4b118
    // 0xa4b0e8: LoadField: r1 = r0->field_13
    //     0xa4b0e8: ldur            w1, [x0, #0x13]
    // 0xa4b0ec: DecompressPointer r1
    //     0xa4b0ec: add             x1, x1, HEAP, lsl #32
    // 0xa4b0f0: cmp             w1, NULL
    // 0xa4b0f4: b.ne            #0xa4b100
    // 0xa4b0f8: r0 = Null
    //     0xa4b0f8: mov             x0, NULL
    // 0xa4b0fc: b               #0xa4b118
    // 0xa4b100: LoadField: r0 = r1->field_7
    //     0xa4b100: ldur            w0, [x1, #7]
    // 0xa4b104: cbz             w0, #0xa4b110
    // 0xa4b108: r1 = false
    //     0xa4b108: add             x1, NULL, #0x30  ; false
    // 0xa4b10c: b               #0xa4b114
    // 0xa4b110: r1 = true
    //     0xa4b110: add             x1, NULL, #0x20  ; true
    // 0xa4b114: mov             x0, x1
    // 0xa4b118: cmp             w0, NULL
    // 0xa4b11c: b.ne            #0xa4b12c
    // 0xa4b120: ldur            x2, [fp, #-0x10]
    // 0xa4b124: ldur            x0, [fp, #-0x28]
    // 0xa4b128: b               #0xa4b3c0
    // 0xa4b12c: tbnz            w0, #4, #0xa4b3b8
    // 0xa4b130: ldur            x0, [fp, #-0x10]
    // 0xa4b134: ldur            x1, [fp, #-0x28]
    // 0xa4b138: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4b138: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4b13c: ldr             x0, [x0, #0x1c80]
    //     0xa4b140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4b144: cmp             w0, w16
    //     0xa4b148: b.ne            #0xa4b154
    //     0xa4b14c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4b150: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4b154: r1 = Null
    //     0xa4b154: mov             x1, NULL
    // 0xa4b158: r2 = 40
    //     0xa4b158: movz            x2, #0x28
    // 0xa4b15c: r0 = AllocateArray()
    //     0xa4b15c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4b160: mov             x2, x0
    // 0xa4b164: stur            x2, [fp, #-0x18]
    // 0xa4b168: r16 = "couponCode"
    //     0xa4b168: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa4b16c: ldr             x16, [x16, #0x310]
    // 0xa4b170: StoreField: r2->field_f = r16
    //     0xa4b170: stur            w16, [x2, #0xf]
    // 0xa4b174: r16 = ""
    //     0xa4b174: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4b178: StoreField: r2->field_13 = r16
    //     0xa4b178: stur            w16, [x2, #0x13]
    // 0xa4b17c: r16 = "product_id"
    //     0xa4b17c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4b180: ldr             x16, [x16, #0x9b8]
    // 0xa4b184: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4b184: stur            w16, [x2, #0x17]
    // 0xa4b188: ldur            x0, [fp, #-0x28]
    // 0xa4b18c: LoadField: r1 = r0->field_f
    //     0xa4b18c: ldur            w1, [x0, #0xf]
    // 0xa4b190: DecompressPointer r1
    //     0xa4b190: add             x1, x1, HEAP, lsl #32
    // 0xa4b194: LoadField: r3 = r1->field_b
    //     0xa4b194: ldur            w3, [x1, #0xb]
    // 0xa4b198: DecompressPointer r3
    //     0xa4b198: add             x3, x3, HEAP, lsl #32
    // 0xa4b19c: cmp             w3, NULL
    // 0xa4b1a0: b.eq            #0xa4b8f4
    // 0xa4b1a4: LoadField: r0 = r3->field_f
    //     0xa4b1a4: ldur            w0, [x3, #0xf]
    // 0xa4b1a8: DecompressPointer r0
    //     0xa4b1a8: add             x0, x0, HEAP, lsl #32
    // 0xa4b1ac: StoreField: r2->field_1b = r0
    //     0xa4b1ac: stur            w0, [x2, #0x1b]
    // 0xa4b1b0: r16 = "sku_id"
    //     0xa4b1b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa4b1b4: ldr             x16, [x16, #0x498]
    // 0xa4b1b8: StoreField: r2->field_1f = r16
    //     0xa4b1b8: stur            w16, [x2, #0x1f]
    // 0xa4b1bc: LoadField: r0 = r3->field_13
    //     0xa4b1bc: ldur            w0, [x3, #0x13]
    // 0xa4b1c0: DecompressPointer r0
    //     0xa4b1c0: add             x0, x0, HEAP, lsl #32
    // 0xa4b1c4: StoreField: r2->field_23 = r0
    //     0xa4b1c4: stur            w0, [x2, #0x23]
    // 0xa4b1c8: r16 = "quantity"
    //     0xa4b1c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa4b1cc: ldr             x16, [x16, #0x428]
    // 0xa4b1d0: StoreField: r2->field_27 = r16
    //     0xa4b1d0: stur            w16, [x2, #0x27]
    // 0xa4b1d4: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa4b1d4: ldur            x4, [x3, #0x17]
    // 0xa4b1d8: r0 = BoxInt64Instr(r4)
    //     0xa4b1d8: sbfiz           x0, x4, #1, #0x1f
    //     0xa4b1dc: cmp             x4, x0, asr #1
    //     0xa4b1e0: b.eq            #0xa4b1ec
    //     0xa4b1e4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b1e8: stur            x4, [x0, #7]
    // 0xa4b1ec: mov             x1, x2
    // 0xa4b1f0: ArrayStore: r1[7] = r0  ; List_4
    //     0xa4b1f0: add             x25, x1, #0x2b
    //     0xa4b1f4: str             w0, [x25]
    //     0xa4b1f8: tbz             w0, #0, #0xa4b214
    //     0xa4b1fc: ldurb           w16, [x1, #-1]
    //     0xa4b200: ldurb           w17, [x0, #-1]
    //     0xa4b204: and             x16, x17, x16, lsr #2
    //     0xa4b208: tst             x16, HEAP, lsr #32
    //     0xa4b20c: b.eq            #0xa4b214
    //     0xa4b210: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b214: r16 = "checkout_event_data"
    //     0xa4b214: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4b218: ldr             x16, [x16, #0xd50]
    // 0xa4b21c: StoreField: r2->field_2f = r16
    //     0xa4b21c: stur            w16, [x2, #0x2f]
    // 0xa4b220: mov             x1, x2
    // 0xa4b224: ldur            x0, [fp, #-0x38]
    // 0xa4b228: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4b228: add             x25, x1, #0x33
    //     0xa4b22c: str             w0, [x25]
    //     0xa4b230: tbz             w0, #0, #0xa4b24c
    //     0xa4b234: ldurb           w16, [x1, #-1]
    //     0xa4b238: ldurb           w17, [x0, #-1]
    //     0xa4b23c: and             x16, x17, x16, lsr #2
    //     0xa4b240: tst             x16, HEAP, lsr #32
    //     0xa4b244: b.eq            #0xa4b24c
    //     0xa4b248: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b24c: r16 = "previousScreenSource"
    //     0xa4b24c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4b250: ldr             x16, [x16, #0x448]
    // 0xa4b254: StoreField: r2->field_37 = r16
    //     0xa4b254: stur            w16, [x2, #0x37]
    // 0xa4b258: r16 = "product_page"
    //     0xa4b258: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4b25c: ldr             x16, [x16, #0x480]
    // 0xa4b260: StoreField: r2->field_3b = r16
    //     0xa4b260: stur            w16, [x2, #0x3b]
    // 0xa4b264: r16 = "is_skipped_address"
    //     0xa4b264: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4b268: ldr             x16, [x16, #0xb80]
    // 0xa4b26c: StoreField: r2->field_3f = r16
    //     0xa4b26c: stur            w16, [x2, #0x3f]
    // 0xa4b270: r16 = true
    //     0xa4b270: add             x16, NULL, #0x20  ; true
    // 0xa4b274: StoreField: r2->field_43 = r16
    //     0xa4b274: stur            w16, [x2, #0x43]
    // 0xa4b278: r16 = "coming_from"
    //     0xa4b278: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa4b27c: ldr             x16, [x16, #0x328]
    // 0xa4b280: StoreField: r2->field_47 = r16
    //     0xa4b280: stur            w16, [x2, #0x47]
    // 0xa4b284: LoadField: r0 = r3->field_2f
    //     0xa4b284: ldur            w0, [x3, #0x2f]
    // 0xa4b288: DecompressPointer r0
    //     0xa4b288: add             x0, x0, HEAP, lsl #32
    // 0xa4b28c: mov             x1, x2
    // 0xa4b290: ArrayStore: r1[15] = r0  ; List_4
    //     0xa4b290: add             x25, x1, #0x4b
    //     0xa4b294: str             w0, [x25]
    //     0xa4b298: tbz             w0, #0, #0xa4b2b4
    //     0xa4b29c: ldurb           w16, [x1, #-1]
    //     0xa4b2a0: ldurb           w17, [x0, #-1]
    //     0xa4b2a4: and             x16, x17, x16, lsr #2
    //     0xa4b2a8: tst             x16, HEAP, lsr #32
    //     0xa4b2ac: b.eq            #0xa4b2b4
    //     0xa4b2b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b2b4: r16 = "checkout_id"
    //     0xa4b2b4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa4b2b8: ldr             x16, [x16, #0xb88]
    // 0xa4b2bc: StoreField: r2->field_4f = r16
    //     0xa4b2bc: stur            w16, [x2, #0x4f]
    // 0xa4b2c0: ldur            x0, [fp, #-0x10]
    // 0xa4b2c4: LoadField: r1 = r0->field_f
    //     0xa4b2c4: ldur            w1, [x0, #0xf]
    // 0xa4b2c8: DecompressPointer r1
    //     0xa4b2c8: add             x1, x1, HEAP, lsl #32
    // 0xa4b2cc: r16 = Sentinel
    //     0xa4b2cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b2d0: cmp             w1, w16
    // 0xa4b2d4: b.eq            #0xa4b8a8
    // 0xa4b2d8: str             x1, [SP]
    // 0xa4b2dc: r4 = 0
    //     0xa4b2dc: movz            x4, #0
    // 0xa4b2e0: ldr             x0, [SP]
    // 0xa4b2e4: r16 = UnlinkedCall_0x613b5c
    //     0xa4b2e4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ac90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4b2e8: add             x16, x16, #0xc90
    // 0xa4b2ec: ldp             x5, lr, [x16]
    // 0xa4b2f0: blr             lr
    // 0xa4b2f4: ldur            x1, [fp, #-0x18]
    // 0xa4b2f8: ArrayStore: r1[17] = r0  ; List_4
    //     0xa4b2f8: add             x25, x1, #0x53
    //     0xa4b2fc: str             w0, [x25]
    //     0xa4b300: tbz             w0, #0, #0xa4b31c
    //     0xa4b304: ldurb           w16, [x1, #-1]
    //     0xa4b308: ldurb           w17, [x0, #-1]
    //     0xa4b30c: and             x16, x17, x16, lsr #2
    //     0xa4b310: tst             x16, HEAP, lsr #32
    //     0xa4b314: b.eq            #0xa4b31c
    //     0xa4b318: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b31c: ldur            x1, [fp, #-0x18]
    // 0xa4b320: r16 = "user_data"
    //     0xa4b320: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa4b324: ldr             x16, [x16, #0x58]
    // 0xa4b328: StoreField: r1->field_57 = r16
    //     0xa4b328: stur            w16, [x1, #0x57]
    // 0xa4b32c: ldur            x2, [fp, #-0x10]
    // 0xa4b330: LoadField: r0 = r2->field_f
    //     0xa4b330: ldur            w0, [x2, #0xf]
    // 0xa4b334: DecompressPointer r0
    //     0xa4b334: add             x0, x0, HEAP, lsl #32
    // 0xa4b338: r16 = Sentinel
    //     0xa4b338: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b33c: cmp             w0, w16
    // 0xa4b340: b.eq            #0xa4b8b8
    // 0xa4b344: str             x0, [SP]
    // 0xa4b348: r4 = 0
    //     0xa4b348: movz            x4, #0
    // 0xa4b34c: ldr             x0, [SP]
    // 0xa4b350: r16 = UnlinkedCall_0x613b5c
    //     0xa4b350: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5aca0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4b354: add             x16, x16, #0xca0
    // 0xa4b358: ldp             x5, lr, [x16]
    // 0xa4b35c: blr             lr
    // 0xa4b360: ldur            x1, [fp, #-0x18]
    // 0xa4b364: ArrayStore: r1[19] = r0  ; List_4
    //     0xa4b364: add             x25, x1, #0x5b
    //     0xa4b368: str             w0, [x25]
    //     0xa4b36c: tbz             w0, #0, #0xa4b388
    //     0xa4b370: ldurb           w16, [x1, #-1]
    //     0xa4b374: ldurb           w17, [x0, #-1]
    //     0xa4b378: and             x16, x17, x16, lsr #2
    //     0xa4b37c: tst             x16, HEAP, lsr #32
    //     0xa4b380: b.eq            #0xa4b388
    //     0xa4b384: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b388: r16 = <String, dynamic>
    //     0xa4b388: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa4b38c: ldur            lr, [fp, #-0x18]
    // 0xa4b390: stp             lr, x16, [SP]
    // 0xa4b394: r0 = Map._fromLiteral()
    //     0xa4b394: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4b398: r16 = "/checkout_request_address_page"
    //     0xa4b398: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xa4b39c: ldr             x16, [x16, #0x9e8]
    // 0xa4b3a0: stp             x16, NULL, [SP, #8]
    // 0xa4b3a4: str             x0, [SP]
    // 0xa4b3a8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4b3a8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa4b3ac: ldr             x4, [x4, #0x438]
    // 0xa4b3b0: r0 = GetNavigation.toNamed()
    //     0xa4b3b0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4b3b4: b               #0xa4b880
    // 0xa4b3b8: ldur            x2, [fp, #-0x10]
    // 0xa4b3bc: ldur            x0, [fp, #-0x28]
    // 0xa4b3c0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4b3c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4b3c4: ldr             x0, [x0, #0x1c80]
    //     0xa4b3c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4b3cc: cmp             w0, w16
    //     0xa4b3d0: b.ne            #0xa4b3dc
    //     0xa4b3d4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4b3d8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4b3dc: r1 = Null
    //     0xa4b3dc: mov             x1, NULL
    // 0xa4b3e0: r2 = 40
    //     0xa4b3e0: movz            x2, #0x28
    // 0xa4b3e4: r0 = AllocateArray()
    //     0xa4b3e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4b3e8: mov             x2, x0
    // 0xa4b3ec: stur            x2, [fp, #-0x18]
    // 0xa4b3f0: r16 = "couponCode"
    //     0xa4b3f0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa4b3f4: ldr             x16, [x16, #0x310]
    // 0xa4b3f8: StoreField: r2->field_f = r16
    //     0xa4b3f8: stur            w16, [x2, #0xf]
    // 0xa4b3fc: r16 = ""
    //     0xa4b3fc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4b400: StoreField: r2->field_13 = r16
    //     0xa4b400: stur            w16, [x2, #0x13]
    // 0xa4b404: r16 = "product_id"
    //     0xa4b404: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4b408: ldr             x16, [x16, #0x9b8]
    // 0xa4b40c: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4b40c: stur            w16, [x2, #0x17]
    // 0xa4b410: ldur            x0, [fp, #-0x28]
    // 0xa4b414: LoadField: r1 = r0->field_f
    //     0xa4b414: ldur            w1, [x0, #0xf]
    // 0xa4b418: DecompressPointer r1
    //     0xa4b418: add             x1, x1, HEAP, lsl #32
    // 0xa4b41c: LoadField: r3 = r1->field_b
    //     0xa4b41c: ldur            w3, [x1, #0xb]
    // 0xa4b420: DecompressPointer r3
    //     0xa4b420: add             x3, x3, HEAP, lsl #32
    // 0xa4b424: cmp             w3, NULL
    // 0xa4b428: b.eq            #0xa4b8f8
    // 0xa4b42c: LoadField: r0 = r3->field_f
    //     0xa4b42c: ldur            w0, [x3, #0xf]
    // 0xa4b430: DecompressPointer r0
    //     0xa4b430: add             x0, x0, HEAP, lsl #32
    // 0xa4b434: StoreField: r2->field_1b = r0
    //     0xa4b434: stur            w0, [x2, #0x1b]
    // 0xa4b438: r16 = "sku_id"
    //     0xa4b438: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa4b43c: ldr             x16, [x16, #0x498]
    // 0xa4b440: StoreField: r2->field_1f = r16
    //     0xa4b440: stur            w16, [x2, #0x1f]
    // 0xa4b444: LoadField: r0 = r3->field_13
    //     0xa4b444: ldur            w0, [x3, #0x13]
    // 0xa4b448: DecompressPointer r0
    //     0xa4b448: add             x0, x0, HEAP, lsl #32
    // 0xa4b44c: StoreField: r2->field_23 = r0
    //     0xa4b44c: stur            w0, [x2, #0x23]
    // 0xa4b450: r16 = "quantity"
    //     0xa4b450: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa4b454: ldr             x16, [x16, #0x428]
    // 0xa4b458: StoreField: r2->field_27 = r16
    //     0xa4b458: stur            w16, [x2, #0x27]
    // 0xa4b45c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa4b45c: ldur            x4, [x3, #0x17]
    // 0xa4b460: r0 = BoxInt64Instr(r4)
    //     0xa4b460: sbfiz           x0, x4, #1, #0x1f
    //     0xa4b464: cmp             x4, x0, asr #1
    //     0xa4b468: b.eq            #0xa4b474
    //     0xa4b46c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b470: stur            x4, [x0, #7]
    // 0xa4b474: mov             x1, x2
    // 0xa4b478: ArrayStore: r1[7] = r0  ; List_4
    //     0xa4b478: add             x25, x1, #0x2b
    //     0xa4b47c: str             w0, [x25]
    //     0xa4b480: tbz             w0, #0, #0xa4b49c
    //     0xa4b484: ldurb           w16, [x1, #-1]
    //     0xa4b488: ldurb           w17, [x0, #-1]
    //     0xa4b48c: and             x16, x17, x16, lsr #2
    //     0xa4b490: tst             x16, HEAP, lsr #32
    //     0xa4b494: b.eq            #0xa4b49c
    //     0xa4b498: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b49c: r16 = "checkout_event_data"
    //     0xa4b49c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4b4a0: ldr             x16, [x16, #0xd50]
    // 0xa4b4a4: StoreField: r2->field_2f = r16
    //     0xa4b4a4: stur            w16, [x2, #0x2f]
    // 0xa4b4a8: mov             x1, x2
    // 0xa4b4ac: ldur            x0, [fp, #-0x38]
    // 0xa4b4b0: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4b4b0: add             x25, x1, #0x33
    //     0xa4b4b4: str             w0, [x25]
    //     0xa4b4b8: tbz             w0, #0, #0xa4b4d4
    //     0xa4b4bc: ldurb           w16, [x1, #-1]
    //     0xa4b4c0: ldurb           w17, [x0, #-1]
    //     0xa4b4c4: and             x16, x17, x16, lsr #2
    //     0xa4b4c8: tst             x16, HEAP, lsr #32
    //     0xa4b4cc: b.eq            #0xa4b4d4
    //     0xa4b4d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b4d4: r16 = "previousScreenSource"
    //     0xa4b4d4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4b4d8: ldr             x16, [x16, #0x448]
    // 0xa4b4dc: StoreField: r2->field_37 = r16
    //     0xa4b4dc: stur            w16, [x2, #0x37]
    // 0xa4b4e0: r16 = "product_page"
    //     0xa4b4e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4b4e4: ldr             x16, [x16, #0x480]
    // 0xa4b4e8: StoreField: r2->field_3b = r16
    //     0xa4b4e8: stur            w16, [x2, #0x3b]
    // 0xa4b4ec: r16 = "is_skipped_address"
    //     0xa4b4ec: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4b4f0: ldr             x16, [x16, #0xb80]
    // 0xa4b4f4: StoreField: r2->field_3f = r16
    //     0xa4b4f4: stur            w16, [x2, #0x3f]
    // 0xa4b4f8: r16 = true
    //     0xa4b4f8: add             x16, NULL, #0x20  ; true
    // 0xa4b4fc: StoreField: r2->field_43 = r16
    //     0xa4b4fc: stur            w16, [x2, #0x43]
    // 0xa4b500: r16 = "coming_from"
    //     0xa4b500: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa4b504: ldr             x16, [x16, #0x328]
    // 0xa4b508: StoreField: r2->field_47 = r16
    //     0xa4b508: stur            w16, [x2, #0x47]
    // 0xa4b50c: LoadField: r0 = r3->field_2f
    //     0xa4b50c: ldur            w0, [x3, #0x2f]
    // 0xa4b510: DecompressPointer r0
    //     0xa4b510: add             x0, x0, HEAP, lsl #32
    // 0xa4b514: mov             x1, x2
    // 0xa4b518: ArrayStore: r1[15] = r0  ; List_4
    //     0xa4b518: add             x25, x1, #0x4b
    //     0xa4b51c: str             w0, [x25]
    //     0xa4b520: tbz             w0, #0, #0xa4b53c
    //     0xa4b524: ldurb           w16, [x1, #-1]
    //     0xa4b528: ldurb           w17, [x0, #-1]
    //     0xa4b52c: and             x16, x17, x16, lsr #2
    //     0xa4b530: tst             x16, HEAP, lsr #32
    //     0xa4b534: b.eq            #0xa4b53c
    //     0xa4b538: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b53c: r16 = "checkout_id"
    //     0xa4b53c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa4b540: ldr             x16, [x16, #0xb88]
    // 0xa4b544: StoreField: r2->field_4f = r16
    //     0xa4b544: stur            w16, [x2, #0x4f]
    // 0xa4b548: ldur            x0, [fp, #-0x10]
    // 0xa4b54c: LoadField: r1 = r0->field_f
    //     0xa4b54c: ldur            w1, [x0, #0xf]
    // 0xa4b550: DecompressPointer r1
    //     0xa4b550: add             x1, x1, HEAP, lsl #32
    // 0xa4b554: r16 = Sentinel
    //     0xa4b554: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b558: cmp             w1, w16
    // 0xa4b55c: b.eq            #0xa4b8c8
    // 0xa4b560: str             x1, [SP]
    // 0xa4b564: r4 = 0
    //     0xa4b564: movz            x4, #0
    // 0xa4b568: ldr             x0, [SP]
    // 0xa4b56c: r16 = UnlinkedCall_0x613b5c
    //     0xa4b56c: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5acb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4b570: add             x16, x16, #0xcb0
    // 0xa4b574: ldp             x5, lr, [x16]
    // 0xa4b578: blr             lr
    // 0xa4b57c: ldur            x1, [fp, #-0x18]
    // 0xa4b580: ArrayStore: r1[17] = r0  ; List_4
    //     0xa4b580: add             x25, x1, #0x53
    //     0xa4b584: str             w0, [x25]
    //     0xa4b588: tbz             w0, #0, #0xa4b5a4
    //     0xa4b58c: ldurb           w16, [x1, #-1]
    //     0xa4b590: ldurb           w17, [x0, #-1]
    //     0xa4b594: and             x16, x17, x16, lsr #2
    //     0xa4b598: tst             x16, HEAP, lsr #32
    //     0xa4b59c: b.eq            #0xa4b5a4
    //     0xa4b5a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b5a4: ldur            x1, [fp, #-0x18]
    // 0xa4b5a8: r16 = "user_data"
    //     0xa4b5a8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa4b5ac: ldr             x16, [x16, #0x58]
    // 0xa4b5b0: StoreField: r1->field_57 = r16
    //     0xa4b5b0: stur            w16, [x1, #0x57]
    // 0xa4b5b4: ldur            x0, [fp, #-0x10]
    // 0xa4b5b8: LoadField: r2 = r0->field_f
    //     0xa4b5b8: ldur            w2, [x0, #0xf]
    // 0xa4b5bc: DecompressPointer r2
    //     0xa4b5bc: add             x2, x2, HEAP, lsl #32
    // 0xa4b5c0: r16 = Sentinel
    //     0xa4b5c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b5c4: cmp             w2, w16
    // 0xa4b5c8: b.eq            #0xa4b8d8
    // 0xa4b5cc: str             x2, [SP]
    // 0xa4b5d0: r4 = 0
    //     0xa4b5d0: movz            x4, #0
    // 0xa4b5d4: ldr             x0, [SP]
    // 0xa4b5d8: r16 = UnlinkedCall_0x613b5c
    //     0xa4b5d8: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5acc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4b5dc: add             x16, x16, #0xcc0
    // 0xa4b5e0: ldp             x5, lr, [x16]
    // 0xa4b5e4: blr             lr
    // 0xa4b5e8: ldur            x1, [fp, #-0x18]
    // 0xa4b5ec: ArrayStore: r1[19] = r0  ; List_4
    //     0xa4b5ec: add             x25, x1, #0x5b
    //     0xa4b5f0: str             w0, [x25]
    //     0xa4b5f4: tbz             w0, #0, #0xa4b610
    //     0xa4b5f8: ldurb           w16, [x1, #-1]
    //     0xa4b5fc: ldurb           w17, [x0, #-1]
    //     0xa4b600: and             x16, x17, x16, lsr #2
    //     0xa4b604: tst             x16, HEAP, lsr #32
    //     0xa4b608: b.eq            #0xa4b610
    //     0xa4b60c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b610: r16 = <String, dynamic>
    //     0xa4b610: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa4b614: ldur            lr, [fp, #-0x18]
    // 0xa4b618: stp             lr, x16, [SP]
    // 0xa4b61c: r0 = Map._fromLiteral()
    //     0xa4b61c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4b620: r16 = "/checkout_order_summary_page"
    //     0xa4b620: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xa4b624: ldr             x16, [x16, #0x9d8]
    // 0xa4b628: stp             x16, NULL, [SP, #8]
    // 0xa4b62c: str             x0, [SP]
    // 0xa4b630: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4b630: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa4b634: ldr             x4, [x4, #0x438]
    // 0xa4b638: r0 = GetNavigation.toNamed()
    //     0xa4b638: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4b63c: b               #0xa4b880
    // 0xa4b640: ldur            x0, [fp, #-0x10]
    // 0xa4b644: LoadField: r2 = r0->field_b
    //     0xa4b644: ldur            w2, [x0, #0xb]
    // 0xa4b648: DecompressPointer r2
    //     0xa4b648: add             x2, x2, HEAP, lsl #32
    // 0xa4b64c: stur            x2, [fp, #-0x18]
    // 0xa4b650: LoadField: r0 = r2->field_f
    //     0xa4b650: ldur            w0, [x2, #0xf]
    // 0xa4b654: DecompressPointer r0
    //     0xa4b654: add             x0, x0, HEAP, lsl #32
    // 0xa4b658: LoadField: r1 = r0->field_b
    //     0xa4b658: ldur            w1, [x0, #0xb]
    // 0xa4b65c: DecompressPointer r1
    //     0xa4b65c: add             x1, x1, HEAP, lsl #32
    // 0xa4b660: cmp             w1, NULL
    // 0xa4b664: b.eq            #0xa4b8fc
    // 0xa4b668: LoadField: r3 = r1->field_f
    //     0xa4b668: ldur            w3, [x1, #0xf]
    // 0xa4b66c: DecompressPointer r3
    //     0xa4b66c: add             x3, x3, HEAP, lsl #32
    // 0xa4b670: stur            x3, [fp, #-0x10]
    // 0xa4b674: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xa4b674: ldur            x4, [x1, #0x17]
    // 0xa4b678: stur            x4, [fp, #-0x20]
    // 0xa4b67c: LoadField: r0 = r1->field_27
    //     0xa4b67c: ldur            x0, [x1, #0x27]
    // 0xa4b680: mul             x5, x0, x4
    // 0xa4b684: r0 = BoxInt64Instr(r5)
    //     0xa4b684: sbfiz           x0, x5, #1, #0x1f
    //     0xa4b688: cmp             x5, x0, asr #1
    //     0xa4b68c: b.eq            #0xa4b698
    //     0xa4b690: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b694: stur            x5, [x0, #7]
    // 0xa4b698: stp             x0, NULL, [SP]
    // 0xa4b69c: r0 = _Double.fromInteger()
    //     0xa4b69c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa4b6a0: stur            x0, [fp, #-0x28]
    // 0xa4b6a4: r0 = CheckoutEventData()
    //     0xa4b6a4: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa4b6a8: mov             x2, x0
    // 0xa4b6ac: ldur            x0, [fp, #-0x28]
    // 0xa4b6b0: stur            x2, [fp, #-0x30]
    // 0xa4b6b4: StoreField: r2->field_7 = r0
    //     0xa4b6b4: stur            w0, [x2, #7]
    // 0xa4b6b8: ldur            x3, [fp, #-0x20]
    // 0xa4b6bc: r0 = BoxInt64Instr(r3)
    //     0xa4b6bc: sbfiz           x0, x3, #1, #0x1f
    //     0xa4b6c0: cmp             x3, x0, asr #1
    //     0xa4b6c4: b.eq            #0xa4b6d0
    //     0xa4b6c8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b6cc: stur            x3, [x0, #7]
    // 0xa4b6d0: StoreField: r2->field_b = r0
    //     0xa4b6d0: stur            w0, [x2, #0xb]
    // 0xa4b6d4: ldur            x0, [fp, #-0x10]
    // 0xa4b6d8: StoreField: r2->field_f = r0
    //     0xa4b6d8: stur            w0, [x2, #0xf]
    // 0xa4b6dc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4b6dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4b6e0: ldr             x0, [x0, #0x1c80]
    //     0xa4b6e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4b6e8: cmp             w0, w16
    //     0xa4b6ec: b.ne            #0xa4b6f8
    //     0xa4b6f0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4b6f4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4b6f8: r1 = Null
    //     0xa4b6f8: mov             x1, NULL
    // 0xa4b6fc: r2 = 32
    //     0xa4b6fc: movz            x2, #0x20
    // 0xa4b700: r0 = AllocateArray()
    //     0xa4b700: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4b704: mov             x2, x0
    // 0xa4b708: r16 = "couponCode"
    //     0xa4b708: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa4b70c: ldr             x16, [x16, #0x310]
    // 0xa4b710: StoreField: r2->field_f = r16
    //     0xa4b710: stur            w16, [x2, #0xf]
    // 0xa4b714: r16 = ""
    //     0xa4b714: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4b718: StoreField: r2->field_13 = r16
    //     0xa4b718: stur            w16, [x2, #0x13]
    // 0xa4b71c: r16 = "product_id"
    //     0xa4b71c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4b720: ldr             x16, [x16, #0x9b8]
    // 0xa4b724: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4b724: stur            w16, [x2, #0x17]
    // 0xa4b728: ldur            x0, [fp, #-0x18]
    // 0xa4b72c: LoadField: r1 = r0->field_f
    //     0xa4b72c: ldur            w1, [x0, #0xf]
    // 0xa4b730: DecompressPointer r1
    //     0xa4b730: add             x1, x1, HEAP, lsl #32
    // 0xa4b734: LoadField: r3 = r1->field_b
    //     0xa4b734: ldur            w3, [x1, #0xb]
    // 0xa4b738: DecompressPointer r3
    //     0xa4b738: add             x3, x3, HEAP, lsl #32
    // 0xa4b73c: cmp             w3, NULL
    // 0xa4b740: b.eq            #0xa4b900
    // 0xa4b744: LoadField: r0 = r3->field_f
    //     0xa4b744: ldur            w0, [x3, #0xf]
    // 0xa4b748: DecompressPointer r0
    //     0xa4b748: add             x0, x0, HEAP, lsl #32
    // 0xa4b74c: StoreField: r2->field_1b = r0
    //     0xa4b74c: stur            w0, [x2, #0x1b]
    // 0xa4b750: r16 = "sku_id"
    //     0xa4b750: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa4b754: ldr             x16, [x16, #0x498]
    // 0xa4b758: StoreField: r2->field_1f = r16
    //     0xa4b758: stur            w16, [x2, #0x1f]
    // 0xa4b75c: LoadField: r0 = r3->field_13
    //     0xa4b75c: ldur            w0, [x3, #0x13]
    // 0xa4b760: DecompressPointer r0
    //     0xa4b760: add             x0, x0, HEAP, lsl #32
    // 0xa4b764: StoreField: r2->field_23 = r0
    //     0xa4b764: stur            w0, [x2, #0x23]
    // 0xa4b768: r16 = "quantity"
    //     0xa4b768: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa4b76c: ldr             x16, [x16, #0x428]
    // 0xa4b770: StoreField: r2->field_27 = r16
    //     0xa4b770: stur            w16, [x2, #0x27]
    // 0xa4b774: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa4b774: ldur            x4, [x3, #0x17]
    // 0xa4b778: r0 = BoxInt64Instr(r4)
    //     0xa4b778: sbfiz           x0, x4, #1, #0x1f
    //     0xa4b77c: cmp             x4, x0, asr #1
    //     0xa4b780: b.eq            #0xa4b78c
    //     0xa4b784: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b788: stur            x4, [x0, #7]
    // 0xa4b78c: mov             x1, x2
    // 0xa4b790: ArrayStore: r1[7] = r0  ; List_4
    //     0xa4b790: add             x25, x1, #0x2b
    //     0xa4b794: str             w0, [x25]
    //     0xa4b798: tbz             w0, #0, #0xa4b7b4
    //     0xa4b79c: ldurb           w16, [x1, #-1]
    //     0xa4b7a0: ldurb           w17, [x0, #-1]
    //     0xa4b7a4: and             x16, x17, x16, lsr #2
    //     0xa4b7a8: tst             x16, HEAP, lsr #32
    //     0xa4b7ac: b.eq            #0xa4b7b4
    //     0xa4b7b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b7b4: r16 = "checkout_event_data"
    //     0xa4b7b4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4b7b8: ldr             x16, [x16, #0xd50]
    // 0xa4b7bc: StoreField: r2->field_2f = r16
    //     0xa4b7bc: stur            w16, [x2, #0x2f]
    // 0xa4b7c0: mov             x1, x2
    // 0xa4b7c4: ldur            x0, [fp, #-0x30]
    // 0xa4b7c8: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4b7c8: add             x25, x1, #0x33
    //     0xa4b7cc: str             w0, [x25]
    //     0xa4b7d0: tbz             w0, #0, #0xa4b7ec
    //     0xa4b7d4: ldurb           w16, [x1, #-1]
    //     0xa4b7d8: ldurb           w17, [x0, #-1]
    //     0xa4b7dc: and             x16, x17, x16, lsr #2
    //     0xa4b7e0: tst             x16, HEAP, lsr #32
    //     0xa4b7e4: b.eq            #0xa4b7ec
    //     0xa4b7e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b7ec: r16 = "previousScreenSource"
    //     0xa4b7ec: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4b7f0: ldr             x16, [x16, #0x448]
    // 0xa4b7f4: StoreField: r2->field_37 = r16
    //     0xa4b7f4: stur            w16, [x2, #0x37]
    // 0xa4b7f8: r16 = "product_page"
    //     0xa4b7f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4b7fc: ldr             x16, [x16, #0x480]
    // 0xa4b800: StoreField: r2->field_3b = r16
    //     0xa4b800: stur            w16, [x2, #0x3b]
    // 0xa4b804: r16 = "coming_from"
    //     0xa4b804: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa4b808: ldr             x16, [x16, #0x328]
    // 0xa4b80c: StoreField: r2->field_3f = r16
    //     0xa4b80c: stur            w16, [x2, #0x3f]
    // 0xa4b810: LoadField: r0 = r3->field_2f
    //     0xa4b810: ldur            w0, [x3, #0x2f]
    // 0xa4b814: DecompressPointer r0
    //     0xa4b814: add             x0, x0, HEAP, lsl #32
    // 0xa4b818: mov             x1, x2
    // 0xa4b81c: ArrayStore: r1[13] = r0  ; List_4
    //     0xa4b81c: add             x25, x1, #0x43
    //     0xa4b820: str             w0, [x25]
    //     0xa4b824: tbz             w0, #0, #0xa4b840
    //     0xa4b828: ldurb           w16, [x1, #-1]
    //     0xa4b82c: ldurb           w17, [x0, #-1]
    //     0xa4b830: and             x16, x17, x16, lsr #2
    //     0xa4b834: tst             x16, HEAP, lsr #32
    //     0xa4b838: b.eq            #0xa4b840
    //     0xa4b83c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4b840: r16 = "is_skipped_address"
    //     0xa4b840: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4b844: ldr             x16, [x16, #0xb80]
    // 0xa4b848: StoreField: r2->field_47 = r16
    //     0xa4b848: stur            w16, [x2, #0x47]
    // 0xa4b84c: r16 = true
    //     0xa4b84c: add             x16, NULL, #0x20  ; true
    // 0xa4b850: StoreField: r2->field_4b = r16
    //     0xa4b850: stur            w16, [x2, #0x4b]
    // 0xa4b854: r16 = <String, Object?>
    //     0xa4b854: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa4b858: ldr             x16, [x16, #0xc28]
    // 0xa4b85c: stp             x2, x16, [SP]
    // 0xa4b860: r0 = Map._fromLiteral()
    //     0xa4b860: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4b864: r16 = "/checkout_request_number_page"
    //     0xa4b864: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xa4b868: ldr             x16, [x16, #0x9f8]
    // 0xa4b86c: stp             x16, NULL, [SP, #8]
    // 0xa4b870: str             x0, [SP]
    // 0xa4b874: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4b874: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa4b878: ldr             x4, [x4, #0x438]
    // 0xa4b87c: r0 = GetNavigation.toNamed()
    //     0xa4b87c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4b880: r0 = Null
    //     0xa4b880: mov             x0, NULL
    // 0xa4b884: r0 = ReturnAsyncNotFuture()
    //     0xa4b884: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xa4b888: r16 = "controller"
    //     0xa4b888: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4b88c: str             x16, [SP]
    // 0xa4b890: r0 = _throwLocalNotInitialized()
    //     0xa4b890: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4b894: brk             #0
    // 0xa4b898: r16 = "controller"
    //     0xa4b898: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4b89c: str             x16, [SP]
    // 0xa4b8a0: r0 = _throwLocalNotInitialized()
    //     0xa4b8a0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4b8a4: brk             #0
    // 0xa4b8a8: r16 = "controller"
    //     0xa4b8a8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4b8ac: str             x16, [SP]
    // 0xa4b8b0: r0 = _throwLocalNotInitialized()
    //     0xa4b8b0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4b8b4: brk             #0
    // 0xa4b8b8: r16 = "controller"
    //     0xa4b8b8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4b8bc: str             x16, [SP]
    // 0xa4b8c0: r0 = _throwLocalNotInitialized()
    //     0xa4b8c0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4b8c4: brk             #0
    // 0xa4b8c8: r16 = "controller"
    //     0xa4b8c8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4b8cc: str             x16, [SP]
    // 0xa4b8d0: r0 = _throwLocalNotInitialized()
    //     0xa4b8d0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4b8d4: brk             #0
    // 0xa4b8d8: r16 = "controller"
    //     0xa4b8d8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4b8dc: str             x16, [SP]
    // 0xa4b8e0: r0 = _throwLocalNotInitialized()
    //     0xa4b8e0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4b8e4: brk             #0
    // 0xa4b8e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4b8e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4b8ec: b               #0xa4af4c
    // 0xa4b8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b900: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b900: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4245, size: 0x50, field offset: 0xc
//   const constructor, 
class CustomizedBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7bb18, size: 0x24
    // 0xc7bb18: EnterFrame
    //     0xc7bb18: stp             fp, lr, [SP, #-0x10]!
    //     0xc7bb1c: mov             fp, SP
    // 0xc7bb20: mov             x0, x1
    // 0xc7bb24: r1 = <CustomizedBottomSheet>
    //     0xc7bb24: add             x1, PP, #0x49, lsl #12  ; [pp+0x49098] TypeArguments: <CustomizedBottomSheet>
    //     0xc7bb28: ldr             x1, [x1, #0x98]
    // 0xc7bb2c: r0 = _CustomizedBottomSheetState()
    //     0xc7bb2c: bl              #0xc7bb3c  ; Allocate_CustomizedBottomSheetStateStub -> _CustomizedBottomSheetState (size=0x14)
    // 0xc7bb30: LeaveFrame
    //     0xc7bb30: mov             SP, fp
    //     0xc7bb34: ldp             fp, lr, [SP], #0x10
    // 0xc7bb38: ret
    //     0xc7bb38: ret             
  }
}
