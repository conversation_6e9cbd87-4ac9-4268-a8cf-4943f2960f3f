// lib: , url: package:easebuzz_flutter/easebuzz_flutter_platform_interface.dart

// class id: 1049623, size: 0x8
class :: {
}

// class id: 5912, size: 0x8, field offset: 0x8
abstract class EasebuzzFlutterPlatform extends PlatformInterface {

  static late EasebuzzFlutterPlatform _instance; // offset: 0xed4
  static late final Object _token; // offset: 0xed0

  static EasebuzzFlutterPlatform _instance() {
    // ** addr: 0x12d87ac, size: 0x90
    // 0x12d87ac: EnterFrame
    //     0x12d87ac: stp             fp, lr, [SP, #-0x10]!
    //     0x12d87b0: mov             fp, SP
    // 0x12d87b4: AllocStack(0x10)
    //     0x12d87b4: sub             SP, SP, #0x10
    // 0x12d87b8: CheckStackOverflow
    //     0x12d87b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12d87bc: cmp             SP, x16
    //     0x12d87c0: b.ls            #0x12d8834
    // 0x12d87c4: r0 = InitLateStaticField(0xed0) // [package:easebuzz_flutter/easebuzz_flutter_platform_interface.dart] EasebuzzFlutterPlatform::_token
    //     0x12d87c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12d87c8: ldr             x0, [x0, #0x1da0]
    //     0x12d87cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12d87d0: cmp             w0, w16
    //     0x12d87d4: b.ne            #0x12d87e4
    //     0x12d87d8: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a760] Field <EasebuzzFlutterPlatform._token@1789398441>: static late final (offset: 0xed0)
    //     0x12d87dc: ldr             x2, [x2, #0x760]
    //     0x12d87e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12d87e4: stur            x0, [fp, #-8]
    // 0x12d87e8: r0 = InitLateStaticField(0x5fc) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x12d87e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12d87ec: ldr             x0, [x0, #0xbf8]
    //     0x12d87f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12d87f4: cmp             w0, w16
    //     0x12d87f8: b.ne            #0x12d8808
    //     0x12d87fc: add             x2, PP, #0xa, lsl #12  ; [pp+0xaa30] Field <PlatformInterface._instanceTokens@92304592>: static late final (offset: 0x5fc)
    //     0x12d8800: ldr             x2, [x2, #0xa30]
    //     0x12d8804: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12d8808: stur            x0, [fp, #-0x10]
    // 0x12d880c: r0 = MethodChannelEasebuzzFlutter()
    //     0x12d880c: bl              #0x12d883c  ; AllocateMethodChannelEasebuzzFlutterStub -> MethodChannelEasebuzzFlutter (size=0x8)
    // 0x12d8810: ldur            x1, [fp, #-0x10]
    // 0x12d8814: mov             x2, x0
    // 0x12d8818: ldur            x3, [fp, #-8]
    // 0x12d881c: stur            x0, [fp, #-8]
    // 0x12d8820: r0 = []=()
    //     0x12d8820: bl              #0x632b78  ; [dart:core] Expando::[]=
    // 0x12d8824: ldur            x0, [fp, #-8]
    // 0x12d8828: LeaveFrame
    //     0x12d8828: mov             SP, fp
    //     0x12d882c: ldp             fp, lr, [SP], #0x10
    // 0x12d8830: ret
    //     0x12d8830: ret             
    // 0x12d8834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12d8834: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12d8838: b               #0x12d87c4
  }
}
