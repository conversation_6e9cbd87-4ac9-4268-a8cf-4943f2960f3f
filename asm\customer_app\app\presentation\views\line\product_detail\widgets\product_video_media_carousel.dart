// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_video_media_carousel.dart

// class id: 1049564, size: 0x8
class :: {
}

// class id: 3219, size: 0x1c, field offset: 0x14
class _ProductVideoMediaCarouselState extends State<dynamic> {

  late VideoPlayerController _videoPlayerController; // offset: 0x14

  [closure] Center <anonymous closure>(dynamic, BuildContext, String) {
    // ** addr: 0x934ed0, size: 0xb4
    // 0x934ed0: EnterFrame
    //     0x934ed0: stp             fp, lr, [SP, #-0x10]!
    //     0x934ed4: mov             fp, SP
    // 0x934ed8: AllocStack(0x18)
    //     0x934ed8: sub             SP, SP, #0x18
    // 0x934edc: CheckStackOverflow
    //     0x934edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934ee0: cmp             SP, x16
    //     0x934ee4: b.ls            #0x934f7c
    // 0x934ee8: ldr             x1, [fp, #0x18]
    // 0x934eec: r0 = of()
    //     0x934eec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x934ef0: LoadField: r1 = r0->field_87
    //     0x934ef0: ldur            w1, [x0, #0x87]
    // 0x934ef4: DecompressPointer r1
    //     0x934ef4: add             x1, x1, HEAP, lsl #32
    // 0x934ef8: LoadField: r0 = r1->field_27
    //     0x934ef8: ldur            w0, [x1, #0x27]
    // 0x934efc: DecompressPointer r0
    //     0x934efc: add             x0, x0, HEAP, lsl #32
    // 0x934f00: r16 = Instance_Color
    //     0x934f00: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x934f04: str             x16, [SP]
    // 0x934f08: mov             x1, x0
    // 0x934f0c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x934f0c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x934f10: ldr             x4, [x4, #0xf40]
    // 0x934f14: r0 = copyWith()
    //     0x934f14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x934f18: stur            x0, [fp, #-8]
    // 0x934f1c: r0 = Text()
    //     0x934f1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x934f20: mov             x1, x0
    // 0x934f24: ldr             x0, [fp, #0x10]
    // 0x934f28: stur            x1, [fp, #-0x10]
    // 0x934f2c: StoreField: r1->field_b = r0
    //     0x934f2c: stur            w0, [x1, #0xb]
    // 0x934f30: ldur            x0, [fp, #-8]
    // 0x934f34: StoreField: r1->field_13 = r0
    //     0x934f34: stur            w0, [x1, #0x13]
    // 0x934f38: r0 = Padding()
    //     0x934f38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x934f3c: mov             x1, x0
    // 0x934f40: r0 = Instance_EdgeInsets
    //     0x934f40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x934f44: ldr             x0, [x0, #0x980]
    // 0x934f48: stur            x1, [fp, #-8]
    // 0x934f4c: StoreField: r1->field_f = r0
    //     0x934f4c: stur            w0, [x1, #0xf]
    // 0x934f50: ldur            x0, [fp, #-0x10]
    // 0x934f54: StoreField: r1->field_b = r0
    //     0x934f54: stur            w0, [x1, #0xb]
    // 0x934f58: r0 = Center()
    //     0x934f58: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x934f5c: r1 = Instance_Alignment
    //     0x934f5c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x934f60: ldr             x1, [x1, #0xb10]
    // 0x934f64: StoreField: r0->field_f = r1
    //     0x934f64: stur            w1, [x0, #0xf]
    // 0x934f68: ldur            x1, [fp, #-8]
    // 0x934f6c: StoreField: r0->field_b = r1
    //     0x934f6c: stur            w1, [x0, #0xb]
    // 0x934f70: LeaveFrame
    //     0x934f70: mov             SP, fp
    //     0x934f74: ldp             fp, lr, [SP], #0x10
    // 0x934f78: ret
    //     0x934f78: ret             
    // 0x934f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934f7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934f80: b               #0x934ee8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x934f84, size: 0x158
    // 0x934f84: EnterFrame
    //     0x934f84: stp             fp, lr, [SP, #-0x10]!
    //     0x934f88: mov             fp, SP
    // 0x934f8c: AllocStack(0x58)
    //     0x934f8c: sub             SP, SP, #0x58
    // 0x934f90: SetupParameters()
    //     0x934f90: ldr             x0, [fp, #0x10]
    //     0x934f94: ldur            w2, [x0, #0x17]
    //     0x934f98: add             x2, x2, HEAP, lsl #32
    //     0x934f9c: stur            x2, [fp, #-0x18]
    // 0x934fa0: CheckStackOverflow
    //     0x934fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934fa4: cmp             SP, x16
    //     0x934fa8: b.ls            #0x9350b8
    // 0x934fac: LoadField: r0 = r2->field_f
    //     0x934fac: ldur            w0, [x2, #0xf]
    // 0x934fb0: DecompressPointer r0
    //     0x934fb0: add             x0, x0, HEAP, lsl #32
    // 0x934fb4: stur            x0, [fp, #-0x10]
    // 0x934fb8: LoadField: r5 = r0->field_13
    //     0x934fb8: ldur            w5, [x0, #0x13]
    // 0x934fbc: DecompressPointer r5
    //     0x934fbc: add             x5, x5, HEAP, lsl #32
    // 0x934fc0: r16 = Sentinel
    //     0x934fc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x934fc4: cmp             w5, w16
    // 0x934fc8: b.eq            #0x9350c0
    // 0x934fcc: stur            x5, [fp, #-8]
    // 0x934fd0: LoadField: r1 = r5->field_27
    //     0x934fd0: ldur            w1, [x5, #0x27]
    // 0x934fd4: DecompressPointer r1
    //     0x934fd4: add             x1, x1, HEAP, lsl #32
    // 0x934fd8: r0 = aspectRatio()
    //     0x934fd8: bl              #0x8faac4  ; [package:video_player/video_player.dart] VideoPlayerValue::aspectRatio
    // 0x934fdc: r0 = inline_Allocate_Double()
    //     0x934fdc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x934fe0: add             x0, x0, #0x10
    //     0x934fe4: cmp             x1, x0
    //     0x934fe8: b.ls            #0x9350cc
    //     0x934fec: str             x0, [THR, #0x50]  ; THR::top
    //     0x934ff0: sub             x0, x0, #0xf
    //     0x934ff4: movz            x1, #0xe15c
    //     0x934ff8: movk            x1, #0x3, lsl #16
    //     0x934ffc: stur            x1, [x0, #-1]
    // 0x935000: StoreField: r0->field_7 = d0
    //     0x935000: stur            d0, [x0, #7]
    // 0x935004: stur            x0, [fp, #-0x20]
    // 0x935008: r1 = Function '<anonymous closure>':.
    //     0x935008: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a1b8] AnonymousClosure: (0x934ed0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer (0x9350dc)
    //     0x93500c: ldr             x1, [x1, #0x1b8]
    // 0x935010: r2 = Null
    //     0x935010: mov             x2, NULL
    // 0x935014: r0 = AllocateClosure()
    //     0x935014: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x935018: stur            x0, [fp, #-0x28]
    // 0x93501c: r0 = ChewieController()
    //     0x93501c: bl              #0x8faaa0  ; AllocateChewieControllerStub -> ChewieController (size=0xdc)
    // 0x935020: stur            x0, [fp, #-0x30]
    // 0x935024: ldur            x16, [fp, #-0x20]
    // 0x935028: r30 = true
    //     0x935028: add             lr, NULL, #0x20  ; true
    // 0x93502c: stp             lr, x16, [SP, #0x18]
    // 0x935030: r16 = true
    //     0x935030: add             x16, NULL, #0x20  ; true
    // 0x935034: r30 = false
    //     0x935034: add             lr, NULL, #0x30  ; false
    // 0x935038: stp             lr, x16, [SP, #8]
    // 0x93503c: ldur            x16, [fp, #-0x28]
    // 0x935040: str             x16, [SP]
    // 0x935044: mov             x1, x0
    // 0x935048: ldur            x5, [fp, #-8]
    // 0x93504c: r2 = true
    //     0x93504c: add             x2, NULL, #0x20  ; true
    // 0x935050: r3 = true
    //     0x935050: add             x3, NULL, #0x20  ; true
    // 0x935054: r4 = const [0, 0x9, 0x5, 0x4, allowFullScreen, 0x7, aspectRatio, 0x4, autoInitialize, 0x5, errorBuilder, 0x8, showOptions, 0x6, null]
    //     0x935054: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a1c0] List(15) [0, 0x9, 0x5, 0x4, "allowFullScreen", 0x7, "aspectRatio", 0x4, "autoInitialize", 0x5, "errorBuilder", 0x8, "showOptions", 0x6, Null]
    //     0x935058: ldr             x4, [x4, #0x1c0]
    // 0x93505c: r0 = ChewieController()
    //     0x93505c: bl              #0x8fa550  ; [package:chewie/src/chewie_player.dart] ChewieController::ChewieController
    // 0x935060: ldur            x0, [fp, #-0x30]
    // 0x935064: ldur            x1, [fp, #-0x10]
    // 0x935068: ArrayStore: r1[0] = r0  ; List_4
    //     0x935068: stur            w0, [x1, #0x17]
    //     0x93506c: ldurb           w16, [x1, #-1]
    //     0x935070: ldurb           w17, [x0, #-1]
    //     0x935074: and             x16, x17, x16, lsr #2
    //     0x935078: tst             x16, HEAP, lsr #32
    //     0x93507c: b.eq            #0x935084
    //     0x935080: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x935084: ldur            x0, [fp, #-0x18]
    // 0x935088: LoadField: r1 = r0->field_f
    //     0x935088: ldur            w1, [x0, #0xf]
    // 0x93508c: DecompressPointer r1
    //     0x93508c: add             x1, x1, HEAP, lsl #32
    // 0x935090: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x935090: ldur            w0, [x1, #0x17]
    // 0x935094: DecompressPointer r0
    //     0x935094: add             x0, x0, HEAP, lsl #32
    // 0x935098: cmp             w0, NULL
    // 0x93509c: b.eq            #0x9350a8
    // 0x9350a0: mov             x1, x0
    // 0x9350a4: r0 = setVolume()
    //     0x9350a4: bl              #0x8fa424  ; [package:chewie/src/chewie_player.dart] ChewieController::setVolume
    // 0x9350a8: r0 = Null
    //     0x9350a8: mov             x0, NULL
    // 0x9350ac: LeaveFrame
    //     0x9350ac: mov             SP, fp
    //     0x9350b0: ldp             fp, lr, [SP], #0x10
    // 0x9350b4: ret
    //     0x9350b4: ret             
    // 0x9350b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9350b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9350bc: b               #0x934fac
    // 0x9350c0: r9 = _videoPlayerController
    //     0x9350c0: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a1a0] Field <_ProductVideoMediaCarouselState@1747127890._videoPlayerController@1747127890>: late (offset: 0x14)
    //     0x9350c4: ldr             x9, [x9, #0x1a0]
    // 0x9350c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9350c8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9350cc: SaveReg d0
    //     0x9350cc: str             q0, [SP, #-0x10]!
    // 0x9350d0: r0 = AllocateDouble()
    //     0x9350d0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9350d4: RestoreReg d0
    //     0x9350d4: ldr             q0, [SP], #0x10
    // 0x9350d8: b               #0x935000
  }
  _ initVideoPlayer(/* No info */) async {
    // ** addr: 0x9350dc, size: 0xec
    // 0x9350dc: EnterFrame
    //     0x9350dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9350e0: mov             fp, SP
    // 0x9350e4: AllocStack(0x78)
    //     0x9350e4: sub             SP, SP, #0x78
    // 0x9350e8: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r1, fp-0x68 */)
    //     0x9350e8: stur            NULL, [fp, #-8]
    //     0x9350ec: stur            x1, [fp, #-0x68]
    // 0x9350f0: CheckStackOverflow
    //     0x9350f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9350f4: cmp             SP, x16
    //     0x9350f8: b.ls            #0x9351b4
    // 0x9350fc: r1 = 1
    //     0x9350fc: movz            x1, #0x1
    // 0x935100: r0 = AllocateContext()
    //     0x935100: bl              #0x16f6108  ; AllocateContextStub
    // 0x935104: mov             x2, x0
    // 0x935108: ldur            x1, [fp, #-0x68]
    // 0x93510c: stur            x2, [fp, #-0x70]
    // 0x935110: StoreField: r2->field_f = r1
    //     0x935110: stur            w1, [x2, #0xf]
    // 0x935114: InitAsync() -> Future<void?>
    //     0x935114: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x935118: bl              #0x6326e0  ; InitAsyncStub
    // 0x93511c: ldur            x0, [fp, #-0x68]
    // 0x935120: LoadField: r1 = r0->field_13
    //     0x935120: ldur            w1, [x0, #0x13]
    // 0x935124: DecompressPointer r1
    //     0x935124: add             x1, x1, HEAP, lsl #32
    // 0x935128: r16 = Sentinel
    //     0x935128: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93512c: cmp             w1, w16
    // 0x935130: b.eq            #0x9351bc
    // 0x935134: r0 = initialize()
    //     0x935134: bl              #0x8f78ec  ; [package:video_player/video_player.dart] VideoPlayerController::initialize
    // 0x935138: mov             x1, x0
    // 0x93513c: stur            x1, [fp, #-0x78]
    // 0x935140: r0 = Await()
    //     0x935140: bl              #0x63248c  ; AwaitStub
    // 0x935144: ldur            x0, [fp, #-0x68]
    // 0x935148: LoadField: r1 = r0->field_f
    //     0x935148: ldur            w1, [x0, #0xf]
    // 0x93514c: DecompressPointer r1
    //     0x93514c: add             x1, x1, HEAP, lsl #32
    // 0x935150: cmp             w1, NULL
    // 0x935154: b.eq            #0x9351ac
    // 0x935158: ldur            x2, [fp, #-0x70]
    // 0x93515c: r1 = Function '<anonymous closure>':.
    //     0x93515c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a1a8] AnonymousClosure: (0x934f84), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer (0x9350dc)
    //     0x935160: ldr             x1, [x1, #0x1a8]
    // 0x935164: r0 = AllocateClosure()
    //     0x935164: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x935168: ldur            x1, [fp, #-0x68]
    // 0x93516c: mov             x2, x0
    // 0x935170: r0 = setState()
    //     0x935170: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x935174: b               #0x9351ac
    // 0x935178: sub             SP, fp, #0x78
    // 0x93517c: ldur            x3, [fp, #-0x68]
    // 0x935180: LoadField: r0 = r3->field_f
    //     0x935180: ldur            w0, [x3, #0xf]
    // 0x935184: DecompressPointer r0
    //     0x935184: add             x0, x0, HEAP, lsl #32
    // 0x935188: cmp             w0, NULL
    // 0x93518c: b.eq            #0x9351ac
    // 0x935190: r1 = Function '<anonymous closure>':.
    //     0x935190: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a1b0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x935194: ldr             x1, [x1, #0x1b0]
    // 0x935198: r2 = Null
    //     0x935198: mov             x2, NULL
    // 0x93519c: r0 = AllocateClosure()
    //     0x93519c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9351a0: ldur            x1, [fp, #-0x68]
    // 0x9351a4: mov             x2, x0
    // 0x9351a8: r0 = setState()
    //     0x9351a8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9351ac: r0 = Null
    //     0x9351ac: mov             x0, NULL
    // 0x9351b0: r0 = ReturnAsyncNotFuture()
    //     0x9351b0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x9351b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9351b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9351b8: b               #0x9350fc
    // 0x9351bc: r9 = _videoPlayerController
    //     0x9351bc: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a1a0] Field <_ProductVideoMediaCarouselState@1747127890._videoPlayerController@1747127890>: late (offset: 0x14)
    //     0x9351c0: ldr             x9, [x9, #0x1a0]
    // 0x9351c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9351c4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x94b0f4, size: 0xd4
    // 0x94b0f4: EnterFrame
    //     0x94b0f4: stp             fp, lr, [SP, #-0x10]!
    //     0x94b0f8: mov             fp, SP
    // 0x94b0fc: AllocStack(0x10)
    //     0x94b0fc: sub             SP, SP, #0x10
    // 0x94b100: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0x94b100: mov             x0, x1
    //     0x94b104: stur            x1, [fp, #-8]
    // 0x94b108: CheckStackOverflow
    //     0x94b108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b10c: cmp             SP, x16
    //     0x94b110: b.ls            #0x94b1bc
    // 0x94b114: LoadField: r1 = r0->field_b
    //     0x94b114: ldur            w1, [x0, #0xb]
    // 0x94b118: DecompressPointer r1
    //     0x94b118: add             x1, x1, HEAP, lsl #32
    // 0x94b11c: cmp             w1, NULL
    // 0x94b120: b.eq            #0x94b1c4
    // 0x94b124: LoadField: r2 = r1->field_b
    //     0x94b124: ldur            w2, [x1, #0xb]
    // 0x94b128: DecompressPointer r2
    //     0x94b128: add             x2, x2, HEAP, lsl #32
    // 0x94b12c: LoadField: r1 = r2->field_2f
    //     0x94b12c: ldur            w1, [x2, #0x2f]
    // 0x94b130: DecompressPointer r1
    //     0x94b130: add             x1, x1, HEAP, lsl #32
    // 0x94b134: cmp             w1, NULL
    // 0x94b138: b.ne            #0x94b144
    // 0x94b13c: r1 = Null
    //     0x94b13c: mov             x1, NULL
    // 0x94b140: b               #0x94b150
    // 0x94b144: LoadField: r2 = r1->field_b
    //     0x94b144: ldur            w2, [x1, #0xb]
    // 0x94b148: DecompressPointer r2
    //     0x94b148: add             x2, x2, HEAP, lsl #32
    // 0x94b14c: mov             x1, x2
    // 0x94b150: cmp             w1, NULL
    // 0x94b154: b.ne            #0x94b15c
    // 0x94b158: r1 = ""
    //     0x94b158: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94b15c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94b15c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94b160: r0 = parse()
    //     0x94b160: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0x94b164: r1 = <VideoPlayerValue>
    //     0x94b164: add             x1, PP, #0x52, lsl #12  ; [pp+0x52f90] TypeArguments: <VideoPlayerValue>
    //     0x94b168: ldr             x1, [x1, #0xf90]
    // 0x94b16c: stur            x0, [fp, #-0x10]
    // 0x94b170: r0 = VideoPlayerController()
    //     0x94b170: bl              #0x8f9c94  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x68)
    // 0x94b174: mov             x1, x0
    // 0x94b178: ldur            x2, [fp, #-0x10]
    // 0x94b17c: stur            x0, [fp, #-0x10]
    // 0x94b180: r0 = VideoPlayerController.networkUrl()
    //     0x94b180: bl              #0x8f9ba4  ; [package:video_player/video_player.dart] VideoPlayerController::VideoPlayerController.networkUrl
    // 0x94b184: ldur            x0, [fp, #-0x10]
    // 0x94b188: ldur            x1, [fp, #-8]
    // 0x94b18c: StoreField: r1->field_13 = r0
    //     0x94b18c: stur            w0, [x1, #0x13]
    //     0x94b190: ldurb           w16, [x1, #-1]
    //     0x94b194: ldurb           w17, [x0, #-1]
    //     0x94b198: and             x16, x17, x16, lsr #2
    //     0x94b19c: tst             x16, HEAP, lsr #32
    //     0x94b1a0: b.eq            #0x94b1a8
    //     0x94b1a4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94b1a8: r0 = initVideoPlayer()
    //     0x94b1a8: bl              #0x9350dc  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer
    // 0x94b1ac: r0 = Null
    //     0x94b1ac: mov             x0, NULL
    // 0x94b1b0: LeaveFrame
    //     0x94b1b0: mov             SP, fp
    //     0x94b1b4: ldp             fp, lr, [SP], #0x10
    // 0x94b1b8: ret
    //     0x94b1b8: ret             
    // 0x94b1bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b1bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b1c0: b               #0x94b114
    // 0x94b1c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b1c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc08d58, size: 0x17c
    // 0xc08d58: EnterFrame
    //     0xc08d58: stp             fp, lr, [SP, #-0x10]!
    //     0xc08d5c: mov             fp, SP
    // 0xc08d60: AllocStack(0x20)
    //     0xc08d60: sub             SP, SP, #0x20
    // 0xc08d64: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc08d64: mov             x0, x2
    //     0xc08d68: stur            x2, [fp, #-0x10]
    //     0xc08d6c: mov             x2, x1
    //     0xc08d70: stur            x1, [fp, #-8]
    // 0xc08d74: CheckStackOverflow
    //     0xc08d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08d78: cmp             SP, x16
    //     0xc08d7c: b.ls            #0xc08ec0
    // 0xc08d80: LoadField: r1 = r2->field_13
    //     0xc08d80: ldur            w1, [x2, #0x13]
    // 0xc08d84: DecompressPointer r1
    //     0xc08d84: add             x1, x1, HEAP, lsl #32
    // 0xc08d88: r16 = Sentinel
    //     0xc08d88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc08d8c: cmp             w1, w16
    // 0xc08d90: b.eq            #0xc08ec8
    // 0xc08d94: LoadField: r3 = r1->field_27
    //     0xc08d94: ldur            w3, [x1, #0x27]
    // 0xc08d98: DecompressPointer r3
    //     0xc08d98: add             x3, x3, HEAP, lsl #32
    // 0xc08d9c: LoadField: r1 = r3->field_4b
    //     0xc08d9c: ldur            w1, [x3, #0x4b]
    // 0xc08da0: DecompressPointer r1
    //     0xc08da0: add             x1, x1, HEAP, lsl #32
    // 0xc08da4: tbnz            w1, #4, #0xc08db4
    // 0xc08da8: mov             x1, x3
    // 0xc08dac: r0 = aspectRatio()
    //     0xc08dac: bl              #0x8faac4  ; [package:video_player/video_player.dart] VideoPlayerValue::aspectRatio
    // 0xc08db0: b               #0xc08dbc
    // 0xc08db4: d0 = 1.777778
    //     0xc08db4: add             x17, PP, #0x4e, lsl #12  ; [pp+0x4e8f0] IMM: double(1.7777777777777777) from 0x3ffc71c71c71c71c
    //     0xc08db8: ldr             d0, [x17, #0x8f0]
    // 0xc08dbc: ldur            x0, [fp, #-8]
    // 0xc08dc0: stur            d0, [fp, #-0x20]
    // 0xc08dc4: LoadField: r1 = r0->field_13
    //     0xc08dc4: ldur            w1, [x0, #0x13]
    // 0xc08dc8: DecompressPointer r1
    //     0xc08dc8: add             x1, x1, HEAP, lsl #32
    // 0xc08dcc: LoadField: r2 = r1->field_27
    //     0xc08dcc: ldur            w2, [x1, #0x27]
    // 0xc08dd0: DecompressPointer r2
    //     0xc08dd0: add             x2, x2, HEAP, lsl #32
    // 0xc08dd4: LoadField: r1 = r2->field_4b
    //     0xc08dd4: ldur            w1, [x2, #0x4b]
    // 0xc08dd8: DecompressPointer r1
    //     0xc08dd8: add             x1, x1, HEAP, lsl #32
    // 0xc08ddc: tbnz            w1, #4, #0xc08e0c
    // 0xc08de0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc08de0: ldur            w1, [x0, #0x17]
    // 0xc08de4: DecompressPointer r1
    //     0xc08de4: add             x1, x1, HEAP, lsl #32
    // 0xc08de8: stur            x1, [fp, #-0x18]
    // 0xc08dec: cmp             w1, NULL
    // 0xc08df0: b.eq            #0xc08e0c
    // 0xc08df4: r0 = Chewie()
    //     0xc08df4: bl              #0x9d1c78  ; AllocateChewieStub -> Chewie (size=0x10)
    // 0xc08df8: mov             x1, x0
    // 0xc08dfc: ldur            x0, [fp, #-0x18]
    // 0xc08e00: StoreField: r1->field_b = r0
    //     0xc08e00: stur            w0, [x1, #0xb]
    // 0xc08e04: mov             x0, x1
    // 0xc08e08: b               #0xc08e5c
    // 0xc08e0c: ldur            x1, [fp, #-0x10]
    // 0xc08e10: r0 = of()
    //     0xc08e10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc08e14: LoadField: r1 = r0->field_5b
    //     0xc08e14: ldur            w1, [x0, #0x5b]
    // 0xc08e18: DecompressPointer r1
    //     0xc08e18: add             x1, x1, HEAP, lsl #32
    // 0xc08e1c: r0 = LoadClassIdInstr(r1)
    //     0xc08e1c: ldur            x0, [x1, #-1]
    //     0xc08e20: ubfx            x0, x0, #0xc, #0x14
    // 0xc08e24: d0 = 0.300000
    //     0xc08e24: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xc08e28: ldr             d0, [x17, #0x658]
    // 0xc08e2c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc08e2c: sub             lr, x0, #0xffa
    //     0xc08e30: ldr             lr, [x21, lr, lsl #3]
    //     0xc08e34: blr             lr
    // 0xc08e38: stur            x0, [fp, #-8]
    // 0xc08e3c: r0 = CircularProgressIndicator()
    //     0xc08e3c: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xc08e40: mov             x1, x0
    // 0xc08e44: r0 = Instance__ActivityIndicatorType
    //     0xc08e44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xc08e48: ldr             x0, [x0, #0x1b0]
    // 0xc08e4c: StoreField: r1->field_23 = r0
    //     0xc08e4c: stur            w0, [x1, #0x23]
    // 0xc08e50: ldur            x0, [fp, #-8]
    // 0xc08e54: StoreField: r1->field_13 = r0
    //     0xc08e54: stur            w0, [x1, #0x13]
    // 0xc08e58: mov             x0, x1
    // 0xc08e5c: ldur            d0, [fp, #-0x20]
    // 0xc08e60: stur            x0, [fp, #-8]
    // 0xc08e64: r0 = Center()
    //     0xc08e64: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc08e68: mov             x1, x0
    // 0xc08e6c: r0 = Instance_Alignment
    //     0xc08e6c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc08e70: ldr             x0, [x0, #0xb10]
    // 0xc08e74: stur            x1, [fp, #-0x10]
    // 0xc08e78: StoreField: r1->field_f = r0
    //     0xc08e78: stur            w0, [x1, #0xf]
    // 0xc08e7c: ldur            x0, [fp, #-8]
    // 0xc08e80: StoreField: r1->field_b = r0
    //     0xc08e80: stur            w0, [x1, #0xb]
    // 0xc08e84: r0 = AspectRatio()
    //     0xc08e84: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xc08e88: ldur            d0, [fp, #-0x20]
    // 0xc08e8c: stur            x0, [fp, #-8]
    // 0xc08e90: StoreField: r0->field_f = d0
    //     0xc08e90: stur            d0, [x0, #0xf]
    // 0xc08e94: ldur            x1, [fp, #-0x10]
    // 0xc08e98: StoreField: r0->field_b = r1
    //     0xc08e98: stur            w1, [x0, #0xb]
    // 0xc08e9c: r0 = Padding()
    //     0xc08e9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc08ea0: r1 = Instance_EdgeInsets
    //     0xc08ea0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc08ea4: ldr             x1, [x1, #0x980]
    // 0xc08ea8: StoreField: r0->field_f = r1
    //     0xc08ea8: stur            w1, [x0, #0xf]
    // 0xc08eac: ldur            x1, [fp, #-8]
    // 0xc08eb0: StoreField: r0->field_b = r1
    //     0xc08eb0: stur            w1, [x0, #0xb]
    // 0xc08eb4: LeaveFrame
    //     0xc08eb4: mov             SP, fp
    //     0xc08eb8: ldp             fp, lr, [SP], #0x10
    // 0xc08ebc: ret
    //     0xc08ebc: ret             
    // 0xc08ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08ec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08ec4: b               #0xc08d80
    // 0xc08ec8: r9 = _videoPlayerController
    //     0xc08ec8: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a1a0] Field <_ProductVideoMediaCarouselState@1747127890._videoPlayerController@1747127890>: late (offset: 0x14)
    //     0xc08ecc: ldr             x9, [x9, #0x1a0]
    // 0xc08ed0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc08ed0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88568, size: 0x74
    // 0xc88568: EnterFrame
    //     0xc88568: stp             fp, lr, [SP, #-0x10]!
    //     0xc8856c: mov             fp, SP
    // 0xc88570: AllocStack(0x8)
    //     0xc88570: sub             SP, SP, #8
    // 0xc88574: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0xc88574: mov             x0, x1
    //     0xc88578: stur            x1, [fp, #-8]
    // 0xc8857c: CheckStackOverflow
    //     0xc8857c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88580: cmp             SP, x16
    //     0xc88584: b.ls            #0xc885c8
    // 0xc88588: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc88588: ldur            w1, [x0, #0x17]
    // 0xc8858c: DecompressPointer r1
    //     0xc8858c: add             x1, x1, HEAP, lsl #32
    // 0xc88590: cmp             w1, NULL
    // 0xc88594: b.eq            #0xc885a0
    // 0xc88598: r0 = dispose()
    //     0xc88598: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc8859c: ldur            x0, [fp, #-8]
    // 0xc885a0: LoadField: r1 = r0->field_13
    //     0xc885a0: ldur            w1, [x0, #0x13]
    // 0xc885a4: DecompressPointer r1
    //     0xc885a4: add             x1, x1, HEAP, lsl #32
    // 0xc885a8: r16 = Sentinel
    //     0xc885a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc885ac: cmp             w1, w16
    // 0xc885b0: b.eq            #0xc885d0
    // 0xc885b4: r0 = dispose()
    //     0xc885b4: bl              #0xc75c5c  ; [package:video_player/video_player.dart] VideoPlayerController::dispose
    // 0xc885b8: r0 = Null
    //     0xc885b8: mov             x0, NULL
    // 0xc885bc: LeaveFrame
    //     0xc885bc: mov             SP, fp
    //     0xc885c0: ldp             fp, lr, [SP], #0x10
    // 0xc885c4: ret
    //     0xc885c4: ret             
    // 0xc885c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc885c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc885cc: b               #0xc88588
    // 0xc885d0: r9 = _videoPlayerController
    //     0xc885d0: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a1a0] Field <_ProductVideoMediaCarouselState@1747127890._videoPlayerController@1747127890>: late (offset: 0x14)
    //     0xc885d4: ldr             x9, [x9, #0x1a0]
    // 0xc885d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc885d8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3967, size: 0x10, field offset: 0xc
//   const constructor, 
class ProductVideoMediaCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81154, size: 0x2c
    // 0xc81154: EnterFrame
    //     0xc81154: stp             fp, lr, [SP, #-0x10]!
    //     0xc81158: mov             fp, SP
    // 0xc8115c: mov             x0, x1
    // 0xc81160: r1 = <ProductVideoMediaCarousel>
    //     0xc81160: add             x1, PP, #0x61, lsl #12  ; [pp+0x61b60] TypeArguments: <ProductVideoMediaCarousel>
    //     0xc81164: ldr             x1, [x1, #0xb60]
    // 0xc81168: r0 = _ProductVideoMediaCarouselState()
    //     0xc81168: bl              #0xc81180  ; Allocate_ProductVideoMediaCarouselStateStub -> _ProductVideoMediaCarouselState (size=0x1c)
    // 0xc8116c: r1 = Sentinel
    //     0xc8116c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc81170: StoreField: r0->field_13 = r1
    //     0xc81170: stur            w1, [x0, #0x13]
    // 0xc81174: LeaveFrame
    //     0xc81174: mov             SP, fp
    //     0xc81178: ldp             fp, lr, [SP], #0x10
    // 0xc8117c: ret
    //     0xc8117c: ret             
  }
}
