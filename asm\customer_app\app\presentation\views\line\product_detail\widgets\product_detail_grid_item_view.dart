// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart

// class id: 1049559, size: 0x8
class :: {
}

// class id: 3225, size: 0x14, field offset: 0x14
class _ProductGridItemState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc01d64, size: 0xc0
    // 0xc01d64: EnterFrame
    //     0xc01d64: stp             fp, lr, [SP, #-0x10]!
    //     0xc01d68: mov             fp, SP
    // 0xc01d6c: AllocStack(0x18)
    //     0xc01d6c: sub             SP, SP, #0x18
    // 0xc01d70: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */)
    //     0xc01d70: stur            x1, [fp, #-8]
    // 0xc01d74: CheckStackOverflow
    //     0xc01d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc01d78: cmp             SP, x16
    //     0xc01d7c: b.ls            #0xc01e18
    // 0xc01d80: r1 = 1
    //     0xc01d80: movz            x1, #0x1
    // 0xc01d84: r0 = AllocateContext()
    //     0xc01d84: bl              #0x16f6108  ; AllocateContextStub
    // 0xc01d88: mov             x1, x0
    // 0xc01d8c: ldur            x0, [fp, #-8]
    // 0xc01d90: stur            x1, [fp, #-0x10]
    // 0xc01d94: StoreField: r1->field_f = r0
    //     0xc01d94: stur            w0, [x1, #0xf]
    // 0xc01d98: LoadField: r2 = r0->field_b
    //     0xc01d98: ldur            w2, [x0, #0xb]
    // 0xc01d9c: DecompressPointer r2
    //     0xc01d9c: add             x2, x2, HEAP, lsl #32
    // 0xc01da0: cmp             w2, NULL
    // 0xc01da4: b.eq            #0xc01e20
    // 0xc01da8: LoadField: r0 = r2->field_b
    //     0xc01da8: ldur            w0, [x2, #0xb]
    // 0xc01dac: DecompressPointer r0
    //     0xc01dac: add             x0, x0, HEAP, lsl #32
    // 0xc01db0: r2 = LoadClassIdInstr(r0)
    //     0xc01db0: ldur            x2, [x0, #-1]
    //     0xc01db4: ubfx            x2, x2, #0xc, #0x14
    // 0xc01db8: str             x0, [SP]
    // 0xc01dbc: mov             x0, x2
    // 0xc01dc0: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc01dc0: movz            x17, #0xc898
    //     0xc01dc4: add             lr, x0, x17
    //     0xc01dc8: ldr             lr, [x21, lr, lsl #3]
    //     0xc01dcc: blr             lr
    // 0xc01dd0: ldur            x2, [fp, #-0x10]
    // 0xc01dd4: r1 = Function '<anonymous closure>':.
    //     0xc01dd4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61b78] AnonymousClosure: (0xc01e44), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xc01d64)
    //     0xc01dd8: ldr             x1, [x1, #0xb78]
    // 0xc01ddc: stur            x0, [fp, #-8]
    // 0xc01de0: r0 = AllocateClosure()
    //     0xc01de0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01de4: stur            x0, [fp, #-0x10]
    // 0xc01de8: r0 = GridView()
    //     0xc01de8: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xc01dec: mov             x1, x0
    // 0xc01df0: ldur            x3, [fp, #-0x10]
    // 0xc01df4: ldur            x5, [fp, #-8]
    // 0xc01df8: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0xc01df8: add             x2, PP, #0x61, lsl #12  ; [pp+0x61b80] Obj!SliverGridDelegateWithFixedCrossAxisCount@d564b1
    //     0xc01dfc: ldr             x2, [x2, #0xb80]
    // 0xc01e00: stur            x0, [fp, #-8]
    // 0xc01e04: r0 = GridView.builder()
    //     0xc01e04: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xc01e08: ldur            x0, [fp, #-8]
    // 0xc01e0c: LeaveFrame
    //     0xc01e0c: mov             SP, fp
    //     0xc01e10: ldp             fp, lr, [SP], #0x10
    // 0xc01e14: ret
    //     0xc01e14: ret             
    // 0xc01e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc01e18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc01e1c: b               #0xc01d80
    // 0xc01e20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01e20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc01e44, size: 0x45c
    // 0xc01e44: EnterFrame
    //     0xc01e44: stp             fp, lr, [SP, #-0x10]!
    //     0xc01e48: mov             fp, SP
    // 0xc01e4c: AllocStack(0x58)
    //     0xc01e4c: sub             SP, SP, #0x58
    // 0xc01e50: SetupParameters()
    //     0xc01e50: ldr             x0, [fp, #0x20]
    //     0xc01e54: ldur            w1, [x0, #0x17]
    //     0xc01e58: add             x1, x1, HEAP, lsl #32
    //     0xc01e5c: stur            x1, [fp, #-8]
    // 0xc01e60: CheckStackOverflow
    //     0xc01e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc01e64: cmp             SP, x16
    //     0xc01e68: b.ls            #0xc02294
    // 0xc01e6c: r1 = 1
    //     0xc01e6c: movz            x1, #0x1
    // 0xc01e70: r0 = AllocateContext()
    //     0xc01e70: bl              #0x16f6108  ; AllocateContextStub
    // 0xc01e74: mov             x2, x0
    // 0xc01e78: ldur            x1, [fp, #-8]
    // 0xc01e7c: stur            x2, [fp, #-0x10]
    // 0xc01e80: StoreField: r2->field_b = r1
    //     0xc01e80: stur            w1, [x2, #0xb]
    // 0xc01e84: LoadField: r0 = r1->field_f
    //     0xc01e84: ldur            w0, [x1, #0xf]
    // 0xc01e88: DecompressPointer r0
    //     0xc01e88: add             x0, x0, HEAP, lsl #32
    // 0xc01e8c: LoadField: r3 = r0->field_b
    //     0xc01e8c: ldur            w3, [x0, #0xb]
    // 0xc01e90: DecompressPointer r3
    //     0xc01e90: add             x3, x3, HEAP, lsl #32
    // 0xc01e94: cmp             w3, NULL
    // 0xc01e98: b.eq            #0xc0229c
    // 0xc01e9c: LoadField: r0 = r3->field_b
    //     0xc01e9c: ldur            w0, [x3, #0xb]
    // 0xc01ea0: DecompressPointer r0
    //     0xc01ea0: add             x0, x0, HEAP, lsl #32
    // 0xc01ea4: r3 = LoadClassIdInstr(r0)
    //     0xc01ea4: ldur            x3, [x0, #-1]
    //     0xc01ea8: ubfx            x3, x3, #0xc, #0x14
    // 0xc01eac: ldr             x16, [fp, #0x10]
    // 0xc01eb0: stp             x16, x0, [SP]
    // 0xc01eb4: mov             x0, x3
    // 0xc01eb8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc01eb8: sub             lr, x0, #0xb7
    //     0xc01ebc: ldr             lr, [x21, lr, lsl #3]
    //     0xc01ec0: blr             lr
    // 0xc01ec4: mov             x4, x0
    // 0xc01ec8: ldur            x3, [fp, #-0x10]
    // 0xc01ecc: stur            x4, [fp, #-0x18]
    // 0xc01ed0: StoreField: r3->field_f = r0
    //     0xc01ed0: stur            w0, [x3, #0xf]
    //     0xc01ed4: ldurb           w16, [x3, #-1]
    //     0xc01ed8: ldurb           w17, [x0, #-1]
    //     0xc01edc: and             x16, x17, x16, lsr #2
    //     0xc01ee0: tst             x16, HEAP, lsr #32
    //     0xc01ee4: b.eq            #0xc01eec
    //     0xc01ee8: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc01eec: cmp             w4, NULL
    // 0xc01ef0: b.ne            #0xc01f04
    // 0xc01ef4: r0 = Instance_SizedBox
    //     0xc01ef4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc01ef8: LeaveFrame
    //     0xc01ef8: mov             SP, fp
    //     0xc01efc: ldp             fp, lr, [SP], #0x10
    // 0xc01f00: ret
    //     0xc01f00: ret             
    // 0xc01f04: ldr             x0, [fp, #0x10]
    // 0xc01f08: r1 = Null
    //     0xc01f08: mov             x1, NULL
    // 0xc01f0c: r2 = 8
    //     0xc01f0c: movz            x2, #0x8
    // 0xc01f10: r0 = AllocateArray()
    //     0xc01f10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc01f14: r16 = "product_"
    //     0xc01f14: add             x16, PP, #0x61, lsl #12  ; [pp+0x61b88] "product_"
    //     0xc01f18: ldr             x16, [x16, #0xb88]
    // 0xc01f1c: StoreField: r0->field_f = r16
    //     0xc01f1c: stur            w16, [x0, #0xf]
    // 0xc01f20: ldur            x1, [fp, #-0x18]
    // 0xc01f24: LoadField: r2 = r1->field_47
    //     0xc01f24: ldur            w2, [x1, #0x47]
    // 0xc01f28: DecompressPointer r2
    //     0xc01f28: add             x2, x2, HEAP, lsl #32
    // 0xc01f2c: StoreField: r0->field_13 = r2
    //     0xc01f2c: stur            w2, [x0, #0x13]
    // 0xc01f30: r16 = "_"
    //     0xc01f30: ldr             x16, [PP, #0x45a8]  ; [pp+0x45a8] "_"
    // 0xc01f34: ArrayStore: r0[0] = r16  ; List_4
    //     0xc01f34: stur            w16, [x0, #0x17]
    // 0xc01f38: ldr             x2, [fp, #0x10]
    // 0xc01f3c: StoreField: r0->field_1b = r2
    //     0xc01f3c: stur            w2, [x0, #0x1b]
    // 0xc01f40: str             x0, [SP]
    // 0xc01f44: r0 = _interpolate()
    //     0xc01f44: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc01f48: r1 = <String>
    //     0xc01f48: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xc01f4c: stur            x0, [fp, #-0x20]
    // 0xc01f50: r0 = ValueKey()
    //     0xc01f50: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xc01f54: mov             x3, x0
    // 0xc01f58: ldur            x0, [fp, #-0x20]
    // 0xc01f5c: stur            x3, [fp, #-0x28]
    // 0xc01f60: StoreField: r3->field_b = r0
    //     0xc01f60: stur            w0, [x3, #0xb]
    // 0xc01f64: ldur            x0, [fp, #-0x18]
    // 0xc01f68: LoadField: r1 = r0->field_e7
    //     0xc01f68: ldur            w1, [x0, #0xe7]
    // 0xc01f6c: DecompressPointer r1
    //     0xc01f6c: add             x1, x1, HEAP, lsl #32
    // 0xc01f70: cmp             w1, NULL
    // 0xc01f74: b.ne            #0xc01f80
    // 0xc01f78: r1 = Null
    //     0xc01f78: mov             x1, NULL
    // 0xc01f7c: b               #0xc01f88
    // 0xc01f80: LoadField: r2 = r1->field_b
    //     0xc01f80: ldur            w2, [x1, #0xb]
    // 0xc01f84: mov             x1, x2
    // 0xc01f88: cmp             w1, NULL
    // 0xc01f8c: b.ne            #0xc01f98
    // 0xc01f90: r1 = 0
    //     0xc01f90: movz            x1, #0
    // 0xc01f94: b               #0xc01fa0
    // 0xc01f98: r2 = LoadInt32Instr(r1)
    //     0xc01f98: sbfx            x2, x1, #1, #0x1f
    // 0xc01f9c: mov             x1, x2
    // 0xc01fa0: lsl             x4, x1, #1
    // 0xc01fa4: stur            x4, [fp, #-0x20]
    // 0xc01fa8: r1 = Function '<anonymous closure>':.
    //     0xc01fa8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61b90] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc01fac: ldr             x1, [x1, #0xb90]
    // 0xc01fb0: r2 = Null
    //     0xc01fb0: mov             x2, NULL
    // 0xc01fb4: r0 = AllocateClosure()
    //     0xc01fb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01fb8: ldur            x2, [fp, #-0x10]
    // 0xc01fbc: r1 = Function '<anonymous closure>':.
    //     0xc01fbc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61b98] AnonymousClosure: (0xc03920), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xc01d64)
    //     0xc01fc0: ldr             x1, [x1, #0xb98]
    // 0xc01fc4: stur            x0, [fp, #-0x30]
    // 0xc01fc8: r0 = AllocateClosure()
    //     0xc01fc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01fcc: stur            x0, [fp, #-0x38]
    // 0xc01fd0: r0 = PageView()
    //     0xc01fd0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xc01fd4: mov             x1, x0
    // 0xc01fd8: ldur            x2, [fp, #-0x38]
    // 0xc01fdc: ldur            x3, [fp, #-0x20]
    // 0xc01fe0: ldur            x5, [fp, #-0x30]
    // 0xc01fe4: stur            x0, [fp, #-0x20]
    // 0xc01fe8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xc01fe8: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xc01fec: r0 = PageView.builder()
    //     0xc01fec: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xc01ff0: r0 = AspectRatio()
    //     0xc01ff0: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xc01ff4: d0 = 0.740741
    //     0xc01ff4: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d68] IMM: double(0.7407407407407407) from 0x3fe7b425ed097b42
    //     0xc01ff8: ldr             d0, [x17, #0xd68]
    // 0xc01ffc: stur            x0, [fp, #-0x30]
    // 0xc02000: StoreField: r0->field_f = d0
    //     0xc02000: stur            d0, [x0, #0xf]
    // 0xc02004: ldur            x1, [fp, #-0x20]
    // 0xc02008: StoreField: r0->field_b = r1
    //     0xc02008: stur            w1, [x0, #0xb]
    // 0xc0200c: ldur            x1, [fp, #-0x18]
    // 0xc02010: LoadField: r2 = r1->field_53
    //     0xc02010: ldur            w2, [x1, #0x53]
    // 0xc02014: DecompressPointer r2
    //     0xc02014: add             x2, x2, HEAP, lsl #32
    // 0xc02018: cmp             w2, NULL
    // 0xc0201c: b.ne            #0xc02028
    // 0xc02020: r1 = Null
    //     0xc02020: mov             x1, NULL
    // 0xc02024: b               #0xc02030
    // 0xc02028: LoadField: r1 = r2->field_b
    //     0xc02028: ldur            w1, [x2, #0xb]
    // 0xc0202c: DecompressPointer r1
    //     0xc0202c: add             x1, x1, HEAP, lsl #32
    // 0xc02030: cmp             w1, NULL
    // 0xc02034: b.ne            #0xc0203c
    // 0xc02038: r1 = ""
    //     0xc02038: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0203c: ldr             x3, [fp, #0x10]
    // 0xc02040: ldur            x4, [fp, #-8]
    // 0xc02044: ldur            x2, [fp, #-0x28]
    // 0xc02048: r0 = capitalizeFirstWord()
    //     0xc02048: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xc0204c: ldr             x1, [fp, #0x18]
    // 0xc02050: stur            x0, [fp, #-0x18]
    // 0xc02054: r0 = of()
    //     0xc02054: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc02058: LoadField: r1 = r0->field_87
    //     0xc02058: ldur            w1, [x0, #0x87]
    // 0xc0205c: DecompressPointer r1
    //     0xc0205c: add             x1, x1, HEAP, lsl #32
    // 0xc02060: LoadField: r0 = r1->field_2b
    //     0xc02060: ldur            w0, [x1, #0x2b]
    // 0xc02064: DecompressPointer r0
    //     0xc02064: add             x0, x0, HEAP, lsl #32
    // 0xc02068: r16 = 12.000000
    //     0xc02068: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0206c: ldr             x16, [x16, #0x9e8]
    // 0xc02070: r30 = Instance_Color
    //     0xc02070: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc02074: stp             lr, x16, [SP]
    // 0xc02078: mov             x1, x0
    // 0xc0207c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0207c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc02080: ldr             x4, [x4, #0xaa0]
    // 0xc02084: r0 = copyWith()
    //     0xc02084: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc02088: stur            x0, [fp, #-0x20]
    // 0xc0208c: r0 = Text()
    //     0xc0208c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc02090: mov             x1, x0
    // 0xc02094: ldur            x0, [fp, #-0x18]
    // 0xc02098: stur            x1, [fp, #-0x38]
    // 0xc0209c: StoreField: r1->field_b = r0
    //     0xc0209c: stur            w0, [x1, #0xb]
    // 0xc020a0: ldur            x0, [fp, #-0x20]
    // 0xc020a4: StoreField: r1->field_13 = r0
    //     0xc020a4: stur            w0, [x1, #0x13]
    // 0xc020a8: r0 = Instance_TextOverflow
    //     0xc020a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xc020ac: ldr             x0, [x0, #0xe10]
    // 0xc020b0: StoreField: r1->field_2b = r0
    //     0xc020b0: stur            w0, [x1, #0x2b]
    // 0xc020b4: r0 = 2
    //     0xc020b4: movz            x0, #0x2
    // 0xc020b8: StoreField: r1->field_37 = r0
    //     0xc020b8: stur            w0, [x1, #0x37]
    // 0xc020bc: r0 = Padding()
    //     0xc020bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc020c0: mov             x3, x0
    // 0xc020c4: r0 = Instance_EdgeInsets
    //     0xc020c4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc020c8: ldr             x0, [x0, #0x668]
    // 0xc020cc: stur            x3, [fp, #-0x18]
    // 0xc020d0: StoreField: r3->field_f = r0
    //     0xc020d0: stur            w0, [x3, #0xf]
    // 0xc020d4: ldur            x0, [fp, #-0x38]
    // 0xc020d8: StoreField: r3->field_b = r0
    //     0xc020d8: stur            w0, [x3, #0xb]
    // 0xc020dc: ldur            x0, [fp, #-8]
    // 0xc020e0: LoadField: r1 = r0->field_f
    //     0xc020e0: ldur            w1, [x0, #0xf]
    // 0xc020e4: DecompressPointer r1
    //     0xc020e4: add             x1, x1, HEAP, lsl #32
    // 0xc020e8: ldr             x2, [fp, #0x10]
    // 0xc020ec: r4 = LoadInt32Instr(r2)
    //     0xc020ec: sbfx            x4, x2, #1, #0x1f
    //     0xc020f0: tbz             w2, #0, #0xc020f8
    //     0xc020f4: ldur            x4, [x2, #7]
    // 0xc020f8: mov             x2, x4
    // 0xc020fc: stur            x4, [fp, #-0x40]
    // 0xc02100: r0 = _buildRatingSection()
    //     0xc02100: bl              #0xc02d7c  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildRatingSection
    // 0xc02104: mov             x3, x0
    // 0xc02108: ldur            x0, [fp, #-8]
    // 0xc0210c: stur            x3, [fp, #-0x20]
    // 0xc02110: LoadField: r1 = r0->field_f
    //     0xc02110: ldur            w1, [x0, #0xf]
    // 0xc02114: DecompressPointer r1
    //     0xc02114: add             x1, x1, HEAP, lsl #32
    // 0xc02118: ldur            x2, [fp, #-0x40]
    // 0xc0211c: r0 = _buildPriceSection()
    //     0xc0211c: bl              #0xc0290c  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildPriceSection
    // 0xc02120: mov             x3, x0
    // 0xc02124: ldur            x0, [fp, #-8]
    // 0xc02128: stur            x3, [fp, #-0x38]
    // 0xc0212c: LoadField: r1 = r0->field_f
    //     0xc0212c: ldur            w1, [x0, #0xf]
    // 0xc02130: DecompressPointer r1
    //     0xc02130: add             x1, x1, HEAP, lsl #32
    // 0xc02134: ldur            x2, [fp, #-0x40]
    // 0xc02138: r0 = _buildAddToBagSection()
    //     0xc02138: bl              #0xc022a0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildAddToBagSection
    // 0xc0213c: r1 = Null
    //     0xc0213c: mov             x1, NULL
    // 0xc02140: r2 = 10
    //     0xc02140: movz            x2, #0xa
    // 0xc02144: stur            x0, [fp, #-8]
    // 0xc02148: r0 = AllocateArray()
    //     0xc02148: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0214c: mov             x2, x0
    // 0xc02150: ldur            x0, [fp, #-0x30]
    // 0xc02154: stur            x2, [fp, #-0x48]
    // 0xc02158: StoreField: r2->field_f = r0
    //     0xc02158: stur            w0, [x2, #0xf]
    // 0xc0215c: ldur            x0, [fp, #-0x18]
    // 0xc02160: StoreField: r2->field_13 = r0
    //     0xc02160: stur            w0, [x2, #0x13]
    // 0xc02164: ldur            x0, [fp, #-0x20]
    // 0xc02168: ArrayStore: r2[0] = r0  ; List_4
    //     0xc02168: stur            w0, [x2, #0x17]
    // 0xc0216c: ldur            x0, [fp, #-0x38]
    // 0xc02170: StoreField: r2->field_1b = r0
    //     0xc02170: stur            w0, [x2, #0x1b]
    // 0xc02174: ldur            x0, [fp, #-8]
    // 0xc02178: StoreField: r2->field_1f = r0
    //     0xc02178: stur            w0, [x2, #0x1f]
    // 0xc0217c: r1 = <Widget>
    //     0xc0217c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc02180: r0 = AllocateGrowableArray()
    //     0xc02180: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc02184: mov             x1, x0
    // 0xc02188: ldur            x0, [fp, #-0x48]
    // 0xc0218c: stur            x1, [fp, #-8]
    // 0xc02190: StoreField: r1->field_f = r0
    //     0xc02190: stur            w0, [x1, #0xf]
    // 0xc02194: r0 = 10
    //     0xc02194: movz            x0, #0xa
    // 0xc02198: StoreField: r1->field_b = r0
    //     0xc02198: stur            w0, [x1, #0xb]
    // 0xc0219c: r0 = Column()
    //     0xc0219c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc021a0: mov             x3, x0
    // 0xc021a4: r0 = Instance_Axis
    //     0xc021a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc021a8: stur            x3, [fp, #-0x18]
    // 0xc021ac: StoreField: r3->field_f = r0
    //     0xc021ac: stur            w0, [x3, #0xf]
    // 0xc021b0: r0 = Instance_MainAxisAlignment
    //     0xc021b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc021b4: ldr             x0, [x0, #0xa08]
    // 0xc021b8: StoreField: r3->field_13 = r0
    //     0xc021b8: stur            w0, [x3, #0x13]
    // 0xc021bc: r0 = Instance_MainAxisSize
    //     0xc021bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc021c0: ldr             x0, [x0, #0xdd0]
    // 0xc021c4: ArrayStore: r3[0] = r0  ; List_4
    //     0xc021c4: stur            w0, [x3, #0x17]
    // 0xc021c8: r0 = Instance_CrossAxisAlignment
    //     0xc021c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc021cc: ldr             x0, [x0, #0x890]
    // 0xc021d0: StoreField: r3->field_1b = r0
    //     0xc021d0: stur            w0, [x3, #0x1b]
    // 0xc021d4: r0 = Instance_VerticalDirection
    //     0xc021d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc021d8: ldr             x0, [x0, #0xa20]
    // 0xc021dc: StoreField: r3->field_23 = r0
    //     0xc021dc: stur            w0, [x3, #0x23]
    // 0xc021e0: r0 = Instance_Clip
    //     0xc021e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc021e4: ldr             x0, [x0, #0x38]
    // 0xc021e8: StoreField: r3->field_2b = r0
    //     0xc021e8: stur            w0, [x3, #0x2b]
    // 0xc021ec: StoreField: r3->field_2f = rZR
    //     0xc021ec: stur            xzr, [x3, #0x2f]
    // 0xc021f0: ldur            x0, [fp, #-8]
    // 0xc021f4: StoreField: r3->field_b = r0
    //     0xc021f4: stur            w0, [x3, #0xb]
    // 0xc021f8: ldur            x2, [fp, #-0x10]
    // 0xc021fc: r1 = Function '<anonymous closure>':.
    //     0xc021fc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ba0] AnonymousClosure: (0xc037fc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xc01d64)
    //     0xc02200: ldr             x1, [x1, #0xba0]
    // 0xc02204: r0 = AllocateClosure()
    //     0xc02204: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc02208: stur            x0, [fp, #-8]
    // 0xc0220c: r0 = VisibilityDetector()
    //     0xc0220c: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xc02210: mov             x1, x0
    // 0xc02214: ldur            x0, [fp, #-8]
    // 0xc02218: stur            x1, [fp, #-0x20]
    // 0xc0221c: StoreField: r1->field_f = r0
    //     0xc0221c: stur            w0, [x1, #0xf]
    // 0xc02220: ldur            x0, [fp, #-0x18]
    // 0xc02224: StoreField: r1->field_b = r0
    //     0xc02224: stur            w0, [x1, #0xb]
    // 0xc02228: ldur            x0, [fp, #-0x28]
    // 0xc0222c: StoreField: r1->field_7 = r0
    //     0xc0222c: stur            w0, [x1, #7]
    // 0xc02230: r0 = InkWell()
    //     0xc02230: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc02234: mov             x3, x0
    // 0xc02238: ldur            x0, [fp, #-0x20]
    // 0xc0223c: stur            x3, [fp, #-8]
    // 0xc02240: StoreField: r3->field_b = r0
    //     0xc02240: stur            w0, [x3, #0xb]
    // 0xc02244: ldur            x2, [fp, #-0x10]
    // 0xc02248: r1 = Function '<anonymous closure>':.
    //     0xc02248: add             x1, PP, #0x61, lsl #12  ; [pp+0x61ba8] AnonymousClosure: (0xc036c0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xc01d64)
    //     0xc0224c: ldr             x1, [x1, #0xba8]
    // 0xc02250: r0 = AllocateClosure()
    //     0xc02250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc02254: mov             x1, x0
    // 0xc02258: ldur            x0, [fp, #-8]
    // 0xc0225c: StoreField: r0->field_f = r1
    //     0xc0225c: stur            w1, [x0, #0xf]
    // 0xc02260: r1 = true
    //     0xc02260: add             x1, NULL, #0x20  ; true
    // 0xc02264: StoreField: r0->field_43 = r1
    //     0xc02264: stur            w1, [x0, #0x43]
    // 0xc02268: r2 = Instance_BoxShape
    //     0xc02268: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0226c: ldr             x2, [x2, #0x80]
    // 0xc02270: StoreField: r0->field_47 = r2
    //     0xc02270: stur            w2, [x0, #0x47]
    // 0xc02274: StoreField: r0->field_6f = r1
    //     0xc02274: stur            w1, [x0, #0x6f]
    // 0xc02278: r2 = false
    //     0xc02278: add             x2, NULL, #0x30  ; false
    // 0xc0227c: StoreField: r0->field_73 = r2
    //     0xc0227c: stur            w2, [x0, #0x73]
    // 0xc02280: StoreField: r0->field_83 = r1
    //     0xc02280: stur            w1, [x0, #0x83]
    // 0xc02284: StoreField: r0->field_7b = r2
    //     0xc02284: stur            w2, [x0, #0x7b]
    // 0xc02288: LeaveFrame
    //     0xc02288: mov             SP, fp
    //     0xc0228c: ldp             fp, lr, [SP], #0x10
    // 0xc02290: ret
    //     0xc02290: ret             
    // 0xc02294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc02294: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc02298: b               #0xc01e6c
    // 0xc0229c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0229c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagSection(/* No info */) {
    // ** addr: 0xc022a0, size: 0x574
    // 0xc022a0: EnterFrame
    //     0xc022a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc022a4: mov             fp, SP
    // 0xc022a8: AllocStack(0x58)
    //     0xc022a8: sub             SP, SP, #0x58
    // 0xc022ac: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc022ac: stur            x1, [fp, #-8]
    //     0xc022b0: stur            x2, [fp, #-0x10]
    // 0xc022b4: CheckStackOverflow
    //     0xc022b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc022b8: cmp             SP, x16
    //     0xc022bc: b.ls            #0xc027f0
    // 0xc022c0: r1 = 2
    //     0xc022c0: movz            x1, #0x2
    // 0xc022c4: r0 = AllocateContext()
    //     0xc022c4: bl              #0x16f6108  ; AllocateContextStub
    // 0xc022c8: mov             x3, x0
    // 0xc022cc: ldur            x2, [fp, #-8]
    // 0xc022d0: stur            x3, [fp, #-0x18]
    // 0xc022d4: StoreField: r3->field_f = r2
    //     0xc022d4: stur            w2, [x3, #0xf]
    // 0xc022d8: LoadField: r0 = r2->field_b
    //     0xc022d8: ldur            w0, [x2, #0xb]
    // 0xc022dc: DecompressPointer r0
    //     0xc022dc: add             x0, x0, HEAP, lsl #32
    // 0xc022e0: cmp             w0, NULL
    // 0xc022e4: b.eq            #0xc027f8
    // 0xc022e8: LoadField: r4 = r0->field_b
    //     0xc022e8: ldur            w4, [x0, #0xb]
    // 0xc022ec: DecompressPointer r4
    //     0xc022ec: add             x4, x4, HEAP, lsl #32
    // 0xc022f0: ldur            x5, [fp, #-0x10]
    // 0xc022f4: r0 = BoxInt64Instr(r5)
    //     0xc022f4: sbfiz           x0, x5, #1, #0x1f
    //     0xc022f8: cmp             x5, x0, asr #1
    //     0xc022fc: b.eq            #0xc02308
    //     0xc02300: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc02304: stur            x5, [x0, #7]
    // 0xc02308: r1 = LoadClassIdInstr(r4)
    //     0xc02308: ldur            x1, [x4, #-1]
    //     0xc0230c: ubfx            x1, x1, #0xc, #0x14
    // 0xc02310: stp             x0, x4, [SP]
    // 0xc02314: mov             x0, x1
    // 0xc02318: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc02318: sub             lr, x0, #0xb7
    //     0xc0231c: ldr             lr, [x21, lr, lsl #3]
    //     0xc02320: blr             lr
    // 0xc02324: mov             x1, x0
    // 0xc02328: ldur            x2, [fp, #-0x18]
    // 0xc0232c: stur            x1, [fp, #-0x20]
    // 0xc02330: StoreField: r2->field_13 = r0
    //     0xc02330: stur            w0, [x2, #0x13]
    //     0xc02334: ldurb           w16, [x2, #-1]
    //     0xc02338: ldurb           w17, [x0, #-1]
    //     0xc0233c: and             x16, x17, x16, lsr #2
    //     0xc02340: tst             x16, HEAP, lsr #32
    //     0xc02344: b.eq            #0xc0234c
    //     0xc02348: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc0234c: cmp             w1, NULL
    // 0xc02350: b.ne            #0xc02364
    // 0xc02354: r0 = Instance_SizedBox
    //     0xc02354: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc02358: LeaveFrame
    //     0xc02358: mov             SP, fp
    //     0xc0235c: ldp             fp, lr, [SP], #0x10
    // 0xc02360: ret
    //     0xc02360: ret             
    // 0xc02364: ldur            x0, [fp, #-8]
    // 0xc02368: LoadField: r3 = r0->field_b
    //     0xc02368: ldur            w3, [x0, #0xb]
    // 0xc0236c: DecompressPointer r3
    //     0xc0236c: add             x3, x3, HEAP, lsl #32
    // 0xc02370: cmp             w3, NULL
    // 0xc02374: b.eq            #0xc027fc
    // 0xc02378: LoadField: r4 = r3->field_f
    //     0xc02378: ldur            w4, [x3, #0xf]
    // 0xc0237c: DecompressPointer r4
    //     0xc0237c: add             x4, x4, HEAP, lsl #32
    // 0xc02380: LoadField: r3 = r4->field_1f
    //     0xc02380: ldur            w3, [x4, #0x1f]
    // 0xc02384: DecompressPointer r3
    //     0xc02384: add             x3, x3, HEAP, lsl #32
    // 0xc02388: cmp             w3, NULL
    // 0xc0238c: b.ne            #0xc02398
    // 0xc02390: r3 = Null
    //     0xc02390: mov             x3, NULL
    // 0xc02394: b               #0xc023a4
    // 0xc02398: LoadField: r5 = r3->field_7
    //     0xc02398: ldur            w5, [x3, #7]
    // 0xc0239c: DecompressPointer r5
    //     0xc0239c: add             x5, x5, HEAP, lsl #32
    // 0xc023a0: mov             x3, x5
    // 0xc023a4: cmp             w3, NULL
    // 0xc023a8: b.eq            #0xc027e0
    // 0xc023ac: tbnz            w3, #4, #0xc027e0
    // 0xc023b0: LoadField: r3 = r4->field_3f
    //     0xc023b0: ldur            w3, [x4, #0x3f]
    // 0xc023b4: DecompressPointer r3
    //     0xc023b4: add             x3, x3, HEAP, lsl #32
    // 0xc023b8: cmp             w3, NULL
    // 0xc023bc: b.ne            #0xc023c8
    // 0xc023c0: r3 = Null
    //     0xc023c0: mov             x3, NULL
    // 0xc023c4: b               #0xc023d4
    // 0xc023c8: LoadField: r4 = r3->field_23
    //     0xc023c8: ldur            w4, [x3, #0x23]
    // 0xc023cc: DecompressPointer r4
    //     0xc023cc: add             x4, x4, HEAP, lsl #32
    // 0xc023d0: mov             x3, x4
    // 0xc023d4: cmp             w3, NULL
    // 0xc023d8: b.eq            #0xc027e0
    // 0xc023dc: tbnz            w3, #4, #0xc027e0
    // 0xc023e0: r16 = <Size?>
    //     0xc023e0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xc023e4: ldr             x16, [x16, #0x768]
    // 0xc023e8: r30 = Instance_Size
    //     0xc023e8: add             lr, PP, #0x52, lsl #12  ; [pp+0x52da0] Obj!Size@d6c221
    //     0xc023ec: ldr             lr, [lr, #0xda0]
    // 0xc023f0: stp             lr, x16, [SP]
    // 0xc023f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc023f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc023f8: r0 = all()
    //     0xc023f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc023fc: mov             x2, x0
    // 0xc02400: ldur            x0, [fp, #-0x20]
    // 0xc02404: stur            x2, [fp, #-0x28]
    // 0xc02408: r17 = 275
    //     0xc02408: movz            x17, #0x113
    // 0xc0240c: ldr             w1, [x0, x17]
    // 0xc02410: DecompressPointer r1
    //     0xc02410: add             x1, x1, HEAP, lsl #32
    // 0xc02414: cmp             w1, NULL
    // 0xc02418: b.eq            #0xc02420
    // 0xc0241c: tbnz            w1, #4, #0xc02474
    // 0xc02420: ldur            x3, [fp, #-8]
    // 0xc02424: LoadField: r1 = r3->field_f
    //     0xc02424: ldur            w1, [x3, #0xf]
    // 0xc02428: DecompressPointer r1
    //     0xc02428: add             x1, x1, HEAP, lsl #32
    // 0xc0242c: cmp             w1, NULL
    // 0xc02430: b.eq            #0xc02800
    // 0xc02434: r0 = of()
    //     0xc02434: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc02438: LoadField: r1 = r0->field_5b
    //     0xc02438: ldur            w1, [x0, #0x5b]
    // 0xc0243c: DecompressPointer r1
    //     0xc0243c: add             x1, x1, HEAP, lsl #32
    // 0xc02440: r0 = LoadClassIdInstr(r1)
    //     0xc02440: ldur            x0, [x1, #-1]
    //     0xc02444: ubfx            x0, x0, #0xc, #0x14
    // 0xc02448: d0 = 0.100000
    //     0xc02448: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0244c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc0244c: sub             lr, x0, #0xffa
    //     0xc02450: ldr             lr, [x21, lr, lsl #3]
    //     0xc02454: blr             lr
    // 0xc02458: r16 = <Color>
    //     0xc02458: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc0245c: ldr             x16, [x16, #0xf80]
    // 0xc02460: stp             x0, x16, [SP]
    // 0xc02464: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc02464: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc02468: r0 = all()
    //     0xc02468: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0246c: mov             x2, x0
    // 0xc02470: b               #0xc02490
    // 0xc02474: r16 = <Color>
    //     0xc02474: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc02478: ldr             x16, [x16, #0xf80]
    // 0xc0247c: r30 = Instance_Color
    //     0xc0247c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc02480: stp             lr, x16, [SP]
    // 0xc02484: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc02484: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc02488: r0 = all()
    //     0xc02488: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc0248c: mov             x2, x0
    // 0xc02490: ldur            x0, [fp, #-0x20]
    // 0xc02494: stur            x2, [fp, #-0x30]
    // 0xc02498: r17 = 275
    //     0xc02498: movz            x17, #0x113
    // 0xc0249c: ldr             w1, [x0, x17]
    // 0xc024a0: DecompressPointer r1
    //     0xc024a0: add             x1, x1, HEAP, lsl #32
    // 0xc024a4: cmp             w1, NULL
    // 0xc024a8: b.eq            #0xc024b0
    // 0xc024ac: tbnz            w1, #4, #0xc02504
    // 0xc024b0: ldur            x3, [fp, #-8]
    // 0xc024b4: LoadField: r1 = r3->field_f
    //     0xc024b4: ldur            w1, [x3, #0xf]
    // 0xc024b8: DecompressPointer r1
    //     0xc024b8: add             x1, x1, HEAP, lsl #32
    // 0xc024bc: cmp             w1, NULL
    // 0xc024c0: b.eq            #0xc02804
    // 0xc024c4: r0 = of()
    //     0xc024c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc024c8: LoadField: r1 = r0->field_5b
    //     0xc024c8: ldur            w1, [x0, #0x5b]
    // 0xc024cc: DecompressPointer r1
    //     0xc024cc: add             x1, x1, HEAP, lsl #32
    // 0xc024d0: r0 = LoadClassIdInstr(r1)
    //     0xc024d0: ldur            x0, [x1, #-1]
    //     0xc024d4: ubfx            x0, x0, #0xc, #0x14
    // 0xc024d8: d0 = 0.100000
    //     0xc024d8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc024dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc024dc: sub             lr, x0, #0xffa
    //     0xc024e0: ldr             lr, [x21, lr, lsl #3]
    //     0xc024e4: blr             lr
    // 0xc024e8: r16 = <Color>
    //     0xc024e8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc024ec: ldr             x16, [x16, #0xf80]
    // 0xc024f0: stp             x0, x16, [SP]
    // 0xc024f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc024f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc024f8: r0 = all()
    //     0xc024f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc024fc: mov             x3, x0
    // 0xc02500: b               #0xc0253c
    // 0xc02504: ldur            x0, [fp, #-8]
    // 0xc02508: LoadField: r1 = r0->field_f
    //     0xc02508: ldur            w1, [x0, #0xf]
    // 0xc0250c: DecompressPointer r1
    //     0xc0250c: add             x1, x1, HEAP, lsl #32
    // 0xc02510: cmp             w1, NULL
    // 0xc02514: b.eq            #0xc02808
    // 0xc02518: r0 = of()
    //     0xc02518: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0251c: LoadField: r1 = r0->field_5b
    //     0xc0251c: ldur            w1, [x0, #0x5b]
    // 0xc02520: DecompressPointer r1
    //     0xc02520: add             x1, x1, HEAP, lsl #32
    // 0xc02524: r16 = <Color>
    //     0xc02524: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc02528: ldr             x16, [x16, #0xf80]
    // 0xc0252c: stp             x1, x16, [SP]
    // 0xc02530: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc02530: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc02534: r0 = all()
    //     0xc02534: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc02538: mov             x3, x0
    // 0xc0253c: ldur            x0, [fp, #-0x20]
    // 0xc02540: ldur            x2, [fp, #-0x28]
    // 0xc02544: ldur            x1, [fp, #-0x30]
    // 0xc02548: stur            x3, [fp, #-0x38]
    // 0xc0254c: r16 = <OutlinedBorder?>
    //     0xc0254c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52da8] TypeArguments: <OutlinedBorder?>
    //     0xc02550: ldr             x16, [x16, #0xda8]
    // 0xc02554: r30 = Instance_RoundedRectangleBorder
    //     0xc02554: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xc02558: ldr             lr, [lr, #0xd68]
    // 0xc0255c: stp             lr, x16, [SP]
    // 0xc02560: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc02560: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc02564: r0 = all()
    //     0xc02564: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc02568: stur            x0, [fp, #-0x40]
    // 0xc0256c: r0 = ButtonStyle()
    //     0xc0256c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc02570: mov             x1, x0
    // 0xc02574: ldur            x0, [fp, #-0x38]
    // 0xc02578: stur            x1, [fp, #-0x48]
    // 0xc0257c: StoreField: r1->field_b = r0
    //     0xc0257c: stur            w0, [x1, #0xb]
    // 0xc02580: ldur            x0, [fp, #-0x30]
    // 0xc02584: StoreField: r1->field_f = r0
    //     0xc02584: stur            w0, [x1, #0xf]
    // 0xc02588: ldur            x0, [fp, #-0x28]
    // 0xc0258c: StoreField: r1->field_27 = r0
    //     0xc0258c: stur            w0, [x1, #0x27]
    // 0xc02590: ldur            x0, [fp, #-0x40]
    // 0xc02594: StoreField: r1->field_43 = r0
    //     0xc02594: stur            w0, [x1, #0x43]
    // 0xc02598: r0 = TextButtonThemeData()
    //     0xc02598: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc0259c: mov             x1, x0
    // 0xc025a0: ldur            x0, [fp, #-0x48]
    // 0xc025a4: stur            x1, [fp, #-0x28]
    // 0xc025a8: StoreField: r1->field_7 = r0
    //     0xc025a8: stur            w0, [x1, #7]
    // 0xc025ac: ldur            x0, [fp, #-0x20]
    // 0xc025b0: r17 = 275
    //     0xc025b0: movz            x17, #0x113
    // 0xc025b4: ldr             w2, [x0, x17]
    // 0xc025b8: DecompressPointer r2
    //     0xc025b8: add             x2, x2, HEAP, lsl #32
    // 0xc025bc: cmp             w2, NULL
    // 0xc025c0: b.eq            #0xc025c8
    // 0xc025c4: tbnz            w2, #4, #0xc02684
    // 0xc025c8: LoadField: r2 = r0->field_f
    //     0xc025c8: ldur            w2, [x0, #0xf]
    // 0xc025cc: DecompressPointer r2
    //     0xc025cc: add             x2, x2, HEAP, lsl #32
    // 0xc025d0: cmp             w2, NULL
    // 0xc025d4: b.ne            #0xc025e0
    // 0xc025d8: r0 = Null
    //     0xc025d8: mov             x0, NULL
    // 0xc025dc: b               #0xc025f8
    // 0xc025e0: r0 = LoadClassIdInstr(r2)
    //     0xc025e0: ldur            x0, [x2, #-1]
    //     0xc025e4: ubfx            x0, x0, #0xc, #0x14
    // 0xc025e8: str             x2, [SP]
    // 0xc025ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc025ec: sub             lr, x0, #1, lsl #12
    //     0xc025f0: ldr             lr, [x21, lr, lsl #3]
    //     0xc025f4: blr             lr
    // 0xc025f8: cmp             w0, NULL
    // 0xc025fc: b.ne            #0xc02604
    // 0xc02600: r0 = ""
    //     0xc02600: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc02604: ldur            x1, [fp, #-8]
    // 0xc02608: stur            x0, [fp, #-0x30]
    // 0xc0260c: LoadField: r2 = r1->field_f
    //     0xc0260c: ldur            w2, [x1, #0xf]
    // 0xc02610: DecompressPointer r2
    //     0xc02610: add             x2, x2, HEAP, lsl #32
    // 0xc02614: cmp             w2, NULL
    // 0xc02618: b.eq            #0xc0280c
    // 0xc0261c: mov             x1, x2
    // 0xc02620: r0 = of()
    //     0xc02620: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc02624: LoadField: r1 = r0->field_87
    //     0xc02624: ldur            w1, [x0, #0x87]
    // 0xc02628: DecompressPointer r1
    //     0xc02628: add             x1, x1, HEAP, lsl #32
    // 0xc0262c: LoadField: r0 = r1->field_7
    //     0xc0262c: ldur            w0, [x1, #7]
    // 0xc02630: DecompressPointer r0
    //     0xc02630: add             x0, x0, HEAP, lsl #32
    // 0xc02634: stur            x0, [fp, #-0x38]
    // 0xc02638: r1 = Instance_Color
    //     0xc02638: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0263c: d0 = 0.400000
    //     0xc0263c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xc02640: r0 = withOpacity()
    //     0xc02640: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc02644: r16 = 14.000000
    //     0xc02644: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc02648: ldr             x16, [x16, #0x1d8]
    // 0xc0264c: stp             x0, x16, [SP]
    // 0xc02650: ldur            x1, [fp, #-0x38]
    // 0xc02654: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc02654: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc02658: ldr             x4, [x4, #0xaa0]
    // 0xc0265c: r0 = copyWith()
    //     0xc0265c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc02660: stur            x0, [fp, #-0x38]
    // 0xc02664: r0 = Text()
    //     0xc02664: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc02668: mov             x1, x0
    // 0xc0266c: ldur            x0, [fp, #-0x30]
    // 0xc02670: StoreField: r1->field_b = r0
    //     0xc02670: stur            w0, [x1, #0xb]
    // 0xc02674: ldur            x0, [fp, #-0x38]
    // 0xc02678: StoreField: r1->field_13 = r0
    //     0xc02678: stur            w0, [x1, #0x13]
    // 0xc0267c: mov             x3, x1
    // 0xc02680: b               #0xc02738
    // 0xc02684: ldur            x1, [fp, #-8]
    // 0xc02688: LoadField: r2 = r0->field_f
    //     0xc02688: ldur            w2, [x0, #0xf]
    // 0xc0268c: DecompressPointer r2
    //     0xc0268c: add             x2, x2, HEAP, lsl #32
    // 0xc02690: cmp             w2, NULL
    // 0xc02694: b.ne            #0xc026a0
    // 0xc02698: r0 = Null
    //     0xc02698: mov             x0, NULL
    // 0xc0269c: b               #0xc026b8
    // 0xc026a0: r0 = LoadClassIdInstr(r2)
    //     0xc026a0: ldur            x0, [x2, #-1]
    //     0xc026a4: ubfx            x0, x0, #0xc, #0x14
    // 0xc026a8: str             x2, [SP]
    // 0xc026ac: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc026ac: sub             lr, x0, #1, lsl #12
    //     0xc026b0: ldr             lr, [x21, lr, lsl #3]
    //     0xc026b4: blr             lr
    // 0xc026b8: cmp             w0, NULL
    // 0xc026bc: b.ne            #0xc026c8
    // 0xc026c0: r2 = ""
    //     0xc026c0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc026c4: b               #0xc026cc
    // 0xc026c8: mov             x2, x0
    // 0xc026cc: ldur            x0, [fp, #-8]
    // 0xc026d0: stur            x2, [fp, #-0x20]
    // 0xc026d4: LoadField: r1 = r0->field_f
    //     0xc026d4: ldur            w1, [x0, #0xf]
    // 0xc026d8: DecompressPointer r1
    //     0xc026d8: add             x1, x1, HEAP, lsl #32
    // 0xc026dc: cmp             w1, NULL
    // 0xc026e0: b.eq            #0xc02810
    // 0xc026e4: r0 = of()
    //     0xc026e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc026e8: LoadField: r1 = r0->field_87
    //     0xc026e8: ldur            w1, [x0, #0x87]
    // 0xc026ec: DecompressPointer r1
    //     0xc026ec: add             x1, x1, HEAP, lsl #32
    // 0xc026f0: LoadField: r0 = r1->field_7
    //     0xc026f0: ldur            w0, [x1, #7]
    // 0xc026f4: DecompressPointer r0
    //     0xc026f4: add             x0, x0, HEAP, lsl #32
    // 0xc026f8: r16 = 14.000000
    //     0xc026f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc026fc: ldr             x16, [x16, #0x1d8]
    // 0xc02700: r30 = Instance_Color
    //     0xc02700: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc02704: stp             lr, x16, [SP]
    // 0xc02708: mov             x1, x0
    // 0xc0270c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0270c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc02710: ldr             x4, [x4, #0xaa0]
    // 0xc02714: r0 = copyWith()
    //     0xc02714: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc02718: stur            x0, [fp, #-8]
    // 0xc0271c: r0 = Text()
    //     0xc0271c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc02720: mov             x1, x0
    // 0xc02724: ldur            x0, [fp, #-0x20]
    // 0xc02728: StoreField: r1->field_b = r0
    //     0xc02728: stur            w0, [x1, #0xb]
    // 0xc0272c: ldur            x0, [fp, #-8]
    // 0xc02730: StoreField: r1->field_13 = r0
    //     0xc02730: stur            w0, [x1, #0x13]
    // 0xc02734: mov             x3, x1
    // 0xc02738: ldur            x0, [fp, #-0x28]
    // 0xc0273c: ldur            x2, [fp, #-0x18]
    // 0xc02740: stur            x3, [fp, #-8]
    // 0xc02744: r1 = Function '<anonymous closure>':.
    //     0xc02744: add             x1, PP, #0x61, lsl #12  ; [pp+0x61bf8] AnonymousClosure: (0xc02814), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildAddToBagSection (0xc022a0)
    //     0xc02748: ldr             x1, [x1, #0xbf8]
    // 0xc0274c: r0 = AllocateClosure()
    //     0xc0274c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc02750: stur            x0, [fp, #-0x18]
    // 0xc02754: r0 = TextButton()
    //     0xc02754: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc02758: mov             x1, x0
    // 0xc0275c: ldur            x0, [fp, #-0x18]
    // 0xc02760: stur            x1, [fp, #-0x20]
    // 0xc02764: StoreField: r1->field_b = r0
    //     0xc02764: stur            w0, [x1, #0xb]
    // 0xc02768: r0 = false
    //     0xc02768: add             x0, NULL, #0x30  ; false
    // 0xc0276c: StoreField: r1->field_27 = r0
    //     0xc0276c: stur            w0, [x1, #0x27]
    // 0xc02770: r0 = true
    //     0xc02770: add             x0, NULL, #0x20  ; true
    // 0xc02774: StoreField: r1->field_2f = r0
    //     0xc02774: stur            w0, [x1, #0x2f]
    // 0xc02778: ldur            x0, [fp, #-8]
    // 0xc0277c: StoreField: r1->field_37 = r0
    //     0xc0277c: stur            w0, [x1, #0x37]
    // 0xc02780: r0 = TextButtonTheme()
    //     0xc02780: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc02784: mov             x1, x0
    // 0xc02788: ldur            x0, [fp, #-0x28]
    // 0xc0278c: stur            x1, [fp, #-8]
    // 0xc02790: StoreField: r1->field_f = r0
    //     0xc02790: stur            w0, [x1, #0xf]
    // 0xc02794: ldur            x0, [fp, #-0x20]
    // 0xc02798: StoreField: r1->field_b = r0
    //     0xc02798: stur            w0, [x1, #0xb]
    // 0xc0279c: r0 = SizedBox()
    //     0xc0279c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc027a0: mov             x1, x0
    // 0xc027a4: r0 = inf
    //     0xc027a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc027a8: ldr             x0, [x0, #0x9f8]
    // 0xc027ac: stur            x1, [fp, #-0x18]
    // 0xc027b0: StoreField: r1->field_f = r0
    //     0xc027b0: stur            w0, [x1, #0xf]
    // 0xc027b4: ldur            x0, [fp, #-8]
    // 0xc027b8: StoreField: r1->field_b = r0
    //     0xc027b8: stur            w0, [x1, #0xb]
    // 0xc027bc: r0 = Padding()
    //     0xc027bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc027c0: r1 = Instance_EdgeInsets
    //     0xc027c0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xc027c4: ldr             x1, [x1, #0x770]
    // 0xc027c8: StoreField: r0->field_f = r1
    //     0xc027c8: stur            w1, [x0, #0xf]
    // 0xc027cc: ldur            x1, [fp, #-0x18]
    // 0xc027d0: StoreField: r0->field_b = r1
    //     0xc027d0: stur            w1, [x0, #0xb]
    // 0xc027d4: LeaveFrame
    //     0xc027d4: mov             SP, fp
    //     0xc027d8: ldp             fp, lr, [SP], #0x10
    // 0xc027dc: ret
    //     0xc027dc: ret             
    // 0xc027e0: r0 = Instance_SizedBox
    //     0xc027e0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc027e4: LeaveFrame
    //     0xc027e4: mov             SP, fp
    //     0xc027e8: ldp             fp, lr, [SP], #0x10
    // 0xc027ec: ret
    //     0xc027ec: ret             
    // 0xc027f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc027f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc027f4: b               #0xc022c0
    // 0xc027f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc027f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc027fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc027fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02800: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02804: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02804: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02808: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0280c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0280c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02810: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02810: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc02814, size: 0xf8
    // 0xc02814: EnterFrame
    //     0xc02814: stp             fp, lr, [SP, #-0x10]!
    //     0xc02818: mov             fp, SP
    // 0xc0281c: AllocStack(0x20)
    //     0xc0281c: sub             SP, SP, #0x20
    // 0xc02820: SetupParameters()
    //     0xc02820: ldr             x0, [fp, #0x10]
    //     0xc02824: ldur            w1, [x0, #0x17]
    //     0xc02828: add             x1, x1, HEAP, lsl #32
    //     0xc0282c: stur            x1, [fp, #-0x10]
    // 0xc02830: CheckStackOverflow
    //     0xc02830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc02834: cmp             SP, x16
    //     0xc02838: b.ls            #0xc028f8
    // 0xc0283c: LoadField: r0 = r1->field_13
    //     0xc0283c: ldur            w0, [x1, #0x13]
    // 0xc02840: DecompressPointer r0
    //     0xc02840: add             x0, x0, HEAP, lsl #32
    // 0xc02844: stur            x0, [fp, #-8]
    // 0xc02848: cmp             w0, NULL
    // 0xc0284c: b.eq            #0xc02900
    // 0xc02850: r17 = 275
    //     0xc02850: movz            x17, #0x113
    // 0xc02854: ldr             w2, [x0, x17]
    // 0xc02858: DecompressPointer r2
    //     0xc02858: add             x2, x2, HEAP, lsl #32
    // 0xc0285c: cmp             w2, NULL
    // 0xc02860: b.eq            #0xc02868
    // 0xc02864: tbz             w2, #4, #0xc028e8
    // 0xc02868: LoadField: r2 = r1->field_f
    //     0xc02868: ldur            w2, [x1, #0xf]
    // 0xc0286c: DecompressPointer r2
    //     0xc0286c: add             x2, x2, HEAP, lsl #32
    // 0xc02870: LoadField: r3 = r2->field_b
    //     0xc02870: ldur            w3, [x2, #0xb]
    // 0xc02874: DecompressPointer r3
    //     0xc02874: add             x3, x3, HEAP, lsl #32
    // 0xc02878: cmp             w3, NULL
    // 0xc0287c: b.eq            #0xc02904
    // 0xc02880: LoadField: r2 = r3->field_2f
    //     0xc02880: ldur            w2, [x3, #0x2f]
    // 0xc02884: DecompressPointer r2
    //     0xc02884: add             x2, x2, HEAP, lsl #32
    // 0xc02888: r16 = "add_to_bag"
    //     0xc02888: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xc0288c: ldr             x16, [x16, #0xa38]
    // 0xc02890: stp             x16, x2, [SP]
    // 0xc02894: r4 = 0
    //     0xc02894: movz            x4, #0
    // 0xc02898: ldr             x0, [SP, #8]
    // 0xc0289c: r16 = UnlinkedCall_0x613b5c
    //     0xc0289c: add             x16, PP, #0x61, lsl #12  ; [pp+0x61c00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc028a0: add             x16, x16, #0xc00
    // 0xc028a4: ldp             x5, lr, [x16]
    // 0xc028a8: blr             lr
    // 0xc028ac: ldur            x0, [fp, #-0x10]
    // 0xc028b0: LoadField: r1 = r0->field_f
    //     0xc028b0: ldur            w1, [x0, #0xf]
    // 0xc028b4: DecompressPointer r1
    //     0xc028b4: add             x1, x1, HEAP, lsl #32
    // 0xc028b8: LoadField: r0 = r1->field_b
    //     0xc028b8: ldur            w0, [x1, #0xb]
    // 0xc028bc: DecompressPointer r0
    //     0xc028bc: add             x0, x0, HEAP, lsl #32
    // 0xc028c0: cmp             w0, NULL
    // 0xc028c4: b.eq            #0xc02908
    // 0xc028c8: LoadField: r1 = r0->field_3b
    //     0xc028c8: ldur            w1, [x0, #0x3b]
    // 0xc028cc: DecompressPointer r1
    //     0xc028cc: add             x1, x1, HEAP, lsl #32
    // 0xc028d0: ldur            x16, [fp, #-8]
    // 0xc028d4: stp             x16, x1, [SP]
    // 0xc028d8: mov             x0, x1
    // 0xc028dc: ClosureCall
    //     0xc028dc: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xc028e0: ldur            x2, [x0, #0x1f]
    //     0xc028e4: blr             x2
    // 0xc028e8: r0 = Null
    //     0xc028e8: mov             x0, NULL
    // 0xc028ec: LeaveFrame
    //     0xc028ec: mov             SP, fp
    //     0xc028f0: ldp             fp, lr, [SP], #0x10
    // 0xc028f4: ret
    //     0xc028f4: ret             
    // 0xc028f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc028f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc028fc: b               #0xc0283c
    // 0xc02900: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc02900: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc02904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02904: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02908: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildPriceSection(/* No info */) {
    // ** addr: 0xc0290c, size: 0x470
    // 0xc0290c: EnterFrame
    //     0xc0290c: stp             fp, lr, [SP, #-0x10]!
    //     0xc02910: mov             fp, SP
    // 0xc02914: AllocStack(0x48)
    //     0xc02914: sub             SP, SP, #0x48
    // 0xc02918: SetupParameters(_ProductGridItemState this /* r1 => r3, fp-0x8 */)
    //     0xc02918: mov             x3, x1
    //     0xc0291c: stur            x1, [fp, #-8]
    // 0xc02920: CheckStackOverflow
    //     0xc02920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc02924: cmp             SP, x16
    //     0xc02928: b.ls            #0xc02d64
    // 0xc0292c: LoadField: r0 = r3->field_b
    //     0xc0292c: ldur            w0, [x3, #0xb]
    // 0xc02930: DecompressPointer r0
    //     0xc02930: add             x0, x0, HEAP, lsl #32
    // 0xc02934: cmp             w0, NULL
    // 0xc02938: b.eq            #0xc02d6c
    // 0xc0293c: LoadField: r4 = r0->field_b
    //     0xc0293c: ldur            w4, [x0, #0xb]
    // 0xc02940: DecompressPointer r4
    //     0xc02940: add             x4, x4, HEAP, lsl #32
    // 0xc02944: r0 = BoxInt64Instr(r2)
    //     0xc02944: sbfiz           x0, x2, #1, #0x1f
    //     0xc02948: cmp             x2, x0, asr #1
    //     0xc0294c: b.eq            #0xc02958
    //     0xc02950: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc02954: stur            x2, [x0, #7]
    // 0xc02958: r1 = LoadClassIdInstr(r4)
    //     0xc02958: ldur            x1, [x4, #-1]
    //     0xc0295c: ubfx            x1, x1, #0xc, #0x14
    // 0xc02960: stp             x0, x4, [SP]
    // 0xc02964: mov             x0, x1
    // 0xc02968: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc02968: sub             lr, x0, #0xb7
    //     0xc0296c: ldr             lr, [x21, lr, lsl #3]
    //     0xc02970: blr             lr
    // 0xc02974: stur            x0, [fp, #-0x18]
    // 0xc02978: cmp             w0, NULL
    // 0xc0297c: b.ne            #0xc02990
    // 0xc02980: r0 = Instance_SizedBox
    //     0xc02980: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc02984: LeaveFrame
    //     0xc02984: mov             SP, fp
    //     0xc02988: ldp             fp, lr, [SP], #0x10
    // 0xc0298c: ret
    //     0xc0298c: ret             
    // 0xc02990: LoadField: r3 = r0->field_f3
    //     0xc02990: ldur            w3, [x0, #0xf3]
    // 0xc02994: DecompressPointer r3
    //     0xc02994: add             x3, x3, HEAP, lsl #32
    // 0xc02998: stur            x3, [fp, #-0x10]
    // 0xc0299c: cmp             w3, NULL
    // 0xc029a0: b.ne            #0xc029ac
    // 0xc029a4: r1 = Null
    //     0xc029a4: mov             x1, NULL
    // 0xc029a8: b               #0xc029c4
    // 0xc029ac: LoadField: r1 = r3->field_7
    //     0xc029ac: ldur            w1, [x3, #7]
    // 0xc029b0: cbnz            w1, #0xc029bc
    // 0xc029b4: r2 = false
    //     0xc029b4: add             x2, NULL, #0x30  ; false
    // 0xc029b8: b               #0xc029c0
    // 0xc029bc: r2 = true
    //     0xc029bc: add             x2, NULL, #0x20  ; true
    // 0xc029c0: mov             x1, x2
    // 0xc029c4: cmp             w1, NULL
    // 0xc029c8: b.eq            #0xc029d0
    // 0xc029cc: tbz             w1, #4, #0xc029e0
    // 0xc029d0: r0 = Instance_SizedBox
    //     0xc029d0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc029d4: LeaveFrame
    //     0xc029d4: mov             SP, fp
    //     0xc029d8: ldp             fp, lr, [SP], #0x10
    // 0xc029dc: ret
    //     0xc029dc: ret             
    // 0xc029e0: ldur            x4, [fp, #-8]
    // 0xc029e4: r1 = Null
    //     0xc029e4: mov             x1, NULL
    // 0xc029e8: r2 = 4
    //     0xc029e8: movz            x2, #0x4
    // 0xc029ec: r0 = AllocateArray()
    //     0xc029ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc029f0: mov             x1, x0
    // 0xc029f4: ldur            x0, [fp, #-0x10]
    // 0xc029f8: StoreField: r1->field_f = r0
    //     0xc029f8: stur            w0, [x1, #0xf]
    // 0xc029fc: r16 = "  "
    //     0xc029fc: ldr             x16, [PP, #0xc58]  ; [pp+0xc58] "  "
    // 0xc02a00: StoreField: r1->field_13 = r16
    //     0xc02a00: stur            w16, [x1, #0x13]
    // 0xc02a04: str             x1, [SP]
    // 0xc02a08: r0 = _interpolate()
    //     0xc02a08: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc02a0c: mov             x2, x0
    // 0xc02a10: ldur            x0, [fp, #-8]
    // 0xc02a14: stur            x2, [fp, #-0x10]
    // 0xc02a18: LoadField: r1 = r0->field_f
    //     0xc02a18: ldur            w1, [x0, #0xf]
    // 0xc02a1c: DecompressPointer r1
    //     0xc02a1c: add             x1, x1, HEAP, lsl #32
    // 0xc02a20: cmp             w1, NULL
    // 0xc02a24: b.eq            #0xc02d70
    // 0xc02a28: r0 = of()
    //     0xc02a28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc02a2c: LoadField: r1 = r0->field_87
    //     0xc02a2c: ldur            w1, [x0, #0x87]
    // 0xc02a30: DecompressPointer r1
    //     0xc02a30: add             x1, x1, HEAP, lsl #32
    // 0xc02a34: LoadField: r0 = r1->field_7
    //     0xc02a34: ldur            w0, [x1, #7]
    // 0xc02a38: DecompressPointer r0
    //     0xc02a38: add             x0, x0, HEAP, lsl #32
    // 0xc02a3c: r16 = 14.000000
    //     0xc02a3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc02a40: ldr             x16, [x16, #0x1d8]
    // 0xc02a44: r30 = Instance_Color
    //     0xc02a44: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc02a48: stp             lr, x16, [SP]
    // 0xc02a4c: mov             x1, x0
    // 0xc02a50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc02a50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc02a54: ldr             x4, [x4, #0xaa0]
    // 0xc02a58: r0 = copyWith()
    //     0xc02a58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc02a5c: stur            x0, [fp, #-0x20]
    // 0xc02a60: r0 = Text()
    //     0xc02a60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc02a64: mov             x1, x0
    // 0xc02a68: ldur            x0, [fp, #-0x10]
    // 0xc02a6c: stur            x1, [fp, #-0x28]
    // 0xc02a70: StoreField: r1->field_b = r0
    //     0xc02a70: stur            w0, [x1, #0xb]
    // 0xc02a74: ldur            x0, [fp, #-0x20]
    // 0xc02a78: StoreField: r1->field_13 = r0
    //     0xc02a78: stur            w0, [x1, #0x13]
    // 0xc02a7c: r0 = WidgetSpan()
    //     0xc02a7c: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xc02a80: mov             x1, x0
    // 0xc02a84: ldur            x0, [fp, #-0x28]
    // 0xc02a88: stur            x1, [fp, #-0x10]
    // 0xc02a8c: StoreField: r1->field_13 = r0
    //     0xc02a8c: stur            w0, [x1, #0x13]
    // 0xc02a90: r0 = Instance_PlaceholderAlignment
    //     0xc02a90: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xc02a94: ldr             x0, [x0, #0xa0]
    // 0xc02a98: StoreField: r1->field_b = r0
    //     0xc02a98: stur            w0, [x1, #0xb]
    // 0xc02a9c: ldur            x0, [fp, #-0x18]
    // 0xc02aa0: LoadField: r2 = r0->field_fb
    //     0xc02aa0: ldur            w2, [x0, #0xfb]
    // 0xc02aa4: DecompressPointer r2
    //     0xc02aa4: add             x2, x2, HEAP, lsl #32
    // 0xc02aa8: str             x2, [SP]
    // 0xc02aac: r0 = _interpolateSingle()
    //     0xc02aac: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xc02ab0: mov             x2, x0
    // 0xc02ab4: ldur            x0, [fp, #-8]
    // 0xc02ab8: stur            x2, [fp, #-0x20]
    // 0xc02abc: LoadField: r1 = r0->field_f
    //     0xc02abc: ldur            w1, [x0, #0xf]
    // 0xc02ac0: DecompressPointer r1
    //     0xc02ac0: add             x1, x1, HEAP, lsl #32
    // 0xc02ac4: cmp             w1, NULL
    // 0xc02ac8: b.eq            #0xc02d74
    // 0xc02acc: r0 = of()
    //     0xc02acc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc02ad0: LoadField: r1 = r0->field_87
    //     0xc02ad0: ldur            w1, [x0, #0x87]
    // 0xc02ad4: DecompressPointer r1
    //     0xc02ad4: add             x1, x1, HEAP, lsl #32
    // 0xc02ad8: LoadField: r0 = r1->field_2b
    //     0xc02ad8: ldur            w0, [x1, #0x2b]
    // 0xc02adc: DecompressPointer r0
    //     0xc02adc: add             x0, x0, HEAP, lsl #32
    // 0xc02ae0: stur            x0, [fp, #-0x28]
    // 0xc02ae4: r1 = Instance_Color
    //     0xc02ae4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc02ae8: d0 = 0.400000
    //     0xc02ae8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xc02aec: r0 = withOpacity()
    //     0xc02aec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc02af0: r16 = 12.000000
    //     0xc02af0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc02af4: ldr             x16, [x16, #0x9e8]
    // 0xc02af8: stp             x16, x0, [SP, #8]
    // 0xc02afc: r16 = Instance_TextDecoration
    //     0xc02afc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xc02b00: ldr             x16, [x16, #0xe30]
    // 0xc02b04: str             x16, [SP]
    // 0xc02b08: ldur            x1, [fp, #-0x28]
    // 0xc02b0c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xc02b0c: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xc02b10: ldr             x4, [x4, #0x7c8]
    // 0xc02b14: r0 = copyWith()
    //     0xc02b14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc02b18: stur            x0, [fp, #-0x28]
    // 0xc02b1c: r0 = TextSpan()
    //     0xc02b1c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc02b20: mov             x1, x0
    // 0xc02b24: ldur            x0, [fp, #-0x20]
    // 0xc02b28: stur            x1, [fp, #-0x30]
    // 0xc02b2c: StoreField: r1->field_b = r0
    //     0xc02b2c: stur            w0, [x1, #0xb]
    // 0xc02b30: r2 = Instance__DeferringMouseCursor
    //     0xc02b30: ldr             x2, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc02b34: ArrayStore: r1[0] = r2  ; List_4
    //     0xc02b34: stur            w2, [x1, #0x17]
    // 0xc02b38: ldur            x0, [fp, #-0x28]
    // 0xc02b3c: StoreField: r1->field_7 = r0
    //     0xc02b3c: stur            w0, [x1, #7]
    // 0xc02b40: ldur            x3, [fp, #-0x18]
    // 0xc02b44: LoadField: r0 = r3->field_5f
    //     0xc02b44: ldur            w0, [x3, #0x5f]
    // 0xc02b48: DecompressPointer r0
    //     0xc02b48: add             x0, x0, HEAP, lsl #32
    // 0xc02b4c: r4 = 60
    //     0xc02b4c: movz            x4, #0x3c
    // 0xc02b50: branchIfSmi(r0, 0xc02b5c)
    //     0xc02b50: tbz             w0, #0, #0xc02b5c
    // 0xc02b54: r4 = LoadClassIdInstr(r0)
    //     0xc02b54: ldur            x4, [x0, #-1]
    //     0xc02b58: ubfx            x4, x4, #0xc, #0x14
    // 0xc02b5c: r16 = 0.000000
    //     0xc02b5c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc02b60: stp             x16, x0, [SP]
    // 0xc02b64: mov             x0, x4
    // 0xc02b68: mov             lr, x0
    // 0xc02b6c: ldr             lr, [x21, lr, lsl #3]
    // 0xc02b70: blr             lr
    // 0xc02b74: tbz             w0, #4, #0xc02c14
    // 0xc02b78: ldur            x0, [fp, #-0x18]
    // 0xc02b7c: LoadField: r3 = r0->field_5f
    //     0xc02b7c: ldur            w3, [x0, #0x5f]
    // 0xc02b80: DecompressPointer r3
    //     0xc02b80: add             x3, x3, HEAP, lsl #32
    // 0xc02b84: stur            x3, [fp, #-0x20]
    // 0xc02b88: cmp             w3, NULL
    // 0xc02b8c: b.eq            #0xc02c14
    // 0xc02b90: r1 = Null
    //     0xc02b90: mov             x1, NULL
    // 0xc02b94: r2 = 6
    //     0xc02b94: movz            x2, #0x6
    // 0xc02b98: r0 = AllocateArray()
    //     0xc02b98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc02b9c: stur            x0, [fp, #-0x18]
    // 0xc02ba0: r16 = " | "
    //     0xc02ba0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xc02ba4: ldr             x16, [x16, #0xd80]
    // 0xc02ba8: StoreField: r0->field_f = r16
    //     0xc02ba8: stur            w16, [x0, #0xf]
    // 0xc02bac: ldur            x16, [fp, #-0x20]
    // 0xc02bb0: stp             xzr, x16, [SP]
    // 0xc02bb4: r4 = 0
    //     0xc02bb4: movz            x4, #0
    // 0xc02bb8: ldr             x0, [SP, #8]
    // 0xc02bbc: r16 = UnlinkedCall_0x613b5c
    //     0xc02bbc: add             x16, PP, #0x61, lsl #12  ; [pp+0x61c10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc02bc0: add             x16, x16, #0xc10
    // 0xc02bc4: ldp             x5, lr, [x16]
    // 0xc02bc8: blr             lr
    // 0xc02bcc: ldur            x1, [fp, #-0x18]
    // 0xc02bd0: ArrayStore: r1[1] = r0  ; List_4
    //     0xc02bd0: add             x25, x1, #0x13
    //     0xc02bd4: str             w0, [x25]
    //     0xc02bd8: tbz             w0, #0, #0xc02bf4
    //     0xc02bdc: ldurb           w16, [x1, #-1]
    //     0xc02be0: ldurb           w17, [x0, #-1]
    //     0xc02be4: and             x16, x17, x16, lsr #2
    //     0xc02be8: tst             x16, HEAP, lsr #32
    //     0xc02bec: b.eq            #0xc02bf4
    //     0xc02bf0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc02bf4: ldur            x0, [fp, #-0x18]
    // 0xc02bf8: r16 = "% OFF"
    //     0xc02bf8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xc02bfc: ldr             x16, [x16, #0xd98]
    // 0xc02c00: ArrayStore: r0[0] = r16  ; List_4
    //     0xc02c00: stur            w16, [x0, #0x17]
    // 0xc02c04: str             x0, [SP]
    // 0xc02c08: r0 = _interpolate()
    //     0xc02c08: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc02c0c: mov             x3, x0
    // 0xc02c10: b               #0xc02c18
    // 0xc02c14: r3 = ""
    //     0xc02c14: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc02c18: ldur            x1, [fp, #-8]
    // 0xc02c1c: ldur            x2, [fp, #-0x10]
    // 0xc02c20: ldur            x0, [fp, #-0x30]
    // 0xc02c24: stur            x3, [fp, #-0x18]
    // 0xc02c28: LoadField: r4 = r1->field_f
    //     0xc02c28: ldur            w4, [x1, #0xf]
    // 0xc02c2c: DecompressPointer r4
    //     0xc02c2c: add             x4, x4, HEAP, lsl #32
    // 0xc02c30: cmp             w4, NULL
    // 0xc02c34: b.eq            #0xc02d78
    // 0xc02c38: mov             x1, x4
    // 0xc02c3c: r0 = of()
    //     0xc02c3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc02c40: LoadField: r1 = r0->field_87
    //     0xc02c40: ldur            w1, [x0, #0x87]
    // 0xc02c44: DecompressPointer r1
    //     0xc02c44: add             x1, x1, HEAP, lsl #32
    // 0xc02c48: LoadField: r0 = r1->field_2b
    //     0xc02c48: ldur            w0, [x1, #0x2b]
    // 0xc02c4c: DecompressPointer r0
    //     0xc02c4c: add             x0, x0, HEAP, lsl #32
    // 0xc02c50: r16 = 12.000000
    //     0xc02c50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc02c54: ldr             x16, [x16, #0x9e8]
    // 0xc02c58: r30 = Instance_Color
    //     0xc02c58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc02c5c: ldr             lr, [lr, #0x858]
    // 0xc02c60: stp             lr, x16, [SP]
    // 0xc02c64: mov             x1, x0
    // 0xc02c68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc02c68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc02c6c: ldr             x4, [x4, #0xaa0]
    // 0xc02c70: r0 = copyWith()
    //     0xc02c70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc02c74: stur            x0, [fp, #-8]
    // 0xc02c78: r0 = TextSpan()
    //     0xc02c78: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc02c7c: mov             x3, x0
    // 0xc02c80: ldur            x0, [fp, #-0x18]
    // 0xc02c84: stur            x3, [fp, #-0x20]
    // 0xc02c88: StoreField: r3->field_b = r0
    //     0xc02c88: stur            w0, [x3, #0xb]
    // 0xc02c8c: r0 = Instance__DeferringMouseCursor
    //     0xc02c8c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc02c90: ArrayStore: r3[0] = r0  ; List_4
    //     0xc02c90: stur            w0, [x3, #0x17]
    // 0xc02c94: ldur            x1, [fp, #-8]
    // 0xc02c98: StoreField: r3->field_7 = r1
    //     0xc02c98: stur            w1, [x3, #7]
    // 0xc02c9c: r1 = Null
    //     0xc02c9c: mov             x1, NULL
    // 0xc02ca0: r2 = 6
    //     0xc02ca0: movz            x2, #0x6
    // 0xc02ca4: r0 = AllocateArray()
    //     0xc02ca4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc02ca8: mov             x2, x0
    // 0xc02cac: ldur            x0, [fp, #-0x10]
    // 0xc02cb0: stur            x2, [fp, #-8]
    // 0xc02cb4: StoreField: r2->field_f = r0
    //     0xc02cb4: stur            w0, [x2, #0xf]
    // 0xc02cb8: ldur            x0, [fp, #-0x30]
    // 0xc02cbc: StoreField: r2->field_13 = r0
    //     0xc02cbc: stur            w0, [x2, #0x13]
    // 0xc02cc0: ldur            x0, [fp, #-0x20]
    // 0xc02cc4: ArrayStore: r2[0] = r0  ; List_4
    //     0xc02cc4: stur            w0, [x2, #0x17]
    // 0xc02cc8: r1 = <InlineSpan>
    //     0xc02cc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xc02ccc: ldr             x1, [x1, #0xe40]
    // 0xc02cd0: r0 = AllocateGrowableArray()
    //     0xc02cd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc02cd4: mov             x1, x0
    // 0xc02cd8: ldur            x0, [fp, #-8]
    // 0xc02cdc: stur            x1, [fp, #-0x10]
    // 0xc02ce0: StoreField: r1->field_f = r0
    //     0xc02ce0: stur            w0, [x1, #0xf]
    // 0xc02ce4: r0 = 6
    //     0xc02ce4: movz            x0, #0x6
    // 0xc02ce8: StoreField: r1->field_b = r0
    //     0xc02ce8: stur            w0, [x1, #0xb]
    // 0xc02cec: r0 = TextSpan()
    //     0xc02cec: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc02cf0: mov             x1, x0
    // 0xc02cf4: ldur            x0, [fp, #-0x10]
    // 0xc02cf8: stur            x1, [fp, #-8]
    // 0xc02cfc: StoreField: r1->field_f = r0
    //     0xc02cfc: stur            w0, [x1, #0xf]
    // 0xc02d00: r0 = Instance__DeferringMouseCursor
    //     0xc02d00: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc02d04: ArrayStore: r1[0] = r0  ; List_4
    //     0xc02d04: stur            w0, [x1, #0x17]
    // 0xc02d08: r0 = RichText()
    //     0xc02d08: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xc02d0c: mov             x1, x0
    // 0xc02d10: ldur            x2, [fp, #-8]
    // 0xc02d14: stur            x0, [fp, #-8]
    // 0xc02d18: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc02d18: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc02d1c: r0 = RichText()
    //     0xc02d1c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xc02d20: r0 = SizedBox()
    //     0xc02d20: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc02d24: mov             x1, x0
    // 0xc02d28: r0 = 32.000000
    //     0xc02d28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xc02d2c: ldr             x0, [x0, #0x848]
    // 0xc02d30: stur            x1, [fp, #-0x10]
    // 0xc02d34: StoreField: r1->field_13 = r0
    //     0xc02d34: stur            w0, [x1, #0x13]
    // 0xc02d38: ldur            x0, [fp, #-8]
    // 0xc02d3c: StoreField: r1->field_b = r0
    //     0xc02d3c: stur            w0, [x1, #0xb]
    // 0xc02d40: r0 = Padding()
    //     0xc02d40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc02d44: r1 = Instance_EdgeInsets
    //     0xc02d44: add             x1, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xc02d48: ldr             x1, [x1, #0x770]
    // 0xc02d4c: StoreField: r0->field_f = r1
    //     0xc02d4c: stur            w1, [x0, #0xf]
    // 0xc02d50: ldur            x1, [fp, #-0x10]
    // 0xc02d54: StoreField: r0->field_b = r1
    //     0xc02d54: stur            w1, [x0, #0xb]
    // 0xc02d58: LeaveFrame
    //     0xc02d58: mov             SP, fp
    //     0xc02d5c: ldp             fp, lr, [SP], #0x10
    // 0xc02d60: ret
    //     0xc02d60: ret             
    // 0xc02d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc02d64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc02d68: b               #0xc0292c
    // 0xc02d6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02d6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02d70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02d70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02d74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02d74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc02d78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc02d78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingSection(/* No info */) {
    // ** addr: 0xc02d7c, size: 0x944
    // 0xc02d7c: EnterFrame
    //     0xc02d7c: stp             fp, lr, [SP, #-0x10]!
    //     0xc02d80: mov             fp, SP
    // 0xc02d84: AllocStack(0x50)
    //     0xc02d84: sub             SP, SP, #0x50
    // 0xc02d88: SetupParameters(_ProductGridItemState this /* r1 => r3, fp-0x8 */)
    //     0xc02d88: mov             x3, x1
    //     0xc02d8c: stur            x1, [fp, #-8]
    // 0xc02d90: CheckStackOverflow
    //     0xc02d90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc02d94: cmp             SP, x16
    //     0xc02d98: b.ls            #0xc03674
    // 0xc02d9c: LoadField: r0 = r3->field_b
    //     0xc02d9c: ldur            w0, [x3, #0xb]
    // 0xc02da0: DecompressPointer r0
    //     0xc02da0: add             x0, x0, HEAP, lsl #32
    // 0xc02da4: cmp             w0, NULL
    // 0xc02da8: b.eq            #0xc0367c
    // 0xc02dac: LoadField: r4 = r0->field_b
    //     0xc02dac: ldur            w4, [x0, #0xb]
    // 0xc02db0: DecompressPointer r4
    //     0xc02db0: add             x4, x4, HEAP, lsl #32
    // 0xc02db4: r0 = BoxInt64Instr(r2)
    //     0xc02db4: sbfiz           x0, x2, #1, #0x1f
    //     0xc02db8: cmp             x2, x0, asr #1
    //     0xc02dbc: b.eq            #0xc02dc8
    //     0xc02dc0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc02dc4: stur            x2, [x0, #7]
    // 0xc02dc8: r1 = LoadClassIdInstr(r4)
    //     0xc02dc8: ldur            x1, [x4, #-1]
    //     0xc02dcc: ubfx            x1, x1, #0xc, #0x14
    // 0xc02dd0: stp             x0, x4, [SP]
    // 0xc02dd4: mov             x0, x1
    // 0xc02dd8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc02dd8: sub             lr, x0, #0xb7
    //     0xc02ddc: ldr             lr, [x21, lr, lsl #3]
    //     0xc02de0: blr             lr
    // 0xc02de4: mov             x1, x0
    // 0xc02de8: stur            x1, [fp, #-0x10]
    // 0xc02dec: cmp             w1, NULL
    // 0xc02df0: b.ne            #0xc02e04
    // 0xc02df4: r0 = Instance_SizedBox
    //     0xc02df4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc02df8: LeaveFrame
    //     0xc02df8: mov             SP, fp
    //     0xc02dfc: ldp             fp, lr, [SP], #0x10
    // 0xc02e00: ret
    //     0xc02e00: ret             
    // 0xc02e04: LoadField: r0 = r1->field_e3
    //     0xc02e04: ldur            w0, [x1, #0xe3]
    // 0xc02e08: DecompressPointer r0
    //     0xc02e08: add             x0, x0, HEAP, lsl #32
    // 0xc02e0c: cmp             w0, NULL
    // 0xc02e10: b.ne            #0xc02e1c
    // 0xc02e14: r0 = Null
    //     0xc02e14: mov             x0, NULL
    // 0xc02e18: b               #0xc02e28
    // 0xc02e1c: LoadField: r2 = r0->field_7
    //     0xc02e1c: ldur            w2, [x0, #7]
    // 0xc02e20: DecompressPointer r2
    //     0xc02e20: add             x2, x2, HEAP, lsl #32
    // 0xc02e24: mov             x0, x2
    // 0xc02e28: r2 = LoadClassIdInstr(r0)
    //     0xc02e28: ldur            x2, [x0, #-1]
    //     0xc02e2c: ubfx            x2, x2, #0xc, #0x14
    // 0xc02e30: r16 = 0.000000
    //     0xc02e30: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc02e34: stp             x16, x0, [SP]
    // 0xc02e38: mov             x0, x2
    // 0xc02e3c: mov             lr, x0
    // 0xc02e40: ldr             lr, [x21, lr, lsl #3]
    // 0xc02e44: blr             lr
    // 0xc02e48: eor             x1, x0, #0x10
    // 0xc02e4c: tbz             w1, #4, #0xc02e60
    // 0xc02e50: r0 = Instance_SizedBox
    //     0xc02e50: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc02e54: LeaveFrame
    //     0xc02e54: mov             SP, fp
    //     0xc02e58: ldp             fp, lr, [SP], #0x10
    // 0xc02e5c: ret
    //     0xc02e5c: ret             
    // 0xc02e60: ldur            x1, [fp, #-0x10]
    // 0xc02e64: LoadField: r0 = r1->field_e3
    //     0xc02e64: ldur            w0, [x1, #0xe3]
    // 0xc02e68: DecompressPointer r0
    //     0xc02e68: add             x0, x0, HEAP, lsl #32
    // 0xc02e6c: cmp             w0, NULL
    // 0xc02e70: b.ne            #0xc02e7c
    // 0xc02e74: r0 = Null
    //     0xc02e74: mov             x0, NULL
    // 0xc02e78: b               #0xc02e88
    // 0xc02e7c: LoadField: r2 = r0->field_f
    //     0xc02e7c: ldur            w2, [x0, #0xf]
    // 0xc02e80: DecompressPointer r2
    //     0xc02e80: add             x2, x2, HEAP, lsl #32
    // 0xc02e84: mov             x0, x2
    // 0xc02e88: r2 = LoadClassIdInstr(r0)
    //     0xc02e88: ldur            x2, [x0, #-1]
    //     0xc02e8c: ubfx            x2, x2, #0xc, #0x14
    // 0xc02e90: r16 = "product_rating"
    //     0xc02e90: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xc02e94: ldr             x16, [x16, #0xf20]
    // 0xc02e98: stp             x16, x0, [SP]
    // 0xc02e9c: mov             x0, x2
    // 0xc02ea0: mov             lr, x0
    // 0xc02ea4: ldr             lr, [x21, lr, lsl #3]
    // 0xc02ea8: blr             lr
    // 0xc02eac: tbnz            w0, #4, #0xc03348
    // 0xc02eb0: ldur            x0, [fp, #-0x10]
    // 0xc02eb4: LoadField: r1 = r0->field_e3
    //     0xc02eb4: ldur            w1, [x0, #0xe3]
    // 0xc02eb8: DecompressPointer r1
    //     0xc02eb8: add             x1, x1, HEAP, lsl #32
    // 0xc02ebc: cmp             w1, NULL
    // 0xc02ec0: b.ne            #0xc02ecc
    // 0xc02ec4: r2 = Null
    //     0xc02ec4: mov             x2, NULL
    // 0xc02ec8: b               #0xc02ed4
    // 0xc02ecc: LoadField: r2 = r1->field_7
    //     0xc02ecc: ldur            w2, [x1, #7]
    // 0xc02ed0: DecompressPointer r2
    //     0xc02ed0: add             x2, x2, HEAP, lsl #32
    // 0xc02ed4: cmp             w2, NULL
    // 0xc02ed8: r16 = true
    //     0xc02ed8: add             x16, NULL, #0x20  ; true
    // 0xc02edc: r17 = false
    //     0xc02edc: add             x17, NULL, #0x30  ; false
    // 0xc02ee0: csel            x3, x16, x17, ne
    // 0xc02ee4: stur            x3, [fp, #-0x18]
    // 0xc02ee8: cmp             w1, NULL
    // 0xc02eec: b.ne            #0xc02ef8
    // 0xc02ef0: r2 = Null
    //     0xc02ef0: mov             x2, NULL
    // 0xc02ef4: b               #0xc02f00
    // 0xc02ef8: LoadField: r2 = r1->field_7
    //     0xc02ef8: ldur            w2, [x1, #7]
    // 0xc02efc: DecompressPointer r2
    //     0xc02efc: add             x2, x2, HEAP, lsl #32
    // 0xc02f00: cmp             w2, NULL
    // 0xc02f04: b.ne            #0xc02f10
    // 0xc02f08: d1 = 0.000000
    //     0xc02f08: eor             v1.16b, v1.16b, v1.16b
    // 0xc02f0c: b               #0xc02f18
    // 0xc02f10: LoadField: d0 = r2->field_7
    //     0xc02f10: ldur            d0, [x2, #7]
    // 0xc02f14: mov             v1.16b, v0.16b
    // 0xc02f18: d0 = 4.000000
    //     0xc02f18: fmov            d0, #4.00000000
    // 0xc02f1c: fcmp            d1, d0
    // 0xc02f20: b.lt            #0xc02f30
    // 0xc02f24: r1 = Instance_Color
    //     0xc02f24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc02f28: ldr             x1, [x1, #0x858]
    // 0xc02f2c: b               #0xc02fdc
    // 0xc02f30: cmp             w1, NULL
    // 0xc02f34: b.ne            #0xc02f40
    // 0xc02f38: r2 = Null
    //     0xc02f38: mov             x2, NULL
    // 0xc02f3c: b               #0xc02f48
    // 0xc02f40: LoadField: r2 = r1->field_7
    //     0xc02f40: ldur            w2, [x1, #7]
    // 0xc02f44: DecompressPointer r2
    //     0xc02f44: add             x2, x2, HEAP, lsl #32
    // 0xc02f48: cmp             w2, NULL
    // 0xc02f4c: b.ne            #0xc02f58
    // 0xc02f50: d1 = 0.000000
    //     0xc02f50: eor             v1.16b, v1.16b, v1.16b
    // 0xc02f54: b               #0xc02f60
    // 0xc02f58: LoadField: d0 = r2->field_7
    //     0xc02f58: ldur            d0, [x2, #7]
    // 0xc02f5c: mov             v1.16b, v0.16b
    // 0xc02f60: d0 = 3.500000
    //     0xc02f60: fmov            d0, #3.50000000
    // 0xc02f64: fcmp            d1, d0
    // 0xc02f68: b.lt            #0xc02f84
    // 0xc02f6c: r1 = Instance_Color
    //     0xc02f6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc02f70: ldr             x1, [x1, #0x858]
    // 0xc02f74: d0 = 0.700000
    //     0xc02f74: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc02f78: ldr             d0, [x17, #0xf48]
    // 0xc02f7c: r0 = withOpacity()
    //     0xc02f7c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc02f80: b               #0xc02fd4
    // 0xc02f84: cmp             w1, NULL
    // 0xc02f88: b.ne            #0xc02f94
    // 0xc02f8c: r0 = Null
    //     0xc02f8c: mov             x0, NULL
    // 0xc02f90: b               #0xc02f9c
    // 0xc02f94: LoadField: r0 = r1->field_7
    //     0xc02f94: ldur            w0, [x1, #7]
    // 0xc02f98: DecompressPointer r0
    //     0xc02f98: add             x0, x0, HEAP, lsl #32
    // 0xc02f9c: cmp             w0, NULL
    // 0xc02fa0: b.ne            #0xc02fac
    // 0xc02fa4: d1 = 0.000000
    //     0xc02fa4: eor             v1.16b, v1.16b, v1.16b
    // 0xc02fa8: b               #0xc02fb4
    // 0xc02fac: LoadField: d0 = r0->field_7
    //     0xc02fac: ldur            d0, [x0, #7]
    // 0xc02fb0: mov             v1.16b, v0.16b
    // 0xc02fb4: d0 = 2.000000
    //     0xc02fb4: fmov            d0, #2.00000000
    // 0xc02fb8: fcmp            d1, d0
    // 0xc02fbc: b.lt            #0xc02fcc
    // 0xc02fc0: r0 = Instance_Color
    //     0xc02fc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xc02fc4: ldr             x0, [x0, #0x860]
    // 0xc02fc8: b               #0xc02fd4
    // 0xc02fcc: r0 = Instance_Color
    //     0xc02fcc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xc02fd0: ldr             x0, [x0, #0x50]
    // 0xc02fd4: mov             x1, x0
    // 0xc02fd8: ldur            x0, [fp, #-0x10]
    // 0xc02fdc: stur            x1, [fp, #-0x20]
    // 0xc02fe0: r0 = ColorFilter()
    //     0xc02fe0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc02fe4: mov             x1, x0
    // 0xc02fe8: ldur            x0, [fp, #-0x20]
    // 0xc02fec: stur            x1, [fp, #-0x28]
    // 0xc02ff0: StoreField: r1->field_7 = r0
    //     0xc02ff0: stur            w0, [x1, #7]
    // 0xc02ff4: r0 = Instance_BlendMode
    //     0xc02ff4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc02ff8: ldr             x0, [x0, #0xb30]
    // 0xc02ffc: StoreField: r1->field_b = r0
    //     0xc02ffc: stur            w0, [x1, #0xb]
    // 0xc03000: r2 = 1
    //     0xc03000: movz            x2, #0x1
    // 0xc03004: StoreField: r1->field_13 = r2
    //     0xc03004: stur            x2, [x1, #0x13]
    // 0xc03008: r0 = SvgPicture()
    //     0xc03008: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc0300c: stur            x0, [fp, #-0x20]
    // 0xc03010: ldur            x16, [fp, #-0x28]
    // 0xc03014: str             x16, [SP]
    // 0xc03018: mov             x1, x0
    // 0xc0301c: r2 = "assets/images/green_star.svg"
    //     0xc0301c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xc03020: ldr             x2, [x2, #0x9a0]
    // 0xc03024: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc03024: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc03028: ldr             x4, [x4, #0xa38]
    // 0xc0302c: r0 = SvgPicture.asset()
    //     0xc0302c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc03030: ldur            x0, [fp, #-0x10]
    // 0xc03034: LoadField: r1 = r0->field_e3
    //     0xc03034: ldur            w1, [x0, #0xe3]
    // 0xc03038: DecompressPointer r1
    //     0xc03038: add             x1, x1, HEAP, lsl #32
    // 0xc0303c: cmp             w1, NULL
    // 0xc03040: b.ne            #0xc0304c
    // 0xc03044: r0 = Null
    //     0xc03044: mov             x0, NULL
    // 0xc03048: b               #0xc0306c
    // 0xc0304c: LoadField: r2 = r1->field_7
    //     0xc0304c: ldur            w2, [x1, #7]
    // 0xc03050: DecompressPointer r2
    //     0xc03050: add             x2, x2, HEAP, lsl #32
    // 0xc03054: cmp             w2, NULL
    // 0xc03058: b.ne            #0xc03064
    // 0xc0305c: r0 = Null
    //     0xc0305c: mov             x0, NULL
    // 0xc03060: b               #0xc0306c
    // 0xc03064: str             x2, [SP]
    // 0xc03068: r0 = toString()
    //     0xc03068: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xc0306c: cmp             w0, NULL
    // 0xc03070: b.ne            #0xc0307c
    // 0xc03074: r4 = ""
    //     0xc03074: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03078: b               #0xc03080
    // 0xc0307c: mov             x4, x0
    // 0xc03080: ldur            x3, [fp, #-8]
    // 0xc03084: ldur            x0, [fp, #-0x10]
    // 0xc03088: ldur            x2, [fp, #-0x20]
    // 0xc0308c: stur            x4, [fp, #-0x28]
    // 0xc03090: LoadField: r1 = r3->field_f
    //     0xc03090: ldur            w1, [x3, #0xf]
    // 0xc03094: DecompressPointer r1
    //     0xc03094: add             x1, x1, HEAP, lsl #32
    // 0xc03098: cmp             w1, NULL
    // 0xc0309c: b.eq            #0xc03680
    // 0xc030a0: r0 = of()
    //     0xc030a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc030a4: LoadField: r1 = r0->field_87
    //     0xc030a4: ldur            w1, [x0, #0x87]
    // 0xc030a8: DecompressPointer r1
    //     0xc030a8: add             x1, x1, HEAP, lsl #32
    // 0xc030ac: LoadField: r0 = r1->field_7
    //     0xc030ac: ldur            w0, [x1, #7]
    // 0xc030b0: DecompressPointer r0
    //     0xc030b0: add             x0, x0, HEAP, lsl #32
    // 0xc030b4: r16 = 12.000000
    //     0xc030b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc030b8: ldr             x16, [x16, #0x9e8]
    // 0xc030bc: r30 = Instance_Color
    //     0xc030bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc030c0: stp             lr, x16, [SP]
    // 0xc030c4: mov             x1, x0
    // 0xc030c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc030c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc030cc: ldr             x4, [x4, #0xaa0]
    // 0xc030d0: r0 = copyWith()
    //     0xc030d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc030d4: stur            x0, [fp, #-0x30]
    // 0xc030d8: r0 = Text()
    //     0xc030d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc030dc: mov             x3, x0
    // 0xc030e0: ldur            x0, [fp, #-0x28]
    // 0xc030e4: stur            x3, [fp, #-0x38]
    // 0xc030e8: StoreField: r3->field_b = r0
    //     0xc030e8: stur            w0, [x3, #0xb]
    // 0xc030ec: ldur            x0, [fp, #-0x30]
    // 0xc030f0: StoreField: r3->field_13 = r0
    //     0xc030f0: stur            w0, [x3, #0x13]
    // 0xc030f4: r1 = Null
    //     0xc030f4: mov             x1, NULL
    // 0xc030f8: r2 = 4
    //     0xc030f8: movz            x2, #0x4
    // 0xc030fc: r0 = AllocateArray()
    //     0xc030fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc03100: mov             x2, x0
    // 0xc03104: ldur            x0, [fp, #-0x20]
    // 0xc03108: stur            x2, [fp, #-0x28]
    // 0xc0310c: StoreField: r2->field_f = r0
    //     0xc0310c: stur            w0, [x2, #0xf]
    // 0xc03110: ldur            x0, [fp, #-0x38]
    // 0xc03114: StoreField: r2->field_13 = r0
    //     0xc03114: stur            w0, [x2, #0x13]
    // 0xc03118: r1 = <Widget>
    //     0xc03118: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0311c: r0 = AllocateGrowableArray()
    //     0xc0311c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc03120: mov             x3, x0
    // 0xc03124: ldur            x0, [fp, #-0x28]
    // 0xc03128: stur            x3, [fp, #-0x30]
    // 0xc0312c: StoreField: r3->field_f = r0
    //     0xc0312c: stur            w0, [x3, #0xf]
    // 0xc03130: r0 = 4
    //     0xc03130: movz            x0, #0x4
    // 0xc03134: StoreField: r3->field_b = r0
    //     0xc03134: stur            w0, [x3, #0xb]
    // 0xc03138: ldur            x4, [fp, #-0x10]
    // 0xc0313c: LoadField: r0 = r4->field_e3
    //     0xc0313c: ldur            w0, [x4, #0xe3]
    // 0xc03140: DecompressPointer r0
    //     0xc03140: add             x0, x0, HEAP, lsl #32
    // 0xc03144: cmp             w0, NULL
    // 0xc03148: b.ne            #0xc03154
    // 0xc0314c: mov             x2, x3
    // 0xc03150: b               #0xc032a8
    // 0xc03154: LoadField: r4 = r0->field_b
    //     0xc03154: ldur            w4, [x0, #0xb]
    // 0xc03158: DecompressPointer r4
    //     0xc03158: add             x4, x4, HEAP, lsl #32
    // 0xc0315c: stur            x4, [fp, #-0x20]
    // 0xc03160: cmp             w4, NULL
    // 0xc03164: b.eq            #0xc032a4
    // 0xc03168: ldur            x0, [fp, #-8]
    // 0xc0316c: r1 = Null
    //     0xc0316c: mov             x1, NULL
    // 0xc03170: r2 = 6
    //     0xc03170: movz            x2, #0x6
    // 0xc03174: r0 = AllocateArray()
    //     0xc03174: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc03178: r16 = " | ("
    //     0xc03178: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xc0317c: ldr             x16, [x16, #0xd70]
    // 0xc03180: StoreField: r0->field_f = r16
    //     0xc03180: stur            w16, [x0, #0xf]
    // 0xc03184: ldur            x1, [fp, #-0x20]
    // 0xc03188: LoadField: d0 = r1->field_7
    //     0xc03188: ldur            d0, [x1, #7]
    // 0xc0318c: fcmp            d0, d0
    // 0xc03190: b.vs            #0xc03684
    // 0xc03194: fcvtzs          x1, d0
    // 0xc03198: asr             x16, x1, #0x1e
    // 0xc0319c: cmp             x16, x1, asr #63
    // 0xc031a0: b.ne            #0xc03684
    // 0xc031a4: lsl             x1, x1, #1
    // 0xc031a8: StoreField: r0->field_13 = r1
    //     0xc031a8: stur            w1, [x0, #0x13]
    // 0xc031ac: r16 = ")"
    //     0xc031ac: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xc031b0: ArrayStore: r0[0] = r16  ; List_4
    //     0xc031b0: stur            w16, [x0, #0x17]
    // 0xc031b4: str             x0, [SP]
    // 0xc031b8: r0 = _interpolate()
    //     0xc031b8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc031bc: ldur            x3, [fp, #-8]
    // 0xc031c0: stur            x0, [fp, #-0x20]
    // 0xc031c4: LoadField: r1 = r3->field_f
    //     0xc031c4: ldur            w1, [x3, #0xf]
    // 0xc031c8: DecompressPointer r1
    //     0xc031c8: add             x1, x1, HEAP, lsl #32
    // 0xc031cc: cmp             w1, NULL
    // 0xc031d0: b.eq            #0xc036ac
    // 0xc031d4: r0 = of()
    //     0xc031d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc031d8: LoadField: r1 = r0->field_87
    //     0xc031d8: ldur            w1, [x0, #0x87]
    // 0xc031dc: DecompressPointer r1
    //     0xc031dc: add             x1, x1, HEAP, lsl #32
    // 0xc031e0: LoadField: r0 = r1->field_2b
    //     0xc031e0: ldur            w0, [x1, #0x2b]
    // 0xc031e4: DecompressPointer r0
    //     0xc031e4: add             x0, x0, HEAP, lsl #32
    // 0xc031e8: r16 = 12.000000
    //     0xc031e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc031ec: ldr             x16, [x16, #0x9e8]
    // 0xc031f0: r30 = Instance_Color
    //     0xc031f0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc031f4: stp             lr, x16, [SP]
    // 0xc031f8: mov             x1, x0
    // 0xc031fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc031fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc03200: ldr             x4, [x4, #0xaa0]
    // 0xc03204: r0 = copyWith()
    //     0xc03204: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc03208: stur            x0, [fp, #-0x28]
    // 0xc0320c: r0 = Text()
    //     0xc0320c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc03210: mov             x2, x0
    // 0xc03214: ldur            x0, [fp, #-0x20]
    // 0xc03218: stur            x2, [fp, #-0x38]
    // 0xc0321c: StoreField: r2->field_b = r0
    //     0xc0321c: stur            w0, [x2, #0xb]
    // 0xc03220: ldur            x0, [fp, #-0x28]
    // 0xc03224: StoreField: r2->field_13 = r0
    //     0xc03224: stur            w0, [x2, #0x13]
    // 0xc03228: ldur            x0, [fp, #-0x30]
    // 0xc0322c: LoadField: r1 = r0->field_b
    //     0xc0322c: ldur            w1, [x0, #0xb]
    // 0xc03230: LoadField: r3 = r0->field_f
    //     0xc03230: ldur            w3, [x0, #0xf]
    // 0xc03234: DecompressPointer r3
    //     0xc03234: add             x3, x3, HEAP, lsl #32
    // 0xc03238: LoadField: r4 = r3->field_b
    //     0xc03238: ldur            w4, [x3, #0xb]
    // 0xc0323c: r3 = LoadInt32Instr(r1)
    //     0xc0323c: sbfx            x3, x1, #1, #0x1f
    // 0xc03240: stur            x3, [fp, #-0x40]
    // 0xc03244: r1 = LoadInt32Instr(r4)
    //     0xc03244: sbfx            x1, x4, #1, #0x1f
    // 0xc03248: cmp             x3, x1
    // 0xc0324c: b.ne            #0xc03258
    // 0xc03250: mov             x1, x0
    // 0xc03254: r0 = _growToNextCapacity()
    //     0xc03254: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc03258: ldur            x2, [fp, #-0x30]
    // 0xc0325c: ldur            x3, [fp, #-0x40]
    // 0xc03260: add             x0, x3, #1
    // 0xc03264: lsl             x1, x0, #1
    // 0xc03268: StoreField: r2->field_b = r1
    //     0xc03268: stur            w1, [x2, #0xb]
    // 0xc0326c: LoadField: r1 = r2->field_f
    //     0xc0326c: ldur            w1, [x2, #0xf]
    // 0xc03270: DecompressPointer r1
    //     0xc03270: add             x1, x1, HEAP, lsl #32
    // 0xc03274: ldur            x0, [fp, #-0x38]
    // 0xc03278: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc03278: add             x25, x1, x3, lsl #2
    //     0xc0327c: add             x25, x25, #0xf
    //     0xc03280: str             w0, [x25]
    //     0xc03284: tbz             w0, #0, #0xc032a0
    //     0xc03288: ldurb           w16, [x1, #-1]
    //     0xc0328c: ldurb           w17, [x0, #-1]
    //     0xc03290: and             x16, x17, x16, lsr #2
    //     0xc03294: tst             x16, HEAP, lsr #32
    //     0xc03298: b.eq            #0xc032a0
    //     0xc0329c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc032a0: b               #0xc032a8
    // 0xc032a4: mov             x2, x3
    // 0xc032a8: ldur            x0, [fp, #-0x18]
    // 0xc032ac: r0 = Row()
    //     0xc032ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc032b0: r5 = Instance_Axis
    //     0xc032b0: ldr             x5, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc032b4: stur            x0, [fp, #-0x20]
    // 0xc032b8: StoreField: r0->field_f = r5
    //     0xc032b8: stur            w5, [x0, #0xf]
    // 0xc032bc: r6 = Instance_MainAxisAlignment
    //     0xc032bc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc032c0: ldr             x6, [x6, #0xa08]
    // 0xc032c4: StoreField: r0->field_13 = r6
    //     0xc032c4: stur            w6, [x0, #0x13]
    // 0xc032c8: r7 = Instance_MainAxisSize
    //     0xc032c8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc032cc: ldr             x7, [x7, #0xa10]
    // 0xc032d0: ArrayStore: r0[0] = r7  ; List_4
    //     0xc032d0: stur            w7, [x0, #0x17]
    // 0xc032d4: r8 = Instance_CrossAxisAlignment
    //     0xc032d4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc032d8: ldr             x8, [x8, #0xa18]
    // 0xc032dc: StoreField: r0->field_1b = r8
    //     0xc032dc: stur            w8, [x0, #0x1b]
    // 0xc032e0: r9 = Instance_VerticalDirection
    //     0xc032e0: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc032e4: ldr             x9, [x9, #0xa20]
    // 0xc032e8: StoreField: r0->field_23 = r9
    //     0xc032e8: stur            w9, [x0, #0x23]
    // 0xc032ec: r10 = Instance_Clip
    //     0xc032ec: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc032f0: ldr             x10, [x10, #0x38]
    // 0xc032f4: StoreField: r0->field_2b = r10
    //     0xc032f4: stur            w10, [x0, #0x2b]
    // 0xc032f8: StoreField: r0->field_2f = rZR
    //     0xc032f8: stur            xzr, [x0, #0x2f]
    // 0xc032fc: ldur            x1, [fp, #-0x30]
    // 0xc03300: StoreField: r0->field_b = r1
    //     0xc03300: stur            w1, [x0, #0xb]
    // 0xc03304: r0 = Visibility()
    //     0xc03304: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc03308: mov             x1, x0
    // 0xc0330c: ldur            x0, [fp, #-0x20]
    // 0xc03310: StoreField: r1->field_b = r0
    //     0xc03310: stur            w0, [x1, #0xb]
    // 0xc03314: r11 = Instance_SizedBox
    //     0xc03314: ldr             x11, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc03318: StoreField: r1->field_f = r11
    //     0xc03318: stur            w11, [x1, #0xf]
    // 0xc0331c: ldur            x0, [fp, #-0x18]
    // 0xc03320: StoreField: r1->field_13 = r0
    //     0xc03320: stur            w0, [x1, #0x13]
    // 0xc03324: r12 = false
    //     0xc03324: add             x12, NULL, #0x30  ; false
    // 0xc03328: ArrayStore: r1[0] = r12  ; List_4
    //     0xc03328: stur            w12, [x1, #0x17]
    // 0xc0332c: StoreField: r1->field_1b = r12
    //     0xc0332c: stur            w12, [x1, #0x1b]
    // 0xc03330: StoreField: r1->field_1f = r12
    //     0xc03330: stur            w12, [x1, #0x1f]
    // 0xc03334: StoreField: r1->field_23 = r12
    //     0xc03334: stur            w12, [x1, #0x23]
    // 0xc03338: StoreField: r1->field_27 = r12
    //     0xc03338: stur            w12, [x1, #0x27]
    // 0xc0333c: StoreField: r1->field_2b = r12
    //     0xc0333c: stur            w12, [x1, #0x2b]
    // 0xc03340: mov             x0, x1
    // 0xc03344: b               #0xc03668
    // 0xc03348: ldur            x3, [fp, #-8]
    // 0xc0334c: ldur            x4, [fp, #-0x10]
    // 0xc03350: r11 = Instance_SizedBox
    //     0xc03350: ldr             x11, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc03354: r8 = Instance_CrossAxisAlignment
    //     0xc03354: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc03358: ldr             x8, [x8, #0xa18]
    // 0xc0335c: r6 = Instance_MainAxisAlignment
    //     0xc0335c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc03360: ldr             x6, [x6, #0xa08]
    // 0xc03364: r7 = Instance_MainAxisSize
    //     0xc03364: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc03368: ldr             x7, [x7, #0xa10]
    // 0xc0336c: r5 = Instance_Axis
    //     0xc0336c: ldr             x5, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc03370: r9 = Instance_VerticalDirection
    //     0xc03370: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc03374: ldr             x9, [x9, #0xa20]
    // 0xc03378: r12 = false
    //     0xc03378: add             x12, NULL, #0x30  ; false
    // 0xc0337c: r0 = Instance_BlendMode
    //     0xc0337c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc03380: ldr             x0, [x0, #0xb30]
    // 0xc03384: r10 = Instance_Clip
    //     0xc03384: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc03388: ldr             x10, [x10, #0x38]
    // 0xc0338c: r2 = 1
    //     0xc0338c: movz            x2, #0x1
    // 0xc03390: LoadField: r1 = r4->field_e3
    //     0xc03390: ldur            w1, [x4, #0xe3]
    // 0xc03394: DecompressPointer r1
    //     0xc03394: add             x1, x1, HEAP, lsl #32
    // 0xc03398: cmp             w1, NULL
    // 0xc0339c: b.ne            #0xc033a8
    // 0xc033a0: r1 = Null
    //     0xc033a0: mov             x1, NULL
    // 0xc033a4: b               #0xc033b4
    // 0xc033a8: LoadField: r13 = r1->field_7
    //     0xc033a8: ldur            w13, [x1, #7]
    // 0xc033ac: DecompressPointer r13
    //     0xc033ac: add             x13, x13, HEAP, lsl #32
    // 0xc033b0: mov             x1, x13
    // 0xc033b4: cmp             w1, NULL
    // 0xc033b8: r16 = true
    //     0xc033b8: add             x16, NULL, #0x20  ; true
    // 0xc033bc: r17 = false
    //     0xc033bc: add             x17, NULL, #0x30  ; false
    // 0xc033c0: csel            x13, x16, x17, ne
    // 0xc033c4: stur            x13, [fp, #-0x18]
    // 0xc033c8: LoadField: r1 = r3->field_f
    //     0xc033c8: ldur            w1, [x3, #0xf]
    // 0xc033cc: DecompressPointer r1
    //     0xc033cc: add             x1, x1, HEAP, lsl #32
    // 0xc033d0: cmp             w1, NULL
    // 0xc033d4: b.eq            #0xc036b0
    // 0xc033d8: r0 = of()
    //     0xc033d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc033dc: LoadField: r1 = r0->field_5b
    //     0xc033dc: ldur            w1, [x0, #0x5b]
    // 0xc033e0: DecompressPointer r1
    //     0xc033e0: add             x1, x1, HEAP, lsl #32
    // 0xc033e4: stur            x1, [fp, #-0x20]
    // 0xc033e8: r0 = ColorFilter()
    //     0xc033e8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc033ec: mov             x1, x0
    // 0xc033f0: ldur            x0, [fp, #-0x20]
    // 0xc033f4: stur            x1, [fp, #-0x28]
    // 0xc033f8: StoreField: r1->field_7 = r0
    //     0xc033f8: stur            w0, [x1, #7]
    // 0xc033fc: r0 = Instance_BlendMode
    //     0xc033fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc03400: ldr             x0, [x0, #0xb30]
    // 0xc03404: StoreField: r1->field_b = r0
    //     0xc03404: stur            w0, [x1, #0xb]
    // 0xc03408: r0 = 1
    //     0xc03408: movz            x0, #0x1
    // 0xc0340c: StoreField: r1->field_13 = r0
    //     0xc0340c: stur            x0, [x1, #0x13]
    // 0xc03410: r0 = SvgPicture()
    //     0xc03410: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc03414: stur            x0, [fp, #-0x20]
    // 0xc03418: ldur            x16, [fp, #-0x28]
    // 0xc0341c: str             x16, [SP]
    // 0xc03420: mov             x1, x0
    // 0xc03424: r2 = "assets/images/green_star.svg"
    //     0xc03424: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xc03428: ldr             x2, [x2, #0x9a0]
    // 0xc0342c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc0342c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc03430: ldr             x4, [x4, #0xa38]
    // 0xc03434: r0 = SvgPicture.asset()
    //     0xc03434: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc03438: ldur            x0, [fp, #-0x10]
    // 0xc0343c: LoadField: r1 = r0->field_e3
    //     0xc0343c: ldur            w1, [x0, #0xe3]
    // 0xc03440: DecompressPointer r1
    //     0xc03440: add             x1, x1, HEAP, lsl #32
    // 0xc03444: cmp             w1, NULL
    // 0xc03448: b.ne            #0xc03454
    // 0xc0344c: r0 = Null
    //     0xc0344c: mov             x0, NULL
    // 0xc03450: b               #0xc03474
    // 0xc03454: LoadField: r0 = r1->field_7
    //     0xc03454: ldur            w0, [x1, #7]
    // 0xc03458: DecompressPointer r0
    //     0xc03458: add             x0, x0, HEAP, lsl #32
    // 0xc0345c: cmp             w0, NULL
    // 0xc03460: b.ne            #0xc0346c
    // 0xc03464: r0 = Null
    //     0xc03464: mov             x0, NULL
    // 0xc03468: b               #0xc03474
    // 0xc0346c: str             x0, [SP]
    // 0xc03470: r0 = toString()
    //     0xc03470: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xc03474: cmp             w0, NULL
    // 0xc03478: b.ne            #0xc03484
    // 0xc0347c: r4 = ""
    //     0xc0347c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03480: b               #0xc03488
    // 0xc03484: mov             x4, x0
    // 0xc03488: ldur            x2, [fp, #-8]
    // 0xc0348c: ldur            x3, [fp, #-0x18]
    // 0xc03490: ldur            x0, [fp, #-0x20]
    // 0xc03494: stur            x4, [fp, #-0x10]
    // 0xc03498: LoadField: r1 = r2->field_f
    //     0xc03498: ldur            w1, [x2, #0xf]
    // 0xc0349c: DecompressPointer r1
    //     0xc0349c: add             x1, x1, HEAP, lsl #32
    // 0xc034a0: cmp             w1, NULL
    // 0xc034a4: b.eq            #0xc036b4
    // 0xc034a8: r0 = of()
    //     0xc034a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc034ac: LoadField: r1 = r0->field_87
    //     0xc034ac: ldur            w1, [x0, #0x87]
    // 0xc034b0: DecompressPointer r1
    //     0xc034b0: add             x1, x1, HEAP, lsl #32
    // 0xc034b4: LoadField: r0 = r1->field_7
    //     0xc034b4: ldur            w0, [x1, #7]
    // 0xc034b8: DecompressPointer r0
    //     0xc034b8: add             x0, x0, HEAP, lsl #32
    // 0xc034bc: r16 = 12.000000
    //     0xc034bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc034c0: ldr             x16, [x16, #0x9e8]
    // 0xc034c4: r30 = Instance_Color
    //     0xc034c4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc034c8: stp             lr, x16, [SP]
    // 0xc034cc: mov             x1, x0
    // 0xc034d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc034d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc034d4: ldr             x4, [x4, #0xaa0]
    // 0xc034d8: r0 = copyWith()
    //     0xc034d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc034dc: stur            x0, [fp, #-0x28]
    // 0xc034e0: r0 = Text()
    //     0xc034e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc034e4: mov             x2, x0
    // 0xc034e8: ldur            x0, [fp, #-0x10]
    // 0xc034ec: stur            x2, [fp, #-0x30]
    // 0xc034f0: StoreField: r2->field_b = r0
    //     0xc034f0: stur            w0, [x2, #0xb]
    // 0xc034f4: ldur            x0, [fp, #-0x28]
    // 0xc034f8: StoreField: r2->field_13 = r0
    //     0xc034f8: stur            w0, [x2, #0x13]
    // 0xc034fc: ldur            x0, [fp, #-8]
    // 0xc03500: LoadField: r1 = r0->field_f
    //     0xc03500: ldur            w1, [x0, #0xf]
    // 0xc03504: DecompressPointer r1
    //     0xc03504: add             x1, x1, HEAP, lsl #32
    // 0xc03508: cmp             w1, NULL
    // 0xc0350c: b.eq            #0xc036b8
    // 0xc03510: r0 = of()
    //     0xc03510: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc03514: LoadField: r1 = r0->field_87
    //     0xc03514: ldur            w1, [x0, #0x87]
    // 0xc03518: DecompressPointer r1
    //     0xc03518: add             x1, x1, HEAP, lsl #32
    // 0xc0351c: LoadField: r0 = r1->field_2b
    //     0xc0351c: ldur            w0, [x1, #0x2b]
    // 0xc03520: DecompressPointer r0
    //     0xc03520: add             x0, x0, HEAP, lsl #32
    // 0xc03524: ldur            x1, [fp, #-8]
    // 0xc03528: stur            x0, [fp, #-0x10]
    // 0xc0352c: LoadField: r2 = r1->field_f
    //     0xc0352c: ldur            w2, [x1, #0xf]
    // 0xc03530: DecompressPointer r2
    //     0xc03530: add             x2, x2, HEAP, lsl #32
    // 0xc03534: cmp             w2, NULL
    // 0xc03538: b.eq            #0xc036bc
    // 0xc0353c: mov             x1, x2
    // 0xc03540: r0 = of()
    //     0xc03540: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc03544: LoadField: r1 = r0->field_5b
    //     0xc03544: ldur            w1, [x0, #0x5b]
    // 0xc03548: DecompressPointer r1
    //     0xc03548: add             x1, x1, HEAP, lsl #32
    // 0xc0354c: r16 = 10.000000
    //     0xc0354c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc03550: stp             x1, x16, [SP]
    // 0xc03554: ldur            x1, [fp, #-0x10]
    // 0xc03558: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc03558: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0355c: ldr             x4, [x4, #0xaa0]
    // 0xc03560: r0 = copyWith()
    //     0xc03560: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc03564: stur            x0, [fp, #-8]
    // 0xc03568: r0 = Text()
    //     0xc03568: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0356c: mov             x3, x0
    // 0xc03570: r0 = " Brand Rating"
    //     0xc03570: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xc03574: ldr             x0, [x0, #0xd78]
    // 0xc03578: stur            x3, [fp, #-0x10]
    // 0xc0357c: StoreField: r3->field_b = r0
    //     0xc0357c: stur            w0, [x3, #0xb]
    // 0xc03580: ldur            x0, [fp, #-8]
    // 0xc03584: StoreField: r3->field_13 = r0
    //     0xc03584: stur            w0, [x3, #0x13]
    // 0xc03588: r1 = Null
    //     0xc03588: mov             x1, NULL
    // 0xc0358c: r2 = 6
    //     0xc0358c: movz            x2, #0x6
    // 0xc03590: r0 = AllocateArray()
    //     0xc03590: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc03594: mov             x2, x0
    // 0xc03598: ldur            x0, [fp, #-0x20]
    // 0xc0359c: stur            x2, [fp, #-8]
    // 0xc035a0: StoreField: r2->field_f = r0
    //     0xc035a0: stur            w0, [x2, #0xf]
    // 0xc035a4: ldur            x0, [fp, #-0x30]
    // 0xc035a8: StoreField: r2->field_13 = r0
    //     0xc035a8: stur            w0, [x2, #0x13]
    // 0xc035ac: ldur            x0, [fp, #-0x10]
    // 0xc035b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xc035b0: stur            w0, [x2, #0x17]
    // 0xc035b4: r1 = <Widget>
    //     0xc035b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc035b8: r0 = AllocateGrowableArray()
    //     0xc035b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc035bc: mov             x1, x0
    // 0xc035c0: ldur            x0, [fp, #-8]
    // 0xc035c4: stur            x1, [fp, #-0x10]
    // 0xc035c8: StoreField: r1->field_f = r0
    //     0xc035c8: stur            w0, [x1, #0xf]
    // 0xc035cc: r0 = 6
    //     0xc035cc: movz            x0, #0x6
    // 0xc035d0: StoreField: r1->field_b = r0
    //     0xc035d0: stur            w0, [x1, #0xb]
    // 0xc035d4: r0 = Row()
    //     0xc035d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc035d8: mov             x1, x0
    // 0xc035dc: r0 = Instance_Axis
    //     0xc035dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc035e0: stur            x1, [fp, #-8]
    // 0xc035e4: StoreField: r1->field_f = r0
    //     0xc035e4: stur            w0, [x1, #0xf]
    // 0xc035e8: r0 = Instance_MainAxisAlignment
    //     0xc035e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc035ec: ldr             x0, [x0, #0xa08]
    // 0xc035f0: StoreField: r1->field_13 = r0
    //     0xc035f0: stur            w0, [x1, #0x13]
    // 0xc035f4: r0 = Instance_MainAxisSize
    //     0xc035f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc035f8: ldr             x0, [x0, #0xa10]
    // 0xc035fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc035fc: stur            w0, [x1, #0x17]
    // 0xc03600: r0 = Instance_CrossAxisAlignment
    //     0xc03600: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc03604: ldr             x0, [x0, #0xa18]
    // 0xc03608: StoreField: r1->field_1b = r0
    //     0xc03608: stur            w0, [x1, #0x1b]
    // 0xc0360c: r0 = Instance_VerticalDirection
    //     0xc0360c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc03610: ldr             x0, [x0, #0xa20]
    // 0xc03614: StoreField: r1->field_23 = r0
    //     0xc03614: stur            w0, [x1, #0x23]
    // 0xc03618: r0 = Instance_Clip
    //     0xc03618: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0361c: ldr             x0, [x0, #0x38]
    // 0xc03620: StoreField: r1->field_2b = r0
    //     0xc03620: stur            w0, [x1, #0x2b]
    // 0xc03624: StoreField: r1->field_2f = rZR
    //     0xc03624: stur            xzr, [x1, #0x2f]
    // 0xc03628: ldur            x0, [fp, #-0x10]
    // 0xc0362c: StoreField: r1->field_b = r0
    //     0xc0362c: stur            w0, [x1, #0xb]
    // 0xc03630: r0 = Visibility()
    //     0xc03630: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc03634: ldur            x1, [fp, #-8]
    // 0xc03638: StoreField: r0->field_b = r1
    //     0xc03638: stur            w1, [x0, #0xb]
    // 0xc0363c: r1 = Instance_SizedBox
    //     0xc0363c: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc03640: StoreField: r0->field_f = r1
    //     0xc03640: stur            w1, [x0, #0xf]
    // 0xc03644: ldur            x1, [fp, #-0x18]
    // 0xc03648: StoreField: r0->field_13 = r1
    //     0xc03648: stur            w1, [x0, #0x13]
    // 0xc0364c: r1 = false
    //     0xc0364c: add             x1, NULL, #0x30  ; false
    // 0xc03650: ArrayStore: r0[0] = r1  ; List_4
    //     0xc03650: stur            w1, [x0, #0x17]
    // 0xc03654: StoreField: r0->field_1b = r1
    //     0xc03654: stur            w1, [x0, #0x1b]
    // 0xc03658: StoreField: r0->field_1f = r1
    //     0xc03658: stur            w1, [x0, #0x1f]
    // 0xc0365c: StoreField: r0->field_23 = r1
    //     0xc0365c: stur            w1, [x0, #0x23]
    // 0xc03660: StoreField: r0->field_27 = r1
    //     0xc03660: stur            w1, [x0, #0x27]
    // 0xc03664: StoreField: r0->field_2b = r1
    //     0xc03664: stur            w1, [x0, #0x2b]
    // 0xc03668: LeaveFrame
    //     0xc03668: mov             SP, fp
    //     0xc0366c: ldp             fp, lr, [SP], #0x10
    // 0xc03670: ret
    //     0xc03670: ret             
    // 0xc03674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc03674: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc03678: b               #0xc02d9c
    // 0xc0367c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0367c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc03680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc03680: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc03684: SaveReg d0
    //     0xc03684: str             q0, [SP, #-0x10]!
    // 0xc03688: SaveReg r0
    //     0xc03688: str             x0, [SP, #-8]!
    // 0xc0368c: r0 = 74
    //     0xc0368c: movz            x0, #0x4a
    // 0xc03690: r30 = DoubleToIntegerStub
    //     0xc03690: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xc03694: LoadField: r30 = r30->field_7
    //     0xc03694: ldur            lr, [lr, #7]
    // 0xc03698: blr             lr
    // 0xc0369c: mov             x1, x0
    // 0xc036a0: RestoreReg r0
    //     0xc036a0: ldr             x0, [SP], #8
    // 0xc036a4: RestoreReg d0
    //     0xc036a4: ldr             q0, [SP], #0x10
    // 0xc036a8: b               #0xc031a8
    // 0xc036ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc036ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc036b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc036b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc036b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc036b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc036b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc036b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc036bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc036bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc036c0, size: 0x13c
    // 0xc036c0: EnterFrame
    //     0xc036c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc036c4: mov             fp, SP
    // 0xc036c8: AllocStack(0x40)
    //     0xc036c8: sub             SP, SP, #0x40
    // 0xc036cc: SetupParameters()
    //     0xc036cc: ldr             x0, [fp, #0x10]
    //     0xc036d0: ldur            w1, [x0, #0x17]
    //     0xc036d4: add             x1, x1, HEAP, lsl #32
    // 0xc036d8: CheckStackOverflow
    //     0xc036d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc036dc: cmp             SP, x16
    //     0xc036e0: b.ls            #0xc037ec
    // 0xc036e4: LoadField: r0 = r1->field_b
    //     0xc036e4: ldur            w0, [x1, #0xb]
    // 0xc036e8: DecompressPointer r0
    //     0xc036e8: add             x0, x0, HEAP, lsl #32
    // 0xc036ec: LoadField: r2 = r0->field_f
    //     0xc036ec: ldur            w2, [x0, #0xf]
    // 0xc036f0: DecompressPointer r2
    //     0xc036f0: add             x2, x2, HEAP, lsl #32
    // 0xc036f4: LoadField: r0 = r2->field_b
    //     0xc036f4: ldur            w0, [x2, #0xb]
    // 0xc036f8: DecompressPointer r0
    //     0xc036f8: add             x0, x0, HEAP, lsl #32
    // 0xc036fc: cmp             w0, NULL
    // 0xc03700: b.eq            #0xc037f4
    // 0xc03704: LoadField: r2 = r0->field_1b
    //     0xc03704: ldur            w2, [x0, #0x1b]
    // 0xc03708: DecompressPointer r2
    //     0xc03708: add             x2, x2, HEAP, lsl #32
    // 0xc0370c: cmp             w2, NULL
    // 0xc03710: b.ne            #0xc03718
    // 0xc03714: r2 = ""
    //     0xc03714: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03718: LoadField: r3 = r0->field_13
    //     0xc03718: ldur            w3, [x0, #0x13]
    // 0xc0371c: DecompressPointer r3
    //     0xc0371c: add             x3, x3, HEAP, lsl #32
    // 0xc03720: cmp             w3, NULL
    // 0xc03724: b.ne            #0xc0372c
    // 0xc03728: r3 = ""
    //     0xc03728: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0372c: LoadField: r4 = r0->field_1f
    //     0xc0372c: ldur            w4, [x0, #0x1f]
    // 0xc03730: DecompressPointer r4
    //     0xc03730: add             x4, x4, HEAP, lsl #32
    // 0xc03734: cmp             w4, NULL
    // 0xc03738: b.ne            #0xc03740
    // 0xc0373c: r4 = ""
    //     0xc0373c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03740: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xc03740: ldur            w5, [x0, #0x17]
    // 0xc03744: DecompressPointer r5
    //     0xc03744: add             x5, x5, HEAP, lsl #32
    // 0xc03748: cmp             w5, NULL
    // 0xc0374c: b.ne            #0xc03754
    // 0xc03750: r5 = ""
    //     0xc03750: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03754: LoadField: r6 = r1->field_f
    //     0xc03754: ldur            w6, [x1, #0xf]
    // 0xc03758: DecompressPointer r6
    //     0xc03758: add             x6, x6, HEAP, lsl #32
    // 0xc0375c: cmp             w6, NULL
    // 0xc03760: b.eq            #0xc037f8
    // 0xc03764: LoadField: r1 = r6->field_47
    //     0xc03764: ldur            w1, [x6, #0x47]
    // 0xc03768: DecompressPointer r1
    //     0xc03768: add             x1, x1, HEAP, lsl #32
    // 0xc0376c: cmp             w1, NULL
    // 0xc03770: b.ne            #0xc03778
    // 0xc03774: r1 = ""
    //     0xc03774: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03778: LoadField: r7 = r6->field_eb
    //     0xc03778: ldur            w7, [x6, #0xeb]
    // 0xc0377c: DecompressPointer r7
    //     0xc0377c: add             x7, x7, HEAP, lsl #32
    // 0xc03780: cmp             w7, NULL
    // 0xc03784: b.ne            #0xc03790
    // 0xc03788: r6 = Null
    //     0xc03788: mov             x6, NULL
    // 0xc0378c: b               #0xc03798
    // 0xc03790: LoadField: r6 = r7->field_23
    //     0xc03790: ldur            w6, [x7, #0x23]
    // 0xc03794: DecompressPointer r6
    //     0xc03794: add             x6, x6, HEAP, lsl #32
    // 0xc03798: cmp             w6, NULL
    // 0xc0379c: b.ne            #0xc037a4
    // 0xc037a0: r6 = ""
    //     0xc037a0: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc037a4: LoadField: r7 = r0->field_33
    //     0xc037a4: ldur            w7, [x0, #0x33]
    // 0xc037a8: DecompressPointer r7
    //     0xc037a8: add             x7, x7, HEAP, lsl #32
    // 0xc037ac: stp             x2, x7, [SP, #0x30]
    // 0xc037b0: r16 = "product_page"
    //     0xc037b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xc037b4: ldr             x16, [x16, #0x480]
    // 0xc037b8: stp             x16, x3, [SP, #0x20]
    // 0xc037bc: stp             x5, x4, [SP, #0x10]
    // 0xc037c0: stp             x6, x1, [SP]
    // 0xc037c4: r4 = 0
    //     0xc037c4: movz            x4, #0
    // 0xc037c8: ldr             x0, [SP, #0x38]
    // 0xc037cc: r16 = UnlinkedCall_0x613b5c
    //     0xc037cc: add             x16, PP, #0x61, lsl #12  ; [pp+0x61bb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc037d0: add             x16, x16, #0xbb0
    // 0xc037d4: ldp             x5, lr, [x16]
    // 0xc037d8: blr             lr
    // 0xc037dc: r0 = Null
    //     0xc037dc: mov             x0, NULL
    // 0xc037e0: LeaveFrame
    //     0xc037e0: mov             SP, fp
    //     0xc037e4: ldp             fp, lr, [SP], #0x10
    // 0xc037e8: ret
    //     0xc037e8: ret             
    // 0xc037ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc037ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc037f0: b               #0xc036e4
    // 0xc037f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc037f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc037f8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc037f8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xc037fc, size: 0x124
    // 0xc037fc: EnterFrame
    //     0xc037fc: stp             fp, lr, [SP, #-0x10]!
    //     0xc03800: mov             fp, SP
    // 0xc03804: AllocStack(0x38)
    //     0xc03804: sub             SP, SP, #0x38
    // 0xc03808: SetupParameters()
    //     0xc03808: ldr             x0, [fp, #0x18]
    //     0xc0380c: ldur            w2, [x0, #0x17]
    //     0xc03810: add             x2, x2, HEAP, lsl #32
    //     0xc03814: stur            x2, [fp, #-8]
    // 0xc03818: CheckStackOverflow
    //     0xc03818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0381c: cmp             SP, x16
    //     0xc03820: b.ls            #0xc03910
    // 0xc03824: ldr             x1, [fp, #0x10]
    // 0xc03828: r0 = visibleFraction()
    //     0xc03828: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xc0382c: mov             v1.16b, v0.16b
    // 0xc03830: d0 = 0.500000
    //     0xc03830: fmov            d0, #0.50000000
    // 0xc03834: fcmp            d1, d0
    // 0xc03838: b.le            #0xc03900
    // 0xc0383c: ldur            x0, [fp, #-8]
    // 0xc03840: LoadField: r1 = r0->field_b
    //     0xc03840: ldur            w1, [x0, #0xb]
    // 0xc03844: DecompressPointer r1
    //     0xc03844: add             x1, x1, HEAP, lsl #32
    // 0xc03848: LoadField: r2 = r1->field_f
    //     0xc03848: ldur            w2, [x1, #0xf]
    // 0xc0384c: DecompressPointer r2
    //     0xc0384c: add             x2, x2, HEAP, lsl #32
    // 0xc03850: LoadField: r1 = r2->field_b
    //     0xc03850: ldur            w1, [x2, #0xb]
    // 0xc03854: DecompressPointer r1
    //     0xc03854: add             x1, x1, HEAP, lsl #32
    // 0xc03858: cmp             w1, NULL
    // 0xc0385c: b.eq            #0xc03918
    // 0xc03860: LoadField: r2 = r0->field_f
    //     0xc03860: ldur            w2, [x0, #0xf]
    // 0xc03864: DecompressPointer r2
    //     0xc03864: add             x2, x2, HEAP, lsl #32
    // 0xc03868: cmp             w2, NULL
    // 0xc0386c: b.eq            #0xc0391c
    // 0xc03870: LoadField: r0 = r2->field_eb
    //     0xc03870: ldur            w0, [x2, #0xeb]
    // 0xc03874: DecompressPointer r0
    //     0xc03874: add             x0, x0, HEAP, lsl #32
    // 0xc03878: cmp             w0, NULL
    // 0xc0387c: b.ne            #0xc03888
    // 0xc03880: r3 = Null
    //     0xc03880: mov             x3, NULL
    // 0xc03884: b               #0xc03890
    // 0xc03888: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc03888: ldur            w3, [x0, #0x17]
    // 0xc0388c: DecompressPointer r3
    //     0xc0388c: add             x3, x3, HEAP, lsl #32
    // 0xc03890: cmp             w0, NULL
    // 0xc03894: b.ne            #0xc038a0
    // 0xc03898: r4 = Null
    //     0xc03898: mov             x4, NULL
    // 0xc0389c: b               #0xc038a8
    // 0xc038a0: LoadField: r4 = r0->field_23
    //     0xc038a0: ldur            w4, [x0, #0x23]
    // 0xc038a4: DecompressPointer r4
    //     0xc038a4: add             x4, x4, HEAP, lsl #32
    // 0xc038a8: LoadField: r5 = r2->field_47
    //     0xc038a8: ldur            w5, [x2, #0x47]
    // 0xc038ac: DecompressPointer r5
    //     0xc038ac: add             x5, x5, HEAP, lsl #32
    // 0xc038b0: cmp             w0, NULL
    // 0xc038b4: b.ne            #0xc038c0
    // 0xc038b8: r0 = Null
    //     0xc038b8: mov             x0, NULL
    // 0xc038bc: b               #0xc038cc
    // 0xc038c0: LoadField: r6 = r0->field_13
    //     0xc038c0: ldur            w6, [x0, #0x13]
    // 0xc038c4: DecompressPointer r6
    //     0xc038c4: add             x6, x6, HEAP, lsl #32
    // 0xc038c8: mov             x0, x6
    // 0xc038cc: LoadField: r6 = r2->field_e3
    //     0xc038cc: ldur            w6, [x2, #0xe3]
    // 0xc038d0: DecompressPointer r6
    //     0xc038d0: add             x6, x6, HEAP, lsl #32
    // 0xc038d4: LoadField: r2 = r1->field_37
    //     0xc038d4: ldur            w2, [x1, #0x37]
    // 0xc038d8: DecompressPointer r2
    //     0xc038d8: add             x2, x2, HEAP, lsl #32
    // 0xc038dc: stp             x3, x2, [SP, #0x20]
    // 0xc038e0: stp             x5, x4, [SP, #0x10]
    // 0xc038e4: stp             x6, x0, [SP]
    // 0xc038e8: r4 = 0
    //     0xc038e8: movz            x4, #0
    // 0xc038ec: ldr             x0, [SP, #0x28]
    // 0xc038f0: r16 = UnlinkedCall_0x613b5c
    //     0xc038f0: add             x16, PP, #0x61, lsl #12  ; [pp+0x61bc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc038f4: add             x16, x16, #0xbc0
    // 0xc038f8: ldp             x5, lr, [x16]
    // 0xc038fc: blr             lr
    // 0xc03900: r0 = Null
    //     0xc03900: mov             x0, NULL
    // 0xc03904: LeaveFrame
    //     0xc03904: mov             SP, fp
    //     0xc03908: ldp             fp, lr, [SP], #0x10
    // 0xc0390c: ret
    //     0xc0390c: ret             
    // 0xc03910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc03910: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc03914: b               #0xc03824
    // 0xc03918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc03918: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0391c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc0391c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc03920, size: 0x218
    // 0xc03920: EnterFrame
    //     0xc03920: stp             fp, lr, [SP, #-0x10]!
    //     0xc03924: mov             fp, SP
    // 0xc03928: AllocStack(0x20)
    //     0xc03928: sub             SP, SP, #0x20
    // 0xc0392c: SetupParameters()
    //     0xc0392c: ldr             x0, [fp, #0x20]
    //     0xc03930: ldur            w1, [x0, #0x17]
    //     0xc03934: add             x1, x1, HEAP, lsl #32
    // 0xc03938: CheckStackOverflow
    //     0xc03938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0393c: cmp             SP, x16
    //     0xc03940: b.ls            #0xc03b28
    // 0xc03944: LoadField: r0 = r1->field_b
    //     0xc03944: ldur            w0, [x1, #0xb]
    // 0xc03948: DecompressPointer r0
    //     0xc03948: add             x0, x0, HEAP, lsl #32
    // 0xc0394c: LoadField: r2 = r0->field_f
    //     0xc0394c: ldur            w2, [x0, #0xf]
    // 0xc03950: DecompressPointer r2
    //     0xc03950: add             x2, x2, HEAP, lsl #32
    // 0xc03954: LoadField: r4 = r1->field_f
    //     0xc03954: ldur            w4, [x1, #0xf]
    // 0xc03958: DecompressPointer r4
    //     0xc03958: add             x4, x4, HEAP, lsl #32
    // 0xc0395c: stur            x4, [fp, #-8]
    // 0xc03960: cmp             w4, NULL
    // 0xc03964: b.eq            #0xc03b30
    // 0xc03968: LoadField: r3 = r4->field_e7
    //     0xc03968: ldur            w3, [x4, #0xe7]
    // 0xc0396c: DecompressPointer r3
    //     0xc0396c: add             x3, x3, HEAP, lsl #32
    // 0xc03970: cmp             w3, NULL
    // 0xc03974: b.ne            #0xc03980
    // 0xc03978: r0 = Null
    //     0xc03978: mov             x0, NULL
    // 0xc0397c: b               #0xc039bc
    // 0xc03980: ldr             x0, [fp, #0x10]
    // 0xc03984: LoadField: r1 = r3->field_b
    //     0xc03984: ldur            w1, [x3, #0xb]
    // 0xc03988: r5 = LoadInt32Instr(r0)
    //     0xc03988: sbfx            x5, x0, #1, #0x1f
    //     0xc0398c: tbz             w0, #0, #0xc03994
    //     0xc03990: ldur            x5, [x0, #7]
    // 0xc03994: r0 = LoadInt32Instr(r1)
    //     0xc03994: sbfx            x0, x1, #1, #0x1f
    // 0xc03998: mov             x1, x5
    // 0xc0399c: cmp             x1, x0
    // 0xc039a0: b.hs            #0xc03b34
    // 0xc039a4: LoadField: r0 = r3->field_f
    //     0xc039a4: ldur            w0, [x3, #0xf]
    // 0xc039a8: DecompressPointer r0
    //     0xc039a8: add             x0, x0, HEAP, lsl #32
    // 0xc039ac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc039ac: add             x16, x0, x5, lsl #2
    //     0xc039b0: ldur            w1, [x16, #0xf]
    // 0xc039b4: DecompressPointer r1
    //     0xc039b4: add             x1, x1, HEAP, lsl #32
    // 0xc039b8: mov             x0, x1
    // 0xc039bc: mov             x1, x2
    // 0xc039c0: mov             x2, x0
    // 0xc039c4: mov             x3, x4
    // 0xc039c8: r0 = _buildImageSlider()
    //     0xc039c8: bl              #0xc03b38  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildImageSlider
    // 0xc039cc: r1 = Null
    //     0xc039cc: mov             x1, NULL
    // 0xc039d0: r2 = 2
    //     0xc039d0: movz            x2, #0x2
    // 0xc039d4: stur            x0, [fp, #-0x10]
    // 0xc039d8: r0 = AllocateArray()
    //     0xc039d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc039dc: mov             x2, x0
    // 0xc039e0: ldur            x0, [fp, #-0x10]
    // 0xc039e4: stur            x2, [fp, #-0x18]
    // 0xc039e8: StoreField: r2->field_f = r0
    //     0xc039e8: stur            w0, [x2, #0xf]
    // 0xc039ec: r1 = <Widget>
    //     0xc039ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc039f0: r0 = AllocateGrowableArray()
    //     0xc039f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc039f4: mov             x1, x0
    // 0xc039f8: ldur            x0, [fp, #-0x18]
    // 0xc039fc: stur            x1, [fp, #-0x10]
    // 0xc03a00: StoreField: r1->field_f = r0
    //     0xc03a00: stur            w0, [x1, #0xf]
    // 0xc03a04: r0 = 2
    //     0xc03a04: movz            x0, #0x2
    // 0xc03a08: StoreField: r1->field_b = r0
    //     0xc03a08: stur            w0, [x1, #0xb]
    // 0xc03a0c: ldur            x0, [fp, #-8]
    // 0xc03a10: r17 = 315
    //     0xc03a10: movz            x17, #0x13b
    // 0xc03a14: ldr             w2, [x0, x17]
    // 0xc03a18: DecompressPointer r2
    //     0xc03a18: add             x2, x2, HEAP, lsl #32
    // 0xc03a1c: cmp             w2, NULL
    // 0xc03a20: b.ne            #0xc03a2c
    // 0xc03a24: mov             x2, x1
    // 0xc03a28: b               #0xc03aec
    // 0xc03a2c: tbnz            w2, #4, #0xc03ae8
    // 0xc03a30: r0 = SvgPicture()
    //     0xc03a30: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc03a34: mov             x1, x0
    // 0xc03a38: r2 = "assets/images/free-gift-icon.svg"
    //     0xc03a38: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xc03a3c: ldr             x2, [x2, #0xd40]
    // 0xc03a40: stur            x0, [fp, #-8]
    // 0xc03a44: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc03a44: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc03a48: r0 = SvgPicture.asset()
    //     0xc03a48: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc03a4c: r0 = Padding()
    //     0xc03a4c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc03a50: mov             x2, x0
    // 0xc03a54: r0 = Instance_EdgeInsets
    //     0xc03a54: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xc03a58: ldr             x0, [x0, #0xd48]
    // 0xc03a5c: stur            x2, [fp, #-0x18]
    // 0xc03a60: StoreField: r2->field_f = r0
    //     0xc03a60: stur            w0, [x2, #0xf]
    // 0xc03a64: ldur            x0, [fp, #-8]
    // 0xc03a68: StoreField: r2->field_b = r0
    //     0xc03a68: stur            w0, [x2, #0xb]
    // 0xc03a6c: ldur            x0, [fp, #-0x10]
    // 0xc03a70: LoadField: r1 = r0->field_b
    //     0xc03a70: ldur            w1, [x0, #0xb]
    // 0xc03a74: LoadField: r3 = r0->field_f
    //     0xc03a74: ldur            w3, [x0, #0xf]
    // 0xc03a78: DecompressPointer r3
    //     0xc03a78: add             x3, x3, HEAP, lsl #32
    // 0xc03a7c: LoadField: r4 = r3->field_b
    //     0xc03a7c: ldur            w4, [x3, #0xb]
    // 0xc03a80: r3 = LoadInt32Instr(r1)
    //     0xc03a80: sbfx            x3, x1, #1, #0x1f
    // 0xc03a84: stur            x3, [fp, #-0x20]
    // 0xc03a88: r1 = LoadInt32Instr(r4)
    //     0xc03a88: sbfx            x1, x4, #1, #0x1f
    // 0xc03a8c: cmp             x3, x1
    // 0xc03a90: b.ne            #0xc03a9c
    // 0xc03a94: mov             x1, x0
    // 0xc03a98: r0 = _growToNextCapacity()
    //     0xc03a98: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc03a9c: ldur            x2, [fp, #-0x10]
    // 0xc03aa0: ldur            x3, [fp, #-0x20]
    // 0xc03aa4: add             x0, x3, #1
    // 0xc03aa8: lsl             x1, x0, #1
    // 0xc03aac: StoreField: r2->field_b = r1
    //     0xc03aac: stur            w1, [x2, #0xb]
    // 0xc03ab0: LoadField: r1 = r2->field_f
    //     0xc03ab0: ldur            w1, [x2, #0xf]
    // 0xc03ab4: DecompressPointer r1
    //     0xc03ab4: add             x1, x1, HEAP, lsl #32
    // 0xc03ab8: ldur            x0, [fp, #-0x18]
    // 0xc03abc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc03abc: add             x25, x1, x3, lsl #2
    //     0xc03ac0: add             x25, x25, #0xf
    //     0xc03ac4: str             w0, [x25]
    //     0xc03ac8: tbz             w0, #0, #0xc03ae4
    //     0xc03acc: ldurb           w16, [x1, #-1]
    //     0xc03ad0: ldurb           w17, [x0, #-1]
    //     0xc03ad4: and             x16, x17, x16, lsr #2
    //     0xc03ad8: tst             x16, HEAP, lsr #32
    //     0xc03adc: b.eq            #0xc03ae4
    //     0xc03ae0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc03ae4: b               #0xc03aec
    // 0xc03ae8: mov             x2, x1
    // 0xc03aec: r0 = Stack()
    //     0xc03aec: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc03af0: r1 = Instance_Alignment
    //     0xc03af0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xc03af4: ldr             x1, [x1, #0x950]
    // 0xc03af8: StoreField: r0->field_f = r1
    //     0xc03af8: stur            w1, [x0, #0xf]
    // 0xc03afc: r1 = Instance_StackFit
    //     0xc03afc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc03b00: ldr             x1, [x1, #0xfa8]
    // 0xc03b04: ArrayStore: r0[0] = r1  ; List_4
    //     0xc03b04: stur            w1, [x0, #0x17]
    // 0xc03b08: r1 = Instance_Clip
    //     0xc03b08: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc03b0c: ldr             x1, [x1, #0x7e0]
    // 0xc03b10: StoreField: r0->field_1b = r1
    //     0xc03b10: stur            w1, [x0, #0x1b]
    // 0xc03b14: ldur            x1, [fp, #-0x10]
    // 0xc03b18: StoreField: r0->field_b = r1
    //     0xc03b18: stur            w1, [x0, #0xb]
    // 0xc03b1c: LeaveFrame
    //     0xc03b1c: mov             SP, fp
    //     0xc03b20: ldp             fp, lr, [SP], #0x10
    // 0xc03b24: ret
    //     0xc03b24: ret             
    // 0xc03b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc03b28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc03b2c: b               #0xc03944
    // 0xc03b30: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc03b30: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc03b34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc03b34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildImageSlider(/* No info */) {
    // ** addr: 0xc03b38, size: 0x448
    // 0xc03b38: EnterFrame
    //     0xc03b38: stp             fp, lr, [SP, #-0x10]!
    //     0xc03b3c: mov             fp, SP
    // 0xc03b40: AllocStack(0x60)
    //     0xc03b40: sub             SP, SP, #0x60
    // 0xc03b44: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xc03b44: mov             x0, x2
    //     0xc03b48: stur            x2, [fp, #-0x10]
    //     0xc03b4c: mov             x2, x3
    //     0xc03b50: stur            x1, [fp, #-8]
    //     0xc03b54: stur            x3, [fp, #-0x18]
    // 0xc03b58: CheckStackOverflow
    //     0xc03b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc03b5c: cmp             SP, x16
    //     0xc03b60: b.ls            #0xc03f70
    // 0xc03b64: r0 = ImageHeaders.forImages()
    //     0xc03b64: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc03b68: mov             x3, x0
    // 0xc03b6c: ldur            x0, [fp, #-0x10]
    // 0xc03b70: stur            x3, [fp, #-0x20]
    // 0xc03b74: cmp             w0, NULL
    // 0xc03b78: b.ne            #0xc03b84
    // 0xc03b7c: r0 = Null
    //     0xc03b7c: mov             x0, NULL
    // 0xc03b80: b               #0xc03b90
    // 0xc03b84: LoadField: r1 = r0->field_b
    //     0xc03b84: ldur            w1, [x0, #0xb]
    // 0xc03b88: DecompressPointer r1
    //     0xc03b88: add             x1, x1, HEAP, lsl #32
    // 0xc03b8c: mov             x0, x1
    // 0xc03b90: cmp             w0, NULL
    // 0xc03b94: b.ne            #0xc03ba0
    // 0xc03b98: r4 = ""
    //     0xc03b98: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc03b9c: b               #0xc03ba4
    // 0xc03ba0: mov             x4, x0
    // 0xc03ba4: ldur            x0, [fp, #-0x18]
    // 0xc03ba8: stur            x4, [fp, #-0x10]
    // 0xc03bac: r1 = Function '<anonymous closure>':.
    //     0xc03bac: add             x1, PP, #0x61, lsl #12  ; [pp+0x61bd8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc03bb0: ldr             x1, [x1, #0xbd8]
    // 0xc03bb4: r2 = Null
    //     0xc03bb4: mov             x2, NULL
    // 0xc03bb8: r0 = AllocateClosure()
    //     0xc03bb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc03bbc: r1 = Function '<anonymous closure>':.
    //     0xc03bbc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61be0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc03bc0: ldr             x1, [x1, #0xbe0]
    // 0xc03bc4: r2 = Null
    //     0xc03bc4: mov             x2, NULL
    // 0xc03bc8: stur            x0, [fp, #-0x28]
    // 0xc03bcc: r0 = AllocateClosure()
    //     0xc03bcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc03bd0: stur            x0, [fp, #-0x30]
    // 0xc03bd4: r0 = CachedNetworkImage()
    //     0xc03bd4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc03bd8: stur            x0, [fp, #-0x38]
    // 0xc03bdc: ldur            x16, [fp, #-0x20]
    // 0xc03be0: r30 = Instance_BoxFit
    //     0xc03be0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xc03be4: ldr             lr, [lr, #0x118]
    // 0xc03be8: stp             lr, x16, [SP, #0x10]
    // 0xc03bec: ldur            x16, [fp, #-0x28]
    // 0xc03bf0: ldur            lr, [fp, #-0x30]
    // 0xc03bf4: stp             lr, x16, [SP]
    // 0xc03bf8: mov             x1, x0
    // 0xc03bfc: ldur            x2, [fp, #-0x10]
    // 0xc03c00: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xc03c00: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xc03c04: ldr             x4, [x4, #0x828]
    // 0xc03c08: r0 = CachedNetworkImage()
    //     0xc03c08: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc03c0c: r0 = Center()
    //     0xc03c0c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc03c10: mov             x3, x0
    // 0xc03c14: r0 = Instance_Alignment
    //     0xc03c14: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc03c18: ldr             x0, [x0, #0xb10]
    // 0xc03c1c: stur            x3, [fp, #-0x10]
    // 0xc03c20: StoreField: r3->field_f = r0
    //     0xc03c20: stur            w0, [x3, #0xf]
    // 0xc03c24: ldur            x0, [fp, #-0x38]
    // 0xc03c28: StoreField: r3->field_b = r0
    //     0xc03c28: stur            w0, [x3, #0xb]
    // 0xc03c2c: r1 = Null
    //     0xc03c2c: mov             x1, NULL
    // 0xc03c30: r2 = 2
    //     0xc03c30: movz            x2, #0x2
    // 0xc03c34: r0 = AllocateArray()
    //     0xc03c34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc03c38: mov             x2, x0
    // 0xc03c3c: ldur            x0, [fp, #-0x10]
    // 0xc03c40: stur            x2, [fp, #-0x20]
    // 0xc03c44: StoreField: r2->field_f = r0
    //     0xc03c44: stur            w0, [x2, #0xf]
    // 0xc03c48: r1 = <Widget>
    //     0xc03c48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc03c4c: r0 = AllocateGrowableArray()
    //     0xc03c4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc03c50: mov             x3, x0
    // 0xc03c54: ldur            x0, [fp, #-0x20]
    // 0xc03c58: stur            x3, [fp, #-0x10]
    // 0xc03c5c: StoreField: r3->field_f = r0
    //     0xc03c5c: stur            w0, [x3, #0xf]
    // 0xc03c60: r0 = 2
    //     0xc03c60: movz            x0, #0x2
    // 0xc03c64: StoreField: r3->field_b = r0
    //     0xc03c64: stur            w0, [x3, #0xb]
    // 0xc03c68: ldur            x0, [fp, #-0x18]
    // 0xc03c6c: cmp             w0, NULL
    // 0xc03c70: b.ne            #0xc03c7c
    // 0xc03c74: r1 = Null
    //     0xc03c74: mov             x1, NULL
    // 0xc03c78: b               #0xc03cac
    // 0xc03c7c: r17 = 295
    //     0xc03c7c: movz            x17, #0x127
    // 0xc03c80: ldr             w1, [x0, x17]
    // 0xc03c84: DecompressPointer r1
    //     0xc03c84: add             x1, x1, HEAP, lsl #32
    // 0xc03c88: cmp             w1, NULL
    // 0xc03c8c: b.ne            #0xc03c98
    // 0xc03c90: r1 = Null
    //     0xc03c90: mov             x1, NULL
    // 0xc03c94: b               #0xc03cac
    // 0xc03c98: LoadField: r2 = r1->field_7
    //     0xc03c98: ldur            w2, [x1, #7]
    // 0xc03c9c: cbnz            w2, #0xc03ca8
    // 0xc03ca0: r1 = false
    //     0xc03ca0: add             x1, NULL, #0x30  ; false
    // 0xc03ca4: b               #0xc03cac
    // 0xc03ca8: r1 = true
    //     0xc03ca8: add             x1, NULL, #0x20  ; true
    // 0xc03cac: cmp             w1, NULL
    // 0xc03cb0: b.eq            #0xc03d4c
    // 0xc03cb4: tbnz            w1, #4, #0xc03d4c
    // 0xc03cb8: cmp             w0, NULL
    // 0xc03cbc: b.eq            #0xc03f78
    // 0xc03cc0: ldur            x1, [fp, #-8]
    // 0xc03cc4: mov             x2, x0
    // 0xc03cc8: r0 = _buildDiscountBadge()
    //     0xc03cc8: bl              #0xc04300  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildDiscountBadge
    // 0xc03ccc: mov             x2, x0
    // 0xc03cd0: ldur            x0, [fp, #-0x10]
    // 0xc03cd4: stur            x2, [fp, #-0x20]
    // 0xc03cd8: LoadField: r1 = r0->field_b
    //     0xc03cd8: ldur            w1, [x0, #0xb]
    // 0xc03cdc: LoadField: r3 = r0->field_f
    //     0xc03cdc: ldur            w3, [x0, #0xf]
    // 0xc03ce0: DecompressPointer r3
    //     0xc03ce0: add             x3, x3, HEAP, lsl #32
    // 0xc03ce4: LoadField: r4 = r3->field_b
    //     0xc03ce4: ldur            w4, [x3, #0xb]
    // 0xc03ce8: r3 = LoadInt32Instr(r1)
    //     0xc03ce8: sbfx            x3, x1, #1, #0x1f
    // 0xc03cec: stur            x3, [fp, #-0x40]
    // 0xc03cf0: r1 = LoadInt32Instr(r4)
    //     0xc03cf0: sbfx            x1, x4, #1, #0x1f
    // 0xc03cf4: cmp             x3, x1
    // 0xc03cf8: b.ne            #0xc03d04
    // 0xc03cfc: mov             x1, x0
    // 0xc03d00: r0 = _growToNextCapacity()
    //     0xc03d00: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc03d04: ldur            x3, [fp, #-0x10]
    // 0xc03d08: ldur            x2, [fp, #-0x40]
    // 0xc03d0c: add             x0, x2, #1
    // 0xc03d10: lsl             x1, x0, #1
    // 0xc03d14: StoreField: r3->field_b = r1
    //     0xc03d14: stur            w1, [x3, #0xb]
    // 0xc03d18: LoadField: r1 = r3->field_f
    //     0xc03d18: ldur            w1, [x3, #0xf]
    // 0xc03d1c: DecompressPointer r1
    //     0xc03d1c: add             x1, x1, HEAP, lsl #32
    // 0xc03d20: ldur            x0, [fp, #-0x20]
    // 0xc03d24: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc03d24: add             x25, x1, x2, lsl #2
    //     0xc03d28: add             x25, x25, #0xf
    //     0xc03d2c: str             w0, [x25]
    //     0xc03d30: tbz             w0, #0, #0xc03d4c
    //     0xc03d34: ldurb           w16, [x1, #-1]
    //     0xc03d38: ldurb           w17, [x0, #-1]
    //     0xc03d3c: and             x16, x17, x16, lsr #2
    //     0xc03d40: tst             x16, HEAP, lsr #32
    //     0xc03d44: b.eq            #0xc03d4c
    //     0xc03d48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc03d4c: ldur            x0, [fp, #-0x18]
    // 0xc03d50: cmp             w0, NULL
    // 0xc03d54: b.ne            #0xc03d60
    // 0xc03d58: r1 = Null
    //     0xc03d58: mov             x1, NULL
    // 0xc03d5c: b               #0xc03d90
    // 0xc03d60: r17 = 311
    //     0xc03d60: movz            x17, #0x137
    // 0xc03d64: ldr             w1, [x0, x17]
    // 0xc03d68: DecompressPointer r1
    //     0xc03d68: add             x1, x1, HEAP, lsl #32
    // 0xc03d6c: cmp             w1, NULL
    // 0xc03d70: b.ne            #0xc03d7c
    // 0xc03d74: r1 = Null
    //     0xc03d74: mov             x1, NULL
    // 0xc03d78: b               #0xc03d90
    // 0xc03d7c: LoadField: r2 = r1->field_7
    //     0xc03d7c: ldur            w2, [x1, #7]
    // 0xc03d80: cbnz            w2, #0xc03d8c
    // 0xc03d84: r1 = false
    //     0xc03d84: add             x1, NULL, #0x30  ; false
    // 0xc03d88: b               #0xc03d90
    // 0xc03d8c: r1 = true
    //     0xc03d8c: add             x1, NULL, #0x20  ; true
    // 0xc03d90: cmp             w1, NULL
    // 0xc03d94: b.ne            #0xc03da0
    // 0xc03d98: mov             x2, x3
    // 0xc03d9c: b               #0xc03e40
    // 0xc03da0: tbnz            w1, #4, #0xc03e3c
    // 0xc03da4: cmp             w0, NULL
    // 0xc03da8: b.eq            #0xc03f7c
    // 0xc03dac: ldur            x1, [fp, #-8]
    // 0xc03db0: mov             x2, x0
    // 0xc03db4: r0 = _buildStockAlert()
    //     0xc03db4: bl              #0xc040e0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildStockAlert
    // 0xc03db8: mov             x2, x0
    // 0xc03dbc: ldur            x0, [fp, #-0x10]
    // 0xc03dc0: stur            x2, [fp, #-0x20]
    // 0xc03dc4: LoadField: r1 = r0->field_b
    //     0xc03dc4: ldur            w1, [x0, #0xb]
    // 0xc03dc8: LoadField: r3 = r0->field_f
    //     0xc03dc8: ldur            w3, [x0, #0xf]
    // 0xc03dcc: DecompressPointer r3
    //     0xc03dcc: add             x3, x3, HEAP, lsl #32
    // 0xc03dd0: LoadField: r4 = r3->field_b
    //     0xc03dd0: ldur            w4, [x3, #0xb]
    // 0xc03dd4: r3 = LoadInt32Instr(r1)
    //     0xc03dd4: sbfx            x3, x1, #1, #0x1f
    // 0xc03dd8: stur            x3, [fp, #-0x40]
    // 0xc03ddc: r1 = LoadInt32Instr(r4)
    //     0xc03ddc: sbfx            x1, x4, #1, #0x1f
    // 0xc03de0: cmp             x3, x1
    // 0xc03de4: b.ne            #0xc03df0
    // 0xc03de8: mov             x1, x0
    // 0xc03dec: r0 = _growToNextCapacity()
    //     0xc03dec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc03df0: ldur            x2, [fp, #-0x10]
    // 0xc03df4: ldur            x3, [fp, #-0x40]
    // 0xc03df8: add             x0, x3, #1
    // 0xc03dfc: lsl             x1, x0, #1
    // 0xc03e00: StoreField: r2->field_b = r1
    //     0xc03e00: stur            w1, [x2, #0xb]
    // 0xc03e04: LoadField: r1 = r2->field_f
    //     0xc03e04: ldur            w1, [x2, #0xf]
    // 0xc03e08: DecompressPointer r1
    //     0xc03e08: add             x1, x1, HEAP, lsl #32
    // 0xc03e0c: ldur            x0, [fp, #-0x20]
    // 0xc03e10: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc03e10: add             x25, x1, x3, lsl #2
    //     0xc03e14: add             x25, x25, #0xf
    //     0xc03e18: str             w0, [x25]
    //     0xc03e1c: tbz             w0, #0, #0xc03e38
    //     0xc03e20: ldurb           w16, [x1, #-1]
    //     0xc03e24: ldurb           w17, [x0, #-1]
    //     0xc03e28: and             x16, x17, x16, lsr #2
    //     0xc03e2c: tst             x16, HEAP, lsr #32
    //     0xc03e30: b.eq            #0xc03e38
    //     0xc03e34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc03e38: b               #0xc03e40
    // 0xc03e3c: mov             x2, x3
    // 0xc03e40: ldur            x0, [fp, #-0x18]
    // 0xc03e44: cmp             w0, NULL
    // 0xc03e48: b.ne            #0xc03e54
    // 0xc03e4c: r0 = Null
    //     0xc03e4c: mov             x0, NULL
    // 0xc03e50: b               #0xc03e64
    // 0xc03e54: r17 = 263
    //     0xc03e54: movz            x17, #0x107
    // 0xc03e58: ldr             w1, [x0, x17]
    // 0xc03e5c: DecompressPointer r1
    //     0xc03e5c: add             x1, x1, HEAP, lsl #32
    // 0xc03e60: mov             x0, x1
    // 0xc03e64: cmp             w0, NULL
    // 0xc03e68: b.eq            #0xc03ef8
    // 0xc03e6c: tbnz            w0, #4, #0xc03ef8
    // 0xc03e70: ldur            x1, [fp, #-8]
    // 0xc03e74: r0 = _buildCustomizationBadge()
    //     0xc03e74: bl              #0xc03f80  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildCustomizationBadge
    // 0xc03e78: mov             x2, x0
    // 0xc03e7c: ldur            x0, [fp, #-0x10]
    // 0xc03e80: stur            x2, [fp, #-8]
    // 0xc03e84: LoadField: r1 = r0->field_b
    //     0xc03e84: ldur            w1, [x0, #0xb]
    // 0xc03e88: LoadField: r3 = r0->field_f
    //     0xc03e88: ldur            w3, [x0, #0xf]
    // 0xc03e8c: DecompressPointer r3
    //     0xc03e8c: add             x3, x3, HEAP, lsl #32
    // 0xc03e90: LoadField: r4 = r3->field_b
    //     0xc03e90: ldur            w4, [x3, #0xb]
    // 0xc03e94: r3 = LoadInt32Instr(r1)
    //     0xc03e94: sbfx            x3, x1, #1, #0x1f
    // 0xc03e98: stur            x3, [fp, #-0x40]
    // 0xc03e9c: r1 = LoadInt32Instr(r4)
    //     0xc03e9c: sbfx            x1, x4, #1, #0x1f
    // 0xc03ea0: cmp             x3, x1
    // 0xc03ea4: b.ne            #0xc03eb0
    // 0xc03ea8: mov             x1, x0
    // 0xc03eac: r0 = _growToNextCapacity()
    //     0xc03eac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc03eb0: ldur            x2, [fp, #-0x10]
    // 0xc03eb4: ldur            x3, [fp, #-0x40]
    // 0xc03eb8: add             x0, x3, #1
    // 0xc03ebc: lsl             x1, x0, #1
    // 0xc03ec0: StoreField: r2->field_b = r1
    //     0xc03ec0: stur            w1, [x2, #0xb]
    // 0xc03ec4: LoadField: r1 = r2->field_f
    //     0xc03ec4: ldur            w1, [x2, #0xf]
    // 0xc03ec8: DecompressPointer r1
    //     0xc03ec8: add             x1, x1, HEAP, lsl #32
    // 0xc03ecc: ldur            x0, [fp, #-8]
    // 0xc03ed0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc03ed0: add             x25, x1, x3, lsl #2
    //     0xc03ed4: add             x25, x25, #0xf
    //     0xc03ed8: str             w0, [x25]
    //     0xc03edc: tbz             w0, #0, #0xc03ef8
    //     0xc03ee0: ldurb           w16, [x1, #-1]
    //     0xc03ee4: ldurb           w17, [x0, #-1]
    //     0xc03ee8: and             x16, x17, x16, lsr #2
    //     0xc03eec: tst             x16, HEAP, lsr #32
    //     0xc03ef0: b.eq            #0xc03ef8
    //     0xc03ef4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc03ef8: r0 = Stack()
    //     0xc03ef8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc03efc: mov             x1, x0
    // 0xc03f00: r0 = Instance_Alignment
    //     0xc03f00: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xc03f04: ldr             x0, [x0, #0x5b8]
    // 0xc03f08: stur            x1, [fp, #-8]
    // 0xc03f0c: StoreField: r1->field_f = r0
    //     0xc03f0c: stur            w0, [x1, #0xf]
    // 0xc03f10: r0 = Instance_StackFit
    //     0xc03f10: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc03f14: ldr             x0, [x0, #0xfa8]
    // 0xc03f18: ArrayStore: r1[0] = r0  ; List_4
    //     0xc03f18: stur            w0, [x1, #0x17]
    // 0xc03f1c: r0 = Instance_Clip
    //     0xc03f1c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc03f20: ldr             x0, [x0, #0x7e0]
    // 0xc03f24: StoreField: r1->field_1b = r0
    //     0xc03f24: stur            w0, [x1, #0x1b]
    // 0xc03f28: ldur            x0, [fp, #-0x10]
    // 0xc03f2c: StoreField: r1->field_b = r0
    //     0xc03f2c: stur            w0, [x1, #0xb]
    // 0xc03f30: r0 = AnimatedContainer()
    //     0xc03f30: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xc03f34: stur            x0, [fp, #-0x10]
    // 0xc03f38: r16 = Instance_Cubic
    //     0xc03f38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xc03f3c: ldr             x16, [x16, #0xaf8]
    // 0xc03f40: str             x16, [SP]
    // 0xc03f44: mov             x1, x0
    // 0xc03f48: ldur            x2, [fp, #-8]
    // 0xc03f4c: r3 = Instance_Duration
    //     0xc03f4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e150] Obj!Duration@d77761
    //     0xc03f50: ldr             x3, [x3, #0x150]
    // 0xc03f54: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xc03f54: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xc03f58: ldr             x4, [x4, #0xbc8]
    // 0xc03f5c: r0 = AnimatedContainer()
    //     0xc03f5c: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xc03f60: ldur            x0, [fp, #-0x10]
    // 0xc03f64: LeaveFrame
    //     0xc03f64: mov             SP, fp
    //     0xc03f68: ldp             fp, lr, [SP], #0x10
    // 0xc03f6c: ret
    //     0xc03f6c: ret             
    // 0xc03f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc03f70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc03f74: b               #0xc03b64
    // 0xc03f78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc03f78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc03f7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc03f7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationBadge(/* No info */) {
    // ** addr: 0xc03f80, size: 0x160
    // 0xc03f80: EnterFrame
    //     0xc03f80: stp             fp, lr, [SP, #-0x10]!
    //     0xc03f84: mov             fp, SP
    // 0xc03f88: AllocStack(0x30)
    //     0xc03f88: sub             SP, SP, #0x30
    // 0xc03f8c: SetupParameters(_ProductGridItemState this /* r1 => r0, fp-0x8 */)
    //     0xc03f8c: mov             x0, x1
    //     0xc03f90: stur            x1, [fp, #-8]
    // 0xc03f94: CheckStackOverflow
    //     0xc03f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc03f98: cmp             SP, x16
    //     0xc03f9c: b.ls            #0xc040d0
    // 0xc03fa0: LoadField: r1 = r0->field_f
    //     0xc03fa0: ldur            w1, [x0, #0xf]
    // 0xc03fa4: DecompressPointer r1
    //     0xc03fa4: add             x1, x1, HEAP, lsl #32
    // 0xc03fa8: cmp             w1, NULL
    // 0xc03fac: b.eq            #0xc040d8
    // 0xc03fb0: r0 = of()
    //     0xc03fb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc03fb4: r17 = 307
    //     0xc03fb4: movz            x17, #0x133
    // 0xc03fb8: ldr             w2, [x0, x17]
    // 0xc03fbc: DecompressPointer r2
    //     0xc03fbc: add             x2, x2, HEAP, lsl #32
    // 0xc03fc0: ldur            x0, [fp, #-8]
    // 0xc03fc4: stur            x2, [fp, #-0x10]
    // 0xc03fc8: LoadField: r1 = r0->field_f
    //     0xc03fc8: ldur            w1, [x0, #0xf]
    // 0xc03fcc: DecompressPointer r1
    //     0xc03fcc: add             x1, x1, HEAP, lsl #32
    // 0xc03fd0: cmp             w1, NULL
    // 0xc03fd4: b.eq            #0xc040dc
    // 0xc03fd8: r0 = of()
    //     0xc03fd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc03fdc: LoadField: r1 = r0->field_87
    //     0xc03fdc: ldur            w1, [x0, #0x87]
    // 0xc03fe0: DecompressPointer r1
    //     0xc03fe0: add             x1, x1, HEAP, lsl #32
    // 0xc03fe4: LoadField: r0 = r1->field_2b
    //     0xc03fe4: ldur            w0, [x1, #0x2b]
    // 0xc03fe8: DecompressPointer r0
    //     0xc03fe8: add             x0, x0, HEAP, lsl #32
    // 0xc03fec: r16 = 12.000000
    //     0xc03fec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc03ff0: ldr             x16, [x16, #0x9e8]
    // 0xc03ff4: r30 = Instance_Color
    //     0xc03ff4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc03ff8: stp             lr, x16, [SP]
    // 0xc03ffc: mov             x1, x0
    // 0xc04000: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc04000: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc04004: ldr             x4, [x4, #0xaa0]
    // 0xc04008: r0 = copyWith()
    //     0xc04008: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0400c: stur            x0, [fp, #-8]
    // 0xc04010: r0 = Text()
    //     0xc04010: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc04014: mov             x3, x0
    // 0xc04018: r0 = "Customisable"
    //     0xc04018: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xc0401c: ldr             x0, [x0, #0x970]
    // 0xc04020: stur            x3, [fp, #-0x18]
    // 0xc04024: StoreField: r3->field_b = r0
    //     0xc04024: stur            w0, [x3, #0xb]
    // 0xc04028: ldur            x0, [fp, #-8]
    // 0xc0402c: StoreField: r3->field_13 = r0
    //     0xc0402c: stur            w0, [x3, #0x13]
    // 0xc04030: r1 = Function '<anonymous closure>':.
    //     0xc04030: add             x1, PP, #0x61, lsl #12  ; [pp+0x61be8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc04034: ldr             x1, [x1, #0xbe8]
    // 0xc04038: r2 = Null
    //     0xc04038: mov             x2, NULL
    // 0xc0403c: r0 = AllocateClosure()
    //     0xc0403c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc04040: stur            x0, [fp, #-8]
    // 0xc04044: r0 = TextButton()
    //     0xc04044: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc04048: mov             x1, x0
    // 0xc0404c: ldur            x0, [fp, #-8]
    // 0xc04050: stur            x1, [fp, #-0x20]
    // 0xc04054: StoreField: r1->field_b = r0
    //     0xc04054: stur            w0, [x1, #0xb]
    // 0xc04058: r0 = false
    //     0xc04058: add             x0, NULL, #0x30  ; false
    // 0xc0405c: StoreField: r1->field_27 = r0
    //     0xc0405c: stur            w0, [x1, #0x27]
    // 0xc04060: r0 = true
    //     0xc04060: add             x0, NULL, #0x20  ; true
    // 0xc04064: StoreField: r1->field_2f = r0
    //     0xc04064: stur            w0, [x1, #0x2f]
    // 0xc04068: ldur            x0, [fp, #-0x18]
    // 0xc0406c: StoreField: r1->field_37 = r0
    //     0xc0406c: stur            w0, [x1, #0x37]
    // 0xc04070: r0 = TextButtonTheme()
    //     0xc04070: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc04074: mov             x1, x0
    // 0xc04078: ldur            x0, [fp, #-0x10]
    // 0xc0407c: stur            x1, [fp, #-8]
    // 0xc04080: StoreField: r1->field_f = r0
    //     0xc04080: stur            w0, [x1, #0xf]
    // 0xc04084: ldur            x0, [fp, #-0x20]
    // 0xc04088: StoreField: r1->field_b = r0
    //     0xc04088: stur            w0, [x1, #0xb]
    // 0xc0408c: r0 = SizedBox()
    //     0xc0408c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc04090: mov             x1, x0
    // 0xc04094: r0 = 35.000000
    //     0xc04094: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xc04098: ldr             x0, [x0, #0x2b0]
    // 0xc0409c: stur            x1, [fp, #-0x10]
    // 0xc040a0: StoreField: r1->field_13 = r0
    //     0xc040a0: stur            w0, [x1, #0x13]
    // 0xc040a4: ldur            x0, [fp, #-8]
    // 0xc040a8: StoreField: r1->field_b = r0
    //     0xc040a8: stur            w0, [x1, #0xb]
    // 0xc040ac: r0 = Padding()
    //     0xc040ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc040b0: r1 = Instance_EdgeInsets
    //     0xc040b0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xc040b4: ldr             x1, [x1, #0xe68]
    // 0xc040b8: StoreField: r0->field_f = r1
    //     0xc040b8: stur            w1, [x0, #0xf]
    // 0xc040bc: ldur            x1, [fp, #-0x10]
    // 0xc040c0: StoreField: r0->field_b = r1
    //     0xc040c0: stur            w1, [x0, #0xb]
    // 0xc040c4: LeaveFrame
    //     0xc040c4: mov             SP, fp
    //     0xc040c8: ldp             fp, lr, [SP], #0x10
    // 0xc040cc: ret
    //     0xc040cc: ret             
    // 0xc040d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc040d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc040d4: b               #0xc03fa0
    // 0xc040d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc040d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc040dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc040dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStockAlert(/* No info */) {
    // ** addr: 0xc040e0, size: 0x220
    // 0xc040e0: EnterFrame
    //     0xc040e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc040e4: mov             fp, SP
    // 0xc040e8: AllocStack(0x50)
    //     0xc040e8: sub             SP, SP, #0x50
    // 0xc040ec: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc040ec: stur            x1, [fp, #-8]
    //     0xc040f0: stur            x2, [fp, #-0x10]
    // 0xc040f4: CheckStackOverflow
    //     0xc040f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc040f8: cmp             SP, x16
    //     0xc040fc: b.ls            #0xc042f4
    // 0xc04100: r17 = 263
    //     0xc04100: movz            x17, #0x107
    // 0xc04104: ldr             w0, [x2, x17]
    // 0xc04108: DecompressPointer r0
    //     0xc04108: add             x0, x0, HEAP, lsl #32
    // 0xc0410c: cmp             w0, NULL
    // 0xc04110: b.eq            #0xc04124
    // 0xc04114: tbnz            w0, #4, #0xc04124
    // 0xc04118: d0 = 38.000000
    //     0xc04118: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xc0411c: ldr             d0, [x17, #0xd10]
    // 0xc04120: b               #0xc04128
    // 0xc04124: d0 = 4.000000
    //     0xc04124: fmov            d0, #4.00000000
    // 0xc04128: stur            d0, [fp, #-0x40]
    // 0xc0412c: r0 = EdgeInsets()
    //     0xc0412c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xc04130: d0 = 8.000000
    //     0xc04130: fmov            d0, #8.00000000
    // 0xc04134: stur            x0, [fp, #-0x18]
    // 0xc04138: StoreField: r0->field_7 = d0
    //     0xc04138: stur            d0, [x0, #7]
    // 0xc0413c: StoreField: r0->field_f = rZR
    //     0xc0413c: stur            xzr, [x0, #0xf]
    // 0xc04140: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc04140: stur            xzr, [x0, #0x17]
    // 0xc04144: ldur            d0, [fp, #-0x40]
    // 0xc04148: StoreField: r0->field_1f = d0
    //     0xc04148: stur            d0, [x0, #0x1f]
    // 0xc0414c: r16 = <EdgeInsets>
    //     0xc0414c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xc04150: ldr             x16, [x16, #0xda0]
    // 0xc04154: r30 = Instance_EdgeInsets
    //     0xc04154: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc04158: ldr             lr, [lr, #0x668]
    // 0xc0415c: stp             lr, x16, [SP]
    // 0xc04160: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc04160: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc04164: r0 = all()
    //     0xc04164: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc04168: stur            x0, [fp, #-0x20]
    // 0xc0416c: r16 = <Color>
    //     0xc0416c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc04170: ldr             x16, [x16, #0xf80]
    // 0xc04174: r30 = Instance_Color
    //     0xc04174: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc04178: stp             lr, x16, [SP]
    // 0xc0417c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0417c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc04180: r0 = all()
    //     0xc04180: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc04184: stur            x0, [fp, #-0x28]
    // 0xc04188: r16 = <RoundedRectangleBorder>
    //     0xc04188: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xc0418c: ldr             x16, [x16, #0xf78]
    // 0xc04190: r30 = Instance_RoundedRectangleBorder
    //     0xc04190: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xc04194: ldr             lr, [lr, #0xd68]
    // 0xc04198: stp             lr, x16, [SP]
    // 0xc0419c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0419c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc041a0: r0 = all()
    //     0xc041a0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc041a4: stur            x0, [fp, #-0x30]
    // 0xc041a8: r0 = ButtonStyle()
    //     0xc041a8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc041ac: mov             x1, x0
    // 0xc041b0: ldur            x0, [fp, #-0x28]
    // 0xc041b4: stur            x1, [fp, #-0x38]
    // 0xc041b8: StoreField: r1->field_b = r0
    //     0xc041b8: stur            w0, [x1, #0xb]
    // 0xc041bc: ldur            x0, [fp, #-0x20]
    // 0xc041c0: StoreField: r1->field_23 = r0
    //     0xc041c0: stur            w0, [x1, #0x23]
    // 0xc041c4: ldur            x0, [fp, #-0x30]
    // 0xc041c8: StoreField: r1->field_43 = r0
    //     0xc041c8: stur            w0, [x1, #0x43]
    // 0xc041cc: r0 = TextButtonThemeData()
    //     0xc041cc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc041d0: mov             x2, x0
    // 0xc041d4: ldur            x0, [fp, #-0x38]
    // 0xc041d8: stur            x2, [fp, #-0x20]
    // 0xc041dc: StoreField: r2->field_7 = r0
    //     0xc041dc: stur            w0, [x2, #7]
    // 0xc041e0: ldur            x0, [fp, #-0x10]
    // 0xc041e4: r17 = 311
    //     0xc041e4: movz            x17, #0x137
    // 0xc041e8: ldr             w1, [x0, x17]
    // 0xc041ec: DecompressPointer r1
    //     0xc041ec: add             x1, x1, HEAP, lsl #32
    // 0xc041f0: cmp             w1, NULL
    // 0xc041f4: b.ne            #0xc04200
    // 0xc041f8: r3 = ""
    //     0xc041f8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc041fc: b               #0xc04204
    // 0xc04200: mov             x3, x1
    // 0xc04204: ldur            x1, [fp, #-8]
    // 0xc04208: ldur            x0, [fp, #-0x18]
    // 0xc0420c: stur            x3, [fp, #-0x10]
    // 0xc04210: LoadField: r4 = r1->field_f
    //     0xc04210: ldur            w4, [x1, #0xf]
    // 0xc04214: DecompressPointer r4
    //     0xc04214: add             x4, x4, HEAP, lsl #32
    // 0xc04218: cmp             w4, NULL
    // 0xc0421c: b.eq            #0xc042fc
    // 0xc04220: mov             x1, x4
    // 0xc04224: r0 = of()
    //     0xc04224: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc04228: LoadField: r1 = r0->field_87
    //     0xc04228: ldur            w1, [x0, #0x87]
    // 0xc0422c: DecompressPointer r1
    //     0xc0422c: add             x1, x1, HEAP, lsl #32
    // 0xc04230: LoadField: r0 = r1->field_2b
    //     0xc04230: ldur            w0, [x1, #0x2b]
    // 0xc04234: DecompressPointer r0
    //     0xc04234: add             x0, x0, HEAP, lsl #32
    // 0xc04238: r16 = 12.000000
    //     0xc04238: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0423c: ldr             x16, [x16, #0x9e8]
    // 0xc04240: r30 = Instance_Color
    //     0xc04240: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc04244: stp             lr, x16, [SP]
    // 0xc04248: mov             x1, x0
    // 0xc0424c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0424c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc04250: ldr             x4, [x4, #0xaa0]
    // 0xc04254: r0 = copyWith()
    //     0xc04254: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc04258: stur            x0, [fp, #-8]
    // 0xc0425c: r0 = Text()
    //     0xc0425c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc04260: mov             x3, x0
    // 0xc04264: ldur            x0, [fp, #-0x10]
    // 0xc04268: stur            x3, [fp, #-0x28]
    // 0xc0426c: StoreField: r3->field_b = r0
    //     0xc0426c: stur            w0, [x3, #0xb]
    // 0xc04270: ldur            x0, [fp, #-8]
    // 0xc04274: StoreField: r3->field_13 = r0
    //     0xc04274: stur            w0, [x3, #0x13]
    // 0xc04278: r1 = Function '<anonymous closure>':.
    //     0xc04278: add             x1, PP, #0x61, lsl #12  ; [pp+0x61bf0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc0427c: ldr             x1, [x1, #0xbf0]
    // 0xc04280: r2 = Null
    //     0xc04280: mov             x2, NULL
    // 0xc04284: r0 = AllocateClosure()
    //     0xc04284: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc04288: stur            x0, [fp, #-8]
    // 0xc0428c: r0 = TextButton()
    //     0xc0428c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc04290: mov             x1, x0
    // 0xc04294: ldur            x0, [fp, #-8]
    // 0xc04298: stur            x1, [fp, #-0x10]
    // 0xc0429c: StoreField: r1->field_b = r0
    //     0xc0429c: stur            w0, [x1, #0xb]
    // 0xc042a0: r0 = false
    //     0xc042a0: add             x0, NULL, #0x30  ; false
    // 0xc042a4: StoreField: r1->field_27 = r0
    //     0xc042a4: stur            w0, [x1, #0x27]
    // 0xc042a8: r0 = true
    //     0xc042a8: add             x0, NULL, #0x20  ; true
    // 0xc042ac: StoreField: r1->field_2f = r0
    //     0xc042ac: stur            w0, [x1, #0x2f]
    // 0xc042b0: ldur            x0, [fp, #-0x28]
    // 0xc042b4: StoreField: r1->field_37 = r0
    //     0xc042b4: stur            w0, [x1, #0x37]
    // 0xc042b8: r0 = TextButtonTheme()
    //     0xc042b8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc042bc: mov             x1, x0
    // 0xc042c0: ldur            x0, [fp, #-0x20]
    // 0xc042c4: stur            x1, [fp, #-8]
    // 0xc042c8: StoreField: r1->field_f = r0
    //     0xc042c8: stur            w0, [x1, #0xf]
    // 0xc042cc: ldur            x0, [fp, #-0x10]
    // 0xc042d0: StoreField: r1->field_b = r0
    //     0xc042d0: stur            w0, [x1, #0xb]
    // 0xc042d4: r0 = Padding()
    //     0xc042d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc042d8: ldur            x1, [fp, #-0x18]
    // 0xc042dc: StoreField: r0->field_f = r1
    //     0xc042dc: stur            w1, [x0, #0xf]
    // 0xc042e0: ldur            x1, [fp, #-8]
    // 0xc042e4: StoreField: r0->field_b = r1
    //     0xc042e4: stur            w1, [x0, #0xb]
    // 0xc042e8: LeaveFrame
    //     0xc042e8: mov             SP, fp
    //     0xc042ec: ldp             fp, lr, [SP], #0x10
    // 0xc042f0: ret
    //     0xc042f0: ret             
    // 0xc042f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc042f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc042f8: b               #0xc04100
    // 0xc042fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc042fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildDiscountBadge(/* No info */) {
    // ** addr: 0xc04300, size: 0x7b8
    // 0xc04300: EnterFrame
    //     0xc04300: stp             fp, lr, [SP, #-0x10]!
    //     0xc04304: mov             fp, SP
    // 0xc04308: AllocStack(0x70)
    //     0xc04308: sub             SP, SP, #0x70
    // 0xc0430c: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x28 */, dynamic _ /* r2 => r2, fp-0x30 */)
    //     0xc0430c: stur            x1, [fp, #-0x28]
    //     0xc04310: stur            x2, [fp, #-0x30]
    // 0xc04314: CheckStackOverflow
    //     0xc04314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04318: cmp             SP, x16
    //     0xc0431c: b.ls            #0xc04a84
    // 0xc04320: r17 = 271
    //     0xc04320: movz            x17, #0x10f
    // 0xc04324: ldr             w0, [x2, x17]
    // 0xc04328: DecompressPointer r0
    //     0xc04328: add             x0, x0, HEAP, lsl #32
    // 0xc0432c: cmp             w0, NULL
    // 0xc04330: b.ne            #0xc0435c
    // 0xc04334: mov             x3, x1
    // 0xc04338: mov             x0, x2
    // 0xc0433c: r5 = Instance_EdgeInsets
    //     0xc0433c: add             x5, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xc04340: ldr             x5, [x5, #0xe50]
    // 0xc04344: r4 = Instance_Alignment
    //     0xc04344: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc04348: ldr             x4, [x4, #0xfa0]
    // 0xc0434c: r2 = 4
    //     0xc0434c: movz            x2, #0x4
    // 0xc04350: d0 = 0.700000
    //     0xc04350: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc04354: ldr             d0, [x17, #0xf48]
    // 0xc04358: b               #0xc047bc
    // 0xc0435c: tbnz            w0, #4, #0xc04798
    // 0xc04360: LoadField: r0 = r1->field_b
    //     0xc04360: ldur            w0, [x1, #0xb]
    // 0xc04364: DecompressPointer r0
    //     0xc04364: add             x0, x0, HEAP, lsl #32
    // 0xc04368: cmp             w0, NULL
    // 0xc0436c: b.eq            #0xc04a8c
    // 0xc04370: LoadField: r3 = r0->field_2b
    //     0xc04370: ldur            w3, [x0, #0x2b]
    // 0xc04374: DecompressPointer r3
    //     0xc04374: add             x3, x3, HEAP, lsl #32
    // 0xc04378: LoadField: r0 = r3->field_13
    //     0xc04378: ldur            w0, [x3, #0x13]
    // 0xc0437c: DecompressPointer r0
    //     0xc0437c: add             x0, x0, HEAP, lsl #32
    // 0xc04380: stur            x0, [fp, #-0x20]
    // 0xc04384: cmp             w0, NULL
    // 0xc04388: b.ne            #0xc04394
    // 0xc0438c: r3 = Null
    //     0xc0438c: mov             x3, NULL
    // 0xc04390: b               #0xc0439c
    // 0xc04394: LoadField: r3 = r0->field_7
    //     0xc04394: ldur            w3, [x0, #7]
    // 0xc04398: DecompressPointer r3
    //     0xc04398: add             x3, x3, HEAP, lsl #32
    // 0xc0439c: cmp             w3, NULL
    // 0xc043a0: b.ne            #0xc043ac
    // 0xc043a4: r3 = 0
    //     0xc043a4: movz            x3, #0
    // 0xc043a8: b               #0xc043bc
    // 0xc043ac: r4 = LoadInt32Instr(r3)
    //     0xc043ac: sbfx            x4, x3, #1, #0x1f
    //     0xc043b0: tbz             w3, #0, #0xc043b8
    //     0xc043b4: ldur            x4, [x3, #7]
    // 0xc043b8: mov             x3, x4
    // 0xc043bc: stur            x3, [fp, #-0x18]
    // 0xc043c0: cmp             w0, NULL
    // 0xc043c4: b.ne            #0xc043d0
    // 0xc043c8: r4 = Null
    //     0xc043c8: mov             x4, NULL
    // 0xc043cc: b               #0xc043d8
    // 0xc043d0: LoadField: r4 = r0->field_b
    //     0xc043d0: ldur            w4, [x0, #0xb]
    // 0xc043d4: DecompressPointer r4
    //     0xc043d4: add             x4, x4, HEAP, lsl #32
    // 0xc043d8: cmp             w4, NULL
    // 0xc043dc: b.ne            #0xc043e8
    // 0xc043e0: r4 = 0
    //     0xc043e0: movz            x4, #0
    // 0xc043e4: b               #0xc043f8
    // 0xc043e8: r5 = LoadInt32Instr(r4)
    //     0xc043e8: sbfx            x5, x4, #1, #0x1f
    //     0xc043ec: tbz             w4, #0, #0xc043f4
    //     0xc043f0: ldur            x5, [x4, #7]
    // 0xc043f4: mov             x4, x5
    // 0xc043f8: stur            x4, [fp, #-0x10]
    // 0xc043fc: cmp             w0, NULL
    // 0xc04400: b.ne            #0xc0440c
    // 0xc04404: r5 = Null
    //     0xc04404: mov             x5, NULL
    // 0xc04408: b               #0xc04414
    // 0xc0440c: LoadField: r5 = r0->field_f
    //     0xc0440c: ldur            w5, [x0, #0xf]
    // 0xc04410: DecompressPointer r5
    //     0xc04410: add             x5, x5, HEAP, lsl #32
    // 0xc04414: cmp             w5, NULL
    // 0xc04418: b.ne            #0xc04424
    // 0xc0441c: r5 = 0
    //     0xc0441c: movz            x5, #0
    // 0xc04420: b               #0xc04434
    // 0xc04424: r6 = LoadInt32Instr(r5)
    //     0xc04424: sbfx            x6, x5, #1, #0x1f
    //     0xc04428: tbz             w5, #0, #0xc04430
    //     0xc0442c: ldur            x6, [x5, #7]
    // 0xc04430: mov             x5, x6
    // 0xc04434: stur            x5, [fp, #-8]
    // 0xc04438: r0 = Color()
    //     0xc04438: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xc0443c: mov             x1, x0
    // 0xc04440: r0 = Instance_ColorSpace
    //     0xc04440: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xc04444: stur            x1, [fp, #-0x38]
    // 0xc04448: StoreField: r1->field_27 = r0
    //     0xc04448: stur            w0, [x1, #0x27]
    // 0xc0444c: d0 = 1.000000
    //     0xc0444c: fmov            d0, #1.00000000
    // 0xc04450: StoreField: r1->field_7 = d0
    //     0xc04450: stur            d0, [x1, #7]
    // 0xc04454: ldur            x2, [fp, #-0x18]
    // 0xc04458: ubfx            x2, x2, #0, #0x20
    // 0xc0445c: and             w3, w2, #0xff
    // 0xc04460: ubfx            x3, x3, #0, #0x20
    // 0xc04464: scvtf           d0, x3
    // 0xc04468: d1 = 255.000000
    //     0xc04468: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xc0446c: fdiv            d2, d0, d1
    // 0xc04470: StoreField: r1->field_f = d2
    //     0xc04470: stur            d2, [x1, #0xf]
    // 0xc04474: ldur            x2, [fp, #-0x10]
    // 0xc04478: ubfx            x2, x2, #0, #0x20
    // 0xc0447c: and             w3, w2, #0xff
    // 0xc04480: ubfx            x3, x3, #0, #0x20
    // 0xc04484: scvtf           d0, x3
    // 0xc04488: fdiv            d2, d0, d1
    // 0xc0448c: ArrayStore: r1[0] = d2  ; List_8
    //     0xc0448c: stur            d2, [x1, #0x17]
    // 0xc04490: ldur            x2, [fp, #-8]
    // 0xc04494: ubfx            x2, x2, #0, #0x20
    // 0xc04498: and             w3, w2, #0xff
    // 0xc0449c: ubfx            x3, x3, #0, #0x20
    // 0xc044a0: scvtf           d0, x3
    // 0xc044a4: fdiv            d2, d0, d1
    // 0xc044a8: StoreField: r1->field_1f = d2
    //     0xc044a8: stur            d2, [x1, #0x1f]
    // 0xc044ac: ldur            x2, [fp, #-0x20]
    // 0xc044b0: cmp             w2, NULL
    // 0xc044b4: b.ne            #0xc044c0
    // 0xc044b8: r3 = Null
    //     0xc044b8: mov             x3, NULL
    // 0xc044bc: b               #0xc044c8
    // 0xc044c0: LoadField: r3 = r2->field_7
    //     0xc044c0: ldur            w3, [x2, #7]
    // 0xc044c4: DecompressPointer r3
    //     0xc044c4: add             x3, x3, HEAP, lsl #32
    // 0xc044c8: cmp             w3, NULL
    // 0xc044cc: b.ne            #0xc044d8
    // 0xc044d0: r3 = 0
    //     0xc044d0: movz            x3, #0
    // 0xc044d4: b               #0xc044e8
    // 0xc044d8: r4 = LoadInt32Instr(r3)
    //     0xc044d8: sbfx            x4, x3, #1, #0x1f
    //     0xc044dc: tbz             w3, #0, #0xc044e4
    //     0xc044e0: ldur            x4, [x3, #7]
    // 0xc044e4: mov             x3, x4
    // 0xc044e8: stur            x3, [fp, #-0x18]
    // 0xc044ec: cmp             w2, NULL
    // 0xc044f0: b.ne            #0xc044fc
    // 0xc044f4: r4 = Null
    //     0xc044f4: mov             x4, NULL
    // 0xc044f8: b               #0xc04504
    // 0xc044fc: LoadField: r4 = r2->field_b
    //     0xc044fc: ldur            w4, [x2, #0xb]
    // 0xc04500: DecompressPointer r4
    //     0xc04500: add             x4, x4, HEAP, lsl #32
    // 0xc04504: cmp             w4, NULL
    // 0xc04508: b.ne            #0xc04514
    // 0xc0450c: r4 = 0
    //     0xc0450c: movz            x4, #0
    // 0xc04510: b               #0xc04524
    // 0xc04514: r5 = LoadInt32Instr(r4)
    //     0xc04514: sbfx            x5, x4, #1, #0x1f
    //     0xc04518: tbz             w4, #0, #0xc04520
    //     0xc0451c: ldur            x5, [x4, #7]
    // 0xc04520: mov             x4, x5
    // 0xc04524: stur            x4, [fp, #-0x10]
    // 0xc04528: cmp             w2, NULL
    // 0xc0452c: b.ne            #0xc04538
    // 0xc04530: r2 = Null
    //     0xc04530: mov             x2, NULL
    // 0xc04534: b               #0xc04544
    // 0xc04538: LoadField: r5 = r2->field_f
    //     0xc04538: ldur            w5, [x2, #0xf]
    // 0xc0453c: DecompressPointer r5
    //     0xc0453c: add             x5, x5, HEAP, lsl #32
    // 0xc04540: mov             x2, x5
    // 0xc04544: cmp             w2, NULL
    // 0xc04548: b.ne            #0xc04554
    // 0xc0454c: r5 = 0
    //     0xc0454c: movz            x5, #0
    // 0xc04550: b               #0xc04560
    // 0xc04554: r5 = LoadInt32Instr(r2)
    //     0xc04554: sbfx            x5, x2, #1, #0x1f
    //     0xc04558: tbz             w2, #0, #0xc04560
    //     0xc0455c: ldur            x5, [x2, #7]
    // 0xc04560: ldur            x2, [fp, #-0x30]
    // 0xc04564: stur            x5, [fp, #-8]
    // 0xc04568: r0 = Color()
    //     0xc04568: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xc0456c: mov             x3, x0
    // 0xc04570: r0 = Instance_ColorSpace
    //     0xc04570: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xc04574: stur            x3, [fp, #-0x20]
    // 0xc04578: StoreField: r3->field_27 = r0
    //     0xc04578: stur            w0, [x3, #0x27]
    // 0xc0457c: d0 = 0.700000
    //     0xc0457c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc04580: ldr             d0, [x17, #0xf48]
    // 0xc04584: StoreField: r3->field_7 = d0
    //     0xc04584: stur            d0, [x3, #7]
    // 0xc04588: ldur            x0, [fp, #-0x18]
    // 0xc0458c: ubfx            x0, x0, #0, #0x20
    // 0xc04590: and             w1, w0, #0xff
    // 0xc04594: ubfx            x1, x1, #0, #0x20
    // 0xc04598: scvtf           d0, x1
    // 0xc0459c: d1 = 255.000000
    //     0xc0459c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xc045a0: fdiv            d2, d0, d1
    // 0xc045a4: StoreField: r3->field_f = d2
    //     0xc045a4: stur            d2, [x3, #0xf]
    // 0xc045a8: ldur            x0, [fp, #-0x10]
    // 0xc045ac: ubfx            x0, x0, #0, #0x20
    // 0xc045b0: and             w1, w0, #0xff
    // 0xc045b4: ubfx            x1, x1, #0, #0x20
    // 0xc045b8: scvtf           d0, x1
    // 0xc045bc: fdiv            d2, d0, d1
    // 0xc045c0: ArrayStore: r3[0] = d2  ; List_8
    //     0xc045c0: stur            d2, [x3, #0x17]
    // 0xc045c4: ldur            x0, [fp, #-8]
    // 0xc045c8: ubfx            x0, x0, #0, #0x20
    // 0xc045cc: and             w1, w0, #0xff
    // 0xc045d0: ubfx            x1, x1, #0, #0x20
    // 0xc045d4: scvtf           d0, x1
    // 0xc045d8: fdiv            d2, d0, d1
    // 0xc045dc: StoreField: r3->field_1f = d2
    //     0xc045dc: stur            d2, [x3, #0x1f]
    // 0xc045e0: r1 = Null
    //     0xc045e0: mov             x1, NULL
    // 0xc045e4: r2 = 4
    //     0xc045e4: movz            x2, #0x4
    // 0xc045e8: r0 = AllocateArray()
    //     0xc045e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc045ec: mov             x2, x0
    // 0xc045f0: ldur            x0, [fp, #-0x38]
    // 0xc045f4: stur            x2, [fp, #-0x40]
    // 0xc045f8: StoreField: r2->field_f = r0
    //     0xc045f8: stur            w0, [x2, #0xf]
    // 0xc045fc: ldur            x0, [fp, #-0x20]
    // 0xc04600: StoreField: r2->field_13 = r0
    //     0xc04600: stur            w0, [x2, #0x13]
    // 0xc04604: r1 = <Color>
    //     0xc04604: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc04608: ldr             x1, [x1, #0xf80]
    // 0xc0460c: r0 = AllocateGrowableArray()
    //     0xc0460c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc04610: mov             x1, x0
    // 0xc04614: ldur            x0, [fp, #-0x40]
    // 0xc04618: stur            x1, [fp, #-0x20]
    // 0xc0461c: StoreField: r1->field_f = r0
    //     0xc0461c: stur            w0, [x1, #0xf]
    // 0xc04620: r2 = 4
    //     0xc04620: movz            x2, #0x4
    // 0xc04624: StoreField: r1->field_b = r2
    //     0xc04624: stur            w2, [x1, #0xb]
    // 0xc04628: r0 = LinearGradient()
    //     0xc04628: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xc0462c: mov             x1, x0
    // 0xc04630: r0 = Instance_Alignment
    //     0xc04630: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xc04634: ldr             x0, [x0, #0xce0]
    // 0xc04638: stur            x1, [fp, #-0x38]
    // 0xc0463c: StoreField: r1->field_13 = r0
    //     0xc0463c: stur            w0, [x1, #0x13]
    // 0xc04640: r0 = Instance_Alignment
    //     0xc04640: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xc04644: ldr             x0, [x0, #0xce8]
    // 0xc04648: ArrayStore: r1[0] = r0  ; List_4
    //     0xc04648: stur            w0, [x1, #0x17]
    // 0xc0464c: r0 = Instance_TileMode
    //     0xc0464c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xc04650: ldr             x0, [x0, #0xcf0]
    // 0xc04654: StoreField: r1->field_1b = r0
    //     0xc04654: stur            w0, [x1, #0x1b]
    // 0xc04658: ldur            x0, [fp, #-0x20]
    // 0xc0465c: StoreField: r1->field_7 = r0
    //     0xc0465c: stur            w0, [x1, #7]
    // 0xc04660: r0 = BoxDecoration()
    //     0xc04660: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc04664: mov             x2, x0
    // 0xc04668: ldur            x0, [fp, #-0x38]
    // 0xc0466c: stur            x2, [fp, #-0x40]
    // 0xc04670: StoreField: r2->field_1b = r0
    //     0xc04670: stur            w0, [x2, #0x1b]
    // 0xc04674: r0 = Instance_BoxShape
    //     0xc04674: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc04678: ldr             x0, [x0, #0x80]
    // 0xc0467c: StoreField: r2->field_23 = r0
    //     0xc0467c: stur            w0, [x2, #0x23]
    // 0xc04680: ldur            x0, [fp, #-0x30]
    // 0xc04684: r17 = 295
    //     0xc04684: movz            x17, #0x127
    // 0xc04688: ldr             w1, [x0, x17]
    // 0xc0468c: DecompressPointer r1
    //     0xc0468c: add             x1, x1, HEAP, lsl #32
    // 0xc04690: cmp             w1, NULL
    // 0xc04694: b.ne            #0xc046a0
    // 0xc04698: r0 = ""
    //     0xc04698: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0469c: b               #0xc046a4
    // 0xc046a0: mov             x0, x1
    // 0xc046a4: ldur            x3, [fp, #-0x28]
    // 0xc046a8: stur            x0, [fp, #-0x20]
    // 0xc046ac: LoadField: r1 = r3->field_f
    //     0xc046ac: ldur            w1, [x3, #0xf]
    // 0xc046b0: DecompressPointer r1
    //     0xc046b0: add             x1, x1, HEAP, lsl #32
    // 0xc046b4: cmp             w1, NULL
    // 0xc046b8: b.eq            #0xc04a90
    // 0xc046bc: r0 = of()
    //     0xc046bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc046c0: LoadField: r1 = r0->field_87
    //     0xc046c0: ldur            w1, [x0, #0x87]
    // 0xc046c4: DecompressPointer r1
    //     0xc046c4: add             x1, x1, HEAP, lsl #32
    // 0xc046c8: LoadField: r0 = r1->field_7
    //     0xc046c8: ldur            w0, [x1, #7]
    // 0xc046cc: DecompressPointer r0
    //     0xc046cc: add             x0, x0, HEAP, lsl #32
    // 0xc046d0: r16 = 12.000000
    //     0xc046d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc046d4: ldr             x16, [x16, #0x9e8]
    // 0xc046d8: r30 = Instance_Color
    //     0xc046d8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc046dc: stp             lr, x16, [SP]
    // 0xc046e0: mov             x1, x0
    // 0xc046e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc046e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc046e8: ldr             x4, [x4, #0xaa0]
    // 0xc046ec: r0 = copyWith()
    //     0xc046ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc046f0: stur            x0, [fp, #-0x38]
    // 0xc046f4: r0 = Text()
    //     0xc046f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc046f8: mov             x1, x0
    // 0xc046fc: ldur            x0, [fp, #-0x20]
    // 0xc04700: stur            x1, [fp, #-0x48]
    // 0xc04704: StoreField: r1->field_b = r0
    //     0xc04704: stur            w0, [x1, #0xb]
    // 0xc04708: ldur            x0, [fp, #-0x38]
    // 0xc0470c: StoreField: r1->field_13 = r0
    //     0xc0470c: stur            w0, [x1, #0x13]
    // 0xc04710: r0 = Padding()
    //     0xc04710: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc04714: mov             x1, x0
    // 0xc04718: r0 = Instance_EdgeInsets
    //     0xc04718: add             x0, PP, #0x46, lsl #12  ; [pp+0x46f10] Obj!EdgeInsets@d58191
    //     0xc0471c: ldr             x0, [x0, #0xf10]
    // 0xc04720: stur            x1, [fp, #-0x20]
    // 0xc04724: StoreField: r1->field_f = r0
    //     0xc04724: stur            w0, [x1, #0xf]
    // 0xc04728: ldur            x0, [fp, #-0x48]
    // 0xc0472c: StoreField: r1->field_b = r0
    //     0xc0472c: stur            w0, [x1, #0xb]
    // 0xc04730: r0 = Container()
    //     0xc04730: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc04734: stur            x0, [fp, #-0x38]
    // 0xc04738: r16 = 20.000000
    //     0xc04738: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xc0473c: ldr             x16, [x16, #0xac8]
    // 0xc04740: ldur            lr, [fp, #-0x40]
    // 0xc04744: stp             lr, x16, [SP, #8]
    // 0xc04748: ldur            x16, [fp, #-0x20]
    // 0xc0474c: str             x16, [SP]
    // 0xc04750: mov             x1, x0
    // 0xc04754: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xc04754: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xc04758: ldr             x4, [x4, #0xc78]
    // 0xc0475c: r0 = Container()
    //     0xc0475c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc04760: r0 = Align()
    //     0xc04760: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc04764: r4 = Instance_Alignment
    //     0xc04764: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc04768: ldr             x4, [x4, #0xfa0]
    // 0xc0476c: stur            x0, [fp, #-0x20]
    // 0xc04770: StoreField: r0->field_f = r4
    //     0xc04770: stur            w4, [x0, #0xf]
    // 0xc04774: ldur            x1, [fp, #-0x38]
    // 0xc04778: StoreField: r0->field_b = r1
    //     0xc04778: stur            w1, [x0, #0xb]
    // 0xc0477c: r0 = Padding()
    //     0xc0477c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc04780: r5 = Instance_EdgeInsets
    //     0xc04780: add             x5, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xc04784: ldr             x5, [x5, #0xe50]
    // 0xc04788: StoreField: r0->field_f = r5
    //     0xc04788: stur            w5, [x0, #0xf]
    // 0xc0478c: ldur            x1, [fp, #-0x20]
    // 0xc04790: StoreField: r0->field_b = r1
    //     0xc04790: stur            w1, [x0, #0xb]
    // 0xc04794: b               #0xc04a78
    // 0xc04798: mov             x3, x1
    // 0xc0479c: mov             x0, x2
    // 0xc047a0: r5 = Instance_EdgeInsets
    //     0xc047a0: add             x5, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xc047a4: ldr             x5, [x5, #0xe50]
    // 0xc047a8: r4 = Instance_Alignment
    //     0xc047a8: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc047ac: ldr             x4, [x4, #0xfa0]
    // 0xc047b0: r2 = 4
    //     0xc047b0: movz            x2, #0x4
    // 0xc047b4: d0 = 0.700000
    //     0xc047b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc047b8: ldr             d0, [x17, #0xf48]
    // 0xc047bc: LoadField: r1 = r3->field_f
    //     0xc047bc: ldur            w1, [x3, #0xf]
    // 0xc047c0: DecompressPointer r1
    //     0xc047c0: add             x1, x1, HEAP, lsl #32
    // 0xc047c4: cmp             w1, NULL
    // 0xc047c8: b.eq            #0xc04a94
    // 0xc047cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc047cc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc047d0: r0 = _of()
    //     0xc047d0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc047d4: LoadField: r1 = r0->field_7
    //     0xc047d4: ldur            w1, [x0, #7]
    // 0xc047d8: DecompressPointer r1
    //     0xc047d8: add             x1, x1, HEAP, lsl #32
    // 0xc047dc: LoadField: d0 = r1->field_7
    //     0xc047dc: ldur            d0, [x1, #7]
    // 0xc047e0: d1 = 0.370000
    //     0xc047e0: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xc047e4: ldr             d1, [x17, #0xe40]
    // 0xc047e8: fmul            d2, d0, d1
    // 0xc047ec: ldur            x0, [fp, #-0x28]
    // 0xc047f0: stur            d2, [fp, #-0x50]
    // 0xc047f4: LoadField: r1 = r0->field_f
    //     0xc047f4: ldur            w1, [x0, #0xf]
    // 0xc047f8: DecompressPointer r1
    //     0xc047f8: add             x1, x1, HEAP, lsl #32
    // 0xc047fc: cmp             w1, NULL
    // 0xc04800: b.eq            #0xc04a98
    // 0xc04804: r0 = of()
    //     0xc04804: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc04808: LoadField: r1 = r0->field_5b
    //     0xc04808: ldur            w1, [x0, #0x5b]
    // 0xc0480c: DecompressPointer r1
    //     0xc0480c: add             x1, x1, HEAP, lsl #32
    // 0xc04810: stur            x1, [fp, #-0x20]
    // 0xc04814: r0 = ColorFilter()
    //     0xc04814: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc04818: mov             x1, x0
    // 0xc0481c: ldur            x0, [fp, #-0x20]
    // 0xc04820: stur            x1, [fp, #-0x38]
    // 0xc04824: StoreField: r1->field_7 = r0
    //     0xc04824: stur            w0, [x1, #7]
    // 0xc04828: r0 = Instance_BlendMode
    //     0xc04828: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc0482c: ldr             x0, [x0, #0xb30]
    // 0xc04830: StoreField: r1->field_b = r0
    //     0xc04830: stur            w0, [x1, #0xb]
    // 0xc04834: r0 = 1
    //     0xc04834: movz            x0, #0x1
    // 0xc04838: StoreField: r1->field_13 = r0
    //     0xc04838: stur            x0, [x1, #0x13]
    // 0xc0483c: r0 = SvgPicture()
    //     0xc0483c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc04840: stur            x0, [fp, #-0x20]
    // 0xc04844: ldur            x16, [fp, #-0x38]
    // 0xc04848: str             x16, [SP]
    // 0xc0484c: mov             x1, x0
    // 0xc04850: r2 = "assets/images/bumper_coupon.svg"
    //     0xc04850: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xc04854: ldr             x2, [x2, #0xe48]
    // 0xc04858: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc04858: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc0485c: ldr             x4, [x4, #0xa38]
    // 0xc04860: r0 = SvgPicture.asset()
    //     0xc04860: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc04864: ldur            x0, [fp, #-0x30]
    // 0xc04868: r17 = 295
    //     0xc04868: movz            x17, #0x127
    // 0xc0486c: ldr             w1, [x0, x17]
    // 0xc04870: DecompressPointer r1
    //     0xc04870: add             x1, x1, HEAP, lsl #32
    // 0xc04874: cmp             w1, NULL
    // 0xc04878: b.ne            #0xc04884
    // 0xc0487c: r2 = ""
    //     0xc0487c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc04880: b               #0xc04888
    // 0xc04884: mov             x2, x1
    // 0xc04888: ldur            x1, [fp, #-0x28]
    // 0xc0488c: ldur            d0, [fp, #-0x50]
    // 0xc04890: ldur            x0, [fp, #-0x20]
    // 0xc04894: stur            x2, [fp, #-0x30]
    // 0xc04898: LoadField: r3 = r1->field_f
    //     0xc04898: ldur            w3, [x1, #0xf]
    // 0xc0489c: DecompressPointer r3
    //     0xc0489c: add             x3, x3, HEAP, lsl #32
    // 0xc048a0: cmp             w3, NULL
    // 0xc048a4: b.eq            #0xc04a9c
    // 0xc048a8: mov             x1, x3
    // 0xc048ac: r0 = of()
    //     0xc048ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc048b0: LoadField: r1 = r0->field_87
    //     0xc048b0: ldur            w1, [x0, #0x87]
    // 0xc048b4: DecompressPointer r1
    //     0xc048b4: add             x1, x1, HEAP, lsl #32
    // 0xc048b8: LoadField: r0 = r1->field_2b
    //     0xc048b8: ldur            w0, [x1, #0x2b]
    // 0xc048bc: DecompressPointer r0
    //     0xc048bc: add             x0, x0, HEAP, lsl #32
    // 0xc048c0: stur            x0, [fp, #-0x28]
    // 0xc048c4: r1 = Instance_Color
    //     0xc048c4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc048c8: d0 = 0.700000
    //     0xc048c8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc048cc: ldr             d0, [x17, #0xf48]
    // 0xc048d0: r0 = withOpacity()
    //     0xc048d0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc048d4: r16 = 12.000000
    //     0xc048d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc048d8: ldr             x16, [x16, #0x9e8]
    // 0xc048dc: stp             x0, x16, [SP]
    // 0xc048e0: ldur            x1, [fp, #-0x28]
    // 0xc048e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc048e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc048e8: ldr             x4, [x4, #0xaa0]
    // 0xc048ec: r0 = copyWith()
    //     0xc048ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc048f0: stur            x0, [fp, #-0x28]
    // 0xc048f4: r0 = Text()
    //     0xc048f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc048f8: mov             x1, x0
    // 0xc048fc: ldur            x0, [fp, #-0x30]
    // 0xc04900: stur            x1, [fp, #-0x38]
    // 0xc04904: StoreField: r1->field_b = r0
    //     0xc04904: stur            w0, [x1, #0xb]
    // 0xc04908: ldur            x0, [fp, #-0x28]
    // 0xc0490c: StoreField: r1->field_13 = r0
    //     0xc0490c: stur            w0, [x1, #0x13]
    // 0xc04910: r0 = Instance_TextAlign
    //     0xc04910: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xc04914: StoreField: r1->field_1b = r0
    //     0xc04914: stur            w0, [x1, #0x1b]
    // 0xc04918: r0 = Padding()
    //     0xc04918: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0491c: mov             x3, x0
    // 0xc04920: r0 = Instance_EdgeInsets
    //     0xc04920: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xc04924: ldr             x0, [x0, #0xe60]
    // 0xc04928: stur            x3, [fp, #-0x28]
    // 0xc0492c: StoreField: r3->field_f = r0
    //     0xc0492c: stur            w0, [x3, #0xf]
    // 0xc04930: ldur            x0, [fp, #-0x38]
    // 0xc04934: StoreField: r3->field_b = r0
    //     0xc04934: stur            w0, [x3, #0xb]
    // 0xc04938: r1 = Null
    //     0xc04938: mov             x1, NULL
    // 0xc0493c: r2 = 4
    //     0xc0493c: movz            x2, #0x4
    // 0xc04940: r0 = AllocateArray()
    //     0xc04940: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc04944: mov             x2, x0
    // 0xc04948: ldur            x0, [fp, #-0x20]
    // 0xc0494c: stur            x2, [fp, #-0x30]
    // 0xc04950: StoreField: r2->field_f = r0
    //     0xc04950: stur            w0, [x2, #0xf]
    // 0xc04954: ldur            x0, [fp, #-0x28]
    // 0xc04958: StoreField: r2->field_13 = r0
    //     0xc04958: stur            w0, [x2, #0x13]
    // 0xc0495c: r1 = <Widget>
    //     0xc0495c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc04960: r0 = AllocateGrowableArray()
    //     0xc04960: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc04964: mov             x1, x0
    // 0xc04968: ldur            x0, [fp, #-0x30]
    // 0xc0496c: stur            x1, [fp, #-0x20]
    // 0xc04970: StoreField: r1->field_f = r0
    //     0xc04970: stur            w0, [x1, #0xf]
    // 0xc04974: r0 = 4
    //     0xc04974: movz            x0, #0x4
    // 0xc04978: StoreField: r1->field_b = r0
    //     0xc04978: stur            w0, [x1, #0xb]
    // 0xc0497c: r0 = Row()
    //     0xc0497c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc04980: mov             x1, x0
    // 0xc04984: r0 = Instance_Axis
    //     0xc04984: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc04988: stur            x1, [fp, #-0x28]
    // 0xc0498c: StoreField: r1->field_f = r0
    //     0xc0498c: stur            w0, [x1, #0xf]
    // 0xc04990: r0 = Instance_MainAxisAlignment
    //     0xc04990: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc04994: ldr             x0, [x0, #0xa08]
    // 0xc04998: StoreField: r1->field_13 = r0
    //     0xc04998: stur            w0, [x1, #0x13]
    // 0xc0499c: r0 = Instance_MainAxisSize
    //     0xc0499c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc049a0: ldr             x0, [x0, #0xa10]
    // 0xc049a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xc049a4: stur            w0, [x1, #0x17]
    // 0xc049a8: r0 = Instance_CrossAxisAlignment
    //     0xc049a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc049ac: ldr             x0, [x0, #0xa18]
    // 0xc049b0: StoreField: r1->field_1b = r0
    //     0xc049b0: stur            w0, [x1, #0x1b]
    // 0xc049b4: r0 = Instance_VerticalDirection
    //     0xc049b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc049b8: ldr             x0, [x0, #0xa20]
    // 0xc049bc: StoreField: r1->field_23 = r0
    //     0xc049bc: stur            w0, [x1, #0x23]
    // 0xc049c0: r0 = Instance_Clip
    //     0xc049c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc049c4: ldr             x0, [x0, #0x38]
    // 0xc049c8: StoreField: r1->field_2b = r0
    //     0xc049c8: stur            w0, [x1, #0x2b]
    // 0xc049cc: StoreField: r1->field_2f = rZR
    //     0xc049cc: stur            xzr, [x1, #0x2f]
    // 0xc049d0: ldur            x0, [fp, #-0x20]
    // 0xc049d4: StoreField: r1->field_b = r0
    //     0xc049d4: stur            w0, [x1, #0xb]
    // 0xc049d8: ldur            d0, [fp, #-0x50]
    // 0xc049dc: r0 = inline_Allocate_Double()
    //     0xc049dc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xc049e0: add             x0, x0, #0x10
    //     0xc049e4: cmp             x2, x0
    //     0xc049e8: b.ls            #0xc04aa0
    //     0xc049ec: str             x0, [THR, #0x50]  ; THR::top
    //     0xc049f0: sub             x0, x0, #0xf
    //     0xc049f4: movz            x2, #0xe15c
    //     0xc049f8: movk            x2, #0x3, lsl #16
    //     0xc049fc: stur            x2, [x0, #-1]
    // 0xc04a00: StoreField: r0->field_7 = d0
    //     0xc04a00: stur            d0, [x0, #7]
    // 0xc04a04: stur            x0, [fp, #-0x20]
    // 0xc04a08: r0 = Container()
    //     0xc04a08: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc04a0c: stur            x0, [fp, #-0x30]
    // 0xc04a10: r16 = 20.000000
    //     0xc04a10: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xc04a14: ldr             x16, [x16, #0xac8]
    // 0xc04a18: ldur            lr, [fp, #-0x20]
    // 0xc04a1c: stp             lr, x16, [SP, #0x10]
    // 0xc04a20: r16 = Instance_BoxDecoration
    //     0xc04a20: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xc04a24: ldr             x16, [x16, #0x5a8]
    // 0xc04a28: ldur            lr, [fp, #-0x28]
    // 0xc04a2c: stp             lr, x16, [SP]
    // 0xc04a30: mov             x1, x0
    // 0xc04a34: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xc04a34: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xc04a38: ldr             x4, [x4, #0x8c0]
    // 0xc04a3c: r0 = Container()
    //     0xc04a3c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc04a40: r0 = Padding()
    //     0xc04a40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc04a44: mov             x1, x0
    // 0xc04a48: r0 = Instance_EdgeInsets
    //     0xc04a48: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xc04a4c: ldr             x0, [x0, #0xe50]
    // 0xc04a50: stur            x1, [fp, #-0x20]
    // 0xc04a54: StoreField: r1->field_f = r0
    //     0xc04a54: stur            w0, [x1, #0xf]
    // 0xc04a58: ldur            x0, [fp, #-0x30]
    // 0xc04a5c: StoreField: r1->field_b = r0
    //     0xc04a5c: stur            w0, [x1, #0xb]
    // 0xc04a60: r0 = Align()
    //     0xc04a60: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc04a64: r1 = Instance_Alignment
    //     0xc04a64: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc04a68: ldr             x1, [x1, #0xfa0]
    // 0xc04a6c: StoreField: r0->field_f = r1
    //     0xc04a6c: stur            w1, [x0, #0xf]
    // 0xc04a70: ldur            x1, [fp, #-0x20]
    // 0xc04a74: StoreField: r0->field_b = r1
    //     0xc04a74: stur            w1, [x0, #0xb]
    // 0xc04a78: LeaveFrame
    //     0xc04a78: mov             SP, fp
    //     0xc04a7c: ldp             fp, lr, [SP], #0x10
    // 0xc04a80: ret
    //     0xc04a80: ret             
    // 0xc04a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04a84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04a88: b               #0xc04320
    // 0xc04a8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc04a8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc04a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc04a90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc04a94: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc04a94: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc04a98: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc04a98: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc04a9c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc04a9c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc04aa0: SaveReg d0
    //     0xc04aa0: str             q0, [SP, #-0x10]!
    // 0xc04aa4: SaveReg r1
    //     0xc04aa4: str             x1, [SP, #-8]!
    // 0xc04aa8: r0 = AllocateDouble()
    //     0xc04aa8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc04aac: RestoreReg r1
    //     0xc04aac: ldr             x1, [SP], #8
    // 0xc04ab0: RestoreReg d0
    //     0xc04ab0: ldr             q0, [SP], #0x10
    // 0xc04ab4: b               #0xc04a00
  }
}

// class id: 3972, size: 0x40, field offset: 0xc
//   const constructor, 
class _ProductGridItem extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80fbc, size: 0x24
    // 0xc80fbc: EnterFrame
    //     0xc80fbc: stp             fp, lr, [SP, #-0x10]!
    //     0xc80fc0: mov             fp, SP
    // 0xc80fc4: mov             x0, x1
    // 0xc80fc8: r1 = <_ProductGridItem>
    //     0xc80fc8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b10] TypeArguments: <_ProductGridItem>
    //     0xc80fcc: ldr             x1, [x1, #0xb10]
    // 0xc80fd0: r0 = _ProductGridItemState()
    //     0xc80fd0: bl              #0xc80fe0  ; Allocate_ProductGridItemStateStub -> _ProductGridItemState (size=0x14)
    // 0xc80fd4: LeaveFrame
    //     0xc80fd4: mov             SP, fp
    //     0xc80fd8: ldp             fp, lr, [SP], #0x10
    // 0xc80fdc: ret
    //     0xc80fdc: ret             
  }
}

// class id: 4482, size: 0x4c, field offset: 0xc
//   const constructor, 
class ProductDetailGridItemView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x12982f8, size: 0x76c
    // 0x12982f8: EnterFrame
    //     0x12982f8: stp             fp, lr, [SP, #-0x10]!
    //     0x12982fc: mov             fp, SP
    // 0x1298300: AllocStack(0x98)
    //     0x1298300: sub             SP, SP, #0x98
    // 0x1298304: SetupParameters(ProductDetailGridItemView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x1298304: mov             x0, x1
    //     0x1298308: stur            x1, [fp, #-8]
    //     0x129830c: mov             x1, x2
    //     0x1298310: stur            x2, [fp, #-0x10]
    // 0x1298314: CheckStackOverflow
    //     0x1298314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1298318: cmp             SP, x16
    //     0x129831c: b.ls            #0x1298a58
    // 0x1298320: r1 = 1
    //     0x1298320: movz            x1, #0x1
    // 0x1298324: r0 = AllocateContext()
    //     0x1298324: bl              #0x16f6108  ; AllocateContextStub
    // 0x1298328: mov             x3, x0
    // 0x129832c: ldur            x0, [fp, #-8]
    // 0x1298330: stur            x3, [fp, #-0x28]
    // 0x1298334: StoreField: r3->field_f = r0
    //     0x1298334: stur            w0, [x3, #0xf]
    // 0x1298338: LoadField: r4 = r0->field_1f
    //     0x1298338: ldur            w4, [x0, #0x1f]
    // 0x129833c: DecompressPointer r4
    //     0x129833c: add             x4, x4, HEAP, lsl #32
    // 0x1298340: stur            x4, [fp, #-0x20]
    // 0x1298344: LoadField: r1 = r4->field_7
    //     0x1298344: ldur            w1, [x4, #7]
    // 0x1298348: DecompressPointer r1
    //     0x1298348: add             x1, x1, HEAP, lsl #32
    // 0x129834c: cmp             w1, NULL
    // 0x1298350: b.ne            #0x129835c
    // 0x1298354: r1 = Instance_TitleAlignment
    //     0x1298354: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1298358: ldr             x1, [x1, #0x518]
    // 0x129835c: r16 = Instance_TitleAlignment
    //     0x129835c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x1298360: ldr             x16, [x16, #0x520]
    // 0x1298364: cmp             w1, w16
    // 0x1298368: b.ne            #0x1298378
    // 0x129836c: r5 = Instance_CrossAxisAlignment
    //     0x129836c: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x1298370: ldr             x5, [x5, #0xc68]
    // 0x1298374: b               #0x129839c
    // 0x1298378: r16 = Instance_TitleAlignment
    //     0x1298378: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x129837c: ldr             x16, [x16, #0x518]
    // 0x1298380: cmp             w1, w16
    // 0x1298384: b.ne            #0x1298394
    // 0x1298388: r5 = Instance_CrossAxisAlignment
    //     0x1298388: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x129838c: ldr             x5, [x5, #0x890]
    // 0x1298390: b               #0x129839c
    // 0x1298394: r5 = Instance_CrossAxisAlignment
    //     0x1298394: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1298398: ldr             x5, [x5, #0xa18]
    // 0x129839c: stur            x5, [fp, #-0x18]
    // 0x12983a0: r1 = <Widget>
    //     0x12983a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12983a4: r2 = 0
    //     0x12983a4: movz            x2, #0
    // 0x12983a8: r0 = _GrowableList()
    //     0x12983a8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x12983ac: mov             x2, x0
    // 0x12983b0: ldur            x1, [fp, #-8]
    // 0x12983b4: stur            x2, [fp, #-0x38]
    // 0x12983b8: LoadField: r3 = r1->field_f
    //     0x12983b8: ldur            w3, [x1, #0xf]
    // 0x12983bc: DecompressPointer r3
    //     0x12983bc: add             x3, x3, HEAP, lsl #32
    // 0x12983c0: stur            x3, [fp, #-0x30]
    // 0x12983c4: cmp             w3, NULL
    // 0x12983c8: b.ne            #0x12983d4
    // 0x12983cc: r0 = Null
    //     0x12983cc: mov             x0, NULL
    // 0x12983d0: b               #0x12983ec
    // 0x12983d4: LoadField: r0 = r3->field_7
    //     0x12983d4: ldur            w0, [x3, #7]
    // 0x12983d8: cbnz            w0, #0x12983e4
    // 0x12983dc: r4 = false
    //     0x12983dc: add             x4, NULL, #0x30  ; false
    // 0x12983e0: b               #0x12983e8
    // 0x12983e4: r4 = true
    //     0x12983e4: add             x4, NULL, #0x20  ; true
    // 0x12983e8: mov             x0, x4
    // 0x12983ec: cmp             w0, NULL
    // 0x12983f0: b.eq            #0x1298570
    // 0x12983f4: tbnz            w0, #4, #0x1298570
    // 0x12983f8: cmp             w3, NULL
    // 0x12983fc: b.ne            #0x1298408
    // 0x1298400: r0 = Null
    //     0x1298400: mov             x0, NULL
    // 0x1298404: b               #0x1298420
    // 0x1298408: r0 = LoadClassIdInstr(r3)
    //     0x1298408: ldur            x0, [x3, #-1]
    //     0x129840c: ubfx            x0, x0, #0xc, #0x14
    // 0x1298410: str             x3, [SP]
    // 0x1298414: r0 = GDT[cid_x0 + -0x1000]()
    //     0x1298414: sub             lr, x0, #1, lsl #12
    //     0x1298418: ldr             lr, [x21, lr, lsl #3]
    //     0x129841c: blr             lr
    // 0x1298420: cmp             w0, NULL
    // 0x1298424: b.ne            #0x1298430
    // 0x1298428: r2 = ""
    //     0x1298428: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x129842c: b               #0x1298434
    // 0x1298430: mov             x2, x0
    // 0x1298434: ldur            x0, [fp, #-0x20]
    // 0x1298438: stur            x2, [fp, #-0x40]
    // 0x129843c: LoadField: r1 = r0->field_7
    //     0x129843c: ldur            w1, [x0, #7]
    // 0x1298440: DecompressPointer r1
    //     0x1298440: add             x1, x1, HEAP, lsl #32
    // 0x1298444: cmp             w1, NULL
    // 0x1298448: b.ne            #0x1298458
    // 0x129844c: r0 = Instance_TitleAlignment
    //     0x129844c: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1298450: ldr             x0, [x0, #0x518]
    // 0x1298454: b               #0x129845c
    // 0x1298458: mov             x0, x1
    // 0x129845c: r16 = Instance_TitleAlignment
    //     0x129845c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x1298460: ldr             x16, [x16, #0x520]
    // 0x1298464: cmp             w0, w16
    // 0x1298468: b.ne            #0x1298474
    // 0x129846c: r3 = Instance_TextAlign
    //     0x129846c: ldr             x3, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0x1298470: b               #0x1298490
    // 0x1298474: r16 = Instance_TitleAlignment
    //     0x1298474: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1298478: ldr             x16, [x16, #0x518]
    // 0x129847c: cmp             w0, w16
    // 0x1298480: b.ne            #0x129848c
    // 0x1298484: r3 = Instance_TextAlign
    //     0x1298484: ldr             x3, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0x1298488: b               #0x1298490
    // 0x129848c: r3 = Instance_TextAlign
    //     0x129848c: ldr             x3, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1298490: ldur            x0, [fp, #-0x38]
    // 0x1298494: ldur            x1, [fp, #-0x10]
    // 0x1298498: stur            x3, [fp, #-0x20]
    // 0x129849c: r0 = of()
    //     0x129849c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12984a0: LoadField: r1 = r0->field_87
    //     0x12984a0: ldur            w1, [x0, #0x87]
    // 0x12984a4: DecompressPointer r1
    //     0x12984a4: add             x1, x1, HEAP, lsl #32
    // 0x12984a8: LoadField: r0 = r1->field_27
    //     0x12984a8: ldur            w0, [x1, #0x27]
    // 0x12984ac: DecompressPointer r0
    //     0x12984ac: add             x0, x0, HEAP, lsl #32
    // 0x12984b0: r16 = 21.000000
    //     0x12984b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x12984b4: ldr             x16, [x16, #0x9b0]
    // 0x12984b8: r30 = Instance_Color
    //     0x12984b8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12984bc: stp             lr, x16, [SP]
    // 0x12984c0: mov             x1, x0
    // 0x12984c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12984c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12984c8: ldr             x4, [x4, #0xaa0]
    // 0x12984cc: r0 = copyWith()
    //     0x12984cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12984d0: stur            x0, [fp, #-0x48]
    // 0x12984d4: r0 = Text()
    //     0x12984d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12984d8: mov             x2, x0
    // 0x12984dc: ldur            x0, [fp, #-0x40]
    // 0x12984e0: stur            x2, [fp, #-0x58]
    // 0x12984e4: StoreField: r2->field_b = r0
    //     0x12984e4: stur            w0, [x2, #0xb]
    // 0x12984e8: ldur            x0, [fp, #-0x48]
    // 0x12984ec: StoreField: r2->field_13 = r0
    //     0x12984ec: stur            w0, [x2, #0x13]
    // 0x12984f0: ldur            x0, [fp, #-0x20]
    // 0x12984f4: StoreField: r2->field_1b = r0
    //     0x12984f4: stur            w0, [x2, #0x1b]
    // 0x12984f8: ldur            x0, [fp, #-0x38]
    // 0x12984fc: LoadField: r1 = r0->field_b
    //     0x12984fc: ldur            w1, [x0, #0xb]
    // 0x1298500: LoadField: r3 = r0->field_f
    //     0x1298500: ldur            w3, [x0, #0xf]
    // 0x1298504: DecompressPointer r3
    //     0x1298504: add             x3, x3, HEAP, lsl #32
    // 0x1298508: LoadField: r4 = r3->field_b
    //     0x1298508: ldur            w4, [x3, #0xb]
    // 0x129850c: r3 = LoadInt32Instr(r1)
    //     0x129850c: sbfx            x3, x1, #1, #0x1f
    // 0x1298510: stur            x3, [fp, #-0x50]
    // 0x1298514: r1 = LoadInt32Instr(r4)
    //     0x1298514: sbfx            x1, x4, #1, #0x1f
    // 0x1298518: cmp             x3, x1
    // 0x129851c: b.ne            #0x1298528
    // 0x1298520: mov             x1, x0
    // 0x1298524: r0 = _growToNextCapacity()
    //     0x1298524: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1298528: ldur            x2, [fp, #-0x38]
    // 0x129852c: ldur            x3, [fp, #-0x50]
    // 0x1298530: add             x0, x3, #1
    // 0x1298534: lsl             x1, x0, #1
    // 0x1298538: StoreField: r2->field_b = r1
    //     0x1298538: stur            w1, [x2, #0xb]
    // 0x129853c: LoadField: r1 = r2->field_f
    //     0x129853c: ldur            w1, [x2, #0xf]
    // 0x1298540: DecompressPointer r1
    //     0x1298540: add             x1, x1, HEAP, lsl #32
    // 0x1298544: ldur            x0, [fp, #-0x58]
    // 0x1298548: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1298548: add             x25, x1, x3, lsl #2
    //     0x129854c: add             x25, x25, #0xf
    //     0x1298550: str             w0, [x25]
    //     0x1298554: tbz             w0, #0, #0x1298570
    //     0x1298558: ldurb           w16, [x1, #-1]
    //     0x129855c: ldurb           w17, [x0, #-1]
    //     0x1298560: and             x16, x17, x16, lsr #2
    //     0x1298564: tst             x16, HEAP, lsr #32
    //     0x1298568: b.eq            #0x1298570
    //     0x129856c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1298570: ldur            x0, [fp, #-8]
    // 0x1298574: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1298574: ldur            w1, [x0, #0x17]
    // 0x1298578: DecompressPointer r1
    //     0x1298578: add             x1, x1, HEAP, lsl #32
    // 0x129857c: cmp             w1, NULL
    // 0x1298580: b.ne            #0x129858c
    // 0x1298584: r3 = Null
    //     0x1298584: mov             x3, NULL
    // 0x1298588: b               #0x12985b8
    // 0x129858c: LoadField: r3 = r1->field_7
    //     0x129858c: ldur            w3, [x1, #7]
    // 0x1298590: DecompressPointer r3
    //     0x1298590: add             x3, x3, HEAP, lsl #32
    // 0x1298594: cmp             w3, NULL
    // 0x1298598: b.ne            #0x12985a4
    // 0x129859c: r3 = Null
    //     0x129859c: mov             x3, NULL
    // 0x12985a0: b               #0x12985b8
    // 0x12985a4: LoadField: r4 = r3->field_7
    //     0x12985a4: ldur            w4, [x3, #7]
    // 0x12985a8: cbnz            w4, #0x12985b4
    // 0x12985ac: r3 = false
    //     0x12985ac: add             x3, NULL, #0x30  ; false
    // 0x12985b0: b               #0x12985b8
    // 0x12985b4: r3 = true
    //     0x12985b4: add             x3, NULL, #0x20  ; true
    // 0x12985b8: cmp             w3, NULL
    // 0x12985bc: b.ne            #0x12985c4
    // 0x12985c0: r3 = false
    //     0x12985c0: add             x3, NULL, #0x30  ; false
    // 0x12985c4: stur            x3, [fp, #-0x40]
    // 0x12985c8: cmp             w1, NULL
    // 0x12985cc: b.ne            #0x12985d8
    // 0x12985d0: r1 = Null
    //     0x12985d0: mov             x1, NULL
    // 0x12985d4: b               #0x12985e4
    // 0x12985d8: LoadField: r4 = r1->field_7
    //     0x12985d8: ldur            w4, [x1, #7]
    // 0x12985dc: DecompressPointer r4
    //     0x12985dc: add             x4, x4, HEAP, lsl #32
    // 0x12985e0: mov             x1, x4
    // 0x12985e4: cmp             w1, NULL
    // 0x12985e8: b.ne            #0x12985f4
    // 0x12985ec: r4 = ""
    //     0x12985ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12985f0: b               #0x12985f8
    // 0x12985f4: mov             x4, x1
    // 0x12985f8: ldur            x1, [fp, #-0x10]
    // 0x12985fc: stur            x4, [fp, #-0x20]
    // 0x1298600: r0 = of()
    //     0x1298600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1298604: LoadField: r1 = r0->field_87
    //     0x1298604: ldur            w1, [x0, #0x87]
    // 0x1298608: DecompressPointer r1
    //     0x1298608: add             x1, x1, HEAP, lsl #32
    // 0x129860c: LoadField: r0 = r1->field_2b
    //     0x129860c: ldur            w0, [x1, #0x2b]
    // 0x1298610: DecompressPointer r0
    //     0x1298610: add             x0, x0, HEAP, lsl #32
    // 0x1298614: stur            x0, [fp, #-0x48]
    // 0x1298618: r1 = Instance_Color
    //     0x1298618: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x129861c: d0 = 0.700000
    //     0x129861c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1298620: ldr             d0, [x17, #0xf48]
    // 0x1298624: r0 = withOpacity()
    //     0x1298624: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1298628: r16 = 12.000000
    //     0x1298628: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x129862c: ldr             x16, [x16, #0x9e8]
    // 0x1298630: stp             x0, x16, [SP, #8]
    // 0x1298634: r16 = Instance_TextDecoration
    //     0x1298634: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0x1298638: ldr             x16, [x16, #0x10]
    // 0x129863c: str             x16, [SP]
    // 0x1298640: ldur            x1, [fp, #-0x48]
    // 0x1298644: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0x1298644: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0x1298648: ldr             x4, [x4, #0xe38]
    // 0x129864c: r0 = copyWith()
    //     0x129864c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1298650: stur            x0, [fp, #-0x48]
    // 0x1298654: r0 = Text()
    //     0x1298654: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1298658: mov             x1, x0
    // 0x129865c: ldur            x0, [fp, #-0x20]
    // 0x1298660: stur            x1, [fp, #-0x58]
    // 0x1298664: StoreField: r1->field_b = r0
    //     0x1298664: stur            w0, [x1, #0xb]
    // 0x1298668: ldur            x0, [fp, #-0x48]
    // 0x129866c: StoreField: r1->field_13 = r0
    //     0x129866c: stur            w0, [x1, #0x13]
    // 0x1298670: r0 = InkWell()
    //     0x1298670: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1298674: mov             x3, x0
    // 0x1298678: ldur            x0, [fp, #-0x58]
    // 0x129867c: stur            x3, [fp, #-0x20]
    // 0x1298680: StoreField: r3->field_b = r0
    //     0x1298680: stur            w0, [x3, #0xb]
    // 0x1298684: ldur            x2, [fp, #-0x28]
    // 0x1298688: r1 = Function '<anonymous closure>':.
    //     0x1298688: add             x1, PP, #0x48, lsl #12  ; [pp+0x482b8] AnonymousClosure: (0x12991fc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] ProductDetailGridItemView::build (0x12982f8)
    //     0x129868c: ldr             x1, [x1, #0x2b8]
    // 0x1298690: r0 = AllocateClosure()
    //     0x1298690: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1298694: mov             x1, x0
    // 0x1298698: ldur            x0, [fp, #-0x20]
    // 0x129869c: StoreField: r0->field_f = r1
    //     0x129869c: stur            w1, [x0, #0xf]
    // 0x12986a0: r1 = true
    //     0x12986a0: add             x1, NULL, #0x20  ; true
    // 0x12986a4: StoreField: r0->field_43 = r1
    //     0x12986a4: stur            w1, [x0, #0x43]
    // 0x12986a8: r2 = Instance_BoxShape
    //     0x12986a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x12986ac: ldr             x2, [x2, #0x80]
    // 0x12986b0: StoreField: r0->field_47 = r2
    //     0x12986b0: stur            w2, [x0, #0x47]
    // 0x12986b4: StoreField: r0->field_6f = r1
    //     0x12986b4: stur            w1, [x0, #0x6f]
    // 0x12986b8: r2 = false
    //     0x12986b8: add             x2, NULL, #0x30  ; false
    // 0x12986bc: StoreField: r0->field_73 = r2
    //     0x12986bc: stur            w2, [x0, #0x73]
    // 0x12986c0: StoreField: r0->field_83 = r1
    //     0x12986c0: stur            w1, [x0, #0x83]
    // 0x12986c4: StoreField: r0->field_7b = r2
    //     0x12986c4: stur            w2, [x0, #0x7b]
    // 0x12986c8: r0 = Visibility()
    //     0x12986c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x12986cc: mov             x1, x0
    // 0x12986d0: ldur            x0, [fp, #-0x20]
    // 0x12986d4: stur            x1, [fp, #-0x28]
    // 0x12986d8: StoreField: r1->field_b = r0
    //     0x12986d8: stur            w0, [x1, #0xb]
    // 0x12986dc: r0 = Instance_SizedBox
    //     0x12986dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x12986e0: StoreField: r1->field_f = r0
    //     0x12986e0: stur            w0, [x1, #0xf]
    // 0x12986e4: ldur            x0, [fp, #-0x40]
    // 0x12986e8: StoreField: r1->field_13 = r0
    //     0x12986e8: stur            w0, [x1, #0x13]
    // 0x12986ec: r0 = false
    //     0x12986ec: add             x0, NULL, #0x30  ; false
    // 0x12986f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x12986f0: stur            w0, [x1, #0x17]
    // 0x12986f4: StoreField: r1->field_1b = r0
    //     0x12986f4: stur            w0, [x1, #0x1b]
    // 0x12986f8: StoreField: r1->field_1f = r0
    //     0x12986f8: stur            w0, [x1, #0x1f]
    // 0x12986fc: StoreField: r1->field_23 = r0
    //     0x12986fc: stur            w0, [x1, #0x23]
    // 0x1298700: StoreField: r1->field_27 = r0
    //     0x1298700: stur            w0, [x1, #0x27]
    // 0x1298704: StoreField: r1->field_2b = r0
    //     0x1298704: stur            w0, [x1, #0x2b]
    // 0x1298708: r0 = Padding()
    //     0x1298708: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x129870c: mov             x2, x0
    // 0x1298710: r0 = Instance_EdgeInsets
    //     0x1298710: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x1298714: ldr             x0, [x0, #0x770]
    // 0x1298718: stur            x2, [fp, #-0x20]
    // 0x129871c: StoreField: r2->field_f = r0
    //     0x129871c: stur            w0, [x2, #0xf]
    // 0x1298720: ldur            x0, [fp, #-0x28]
    // 0x1298724: StoreField: r2->field_b = r0
    //     0x1298724: stur            w0, [x2, #0xb]
    // 0x1298728: ldur            x0, [fp, #-0x38]
    // 0x129872c: LoadField: r1 = r0->field_b
    //     0x129872c: ldur            w1, [x0, #0xb]
    // 0x1298730: LoadField: r3 = r0->field_f
    //     0x1298730: ldur            w3, [x0, #0xf]
    // 0x1298734: DecompressPointer r3
    //     0x1298734: add             x3, x3, HEAP, lsl #32
    // 0x1298738: LoadField: r4 = r3->field_b
    //     0x1298738: ldur            w4, [x3, #0xb]
    // 0x129873c: r3 = LoadInt32Instr(r1)
    //     0x129873c: sbfx            x3, x1, #1, #0x1f
    // 0x1298740: stur            x3, [fp, #-0x50]
    // 0x1298744: r1 = LoadInt32Instr(r4)
    //     0x1298744: sbfx            x1, x4, #1, #0x1f
    // 0x1298748: cmp             x3, x1
    // 0x129874c: b.ne            #0x1298758
    // 0x1298750: mov             x1, x0
    // 0x1298754: r0 = _growToNextCapacity()
    //     0x1298754: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1298758: ldur            x4, [fp, #-8]
    // 0x129875c: ldur            x3, [fp, #-0x38]
    // 0x1298760: ldur            x2, [fp, #-0x50]
    // 0x1298764: add             x5, x2, #1
    // 0x1298768: lsl             x0, x5, #1
    // 0x129876c: StoreField: r3->field_b = r0
    //     0x129876c: stur            w0, [x3, #0xb]
    // 0x1298770: LoadField: r6 = r3->field_f
    //     0x1298770: ldur            w6, [x3, #0xf]
    // 0x1298774: DecompressPointer r6
    //     0x1298774: add             x6, x6, HEAP, lsl #32
    // 0x1298778: mov             x1, x6
    // 0x129877c: ldur            x0, [fp, #-0x20]
    // 0x1298780: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1298780: add             x25, x1, x2, lsl #2
    //     0x1298784: add             x25, x25, #0xf
    //     0x1298788: str             w0, [x25]
    //     0x129878c: tbz             w0, #0, #0x12987a8
    //     0x1298790: ldurb           w16, [x1, #-1]
    //     0x1298794: ldurb           w17, [x0, #-1]
    //     0x1298798: and             x16, x17, x16, lsr #2
    //     0x129879c: tst             x16, HEAP, lsr #32
    //     0x12987a0: b.eq            #0x12987a8
    //     0x12987a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12987a8: LoadField: r0 = r4->field_33
    //     0x12987a8: ldur            w0, [x4, #0x33]
    // 0x12987ac: DecompressPointer r0
    //     0x12987ac: add             x0, x0, HEAP, lsl #32
    // 0x12987b0: stur            x0, [fp, #-0x20]
    // 0x12987b4: LoadField: r1 = r0->field_f
    //     0x12987b4: ldur            w1, [x0, #0xf]
    // 0x12987b8: DecompressPointer r1
    //     0x12987b8: add             x1, x1, HEAP, lsl #32
    // 0x12987bc: cmp             w1, NULL
    // 0x12987c0: b.eq            #0x1298864
    // 0x12987c4: mov             x1, x4
    // 0x12987c8: ldur            x2, [fp, #-0x10]
    // 0x12987cc: r0 = _buildBumperCouponWidget()
    //     0x12987cc: bl              #0x1298a70  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_detail_grid_item_view.dart] ProductDetailGridItemView::_buildBumperCouponWidget
    // 0x12987d0: mov             x2, x0
    // 0x12987d4: ldur            x0, [fp, #-0x38]
    // 0x12987d8: stur            x2, [fp, #-0x10]
    // 0x12987dc: LoadField: r1 = r0->field_b
    //     0x12987dc: ldur            w1, [x0, #0xb]
    // 0x12987e0: LoadField: r3 = r0->field_f
    //     0x12987e0: ldur            w3, [x0, #0xf]
    // 0x12987e4: DecompressPointer r3
    //     0x12987e4: add             x3, x3, HEAP, lsl #32
    // 0x12987e8: LoadField: r4 = r3->field_b
    //     0x12987e8: ldur            w4, [x3, #0xb]
    // 0x12987ec: r3 = LoadInt32Instr(r1)
    //     0x12987ec: sbfx            x3, x1, #1, #0x1f
    // 0x12987f0: stur            x3, [fp, #-0x50]
    // 0x12987f4: r1 = LoadInt32Instr(r4)
    //     0x12987f4: sbfx            x1, x4, #1, #0x1f
    // 0x12987f8: cmp             x3, x1
    // 0x12987fc: b.ne            #0x1298808
    // 0x1298800: mov             x1, x0
    // 0x1298804: r0 = _growToNextCapacity()
    //     0x1298804: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1298808: ldur            x2, [fp, #-0x38]
    // 0x129880c: ldur            x3, [fp, #-0x50]
    // 0x1298810: add             x4, x3, #1
    // 0x1298814: lsl             x0, x4, #1
    // 0x1298818: StoreField: r2->field_b = r0
    //     0x1298818: stur            w0, [x2, #0xb]
    // 0x129881c: LoadField: r5 = r2->field_f
    //     0x129881c: ldur            w5, [x2, #0xf]
    // 0x1298820: DecompressPointer r5
    //     0x1298820: add             x5, x5, HEAP, lsl #32
    // 0x1298824: mov             x1, x5
    // 0x1298828: ldur            x0, [fp, #-0x10]
    // 0x129882c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x129882c: add             x25, x1, x3, lsl #2
    //     0x1298830: add             x25, x25, #0xf
    //     0x1298834: str             w0, [x25]
    //     0x1298838: tbz             w0, #0, #0x1298854
    //     0x129883c: ldurb           w16, [x1, #-1]
    //     0x1298840: ldurb           w17, [x0, #-1]
    //     0x1298844: and             x16, x17, x16, lsr #2
    //     0x1298848: tst             x16, HEAP, lsr #32
    //     0x129884c: b.eq            #0x1298854
    //     0x1298850: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1298854: mov             x16, x5
    // 0x1298858: mov             x5, x4
    // 0x129885c: mov             x4, x16
    // 0x1298860: b               #0x129886c
    // 0x1298864: mov             x2, x3
    // 0x1298868: mov             x4, x6
    // 0x129886c: ldur            x0, [fp, #-8]
    // 0x1298870: ldur            x3, [fp, #-0x30]
    // 0x1298874: ldur            x1, [fp, #-0x20]
    // 0x1298878: stur            x5, [fp, #-0x50]
    // 0x129887c: stur            x4, [fp, #-0x80]
    // 0x1298880: LoadField: r6 = r0->field_b
    //     0x1298880: ldur            w6, [x0, #0xb]
    // 0x1298884: DecompressPointer r6
    //     0x1298884: add             x6, x6, HEAP, lsl #32
    // 0x1298888: stur            x6, [fp, #-0x78]
    // 0x129888c: LoadField: r7 = r0->field_1b
    //     0x129888c: ldur            w7, [x0, #0x1b]
    // 0x1298890: DecompressPointer r7
    //     0x1298890: add             x7, x7, HEAP, lsl #32
    // 0x1298894: stur            x7, [fp, #-0x70]
    // 0x1298898: LoadField: r8 = r0->field_27
    //     0x1298898: ldur            w8, [x0, #0x27]
    // 0x129889c: DecompressPointer r8
    //     0x129889c: add             x8, x8, HEAP, lsl #32
    // 0x12988a0: stur            x8, [fp, #-0x68]
    // 0x12988a4: LoadField: r9 = r0->field_23
    //     0x12988a4: ldur            w9, [x0, #0x23]
    // 0x12988a8: DecompressPointer r9
    //     0x12988a8: add             x9, x9, HEAP, lsl #32
    // 0x12988ac: stur            x9, [fp, #-0x60]
    // 0x12988b0: LoadField: r10 = r0->field_2b
    //     0x12988b0: ldur            w10, [x0, #0x2b]
    // 0x12988b4: DecompressPointer r10
    //     0x12988b4: add             x10, x10, HEAP, lsl #32
    // 0x12988b8: stur            x10, [fp, #-0x58]
    // 0x12988bc: LoadField: r11 = r0->field_3b
    //     0x12988bc: ldur            w11, [x0, #0x3b]
    // 0x12988c0: DecompressPointer r11
    //     0x12988c0: add             x11, x11, HEAP, lsl #32
    // 0x12988c4: stur            x11, [fp, #-0x48]
    // 0x12988c8: LoadField: r12 = r0->field_43
    //     0x12988c8: ldur            w12, [x0, #0x43]
    // 0x12988cc: DecompressPointer r12
    //     0x12988cc: add             x12, x12, HEAP, lsl #32
    // 0x12988d0: stur            x12, [fp, #-0x40]
    // 0x12988d4: LoadField: r13 = r0->field_37
    //     0x12988d4: ldur            w13, [x0, #0x37]
    // 0x12988d8: DecompressPointer r13
    //     0x12988d8: add             x13, x13, HEAP, lsl #32
    // 0x12988dc: stur            x13, [fp, #-0x28]
    // 0x12988e0: LoadField: r14 = r0->field_47
    //     0x12988e0: ldur            w14, [x0, #0x47]
    // 0x12988e4: DecompressPointer r14
    //     0x12988e4: add             x14, x14, HEAP, lsl #32
    // 0x12988e8: stur            x14, [fp, #-0x10]
    // 0x12988ec: r0 = _ProductGridItem()
    //     0x12988ec: bl              #0x1298a64  ; Allocate_ProductGridItemStub -> _ProductGridItem (size=0x40)
    // 0x12988f0: mov             x2, x0
    // 0x12988f4: ldur            x0, [fp, #-0x78]
    // 0x12988f8: stur            x2, [fp, #-8]
    // 0x12988fc: StoreField: r2->field_b = r0
    //     0x12988fc: stur            w0, [x2, #0xb]
    // 0x1298900: ldur            x0, [fp, #-0x70]
    // 0x1298904: StoreField: r2->field_f = r0
    //     0x1298904: stur            w0, [x2, #0xf]
    // 0x1298908: ldur            x0, [fp, #-0x30]
    // 0x129890c: ArrayStore: r2[0] = r0  ; List_4
    //     0x129890c: stur            w0, [x2, #0x17]
    // 0x1298910: ldur            x0, [fp, #-0x68]
    // 0x1298914: StoreField: r2->field_1b = r0
    //     0x1298914: stur            w0, [x2, #0x1b]
    // 0x1298918: r0 = true
    //     0x1298918: add             x0, NULL, #0x20  ; true
    // 0x129891c: StoreField: r2->field_27 = r0
    //     0x129891c: stur            w0, [x2, #0x27]
    // 0x1298920: ldur            x0, [fp, #-0x60]
    // 0x1298924: StoreField: r2->field_13 = r0
    //     0x1298924: stur            w0, [x2, #0x13]
    // 0x1298928: ldur            x0, [fp, #-0x58]
    // 0x129892c: StoreField: r2->field_1f = r0
    //     0x129892c: stur            w0, [x2, #0x1f]
    // 0x1298930: r0 = "product_page"
    //     0x1298930: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1298934: ldr             x0, [x0, #0x480]
    // 0x1298938: StoreField: r2->field_23 = r0
    //     0x1298938: stur            w0, [x2, #0x23]
    // 0x129893c: ldur            x0, [fp, #-0x20]
    // 0x1298940: StoreField: r2->field_2b = r0
    //     0x1298940: stur            w0, [x2, #0x2b]
    // 0x1298944: ldur            x0, [fp, #-0x48]
    // 0x1298948: StoreField: r2->field_2f = r0
    //     0x1298948: stur            w0, [x2, #0x2f]
    // 0x129894c: ldur            x0, [fp, #-0x40]
    // 0x1298950: StoreField: r2->field_33 = r0
    //     0x1298950: stur            w0, [x2, #0x33]
    // 0x1298954: ldur            x0, [fp, #-0x28]
    // 0x1298958: StoreField: r2->field_37 = r0
    //     0x1298958: stur            w0, [x2, #0x37]
    // 0x129895c: ldur            x0, [fp, #-0x10]
    // 0x1298960: StoreField: r2->field_3b = r0
    //     0x1298960: stur            w0, [x2, #0x3b]
    // 0x1298964: ldur            x0, [fp, #-0x80]
    // 0x1298968: LoadField: r1 = r0->field_b
    //     0x1298968: ldur            w1, [x0, #0xb]
    // 0x129896c: r0 = LoadInt32Instr(r1)
    //     0x129896c: sbfx            x0, x1, #1, #0x1f
    // 0x1298970: ldur            x3, [fp, #-0x50]
    // 0x1298974: cmp             x3, x0
    // 0x1298978: b.ne            #0x1298984
    // 0x129897c: ldur            x1, [fp, #-0x38]
    // 0x1298980: r0 = _growToNextCapacity()
    //     0x1298980: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1298984: ldur            x3, [fp, #-0x38]
    // 0x1298988: ldur            x4, [fp, #-0x18]
    // 0x129898c: ldur            x2, [fp, #-0x50]
    // 0x1298990: add             x0, x2, #1
    // 0x1298994: lsl             x1, x0, #1
    // 0x1298998: StoreField: r3->field_b = r1
    //     0x1298998: stur            w1, [x3, #0xb]
    // 0x129899c: mov             x1, x2
    // 0x12989a0: cmp             x1, x0
    // 0x12989a4: b.hs            #0x1298a60
    // 0x12989a8: LoadField: r1 = r3->field_f
    //     0x12989a8: ldur            w1, [x3, #0xf]
    // 0x12989ac: DecompressPointer r1
    //     0x12989ac: add             x1, x1, HEAP, lsl #32
    // 0x12989b0: ldur            x0, [fp, #-8]
    // 0x12989b4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x12989b4: add             x25, x1, x2, lsl #2
    //     0x12989b8: add             x25, x25, #0xf
    //     0x12989bc: str             w0, [x25]
    //     0x12989c0: tbz             w0, #0, #0x12989dc
    //     0x12989c4: ldurb           w16, [x1, #-1]
    //     0x12989c8: ldurb           w17, [x0, #-1]
    //     0x12989cc: and             x16, x17, x16, lsr #2
    //     0x12989d0: tst             x16, HEAP, lsr #32
    //     0x12989d4: b.eq            #0x12989dc
    //     0x12989d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12989dc: r0 = Column()
    //     0x12989dc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x12989e0: mov             x1, x0
    // 0x12989e4: r0 = Instance_Axis
    //     0x12989e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x12989e8: stur            x1, [fp, #-8]
    // 0x12989ec: StoreField: r1->field_f = r0
    //     0x12989ec: stur            w0, [x1, #0xf]
    // 0x12989f0: r0 = Instance_MainAxisAlignment
    //     0x12989f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x12989f4: ldr             x0, [x0, #0xab0]
    // 0x12989f8: StoreField: r1->field_13 = r0
    //     0x12989f8: stur            w0, [x1, #0x13]
    // 0x12989fc: r0 = Instance_MainAxisSize
    //     0x12989fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1298a00: ldr             x0, [x0, #0xa10]
    // 0x1298a04: ArrayStore: r1[0] = r0  ; List_4
    //     0x1298a04: stur            w0, [x1, #0x17]
    // 0x1298a08: ldur            x0, [fp, #-0x18]
    // 0x1298a0c: StoreField: r1->field_1b = r0
    //     0x1298a0c: stur            w0, [x1, #0x1b]
    // 0x1298a10: r0 = Instance_VerticalDirection
    //     0x1298a10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1298a14: ldr             x0, [x0, #0xa20]
    // 0x1298a18: StoreField: r1->field_23 = r0
    //     0x1298a18: stur            w0, [x1, #0x23]
    // 0x1298a1c: r0 = Instance_Clip
    //     0x1298a1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1298a20: ldr             x0, [x0, #0x38]
    // 0x1298a24: StoreField: r1->field_2b = r0
    //     0x1298a24: stur            w0, [x1, #0x2b]
    // 0x1298a28: StoreField: r1->field_2f = rZR
    //     0x1298a28: stur            xzr, [x1, #0x2f]
    // 0x1298a2c: ldur            x0, [fp, #-0x38]
    // 0x1298a30: StoreField: r1->field_b = r0
    //     0x1298a30: stur            w0, [x1, #0xb]
    // 0x1298a34: r0 = Padding()
    //     0x1298a34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1298a38: r1 = Instance_EdgeInsets
    //     0x1298a38: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0x1298a3c: ldr             x1, [x1, #0x110]
    // 0x1298a40: StoreField: r0->field_f = r1
    //     0x1298a40: stur            w1, [x0, #0xf]
    // 0x1298a44: ldur            x1, [fp, #-8]
    // 0x1298a48: StoreField: r0->field_b = r1
    //     0x1298a48: stur            w1, [x0, #0xb]
    // 0x1298a4c: LeaveFrame
    //     0x1298a4c: mov             SP, fp
    //     0x1298a50: ldp             fp, lr, [SP], #0x10
    // 0x1298a54: ret
    //     0x1298a54: ret             
    // 0x1298a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1298a58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1298a5c: b               #0x1298320
    // 0x1298a60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1298a60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildBumperCouponWidget(/* No info */) {
    // ** addr: 0x1298a70, size: 0x78c
    // 0x1298a70: EnterFrame
    //     0x1298a70: stp             fp, lr, [SP, #-0x10]!
    //     0x1298a74: mov             fp, SP
    // 0x1298a78: AllocStack(0x68)
    //     0x1298a78: sub             SP, SP, #0x68
    // 0x1298a7c: SetupParameters(ProductDetailGridItemView this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x30 */)
    //     0x1298a7c: mov             x0, x1
    //     0x1298a80: mov             x1, x2
    //     0x1298a84: stur            x2, [fp, #-0x30]
    // 0x1298a88: CheckStackOverflow
    //     0x1298a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1298a8c: cmp             SP, x16
    //     0x1298a90: b.ls            #0x12991f4
    // 0x1298a94: LoadField: r2 = r0->field_33
    //     0x1298a94: ldur            w2, [x0, #0x33]
    // 0x1298a98: DecompressPointer r2
    //     0x1298a98: add             x2, x2, HEAP, lsl #32
    // 0x1298a9c: stur            x2, [fp, #-0x28]
    // 0x1298aa0: LoadField: r0 = r2->field_13
    //     0x1298aa0: ldur            w0, [x2, #0x13]
    // 0x1298aa4: DecompressPointer r0
    //     0x1298aa4: add             x0, x0, HEAP, lsl #32
    // 0x1298aa8: stur            x0, [fp, #-0x20]
    // 0x1298aac: cmp             w0, NULL
    // 0x1298ab0: b.ne            #0x1298abc
    // 0x1298ab4: r3 = Null
    //     0x1298ab4: mov             x3, NULL
    // 0x1298ab8: b               #0x1298ac4
    // 0x1298abc: LoadField: r3 = r0->field_7
    //     0x1298abc: ldur            w3, [x0, #7]
    // 0x1298ac0: DecompressPointer r3
    //     0x1298ac0: add             x3, x3, HEAP, lsl #32
    // 0x1298ac4: cmp             w3, NULL
    // 0x1298ac8: b.ne            #0x1298ad4
    // 0x1298acc: r3 = 0
    //     0x1298acc: movz            x3, #0
    // 0x1298ad0: b               #0x1298ae4
    // 0x1298ad4: r4 = LoadInt32Instr(r3)
    //     0x1298ad4: sbfx            x4, x3, #1, #0x1f
    //     0x1298ad8: tbz             w3, #0, #0x1298ae0
    //     0x1298adc: ldur            x4, [x3, #7]
    // 0x1298ae0: mov             x3, x4
    // 0x1298ae4: stur            x3, [fp, #-0x18]
    // 0x1298ae8: cmp             w0, NULL
    // 0x1298aec: b.ne            #0x1298af8
    // 0x1298af0: r4 = Null
    //     0x1298af0: mov             x4, NULL
    // 0x1298af4: b               #0x1298b00
    // 0x1298af8: LoadField: r4 = r0->field_b
    //     0x1298af8: ldur            w4, [x0, #0xb]
    // 0x1298afc: DecompressPointer r4
    //     0x1298afc: add             x4, x4, HEAP, lsl #32
    // 0x1298b00: cmp             w4, NULL
    // 0x1298b04: b.ne            #0x1298b10
    // 0x1298b08: r4 = 0
    //     0x1298b08: movz            x4, #0
    // 0x1298b0c: b               #0x1298b20
    // 0x1298b10: r5 = LoadInt32Instr(r4)
    //     0x1298b10: sbfx            x5, x4, #1, #0x1f
    //     0x1298b14: tbz             w4, #0, #0x1298b1c
    //     0x1298b18: ldur            x5, [x4, #7]
    // 0x1298b1c: mov             x4, x5
    // 0x1298b20: stur            x4, [fp, #-0x10]
    // 0x1298b24: cmp             w0, NULL
    // 0x1298b28: b.ne            #0x1298b34
    // 0x1298b2c: r5 = Null
    //     0x1298b2c: mov             x5, NULL
    // 0x1298b30: b               #0x1298b3c
    // 0x1298b34: LoadField: r5 = r0->field_f
    //     0x1298b34: ldur            w5, [x0, #0xf]
    // 0x1298b38: DecompressPointer r5
    //     0x1298b38: add             x5, x5, HEAP, lsl #32
    // 0x1298b3c: cmp             w5, NULL
    // 0x1298b40: b.ne            #0x1298b4c
    // 0x1298b44: r5 = 0
    //     0x1298b44: movz            x5, #0
    // 0x1298b48: b               #0x1298b5c
    // 0x1298b4c: r6 = LoadInt32Instr(r5)
    //     0x1298b4c: sbfx            x6, x5, #1, #0x1f
    //     0x1298b50: tbz             w5, #0, #0x1298b58
    //     0x1298b54: ldur            x6, [x5, #7]
    // 0x1298b58: mov             x5, x6
    // 0x1298b5c: stur            x5, [fp, #-8]
    // 0x1298b60: r0 = Color()
    //     0x1298b60: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1298b64: mov             x1, x0
    // 0x1298b68: r0 = Instance_ColorSpace
    //     0x1298b68: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1298b6c: stur            x1, [fp, #-0x38]
    // 0x1298b70: StoreField: r1->field_27 = r0
    //     0x1298b70: stur            w0, [x1, #0x27]
    // 0x1298b74: d0 = 1.000000
    //     0x1298b74: fmov            d0, #1.00000000
    // 0x1298b78: StoreField: r1->field_7 = d0
    //     0x1298b78: stur            d0, [x1, #7]
    // 0x1298b7c: ldur            x2, [fp, #-0x18]
    // 0x1298b80: ubfx            x2, x2, #0, #0x20
    // 0x1298b84: and             w3, w2, #0xff
    // 0x1298b88: ubfx            x3, x3, #0, #0x20
    // 0x1298b8c: scvtf           d1, x3
    // 0x1298b90: d2 = 255.000000
    //     0x1298b90: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1298b94: fdiv            d3, d1, d2
    // 0x1298b98: StoreField: r1->field_f = d3
    //     0x1298b98: stur            d3, [x1, #0xf]
    // 0x1298b9c: ldur            x2, [fp, #-0x10]
    // 0x1298ba0: ubfx            x2, x2, #0, #0x20
    // 0x1298ba4: and             w3, w2, #0xff
    // 0x1298ba8: ubfx            x3, x3, #0, #0x20
    // 0x1298bac: scvtf           d1, x3
    // 0x1298bb0: fdiv            d3, d1, d2
    // 0x1298bb4: ArrayStore: r1[0] = d3  ; List_8
    //     0x1298bb4: stur            d3, [x1, #0x17]
    // 0x1298bb8: ldur            x2, [fp, #-8]
    // 0x1298bbc: ubfx            x2, x2, #0, #0x20
    // 0x1298bc0: and             w3, w2, #0xff
    // 0x1298bc4: ubfx            x3, x3, #0, #0x20
    // 0x1298bc8: scvtf           d1, x3
    // 0x1298bcc: fdiv            d3, d1, d2
    // 0x1298bd0: StoreField: r1->field_1f = d3
    //     0x1298bd0: stur            d3, [x1, #0x1f]
    // 0x1298bd4: ldur            x2, [fp, #-0x20]
    // 0x1298bd8: cmp             w2, NULL
    // 0x1298bdc: b.ne            #0x1298be8
    // 0x1298be0: r3 = Null
    //     0x1298be0: mov             x3, NULL
    // 0x1298be4: b               #0x1298bf0
    // 0x1298be8: LoadField: r3 = r2->field_7
    //     0x1298be8: ldur            w3, [x2, #7]
    // 0x1298bec: DecompressPointer r3
    //     0x1298bec: add             x3, x3, HEAP, lsl #32
    // 0x1298bf0: cmp             w3, NULL
    // 0x1298bf4: b.ne            #0x1298c00
    // 0x1298bf8: r3 = 0
    //     0x1298bf8: movz            x3, #0
    // 0x1298bfc: b               #0x1298c10
    // 0x1298c00: r4 = LoadInt32Instr(r3)
    //     0x1298c00: sbfx            x4, x3, #1, #0x1f
    //     0x1298c04: tbz             w3, #0, #0x1298c0c
    //     0x1298c08: ldur            x4, [x3, #7]
    // 0x1298c0c: mov             x3, x4
    // 0x1298c10: stur            x3, [fp, #-0x18]
    // 0x1298c14: cmp             w2, NULL
    // 0x1298c18: b.ne            #0x1298c24
    // 0x1298c1c: r4 = Null
    //     0x1298c1c: mov             x4, NULL
    // 0x1298c20: b               #0x1298c2c
    // 0x1298c24: LoadField: r4 = r2->field_b
    //     0x1298c24: ldur            w4, [x2, #0xb]
    // 0x1298c28: DecompressPointer r4
    //     0x1298c28: add             x4, x4, HEAP, lsl #32
    // 0x1298c2c: cmp             w4, NULL
    // 0x1298c30: b.ne            #0x1298c3c
    // 0x1298c34: r4 = 0
    //     0x1298c34: movz            x4, #0
    // 0x1298c38: b               #0x1298c4c
    // 0x1298c3c: r5 = LoadInt32Instr(r4)
    //     0x1298c3c: sbfx            x5, x4, #1, #0x1f
    //     0x1298c40: tbz             w4, #0, #0x1298c48
    //     0x1298c44: ldur            x5, [x4, #7]
    // 0x1298c48: mov             x4, x5
    // 0x1298c4c: stur            x4, [fp, #-0x10]
    // 0x1298c50: cmp             w2, NULL
    // 0x1298c54: b.ne            #0x1298c60
    // 0x1298c58: r2 = Null
    //     0x1298c58: mov             x2, NULL
    // 0x1298c5c: b               #0x1298c6c
    // 0x1298c60: LoadField: r5 = r2->field_f
    //     0x1298c60: ldur            w5, [x2, #0xf]
    // 0x1298c64: DecompressPointer r5
    //     0x1298c64: add             x5, x5, HEAP, lsl #32
    // 0x1298c68: mov             x2, x5
    // 0x1298c6c: cmp             w2, NULL
    // 0x1298c70: b.ne            #0x1298c7c
    // 0x1298c74: r5 = 0
    //     0x1298c74: movz            x5, #0
    // 0x1298c78: b               #0x1298c88
    // 0x1298c7c: r5 = LoadInt32Instr(r2)
    //     0x1298c7c: sbfx            x5, x2, #1, #0x1f
    //     0x1298c80: tbz             w2, #0, #0x1298c88
    //     0x1298c84: ldur            x5, [x2, #7]
    // 0x1298c88: ldur            x2, [fp, #-0x28]
    // 0x1298c8c: stur            x5, [fp, #-8]
    // 0x1298c90: r0 = Color()
    //     0x1298c90: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1298c94: mov             x3, x0
    // 0x1298c98: r0 = Instance_ColorSpace
    //     0x1298c98: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1298c9c: stur            x3, [fp, #-0x20]
    // 0x1298ca0: StoreField: r3->field_27 = r0
    //     0x1298ca0: stur            w0, [x3, #0x27]
    // 0x1298ca4: d0 = 0.700000
    //     0x1298ca4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1298ca8: ldr             d0, [x17, #0xf48]
    // 0x1298cac: StoreField: r3->field_7 = d0
    //     0x1298cac: stur            d0, [x3, #7]
    // 0x1298cb0: ldur            x0, [fp, #-0x18]
    // 0x1298cb4: ubfx            x0, x0, #0, #0x20
    // 0x1298cb8: and             w1, w0, #0xff
    // 0x1298cbc: ubfx            x1, x1, #0, #0x20
    // 0x1298cc0: scvtf           d0, x1
    // 0x1298cc4: d1 = 255.000000
    //     0x1298cc4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1298cc8: fdiv            d2, d0, d1
    // 0x1298ccc: StoreField: r3->field_f = d2
    //     0x1298ccc: stur            d2, [x3, #0xf]
    // 0x1298cd0: ldur            x0, [fp, #-0x10]
    // 0x1298cd4: ubfx            x0, x0, #0, #0x20
    // 0x1298cd8: and             w1, w0, #0xff
    // 0x1298cdc: ubfx            x1, x1, #0, #0x20
    // 0x1298ce0: scvtf           d0, x1
    // 0x1298ce4: fdiv            d2, d0, d1
    // 0x1298ce8: ArrayStore: r3[0] = d2  ; List_8
    //     0x1298ce8: stur            d2, [x3, #0x17]
    // 0x1298cec: ldur            x0, [fp, #-8]
    // 0x1298cf0: ubfx            x0, x0, #0, #0x20
    // 0x1298cf4: and             w1, w0, #0xff
    // 0x1298cf8: ubfx            x1, x1, #0, #0x20
    // 0x1298cfc: scvtf           d0, x1
    // 0x1298d00: fdiv            d2, d0, d1
    // 0x1298d04: StoreField: r3->field_1f = d2
    //     0x1298d04: stur            d2, [x3, #0x1f]
    // 0x1298d08: r1 = Null
    //     0x1298d08: mov             x1, NULL
    // 0x1298d0c: r2 = 4
    //     0x1298d0c: movz            x2, #0x4
    // 0x1298d10: r0 = AllocateArray()
    //     0x1298d10: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1298d14: mov             x2, x0
    // 0x1298d18: ldur            x0, [fp, #-0x38]
    // 0x1298d1c: stur            x2, [fp, #-0x40]
    // 0x1298d20: StoreField: r2->field_f = r0
    //     0x1298d20: stur            w0, [x2, #0xf]
    // 0x1298d24: ldur            x0, [fp, #-0x20]
    // 0x1298d28: StoreField: r2->field_13 = r0
    //     0x1298d28: stur            w0, [x2, #0x13]
    // 0x1298d2c: r1 = <Color>
    //     0x1298d2c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1298d30: ldr             x1, [x1, #0xf80]
    // 0x1298d34: r0 = AllocateGrowableArray()
    //     0x1298d34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1298d38: mov             x1, x0
    // 0x1298d3c: ldur            x0, [fp, #-0x40]
    // 0x1298d40: stur            x1, [fp, #-0x20]
    // 0x1298d44: StoreField: r1->field_f = r0
    //     0x1298d44: stur            w0, [x1, #0xf]
    // 0x1298d48: r2 = 4
    //     0x1298d48: movz            x2, #0x4
    // 0x1298d4c: StoreField: r1->field_b = r2
    //     0x1298d4c: stur            w2, [x1, #0xb]
    // 0x1298d50: r0 = LinearGradient()
    //     0x1298d50: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0x1298d54: mov             x1, x0
    // 0x1298d58: r0 = Instance_Alignment
    //     0x1298d58: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x1298d5c: ldr             x0, [x0, #0xce0]
    // 0x1298d60: stur            x1, [fp, #-0x38]
    // 0x1298d64: StoreField: r1->field_13 = r0
    //     0x1298d64: stur            w0, [x1, #0x13]
    // 0x1298d68: r0 = Instance_Alignment
    //     0x1298d68: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0x1298d6c: ldr             x0, [x0, #0xce8]
    // 0x1298d70: ArrayStore: r1[0] = r0  ; List_4
    //     0x1298d70: stur            w0, [x1, #0x17]
    // 0x1298d74: r0 = Instance_TileMode
    //     0x1298d74: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0x1298d78: ldr             x0, [x0, #0xcf0]
    // 0x1298d7c: StoreField: r1->field_1b = r0
    //     0x1298d7c: stur            w0, [x1, #0x1b]
    // 0x1298d80: ldur            x0, [fp, #-0x20]
    // 0x1298d84: StoreField: r1->field_7 = r0
    //     0x1298d84: stur            w0, [x1, #7]
    // 0x1298d88: r0 = BoxDecoration()
    //     0x1298d88: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1298d8c: mov             x2, x0
    // 0x1298d90: r0 = Instance_BorderRadius
    //     0x1298d90: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1298d94: ldr             x0, [x0, #0xf70]
    // 0x1298d98: stur            x2, [fp, #-0x20]
    // 0x1298d9c: StoreField: r2->field_13 = r0
    //     0x1298d9c: stur            w0, [x2, #0x13]
    // 0x1298da0: ldur            x0, [fp, #-0x38]
    // 0x1298da4: StoreField: r2->field_1b = r0
    //     0x1298da4: stur            w0, [x2, #0x1b]
    // 0x1298da8: r0 = Instance_BoxShape
    //     0x1298da8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1298dac: ldr             x0, [x0, #0x80]
    // 0x1298db0: StoreField: r2->field_23 = r0
    //     0x1298db0: stur            w0, [x2, #0x23]
    // 0x1298db4: ldur            x1, [fp, #-0x30]
    // 0x1298db8: r0 = of()
    //     0x1298db8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1298dbc: LoadField: r1 = r0->field_87
    //     0x1298dbc: ldur            w1, [x0, #0x87]
    // 0x1298dc0: DecompressPointer r1
    //     0x1298dc0: add             x1, x1, HEAP, lsl #32
    // 0x1298dc4: LoadField: r0 = r1->field_7
    //     0x1298dc4: ldur            w0, [x1, #7]
    // 0x1298dc8: DecompressPointer r0
    //     0x1298dc8: add             x0, x0, HEAP, lsl #32
    // 0x1298dcc: r16 = 16.000000
    //     0x1298dcc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1298dd0: ldr             x16, [x16, #0x188]
    // 0x1298dd4: r30 = Instance_Color
    //     0x1298dd4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1298dd8: stp             lr, x16, [SP]
    // 0x1298ddc: mov             x1, x0
    // 0x1298de0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1298de0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1298de4: ldr             x4, [x4, #0xaa0]
    // 0x1298de8: r0 = copyWith()
    //     0x1298de8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1298dec: stur            x0, [fp, #-0x38]
    // 0x1298df0: r0 = TextSpan()
    //     0x1298df0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1298df4: mov             x2, x0
    // 0x1298df8: r0 = "BUMPER OFFER\n"
    //     0x1298df8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0x1298dfc: ldr             x0, [x0, #0x338]
    // 0x1298e00: stur            x2, [fp, #-0x40]
    // 0x1298e04: StoreField: r2->field_b = r0
    //     0x1298e04: stur            w0, [x2, #0xb]
    // 0x1298e08: r0 = Instance__DeferringMouseCursor
    //     0x1298e08: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1298e0c: ArrayStore: r2[0] = r0  ; List_4
    //     0x1298e0c: stur            w0, [x2, #0x17]
    // 0x1298e10: ldur            x1, [fp, #-0x38]
    // 0x1298e14: StoreField: r2->field_7 = r1
    //     0x1298e14: stur            w1, [x2, #7]
    // 0x1298e18: ldur            x1, [fp, #-0x30]
    // 0x1298e1c: r0 = of()
    //     0x1298e1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1298e20: LoadField: r1 = r0->field_87
    //     0x1298e20: ldur            w1, [x0, #0x87]
    // 0x1298e24: DecompressPointer r1
    //     0x1298e24: add             x1, x1, HEAP, lsl #32
    // 0x1298e28: LoadField: r0 = r1->field_2b
    //     0x1298e28: ldur            w0, [x1, #0x2b]
    // 0x1298e2c: DecompressPointer r0
    //     0x1298e2c: add             x0, x0, HEAP, lsl #32
    // 0x1298e30: r16 = 12.000000
    //     0x1298e30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1298e34: ldr             x16, [x16, #0x9e8]
    // 0x1298e38: r30 = Instance_Color
    //     0x1298e38: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1298e3c: stp             lr, x16, [SP]
    // 0x1298e40: mov             x1, x0
    // 0x1298e44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1298e44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1298e48: ldr             x4, [x4, #0xaa0]
    // 0x1298e4c: r0 = copyWith()
    //     0x1298e4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1298e50: stur            x0, [fp, #-0x38]
    // 0x1298e54: r0 = TextSpan()
    //     0x1298e54: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1298e58: mov             x3, x0
    // 0x1298e5c: r0 = "Unlocked from your last order"
    //     0x1298e5c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0x1298e60: ldr             x0, [x0, #0x340]
    // 0x1298e64: stur            x3, [fp, #-0x48]
    // 0x1298e68: StoreField: r3->field_b = r0
    //     0x1298e68: stur            w0, [x3, #0xb]
    // 0x1298e6c: r0 = Instance__DeferringMouseCursor
    //     0x1298e6c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1298e70: ArrayStore: r3[0] = r0  ; List_4
    //     0x1298e70: stur            w0, [x3, #0x17]
    // 0x1298e74: ldur            x1, [fp, #-0x38]
    // 0x1298e78: StoreField: r3->field_7 = r1
    //     0x1298e78: stur            w1, [x3, #7]
    // 0x1298e7c: r1 = Null
    //     0x1298e7c: mov             x1, NULL
    // 0x1298e80: r2 = 4
    //     0x1298e80: movz            x2, #0x4
    // 0x1298e84: r0 = AllocateArray()
    //     0x1298e84: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1298e88: mov             x2, x0
    // 0x1298e8c: ldur            x0, [fp, #-0x40]
    // 0x1298e90: stur            x2, [fp, #-0x38]
    // 0x1298e94: StoreField: r2->field_f = r0
    //     0x1298e94: stur            w0, [x2, #0xf]
    // 0x1298e98: ldur            x0, [fp, #-0x48]
    // 0x1298e9c: StoreField: r2->field_13 = r0
    //     0x1298e9c: stur            w0, [x2, #0x13]
    // 0x1298ea0: r1 = <InlineSpan>
    //     0x1298ea0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1298ea4: ldr             x1, [x1, #0xe40]
    // 0x1298ea8: r0 = AllocateGrowableArray()
    //     0x1298ea8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1298eac: mov             x1, x0
    // 0x1298eb0: ldur            x0, [fp, #-0x38]
    // 0x1298eb4: stur            x1, [fp, #-0x40]
    // 0x1298eb8: StoreField: r1->field_f = r0
    //     0x1298eb8: stur            w0, [x1, #0xf]
    // 0x1298ebc: r2 = 4
    //     0x1298ebc: movz            x2, #0x4
    // 0x1298ec0: StoreField: r1->field_b = r2
    //     0x1298ec0: stur            w2, [x1, #0xb]
    // 0x1298ec4: r0 = TextSpan()
    //     0x1298ec4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1298ec8: mov             x1, x0
    // 0x1298ecc: ldur            x0, [fp, #-0x40]
    // 0x1298ed0: stur            x1, [fp, #-0x38]
    // 0x1298ed4: StoreField: r1->field_f = r0
    //     0x1298ed4: stur            w0, [x1, #0xf]
    // 0x1298ed8: r0 = Instance__DeferringMouseCursor
    //     0x1298ed8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1298edc: ArrayStore: r1[0] = r0  ; List_4
    //     0x1298edc: stur            w0, [x1, #0x17]
    // 0x1298ee0: r0 = RichText()
    //     0x1298ee0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1298ee4: mov             x1, x0
    // 0x1298ee8: ldur            x2, [fp, #-0x38]
    // 0x1298eec: stur            x0, [fp, #-0x38]
    // 0x1298ef0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1298ef0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1298ef4: r0 = RichText()
    //     0x1298ef4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1298ef8: r1 = Instance_Color
    //     0x1298ef8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1298efc: d0 = 0.500000
    //     0x1298efc: fmov            d0, #0.50000000
    // 0x1298f00: r0 = withOpacity()
    //     0x1298f00: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1298f04: stur            x0, [fp, #-0x40]
    // 0x1298f08: r0 = VerticalDivider()
    //     0x1298f08: bl              #0x99b78c  ; AllocateVerticalDividerStub -> VerticalDivider (size=0x28)
    // 0x1298f0c: d0 = 1.000000
    //     0x1298f0c: fmov            d0, #1.00000000
    // 0x1298f10: stur            x0, [fp, #-0x48]
    // 0x1298f14: StoreField: r0->field_f = d0
    //     0x1298f14: stur            d0, [x0, #0xf]
    // 0x1298f18: ldur            x1, [fp, #-0x40]
    // 0x1298f1c: StoreField: r0->field_1f = r1
    //     0x1298f1c: stur            w1, [x0, #0x1f]
    // 0x1298f20: ldur            x1, [fp, #-0x28]
    // 0x1298f24: LoadField: r3 = r1->field_7
    //     0x1298f24: ldur            w3, [x1, #7]
    // 0x1298f28: DecompressPointer r3
    //     0x1298f28: add             x3, x3, HEAP, lsl #32
    // 0x1298f2c: stur            x3, [fp, #-0x40]
    // 0x1298f30: r1 = Null
    //     0x1298f30: mov             x1, NULL
    // 0x1298f34: r2 = 4
    //     0x1298f34: movz            x2, #0x4
    // 0x1298f38: r0 = AllocateArray()
    //     0x1298f38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1298f3c: mov             x1, x0
    // 0x1298f40: ldur            x0, [fp, #-0x40]
    // 0x1298f44: StoreField: r1->field_f = r0
    //     0x1298f44: stur            w0, [x1, #0xf]
    // 0x1298f48: r16 = "\n"
    //     0x1298f48: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0x1298f4c: StoreField: r1->field_13 = r16
    //     0x1298f4c: stur            w16, [x1, #0x13]
    // 0x1298f50: str             x1, [SP]
    // 0x1298f54: r0 = _interpolate()
    //     0x1298f54: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1298f58: ldur            x1, [fp, #-0x30]
    // 0x1298f5c: stur            x0, [fp, #-0x28]
    // 0x1298f60: r0 = of()
    //     0x1298f60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1298f64: LoadField: r1 = r0->field_87
    //     0x1298f64: ldur            w1, [x0, #0x87]
    // 0x1298f68: DecompressPointer r1
    //     0x1298f68: add             x1, x1, HEAP, lsl #32
    // 0x1298f6c: LoadField: r0 = r1->field_7
    //     0x1298f6c: ldur            w0, [x1, #7]
    // 0x1298f70: DecompressPointer r0
    //     0x1298f70: add             x0, x0, HEAP, lsl #32
    // 0x1298f74: r16 = 32.000000
    //     0x1298f74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x1298f78: ldr             x16, [x16, #0x848]
    // 0x1298f7c: r30 = Instance_Color
    //     0x1298f7c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1298f80: stp             lr, x16, [SP]
    // 0x1298f84: mov             x1, x0
    // 0x1298f88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1298f88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1298f8c: ldr             x4, [x4, #0xaa0]
    // 0x1298f90: r0 = copyWith()
    //     0x1298f90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1298f94: ldur            x1, [fp, #-0x30]
    // 0x1298f98: stur            x0, [fp, #-0x30]
    // 0x1298f9c: r0 = of()
    //     0x1298f9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1298fa0: LoadField: r1 = r0->field_87
    //     0x1298fa0: ldur            w1, [x0, #0x87]
    // 0x1298fa4: DecompressPointer r1
    //     0x1298fa4: add             x1, x1, HEAP, lsl #32
    // 0x1298fa8: LoadField: r0 = r1->field_7
    //     0x1298fa8: ldur            w0, [x1, #7]
    // 0x1298fac: DecompressPointer r0
    //     0x1298fac: add             x0, x0, HEAP, lsl #32
    // 0x1298fb0: r16 = Instance_Color
    //     0x1298fb0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1298fb4: r30 = 16.000000
    //     0x1298fb4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1298fb8: ldr             lr, [lr, #0x188]
    // 0x1298fbc: stp             lr, x16, [SP]
    // 0x1298fc0: mov             x1, x0
    // 0x1298fc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1298fc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1298fc8: ldr             x4, [x4, #0x9b8]
    // 0x1298fcc: r0 = copyWith()
    //     0x1298fcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1298fd0: stur            x0, [fp, #-0x40]
    // 0x1298fd4: r0 = TextSpan()
    //     0x1298fd4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1298fd8: mov             x3, x0
    // 0x1298fdc: r0 = "OFF"
    //     0x1298fdc: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0x1298fe0: ldr             x0, [x0, #0x348]
    // 0x1298fe4: stur            x3, [fp, #-0x50]
    // 0x1298fe8: StoreField: r3->field_b = r0
    //     0x1298fe8: stur            w0, [x3, #0xb]
    // 0x1298fec: r0 = Instance__DeferringMouseCursor
    //     0x1298fec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1298ff0: ArrayStore: r3[0] = r0  ; List_4
    //     0x1298ff0: stur            w0, [x3, #0x17]
    // 0x1298ff4: ldur            x1, [fp, #-0x40]
    // 0x1298ff8: StoreField: r3->field_7 = r1
    //     0x1298ff8: stur            w1, [x3, #7]
    // 0x1298ffc: r1 = Null
    //     0x1298ffc: mov             x1, NULL
    // 0x1299000: r2 = 2
    //     0x1299000: movz            x2, #0x2
    // 0x1299004: r0 = AllocateArray()
    //     0x1299004: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1299008: mov             x2, x0
    // 0x129900c: ldur            x0, [fp, #-0x50]
    // 0x1299010: stur            x2, [fp, #-0x40]
    // 0x1299014: StoreField: r2->field_f = r0
    //     0x1299014: stur            w0, [x2, #0xf]
    // 0x1299018: r1 = <InlineSpan>
    //     0x1299018: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x129901c: ldr             x1, [x1, #0xe40]
    // 0x1299020: r0 = AllocateGrowableArray()
    //     0x1299020: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1299024: mov             x1, x0
    // 0x1299028: ldur            x0, [fp, #-0x40]
    // 0x129902c: stur            x1, [fp, #-0x50]
    // 0x1299030: StoreField: r1->field_f = r0
    //     0x1299030: stur            w0, [x1, #0xf]
    // 0x1299034: r0 = 2
    //     0x1299034: movz            x0, #0x2
    // 0x1299038: StoreField: r1->field_b = r0
    //     0x1299038: stur            w0, [x1, #0xb]
    // 0x129903c: r0 = TextSpan()
    //     0x129903c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1299040: mov             x1, x0
    // 0x1299044: ldur            x0, [fp, #-0x28]
    // 0x1299048: stur            x1, [fp, #-0x40]
    // 0x129904c: StoreField: r1->field_b = r0
    //     0x129904c: stur            w0, [x1, #0xb]
    // 0x1299050: ldur            x0, [fp, #-0x50]
    // 0x1299054: StoreField: r1->field_f = r0
    //     0x1299054: stur            w0, [x1, #0xf]
    // 0x1299058: r0 = Instance__DeferringMouseCursor
    //     0x1299058: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x129905c: ArrayStore: r1[0] = r0  ; List_4
    //     0x129905c: stur            w0, [x1, #0x17]
    // 0x1299060: ldur            x0, [fp, #-0x30]
    // 0x1299064: StoreField: r1->field_7 = r0
    //     0x1299064: stur            w0, [x1, #7]
    // 0x1299068: r0 = RichText()
    //     0x1299068: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x129906c: stur            x0, [fp, #-0x28]
    // 0x1299070: r16 = Instance_TextAlign
    //     0x1299070: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1299074: str             x16, [SP]
    // 0x1299078: mov             x1, x0
    // 0x129907c: ldur            x2, [fp, #-0x40]
    // 0x1299080: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0x1299080: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0x1299084: ldr             x4, [x4, #0x350]
    // 0x1299088: r0 = RichText()
    //     0x1299088: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x129908c: r1 = Null
    //     0x129908c: mov             x1, NULL
    // 0x1299090: r2 = 6
    //     0x1299090: movz            x2, #0x6
    // 0x1299094: r0 = AllocateArray()
    //     0x1299094: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1299098: mov             x2, x0
    // 0x129909c: ldur            x0, [fp, #-0x38]
    // 0x12990a0: stur            x2, [fp, #-0x30]
    // 0x12990a4: StoreField: r2->field_f = r0
    //     0x12990a4: stur            w0, [x2, #0xf]
    // 0x12990a8: ldur            x0, [fp, #-0x48]
    // 0x12990ac: StoreField: r2->field_13 = r0
    //     0x12990ac: stur            w0, [x2, #0x13]
    // 0x12990b0: ldur            x0, [fp, #-0x28]
    // 0x12990b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x12990b4: stur            w0, [x2, #0x17]
    // 0x12990b8: r1 = <Widget>
    //     0x12990b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12990bc: r0 = AllocateGrowableArray()
    //     0x12990bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12990c0: mov             x1, x0
    // 0x12990c4: ldur            x0, [fp, #-0x30]
    // 0x12990c8: stur            x1, [fp, #-0x28]
    // 0x12990cc: StoreField: r1->field_f = r0
    //     0x12990cc: stur            w0, [x1, #0xf]
    // 0x12990d0: r0 = 6
    //     0x12990d0: movz            x0, #0x6
    // 0x12990d4: StoreField: r1->field_b = r0
    //     0x12990d4: stur            w0, [x1, #0xb]
    // 0x12990d8: r0 = Row()
    //     0x12990d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12990dc: mov             x1, x0
    // 0x12990e0: r0 = Instance_Axis
    //     0x12990e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12990e4: stur            x1, [fp, #-0x30]
    // 0x12990e8: StoreField: r1->field_f = r0
    //     0x12990e8: stur            w0, [x1, #0xf]
    // 0x12990ec: r0 = Instance_MainAxisAlignment
    //     0x12990ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x12990f0: ldr             x0, [x0, #0xa8]
    // 0x12990f4: StoreField: r1->field_13 = r0
    //     0x12990f4: stur            w0, [x1, #0x13]
    // 0x12990f8: r0 = Instance_MainAxisSize
    //     0x12990f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12990fc: ldr             x0, [x0, #0xa10]
    // 0x1299100: ArrayStore: r1[0] = r0  ; List_4
    //     0x1299100: stur            w0, [x1, #0x17]
    // 0x1299104: r0 = Instance_CrossAxisAlignment
    //     0x1299104: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1299108: ldr             x0, [x0, #0xa18]
    // 0x129910c: StoreField: r1->field_1b = r0
    //     0x129910c: stur            w0, [x1, #0x1b]
    // 0x1299110: r0 = Instance_VerticalDirection
    //     0x1299110: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1299114: ldr             x0, [x0, #0xa20]
    // 0x1299118: StoreField: r1->field_23 = r0
    //     0x1299118: stur            w0, [x1, #0x23]
    // 0x129911c: r0 = Instance_Clip
    //     0x129911c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1299120: ldr             x0, [x0, #0x38]
    // 0x1299124: StoreField: r1->field_2b = r0
    //     0x1299124: stur            w0, [x1, #0x2b]
    // 0x1299128: StoreField: r1->field_2f = rZR
    //     0x1299128: stur            xzr, [x1, #0x2f]
    // 0x129912c: ldur            x0, [fp, #-0x28]
    // 0x1299130: StoreField: r1->field_b = r0
    //     0x1299130: stur            w0, [x1, #0xb]
    // 0x1299134: r0 = Padding()
    //     0x1299134: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1299138: mov             x1, x0
    // 0x129913c: r0 = Instance_EdgeInsets
    //     0x129913c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0x1299140: ldr             x0, [x0, #0x358]
    // 0x1299144: stur            x1, [fp, #-0x28]
    // 0x1299148: StoreField: r1->field_f = r0
    //     0x1299148: stur            w0, [x1, #0xf]
    // 0x129914c: ldur            x0, [fp, #-0x30]
    // 0x1299150: StoreField: r1->field_b = r0
    //     0x1299150: stur            w0, [x1, #0xb]
    // 0x1299154: r0 = IntrinsicHeight()
    //     0x1299154: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x1299158: mov             x1, x0
    // 0x129915c: ldur            x0, [fp, #-0x28]
    // 0x1299160: stur            x1, [fp, #-0x30]
    // 0x1299164: StoreField: r1->field_b = r0
    //     0x1299164: stur            w0, [x1, #0xb]
    // 0x1299168: r0 = Container()
    //     0x1299168: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x129916c: stur            x0, [fp, #-0x28]
    // 0x1299170: r16 = 93.000000
    //     0x1299170: add             x16, PP, #0x48, lsl #12  ; [pp+0x48360] 93
    //     0x1299174: ldr             x16, [x16, #0x360]
    // 0x1299178: ldur            lr, [fp, #-0x20]
    // 0x129917c: stp             lr, x16, [SP, #8]
    // 0x1299180: ldur            x16, [fp, #-0x30]
    // 0x1299184: str             x16, [SP]
    // 0x1299188: mov             x1, x0
    // 0x129918c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x129918c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x1299190: ldr             x4, [x4, #0xc78]
    // 0x1299194: r0 = Container()
    //     0x1299194: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1299198: r1 = <Path>
    //     0x1299198: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0x129919c: ldr             x1, [x1, #0xd30]
    // 0x12991a0: r0 = MovieTicketClipper()
    //     0x12991a0: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0x12991a4: stur            x0, [fp, #-0x20]
    // 0x12991a8: r0 = ClipPath()
    //     0x12991a8: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0x12991ac: mov             x1, x0
    // 0x12991b0: ldur            x0, [fp, #-0x20]
    // 0x12991b4: stur            x1, [fp, #-0x30]
    // 0x12991b8: StoreField: r1->field_f = r0
    //     0x12991b8: stur            w0, [x1, #0xf]
    // 0x12991bc: r0 = Instance_Clip
    //     0x12991bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x12991c0: ldr             x0, [x0, #0x138]
    // 0x12991c4: StoreField: r1->field_13 = r0
    //     0x12991c4: stur            w0, [x1, #0x13]
    // 0x12991c8: ldur            x0, [fp, #-0x28]
    // 0x12991cc: StoreField: r1->field_b = r0
    //     0x12991cc: stur            w0, [x1, #0xb]
    // 0x12991d0: r0 = Padding()
    //     0x12991d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12991d4: r1 = Instance_EdgeInsets
    //     0x12991d4: add             x1, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x12991d8: ldr             x1, [x1, #0x778]
    // 0x12991dc: StoreField: r0->field_f = r1
    //     0x12991dc: stur            w1, [x0, #0xf]
    // 0x12991e0: ldur            x1, [fp, #-0x30]
    // 0x12991e4: StoreField: r0->field_b = r1
    //     0x12991e4: stur            w1, [x0, #0xb]
    // 0x12991e8: LeaveFrame
    //     0x12991e8: mov             SP, fp
    //     0x12991ec: ldp             fp, lr, [SP], #0x10
    // 0x12991f0: ret
    //     0x12991f0: ret             
    // 0x12991f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12991f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12991f8: b               #0x1298a94
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x12991fc, size: 0xc0
    // 0x12991fc: EnterFrame
    //     0x12991fc: stp             fp, lr, [SP, #-0x10]!
    //     0x1299200: mov             fp, SP
    // 0x1299204: AllocStack(0x38)
    //     0x1299204: sub             SP, SP, #0x38
    // 0x1299208: SetupParameters()
    //     0x1299208: ldr             x0, [fp, #0x10]
    //     0x129920c: ldur            w1, [x0, #0x17]
    //     0x1299210: add             x1, x1, HEAP, lsl #32
    // 0x1299214: CheckStackOverflow
    //     0x1299214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1299218: cmp             SP, x16
    //     0x129921c: b.ls            #0x12992b4
    // 0x1299220: LoadField: r0 = r1->field_f
    //     0x1299220: ldur            w0, [x1, #0xf]
    // 0x1299224: DecompressPointer r0
    //     0x1299224: add             x0, x0, HEAP, lsl #32
    // 0x1299228: LoadField: r1 = r0->field_27
    //     0x1299228: ldur            w1, [x0, #0x27]
    // 0x129922c: DecompressPointer r1
    //     0x129922c: add             x1, x1, HEAP, lsl #32
    // 0x1299230: LoadField: r2 = r0->field_23
    //     0x1299230: ldur            w2, [x0, #0x23]
    // 0x1299234: DecompressPointer r2
    //     0x1299234: add             x2, x2, HEAP, lsl #32
    // 0x1299238: LoadField: r3 = r0->field_2b
    //     0x1299238: ldur            w3, [x0, #0x2b]
    // 0x129923c: DecompressPointer r3
    //     0x129923c: add             x3, x3, HEAP, lsl #32
    // 0x1299240: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x1299240: ldur            w4, [x0, #0x17]
    // 0x1299244: DecompressPointer r4
    //     0x1299244: add             x4, x4, HEAP, lsl #32
    // 0x1299248: cmp             w4, NULL
    // 0x129924c: b.ne            #0x1299258
    // 0x1299250: r4 = Null
    //     0x1299250: mov             x4, NULL
    // 0x1299254: b               #0x1299264
    // 0x1299258: LoadField: r5 = r4->field_b
    //     0x1299258: ldur            w5, [x4, #0xb]
    // 0x129925c: DecompressPointer r5
    //     0x129925c: add             x5, x5, HEAP, lsl #32
    // 0x1299260: mov             x4, x5
    // 0x1299264: LoadField: r5 = r0->field_f
    //     0x1299264: ldur            w5, [x0, #0xf]
    // 0x1299268: DecompressPointer r5
    //     0x1299268: add             x5, x5, HEAP, lsl #32
    // 0x129926c: LoadField: r6 = r0->field_3f
    //     0x129926c: ldur            w6, [x0, #0x3f]
    // 0x1299270: DecompressPointer r6
    //     0x1299270: add             x6, x6, HEAP, lsl #32
    // 0x1299274: stp             x1, x6, [SP, #0x28]
    // 0x1299278: r16 = "product_page"
    //     0x1299278: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x129927c: ldr             x16, [x16, #0x480]
    // 0x1299280: stp             x16, x2, [SP, #0x18]
    // 0x1299284: stp             x4, x3, [SP, #8]
    // 0x1299288: str             x5, [SP]
    // 0x129928c: r4 = 0
    //     0x129928c: movz            x4, #0
    // 0x1299290: ldr             x0, [SP, #0x30]
    // 0x1299294: r16 = UnlinkedCall_0x613b5c
    //     0x1299294: add             x16, PP, #0x48, lsl #12  ; [pp+0x482c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1299298: add             x16, x16, #0x2c0
    // 0x129929c: ldp             x5, lr, [x16]
    // 0x12992a0: blr             lr
    // 0x12992a4: r0 = Null
    //     0x12992a4: mov             x0, NULL
    // 0x12992a8: LeaveFrame
    //     0x12992a8: mov             SP, fp
    //     0x12992ac: ldp             fp, lr, [SP], #0x10
    // 0x12992b0: ret
    //     0x12992b0: ret             
    // 0x12992b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12992b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12992b8: b               #0x1299220
  }
}
