// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart

// class id: 1049568, size: 0x8
class :: {
}

// class id: 4521, size: 0x14, field offset: 0x14
//   const constructor, 
class ReviewListWidget extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x98bce4, size: 0x54
    // 0x98bce4: EnterFrame
    //     0x98bce4: stp             fp, lr, [SP, #-0x10]!
    //     0x98bce8: mov             fp, SP
    // 0x98bcec: AllocStack(0x10)
    //     0x98bcec: sub             SP, SP, #0x10
    // 0x98bcf0: SetupParameters()
    //     0x98bcf0: ldr             x0, [fp, #0x10]
    //     0x98bcf4: ldur            w1, [x0, #0x17]
    //     0x98bcf8: add             x1, x1, HEAP, lsl #32
    // 0x98bcfc: CheckStackOverflow
    //     0x98bcfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98bd00: cmp             SP, x16
    //     0x98bd04: b.ls            #0x98bd30
    // 0x98bd08: LoadField: r0 = r1->field_13
    //     0x98bd08: ldur            w0, [x1, #0x13]
    // 0x98bd0c: DecompressPointer r0
    //     0x98bd0c: add             x0, x0, HEAP, lsl #32
    // 0x98bd10: r16 = <Object?>
    //     0x98bd10: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0x98bd14: stp             x0, x16, [SP]
    // 0x98bd18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x98bd18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x98bd1c: r0 = pop()
    //     0x98bd1c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0x98bd20: r0 = Null
    //     0x98bd20: mov             x0, NULL
    // 0x98bd24: LeaveFrame
    //     0x98bd24: mov             SP, fp
    //     0x98bd28: ldp             fp, lr, [SP], #0x10
    // 0x98bd2c: ret
    //     0x98bd2c: ret             
    // 0x98bd30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98bd30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98bd34: b               #0x98bd08
  }
  [closure] ShopDeckTrustBadgeBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x999958, size: 0xc
    // 0x999958: r0 = Instance_ShopDeckTrustBadgeBottomSheet
    //     0x999958: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd78] Obj!ShopDeckTrustBadgeBottomSheet@d65901
    //     0x99995c: ldr             x0, [x0, #0xd78]
    // 0x999960: ret
    //     0x999960: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x999964, size: 0xa8
    // 0x999964: EnterFrame
    //     0x999964: stp             fp, lr, [SP, #-0x10]!
    //     0x999968: mov             fp, SP
    // 0x99996c: AllocStack(0x38)
    //     0x99996c: sub             SP, SP, #0x38
    // 0x999970: SetupParameters()
    //     0x999970: ldr             x0, [fp, #0x10]
    //     0x999974: ldur            w2, [x0, #0x17]
    //     0x999978: add             x2, x2, HEAP, lsl #32
    //     0x99997c: stur            x2, [fp, #-8]
    // 0x999980: CheckStackOverflow
    //     0x999980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x999984: cmp             SP, x16
    //     0x999988: b.ls            #0x999a04
    // 0x99998c: LoadField: r1 = r2->field_f
    //     0x99998c: ldur            w1, [x2, #0xf]
    // 0x999990: DecompressPointer r1
    //     0x999990: add             x1, x1, HEAP, lsl #32
    // 0x999994: r0 = controller()
    //     0x999994: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x999998: mov             x1, x0
    // 0x99999c: r2 = "trusted_badge"
    //     0x99999c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "trusted_badge"
    //     0x9999a0: ldr             x2, [x2, #0xd58]
    // 0x9999a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9999a4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9999a8: r0 = ratingReviewClickedEvent()
    //     0x9999a8: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9999ac: ldur            x0, [fp, #-8]
    // 0x9999b0: LoadField: r3 = r0->field_13
    //     0x9999b0: ldur            w3, [x0, #0x13]
    // 0x9999b4: DecompressPointer r3
    //     0x9999b4: add             x3, x3, HEAP, lsl #32
    // 0x9999b8: stur            x3, [fp, #-0x10]
    // 0x9999bc: r1 = Function '<anonymous closure>':.
    //     0x9999bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd60] AnonymousClosure: (0x999958), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x9999c0: ldr             x1, [x1, #0xd60]
    // 0x9999c4: r2 = Null
    //     0x9999c4: mov             x2, NULL
    // 0x9999c8: r0 = AllocateClosure()
    //     0x9999c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9999cc: stp             x0, NULL, [SP, #0x18]
    // 0x9999d0: ldur            x16, [fp, #-0x10]
    // 0x9999d4: r30 = Instance_RoundedRectangleBorder
    //     0x9999d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x9999d8: ldr             lr, [lr, #0xd68]
    // 0x9999dc: stp             lr, x16, [SP, #8]
    // 0x9999e0: r16 = true
    //     0x9999e0: add             x16, NULL, #0x20  ; true
    // 0x9999e4: str             x16, [SP]
    // 0x9999e8: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x9999e8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x9999ec: ldr             x4, [x4, #0xd70]
    // 0x9999f0: r0 = showModalBottomSheet()
    //     0x9999f0: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x9999f4: r0 = Null
    //     0x9999f4: mov             x0, NULL
    // 0x9999f8: LeaveFrame
    //     0x9999f8: mov             SP, fp
    //     0x9999fc: ldp             fp, lr, [SP], #0x10
    // 0x999a00: ret
    //     0x999a00: ret             
    // 0x999a04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x999a04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x999a08: b               #0x99998c
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0x9b1028, size: 0xfc
    // 0x9b1028: EnterFrame
    //     0x9b1028: stp             fp, lr, [SP, #-0x10]!
    //     0x9b102c: mov             fp, SP
    // 0x9b1030: AllocStack(0x10)
    //     0x9b1030: sub             SP, SP, #0x10
    // 0x9b1034: ldr             x0, [fp, #0x10]
    // 0x9b1038: LoadField: r1 = r0->field_b
    //     0x9b1038: ldur            w1, [x0, #0xb]
    // 0x9b103c: DecompressPointer r1
    //     0x9b103c: add             x1, x1, HEAP, lsl #32
    // 0x9b1040: cmp             w1, NULL
    // 0x9b1044: b.eq            #0x9b1060
    // 0x9b1048: LoadField: r2 = r0->field_f
    //     0x9b1048: ldur            x2, [x0, #0xf]
    // 0x9b104c: r0 = LoadInt32Instr(r1)
    //     0x9b104c: sbfx            x0, x1, #1, #0x1f
    //     0x9b1050: tbz             w1, #0, #0x9b1058
    //     0x9b1054: ldur            x0, [x1, #7]
    // 0x9b1058: cmp             x2, x0
    // 0x9b105c: b.le            #0x9b1068
    // 0x9b1060: r0 = Null
    //     0x9b1060: mov             x0, NULL
    // 0x9b1064: b               #0x9b109c
    // 0x9b1068: scvtf           d0, x2
    // 0x9b106c: scvtf           d1, x0
    // 0x9b1070: fdiv            d2, d0, d1
    // 0x9b1074: r0 = inline_Allocate_Double()
    //     0x9b1074: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9b1078: add             x0, x0, #0x10
    //     0x9b107c: cmp             x1, x0
    //     0x9b1080: b.ls            #0x9b1114
    //     0x9b1084: str             x0, [THR, #0x50]  ; THR::top
    //     0x9b1088: sub             x0, x0, #0xf
    //     0x9b108c: movz            x1, #0xe15c
    //     0x9b1090: movk            x1, #0x3, lsl #16
    //     0x9b1094: stur            x1, [x0, #-1]
    // 0x9b1098: StoreField: r0->field_7 = d2
    //     0x9b1098: stur            d2, [x0, #7]
    // 0x9b109c: stur            x0, [fp, #-8]
    // 0x9b10a0: r0 = CircularProgressIndicator()
    //     0x9b10a0: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0x9b10a4: mov             x1, x0
    // 0x9b10a8: r0 = Instance__ActivityIndicatorType
    //     0x9b10a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0x9b10ac: ldr             x0, [x0, #0x1b0]
    // 0x9b10b0: stur            x1, [fp, #-0x10]
    // 0x9b10b4: StoreField: r1->field_23 = r0
    //     0x9b10b4: stur            w0, [x1, #0x23]
    // 0x9b10b8: ldur            x0, [fp, #-8]
    // 0x9b10bc: StoreField: r1->field_b = r0
    //     0x9b10bc: stur            w0, [x1, #0xb]
    // 0x9b10c0: r0 = Instance_Color
    //     0x9b10c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0x9b10c4: ldr             x0, [x0, #0xb18]
    // 0x9b10c8: StoreField: r1->field_13 = r0
    //     0x9b10c8: stur            w0, [x1, #0x13]
    // 0x9b10cc: r0 = SizedBox()
    //     0x9b10cc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9b10d0: mov             x1, x0
    // 0x9b10d4: r0 = 25.000000
    //     0x9b10d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x9b10d8: ldr             x0, [x0, #0x98]
    // 0x9b10dc: stur            x1, [fp, #-8]
    // 0x9b10e0: StoreField: r1->field_f = r0
    //     0x9b10e0: stur            w0, [x1, #0xf]
    // 0x9b10e4: StoreField: r1->field_13 = r0
    //     0x9b10e4: stur            w0, [x1, #0x13]
    // 0x9b10e8: ldur            x0, [fp, #-0x10]
    // 0x9b10ec: StoreField: r1->field_b = r0
    //     0x9b10ec: stur            w0, [x1, #0xb]
    // 0x9b10f0: r0 = Center()
    //     0x9b10f0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9b10f4: r1 = Instance_Alignment
    //     0x9b10f4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9b10f8: ldr             x1, [x1, #0xb10]
    // 0x9b10fc: StoreField: r0->field_f = r1
    //     0x9b10fc: stur            w1, [x0, #0xf]
    // 0x9b1100: ldur            x1, [fp, #-8]
    // 0x9b1104: StoreField: r0->field_b = r1
    //     0x9b1104: stur            w1, [x0, #0xb]
    // 0x9b1108: LeaveFrame
    //     0x9b1108: mov             SP, fp
    //     0x9b110c: ldp             fp, lr, [SP], #0x10
    // 0x9b1110: ret
    //     0x9b1110: ret             
    // 0x9b1114: SaveReg d2
    //     0x9b1114: str             q2, [SP, #-0x10]!
    // 0x9b1118: r0 = AllocateDouble()
    //     0x9b1118: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9b111c: RestoreReg d2
    //     0x9b111c: ldr             q2, [SP], #0x10
    // 0x9b1120: b               #0x9b1098
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9b1124, size: 0x370
    // 0x9b1124: EnterFrame
    //     0x9b1124: stp             fp, lr, [SP, #-0x10]!
    //     0x9b1128: mov             fp, SP
    // 0x9b112c: AllocStack(0x58)
    //     0x9b112c: sub             SP, SP, #0x58
    // 0x9b1130: SetupParameters()
    //     0x9b1130: ldr             x0, [fp, #0x20]
    //     0x9b1134: ldur            w1, [x0, #0x17]
    //     0x9b1138: add             x1, x1, HEAP, lsl #32
    //     0x9b113c: stur            x1, [fp, #-8]
    // 0x9b1140: CheckStackOverflow
    //     0x9b1140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b1144: cmp             SP, x16
    //     0x9b1148: b.ls            #0x9b147c
    // 0x9b114c: r1 = 3
    //     0x9b114c: movz            x1, #0x3
    // 0x9b1150: r0 = AllocateContext()
    //     0x9b1150: bl              #0x16f6108  ; AllocateContextStub
    // 0x9b1154: mov             x2, x0
    // 0x9b1158: ldur            x0, [fp, #-8]
    // 0x9b115c: stur            x2, [fp, #-0x18]
    // 0x9b1160: StoreField: r2->field_b = r0
    //     0x9b1160: stur            w0, [x2, #0xb]
    // 0x9b1164: ldr             x1, [fp, #0x18]
    // 0x9b1168: StoreField: r2->field_f = r1
    //     0x9b1168: stur            w1, [x2, #0xf]
    // 0x9b116c: ldr             x1, [fp, #0x10]
    // 0x9b1170: StoreField: r2->field_13 = r1
    //     0x9b1170: stur            w1, [x2, #0x13]
    // 0x9b1174: LoadField: r3 = r0->field_b
    //     0x9b1174: ldur            w3, [x0, #0xb]
    // 0x9b1178: DecompressPointer r3
    //     0x9b1178: add             x3, x3, HEAP, lsl #32
    // 0x9b117c: stur            x3, [fp, #-0x10]
    // 0x9b1180: LoadField: r1 = r3->field_f
    //     0x9b1180: ldur            w1, [x3, #0xf]
    // 0x9b1184: DecompressPointer r1
    //     0x9b1184: add             x1, x1, HEAP, lsl #32
    // 0x9b1188: r0 = controller()
    //     0x9b1188: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b118c: LoadField: r1 = r0->field_77
    //     0x9b118c: ldur            w1, [x0, #0x77]
    // 0x9b1190: DecompressPointer r1
    //     0x9b1190: add             x1, x1, HEAP, lsl #32
    // 0x9b1194: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1194: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1198: r0 = toList()
    //     0x9b1198: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b119c: mov             x3, x0
    // 0x9b11a0: ldur            x2, [fp, #-8]
    // 0x9b11a4: LoadField: r0 = r2->field_13
    //     0x9b11a4: ldur            w0, [x2, #0x13]
    // 0x9b11a8: DecompressPointer r0
    //     0x9b11a8: add             x0, x0, HEAP, lsl #32
    // 0x9b11ac: LoadField: r1 = r3->field_b
    //     0x9b11ac: ldur            w1, [x3, #0xb]
    // 0x9b11b0: r4 = LoadInt32Instr(r0)
    //     0x9b11b0: sbfx            x4, x0, #1, #0x1f
    //     0x9b11b4: tbz             w0, #0, #0x9b11bc
    //     0x9b11b8: ldur            x4, [x0, #7]
    // 0x9b11bc: r0 = LoadInt32Instr(r1)
    //     0x9b11bc: sbfx            x0, x1, #1, #0x1f
    // 0x9b11c0: mov             x1, x4
    // 0x9b11c4: cmp             x1, x0
    // 0x9b11c8: b.hs            #0x9b1484
    // 0x9b11cc: LoadField: r0 = r3->field_f
    //     0x9b11cc: ldur            w0, [x3, #0xf]
    // 0x9b11d0: DecompressPointer r0
    //     0x9b11d0: add             x0, x0, HEAP, lsl #32
    // 0x9b11d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b11d4: add             x16, x0, x4, lsl #2
    //     0x9b11d8: ldur            w1, [x16, #0xf]
    // 0x9b11dc: DecompressPointer r1
    //     0x9b11dc: add             x1, x1, HEAP, lsl #32
    // 0x9b11e0: cmp             w1, NULL
    // 0x9b11e4: b.ne            #0x9b11f4
    // 0x9b11e8: ldur            x3, [fp, #-0x18]
    // 0x9b11ec: r4 = Null
    //     0x9b11ec: mov             x4, NULL
    // 0x9b11f0: b               #0x9b1248
    // 0x9b11f4: ldur            x3, [fp, #-0x18]
    // 0x9b11f8: LoadField: r4 = r1->field_1b
    //     0x9b11f8: ldur            w4, [x1, #0x1b]
    // 0x9b11fc: DecompressPointer r4
    //     0x9b11fc: add             x4, x4, HEAP, lsl #32
    // 0x9b1200: LoadField: r0 = r3->field_13
    //     0x9b1200: ldur            w0, [x3, #0x13]
    // 0x9b1204: DecompressPointer r0
    //     0x9b1204: add             x0, x0, HEAP, lsl #32
    // 0x9b1208: LoadField: r1 = r4->field_b
    //     0x9b1208: ldur            w1, [x4, #0xb]
    // 0x9b120c: r5 = LoadInt32Instr(r0)
    //     0x9b120c: sbfx            x5, x0, #1, #0x1f
    //     0x9b1210: tbz             w0, #0, #0x9b1218
    //     0x9b1214: ldur            x5, [x0, #7]
    // 0x9b1218: r0 = LoadInt32Instr(r1)
    //     0x9b1218: sbfx            x0, x1, #1, #0x1f
    // 0x9b121c: mov             x1, x5
    // 0x9b1220: cmp             x1, x0
    // 0x9b1224: b.hs            #0x9b1488
    // 0x9b1228: LoadField: r0 = r4->field_f
    //     0x9b1228: ldur            w0, [x4, #0xf]
    // 0x9b122c: DecompressPointer r0
    //     0x9b122c: add             x0, x0, HEAP, lsl #32
    // 0x9b1230: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9b1230: add             x16, x0, x5, lsl #2
    //     0x9b1234: ldur            w1, [x16, #0xf]
    // 0x9b1238: DecompressPointer r1
    //     0x9b1238: add             x1, x1, HEAP, lsl #32
    // 0x9b123c: LoadField: r0 = r1->field_13
    //     0x9b123c: ldur            w0, [x1, #0x13]
    // 0x9b1240: DecompressPointer r0
    //     0x9b1240: add             x0, x0, HEAP, lsl #32
    // 0x9b1244: mov             x4, x0
    // 0x9b1248: ldur            x1, [fp, #-0x10]
    // 0x9b124c: mov             x0, x4
    // 0x9b1250: stur            x4, [fp, #-0x20]
    // 0x9b1254: ArrayStore: r3[0] = r0  ; List_4
    //     0x9b1254: stur            w0, [x3, #0x17]
    //     0x9b1258: ldurb           w16, [x3, #-1]
    //     0x9b125c: ldurb           w17, [x0, #-1]
    //     0x9b1260: and             x16, x17, x16, lsr #2
    //     0x9b1264: tst             x16, HEAP, lsr #32
    //     0x9b1268: b.eq            #0x9b1270
    //     0x9b126c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9b1270: LoadField: r0 = r1->field_f
    //     0x9b1270: ldur            w0, [x1, #0xf]
    // 0x9b1274: DecompressPointer r0
    //     0x9b1274: add             x0, x0, HEAP, lsl #32
    // 0x9b1278: mov             x1, x0
    // 0x9b127c: r0 = controller()
    //     0x9b127c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1280: LoadField: r1 = r0->field_77
    //     0x9b1280: ldur            w1, [x0, #0x77]
    // 0x9b1284: DecompressPointer r1
    //     0x9b1284: add             x1, x1, HEAP, lsl #32
    // 0x9b1288: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1288: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b128c: r0 = toList()
    //     0x9b128c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1290: mov             x2, x0
    // 0x9b1294: ldur            x0, [fp, #-8]
    // 0x9b1298: LoadField: r1 = r0->field_13
    //     0x9b1298: ldur            w1, [x0, #0x13]
    // 0x9b129c: DecompressPointer r1
    //     0x9b129c: add             x1, x1, HEAP, lsl #32
    // 0x9b12a0: LoadField: r0 = r2->field_b
    //     0x9b12a0: ldur            w0, [x2, #0xb]
    // 0x9b12a4: r3 = LoadInt32Instr(r1)
    //     0x9b12a4: sbfx            x3, x1, #1, #0x1f
    //     0x9b12a8: tbz             w1, #0, #0x9b12b0
    //     0x9b12ac: ldur            x3, [x1, #7]
    // 0x9b12b0: r1 = LoadInt32Instr(r0)
    //     0x9b12b0: sbfx            x1, x0, #1, #0x1f
    // 0x9b12b4: mov             x0, x1
    // 0x9b12b8: mov             x1, x3
    // 0x9b12bc: cmp             x1, x0
    // 0x9b12c0: b.hs            #0x9b148c
    // 0x9b12c4: LoadField: r0 = r2->field_f
    //     0x9b12c4: ldur            w0, [x2, #0xf]
    // 0x9b12c8: DecompressPointer r0
    //     0x9b12c8: add             x0, x0, HEAP, lsl #32
    // 0x9b12cc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x9b12cc: add             x16, x0, x3, lsl #2
    //     0x9b12d0: ldur            w1, [x16, #0xf]
    // 0x9b12d4: DecompressPointer r1
    //     0x9b12d4: add             x1, x1, HEAP, lsl #32
    // 0x9b12d8: cmp             w1, NULL
    // 0x9b12dc: b.ne            #0x9b12ec
    // 0x9b12e0: ldur            x2, [fp, #-0x18]
    // 0x9b12e4: r0 = Null
    //     0x9b12e4: mov             x0, NULL
    // 0x9b12e8: b               #0x9b133c
    // 0x9b12ec: ldur            x2, [fp, #-0x18]
    // 0x9b12f0: LoadField: r3 = r1->field_1b
    //     0x9b12f0: ldur            w3, [x1, #0x1b]
    // 0x9b12f4: DecompressPointer r3
    //     0x9b12f4: add             x3, x3, HEAP, lsl #32
    // 0x9b12f8: LoadField: r0 = r2->field_13
    //     0x9b12f8: ldur            w0, [x2, #0x13]
    // 0x9b12fc: DecompressPointer r0
    //     0x9b12fc: add             x0, x0, HEAP, lsl #32
    // 0x9b1300: LoadField: r1 = r3->field_b
    //     0x9b1300: ldur            w1, [x3, #0xb]
    // 0x9b1304: r4 = LoadInt32Instr(r0)
    //     0x9b1304: sbfx            x4, x0, #1, #0x1f
    //     0x9b1308: tbz             w0, #0, #0x9b1310
    //     0x9b130c: ldur            x4, [x0, #7]
    // 0x9b1310: r0 = LoadInt32Instr(r1)
    //     0x9b1310: sbfx            x0, x1, #1, #0x1f
    // 0x9b1314: mov             x1, x4
    // 0x9b1318: cmp             x1, x0
    // 0x9b131c: b.hs            #0x9b1490
    // 0x9b1320: LoadField: r0 = r3->field_f
    //     0x9b1320: ldur            w0, [x3, #0xf]
    // 0x9b1324: DecompressPointer r0
    //     0x9b1324: add             x0, x0, HEAP, lsl #32
    // 0x9b1328: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1328: add             x16, x0, x4, lsl #2
    //     0x9b132c: ldur            w1, [x16, #0xf]
    // 0x9b1330: DecompressPointer r1
    //     0x9b1330: add             x1, x1, HEAP, lsl #32
    // 0x9b1334: LoadField: r0 = r1->field_f
    //     0x9b1334: ldur            w0, [x1, #0xf]
    // 0x9b1338: DecompressPointer r0
    //     0x9b1338: add             x0, x0, HEAP, lsl #32
    // 0x9b133c: r1 = LoadClassIdInstr(r0)
    //     0x9b133c: ldur            x1, [x0, #-1]
    //     0x9b1340: ubfx            x1, x1, #0xc, #0x14
    // 0x9b1344: r16 = "image"
    //     0x9b1344: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x9b1348: stp             x16, x0, [SP]
    // 0x9b134c: mov             x0, x1
    // 0x9b1350: mov             lr, x0
    // 0x9b1354: ldr             lr, [x21, lr, lsl #3]
    // 0x9b1358: blr             lr
    // 0x9b135c: tbnz            w0, #4, #0x9b13ec
    // 0x9b1360: ldur            x0, [fp, #-0x20]
    // 0x9b1364: cmp             w0, NULL
    // 0x9b1368: b.ne            #0x9b1370
    // 0x9b136c: r0 = ""
    //     0x9b136c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b1370: stur            x0, [fp, #-8]
    // 0x9b1374: r1 = Function '<anonymous closure>':.
    //     0x9b1374: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fac8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b1378: ldr             x1, [x1, #0xac8]
    // 0x9b137c: r2 = Null
    //     0x9b137c: mov             x2, NULL
    // 0x9b1380: r0 = AllocateClosure()
    //     0x9b1380: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b1384: r1 = Function '<anonymous closure>':.
    //     0x9b1384: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fad0] AnonymousClosure: (0x9b17ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b1388: ldr             x1, [x1, #0xad0]
    // 0x9b138c: r2 = Null
    //     0x9b138c: mov             x2, NULL
    // 0x9b1390: stur            x0, [fp, #-0x10]
    // 0x9b1394: r0 = AllocateClosure()
    //     0x9b1394: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b1398: stur            x0, [fp, #-0x28]
    // 0x9b139c: r0 = CachedNetworkImage()
    //     0x9b139c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x9b13a0: stur            x0, [fp, #-0x30]
    // 0x9b13a4: r16 = Instance_BoxFit
    //     0x9b13a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x9b13a8: ldr             x16, [x16, #0x118]
    // 0x9b13ac: r30 = 48.000000
    //     0x9b13ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x9b13b0: ldr             lr, [lr, #0xad8]
    // 0x9b13b4: stp             lr, x16, [SP, #0x18]
    // 0x9b13b8: r16 = 48.000000
    //     0x9b13b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x9b13bc: ldr             x16, [x16, #0xad8]
    // 0x9b13c0: ldur            lr, [fp, #-0x10]
    // 0x9b13c4: stp             lr, x16, [SP, #8]
    // 0x9b13c8: ldur            x16, [fp, #-0x28]
    // 0x9b13cc: str             x16, [SP]
    // 0x9b13d0: mov             x1, x0
    // 0x9b13d4: ldur            x2, [fp, #-8]
    // 0x9b13d8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x9b13d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x9b13dc: ldr             x4, [x4, #0xae0]
    // 0x9b13e0: r0 = CachedNetworkImage()
    //     0x9b13e0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x9b13e4: ldur            x0, [fp, #-0x30]
    // 0x9b13e8: b               #0x9b1438
    // 0x9b13ec: ldur            x0, [fp, #-0x20]
    // 0x9b13f0: cmp             w0, NULL
    // 0x9b13f4: b.ne            #0x9b13fc
    // 0x9b13f8: r0 = ""
    //     0x9b13f8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b13fc: stur            x0, [fp, #-8]
    // 0x9b1400: r0 = VideoPlayerWidget()
    //     0x9b1400: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x9b1404: mov             x1, x0
    // 0x9b1408: ldur            x0, [fp, #-8]
    // 0x9b140c: stur            x1, [fp, #-0x10]
    // 0x9b1410: StoreField: r1->field_b = r0
    //     0x9b1410: stur            w0, [x1, #0xb]
    // 0x9b1414: r0 = SizedBox()
    //     0x9b1414: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9b1418: mov             x1, x0
    // 0x9b141c: r0 = 48.000000
    //     0x9b141c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x9b1420: ldr             x0, [x0, #0xad8]
    // 0x9b1424: StoreField: r1->field_f = r0
    //     0x9b1424: stur            w0, [x1, #0xf]
    // 0x9b1428: StoreField: r1->field_13 = r0
    //     0x9b1428: stur            w0, [x1, #0x13]
    // 0x9b142c: ldur            x0, [fp, #-0x10]
    // 0x9b1430: StoreField: r1->field_b = r0
    //     0x9b1430: stur            w0, [x1, #0xb]
    // 0x9b1434: mov             x0, x1
    // 0x9b1438: stur            x0, [fp, #-8]
    // 0x9b143c: r0 = GestureDetector()
    //     0x9b143c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x9b1440: ldur            x2, [fp, #-0x18]
    // 0x9b1444: r1 = Function '<anonymous closure>':.
    //     0x9b1444: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fae8] AnonymousClosure: (0x9b14d0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b1448: ldr             x1, [x1, #0xae8]
    // 0x9b144c: stur            x0, [fp, #-0x10]
    // 0x9b1450: r0 = AllocateClosure()
    //     0x9b1450: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b1454: ldur            x16, [fp, #-8]
    // 0x9b1458: stp             x16, x0, [SP]
    // 0x9b145c: ldur            x1, [fp, #-0x10]
    // 0x9b1460: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x9b1460: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x9b1464: ldr             x4, [x4, #0xaf0]
    // 0x9b1468: r0 = GestureDetector()
    //     0x9b1468: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x9b146c: ldur            x0, [fp, #-0x10]
    // 0x9b1470: LeaveFrame
    //     0x9b1470: mov             SP, fp
    //     0x9b1474: ldp             fp, lr, [SP], #0x10
    // 0x9b1478: ret
    //     0x9b1478: ret             
    // 0x9b147c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b147c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b1480: b               #0x9b114c
    // 0x9b1484: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b1484: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b1488: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b1488: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b148c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b148c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b1490: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b1490: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9b14d0, size: 0xf4
    // 0x9b14d0: EnterFrame
    //     0x9b14d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9b14d4: mov             fp, SP
    // 0x9b14d8: AllocStack(0x28)
    //     0x9b14d8: sub             SP, SP, #0x28
    // 0x9b14dc: SetupParameters()
    //     0x9b14dc: ldr             x0, [fp, #0x10]
    //     0x9b14e0: ldur            w2, [x0, #0x17]
    //     0x9b14e4: add             x2, x2, HEAP, lsl #32
    //     0x9b14e8: stur            x2, [fp, #-8]
    // 0x9b14ec: CheckStackOverflow
    //     0x9b14ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b14f0: cmp             SP, x16
    //     0x9b14f4: b.ls            #0x9b15bc
    // 0x9b14f8: LoadField: r0 = r2->field_b
    //     0x9b14f8: ldur            w0, [x2, #0xb]
    // 0x9b14fc: DecompressPointer r0
    //     0x9b14fc: add             x0, x0, HEAP, lsl #32
    // 0x9b1500: LoadField: r1 = r0->field_b
    //     0x9b1500: ldur            w1, [x0, #0xb]
    // 0x9b1504: DecompressPointer r1
    //     0x9b1504: add             x1, x1, HEAP, lsl #32
    // 0x9b1508: LoadField: r0 = r1->field_f
    //     0x9b1508: ldur            w0, [x1, #0xf]
    // 0x9b150c: DecompressPointer r0
    //     0x9b150c: add             x0, x0, HEAP, lsl #32
    // 0x9b1510: mov             x1, x0
    // 0x9b1514: r0 = controller()
    //     0x9b1514: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1518: mov             x1, x0
    // 0x9b151c: ldur            x0, [fp, #-8]
    // 0x9b1520: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9b1520: ldur            w2, [x0, #0x17]
    // 0x9b1524: DecompressPointer r2
    //     0x9b1524: add             x2, x2, HEAP, lsl #32
    // 0x9b1528: cmp             w2, NULL
    // 0x9b152c: b.ne            #0x9b1534
    // 0x9b1530: r2 = ""
    //     0x9b1530: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b1534: str             x2, [SP]
    // 0x9b1538: r2 = "single_media"
    //     0x9b1538: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0x9b153c: ldr             x2, [x2, #0xab0]
    // 0x9b1540: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0x9b1540: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0x9b1544: ldr             x4, [x4, #0xaf8]
    // 0x9b1548: r0 = ratingReviewClickedEvent()
    //     0x9b1548: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9b154c: ldur            x2, [fp, #-8]
    // 0x9b1550: LoadField: r1 = r2->field_f
    //     0x9b1550: ldur            w1, [x2, #0xf]
    // 0x9b1554: DecompressPointer r1
    //     0x9b1554: add             x1, x1, HEAP, lsl #32
    // 0x9b1558: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1558: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b155c: r0 = of()
    //     0x9b155c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x9b1560: ldur            x2, [fp, #-8]
    // 0x9b1564: r1 = Function '<anonymous closure>':.
    //     0x9b1564: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb00] AnonymousClosure: (0x9b15c4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b1568: ldr             x1, [x1, #0xb00]
    // 0x9b156c: stur            x0, [fp, #-8]
    // 0x9b1570: r0 = AllocateClosure()
    //     0x9b1570: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b1574: r1 = Null
    //     0x9b1574: mov             x1, NULL
    // 0x9b1578: stur            x0, [fp, #-0x10]
    // 0x9b157c: r0 = MaterialPageRoute()
    //     0x9b157c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x9b1580: mov             x1, x0
    // 0x9b1584: ldur            x2, [fp, #-0x10]
    // 0x9b1588: stur            x0, [fp, #-0x10]
    // 0x9b158c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9b158c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9b1590: r0 = MaterialPageRoute()
    //     0x9b1590: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x9b1594: ldur            x16, [fp, #-8]
    // 0x9b1598: stp             x16, NULL, [SP, #8]
    // 0x9b159c: ldur            x16, [fp, #-0x10]
    // 0x9b15a0: str             x16, [SP]
    // 0x9b15a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9b15a4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9b15a8: r0 = push()
    //     0x9b15a8: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x9b15ac: r0 = Null
    //     0x9b15ac: mov             x0, NULL
    // 0x9b15b0: LeaveFrame
    //     0x9b15b0: mov             SP, fp
    //     0x9b15b4: ldp             fp, lr, [SP], #0x10
    // 0x9b15b8: ret
    //     0x9b15b8: ret             
    // 0x9b15bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b15bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b15c0: b               #0x9b14f8
  }
  [closure] RatingReviewOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x9b15c4, size: 0x13c
    // 0x9b15c4: EnterFrame
    //     0x9b15c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9b15c8: mov             fp, SP
    // 0x9b15cc: AllocStack(0x28)
    //     0x9b15cc: sub             SP, SP, #0x28
    // 0x9b15d0: SetupParameters()
    //     0x9b15d0: ldr             x0, [fp, #0x18]
    //     0x9b15d4: ldur            w2, [x0, #0x17]
    //     0x9b15d8: add             x2, x2, HEAP, lsl #32
    //     0x9b15dc: stur            x2, [fp, #-0x18]
    // 0x9b15e0: CheckStackOverflow
    //     0x9b15e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b15e4: cmp             SP, x16
    //     0x9b15e8: b.ls            #0x9b16f4
    // 0x9b15ec: LoadField: r0 = r2->field_b
    //     0x9b15ec: ldur            w0, [x2, #0xb]
    // 0x9b15f0: DecompressPointer r0
    //     0x9b15f0: add             x0, x0, HEAP, lsl #32
    // 0x9b15f4: stur            x0, [fp, #-0x10]
    // 0x9b15f8: LoadField: r3 = r0->field_b
    //     0x9b15f8: ldur            w3, [x0, #0xb]
    // 0x9b15fc: DecompressPointer r3
    //     0x9b15fc: add             x3, x3, HEAP, lsl #32
    // 0x9b1600: stur            x3, [fp, #-8]
    // 0x9b1604: LoadField: r1 = r3->field_f
    //     0x9b1604: ldur            w1, [x3, #0xf]
    // 0x9b1608: DecompressPointer r1
    //     0x9b1608: add             x1, x1, HEAP, lsl #32
    // 0x9b160c: r0 = controller()
    //     0x9b160c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1610: LoadField: r1 = r0->field_77
    //     0x9b1610: ldur            w1, [x0, #0x77]
    // 0x9b1614: DecompressPointer r1
    //     0x9b1614: add             x1, x1, HEAP, lsl #32
    // 0x9b1618: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1618: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b161c: r0 = toList()
    //     0x9b161c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1620: mov             x2, x0
    // 0x9b1624: ldur            x0, [fp, #-0x10]
    // 0x9b1628: LoadField: r1 = r0->field_13
    //     0x9b1628: ldur            w1, [x0, #0x13]
    // 0x9b162c: DecompressPointer r1
    //     0x9b162c: add             x1, x1, HEAP, lsl #32
    // 0x9b1630: LoadField: r0 = r2->field_b
    //     0x9b1630: ldur            w0, [x2, #0xb]
    // 0x9b1634: r3 = LoadInt32Instr(r1)
    //     0x9b1634: sbfx            x3, x1, #1, #0x1f
    //     0x9b1638: tbz             w1, #0, #0x9b1640
    //     0x9b163c: ldur            x3, [x1, #7]
    // 0x9b1640: r1 = LoadInt32Instr(r0)
    //     0x9b1640: sbfx            x1, x0, #1, #0x1f
    // 0x9b1644: mov             x0, x1
    // 0x9b1648: mov             x1, x3
    // 0x9b164c: cmp             x1, x0
    // 0x9b1650: b.hs            #0x9b16fc
    // 0x9b1654: LoadField: r0 = r2->field_f
    //     0x9b1654: ldur            w0, [x2, #0xf]
    // 0x9b1658: DecompressPointer r0
    //     0x9b1658: add             x0, x0, HEAP, lsl #32
    // 0x9b165c: ArrayLoad: r2 = r0[r3]  ; Unknown_4
    //     0x9b165c: add             x16, x0, x3, lsl #2
    //     0x9b1660: ldur            w2, [x16, #0xf]
    // 0x9b1664: DecompressPointer r2
    //     0x9b1664: add             x2, x2, HEAP, lsl #32
    // 0x9b1668: ldur            x0, [fp, #-0x18]
    // 0x9b166c: stur            x2, [fp, #-0x20]
    // 0x9b1670: LoadField: r3 = r0->field_13
    //     0x9b1670: ldur            w3, [x0, #0x13]
    // 0x9b1674: DecompressPointer r3
    //     0x9b1674: add             x3, x3, HEAP, lsl #32
    // 0x9b1678: ldur            x1, [fp, #-8]
    // 0x9b167c: stur            x3, [fp, #-0x10]
    // 0x9b1680: LoadField: r4 = r1->field_f
    //     0x9b1680: ldur            w4, [x1, #0xf]
    // 0x9b1684: DecompressPointer r4
    //     0x9b1684: add             x4, x4, HEAP, lsl #32
    // 0x9b1688: mov             x1, x4
    // 0x9b168c: r0 = controller()
    //     0x9b168c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1690: LoadField: r1 = r0->field_97
    //     0x9b1690: ldur            w1, [x0, #0x97]
    // 0x9b1694: DecompressPointer r1
    //     0x9b1694: add             x1, x1, HEAP, lsl #32
    // 0x9b1698: stur            x1, [fp, #-8]
    // 0x9b169c: r0 = RatingReviewOnTapImage()
    //     0x9b169c: bl              #0x9b1700  ; AllocateRatingReviewOnTapImageStub -> RatingReviewOnTapImage (size=0x20)
    // 0x9b16a0: mov             x3, x0
    // 0x9b16a4: ldur            x0, [fp, #-0x20]
    // 0x9b16a8: stur            x3, [fp, #-0x28]
    // 0x9b16ac: StoreField: r3->field_b = r0
    //     0x9b16ac: stur            w0, [x3, #0xb]
    // 0x9b16b0: ldur            x0, [fp, #-0x10]
    // 0x9b16b4: r1 = LoadInt32Instr(r0)
    //     0x9b16b4: sbfx            x1, x0, #1, #0x1f
    //     0x9b16b8: tbz             w0, #0, #0x9b16c0
    //     0x9b16bc: ldur            x1, [x0, #7]
    // 0x9b16c0: StoreField: r3->field_f = r1
    //     0x9b16c0: stur            x1, [x3, #0xf]
    // 0x9b16c4: ldur            x2, [fp, #-0x18]
    // 0x9b16c8: r1 = Function '<anonymous closure>':.
    //     0x9b16c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb08] AnonymousClosure: (0x9b170c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b16cc: ldr             x1, [x1, #0xb08]
    // 0x9b16d0: r0 = AllocateClosure()
    //     0x9b16d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b16d4: mov             x1, x0
    // 0x9b16d8: ldur            x0, [fp, #-0x28]
    // 0x9b16dc: ArrayStore: r0[0] = r1  ; List_4
    //     0x9b16dc: stur            w1, [x0, #0x17]
    // 0x9b16e0: ldur            x1, [fp, #-8]
    // 0x9b16e4: StoreField: r0->field_1b = r1
    //     0x9b16e4: stur            w1, [x0, #0x1b]
    // 0x9b16e8: LeaveFrame
    //     0x9b16e8: mov             SP, fp
    //     0x9b16ec: ldp             fp, lr, [SP], #0x10
    // 0x9b16f0: ret
    //     0x9b16f0: ret             
    // 0x9b16f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b16f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b16f8: b               #0x9b15ec
    // 0x9b16fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b16fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0x9b170c, size: 0x64
    // 0x9b170c: EnterFrame
    //     0x9b170c: stp             fp, lr, [SP, #-0x10]!
    //     0x9b1710: mov             fp, SP
    // 0x9b1714: ldr             x0, [fp, #0x18]
    // 0x9b1718: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9b1718: ldur            w1, [x0, #0x17]
    // 0x9b171c: DecompressPointer r1
    //     0x9b171c: add             x1, x1, HEAP, lsl #32
    // 0x9b1720: CheckStackOverflow
    //     0x9b1720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b1724: cmp             SP, x16
    //     0x9b1728: b.ls            #0x9b1768
    // 0x9b172c: LoadField: r0 = r1->field_b
    //     0x9b172c: ldur            w0, [x1, #0xb]
    // 0x9b1730: DecompressPointer r0
    //     0x9b1730: add             x0, x0, HEAP, lsl #32
    // 0x9b1734: LoadField: r1 = r0->field_b
    //     0x9b1734: ldur            w1, [x0, #0xb]
    // 0x9b1738: DecompressPointer r1
    //     0x9b1738: add             x1, x1, HEAP, lsl #32
    // 0x9b173c: LoadField: r0 = r1->field_f
    //     0x9b173c: ldur            w0, [x1, #0xf]
    // 0x9b1740: DecompressPointer r0
    //     0x9b1740: add             x0, x0, HEAP, lsl #32
    // 0x9b1744: mov             x1, x0
    // 0x9b1748: r0 = controller()
    //     0x9b1748: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b174c: mov             x1, x0
    // 0x9b1750: ldr             x2, [fp, #0x10]
    // 0x9b1754: r0 = saveFlaggedData()
    //     0x9b1754: bl              #0x9b1770  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::saveFlaggedData
    // 0x9b1758: r0 = Null
    //     0x9b1758: mov             x0, NULL
    // 0x9b175c: LeaveFrame
    //     0x9b175c: mov             SP, fp
    //     0x9b1760: ldp             fp, lr, [SP], #0x10
    // 0x9b1764: ret
    //     0x9b1764: ret             
    // 0x9b1768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b1768: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b176c: b               #0x9b172c
  }
  [closure] Icon <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0x9b17ac, size: 0xc
    // 0x9b17ac: r0 = Instance_Icon
    //     0x9b17ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb10] Obj!Icon@d65e31
    //     0x9b17b0: ldr             x0, [x0, #0xb10]
    // 0x9b17b4: ret
    //     0x9b17b4: ret             
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9b17b8, size: 0x1344
    // 0x9b17b8: EnterFrame
    //     0x9b17b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9b17bc: mov             fp, SP
    // 0x9b17c0: AllocStack(0x78)
    //     0x9b17c0: sub             SP, SP, #0x78
    // 0x9b17c4: SetupParameters()
    //     0x9b17c4: ldr             x0, [fp, #0x20]
    //     0x9b17c8: ldur            w1, [x0, #0x17]
    //     0x9b17cc: add             x1, x1, HEAP, lsl #32
    //     0x9b17d0: stur            x1, [fp, #-8]
    // 0x9b17d4: CheckStackOverflow
    //     0x9b17d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b17d8: cmp             SP, x16
    //     0x9b17dc: b.ls            #0x9b2aa4
    // 0x9b17e0: r1 = 2
    //     0x9b17e0: movz            x1, #0x2
    // 0x9b17e4: r0 = AllocateContext()
    //     0x9b17e4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9b17e8: mov             x2, x0
    // 0x9b17ec: ldur            x0, [fp, #-8]
    // 0x9b17f0: stur            x2, [fp, #-0x10]
    // 0x9b17f4: StoreField: r2->field_b = r0
    //     0x9b17f4: stur            w0, [x2, #0xb]
    // 0x9b17f8: ldr             x1, [fp, #0x18]
    // 0x9b17fc: StoreField: r2->field_f = r1
    //     0x9b17fc: stur            w1, [x2, #0xf]
    // 0x9b1800: ldr             x1, [fp, #0x10]
    // 0x9b1804: StoreField: r2->field_13 = r1
    //     0x9b1804: stur            w1, [x2, #0x13]
    // 0x9b1808: r1 = Instance_Color
    //     0x9b1808: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b180c: d0 = 0.050000
    //     0x9b180c: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x9b1810: r0 = withOpacity()
    //     0x9b1810: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b1814: stur            x0, [fp, #-0x18]
    // 0x9b1818: r0 = BoxDecoration()
    //     0x9b1818: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9b181c: mov             x2, x0
    // 0x9b1820: ldur            x0, [fp, #-0x18]
    // 0x9b1824: stur            x2, [fp, #-0x20]
    // 0x9b1828: StoreField: r2->field_7 = r0
    //     0x9b1828: stur            w0, [x2, #7]
    // 0x9b182c: r0 = Instance_BoxShape
    //     0x9b182c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x9b1830: ldr             x0, [x0, #0x970]
    // 0x9b1834: StoreField: r2->field_23 = r0
    //     0x9b1834: stur            w0, [x2, #0x23]
    // 0x9b1838: ldur            x0, [fp, #-8]
    // 0x9b183c: LoadField: r1 = r0->field_f
    //     0x9b183c: ldur            w1, [x0, #0xf]
    // 0x9b1840: DecompressPointer r1
    //     0x9b1840: add             x1, x1, HEAP, lsl #32
    // 0x9b1844: r0 = controller()
    //     0x9b1844: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1848: LoadField: r1 = r0->field_77
    //     0x9b1848: ldur            w1, [x0, #0x77]
    // 0x9b184c: DecompressPointer r1
    //     0x9b184c: add             x1, x1, HEAP, lsl #32
    // 0x9b1850: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1850: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1854: r0 = toList()
    //     0x9b1854: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1858: mov             x3, x0
    // 0x9b185c: ldur            x2, [fp, #-0x10]
    // 0x9b1860: LoadField: r0 = r2->field_13
    //     0x9b1860: ldur            w0, [x2, #0x13]
    // 0x9b1864: DecompressPointer r0
    //     0x9b1864: add             x0, x0, HEAP, lsl #32
    // 0x9b1868: LoadField: r1 = r3->field_b
    //     0x9b1868: ldur            w1, [x3, #0xb]
    // 0x9b186c: r4 = LoadInt32Instr(r0)
    //     0x9b186c: sbfx            x4, x0, #1, #0x1f
    //     0x9b1870: tbz             w0, #0, #0x9b1878
    //     0x9b1874: ldur            x4, [x0, #7]
    // 0x9b1878: r0 = LoadInt32Instr(r1)
    //     0x9b1878: sbfx            x0, x1, #1, #0x1f
    // 0x9b187c: mov             x1, x4
    // 0x9b1880: cmp             x1, x0
    // 0x9b1884: b.hs            #0x9b2aac
    // 0x9b1888: LoadField: r0 = r3->field_f
    //     0x9b1888: ldur            w0, [x3, #0xf]
    // 0x9b188c: DecompressPointer r0
    //     0x9b188c: add             x0, x0, HEAP, lsl #32
    // 0x9b1890: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1890: add             x16, x0, x4, lsl #2
    //     0x9b1894: ldur            w1, [x16, #0xf]
    // 0x9b1898: DecompressPointer r1
    //     0x9b1898: add             x1, x1, HEAP, lsl #32
    // 0x9b189c: cmp             w1, NULL
    // 0x9b18a0: b.ne            #0x9b18ac
    // 0x9b18a4: r0 = Null
    //     0x9b18a4: mov             x0, NULL
    // 0x9b18a8: b               #0x9b18e8
    // 0x9b18ac: LoadField: r0 = r1->field_7
    //     0x9b18ac: ldur            w0, [x1, #7]
    // 0x9b18b0: DecompressPointer r0
    //     0x9b18b0: add             x0, x0, HEAP, lsl #32
    // 0x9b18b4: cmp             w0, NULL
    // 0x9b18b8: b.ne            #0x9b18c4
    // 0x9b18bc: r0 = Null
    //     0x9b18bc: mov             x0, NULL
    // 0x9b18c0: b               #0x9b18e8
    // 0x9b18c4: stp             xzr, x0, [SP]
    // 0x9b18c8: r0 = []()
    //     0x9b18c8: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0x9b18cc: r1 = LoadClassIdInstr(r0)
    //     0x9b18cc: ldur            x1, [x0, #-1]
    //     0x9b18d0: ubfx            x1, x1, #0xc, #0x14
    // 0x9b18d4: str             x0, [SP]
    // 0x9b18d8: mov             x0, x1
    // 0x9b18dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9b18dc: sub             lr, x0, #1, lsl #12
    //     0x9b18e0: ldr             lr, [x21, lr, lsl #3]
    //     0x9b18e4: blr             lr
    // 0x9b18e8: cmp             w0, NULL
    // 0x9b18ec: b.ne            #0x9b18f8
    // 0x9b18f0: r3 = ""
    //     0x9b18f0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b18f4: b               #0x9b18fc
    // 0x9b18f8: mov             x3, x0
    // 0x9b18fc: ldur            x0, [fp, #-8]
    // 0x9b1900: ldur            x2, [fp, #-0x10]
    // 0x9b1904: stur            x3, [fp, #-0x18]
    // 0x9b1908: LoadField: r1 = r2->field_f
    //     0x9b1908: ldur            w1, [x2, #0xf]
    // 0x9b190c: DecompressPointer r1
    //     0x9b190c: add             x1, x1, HEAP, lsl #32
    // 0x9b1910: r0 = of()
    //     0x9b1910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b1914: LoadField: r1 = r0->field_87
    //     0x9b1914: ldur            w1, [x0, #0x87]
    // 0x9b1918: DecompressPointer r1
    //     0x9b1918: add             x1, x1, HEAP, lsl #32
    // 0x9b191c: LoadField: r0 = r1->field_7
    //     0x9b191c: ldur            w0, [x1, #7]
    // 0x9b1920: DecompressPointer r0
    //     0x9b1920: add             x0, x0, HEAP, lsl #32
    // 0x9b1924: stur            x0, [fp, #-0x28]
    // 0x9b1928: r1 = Instance_Color
    //     0x9b1928: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b192c: d0 = 0.500000
    //     0x9b192c: fmov            d0, #0.50000000
    // 0x9b1930: r0 = withOpacity()
    //     0x9b1930: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b1934: r16 = 16.000000
    //     0x9b1934: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9b1938: ldr             x16, [x16, #0x188]
    // 0x9b193c: stp             x0, x16, [SP]
    // 0x9b1940: ldur            x1, [fp, #-0x28]
    // 0x9b1944: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b1944: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b1948: ldr             x4, [x4, #0xaa0]
    // 0x9b194c: r0 = copyWith()
    //     0x9b194c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b1950: stur            x0, [fp, #-0x28]
    // 0x9b1954: r0 = Text()
    //     0x9b1954: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b1958: mov             x1, x0
    // 0x9b195c: ldur            x0, [fp, #-0x18]
    // 0x9b1960: stur            x1, [fp, #-0x30]
    // 0x9b1964: StoreField: r1->field_b = r0
    //     0x9b1964: stur            w0, [x1, #0xb]
    // 0x9b1968: ldur            x0, [fp, #-0x28]
    // 0x9b196c: StoreField: r1->field_13 = r0
    //     0x9b196c: stur            w0, [x1, #0x13]
    // 0x9b1970: r0 = Center()
    //     0x9b1970: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9b1974: mov             x1, x0
    // 0x9b1978: r0 = Instance_Alignment
    //     0x9b1978: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9b197c: ldr             x0, [x0, #0xb10]
    // 0x9b1980: stur            x1, [fp, #-0x18]
    // 0x9b1984: StoreField: r1->field_f = r0
    //     0x9b1984: stur            w0, [x1, #0xf]
    // 0x9b1988: ldur            x0, [fp, #-0x30]
    // 0x9b198c: StoreField: r1->field_b = r0
    //     0x9b198c: stur            w0, [x1, #0xb]
    // 0x9b1990: r0 = Container()
    //     0x9b1990: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9b1994: stur            x0, [fp, #-0x28]
    // 0x9b1998: r16 = 34.000000
    //     0x9b1998: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0x9b199c: ldr             x16, [x16, #0x978]
    // 0x9b19a0: r30 = 34.000000
    //     0x9b19a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0x9b19a4: ldr             lr, [lr, #0x978]
    // 0x9b19a8: stp             lr, x16, [SP, #0x18]
    // 0x9b19ac: r16 = Instance_EdgeInsets
    //     0x9b19ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x9b19b0: ldr             x16, [x16, #0x980]
    // 0x9b19b4: ldur            lr, [fp, #-0x20]
    // 0x9b19b8: stp             lr, x16, [SP, #8]
    // 0x9b19bc: ldur            x16, [fp, #-0x18]
    // 0x9b19c0: str             x16, [SP]
    // 0x9b19c4: mov             x1, x0
    // 0x9b19c8: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0x9b19c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0x9b19cc: ldr             x4, [x4, #0x988]
    // 0x9b19d0: r0 = Container()
    //     0x9b19d0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9b19d4: ldur            x0, [fp, #-8]
    // 0x9b19d8: LoadField: r1 = r0->field_f
    //     0x9b19d8: ldur            w1, [x0, #0xf]
    // 0x9b19dc: DecompressPointer r1
    //     0x9b19dc: add             x1, x1, HEAP, lsl #32
    // 0x9b19e0: r0 = controller()
    //     0x9b19e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b19e4: LoadField: r1 = r0->field_77
    //     0x9b19e4: ldur            w1, [x0, #0x77]
    // 0x9b19e8: DecompressPointer r1
    //     0x9b19e8: add             x1, x1, HEAP, lsl #32
    // 0x9b19ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b19ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b19f0: r0 = toList()
    //     0x9b19f0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b19f4: mov             x3, x0
    // 0x9b19f8: ldur            x2, [fp, #-0x10]
    // 0x9b19fc: LoadField: r0 = r2->field_13
    //     0x9b19fc: ldur            w0, [x2, #0x13]
    // 0x9b1a00: DecompressPointer r0
    //     0x9b1a00: add             x0, x0, HEAP, lsl #32
    // 0x9b1a04: LoadField: r1 = r3->field_b
    //     0x9b1a04: ldur            w1, [x3, #0xb]
    // 0x9b1a08: r4 = LoadInt32Instr(r0)
    //     0x9b1a08: sbfx            x4, x0, #1, #0x1f
    //     0x9b1a0c: tbz             w0, #0, #0x9b1a14
    //     0x9b1a10: ldur            x4, [x0, #7]
    // 0x9b1a14: r0 = LoadInt32Instr(r1)
    //     0x9b1a14: sbfx            x0, x1, #1, #0x1f
    // 0x9b1a18: mov             x1, x4
    // 0x9b1a1c: cmp             x1, x0
    // 0x9b1a20: b.hs            #0x9b2ab0
    // 0x9b1a24: LoadField: r0 = r3->field_f
    //     0x9b1a24: ldur            w0, [x3, #0xf]
    // 0x9b1a28: DecompressPointer r0
    //     0x9b1a28: add             x0, x0, HEAP, lsl #32
    // 0x9b1a2c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1a2c: add             x16, x0, x4, lsl #2
    //     0x9b1a30: ldur            w1, [x16, #0xf]
    // 0x9b1a34: DecompressPointer r1
    //     0x9b1a34: add             x1, x1, HEAP, lsl #32
    // 0x9b1a38: cmp             w1, NULL
    // 0x9b1a3c: b.ne            #0x9b1a48
    // 0x9b1a40: r0 = Null
    //     0x9b1a40: mov             x0, NULL
    // 0x9b1a44: b               #0x9b1a50
    // 0x9b1a48: LoadField: r0 = r1->field_7
    //     0x9b1a48: ldur            w0, [x1, #7]
    // 0x9b1a4c: DecompressPointer r0
    //     0x9b1a4c: add             x0, x0, HEAP, lsl #32
    // 0x9b1a50: cmp             w0, NULL
    // 0x9b1a54: b.ne            #0x9b1a60
    // 0x9b1a58: r3 = ""
    //     0x9b1a58: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b1a5c: b               #0x9b1a64
    // 0x9b1a60: mov             x3, x0
    // 0x9b1a64: ldur            x0, [fp, #-8]
    // 0x9b1a68: stur            x3, [fp, #-0x18]
    // 0x9b1a6c: LoadField: r1 = r2->field_f
    //     0x9b1a6c: ldur            w1, [x2, #0xf]
    // 0x9b1a70: DecompressPointer r1
    //     0x9b1a70: add             x1, x1, HEAP, lsl #32
    // 0x9b1a74: r0 = of()
    //     0x9b1a74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b1a78: LoadField: r1 = r0->field_87
    //     0x9b1a78: ldur            w1, [x0, #0x87]
    // 0x9b1a7c: DecompressPointer r1
    //     0x9b1a7c: add             x1, x1, HEAP, lsl #32
    // 0x9b1a80: LoadField: r0 = r1->field_7
    //     0x9b1a80: ldur            w0, [x1, #7]
    // 0x9b1a84: DecompressPointer r0
    //     0x9b1a84: add             x0, x0, HEAP, lsl #32
    // 0x9b1a88: r16 = 14.000000
    //     0x9b1a88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x9b1a8c: ldr             x16, [x16, #0x1d8]
    // 0x9b1a90: r30 = Instance_Color
    //     0x9b1a90: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b1a94: stp             lr, x16, [SP]
    // 0x9b1a98: mov             x1, x0
    // 0x9b1a9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b1a9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b1aa0: ldr             x4, [x4, #0xaa0]
    // 0x9b1aa4: r0 = copyWith()
    //     0x9b1aa4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b1aa8: stur            x0, [fp, #-0x20]
    // 0x9b1aac: r0 = Text()
    //     0x9b1aac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b1ab0: mov             x2, x0
    // 0x9b1ab4: ldur            x0, [fp, #-0x18]
    // 0x9b1ab8: stur            x2, [fp, #-0x30]
    // 0x9b1abc: StoreField: r2->field_b = r0
    //     0x9b1abc: stur            w0, [x2, #0xb]
    // 0x9b1ac0: ldur            x0, [fp, #-0x20]
    // 0x9b1ac4: StoreField: r2->field_13 = r0
    //     0x9b1ac4: stur            w0, [x2, #0x13]
    // 0x9b1ac8: ldur            x0, [fp, #-8]
    // 0x9b1acc: LoadField: r1 = r0->field_f
    //     0x9b1acc: ldur            w1, [x0, #0xf]
    // 0x9b1ad0: DecompressPointer r1
    //     0x9b1ad0: add             x1, x1, HEAP, lsl #32
    // 0x9b1ad4: r0 = controller()
    //     0x9b1ad4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1ad8: LoadField: r1 = r0->field_77
    //     0x9b1ad8: ldur            w1, [x0, #0x77]
    // 0x9b1adc: DecompressPointer r1
    //     0x9b1adc: add             x1, x1, HEAP, lsl #32
    // 0x9b1ae0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1ae0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1ae4: r0 = toList()
    //     0x9b1ae4: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1ae8: mov             x3, x0
    // 0x9b1aec: ldur            x2, [fp, #-0x10]
    // 0x9b1af0: LoadField: r0 = r2->field_13
    //     0x9b1af0: ldur            w0, [x2, #0x13]
    // 0x9b1af4: DecompressPointer r0
    //     0x9b1af4: add             x0, x0, HEAP, lsl #32
    // 0x9b1af8: LoadField: r1 = r3->field_b
    //     0x9b1af8: ldur            w1, [x3, #0xb]
    // 0x9b1afc: r4 = LoadInt32Instr(r0)
    //     0x9b1afc: sbfx            x4, x0, #1, #0x1f
    //     0x9b1b00: tbz             w0, #0, #0x9b1b08
    //     0x9b1b04: ldur            x4, [x0, #7]
    // 0x9b1b08: r0 = LoadInt32Instr(r1)
    //     0x9b1b08: sbfx            x0, x1, #1, #0x1f
    // 0x9b1b0c: mov             x1, x4
    // 0x9b1b10: cmp             x1, x0
    // 0x9b1b14: b.hs            #0x9b2ab4
    // 0x9b1b18: LoadField: r0 = r3->field_f
    //     0x9b1b18: ldur            w0, [x3, #0xf]
    // 0x9b1b1c: DecompressPointer r0
    //     0x9b1b1c: add             x0, x0, HEAP, lsl #32
    // 0x9b1b20: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1b20: add             x16, x0, x4, lsl #2
    //     0x9b1b24: ldur            w1, [x16, #0xf]
    // 0x9b1b28: DecompressPointer r1
    //     0x9b1b28: add             x1, x1, HEAP, lsl #32
    // 0x9b1b2c: cmp             w1, NULL
    // 0x9b1b30: b.ne            #0x9b1b3c
    // 0x9b1b34: r0 = Null
    //     0x9b1b34: mov             x0, NULL
    // 0x9b1b38: b               #0x9b1b44
    // 0x9b1b3c: LoadField: r0 = r1->field_1f
    //     0x9b1b3c: ldur            w0, [x1, #0x1f]
    // 0x9b1b40: DecompressPointer r0
    //     0x9b1b40: add             x0, x0, HEAP, lsl #32
    // 0x9b1b44: cmp             w0, NULL
    // 0x9b1b48: b.ne            #0x9b1b50
    // 0x9b1b4c: r0 = ""
    //     0x9b1b4c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b1b50: stur            x0, [fp, #-0x18]
    // 0x9b1b54: LoadField: r1 = r2->field_f
    //     0x9b1b54: ldur            w1, [x2, #0xf]
    // 0x9b1b58: DecompressPointer r1
    //     0x9b1b58: add             x1, x1, HEAP, lsl #32
    // 0x9b1b5c: r0 = of()
    //     0x9b1b5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b1b60: LoadField: r1 = r0->field_87
    //     0x9b1b60: ldur            w1, [x0, #0x87]
    // 0x9b1b64: DecompressPointer r1
    //     0x9b1b64: add             x1, x1, HEAP, lsl #32
    // 0x9b1b68: LoadField: r0 = r1->field_33
    //     0x9b1b68: ldur            w0, [x1, #0x33]
    // 0x9b1b6c: DecompressPointer r0
    //     0x9b1b6c: add             x0, x0, HEAP, lsl #32
    // 0x9b1b70: stur            x0, [fp, #-0x20]
    // 0x9b1b74: cmp             w0, NULL
    // 0x9b1b78: b.ne            #0x9b1b84
    // 0x9b1b7c: r5 = Null
    //     0x9b1b7c: mov             x5, NULL
    // 0x9b1b80: b               #0x9b1bac
    // 0x9b1b84: r1 = Instance_Color
    //     0x9b1b84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b1b88: d0 = 0.500000
    //     0x9b1b88: fmov            d0, #0.50000000
    // 0x9b1b8c: r0 = withOpacity()
    //     0x9b1b8c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b1b90: r16 = 10.000000
    //     0x9b1b90: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x9b1b94: stp             x0, x16, [SP]
    // 0x9b1b98: ldur            x1, [fp, #-0x20]
    // 0x9b1b9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b1b9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b1ba0: ldr             x4, [x4, #0xaa0]
    // 0x9b1ba4: r0 = copyWith()
    //     0x9b1ba4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b1ba8: mov             x5, x0
    // 0x9b1bac: ldur            x3, [fp, #-8]
    // 0x9b1bb0: ldur            x2, [fp, #-0x10]
    // 0x9b1bb4: ldur            x4, [fp, #-0x28]
    // 0x9b1bb8: ldur            x1, [fp, #-0x30]
    // 0x9b1bbc: ldur            x0, [fp, #-0x18]
    // 0x9b1bc0: stur            x5, [fp, #-0x20]
    // 0x9b1bc4: r0 = Text()
    //     0x9b1bc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b1bc8: mov             x1, x0
    // 0x9b1bcc: ldur            x0, [fp, #-0x18]
    // 0x9b1bd0: stur            x1, [fp, #-0x38]
    // 0x9b1bd4: StoreField: r1->field_b = r0
    //     0x9b1bd4: stur            w0, [x1, #0xb]
    // 0x9b1bd8: ldur            x0, [fp, #-0x20]
    // 0x9b1bdc: StoreField: r1->field_13 = r0
    //     0x9b1bdc: stur            w0, [x1, #0x13]
    // 0x9b1be0: r0 = Padding()
    //     0x9b1be0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9b1be4: mov             x3, x0
    // 0x9b1be8: r0 = Instance_EdgeInsets
    //     0x9b1be8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0x9b1bec: ldr             x0, [x0, #0x990]
    // 0x9b1bf0: stur            x3, [fp, #-0x18]
    // 0x9b1bf4: StoreField: r3->field_f = r0
    //     0x9b1bf4: stur            w0, [x3, #0xf]
    // 0x9b1bf8: ldur            x0, [fp, #-0x38]
    // 0x9b1bfc: StoreField: r3->field_b = r0
    //     0x9b1bfc: stur            w0, [x3, #0xb]
    // 0x9b1c00: r1 = Null
    //     0x9b1c00: mov             x1, NULL
    // 0x9b1c04: r2 = 4
    //     0x9b1c04: movz            x2, #0x4
    // 0x9b1c08: r0 = AllocateArray()
    //     0x9b1c08: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b1c0c: mov             x2, x0
    // 0x9b1c10: ldur            x0, [fp, #-0x30]
    // 0x9b1c14: stur            x2, [fp, #-0x20]
    // 0x9b1c18: StoreField: r2->field_f = r0
    //     0x9b1c18: stur            w0, [x2, #0xf]
    // 0x9b1c1c: ldur            x0, [fp, #-0x18]
    // 0x9b1c20: StoreField: r2->field_13 = r0
    //     0x9b1c20: stur            w0, [x2, #0x13]
    // 0x9b1c24: r1 = <Widget>
    //     0x9b1c24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b1c28: r0 = AllocateGrowableArray()
    //     0x9b1c28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b1c2c: mov             x1, x0
    // 0x9b1c30: ldur            x0, [fp, #-0x20]
    // 0x9b1c34: stur            x1, [fp, #-0x18]
    // 0x9b1c38: StoreField: r1->field_f = r0
    //     0x9b1c38: stur            w0, [x1, #0xf]
    // 0x9b1c3c: r2 = 4
    //     0x9b1c3c: movz            x2, #0x4
    // 0x9b1c40: StoreField: r1->field_b = r2
    //     0x9b1c40: stur            w2, [x1, #0xb]
    // 0x9b1c44: r0 = Column()
    //     0x9b1c44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b1c48: mov             x3, x0
    // 0x9b1c4c: r0 = Instance_Axis
    //     0x9b1c4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b1c50: stur            x3, [fp, #-0x20]
    // 0x9b1c54: StoreField: r3->field_f = r0
    //     0x9b1c54: stur            w0, [x3, #0xf]
    // 0x9b1c58: r4 = Instance_MainAxisAlignment
    //     0x9b1c58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b1c5c: ldr             x4, [x4, #0xa08]
    // 0x9b1c60: StoreField: r3->field_13 = r4
    //     0x9b1c60: stur            w4, [x3, #0x13]
    // 0x9b1c64: r5 = Instance_MainAxisSize
    //     0x9b1c64: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b1c68: ldr             x5, [x5, #0xa10]
    // 0x9b1c6c: ArrayStore: r3[0] = r5  ; List_4
    //     0x9b1c6c: stur            w5, [x3, #0x17]
    // 0x9b1c70: r6 = Instance_CrossAxisAlignment
    //     0x9b1c70: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9b1c74: ldr             x6, [x6, #0x890]
    // 0x9b1c78: StoreField: r3->field_1b = r6
    //     0x9b1c78: stur            w6, [x3, #0x1b]
    // 0x9b1c7c: r7 = Instance_VerticalDirection
    //     0x9b1c7c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b1c80: ldr             x7, [x7, #0xa20]
    // 0x9b1c84: StoreField: r3->field_23 = r7
    //     0x9b1c84: stur            w7, [x3, #0x23]
    // 0x9b1c88: r8 = Instance_Clip
    //     0x9b1c88: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b1c8c: ldr             x8, [x8, #0x38]
    // 0x9b1c90: StoreField: r3->field_2b = r8
    //     0x9b1c90: stur            w8, [x3, #0x2b]
    // 0x9b1c94: StoreField: r3->field_2f = rZR
    //     0x9b1c94: stur            xzr, [x3, #0x2f]
    // 0x9b1c98: ldur            x1, [fp, #-0x18]
    // 0x9b1c9c: StoreField: r3->field_b = r1
    //     0x9b1c9c: stur            w1, [x3, #0xb]
    // 0x9b1ca0: r1 = Null
    //     0x9b1ca0: mov             x1, NULL
    // 0x9b1ca4: r2 = 6
    //     0x9b1ca4: movz            x2, #0x6
    // 0x9b1ca8: r0 = AllocateArray()
    //     0x9b1ca8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b1cac: mov             x2, x0
    // 0x9b1cb0: ldur            x0, [fp, #-0x28]
    // 0x9b1cb4: stur            x2, [fp, #-0x18]
    // 0x9b1cb8: StoreField: r2->field_f = r0
    //     0x9b1cb8: stur            w0, [x2, #0xf]
    // 0x9b1cbc: r16 = Instance_SizedBox
    //     0x9b1cbc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x9b1cc0: ldr             x16, [x16, #0x998]
    // 0x9b1cc4: StoreField: r2->field_13 = r16
    //     0x9b1cc4: stur            w16, [x2, #0x13]
    // 0x9b1cc8: ldur            x0, [fp, #-0x20]
    // 0x9b1ccc: ArrayStore: r2[0] = r0  ; List_4
    //     0x9b1ccc: stur            w0, [x2, #0x17]
    // 0x9b1cd0: r1 = <Widget>
    //     0x9b1cd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b1cd4: r0 = AllocateGrowableArray()
    //     0x9b1cd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b1cd8: mov             x1, x0
    // 0x9b1cdc: ldur            x0, [fp, #-0x18]
    // 0x9b1ce0: stur            x1, [fp, #-0x20]
    // 0x9b1ce4: StoreField: r1->field_f = r0
    //     0x9b1ce4: stur            w0, [x1, #0xf]
    // 0x9b1ce8: r2 = 6
    //     0x9b1ce8: movz            x2, #0x6
    // 0x9b1cec: StoreField: r1->field_b = r2
    //     0x9b1cec: stur            w2, [x1, #0xb]
    // 0x9b1cf0: r0 = Row()
    //     0x9b1cf0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b1cf4: mov             x2, x0
    // 0x9b1cf8: r0 = Instance_Axis
    //     0x9b1cf8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b1cfc: stur            x2, [fp, #-0x18]
    // 0x9b1d00: StoreField: r2->field_f = r0
    //     0x9b1d00: stur            w0, [x2, #0xf]
    // 0x9b1d04: r3 = Instance_MainAxisAlignment
    //     0x9b1d04: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b1d08: ldr             x3, [x3, #0xa08]
    // 0x9b1d0c: StoreField: r2->field_13 = r3
    //     0x9b1d0c: stur            w3, [x2, #0x13]
    // 0x9b1d10: r4 = Instance_MainAxisSize
    //     0x9b1d10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b1d14: ldr             x4, [x4, #0xa10]
    // 0x9b1d18: ArrayStore: r2[0] = r4  ; List_4
    //     0x9b1d18: stur            w4, [x2, #0x17]
    // 0x9b1d1c: r5 = Instance_CrossAxisAlignment
    //     0x9b1d1c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b1d20: ldr             x5, [x5, #0xa18]
    // 0x9b1d24: StoreField: r2->field_1b = r5
    //     0x9b1d24: stur            w5, [x2, #0x1b]
    // 0x9b1d28: r6 = Instance_VerticalDirection
    //     0x9b1d28: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b1d2c: ldr             x6, [x6, #0xa20]
    // 0x9b1d30: StoreField: r2->field_23 = r6
    //     0x9b1d30: stur            w6, [x2, #0x23]
    // 0x9b1d34: r7 = Instance_Clip
    //     0x9b1d34: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b1d38: ldr             x7, [x7, #0x38]
    // 0x9b1d3c: StoreField: r2->field_2b = r7
    //     0x9b1d3c: stur            w7, [x2, #0x2b]
    // 0x9b1d40: StoreField: r2->field_2f = rZR
    //     0x9b1d40: stur            xzr, [x2, #0x2f]
    // 0x9b1d44: ldur            x1, [fp, #-0x20]
    // 0x9b1d48: StoreField: r2->field_b = r1
    //     0x9b1d48: stur            w1, [x2, #0xb]
    // 0x9b1d4c: ldur            x8, [fp, #-8]
    // 0x9b1d50: LoadField: r1 = r8->field_f
    //     0x9b1d50: ldur            w1, [x8, #0xf]
    // 0x9b1d54: DecompressPointer r1
    //     0x9b1d54: add             x1, x1, HEAP, lsl #32
    // 0x9b1d58: r0 = controller()
    //     0x9b1d58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1d5c: LoadField: r1 = r0->field_77
    //     0x9b1d5c: ldur            w1, [x0, #0x77]
    // 0x9b1d60: DecompressPointer r1
    //     0x9b1d60: add             x1, x1, HEAP, lsl #32
    // 0x9b1d64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1d64: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1d68: r0 = toList()
    //     0x9b1d68: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1d6c: mov             x3, x0
    // 0x9b1d70: ldur            x2, [fp, #-0x10]
    // 0x9b1d74: LoadField: r0 = r2->field_13
    //     0x9b1d74: ldur            w0, [x2, #0x13]
    // 0x9b1d78: DecompressPointer r0
    //     0x9b1d78: add             x0, x0, HEAP, lsl #32
    // 0x9b1d7c: LoadField: r1 = r3->field_b
    //     0x9b1d7c: ldur            w1, [x3, #0xb]
    // 0x9b1d80: r4 = LoadInt32Instr(r0)
    //     0x9b1d80: sbfx            x4, x0, #1, #0x1f
    //     0x9b1d84: tbz             w0, #0, #0x9b1d8c
    //     0x9b1d88: ldur            x4, [x0, #7]
    // 0x9b1d8c: r0 = LoadInt32Instr(r1)
    //     0x9b1d8c: sbfx            x0, x1, #1, #0x1f
    // 0x9b1d90: mov             x1, x4
    // 0x9b1d94: cmp             x1, x0
    // 0x9b1d98: b.hs            #0x9b2ab8
    // 0x9b1d9c: LoadField: r0 = r3->field_f
    //     0x9b1d9c: ldur            w0, [x3, #0xf]
    // 0x9b1da0: DecompressPointer r0
    //     0x9b1da0: add             x0, x0, HEAP, lsl #32
    // 0x9b1da4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1da4: add             x16, x0, x4, lsl #2
    //     0x9b1da8: ldur            w1, [x16, #0xf]
    // 0x9b1dac: DecompressPointer r1
    //     0x9b1dac: add             x1, x1, HEAP, lsl #32
    // 0x9b1db0: cmp             w1, NULL
    // 0x9b1db4: b.eq            #0x9b1dc8
    // 0x9b1db8: LoadField: r0 = r1->field_f
    //     0x9b1db8: ldur            w0, [x1, #0xf]
    // 0x9b1dbc: DecompressPointer r0
    //     0x9b1dbc: add             x0, x0, HEAP, lsl #32
    // 0x9b1dc0: cmp             w0, #0xa
    // 0x9b1dc4: b.eq            #0x9b1e44
    // 0x9b1dc8: ldur            x0, [fp, #-8]
    // 0x9b1dcc: LoadField: r1 = r0->field_f
    //     0x9b1dcc: ldur            w1, [x0, #0xf]
    // 0x9b1dd0: DecompressPointer r1
    //     0x9b1dd0: add             x1, x1, HEAP, lsl #32
    // 0x9b1dd4: r0 = controller()
    //     0x9b1dd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1dd8: LoadField: r1 = r0->field_77
    //     0x9b1dd8: ldur            w1, [x0, #0x77]
    // 0x9b1ddc: DecompressPointer r1
    //     0x9b1ddc: add             x1, x1, HEAP, lsl #32
    // 0x9b1de0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1de0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1de4: r0 = toList()
    //     0x9b1de4: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1de8: mov             x3, x0
    // 0x9b1dec: ldur            x2, [fp, #-0x10]
    // 0x9b1df0: LoadField: r0 = r2->field_13
    //     0x9b1df0: ldur            w0, [x2, #0x13]
    // 0x9b1df4: DecompressPointer r0
    //     0x9b1df4: add             x0, x0, HEAP, lsl #32
    // 0x9b1df8: LoadField: r1 = r3->field_b
    //     0x9b1df8: ldur            w1, [x3, #0xb]
    // 0x9b1dfc: r4 = LoadInt32Instr(r0)
    //     0x9b1dfc: sbfx            x4, x0, #1, #0x1f
    //     0x9b1e00: tbz             w0, #0, #0x9b1e08
    //     0x9b1e04: ldur            x4, [x0, #7]
    // 0x9b1e08: r0 = LoadInt32Instr(r1)
    //     0x9b1e08: sbfx            x0, x1, #1, #0x1f
    // 0x9b1e0c: mov             x1, x4
    // 0x9b1e10: cmp             x1, x0
    // 0x9b1e14: b.hs            #0x9b2abc
    // 0x9b1e18: LoadField: r0 = r3->field_f
    //     0x9b1e18: ldur            w0, [x3, #0xf]
    // 0x9b1e1c: DecompressPointer r0
    //     0x9b1e1c: add             x0, x0, HEAP, lsl #32
    // 0x9b1e20: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1e20: add             x16, x0, x4, lsl #2
    //     0x9b1e24: ldur            w1, [x16, #0xf]
    // 0x9b1e28: DecompressPointer r1
    //     0x9b1e28: add             x1, x1, HEAP, lsl #32
    // 0x9b1e2c: cmp             w1, NULL
    // 0x9b1e30: b.eq            #0x9b1e50
    // 0x9b1e34: LoadField: r0 = r1->field_f
    //     0x9b1e34: ldur            w0, [x1, #0xf]
    // 0x9b1e38: DecompressPointer r0
    //     0x9b1e38: add             x0, x0, HEAP, lsl #32
    // 0x9b1e3c: cmp             w0, #8
    // 0x9b1e40: b.ne            #0x9b1e50
    // 0x9b1e44: r1 = Instance_Color
    //     0x9b1e44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9b1e48: ldr             x1, [x1, #0x858]
    // 0x9b1e4c: b               #0x9b1f80
    // 0x9b1e50: ldur            x0, [fp, #-8]
    // 0x9b1e54: LoadField: r1 = r0->field_f
    //     0x9b1e54: ldur            w1, [x0, #0xf]
    // 0x9b1e58: DecompressPointer r1
    //     0x9b1e58: add             x1, x1, HEAP, lsl #32
    // 0x9b1e5c: r0 = controller()
    //     0x9b1e5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1e60: LoadField: r1 = r0->field_77
    //     0x9b1e60: ldur            w1, [x0, #0x77]
    // 0x9b1e64: DecompressPointer r1
    //     0x9b1e64: add             x1, x1, HEAP, lsl #32
    // 0x9b1e68: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1e68: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1e6c: r0 = toList()
    //     0x9b1e6c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1e70: mov             x3, x0
    // 0x9b1e74: ldur            x2, [fp, #-0x10]
    // 0x9b1e78: LoadField: r0 = r2->field_13
    //     0x9b1e78: ldur            w0, [x2, #0x13]
    // 0x9b1e7c: DecompressPointer r0
    //     0x9b1e7c: add             x0, x0, HEAP, lsl #32
    // 0x9b1e80: LoadField: r1 = r3->field_b
    //     0x9b1e80: ldur            w1, [x3, #0xb]
    // 0x9b1e84: r4 = LoadInt32Instr(r0)
    //     0x9b1e84: sbfx            x4, x0, #1, #0x1f
    //     0x9b1e88: tbz             w0, #0, #0x9b1e90
    //     0x9b1e8c: ldur            x4, [x0, #7]
    // 0x9b1e90: r0 = LoadInt32Instr(r1)
    //     0x9b1e90: sbfx            x0, x1, #1, #0x1f
    // 0x9b1e94: mov             x1, x4
    // 0x9b1e98: cmp             x1, x0
    // 0x9b1e9c: b.hs            #0x9b2ac0
    // 0x9b1ea0: LoadField: r0 = r3->field_f
    //     0x9b1ea0: ldur            w0, [x3, #0xf]
    // 0x9b1ea4: DecompressPointer r0
    //     0x9b1ea4: add             x0, x0, HEAP, lsl #32
    // 0x9b1ea8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1ea8: add             x16, x0, x4, lsl #2
    //     0x9b1eac: ldur            w1, [x16, #0xf]
    // 0x9b1eb0: DecompressPointer r1
    //     0x9b1eb0: add             x1, x1, HEAP, lsl #32
    // 0x9b1eb4: cmp             w1, NULL
    // 0x9b1eb8: b.eq            #0x9b1eec
    // 0x9b1ebc: LoadField: r0 = r1->field_f
    //     0x9b1ebc: ldur            w0, [x1, #0xf]
    // 0x9b1ec0: DecompressPointer r0
    //     0x9b1ec0: add             x0, x0, HEAP, lsl #32
    // 0x9b1ec4: cmp             w0, #6
    // 0x9b1ec8: b.ne            #0x9b1ee8
    // 0x9b1ecc: r1 = Instance_Color
    //     0x9b1ecc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9b1ed0: ldr             x1, [x1, #0x858]
    // 0x9b1ed4: d0 = 0.700000
    //     0x9b1ed4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9b1ed8: ldr             d0, [x17, #0xf48]
    // 0x9b1edc: r0 = withOpacity()
    //     0x9b1edc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b1ee0: ldur            x2, [fp, #-0x10]
    // 0x9b1ee4: b               #0x9b1f7c
    // 0x9b1ee8: ldur            x2, [fp, #-0x10]
    // 0x9b1eec: ldur            x0, [fp, #-8]
    // 0x9b1ef0: LoadField: r1 = r0->field_f
    //     0x9b1ef0: ldur            w1, [x0, #0xf]
    // 0x9b1ef4: DecompressPointer r1
    //     0x9b1ef4: add             x1, x1, HEAP, lsl #32
    // 0x9b1ef8: r0 = controller()
    //     0x9b1ef8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1efc: LoadField: r1 = r0->field_77
    //     0x9b1efc: ldur            w1, [x0, #0x77]
    // 0x9b1f00: DecompressPointer r1
    //     0x9b1f00: add             x1, x1, HEAP, lsl #32
    // 0x9b1f04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1f04: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1f08: r0 = toList()
    //     0x9b1f08: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1f0c: mov             x3, x0
    // 0x9b1f10: ldur            x2, [fp, #-0x10]
    // 0x9b1f14: LoadField: r0 = r2->field_13
    //     0x9b1f14: ldur            w0, [x2, #0x13]
    // 0x9b1f18: DecompressPointer r0
    //     0x9b1f18: add             x0, x0, HEAP, lsl #32
    // 0x9b1f1c: LoadField: r1 = r3->field_b
    //     0x9b1f1c: ldur            w1, [x3, #0xb]
    // 0x9b1f20: r4 = LoadInt32Instr(r0)
    //     0x9b1f20: sbfx            x4, x0, #1, #0x1f
    //     0x9b1f24: tbz             w0, #0, #0x9b1f2c
    //     0x9b1f28: ldur            x4, [x0, #7]
    // 0x9b1f2c: r0 = LoadInt32Instr(r1)
    //     0x9b1f2c: sbfx            x0, x1, #1, #0x1f
    // 0x9b1f30: mov             x1, x4
    // 0x9b1f34: cmp             x1, x0
    // 0x9b1f38: b.hs            #0x9b2ac4
    // 0x9b1f3c: LoadField: r0 = r3->field_f
    //     0x9b1f3c: ldur            w0, [x3, #0xf]
    // 0x9b1f40: DecompressPointer r0
    //     0x9b1f40: add             x0, x0, HEAP, lsl #32
    // 0x9b1f44: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b1f44: add             x16, x0, x4, lsl #2
    //     0x9b1f48: ldur            w1, [x16, #0xf]
    // 0x9b1f4c: DecompressPointer r1
    //     0x9b1f4c: add             x1, x1, HEAP, lsl #32
    // 0x9b1f50: cmp             w1, NULL
    // 0x9b1f54: b.eq            #0x9b1f74
    // 0x9b1f58: LoadField: r0 = r1->field_f
    //     0x9b1f58: ldur            w0, [x1, #0xf]
    // 0x9b1f5c: DecompressPointer r0
    //     0x9b1f5c: add             x0, x0, HEAP, lsl #32
    // 0x9b1f60: cmp             w0, #4
    // 0x9b1f64: b.ne            #0x9b1f74
    // 0x9b1f68: r0 = Instance_Color
    //     0x9b1f68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0x9b1f6c: ldr             x0, [x0, #0x860]
    // 0x9b1f70: b               #0x9b1f7c
    // 0x9b1f74: r0 = Instance_Color
    //     0x9b1f74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x9b1f78: ldr             x0, [x0, #0x50]
    // 0x9b1f7c: mov             x1, x0
    // 0x9b1f80: ldur            x0, [fp, #-8]
    // 0x9b1f84: stur            x1, [fp, #-0x20]
    // 0x9b1f88: r0 = ColorFilter()
    //     0x9b1f88: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x9b1f8c: mov             x1, x0
    // 0x9b1f90: ldur            x0, [fp, #-0x20]
    // 0x9b1f94: stur            x1, [fp, #-0x28]
    // 0x9b1f98: StoreField: r1->field_7 = r0
    //     0x9b1f98: stur            w0, [x1, #7]
    // 0x9b1f9c: r0 = Instance_BlendMode
    //     0x9b1f9c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x9b1fa0: ldr             x0, [x0, #0xb30]
    // 0x9b1fa4: StoreField: r1->field_b = r0
    //     0x9b1fa4: stur            w0, [x1, #0xb]
    // 0x9b1fa8: r0 = 1
    //     0x9b1fa8: movz            x0, #0x1
    // 0x9b1fac: StoreField: r1->field_13 = r0
    //     0x9b1fac: stur            x0, [x1, #0x13]
    // 0x9b1fb0: r0 = SvgPicture()
    //     0x9b1fb0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9b1fb4: stur            x0, [fp, #-0x20]
    // 0x9b1fb8: ldur            x16, [fp, #-0x28]
    // 0x9b1fbc: str             x16, [SP]
    // 0x9b1fc0: mov             x1, x0
    // 0x9b1fc4: r2 = "assets/images/green_star.svg"
    //     0x9b1fc4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0x9b1fc8: ldr             x2, [x2, #0x9a0]
    // 0x9b1fcc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x9b1fcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x9b1fd0: ldr             x4, [x4, #0xa38]
    // 0x9b1fd4: r0 = SvgPicture.asset()
    //     0x9b1fd4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9b1fd8: ldur            x0, [fp, #-8]
    // 0x9b1fdc: LoadField: r1 = r0->field_f
    //     0x9b1fdc: ldur            w1, [x0, #0xf]
    // 0x9b1fe0: DecompressPointer r1
    //     0x9b1fe0: add             x1, x1, HEAP, lsl #32
    // 0x9b1fe4: r0 = controller()
    //     0x9b1fe4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b1fe8: LoadField: r1 = r0->field_77
    //     0x9b1fe8: ldur            w1, [x0, #0x77]
    // 0x9b1fec: DecompressPointer r1
    //     0x9b1fec: add             x1, x1, HEAP, lsl #32
    // 0x9b1ff0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b1ff0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b1ff4: r0 = toList()
    //     0x9b1ff4: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b1ff8: mov             x3, x0
    // 0x9b1ffc: ldur            x2, [fp, #-0x10]
    // 0x9b2000: LoadField: r0 = r2->field_13
    //     0x9b2000: ldur            w0, [x2, #0x13]
    // 0x9b2004: DecompressPointer r0
    //     0x9b2004: add             x0, x0, HEAP, lsl #32
    // 0x9b2008: LoadField: r1 = r3->field_b
    //     0x9b2008: ldur            w1, [x3, #0xb]
    // 0x9b200c: r4 = LoadInt32Instr(r0)
    //     0x9b200c: sbfx            x4, x0, #1, #0x1f
    //     0x9b2010: tbz             w0, #0, #0x9b2018
    //     0x9b2014: ldur            x4, [x0, #7]
    // 0x9b2018: r0 = LoadInt32Instr(r1)
    //     0x9b2018: sbfx            x0, x1, #1, #0x1f
    // 0x9b201c: mov             x1, x4
    // 0x9b2020: cmp             x1, x0
    // 0x9b2024: b.hs            #0x9b2ac8
    // 0x9b2028: LoadField: r0 = r3->field_f
    //     0x9b2028: ldur            w0, [x3, #0xf]
    // 0x9b202c: DecompressPointer r0
    //     0x9b202c: add             x0, x0, HEAP, lsl #32
    // 0x9b2030: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b2030: add             x16, x0, x4, lsl #2
    //     0x9b2034: ldur            w1, [x16, #0xf]
    // 0x9b2038: DecompressPointer r1
    //     0x9b2038: add             x1, x1, HEAP, lsl #32
    // 0x9b203c: cmp             w1, NULL
    // 0x9b2040: b.ne            #0x9b204c
    // 0x9b2044: r0 = Null
    //     0x9b2044: mov             x0, NULL
    // 0x9b2048: b               #0x9b2080
    // 0x9b204c: LoadField: r0 = r1->field_f
    //     0x9b204c: ldur            w0, [x1, #0xf]
    // 0x9b2050: DecompressPointer r0
    //     0x9b2050: add             x0, x0, HEAP, lsl #32
    // 0x9b2054: r1 = 60
    //     0x9b2054: movz            x1, #0x3c
    // 0x9b2058: branchIfSmi(r0, 0x9b2064)
    //     0x9b2058: tbz             w0, #0, #0x9b2064
    // 0x9b205c: r1 = LoadClassIdInstr(r0)
    //     0x9b205c: ldur            x1, [x0, #-1]
    //     0x9b2060: ubfx            x1, x1, #0xc, #0x14
    // 0x9b2064: str             x0, [SP]
    // 0x9b2068: mov             x0, x1
    // 0x9b206c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x9b206c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x9b2070: r0 = GDT[cid_x0 + 0x2700]()
    //     0x9b2070: movz            x17, #0x2700
    //     0x9b2074: add             lr, x0, x17
    //     0x9b2078: ldr             lr, [x21, lr, lsl #3]
    //     0x9b207c: blr             lr
    // 0x9b2080: cmp             w0, NULL
    // 0x9b2084: b.ne            #0x9b2090
    // 0x9b2088: r5 = ""
    //     0x9b2088: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b208c: b               #0x9b2094
    // 0x9b2090: mov             x5, x0
    // 0x9b2094: ldur            x0, [fp, #-8]
    // 0x9b2098: ldur            x2, [fp, #-0x10]
    // 0x9b209c: ldur            x4, [fp, #-0x18]
    // 0x9b20a0: ldur            x3, [fp, #-0x20]
    // 0x9b20a4: stur            x5, [fp, #-0x28]
    // 0x9b20a8: LoadField: r1 = r2->field_f
    //     0x9b20a8: ldur            w1, [x2, #0xf]
    // 0x9b20ac: DecompressPointer r1
    //     0x9b20ac: add             x1, x1, HEAP, lsl #32
    // 0x9b20b0: r0 = of()
    //     0x9b20b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b20b4: LoadField: r1 = r0->field_87
    //     0x9b20b4: ldur            w1, [x0, #0x87]
    // 0x9b20b8: DecompressPointer r1
    //     0x9b20b8: add             x1, x1, HEAP, lsl #32
    // 0x9b20bc: LoadField: r0 = r1->field_7
    //     0x9b20bc: ldur            w0, [x1, #7]
    // 0x9b20c0: DecompressPointer r0
    //     0x9b20c0: add             x0, x0, HEAP, lsl #32
    // 0x9b20c4: r16 = 12.000000
    //     0x9b20c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9b20c8: ldr             x16, [x16, #0x9e8]
    // 0x9b20cc: r30 = Instance_Color
    //     0x9b20cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b20d0: stp             lr, x16, [SP]
    // 0x9b20d4: mov             x1, x0
    // 0x9b20d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b20d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b20dc: ldr             x4, [x4, #0xaa0]
    // 0x9b20e0: r0 = copyWith()
    //     0x9b20e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b20e4: stur            x0, [fp, #-0x30]
    // 0x9b20e8: r0 = Text()
    //     0x9b20e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b20ec: mov             x3, x0
    // 0x9b20f0: ldur            x0, [fp, #-0x28]
    // 0x9b20f4: stur            x3, [fp, #-0x38]
    // 0x9b20f8: StoreField: r3->field_b = r0
    //     0x9b20f8: stur            w0, [x3, #0xb]
    // 0x9b20fc: ldur            x0, [fp, #-0x30]
    // 0x9b2100: StoreField: r3->field_13 = r0
    //     0x9b2100: stur            w0, [x3, #0x13]
    // 0x9b2104: r1 = Null
    //     0x9b2104: mov             x1, NULL
    // 0x9b2108: r2 = 6
    //     0x9b2108: movz            x2, #0x6
    // 0x9b210c: r0 = AllocateArray()
    //     0x9b210c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b2110: mov             x2, x0
    // 0x9b2114: ldur            x0, [fp, #-0x20]
    // 0x9b2118: stur            x2, [fp, #-0x28]
    // 0x9b211c: StoreField: r2->field_f = r0
    //     0x9b211c: stur            w0, [x2, #0xf]
    // 0x9b2120: r16 = Instance_SizedBox
    //     0x9b2120: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] Obj!SizedBox@d67ea1
    //     0x9b2124: ldr             x16, [x16, #0x9a8]
    // 0x9b2128: StoreField: r2->field_13 = r16
    //     0x9b2128: stur            w16, [x2, #0x13]
    // 0x9b212c: ldur            x0, [fp, #-0x38]
    // 0x9b2130: ArrayStore: r2[0] = r0  ; List_4
    //     0x9b2130: stur            w0, [x2, #0x17]
    // 0x9b2134: r1 = <Widget>
    //     0x9b2134: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b2138: r0 = AllocateGrowableArray()
    //     0x9b2138: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b213c: mov             x1, x0
    // 0x9b2140: ldur            x0, [fp, #-0x28]
    // 0x9b2144: stur            x1, [fp, #-0x20]
    // 0x9b2148: StoreField: r1->field_f = r0
    //     0x9b2148: stur            w0, [x1, #0xf]
    // 0x9b214c: r0 = 6
    //     0x9b214c: movz            x0, #0x6
    // 0x9b2150: StoreField: r1->field_b = r0
    //     0x9b2150: stur            w0, [x1, #0xb]
    // 0x9b2154: r0 = Row()
    //     0x9b2154: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b2158: mov             x3, x0
    // 0x9b215c: r0 = Instance_Axis
    //     0x9b215c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b2160: stur            x3, [fp, #-0x28]
    // 0x9b2164: StoreField: r3->field_f = r0
    //     0x9b2164: stur            w0, [x3, #0xf]
    // 0x9b2168: r4 = Instance_MainAxisAlignment
    //     0x9b2168: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b216c: ldr             x4, [x4, #0xa08]
    // 0x9b2170: StoreField: r3->field_13 = r4
    //     0x9b2170: stur            w4, [x3, #0x13]
    // 0x9b2174: r5 = Instance_MainAxisSize
    //     0x9b2174: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b2178: ldr             x5, [x5, #0xa10]
    // 0x9b217c: ArrayStore: r3[0] = r5  ; List_4
    //     0x9b217c: stur            w5, [x3, #0x17]
    // 0x9b2180: r6 = Instance_CrossAxisAlignment
    //     0x9b2180: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b2184: ldr             x6, [x6, #0xa18]
    // 0x9b2188: StoreField: r3->field_1b = r6
    //     0x9b2188: stur            w6, [x3, #0x1b]
    // 0x9b218c: r7 = Instance_VerticalDirection
    //     0x9b218c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b2190: ldr             x7, [x7, #0xa20]
    // 0x9b2194: StoreField: r3->field_23 = r7
    //     0x9b2194: stur            w7, [x3, #0x23]
    // 0x9b2198: r8 = Instance_Clip
    //     0x9b2198: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b219c: ldr             x8, [x8, #0x38]
    // 0x9b21a0: StoreField: r3->field_2b = r8
    //     0x9b21a0: stur            w8, [x3, #0x2b]
    // 0x9b21a4: StoreField: r3->field_2f = rZR
    //     0x9b21a4: stur            xzr, [x3, #0x2f]
    // 0x9b21a8: ldur            x1, [fp, #-0x20]
    // 0x9b21ac: StoreField: r3->field_b = r1
    //     0x9b21ac: stur            w1, [x3, #0xb]
    // 0x9b21b0: r1 = Null
    //     0x9b21b0: mov             x1, NULL
    // 0x9b21b4: r2 = 4
    //     0x9b21b4: movz            x2, #0x4
    // 0x9b21b8: r0 = AllocateArray()
    //     0x9b21b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b21bc: mov             x2, x0
    // 0x9b21c0: ldur            x0, [fp, #-0x18]
    // 0x9b21c4: stur            x2, [fp, #-0x20]
    // 0x9b21c8: StoreField: r2->field_f = r0
    //     0x9b21c8: stur            w0, [x2, #0xf]
    // 0x9b21cc: ldur            x0, [fp, #-0x28]
    // 0x9b21d0: StoreField: r2->field_13 = r0
    //     0x9b21d0: stur            w0, [x2, #0x13]
    // 0x9b21d4: r1 = <Widget>
    //     0x9b21d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b21d8: r0 = AllocateGrowableArray()
    //     0x9b21d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b21dc: mov             x1, x0
    // 0x9b21e0: ldur            x0, [fp, #-0x20]
    // 0x9b21e4: stur            x1, [fp, #-0x18]
    // 0x9b21e8: StoreField: r1->field_f = r0
    //     0x9b21e8: stur            w0, [x1, #0xf]
    // 0x9b21ec: r2 = 4
    //     0x9b21ec: movz            x2, #0x4
    // 0x9b21f0: StoreField: r1->field_b = r2
    //     0x9b21f0: stur            w2, [x1, #0xb]
    // 0x9b21f4: r0 = Row()
    //     0x9b21f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b21f8: mov             x3, x0
    // 0x9b21fc: r0 = Instance_Axis
    //     0x9b21fc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b2200: stur            x3, [fp, #-0x20]
    // 0x9b2204: StoreField: r3->field_f = r0
    //     0x9b2204: stur            w0, [x3, #0xf]
    // 0x9b2208: r0 = Instance_MainAxisAlignment
    //     0x9b2208: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x9b220c: ldr             x0, [x0, #0xa8]
    // 0x9b2210: StoreField: r3->field_13 = r0
    //     0x9b2210: stur            w0, [x3, #0x13]
    // 0x9b2214: r0 = Instance_MainAxisSize
    //     0x9b2214: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b2218: ldr             x0, [x0, #0xa10]
    // 0x9b221c: ArrayStore: r3[0] = r0  ; List_4
    //     0x9b221c: stur            w0, [x3, #0x17]
    // 0x9b2220: r1 = Instance_CrossAxisAlignment
    //     0x9b2220: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b2224: ldr             x1, [x1, #0xa18]
    // 0x9b2228: StoreField: r3->field_1b = r1
    //     0x9b2228: stur            w1, [x3, #0x1b]
    // 0x9b222c: r4 = Instance_VerticalDirection
    //     0x9b222c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b2230: ldr             x4, [x4, #0xa20]
    // 0x9b2234: StoreField: r3->field_23 = r4
    //     0x9b2234: stur            w4, [x3, #0x23]
    // 0x9b2238: r5 = Instance_Clip
    //     0x9b2238: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b223c: ldr             x5, [x5, #0x38]
    // 0x9b2240: StoreField: r3->field_2b = r5
    //     0x9b2240: stur            w5, [x3, #0x2b]
    // 0x9b2244: StoreField: r3->field_2f = rZR
    //     0x9b2244: stur            xzr, [x3, #0x2f]
    // 0x9b2248: ldur            x1, [fp, #-0x18]
    // 0x9b224c: StoreField: r3->field_b = r1
    //     0x9b224c: stur            w1, [x3, #0xb]
    // 0x9b2250: r1 = Null
    //     0x9b2250: mov             x1, NULL
    // 0x9b2254: r2 = 2
    //     0x9b2254: movz            x2, #0x2
    // 0x9b2258: r0 = AllocateArray()
    //     0x9b2258: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b225c: mov             x2, x0
    // 0x9b2260: ldur            x0, [fp, #-0x20]
    // 0x9b2264: stur            x2, [fp, #-0x18]
    // 0x9b2268: StoreField: r2->field_f = r0
    //     0x9b2268: stur            w0, [x2, #0xf]
    // 0x9b226c: r1 = <Widget>
    //     0x9b226c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b2270: r0 = AllocateGrowableArray()
    //     0x9b2270: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b2274: mov             x2, x0
    // 0x9b2278: ldur            x0, [fp, #-0x18]
    // 0x9b227c: stur            x2, [fp, #-0x20]
    // 0x9b2280: StoreField: r2->field_f = r0
    //     0x9b2280: stur            w0, [x2, #0xf]
    // 0x9b2284: r0 = 2
    //     0x9b2284: movz            x0, #0x2
    // 0x9b2288: StoreField: r2->field_b = r0
    //     0x9b2288: stur            w0, [x2, #0xb]
    // 0x9b228c: ldur            x0, [fp, #-8]
    // 0x9b2290: LoadField: r1 = r0->field_f
    //     0x9b2290: ldur            w1, [x0, #0xf]
    // 0x9b2294: DecompressPointer r1
    //     0x9b2294: add             x1, x1, HEAP, lsl #32
    // 0x9b2298: r0 = controller()
    //     0x9b2298: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b229c: LoadField: r1 = r0->field_77
    //     0x9b229c: ldur            w1, [x0, #0x77]
    // 0x9b22a0: DecompressPointer r1
    //     0x9b22a0: add             x1, x1, HEAP, lsl #32
    // 0x9b22a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b22a4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b22a8: r0 = toList()
    //     0x9b22a8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b22ac: mov             x3, x0
    // 0x9b22b0: ldur            x2, [fp, #-0x10]
    // 0x9b22b4: LoadField: r0 = r2->field_13
    //     0x9b22b4: ldur            w0, [x2, #0x13]
    // 0x9b22b8: DecompressPointer r0
    //     0x9b22b8: add             x0, x0, HEAP, lsl #32
    // 0x9b22bc: LoadField: r1 = r3->field_b
    //     0x9b22bc: ldur            w1, [x3, #0xb]
    // 0x9b22c0: r4 = LoadInt32Instr(r0)
    //     0x9b22c0: sbfx            x4, x0, #1, #0x1f
    //     0x9b22c4: tbz             w0, #0, #0x9b22cc
    //     0x9b22c8: ldur            x4, [x0, #7]
    // 0x9b22cc: r0 = LoadInt32Instr(r1)
    //     0x9b22cc: sbfx            x0, x1, #1, #0x1f
    // 0x9b22d0: mov             x1, x4
    // 0x9b22d4: cmp             x1, x0
    // 0x9b22d8: b.hs            #0x9b2acc
    // 0x9b22dc: LoadField: r0 = r3->field_f
    //     0x9b22dc: ldur            w0, [x3, #0xf]
    // 0x9b22e0: DecompressPointer r0
    //     0x9b22e0: add             x0, x0, HEAP, lsl #32
    // 0x9b22e4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b22e4: add             x16, x0, x4, lsl #2
    //     0x9b22e8: ldur            w1, [x16, #0xf]
    // 0x9b22ec: DecompressPointer r1
    //     0x9b22ec: add             x1, x1, HEAP, lsl #32
    // 0x9b22f0: cmp             w1, NULL
    // 0x9b22f4: b.ne            #0x9b2300
    // 0x9b22f8: r0 = Null
    //     0x9b22f8: mov             x0, NULL
    // 0x9b22fc: b               #0x9b232c
    // 0x9b2300: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b2300: ldur            w0, [x1, #0x17]
    // 0x9b2304: DecompressPointer r0
    //     0x9b2304: add             x0, x0, HEAP, lsl #32
    // 0x9b2308: cmp             w0, NULL
    // 0x9b230c: b.ne            #0x9b2318
    // 0x9b2310: r0 = Null
    //     0x9b2310: mov             x0, NULL
    // 0x9b2314: b               #0x9b232c
    // 0x9b2318: LoadField: r1 = r0->field_7
    //     0x9b2318: ldur            w1, [x0, #7]
    // 0x9b231c: cbnz            w1, #0x9b2328
    // 0x9b2320: r0 = false
    //     0x9b2320: add             x0, NULL, #0x30  ; false
    // 0x9b2324: b               #0x9b232c
    // 0x9b2328: r0 = true
    //     0x9b2328: add             x0, NULL, #0x20  ; true
    // 0x9b232c: cmp             w0, NULL
    // 0x9b2330: b.ne            #0x9b233c
    // 0x9b2334: ldur            x2, [fp, #-0x20]
    // 0x9b2338: b               #0x9b25dc
    // 0x9b233c: tbnz            w0, #4, #0x9b25d8
    // 0x9b2340: ldur            x0, [fp, #-8]
    // 0x9b2344: LoadField: r1 = r0->field_f
    //     0x9b2344: ldur            w1, [x0, #0xf]
    // 0x9b2348: DecompressPointer r1
    //     0x9b2348: add             x1, x1, HEAP, lsl #32
    // 0x9b234c: r0 = controller()
    //     0x9b234c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2350: LoadField: r1 = r0->field_77
    //     0x9b2350: ldur            w1, [x0, #0x77]
    // 0x9b2354: DecompressPointer r1
    //     0x9b2354: add             x1, x1, HEAP, lsl #32
    // 0x9b2358: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b2358: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b235c: r0 = toList()
    //     0x9b235c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b2360: mov             x3, x0
    // 0x9b2364: ldur            x2, [fp, #-0x10]
    // 0x9b2368: LoadField: r0 = r2->field_13
    //     0x9b2368: ldur            w0, [x2, #0x13]
    // 0x9b236c: DecompressPointer r0
    //     0x9b236c: add             x0, x0, HEAP, lsl #32
    // 0x9b2370: LoadField: r1 = r3->field_b
    //     0x9b2370: ldur            w1, [x3, #0xb]
    // 0x9b2374: r4 = LoadInt32Instr(r0)
    //     0x9b2374: sbfx            x4, x0, #1, #0x1f
    //     0x9b2378: tbz             w0, #0, #0x9b2380
    //     0x9b237c: ldur            x4, [x0, #7]
    // 0x9b2380: r0 = LoadInt32Instr(r1)
    //     0x9b2380: sbfx            x0, x1, #1, #0x1f
    // 0x9b2384: mov             x1, x4
    // 0x9b2388: cmp             x1, x0
    // 0x9b238c: b.hs            #0x9b2ad0
    // 0x9b2390: LoadField: r0 = r3->field_f
    //     0x9b2390: ldur            w0, [x3, #0xf]
    // 0x9b2394: DecompressPointer r0
    //     0x9b2394: add             x0, x0, HEAP, lsl #32
    // 0x9b2398: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b2398: add             x16, x0, x4, lsl #2
    //     0x9b239c: ldur            w1, [x16, #0xf]
    // 0x9b23a0: DecompressPointer r1
    //     0x9b23a0: add             x1, x1, HEAP, lsl #32
    // 0x9b23a4: cmp             w1, NULL
    // 0x9b23a8: b.ne            #0x9b23b4
    // 0x9b23ac: r0 = Null
    //     0x9b23ac: mov             x0, NULL
    // 0x9b23b0: b               #0x9b23d4
    // 0x9b23b4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b23b4: ldur            w0, [x1, #0x17]
    // 0x9b23b8: DecompressPointer r0
    //     0x9b23b8: add             x0, x0, HEAP, lsl #32
    // 0x9b23bc: cmp             w0, NULL
    // 0x9b23c0: b.ne            #0x9b23cc
    // 0x9b23c4: r0 = Null
    //     0x9b23c4: mov             x0, NULL
    // 0x9b23c8: b               #0x9b23d4
    // 0x9b23cc: mov             x1, x0
    // 0x9b23d0: r0 = trim()
    //     0x9b23d0: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x9b23d4: cmp             w0, NULL
    // 0x9b23d8: b.ne            #0x9b23e4
    // 0x9b23dc: r3 = ""
    //     0x9b23dc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b23e0: b               #0x9b23e8
    // 0x9b23e4: mov             x3, x0
    // 0x9b23e8: ldur            x2, [fp, #-0x10]
    // 0x9b23ec: ldur            x0, [fp, #-0x20]
    // 0x9b23f0: stur            x3, [fp, #-0x18]
    // 0x9b23f4: LoadField: r1 = r2->field_f
    //     0x9b23f4: ldur            w1, [x2, #0xf]
    // 0x9b23f8: DecompressPointer r1
    //     0x9b23f8: add             x1, x1, HEAP, lsl #32
    // 0x9b23fc: r0 = of()
    //     0x9b23fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b2400: LoadField: r1 = r0->field_87
    //     0x9b2400: ldur            w1, [x0, #0x87]
    // 0x9b2404: DecompressPointer r1
    //     0x9b2404: add             x1, x1, HEAP, lsl #32
    // 0x9b2408: LoadField: r0 = r1->field_2b
    //     0x9b2408: ldur            w0, [x1, #0x2b]
    // 0x9b240c: DecompressPointer r0
    //     0x9b240c: add             x0, x0, HEAP, lsl #32
    // 0x9b2410: LoadField: r1 = r0->field_13
    //     0x9b2410: ldur            w1, [x0, #0x13]
    // 0x9b2414: DecompressPointer r1
    //     0x9b2414: add             x1, x1, HEAP, lsl #32
    // 0x9b2418: r16 = Instance_Color
    //     0x9b2418: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b241c: stp             x16, x1, [SP]
    // 0x9b2420: r1 = Instance_TextStyle
    //     0x9b2420: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0x9b2424: ldr             x1, [x1, #0x9b0]
    // 0x9b2428: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0x9b2428: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0x9b242c: ldr             x4, [x4, #0x9b8]
    // 0x9b2430: r0 = copyWith()
    //     0x9b2430: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b2434: ldur            x2, [fp, #-0x10]
    // 0x9b2438: stur            x0, [fp, #-0x28]
    // 0x9b243c: LoadField: r1 = r2->field_f
    //     0x9b243c: ldur            w1, [x2, #0xf]
    // 0x9b2440: DecompressPointer r1
    //     0x9b2440: add             x1, x1, HEAP, lsl #32
    // 0x9b2444: r0 = of()
    //     0x9b2444: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b2448: LoadField: r1 = r0->field_87
    //     0x9b2448: ldur            w1, [x0, #0x87]
    // 0x9b244c: DecompressPointer r1
    //     0x9b244c: add             x1, x1, HEAP, lsl #32
    // 0x9b2450: LoadField: r0 = r1->field_7
    //     0x9b2450: ldur            w0, [x1, #7]
    // 0x9b2454: DecompressPointer r0
    //     0x9b2454: add             x0, x0, HEAP, lsl #32
    // 0x9b2458: r16 = Instance_Color
    //     0x9b2458: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b245c: r30 = 12.000000
    //     0x9b245c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9b2460: ldr             lr, [lr, #0x9e8]
    // 0x9b2464: stp             lr, x16, [SP]
    // 0x9b2468: mov             x1, x0
    // 0x9b246c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9b246c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9b2470: ldr             x4, [x4, #0x9b8]
    // 0x9b2474: r0 = copyWith()
    //     0x9b2474: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b2478: ldur            x2, [fp, #-0x10]
    // 0x9b247c: stur            x0, [fp, #-0x30]
    // 0x9b2480: LoadField: r1 = r2->field_f
    //     0x9b2480: ldur            w1, [x2, #0xf]
    // 0x9b2484: DecompressPointer r1
    //     0x9b2484: add             x1, x1, HEAP, lsl #32
    // 0x9b2488: r0 = of()
    //     0x9b2488: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b248c: LoadField: r1 = r0->field_87
    //     0x9b248c: ldur            w1, [x0, #0x87]
    // 0x9b2490: DecompressPointer r1
    //     0x9b2490: add             x1, x1, HEAP, lsl #32
    // 0x9b2494: LoadField: r0 = r1->field_7
    //     0x9b2494: ldur            w0, [x1, #7]
    // 0x9b2498: DecompressPointer r0
    //     0x9b2498: add             x0, x0, HEAP, lsl #32
    // 0x9b249c: r16 = Instance_Color
    //     0x9b249c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b24a0: r30 = 12.000000
    //     0x9b24a0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9b24a4: ldr             lr, [lr, #0x9e8]
    // 0x9b24a8: stp             lr, x16, [SP]
    // 0x9b24ac: mov             x1, x0
    // 0x9b24b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9b24b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9b24b4: ldr             x4, [x4, #0x9b8]
    // 0x9b24b8: r0 = copyWith()
    //     0x9b24b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b24bc: stur            x0, [fp, #-0x38]
    // 0x9b24c0: r0 = ReadMoreText()
    //     0x9b24c0: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0x9b24c4: mov             x1, x0
    // 0x9b24c8: ldur            x0, [fp, #-0x18]
    // 0x9b24cc: stur            x1, [fp, #-0x40]
    // 0x9b24d0: StoreField: r1->field_3f = r0
    //     0x9b24d0: stur            w0, [x1, #0x3f]
    // 0x9b24d4: r0 = " Read Less"
    //     0x9b24d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0x9b24d8: ldr             x0, [x0, #0x9c0]
    // 0x9b24dc: StoreField: r1->field_43 = r0
    //     0x9b24dc: stur            w0, [x1, #0x43]
    // 0x9b24e0: r0 = "Read More"
    //     0x9b24e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0x9b24e4: ldr             x0, [x0, #0x9c8]
    // 0x9b24e8: StoreField: r1->field_47 = r0
    //     0x9b24e8: stur            w0, [x1, #0x47]
    // 0x9b24ec: r0 = 240
    //     0x9b24ec: movz            x0, #0xf0
    // 0x9b24f0: StoreField: r1->field_f = r0
    //     0x9b24f0: stur            x0, [x1, #0xf]
    // 0x9b24f4: r0 = 2
    //     0x9b24f4: movz            x0, #0x2
    // 0x9b24f8: ArrayStore: r1[0] = r0  ; List_8
    //     0x9b24f8: stur            x0, [x1, #0x17]
    // 0x9b24fc: r0 = Instance_TrimMode
    //     0x9b24fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0x9b2500: ldr             x0, [x0, #0x9d0]
    // 0x9b2504: StoreField: r1->field_1f = r0
    //     0x9b2504: stur            w0, [x1, #0x1f]
    // 0x9b2508: ldur            x0, [fp, #-0x28]
    // 0x9b250c: StoreField: r1->field_4f = r0
    //     0x9b250c: stur            w0, [x1, #0x4f]
    // 0x9b2510: r0 = Instance_TextAlign
    //     0x9b2510: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0x9b2514: StoreField: r1->field_53 = r0
    //     0x9b2514: stur            w0, [x1, #0x53]
    // 0x9b2518: ldur            x0, [fp, #-0x30]
    // 0x9b251c: StoreField: r1->field_23 = r0
    //     0x9b251c: stur            w0, [x1, #0x23]
    // 0x9b2520: ldur            x0, [fp, #-0x38]
    // 0x9b2524: StoreField: r1->field_27 = r0
    //     0x9b2524: stur            w0, [x1, #0x27]
    // 0x9b2528: r0 = "… "
    //     0x9b2528: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0x9b252c: ldr             x0, [x0, #0x9d8]
    // 0x9b2530: StoreField: r1->field_3b = r0
    //     0x9b2530: stur            w0, [x1, #0x3b]
    // 0x9b2534: r0 = true
    //     0x9b2534: add             x0, NULL, #0x20  ; true
    // 0x9b2538: StoreField: r1->field_37 = r0
    //     0x9b2538: stur            w0, [x1, #0x37]
    // 0x9b253c: r0 = Padding()
    //     0x9b253c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9b2540: mov             x2, x0
    // 0x9b2544: r0 = Instance_EdgeInsets
    //     0x9b2544: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0x9b2548: ldr             x0, [x0, #0x9e0]
    // 0x9b254c: stur            x2, [fp, #-0x18]
    // 0x9b2550: StoreField: r2->field_f = r0
    //     0x9b2550: stur            w0, [x2, #0xf]
    // 0x9b2554: ldur            x0, [fp, #-0x40]
    // 0x9b2558: StoreField: r2->field_b = r0
    //     0x9b2558: stur            w0, [x2, #0xb]
    // 0x9b255c: ldur            x0, [fp, #-0x20]
    // 0x9b2560: LoadField: r1 = r0->field_b
    //     0x9b2560: ldur            w1, [x0, #0xb]
    // 0x9b2564: LoadField: r3 = r0->field_f
    //     0x9b2564: ldur            w3, [x0, #0xf]
    // 0x9b2568: DecompressPointer r3
    //     0x9b2568: add             x3, x3, HEAP, lsl #32
    // 0x9b256c: LoadField: r4 = r3->field_b
    //     0x9b256c: ldur            w4, [x3, #0xb]
    // 0x9b2570: r3 = LoadInt32Instr(r1)
    //     0x9b2570: sbfx            x3, x1, #1, #0x1f
    // 0x9b2574: stur            x3, [fp, #-0x48]
    // 0x9b2578: r1 = LoadInt32Instr(r4)
    //     0x9b2578: sbfx            x1, x4, #1, #0x1f
    // 0x9b257c: cmp             x3, x1
    // 0x9b2580: b.ne            #0x9b258c
    // 0x9b2584: mov             x1, x0
    // 0x9b2588: r0 = _growToNextCapacity()
    //     0x9b2588: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b258c: ldur            x2, [fp, #-0x20]
    // 0x9b2590: ldur            x3, [fp, #-0x48]
    // 0x9b2594: add             x0, x3, #1
    // 0x9b2598: lsl             x1, x0, #1
    // 0x9b259c: StoreField: r2->field_b = r1
    //     0x9b259c: stur            w1, [x2, #0xb]
    // 0x9b25a0: LoadField: r1 = r2->field_f
    //     0x9b25a0: ldur            w1, [x2, #0xf]
    // 0x9b25a4: DecompressPointer r1
    //     0x9b25a4: add             x1, x1, HEAP, lsl #32
    // 0x9b25a8: ldur            x0, [fp, #-0x18]
    // 0x9b25ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b25ac: add             x25, x1, x3, lsl #2
    //     0x9b25b0: add             x25, x25, #0xf
    //     0x9b25b4: str             w0, [x25]
    //     0x9b25b8: tbz             w0, #0, #0x9b25d4
    //     0x9b25bc: ldurb           w16, [x1, #-1]
    //     0x9b25c0: ldurb           w17, [x0, #-1]
    //     0x9b25c4: and             x16, x17, x16, lsr #2
    //     0x9b25c8: tst             x16, HEAP, lsr #32
    //     0x9b25cc: b.eq            #0x9b25d4
    //     0x9b25d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b25d4: b               #0x9b25dc
    // 0x9b25d8: ldur            x2, [fp, #-0x20]
    // 0x9b25dc: ldur            x3, [fp, #-8]
    // 0x9b25e0: ldur            x0, [fp, #-0x10]
    // 0x9b25e4: LoadField: r1 = r3->field_f
    //     0x9b25e4: ldur            w1, [x3, #0xf]
    // 0x9b25e8: DecompressPointer r1
    //     0x9b25e8: add             x1, x1, HEAP, lsl #32
    // 0x9b25ec: r0 = controller()
    //     0x9b25ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b25f0: LoadField: r1 = r0->field_77
    //     0x9b25f0: ldur            w1, [x0, #0x77]
    // 0x9b25f4: DecompressPointer r1
    //     0x9b25f4: add             x1, x1, HEAP, lsl #32
    // 0x9b25f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b25f8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b25fc: r0 = toList()
    //     0x9b25fc: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b2600: mov             x3, x0
    // 0x9b2604: ldur            x2, [fp, #-0x10]
    // 0x9b2608: LoadField: r0 = r2->field_13
    //     0x9b2608: ldur            w0, [x2, #0x13]
    // 0x9b260c: DecompressPointer r0
    //     0x9b260c: add             x0, x0, HEAP, lsl #32
    // 0x9b2610: LoadField: r1 = r3->field_b
    //     0x9b2610: ldur            w1, [x3, #0xb]
    // 0x9b2614: r4 = LoadInt32Instr(r0)
    //     0x9b2614: sbfx            x4, x0, #1, #0x1f
    //     0x9b2618: tbz             w0, #0, #0x9b2620
    //     0x9b261c: ldur            x4, [x0, #7]
    // 0x9b2620: r0 = LoadInt32Instr(r1)
    //     0x9b2620: sbfx            x0, x1, #1, #0x1f
    // 0x9b2624: mov             x1, x4
    // 0x9b2628: cmp             x1, x0
    // 0x9b262c: b.hs            #0x9b2ad4
    // 0x9b2630: LoadField: r0 = r3->field_f
    //     0x9b2630: ldur            w0, [x3, #0xf]
    // 0x9b2634: DecompressPointer r0
    //     0x9b2634: add             x0, x0, HEAP, lsl #32
    // 0x9b2638: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b2638: add             x16, x0, x4, lsl #2
    //     0x9b263c: ldur            w1, [x16, #0xf]
    // 0x9b2640: DecompressPointer r1
    //     0x9b2640: add             x1, x1, HEAP, lsl #32
    // 0x9b2644: cmp             w1, NULL
    // 0x9b2648: b.ne            #0x9b2654
    // 0x9b264c: r1 = Null
    //     0x9b264c: mov             x1, NULL
    // 0x9b2650: b               #0x9b2660
    // 0x9b2654: LoadField: r0 = r1->field_1b
    //     0x9b2654: ldur            w0, [x1, #0x1b]
    // 0x9b2658: DecompressPointer r0
    //     0x9b2658: add             x0, x0, HEAP, lsl #32
    // 0x9b265c: LoadField: r1 = r0->field_b
    //     0x9b265c: ldur            w1, [x0, #0xb]
    // 0x9b2660: ldur            x0, [fp, #-8]
    // 0x9b2664: cmp             w1, NULL
    // 0x9b2668: b.eq            #0x9b2ad8
    // 0x9b266c: r3 = LoadInt32Instr(r1)
    //     0x9b266c: sbfx            x3, x1, #1, #0x1f
    // 0x9b2670: cmp             x3, #1
    // 0x9b2674: r16 = true
    //     0x9b2674: add             x16, NULL, #0x20  ; true
    // 0x9b2678: r17 = false
    //     0x9b2678: add             x17, NULL, #0x30  ; false
    // 0x9b267c: csel            x4, x16, x17, ge
    // 0x9b2680: stur            x4, [fp, #-0x18]
    // 0x9b2684: LoadField: r1 = r0->field_f
    //     0x9b2684: ldur            w1, [x0, #0xf]
    // 0x9b2688: DecompressPointer r1
    //     0x9b2688: add             x1, x1, HEAP, lsl #32
    // 0x9b268c: r0 = controller()
    //     0x9b268c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2690: LoadField: r1 = r0->field_77
    //     0x9b2690: ldur            w1, [x0, #0x77]
    // 0x9b2694: DecompressPointer r1
    //     0x9b2694: add             x1, x1, HEAP, lsl #32
    // 0x9b2698: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b2698: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b269c: r0 = toList()
    //     0x9b269c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b26a0: mov             x3, x0
    // 0x9b26a4: ldur            x2, [fp, #-0x10]
    // 0x9b26a8: LoadField: r0 = r2->field_13
    //     0x9b26a8: ldur            w0, [x2, #0x13]
    // 0x9b26ac: DecompressPointer r0
    //     0x9b26ac: add             x0, x0, HEAP, lsl #32
    // 0x9b26b0: LoadField: r1 = r3->field_b
    //     0x9b26b0: ldur            w1, [x3, #0xb]
    // 0x9b26b4: r4 = LoadInt32Instr(r0)
    //     0x9b26b4: sbfx            x4, x0, #1, #0x1f
    //     0x9b26b8: tbz             w0, #0, #0x9b26c0
    //     0x9b26bc: ldur            x4, [x0, #7]
    // 0x9b26c0: r0 = LoadInt32Instr(r1)
    //     0x9b26c0: sbfx            x0, x1, #1, #0x1f
    // 0x9b26c4: mov             x1, x4
    // 0x9b26c8: cmp             x1, x0
    // 0x9b26cc: b.hs            #0x9b2adc
    // 0x9b26d0: LoadField: r0 = r3->field_f
    //     0x9b26d0: ldur            w0, [x3, #0xf]
    // 0x9b26d4: DecompressPointer r0
    //     0x9b26d4: add             x0, x0, HEAP, lsl #32
    // 0x9b26d8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b26d8: add             x16, x0, x4, lsl #2
    //     0x9b26dc: ldur            w1, [x16, #0xf]
    // 0x9b26e0: DecompressPointer r1
    //     0x9b26e0: add             x1, x1, HEAP, lsl #32
    // 0x9b26e4: cmp             w1, NULL
    // 0x9b26e8: b.ne            #0x9b26f4
    // 0x9b26ec: r0 = Null
    //     0x9b26ec: mov             x0, NULL
    // 0x9b26f0: b               #0x9b2710
    // 0x9b26f4: LoadField: r0 = r1->field_1b
    //     0x9b26f4: ldur            w0, [x1, #0x1b]
    // 0x9b26f8: DecompressPointer r0
    //     0x9b26f8: add             x0, x0, HEAP, lsl #32
    // 0x9b26fc: LoadField: r1 = r0->field_b
    //     0x9b26fc: ldur            w1, [x0, #0xb]
    // 0x9b2700: cbz             w1, #0x9b270c
    // 0x9b2704: r0 = false
    //     0x9b2704: add             x0, NULL, #0x30  ; false
    // 0x9b2708: b               #0x9b2710
    // 0x9b270c: r0 = true
    //     0x9b270c: add             x0, NULL, #0x20  ; true
    // 0x9b2710: cmp             w0, NULL
    // 0x9b2714: b.eq            #0x9b2724
    // 0x9b2718: tbnz            w0, #4, #0x9b2724
    // 0x9b271c: d0 = 0.000000
    //     0x9b271c: eor             v0.16b, v0.16b, v0.16b
    // 0x9b2720: b               #0x9b2728
    // 0x9b2724: d0 = 48.000000
    //     0x9b2724: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0x9b2728: ldur            x0, [fp, #-8]
    // 0x9b272c: stur            d0, [fp, #-0x50]
    // 0x9b2730: LoadField: r1 = r0->field_f
    //     0x9b2730: ldur            w1, [x0, #0xf]
    // 0x9b2734: DecompressPointer r1
    //     0x9b2734: add             x1, x1, HEAP, lsl #32
    // 0x9b2738: r0 = controller()
    //     0x9b2738: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b273c: LoadField: r1 = r0->field_77
    //     0x9b273c: ldur            w1, [x0, #0x77]
    // 0x9b2740: DecompressPointer r1
    //     0x9b2740: add             x1, x1, HEAP, lsl #32
    // 0x9b2744: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b2744: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b2748: r0 = toList()
    //     0x9b2748: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b274c: mov             x2, x0
    // 0x9b2750: ldur            x3, [fp, #-0x10]
    // 0x9b2754: LoadField: r0 = r3->field_13
    //     0x9b2754: ldur            w0, [x3, #0x13]
    // 0x9b2758: DecompressPointer r0
    //     0x9b2758: add             x0, x0, HEAP, lsl #32
    // 0x9b275c: LoadField: r1 = r2->field_b
    //     0x9b275c: ldur            w1, [x2, #0xb]
    // 0x9b2760: r4 = LoadInt32Instr(r0)
    //     0x9b2760: sbfx            x4, x0, #1, #0x1f
    //     0x9b2764: tbz             w0, #0, #0x9b276c
    //     0x9b2768: ldur            x4, [x0, #7]
    // 0x9b276c: r0 = LoadInt32Instr(r1)
    //     0x9b276c: sbfx            x0, x1, #1, #0x1f
    // 0x9b2770: mov             x1, x4
    // 0x9b2774: cmp             x1, x0
    // 0x9b2778: b.hs            #0x9b2ae0
    // 0x9b277c: LoadField: r0 = r2->field_f
    //     0x9b277c: ldur            w0, [x2, #0xf]
    // 0x9b2780: DecompressPointer r0
    //     0x9b2780: add             x0, x0, HEAP, lsl #32
    // 0x9b2784: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9b2784: add             x16, x0, x4, lsl #2
    //     0x9b2788: ldur            w1, [x16, #0xf]
    // 0x9b278c: DecompressPointer r1
    //     0x9b278c: add             x1, x1, HEAP, lsl #32
    // 0x9b2790: cmp             w1, NULL
    // 0x9b2794: b.ne            #0x9b27a0
    // 0x9b2798: r0 = Null
    //     0x9b2798: mov             x0, NULL
    // 0x9b279c: b               #0x9b27b0
    // 0x9b27a0: LoadField: r0 = r1->field_1b
    //     0x9b27a0: ldur            w0, [x1, #0x1b]
    // 0x9b27a4: DecompressPointer r0
    //     0x9b27a4: add             x0, x0, HEAP, lsl #32
    // 0x9b27a8: LoadField: r1 = r0->field_b
    //     0x9b27a8: ldur            w1, [x0, #0xb]
    // 0x9b27ac: mov             x0, x1
    // 0x9b27b0: cmp             w0, NULL
    // 0x9b27b4: b.ne            #0x9b27c0
    // 0x9b27b8: r5 = 0
    //     0x9b27b8: movz            x5, #0
    // 0x9b27bc: b               #0x9b27c8
    // 0x9b27c0: r1 = LoadInt32Instr(r0)
    //     0x9b27c0: sbfx            x1, x0, #1, #0x1f
    // 0x9b27c4: mov             x5, x1
    // 0x9b27c8: ldur            x0, [fp, #-0x18]
    // 0x9b27cc: ldur            d0, [fp, #-0x50]
    // 0x9b27d0: ldur            x4, [fp, #-0x20]
    // 0x9b27d4: stur            x5, [fp, #-0x48]
    // 0x9b27d8: r1 = Function '<anonymous closure>':.
    //     0x9b27d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9e8] AnonymousClosure: (0x9b3480), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b27dc: ldr             x1, [x1, #0x9e8]
    // 0x9b27e0: r2 = Null
    //     0x9b27e0: mov             x2, NULL
    // 0x9b27e4: r0 = AllocateClosure()
    //     0x9b27e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b27e8: ldur            x2, [fp, #-0x10]
    // 0x9b27ec: r1 = Function '<anonymous closure>':.
    //     0x9b27ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9f0] AnonymousClosure: (0x9b1124), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b27f0: ldr             x1, [x1, #0x9f0]
    // 0x9b27f4: stur            x0, [fp, #-8]
    // 0x9b27f8: r0 = AllocateClosure()
    //     0x9b27f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b27fc: stur            x0, [fp, #-0x28]
    // 0x9b2800: r0 = ListView()
    //     0x9b2800: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x9b2804: stur            x0, [fp, #-0x30]
    // 0x9b2808: r16 = true
    //     0x9b2808: add             x16, NULL, #0x20  ; true
    // 0x9b280c: r30 = Instance_Axis
    //     0x9b280c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b2810: stp             lr, x16, [SP]
    // 0x9b2814: mov             x1, x0
    // 0x9b2818: ldur            x2, [fp, #-0x28]
    // 0x9b281c: ldur            x3, [fp, #-0x48]
    // 0x9b2820: ldur            x5, [fp, #-8]
    // 0x9b2824: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0x9b2824: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0x9b2828: ldr             x4, [x4, #0x8e8]
    // 0x9b282c: r0 = ListView.separated()
    //     0x9b282c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x9b2830: r0 = SizedBox()
    //     0x9b2830: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9b2834: mov             x1, x0
    // 0x9b2838: r0 = inf
    //     0x9b2838: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x9b283c: ldr             x0, [x0, #0x9f8]
    // 0x9b2840: stur            x1, [fp, #-8]
    // 0x9b2844: StoreField: r1->field_f = r0
    //     0x9b2844: stur            w0, [x1, #0xf]
    // 0x9b2848: ldur            d0, [fp, #-0x50]
    // 0x9b284c: r0 = inline_Allocate_Double()
    //     0x9b284c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x9b2850: add             x0, x0, #0x10
    //     0x9b2854: cmp             x2, x0
    //     0x9b2858: b.ls            #0x9b2ae4
    //     0x9b285c: str             x0, [THR, #0x50]  ; THR::top
    //     0x9b2860: sub             x0, x0, #0xf
    //     0x9b2864: movz            x2, #0xe15c
    //     0x9b2868: movk            x2, #0x3, lsl #16
    //     0x9b286c: stur            x2, [x0, #-1]
    // 0x9b2870: StoreField: r0->field_7 = d0
    //     0x9b2870: stur            d0, [x0, #7]
    // 0x9b2874: StoreField: r1->field_13 = r0
    //     0x9b2874: stur            w0, [x1, #0x13]
    // 0x9b2878: ldur            x0, [fp, #-0x30]
    // 0x9b287c: StoreField: r1->field_b = r0
    //     0x9b287c: stur            w0, [x1, #0xb]
    // 0x9b2880: r0 = Padding()
    //     0x9b2880: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9b2884: mov             x1, x0
    // 0x9b2888: r0 = Instance_EdgeInsets
    //     0x9b2888: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x9b288c: ldr             x0, [x0, #0xa00]
    // 0x9b2890: stur            x1, [fp, #-0x28]
    // 0x9b2894: StoreField: r1->field_f = r0
    //     0x9b2894: stur            w0, [x1, #0xf]
    // 0x9b2898: ldur            x0, [fp, #-8]
    // 0x9b289c: StoreField: r1->field_b = r0
    //     0x9b289c: stur            w0, [x1, #0xb]
    // 0x9b28a0: r0 = Visibility()
    //     0x9b28a0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x9b28a4: mov             x1, x0
    // 0x9b28a8: ldur            x0, [fp, #-0x28]
    // 0x9b28ac: stur            x1, [fp, #-8]
    // 0x9b28b0: StoreField: r1->field_b = r0
    //     0x9b28b0: stur            w0, [x1, #0xb]
    // 0x9b28b4: r0 = Instance_SizedBox
    //     0x9b28b4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x9b28b8: StoreField: r1->field_f = r0
    //     0x9b28b8: stur            w0, [x1, #0xf]
    // 0x9b28bc: ldur            x0, [fp, #-0x18]
    // 0x9b28c0: StoreField: r1->field_13 = r0
    //     0x9b28c0: stur            w0, [x1, #0x13]
    // 0x9b28c4: r0 = false
    //     0x9b28c4: add             x0, NULL, #0x30  ; false
    // 0x9b28c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x9b28c8: stur            w0, [x1, #0x17]
    // 0x9b28cc: StoreField: r1->field_1b = r0
    //     0x9b28cc: stur            w0, [x1, #0x1b]
    // 0x9b28d0: StoreField: r1->field_1f = r0
    //     0x9b28d0: stur            w0, [x1, #0x1f]
    // 0x9b28d4: StoreField: r1->field_23 = r0
    //     0x9b28d4: stur            w0, [x1, #0x23]
    // 0x9b28d8: StoreField: r1->field_27 = r0
    //     0x9b28d8: stur            w0, [x1, #0x27]
    // 0x9b28dc: StoreField: r1->field_2b = r0
    //     0x9b28dc: stur            w0, [x1, #0x2b]
    // 0x9b28e0: r0 = GestureDetector()
    //     0x9b28e0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x9b28e4: ldur            x2, [fp, #-0x10]
    // 0x9b28e8: r1 = Function '<anonymous closure>':.
    //     0x9b28e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa08] AnonymousClosure: (0x9b3408), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b28ec: ldr             x1, [x1, #0xa08]
    // 0x9b28f0: stur            x0, [fp, #-0x18]
    // 0x9b28f4: r0 = AllocateClosure()
    //     0x9b28f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b28f8: ldur            x2, [fp, #-0x10]
    // 0x9b28fc: r1 = Function '<anonymous closure>':.
    //     0x9b28fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa10] AnonymousClosure: (0x9b2afc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b2900: ldr             x1, [x1, #0xa10]
    // 0x9b2904: stur            x0, [fp, #-0x10]
    // 0x9b2908: r0 = AllocateClosure()
    //     0x9b2908: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b290c: ldur            x16, [fp, #-0x10]
    // 0x9b2910: stp             x0, x16, [SP, #8]
    // 0x9b2914: r16 = Instance_Icon
    //     0x9b2914: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa18] Obj!Icon@d65e71
    //     0x9b2918: ldr             x16, [x16, #0xa18]
    // 0x9b291c: str             x16, [SP]
    // 0x9b2920: ldur            x1, [fp, #-0x18]
    // 0x9b2924: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0x9b2924: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0x9b2928: ldr             x4, [x4, #0xa20]
    // 0x9b292c: r0 = GestureDetector()
    //     0x9b292c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x9b2930: r0 = Align()
    //     0x9b2930: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9b2934: mov             x3, x0
    // 0x9b2938: r0 = Instance_Alignment
    //     0x9b2938: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x9b293c: ldr             x0, [x0, #0xa28]
    // 0x9b2940: stur            x3, [fp, #-0x10]
    // 0x9b2944: StoreField: r3->field_f = r0
    //     0x9b2944: stur            w0, [x3, #0xf]
    // 0x9b2948: ldur            x1, [fp, #-0x18]
    // 0x9b294c: StoreField: r3->field_b = r1
    //     0x9b294c: stur            w1, [x3, #0xb]
    // 0x9b2950: r1 = Null
    //     0x9b2950: mov             x1, NULL
    // 0x9b2954: r2 = 4
    //     0x9b2954: movz            x2, #0x4
    // 0x9b2958: r0 = AllocateArray()
    //     0x9b2958: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b295c: mov             x2, x0
    // 0x9b2960: ldur            x0, [fp, #-8]
    // 0x9b2964: stur            x2, [fp, #-0x18]
    // 0x9b2968: StoreField: r2->field_f = r0
    //     0x9b2968: stur            w0, [x2, #0xf]
    // 0x9b296c: ldur            x0, [fp, #-0x10]
    // 0x9b2970: StoreField: r2->field_13 = r0
    //     0x9b2970: stur            w0, [x2, #0x13]
    // 0x9b2974: r1 = <Widget>
    //     0x9b2974: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b2978: r0 = AllocateGrowableArray()
    //     0x9b2978: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b297c: mov             x1, x0
    // 0x9b2980: ldur            x0, [fp, #-0x18]
    // 0x9b2984: stur            x1, [fp, #-8]
    // 0x9b2988: StoreField: r1->field_f = r0
    //     0x9b2988: stur            w0, [x1, #0xf]
    // 0x9b298c: r0 = 4
    //     0x9b298c: movz            x0, #0x4
    // 0x9b2990: StoreField: r1->field_b = r0
    //     0x9b2990: stur            w0, [x1, #0xb]
    // 0x9b2994: r0 = Stack()
    //     0x9b2994: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x9b2998: mov             x2, x0
    // 0x9b299c: r0 = Instance_Alignment
    //     0x9b299c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x9b29a0: ldr             x0, [x0, #0xa28]
    // 0x9b29a4: stur            x2, [fp, #-0x10]
    // 0x9b29a8: StoreField: r2->field_f = r0
    //     0x9b29a8: stur            w0, [x2, #0xf]
    // 0x9b29ac: r0 = Instance_StackFit
    //     0x9b29ac: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x9b29b0: ldr             x0, [x0, #0xfa8]
    // 0x9b29b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x9b29b4: stur            w0, [x2, #0x17]
    // 0x9b29b8: r0 = Instance_Clip
    //     0x9b29b8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x9b29bc: ldr             x0, [x0, #0x7e0]
    // 0x9b29c0: StoreField: r2->field_1b = r0
    //     0x9b29c0: stur            w0, [x2, #0x1b]
    // 0x9b29c4: ldur            x0, [fp, #-8]
    // 0x9b29c8: StoreField: r2->field_b = r0
    //     0x9b29c8: stur            w0, [x2, #0xb]
    // 0x9b29cc: ldur            x0, [fp, #-0x20]
    // 0x9b29d0: LoadField: r1 = r0->field_b
    //     0x9b29d0: ldur            w1, [x0, #0xb]
    // 0x9b29d4: LoadField: r3 = r0->field_f
    //     0x9b29d4: ldur            w3, [x0, #0xf]
    // 0x9b29d8: DecompressPointer r3
    //     0x9b29d8: add             x3, x3, HEAP, lsl #32
    // 0x9b29dc: LoadField: r4 = r3->field_b
    //     0x9b29dc: ldur            w4, [x3, #0xb]
    // 0x9b29e0: r3 = LoadInt32Instr(r1)
    //     0x9b29e0: sbfx            x3, x1, #1, #0x1f
    // 0x9b29e4: stur            x3, [fp, #-0x48]
    // 0x9b29e8: r1 = LoadInt32Instr(r4)
    //     0x9b29e8: sbfx            x1, x4, #1, #0x1f
    // 0x9b29ec: cmp             x3, x1
    // 0x9b29f0: b.ne            #0x9b29fc
    // 0x9b29f4: mov             x1, x0
    // 0x9b29f8: r0 = _growToNextCapacity()
    //     0x9b29f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b29fc: ldur            x2, [fp, #-0x20]
    // 0x9b2a00: ldur            x3, [fp, #-0x48]
    // 0x9b2a04: add             x0, x3, #1
    // 0x9b2a08: lsl             x1, x0, #1
    // 0x9b2a0c: StoreField: r2->field_b = r1
    //     0x9b2a0c: stur            w1, [x2, #0xb]
    // 0x9b2a10: LoadField: r1 = r2->field_f
    //     0x9b2a10: ldur            w1, [x2, #0xf]
    // 0x9b2a14: DecompressPointer r1
    //     0x9b2a14: add             x1, x1, HEAP, lsl #32
    // 0x9b2a18: ldur            x0, [fp, #-0x10]
    // 0x9b2a1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b2a1c: add             x25, x1, x3, lsl #2
    //     0x9b2a20: add             x25, x25, #0xf
    //     0x9b2a24: str             w0, [x25]
    //     0x9b2a28: tbz             w0, #0, #0x9b2a44
    //     0x9b2a2c: ldurb           w16, [x1, #-1]
    //     0x9b2a30: ldurb           w17, [x0, #-1]
    //     0x9b2a34: and             x16, x17, x16, lsr #2
    //     0x9b2a38: tst             x16, HEAP, lsr #32
    //     0x9b2a3c: b.eq            #0x9b2a44
    //     0x9b2a40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b2a44: r0 = Column()
    //     0x9b2a44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b2a48: r1 = Instance_Axis
    //     0x9b2a48: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b2a4c: StoreField: r0->field_f = r1
    //     0x9b2a4c: stur            w1, [x0, #0xf]
    // 0x9b2a50: r1 = Instance_MainAxisAlignment
    //     0x9b2a50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b2a54: ldr             x1, [x1, #0xa08]
    // 0x9b2a58: StoreField: r0->field_13 = r1
    //     0x9b2a58: stur            w1, [x0, #0x13]
    // 0x9b2a5c: r1 = Instance_MainAxisSize
    //     0x9b2a5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b2a60: ldr             x1, [x1, #0xa10]
    // 0x9b2a64: ArrayStore: r0[0] = r1  ; List_4
    //     0x9b2a64: stur            w1, [x0, #0x17]
    // 0x9b2a68: r1 = Instance_CrossAxisAlignment
    //     0x9b2a68: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9b2a6c: ldr             x1, [x1, #0x890]
    // 0x9b2a70: StoreField: r0->field_1b = r1
    //     0x9b2a70: stur            w1, [x0, #0x1b]
    // 0x9b2a74: r1 = Instance_VerticalDirection
    //     0x9b2a74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b2a78: ldr             x1, [x1, #0xa20]
    // 0x9b2a7c: StoreField: r0->field_23 = r1
    //     0x9b2a7c: stur            w1, [x0, #0x23]
    // 0x9b2a80: r1 = Instance_Clip
    //     0x9b2a80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b2a84: ldr             x1, [x1, #0x38]
    // 0x9b2a88: StoreField: r0->field_2b = r1
    //     0x9b2a88: stur            w1, [x0, #0x2b]
    // 0x9b2a8c: StoreField: r0->field_2f = rZR
    //     0x9b2a8c: stur            xzr, [x0, #0x2f]
    // 0x9b2a90: ldur            x1, [fp, #-0x20]
    // 0x9b2a94: StoreField: r0->field_b = r1
    //     0x9b2a94: stur            w1, [x0, #0xb]
    // 0x9b2a98: LeaveFrame
    //     0x9b2a98: mov             SP, fp
    //     0x9b2a9c: ldp             fp, lr, [SP], #0x10
    // 0x9b2aa0: ret
    //     0x9b2aa0: ret             
    // 0x9b2aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b2aa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b2aa8: b               #0x9b17e0
    // 0x9b2aac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2aac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ab0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ab0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ab4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ab4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ab8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ab8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2abc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2abc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ac0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ac0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ac4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ac4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ac8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ac8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2acc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2acc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ad0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ad0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ad4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ad4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b2ad8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b2adc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2adc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ae0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2ae0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b2ae4: SaveReg d0
    //     0x9b2ae4: str             q0, [SP, #-0x10]!
    // 0x9b2ae8: SaveReg r1
    //     0x9b2ae8: str             x1, [SP, #-8]!
    // 0x9b2aec: r0 = AllocateDouble()
    //     0x9b2aec: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9b2af0: RestoreReg r1
    //     0x9b2af0: ldr             x1, [SP], #8
    // 0x9b2af4: RestoreReg d0
    //     0x9b2af4: ldr             q0, [SP], #0x10
    // 0x9b2af8: b               #0x9b2870
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9b2afc, size: 0x178
    // 0x9b2afc: EnterFrame
    //     0x9b2afc: stp             fp, lr, [SP, #-0x10]!
    //     0x9b2b00: mov             fp, SP
    // 0x9b2b04: AllocStack(0x28)
    //     0x9b2b04: sub             SP, SP, #0x28
    // 0x9b2b08: SetupParameters()
    //     0x9b2b08: ldr             x0, [fp, #0x10]
    //     0x9b2b0c: ldur            w2, [x0, #0x17]
    //     0x9b2b10: add             x2, x2, HEAP, lsl #32
    //     0x9b2b14: stur            x2, [fp, #-0x10]
    // 0x9b2b18: CheckStackOverflow
    //     0x9b2b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b2b1c: cmp             SP, x16
    //     0x9b2b20: b.ls            #0x9b2c64
    // 0x9b2b24: LoadField: r0 = r2->field_b
    //     0x9b2b24: ldur            w0, [x2, #0xb]
    // 0x9b2b28: DecompressPointer r0
    //     0x9b2b28: add             x0, x0, HEAP, lsl #32
    // 0x9b2b2c: stur            x0, [fp, #-8]
    // 0x9b2b30: LoadField: r1 = r0->field_f
    //     0x9b2b30: ldur            w1, [x0, #0xf]
    // 0x9b2b34: DecompressPointer r1
    //     0x9b2b34: add             x1, x1, HEAP, lsl #32
    // 0x9b2b38: r0 = controller()
    //     0x9b2b38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2b3c: LoadField: r1 = r0->field_8f
    //     0x9b2b3c: ldur            w1, [x0, #0x8f]
    // 0x9b2b40: DecompressPointer r1
    //     0x9b2b40: add             x1, x1, HEAP, lsl #32
    // 0x9b2b44: cmp             w1, NULL
    // 0x9b2b48: b.eq            #0x9b2c54
    // 0x9b2b4c: ldur            x0, [fp, #-0x10]
    // 0x9b2b50: ldur            x2, [fp, #-8]
    // 0x9b2b54: LoadField: r1 = r2->field_f
    //     0x9b2b54: ldur            w1, [x2, #0xf]
    // 0x9b2b58: DecompressPointer r1
    //     0x9b2b58: add             x1, x1, HEAP, lsl #32
    // 0x9b2b5c: r0 = controller()
    //     0x9b2b5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2b60: mov             x1, x0
    // 0x9b2b64: r2 = "flag_dots"
    //     0x9b2b64: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fa30] "flag_dots"
    //     0x9b2b68: ldr             x2, [x2, #0xa30]
    // 0x9b2b6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9b2b6c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9b2b70: r0 = ratingReviewClickedEvent()
    //     0x9b2b70: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9b2b74: ldur            x0, [fp, #-8]
    // 0x9b2b78: LoadField: r2 = r0->field_f
    //     0x9b2b78: ldur            w2, [x0, #0xf]
    // 0x9b2b7c: DecompressPointer r2
    //     0x9b2b7c: add             x2, x2, HEAP, lsl #32
    // 0x9b2b80: ldur            x3, [fp, #-0x10]
    // 0x9b2b84: stur            x2, [fp, #-0x20]
    // 0x9b2b88: LoadField: r4 = r3->field_f
    //     0x9b2b88: ldur            w4, [x3, #0xf]
    // 0x9b2b8c: DecompressPointer r4
    //     0x9b2b8c: add             x4, x4, HEAP, lsl #32
    // 0x9b2b90: mov             x1, x2
    // 0x9b2b94: stur            x4, [fp, #-0x18]
    // 0x9b2b98: r0 = controller()
    //     0x9b2b98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2b9c: LoadField: r3 = r0->field_8f
    //     0x9b2b9c: ldur            w3, [x0, #0x8f]
    // 0x9b2ba0: DecompressPointer r3
    //     0x9b2ba0: add             x3, x3, HEAP, lsl #32
    // 0x9b2ba4: stur            x3, [fp, #-0x28]
    // 0x9b2ba8: cmp             w3, NULL
    // 0x9b2bac: b.eq            #0x9b2c6c
    // 0x9b2bb0: ldur            x0, [fp, #-8]
    // 0x9b2bb4: LoadField: r1 = r0->field_f
    //     0x9b2bb4: ldur            w1, [x0, #0xf]
    // 0x9b2bb8: DecompressPointer r1
    //     0x9b2bb8: add             x1, x1, HEAP, lsl #32
    // 0x9b2bbc: r0 = controller()
    //     0x9b2bbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2bc0: LoadField: r1 = r0->field_77
    //     0x9b2bc0: ldur            w1, [x0, #0x77]
    // 0x9b2bc4: DecompressPointer r1
    //     0x9b2bc4: add             x1, x1, HEAP, lsl #32
    // 0x9b2bc8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b2bc8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b2bcc: r0 = toList()
    //     0x9b2bcc: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b2bd0: mov             x2, x0
    // 0x9b2bd4: ldur            x0, [fp, #-0x10]
    // 0x9b2bd8: LoadField: r1 = r0->field_13
    //     0x9b2bd8: ldur            w1, [x0, #0x13]
    // 0x9b2bdc: DecompressPointer r1
    //     0x9b2bdc: add             x1, x1, HEAP, lsl #32
    // 0x9b2be0: LoadField: r0 = r2->field_b
    //     0x9b2be0: ldur            w0, [x2, #0xb]
    // 0x9b2be4: r3 = LoadInt32Instr(r1)
    //     0x9b2be4: sbfx            x3, x1, #1, #0x1f
    //     0x9b2be8: tbz             w1, #0, #0x9b2bf0
    //     0x9b2bec: ldur            x3, [x1, #7]
    // 0x9b2bf0: r1 = LoadInt32Instr(r0)
    //     0x9b2bf0: sbfx            x1, x0, #1, #0x1f
    // 0x9b2bf4: mov             x0, x1
    // 0x9b2bf8: mov             x1, x3
    // 0x9b2bfc: cmp             x1, x0
    // 0x9b2c00: b.hs            #0x9b2c70
    // 0x9b2c04: LoadField: r0 = r2->field_f
    //     0x9b2c04: ldur            w0, [x2, #0xf]
    // 0x9b2c08: DecompressPointer r0
    //     0x9b2c08: add             x0, x0, HEAP, lsl #32
    // 0x9b2c0c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x9b2c0c: add             x16, x0, x3, lsl #2
    //     0x9b2c10: ldur            w1, [x16, #0xf]
    // 0x9b2c14: DecompressPointer r1
    //     0x9b2c14: add             x1, x1, HEAP, lsl #32
    // 0x9b2c18: cmp             w1, NULL
    // 0x9b2c1c: b.ne            #0x9b2c28
    // 0x9b2c20: r0 = Null
    //     0x9b2c20: mov             x0, NULL
    // 0x9b2c24: b               #0x9b2c30
    // 0x9b2c28: LoadField: r0 = r1->field_b
    //     0x9b2c28: ldur            w0, [x1, #0xb]
    // 0x9b2c2c: DecompressPointer r0
    //     0x9b2c2c: add             x0, x0, HEAP, lsl #32
    // 0x9b2c30: cmp             w0, NULL
    // 0x9b2c34: b.ne            #0x9b2c40
    // 0x9b2c38: r5 = ""
    //     0x9b2c38: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b2c3c: b               #0x9b2c44
    // 0x9b2c40: mov             x5, x0
    // 0x9b2c44: ldur            x1, [fp, #-0x20]
    // 0x9b2c48: ldur            x2, [fp, #-0x18]
    // 0x9b2c4c: ldur            x3, [fp, #-0x28]
    // 0x9b2c50: r0 = showMenuItem()
    //     0x9b2c50: bl              #0x9b2c74  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem
    // 0x9b2c54: r0 = Null
    //     0x9b2c54: mov             x0, NULL
    // 0x9b2c58: LeaveFrame
    //     0x9b2c58: mov             SP, fp
    //     0x9b2c5c: ldp             fp, lr, [SP], #0x10
    // 0x9b2c60: ret
    //     0x9b2c60: ret             
    // 0x9b2c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b2c64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b2c68: b               #0x9b2b24
    // 0x9b2c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b2c6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b2c70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b2c70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0x9b2c74, size: 0x54c
    // 0x9b2c74: EnterFrame
    //     0x9b2c74: stp             fp, lr, [SP, #-0x10]!
    //     0x9b2c78: mov             fp, SP
    // 0x9b2c7c: AllocStack(0xa0)
    //     0x9b2c7c: sub             SP, SP, #0xa0
    // 0x9b2c80: SetupParameters(ReviewListWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x9b2c80: mov             x0, x1
    //     0x9b2c84: stur            x1, [fp, #-8]
    //     0x9b2c88: mov             x1, x2
    //     0x9b2c8c: stur            x2, [fp, #-0x10]
    //     0x9b2c90: stur            x3, [fp, #-0x18]
    //     0x9b2c94: stur            x5, [fp, #-0x20]
    // 0x9b2c98: CheckStackOverflow
    //     0x9b2c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b2c9c: cmp             SP, x16
    //     0x9b2ca0: b.ls            #0x9b31b8
    // 0x9b2ca4: r1 = 2
    //     0x9b2ca4: movz            x1, #0x2
    // 0x9b2ca8: r0 = AllocateContext()
    //     0x9b2ca8: bl              #0x16f6108  ; AllocateContextStub
    // 0x9b2cac: mov             x2, x0
    // 0x9b2cb0: ldur            x0, [fp, #-8]
    // 0x9b2cb4: stur            x2, [fp, #-0x28]
    // 0x9b2cb8: StoreField: r2->field_f = r0
    //     0x9b2cb8: stur            w0, [x2, #0xf]
    // 0x9b2cbc: ldur            x1, [fp, #-0x20]
    // 0x9b2cc0: StoreField: r2->field_13 = r1
    //     0x9b2cc0: stur            w1, [x2, #0x13]
    // 0x9b2cc4: mov             x1, x0
    // 0x9b2cc8: r0 = controller()
    //     0x9b2cc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2ccc: LoadField: r1 = r0->field_93
    //     0x9b2ccc: ldur            w1, [x0, #0x93]
    // 0x9b2cd0: DecompressPointer r1
    //     0x9b2cd0: add             x1, x1, HEAP, lsl #32
    // 0x9b2cd4: ldur            x0, [fp, #-0x28]
    // 0x9b2cd8: LoadField: r2 = r0->field_13
    //     0x9b2cd8: ldur            w2, [x0, #0x13]
    // 0x9b2cdc: DecompressPointer r2
    //     0x9b2cdc: add             x2, x2, HEAP, lsl #32
    // 0x9b2ce0: r0 = []()
    //     0x9b2ce0: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x9b2ce4: r1 = 60
    //     0x9b2ce4: movz            x1, #0x3c
    // 0x9b2ce8: branchIfSmi(r0, 0x9b2cf4)
    //     0x9b2ce8: tbz             w0, #0, #0x9b2cf4
    // 0x9b2cec: r1 = LoadClassIdInstr(r0)
    //     0x9b2cec: ldur            x1, [x0, #-1]
    //     0x9b2cf0: ubfx            x1, x1, #0xc, #0x14
    // 0x9b2cf4: r16 = true
    //     0x9b2cf4: add             x16, NULL, #0x20  ; true
    // 0x9b2cf8: stp             x16, x0, [SP]
    // 0x9b2cfc: mov             x0, x1
    // 0x9b2d00: mov             lr, x0
    // 0x9b2d04: ldr             lr, [x21, lr, lsl #3]
    // 0x9b2d08: blr             lr
    // 0x9b2d0c: tbnz            w0, #4, #0x9b2d18
    // 0x9b2d10: d0 = 100.000000
    //     0x9b2d10: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0x9b2d14: b               #0x9b2d20
    // 0x9b2d18: d0 = 120.000000
    //     0x9b2d18: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0x9b2d1c: ldr             d0, [x17, #0xa38]
    // 0x9b2d20: ldur            x0, [fp, #-0x18]
    // 0x9b2d24: ldur            x2, [fp, #-0x28]
    // 0x9b2d28: stur            d0, [fp, #-0x58]
    // 0x9b2d2c: r0 = BoxConstraints()
    //     0x9b2d2c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x9b2d30: stur            x0, [fp, #-0x20]
    // 0x9b2d34: StoreField: r0->field_7 = rZR
    //     0x9b2d34: stur            xzr, [x0, #7]
    // 0x9b2d38: ldur            d0, [fp, #-0x58]
    // 0x9b2d3c: StoreField: r0->field_f = d0
    //     0x9b2d3c: stur            d0, [x0, #0xf]
    // 0x9b2d40: ArrayStore: r0[0] = rZR  ; List_8
    //     0x9b2d40: stur            xzr, [x0, #0x17]
    // 0x9b2d44: d0 = inf
    //     0x9b2d44: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x9b2d48: StoreField: r0->field_1f = d0
    //     0x9b2d48: stur            d0, [x0, #0x1f]
    // 0x9b2d4c: ldur            x1, [fp, #-0x18]
    // 0x9b2d50: LoadField: d0 = r1->field_7
    //     0x9b2d50: ldur            d0, [x1, #7]
    // 0x9b2d54: stur            d0, [fp, #-0x70]
    // 0x9b2d58: LoadField: d1 = r1->field_f
    //     0x9b2d58: ldur            d1, [x1, #0xf]
    // 0x9b2d5c: stur            d1, [fp, #-0x68]
    // 0x9b2d60: d2 = 50.000000
    //     0x9b2d60: ldr             d2, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0x9b2d64: fsub            d3, d1, d2
    // 0x9b2d68: stur            d3, [fp, #-0x60]
    // 0x9b2d6c: fadd            d4, d0, d2
    // 0x9b2d70: stur            d4, [fp, #-0x58]
    // 0x9b2d74: r0 = RelativeRect()
    //     0x9b2d74: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0x9b2d78: ldur            d0, [fp, #-0x70]
    // 0x9b2d7c: stur            x0, [fp, #-0x18]
    // 0x9b2d80: StoreField: r0->field_7 = d0
    //     0x9b2d80: stur            d0, [x0, #7]
    // 0x9b2d84: ldur            d0, [fp, #-0x60]
    // 0x9b2d88: StoreField: r0->field_f = d0
    //     0x9b2d88: stur            d0, [x0, #0xf]
    // 0x9b2d8c: ldur            d0, [fp, #-0x58]
    // 0x9b2d90: ArrayStore: r0[0] = d0  ; List_8
    //     0x9b2d90: stur            d0, [x0, #0x17]
    // 0x9b2d94: ldur            d0, [fp, #-0x68]
    // 0x9b2d98: StoreField: r0->field_1f = d0
    //     0x9b2d98: stur            d0, [x0, #0x1f]
    // 0x9b2d9c: ldur            x1, [fp, #-8]
    // 0x9b2da0: r0 = controller()
    //     0x9b2da0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2da4: LoadField: r1 = r0->field_93
    //     0x9b2da4: ldur            w1, [x0, #0x93]
    // 0x9b2da8: DecompressPointer r1
    //     0x9b2da8: add             x1, x1, HEAP, lsl #32
    // 0x9b2dac: ldur            x0, [fp, #-0x28]
    // 0x9b2db0: LoadField: r2 = r0->field_13
    //     0x9b2db0: ldur            w2, [x0, #0x13]
    // 0x9b2db4: DecompressPointer r2
    //     0x9b2db4: add             x2, x2, HEAP, lsl #32
    // 0x9b2db8: r0 = []()
    //     0x9b2db8: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x9b2dbc: r1 = 60
    //     0x9b2dbc: movz            x1, #0x3c
    // 0x9b2dc0: branchIfSmi(r0, 0x9b2dcc)
    //     0x9b2dc0: tbz             w0, #0, #0x9b2dcc
    // 0x9b2dc4: r1 = LoadClassIdInstr(r0)
    //     0x9b2dc4: ldur            x1, [x0, #-1]
    //     0x9b2dc8: ubfx            x1, x1, #0xc, #0x14
    // 0x9b2dcc: r16 = true
    //     0x9b2dcc: add             x16, NULL, #0x20  ; true
    // 0x9b2dd0: stp             x16, x0, [SP]
    // 0x9b2dd4: mov             x0, x1
    // 0x9b2dd8: mov             lr, x0
    // 0x9b2ddc: ldr             lr, [x21, lr, lsl #3]
    // 0x9b2de0: blr             lr
    // 0x9b2de4: tbnz            w0, #4, #0x9b2df0
    // 0x9b2de8: r3 = Null
    //     0x9b2de8: mov             x3, NULL
    // 0x9b2dec: b               #0x9b2e04
    // 0x9b2df0: ldur            x2, [fp, #-0x28]
    // 0x9b2df4: r1 = Function '<anonymous closure>':.
    //     0x9b2df4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa40] AnonymousClosure: (0x9b3344), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem (0x9b2c74)
    //     0x9b2df8: ldr             x1, [x1, #0xa40]
    // 0x9b2dfc: r0 = AllocateClosure()
    //     0x9b2dfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b2e00: mov             x3, x0
    // 0x9b2e04: ldur            x0, [fp, #-0x28]
    // 0x9b2e08: stur            x3, [fp, #-0x30]
    // 0x9b2e0c: r1 = <Widget>
    //     0x9b2e0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b2e10: r2 = 0
    //     0x9b2e10: movz            x2, #0
    // 0x9b2e14: r0 = _GrowableList()
    //     0x9b2e14: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x9b2e18: ldur            x1, [fp, #-8]
    // 0x9b2e1c: stur            x0, [fp, #-0x38]
    // 0x9b2e20: r0 = controller()
    //     0x9b2e20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2e24: LoadField: r1 = r0->field_93
    //     0x9b2e24: ldur            w1, [x0, #0x93]
    // 0x9b2e28: DecompressPointer r1
    //     0x9b2e28: add             x1, x1, HEAP, lsl #32
    // 0x9b2e2c: ldur            x0, [fp, #-0x28]
    // 0x9b2e30: LoadField: r2 = r0->field_13
    //     0x9b2e30: ldur            w2, [x0, #0x13]
    // 0x9b2e34: DecompressPointer r2
    //     0x9b2e34: add             x2, x2, HEAP, lsl #32
    // 0x9b2e38: r0 = []()
    //     0x9b2e38: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x9b2e3c: r1 = 60
    //     0x9b2e3c: movz            x1, #0x3c
    // 0x9b2e40: branchIfSmi(r0, 0x9b2e4c)
    //     0x9b2e40: tbz             w0, #0, #0x9b2e4c
    // 0x9b2e44: r1 = LoadClassIdInstr(r0)
    //     0x9b2e44: ldur            x1, [x0, #-1]
    //     0x9b2e48: ubfx            x1, x1, #0xc, #0x14
    // 0x9b2e4c: r16 = true
    //     0x9b2e4c: add             x16, NULL, #0x20  ; true
    // 0x9b2e50: stp             x16, x0, [SP]
    // 0x9b2e54: mov             x0, x1
    // 0x9b2e58: mov             lr, x0
    // 0x9b2e5c: ldr             lr, [x21, lr, lsl #3]
    // 0x9b2e60: blr             lr
    // 0x9b2e64: tbnz            w0, #4, #0x9b2ec8
    // 0x9b2e68: ldur            x0, [fp, #-0x38]
    // 0x9b2e6c: LoadField: r1 = r0->field_b
    //     0x9b2e6c: ldur            w1, [x0, #0xb]
    // 0x9b2e70: LoadField: r2 = r0->field_f
    //     0x9b2e70: ldur            w2, [x0, #0xf]
    // 0x9b2e74: DecompressPointer r2
    //     0x9b2e74: add             x2, x2, HEAP, lsl #32
    // 0x9b2e78: LoadField: r3 = r2->field_b
    //     0x9b2e78: ldur            w3, [x2, #0xb]
    // 0x9b2e7c: r2 = LoadInt32Instr(r1)
    //     0x9b2e7c: sbfx            x2, x1, #1, #0x1f
    // 0x9b2e80: stur            x2, [fp, #-0x40]
    // 0x9b2e84: r1 = LoadInt32Instr(r3)
    //     0x9b2e84: sbfx            x1, x3, #1, #0x1f
    // 0x9b2e88: cmp             x2, x1
    // 0x9b2e8c: b.ne            #0x9b2e98
    // 0x9b2e90: mov             x1, x0
    // 0x9b2e94: r0 = _growToNextCapacity()
    //     0x9b2e94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b2e98: ldur            x0, [fp, #-0x38]
    // 0x9b2e9c: ldur            x1, [fp, #-0x40]
    // 0x9b2ea0: add             x2, x1, #1
    // 0x9b2ea4: lsl             x3, x2, #1
    // 0x9b2ea8: StoreField: r0->field_b = r3
    //     0x9b2ea8: stur            w3, [x0, #0xb]
    // 0x9b2eac: LoadField: r2 = r0->field_f
    //     0x9b2eac: ldur            w2, [x0, #0xf]
    // 0x9b2eb0: DecompressPointer r2
    //     0x9b2eb0: add             x2, x2, HEAP, lsl #32
    // 0x9b2eb4: add             x3, x2, x1, lsl #2
    // 0x9b2eb8: r16 = Instance_Icon
    //     0x9b2eb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0x9b2ebc: ldr             x16, [x16, #0xa48]
    // 0x9b2ec0: StoreField: r3->field_f = r16
    //     0x9b2ec0: stur            w16, [x3, #0xf]
    // 0x9b2ec4: b               #0x9b2ecc
    // 0x9b2ec8: ldur            x0, [fp, #-0x38]
    // 0x9b2ecc: LoadField: r1 = r0->field_b
    //     0x9b2ecc: ldur            w1, [x0, #0xb]
    // 0x9b2ed0: LoadField: r2 = r0->field_f
    //     0x9b2ed0: ldur            w2, [x0, #0xf]
    // 0x9b2ed4: DecompressPointer r2
    //     0x9b2ed4: add             x2, x2, HEAP, lsl #32
    // 0x9b2ed8: LoadField: r3 = r2->field_b
    //     0x9b2ed8: ldur            w3, [x2, #0xb]
    // 0x9b2edc: r2 = LoadInt32Instr(r1)
    //     0x9b2edc: sbfx            x2, x1, #1, #0x1f
    // 0x9b2ee0: stur            x2, [fp, #-0x40]
    // 0x9b2ee4: r1 = LoadInt32Instr(r3)
    //     0x9b2ee4: sbfx            x1, x3, #1, #0x1f
    // 0x9b2ee8: cmp             x2, x1
    // 0x9b2eec: b.ne            #0x9b2ef8
    // 0x9b2ef0: mov             x1, x0
    // 0x9b2ef4: r0 = _growToNextCapacity()
    //     0x9b2ef4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b2ef8: ldur            x2, [fp, #-0x28]
    // 0x9b2efc: ldur            x0, [fp, #-0x38]
    // 0x9b2f00: ldur            x1, [fp, #-0x40]
    // 0x9b2f04: add             x3, x1, #1
    // 0x9b2f08: lsl             x4, x3, #1
    // 0x9b2f0c: StoreField: r0->field_b = r4
    //     0x9b2f0c: stur            w4, [x0, #0xb]
    // 0x9b2f10: LoadField: r3 = r0->field_f
    //     0x9b2f10: ldur            w3, [x0, #0xf]
    // 0x9b2f14: DecompressPointer r3
    //     0x9b2f14: add             x3, x3, HEAP, lsl #32
    // 0x9b2f18: add             x4, x3, x1, lsl #2
    // 0x9b2f1c: r16 = Instance_SizedBox
    //     0x9b2f1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x9b2f20: ldr             x16, [x16, #0xa50]
    // 0x9b2f24: StoreField: r4->field_f = r16
    //     0x9b2f24: stur            w16, [x4, #0xf]
    // 0x9b2f28: ldur            x1, [fp, #-8]
    // 0x9b2f2c: r0 = controller()
    //     0x9b2f2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b2f30: LoadField: r1 = r0->field_93
    //     0x9b2f30: ldur            w1, [x0, #0x93]
    // 0x9b2f34: DecompressPointer r1
    //     0x9b2f34: add             x1, x1, HEAP, lsl #32
    // 0x9b2f38: ldur            x0, [fp, #-0x28]
    // 0x9b2f3c: LoadField: r2 = r0->field_13
    //     0x9b2f3c: ldur            w2, [x0, #0x13]
    // 0x9b2f40: DecompressPointer r2
    //     0x9b2f40: add             x2, x2, HEAP, lsl #32
    // 0x9b2f44: r0 = []()
    //     0x9b2f44: bl              #0x1653f0c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]
    // 0x9b2f48: r1 = 60
    //     0x9b2f48: movz            x1, #0x3c
    // 0x9b2f4c: branchIfSmi(r0, 0x9b2f58)
    //     0x9b2f4c: tbz             w0, #0, #0x9b2f58
    // 0x9b2f50: r1 = LoadClassIdInstr(r0)
    //     0x9b2f50: ldur            x1, [x0, #-1]
    //     0x9b2f54: ubfx            x1, x1, #0xc, #0x14
    // 0x9b2f58: r16 = true
    //     0x9b2f58: add             x16, NULL, #0x20  ; true
    // 0x9b2f5c: stp             x16, x0, [SP]
    // 0x9b2f60: mov             x0, x1
    // 0x9b2f64: mov             lr, x0
    // 0x9b2f68: ldr             lr, [x21, lr, lsl #3]
    // 0x9b2f6c: blr             lr
    // 0x9b2f70: tbnz            w0, #4, #0x9b2f80
    // 0x9b2f74: r0 = "Flagged"
    //     0x9b2f74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0x9b2f78: ldr             x0, [x0, #0xa58]
    // 0x9b2f7c: b               #0x9b2f88
    // 0x9b2f80: r0 = "Flag as abusive"
    //     0x9b2f80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0x9b2f84: ldr             x0, [x0, #0xa60]
    // 0x9b2f88: ldur            x1, [fp, #-0x10]
    // 0x9b2f8c: stur            x0, [fp, #-8]
    // 0x9b2f90: r0 = of()
    //     0x9b2f90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b2f94: LoadField: r1 = r0->field_87
    //     0x9b2f94: ldur            w1, [x0, #0x87]
    // 0x9b2f98: DecompressPointer r1
    //     0x9b2f98: add             x1, x1, HEAP, lsl #32
    // 0x9b2f9c: LoadField: r0 = r1->field_33
    //     0x9b2f9c: ldur            w0, [x1, #0x33]
    // 0x9b2fa0: DecompressPointer r0
    //     0x9b2fa0: add             x0, x0, HEAP, lsl #32
    // 0x9b2fa4: cmp             w0, NULL
    // 0x9b2fa8: b.ne            #0x9b2fb4
    // 0x9b2fac: r2 = Null
    //     0x9b2fac: mov             x2, NULL
    // 0x9b2fb0: b               #0x9b2fd8
    // 0x9b2fb4: r16 = 12.000000
    //     0x9b2fb4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9b2fb8: ldr             x16, [x16, #0x9e8]
    // 0x9b2fbc: r30 = Instance_Color
    //     0x9b2fbc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b2fc0: stp             lr, x16, [SP]
    // 0x9b2fc4: mov             x1, x0
    // 0x9b2fc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b2fc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b2fcc: ldr             x4, [x4, #0xaa0]
    // 0x9b2fd0: r0 = copyWith()
    //     0x9b2fd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b2fd4: mov             x2, x0
    // 0x9b2fd8: ldur            x1, [fp, #-0x38]
    // 0x9b2fdc: ldur            x0, [fp, #-8]
    // 0x9b2fe0: stur            x2, [fp, #-0x48]
    // 0x9b2fe4: r0 = Text()
    //     0x9b2fe4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b2fe8: mov             x2, x0
    // 0x9b2fec: ldur            x0, [fp, #-8]
    // 0x9b2ff0: stur            x2, [fp, #-0x50]
    // 0x9b2ff4: StoreField: r2->field_b = r0
    //     0x9b2ff4: stur            w0, [x2, #0xb]
    // 0x9b2ff8: ldur            x0, [fp, #-0x48]
    // 0x9b2ffc: StoreField: r2->field_13 = r0
    //     0x9b2ffc: stur            w0, [x2, #0x13]
    // 0x9b3000: ldur            x0, [fp, #-0x38]
    // 0x9b3004: LoadField: r1 = r0->field_b
    //     0x9b3004: ldur            w1, [x0, #0xb]
    // 0x9b3008: LoadField: r3 = r0->field_f
    //     0x9b3008: ldur            w3, [x0, #0xf]
    // 0x9b300c: DecompressPointer r3
    //     0x9b300c: add             x3, x3, HEAP, lsl #32
    // 0x9b3010: LoadField: r4 = r3->field_b
    //     0x9b3010: ldur            w4, [x3, #0xb]
    // 0x9b3014: r3 = LoadInt32Instr(r1)
    //     0x9b3014: sbfx            x3, x1, #1, #0x1f
    // 0x9b3018: stur            x3, [fp, #-0x40]
    // 0x9b301c: r1 = LoadInt32Instr(r4)
    //     0x9b301c: sbfx            x1, x4, #1, #0x1f
    // 0x9b3020: cmp             x3, x1
    // 0x9b3024: b.ne            #0x9b3030
    // 0x9b3028: mov             x1, x0
    // 0x9b302c: r0 = _growToNextCapacity()
    //     0x9b302c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b3030: ldur            x4, [fp, #-0x30]
    // 0x9b3034: ldur            x2, [fp, #-0x38]
    // 0x9b3038: ldur            x3, [fp, #-0x40]
    // 0x9b303c: add             x0, x3, #1
    // 0x9b3040: lsl             x1, x0, #1
    // 0x9b3044: StoreField: r2->field_b = r1
    //     0x9b3044: stur            w1, [x2, #0xb]
    // 0x9b3048: LoadField: r1 = r2->field_f
    //     0x9b3048: ldur            w1, [x2, #0xf]
    // 0x9b304c: DecompressPointer r1
    //     0x9b304c: add             x1, x1, HEAP, lsl #32
    // 0x9b3050: ldur            x0, [fp, #-0x50]
    // 0x9b3054: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b3054: add             x25, x1, x3, lsl #2
    //     0x9b3058: add             x25, x25, #0xf
    //     0x9b305c: str             w0, [x25]
    //     0x9b3060: tbz             w0, #0, #0x9b307c
    //     0x9b3064: ldurb           w16, [x1, #-1]
    //     0x9b3068: ldurb           w17, [x0, #-1]
    //     0x9b306c: and             x16, x17, x16, lsr #2
    //     0x9b3070: tst             x16, HEAP, lsr #32
    //     0x9b3074: b.eq            #0x9b307c
    //     0x9b3078: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b307c: r0 = Row()
    //     0x9b307c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b3080: mov             x2, x0
    // 0x9b3084: r0 = Instance_Axis
    //     0x9b3084: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b3088: stur            x2, [fp, #-8]
    // 0x9b308c: StoreField: r2->field_f = r0
    //     0x9b308c: stur            w0, [x2, #0xf]
    // 0x9b3090: r0 = Instance_MainAxisAlignment
    //     0x9b3090: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b3094: ldr             x0, [x0, #0xa08]
    // 0x9b3098: StoreField: r2->field_13 = r0
    //     0x9b3098: stur            w0, [x2, #0x13]
    // 0x9b309c: r0 = Instance_MainAxisSize
    //     0x9b309c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b30a0: ldr             x0, [x0, #0xa10]
    // 0x9b30a4: ArrayStore: r2[0] = r0  ; List_4
    //     0x9b30a4: stur            w0, [x2, #0x17]
    // 0x9b30a8: r0 = Instance_CrossAxisAlignment
    //     0x9b30a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b30ac: ldr             x0, [x0, #0xa18]
    // 0x9b30b0: StoreField: r2->field_1b = r0
    //     0x9b30b0: stur            w0, [x2, #0x1b]
    // 0x9b30b4: r0 = Instance_VerticalDirection
    //     0x9b30b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b30b8: ldr             x0, [x0, #0xa20]
    // 0x9b30bc: StoreField: r2->field_23 = r0
    //     0x9b30bc: stur            w0, [x2, #0x23]
    // 0x9b30c0: r0 = Instance_Clip
    //     0x9b30c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b30c4: ldr             x0, [x0, #0x38]
    // 0x9b30c8: StoreField: r2->field_2b = r0
    //     0x9b30c8: stur            w0, [x2, #0x2b]
    // 0x9b30cc: StoreField: r2->field_2f = rZR
    //     0x9b30cc: stur            xzr, [x2, #0x2f]
    // 0x9b30d0: ldur            x0, [fp, #-0x38]
    // 0x9b30d4: StoreField: r2->field_b = r0
    //     0x9b30d4: stur            w0, [x2, #0xb]
    // 0x9b30d8: r1 = <String>
    //     0x9b30d8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x9b30dc: r0 = PopupMenuItem()
    //     0x9b30dc: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0x9b30e0: mov             x3, x0
    // 0x9b30e4: r0 = "flag"
    //     0x9b30e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0x9b30e8: ldr             x0, [x0, #0xa68]
    // 0x9b30ec: stur            x3, [fp, #-0x38]
    // 0x9b30f0: StoreField: r3->field_f = r0
    //     0x9b30f0: stur            w0, [x3, #0xf]
    // 0x9b30f4: ldur            x0, [fp, #-0x30]
    // 0x9b30f8: StoreField: r3->field_13 = r0
    //     0x9b30f8: stur            w0, [x3, #0x13]
    // 0x9b30fc: r0 = true
    //     0x9b30fc: add             x0, NULL, #0x20  ; true
    // 0x9b3100: ArrayStore: r3[0] = r0  ; List_4
    //     0x9b3100: stur            w0, [x3, #0x17]
    // 0x9b3104: d0 = 25.000000
    //     0x9b3104: fmov            d0, #25.00000000
    // 0x9b3108: StoreField: r3->field_1b = d0
    //     0x9b3108: stur            d0, [x3, #0x1b]
    // 0x9b310c: ldur            x0, [fp, #-8]
    // 0x9b3110: StoreField: r3->field_33 = r0
    //     0x9b3110: stur            w0, [x3, #0x33]
    // 0x9b3114: r1 = Null
    //     0x9b3114: mov             x1, NULL
    // 0x9b3118: r2 = 2
    //     0x9b3118: movz            x2, #0x2
    // 0x9b311c: r0 = AllocateArray()
    //     0x9b311c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b3120: mov             x2, x0
    // 0x9b3124: ldur            x0, [fp, #-0x38]
    // 0x9b3128: stur            x2, [fp, #-8]
    // 0x9b312c: StoreField: r2->field_f = r0
    //     0x9b312c: stur            w0, [x2, #0xf]
    // 0x9b3130: r1 = <PopupMenuEntry<String>>
    //     0x9b3130: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0x9b3134: ldr             x1, [x1, #0xa70]
    // 0x9b3138: r0 = AllocateGrowableArray()
    //     0x9b3138: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b313c: mov             x1, x0
    // 0x9b3140: ldur            x0, [fp, #-8]
    // 0x9b3144: StoreField: r1->field_f = r0
    //     0x9b3144: stur            w0, [x1, #0xf]
    // 0x9b3148: r0 = 2
    //     0x9b3148: movz            x0, #0x2
    // 0x9b314c: StoreField: r1->field_b = r0
    //     0x9b314c: stur            w0, [x1, #0xb]
    // 0x9b3150: r16 = <String>
    //     0x9b3150: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x9b3154: ldur            lr, [fp, #-0x10]
    // 0x9b3158: stp             lr, x16, [SP, #0x20]
    // 0x9b315c: ldur            x16, [fp, #-0x18]
    // 0x9b3160: stp             x16, x1, [SP, #0x10]
    // 0x9b3164: r16 = Instance_Color
    //     0x9b3164: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x9b3168: ldur            lr, [fp, #-0x20]
    // 0x9b316c: stp             lr, x16, [SP]
    // 0x9b3170: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0x9b3170: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0x9b3174: ldr             x4, [x4, #0xa78]
    // 0x9b3178: r0 = showMenu()
    //     0x9b3178: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0x9b317c: ldur            x2, [fp, #-0x28]
    // 0x9b3180: r1 = Function '<anonymous closure>':.
    //     0x9b3180: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa80] AnonymousClosure: (0x9b31c0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::showMenuItem (0x9b2c74)
    //     0x9b3184: ldr             x1, [x1, #0xa80]
    // 0x9b3188: stur            x0, [fp, #-8]
    // 0x9b318c: r0 = AllocateClosure()
    //     0x9b318c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b3190: r16 = <Null?>
    //     0x9b3190: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x9b3194: ldur            lr, [fp, #-8]
    // 0x9b3198: stp             lr, x16, [SP, #8]
    // 0x9b319c: str             x0, [SP]
    // 0x9b31a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9b31a0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9b31a4: r0 = then()
    //     0x9b31a4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x9b31a8: r0 = Null
    //     0x9b31a8: mov             x0, NULL
    // 0x9b31ac: LeaveFrame
    //     0x9b31ac: mov             SP, fp
    //     0x9b31b0: ldp             fp, lr, [SP], #0x10
    // 0x9b31b4: ret
    //     0x9b31b4: ret             
    // 0x9b31b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b31b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b31bc: b               #0x9b2ca4
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0x9b31c0, size: 0xf4
    // 0x9b31c0: EnterFrame
    //     0x9b31c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9b31c4: mov             fp, SP
    // 0x9b31c8: AllocStack(0x20)
    //     0x9b31c8: sub             SP, SP, #0x20
    // 0x9b31cc: SetupParameters()
    //     0x9b31cc: ldr             x0, [fp, #0x18]
    //     0x9b31d0: ldur            w1, [x0, #0x17]
    //     0x9b31d4: add             x1, x1, HEAP, lsl #32
    //     0x9b31d8: stur            x1, [fp, #-8]
    // 0x9b31dc: CheckStackOverflow
    //     0x9b31dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b31e0: cmp             SP, x16
    //     0x9b31e4: b.ls            #0x9b32ac
    // 0x9b31e8: ldr             x0, [fp, #0x10]
    // 0x9b31ec: r2 = LoadClassIdInstr(r0)
    //     0x9b31ec: ldur            x2, [x0, #-1]
    //     0x9b31f0: ubfx            x2, x2, #0xc, #0x14
    // 0x9b31f4: r16 = "flag"
    //     0x9b31f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0x9b31f8: ldr             x16, [x16, #0xa68]
    // 0x9b31fc: stp             x16, x0, [SP]
    // 0x9b3200: mov             x0, x2
    // 0x9b3204: mov             lr, x0
    // 0x9b3208: ldr             lr, [x21, lr, lsl #3]
    // 0x9b320c: blr             lr
    // 0x9b3210: tbnz            w0, #4, #0x9b329c
    // 0x9b3214: ldur            x0, [fp, #-8]
    // 0x9b3218: LoadField: r1 = r0->field_f
    //     0x9b3218: ldur            w1, [x0, #0xf]
    // 0x9b321c: DecompressPointer r1
    //     0x9b321c: add             x1, x1, HEAP, lsl #32
    // 0x9b3220: r0 = controller()
    //     0x9b3220: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3224: LoadField: r1 = r0->field_93
    //     0x9b3224: ldur            w1, [x0, #0x93]
    // 0x9b3228: DecompressPointer r1
    //     0x9b3228: add             x1, x1, HEAP, lsl #32
    // 0x9b322c: ldur            x0, [fp, #-8]
    // 0x9b3230: LoadField: r2 = r0->field_13
    //     0x9b3230: ldur            w2, [x0, #0x13]
    // 0x9b3234: DecompressPointer r2
    //     0x9b3234: add             x2, x2, HEAP, lsl #32
    // 0x9b3238: r3 = true
    //     0x9b3238: add             x3, NULL, #0x20  ; true
    // 0x9b323c: r0 = []=()
    //     0x9b323c: bl              #0x164c2a0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]=
    // 0x9b3240: ldur            x0, [fp, #-8]
    // 0x9b3244: LoadField: r1 = r0->field_f
    //     0x9b3244: ldur            w1, [x0, #0xf]
    // 0x9b3248: DecompressPointer r1
    //     0x9b3248: add             x1, x1, HEAP, lsl #32
    // 0x9b324c: r0 = controller()
    //     0x9b324c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3250: mov             x2, x0
    // 0x9b3254: ldur            x0, [fp, #-8]
    // 0x9b3258: stur            x2, [fp, #-0x10]
    // 0x9b325c: LoadField: r1 = r0->field_f
    //     0x9b325c: ldur            w1, [x0, #0xf]
    // 0x9b3260: DecompressPointer r1
    //     0x9b3260: add             x1, x1, HEAP, lsl #32
    // 0x9b3264: r0 = controller()
    //     0x9b3264: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3268: LoadField: r2 = r0->field_93
    //     0x9b3268: ldur            w2, [x0, #0x93]
    // 0x9b326c: DecompressPointer r2
    //     0x9b326c: add             x2, x2, HEAP, lsl #32
    // 0x9b3270: ldur            x1, [fp, #-0x10]
    // 0x9b3274: r0 = saveFlaggedData()
    //     0x9b3274: bl              #0x9b1770  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::saveFlaggedData
    // 0x9b3278: ldur            x0, [fp, #-8]
    // 0x9b327c: LoadField: r1 = r0->field_f
    //     0x9b327c: ldur            w1, [x0, #0xf]
    // 0x9b3280: DecompressPointer r1
    //     0x9b3280: add             x1, x1, HEAP, lsl #32
    // 0x9b3284: r0 = controller()
    //     0x9b3284: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3288: mov             x1, x0
    // 0x9b328c: r2 = "flag_abusive"
    //     0x9b328c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0x9b3290: ldr             x2, [x2, #0xa88]
    // 0x9b3294: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9b3294: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9b3298: r0 = ratingReviewClickedEvent()
    //     0x9b3298: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9b329c: r0 = Null
    //     0x9b329c: mov             x0, NULL
    // 0x9b32a0: LeaveFrame
    //     0x9b32a0: mov             SP, fp
    //     0x9b32a4: ldp             fp, lr, [SP], #0x10
    // 0x9b32a8: ret
    //     0x9b32a8: ret             
    // 0x9b32ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b32ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b32b0: b               #0x9b31e8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9b3344, size: 0xc4
    // 0x9b3344: EnterFrame
    //     0x9b3344: stp             fp, lr, [SP, #-0x10]!
    //     0x9b3348: mov             fp, SP
    // 0x9b334c: AllocStack(0x10)
    //     0x9b334c: sub             SP, SP, #0x10
    // 0x9b3350: SetupParameters()
    //     0x9b3350: ldr             x0, [fp, #0x10]
    //     0x9b3354: ldur            w2, [x0, #0x17]
    //     0x9b3358: add             x2, x2, HEAP, lsl #32
    //     0x9b335c: stur            x2, [fp, #-8]
    // 0x9b3360: CheckStackOverflow
    //     0x9b3360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b3364: cmp             SP, x16
    //     0x9b3368: b.ls            #0x9b3400
    // 0x9b336c: LoadField: r1 = r2->field_f
    //     0x9b336c: ldur            w1, [x2, #0xf]
    // 0x9b3370: DecompressPointer r1
    //     0x9b3370: add             x1, x1, HEAP, lsl #32
    // 0x9b3374: r0 = controller()
    //     0x9b3374: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3378: LoadField: r1 = r0->field_93
    //     0x9b3378: ldur            w1, [x0, #0x93]
    // 0x9b337c: DecompressPointer r1
    //     0x9b337c: add             x1, x1, HEAP, lsl #32
    // 0x9b3380: ldur            x0, [fp, #-8]
    // 0x9b3384: LoadField: r2 = r0->field_13
    //     0x9b3384: ldur            w2, [x0, #0x13]
    // 0x9b3388: DecompressPointer r2
    //     0x9b3388: add             x2, x2, HEAP, lsl #32
    // 0x9b338c: r3 = true
    //     0x9b338c: add             x3, NULL, #0x20  ; true
    // 0x9b3390: r0 = []=()
    //     0x9b3390: bl              #0x164c2a0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]=
    // 0x9b3394: ldur            x0, [fp, #-8]
    // 0x9b3398: LoadField: r1 = r0->field_f
    //     0x9b3398: ldur            w1, [x0, #0xf]
    // 0x9b339c: DecompressPointer r1
    //     0x9b339c: add             x1, x1, HEAP, lsl #32
    // 0x9b33a0: r0 = controller()
    //     0x9b33a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b33a4: mov             x2, x0
    // 0x9b33a8: ldur            x0, [fp, #-8]
    // 0x9b33ac: stur            x2, [fp, #-0x10]
    // 0x9b33b0: LoadField: r1 = r0->field_f
    //     0x9b33b0: ldur            w1, [x0, #0xf]
    // 0x9b33b4: DecompressPointer r1
    //     0x9b33b4: add             x1, x1, HEAP, lsl #32
    // 0x9b33b8: r0 = controller()
    //     0x9b33b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b33bc: LoadField: r2 = r0->field_93
    //     0x9b33bc: ldur            w2, [x0, #0x93]
    // 0x9b33c0: DecompressPointer r2
    //     0x9b33c0: add             x2, x2, HEAP, lsl #32
    // 0x9b33c4: ldur            x1, [fp, #-0x10]
    // 0x9b33c8: r0 = saveFlaggedData()
    //     0x9b33c8: bl              #0x9b1770  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::saveFlaggedData
    // 0x9b33cc: ldur            x0, [fp, #-8]
    // 0x9b33d0: LoadField: r1 = r0->field_f
    //     0x9b33d0: ldur            w1, [x0, #0xf]
    // 0x9b33d4: DecompressPointer r1
    //     0x9b33d4: add             x1, x1, HEAP, lsl #32
    // 0x9b33d8: r0 = controller()
    //     0x9b33d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b33dc: mov             x1, x0
    // 0x9b33e0: r2 = "flag_abusive"
    //     0x9b33e0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0x9b33e4: ldr             x2, [x2, #0xa88]
    // 0x9b33e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9b33e8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9b33ec: r0 = ratingReviewClickedEvent()
    //     0x9b33ec: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9b33f0: r0 = Null
    //     0x9b33f0: mov             x0, NULL
    // 0x9b33f4: LeaveFrame
    //     0x9b33f4: mov             SP, fp
    //     0x9b33f8: ldp             fp, lr, [SP], #0x10
    // 0x9b33fc: ret
    //     0x9b33fc: ret             
    // 0x9b3400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b3400: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b3404: b               #0x9b336c
  }
  [closure] void <anonymous closure>(dynamic, TapDownDetails) {
    // ** addr: 0x9b3408, size: 0x78
    // 0x9b3408: EnterFrame
    //     0x9b3408: stp             fp, lr, [SP, #-0x10]!
    //     0x9b340c: mov             fp, SP
    // 0x9b3410: ldr             x0, [fp, #0x18]
    // 0x9b3414: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9b3414: ldur            w1, [x0, #0x17]
    // 0x9b3418: DecompressPointer r1
    //     0x9b3418: add             x1, x1, HEAP, lsl #32
    // 0x9b341c: CheckStackOverflow
    //     0x9b341c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b3420: cmp             SP, x16
    //     0x9b3424: b.ls            #0x9b3478
    // 0x9b3428: LoadField: r0 = r1->field_b
    //     0x9b3428: ldur            w0, [x1, #0xb]
    // 0x9b342c: DecompressPointer r0
    //     0x9b342c: add             x0, x0, HEAP, lsl #32
    // 0x9b3430: LoadField: r1 = r0->field_f
    //     0x9b3430: ldur            w1, [x0, #0xf]
    // 0x9b3434: DecompressPointer r1
    //     0x9b3434: add             x1, x1, HEAP, lsl #32
    // 0x9b3438: r0 = controller()
    //     0x9b3438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b343c: mov             x2, x0
    // 0x9b3440: ldr             x1, [fp, #0x10]
    // 0x9b3444: LoadField: r0 = r1->field_7
    //     0x9b3444: ldur            w0, [x1, #7]
    // 0x9b3448: DecompressPointer r0
    //     0x9b3448: add             x0, x0, HEAP, lsl #32
    // 0x9b344c: StoreField: r2->field_8f = r0
    //     0x9b344c: stur            w0, [x2, #0x8f]
    //     0x9b3450: ldurb           w16, [x2, #-1]
    //     0x9b3454: ldurb           w17, [x0, #-1]
    //     0x9b3458: and             x16, x17, x16, lsr #2
    //     0x9b345c: tst             x16, HEAP, lsr #32
    //     0x9b3460: b.eq            #0x9b3468
    //     0x9b3464: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9b3468: r0 = Null
    //     0x9b3468: mov             x0, NULL
    // 0x9b346c: LeaveFrame
    //     0x9b346c: mov             SP, fp
    //     0x9b3470: ldp             fp, lr, [SP], #0x10
    // 0x9b3474: ret
    //     0x9b3474: ret             
    // 0x9b3478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b3478: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b347c: b               #0x9b3428
  }
  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9b3480, size: 0xc
    // 0x9b3480: r0 = Instance_SizedBox
    //     0x9b3480: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x9b3484: ldr             x0, [x0, #0xb20]
    // 0x9b3488: ret
    //     0x9b3488: ret             
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x9b348c, size: 0x2984
    // 0x9b348c: EnterFrame
    //     0x9b348c: stp             fp, lr, [SP, #-0x10]!
    //     0x9b3490: mov             fp, SP
    // 0x9b3494: AllocStack(0x80)
    //     0x9b3494: sub             SP, SP, #0x80
    // 0x9b3498: SetupParameters()
    //     0x9b3498: ldr             x0, [fp, #0x10]
    //     0x9b349c: ldur            w2, [x0, #0x17]
    //     0x9b34a0: add             x2, x2, HEAP, lsl #32
    //     0x9b34a4: stur            x2, [fp, #-8]
    // 0x9b34a8: CheckStackOverflow
    //     0x9b34a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b34ac: cmp             SP, x16
    //     0x9b34b0: b.ls            #0x9b5e08
    // 0x9b34b4: LoadField: r1 = r2->field_f
    //     0x9b34b4: ldur            w1, [x2, #0xf]
    // 0x9b34b8: DecompressPointer r1
    //     0x9b34b8: add             x1, x1, HEAP, lsl #32
    // 0x9b34bc: r0 = controller()
    //     0x9b34bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b34c0: LoadField: r1 = r0->field_7b
    //     0x9b34c0: ldur            w1, [x0, #0x7b]
    // 0x9b34c4: DecompressPointer r1
    //     0x9b34c4: add             x1, x1, HEAP, lsl #32
    // 0x9b34c8: r0 = value()
    //     0x9b34c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b34cc: LoadField: r1 = r0->field_b
    //     0x9b34cc: ldur            w1, [x0, #0xb]
    // 0x9b34d0: DecompressPointer r1
    //     0x9b34d0: add             x1, x1, HEAP, lsl #32
    // 0x9b34d4: cmp             w1, NULL
    // 0x9b34d8: b.ne            #0x9b34f8
    // 0x9b34dc: r0 = Container()
    //     0x9b34dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9b34e0: mov             x1, x0
    // 0x9b34e4: stur            x0, [fp, #-0x10]
    // 0x9b34e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b34e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b34ec: r0 = Container()
    //     0x9b34ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9b34f0: ldur            x0, [fp, #-0x10]
    // 0x9b34f4: b               #0x9b5dfc
    // 0x9b34f8: ldur            x2, [fp, #-8]
    // 0x9b34fc: LoadField: r1 = r2->field_f
    //     0x9b34fc: ldur            w1, [x2, #0xf]
    // 0x9b3500: DecompressPointer r1
    //     0x9b3500: add             x1, x1, HEAP, lsl #32
    // 0x9b3504: r0 = controller()
    //     0x9b3504: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3508: LoadField: r1 = r0->field_7b
    //     0x9b3508: ldur            w1, [x0, #0x7b]
    // 0x9b350c: DecompressPointer r1
    //     0x9b350c: add             x1, x1, HEAP, lsl #32
    // 0x9b3510: r0 = value()
    //     0x9b3510: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3514: LoadField: r1 = r0->field_b
    //     0x9b3514: ldur            w1, [x0, #0xb]
    // 0x9b3518: DecompressPointer r1
    //     0x9b3518: add             x1, x1, HEAP, lsl #32
    // 0x9b351c: cmp             w1, NULL
    // 0x9b3520: b.ne            #0x9b352c
    // 0x9b3524: r0 = Null
    //     0x9b3524: mov             x0, NULL
    // 0x9b3528: b               #0x9b3550
    // 0x9b352c: LoadField: r0 = r1->field_f
    //     0x9b352c: ldur            w0, [x1, #0xf]
    // 0x9b3530: DecompressPointer r0
    //     0x9b3530: add             x0, x0, HEAP, lsl #32
    // 0x9b3534: cmp             w0, NULL
    // 0x9b3538: b.ne            #0x9b3544
    // 0x9b353c: r0 = Null
    //     0x9b353c: mov             x0, NULL
    // 0x9b3540: b               #0x9b3550
    // 0x9b3544: LoadField: r1 = r0->field_f
    //     0x9b3544: ldur            w1, [x0, #0xf]
    // 0x9b3548: DecompressPointer r1
    //     0x9b3548: add             x1, x1, HEAP, lsl #32
    // 0x9b354c: mov             x0, x1
    // 0x9b3550: ldur            x2, [fp, #-8]
    // 0x9b3554: r1 = LoadClassIdInstr(r0)
    //     0x9b3554: ldur            x1, [x0, #-1]
    //     0x9b3558: ubfx            x1, x1, #0xc, #0x14
    // 0x9b355c: stp             xzr, x0, [SP]
    // 0x9b3560: mov             x0, x1
    // 0x9b3564: mov             lr, x0
    // 0x9b3568: ldr             lr, [x21, lr, lsl #3]
    // 0x9b356c: blr             lr
    // 0x9b3570: eor             x2, x0, #0x10
    // 0x9b3574: ldur            x0, [fp, #-8]
    // 0x9b3578: stur            x2, [fp, #-0x10]
    // 0x9b357c: LoadField: r1 = r0->field_f
    //     0x9b357c: ldur            w1, [x0, #0xf]
    // 0x9b3580: DecompressPointer r1
    //     0x9b3580: add             x1, x1, HEAP, lsl #32
    // 0x9b3584: r0 = controller()
    //     0x9b3584: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3588: LoadField: r1 = r0->field_7b
    //     0x9b3588: ldur            w1, [x0, #0x7b]
    // 0x9b358c: DecompressPointer r1
    //     0x9b358c: add             x1, x1, HEAP, lsl #32
    // 0x9b3590: r0 = value()
    //     0x9b3590: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3594: LoadField: r1 = r0->field_b
    //     0x9b3594: ldur            w1, [x0, #0xb]
    // 0x9b3598: DecompressPointer r1
    //     0x9b3598: add             x1, x1, HEAP, lsl #32
    // 0x9b359c: cmp             w1, NULL
    // 0x9b35a0: b.ne            #0x9b35ac
    // 0x9b35a4: r0 = Null
    //     0x9b35a4: mov             x0, NULL
    // 0x9b35a8: b               #0x9b35ec
    // 0x9b35ac: LoadField: r0 = r1->field_f
    //     0x9b35ac: ldur            w0, [x1, #0xf]
    // 0x9b35b0: DecompressPointer r0
    //     0x9b35b0: add             x0, x0, HEAP, lsl #32
    // 0x9b35b4: cmp             w0, NULL
    // 0x9b35b8: b.ne            #0x9b35c4
    // 0x9b35bc: r0 = Null
    //     0x9b35bc: mov             x0, NULL
    // 0x9b35c0: b               #0x9b35ec
    // 0x9b35c4: LoadField: r1 = r0->field_f
    //     0x9b35c4: ldur            w1, [x0, #0xf]
    // 0x9b35c8: DecompressPointer r1
    //     0x9b35c8: add             x1, x1, HEAP, lsl #32
    // 0x9b35cc: r0 = LoadClassIdInstr(r1)
    //     0x9b35cc: ldur            x0, [x1, #-1]
    //     0x9b35d0: ubfx            x0, x0, #0xc, #0x14
    // 0x9b35d4: str             x1, [SP]
    // 0x9b35d8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x9b35d8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x9b35dc: r0 = GDT[cid_x0 + 0x2700]()
    //     0x9b35dc: movz            x17, #0x2700
    //     0x9b35e0: add             lr, x0, x17
    //     0x9b35e4: ldr             lr, [x21, lr, lsl #3]
    //     0x9b35e8: blr             lr
    // 0x9b35ec: cmp             w0, NULL
    // 0x9b35f0: b.ne            #0x9b35f8
    // 0x9b35f4: r0 = ""
    //     0x9b35f4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b35f8: ldur            x2, [fp, #-8]
    // 0x9b35fc: stur            x0, [fp, #-0x18]
    // 0x9b3600: LoadField: r1 = r2->field_13
    //     0x9b3600: ldur            w1, [x2, #0x13]
    // 0x9b3604: DecompressPointer r1
    //     0x9b3604: add             x1, x1, HEAP, lsl #32
    // 0x9b3608: r0 = of()
    //     0x9b3608: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b360c: LoadField: r1 = r0->field_87
    //     0x9b360c: ldur            w1, [x0, #0x87]
    // 0x9b3610: DecompressPointer r1
    //     0x9b3610: add             x1, x1, HEAP, lsl #32
    // 0x9b3614: LoadField: r0 = r1->field_23
    //     0x9b3614: ldur            w0, [x1, #0x23]
    // 0x9b3618: DecompressPointer r0
    //     0x9b3618: add             x0, x0, HEAP, lsl #32
    // 0x9b361c: r16 = Instance_Color
    //     0x9b361c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b3620: r30 = 32.000000
    //     0x9b3620: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x9b3624: ldr             lr, [lr, #0x848]
    // 0x9b3628: stp             lr, x16, [SP]
    // 0x9b362c: mov             x1, x0
    // 0x9b3630: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9b3630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9b3634: ldr             x4, [x4, #0x9b8]
    // 0x9b3638: r0 = copyWith()
    //     0x9b3638: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b363c: stur            x0, [fp, #-0x20]
    // 0x9b3640: r0 = Text()
    //     0x9b3640: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b3644: mov             x1, x0
    // 0x9b3648: ldur            x0, [fp, #-0x18]
    // 0x9b364c: stur            x1, [fp, #-0x28]
    // 0x9b3650: StoreField: r1->field_b = r0
    //     0x9b3650: stur            w0, [x1, #0xb]
    // 0x9b3654: ldur            x0, [fp, #-0x20]
    // 0x9b3658: StoreField: r1->field_13 = r0
    //     0x9b3658: stur            w0, [x1, #0x13]
    // 0x9b365c: r0 = Padding()
    //     0x9b365c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9b3660: mov             x2, x0
    // 0x9b3664: r0 = Instance_EdgeInsets
    //     0x9b3664: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0x9b3668: ldr             x0, [x0, #0x850]
    // 0x9b366c: stur            x2, [fp, #-0x18]
    // 0x9b3670: StoreField: r2->field_f = r0
    //     0x9b3670: stur            w0, [x2, #0xf]
    // 0x9b3674: ldur            x0, [fp, #-0x28]
    // 0x9b3678: StoreField: r2->field_b = r0
    //     0x9b3678: stur            w0, [x2, #0xb]
    // 0x9b367c: ldur            x0, [fp, #-8]
    // 0x9b3680: LoadField: r1 = r0->field_f
    //     0x9b3680: ldur            w1, [x0, #0xf]
    // 0x9b3684: DecompressPointer r1
    //     0x9b3684: add             x1, x1, HEAP, lsl #32
    // 0x9b3688: r0 = controller()
    //     0x9b3688: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b368c: LoadField: r1 = r0->field_7b
    //     0x9b368c: ldur            w1, [x0, #0x7b]
    // 0x9b3690: DecompressPointer r1
    //     0x9b3690: add             x1, x1, HEAP, lsl #32
    // 0x9b3694: r0 = value()
    //     0x9b3694: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3698: LoadField: r1 = r0->field_b
    //     0x9b3698: ldur            w1, [x0, #0xb]
    // 0x9b369c: DecompressPointer r1
    //     0x9b369c: add             x1, x1, HEAP, lsl #32
    // 0x9b36a0: cmp             w1, NULL
    // 0x9b36a4: b.ne            #0x9b36b0
    // 0x9b36a8: r0 = Null
    //     0x9b36a8: mov             x0, NULL
    // 0x9b36ac: b               #0x9b36d4
    // 0x9b36b0: LoadField: r0 = r1->field_f
    //     0x9b36b0: ldur            w0, [x1, #0xf]
    // 0x9b36b4: DecompressPointer r0
    //     0x9b36b4: add             x0, x0, HEAP, lsl #32
    // 0x9b36b8: cmp             w0, NULL
    // 0x9b36bc: b.ne            #0x9b36c8
    // 0x9b36c0: r0 = Null
    //     0x9b36c0: mov             x0, NULL
    // 0x9b36c4: b               #0x9b36d4
    // 0x9b36c8: LoadField: r1 = r0->field_f
    //     0x9b36c8: ldur            w1, [x0, #0xf]
    // 0x9b36cc: DecompressPointer r1
    //     0x9b36cc: add             x1, x1, HEAP, lsl #32
    // 0x9b36d0: mov             x0, x1
    // 0x9b36d4: cmp             w0, NULL
    // 0x9b36d8: b.ne            #0x9b36e4
    // 0x9b36dc: d1 = 0.000000
    //     0x9b36dc: eor             v1.16b, v1.16b, v1.16b
    // 0x9b36e0: b               #0x9b36ec
    // 0x9b36e4: LoadField: d0 = r0->field_7
    //     0x9b36e4: ldur            d0, [x0, #7]
    // 0x9b36e8: mov             v1.16b, v0.16b
    // 0x9b36ec: d0 = 4.000000
    //     0x9b36ec: fmov            d0, #4.00000000
    // 0x9b36f0: fcmp            d1, d0
    // 0x9b36f4: b.lt            #0x9b3704
    // 0x9b36f8: r1 = Instance_Color
    //     0x9b36f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9b36fc: ldr             x1, [x1, #0x858]
    // 0x9b3700: b               #0x9b382c
    // 0x9b3704: ldur            x2, [fp, #-8]
    // 0x9b3708: LoadField: r1 = r2->field_f
    //     0x9b3708: ldur            w1, [x2, #0xf]
    // 0x9b370c: DecompressPointer r1
    //     0x9b370c: add             x1, x1, HEAP, lsl #32
    // 0x9b3710: r0 = controller()
    //     0x9b3710: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3714: LoadField: r1 = r0->field_7b
    //     0x9b3714: ldur            w1, [x0, #0x7b]
    // 0x9b3718: DecompressPointer r1
    //     0x9b3718: add             x1, x1, HEAP, lsl #32
    // 0x9b371c: r0 = value()
    //     0x9b371c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3720: LoadField: r1 = r0->field_b
    //     0x9b3720: ldur            w1, [x0, #0xb]
    // 0x9b3724: DecompressPointer r1
    //     0x9b3724: add             x1, x1, HEAP, lsl #32
    // 0x9b3728: cmp             w1, NULL
    // 0x9b372c: b.ne            #0x9b3738
    // 0x9b3730: r0 = Null
    //     0x9b3730: mov             x0, NULL
    // 0x9b3734: b               #0x9b375c
    // 0x9b3738: LoadField: r0 = r1->field_f
    //     0x9b3738: ldur            w0, [x1, #0xf]
    // 0x9b373c: DecompressPointer r0
    //     0x9b373c: add             x0, x0, HEAP, lsl #32
    // 0x9b3740: cmp             w0, NULL
    // 0x9b3744: b.ne            #0x9b3750
    // 0x9b3748: r0 = Null
    //     0x9b3748: mov             x0, NULL
    // 0x9b374c: b               #0x9b375c
    // 0x9b3750: LoadField: r1 = r0->field_f
    //     0x9b3750: ldur            w1, [x0, #0xf]
    // 0x9b3754: DecompressPointer r1
    //     0x9b3754: add             x1, x1, HEAP, lsl #32
    // 0x9b3758: mov             x0, x1
    // 0x9b375c: cmp             w0, NULL
    // 0x9b3760: b.ne            #0x9b376c
    // 0x9b3764: d1 = 0.000000
    //     0x9b3764: eor             v1.16b, v1.16b, v1.16b
    // 0x9b3768: b               #0x9b3774
    // 0x9b376c: LoadField: d0 = r0->field_7
    //     0x9b376c: ldur            d0, [x0, #7]
    // 0x9b3770: mov             v1.16b, v0.16b
    // 0x9b3774: d0 = 3.500000
    //     0x9b3774: fmov            d0, #3.50000000
    // 0x9b3778: fcmp            d1, d0
    // 0x9b377c: b.lt            #0x9b3798
    // 0x9b3780: r1 = Instance_Color
    //     0x9b3780: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9b3784: ldr             x1, [x1, #0x858]
    // 0x9b3788: d0 = 0.700000
    //     0x9b3788: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9b378c: ldr             d0, [x17, #0xf48]
    // 0x9b3790: r0 = withOpacity()
    //     0x9b3790: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b3794: b               #0x9b3828
    // 0x9b3798: ldur            x2, [fp, #-8]
    // 0x9b379c: LoadField: r1 = r2->field_f
    //     0x9b379c: ldur            w1, [x2, #0xf]
    // 0x9b37a0: DecompressPointer r1
    //     0x9b37a0: add             x1, x1, HEAP, lsl #32
    // 0x9b37a4: r0 = controller()
    //     0x9b37a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b37a8: LoadField: r1 = r0->field_7b
    //     0x9b37a8: ldur            w1, [x0, #0x7b]
    // 0x9b37ac: DecompressPointer r1
    //     0x9b37ac: add             x1, x1, HEAP, lsl #32
    // 0x9b37b0: r0 = value()
    //     0x9b37b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b37b4: LoadField: r1 = r0->field_b
    //     0x9b37b4: ldur            w1, [x0, #0xb]
    // 0x9b37b8: DecompressPointer r1
    //     0x9b37b8: add             x1, x1, HEAP, lsl #32
    // 0x9b37bc: cmp             w1, NULL
    // 0x9b37c0: b.ne            #0x9b37cc
    // 0x9b37c4: r0 = Null
    //     0x9b37c4: mov             x0, NULL
    // 0x9b37c8: b               #0x9b37f0
    // 0x9b37cc: LoadField: r0 = r1->field_f
    //     0x9b37cc: ldur            w0, [x1, #0xf]
    // 0x9b37d0: DecompressPointer r0
    //     0x9b37d0: add             x0, x0, HEAP, lsl #32
    // 0x9b37d4: cmp             w0, NULL
    // 0x9b37d8: b.ne            #0x9b37e4
    // 0x9b37dc: r0 = Null
    //     0x9b37dc: mov             x0, NULL
    // 0x9b37e0: b               #0x9b37f0
    // 0x9b37e4: LoadField: r1 = r0->field_f
    //     0x9b37e4: ldur            w1, [x0, #0xf]
    // 0x9b37e8: DecompressPointer r1
    //     0x9b37e8: add             x1, x1, HEAP, lsl #32
    // 0x9b37ec: mov             x0, x1
    // 0x9b37f0: cmp             w0, NULL
    // 0x9b37f4: b.ne            #0x9b3800
    // 0x9b37f8: d1 = 0.000000
    //     0x9b37f8: eor             v1.16b, v1.16b, v1.16b
    // 0x9b37fc: b               #0x9b3808
    // 0x9b3800: LoadField: d0 = r0->field_7
    //     0x9b3800: ldur            d0, [x0, #7]
    // 0x9b3804: mov             v1.16b, v0.16b
    // 0x9b3808: d0 = 2.000000
    //     0x9b3808: fmov            d0, #2.00000000
    // 0x9b380c: fcmp            d1, d0
    // 0x9b3810: b.lt            #0x9b3820
    // 0x9b3814: r0 = Instance_Color
    //     0x9b3814: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0x9b3818: ldr             x0, [x0, #0x860]
    // 0x9b381c: b               #0x9b3828
    // 0x9b3820: r0 = Instance_Color
    //     0x9b3820: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x9b3824: ldr             x0, [x0, #0x50]
    // 0x9b3828: mov             x1, x0
    // 0x9b382c: ldur            x2, [fp, #-8]
    // 0x9b3830: ldur            x0, [fp, #-0x18]
    // 0x9b3834: stur            x1, [fp, #-0x20]
    // 0x9b3838: r0 = ColorFilter()
    //     0x9b3838: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x9b383c: mov             x1, x0
    // 0x9b3840: ldur            x0, [fp, #-0x20]
    // 0x9b3844: stur            x1, [fp, #-0x28]
    // 0x9b3848: StoreField: r1->field_7 = r0
    //     0x9b3848: stur            w0, [x1, #7]
    // 0x9b384c: r0 = Instance_BlendMode
    //     0x9b384c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x9b3850: ldr             x0, [x0, #0xb30]
    // 0x9b3854: StoreField: r1->field_b = r0
    //     0x9b3854: stur            w0, [x1, #0xb]
    // 0x9b3858: r0 = 1
    //     0x9b3858: movz            x0, #0x1
    // 0x9b385c: StoreField: r1->field_13 = r0
    //     0x9b385c: stur            x0, [x1, #0x13]
    // 0x9b3860: r0 = SvgPicture()
    //     0x9b3860: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9b3864: stur            x0, [fp, #-0x20]
    // 0x9b3868: ldur            x16, [fp, #-0x28]
    // 0x9b386c: str             x16, [SP]
    // 0x9b3870: mov             x1, x0
    // 0x9b3874: r2 = "assets/images/big_green_star.svg"
    //     0x9b3874: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f868] "assets/images/big_green_star.svg"
    //     0x9b3878: ldr             x2, [x2, #0x868]
    // 0x9b387c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x9b387c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x9b3880: ldr             x4, [x4, #0xa38]
    // 0x9b3884: r0 = SvgPicture.asset()
    //     0x9b3884: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9b3888: r1 = Null
    //     0x9b3888: mov             x1, NULL
    // 0x9b388c: r2 = 4
    //     0x9b388c: movz            x2, #0x4
    // 0x9b3890: r0 = AllocateArray()
    //     0x9b3890: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b3894: mov             x2, x0
    // 0x9b3898: ldur            x0, [fp, #-0x18]
    // 0x9b389c: stur            x2, [fp, #-0x28]
    // 0x9b38a0: StoreField: r2->field_f = r0
    //     0x9b38a0: stur            w0, [x2, #0xf]
    // 0x9b38a4: ldur            x0, [fp, #-0x20]
    // 0x9b38a8: StoreField: r2->field_13 = r0
    //     0x9b38a8: stur            w0, [x2, #0x13]
    // 0x9b38ac: r1 = <Widget>
    //     0x9b38ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b38b0: r0 = AllocateGrowableArray()
    //     0x9b38b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b38b4: mov             x1, x0
    // 0x9b38b8: ldur            x0, [fp, #-0x28]
    // 0x9b38bc: stur            x1, [fp, #-0x18]
    // 0x9b38c0: StoreField: r1->field_f = r0
    //     0x9b38c0: stur            w0, [x1, #0xf]
    // 0x9b38c4: r2 = 4
    //     0x9b38c4: movz            x2, #0x4
    // 0x9b38c8: StoreField: r1->field_b = r2
    //     0x9b38c8: stur            w2, [x1, #0xb]
    // 0x9b38cc: r0 = Row()
    //     0x9b38cc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b38d0: mov             x3, x0
    // 0x9b38d4: r0 = Instance_Axis
    //     0x9b38d4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b38d8: stur            x3, [fp, #-0x20]
    // 0x9b38dc: StoreField: r3->field_f = r0
    //     0x9b38dc: stur            w0, [x3, #0xf]
    // 0x9b38e0: r4 = Instance_MainAxisAlignment
    //     0x9b38e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b38e4: ldr             x4, [x4, #0xa08]
    // 0x9b38e8: StoreField: r3->field_13 = r4
    //     0x9b38e8: stur            w4, [x3, #0x13]
    // 0x9b38ec: r5 = Instance_MainAxisSize
    //     0x9b38ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b38f0: ldr             x5, [x5, #0xa10]
    // 0x9b38f4: ArrayStore: r3[0] = r5  ; List_4
    //     0x9b38f4: stur            w5, [x3, #0x17]
    // 0x9b38f8: r6 = Instance_CrossAxisAlignment
    //     0x9b38f8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b38fc: ldr             x6, [x6, #0xa18]
    // 0x9b3900: StoreField: r3->field_1b = r6
    //     0x9b3900: stur            w6, [x3, #0x1b]
    // 0x9b3904: r7 = Instance_VerticalDirection
    //     0x9b3904: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b3908: ldr             x7, [x7, #0xa20]
    // 0x9b390c: StoreField: r3->field_23 = r7
    //     0x9b390c: stur            w7, [x3, #0x23]
    // 0x9b3910: r8 = Instance_Clip
    //     0x9b3910: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b3914: ldr             x8, [x8, #0x38]
    // 0x9b3918: StoreField: r3->field_2b = r8
    //     0x9b3918: stur            w8, [x3, #0x2b]
    // 0x9b391c: StoreField: r3->field_2f = rZR
    //     0x9b391c: stur            xzr, [x3, #0x2f]
    // 0x9b3920: ldur            x1, [fp, #-0x18]
    // 0x9b3924: StoreField: r3->field_b = r1
    //     0x9b3924: stur            w1, [x3, #0xb]
    // 0x9b3928: r1 = Null
    //     0x9b3928: mov             x1, NULL
    // 0x9b392c: r2 = 2
    //     0x9b392c: movz            x2, #0x2
    // 0x9b3930: r0 = AllocateArray()
    //     0x9b3930: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b3934: mov             x2, x0
    // 0x9b3938: ldur            x0, [fp, #-0x20]
    // 0x9b393c: stur            x2, [fp, #-0x18]
    // 0x9b3940: StoreField: r2->field_f = r0
    //     0x9b3940: stur            w0, [x2, #0xf]
    // 0x9b3944: r1 = <Widget>
    //     0x9b3944: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b3948: r0 = AllocateGrowableArray()
    //     0x9b3948: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b394c: mov             x2, x0
    // 0x9b3950: ldur            x0, [fp, #-0x18]
    // 0x9b3954: stur            x2, [fp, #-0x20]
    // 0x9b3958: StoreField: r2->field_f = r0
    //     0x9b3958: stur            w0, [x2, #0xf]
    // 0x9b395c: r0 = 2
    //     0x9b395c: movz            x0, #0x2
    // 0x9b3960: StoreField: r2->field_b = r0
    //     0x9b3960: stur            w0, [x2, #0xb]
    // 0x9b3964: ldur            x3, [fp, #-8]
    // 0x9b3968: LoadField: r1 = r3->field_f
    //     0x9b3968: ldur            w1, [x3, #0xf]
    // 0x9b396c: DecompressPointer r1
    //     0x9b396c: add             x1, x1, HEAP, lsl #32
    // 0x9b3970: r0 = controller()
    //     0x9b3970: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3974: LoadField: r1 = r0->field_7b
    //     0x9b3974: ldur            w1, [x0, #0x7b]
    // 0x9b3978: DecompressPointer r1
    //     0x9b3978: add             x1, x1, HEAP, lsl #32
    // 0x9b397c: r0 = value()
    //     0x9b397c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3980: LoadField: r1 = r0->field_b
    //     0x9b3980: ldur            w1, [x0, #0xb]
    // 0x9b3984: DecompressPointer r1
    //     0x9b3984: add             x1, x1, HEAP, lsl #32
    // 0x9b3988: cmp             w1, NULL
    // 0x9b398c: b.eq            #0x9b39ac
    // 0x9b3990: LoadField: r0 = r1->field_f
    //     0x9b3990: ldur            w0, [x1, #0xf]
    // 0x9b3994: DecompressPointer r0
    //     0x9b3994: add             x0, x0, HEAP, lsl #32
    // 0x9b3998: cmp             w0, NULL
    // 0x9b399c: b.eq            #0x9b39ac
    // 0x9b39a0: LoadField: r1 = r0->field_13
    //     0x9b39a0: ldur            w1, [x0, #0x13]
    // 0x9b39a4: DecompressPointer r1
    //     0x9b39a4: add             x1, x1, HEAP, lsl #32
    // 0x9b39a8: cbz             w1, #0x9b3b3c
    // 0x9b39ac: ldur            x2, [fp, #-8]
    // 0x9b39b0: LoadField: r1 = r2->field_f
    //     0x9b39b0: ldur            w1, [x2, #0xf]
    // 0x9b39b4: DecompressPointer r1
    //     0x9b39b4: add             x1, x1, HEAP, lsl #32
    // 0x9b39b8: r0 = controller()
    //     0x9b39b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b39bc: LoadField: r1 = r0->field_7b
    //     0x9b39bc: ldur            w1, [x0, #0x7b]
    // 0x9b39c0: DecompressPointer r1
    //     0x9b39c0: add             x1, x1, HEAP, lsl #32
    // 0x9b39c4: r0 = value()
    //     0x9b39c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b39c8: LoadField: r1 = r0->field_b
    //     0x9b39c8: ldur            w1, [x0, #0xb]
    // 0x9b39cc: DecompressPointer r1
    //     0x9b39cc: add             x1, x1, HEAP, lsl #32
    // 0x9b39d0: cmp             w1, NULL
    // 0x9b39d4: b.ne            #0x9b39e0
    // 0x9b39d8: r0 = Null
    //     0x9b39d8: mov             x0, NULL
    // 0x9b39dc: b               #0x9b3a04
    // 0x9b39e0: LoadField: r0 = r1->field_f
    //     0x9b39e0: ldur            w0, [x1, #0xf]
    // 0x9b39e4: DecompressPointer r0
    //     0x9b39e4: add             x0, x0, HEAP, lsl #32
    // 0x9b39e8: cmp             w0, NULL
    // 0x9b39ec: b.ne            #0x9b39f8
    // 0x9b39f0: r0 = Null
    //     0x9b39f0: mov             x0, NULL
    // 0x9b39f4: b               #0x9b3a04
    // 0x9b39f8: LoadField: r1 = r0->field_13
    //     0x9b39f8: ldur            w1, [x0, #0x13]
    // 0x9b39fc: DecompressPointer r1
    //     0x9b39fc: add             x1, x1, HEAP, lsl #32
    // 0x9b3a00: mov             x0, x1
    // 0x9b3a04: cmp             w0, NULL
    // 0x9b3a08: b.ne            #0x9b3a14
    // 0x9b3a0c: r4 = ""
    //     0x9b3a0c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b3a10: b               #0x9b3a18
    // 0x9b3a14: mov             x4, x0
    // 0x9b3a18: ldur            x0, [fp, #-8]
    // 0x9b3a1c: ldur            x3, [fp, #-0x20]
    // 0x9b3a20: stur            x4, [fp, #-0x18]
    // 0x9b3a24: r1 = Null
    //     0x9b3a24: mov             x1, NULL
    // 0x9b3a28: r2 = 4
    //     0x9b3a28: movz            x2, #0x4
    // 0x9b3a2c: r0 = AllocateArray()
    //     0x9b3a2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b3a30: mov             x1, x0
    // 0x9b3a34: ldur            x0, [fp, #-0x18]
    // 0x9b3a38: StoreField: r1->field_f = r0
    //     0x9b3a38: stur            w0, [x1, #0xf]
    // 0x9b3a3c: r16 = " Ratings"
    //     0x9b3a3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f870] " Ratings"
    //     0x9b3a40: ldr             x16, [x16, #0x870]
    // 0x9b3a44: StoreField: r1->field_13 = r16
    //     0x9b3a44: stur            w16, [x1, #0x13]
    // 0x9b3a48: str             x1, [SP]
    // 0x9b3a4c: r0 = _interpolate()
    //     0x9b3a4c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9b3a50: ldur            x2, [fp, #-8]
    // 0x9b3a54: stur            x0, [fp, #-0x18]
    // 0x9b3a58: LoadField: r1 = r2->field_13
    //     0x9b3a58: ldur            w1, [x2, #0x13]
    // 0x9b3a5c: DecompressPointer r1
    //     0x9b3a5c: add             x1, x1, HEAP, lsl #32
    // 0x9b3a60: r0 = of()
    //     0x9b3a60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b3a64: LoadField: r1 = r0->field_87
    //     0x9b3a64: ldur            w1, [x0, #0x87]
    // 0x9b3a68: DecompressPointer r1
    //     0x9b3a68: add             x1, x1, HEAP, lsl #32
    // 0x9b3a6c: LoadField: r0 = r1->field_2b
    //     0x9b3a6c: ldur            w0, [x1, #0x2b]
    // 0x9b3a70: DecompressPointer r0
    //     0x9b3a70: add             x0, x0, HEAP, lsl #32
    // 0x9b3a74: stur            x0, [fp, #-0x28]
    // 0x9b3a78: r1 = Instance_Color
    //     0x9b3a78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b3a7c: d0 = 0.850000
    //     0x9b3a7c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0x9b3a80: ldr             d0, [x17, #0x878]
    // 0x9b3a84: r0 = withOpacity()
    //     0x9b3a84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b3a88: r16 = 10.000000
    //     0x9b3a88: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x9b3a8c: stp             x0, x16, [SP]
    // 0x9b3a90: ldur            x1, [fp, #-0x28]
    // 0x9b3a94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b3a94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b3a98: ldr             x4, [x4, #0xaa0]
    // 0x9b3a9c: r0 = copyWith()
    //     0x9b3a9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b3aa0: stur            x0, [fp, #-0x28]
    // 0x9b3aa4: r0 = Text()
    //     0x9b3aa4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b3aa8: mov             x2, x0
    // 0x9b3aac: ldur            x0, [fp, #-0x18]
    // 0x9b3ab0: stur            x2, [fp, #-0x38]
    // 0x9b3ab4: StoreField: r2->field_b = r0
    //     0x9b3ab4: stur            w0, [x2, #0xb]
    // 0x9b3ab8: ldur            x0, [fp, #-0x28]
    // 0x9b3abc: StoreField: r2->field_13 = r0
    //     0x9b3abc: stur            w0, [x2, #0x13]
    // 0x9b3ac0: ldur            x0, [fp, #-0x20]
    // 0x9b3ac4: LoadField: r1 = r0->field_b
    //     0x9b3ac4: ldur            w1, [x0, #0xb]
    // 0x9b3ac8: LoadField: r3 = r0->field_f
    //     0x9b3ac8: ldur            w3, [x0, #0xf]
    // 0x9b3acc: DecompressPointer r3
    //     0x9b3acc: add             x3, x3, HEAP, lsl #32
    // 0x9b3ad0: LoadField: r4 = r3->field_b
    //     0x9b3ad0: ldur            w4, [x3, #0xb]
    // 0x9b3ad4: r3 = LoadInt32Instr(r1)
    //     0x9b3ad4: sbfx            x3, x1, #1, #0x1f
    // 0x9b3ad8: stur            x3, [fp, #-0x30]
    // 0x9b3adc: r1 = LoadInt32Instr(r4)
    //     0x9b3adc: sbfx            x1, x4, #1, #0x1f
    // 0x9b3ae0: cmp             x3, x1
    // 0x9b3ae4: b.ne            #0x9b3af0
    // 0x9b3ae8: mov             x1, x0
    // 0x9b3aec: r0 = _growToNextCapacity()
    //     0x9b3aec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b3af0: ldur            x2, [fp, #-0x20]
    // 0x9b3af4: ldur            x3, [fp, #-0x30]
    // 0x9b3af8: add             x0, x3, #1
    // 0x9b3afc: lsl             x1, x0, #1
    // 0x9b3b00: StoreField: r2->field_b = r1
    //     0x9b3b00: stur            w1, [x2, #0xb]
    // 0x9b3b04: LoadField: r1 = r2->field_f
    //     0x9b3b04: ldur            w1, [x2, #0xf]
    // 0x9b3b08: DecompressPointer r1
    //     0x9b3b08: add             x1, x1, HEAP, lsl #32
    // 0x9b3b0c: ldur            x0, [fp, #-0x38]
    // 0x9b3b10: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b3b10: add             x25, x1, x3, lsl #2
    //     0x9b3b14: add             x25, x25, #0xf
    //     0x9b3b18: str             w0, [x25]
    //     0x9b3b1c: tbz             w0, #0, #0x9b3b38
    //     0x9b3b20: ldurb           w16, [x1, #-1]
    //     0x9b3b24: ldurb           w17, [x0, #-1]
    //     0x9b3b28: and             x16, x17, x16, lsr #2
    //     0x9b3b2c: tst             x16, HEAP, lsr #32
    //     0x9b3b30: b.eq            #0x9b3b38
    //     0x9b3b34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b3b38: b               #0x9b3b40
    // 0x9b3b3c: ldur            x2, [fp, #-0x20]
    // 0x9b3b40: ldur            x0, [fp, #-8]
    // 0x9b3b44: LoadField: r1 = r0->field_f
    //     0x9b3b44: ldur            w1, [x0, #0xf]
    // 0x9b3b48: DecompressPointer r1
    //     0x9b3b48: add             x1, x1, HEAP, lsl #32
    // 0x9b3b4c: r0 = controller()
    //     0x9b3b4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3b50: LoadField: r1 = r0->field_7b
    //     0x9b3b50: ldur            w1, [x0, #0x7b]
    // 0x9b3b54: DecompressPointer r1
    //     0x9b3b54: add             x1, x1, HEAP, lsl #32
    // 0x9b3b58: r0 = value()
    //     0x9b3b58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3b5c: LoadField: r1 = r0->field_b
    //     0x9b3b5c: ldur            w1, [x0, #0xb]
    // 0x9b3b60: DecompressPointer r1
    //     0x9b3b60: add             x1, x1, HEAP, lsl #32
    // 0x9b3b64: cmp             w1, NULL
    // 0x9b3b68: b.eq            #0x9b3b88
    // 0x9b3b6c: LoadField: r0 = r1->field_f
    //     0x9b3b6c: ldur            w0, [x1, #0xf]
    // 0x9b3b70: DecompressPointer r0
    //     0x9b3b70: add             x0, x0, HEAP, lsl #32
    // 0x9b3b74: cmp             w0, NULL
    // 0x9b3b78: b.eq            #0x9b3b88
    // 0x9b3b7c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9b3b7c: ldur            w1, [x0, #0x17]
    // 0x9b3b80: DecompressPointer r1
    //     0x9b3b80: add             x1, x1, HEAP, lsl #32
    // 0x9b3b84: cbz             w1, #0x9b3d40
    // 0x9b3b88: ldur            x0, [fp, #-8]
    // 0x9b3b8c: r1 = Null
    //     0x9b3b8c: mov             x1, NULL
    // 0x9b3b90: r2 = 6
    //     0x9b3b90: movz            x2, #0x6
    // 0x9b3b94: r0 = AllocateArray()
    //     0x9b3b94: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b3b98: stur            x0, [fp, #-0x18]
    // 0x9b3b9c: r16 = "& "
    //     0x9b3b9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f880] "& "
    //     0x9b3ba0: ldr             x16, [x16, #0x880]
    // 0x9b3ba4: StoreField: r0->field_f = r16
    //     0x9b3ba4: stur            w16, [x0, #0xf]
    // 0x9b3ba8: ldur            x2, [fp, #-8]
    // 0x9b3bac: LoadField: r1 = r2->field_f
    //     0x9b3bac: ldur            w1, [x2, #0xf]
    // 0x9b3bb0: DecompressPointer r1
    //     0x9b3bb0: add             x1, x1, HEAP, lsl #32
    // 0x9b3bb4: r0 = controller()
    //     0x9b3bb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3bb8: LoadField: r1 = r0->field_7b
    //     0x9b3bb8: ldur            w1, [x0, #0x7b]
    // 0x9b3bbc: DecompressPointer r1
    //     0x9b3bbc: add             x1, x1, HEAP, lsl #32
    // 0x9b3bc0: r0 = value()
    //     0x9b3bc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3bc4: LoadField: r1 = r0->field_b
    //     0x9b3bc4: ldur            w1, [x0, #0xb]
    // 0x9b3bc8: DecompressPointer r1
    //     0x9b3bc8: add             x1, x1, HEAP, lsl #32
    // 0x9b3bcc: cmp             w1, NULL
    // 0x9b3bd0: b.ne            #0x9b3bdc
    // 0x9b3bd4: r0 = Null
    //     0x9b3bd4: mov             x0, NULL
    // 0x9b3bd8: b               #0x9b3c00
    // 0x9b3bdc: LoadField: r0 = r1->field_f
    //     0x9b3bdc: ldur            w0, [x1, #0xf]
    // 0x9b3be0: DecompressPointer r0
    //     0x9b3be0: add             x0, x0, HEAP, lsl #32
    // 0x9b3be4: cmp             w0, NULL
    // 0x9b3be8: b.ne            #0x9b3bf4
    // 0x9b3bec: r0 = Null
    //     0x9b3bec: mov             x0, NULL
    // 0x9b3bf0: b               #0x9b3c00
    // 0x9b3bf4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9b3bf4: ldur            w1, [x0, #0x17]
    // 0x9b3bf8: DecompressPointer r1
    //     0x9b3bf8: add             x1, x1, HEAP, lsl #32
    // 0x9b3bfc: mov             x0, x1
    // 0x9b3c00: cmp             w0, NULL
    // 0x9b3c04: b.ne            #0x9b3c0c
    // 0x9b3c08: r0 = ""
    //     0x9b3c08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b3c0c: ldur            x3, [fp, #-8]
    // 0x9b3c10: ldur            x2, [fp, #-0x18]
    // 0x9b3c14: ldur            x4, [fp, #-0x20]
    // 0x9b3c18: mov             x1, x2
    // 0x9b3c1c: ArrayStore: r1[1] = r0  ; List_4
    //     0x9b3c1c: add             x25, x1, #0x13
    //     0x9b3c20: str             w0, [x25]
    //     0x9b3c24: tbz             w0, #0, #0x9b3c40
    //     0x9b3c28: ldurb           w16, [x1, #-1]
    //     0x9b3c2c: ldurb           w17, [x0, #-1]
    //     0x9b3c30: and             x16, x17, x16, lsr #2
    //     0x9b3c34: tst             x16, HEAP, lsr #32
    //     0x9b3c38: b.eq            #0x9b3c40
    //     0x9b3c3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b3c40: r16 = " Reviews"
    //     0x9b3c40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f888] " Reviews"
    //     0x9b3c44: ldr             x16, [x16, #0x888]
    // 0x9b3c48: ArrayStore: r2[0] = r16  ; List_4
    //     0x9b3c48: stur            w16, [x2, #0x17]
    // 0x9b3c4c: str             x2, [SP]
    // 0x9b3c50: r0 = _interpolate()
    //     0x9b3c50: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9b3c54: ldur            x2, [fp, #-8]
    // 0x9b3c58: stur            x0, [fp, #-0x18]
    // 0x9b3c5c: LoadField: r1 = r2->field_13
    //     0x9b3c5c: ldur            w1, [x2, #0x13]
    // 0x9b3c60: DecompressPointer r1
    //     0x9b3c60: add             x1, x1, HEAP, lsl #32
    // 0x9b3c64: r0 = of()
    //     0x9b3c64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b3c68: LoadField: r1 = r0->field_87
    //     0x9b3c68: ldur            w1, [x0, #0x87]
    // 0x9b3c6c: DecompressPointer r1
    //     0x9b3c6c: add             x1, x1, HEAP, lsl #32
    // 0x9b3c70: LoadField: r0 = r1->field_2b
    //     0x9b3c70: ldur            w0, [x1, #0x2b]
    // 0x9b3c74: DecompressPointer r0
    //     0x9b3c74: add             x0, x0, HEAP, lsl #32
    // 0x9b3c78: stur            x0, [fp, #-0x28]
    // 0x9b3c7c: r1 = Instance_Color
    //     0x9b3c7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b3c80: d0 = 0.850000
    //     0x9b3c80: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0x9b3c84: ldr             d0, [x17, #0x878]
    // 0x9b3c88: r0 = withOpacity()
    //     0x9b3c88: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b3c8c: r16 = 10.000000
    //     0x9b3c8c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x9b3c90: stp             x0, x16, [SP]
    // 0x9b3c94: ldur            x1, [fp, #-0x28]
    // 0x9b3c98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9b3c98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9b3c9c: ldr             x4, [x4, #0xaa0]
    // 0x9b3ca0: r0 = copyWith()
    //     0x9b3ca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b3ca4: stur            x0, [fp, #-0x28]
    // 0x9b3ca8: r0 = Text()
    //     0x9b3ca8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b3cac: mov             x2, x0
    // 0x9b3cb0: ldur            x0, [fp, #-0x18]
    // 0x9b3cb4: stur            x2, [fp, #-0x38]
    // 0x9b3cb8: StoreField: r2->field_b = r0
    //     0x9b3cb8: stur            w0, [x2, #0xb]
    // 0x9b3cbc: ldur            x0, [fp, #-0x28]
    // 0x9b3cc0: StoreField: r2->field_13 = r0
    //     0x9b3cc0: stur            w0, [x2, #0x13]
    // 0x9b3cc4: ldur            x0, [fp, #-0x20]
    // 0x9b3cc8: LoadField: r1 = r0->field_b
    //     0x9b3cc8: ldur            w1, [x0, #0xb]
    // 0x9b3ccc: LoadField: r3 = r0->field_f
    //     0x9b3ccc: ldur            w3, [x0, #0xf]
    // 0x9b3cd0: DecompressPointer r3
    //     0x9b3cd0: add             x3, x3, HEAP, lsl #32
    // 0x9b3cd4: LoadField: r4 = r3->field_b
    //     0x9b3cd4: ldur            w4, [x3, #0xb]
    // 0x9b3cd8: r3 = LoadInt32Instr(r1)
    //     0x9b3cd8: sbfx            x3, x1, #1, #0x1f
    // 0x9b3cdc: stur            x3, [fp, #-0x30]
    // 0x9b3ce0: r1 = LoadInt32Instr(r4)
    //     0x9b3ce0: sbfx            x1, x4, #1, #0x1f
    // 0x9b3ce4: cmp             x3, x1
    // 0x9b3ce8: b.ne            #0x9b3cf4
    // 0x9b3cec: mov             x1, x0
    // 0x9b3cf0: r0 = _growToNextCapacity()
    //     0x9b3cf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b3cf4: ldur            x2, [fp, #-0x20]
    // 0x9b3cf8: ldur            x3, [fp, #-0x30]
    // 0x9b3cfc: add             x0, x3, #1
    // 0x9b3d00: lsl             x1, x0, #1
    // 0x9b3d04: StoreField: r2->field_b = r1
    //     0x9b3d04: stur            w1, [x2, #0xb]
    // 0x9b3d08: LoadField: r1 = r2->field_f
    //     0x9b3d08: ldur            w1, [x2, #0xf]
    // 0x9b3d0c: DecompressPointer r1
    //     0x9b3d0c: add             x1, x1, HEAP, lsl #32
    // 0x9b3d10: ldur            x0, [fp, #-0x38]
    // 0x9b3d14: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b3d14: add             x25, x1, x3, lsl #2
    //     0x9b3d18: add             x25, x25, #0xf
    //     0x9b3d1c: str             w0, [x25]
    //     0x9b3d20: tbz             w0, #0, #0x9b3d3c
    //     0x9b3d24: ldurb           w16, [x1, #-1]
    //     0x9b3d28: ldurb           w17, [x0, #-1]
    //     0x9b3d2c: and             x16, x17, x16, lsr #2
    //     0x9b3d30: tst             x16, HEAP, lsr #32
    //     0x9b3d34: b.eq            #0x9b3d3c
    //     0x9b3d38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b3d3c: b               #0x9b3d44
    // 0x9b3d40: ldur            x2, [fp, #-0x20]
    // 0x9b3d44: ldur            x0, [fp, #-8]
    // 0x9b3d48: r0 = Column()
    //     0x9b3d48: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b3d4c: mov             x2, x0
    // 0x9b3d50: r0 = Instance_Axis
    //     0x9b3d50: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b3d54: stur            x2, [fp, #-0x28]
    // 0x9b3d58: StoreField: r2->field_f = r0
    //     0x9b3d58: stur            w0, [x2, #0xf]
    // 0x9b3d5c: r3 = Instance_MainAxisAlignment
    //     0x9b3d5c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b3d60: ldr             x3, [x3, #0xa08]
    // 0x9b3d64: StoreField: r2->field_13 = r3
    //     0x9b3d64: stur            w3, [x2, #0x13]
    // 0x9b3d68: r4 = Instance_MainAxisSize
    //     0x9b3d68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b3d6c: ldr             x4, [x4, #0xa10]
    // 0x9b3d70: ArrayStore: r2[0] = r4  ; List_4
    //     0x9b3d70: stur            w4, [x2, #0x17]
    // 0x9b3d74: r5 = Instance_CrossAxisAlignment
    //     0x9b3d74: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9b3d78: ldr             x5, [x5, #0x890]
    // 0x9b3d7c: StoreField: r2->field_1b = r5
    //     0x9b3d7c: stur            w5, [x2, #0x1b]
    // 0x9b3d80: r6 = Instance_VerticalDirection
    //     0x9b3d80: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b3d84: ldr             x6, [x6, #0xa20]
    // 0x9b3d88: StoreField: r2->field_23 = r6
    //     0x9b3d88: stur            w6, [x2, #0x23]
    // 0x9b3d8c: r7 = Instance_Clip
    //     0x9b3d8c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b3d90: ldr             x7, [x7, #0x38]
    // 0x9b3d94: StoreField: r2->field_2b = r7
    //     0x9b3d94: stur            w7, [x2, #0x2b]
    // 0x9b3d98: StoreField: r2->field_2f = rZR
    //     0x9b3d98: stur            xzr, [x2, #0x2f]
    // 0x9b3d9c: ldur            x1, [fp, #-0x20]
    // 0x9b3da0: StoreField: r2->field_b = r1
    //     0x9b3da0: stur            w1, [x2, #0xb]
    // 0x9b3da4: ldur            x8, [fp, #-8]
    // 0x9b3da8: LoadField: r9 = r8->field_f
    //     0x9b3da8: ldur            w9, [x8, #0xf]
    // 0x9b3dac: DecompressPointer r9
    //     0x9b3dac: add             x9, x9, HEAP, lsl #32
    // 0x9b3db0: stur            x9, [fp, #-0x20]
    // 0x9b3db4: LoadField: r10 = r8->field_13
    //     0x9b3db4: ldur            w10, [x8, #0x13]
    // 0x9b3db8: DecompressPointer r10
    //     0x9b3db8: add             x10, x10, HEAP, lsl #32
    // 0x9b3dbc: mov             x1, x9
    // 0x9b3dc0: stur            x10, [fp, #-0x18]
    // 0x9b3dc4: r0 = controller()
    //     0x9b3dc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3dc8: LoadField: r1 = r0->field_7b
    //     0x9b3dc8: ldur            w1, [x0, #0x7b]
    // 0x9b3dcc: DecompressPointer r1
    //     0x9b3dcc: add             x1, x1, HEAP, lsl #32
    // 0x9b3dd0: r0 = value()
    //     0x9b3dd0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3dd4: LoadField: r1 = r0->field_b
    //     0x9b3dd4: ldur            w1, [x0, #0xb]
    // 0x9b3dd8: DecompressPointer r1
    //     0x9b3dd8: add             x1, x1, HEAP, lsl #32
    // 0x9b3ddc: cmp             w1, NULL
    // 0x9b3de0: b.ne            #0x9b3dec
    // 0x9b3de4: r0 = Null
    //     0x9b3de4: mov             x0, NULL
    // 0x9b3de8: b               #0x9b3e24
    // 0x9b3dec: LoadField: r0 = r1->field_f
    //     0x9b3dec: ldur            w0, [x1, #0xf]
    // 0x9b3df0: DecompressPointer r0
    //     0x9b3df0: add             x0, x0, HEAP, lsl #32
    // 0x9b3df4: cmp             w0, NULL
    // 0x9b3df8: b.ne            #0x9b3e04
    // 0x9b3dfc: r0 = Null
    //     0x9b3dfc: mov             x0, NULL
    // 0x9b3e00: b               #0x9b3e24
    // 0x9b3e04: LoadField: r1 = r0->field_1b
    //     0x9b3e04: ldur            w1, [x0, #0x1b]
    // 0x9b3e08: DecompressPointer r1
    //     0x9b3e08: add             x1, x1, HEAP, lsl #32
    // 0x9b3e0c: cmp             w1, NULL
    // 0x9b3e10: b.ne            #0x9b3e1c
    // 0x9b3e14: r0 = Null
    //     0x9b3e14: mov             x0, NULL
    // 0x9b3e18: b               #0x9b3e24
    // 0x9b3e1c: LoadField: r0 = r1->field_7
    //     0x9b3e1c: ldur            w0, [x1, #7]
    // 0x9b3e20: DecompressPointer r0
    //     0x9b3e20: add             x0, x0, HEAP, lsl #32
    // 0x9b3e24: cmp             w0, NULL
    // 0x9b3e28: b.ne            #0x9b3e34
    // 0x9b3e2c: r0 = 0
    //     0x9b3e2c: movz            x0, #0
    // 0x9b3e30: b               #0x9b3e44
    // 0x9b3e34: r1 = LoadInt32Instr(r0)
    //     0x9b3e34: sbfx            x1, x0, #1, #0x1f
    //     0x9b3e38: tbz             w0, #0, #0x9b3e40
    //     0x9b3e3c: ldur            x1, [x0, #7]
    // 0x9b3e40: mov             x0, x1
    // 0x9b3e44: ldur            x2, [fp, #-8]
    // 0x9b3e48: stur            x0, [fp, #-0x30]
    // 0x9b3e4c: LoadField: r1 = r2->field_f
    //     0x9b3e4c: ldur            w1, [x2, #0xf]
    // 0x9b3e50: DecompressPointer r1
    //     0x9b3e50: add             x1, x1, HEAP, lsl #32
    // 0x9b3e54: r0 = controller()
    //     0x9b3e54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3e58: LoadField: r1 = r0->field_7b
    //     0x9b3e58: ldur            w1, [x0, #0x7b]
    // 0x9b3e5c: DecompressPointer r1
    //     0x9b3e5c: add             x1, x1, HEAP, lsl #32
    // 0x9b3e60: r0 = value()
    //     0x9b3e60: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3e64: LoadField: r1 = r0->field_b
    //     0x9b3e64: ldur            w1, [x0, #0xb]
    // 0x9b3e68: DecompressPointer r1
    //     0x9b3e68: add             x1, x1, HEAP, lsl #32
    // 0x9b3e6c: cmp             w1, NULL
    // 0x9b3e70: b.ne            #0x9b3e7c
    // 0x9b3e74: r0 = Null
    //     0x9b3e74: mov             x0, NULL
    // 0x9b3e78: b               #0x9b3eb4
    // 0x9b3e7c: LoadField: r0 = r1->field_f
    //     0x9b3e7c: ldur            w0, [x1, #0xf]
    // 0x9b3e80: DecompressPointer r0
    //     0x9b3e80: add             x0, x0, HEAP, lsl #32
    // 0x9b3e84: cmp             w0, NULL
    // 0x9b3e88: b.ne            #0x9b3e94
    // 0x9b3e8c: r0 = Null
    //     0x9b3e8c: mov             x0, NULL
    // 0x9b3e90: b               #0x9b3eb4
    // 0x9b3e94: LoadField: r1 = r0->field_1b
    //     0x9b3e94: ldur            w1, [x0, #0x1b]
    // 0x9b3e98: DecompressPointer r1
    //     0x9b3e98: add             x1, x1, HEAP, lsl #32
    // 0x9b3e9c: cmp             w1, NULL
    // 0x9b3ea0: b.ne            #0x9b3eac
    // 0x9b3ea4: r0 = Null
    //     0x9b3ea4: mov             x0, NULL
    // 0x9b3ea8: b               #0x9b3eb4
    // 0x9b3eac: LoadField: r0 = r1->field_7
    //     0x9b3eac: ldur            w0, [x1, #7]
    // 0x9b3eb0: DecompressPointer r0
    //     0x9b3eb0: add             x0, x0, HEAP, lsl #32
    // 0x9b3eb4: cmp             w0, NULL
    // 0x9b3eb8: b.ne            #0x9b3ec4
    // 0x9b3ebc: r0 = 0
    //     0x9b3ebc: movz            x0, #0
    // 0x9b3ec0: b               #0x9b3ed4
    // 0x9b3ec4: r1 = LoadInt32Instr(r0)
    //     0x9b3ec4: sbfx            x1, x0, #1, #0x1f
    //     0x9b3ec8: tbz             w0, #0, #0x9b3ed0
    //     0x9b3ecc: ldur            x1, [x0, #7]
    // 0x9b3ed0: mov             x0, x1
    // 0x9b3ed4: ldur            x2, [fp, #-8]
    // 0x9b3ed8: stur            x0, [fp, #-0x40]
    // 0x9b3edc: LoadField: r1 = r2->field_f
    //     0x9b3edc: ldur            w1, [x2, #0xf]
    // 0x9b3ee0: DecompressPointer r1
    //     0x9b3ee0: add             x1, x1, HEAP, lsl #32
    // 0x9b3ee4: r0 = controller()
    //     0x9b3ee4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3ee8: LoadField: r1 = r0->field_7b
    //     0x9b3ee8: ldur            w1, [x0, #0x7b]
    // 0x9b3eec: DecompressPointer r1
    //     0x9b3eec: add             x1, x1, HEAP, lsl #32
    // 0x9b3ef0: r0 = value()
    //     0x9b3ef0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3ef4: LoadField: r1 = r0->field_b
    //     0x9b3ef4: ldur            w1, [x0, #0xb]
    // 0x9b3ef8: DecompressPointer r1
    //     0x9b3ef8: add             x1, x1, HEAP, lsl #32
    // 0x9b3efc: cmp             w1, NULL
    // 0x9b3f00: b.ne            #0x9b3f0c
    // 0x9b3f04: r0 = Null
    //     0x9b3f04: mov             x0, NULL
    // 0x9b3f08: b               #0x9b3f44
    // 0x9b3f0c: LoadField: r0 = r1->field_f
    //     0x9b3f0c: ldur            w0, [x1, #0xf]
    // 0x9b3f10: DecompressPointer r0
    //     0x9b3f10: add             x0, x0, HEAP, lsl #32
    // 0x9b3f14: cmp             w0, NULL
    // 0x9b3f18: b.ne            #0x9b3f24
    // 0x9b3f1c: r0 = Null
    //     0x9b3f1c: mov             x0, NULL
    // 0x9b3f20: b               #0x9b3f44
    // 0x9b3f24: LoadField: r1 = r0->field_1b
    //     0x9b3f24: ldur            w1, [x0, #0x1b]
    // 0x9b3f28: DecompressPointer r1
    //     0x9b3f28: add             x1, x1, HEAP, lsl #32
    // 0x9b3f2c: cmp             w1, NULL
    // 0x9b3f30: b.ne            #0x9b3f3c
    // 0x9b3f34: r0 = Null
    //     0x9b3f34: mov             x0, NULL
    // 0x9b3f38: b               #0x9b3f44
    // 0x9b3f3c: LoadField: r0 = r1->field_b
    //     0x9b3f3c: ldur            w0, [x1, #0xb]
    // 0x9b3f40: DecompressPointer r0
    //     0x9b3f40: add             x0, x0, HEAP, lsl #32
    // 0x9b3f44: cmp             w0, NULL
    // 0x9b3f48: b.ne            #0x9b3f54
    // 0x9b3f4c: r1 = 0
    //     0x9b3f4c: movz            x1, #0
    // 0x9b3f50: b               #0x9b3f60
    // 0x9b3f54: r1 = LoadInt32Instr(r0)
    //     0x9b3f54: sbfx            x1, x0, #1, #0x1f
    //     0x9b3f58: tbz             w0, #0, #0x9b3f60
    //     0x9b3f5c: ldur            x1, [x0, #7]
    // 0x9b3f60: ldur            x2, [fp, #-8]
    // 0x9b3f64: ldur            x0, [fp, #-0x40]
    // 0x9b3f68: add             x3, x0, x1
    // 0x9b3f6c: stur            x3, [fp, #-0x48]
    // 0x9b3f70: LoadField: r1 = r2->field_f
    //     0x9b3f70: ldur            w1, [x2, #0xf]
    // 0x9b3f74: DecompressPointer r1
    //     0x9b3f74: add             x1, x1, HEAP, lsl #32
    // 0x9b3f78: r0 = controller()
    //     0x9b3f78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b3f7c: LoadField: r1 = r0->field_7b
    //     0x9b3f7c: ldur            w1, [x0, #0x7b]
    // 0x9b3f80: DecompressPointer r1
    //     0x9b3f80: add             x1, x1, HEAP, lsl #32
    // 0x9b3f84: r0 = value()
    //     0x9b3f84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b3f88: LoadField: r1 = r0->field_b
    //     0x9b3f88: ldur            w1, [x0, #0xb]
    // 0x9b3f8c: DecompressPointer r1
    //     0x9b3f8c: add             x1, x1, HEAP, lsl #32
    // 0x9b3f90: cmp             w1, NULL
    // 0x9b3f94: b.ne            #0x9b3fa0
    // 0x9b3f98: r0 = Null
    //     0x9b3f98: mov             x0, NULL
    // 0x9b3f9c: b               #0x9b3fd8
    // 0x9b3fa0: LoadField: r0 = r1->field_f
    //     0x9b3fa0: ldur            w0, [x1, #0xf]
    // 0x9b3fa4: DecompressPointer r0
    //     0x9b3fa4: add             x0, x0, HEAP, lsl #32
    // 0x9b3fa8: cmp             w0, NULL
    // 0x9b3fac: b.ne            #0x9b3fb8
    // 0x9b3fb0: r0 = Null
    //     0x9b3fb0: mov             x0, NULL
    // 0x9b3fb4: b               #0x9b3fd8
    // 0x9b3fb8: LoadField: r1 = r0->field_1b
    //     0x9b3fb8: ldur            w1, [x0, #0x1b]
    // 0x9b3fbc: DecompressPointer r1
    //     0x9b3fbc: add             x1, x1, HEAP, lsl #32
    // 0x9b3fc0: cmp             w1, NULL
    // 0x9b3fc4: b.ne            #0x9b3fd0
    // 0x9b3fc8: r0 = Null
    //     0x9b3fc8: mov             x0, NULL
    // 0x9b3fcc: b               #0x9b3fd8
    // 0x9b3fd0: LoadField: r0 = r1->field_f
    //     0x9b3fd0: ldur            w0, [x1, #0xf]
    // 0x9b3fd4: DecompressPointer r0
    //     0x9b3fd4: add             x0, x0, HEAP, lsl #32
    // 0x9b3fd8: cmp             w0, NULL
    // 0x9b3fdc: b.ne            #0x9b3fe8
    // 0x9b3fe0: r1 = 0
    //     0x9b3fe0: movz            x1, #0
    // 0x9b3fe4: b               #0x9b3ff4
    // 0x9b3fe8: r1 = LoadInt32Instr(r0)
    //     0x9b3fe8: sbfx            x1, x0, #1, #0x1f
    //     0x9b3fec: tbz             w0, #0, #0x9b3ff4
    //     0x9b3ff0: ldur            x1, [x0, #7]
    // 0x9b3ff4: ldur            x2, [fp, #-8]
    // 0x9b3ff8: ldur            x0, [fp, #-0x48]
    // 0x9b3ffc: add             x3, x0, x1
    // 0x9b4000: stur            x3, [fp, #-0x40]
    // 0x9b4004: LoadField: r1 = r2->field_f
    //     0x9b4004: ldur            w1, [x2, #0xf]
    // 0x9b4008: DecompressPointer r1
    //     0x9b4008: add             x1, x1, HEAP, lsl #32
    // 0x9b400c: r0 = controller()
    //     0x9b400c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4010: LoadField: r1 = r0->field_7b
    //     0x9b4010: ldur            w1, [x0, #0x7b]
    // 0x9b4014: DecompressPointer r1
    //     0x9b4014: add             x1, x1, HEAP, lsl #32
    // 0x9b4018: r0 = value()
    //     0x9b4018: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b401c: LoadField: r1 = r0->field_b
    //     0x9b401c: ldur            w1, [x0, #0xb]
    // 0x9b4020: DecompressPointer r1
    //     0x9b4020: add             x1, x1, HEAP, lsl #32
    // 0x9b4024: cmp             w1, NULL
    // 0x9b4028: b.ne            #0x9b4034
    // 0x9b402c: r0 = Null
    //     0x9b402c: mov             x0, NULL
    // 0x9b4030: b               #0x9b406c
    // 0x9b4034: LoadField: r0 = r1->field_f
    //     0x9b4034: ldur            w0, [x1, #0xf]
    // 0x9b4038: DecompressPointer r0
    //     0x9b4038: add             x0, x0, HEAP, lsl #32
    // 0x9b403c: cmp             w0, NULL
    // 0x9b4040: b.ne            #0x9b404c
    // 0x9b4044: r0 = Null
    //     0x9b4044: mov             x0, NULL
    // 0x9b4048: b               #0x9b406c
    // 0x9b404c: LoadField: r1 = r0->field_1b
    //     0x9b404c: ldur            w1, [x0, #0x1b]
    // 0x9b4050: DecompressPointer r1
    //     0x9b4050: add             x1, x1, HEAP, lsl #32
    // 0x9b4054: cmp             w1, NULL
    // 0x9b4058: b.ne            #0x9b4064
    // 0x9b405c: r0 = Null
    //     0x9b405c: mov             x0, NULL
    // 0x9b4060: b               #0x9b406c
    // 0x9b4064: LoadField: r0 = r1->field_13
    //     0x9b4064: ldur            w0, [x1, #0x13]
    // 0x9b4068: DecompressPointer r0
    //     0x9b4068: add             x0, x0, HEAP, lsl #32
    // 0x9b406c: cmp             w0, NULL
    // 0x9b4070: b.ne            #0x9b407c
    // 0x9b4074: r1 = 0
    //     0x9b4074: movz            x1, #0
    // 0x9b4078: b               #0x9b4088
    // 0x9b407c: r1 = LoadInt32Instr(r0)
    //     0x9b407c: sbfx            x1, x0, #1, #0x1f
    //     0x9b4080: tbz             w0, #0, #0x9b4088
    //     0x9b4084: ldur            x1, [x0, #7]
    // 0x9b4088: ldur            x2, [fp, #-8]
    // 0x9b408c: ldur            x0, [fp, #-0x40]
    // 0x9b4090: add             x3, x0, x1
    // 0x9b4094: stur            x3, [fp, #-0x48]
    // 0x9b4098: LoadField: r1 = r2->field_f
    //     0x9b4098: ldur            w1, [x2, #0xf]
    // 0x9b409c: DecompressPointer r1
    //     0x9b409c: add             x1, x1, HEAP, lsl #32
    // 0x9b40a0: r0 = controller()
    //     0x9b40a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b40a4: LoadField: r1 = r0->field_7b
    //     0x9b40a4: ldur            w1, [x0, #0x7b]
    // 0x9b40a8: DecompressPointer r1
    //     0x9b40a8: add             x1, x1, HEAP, lsl #32
    // 0x9b40ac: r0 = value()
    //     0x9b40ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b40b0: LoadField: r1 = r0->field_b
    //     0x9b40b0: ldur            w1, [x0, #0xb]
    // 0x9b40b4: DecompressPointer r1
    //     0x9b40b4: add             x1, x1, HEAP, lsl #32
    // 0x9b40b8: cmp             w1, NULL
    // 0x9b40bc: b.ne            #0x9b40c8
    // 0x9b40c0: r0 = Null
    //     0x9b40c0: mov             x0, NULL
    // 0x9b40c4: b               #0x9b4100
    // 0x9b40c8: LoadField: r0 = r1->field_f
    //     0x9b40c8: ldur            w0, [x1, #0xf]
    // 0x9b40cc: DecompressPointer r0
    //     0x9b40cc: add             x0, x0, HEAP, lsl #32
    // 0x9b40d0: cmp             w0, NULL
    // 0x9b40d4: b.ne            #0x9b40e0
    // 0x9b40d8: r0 = Null
    //     0x9b40d8: mov             x0, NULL
    // 0x9b40dc: b               #0x9b4100
    // 0x9b40e0: LoadField: r1 = r0->field_1b
    //     0x9b40e0: ldur            w1, [x0, #0x1b]
    // 0x9b40e4: DecompressPointer r1
    //     0x9b40e4: add             x1, x1, HEAP, lsl #32
    // 0x9b40e8: cmp             w1, NULL
    // 0x9b40ec: b.ne            #0x9b40f8
    // 0x9b40f0: r0 = Null
    //     0x9b40f0: mov             x0, NULL
    // 0x9b40f4: b               #0x9b4100
    // 0x9b40f8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b40f8: ldur            w0, [x1, #0x17]
    // 0x9b40fc: DecompressPointer r0
    //     0x9b40fc: add             x0, x0, HEAP, lsl #32
    // 0x9b4100: cmp             w0, NULL
    // 0x9b4104: b.ne            #0x9b4110
    // 0x9b4108: r3 = 0
    //     0x9b4108: movz            x3, #0
    // 0x9b410c: b               #0x9b4120
    // 0x9b4110: r1 = LoadInt32Instr(r0)
    //     0x9b4110: sbfx            x1, x0, #1, #0x1f
    //     0x9b4114: tbz             w0, #0, #0x9b411c
    //     0x9b4118: ldur            x1, [x0, #7]
    // 0x9b411c: mov             x3, x1
    // 0x9b4120: ldur            x2, [fp, #-8]
    // 0x9b4124: ldur            x1, [fp, #-0x30]
    // 0x9b4128: ldur            x0, [fp, #-0x48]
    // 0x9b412c: add             x4, x0, x3
    // 0x9b4130: scvtf           d0, x1
    // 0x9b4134: scvtf           d1, x4
    // 0x9b4138: fdiv            d2, d0, d1
    // 0x9b413c: stur            d2, [fp, #-0x68]
    // 0x9b4140: LoadField: r1 = r2->field_f
    //     0x9b4140: ldur            w1, [x2, #0xf]
    // 0x9b4144: DecompressPointer r1
    //     0x9b4144: add             x1, x1, HEAP, lsl #32
    // 0x9b4148: r0 = controller()
    //     0x9b4148: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b414c: LoadField: r1 = r0->field_7b
    //     0x9b414c: ldur            w1, [x0, #0x7b]
    // 0x9b4150: DecompressPointer r1
    //     0x9b4150: add             x1, x1, HEAP, lsl #32
    // 0x9b4154: r0 = value()
    //     0x9b4154: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4158: LoadField: r1 = r0->field_b
    //     0x9b4158: ldur            w1, [x0, #0xb]
    // 0x9b415c: DecompressPointer r1
    //     0x9b415c: add             x1, x1, HEAP, lsl #32
    // 0x9b4160: cmp             w1, NULL
    // 0x9b4164: b.ne            #0x9b4170
    // 0x9b4168: r0 = Null
    //     0x9b4168: mov             x0, NULL
    // 0x9b416c: b               #0x9b41a8
    // 0x9b4170: LoadField: r0 = r1->field_f
    //     0x9b4170: ldur            w0, [x1, #0xf]
    // 0x9b4174: DecompressPointer r0
    //     0x9b4174: add             x0, x0, HEAP, lsl #32
    // 0x9b4178: cmp             w0, NULL
    // 0x9b417c: b.ne            #0x9b4188
    // 0x9b4180: r0 = Null
    //     0x9b4180: mov             x0, NULL
    // 0x9b4184: b               #0x9b41a8
    // 0x9b4188: LoadField: r1 = r0->field_1b
    //     0x9b4188: ldur            w1, [x0, #0x1b]
    // 0x9b418c: DecompressPointer r1
    //     0x9b418c: add             x1, x1, HEAP, lsl #32
    // 0x9b4190: cmp             w1, NULL
    // 0x9b4194: b.ne            #0x9b41a0
    // 0x9b4198: r0 = Null
    //     0x9b4198: mov             x0, NULL
    // 0x9b419c: b               #0x9b41a8
    // 0x9b41a0: LoadField: r0 = r1->field_7
    //     0x9b41a0: ldur            w0, [x1, #7]
    // 0x9b41a4: DecompressPointer r0
    //     0x9b41a4: add             x0, x0, HEAP, lsl #32
    // 0x9b41a8: cmp             w0, NULL
    // 0x9b41ac: b.ne            #0x9b41b8
    // 0x9b41b0: r5 = 0
    //     0x9b41b0: movz            x5, #0
    // 0x9b41b4: b               #0x9b41c8
    // 0x9b41b8: r1 = LoadInt32Instr(r0)
    //     0x9b41b8: sbfx            x1, x0, #1, #0x1f
    //     0x9b41bc: tbz             w0, #0, #0x9b41c4
    //     0x9b41c0: ldur            x1, [x0, #7]
    // 0x9b41c4: mov             x5, x1
    // 0x9b41c8: ldur            x0, [fp, #-8]
    // 0x9b41cc: ldur            x1, [fp, #-0x20]
    // 0x9b41d0: ldur            x2, [fp, #-0x18]
    // 0x9b41d4: ldur            d0, [fp, #-0x68]
    // 0x9b41d8: r3 = "5"
    //     0x9b41d8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f898] "5"
    //     0x9b41dc: ldr             x3, [x3, #0x898]
    // 0x9b41e0: r0 = chartRow()
    //     0x9b41e0: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x9b41e4: ldur            x2, [fp, #-8]
    // 0x9b41e8: stur            x0, [fp, #-0x38]
    // 0x9b41ec: LoadField: r3 = r2->field_f
    //     0x9b41ec: ldur            w3, [x2, #0xf]
    // 0x9b41f0: DecompressPointer r3
    //     0x9b41f0: add             x3, x3, HEAP, lsl #32
    // 0x9b41f4: stur            x3, [fp, #-0x20]
    // 0x9b41f8: LoadField: r4 = r2->field_13
    //     0x9b41f8: ldur            w4, [x2, #0x13]
    // 0x9b41fc: DecompressPointer r4
    //     0x9b41fc: add             x4, x4, HEAP, lsl #32
    // 0x9b4200: mov             x1, x3
    // 0x9b4204: stur            x4, [fp, #-0x18]
    // 0x9b4208: r0 = controller()
    //     0x9b4208: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b420c: LoadField: r1 = r0->field_7b
    //     0x9b420c: ldur            w1, [x0, #0x7b]
    // 0x9b4210: DecompressPointer r1
    //     0x9b4210: add             x1, x1, HEAP, lsl #32
    // 0x9b4214: r0 = value()
    //     0x9b4214: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4218: LoadField: r1 = r0->field_b
    //     0x9b4218: ldur            w1, [x0, #0xb]
    // 0x9b421c: DecompressPointer r1
    //     0x9b421c: add             x1, x1, HEAP, lsl #32
    // 0x9b4220: cmp             w1, NULL
    // 0x9b4224: b.ne            #0x9b4230
    // 0x9b4228: r0 = Null
    //     0x9b4228: mov             x0, NULL
    // 0x9b422c: b               #0x9b4268
    // 0x9b4230: LoadField: r0 = r1->field_f
    //     0x9b4230: ldur            w0, [x1, #0xf]
    // 0x9b4234: DecompressPointer r0
    //     0x9b4234: add             x0, x0, HEAP, lsl #32
    // 0x9b4238: cmp             w0, NULL
    // 0x9b423c: b.ne            #0x9b4248
    // 0x9b4240: r0 = Null
    //     0x9b4240: mov             x0, NULL
    // 0x9b4244: b               #0x9b4268
    // 0x9b4248: LoadField: r1 = r0->field_1b
    //     0x9b4248: ldur            w1, [x0, #0x1b]
    // 0x9b424c: DecompressPointer r1
    //     0x9b424c: add             x1, x1, HEAP, lsl #32
    // 0x9b4250: cmp             w1, NULL
    // 0x9b4254: b.ne            #0x9b4260
    // 0x9b4258: r0 = Null
    //     0x9b4258: mov             x0, NULL
    // 0x9b425c: b               #0x9b4268
    // 0x9b4260: LoadField: r0 = r1->field_b
    //     0x9b4260: ldur            w0, [x1, #0xb]
    // 0x9b4264: DecompressPointer r0
    //     0x9b4264: add             x0, x0, HEAP, lsl #32
    // 0x9b4268: cmp             w0, NULL
    // 0x9b426c: b.ne            #0x9b4278
    // 0x9b4270: r0 = 0
    //     0x9b4270: movz            x0, #0
    // 0x9b4274: b               #0x9b4288
    // 0x9b4278: r1 = LoadInt32Instr(r0)
    //     0x9b4278: sbfx            x1, x0, #1, #0x1f
    //     0x9b427c: tbz             w0, #0, #0x9b4284
    //     0x9b4280: ldur            x1, [x0, #7]
    // 0x9b4284: mov             x0, x1
    // 0x9b4288: ldur            x2, [fp, #-8]
    // 0x9b428c: stur            x0, [fp, #-0x30]
    // 0x9b4290: LoadField: r1 = r2->field_f
    //     0x9b4290: ldur            w1, [x2, #0xf]
    // 0x9b4294: DecompressPointer r1
    //     0x9b4294: add             x1, x1, HEAP, lsl #32
    // 0x9b4298: r0 = controller()
    //     0x9b4298: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b429c: LoadField: r1 = r0->field_7b
    //     0x9b429c: ldur            w1, [x0, #0x7b]
    // 0x9b42a0: DecompressPointer r1
    //     0x9b42a0: add             x1, x1, HEAP, lsl #32
    // 0x9b42a4: r0 = value()
    //     0x9b42a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b42a8: LoadField: r1 = r0->field_b
    //     0x9b42a8: ldur            w1, [x0, #0xb]
    // 0x9b42ac: DecompressPointer r1
    //     0x9b42ac: add             x1, x1, HEAP, lsl #32
    // 0x9b42b0: cmp             w1, NULL
    // 0x9b42b4: b.ne            #0x9b42c0
    // 0x9b42b8: r0 = Null
    //     0x9b42b8: mov             x0, NULL
    // 0x9b42bc: b               #0x9b42f8
    // 0x9b42c0: LoadField: r0 = r1->field_f
    //     0x9b42c0: ldur            w0, [x1, #0xf]
    // 0x9b42c4: DecompressPointer r0
    //     0x9b42c4: add             x0, x0, HEAP, lsl #32
    // 0x9b42c8: cmp             w0, NULL
    // 0x9b42cc: b.ne            #0x9b42d8
    // 0x9b42d0: r0 = Null
    //     0x9b42d0: mov             x0, NULL
    // 0x9b42d4: b               #0x9b42f8
    // 0x9b42d8: LoadField: r1 = r0->field_1b
    //     0x9b42d8: ldur            w1, [x0, #0x1b]
    // 0x9b42dc: DecompressPointer r1
    //     0x9b42dc: add             x1, x1, HEAP, lsl #32
    // 0x9b42e0: cmp             w1, NULL
    // 0x9b42e4: b.ne            #0x9b42f0
    // 0x9b42e8: r0 = Null
    //     0x9b42e8: mov             x0, NULL
    // 0x9b42ec: b               #0x9b42f8
    // 0x9b42f0: LoadField: r0 = r1->field_7
    //     0x9b42f0: ldur            w0, [x1, #7]
    // 0x9b42f4: DecompressPointer r0
    //     0x9b42f4: add             x0, x0, HEAP, lsl #32
    // 0x9b42f8: cmp             w0, NULL
    // 0x9b42fc: b.ne            #0x9b4308
    // 0x9b4300: r0 = 0
    //     0x9b4300: movz            x0, #0
    // 0x9b4304: b               #0x9b4318
    // 0x9b4308: r1 = LoadInt32Instr(r0)
    //     0x9b4308: sbfx            x1, x0, #1, #0x1f
    //     0x9b430c: tbz             w0, #0, #0x9b4314
    //     0x9b4310: ldur            x1, [x0, #7]
    // 0x9b4314: mov             x0, x1
    // 0x9b4318: ldur            x2, [fp, #-8]
    // 0x9b431c: stur            x0, [fp, #-0x40]
    // 0x9b4320: LoadField: r1 = r2->field_f
    //     0x9b4320: ldur            w1, [x2, #0xf]
    // 0x9b4324: DecompressPointer r1
    //     0x9b4324: add             x1, x1, HEAP, lsl #32
    // 0x9b4328: r0 = controller()
    //     0x9b4328: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b432c: LoadField: r1 = r0->field_7b
    //     0x9b432c: ldur            w1, [x0, #0x7b]
    // 0x9b4330: DecompressPointer r1
    //     0x9b4330: add             x1, x1, HEAP, lsl #32
    // 0x9b4334: r0 = value()
    //     0x9b4334: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4338: LoadField: r1 = r0->field_b
    //     0x9b4338: ldur            w1, [x0, #0xb]
    // 0x9b433c: DecompressPointer r1
    //     0x9b433c: add             x1, x1, HEAP, lsl #32
    // 0x9b4340: cmp             w1, NULL
    // 0x9b4344: b.ne            #0x9b4350
    // 0x9b4348: r0 = Null
    //     0x9b4348: mov             x0, NULL
    // 0x9b434c: b               #0x9b4388
    // 0x9b4350: LoadField: r0 = r1->field_f
    //     0x9b4350: ldur            w0, [x1, #0xf]
    // 0x9b4354: DecompressPointer r0
    //     0x9b4354: add             x0, x0, HEAP, lsl #32
    // 0x9b4358: cmp             w0, NULL
    // 0x9b435c: b.ne            #0x9b4368
    // 0x9b4360: r0 = Null
    //     0x9b4360: mov             x0, NULL
    // 0x9b4364: b               #0x9b4388
    // 0x9b4368: LoadField: r1 = r0->field_1b
    //     0x9b4368: ldur            w1, [x0, #0x1b]
    // 0x9b436c: DecompressPointer r1
    //     0x9b436c: add             x1, x1, HEAP, lsl #32
    // 0x9b4370: cmp             w1, NULL
    // 0x9b4374: b.ne            #0x9b4380
    // 0x9b4378: r0 = Null
    //     0x9b4378: mov             x0, NULL
    // 0x9b437c: b               #0x9b4388
    // 0x9b4380: LoadField: r0 = r1->field_b
    //     0x9b4380: ldur            w0, [x1, #0xb]
    // 0x9b4384: DecompressPointer r0
    //     0x9b4384: add             x0, x0, HEAP, lsl #32
    // 0x9b4388: cmp             w0, NULL
    // 0x9b438c: b.ne            #0x9b4398
    // 0x9b4390: r1 = 0
    //     0x9b4390: movz            x1, #0
    // 0x9b4394: b               #0x9b43a4
    // 0x9b4398: r1 = LoadInt32Instr(r0)
    //     0x9b4398: sbfx            x1, x0, #1, #0x1f
    //     0x9b439c: tbz             w0, #0, #0x9b43a4
    //     0x9b43a0: ldur            x1, [x0, #7]
    // 0x9b43a4: ldur            x2, [fp, #-8]
    // 0x9b43a8: ldur            x0, [fp, #-0x40]
    // 0x9b43ac: add             x3, x0, x1
    // 0x9b43b0: stur            x3, [fp, #-0x48]
    // 0x9b43b4: LoadField: r1 = r2->field_f
    //     0x9b43b4: ldur            w1, [x2, #0xf]
    // 0x9b43b8: DecompressPointer r1
    //     0x9b43b8: add             x1, x1, HEAP, lsl #32
    // 0x9b43bc: r0 = controller()
    //     0x9b43bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b43c0: LoadField: r1 = r0->field_7b
    //     0x9b43c0: ldur            w1, [x0, #0x7b]
    // 0x9b43c4: DecompressPointer r1
    //     0x9b43c4: add             x1, x1, HEAP, lsl #32
    // 0x9b43c8: r0 = value()
    //     0x9b43c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b43cc: LoadField: r1 = r0->field_b
    //     0x9b43cc: ldur            w1, [x0, #0xb]
    // 0x9b43d0: DecompressPointer r1
    //     0x9b43d0: add             x1, x1, HEAP, lsl #32
    // 0x9b43d4: cmp             w1, NULL
    // 0x9b43d8: b.ne            #0x9b43e4
    // 0x9b43dc: r0 = Null
    //     0x9b43dc: mov             x0, NULL
    // 0x9b43e0: b               #0x9b441c
    // 0x9b43e4: LoadField: r0 = r1->field_f
    //     0x9b43e4: ldur            w0, [x1, #0xf]
    // 0x9b43e8: DecompressPointer r0
    //     0x9b43e8: add             x0, x0, HEAP, lsl #32
    // 0x9b43ec: cmp             w0, NULL
    // 0x9b43f0: b.ne            #0x9b43fc
    // 0x9b43f4: r0 = Null
    //     0x9b43f4: mov             x0, NULL
    // 0x9b43f8: b               #0x9b441c
    // 0x9b43fc: LoadField: r1 = r0->field_1b
    //     0x9b43fc: ldur            w1, [x0, #0x1b]
    // 0x9b4400: DecompressPointer r1
    //     0x9b4400: add             x1, x1, HEAP, lsl #32
    // 0x9b4404: cmp             w1, NULL
    // 0x9b4408: b.ne            #0x9b4414
    // 0x9b440c: r0 = Null
    //     0x9b440c: mov             x0, NULL
    // 0x9b4410: b               #0x9b441c
    // 0x9b4414: LoadField: r0 = r1->field_f
    //     0x9b4414: ldur            w0, [x1, #0xf]
    // 0x9b4418: DecompressPointer r0
    //     0x9b4418: add             x0, x0, HEAP, lsl #32
    // 0x9b441c: cmp             w0, NULL
    // 0x9b4420: b.ne            #0x9b442c
    // 0x9b4424: r1 = 0
    //     0x9b4424: movz            x1, #0
    // 0x9b4428: b               #0x9b4438
    // 0x9b442c: r1 = LoadInt32Instr(r0)
    //     0x9b442c: sbfx            x1, x0, #1, #0x1f
    //     0x9b4430: tbz             w0, #0, #0x9b4438
    //     0x9b4434: ldur            x1, [x0, #7]
    // 0x9b4438: ldur            x2, [fp, #-8]
    // 0x9b443c: ldur            x0, [fp, #-0x48]
    // 0x9b4440: add             x3, x0, x1
    // 0x9b4444: stur            x3, [fp, #-0x40]
    // 0x9b4448: LoadField: r1 = r2->field_f
    //     0x9b4448: ldur            w1, [x2, #0xf]
    // 0x9b444c: DecompressPointer r1
    //     0x9b444c: add             x1, x1, HEAP, lsl #32
    // 0x9b4450: r0 = controller()
    //     0x9b4450: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4454: LoadField: r1 = r0->field_7b
    //     0x9b4454: ldur            w1, [x0, #0x7b]
    // 0x9b4458: DecompressPointer r1
    //     0x9b4458: add             x1, x1, HEAP, lsl #32
    // 0x9b445c: r0 = value()
    //     0x9b445c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4460: LoadField: r1 = r0->field_b
    //     0x9b4460: ldur            w1, [x0, #0xb]
    // 0x9b4464: DecompressPointer r1
    //     0x9b4464: add             x1, x1, HEAP, lsl #32
    // 0x9b4468: cmp             w1, NULL
    // 0x9b446c: b.ne            #0x9b4478
    // 0x9b4470: r0 = Null
    //     0x9b4470: mov             x0, NULL
    // 0x9b4474: b               #0x9b44b0
    // 0x9b4478: LoadField: r0 = r1->field_f
    //     0x9b4478: ldur            w0, [x1, #0xf]
    // 0x9b447c: DecompressPointer r0
    //     0x9b447c: add             x0, x0, HEAP, lsl #32
    // 0x9b4480: cmp             w0, NULL
    // 0x9b4484: b.ne            #0x9b4490
    // 0x9b4488: r0 = Null
    //     0x9b4488: mov             x0, NULL
    // 0x9b448c: b               #0x9b44b0
    // 0x9b4490: LoadField: r1 = r0->field_1b
    //     0x9b4490: ldur            w1, [x0, #0x1b]
    // 0x9b4494: DecompressPointer r1
    //     0x9b4494: add             x1, x1, HEAP, lsl #32
    // 0x9b4498: cmp             w1, NULL
    // 0x9b449c: b.ne            #0x9b44a8
    // 0x9b44a0: r0 = Null
    //     0x9b44a0: mov             x0, NULL
    // 0x9b44a4: b               #0x9b44b0
    // 0x9b44a8: LoadField: r0 = r1->field_13
    //     0x9b44a8: ldur            w0, [x1, #0x13]
    // 0x9b44ac: DecompressPointer r0
    //     0x9b44ac: add             x0, x0, HEAP, lsl #32
    // 0x9b44b0: cmp             w0, NULL
    // 0x9b44b4: b.ne            #0x9b44c0
    // 0x9b44b8: r1 = 0
    //     0x9b44b8: movz            x1, #0
    // 0x9b44bc: b               #0x9b44cc
    // 0x9b44c0: r1 = LoadInt32Instr(r0)
    //     0x9b44c0: sbfx            x1, x0, #1, #0x1f
    //     0x9b44c4: tbz             w0, #0, #0x9b44cc
    //     0x9b44c8: ldur            x1, [x0, #7]
    // 0x9b44cc: ldur            x2, [fp, #-8]
    // 0x9b44d0: ldur            x0, [fp, #-0x40]
    // 0x9b44d4: add             x3, x0, x1
    // 0x9b44d8: stur            x3, [fp, #-0x48]
    // 0x9b44dc: LoadField: r1 = r2->field_f
    //     0x9b44dc: ldur            w1, [x2, #0xf]
    // 0x9b44e0: DecompressPointer r1
    //     0x9b44e0: add             x1, x1, HEAP, lsl #32
    // 0x9b44e4: r0 = controller()
    //     0x9b44e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b44e8: LoadField: r1 = r0->field_7b
    //     0x9b44e8: ldur            w1, [x0, #0x7b]
    // 0x9b44ec: DecompressPointer r1
    //     0x9b44ec: add             x1, x1, HEAP, lsl #32
    // 0x9b44f0: r0 = value()
    //     0x9b44f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b44f4: LoadField: r1 = r0->field_b
    //     0x9b44f4: ldur            w1, [x0, #0xb]
    // 0x9b44f8: DecompressPointer r1
    //     0x9b44f8: add             x1, x1, HEAP, lsl #32
    // 0x9b44fc: cmp             w1, NULL
    // 0x9b4500: b.ne            #0x9b450c
    // 0x9b4504: r0 = Null
    //     0x9b4504: mov             x0, NULL
    // 0x9b4508: b               #0x9b4544
    // 0x9b450c: LoadField: r0 = r1->field_f
    //     0x9b450c: ldur            w0, [x1, #0xf]
    // 0x9b4510: DecompressPointer r0
    //     0x9b4510: add             x0, x0, HEAP, lsl #32
    // 0x9b4514: cmp             w0, NULL
    // 0x9b4518: b.ne            #0x9b4524
    // 0x9b451c: r0 = Null
    //     0x9b451c: mov             x0, NULL
    // 0x9b4520: b               #0x9b4544
    // 0x9b4524: LoadField: r1 = r0->field_1b
    //     0x9b4524: ldur            w1, [x0, #0x1b]
    // 0x9b4528: DecompressPointer r1
    //     0x9b4528: add             x1, x1, HEAP, lsl #32
    // 0x9b452c: cmp             w1, NULL
    // 0x9b4530: b.ne            #0x9b453c
    // 0x9b4534: r0 = Null
    //     0x9b4534: mov             x0, NULL
    // 0x9b4538: b               #0x9b4544
    // 0x9b453c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b453c: ldur            w0, [x1, #0x17]
    // 0x9b4540: DecompressPointer r0
    //     0x9b4540: add             x0, x0, HEAP, lsl #32
    // 0x9b4544: cmp             w0, NULL
    // 0x9b4548: b.ne            #0x9b4554
    // 0x9b454c: r3 = 0
    //     0x9b454c: movz            x3, #0
    // 0x9b4550: b               #0x9b4564
    // 0x9b4554: r1 = LoadInt32Instr(r0)
    //     0x9b4554: sbfx            x1, x0, #1, #0x1f
    //     0x9b4558: tbz             w0, #0, #0x9b4560
    //     0x9b455c: ldur            x1, [x0, #7]
    // 0x9b4560: mov             x3, x1
    // 0x9b4564: ldur            x2, [fp, #-8]
    // 0x9b4568: ldur            x1, [fp, #-0x30]
    // 0x9b456c: ldur            x0, [fp, #-0x48]
    // 0x9b4570: add             x4, x0, x3
    // 0x9b4574: scvtf           d0, x1
    // 0x9b4578: scvtf           d1, x4
    // 0x9b457c: fdiv            d2, d0, d1
    // 0x9b4580: stur            d2, [fp, #-0x68]
    // 0x9b4584: LoadField: r1 = r2->field_f
    //     0x9b4584: ldur            w1, [x2, #0xf]
    // 0x9b4588: DecompressPointer r1
    //     0x9b4588: add             x1, x1, HEAP, lsl #32
    // 0x9b458c: r0 = controller()
    //     0x9b458c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4590: LoadField: r1 = r0->field_7b
    //     0x9b4590: ldur            w1, [x0, #0x7b]
    // 0x9b4594: DecompressPointer r1
    //     0x9b4594: add             x1, x1, HEAP, lsl #32
    // 0x9b4598: r0 = value()
    //     0x9b4598: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b459c: LoadField: r1 = r0->field_b
    //     0x9b459c: ldur            w1, [x0, #0xb]
    // 0x9b45a0: DecompressPointer r1
    //     0x9b45a0: add             x1, x1, HEAP, lsl #32
    // 0x9b45a4: cmp             w1, NULL
    // 0x9b45a8: b.ne            #0x9b45b4
    // 0x9b45ac: r0 = Null
    //     0x9b45ac: mov             x0, NULL
    // 0x9b45b0: b               #0x9b45ec
    // 0x9b45b4: LoadField: r0 = r1->field_f
    //     0x9b45b4: ldur            w0, [x1, #0xf]
    // 0x9b45b8: DecompressPointer r0
    //     0x9b45b8: add             x0, x0, HEAP, lsl #32
    // 0x9b45bc: cmp             w0, NULL
    // 0x9b45c0: b.ne            #0x9b45cc
    // 0x9b45c4: r0 = Null
    //     0x9b45c4: mov             x0, NULL
    // 0x9b45c8: b               #0x9b45ec
    // 0x9b45cc: LoadField: r1 = r0->field_1b
    //     0x9b45cc: ldur            w1, [x0, #0x1b]
    // 0x9b45d0: DecompressPointer r1
    //     0x9b45d0: add             x1, x1, HEAP, lsl #32
    // 0x9b45d4: cmp             w1, NULL
    // 0x9b45d8: b.ne            #0x9b45e4
    // 0x9b45dc: r0 = Null
    //     0x9b45dc: mov             x0, NULL
    // 0x9b45e0: b               #0x9b45ec
    // 0x9b45e4: LoadField: r0 = r1->field_b
    //     0x9b45e4: ldur            w0, [x1, #0xb]
    // 0x9b45e8: DecompressPointer r0
    //     0x9b45e8: add             x0, x0, HEAP, lsl #32
    // 0x9b45ec: cmp             w0, NULL
    // 0x9b45f0: b.ne            #0x9b45fc
    // 0x9b45f4: r5 = 0
    //     0x9b45f4: movz            x5, #0
    // 0x9b45f8: b               #0x9b460c
    // 0x9b45fc: r1 = LoadInt32Instr(r0)
    //     0x9b45fc: sbfx            x1, x0, #1, #0x1f
    //     0x9b4600: tbz             w0, #0, #0x9b4608
    //     0x9b4604: ldur            x1, [x0, #7]
    // 0x9b4608: mov             x5, x1
    // 0x9b460c: ldur            x0, [fp, #-8]
    // 0x9b4610: ldur            x1, [fp, #-0x20]
    // 0x9b4614: ldur            x2, [fp, #-0x18]
    // 0x9b4618: ldur            d0, [fp, #-0x68]
    // 0x9b461c: r3 = "4"
    //     0x9b461c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] "4"
    //     0x9b4620: ldr             x3, [x3, #0x8a0]
    // 0x9b4624: r0 = chartRow()
    //     0x9b4624: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x9b4628: ldur            x2, [fp, #-8]
    // 0x9b462c: stur            x0, [fp, #-0x50]
    // 0x9b4630: LoadField: r3 = r2->field_f
    //     0x9b4630: ldur            w3, [x2, #0xf]
    // 0x9b4634: DecompressPointer r3
    //     0x9b4634: add             x3, x3, HEAP, lsl #32
    // 0x9b4638: stur            x3, [fp, #-0x20]
    // 0x9b463c: LoadField: r4 = r2->field_13
    //     0x9b463c: ldur            w4, [x2, #0x13]
    // 0x9b4640: DecompressPointer r4
    //     0x9b4640: add             x4, x4, HEAP, lsl #32
    // 0x9b4644: mov             x1, x3
    // 0x9b4648: stur            x4, [fp, #-0x18]
    // 0x9b464c: r0 = controller()
    //     0x9b464c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4650: LoadField: r1 = r0->field_7b
    //     0x9b4650: ldur            w1, [x0, #0x7b]
    // 0x9b4654: DecompressPointer r1
    //     0x9b4654: add             x1, x1, HEAP, lsl #32
    // 0x9b4658: r0 = value()
    //     0x9b4658: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b465c: LoadField: r1 = r0->field_b
    //     0x9b465c: ldur            w1, [x0, #0xb]
    // 0x9b4660: DecompressPointer r1
    //     0x9b4660: add             x1, x1, HEAP, lsl #32
    // 0x9b4664: cmp             w1, NULL
    // 0x9b4668: b.ne            #0x9b4674
    // 0x9b466c: r0 = Null
    //     0x9b466c: mov             x0, NULL
    // 0x9b4670: b               #0x9b46ac
    // 0x9b4674: LoadField: r0 = r1->field_f
    //     0x9b4674: ldur            w0, [x1, #0xf]
    // 0x9b4678: DecompressPointer r0
    //     0x9b4678: add             x0, x0, HEAP, lsl #32
    // 0x9b467c: cmp             w0, NULL
    // 0x9b4680: b.ne            #0x9b468c
    // 0x9b4684: r0 = Null
    //     0x9b4684: mov             x0, NULL
    // 0x9b4688: b               #0x9b46ac
    // 0x9b468c: LoadField: r1 = r0->field_1b
    //     0x9b468c: ldur            w1, [x0, #0x1b]
    // 0x9b4690: DecompressPointer r1
    //     0x9b4690: add             x1, x1, HEAP, lsl #32
    // 0x9b4694: cmp             w1, NULL
    // 0x9b4698: b.ne            #0x9b46a4
    // 0x9b469c: r0 = Null
    //     0x9b469c: mov             x0, NULL
    // 0x9b46a0: b               #0x9b46ac
    // 0x9b46a4: LoadField: r0 = r1->field_f
    //     0x9b46a4: ldur            w0, [x1, #0xf]
    // 0x9b46a8: DecompressPointer r0
    //     0x9b46a8: add             x0, x0, HEAP, lsl #32
    // 0x9b46ac: cmp             w0, NULL
    // 0x9b46b0: b.ne            #0x9b46bc
    // 0x9b46b4: r0 = 0
    //     0x9b46b4: movz            x0, #0
    // 0x9b46b8: b               #0x9b46cc
    // 0x9b46bc: r1 = LoadInt32Instr(r0)
    //     0x9b46bc: sbfx            x1, x0, #1, #0x1f
    //     0x9b46c0: tbz             w0, #0, #0x9b46c8
    //     0x9b46c4: ldur            x1, [x0, #7]
    // 0x9b46c8: mov             x0, x1
    // 0x9b46cc: ldur            x2, [fp, #-8]
    // 0x9b46d0: stur            x0, [fp, #-0x30]
    // 0x9b46d4: LoadField: r1 = r2->field_f
    //     0x9b46d4: ldur            w1, [x2, #0xf]
    // 0x9b46d8: DecompressPointer r1
    //     0x9b46d8: add             x1, x1, HEAP, lsl #32
    // 0x9b46dc: r0 = controller()
    //     0x9b46dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b46e0: LoadField: r1 = r0->field_7b
    //     0x9b46e0: ldur            w1, [x0, #0x7b]
    // 0x9b46e4: DecompressPointer r1
    //     0x9b46e4: add             x1, x1, HEAP, lsl #32
    // 0x9b46e8: r0 = value()
    //     0x9b46e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b46ec: LoadField: r1 = r0->field_b
    //     0x9b46ec: ldur            w1, [x0, #0xb]
    // 0x9b46f0: DecompressPointer r1
    //     0x9b46f0: add             x1, x1, HEAP, lsl #32
    // 0x9b46f4: cmp             w1, NULL
    // 0x9b46f8: b.ne            #0x9b4704
    // 0x9b46fc: r0 = Null
    //     0x9b46fc: mov             x0, NULL
    // 0x9b4700: b               #0x9b473c
    // 0x9b4704: LoadField: r0 = r1->field_f
    //     0x9b4704: ldur            w0, [x1, #0xf]
    // 0x9b4708: DecompressPointer r0
    //     0x9b4708: add             x0, x0, HEAP, lsl #32
    // 0x9b470c: cmp             w0, NULL
    // 0x9b4710: b.ne            #0x9b471c
    // 0x9b4714: r0 = Null
    //     0x9b4714: mov             x0, NULL
    // 0x9b4718: b               #0x9b473c
    // 0x9b471c: LoadField: r1 = r0->field_1b
    //     0x9b471c: ldur            w1, [x0, #0x1b]
    // 0x9b4720: DecompressPointer r1
    //     0x9b4720: add             x1, x1, HEAP, lsl #32
    // 0x9b4724: cmp             w1, NULL
    // 0x9b4728: b.ne            #0x9b4734
    // 0x9b472c: r0 = Null
    //     0x9b472c: mov             x0, NULL
    // 0x9b4730: b               #0x9b473c
    // 0x9b4734: LoadField: r0 = r1->field_7
    //     0x9b4734: ldur            w0, [x1, #7]
    // 0x9b4738: DecompressPointer r0
    //     0x9b4738: add             x0, x0, HEAP, lsl #32
    // 0x9b473c: cmp             w0, NULL
    // 0x9b4740: b.ne            #0x9b474c
    // 0x9b4744: r0 = 0
    //     0x9b4744: movz            x0, #0
    // 0x9b4748: b               #0x9b475c
    // 0x9b474c: r1 = LoadInt32Instr(r0)
    //     0x9b474c: sbfx            x1, x0, #1, #0x1f
    //     0x9b4750: tbz             w0, #0, #0x9b4758
    //     0x9b4754: ldur            x1, [x0, #7]
    // 0x9b4758: mov             x0, x1
    // 0x9b475c: ldur            x2, [fp, #-8]
    // 0x9b4760: stur            x0, [fp, #-0x40]
    // 0x9b4764: LoadField: r1 = r2->field_f
    //     0x9b4764: ldur            w1, [x2, #0xf]
    // 0x9b4768: DecompressPointer r1
    //     0x9b4768: add             x1, x1, HEAP, lsl #32
    // 0x9b476c: r0 = controller()
    //     0x9b476c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4770: LoadField: r1 = r0->field_7b
    //     0x9b4770: ldur            w1, [x0, #0x7b]
    // 0x9b4774: DecompressPointer r1
    //     0x9b4774: add             x1, x1, HEAP, lsl #32
    // 0x9b4778: r0 = value()
    //     0x9b4778: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b477c: LoadField: r1 = r0->field_b
    //     0x9b477c: ldur            w1, [x0, #0xb]
    // 0x9b4780: DecompressPointer r1
    //     0x9b4780: add             x1, x1, HEAP, lsl #32
    // 0x9b4784: cmp             w1, NULL
    // 0x9b4788: b.ne            #0x9b4794
    // 0x9b478c: r0 = Null
    //     0x9b478c: mov             x0, NULL
    // 0x9b4790: b               #0x9b47cc
    // 0x9b4794: LoadField: r0 = r1->field_f
    //     0x9b4794: ldur            w0, [x1, #0xf]
    // 0x9b4798: DecompressPointer r0
    //     0x9b4798: add             x0, x0, HEAP, lsl #32
    // 0x9b479c: cmp             w0, NULL
    // 0x9b47a0: b.ne            #0x9b47ac
    // 0x9b47a4: r0 = Null
    //     0x9b47a4: mov             x0, NULL
    // 0x9b47a8: b               #0x9b47cc
    // 0x9b47ac: LoadField: r1 = r0->field_1b
    //     0x9b47ac: ldur            w1, [x0, #0x1b]
    // 0x9b47b0: DecompressPointer r1
    //     0x9b47b0: add             x1, x1, HEAP, lsl #32
    // 0x9b47b4: cmp             w1, NULL
    // 0x9b47b8: b.ne            #0x9b47c4
    // 0x9b47bc: r0 = Null
    //     0x9b47bc: mov             x0, NULL
    // 0x9b47c0: b               #0x9b47cc
    // 0x9b47c4: LoadField: r0 = r1->field_b
    //     0x9b47c4: ldur            w0, [x1, #0xb]
    // 0x9b47c8: DecompressPointer r0
    //     0x9b47c8: add             x0, x0, HEAP, lsl #32
    // 0x9b47cc: cmp             w0, NULL
    // 0x9b47d0: b.ne            #0x9b47dc
    // 0x9b47d4: r1 = 0
    //     0x9b47d4: movz            x1, #0
    // 0x9b47d8: b               #0x9b47e8
    // 0x9b47dc: r1 = LoadInt32Instr(r0)
    //     0x9b47dc: sbfx            x1, x0, #1, #0x1f
    //     0x9b47e0: tbz             w0, #0, #0x9b47e8
    //     0x9b47e4: ldur            x1, [x0, #7]
    // 0x9b47e8: ldur            x2, [fp, #-8]
    // 0x9b47ec: ldur            x0, [fp, #-0x40]
    // 0x9b47f0: add             x3, x0, x1
    // 0x9b47f4: stur            x3, [fp, #-0x48]
    // 0x9b47f8: LoadField: r1 = r2->field_f
    //     0x9b47f8: ldur            w1, [x2, #0xf]
    // 0x9b47fc: DecompressPointer r1
    //     0x9b47fc: add             x1, x1, HEAP, lsl #32
    // 0x9b4800: r0 = controller()
    //     0x9b4800: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4804: LoadField: r1 = r0->field_7b
    //     0x9b4804: ldur            w1, [x0, #0x7b]
    // 0x9b4808: DecompressPointer r1
    //     0x9b4808: add             x1, x1, HEAP, lsl #32
    // 0x9b480c: r0 = value()
    //     0x9b480c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4810: LoadField: r1 = r0->field_b
    //     0x9b4810: ldur            w1, [x0, #0xb]
    // 0x9b4814: DecompressPointer r1
    //     0x9b4814: add             x1, x1, HEAP, lsl #32
    // 0x9b4818: cmp             w1, NULL
    // 0x9b481c: b.ne            #0x9b4828
    // 0x9b4820: r0 = Null
    //     0x9b4820: mov             x0, NULL
    // 0x9b4824: b               #0x9b4860
    // 0x9b4828: LoadField: r0 = r1->field_f
    //     0x9b4828: ldur            w0, [x1, #0xf]
    // 0x9b482c: DecompressPointer r0
    //     0x9b482c: add             x0, x0, HEAP, lsl #32
    // 0x9b4830: cmp             w0, NULL
    // 0x9b4834: b.ne            #0x9b4840
    // 0x9b4838: r0 = Null
    //     0x9b4838: mov             x0, NULL
    // 0x9b483c: b               #0x9b4860
    // 0x9b4840: LoadField: r1 = r0->field_1b
    //     0x9b4840: ldur            w1, [x0, #0x1b]
    // 0x9b4844: DecompressPointer r1
    //     0x9b4844: add             x1, x1, HEAP, lsl #32
    // 0x9b4848: cmp             w1, NULL
    // 0x9b484c: b.ne            #0x9b4858
    // 0x9b4850: r0 = Null
    //     0x9b4850: mov             x0, NULL
    // 0x9b4854: b               #0x9b4860
    // 0x9b4858: LoadField: r0 = r1->field_f
    //     0x9b4858: ldur            w0, [x1, #0xf]
    // 0x9b485c: DecompressPointer r0
    //     0x9b485c: add             x0, x0, HEAP, lsl #32
    // 0x9b4860: cmp             w0, NULL
    // 0x9b4864: b.ne            #0x9b4870
    // 0x9b4868: r1 = 0
    //     0x9b4868: movz            x1, #0
    // 0x9b486c: b               #0x9b487c
    // 0x9b4870: r1 = LoadInt32Instr(r0)
    //     0x9b4870: sbfx            x1, x0, #1, #0x1f
    //     0x9b4874: tbz             w0, #0, #0x9b487c
    //     0x9b4878: ldur            x1, [x0, #7]
    // 0x9b487c: ldur            x2, [fp, #-8]
    // 0x9b4880: ldur            x0, [fp, #-0x48]
    // 0x9b4884: add             x3, x0, x1
    // 0x9b4888: stur            x3, [fp, #-0x40]
    // 0x9b488c: LoadField: r1 = r2->field_f
    //     0x9b488c: ldur            w1, [x2, #0xf]
    // 0x9b4890: DecompressPointer r1
    //     0x9b4890: add             x1, x1, HEAP, lsl #32
    // 0x9b4894: r0 = controller()
    //     0x9b4894: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4898: LoadField: r1 = r0->field_7b
    //     0x9b4898: ldur            w1, [x0, #0x7b]
    // 0x9b489c: DecompressPointer r1
    //     0x9b489c: add             x1, x1, HEAP, lsl #32
    // 0x9b48a0: r0 = value()
    //     0x9b48a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b48a4: LoadField: r1 = r0->field_b
    //     0x9b48a4: ldur            w1, [x0, #0xb]
    // 0x9b48a8: DecompressPointer r1
    //     0x9b48a8: add             x1, x1, HEAP, lsl #32
    // 0x9b48ac: cmp             w1, NULL
    // 0x9b48b0: b.ne            #0x9b48bc
    // 0x9b48b4: r0 = Null
    //     0x9b48b4: mov             x0, NULL
    // 0x9b48b8: b               #0x9b48f4
    // 0x9b48bc: LoadField: r0 = r1->field_f
    //     0x9b48bc: ldur            w0, [x1, #0xf]
    // 0x9b48c0: DecompressPointer r0
    //     0x9b48c0: add             x0, x0, HEAP, lsl #32
    // 0x9b48c4: cmp             w0, NULL
    // 0x9b48c8: b.ne            #0x9b48d4
    // 0x9b48cc: r0 = Null
    //     0x9b48cc: mov             x0, NULL
    // 0x9b48d0: b               #0x9b48f4
    // 0x9b48d4: LoadField: r1 = r0->field_1b
    //     0x9b48d4: ldur            w1, [x0, #0x1b]
    // 0x9b48d8: DecompressPointer r1
    //     0x9b48d8: add             x1, x1, HEAP, lsl #32
    // 0x9b48dc: cmp             w1, NULL
    // 0x9b48e0: b.ne            #0x9b48ec
    // 0x9b48e4: r0 = Null
    //     0x9b48e4: mov             x0, NULL
    // 0x9b48e8: b               #0x9b48f4
    // 0x9b48ec: LoadField: r0 = r1->field_13
    //     0x9b48ec: ldur            w0, [x1, #0x13]
    // 0x9b48f0: DecompressPointer r0
    //     0x9b48f0: add             x0, x0, HEAP, lsl #32
    // 0x9b48f4: cmp             w0, NULL
    // 0x9b48f8: b.ne            #0x9b4904
    // 0x9b48fc: r1 = 0
    //     0x9b48fc: movz            x1, #0
    // 0x9b4900: b               #0x9b4910
    // 0x9b4904: r1 = LoadInt32Instr(r0)
    //     0x9b4904: sbfx            x1, x0, #1, #0x1f
    //     0x9b4908: tbz             w0, #0, #0x9b4910
    //     0x9b490c: ldur            x1, [x0, #7]
    // 0x9b4910: ldur            x2, [fp, #-8]
    // 0x9b4914: ldur            x0, [fp, #-0x40]
    // 0x9b4918: add             x3, x0, x1
    // 0x9b491c: stur            x3, [fp, #-0x48]
    // 0x9b4920: LoadField: r1 = r2->field_f
    //     0x9b4920: ldur            w1, [x2, #0xf]
    // 0x9b4924: DecompressPointer r1
    //     0x9b4924: add             x1, x1, HEAP, lsl #32
    // 0x9b4928: r0 = controller()
    //     0x9b4928: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b492c: LoadField: r1 = r0->field_7b
    //     0x9b492c: ldur            w1, [x0, #0x7b]
    // 0x9b4930: DecompressPointer r1
    //     0x9b4930: add             x1, x1, HEAP, lsl #32
    // 0x9b4934: r0 = value()
    //     0x9b4934: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4938: LoadField: r1 = r0->field_b
    //     0x9b4938: ldur            w1, [x0, #0xb]
    // 0x9b493c: DecompressPointer r1
    //     0x9b493c: add             x1, x1, HEAP, lsl #32
    // 0x9b4940: cmp             w1, NULL
    // 0x9b4944: b.ne            #0x9b4950
    // 0x9b4948: r0 = Null
    //     0x9b4948: mov             x0, NULL
    // 0x9b494c: b               #0x9b4988
    // 0x9b4950: LoadField: r0 = r1->field_f
    //     0x9b4950: ldur            w0, [x1, #0xf]
    // 0x9b4954: DecompressPointer r0
    //     0x9b4954: add             x0, x0, HEAP, lsl #32
    // 0x9b4958: cmp             w0, NULL
    // 0x9b495c: b.ne            #0x9b4968
    // 0x9b4960: r0 = Null
    //     0x9b4960: mov             x0, NULL
    // 0x9b4964: b               #0x9b4988
    // 0x9b4968: LoadField: r1 = r0->field_1b
    //     0x9b4968: ldur            w1, [x0, #0x1b]
    // 0x9b496c: DecompressPointer r1
    //     0x9b496c: add             x1, x1, HEAP, lsl #32
    // 0x9b4970: cmp             w1, NULL
    // 0x9b4974: b.ne            #0x9b4980
    // 0x9b4978: r0 = Null
    //     0x9b4978: mov             x0, NULL
    // 0x9b497c: b               #0x9b4988
    // 0x9b4980: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b4980: ldur            w0, [x1, #0x17]
    // 0x9b4984: DecompressPointer r0
    //     0x9b4984: add             x0, x0, HEAP, lsl #32
    // 0x9b4988: cmp             w0, NULL
    // 0x9b498c: b.ne            #0x9b4998
    // 0x9b4990: r3 = 0
    //     0x9b4990: movz            x3, #0
    // 0x9b4994: b               #0x9b49a8
    // 0x9b4998: r1 = LoadInt32Instr(r0)
    //     0x9b4998: sbfx            x1, x0, #1, #0x1f
    //     0x9b499c: tbz             w0, #0, #0x9b49a4
    //     0x9b49a0: ldur            x1, [x0, #7]
    // 0x9b49a4: mov             x3, x1
    // 0x9b49a8: ldur            x2, [fp, #-8]
    // 0x9b49ac: ldur            x1, [fp, #-0x30]
    // 0x9b49b0: ldur            x0, [fp, #-0x48]
    // 0x9b49b4: add             x4, x0, x3
    // 0x9b49b8: scvtf           d0, x1
    // 0x9b49bc: scvtf           d1, x4
    // 0x9b49c0: fdiv            d2, d0, d1
    // 0x9b49c4: stur            d2, [fp, #-0x68]
    // 0x9b49c8: LoadField: r1 = r2->field_f
    //     0x9b49c8: ldur            w1, [x2, #0xf]
    // 0x9b49cc: DecompressPointer r1
    //     0x9b49cc: add             x1, x1, HEAP, lsl #32
    // 0x9b49d0: r0 = controller()
    //     0x9b49d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b49d4: LoadField: r1 = r0->field_7b
    //     0x9b49d4: ldur            w1, [x0, #0x7b]
    // 0x9b49d8: DecompressPointer r1
    //     0x9b49d8: add             x1, x1, HEAP, lsl #32
    // 0x9b49dc: r0 = value()
    //     0x9b49dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b49e0: LoadField: r1 = r0->field_b
    //     0x9b49e0: ldur            w1, [x0, #0xb]
    // 0x9b49e4: DecompressPointer r1
    //     0x9b49e4: add             x1, x1, HEAP, lsl #32
    // 0x9b49e8: cmp             w1, NULL
    // 0x9b49ec: b.ne            #0x9b49f8
    // 0x9b49f0: r0 = Null
    //     0x9b49f0: mov             x0, NULL
    // 0x9b49f4: b               #0x9b4a30
    // 0x9b49f8: LoadField: r0 = r1->field_f
    //     0x9b49f8: ldur            w0, [x1, #0xf]
    // 0x9b49fc: DecompressPointer r0
    //     0x9b49fc: add             x0, x0, HEAP, lsl #32
    // 0x9b4a00: cmp             w0, NULL
    // 0x9b4a04: b.ne            #0x9b4a10
    // 0x9b4a08: r0 = Null
    //     0x9b4a08: mov             x0, NULL
    // 0x9b4a0c: b               #0x9b4a30
    // 0x9b4a10: LoadField: r1 = r0->field_1b
    //     0x9b4a10: ldur            w1, [x0, #0x1b]
    // 0x9b4a14: DecompressPointer r1
    //     0x9b4a14: add             x1, x1, HEAP, lsl #32
    // 0x9b4a18: cmp             w1, NULL
    // 0x9b4a1c: b.ne            #0x9b4a28
    // 0x9b4a20: r0 = Null
    //     0x9b4a20: mov             x0, NULL
    // 0x9b4a24: b               #0x9b4a30
    // 0x9b4a28: LoadField: r0 = r1->field_f
    //     0x9b4a28: ldur            w0, [x1, #0xf]
    // 0x9b4a2c: DecompressPointer r0
    //     0x9b4a2c: add             x0, x0, HEAP, lsl #32
    // 0x9b4a30: cmp             w0, NULL
    // 0x9b4a34: b.ne            #0x9b4a40
    // 0x9b4a38: r5 = 0
    //     0x9b4a38: movz            x5, #0
    // 0x9b4a3c: b               #0x9b4a50
    // 0x9b4a40: r1 = LoadInt32Instr(r0)
    //     0x9b4a40: sbfx            x1, x0, #1, #0x1f
    //     0x9b4a44: tbz             w0, #0, #0x9b4a4c
    //     0x9b4a48: ldur            x1, [x0, #7]
    // 0x9b4a4c: mov             x5, x1
    // 0x9b4a50: ldur            x0, [fp, #-8]
    // 0x9b4a54: ldur            x1, [fp, #-0x20]
    // 0x9b4a58: ldur            x2, [fp, #-0x18]
    // 0x9b4a5c: ldur            d0, [fp, #-0x68]
    // 0x9b4a60: r3 = "3"
    //     0x9b4a60: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] "3"
    //     0x9b4a64: ldr             x3, [x3, #0x8a8]
    // 0x9b4a68: r0 = chartRow()
    //     0x9b4a68: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x9b4a6c: ldur            x2, [fp, #-8]
    // 0x9b4a70: stur            x0, [fp, #-0x58]
    // 0x9b4a74: LoadField: r3 = r2->field_f
    //     0x9b4a74: ldur            w3, [x2, #0xf]
    // 0x9b4a78: DecompressPointer r3
    //     0x9b4a78: add             x3, x3, HEAP, lsl #32
    // 0x9b4a7c: stur            x3, [fp, #-0x20]
    // 0x9b4a80: LoadField: r4 = r2->field_13
    //     0x9b4a80: ldur            w4, [x2, #0x13]
    // 0x9b4a84: DecompressPointer r4
    //     0x9b4a84: add             x4, x4, HEAP, lsl #32
    // 0x9b4a88: mov             x1, x3
    // 0x9b4a8c: stur            x4, [fp, #-0x18]
    // 0x9b4a90: r0 = controller()
    //     0x9b4a90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4a94: LoadField: r1 = r0->field_7b
    //     0x9b4a94: ldur            w1, [x0, #0x7b]
    // 0x9b4a98: DecompressPointer r1
    //     0x9b4a98: add             x1, x1, HEAP, lsl #32
    // 0x9b4a9c: r0 = value()
    //     0x9b4a9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4aa0: LoadField: r1 = r0->field_b
    //     0x9b4aa0: ldur            w1, [x0, #0xb]
    // 0x9b4aa4: DecompressPointer r1
    //     0x9b4aa4: add             x1, x1, HEAP, lsl #32
    // 0x9b4aa8: cmp             w1, NULL
    // 0x9b4aac: b.ne            #0x9b4ab8
    // 0x9b4ab0: r0 = Null
    //     0x9b4ab0: mov             x0, NULL
    // 0x9b4ab4: b               #0x9b4af0
    // 0x9b4ab8: LoadField: r0 = r1->field_f
    //     0x9b4ab8: ldur            w0, [x1, #0xf]
    // 0x9b4abc: DecompressPointer r0
    //     0x9b4abc: add             x0, x0, HEAP, lsl #32
    // 0x9b4ac0: cmp             w0, NULL
    // 0x9b4ac4: b.ne            #0x9b4ad0
    // 0x9b4ac8: r0 = Null
    //     0x9b4ac8: mov             x0, NULL
    // 0x9b4acc: b               #0x9b4af0
    // 0x9b4ad0: LoadField: r1 = r0->field_1b
    //     0x9b4ad0: ldur            w1, [x0, #0x1b]
    // 0x9b4ad4: DecompressPointer r1
    //     0x9b4ad4: add             x1, x1, HEAP, lsl #32
    // 0x9b4ad8: cmp             w1, NULL
    // 0x9b4adc: b.ne            #0x9b4ae8
    // 0x9b4ae0: r0 = Null
    //     0x9b4ae0: mov             x0, NULL
    // 0x9b4ae4: b               #0x9b4af0
    // 0x9b4ae8: LoadField: r0 = r1->field_13
    //     0x9b4ae8: ldur            w0, [x1, #0x13]
    // 0x9b4aec: DecompressPointer r0
    //     0x9b4aec: add             x0, x0, HEAP, lsl #32
    // 0x9b4af0: cmp             w0, NULL
    // 0x9b4af4: b.ne            #0x9b4b00
    // 0x9b4af8: r0 = 0
    //     0x9b4af8: movz            x0, #0
    // 0x9b4afc: b               #0x9b4b10
    // 0x9b4b00: r1 = LoadInt32Instr(r0)
    //     0x9b4b00: sbfx            x1, x0, #1, #0x1f
    //     0x9b4b04: tbz             w0, #0, #0x9b4b0c
    //     0x9b4b08: ldur            x1, [x0, #7]
    // 0x9b4b0c: mov             x0, x1
    // 0x9b4b10: ldur            x2, [fp, #-8]
    // 0x9b4b14: stur            x0, [fp, #-0x30]
    // 0x9b4b18: LoadField: r1 = r2->field_f
    //     0x9b4b18: ldur            w1, [x2, #0xf]
    // 0x9b4b1c: DecompressPointer r1
    //     0x9b4b1c: add             x1, x1, HEAP, lsl #32
    // 0x9b4b20: r0 = controller()
    //     0x9b4b20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4b24: LoadField: r1 = r0->field_7b
    //     0x9b4b24: ldur            w1, [x0, #0x7b]
    // 0x9b4b28: DecompressPointer r1
    //     0x9b4b28: add             x1, x1, HEAP, lsl #32
    // 0x9b4b2c: r0 = value()
    //     0x9b4b2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4b30: LoadField: r1 = r0->field_b
    //     0x9b4b30: ldur            w1, [x0, #0xb]
    // 0x9b4b34: DecompressPointer r1
    //     0x9b4b34: add             x1, x1, HEAP, lsl #32
    // 0x9b4b38: cmp             w1, NULL
    // 0x9b4b3c: b.ne            #0x9b4b48
    // 0x9b4b40: r0 = Null
    //     0x9b4b40: mov             x0, NULL
    // 0x9b4b44: b               #0x9b4b80
    // 0x9b4b48: LoadField: r0 = r1->field_f
    //     0x9b4b48: ldur            w0, [x1, #0xf]
    // 0x9b4b4c: DecompressPointer r0
    //     0x9b4b4c: add             x0, x0, HEAP, lsl #32
    // 0x9b4b50: cmp             w0, NULL
    // 0x9b4b54: b.ne            #0x9b4b60
    // 0x9b4b58: r0 = Null
    //     0x9b4b58: mov             x0, NULL
    // 0x9b4b5c: b               #0x9b4b80
    // 0x9b4b60: LoadField: r1 = r0->field_1b
    //     0x9b4b60: ldur            w1, [x0, #0x1b]
    // 0x9b4b64: DecompressPointer r1
    //     0x9b4b64: add             x1, x1, HEAP, lsl #32
    // 0x9b4b68: cmp             w1, NULL
    // 0x9b4b6c: b.ne            #0x9b4b78
    // 0x9b4b70: r0 = Null
    //     0x9b4b70: mov             x0, NULL
    // 0x9b4b74: b               #0x9b4b80
    // 0x9b4b78: LoadField: r0 = r1->field_7
    //     0x9b4b78: ldur            w0, [x1, #7]
    // 0x9b4b7c: DecompressPointer r0
    //     0x9b4b7c: add             x0, x0, HEAP, lsl #32
    // 0x9b4b80: cmp             w0, NULL
    // 0x9b4b84: b.ne            #0x9b4b90
    // 0x9b4b88: r0 = 0
    //     0x9b4b88: movz            x0, #0
    // 0x9b4b8c: b               #0x9b4ba0
    // 0x9b4b90: r1 = LoadInt32Instr(r0)
    //     0x9b4b90: sbfx            x1, x0, #1, #0x1f
    //     0x9b4b94: tbz             w0, #0, #0x9b4b9c
    //     0x9b4b98: ldur            x1, [x0, #7]
    // 0x9b4b9c: mov             x0, x1
    // 0x9b4ba0: ldur            x2, [fp, #-8]
    // 0x9b4ba4: stur            x0, [fp, #-0x40]
    // 0x9b4ba8: LoadField: r1 = r2->field_f
    //     0x9b4ba8: ldur            w1, [x2, #0xf]
    // 0x9b4bac: DecompressPointer r1
    //     0x9b4bac: add             x1, x1, HEAP, lsl #32
    // 0x9b4bb0: r0 = controller()
    //     0x9b4bb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4bb4: LoadField: r1 = r0->field_7b
    //     0x9b4bb4: ldur            w1, [x0, #0x7b]
    // 0x9b4bb8: DecompressPointer r1
    //     0x9b4bb8: add             x1, x1, HEAP, lsl #32
    // 0x9b4bbc: r0 = value()
    //     0x9b4bbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4bc0: LoadField: r1 = r0->field_b
    //     0x9b4bc0: ldur            w1, [x0, #0xb]
    // 0x9b4bc4: DecompressPointer r1
    //     0x9b4bc4: add             x1, x1, HEAP, lsl #32
    // 0x9b4bc8: cmp             w1, NULL
    // 0x9b4bcc: b.ne            #0x9b4bd8
    // 0x9b4bd0: r0 = Null
    //     0x9b4bd0: mov             x0, NULL
    // 0x9b4bd4: b               #0x9b4c10
    // 0x9b4bd8: LoadField: r0 = r1->field_f
    //     0x9b4bd8: ldur            w0, [x1, #0xf]
    // 0x9b4bdc: DecompressPointer r0
    //     0x9b4bdc: add             x0, x0, HEAP, lsl #32
    // 0x9b4be0: cmp             w0, NULL
    // 0x9b4be4: b.ne            #0x9b4bf0
    // 0x9b4be8: r0 = Null
    //     0x9b4be8: mov             x0, NULL
    // 0x9b4bec: b               #0x9b4c10
    // 0x9b4bf0: LoadField: r1 = r0->field_1b
    //     0x9b4bf0: ldur            w1, [x0, #0x1b]
    // 0x9b4bf4: DecompressPointer r1
    //     0x9b4bf4: add             x1, x1, HEAP, lsl #32
    // 0x9b4bf8: cmp             w1, NULL
    // 0x9b4bfc: b.ne            #0x9b4c08
    // 0x9b4c00: r0 = Null
    //     0x9b4c00: mov             x0, NULL
    // 0x9b4c04: b               #0x9b4c10
    // 0x9b4c08: LoadField: r0 = r1->field_b
    //     0x9b4c08: ldur            w0, [x1, #0xb]
    // 0x9b4c0c: DecompressPointer r0
    //     0x9b4c0c: add             x0, x0, HEAP, lsl #32
    // 0x9b4c10: cmp             w0, NULL
    // 0x9b4c14: b.ne            #0x9b4c20
    // 0x9b4c18: r1 = 0
    //     0x9b4c18: movz            x1, #0
    // 0x9b4c1c: b               #0x9b4c2c
    // 0x9b4c20: r1 = LoadInt32Instr(r0)
    //     0x9b4c20: sbfx            x1, x0, #1, #0x1f
    //     0x9b4c24: tbz             w0, #0, #0x9b4c2c
    //     0x9b4c28: ldur            x1, [x0, #7]
    // 0x9b4c2c: ldur            x2, [fp, #-8]
    // 0x9b4c30: ldur            x0, [fp, #-0x40]
    // 0x9b4c34: add             x3, x0, x1
    // 0x9b4c38: stur            x3, [fp, #-0x48]
    // 0x9b4c3c: LoadField: r1 = r2->field_f
    //     0x9b4c3c: ldur            w1, [x2, #0xf]
    // 0x9b4c40: DecompressPointer r1
    //     0x9b4c40: add             x1, x1, HEAP, lsl #32
    // 0x9b4c44: r0 = controller()
    //     0x9b4c44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4c48: LoadField: r1 = r0->field_7b
    //     0x9b4c48: ldur            w1, [x0, #0x7b]
    // 0x9b4c4c: DecompressPointer r1
    //     0x9b4c4c: add             x1, x1, HEAP, lsl #32
    // 0x9b4c50: r0 = value()
    //     0x9b4c50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4c54: LoadField: r1 = r0->field_b
    //     0x9b4c54: ldur            w1, [x0, #0xb]
    // 0x9b4c58: DecompressPointer r1
    //     0x9b4c58: add             x1, x1, HEAP, lsl #32
    // 0x9b4c5c: cmp             w1, NULL
    // 0x9b4c60: b.ne            #0x9b4c6c
    // 0x9b4c64: r0 = Null
    //     0x9b4c64: mov             x0, NULL
    // 0x9b4c68: b               #0x9b4ca4
    // 0x9b4c6c: LoadField: r0 = r1->field_f
    //     0x9b4c6c: ldur            w0, [x1, #0xf]
    // 0x9b4c70: DecompressPointer r0
    //     0x9b4c70: add             x0, x0, HEAP, lsl #32
    // 0x9b4c74: cmp             w0, NULL
    // 0x9b4c78: b.ne            #0x9b4c84
    // 0x9b4c7c: r0 = Null
    //     0x9b4c7c: mov             x0, NULL
    // 0x9b4c80: b               #0x9b4ca4
    // 0x9b4c84: LoadField: r1 = r0->field_1b
    //     0x9b4c84: ldur            w1, [x0, #0x1b]
    // 0x9b4c88: DecompressPointer r1
    //     0x9b4c88: add             x1, x1, HEAP, lsl #32
    // 0x9b4c8c: cmp             w1, NULL
    // 0x9b4c90: b.ne            #0x9b4c9c
    // 0x9b4c94: r0 = Null
    //     0x9b4c94: mov             x0, NULL
    // 0x9b4c98: b               #0x9b4ca4
    // 0x9b4c9c: LoadField: r0 = r1->field_f
    //     0x9b4c9c: ldur            w0, [x1, #0xf]
    // 0x9b4ca0: DecompressPointer r0
    //     0x9b4ca0: add             x0, x0, HEAP, lsl #32
    // 0x9b4ca4: cmp             w0, NULL
    // 0x9b4ca8: b.ne            #0x9b4cb4
    // 0x9b4cac: r1 = 0
    //     0x9b4cac: movz            x1, #0
    // 0x9b4cb0: b               #0x9b4cc0
    // 0x9b4cb4: r1 = LoadInt32Instr(r0)
    //     0x9b4cb4: sbfx            x1, x0, #1, #0x1f
    //     0x9b4cb8: tbz             w0, #0, #0x9b4cc0
    //     0x9b4cbc: ldur            x1, [x0, #7]
    // 0x9b4cc0: ldur            x2, [fp, #-8]
    // 0x9b4cc4: ldur            x0, [fp, #-0x48]
    // 0x9b4cc8: add             x3, x0, x1
    // 0x9b4ccc: stur            x3, [fp, #-0x40]
    // 0x9b4cd0: LoadField: r1 = r2->field_f
    //     0x9b4cd0: ldur            w1, [x2, #0xf]
    // 0x9b4cd4: DecompressPointer r1
    //     0x9b4cd4: add             x1, x1, HEAP, lsl #32
    // 0x9b4cd8: r0 = controller()
    //     0x9b4cd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4cdc: LoadField: r1 = r0->field_7b
    //     0x9b4cdc: ldur            w1, [x0, #0x7b]
    // 0x9b4ce0: DecompressPointer r1
    //     0x9b4ce0: add             x1, x1, HEAP, lsl #32
    // 0x9b4ce4: r0 = value()
    //     0x9b4ce4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4ce8: LoadField: r1 = r0->field_b
    //     0x9b4ce8: ldur            w1, [x0, #0xb]
    // 0x9b4cec: DecompressPointer r1
    //     0x9b4cec: add             x1, x1, HEAP, lsl #32
    // 0x9b4cf0: cmp             w1, NULL
    // 0x9b4cf4: b.ne            #0x9b4d00
    // 0x9b4cf8: r0 = Null
    //     0x9b4cf8: mov             x0, NULL
    // 0x9b4cfc: b               #0x9b4d38
    // 0x9b4d00: LoadField: r0 = r1->field_f
    //     0x9b4d00: ldur            w0, [x1, #0xf]
    // 0x9b4d04: DecompressPointer r0
    //     0x9b4d04: add             x0, x0, HEAP, lsl #32
    // 0x9b4d08: cmp             w0, NULL
    // 0x9b4d0c: b.ne            #0x9b4d18
    // 0x9b4d10: r0 = Null
    //     0x9b4d10: mov             x0, NULL
    // 0x9b4d14: b               #0x9b4d38
    // 0x9b4d18: LoadField: r1 = r0->field_1b
    //     0x9b4d18: ldur            w1, [x0, #0x1b]
    // 0x9b4d1c: DecompressPointer r1
    //     0x9b4d1c: add             x1, x1, HEAP, lsl #32
    // 0x9b4d20: cmp             w1, NULL
    // 0x9b4d24: b.ne            #0x9b4d30
    // 0x9b4d28: r0 = Null
    //     0x9b4d28: mov             x0, NULL
    // 0x9b4d2c: b               #0x9b4d38
    // 0x9b4d30: LoadField: r0 = r1->field_13
    //     0x9b4d30: ldur            w0, [x1, #0x13]
    // 0x9b4d34: DecompressPointer r0
    //     0x9b4d34: add             x0, x0, HEAP, lsl #32
    // 0x9b4d38: cmp             w0, NULL
    // 0x9b4d3c: b.ne            #0x9b4d48
    // 0x9b4d40: r1 = 0
    //     0x9b4d40: movz            x1, #0
    // 0x9b4d44: b               #0x9b4d54
    // 0x9b4d48: r1 = LoadInt32Instr(r0)
    //     0x9b4d48: sbfx            x1, x0, #1, #0x1f
    //     0x9b4d4c: tbz             w0, #0, #0x9b4d54
    //     0x9b4d50: ldur            x1, [x0, #7]
    // 0x9b4d54: ldur            x2, [fp, #-8]
    // 0x9b4d58: ldur            x0, [fp, #-0x40]
    // 0x9b4d5c: add             x3, x0, x1
    // 0x9b4d60: stur            x3, [fp, #-0x48]
    // 0x9b4d64: LoadField: r1 = r2->field_f
    //     0x9b4d64: ldur            w1, [x2, #0xf]
    // 0x9b4d68: DecompressPointer r1
    //     0x9b4d68: add             x1, x1, HEAP, lsl #32
    // 0x9b4d6c: r0 = controller()
    //     0x9b4d6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4d70: LoadField: r1 = r0->field_7b
    //     0x9b4d70: ldur            w1, [x0, #0x7b]
    // 0x9b4d74: DecompressPointer r1
    //     0x9b4d74: add             x1, x1, HEAP, lsl #32
    // 0x9b4d78: r0 = value()
    //     0x9b4d78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4d7c: LoadField: r1 = r0->field_b
    //     0x9b4d7c: ldur            w1, [x0, #0xb]
    // 0x9b4d80: DecompressPointer r1
    //     0x9b4d80: add             x1, x1, HEAP, lsl #32
    // 0x9b4d84: cmp             w1, NULL
    // 0x9b4d88: b.ne            #0x9b4d94
    // 0x9b4d8c: r0 = Null
    //     0x9b4d8c: mov             x0, NULL
    // 0x9b4d90: b               #0x9b4dcc
    // 0x9b4d94: LoadField: r0 = r1->field_f
    //     0x9b4d94: ldur            w0, [x1, #0xf]
    // 0x9b4d98: DecompressPointer r0
    //     0x9b4d98: add             x0, x0, HEAP, lsl #32
    // 0x9b4d9c: cmp             w0, NULL
    // 0x9b4da0: b.ne            #0x9b4dac
    // 0x9b4da4: r0 = Null
    //     0x9b4da4: mov             x0, NULL
    // 0x9b4da8: b               #0x9b4dcc
    // 0x9b4dac: LoadField: r1 = r0->field_1b
    //     0x9b4dac: ldur            w1, [x0, #0x1b]
    // 0x9b4db0: DecompressPointer r1
    //     0x9b4db0: add             x1, x1, HEAP, lsl #32
    // 0x9b4db4: cmp             w1, NULL
    // 0x9b4db8: b.ne            #0x9b4dc4
    // 0x9b4dbc: r0 = Null
    //     0x9b4dbc: mov             x0, NULL
    // 0x9b4dc0: b               #0x9b4dcc
    // 0x9b4dc4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b4dc4: ldur            w0, [x1, #0x17]
    // 0x9b4dc8: DecompressPointer r0
    //     0x9b4dc8: add             x0, x0, HEAP, lsl #32
    // 0x9b4dcc: cmp             w0, NULL
    // 0x9b4dd0: b.ne            #0x9b4ddc
    // 0x9b4dd4: r3 = 0
    //     0x9b4dd4: movz            x3, #0
    // 0x9b4dd8: b               #0x9b4dec
    // 0x9b4ddc: r1 = LoadInt32Instr(r0)
    //     0x9b4ddc: sbfx            x1, x0, #1, #0x1f
    //     0x9b4de0: tbz             w0, #0, #0x9b4de8
    //     0x9b4de4: ldur            x1, [x0, #7]
    // 0x9b4de8: mov             x3, x1
    // 0x9b4dec: ldur            x2, [fp, #-8]
    // 0x9b4df0: ldur            x1, [fp, #-0x30]
    // 0x9b4df4: ldur            x0, [fp, #-0x48]
    // 0x9b4df8: add             x4, x0, x3
    // 0x9b4dfc: scvtf           d0, x1
    // 0x9b4e00: scvtf           d1, x4
    // 0x9b4e04: fdiv            d2, d0, d1
    // 0x9b4e08: stur            d2, [fp, #-0x68]
    // 0x9b4e0c: LoadField: r1 = r2->field_f
    //     0x9b4e0c: ldur            w1, [x2, #0xf]
    // 0x9b4e10: DecompressPointer r1
    //     0x9b4e10: add             x1, x1, HEAP, lsl #32
    // 0x9b4e14: r0 = controller()
    //     0x9b4e14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4e18: LoadField: r1 = r0->field_7b
    //     0x9b4e18: ldur            w1, [x0, #0x7b]
    // 0x9b4e1c: DecompressPointer r1
    //     0x9b4e1c: add             x1, x1, HEAP, lsl #32
    // 0x9b4e20: r0 = value()
    //     0x9b4e20: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4e24: LoadField: r1 = r0->field_b
    //     0x9b4e24: ldur            w1, [x0, #0xb]
    // 0x9b4e28: DecompressPointer r1
    //     0x9b4e28: add             x1, x1, HEAP, lsl #32
    // 0x9b4e2c: cmp             w1, NULL
    // 0x9b4e30: b.ne            #0x9b4e3c
    // 0x9b4e34: r0 = Null
    //     0x9b4e34: mov             x0, NULL
    // 0x9b4e38: b               #0x9b4e74
    // 0x9b4e3c: LoadField: r0 = r1->field_f
    //     0x9b4e3c: ldur            w0, [x1, #0xf]
    // 0x9b4e40: DecompressPointer r0
    //     0x9b4e40: add             x0, x0, HEAP, lsl #32
    // 0x9b4e44: cmp             w0, NULL
    // 0x9b4e48: b.ne            #0x9b4e54
    // 0x9b4e4c: r0 = Null
    //     0x9b4e4c: mov             x0, NULL
    // 0x9b4e50: b               #0x9b4e74
    // 0x9b4e54: LoadField: r1 = r0->field_1b
    //     0x9b4e54: ldur            w1, [x0, #0x1b]
    // 0x9b4e58: DecompressPointer r1
    //     0x9b4e58: add             x1, x1, HEAP, lsl #32
    // 0x9b4e5c: cmp             w1, NULL
    // 0x9b4e60: b.ne            #0x9b4e6c
    // 0x9b4e64: r0 = Null
    //     0x9b4e64: mov             x0, NULL
    // 0x9b4e68: b               #0x9b4e74
    // 0x9b4e6c: LoadField: r0 = r1->field_13
    //     0x9b4e6c: ldur            w0, [x1, #0x13]
    // 0x9b4e70: DecompressPointer r0
    //     0x9b4e70: add             x0, x0, HEAP, lsl #32
    // 0x9b4e74: cmp             w0, NULL
    // 0x9b4e78: b.ne            #0x9b4e84
    // 0x9b4e7c: r5 = 0
    //     0x9b4e7c: movz            x5, #0
    // 0x9b4e80: b               #0x9b4e94
    // 0x9b4e84: r1 = LoadInt32Instr(r0)
    //     0x9b4e84: sbfx            x1, x0, #1, #0x1f
    //     0x9b4e88: tbz             w0, #0, #0x9b4e90
    //     0x9b4e8c: ldur            x1, [x0, #7]
    // 0x9b4e90: mov             x5, x1
    // 0x9b4e94: ldur            x0, [fp, #-8]
    // 0x9b4e98: ldur            x1, [fp, #-0x20]
    // 0x9b4e9c: ldur            x2, [fp, #-0x18]
    // 0x9b4ea0: ldur            d0, [fp, #-0x68]
    // 0x9b4ea4: r3 = "2"
    //     0x9b4ea4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] "2"
    //     0x9b4ea8: ldr             x3, [x3, #0x8b0]
    // 0x9b4eac: r0 = chartRow()
    //     0x9b4eac: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x9b4eb0: ldur            x2, [fp, #-8]
    // 0x9b4eb4: stur            x0, [fp, #-0x60]
    // 0x9b4eb8: LoadField: r3 = r2->field_f
    //     0x9b4eb8: ldur            w3, [x2, #0xf]
    // 0x9b4ebc: DecompressPointer r3
    //     0x9b4ebc: add             x3, x3, HEAP, lsl #32
    // 0x9b4ec0: stur            x3, [fp, #-0x20]
    // 0x9b4ec4: LoadField: r4 = r2->field_13
    //     0x9b4ec4: ldur            w4, [x2, #0x13]
    // 0x9b4ec8: DecompressPointer r4
    //     0x9b4ec8: add             x4, x4, HEAP, lsl #32
    // 0x9b4ecc: mov             x1, x3
    // 0x9b4ed0: stur            x4, [fp, #-0x18]
    // 0x9b4ed4: r0 = controller()
    //     0x9b4ed4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4ed8: LoadField: r1 = r0->field_7b
    //     0x9b4ed8: ldur            w1, [x0, #0x7b]
    // 0x9b4edc: DecompressPointer r1
    //     0x9b4edc: add             x1, x1, HEAP, lsl #32
    // 0x9b4ee0: r0 = value()
    //     0x9b4ee0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4ee4: LoadField: r1 = r0->field_b
    //     0x9b4ee4: ldur            w1, [x0, #0xb]
    // 0x9b4ee8: DecompressPointer r1
    //     0x9b4ee8: add             x1, x1, HEAP, lsl #32
    // 0x9b4eec: cmp             w1, NULL
    // 0x9b4ef0: b.ne            #0x9b4efc
    // 0x9b4ef4: r0 = Null
    //     0x9b4ef4: mov             x0, NULL
    // 0x9b4ef8: b               #0x9b4f34
    // 0x9b4efc: LoadField: r0 = r1->field_f
    //     0x9b4efc: ldur            w0, [x1, #0xf]
    // 0x9b4f00: DecompressPointer r0
    //     0x9b4f00: add             x0, x0, HEAP, lsl #32
    // 0x9b4f04: cmp             w0, NULL
    // 0x9b4f08: b.ne            #0x9b4f14
    // 0x9b4f0c: r0 = Null
    //     0x9b4f0c: mov             x0, NULL
    // 0x9b4f10: b               #0x9b4f34
    // 0x9b4f14: LoadField: r1 = r0->field_1b
    //     0x9b4f14: ldur            w1, [x0, #0x1b]
    // 0x9b4f18: DecompressPointer r1
    //     0x9b4f18: add             x1, x1, HEAP, lsl #32
    // 0x9b4f1c: cmp             w1, NULL
    // 0x9b4f20: b.ne            #0x9b4f2c
    // 0x9b4f24: r0 = Null
    //     0x9b4f24: mov             x0, NULL
    // 0x9b4f28: b               #0x9b4f34
    // 0x9b4f2c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b4f2c: ldur            w0, [x1, #0x17]
    // 0x9b4f30: DecompressPointer r0
    //     0x9b4f30: add             x0, x0, HEAP, lsl #32
    // 0x9b4f34: cmp             w0, NULL
    // 0x9b4f38: b.ne            #0x9b4f44
    // 0x9b4f3c: r0 = 0
    //     0x9b4f3c: movz            x0, #0
    // 0x9b4f40: b               #0x9b4f54
    // 0x9b4f44: r1 = LoadInt32Instr(r0)
    //     0x9b4f44: sbfx            x1, x0, #1, #0x1f
    //     0x9b4f48: tbz             w0, #0, #0x9b4f50
    //     0x9b4f4c: ldur            x1, [x0, #7]
    // 0x9b4f50: mov             x0, x1
    // 0x9b4f54: ldur            x2, [fp, #-8]
    // 0x9b4f58: stur            x0, [fp, #-0x30]
    // 0x9b4f5c: LoadField: r1 = r2->field_f
    //     0x9b4f5c: ldur            w1, [x2, #0xf]
    // 0x9b4f60: DecompressPointer r1
    //     0x9b4f60: add             x1, x1, HEAP, lsl #32
    // 0x9b4f64: r0 = controller()
    //     0x9b4f64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4f68: LoadField: r1 = r0->field_7b
    //     0x9b4f68: ldur            w1, [x0, #0x7b]
    // 0x9b4f6c: DecompressPointer r1
    //     0x9b4f6c: add             x1, x1, HEAP, lsl #32
    // 0x9b4f70: r0 = value()
    //     0x9b4f70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b4f74: LoadField: r1 = r0->field_b
    //     0x9b4f74: ldur            w1, [x0, #0xb]
    // 0x9b4f78: DecompressPointer r1
    //     0x9b4f78: add             x1, x1, HEAP, lsl #32
    // 0x9b4f7c: cmp             w1, NULL
    // 0x9b4f80: b.ne            #0x9b4f8c
    // 0x9b4f84: r0 = Null
    //     0x9b4f84: mov             x0, NULL
    // 0x9b4f88: b               #0x9b4fc4
    // 0x9b4f8c: LoadField: r0 = r1->field_f
    //     0x9b4f8c: ldur            w0, [x1, #0xf]
    // 0x9b4f90: DecompressPointer r0
    //     0x9b4f90: add             x0, x0, HEAP, lsl #32
    // 0x9b4f94: cmp             w0, NULL
    // 0x9b4f98: b.ne            #0x9b4fa4
    // 0x9b4f9c: r0 = Null
    //     0x9b4f9c: mov             x0, NULL
    // 0x9b4fa0: b               #0x9b4fc4
    // 0x9b4fa4: LoadField: r1 = r0->field_1b
    //     0x9b4fa4: ldur            w1, [x0, #0x1b]
    // 0x9b4fa8: DecompressPointer r1
    //     0x9b4fa8: add             x1, x1, HEAP, lsl #32
    // 0x9b4fac: cmp             w1, NULL
    // 0x9b4fb0: b.ne            #0x9b4fbc
    // 0x9b4fb4: r0 = Null
    //     0x9b4fb4: mov             x0, NULL
    // 0x9b4fb8: b               #0x9b4fc4
    // 0x9b4fbc: LoadField: r0 = r1->field_7
    //     0x9b4fbc: ldur            w0, [x1, #7]
    // 0x9b4fc0: DecompressPointer r0
    //     0x9b4fc0: add             x0, x0, HEAP, lsl #32
    // 0x9b4fc4: cmp             w0, NULL
    // 0x9b4fc8: b.ne            #0x9b4fd4
    // 0x9b4fcc: r0 = 0
    //     0x9b4fcc: movz            x0, #0
    // 0x9b4fd0: b               #0x9b4fe4
    // 0x9b4fd4: r1 = LoadInt32Instr(r0)
    //     0x9b4fd4: sbfx            x1, x0, #1, #0x1f
    //     0x9b4fd8: tbz             w0, #0, #0x9b4fe0
    //     0x9b4fdc: ldur            x1, [x0, #7]
    // 0x9b4fe0: mov             x0, x1
    // 0x9b4fe4: ldur            x2, [fp, #-8]
    // 0x9b4fe8: stur            x0, [fp, #-0x40]
    // 0x9b4fec: LoadField: r1 = r2->field_f
    //     0x9b4fec: ldur            w1, [x2, #0xf]
    // 0x9b4ff0: DecompressPointer r1
    //     0x9b4ff0: add             x1, x1, HEAP, lsl #32
    // 0x9b4ff4: r0 = controller()
    //     0x9b4ff4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b4ff8: LoadField: r1 = r0->field_7b
    //     0x9b4ff8: ldur            w1, [x0, #0x7b]
    // 0x9b4ffc: DecompressPointer r1
    //     0x9b4ffc: add             x1, x1, HEAP, lsl #32
    // 0x9b5000: r0 = value()
    //     0x9b5000: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b5004: LoadField: r1 = r0->field_b
    //     0x9b5004: ldur            w1, [x0, #0xb]
    // 0x9b5008: DecompressPointer r1
    //     0x9b5008: add             x1, x1, HEAP, lsl #32
    // 0x9b500c: cmp             w1, NULL
    // 0x9b5010: b.ne            #0x9b501c
    // 0x9b5014: r0 = Null
    //     0x9b5014: mov             x0, NULL
    // 0x9b5018: b               #0x9b5054
    // 0x9b501c: LoadField: r0 = r1->field_f
    //     0x9b501c: ldur            w0, [x1, #0xf]
    // 0x9b5020: DecompressPointer r0
    //     0x9b5020: add             x0, x0, HEAP, lsl #32
    // 0x9b5024: cmp             w0, NULL
    // 0x9b5028: b.ne            #0x9b5034
    // 0x9b502c: r0 = Null
    //     0x9b502c: mov             x0, NULL
    // 0x9b5030: b               #0x9b5054
    // 0x9b5034: LoadField: r1 = r0->field_1b
    //     0x9b5034: ldur            w1, [x0, #0x1b]
    // 0x9b5038: DecompressPointer r1
    //     0x9b5038: add             x1, x1, HEAP, lsl #32
    // 0x9b503c: cmp             w1, NULL
    // 0x9b5040: b.ne            #0x9b504c
    // 0x9b5044: r0 = Null
    //     0x9b5044: mov             x0, NULL
    // 0x9b5048: b               #0x9b5054
    // 0x9b504c: LoadField: r0 = r1->field_b
    //     0x9b504c: ldur            w0, [x1, #0xb]
    // 0x9b5050: DecompressPointer r0
    //     0x9b5050: add             x0, x0, HEAP, lsl #32
    // 0x9b5054: cmp             w0, NULL
    // 0x9b5058: b.ne            #0x9b5064
    // 0x9b505c: r1 = 0
    //     0x9b505c: movz            x1, #0
    // 0x9b5060: b               #0x9b5070
    // 0x9b5064: r1 = LoadInt32Instr(r0)
    //     0x9b5064: sbfx            x1, x0, #1, #0x1f
    //     0x9b5068: tbz             w0, #0, #0x9b5070
    //     0x9b506c: ldur            x1, [x0, #7]
    // 0x9b5070: ldur            x2, [fp, #-8]
    // 0x9b5074: ldur            x0, [fp, #-0x40]
    // 0x9b5078: add             x3, x0, x1
    // 0x9b507c: stur            x3, [fp, #-0x48]
    // 0x9b5080: LoadField: r1 = r2->field_f
    //     0x9b5080: ldur            w1, [x2, #0xf]
    // 0x9b5084: DecompressPointer r1
    //     0x9b5084: add             x1, x1, HEAP, lsl #32
    // 0x9b5088: r0 = controller()
    //     0x9b5088: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b508c: LoadField: r1 = r0->field_7b
    //     0x9b508c: ldur            w1, [x0, #0x7b]
    // 0x9b5090: DecompressPointer r1
    //     0x9b5090: add             x1, x1, HEAP, lsl #32
    // 0x9b5094: r0 = value()
    //     0x9b5094: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b5098: LoadField: r1 = r0->field_b
    //     0x9b5098: ldur            w1, [x0, #0xb]
    // 0x9b509c: DecompressPointer r1
    //     0x9b509c: add             x1, x1, HEAP, lsl #32
    // 0x9b50a0: cmp             w1, NULL
    // 0x9b50a4: b.ne            #0x9b50b0
    // 0x9b50a8: r0 = Null
    //     0x9b50a8: mov             x0, NULL
    // 0x9b50ac: b               #0x9b50e8
    // 0x9b50b0: LoadField: r0 = r1->field_f
    //     0x9b50b0: ldur            w0, [x1, #0xf]
    // 0x9b50b4: DecompressPointer r0
    //     0x9b50b4: add             x0, x0, HEAP, lsl #32
    // 0x9b50b8: cmp             w0, NULL
    // 0x9b50bc: b.ne            #0x9b50c8
    // 0x9b50c0: r0 = Null
    //     0x9b50c0: mov             x0, NULL
    // 0x9b50c4: b               #0x9b50e8
    // 0x9b50c8: LoadField: r1 = r0->field_1b
    //     0x9b50c8: ldur            w1, [x0, #0x1b]
    // 0x9b50cc: DecompressPointer r1
    //     0x9b50cc: add             x1, x1, HEAP, lsl #32
    // 0x9b50d0: cmp             w1, NULL
    // 0x9b50d4: b.ne            #0x9b50e0
    // 0x9b50d8: r0 = Null
    //     0x9b50d8: mov             x0, NULL
    // 0x9b50dc: b               #0x9b50e8
    // 0x9b50e0: LoadField: r0 = r1->field_f
    //     0x9b50e0: ldur            w0, [x1, #0xf]
    // 0x9b50e4: DecompressPointer r0
    //     0x9b50e4: add             x0, x0, HEAP, lsl #32
    // 0x9b50e8: cmp             w0, NULL
    // 0x9b50ec: b.ne            #0x9b50f8
    // 0x9b50f0: r1 = 0
    //     0x9b50f0: movz            x1, #0
    // 0x9b50f4: b               #0x9b5104
    // 0x9b50f8: r1 = LoadInt32Instr(r0)
    //     0x9b50f8: sbfx            x1, x0, #1, #0x1f
    //     0x9b50fc: tbz             w0, #0, #0x9b5104
    //     0x9b5100: ldur            x1, [x0, #7]
    // 0x9b5104: ldur            x2, [fp, #-8]
    // 0x9b5108: ldur            x0, [fp, #-0x48]
    // 0x9b510c: add             x3, x0, x1
    // 0x9b5110: stur            x3, [fp, #-0x40]
    // 0x9b5114: LoadField: r1 = r2->field_f
    //     0x9b5114: ldur            w1, [x2, #0xf]
    // 0x9b5118: DecompressPointer r1
    //     0x9b5118: add             x1, x1, HEAP, lsl #32
    // 0x9b511c: r0 = controller()
    //     0x9b511c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b5120: LoadField: r1 = r0->field_7b
    //     0x9b5120: ldur            w1, [x0, #0x7b]
    // 0x9b5124: DecompressPointer r1
    //     0x9b5124: add             x1, x1, HEAP, lsl #32
    // 0x9b5128: r0 = value()
    //     0x9b5128: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b512c: LoadField: r1 = r0->field_b
    //     0x9b512c: ldur            w1, [x0, #0xb]
    // 0x9b5130: DecompressPointer r1
    //     0x9b5130: add             x1, x1, HEAP, lsl #32
    // 0x9b5134: cmp             w1, NULL
    // 0x9b5138: b.ne            #0x9b5144
    // 0x9b513c: r0 = Null
    //     0x9b513c: mov             x0, NULL
    // 0x9b5140: b               #0x9b517c
    // 0x9b5144: LoadField: r0 = r1->field_f
    //     0x9b5144: ldur            w0, [x1, #0xf]
    // 0x9b5148: DecompressPointer r0
    //     0x9b5148: add             x0, x0, HEAP, lsl #32
    // 0x9b514c: cmp             w0, NULL
    // 0x9b5150: b.ne            #0x9b515c
    // 0x9b5154: r0 = Null
    //     0x9b5154: mov             x0, NULL
    // 0x9b5158: b               #0x9b517c
    // 0x9b515c: LoadField: r1 = r0->field_1b
    //     0x9b515c: ldur            w1, [x0, #0x1b]
    // 0x9b5160: DecompressPointer r1
    //     0x9b5160: add             x1, x1, HEAP, lsl #32
    // 0x9b5164: cmp             w1, NULL
    // 0x9b5168: b.ne            #0x9b5174
    // 0x9b516c: r0 = Null
    //     0x9b516c: mov             x0, NULL
    // 0x9b5170: b               #0x9b517c
    // 0x9b5174: LoadField: r0 = r1->field_13
    //     0x9b5174: ldur            w0, [x1, #0x13]
    // 0x9b5178: DecompressPointer r0
    //     0x9b5178: add             x0, x0, HEAP, lsl #32
    // 0x9b517c: cmp             w0, NULL
    // 0x9b5180: b.ne            #0x9b518c
    // 0x9b5184: r1 = 0
    //     0x9b5184: movz            x1, #0
    // 0x9b5188: b               #0x9b5198
    // 0x9b518c: r1 = LoadInt32Instr(r0)
    //     0x9b518c: sbfx            x1, x0, #1, #0x1f
    //     0x9b5190: tbz             w0, #0, #0x9b5198
    //     0x9b5194: ldur            x1, [x0, #7]
    // 0x9b5198: ldur            x2, [fp, #-8]
    // 0x9b519c: ldur            x0, [fp, #-0x40]
    // 0x9b51a0: add             x3, x0, x1
    // 0x9b51a4: stur            x3, [fp, #-0x48]
    // 0x9b51a8: LoadField: r1 = r2->field_f
    //     0x9b51a8: ldur            w1, [x2, #0xf]
    // 0x9b51ac: DecompressPointer r1
    //     0x9b51ac: add             x1, x1, HEAP, lsl #32
    // 0x9b51b0: r0 = controller()
    //     0x9b51b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b51b4: LoadField: r1 = r0->field_7b
    //     0x9b51b4: ldur            w1, [x0, #0x7b]
    // 0x9b51b8: DecompressPointer r1
    //     0x9b51b8: add             x1, x1, HEAP, lsl #32
    // 0x9b51bc: r0 = value()
    //     0x9b51bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b51c0: LoadField: r1 = r0->field_b
    //     0x9b51c0: ldur            w1, [x0, #0xb]
    // 0x9b51c4: DecompressPointer r1
    //     0x9b51c4: add             x1, x1, HEAP, lsl #32
    // 0x9b51c8: cmp             w1, NULL
    // 0x9b51cc: b.ne            #0x9b51d8
    // 0x9b51d0: r0 = Null
    //     0x9b51d0: mov             x0, NULL
    // 0x9b51d4: b               #0x9b5210
    // 0x9b51d8: LoadField: r0 = r1->field_f
    //     0x9b51d8: ldur            w0, [x1, #0xf]
    // 0x9b51dc: DecompressPointer r0
    //     0x9b51dc: add             x0, x0, HEAP, lsl #32
    // 0x9b51e0: cmp             w0, NULL
    // 0x9b51e4: b.ne            #0x9b51f0
    // 0x9b51e8: r0 = Null
    //     0x9b51e8: mov             x0, NULL
    // 0x9b51ec: b               #0x9b5210
    // 0x9b51f0: LoadField: r1 = r0->field_1b
    //     0x9b51f0: ldur            w1, [x0, #0x1b]
    // 0x9b51f4: DecompressPointer r1
    //     0x9b51f4: add             x1, x1, HEAP, lsl #32
    // 0x9b51f8: cmp             w1, NULL
    // 0x9b51fc: b.ne            #0x9b5208
    // 0x9b5200: r0 = Null
    //     0x9b5200: mov             x0, NULL
    // 0x9b5204: b               #0x9b5210
    // 0x9b5208: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b5208: ldur            w0, [x1, #0x17]
    // 0x9b520c: DecompressPointer r0
    //     0x9b520c: add             x0, x0, HEAP, lsl #32
    // 0x9b5210: cmp             w0, NULL
    // 0x9b5214: b.ne            #0x9b5220
    // 0x9b5218: r3 = 0
    //     0x9b5218: movz            x3, #0
    // 0x9b521c: b               #0x9b5230
    // 0x9b5220: r1 = LoadInt32Instr(r0)
    //     0x9b5220: sbfx            x1, x0, #1, #0x1f
    //     0x9b5224: tbz             w0, #0, #0x9b522c
    //     0x9b5228: ldur            x1, [x0, #7]
    // 0x9b522c: mov             x3, x1
    // 0x9b5230: ldur            x2, [fp, #-8]
    // 0x9b5234: ldur            x1, [fp, #-0x30]
    // 0x9b5238: ldur            x0, [fp, #-0x48]
    // 0x9b523c: add             x4, x0, x3
    // 0x9b5240: scvtf           d0, x1
    // 0x9b5244: scvtf           d1, x4
    // 0x9b5248: fdiv            d2, d0, d1
    // 0x9b524c: stur            d2, [fp, #-0x68]
    // 0x9b5250: LoadField: r1 = r2->field_f
    //     0x9b5250: ldur            w1, [x2, #0xf]
    // 0x9b5254: DecompressPointer r1
    //     0x9b5254: add             x1, x1, HEAP, lsl #32
    // 0x9b5258: r0 = controller()
    //     0x9b5258: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b525c: LoadField: r1 = r0->field_7b
    //     0x9b525c: ldur            w1, [x0, #0x7b]
    // 0x9b5260: DecompressPointer r1
    //     0x9b5260: add             x1, x1, HEAP, lsl #32
    // 0x9b5264: r0 = value()
    //     0x9b5264: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b5268: LoadField: r1 = r0->field_b
    //     0x9b5268: ldur            w1, [x0, #0xb]
    // 0x9b526c: DecompressPointer r1
    //     0x9b526c: add             x1, x1, HEAP, lsl #32
    // 0x9b5270: cmp             w1, NULL
    // 0x9b5274: b.ne            #0x9b5280
    // 0x9b5278: r0 = Null
    //     0x9b5278: mov             x0, NULL
    // 0x9b527c: b               #0x9b52b8
    // 0x9b5280: LoadField: r0 = r1->field_f
    //     0x9b5280: ldur            w0, [x1, #0xf]
    // 0x9b5284: DecompressPointer r0
    //     0x9b5284: add             x0, x0, HEAP, lsl #32
    // 0x9b5288: cmp             w0, NULL
    // 0x9b528c: b.ne            #0x9b5298
    // 0x9b5290: r0 = Null
    //     0x9b5290: mov             x0, NULL
    // 0x9b5294: b               #0x9b52b8
    // 0x9b5298: LoadField: r1 = r0->field_1b
    //     0x9b5298: ldur            w1, [x0, #0x1b]
    // 0x9b529c: DecompressPointer r1
    //     0x9b529c: add             x1, x1, HEAP, lsl #32
    // 0x9b52a0: cmp             w1, NULL
    // 0x9b52a4: b.ne            #0x9b52b0
    // 0x9b52a8: r0 = Null
    //     0x9b52a8: mov             x0, NULL
    // 0x9b52ac: b               #0x9b52b8
    // 0x9b52b0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9b52b0: ldur            w0, [x1, #0x17]
    // 0x9b52b4: DecompressPointer r0
    //     0x9b52b4: add             x0, x0, HEAP, lsl #32
    // 0x9b52b8: cmp             w0, NULL
    // 0x9b52bc: b.ne            #0x9b52c8
    // 0x9b52c0: r5 = 0
    //     0x9b52c0: movz            x5, #0
    // 0x9b52c4: b               #0x9b52d8
    // 0x9b52c8: r1 = LoadInt32Instr(r0)
    //     0x9b52c8: sbfx            x1, x0, #1, #0x1f
    //     0x9b52cc: tbz             w0, #0, #0x9b52d4
    //     0x9b52d0: ldur            x1, [x0, #7]
    // 0x9b52d4: mov             x5, x1
    // 0x9b52d8: ldur            x0, [fp, #-8]
    // 0x9b52dc: ldur            x10, [fp, #-0x10]
    // 0x9b52e0: ldur            x9, [fp, #-0x28]
    // 0x9b52e4: ldur            x8, [fp, #-0x38]
    // 0x9b52e8: ldur            x7, [fp, #-0x50]
    // 0x9b52ec: ldur            x6, [fp, #-0x58]
    // 0x9b52f0: ldur            x4, [fp, #-0x60]
    // 0x9b52f4: ldur            x1, [fp, #-0x20]
    // 0x9b52f8: ldur            x2, [fp, #-0x18]
    // 0x9b52fc: ldur            d0, [fp, #-0x68]
    // 0x9b5300: r3 = "1"
    //     0x9b5300: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0x9b5304: ldr             x3, [x3, #0xba8]
    // 0x9b5308: r0 = chartRow()
    //     0x9b5308: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0x9b530c: r1 = Null
    //     0x9b530c: mov             x1, NULL
    // 0x9b5310: r2 = 14
    //     0x9b5310: movz            x2, #0xe
    // 0x9b5314: stur            x0, [fp, #-0x18]
    // 0x9b5318: r0 = AllocateArray()
    //     0x9b5318: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b531c: stur            x0, [fp, #-0x20]
    // 0x9b5320: r16 = Instance_SizedBox
    //     0x9b5320: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x9b5324: ldr             x16, [x16, #0x8b8]
    // 0x9b5328: StoreField: r0->field_f = r16
    //     0x9b5328: stur            w16, [x0, #0xf]
    // 0x9b532c: ldur            x1, [fp, #-0x38]
    // 0x9b5330: StoreField: r0->field_13 = r1
    //     0x9b5330: stur            w1, [x0, #0x13]
    // 0x9b5334: ldur            x1, [fp, #-0x50]
    // 0x9b5338: ArrayStore: r0[0] = r1  ; List_4
    //     0x9b5338: stur            w1, [x0, #0x17]
    // 0x9b533c: ldur            x1, [fp, #-0x58]
    // 0x9b5340: StoreField: r0->field_1b = r1
    //     0x9b5340: stur            w1, [x0, #0x1b]
    // 0x9b5344: ldur            x1, [fp, #-0x60]
    // 0x9b5348: StoreField: r0->field_1f = r1
    //     0x9b5348: stur            w1, [x0, #0x1f]
    // 0x9b534c: ldur            x1, [fp, #-0x18]
    // 0x9b5350: StoreField: r0->field_23 = r1
    //     0x9b5350: stur            w1, [x0, #0x23]
    // 0x9b5354: r16 = Instance_SizedBox
    //     0x9b5354: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x9b5358: ldr             x16, [x16, #0x8b8]
    // 0x9b535c: StoreField: r0->field_27 = r16
    //     0x9b535c: stur            w16, [x0, #0x27]
    // 0x9b5360: r1 = <Widget>
    //     0x9b5360: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b5364: r0 = AllocateGrowableArray()
    //     0x9b5364: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b5368: mov             x1, x0
    // 0x9b536c: ldur            x0, [fp, #-0x20]
    // 0x9b5370: stur            x1, [fp, #-0x18]
    // 0x9b5374: StoreField: r1->field_f = r0
    //     0x9b5374: stur            w0, [x1, #0xf]
    // 0x9b5378: r0 = 14
    //     0x9b5378: movz            x0, #0xe
    // 0x9b537c: StoreField: r1->field_b = r0
    //     0x9b537c: stur            w0, [x1, #0xb]
    // 0x9b5380: r0 = Column()
    //     0x9b5380: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b5384: mov             x3, x0
    // 0x9b5388: r0 = Instance_Axis
    //     0x9b5388: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b538c: stur            x3, [fp, #-0x20]
    // 0x9b5390: StoreField: r3->field_f = r0
    //     0x9b5390: stur            w0, [x3, #0xf]
    // 0x9b5394: r4 = Instance_MainAxisAlignment
    //     0x9b5394: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b5398: ldr             x4, [x4, #0xa08]
    // 0x9b539c: StoreField: r3->field_13 = r4
    //     0x9b539c: stur            w4, [x3, #0x13]
    // 0x9b53a0: r5 = Instance_MainAxisSize
    //     0x9b53a0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b53a4: ldr             x5, [x5, #0xa10]
    // 0x9b53a8: ArrayStore: r3[0] = r5  ; List_4
    //     0x9b53a8: stur            w5, [x3, #0x17]
    // 0x9b53ac: r1 = Instance_CrossAxisAlignment
    //     0x9b53ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x9b53b0: ldr             x1, [x1, #0x890]
    // 0x9b53b4: StoreField: r3->field_1b = r1
    //     0x9b53b4: stur            w1, [x3, #0x1b]
    // 0x9b53b8: r6 = Instance_VerticalDirection
    //     0x9b53b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b53bc: ldr             x6, [x6, #0xa20]
    // 0x9b53c0: StoreField: r3->field_23 = r6
    //     0x9b53c0: stur            w6, [x3, #0x23]
    // 0x9b53c4: r7 = Instance_Clip
    //     0x9b53c4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b53c8: ldr             x7, [x7, #0x38]
    // 0x9b53cc: StoreField: r3->field_2b = r7
    //     0x9b53cc: stur            w7, [x3, #0x2b]
    // 0x9b53d0: StoreField: r3->field_2f = rZR
    //     0x9b53d0: stur            xzr, [x3, #0x2f]
    // 0x9b53d4: ldur            x1, [fp, #-0x18]
    // 0x9b53d8: StoreField: r3->field_b = r1
    //     0x9b53d8: stur            w1, [x3, #0xb]
    // 0x9b53dc: r1 = Null
    //     0x9b53dc: mov             x1, NULL
    // 0x9b53e0: r2 = 4
    //     0x9b53e0: movz            x2, #0x4
    // 0x9b53e4: r0 = AllocateArray()
    //     0x9b53e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b53e8: mov             x2, x0
    // 0x9b53ec: ldur            x0, [fp, #-0x28]
    // 0x9b53f0: stur            x2, [fp, #-0x18]
    // 0x9b53f4: StoreField: r2->field_f = r0
    //     0x9b53f4: stur            w0, [x2, #0xf]
    // 0x9b53f8: ldur            x0, [fp, #-0x20]
    // 0x9b53fc: StoreField: r2->field_13 = r0
    //     0x9b53fc: stur            w0, [x2, #0x13]
    // 0x9b5400: r1 = <Widget>
    //     0x9b5400: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b5404: r0 = AllocateGrowableArray()
    //     0x9b5404: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b5408: mov             x1, x0
    // 0x9b540c: ldur            x0, [fp, #-0x18]
    // 0x9b5410: stur            x1, [fp, #-0x20]
    // 0x9b5414: StoreField: r1->field_f = r0
    //     0x9b5414: stur            w0, [x1, #0xf]
    // 0x9b5418: r0 = 4
    //     0x9b5418: movz            x0, #0x4
    // 0x9b541c: StoreField: r1->field_b = r0
    //     0x9b541c: stur            w0, [x1, #0xb]
    // 0x9b5420: r0 = Row()
    //     0x9b5420: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b5424: mov             x1, x0
    // 0x9b5428: r0 = Instance_Axis
    //     0x9b5428: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b542c: stur            x1, [fp, #-0x18]
    // 0x9b5430: StoreField: r1->field_f = r0
    //     0x9b5430: stur            w0, [x1, #0xf]
    // 0x9b5434: r2 = Instance_MainAxisAlignment
    //     0x9b5434: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x9b5438: ldr             x2, [x2, #0xa8]
    // 0x9b543c: StoreField: r1->field_13 = r2
    //     0x9b543c: stur            w2, [x1, #0x13]
    // 0x9b5440: r2 = Instance_MainAxisSize
    //     0x9b5440: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b5444: ldr             x2, [x2, #0xa10]
    // 0x9b5448: ArrayStore: r1[0] = r2  ; List_4
    //     0x9b5448: stur            w2, [x1, #0x17]
    // 0x9b544c: r3 = Instance_CrossAxisAlignment
    //     0x9b544c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b5450: ldr             x3, [x3, #0xa18]
    // 0x9b5454: StoreField: r1->field_1b = r3
    //     0x9b5454: stur            w3, [x1, #0x1b]
    // 0x9b5458: r4 = Instance_VerticalDirection
    //     0x9b5458: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b545c: ldr             x4, [x4, #0xa20]
    // 0x9b5460: StoreField: r1->field_23 = r4
    //     0x9b5460: stur            w4, [x1, #0x23]
    // 0x9b5464: r5 = Instance_Clip
    //     0x9b5464: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b5468: ldr             x5, [x5, #0x38]
    // 0x9b546c: StoreField: r1->field_2b = r5
    //     0x9b546c: stur            w5, [x1, #0x2b]
    // 0x9b5470: StoreField: r1->field_2f = rZR
    //     0x9b5470: stur            xzr, [x1, #0x2f]
    // 0x9b5474: ldur            x6, [fp, #-0x20]
    // 0x9b5478: StoreField: r1->field_b = r6
    //     0x9b5478: stur            w6, [x1, #0xb]
    // 0x9b547c: r0 = Visibility()
    //     0x9b547c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x9b5480: mov             x3, x0
    // 0x9b5484: ldur            x0, [fp, #-0x18]
    // 0x9b5488: stur            x3, [fp, #-0x20]
    // 0x9b548c: StoreField: r3->field_b = r0
    //     0x9b548c: stur            w0, [x3, #0xb]
    // 0x9b5490: r0 = Instance_SizedBox
    //     0x9b5490: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x9b5494: StoreField: r3->field_f = r0
    //     0x9b5494: stur            w0, [x3, #0xf]
    // 0x9b5498: ldur            x0, [fp, #-0x10]
    // 0x9b549c: StoreField: r3->field_13 = r0
    //     0x9b549c: stur            w0, [x3, #0x13]
    // 0x9b54a0: r0 = false
    //     0x9b54a0: add             x0, NULL, #0x30  ; false
    // 0x9b54a4: ArrayStore: r3[0] = r0  ; List_4
    //     0x9b54a4: stur            w0, [x3, #0x17]
    // 0x9b54a8: StoreField: r3->field_1b = r0
    //     0x9b54a8: stur            w0, [x3, #0x1b]
    // 0x9b54ac: StoreField: r3->field_1f = r0
    //     0x9b54ac: stur            w0, [x3, #0x1f]
    // 0x9b54b0: StoreField: r3->field_23 = r0
    //     0x9b54b0: stur            w0, [x3, #0x23]
    // 0x9b54b4: StoreField: r3->field_27 = r0
    //     0x9b54b4: stur            w0, [x3, #0x27]
    // 0x9b54b8: StoreField: r3->field_2b = r0
    //     0x9b54b8: stur            w0, [x3, #0x2b]
    // 0x9b54bc: r1 = Null
    //     0x9b54bc: mov             x1, NULL
    // 0x9b54c0: r2 = 2
    //     0x9b54c0: movz            x2, #0x2
    // 0x9b54c4: r0 = AllocateArray()
    //     0x9b54c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b54c8: mov             x2, x0
    // 0x9b54cc: ldur            x0, [fp, #-0x20]
    // 0x9b54d0: stur            x2, [fp, #-0x10]
    // 0x9b54d4: StoreField: r2->field_f = r0
    //     0x9b54d4: stur            w0, [x2, #0xf]
    // 0x9b54d8: r1 = <Widget>
    //     0x9b54d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b54dc: r0 = AllocateGrowableArray()
    //     0x9b54dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b54e0: mov             x2, x0
    // 0x9b54e4: ldur            x0, [fp, #-0x10]
    // 0x9b54e8: stur            x2, [fp, #-0x18]
    // 0x9b54ec: StoreField: r2->field_f = r0
    //     0x9b54ec: stur            w0, [x2, #0xf]
    // 0x9b54f0: r0 = 2
    //     0x9b54f0: movz            x0, #0x2
    // 0x9b54f4: StoreField: r2->field_b = r0
    //     0x9b54f4: stur            w0, [x2, #0xb]
    // 0x9b54f8: ldur            x0, [fp, #-8]
    // 0x9b54fc: LoadField: r1 = r0->field_f
    //     0x9b54fc: ldur            w1, [x0, #0xf]
    // 0x9b5500: DecompressPointer r1
    //     0x9b5500: add             x1, x1, HEAP, lsl #32
    // 0x9b5504: r0 = controller()
    //     0x9b5504: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b5508: LoadField: r1 = r0->field_7f
    //     0x9b5508: ldur            w1, [x0, #0x7f]
    // 0x9b550c: DecompressPointer r1
    //     0x9b550c: add             x1, x1, HEAP, lsl #32
    // 0x9b5510: r0 = value()
    //     0x9b5510: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b5514: LoadField: r3 = r0->field_f
    //     0x9b5514: ldur            w3, [x0, #0xf]
    // 0x9b5518: DecompressPointer r3
    //     0x9b5518: add             x3, x3, HEAP, lsl #32
    // 0x9b551c: stur            x3, [fp, #-0x10]
    // 0x9b5520: cmp             w3, NULL
    // 0x9b5524: b.ne            #0x9b5530
    // 0x9b5528: r0 = Null
    //     0x9b5528: mov             x0, NULL
    // 0x9b552c: b               #0x9b5584
    // 0x9b5530: r1 = Function '<anonymous closure>':.
    //     0x9b5530: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8c0] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x9b5534: ldr             x1, [x1, #0x8c0]
    // 0x9b5538: r2 = Null
    //     0x9b5538: mov             x2, NULL
    // 0x9b553c: r0 = AllocateClosure()
    //     0x9b553c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5540: ldur            x16, [fp, #-0x10]
    // 0x9b5544: stp             x16, NULL, [SP, #8]
    // 0x9b5548: str             x0, [SP]
    // 0x9b554c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9b554c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9b5550: r0 = expand()
    //     0x9b5550: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x9b5554: mov             x1, x0
    // 0x9b5558: r0 = iterator()
    //     0x9b5558: bl              #0x7e2acc  ; [dart:_internal] ExpandIterable::iterator
    // 0x9b555c: r1 = LoadClassIdInstr(r0)
    //     0x9b555c: ldur            x1, [x0, #-1]
    //     0x9b5560: ubfx            x1, x1, #0xc, #0x14
    // 0x9b5564: mov             x16, x0
    // 0x9b5568: mov             x0, x1
    // 0x9b556c: mov             x1, x16
    // 0x9b5570: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x9b5570: add             lr, x0, #0x5ea
    //     0x9b5574: ldr             lr, [x21, lr, lsl #3]
    //     0x9b5578: blr             lr
    // 0x9b557c: eor             x1, x0, #0x10
    // 0x9b5580: eor             x0, x1, #0x10
    // 0x9b5584: cmp             w0, NULL
    // 0x9b5588: b.ne            #0x9b5594
    // 0x9b558c: ldur            x2, [fp, #-0x18]
    // 0x9b5590: b               #0x9b58dc
    // 0x9b5594: tbnz            w0, #4, #0x9b58d8
    // 0x9b5598: ldur            x2, [fp, #-8]
    // 0x9b559c: LoadField: r1 = r2->field_13
    //     0x9b559c: ldur            w1, [x2, #0x13]
    // 0x9b55a0: DecompressPointer r1
    //     0x9b55a0: add             x1, x1, HEAP, lsl #32
    // 0x9b55a4: r0 = of()
    //     0x9b55a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b55a8: LoadField: r1 = r0->field_87
    //     0x9b55a8: ldur            w1, [x0, #0x87]
    // 0x9b55ac: DecompressPointer r1
    //     0x9b55ac: add             x1, x1, HEAP, lsl #32
    // 0x9b55b0: LoadField: r0 = r1->field_2f
    //     0x9b55b0: ldur            w0, [x1, #0x2f]
    // 0x9b55b4: DecompressPointer r0
    //     0x9b55b4: add             x0, x0, HEAP, lsl #32
    // 0x9b55b8: cmp             w0, NULL
    // 0x9b55bc: b.ne            #0x9b55c8
    // 0x9b55c0: r0 = Null
    //     0x9b55c0: mov             x0, NULL
    // 0x9b55c4: b               #0x9b55e8
    // 0x9b55c8: r16 = Instance_Color
    //     0x9b55c8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b55cc: r30 = 14.000000
    //     0x9b55cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x9b55d0: ldr             lr, [lr, #0x1d8]
    // 0x9b55d4: stp             lr, x16, [SP]
    // 0x9b55d8: mov             x1, x0
    // 0x9b55dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9b55dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9b55e0: ldr             x4, [x4, #0x9b8]
    // 0x9b55e4: r0 = copyWith()
    //     0x9b55e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b55e8: ldur            x2, [fp, #-8]
    // 0x9b55ec: stur            x0, [fp, #-0x10]
    // 0x9b55f0: r0 = Text()
    //     0x9b55f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b55f4: mov             x2, x0
    // 0x9b55f8: r0 = "Real images from customers"
    //     0x9b55f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0x9b55fc: ldr             x0, [x0, #0x88]
    // 0x9b5600: stur            x2, [fp, #-0x20]
    // 0x9b5604: StoreField: r2->field_b = r0
    //     0x9b5604: stur            w0, [x2, #0xb]
    // 0x9b5608: ldur            x0, [fp, #-0x10]
    // 0x9b560c: StoreField: r2->field_13 = r0
    //     0x9b560c: stur            w0, [x2, #0x13]
    // 0x9b5610: ldur            x0, [fp, #-8]
    // 0x9b5614: LoadField: r1 = r0->field_f
    //     0x9b5614: ldur            w1, [x0, #0xf]
    // 0x9b5618: DecompressPointer r1
    //     0x9b5618: add             x1, x1, HEAP, lsl #32
    // 0x9b561c: r0 = controller()
    //     0x9b561c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b5620: LoadField: r1 = r0->field_7f
    //     0x9b5620: ldur            w1, [x0, #0x7f]
    // 0x9b5624: DecompressPointer r1
    //     0x9b5624: add             x1, x1, HEAP, lsl #32
    // 0x9b5628: r0 = value()
    //     0x9b5628: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b562c: LoadField: r3 = r0->field_f
    //     0x9b562c: ldur            w3, [x0, #0xf]
    // 0x9b5630: DecompressPointer r3
    //     0x9b5630: add             x3, x3, HEAP, lsl #32
    // 0x9b5634: stur            x3, [fp, #-0x10]
    // 0x9b5638: cmp             w3, NULL
    // 0x9b563c: b.ne            #0x9b5648
    // 0x9b5640: r0 = Null
    //     0x9b5640: mov             x0, NULL
    // 0x9b5644: b               #0x9b5674
    // 0x9b5648: r1 = Function '<anonymous closure>':.
    //     0x9b5648: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8c8] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x9b564c: ldr             x1, [x1, #0x8c8]
    // 0x9b5650: r2 = Null
    //     0x9b5650: mov             x2, NULL
    // 0x9b5654: r0 = AllocateClosure()
    //     0x9b5654: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5658: ldur            x16, [fp, #-0x10]
    // 0x9b565c: stp             x16, NULL, [SP, #8]
    // 0x9b5660: str             x0, [SP]
    // 0x9b5664: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9b5664: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9b5668: r0 = expand()
    //     0x9b5668: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x9b566c: str             x0, [SP]
    // 0x9b5670: r0 = length()
    //     0x9b5670: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x9b5674: cmp             w0, NULL
    // 0x9b5678: b.ne            #0x9b5684
    // 0x9b567c: r0 = 0
    //     0x9b567c: movz            x0, #0
    // 0x9b5680: b               #0x9b5694
    // 0x9b5684: r1 = LoadInt32Instr(r0)
    //     0x9b5684: sbfx            x1, x0, #1, #0x1f
    //     0x9b5688: tbz             w0, #0, #0x9b5690
    //     0x9b568c: ldur            x1, [x0, #7]
    // 0x9b5690: mov             x0, x1
    // 0x9b5694: cmp             x0, #5
    // 0x9b5698: b.le            #0x9b56a4
    // 0x9b569c: r4 = 5
    //     0x9b569c: movz            x4, #0x5
    // 0x9b56a0: b               #0x9b572c
    // 0x9b56a4: ldur            x2, [fp, #-8]
    // 0x9b56a8: LoadField: r1 = r2->field_f
    //     0x9b56a8: ldur            w1, [x2, #0xf]
    // 0x9b56ac: DecompressPointer r1
    //     0x9b56ac: add             x1, x1, HEAP, lsl #32
    // 0x9b56b0: r0 = controller()
    //     0x9b56b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b56b4: LoadField: r1 = r0->field_7f
    //     0x9b56b4: ldur            w1, [x0, #0x7f]
    // 0x9b56b8: DecompressPointer r1
    //     0x9b56b8: add             x1, x1, HEAP, lsl #32
    // 0x9b56bc: r0 = value()
    //     0x9b56bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b56c0: LoadField: r3 = r0->field_f
    //     0x9b56c0: ldur            w3, [x0, #0xf]
    // 0x9b56c4: DecompressPointer r3
    //     0x9b56c4: add             x3, x3, HEAP, lsl #32
    // 0x9b56c8: stur            x3, [fp, #-0x10]
    // 0x9b56cc: cmp             w3, NULL
    // 0x9b56d0: b.ne            #0x9b56dc
    // 0x9b56d4: r0 = Null
    //     0x9b56d4: mov             x0, NULL
    // 0x9b56d8: b               #0x9b5708
    // 0x9b56dc: r1 = Function '<anonymous closure>':.
    //     0x9b56dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8d0] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x9b56e0: ldr             x1, [x1, #0x8d0]
    // 0x9b56e4: r2 = Null
    //     0x9b56e4: mov             x2, NULL
    // 0x9b56e8: r0 = AllocateClosure()
    //     0x9b56e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b56ec: ldur            x16, [fp, #-0x10]
    // 0x9b56f0: stp             x16, NULL, [SP, #8]
    // 0x9b56f4: str             x0, [SP]
    // 0x9b56f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9b56f8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9b56fc: r0 = expand()
    //     0x9b56fc: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x9b5700: str             x0, [SP]
    // 0x9b5704: r0 = length()
    //     0x9b5704: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x9b5708: cmp             w0, NULL
    // 0x9b570c: b.ne            #0x9b5718
    // 0x9b5710: r0 = 0
    //     0x9b5710: movz            x0, #0
    // 0x9b5714: b               #0x9b5728
    // 0x9b5718: r1 = LoadInt32Instr(r0)
    //     0x9b5718: sbfx            x1, x0, #1, #0x1f
    //     0x9b571c: tbz             w0, #0, #0x9b5724
    //     0x9b5720: ldur            x1, [x0, #7]
    // 0x9b5724: mov             x0, x1
    // 0x9b5728: mov             x4, x0
    // 0x9b572c: ldur            x0, [fp, #-0x20]
    // 0x9b5730: ldur            x3, [fp, #-0x18]
    // 0x9b5734: ldur            x2, [fp, #-8]
    // 0x9b5738: stur            x4, [fp, #-0x30]
    // 0x9b573c: r1 = Function '<anonymous closure>':.
    //     0x9b573c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8d8] AnonymousClosure: (0x9ba774), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b5740: ldr             x1, [x1, #0x8d8]
    // 0x9b5744: r0 = AllocateClosure()
    //     0x9b5744: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5748: r1 = Function '<anonymous closure>':.
    //     0x9b5748: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8e0] AnonymousClosure: (0x9ba768), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b574c: ldr             x1, [x1, #0x8e0]
    // 0x9b5750: r2 = Null
    //     0x9b5750: mov             x2, NULL
    // 0x9b5754: stur            x0, [fp, #-0x10]
    // 0x9b5758: r0 = AllocateClosure()
    //     0x9b5758: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b575c: stur            x0, [fp, #-0x28]
    // 0x9b5760: r0 = ListView()
    //     0x9b5760: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x9b5764: stur            x0, [fp, #-0x38]
    // 0x9b5768: r16 = true
    //     0x9b5768: add             x16, NULL, #0x20  ; true
    // 0x9b576c: r30 = Instance_Axis
    //     0x9b576c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b5770: stp             lr, x16, [SP]
    // 0x9b5774: mov             x1, x0
    // 0x9b5778: ldur            x2, [fp, #-0x10]
    // 0x9b577c: ldur            x3, [fp, #-0x30]
    // 0x9b5780: ldur            x5, [fp, #-0x28]
    // 0x9b5784: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0x9b5784: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0x9b5788: ldr             x4, [x4, #0x8e8]
    // 0x9b578c: r0 = ListView.separated()
    //     0x9b578c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x9b5790: r0 = SizedBox()
    //     0x9b5790: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9b5794: mov             x3, x0
    // 0x9b5798: r0 = 60.000000
    //     0x9b5798: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x9b579c: ldr             x0, [x0, #0x110]
    // 0x9b57a0: stur            x3, [fp, #-0x10]
    // 0x9b57a4: StoreField: r3->field_13 = r0
    //     0x9b57a4: stur            w0, [x3, #0x13]
    // 0x9b57a8: ldur            x0, [fp, #-0x38]
    // 0x9b57ac: StoreField: r3->field_b = r0
    //     0x9b57ac: stur            w0, [x3, #0xb]
    // 0x9b57b0: r1 = Null
    //     0x9b57b0: mov             x1, NULL
    // 0x9b57b4: r2 = 6
    //     0x9b57b4: movz            x2, #0x6
    // 0x9b57b8: r0 = AllocateArray()
    //     0x9b57b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b57bc: mov             x2, x0
    // 0x9b57c0: ldur            x0, [fp, #-0x20]
    // 0x9b57c4: stur            x2, [fp, #-0x28]
    // 0x9b57c8: StoreField: r2->field_f = r0
    //     0x9b57c8: stur            w0, [x2, #0xf]
    // 0x9b57cc: r16 = Instance_SizedBox
    //     0x9b57cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x9b57d0: ldr             x16, [x16, #0x8f0]
    // 0x9b57d4: StoreField: r2->field_13 = r16
    //     0x9b57d4: stur            w16, [x2, #0x13]
    // 0x9b57d8: ldur            x0, [fp, #-0x10]
    // 0x9b57dc: ArrayStore: r2[0] = r0  ; List_4
    //     0x9b57dc: stur            w0, [x2, #0x17]
    // 0x9b57e0: r1 = <Widget>
    //     0x9b57e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b57e4: r0 = AllocateGrowableArray()
    //     0x9b57e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b57e8: mov             x1, x0
    // 0x9b57ec: ldur            x0, [fp, #-0x28]
    // 0x9b57f0: stur            x1, [fp, #-0x10]
    // 0x9b57f4: StoreField: r1->field_f = r0
    //     0x9b57f4: stur            w0, [x1, #0xf]
    // 0x9b57f8: r2 = 6
    //     0x9b57f8: movz            x2, #0x6
    // 0x9b57fc: StoreField: r1->field_b = r2
    //     0x9b57fc: stur            w2, [x1, #0xb]
    // 0x9b5800: r0 = Column()
    //     0x9b5800: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b5804: mov             x2, x0
    // 0x9b5808: r0 = Instance_Axis
    //     0x9b5808: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b580c: stur            x2, [fp, #-0x20]
    // 0x9b5810: StoreField: r2->field_f = r0
    //     0x9b5810: stur            w0, [x2, #0xf]
    // 0x9b5814: r3 = Instance_MainAxisAlignment
    //     0x9b5814: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b5818: ldr             x3, [x3, #0xa08]
    // 0x9b581c: StoreField: r2->field_13 = r3
    //     0x9b581c: stur            w3, [x2, #0x13]
    // 0x9b5820: r4 = Instance_MainAxisSize
    //     0x9b5820: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b5824: ldr             x4, [x4, #0xa10]
    // 0x9b5828: ArrayStore: r2[0] = r4  ; List_4
    //     0x9b5828: stur            w4, [x2, #0x17]
    // 0x9b582c: r5 = Instance_CrossAxisAlignment
    //     0x9b582c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b5830: ldr             x5, [x5, #0xa18]
    // 0x9b5834: StoreField: r2->field_1b = r5
    //     0x9b5834: stur            w5, [x2, #0x1b]
    // 0x9b5838: r6 = Instance_VerticalDirection
    //     0x9b5838: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b583c: ldr             x6, [x6, #0xa20]
    // 0x9b5840: StoreField: r2->field_23 = r6
    //     0x9b5840: stur            w6, [x2, #0x23]
    // 0x9b5844: r7 = Instance_Clip
    //     0x9b5844: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b5848: ldr             x7, [x7, #0x38]
    // 0x9b584c: StoreField: r2->field_2b = r7
    //     0x9b584c: stur            w7, [x2, #0x2b]
    // 0x9b5850: StoreField: r2->field_2f = rZR
    //     0x9b5850: stur            xzr, [x2, #0x2f]
    // 0x9b5854: ldur            x1, [fp, #-0x10]
    // 0x9b5858: StoreField: r2->field_b = r1
    //     0x9b5858: stur            w1, [x2, #0xb]
    // 0x9b585c: ldur            x8, [fp, #-0x18]
    // 0x9b5860: LoadField: r1 = r8->field_b
    //     0x9b5860: ldur            w1, [x8, #0xb]
    // 0x9b5864: LoadField: r9 = r8->field_f
    //     0x9b5864: ldur            w9, [x8, #0xf]
    // 0x9b5868: DecompressPointer r9
    //     0x9b5868: add             x9, x9, HEAP, lsl #32
    // 0x9b586c: LoadField: r10 = r9->field_b
    //     0x9b586c: ldur            w10, [x9, #0xb]
    // 0x9b5870: r9 = LoadInt32Instr(r1)
    //     0x9b5870: sbfx            x9, x1, #1, #0x1f
    // 0x9b5874: stur            x9, [fp, #-0x30]
    // 0x9b5878: r1 = LoadInt32Instr(r10)
    //     0x9b5878: sbfx            x1, x10, #1, #0x1f
    // 0x9b587c: cmp             x9, x1
    // 0x9b5880: b.ne            #0x9b588c
    // 0x9b5884: mov             x1, x8
    // 0x9b5888: r0 = _growToNextCapacity()
    //     0x9b5888: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b588c: ldur            x2, [fp, #-0x18]
    // 0x9b5890: ldur            x3, [fp, #-0x30]
    // 0x9b5894: add             x0, x3, #1
    // 0x9b5898: lsl             x1, x0, #1
    // 0x9b589c: StoreField: r2->field_b = r1
    //     0x9b589c: stur            w1, [x2, #0xb]
    // 0x9b58a0: LoadField: r1 = r2->field_f
    //     0x9b58a0: ldur            w1, [x2, #0xf]
    // 0x9b58a4: DecompressPointer r1
    //     0x9b58a4: add             x1, x1, HEAP, lsl #32
    // 0x9b58a8: ldur            x0, [fp, #-0x20]
    // 0x9b58ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b58ac: add             x25, x1, x3, lsl #2
    //     0x9b58b0: add             x25, x25, #0xf
    //     0x9b58b4: str             w0, [x25]
    //     0x9b58b8: tbz             w0, #0, #0x9b58d4
    //     0x9b58bc: ldurb           w16, [x1, #-1]
    //     0x9b58c0: ldurb           w17, [x0, #-1]
    //     0x9b58c4: and             x16, x17, x16, lsr #2
    //     0x9b58c8: tst             x16, HEAP, lsr #32
    //     0x9b58cc: b.eq            #0x9b58d4
    //     0x9b58d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b58d4: b               #0x9b58dc
    // 0x9b58d8: ldur            x2, [fp, #-0x18]
    // 0x9b58dc: ldur            x0, [fp, #-8]
    // 0x9b58e0: LoadField: r1 = r0->field_f
    //     0x9b58e0: ldur            w1, [x0, #0xf]
    // 0x9b58e4: DecompressPointer r1
    //     0x9b58e4: add             x1, x1, HEAP, lsl #32
    // 0x9b58e8: r0 = controller()
    //     0x9b58e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b58ec: LoadField: r1 = r0->field_77
    //     0x9b58ec: ldur            w1, [x0, #0x77]
    // 0x9b58f0: DecompressPointer r1
    //     0x9b58f0: add             x1, x1, HEAP, lsl #32
    // 0x9b58f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b58f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b58f8: r0 = toList()
    //     0x9b58f8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x9b58fc: LoadField: r1 = r0->field_b
    //     0x9b58fc: ldur            w1, [x0, #0xb]
    // 0x9b5900: cbz             w1, #0x9b5d40
    // 0x9b5904: ldur            x2, [fp, #-8]
    // 0x9b5908: r0 = SvgPicture()
    //     0x9b5908: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9b590c: stur            x0, [fp, #-0x10]
    // 0x9b5910: r16 = 20.000000
    //     0x9b5910: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x9b5914: ldr             x16, [x16, #0xac8]
    // 0x9b5918: r30 = 20.000000
    //     0x9b5918: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x9b591c: ldr             lr, [lr, #0xac8]
    // 0x9b5920: stp             lr, x16, [SP]
    // 0x9b5924: mov             x1, x0
    // 0x9b5928: r2 = "assets/images/bar_chart.svg"
    //     0x9b5928: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f8f8] "assets/images/bar_chart.svg"
    //     0x9b592c: ldr             x2, [x2, #0x8f8]
    // 0x9b5930: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x9b5930: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x9b5934: ldr             x4, [x4, #0x900]
    // 0x9b5938: r0 = SvgPicture.asset()
    //     0x9b5938: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9b593c: ldur            x2, [fp, #-8]
    // 0x9b5940: LoadField: r1 = r2->field_f
    //     0x9b5940: ldur            w1, [x2, #0xf]
    // 0x9b5944: DecompressPointer r1
    //     0x9b5944: add             x1, x1, HEAP, lsl #32
    // 0x9b5948: r0 = controller()
    //     0x9b5948: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b594c: mov             x1, x0
    // 0x9b5950: r0 = bumperCouponData()
    //     0x9b5950: bl              #0x8a2a70  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bumperCouponData
    // 0x9b5954: LoadField: r1 = r0->field_b
    //     0x9b5954: ldur            w1, [x0, #0xb]
    // 0x9b5958: DecompressPointer r1
    //     0x9b5958: add             x1, x1, HEAP, lsl #32
    // 0x9b595c: cmp             w1, NULL
    // 0x9b5960: b.ne            #0x9b5970
    // 0x9b5964: r5 = "Most Recent"
    //     0x9b5964: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f908] "Most Recent"
    //     0x9b5968: ldr             x5, [x5, #0x908]
    // 0x9b596c: b               #0x9b5974
    // 0x9b5970: mov             x5, x1
    // 0x9b5974: ldur            x2, [fp, #-8]
    // 0x9b5978: stur            x5, [fp, #-0x20]
    // 0x9b597c: LoadField: r1 = r2->field_f
    //     0x9b597c: ldur            w1, [x2, #0xf]
    // 0x9b5980: DecompressPointer r1
    //     0x9b5980: add             x1, x1, HEAP, lsl #32
    // 0x9b5984: r0 = controller()
    //     0x9b5984: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b5988: LoadField: r1 = r0->field_7b
    //     0x9b5988: ldur            w1, [x0, #0x7b]
    // 0x9b598c: DecompressPointer r1
    //     0x9b598c: add             x1, x1, HEAP, lsl #32
    // 0x9b5990: r0 = value()
    //     0x9b5990: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b5994: LoadField: r1 = r0->field_b
    //     0x9b5994: ldur            w1, [x0, #0xb]
    // 0x9b5998: DecompressPointer r1
    //     0x9b5998: add             x1, x1, HEAP, lsl #32
    // 0x9b599c: cmp             w1, NULL
    // 0x9b59a0: b.ne            #0x9b59ac
    // 0x9b59a4: r5 = Null
    //     0x9b59a4: mov             x5, NULL
    // 0x9b59a8: b               #0x9b59f4
    // 0x9b59ac: LoadField: r0 = r1->field_b
    //     0x9b59ac: ldur            w0, [x1, #0xb]
    // 0x9b59b0: DecompressPointer r0
    //     0x9b59b0: add             x0, x0, HEAP, lsl #32
    // 0x9b59b4: ldur            x2, [fp, #-8]
    // 0x9b59b8: stur            x0, [fp, #-0x28]
    // 0x9b59bc: r1 = Function '<anonymous closure>':.
    //     0x9b59bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f910] AnonymousClosure: (0x9ba648), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b59c0: ldr             x1, [x1, #0x910]
    // 0x9b59c4: r0 = AllocateClosure()
    //     0x9b59c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b59c8: r16 = <DropdownMenuItem<String>>
    //     0x9b59c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f918] TypeArguments: <DropdownMenuItem<String>>
    //     0x9b59cc: ldr             x16, [x16, #0x918]
    // 0x9b59d0: ldur            lr, [fp, #-0x28]
    // 0x9b59d4: stp             lr, x16, [SP, #8]
    // 0x9b59d8: str             x0, [SP]
    // 0x9b59dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9b59dc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9b59e0: r0 = map()
    //     0x9b59e0: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x9b59e4: mov             x1, x0
    // 0x9b59e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9b59e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9b59ec: r0 = toList()
    //     0x9b59ec: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x9b59f0: mov             x5, x0
    // 0x9b59f4: ldur            x0, [fp, #-8]
    // 0x9b59f8: ldur            x3, [fp, #-0x10]
    // 0x9b59fc: ldur            x4, [fp, #-0x18]
    // 0x9b5a00: mov             x2, x0
    // 0x9b5a04: stur            x5, [fp, #-0x28]
    // 0x9b5a08: r1 = Function '<anonymous closure>':.
    //     0x9b5a08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f920] AnonymousClosure: (0x9b66fc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b5a0c: ldr             x1, [x1, #0x920]
    // 0x9b5a10: r0 = AllocateClosure()
    //     0x9b5a10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5a14: ldur            x2, [fp, #-8]
    // 0x9b5a18: r1 = Function '<anonymous closure>':.
    //     0x9b5a18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f928] AnonymousClosure: (0x9b66a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b5a1c: ldr             x1, [x1, #0x928]
    // 0x9b5a20: stur            x0, [fp, #-0x38]
    // 0x9b5a24: r0 = AllocateClosure()
    //     0x9b5a24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5a28: r1 = <String>
    //     0x9b5a28: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x9b5a2c: stur            x0, [fp, #-0x50]
    // 0x9b5a30: r0 = DropdownButton()
    //     0x9b5a30: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0x9b5a34: stur            x0, [fp, #-0x58]
    // 0x9b5a38: r16 = Instance_Color
    //     0x9b5a38: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x9b5a3c: r30 = Instance_Icon
    //     0x9b5a3c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f930] Obj!Icon@d65ef1
    //     0x9b5a40: ldr             lr, [lr, #0x930]
    // 0x9b5a44: stp             lr, x16, [SP, #8]
    // 0x9b5a48: ldur            x16, [fp, #-0x50]
    // 0x9b5a4c: str             x16, [SP]
    // 0x9b5a50: mov             x1, x0
    // 0x9b5a54: ldur            x2, [fp, #-0x28]
    // 0x9b5a58: ldur            x3, [fp, #-0x38]
    // 0x9b5a5c: ldur            x5, [fp, #-0x20]
    // 0x9b5a60: r4 = const [0, 0x7, 0x3, 0x4, dropdownColor, 0x4, icon, 0x5, onTap, 0x6, null]
    //     0x9b5a60: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f938] List(11) [0, 0x7, 0x3, 0x4, "dropdownColor", 0x4, "icon", 0x5, "onTap", 0x6, Null]
    //     0x9b5a64: ldr             x4, [x4, #0x938]
    // 0x9b5a68: r0 = DropdownButton()
    //     0x9b5a68: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0x9b5a6c: r0 = DropdownButtonHideUnderline()
    //     0x9b5a6c: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0x9b5a70: mov             x3, x0
    // 0x9b5a74: ldur            x0, [fp, #-0x58]
    // 0x9b5a78: stur            x3, [fp, #-0x20]
    // 0x9b5a7c: StoreField: r3->field_b = r0
    //     0x9b5a7c: stur            w0, [x3, #0xb]
    // 0x9b5a80: r1 = Null
    //     0x9b5a80: mov             x1, NULL
    // 0x9b5a84: r2 = 6
    //     0x9b5a84: movz            x2, #0x6
    // 0x9b5a88: r0 = AllocateArray()
    //     0x9b5a88: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b5a8c: mov             x2, x0
    // 0x9b5a90: ldur            x0, [fp, #-0x10]
    // 0x9b5a94: stur            x2, [fp, #-0x28]
    // 0x9b5a98: StoreField: r2->field_f = r0
    //     0x9b5a98: stur            w0, [x2, #0xf]
    // 0x9b5a9c: r16 = Instance_SizedBox
    //     0x9b5a9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0x9b5aa0: ldr             x16, [x16, #0x940]
    // 0x9b5aa4: StoreField: r2->field_13 = r16
    //     0x9b5aa4: stur            w16, [x2, #0x13]
    // 0x9b5aa8: ldur            x0, [fp, #-0x20]
    // 0x9b5aac: ArrayStore: r2[0] = r0  ; List_4
    //     0x9b5aac: stur            w0, [x2, #0x17]
    // 0x9b5ab0: r1 = <Widget>
    //     0x9b5ab0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b5ab4: r0 = AllocateGrowableArray()
    //     0x9b5ab4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b5ab8: mov             x1, x0
    // 0x9b5abc: ldur            x0, [fp, #-0x28]
    // 0x9b5ac0: stur            x1, [fp, #-0x10]
    // 0x9b5ac4: StoreField: r1->field_f = r0
    //     0x9b5ac4: stur            w0, [x1, #0xf]
    // 0x9b5ac8: r0 = 6
    //     0x9b5ac8: movz            x0, #0x6
    // 0x9b5acc: StoreField: r1->field_b = r0
    //     0x9b5acc: stur            w0, [x1, #0xb]
    // 0x9b5ad0: r0 = Row()
    //     0x9b5ad0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9b5ad4: mov             x1, x0
    // 0x9b5ad8: r0 = Instance_Axis
    //     0x9b5ad8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9b5adc: stur            x1, [fp, #-0x20]
    // 0x9b5ae0: StoreField: r1->field_f = r0
    //     0x9b5ae0: stur            w0, [x1, #0xf]
    // 0x9b5ae4: r0 = Instance_MainAxisAlignment
    //     0x9b5ae4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b5ae8: ldr             x0, [x0, #0xa08]
    // 0x9b5aec: StoreField: r1->field_13 = r0
    //     0x9b5aec: stur            w0, [x1, #0x13]
    // 0x9b5af0: r2 = Instance_MainAxisSize
    //     0x9b5af0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b5af4: ldr             x2, [x2, #0xa10]
    // 0x9b5af8: ArrayStore: r1[0] = r2  ; List_4
    //     0x9b5af8: stur            w2, [x1, #0x17]
    // 0x9b5afc: r3 = Instance_CrossAxisAlignment
    //     0x9b5afc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b5b00: ldr             x3, [x3, #0xa18]
    // 0x9b5b04: StoreField: r1->field_1b = r3
    //     0x9b5b04: stur            w3, [x1, #0x1b]
    // 0x9b5b08: r4 = Instance_VerticalDirection
    //     0x9b5b08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b5b0c: ldr             x4, [x4, #0xa20]
    // 0x9b5b10: StoreField: r1->field_23 = r4
    //     0x9b5b10: stur            w4, [x1, #0x23]
    // 0x9b5b14: r5 = Instance_Clip
    //     0x9b5b14: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b5b18: ldr             x5, [x5, #0x38]
    // 0x9b5b1c: StoreField: r1->field_2b = r5
    //     0x9b5b1c: stur            w5, [x1, #0x2b]
    // 0x9b5b20: StoreField: r1->field_2f = rZR
    //     0x9b5b20: stur            xzr, [x1, #0x2f]
    // 0x9b5b24: ldur            x6, [fp, #-0x10]
    // 0x9b5b28: StoreField: r1->field_b = r6
    //     0x9b5b28: stur            w6, [x1, #0xb]
    // 0x9b5b2c: r0 = SizedBox()
    //     0x9b5b2c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9b5b30: mov             x1, x0
    // 0x9b5b34: r0 = 155.000000
    //     0x9b5b34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f948] 155
    //     0x9b5b38: ldr             x0, [x0, #0x948]
    // 0x9b5b3c: stur            x1, [fp, #-0x10]
    // 0x9b5b40: StoreField: r1->field_f = r0
    //     0x9b5b40: stur            w0, [x1, #0xf]
    // 0x9b5b44: ldur            x0, [fp, #-0x20]
    // 0x9b5b48: StoreField: r1->field_b = r0
    //     0x9b5b48: stur            w0, [x1, #0xb]
    // 0x9b5b4c: r0 = Align()
    //     0x9b5b4c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9b5b50: mov             x2, x0
    // 0x9b5b54: r0 = Instance_Alignment
    //     0x9b5b54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x9b5b58: ldr             x0, [x0, #0x950]
    // 0x9b5b5c: stur            x2, [fp, #-0x20]
    // 0x9b5b60: StoreField: r2->field_f = r0
    //     0x9b5b60: stur            w0, [x2, #0xf]
    // 0x9b5b64: ldur            x0, [fp, #-0x10]
    // 0x9b5b68: StoreField: r2->field_b = r0
    //     0x9b5b68: stur            w0, [x2, #0xb]
    // 0x9b5b6c: r1 = Instance_Color
    //     0x9b5b6c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b5b70: d0 = 0.100000
    //     0x9b5b70: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x9b5b74: r0 = withOpacity()
    //     0x9b5b74: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b5b78: stur            x0, [fp, #-0x10]
    // 0x9b5b7c: r0 = Divider()
    //     0x9b5b7c: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x9b5b80: mov             x2, x0
    // 0x9b5b84: ldur            x0, [fp, #-0x10]
    // 0x9b5b88: stur            x2, [fp, #-0x28]
    // 0x9b5b8c: StoreField: r2->field_1f = r0
    //     0x9b5b8c: stur            w0, [x2, #0x1f]
    // 0x9b5b90: ldur            x0, [fp, #-8]
    // 0x9b5b94: LoadField: r1 = r0->field_f
    //     0x9b5b94: ldur            w1, [x0, #0xf]
    // 0x9b5b98: DecompressPointer r1
    //     0x9b5b98: add             x1, x1, HEAP, lsl #32
    // 0x9b5b9c: r0 = controller()
    //     0x9b5b9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b5ba0: mov             x1, x0
    // 0x9b5ba4: r0 = entityList()
    //     0x9b5ba4: bl              #0x9b1494  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::entityList
    // 0x9b5ba8: LoadField: r1 = r0->field_b
    //     0x9b5ba8: ldur            w1, [x0, #0xb]
    // 0x9b5bac: r3 = LoadInt32Instr(r1)
    //     0x9b5bac: sbfx            x3, x1, #1, #0x1f
    // 0x9b5bb0: stur            x3, [fp, #-0x30]
    // 0x9b5bb4: r1 = Function '<anonymous closure>':.
    //     0x9b5bb4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f958] AnonymousClosure: (0x9b6638), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b5bb8: ldr             x1, [x1, #0x958]
    // 0x9b5bbc: r2 = Null
    //     0x9b5bbc: mov             x2, NULL
    // 0x9b5bc0: r0 = AllocateClosure()
    //     0x9b5bc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5bc4: ldur            x2, [fp, #-8]
    // 0x9b5bc8: r1 = Function '<anonymous closure>':.
    //     0x9b5bc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f960] AnonymousClosure: (0x9b17b8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b5bcc: ldr             x1, [x1, #0x960]
    // 0x9b5bd0: stur            x0, [fp, #-8]
    // 0x9b5bd4: r0 = AllocateClosure()
    //     0x9b5bd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b5bd8: stur            x0, [fp, #-0x10]
    // 0x9b5bdc: r0 = ListView()
    //     0x9b5bdc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x9b5be0: stur            x0, [fp, #-0x38]
    // 0x9b5be4: r16 = Instance_NeverScrollableScrollPhysics
    //     0x9b5be4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x9b5be8: ldr             x16, [x16, #0x1c8]
    // 0x9b5bec: r30 = true
    //     0x9b5bec: add             lr, NULL, #0x20  ; true
    // 0x9b5bf0: stp             lr, x16, [SP]
    // 0x9b5bf4: mov             x1, x0
    // 0x9b5bf8: ldur            x2, [fp, #-0x10]
    // 0x9b5bfc: ldur            x3, [fp, #-0x30]
    // 0x9b5c00: ldur            x5, [fp, #-8]
    // 0x9b5c04: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0x9b5c04: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0x9b5c08: ldr             x4, [x4, #0x968]
    // 0x9b5c0c: r0 = ListView.separated()
    //     0x9b5c0c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x9b5c10: r1 = Null
    //     0x9b5c10: mov             x1, NULL
    // 0x9b5c14: r2 = 8
    //     0x9b5c14: movz            x2, #0x8
    // 0x9b5c18: r0 = AllocateArray()
    //     0x9b5c18: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9b5c1c: mov             x2, x0
    // 0x9b5c20: ldur            x0, [fp, #-0x20]
    // 0x9b5c24: stur            x2, [fp, #-8]
    // 0x9b5c28: StoreField: r2->field_f = r0
    //     0x9b5c28: stur            w0, [x2, #0xf]
    // 0x9b5c2c: ldur            x0, [fp, #-0x28]
    // 0x9b5c30: StoreField: r2->field_13 = r0
    //     0x9b5c30: stur            w0, [x2, #0x13]
    // 0x9b5c34: r16 = Instance_SizedBox
    //     0x9b5c34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x9b5c38: ldr             x16, [x16, #0x8f0]
    // 0x9b5c3c: ArrayStore: r2[0] = r16  ; List_4
    //     0x9b5c3c: stur            w16, [x2, #0x17]
    // 0x9b5c40: ldur            x0, [fp, #-0x38]
    // 0x9b5c44: StoreField: r2->field_1b = r0
    //     0x9b5c44: stur            w0, [x2, #0x1b]
    // 0x9b5c48: r1 = <Widget>
    //     0x9b5c48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9b5c4c: r0 = AllocateGrowableArray()
    //     0x9b5c4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9b5c50: mov             x1, x0
    // 0x9b5c54: ldur            x0, [fp, #-8]
    // 0x9b5c58: stur            x1, [fp, #-0x10]
    // 0x9b5c5c: StoreField: r1->field_f = r0
    //     0x9b5c5c: stur            w0, [x1, #0xf]
    // 0x9b5c60: r0 = 8
    //     0x9b5c60: movz            x0, #0x8
    // 0x9b5c64: StoreField: r1->field_b = r0
    //     0x9b5c64: stur            w0, [x1, #0xb]
    // 0x9b5c68: r0 = Column()
    //     0x9b5c68: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b5c6c: mov             x2, x0
    // 0x9b5c70: r0 = Instance_Axis
    //     0x9b5c70: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b5c74: stur            x2, [fp, #-8]
    // 0x9b5c78: StoreField: r2->field_f = r0
    //     0x9b5c78: stur            w0, [x2, #0xf]
    // 0x9b5c7c: r3 = Instance_MainAxisAlignment
    //     0x9b5c7c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b5c80: ldr             x3, [x3, #0xa08]
    // 0x9b5c84: StoreField: r2->field_13 = r3
    //     0x9b5c84: stur            w3, [x2, #0x13]
    // 0x9b5c88: r4 = Instance_MainAxisSize
    //     0x9b5c88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b5c8c: ldr             x4, [x4, #0xa10]
    // 0x9b5c90: ArrayStore: r2[0] = r4  ; List_4
    //     0x9b5c90: stur            w4, [x2, #0x17]
    // 0x9b5c94: r5 = Instance_CrossAxisAlignment
    //     0x9b5c94: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b5c98: ldr             x5, [x5, #0xa18]
    // 0x9b5c9c: StoreField: r2->field_1b = r5
    //     0x9b5c9c: stur            w5, [x2, #0x1b]
    // 0x9b5ca0: r6 = Instance_VerticalDirection
    //     0x9b5ca0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b5ca4: ldr             x6, [x6, #0xa20]
    // 0x9b5ca8: StoreField: r2->field_23 = r6
    //     0x9b5ca8: stur            w6, [x2, #0x23]
    // 0x9b5cac: r7 = Instance_Clip
    //     0x9b5cac: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b5cb0: ldr             x7, [x7, #0x38]
    // 0x9b5cb4: StoreField: r2->field_2b = r7
    //     0x9b5cb4: stur            w7, [x2, #0x2b]
    // 0x9b5cb8: StoreField: r2->field_2f = rZR
    //     0x9b5cb8: stur            xzr, [x2, #0x2f]
    // 0x9b5cbc: ldur            x1, [fp, #-0x10]
    // 0x9b5cc0: StoreField: r2->field_b = r1
    //     0x9b5cc0: stur            w1, [x2, #0xb]
    // 0x9b5cc4: ldur            x8, [fp, #-0x18]
    // 0x9b5cc8: LoadField: r1 = r8->field_b
    //     0x9b5cc8: ldur            w1, [x8, #0xb]
    // 0x9b5ccc: LoadField: r9 = r8->field_f
    //     0x9b5ccc: ldur            w9, [x8, #0xf]
    // 0x9b5cd0: DecompressPointer r9
    //     0x9b5cd0: add             x9, x9, HEAP, lsl #32
    // 0x9b5cd4: LoadField: r10 = r9->field_b
    //     0x9b5cd4: ldur            w10, [x9, #0xb]
    // 0x9b5cd8: r9 = LoadInt32Instr(r1)
    //     0x9b5cd8: sbfx            x9, x1, #1, #0x1f
    // 0x9b5cdc: stur            x9, [fp, #-0x30]
    // 0x9b5ce0: r1 = LoadInt32Instr(r10)
    //     0x9b5ce0: sbfx            x1, x10, #1, #0x1f
    // 0x9b5ce4: cmp             x9, x1
    // 0x9b5ce8: b.ne            #0x9b5cf4
    // 0x9b5cec: mov             x1, x8
    // 0x9b5cf0: r0 = _growToNextCapacity()
    //     0x9b5cf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9b5cf4: ldur            x2, [fp, #-0x18]
    // 0x9b5cf8: ldur            x3, [fp, #-0x30]
    // 0x9b5cfc: add             x0, x3, #1
    // 0x9b5d00: lsl             x1, x0, #1
    // 0x9b5d04: StoreField: r2->field_b = r1
    //     0x9b5d04: stur            w1, [x2, #0xb]
    // 0x9b5d08: LoadField: r1 = r2->field_f
    //     0x9b5d08: ldur            w1, [x2, #0xf]
    // 0x9b5d0c: DecompressPointer r1
    //     0x9b5d0c: add             x1, x1, HEAP, lsl #32
    // 0x9b5d10: ldur            x0, [fp, #-8]
    // 0x9b5d14: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9b5d14: add             x25, x1, x3, lsl #2
    //     0x9b5d18: add             x25, x25, #0xf
    //     0x9b5d1c: str             w0, [x25]
    //     0x9b5d20: tbz             w0, #0, #0x9b5d3c
    //     0x9b5d24: ldurb           w16, [x1, #-1]
    //     0x9b5d28: ldurb           w17, [x0, #-1]
    //     0x9b5d2c: and             x16, x17, x16, lsr #2
    //     0x9b5d30: tst             x16, HEAP, lsr #32
    //     0x9b5d34: b.eq            #0x9b5d3c
    //     0x9b5d38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9b5d3c: b               #0x9b5d44
    // 0x9b5d40: ldur            x2, [fp, #-0x18]
    // 0x9b5d44: r0 = Column()
    //     0x9b5d44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9b5d48: mov             x1, x0
    // 0x9b5d4c: r0 = Instance_Axis
    //     0x9b5d4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b5d50: stur            x1, [fp, #-8]
    // 0x9b5d54: StoreField: r1->field_f = r0
    //     0x9b5d54: stur            w0, [x1, #0xf]
    // 0x9b5d58: r2 = Instance_MainAxisAlignment
    //     0x9b5d58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9b5d5c: ldr             x2, [x2, #0xa08]
    // 0x9b5d60: StoreField: r1->field_13 = r2
    //     0x9b5d60: stur            w2, [x1, #0x13]
    // 0x9b5d64: r2 = Instance_MainAxisSize
    //     0x9b5d64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9b5d68: ldr             x2, [x2, #0xa10]
    // 0x9b5d6c: ArrayStore: r1[0] = r2  ; List_4
    //     0x9b5d6c: stur            w2, [x1, #0x17]
    // 0x9b5d70: r2 = Instance_CrossAxisAlignment
    //     0x9b5d70: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9b5d74: ldr             x2, [x2, #0xa18]
    // 0x9b5d78: StoreField: r1->field_1b = r2
    //     0x9b5d78: stur            w2, [x1, #0x1b]
    // 0x9b5d7c: r2 = Instance_VerticalDirection
    //     0x9b5d7c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9b5d80: ldr             x2, [x2, #0xa20]
    // 0x9b5d84: StoreField: r1->field_23 = r2
    //     0x9b5d84: stur            w2, [x1, #0x23]
    // 0x9b5d88: r2 = Instance_Clip
    //     0x9b5d88: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9b5d8c: ldr             x2, [x2, #0x38]
    // 0x9b5d90: StoreField: r1->field_2b = r2
    //     0x9b5d90: stur            w2, [x1, #0x2b]
    // 0x9b5d94: StoreField: r1->field_2f = rZR
    //     0x9b5d94: stur            xzr, [x1, #0x2f]
    // 0x9b5d98: ldur            x2, [fp, #-0x18]
    // 0x9b5d9c: StoreField: r1->field_b = r2
    //     0x9b5d9c: stur            w2, [x1, #0xb]
    // 0x9b5da0: r0 = Padding()
    //     0x9b5da0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9b5da4: mov             x1, x0
    // 0x9b5da8: r0 = Instance_EdgeInsets
    //     0x9b5da8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9b5dac: ldr             x0, [x0, #0x1f0]
    // 0x9b5db0: stur            x1, [fp, #-0x10]
    // 0x9b5db4: StoreField: r1->field_f = r0
    //     0x9b5db4: stur            w0, [x1, #0xf]
    // 0x9b5db8: ldur            x0, [fp, #-8]
    // 0x9b5dbc: StoreField: r1->field_b = r0
    //     0x9b5dbc: stur            w0, [x1, #0xb]
    // 0x9b5dc0: r0 = SingleChildScrollView()
    //     0x9b5dc0: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x9b5dc4: r1 = Instance_Axis
    //     0x9b5dc4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9b5dc8: StoreField: r0->field_b = r1
    //     0x9b5dc8: stur            w1, [x0, #0xb]
    // 0x9b5dcc: r1 = false
    //     0x9b5dcc: add             x1, NULL, #0x30  ; false
    // 0x9b5dd0: StoreField: r0->field_f = r1
    //     0x9b5dd0: stur            w1, [x0, #0xf]
    // 0x9b5dd4: ldur            x1, [fp, #-0x10]
    // 0x9b5dd8: StoreField: r0->field_23 = r1
    //     0x9b5dd8: stur            w1, [x0, #0x23]
    // 0x9b5ddc: r1 = Instance_DragStartBehavior
    //     0x9b5ddc: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x9b5de0: StoreField: r0->field_27 = r1
    //     0x9b5de0: stur            w1, [x0, #0x27]
    // 0x9b5de4: r1 = Instance_Clip
    //     0x9b5de4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x9b5de8: ldr             x1, [x1, #0x7e0]
    // 0x9b5dec: StoreField: r0->field_2b = r1
    //     0x9b5dec: stur            w1, [x0, #0x2b]
    // 0x9b5df0: r1 = Instance_HitTestBehavior
    //     0x9b5df0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x9b5df4: ldr             x1, [x1, #0x288]
    // 0x9b5df8: StoreField: r0->field_2f = r1
    //     0x9b5df8: stur            w1, [x0, #0x2f]
    // 0x9b5dfc: LeaveFrame
    //     0x9b5dfc: mov             SP, fp
    //     0x9b5e00: ldp             fp, lr, [SP], #0x10
    // 0x9b5e04: ret
    //     0x9b5e04: ret             
    // 0x9b5e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b5e08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b5e0c: b               #0x9b34b4
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9b6638, size: 0x68
    // 0x9b6638: EnterFrame
    //     0x9b6638: stp             fp, lr, [SP, #-0x10]!
    //     0x9b663c: mov             fp, SP
    // 0x9b6640: AllocStack(0x10)
    //     0x9b6640: sub             SP, SP, #0x10
    // 0x9b6644: CheckStackOverflow
    //     0x9b6644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b6648: cmp             SP, x16
    //     0x9b664c: b.ls            #0x9b6698
    // 0x9b6650: r1 = Instance_Color
    //     0x9b6650: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b6654: d0 = 0.100000
    //     0x9b6654: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x9b6658: r0 = withOpacity()
    //     0x9b6658: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9b665c: stur            x0, [fp, #-8]
    // 0x9b6660: r0 = Divider()
    //     0x9b6660: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x9b6664: mov             x1, x0
    // 0x9b6668: ldur            x0, [fp, #-8]
    // 0x9b666c: stur            x1, [fp, #-0x10]
    // 0x9b6670: StoreField: r1->field_1f = r0
    //     0x9b6670: stur            w0, [x1, #0x1f]
    // 0x9b6674: r0 = Padding()
    //     0x9b6674: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9b6678: r1 = Instance_EdgeInsets
    //     0x9b6678: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!EdgeInsets@d577d1
    //     0x9b667c: ldr             x1, [x1, #0xb28]
    // 0x9b6680: StoreField: r0->field_f = r1
    //     0x9b6680: stur            w1, [x0, #0xf]
    // 0x9b6684: ldur            x1, [fp, #-0x10]
    // 0x9b6688: StoreField: r0->field_b = r1
    //     0x9b6688: stur            w1, [x0, #0xb]
    // 0x9b668c: LeaveFrame
    //     0x9b668c: mov             SP, fp
    //     0x9b6690: ldp             fp, lr, [SP], #0x10
    // 0x9b6694: ret
    //     0x9b6694: ret             
    // 0x9b6698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b6698: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b669c: b               #0x9b6650
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9b66a0, size: 0x5c
    // 0x9b66a0: EnterFrame
    //     0x9b66a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9b66a4: mov             fp, SP
    // 0x9b66a8: ldr             x0, [fp, #0x10]
    // 0x9b66ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9b66ac: ldur            w1, [x0, #0x17]
    // 0x9b66b0: DecompressPointer r1
    //     0x9b66b0: add             x1, x1, HEAP, lsl #32
    // 0x9b66b4: CheckStackOverflow
    //     0x9b66b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b66b8: cmp             SP, x16
    //     0x9b66bc: b.ls            #0x9b66f4
    // 0x9b66c0: LoadField: r0 = r1->field_f
    //     0x9b66c0: ldur            w0, [x1, #0xf]
    // 0x9b66c4: DecompressPointer r0
    //     0x9b66c4: add             x0, x0, HEAP, lsl #32
    // 0x9b66c8: mov             x1, x0
    // 0x9b66cc: r0 = controller()
    //     0x9b66cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b66d0: mov             x1, x0
    // 0x9b66d4: r2 = "sort"
    //     0x9b66d4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fb30] "sort"
    //     0x9b66d8: ldr             x2, [x2, #0xb30]
    // 0x9b66dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9b66dc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9b66e0: r0 = ratingReviewClickedEvent()
    //     0x9b66e0: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9b66e4: r0 = Null
    //     0x9b66e4: mov             x0, NULL
    // 0x9b66e8: LeaveFrame
    //     0x9b66e8: mov             SP, fp
    //     0x9b66ec: ldp             fp, lr, [SP], #0x10
    // 0x9b66f0: ret
    //     0x9b66f0: ret             
    // 0x9b66f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b66f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b66f8: b               #0x9b66c0
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x9b66fc, size: 0x230
    // 0x9b66fc: EnterFrame
    //     0x9b66fc: stp             fp, lr, [SP, #-0x10]!
    //     0x9b6700: mov             fp, SP
    // 0x9b6704: AllocStack(0x20)
    //     0x9b6704: sub             SP, SP, #0x20
    // 0x9b6708: SetupParameters()
    //     0x9b6708: ldr             x0, [fp, #0x18]
    //     0x9b670c: ldur            w1, [x0, #0x17]
    //     0x9b6710: add             x1, x1, HEAP, lsl #32
    //     0x9b6714: stur            x1, [fp, #-8]
    // 0x9b6718: CheckStackOverflow
    //     0x9b6718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b671c: cmp             SP, x16
    //     0x9b6720: b.ls            #0x9b6924
    // 0x9b6724: r1 = 1
    //     0x9b6724: movz            x1, #0x1
    // 0x9b6728: r0 = AllocateContext()
    //     0x9b6728: bl              #0x16f6108  ; AllocateContextStub
    // 0x9b672c: mov             x2, x0
    // 0x9b6730: ldur            x0, [fp, #-8]
    // 0x9b6734: stur            x2, [fp, #-0x10]
    // 0x9b6738: StoreField: r2->field_b = r0
    //     0x9b6738: stur            w0, [x2, #0xb]
    // 0x9b673c: ldr             x1, [fp, #0x10]
    // 0x9b6740: StoreField: r2->field_f = r1
    //     0x9b6740: stur            w1, [x2, #0xf]
    // 0x9b6744: LoadField: r1 = r0->field_f
    //     0x9b6744: ldur            w1, [x0, #0xf]
    // 0x9b6748: DecompressPointer r1
    //     0x9b6748: add             x1, x1, HEAP, lsl #32
    // 0x9b674c: r0 = controller()
    //     0x9b674c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b6750: LoadField: r1 = r0->field_7b
    //     0x9b6750: ldur            w1, [x0, #0x7b]
    // 0x9b6754: DecompressPointer r1
    //     0x9b6754: add             x1, x1, HEAP, lsl #32
    // 0x9b6758: r0 = value()
    //     0x9b6758: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b675c: LoadField: r1 = r0->field_b
    //     0x9b675c: ldur            w1, [x0, #0xb]
    // 0x9b6760: DecompressPointer r1
    //     0x9b6760: add             x1, x1, HEAP, lsl #32
    // 0x9b6764: cmp             w1, NULL
    // 0x9b6768: b.ne            #0x9b6774
    // 0x9b676c: r2 = Null
    //     0x9b676c: mov             x2, NULL
    // 0x9b6770: b               #0x9b67c0
    // 0x9b6774: LoadField: r0 = r1->field_b
    //     0x9b6774: ldur            w0, [x1, #0xb]
    // 0x9b6778: DecompressPointer r0
    //     0x9b6778: add             x0, x0, HEAP, lsl #32
    // 0x9b677c: ldur            x2, [fp, #-0x10]
    // 0x9b6780: stur            x0, [fp, #-0x18]
    // 0x9b6784: r1 = Function '<anonymous closure>':.
    //     0x9b6784: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb38] AnonymousClosure: (0x9ba5e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b6788: ldr             x1, [x1, #0xb38]
    // 0x9b678c: r0 = AllocateClosure()
    //     0x9b678c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b6790: r1 = Function '<anonymous closure>':.
    //     0x9b6790: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb40] AnonymousClosure: (0x9ba5c8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9b6794: ldr             x1, [x1, #0xb40]
    // 0x9b6798: r2 = Null
    //     0x9b6798: mov             x2, NULL
    // 0x9b679c: stur            x0, [fp, #-0x10]
    // 0x9b67a0: r0 = AllocateClosure()
    //     0x9b67a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b67a4: str             x0, [SP]
    // 0x9b67a8: ldur            x1, [fp, #-0x18]
    // 0x9b67ac: ldur            x2, [fp, #-0x10]
    // 0x9b67b0: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x9b67b0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x9b67b4: ldr             x4, [x4, #0xb48]
    // 0x9b67b8: r0 = firstWhere()
    //     0x9b67b8: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x9b67bc: mov             x2, x0
    // 0x9b67c0: ldur            x0, [fp, #-8]
    // 0x9b67c4: stur            x2, [fp, #-0x10]
    // 0x9b67c8: LoadField: r1 = r0->field_f
    //     0x9b67c8: ldur            w1, [x0, #0xf]
    // 0x9b67cc: DecompressPointer r1
    //     0x9b67cc: add             x1, x1, HEAP, lsl #32
    // 0x9b67d0: r0 = controller()
    //     0x9b67d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b67d4: mov             x1, x0
    // 0x9b67d8: ldur            x2, [fp, #-0x10]
    // 0x9b67dc: r0 = activeViewFilter=()
    //     0x9b67dc: bl              #0x9ba544  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::activeViewFilter=
    // 0x9b67e0: ldur            x0, [fp, #-8]
    // 0x9b67e4: LoadField: r1 = r0->field_f
    //     0x9b67e4: ldur            w1, [x0, #0xf]
    // 0x9b67e8: DecompressPointer r1
    //     0x9b67e8: add             x1, x1, HEAP, lsl #32
    // 0x9b67ec: r0 = controller()
    //     0x9b67ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b67f0: LoadField: r1 = r0->field_6b
    //     0x9b67f0: ldur            w1, [x0, #0x6b]
    // 0x9b67f4: DecompressPointer r1
    //     0x9b67f4: add             x1, x1, HEAP, lsl #32
    // 0x9b67f8: r0 = initRefresh()
    //     0x9b67f8: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x9b67fc: ldur            x0, [fp, #-8]
    // 0x9b6800: LoadField: r1 = r0->field_f
    //     0x9b6800: ldur            w1, [x0, #0xf]
    // 0x9b6804: DecompressPointer r1
    //     0x9b6804: add             x1, x1, HEAP, lsl #32
    // 0x9b6808: r0 = controller()
    //     0x9b6808: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b680c: LoadField: r1 = r0->field_67
    //     0x9b680c: ldur            w1, [x0, #0x67]
    // 0x9b6810: DecompressPointer r1
    //     0x9b6810: add             x1, x1, HEAP, lsl #32
    // 0x9b6814: ldur            x0, [fp, #-0x10]
    // 0x9b6818: cmp             w0, NULL
    // 0x9b681c: b.ne            #0x9b6828
    // 0x9b6820: r2 = Null
    //     0x9b6820: mov             x2, NULL
    // 0x9b6824: b               #0x9b6830
    // 0x9b6828: LoadField: r2 = r0->field_7
    //     0x9b6828: ldur            w2, [x0, #7]
    // 0x9b682c: DecompressPointer r2
    //     0x9b682c: add             x2, x2, HEAP, lsl #32
    // 0x9b6830: cmp             w2, NULL
    // 0x9b6834: b.ne            #0x9b683c
    // 0x9b6838: r2 = ""
    //     0x9b6838: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b683c: ldur            x3, [fp, #-8]
    // 0x9b6840: r0 = value=()
    //     0x9b6840: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x9b6844: ldur            x0, [fp, #-8]
    // 0x9b6848: LoadField: r1 = r0->field_f
    //     0x9b6848: ldur            w1, [x0, #0xf]
    // 0x9b684c: DecompressPointer r1
    //     0x9b684c: add             x1, x1, HEAP, lsl #32
    // 0x9b6850: r0 = controller()
    //     0x9b6850: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b6854: mov             x2, x0
    // 0x9b6858: ldur            x0, [fp, #-0x10]
    // 0x9b685c: stur            x2, [fp, #-0x18]
    // 0x9b6860: cmp             w0, NULL
    // 0x9b6864: b.ne            #0x9b6870
    // 0x9b6868: r0 = Null
    //     0x9b6868: mov             x0, NULL
    // 0x9b686c: b               #0x9b687c
    // 0x9b6870: LoadField: r1 = r0->field_7
    //     0x9b6870: ldur            w1, [x0, #7]
    // 0x9b6874: DecompressPointer r1
    //     0x9b6874: add             x1, x1, HEAP, lsl #32
    // 0x9b6878: mov             x0, x1
    // 0x9b687c: cmp             w0, NULL
    // 0x9b6880: b.ne            #0x9b688c
    // 0x9b6884: r3 = ""
    //     0x9b6884: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9b6888: b               #0x9b6890
    // 0x9b688c: mov             x3, x0
    // 0x9b6890: ldur            x0, [fp, #-8]
    // 0x9b6894: stur            x3, [fp, #-0x10]
    // 0x9b6898: LoadField: r1 = r0->field_f
    //     0x9b6898: ldur            w1, [x0, #0xf]
    // 0x9b689c: DecompressPointer r1
    //     0x9b689c: add             x1, x1, HEAP, lsl #32
    // 0x9b68a0: r0 = controller()
    //     0x9b68a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b68a4: LoadField: r2 = r0->field_53
    //     0x9b68a4: ldur            w2, [x0, #0x53]
    // 0x9b68a8: DecompressPointer r2
    //     0x9b68a8: add             x2, x2, HEAP, lsl #32
    // 0x9b68ac: ldur            x16, [fp, #-0x10]
    // 0x9b68b0: str             x16, [SP]
    // 0x9b68b4: ldur            x1, [fp, #-0x18]
    // 0x9b68b8: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0x9b68b8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0x9b68bc: ldr             x4, [x4, #0x220]
    // 0x9b68c0: r0 = getViewAllReviews()
    //     0x9b68c0: bl              #0x9b692c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::getViewAllReviews
    // 0x9b68c4: ldur            x0, [fp, #-8]
    // 0x9b68c8: LoadField: r1 = r0->field_f
    //     0x9b68c8: ldur            w1, [x0, #0xf]
    // 0x9b68cc: DecompressPointer r1
    //     0x9b68cc: add             x1, x1, HEAP, lsl #32
    // 0x9b68d0: r0 = controller()
    //     0x9b68d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b68d4: mov             x2, x0
    // 0x9b68d8: ldur            x0, [fp, #-8]
    // 0x9b68dc: stur            x2, [fp, #-0x10]
    // 0x9b68e0: LoadField: r1 = r0->field_f
    //     0x9b68e0: ldur            w1, [x0, #0xf]
    // 0x9b68e4: DecompressPointer r1
    //     0x9b68e4: add             x1, x1, HEAP, lsl #32
    // 0x9b68e8: r0 = controller()
    //     0x9b68e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9b68ec: LoadField: r1 = r0->field_67
    //     0x9b68ec: ldur            w1, [x0, #0x67]
    // 0x9b68f0: DecompressPointer r1
    //     0x9b68f0: add             x1, x1, HEAP, lsl #32
    // 0x9b68f4: r0 = value()
    //     0x9b68f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9b68f8: str             x0, [SP]
    // 0x9b68fc: ldur            x1, [fp, #-0x10]
    // 0x9b6900: r2 = "sort_selected"
    //     0x9b6900: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fb50] "sort_selected"
    //     0x9b6904: ldr             x2, [x2, #0xb50]
    // 0x9b6908: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0x9b6908: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0x9b690c: ldr             x4, [x4, #0xaf8]
    // 0x9b6910: r0 = ratingReviewClickedEvent()
    //     0x9b6910: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9b6914: r0 = Null
    //     0x9b6914: mov             x0, NULL
    // 0x9b6918: LeaveFrame
    //     0x9b6918: mov             SP, fp
    //     0x9b691c: ldp             fp, lr, [SP], #0x10
    // 0x9b6920: ret
    //     0x9b6920: ret             
    // 0x9b6924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b6924: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b6928: b               #0x9b6724
  }
  [closure] Filter <anonymous closure>(dynamic) {
    // ** addr: 0x9ba5c8, size: 0x18
    // 0x9ba5c8: EnterFrame
    //     0x9ba5c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9ba5cc: mov             fp, SP
    // 0x9ba5d0: r0 = Filter()
    //     0x9ba5d0: bl              #0x9b95cc  ; AllocateFilterStub -> Filter (size=0x10)
    // 0x9ba5d4: LeaveFrame
    //     0x9ba5d4: mov             SP, fp
    //     0x9ba5d8: ldp             fp, lr, [SP], #0x10
    // 0x9ba5dc: ret
    //     0x9ba5dc: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Filter) {
    // ** addr: 0x9ba5e0, size: 0x68
    // 0x9ba5e0: EnterFrame
    //     0x9ba5e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ba5e4: mov             fp, SP
    // 0x9ba5e8: AllocStack(0x10)
    //     0x9ba5e8: sub             SP, SP, #0x10
    // 0x9ba5ec: SetupParameters()
    //     0x9ba5ec: ldr             x0, [fp, #0x18]
    //     0x9ba5f0: ldur            w1, [x0, #0x17]
    //     0x9ba5f4: add             x1, x1, HEAP, lsl #32
    // 0x9ba5f8: CheckStackOverflow
    //     0x9ba5f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ba5fc: cmp             SP, x16
    //     0x9ba600: b.ls            #0x9ba640
    // 0x9ba604: ldr             x0, [fp, #0x10]
    // 0x9ba608: LoadField: r2 = r0->field_b
    //     0x9ba608: ldur            w2, [x0, #0xb]
    // 0x9ba60c: DecompressPointer r2
    //     0x9ba60c: add             x2, x2, HEAP, lsl #32
    // 0x9ba610: LoadField: r0 = r1->field_f
    //     0x9ba610: ldur            w0, [x1, #0xf]
    // 0x9ba614: DecompressPointer r0
    //     0x9ba614: add             x0, x0, HEAP, lsl #32
    // 0x9ba618: r1 = LoadClassIdInstr(r2)
    //     0x9ba618: ldur            x1, [x2, #-1]
    //     0x9ba61c: ubfx            x1, x1, #0xc, #0x14
    // 0x9ba620: stp             x0, x2, [SP]
    // 0x9ba624: mov             x0, x1
    // 0x9ba628: mov             lr, x0
    // 0x9ba62c: ldr             lr, [x21, lr, lsl #3]
    // 0x9ba630: blr             lr
    // 0x9ba634: LeaveFrame
    //     0x9ba634: mov             SP, fp
    //     0x9ba638: ldp             fp, lr, [SP], #0x10
    // 0x9ba63c: ret
    //     0x9ba63c: ret             
    // 0x9ba640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ba640: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ba644: b               #0x9ba604
  }
  [closure] DropdownMenuItem<String> <anonymous closure>(dynamic, Filter) {
    // ** addr: 0x9ba648, size: 0x114
    // 0x9ba648: EnterFrame
    //     0x9ba648: stp             fp, lr, [SP, #-0x10]!
    //     0x9ba64c: mov             fp, SP
    // 0x9ba650: AllocStack(0x30)
    //     0x9ba650: sub             SP, SP, #0x30
    // 0x9ba654: SetupParameters()
    //     0x9ba654: ldr             x0, [fp, #0x18]
    //     0x9ba658: ldur            w1, [x0, #0x17]
    //     0x9ba65c: add             x1, x1, HEAP, lsl #32
    // 0x9ba660: CheckStackOverflow
    //     0x9ba660: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ba664: cmp             SP, x16
    //     0x9ba668: b.ls            #0x9ba754
    // 0x9ba66c: ldr             x0, [fp, #0x10]
    // 0x9ba670: LoadField: r2 = r0->field_b
    //     0x9ba670: ldur            w2, [x0, #0xb]
    // 0x9ba674: DecompressPointer r2
    //     0x9ba674: add             x2, x2, HEAP, lsl #32
    // 0x9ba678: cmp             w2, NULL
    // 0x9ba67c: b.ne            #0x9ba688
    // 0x9ba680: r0 = ""
    //     0x9ba680: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ba684: b               #0x9ba68c
    // 0x9ba688: mov             x0, x2
    // 0x9ba68c: stur            x0, [fp, #-0x10]
    // 0x9ba690: cmp             w2, NULL
    // 0x9ba694: b.ne            #0x9ba69c
    // 0x9ba698: r2 = ""
    //     0x9ba698: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ba69c: stur            x2, [fp, #-8]
    // 0x9ba6a0: LoadField: r3 = r1->field_13
    //     0x9ba6a0: ldur            w3, [x1, #0x13]
    // 0x9ba6a4: DecompressPointer r3
    //     0x9ba6a4: add             x3, x3, HEAP, lsl #32
    // 0x9ba6a8: mov             x1, x3
    // 0x9ba6ac: r0 = of()
    //     0x9ba6ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ba6b0: LoadField: r1 = r0->field_87
    //     0x9ba6b0: ldur            w1, [x0, #0x87]
    // 0x9ba6b4: DecompressPointer r1
    //     0x9ba6b4: add             x1, x1, HEAP, lsl #32
    // 0x9ba6b8: LoadField: r0 = r1->field_f
    //     0x9ba6b8: ldur            w0, [x1, #0xf]
    // 0x9ba6bc: DecompressPointer r0
    //     0x9ba6bc: add             x0, x0, HEAP, lsl #32
    // 0x9ba6c0: cmp             w0, NULL
    // 0x9ba6c4: b.ne            #0x9ba6d0
    // 0x9ba6c8: r2 = Null
    //     0x9ba6c8: mov             x2, NULL
    // 0x9ba6cc: b               #0x9ba6f4
    // 0x9ba6d0: r16 = Instance_Color
    //     0x9ba6d0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9ba6d4: r30 = 12.000000
    //     0x9ba6d4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9ba6d8: ldr             lr, [lr, #0x9e8]
    // 0x9ba6dc: stp             lr, x16, [SP]
    // 0x9ba6e0: mov             x1, x0
    // 0x9ba6e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9ba6e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9ba6e8: ldr             x4, [x4, #0x9b8]
    // 0x9ba6ec: r0 = copyWith()
    //     0x9ba6ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ba6f0: mov             x2, x0
    // 0x9ba6f4: ldur            x0, [fp, #-0x10]
    // 0x9ba6f8: ldur            x1, [fp, #-8]
    // 0x9ba6fc: stur            x2, [fp, #-0x18]
    // 0x9ba700: r0 = Text()
    //     0x9ba700: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ba704: mov             x2, x0
    // 0x9ba708: ldur            x0, [fp, #-8]
    // 0x9ba70c: stur            x2, [fp, #-0x20]
    // 0x9ba710: StoreField: r2->field_b = r0
    //     0x9ba710: stur            w0, [x2, #0xb]
    // 0x9ba714: ldur            x0, [fp, #-0x18]
    // 0x9ba718: StoreField: r2->field_13 = r0
    //     0x9ba718: stur            w0, [x2, #0x13]
    // 0x9ba71c: r1 = <String>
    //     0x9ba71c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x9ba720: r0 = DropdownMenuItem()
    //     0x9ba720: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0x9ba724: ldur            x1, [fp, #-0x10]
    // 0x9ba728: StoreField: r0->field_1b = r1
    //     0x9ba728: stur            w1, [x0, #0x1b]
    // 0x9ba72c: r1 = true
    //     0x9ba72c: add             x1, NULL, #0x20  ; true
    // 0x9ba730: StoreField: r0->field_1f = r1
    //     0x9ba730: stur            w1, [x0, #0x1f]
    // 0x9ba734: r1 = Instance_AlignmentDirectional
    //     0x9ba734: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0x9ba738: ldr             x1, [x1, #0xb70]
    // 0x9ba73c: StoreField: r0->field_f = r1
    //     0x9ba73c: stur            w1, [x0, #0xf]
    // 0x9ba740: ldur            x1, [fp, #-0x20]
    // 0x9ba744: StoreField: r0->field_b = r1
    //     0x9ba744: stur            w1, [x0, #0xb]
    // 0x9ba748: LeaveFrame
    //     0x9ba748: mov             SP, fp
    //     0x9ba74c: ldp             fp, lr, [SP], #0x10
    // 0x9ba750: ret
    //     0x9ba750: ret             
    // 0x9ba754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ba754: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ba758: b               #0x9ba66c
  }
  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9ba768, size: 0xc
    // 0x9ba768: r0 = Instance_SizedBox
    //     0x9ba768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x9ba76c: ldr             x0, [x0, #0xa50]
    // 0x9ba770: ret
    //     0x9ba770: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9ba774, size: 0x674
    // 0x9ba774: EnterFrame
    //     0x9ba774: stp             fp, lr, [SP, #-0x10]!
    //     0x9ba778: mov             fp, SP
    // 0x9ba77c: AllocStack(0x70)
    //     0x9ba77c: sub             SP, SP, #0x70
    // 0x9ba780: SetupParameters()
    //     0x9ba780: ldr             x0, [fp, #0x20]
    //     0x9ba784: ldur            w1, [x0, #0x17]
    //     0x9ba788: add             x1, x1, HEAP, lsl #32
    //     0x9ba78c: stur            x1, [fp, #-8]
    // 0x9ba790: CheckStackOverflow
    //     0x9ba790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ba794: cmp             SP, x16
    //     0x9ba798: b.ls            #0x9baddc
    // 0x9ba79c: r1 = 3
    //     0x9ba79c: movz            x1, #0x3
    // 0x9ba7a0: r0 = AllocateContext()
    //     0x9ba7a0: bl              #0x16f6108  ; AllocateContextStub
    // 0x9ba7a4: mov             x2, x0
    // 0x9ba7a8: ldur            x0, [fp, #-8]
    // 0x9ba7ac: stur            x2, [fp, #-0x10]
    // 0x9ba7b0: StoreField: r2->field_b = r0
    //     0x9ba7b0: stur            w0, [x2, #0xb]
    // 0x9ba7b4: ldr             x3, [fp, #0x18]
    // 0x9ba7b8: StoreField: r2->field_f = r3
    //     0x9ba7b8: stur            w3, [x2, #0xf]
    // 0x9ba7bc: ldr             x4, [fp, #0x10]
    // 0x9ba7c0: StoreField: r2->field_13 = r4
    //     0x9ba7c0: stur            w4, [x2, #0x13]
    // 0x9ba7c4: LoadField: r1 = r0->field_f
    //     0x9ba7c4: ldur            w1, [x0, #0xf]
    // 0x9ba7c8: DecompressPointer r1
    //     0x9ba7c8: add             x1, x1, HEAP, lsl #32
    // 0x9ba7cc: r0 = controller()
    //     0x9ba7cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9ba7d0: LoadField: r1 = r0->field_7f
    //     0x9ba7d0: ldur            w1, [x0, #0x7f]
    // 0x9ba7d4: DecompressPointer r1
    //     0x9ba7d4: add             x1, x1, HEAP, lsl #32
    // 0x9ba7d8: r0 = value()
    //     0x9ba7d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9ba7dc: LoadField: r3 = r0->field_f
    //     0x9ba7dc: ldur            w3, [x0, #0xf]
    // 0x9ba7e0: DecompressPointer r3
    //     0x9ba7e0: add             x3, x3, HEAP, lsl #32
    // 0x9ba7e4: stur            x3, [fp, #-0x18]
    // 0x9ba7e8: cmp             w3, NULL
    // 0x9ba7ec: b.ne            #0x9ba7f8
    // 0x9ba7f0: r2 = Null
    //     0x9ba7f0: mov             x2, NULL
    // 0x9ba7f4: b               #0x9ba82c
    // 0x9ba7f8: r1 = Function '<anonymous closure>':.
    //     0x9ba7f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb78] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x9ba7fc: ldr             x1, [x1, #0xb78]
    // 0x9ba800: r2 = Null
    //     0x9ba800: mov             x2, NULL
    // 0x9ba804: r0 = AllocateClosure()
    //     0x9ba804: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ba808: ldur            x16, [fp, #-0x18]
    // 0x9ba80c: stp             x16, NULL, [SP, #8]
    // 0x9ba810: str             x0, [SP]
    // 0x9ba814: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9ba814: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9ba818: r0 = expand()
    //     0x9ba818: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x9ba81c: mov             x1, x0
    // 0x9ba820: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9ba820: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9ba824: r0 = toList()
    //     0x9ba824: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x9ba828: mov             x2, x0
    // 0x9ba82c: cmp             w2, NULL
    // 0x9ba830: b.ne            #0x9ba840
    // 0x9ba834: ldr             x3, [fp, #0x10]
    // 0x9ba838: r1 = Null
    //     0x9ba838: mov             x1, NULL
    // 0x9ba83c: b               #0x9ba87c
    // 0x9ba840: ldr             x3, [fp, #0x10]
    // 0x9ba844: LoadField: r0 = r2->field_b
    //     0x9ba844: ldur            w0, [x2, #0xb]
    // 0x9ba848: r4 = LoadInt32Instr(r3)
    //     0x9ba848: sbfx            x4, x3, #1, #0x1f
    //     0x9ba84c: tbz             w3, #0, #0x9ba854
    //     0x9ba850: ldur            x4, [x3, #7]
    // 0x9ba854: r1 = LoadInt32Instr(r0)
    //     0x9ba854: sbfx            x1, x0, #1, #0x1f
    // 0x9ba858: mov             x0, x1
    // 0x9ba85c: mov             x1, x4
    // 0x9ba860: cmp             x1, x0
    // 0x9ba864: b.hs            #0x9bade4
    // 0x9ba868: LoadField: r0 = r2->field_f
    //     0x9ba868: ldur            w0, [x2, #0xf]
    // 0x9ba86c: DecompressPointer r0
    //     0x9ba86c: add             x0, x0, HEAP, lsl #32
    // 0x9ba870: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9ba870: add             x16, x0, x4, lsl #2
    //     0x9ba874: ldur            w1, [x16, #0xf]
    // 0x9ba878: DecompressPointer r1
    //     0x9ba878: add             x1, x1, HEAP, lsl #32
    // 0x9ba87c: ldur            x2, [fp, #-0x10]
    // 0x9ba880: mov             x0, x1
    // 0x9ba884: stur            x1, [fp, #-0x18]
    // 0x9ba888: ArrayStore: r2[0] = r0  ; List_4
    //     0x9ba888: stur            w0, [x2, #0x17]
    //     0x9ba88c: tbz             w0, #0, #0x9ba8a8
    //     0x9ba890: ldurb           w16, [x2, #-1]
    //     0x9ba894: ldurb           w17, [x0, #-1]
    //     0x9ba898: and             x16, x17, x16, lsr #2
    //     0x9ba89c: tst             x16, HEAP, lsr #32
    //     0x9ba8a0: b.eq            #0x9ba8a8
    //     0x9ba8a4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9ba8a8: r0 = LoadInt32Instr(r3)
    //     0x9ba8a8: sbfx            x0, x3, #1, #0x1f
    //     0x9ba8ac: tbz             w3, #0, #0x9ba8b4
    //     0x9ba8b0: ldur            x0, [x3, #7]
    // 0x9ba8b4: cmp             x0, #4
    // 0x9ba8b8: b.ge            #0x9baabc
    // 0x9ba8bc: str             x1, [SP]
    // 0x9ba8c0: r4 = 0
    //     0x9ba8c0: movz            x4, #0
    // 0x9ba8c4: ldr             x0, [SP]
    // 0x9ba8c8: r16 = UnlinkedCall_0x613b5c
    //     0x9ba8c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9ba8cc: add             x16, x16, #0xb80
    // 0x9ba8d0: ldp             x5, lr, [x16]
    // 0x9ba8d4: blr             lr
    // 0x9ba8d8: r1 = 60
    //     0x9ba8d8: movz            x1, #0x3c
    // 0x9ba8dc: branchIfSmi(r0, 0x9ba8e8)
    //     0x9ba8dc: tbz             w0, #0, #0x9ba8e8
    // 0x9ba8e0: r1 = LoadClassIdInstr(r0)
    //     0x9ba8e0: ldur            x1, [x0, #-1]
    //     0x9ba8e4: ubfx            x1, x1, #0xc, #0x14
    // 0x9ba8e8: r16 = "image"
    //     0x9ba8e8: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x9ba8ec: stp             x16, x0, [SP]
    // 0x9ba8f0: mov             x0, x1
    // 0x9ba8f4: mov             lr, x0
    // 0x9ba8f8: ldr             lr, [x21, lr, lsl #3]
    // 0x9ba8fc: blr             lr
    // 0x9ba900: tbnz            w0, #4, #0x9ba9e4
    // 0x9ba904: r0 = ImageHeaders.forImages()
    //     0x9ba904: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x9ba908: stur            x0, [fp, #-0x20]
    // 0x9ba90c: ldur            x16, [fp, #-0x18]
    // 0x9ba910: str             x16, [SP]
    // 0x9ba914: r4 = 0
    //     0x9ba914: movz            x4, #0
    // 0x9ba918: ldr             x0, [SP]
    // 0x9ba91c: r16 = UnlinkedCall_0x613b5c
    //     0x9ba91c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9ba920: add             x16, x16, #0xb90
    // 0x9ba924: ldp             x5, lr, [x16]
    // 0x9ba928: blr             lr
    // 0x9ba92c: mov             x3, x0
    // 0x9ba930: r2 = Null
    //     0x9ba930: mov             x2, NULL
    // 0x9ba934: r1 = Null
    //     0x9ba934: mov             x1, NULL
    // 0x9ba938: stur            x3, [fp, #-0x28]
    // 0x9ba93c: r4 = 60
    //     0x9ba93c: movz            x4, #0x3c
    // 0x9ba940: branchIfSmi(r0, 0x9ba94c)
    //     0x9ba940: tbz             w0, #0, #0x9ba94c
    // 0x9ba944: r4 = LoadClassIdInstr(r0)
    //     0x9ba944: ldur            x4, [x0, #-1]
    //     0x9ba948: ubfx            x4, x4, #0xc, #0x14
    // 0x9ba94c: sub             x4, x4, #0x5e
    // 0x9ba950: cmp             x4, #1
    // 0x9ba954: b.ls            #0x9ba968
    // 0x9ba958: r8 = String
    //     0x9ba958: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x9ba95c: r3 = Null
    //     0x9ba95c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fba0] Null
    //     0x9ba960: ldr             x3, [x3, #0xba0]
    // 0x9ba964: r0 = String()
    //     0x9ba964: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x9ba968: r1 = Function '<anonymous closure>':.
    //     0x9ba968: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbb0] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9ba96c: ldr             x1, [x1, #0xbb0]
    // 0x9ba970: r2 = Null
    //     0x9ba970: mov             x2, NULL
    // 0x9ba974: r0 = AllocateClosure()
    //     0x9ba974: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ba978: r1 = Function '<anonymous closure>':.
    //     0x9ba978: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbb8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x9ba97c: ldr             x1, [x1, #0xbb8]
    // 0x9ba980: r2 = Null
    //     0x9ba980: mov             x2, NULL
    // 0x9ba984: stur            x0, [fp, #-0x30]
    // 0x9ba988: r0 = AllocateClosure()
    //     0x9ba988: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9ba98c: stur            x0, [fp, #-0x38]
    // 0x9ba990: r0 = CachedNetworkImage()
    //     0x9ba990: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x9ba994: stur            x0, [fp, #-0x40]
    // 0x9ba998: r16 = 60.000000
    //     0x9ba998: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x9ba99c: ldr             x16, [x16, #0x110]
    // 0x9ba9a0: r30 = 62.000000
    //     0x9ba9a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0x9ba9a4: ldr             lr, [lr, #0xbc0]
    // 0x9ba9a8: stp             lr, x16, [SP, #0x20]
    // 0x9ba9ac: ldur            x16, [fp, #-0x20]
    // 0x9ba9b0: r30 = Instance_BoxFit
    //     0x9ba9b0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x9ba9b4: ldr             lr, [lr, #0x118]
    // 0x9ba9b8: stp             lr, x16, [SP, #0x10]
    // 0x9ba9bc: ldur            x16, [fp, #-0x30]
    // 0x9ba9c0: ldur            lr, [fp, #-0x38]
    // 0x9ba9c4: stp             lr, x16, [SP]
    // 0x9ba9c8: mov             x1, x0
    // 0x9ba9cc: ldur            x2, [fp, #-0x28]
    // 0x9ba9d0: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0x9ba9d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fbc8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0x9ba9d4: ldr             x4, [x4, #0xbc8]
    // 0x9ba9d8: r0 = CachedNetworkImage()
    //     0x9ba9d8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x9ba9dc: ldur            x0, [fp, #-0x40]
    // 0x9ba9e0: b               #0x9baa54
    // 0x9ba9e4: ldur            x16, [fp, #-0x18]
    // 0x9ba9e8: str             x16, [SP]
    // 0x9ba9ec: r4 = 0
    //     0x9ba9ec: movz            x4, #0
    // 0x9ba9f0: ldr             x0, [SP]
    // 0x9ba9f4: r16 = UnlinkedCall_0x613b5c
    //     0x9ba9f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fbd0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9ba9f8: add             x16, x16, #0xbd0
    // 0x9ba9fc: ldp             x5, lr, [x16]
    // 0x9baa00: blr             lr
    // 0x9baa04: mov             x3, x0
    // 0x9baa08: r2 = Null
    //     0x9baa08: mov             x2, NULL
    // 0x9baa0c: r1 = Null
    //     0x9baa0c: mov             x1, NULL
    // 0x9baa10: stur            x3, [fp, #-0x20]
    // 0x9baa14: r4 = 60
    //     0x9baa14: movz            x4, #0x3c
    // 0x9baa18: branchIfSmi(r0, 0x9baa24)
    //     0x9baa18: tbz             w0, #0, #0x9baa24
    // 0x9baa1c: r4 = LoadClassIdInstr(r0)
    //     0x9baa1c: ldur            x4, [x0, #-1]
    //     0x9baa20: ubfx            x4, x4, #0xc, #0x14
    // 0x9baa24: sub             x4, x4, #0x5e
    // 0x9baa28: cmp             x4, #1
    // 0x9baa2c: b.ls            #0x9baa40
    // 0x9baa30: r8 = String
    //     0x9baa30: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x9baa34: r3 = Null
    //     0x9baa34: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fbe0] Null
    //     0x9baa38: ldr             x3, [x3, #0xbe0]
    // 0x9baa3c: r0 = String()
    //     0x9baa3c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x9baa40: r0 = VideoPlayerWidget()
    //     0x9baa40: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x9baa44: mov             x1, x0
    // 0x9baa48: ldur            x0, [fp, #-0x20]
    // 0x9baa4c: StoreField: r1->field_b = r0
    //     0x9baa4c: stur            w0, [x1, #0xb]
    // 0x9baa50: mov             x0, x1
    // 0x9baa54: stur            x0, [fp, #-0x20]
    // 0x9baa58: r0 = InkWell()
    //     0x9baa58: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9baa5c: mov             x3, x0
    // 0x9baa60: ldur            x0, [fp, #-0x20]
    // 0x9baa64: stur            x3, [fp, #-0x28]
    // 0x9baa68: StoreField: r3->field_b = r0
    //     0x9baa68: stur            w0, [x3, #0xb]
    // 0x9baa6c: ldur            x2, [fp, #-0x10]
    // 0x9baa70: r1 = Function '<anonymous closure>':.
    //     0x9baa70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbf0] AnonymousClosure: (0x9bb088), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9baa74: ldr             x1, [x1, #0xbf0]
    // 0x9baa78: r0 = AllocateClosure()
    //     0x9baa78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9baa7c: mov             x1, x0
    // 0x9baa80: ldur            x0, [fp, #-0x28]
    // 0x9baa84: StoreField: r0->field_f = r1
    //     0x9baa84: stur            w1, [x0, #0xf]
    // 0x9baa88: r1 = true
    //     0x9baa88: add             x1, NULL, #0x20  ; true
    // 0x9baa8c: StoreField: r0->field_43 = r1
    //     0x9baa8c: stur            w1, [x0, #0x43]
    // 0x9baa90: r2 = Instance_BoxShape
    //     0x9baa90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9baa94: ldr             x2, [x2, #0x80]
    // 0x9baa98: StoreField: r0->field_47 = r2
    //     0x9baa98: stur            w2, [x0, #0x47]
    // 0x9baa9c: StoreField: r0->field_6f = r1
    //     0x9baa9c: stur            w1, [x0, #0x6f]
    // 0x9baaa0: r3 = false
    //     0x9baaa0: add             x3, NULL, #0x30  ; false
    // 0x9baaa4: StoreField: r0->field_73 = r3
    //     0x9baaa4: stur            w3, [x0, #0x73]
    // 0x9baaa8: StoreField: r0->field_83 = r1
    //     0x9baaa8: stur            w1, [x0, #0x83]
    // 0x9baaac: StoreField: r0->field_7b = r3
    //     0x9baaac: stur            w3, [x0, #0x7b]
    // 0x9baab0: LeaveFrame
    //     0x9baab0: mov             SP, fp
    //     0x9baab4: ldp             fp, lr, [SP], #0x10
    // 0x9baab8: ret
    //     0x9baab8: ret             
    // 0x9baabc: ldur            x0, [fp, #-8]
    // 0x9baac0: r1 = true
    //     0x9baac0: add             x1, NULL, #0x20  ; true
    // 0x9baac4: r3 = false
    //     0x9baac4: add             x3, NULL, #0x30  ; false
    // 0x9baac8: r2 = Instance_BoxShape
    //     0x9baac8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9baacc: ldr             x2, [x2, #0x80]
    // 0x9baad0: ldur            x16, [fp, #-0x18]
    // 0x9baad4: str             x16, [SP]
    // 0x9baad8: r4 = 0
    //     0x9baad8: movz            x4, #0
    // 0x9baadc: ldr             x0, [SP]
    // 0x9baae0: r16 = UnlinkedCall_0x613b5c
    //     0x9baae0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fbf8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9baae4: add             x16, x16, #0xbf8
    // 0x9baae8: ldp             x5, lr, [x16]
    // 0x9baaec: blr             lr
    // 0x9baaf0: mov             x3, x0
    // 0x9baaf4: r2 = Null
    //     0x9baaf4: mov             x2, NULL
    // 0x9baaf8: r1 = Null
    //     0x9baaf8: mov             x1, NULL
    // 0x9baafc: stur            x3, [fp, #-0x18]
    // 0x9bab00: r4 = 60
    //     0x9bab00: movz            x4, #0x3c
    // 0x9bab04: branchIfSmi(r0, 0x9bab10)
    //     0x9bab04: tbz             w0, #0, #0x9bab10
    // 0x9bab08: r4 = LoadClassIdInstr(r0)
    //     0x9bab08: ldur            x4, [x0, #-1]
    //     0x9bab0c: ubfx            x4, x4, #0xc, #0x14
    // 0x9bab10: sub             x4, x4, #0x5e
    // 0x9bab14: cmp             x4, #1
    // 0x9bab18: b.ls            #0x9bab2c
    // 0x9bab1c: r8 = String
    //     0x9bab1c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x9bab20: r3 = Null
    //     0x9bab20: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fc08] Null
    //     0x9bab24: ldr             x3, [x3, #0xc08]
    // 0x9bab28: r0 = String()
    //     0x9bab28: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x9bab2c: r1 = Function '<anonymous closure>':.
    //     0x9bab2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc18] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9bab30: ldr             x1, [x1, #0xc18]
    // 0x9bab34: r2 = Null
    //     0x9bab34: mov             x2, NULL
    // 0x9bab38: r0 = AllocateClosure()
    //     0x9bab38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9bab3c: r1 = Function '<anonymous closure>':.
    //     0x9bab3c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc20] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x9bab40: ldr             x1, [x1, #0xc20]
    // 0x9bab44: r2 = Null
    //     0x9bab44: mov             x2, NULL
    // 0x9bab48: stur            x0, [fp, #-0x20]
    // 0x9bab4c: r0 = AllocateClosure()
    //     0x9bab4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9bab50: stur            x0, [fp, #-0x28]
    // 0x9bab54: r0 = CachedNetworkImage()
    //     0x9bab54: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x9bab58: stur            x0, [fp, #-0x30]
    // 0x9bab5c: r16 = 60.000000
    //     0x9bab5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x9bab60: ldr             x16, [x16, #0x110]
    // 0x9bab64: r30 = 62.000000
    //     0x9bab64: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0x9bab68: ldr             lr, [lr, #0xbc0]
    // 0x9bab6c: stp             lr, x16, [SP, #0x18]
    // 0x9bab70: r16 = Instance_BoxFit
    //     0x9bab70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x9bab74: ldr             x16, [x16, #0x118]
    // 0x9bab78: ldur            lr, [fp, #-0x20]
    // 0x9bab7c: stp             lr, x16, [SP, #8]
    // 0x9bab80: ldur            x16, [fp, #-0x28]
    // 0x9bab84: str             x16, [SP]
    // 0x9bab88: mov             x1, x0
    // 0x9bab8c: ldur            x2, [fp, #-0x18]
    // 0x9bab90: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x9bab90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x9bab94: ldr             x4, [x4, #0xc28]
    // 0x9bab98: r0 = CachedNetworkImage()
    //     0x9bab98: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x9bab9c: r1 = Instance_Color
    //     0x9bab9c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9baba0: d0 = 0.600000
    //     0x9baba0: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0x9baba4: r0 = withOpacity()
    //     0x9baba4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9baba8: r1 = Null
    //     0x9baba8: mov             x1, NULL
    // 0x9babac: r2 = 4
    //     0x9babac: movz            x2, #0x4
    // 0x9babb0: stur            x0, [fp, #-0x18]
    // 0x9babb4: r0 = AllocateArray()
    //     0x9babb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9babb8: stur            x0, [fp, #-0x20]
    // 0x9babbc: r16 = "+ "
    //     0x9babbc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0x9babc0: ldr             x16, [x16, #0xc30]
    // 0x9babc4: StoreField: r0->field_f = r16
    //     0x9babc4: stur            w16, [x0, #0xf]
    // 0x9babc8: ldur            x1, [fp, #-8]
    // 0x9babcc: LoadField: r2 = r1->field_f
    //     0x9babcc: ldur            w2, [x1, #0xf]
    // 0x9babd0: DecompressPointer r2
    //     0x9babd0: add             x2, x2, HEAP, lsl #32
    // 0x9babd4: mov             x1, x2
    // 0x9babd8: r0 = controller()
    //     0x9babd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9babdc: LoadField: r1 = r0->field_7f
    //     0x9babdc: ldur            w1, [x0, #0x7f]
    // 0x9babe0: DecompressPointer r1
    //     0x9babe0: add             x1, x1, HEAP, lsl #32
    // 0x9babe4: r0 = value()
    //     0x9babe4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9babe8: LoadField: r1 = r0->field_b
    //     0x9babe8: ldur            w1, [x0, #0xb]
    // 0x9babec: DecompressPointer r1
    //     0x9babec: add             x1, x1, HEAP, lsl #32
    // 0x9babf0: cmp             w1, NULL
    // 0x9babf4: b.ne            #0x9bac00
    // 0x9babf8: r0 = 0
    //     0x9babf8: movz            x0, #0
    // 0x9babfc: b               #0x9bac0c
    // 0x9bac00: r0 = LoadInt32Instr(r1)
    //     0x9bac00: sbfx            x0, x1, #1, #0x1f
    //     0x9bac04: tbz             w1, #0, #0x9bac0c
    //     0x9bac08: ldur            x0, [x1, #7]
    // 0x9bac0c: ldur            x2, [fp, #-0x30]
    // 0x9bac10: sub             x3, x0, #4
    // 0x9bac14: r0 = BoxInt64Instr(r3)
    //     0x9bac14: sbfiz           x0, x3, #1, #0x1f
    //     0x9bac18: cmp             x3, x0, asr #1
    //     0x9bac1c: b.eq            #0x9bac28
    //     0x9bac20: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9bac24: stur            x3, [x0, #7]
    // 0x9bac28: ldur            x1, [fp, #-0x20]
    // 0x9bac2c: ArrayStore: r1[1] = r0  ; List_4
    //     0x9bac2c: add             x25, x1, #0x13
    //     0x9bac30: str             w0, [x25]
    //     0x9bac34: tbz             w0, #0, #0x9bac50
    //     0x9bac38: ldurb           w16, [x1, #-1]
    //     0x9bac3c: ldurb           w17, [x0, #-1]
    //     0x9bac40: and             x16, x17, x16, lsr #2
    //     0x9bac44: tst             x16, HEAP, lsr #32
    //     0x9bac48: b.eq            #0x9bac50
    //     0x9bac4c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9bac50: ldur            x16, [fp, #-0x20]
    // 0x9bac54: str             x16, [SP]
    // 0x9bac58: r0 = _interpolate()
    //     0x9bac58: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9bac5c: ldr             x1, [fp, #0x18]
    // 0x9bac60: stur            x0, [fp, #-8]
    // 0x9bac64: r0 = of()
    //     0x9bac64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9bac68: LoadField: r1 = r0->field_87
    //     0x9bac68: ldur            w1, [x0, #0x87]
    // 0x9bac6c: DecompressPointer r1
    //     0x9bac6c: add             x1, x1, HEAP, lsl #32
    // 0x9bac70: LoadField: r0 = r1->field_7
    //     0x9bac70: ldur            w0, [x1, #7]
    // 0x9bac74: DecompressPointer r0
    //     0x9bac74: add             x0, x0, HEAP, lsl #32
    // 0x9bac78: r16 = 12.000000
    //     0x9bac78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9bac7c: ldr             x16, [x16, #0x9e8]
    // 0x9bac80: r30 = Instance_Color
    //     0x9bac80: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x9bac84: stp             lr, x16, [SP]
    // 0x9bac88: mov             x1, x0
    // 0x9bac8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9bac8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9bac90: ldr             x4, [x4, #0xaa0]
    // 0x9bac94: r0 = copyWith()
    //     0x9bac94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9bac98: stur            x0, [fp, #-0x20]
    // 0x9bac9c: r0 = Text()
    //     0x9bac9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9baca0: mov             x1, x0
    // 0x9baca4: ldur            x0, [fp, #-8]
    // 0x9baca8: stur            x1, [fp, #-0x28]
    // 0x9bacac: StoreField: r1->field_b = r0
    //     0x9bacac: stur            w0, [x1, #0xb]
    // 0x9bacb0: ldur            x0, [fp, #-0x20]
    // 0x9bacb4: StoreField: r1->field_13 = r0
    //     0x9bacb4: stur            w0, [x1, #0x13]
    // 0x9bacb8: r0 = Container()
    //     0x9bacb8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9bacbc: stur            x0, [fp, #-8]
    // 0x9bacc0: r16 = 60.000000
    //     0x9bacc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x9bacc4: ldr             x16, [x16, #0x110]
    // 0x9bacc8: r30 = 62.000000
    //     0x9bacc8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] 62
    //     0x9baccc: ldr             lr, [lr, #0xbc0]
    // 0x9bacd0: stp             lr, x16, [SP, #0x18]
    // 0x9bacd4: ldur            x16, [fp, #-0x18]
    // 0x9bacd8: r30 = Instance_Alignment
    //     0x9bacd8: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9bacdc: ldr             lr, [lr, #0xb10]
    // 0x9bace0: stp             lr, x16, [SP, #8]
    // 0x9bace4: ldur            x16, [fp, #-0x28]
    // 0x9bace8: str             x16, [SP]
    // 0x9bacec: mov             x1, x0
    // 0x9bacf0: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, color, 0x3, height, 0x1, width, 0x2, null]
    //     0x9bacf0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc38] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x9bacf4: ldr             x4, [x4, #0xc38]
    // 0x9bacf8: r0 = Container()
    //     0x9bacf8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9bacfc: r1 = Null
    //     0x9bacfc: mov             x1, NULL
    // 0x9bad00: r2 = 4
    //     0x9bad00: movz            x2, #0x4
    // 0x9bad04: r0 = AllocateArray()
    //     0x9bad04: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9bad08: mov             x2, x0
    // 0x9bad0c: ldur            x0, [fp, #-0x30]
    // 0x9bad10: stur            x2, [fp, #-0x18]
    // 0x9bad14: StoreField: r2->field_f = r0
    //     0x9bad14: stur            w0, [x2, #0xf]
    // 0x9bad18: ldur            x0, [fp, #-8]
    // 0x9bad1c: StoreField: r2->field_13 = r0
    //     0x9bad1c: stur            w0, [x2, #0x13]
    // 0x9bad20: r1 = <Widget>
    //     0x9bad20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9bad24: r0 = AllocateGrowableArray()
    //     0x9bad24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9bad28: mov             x1, x0
    // 0x9bad2c: ldur            x0, [fp, #-0x18]
    // 0x9bad30: stur            x1, [fp, #-8]
    // 0x9bad34: StoreField: r1->field_f = r0
    //     0x9bad34: stur            w0, [x1, #0xf]
    // 0x9bad38: r0 = 4
    //     0x9bad38: movz            x0, #0x4
    // 0x9bad3c: StoreField: r1->field_b = r0
    //     0x9bad3c: stur            w0, [x1, #0xb]
    // 0x9bad40: r0 = Stack()
    //     0x9bad40: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x9bad44: mov             x1, x0
    // 0x9bad48: r0 = Instance_Alignment
    //     0x9bad48: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9bad4c: ldr             x0, [x0, #0xb10]
    // 0x9bad50: stur            x1, [fp, #-0x18]
    // 0x9bad54: StoreField: r1->field_f = r0
    //     0x9bad54: stur            w0, [x1, #0xf]
    // 0x9bad58: r0 = Instance_StackFit
    //     0x9bad58: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x9bad5c: ldr             x0, [x0, #0xfa8]
    // 0x9bad60: ArrayStore: r1[0] = r0  ; List_4
    //     0x9bad60: stur            w0, [x1, #0x17]
    // 0x9bad64: r0 = Instance_Clip
    //     0x9bad64: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x9bad68: ldr             x0, [x0, #0x7e0]
    // 0x9bad6c: StoreField: r1->field_1b = r0
    //     0x9bad6c: stur            w0, [x1, #0x1b]
    // 0x9bad70: ldur            x0, [fp, #-8]
    // 0x9bad74: StoreField: r1->field_b = r0
    //     0x9bad74: stur            w0, [x1, #0xb]
    // 0x9bad78: r0 = InkWell()
    //     0x9bad78: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9bad7c: mov             x3, x0
    // 0x9bad80: ldur            x0, [fp, #-0x18]
    // 0x9bad84: stur            x3, [fp, #-8]
    // 0x9bad88: StoreField: r3->field_b = r0
    //     0x9bad88: stur            w0, [x3, #0xb]
    // 0x9bad8c: ldur            x2, [fp, #-0x10]
    // 0x9bad90: r1 = Function '<anonymous closure>':.
    //     0x9bad90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc40] AnonymousClosure: (0x9bade8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9bad94: ldr             x1, [x1, #0xc40]
    // 0x9bad98: r0 = AllocateClosure()
    //     0x9bad98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9bad9c: mov             x1, x0
    // 0x9bada0: ldur            x0, [fp, #-8]
    // 0x9bada4: StoreField: r0->field_f = r1
    //     0x9bada4: stur            w1, [x0, #0xf]
    // 0x9bada8: r1 = true
    //     0x9bada8: add             x1, NULL, #0x20  ; true
    // 0x9badac: StoreField: r0->field_43 = r1
    //     0x9badac: stur            w1, [x0, #0x43]
    // 0x9badb0: r2 = Instance_BoxShape
    //     0x9badb0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9badb4: ldr             x2, [x2, #0x80]
    // 0x9badb8: StoreField: r0->field_47 = r2
    //     0x9badb8: stur            w2, [x0, #0x47]
    // 0x9badbc: StoreField: r0->field_6f = r1
    //     0x9badbc: stur            w1, [x0, #0x6f]
    // 0x9badc0: r2 = false
    //     0x9badc0: add             x2, NULL, #0x30  ; false
    // 0x9badc4: StoreField: r0->field_73 = r2
    //     0x9badc4: stur            w2, [x0, #0x73]
    // 0x9badc8: StoreField: r0->field_83 = r1
    //     0x9badc8: stur            w1, [x0, #0x83]
    // 0x9badcc: StoreField: r0->field_7b = r2
    //     0x9badcc: stur            w2, [x0, #0x7b]
    // 0x9badd0: LeaveFrame
    //     0x9badd0: mov             SP, fp
    //     0x9badd4: ldp             fp, lr, [SP], #0x10
    // 0x9badd8: ret
    //     0x9badd8: ret             
    // 0x9baddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9baddc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9bade0: b               #0x9ba79c
    // 0x9bade4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9bade4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9bade8, size: 0x180
    // 0x9bade8: EnterFrame
    //     0x9bade8: stp             fp, lr, [SP, #-0x10]!
    //     0x9badec: mov             fp, SP
    // 0x9badf0: AllocStack(0x28)
    //     0x9badf0: sub             SP, SP, #0x28
    // 0x9badf4: SetupParameters()
    //     0x9badf4: ldr             x0, [fp, #0x10]
    //     0x9badf8: ldur            w1, [x0, #0x17]
    //     0x9badfc: add             x1, x1, HEAP, lsl #32
    // 0x9bae00: CheckStackOverflow
    //     0x9bae00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9bae04: cmp             SP, x16
    //     0x9bae08: b.ls            #0x9baf60
    // 0x9bae0c: LoadField: r0 = r1->field_b
    //     0x9bae0c: ldur            w0, [x1, #0xb]
    // 0x9bae10: DecompressPointer r0
    //     0x9bae10: add             x0, x0, HEAP, lsl #32
    // 0x9bae14: stur            x0, [fp, #-8]
    // 0x9bae18: LoadField: r1 = r0->field_f
    //     0x9bae18: ldur            w1, [x0, #0xf]
    // 0x9bae1c: DecompressPointer r1
    //     0x9bae1c: add             x1, x1, HEAP, lsl #32
    // 0x9bae20: r0 = controller()
    //     0x9bae20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9bae24: mov             x1, x0
    // 0x9bae28: r2 = "more_media"
    //     0x9bae28: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc48] "more_media"
    //     0x9bae2c: ldr             x2, [x2, #0xc48]
    // 0x9bae30: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9bae30: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9bae34: r0 = ratingReviewClickedEvent()
    //     0x9bae34: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9bae38: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x9bae38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9bae3c: ldr             x0, [x0, #0x1c80]
    //     0x9bae40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9bae44: cmp             w0, w16
    //     0x9bae48: b.ne            #0x9bae54
    //     0x9bae4c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x9bae50: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9bae54: r1 = Null
    //     0x9bae54: mov             x1, NULL
    // 0x9bae58: r2 = 12
    //     0x9bae58: movz            x2, #0xc
    // 0x9bae5c: r0 = AllocateArray()
    //     0x9bae5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9bae60: stur            x0, [fp, #-0x10]
    // 0x9bae64: r16 = "product_short_id"
    //     0x9bae64: add             x16, PP, #0xb, lsl #12  ; [pp+0xb490] "product_short_id"
    //     0x9bae68: ldr             x16, [x16, #0x490]
    // 0x9bae6c: StoreField: r0->field_f = r16
    //     0x9bae6c: stur            w16, [x0, #0xf]
    // 0x9bae70: ldur            x2, [fp, #-8]
    // 0x9bae74: LoadField: r1 = r2->field_f
    //     0x9bae74: ldur            w1, [x2, #0xf]
    // 0x9bae78: DecompressPointer r1
    //     0x9bae78: add             x1, x1, HEAP, lsl #32
    // 0x9bae7c: r0 = controller()
    //     0x9bae7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9bae80: LoadField: r1 = r0->field_53
    //     0x9bae80: ldur            w1, [x0, #0x53]
    // 0x9bae84: DecompressPointer r1
    //     0x9bae84: add             x1, x1, HEAP, lsl #32
    // 0x9bae88: mov             x0, x1
    // 0x9bae8c: ldur            x1, [fp, #-0x10]
    // 0x9bae90: ArrayStore: r1[1] = r0  ; List_4
    //     0x9bae90: add             x25, x1, #0x13
    //     0x9bae94: str             w0, [x25]
    //     0x9bae98: tbz             w0, #0, #0x9baeb4
    //     0x9bae9c: ldurb           w16, [x1, #-1]
    //     0x9baea0: ldurb           w17, [x0, #-1]
    //     0x9baea4: and             x16, x17, x16, lsr #2
    //     0x9baea8: tst             x16, HEAP, lsr #32
    //     0x9baeac: b.eq            #0x9baeb4
    //     0x9baeb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9baeb4: ldur            x0, [fp, #-0x10]
    // 0x9baeb8: r16 = "sort_by"
    //     0x9baeb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f378] "sort_by"
    //     0x9baebc: ldr             x16, [x16, #0x378]
    // 0x9baec0: ArrayStore: r0[0] = r16  ; List_4
    //     0x9baec0: stur            w16, [x0, #0x17]
    // 0x9baec4: ldur            x1, [fp, #-8]
    // 0x9baec8: LoadField: r2 = r1->field_f
    //     0x9baec8: ldur            w2, [x1, #0xf]
    // 0x9baecc: DecompressPointer r2
    //     0x9baecc: add             x2, x2, HEAP, lsl #32
    // 0x9baed0: mov             x1, x2
    // 0x9baed4: r0 = controller()
    //     0x9baed4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9baed8: LoadField: r1 = r0->field_63
    //     0x9baed8: ldur            w1, [x0, #0x63]
    // 0x9baedc: DecompressPointer r1
    //     0x9baedc: add             x1, x1, HEAP, lsl #32
    // 0x9baee0: mov             x0, x1
    // 0x9baee4: ldur            x1, [fp, #-0x10]
    // 0x9baee8: ArrayStore: r1[3] = r0  ; List_4
    //     0x9baee8: add             x25, x1, #0x1b
    //     0x9baeec: str             w0, [x25]
    //     0x9baef0: tbz             w0, #0, #0x9baf0c
    //     0x9baef4: ldurb           w16, [x1, #-1]
    //     0x9baef8: ldurb           w17, [x0, #-1]
    //     0x9baefc: and             x16, x17, x16, lsr #2
    //     0x9baf00: tst             x16, HEAP, lsr #32
    //     0x9baf04: b.eq            #0x9baf0c
    //     0x9baf08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9baf0c: ldur            x0, [fp, #-0x10]
    // 0x9baf10: r16 = "is_product_page"
    //     0x9baf10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f368] "is_product_page"
    //     0x9baf14: ldr             x16, [x16, #0x368]
    // 0x9baf18: StoreField: r0->field_1f = r16
    //     0x9baf18: stur            w16, [x0, #0x1f]
    // 0x9baf1c: r16 = false
    //     0x9baf1c: add             x16, NULL, #0x30  ; false
    // 0x9baf20: StoreField: r0->field_23 = r16
    //     0x9baf20: stur            w16, [x0, #0x23]
    // 0x9baf24: r16 = <String, Object?>
    //     0x9baf24: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x9baf28: ldr             x16, [x16, #0xc28]
    // 0x9baf2c: stp             x0, x16, [SP]
    // 0x9baf30: r0 = Map._fromLiteral()
    //     0x9baf30: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x9baf34: r16 = "/rating_review_media_screen"
    //     0x9baf34: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9c8] "/rating_review_media_screen"
    //     0x9baf38: ldr             x16, [x16, #0x9c8]
    // 0x9baf3c: stp             x16, NULL, [SP, #8]
    // 0x9baf40: str             x0, [SP]
    // 0x9baf44: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x9baf44: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x9baf48: ldr             x4, [x4, #0x438]
    // 0x9baf4c: r0 = GetNavigation.toNamed()
    //     0x9baf4c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x9baf50: r0 = Null
    //     0x9baf50: mov             x0, NULL
    // 0x9baf54: LeaveFrame
    //     0x9baf54: mov             SP, fp
    //     0x9baf58: ldp             fp, lr, [SP], #0x10
    // 0x9baf5c: ret
    //     0x9baf5c: ret             
    // 0x9baf60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9baf60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9baf64: b               #0x9bae0c
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0x9baf68, size: 0x120
    // 0x9baf68: EnterFrame
    //     0x9baf68: stp             fp, lr, [SP, #-0x10]!
    //     0x9baf6c: mov             fp, SP
    // 0x9baf70: AllocStack(0x18)
    //     0x9baf70: sub             SP, SP, #0x18
    // 0x9baf74: CheckStackOverflow
    //     0x9baf74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9baf78: cmp             SP, x16
    //     0x9baf7c: b.ls            #0x9bb070
    // 0x9baf80: ldr             x0, [fp, #0x10]
    // 0x9baf84: LoadField: r1 = r0->field_b
    //     0x9baf84: ldur            w1, [x0, #0xb]
    // 0x9baf88: DecompressPointer r1
    //     0x9baf88: add             x1, x1, HEAP, lsl #32
    // 0x9baf8c: cmp             w1, NULL
    // 0x9baf90: b.eq            #0x9bafac
    // 0x9baf94: LoadField: r2 = r0->field_f
    //     0x9baf94: ldur            x2, [x0, #0xf]
    // 0x9baf98: r0 = LoadInt32Instr(r1)
    //     0x9baf98: sbfx            x0, x1, #1, #0x1f
    //     0x9baf9c: tbz             w1, #0, #0x9bafa4
    //     0x9bafa0: ldur            x0, [x1, #7]
    // 0x9bafa4: cmp             x2, x0
    // 0x9bafa8: b.le            #0x9bafb4
    // 0x9bafac: r0 = Null
    //     0x9bafac: mov             x0, NULL
    // 0x9bafb0: b               #0x9bafe8
    // 0x9bafb4: scvtf           d0, x2
    // 0x9bafb8: scvtf           d1, x0
    // 0x9bafbc: fdiv            d2, d0, d1
    // 0x9bafc0: r0 = inline_Allocate_Double()
    //     0x9bafc0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9bafc4: add             x0, x0, #0x10
    //     0x9bafc8: cmp             x1, x0
    //     0x9bafcc: b.ls            #0x9bb078
    //     0x9bafd0: str             x0, [THR, #0x50]  ; THR::top
    //     0x9bafd4: sub             x0, x0, #0xf
    //     0x9bafd8: movz            x1, #0xe15c
    //     0x9bafdc: movk            x1, #0x3, lsl #16
    //     0x9bafe0: stur            x1, [x0, #-1]
    // 0x9bafe4: StoreField: r0->field_7 = d2
    //     0x9bafe4: stur            d2, [x0, #7]
    // 0x9bafe8: ldr             x1, [fp, #0x20]
    // 0x9bafec: stur            x0, [fp, #-8]
    // 0x9baff0: r0 = of()
    //     0x9baff0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9baff4: LoadField: r1 = r0->field_5b
    //     0x9baff4: ldur            w1, [x0, #0x5b]
    // 0x9baff8: DecompressPointer r1
    //     0x9baff8: add             x1, x1, HEAP, lsl #32
    // 0x9baffc: stur            x1, [fp, #-0x10]
    // 0x9bb000: r0 = CircularProgressIndicator()
    //     0x9bb000: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0x9bb004: mov             x1, x0
    // 0x9bb008: r0 = Instance__ActivityIndicatorType
    //     0x9bb008: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0x9bb00c: ldr             x0, [x0, #0x1b0]
    // 0x9bb010: stur            x1, [fp, #-0x18]
    // 0x9bb014: StoreField: r1->field_23 = r0
    //     0x9bb014: stur            w0, [x1, #0x23]
    // 0x9bb018: ldur            x0, [fp, #-8]
    // 0x9bb01c: StoreField: r1->field_b = r0
    //     0x9bb01c: stur            w0, [x1, #0xb]
    // 0x9bb020: ldur            x0, [fp, #-0x10]
    // 0x9bb024: StoreField: r1->field_13 = r0
    //     0x9bb024: stur            w0, [x1, #0x13]
    // 0x9bb028: r0 = SizedBox()
    //     0x9bb028: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9bb02c: mov             x1, x0
    // 0x9bb030: r0 = 25.000000
    //     0x9bb030: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x9bb034: ldr             x0, [x0, #0x98]
    // 0x9bb038: stur            x1, [fp, #-8]
    // 0x9bb03c: StoreField: r1->field_f = r0
    //     0x9bb03c: stur            w0, [x1, #0xf]
    // 0x9bb040: StoreField: r1->field_13 = r0
    //     0x9bb040: stur            w0, [x1, #0x13]
    // 0x9bb044: ldur            x0, [fp, #-0x18]
    // 0x9bb048: StoreField: r1->field_b = r0
    //     0x9bb048: stur            w0, [x1, #0xb]
    // 0x9bb04c: r0 = Center()
    //     0x9bb04c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9bb050: r1 = Instance_Alignment
    //     0x9bb050: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9bb054: ldr             x1, [x1, #0xb10]
    // 0x9bb058: StoreField: r0->field_f = r1
    //     0x9bb058: stur            w1, [x0, #0xf]
    // 0x9bb05c: ldur            x1, [fp, #-8]
    // 0x9bb060: StoreField: r0->field_b = r1
    //     0x9bb060: stur            w1, [x0, #0xb]
    // 0x9bb064: LeaveFrame
    //     0x9bb064: mov             SP, fp
    //     0x9bb068: ldp             fp, lr, [SP], #0x10
    // 0x9bb06c: ret
    //     0x9bb06c: ret             
    // 0x9bb070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9bb070: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9bb074: b               #0x9baf80
    // 0x9bb078: SaveReg d2
    //     0x9bb078: str             q2, [SP, #-0x10]!
    // 0x9bb07c: r0 = AllocateDouble()
    //     0x9bb07c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9bb080: RestoreReg d2
    //     0x9bb080: ldr             q2, [SP], #0x10
    // 0x9bb084: b               #0x9bafe4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9bb088, size: 0xfc
    // 0x9bb088: EnterFrame
    //     0x9bb088: stp             fp, lr, [SP, #-0x10]!
    //     0x9bb08c: mov             fp, SP
    // 0x9bb090: AllocStack(0x28)
    //     0x9bb090: sub             SP, SP, #0x28
    // 0x9bb094: SetupParameters()
    //     0x9bb094: ldr             x0, [fp, #0x10]
    //     0x9bb098: ldur            w2, [x0, #0x17]
    //     0x9bb09c: add             x2, x2, HEAP, lsl #32
    //     0x9bb0a0: stur            x2, [fp, #-8]
    // 0x9bb0a4: CheckStackOverflow
    //     0x9bb0a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9bb0a8: cmp             SP, x16
    //     0x9bb0ac: b.ls            #0x9bb17c
    // 0x9bb0b0: LoadField: r0 = r2->field_b
    //     0x9bb0b0: ldur            w0, [x2, #0xb]
    // 0x9bb0b4: DecompressPointer r0
    //     0x9bb0b4: add             x0, x0, HEAP, lsl #32
    // 0x9bb0b8: LoadField: r1 = r0->field_f
    //     0x9bb0b8: ldur            w1, [x0, #0xf]
    // 0x9bb0bc: DecompressPointer r1
    //     0x9bb0bc: add             x1, x1, HEAP, lsl #32
    // 0x9bb0c0: r0 = controller()
    //     0x9bb0c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9bb0c4: ldur            x2, [fp, #-8]
    // 0x9bb0c8: stur            x0, [fp, #-0x10]
    // 0x9bb0cc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9bb0cc: ldur            w1, [x2, #0x17]
    // 0x9bb0d0: DecompressPointer r1
    //     0x9bb0d0: add             x1, x1, HEAP, lsl #32
    // 0x9bb0d4: str             x1, [SP]
    // 0x9bb0d8: r4 = 0
    //     0x9bb0d8: movz            x4, #0
    // 0x9bb0dc: ldr             x0, [SP]
    // 0x9bb0e0: r16 = UnlinkedCall_0x613b5c
    //     0x9bb0e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9bb0e4: add             x16, x16, #0xc50
    // 0x9bb0e8: ldp             x5, lr, [x16]
    // 0x9bb0ec: blr             lr
    // 0x9bb0f0: str             x0, [SP]
    // 0x9bb0f4: ldur            x1, [fp, #-0x10]
    // 0x9bb0f8: r2 = "single_media"
    //     0x9bb0f8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0x9bb0fc: ldr             x2, [x2, #0xab0]
    // 0x9bb100: r4 = const [0, 0x3, 0x1, 0x2, selectedOption, 0x2, null]
    //     0x9bb100: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf8] List(7) [0, 0x3, 0x1, 0x2, "selectedOption", 0x2, Null]
    //     0x9bb104: ldr             x4, [x4, #0xaf8]
    // 0x9bb108: r0 = ratingReviewClickedEvent()
    //     0x9bb108: bl              #0x999a0c  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::ratingReviewClickedEvent
    // 0x9bb10c: ldur            x2, [fp, #-8]
    // 0x9bb110: LoadField: r1 = r2->field_f
    //     0x9bb110: ldur            w1, [x2, #0xf]
    // 0x9bb114: DecompressPointer r1
    //     0x9bb114: add             x1, x1, HEAP, lsl #32
    // 0x9bb118: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9bb118: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9bb11c: r0 = of()
    //     0x9bb11c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x9bb120: ldur            x2, [fp, #-8]
    // 0x9bb124: r1 = Function '<anonymous closure>':.
    //     0x9bb124: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc60] AnonymousClosure: (0x9bb184), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9bb128: ldr             x1, [x1, #0xc60]
    // 0x9bb12c: stur            x0, [fp, #-8]
    // 0x9bb130: r0 = AllocateClosure()
    //     0x9bb130: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9bb134: r1 = Null
    //     0x9bb134: mov             x1, NULL
    // 0x9bb138: stur            x0, [fp, #-0x10]
    // 0x9bb13c: r0 = MaterialPageRoute()
    //     0x9bb13c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x9bb140: mov             x1, x0
    // 0x9bb144: ldur            x2, [fp, #-0x10]
    // 0x9bb148: stur            x0, [fp, #-0x10]
    // 0x9bb14c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9bb14c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9bb150: r0 = MaterialPageRoute()
    //     0x9bb150: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x9bb154: ldur            x16, [fp, #-8]
    // 0x9bb158: stp             x16, NULL, [SP, #8]
    // 0x9bb15c: ldur            x16, [fp, #-0x10]
    // 0x9bb160: str             x16, [SP]
    // 0x9bb164: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9bb164: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9bb168: r0 = push()
    //     0x9bb168: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x9bb16c: r0 = Null
    //     0x9bb16c: mov             x0, NULL
    // 0x9bb170: LeaveFrame
    //     0x9bb170: mov             SP, fp
    //     0x9bb174: ldp             fp, lr, [SP], #0x10
    // 0x9bb178: ret
    //     0x9bb178: ret             
    // 0x9bb17c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9bb17c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9bb180: b               #0x9bb0b0
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x9bb184, size: 0x190
    // 0x9bb184: EnterFrame
    //     0x9bb184: stp             fp, lr, [SP, #-0x10]!
    //     0x9bb188: mov             fp, SP
    // 0x9bb18c: AllocStack(0x38)
    //     0x9bb18c: sub             SP, SP, #0x38
    // 0x9bb190: SetupParameters()
    //     0x9bb190: ldr             x0, [fp, #0x18]
    //     0x9bb194: ldur            w2, [x0, #0x17]
    //     0x9bb198: add             x2, x2, HEAP, lsl #32
    //     0x9bb19c: stur            x2, [fp, #-0x10]
    // 0x9bb1a0: CheckStackOverflow
    //     0x9bb1a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9bb1a4: cmp             SP, x16
    //     0x9bb1a8: b.ls            #0x9bb30c
    // 0x9bb1ac: LoadField: r0 = r2->field_b
    //     0x9bb1ac: ldur            w0, [x2, #0xb]
    // 0x9bb1b0: DecompressPointer r0
    //     0x9bb1b0: add             x0, x0, HEAP, lsl #32
    // 0x9bb1b4: stur            x0, [fp, #-8]
    // 0x9bb1b8: LoadField: r1 = r0->field_f
    //     0x9bb1b8: ldur            w1, [x0, #0xf]
    // 0x9bb1bc: DecompressPointer r1
    //     0x9bb1bc: add             x1, x1, HEAP, lsl #32
    // 0x9bb1c0: r0 = controller()
    //     0x9bb1c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9bb1c4: LoadField: r1 = r0->field_7f
    //     0x9bb1c4: ldur            w1, [x0, #0x7f]
    // 0x9bb1c8: DecompressPointer r1
    //     0x9bb1c8: add             x1, x1, HEAP, lsl #32
    // 0x9bb1cc: r0 = value()
    //     0x9bb1cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9bb1d0: LoadField: r1 = r0->field_f
    //     0x9bb1d0: ldur            w1, [x0, #0xf]
    // 0x9bb1d4: DecompressPointer r1
    //     0x9bb1d4: add             x1, x1, HEAP, lsl #32
    // 0x9bb1d8: cmp             w1, NULL
    // 0x9bb1dc: b.ne            #0x9bb1f4
    // 0x9bb1e0: r1 = <ReviewRatingEntity>
    //     0x9bb1e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0x9bb1e4: ldr             x1, [x1, #0x150]
    // 0x9bb1e8: r2 = 0
    //     0x9bb1e8: movz            x2, #0
    // 0x9bb1ec: r0 = _GrowableList()
    //     0x9bb1ec: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x9bb1f0: mov             x1, x0
    // 0x9bb1f4: ldur            x2, [fp, #-0x10]
    // 0x9bb1f8: ldur            x0, [fp, #-8]
    // 0x9bb1fc: stur            x1, [fp, #-0x20]
    // 0x9bb200: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9bb200: ldur            w3, [x2, #0x17]
    // 0x9bb204: DecompressPointer r3
    //     0x9bb204: add             x3, x3, HEAP, lsl #32
    // 0x9bb208: stur            x3, [fp, #-0x18]
    // 0x9bb20c: str             x3, [SP]
    // 0x9bb210: r4 = 0
    //     0x9bb210: movz            x4, #0
    // 0x9bb214: ldr             x0, [SP]
    // 0x9bb218: r16 = UnlinkedCall_0x613b5c
    //     0x9bb218: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9bb21c: add             x16, x16, #0xc68
    // 0x9bb220: ldp             x5, lr, [x16]
    // 0x9bb224: blr             lr
    // 0x9bb228: stur            x0, [fp, #-0x28]
    // 0x9bb22c: ldur            x16, [fp, #-0x18]
    // 0x9bb230: str             x16, [SP]
    // 0x9bb234: r4 = 0
    //     0x9bb234: movz            x4, #0
    // 0x9bb238: ldr             x0, [SP]
    // 0x9bb23c: r16 = UnlinkedCall_0x613b5c
    //     0x9bb23c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc78] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9bb240: add             x16, x16, #0xc78
    // 0x9bb244: ldp             x5, lr, [x16]
    // 0x9bb248: blr             lr
    // 0x9bb24c: mov             x3, x0
    // 0x9bb250: r2 = Null
    //     0x9bb250: mov             x2, NULL
    // 0x9bb254: r1 = Null
    //     0x9bb254: mov             x1, NULL
    // 0x9bb258: stur            x3, [fp, #-0x18]
    // 0x9bb25c: branchIfSmi(r0, 0x9bb284)
    //     0x9bb25c: tbz             w0, #0, #0x9bb284
    // 0x9bb260: r4 = LoadClassIdInstr(r0)
    //     0x9bb260: ldur            x4, [x0, #-1]
    //     0x9bb264: ubfx            x4, x4, #0xc, #0x14
    // 0x9bb268: sub             x4, x4, #0x3c
    // 0x9bb26c: cmp             x4, #1
    // 0x9bb270: b.ls            #0x9bb284
    // 0x9bb274: r8 = int?
    //     0x9bb274: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0x9bb278: r3 = Null
    //     0x9bb278: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fc88] Null
    //     0x9bb27c: ldr             x3, [x3, #0xc88]
    // 0x9bb280: r0 = int?()
    //     0x9bb280: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0x9bb284: ldur            x0, [fp, #-8]
    // 0x9bb288: LoadField: r1 = r0->field_f
    //     0x9bb288: ldur            w1, [x0, #0xf]
    // 0x9bb28c: DecompressPointer r1
    //     0x9bb28c: add             x1, x1, HEAP, lsl #32
    // 0x9bb290: r0 = controller()
    //     0x9bb290: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9bb294: LoadField: r1 = r0->field_97
    //     0x9bb294: ldur            w1, [x0, #0x97]
    // 0x9bb298: DecompressPointer r1
    //     0x9bb298: add             x1, x1, HEAP, lsl #32
    // 0x9bb29c: stur            x1, [fp, #-8]
    // 0x9bb2a0: r0 = RatingReviewAllMediaOnTapImage()
    //     0x9bb2a0: bl              #0x8ff180  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0x9bb2a4: mov             x3, x0
    // 0x9bb2a8: ldur            x0, [fp, #-0x20]
    // 0x9bb2ac: stur            x3, [fp, #-0x30]
    // 0x9bb2b0: StoreField: r3->field_f = r0
    //     0x9bb2b0: stur            w0, [x3, #0xf]
    // 0x9bb2b4: ldur            x0, [fp, #-0x28]
    // 0x9bb2b8: r1 = LoadInt32Instr(r0)
    //     0x9bb2b8: sbfx            x1, x0, #1, #0x1f
    //     0x9bb2bc: tbz             w0, #0, #0x9bb2c4
    //     0x9bb2c0: ldur            x1, [x0, #7]
    // 0x9bb2c4: StoreField: r3->field_13 = r1
    //     0x9bb2c4: stur            x1, [x3, #0x13]
    // 0x9bb2c8: ldur            x0, [fp, #-0x18]
    // 0x9bb2cc: StoreField: r3->field_1b = r0
    //     0x9bb2cc: stur            w0, [x3, #0x1b]
    // 0x9bb2d0: r0 = "direct_image"
    //     0x9bb2d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0x9bb2d4: ldr             x0, [x0, #0xc98]
    // 0x9bb2d8: StoreField: r3->field_b = r0
    //     0x9bb2d8: stur            w0, [x3, #0xb]
    // 0x9bb2dc: ldur            x2, [fp, #-0x10]
    // 0x9bb2e0: r1 = Function '<anonymous closure>':.
    //     0x9bb2e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fca0] AnonymousClosure: (0x9bb314), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x9bb2e4: ldr             x1, [x1, #0xca0]
    // 0x9bb2e8: r0 = AllocateClosure()
    //     0x9bb2e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9bb2ec: mov             x1, x0
    // 0x9bb2f0: ldur            x0, [fp, #-0x30]
    // 0x9bb2f4: StoreField: r0->field_1f = r1
    //     0x9bb2f4: stur            w1, [x0, #0x1f]
    // 0x9bb2f8: ldur            x1, [fp, #-8]
    // 0x9bb2fc: StoreField: r0->field_23 = r1
    //     0x9bb2fc: stur            w1, [x0, #0x23]
    // 0x9bb300: LeaveFrame
    //     0x9bb300: mov             SP, fp
    //     0x9bb304: ldp             fp, lr, [SP], #0x10
    // 0x9bb308: ret
    //     0x9bb308: ret             
    // 0x9bb30c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9bb30c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9bb310: b               #0x9bb1ac
  }
  [closure] Null <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0x9bb314, size: 0x58
    // 0x9bb314: EnterFrame
    //     0x9bb314: stp             fp, lr, [SP, #-0x10]!
    //     0x9bb318: mov             fp, SP
    // 0x9bb31c: ldr             x0, [fp, #0x18]
    // 0x9bb320: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9bb320: ldur            w1, [x0, #0x17]
    // 0x9bb324: DecompressPointer r1
    //     0x9bb324: add             x1, x1, HEAP, lsl #32
    // 0x9bb328: CheckStackOverflow
    //     0x9bb328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9bb32c: cmp             SP, x16
    //     0x9bb330: b.ls            #0x9bb364
    // 0x9bb334: LoadField: r0 = r1->field_b
    //     0x9bb334: ldur            w0, [x1, #0xb]
    // 0x9bb338: DecompressPointer r0
    //     0x9bb338: add             x0, x0, HEAP, lsl #32
    // 0x9bb33c: LoadField: r1 = r0->field_f
    //     0x9bb33c: ldur            w1, [x0, #0xf]
    // 0x9bb340: DecompressPointer r1
    //     0x9bb340: add             x1, x1, HEAP, lsl #32
    // 0x9bb344: r0 = controller()
    //     0x9bb344: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x9bb348: mov             x1, x0
    // 0x9bb34c: ldr             x2, [fp, #0x10]
    // 0x9bb350: r0 = saveFlaggedData()
    //     0x9bb350: bl              #0x9b1770  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::saveFlaggedData
    // 0x9bb354: r0 = Null
    //     0x9bb354: mov             x0, NULL
    // 0x9bb358: LeaveFrame
    //     0x9bb358: mov             SP, fp
    //     0x9bb35c: ldp             fp, lr, [SP], #0x10
    // 0x9bb360: ret
    //     0x9bb360: ret             
    // 0x9bb364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9bb364: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9bb368: b               #0x9bb334
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x146fba8, size: 0x50
    // 0x146fba8: EnterFrame
    //     0x146fba8: stp             fp, lr, [SP, #-0x10]!
    //     0x146fbac: mov             fp, SP
    // 0x146fbb0: ldr             x0, [fp, #0x10]
    // 0x146fbb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x146fbb4: ldur            w1, [x0, #0x17]
    // 0x146fbb8: DecompressPointer r1
    //     0x146fbb8: add             x1, x1, HEAP, lsl #32
    // 0x146fbbc: CheckStackOverflow
    //     0x146fbbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x146fbc0: cmp             SP, x16
    //     0x146fbc4: b.ls            #0x146fbf0
    // 0x146fbc8: LoadField: r0 = r1->field_f
    //     0x146fbc8: ldur            w0, [x1, #0xf]
    // 0x146fbcc: DecompressPointer r0
    //     0x146fbcc: add             x0, x0, HEAP, lsl #32
    // 0x146fbd0: mov             x1, x0
    // 0x146fbd4: r0 = controller()
    //     0x146fbd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x146fbd8: mov             x1, x0
    // 0x146fbdc: r0 = onLoadNextPage()
    //     0x146fbdc: bl              #0x146fbf8  ; [package:customer_app/app/presentation/controllers/product_detail/view_all_reviews_controller.dart] ViewAllReviewsController::onLoadNextPage
    // 0x146fbe0: r0 = Null
    //     0x146fbe0: mov             x0, NULL
    // 0x146fbe4: LeaveFrame
    //     0x146fbe4: mov             SP, fp
    //     0x146fbe8: ldp             fp, lr, [SP], #0x10
    // 0x146fbec: ret
    //     0x146fbec: ret             
    // 0x146fbf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x146fbf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x146fbf4: b               #0x146fbc8
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x146fc60, size: 0x5c
    // 0x146fc60: EnterFrame
    //     0x146fc60: stp             fp, lr, [SP, #-0x10]!
    //     0x146fc64: mov             fp, SP
    // 0x146fc68: AllocStack(0x10)
    //     0x146fc68: sub             SP, SP, #0x10
    // 0x146fc6c: SetupParameters(ReviewListWidget this /* r1 */)
    //     0x146fc6c: stur            NULL, [fp, #-8]
    //     0x146fc70: movz            x0, #0
    //     0x146fc74: add             x1, fp, w0, sxtw #2
    //     0x146fc78: ldr             x1, [x1, #0x10]
    //     0x146fc7c: ldur            w2, [x1, #0x17]
    //     0x146fc80: add             x2, x2, HEAP, lsl #32
    //     0x146fc84: stur            x2, [fp, #-0x10]
    // 0x146fc88: CheckStackOverflow
    //     0x146fc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x146fc8c: cmp             SP, x16
    //     0x146fc90: b.ls            #0x146fcb4
    // 0x146fc94: InitAsync() -> Future<void?>
    //     0x146fc94: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x146fc98: bl              #0x6326e0  ; InitAsyncStub
    // 0x146fc9c: ldur            x0, [fp, #-0x10]
    // 0x146fca0: LoadField: r1 = r0->field_f
    //     0x146fca0: ldur            w1, [x0, #0xf]
    // 0x146fca4: DecompressPointer r1
    //     0x146fca4: add             x1, x1, HEAP, lsl #32
    // 0x146fca8: r0 = controller()
    //     0x146fca8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x146fcac: r0 = Null
    //     0x146fcac: mov             x0, NULL
    // 0x146fcb0: r0 = ReturnAsyncNotFuture()
    //     0x146fcb0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x146fcb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x146fcb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x146fcb8: b               #0x146fc94
  }
  _ body(/* No info */) {
    // ** addr: 0x15093c4, size: 0xc0
    // 0x15093c4: EnterFrame
    //     0x15093c4: stp             fp, lr, [SP, #-0x10]!
    //     0x15093c8: mov             fp, SP
    // 0x15093cc: AllocStack(0x18)
    //     0x15093cc: sub             SP, SP, #0x18
    // 0x15093d0: SetupParameters(ReviewListWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15093d0: stur            x1, [fp, #-8]
    //     0x15093d4: stur            x2, [fp, #-0x10]
    // 0x15093d8: CheckStackOverflow
    //     0x15093d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15093dc: cmp             SP, x16
    //     0x15093e0: b.ls            #0x150947c
    // 0x15093e4: r1 = 2
    //     0x15093e4: movz            x1, #0x2
    // 0x15093e8: r0 = AllocateContext()
    //     0x15093e8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15093ec: mov             x1, x0
    // 0x15093f0: ldur            x0, [fp, #-8]
    // 0x15093f4: stur            x1, [fp, #-0x18]
    // 0x15093f8: StoreField: r1->field_f = r0
    //     0x15093f8: stur            w0, [x1, #0xf]
    // 0x15093fc: ldur            x0, [fp, #-0x10]
    // 0x1509400: StoreField: r1->field_13 = r0
    //     0x1509400: stur            w0, [x1, #0x13]
    // 0x1509404: r0 = Obx()
    //     0x1509404: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1509408: ldur            x2, [fp, #-0x18]
    // 0x150940c: r1 = Function '<anonymous closure>':.
    //     0x150940c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f208] AnonymousClosure: (0x9b348c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x1509410: ldr             x1, [x1, #0x208]
    // 0x1509414: stur            x0, [fp, #-8]
    // 0x1509418: r0 = AllocateClosure()
    //     0x1509418: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150941c: mov             x1, x0
    // 0x1509420: ldur            x0, [fp, #-8]
    // 0x1509424: StoreField: r0->field_b = r1
    //     0x1509424: stur            w1, [x0, #0xb]
    // 0x1509428: ldur            x2, [fp, #-0x18]
    // 0x150942c: r1 = Function '<anonymous closure>':.
    //     0x150942c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f210] AnonymousClosure: (0x146fc60), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x1509430: ldr             x1, [x1, #0x210]
    // 0x1509434: r0 = AllocateClosure()
    //     0x1509434: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509438: ldur            x2, [fp, #-0x18]
    // 0x150943c: r1 = Function '<anonymous closure>':.
    //     0x150943c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f218] AnonymousClosure: (0x146fba8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x1509440: ldr             x1, [x1, #0x218]
    // 0x1509444: stur            x0, [fp, #-0x10]
    // 0x1509448: r0 = AllocateClosure()
    //     0x1509448: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150944c: stur            x0, [fp, #-0x18]
    // 0x1509450: r0 = PagingView()
    //     0x1509450: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x1509454: mov             x1, x0
    // 0x1509458: ldur            x2, [fp, #-8]
    // 0x150945c: ldur            x3, [fp, #-0x18]
    // 0x1509460: ldur            x5, [fp, #-0x10]
    // 0x1509464: stur            x0, [fp, #-8]
    // 0x1509468: r0 = PagingView()
    //     0x1509468: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x150946c: ldur            x0, [fp, #-8]
    // 0x1509470: LeaveFrame
    //     0x1509470: mov             SP, fp
    //     0x1509474: ldp             fp, lr, [SP], #0x10
    // 0x1509478: ret
    //     0x1509478: ret             
    // 0x150947c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150947c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1509480: b               #0x15093e4
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15edb38, size: 0x38c
    // 0x15edb38: EnterFrame
    //     0x15edb38: stp             fp, lr, [SP, #-0x10]!
    //     0x15edb3c: mov             fp, SP
    // 0x15edb40: AllocStack(0x60)
    //     0x15edb40: sub             SP, SP, #0x60
    // 0x15edb44: SetupParameters(ReviewListWidget this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15edb44: mov             x0, x1
    //     0x15edb48: stur            x1, [fp, #-8]
    //     0x15edb4c: mov             x1, x2
    //     0x15edb50: stur            x2, [fp, #-0x10]
    // 0x15edb54: CheckStackOverflow
    //     0x15edb54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15edb58: cmp             SP, x16
    //     0x15edb5c: b.ls            #0x15edebc
    // 0x15edb60: r1 = 2
    //     0x15edb60: movz            x1, #0x2
    // 0x15edb64: r0 = AllocateContext()
    //     0x15edb64: bl              #0x16f6108  ; AllocateContextStub
    // 0x15edb68: mov             x2, x0
    // 0x15edb6c: ldur            x0, [fp, #-8]
    // 0x15edb70: stur            x2, [fp, #-0x18]
    // 0x15edb74: StoreField: r2->field_f = r0
    //     0x15edb74: stur            w0, [x2, #0xf]
    // 0x15edb78: ldur            x1, [fp, #-0x10]
    // 0x15edb7c: StoreField: r2->field_13 = r1
    //     0x15edb7c: stur            w1, [x2, #0x13]
    // 0x15edb80: r0 = of()
    //     0x15edb80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15edb84: LoadField: r1 = r0->field_5b
    //     0x15edb84: ldur            w1, [x0, #0x5b]
    // 0x15edb88: DecompressPointer r1
    //     0x15edb88: add             x1, x1, HEAP, lsl #32
    // 0x15edb8c: stur            x1, [fp, #-0x10]
    // 0x15edb90: r0 = ColorFilter()
    //     0x15edb90: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15edb94: mov             x1, x0
    // 0x15edb98: ldur            x0, [fp, #-0x10]
    // 0x15edb9c: stur            x1, [fp, #-0x20]
    // 0x15edba0: StoreField: r1->field_7 = r0
    //     0x15edba0: stur            w0, [x1, #7]
    // 0x15edba4: r0 = Instance_BlendMode
    //     0x15edba4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15edba8: ldr             x0, [x0, #0xb30]
    // 0x15edbac: StoreField: r1->field_b = r0
    //     0x15edbac: stur            w0, [x1, #0xb]
    // 0x15edbb0: r0 = 1
    //     0x15edbb0: movz            x0, #0x1
    // 0x15edbb4: StoreField: r1->field_13 = r0
    //     0x15edbb4: stur            x0, [x1, #0x13]
    // 0x15edbb8: r0 = SvgPicture()
    //     0x15edbb8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15edbbc: stur            x0, [fp, #-0x10]
    // 0x15edbc0: ldur            x16, [fp, #-0x20]
    // 0x15edbc4: str             x16, [SP]
    // 0x15edbc8: mov             x1, x0
    // 0x15edbcc: r2 = "assets/images/appbar_arrow.svg"
    //     0x15edbcc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15edbd0: ldr             x2, [x2, #0xa40]
    // 0x15edbd4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15edbd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15edbd8: ldr             x4, [x4, #0xa38]
    // 0x15edbdc: r0 = SvgPicture.asset()
    //     0x15edbdc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15edbe0: r0 = Align()
    //     0x15edbe0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15edbe4: mov             x1, x0
    // 0x15edbe8: r0 = Instance_Alignment
    //     0x15edbe8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15edbec: ldr             x0, [x0, #0xb10]
    // 0x15edbf0: stur            x1, [fp, #-0x20]
    // 0x15edbf4: StoreField: r1->field_f = r0
    //     0x15edbf4: stur            w0, [x1, #0xf]
    // 0x15edbf8: r0 = 1.500000
    //     0x15edbf8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd18] 1.5
    //     0x15edbfc: ldr             x0, [x0, #0xd18]
    // 0x15edc00: StoreField: r1->field_13 = r0
    //     0x15edc00: stur            w0, [x1, #0x13]
    // 0x15edc04: ArrayStore: r1[0] = r0  ; List_4
    //     0x15edc04: stur            w0, [x1, #0x17]
    // 0x15edc08: ldur            x0, [fp, #-0x10]
    // 0x15edc0c: StoreField: r1->field_b = r0
    //     0x15edc0c: stur            w0, [x1, #0xb]
    // 0x15edc10: r0 = InkWell()
    //     0x15edc10: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15edc14: mov             x3, x0
    // 0x15edc18: ldur            x0, [fp, #-0x20]
    // 0x15edc1c: stur            x3, [fp, #-0x10]
    // 0x15edc20: StoreField: r3->field_b = r0
    //     0x15edc20: stur            w0, [x3, #0xb]
    // 0x15edc24: ldur            x2, [fp, #-0x18]
    // 0x15edc28: r1 = Function '<anonymous closure>':.
    //     0x15edc28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd20] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x15edc2c: ldr             x1, [x1, #0xd20]
    // 0x15edc30: r0 = AllocateClosure()
    //     0x15edc30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15edc34: ldur            x2, [fp, #-0x10]
    // 0x15edc38: StoreField: r2->field_f = r0
    //     0x15edc38: stur            w0, [x2, #0xf]
    // 0x15edc3c: r0 = true
    //     0x15edc3c: add             x0, NULL, #0x20  ; true
    // 0x15edc40: StoreField: r2->field_43 = r0
    //     0x15edc40: stur            w0, [x2, #0x43]
    // 0x15edc44: r3 = Instance_BoxShape
    //     0x15edc44: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15edc48: ldr             x3, [x3, #0x80]
    // 0x15edc4c: StoreField: r2->field_47 = r3
    //     0x15edc4c: stur            w3, [x2, #0x47]
    // 0x15edc50: StoreField: r2->field_6f = r0
    //     0x15edc50: stur            w0, [x2, #0x6f]
    // 0x15edc54: r4 = false
    //     0x15edc54: add             x4, NULL, #0x30  ; false
    // 0x15edc58: StoreField: r2->field_73 = r4
    //     0x15edc58: stur            w4, [x2, #0x73]
    // 0x15edc5c: StoreField: r2->field_83 = r0
    //     0x15edc5c: stur            w0, [x2, #0x83]
    // 0x15edc60: StoreField: r2->field_7b = r4
    //     0x15edc60: stur            w4, [x2, #0x7b]
    // 0x15edc64: ldur            x5, [fp, #-0x18]
    // 0x15edc68: LoadField: r1 = r5->field_13
    //     0x15edc68: ldur            w1, [x5, #0x13]
    // 0x15edc6c: DecompressPointer r1
    //     0x15edc6c: add             x1, x1, HEAP, lsl #32
    // 0x15edc70: r0 = of()
    //     0x15edc70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15edc74: LoadField: r1 = r0->field_87
    //     0x15edc74: ldur            w1, [x0, #0x87]
    // 0x15edc78: DecompressPointer r1
    //     0x15edc78: add             x1, x1, HEAP, lsl #32
    // 0x15edc7c: LoadField: r0 = r1->field_2b
    //     0x15edc7c: ldur            w0, [x1, #0x2b]
    // 0x15edc80: DecompressPointer r0
    //     0x15edc80: add             x0, x0, HEAP, lsl #32
    // 0x15edc84: stur            x0, [fp, #-0x20]
    // 0x15edc88: r0 = Text()
    //     0x15edc88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15edc8c: mov             x1, x0
    // 0x15edc90: r0 = "Ratings & Reviews"
    //     0x15edc90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd28] "Ratings & Reviews"
    //     0x15edc94: ldr             x0, [x0, #0xd28]
    // 0x15edc98: stur            x1, [fp, #-0x28]
    // 0x15edc9c: StoreField: r1->field_b = r0
    //     0x15edc9c: stur            w0, [x1, #0xb]
    // 0x15edca0: ldur            x0, [fp, #-0x20]
    // 0x15edca4: StoreField: r1->field_13 = r0
    //     0x15edca4: stur            w0, [x1, #0x13]
    // 0x15edca8: r0 = Container()
    //     0x15edca8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15edcac: stur            x0, [fp, #-0x20]
    // 0x15edcb0: r16 = Instance_Color
    //     0x15edcb0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15edcb4: r30 = 1.000000
    //     0x15edcb4: ldr             lr, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15edcb8: stp             lr, x16, [SP]
    // 0x15edcbc: mov             x1, x0
    // 0x15edcc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, height, 0x2, null]
    //     0x15edcc0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd30] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "height", 0x2, Null]
    //     0x15edcc4: ldr             x4, [x4, #0xd30]
    // 0x15edcc8: r0 = Container()
    //     0x15edcc8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15edccc: r0 = PreferredSize()
    //     0x15edccc: bl              #0x15d6644  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0x15edcd0: mov             x3, x0
    // 0x15edcd4: r0 = Instance_Size
    //     0x15edcd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb70] Obj!Size@d6c3e1
    //     0x15edcd8: ldr             x0, [x0, #0xb70]
    // 0x15edcdc: stur            x3, [fp, #-0x30]
    // 0x15edce0: StoreField: r3->field_f = r0
    //     0x15edce0: stur            w0, [x3, #0xf]
    // 0x15edce4: ldur            x0, [fp, #-0x20]
    // 0x15edce8: StoreField: r3->field_b = r0
    //     0x15edce8: stur            w0, [x3, #0xb]
    // 0x15edcec: r1 = <Widget>
    //     0x15edcec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15edcf0: r2 = 0
    //     0x15edcf0: movz            x2, #0
    // 0x15edcf4: r0 = _GrowableList()
    //     0x15edcf4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x15edcf8: ldur            x1, [fp, #-8]
    // 0x15edcfc: stur            x0, [fp, #-8]
    // 0x15edd00: r0 = controller()
    //     0x15edd00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15edd04: LoadField: r1 = r0->field_7b
    //     0x15edd04: ldur            w1, [x0, #0x7b]
    // 0x15edd08: DecompressPointer r1
    //     0x15edd08: add             x1, x1, HEAP, lsl #32
    // 0x15edd0c: r0 = value()
    //     0x15edd0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15edd10: LoadField: r1 = r0->field_b
    //     0x15edd10: ldur            w1, [x0, #0xb]
    // 0x15edd14: DecompressPointer r1
    //     0x15edd14: add             x1, x1, HEAP, lsl #32
    // 0x15edd18: cmp             w1, NULL
    // 0x15edd1c: b.ne            #0x15edd28
    // 0x15edd20: r0 = Null
    //     0x15edd20: mov             x0, NULL
    // 0x15edd24: b               #0x15edd4c
    // 0x15edd28: LoadField: r0 = r1->field_f
    //     0x15edd28: ldur            w0, [x1, #0xf]
    // 0x15edd2c: DecompressPointer r0
    //     0x15edd2c: add             x0, x0, HEAP, lsl #32
    // 0x15edd30: cmp             w0, NULL
    // 0x15edd34: b.ne            #0x15edd40
    // 0x15edd38: r0 = Null
    //     0x15edd38: mov             x0, NULL
    // 0x15edd3c: b               #0x15edd4c
    // 0x15edd40: LoadField: r1 = r0->field_b
    //     0x15edd40: ldur            w1, [x0, #0xb]
    // 0x15edd44: DecompressPointer r1
    //     0x15edd44: add             x1, x1, HEAP, lsl #32
    // 0x15edd48: mov             x0, x1
    // 0x15edd4c: cmp             w0, NULL
    // 0x15edd50: b.ne            #0x15edd5c
    // 0x15edd54: ldur            x2, [fp, #-8]
    // 0x15edd58: b               #0x15ede78
    // 0x15edd5c: tbnz            w0, #4, #0x15ede74
    // 0x15edd60: ldur            x1, [fp, #-8]
    // 0x15edd64: r0 = SvgPicture()
    //     0x15edd64: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15edd68: mov             x1, x0
    // 0x15edd6c: r2 = "assets/images/shopdeck-tag.svg"
    //     0x15edd6c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd38] "assets/images/shopdeck-tag.svg"
    //     0x15edd70: ldr             x2, [x2, #0xd38]
    // 0x15edd74: stur            x0, [fp, #-0x20]
    // 0x15edd78: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15edd78: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15edd7c: r0 = SvgPicture.asset()
    //     0x15edd7c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15edd80: r0 = InkWell()
    //     0x15edd80: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15edd84: mov             x3, x0
    // 0x15edd88: ldur            x0, [fp, #-0x20]
    // 0x15edd8c: stur            x3, [fp, #-0x38]
    // 0x15edd90: StoreField: r3->field_b = r0
    //     0x15edd90: stur            w0, [x3, #0xb]
    // 0x15edd94: ldur            x2, [fp, #-0x18]
    // 0x15edd98: r1 = Function '<anonymous closure>':.
    //     0x15edd98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd40] AnonymousClosure: (0x999964), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0x15edd9c: ldr             x1, [x1, #0xd40]
    // 0x15edda0: r0 = AllocateClosure()
    //     0x15edda0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15edda4: mov             x1, x0
    // 0x15edda8: ldur            x0, [fp, #-0x38]
    // 0x15eddac: StoreField: r0->field_f = r1
    //     0x15eddac: stur            w1, [x0, #0xf]
    // 0x15eddb0: r1 = true
    //     0x15eddb0: add             x1, NULL, #0x20  ; true
    // 0x15eddb4: StoreField: r0->field_43 = r1
    //     0x15eddb4: stur            w1, [x0, #0x43]
    // 0x15eddb8: r2 = Instance_BoxShape
    //     0x15eddb8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15eddbc: ldr             x2, [x2, #0x80]
    // 0x15eddc0: StoreField: r0->field_47 = r2
    //     0x15eddc0: stur            w2, [x0, #0x47]
    // 0x15eddc4: StoreField: r0->field_6f = r1
    //     0x15eddc4: stur            w1, [x0, #0x6f]
    // 0x15eddc8: r2 = false
    //     0x15eddc8: add             x2, NULL, #0x30  ; false
    // 0x15eddcc: StoreField: r0->field_73 = r2
    //     0x15eddcc: stur            w2, [x0, #0x73]
    // 0x15eddd0: StoreField: r0->field_83 = r1
    //     0x15eddd0: stur            w1, [x0, #0x83]
    // 0x15eddd4: StoreField: r0->field_7b = r2
    //     0x15eddd4: stur            w2, [x0, #0x7b]
    // 0x15eddd8: r0 = Padding()
    //     0x15eddd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15edddc: mov             x2, x0
    // 0x15edde0: r0 = Instance_EdgeInsets
    //     0x15edde0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0x15edde4: ldr             x0, [x0, #0xd48]
    // 0x15edde8: stur            x2, [fp, #-0x18]
    // 0x15eddec: StoreField: r2->field_f = r0
    //     0x15eddec: stur            w0, [x2, #0xf]
    // 0x15eddf0: ldur            x0, [fp, #-0x38]
    // 0x15eddf4: StoreField: r2->field_b = r0
    //     0x15eddf4: stur            w0, [x2, #0xb]
    // 0x15eddf8: ldur            x0, [fp, #-8]
    // 0x15eddfc: LoadField: r1 = r0->field_b
    //     0x15eddfc: ldur            w1, [x0, #0xb]
    // 0x15ede00: LoadField: r3 = r0->field_f
    //     0x15ede00: ldur            w3, [x0, #0xf]
    // 0x15ede04: DecompressPointer r3
    //     0x15ede04: add             x3, x3, HEAP, lsl #32
    // 0x15ede08: LoadField: r4 = r3->field_b
    //     0x15ede08: ldur            w4, [x3, #0xb]
    // 0x15ede0c: r3 = LoadInt32Instr(r1)
    //     0x15ede0c: sbfx            x3, x1, #1, #0x1f
    // 0x15ede10: stur            x3, [fp, #-0x40]
    // 0x15ede14: r1 = LoadInt32Instr(r4)
    //     0x15ede14: sbfx            x1, x4, #1, #0x1f
    // 0x15ede18: cmp             x3, x1
    // 0x15ede1c: b.ne            #0x15ede28
    // 0x15ede20: mov             x1, x0
    // 0x15ede24: r0 = _growToNextCapacity()
    //     0x15ede24: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15ede28: ldur            x2, [fp, #-8]
    // 0x15ede2c: ldur            x3, [fp, #-0x40]
    // 0x15ede30: add             x0, x3, #1
    // 0x15ede34: lsl             x1, x0, #1
    // 0x15ede38: StoreField: r2->field_b = r1
    //     0x15ede38: stur            w1, [x2, #0xb]
    // 0x15ede3c: LoadField: r1 = r2->field_f
    //     0x15ede3c: ldur            w1, [x2, #0xf]
    // 0x15ede40: DecompressPointer r1
    //     0x15ede40: add             x1, x1, HEAP, lsl #32
    // 0x15ede44: ldur            x0, [fp, #-0x18]
    // 0x15ede48: ArrayStore: r1[r3] = r0  ; List_4
    //     0x15ede48: add             x25, x1, x3, lsl #2
    //     0x15ede4c: add             x25, x25, #0xf
    //     0x15ede50: str             w0, [x25]
    //     0x15ede54: tbz             w0, #0, #0x15ede70
    //     0x15ede58: ldurb           w16, [x1, #-1]
    //     0x15ede5c: ldurb           w17, [x0, #-1]
    //     0x15ede60: and             x16, x17, x16, lsr #2
    //     0x15ede64: tst             x16, HEAP, lsr #32
    //     0x15ede68: b.eq            #0x15ede70
    //     0x15ede6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15ede70: b               #0x15ede78
    // 0x15ede74: ldur            x2, [fp, #-8]
    // 0x15ede78: r0 = AppBar()
    //     0x15ede78: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ede7c: stur            x0, [fp, #-0x18]
    // 0x15ede80: r16 = 0.000000
    //     0x15ede80: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x15ede84: ldur            lr, [fp, #-0x28]
    // 0x15ede88: stp             lr, x16, [SP, #0x10]
    // 0x15ede8c: ldur            x16, [fp, #-0x30]
    // 0x15ede90: ldur            lr, [fp, #-8]
    // 0x15ede94: stp             lr, x16, [SP]
    // 0x15ede98: mov             x1, x0
    // 0x15ede9c: ldur            x2, [fp, #-0x10]
    // 0x15edea0: r4 = const [0, 0x6, 0x4, 0x2, actions, 0x5, bottom, 0x4, title, 0x3, titleSpacing, 0x2, null]
    //     0x15edea0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd50] List(13) [0, 0x6, 0x4, 0x2, "actions", 0x5, "bottom", 0x4, "title", 0x3, "titleSpacing", 0x2, Null]
    //     0x15edea4: ldr             x4, [x4, #0xd50]
    // 0x15edea8: r0 = AppBar()
    //     0x15edea8: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15edeac: ldur            x0, [fp, #-0x18]
    // 0x15edeb0: LeaveFrame
    //     0x15edeb0: mov             SP, fp
    //     0x15edeb4: ldp             fp, lr, [SP], #0x10
    // 0x15edeb8: ret
    //     0x15edeb8: ret             
    // 0x15edebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15edebc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15edec0: b               #0x15edb60
  }
}
