// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart

// class id: 1049315, size: 0x8
class :: {
}

// class id: 3402, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin extends State<dynamic>
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ deactivate(/* No info */) {
    // ** addr: 0x7f3728, size: 0x40
    // 0x7f3728: EnterFrame
    //     0x7f3728: stp             fp, lr, [SP, #-0x10]!
    //     0x7f372c: mov             fp, SP
    // 0x7f3730: CheckStackOverflow
    //     0x7f3730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f3734: cmp             SP, x16
    //     0x7f3738: b.ls            #0x7f3760
    // 0x7f373c: LoadField: r0 = r1->field_13
    //     0x7f373c: ldur            w0, [x1, #0x13]
    // 0x7f3740: DecompressPointer r0
    //     0x7f3740: add             x0, x0, HEAP, lsl #32
    // 0x7f3744: cmp             w0, NULL
    // 0x7f3748: b.eq            #0x7f3750
    // 0x7f374c: r0 = _releaseKeepAlive()
    //     0x7f374c: bl              #0x7f36b0  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0x7f3750: r0 = Null
    //     0x7f3750: mov             x0, NULL
    // 0x7f3754: LeaveFrame
    //     0x7f3754: mov             SP, fp
    //     0x7f3758: ldp             fp, lr, [SP], #0x10
    // 0x7f375c: ret
    //     0x7f375c: ret             
    // 0x7f3760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f3760: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f3764: b               #0x7f373c
  }
  _ initState(/* No info */) {
    // ** addr: 0x93c698, size: 0x30
    // 0x93c698: EnterFrame
    //     0x93c698: stp             fp, lr, [SP, #-0x10]!
    //     0x93c69c: mov             fp, SP
    // 0x93c6a0: CheckStackOverflow
    //     0x93c6a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93c6a4: cmp             SP, x16
    //     0x93c6a8: b.ls            #0x93c6c0
    // 0x93c6ac: r0 = _ensureKeepAlive()
    //     0x93c6ac: bl              #0x80e0d4  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0x93c6b0: r0 = Null
    //     0x93c6b0: mov             x0, NULL
    // 0x93c6b4: LeaveFrame
    //     0x93c6b4: mov             SP, fp
    //     0x93c6b8: ldp             fp, lr, [SP], #0x10
    // 0x93c6bc: ret
    //     0x93c6bc: ret             
    // 0x93c6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93c6c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93c6c4: b               #0x93c6ac
  }
  _ build(/* No info */) {
    // ** addr: 0xb0e058, size: 0x44
    // 0xb0e058: EnterFrame
    //     0xb0e058: stp             fp, lr, [SP, #-0x10]!
    //     0xb0e05c: mov             fp, SP
    // 0xb0e060: CheckStackOverflow
    //     0xb0e060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0e064: cmp             SP, x16
    //     0xb0e068: b.ls            #0xb0e094
    // 0xb0e06c: LoadField: r0 = r1->field_13
    //     0xb0e06c: ldur            w0, [x1, #0x13]
    // 0xb0e070: DecompressPointer r0
    //     0xb0e070: add             x0, x0, HEAP, lsl #32
    // 0xb0e074: cmp             w0, NULL
    // 0xb0e078: b.ne            #0xb0e080
    // 0xb0e07c: r0 = _ensureKeepAlive()
    //     0xb0e07c: bl              #0x80e0d4  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xb0e080: r0 = Instance__NullWidget
    //     0xb0e080: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b08] Obj!_NullWidget@d66b41
    //     0xb0e084: ldr             x0, [x0, #0xb08]
    // 0xb0e088: LeaveFrame
    //     0xb0e088: mov             SP, fp
    //     0xb0e08c: ldp             fp, lr, [SP], #0x10
    // 0xb0e090: ret
    //     0xb0e090: ret             
    // 0xb0e094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0e094: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0e098: b               #0xb0e06c
  }
}

// class id: 3403, size: 0x34, field offset: 0x18
class _ProductMediaCarouselState extends __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x803988, size: 0x184
    // 0x803988: EnterFrame
    //     0x803988: stp             fp, lr, [SP, #-0x10]!
    //     0x80398c: mov             fp, SP
    // 0x803990: AllocStack(0x20)
    //     0x803990: sub             SP, SP, #0x20
    // 0x803994: SetupParameters(_ProductMediaCarouselState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x803994: mov             x4, x1
    //     0x803998: mov             x3, x2
    //     0x80399c: stur            x1, [fp, #-8]
    //     0x8039a0: stur            x2, [fp, #-0x10]
    // 0x8039a4: CheckStackOverflow
    //     0x8039a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8039a8: cmp             SP, x16
    //     0x8039ac: b.ls            #0x803b00
    // 0x8039b0: mov             x0, x3
    // 0x8039b4: r2 = Null
    //     0x8039b4: mov             x2, NULL
    // 0x8039b8: r1 = Null
    //     0x8039b8: mov             x1, NULL
    // 0x8039bc: r4 = 60
    //     0x8039bc: movz            x4, #0x3c
    // 0x8039c0: branchIfSmi(r0, 0x8039cc)
    //     0x8039c0: tbz             w0, #0, #0x8039cc
    // 0x8039c4: r4 = LoadClassIdInstr(r0)
    //     0x8039c4: ldur            x4, [x0, #-1]
    //     0x8039c8: ubfx            x4, x4, #0xc, #0x14
    // 0x8039cc: r17 = 4140
    //     0x8039cc: movz            x17, #0x102c
    // 0x8039d0: cmp             x4, x17
    // 0x8039d4: b.eq            #0x8039ec
    // 0x8039d8: r8 = ProductMediaCarousel
    //     0x8039d8: add             x8, PP, #0x57, lsl #12  ; [pp+0x57cf0] Type: ProductMediaCarousel
    //     0x8039dc: ldr             x8, [x8, #0xcf0]
    // 0x8039e0: r3 = Null
    //     0x8039e0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57cf8] Null
    //     0x8039e4: ldr             x3, [x3, #0xcf8]
    // 0x8039e8: r0 = ProductMediaCarousel()
    //     0x8039e8: bl              #0x7f3768  ; IsType_ProductMediaCarousel_Stub
    // 0x8039ec: ldur            x3, [fp, #-8]
    // 0x8039f0: LoadField: r2 = r3->field_7
    //     0x8039f0: ldur            w2, [x3, #7]
    // 0x8039f4: DecompressPointer r2
    //     0x8039f4: add             x2, x2, HEAP, lsl #32
    // 0x8039f8: ldur            x0, [fp, #-0x10]
    // 0x8039fc: r1 = Null
    //     0x8039fc: mov             x1, NULL
    // 0x803a00: cmp             w2, NULL
    // 0x803a04: b.eq            #0x803a28
    // 0x803a08: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x803a08: ldur            w4, [x2, #0x17]
    // 0x803a0c: DecompressPointer r4
    //     0x803a0c: add             x4, x4, HEAP, lsl #32
    // 0x803a10: r8 = X0 bound StatefulWidget
    //     0x803a10: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x803a14: ldr             x8, [x8, #0x7a0]
    // 0x803a18: LoadField: r9 = r4->field_7
    //     0x803a18: ldur            x9, [x4, #7]
    // 0x803a1c: r3 = Null
    //     0x803a1c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d08] Null
    //     0x803a20: ldr             x3, [x3, #0xd08]
    // 0x803a24: blr             x9
    // 0x803a28: ldur            x0, [fp, #-0x10]
    // 0x803a2c: LoadField: r1 = r0->field_b
    //     0x803a2c: ldur            w1, [x0, #0xb]
    // 0x803a30: DecompressPointer r1
    //     0x803a30: add             x1, x1, HEAP, lsl #32
    // 0x803a34: ldur            x2, [fp, #-8]
    // 0x803a38: LoadField: r3 = r2->field_b
    //     0x803a38: ldur            w3, [x2, #0xb]
    // 0x803a3c: DecompressPointer r3
    //     0x803a3c: add             x3, x3, HEAP, lsl #32
    // 0x803a40: cmp             w3, NULL
    // 0x803a44: b.eq            #0x803b08
    // 0x803a48: LoadField: r4 = r3->field_b
    //     0x803a48: ldur            w4, [x3, #0xb]
    // 0x803a4c: DecompressPointer r4
    //     0x803a4c: add             x4, x4, HEAP, lsl #32
    // 0x803a50: cmp             w1, w4
    // 0x803a54: b.eq            #0x803a60
    // 0x803a58: mov             x0, x2
    // 0x803a5c: b               #0x803a94
    // 0x803a60: LoadField: r1 = r0->field_f
    //     0x803a60: ldur            w1, [x0, #0xf]
    // 0x803a64: DecompressPointer r1
    //     0x803a64: add             x1, x1, HEAP, lsl #32
    // 0x803a68: LoadField: r0 = r3->field_f
    //     0x803a68: ldur            w0, [x3, #0xf]
    // 0x803a6c: DecompressPointer r0
    //     0x803a6c: add             x0, x0, HEAP, lsl #32
    // 0x803a70: r3 = LoadClassIdInstr(r1)
    //     0x803a70: ldur            x3, [x1, #-1]
    //     0x803a74: ubfx            x3, x3, #0xc, #0x14
    // 0x803a78: stp             x0, x1, [SP]
    // 0x803a7c: mov             x0, x3
    // 0x803a80: mov             lr, x0
    // 0x803a84: ldr             lr, [x21, lr, lsl #3]
    // 0x803a88: blr             lr
    // 0x803a8c: tbz             w0, #4, #0x803af0
    // 0x803a90: ldur            x0, [fp, #-8]
    // 0x803a94: LoadField: r1 = r0->field_27
    //     0x803a94: ldur            w1, [x0, #0x27]
    // 0x803a98: DecompressPointer r1
    //     0x803a98: add             x1, x1, HEAP, lsl #32
    // 0x803a9c: r0 = clear()
    //     0x803a9c: bl              #0x649b54  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x803aa0: ldur            x0, [fp, #-8]
    // 0x803aa4: StoreField: r0->field_2f = rNULL
    //     0x803aa4: stur            NULL, [x0, #0x2f]
    // 0x803aa8: mov             x1, x0
    // 0x803aac: r0 = _initializeMediaItems()
    //     0x803aac: bl              #0x803fbc  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems
    // 0x803ab0: ldur            x1, [fp, #-8]
    // 0x803ab4: r0 = _preloadImageSizes()
    //     0x803ab4: bl              #0x803b0c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0x803ab8: ldur            x0, [fp, #-8]
    // 0x803abc: StoreField: r0->field_1b = rZR
    //     0x803abc: stur            xzr, [x0, #0x1b]
    // 0x803ac0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x803ac0: ldur            w1, [x0, #0x17]
    // 0x803ac4: DecompressPointer r1
    //     0x803ac4: add             x1, x1, HEAP, lsl #32
    // 0x803ac8: LoadField: r0 = r1->field_3b
    //     0x803ac8: ldur            w0, [x1, #0x3b]
    // 0x803acc: DecompressPointer r0
    //     0x803acc: add             x0, x0, HEAP, lsl #32
    // 0x803ad0: LoadField: r2 = r0->field_b
    //     0x803ad0: ldur            w2, [x0, #0xb]
    // 0x803ad4: cbz             w2, #0x803af0
    // 0x803ad8: r2 = 0
    //     0x803ad8: movz            x2, #0
    // 0x803adc: r3 = Instance_Cubic
    //     0x803adc: add             x3, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0x803ae0: ldr             x3, [x3, #0x2b0]
    // 0x803ae4: r5 = Instance_Duration
    //     0x803ae4: add             x5, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x803ae8: ldr             x5, [x5, #0xf00]
    // 0x803aec: r0 = animateToPage()
    //     0x803aec: bl              #0x7f6cd0  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0x803af0: r0 = Null
    //     0x803af0: mov             x0, NULL
    // 0x803af4: LeaveFrame
    //     0x803af4: mov             SP, fp
    //     0x803af8: ldp             fp, lr, [SP], #0x10
    // 0x803afc: ret
    //     0x803afc: ret             
    // 0x803b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803b00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803b04: b               #0x8039b0
    // 0x803b08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803b08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _preloadImageSizes(/* No info */) async {
    // ** addr: 0x803b0c, size: 0x3f8
    // 0x803b0c: EnterFrame
    //     0x803b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x803b10: mov             fp, SP
    // 0x803b14: AllocStack(0xb8)
    //     0x803b14: sub             SP, SP, #0xb8
    // 0x803b18: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x80 */)
    //     0x803b18: stur            NULL, [fp, #-8]
    //     0x803b1c: stur            x1, [fp, #-0x80]
    // 0x803b20: CheckStackOverflow
    //     0x803b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803b24: cmp             SP, x16
    //     0x803b28: b.ls            #0x803ee0
    // 0x803b2c: r1 = 1
    //     0x803b2c: movz            x1, #0x1
    // 0x803b30: r0 = AllocateContext()
    //     0x803b30: bl              #0x16f6108  ; AllocateContextStub
    // 0x803b34: mov             x2, x0
    // 0x803b38: ldur            x1, [fp, #-0x80]
    // 0x803b3c: stur            x2, [fp, #-0x88]
    // 0x803b40: StoreField: r2->field_f = r1
    //     0x803b40: stur            w1, [x2, #0xf]
    // 0x803b44: InitAsync() -> Future<void?>
    //     0x803b44: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x803b48: bl              #0x6326e0  ; InitAsyncStub
    // 0x803b4c: ldur            x1, [fp, #-0x80]
    // 0x803b50: LoadField: r0 = r1->field_2b
    //     0x803b50: ldur            w0, [x1, #0x2b]
    // 0x803b54: DecompressPointer r0
    //     0x803b54: add             x0, x0, HEAP, lsl #32
    // 0x803b58: tbnz            w0, #4, #0x803b64
    // 0x803b5c: r0 = Null
    //     0x803b5c: mov             x0, NULL
    // 0x803b60: r0 = ReturnAsyncNotFuture()
    //     0x803b60: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x803b64: ldur            x0, [fp, #-0x88]
    // 0x803b68: r1 = 3
    //     0x803b68: movz            x1, #0x3
    // 0x803b6c: r0 = AllocateContext()
    //     0x803b6c: bl              #0x16f6108  ; AllocateContextStub
    // 0x803b70: mov             x1, x0
    // 0x803b74: ldur            x0, [fp, #-0x88]
    // 0x803b78: StoreField: r1->field_b = r0
    //     0x803b78: stur            w0, [x1, #0xb]
    // 0x803b7c: StoreField: r1->field_f = rZR
    //     0x803b7c: stur            wzr, [x1, #0xf]
    // 0x803b80: ldur            x2, [fp, #-0x80]
    // 0x803b84: LoadField: r3 = r2->field_27
    //     0x803b84: ldur            w3, [x2, #0x27]
    // 0x803b88: DecompressPointer r3
    //     0x803b88: add             x3, x3, HEAP, lsl #32
    // 0x803b8c: stur            x3, [fp, #-0xa0]
    // 0x803b90: mov             x6, x1
    // 0x803b94: r5 = Null
    //     0x803b94: mov             x5, NULL
    // 0x803b98: r4 = 0
    //     0x803b98: movz            x4, #0
    // 0x803b9c: stur            x6, [fp, #-0x90]
    // 0x803ba0: stur            x5, [fp, #-0x98]
    // 0x803ba4: CheckStackOverflow
    //     0x803ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803ba8: cmp             SP, x16
    //     0x803bac: b.ls            #0x803ee8
    // 0x803bb0: LoadField: r7 = r2->field_23
    //     0x803bb0: ldur            w7, [x2, #0x23]
    // 0x803bb4: DecompressPointer r7
    //     0x803bb4: add             x7, x7, HEAP, lsl #32
    // 0x803bb8: LoadField: r0 = r7->field_b
    //     0x803bb8: ldur            w0, [x7, #0xb]
    // 0x803bbc: r1 = LoadInt32Instr(r0)
    //     0x803bbc: sbfx            x1, x0, #1, #0x1f
    // 0x803bc0: cmp             x4, x1
    // 0x803bc4: b.ge            #0x803ed8
    // 0x803bc8: mov             x0, x1
    // 0x803bcc: mov             x1, x4
    // 0x803bd0: cmp             x1, x0
    // 0x803bd4: b.hs            #0x803ef0
    // 0x803bd8: LoadField: r0 = r7->field_f
    //     0x803bd8: ldur            w0, [x7, #0xf]
    // 0x803bdc: DecompressPointer r0
    //     0x803bdc: add             x0, x0, HEAP, lsl #32
    // 0x803be0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x803be0: add             x16, x0, x4, lsl #2
    //     0x803be4: ldur            w1, [x16, #0xf]
    // 0x803be8: DecompressPointer r1
    //     0x803be8: add             x1, x1, HEAP, lsl #32
    // 0x803bec: stur            x1, [fp, #-0x88]
    // 0x803bf0: LoadField: r0 = r1->field_1f
    //     0x803bf0: ldur            w0, [x1, #0x1f]
    // 0x803bf4: DecompressPointer r0
    //     0x803bf4: add             x0, x0, HEAP, lsl #32
    // 0x803bf8: r4 = LoadClassIdInstr(r0)
    //     0x803bf8: ldur            x4, [x0, #-1]
    //     0x803bfc: ubfx            x4, x4, #0xc, #0x14
    // 0x803c00: r16 = "video"
    //     0x803c00: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x803c04: ldr             x16, [x16, #0xb50]
    // 0x803c08: stp             x16, x0, [SP]
    // 0x803c0c: mov             x0, x4
    // 0x803c10: mov             lr, x0
    // 0x803c14: ldr             lr, [x21, lr, lsl #3]
    // 0x803c18: blr             lr
    // 0x803c1c: tbz             w0, #4, #0x803e60
    // 0x803c20: ldur            x0, [fp, #-0x88]
    // 0x803c24: LoadField: r1 = r0->field_2b
    //     0x803c24: ldur            w1, [x0, #0x2b]
    // 0x803c28: DecompressPointer r1
    //     0x803c28: add             x1, x1, HEAP, lsl #32
    // 0x803c2c: cmp             w1, NULL
    // 0x803c30: b.ne            #0x803c3c
    // 0x803c34: r0 = Null
    //     0x803c34: mov             x0, NULL
    // 0x803c38: b               #0x803c68
    // 0x803c3c: LoadField: r0 = r1->field_b
    //     0x803c3c: ldur            w0, [x1, #0xb]
    // 0x803c40: DecompressPointer r0
    //     0x803c40: add             x0, x0, HEAP, lsl #32
    // 0x803c44: cmp             w0, NULL
    // 0x803c48: b.ne            #0x803c54
    // 0x803c4c: r0 = Null
    //     0x803c4c: mov             x0, NULL
    // 0x803c50: b               #0x803c68
    // 0x803c54: LoadField: r2 = r0->field_7
    //     0x803c54: ldur            w2, [x0, #7]
    // 0x803c58: cbnz            w2, #0x803c64
    // 0x803c5c: r0 = false
    //     0x803c5c: add             x0, NULL, #0x30  ; false
    // 0x803c60: b               #0x803c68
    // 0x803c64: r0 = true
    //     0x803c64: add             x0, NULL, #0x20  ; true
    // 0x803c68: cmp             w0, NULL
    // 0x803c6c: b.eq            #0x803e60
    // 0x803c70: tbnz            w0, #4, #0x803e60
    // 0x803c74: ldur            x4, [fp, #-0x90]
    // 0x803c78: ldur            x3, [fp, #-0xa0]
    // 0x803c7c: cmp             w1, NULL
    // 0x803c80: b.eq            #0x803ef4
    // 0x803c84: LoadField: r5 = r1->field_b
    //     0x803c84: ldur            w5, [x1, #0xb]
    // 0x803c88: DecompressPointer r5
    //     0x803c88: add             x5, x5, HEAP, lsl #32
    // 0x803c8c: stur            x5, [fp, #-0xa8]
    // 0x803c90: cmp             w5, NULL
    // 0x803c94: b.eq            #0x803ef8
    // 0x803c98: mov             x0, x5
    // 0x803c9c: StoreField: r4->field_13 = r0
    //     0x803c9c: stur            w0, [x4, #0x13]
    //     0x803ca0: ldurb           w16, [x4, #-1]
    //     0x803ca4: ldurb           w17, [x0, #-1]
    //     0x803ca8: and             x16, x17, x16, lsr #2
    //     0x803cac: tst             x16, HEAP, lsr #32
    //     0x803cb0: b.eq            #0x803cb8
    //     0x803cb4: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x803cb8: LoadField: r0 = r3->field_f
    //     0x803cb8: ldur            w0, [x3, #0xf]
    // 0x803cbc: DecompressPointer r0
    //     0x803cbc: add             x0, x0, HEAP, lsl #32
    // 0x803cc0: mov             x1, x3
    // 0x803cc4: mov             x2, x5
    // 0x803cc8: stur            x0, [fp, #-0x88]
    // 0x803ccc: r0 = _getValueOrData()
    //     0x803ccc: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x803cd0: mov             x1, x0
    // 0x803cd4: ldur            x0, [fp, #-0x88]
    // 0x803cd8: cmp             w0, w1
    // 0x803cdc: b.ne            #0x803df0
    // 0x803ce0: ldur            x0, [fp, #-0x80]
    // 0x803ce4: ldur            x2, [fp, #-0x90]
    // 0x803ce8: ldur            x1, [fp, #-0xa8]
    // 0x803cec: r0 = ImageSizeExtension.getImageSize()
    //     0x803cec: bl              #0x801f84  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageSizeExtension.getImageSize
    // 0x803cf0: mov             x1, x0
    // 0x803cf4: stur            x1, [fp, #-0x88]
    // 0x803cf8: r0 = Await()
    //     0x803cf8: bl              #0x63248c  ; AwaitStub
    // 0x803cfc: ldur            x3, [fp, #-0x90]
    // 0x803d00: ArrayStore: r3[0] = r0  ; List_4
    //     0x803d00: stur            w0, [x3, #0x17]
    //     0x803d04: tbz             w0, #0, #0x803d20
    //     0x803d08: ldurb           w16, [x3, #-1]
    //     0x803d0c: ldurb           w17, [x0, #-1]
    //     0x803d10: and             x16, x17, x16, lsr #2
    //     0x803d14: tst             x16, HEAP, lsr #32
    //     0x803d18: b.eq            #0x803d20
    //     0x803d1c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x803d20: ldur            x0, [fp, #-0x80]
    // 0x803d24: LoadField: r1 = r0->field_2b
    //     0x803d24: ldur            w1, [x0, #0x2b]
    // 0x803d28: DecompressPointer r1
    //     0x803d28: add             x1, x1, HEAP, lsl #32
    // 0x803d2c: tbz             w1, #4, #0x803d84
    // 0x803d30: LoadField: r1 = r0->field_f
    //     0x803d30: ldur            w1, [x0, #0xf]
    // 0x803d34: DecompressPointer r1
    //     0x803d34: add             x1, x1, HEAP, lsl #32
    // 0x803d38: cmp             w1, NULL
    // 0x803d3c: b.eq            #0x803d84
    // 0x803d40: mov             x2, x3
    // 0x803d44: r1 = Function '<anonymous closure>':.
    //     0x803d44: add             x1, PP, #0x57, lsl #12  ; [pp+0x57cd0] AnonymousClosure: (0x803f04), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes (0x803b0c)
    //     0x803d48: ldr             x1, [x1, #0xcd0]
    // 0x803d4c: r0 = AllocateClosure()
    //     0x803d4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x803d50: mov             x1, x0
    // 0x803d54: stur            x1, [fp, #-0x88]
    // 0x803d58: str             x1, [SP]
    // 0x803d5c: mov             x0, x1
    // 0x803d60: ClosureCall
    //     0x803d60: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x803d64: ldur            x2, [x0, #0x1f]
    //     0x803d68: blr             x2
    // 0x803d6c: ldur            x0, [fp, #-0x80]
    // 0x803d70: LoadField: r1 = r0->field_f
    //     0x803d70: ldur            w1, [x0, #0xf]
    // 0x803d74: DecompressPointer r1
    //     0x803d74: add             x1, x1, HEAP, lsl #32
    // 0x803d78: cmp             w1, NULL
    // 0x803d7c: b.eq            #0x803efc
    // 0x803d80: r0 = markNeedsBuild()
    //     0x803d80: bl              #0x78858c  ; [package:flutter/src/widgets/framework.dart] Element::markNeedsBuild
    // 0x803d84: ldur            x0, [fp, #-0x98]
    // 0x803d88: b               #0x803e64
    // 0x803d8c: sub             SP, fp, #0xb8
    // 0x803d90: stur            x0, [fp, #-0x88]
    // 0x803d94: r0 = InitLateStaticField(0x678) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x803d94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x803d98: ldr             x0, [x0, #0xcf0]
    //     0x803d9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x803da0: cmp             w0, w16
    //     0x803da4: b.ne            #0x803db0
    //     0x803da8: ldr             x2, [PP, #0x880]  ; [pp+0x880] Field <::.debugPrint>: static late (offset: 0x678)
    //     0x803dac: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x803db0: r1 = Null
    //     0x803db0: mov             x1, NULL
    // 0x803db4: r2 = 4
    //     0x803db4: movz            x2, #0x4
    // 0x803db8: r0 = AllocateArray()
    //     0x803db8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x803dbc: r16 = "Error loading image size: "
    //     0x803dbc: add             x16, PP, #0x52, lsl #12  ; [pp+0x529b0] "Error loading image size: "
    //     0x803dc0: ldr             x16, [x16, #0x9b0]
    // 0x803dc4: StoreField: r0->field_f = r16
    //     0x803dc4: stur            w16, [x0, #0xf]
    // 0x803dc8: ldur            x1, [fp, #-0x88]
    // 0x803dcc: StoreField: r0->field_13 = r1
    //     0x803dcc: stur            w1, [x0, #0x13]
    // 0x803dd0: str             x0, [SP]
    // 0x803dd4: r0 = _interpolate()
    //     0x803dd4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x803dd8: str             NULL, [SP]
    // 0x803ddc: mov             x1, x0
    // 0x803de0: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x803de0: ldr             x4, [PP, #0x890]  ; [pp+0x890] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x803de4: r0 = debugPrintThrottled()
    //     0x803de4: bl              #0x6368ec  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x803de8: ldur            x0, [fp, #-0x88]
    // 0x803dec: b               #0x803e64
    // 0x803df0: ldur            x5, [fp, #-0x90]
    // 0x803df4: LoadField: r0 = r5->field_f
    //     0x803df4: ldur            w0, [x5, #0xf]
    // 0x803df8: DecompressPointer r0
    //     0x803df8: add             x0, x0, HEAP, lsl #32
    // 0x803dfc: cbnz            w0, #0x803e58
    // 0x803e00: ldur            x0, [fp, #-0x80]
    // 0x803e04: LoadField: r1 = r0->field_2f
    //     0x803e04: ldur            w1, [x0, #0x2f]
    // 0x803e08: DecompressPointer r1
    //     0x803e08: add             x1, x1, HEAP, lsl #32
    // 0x803e0c: cmp             w1, NULL
    // 0x803e10: b.ne            #0x803e58
    // 0x803e14: ldur            x3, [fp, #-0xa0]
    // 0x803e18: mov             x1, x3
    // 0x803e1c: ldur            x2, [fp, #-0xa8]
    // 0x803e20: r0 = _getValueOrData()
    //     0x803e20: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x803e24: mov             x1, x0
    // 0x803e28: ldur            x0, [fp, #-0xa0]
    // 0x803e2c: LoadField: r2 = r0->field_f
    //     0x803e2c: ldur            w2, [x0, #0xf]
    // 0x803e30: DecompressPointer r2
    //     0x803e30: add             x2, x2, HEAP, lsl #32
    // 0x803e34: cmp             w2, w1
    // 0x803e38: b.ne            #0x803e44
    // 0x803e3c: r2 = Null
    //     0x803e3c: mov             x2, NULL
    // 0x803e40: b               #0x803e48
    // 0x803e44: mov             x2, x1
    // 0x803e48: cmp             w2, NULL
    // 0x803e4c: b.eq            #0x803f00
    // 0x803e50: ldur            x1, [fp, #-0x80]
    // 0x803e54: r0 = _calculateCarouselHeight()
    //     0x803e54: bl              #0x801e60  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_calculateCarouselHeight
    // 0x803e58: ldur            x0, [fp, #-0x98]
    // 0x803e5c: b               #0x803e64
    // 0x803e60: ldur            x0, [fp, #-0x98]
    // 0x803e64: ldur            x5, [fp, #-0x90]
    // 0x803e68: stur            x0, [fp, #-0x88]
    // 0x803e6c: r0 = CloneContext()
    //     0x803e6c: bl              #0x16f5ae8  ; CloneContextStub
    // 0x803e70: mov             x2, x0
    // 0x803e74: LoadField: r3 = r2->field_f
    //     0x803e74: ldur            w3, [x2, #0xf]
    // 0x803e78: DecompressPointer r3
    //     0x803e78: add             x3, x3, HEAP, lsl #32
    // 0x803e7c: r4 = LoadInt32Instr(r3)
    //     0x803e7c: sbfx            x4, x3, #1, #0x1f
    //     0x803e80: tbz             w3, #0, #0x803e88
    //     0x803e84: ldur            x4, [x3, #7]
    // 0x803e88: add             x3, x4, #1
    // 0x803e8c: r0 = BoxInt64Instr(r3)
    //     0x803e8c: sbfiz           x0, x3, #1, #0x1f
    //     0x803e90: cmp             x3, x0, asr #1
    //     0x803e94: b.eq            #0x803ea0
    //     0x803e98: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x803e9c: stur            x3, [x0, #7]
    // 0x803ea0: StoreField: r2->field_f = r0
    //     0x803ea0: stur            w0, [x2, #0xf]
    //     0x803ea4: tbz             w0, #0, #0x803ec0
    //     0x803ea8: ldurb           w16, [x2, #-1]
    //     0x803eac: ldurb           w17, [x0, #-1]
    //     0x803eb0: and             x16, x17, x16, lsr #2
    //     0x803eb4: tst             x16, HEAP, lsr #32
    //     0x803eb8: b.eq            #0x803ec0
    //     0x803ebc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x803ec0: mov             x6, x2
    // 0x803ec4: ldur            x5, [fp, #-0x88]
    // 0x803ec8: mov             x4, x3
    // 0x803ecc: ldur            x2, [fp, #-0x80]
    // 0x803ed0: ldur            x3, [fp, #-0xa0]
    // 0x803ed4: b               #0x803b9c
    // 0x803ed8: r0 = Null
    //     0x803ed8: mov             x0, NULL
    // 0x803edc: r0 = ReturnAsyncNotFuture()
    //     0x803edc: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x803ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803ee0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803ee4: b               #0x803b2c
    // 0x803ee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803ee8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803eec: b               #0x803bb0
    // 0x803ef0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x803ef0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x803ef4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803ef4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x803ef8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803ef8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x803efc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803efc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x803f00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803f00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x803f04, size: 0xb8
    // 0x803f04: EnterFrame
    //     0x803f04: stp             fp, lr, [SP, #-0x10]!
    //     0x803f08: mov             fp, SP
    // 0x803f0c: AllocStack(0x18)
    //     0x803f0c: sub             SP, SP, #0x18
    // 0x803f10: SetupParameters()
    //     0x803f10: ldr             x0, [fp, #0x10]
    //     0x803f14: ldur            w4, [x0, #0x17]
    //     0x803f18: add             x4, x4, HEAP, lsl #32
    //     0x803f1c: stur            x4, [fp, #-0x18]
    // 0x803f20: CheckStackOverflow
    //     0x803f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803f24: cmp             SP, x16
    //     0x803f28: b.ls            #0x803fb4
    // 0x803f2c: LoadField: r0 = r4->field_b
    //     0x803f2c: ldur            w0, [x4, #0xb]
    // 0x803f30: DecompressPointer r0
    //     0x803f30: add             x0, x0, HEAP, lsl #32
    // 0x803f34: stur            x0, [fp, #-0x10]
    // 0x803f38: LoadField: r1 = r0->field_f
    //     0x803f38: ldur            w1, [x0, #0xf]
    // 0x803f3c: DecompressPointer r1
    //     0x803f3c: add             x1, x1, HEAP, lsl #32
    // 0x803f40: LoadField: r2 = r1->field_27
    //     0x803f40: ldur            w2, [x1, #0x27]
    // 0x803f44: DecompressPointer r2
    //     0x803f44: add             x2, x2, HEAP, lsl #32
    // 0x803f48: LoadField: r1 = r4->field_13
    //     0x803f48: ldur            w1, [x4, #0x13]
    // 0x803f4c: DecompressPointer r1
    //     0x803f4c: add             x1, x1, HEAP, lsl #32
    // 0x803f50: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x803f50: ldur            w5, [x4, #0x17]
    // 0x803f54: DecompressPointer r5
    //     0x803f54: add             x5, x5, HEAP, lsl #32
    // 0x803f58: mov             x16, x1
    // 0x803f5c: mov             x1, x2
    // 0x803f60: mov             x2, x16
    // 0x803f64: mov             x3, x5
    // 0x803f68: stur            x5, [fp, #-8]
    // 0x803f6c: r0 = []=()
    //     0x803f6c: bl              #0x1699264  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x803f70: ldur            x0, [fp, #-0x18]
    // 0x803f74: LoadField: r1 = r0->field_f
    //     0x803f74: ldur            w1, [x0, #0xf]
    // 0x803f78: DecompressPointer r1
    //     0x803f78: add             x1, x1, HEAP, lsl #32
    // 0x803f7c: cbnz            w1, #0x803fa4
    // 0x803f80: ldur            x0, [fp, #-0x10]
    // 0x803f84: LoadField: r1 = r0->field_f
    //     0x803f84: ldur            w1, [x0, #0xf]
    // 0x803f88: DecompressPointer r1
    //     0x803f88: add             x1, x1, HEAP, lsl #32
    // 0x803f8c: LoadField: r0 = r1->field_2f
    //     0x803f8c: ldur            w0, [x1, #0x2f]
    // 0x803f90: DecompressPointer r0
    //     0x803f90: add             x0, x0, HEAP, lsl #32
    // 0x803f94: cmp             w0, NULL
    // 0x803f98: b.ne            #0x803fa4
    // 0x803f9c: ldur            x2, [fp, #-8]
    // 0x803fa0: r0 = _calculateCarouselHeight()
    //     0x803fa0: bl              #0x801e60  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_calculateCarouselHeight
    // 0x803fa4: r0 = Null
    //     0x803fa4: mov             x0, NULL
    // 0x803fa8: LeaveFrame
    //     0x803fa8: mov             SP, fp
    //     0x803fac: ldp             fp, lr, [SP], #0x10
    // 0x803fb0: ret
    //     0x803fb0: ret             
    // 0x803fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803fb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803fb8: b               #0x803f2c
  }
  _ _initializeMediaItems(/* No info */) {
    // ** addr: 0x803fbc, size: 0xd4
    // 0x803fbc: EnterFrame
    //     0x803fbc: stp             fp, lr, [SP, #-0x10]!
    //     0x803fc0: mov             fp, SP
    // 0x803fc4: AllocStack(0x8)
    //     0x803fc4: sub             SP, SP, #8
    // 0x803fc8: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x803fc8: stur            x1, [fp, #-8]
    // 0x803fcc: CheckStackOverflow
    //     0x803fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803fd0: cmp             SP, x16
    //     0x803fd4: b.ls            #0x804084
    // 0x803fd8: r1 = 1
    //     0x803fd8: movz            x1, #0x1
    // 0x803fdc: r0 = AllocateContext()
    //     0x803fdc: bl              #0x16f6108  ; AllocateContextStub
    // 0x803fe0: mov             x1, x0
    // 0x803fe4: ldur            x0, [fp, #-8]
    // 0x803fe8: StoreField: r1->field_f = r0
    //     0x803fe8: stur            w0, [x1, #0xf]
    // 0x803fec: LoadField: r2 = r0->field_b
    //     0x803fec: ldur            w2, [x0, #0xb]
    // 0x803ff0: DecompressPointer r2
    //     0x803ff0: add             x2, x2, HEAP, lsl #32
    // 0x803ff4: cmp             w2, NULL
    // 0x803ff8: b.eq            #0x80408c
    // 0x803ffc: LoadField: r3 = r2->field_b
    //     0x803ffc: ldur            w3, [x2, #0xb]
    // 0x804000: DecompressPointer r3
    //     0x804000: add             x3, x3, HEAP, lsl #32
    // 0x804004: cmp             w3, NULL
    // 0x804008: b.eq            #0x804014
    // 0x80400c: LoadField: r2 = r3->field_b
    //     0x80400c: ldur            w2, [x3, #0xb]
    // 0x804010: cbnz            w2, #0x804054
    // 0x804014: r1 = <WidgetEntity>
    //     0x804014: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0x804018: ldr             x1, [x1, #0x878]
    // 0x80401c: r2 = 0
    //     0x80401c: movz            x2, #0
    // 0x804020: r0 = _GrowableList()
    //     0x804020: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x804024: ldur            x3, [fp, #-8]
    // 0x804028: StoreField: r3->field_23 = r0
    //     0x804028: stur            w0, [x3, #0x23]
    //     0x80402c: ldurb           w16, [x3, #-1]
    //     0x804030: ldurb           w17, [x0, #-1]
    //     0x804034: and             x16, x17, x16, lsr #2
    //     0x804038: tst             x16, HEAP, lsr #32
    //     0x80403c: b.eq            #0x804044
    //     0x804040: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x804044: r0 = Null
    //     0x804044: mov             x0, NULL
    // 0x804048: LeaveFrame
    //     0x804048: mov             SP, fp
    //     0x80404c: ldp             fp, lr, [SP], #0x10
    // 0x804050: ret
    //     0x804050: ret             
    // 0x804054: mov             x3, x0
    // 0x804058: mov             x2, x1
    // 0x80405c: r1 = Function '<anonymous closure>':.
    //     0x80405c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d18] AnonymousClosure: (0x804090), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems (0x803fbc)
    //     0x804060: ldr             x1, [x1, #0xd18]
    // 0x804064: r0 = AllocateClosure()
    //     0x804064: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x804068: ldur            x1, [fp, #-8]
    // 0x80406c: mov             x2, x0
    // 0x804070: r0 = setState()
    //     0x804070: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x804074: r0 = Null
    //     0x804074: mov             x0, NULL
    // 0x804078: LeaveFrame
    //     0x804078: mov             SP, fp
    //     0x80407c: ldp             fp, lr, [SP], #0x10
    // 0x804080: ret
    //     0x804080: ret             
    // 0x804084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804084: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804088: b               #0x803fd8
    // 0x80408c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80408c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x804090, size: 0x348
    // 0x804090: EnterFrame
    //     0x804090: stp             fp, lr, [SP, #-0x10]!
    //     0x804094: mov             fp, SP
    // 0x804098: AllocStack(0x50)
    //     0x804098: sub             SP, SP, #0x50
    // 0x80409c: SetupParameters()
    //     0x80409c: ldr             x0, [fp, #0x10]
    //     0x8040a0: ldur            w3, [x0, #0x17]
    //     0x8040a4: add             x3, x3, HEAP, lsl #32
    //     0x8040a8: stur            x3, [fp, #-0x10]
    // 0x8040ac: CheckStackOverflow
    //     0x8040ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8040b0: cmp             SP, x16
    //     0x8040b4: b.ls            #0x8043b8
    // 0x8040b8: LoadField: r0 = r3->field_f
    //     0x8040b8: ldur            w0, [x3, #0xf]
    // 0x8040bc: DecompressPointer r0
    //     0x8040bc: add             x0, x0, HEAP, lsl #32
    // 0x8040c0: stur            x0, [fp, #-8]
    // 0x8040c4: LoadField: r1 = r0->field_b
    //     0x8040c4: ldur            w1, [x0, #0xb]
    // 0x8040c8: DecompressPointer r1
    //     0x8040c8: add             x1, x1, HEAP, lsl #32
    // 0x8040cc: cmp             w1, NULL
    // 0x8040d0: b.eq            #0x8043c0
    // 0x8040d4: LoadField: r2 = r1->field_b
    //     0x8040d4: ldur            w2, [x1, #0xb]
    // 0x8040d8: DecompressPointer r2
    //     0x8040d8: add             x2, x2, HEAP, lsl #32
    // 0x8040dc: cmp             w2, NULL
    // 0x8040e0: b.eq            #0x8043c4
    // 0x8040e4: r1 = <WidgetEntity>
    //     0x8040e4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0x8040e8: ldr             x1, [x1, #0x878]
    // 0x8040ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8040ec: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8040f0: r0 = List.from()
    //     0x8040f0: bl              #0x6cdc40  ; [dart:core] List::List.from
    // 0x8040f4: ldur            x1, [fp, #-8]
    // 0x8040f8: StoreField: r1->field_23 = r0
    //     0x8040f8: stur            w0, [x1, #0x23]
    //     0x8040fc: ldurb           w16, [x1, #-1]
    //     0x804100: ldurb           w17, [x0, #-1]
    //     0x804104: and             x16, x17, x16, lsr #2
    //     0x804108: tst             x16, HEAP, lsr #32
    //     0x80410c: b.eq            #0x804114
    //     0x804110: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x804114: ldur            x0, [fp, #-0x10]
    // 0x804118: LoadField: r1 = r0->field_f
    //     0x804118: ldur            w1, [x0, #0xf]
    // 0x80411c: DecompressPointer r1
    //     0x80411c: add             x1, x1, HEAP, lsl #32
    // 0x804120: StoreField: r1->field_1b = rZR
    //     0x804120: stur            xzr, [x1, #0x1b]
    // 0x804124: StoreField: r1->field_2f = rNULL
    //     0x804124: stur            NULL, [x1, #0x2f]
    // 0x804128: LoadField: r3 = r1->field_23
    //     0x804128: ldur            w3, [x1, #0x23]
    // 0x80412c: DecompressPointer r3
    //     0x80412c: add             x3, x3, HEAP, lsl #32
    // 0x804130: stur            x3, [fp, #-8]
    // 0x804134: r1 = Function '<anonymous closure>':.
    //     0x804134: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d20] AnonymousClosure: (0x802880), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems (0x802c20)
    //     0x804138: ldr             x1, [x1, #0xd20]
    // 0x80413c: r2 = Null
    //     0x80413c: mov             x2, NULL
    // 0x804140: r0 = AllocateClosure()
    //     0x804140: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x804144: ldur            x1, [fp, #-8]
    // 0x804148: mov             x2, x0
    // 0x80414c: r0 = where()
    //     0x80414c: bl              #0x7d9800  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x804150: mov             x1, x0
    // 0x804154: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x804154: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x804158: r0 = toList()
    //     0x804158: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x80415c: mov             x3, x0
    // 0x804160: stur            x3, [fp, #-0x38]
    // 0x804164: LoadField: r4 = r3->field_7
    //     0x804164: ldur            w4, [x3, #7]
    // 0x804168: DecompressPointer r4
    //     0x804168: add             x4, x4, HEAP, lsl #32
    // 0x80416c: stur            x4, [fp, #-0x30]
    // 0x804170: LoadField: r0 = r3->field_b
    //     0x804170: ldur            w0, [x3, #0xb]
    // 0x804174: r5 = LoadInt32Instr(r0)
    //     0x804174: sbfx            x5, x0, #1, #0x1f
    // 0x804178: stur            x5, [fp, #-0x28]
    // 0x80417c: r7 = 1
    //     0x80417c: movz            x7, #0x1
    // 0x804180: r0 = 0
    //     0x804180: movz            x0, #0
    // 0x804184: ldur            x6, [fp, #-0x10]
    // 0x804188: stur            x7, [fp, #-0x20]
    // 0x80418c: CheckStackOverflow
    //     0x80418c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804190: cmp             SP, x16
    //     0x804194: b.ls            #0x8043c8
    // 0x804198: LoadField: r1 = r3->field_b
    //     0x804198: ldur            w1, [x3, #0xb]
    // 0x80419c: r2 = LoadInt32Instr(r1)
    //     0x80419c: sbfx            x2, x1, #1, #0x1f
    // 0x8041a0: cmp             x5, x2
    // 0x8041a4: b.ne            #0x804398
    // 0x8041a8: cmp             x0, x2
    // 0x8041ac: b.ge            #0x804388
    // 0x8041b0: LoadField: r1 = r3->field_f
    //     0x8041b0: ldur            w1, [x3, #0xf]
    // 0x8041b4: DecompressPointer r1
    //     0x8041b4: add             x1, x1, HEAP, lsl #32
    // 0x8041b8: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x8041b8: add             x16, x1, x0, lsl #2
    //     0x8041bc: ldur            w8, [x16, #0xf]
    // 0x8041c0: DecompressPointer r8
    //     0x8041c0: add             x8, x8, HEAP, lsl #32
    // 0x8041c4: stur            x8, [fp, #-8]
    // 0x8041c8: add             x9, x0, #1
    // 0x8041cc: stur            x9, [fp, #-0x18]
    // 0x8041d0: cmp             w8, NULL
    // 0x8041d4: b.ne            #0x804208
    // 0x8041d8: mov             x0, x8
    // 0x8041dc: mov             x2, x4
    // 0x8041e0: r1 = Null
    //     0x8041e0: mov             x1, NULL
    // 0x8041e4: cmp             w2, NULL
    // 0x8041e8: b.eq            #0x804208
    // 0x8041ec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8041ec: ldur            w4, [x2, #0x17]
    // 0x8041f0: DecompressPointer r4
    //     0x8041f0: add             x4, x4, HEAP, lsl #32
    // 0x8041f4: r8 = X0
    //     0x8041f4: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8041f8: LoadField: r9 = r4->field_7
    //     0x8041f8: ldur            x9, [x4, #7]
    // 0x8041fc: r3 = Null
    //     0x8041fc: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d28] Null
    //     0x804200: ldr             x3, [x3, #0xd28]
    // 0x804204: blr             x9
    // 0x804208: ldur            x0, [fp, #-0x10]
    // 0x80420c: LoadField: r1 = r0->field_f
    //     0x80420c: ldur            w1, [x0, #0xf]
    // 0x804210: DecompressPointer r1
    //     0x804210: add             x1, x1, HEAP, lsl #32
    // 0x804214: LoadField: r2 = r1->field_23
    //     0x804214: ldur            w2, [x1, #0x23]
    // 0x804218: DecompressPointer r2
    //     0x804218: add             x2, x2, HEAP, lsl #32
    // 0x80421c: LoadField: r1 = r2->field_b
    //     0x80421c: ldur            w1, [x2, #0xb]
    // 0x804220: r3 = LoadInt32Instr(r1)
    //     0x804220: sbfx            x3, x1, #1, #0x1f
    // 0x804224: LoadField: r1 = r2->field_f
    //     0x804224: ldur            w1, [x2, #0xf]
    // 0x804228: DecompressPointer r1
    //     0x804228: add             x1, x1, HEAP, lsl #32
    // 0x80422c: ldur            x4, [fp, #-8]
    // 0x804230: r5 = 0
    //     0x804230: movz            x5, #0
    // 0x804234: CheckStackOverflow
    //     0x804234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804238: cmp             SP, x16
    //     0x80423c: b.ls            #0x8043d0
    // 0x804240: cmp             x5, x3
    // 0x804244: b.ge            #0x804274
    // 0x804248: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x804248: add             x16, x1, x5, lsl #2
    //     0x80424c: ldur            w6, [x16, #0xf]
    // 0x804250: DecompressPointer r6
    //     0x804250: add             x6, x6, HEAP, lsl #32
    // 0x804254: cmp             w6, w4
    // 0x804258: b.eq            #0x804268
    // 0x80425c: add             x6, x5, #1
    // 0x804260: mov             x5, x6
    // 0x804264: b               #0x804234
    // 0x804268: mov             x1, x2
    // 0x80426c: mov             x2, x5
    // 0x804270: r0 = removeAt()
    //     0x804270: bl              #0x7145c0  ; [dart:core] _GrowableList::removeAt
    // 0x804274: ldur            x0, [fp, #-0x10]
    // 0x804278: ldur            x4, [fp, #-0x20]
    // 0x80427c: LoadField: r1 = r0->field_f
    //     0x80427c: ldur            w1, [x0, #0xf]
    // 0x804280: DecompressPointer r1
    //     0x804280: add             x1, x1, HEAP, lsl #32
    // 0x804284: LoadField: r3 = r1->field_23
    //     0x804284: ldur            w3, [x1, #0x23]
    // 0x804288: DecompressPointer r3
    //     0x804288: add             x3, x3, HEAP, lsl #32
    // 0x80428c: stur            x3, [fp, #-0x48]
    // 0x804290: LoadField: r5 = r3->field_b
    //     0x804290: ldur            w5, [x3, #0xb]
    // 0x804294: stur            x5, [fp, #-0x40]
    // 0x804298: r1 = LoadInt32Instr(r5)
    //     0x804298: sbfx            x1, x5, #1, #0x1f
    // 0x80429c: cmp             x4, x1
    // 0x8042a0: b.ge            #0x8042c4
    // 0x8042a4: mov             x1, x3
    // 0x8042a8: mov             x2, x4
    // 0x8042ac: ldur            x3, [fp, #-8]
    // 0x8042b0: r0 = insert()
    //     0x8042b0: bl              #0x697f94  ; [dart:core] _GrowableList::insert
    // 0x8042b4: ldur            x4, [fp, #-0x20]
    // 0x8042b8: add             x1, x4, #2
    // 0x8042bc: mov             x7, x1
    // 0x8042c0: b               #0x804374
    // 0x8042c4: LoadField: r2 = r3->field_7
    //     0x8042c4: ldur            w2, [x3, #7]
    // 0x8042c8: DecompressPointer r2
    //     0x8042c8: add             x2, x2, HEAP, lsl #32
    // 0x8042cc: ldur            x0, [fp, #-8]
    // 0x8042d0: r1 = Null
    //     0x8042d0: mov             x1, NULL
    // 0x8042d4: cmp             w2, NULL
    // 0x8042d8: b.eq            #0x8042f8
    // 0x8042dc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8042dc: ldur            w4, [x2, #0x17]
    // 0x8042e0: DecompressPointer r4
    //     0x8042e0: add             x4, x4, HEAP, lsl #32
    // 0x8042e4: r8 = X0
    //     0x8042e4: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8042e8: LoadField: r9 = r4->field_7
    //     0x8042e8: ldur            x9, [x4, #7]
    // 0x8042ec: r3 = Null
    //     0x8042ec: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d38] Null
    //     0x8042f0: ldr             x3, [x3, #0xd38]
    // 0x8042f4: blr             x9
    // 0x8042f8: ldur            x0, [fp, #-0x48]
    // 0x8042fc: LoadField: r1 = r0->field_f
    //     0x8042fc: ldur            w1, [x0, #0xf]
    // 0x804300: DecompressPointer r1
    //     0x804300: add             x1, x1, HEAP, lsl #32
    // 0x804304: LoadField: r2 = r1->field_b
    //     0x804304: ldur            w2, [x1, #0xb]
    // 0x804308: ldur            x1, [fp, #-0x40]
    // 0x80430c: r3 = LoadInt32Instr(r1)
    //     0x80430c: sbfx            x3, x1, #1, #0x1f
    // 0x804310: stur            x3, [fp, #-0x50]
    // 0x804314: r1 = LoadInt32Instr(r2)
    //     0x804314: sbfx            x1, x2, #1, #0x1f
    // 0x804318: cmp             x3, x1
    // 0x80431c: b.ne            #0x804328
    // 0x804320: mov             x1, x0
    // 0x804324: r0 = _growToNextCapacity()
    //     0x804324: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x804328: ldur            x0, [fp, #-0x48]
    // 0x80432c: ldur            x2, [fp, #-0x50]
    // 0x804330: add             x1, x2, #1
    // 0x804334: lsl             x3, x1, #1
    // 0x804338: StoreField: r0->field_b = r3
    //     0x804338: stur            w3, [x0, #0xb]
    // 0x80433c: LoadField: r1 = r0->field_f
    //     0x80433c: ldur            w1, [x0, #0xf]
    // 0x804340: DecompressPointer r1
    //     0x804340: add             x1, x1, HEAP, lsl #32
    // 0x804344: ldur            x0, [fp, #-8]
    // 0x804348: ArrayStore: r1[r2] = r0  ; List_4
    //     0x804348: add             x25, x1, x2, lsl #2
    //     0x80434c: add             x25, x25, #0xf
    //     0x804350: str             w0, [x25]
    //     0x804354: tbz             w0, #0, #0x804370
    //     0x804358: ldurb           w16, [x1, #-1]
    //     0x80435c: ldurb           w17, [x0, #-1]
    //     0x804360: and             x16, x17, x16, lsr #2
    //     0x804364: tst             x16, HEAP, lsr #32
    //     0x804368: b.eq            #0x804370
    //     0x80436c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x804370: ldur            x7, [fp, #-0x20]
    // 0x804374: ldur            x0, [fp, #-0x18]
    // 0x804378: ldur            x3, [fp, #-0x38]
    // 0x80437c: ldur            x4, [fp, #-0x30]
    // 0x804380: ldur            x5, [fp, #-0x28]
    // 0x804384: b               #0x804184
    // 0x804388: r0 = Null
    //     0x804388: mov             x0, NULL
    // 0x80438c: LeaveFrame
    //     0x80438c: mov             SP, fp
    //     0x804390: ldp             fp, lr, [SP], #0x10
    // 0x804394: ret
    //     0x804394: ret             
    // 0x804398: mov             x0, x3
    // 0x80439c: r0 = ConcurrentModificationError()
    //     0x80439c: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8043a0: mov             x1, x0
    // 0x8043a4: ldur            x0, [fp, #-0x38]
    // 0x8043a8: StoreField: r1->field_b = r0
    //     0x8043a8: stur            w0, [x1, #0xb]
    // 0x8043ac: mov             x0, x1
    // 0x8043b0: r0 = Throw()
    //     0x8043b0: bl              #0x16f5420  ; ThrowStub
    // 0x8043b4: brk             #0
    // 0x8043b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8043b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8043bc: b               #0x8040b8
    // 0x8043c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8043c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8043c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8043c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8043c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8043c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8043cc: b               #0x804198
    // 0x8043d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8043d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8043d4: b               #0x804240
  }
  _ initState(/* No info */) {
    // ** addr: 0x93c648, size: 0x50
    // 0x93c648: EnterFrame
    //     0x93c648: stp             fp, lr, [SP, #-0x10]!
    //     0x93c64c: mov             fp, SP
    // 0x93c650: AllocStack(0x8)
    //     0x93c650: sub             SP, SP, #8
    // 0x93c654: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0x93c654: mov             x0, x1
    //     0x93c658: stur            x1, [fp, #-8]
    // 0x93c65c: CheckStackOverflow
    //     0x93c65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93c660: cmp             SP, x16
    //     0x93c664: b.ls            #0x93c690
    // 0x93c668: mov             x1, x0
    // 0x93c66c: r0 = initState()
    //     0x93c66c: bl              #0x93c698  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin::initState
    // 0x93c670: ldur            x1, [fp, #-8]
    // 0x93c674: r0 = _initializeMediaItems()
    //     0x93c674: bl              #0x803fbc  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems
    // 0x93c678: ldur            x1, [fp, #-8]
    // 0x93c67c: r0 = _preloadImageSizes()
    //     0x93c67c: bl              #0x803b0c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0x93c680: r0 = Null
    //     0x93c680: mov             x0, NULL
    // 0x93c684: LeaveFrame
    //     0x93c684: mov             SP, fp
    //     0x93c688: ldp             fp, lr, [SP], #0x10
    // 0x93c68c: ret
    //     0x93c68c: ret             
    // 0x93c690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93c690: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93c694: b               #0x93c668
  }
  _ build(/* No info */) {
    // ** addr: 0xb0c0dc, size: 0x164
    // 0xb0c0dc: EnterFrame
    //     0xb0c0dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb0c0e0: mov             fp, SP
    // 0xb0c0e4: AllocStack(0x30)
    //     0xb0c0e4: sub             SP, SP, #0x30
    // 0xb0c0e8: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb0c0e8: mov             x3, x1
    //     0xb0c0ec: mov             x0, x2
    //     0xb0c0f0: stur            x1, [fp, #-8]
    //     0xb0c0f4: stur            x2, [fp, #-0x10]
    // 0xb0c0f8: CheckStackOverflow
    //     0xb0c0f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0c0fc: cmp             SP, x16
    //     0xb0c100: b.ls            #0xb0c238
    // 0xb0c104: mov             x1, x3
    // 0xb0c108: mov             x2, x0
    // 0xb0c10c: r0 = build()
    //     0xb0c10c: bl              #0xb0e058  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin::build
    // 0xb0c110: ldur            x0, [fp, #-8]
    // 0xb0c114: LoadField: r1 = r0->field_23
    //     0xb0c114: ldur            w1, [x0, #0x23]
    // 0xb0c118: DecompressPointer r1
    //     0xb0c118: add             x1, x1, HEAP, lsl #32
    // 0xb0c11c: LoadField: r2 = r1->field_b
    //     0xb0c11c: ldur            w2, [x1, #0xb]
    // 0xb0c120: cbnz            w2, #0xb0c134
    // 0xb0c124: r0 = Instance_SizedBox
    //     0xb0c124: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0c128: LeaveFrame
    //     0xb0c128: mov             SP, fp
    //     0xb0c12c: ldp             fp, lr, [SP], #0x10
    // 0xb0c130: ret
    //     0xb0c130: ret             
    // 0xb0c134: mov             x1, x0
    // 0xb0c138: ldur            x2, [fp, #-0x10]
    // 0xb0c13c: r0 = _buildCarouselSection()
    //     0xb0c13c: bl              #0xb0c398  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCarouselSection
    // 0xb0c140: ldur            x1, [fp, #-8]
    // 0xb0c144: ldur            x2, [fp, #-0x10]
    // 0xb0c148: stur            x0, [fp, #-8]
    // 0xb0c14c: r0 = _buildIndicator()
    //     0xb0c14c: bl              #0xb0c240  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildIndicator
    // 0xb0c150: r1 = Null
    //     0xb0c150: mov             x1, NULL
    // 0xb0c154: r2 = 4
    //     0xb0c154: movz            x2, #0x4
    // 0xb0c158: stur            x0, [fp, #-0x10]
    // 0xb0c15c: r0 = AllocateArray()
    //     0xb0c15c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0c160: mov             x2, x0
    // 0xb0c164: ldur            x0, [fp, #-8]
    // 0xb0c168: stur            x2, [fp, #-0x18]
    // 0xb0c16c: StoreField: r2->field_f = r0
    //     0xb0c16c: stur            w0, [x2, #0xf]
    // 0xb0c170: ldur            x0, [fp, #-0x10]
    // 0xb0c174: StoreField: r2->field_13 = r0
    //     0xb0c174: stur            w0, [x2, #0x13]
    // 0xb0c178: r1 = <Widget>
    //     0xb0c178: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0c17c: r0 = AllocateGrowableArray()
    //     0xb0c17c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0c180: mov             x1, x0
    // 0xb0c184: ldur            x0, [fp, #-0x18]
    // 0xb0c188: stur            x1, [fp, #-8]
    // 0xb0c18c: StoreField: r1->field_f = r0
    //     0xb0c18c: stur            w0, [x1, #0xf]
    // 0xb0c190: r0 = 4
    //     0xb0c190: movz            x0, #0x4
    // 0xb0c194: StoreField: r1->field_b = r0
    //     0xb0c194: stur            w0, [x1, #0xb]
    // 0xb0c198: r0 = Column()
    //     0xb0c198: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb0c19c: mov             x1, x0
    // 0xb0c1a0: r0 = Instance_Axis
    //     0xb0c1a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb0c1a4: stur            x1, [fp, #-0x10]
    // 0xb0c1a8: StoreField: r1->field_f = r0
    //     0xb0c1a8: stur            w0, [x1, #0xf]
    // 0xb0c1ac: r0 = Instance_MainAxisAlignment
    //     0xb0c1ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0c1b0: ldr             x0, [x0, #0xa08]
    // 0xb0c1b4: StoreField: r1->field_13 = r0
    //     0xb0c1b4: stur            w0, [x1, #0x13]
    // 0xb0c1b8: r0 = Instance_MainAxisSize
    //     0xb0c1b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0c1bc: ldr             x0, [x0, #0xa10]
    // 0xb0c1c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0c1c0: stur            w0, [x1, #0x17]
    // 0xb0c1c4: r0 = Instance_CrossAxisAlignment
    //     0xb0c1c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0c1c8: ldr             x0, [x0, #0xa18]
    // 0xb0c1cc: StoreField: r1->field_1b = r0
    //     0xb0c1cc: stur            w0, [x1, #0x1b]
    // 0xb0c1d0: r0 = Instance_VerticalDirection
    //     0xb0c1d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0c1d4: ldr             x0, [x0, #0xa20]
    // 0xb0c1d8: StoreField: r1->field_23 = r0
    //     0xb0c1d8: stur            w0, [x1, #0x23]
    // 0xb0c1dc: r0 = Instance_Clip
    //     0xb0c1dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0c1e0: ldr             x0, [x0, #0x38]
    // 0xb0c1e4: StoreField: r1->field_2b = r0
    //     0xb0c1e4: stur            w0, [x1, #0x2b]
    // 0xb0c1e8: StoreField: r1->field_2f = rZR
    //     0xb0c1e8: stur            xzr, [x1, #0x2f]
    // 0xb0c1ec: ldur            x0, [fp, #-8]
    // 0xb0c1f0: StoreField: r1->field_b = r0
    //     0xb0c1f0: stur            w0, [x1, #0xb]
    // 0xb0c1f4: r0 = Container()
    //     0xb0c1f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0c1f8: stur            x0, [fp, #-8]
    // 0xb0c1fc: r16 = Instance_Color
    //     0xb0c1fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb0c200: ldr             x16, [x16, #0x90]
    // 0xb0c204: r30 = Instance_EdgeInsets
    //     0xb0c204: add             lr, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb0c208: ldr             lr, [lr, #0x240]
    // 0xb0c20c: stp             lr, x16, [SP, #8]
    // 0xb0c210: ldur            x16, [fp, #-0x10]
    // 0xb0c214: str             x16, [SP]
    // 0xb0c218: mov             x1, x0
    // 0xb0c21c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, padding, 0x2, null]
    //     0xb0c21c: add             x4, PP, #0x45, lsl #12  ; [pp+0x45c40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "padding", 0x2, Null]
    //     0xb0c220: ldr             x4, [x4, #0xc40]
    // 0xb0c224: r0 = Container()
    //     0xb0c224: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0c228: ldur            x0, [fp, #-8]
    // 0xb0c22c: LeaveFrame
    //     0xb0c22c: mov             SP, fp
    //     0xb0c230: ldp             fp, lr, [SP], #0x10
    // 0xb0c234: ret
    //     0xb0c234: ret             
    // 0xb0c238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c23c: b               #0xb0c104
  }
  _ _buildIndicator(/* No info */) {
    // ** addr: 0xb0c240, size: 0x158
    // 0xb0c240: EnterFrame
    //     0xb0c240: stp             fp, lr, [SP, #-0x10]!
    //     0xb0c244: mov             fp, SP
    // 0xb0c248: AllocStack(0x20)
    //     0xb0c248: sub             SP, SP, #0x20
    // 0xb0c24c: SetupParameters(_ProductMediaCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xb0c24c: mov             x0, x1
    //     0xb0c250: mov             x1, x2
    // 0xb0c254: CheckStackOverflow
    //     0xb0c254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0c258: cmp             SP, x16
    //     0xb0c25c: b.ls            #0xb0c390
    // 0xb0c260: LoadField: r2 = r0->field_23
    //     0xb0c260: ldur            w2, [x0, #0x23]
    // 0xb0c264: DecompressPointer r2
    //     0xb0c264: add             x2, x2, HEAP, lsl #32
    // 0xb0c268: LoadField: r3 = r2->field_b
    //     0xb0c268: ldur            w3, [x2, #0xb]
    // 0xb0c26c: r2 = LoadInt32Instr(r3)
    //     0xb0c26c: sbfx            x2, x3, #1, #0x1f
    // 0xb0c270: stur            x2, [fp, #-0x10]
    // 0xb0c274: cmp             x2, #1
    // 0xb0c278: b.gt            #0xb0c28c
    // 0xb0c27c: r0 = Instance_SizedBox
    //     0xb0c27c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0c280: LeaveFrame
    //     0xb0c280: mov             SP, fp
    //     0xb0c284: ldp             fp, lr, [SP], #0x10
    // 0xb0c288: ret
    //     0xb0c288: ret             
    // 0xb0c28c: LoadField: r3 = r0->field_1b
    //     0xb0c28c: ldur            x3, [x0, #0x1b]
    // 0xb0c290: stur            x3, [fp, #-8]
    // 0xb0c294: r0 = of()
    //     0xb0c294: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0c298: LoadField: r1 = r0->field_5b
    //     0xb0c298: ldur            w1, [x0, #0x5b]
    // 0xb0c29c: DecompressPointer r1
    //     0xb0c29c: add             x1, x1, HEAP, lsl #32
    // 0xb0c2a0: stur            x1, [fp, #-0x18]
    // 0xb0c2a4: r0 = CarouselIndicator()
    //     0xb0c2a4: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb0c2a8: mov             x3, x0
    // 0xb0c2ac: ldur            x0, [fp, #-0x10]
    // 0xb0c2b0: stur            x3, [fp, #-0x20]
    // 0xb0c2b4: StoreField: r3->field_b = r0
    //     0xb0c2b4: stur            x0, [x3, #0xb]
    // 0xb0c2b8: ldur            x0, [fp, #-8]
    // 0xb0c2bc: StoreField: r3->field_13 = r0
    //     0xb0c2bc: stur            x0, [x3, #0x13]
    // 0xb0c2c0: ldur            x0, [fp, #-0x18]
    // 0xb0c2c4: StoreField: r3->field_1b = r0
    //     0xb0c2c4: stur            w0, [x3, #0x1b]
    // 0xb0c2c8: r0 = Instance_Color
    //     0xb0c2c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb0c2cc: ldr             x0, [x0, #0x90]
    // 0xb0c2d0: StoreField: r3->field_1f = r0
    //     0xb0c2d0: stur            w0, [x3, #0x1f]
    // 0xb0c2d4: r1 = Null
    //     0xb0c2d4: mov             x1, NULL
    // 0xb0c2d8: r2 = 2
    //     0xb0c2d8: movz            x2, #0x2
    // 0xb0c2dc: r0 = AllocateArray()
    //     0xb0c2dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0c2e0: mov             x2, x0
    // 0xb0c2e4: ldur            x0, [fp, #-0x20]
    // 0xb0c2e8: stur            x2, [fp, #-0x18]
    // 0xb0c2ec: StoreField: r2->field_f = r0
    //     0xb0c2ec: stur            w0, [x2, #0xf]
    // 0xb0c2f0: r1 = <Widget>
    //     0xb0c2f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0c2f4: r0 = AllocateGrowableArray()
    //     0xb0c2f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0c2f8: mov             x1, x0
    // 0xb0c2fc: ldur            x0, [fp, #-0x18]
    // 0xb0c300: stur            x1, [fp, #-0x20]
    // 0xb0c304: StoreField: r1->field_f = r0
    //     0xb0c304: stur            w0, [x1, #0xf]
    // 0xb0c308: r0 = 2
    //     0xb0c308: movz            x0, #0x2
    // 0xb0c30c: StoreField: r1->field_b = r0
    //     0xb0c30c: stur            w0, [x1, #0xb]
    // 0xb0c310: r0 = Row()
    //     0xb0c310: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0c314: mov             x1, x0
    // 0xb0c318: r0 = Instance_Axis
    //     0xb0c318: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0c31c: stur            x1, [fp, #-0x18]
    // 0xb0c320: StoreField: r1->field_f = r0
    //     0xb0c320: stur            w0, [x1, #0xf]
    // 0xb0c324: r0 = Instance_MainAxisAlignment
    //     0xb0c324: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb0c328: ldr             x0, [x0, #0xab0]
    // 0xb0c32c: StoreField: r1->field_13 = r0
    //     0xb0c32c: stur            w0, [x1, #0x13]
    // 0xb0c330: r0 = Instance_MainAxisSize
    //     0xb0c330: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0c334: ldr             x0, [x0, #0xa10]
    // 0xb0c338: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0c338: stur            w0, [x1, #0x17]
    // 0xb0c33c: r0 = Instance_CrossAxisAlignment
    //     0xb0c33c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0c340: ldr             x0, [x0, #0xa18]
    // 0xb0c344: StoreField: r1->field_1b = r0
    //     0xb0c344: stur            w0, [x1, #0x1b]
    // 0xb0c348: r0 = Instance_VerticalDirection
    //     0xb0c348: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0c34c: ldr             x0, [x0, #0xa20]
    // 0xb0c350: StoreField: r1->field_23 = r0
    //     0xb0c350: stur            w0, [x1, #0x23]
    // 0xb0c354: r0 = Instance_Clip
    //     0xb0c354: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0c358: ldr             x0, [x0, #0x38]
    // 0xb0c35c: StoreField: r1->field_2b = r0
    //     0xb0c35c: stur            w0, [x1, #0x2b]
    // 0xb0c360: StoreField: r1->field_2f = rZR
    //     0xb0c360: stur            xzr, [x1, #0x2f]
    // 0xb0c364: ldur            x0, [fp, #-0x20]
    // 0xb0c368: StoreField: r1->field_b = r0
    //     0xb0c368: stur            w0, [x1, #0xb]
    // 0xb0c36c: r0 = Padding()
    //     0xb0c36c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0c370: r1 = Instance_EdgeInsets
    //     0xb0c370: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb0c374: ldr             x1, [x1, #0xa00]
    // 0xb0c378: StoreField: r0->field_f = r1
    //     0xb0c378: stur            w1, [x0, #0xf]
    // 0xb0c37c: ldur            x1, [fp, #-0x18]
    // 0xb0c380: StoreField: r0->field_b = r1
    //     0xb0c380: stur            w1, [x0, #0xb]
    // 0xb0c384: LeaveFrame
    //     0xb0c384: mov             SP, fp
    //     0xb0c388: ldp             fp, lr, [SP], #0x10
    // 0xb0c38c: ret
    //     0xb0c38c: ret             
    // 0xb0c390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c390: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c394: b               #0xb0c260
  }
  _ _buildCarouselSection(/* No info */) {
    // ** addr: 0xb0c398, size: 0x16c
    // 0xb0c398: EnterFrame
    //     0xb0c398: stp             fp, lr, [SP, #-0x10]!
    //     0xb0c39c: mov             fp, SP
    // 0xb0c3a0: AllocStack(0x28)
    //     0xb0c3a0: sub             SP, SP, #0x28
    // 0xb0c3a4: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb0c3a4: mov             x3, x1
    //     0xb0c3a8: mov             x0, x2
    //     0xb0c3ac: stur            x1, [fp, #-8]
    //     0xb0c3b0: stur            x2, [fp, #-0x10]
    // 0xb0c3b4: CheckStackOverflow
    //     0xb0c3b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0c3b8: cmp             SP, x16
    //     0xb0c3bc: b.ls            #0xb0c4f8
    // 0xb0c3c0: mov             x1, x3
    // 0xb0c3c4: mov             x2, x0
    // 0xb0c3c8: r0 = _buildPageView()
    //     0xb0c3c8: bl              #0xb0d51c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildPageView
    // 0xb0c3cc: r1 = Null
    //     0xb0c3cc: mov             x1, NULL
    // 0xb0c3d0: r2 = 2
    //     0xb0c3d0: movz            x2, #0x2
    // 0xb0c3d4: stur            x0, [fp, #-0x18]
    // 0xb0c3d8: r0 = AllocateArray()
    //     0xb0c3d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0c3dc: mov             x2, x0
    // 0xb0c3e0: ldur            x0, [fp, #-0x18]
    // 0xb0c3e4: stur            x2, [fp, #-0x20]
    // 0xb0c3e8: StoreField: r2->field_f = r0
    //     0xb0c3e8: stur            w0, [x2, #0xf]
    // 0xb0c3ec: r1 = <Widget>
    //     0xb0c3ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0c3f0: r0 = AllocateGrowableArray()
    //     0xb0c3f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0c3f4: mov             x3, x0
    // 0xb0c3f8: ldur            x0, [fp, #-0x20]
    // 0xb0c3fc: stur            x3, [fp, #-0x18]
    // 0xb0c400: StoreField: r3->field_f = r0
    //     0xb0c400: stur            w0, [x3, #0xf]
    // 0xb0c404: r0 = 2
    //     0xb0c404: movz            x0, #0x2
    // 0xb0c408: StoreField: r3->field_b = r0
    //     0xb0c408: stur            w0, [x3, #0xb]
    // 0xb0c40c: ldur            x1, [fp, #-8]
    // 0xb0c410: LoadField: r0 = r1->field_b
    //     0xb0c410: ldur            w0, [x1, #0xb]
    // 0xb0c414: DecompressPointer r0
    //     0xb0c414: add             x0, x0, HEAP, lsl #32
    // 0xb0c418: cmp             w0, NULL
    // 0xb0c41c: b.eq            #0xb0c500
    // 0xb0c420: LoadField: r2 = r0->field_13
    //     0xb0c420: ldur            w2, [x0, #0x13]
    // 0xb0c424: DecompressPointer r2
    //     0xb0c424: add             x2, x2, HEAP, lsl #32
    // 0xb0c428: tbnz            w2, #4, #0xb0c4b8
    // 0xb0c42c: ldur            x2, [fp, #-0x10]
    // 0xb0c430: r0 = _buildFreeGiftButton()
    //     0xb0c430: bl              #0xb0c504  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildFreeGiftButton
    // 0xb0c434: mov             x2, x0
    // 0xb0c438: ldur            x0, [fp, #-0x18]
    // 0xb0c43c: stur            x2, [fp, #-8]
    // 0xb0c440: LoadField: r1 = r0->field_b
    //     0xb0c440: ldur            w1, [x0, #0xb]
    // 0xb0c444: LoadField: r3 = r0->field_f
    //     0xb0c444: ldur            w3, [x0, #0xf]
    // 0xb0c448: DecompressPointer r3
    //     0xb0c448: add             x3, x3, HEAP, lsl #32
    // 0xb0c44c: LoadField: r4 = r3->field_b
    //     0xb0c44c: ldur            w4, [x3, #0xb]
    // 0xb0c450: r3 = LoadInt32Instr(r1)
    //     0xb0c450: sbfx            x3, x1, #1, #0x1f
    // 0xb0c454: stur            x3, [fp, #-0x28]
    // 0xb0c458: r1 = LoadInt32Instr(r4)
    //     0xb0c458: sbfx            x1, x4, #1, #0x1f
    // 0xb0c45c: cmp             x3, x1
    // 0xb0c460: b.ne            #0xb0c46c
    // 0xb0c464: mov             x1, x0
    // 0xb0c468: r0 = _growToNextCapacity()
    //     0xb0c468: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0c46c: ldur            x2, [fp, #-0x18]
    // 0xb0c470: ldur            x3, [fp, #-0x28]
    // 0xb0c474: add             x0, x3, #1
    // 0xb0c478: lsl             x1, x0, #1
    // 0xb0c47c: StoreField: r2->field_b = r1
    //     0xb0c47c: stur            w1, [x2, #0xb]
    // 0xb0c480: LoadField: r1 = r2->field_f
    //     0xb0c480: ldur            w1, [x2, #0xf]
    // 0xb0c484: DecompressPointer r1
    //     0xb0c484: add             x1, x1, HEAP, lsl #32
    // 0xb0c488: ldur            x0, [fp, #-8]
    // 0xb0c48c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0c48c: add             x25, x1, x3, lsl #2
    //     0xb0c490: add             x25, x25, #0xf
    //     0xb0c494: str             w0, [x25]
    //     0xb0c498: tbz             w0, #0, #0xb0c4b4
    //     0xb0c49c: ldurb           w16, [x1, #-1]
    //     0xb0c4a0: ldurb           w17, [x0, #-1]
    //     0xb0c4a4: and             x16, x17, x16, lsr #2
    //     0xb0c4a8: tst             x16, HEAP, lsr #32
    //     0xb0c4ac: b.eq            #0xb0c4b4
    //     0xb0c4b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0c4b4: b               #0xb0c4bc
    // 0xb0c4b8: mov             x2, x3
    // 0xb0c4bc: r0 = Stack()
    //     0xb0c4bc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0c4c0: r1 = Instance_AlignmentDirectional
    //     0xb0c4c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb0c4c4: ldr             x1, [x1, #0xd08]
    // 0xb0c4c8: StoreField: r0->field_f = r1
    //     0xb0c4c8: stur            w1, [x0, #0xf]
    // 0xb0c4cc: r1 = Instance_StackFit
    //     0xb0c4cc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb0c4d0: ldr             x1, [x1, #0xfa8]
    // 0xb0c4d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb0c4d4: stur            w1, [x0, #0x17]
    // 0xb0c4d8: r1 = Instance_Clip
    //     0xb0c4d8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0c4dc: ldr             x1, [x1, #0x7e0]
    // 0xb0c4e0: StoreField: r0->field_1b = r1
    //     0xb0c4e0: stur            w1, [x0, #0x1b]
    // 0xb0c4e4: ldur            x1, [fp, #-0x18]
    // 0xb0c4e8: StoreField: r0->field_b = r1
    //     0xb0c4e8: stur            w1, [x0, #0xb]
    // 0xb0c4ec: LeaveFrame
    //     0xb0c4ec: mov             SP, fp
    //     0xb0c4f0: ldp             fp, lr, [SP], #0x10
    // 0xb0c4f4: ret
    //     0xb0c4f4: ret             
    // 0xb0c4f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c4f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c4fc: b               #0xb0c3c0
    // 0xb0c500: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0c500: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildFreeGiftButton(/* No info */) {
    // ** addr: 0xb0c504, size: 0x2a4
    // 0xb0c504: EnterFrame
    //     0xb0c504: stp             fp, lr, [SP, #-0x10]!
    //     0xb0c508: mov             fp, SP
    // 0xb0c50c: AllocStack(0x48)
    //     0xb0c50c: sub             SP, SP, #0x48
    // 0xb0c510: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb0c510: mov             x0, x1
    //     0xb0c514: stur            x1, [fp, #-8]
    //     0xb0c518: mov             x1, x2
    //     0xb0c51c: stur            x2, [fp, #-0x10]
    // 0xb0c520: CheckStackOverflow
    //     0xb0c520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0c524: cmp             SP, x16
    //     0xb0c528: b.ls            #0xb0c7a0
    // 0xb0c52c: r1 = 2
    //     0xb0c52c: movz            x1, #0x2
    // 0xb0c530: r0 = AllocateContext()
    //     0xb0c530: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0c534: mov             x2, x0
    // 0xb0c538: ldur            x0, [fp, #-8]
    // 0xb0c53c: stur            x2, [fp, #-0x18]
    // 0xb0c540: StoreField: r2->field_f = r0
    //     0xb0c540: stur            w0, [x2, #0xf]
    // 0xb0c544: ldur            x1, [fp, #-0x10]
    // 0xb0c548: StoreField: r2->field_13 = r1
    //     0xb0c548: stur            w1, [x2, #0x13]
    // 0xb0c54c: r0 = of()
    //     0xb0c54c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0c550: LoadField: r1 = r0->field_87
    //     0xb0c550: ldur            w1, [x0, #0x87]
    // 0xb0c554: DecompressPointer r1
    //     0xb0c554: add             x1, x1, HEAP, lsl #32
    // 0xb0c558: LoadField: r0 = r1->field_7
    //     0xb0c558: ldur            w0, [x1, #7]
    // 0xb0c55c: DecompressPointer r0
    //     0xb0c55c: add             x0, x0, HEAP, lsl #32
    // 0xb0c560: r16 = 15.000000
    //     0xb0c560: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xb0c564: ldr             x16, [x16, #0x480]
    // 0xb0c568: r30 = Instance_Color
    //     0xb0c568: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb0c56c: ldr             lr, [lr, #0x858]
    // 0xb0c570: stp             lr, x16, [SP]
    // 0xb0c574: mov             x1, x0
    // 0xb0c578: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0c578: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0c57c: ldr             x4, [x4, #0xaa0]
    // 0xb0c580: r0 = copyWith()
    //     0xb0c580: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0c584: stur            x0, [fp, #-8]
    // 0xb0c588: r0 = Text()
    //     0xb0c588: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0c58c: mov             x1, x0
    // 0xb0c590: r0 = "Get Free Gift"
    //     0xb0c590: add             x0, PP, #0x52, lsl #12  ; [pp+0x52880] "Get Free Gift"
    //     0xb0c594: ldr             x0, [x0, #0x880]
    // 0xb0c598: stur            x1, [fp, #-0x10]
    // 0xb0c59c: StoreField: r1->field_b = r0
    //     0xb0c59c: stur            w0, [x1, #0xb]
    // 0xb0c5a0: ldur            x0, [fp, #-8]
    // 0xb0c5a4: StoreField: r1->field_13 = r0
    //     0xb0c5a4: stur            w0, [x1, #0x13]
    // 0xb0c5a8: r0 = SvgPicture()
    //     0xb0c5a8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb0c5ac: mov             x1, x0
    // 0xb0c5b0: r2 = "assets/images/gift.svg"
    //     0xb0c5b0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52888] "assets/images/gift.svg"
    //     0xb0c5b4: ldr             x2, [x2, #0x888]
    // 0xb0c5b8: stur            x0, [fp, #-8]
    // 0xb0c5bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0c5bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0c5c0: r0 = SvgPicture.asset()
    //     0xb0c5c0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0c5c4: r0 = Padding()
    //     0xb0c5c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0c5c8: mov             x3, x0
    // 0xb0c5cc: r0 = Instance_EdgeInsets
    //     0xb0c5cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xb0c5d0: ldr             x0, [x0, #0xe60]
    // 0xb0c5d4: stur            x3, [fp, #-0x20]
    // 0xb0c5d8: StoreField: r3->field_f = r0
    //     0xb0c5d8: stur            w0, [x3, #0xf]
    // 0xb0c5dc: ldur            x0, [fp, #-8]
    // 0xb0c5e0: StoreField: r3->field_b = r0
    //     0xb0c5e0: stur            w0, [x3, #0xb]
    // 0xb0c5e4: r1 = Null
    //     0xb0c5e4: mov             x1, NULL
    // 0xb0c5e8: r2 = 4
    //     0xb0c5e8: movz            x2, #0x4
    // 0xb0c5ec: r0 = AllocateArray()
    //     0xb0c5ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0c5f0: mov             x2, x0
    // 0xb0c5f4: ldur            x0, [fp, #-0x10]
    // 0xb0c5f8: stur            x2, [fp, #-8]
    // 0xb0c5fc: StoreField: r2->field_f = r0
    //     0xb0c5fc: stur            w0, [x2, #0xf]
    // 0xb0c600: ldur            x0, [fp, #-0x20]
    // 0xb0c604: StoreField: r2->field_13 = r0
    //     0xb0c604: stur            w0, [x2, #0x13]
    // 0xb0c608: r1 = <Widget>
    //     0xb0c608: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0c60c: r0 = AllocateGrowableArray()
    //     0xb0c60c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0c610: mov             x1, x0
    // 0xb0c614: ldur            x0, [fp, #-8]
    // 0xb0c618: stur            x1, [fp, #-0x10]
    // 0xb0c61c: StoreField: r1->field_f = r0
    //     0xb0c61c: stur            w0, [x1, #0xf]
    // 0xb0c620: r0 = 4
    //     0xb0c620: movz            x0, #0x4
    // 0xb0c624: StoreField: r1->field_b = r0
    //     0xb0c624: stur            w0, [x1, #0xb]
    // 0xb0c628: r0 = Row()
    //     0xb0c628: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0c62c: mov             x1, x0
    // 0xb0c630: r0 = Instance_Axis
    //     0xb0c630: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0c634: stur            x1, [fp, #-8]
    // 0xb0c638: StoreField: r1->field_f = r0
    //     0xb0c638: stur            w0, [x1, #0xf]
    // 0xb0c63c: r0 = Instance_MainAxisAlignment
    //     0xb0c63c: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4daf8] Obj!MainAxisAlignment@d734e1
    //     0xb0c640: ldr             x0, [x0, #0xaf8]
    // 0xb0c644: StoreField: r1->field_13 = r0
    //     0xb0c644: stur            w0, [x1, #0x13]
    // 0xb0c648: r0 = Instance_MainAxisSize
    //     0xb0c648: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0c64c: ldr             x0, [x0, #0xa10]
    // 0xb0c650: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0c650: stur            w0, [x1, #0x17]
    // 0xb0c654: r0 = Instance_CrossAxisAlignment
    //     0xb0c654: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0c658: ldr             x0, [x0, #0xa18]
    // 0xb0c65c: StoreField: r1->field_1b = r0
    //     0xb0c65c: stur            w0, [x1, #0x1b]
    // 0xb0c660: r0 = Instance_VerticalDirection
    //     0xb0c660: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0c664: ldr             x0, [x0, #0xa20]
    // 0xb0c668: StoreField: r1->field_23 = r0
    //     0xb0c668: stur            w0, [x1, #0x23]
    // 0xb0c66c: r0 = Instance_Clip
    //     0xb0c66c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0c670: ldr             x0, [x0, #0x38]
    // 0xb0c674: StoreField: r1->field_2b = r0
    //     0xb0c674: stur            w0, [x1, #0x2b]
    // 0xb0c678: StoreField: r1->field_2f = rZR
    //     0xb0c678: stur            xzr, [x1, #0x2f]
    // 0xb0c67c: ldur            x0, [fp, #-0x10]
    // 0xb0c680: StoreField: r1->field_b = r0
    //     0xb0c680: stur            w0, [x1, #0xb]
    // 0xb0c684: r0 = Container()
    //     0xb0c684: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0c688: stur            x0, [fp, #-0x10]
    // 0xb0c68c: r16 = 147.000000
    //     0xb0c68c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52890] 147
    //     0xb0c690: ldr             x16, [x16, #0x890]
    // 0xb0c694: r30 = 35.000000
    //     0xb0c694: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xb0c698: ldr             lr, [lr, #0x2b0]
    // 0xb0c69c: stp             lr, x16, [SP, #0x18]
    // 0xb0c6a0: r16 = Instance_EdgeInsets
    //     0xb0c6a0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb0c6a4: ldr             x16, [x16, #0x850]
    // 0xb0c6a8: r30 = Instance_BoxDecoration
    //     0xb0c6a8: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb0c6ac: ldr             lr, [lr, #0x5a8]
    // 0xb0c6b0: stp             lr, x16, [SP, #8]
    // 0xb0c6b4: ldur            x16, [fp, #-8]
    // 0xb0c6b8: str             x16, [SP]
    // 0xb0c6bc: mov             x1, x0
    // 0xb0c6c0: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x2, padding, 0x3, width, 0x1, null]
    //     0xb0c6c0: add             x4, PP, #0x38, lsl #12  ; [pp+0x38060] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xb0c6c4: ldr             x4, [x4, #0x60]
    // 0xb0c6c8: r0 = Container()
    //     0xb0c6c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0c6cc: r1 = <Path>
    //     0xb0c6cc: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb0c6d0: ldr             x1, [x1, #0xd30]
    // 0xb0c6d4: r0 = CustomPath()
    //     0xb0c6d4: bl              #0xb0c7a8  ; AllocateCustomPathStub -> CustomPath (size=0x10)
    // 0xb0c6d8: stur            x0, [fp, #-8]
    // 0xb0c6dc: r0 = ClipPath()
    //     0xb0c6dc: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb0c6e0: mov             x1, x0
    // 0xb0c6e4: ldur            x0, [fp, #-8]
    // 0xb0c6e8: stur            x1, [fp, #-0x20]
    // 0xb0c6ec: StoreField: r1->field_f = r0
    //     0xb0c6ec: stur            w0, [x1, #0xf]
    // 0xb0c6f0: r0 = Instance_Clip
    //     0xb0c6f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb0c6f4: ldr             x0, [x0, #0x138]
    // 0xb0c6f8: StoreField: r1->field_13 = r0
    //     0xb0c6f8: stur            w0, [x1, #0x13]
    // 0xb0c6fc: ldur            x0, [fp, #-0x10]
    // 0xb0c700: StoreField: r1->field_b = r0
    //     0xb0c700: stur            w0, [x1, #0xb]
    // 0xb0c704: r0 = InkWell()
    //     0xb0c704: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb0c708: mov             x3, x0
    // 0xb0c70c: ldur            x0, [fp, #-0x20]
    // 0xb0c710: stur            x3, [fp, #-8]
    // 0xb0c714: StoreField: r3->field_b = r0
    //     0xb0c714: stur            w0, [x3, #0xb]
    // 0xb0c718: ldur            x2, [fp, #-0x18]
    // 0xb0c71c: r1 = Function '<anonymous closure>':.
    //     0xb0c71c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c58] AnonymousClosure: (0xb0c7b4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildFreeGiftButton (0xb0c504)
    //     0xb0c720: ldr             x1, [x1, #0xc58]
    // 0xb0c724: r0 = AllocateClosure()
    //     0xb0c724: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0c728: mov             x1, x0
    // 0xb0c72c: ldur            x0, [fp, #-8]
    // 0xb0c730: StoreField: r0->field_f = r1
    //     0xb0c730: stur            w1, [x0, #0xf]
    // 0xb0c734: r1 = true
    //     0xb0c734: add             x1, NULL, #0x20  ; true
    // 0xb0c738: StoreField: r0->field_43 = r1
    //     0xb0c738: stur            w1, [x0, #0x43]
    // 0xb0c73c: r2 = Instance_BoxShape
    //     0xb0c73c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0c740: ldr             x2, [x2, #0x80]
    // 0xb0c744: StoreField: r0->field_47 = r2
    //     0xb0c744: stur            w2, [x0, #0x47]
    // 0xb0c748: StoreField: r0->field_6f = r1
    //     0xb0c748: stur            w1, [x0, #0x6f]
    // 0xb0c74c: r2 = false
    //     0xb0c74c: add             x2, NULL, #0x30  ; false
    // 0xb0c750: StoreField: r0->field_73 = r2
    //     0xb0c750: stur            w2, [x0, #0x73]
    // 0xb0c754: StoreField: r0->field_83 = r1
    //     0xb0c754: stur            w1, [x0, #0x83]
    // 0xb0c758: StoreField: r0->field_7b = r2
    //     0xb0c758: stur            w2, [x0, #0x7b]
    // 0xb0c75c: r0 = Align()
    //     0xb0c75c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb0c760: mov             x1, x0
    // 0xb0c764: r0 = Instance_Alignment
    //     0xb0c764: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb0c768: ldr             x0, [x0, #0xa78]
    // 0xb0c76c: stur            x1, [fp, #-0x10]
    // 0xb0c770: StoreField: r1->field_f = r0
    //     0xb0c770: stur            w0, [x1, #0xf]
    // 0xb0c774: ldur            x0, [fp, #-8]
    // 0xb0c778: StoreField: r1->field_b = r0
    //     0xb0c778: stur            w0, [x1, #0xb]
    // 0xb0c77c: r0 = Padding()
    //     0xb0c77c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0c780: r1 = Instance_EdgeInsets
    //     0xb0c780: add             x1, PP, #0x52, lsl #12  ; [pp+0x528a0] Obj!EdgeInsets@d58941
    //     0xb0c784: ldr             x1, [x1, #0x8a0]
    // 0xb0c788: StoreField: r0->field_f = r1
    //     0xb0c788: stur            w1, [x0, #0xf]
    // 0xb0c78c: ldur            x1, [fp, #-0x10]
    // 0xb0c790: StoreField: r0->field_b = r1
    //     0xb0c790: stur            w1, [x0, #0xb]
    // 0xb0c794: LeaveFrame
    //     0xb0c794: mov             SP, fp
    //     0xb0c798: ldp             fp, lr, [SP], #0x10
    // 0xb0c79c: ret
    //     0xb0c79c: ret             
    // 0xb0c7a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c7a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c7a4: b               #0xb0c52c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0c7b4, size: 0x98
    // 0xb0c7b4: EnterFrame
    //     0xb0c7b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0c7b8: mov             fp, SP
    // 0xb0c7bc: AllocStack(0x10)
    //     0xb0c7bc: sub             SP, SP, #0x10
    // 0xb0c7c0: SetupParameters()
    //     0xb0c7c0: ldr             x0, [fp, #0x10]
    //     0xb0c7c4: ldur            w1, [x0, #0x17]
    //     0xb0c7c8: add             x1, x1, HEAP, lsl #32
    //     0xb0c7cc: stur            x1, [fp, #-8]
    // 0xb0c7d0: CheckStackOverflow
    //     0xb0c7d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0c7d4: cmp             SP, x16
    //     0xb0c7d8: b.ls            #0xb0c840
    // 0xb0c7dc: LoadField: r0 = r1->field_f
    //     0xb0c7dc: ldur            w0, [x1, #0xf]
    // 0xb0c7e0: DecompressPointer r0
    //     0xb0c7e0: add             x0, x0, HEAP, lsl #32
    // 0xb0c7e4: LoadField: r2 = r0->field_b
    //     0xb0c7e4: ldur            w2, [x0, #0xb]
    // 0xb0c7e8: DecompressPointer r2
    //     0xb0c7e8: add             x2, x2, HEAP, lsl #32
    // 0xb0c7ec: cmp             w2, NULL
    // 0xb0c7f0: b.eq            #0xb0c848
    // 0xb0c7f4: LoadField: r0 = r2->field_1f
    //     0xb0c7f4: ldur            w0, [x2, #0x1f]
    // 0xb0c7f8: DecompressPointer r0
    //     0xb0c7f8: add             x0, x0, HEAP, lsl #32
    // 0xb0c7fc: str             x0, [SP]
    // 0xb0c800: r4 = 0
    //     0xb0c800: movz            x4, #0
    // 0xb0c804: ldr             x0, [SP]
    // 0xb0c808: r16 = UnlinkedCall_0x613b5c
    //     0xb0c808: add             x16, PP, #0x57, lsl #12  ; [pp+0x57c60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0c80c: add             x16, x16, #0xc60
    // 0xb0c810: ldp             x5, lr, [x16]
    // 0xb0c814: blr             lr
    // 0xb0c818: ldur            x0, [fp, #-8]
    // 0xb0c81c: LoadField: r1 = r0->field_f
    //     0xb0c81c: ldur            w1, [x0, #0xf]
    // 0xb0c820: DecompressPointer r1
    //     0xb0c820: add             x1, x1, HEAP, lsl #32
    // 0xb0c824: LoadField: r2 = r0->field_13
    //     0xb0c824: ldur            w2, [x0, #0x13]
    // 0xb0c828: DecompressPointer r2
    //     0xb0c828: add             x2, x2, HEAP, lsl #32
    // 0xb0c82c: r0 = _showFreeGiftDialog()
    //     0xb0c82c: bl              #0xb0c84c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog
    // 0xb0c830: r0 = Null
    //     0xb0c830: mov             x0, NULL
    // 0xb0c834: LeaveFrame
    //     0xb0c834: mov             SP, fp
    //     0xb0c838: ldp             fp, lr, [SP], #0x10
    // 0xb0c83c: ret
    //     0xb0c83c: ret             
    // 0xb0c840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c840: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c844: b               #0xb0c7dc
    // 0xb0c848: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0c848: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _showFreeGiftDialog(/* No info */) {
    // ** addr: 0xb0c84c, size: 0x1c0
    // 0xb0c84c: EnterFrame
    //     0xb0c84c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0c850: mov             fp, SP
    // 0xb0c854: AllocStack(0x38)
    //     0xb0c854: sub             SP, SP, #0x38
    // 0xb0c858: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb0c858: mov             x0, x1
    //     0xb0c85c: stur            x1, [fp, #-8]
    //     0xb0c860: mov             x1, x2
    //     0xb0c864: stur            x2, [fp, #-0x10]
    // 0xb0c868: CheckStackOverflow
    //     0xb0c868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0c86c: cmp             SP, x16
    //     0xb0c870: b.ls            #0xb0c9e8
    // 0xb0c874: r1 = 3
    //     0xb0c874: movz            x1, #0x3
    // 0xb0c878: r0 = AllocateContext()
    //     0xb0c878: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0c87c: mov             x2, x0
    // 0xb0c880: ldur            x0, [fp, #-8]
    // 0xb0c884: stur            x2, [fp, #-0x18]
    // 0xb0c888: StoreField: r2->field_f = r0
    //     0xb0c888: stur            w0, [x2, #0xf]
    // 0xb0c88c: LoadField: r1 = r0->field_b
    //     0xb0c88c: ldur            w1, [x0, #0xb]
    // 0xb0c890: DecompressPointer r1
    //     0xb0c890: add             x1, x1, HEAP, lsl #32
    // 0xb0c894: cmp             w1, NULL
    // 0xb0c898: b.eq            #0xb0c9f0
    // 0xb0c89c: LoadField: r0 = r1->field_1b
    //     0xb0c89c: ldur            w0, [x1, #0x1b]
    // 0xb0c8a0: DecompressPointer r0
    //     0xb0c8a0: add             x0, x0, HEAP, lsl #32
    // 0xb0c8a4: LoadField: r1 = r0->field_b
    //     0xb0c8a4: ldur            w1, [x0, #0xb]
    // 0xb0c8a8: DecompressPointer r1
    //     0xb0c8a8: add             x1, x1, HEAP, lsl #32
    // 0xb0c8ac: StoreField: r2->field_13 = r1
    //     0xb0c8ac: stur            w1, [x2, #0x13]
    // 0xb0c8b0: cmp             w1, NULL
    // 0xb0c8b4: b.ne            #0xb0c8c8
    // 0xb0c8b8: r0 = Null
    //     0xb0c8b8: mov             x0, NULL
    // 0xb0c8bc: LeaveFrame
    //     0xb0c8bc: mov             SP, fp
    //     0xb0c8c0: ldp             fp, lr, [SP], #0x10
    // 0xb0c8c4: ret
    //     0xb0c8c4: ret             
    // 0xb0c8c8: LoadField: r0 = r1->field_7
    //     0xb0c8c8: ldur            w0, [x1, #7]
    // 0xb0c8cc: DecompressPointer r0
    //     0xb0c8cc: add             x0, x0, HEAP, lsl #32
    // 0xb0c8d0: LoadField: r1 = r0->field_b
    //     0xb0c8d0: ldur            w1, [x0, #0xb]
    // 0xb0c8d4: r0 = LoadInt32Instr(r1)
    //     0xb0c8d4: sbfx            x0, x1, #1, #0x1f
    // 0xb0c8d8: cbnz            x0, #0xb0c908
    // 0xb0c8dc: ldur            x1, [fp, #-0x10]
    // 0xb0c8e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0c8e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0c8e4: r0 = _of()
    //     0xb0c8e4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0c8e8: LoadField: r1 = r0->field_7
    //     0xb0c8e8: ldur            w1, [x0, #7]
    // 0xb0c8ec: DecompressPointer r1
    //     0xb0c8ec: add             x1, x1, HEAP, lsl #32
    // 0xb0c8f0: LoadField: d0 = r1->field_f
    //     0xb0c8f0: ldur            d0, [x1, #0xf]
    // 0xb0c8f4: d1 = 0.230000
    //     0xb0c8f4: add             x17, PP, #0x52, lsl #12  ; [pp+0x528b8] IMM: double(0.23) from 0x3fcd70a3d70a3d71
    //     0xb0c8f8: ldr             d1, [x17, #0x8b8]
    // 0xb0c8fc: fmul            d2, d0, d1
    // 0xb0c900: mov             v0.16b, v2.16b
    // 0xb0c904: b               #0xb0c964
    // 0xb0c908: cmp             x0, #1
    // 0xb0c90c: b.ne            #0xb0c93c
    // 0xb0c910: ldur            x1, [fp, #-0x10]
    // 0xb0c914: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0c914: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0c918: r0 = _of()
    //     0xb0c918: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0c91c: LoadField: r1 = r0->field_7
    //     0xb0c91c: ldur            w1, [x0, #7]
    // 0xb0c920: DecompressPointer r1
    //     0xb0c920: add             x1, x1, HEAP, lsl #32
    // 0xb0c924: LoadField: d0 = r1->field_f
    //     0xb0c924: ldur            d0, [x1, #0xf]
    // 0xb0c928: d1 = 0.210000
    //     0xb0c928: add             x17, PP, #0x52, lsl #12  ; [pp+0x528c0] IMM: double(0.21) from 0x3fcae147ae147ae1
    //     0xb0c92c: ldr             d1, [x17, #0x8c0]
    // 0xb0c930: fmul            d2, d0, d1
    // 0xb0c934: mov             v0.16b, v2.16b
    // 0xb0c938: b               #0xb0c964
    // 0xb0c93c: ldur            x1, [fp, #-0x10]
    // 0xb0c940: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0c940: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0c944: r0 = _of()
    //     0xb0c944: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0c948: LoadField: r1 = r0->field_7
    //     0xb0c948: ldur            w1, [x0, #7]
    // 0xb0c94c: DecompressPointer r1
    //     0xb0c94c: add             x1, x1, HEAP, lsl #32
    // 0xb0c950: LoadField: d0 = r1->field_f
    //     0xb0c950: ldur            d0, [x1, #0xf]
    // 0xb0c954: d1 = 0.185000
    //     0xb0c954: add             x17, PP, #0x52, lsl #12  ; [pp+0x528c8] IMM: double(0.185) from 0x3fc7ae147ae147ae
    //     0xb0c958: ldr             d1, [x17, #0x8c8]
    // 0xb0c95c: fmul            d2, d0, d1
    // 0xb0c960: mov             v0.16b, v2.16b
    // 0xb0c964: ldur            x2, [fp, #-0x18]
    // 0xb0c968: r0 = inline_Allocate_Double()
    //     0xb0c968: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb0c96c: add             x0, x0, #0x10
    //     0xb0c970: cmp             x1, x0
    //     0xb0c974: b.ls            #0xb0c9f4
    //     0xb0c978: str             x0, [THR, #0x50]  ; THR::top
    //     0xb0c97c: sub             x0, x0, #0xf
    //     0xb0c980: movz            x1, #0xe15c
    //     0xb0c984: movk            x1, #0x3, lsl #16
    //     0xb0c988: stur            x1, [x0, #-1]
    // 0xb0c98c: StoreField: r0->field_7 = d0
    //     0xb0c98c: stur            d0, [x0, #7]
    // 0xb0c990: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0c990: stur            w0, [x2, #0x17]
    //     0xb0c994: ldurb           w16, [x2, #-1]
    //     0xb0c998: ldurb           w17, [x0, #-1]
    //     0xb0c99c: and             x16, x17, x16, lsr #2
    //     0xb0c9a0: tst             x16, HEAP, lsr #32
    //     0xb0c9a4: b.eq            #0xb0c9ac
    //     0xb0c9a8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb0c9ac: r1 = Function '<anonymous closure>':.
    //     0xb0c9ac: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c70] AnonymousClosure: (0xb0ca0c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0xb0c84c)
    //     0xb0c9b0: ldr             x1, [x1, #0xc70]
    // 0xb0c9b4: r0 = AllocateClosure()
    //     0xb0c9b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0c9b8: r16 = <void?>
    //     0xb0c9b8: ldr             x16, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0xb0c9bc: stp             x0, x16, [SP, #0x10]
    // 0xb0c9c0: ldur            x16, [fp, #-0x10]
    // 0xb0c9c4: r30 = false
    //     0xb0c9c4: add             lr, NULL, #0x30  ; false
    // 0xb0c9c8: stp             lr, x16, [SP]
    // 0xb0c9cc: r4 = const [0x1, 0x3, 0x3, 0x2, barrierDismissible, 0x2, null]
    //     0xb0c9cc: add             x4, PP, #0x52, lsl #12  ; [pp+0x528d8] List(7) [0x1, 0x3, 0x3, 0x2, "barrierDismissible", 0x2, Null]
    //     0xb0c9d0: ldr             x4, [x4, #0x8d8]
    // 0xb0c9d4: r0 = showDialog()
    //     0xb0c9d4: bl              #0x99c870  ; [package:flutter/src/material/dialog.dart] ::showDialog
    // 0xb0c9d8: r0 = Null
    //     0xb0c9d8: mov             x0, NULL
    // 0xb0c9dc: LeaveFrame
    //     0xb0c9dc: mov             SP, fp
    //     0xb0c9e0: ldp             fp, lr, [SP], #0x10
    // 0xb0c9e4: ret
    //     0xb0c9e4: ret             
    // 0xb0c9e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0c9e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0c9ec: b               #0xb0c874
    // 0xb0c9f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0c9f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0c9f4: SaveReg d0
    //     0xb0c9f4: str             q0, [SP, #-0x10]!
    // 0xb0c9f8: SaveReg r2
    //     0xb0c9f8: str             x2, [SP, #-8]!
    // 0xb0c9fc: r0 = AllocateDouble()
    //     0xb0c9fc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0ca00: RestoreReg r2
    //     0xb0ca00: ldr             x2, [SP], #8
    // 0xb0ca04: RestoreReg d0
    //     0xb0ca04: ldr             q0, [SP], #0x10
    // 0xb0ca08: b               #0xb0c98c
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb0ca0c, size: 0x990
    // 0xb0ca0c: EnterFrame
    //     0xb0ca0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0ca10: mov             fp, SP
    // 0xb0ca14: AllocStack(0x78)
    //     0xb0ca14: sub             SP, SP, #0x78
    // 0xb0ca18: SetupParameters()
    //     0xb0ca18: ldr             x0, [fp, #0x18]
    //     0xb0ca1c: ldur            w1, [x0, #0x17]
    //     0xb0ca20: add             x1, x1, HEAP, lsl #32
    //     0xb0ca24: stur            x1, [fp, #-8]
    // 0xb0ca28: CheckStackOverflow
    //     0xb0ca28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0ca2c: cmp             SP, x16
    //     0xb0ca30: b.ls            #0xb0d330
    // 0xb0ca34: r1 = 1
    //     0xb0ca34: movz            x1, #0x1
    // 0xb0ca38: r0 = AllocateContext()
    //     0xb0ca38: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0ca3c: mov             x2, x0
    // 0xb0ca40: ldur            x0, [fp, #-8]
    // 0xb0ca44: stur            x2, [fp, #-0x10]
    // 0xb0ca48: StoreField: r2->field_b = r0
    //     0xb0ca48: stur            w0, [x2, #0xb]
    // 0xb0ca4c: ldr             x1, [fp, #0x10]
    // 0xb0ca50: StoreField: r2->field_f = r1
    //     0xb0ca50: stur            w1, [x2, #0xf]
    // 0xb0ca54: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0ca54: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0ca58: r0 = _of()
    //     0xb0ca58: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0ca5c: LoadField: r1 = r0->field_7
    //     0xb0ca5c: ldur            w1, [x0, #7]
    // 0xb0ca60: DecompressPointer r1
    //     0xb0ca60: add             x1, x1, HEAP, lsl #32
    // 0xb0ca64: LoadField: d0 = r1->field_7
    //     0xb0ca64: ldur            d0, [x1, #7]
    // 0xb0ca68: d1 = 0.880000
    //     0xb0ca68: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0xb0ca6c: ldr             d1, [x17, #0x8e0]
    // 0xb0ca70: fmul            d2, d0, d1
    // 0xb0ca74: ldur            x2, [fp, #-0x10]
    // 0xb0ca78: stur            d2, [fp, #-0x50]
    // 0xb0ca7c: LoadField: r1 = r2->field_f
    //     0xb0ca7c: ldur            w1, [x2, #0xf]
    // 0xb0ca80: DecompressPointer r1
    //     0xb0ca80: add             x1, x1, HEAP, lsl #32
    // 0xb0ca84: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0ca84: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0ca88: r0 = _of()
    //     0xb0ca88: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0ca8c: LoadField: r1 = r0->field_7
    //     0xb0ca8c: ldur            w1, [x0, #7]
    // 0xb0ca90: DecompressPointer r1
    //     0xb0ca90: add             x1, x1, HEAP, lsl #32
    // 0xb0ca94: LoadField: d0 = r1->field_7
    //     0xb0ca94: ldur            d0, [x1, #7]
    // 0xb0ca98: d1 = 0.880000
    //     0xb0ca98: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0xb0ca9c: ldr             d1, [x17, #0x8e0]
    // 0xb0caa0: fmul            d2, d0, d1
    // 0xb0caa4: stur            d2, [fp, #-0x58]
    // 0xb0caa8: r0 = Radius()
    //     0xb0caa8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0caac: d0 = 12.000000
    //     0xb0caac: fmov            d0, #12.00000000
    // 0xb0cab0: stur            x0, [fp, #-0x18]
    // 0xb0cab4: StoreField: r0->field_7 = d0
    //     0xb0cab4: stur            d0, [x0, #7]
    // 0xb0cab8: StoreField: r0->field_f = d0
    //     0xb0cab8: stur            d0, [x0, #0xf]
    // 0xb0cabc: r0 = BorderRadius()
    //     0xb0cabc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0cac0: mov             x1, x0
    // 0xb0cac4: ldur            x0, [fp, #-0x18]
    // 0xb0cac8: stur            x1, [fp, #-0x20]
    // 0xb0cacc: StoreField: r1->field_7 = r0
    //     0xb0cacc: stur            w0, [x1, #7]
    // 0xb0cad0: StoreField: r1->field_b = r0
    //     0xb0cad0: stur            w0, [x1, #0xb]
    // 0xb0cad4: StoreField: r1->field_f = r0
    //     0xb0cad4: stur            w0, [x1, #0xf]
    // 0xb0cad8: StoreField: r1->field_13 = r0
    //     0xb0cad8: stur            w0, [x1, #0x13]
    // 0xb0cadc: r0 = RoundedRectangleBorder()
    //     0xb0cadc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb0cae0: mov             x3, x0
    // 0xb0cae4: ldur            x0, [fp, #-0x20]
    // 0xb0cae8: stur            x3, [fp, #-0x18]
    // 0xb0caec: StoreField: r3->field_b = r0
    //     0xb0caec: stur            w0, [x3, #0xb]
    // 0xb0caf0: r0 = Instance_BorderSide
    //     0xb0caf0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb0caf4: ldr             x0, [x0, #0xe20]
    // 0xb0caf8: StoreField: r3->field_7 = r0
    //     0xb0caf8: stur            w0, [x3, #7]
    // 0xb0cafc: r1 = <Widget>
    //     0xb0cafc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0cb00: r2 = 20
    //     0xb0cb00: movz            x2, #0x14
    // 0xb0cb04: r0 = AllocateArray()
    //     0xb0cb04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0cb08: stur            x0, [fp, #-0x20]
    // 0xb0cb0c: r16 = Instance_SizedBox
    //     0xb0cb0c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0xb0cb10: ldr             x16, [x16, #0xd68]
    // 0xb0cb14: StoreField: r0->field_f = r16
    //     0xb0cb14: stur            w16, [x0, #0xf]
    // 0xb0cb18: ldur            x2, [fp, #-0x10]
    // 0xb0cb1c: LoadField: r1 = r2->field_f
    //     0xb0cb1c: ldur            w1, [x2, #0xf]
    // 0xb0cb20: DecompressPointer r1
    //     0xb0cb20: add             x1, x1, HEAP, lsl #32
    // 0xb0cb24: r0 = of()
    //     0xb0cb24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0cb28: LoadField: r1 = r0->field_87
    //     0xb0cb28: ldur            w1, [x0, #0x87]
    // 0xb0cb2c: DecompressPointer r1
    //     0xb0cb2c: add             x1, x1, HEAP, lsl #32
    // 0xb0cb30: LoadField: r0 = r1->field_7
    //     0xb0cb30: ldur            w0, [x1, #7]
    // 0xb0cb34: DecompressPointer r0
    //     0xb0cb34: add             x0, x0, HEAP, lsl #32
    // 0xb0cb38: r16 = 12.000000
    //     0xb0cb38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0cb3c: ldr             x16, [x16, #0x9e8]
    // 0xb0cb40: str             x16, [SP]
    // 0xb0cb44: mov             x1, x0
    // 0xb0cb48: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb0cb48: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb0cb4c: ldr             x4, [x4, #0x798]
    // 0xb0cb50: r0 = copyWith()
    //     0xb0cb50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0cb54: stur            x0, [fp, #-0x28]
    // 0xb0cb58: r0 = Text()
    //     0xb0cb58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0cb5c: mov             x1, x0
    // 0xb0cb60: r0 = "Get a Free Gift with this product!"
    //     0xb0cb60: add             x0, PP, #0x52, lsl #12  ; [pp+0x52910] "Get a Free Gift with this product!"
    //     0xb0cb64: ldr             x0, [x0, #0x910]
    // 0xb0cb68: StoreField: r1->field_b = r0
    //     0xb0cb68: stur            w0, [x1, #0xb]
    // 0xb0cb6c: ldur            x0, [fp, #-0x28]
    // 0xb0cb70: StoreField: r1->field_13 = r0
    //     0xb0cb70: stur            w0, [x1, #0x13]
    // 0xb0cb74: mov             x0, x1
    // 0xb0cb78: ldur            x1, [fp, #-0x20]
    // 0xb0cb7c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb0cb7c: add             x25, x1, #0x13
    //     0xb0cb80: str             w0, [x25]
    //     0xb0cb84: tbz             w0, #0, #0xb0cba0
    //     0xb0cb88: ldurb           w16, [x1, #-1]
    //     0xb0cb8c: ldurb           w17, [x0, #-1]
    //     0xb0cb90: and             x16, x17, x16, lsr #2
    //     0xb0cb94: tst             x16, HEAP, lsr #32
    //     0xb0cb98: b.eq            #0xb0cba0
    //     0xb0cb9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0cba0: ldur            x0, [fp, #-0x20]
    // 0xb0cba4: r16 = Instance_SizedBox
    //     0xb0cba4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb0cba8: ldr             x16, [x16, #0x578]
    // 0xb0cbac: ArrayStore: r0[0] = r16  ; List_4
    //     0xb0cbac: stur            w16, [x0, #0x17]
    // 0xb0cbb0: ldur            x2, [fp, #-8]
    // 0xb0cbb4: LoadField: r3 = r2->field_13
    //     0xb0cbb4: ldur            w3, [x2, #0x13]
    // 0xb0cbb8: DecompressPointer r3
    //     0xb0cbb8: add             x3, x3, HEAP, lsl #32
    // 0xb0cbbc: stur            x3, [fp, #-0x30]
    // 0xb0cbc0: cmp             w3, NULL
    // 0xb0cbc4: b.eq            #0xb0d338
    // 0xb0cbc8: LoadField: r1 = r3->field_f
    //     0xb0cbc8: ldur            w1, [x3, #0xf]
    // 0xb0cbcc: DecompressPointer r1
    //     0xb0cbcc: add             x1, x1, HEAP, lsl #32
    // 0xb0cbd0: cmp             w1, NULL
    // 0xb0cbd4: b.ne            #0xb0cbe0
    // 0xb0cbd8: r5 = ""
    //     0xb0cbd8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0cbdc: b               #0xb0cbe4
    // 0xb0cbe0: mov             x5, x1
    // 0xb0cbe4: ldur            x4, [fp, #-0x10]
    // 0xb0cbe8: stur            x5, [fp, #-0x28]
    // 0xb0cbec: LoadField: r1 = r4->field_f
    //     0xb0cbec: ldur            w1, [x4, #0xf]
    // 0xb0cbf0: DecompressPointer r1
    //     0xb0cbf0: add             x1, x1, HEAP, lsl #32
    // 0xb0cbf4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0cbf4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0cbf8: r0 = _of()
    //     0xb0cbf8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0cbfc: LoadField: r1 = r0->field_7
    //     0xb0cbfc: ldur            w1, [x0, #7]
    // 0xb0cc00: DecompressPointer r1
    //     0xb0cc00: add             x1, x1, HEAP, lsl #32
    // 0xb0cc04: LoadField: d0 = r1->field_f
    //     0xb0cc04: ldur            d0, [x1, #0xf]
    // 0xb0cc08: d1 = 0.200000
    //     0xb0cc08: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb0cc0c: fmul            d2, d0, d1
    // 0xb0cc10: ldur            x2, [fp, #-0x10]
    // 0xb0cc14: stur            d2, [fp, #-0x60]
    // 0xb0cc18: LoadField: r1 = r2->field_f
    //     0xb0cc18: ldur            w1, [x2, #0xf]
    // 0xb0cc1c: DecompressPointer r1
    //     0xb0cc1c: add             x1, x1, HEAP, lsl #32
    // 0xb0cc20: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0cc20: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0cc24: r0 = _of()
    //     0xb0cc24: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0cc28: LoadField: r1 = r0->field_7
    //     0xb0cc28: ldur            w1, [x0, #7]
    // 0xb0cc2c: DecompressPointer r1
    //     0xb0cc2c: add             x1, x1, HEAP, lsl #32
    // 0xb0cc30: LoadField: d0 = r1->field_7
    //     0xb0cc30: ldur            d0, [x1, #7]
    // 0xb0cc34: d1 = 0.770000
    //     0xb0cc34: add             x17, PP, #0x52, lsl #12  ; [pp+0x52918] IMM: double(0.77) from 0x3fe8a3d70a3d70a4
    //     0xb0cc38: ldr             d1, [x17, #0x918]
    // 0xb0cc3c: fmul            d2, d0, d1
    // 0xb0cc40: ldur            d0, [fp, #-0x60]
    // 0xb0cc44: r0 = inline_Allocate_Double()
    //     0xb0cc44: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb0cc48: add             x0, x0, #0x10
    //     0xb0cc4c: cmp             x1, x0
    //     0xb0cc50: b.ls            #0xb0d33c
    //     0xb0cc54: str             x0, [THR, #0x50]  ; THR::top
    //     0xb0cc58: sub             x0, x0, #0xf
    //     0xb0cc5c: movz            x1, #0xe15c
    //     0xb0cc60: movk            x1, #0x3, lsl #16
    //     0xb0cc64: stur            x1, [x0, #-1]
    // 0xb0cc68: StoreField: r0->field_7 = d0
    //     0xb0cc68: stur            d0, [x0, #7]
    // 0xb0cc6c: stur            x0, [fp, #-0x40]
    // 0xb0cc70: r1 = inline_Allocate_Double()
    //     0xb0cc70: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb0cc74: add             x1, x1, #0x10
    //     0xb0cc78: cmp             x2, x1
    //     0xb0cc7c: b.ls            #0xb0d34c
    //     0xb0cc80: str             x1, [THR, #0x50]  ; THR::top
    //     0xb0cc84: sub             x1, x1, #0xf
    //     0xb0cc88: movz            x2, #0xe15c
    //     0xb0cc8c: movk            x2, #0x3, lsl #16
    //     0xb0cc90: stur            x2, [x1, #-1]
    // 0xb0cc94: StoreField: r1->field_7 = d2
    //     0xb0cc94: stur            d2, [x1, #7]
    // 0xb0cc98: stur            x1, [fp, #-0x38]
    // 0xb0cc9c: r0 = CachedNetworkImage()
    //     0xb0cc9c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb0cca0: stur            x0, [fp, #-0x48]
    // 0xb0cca4: r16 = Instance_BoxFit
    //     0xb0cca4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb0cca8: ldr             x16, [x16, #0x118]
    // 0xb0ccac: ldur            lr, [fp, #-0x40]
    // 0xb0ccb0: stp             lr, x16, [SP, #8]
    // 0xb0ccb4: ldur            x16, [fp, #-0x38]
    // 0xb0ccb8: str             x16, [SP]
    // 0xb0ccbc: mov             x1, x0
    // 0xb0ccc0: ldur            x2, [fp, #-0x28]
    // 0xb0ccc4: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xb0ccc4: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xb0ccc8: ldr             x4, [x4, #0x8e0]
    // 0xb0cccc: r0 = CachedNetworkImage()
    //     0xb0cccc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb0ccd0: ldur            x1, [fp, #-0x20]
    // 0xb0ccd4: ldur            x0, [fp, #-0x48]
    // 0xb0ccd8: ArrayStore: r1[3] = r0  ; List_4
    //     0xb0ccd8: add             x25, x1, #0x1b
    //     0xb0ccdc: str             w0, [x25]
    //     0xb0cce0: tbz             w0, #0, #0xb0ccfc
    //     0xb0cce4: ldurb           w16, [x1, #-1]
    //     0xb0cce8: ldurb           w17, [x0, #-1]
    //     0xb0ccec: and             x16, x17, x16, lsr #2
    //     0xb0ccf0: tst             x16, HEAP, lsr #32
    //     0xb0ccf4: b.eq            #0xb0ccfc
    //     0xb0ccf8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0ccfc: ldur            x0, [fp, #-0x20]
    // 0xb0cd00: r16 = Instance_SizedBox
    //     0xb0cd00: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb0cd04: ldr             x16, [x16, #0x578]
    // 0xb0cd08: StoreField: r0->field_1f = r16
    //     0xb0cd08: stur            w16, [x0, #0x1f]
    // 0xb0cd0c: ldur            x3, [fp, #-0x30]
    // 0xb0cd10: LoadField: r1 = r3->field_b
    //     0xb0cd10: ldur            w1, [x3, #0xb]
    // 0xb0cd14: DecompressPointer r1
    //     0xb0cd14: add             x1, x1, HEAP, lsl #32
    // 0xb0cd18: cmp             w1, NULL
    // 0xb0cd1c: b.ne            #0xb0cd28
    // 0xb0cd20: r4 = ""
    //     0xb0cd20: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0cd24: b               #0xb0cd2c
    // 0xb0cd28: mov             x4, x1
    // 0xb0cd2c: ldur            x2, [fp, #-0x10]
    // 0xb0cd30: stur            x4, [fp, #-0x28]
    // 0xb0cd34: LoadField: r1 = r2->field_f
    //     0xb0cd34: ldur            w1, [x2, #0xf]
    // 0xb0cd38: DecompressPointer r1
    //     0xb0cd38: add             x1, x1, HEAP, lsl #32
    // 0xb0cd3c: r0 = of()
    //     0xb0cd3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0cd40: LoadField: r1 = r0->field_87
    //     0xb0cd40: ldur            w1, [x0, #0x87]
    // 0xb0cd44: DecompressPointer r1
    //     0xb0cd44: add             x1, x1, HEAP, lsl #32
    // 0xb0cd48: LoadField: r0 = r1->field_7
    //     0xb0cd48: ldur            w0, [x1, #7]
    // 0xb0cd4c: DecompressPointer r0
    //     0xb0cd4c: add             x0, x0, HEAP, lsl #32
    // 0xb0cd50: r16 = 12.000000
    //     0xb0cd50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0cd54: ldr             x16, [x16, #0x9e8]
    // 0xb0cd58: str             x16, [SP]
    // 0xb0cd5c: mov             x1, x0
    // 0xb0cd60: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb0cd60: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb0cd64: ldr             x4, [x4, #0x798]
    // 0xb0cd68: r0 = copyWith()
    //     0xb0cd68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0cd6c: stur            x0, [fp, #-0x38]
    // 0xb0cd70: r0 = Text()
    //     0xb0cd70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0cd74: mov             x1, x0
    // 0xb0cd78: ldur            x0, [fp, #-0x28]
    // 0xb0cd7c: StoreField: r1->field_b = r0
    //     0xb0cd7c: stur            w0, [x1, #0xb]
    // 0xb0cd80: ldur            x0, [fp, #-0x38]
    // 0xb0cd84: StoreField: r1->field_13 = r0
    //     0xb0cd84: stur            w0, [x1, #0x13]
    // 0xb0cd88: mov             x0, x1
    // 0xb0cd8c: ldur            x1, [fp, #-0x20]
    // 0xb0cd90: ArrayStore: r1[5] = r0  ; List_4
    //     0xb0cd90: add             x25, x1, #0x23
    //     0xb0cd94: str             w0, [x25]
    //     0xb0cd98: tbz             w0, #0, #0xb0cdb4
    //     0xb0cd9c: ldurb           w16, [x1, #-1]
    //     0xb0cda0: ldurb           w17, [x0, #-1]
    //     0xb0cda4: and             x16, x17, x16, lsr #2
    //     0xb0cda8: tst             x16, HEAP, lsr #32
    //     0xb0cdac: b.eq            #0xb0cdb4
    //     0xb0cdb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0cdb4: ldur            x0, [fp, #-0x20]
    // 0xb0cdb8: r16 = Instance_SizedBox
    //     0xb0cdb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb0cdbc: ldr             x16, [x16, #0x8f0]
    // 0xb0cdc0: StoreField: r0->field_27 = r16
    //     0xb0cdc0: stur            w16, [x0, #0x27]
    // 0xb0cdc4: ldur            x2, [fp, #-0x10]
    // 0xb0cdc8: LoadField: r1 = r2->field_f
    //     0xb0cdc8: ldur            w1, [x2, #0xf]
    // 0xb0cdcc: DecompressPointer r1
    //     0xb0cdcc: add             x1, x1, HEAP, lsl #32
    // 0xb0cdd0: r0 = of()
    //     0xb0cdd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0cdd4: LoadField: r1 = r0->field_87
    //     0xb0cdd4: ldur            w1, [x0, #0x87]
    // 0xb0cdd8: DecompressPointer r1
    //     0xb0cdd8: add             x1, x1, HEAP, lsl #32
    // 0xb0cddc: LoadField: r0 = r1->field_2b
    //     0xb0cddc: ldur            w0, [x1, #0x2b]
    // 0xb0cde0: DecompressPointer r0
    //     0xb0cde0: add             x0, x0, HEAP, lsl #32
    // 0xb0cde4: r16 = 12.000000
    //     0xb0cde4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0cde8: ldr             x16, [x16, #0x9e8]
    // 0xb0cdec: r30 = Instance_Color
    //     0xb0cdec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb0cdf0: ldr             lr, [lr, #0x858]
    // 0xb0cdf4: stp             lr, x16, [SP]
    // 0xb0cdf8: mov             x1, x0
    // 0xb0cdfc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0cdfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0ce00: ldr             x4, [x4, #0xaa0]
    // 0xb0ce04: r0 = copyWith()
    //     0xb0ce04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0ce08: stur            x0, [fp, #-0x28]
    // 0xb0ce0c: r0 = Text()
    //     0xb0ce0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0ce10: mov             x2, x0
    // 0xb0ce14: r0 = "Free"
    //     0xb0ce14: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb0ce18: ldr             x0, [x0, #0x668]
    // 0xb0ce1c: stur            x2, [fp, #-0x38]
    // 0xb0ce20: StoreField: r2->field_b = r0
    //     0xb0ce20: stur            w0, [x2, #0xb]
    // 0xb0ce24: ldur            x0, [fp, #-0x28]
    // 0xb0ce28: StoreField: r2->field_13 = r0
    //     0xb0ce28: stur            w0, [x2, #0x13]
    // 0xb0ce2c: ldur            x3, [fp, #-0x30]
    // 0xb0ce30: LoadField: r0 = r3->field_13
    //     0xb0ce30: ldur            w0, [x3, #0x13]
    // 0xb0ce34: DecompressPointer r0
    //     0xb0ce34: add             x0, x0, HEAP, lsl #32
    // 0xb0ce38: cmp             w0, NULL
    // 0xb0ce3c: b.ne            #0xb0ce48
    // 0xb0ce40: r7 = ""
    //     0xb0ce40: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ce44: b               #0xb0ce4c
    // 0xb0ce48: mov             x7, x0
    // 0xb0ce4c: ldur            x5, [fp, #-8]
    // 0xb0ce50: ldur            x4, [fp, #-0x10]
    // 0xb0ce54: ldur            d1, [fp, #-0x50]
    // 0xb0ce58: ldur            d0, [fp, #-0x58]
    // 0xb0ce5c: ldur            x6, [fp, #-0x18]
    // 0xb0ce60: ldur            x0, [fp, #-0x20]
    // 0xb0ce64: stur            x7, [fp, #-0x28]
    // 0xb0ce68: LoadField: r1 = r4->field_f
    //     0xb0ce68: ldur            w1, [x4, #0xf]
    // 0xb0ce6c: DecompressPointer r1
    //     0xb0ce6c: add             x1, x1, HEAP, lsl #32
    // 0xb0ce70: r0 = of()
    //     0xb0ce70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0ce74: LoadField: r1 = r0->field_87
    //     0xb0ce74: ldur            w1, [x0, #0x87]
    // 0xb0ce78: DecompressPointer r1
    //     0xb0ce78: add             x1, x1, HEAP, lsl #32
    // 0xb0ce7c: LoadField: r0 = r1->field_2b
    //     0xb0ce7c: ldur            w0, [x1, #0x2b]
    // 0xb0ce80: DecompressPointer r0
    //     0xb0ce80: add             x0, x0, HEAP, lsl #32
    // 0xb0ce84: r16 = 12.000000
    //     0xb0ce84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0ce88: ldr             x16, [x16, #0x9e8]
    // 0xb0ce8c: r30 = Instance_TextDecoration
    //     0xb0ce8c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb0ce90: ldr             lr, [lr, #0xe30]
    // 0xb0ce94: stp             lr, x16, [SP]
    // 0xb0ce98: mov             x1, x0
    // 0xb0ce9c: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb0ce9c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb0cea0: ldr             x4, [x4, #0x698]
    // 0xb0cea4: r0 = copyWith()
    //     0xb0cea4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0cea8: stur            x0, [fp, #-0x40]
    // 0xb0ceac: r0 = Text()
    //     0xb0ceac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0ceb0: mov             x3, x0
    // 0xb0ceb4: ldur            x0, [fp, #-0x28]
    // 0xb0ceb8: stur            x3, [fp, #-0x48]
    // 0xb0cebc: StoreField: r3->field_b = r0
    //     0xb0cebc: stur            w0, [x3, #0xb]
    // 0xb0cec0: ldur            x0, [fp, #-0x40]
    // 0xb0cec4: StoreField: r3->field_13 = r0
    //     0xb0cec4: stur            w0, [x3, #0x13]
    // 0xb0cec8: r1 = Null
    //     0xb0cec8: mov             x1, NULL
    // 0xb0cecc: r2 = 6
    //     0xb0cecc: movz            x2, #0x6
    // 0xb0ced0: r0 = AllocateArray()
    //     0xb0ced0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0ced4: mov             x2, x0
    // 0xb0ced8: ldur            x0, [fp, #-0x38]
    // 0xb0cedc: stur            x2, [fp, #-0x28]
    // 0xb0cee0: StoreField: r2->field_f = r0
    //     0xb0cee0: stur            w0, [x2, #0xf]
    // 0xb0cee4: r16 = Instance_SizedBox
    //     0xb0cee4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb0cee8: ldr             x16, [x16, #0xa50]
    // 0xb0ceec: StoreField: r2->field_13 = r16
    //     0xb0ceec: stur            w16, [x2, #0x13]
    // 0xb0cef0: ldur            x0, [fp, #-0x48]
    // 0xb0cef4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0cef4: stur            w0, [x2, #0x17]
    // 0xb0cef8: r1 = <Widget>
    //     0xb0cef8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0cefc: r0 = AllocateGrowableArray()
    //     0xb0cefc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0cf00: mov             x1, x0
    // 0xb0cf04: ldur            x0, [fp, #-0x28]
    // 0xb0cf08: stur            x1, [fp, #-0x38]
    // 0xb0cf0c: StoreField: r1->field_f = r0
    //     0xb0cf0c: stur            w0, [x1, #0xf]
    // 0xb0cf10: r0 = 6
    //     0xb0cf10: movz            x0, #0x6
    // 0xb0cf14: StoreField: r1->field_b = r0
    //     0xb0cf14: stur            w0, [x1, #0xb]
    // 0xb0cf18: r0 = Row()
    //     0xb0cf18: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0cf1c: mov             x1, x0
    // 0xb0cf20: r0 = Instance_Axis
    //     0xb0cf20: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0cf24: StoreField: r1->field_f = r0
    //     0xb0cf24: stur            w0, [x1, #0xf]
    // 0xb0cf28: r4 = Instance_MainAxisAlignment
    //     0xb0cf28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0cf2c: ldr             x4, [x4, #0xa08]
    // 0xb0cf30: StoreField: r1->field_13 = r4
    //     0xb0cf30: stur            w4, [x1, #0x13]
    // 0xb0cf34: r0 = Instance_MainAxisSize
    //     0xb0cf34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0cf38: ldr             x0, [x0, #0xa10]
    // 0xb0cf3c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0cf3c: stur            w0, [x1, #0x17]
    // 0xb0cf40: r0 = Instance_CrossAxisAlignment
    //     0xb0cf40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0cf44: ldr             x0, [x0, #0xa18]
    // 0xb0cf48: StoreField: r1->field_1b = r0
    //     0xb0cf48: stur            w0, [x1, #0x1b]
    // 0xb0cf4c: r5 = Instance_VerticalDirection
    //     0xb0cf4c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0cf50: ldr             x5, [x5, #0xa20]
    // 0xb0cf54: StoreField: r1->field_23 = r5
    //     0xb0cf54: stur            w5, [x1, #0x23]
    // 0xb0cf58: r6 = Instance_Clip
    //     0xb0cf58: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0cf5c: ldr             x6, [x6, #0x38]
    // 0xb0cf60: StoreField: r1->field_2b = r6
    //     0xb0cf60: stur            w6, [x1, #0x2b]
    // 0xb0cf64: StoreField: r1->field_2f = rZR
    //     0xb0cf64: stur            xzr, [x1, #0x2f]
    // 0xb0cf68: ldur            x0, [fp, #-0x38]
    // 0xb0cf6c: StoreField: r1->field_b = r0
    //     0xb0cf6c: stur            w0, [x1, #0xb]
    // 0xb0cf70: mov             x0, x1
    // 0xb0cf74: ldur            x1, [fp, #-0x20]
    // 0xb0cf78: ArrayStore: r1[7] = r0  ; List_4
    //     0xb0cf78: add             x25, x1, #0x2b
    //     0xb0cf7c: str             w0, [x25]
    //     0xb0cf80: tbz             w0, #0, #0xb0cf9c
    //     0xb0cf84: ldurb           w16, [x1, #-1]
    //     0xb0cf88: ldurb           w17, [x0, #-1]
    //     0xb0cf8c: and             x16, x17, x16, lsr #2
    //     0xb0cf90: tst             x16, HEAP, lsr #32
    //     0xb0cf94: b.eq            #0xb0cf9c
    //     0xb0cf98: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0cf9c: ldur            x0, [fp, #-0x20]
    // 0xb0cfa0: r16 = Instance_SizedBox
    //     0xb0cfa0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb0cfa4: ldr             x16, [x16, #0x8f0]
    // 0xb0cfa8: StoreField: r0->field_2f = r16
    //     0xb0cfa8: stur            w16, [x0, #0x2f]
    // 0xb0cfac: ldur            x7, [fp, #-8]
    // 0xb0cfb0: LoadField: r1 = r7->field_f
    //     0xb0cfb0: ldur            w1, [x7, #0xf]
    // 0xb0cfb4: DecompressPointer r1
    //     0xb0cfb4: add             x1, x1, HEAP, lsl #32
    // 0xb0cfb8: ldur            x8, [fp, #-0x10]
    // 0xb0cfbc: LoadField: r2 = r8->field_f
    //     0xb0cfbc: ldur            w2, [x8, #0xf]
    // 0xb0cfc0: DecompressPointer r2
    //     0xb0cfc0: add             x2, x2, HEAP, lsl #32
    // 0xb0cfc4: ldur            x3, [fp, #-0x30]
    // 0xb0cfc8: r0 = _buildRulesList()
    //     0xb0cfc8: bl              #0xb0d39c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildRulesList
    // 0xb0cfcc: ldur            x1, [fp, #-0x20]
    // 0xb0cfd0: ArrayStore: r1[9] = r0  ; List_4
    //     0xb0cfd0: add             x25, x1, #0x33
    //     0xb0cfd4: str             w0, [x25]
    //     0xb0cfd8: tbz             w0, #0, #0xb0cff4
    //     0xb0cfdc: ldurb           w16, [x1, #-1]
    //     0xb0cfe0: ldurb           w17, [x0, #-1]
    //     0xb0cfe4: and             x16, x17, x16, lsr #2
    //     0xb0cfe8: tst             x16, HEAP, lsr #32
    //     0xb0cfec: b.eq            #0xb0cff4
    //     0xb0cff0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0cff4: r1 = <Widget>
    //     0xb0cff4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0cff8: r0 = AllocateGrowableArray()
    //     0xb0cff8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0cffc: mov             x1, x0
    // 0xb0d000: ldur            x0, [fp, #-0x20]
    // 0xb0d004: stur            x1, [fp, #-0x28]
    // 0xb0d008: StoreField: r1->field_f = r0
    //     0xb0d008: stur            w0, [x1, #0xf]
    // 0xb0d00c: r0 = 20
    //     0xb0d00c: movz            x0, #0x14
    // 0xb0d010: StoreField: r1->field_b = r0
    //     0xb0d010: stur            w0, [x1, #0xb]
    // 0xb0d014: r0 = Column()
    //     0xb0d014: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb0d018: mov             x1, x0
    // 0xb0d01c: r0 = Instance_Axis
    //     0xb0d01c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb0d020: stur            x1, [fp, #-0x20]
    // 0xb0d024: StoreField: r1->field_f = r0
    //     0xb0d024: stur            w0, [x1, #0xf]
    // 0xb0d028: r0 = Instance_MainAxisAlignment
    //     0xb0d028: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0d02c: ldr             x0, [x0, #0xa08]
    // 0xb0d030: StoreField: r1->field_13 = r0
    //     0xb0d030: stur            w0, [x1, #0x13]
    // 0xb0d034: r0 = Instance_MainAxisSize
    //     0xb0d034: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb0d038: ldr             x0, [x0, #0xdd0]
    // 0xb0d03c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0d03c: stur            w0, [x1, #0x17]
    // 0xb0d040: r0 = Instance_CrossAxisAlignment
    //     0xb0d040: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb0d044: ldr             x0, [x0, #0x890]
    // 0xb0d048: StoreField: r1->field_1b = r0
    //     0xb0d048: stur            w0, [x1, #0x1b]
    // 0xb0d04c: r0 = Instance_VerticalDirection
    //     0xb0d04c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0d050: ldr             x0, [x0, #0xa20]
    // 0xb0d054: StoreField: r1->field_23 = r0
    //     0xb0d054: stur            w0, [x1, #0x23]
    // 0xb0d058: r0 = Instance_Clip
    //     0xb0d058: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0d05c: ldr             x0, [x0, #0x38]
    // 0xb0d060: StoreField: r1->field_2b = r0
    //     0xb0d060: stur            w0, [x1, #0x2b]
    // 0xb0d064: StoreField: r1->field_2f = rZR
    //     0xb0d064: stur            xzr, [x1, #0x2f]
    // 0xb0d068: ldur            x0, [fp, #-0x28]
    // 0xb0d06c: StoreField: r1->field_b = r0
    //     0xb0d06c: stur            w0, [x1, #0xb]
    // 0xb0d070: r0 = AlertDialog()
    //     0xb0d070: bl              #0x99db14  ; AllocateAlertDialogStub -> AlertDialog (size=0x50)
    // 0xb0d074: mov             x1, x0
    // 0xb0d078: ldur            x0, [fp, #-0x20]
    // 0xb0d07c: stur            x1, [fp, #-0x28]
    // 0xb0d080: StoreField: r1->field_f = r0
    //     0xb0d080: stur            w0, [x1, #0xf]
    // 0xb0d084: ldur            x0, [fp, #-0x18]
    // 0xb0d088: StoreField: r1->field_3f = r0
    //     0xb0d088: stur            w0, [x1, #0x3f]
    // 0xb0d08c: r0 = false
    //     0xb0d08c: add             x0, NULL, #0x30  ; false
    // 0xb0d090: StoreField: r1->field_4b = r0
    //     0xb0d090: stur            w0, [x1, #0x4b]
    // 0xb0d094: ldur            d0, [fp, #-0x58]
    // 0xb0d098: r2 = inline_Allocate_Double()
    //     0xb0d098: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb0d09c: add             x2, x2, #0x10
    //     0xb0d0a0: cmp             x3, x2
    //     0xb0d0a4: b.ls            #0xb0d368
    //     0xb0d0a8: str             x2, [THR, #0x50]  ; THR::top
    //     0xb0d0ac: sub             x2, x2, #0xf
    //     0xb0d0b0: movz            x3, #0xe15c
    //     0xb0d0b4: movk            x3, #0x3, lsl #16
    //     0xb0d0b8: stur            x3, [x2, #-1]
    // 0xb0d0bc: StoreField: r2->field_7 = d0
    //     0xb0d0bc: stur            d0, [x2, #7]
    // 0xb0d0c0: stur            x2, [fp, #-0x18]
    // 0xb0d0c4: r0 = SizedBox()
    //     0xb0d0c4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0d0c8: mov             x1, x0
    // 0xb0d0cc: ldur            x0, [fp, #-0x18]
    // 0xb0d0d0: stur            x1, [fp, #-0x20]
    // 0xb0d0d4: StoreField: r1->field_f = r0
    //     0xb0d0d4: stur            w0, [x1, #0xf]
    // 0xb0d0d8: ldur            x0, [fp, #-0x28]
    // 0xb0d0dc: StoreField: r1->field_b = r0
    //     0xb0d0dc: stur            w0, [x1, #0xb]
    // 0xb0d0e0: r0 = Center()
    //     0xb0d0e0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb0d0e4: mov             x1, x0
    // 0xb0d0e8: r0 = Instance_Alignment
    //     0xb0d0e8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb0d0ec: ldr             x0, [x0, #0xb10]
    // 0xb0d0f0: stur            x1, [fp, #-0x18]
    // 0xb0d0f4: StoreField: r1->field_f = r0
    //     0xb0d0f4: stur            w0, [x1, #0xf]
    // 0xb0d0f8: ldur            x0, [fp, #-0x20]
    // 0xb0d0fc: StoreField: r1->field_b = r0
    //     0xb0d0fc: stur            w0, [x1, #0xb]
    // 0xb0d100: r0 = SvgPicture()
    //     0xb0d100: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb0d104: mov             x1, x0
    // 0xb0d108: r2 = "assets/images/gift-icon-popup.svg"
    //     0xb0d108: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xb0d10c: ldr             x2, [x2, #0x8e8]
    // 0xb0d110: stur            x0, [fp, #-0x20]
    // 0xb0d114: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0d114: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0d118: r0 = SvgPicture.asset()
    //     0xb0d118: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0d11c: ldur            x0, [fp, #-8]
    // 0xb0d120: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb0d120: ldur            w2, [x0, #0x17]
    // 0xb0d124: DecompressPointer r2
    //     0xb0d124: add             x2, x2, HEAP, lsl #32
    // 0xb0d128: stur            x2, [fp, #-0x28]
    // 0xb0d12c: r1 = <StackParentData>
    //     0xb0d12c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb0d130: ldr             x1, [x1, #0x8e0]
    // 0xb0d134: r0 = Positioned()
    //     0xb0d134: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb0d138: mov             x3, x0
    // 0xb0d13c: ldur            x0, [fp, #-0x28]
    // 0xb0d140: stur            x3, [fp, #-8]
    // 0xb0d144: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0d144: stur            w0, [x3, #0x17]
    // 0xb0d148: ldur            x1, [fp, #-0x20]
    // 0xb0d14c: StoreField: r3->field_b = r1
    //     0xb0d14c: stur            w1, [x3, #0xb]
    // 0xb0d150: r1 = Null
    //     0xb0d150: mov             x1, NULL
    // 0xb0d154: r2 = 4
    //     0xb0d154: movz            x2, #0x4
    // 0xb0d158: r0 = AllocateArray()
    //     0xb0d158: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0d15c: mov             x2, x0
    // 0xb0d160: ldur            x0, [fp, #-0x18]
    // 0xb0d164: stur            x2, [fp, #-0x20]
    // 0xb0d168: StoreField: r2->field_f = r0
    //     0xb0d168: stur            w0, [x2, #0xf]
    // 0xb0d16c: ldur            x0, [fp, #-8]
    // 0xb0d170: StoreField: r2->field_13 = r0
    //     0xb0d170: stur            w0, [x2, #0x13]
    // 0xb0d174: r1 = <Widget>
    //     0xb0d174: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0d178: r0 = AllocateGrowableArray()
    //     0xb0d178: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0d17c: mov             x1, x0
    // 0xb0d180: ldur            x0, [fp, #-0x20]
    // 0xb0d184: stur            x1, [fp, #-8]
    // 0xb0d188: StoreField: r1->field_f = r0
    //     0xb0d188: stur            w0, [x1, #0xf]
    // 0xb0d18c: r2 = 4
    //     0xb0d18c: movz            x2, #0x4
    // 0xb0d190: StoreField: r1->field_b = r2
    //     0xb0d190: stur            w2, [x1, #0xb]
    // 0xb0d194: r0 = Stack()
    //     0xb0d194: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0d198: mov             x3, x0
    // 0xb0d19c: r0 = Instance_Alignment
    //     0xb0d19c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb0d1a0: ldr             x0, [x0, #0xce0]
    // 0xb0d1a4: stur            x3, [fp, #-0x18]
    // 0xb0d1a8: StoreField: r3->field_f = r0
    //     0xb0d1a8: stur            w0, [x3, #0xf]
    // 0xb0d1ac: r4 = Instance_StackFit
    //     0xb0d1ac: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb0d1b0: ldr             x4, [x4, #0xfa8]
    // 0xb0d1b4: ArrayStore: r3[0] = r4  ; List_4
    //     0xb0d1b4: stur            w4, [x3, #0x17]
    // 0xb0d1b8: r5 = Instance_Clip
    //     0xb0d1b8: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0d1bc: ldr             x5, [x5, #0x7e0]
    // 0xb0d1c0: StoreField: r3->field_1b = r5
    //     0xb0d1c0: stur            w5, [x3, #0x1b]
    // 0xb0d1c4: ldur            x1, [fp, #-8]
    // 0xb0d1c8: StoreField: r3->field_b = r1
    //     0xb0d1c8: stur            w1, [x3, #0xb]
    // 0xb0d1cc: ldur            x2, [fp, #-0x10]
    // 0xb0d1d0: r1 = Function '<anonymous closure>':.
    //     0xb0d1d0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c78] AnonymousClosure: (0x99db20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0x99e89c)
    //     0xb0d1d4: ldr             x1, [x1, #0xc78]
    // 0xb0d1d8: r0 = AllocateClosure()
    //     0xb0d1d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0d1dc: stur            x0, [fp, #-8]
    // 0xb0d1e0: r0 = IconButton()
    //     0xb0d1e0: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xb0d1e4: mov             x2, x0
    // 0xb0d1e8: ldur            x0, [fp, #-8]
    // 0xb0d1ec: stur            x2, [fp, #-0x10]
    // 0xb0d1f0: StoreField: r2->field_3b = r0
    //     0xb0d1f0: stur            w0, [x2, #0x3b]
    // 0xb0d1f4: r0 = false
    //     0xb0d1f4: add             x0, NULL, #0x30  ; false
    // 0xb0d1f8: StoreField: r2->field_4f = r0
    //     0xb0d1f8: stur            w0, [x2, #0x4f]
    // 0xb0d1fc: r0 = Instance_Icon
    //     0xb0d1fc: add             x0, PP, #0x52, lsl #12  ; [pp+0x528f8] Obj!Icon@d65d71
    //     0xb0d200: ldr             x0, [x0, #0x8f8]
    // 0xb0d204: StoreField: r2->field_1f = r0
    //     0xb0d204: stur            w0, [x2, #0x1f]
    // 0xb0d208: r0 = Instance__IconButtonVariant
    //     0xb0d208: add             x0, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xb0d20c: ldr             x0, [x0, #0x900]
    // 0xb0d210: StoreField: r2->field_6b = r0
    //     0xb0d210: stur            w0, [x2, #0x6b]
    // 0xb0d214: r1 = <StackParentData>
    //     0xb0d214: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb0d218: ldr             x1, [x1, #0x8e0]
    // 0xb0d21c: r0 = Positioned()
    //     0xb0d21c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb0d220: mov             x3, x0
    // 0xb0d224: ldur            x0, [fp, #-0x28]
    // 0xb0d228: stur            x3, [fp, #-8]
    // 0xb0d22c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0d22c: stur            w0, [x3, #0x17]
    // 0xb0d230: r0 = 50.000000
    //     0xb0d230: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xb0d234: ldr             x0, [x0, #0xa90]
    // 0xb0d238: StoreField: r3->field_1b = r0
    //     0xb0d238: stur            w0, [x3, #0x1b]
    // 0xb0d23c: ldur            x0, [fp, #-0x10]
    // 0xb0d240: StoreField: r3->field_b = r0
    //     0xb0d240: stur            w0, [x3, #0xb]
    // 0xb0d244: r1 = Null
    //     0xb0d244: mov             x1, NULL
    // 0xb0d248: r2 = 4
    //     0xb0d248: movz            x2, #0x4
    // 0xb0d24c: r0 = AllocateArray()
    //     0xb0d24c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0d250: mov             x2, x0
    // 0xb0d254: ldur            x0, [fp, #-0x18]
    // 0xb0d258: stur            x2, [fp, #-0x10]
    // 0xb0d25c: StoreField: r2->field_f = r0
    //     0xb0d25c: stur            w0, [x2, #0xf]
    // 0xb0d260: ldur            x0, [fp, #-8]
    // 0xb0d264: StoreField: r2->field_13 = r0
    //     0xb0d264: stur            w0, [x2, #0x13]
    // 0xb0d268: r1 = <Widget>
    //     0xb0d268: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0d26c: r0 = AllocateGrowableArray()
    //     0xb0d26c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0d270: mov             x1, x0
    // 0xb0d274: ldur            x0, [fp, #-0x10]
    // 0xb0d278: stur            x1, [fp, #-8]
    // 0xb0d27c: StoreField: r1->field_f = r0
    //     0xb0d27c: stur            w0, [x1, #0xf]
    // 0xb0d280: r0 = 4
    //     0xb0d280: movz            x0, #0x4
    // 0xb0d284: StoreField: r1->field_b = r0
    //     0xb0d284: stur            w0, [x1, #0xb]
    // 0xb0d288: r0 = Stack()
    //     0xb0d288: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0d28c: mov             x1, x0
    // 0xb0d290: r0 = Instance_Alignment
    //     0xb0d290: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb0d294: ldr             x0, [x0, #0xce0]
    // 0xb0d298: stur            x1, [fp, #-0x10]
    // 0xb0d29c: StoreField: r1->field_f = r0
    //     0xb0d29c: stur            w0, [x1, #0xf]
    // 0xb0d2a0: r0 = Instance_StackFit
    //     0xb0d2a0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb0d2a4: ldr             x0, [x0, #0xfa8]
    // 0xb0d2a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0d2a8: stur            w0, [x1, #0x17]
    // 0xb0d2ac: r0 = Instance_Clip
    //     0xb0d2ac: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0d2b0: ldr             x0, [x0, #0x7e0]
    // 0xb0d2b4: StoreField: r1->field_1b = r0
    //     0xb0d2b4: stur            w0, [x1, #0x1b]
    // 0xb0d2b8: ldur            x0, [fp, #-8]
    // 0xb0d2bc: StoreField: r1->field_b = r0
    //     0xb0d2bc: stur            w0, [x1, #0xb]
    // 0xb0d2c0: ldur            d0, [fp, #-0x50]
    // 0xb0d2c4: r0 = inline_Allocate_Double()
    //     0xb0d2c4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb0d2c8: add             x0, x0, #0x10
    //     0xb0d2cc: cmp             x2, x0
    //     0xb0d2d0: b.ls            #0xb0d384
    //     0xb0d2d4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb0d2d8: sub             x0, x0, #0xf
    //     0xb0d2dc: movz            x2, #0xe15c
    //     0xb0d2e0: movk            x2, #0x3, lsl #16
    //     0xb0d2e4: stur            x2, [x0, #-1]
    // 0xb0d2e8: StoreField: r0->field_7 = d0
    //     0xb0d2e8: stur            d0, [x0, #7]
    // 0xb0d2ec: stur            x0, [fp, #-8]
    // 0xb0d2f0: r0 = Container()
    //     0xb0d2f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0d2f4: stur            x0, [fp, #-0x18]
    // 0xb0d2f8: r16 = Instance_Color
    //     0xb0d2f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb0d2fc: ldr             x16, [x16, #0xf88]
    // 0xb0d300: ldur            lr, [fp, #-8]
    // 0xb0d304: stp             lr, x16, [SP, #8]
    // 0xb0d308: ldur            x16, [fp, #-0x10]
    // 0xb0d30c: str             x16, [SP]
    // 0xb0d310: mov             x1, x0
    // 0xb0d314: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, width, 0x2, null]
    //     0xb0d314: add             x4, PP, #0x52, lsl #12  ; [pp+0x52908] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "width", 0x2, Null]
    //     0xb0d318: ldr             x4, [x4, #0x908]
    // 0xb0d31c: r0 = Container()
    //     0xb0d31c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0d320: ldur            x0, [fp, #-0x18]
    // 0xb0d324: LeaveFrame
    //     0xb0d324: mov             SP, fp
    //     0xb0d328: ldp             fp, lr, [SP], #0x10
    // 0xb0d32c: ret
    //     0xb0d32c: ret             
    // 0xb0d330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d330: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d334: b               #0xb0ca34
    // 0xb0d338: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb0d338: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb0d33c: stp             q0, q2, [SP, #-0x20]!
    // 0xb0d340: r0 = AllocateDouble()
    //     0xb0d340: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0d344: ldp             q0, q2, [SP], #0x20
    // 0xb0d348: b               #0xb0cc68
    // 0xb0d34c: SaveReg d2
    //     0xb0d34c: str             q2, [SP, #-0x10]!
    // 0xb0d350: SaveReg r0
    //     0xb0d350: str             x0, [SP, #-8]!
    // 0xb0d354: r0 = AllocateDouble()
    //     0xb0d354: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0d358: mov             x1, x0
    // 0xb0d35c: RestoreReg r0
    //     0xb0d35c: ldr             x0, [SP], #8
    // 0xb0d360: RestoreReg d2
    //     0xb0d360: ldr             q2, [SP], #0x10
    // 0xb0d364: b               #0xb0cc94
    // 0xb0d368: SaveReg d0
    //     0xb0d368: str             q0, [SP, #-0x10]!
    // 0xb0d36c: stp             x0, x1, [SP, #-0x10]!
    // 0xb0d370: r0 = AllocateDouble()
    //     0xb0d370: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0d374: mov             x2, x0
    // 0xb0d378: ldp             x0, x1, [SP], #0x10
    // 0xb0d37c: RestoreReg d0
    //     0xb0d37c: ldr             q0, [SP], #0x10
    // 0xb0d380: b               #0xb0d0bc
    // 0xb0d384: SaveReg d0
    //     0xb0d384: str             q0, [SP, #-0x10]!
    // 0xb0d388: SaveReg r1
    //     0xb0d388: str             x1, [SP, #-8]!
    // 0xb0d38c: r0 = AllocateDouble()
    //     0xb0d38c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0d390: RestoreReg r1
    //     0xb0d390: ldr             x1, [SP], #8
    // 0xb0d394: RestoreReg d0
    //     0xb0d394: ldr             q0, [SP], #0x10
    // 0xb0d398: b               #0xb0d2e8
  }
  _ _buildRulesList(/* No info */) {
    // ** addr: 0xb0d39c, size: 0x180
    // 0xb0d39c: EnterFrame
    //     0xb0d39c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d3a0: mov             fp, SP
    // 0xb0d3a4: AllocStack(0x30)
    //     0xb0d3a4: sub             SP, SP, #0x30
    // 0xb0d3a8: SetupParameters(_ProductMediaCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb0d3a8: mov             x0, x1
    //     0xb0d3ac: mov             x1, x2
    //     0xb0d3b0: stur            x2, [fp, #-0x10]
    // 0xb0d3b4: CheckStackOverflow
    //     0xb0d3b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d3b8: cmp             SP, x16
    //     0xb0d3bc: b.ls            #0xb0d500
    // 0xb0d3c0: cmp             w3, NULL
    // 0xb0d3c4: b.eq            #0xb0d508
    // 0xb0d3c8: LoadField: r0 = r3->field_7
    //     0xb0d3c8: ldur            w0, [x3, #7]
    // 0xb0d3cc: DecompressPointer r0
    //     0xb0d3cc: add             x0, x0, HEAP, lsl #32
    // 0xb0d3d0: stur            x0, [fp, #-8]
    // 0xb0d3d4: r1 = 1
    //     0xb0d3d4: movz            x1, #0x1
    // 0xb0d3d8: r0 = AllocateContext()
    //     0xb0d3d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0d3dc: mov             x2, x0
    // 0xb0d3e0: ldur            x0, [fp, #-8]
    // 0xb0d3e4: stur            x2, [fp, #-0x18]
    // 0xb0d3e8: StoreField: r2->field_f = r0
    //     0xb0d3e8: stur            w0, [x2, #0xf]
    // 0xb0d3ec: LoadField: r1 = r0->field_b
    //     0xb0d3ec: ldur            w1, [x0, #0xb]
    // 0xb0d3f0: cbnz            w1, #0xb0d404
    // 0xb0d3f4: r0 = Instance_SizedBox
    //     0xb0d3f4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0d3f8: LeaveFrame
    //     0xb0d3f8: mov             SP, fp
    //     0xb0d3fc: ldp             fp, lr, [SP], #0x10
    // 0xb0d400: ret
    //     0xb0d400: ret             
    // 0xb0d404: cmp             w1, #2
    // 0xb0d408: b.ne            #0xb0d434
    // 0xb0d40c: ldur            x1, [fp, #-0x10]
    // 0xb0d410: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d410: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d414: r0 = _of()
    //     0xb0d414: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0d418: LoadField: r1 = r0->field_7
    //     0xb0d418: ldur            w1, [x0, #7]
    // 0xb0d41c: DecompressPointer r1
    //     0xb0d41c: add             x1, x1, HEAP, lsl #32
    // 0xb0d420: LoadField: d0 = r1->field_f
    //     0xb0d420: ldur            d0, [x1, #0xf]
    // 0xb0d424: d1 = 0.040000
    //     0xb0d424: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0xb0d428: fmul            d2, d0, d1
    // 0xb0d42c: mov             v0.16b, v2.16b
    // 0xb0d430: b               #0xb0d45c
    // 0xb0d434: ldur            x1, [fp, #-0x10]
    // 0xb0d438: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d438: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d43c: r0 = _of()
    //     0xb0d43c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0d440: LoadField: r1 = r0->field_7
    //     0xb0d440: ldur            w1, [x0, #7]
    // 0xb0d444: DecompressPointer r1
    //     0xb0d444: add             x1, x1, HEAP, lsl #32
    // 0xb0d448: LoadField: d0 = r1->field_f
    //     0xb0d448: ldur            d0, [x1, #0xf]
    // 0xb0d44c: d1 = 0.090000
    //     0xb0d44c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0xb0d450: ldr             d1, [x17, #0x920]
    // 0xb0d454: fmul            d2, d0, d1
    // 0xb0d458: mov             v0.16b, v2.16b
    // 0xb0d45c: ldur            x0, [fp, #-8]
    // 0xb0d460: stur            d0, [fp, #-0x20]
    // 0xb0d464: LoadField: r3 = r0->field_b
    //     0xb0d464: ldur            w3, [x0, #0xb]
    // 0xb0d468: ldur            x2, [fp, #-0x18]
    // 0xb0d46c: stur            x3, [fp, #-0x10]
    // 0xb0d470: r1 = Function '<anonymous closure>':.
    //     0xb0d470: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c80] AnonymousClosure: (0xa81aec), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildRulesList (0xa81bec)
    //     0xb0d474: ldr             x1, [x1, #0xc80]
    // 0xb0d478: r0 = AllocateClosure()
    //     0xb0d478: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0d47c: stur            x0, [fp, #-8]
    // 0xb0d480: r0 = ListView()
    //     0xb0d480: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb0d484: stur            x0, [fp, #-0x18]
    // 0xb0d488: r16 = true
    //     0xb0d488: add             x16, NULL, #0x20  ; true
    // 0xb0d48c: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb0d48c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb0d490: ldr             lr, [lr, #0x1c8]
    // 0xb0d494: stp             lr, x16, [SP]
    // 0xb0d498: mov             x1, x0
    // 0xb0d49c: ldur            x2, [fp, #-8]
    // 0xb0d4a0: ldur            x3, [fp, #-0x10]
    // 0xb0d4a4: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb0d4a4: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb0d4a8: ldr             x4, [x4, #8]
    // 0xb0d4ac: r0 = ListView.builder()
    //     0xb0d4ac: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb0d4b0: ldur            d0, [fp, #-0x20]
    // 0xb0d4b4: r0 = inline_Allocate_Double()
    //     0xb0d4b4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb0d4b8: add             x0, x0, #0x10
    //     0xb0d4bc: cmp             x1, x0
    //     0xb0d4c0: b.ls            #0xb0d50c
    //     0xb0d4c4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb0d4c8: sub             x0, x0, #0xf
    //     0xb0d4cc: movz            x1, #0xe15c
    //     0xb0d4d0: movk            x1, #0x3, lsl #16
    //     0xb0d4d4: stur            x1, [x0, #-1]
    // 0xb0d4d8: StoreField: r0->field_7 = d0
    //     0xb0d4d8: stur            d0, [x0, #7]
    // 0xb0d4dc: stur            x0, [fp, #-8]
    // 0xb0d4e0: r0 = SizedBox()
    //     0xb0d4e0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0d4e4: ldur            x1, [fp, #-8]
    // 0xb0d4e8: StoreField: r0->field_13 = r1
    //     0xb0d4e8: stur            w1, [x0, #0x13]
    // 0xb0d4ec: ldur            x1, [fp, #-0x18]
    // 0xb0d4f0: StoreField: r0->field_b = r1
    //     0xb0d4f0: stur            w1, [x0, #0xb]
    // 0xb0d4f4: LeaveFrame
    //     0xb0d4f4: mov             SP, fp
    //     0xb0d4f8: ldp             fp, lr, [SP], #0x10
    // 0xb0d4fc: ret
    //     0xb0d4fc: ret             
    // 0xb0d500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d500: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d504: b               #0xb0d3c0
    // 0xb0d508: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb0d508: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb0d50c: SaveReg d0
    //     0xb0d50c: str             q0, [SP, #-0x10]!
    // 0xb0d510: r0 = AllocateDouble()
    //     0xb0d510: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0d514: RestoreReg d0
    //     0xb0d514: ldr             q0, [SP], #0x10
    // 0xb0d518: b               #0xb0d4d8
  }
  _ _buildPageView(/* No info */) {
    // ** addr: 0xb0d51c, size: 0x174
    // 0xb0d51c: EnterFrame
    //     0xb0d51c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d520: mov             fp, SP
    // 0xb0d524: AllocStack(0x40)
    //     0xb0d524: sub             SP, SP, #0x40
    // 0xb0d528: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb0d528: mov             x0, x1
    //     0xb0d52c: stur            x1, [fp, #-8]
    //     0xb0d530: mov             x1, x2
    //     0xb0d534: stur            x2, [fp, #-0x10]
    // 0xb0d538: CheckStackOverflow
    //     0xb0d538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d53c: cmp             SP, x16
    //     0xb0d540: b.ls            #0xb0d66c
    // 0xb0d544: r1 = 1
    //     0xb0d544: movz            x1, #0x1
    // 0xb0d548: r0 = AllocateContext()
    //     0xb0d548: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0d54c: mov             x2, x0
    // 0xb0d550: ldur            x0, [fp, #-8]
    // 0xb0d554: stur            x2, [fp, #-0x18]
    // 0xb0d558: StoreField: r2->field_f = r0
    //     0xb0d558: stur            w0, [x2, #0xf]
    // 0xb0d55c: LoadField: r1 = r0->field_2f
    //     0xb0d55c: ldur            w1, [x0, #0x2f]
    // 0xb0d560: DecompressPointer r1
    //     0xb0d560: add             x1, x1, HEAP, lsl #32
    // 0xb0d564: cmp             w1, NULL
    // 0xb0d568: b.ne            #0xb0d594
    // 0xb0d56c: ldur            x1, [fp, #-0x10]
    // 0xb0d570: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d570: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d574: r0 = _of()
    //     0xb0d574: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb0d578: LoadField: r1 = r0->field_7
    //     0xb0d578: ldur            w1, [x0, #7]
    // 0xb0d57c: DecompressPointer r1
    //     0xb0d57c: add             x1, x1, HEAP, lsl #32
    // 0xb0d580: LoadField: d0 = r1->field_f
    //     0xb0d580: ldur            d0, [x1, #0xf]
    // 0xb0d584: d1 = 0.400000
    //     0xb0d584: ldr             d1, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb0d588: fmul            d2, d0, d1
    // 0xb0d58c: mov             v0.16b, v2.16b
    // 0xb0d590: b               #0xb0d598
    // 0xb0d594: LoadField: d0 = r1->field_7
    //     0xb0d594: ldur            d0, [x1, #7]
    // 0xb0d598: ldur            x0, [fp, #-8]
    // 0xb0d59c: stur            d0, [fp, #-0x30]
    // 0xb0d5a0: LoadField: r1 = r0->field_23
    //     0xb0d5a0: ldur            w1, [x0, #0x23]
    // 0xb0d5a4: DecompressPointer r1
    //     0xb0d5a4: add             x1, x1, HEAP, lsl #32
    // 0xb0d5a8: LoadField: r3 = r1->field_b
    //     0xb0d5a8: ldur            w3, [x1, #0xb]
    // 0xb0d5ac: stur            x3, [fp, #-0x20]
    // 0xb0d5b0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xb0d5b0: ldur            w4, [x0, #0x17]
    // 0xb0d5b4: DecompressPointer r4
    //     0xb0d5b4: add             x4, x4, HEAP, lsl #32
    // 0xb0d5b8: ldur            x2, [fp, #-0x18]
    // 0xb0d5bc: stur            x4, [fp, #-0x10]
    // 0xb0d5c0: r1 = Function '<anonymous closure>':.
    //     0xb0d5c0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c88] AnonymousClosure: (0xb0dfd4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildPageView (0xb0d51c)
    //     0xb0d5c4: ldr             x1, [x1, #0xc88]
    // 0xb0d5c8: r0 = AllocateClosure()
    //     0xb0d5c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0d5cc: ldur            x2, [fp, #-0x18]
    // 0xb0d5d0: r1 = Function '<anonymous closure>':.
    //     0xb0d5d0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c90] AnonymousClosure: (0xb0d690), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildPageView (0xb0d51c)
    //     0xb0d5d4: ldr             x1, [x1, #0xc90]
    // 0xb0d5d8: stur            x0, [fp, #-8]
    // 0xb0d5dc: r0 = AllocateClosure()
    //     0xb0d5dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0d5e0: stur            x0, [fp, #-0x18]
    // 0xb0d5e4: r0 = PageView()
    //     0xb0d5e4: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb0d5e8: stur            x0, [fp, #-0x28]
    // 0xb0d5ec: r16 = Instance_BouncingScrollPhysics
    //     0xb0d5ec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb0d5f0: ldr             x16, [x16, #0x890]
    // 0xb0d5f4: ldur            lr, [fp, #-0x10]
    // 0xb0d5f8: stp             lr, x16, [SP]
    // 0xb0d5fc: mov             x1, x0
    // 0xb0d600: ldur            x2, [fp, #-0x18]
    // 0xb0d604: ldur            x3, [fp, #-0x20]
    // 0xb0d608: ldur            x5, [fp, #-8]
    // 0xb0d60c: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb0d60c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb0d610: ldr             x4, [x4, #0xe40]
    // 0xb0d614: r0 = PageView.builder()
    //     0xb0d614: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb0d618: r0 = SizedBox()
    //     0xb0d618: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0d61c: r1 = inf
    //     0xb0d61c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb0d620: ldr             x1, [x1, #0x9f8]
    // 0xb0d624: StoreField: r0->field_f = r1
    //     0xb0d624: stur            w1, [x0, #0xf]
    // 0xb0d628: ldur            d0, [fp, #-0x30]
    // 0xb0d62c: r1 = inline_Allocate_Double()
    //     0xb0d62c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb0d630: add             x1, x1, #0x10
    //     0xb0d634: cmp             x2, x1
    //     0xb0d638: b.ls            #0xb0d674
    //     0xb0d63c: str             x1, [THR, #0x50]  ; THR::top
    //     0xb0d640: sub             x1, x1, #0xf
    //     0xb0d644: movz            x2, #0xe15c
    //     0xb0d648: movk            x2, #0x3, lsl #16
    //     0xb0d64c: stur            x2, [x1, #-1]
    // 0xb0d650: StoreField: r1->field_7 = d0
    //     0xb0d650: stur            d0, [x1, #7]
    // 0xb0d654: StoreField: r0->field_13 = r1
    //     0xb0d654: stur            w1, [x0, #0x13]
    // 0xb0d658: ldur            x1, [fp, #-0x28]
    // 0xb0d65c: StoreField: r0->field_b = r1
    //     0xb0d65c: stur            w1, [x0, #0xb]
    // 0xb0d660: LeaveFrame
    //     0xb0d660: mov             SP, fp
    //     0xb0d664: ldp             fp, lr, [SP], #0x10
    // 0xb0d668: ret
    //     0xb0d668: ret             
    // 0xb0d66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d66c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d670: b               #0xb0d544
    // 0xb0d674: SaveReg d0
    //     0xb0d674: str             q0, [SP, #-0x10]!
    // 0xb0d678: SaveReg r0
    //     0xb0d678: str             x0, [SP, #-8]!
    // 0xb0d67c: r0 = AllocateDouble()
    //     0xb0d67c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0d680: mov             x1, x0
    // 0xb0d684: RestoreReg r0
    //     0xb0d684: ldr             x0, [SP], #8
    // 0xb0d688: RestoreReg d0
    //     0xb0d688: ldr             q0, [SP], #0x10
    // 0xb0d68c: b               #0xb0d650
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb0d690, size: 0x148
    // 0xb0d690: EnterFrame
    //     0xb0d690: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d694: mov             fp, SP
    // 0xb0d698: AllocStack(0x28)
    //     0xb0d698: sub             SP, SP, #0x28
    // 0xb0d69c: SetupParameters()
    //     0xb0d69c: ldr             x0, [fp, #0x20]
    //     0xb0d6a0: ldur            w2, [x0, #0x17]
    //     0xb0d6a4: add             x2, x2, HEAP, lsl #32
    //     0xb0d6a8: stur            x2, [fp, #-0x10]
    // 0xb0d6ac: CheckStackOverflow
    //     0xb0d6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d6b0: cmp             SP, x16
    //     0xb0d6b4: b.ls            #0xb0d7c8
    // 0xb0d6b8: LoadField: r0 = r2->field_f
    //     0xb0d6b8: ldur            w0, [x2, #0xf]
    // 0xb0d6bc: DecompressPointer r0
    //     0xb0d6bc: add             x0, x0, HEAP, lsl #32
    // 0xb0d6c0: LoadField: r3 = r0->field_23
    //     0xb0d6c0: ldur            w3, [x0, #0x23]
    // 0xb0d6c4: DecompressPointer r3
    //     0xb0d6c4: add             x3, x3, HEAP, lsl #32
    // 0xb0d6c8: LoadField: r0 = r3->field_b
    //     0xb0d6c8: ldur            w0, [x3, #0xb]
    // 0xb0d6cc: ldr             x1, [fp, #0x10]
    // 0xb0d6d0: r4 = LoadInt32Instr(r1)
    //     0xb0d6d0: sbfx            x4, x1, #1, #0x1f
    //     0xb0d6d4: tbz             w1, #0, #0xb0d6dc
    //     0xb0d6d8: ldur            x4, [x1, #7]
    // 0xb0d6dc: stur            x4, [fp, #-8]
    // 0xb0d6e0: r1 = LoadInt32Instr(r0)
    //     0xb0d6e0: sbfx            x1, x0, #1, #0x1f
    // 0xb0d6e4: mov             x0, x1
    // 0xb0d6e8: mov             x1, x4
    // 0xb0d6ec: cmp             x1, x0
    // 0xb0d6f0: b.hs            #0xb0d7d0
    // 0xb0d6f4: LoadField: r0 = r3->field_f
    //     0xb0d6f4: ldur            w0, [x3, #0xf]
    // 0xb0d6f8: DecompressPointer r0
    //     0xb0d6f8: add             x0, x0, HEAP, lsl #32
    // 0xb0d6fc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb0d6fc: add             x16, x0, x4, lsl #2
    //     0xb0d700: ldur            w1, [x16, #0xf]
    // 0xb0d704: DecompressPointer r1
    //     0xb0d704: add             x1, x1, HEAP, lsl #32
    // 0xb0d708: LoadField: r0 = r1->field_1f
    //     0xb0d708: ldur            w0, [x1, #0x1f]
    // 0xb0d70c: DecompressPointer r0
    //     0xb0d70c: add             x0, x0, HEAP, lsl #32
    // 0xb0d710: r1 = LoadClassIdInstr(r0)
    //     0xb0d710: ldur            x1, [x0, #-1]
    //     0xb0d714: ubfx            x1, x1, #0xc, #0x14
    // 0xb0d718: r16 = "video"
    //     0xb0d718: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0xb0d71c: ldr             x16, [x16, #0xb50]
    // 0xb0d720: stp             x16, x0, [SP]
    // 0xb0d724: mov             x0, x1
    // 0xb0d728: mov             lr, x0
    // 0xb0d72c: ldr             lr, [x21, lr, lsl #3]
    // 0xb0d730: blr             lr
    // 0xb0d734: tbnz            w0, #4, #0xb0d798
    // 0xb0d738: ldur            x0, [fp, #-0x10]
    // 0xb0d73c: ldur            x2, [fp, #-8]
    // 0xb0d740: LoadField: r1 = r0->field_f
    //     0xb0d740: ldur            w1, [x0, #0xf]
    // 0xb0d744: DecompressPointer r1
    //     0xb0d744: add             x1, x1, HEAP, lsl #32
    // 0xb0d748: LoadField: r3 = r1->field_23
    //     0xb0d748: ldur            w3, [x1, #0x23]
    // 0xb0d74c: DecompressPointer r3
    //     0xb0d74c: add             x3, x3, HEAP, lsl #32
    // 0xb0d750: LoadField: r0 = r3->field_b
    //     0xb0d750: ldur            w0, [x3, #0xb]
    // 0xb0d754: r1 = LoadInt32Instr(r0)
    //     0xb0d754: sbfx            x1, x0, #1, #0x1f
    // 0xb0d758: mov             x0, x1
    // 0xb0d75c: mov             x1, x2
    // 0xb0d760: cmp             x1, x0
    // 0xb0d764: b.hs            #0xb0d7d4
    // 0xb0d768: LoadField: r0 = r3->field_f
    //     0xb0d768: ldur            w0, [x3, #0xf]
    // 0xb0d76c: DecompressPointer r0
    //     0xb0d76c: add             x0, x0, HEAP, lsl #32
    // 0xb0d770: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb0d770: add             x16, x0, x2, lsl #2
    //     0xb0d774: ldur            w1, [x16, #0xf]
    // 0xb0d778: DecompressPointer r1
    //     0xb0d778: add             x1, x1, HEAP, lsl #32
    // 0xb0d77c: stur            x1, [fp, #-0x18]
    // 0xb0d780: r0 = ProductVideoMediaCarousel()
    //     0xb0d780: bl              #0xb0dfc8  ; AllocateProductVideoMediaCarouselStub -> ProductVideoMediaCarousel (size=0x10)
    // 0xb0d784: mov             x1, x0
    // 0xb0d788: ldur            x0, [fp, #-0x18]
    // 0xb0d78c: StoreField: r1->field_b = r0
    //     0xb0d78c: stur            w0, [x1, #0xb]
    // 0xb0d790: mov             x0, x1
    // 0xb0d794: b               #0xb0d7bc
    // 0xb0d798: ldur            x0, [fp, #-0x10]
    // 0xb0d79c: ldur            x2, [fp, #-8]
    // 0xb0d7a0: LoadField: r1 = r0->field_f
    //     0xb0d7a0: ldur            w1, [x0, #0xf]
    // 0xb0d7a4: DecompressPointer r1
    //     0xb0d7a4: add             x1, x1, HEAP, lsl #32
    // 0xb0d7a8: LoadField: r0 = r1->field_23
    //     0xb0d7a8: ldur            w0, [x1, #0x23]
    // 0xb0d7ac: DecompressPointer r0
    //     0xb0d7ac: add             x0, x0, HEAP, lsl #32
    // 0xb0d7b0: mov             x3, x2
    // 0xb0d7b4: mov             x2, x0
    // 0xb0d7b8: r0 = _buildImageSlider()
    //     0xb0d7b8: bl              #0xb0d7d8  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildImageSlider
    // 0xb0d7bc: LeaveFrame
    //     0xb0d7bc: mov             SP, fp
    //     0xb0d7c0: ldp             fp, lr, [SP], #0x10
    // 0xb0d7c4: ret
    //     0xb0d7c4: ret             
    // 0xb0d7c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d7c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d7cc: b               #0xb0d6b8
    // 0xb0d7d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0d7d0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0d7d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0d7d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildImageSlider(/* No info */) {
    // ** addr: 0xb0d7d8, size: 0x234
    // 0xb0d7d8: EnterFrame
    //     0xb0d7d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d7dc: mov             fp, SP
    // 0xb0d7e0: AllocStack(0x28)
    //     0xb0d7e0: sub             SP, SP, #0x28
    // 0xb0d7e4: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb0d7e4: mov             x0, x1
    //     0xb0d7e8: stur            x1, [fp, #-8]
    //     0xb0d7ec: mov             x1, x3
    //     0xb0d7f0: stur            x2, [fp, #-0x10]
    //     0xb0d7f4: stur            x3, [fp, #-0x18]
    // 0xb0d7f8: CheckStackOverflow
    //     0xb0d7f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d7fc: cmp             SP, x16
    //     0xb0d800: b.ls            #0xb0da00
    // 0xb0d804: r1 = 3
    //     0xb0d804: movz            x1, #0x3
    // 0xb0d808: r0 = AllocateContext()
    //     0xb0d808: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0d80c: mov             x4, x0
    // 0xb0d810: ldur            x3, [fp, #-8]
    // 0xb0d814: stur            x4, [fp, #-0x20]
    // 0xb0d818: StoreField: r4->field_f = r3
    //     0xb0d818: stur            w3, [x4, #0xf]
    // 0xb0d81c: ldur            x2, [fp, #-0x10]
    // 0xb0d820: StoreField: r4->field_13 = r2
    //     0xb0d820: stur            w2, [x4, #0x13]
    // 0xb0d824: ldur            x5, [fp, #-0x18]
    // 0xb0d828: r0 = BoxInt64Instr(r5)
    //     0xb0d828: sbfiz           x0, x5, #1, #0x1f
    //     0xb0d82c: cmp             x5, x0, asr #1
    //     0xb0d830: b.eq            #0xb0d83c
    //     0xb0d834: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0d838: stur            x5, [x0, #7]
    // 0xb0d83c: ArrayStore: r4[0] = r0  ; List_4
    //     0xb0d83c: stur            w0, [x4, #0x17]
    // 0xb0d840: LoadField: r0 = r2->field_b
    //     0xb0d840: ldur            w0, [x2, #0xb]
    // 0xb0d844: r1 = LoadInt32Instr(r0)
    //     0xb0d844: sbfx            x1, x0, #1, #0x1f
    // 0xb0d848: cmp             x5, x1
    // 0xb0d84c: b.lt            #0xb0d860
    // 0xb0d850: r0 = Instance_SizedBox
    //     0xb0d850: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0d854: LeaveFrame
    //     0xb0d854: mov             SP, fp
    //     0xb0d858: ldp             fp, lr, [SP], #0x10
    // 0xb0d85c: ret
    //     0xb0d85c: ret             
    // 0xb0d860: LoadField: r2 = r3->field_23
    //     0xb0d860: ldur            w2, [x3, #0x23]
    // 0xb0d864: DecompressPointer r2
    //     0xb0d864: add             x2, x2, HEAP, lsl #32
    // 0xb0d868: LoadField: r0 = r2->field_b
    //     0xb0d868: ldur            w0, [x2, #0xb]
    // 0xb0d86c: r1 = LoadInt32Instr(r0)
    //     0xb0d86c: sbfx            x1, x0, #1, #0x1f
    // 0xb0d870: mov             x0, x1
    // 0xb0d874: mov             x1, x5
    // 0xb0d878: cmp             x1, x0
    // 0xb0d87c: b.hs            #0xb0da08
    // 0xb0d880: LoadField: r0 = r2->field_f
    //     0xb0d880: ldur            w0, [x2, #0xf]
    // 0xb0d884: DecompressPointer r0
    //     0xb0d884: add             x0, x0, HEAP, lsl #32
    // 0xb0d888: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0d888: add             x16, x0, x5, lsl #2
    //     0xb0d88c: ldur            w1, [x16, #0xf]
    // 0xb0d890: DecompressPointer r1
    //     0xb0d890: add             x1, x1, HEAP, lsl #32
    // 0xb0d894: LoadField: r0 = r1->field_2b
    //     0xb0d894: ldur            w0, [x1, #0x2b]
    // 0xb0d898: DecompressPointer r0
    //     0xb0d898: add             x0, x0, HEAP, lsl #32
    // 0xb0d89c: cmp             w0, NULL
    // 0xb0d8a0: b.ne            #0xb0d8ac
    // 0xb0d8a4: r0 = Null
    //     0xb0d8a4: mov             x0, NULL
    // 0xb0d8a8: b               #0xb0d8b8
    // 0xb0d8ac: LoadField: r1 = r0->field_b
    //     0xb0d8ac: ldur            w1, [x0, #0xb]
    // 0xb0d8b0: DecompressPointer r1
    //     0xb0d8b0: add             x1, x1, HEAP, lsl #32
    // 0xb0d8b4: mov             x0, x1
    // 0xb0d8b8: cmp             w0, NULL
    // 0xb0d8bc: b.ne            #0xb0d8c8
    // 0xb0d8c0: r2 = ""
    //     0xb0d8c0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0d8c4: b               #0xb0d8cc
    // 0xb0d8c8: mov             x2, x0
    // 0xb0d8cc: mov             x1, x3
    // 0xb0d8d0: r0 = _buildNetworkImage()
    //     0xb0d8d0: bl              #0xb0dc24  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildNetworkImage
    // 0xb0d8d4: stur            x0, [fp, #-0x10]
    // 0xb0d8d8: r0 = Center()
    //     0xb0d8d8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb0d8dc: mov             x1, x0
    // 0xb0d8e0: r0 = Instance_Alignment
    //     0xb0d8e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb0d8e4: ldr             x0, [x0, #0xb10]
    // 0xb0d8e8: stur            x1, [fp, #-0x28]
    // 0xb0d8ec: StoreField: r1->field_f = r0
    //     0xb0d8ec: stur            w0, [x1, #0xf]
    // 0xb0d8f0: ldur            x0, [fp, #-0x10]
    // 0xb0d8f4: StoreField: r1->field_b = r0
    //     0xb0d8f4: stur            w0, [x1, #0xb]
    // 0xb0d8f8: r0 = SizedBox()
    //     0xb0d8f8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0d8fc: mov             x1, x0
    // 0xb0d900: r0 = inf
    //     0xb0d900: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb0d904: ldr             x0, [x0, #0x9f8]
    // 0xb0d908: stur            x1, [fp, #-0x10]
    // 0xb0d90c: StoreField: r1->field_f = r0
    //     0xb0d90c: stur            w0, [x1, #0xf]
    // 0xb0d910: StoreField: r1->field_13 = r0
    //     0xb0d910: stur            w0, [x1, #0x13]
    // 0xb0d914: ldur            x0, [fp, #-0x28]
    // 0xb0d918: StoreField: r1->field_b = r0
    //     0xb0d918: stur            w0, [x1, #0xb]
    // 0xb0d91c: r0 = InkWell()
    //     0xb0d91c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb0d920: mov             x3, x0
    // 0xb0d924: ldur            x0, [fp, #-0x10]
    // 0xb0d928: stur            x3, [fp, #-0x28]
    // 0xb0d92c: StoreField: r3->field_b = r0
    //     0xb0d92c: stur            w0, [x3, #0xb]
    // 0xb0d930: ldur            x2, [fp, #-0x20]
    // 0xb0d934: r1 = Function '<anonymous closure>':.
    //     0xb0d934: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c98] AnonymousClosure: (0xb0df38), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildImageSlider (0xb0d7d8)
    //     0xb0d938: ldr             x1, [x1, #0xc98]
    // 0xb0d93c: r0 = AllocateClosure()
    //     0xb0d93c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0d940: mov             x1, x0
    // 0xb0d944: ldur            x0, [fp, #-0x28]
    // 0xb0d948: StoreField: r0->field_f = r1
    //     0xb0d948: stur            w1, [x0, #0xf]
    // 0xb0d94c: r1 = true
    //     0xb0d94c: add             x1, NULL, #0x20  ; true
    // 0xb0d950: StoreField: r0->field_43 = r1
    //     0xb0d950: stur            w1, [x0, #0x43]
    // 0xb0d954: r2 = Instance_BoxShape
    //     0xb0d954: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0d958: ldr             x2, [x2, #0x80]
    // 0xb0d95c: StoreField: r0->field_47 = r2
    //     0xb0d95c: stur            w2, [x0, #0x47]
    // 0xb0d960: StoreField: r0->field_6f = r1
    //     0xb0d960: stur            w1, [x0, #0x6f]
    // 0xb0d964: r2 = false
    //     0xb0d964: add             x2, NULL, #0x30  ; false
    // 0xb0d968: StoreField: r0->field_73 = r2
    //     0xb0d968: stur            w2, [x0, #0x73]
    // 0xb0d96c: StoreField: r0->field_83 = r1
    //     0xb0d96c: stur            w1, [x0, #0x83]
    // 0xb0d970: StoreField: r0->field_7b = r2
    //     0xb0d970: stur            w2, [x0, #0x7b]
    // 0xb0d974: ldur            x1, [fp, #-8]
    // 0xb0d978: r0 = _buildCustomizationLabel()
    //     0xb0d978: bl              #0xb0da0c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCustomizationLabel
    // 0xb0d97c: r1 = Null
    //     0xb0d97c: mov             x1, NULL
    // 0xb0d980: r2 = 4
    //     0xb0d980: movz            x2, #0x4
    // 0xb0d984: stur            x0, [fp, #-8]
    // 0xb0d988: r0 = AllocateArray()
    //     0xb0d988: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0d98c: mov             x2, x0
    // 0xb0d990: ldur            x0, [fp, #-0x28]
    // 0xb0d994: stur            x2, [fp, #-0x10]
    // 0xb0d998: StoreField: r2->field_f = r0
    //     0xb0d998: stur            w0, [x2, #0xf]
    // 0xb0d99c: ldur            x0, [fp, #-8]
    // 0xb0d9a0: StoreField: r2->field_13 = r0
    //     0xb0d9a0: stur            w0, [x2, #0x13]
    // 0xb0d9a4: r1 = <Widget>
    //     0xb0d9a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0d9a8: r0 = AllocateGrowableArray()
    //     0xb0d9a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0d9ac: mov             x1, x0
    // 0xb0d9b0: ldur            x0, [fp, #-0x10]
    // 0xb0d9b4: stur            x1, [fp, #-8]
    // 0xb0d9b8: StoreField: r1->field_f = r0
    //     0xb0d9b8: stur            w0, [x1, #0xf]
    // 0xb0d9bc: r0 = 4
    //     0xb0d9bc: movz            x0, #0x4
    // 0xb0d9c0: StoreField: r1->field_b = r0
    //     0xb0d9c0: stur            w0, [x1, #0xb]
    // 0xb0d9c4: r0 = Stack()
    //     0xb0d9c4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0d9c8: r1 = Instance_Alignment
    //     0xb0d9c8: add             x1, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb0d9cc: ldr             x1, [x1, #0x5b8]
    // 0xb0d9d0: StoreField: r0->field_f = r1
    //     0xb0d9d0: stur            w1, [x0, #0xf]
    // 0xb0d9d4: r1 = Instance_StackFit
    //     0xb0d9d4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb0d9d8: ldr             x1, [x1, #0xfa8]
    // 0xb0d9dc: ArrayStore: r0[0] = r1  ; List_4
    //     0xb0d9dc: stur            w1, [x0, #0x17]
    // 0xb0d9e0: r1 = Instance_Clip
    //     0xb0d9e0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0d9e4: ldr             x1, [x1, #0x7e0]
    // 0xb0d9e8: StoreField: r0->field_1b = r1
    //     0xb0d9e8: stur            w1, [x0, #0x1b]
    // 0xb0d9ec: ldur            x1, [fp, #-8]
    // 0xb0d9f0: StoreField: r0->field_b = r1
    //     0xb0d9f0: stur            w1, [x0, #0xb]
    // 0xb0d9f4: LeaveFrame
    //     0xb0d9f4: mov             SP, fp
    //     0xb0d9f8: ldp             fp, lr, [SP], #0x10
    // 0xb0d9fc: ret
    //     0xb0d9fc: ret             
    // 0xb0da00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0da00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0da04: b               #0xb0d804
    // 0xb0da08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0da08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationLabel(/* No info */) {
    // ** addr: 0xb0da0c, size: 0x218
    // 0xb0da0c: EnterFrame
    //     0xb0da0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0da10: mov             fp, SP
    // 0xb0da14: AllocStack(0x30)
    //     0xb0da14: sub             SP, SP, #0x30
    // 0xb0da18: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0xb0da18: mov             x0, x1
    //     0xb0da1c: stur            x1, [fp, #-8]
    // 0xb0da20: CheckStackOverflow
    //     0xb0da20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0da24: cmp             SP, x16
    //     0xb0da28: b.ls            #0xb0dc10
    // 0xb0da2c: LoadField: r1 = r0->field_b
    //     0xb0da2c: ldur            w1, [x0, #0xb]
    // 0xb0da30: DecompressPointer r1
    //     0xb0da30: add             x1, x1, HEAP, lsl #32
    // 0xb0da34: cmp             w1, NULL
    // 0xb0da38: b.eq            #0xb0dc18
    // 0xb0da3c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb0da3c: ldur            w2, [x1, #0x17]
    // 0xb0da40: DecompressPointer r2
    //     0xb0da40: add             x2, x2, HEAP, lsl #32
    // 0xb0da44: tbz             w2, #4, #0xb0da58
    // 0xb0da48: r0 = Instance_SizedBox
    //     0xb0da48: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0da4c: LeaveFrame
    //     0xb0da4c: mov             SP, fp
    //     0xb0da50: ldp             fp, lr, [SP], #0x10
    // 0xb0da54: ret
    //     0xb0da54: ret             
    // 0xb0da58: LoadField: r1 = r0->field_f
    //     0xb0da58: ldur            w1, [x0, #0xf]
    // 0xb0da5c: DecompressPointer r1
    //     0xb0da5c: add             x1, x1, HEAP, lsl #32
    // 0xb0da60: cmp             w1, NULL
    // 0xb0da64: b.eq            #0xb0dc1c
    // 0xb0da68: r0 = of()
    //     0xb0da68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0da6c: LoadField: r1 = r0->field_5b
    //     0xb0da6c: ldur            w1, [x0, #0x5b]
    // 0xb0da70: DecompressPointer r1
    //     0xb0da70: add             x1, x1, HEAP, lsl #32
    // 0xb0da74: r16 = <Color>
    //     0xb0da74: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb0da78: ldr             x16, [x16, #0xf80]
    // 0xb0da7c: stp             x1, x16, [SP]
    // 0xb0da80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0da80: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0da84: r0 = all()
    //     0xb0da84: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0da88: stur            x0, [fp, #-0x10]
    // 0xb0da8c: r0 = Radius()
    //     0xb0da8c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0da90: d0 = 5.000000
    //     0xb0da90: fmov            d0, #5.00000000
    // 0xb0da94: stur            x0, [fp, #-0x18]
    // 0xb0da98: StoreField: r0->field_7 = d0
    //     0xb0da98: stur            d0, [x0, #7]
    // 0xb0da9c: StoreField: r0->field_f = d0
    //     0xb0da9c: stur            d0, [x0, #0xf]
    // 0xb0daa0: r0 = BorderRadius()
    //     0xb0daa0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0daa4: mov             x1, x0
    // 0xb0daa8: ldur            x0, [fp, #-0x18]
    // 0xb0daac: stur            x1, [fp, #-0x20]
    // 0xb0dab0: StoreField: r1->field_7 = r0
    //     0xb0dab0: stur            w0, [x1, #7]
    // 0xb0dab4: StoreField: r1->field_b = r0
    //     0xb0dab4: stur            w0, [x1, #0xb]
    // 0xb0dab8: StoreField: r1->field_f = r0
    //     0xb0dab8: stur            w0, [x1, #0xf]
    // 0xb0dabc: StoreField: r1->field_13 = r0
    //     0xb0dabc: stur            w0, [x1, #0x13]
    // 0xb0dac0: r0 = RoundedRectangleBorder()
    //     0xb0dac0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb0dac4: mov             x1, x0
    // 0xb0dac8: ldur            x0, [fp, #-0x20]
    // 0xb0dacc: StoreField: r1->field_b = r0
    //     0xb0dacc: stur            w0, [x1, #0xb]
    // 0xb0dad0: r0 = Instance_BorderSide
    //     0xb0dad0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb0dad4: ldr             x0, [x0, #0xe20]
    // 0xb0dad8: StoreField: r1->field_7 = r0
    //     0xb0dad8: stur            w0, [x1, #7]
    // 0xb0dadc: r16 = <RoundedRectangleBorder>
    //     0xb0dadc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb0dae0: ldr             x16, [x16, #0xf78]
    // 0xb0dae4: stp             x1, x16, [SP]
    // 0xb0dae8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0dae8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0daec: r0 = all()
    //     0xb0daec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0daf0: stur            x0, [fp, #-0x18]
    // 0xb0daf4: r0 = ButtonStyle()
    //     0xb0daf4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb0daf8: mov             x1, x0
    // 0xb0dafc: ldur            x0, [fp, #-0x10]
    // 0xb0db00: stur            x1, [fp, #-0x20]
    // 0xb0db04: StoreField: r1->field_b = r0
    //     0xb0db04: stur            w0, [x1, #0xb]
    // 0xb0db08: ldur            x0, [fp, #-0x18]
    // 0xb0db0c: StoreField: r1->field_43 = r0
    //     0xb0db0c: stur            w0, [x1, #0x43]
    // 0xb0db10: r0 = TextButtonThemeData()
    //     0xb0db10: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb0db14: mov             x2, x0
    // 0xb0db18: ldur            x0, [fp, #-0x20]
    // 0xb0db1c: stur            x2, [fp, #-0x10]
    // 0xb0db20: StoreField: r2->field_7 = r0
    //     0xb0db20: stur            w0, [x2, #7]
    // 0xb0db24: ldur            x0, [fp, #-8]
    // 0xb0db28: LoadField: r1 = r0->field_f
    //     0xb0db28: ldur            w1, [x0, #0xf]
    // 0xb0db2c: DecompressPointer r1
    //     0xb0db2c: add             x1, x1, HEAP, lsl #32
    // 0xb0db30: cmp             w1, NULL
    // 0xb0db34: b.eq            #0xb0dc20
    // 0xb0db38: r0 = of()
    //     0xb0db38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0db3c: LoadField: r1 = r0->field_87
    //     0xb0db3c: ldur            w1, [x0, #0x87]
    // 0xb0db40: DecompressPointer r1
    //     0xb0db40: add             x1, x1, HEAP, lsl #32
    // 0xb0db44: LoadField: r0 = r1->field_2b
    //     0xb0db44: ldur            w0, [x1, #0x2b]
    // 0xb0db48: DecompressPointer r0
    //     0xb0db48: add             x0, x0, HEAP, lsl #32
    // 0xb0db4c: r16 = 14.000000
    //     0xb0db4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb0db50: ldr             x16, [x16, #0x1d8]
    // 0xb0db54: r30 = Instance_Color
    //     0xb0db54: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0db58: stp             lr, x16, [SP]
    // 0xb0db5c: mov             x1, x0
    // 0xb0db60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0db60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0db64: ldr             x4, [x4, #0xaa0]
    // 0xb0db68: r0 = copyWith()
    //     0xb0db68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0db6c: stur            x0, [fp, #-8]
    // 0xb0db70: r0 = Text()
    //     0xb0db70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0db74: mov             x3, x0
    // 0xb0db78: r0 = "Customisable"
    //     0xb0db78: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb0db7c: ldr             x0, [x0, #0x970]
    // 0xb0db80: stur            x3, [fp, #-0x18]
    // 0xb0db84: StoreField: r3->field_b = r0
    //     0xb0db84: stur            w0, [x3, #0xb]
    // 0xb0db88: ldur            x0, [fp, #-8]
    // 0xb0db8c: StoreField: r3->field_13 = r0
    //     0xb0db8c: stur            w0, [x3, #0x13]
    // 0xb0db90: r1 = Function '<anonymous closure>':.
    //     0xb0db90: add             x1, PP, #0x57, lsl #12  ; [pp+0x57cb0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb0db94: ldr             x1, [x1, #0xcb0]
    // 0xb0db98: r2 = Null
    //     0xb0db98: mov             x2, NULL
    // 0xb0db9c: r0 = AllocateClosure()
    //     0xb0db9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0dba0: stur            x0, [fp, #-8]
    // 0xb0dba4: r0 = TextButton()
    //     0xb0dba4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb0dba8: mov             x1, x0
    // 0xb0dbac: ldur            x0, [fp, #-8]
    // 0xb0dbb0: stur            x1, [fp, #-0x20]
    // 0xb0dbb4: StoreField: r1->field_b = r0
    //     0xb0dbb4: stur            w0, [x1, #0xb]
    // 0xb0dbb8: r0 = false
    //     0xb0dbb8: add             x0, NULL, #0x30  ; false
    // 0xb0dbbc: StoreField: r1->field_27 = r0
    //     0xb0dbbc: stur            w0, [x1, #0x27]
    // 0xb0dbc0: r0 = true
    //     0xb0dbc0: add             x0, NULL, #0x20  ; true
    // 0xb0dbc4: StoreField: r1->field_2f = r0
    //     0xb0dbc4: stur            w0, [x1, #0x2f]
    // 0xb0dbc8: ldur            x0, [fp, #-0x18]
    // 0xb0dbcc: StoreField: r1->field_37 = r0
    //     0xb0dbcc: stur            w0, [x1, #0x37]
    // 0xb0dbd0: r0 = TextButtonTheme()
    //     0xb0dbd0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb0dbd4: mov             x1, x0
    // 0xb0dbd8: ldur            x0, [fp, #-0x10]
    // 0xb0dbdc: stur            x1, [fp, #-8]
    // 0xb0dbe0: StoreField: r1->field_f = r0
    //     0xb0dbe0: stur            w0, [x1, #0xf]
    // 0xb0dbe4: ldur            x0, [fp, #-0x20]
    // 0xb0dbe8: StoreField: r1->field_b = r0
    //     0xb0dbe8: stur            w0, [x1, #0xb]
    // 0xb0dbec: r0 = Padding()
    //     0xb0dbec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0dbf0: r1 = Instance_EdgeInsets
    //     0xb0dbf0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb0dbf4: ldr             x1, [x1, #0xe68]
    // 0xb0dbf8: StoreField: r0->field_f = r1
    //     0xb0dbf8: stur            w1, [x0, #0xf]
    // 0xb0dbfc: ldur            x1, [fp, #-8]
    // 0xb0dc00: StoreField: r0->field_b = r1
    //     0xb0dc00: stur            w1, [x0, #0xb]
    // 0xb0dc04: LeaveFrame
    //     0xb0dc04: mov             SP, fp
    //     0xb0dc08: ldp             fp, lr, [SP], #0x10
    // 0xb0dc0c: ret
    //     0xb0dc0c: ret             
    // 0xb0dc10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0dc10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0dc14: b               #0xb0da2c
    // 0xb0dc18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0dc18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0dc1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0dc1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0dc20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0dc20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildNetworkImage(/* No info */) {
    // ** addr: 0xb0dc24, size: 0x1ac
    // 0xb0dc24: EnterFrame
    //     0xb0dc24: stp             fp, lr, [SP, #-0x10]!
    //     0xb0dc28: mov             fp, SP
    // 0xb0dc2c: AllocStack(0x28)
    //     0xb0dc2c: sub             SP, SP, #0x28
    // 0xb0dc30: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb0dc30: stur            x1, [fp, #-8]
    //     0xb0dc34: stur            x2, [fp, #-0x10]
    // 0xb0dc38: CheckStackOverflow
    //     0xb0dc38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0dc3c: cmp             SP, x16
    //     0xb0dc40: b.ls            #0xb0ddc4
    // 0xb0dc44: r1 = 1
    //     0xb0dc44: movz            x1, #0x1
    // 0xb0dc48: r0 = AllocateContext()
    //     0xb0dc48: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0dc4c: mov             x3, x0
    // 0xb0dc50: ldur            x0, [fp, #-8]
    // 0xb0dc54: stur            x3, [fp, #-0x20]
    // 0xb0dc58: StoreField: r3->field_f = r0
    //     0xb0dc58: stur            w0, [x3, #0xf]
    // 0xb0dc5c: ldur            x4, [fp, #-0x10]
    // 0xb0dc60: LoadField: r1 = r4->field_7
    //     0xb0dc60: ldur            w1, [x4, #7]
    // 0xb0dc64: cbnz            w1, #0xb0dc7c
    // 0xb0dc68: r0 = Instance_SizedBox
    //     0xb0dc68: add             x0, PP, #0x52, lsl #12  ; [pp+0x52988] Obj!SizedBox@d67fc1
    //     0xb0dc6c: ldr             x0, [x0, #0x988]
    // 0xb0dc70: LeaveFrame
    //     0xb0dc70: mov             SP, fp
    //     0xb0dc74: ldp             fp, lr, [SP], #0x10
    // 0xb0dc78: ret
    //     0xb0dc78: ret             
    // 0xb0dc7c: LoadField: r5 = r0->field_27
    //     0xb0dc7c: ldur            w5, [x0, #0x27]
    // 0xb0dc80: DecompressPointer r5
    //     0xb0dc80: add             x5, x5, HEAP, lsl #32
    // 0xb0dc84: mov             x1, x5
    // 0xb0dc88: mov             x2, x4
    // 0xb0dc8c: stur            x5, [fp, #-0x18]
    // 0xb0dc90: r0 = _getValueOrData()
    //     0xb0dc90: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb0dc94: mov             x1, x0
    // 0xb0dc98: ldur            x0, [fp, #-0x18]
    // 0xb0dc9c: LoadField: r2 = r0->field_f
    //     0xb0dc9c: ldur            w2, [x0, #0xf]
    // 0xb0dca0: DecompressPointer r2
    //     0xb0dca0: add             x2, x2, HEAP, lsl #32
    // 0xb0dca4: cmp             w2, w1
    // 0xb0dca8: b.eq            #0xb0dccc
    // 0xb0dcac: cmp             w1, NULL
    // 0xb0dcb0: b.eq            #0xb0dccc
    // 0xb0dcb4: ldur            x1, [fp, #-8]
    // 0xb0dcb8: ldur            x2, [fp, #-0x10]
    // 0xb0dcbc: r0 = _buildCachedImage()
    //     0xb0dcbc: bl              #0xb0ddd0  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCachedImage
    // 0xb0dcc0: LeaveFrame
    //     0xb0dcc0: mov             SP, fp
    //     0xb0dcc4: ldp             fp, lr, [SP], #0x10
    // 0xb0dcc8: ret
    //     0xb0dcc8: ret             
    // 0xb0dccc: r0 = LoadStaticField(0x878)
    //     0xb0dccc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb0dcd0: ldr             x0, [x0, #0x10f0]
    // 0xb0dcd4: cmp             w0, NULL
    // 0xb0dcd8: b.eq            #0xb0ddcc
    // 0xb0dcdc: LoadField: r3 = r0->field_53
    //     0xb0dcdc: ldur            w3, [x0, #0x53]
    // 0xb0dce0: DecompressPointer r3
    //     0xb0dce0: add             x3, x3, HEAP, lsl #32
    // 0xb0dce4: stur            x3, [fp, #-0x18]
    // 0xb0dce8: LoadField: r0 = r3->field_7
    //     0xb0dce8: ldur            w0, [x3, #7]
    // 0xb0dcec: DecompressPointer r0
    //     0xb0dcec: add             x0, x0, HEAP, lsl #32
    // 0xb0dcf0: ldur            x2, [fp, #-0x20]
    // 0xb0dcf4: stur            x0, [fp, #-0x10]
    // 0xb0dcf8: r1 = Function '<anonymous closure>':.
    //     0xb0dcf8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57cb8] AnonymousClosure: (0xb0ded4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildNetworkImage (0xb0dc24)
    //     0xb0dcfc: ldr             x1, [x1, #0xcb8]
    // 0xb0dd00: r0 = AllocateClosure()
    //     0xb0dd00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0dd04: ldur            x2, [fp, #-0x10]
    // 0xb0dd08: mov             x3, x0
    // 0xb0dd0c: r1 = Null
    //     0xb0dd0c: mov             x1, NULL
    // 0xb0dd10: stur            x3, [fp, #-0x10]
    // 0xb0dd14: cmp             w2, NULL
    // 0xb0dd18: b.eq            #0xb0dd38
    // 0xb0dd1c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb0dd1c: ldur            w4, [x2, #0x17]
    // 0xb0dd20: DecompressPointer r4
    //     0xb0dd20: add             x4, x4, HEAP, lsl #32
    // 0xb0dd24: r8 = X0
    //     0xb0dd24: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xb0dd28: LoadField: r9 = r4->field_7
    //     0xb0dd28: ldur            x9, [x4, #7]
    // 0xb0dd2c: r3 = Null
    //     0xb0dd2c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57cc0] Null
    //     0xb0dd30: ldr             x3, [x3, #0xcc0]
    // 0xb0dd34: blr             x9
    // 0xb0dd38: ldur            x0, [fp, #-0x18]
    // 0xb0dd3c: LoadField: r1 = r0->field_b
    //     0xb0dd3c: ldur            w1, [x0, #0xb]
    // 0xb0dd40: LoadField: r2 = r0->field_f
    //     0xb0dd40: ldur            w2, [x0, #0xf]
    // 0xb0dd44: DecompressPointer r2
    //     0xb0dd44: add             x2, x2, HEAP, lsl #32
    // 0xb0dd48: LoadField: r3 = r2->field_b
    //     0xb0dd48: ldur            w3, [x2, #0xb]
    // 0xb0dd4c: r2 = LoadInt32Instr(r1)
    //     0xb0dd4c: sbfx            x2, x1, #1, #0x1f
    // 0xb0dd50: stur            x2, [fp, #-0x28]
    // 0xb0dd54: r1 = LoadInt32Instr(r3)
    //     0xb0dd54: sbfx            x1, x3, #1, #0x1f
    // 0xb0dd58: cmp             x2, x1
    // 0xb0dd5c: b.ne            #0xb0dd68
    // 0xb0dd60: mov             x1, x0
    // 0xb0dd64: r0 = _growToNextCapacity()
    //     0xb0dd64: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0dd68: ldur            x0, [fp, #-0x18]
    // 0xb0dd6c: ldur            x2, [fp, #-0x28]
    // 0xb0dd70: add             x1, x2, #1
    // 0xb0dd74: lsl             x3, x1, #1
    // 0xb0dd78: StoreField: r0->field_b = r3
    //     0xb0dd78: stur            w3, [x0, #0xb]
    // 0xb0dd7c: LoadField: r1 = r0->field_f
    //     0xb0dd7c: ldur            w1, [x0, #0xf]
    // 0xb0dd80: DecompressPointer r1
    //     0xb0dd80: add             x1, x1, HEAP, lsl #32
    // 0xb0dd84: ldur            x0, [fp, #-0x10]
    // 0xb0dd88: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0dd88: add             x25, x1, x2, lsl #2
    //     0xb0dd8c: add             x25, x25, #0xf
    //     0xb0dd90: str             w0, [x25]
    //     0xb0dd94: tbz             w0, #0, #0xb0ddb0
    //     0xb0dd98: ldurb           w16, [x1, #-1]
    //     0xb0dd9c: ldurb           w17, [x0, #-1]
    //     0xb0dda0: and             x16, x17, x16, lsr #2
    //     0xb0dda4: tst             x16, HEAP, lsr #32
    //     0xb0dda8: b.eq            #0xb0ddb0
    //     0xb0ddac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0ddb0: ldur            x1, [fp, #-8]
    // 0xb0ddb4: r0 = _buildProgressIndicator()
    //     0xb0ddb4: bl              #0xa5a164  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildProgressIndicator
    // 0xb0ddb8: LeaveFrame
    //     0xb0ddb8: mov             SP, fp
    //     0xb0ddbc: ldp             fp, lr, [SP], #0x10
    // 0xb0ddc0: ret
    //     0xb0ddc0: ret             
    // 0xb0ddc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0ddc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0ddc8: b               #0xb0dc44
    // 0xb0ddcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ddcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCachedImage(/* No info */) {
    // ** addr: 0xb0ddd0, size: 0xc0
    // 0xb0ddd0: EnterFrame
    //     0xb0ddd0: stp             fp, lr, [SP, #-0x10]!
    //     0xb0ddd4: mov             fp, SP
    // 0xb0ddd8: AllocStack(0x38)
    //     0xb0ddd8: sub             SP, SP, #0x38
    // 0xb0dddc: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb0dddc: stur            x1, [fp, #-8]
    //     0xb0dde0: stur            x2, [fp, #-0x10]
    // 0xb0dde4: CheckStackOverflow
    //     0xb0dde4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0dde8: cmp             SP, x16
    //     0xb0ddec: b.ls            #0xb0de88
    // 0xb0ddf0: r1 = 1
    //     0xb0ddf0: movz            x1, #0x1
    // 0xb0ddf4: r0 = AllocateContext()
    //     0xb0ddf4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0ddf8: mov             x1, x0
    // 0xb0ddfc: ldur            x0, [fp, #-8]
    // 0xb0de00: stur            x1, [fp, #-0x18]
    // 0xb0de04: StoreField: r1->field_f = r0
    //     0xb0de04: stur            w0, [x1, #0xf]
    // 0xb0de08: r0 = CachedNetworkImage()
    //     0xb0de08: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb0de0c: ldur            x2, [fp, #-0x18]
    // 0xb0de10: r1 = Function '<anonymous closure>':.
    //     0xb0de10: add             x1, PP, #0x57, lsl #12  ; [pp+0x57cd8] AnonymousClosure: (0xb0de90), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCachedImage (0xb0ddd0)
    //     0xb0de14: ldr             x1, [x1, #0xcd8]
    // 0xb0de18: stur            x0, [fp, #-8]
    // 0xb0de1c: r0 = AllocateClosure()
    //     0xb0de1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0de20: r1 = Function '<anonymous closure>':.
    //     0xb0de20: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ce0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb0de24: ldr             x1, [x1, #0xce0]
    // 0xb0de28: r2 = Null
    //     0xb0de28: mov             x2, NULL
    // 0xb0de2c: stur            x0, [fp, #-0x18]
    // 0xb0de30: r0 = AllocateClosure()
    //     0xb0de30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0de34: r16 = inf
    //     0xb0de34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb0de38: ldr             x16, [x16, #0x9f8]
    // 0xb0de3c: r30 = Instance_BoxFit
    //     0xb0de3c: add             lr, PP, #0x2d, lsl #12  ; [pp+0x2d7d0] Obj!BoxFit@d73881
    //     0xb0de40: ldr             lr, [lr, #0x7d0]
    // 0xb0de44: stp             lr, x16, [SP, #0x10]
    // 0xb0de48: ldur            x16, [fp, #-0x18]
    // 0xb0de4c: stp             x0, x16, [SP]
    // 0xb0de50: ldur            x1, [fp, #-8]
    // 0xb0de54: ldur            x2, [fp, #-0x10]
    // 0xb0de58: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, placeholder, 0x4, width, 0x2, null]
    //     0xb0de58: add             x4, PP, #0x55, lsl #12  ; [pp+0x55708] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "placeholder", 0x4, "width", 0x2, Null]
    //     0xb0de5c: ldr             x4, [x4, #0x708]
    // 0xb0de60: r0 = CachedNetworkImage()
    //     0xb0de60: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb0de64: r0 = Center()
    //     0xb0de64: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb0de68: r1 = Instance_Alignment
    //     0xb0de68: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb0de6c: ldr             x1, [x1, #0xb10]
    // 0xb0de70: StoreField: r0->field_f = r1
    //     0xb0de70: stur            w1, [x0, #0xf]
    // 0xb0de74: ldur            x1, [fp, #-8]
    // 0xb0de78: StoreField: r0->field_b = r1
    //     0xb0de78: stur            w1, [x0, #0xb]
    // 0xb0de7c: LeaveFrame
    //     0xb0de7c: mov             SP, fp
    //     0xb0de80: ldp             fp, lr, [SP], #0x10
    // 0xb0de84: ret
    //     0xb0de84: ret             
    // 0xb0de88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0de88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0de8c: b               #0xb0ddf0
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, String) {
    // ** addr: 0xb0de90, size: 0x44
    // 0xb0de90: EnterFrame
    //     0xb0de90: stp             fp, lr, [SP, #-0x10]!
    //     0xb0de94: mov             fp, SP
    // 0xb0de98: ldr             x0, [fp, #0x20]
    // 0xb0de9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb0de9c: ldur            w1, [x0, #0x17]
    // 0xb0dea0: DecompressPointer r1
    //     0xb0dea0: add             x1, x1, HEAP, lsl #32
    // 0xb0dea4: CheckStackOverflow
    //     0xb0dea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0dea8: cmp             SP, x16
    //     0xb0deac: b.ls            #0xb0decc
    // 0xb0deb0: LoadField: r0 = r1->field_f
    //     0xb0deb0: ldur            w0, [x1, #0xf]
    // 0xb0deb4: DecompressPointer r0
    //     0xb0deb4: add             x0, x0, HEAP, lsl #32
    // 0xb0deb8: mov             x1, x0
    // 0xb0debc: r0 = _buildProgressIndicator()
    //     0xb0debc: bl              #0xa5a164  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildProgressIndicator
    // 0xb0dec0: LeaveFrame
    //     0xb0dec0: mov             SP, fp
    //     0xb0dec4: ldp             fp, lr, [SP], #0x10
    // 0xb0dec8: ret
    //     0xb0dec8: ret             
    // 0xb0decc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0decc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0ded0: b               #0xb0deb0
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xb0ded4, size: 0x64
    // 0xb0ded4: EnterFrame
    //     0xb0ded4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0ded8: mov             fp, SP
    // 0xb0dedc: ldr             x0, [fp, #0x18]
    // 0xb0dee0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb0dee0: ldur            w1, [x0, #0x17]
    // 0xb0dee4: DecompressPointer r1
    //     0xb0dee4: add             x1, x1, HEAP, lsl #32
    // 0xb0dee8: CheckStackOverflow
    //     0xb0dee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0deec: cmp             SP, x16
    //     0xb0def0: b.ls            #0xb0df30
    // 0xb0def4: LoadField: r0 = r1->field_f
    //     0xb0def4: ldur            w0, [x1, #0xf]
    // 0xb0def8: DecompressPointer r0
    //     0xb0def8: add             x0, x0, HEAP, lsl #32
    // 0xb0defc: LoadField: r1 = r0->field_2b
    //     0xb0defc: ldur            w1, [x0, #0x2b]
    // 0xb0df00: DecompressPointer r1
    //     0xb0df00: add             x1, x1, HEAP, lsl #32
    // 0xb0df04: tbz             w1, #4, #0xb0df20
    // 0xb0df08: LoadField: r1 = r0->field_f
    //     0xb0df08: ldur            w1, [x0, #0xf]
    // 0xb0df0c: DecompressPointer r1
    //     0xb0df0c: add             x1, x1, HEAP, lsl #32
    // 0xb0df10: cmp             w1, NULL
    // 0xb0df14: b.eq            #0xb0df20
    // 0xb0df18: mov             x1, x0
    // 0xb0df1c: r0 = _preloadImageSizes()
    //     0xb0df1c: bl              #0x803b0c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0xb0df20: r0 = Null
    //     0xb0df20: mov             x0, NULL
    // 0xb0df24: LeaveFrame
    //     0xb0df24: mov             SP, fp
    //     0xb0df28: ldp             fp, lr, [SP], #0x10
    // 0xb0df2c: ret
    //     0xb0df2c: ret             
    // 0xb0df30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0df30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0df34: b               #0xb0def4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0df38, size: 0x90
    // 0xb0df38: EnterFrame
    //     0xb0df38: stp             fp, lr, [SP, #-0x10]!
    //     0xb0df3c: mov             fp, SP
    // 0xb0df40: AllocStack(0x18)
    //     0xb0df40: sub             SP, SP, #0x18
    // 0xb0df44: SetupParameters()
    //     0xb0df44: ldr             x0, [fp, #0x10]
    //     0xb0df48: ldur            w1, [x0, #0x17]
    //     0xb0df4c: add             x1, x1, HEAP, lsl #32
    // 0xb0df50: CheckStackOverflow
    //     0xb0df50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0df54: cmp             SP, x16
    //     0xb0df58: b.ls            #0xb0dfbc
    // 0xb0df5c: LoadField: r0 = r1->field_f
    //     0xb0df5c: ldur            w0, [x1, #0xf]
    // 0xb0df60: DecompressPointer r0
    //     0xb0df60: add             x0, x0, HEAP, lsl #32
    // 0xb0df64: LoadField: r2 = r0->field_b
    //     0xb0df64: ldur            w2, [x0, #0xb]
    // 0xb0df68: DecompressPointer r2
    //     0xb0df68: add             x2, x2, HEAP, lsl #32
    // 0xb0df6c: cmp             w2, NULL
    // 0xb0df70: b.eq            #0xb0dfc4
    // 0xb0df74: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb0df74: ldur            w0, [x1, #0x17]
    // 0xb0df78: DecompressPointer r0
    //     0xb0df78: add             x0, x0, HEAP, lsl #32
    // 0xb0df7c: LoadField: r3 = r1->field_13
    //     0xb0df7c: ldur            w3, [x1, #0x13]
    // 0xb0df80: DecompressPointer r3
    //     0xb0df80: add             x3, x3, HEAP, lsl #32
    // 0xb0df84: LoadField: r1 = r2->field_23
    //     0xb0df84: ldur            w1, [x2, #0x23]
    // 0xb0df88: DecompressPointer r1
    //     0xb0df88: add             x1, x1, HEAP, lsl #32
    // 0xb0df8c: stp             x0, x1, [SP, #8]
    // 0xb0df90: str             x3, [SP]
    // 0xb0df94: r4 = 0
    //     0xb0df94: movz            x4, #0
    // 0xb0df98: ldr             x0, [SP, #0x10]
    // 0xb0df9c: r16 = UnlinkedCall_0x613b5c
    //     0xb0df9c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ca0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0dfa0: add             x16, x16, #0xca0
    // 0xb0dfa4: ldp             x5, lr, [x16]
    // 0xb0dfa8: blr             lr
    // 0xb0dfac: r0 = Null
    //     0xb0dfac: mov             x0, NULL
    // 0xb0dfb0: LeaveFrame
    //     0xb0dfb0: mov             SP, fp
    //     0xb0dfb4: ldp             fp, lr, [SP], #0x10
    // 0xb0dfb8: ret
    //     0xb0dfb8: ret             
    // 0xb0dfbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0dfbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0dfc0: b               #0xb0df5c
    // 0xb0dfc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0dfc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb0dfd4, size: 0x84
    // 0xb0dfd4: EnterFrame
    //     0xb0dfd4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0dfd8: mov             fp, SP
    // 0xb0dfdc: AllocStack(0x10)
    //     0xb0dfdc: sub             SP, SP, #0x10
    // 0xb0dfe0: SetupParameters()
    //     0xb0dfe0: ldr             x0, [fp, #0x18]
    //     0xb0dfe4: ldur            w1, [x0, #0x17]
    //     0xb0dfe8: add             x1, x1, HEAP, lsl #32
    //     0xb0dfec: stur            x1, [fp, #-8]
    // 0xb0dff0: CheckStackOverflow
    //     0xb0dff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0dff4: cmp             SP, x16
    //     0xb0dff8: b.ls            #0xb0e050
    // 0xb0dffc: r1 = 1
    //     0xb0dffc: movz            x1, #0x1
    // 0xb0e000: r0 = AllocateContext()
    //     0xb0e000: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0e004: mov             x1, x0
    // 0xb0e008: ldur            x0, [fp, #-8]
    // 0xb0e00c: StoreField: r1->field_b = r0
    //     0xb0e00c: stur            w0, [x1, #0xb]
    // 0xb0e010: ldr             x2, [fp, #0x10]
    // 0xb0e014: StoreField: r1->field_f = r2
    //     0xb0e014: stur            w2, [x1, #0xf]
    // 0xb0e018: LoadField: r3 = r0->field_f
    //     0xb0e018: ldur            w3, [x0, #0xf]
    // 0xb0e01c: DecompressPointer r3
    //     0xb0e01c: add             x3, x3, HEAP, lsl #32
    // 0xb0e020: mov             x2, x1
    // 0xb0e024: stur            x3, [fp, #-0x10]
    // 0xb0e028: r1 = Function '<anonymous closure>':.
    //     0xb0e028: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ce8] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xb0e02c: ldr             x1, [x1, #0xce8]
    // 0xb0e030: r0 = AllocateClosure()
    //     0xb0e030: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0e034: ldur            x1, [fp, #-0x10]
    // 0xb0e038: mov             x2, x0
    // 0xb0e03c: r0 = setState()
    //     0xb0e03c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb0e040: r0 = Null
    //     0xb0e040: mov             x0, NULL
    // 0xb0e044: LeaveFrame
    //     0xb0e044: mov             SP, fp
    //     0xb0e048: ldp             fp, lr, [SP], #0x10
    // 0xb0e04c: ret
    //     0xb0e04c: ret             
    // 0xb0e050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0e050: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0e054: b               #0xb0dffc
  }
}

// class id: 4140, size: 0x28, field offset: 0xc
//   const constructor, 
class ProductMediaCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dfe0, size: 0x48
    // 0xc7dfe0: EnterFrame
    //     0xc7dfe0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dfe4: mov             fp, SP
    // 0xc7dfe8: AllocStack(0x8)
    //     0xc7dfe8: sub             SP, SP, #8
    // 0xc7dfec: CheckStackOverflow
    //     0xc7dfec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7dff0: cmp             SP, x16
    //     0xc7dff4: b.ls            #0xc7e020
    // 0xc7dff8: r1 = <ProductMediaCarousel>
    //     0xc7dff8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b10] TypeArguments: <ProductMediaCarousel>
    //     0xc7dffc: ldr             x1, [x1, #0xb10]
    // 0xc7e000: r0 = _ProductMediaCarouselState()
    //     0xc7e000: bl              #0xc7e028  ; Allocate_ProductMediaCarouselStateStub -> _ProductMediaCarouselState (size=0x34)
    // 0xc7e004: mov             x1, x0
    // 0xc7e008: stur            x0, [fp, #-8]
    // 0xc7e00c: r0 = _ProductMediaCarouselState()
    //     0xc7e00c: bl              #0xc7c270  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_ProductMediaCarouselState
    // 0xc7e010: ldur            x0, [fp, #-8]
    // 0xc7e014: LeaveFrame
    //     0xc7e014: mov             SP, fp
    //     0xc7e018: ldp             fp, lr, [SP], #0x10
    // 0xc7e01c: ret
    //     0xc7e01c: ret             
    // 0xc7e020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e020: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e024: b               #0xc7dff8
  }
}

// class id: 4861, size: 0x10, field offset: 0x10
class CustomPath extends CustomClipper<dynamic> {

  _ shouldReclip(/* No info */) {
    // ** addr: 0x161d614, size: 0x40
    // 0x161d614: EnterFrame
    //     0x161d614: stp             fp, lr, [SP, #-0x10]!
    //     0x161d618: mov             fp, SP
    // 0x161d61c: mov             x0, x2
    // 0x161d620: mov             x4, x1
    // 0x161d624: mov             x3, x2
    // 0x161d628: r2 = Null
    //     0x161d628: mov             x2, NULL
    // 0x161d62c: r1 = Null
    //     0x161d62c: mov             x1, NULL
    // 0x161d630: r8 = CustomClipper<Path>
    //     0x161d630: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4cc88] Type: CustomClipper<Path>
    //     0x161d634: ldr             x8, [x8, #0xc88]
    // 0x161d638: r3 = Null
    //     0x161d638: add             x3, PP, #0x61, lsl #12  ; [pp+0x61e88] Null
    //     0x161d63c: ldr             x3, [x3, #0xe88]
    // 0x161d640: r0 = CustomClipper<Path>()
    //     0x161d640: bl              #0x99065c  ; IsType_CustomClipper<Path>_Stub
    // 0x161d644: r0 = true
    //     0x161d644: add             x0, NULL, #0x20  ; true
    // 0x161d648: LeaveFrame
    //     0x161d648: mov             SP, fp
    //     0x161d64c: ldp             fp, lr, [SP], #0x10
    // 0x161d650: ret
    //     0x161d650: ret             
  }
}
