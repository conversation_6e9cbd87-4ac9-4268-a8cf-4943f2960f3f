// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart

// class id: 1049481, size: 0x8
class :: {
}

// class id: 3284, size: 0x14, field offset: 0x14
class _BagDetailWidgetState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x902d60, size: 0x70
    // 0x902d60: EnterFrame
    //     0x902d60: stp             fp, lr, [SP, #-0x10]!
    //     0x902d64: mov             fp, SP
    // 0x902d68: AllocStack(0x10)
    //     0x902d68: sub             SP, SP, #0x10
    // 0x902d6c: SetupParameters()
    //     0x902d6c: ldr             x0, [fp, #0x18]
    //     0x902d70: ldur            w1, [x0, #0x17]
    //     0x902d74: add             x1, x1, HEAP, lsl #32
    // 0x902d78: CheckStackOverflow
    //     0x902d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x902d7c: cmp             SP, x16
    //     0x902d80: b.ls            #0x902dc4
    // 0x902d84: LoadField: r0 = r1->field_f
    //     0x902d84: ldur            w0, [x1, #0xf]
    // 0x902d88: DecompressPointer r0
    //     0x902d88: add             x0, x0, HEAP, lsl #32
    // 0x902d8c: LoadField: r1 = r0->field_b
    //     0x902d8c: ldur            w1, [x0, #0xb]
    // 0x902d90: DecompressPointer r1
    //     0x902d90: add             x1, x1, HEAP, lsl #32
    // 0x902d94: cmp             w1, NULL
    // 0x902d98: b.eq            #0x902dcc
    // 0x902d9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x902d9c: ldur            w0, [x1, #0x17]
    // 0x902da0: DecompressPointer r0
    //     0x902da0: add             x0, x0, HEAP, lsl #32
    // 0x902da4: r16 = true
    //     0x902da4: add             x16, NULL, #0x20  ; true
    // 0x902da8: stp             x16, x0, [SP]
    // 0x902dac: ClosureCall
    //     0x902dac: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x902db0: ldur            x2, [x0, #0x1f]
    //     0x902db4: blr             x2
    // 0x902db8: LeaveFrame
    //     0x902db8: mov             SP, fp
    //     0x902dbc: ldp             fp, lr, [SP], #0x10
    // 0x902dc0: ret
    //     0x902dc0: ret             
    // 0x902dc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x902dc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x902dc8: b               #0x902d84
    // 0x902dcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x902dcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x945948, size: 0x130
    // 0x945948: EnterFrame
    //     0x945948: stp             fp, lr, [SP, #-0x10]!
    //     0x94594c: mov             fp, SP
    // 0x945950: AllocStack(0x18)
    //     0x945950: sub             SP, SP, #0x18
    // 0x945954: SetupParameters(_BagDetailWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x945954: stur            x1, [fp, #-8]
    // 0x945958: CheckStackOverflow
    //     0x945958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94595c: cmp             SP, x16
    //     0x945960: b.ls            #0x945a6c
    // 0x945964: r1 = 1
    //     0x945964: movz            x1, #0x1
    // 0x945968: r0 = AllocateContext()
    //     0x945968: bl              #0x16f6108  ; AllocateContextStub
    // 0x94596c: mov             x1, x0
    // 0x945970: ldur            x0, [fp, #-8]
    // 0x945974: StoreField: r1->field_f = r0
    //     0x945974: stur            w0, [x1, #0xf]
    // 0x945978: r0 = LoadStaticField(0x878)
    //     0x945978: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94597c: ldr             x0, [x0, #0x10f0]
    // 0x945980: cmp             w0, NULL
    // 0x945984: b.eq            #0x945a74
    // 0x945988: LoadField: r3 = r0->field_53
    //     0x945988: ldur            w3, [x0, #0x53]
    // 0x94598c: DecompressPointer r3
    //     0x94598c: add             x3, x3, HEAP, lsl #32
    // 0x945990: stur            x3, [fp, #-0x10]
    // 0x945994: LoadField: r0 = r3->field_7
    //     0x945994: ldur            w0, [x3, #7]
    // 0x945998: DecompressPointer r0
    //     0x945998: add             x0, x0, HEAP, lsl #32
    // 0x94599c: mov             x2, x1
    // 0x9459a0: stur            x0, [fp, #-8]
    // 0x9459a4: r1 = Function '<anonymous closure>':.
    //     0x9459a4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b10] AnonymousClosure: (0x902d60), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::initState (0x945948)
    //     0x9459a8: ldr             x1, [x1, #0xb10]
    // 0x9459ac: r0 = AllocateClosure()
    //     0x9459ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9459b0: ldur            x2, [fp, #-8]
    // 0x9459b4: mov             x3, x0
    // 0x9459b8: r1 = Null
    //     0x9459b8: mov             x1, NULL
    // 0x9459bc: stur            x3, [fp, #-8]
    // 0x9459c0: cmp             w2, NULL
    // 0x9459c4: b.eq            #0x9459e4
    // 0x9459c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9459c8: ldur            w4, [x2, #0x17]
    // 0x9459cc: DecompressPointer r4
    //     0x9459cc: add             x4, x4, HEAP, lsl #32
    // 0x9459d0: r8 = X0
    //     0x9459d0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x9459d4: LoadField: r9 = r4->field_7
    //     0x9459d4: ldur            x9, [x4, #7]
    // 0x9459d8: r3 = Null
    //     0x9459d8: add             x3, PP, #0x54, lsl #12  ; [pp+0x54b18] Null
    //     0x9459dc: ldr             x3, [x3, #0xb18]
    // 0x9459e0: blr             x9
    // 0x9459e4: ldur            x0, [fp, #-0x10]
    // 0x9459e8: LoadField: r1 = r0->field_b
    //     0x9459e8: ldur            w1, [x0, #0xb]
    // 0x9459ec: LoadField: r2 = r0->field_f
    //     0x9459ec: ldur            w2, [x0, #0xf]
    // 0x9459f0: DecompressPointer r2
    //     0x9459f0: add             x2, x2, HEAP, lsl #32
    // 0x9459f4: LoadField: r3 = r2->field_b
    //     0x9459f4: ldur            w3, [x2, #0xb]
    // 0x9459f8: r2 = LoadInt32Instr(r1)
    //     0x9459f8: sbfx            x2, x1, #1, #0x1f
    // 0x9459fc: stur            x2, [fp, #-0x18]
    // 0x945a00: r1 = LoadInt32Instr(r3)
    //     0x945a00: sbfx            x1, x3, #1, #0x1f
    // 0x945a04: cmp             x2, x1
    // 0x945a08: b.ne            #0x945a14
    // 0x945a0c: mov             x1, x0
    // 0x945a10: r0 = _growToNextCapacity()
    //     0x945a10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945a14: ldur            x2, [fp, #-0x10]
    // 0x945a18: ldur            x3, [fp, #-0x18]
    // 0x945a1c: add             x4, x3, #1
    // 0x945a20: lsl             x5, x4, #1
    // 0x945a24: StoreField: r2->field_b = r5
    //     0x945a24: stur            w5, [x2, #0xb]
    // 0x945a28: LoadField: r1 = r2->field_f
    //     0x945a28: ldur            w1, [x2, #0xf]
    // 0x945a2c: DecompressPointer r1
    //     0x945a2c: add             x1, x1, HEAP, lsl #32
    // 0x945a30: ldur            x0, [fp, #-8]
    // 0x945a34: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945a34: add             x25, x1, x3, lsl #2
    //     0x945a38: add             x25, x25, #0xf
    //     0x945a3c: str             w0, [x25]
    //     0x945a40: tbz             w0, #0, #0x945a5c
    //     0x945a44: ldurb           w16, [x1, #-1]
    //     0x945a48: ldurb           w17, [x0, #-1]
    //     0x945a4c: and             x16, x17, x16, lsr #2
    //     0x945a50: tst             x16, HEAP, lsr #32
    //     0x945a54: b.eq            #0x945a5c
    //     0x945a58: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945a5c: r0 = Null
    //     0x945a5c: mov             x0, NULL
    // 0x945a60: LeaveFrame
    //     0x945a60: mov             SP, fp
    //     0x945a64: ldp             fp, lr, [SP], #0x10
    // 0x945a68: ret
    //     0x945a68: ret             
    // 0x945a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945a6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945a70: b               #0x945964
    // 0x945a74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945a74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9fbd20, size: 0xc
    // 0x9fbd20: r0 = Instance_Padding
    //     0x9fbd20: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a90] Obj!Padding@d68461
    //     0x9fbd24: ldr             x0, [x0, #0xa90]
    // 0x9fbd28: ret
    //     0x9fbd28: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xbaa86c, size: 0x1588
    // 0xbaa86c: EnterFrame
    //     0xbaa86c: stp             fp, lr, [SP, #-0x10]!
    //     0xbaa870: mov             fp, SP
    // 0xbaa874: AllocStack(0x98)
    //     0xbaa874: sub             SP, SP, #0x98
    // 0xbaa878: SetupParameters(_BagDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbaa878: mov             x0, x1
    //     0xbaa87c: stur            x1, [fp, #-8]
    //     0xbaa880: mov             x1, x2
    //     0xbaa884: stur            x2, [fp, #-0x10]
    // 0xbaa888: CheckStackOverflow
    //     0xbaa888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaa88c: cmp             SP, x16
    //     0xbaa890: b.ls            #0xbabdcc
    // 0xbaa894: r1 = 1
    //     0xbaa894: movz            x1, #0x1
    // 0xbaa898: r0 = AllocateContext()
    //     0xbaa898: bl              #0x16f6108  ; AllocateContextStub
    // 0xbaa89c: mov             x2, x0
    // 0xbaa8a0: ldur            x0, [fp, #-8]
    // 0xbaa8a4: stur            x2, [fp, #-0x18]
    // 0xbaa8a8: StoreField: r2->field_f = r0
    //     0xbaa8a8: stur            w0, [x2, #0xf]
    // 0xbaa8ac: ldur            x1, [fp, #-0x10]
    // 0xbaa8b0: r0 = of()
    //     0xbaa8b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaa8b4: LoadField: r1 = r0->field_87
    //     0xbaa8b4: ldur            w1, [x0, #0x87]
    // 0xbaa8b8: DecompressPointer r1
    //     0xbaa8b8: add             x1, x1, HEAP, lsl #32
    // 0xbaa8bc: LoadField: r0 = r1->field_7
    //     0xbaa8bc: ldur            w0, [x1, #7]
    // 0xbaa8c0: DecompressPointer r0
    //     0xbaa8c0: add             x0, x0, HEAP, lsl #32
    // 0xbaa8c4: stur            x0, [fp, #-0x20]
    // 0xbaa8c8: r1 = Instance_Color
    //     0xbaa8c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaa8cc: d0 = 0.700000
    //     0xbaa8cc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbaa8d0: ldr             d0, [x17, #0xf48]
    // 0xbaa8d4: r0 = withOpacity()
    //     0xbaa8d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaa8d8: r16 = 14.000000
    //     0xbaa8d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbaa8dc: ldr             x16, [x16, #0x1d8]
    // 0xbaa8e0: stp             x0, x16, [SP]
    // 0xbaa8e4: ldur            x1, [fp, #-0x20]
    // 0xbaa8e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaa8e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaa8ec: ldr             x4, [x4, #0xaa0]
    // 0xbaa8f0: r0 = copyWith()
    //     0xbaa8f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaa8f4: stur            x0, [fp, #-0x20]
    // 0xbaa8f8: r0 = Text()
    //     0xbaa8f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbaa8fc: mov             x1, x0
    // 0xbaa900: r0 = "Bag"
    //     0xbaa900: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0xbaa904: ldr             x0, [x0, #0xd60]
    // 0xbaa908: stur            x1, [fp, #-0x28]
    // 0xbaa90c: StoreField: r1->field_b = r0
    //     0xbaa90c: stur            w0, [x1, #0xb]
    // 0xbaa910: ldur            x0, [fp, #-0x20]
    // 0xbaa914: StoreField: r1->field_13 = r0
    //     0xbaa914: stur            w0, [x1, #0x13]
    // 0xbaa918: r0 = Padding()
    //     0xbaa918: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbaa91c: mov             x2, x0
    // 0xbaa920: r0 = Instance_EdgeInsets
    //     0xbaa920: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c50] Obj!EdgeInsets@d59541
    //     0xbaa924: ldr             x0, [x0, #0xc50]
    // 0xbaa928: stur            x2, [fp, #-0x30]
    // 0xbaa92c: StoreField: r2->field_f = r0
    //     0xbaa92c: stur            w0, [x2, #0xf]
    // 0xbaa930: ldur            x0, [fp, #-0x28]
    // 0xbaa934: StoreField: r2->field_b = r0
    //     0xbaa934: stur            w0, [x2, #0xb]
    // 0xbaa938: ldur            x0, [fp, #-8]
    // 0xbaa93c: LoadField: r1 = r0->field_b
    //     0xbaa93c: ldur            w1, [x0, #0xb]
    // 0xbaa940: DecompressPointer r1
    //     0xbaa940: add             x1, x1, HEAP, lsl #32
    // 0xbaa944: cmp             w1, NULL
    // 0xbaa948: b.eq            #0xbabdd4
    // 0xbaa94c: LoadField: r3 = r1->field_b
    //     0xbaa94c: ldur            w3, [x1, #0xb]
    // 0xbaa950: DecompressPointer r3
    //     0xbaa950: add             x3, x3, HEAP, lsl #32
    // 0xbaa954: LoadField: r1 = r3->field_b
    //     0xbaa954: ldur            w1, [x3, #0xb]
    // 0xbaa958: DecompressPointer r1
    //     0xbaa958: add             x1, x1, HEAP, lsl #32
    // 0xbaa95c: cmp             w1, NULL
    // 0xbaa960: b.ne            #0xbaa96c
    // 0xbaa964: r3 = Null
    //     0xbaa964: mov             x3, NULL
    // 0xbaa968: b               #0xbaa974
    // 0xbaa96c: LoadField: r3 = r1->field_43
    //     0xbaa96c: ldur            w3, [x1, #0x43]
    // 0xbaa970: DecompressPointer r3
    //     0xbaa970: add             x3, x3, HEAP, lsl #32
    // 0xbaa974: cmp             w3, NULL
    // 0xbaa978: r16 = true
    //     0xbaa978: add             x16, NULL, #0x20  ; true
    // 0xbaa97c: r17 = false
    //     0xbaa97c: add             x17, NULL, #0x30  ; false
    // 0xbaa980: csel            x4, x16, x17, ne
    // 0xbaa984: stur            x4, [fp, #-0x20]
    // 0xbaa988: cmp             w1, NULL
    // 0xbaa98c: b.ne            #0xbaa998
    // 0xbaa990: r1 = Null
    //     0xbaa990: mov             x1, NULL
    // 0xbaa994: b               #0xbaa9b8
    // 0xbaa998: LoadField: r3 = r1->field_43
    //     0xbaa998: ldur            w3, [x1, #0x43]
    // 0xbaa99c: DecompressPointer r3
    //     0xbaa99c: add             x3, x3, HEAP, lsl #32
    // 0xbaa9a0: cmp             w3, NULL
    // 0xbaa9a4: b.ne            #0xbaa9b0
    // 0xbaa9a8: r1 = Null
    //     0xbaa9a8: mov             x1, NULL
    // 0xbaa9ac: b               #0xbaa9b8
    // 0xbaa9b0: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xbaa9b0: ldur            w1, [x3, #0x17]
    // 0xbaa9b4: DecompressPointer r1
    //     0xbaa9b4: add             x1, x1, HEAP, lsl #32
    // 0xbaa9b8: cmp             w1, NULL
    // 0xbaa9bc: b.ne            #0xbaaa38
    // 0xbaa9c0: mov             x24, x0
    // 0xbaa9c4: r11 = true
    //     0xbaa9c4: add             x11, NULL, #0x20  ; true
    // 0xbaa9c8: r4 = 2
    //     0xbaa9c8: movz            x4, #0x2
    // 0xbaa9cc: r6 = "Free"
    //     0xbaa9cc: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbaa9d0: ldr             x6, [x6, #0x668]
    // 0xbaa9d4: r5 = 150.000000
    //     0xbaa9d4: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbaa9d8: ldr             x5, [x5, #0x690]
    // 0xbaa9dc: r3 = Instance_TextOverflow
    //     0xbaa9dc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbaa9e0: ldr             x3, [x3, #0xe10]
    // 0xbaa9e4: r7 = Instance_EdgeInsets
    //     0xbaa9e4: add             x7, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbaa9e8: ldr             x7, [x7, #0xa78]
    // 0xbaa9ec: r8 = Instance_CrossAxisAlignment
    //     0xbaa9ec: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbaa9f0: ldr             x8, [x8, #0xa18]
    // 0xbaa9f4: r13 = Instance_EdgeInsets
    //     0xbaa9f4: add             x13, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xbaa9f8: ldr             x13, [x13, #0xa40]
    // 0xbaa9fc: r20 = Instance_MainAxisAlignment
    //     0xbaa9fc: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbaaa00: ldr             x20, [x20, #0xa8]
    // 0xbaaa04: r23 = Instance_EdgeInsets
    //     0xbaaa04: add             x23, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbaaa08: ldr             x23, [x23, #0x778]
    // 0xbaaa0c: r14 = 4
    //     0xbaaa0c: movz            x14, #0x4
    // 0xbaaa10: r19 = Instance_Axis
    //     0xbaaa10: ldr             x19, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbaaa14: r10 = Instance_FlexFit
    //     0xbaaa14: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbaaa18: ldr             x10, [x10, #0xe08]
    // 0xbaaa1c: r12 = Instance_BoxShape
    //     0xbaaa1c: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbaaa20: ldr             x12, [x12, #0x80]
    // 0xbaaa24: r0 = Instance_Alignment
    //     0xbaaa24: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbaaa28: ldr             x0, [x0, #0xb10]
    // 0xbaaa2c: r2 = -1
    //     0xbaaa2c: movn            x2, #0
    // 0xbaaa30: r9 = 1
    //     0xbaaa30: movz            x9, #0x1
    // 0xbaaa34: b               #0xbab308
    // 0xbaaa38: tbnz            w1, #4, #0xbab294
    // 0xbaaa3c: r1 = Instance_Color
    //     0xbaaa3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaaa40: d0 = 0.070000
    //     0xbaaa40: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbaaa44: ldr             d0, [x17, #0x5f8]
    // 0xbaaa48: r0 = withOpacity()
    //     0xbaaa48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbaaa4c: r16 = 1.000000
    //     0xbaaa4c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbaaa50: str             x16, [SP]
    // 0xbaaa54: mov             x2, x0
    // 0xbaaa58: r1 = Null
    //     0xbaaa58: mov             x1, NULL
    // 0xbaaa5c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbaaa5c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbaaa60: ldr             x4, [x4, #0x108]
    // 0xbaaa64: r0 = Border.all()
    //     0xbaaa64: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbaaa68: stur            x0, [fp, #-0x28]
    // 0xbaaa6c: r0 = BoxDecoration()
    //     0xbaaa6c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbaaa70: mov             x2, x0
    // 0xbaaa74: ldur            x0, [fp, #-0x28]
    // 0xbaaa78: stur            x2, [fp, #-0x38]
    // 0xbaaa7c: StoreField: r2->field_f = r0
    //     0xbaaa7c: stur            w0, [x2, #0xf]
    // 0xbaaa80: r0 = Instance_LinearGradient
    //     0xbaaa80: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xbaaa84: ldr             x0, [x0, #0x660]
    // 0xbaaa88: StoreField: r2->field_1b = r0
    //     0xbaaa88: stur            w0, [x2, #0x1b]
    // 0xbaaa8c: r0 = Instance_BoxShape
    //     0xbaaa8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbaaa90: ldr             x0, [x0, #0x80]
    // 0xbaaa94: StoreField: r2->field_23 = r0
    //     0xbaaa94: stur            w0, [x2, #0x23]
    // 0xbaaa98: ldur            x1, [fp, #-0x10]
    // 0xbaaa9c: r0 = of()
    //     0xbaaa9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaaaa0: LoadField: r1 = r0->field_87
    //     0xbaaaa0: ldur            w1, [x0, #0x87]
    // 0xbaaaa4: DecompressPointer r1
    //     0xbaaaa4: add             x1, x1, HEAP, lsl #32
    // 0xbaaaa8: LoadField: r0 = r1->field_7
    //     0xbaaaa8: ldur            w0, [x1, #7]
    // 0xbaaaac: DecompressPointer r0
    //     0xbaaaac: add             x0, x0, HEAP, lsl #32
    // 0xbaaab0: r16 = 12.000000
    //     0xbaaab0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbaaab4: ldr             x16, [x16, #0x9e8]
    // 0xbaaab8: r30 = Instance_Color
    //     0xbaaab8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbaaabc: stp             lr, x16, [SP]
    // 0xbaaac0: mov             x1, x0
    // 0xbaaac4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaaac4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaaac8: ldr             x4, [x4, #0xaa0]
    // 0xbaaacc: r0 = copyWith()
    //     0xbaaacc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaaad0: stur            x0, [fp, #-0x28]
    // 0xbaaad4: r0 = Text()
    //     0xbaaad4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbaaad8: mov             x1, x0
    // 0xbaaadc: r0 = "Free"
    //     0xbaaadc: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbaaae0: ldr             x0, [x0, #0x668]
    // 0xbaaae4: stur            x1, [fp, #-0x40]
    // 0xbaaae8: StoreField: r1->field_b = r0
    //     0xbaaae8: stur            w0, [x1, #0xb]
    // 0xbaaaec: ldur            x2, [fp, #-0x28]
    // 0xbaaaf0: StoreField: r1->field_13 = r2
    //     0xbaaaf0: stur            w2, [x1, #0x13]
    // 0xbaaaf4: r0 = Center()
    //     0xbaaaf4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbaaaf8: mov             x1, x0
    // 0xbaaafc: r0 = Instance_Alignment
    //     0xbaaafc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbaab00: ldr             x0, [x0, #0xb10]
    // 0xbaab04: stur            x1, [fp, #-0x28]
    // 0xbaab08: StoreField: r1->field_f = r0
    //     0xbaab08: stur            w0, [x1, #0xf]
    // 0xbaab0c: ldur            x0, [fp, #-0x40]
    // 0xbaab10: StoreField: r1->field_b = r0
    //     0xbaab10: stur            w0, [x1, #0xb]
    // 0xbaab14: r0 = RotatedBox()
    //     0xbaab14: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbaab18: r2 = -1
    //     0xbaab18: movn            x2, #0
    // 0xbaab1c: stur            x0, [fp, #-0x40]
    // 0xbaab20: StoreField: r0->field_f = r2
    //     0xbaab20: stur            x2, [x0, #0xf]
    // 0xbaab24: ldur            x1, [fp, #-0x28]
    // 0xbaab28: StoreField: r0->field_b = r1
    //     0xbaab28: stur            w1, [x0, #0xb]
    // 0xbaab2c: r0 = Container()
    //     0xbaab2c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbaab30: stur            x0, [fp, #-0x28]
    // 0xbaab34: r16 = 24.000000
    //     0xbaab34: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbaab38: ldr             x16, [x16, #0xba8]
    // 0xbaab3c: r30 = 56.000000
    //     0xbaab3c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbaab40: ldr             lr, [lr, #0xb78]
    // 0xbaab44: stp             lr, x16, [SP, #0x10]
    // 0xbaab48: r16 = Instance_Color
    //     0xbaab48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaab4c: ldr             x16, [x16, #0x858]
    // 0xbaab50: ldur            lr, [fp, #-0x40]
    // 0xbaab54: stp             lr, x16, [SP]
    // 0xbaab58: mov             x1, x0
    // 0xbaab5c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbaab5c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbaab60: ldr             x4, [x4, #0x670]
    // 0xbaab64: r0 = Container()
    //     0xbaab64: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbaab68: ldur            x0, [fp, #-8]
    // 0xbaab6c: LoadField: r1 = r0->field_b
    //     0xbaab6c: ldur            w1, [x0, #0xb]
    // 0xbaab70: DecompressPointer r1
    //     0xbaab70: add             x1, x1, HEAP, lsl #32
    // 0xbaab74: cmp             w1, NULL
    // 0xbaab78: b.eq            #0xbabdd8
    // 0xbaab7c: LoadField: r2 = r1->field_b
    //     0xbaab7c: ldur            w2, [x1, #0xb]
    // 0xbaab80: DecompressPointer r2
    //     0xbaab80: add             x2, x2, HEAP, lsl #32
    // 0xbaab84: LoadField: r1 = r2->field_b
    //     0xbaab84: ldur            w1, [x2, #0xb]
    // 0xbaab88: DecompressPointer r1
    //     0xbaab88: add             x1, x1, HEAP, lsl #32
    // 0xbaab8c: cmp             w1, NULL
    // 0xbaab90: b.ne            #0xbaab9c
    // 0xbaab94: r1 = Null
    //     0xbaab94: mov             x1, NULL
    // 0xbaab98: b               #0xbaabbc
    // 0xbaab9c: LoadField: r2 = r1->field_43
    //     0xbaab9c: ldur            w2, [x1, #0x43]
    // 0xbaaba0: DecompressPointer r2
    //     0xbaaba0: add             x2, x2, HEAP, lsl #32
    // 0xbaaba4: cmp             w2, NULL
    // 0xbaaba8: b.ne            #0xbaabb4
    // 0xbaabac: r1 = Null
    //     0xbaabac: mov             x1, NULL
    // 0xbaabb0: b               #0xbaabbc
    // 0xbaabb4: LoadField: r1 = r2->field_7
    //     0xbaabb4: ldur            w1, [x2, #7]
    // 0xbaabb8: DecompressPointer r1
    //     0xbaabb8: add             x1, x1, HEAP, lsl #32
    // 0xbaabbc: cmp             w1, NULL
    // 0xbaabc0: b.ne            #0xbaabcc
    // 0xbaabc4: r3 = ""
    //     0xbaabc4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaabc8: b               #0xbaabd0
    // 0xbaabcc: mov             x3, x1
    // 0xbaabd0: stur            x3, [fp, #-0x40]
    // 0xbaabd4: r1 = Function '<anonymous closure>':.
    //     0xbaabd4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a48] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbaabd8: ldr             x1, [x1, #0xa48]
    // 0xbaabdc: r2 = Null
    //     0xbaabdc: mov             x2, NULL
    // 0xbaabe0: r0 = AllocateClosure()
    //     0xbaabe0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaabe4: r1 = Function '<anonymous closure>':.
    //     0xbaabe4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a50] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbaabe8: ldr             x1, [x1, #0xa50]
    // 0xbaabec: r2 = Null
    //     0xbaabec: mov             x2, NULL
    // 0xbaabf0: stur            x0, [fp, #-0x48]
    // 0xbaabf4: r0 = AllocateClosure()
    //     0xbaabf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaabf8: stur            x0, [fp, #-0x50]
    // 0xbaabfc: r0 = CachedNetworkImage()
    //     0xbaabfc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbaac00: stur            x0, [fp, #-0x58]
    // 0xbaac04: r16 = 56.000000
    //     0xbaac04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbaac08: ldr             x16, [x16, #0xb78]
    // 0xbaac0c: r30 = 56.000000
    //     0xbaac0c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbaac10: ldr             lr, [lr, #0xb78]
    // 0xbaac14: stp             lr, x16, [SP, #0x18]
    // 0xbaac18: r16 = Instance_BoxFit
    //     0xbaac18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbaac1c: ldr             x16, [x16, #0x118]
    // 0xbaac20: ldur            lr, [fp, #-0x48]
    // 0xbaac24: stp             lr, x16, [SP, #8]
    // 0xbaac28: ldur            x16, [fp, #-0x50]
    // 0xbaac2c: str             x16, [SP]
    // 0xbaac30: mov             x1, x0
    // 0xbaac34: ldur            x2, [fp, #-0x40]
    // 0xbaac38: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xbaac38: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xbaac3c: ldr             x4, [x4, #0x710]
    // 0xbaac40: r0 = CachedNetworkImage()
    //     0xbaac40: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbaac44: ldur            x0, [fp, #-8]
    // 0xbaac48: LoadField: r1 = r0->field_b
    //     0xbaac48: ldur            w1, [x0, #0xb]
    // 0xbaac4c: DecompressPointer r1
    //     0xbaac4c: add             x1, x1, HEAP, lsl #32
    // 0xbaac50: cmp             w1, NULL
    // 0xbaac54: b.eq            #0xbabddc
    // 0xbaac58: LoadField: r2 = r1->field_b
    //     0xbaac58: ldur            w2, [x1, #0xb]
    // 0xbaac5c: DecompressPointer r2
    //     0xbaac5c: add             x2, x2, HEAP, lsl #32
    // 0xbaac60: LoadField: r1 = r2->field_b
    //     0xbaac60: ldur            w1, [x2, #0xb]
    // 0xbaac64: DecompressPointer r1
    //     0xbaac64: add             x1, x1, HEAP, lsl #32
    // 0xbaac68: cmp             w1, NULL
    // 0xbaac6c: b.ne            #0xbaac78
    // 0xbaac70: r1 = Null
    //     0xbaac70: mov             x1, NULL
    // 0xbaac74: b               #0xbaac98
    // 0xbaac78: LoadField: r2 = r1->field_43
    //     0xbaac78: ldur            w2, [x1, #0x43]
    // 0xbaac7c: DecompressPointer r2
    //     0xbaac7c: add             x2, x2, HEAP, lsl #32
    // 0xbaac80: cmp             w2, NULL
    // 0xbaac84: b.ne            #0xbaac90
    // 0xbaac88: r1 = Null
    //     0xbaac88: mov             x1, NULL
    // 0xbaac8c: b               #0xbaac98
    // 0xbaac90: LoadField: r1 = r2->field_b
    //     0xbaac90: ldur            w1, [x2, #0xb]
    // 0xbaac94: DecompressPointer r1
    //     0xbaac94: add             x1, x1, HEAP, lsl #32
    // 0xbaac98: cmp             w1, NULL
    // 0xbaac9c: b.ne            #0xbaaca8
    // 0xbaaca0: r2 = ""
    //     0xbaaca0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaaca4: b               #0xbaacac
    // 0xbaaca8: mov             x2, x1
    // 0xbaacac: ldur            x1, [fp, #-0x10]
    // 0xbaacb0: stur            x2, [fp, #-0x40]
    // 0xbaacb4: r0 = of()
    //     0xbaacb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaacb8: LoadField: r1 = r0->field_87
    //     0xbaacb8: ldur            w1, [x0, #0x87]
    // 0xbaacbc: DecompressPointer r1
    //     0xbaacbc: add             x1, x1, HEAP, lsl #32
    // 0xbaacc0: LoadField: r0 = r1->field_7
    //     0xbaacc0: ldur            w0, [x1, #7]
    // 0xbaacc4: DecompressPointer r0
    //     0xbaacc4: add             x0, x0, HEAP, lsl #32
    // 0xbaacc8: r16 = 12.000000
    //     0xbaacc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbaaccc: ldr             x16, [x16, #0x9e8]
    // 0xbaacd0: r30 = Instance_Color
    //     0xbaacd0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbaacd4: stp             lr, x16, [SP]
    // 0xbaacd8: mov             x1, x0
    // 0xbaacdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaacdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaace0: ldr             x4, [x4, #0xaa0]
    // 0xbaace4: r0 = copyWith()
    //     0xbaace4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaace8: stur            x0, [fp, #-0x48]
    // 0xbaacec: r0 = Text()
    //     0xbaacec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbaacf0: mov             x1, x0
    // 0xbaacf4: ldur            x0, [fp, #-0x40]
    // 0xbaacf8: stur            x1, [fp, #-0x50]
    // 0xbaacfc: StoreField: r1->field_b = r0
    //     0xbaacfc: stur            w0, [x1, #0xb]
    // 0xbaad00: ldur            x0, [fp, #-0x48]
    // 0xbaad04: StoreField: r1->field_13 = r0
    //     0xbaad04: stur            w0, [x1, #0x13]
    // 0xbaad08: r3 = Instance_TextOverflow
    //     0xbaad08: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbaad0c: ldr             x3, [x3, #0xe10]
    // 0xbaad10: StoreField: r1->field_2b = r3
    //     0xbaad10: stur            w3, [x1, #0x2b]
    // 0xbaad14: r4 = 2
    //     0xbaad14: movz            x4, #0x2
    // 0xbaad18: StoreField: r1->field_37 = r4
    //     0xbaad18: stur            w4, [x1, #0x37]
    // 0xbaad1c: r0 = SizedBox()
    //     0xbaad1c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbaad20: r5 = 150.000000
    //     0xbaad20: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbaad24: ldr             x5, [x5, #0x690]
    // 0xbaad28: stur            x0, [fp, #-0x40]
    // 0xbaad2c: StoreField: r0->field_f = r5
    //     0xbaad2c: stur            w5, [x0, #0xf]
    // 0xbaad30: ldur            x1, [fp, #-0x50]
    // 0xbaad34: StoreField: r0->field_b = r1
    //     0xbaad34: stur            w1, [x0, #0xb]
    // 0xbaad38: ldur            x1, [fp, #-0x10]
    // 0xbaad3c: r0 = of()
    //     0xbaad3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaad40: LoadField: r1 = r0->field_87
    //     0xbaad40: ldur            w1, [x0, #0x87]
    // 0xbaad44: DecompressPointer r1
    //     0xbaad44: add             x1, x1, HEAP, lsl #32
    // 0xbaad48: LoadField: r0 = r1->field_2b
    //     0xbaad48: ldur            w0, [x1, #0x2b]
    // 0xbaad4c: DecompressPointer r0
    //     0xbaad4c: add             x0, x0, HEAP, lsl #32
    // 0xbaad50: r16 = 12.000000
    //     0xbaad50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbaad54: ldr             x16, [x16, #0x9e8]
    // 0xbaad58: r30 = Instance_Color
    //     0xbaad58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaad5c: ldr             lr, [lr, #0x858]
    // 0xbaad60: stp             lr, x16, [SP]
    // 0xbaad64: mov             x1, x0
    // 0xbaad68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaad68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaad6c: ldr             x4, [x4, #0xaa0]
    // 0xbaad70: r0 = copyWith()
    //     0xbaad70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaad74: stur            x0, [fp, #-0x48]
    // 0xbaad78: r0 = Text()
    //     0xbaad78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbaad7c: r6 = "Free"
    //     0xbaad7c: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbaad80: ldr             x6, [x6, #0x668]
    // 0xbaad84: stur            x0, [fp, #-0x50]
    // 0xbaad88: StoreField: r0->field_b = r6
    //     0xbaad88: stur            w6, [x0, #0xb]
    // 0xbaad8c: ldur            x1, [fp, #-0x48]
    // 0xbaad90: StoreField: r0->field_13 = r1
    //     0xbaad90: stur            w1, [x0, #0x13]
    // 0xbaad94: ldur            x2, [fp, #-8]
    // 0xbaad98: LoadField: r1 = r2->field_b
    //     0xbaad98: ldur            w1, [x2, #0xb]
    // 0xbaad9c: DecompressPointer r1
    //     0xbaad9c: add             x1, x1, HEAP, lsl #32
    // 0xbaada0: cmp             w1, NULL
    // 0xbaada4: b.eq            #0xbabde0
    // 0xbaada8: LoadField: r3 = r1->field_b
    //     0xbaada8: ldur            w3, [x1, #0xb]
    // 0xbaadac: DecompressPointer r3
    //     0xbaadac: add             x3, x3, HEAP, lsl #32
    // 0xbaadb0: LoadField: r1 = r3->field_b
    //     0xbaadb0: ldur            w1, [x3, #0xb]
    // 0xbaadb4: DecompressPointer r1
    //     0xbaadb4: add             x1, x1, HEAP, lsl #32
    // 0xbaadb8: cmp             w1, NULL
    // 0xbaadbc: b.ne            #0xbaadc8
    // 0xbaadc0: r1 = Null
    //     0xbaadc0: mov             x1, NULL
    // 0xbaadc4: b               #0xbaade8
    // 0xbaadc8: LoadField: r3 = r1->field_43
    //     0xbaadc8: ldur            w3, [x1, #0x43]
    // 0xbaadcc: DecompressPointer r3
    //     0xbaadcc: add             x3, x3, HEAP, lsl #32
    // 0xbaadd0: cmp             w3, NULL
    // 0xbaadd4: b.ne            #0xbaade0
    // 0xbaadd8: r1 = Null
    //     0xbaadd8: mov             x1, NULL
    // 0xbaaddc: b               #0xbaade8
    // 0xbaade0: LoadField: r1 = r3->field_13
    //     0xbaade0: ldur            w1, [x3, #0x13]
    // 0xbaade4: DecompressPointer r1
    //     0xbaade4: add             x1, x1, HEAP, lsl #32
    // 0xbaade8: cmp             w1, NULL
    // 0xbaadec: b.ne            #0xbaadf8
    // 0xbaadf0: r6 = ""
    //     0xbaadf0: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbaadf4: b               #0xbaadfc
    // 0xbaadf8: mov             x6, x1
    // 0xbaadfc: ldur            x5, [fp, #-0x28]
    // 0xbaae00: ldur            x4, [fp, #-0x58]
    // 0xbaae04: ldur            x3, [fp, #-0x40]
    // 0xbaae08: ldur            x1, [fp, #-0x10]
    // 0xbaae0c: stur            x6, [fp, #-0x48]
    // 0xbaae10: r0 = of()
    //     0xbaae10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaae14: LoadField: r1 = r0->field_87
    //     0xbaae14: ldur            w1, [x0, #0x87]
    // 0xbaae18: DecompressPointer r1
    //     0xbaae18: add             x1, x1, HEAP, lsl #32
    // 0xbaae1c: LoadField: r0 = r1->field_2b
    //     0xbaae1c: ldur            w0, [x1, #0x2b]
    // 0xbaae20: DecompressPointer r0
    //     0xbaae20: add             x0, x0, HEAP, lsl #32
    // 0xbaae24: r16 = 12.000000
    //     0xbaae24: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbaae28: ldr             x16, [x16, #0x9e8]
    // 0xbaae2c: r30 = Instance_TextDecoration
    //     0xbaae2c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbaae30: ldr             lr, [lr, #0xe30]
    // 0xbaae34: stp             lr, x16, [SP]
    // 0xbaae38: mov             x1, x0
    // 0xbaae3c: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbaae3c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbaae40: ldr             x4, [x4, #0x698]
    // 0xbaae44: r0 = copyWith()
    //     0xbaae44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaae48: stur            x0, [fp, #-0x60]
    // 0xbaae4c: r0 = Text()
    //     0xbaae4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbaae50: mov             x3, x0
    // 0xbaae54: ldur            x0, [fp, #-0x48]
    // 0xbaae58: stur            x3, [fp, #-0x68]
    // 0xbaae5c: StoreField: r3->field_b = r0
    //     0xbaae5c: stur            w0, [x3, #0xb]
    // 0xbaae60: ldur            x0, [fp, #-0x60]
    // 0xbaae64: StoreField: r3->field_13 = r0
    //     0xbaae64: stur            w0, [x3, #0x13]
    // 0xbaae68: r1 = Null
    //     0xbaae68: mov             x1, NULL
    // 0xbaae6c: r2 = 6
    //     0xbaae6c: movz            x2, #0x6
    // 0xbaae70: r0 = AllocateArray()
    //     0xbaae70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaae74: mov             x2, x0
    // 0xbaae78: ldur            x0, [fp, #-0x50]
    // 0xbaae7c: stur            x2, [fp, #-0x48]
    // 0xbaae80: StoreField: r2->field_f = r0
    //     0xbaae80: stur            w0, [x2, #0xf]
    // 0xbaae84: r16 = Instance_SizedBox
    //     0xbaae84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbaae88: ldr             x16, [x16, #0xa50]
    // 0xbaae8c: StoreField: r2->field_13 = r16
    //     0xbaae8c: stur            w16, [x2, #0x13]
    // 0xbaae90: ldur            x0, [fp, #-0x68]
    // 0xbaae94: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaae94: stur            w0, [x2, #0x17]
    // 0xbaae98: r1 = <Widget>
    //     0xbaae98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaae9c: r0 = AllocateGrowableArray()
    //     0xbaae9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaaea0: mov             x1, x0
    // 0xbaaea4: ldur            x0, [fp, #-0x48]
    // 0xbaaea8: stur            x1, [fp, #-0x50]
    // 0xbaaeac: StoreField: r1->field_f = r0
    //     0xbaaeac: stur            w0, [x1, #0xf]
    // 0xbaaeb0: r2 = 6
    //     0xbaaeb0: movz            x2, #0x6
    // 0xbaaeb4: StoreField: r1->field_b = r2
    //     0xbaaeb4: stur            w2, [x1, #0xb]
    // 0xbaaeb8: r0 = Row()
    //     0xbaaeb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbaaebc: mov             x3, x0
    // 0xbaaec0: r0 = Instance_Axis
    //     0xbaaec0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbaaec4: stur            x3, [fp, #-0x48]
    // 0xbaaec8: StoreField: r3->field_f = r0
    //     0xbaaec8: stur            w0, [x3, #0xf]
    // 0xbaaecc: r4 = Instance_MainAxisAlignment
    //     0xbaaecc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbaaed0: ldr             x4, [x4, #0xa08]
    // 0xbaaed4: StoreField: r3->field_13 = r4
    //     0xbaaed4: stur            w4, [x3, #0x13]
    // 0xbaaed8: r5 = Instance_MainAxisSize
    //     0xbaaed8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaaedc: ldr             x5, [x5, #0xa10]
    // 0xbaaee0: ArrayStore: r3[0] = r5  ; List_4
    //     0xbaaee0: stur            w5, [x3, #0x17]
    // 0xbaaee4: r6 = Instance_CrossAxisAlignment
    //     0xbaaee4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbaaee8: ldr             x6, [x6, #0xa18]
    // 0xbaaeec: StoreField: r3->field_1b = r6
    //     0xbaaeec: stur            w6, [x3, #0x1b]
    // 0xbaaef0: r7 = Instance_VerticalDirection
    //     0xbaaef0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaaef4: ldr             x7, [x7, #0xa20]
    // 0xbaaef8: StoreField: r3->field_23 = r7
    //     0xbaaef8: stur            w7, [x3, #0x23]
    // 0xbaaefc: r8 = Instance_Clip
    //     0xbaaefc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaaf00: ldr             x8, [x8, #0x38]
    // 0xbaaf04: StoreField: r3->field_2b = r8
    //     0xbaaf04: stur            w8, [x3, #0x2b]
    // 0xbaaf08: StoreField: r3->field_2f = rZR
    //     0xbaaf08: stur            xzr, [x3, #0x2f]
    // 0xbaaf0c: ldur            x1, [fp, #-0x50]
    // 0xbaaf10: StoreField: r3->field_b = r1
    //     0xbaaf10: stur            w1, [x3, #0xb]
    // 0xbaaf14: r1 = Null
    //     0xbaaf14: mov             x1, NULL
    // 0xbaaf18: r2 = 6
    //     0xbaaf18: movz            x2, #0x6
    // 0xbaaf1c: r0 = AllocateArray()
    //     0xbaaf1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaaf20: mov             x2, x0
    // 0xbaaf24: ldur            x0, [fp, #-0x40]
    // 0xbaaf28: stur            x2, [fp, #-0x50]
    // 0xbaaf2c: StoreField: r2->field_f = r0
    //     0xbaaf2c: stur            w0, [x2, #0xf]
    // 0xbaaf30: r16 = Instance_SizedBox
    //     0xbaaf30: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbaaf34: ldr             x16, [x16, #0xc70]
    // 0xbaaf38: StoreField: r2->field_13 = r16
    //     0xbaaf38: stur            w16, [x2, #0x13]
    // 0xbaaf3c: ldur            x0, [fp, #-0x48]
    // 0xbaaf40: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaaf40: stur            w0, [x2, #0x17]
    // 0xbaaf44: r1 = <Widget>
    //     0xbaaf44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaaf48: r0 = AllocateGrowableArray()
    //     0xbaaf48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaaf4c: mov             x1, x0
    // 0xbaaf50: ldur            x0, [fp, #-0x50]
    // 0xbaaf54: stur            x1, [fp, #-0x40]
    // 0xbaaf58: StoreField: r1->field_f = r0
    //     0xbaaf58: stur            w0, [x1, #0xf]
    // 0xbaaf5c: r2 = 6
    //     0xbaaf5c: movz            x2, #0x6
    // 0xbaaf60: StoreField: r1->field_b = r2
    //     0xbaaf60: stur            w2, [x1, #0xb]
    // 0xbaaf64: r0 = Column()
    //     0xbaaf64: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbaaf68: mov             x1, x0
    // 0xbaaf6c: r0 = Instance_Axis
    //     0xbaaf6c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbaaf70: stur            x1, [fp, #-0x48]
    // 0xbaaf74: StoreField: r1->field_f = r0
    //     0xbaaf74: stur            w0, [x1, #0xf]
    // 0xbaaf78: r2 = Instance_MainAxisAlignment
    //     0xbaaf78: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbaaf7c: ldr             x2, [x2, #0xa08]
    // 0xbaaf80: StoreField: r1->field_13 = r2
    //     0xbaaf80: stur            w2, [x1, #0x13]
    // 0xbaaf84: r3 = Instance_MainAxisSize
    //     0xbaaf84: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaaf88: ldr             x3, [x3, #0xa10]
    // 0xbaaf8c: ArrayStore: r1[0] = r3  ; List_4
    //     0xbaaf8c: stur            w3, [x1, #0x17]
    // 0xbaaf90: r4 = Instance_CrossAxisAlignment
    //     0xbaaf90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbaaf94: ldr             x4, [x4, #0x890]
    // 0xbaaf98: StoreField: r1->field_1b = r4
    //     0xbaaf98: stur            w4, [x1, #0x1b]
    // 0xbaaf9c: r5 = Instance_VerticalDirection
    //     0xbaaf9c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaafa0: ldr             x5, [x5, #0xa20]
    // 0xbaafa4: StoreField: r1->field_23 = r5
    //     0xbaafa4: stur            w5, [x1, #0x23]
    // 0xbaafa8: r6 = Instance_Clip
    //     0xbaafa8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaafac: ldr             x6, [x6, #0x38]
    // 0xbaafb0: StoreField: r1->field_2b = r6
    //     0xbaafb0: stur            w6, [x1, #0x2b]
    // 0xbaafb4: StoreField: r1->field_2f = rZR
    //     0xbaafb4: stur            xzr, [x1, #0x2f]
    // 0xbaafb8: ldur            x7, [fp, #-0x40]
    // 0xbaafbc: StoreField: r1->field_b = r7
    //     0xbaafbc: stur            w7, [x1, #0xb]
    // 0xbaafc0: r0 = Padding()
    //     0xbaafc0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbaafc4: r7 = Instance_EdgeInsets
    //     0xbaafc4: add             x7, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbaafc8: ldr             x7, [x7, #0xa78]
    // 0xbaafcc: stur            x0, [fp, #-0x40]
    // 0xbaafd0: StoreField: r0->field_f = r7
    //     0xbaafd0: stur            w7, [x0, #0xf]
    // 0xbaafd4: ldur            x1, [fp, #-0x48]
    // 0xbaafd8: StoreField: r0->field_b = r1
    //     0xbaafd8: stur            w1, [x0, #0xb]
    // 0xbaafdc: r1 = <FlexParentData>
    //     0xbaafdc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbaafe0: ldr             x1, [x1, #0xe00]
    // 0xbaafe4: r0 = Expanded()
    //     0xbaafe4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbaafe8: mov             x3, x0
    // 0xbaafec: r0 = 1
    //     0xbaafec: movz            x0, #0x1
    // 0xbaaff0: stur            x3, [fp, #-0x48]
    // 0xbaaff4: StoreField: r3->field_13 = r0
    //     0xbaaff4: stur            x0, [x3, #0x13]
    // 0xbaaff8: r4 = Instance_FlexFit
    //     0xbaaff8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbaaffc: ldr             x4, [x4, #0xe08]
    // 0xbab000: StoreField: r3->field_1b = r4
    //     0xbab000: stur            w4, [x3, #0x1b]
    // 0xbab004: ldur            x1, [fp, #-0x40]
    // 0xbab008: StoreField: r3->field_b = r1
    //     0xbab008: stur            w1, [x3, #0xb]
    // 0xbab00c: r1 = Null
    //     0xbab00c: mov             x1, NULL
    // 0xbab010: r2 = 6
    //     0xbab010: movz            x2, #0x6
    // 0xbab014: r0 = AllocateArray()
    //     0xbab014: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbab018: mov             x2, x0
    // 0xbab01c: ldur            x0, [fp, #-0x28]
    // 0xbab020: stur            x2, [fp, #-0x40]
    // 0xbab024: StoreField: r2->field_f = r0
    //     0xbab024: stur            w0, [x2, #0xf]
    // 0xbab028: ldur            x0, [fp, #-0x58]
    // 0xbab02c: StoreField: r2->field_13 = r0
    //     0xbab02c: stur            w0, [x2, #0x13]
    // 0xbab030: ldur            x0, [fp, #-0x48]
    // 0xbab034: ArrayStore: r2[0] = r0  ; List_4
    //     0xbab034: stur            w0, [x2, #0x17]
    // 0xbab038: r1 = <Widget>
    //     0xbab038: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbab03c: r0 = AllocateGrowableArray()
    //     0xbab03c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbab040: mov             x1, x0
    // 0xbab044: ldur            x0, [fp, #-0x40]
    // 0xbab048: stur            x1, [fp, #-0x28]
    // 0xbab04c: StoreField: r1->field_f = r0
    //     0xbab04c: stur            w0, [x1, #0xf]
    // 0xbab050: r2 = 6
    //     0xbab050: movz            x2, #0x6
    // 0xbab054: StoreField: r1->field_b = r2
    //     0xbab054: stur            w2, [x1, #0xb]
    // 0xbab058: r0 = Row()
    //     0xbab058: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbab05c: mov             x2, x0
    // 0xbab060: r0 = Instance_Axis
    //     0xbab060: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbab064: stur            x2, [fp, #-0x40]
    // 0xbab068: StoreField: r2->field_f = r0
    //     0xbab068: stur            w0, [x2, #0xf]
    // 0xbab06c: r3 = Instance_MainAxisAlignment
    //     0xbab06c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbab070: ldr             x3, [x3, #0xa08]
    // 0xbab074: StoreField: r2->field_13 = r3
    //     0xbab074: stur            w3, [x2, #0x13]
    // 0xbab078: r4 = Instance_MainAxisSize
    //     0xbab078: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbab07c: ldr             x4, [x4, #0xa10]
    // 0xbab080: ArrayStore: r2[0] = r4  ; List_4
    //     0xbab080: stur            w4, [x2, #0x17]
    // 0xbab084: r8 = Instance_CrossAxisAlignment
    //     0xbab084: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbab088: ldr             x8, [x8, #0xa18]
    // 0xbab08c: StoreField: r2->field_1b = r8
    //     0xbab08c: stur            w8, [x2, #0x1b]
    // 0xbab090: r5 = Instance_VerticalDirection
    //     0xbab090: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbab094: ldr             x5, [x5, #0xa20]
    // 0xbab098: StoreField: r2->field_23 = r5
    //     0xbab098: stur            w5, [x2, #0x23]
    // 0xbab09c: r6 = Instance_Clip
    //     0xbab09c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbab0a0: ldr             x6, [x6, #0x38]
    // 0xbab0a4: StoreField: r2->field_2b = r6
    //     0xbab0a4: stur            w6, [x2, #0x2b]
    // 0xbab0a8: StoreField: r2->field_2f = rZR
    //     0xbab0a8: stur            xzr, [x2, #0x2f]
    // 0xbab0ac: ldur            x1, [fp, #-0x28]
    // 0xbab0b0: StoreField: r2->field_b = r1
    //     0xbab0b0: stur            w1, [x2, #0xb]
    // 0xbab0b4: r1 = <FlexParentData>
    //     0xbab0b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbab0b8: ldr             x1, [x1, #0xe00]
    // 0xbab0bc: r0 = Expanded()
    //     0xbab0bc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbab0c0: r9 = 1
    //     0xbab0c0: movz            x9, #0x1
    // 0xbab0c4: stur            x0, [fp, #-0x28]
    // 0xbab0c8: StoreField: r0->field_13 = r9
    //     0xbab0c8: stur            x9, [x0, #0x13]
    // 0xbab0cc: r10 = Instance_FlexFit
    //     0xbab0cc: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbab0d0: ldr             x10, [x10, #0xe08]
    // 0xbab0d4: StoreField: r0->field_1b = r10
    //     0xbab0d4: stur            w10, [x0, #0x1b]
    // 0xbab0d8: ldur            x1, [fp, #-0x40]
    // 0xbab0dc: StoreField: r0->field_b = r1
    //     0xbab0dc: stur            w1, [x0, #0xb]
    // 0xbab0e0: ldur            x1, [fp, #-0x10]
    // 0xbab0e4: r0 = of()
    //     0xbab0e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab0e8: LoadField: r1 = r0->field_87
    //     0xbab0e8: ldur            w1, [x0, #0x87]
    // 0xbab0ec: DecompressPointer r1
    //     0xbab0ec: add             x1, x1, HEAP, lsl #32
    // 0xbab0f0: LoadField: r0 = r1->field_7
    //     0xbab0f0: ldur            w0, [x1, #7]
    // 0xbab0f4: DecompressPointer r0
    //     0xbab0f4: add             x0, x0, HEAP, lsl #32
    // 0xbab0f8: r16 = 12.000000
    //     0xbab0f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbab0fc: ldr             x16, [x16, #0x9e8]
    // 0xbab100: r30 = Instance_Color
    //     0xbab100: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbab104: ldr             lr, [lr, #0x858]
    // 0xbab108: stp             lr, x16, [SP]
    // 0xbab10c: mov             x1, x0
    // 0xbab110: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbab110: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbab114: ldr             x4, [x4, #0xaa0]
    // 0xbab118: r0 = copyWith()
    //     0xbab118: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbab11c: stur            x0, [fp, #-0x40]
    // 0xbab120: r0 = Text()
    //     0xbab120: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbab124: mov             x1, x0
    // 0xbab128: r0 = "Remove"
    //     0xbab128: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0xbab12c: ldr             x0, [x0, #0x7d0]
    // 0xbab130: stur            x1, [fp, #-0x48]
    // 0xbab134: StoreField: r1->field_b = r0
    //     0xbab134: stur            w0, [x1, #0xb]
    // 0xbab138: ldur            x0, [fp, #-0x40]
    // 0xbab13c: StoreField: r1->field_13 = r0
    //     0xbab13c: stur            w0, [x1, #0x13]
    // 0xbab140: r0 = InkWell()
    //     0xbab140: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbab144: mov             x3, x0
    // 0xbab148: ldur            x0, [fp, #-0x48]
    // 0xbab14c: stur            x3, [fp, #-0x40]
    // 0xbab150: StoreField: r3->field_b = r0
    //     0xbab150: stur            w0, [x3, #0xb]
    // 0xbab154: ldur            x2, [fp, #-0x18]
    // 0xbab158: r1 = Function '<anonymous closure>':.
    //     0xbab158: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a58] AnonymousClosure: (0xbacd44), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbab15c: ldr             x1, [x1, #0xa58]
    // 0xbab160: r0 = AllocateClosure()
    //     0xbab160: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbab164: mov             x1, x0
    // 0xbab168: ldur            x0, [fp, #-0x40]
    // 0xbab16c: StoreField: r0->field_f = r1
    //     0xbab16c: stur            w1, [x0, #0xf]
    // 0xbab170: r11 = true
    //     0xbab170: add             x11, NULL, #0x20  ; true
    // 0xbab174: StoreField: r0->field_43 = r11
    //     0xbab174: stur            w11, [x0, #0x43]
    // 0xbab178: r12 = Instance_BoxShape
    //     0xbab178: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbab17c: ldr             x12, [x12, #0x80]
    // 0xbab180: StoreField: r0->field_47 = r12
    //     0xbab180: stur            w12, [x0, #0x47]
    // 0xbab184: StoreField: r0->field_6f = r11
    //     0xbab184: stur            w11, [x0, #0x6f]
    // 0xbab188: r1 = false
    //     0xbab188: add             x1, NULL, #0x30  ; false
    // 0xbab18c: StoreField: r0->field_73 = r1
    //     0xbab18c: stur            w1, [x0, #0x73]
    // 0xbab190: StoreField: r0->field_83 = r11
    //     0xbab190: stur            w11, [x0, #0x83]
    // 0xbab194: StoreField: r0->field_7b = r1
    //     0xbab194: stur            w1, [x0, #0x7b]
    // 0xbab198: r0 = Padding()
    //     0xbab198: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbab19c: r13 = Instance_EdgeInsets
    //     0xbab19c: add             x13, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xbab1a0: ldr             x13, [x13, #0xa40]
    // 0xbab1a4: stur            x0, [fp, #-0x48]
    // 0xbab1a8: StoreField: r0->field_f = r13
    //     0xbab1a8: stur            w13, [x0, #0xf]
    // 0xbab1ac: ldur            x1, [fp, #-0x40]
    // 0xbab1b0: StoreField: r0->field_b = r1
    //     0xbab1b0: stur            w1, [x0, #0xb]
    // 0xbab1b4: r1 = Null
    //     0xbab1b4: mov             x1, NULL
    // 0xbab1b8: r2 = 4
    //     0xbab1b8: movz            x2, #0x4
    // 0xbab1bc: r0 = AllocateArray()
    //     0xbab1bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbab1c0: mov             x2, x0
    // 0xbab1c4: ldur            x0, [fp, #-0x28]
    // 0xbab1c8: stur            x2, [fp, #-0x40]
    // 0xbab1cc: StoreField: r2->field_f = r0
    //     0xbab1cc: stur            w0, [x2, #0xf]
    // 0xbab1d0: ldur            x0, [fp, #-0x48]
    // 0xbab1d4: StoreField: r2->field_13 = r0
    //     0xbab1d4: stur            w0, [x2, #0x13]
    // 0xbab1d8: r1 = <Widget>
    //     0xbab1d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbab1dc: r0 = AllocateGrowableArray()
    //     0xbab1dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbab1e0: mov             x1, x0
    // 0xbab1e4: ldur            x0, [fp, #-0x40]
    // 0xbab1e8: stur            x1, [fp, #-0x28]
    // 0xbab1ec: StoreField: r1->field_f = r0
    //     0xbab1ec: stur            w0, [x1, #0xf]
    // 0xbab1f0: r14 = 4
    //     0xbab1f0: movz            x14, #0x4
    // 0xbab1f4: StoreField: r1->field_b = r14
    //     0xbab1f4: stur            w14, [x1, #0xb]
    // 0xbab1f8: r0 = Row()
    //     0xbab1f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbab1fc: r19 = Instance_Axis
    //     0xbab1fc: ldr             x19, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbab200: stur            x0, [fp, #-0x40]
    // 0xbab204: StoreField: r0->field_f = r19
    //     0xbab204: stur            w19, [x0, #0xf]
    // 0xbab208: r20 = Instance_MainAxisAlignment
    //     0xbab208: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbab20c: ldr             x20, [x20, #0xa8]
    // 0xbab210: StoreField: r0->field_13 = r20
    //     0xbab210: stur            w20, [x0, #0x13]
    // 0xbab214: r1 = Instance_MainAxisSize
    //     0xbab214: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbab218: ldr             x1, [x1, #0xa10]
    // 0xbab21c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbab21c: stur            w1, [x0, #0x17]
    // 0xbab220: r2 = Instance_CrossAxisAlignment
    //     0xbab220: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbab224: ldr             x2, [x2, #0x890]
    // 0xbab228: StoreField: r0->field_1b = r2
    //     0xbab228: stur            w2, [x0, #0x1b]
    // 0xbab22c: r3 = Instance_VerticalDirection
    //     0xbab22c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbab230: ldr             x3, [x3, #0xa20]
    // 0xbab234: StoreField: r0->field_23 = r3
    //     0xbab234: stur            w3, [x0, #0x23]
    // 0xbab238: r4 = Instance_Clip
    //     0xbab238: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbab23c: ldr             x4, [x4, #0x38]
    // 0xbab240: StoreField: r0->field_2b = r4
    //     0xbab240: stur            w4, [x0, #0x2b]
    // 0xbab244: StoreField: r0->field_2f = rZR
    //     0xbab244: stur            xzr, [x0, #0x2f]
    // 0xbab248: ldur            x5, [fp, #-0x28]
    // 0xbab24c: StoreField: r0->field_b = r5
    //     0xbab24c: stur            w5, [x0, #0xb]
    // 0xbab250: r0 = Container()
    //     0xbab250: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbab254: stur            x0, [fp, #-0x28]
    // 0xbab258: ldur            x16, [fp, #-0x38]
    // 0xbab25c: ldur            lr, [fp, #-0x40]
    // 0xbab260: stp             lr, x16, [SP]
    // 0xbab264: mov             x1, x0
    // 0xbab268: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbab268: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbab26c: ldr             x4, [x4, #0x88]
    // 0xbab270: r0 = Container()
    //     0xbab270: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbab274: r0 = Padding()
    //     0xbab274: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbab278: r23 = Instance_EdgeInsets
    //     0xbab278: add             x23, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbab27c: ldr             x23, [x23, #0x778]
    // 0xbab280: StoreField: r0->field_f = r23
    //     0xbab280: stur            w23, [x0, #0xf]
    // 0xbab284: ldur            x1, [fp, #-0x28]
    // 0xbab288: StoreField: r0->field_b = r1
    //     0xbab288: stur            w1, [x0, #0xb]
    // 0xbab28c: mov             x2, x0
    // 0xbab290: b               #0xbabbb0
    // 0xbab294: r11 = true
    //     0xbab294: add             x11, NULL, #0x20  ; true
    // 0xbab298: r4 = 2
    //     0xbab298: movz            x4, #0x2
    // 0xbab29c: r6 = "Free"
    //     0xbab29c: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbab2a0: ldr             x6, [x6, #0x668]
    // 0xbab2a4: r5 = 150.000000
    //     0xbab2a4: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbab2a8: ldr             x5, [x5, #0x690]
    // 0xbab2ac: r3 = Instance_TextOverflow
    //     0xbab2ac: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbab2b0: ldr             x3, [x3, #0xe10]
    // 0xbab2b4: r7 = Instance_EdgeInsets
    //     0xbab2b4: add             x7, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbab2b8: ldr             x7, [x7, #0xa78]
    // 0xbab2bc: r8 = Instance_CrossAxisAlignment
    //     0xbab2bc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbab2c0: ldr             x8, [x8, #0xa18]
    // 0xbab2c4: r13 = Instance_EdgeInsets
    //     0xbab2c4: add             x13, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xbab2c8: ldr             x13, [x13, #0xa40]
    // 0xbab2cc: r20 = Instance_MainAxisAlignment
    //     0xbab2cc: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbab2d0: ldr             x20, [x20, #0xa8]
    // 0xbab2d4: r23 = Instance_EdgeInsets
    //     0xbab2d4: add             x23, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbab2d8: ldr             x23, [x23, #0x778]
    // 0xbab2dc: r14 = 4
    //     0xbab2dc: movz            x14, #0x4
    // 0xbab2e0: r19 = Instance_Axis
    //     0xbab2e0: ldr             x19, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbab2e4: r10 = Instance_FlexFit
    //     0xbab2e4: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbab2e8: ldr             x10, [x10, #0xe08]
    // 0xbab2ec: r12 = Instance_BoxShape
    //     0xbab2ec: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbab2f0: ldr             x12, [x12, #0x80]
    // 0xbab2f4: r0 = Instance_Alignment
    //     0xbab2f4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbab2f8: ldr             x0, [x0, #0xb10]
    // 0xbab2fc: r2 = -1
    //     0xbab2fc: movn            x2, #0
    // 0xbab300: r9 = 1
    //     0xbab300: movz            x9, #0x1
    // 0xbab304: ldur            x24, [fp, #-8]
    // 0xbab308: ldur            x1, [fp, #-0x10]
    // 0xbab30c: r0 = of()
    //     0xbab30c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab310: LoadField: r1 = r0->field_5b
    //     0xbab310: ldur            w1, [x0, #0x5b]
    // 0xbab314: DecompressPointer r1
    //     0xbab314: add             x1, x1, HEAP, lsl #32
    // 0xbab318: r0 = LoadClassIdInstr(r1)
    //     0xbab318: ldur            x0, [x1, #-1]
    //     0xbab31c: ubfx            x0, x0, #0xc, #0x14
    // 0xbab320: d0 = 0.070000
    //     0xbab320: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbab324: ldr             d0, [x17, #0x5f8]
    // 0xbab328: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbab328: sub             lr, x0, #0xffa
    //     0xbab32c: ldr             lr, [x21, lr, lsl #3]
    //     0xbab330: blr             lr
    // 0xbab334: r16 = 1.000000
    //     0xbab334: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbab338: str             x16, [SP]
    // 0xbab33c: mov             x2, x0
    // 0xbab340: r1 = Null
    //     0xbab340: mov             x1, NULL
    // 0xbab344: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbab344: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbab348: ldr             x4, [x4, #0x108]
    // 0xbab34c: r0 = Border.all()
    //     0xbab34c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbab350: stur            x0, [fp, #-0x28]
    // 0xbab354: r0 = BoxDecoration()
    //     0xbab354: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbab358: mov             x2, x0
    // 0xbab35c: ldur            x0, [fp, #-0x28]
    // 0xbab360: stur            x2, [fp, #-0x38]
    // 0xbab364: StoreField: r2->field_f = r0
    //     0xbab364: stur            w0, [x2, #0xf]
    // 0xbab368: r0 = Instance_BoxShape
    //     0xbab368: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbab36c: ldr             x0, [x0, #0x80]
    // 0xbab370: StoreField: r2->field_23 = r0
    //     0xbab370: stur            w0, [x2, #0x23]
    // 0xbab374: ldur            x1, [fp, #-0x10]
    // 0xbab378: r0 = of()
    //     0xbab378: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab37c: LoadField: r1 = r0->field_5b
    //     0xbab37c: ldur            w1, [x0, #0x5b]
    // 0xbab380: DecompressPointer r1
    //     0xbab380: add             x1, x1, HEAP, lsl #32
    // 0xbab384: r0 = LoadClassIdInstr(r1)
    //     0xbab384: ldur            x0, [x1, #-1]
    //     0xbab388: ubfx            x0, x0, #0xc, #0x14
    // 0xbab38c: d0 = 0.400000
    //     0xbab38c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbab390: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbab390: sub             lr, x0, #0xffa
    //     0xbab394: ldr             lr, [x21, lr, lsl #3]
    //     0xbab398: blr             lr
    // 0xbab39c: ldur            x1, [fp, #-0x10]
    // 0xbab3a0: stur            x0, [fp, #-0x28]
    // 0xbab3a4: r0 = of()
    //     0xbab3a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab3a8: LoadField: r1 = r0->field_87
    //     0xbab3a8: ldur            w1, [x0, #0x87]
    // 0xbab3ac: DecompressPointer r1
    //     0xbab3ac: add             x1, x1, HEAP, lsl #32
    // 0xbab3b0: LoadField: r0 = r1->field_7
    //     0xbab3b0: ldur            w0, [x1, #7]
    // 0xbab3b4: DecompressPointer r0
    //     0xbab3b4: add             x0, x0, HEAP, lsl #32
    // 0xbab3b8: r16 = 12.000000
    //     0xbab3b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbab3bc: ldr             x16, [x16, #0x9e8]
    // 0xbab3c0: r30 = Instance_Color
    //     0xbab3c0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbab3c4: stp             lr, x16, [SP]
    // 0xbab3c8: mov             x1, x0
    // 0xbab3cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbab3cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbab3d0: ldr             x4, [x4, #0xaa0]
    // 0xbab3d4: r0 = copyWith()
    //     0xbab3d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbab3d8: stur            x0, [fp, #-0x40]
    // 0xbab3dc: r0 = Text()
    //     0xbab3dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbab3e0: mov             x1, x0
    // 0xbab3e4: r0 = "Free"
    //     0xbab3e4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbab3e8: ldr             x0, [x0, #0x668]
    // 0xbab3ec: stur            x1, [fp, #-0x48]
    // 0xbab3f0: StoreField: r1->field_b = r0
    //     0xbab3f0: stur            w0, [x1, #0xb]
    // 0xbab3f4: ldur            x2, [fp, #-0x40]
    // 0xbab3f8: StoreField: r1->field_13 = r2
    //     0xbab3f8: stur            w2, [x1, #0x13]
    // 0xbab3fc: r0 = Center()
    //     0xbab3fc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbab400: mov             x1, x0
    // 0xbab404: r0 = Instance_Alignment
    //     0xbab404: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbab408: ldr             x0, [x0, #0xb10]
    // 0xbab40c: stur            x1, [fp, #-0x40]
    // 0xbab410: StoreField: r1->field_f = r0
    //     0xbab410: stur            w0, [x1, #0xf]
    // 0xbab414: ldur            x0, [fp, #-0x48]
    // 0xbab418: StoreField: r1->field_b = r0
    //     0xbab418: stur            w0, [x1, #0xb]
    // 0xbab41c: r0 = RotatedBox()
    //     0xbab41c: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbab420: mov             x1, x0
    // 0xbab424: r0 = -1
    //     0xbab424: movn            x0, #0
    // 0xbab428: stur            x1, [fp, #-0x48]
    // 0xbab42c: StoreField: r1->field_f = r0
    //     0xbab42c: stur            x0, [x1, #0xf]
    // 0xbab430: ldur            x0, [fp, #-0x40]
    // 0xbab434: StoreField: r1->field_b = r0
    //     0xbab434: stur            w0, [x1, #0xb]
    // 0xbab438: r0 = Container()
    //     0xbab438: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbab43c: stur            x0, [fp, #-0x40]
    // 0xbab440: r16 = 24.000000
    //     0xbab440: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbab444: ldr             x16, [x16, #0xba8]
    // 0xbab448: r30 = 56.000000
    //     0xbab448: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbab44c: ldr             lr, [lr, #0xb78]
    // 0xbab450: stp             lr, x16, [SP, #0x10]
    // 0xbab454: ldur            x16, [fp, #-0x28]
    // 0xbab458: ldur            lr, [fp, #-0x48]
    // 0xbab45c: stp             lr, x16, [SP]
    // 0xbab460: mov             x1, x0
    // 0xbab464: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbab464: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbab468: ldr             x4, [x4, #0x670]
    // 0xbab46c: r0 = Container()
    //     0xbab46c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbab470: ldur            x0, [fp, #-8]
    // 0xbab474: LoadField: r1 = r0->field_b
    //     0xbab474: ldur            w1, [x0, #0xb]
    // 0xbab478: DecompressPointer r1
    //     0xbab478: add             x1, x1, HEAP, lsl #32
    // 0xbab47c: cmp             w1, NULL
    // 0xbab480: b.eq            #0xbabde4
    // 0xbab484: LoadField: r2 = r1->field_b
    //     0xbab484: ldur            w2, [x1, #0xb]
    // 0xbab488: DecompressPointer r2
    //     0xbab488: add             x2, x2, HEAP, lsl #32
    // 0xbab48c: LoadField: r1 = r2->field_b
    //     0xbab48c: ldur            w1, [x2, #0xb]
    // 0xbab490: DecompressPointer r1
    //     0xbab490: add             x1, x1, HEAP, lsl #32
    // 0xbab494: cmp             w1, NULL
    // 0xbab498: b.ne            #0xbab4a4
    // 0xbab49c: r1 = Null
    //     0xbab49c: mov             x1, NULL
    // 0xbab4a0: b               #0xbab4c4
    // 0xbab4a4: LoadField: r2 = r1->field_43
    //     0xbab4a4: ldur            w2, [x1, #0x43]
    // 0xbab4a8: DecompressPointer r2
    //     0xbab4a8: add             x2, x2, HEAP, lsl #32
    // 0xbab4ac: cmp             w2, NULL
    // 0xbab4b0: b.ne            #0xbab4bc
    // 0xbab4b4: r1 = Null
    //     0xbab4b4: mov             x1, NULL
    // 0xbab4b8: b               #0xbab4c4
    // 0xbab4bc: LoadField: r1 = r2->field_7
    //     0xbab4bc: ldur            w1, [x2, #7]
    // 0xbab4c0: DecompressPointer r1
    //     0xbab4c0: add             x1, x1, HEAP, lsl #32
    // 0xbab4c4: cmp             w1, NULL
    // 0xbab4c8: b.ne            #0xbab4d4
    // 0xbab4cc: r3 = ""
    //     0xbab4cc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbab4d0: b               #0xbab4d8
    // 0xbab4d4: mov             x3, x1
    // 0xbab4d8: stur            x3, [fp, #-0x28]
    // 0xbab4dc: r1 = Function '<anonymous closure>':.
    //     0xbab4dc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a60] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbab4e0: ldr             x1, [x1, #0xa60]
    // 0xbab4e4: r2 = Null
    //     0xbab4e4: mov             x2, NULL
    // 0xbab4e8: r0 = AllocateClosure()
    //     0xbab4e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbab4ec: r1 = Function '<anonymous closure>':.
    //     0xbab4ec: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a68] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbab4f0: ldr             x1, [x1, #0xa68]
    // 0xbab4f4: r2 = Null
    //     0xbab4f4: mov             x2, NULL
    // 0xbab4f8: stur            x0, [fp, #-0x48]
    // 0xbab4fc: r0 = AllocateClosure()
    //     0xbab4fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbab500: stur            x0, [fp, #-0x50]
    // 0xbab504: r0 = CachedNetworkImage()
    //     0xbab504: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbab508: stur            x0, [fp, #-0x58]
    // 0xbab50c: r16 = 56.000000
    //     0xbab50c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbab510: ldr             x16, [x16, #0xb78]
    // 0xbab514: r30 = 56.000000
    //     0xbab514: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbab518: ldr             lr, [lr, #0xb78]
    // 0xbab51c: stp             lr, x16, [SP, #0x18]
    // 0xbab520: r16 = Instance_BoxFit
    //     0xbab520: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbab524: ldr             x16, [x16, #0x118]
    // 0xbab528: ldur            lr, [fp, #-0x48]
    // 0xbab52c: stp             lr, x16, [SP, #8]
    // 0xbab530: ldur            x16, [fp, #-0x50]
    // 0xbab534: str             x16, [SP]
    // 0xbab538: mov             x1, x0
    // 0xbab53c: ldur            x2, [fp, #-0x28]
    // 0xbab540: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xbab540: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xbab544: ldr             x4, [x4, #0x710]
    // 0xbab548: r0 = CachedNetworkImage()
    //     0xbab548: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbab54c: ldur            x0, [fp, #-8]
    // 0xbab550: LoadField: r1 = r0->field_b
    //     0xbab550: ldur            w1, [x0, #0xb]
    // 0xbab554: DecompressPointer r1
    //     0xbab554: add             x1, x1, HEAP, lsl #32
    // 0xbab558: cmp             w1, NULL
    // 0xbab55c: b.eq            #0xbabde8
    // 0xbab560: LoadField: r2 = r1->field_b
    //     0xbab560: ldur            w2, [x1, #0xb]
    // 0xbab564: DecompressPointer r2
    //     0xbab564: add             x2, x2, HEAP, lsl #32
    // 0xbab568: LoadField: r1 = r2->field_b
    //     0xbab568: ldur            w1, [x2, #0xb]
    // 0xbab56c: DecompressPointer r1
    //     0xbab56c: add             x1, x1, HEAP, lsl #32
    // 0xbab570: cmp             w1, NULL
    // 0xbab574: b.ne            #0xbab580
    // 0xbab578: r1 = Null
    //     0xbab578: mov             x1, NULL
    // 0xbab57c: b               #0xbab5a0
    // 0xbab580: LoadField: r2 = r1->field_43
    //     0xbab580: ldur            w2, [x1, #0x43]
    // 0xbab584: DecompressPointer r2
    //     0xbab584: add             x2, x2, HEAP, lsl #32
    // 0xbab588: cmp             w2, NULL
    // 0xbab58c: b.ne            #0xbab598
    // 0xbab590: r1 = Null
    //     0xbab590: mov             x1, NULL
    // 0xbab594: b               #0xbab5a0
    // 0xbab598: LoadField: r1 = r2->field_b
    //     0xbab598: ldur            w1, [x2, #0xb]
    // 0xbab59c: DecompressPointer r1
    //     0xbab59c: add             x1, x1, HEAP, lsl #32
    // 0xbab5a0: cmp             w1, NULL
    // 0xbab5a4: b.ne            #0xbab5b0
    // 0xbab5a8: r2 = ""
    //     0xbab5a8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbab5ac: b               #0xbab5b4
    // 0xbab5b0: mov             x2, x1
    // 0xbab5b4: ldur            x1, [fp, #-0x10]
    // 0xbab5b8: stur            x2, [fp, #-0x28]
    // 0xbab5bc: r0 = of()
    //     0xbab5bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab5c0: LoadField: r1 = r0->field_87
    //     0xbab5c0: ldur            w1, [x0, #0x87]
    // 0xbab5c4: DecompressPointer r1
    //     0xbab5c4: add             x1, x1, HEAP, lsl #32
    // 0xbab5c8: LoadField: r0 = r1->field_7
    //     0xbab5c8: ldur            w0, [x1, #7]
    // 0xbab5cc: DecompressPointer r0
    //     0xbab5cc: add             x0, x0, HEAP, lsl #32
    // 0xbab5d0: r16 = 12.000000
    //     0xbab5d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbab5d4: ldr             x16, [x16, #0x9e8]
    // 0xbab5d8: r30 = Instance_Color
    //     0xbab5d8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbab5dc: stp             lr, x16, [SP]
    // 0xbab5e0: mov             x1, x0
    // 0xbab5e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbab5e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbab5e8: ldr             x4, [x4, #0xaa0]
    // 0xbab5ec: r0 = copyWith()
    //     0xbab5ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbab5f0: stur            x0, [fp, #-0x48]
    // 0xbab5f4: r0 = Text()
    //     0xbab5f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbab5f8: mov             x1, x0
    // 0xbab5fc: ldur            x0, [fp, #-0x28]
    // 0xbab600: stur            x1, [fp, #-0x50]
    // 0xbab604: StoreField: r1->field_b = r0
    //     0xbab604: stur            w0, [x1, #0xb]
    // 0xbab608: ldur            x0, [fp, #-0x48]
    // 0xbab60c: StoreField: r1->field_13 = r0
    //     0xbab60c: stur            w0, [x1, #0x13]
    // 0xbab610: r0 = Instance_TextOverflow
    //     0xbab610: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbab614: ldr             x0, [x0, #0xe10]
    // 0xbab618: StoreField: r1->field_2b = r0
    //     0xbab618: stur            w0, [x1, #0x2b]
    // 0xbab61c: r0 = 2
    //     0xbab61c: movz            x0, #0x2
    // 0xbab620: StoreField: r1->field_37 = r0
    //     0xbab620: stur            w0, [x1, #0x37]
    // 0xbab624: r0 = SizedBox()
    //     0xbab624: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbab628: mov             x2, x0
    // 0xbab62c: r0 = 150.000000
    //     0xbab62c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbab630: ldr             x0, [x0, #0x690]
    // 0xbab634: stur            x2, [fp, #-0x28]
    // 0xbab638: StoreField: r2->field_f = r0
    //     0xbab638: stur            w0, [x2, #0xf]
    // 0xbab63c: ldur            x0, [fp, #-0x50]
    // 0xbab640: StoreField: r2->field_b = r0
    //     0xbab640: stur            w0, [x2, #0xb]
    // 0xbab644: ldur            x1, [fp, #-0x10]
    // 0xbab648: r0 = of()
    //     0xbab648: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab64c: LoadField: r1 = r0->field_87
    //     0xbab64c: ldur            w1, [x0, #0x87]
    // 0xbab650: DecompressPointer r1
    //     0xbab650: add             x1, x1, HEAP, lsl #32
    // 0xbab654: LoadField: r0 = r1->field_2b
    //     0xbab654: ldur            w0, [x1, #0x2b]
    // 0xbab658: DecompressPointer r0
    //     0xbab658: add             x0, x0, HEAP, lsl #32
    // 0xbab65c: r16 = 12.000000
    //     0xbab65c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbab660: ldr             x16, [x16, #0x9e8]
    // 0xbab664: r30 = Instance_Color
    //     0xbab664: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbab668: stp             lr, x16, [SP]
    // 0xbab66c: mov             x1, x0
    // 0xbab670: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbab670: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbab674: ldr             x4, [x4, #0xaa0]
    // 0xbab678: r0 = copyWith()
    //     0xbab678: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbab67c: stur            x0, [fp, #-0x48]
    // 0xbab680: r0 = Text()
    //     0xbab680: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbab684: mov             x2, x0
    // 0xbab688: r0 = "Free"
    //     0xbab688: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbab68c: ldr             x0, [x0, #0x668]
    // 0xbab690: stur            x2, [fp, #-0x50]
    // 0xbab694: StoreField: r2->field_b = r0
    //     0xbab694: stur            w0, [x2, #0xb]
    // 0xbab698: ldur            x0, [fp, #-0x48]
    // 0xbab69c: StoreField: r2->field_13 = r0
    //     0xbab69c: stur            w0, [x2, #0x13]
    // 0xbab6a0: ldur            x0, [fp, #-8]
    // 0xbab6a4: LoadField: r1 = r0->field_b
    //     0xbab6a4: ldur            w1, [x0, #0xb]
    // 0xbab6a8: DecompressPointer r1
    //     0xbab6a8: add             x1, x1, HEAP, lsl #32
    // 0xbab6ac: cmp             w1, NULL
    // 0xbab6b0: b.eq            #0xbabdec
    // 0xbab6b4: LoadField: r3 = r1->field_b
    //     0xbab6b4: ldur            w3, [x1, #0xb]
    // 0xbab6b8: DecompressPointer r3
    //     0xbab6b8: add             x3, x3, HEAP, lsl #32
    // 0xbab6bc: LoadField: r1 = r3->field_b
    //     0xbab6bc: ldur            w1, [x3, #0xb]
    // 0xbab6c0: DecompressPointer r1
    //     0xbab6c0: add             x1, x1, HEAP, lsl #32
    // 0xbab6c4: cmp             w1, NULL
    // 0xbab6c8: b.ne            #0xbab6d4
    // 0xbab6cc: r1 = Null
    //     0xbab6cc: mov             x1, NULL
    // 0xbab6d0: b               #0xbab6f4
    // 0xbab6d4: LoadField: r3 = r1->field_43
    //     0xbab6d4: ldur            w3, [x1, #0x43]
    // 0xbab6d8: DecompressPointer r3
    //     0xbab6d8: add             x3, x3, HEAP, lsl #32
    // 0xbab6dc: cmp             w3, NULL
    // 0xbab6e0: b.ne            #0xbab6ec
    // 0xbab6e4: r1 = Null
    //     0xbab6e4: mov             x1, NULL
    // 0xbab6e8: b               #0xbab6f4
    // 0xbab6ec: LoadField: r1 = r3->field_13
    //     0xbab6ec: ldur            w1, [x3, #0x13]
    // 0xbab6f0: DecompressPointer r1
    //     0xbab6f0: add             x1, x1, HEAP, lsl #32
    // 0xbab6f4: cmp             w1, NULL
    // 0xbab6f8: b.ne            #0xbab704
    // 0xbab6fc: r6 = ""
    //     0xbab6fc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbab700: b               #0xbab708
    // 0xbab704: mov             x6, x1
    // 0xbab708: ldur            x5, [fp, #-0x40]
    // 0xbab70c: ldur            x4, [fp, #-0x58]
    // 0xbab710: ldur            x3, [fp, #-0x28]
    // 0xbab714: ldur            x1, [fp, #-0x10]
    // 0xbab718: stur            x6, [fp, #-0x48]
    // 0xbab71c: r0 = of()
    //     0xbab71c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab720: LoadField: r1 = r0->field_87
    //     0xbab720: ldur            w1, [x0, #0x87]
    // 0xbab724: DecompressPointer r1
    //     0xbab724: add             x1, x1, HEAP, lsl #32
    // 0xbab728: LoadField: r0 = r1->field_2b
    //     0xbab728: ldur            w0, [x1, #0x2b]
    // 0xbab72c: DecompressPointer r0
    //     0xbab72c: add             x0, x0, HEAP, lsl #32
    // 0xbab730: r16 = 12.000000
    //     0xbab730: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbab734: ldr             x16, [x16, #0x9e8]
    // 0xbab738: r30 = Instance_TextDecoration
    //     0xbab738: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbab73c: ldr             lr, [lr, #0xe30]
    // 0xbab740: stp             lr, x16, [SP]
    // 0xbab744: mov             x1, x0
    // 0xbab748: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbab748: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbab74c: ldr             x4, [x4, #0x698]
    // 0xbab750: r0 = copyWith()
    //     0xbab750: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbab754: stur            x0, [fp, #-0x60]
    // 0xbab758: r0 = Text()
    //     0xbab758: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbab75c: mov             x3, x0
    // 0xbab760: ldur            x0, [fp, #-0x48]
    // 0xbab764: stur            x3, [fp, #-0x68]
    // 0xbab768: StoreField: r3->field_b = r0
    //     0xbab768: stur            w0, [x3, #0xb]
    // 0xbab76c: ldur            x0, [fp, #-0x60]
    // 0xbab770: StoreField: r3->field_13 = r0
    //     0xbab770: stur            w0, [x3, #0x13]
    // 0xbab774: r1 = Null
    //     0xbab774: mov             x1, NULL
    // 0xbab778: r2 = 6
    //     0xbab778: movz            x2, #0x6
    // 0xbab77c: r0 = AllocateArray()
    //     0xbab77c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbab780: mov             x2, x0
    // 0xbab784: ldur            x0, [fp, #-0x50]
    // 0xbab788: stur            x2, [fp, #-0x48]
    // 0xbab78c: StoreField: r2->field_f = r0
    //     0xbab78c: stur            w0, [x2, #0xf]
    // 0xbab790: r16 = Instance_SizedBox
    //     0xbab790: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbab794: ldr             x16, [x16, #0xa50]
    // 0xbab798: StoreField: r2->field_13 = r16
    //     0xbab798: stur            w16, [x2, #0x13]
    // 0xbab79c: ldur            x0, [fp, #-0x68]
    // 0xbab7a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbab7a0: stur            w0, [x2, #0x17]
    // 0xbab7a4: r1 = <Widget>
    //     0xbab7a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbab7a8: r0 = AllocateGrowableArray()
    //     0xbab7a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbab7ac: mov             x1, x0
    // 0xbab7b0: ldur            x0, [fp, #-0x48]
    // 0xbab7b4: stur            x1, [fp, #-0x50]
    // 0xbab7b8: StoreField: r1->field_f = r0
    //     0xbab7b8: stur            w0, [x1, #0xf]
    // 0xbab7bc: r2 = 6
    //     0xbab7bc: movz            x2, #0x6
    // 0xbab7c0: StoreField: r1->field_b = r2
    //     0xbab7c0: stur            w2, [x1, #0xb]
    // 0xbab7c4: r0 = Row()
    //     0xbab7c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbab7c8: mov             x3, x0
    // 0xbab7cc: r0 = Instance_Axis
    //     0xbab7cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbab7d0: stur            x3, [fp, #-0x48]
    // 0xbab7d4: StoreField: r3->field_f = r0
    //     0xbab7d4: stur            w0, [x3, #0xf]
    // 0xbab7d8: r4 = Instance_MainAxisAlignment
    //     0xbab7d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbab7dc: ldr             x4, [x4, #0xa08]
    // 0xbab7e0: StoreField: r3->field_13 = r4
    //     0xbab7e0: stur            w4, [x3, #0x13]
    // 0xbab7e4: r5 = Instance_MainAxisSize
    //     0xbab7e4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbab7e8: ldr             x5, [x5, #0xa10]
    // 0xbab7ec: ArrayStore: r3[0] = r5  ; List_4
    //     0xbab7ec: stur            w5, [x3, #0x17]
    // 0xbab7f0: r6 = Instance_CrossAxisAlignment
    //     0xbab7f0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbab7f4: ldr             x6, [x6, #0xa18]
    // 0xbab7f8: StoreField: r3->field_1b = r6
    //     0xbab7f8: stur            w6, [x3, #0x1b]
    // 0xbab7fc: r7 = Instance_VerticalDirection
    //     0xbab7fc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbab800: ldr             x7, [x7, #0xa20]
    // 0xbab804: StoreField: r3->field_23 = r7
    //     0xbab804: stur            w7, [x3, #0x23]
    // 0xbab808: r8 = Instance_Clip
    //     0xbab808: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbab80c: ldr             x8, [x8, #0x38]
    // 0xbab810: StoreField: r3->field_2b = r8
    //     0xbab810: stur            w8, [x3, #0x2b]
    // 0xbab814: StoreField: r3->field_2f = rZR
    //     0xbab814: stur            xzr, [x3, #0x2f]
    // 0xbab818: ldur            x1, [fp, #-0x50]
    // 0xbab81c: StoreField: r3->field_b = r1
    //     0xbab81c: stur            w1, [x3, #0xb]
    // 0xbab820: r1 = Null
    //     0xbab820: mov             x1, NULL
    // 0xbab824: r2 = 6
    //     0xbab824: movz            x2, #0x6
    // 0xbab828: r0 = AllocateArray()
    //     0xbab828: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbab82c: mov             x2, x0
    // 0xbab830: ldur            x0, [fp, #-0x28]
    // 0xbab834: stur            x2, [fp, #-0x50]
    // 0xbab838: StoreField: r2->field_f = r0
    //     0xbab838: stur            w0, [x2, #0xf]
    // 0xbab83c: r16 = Instance_SizedBox
    //     0xbab83c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbab840: ldr             x16, [x16, #0xc70]
    // 0xbab844: StoreField: r2->field_13 = r16
    //     0xbab844: stur            w16, [x2, #0x13]
    // 0xbab848: ldur            x0, [fp, #-0x48]
    // 0xbab84c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbab84c: stur            w0, [x2, #0x17]
    // 0xbab850: r1 = <Widget>
    //     0xbab850: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbab854: r0 = AllocateGrowableArray()
    //     0xbab854: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbab858: mov             x1, x0
    // 0xbab85c: ldur            x0, [fp, #-0x50]
    // 0xbab860: stur            x1, [fp, #-0x28]
    // 0xbab864: StoreField: r1->field_f = r0
    //     0xbab864: stur            w0, [x1, #0xf]
    // 0xbab868: r2 = 6
    //     0xbab868: movz            x2, #0x6
    // 0xbab86c: StoreField: r1->field_b = r2
    //     0xbab86c: stur            w2, [x1, #0xb]
    // 0xbab870: r0 = Column()
    //     0xbab870: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbab874: mov             x1, x0
    // 0xbab878: r0 = Instance_Axis
    //     0xbab878: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbab87c: stur            x1, [fp, #-0x48]
    // 0xbab880: StoreField: r1->field_f = r0
    //     0xbab880: stur            w0, [x1, #0xf]
    // 0xbab884: r2 = Instance_MainAxisAlignment
    //     0xbab884: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbab888: ldr             x2, [x2, #0xa08]
    // 0xbab88c: StoreField: r1->field_13 = r2
    //     0xbab88c: stur            w2, [x1, #0x13]
    // 0xbab890: r3 = Instance_MainAxisSize
    //     0xbab890: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbab894: ldr             x3, [x3, #0xa10]
    // 0xbab898: ArrayStore: r1[0] = r3  ; List_4
    //     0xbab898: stur            w3, [x1, #0x17]
    // 0xbab89c: r4 = Instance_CrossAxisAlignment
    //     0xbab89c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbab8a0: ldr             x4, [x4, #0x890]
    // 0xbab8a4: StoreField: r1->field_1b = r4
    //     0xbab8a4: stur            w4, [x1, #0x1b]
    // 0xbab8a8: r5 = Instance_VerticalDirection
    //     0xbab8a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbab8ac: ldr             x5, [x5, #0xa20]
    // 0xbab8b0: StoreField: r1->field_23 = r5
    //     0xbab8b0: stur            w5, [x1, #0x23]
    // 0xbab8b4: r6 = Instance_Clip
    //     0xbab8b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbab8b8: ldr             x6, [x6, #0x38]
    // 0xbab8bc: StoreField: r1->field_2b = r6
    //     0xbab8bc: stur            w6, [x1, #0x2b]
    // 0xbab8c0: StoreField: r1->field_2f = rZR
    //     0xbab8c0: stur            xzr, [x1, #0x2f]
    // 0xbab8c4: ldur            x7, [fp, #-0x28]
    // 0xbab8c8: StoreField: r1->field_b = r7
    //     0xbab8c8: stur            w7, [x1, #0xb]
    // 0xbab8cc: r0 = Padding()
    //     0xbab8cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbab8d0: mov             x2, x0
    // 0xbab8d4: r0 = Instance_EdgeInsets
    //     0xbab8d4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbab8d8: ldr             x0, [x0, #0xa78]
    // 0xbab8dc: stur            x2, [fp, #-0x28]
    // 0xbab8e0: StoreField: r2->field_f = r0
    //     0xbab8e0: stur            w0, [x2, #0xf]
    // 0xbab8e4: ldur            x0, [fp, #-0x48]
    // 0xbab8e8: StoreField: r2->field_b = r0
    //     0xbab8e8: stur            w0, [x2, #0xb]
    // 0xbab8ec: r1 = <FlexParentData>
    //     0xbab8ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbab8f0: ldr             x1, [x1, #0xe00]
    // 0xbab8f4: r0 = Expanded()
    //     0xbab8f4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbab8f8: mov             x3, x0
    // 0xbab8fc: r0 = 1
    //     0xbab8fc: movz            x0, #0x1
    // 0xbab900: stur            x3, [fp, #-0x48]
    // 0xbab904: StoreField: r3->field_13 = r0
    //     0xbab904: stur            x0, [x3, #0x13]
    // 0xbab908: r4 = Instance_FlexFit
    //     0xbab908: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbab90c: ldr             x4, [x4, #0xe08]
    // 0xbab910: StoreField: r3->field_1b = r4
    //     0xbab910: stur            w4, [x3, #0x1b]
    // 0xbab914: ldur            x1, [fp, #-0x28]
    // 0xbab918: StoreField: r3->field_b = r1
    //     0xbab918: stur            w1, [x3, #0xb]
    // 0xbab91c: r1 = Null
    //     0xbab91c: mov             x1, NULL
    // 0xbab920: r2 = 6
    //     0xbab920: movz            x2, #0x6
    // 0xbab924: r0 = AllocateArray()
    //     0xbab924: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbab928: mov             x2, x0
    // 0xbab92c: ldur            x0, [fp, #-0x40]
    // 0xbab930: stur            x2, [fp, #-0x28]
    // 0xbab934: StoreField: r2->field_f = r0
    //     0xbab934: stur            w0, [x2, #0xf]
    // 0xbab938: ldur            x0, [fp, #-0x58]
    // 0xbab93c: StoreField: r2->field_13 = r0
    //     0xbab93c: stur            w0, [x2, #0x13]
    // 0xbab940: ldur            x0, [fp, #-0x48]
    // 0xbab944: ArrayStore: r2[0] = r0  ; List_4
    //     0xbab944: stur            w0, [x2, #0x17]
    // 0xbab948: r1 = <Widget>
    //     0xbab948: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbab94c: r0 = AllocateGrowableArray()
    //     0xbab94c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbab950: mov             x1, x0
    // 0xbab954: ldur            x0, [fp, #-0x28]
    // 0xbab958: stur            x1, [fp, #-0x40]
    // 0xbab95c: StoreField: r1->field_f = r0
    //     0xbab95c: stur            w0, [x1, #0xf]
    // 0xbab960: r2 = 6
    //     0xbab960: movz            x2, #0x6
    // 0xbab964: StoreField: r1->field_b = r2
    //     0xbab964: stur            w2, [x1, #0xb]
    // 0xbab968: r0 = Row()
    //     0xbab968: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbab96c: mov             x2, x0
    // 0xbab970: r0 = Instance_Axis
    //     0xbab970: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbab974: stur            x2, [fp, #-0x28]
    // 0xbab978: StoreField: r2->field_f = r0
    //     0xbab978: stur            w0, [x2, #0xf]
    // 0xbab97c: r3 = Instance_MainAxisAlignment
    //     0xbab97c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbab980: ldr             x3, [x3, #0xa08]
    // 0xbab984: StoreField: r2->field_13 = r3
    //     0xbab984: stur            w3, [x2, #0x13]
    // 0xbab988: r4 = Instance_MainAxisSize
    //     0xbab988: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbab98c: ldr             x4, [x4, #0xa10]
    // 0xbab990: ArrayStore: r2[0] = r4  ; List_4
    //     0xbab990: stur            w4, [x2, #0x17]
    // 0xbab994: r1 = Instance_CrossAxisAlignment
    //     0xbab994: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbab998: ldr             x1, [x1, #0xa18]
    // 0xbab99c: StoreField: r2->field_1b = r1
    //     0xbab99c: stur            w1, [x2, #0x1b]
    // 0xbab9a0: r5 = Instance_VerticalDirection
    //     0xbab9a0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbab9a4: ldr             x5, [x5, #0xa20]
    // 0xbab9a8: StoreField: r2->field_23 = r5
    //     0xbab9a8: stur            w5, [x2, #0x23]
    // 0xbab9ac: r6 = Instance_Clip
    //     0xbab9ac: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbab9b0: ldr             x6, [x6, #0x38]
    // 0xbab9b4: StoreField: r2->field_2b = r6
    //     0xbab9b4: stur            w6, [x2, #0x2b]
    // 0xbab9b8: StoreField: r2->field_2f = rZR
    //     0xbab9b8: stur            xzr, [x2, #0x2f]
    // 0xbab9bc: ldur            x1, [fp, #-0x40]
    // 0xbab9c0: StoreField: r2->field_b = r1
    //     0xbab9c0: stur            w1, [x2, #0xb]
    // 0xbab9c4: r1 = <FlexParentData>
    //     0xbab9c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbab9c8: ldr             x1, [x1, #0xe00]
    // 0xbab9cc: r0 = Expanded()
    //     0xbab9cc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbab9d0: mov             x2, x0
    // 0xbab9d4: r0 = 1
    //     0xbab9d4: movz            x0, #0x1
    // 0xbab9d8: stur            x2, [fp, #-0x40]
    // 0xbab9dc: StoreField: r2->field_13 = r0
    //     0xbab9dc: stur            x0, [x2, #0x13]
    // 0xbab9e0: r0 = Instance_FlexFit
    //     0xbab9e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbab9e4: ldr             x0, [x0, #0xe08]
    // 0xbab9e8: StoreField: r2->field_1b = r0
    //     0xbab9e8: stur            w0, [x2, #0x1b]
    // 0xbab9ec: ldur            x0, [fp, #-0x28]
    // 0xbab9f0: StoreField: r2->field_b = r0
    //     0xbab9f0: stur            w0, [x2, #0xb]
    // 0xbab9f4: ldur            x1, [fp, #-0x10]
    // 0xbab9f8: r0 = of()
    //     0xbab9f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbab9fc: LoadField: r1 = r0->field_87
    //     0xbab9fc: ldur            w1, [x0, #0x87]
    // 0xbaba00: DecompressPointer r1
    //     0xbaba00: add             x1, x1, HEAP, lsl #32
    // 0xbaba04: LoadField: r0 = r1->field_7
    //     0xbaba04: ldur            w0, [x1, #7]
    // 0xbaba08: DecompressPointer r0
    //     0xbaba08: add             x0, x0, HEAP, lsl #32
    // 0xbaba0c: r16 = 12.000000
    //     0xbaba0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbaba10: ldr             x16, [x16, #0x9e8]
    // 0xbaba14: r30 = Instance_Color
    //     0xbaba14: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbaba18: ldr             lr, [lr, #0x858]
    // 0xbaba1c: stp             lr, x16, [SP]
    // 0xbaba20: mov             x1, x0
    // 0xbaba24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbaba24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbaba28: ldr             x4, [x4, #0xaa0]
    // 0xbaba2c: r0 = copyWith()
    //     0xbaba2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaba30: stur            x0, [fp, #-0x10]
    // 0xbaba34: r0 = Text()
    //     0xbaba34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbaba38: mov             x1, x0
    // 0xbaba3c: r0 = "Add"
    //     0xbaba3c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a70] "Add"
    //     0xbaba40: ldr             x0, [x0, #0xa70]
    // 0xbaba44: stur            x1, [fp, #-0x28]
    // 0xbaba48: StoreField: r1->field_b = r0
    //     0xbaba48: stur            w0, [x1, #0xb]
    // 0xbaba4c: ldur            x0, [fp, #-0x10]
    // 0xbaba50: StoreField: r1->field_13 = r0
    //     0xbaba50: stur            w0, [x1, #0x13]
    // 0xbaba54: r0 = InkWell()
    //     0xbaba54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbaba58: mov             x3, x0
    // 0xbaba5c: ldur            x0, [fp, #-0x28]
    // 0xbaba60: stur            x3, [fp, #-0x10]
    // 0xbaba64: StoreField: r3->field_b = r0
    //     0xbaba64: stur            w0, [x3, #0xb]
    // 0xbaba68: ldur            x2, [fp, #-0x18]
    // 0xbaba6c: r1 = Function '<anonymous closure>':.
    //     0xbaba6c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a78] AnonymousClosure: (0xbacc68), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbaba70: ldr             x1, [x1, #0xa78]
    // 0xbaba74: r0 = AllocateClosure()
    //     0xbaba74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaba78: mov             x1, x0
    // 0xbaba7c: ldur            x0, [fp, #-0x10]
    // 0xbaba80: StoreField: r0->field_f = r1
    //     0xbaba80: stur            w1, [x0, #0xf]
    // 0xbaba84: r1 = true
    //     0xbaba84: add             x1, NULL, #0x20  ; true
    // 0xbaba88: StoreField: r0->field_43 = r1
    //     0xbaba88: stur            w1, [x0, #0x43]
    // 0xbaba8c: r2 = Instance_BoxShape
    //     0xbaba8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbaba90: ldr             x2, [x2, #0x80]
    // 0xbaba94: StoreField: r0->field_47 = r2
    //     0xbaba94: stur            w2, [x0, #0x47]
    // 0xbaba98: StoreField: r0->field_6f = r1
    //     0xbaba98: stur            w1, [x0, #0x6f]
    // 0xbaba9c: r2 = false
    //     0xbaba9c: add             x2, NULL, #0x30  ; false
    // 0xbabaa0: StoreField: r0->field_73 = r2
    //     0xbabaa0: stur            w2, [x0, #0x73]
    // 0xbabaa4: StoreField: r0->field_83 = r1
    //     0xbabaa4: stur            w1, [x0, #0x83]
    // 0xbabaa8: StoreField: r0->field_7b = r2
    //     0xbabaa8: stur            w2, [x0, #0x7b]
    // 0xbabaac: r0 = Padding()
    //     0xbabaac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbabab0: mov             x3, x0
    // 0xbabab4: r0 = Instance_EdgeInsets
    //     0xbabab4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xbabab8: ldr             x0, [x0, #0xa40]
    // 0xbababc: stur            x3, [fp, #-0x28]
    // 0xbabac0: StoreField: r3->field_f = r0
    //     0xbabac0: stur            w0, [x3, #0xf]
    // 0xbabac4: ldur            x0, [fp, #-0x10]
    // 0xbabac8: StoreField: r3->field_b = r0
    //     0xbabac8: stur            w0, [x3, #0xb]
    // 0xbabacc: r1 = Null
    //     0xbabacc: mov             x1, NULL
    // 0xbabad0: r2 = 4
    //     0xbabad0: movz            x2, #0x4
    // 0xbabad4: r0 = AllocateArray()
    //     0xbabad4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbabad8: mov             x2, x0
    // 0xbabadc: ldur            x0, [fp, #-0x40]
    // 0xbabae0: stur            x2, [fp, #-0x10]
    // 0xbabae4: StoreField: r2->field_f = r0
    //     0xbabae4: stur            w0, [x2, #0xf]
    // 0xbabae8: ldur            x0, [fp, #-0x28]
    // 0xbabaec: StoreField: r2->field_13 = r0
    //     0xbabaec: stur            w0, [x2, #0x13]
    // 0xbabaf0: r1 = <Widget>
    //     0xbabaf0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbabaf4: r0 = AllocateGrowableArray()
    //     0xbabaf4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbabaf8: mov             x1, x0
    // 0xbabafc: ldur            x0, [fp, #-0x10]
    // 0xbabb00: stur            x1, [fp, #-0x28]
    // 0xbabb04: StoreField: r1->field_f = r0
    //     0xbabb04: stur            w0, [x1, #0xf]
    // 0xbabb08: r0 = 4
    //     0xbabb08: movz            x0, #0x4
    // 0xbabb0c: StoreField: r1->field_b = r0
    //     0xbabb0c: stur            w0, [x1, #0xb]
    // 0xbabb10: r0 = Row()
    //     0xbabb10: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbabb14: mov             x1, x0
    // 0xbabb18: r0 = Instance_Axis
    //     0xbabb18: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbabb1c: stur            x1, [fp, #-0x10]
    // 0xbabb20: StoreField: r1->field_f = r0
    //     0xbabb20: stur            w0, [x1, #0xf]
    // 0xbabb24: r0 = Instance_MainAxisAlignment
    //     0xbabb24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbabb28: ldr             x0, [x0, #0xa8]
    // 0xbabb2c: StoreField: r1->field_13 = r0
    //     0xbabb2c: stur            w0, [x1, #0x13]
    // 0xbabb30: r0 = Instance_MainAxisSize
    //     0xbabb30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbabb34: ldr             x0, [x0, #0xa10]
    // 0xbabb38: ArrayStore: r1[0] = r0  ; List_4
    //     0xbabb38: stur            w0, [x1, #0x17]
    // 0xbabb3c: r2 = Instance_CrossAxisAlignment
    //     0xbabb3c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbabb40: ldr             x2, [x2, #0x890]
    // 0xbabb44: StoreField: r1->field_1b = r2
    //     0xbabb44: stur            w2, [x1, #0x1b]
    // 0xbabb48: r3 = Instance_VerticalDirection
    //     0xbabb48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbabb4c: ldr             x3, [x3, #0xa20]
    // 0xbabb50: StoreField: r1->field_23 = r3
    //     0xbabb50: stur            w3, [x1, #0x23]
    // 0xbabb54: r4 = Instance_Clip
    //     0xbabb54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbabb58: ldr             x4, [x4, #0x38]
    // 0xbabb5c: StoreField: r1->field_2b = r4
    //     0xbabb5c: stur            w4, [x1, #0x2b]
    // 0xbabb60: StoreField: r1->field_2f = rZR
    //     0xbabb60: stur            xzr, [x1, #0x2f]
    // 0xbabb64: ldur            x5, [fp, #-0x28]
    // 0xbabb68: StoreField: r1->field_b = r5
    //     0xbabb68: stur            w5, [x1, #0xb]
    // 0xbabb6c: r0 = Container()
    //     0xbabb6c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbabb70: stur            x0, [fp, #-0x28]
    // 0xbabb74: ldur            x16, [fp, #-0x38]
    // 0xbabb78: ldur            lr, [fp, #-0x10]
    // 0xbabb7c: stp             lr, x16, [SP]
    // 0xbabb80: mov             x1, x0
    // 0xbabb84: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbabb84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbabb88: ldr             x4, [x4, #0x88]
    // 0xbabb8c: r0 = Container()
    //     0xbabb8c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbabb90: r0 = Padding()
    //     0xbabb90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbabb94: mov             x1, x0
    // 0xbabb98: r0 = Instance_EdgeInsets
    //     0xbabb98: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbabb9c: ldr             x0, [x0, #0x778]
    // 0xbabba0: StoreField: r1->field_f = r0
    //     0xbabba0: stur            w0, [x1, #0xf]
    // 0xbabba4: ldur            x0, [fp, #-0x28]
    // 0xbabba8: StoreField: r1->field_b = r0
    //     0xbabba8: stur            w0, [x1, #0xb]
    // 0xbabbac: mov             x2, x1
    // 0xbabbb0: ldur            x0, [fp, #-8]
    // 0xbabbb4: ldur            x1, [fp, #-0x20]
    // 0xbabbb8: stur            x2, [fp, #-0x10]
    // 0xbabbbc: r0 = Visibility()
    //     0xbabbbc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbabbc0: mov             x3, x0
    // 0xbabbc4: ldur            x0, [fp, #-0x10]
    // 0xbabbc8: stur            x3, [fp, #-0x28]
    // 0xbabbcc: StoreField: r3->field_b = r0
    //     0xbabbcc: stur            w0, [x3, #0xb]
    // 0xbabbd0: r0 = Instance_SizedBox
    //     0xbabbd0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbabbd4: StoreField: r3->field_f = r0
    //     0xbabbd4: stur            w0, [x3, #0xf]
    // 0xbabbd8: ldur            x0, [fp, #-0x20]
    // 0xbabbdc: StoreField: r3->field_13 = r0
    //     0xbabbdc: stur            w0, [x3, #0x13]
    // 0xbabbe0: r0 = false
    //     0xbabbe0: add             x0, NULL, #0x30  ; false
    // 0xbabbe4: ArrayStore: r3[0] = r0  ; List_4
    //     0xbabbe4: stur            w0, [x3, #0x17]
    // 0xbabbe8: StoreField: r3->field_1b = r0
    //     0xbabbe8: stur            w0, [x3, #0x1b]
    // 0xbabbec: StoreField: r3->field_1f = r0
    //     0xbabbec: stur            w0, [x3, #0x1f]
    // 0xbabbf0: StoreField: r3->field_23 = r0
    //     0xbabbf0: stur            w0, [x3, #0x23]
    // 0xbabbf4: StoreField: r3->field_27 = r0
    //     0xbabbf4: stur            w0, [x3, #0x27]
    // 0xbabbf8: StoreField: r3->field_2b = r0
    //     0xbabbf8: stur            w0, [x3, #0x2b]
    // 0xbabbfc: ldur            x0, [fp, #-8]
    // 0xbabc00: LoadField: r1 = r0->field_b
    //     0xbabc00: ldur            w1, [x0, #0xb]
    // 0xbabc04: DecompressPointer r1
    //     0xbabc04: add             x1, x1, HEAP, lsl #32
    // 0xbabc08: cmp             w1, NULL
    // 0xbabc0c: b.eq            #0xbabdf0
    // 0xbabc10: LoadField: r0 = r1->field_b
    //     0xbabc10: ldur            w0, [x1, #0xb]
    // 0xbabc14: DecompressPointer r0
    //     0xbabc14: add             x0, x0, HEAP, lsl #32
    // 0xbabc18: LoadField: r1 = r0->field_b
    //     0xbabc18: ldur            w1, [x0, #0xb]
    // 0xbabc1c: DecompressPointer r1
    //     0xbabc1c: add             x1, x1, HEAP, lsl #32
    // 0xbabc20: cmp             w1, NULL
    // 0xbabc24: b.ne            #0xbabc30
    // 0xbabc28: r0 = Null
    //     0xbabc28: mov             x0, NULL
    // 0xbabc2c: b               #0xbabc54
    // 0xbabc30: LoadField: r0 = r1->field_f
    //     0xbabc30: ldur            w0, [x1, #0xf]
    // 0xbabc34: DecompressPointer r0
    //     0xbabc34: add             x0, x0, HEAP, lsl #32
    // 0xbabc38: cmp             w0, NULL
    // 0xbabc3c: b.ne            #0xbabc48
    // 0xbabc40: r0 = Null
    //     0xbabc40: mov             x0, NULL
    // 0xbabc44: b               #0xbabc54
    // 0xbabc48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbabc48: ldur            w1, [x0, #0x17]
    // 0xbabc4c: DecompressPointer r1
    //     0xbabc4c: add             x1, x1, HEAP, lsl #32
    // 0xbabc50: mov             x0, x1
    // 0xbabc54: cmp             w0, NULL
    // 0xbabc58: b.ne            #0xbabc6c
    // 0xbabc5c: r1 = <BEntities>
    //     0xbabc5c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0xbabc60: ldr             x1, [x1, #0x130]
    // 0xbabc64: r2 = 0
    //     0xbabc64: movz            x2, #0
    // 0xbabc68: r0 = AllocateArray()
    //     0xbabc68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbabc6c: ldur            x2, [fp, #-0x30]
    // 0xbabc70: ldur            x1, [fp, #-0x28]
    // 0xbabc74: r3 = LoadClassIdInstr(r0)
    //     0xbabc74: ldur            x3, [x0, #-1]
    //     0xbabc78: ubfx            x3, x3, #0xc, #0x14
    // 0xbabc7c: str             x0, [SP]
    // 0xbabc80: mov             x0, x3
    // 0xbabc84: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbabc84: movz            x17, #0xc898
    //     0xbabc88: add             lr, x0, x17
    //     0xbabc8c: ldr             lr, [x21, lr, lsl #3]
    //     0xbabc90: blr             lr
    // 0xbabc94: r3 = LoadInt32Instr(r0)
    //     0xbabc94: sbfx            x3, x0, #1, #0x1f
    // 0xbabc98: ldur            x2, [fp, #-0x18]
    // 0xbabc9c: stur            x3, [fp, #-0x70]
    // 0xbabca0: r1 = Function '<anonymous closure>':.
    //     0xbabca0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a80] AnonymousClosure: (0xbabdf4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbabca4: ldr             x1, [x1, #0xa80]
    // 0xbabca8: r0 = AllocateClosure()
    //     0xbabca8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbabcac: r1 = Function '<anonymous closure>':.
    //     0xbabcac: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a88] AnonymousClosure: (0x9fbd20), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbabcb0: ldr             x1, [x1, #0xa88]
    // 0xbabcb4: r2 = Null
    //     0xbabcb4: mov             x2, NULL
    // 0xbabcb8: stur            x0, [fp, #-8]
    // 0xbabcbc: r0 = AllocateClosure()
    //     0xbabcbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbabcc0: stur            x0, [fp, #-0x10]
    // 0xbabcc4: r0 = ListView()
    //     0xbabcc4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbabcc8: stur            x0, [fp, #-0x18]
    // 0xbabccc: r16 = true
    //     0xbabccc: add             x16, NULL, #0x20  ; true
    // 0xbabcd0: r30 = false
    //     0xbabcd0: add             lr, NULL, #0x30  ; false
    // 0xbabcd4: stp             lr, x16, [SP, #8]
    // 0xbabcd8: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbabcd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbabcdc: ldr             x16, [x16, #0x1c8]
    // 0xbabce0: str             x16, [SP]
    // 0xbabce4: mov             x1, x0
    // 0xbabce8: ldur            x2, [fp, #-8]
    // 0xbabcec: ldur            x3, [fp, #-0x70]
    // 0xbabcf0: ldur            x5, [fp, #-0x10]
    // 0xbabcf4: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xbabcf4: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbabcf8: ldr             x4, [x4, #0x138]
    // 0xbabcfc: r0 = ListView.separated()
    //     0xbabcfc: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbabd00: r1 = Null
    //     0xbabd00: mov             x1, NULL
    // 0xbabd04: r2 = 6
    //     0xbabd04: movz            x2, #0x6
    // 0xbabd08: r0 = AllocateArray()
    //     0xbabd08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbabd0c: mov             x2, x0
    // 0xbabd10: ldur            x0, [fp, #-0x30]
    // 0xbabd14: stur            x2, [fp, #-8]
    // 0xbabd18: StoreField: r2->field_f = r0
    //     0xbabd18: stur            w0, [x2, #0xf]
    // 0xbabd1c: ldur            x0, [fp, #-0x28]
    // 0xbabd20: StoreField: r2->field_13 = r0
    //     0xbabd20: stur            w0, [x2, #0x13]
    // 0xbabd24: ldur            x0, [fp, #-0x18]
    // 0xbabd28: ArrayStore: r2[0] = r0  ; List_4
    //     0xbabd28: stur            w0, [x2, #0x17]
    // 0xbabd2c: r1 = <Widget>
    //     0xbabd2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbabd30: r0 = AllocateGrowableArray()
    //     0xbabd30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbabd34: mov             x1, x0
    // 0xbabd38: ldur            x0, [fp, #-8]
    // 0xbabd3c: stur            x1, [fp, #-0x10]
    // 0xbabd40: StoreField: r1->field_f = r0
    //     0xbabd40: stur            w0, [x1, #0xf]
    // 0xbabd44: r0 = 6
    //     0xbabd44: movz            x0, #0x6
    // 0xbabd48: StoreField: r1->field_b = r0
    //     0xbabd48: stur            w0, [x1, #0xb]
    // 0xbabd4c: r0 = Column()
    //     0xbabd4c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbabd50: mov             x1, x0
    // 0xbabd54: r0 = Instance_Axis
    //     0xbabd54: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbabd58: stur            x1, [fp, #-8]
    // 0xbabd5c: StoreField: r1->field_f = r0
    //     0xbabd5c: stur            w0, [x1, #0xf]
    // 0xbabd60: r0 = Instance_MainAxisAlignment
    //     0xbabd60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbabd64: ldr             x0, [x0, #0xa08]
    // 0xbabd68: StoreField: r1->field_13 = r0
    //     0xbabd68: stur            w0, [x1, #0x13]
    // 0xbabd6c: r0 = Instance_MainAxisSize
    //     0xbabd6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbabd70: ldr             x0, [x0, #0xa10]
    // 0xbabd74: ArrayStore: r1[0] = r0  ; List_4
    //     0xbabd74: stur            w0, [x1, #0x17]
    // 0xbabd78: r0 = Instance_CrossAxisAlignment
    //     0xbabd78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbabd7c: ldr             x0, [x0, #0x890]
    // 0xbabd80: StoreField: r1->field_1b = r0
    //     0xbabd80: stur            w0, [x1, #0x1b]
    // 0xbabd84: r0 = Instance_VerticalDirection
    //     0xbabd84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbabd88: ldr             x0, [x0, #0xa20]
    // 0xbabd8c: StoreField: r1->field_23 = r0
    //     0xbabd8c: stur            w0, [x1, #0x23]
    // 0xbabd90: r0 = Instance_Clip
    //     0xbabd90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbabd94: ldr             x0, [x0, #0x38]
    // 0xbabd98: StoreField: r1->field_2b = r0
    //     0xbabd98: stur            w0, [x1, #0x2b]
    // 0xbabd9c: StoreField: r1->field_2f = rZR
    //     0xbabd9c: stur            xzr, [x1, #0x2f]
    // 0xbabda0: ldur            x0, [fp, #-0x10]
    // 0xbabda4: StoreField: r1->field_b = r0
    //     0xbabda4: stur            w0, [x1, #0xb]
    // 0xbabda8: r0 = Padding()
    //     0xbabda8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbabdac: r1 = Instance_EdgeInsets
    //     0xbabdac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbabdb0: ldr             x1, [x1, #0x668]
    // 0xbabdb4: StoreField: r0->field_f = r1
    //     0xbabdb4: stur            w1, [x0, #0xf]
    // 0xbabdb8: ldur            x1, [fp, #-8]
    // 0xbabdbc: StoreField: r0->field_b = r1
    //     0xbabdbc: stur            w1, [x0, #0xb]
    // 0xbabdc0: LeaveFrame
    //     0xbabdc0: mov             SP, fp
    //     0xbabdc4: ldp             fp, lr, [SP], #0x10
    // 0xbabdc8: ret
    //     0xbabdc8: ret             
    // 0xbabdcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbabdcc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbabdd0: b               #0xbaa894
    // 0xbabdd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabdd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabdd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabdd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabddc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabddc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabde0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabde0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabde4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabde4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabde8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabde8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabdec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabdec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabdf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabdf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbabdf4, size: 0x18c
    // 0xbabdf4: EnterFrame
    //     0xbabdf4: stp             fp, lr, [SP, #-0x10]!
    //     0xbabdf8: mov             fp, SP
    // 0xbabdfc: AllocStack(0x18)
    //     0xbabdfc: sub             SP, SP, #0x18
    // 0xbabe00: SetupParameters()
    //     0xbabe00: ldr             x0, [fp, #0x20]
    //     0xbabe04: ldur            w1, [x0, #0x17]
    //     0xbabe08: add             x1, x1, HEAP, lsl #32
    //     0xbabe0c: stur            x1, [fp, #-8]
    // 0xbabe10: CheckStackOverflow
    //     0xbabe10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbabe14: cmp             SP, x16
    //     0xbabe18: b.ls            #0xbabf70
    // 0xbabe1c: r1 = 1
    //     0xbabe1c: movz            x1, #0x1
    // 0xbabe20: r0 = AllocateContext()
    //     0xbabe20: bl              #0x16f6108  ; AllocateContextStub
    // 0xbabe24: mov             x2, x0
    // 0xbabe28: ldur            x0, [fp, #-8]
    // 0xbabe2c: stur            x2, [fp, #-0x18]
    // 0xbabe30: StoreField: r2->field_b = r0
    //     0xbabe30: stur            w0, [x2, #0xb]
    // 0xbabe34: ldr             x1, [fp, #0x10]
    // 0xbabe38: StoreField: r2->field_f = r1
    //     0xbabe38: stur            w1, [x2, #0xf]
    // 0xbabe3c: LoadField: r3 = r0->field_f
    //     0xbabe3c: ldur            w3, [x0, #0xf]
    // 0xbabe40: DecompressPointer r3
    //     0xbabe40: add             x3, x3, HEAP, lsl #32
    // 0xbabe44: stur            x3, [fp, #-0x10]
    // 0xbabe48: LoadField: r0 = r3->field_b
    //     0xbabe48: ldur            w0, [x3, #0xb]
    // 0xbabe4c: DecompressPointer r0
    //     0xbabe4c: add             x0, x0, HEAP, lsl #32
    // 0xbabe50: cmp             w0, NULL
    // 0xbabe54: b.eq            #0xbabf78
    // 0xbabe58: LoadField: r4 = r0->field_b
    //     0xbabe58: ldur            w4, [x0, #0xb]
    // 0xbabe5c: DecompressPointer r4
    //     0xbabe5c: add             x4, x4, HEAP, lsl #32
    // 0xbabe60: LoadField: r0 = r4->field_b
    //     0xbabe60: ldur            w0, [x4, #0xb]
    // 0xbabe64: DecompressPointer r0
    //     0xbabe64: add             x0, x0, HEAP, lsl #32
    // 0xbabe68: cmp             w0, NULL
    // 0xbabe6c: b.ne            #0xbabe78
    // 0xbabe70: r0 = Null
    //     0xbabe70: mov             x0, NULL
    // 0xbabe74: b               #0xbabee4
    // 0xbabe78: LoadField: r4 = r0->field_f
    //     0xbabe78: ldur            w4, [x0, #0xf]
    // 0xbabe7c: DecompressPointer r4
    //     0xbabe7c: add             x4, x4, HEAP, lsl #32
    // 0xbabe80: cmp             w4, NULL
    // 0xbabe84: b.ne            #0xbabe90
    // 0xbabe88: r0 = Null
    //     0xbabe88: mov             x0, NULL
    // 0xbabe8c: b               #0xbabee4
    // 0xbabe90: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xbabe90: ldur            w5, [x4, #0x17]
    // 0xbabe94: DecompressPointer r5
    //     0xbabe94: add             x5, x5, HEAP, lsl #32
    // 0xbabe98: cmp             w5, NULL
    // 0xbabe9c: b.ne            #0xbabea8
    // 0xbabea0: r0 = Null
    //     0xbabea0: mov             x0, NULL
    // 0xbabea4: b               #0xbabee4
    // 0xbabea8: LoadField: r0 = r5->field_b
    //     0xbabea8: ldur            w0, [x5, #0xb]
    // 0xbabeac: r4 = LoadInt32Instr(r1)
    //     0xbabeac: sbfx            x4, x1, #1, #0x1f
    //     0xbabeb0: tbz             w1, #0, #0xbabeb8
    //     0xbabeb4: ldur            x4, [x1, #7]
    // 0xbabeb8: r1 = LoadInt32Instr(r0)
    //     0xbabeb8: sbfx            x1, x0, #1, #0x1f
    // 0xbabebc: mov             x0, x1
    // 0xbabec0: mov             x1, x4
    // 0xbabec4: cmp             x1, x0
    // 0xbabec8: b.hs            #0xbabf7c
    // 0xbabecc: LoadField: r0 = r5->field_f
    //     0xbabecc: ldur            w0, [x5, #0xf]
    // 0xbabed0: DecompressPointer r0
    //     0xbabed0: add             x0, x0, HEAP, lsl #32
    // 0xbabed4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbabed4: add             x16, x0, x4, lsl #2
    //     0xbabed8: ldur            w1, [x16, #0xf]
    // 0xbabedc: DecompressPointer r1
    //     0xbabedc: add             x1, x1, HEAP, lsl #32
    // 0xbabee0: mov             x0, x1
    // 0xbabee4: cmp             w0, NULL
    // 0xbabee8: b.ne            #0xbabef8
    // 0xbabeec: r0 = BEntities()
    //     0xbabeec: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xbabef0: mov             x2, x0
    // 0xbabef4: b               #0xbabefc
    // 0xbabef8: mov             x2, x0
    // 0xbabefc: ldur            x1, [fp, #-0x10]
    // 0xbabf00: ldr             x3, [fp, #0x18]
    // 0xbabf04: r0 = lineThemeBagItem()
    //     0xbabf04: bl              #0xbabf80  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::lineThemeBagItem
    // 0xbabf08: stur            x0, [fp, #-8]
    // 0xbabf0c: r0 = InkWell()
    //     0xbabf0c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbabf10: mov             x3, x0
    // 0xbabf14: ldur            x0, [fp, #-8]
    // 0xbabf18: stur            x3, [fp, #-0x10]
    // 0xbabf1c: StoreField: r3->field_b = r0
    //     0xbabf1c: stur            w0, [x3, #0xb]
    // 0xbabf20: ldur            x2, [fp, #-0x18]
    // 0xbabf24: r1 = Function '<anonymous closure>':.
    //     0xbabf24: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a98] AnonymousClosure: (0xbac954), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbabf28: ldr             x1, [x1, #0xa98]
    // 0xbabf2c: r0 = AllocateClosure()
    //     0xbabf2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbabf30: mov             x1, x0
    // 0xbabf34: ldur            x0, [fp, #-0x10]
    // 0xbabf38: StoreField: r0->field_f = r1
    //     0xbabf38: stur            w1, [x0, #0xf]
    // 0xbabf3c: r1 = true
    //     0xbabf3c: add             x1, NULL, #0x20  ; true
    // 0xbabf40: StoreField: r0->field_43 = r1
    //     0xbabf40: stur            w1, [x0, #0x43]
    // 0xbabf44: r2 = Instance_BoxShape
    //     0xbabf44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbabf48: ldr             x2, [x2, #0x80]
    // 0xbabf4c: StoreField: r0->field_47 = r2
    //     0xbabf4c: stur            w2, [x0, #0x47]
    // 0xbabf50: StoreField: r0->field_6f = r1
    //     0xbabf50: stur            w1, [x0, #0x6f]
    // 0xbabf54: r2 = false
    //     0xbabf54: add             x2, NULL, #0x30  ; false
    // 0xbabf58: StoreField: r0->field_73 = r2
    //     0xbabf58: stur            w2, [x0, #0x73]
    // 0xbabf5c: StoreField: r0->field_83 = r1
    //     0xbabf5c: stur            w1, [x0, #0x83]
    // 0xbabf60: StoreField: r0->field_7b = r2
    //     0xbabf60: stur            w2, [x0, #0x7b]
    // 0xbabf64: LeaveFrame
    //     0xbabf64: mov             SP, fp
    //     0xbabf68: ldp             fp, lr, [SP], #0x10
    // 0xbabf6c: ret
    //     0xbabf6c: ret             
    // 0xbabf70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbabf70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbabf74: b               #0xbabe1c
    // 0xbabf78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbabf78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbabf7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbabf7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ lineThemeBagItem(/* No info */) {
    // ** addr: 0xbabf80, size: 0x9d4
    // 0xbabf80: EnterFrame
    //     0xbabf80: stp             fp, lr, [SP, #-0x10]!
    //     0xbabf84: mov             fp, SP
    // 0xbabf88: AllocStack(0x78)
    //     0xbabf88: sub             SP, SP, #0x78
    // 0xbabf8c: SetupParameters(_BagDetailWidgetState this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0xbabf8c: mov             x4, x1
    //     0xbabf90: mov             x0, x3
    //     0xbabf94: stur            x3, [fp, #-0x20]
    //     0xbabf98: mov             x3, x2
    //     0xbabf9c: stur            x1, [fp, #-0x10]
    //     0xbabfa0: stur            x2, [fp, #-0x18]
    // 0xbabfa4: CheckStackOverflow
    //     0xbabfa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbabfa8: cmp             SP, x16
    //     0xbabfac: b.ls            #0xbac944
    // 0xbabfb0: LoadField: r1 = r3->field_13
    //     0xbabfb0: ldur            w1, [x3, #0x13]
    // 0xbabfb4: DecompressPointer r1
    //     0xbabfb4: add             x1, x1, HEAP, lsl #32
    // 0xbabfb8: cmp             w1, NULL
    // 0xbabfbc: b.ne            #0xbabfc8
    // 0xbabfc0: r5 = ""
    //     0xbabfc0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbabfc4: b               #0xbabfcc
    // 0xbabfc8: mov             x5, x1
    // 0xbabfcc: stur            x5, [fp, #-8]
    // 0xbabfd0: r1 = Function '<anonymous closure>':.
    //     0xbabfd0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ac0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbabfd4: ldr             x1, [x1, #0xac0]
    // 0xbabfd8: r2 = Null
    //     0xbabfd8: mov             x2, NULL
    // 0xbabfdc: r0 = AllocateClosure()
    //     0xbabfdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbabfe0: r1 = Function '<anonymous closure>':.
    //     0xbabfe0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ac8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbabfe4: ldr             x1, [x1, #0xac8]
    // 0xbabfe8: r2 = Null
    //     0xbabfe8: mov             x2, NULL
    // 0xbabfec: stur            x0, [fp, #-0x28]
    // 0xbabff0: r0 = AllocateClosure()
    //     0xbabff0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbabff4: stur            x0, [fp, #-0x30]
    // 0xbabff8: r0 = CachedNetworkImage()
    //     0xbabff8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbabffc: stur            x0, [fp, #-0x38]
    // 0xbac000: r16 = 56.000000
    //     0xbac000: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbac004: ldr             x16, [x16, #0xb78]
    // 0xbac008: r30 = 56.000000
    //     0xbac008: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbac00c: ldr             lr, [lr, #0xb78]
    // 0xbac010: stp             lr, x16, [SP, #0x18]
    // 0xbac014: r16 = Instance_BoxFit
    //     0xbac014: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbac018: ldr             x16, [x16, #0x118]
    // 0xbac01c: ldur            lr, [fp, #-0x28]
    // 0xbac020: stp             lr, x16, [SP, #8]
    // 0xbac024: ldur            x16, [fp, #-0x30]
    // 0xbac028: str             x16, [SP]
    // 0xbac02c: mov             x1, x0
    // 0xbac030: ldur            x2, [fp, #-8]
    // 0xbac034: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xbac034: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xbac038: ldr             x4, [x4, #0x710]
    // 0xbac03c: r0 = CachedNetworkImage()
    //     0xbac03c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbac040: r0 = Padding()
    //     0xbac040: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbac044: mov             x2, x0
    // 0xbac048: r0 = Instance_EdgeInsets
    //     0xbac048: add             x0, PP, #0x54, lsl #12  ; [pp+0x54ad0] Obj!EdgeInsets@d57d11
    //     0xbac04c: ldr             x0, [x0, #0xad0]
    // 0xbac050: stur            x2, [fp, #-0x28]
    // 0xbac054: StoreField: r2->field_f = r0
    //     0xbac054: stur            w0, [x2, #0xf]
    // 0xbac058: ldur            x0, [fp, #-0x38]
    // 0xbac05c: StoreField: r2->field_b = r0
    //     0xbac05c: stur            w0, [x2, #0xb]
    // 0xbac060: ldur            x0, [fp, #-0x18]
    // 0xbac064: LoadField: r1 = r0->field_f
    //     0xbac064: ldur            w1, [x0, #0xf]
    // 0xbac068: DecompressPointer r1
    //     0xbac068: add             x1, x1, HEAP, lsl #32
    // 0xbac06c: cmp             w1, NULL
    // 0xbac070: b.ne            #0xbac07c
    // 0xbac074: r3 = ""
    //     0xbac074: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbac078: b               #0xbac080
    // 0xbac07c: mov             x3, x1
    // 0xbac080: ldur            x1, [fp, #-0x20]
    // 0xbac084: stur            x3, [fp, #-8]
    // 0xbac088: r0 = of()
    //     0xbac088: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbac08c: LoadField: r1 = r0->field_87
    //     0xbac08c: ldur            w1, [x0, #0x87]
    // 0xbac090: DecompressPointer r1
    //     0xbac090: add             x1, x1, HEAP, lsl #32
    // 0xbac094: LoadField: r0 = r1->field_7
    //     0xbac094: ldur            w0, [x1, #7]
    // 0xbac098: DecompressPointer r0
    //     0xbac098: add             x0, x0, HEAP, lsl #32
    // 0xbac09c: r16 = 12.000000
    //     0xbac09c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbac0a0: ldr             x16, [x16, #0x9e8]
    // 0xbac0a4: r30 = Instance_Color
    //     0xbac0a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbac0a8: stp             lr, x16, [SP]
    // 0xbac0ac: mov             x1, x0
    // 0xbac0b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbac0b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbac0b4: ldr             x4, [x4, #0xaa0]
    // 0xbac0b8: r0 = copyWith()
    //     0xbac0b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbac0bc: stur            x0, [fp, #-0x30]
    // 0xbac0c0: r0 = Text()
    //     0xbac0c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbac0c4: mov             x1, x0
    // 0xbac0c8: ldur            x0, [fp, #-8]
    // 0xbac0cc: stur            x1, [fp, #-0x38]
    // 0xbac0d0: StoreField: r1->field_b = r0
    //     0xbac0d0: stur            w0, [x1, #0xb]
    // 0xbac0d4: ldur            x0, [fp, #-0x30]
    // 0xbac0d8: StoreField: r1->field_13 = r0
    //     0xbac0d8: stur            w0, [x1, #0x13]
    // 0xbac0dc: r0 = Instance_TextOverflow
    //     0xbac0dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbac0e0: ldr             x0, [x0, #0xe10]
    // 0xbac0e4: StoreField: r1->field_2b = r0
    //     0xbac0e4: stur            w0, [x1, #0x2b]
    // 0xbac0e8: ldur            x2, [fp, #-0x18]
    // 0xbac0ec: LoadField: r0 = r2->field_57
    //     0xbac0ec: ldur            w0, [x2, #0x57]
    // 0xbac0f0: DecompressPointer r0
    //     0xbac0f0: add             x0, x0, HEAP, lsl #32
    // 0xbac0f4: r3 = LoadClassIdInstr(r0)
    //     0xbac0f4: ldur            x3, [x0, #-1]
    //     0xbac0f8: ubfx            x3, x3, #0xc, #0x14
    // 0xbac0fc: r16 = "size"
    //     0xbac0fc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbac100: ldr             x16, [x16, #0x9c0]
    // 0xbac104: stp             x16, x0, [SP]
    // 0xbac108: mov             x0, x3
    // 0xbac10c: mov             lr, x0
    // 0xbac110: ldr             lr, [x21, lr, lsl #3]
    // 0xbac114: blr             lr
    // 0xbac118: tbnz            w0, #4, #0xbac170
    // 0xbac11c: ldur            x0, [fp, #-0x18]
    // 0xbac120: r1 = Null
    //     0xbac120: mov             x1, NULL
    // 0xbac124: r2 = 8
    //     0xbac124: movz            x2, #0x8
    // 0xbac128: r0 = AllocateArray()
    //     0xbac128: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac12c: r16 = "Size: "
    //     0xbac12c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xbac130: ldr             x16, [x16, #0xf00]
    // 0xbac134: StoreField: r0->field_f = r16
    //     0xbac134: stur            w16, [x0, #0xf]
    // 0xbac138: ldur            x1, [fp, #-0x18]
    // 0xbac13c: LoadField: r2 = r1->field_2f
    //     0xbac13c: ldur            w2, [x1, #0x2f]
    // 0xbac140: DecompressPointer r2
    //     0xbac140: add             x2, x2, HEAP, lsl #32
    // 0xbac144: StoreField: r0->field_13 = r2
    //     0xbac144: stur            w2, [x0, #0x13]
    // 0xbac148: r16 = " / Qty: "
    //     0xbac148: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbac14c: ldr             x16, [x16, #0x760]
    // 0xbac150: ArrayStore: r0[0] = r16  ; List_4
    //     0xbac150: stur            w16, [x0, #0x17]
    // 0xbac154: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbac154: ldur            w2, [x1, #0x17]
    // 0xbac158: DecompressPointer r2
    //     0xbac158: add             x2, x2, HEAP, lsl #32
    // 0xbac15c: StoreField: r0->field_1b = r2
    //     0xbac15c: stur            w2, [x0, #0x1b]
    // 0xbac160: str             x0, [SP]
    // 0xbac164: r0 = _interpolate()
    //     0xbac164: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbac168: mov             x3, x0
    // 0xbac16c: b               #0xbac1c0
    // 0xbac170: ldur            x0, [fp, #-0x18]
    // 0xbac174: r1 = Null
    //     0xbac174: mov             x1, NULL
    // 0xbac178: r2 = 8
    //     0xbac178: movz            x2, #0x8
    // 0xbac17c: r0 = AllocateArray()
    //     0xbac17c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac180: r16 = "Variant: "
    //     0xbac180: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xbac184: ldr             x16, [x16, #0xf08]
    // 0xbac188: StoreField: r0->field_f = r16
    //     0xbac188: stur            w16, [x0, #0xf]
    // 0xbac18c: ldur            x1, [fp, #-0x18]
    // 0xbac190: LoadField: r2 = r1->field_2f
    //     0xbac190: ldur            w2, [x1, #0x2f]
    // 0xbac194: DecompressPointer r2
    //     0xbac194: add             x2, x2, HEAP, lsl #32
    // 0xbac198: StoreField: r0->field_13 = r2
    //     0xbac198: stur            w2, [x0, #0x13]
    // 0xbac19c: r16 = " / Qty: "
    //     0xbac19c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbac1a0: ldr             x16, [x16, #0x760]
    // 0xbac1a4: ArrayStore: r0[0] = r16  ; List_4
    //     0xbac1a4: stur            w16, [x0, #0x17]
    // 0xbac1a8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbac1a8: ldur            w2, [x1, #0x17]
    // 0xbac1ac: DecompressPointer r2
    //     0xbac1ac: add             x2, x2, HEAP, lsl #32
    // 0xbac1b0: StoreField: r0->field_1b = r2
    //     0xbac1b0: stur            w2, [x0, #0x1b]
    // 0xbac1b4: str             x0, [SP]
    // 0xbac1b8: r0 = _interpolate()
    //     0xbac1b8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbac1bc: mov             x3, x0
    // 0xbac1c0: ldur            x2, [fp, #-0x10]
    // 0xbac1c4: ldur            x0, [fp, #-0x18]
    // 0xbac1c8: ldur            x1, [fp, #-0x20]
    // 0xbac1cc: stur            x3, [fp, #-8]
    // 0xbac1d0: r0 = of()
    //     0xbac1d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbac1d4: LoadField: r1 = r0->field_87
    //     0xbac1d4: ldur            w1, [x0, #0x87]
    // 0xbac1d8: DecompressPointer r1
    //     0xbac1d8: add             x1, x1, HEAP, lsl #32
    // 0xbac1dc: LoadField: r0 = r1->field_2b
    //     0xbac1dc: ldur            w0, [x1, #0x2b]
    // 0xbac1e0: DecompressPointer r0
    //     0xbac1e0: add             x0, x0, HEAP, lsl #32
    // 0xbac1e4: stur            x0, [fp, #-0x30]
    // 0xbac1e8: r1 = Instance_Color
    //     0xbac1e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbac1ec: d0 = 0.700000
    //     0xbac1ec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbac1f0: ldr             d0, [x17, #0xf48]
    // 0xbac1f4: r0 = withOpacity()
    //     0xbac1f4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbac1f8: r16 = 12.000000
    //     0xbac1f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbac1fc: ldr             x16, [x16, #0x9e8]
    // 0xbac200: stp             x0, x16, [SP]
    // 0xbac204: ldur            x1, [fp, #-0x30]
    // 0xbac208: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbac208: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbac20c: ldr             x4, [x4, #0xaa0]
    // 0xbac210: r0 = copyWith()
    //     0xbac210: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbac214: stur            x0, [fp, #-0x30]
    // 0xbac218: r0 = Text()
    //     0xbac218: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbac21c: mov             x3, x0
    // 0xbac220: ldur            x0, [fp, #-8]
    // 0xbac224: stur            x3, [fp, #-0x40]
    // 0xbac228: StoreField: r3->field_b = r0
    //     0xbac228: stur            w0, [x3, #0xb]
    // 0xbac22c: ldur            x0, [fp, #-0x30]
    // 0xbac230: StoreField: r3->field_13 = r0
    //     0xbac230: stur            w0, [x3, #0x13]
    // 0xbac234: ldur            x0, [fp, #-0x18]
    // 0xbac238: LoadField: r4 = r0->field_2b
    //     0xbac238: ldur            w4, [x0, #0x2b]
    // 0xbac23c: DecompressPointer r4
    //     0xbac23c: add             x4, x4, HEAP, lsl #32
    // 0xbac240: stur            x4, [fp, #-8]
    // 0xbac244: r1 = Null
    //     0xbac244: mov             x1, NULL
    // 0xbac248: r2 = 4
    //     0xbac248: movz            x2, #0x4
    // 0xbac24c: r0 = AllocateArray()
    //     0xbac24c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac250: mov             x1, x0
    // 0xbac254: ldur            x0, [fp, #-8]
    // 0xbac258: StoreField: r1->field_f = r0
    //     0xbac258: stur            w0, [x1, #0xf]
    // 0xbac25c: r16 = " "
    //     0xbac25c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbac260: StoreField: r1->field_13 = r16
    //     0xbac260: stur            w16, [x1, #0x13]
    // 0xbac264: str             x1, [SP]
    // 0xbac268: r0 = _interpolate()
    //     0xbac268: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbac26c: ldur            x1, [fp, #-0x20]
    // 0xbac270: stur            x0, [fp, #-8]
    // 0xbac274: r0 = of()
    //     0xbac274: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbac278: LoadField: r1 = r0->field_87
    //     0xbac278: ldur            w1, [x0, #0x87]
    // 0xbac27c: DecompressPointer r1
    //     0xbac27c: add             x1, x1, HEAP, lsl #32
    // 0xbac280: LoadField: r0 = r1->field_7
    //     0xbac280: ldur            w0, [x1, #7]
    // 0xbac284: DecompressPointer r0
    //     0xbac284: add             x0, x0, HEAP, lsl #32
    // 0xbac288: r16 = 12.000000
    //     0xbac288: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbac28c: ldr             x16, [x16, #0x9e8]
    // 0xbac290: r30 = Instance_Color
    //     0xbac290: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbac294: stp             lr, x16, [SP]
    // 0xbac298: mov             x1, x0
    // 0xbac29c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbac29c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbac2a0: ldr             x4, [x4, #0xaa0]
    // 0xbac2a4: r0 = copyWith()
    //     0xbac2a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbac2a8: stur            x0, [fp, #-0x30]
    // 0xbac2ac: r0 = TextSpan()
    //     0xbac2ac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbac2b0: mov             x3, x0
    // 0xbac2b4: ldur            x0, [fp, #-8]
    // 0xbac2b8: stur            x3, [fp, #-0x48]
    // 0xbac2bc: StoreField: r3->field_b = r0
    //     0xbac2bc: stur            w0, [x3, #0xb]
    // 0xbac2c0: r0 = Instance__DeferringMouseCursor
    //     0xbac2c0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbac2c4: ArrayStore: r3[0] = r0  ; List_4
    //     0xbac2c4: stur            w0, [x3, #0x17]
    // 0xbac2c8: ldur            x1, [fp, #-0x30]
    // 0xbac2cc: StoreField: r3->field_7 = r1
    //     0xbac2cc: stur            w1, [x3, #7]
    // 0xbac2d0: ldur            x4, [fp, #-0x18]
    // 0xbac2d4: LoadField: r5 = r4->field_37
    //     0xbac2d4: ldur            w5, [x4, #0x37]
    // 0xbac2d8: DecompressPointer r5
    //     0xbac2d8: add             x5, x5, HEAP, lsl #32
    // 0xbac2dc: stur            x5, [fp, #-8]
    // 0xbac2e0: r1 = Null
    //     0xbac2e0: mov             x1, NULL
    // 0xbac2e4: r2 = 4
    //     0xbac2e4: movz            x2, #0x4
    // 0xbac2e8: r0 = AllocateArray()
    //     0xbac2e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac2ec: mov             x1, x0
    // 0xbac2f0: ldur            x0, [fp, #-8]
    // 0xbac2f4: StoreField: r1->field_f = r0
    //     0xbac2f4: stur            w0, [x1, #0xf]
    // 0xbac2f8: r16 = " "
    //     0xbac2f8: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbac2fc: StoreField: r1->field_13 = r16
    //     0xbac2fc: stur            w16, [x1, #0x13]
    // 0xbac300: str             x1, [SP]
    // 0xbac304: r0 = _interpolate()
    //     0xbac304: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbac308: ldur            x1, [fp, #-0x20]
    // 0xbac30c: stur            x0, [fp, #-8]
    // 0xbac310: r0 = of()
    //     0xbac310: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbac314: LoadField: r1 = r0->field_87
    //     0xbac314: ldur            w1, [x0, #0x87]
    // 0xbac318: DecompressPointer r1
    //     0xbac318: add             x1, x1, HEAP, lsl #32
    // 0xbac31c: LoadField: r0 = r1->field_7
    //     0xbac31c: ldur            w0, [x1, #7]
    // 0xbac320: DecompressPointer r0
    //     0xbac320: add             x0, x0, HEAP, lsl #32
    // 0xbac324: stur            x0, [fp, #-0x30]
    // 0xbac328: r1 = Instance_Color
    //     0xbac328: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbac32c: d0 = 0.400000
    //     0xbac32c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbac330: r0 = withOpacity()
    //     0xbac330: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbac334: r16 = 12.000000
    //     0xbac334: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbac338: ldr             x16, [x16, #0x9e8]
    // 0xbac33c: stp             x0, x16, [SP, #8]
    // 0xbac340: r16 = Instance_TextDecoration
    //     0xbac340: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbac344: ldr             x16, [x16, #0xe30]
    // 0xbac348: str             x16, [SP]
    // 0xbac34c: ldur            x1, [fp, #-0x30]
    // 0xbac350: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbac350: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbac354: ldr             x4, [x4, #0xe38]
    // 0xbac358: r0 = copyWith()
    //     0xbac358: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbac35c: stur            x0, [fp, #-0x30]
    // 0xbac360: r0 = TextSpan()
    //     0xbac360: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbac364: mov             x2, x0
    // 0xbac368: ldur            x0, [fp, #-8]
    // 0xbac36c: stur            x2, [fp, #-0x50]
    // 0xbac370: StoreField: r2->field_b = r0
    //     0xbac370: stur            w0, [x2, #0xb]
    // 0xbac374: r0 = Instance__DeferringMouseCursor
    //     0xbac374: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbac378: ArrayStore: r2[0] = r0  ; List_4
    //     0xbac378: stur            w0, [x2, #0x17]
    // 0xbac37c: ldur            x1, [fp, #-0x30]
    // 0xbac380: StoreField: r2->field_7 = r1
    //     0xbac380: stur            w1, [x2, #7]
    // 0xbac384: ldur            x3, [fp, #-0x10]
    // 0xbac388: LoadField: r1 = r3->field_b
    //     0xbac388: ldur            w1, [x3, #0xb]
    // 0xbac38c: DecompressPointer r1
    //     0xbac38c: add             x1, x1, HEAP, lsl #32
    // 0xbac390: cmp             w1, NULL
    // 0xbac394: b.eq            #0xbac94c
    // 0xbac398: LoadField: r4 = r1->field_f
    //     0xbac398: ldur            w4, [x1, #0xf]
    // 0xbac39c: DecompressPointer r4
    //     0xbac39c: add             x4, x4, HEAP, lsl #32
    // 0xbac3a0: cmp             w4, NULL
    // 0xbac3a4: b.eq            #0xbac3b8
    // 0xbac3a8: LoadField: r1 = r4->field_13
    //     0xbac3a8: ldur            w1, [x4, #0x13]
    // 0xbac3ac: DecompressPointer r1
    //     0xbac3ac: add             x1, x1, HEAP, lsl #32
    // 0xbac3b0: cmp             w1, NULL
    // 0xbac3b4: b.ne            #0xbac3fc
    // 0xbac3b8: ldur            x4, [fp, #-0x18]
    // 0xbac3bc: LoadField: r1 = r4->field_3f
    //     0xbac3bc: ldur            w1, [x4, #0x3f]
    // 0xbac3c0: DecompressPointer r1
    //     0xbac3c0: add             x1, x1, HEAP, lsl #32
    // 0xbac3c4: cmp             w1, NULL
    // 0xbac3c8: b.ne            #0xbac3d4
    // 0xbac3cc: r1 = Null
    //     0xbac3cc: mov             x1, NULL
    // 0xbac3d0: b               #0xbac3e8
    // 0xbac3d4: LoadField: r5 = r1->field_b
    //     0xbac3d4: ldur            w5, [x1, #0xb]
    // 0xbac3d8: cbnz            w5, #0xbac3e4
    // 0xbac3dc: r1 = false
    //     0xbac3dc: add             x1, NULL, #0x30  ; false
    // 0xbac3e0: b               #0xbac3e8
    // 0xbac3e4: r1 = true
    //     0xbac3e4: add             x1, NULL, #0x20  ; true
    // 0xbac3e8: cmp             w1, NULL
    // 0xbac3ec: b.ne            #0xbac3f4
    // 0xbac3f0: r1 = false
    //     0xbac3f0: add             x1, NULL, #0x30  ; false
    // 0xbac3f4: mov             x5, x1
    // 0xbac3f8: b               #0xbac404
    // 0xbac3fc: ldur            x4, [fp, #-0x18]
    // 0xbac400: r5 = true
    //     0xbac400: add             x5, NULL, #0x20  ; true
    // 0xbac404: ldur            x1, [fp, #-0x20]
    // 0xbac408: stur            x5, [fp, #-8]
    // 0xbac40c: r0 = of()
    //     0xbac40c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbac410: LoadField: r1 = r0->field_5b
    //     0xbac410: ldur            w1, [x0, #0x5b]
    // 0xbac414: DecompressPointer r1
    //     0xbac414: add             x1, x1, HEAP, lsl #32
    // 0xbac418: r0 = LoadClassIdInstr(r1)
    //     0xbac418: ldur            x0, [x1, #-1]
    //     0xbac41c: ubfx            x0, x0, #0xc, #0x14
    // 0xbac420: d0 = 0.100000
    //     0xbac420: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbac424: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbac424: sub             lr, x0, #0xffa
    //     0xbac428: ldr             lr, [x21, lr, lsl #3]
    //     0xbac42c: blr             lr
    // 0xbac430: mov             x2, x0
    // 0xbac434: ldur            x0, [fp, #-0x10]
    // 0xbac438: stur            x2, [fp, #-0x30]
    // 0xbac43c: LoadField: r1 = r0->field_b
    //     0xbac43c: ldur            w1, [x0, #0xb]
    // 0xbac440: DecompressPointer r1
    //     0xbac440: add             x1, x1, HEAP, lsl #32
    // 0xbac444: cmp             w1, NULL
    // 0xbac448: b.eq            #0xbac950
    // 0xbac44c: LoadField: r0 = r1->field_f
    //     0xbac44c: ldur            w0, [x1, #0xf]
    // 0xbac450: DecompressPointer r0
    //     0xbac450: add             x0, x0, HEAP, lsl #32
    // 0xbac454: cmp             w0, NULL
    // 0xbac458: b.eq            #0xbac46c
    // 0xbac45c: LoadField: r1 = r0->field_13
    //     0xbac45c: ldur            w1, [x0, #0x13]
    // 0xbac460: DecompressPointer r1
    //     0xbac460: add             x1, x1, HEAP, lsl #32
    // 0xbac464: cmp             w1, NULL
    // 0xbac468: b.ne            #0xbac4c4
    // 0xbac46c: ldur            x0, [fp, #-0x18]
    // 0xbac470: LoadField: r1 = r0->field_3f
    //     0xbac470: ldur            w1, [x0, #0x3f]
    // 0xbac474: DecompressPointer r1
    //     0xbac474: add             x1, x1, HEAP, lsl #32
    // 0xbac478: cmp             w1, NULL
    // 0xbac47c: b.ne            #0xbac488
    // 0xbac480: r0 = Null
    //     0xbac480: mov             x0, NULL
    // 0xbac484: b               #0xbac4a0
    // 0xbac488: LoadField: r0 = r1->field_b
    //     0xbac488: ldur            w0, [x1, #0xb]
    // 0xbac48c: cbnz            w0, #0xbac498
    // 0xbac490: r1 = false
    //     0xbac490: add             x1, NULL, #0x30  ; false
    // 0xbac494: b               #0xbac49c
    // 0xbac498: r1 = true
    //     0xbac498: add             x1, NULL, #0x20  ; true
    // 0xbac49c: mov             x0, x1
    // 0xbac4a0: cmp             w0, NULL
    // 0xbac4a4: b.eq            #0xbac4b8
    // 0xbac4a8: tbnz            w0, #4, #0xbac4b8
    // 0xbac4ac: r0 = "Customised"
    //     0xbac4ac: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xbac4b0: ldr             x0, [x0, #0xd88]
    // 0xbac4b4: b               #0xbac4bc
    // 0xbac4b8: r0 = ""
    //     0xbac4b8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbac4bc: mov             x8, x0
    // 0xbac4c0: b               #0xbac4cc
    // 0xbac4c4: r8 = "Customised"
    //     0xbac4c4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xbac4c8: ldr             x8, [x8, #0xd88]
    // 0xbac4cc: ldur            x7, [fp, #-0x28]
    // 0xbac4d0: ldur            x6, [fp, #-0x38]
    // 0xbac4d4: ldur            x5, [fp, #-0x40]
    // 0xbac4d8: ldur            x4, [fp, #-0x48]
    // 0xbac4dc: ldur            x0, [fp, #-0x50]
    // 0xbac4e0: ldur            x3, [fp, #-8]
    // 0xbac4e4: ldur            x1, [fp, #-0x20]
    // 0xbac4e8: stur            x8, [fp, #-0x10]
    // 0xbac4ec: r0 = of()
    //     0xbac4ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbac4f0: LoadField: r1 = r0->field_87
    //     0xbac4f0: ldur            w1, [x0, #0x87]
    // 0xbac4f4: DecompressPointer r1
    //     0xbac4f4: add             x1, x1, HEAP, lsl #32
    // 0xbac4f8: LoadField: r0 = r1->field_7
    //     0xbac4f8: ldur            w0, [x1, #7]
    // 0xbac4fc: DecompressPointer r0
    //     0xbac4fc: add             x0, x0, HEAP, lsl #32
    // 0xbac500: stur            x0, [fp, #-0x18]
    // 0xbac504: r1 = Instance_Color
    //     0xbac504: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbac508: d0 = 0.700000
    //     0xbac508: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbac50c: ldr             d0, [x17, #0xf48]
    // 0xbac510: r0 = withOpacity()
    //     0xbac510: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbac514: r16 = 12.000000
    //     0xbac514: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbac518: ldr             x16, [x16, #0x9e8]
    // 0xbac51c: stp             x0, x16, [SP]
    // 0xbac520: ldur            x1, [fp, #-0x18]
    // 0xbac524: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbac524: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbac528: ldr             x4, [x4, #0xaa0]
    // 0xbac52c: r0 = copyWith()
    //     0xbac52c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbac530: stur            x0, [fp, #-0x18]
    // 0xbac534: r0 = Text()
    //     0xbac534: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbac538: mov             x1, x0
    // 0xbac53c: ldur            x0, [fp, #-0x10]
    // 0xbac540: stur            x1, [fp, #-0x20]
    // 0xbac544: StoreField: r1->field_b = r0
    //     0xbac544: stur            w0, [x1, #0xb]
    // 0xbac548: ldur            x0, [fp, #-0x18]
    // 0xbac54c: StoreField: r1->field_13 = r0
    //     0xbac54c: stur            w0, [x1, #0x13]
    // 0xbac550: r0 = Center()
    //     0xbac550: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbac554: mov             x1, x0
    // 0xbac558: r0 = Instance_Alignment
    //     0xbac558: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbac55c: ldr             x0, [x0, #0xb10]
    // 0xbac560: stur            x1, [fp, #-0x10]
    // 0xbac564: StoreField: r1->field_f = r0
    //     0xbac564: stur            w0, [x1, #0xf]
    // 0xbac568: ldur            x0, [fp, #-0x20]
    // 0xbac56c: StoreField: r1->field_b = r0
    //     0xbac56c: stur            w0, [x1, #0xb]
    // 0xbac570: r0 = Container()
    //     0xbac570: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbac574: stur            x0, [fp, #-0x18]
    // 0xbac578: r16 = 16.000000
    //     0xbac578: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbac57c: ldr             x16, [x16, #0x188]
    // 0xbac580: r30 = 86.000000
    //     0xbac580: add             lr, PP, #0x48, lsl #12  ; [pp+0x48580] 86
    //     0xbac584: ldr             lr, [lr, #0x580]
    // 0xbac588: stp             lr, x16, [SP, #0x10]
    // 0xbac58c: ldur            x16, [fp, #-0x30]
    // 0xbac590: ldur            lr, [fp, #-0x10]
    // 0xbac594: stp             lr, x16, [SP]
    // 0xbac598: mov             x1, x0
    // 0xbac59c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xbac59c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48588] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xbac5a0: ldr             x4, [x4, #0x588]
    // 0xbac5a4: r0 = Container()
    //     0xbac5a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbac5a8: r0 = Padding()
    //     0xbac5a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbac5ac: mov             x1, x0
    // 0xbac5b0: r0 = Instance_EdgeInsets
    //     0xbac5b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xbac5b4: ldr             x0, [x0, #0xe60]
    // 0xbac5b8: stur            x1, [fp, #-0x10]
    // 0xbac5bc: StoreField: r1->field_f = r0
    //     0xbac5bc: stur            w0, [x1, #0xf]
    // 0xbac5c0: ldur            x0, [fp, #-0x18]
    // 0xbac5c4: StoreField: r1->field_b = r0
    //     0xbac5c4: stur            w0, [x1, #0xb]
    // 0xbac5c8: r0 = Visibility()
    //     0xbac5c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbac5cc: mov             x1, x0
    // 0xbac5d0: ldur            x0, [fp, #-0x10]
    // 0xbac5d4: stur            x1, [fp, #-0x18]
    // 0xbac5d8: StoreField: r1->field_b = r0
    //     0xbac5d8: stur            w0, [x1, #0xb]
    // 0xbac5dc: r0 = Instance_SizedBox
    //     0xbac5dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbac5e0: StoreField: r1->field_f = r0
    //     0xbac5e0: stur            w0, [x1, #0xf]
    // 0xbac5e4: ldur            x0, [fp, #-8]
    // 0xbac5e8: StoreField: r1->field_13 = r0
    //     0xbac5e8: stur            w0, [x1, #0x13]
    // 0xbac5ec: r0 = false
    //     0xbac5ec: add             x0, NULL, #0x30  ; false
    // 0xbac5f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbac5f0: stur            w0, [x1, #0x17]
    // 0xbac5f4: StoreField: r1->field_1b = r0
    //     0xbac5f4: stur            w0, [x1, #0x1b]
    // 0xbac5f8: StoreField: r1->field_1f = r0
    //     0xbac5f8: stur            w0, [x1, #0x1f]
    // 0xbac5fc: StoreField: r1->field_23 = r0
    //     0xbac5fc: stur            w0, [x1, #0x23]
    // 0xbac600: StoreField: r1->field_27 = r0
    //     0xbac600: stur            w0, [x1, #0x27]
    // 0xbac604: StoreField: r1->field_2b = r0
    //     0xbac604: stur            w0, [x1, #0x2b]
    // 0xbac608: r0 = WidgetSpan()
    //     0xbac608: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xbac60c: mov             x3, x0
    // 0xbac610: ldur            x0, [fp, #-0x18]
    // 0xbac614: stur            x3, [fp, #-8]
    // 0xbac618: StoreField: r3->field_13 = r0
    //     0xbac618: stur            w0, [x3, #0x13]
    // 0xbac61c: r0 = Instance_PlaceholderAlignment
    //     0xbac61c: add             x0, PP, #0x46, lsl #12  ; [pp+0x46930] Obj!PlaceholderAlignment@d76421
    //     0xbac620: ldr             x0, [x0, #0x930]
    // 0xbac624: StoreField: r3->field_b = r0
    //     0xbac624: stur            w0, [x3, #0xb]
    // 0xbac628: r1 = Null
    //     0xbac628: mov             x1, NULL
    // 0xbac62c: r2 = 6
    //     0xbac62c: movz            x2, #0x6
    // 0xbac630: r0 = AllocateArray()
    //     0xbac630: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac634: mov             x2, x0
    // 0xbac638: ldur            x0, [fp, #-0x48]
    // 0xbac63c: stur            x2, [fp, #-0x10]
    // 0xbac640: StoreField: r2->field_f = r0
    //     0xbac640: stur            w0, [x2, #0xf]
    // 0xbac644: ldur            x0, [fp, #-0x50]
    // 0xbac648: StoreField: r2->field_13 = r0
    //     0xbac648: stur            w0, [x2, #0x13]
    // 0xbac64c: ldur            x0, [fp, #-8]
    // 0xbac650: ArrayStore: r2[0] = r0  ; List_4
    //     0xbac650: stur            w0, [x2, #0x17]
    // 0xbac654: r1 = <InlineSpan>
    //     0xbac654: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbac658: ldr             x1, [x1, #0xe40]
    // 0xbac65c: r0 = AllocateGrowableArray()
    //     0xbac65c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbac660: mov             x1, x0
    // 0xbac664: ldur            x0, [fp, #-0x10]
    // 0xbac668: stur            x1, [fp, #-8]
    // 0xbac66c: StoreField: r1->field_f = r0
    //     0xbac66c: stur            w0, [x1, #0xf]
    // 0xbac670: r2 = 6
    //     0xbac670: movz            x2, #0x6
    // 0xbac674: StoreField: r1->field_b = r2
    //     0xbac674: stur            w2, [x1, #0xb]
    // 0xbac678: r0 = TextSpan()
    //     0xbac678: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbac67c: mov             x1, x0
    // 0xbac680: ldur            x0, [fp, #-8]
    // 0xbac684: stur            x1, [fp, #-0x10]
    // 0xbac688: StoreField: r1->field_f = r0
    //     0xbac688: stur            w0, [x1, #0xf]
    // 0xbac68c: r0 = Instance__DeferringMouseCursor
    //     0xbac68c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbac690: ArrayStore: r1[0] = r0  ; List_4
    //     0xbac690: stur            w0, [x1, #0x17]
    // 0xbac694: r0 = RichText()
    //     0xbac694: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbac698: mov             x1, x0
    // 0xbac69c: ldur            x2, [fp, #-0x10]
    // 0xbac6a0: stur            x0, [fp, #-8]
    // 0xbac6a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbac6a4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbac6a8: r0 = RichText()
    //     0xbac6a8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbac6ac: r1 = Null
    //     0xbac6ac: mov             x1, NULL
    // 0xbac6b0: r2 = 6
    //     0xbac6b0: movz            x2, #0x6
    // 0xbac6b4: r0 = AllocateArray()
    //     0xbac6b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac6b8: mov             x2, x0
    // 0xbac6bc: ldur            x0, [fp, #-0x38]
    // 0xbac6c0: stur            x2, [fp, #-0x10]
    // 0xbac6c4: StoreField: r2->field_f = r0
    //     0xbac6c4: stur            w0, [x2, #0xf]
    // 0xbac6c8: ldur            x0, [fp, #-0x40]
    // 0xbac6cc: StoreField: r2->field_13 = r0
    //     0xbac6cc: stur            w0, [x2, #0x13]
    // 0xbac6d0: ldur            x0, [fp, #-8]
    // 0xbac6d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbac6d4: stur            w0, [x2, #0x17]
    // 0xbac6d8: r1 = <Widget>
    //     0xbac6d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbac6dc: r0 = AllocateGrowableArray()
    //     0xbac6dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbac6e0: mov             x1, x0
    // 0xbac6e4: ldur            x0, [fp, #-0x10]
    // 0xbac6e8: stur            x1, [fp, #-8]
    // 0xbac6ec: StoreField: r1->field_f = r0
    //     0xbac6ec: stur            w0, [x1, #0xf]
    // 0xbac6f0: r2 = 6
    //     0xbac6f0: movz            x2, #0x6
    // 0xbac6f4: StoreField: r1->field_b = r2
    //     0xbac6f4: stur            w2, [x1, #0xb]
    // 0xbac6f8: r0 = Column()
    //     0xbac6f8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbac6fc: mov             x1, x0
    // 0xbac700: r0 = Instance_Axis
    //     0xbac700: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbac704: stur            x1, [fp, #-0x10]
    // 0xbac708: StoreField: r1->field_f = r0
    //     0xbac708: stur            w0, [x1, #0xf]
    // 0xbac70c: r2 = Instance_MainAxisAlignment
    //     0xbac70c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbac710: ldr             x2, [x2, #0xab0]
    // 0xbac714: StoreField: r1->field_13 = r2
    //     0xbac714: stur            w2, [x1, #0x13]
    // 0xbac718: r3 = Instance_MainAxisSize
    //     0xbac718: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbac71c: ldr             x3, [x3, #0xa10]
    // 0xbac720: ArrayStore: r1[0] = r3  ; List_4
    //     0xbac720: stur            w3, [x1, #0x17]
    // 0xbac724: r4 = Instance_CrossAxisAlignment
    //     0xbac724: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbac728: ldr             x4, [x4, #0x890]
    // 0xbac72c: StoreField: r1->field_1b = r4
    //     0xbac72c: stur            w4, [x1, #0x1b]
    // 0xbac730: r4 = Instance_VerticalDirection
    //     0xbac730: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbac734: ldr             x4, [x4, #0xa20]
    // 0xbac738: StoreField: r1->field_23 = r4
    //     0xbac738: stur            w4, [x1, #0x23]
    // 0xbac73c: r5 = Instance_Clip
    //     0xbac73c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbac740: ldr             x5, [x5, #0x38]
    // 0xbac744: StoreField: r1->field_2b = r5
    //     0xbac744: stur            w5, [x1, #0x2b]
    // 0xbac748: StoreField: r1->field_2f = rZR
    //     0xbac748: stur            xzr, [x1, #0x2f]
    // 0xbac74c: ldur            x6, [fp, #-8]
    // 0xbac750: StoreField: r1->field_b = r6
    //     0xbac750: stur            w6, [x1, #0xb]
    // 0xbac754: r0 = Padding()
    //     0xbac754: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbac758: mov             x2, x0
    // 0xbac75c: r0 = Instance_EdgeInsets
    //     0xbac75c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54ad8] Obj!EdgeInsets@d57ce1
    //     0xbac760: ldr             x0, [x0, #0xad8]
    // 0xbac764: stur            x2, [fp, #-8]
    // 0xbac768: StoreField: r2->field_f = r0
    //     0xbac768: stur            w0, [x2, #0xf]
    // 0xbac76c: ldur            x0, [fp, #-0x10]
    // 0xbac770: StoreField: r2->field_b = r0
    //     0xbac770: stur            w0, [x2, #0xb]
    // 0xbac774: r1 = <FlexParentData>
    //     0xbac774: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbac778: ldr             x1, [x1, #0xe00]
    // 0xbac77c: r0 = Expanded()
    //     0xbac77c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbac780: mov             x1, x0
    // 0xbac784: r0 = 1
    //     0xbac784: movz            x0, #0x1
    // 0xbac788: stur            x1, [fp, #-0x10]
    // 0xbac78c: StoreField: r1->field_13 = r0
    //     0xbac78c: stur            x0, [x1, #0x13]
    // 0xbac790: r0 = Instance_FlexFit
    //     0xbac790: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbac794: ldr             x0, [x0, #0xe08]
    // 0xbac798: StoreField: r1->field_1b = r0
    //     0xbac798: stur            w0, [x1, #0x1b]
    // 0xbac79c: ldur            x0, [fp, #-8]
    // 0xbac7a0: StoreField: r1->field_b = r0
    //     0xbac7a0: stur            w0, [x1, #0xb]
    // 0xbac7a4: r0 = SvgPicture()
    //     0xbac7a4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbac7a8: mov             x1, x0
    // 0xbac7ac: r2 = "assets/images/small_right.svg"
    //     0xbac7ac: add             x2, PP, #0x46, lsl #12  ; [pp+0x46a70] "assets/images/small_right.svg"
    //     0xbac7b0: ldr             x2, [x2, #0xa70]
    // 0xbac7b4: stur            x0, [fp, #-8]
    // 0xbac7b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbac7b8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbac7bc: r0 = SvgPicture.asset()
    //     0xbac7bc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbac7c0: r0 = Align()
    //     0xbac7c0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbac7c4: mov             x1, x0
    // 0xbac7c8: r0 = Instance_Alignment
    //     0xbac7c8: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xbac7cc: ldr             x0, [x0, #0xa78]
    // 0xbac7d0: stur            x1, [fp, #-0x18]
    // 0xbac7d4: StoreField: r1->field_f = r0
    //     0xbac7d4: stur            w0, [x1, #0xf]
    // 0xbac7d8: ldur            x0, [fp, #-8]
    // 0xbac7dc: StoreField: r1->field_b = r0
    //     0xbac7dc: stur            w0, [x1, #0xb]
    // 0xbac7e0: r0 = Padding()
    //     0xbac7e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbac7e4: mov             x3, x0
    // 0xbac7e8: r0 = Instance_EdgeInsets
    //     0xbac7e8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xbac7ec: ldr             x0, [x0, #0x268]
    // 0xbac7f0: stur            x3, [fp, #-8]
    // 0xbac7f4: StoreField: r3->field_f = r0
    //     0xbac7f4: stur            w0, [x3, #0xf]
    // 0xbac7f8: ldur            x0, [fp, #-0x18]
    // 0xbac7fc: StoreField: r3->field_b = r0
    //     0xbac7fc: stur            w0, [x3, #0xb]
    // 0xbac800: r1 = Null
    //     0xbac800: mov             x1, NULL
    // 0xbac804: r2 = 6
    //     0xbac804: movz            x2, #0x6
    // 0xbac808: r0 = AllocateArray()
    //     0xbac808: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac80c: mov             x2, x0
    // 0xbac810: ldur            x0, [fp, #-0x28]
    // 0xbac814: stur            x2, [fp, #-0x18]
    // 0xbac818: StoreField: r2->field_f = r0
    //     0xbac818: stur            w0, [x2, #0xf]
    // 0xbac81c: ldur            x0, [fp, #-0x10]
    // 0xbac820: StoreField: r2->field_13 = r0
    //     0xbac820: stur            w0, [x2, #0x13]
    // 0xbac824: ldur            x0, [fp, #-8]
    // 0xbac828: ArrayStore: r2[0] = r0  ; List_4
    //     0xbac828: stur            w0, [x2, #0x17]
    // 0xbac82c: r1 = <Widget>
    //     0xbac82c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbac830: r0 = AllocateGrowableArray()
    //     0xbac830: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbac834: mov             x1, x0
    // 0xbac838: ldur            x0, [fp, #-0x18]
    // 0xbac83c: stur            x1, [fp, #-8]
    // 0xbac840: StoreField: r1->field_f = r0
    //     0xbac840: stur            w0, [x1, #0xf]
    // 0xbac844: r0 = 6
    //     0xbac844: movz            x0, #0x6
    // 0xbac848: StoreField: r1->field_b = r0
    //     0xbac848: stur            w0, [x1, #0xb]
    // 0xbac84c: r0 = Row()
    //     0xbac84c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbac850: mov             x3, x0
    // 0xbac854: r0 = Instance_Axis
    //     0xbac854: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbac858: stur            x3, [fp, #-0x10]
    // 0xbac85c: StoreField: r3->field_f = r0
    //     0xbac85c: stur            w0, [x3, #0xf]
    // 0xbac860: r0 = Instance_MainAxisAlignment
    //     0xbac860: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbac864: ldr             x0, [x0, #0xab0]
    // 0xbac868: StoreField: r3->field_13 = r0
    //     0xbac868: stur            w0, [x3, #0x13]
    // 0xbac86c: r0 = Instance_MainAxisSize
    //     0xbac86c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbac870: ldr             x0, [x0, #0xa10]
    // 0xbac874: ArrayStore: r3[0] = r0  ; List_4
    //     0xbac874: stur            w0, [x3, #0x17]
    // 0xbac878: r4 = Instance_CrossAxisAlignment
    //     0xbac878: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbac87c: ldr             x4, [x4, #0xa18]
    // 0xbac880: StoreField: r3->field_1b = r4
    //     0xbac880: stur            w4, [x3, #0x1b]
    // 0xbac884: r5 = Instance_VerticalDirection
    //     0xbac884: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbac888: ldr             x5, [x5, #0xa20]
    // 0xbac88c: StoreField: r3->field_23 = r5
    //     0xbac88c: stur            w5, [x3, #0x23]
    // 0xbac890: r6 = Instance_Clip
    //     0xbac890: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbac894: ldr             x6, [x6, #0x38]
    // 0xbac898: StoreField: r3->field_2b = r6
    //     0xbac898: stur            w6, [x3, #0x2b]
    // 0xbac89c: StoreField: r3->field_2f = rZR
    //     0xbac89c: stur            xzr, [x3, #0x2f]
    // 0xbac8a0: ldur            x1, [fp, #-8]
    // 0xbac8a4: StoreField: r3->field_b = r1
    //     0xbac8a4: stur            w1, [x3, #0xb]
    // 0xbac8a8: r1 = Null
    //     0xbac8a8: mov             x1, NULL
    // 0xbac8ac: r2 = 2
    //     0xbac8ac: movz            x2, #0x2
    // 0xbac8b0: r0 = AllocateArray()
    //     0xbac8b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbac8b4: mov             x2, x0
    // 0xbac8b8: ldur            x0, [fp, #-0x10]
    // 0xbac8bc: stur            x2, [fp, #-8]
    // 0xbac8c0: StoreField: r2->field_f = r0
    //     0xbac8c0: stur            w0, [x2, #0xf]
    // 0xbac8c4: r1 = <Widget>
    //     0xbac8c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbac8c8: r0 = AllocateGrowableArray()
    //     0xbac8c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbac8cc: mov             x1, x0
    // 0xbac8d0: ldur            x0, [fp, #-8]
    // 0xbac8d4: stur            x1, [fp, #-0x10]
    // 0xbac8d8: StoreField: r1->field_f = r0
    //     0xbac8d8: stur            w0, [x1, #0xf]
    // 0xbac8dc: r0 = 2
    //     0xbac8dc: movz            x0, #0x2
    // 0xbac8e0: StoreField: r1->field_b = r0
    //     0xbac8e0: stur            w0, [x1, #0xb]
    // 0xbac8e4: r0 = Column()
    //     0xbac8e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbac8e8: r1 = Instance_Axis
    //     0xbac8e8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbac8ec: StoreField: r0->field_f = r1
    //     0xbac8ec: stur            w1, [x0, #0xf]
    // 0xbac8f0: r1 = Instance_MainAxisAlignment
    //     0xbac8f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbac8f4: ldr             x1, [x1, #0xa08]
    // 0xbac8f8: StoreField: r0->field_13 = r1
    //     0xbac8f8: stur            w1, [x0, #0x13]
    // 0xbac8fc: r1 = Instance_MainAxisSize
    //     0xbac8fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbac900: ldr             x1, [x1, #0xa10]
    // 0xbac904: ArrayStore: r0[0] = r1  ; List_4
    //     0xbac904: stur            w1, [x0, #0x17]
    // 0xbac908: r1 = Instance_CrossAxisAlignment
    //     0xbac908: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbac90c: ldr             x1, [x1, #0xa18]
    // 0xbac910: StoreField: r0->field_1b = r1
    //     0xbac910: stur            w1, [x0, #0x1b]
    // 0xbac914: r1 = Instance_VerticalDirection
    //     0xbac914: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbac918: ldr             x1, [x1, #0xa20]
    // 0xbac91c: StoreField: r0->field_23 = r1
    //     0xbac91c: stur            w1, [x0, #0x23]
    // 0xbac920: r1 = Instance_Clip
    //     0xbac920: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbac924: ldr             x1, [x1, #0x38]
    // 0xbac928: StoreField: r0->field_2b = r1
    //     0xbac928: stur            w1, [x0, #0x2b]
    // 0xbac92c: StoreField: r0->field_2f = rZR
    //     0xbac92c: stur            xzr, [x0, #0x2f]
    // 0xbac930: ldur            x1, [fp, #-0x10]
    // 0xbac934: StoreField: r0->field_b = r1
    //     0xbac934: stur            w1, [x0, #0xb]
    // 0xbac938: LeaveFrame
    //     0xbac938: mov             SP, fp
    //     0xbac93c: ldp             fp, lr, [SP], #0x10
    // 0xbac940: ret
    //     0xbac940: ret             
    // 0xbac944: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbac944: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbac948: b               #0xbabfb0
    // 0xbac94c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbac94c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbac950: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbac950: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbac954, size: 0x314
    // 0xbac954: EnterFrame
    //     0xbac954: stp             fp, lr, [SP, #-0x10]!
    //     0xbac958: mov             fp, SP
    // 0xbac95c: AllocStack(0x38)
    //     0xbac95c: sub             SP, SP, #0x38
    // 0xbac960: SetupParameters()
    //     0xbac960: ldr             x0, [fp, #0x10]
    //     0xbac964: ldur            w2, [x0, #0x17]
    //     0xbac968: add             x2, x2, HEAP, lsl #32
    //     0xbac96c: stur            x2, [fp, #-0x18]
    // 0xbac970: CheckStackOverflow
    //     0xbac970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbac974: cmp             SP, x16
    //     0xbac978: b.ls            #0xbacc4c
    // 0xbac97c: LoadField: r3 = r2->field_b
    //     0xbac97c: ldur            w3, [x2, #0xb]
    // 0xbac980: DecompressPointer r3
    //     0xbac980: add             x3, x3, HEAP, lsl #32
    // 0xbac984: stur            x3, [fp, #-0x10]
    // 0xbac988: LoadField: r0 = r3->field_f
    //     0xbac988: ldur            w0, [x3, #0xf]
    // 0xbac98c: DecompressPointer r0
    //     0xbac98c: add             x0, x0, HEAP, lsl #32
    // 0xbac990: LoadField: r4 = r0->field_b
    //     0xbac990: ldur            w4, [x0, #0xb]
    // 0xbac994: DecompressPointer r4
    //     0xbac994: add             x4, x4, HEAP, lsl #32
    // 0xbac998: stur            x4, [fp, #-8]
    // 0xbac99c: cmp             w4, NULL
    // 0xbac9a0: b.eq            #0xbacc54
    // 0xbac9a4: LoadField: r0 = r4->field_b
    //     0xbac9a4: ldur            w0, [x4, #0xb]
    // 0xbac9a8: DecompressPointer r0
    //     0xbac9a8: add             x0, x0, HEAP, lsl #32
    // 0xbac9ac: LoadField: r1 = r0->field_b
    //     0xbac9ac: ldur            w1, [x0, #0xb]
    // 0xbac9b0: DecompressPointer r1
    //     0xbac9b0: add             x1, x1, HEAP, lsl #32
    // 0xbac9b4: cmp             w1, NULL
    // 0xbac9b8: b.ne            #0xbac9c4
    // 0xbac9bc: r0 = Null
    //     0xbac9bc: mov             x0, NULL
    // 0xbac9c0: b               #0xbaca34
    // 0xbac9c4: LoadField: r0 = r1->field_f
    //     0xbac9c4: ldur            w0, [x1, #0xf]
    // 0xbac9c8: DecompressPointer r0
    //     0xbac9c8: add             x0, x0, HEAP, lsl #32
    // 0xbac9cc: cmp             w0, NULL
    // 0xbac9d0: b.ne            #0xbac9dc
    // 0xbac9d4: r0 = Null
    //     0xbac9d4: mov             x0, NULL
    // 0xbac9d8: b               #0xbaca34
    // 0xbac9dc: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xbac9dc: ldur            w5, [x0, #0x17]
    // 0xbac9e0: DecompressPointer r5
    //     0xbac9e0: add             x5, x5, HEAP, lsl #32
    // 0xbac9e4: cmp             w5, NULL
    // 0xbac9e8: b.ne            #0xbac9f4
    // 0xbac9ec: r0 = Null
    //     0xbac9ec: mov             x0, NULL
    // 0xbac9f0: b               #0xbaca34
    // 0xbac9f4: LoadField: r0 = r2->field_f
    //     0xbac9f4: ldur            w0, [x2, #0xf]
    // 0xbac9f8: DecompressPointer r0
    //     0xbac9f8: add             x0, x0, HEAP, lsl #32
    // 0xbac9fc: LoadField: r1 = r5->field_b
    //     0xbac9fc: ldur            w1, [x5, #0xb]
    // 0xbaca00: r6 = LoadInt32Instr(r0)
    //     0xbaca00: sbfx            x6, x0, #1, #0x1f
    //     0xbaca04: tbz             w0, #0, #0xbaca0c
    //     0xbaca08: ldur            x6, [x0, #7]
    // 0xbaca0c: r0 = LoadInt32Instr(r1)
    //     0xbaca0c: sbfx            x0, x1, #1, #0x1f
    // 0xbaca10: mov             x1, x6
    // 0xbaca14: cmp             x1, x0
    // 0xbaca18: b.hs            #0xbacc58
    // 0xbaca1c: LoadField: r0 = r5->field_f
    //     0xbaca1c: ldur            w0, [x5, #0xf]
    // 0xbaca20: DecompressPointer r0
    //     0xbaca20: add             x0, x0, HEAP, lsl #32
    // 0xbaca24: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbaca24: add             x16, x0, x6, lsl #2
    //     0xbaca28: ldur            w1, [x16, #0xf]
    // 0xbaca2c: DecompressPointer r1
    //     0xbaca2c: add             x1, x1, HEAP, lsl #32
    // 0xbaca30: mov             x0, x1
    // 0xbaca34: cmp             w0, NULL
    // 0xbaca38: b.ne            #0xbaca48
    // 0xbaca3c: r0 = BEntities()
    //     0xbaca3c: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xbaca40: mov             x3, x0
    // 0xbaca44: b               #0xbaca4c
    // 0xbaca48: mov             x3, x0
    // 0xbaca4c: ldur            x2, [fp, #-8]
    // 0xbaca50: LoadField: r0 = r2->field_b
    //     0xbaca50: ldur            w0, [x2, #0xb]
    // 0xbaca54: DecompressPointer r0
    //     0xbaca54: add             x0, x0, HEAP, lsl #32
    // 0xbaca58: LoadField: r1 = r0->field_b
    //     0xbaca58: ldur            w1, [x0, #0xb]
    // 0xbaca5c: DecompressPointer r1
    //     0xbaca5c: add             x1, x1, HEAP, lsl #32
    // 0xbaca60: cmp             w1, NULL
    // 0xbaca64: b.ne            #0xbaca74
    // 0xbaca68: ldur            x5, [fp, #-0x18]
    // 0xbaca6c: r0 = Null
    //     0xbaca6c: mov             x0, NULL
    // 0xbaca70: b               #0xbacaf4
    // 0xbaca74: LoadField: r0 = r1->field_f
    //     0xbaca74: ldur            w0, [x1, #0xf]
    // 0xbaca78: DecompressPointer r0
    //     0xbaca78: add             x0, x0, HEAP, lsl #32
    // 0xbaca7c: cmp             w0, NULL
    // 0xbaca80: b.ne            #0xbaca90
    // 0xbaca84: ldur            x5, [fp, #-0x18]
    // 0xbaca88: r0 = Null
    //     0xbaca88: mov             x0, NULL
    // 0xbaca8c: b               #0xbacaf4
    // 0xbaca90: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xbaca90: ldur            w4, [x0, #0x17]
    // 0xbaca94: DecompressPointer r4
    //     0xbaca94: add             x4, x4, HEAP, lsl #32
    // 0xbaca98: cmp             w4, NULL
    // 0xbaca9c: b.ne            #0xbacaac
    // 0xbacaa0: ldur            x5, [fp, #-0x18]
    // 0xbacaa4: r0 = Null
    //     0xbacaa4: mov             x0, NULL
    // 0xbacaa8: b               #0xbacaf4
    // 0xbacaac: ldur            x5, [fp, #-0x18]
    // 0xbacab0: LoadField: r0 = r5->field_f
    //     0xbacab0: ldur            w0, [x5, #0xf]
    // 0xbacab4: DecompressPointer r0
    //     0xbacab4: add             x0, x0, HEAP, lsl #32
    // 0xbacab8: LoadField: r1 = r4->field_b
    //     0xbacab8: ldur            w1, [x4, #0xb]
    // 0xbacabc: r6 = LoadInt32Instr(r0)
    //     0xbacabc: sbfx            x6, x0, #1, #0x1f
    //     0xbacac0: tbz             w0, #0, #0xbacac8
    //     0xbacac4: ldur            x6, [x0, #7]
    // 0xbacac8: r0 = LoadInt32Instr(r1)
    //     0xbacac8: sbfx            x0, x1, #1, #0x1f
    // 0xbacacc: mov             x1, x6
    // 0xbacad0: cmp             x1, x0
    // 0xbacad4: b.hs            #0xbacc5c
    // 0xbacad8: LoadField: r0 = r4->field_f
    //     0xbacad8: ldur            w0, [x4, #0xf]
    // 0xbacadc: DecompressPointer r0
    //     0xbacadc: add             x0, x0, HEAP, lsl #32
    // 0xbacae0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbacae0: add             x16, x0, x6, lsl #2
    //     0xbacae4: ldur            w1, [x16, #0xf]
    // 0xbacae8: DecompressPointer r1
    //     0xbacae8: add             x1, x1, HEAP, lsl #32
    // 0xbacaec: LoadField: r0 = r1->field_b
    //     0xbacaec: ldur            w0, [x1, #0xb]
    // 0xbacaf0: DecompressPointer r0
    //     0xbacaf0: add             x0, x0, HEAP, lsl #32
    // 0xbacaf4: cmp             w0, NULL
    // 0xbacaf8: b.ne            #0xbacb04
    // 0xbacafc: r4 = ""
    //     0xbacafc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbacb00: b               #0xbacb08
    // 0xbacb04: mov             x4, x0
    // 0xbacb08: LoadField: r0 = r2->field_b
    //     0xbacb08: ldur            w0, [x2, #0xb]
    // 0xbacb0c: DecompressPointer r0
    //     0xbacb0c: add             x0, x0, HEAP, lsl #32
    // 0xbacb10: LoadField: r1 = r0->field_b
    //     0xbacb10: ldur            w1, [x0, #0xb]
    // 0xbacb14: DecompressPointer r1
    //     0xbacb14: add             x1, x1, HEAP, lsl #32
    // 0xbacb18: cmp             w1, NULL
    // 0xbacb1c: b.ne            #0xbacb28
    // 0xbacb20: r0 = Null
    //     0xbacb20: mov             x0, NULL
    // 0xbacb24: b               #0xbacb9c
    // 0xbacb28: LoadField: r0 = r1->field_f
    //     0xbacb28: ldur            w0, [x1, #0xf]
    // 0xbacb2c: DecompressPointer r0
    //     0xbacb2c: add             x0, x0, HEAP, lsl #32
    // 0xbacb30: cmp             w0, NULL
    // 0xbacb34: b.ne            #0xbacb40
    // 0xbacb38: r0 = Null
    //     0xbacb38: mov             x0, NULL
    // 0xbacb3c: b               #0xbacb9c
    // 0xbacb40: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xbacb40: ldur            w6, [x0, #0x17]
    // 0xbacb44: DecompressPointer r6
    //     0xbacb44: add             x6, x6, HEAP, lsl #32
    // 0xbacb48: cmp             w6, NULL
    // 0xbacb4c: b.ne            #0xbacb58
    // 0xbacb50: r0 = Null
    //     0xbacb50: mov             x0, NULL
    // 0xbacb54: b               #0xbacb9c
    // 0xbacb58: LoadField: r0 = r5->field_f
    //     0xbacb58: ldur            w0, [x5, #0xf]
    // 0xbacb5c: DecompressPointer r0
    //     0xbacb5c: add             x0, x0, HEAP, lsl #32
    // 0xbacb60: LoadField: r1 = r6->field_b
    //     0xbacb60: ldur            w1, [x6, #0xb]
    // 0xbacb64: r5 = LoadInt32Instr(r0)
    //     0xbacb64: sbfx            x5, x0, #1, #0x1f
    //     0xbacb68: tbz             w0, #0, #0xbacb70
    //     0xbacb6c: ldur            x5, [x0, #7]
    // 0xbacb70: r0 = LoadInt32Instr(r1)
    //     0xbacb70: sbfx            x0, x1, #1, #0x1f
    // 0xbacb74: mov             x1, x5
    // 0xbacb78: cmp             x1, x0
    // 0xbacb7c: b.hs            #0xbacc60
    // 0xbacb80: LoadField: r0 = r6->field_f
    //     0xbacb80: ldur            w0, [x6, #0xf]
    // 0xbacb84: DecompressPointer r0
    //     0xbacb84: add             x0, x0, HEAP, lsl #32
    // 0xbacb88: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbacb88: add             x16, x0, x5, lsl #2
    //     0xbacb8c: ldur            w1, [x16, #0xf]
    // 0xbacb90: DecompressPointer r1
    //     0xbacb90: add             x1, x1, HEAP, lsl #32
    // 0xbacb94: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbacb94: ldur            w0, [x1, #0x17]
    // 0xbacb98: DecompressPointer r0
    //     0xbacb98: add             x0, x0, HEAP, lsl #32
    // 0xbacb9c: cmp             w0, NULL
    // 0xbacba0: b.ne            #0xbacbac
    // 0xbacba4: r6 = 0
    //     0xbacba4: movz            x6, #0
    // 0xbacba8: b               #0xbacbbc
    // 0xbacbac: r1 = LoadInt32Instr(r0)
    //     0xbacbac: sbfx            x1, x0, #1, #0x1f
    //     0xbacbb0: tbz             w0, #0, #0xbacbb8
    //     0xbacbb4: ldur            x1, [x0, #7]
    // 0xbacbb8: mov             x6, x1
    // 0xbacbbc: ldur            x5, [fp, #-0x10]
    // 0xbacbc0: LoadField: r7 = r2->field_13
    //     0xbacbc0: ldur            w7, [x2, #0x13]
    // 0xbacbc4: DecompressPointer r7
    //     0xbacbc4: add             x7, x7, HEAP, lsl #32
    // 0xbacbc8: r0 = BoxInt64Instr(r6)
    //     0xbacbc8: sbfiz           x0, x6, #1, #0x1f
    //     0xbacbcc: cmp             x6, x0, asr #1
    //     0xbacbd0: b.eq            #0xbacbdc
    //     0xbacbd4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbacbd8: stur            x6, [x0, #7]
    // 0xbacbdc: stp             x3, x7, [SP, #0x10]
    // 0xbacbe0: stp             x0, x4, [SP]
    // 0xbacbe4: r4 = 0
    //     0xbacbe4: movz            x4, #0
    // 0xbacbe8: ldr             x0, [SP, #0x18]
    // 0xbacbec: r16 = UnlinkedCall_0x613b5c
    //     0xbacbec: add             x16, PP, #0x54, lsl #12  ; [pp+0x54aa0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbacbf0: add             x16, x16, #0xaa0
    // 0xbacbf4: ldp             x5, lr, [x16]
    // 0xbacbf8: blr             lr
    // 0xbacbfc: ldur            x0, [fp, #-0x10]
    // 0xbacc00: LoadField: r1 = r0->field_f
    //     0xbacc00: ldur            w1, [x0, #0xf]
    // 0xbacc04: DecompressPointer r1
    //     0xbacc04: add             x1, x1, HEAP, lsl #32
    // 0xbacc08: LoadField: r0 = r1->field_b
    //     0xbacc08: ldur            w0, [x1, #0xb]
    // 0xbacc0c: DecompressPointer r0
    //     0xbacc0c: add             x0, x0, HEAP, lsl #32
    // 0xbacc10: cmp             w0, NULL
    // 0xbacc14: b.eq            #0xbacc64
    // 0xbacc18: LoadField: r1 = r0->field_1b
    //     0xbacc18: ldur            w1, [x0, #0x1b]
    // 0xbacc1c: DecompressPointer r1
    //     0xbacc1c: add             x1, x1, HEAP, lsl #32
    // 0xbacc20: str             x1, [SP]
    // 0xbacc24: r4 = 0
    //     0xbacc24: movz            x4, #0
    // 0xbacc28: ldr             x0, [SP]
    // 0xbacc2c: r16 = UnlinkedCall_0x613b5c
    //     0xbacc2c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ab0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbacc30: add             x16, x16, #0xab0
    // 0xbacc34: ldp             x5, lr, [x16]
    // 0xbacc38: blr             lr
    // 0xbacc3c: r0 = Null
    //     0xbacc3c: mov             x0, NULL
    // 0xbacc40: LeaveFrame
    //     0xbacc40: mov             SP, fp
    //     0xbacc44: ldp             fp, lr, [SP], #0x10
    // 0xbacc48: ret
    //     0xbacc48: ret             
    // 0xbacc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbacc4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbacc50: b               #0xbac97c
    // 0xbacc54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbacc54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbacc58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbacc58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbacc5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbacc5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbacc60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbacc60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbacc64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbacc64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbacc68, size: 0x60
    // 0xbacc68: EnterFrame
    //     0xbacc68: stp             fp, lr, [SP, #-0x10]!
    //     0xbacc6c: mov             fp, SP
    // 0xbacc70: AllocStack(0x8)
    //     0xbacc70: sub             SP, SP, #8
    // 0xbacc74: SetupParameters()
    //     0xbacc74: ldr             x0, [fp, #0x10]
    //     0xbacc78: ldur            w2, [x0, #0x17]
    //     0xbacc7c: add             x2, x2, HEAP, lsl #32
    // 0xbacc80: CheckStackOverflow
    //     0xbacc80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbacc84: cmp             SP, x16
    //     0xbacc88: b.ls            #0xbaccc0
    // 0xbacc8c: LoadField: r0 = r2->field_f
    //     0xbacc8c: ldur            w0, [x2, #0xf]
    // 0xbacc90: DecompressPointer r0
    //     0xbacc90: add             x0, x0, HEAP, lsl #32
    // 0xbacc94: stur            x0, [fp, #-8]
    // 0xbacc98: r1 = Function '<anonymous closure>':.
    //     0xbacc98: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ae0] AnonymousClosure: (0xbaccc8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbacc9c: ldr             x1, [x1, #0xae0]
    // 0xbacca0: r0 = AllocateClosure()
    //     0xbacca0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbacca4: ldur            x1, [fp, #-8]
    // 0xbacca8: mov             x2, x0
    // 0xbaccac: r0 = setState()
    //     0xbaccac: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbaccb0: r0 = Null
    //     0xbaccb0: mov             x0, NULL
    // 0xbaccb4: LeaveFrame
    //     0xbaccb4: mov             SP, fp
    //     0xbaccb8: ldp             fp, lr, [SP], #0x10
    // 0xbaccbc: ret
    //     0xbaccbc: ret             
    // 0xbaccc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaccc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaccc4: b               #0xbacc8c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbaccc8, size: 0x7c
    // 0xbaccc8: EnterFrame
    //     0xbaccc8: stp             fp, lr, [SP, #-0x10]!
    //     0xbacccc: mov             fp, SP
    // 0xbaccd0: AllocStack(0x8)
    //     0xbaccd0: sub             SP, SP, #8
    // 0xbaccd4: SetupParameters()
    //     0xbaccd4: ldr             x0, [fp, #0x10]
    //     0xbaccd8: ldur            w1, [x0, #0x17]
    //     0xbaccdc: add             x1, x1, HEAP, lsl #32
    // 0xbacce0: CheckStackOverflow
    //     0xbacce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbacce4: cmp             SP, x16
    //     0xbacce8: b.ls            #0xbacd38
    // 0xbaccec: LoadField: r0 = r1->field_f
    //     0xbaccec: ldur            w0, [x1, #0xf]
    // 0xbaccf0: DecompressPointer r0
    //     0xbaccf0: add             x0, x0, HEAP, lsl #32
    // 0xbaccf4: LoadField: r1 = r0->field_b
    //     0xbaccf4: ldur            w1, [x0, #0xb]
    // 0xbaccf8: DecompressPointer r1
    //     0xbaccf8: add             x1, x1, HEAP, lsl #32
    // 0xbaccfc: cmp             w1, NULL
    // 0xbacd00: b.eq            #0xbacd40
    // 0xbacd04: LoadField: r0 = r1->field_1f
    //     0xbacd04: ldur            w0, [x1, #0x1f]
    // 0xbacd08: DecompressPointer r0
    //     0xbacd08: add             x0, x0, HEAP, lsl #32
    // 0xbacd0c: str             x0, [SP]
    // 0xbacd10: r4 = 0
    //     0xbacd10: movz            x4, #0
    // 0xbacd14: ldr             x0, [SP]
    // 0xbacd18: r16 = UnlinkedCall_0x613b5c
    //     0xbacd18: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ae8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbacd1c: add             x16, x16, #0xae8
    // 0xbacd20: ldp             x5, lr, [x16]
    // 0xbacd24: blr             lr
    // 0xbacd28: r0 = Null
    //     0xbacd28: mov             x0, NULL
    // 0xbacd2c: LeaveFrame
    //     0xbacd2c: mov             SP, fp
    //     0xbacd30: ldp             fp, lr, [SP], #0x10
    // 0xbacd34: ret
    //     0xbacd34: ret             
    // 0xbacd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbacd38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbacd3c: b               #0xbaccec
    // 0xbacd40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbacd40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbacd44, size: 0x60
    // 0xbacd44: EnterFrame
    //     0xbacd44: stp             fp, lr, [SP, #-0x10]!
    //     0xbacd48: mov             fp, SP
    // 0xbacd4c: AllocStack(0x8)
    //     0xbacd4c: sub             SP, SP, #8
    // 0xbacd50: SetupParameters()
    //     0xbacd50: ldr             x0, [fp, #0x10]
    //     0xbacd54: ldur            w2, [x0, #0x17]
    //     0xbacd58: add             x2, x2, HEAP, lsl #32
    // 0xbacd5c: CheckStackOverflow
    //     0xbacd5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbacd60: cmp             SP, x16
    //     0xbacd64: b.ls            #0xbacd9c
    // 0xbacd68: LoadField: r0 = r2->field_f
    //     0xbacd68: ldur            w0, [x2, #0xf]
    // 0xbacd6c: DecompressPointer r0
    //     0xbacd6c: add             x0, x0, HEAP, lsl #32
    // 0xbacd70: stur            x0, [fp, #-8]
    // 0xbacd74: r1 = Function '<anonymous closure>':.
    //     0xbacd74: add             x1, PP, #0x54, lsl #12  ; [pp+0x54af8] AnonymousClosure: (0xbacda4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xbacd78: ldr             x1, [x1, #0xaf8]
    // 0xbacd7c: r0 = AllocateClosure()
    //     0xbacd7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbacd80: ldur            x1, [fp, #-8]
    // 0xbacd84: mov             x2, x0
    // 0xbacd88: r0 = setState()
    //     0xbacd88: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbacd8c: r0 = Null
    //     0xbacd8c: mov             x0, NULL
    // 0xbacd90: LeaveFrame
    //     0xbacd90: mov             SP, fp
    //     0xbacd94: ldp             fp, lr, [SP], #0x10
    // 0xbacd98: ret
    //     0xbacd98: ret             
    // 0xbacd9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbacd9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbacda0: b               #0xbacd68
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbacda4, size: 0x7c
    // 0xbacda4: EnterFrame
    //     0xbacda4: stp             fp, lr, [SP, #-0x10]!
    //     0xbacda8: mov             fp, SP
    // 0xbacdac: AllocStack(0x8)
    //     0xbacdac: sub             SP, SP, #8
    // 0xbacdb0: SetupParameters()
    //     0xbacdb0: ldr             x0, [fp, #0x10]
    //     0xbacdb4: ldur            w1, [x0, #0x17]
    //     0xbacdb8: add             x1, x1, HEAP, lsl #32
    // 0xbacdbc: CheckStackOverflow
    //     0xbacdbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbacdc0: cmp             SP, x16
    //     0xbacdc4: b.ls            #0xbace14
    // 0xbacdc8: LoadField: r0 = r1->field_f
    //     0xbacdc8: ldur            w0, [x1, #0xf]
    // 0xbacdcc: DecompressPointer r0
    //     0xbacdcc: add             x0, x0, HEAP, lsl #32
    // 0xbacdd0: LoadField: r1 = r0->field_b
    //     0xbacdd0: ldur            w1, [x0, #0xb]
    // 0xbacdd4: DecompressPointer r1
    //     0xbacdd4: add             x1, x1, HEAP, lsl #32
    // 0xbacdd8: cmp             w1, NULL
    // 0xbacddc: b.eq            #0xbace1c
    // 0xbacde0: LoadField: r0 = r1->field_23
    //     0xbacde0: ldur            w0, [x1, #0x23]
    // 0xbacde4: DecompressPointer r0
    //     0xbacde4: add             x0, x0, HEAP, lsl #32
    // 0xbacde8: str             x0, [SP]
    // 0xbacdec: r4 = 0
    //     0xbacdec: movz            x4, #0
    // 0xbacdf0: ldr             x0, [SP]
    // 0xbacdf4: r16 = UnlinkedCall_0x613b5c
    //     0xbacdf4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54b00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbacdf8: add             x16, x16, #0xb00
    // 0xbacdfc: ldp             x5, lr, [x16]
    // 0xbace00: blr             lr
    // 0xbace04: r0 = Null
    //     0xbace04: mov             x0, NULL
    // 0xbace08: LeaveFrame
    //     0xbace08: mov             SP, fp
    //     0xbace0c: ldp             fp, lr, [SP], #0x10
    // 0xbace10: ret
    //     0xbace10: ret             
    // 0xbace14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbace14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbace18: b               #0xbacdc8
    // 0xbace1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbace1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4027, size: 0x28, field offset: 0xc
class BagDetailWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc800b0, size: 0x24
    // 0xc800b0: EnterFrame
    //     0xc800b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc800b4: mov             fp, SP
    // 0xc800b8: mov             x0, x1
    // 0xc800bc: r1 = <BagDetailWidget>
    //     0xc800bc: add             x1, PP, #0x48, lsl #12  ; [pp+0x486c0] TypeArguments: <BagDetailWidget>
    //     0xc800c0: ldr             x1, [x1, #0x6c0]
    // 0xc800c4: r0 = _BagDetailWidgetState()
    //     0xc800c4: bl              #0xc800d4  ; Allocate_BagDetailWidgetStateStub -> _BagDetailWidgetState (size=0x14)
    // 0xc800c8: LeaveFrame
    //     0xc800c8: mov             SP, fp
    //     0xc800cc: ldp             fp, lr, [SP], #0x10
    // 0xc800d0: ret
    //     0xc800d0: ret             
  }
}
