// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart

// class id: 1049316, size: 0x8
class :: {
}

// class id: 3401, size: 0x2c, field offset: 0x14
class _ProductSelectSizeBottomSheetState extends State<dynamic> {

  late AllSkuDatum dropDownValue; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93c6c8, size: 0x51c
    // 0x93c6c8: EnterFrame
    //     0x93c6c8: stp             fp, lr, [SP, #-0x10]!
    //     0x93c6cc: mov             fp, SP
    // 0x93c6d0: AllocStack(0x38)
    //     0x93c6d0: sub             SP, SP, #0x38
    // 0x93c6d4: SetupParameters(_ProductSelectSizeBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x93c6d4: stur            x1, [fp, #-8]
    // 0x93c6d8: CheckStackOverflow
    //     0x93c6d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93c6dc: cmp             SP, x16
    //     0x93c6e0: b.ls            #0x93cbb8
    // 0x93c6e4: r1 = 1
    //     0x93c6e4: movz            x1, #0x1
    // 0x93c6e8: r0 = AllocateContext()
    //     0x93c6e8: bl              #0x16f6108  ; AllocateContextStub
    // 0x93c6ec: mov             x1, x0
    // 0x93c6f0: ldur            x0, [fp, #-8]
    // 0x93c6f4: stur            x1, [fp, #-0x10]
    // 0x93c6f8: StoreField: r1->field_f = r0
    //     0x93c6f8: stur            w0, [x1, #0xf]
    // 0x93c6fc: LoadField: r2 = r0->field_b
    //     0x93c6fc: ldur            w2, [x0, #0xb]
    // 0x93c700: DecompressPointer r2
    //     0x93c700: add             x2, x2, HEAP, lsl #32
    // 0x93c704: cmp             w2, NULL
    // 0x93c708: b.eq            #0x93cbc0
    // 0x93c70c: LoadField: r3 = r2->field_b
    //     0x93c70c: ldur            w3, [x2, #0xb]
    // 0x93c710: DecompressPointer r3
    //     0x93c710: add             x3, x3, HEAP, lsl #32
    // 0x93c714: LoadField: r2 = r3->field_db
    //     0x93c714: ldur            w2, [x3, #0xdb]
    // 0x93c718: DecompressPointer r2
    //     0x93c718: add             x2, x2, HEAP, lsl #32
    // 0x93c71c: cmp             w2, NULL
    // 0x93c720: b.ne            #0x93c72c
    // 0x93c724: r4 = Null
    //     0x93c724: mov             x4, NULL
    // 0x93c728: b               #0x93c744
    // 0x93c72c: LoadField: r4 = r2->field_b
    //     0x93c72c: ldur            w4, [x2, #0xb]
    // 0x93c730: cbnz            w4, #0x93c73c
    // 0x93c734: r5 = false
    //     0x93c734: add             x5, NULL, #0x30  ; false
    // 0x93c738: b               #0x93c740
    // 0x93c73c: r5 = true
    //     0x93c73c: add             x5, NULL, #0x20  ; true
    // 0x93c740: mov             x4, x5
    // 0x93c744: cmp             w4, NULL
    // 0x93c748: b.ne            #0x93c754
    // 0x93c74c: mov             x1, x0
    // 0x93c750: b               #0x93ca14
    // 0x93c754: tbnz            w4, #4, #0x93ca10
    // 0x93c758: cmp             w2, NULL
    // 0x93c75c: b.ne            #0x93c768
    // 0x93c760: r2 = Null
    //     0x93c760: mov             x2, NULL
    // 0x93c764: b               #0x93c7c0
    // 0x93c768: r16 = <AllGroupedSkusDatum?>
    //     0x93c768: add             x16, PP, #0x52, lsl #12  ; [pp+0x52850] TypeArguments: <AllGroupedSkusDatum?>
    //     0x93c76c: ldr             x16, [x16, #0x850]
    // 0x93c770: stp             x2, x16, [SP]
    // 0x93c774: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x93c774: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x93c778: r0 = cast()
    //     0x93c778: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x93c77c: ldur            x2, [fp, #-0x10]
    // 0x93c780: r1 = Function '<anonymous closure>':.
    //     0x93c780: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c38] AnonymousClosure: (0x934af0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::initState (0x94aa18)
    //     0x93c784: ldr             x1, [x1, #0xc38]
    // 0x93c788: stur            x0, [fp, #-0x18]
    // 0x93c78c: r0 = AllocateClosure()
    //     0x93c78c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93c790: r1 = Function '<anonymous closure>':.
    //     0x93c790: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c40] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x93c794: ldr             x1, [x1, #0xc40]
    // 0x93c798: r2 = Null
    //     0x93c798: mov             x2, NULL
    // 0x93c79c: stur            x0, [fp, #-0x20]
    // 0x93c7a0: r0 = AllocateClosure()
    //     0x93c7a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93c7a4: str             x0, [SP]
    // 0x93c7a8: ldur            x1, [fp, #-0x18]
    // 0x93c7ac: ldur            x2, [fp, #-0x20]
    // 0x93c7b0: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x93c7b0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x93c7b4: ldr             x4, [x4, #0xb48]
    // 0x93c7b8: r0 = firstWhere()
    //     0x93c7b8: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x93c7bc: mov             x2, x0
    // 0x93c7c0: cmp             w2, NULL
    // 0x93c7c4: b.eq            #0x93c84c
    // 0x93c7c8: ldur            x0, [fp, #-8]
    // 0x93c7cc: LoadField: r1 = r0->field_b
    //     0x93c7cc: ldur            w1, [x0, #0xb]
    // 0x93c7d0: DecompressPointer r1
    //     0x93c7d0: add             x1, x1, HEAP, lsl #32
    // 0x93c7d4: cmp             w1, NULL
    // 0x93c7d8: b.eq            #0x93cbc4
    // 0x93c7dc: LoadField: r3 = r1->field_b
    //     0x93c7dc: ldur            w3, [x1, #0xb]
    // 0x93c7e0: DecompressPointer r3
    //     0x93c7e0: add             x3, x3, HEAP, lsl #32
    // 0x93c7e4: LoadField: r1 = r3->field_db
    //     0x93c7e4: ldur            w1, [x3, #0xdb]
    // 0x93c7e8: DecompressPointer r1
    //     0x93c7e8: add             x1, x1, HEAP, lsl #32
    // 0x93c7ec: cmp             w1, NULL
    // 0x93c7f0: b.ne            #0x93c7fc
    // 0x93c7f4: r0 = Null
    //     0x93c7f4: mov             x0, NULL
    // 0x93c7f8: b               #0x93c81c
    // 0x93c7fc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93c7fc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93c800: r0 = indexOf()
    //     0x93c800: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x93c804: mov             x2, x0
    // 0x93c808: r0 = BoxInt64Instr(r2)
    //     0x93c808: sbfiz           x0, x2, #1, #0x1f
    //     0x93c80c: cmp             x2, x0, asr #1
    //     0x93c810: b.eq            #0x93c81c
    //     0x93c814: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93c818: stur            x2, [x0, #7]
    // 0x93c81c: cmp             w0, NULL
    // 0x93c820: b.ne            #0x93c82c
    // 0x93c824: r0 = 0
    //     0x93c824: movz            x0, #0
    // 0x93c828: b               #0x93c83c
    // 0x93c82c: r1 = LoadInt32Instr(r0)
    //     0x93c82c: sbfx            x1, x0, #1, #0x1f
    //     0x93c830: tbz             w0, #0, #0x93c838
    //     0x93c834: ldur            x1, [x0, #7]
    // 0x93c838: mov             x0, x1
    // 0x93c83c: ldur            x2, [fp, #-8]
    // 0x93c840: ArrayStore: r2[0] = r0  ; List_8
    //     0x93c840: stur            x0, [x2, #0x17]
    // 0x93c844: mov             x3, x0
    // 0x93c848: b               #0x93c858
    // 0x93c84c: ldur            x2, [fp, #-8]
    // 0x93c850: ArrayStore: r2[0] = rZR  ; List_8
    //     0x93c850: stur            xzr, [x2, #0x17]
    // 0x93c854: r3 = 0
    //     0x93c854: movz            x3, #0
    // 0x93c858: stur            x3, [fp, #-0x28]
    // 0x93c85c: LoadField: r0 = r2->field_b
    //     0x93c85c: ldur            w0, [x2, #0xb]
    // 0x93c860: DecompressPointer r0
    //     0x93c860: add             x0, x0, HEAP, lsl #32
    // 0x93c864: cmp             w0, NULL
    // 0x93c868: b.eq            #0x93cbc8
    // 0x93c86c: LoadField: r1 = r0->field_b
    //     0x93c86c: ldur            w1, [x0, #0xb]
    // 0x93c870: DecompressPointer r1
    //     0x93c870: add             x1, x1, HEAP, lsl #32
    // 0x93c874: LoadField: r4 = r1->field_db
    //     0x93c874: ldur            w4, [x1, #0xdb]
    // 0x93c878: DecompressPointer r4
    //     0x93c878: add             x4, x4, HEAP, lsl #32
    // 0x93c87c: stur            x4, [fp, #-0x18]
    // 0x93c880: cmp             w4, NULL
    // 0x93c884: b.ne            #0x93c890
    // 0x93c888: r0 = Null
    //     0x93c888: mov             x0, NULL
    // 0x93c88c: b               #0x93c904
    // 0x93c890: LoadField: r0 = r4->field_b
    //     0x93c890: ldur            w0, [x4, #0xb]
    // 0x93c894: r1 = LoadInt32Instr(r0)
    //     0x93c894: sbfx            x1, x0, #1, #0x1f
    // 0x93c898: mov             x0, x1
    // 0x93c89c: mov             x1, x3
    // 0x93c8a0: cmp             x1, x0
    // 0x93c8a4: b.hs            #0x93cbcc
    // 0x93c8a8: LoadField: r0 = r4->field_f
    //     0x93c8a8: ldur            w0, [x4, #0xf]
    // 0x93c8ac: DecompressPointer r0
    //     0x93c8ac: add             x0, x0, HEAP, lsl #32
    // 0x93c8b0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x93c8b0: add             x16, x0, x3, lsl #2
    //     0x93c8b4: ldur            w1, [x16, #0xf]
    // 0x93c8b8: DecompressPointer r1
    //     0x93c8b8: add             x1, x1, HEAP, lsl #32
    // 0x93c8bc: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x93c8bc: ldur            w5, [x1, #0x17]
    // 0x93c8c0: DecompressPointer r5
    //     0x93c8c0: add             x5, x5, HEAP, lsl #32
    // 0x93c8c4: cmp             w5, NULL
    // 0x93c8c8: b.ne            #0x93c8d4
    // 0x93c8cc: r0 = Null
    //     0x93c8cc: mov             x0, NULL
    // 0x93c8d0: b               #0x93c904
    // 0x93c8d4: LoadField: r0 = r5->field_b
    //     0x93c8d4: ldur            w0, [x5, #0xb]
    // 0x93c8d8: r1 = LoadInt32Instr(r0)
    //     0x93c8d8: sbfx            x1, x0, #1, #0x1f
    // 0x93c8dc: mov             x0, x1
    // 0x93c8e0: mov             x1, x3
    // 0x93c8e4: cmp             x1, x0
    // 0x93c8e8: b.hs            #0x93cbd0
    // 0x93c8ec: LoadField: r0 = r5->field_f
    //     0x93c8ec: ldur            w0, [x5, #0xf]
    // 0x93c8f0: DecompressPointer r0
    //     0x93c8f0: add             x0, x0, HEAP, lsl #32
    // 0x93c8f4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x93c8f4: add             x16, x0, x3, lsl #2
    //     0x93c8f8: ldur            w1, [x16, #0xf]
    // 0x93c8fc: DecompressPointer r1
    //     0x93c8fc: add             x1, x1, HEAP, lsl #32
    // 0x93c900: mov             x0, x1
    // 0x93c904: cmp             w0, NULL
    // 0x93c908: b.ne            #0x93c910
    // 0x93c90c: r0 = AllSkuDatum()
    //     0x93c90c: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x93c910: ldur            x1, [fp, #-8]
    // 0x93c914: ldur            x2, [fp, #-0x18]
    // 0x93c918: StoreField: r1->field_13 = r0
    //     0x93c918: stur            w0, [x1, #0x13]
    //     0x93c91c: ldurb           w16, [x1, #-1]
    //     0x93c920: ldurb           w17, [x0, #-1]
    //     0x93c924: and             x16, x17, x16, lsr #2
    //     0x93c928: tst             x16, HEAP, lsr #32
    //     0x93c92c: b.eq            #0x93c934
    //     0x93c930: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93c934: LoadField: r3 = r1->field_27
    //     0x93c934: ldur            w3, [x1, #0x27]
    // 0x93c938: DecompressPointer r3
    //     0x93c938: add             x3, x3, HEAP, lsl #32
    // 0x93c93c: stur            x3, [fp, #-0x20]
    // 0x93c940: cmp             w2, NULL
    // 0x93c944: b.ne            #0x93c950
    // 0x93c948: r0 = Null
    //     0x93c948: mov             x0, NULL
    // 0x93c94c: b               #0x93c988
    // 0x93c950: ldur            x4, [fp, #-0x28]
    // 0x93c954: LoadField: r0 = r2->field_b
    //     0x93c954: ldur            w0, [x2, #0xb]
    // 0x93c958: r1 = LoadInt32Instr(r0)
    //     0x93c958: sbfx            x1, x0, #1, #0x1f
    // 0x93c95c: mov             x0, x1
    // 0x93c960: mov             x1, x4
    // 0x93c964: cmp             x1, x0
    // 0x93c968: b.hs            #0x93cbd4
    // 0x93c96c: LoadField: r0 = r2->field_f
    //     0x93c96c: ldur            w0, [x2, #0xf]
    // 0x93c970: DecompressPointer r0
    //     0x93c970: add             x0, x0, HEAP, lsl #32
    // 0x93c974: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x93c974: add             x16, x0, x4, lsl #2
    //     0x93c978: ldur            w1, [x16, #0xf]
    // 0x93c97c: DecompressPointer r1
    //     0x93c97c: add             x1, x1, HEAP, lsl #32
    // 0x93c980: LoadField: r0 = r1->field_7
    //     0x93c980: ldur            w0, [x1, #7]
    // 0x93c984: DecompressPointer r0
    //     0x93c984: add             x0, x0, HEAP, lsl #32
    // 0x93c988: cmp             w0, NULL
    // 0x93c98c: b.ne            #0x93c994
    // 0x93c990: r0 = ""
    //     0x93c990: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93c994: stur            x0, [fp, #-0x18]
    // 0x93c998: LoadField: r1 = r3->field_b
    //     0x93c998: ldur            w1, [x3, #0xb]
    // 0x93c99c: LoadField: r2 = r3->field_f
    //     0x93c99c: ldur            w2, [x3, #0xf]
    // 0x93c9a0: DecompressPointer r2
    //     0x93c9a0: add             x2, x2, HEAP, lsl #32
    // 0x93c9a4: LoadField: r4 = r2->field_b
    //     0x93c9a4: ldur            w4, [x2, #0xb]
    // 0x93c9a8: r2 = LoadInt32Instr(r1)
    //     0x93c9a8: sbfx            x2, x1, #1, #0x1f
    // 0x93c9ac: stur            x2, [fp, #-0x28]
    // 0x93c9b0: r1 = LoadInt32Instr(r4)
    //     0x93c9b0: sbfx            x1, x4, #1, #0x1f
    // 0x93c9b4: cmp             x2, x1
    // 0x93c9b8: b.ne            #0x93c9c4
    // 0x93c9bc: mov             x1, x3
    // 0x93c9c0: r0 = _growToNextCapacity()
    //     0x93c9c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93c9c4: ldur            x0, [fp, #-0x20]
    // 0x93c9c8: ldur            x2, [fp, #-0x28]
    // 0x93c9cc: add             x1, x2, #1
    // 0x93c9d0: lsl             x3, x1, #1
    // 0x93c9d4: StoreField: r0->field_b = r3
    //     0x93c9d4: stur            w3, [x0, #0xb]
    // 0x93c9d8: LoadField: r1 = r0->field_f
    //     0x93c9d8: ldur            w1, [x0, #0xf]
    // 0x93c9dc: DecompressPointer r1
    //     0x93c9dc: add             x1, x1, HEAP, lsl #32
    // 0x93c9e0: ldur            x0, [fp, #-0x18]
    // 0x93c9e4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93c9e4: add             x25, x1, x2, lsl #2
    //     0x93c9e8: add             x25, x25, #0xf
    //     0x93c9ec: str             w0, [x25]
    //     0x93c9f0: tbz             w0, #0, #0x93ca0c
    //     0x93c9f4: ldurb           w16, [x1, #-1]
    //     0x93c9f8: ldurb           w17, [x0, #-1]
    //     0x93c9fc: and             x16, x17, x16, lsr #2
    //     0x93ca00: tst             x16, HEAP, lsr #32
    //     0x93ca04: b.eq            #0x93ca0c
    //     0x93ca08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93ca0c: b               #0x93cba8
    // 0x93ca10: mov             x1, x0
    // 0x93ca14: LoadField: r0 = r3->field_d7
    //     0x93ca14: ldur            w0, [x3, #0xd7]
    // 0x93ca18: DecompressPointer r0
    //     0x93ca18: add             x0, x0, HEAP, lsl #32
    // 0x93ca1c: cmp             w0, NULL
    // 0x93ca20: b.ne            #0x93ca2c
    // 0x93ca24: r2 = Null
    //     0x93ca24: mov             x2, NULL
    // 0x93ca28: b               #0x93ca84
    // 0x93ca2c: r16 = <AllSkuDatum?>
    //     0x93ca2c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52868] TypeArguments: <AllSkuDatum?>
    //     0x93ca30: ldr             x16, [x16, #0x868]
    // 0x93ca34: stp             x0, x16, [SP]
    // 0x93ca38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x93ca38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x93ca3c: r0 = cast()
    //     0x93ca3c: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x93ca40: ldur            x2, [fp, #-0x10]
    // 0x93ca44: r1 = Function '<anonymous closure>':.
    //     0x93ca44: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c48] AnonymousClosure: (0x934a14), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::initState (0x94aa18)
    //     0x93ca48: ldr             x1, [x1, #0xc48]
    // 0x93ca4c: stur            x0, [fp, #-0x10]
    // 0x93ca50: r0 = AllocateClosure()
    //     0x93ca50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ca54: r1 = Function '<anonymous closure>':.
    //     0x93ca54: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c50] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x93ca58: ldr             x1, [x1, #0xc50]
    // 0x93ca5c: r2 = Null
    //     0x93ca5c: mov             x2, NULL
    // 0x93ca60: stur            x0, [fp, #-0x18]
    // 0x93ca64: r0 = AllocateClosure()
    //     0x93ca64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ca68: str             x0, [SP]
    // 0x93ca6c: ldur            x1, [fp, #-0x10]
    // 0x93ca70: ldur            x2, [fp, #-0x18]
    // 0x93ca74: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x93ca74: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x93ca78: ldr             x4, [x4, #0xb48]
    // 0x93ca7c: r0 = firstWhere()
    //     0x93ca7c: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x93ca80: mov             x2, x0
    // 0x93ca84: cmp             w2, NULL
    // 0x93ca88: b.eq            #0x93cb10
    // 0x93ca8c: ldur            x0, [fp, #-8]
    // 0x93ca90: LoadField: r1 = r0->field_b
    //     0x93ca90: ldur            w1, [x0, #0xb]
    // 0x93ca94: DecompressPointer r1
    //     0x93ca94: add             x1, x1, HEAP, lsl #32
    // 0x93ca98: cmp             w1, NULL
    // 0x93ca9c: b.eq            #0x93cbd8
    // 0x93caa0: LoadField: r3 = r1->field_b
    //     0x93caa0: ldur            w3, [x1, #0xb]
    // 0x93caa4: DecompressPointer r3
    //     0x93caa4: add             x3, x3, HEAP, lsl #32
    // 0x93caa8: LoadField: r1 = r3->field_d7
    //     0x93caa8: ldur            w1, [x3, #0xd7]
    // 0x93caac: DecompressPointer r1
    //     0x93caac: add             x1, x1, HEAP, lsl #32
    // 0x93cab0: cmp             w1, NULL
    // 0x93cab4: b.ne            #0x93cac0
    // 0x93cab8: r0 = Null
    //     0x93cab8: mov             x0, NULL
    // 0x93cabc: b               #0x93cae0
    // 0x93cac0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93cac0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93cac4: r0 = indexOf()
    //     0x93cac4: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x93cac8: mov             x2, x0
    // 0x93cacc: r0 = BoxInt64Instr(r2)
    //     0x93cacc: sbfiz           x0, x2, #1, #0x1f
    //     0x93cad0: cmp             x2, x0, asr #1
    //     0x93cad4: b.eq            #0x93cae0
    //     0x93cad8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93cadc: stur            x2, [x0, #7]
    // 0x93cae0: cmp             w0, NULL
    // 0x93cae4: b.ne            #0x93caf0
    // 0x93cae8: r0 = 0
    //     0x93cae8: movz            x0, #0
    // 0x93caec: b               #0x93cb00
    // 0x93caf0: r1 = LoadInt32Instr(r0)
    //     0x93caf0: sbfx            x1, x0, #1, #0x1f
    //     0x93caf4: tbz             w0, #0, #0x93cafc
    //     0x93caf8: ldur            x1, [x0, #7]
    // 0x93cafc: mov             x0, x1
    // 0x93cb00: ldur            x2, [fp, #-8]
    // 0x93cb04: ArrayStore: r2[0] = r0  ; List_8
    //     0x93cb04: stur            x0, [x2, #0x17]
    // 0x93cb08: mov             x3, x0
    // 0x93cb0c: b               #0x93cb1c
    // 0x93cb10: ldur            x2, [fp, #-8]
    // 0x93cb14: ArrayStore: r2[0] = rZR  ; List_8
    //     0x93cb14: stur            xzr, [x2, #0x17]
    // 0x93cb18: r3 = 0
    //     0x93cb18: movz            x3, #0
    // 0x93cb1c: LoadField: r0 = r2->field_b
    //     0x93cb1c: ldur            w0, [x2, #0xb]
    // 0x93cb20: DecompressPointer r0
    //     0x93cb20: add             x0, x0, HEAP, lsl #32
    // 0x93cb24: cmp             w0, NULL
    // 0x93cb28: b.eq            #0x93cbdc
    // 0x93cb2c: LoadField: r1 = r0->field_b
    //     0x93cb2c: ldur            w1, [x0, #0xb]
    // 0x93cb30: DecompressPointer r1
    //     0x93cb30: add             x1, x1, HEAP, lsl #32
    // 0x93cb34: LoadField: r4 = r1->field_d7
    //     0x93cb34: ldur            w4, [x1, #0xd7]
    // 0x93cb38: DecompressPointer r4
    //     0x93cb38: add             x4, x4, HEAP, lsl #32
    // 0x93cb3c: cmp             w4, NULL
    // 0x93cb40: b.ne            #0x93cb4c
    // 0x93cb44: r0 = Null
    //     0x93cb44: mov             x0, NULL
    // 0x93cb48: b               #0x93cb7c
    // 0x93cb4c: LoadField: r0 = r4->field_b
    //     0x93cb4c: ldur            w0, [x4, #0xb]
    // 0x93cb50: r1 = LoadInt32Instr(r0)
    //     0x93cb50: sbfx            x1, x0, #1, #0x1f
    // 0x93cb54: mov             x0, x1
    // 0x93cb58: mov             x1, x3
    // 0x93cb5c: cmp             x1, x0
    // 0x93cb60: b.hs            #0x93cbe0
    // 0x93cb64: LoadField: r0 = r4->field_f
    //     0x93cb64: ldur            w0, [x4, #0xf]
    // 0x93cb68: DecompressPointer r0
    //     0x93cb68: add             x0, x0, HEAP, lsl #32
    // 0x93cb6c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x93cb6c: add             x16, x0, x3, lsl #2
    //     0x93cb70: ldur            w1, [x16, #0xf]
    // 0x93cb74: DecompressPointer r1
    //     0x93cb74: add             x1, x1, HEAP, lsl #32
    // 0x93cb78: mov             x0, x1
    // 0x93cb7c: cmp             w0, NULL
    // 0x93cb80: b.ne            #0x93cb88
    // 0x93cb84: r0 = AllSkuDatum()
    //     0x93cb84: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x93cb88: ldur            x1, [fp, #-8]
    // 0x93cb8c: StoreField: r1->field_13 = r0
    //     0x93cb8c: stur            w0, [x1, #0x13]
    //     0x93cb90: ldurb           w16, [x1, #-1]
    //     0x93cb94: ldurb           w17, [x0, #-1]
    //     0x93cb98: and             x16, x17, x16, lsr #2
    //     0x93cb9c: tst             x16, HEAP, lsr #32
    //     0x93cba0: b.eq            #0x93cba8
    //     0x93cba4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93cba8: r0 = Null
    //     0x93cba8: mov             x0, NULL
    // 0x93cbac: LeaveFrame
    //     0x93cbac: mov             SP, fp
    //     0x93cbb0: ldp             fp, lr, [SP], #0x10
    // 0x93cbb4: ret
    //     0x93cbb4: ret             
    // 0x93cbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93cbb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93cbbc: b               #0x93c6e4
    // 0x93cbc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cbc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cbc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cbc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cbc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cbc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cbcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93cbcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93cbd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93cbd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93cbd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93cbd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93cbd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cbd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cbdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cbdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cbe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93cbe0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb0e09c, size: 0xbac
    // 0xb0e09c: EnterFrame
    //     0xb0e09c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0e0a0: mov             fp, SP
    // 0xb0e0a4: AllocStack(0x50)
    //     0xb0e0a4: sub             SP, SP, #0x50
    // 0xb0e0a8: SetupParameters(_ProductSelectSizeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb0e0a8: mov             x0, x1
    //     0xb0e0ac: stur            x1, [fp, #-8]
    //     0xb0e0b0: mov             x1, x2
    //     0xb0e0b4: stur            x2, [fp, #-0x10]
    // 0xb0e0b8: CheckStackOverflow
    //     0xb0e0b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0e0bc: cmp             SP, x16
    //     0xb0e0c0: b.ls            #0xb0ec2c
    // 0xb0e0c4: r1 = 2
    //     0xb0e0c4: movz            x1, #0x2
    // 0xb0e0c8: r0 = AllocateContext()
    //     0xb0e0c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0e0cc: mov             x2, x0
    // 0xb0e0d0: ldur            x0, [fp, #-8]
    // 0xb0e0d4: stur            x2, [fp, #-0x18]
    // 0xb0e0d8: StoreField: r2->field_f = r0
    //     0xb0e0d8: stur            w0, [x2, #0xf]
    // 0xb0e0dc: ldur            x1, [fp, #-0x10]
    // 0xb0e0e0: StoreField: r2->field_13 = r1
    //     0xb0e0e0: stur            w1, [x2, #0x13]
    // 0xb0e0e4: r0 = of()
    //     0xb0e0e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0e0e8: LoadField: r1 = r0->field_87
    //     0xb0e0e8: ldur            w1, [x0, #0x87]
    // 0xb0e0ec: DecompressPointer r1
    //     0xb0e0ec: add             x1, x1, HEAP, lsl #32
    // 0xb0e0f0: LoadField: r0 = r1->field_7
    //     0xb0e0f0: ldur            w0, [x1, #7]
    // 0xb0e0f4: DecompressPointer r0
    //     0xb0e0f4: add             x0, x0, HEAP, lsl #32
    // 0xb0e0f8: r16 = Instance_Color
    //     0xb0e0f8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0e0fc: r30 = 16.000000
    //     0xb0e0fc: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0e100: ldr             lr, [lr, #0x188]
    // 0xb0e104: stp             lr, x16, [SP]
    // 0xb0e108: mov             x1, x0
    // 0xb0e10c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb0e10c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb0e110: ldr             x4, [x4, #0x9b8]
    // 0xb0e114: r0 = copyWith()
    //     0xb0e114: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0e118: stur            x0, [fp, #-0x10]
    // 0xb0e11c: r0 = Text()
    //     0xb0e11c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0e120: mov             x1, x0
    // 0xb0e124: r0 = "Select Preference"
    //     0xb0e124: add             x0, PP, #0x52, lsl #12  ; [pp+0x52710] "Select Preference"
    //     0xb0e128: ldr             x0, [x0, #0x710]
    // 0xb0e12c: stur            x1, [fp, #-0x20]
    // 0xb0e130: StoreField: r1->field_b = r0
    //     0xb0e130: stur            w0, [x1, #0xb]
    // 0xb0e134: ldur            x0, [fp, #-0x10]
    // 0xb0e138: StoreField: r1->field_13 = r0
    //     0xb0e138: stur            w0, [x1, #0x13]
    // 0xb0e13c: r0 = InkWell()
    //     0xb0e13c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb0e140: mov             x3, x0
    // 0xb0e144: r0 = Instance_Icon
    //     0xb0e144: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb0e148: ldr             x0, [x0, #0x2b8]
    // 0xb0e14c: stur            x3, [fp, #-0x10]
    // 0xb0e150: StoreField: r3->field_b = r0
    //     0xb0e150: stur            w0, [x3, #0xb]
    // 0xb0e154: ldur            x2, [fp, #-0x18]
    // 0xb0e158: r1 = Function '<anonymous closure>':.
    //     0xb0e158: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ba8] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb0e15c: ldr             x1, [x1, #0xba8]
    // 0xb0e160: r0 = AllocateClosure()
    //     0xb0e160: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0e164: mov             x1, x0
    // 0xb0e168: ldur            x0, [fp, #-0x10]
    // 0xb0e16c: StoreField: r0->field_f = r1
    //     0xb0e16c: stur            w1, [x0, #0xf]
    // 0xb0e170: r3 = true
    //     0xb0e170: add             x3, NULL, #0x20  ; true
    // 0xb0e174: StoreField: r0->field_43 = r3
    //     0xb0e174: stur            w3, [x0, #0x43]
    // 0xb0e178: r1 = Instance_BoxShape
    //     0xb0e178: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0e17c: ldr             x1, [x1, #0x80]
    // 0xb0e180: StoreField: r0->field_47 = r1
    //     0xb0e180: stur            w1, [x0, #0x47]
    // 0xb0e184: StoreField: r0->field_6f = r3
    //     0xb0e184: stur            w3, [x0, #0x6f]
    // 0xb0e188: r4 = false
    //     0xb0e188: add             x4, NULL, #0x30  ; false
    // 0xb0e18c: StoreField: r0->field_73 = r4
    //     0xb0e18c: stur            w4, [x0, #0x73]
    // 0xb0e190: StoreField: r0->field_83 = r3
    //     0xb0e190: stur            w3, [x0, #0x83]
    // 0xb0e194: StoreField: r0->field_7b = r4
    //     0xb0e194: stur            w4, [x0, #0x7b]
    // 0xb0e198: r1 = Null
    //     0xb0e198: mov             x1, NULL
    // 0xb0e19c: r2 = 6
    //     0xb0e19c: movz            x2, #0x6
    // 0xb0e1a0: r0 = AllocateArray()
    //     0xb0e1a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0e1a4: mov             x2, x0
    // 0xb0e1a8: ldur            x0, [fp, #-0x20]
    // 0xb0e1ac: stur            x2, [fp, #-0x28]
    // 0xb0e1b0: StoreField: r2->field_f = r0
    //     0xb0e1b0: stur            w0, [x2, #0xf]
    // 0xb0e1b4: r16 = Instance_Spacer
    //     0xb0e1b4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb0e1b8: ldr             x16, [x16, #0xf0]
    // 0xb0e1bc: StoreField: r2->field_13 = r16
    //     0xb0e1bc: stur            w16, [x2, #0x13]
    // 0xb0e1c0: ldur            x0, [fp, #-0x10]
    // 0xb0e1c4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0e1c4: stur            w0, [x2, #0x17]
    // 0xb0e1c8: r1 = <Widget>
    //     0xb0e1c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0e1cc: r0 = AllocateGrowableArray()
    //     0xb0e1cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0e1d0: mov             x1, x0
    // 0xb0e1d4: ldur            x0, [fp, #-0x28]
    // 0xb0e1d8: stur            x1, [fp, #-0x10]
    // 0xb0e1dc: StoreField: r1->field_f = r0
    //     0xb0e1dc: stur            w0, [x1, #0xf]
    // 0xb0e1e0: r2 = 6
    //     0xb0e1e0: movz            x2, #0x6
    // 0xb0e1e4: StoreField: r1->field_b = r2
    //     0xb0e1e4: stur            w2, [x1, #0xb]
    // 0xb0e1e8: r0 = Row()
    //     0xb0e1e8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb0e1ec: mov             x1, x0
    // 0xb0e1f0: r0 = Instance_Axis
    //     0xb0e1f0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0e1f4: stur            x1, [fp, #-0x20]
    // 0xb0e1f8: StoreField: r1->field_f = r0
    //     0xb0e1f8: stur            w0, [x1, #0xf]
    // 0xb0e1fc: r0 = Instance_MainAxisAlignment
    //     0xb0e1fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0e200: ldr             x0, [x0, #0xa08]
    // 0xb0e204: StoreField: r1->field_13 = r0
    //     0xb0e204: stur            w0, [x1, #0x13]
    // 0xb0e208: r2 = Instance_MainAxisSize
    //     0xb0e208: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0e20c: ldr             x2, [x2, #0xa10]
    // 0xb0e210: ArrayStore: r1[0] = r2  ; List_4
    //     0xb0e210: stur            w2, [x1, #0x17]
    // 0xb0e214: r2 = Instance_CrossAxisAlignment
    //     0xb0e214: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0e218: ldr             x2, [x2, #0xa18]
    // 0xb0e21c: StoreField: r1->field_1b = r2
    //     0xb0e21c: stur            w2, [x1, #0x1b]
    // 0xb0e220: r2 = Instance_VerticalDirection
    //     0xb0e220: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0e224: ldr             x2, [x2, #0xa20]
    // 0xb0e228: StoreField: r1->field_23 = r2
    //     0xb0e228: stur            w2, [x1, #0x23]
    // 0xb0e22c: r3 = Instance_Clip
    //     0xb0e22c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0e230: ldr             x3, [x3, #0x38]
    // 0xb0e234: StoreField: r1->field_2b = r3
    //     0xb0e234: stur            w3, [x1, #0x2b]
    // 0xb0e238: StoreField: r1->field_2f = rZR
    //     0xb0e238: stur            xzr, [x1, #0x2f]
    // 0xb0e23c: ldur            x4, [fp, #-0x10]
    // 0xb0e240: StoreField: r1->field_b = r4
    //     0xb0e240: stur            w4, [x1, #0xb]
    // 0xb0e244: r0 = Padding()
    //     0xb0e244: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0e248: mov             x3, x0
    // 0xb0e24c: r0 = Instance_EdgeInsets
    //     0xb0e24c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb0e250: ldr             x0, [x0, #0xd0]
    // 0xb0e254: stur            x3, [fp, #-0x28]
    // 0xb0e258: StoreField: r3->field_f = r0
    //     0xb0e258: stur            w0, [x3, #0xf]
    // 0xb0e25c: ldur            x0, [fp, #-0x20]
    // 0xb0e260: StoreField: r3->field_b = r0
    //     0xb0e260: stur            w0, [x3, #0xb]
    // 0xb0e264: ldur            x0, [fp, #-8]
    // 0xb0e268: LoadField: r1 = r0->field_b
    //     0xb0e268: ldur            w1, [x0, #0xb]
    // 0xb0e26c: DecompressPointer r1
    //     0xb0e26c: add             x1, x1, HEAP, lsl #32
    // 0xb0e270: cmp             w1, NULL
    // 0xb0e274: b.eq            #0xb0ec34
    // 0xb0e278: LoadField: r2 = r1->field_b
    //     0xb0e278: ldur            w2, [x1, #0xb]
    // 0xb0e27c: DecompressPointer r2
    //     0xb0e27c: add             x2, x2, HEAP, lsl #32
    // 0xb0e280: LoadField: r1 = r2->field_db
    //     0xb0e280: ldur            w1, [x2, #0xdb]
    // 0xb0e284: DecompressPointer r1
    //     0xb0e284: add             x1, x1, HEAP, lsl #32
    // 0xb0e288: cmp             w1, NULL
    // 0xb0e28c: b.ne            #0xb0e298
    // 0xb0e290: r2 = Null
    //     0xb0e290: mov             x2, NULL
    // 0xb0e294: b               #0xb0e2b0
    // 0xb0e298: LoadField: r2 = r1->field_b
    //     0xb0e298: ldur            w2, [x1, #0xb]
    // 0xb0e29c: cbnz            w2, #0xb0e2a8
    // 0xb0e2a0: r4 = false
    //     0xb0e2a0: add             x4, NULL, #0x30  ; false
    // 0xb0e2a4: b               #0xb0e2ac
    // 0xb0e2a8: r4 = true
    //     0xb0e2a8: add             x4, NULL, #0x20  ; true
    // 0xb0e2ac: mov             x2, x4
    // 0xb0e2b0: cmp             w2, NULL
    // 0xb0e2b4: b.ne            #0xb0e2c0
    // 0xb0e2b8: r4 = false
    //     0xb0e2b8: add             x4, NULL, #0x30  ; false
    // 0xb0e2bc: b               #0xb0e2c4
    // 0xb0e2c0: mov             x4, x2
    // 0xb0e2c4: stur            x4, [fp, #-0x20]
    // 0xb0e2c8: cmp             w1, NULL
    // 0xb0e2cc: b.ne            #0xb0e2d8
    // 0xb0e2d0: r5 = Null
    //     0xb0e2d0: mov             x5, NULL
    // 0xb0e2d4: b               #0xb0e2e0
    // 0xb0e2d8: LoadField: r2 = r1->field_b
    //     0xb0e2d8: ldur            w2, [x1, #0xb]
    // 0xb0e2dc: mov             x5, x2
    // 0xb0e2e0: ldur            x2, [fp, #-0x18]
    // 0xb0e2e4: stur            x5, [fp, #-0x10]
    // 0xb0e2e8: r1 = Function '<anonymous closure>':.
    //     0xb0e2e8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57bb0] AnonymousClosure: (0xb0f5d4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0e2ec: ldr             x1, [x1, #0xbb0]
    // 0xb0e2f0: r0 = AllocateClosure()
    //     0xb0e2f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0e2f4: stur            x0, [fp, #-0x30]
    // 0xb0e2f8: r0 = ListView()
    //     0xb0e2f8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb0e2fc: stur            x0, [fp, #-0x38]
    // 0xb0e300: r16 = true
    //     0xb0e300: add             x16, NULL, #0x20  ; true
    // 0xb0e304: r30 = Instance_Axis
    //     0xb0e304: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0e308: stp             lr, x16, [SP]
    // 0xb0e30c: mov             x1, x0
    // 0xb0e310: ldur            x2, [fp, #-0x30]
    // 0xb0e314: ldur            x3, [fp, #-0x10]
    // 0xb0e318: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb0e318: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb0e31c: ldr             x4, [x4, #0x2d0]
    // 0xb0e320: r0 = ListView.builder()
    //     0xb0e320: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb0e324: r0 = SizedBox()
    //     0xb0e324: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0e328: mov             x1, x0
    // 0xb0e32c: r0 = 130.000000
    //     0xb0e32c: add             x0, PP, #0x42, lsl #12  ; [pp+0x427b0] 130
    //     0xb0e330: ldr             x0, [x0, #0x7b0]
    // 0xb0e334: stur            x1, [fp, #-0x10]
    // 0xb0e338: StoreField: r1->field_13 = r0
    //     0xb0e338: stur            w0, [x1, #0x13]
    // 0xb0e33c: ldur            x0, [fp, #-0x38]
    // 0xb0e340: StoreField: r1->field_b = r0
    //     0xb0e340: stur            w0, [x1, #0xb]
    // 0xb0e344: r0 = Visibility()
    //     0xb0e344: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb0e348: mov             x3, x0
    // 0xb0e34c: ldur            x0, [fp, #-0x10]
    // 0xb0e350: stur            x3, [fp, #-0x30]
    // 0xb0e354: StoreField: r3->field_b = r0
    //     0xb0e354: stur            w0, [x3, #0xb]
    // 0xb0e358: r0 = Instance_SizedBox
    //     0xb0e358: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0e35c: StoreField: r3->field_f = r0
    //     0xb0e35c: stur            w0, [x3, #0xf]
    // 0xb0e360: ldur            x0, [fp, #-0x20]
    // 0xb0e364: StoreField: r3->field_13 = r0
    //     0xb0e364: stur            w0, [x3, #0x13]
    // 0xb0e368: r0 = false
    //     0xb0e368: add             x0, NULL, #0x30  ; false
    // 0xb0e36c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0e36c: stur            w0, [x3, #0x17]
    // 0xb0e370: StoreField: r3->field_1b = r0
    //     0xb0e370: stur            w0, [x3, #0x1b]
    // 0xb0e374: StoreField: r3->field_1f = r0
    //     0xb0e374: stur            w0, [x3, #0x1f]
    // 0xb0e378: StoreField: r3->field_23 = r0
    //     0xb0e378: stur            w0, [x3, #0x23]
    // 0xb0e37c: StoreField: r3->field_27 = r0
    //     0xb0e37c: stur            w0, [x3, #0x27]
    // 0xb0e380: StoreField: r3->field_2b = r0
    //     0xb0e380: stur            w0, [x3, #0x2b]
    // 0xb0e384: r1 = Null
    //     0xb0e384: mov             x1, NULL
    // 0xb0e388: r2 = 6
    //     0xb0e388: movz            x2, #0x6
    // 0xb0e38c: r0 = AllocateArray()
    //     0xb0e38c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0e390: mov             x2, x0
    // 0xb0e394: ldur            x0, [fp, #-0x28]
    // 0xb0e398: stur            x2, [fp, #-0x10]
    // 0xb0e39c: StoreField: r2->field_f = r0
    //     0xb0e39c: stur            w0, [x2, #0xf]
    // 0xb0e3a0: r16 = Instance_Divider
    //     0xb0e3a0: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0xb0e3a4: ldr             x16, [x16, #0x2e0]
    // 0xb0e3a8: StoreField: r2->field_13 = r16
    //     0xb0e3a8: stur            w16, [x2, #0x13]
    // 0xb0e3ac: ldur            x0, [fp, #-0x30]
    // 0xb0e3b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0e3b0: stur            w0, [x2, #0x17]
    // 0xb0e3b4: r1 = <Widget>
    //     0xb0e3b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0e3b8: r0 = AllocateGrowableArray()
    //     0xb0e3b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0e3bc: mov             x1, x0
    // 0xb0e3c0: ldur            x0, [fp, #-0x10]
    // 0xb0e3c4: stur            x1, [fp, #-0x20]
    // 0xb0e3c8: StoreField: r1->field_f = r0
    //     0xb0e3c8: stur            w0, [x1, #0xf]
    // 0xb0e3cc: r0 = 6
    //     0xb0e3cc: movz            x0, #0x6
    // 0xb0e3d0: StoreField: r1->field_b = r0
    //     0xb0e3d0: stur            w0, [x1, #0xb]
    // 0xb0e3d4: ldur            x2, [fp, #-8]
    // 0xb0e3d8: LoadField: r0 = r2->field_b
    //     0xb0e3d8: ldur            w0, [x2, #0xb]
    // 0xb0e3dc: DecompressPointer r0
    //     0xb0e3dc: add             x0, x0, HEAP, lsl #32
    // 0xb0e3e0: cmp             w0, NULL
    // 0xb0e3e4: b.eq            #0xb0ec38
    // 0xb0e3e8: LoadField: r3 = r0->field_b
    //     0xb0e3e8: ldur            w3, [x0, #0xb]
    // 0xb0e3ec: DecompressPointer r3
    //     0xb0e3ec: add             x3, x3, HEAP, lsl #32
    // 0xb0e3f0: r17 = 303
    //     0xb0e3f0: movz            x17, #0x12f
    // 0xb0e3f4: ldr             w0, [x3, x17]
    // 0xb0e3f8: DecompressPointer r0
    //     0xb0e3f8: add             x0, x0, HEAP, lsl #32
    // 0xb0e3fc: r3 = LoadClassIdInstr(r0)
    //     0xb0e3fc: ldur            x3, [x0, #-1]
    //     0xb0e400: ubfx            x3, x3, #0xc, #0x14
    // 0xb0e404: r16 = "size"
    //     0xb0e404: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb0e408: ldr             x16, [x16, #0x9c0]
    // 0xb0e40c: stp             x16, x0, [SP]
    // 0xb0e410: mov             x0, x3
    // 0xb0e414: mov             lr, x0
    // 0xb0e418: ldr             lr, [x21, lr, lsl #3]
    // 0xb0e41c: blr             lr
    // 0xb0e420: tbnz            w0, #4, #0xb0e52c
    // 0xb0e424: ldur            x2, [fp, #-0x18]
    // 0xb0e428: ldur            x0, [fp, #-0x20]
    // 0xb0e42c: LoadField: r1 = r2->field_13
    //     0xb0e42c: ldur            w1, [x2, #0x13]
    // 0xb0e430: DecompressPointer r1
    //     0xb0e430: add             x1, x1, HEAP, lsl #32
    // 0xb0e434: r0 = of()
    //     0xb0e434: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0e438: LoadField: r1 = r0->field_87
    //     0xb0e438: ldur            w1, [x0, #0x87]
    // 0xb0e43c: DecompressPointer r1
    //     0xb0e43c: add             x1, x1, HEAP, lsl #32
    // 0xb0e440: LoadField: r0 = r1->field_7
    //     0xb0e440: ldur            w0, [x1, #7]
    // 0xb0e444: DecompressPointer r0
    //     0xb0e444: add             x0, x0, HEAP, lsl #32
    // 0xb0e448: r16 = 16.000000
    //     0xb0e448: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0e44c: ldr             x16, [x16, #0x188]
    // 0xb0e450: r30 = Instance_Color
    //     0xb0e450: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0e454: stp             lr, x16, [SP]
    // 0xb0e458: mov             x1, x0
    // 0xb0e45c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0e45c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0e460: ldr             x4, [x4, #0xaa0]
    // 0xb0e464: r0 = copyWith()
    //     0xb0e464: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0e468: stur            x0, [fp, #-0x10]
    // 0xb0e46c: r0 = Text()
    //     0xb0e46c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0e470: mov             x1, x0
    // 0xb0e474: r0 = "Size"
    //     0xb0e474: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xb0e478: ldr             x0, [x0, #0x730]
    // 0xb0e47c: stur            x1, [fp, #-0x28]
    // 0xb0e480: StoreField: r1->field_b = r0
    //     0xb0e480: stur            w0, [x1, #0xb]
    // 0xb0e484: ldur            x0, [fp, #-0x10]
    // 0xb0e488: StoreField: r1->field_13 = r0
    //     0xb0e488: stur            w0, [x1, #0x13]
    // 0xb0e48c: r0 = Padding()
    //     0xb0e48c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0e490: mov             x2, x0
    // 0xb0e494: r0 = Instance_EdgeInsets
    //     0xb0e494: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb0e498: ldr             x0, [x0, #0xa78]
    // 0xb0e49c: stur            x2, [fp, #-0x10]
    // 0xb0e4a0: StoreField: r2->field_f = r0
    //     0xb0e4a0: stur            w0, [x2, #0xf]
    // 0xb0e4a4: ldur            x0, [fp, #-0x28]
    // 0xb0e4a8: StoreField: r2->field_b = r0
    //     0xb0e4a8: stur            w0, [x2, #0xb]
    // 0xb0e4ac: ldur            x0, [fp, #-0x20]
    // 0xb0e4b0: LoadField: r1 = r0->field_b
    //     0xb0e4b0: ldur            w1, [x0, #0xb]
    // 0xb0e4b4: LoadField: r3 = r0->field_f
    //     0xb0e4b4: ldur            w3, [x0, #0xf]
    // 0xb0e4b8: DecompressPointer r3
    //     0xb0e4b8: add             x3, x3, HEAP, lsl #32
    // 0xb0e4bc: LoadField: r4 = r3->field_b
    //     0xb0e4bc: ldur            w4, [x3, #0xb]
    // 0xb0e4c0: r3 = LoadInt32Instr(r1)
    //     0xb0e4c0: sbfx            x3, x1, #1, #0x1f
    // 0xb0e4c4: stur            x3, [fp, #-0x40]
    // 0xb0e4c8: r1 = LoadInt32Instr(r4)
    //     0xb0e4c8: sbfx            x1, x4, #1, #0x1f
    // 0xb0e4cc: cmp             x3, x1
    // 0xb0e4d0: b.ne            #0xb0e4dc
    // 0xb0e4d4: mov             x1, x0
    // 0xb0e4d8: r0 = _growToNextCapacity()
    //     0xb0e4d8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e4dc: ldur            x2, [fp, #-0x20]
    // 0xb0e4e0: ldur            x3, [fp, #-0x40]
    // 0xb0e4e4: add             x0, x3, #1
    // 0xb0e4e8: lsl             x1, x0, #1
    // 0xb0e4ec: StoreField: r2->field_b = r1
    //     0xb0e4ec: stur            w1, [x2, #0xb]
    // 0xb0e4f0: LoadField: r1 = r2->field_f
    //     0xb0e4f0: ldur            w1, [x2, #0xf]
    // 0xb0e4f4: DecompressPointer r1
    //     0xb0e4f4: add             x1, x1, HEAP, lsl #32
    // 0xb0e4f8: ldur            x0, [fp, #-0x10]
    // 0xb0e4fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0e4fc: add             x25, x1, x3, lsl #2
    //     0xb0e500: add             x25, x25, #0xf
    //     0xb0e504: str             w0, [x25]
    //     0xb0e508: tbz             w0, #0, #0xb0e524
    //     0xb0e50c: ldurb           w16, [x1, #-1]
    //     0xb0e510: ldurb           w17, [x0, #-1]
    //     0xb0e514: and             x16, x17, x16, lsr #2
    //     0xb0e518: tst             x16, HEAP, lsr #32
    //     0xb0e51c: b.eq            #0xb0e524
    //     0xb0e520: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0e524: mov             x3, x2
    // 0xb0e528: b               #0xb0e634
    // 0xb0e52c: ldur            x3, [fp, #-0x18]
    // 0xb0e530: ldur            x2, [fp, #-0x20]
    // 0xb0e534: r0 = Instance_EdgeInsets
    //     0xb0e534: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb0e538: ldr             x0, [x0, #0xa78]
    // 0xb0e53c: LoadField: r1 = r3->field_13
    //     0xb0e53c: ldur            w1, [x3, #0x13]
    // 0xb0e540: DecompressPointer r1
    //     0xb0e540: add             x1, x1, HEAP, lsl #32
    // 0xb0e544: r0 = of()
    //     0xb0e544: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0e548: LoadField: r1 = r0->field_87
    //     0xb0e548: ldur            w1, [x0, #0x87]
    // 0xb0e54c: DecompressPointer r1
    //     0xb0e54c: add             x1, x1, HEAP, lsl #32
    // 0xb0e550: LoadField: r0 = r1->field_7
    //     0xb0e550: ldur            w0, [x1, #7]
    // 0xb0e554: DecompressPointer r0
    //     0xb0e554: add             x0, x0, HEAP, lsl #32
    // 0xb0e558: r16 = 16.000000
    //     0xb0e558: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0e55c: ldr             x16, [x16, #0x188]
    // 0xb0e560: r30 = Instance_Color
    //     0xb0e560: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0e564: stp             lr, x16, [SP]
    // 0xb0e568: mov             x1, x0
    // 0xb0e56c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0e56c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0e570: ldr             x4, [x4, #0xaa0]
    // 0xb0e574: r0 = copyWith()
    //     0xb0e574: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0e578: stur            x0, [fp, #-0x10]
    // 0xb0e57c: r0 = Text()
    //     0xb0e57c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0e580: mov             x1, x0
    // 0xb0e584: r0 = "Variant"
    //     0xb0e584: add             x0, PP, #0x52, lsl #12  ; [pp+0x52738] "Variant"
    //     0xb0e588: ldr             x0, [x0, #0x738]
    // 0xb0e58c: stur            x1, [fp, #-0x28]
    // 0xb0e590: StoreField: r1->field_b = r0
    //     0xb0e590: stur            w0, [x1, #0xb]
    // 0xb0e594: ldur            x0, [fp, #-0x10]
    // 0xb0e598: StoreField: r1->field_13 = r0
    //     0xb0e598: stur            w0, [x1, #0x13]
    // 0xb0e59c: r0 = Padding()
    //     0xb0e59c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0e5a0: mov             x2, x0
    // 0xb0e5a4: r0 = Instance_EdgeInsets
    //     0xb0e5a4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb0e5a8: ldr             x0, [x0, #0xa78]
    // 0xb0e5ac: stur            x2, [fp, #-0x10]
    // 0xb0e5b0: StoreField: r2->field_f = r0
    //     0xb0e5b0: stur            w0, [x2, #0xf]
    // 0xb0e5b4: ldur            x0, [fp, #-0x28]
    // 0xb0e5b8: StoreField: r2->field_b = r0
    //     0xb0e5b8: stur            w0, [x2, #0xb]
    // 0xb0e5bc: ldur            x0, [fp, #-0x20]
    // 0xb0e5c0: LoadField: r1 = r0->field_b
    //     0xb0e5c0: ldur            w1, [x0, #0xb]
    // 0xb0e5c4: LoadField: r3 = r0->field_f
    //     0xb0e5c4: ldur            w3, [x0, #0xf]
    // 0xb0e5c8: DecompressPointer r3
    //     0xb0e5c8: add             x3, x3, HEAP, lsl #32
    // 0xb0e5cc: LoadField: r4 = r3->field_b
    //     0xb0e5cc: ldur            w4, [x3, #0xb]
    // 0xb0e5d0: r3 = LoadInt32Instr(r1)
    //     0xb0e5d0: sbfx            x3, x1, #1, #0x1f
    // 0xb0e5d4: stur            x3, [fp, #-0x40]
    // 0xb0e5d8: r1 = LoadInt32Instr(r4)
    //     0xb0e5d8: sbfx            x1, x4, #1, #0x1f
    // 0xb0e5dc: cmp             x3, x1
    // 0xb0e5e0: b.ne            #0xb0e5ec
    // 0xb0e5e4: mov             x1, x0
    // 0xb0e5e8: r0 = _growToNextCapacity()
    //     0xb0e5e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e5ec: ldur            x3, [fp, #-0x20]
    // 0xb0e5f0: ldur            x2, [fp, #-0x40]
    // 0xb0e5f4: add             x0, x2, #1
    // 0xb0e5f8: lsl             x1, x0, #1
    // 0xb0e5fc: StoreField: r3->field_b = r1
    //     0xb0e5fc: stur            w1, [x3, #0xb]
    // 0xb0e600: LoadField: r1 = r3->field_f
    //     0xb0e600: ldur            w1, [x3, #0xf]
    // 0xb0e604: DecompressPointer r1
    //     0xb0e604: add             x1, x1, HEAP, lsl #32
    // 0xb0e608: ldur            x0, [fp, #-0x10]
    // 0xb0e60c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0e60c: add             x25, x1, x2, lsl #2
    //     0xb0e610: add             x25, x25, #0xf
    //     0xb0e614: str             w0, [x25]
    //     0xb0e618: tbz             w0, #0, #0xb0e634
    //     0xb0e61c: ldurb           w16, [x1, #-1]
    //     0xb0e620: ldurb           w17, [x0, #-1]
    //     0xb0e624: and             x16, x17, x16, lsr #2
    //     0xb0e628: tst             x16, HEAP, lsr #32
    //     0xb0e62c: b.eq            #0xb0e634
    //     0xb0e630: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0e634: ldur            x0, [fp, #-8]
    // 0xb0e638: LoadField: r1 = r0->field_b
    //     0xb0e638: ldur            w1, [x0, #0xb]
    // 0xb0e63c: DecompressPointer r1
    //     0xb0e63c: add             x1, x1, HEAP, lsl #32
    // 0xb0e640: cmp             w1, NULL
    // 0xb0e644: b.eq            #0xb0ec3c
    // 0xb0e648: LoadField: r2 = r1->field_b
    //     0xb0e648: ldur            w2, [x1, #0xb]
    // 0xb0e64c: DecompressPointer r2
    //     0xb0e64c: add             x2, x2, HEAP, lsl #32
    // 0xb0e650: LoadField: r4 = r2->field_db
    //     0xb0e650: ldur            w4, [x2, #0xdb]
    // 0xb0e654: DecompressPointer r4
    //     0xb0e654: add             x4, x4, HEAP, lsl #32
    // 0xb0e658: cmp             w4, NULL
    // 0xb0e65c: b.ne            #0xb0e668
    // 0xb0e660: r1 = Null
    //     0xb0e660: mov             x1, NULL
    // 0xb0e664: b               #0xb0e680
    // 0xb0e668: LoadField: r1 = r4->field_b
    //     0xb0e668: ldur            w1, [x4, #0xb]
    // 0xb0e66c: cbz             w1, #0xb0e678
    // 0xb0e670: r5 = false
    //     0xb0e670: add             x5, NULL, #0x30  ; false
    // 0xb0e674: b               #0xb0e67c
    // 0xb0e678: r5 = true
    //     0xb0e678: add             x5, NULL, #0x20  ; true
    // 0xb0e67c: mov             x1, x5
    // 0xb0e680: cmp             w1, NULL
    // 0xb0e684: b.eq            #0xb0e68c
    // 0xb0e688: tbnz            w1, #4, #0xb0e7b4
    // 0xb0e68c: LoadField: r1 = r2->field_d7
    //     0xb0e68c: ldur            w1, [x2, #0xd7]
    // 0xb0e690: DecompressPointer r1
    //     0xb0e690: add             x1, x1, HEAP, lsl #32
    // 0xb0e694: cmp             w1, NULL
    // 0xb0e698: b.ne            #0xb0e6a4
    // 0xb0e69c: r1 = Null
    //     0xb0e69c: mov             x1, NULL
    // 0xb0e6a0: b               #0xb0e6ac
    // 0xb0e6a4: LoadField: r2 = r1->field_b
    //     0xb0e6a4: ldur            w2, [x1, #0xb]
    // 0xb0e6a8: mov             x1, x2
    // 0xb0e6ac: cmp             w1, NULL
    // 0xb0e6b0: b.ne            #0xb0e6bc
    // 0xb0e6b4: r1 = 0
    //     0xb0e6b4: movz            x1, #0
    // 0xb0e6b8: b               #0xb0e6c4
    // 0xb0e6bc: r2 = LoadInt32Instr(r1)
    //     0xb0e6bc: sbfx            x2, x1, #1, #0x1f
    // 0xb0e6c0: mov             x1, x2
    // 0xb0e6c4: lsl             x4, x1, #1
    // 0xb0e6c8: ldur            x2, [fp, #-0x18]
    // 0xb0e6cc: stur            x4, [fp, #-0x10]
    // 0xb0e6d0: r1 = Function '<anonymous closure>':.
    //     0xb0e6d0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57bb8] AnonymousClosure: (0xb0f2e8), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0e6d4: ldr             x1, [x1, #0xbb8]
    // 0xb0e6d8: r0 = AllocateClosure()
    //     0xb0e6d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0e6dc: stur            x0, [fp, #-0x28]
    // 0xb0e6e0: r0 = ListView()
    //     0xb0e6e0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb0e6e4: stur            x0, [fp, #-0x30]
    // 0xb0e6e8: r16 = Instance_Axis
    //     0xb0e6e8: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0e6ec: str             x16, [SP]
    // 0xb0e6f0: mov             x1, x0
    // 0xb0e6f4: ldur            x2, [fp, #-0x28]
    // 0xb0e6f8: ldur            x3, [fp, #-0x10]
    // 0xb0e6fc: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb0e6fc: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb0e700: ldr             x4, [x4, #0x4a0]
    // 0xb0e704: r0 = ListView.builder()
    //     0xb0e704: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb0e708: r0 = SizedBox()
    //     0xb0e708: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0e70c: mov             x2, x0
    // 0xb0e710: r0 = inf
    //     0xb0e710: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb0e714: ldr             x0, [x0, #0x9f8]
    // 0xb0e718: stur            x2, [fp, #-0x10]
    // 0xb0e71c: StoreField: r2->field_f = r0
    //     0xb0e71c: stur            w0, [x2, #0xf]
    // 0xb0e720: r3 = 55.000000
    //     0xb0e720: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb0e724: ldr             x3, [x3, #0x9b8]
    // 0xb0e728: StoreField: r2->field_13 = r3
    //     0xb0e728: stur            w3, [x2, #0x13]
    // 0xb0e72c: ldur            x1, [fp, #-0x30]
    // 0xb0e730: StoreField: r2->field_b = r1
    //     0xb0e730: stur            w1, [x2, #0xb]
    // 0xb0e734: ldur            x3, [fp, #-0x20]
    // 0xb0e738: LoadField: r1 = r3->field_b
    //     0xb0e738: ldur            w1, [x3, #0xb]
    // 0xb0e73c: LoadField: r4 = r3->field_f
    //     0xb0e73c: ldur            w4, [x3, #0xf]
    // 0xb0e740: DecompressPointer r4
    //     0xb0e740: add             x4, x4, HEAP, lsl #32
    // 0xb0e744: LoadField: r5 = r4->field_b
    //     0xb0e744: ldur            w5, [x4, #0xb]
    // 0xb0e748: r4 = LoadInt32Instr(r1)
    //     0xb0e748: sbfx            x4, x1, #1, #0x1f
    // 0xb0e74c: stur            x4, [fp, #-0x40]
    // 0xb0e750: r1 = LoadInt32Instr(r5)
    //     0xb0e750: sbfx            x1, x5, #1, #0x1f
    // 0xb0e754: cmp             x4, x1
    // 0xb0e758: b.ne            #0xb0e764
    // 0xb0e75c: mov             x1, x3
    // 0xb0e760: r0 = _growToNextCapacity()
    //     0xb0e760: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e764: ldur            x5, [fp, #-0x20]
    // 0xb0e768: ldur            x2, [fp, #-0x40]
    // 0xb0e76c: add             x0, x2, #1
    // 0xb0e770: lsl             x1, x0, #1
    // 0xb0e774: StoreField: r5->field_b = r1
    //     0xb0e774: stur            w1, [x5, #0xb]
    // 0xb0e778: LoadField: r1 = r5->field_f
    //     0xb0e778: ldur            w1, [x5, #0xf]
    // 0xb0e77c: DecompressPointer r1
    //     0xb0e77c: add             x1, x1, HEAP, lsl #32
    // 0xb0e780: ldur            x0, [fp, #-0x10]
    // 0xb0e784: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0e784: add             x25, x1, x2, lsl #2
    //     0xb0e788: add             x25, x25, #0xf
    //     0xb0e78c: str             w0, [x25]
    //     0xb0e790: tbz             w0, #0, #0xb0e7ac
    //     0xb0e794: ldurb           w16, [x1, #-1]
    //     0xb0e798: ldurb           w17, [x0, #-1]
    //     0xb0e79c: and             x16, x17, x16, lsr #2
    //     0xb0e7a0: tst             x16, HEAP, lsr #32
    //     0xb0e7a4: b.eq            #0xb0e7ac
    //     0xb0e7a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0e7ac: mov             x2, x5
    // 0xb0e7b0: b               #0xb0e928
    // 0xb0e7b4: mov             x5, x3
    // 0xb0e7b8: r3 = 55.000000
    //     0xb0e7b8: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb0e7bc: ldr             x3, [x3, #0x9b8]
    // 0xb0e7c0: cmp             w4, NULL
    // 0xb0e7c4: b.ne            #0xb0e7d4
    // 0xb0e7c8: ldur            x6, [fp, #-8]
    // 0xb0e7cc: r0 = Null
    //     0xb0e7cc: mov             x0, NULL
    // 0xb0e7d0: b               #0xb0e828
    // 0xb0e7d4: ldur            x6, [fp, #-8]
    // 0xb0e7d8: LoadField: r2 = r6->field_1f
    //     0xb0e7d8: ldur            x2, [x6, #0x1f]
    // 0xb0e7dc: LoadField: r0 = r4->field_b
    //     0xb0e7dc: ldur            w0, [x4, #0xb]
    // 0xb0e7e0: r1 = LoadInt32Instr(r0)
    //     0xb0e7e0: sbfx            x1, x0, #1, #0x1f
    // 0xb0e7e4: mov             x0, x1
    // 0xb0e7e8: mov             x1, x2
    // 0xb0e7ec: cmp             x1, x0
    // 0xb0e7f0: b.hs            #0xb0ec40
    // 0xb0e7f4: LoadField: r0 = r4->field_f
    //     0xb0e7f4: ldur            w0, [x4, #0xf]
    // 0xb0e7f8: DecompressPointer r0
    //     0xb0e7f8: add             x0, x0, HEAP, lsl #32
    // 0xb0e7fc: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb0e7fc: add             x16, x0, x2, lsl #2
    //     0xb0e800: ldur            w1, [x16, #0xf]
    // 0xb0e804: DecompressPointer r1
    //     0xb0e804: add             x1, x1, HEAP, lsl #32
    // 0xb0e808: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb0e808: ldur            w0, [x1, #0x17]
    // 0xb0e80c: DecompressPointer r0
    //     0xb0e80c: add             x0, x0, HEAP, lsl #32
    // 0xb0e810: cmp             w0, NULL
    // 0xb0e814: b.ne            #0xb0e820
    // 0xb0e818: r0 = Null
    //     0xb0e818: mov             x0, NULL
    // 0xb0e81c: b               #0xb0e828
    // 0xb0e820: LoadField: r1 = r0->field_b
    //     0xb0e820: ldur            w1, [x0, #0xb]
    // 0xb0e824: mov             x0, x1
    // 0xb0e828: cmp             w0, NULL
    // 0xb0e82c: b.ne            #0xb0e838
    // 0xb0e830: r0 = 0
    //     0xb0e830: movz            x0, #0
    // 0xb0e834: b               #0xb0e840
    // 0xb0e838: r1 = LoadInt32Instr(r0)
    //     0xb0e838: sbfx            x1, x0, #1, #0x1f
    // 0xb0e83c: mov             x0, x1
    // 0xb0e840: lsl             x4, x0, #1
    // 0xb0e844: ldur            x2, [fp, #-0x18]
    // 0xb0e848: stur            x4, [fp, #-0x10]
    // 0xb0e84c: r1 = Function '<anonymous closure>':.
    //     0xb0e84c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57bc0] AnonymousClosure: (0xb0efac), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0e850: ldr             x1, [x1, #0xbc0]
    // 0xb0e854: r0 = AllocateClosure()
    //     0xb0e854: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0e858: stur            x0, [fp, #-0x28]
    // 0xb0e85c: r0 = ListView()
    //     0xb0e85c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb0e860: stur            x0, [fp, #-0x30]
    // 0xb0e864: r16 = Instance_Axis
    //     0xb0e864: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb0e868: str             x16, [SP]
    // 0xb0e86c: mov             x1, x0
    // 0xb0e870: ldur            x2, [fp, #-0x28]
    // 0xb0e874: ldur            x3, [fp, #-0x10]
    // 0xb0e878: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb0e878: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb0e87c: ldr             x4, [x4, #0x4a0]
    // 0xb0e880: r0 = ListView.builder()
    //     0xb0e880: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb0e884: r0 = SizedBox()
    //     0xb0e884: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0e888: mov             x2, x0
    // 0xb0e88c: r0 = inf
    //     0xb0e88c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb0e890: ldr             x0, [x0, #0x9f8]
    // 0xb0e894: stur            x2, [fp, #-0x10]
    // 0xb0e898: StoreField: r2->field_f = r0
    //     0xb0e898: stur            w0, [x2, #0xf]
    // 0xb0e89c: r1 = 55.000000
    //     0xb0e89c: add             x1, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb0e8a0: ldr             x1, [x1, #0x9b8]
    // 0xb0e8a4: StoreField: r2->field_13 = r1
    //     0xb0e8a4: stur            w1, [x2, #0x13]
    // 0xb0e8a8: ldur            x1, [fp, #-0x30]
    // 0xb0e8ac: StoreField: r2->field_b = r1
    //     0xb0e8ac: stur            w1, [x2, #0xb]
    // 0xb0e8b0: ldur            x3, [fp, #-0x20]
    // 0xb0e8b4: LoadField: r1 = r3->field_b
    //     0xb0e8b4: ldur            w1, [x3, #0xb]
    // 0xb0e8b8: LoadField: r4 = r3->field_f
    //     0xb0e8b8: ldur            w4, [x3, #0xf]
    // 0xb0e8bc: DecompressPointer r4
    //     0xb0e8bc: add             x4, x4, HEAP, lsl #32
    // 0xb0e8c0: LoadField: r5 = r4->field_b
    //     0xb0e8c0: ldur            w5, [x4, #0xb]
    // 0xb0e8c4: r4 = LoadInt32Instr(r1)
    //     0xb0e8c4: sbfx            x4, x1, #1, #0x1f
    // 0xb0e8c8: stur            x4, [fp, #-0x40]
    // 0xb0e8cc: r1 = LoadInt32Instr(r5)
    //     0xb0e8cc: sbfx            x1, x5, #1, #0x1f
    // 0xb0e8d0: cmp             x4, x1
    // 0xb0e8d4: b.ne            #0xb0e8e0
    // 0xb0e8d8: mov             x1, x3
    // 0xb0e8dc: r0 = _growToNextCapacity()
    //     0xb0e8dc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e8e0: ldur            x2, [fp, #-0x20]
    // 0xb0e8e4: ldur            x3, [fp, #-0x40]
    // 0xb0e8e8: add             x0, x3, #1
    // 0xb0e8ec: lsl             x1, x0, #1
    // 0xb0e8f0: StoreField: r2->field_b = r1
    //     0xb0e8f0: stur            w1, [x2, #0xb]
    // 0xb0e8f4: LoadField: r1 = r2->field_f
    //     0xb0e8f4: ldur            w1, [x2, #0xf]
    // 0xb0e8f8: DecompressPointer r1
    //     0xb0e8f8: add             x1, x1, HEAP, lsl #32
    // 0xb0e8fc: ldur            x0, [fp, #-0x10]
    // 0xb0e900: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0e900: add             x25, x1, x3, lsl #2
    //     0xb0e904: add             x25, x25, #0xf
    //     0xb0e908: str             w0, [x25]
    //     0xb0e90c: tbz             w0, #0, #0xb0e928
    //     0xb0e910: ldurb           w16, [x1, #-1]
    //     0xb0e914: ldurb           w17, [x0, #-1]
    //     0xb0e918: and             x16, x17, x16, lsr #2
    //     0xb0e91c: tst             x16, HEAP, lsr #32
    //     0xb0e920: b.eq            #0xb0e928
    //     0xb0e924: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0e928: ldur            x0, [fp, #-8]
    // 0xb0e92c: ldur            x1, [fp, #-0x18]
    // 0xb0e930: r16 = <EdgeInsets>
    //     0xb0e930: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb0e934: ldr             x16, [x16, #0xda0]
    // 0xb0e938: r30 = Instance_EdgeInsets
    //     0xb0e938: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb0e93c: ldr             lr, [lr, #0x1f0]
    // 0xb0e940: stp             lr, x16, [SP]
    // 0xb0e944: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0e944: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0e948: r0 = all()
    //     0xb0e948: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0e94c: ldur            x2, [fp, #-0x18]
    // 0xb0e950: stur            x0, [fp, #-0x10]
    // 0xb0e954: LoadField: r1 = r2->field_13
    //     0xb0e954: ldur            w1, [x2, #0x13]
    // 0xb0e958: DecompressPointer r1
    //     0xb0e958: add             x1, x1, HEAP, lsl #32
    // 0xb0e95c: r0 = of()
    //     0xb0e95c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0e960: LoadField: r1 = r0->field_5b
    //     0xb0e960: ldur            w1, [x0, #0x5b]
    // 0xb0e964: DecompressPointer r1
    //     0xb0e964: add             x1, x1, HEAP, lsl #32
    // 0xb0e968: r16 = <Color>
    //     0xb0e968: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb0e96c: ldr             x16, [x16, #0xf80]
    // 0xb0e970: stp             x1, x16, [SP]
    // 0xb0e974: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0e974: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0e978: r0 = all()
    //     0xb0e978: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0e97c: stur            x0, [fp, #-0x28]
    // 0xb0e980: r16 = <RoundedRectangleBorder>
    //     0xb0e980: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb0e984: ldr             x16, [x16, #0xf78]
    // 0xb0e988: r30 = Instance_RoundedRectangleBorder
    //     0xb0e988: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb0e98c: ldr             lr, [lr, #0x888]
    // 0xb0e990: stp             lr, x16, [SP]
    // 0xb0e994: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0e994: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0e998: r0 = all()
    //     0xb0e998: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0e99c: stur            x0, [fp, #-0x30]
    // 0xb0e9a0: r0 = ButtonStyle()
    //     0xb0e9a0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb0e9a4: mov             x1, x0
    // 0xb0e9a8: ldur            x0, [fp, #-0x28]
    // 0xb0e9ac: stur            x1, [fp, #-0x38]
    // 0xb0e9b0: StoreField: r1->field_b = r0
    //     0xb0e9b0: stur            w0, [x1, #0xb]
    // 0xb0e9b4: ldur            x0, [fp, #-0x10]
    // 0xb0e9b8: StoreField: r1->field_23 = r0
    //     0xb0e9b8: stur            w0, [x1, #0x23]
    // 0xb0e9bc: ldur            x0, [fp, #-0x30]
    // 0xb0e9c0: StoreField: r1->field_43 = r0
    //     0xb0e9c0: stur            w0, [x1, #0x43]
    // 0xb0e9c4: r0 = TextButtonThemeData()
    //     0xb0e9c4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb0e9c8: mov             x2, x0
    // 0xb0e9cc: ldur            x0, [fp, #-0x38]
    // 0xb0e9d0: stur            x2, [fp, #-0x10]
    // 0xb0e9d4: StoreField: r2->field_7 = r0
    //     0xb0e9d4: stur            w0, [x2, #7]
    // 0xb0e9d8: ldur            x0, [fp, #-8]
    // 0xb0e9dc: LoadField: r1 = r0->field_b
    //     0xb0e9dc: ldur            w1, [x0, #0xb]
    // 0xb0e9e0: DecompressPointer r1
    //     0xb0e9e0: add             x1, x1, HEAP, lsl #32
    // 0xb0e9e4: cmp             w1, NULL
    // 0xb0e9e8: b.eq            #0xb0ec44
    // 0xb0e9ec: LoadField: r0 = r1->field_b
    //     0xb0e9ec: ldur            w0, [x1, #0xb]
    // 0xb0e9f0: DecompressPointer r0
    //     0xb0e9f0: add             x0, x0, HEAP, lsl #32
    // 0xb0e9f4: LoadField: r1 = r0->field_f
    //     0xb0e9f4: ldur            w1, [x0, #0xf]
    // 0xb0e9f8: DecompressPointer r1
    //     0xb0e9f8: add             x1, x1, HEAP, lsl #32
    // 0xb0e9fc: cmp             w1, NULL
    // 0xb0ea00: b.ne            #0xb0ea08
    // 0xb0ea04: r1 = ""
    //     0xb0ea04: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ea08: ldur            x0, [fp, #-0x18]
    // 0xb0ea0c: ldur            x3, [fp, #-0x20]
    // 0xb0ea10: r0 = capitalizeFirstWord()
    //     0xb0ea10: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb0ea14: ldur            x2, [fp, #-0x18]
    // 0xb0ea18: stur            x0, [fp, #-8]
    // 0xb0ea1c: LoadField: r1 = r2->field_13
    //     0xb0ea1c: ldur            w1, [x2, #0x13]
    // 0xb0ea20: DecompressPointer r1
    //     0xb0ea20: add             x1, x1, HEAP, lsl #32
    // 0xb0ea24: r0 = of()
    //     0xb0ea24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0ea28: LoadField: r1 = r0->field_87
    //     0xb0ea28: ldur            w1, [x0, #0x87]
    // 0xb0ea2c: DecompressPointer r1
    //     0xb0ea2c: add             x1, x1, HEAP, lsl #32
    // 0xb0ea30: LoadField: r0 = r1->field_7
    //     0xb0ea30: ldur            w0, [x1, #7]
    // 0xb0ea34: DecompressPointer r0
    //     0xb0ea34: add             x0, x0, HEAP, lsl #32
    // 0xb0ea38: r16 = 16.000000
    //     0xb0ea38: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0ea3c: ldr             x16, [x16, #0x188]
    // 0xb0ea40: r30 = Instance_Color
    //     0xb0ea40: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0ea44: stp             lr, x16, [SP]
    // 0xb0ea48: mov             x1, x0
    // 0xb0ea4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0ea4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0ea50: ldr             x4, [x4, #0xaa0]
    // 0xb0ea54: r0 = copyWith()
    //     0xb0ea54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0ea58: stur            x0, [fp, #-0x28]
    // 0xb0ea5c: r0 = Text()
    //     0xb0ea5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0ea60: mov             x3, x0
    // 0xb0ea64: ldur            x0, [fp, #-8]
    // 0xb0ea68: stur            x3, [fp, #-0x30]
    // 0xb0ea6c: StoreField: r3->field_b = r0
    //     0xb0ea6c: stur            w0, [x3, #0xb]
    // 0xb0ea70: ldur            x0, [fp, #-0x28]
    // 0xb0ea74: StoreField: r3->field_13 = r0
    //     0xb0ea74: stur            w0, [x3, #0x13]
    // 0xb0ea78: ldur            x2, [fp, #-0x18]
    // 0xb0ea7c: r1 = Function '<anonymous closure>':.
    //     0xb0ea7c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57bc8] AnonymousClosure: (0xb0ec48), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0ea80: ldr             x1, [x1, #0xbc8]
    // 0xb0ea84: r0 = AllocateClosure()
    //     0xb0ea84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0ea88: stur            x0, [fp, #-8]
    // 0xb0ea8c: r0 = TextButton()
    //     0xb0ea8c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb0ea90: mov             x1, x0
    // 0xb0ea94: ldur            x0, [fp, #-8]
    // 0xb0ea98: stur            x1, [fp, #-0x18]
    // 0xb0ea9c: StoreField: r1->field_b = r0
    //     0xb0ea9c: stur            w0, [x1, #0xb]
    // 0xb0eaa0: r0 = false
    //     0xb0eaa0: add             x0, NULL, #0x30  ; false
    // 0xb0eaa4: StoreField: r1->field_27 = r0
    //     0xb0eaa4: stur            w0, [x1, #0x27]
    // 0xb0eaa8: r0 = true
    //     0xb0eaa8: add             x0, NULL, #0x20  ; true
    // 0xb0eaac: StoreField: r1->field_2f = r0
    //     0xb0eaac: stur            w0, [x1, #0x2f]
    // 0xb0eab0: ldur            x0, [fp, #-0x30]
    // 0xb0eab4: StoreField: r1->field_37 = r0
    //     0xb0eab4: stur            w0, [x1, #0x37]
    // 0xb0eab8: r0 = Instance_ValueKey
    //     0xb0eab8: add             x0, PP, #0x57, lsl #12  ; [pp+0x57bd0] Obj!ValueKey<String>@d5b311
    //     0xb0eabc: ldr             x0, [x0, #0xbd0]
    // 0xb0eac0: StoreField: r1->field_7 = r0
    //     0xb0eac0: stur            w0, [x1, #7]
    // 0xb0eac4: r0 = TextButtonTheme()
    //     0xb0eac4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb0eac8: mov             x1, x0
    // 0xb0eacc: ldur            x0, [fp, #-0x10]
    // 0xb0ead0: stur            x1, [fp, #-8]
    // 0xb0ead4: StoreField: r1->field_f = r0
    //     0xb0ead4: stur            w0, [x1, #0xf]
    // 0xb0ead8: ldur            x0, [fp, #-0x18]
    // 0xb0eadc: StoreField: r1->field_b = r0
    //     0xb0eadc: stur            w0, [x1, #0xb]
    // 0xb0eae0: r0 = SizedBox()
    //     0xb0eae0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb0eae4: mov             x1, x0
    // 0xb0eae8: r0 = inf
    //     0xb0eae8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb0eaec: ldr             x0, [x0, #0x9f8]
    // 0xb0eaf0: stur            x1, [fp, #-0x10]
    // 0xb0eaf4: StoreField: r1->field_f = r0
    //     0xb0eaf4: stur            w0, [x1, #0xf]
    // 0xb0eaf8: ldur            x0, [fp, #-8]
    // 0xb0eafc: StoreField: r1->field_b = r0
    //     0xb0eafc: stur            w0, [x1, #0xb]
    // 0xb0eb00: r0 = Padding()
    //     0xb0eb00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0eb04: mov             x2, x0
    // 0xb0eb08: r0 = Instance_EdgeInsets
    //     0xb0eb08: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xb0eb0c: ldr             x0, [x0, #0xf48]
    // 0xb0eb10: stur            x2, [fp, #-8]
    // 0xb0eb14: StoreField: r2->field_f = r0
    //     0xb0eb14: stur            w0, [x2, #0xf]
    // 0xb0eb18: ldur            x0, [fp, #-0x10]
    // 0xb0eb1c: StoreField: r2->field_b = r0
    //     0xb0eb1c: stur            w0, [x2, #0xb]
    // 0xb0eb20: ldur            x0, [fp, #-0x20]
    // 0xb0eb24: LoadField: r1 = r0->field_b
    //     0xb0eb24: ldur            w1, [x0, #0xb]
    // 0xb0eb28: LoadField: r3 = r0->field_f
    //     0xb0eb28: ldur            w3, [x0, #0xf]
    // 0xb0eb2c: DecompressPointer r3
    //     0xb0eb2c: add             x3, x3, HEAP, lsl #32
    // 0xb0eb30: LoadField: r4 = r3->field_b
    //     0xb0eb30: ldur            w4, [x3, #0xb]
    // 0xb0eb34: r3 = LoadInt32Instr(r1)
    //     0xb0eb34: sbfx            x3, x1, #1, #0x1f
    // 0xb0eb38: stur            x3, [fp, #-0x40]
    // 0xb0eb3c: r1 = LoadInt32Instr(r4)
    //     0xb0eb3c: sbfx            x1, x4, #1, #0x1f
    // 0xb0eb40: cmp             x3, x1
    // 0xb0eb44: b.ne            #0xb0eb50
    // 0xb0eb48: mov             x1, x0
    // 0xb0eb4c: r0 = _growToNextCapacity()
    //     0xb0eb4c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0eb50: ldur            x2, [fp, #-0x20]
    // 0xb0eb54: ldur            x3, [fp, #-0x40]
    // 0xb0eb58: add             x0, x3, #1
    // 0xb0eb5c: lsl             x1, x0, #1
    // 0xb0eb60: StoreField: r2->field_b = r1
    //     0xb0eb60: stur            w1, [x2, #0xb]
    // 0xb0eb64: LoadField: r1 = r2->field_f
    //     0xb0eb64: ldur            w1, [x2, #0xf]
    // 0xb0eb68: DecompressPointer r1
    //     0xb0eb68: add             x1, x1, HEAP, lsl #32
    // 0xb0eb6c: ldur            x0, [fp, #-8]
    // 0xb0eb70: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0eb70: add             x25, x1, x3, lsl #2
    //     0xb0eb74: add             x25, x25, #0xf
    //     0xb0eb78: str             w0, [x25]
    //     0xb0eb7c: tbz             w0, #0, #0xb0eb98
    //     0xb0eb80: ldurb           w16, [x1, #-1]
    //     0xb0eb84: ldurb           w17, [x0, #-1]
    //     0xb0eb88: and             x16, x17, x16, lsr #2
    //     0xb0eb8c: tst             x16, HEAP, lsr #32
    //     0xb0eb90: b.eq            #0xb0eb98
    //     0xb0eb94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0eb98: r0 = Column()
    //     0xb0eb98: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb0eb9c: mov             x1, x0
    // 0xb0eba0: r0 = Instance_Axis
    //     0xb0eba0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb0eba4: stur            x1, [fp, #-8]
    // 0xb0eba8: StoreField: r1->field_f = r0
    //     0xb0eba8: stur            w0, [x1, #0xf]
    // 0xb0ebac: r0 = Instance_MainAxisAlignment
    //     0xb0ebac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0ebb0: ldr             x0, [x0, #0xa08]
    // 0xb0ebb4: StoreField: r1->field_13 = r0
    //     0xb0ebb4: stur            w0, [x1, #0x13]
    // 0xb0ebb8: r0 = Instance_MainAxisSize
    //     0xb0ebb8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb0ebbc: ldr             x0, [x0, #0xdd0]
    // 0xb0ebc0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0ebc0: stur            w0, [x1, #0x17]
    // 0xb0ebc4: r0 = Instance_CrossAxisAlignment
    //     0xb0ebc4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb0ebc8: ldr             x0, [x0, #0x890]
    // 0xb0ebcc: StoreField: r1->field_1b = r0
    //     0xb0ebcc: stur            w0, [x1, #0x1b]
    // 0xb0ebd0: r0 = Instance_VerticalDirection
    //     0xb0ebd0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0ebd4: ldr             x0, [x0, #0xa20]
    // 0xb0ebd8: StoreField: r1->field_23 = r0
    //     0xb0ebd8: stur            w0, [x1, #0x23]
    // 0xb0ebdc: r0 = Instance_Clip
    //     0xb0ebdc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0ebe0: ldr             x0, [x0, #0x38]
    // 0xb0ebe4: StoreField: r1->field_2b = r0
    //     0xb0ebe4: stur            w0, [x1, #0x2b]
    // 0xb0ebe8: StoreField: r1->field_2f = rZR
    //     0xb0ebe8: stur            xzr, [x1, #0x2f]
    // 0xb0ebec: ldur            x0, [fp, #-0x20]
    // 0xb0ebf0: StoreField: r1->field_b = r0
    //     0xb0ebf0: stur            w0, [x1, #0xb]
    // 0xb0ebf4: r0 = Container()
    //     0xb0ebf4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0ebf8: stur            x0, [fp, #-0x10]
    // 0xb0ebfc: r16 = Instance_BoxDecoration
    //     0xb0ebfc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57bd8] Obj!BoxDecoration@d64ad1
    //     0xb0ec00: ldr             x16, [x16, #0xbd8]
    // 0xb0ec04: ldur            lr, [fp, #-8]
    // 0xb0ec08: stp             lr, x16, [SP]
    // 0xb0ec0c: mov             x1, x0
    // 0xb0ec10: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb0ec10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb0ec14: ldr             x4, [x4, #0x88]
    // 0xb0ec18: r0 = Container()
    //     0xb0ec18: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0ec1c: ldur            x0, [fp, #-0x10]
    // 0xb0ec20: LeaveFrame
    //     0xb0ec20: mov             SP, fp
    //     0xb0ec24: ldp             fp, lr, [SP], #0x10
    // 0xb0ec28: ret
    //     0xb0ec28: ret             
    // 0xb0ec2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0ec2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0ec30: b               #0xb0e0c4
    // 0xb0ec34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ec34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0ec38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ec38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0ec3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ec3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0ec40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0ec40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0ec44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ec44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0ec48, size: 0x364
    // 0xb0ec48: EnterFrame
    //     0xb0ec48: stp             fp, lr, [SP, #-0x10]!
    //     0xb0ec4c: mov             fp, SP
    // 0xb0ec50: AllocStack(0x50)
    //     0xb0ec50: sub             SP, SP, #0x50
    // 0xb0ec54: SetupParameters()
    //     0xb0ec54: ldr             x0, [fp, #0x10]
    //     0xb0ec58: ldur            w3, [x0, #0x17]
    //     0xb0ec5c: add             x3, x3, HEAP, lsl #32
    //     0xb0ec60: stur            x3, [fp, #-8]
    // 0xb0ec64: CheckStackOverflow
    //     0xb0ec64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0ec68: cmp             SP, x16
    //     0xb0ec6c: b.ls            #0xb0ef88
    // 0xb0ec70: LoadField: r0 = r3->field_f
    //     0xb0ec70: ldur            w0, [x3, #0xf]
    // 0xb0ec74: DecompressPointer r0
    //     0xb0ec74: add             x0, x0, HEAP, lsl #32
    // 0xb0ec78: LoadField: r1 = r0->field_b
    //     0xb0ec78: ldur            w1, [x0, #0xb]
    // 0xb0ec7c: DecompressPointer r1
    //     0xb0ec7c: add             x1, x1, HEAP, lsl #32
    // 0xb0ec80: cmp             w1, NULL
    // 0xb0ec84: b.eq            #0xb0ef90
    // 0xb0ec88: LoadField: r0 = r1->field_b
    //     0xb0ec88: ldur            w0, [x1, #0xb]
    // 0xb0ec8c: DecompressPointer r0
    //     0xb0ec8c: add             x0, x0, HEAP, lsl #32
    // 0xb0ec90: LoadField: r1 = r0->field_d7
    //     0xb0ec90: ldur            w1, [x0, #0xd7]
    // 0xb0ec94: DecompressPointer r1
    //     0xb0ec94: add             x1, x1, HEAP, lsl #32
    // 0xb0ec98: cmp             w1, NULL
    // 0xb0ec9c: b.ne            #0xb0ecb8
    // 0xb0eca0: r1 = <AllSkuDatum>
    //     0xb0eca0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xb0eca4: ldr             x1, [x1, #0xea8]
    // 0xb0eca8: r2 = 0
    //     0xb0eca8: movz            x2, #0
    // 0xb0ecac: r0 = _GrowableList()
    //     0xb0ecac: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb0ecb0: mov             x3, x0
    // 0xb0ecb4: b               #0xb0ecbc
    // 0xb0ecb8: mov             x3, x1
    // 0xb0ecbc: ldur            x0, [fp, #-8]
    // 0xb0ecc0: stur            x3, [fp, #-0x10]
    // 0xb0ecc4: LoadField: r1 = r0->field_f
    //     0xb0ecc4: ldur            w1, [x0, #0xf]
    // 0xb0ecc8: DecompressPointer r1
    //     0xb0ecc8: add             x1, x1, HEAP, lsl #32
    // 0xb0eccc: LoadField: r2 = r1->field_b
    //     0xb0eccc: ldur            w2, [x1, #0xb]
    // 0xb0ecd0: DecompressPointer r2
    //     0xb0ecd0: add             x2, x2, HEAP, lsl #32
    // 0xb0ecd4: cmp             w2, NULL
    // 0xb0ecd8: b.eq            #0xb0ef94
    // 0xb0ecdc: LoadField: r1 = r2->field_b
    //     0xb0ecdc: ldur            w1, [x2, #0xb]
    // 0xb0ece0: DecompressPointer r1
    //     0xb0ece0: add             x1, x1, HEAP, lsl #32
    // 0xb0ece4: LoadField: r2 = r1->field_db
    //     0xb0ece4: ldur            w2, [x1, #0xdb]
    // 0xb0ece8: DecompressPointer r2
    //     0xb0ece8: add             x2, x2, HEAP, lsl #32
    // 0xb0ecec: cmp             w2, NULL
    // 0xb0ecf0: b.ne            #0xb0ed0c
    // 0xb0ecf4: r1 = <AllGroupedSkusDatum>
    //     0xb0ecf4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f28] TypeArguments: <AllGroupedSkusDatum>
    //     0xb0ecf8: ldr             x1, [x1, #0xf28]
    // 0xb0ecfc: r2 = 0
    //     0xb0ecfc: movz            x2, #0
    // 0xb0ed00: r0 = _GrowableList()
    //     0xb0ed00: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb0ed04: mov             x3, x0
    // 0xb0ed08: b               #0xb0ed10
    // 0xb0ed0c: mov             x3, x2
    // 0xb0ed10: ldur            x2, [fp, #-0x10]
    // 0xb0ed14: LoadField: r0 = r2->field_b
    //     0xb0ed14: ldur            w0, [x2, #0xb]
    // 0xb0ed18: r1 = LoadInt32Instr(r0)
    //     0xb0ed18: sbfx            x1, x0, #1, #0x1f
    // 0xb0ed1c: cbz             x1, #0xb0ed38
    // 0xb0ed20: ldur            x0, [fp, #-8]
    // 0xb0ed24: LoadField: r4 = r0->field_f
    //     0xb0ed24: ldur            w4, [x0, #0xf]
    // 0xb0ed28: DecompressPointer r4
    //     0xb0ed28: add             x4, x4, HEAP, lsl #32
    // 0xb0ed2c: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xb0ed2c: ldur            x5, [x4, #0x17]
    // 0xb0ed30: cmp             x5, x1
    // 0xb0ed34: b.lt            #0xb0ed48
    // 0xb0ed38: r0 = Null
    //     0xb0ed38: mov             x0, NULL
    // 0xb0ed3c: LeaveFrame
    //     0xb0ed3c: mov             SP, fp
    //     0xb0ed40: ldp             fp, lr, [SP], #0x10
    // 0xb0ed44: ret
    //     0xb0ed44: ret             
    // 0xb0ed48: mov             x0, x1
    // 0xb0ed4c: mov             x1, x5
    // 0xb0ed50: cmp             x1, x0
    // 0xb0ed54: b.hs            #0xb0ef98
    // 0xb0ed58: LoadField: r0 = r2->field_f
    //     0xb0ed58: ldur            w0, [x2, #0xf]
    // 0xb0ed5c: DecompressPointer r0
    //     0xb0ed5c: add             x0, x0, HEAP, lsl #32
    // 0xb0ed60: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0ed60: add             x16, x0, x5, lsl #2
    //     0xb0ed64: ldur            w1, [x16, #0xf]
    // 0xb0ed68: DecompressPointer r1
    //     0xb0ed68: add             x1, x1, HEAP, lsl #32
    // 0xb0ed6c: LoadField: r0 = r1->field_f
    //     0xb0ed6c: ldur            w0, [x1, #0xf]
    // 0xb0ed70: DecompressPointer r0
    //     0xb0ed70: add             x0, x0, HEAP, lsl #32
    // 0xb0ed74: cmp             w0, NULL
    // 0xb0ed78: b.ne            #0xb0ed84
    // 0xb0ed7c: r2 = ""
    //     0xb0ed7c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ed80: b               #0xb0ed88
    // 0xb0ed84: mov             x2, x0
    // 0xb0ed88: LoadField: r0 = r1->field_b
    //     0xb0ed88: ldur            w0, [x1, #0xb]
    // 0xb0ed8c: DecompressPointer r0
    //     0xb0ed8c: add             x0, x0, HEAP, lsl #32
    // 0xb0ed90: cmp             w0, NULL
    // 0xb0ed94: b.ne            #0xb0eda0
    // 0xb0ed98: r6 = ""
    //     0xb0ed98: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ed9c: b               #0xb0eda4
    // 0xb0eda0: mov             x6, x0
    // 0xb0eda4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb0eda4: ldur            w0, [x1, #0x17]
    // 0xb0eda8: DecompressPointer r0
    //     0xb0eda8: add             x0, x0, HEAP, lsl #32
    // 0xb0edac: cmp             w0, NULL
    // 0xb0edb0: b.ne            #0xb0edbc
    // 0xb0edb4: r7 = 0
    //     0xb0edb4: movz            x7, #0
    // 0xb0edb8: b               #0xb0edcc
    // 0xb0edbc: r1 = LoadInt32Instr(r0)
    //     0xb0edbc: sbfx            x1, x0, #1, #0x1f
    //     0xb0edc0: tbz             w0, #0, #0xb0edc8
    //     0xb0edc4: ldur            x1, [x0, #7]
    // 0xb0edc8: mov             x7, x1
    // 0xb0edcc: LoadField: r0 = r3->field_b
    //     0xb0edcc: ldur            w0, [x3, #0xb]
    // 0xb0edd0: r1 = LoadInt32Instr(r0)
    //     0xb0edd0: sbfx            x1, x0, #1, #0x1f
    // 0xb0edd4: cbz             x1, #0xb0eef8
    // 0xb0edd8: LoadField: r8 = r4->field_1f
    //     0xb0edd8: ldur            x8, [x4, #0x1f]
    // 0xb0eddc: cmp             x8, x1
    // 0xb0ede0: b.ge            #0xb0eef8
    // 0xb0ede4: mov             x0, x1
    // 0xb0ede8: mov             x1, x8
    // 0xb0edec: cmp             x1, x0
    // 0xb0edf0: b.hs            #0xb0ef9c
    // 0xb0edf4: LoadField: r0 = r3->field_f
    //     0xb0edf4: ldur            w0, [x3, #0xf]
    // 0xb0edf8: DecompressPointer r0
    //     0xb0edf8: add             x0, x0, HEAP, lsl #32
    // 0xb0edfc: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb0edfc: add             x16, x0, x8, lsl #2
    //     0xb0ee00: ldur            w1, [x16, #0xf]
    // 0xb0ee04: DecompressPointer r1
    //     0xb0ee04: add             x1, x1, HEAP, lsl #32
    // 0xb0ee08: LoadField: r0 = r1->field_b
    //     0xb0ee08: ldur            w0, [x1, #0xb]
    // 0xb0ee0c: DecompressPointer r0
    //     0xb0ee0c: add             x0, x0, HEAP, lsl #32
    // 0xb0ee10: cmp             w0, NULL
    // 0xb0ee14: b.ne            #0xb0ee20
    // 0xb0ee18: r3 = ""
    //     0xb0ee18: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ee1c: b               #0xb0ee24
    // 0xb0ee20: mov             x3, x0
    // 0xb0ee24: LoadField: r0 = r1->field_7
    //     0xb0ee24: ldur            w0, [x1, #7]
    // 0xb0ee28: DecompressPointer r0
    //     0xb0ee28: add             x0, x0, HEAP, lsl #32
    // 0xb0ee2c: cmp             w0, NULL
    // 0xb0ee30: b.ne            #0xb0ee3c
    // 0xb0ee34: r8 = ""
    //     0xb0ee34: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ee38: b               #0xb0ee40
    // 0xb0ee3c: mov             x8, x0
    // 0xb0ee40: ArrayLoad: r9 = r1[0]  ; List_4
    //     0xb0ee40: ldur            w9, [x1, #0x17]
    // 0xb0ee44: DecompressPointer r9
    //     0xb0ee44: add             x9, x9, HEAP, lsl #32
    // 0xb0ee48: cmp             w9, NULL
    // 0xb0ee4c: b.ne            #0xb0ee58
    // 0xb0ee50: r0 = Null
    //     0xb0ee50: mov             x0, NULL
    // 0xb0ee54: b               #0xb0ee70
    // 0xb0ee58: LoadField: r0 = r9->field_b
    //     0xb0ee58: ldur            w0, [x9, #0xb]
    // 0xb0ee5c: cbnz            w0, #0xb0ee68
    // 0xb0ee60: r1 = false
    //     0xb0ee60: add             x1, NULL, #0x30  ; false
    // 0xb0ee64: b               #0xb0ee6c
    // 0xb0ee68: r1 = true
    //     0xb0ee68: add             x1, NULL, #0x20  ; true
    // 0xb0ee6c: mov             x0, x1
    // 0xb0ee70: cmp             w0, NULL
    // 0xb0ee74: b.eq            #0xb0eee4
    // 0xb0ee78: tbnz            w0, #4, #0xb0eee4
    // 0xb0ee7c: cmp             w9, NULL
    // 0xb0ee80: b.eq            #0xb0efa0
    // 0xb0ee84: LoadField: r0 = r9->field_b
    //     0xb0ee84: ldur            w0, [x9, #0xb]
    // 0xb0ee88: r1 = LoadInt32Instr(r0)
    //     0xb0ee88: sbfx            x1, x0, #1, #0x1f
    // 0xb0ee8c: cmp             x5, x1
    // 0xb0ee90: b.ge            #0xb0eee4
    // 0xb0ee94: mov             x0, x1
    // 0xb0ee98: mov             x1, x5
    // 0xb0ee9c: cmp             x1, x0
    // 0xb0eea0: b.hs            #0xb0efa4
    // 0xb0eea4: LoadField: r0 = r9->field_f
    //     0xb0eea4: ldur            w0, [x9, #0xf]
    // 0xb0eea8: DecompressPointer r0
    //     0xb0eea8: add             x0, x0, HEAP, lsl #32
    // 0xb0eeac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0eeac: add             x16, x0, x5, lsl #2
    //     0xb0eeb0: ldur            w1, [x16, #0xf]
    // 0xb0eeb4: DecompressPointer r1
    //     0xb0eeb4: add             x1, x1, HEAP, lsl #32
    // 0xb0eeb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb0eeb8: ldur            w0, [x1, #0x17]
    // 0xb0eebc: DecompressPointer r0
    //     0xb0eebc: add             x0, x0, HEAP, lsl #32
    // 0xb0eec0: cmp             w0, NULL
    // 0xb0eec4: b.ne            #0xb0eed0
    // 0xb0eec8: r0 = 0
    //     0xb0eec8: movz            x0, #0
    // 0xb0eecc: b               #0xb0eee8
    // 0xb0eed0: r1 = LoadInt32Instr(r0)
    //     0xb0eed0: sbfx            x1, x0, #1, #0x1f
    //     0xb0eed4: tbz             w0, #0, #0xb0eedc
    //     0xb0eed8: ldur            x1, [x0, #7]
    // 0xb0eedc: mov             x0, x1
    // 0xb0eee0: b               #0xb0eee8
    // 0xb0eee4: r0 = 0
    //     0xb0eee4: movz            x0, #0
    // 0xb0eee8: mov             x5, x8
    // 0xb0eeec: mov             x8, x3
    // 0xb0eef0: mov             x3, x0
    // 0xb0eef4: b               #0xb0ef04
    // 0xb0eef8: r8 = ""
    //     0xb0eef8: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0eefc: r5 = ""
    //     0xb0eefc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0ef00: r3 = 0
    //     0xb0ef00: movz            x3, #0
    // 0xb0ef04: LoadField: r0 = r4->field_b
    //     0xb0ef04: ldur            w0, [x4, #0xb]
    // 0xb0ef08: DecompressPointer r0
    //     0xb0ef08: add             x0, x0, HEAP, lsl #32
    // 0xb0ef0c: cmp             w0, NULL
    // 0xb0ef10: b.eq            #0xb0efa8
    // 0xb0ef14: LoadField: r4 = r0->field_b
    //     0xb0ef14: ldur            w4, [x0, #0xb]
    // 0xb0ef18: DecompressPointer r4
    //     0xb0ef18: add             x4, x4, HEAP, lsl #32
    // 0xb0ef1c: LoadField: r9 = r0->field_13
    //     0xb0ef1c: ldur            w9, [x0, #0x13]
    // 0xb0ef20: DecompressPointer r9
    //     0xb0ef20: add             x9, x9, HEAP, lsl #32
    // 0xb0ef24: r0 = BoxInt64Instr(r7)
    //     0xb0ef24: sbfiz           x0, x7, #1, #0x1f
    //     0xb0ef28: cmp             x7, x0, asr #1
    //     0xb0ef2c: b.eq            #0xb0ef38
    //     0xb0ef30: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0ef34: stur            x7, [x0, #7]
    // 0xb0ef38: mov             x7, x0
    // 0xb0ef3c: r0 = BoxInt64Instr(r3)
    //     0xb0ef3c: sbfiz           x0, x3, #1, #0x1f
    //     0xb0ef40: cmp             x3, x0, asr #1
    //     0xb0ef44: b.eq            #0xb0ef50
    //     0xb0ef48: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0ef4c: stur            x3, [x0, #7]
    // 0xb0ef50: stp             x2, x9, [SP, #0x30]
    // 0xb0ef54: stp             x7, x6, [SP, #0x20]
    // 0xb0ef58: stp             x8, x4, [SP, #0x10]
    // 0xb0ef5c: stp             x0, x5, [SP]
    // 0xb0ef60: r4 = 0
    //     0xb0ef60: movz            x4, #0
    // 0xb0ef64: ldr             x0, [SP, #0x38]
    // 0xb0ef68: r16 = UnlinkedCall_0x613b5c
    //     0xb0ef68: add             x16, PP, #0x57, lsl #12  ; [pp+0x57be0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb0ef6c: add             x16, x16, #0xbe0
    // 0xb0ef70: ldp             x5, lr, [x16]
    // 0xb0ef74: blr             lr
    // 0xb0ef78: r0 = Null
    //     0xb0ef78: mov             x0, NULL
    // 0xb0ef7c: LeaveFrame
    //     0xb0ef7c: mov             SP, fp
    //     0xb0ef80: ldp             fp, lr, [SP], #0x10
    // 0xb0ef84: ret
    //     0xb0ef84: ret             
    // 0xb0ef88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0ef88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0ef8c: b               #0xb0ec70
    // 0xb0ef90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ef90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0ef94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0ef94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0ef98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0ef98: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0ef9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0ef9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0efa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0efa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0efa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0efa4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0efa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0efa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb0efac, size: 0x2d4
    // 0xb0efac: EnterFrame
    //     0xb0efac: stp             fp, lr, [SP, #-0x10]!
    //     0xb0efb0: mov             fp, SP
    // 0xb0efb4: AllocStack(0x40)
    //     0xb0efb4: sub             SP, SP, #0x40
    // 0xb0efb8: SetupParameters()
    //     0xb0efb8: ldr             x0, [fp, #0x20]
    //     0xb0efbc: ldur            w1, [x0, #0x17]
    //     0xb0efc0: add             x1, x1, HEAP, lsl #32
    //     0xb0efc4: stur            x1, [fp, #-8]
    // 0xb0efc8: CheckStackOverflow
    //     0xb0efc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0efcc: cmp             SP, x16
    //     0xb0efd0: b.ls            #0xb0f254
    // 0xb0efd4: r1 = 2
    //     0xb0efd4: movz            x1, #0x2
    // 0xb0efd8: r0 = AllocateContext()
    //     0xb0efd8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0efdc: mov             x3, x0
    // 0xb0efe0: ldur            x2, [fp, #-8]
    // 0xb0efe4: stur            x3, [fp, #-0x18]
    // 0xb0efe8: StoreField: r3->field_b = r2
    //     0xb0efe8: stur            w2, [x3, #0xb]
    // 0xb0efec: ldr             x4, [fp, #0x10]
    // 0xb0eff0: StoreField: r3->field_f = r4
    //     0xb0eff0: stur            w4, [x3, #0xf]
    // 0xb0eff4: LoadField: r5 = r2->field_f
    //     0xb0eff4: ldur            w5, [x2, #0xf]
    // 0xb0eff8: DecompressPointer r5
    //     0xb0eff8: add             x5, x5, HEAP, lsl #32
    // 0xb0effc: LoadField: r0 = r5->field_b
    //     0xb0effc: ldur            w0, [x5, #0xb]
    // 0xb0f000: DecompressPointer r0
    //     0xb0f000: add             x0, x0, HEAP, lsl #32
    // 0xb0f004: cmp             w0, NULL
    // 0xb0f008: b.eq            #0xb0f25c
    // 0xb0f00c: LoadField: r1 = r0->field_b
    //     0xb0f00c: ldur            w1, [x0, #0xb]
    // 0xb0f010: DecompressPointer r1
    //     0xb0f010: add             x1, x1, HEAP, lsl #32
    // 0xb0f014: LoadField: r6 = r1->field_db
    //     0xb0f014: ldur            w6, [x1, #0xdb]
    // 0xb0f018: DecompressPointer r6
    //     0xb0f018: add             x6, x6, HEAP, lsl #32
    // 0xb0f01c: cmp             w6, NULL
    // 0xb0f020: b.ne            #0xb0f02c
    // 0xb0f024: r0 = Null
    //     0xb0f024: mov             x0, NULL
    // 0xb0f028: b               #0xb0f0b0
    // 0xb0f02c: LoadField: r7 = r5->field_1f
    //     0xb0f02c: ldur            x7, [x5, #0x1f]
    // 0xb0f030: LoadField: r0 = r6->field_b
    //     0xb0f030: ldur            w0, [x6, #0xb]
    // 0xb0f034: r1 = LoadInt32Instr(r0)
    //     0xb0f034: sbfx            x1, x0, #1, #0x1f
    // 0xb0f038: mov             x0, x1
    // 0xb0f03c: mov             x1, x7
    // 0xb0f040: cmp             x1, x0
    // 0xb0f044: b.hs            #0xb0f260
    // 0xb0f048: LoadField: r0 = r6->field_f
    //     0xb0f048: ldur            w0, [x6, #0xf]
    // 0xb0f04c: DecompressPointer r0
    //     0xb0f04c: add             x0, x0, HEAP, lsl #32
    // 0xb0f050: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb0f050: add             x16, x0, x7, lsl #2
    //     0xb0f054: ldur            w1, [x16, #0xf]
    // 0xb0f058: DecompressPointer r1
    //     0xb0f058: add             x1, x1, HEAP, lsl #32
    // 0xb0f05c: ArrayLoad: r6 = r1[0]  ; List_4
    //     0xb0f05c: ldur            w6, [x1, #0x17]
    // 0xb0f060: DecompressPointer r6
    //     0xb0f060: add             x6, x6, HEAP, lsl #32
    // 0xb0f064: cmp             w6, NULL
    // 0xb0f068: b.ne            #0xb0f074
    // 0xb0f06c: r0 = Null
    //     0xb0f06c: mov             x0, NULL
    // 0xb0f070: b               #0xb0f0b0
    // 0xb0f074: LoadField: r0 = r6->field_b
    //     0xb0f074: ldur            w0, [x6, #0xb]
    // 0xb0f078: r7 = LoadInt32Instr(r4)
    //     0xb0f078: sbfx            x7, x4, #1, #0x1f
    //     0xb0f07c: tbz             w4, #0, #0xb0f084
    //     0xb0f080: ldur            x7, [x4, #7]
    // 0xb0f084: r1 = LoadInt32Instr(r0)
    //     0xb0f084: sbfx            x1, x0, #1, #0x1f
    // 0xb0f088: mov             x0, x1
    // 0xb0f08c: mov             x1, x7
    // 0xb0f090: cmp             x1, x0
    // 0xb0f094: b.hs            #0xb0f264
    // 0xb0f098: LoadField: r0 = r6->field_f
    //     0xb0f098: ldur            w0, [x6, #0xf]
    // 0xb0f09c: DecompressPointer r0
    //     0xb0f09c: add             x0, x0, HEAP, lsl #32
    // 0xb0f0a0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb0f0a0: add             x16, x0, x7, lsl #2
    //     0xb0f0a4: ldur            w1, [x16, #0xf]
    // 0xb0f0a8: DecompressPointer r1
    //     0xb0f0a8: add             x1, x1, HEAP, lsl #32
    // 0xb0f0ac: mov             x0, x1
    // 0xb0f0b0: stur            x0, [fp, #-0x10]
    // 0xb0f0b4: StoreField: r3->field_13 = r0
    //     0xb0f0b4: stur            w0, [x3, #0x13]
    // 0xb0f0b8: LoadField: r1 = r5->field_13
    //     0xb0f0b8: ldur            w1, [x5, #0x13]
    // 0xb0f0bc: DecompressPointer r1
    //     0xb0f0bc: add             x1, x1, HEAP, lsl #32
    // 0xb0f0c0: r16 = Sentinel
    //     0xb0f0c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f0c4: cmp             w1, w16
    // 0xb0f0c8: b.eq            #0xb0f268
    // 0xb0f0cc: cmp             w1, w0
    // 0xb0f0d0: b.ne            #0xb0f0e8
    // 0xb0f0d4: ldr             x1, [fp, #0x18]
    // 0xb0f0d8: r0 = of()
    //     0xb0f0d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0f0dc: LoadField: r1 = r0->field_5b
    //     0xb0f0dc: ldur            w1, [x0, #0x5b]
    // 0xb0f0e0: DecompressPointer r1
    //     0xb0f0e0: add             x1, x1, HEAP, lsl #32
    // 0xb0f0e4: b               #0xb0f0ec
    // 0xb0f0e8: r1 = Instance_Color
    //     0xb0f0e8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0f0ec: ldur            x0, [fp, #-0x10]
    // 0xb0f0f0: d0 = 0.000000
    //     0xb0f0f0: eor             v0.16b, v0.16b, v0.16b
    // 0xb0f0f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0f0f4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0f0f8: r0 = styleFrom()
    //     0xb0f0f8: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xb0f0fc: mov             x2, x0
    // 0xb0f100: ldur            x0, [fp, #-0x10]
    // 0xb0f104: stur            x2, [fp, #-0x28]
    // 0xb0f108: cmp             w0, NULL
    // 0xb0f10c: b.ne            #0xb0f118
    // 0xb0f110: r1 = Null
    //     0xb0f110: mov             x1, NULL
    // 0xb0f114: b               #0xb0f120
    // 0xb0f118: LoadField: r1 = r0->field_7
    //     0xb0f118: ldur            w1, [x0, #7]
    // 0xb0f11c: DecompressPointer r1
    //     0xb0f11c: add             x1, x1, HEAP, lsl #32
    // 0xb0f120: cmp             w1, NULL
    // 0xb0f124: b.ne            #0xb0f130
    // 0xb0f128: r4 = ""
    //     0xb0f128: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0f12c: b               #0xb0f134
    // 0xb0f130: mov             x4, x1
    // 0xb0f134: ldur            x3, [fp, #-8]
    // 0xb0f138: ldr             x1, [fp, #0x18]
    // 0xb0f13c: stur            x4, [fp, #-0x20]
    // 0xb0f140: r0 = of()
    //     0xb0f140: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0f144: LoadField: r1 = r0->field_87
    //     0xb0f144: ldur            w1, [x0, #0x87]
    // 0xb0f148: DecompressPointer r1
    //     0xb0f148: add             x1, x1, HEAP, lsl #32
    // 0xb0f14c: LoadField: r0 = r1->field_2b
    //     0xb0f14c: ldur            w0, [x1, #0x2b]
    // 0xb0f150: DecompressPointer r0
    //     0xb0f150: add             x0, x0, HEAP, lsl #32
    // 0xb0f154: ldur            x1, [fp, #-8]
    // 0xb0f158: stur            x0, [fp, #-0x30]
    // 0xb0f15c: LoadField: r2 = r1->field_f
    //     0xb0f15c: ldur            w2, [x1, #0xf]
    // 0xb0f160: DecompressPointer r2
    //     0xb0f160: add             x2, x2, HEAP, lsl #32
    // 0xb0f164: LoadField: r1 = r2->field_13
    //     0xb0f164: ldur            w1, [x2, #0x13]
    // 0xb0f168: DecompressPointer r1
    //     0xb0f168: add             x1, x1, HEAP, lsl #32
    // 0xb0f16c: r16 = Sentinel
    //     0xb0f16c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f170: cmp             w1, w16
    // 0xb0f174: b.eq            #0xb0f274
    // 0xb0f178: ldur            x2, [fp, #-0x10]
    // 0xb0f17c: cmp             w1, w2
    // 0xb0f180: b.ne            #0xb0f18c
    // 0xb0f184: r1 = Instance_Color
    //     0xb0f184: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0f188: b               #0xb0f19c
    // 0xb0f18c: r1 = Instance_Color
    //     0xb0f18c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0f190: d0 = 0.400000
    //     0xb0f190: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb0f194: r0 = withOpacity()
    //     0xb0f194: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb0f198: mov             x1, x0
    // 0xb0f19c: ldur            x0, [fp, #-0x28]
    // 0xb0f1a0: ldur            x2, [fp, #-0x20]
    // 0xb0f1a4: r16 = 16.000000
    //     0xb0f1a4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0f1a8: ldr             x16, [x16, #0x188]
    // 0xb0f1ac: stp             x1, x16, [SP]
    // 0xb0f1b0: ldur            x1, [fp, #-0x30]
    // 0xb0f1b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0f1b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0f1b8: ldr             x4, [x4, #0xaa0]
    // 0xb0f1bc: r0 = copyWith()
    //     0xb0f1bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0f1c0: stur            x0, [fp, #-8]
    // 0xb0f1c4: r0 = Text()
    //     0xb0f1c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0f1c8: mov             x3, x0
    // 0xb0f1cc: ldur            x0, [fp, #-0x20]
    // 0xb0f1d0: stur            x3, [fp, #-0x10]
    // 0xb0f1d4: StoreField: r3->field_b = r0
    //     0xb0f1d4: stur            w0, [x3, #0xb]
    // 0xb0f1d8: ldur            x0, [fp, #-8]
    // 0xb0f1dc: StoreField: r3->field_13 = r0
    //     0xb0f1dc: stur            w0, [x3, #0x13]
    // 0xb0f1e0: r0 = Instance_TextAlign
    //     0xb0f1e0: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb0f1e4: StoreField: r3->field_1b = r0
    //     0xb0f1e4: stur            w0, [x3, #0x1b]
    // 0xb0f1e8: ldur            x2, [fp, #-0x18]
    // 0xb0f1ec: r1 = Function '<anonymous closure>':.
    //     0xb0f1ec: add             x1, PP, #0x57, lsl #12  ; [pp+0x57bf0] AnonymousClosure: (0xb0f280), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0f1f0: ldr             x1, [x1, #0xbf0]
    // 0xb0f1f4: r0 = AllocateClosure()
    //     0xb0f1f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0f1f8: stur            x0, [fp, #-8]
    // 0xb0f1fc: r0 = ElevatedButton()
    //     0xb0f1fc: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xb0f200: mov             x1, x0
    // 0xb0f204: ldur            x0, [fp, #-8]
    // 0xb0f208: stur            x1, [fp, #-0x18]
    // 0xb0f20c: StoreField: r1->field_b = r0
    //     0xb0f20c: stur            w0, [x1, #0xb]
    // 0xb0f210: ldur            x0, [fp, #-0x28]
    // 0xb0f214: StoreField: r1->field_1b = r0
    //     0xb0f214: stur            w0, [x1, #0x1b]
    // 0xb0f218: r0 = false
    //     0xb0f218: add             x0, NULL, #0x30  ; false
    // 0xb0f21c: StoreField: r1->field_27 = r0
    //     0xb0f21c: stur            w0, [x1, #0x27]
    // 0xb0f220: r0 = true
    //     0xb0f220: add             x0, NULL, #0x20  ; true
    // 0xb0f224: StoreField: r1->field_2f = r0
    //     0xb0f224: stur            w0, [x1, #0x2f]
    // 0xb0f228: ldur            x0, [fp, #-0x10]
    // 0xb0f22c: StoreField: r1->field_37 = r0
    //     0xb0f22c: stur            w0, [x1, #0x37]
    // 0xb0f230: r0 = Padding()
    //     0xb0f230: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0f234: r1 = Instance_EdgeInsets
    //     0xb0f234: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb0f238: ldr             x1, [x1, #0x980]
    // 0xb0f23c: StoreField: r0->field_f = r1
    //     0xb0f23c: stur            w1, [x0, #0xf]
    // 0xb0f240: ldur            x1, [fp, #-0x18]
    // 0xb0f244: StoreField: r0->field_b = r1
    //     0xb0f244: stur            w1, [x0, #0xb]
    // 0xb0f248: LeaveFrame
    //     0xb0f248: mov             SP, fp
    //     0xb0f24c: ldp             fp, lr, [SP], #0x10
    // 0xb0f250: ret
    //     0xb0f250: ret             
    // 0xb0f254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f254: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f258: b               #0xb0efd4
    // 0xb0f25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0f25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0f260: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0f260: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0f264: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0f264: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0f268: r9 = dropDownValue
    //     0xb0f268: add             x9, PP, #0x57, lsl #12  ; [pp+0x57bf8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb0f26c: ldr             x9, [x9, #0xbf8]
    // 0xb0f270: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f270: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f274: r9 = dropDownValue
    //     0xb0f274: add             x9, PP, #0x57, lsl #12  ; [pp+0x57bf8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb0f278: ldr             x9, [x9, #0xbf8]
    // 0xb0f27c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f27c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0f280, size: 0x68
    // 0xb0f280: EnterFrame
    //     0xb0f280: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f284: mov             fp, SP
    // 0xb0f288: AllocStack(0x8)
    //     0xb0f288: sub             SP, SP, #8
    // 0xb0f28c: SetupParameters()
    //     0xb0f28c: ldr             x0, [fp, #0x10]
    //     0xb0f290: ldur            w2, [x0, #0x17]
    //     0xb0f294: add             x2, x2, HEAP, lsl #32
    // 0xb0f298: CheckStackOverflow
    //     0xb0f298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f29c: cmp             SP, x16
    //     0xb0f2a0: b.ls            #0xb0f2e0
    // 0xb0f2a4: LoadField: r0 = r2->field_b
    //     0xb0f2a4: ldur            w0, [x2, #0xb]
    // 0xb0f2a8: DecompressPointer r0
    //     0xb0f2a8: add             x0, x0, HEAP, lsl #32
    // 0xb0f2ac: LoadField: r3 = r0->field_f
    //     0xb0f2ac: ldur            w3, [x0, #0xf]
    // 0xb0f2b0: DecompressPointer r3
    //     0xb0f2b0: add             x3, x3, HEAP, lsl #32
    // 0xb0f2b4: stur            x3, [fp, #-8]
    // 0xb0f2b8: r1 = Function '<anonymous closure>':.
    //     0xb0f2b8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c00] AnonymousClosure: (0xa60844), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb0f2bc: ldr             x1, [x1, #0xc00]
    // 0xb0f2c0: r0 = AllocateClosure()
    //     0xb0f2c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0f2c4: ldur            x1, [fp, #-8]
    // 0xb0f2c8: mov             x2, x0
    // 0xb0f2cc: r0 = setState()
    //     0xb0f2cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb0f2d0: r0 = Null
    //     0xb0f2d0: mov             x0, NULL
    // 0xb0f2d4: LeaveFrame
    //     0xb0f2d4: mov             SP, fp
    //     0xb0f2d8: ldp             fp, lr, [SP], #0x10
    // 0xb0f2dc: ret
    //     0xb0f2dc: ret             
    // 0xb0f2e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f2e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f2e4: b               #0xb0f2a4
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb0f2e8, size: 0x284
    // 0xb0f2e8: EnterFrame
    //     0xb0f2e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f2ec: mov             fp, SP
    // 0xb0f2f0: AllocStack(0x40)
    //     0xb0f2f0: sub             SP, SP, #0x40
    // 0xb0f2f4: SetupParameters()
    //     0xb0f2f4: ldr             x0, [fp, #0x20]
    //     0xb0f2f8: ldur            w1, [x0, #0x17]
    //     0xb0f2fc: add             x1, x1, HEAP, lsl #32
    //     0xb0f300: stur            x1, [fp, #-8]
    // 0xb0f304: CheckStackOverflow
    //     0xb0f304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f308: cmp             SP, x16
    //     0xb0f30c: b.ls            #0xb0f544
    // 0xb0f310: r1 = 2
    //     0xb0f310: movz            x1, #0x2
    // 0xb0f314: r0 = AllocateContext()
    //     0xb0f314: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0f318: mov             x3, x0
    // 0xb0f31c: ldur            x2, [fp, #-8]
    // 0xb0f320: stur            x3, [fp, #-0x18]
    // 0xb0f324: StoreField: r3->field_b = r2
    //     0xb0f324: stur            w2, [x3, #0xb]
    // 0xb0f328: ldr             x0, [fp, #0x10]
    // 0xb0f32c: StoreField: r3->field_f = r0
    //     0xb0f32c: stur            w0, [x3, #0xf]
    // 0xb0f330: LoadField: r4 = r2->field_f
    //     0xb0f330: ldur            w4, [x2, #0xf]
    // 0xb0f334: DecompressPointer r4
    //     0xb0f334: add             x4, x4, HEAP, lsl #32
    // 0xb0f338: LoadField: r1 = r4->field_b
    //     0xb0f338: ldur            w1, [x4, #0xb]
    // 0xb0f33c: DecompressPointer r1
    //     0xb0f33c: add             x1, x1, HEAP, lsl #32
    // 0xb0f340: cmp             w1, NULL
    // 0xb0f344: b.eq            #0xb0f54c
    // 0xb0f348: LoadField: r5 = r1->field_b
    //     0xb0f348: ldur            w5, [x1, #0xb]
    // 0xb0f34c: DecompressPointer r5
    //     0xb0f34c: add             x5, x5, HEAP, lsl #32
    // 0xb0f350: LoadField: r6 = r5->field_d7
    //     0xb0f350: ldur            w6, [x5, #0xd7]
    // 0xb0f354: DecompressPointer r6
    //     0xb0f354: add             x6, x6, HEAP, lsl #32
    // 0xb0f358: cmp             w6, NULL
    // 0xb0f35c: b.ne            #0xb0f368
    // 0xb0f360: r0 = Null
    //     0xb0f360: mov             x0, NULL
    // 0xb0f364: b               #0xb0f3a0
    // 0xb0f368: LoadField: r1 = r6->field_b
    //     0xb0f368: ldur            w1, [x6, #0xb]
    // 0xb0f36c: r5 = LoadInt32Instr(r0)
    //     0xb0f36c: sbfx            x5, x0, #1, #0x1f
    //     0xb0f370: tbz             w0, #0, #0xb0f378
    //     0xb0f374: ldur            x5, [x0, #7]
    // 0xb0f378: r0 = LoadInt32Instr(r1)
    //     0xb0f378: sbfx            x0, x1, #1, #0x1f
    // 0xb0f37c: mov             x1, x5
    // 0xb0f380: cmp             x1, x0
    // 0xb0f384: b.hs            #0xb0f550
    // 0xb0f388: LoadField: r0 = r6->field_f
    //     0xb0f388: ldur            w0, [x6, #0xf]
    // 0xb0f38c: DecompressPointer r0
    //     0xb0f38c: add             x0, x0, HEAP, lsl #32
    // 0xb0f390: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0f390: add             x16, x0, x5, lsl #2
    //     0xb0f394: ldur            w1, [x16, #0xf]
    // 0xb0f398: DecompressPointer r1
    //     0xb0f398: add             x1, x1, HEAP, lsl #32
    // 0xb0f39c: mov             x0, x1
    // 0xb0f3a0: stur            x0, [fp, #-0x10]
    // 0xb0f3a4: StoreField: r3->field_13 = r0
    //     0xb0f3a4: stur            w0, [x3, #0x13]
    // 0xb0f3a8: LoadField: r1 = r4->field_13
    //     0xb0f3a8: ldur            w1, [x4, #0x13]
    // 0xb0f3ac: DecompressPointer r1
    //     0xb0f3ac: add             x1, x1, HEAP, lsl #32
    // 0xb0f3b0: r16 = Sentinel
    //     0xb0f3b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f3b4: cmp             w1, w16
    // 0xb0f3b8: b.eq            #0xb0f554
    // 0xb0f3bc: cmp             w1, w0
    // 0xb0f3c0: b.ne            #0xb0f3d8
    // 0xb0f3c4: ldr             x1, [fp, #0x18]
    // 0xb0f3c8: r0 = of()
    //     0xb0f3c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0f3cc: LoadField: r1 = r0->field_5b
    //     0xb0f3cc: ldur            w1, [x0, #0x5b]
    // 0xb0f3d0: DecompressPointer r1
    //     0xb0f3d0: add             x1, x1, HEAP, lsl #32
    // 0xb0f3d4: b               #0xb0f3dc
    // 0xb0f3d8: r1 = Instance_Color
    //     0xb0f3d8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0f3dc: ldur            x0, [fp, #-0x10]
    // 0xb0f3e0: d0 = 0.000000
    //     0xb0f3e0: eor             v0.16b, v0.16b, v0.16b
    // 0xb0f3e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0f3e4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0f3e8: r0 = styleFrom()
    //     0xb0f3e8: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xb0f3ec: mov             x2, x0
    // 0xb0f3f0: ldur            x0, [fp, #-0x10]
    // 0xb0f3f4: stur            x2, [fp, #-0x28]
    // 0xb0f3f8: cmp             w0, NULL
    // 0xb0f3fc: b.ne            #0xb0f408
    // 0xb0f400: r1 = Null
    //     0xb0f400: mov             x1, NULL
    // 0xb0f404: b               #0xb0f410
    // 0xb0f408: LoadField: r1 = r0->field_7
    //     0xb0f408: ldur            w1, [x0, #7]
    // 0xb0f40c: DecompressPointer r1
    //     0xb0f40c: add             x1, x1, HEAP, lsl #32
    // 0xb0f410: cmp             w1, NULL
    // 0xb0f414: b.ne            #0xb0f420
    // 0xb0f418: r4 = ""
    //     0xb0f418: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0f41c: b               #0xb0f424
    // 0xb0f420: mov             x4, x1
    // 0xb0f424: ldur            x3, [fp, #-8]
    // 0xb0f428: ldr             x1, [fp, #0x18]
    // 0xb0f42c: stur            x4, [fp, #-0x20]
    // 0xb0f430: r0 = of()
    //     0xb0f430: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0f434: LoadField: r1 = r0->field_87
    //     0xb0f434: ldur            w1, [x0, #0x87]
    // 0xb0f438: DecompressPointer r1
    //     0xb0f438: add             x1, x1, HEAP, lsl #32
    // 0xb0f43c: LoadField: r0 = r1->field_2b
    //     0xb0f43c: ldur            w0, [x1, #0x2b]
    // 0xb0f440: DecompressPointer r0
    //     0xb0f440: add             x0, x0, HEAP, lsl #32
    // 0xb0f444: ldur            x1, [fp, #-8]
    // 0xb0f448: stur            x0, [fp, #-0x30]
    // 0xb0f44c: LoadField: r2 = r1->field_f
    //     0xb0f44c: ldur            w2, [x1, #0xf]
    // 0xb0f450: DecompressPointer r2
    //     0xb0f450: add             x2, x2, HEAP, lsl #32
    // 0xb0f454: LoadField: r1 = r2->field_13
    //     0xb0f454: ldur            w1, [x2, #0x13]
    // 0xb0f458: DecompressPointer r1
    //     0xb0f458: add             x1, x1, HEAP, lsl #32
    // 0xb0f45c: r16 = Sentinel
    //     0xb0f45c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f460: cmp             w1, w16
    // 0xb0f464: b.eq            #0xb0f560
    // 0xb0f468: ldur            x2, [fp, #-0x10]
    // 0xb0f46c: cmp             w1, w2
    // 0xb0f470: b.ne            #0xb0f47c
    // 0xb0f474: r1 = Instance_Color
    //     0xb0f474: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb0f478: b               #0xb0f48c
    // 0xb0f47c: r1 = Instance_Color
    //     0xb0f47c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0f480: d0 = 0.400000
    //     0xb0f480: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb0f484: r0 = withOpacity()
    //     0xb0f484: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb0f488: mov             x1, x0
    // 0xb0f48c: ldur            x0, [fp, #-0x28]
    // 0xb0f490: ldur            x2, [fp, #-0x20]
    // 0xb0f494: r16 = 16.000000
    //     0xb0f494: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb0f498: ldr             x16, [x16, #0x188]
    // 0xb0f49c: stp             x1, x16, [SP]
    // 0xb0f4a0: ldur            x1, [fp, #-0x30]
    // 0xb0f4a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0f4a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0f4a8: ldr             x4, [x4, #0xaa0]
    // 0xb0f4ac: r0 = copyWith()
    //     0xb0f4ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0f4b0: stur            x0, [fp, #-8]
    // 0xb0f4b4: r0 = Text()
    //     0xb0f4b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0f4b8: mov             x3, x0
    // 0xb0f4bc: ldur            x0, [fp, #-0x20]
    // 0xb0f4c0: stur            x3, [fp, #-0x10]
    // 0xb0f4c4: StoreField: r3->field_b = r0
    //     0xb0f4c4: stur            w0, [x3, #0xb]
    // 0xb0f4c8: ldur            x0, [fp, #-8]
    // 0xb0f4cc: StoreField: r3->field_13 = r0
    //     0xb0f4cc: stur            w0, [x3, #0x13]
    // 0xb0f4d0: r0 = Instance_TextAlign
    //     0xb0f4d0: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb0f4d4: StoreField: r3->field_1b = r0
    //     0xb0f4d4: stur            w0, [x3, #0x1b]
    // 0xb0f4d8: ldur            x2, [fp, #-0x18]
    // 0xb0f4dc: r1 = Function '<anonymous closure>':.
    //     0xb0f4dc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c08] AnonymousClosure: (0xb0f56c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0f4e0: ldr             x1, [x1, #0xc08]
    // 0xb0f4e4: r0 = AllocateClosure()
    //     0xb0f4e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0f4e8: stur            x0, [fp, #-8]
    // 0xb0f4ec: r0 = ElevatedButton()
    //     0xb0f4ec: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xb0f4f0: mov             x1, x0
    // 0xb0f4f4: ldur            x0, [fp, #-8]
    // 0xb0f4f8: stur            x1, [fp, #-0x18]
    // 0xb0f4fc: StoreField: r1->field_b = r0
    //     0xb0f4fc: stur            w0, [x1, #0xb]
    // 0xb0f500: ldur            x0, [fp, #-0x28]
    // 0xb0f504: StoreField: r1->field_1b = r0
    //     0xb0f504: stur            w0, [x1, #0x1b]
    // 0xb0f508: r0 = false
    //     0xb0f508: add             x0, NULL, #0x30  ; false
    // 0xb0f50c: StoreField: r1->field_27 = r0
    //     0xb0f50c: stur            w0, [x1, #0x27]
    // 0xb0f510: r0 = true
    //     0xb0f510: add             x0, NULL, #0x20  ; true
    // 0xb0f514: StoreField: r1->field_2f = r0
    //     0xb0f514: stur            w0, [x1, #0x2f]
    // 0xb0f518: ldur            x0, [fp, #-0x10]
    // 0xb0f51c: StoreField: r1->field_37 = r0
    //     0xb0f51c: stur            w0, [x1, #0x37]
    // 0xb0f520: r0 = Padding()
    //     0xb0f520: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0f524: r1 = Instance_EdgeInsets
    //     0xb0f524: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb0f528: ldr             x1, [x1, #0x980]
    // 0xb0f52c: StoreField: r0->field_f = r1
    //     0xb0f52c: stur            w1, [x0, #0xf]
    // 0xb0f530: ldur            x1, [fp, #-0x18]
    // 0xb0f534: StoreField: r0->field_b = r1
    //     0xb0f534: stur            w1, [x0, #0xb]
    // 0xb0f538: LeaveFrame
    //     0xb0f538: mov             SP, fp
    //     0xb0f53c: ldp             fp, lr, [SP], #0x10
    // 0xb0f540: ret
    //     0xb0f540: ret             
    // 0xb0f544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f548: b               #0xb0f310
    // 0xb0f54c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0f54c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0f550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0f550: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0f554: r9 = dropDownValue
    //     0xb0f554: add             x9, PP, #0x57, lsl #12  ; [pp+0x57bf8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb0f558: ldr             x9, [x9, #0xbf8]
    // 0xb0f55c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f55c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f560: r9 = dropDownValue
    //     0xb0f560: add             x9, PP, #0x57, lsl #12  ; [pp+0x57bf8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb0f564: ldr             x9, [x9, #0xbf8]
    // 0xb0f568: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f568: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0f56c, size: 0x68
    // 0xb0f56c: EnterFrame
    //     0xb0f56c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f570: mov             fp, SP
    // 0xb0f574: AllocStack(0x8)
    //     0xb0f574: sub             SP, SP, #8
    // 0xb0f578: SetupParameters()
    //     0xb0f578: ldr             x0, [fp, #0x10]
    //     0xb0f57c: ldur            w2, [x0, #0x17]
    //     0xb0f580: add             x2, x2, HEAP, lsl #32
    // 0xb0f584: CheckStackOverflow
    //     0xb0f584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f588: cmp             SP, x16
    //     0xb0f58c: b.ls            #0xb0f5cc
    // 0xb0f590: LoadField: r0 = r2->field_b
    //     0xb0f590: ldur            w0, [x2, #0xb]
    // 0xb0f594: DecompressPointer r0
    //     0xb0f594: add             x0, x0, HEAP, lsl #32
    // 0xb0f598: LoadField: r3 = r0->field_f
    //     0xb0f598: ldur            w3, [x0, #0xf]
    // 0xb0f59c: DecompressPointer r3
    //     0xb0f59c: add             x3, x3, HEAP, lsl #32
    // 0xb0f5a0: stur            x3, [fp, #-8]
    // 0xb0f5a4: r1 = Function '<anonymous closure>':.
    //     0xb0f5a4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c10] AnonymousClosure: (0xa610b8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb0f5a8: ldr             x1, [x1, #0xc10]
    // 0xb0f5ac: r0 = AllocateClosure()
    //     0xb0f5ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0f5b0: ldur            x1, [fp, #-8]
    // 0xb0f5b4: mov             x2, x0
    // 0xb0f5b8: r0 = setState()
    //     0xb0f5b8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb0f5bc: r0 = Null
    //     0xb0f5bc: mov             x0, NULL
    // 0xb0f5c0: LeaveFrame
    //     0xb0f5c0: mov             SP, fp
    //     0xb0f5c4: ldp             fp, lr, [SP], #0x10
    // 0xb0f5c8: ret
    //     0xb0f5c8: ret             
    // 0xb0f5cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f5cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f5d0: b               #0xb0f590
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb0f5d4, size: 0x7d8
    // 0xb0f5d4: EnterFrame
    //     0xb0f5d4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f5d8: mov             fp, SP
    // 0xb0f5dc: AllocStack(0x60)
    //     0xb0f5dc: sub             SP, SP, #0x60
    // 0xb0f5e0: SetupParameters()
    //     0xb0f5e0: ldr             x0, [fp, #0x20]
    //     0xb0f5e4: ldur            w1, [x0, #0x17]
    //     0xb0f5e8: add             x1, x1, HEAP, lsl #32
    //     0xb0f5ec: stur            x1, [fp, #-8]
    // 0xb0f5f0: CheckStackOverflow
    //     0xb0f5f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f5f4: cmp             SP, x16
    //     0xb0f5f8: b.ls            #0xb0fd84
    // 0xb0f5fc: r1 = 1
    //     0xb0f5fc: movz            x1, #0x1
    // 0xb0f600: r0 = AllocateContext()
    //     0xb0f600: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0f604: mov             x4, x0
    // 0xb0f608: ldur            x3, [fp, #-8]
    // 0xb0f60c: stur            x4, [fp, #-0x10]
    // 0xb0f610: StoreField: r4->field_b = r3
    //     0xb0f610: stur            w3, [x4, #0xb]
    // 0xb0f614: ldr             x5, [fp, #0x10]
    // 0xb0f618: StoreField: r4->field_f = r5
    //     0xb0f618: stur            w5, [x4, #0xf]
    // 0xb0f61c: LoadField: r0 = r3->field_f
    //     0xb0f61c: ldur            w0, [x3, #0xf]
    // 0xb0f620: DecompressPointer r0
    //     0xb0f620: add             x0, x0, HEAP, lsl #32
    // 0xb0f624: LoadField: r2 = r0->field_27
    //     0xb0f624: ldur            w2, [x0, #0x27]
    // 0xb0f628: DecompressPointer r2
    //     0xb0f628: add             x2, x2, HEAP, lsl #32
    // 0xb0f62c: LoadField: r1 = r0->field_b
    //     0xb0f62c: ldur            w1, [x0, #0xb]
    // 0xb0f630: DecompressPointer r1
    //     0xb0f630: add             x1, x1, HEAP, lsl #32
    // 0xb0f634: cmp             w1, NULL
    // 0xb0f638: b.eq            #0xb0fd8c
    // 0xb0f63c: LoadField: r0 = r1->field_b
    //     0xb0f63c: ldur            w0, [x1, #0xb]
    // 0xb0f640: DecompressPointer r0
    //     0xb0f640: add             x0, x0, HEAP, lsl #32
    // 0xb0f644: LoadField: r6 = r0->field_db
    //     0xb0f644: ldur            w6, [x0, #0xdb]
    // 0xb0f648: DecompressPointer r6
    //     0xb0f648: add             x6, x6, HEAP, lsl #32
    // 0xb0f64c: cmp             w6, NULL
    // 0xb0f650: b.ne            #0xb0f65c
    // 0xb0f654: r0 = Null
    //     0xb0f654: mov             x0, NULL
    // 0xb0f658: b               #0xb0f69c
    // 0xb0f65c: LoadField: r0 = r6->field_b
    //     0xb0f65c: ldur            w0, [x6, #0xb]
    // 0xb0f660: r7 = LoadInt32Instr(r5)
    //     0xb0f660: sbfx            x7, x5, #1, #0x1f
    //     0xb0f664: tbz             w5, #0, #0xb0f66c
    //     0xb0f668: ldur            x7, [x5, #7]
    // 0xb0f66c: r1 = LoadInt32Instr(r0)
    //     0xb0f66c: sbfx            x1, x0, #1, #0x1f
    // 0xb0f670: mov             x0, x1
    // 0xb0f674: mov             x1, x7
    // 0xb0f678: cmp             x1, x0
    // 0xb0f67c: b.hs            #0xb0fd90
    // 0xb0f680: LoadField: r0 = r6->field_f
    //     0xb0f680: ldur            w0, [x6, #0xf]
    // 0xb0f684: DecompressPointer r0
    //     0xb0f684: add             x0, x0, HEAP, lsl #32
    // 0xb0f688: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb0f688: add             x16, x0, x7, lsl #2
    //     0xb0f68c: ldur            w1, [x16, #0xf]
    // 0xb0f690: DecompressPointer r1
    //     0xb0f690: add             x1, x1, HEAP, lsl #32
    // 0xb0f694: LoadField: r0 = r1->field_7
    //     0xb0f694: ldur            w0, [x1, #7]
    // 0xb0f698: DecompressPointer r0
    //     0xb0f698: add             x0, x0, HEAP, lsl #32
    // 0xb0f69c: cmp             w0, NULL
    // 0xb0f6a0: b.ne            #0xb0f6a8
    // 0xb0f6a4: r0 = ""
    //     0xb0f6a4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0f6a8: mov             x1, x2
    // 0xb0f6ac: mov             x2, x0
    // 0xb0f6b0: r0 = contains()
    //     0xb0f6b0: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb0f6b4: tbnz            w0, #4, #0xb0f748
    // 0xb0f6b8: ldr             x1, [fp, #0x18]
    // 0xb0f6bc: r0 = of()
    //     0xb0f6bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0f6c0: LoadField: r2 = r0->field_5b
    //     0xb0f6c0: ldur            w2, [x0, #0x5b]
    // 0xb0f6c4: DecompressPointer r2
    //     0xb0f6c4: add             x2, x2, HEAP, lsl #32
    // 0xb0f6c8: r16 = 2.000000
    //     0xb0f6c8: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xb0f6cc: ldr             x16, [x16, #0xdf8]
    // 0xb0f6d0: str             x16, [SP]
    // 0xb0f6d4: r1 = Null
    //     0xb0f6d4: mov             x1, NULL
    // 0xb0f6d8: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb0f6d8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb0f6dc: ldr             x4, [x4, #0x108]
    // 0xb0f6e0: r0 = Border.all()
    //     0xb0f6e0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb0f6e4: stur            x0, [fp, #-0x18]
    // 0xb0f6e8: r0 = Radius()
    //     0xb0f6e8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0f6ec: d0 = 10.000000
    //     0xb0f6ec: fmov            d0, #10.00000000
    // 0xb0f6f0: stur            x0, [fp, #-0x20]
    // 0xb0f6f4: StoreField: r0->field_7 = d0
    //     0xb0f6f4: stur            d0, [x0, #7]
    // 0xb0f6f8: StoreField: r0->field_f = d0
    //     0xb0f6f8: stur            d0, [x0, #0xf]
    // 0xb0f6fc: r0 = BorderRadius()
    //     0xb0f6fc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0f700: mov             x1, x0
    // 0xb0f704: ldur            x0, [fp, #-0x20]
    // 0xb0f708: stur            x1, [fp, #-0x28]
    // 0xb0f70c: StoreField: r1->field_7 = r0
    //     0xb0f70c: stur            w0, [x1, #7]
    // 0xb0f710: StoreField: r1->field_b = r0
    //     0xb0f710: stur            w0, [x1, #0xb]
    // 0xb0f714: StoreField: r1->field_f = r0
    //     0xb0f714: stur            w0, [x1, #0xf]
    // 0xb0f718: StoreField: r1->field_13 = r0
    //     0xb0f718: stur            w0, [x1, #0x13]
    // 0xb0f71c: r0 = BoxDecoration()
    //     0xb0f71c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb0f720: mov             x1, x0
    // 0xb0f724: ldur            x0, [fp, #-0x18]
    // 0xb0f728: StoreField: r1->field_f = r0
    //     0xb0f728: stur            w0, [x1, #0xf]
    // 0xb0f72c: ldur            x0, [fp, #-0x28]
    // 0xb0f730: StoreField: r1->field_13 = r0
    //     0xb0f730: stur            w0, [x1, #0x13]
    // 0xb0f734: r0 = Instance_BoxShape
    //     0xb0f734: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0f738: ldr             x0, [x0, #0x80]
    // 0xb0f73c: StoreField: r1->field_23 = r0
    //     0xb0f73c: stur            w0, [x1, #0x23]
    // 0xb0f740: mov             x2, x1
    // 0xb0f744: b               #0xb0f7c4
    // 0xb0f748: r0 = Instance_BoxShape
    //     0xb0f748: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0f74c: ldr             x0, [x0, #0x80]
    // 0xb0f750: r1 = Null
    //     0xb0f750: mov             x1, NULL
    // 0xb0f754: r2 = Instance_Color
    //     0xb0f754: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb0f758: ldr             x2, [x2, #0xf88]
    // 0xb0f75c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0f75c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0f760: r0 = Border.all()
    //     0xb0f760: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb0f764: stur            x0, [fp, #-0x18]
    // 0xb0f768: r0 = Radius()
    //     0xb0f768: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0f76c: d0 = 10.000000
    //     0xb0f76c: fmov            d0, #10.00000000
    // 0xb0f770: stur            x0, [fp, #-0x20]
    // 0xb0f774: StoreField: r0->field_7 = d0
    //     0xb0f774: stur            d0, [x0, #7]
    // 0xb0f778: StoreField: r0->field_f = d0
    //     0xb0f778: stur            d0, [x0, #0xf]
    // 0xb0f77c: r0 = BorderRadius()
    //     0xb0f77c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0f780: mov             x1, x0
    // 0xb0f784: ldur            x0, [fp, #-0x20]
    // 0xb0f788: stur            x1, [fp, #-0x28]
    // 0xb0f78c: StoreField: r1->field_7 = r0
    //     0xb0f78c: stur            w0, [x1, #7]
    // 0xb0f790: StoreField: r1->field_b = r0
    //     0xb0f790: stur            w0, [x1, #0xb]
    // 0xb0f794: StoreField: r1->field_f = r0
    //     0xb0f794: stur            w0, [x1, #0xf]
    // 0xb0f798: StoreField: r1->field_13 = r0
    //     0xb0f798: stur            w0, [x1, #0x13]
    // 0xb0f79c: r0 = BoxDecoration()
    //     0xb0f79c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb0f7a0: mov             x1, x0
    // 0xb0f7a4: ldur            x0, [fp, #-0x18]
    // 0xb0f7a8: StoreField: r1->field_f = r0
    //     0xb0f7a8: stur            w0, [x1, #0xf]
    // 0xb0f7ac: ldur            x0, [fp, #-0x28]
    // 0xb0f7b0: StoreField: r1->field_13 = r0
    //     0xb0f7b0: stur            w0, [x1, #0x13]
    // 0xb0f7b4: r0 = Instance_BoxShape
    //     0xb0f7b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0f7b8: ldr             x0, [x0, #0x80]
    // 0xb0f7bc: StoreField: r1->field_23 = r0
    //     0xb0f7bc: stur            w0, [x1, #0x23]
    // 0xb0f7c0: mov             x2, x1
    // 0xb0f7c4: ldur            x1, [fp, #-8]
    // 0xb0f7c8: stur            x2, [fp, #-0x18]
    // 0xb0f7cc: r0 = Radius()
    //     0xb0f7cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb0f7d0: d0 = 10.000000
    //     0xb0f7d0: fmov            d0, #10.00000000
    // 0xb0f7d4: stur            x0, [fp, #-0x20]
    // 0xb0f7d8: StoreField: r0->field_7 = d0
    //     0xb0f7d8: stur            d0, [x0, #7]
    // 0xb0f7dc: StoreField: r0->field_f = d0
    //     0xb0f7dc: stur            d0, [x0, #0xf]
    // 0xb0f7e0: r0 = BorderRadius()
    //     0xb0f7e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb0f7e4: mov             x1, x0
    // 0xb0f7e8: ldur            x0, [fp, #-0x20]
    // 0xb0f7ec: stur            x1, [fp, #-0x28]
    // 0xb0f7f0: StoreField: r1->field_7 = r0
    //     0xb0f7f0: stur            w0, [x1, #7]
    // 0xb0f7f4: StoreField: r1->field_b = r0
    //     0xb0f7f4: stur            w0, [x1, #0xb]
    // 0xb0f7f8: StoreField: r1->field_f = r0
    //     0xb0f7f8: stur            w0, [x1, #0xf]
    // 0xb0f7fc: StoreField: r1->field_13 = r0
    //     0xb0f7fc: stur            w0, [x1, #0x13]
    // 0xb0f800: r0 = RoundedRectangleBorder()
    //     0xb0f800: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb0f804: mov             x3, x0
    // 0xb0f808: ldur            x0, [fp, #-0x28]
    // 0xb0f80c: stur            x3, [fp, #-0x30]
    // 0xb0f810: StoreField: r3->field_b = r0
    //     0xb0f810: stur            w0, [x3, #0xb]
    // 0xb0f814: r0 = Instance_BorderSide
    //     0xb0f814: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb0f818: ldr             x0, [x0, #0xe20]
    // 0xb0f81c: StoreField: r3->field_7 = r0
    //     0xb0f81c: stur            w0, [x3, #7]
    // 0xb0f820: ldur            x4, [fp, #-8]
    // 0xb0f824: LoadField: r0 = r4->field_f
    //     0xb0f824: ldur            w0, [x4, #0xf]
    // 0xb0f828: DecompressPointer r0
    //     0xb0f828: add             x0, x0, HEAP, lsl #32
    // 0xb0f82c: LoadField: r1 = r0->field_b
    //     0xb0f82c: ldur            w1, [x0, #0xb]
    // 0xb0f830: DecompressPointer r1
    //     0xb0f830: add             x1, x1, HEAP, lsl #32
    // 0xb0f834: cmp             w1, NULL
    // 0xb0f838: b.eq            #0xb0fd94
    // 0xb0f83c: LoadField: r0 = r1->field_b
    //     0xb0f83c: ldur            w0, [x1, #0xb]
    // 0xb0f840: DecompressPointer r0
    //     0xb0f840: add             x0, x0, HEAP, lsl #32
    // 0xb0f844: LoadField: r2 = r0->field_db
    //     0xb0f844: ldur            w2, [x0, #0xdb]
    // 0xb0f848: DecompressPointer r2
    //     0xb0f848: add             x2, x2, HEAP, lsl #32
    // 0xb0f84c: cmp             w2, NULL
    // 0xb0f850: b.ne            #0xb0f860
    // 0xb0f854: ldr             x5, [fp, #0x10]
    // 0xb0f858: r0 = Null
    //     0xb0f858: mov             x0, NULL
    // 0xb0f85c: b               #0xb0f8a4
    // 0xb0f860: ldr             x5, [fp, #0x10]
    // 0xb0f864: LoadField: r0 = r2->field_b
    //     0xb0f864: ldur            w0, [x2, #0xb]
    // 0xb0f868: r6 = LoadInt32Instr(r5)
    //     0xb0f868: sbfx            x6, x5, #1, #0x1f
    //     0xb0f86c: tbz             w5, #0, #0xb0f874
    //     0xb0f870: ldur            x6, [x5, #7]
    // 0xb0f874: r1 = LoadInt32Instr(r0)
    //     0xb0f874: sbfx            x1, x0, #1, #0x1f
    // 0xb0f878: mov             x0, x1
    // 0xb0f87c: mov             x1, x6
    // 0xb0f880: cmp             x1, x0
    // 0xb0f884: b.hs            #0xb0fd98
    // 0xb0f888: LoadField: r0 = r2->field_f
    //     0xb0f888: ldur            w0, [x2, #0xf]
    // 0xb0f88c: DecompressPointer r0
    //     0xb0f88c: add             x0, x0, HEAP, lsl #32
    // 0xb0f890: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb0f890: add             x16, x0, x6, lsl #2
    //     0xb0f894: ldur            w1, [x16, #0xf]
    // 0xb0f898: DecompressPointer r1
    //     0xb0f898: add             x1, x1, HEAP, lsl #32
    // 0xb0f89c: LoadField: r0 = r1->field_f
    //     0xb0f89c: ldur            w0, [x1, #0xf]
    // 0xb0f8a0: DecompressPointer r0
    //     0xb0f8a0: add             x0, x0, HEAP, lsl #32
    // 0xb0f8a4: cmp             w0, NULL
    // 0xb0f8a8: b.ne            #0xb0f8b0
    // 0xb0f8ac: r0 = ""
    //     0xb0f8ac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0f8b0: stur            x0, [fp, #-0x20]
    // 0xb0f8b4: r1 = Function '<anonymous closure>':.
    //     0xb0f8b4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c18] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb0f8b8: ldr             x1, [x1, #0xc18]
    // 0xb0f8bc: r2 = Null
    //     0xb0f8bc: mov             x2, NULL
    // 0xb0f8c0: r0 = AllocateClosure()
    //     0xb0f8c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0f8c4: r1 = Function '<anonymous closure>':.
    //     0xb0f8c4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c20] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb0f8c8: ldr             x1, [x1, #0xc20]
    // 0xb0f8cc: r2 = Null
    //     0xb0f8cc: mov             x2, NULL
    // 0xb0f8d0: stur            x0, [fp, #-0x28]
    // 0xb0f8d4: r0 = AllocateClosure()
    //     0xb0f8d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0f8d8: stur            x0, [fp, #-0x38]
    // 0xb0f8dc: r0 = CachedNetworkImage()
    //     0xb0f8dc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb0f8e0: stur            x0, [fp, #-0x40]
    // 0xb0f8e4: r16 = Instance_BoxFit
    //     0xb0f8e4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb0f8e8: ldr             x16, [x16, #0xb18]
    // 0xb0f8ec: ldur            lr, [fp, #-0x28]
    // 0xb0f8f0: stp             lr, x16, [SP, #8]
    // 0xb0f8f4: ldur            x16, [fp, #-0x38]
    // 0xb0f8f8: str             x16, [SP]
    // 0xb0f8fc: mov             x1, x0
    // 0xb0f900: ldur            x2, [fp, #-0x20]
    // 0xb0f904: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb0f904: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb0f908: ldr             x4, [x4, #0x638]
    // 0xb0f90c: r0 = CachedNetworkImage()
    //     0xb0f90c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb0f910: r0 = Card()
    //     0xb0f910: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb0f914: mov             x1, x0
    // 0xb0f918: r0 = 0.000000
    //     0xb0f918: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb0f91c: stur            x1, [fp, #-0x20]
    // 0xb0f920: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0f920: stur            w0, [x1, #0x17]
    // 0xb0f924: ldur            x0, [fp, #-0x30]
    // 0xb0f928: StoreField: r1->field_1b = r0
    //     0xb0f928: stur            w0, [x1, #0x1b]
    // 0xb0f92c: r0 = true
    //     0xb0f92c: add             x0, NULL, #0x20  ; true
    // 0xb0f930: StoreField: r1->field_1f = r0
    //     0xb0f930: stur            w0, [x1, #0x1f]
    // 0xb0f934: r2 = Instance_EdgeInsets
    //     0xb0f934: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb0f938: StoreField: r1->field_27 = r2
    //     0xb0f938: stur            w2, [x1, #0x27]
    // 0xb0f93c: r2 = Instance_Clip
    //     0xb0f93c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb0f940: ldr             x2, [x2, #0xb50]
    // 0xb0f944: StoreField: r1->field_23 = r2
    //     0xb0f944: stur            w2, [x1, #0x23]
    // 0xb0f948: ldur            x2, [fp, #-0x40]
    // 0xb0f94c: StoreField: r1->field_2f = r2
    //     0xb0f94c: stur            w2, [x1, #0x2f]
    // 0xb0f950: StoreField: r1->field_2b = r0
    //     0xb0f950: stur            w0, [x1, #0x2b]
    // 0xb0f954: r2 = Instance__CardVariant
    //     0xb0f954: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb0f958: ldr             x2, [x2, #0xa68]
    // 0xb0f95c: StoreField: r1->field_33 = r2
    //     0xb0f95c: stur            w2, [x1, #0x33]
    // 0xb0f960: r0 = AspectRatio()
    //     0xb0f960: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb0f964: d0 = 0.500000
    //     0xb0f964: fmov            d0, #0.50000000
    // 0xb0f968: stur            x0, [fp, #-0x28]
    // 0xb0f96c: StoreField: r0->field_f = d0
    //     0xb0f96c: stur            d0, [x0, #0xf]
    // 0xb0f970: ldur            x1, [fp, #-0x20]
    // 0xb0f974: StoreField: r0->field_b = r1
    //     0xb0f974: stur            w1, [x0, #0xb]
    // 0xb0f978: r0 = Container()
    //     0xb0f978: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb0f97c: stur            x0, [fp, #-0x20]
    // 0xb0f980: r16 = 80.000000
    //     0xb0f980: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb0f984: ldr             x16, [x16, #0x2f8]
    // 0xb0f988: r30 = 80.000000
    //     0xb0f988: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb0f98c: ldr             lr, [lr, #0x2f8]
    // 0xb0f990: stp             lr, x16, [SP, #0x10]
    // 0xb0f994: ldur            x16, [fp, #-0x18]
    // 0xb0f998: ldur            lr, [fp, #-0x28]
    // 0xb0f99c: stp             lr, x16, [SP]
    // 0xb0f9a0: mov             x1, x0
    // 0xb0f9a4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb0f9a4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb0f9a8: ldr             x4, [x4, #0x870]
    // 0xb0f9ac: r0 = Container()
    //     0xb0f9ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb0f9b0: ldur            x0, [fp, #-8]
    // 0xb0f9b4: LoadField: r1 = r0->field_f
    //     0xb0f9b4: ldur            w1, [x0, #0xf]
    // 0xb0f9b8: DecompressPointer r1
    //     0xb0f9b8: add             x1, x1, HEAP, lsl #32
    // 0xb0f9bc: LoadField: r2 = r1->field_b
    //     0xb0f9bc: ldur            w2, [x1, #0xb]
    // 0xb0f9c0: DecompressPointer r2
    //     0xb0f9c0: add             x2, x2, HEAP, lsl #32
    // 0xb0f9c4: cmp             w2, NULL
    // 0xb0f9c8: b.eq            #0xb0fd9c
    // 0xb0f9cc: LoadField: r0 = r2->field_b
    //     0xb0f9cc: ldur            w0, [x2, #0xb]
    // 0xb0f9d0: DecompressPointer r0
    //     0xb0f9d0: add             x0, x0, HEAP, lsl #32
    // 0xb0f9d4: LoadField: r3 = r0->field_db
    //     0xb0f9d4: ldur            w3, [x0, #0xdb]
    // 0xb0f9d8: DecompressPointer r3
    //     0xb0f9d8: add             x3, x3, HEAP, lsl #32
    // 0xb0f9dc: cmp             w3, NULL
    // 0xb0f9e0: b.ne            #0xb0f9f0
    // 0xb0f9e4: ldr             x4, [fp, #0x10]
    // 0xb0f9e8: r0 = Null
    //     0xb0f9e8: mov             x0, NULL
    // 0xb0f9ec: b               #0xb0fa34
    // 0xb0f9f0: ldr             x4, [fp, #0x10]
    // 0xb0f9f4: LoadField: r0 = r3->field_b
    //     0xb0f9f4: ldur            w0, [x3, #0xb]
    // 0xb0f9f8: r5 = LoadInt32Instr(r4)
    //     0xb0f9f8: sbfx            x5, x4, #1, #0x1f
    //     0xb0f9fc: tbz             w4, #0, #0xb0fa04
    //     0xb0fa00: ldur            x5, [x4, #7]
    // 0xb0fa04: r1 = LoadInt32Instr(r0)
    //     0xb0fa04: sbfx            x1, x0, #1, #0x1f
    // 0xb0fa08: mov             x0, x1
    // 0xb0fa0c: mov             x1, x5
    // 0xb0fa10: cmp             x1, x0
    // 0xb0fa14: b.hs            #0xb0fda0
    // 0xb0fa18: LoadField: r0 = r3->field_f
    //     0xb0fa18: ldur            w0, [x3, #0xf]
    // 0xb0fa1c: DecompressPointer r0
    //     0xb0fa1c: add             x0, x0, HEAP, lsl #32
    // 0xb0fa20: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0fa20: add             x16, x0, x5, lsl #2
    //     0xb0fa24: ldur            w1, [x16, #0xf]
    // 0xb0fa28: DecompressPointer r1
    //     0xb0fa28: add             x1, x1, HEAP, lsl #32
    // 0xb0fa2c: LoadField: r0 = r1->field_13
    //     0xb0fa2c: ldur            w0, [x1, #0x13]
    // 0xb0fa30: DecompressPointer r0
    //     0xb0fa30: add             x0, x0, HEAP, lsl #32
    // 0xb0fa34: cmp             w0, NULL
    // 0xb0fa38: r16 = true
    //     0xb0fa38: add             x16, NULL, #0x20  ; true
    // 0xb0fa3c: r17 = false
    //     0xb0fa3c: add             x17, NULL, #0x30  ; false
    // 0xb0fa40: csel            x3, x16, x17, ne
    // 0xb0fa44: stur            x3, [fp, #-8]
    // 0xb0fa48: LoadField: r0 = r2->field_b
    //     0xb0fa48: ldur            w0, [x2, #0xb]
    // 0xb0fa4c: DecompressPointer r0
    //     0xb0fa4c: add             x0, x0, HEAP, lsl #32
    // 0xb0fa50: LoadField: r5 = r0->field_db
    //     0xb0fa50: ldur            w5, [x0, #0xdb]
    // 0xb0fa54: DecompressPointer r5
    //     0xb0fa54: add             x5, x5, HEAP, lsl #32
    // 0xb0fa58: cmp             w5, NULL
    // 0xb0fa5c: b.ne            #0xb0fa68
    // 0xb0fa60: r0 = Null
    //     0xb0fa60: mov             x0, NULL
    // 0xb0fa64: b               #0xb0facc
    // 0xb0fa68: LoadField: r0 = r5->field_b
    //     0xb0fa68: ldur            w0, [x5, #0xb]
    // 0xb0fa6c: r6 = LoadInt32Instr(r4)
    //     0xb0fa6c: sbfx            x6, x4, #1, #0x1f
    //     0xb0fa70: tbz             w4, #0, #0xb0fa78
    //     0xb0fa74: ldur            x6, [x4, #7]
    // 0xb0fa78: r1 = LoadInt32Instr(r0)
    //     0xb0fa78: sbfx            x1, x0, #1, #0x1f
    // 0xb0fa7c: mov             x0, x1
    // 0xb0fa80: mov             x1, x6
    // 0xb0fa84: cmp             x1, x0
    // 0xb0fa88: b.hs            #0xb0fda4
    // 0xb0fa8c: LoadField: r0 = r5->field_f
    //     0xb0fa8c: ldur            w0, [x5, #0xf]
    // 0xb0fa90: DecompressPointer r0
    //     0xb0fa90: add             x0, x0, HEAP, lsl #32
    // 0xb0fa94: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb0fa94: add             x16, x0, x6, lsl #2
    //     0xb0fa98: ldur            w1, [x16, #0xf]
    // 0xb0fa9c: DecompressPointer r1
    //     0xb0fa9c: add             x1, x1, HEAP, lsl #32
    // 0xb0faa0: LoadField: r0 = r1->field_13
    //     0xb0faa0: ldur            w0, [x1, #0x13]
    // 0xb0faa4: DecompressPointer r0
    //     0xb0faa4: add             x0, x0, HEAP, lsl #32
    // 0xb0faa8: cmp             w0, NULL
    // 0xb0faac: b.ne            #0xb0fab8
    // 0xb0fab0: r0 = Null
    //     0xb0fab0: mov             x0, NULL
    // 0xb0fab4: b               #0xb0facc
    // 0xb0fab8: LoadField: r1 = r0->field_7
    //     0xb0fab8: ldur            w1, [x0, #7]
    // 0xb0fabc: cbnz            w1, #0xb0fac8
    // 0xb0fac0: r0 = false
    //     0xb0fac0: add             x0, NULL, #0x30  ; false
    // 0xb0fac4: b               #0xb0facc
    // 0xb0fac8: r0 = true
    //     0xb0fac8: add             x0, NULL, #0x20  ; true
    // 0xb0facc: cmp             w0, NULL
    // 0xb0fad0: b.eq            #0xb0fb60
    // 0xb0fad4: tbnz            w0, #4, #0xb0fb60
    // 0xb0fad8: LoadField: r0 = r2->field_b
    //     0xb0fad8: ldur            w0, [x2, #0xb]
    // 0xb0fadc: DecompressPointer r0
    //     0xb0fadc: add             x0, x0, HEAP, lsl #32
    // 0xb0fae0: LoadField: r2 = r0->field_db
    //     0xb0fae0: ldur            w2, [x0, #0xdb]
    // 0xb0fae4: DecompressPointer r2
    //     0xb0fae4: add             x2, x2, HEAP, lsl #32
    // 0xb0fae8: cmp             w2, NULL
    // 0xb0faec: b.ne            #0xb0faf8
    // 0xb0faf0: r0 = Null
    //     0xb0faf0: mov             x0, NULL
    // 0xb0faf4: b               #0xb0fb50
    // 0xb0faf8: LoadField: r0 = r2->field_b
    //     0xb0faf8: ldur            w0, [x2, #0xb]
    // 0xb0fafc: r5 = LoadInt32Instr(r4)
    //     0xb0fafc: sbfx            x5, x4, #1, #0x1f
    //     0xb0fb00: tbz             w4, #0, #0xb0fb08
    //     0xb0fb04: ldur            x5, [x4, #7]
    // 0xb0fb08: r1 = LoadInt32Instr(r0)
    //     0xb0fb08: sbfx            x1, x0, #1, #0x1f
    // 0xb0fb0c: mov             x0, x1
    // 0xb0fb10: mov             x1, x5
    // 0xb0fb14: cmp             x1, x0
    // 0xb0fb18: b.hs            #0xb0fda8
    // 0xb0fb1c: LoadField: r0 = r2->field_f
    //     0xb0fb1c: ldur            w0, [x2, #0xf]
    // 0xb0fb20: DecompressPointer r0
    //     0xb0fb20: add             x0, x0, HEAP, lsl #32
    // 0xb0fb24: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb0fb24: add             x16, x0, x5, lsl #2
    //     0xb0fb28: ldur            w1, [x16, #0xf]
    // 0xb0fb2c: DecompressPointer r1
    //     0xb0fb2c: add             x1, x1, HEAP, lsl #32
    // 0xb0fb30: LoadField: r0 = r1->field_13
    //     0xb0fb30: ldur            w0, [x1, #0x13]
    // 0xb0fb34: DecompressPointer r0
    //     0xb0fb34: add             x0, x0, HEAP, lsl #32
    // 0xb0fb38: cmp             w0, NULL
    // 0xb0fb3c: b.ne            #0xb0fb48
    // 0xb0fb40: r0 = Null
    //     0xb0fb40: mov             x0, NULL
    // 0xb0fb44: b               #0xb0fb50
    // 0xb0fb48: mov             x1, x0
    // 0xb0fb4c: r0 = StringExtension.toTitleCase()
    //     0xb0fb4c: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xb0fb50: str             x0, [SP]
    // 0xb0fb54: r0 = _interpolateSingle()
    //     0xb0fb54: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb0fb58: mov             x3, x0
    // 0xb0fb5c: b               #0xb0fb64
    // 0xb0fb60: r3 = ""
    //     0xb0fb60: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0fb64: ldur            x2, [fp, #-0x20]
    // 0xb0fb68: ldur            x0, [fp, #-8]
    // 0xb0fb6c: ldr             x1, [fp, #0x18]
    // 0xb0fb70: stur            x3, [fp, #-0x18]
    // 0xb0fb74: r0 = of()
    //     0xb0fb74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0fb78: LoadField: r1 = r0->field_87
    //     0xb0fb78: ldur            w1, [x0, #0x87]
    // 0xb0fb7c: DecompressPointer r1
    //     0xb0fb7c: add             x1, x1, HEAP, lsl #32
    // 0xb0fb80: LoadField: r0 = r1->field_2b
    //     0xb0fb80: ldur            w0, [x1, #0x2b]
    // 0xb0fb84: DecompressPointer r0
    //     0xb0fb84: add             x0, x0, HEAP, lsl #32
    // 0xb0fb88: stur            x0, [fp, #-0x28]
    // 0xb0fb8c: r1 = Instance_Color
    //     0xb0fb8c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb0fb90: d0 = 0.700000
    //     0xb0fb90: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb0fb94: ldr             d0, [x17, #0xf48]
    // 0xb0fb98: r0 = withOpacity()
    //     0xb0fb98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb0fb9c: r16 = 12.000000
    //     0xb0fb9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb0fba0: ldr             x16, [x16, #0x9e8]
    // 0xb0fba4: stp             x0, x16, [SP]
    // 0xb0fba8: ldur            x1, [fp, #-0x28]
    // 0xb0fbac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0fbac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0fbb0: ldr             x4, [x4, #0xaa0]
    // 0xb0fbb4: r0 = copyWith()
    //     0xb0fbb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0fbb8: stur            x0, [fp, #-0x28]
    // 0xb0fbbc: r0 = Text()
    //     0xb0fbbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0fbc0: mov             x1, x0
    // 0xb0fbc4: ldur            x0, [fp, #-0x18]
    // 0xb0fbc8: stur            x1, [fp, #-0x30]
    // 0xb0fbcc: StoreField: r1->field_b = r0
    //     0xb0fbcc: stur            w0, [x1, #0xb]
    // 0xb0fbd0: ldur            x0, [fp, #-0x28]
    // 0xb0fbd4: StoreField: r1->field_13 = r0
    //     0xb0fbd4: stur            w0, [x1, #0x13]
    // 0xb0fbd8: r0 = Padding()
    //     0xb0fbd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0fbdc: mov             x2, x0
    // 0xb0fbe0: r0 = Instance_EdgeInsets
    //     0xb0fbe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xb0fbe4: ldr             x0, [x0, #0x9e0]
    // 0xb0fbe8: stur            x2, [fp, #-0x18]
    // 0xb0fbec: StoreField: r2->field_f = r0
    //     0xb0fbec: stur            w0, [x2, #0xf]
    // 0xb0fbf0: ldur            x0, [fp, #-0x30]
    // 0xb0fbf4: StoreField: r2->field_b = r0
    //     0xb0fbf4: stur            w0, [x2, #0xb]
    // 0xb0fbf8: r1 = <FlexParentData>
    //     0xb0fbf8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb0fbfc: ldr             x1, [x1, #0xe00]
    // 0xb0fc00: r0 = Expanded()
    //     0xb0fc00: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb0fc04: mov             x1, x0
    // 0xb0fc08: r0 = 1
    //     0xb0fc08: movz            x0, #0x1
    // 0xb0fc0c: stur            x1, [fp, #-0x28]
    // 0xb0fc10: StoreField: r1->field_13 = r0
    //     0xb0fc10: stur            x0, [x1, #0x13]
    // 0xb0fc14: r0 = Instance_FlexFit
    //     0xb0fc14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb0fc18: ldr             x0, [x0, #0xe08]
    // 0xb0fc1c: StoreField: r1->field_1b = r0
    //     0xb0fc1c: stur            w0, [x1, #0x1b]
    // 0xb0fc20: ldur            x0, [fp, #-0x18]
    // 0xb0fc24: StoreField: r1->field_b = r0
    //     0xb0fc24: stur            w0, [x1, #0xb]
    // 0xb0fc28: r0 = Visibility()
    //     0xb0fc28: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb0fc2c: mov             x3, x0
    // 0xb0fc30: ldur            x0, [fp, #-0x28]
    // 0xb0fc34: stur            x3, [fp, #-0x18]
    // 0xb0fc38: StoreField: r3->field_b = r0
    //     0xb0fc38: stur            w0, [x3, #0xb]
    // 0xb0fc3c: r0 = Instance_SizedBox
    //     0xb0fc3c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb0fc40: StoreField: r3->field_f = r0
    //     0xb0fc40: stur            w0, [x3, #0xf]
    // 0xb0fc44: ldur            x0, [fp, #-8]
    // 0xb0fc48: StoreField: r3->field_13 = r0
    //     0xb0fc48: stur            w0, [x3, #0x13]
    // 0xb0fc4c: r0 = false
    //     0xb0fc4c: add             x0, NULL, #0x30  ; false
    // 0xb0fc50: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0fc50: stur            w0, [x3, #0x17]
    // 0xb0fc54: StoreField: r3->field_1b = r0
    //     0xb0fc54: stur            w0, [x3, #0x1b]
    // 0xb0fc58: StoreField: r3->field_1f = r0
    //     0xb0fc58: stur            w0, [x3, #0x1f]
    // 0xb0fc5c: StoreField: r3->field_23 = r0
    //     0xb0fc5c: stur            w0, [x3, #0x23]
    // 0xb0fc60: StoreField: r3->field_27 = r0
    //     0xb0fc60: stur            w0, [x3, #0x27]
    // 0xb0fc64: StoreField: r3->field_2b = r0
    //     0xb0fc64: stur            w0, [x3, #0x2b]
    // 0xb0fc68: r1 = Null
    //     0xb0fc68: mov             x1, NULL
    // 0xb0fc6c: r2 = 4
    //     0xb0fc6c: movz            x2, #0x4
    // 0xb0fc70: r0 = AllocateArray()
    //     0xb0fc70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0fc74: mov             x2, x0
    // 0xb0fc78: ldur            x0, [fp, #-0x20]
    // 0xb0fc7c: stur            x2, [fp, #-8]
    // 0xb0fc80: StoreField: r2->field_f = r0
    //     0xb0fc80: stur            w0, [x2, #0xf]
    // 0xb0fc84: ldur            x0, [fp, #-0x18]
    // 0xb0fc88: StoreField: r2->field_13 = r0
    //     0xb0fc88: stur            w0, [x2, #0x13]
    // 0xb0fc8c: r1 = <Widget>
    //     0xb0fc8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0fc90: r0 = AllocateGrowableArray()
    //     0xb0fc90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0fc94: mov             x1, x0
    // 0xb0fc98: ldur            x0, [fp, #-8]
    // 0xb0fc9c: stur            x1, [fp, #-0x18]
    // 0xb0fca0: StoreField: r1->field_f = r0
    //     0xb0fca0: stur            w0, [x1, #0xf]
    // 0xb0fca4: r0 = 4
    //     0xb0fca4: movz            x0, #0x4
    // 0xb0fca8: StoreField: r1->field_b = r0
    //     0xb0fca8: stur            w0, [x1, #0xb]
    // 0xb0fcac: r0 = Column()
    //     0xb0fcac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb0fcb0: mov             x1, x0
    // 0xb0fcb4: r0 = Instance_Axis
    //     0xb0fcb4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb0fcb8: stur            x1, [fp, #-8]
    // 0xb0fcbc: StoreField: r1->field_f = r0
    //     0xb0fcbc: stur            w0, [x1, #0xf]
    // 0xb0fcc0: r0 = Instance_MainAxisAlignment
    //     0xb0fcc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0fcc4: ldr             x0, [x0, #0xa08]
    // 0xb0fcc8: StoreField: r1->field_13 = r0
    //     0xb0fcc8: stur            w0, [x1, #0x13]
    // 0xb0fccc: r0 = Instance_MainAxisSize
    //     0xb0fccc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0fcd0: ldr             x0, [x0, #0xa10]
    // 0xb0fcd4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0fcd4: stur            w0, [x1, #0x17]
    // 0xb0fcd8: r0 = Instance_CrossAxisAlignment
    //     0xb0fcd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0fcdc: ldr             x0, [x0, #0xa18]
    // 0xb0fce0: StoreField: r1->field_1b = r0
    //     0xb0fce0: stur            w0, [x1, #0x1b]
    // 0xb0fce4: r0 = Instance_VerticalDirection
    //     0xb0fce4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0fce8: ldr             x0, [x0, #0xa20]
    // 0xb0fcec: StoreField: r1->field_23 = r0
    //     0xb0fcec: stur            w0, [x1, #0x23]
    // 0xb0fcf0: r0 = Instance_Clip
    //     0xb0fcf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0fcf4: ldr             x0, [x0, #0x38]
    // 0xb0fcf8: StoreField: r1->field_2b = r0
    //     0xb0fcf8: stur            w0, [x1, #0x2b]
    // 0xb0fcfc: StoreField: r1->field_2f = rZR
    //     0xb0fcfc: stur            xzr, [x1, #0x2f]
    // 0xb0fd00: ldur            x0, [fp, #-0x18]
    // 0xb0fd04: StoreField: r1->field_b = r0
    //     0xb0fd04: stur            w0, [x1, #0xb]
    // 0xb0fd08: r0 = InkWell()
    //     0xb0fd08: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb0fd0c: mov             x3, x0
    // 0xb0fd10: ldur            x0, [fp, #-8]
    // 0xb0fd14: stur            x3, [fp, #-0x18]
    // 0xb0fd18: StoreField: r3->field_b = r0
    //     0xb0fd18: stur            w0, [x3, #0xb]
    // 0xb0fd1c: ldur            x2, [fp, #-0x10]
    // 0xb0fd20: r1 = Function '<anonymous closure>':.
    //     0xb0fd20: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c28] AnonymousClosure: (0xb0fdac), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb0e09c)
    //     0xb0fd24: ldr             x1, [x1, #0xc28]
    // 0xb0fd28: r0 = AllocateClosure()
    //     0xb0fd28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0fd2c: mov             x1, x0
    // 0xb0fd30: ldur            x0, [fp, #-0x18]
    // 0xb0fd34: StoreField: r0->field_f = r1
    //     0xb0fd34: stur            w1, [x0, #0xf]
    // 0xb0fd38: r1 = true
    //     0xb0fd38: add             x1, NULL, #0x20  ; true
    // 0xb0fd3c: StoreField: r0->field_43 = r1
    //     0xb0fd3c: stur            w1, [x0, #0x43]
    // 0xb0fd40: r2 = Instance_BoxShape
    //     0xb0fd40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb0fd44: ldr             x2, [x2, #0x80]
    // 0xb0fd48: StoreField: r0->field_47 = r2
    //     0xb0fd48: stur            w2, [x0, #0x47]
    // 0xb0fd4c: StoreField: r0->field_6f = r1
    //     0xb0fd4c: stur            w1, [x0, #0x6f]
    // 0xb0fd50: r2 = false
    //     0xb0fd50: add             x2, NULL, #0x30  ; false
    // 0xb0fd54: StoreField: r0->field_73 = r2
    //     0xb0fd54: stur            w2, [x0, #0x73]
    // 0xb0fd58: StoreField: r0->field_83 = r1
    //     0xb0fd58: stur            w1, [x0, #0x83]
    // 0xb0fd5c: StoreField: r0->field_7b = r2
    //     0xb0fd5c: stur            w2, [x0, #0x7b]
    // 0xb0fd60: r0 = Padding()
    //     0xb0fd60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0fd64: r1 = Instance_EdgeInsets
    //     0xb0fd64: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb0fd68: ldr             x1, [x1, #0x980]
    // 0xb0fd6c: StoreField: r0->field_f = r1
    //     0xb0fd6c: stur            w1, [x0, #0xf]
    // 0xb0fd70: ldur            x1, [fp, #-0x18]
    // 0xb0fd74: StoreField: r0->field_b = r1
    //     0xb0fd74: stur            w1, [x0, #0xb]
    // 0xb0fd78: LeaveFrame
    //     0xb0fd78: mov             SP, fp
    //     0xb0fd7c: ldp             fp, lr, [SP], #0x10
    // 0xb0fd80: ret
    //     0xb0fd80: ret             
    // 0xb0fd84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0fd84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0fd88: b               #0xb0f5fc
    // 0xb0fd8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0fd8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0fd90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0fd90: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0fd94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0fd94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0fd98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0fd98: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0fd9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0fd9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0fda0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0fda0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0fda4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0fda4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0fda8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0fda8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0fdac, size: 0x68
    // 0xb0fdac: EnterFrame
    //     0xb0fdac: stp             fp, lr, [SP, #-0x10]!
    //     0xb0fdb0: mov             fp, SP
    // 0xb0fdb4: AllocStack(0x8)
    //     0xb0fdb4: sub             SP, SP, #8
    // 0xb0fdb8: SetupParameters()
    //     0xb0fdb8: ldr             x0, [fp, #0x10]
    //     0xb0fdbc: ldur            w2, [x0, #0x17]
    //     0xb0fdc0: add             x2, x2, HEAP, lsl #32
    // 0xb0fdc4: CheckStackOverflow
    //     0xb0fdc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0fdc8: cmp             SP, x16
    //     0xb0fdcc: b.ls            #0xb0fe0c
    // 0xb0fdd0: LoadField: r0 = r2->field_b
    //     0xb0fdd0: ldur            w0, [x2, #0xb]
    // 0xb0fdd4: DecompressPointer r0
    //     0xb0fdd4: add             x0, x0, HEAP, lsl #32
    // 0xb0fdd8: LoadField: r3 = r0->field_f
    //     0xb0fdd8: ldur            w3, [x0, #0xf]
    // 0xb0fddc: DecompressPointer r3
    //     0xb0fddc: add             x3, x3, HEAP, lsl #32
    // 0xb0fde0: stur            x3, [fp, #-8]
    // 0xb0fde4: r1 = Function '<anonymous closure>':.
    //     0xb0fde4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c30] AnonymousClosure: (0xa84cc8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb0fde8: ldr             x1, [x1, #0xc30]
    // 0xb0fdec: r0 = AllocateClosure()
    //     0xb0fdec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0fdf0: ldur            x1, [fp, #-8]
    // 0xb0fdf4: mov             x2, x0
    // 0xb0fdf8: r0 = setState()
    //     0xb0fdf8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb0fdfc: r0 = Null
    //     0xb0fdfc: mov             x0, NULL
    // 0xb0fe00: LeaveFrame
    //     0xb0fe00: mov             SP, fp
    //     0xb0fe04: ldp             fp, lr, [SP], #0x10
    // 0xb0fe08: ret
    //     0xb0fe08: ret             
    // 0xb0fe0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0fe0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0fe10: b               #0xb0fdd0
  }
}

// class id: 4139, size: 0x18, field offset: 0xc
//   const constructor, 
class ProductSelectSizeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e034, size: 0x48
    // 0xc7e034: EnterFrame
    //     0xc7e034: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e038: mov             fp, SP
    // 0xc7e03c: AllocStack(0x8)
    //     0xc7e03c: sub             SP, SP, #8
    // 0xc7e040: CheckStackOverflow
    //     0xc7e040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e044: cmp             SP, x16
    //     0xc7e048: b.ls            #0xc7e074
    // 0xc7e04c: r1 = <ProductSelectSizeBottomSheet>
    //     0xc7e04c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b08] TypeArguments: <ProductSelectSizeBottomSheet>
    //     0xc7e050: ldr             x1, [x1, #0xb08]
    // 0xc7e054: r0 = _ProductSelectSizeBottomSheetState()
    //     0xc7e054: bl              #0xc7e07c  ; Allocate_ProductSelectSizeBottomSheetStateStub -> _ProductSelectSizeBottomSheetState (size=0x2c)
    // 0xc7e058: mov             x1, x0
    // 0xc7e05c: stur            x0, [fp, #-8]
    // 0xc7e060: r0 = _ProductSelectSizeBottomSheetState()
    //     0xc7e060: bl              #0xc7c3b4  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::_ProductSelectSizeBottomSheetState
    // 0xc7e064: ldur            x0, [fp, #-8]
    // 0xc7e068: LeaveFrame
    //     0xc7e068: mov             SP, fp
    //     0xc7e06c: ldp             fp, lr, [SP], #0x10
    // 0xc7e070: ret
    //     0xc7e070: ret             
    // 0xc7e074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e074: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e078: b               #0xc7e04c
  }
}
