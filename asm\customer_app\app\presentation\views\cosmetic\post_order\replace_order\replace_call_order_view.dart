// lib: , url: package:customer_app/app/presentation/views/cosmetic/post_order/replace_order/replace_call_order_view.dart

// class id: 1049303, size: 0x8
class :: {
}

// class id: 4591, size: 0x14, field offset: 0x14
//   const constructor, 
class ReplaceCallOrderView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14c5160, size: 0x88
    // 0x14c5160: EnterFrame
    //     0x14c5160: stp             fp, lr, [SP, #-0x10]!
    //     0x14c5164: mov             fp, SP
    // 0x14c5168: AllocStack(0x18)
    //     0x14c5168: sub             SP, SP, #0x18
    // 0x14c516c: SetupParameters(ReplaceCallOrderView this /* r1 => r1, fp-0x8 */)
    //     0x14c516c: stur            x1, [fp, #-8]
    // 0x14c5170: r1 = 1
    //     0x14c5170: movz            x1, #0x1
    // 0x14c5174: r0 = AllocateContext()
    //     0x14c5174: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c5178: mov             x1, x0
    // 0x14c517c: ldur            x0, [fp, #-8]
    // 0x14c5180: stur            x1, [fp, #-0x10]
    // 0x14c5184: StoreField: r1->field_f = r0
    //     0x14c5184: stur            w0, [x1, #0xf]
    // 0x14c5188: r0 = Obx()
    //     0x14c5188: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14c518c: ldur            x2, [fp, #-0x10]
    // 0x14c5190: r1 = Function '<anonymous closure>':.
    //     0x14c5190: add             x1, PP, #0x42, lsl #12  ; [pp+0x42758] AnonymousClosure: (0x14413c0), in [package:customer_app/app/presentation/views/glass/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x14f8384)
    //     0x14c5194: ldr             x1, [x1, #0x758]
    // 0x14c5198: stur            x0, [fp, #-8]
    // 0x14c519c: r0 = AllocateClosure()
    //     0x14c519c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c51a0: mov             x1, x0
    // 0x14c51a4: ldur            x0, [fp, #-8]
    // 0x14c51a8: StoreField: r0->field_b = r1
    //     0x14c51a8: stur            w1, [x0, #0xb]
    // 0x14c51ac: r0 = WillPopScope()
    //     0x14c51ac: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14c51b0: mov             x3, x0
    // 0x14c51b4: ldur            x0, [fp, #-8]
    // 0x14c51b8: stur            x3, [fp, #-0x18]
    // 0x14c51bc: StoreField: r3->field_b = r0
    //     0x14c51bc: stur            w0, [x3, #0xb]
    // 0x14c51c0: ldur            x2, [fp, #-0x10]
    // 0x14c51c4: r1 = Function '<anonymous closure>':.
    //     0x14c51c4: add             x1, PP, #0x42, lsl #12  ; [pp+0x42760] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x14c51c8: ldr             x1, [x1, #0x760]
    // 0x14c51cc: r0 = AllocateClosure()
    //     0x14c51cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c51d0: mov             x1, x0
    // 0x14c51d4: ldur            x0, [fp, #-0x18]
    // 0x14c51d8: StoreField: r0->field_f = r1
    //     0x14c51d8: stur            w1, [x0, #0xf]
    // 0x14c51dc: LeaveFrame
    //     0x14c51dc: mov             SP, fp
    //     0x14c51e0: ldp             fp, lr, [SP], #0x10
    // 0x14c51e4: ret
    //     0x14c51e4: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dd490, size: 0x1bc
    // 0x15dd490: EnterFrame
    //     0x15dd490: stp             fp, lr, [SP, #-0x10]!
    //     0x15dd494: mov             fp, SP
    // 0x15dd498: AllocStack(0x30)
    //     0x15dd498: sub             SP, SP, #0x30
    // 0x15dd49c: SetupParameters(ReplaceCallOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15dd49c: mov             x0, x1
    //     0x15dd4a0: stur            x1, [fp, #-8]
    //     0x15dd4a4: mov             x1, x2
    //     0x15dd4a8: stur            x2, [fp, #-0x10]
    // 0x15dd4ac: CheckStackOverflow
    //     0x15dd4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dd4b0: cmp             SP, x16
    //     0x15dd4b4: b.ls            #0x15dd644
    // 0x15dd4b8: r1 = 1
    //     0x15dd4b8: movz            x1, #0x1
    // 0x15dd4bc: r0 = AllocateContext()
    //     0x15dd4bc: bl              #0x16f6108  ; AllocateContextStub
    // 0x15dd4c0: mov             x2, x0
    // 0x15dd4c4: ldur            x0, [fp, #-8]
    // 0x15dd4c8: stur            x2, [fp, #-0x18]
    // 0x15dd4cc: StoreField: r2->field_f = r0
    //     0x15dd4cc: stur            w0, [x2, #0xf]
    // 0x15dd4d0: ldur            x1, [fp, #-0x10]
    // 0x15dd4d4: r0 = of()
    //     0x15dd4d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dd4d8: LoadField: r1 = r0->field_87
    //     0x15dd4d8: ldur            w1, [x0, #0x87]
    // 0x15dd4dc: DecompressPointer r1
    //     0x15dd4dc: add             x1, x1, HEAP, lsl #32
    // 0x15dd4e0: LoadField: r0 = r1->field_2b
    //     0x15dd4e0: ldur            w0, [x1, #0x2b]
    // 0x15dd4e4: DecompressPointer r0
    //     0x15dd4e4: add             x0, x0, HEAP, lsl #32
    // 0x15dd4e8: r16 = 16.000000
    //     0x15dd4e8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15dd4ec: ldr             x16, [x16, #0x188]
    // 0x15dd4f0: r30 = Instance_Color
    //     0x15dd4f0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15dd4f4: stp             lr, x16, [SP]
    // 0x15dd4f8: mov             x1, x0
    // 0x15dd4fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15dd4fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15dd500: ldr             x4, [x4, #0xaa0]
    // 0x15dd504: r0 = copyWith()
    //     0x15dd504: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15dd508: stur            x0, [fp, #-8]
    // 0x15dd50c: r0 = Text()
    //     0x15dd50c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15dd510: mov             x2, x0
    // 0x15dd514: r0 = "Return/Replace Order"
    //     0x15dd514: add             x0, PP, #0x34, lsl #12  ; [pp+0x34078] "Return/Replace Order"
    //     0x15dd518: ldr             x0, [x0, #0x78]
    // 0x15dd51c: stur            x2, [fp, #-0x20]
    // 0x15dd520: StoreField: r2->field_b = r0
    //     0x15dd520: stur            w0, [x2, #0xb]
    // 0x15dd524: ldur            x0, [fp, #-8]
    // 0x15dd528: StoreField: r2->field_13 = r0
    //     0x15dd528: stur            w0, [x2, #0x13]
    // 0x15dd52c: ldur            x1, [fp, #-0x10]
    // 0x15dd530: r0 = of()
    //     0x15dd530: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dd534: LoadField: r1 = r0->field_5b
    //     0x15dd534: ldur            w1, [x0, #0x5b]
    // 0x15dd538: DecompressPointer r1
    //     0x15dd538: add             x1, x1, HEAP, lsl #32
    // 0x15dd53c: stur            x1, [fp, #-8]
    // 0x15dd540: r0 = ColorFilter()
    //     0x15dd540: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dd544: mov             x1, x0
    // 0x15dd548: ldur            x0, [fp, #-8]
    // 0x15dd54c: stur            x1, [fp, #-0x10]
    // 0x15dd550: StoreField: r1->field_7 = r0
    //     0x15dd550: stur            w0, [x1, #7]
    // 0x15dd554: r0 = Instance_BlendMode
    //     0x15dd554: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dd558: ldr             x0, [x0, #0xb30]
    // 0x15dd55c: StoreField: r1->field_b = r0
    //     0x15dd55c: stur            w0, [x1, #0xb]
    // 0x15dd560: r0 = 1
    //     0x15dd560: movz            x0, #0x1
    // 0x15dd564: StoreField: r1->field_13 = r0
    //     0x15dd564: stur            x0, [x1, #0x13]
    // 0x15dd568: r0 = SvgPicture()
    //     0x15dd568: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dd56c: stur            x0, [fp, #-8]
    // 0x15dd570: ldur            x16, [fp, #-0x10]
    // 0x15dd574: str             x16, [SP]
    // 0x15dd578: mov             x1, x0
    // 0x15dd57c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15dd57c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15dd580: ldr             x2, [x2, #0xa40]
    // 0x15dd584: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dd584: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dd588: ldr             x4, [x4, #0xa38]
    // 0x15dd58c: r0 = SvgPicture.asset()
    //     0x15dd58c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dd590: r0 = Align()
    //     0x15dd590: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dd594: mov             x1, x0
    // 0x15dd598: r0 = Instance_Alignment
    //     0x15dd598: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dd59c: ldr             x0, [x0, #0xb10]
    // 0x15dd5a0: stur            x1, [fp, #-0x10]
    // 0x15dd5a4: StoreField: r1->field_f = r0
    //     0x15dd5a4: stur            w0, [x1, #0xf]
    // 0x15dd5a8: r0 = 1.000000
    //     0x15dd5a8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dd5ac: StoreField: r1->field_13 = r0
    //     0x15dd5ac: stur            w0, [x1, #0x13]
    // 0x15dd5b0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dd5b0: stur            w0, [x1, #0x17]
    // 0x15dd5b4: ldur            x0, [fp, #-8]
    // 0x15dd5b8: StoreField: r1->field_b = r0
    //     0x15dd5b8: stur            w0, [x1, #0xb]
    // 0x15dd5bc: r0 = InkWell()
    //     0x15dd5bc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dd5c0: mov             x3, x0
    // 0x15dd5c4: ldur            x0, [fp, #-0x10]
    // 0x15dd5c8: stur            x3, [fp, #-8]
    // 0x15dd5cc: StoreField: r3->field_b = r0
    //     0x15dd5cc: stur            w0, [x3, #0xb]
    // 0x15dd5d0: ldur            x2, [fp, #-0x18]
    // 0x15dd5d4: r1 = Function '<anonymous closure>':.
    //     0x15dd5d4: add             x1, PP, #0x42, lsl #12  ; [pp+0x42768] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15dd5d8: ldr             x1, [x1, #0x768]
    // 0x15dd5dc: r0 = AllocateClosure()
    //     0x15dd5dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dd5e0: ldur            x2, [fp, #-8]
    // 0x15dd5e4: StoreField: r2->field_f = r0
    //     0x15dd5e4: stur            w0, [x2, #0xf]
    // 0x15dd5e8: r0 = true
    //     0x15dd5e8: add             x0, NULL, #0x20  ; true
    // 0x15dd5ec: StoreField: r2->field_43 = r0
    //     0x15dd5ec: stur            w0, [x2, #0x43]
    // 0x15dd5f0: r1 = Instance_BoxShape
    //     0x15dd5f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dd5f4: ldr             x1, [x1, #0x80]
    // 0x15dd5f8: StoreField: r2->field_47 = r1
    //     0x15dd5f8: stur            w1, [x2, #0x47]
    // 0x15dd5fc: StoreField: r2->field_6f = r0
    //     0x15dd5fc: stur            w0, [x2, #0x6f]
    // 0x15dd600: r1 = false
    //     0x15dd600: add             x1, NULL, #0x30  ; false
    // 0x15dd604: StoreField: r2->field_73 = r1
    //     0x15dd604: stur            w1, [x2, #0x73]
    // 0x15dd608: StoreField: r2->field_83 = r0
    //     0x15dd608: stur            w0, [x2, #0x83]
    // 0x15dd60c: StoreField: r2->field_7b = r1
    //     0x15dd60c: stur            w1, [x2, #0x7b]
    // 0x15dd610: r0 = AppBar()
    //     0x15dd610: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dd614: stur            x0, [fp, #-0x10]
    // 0x15dd618: ldur            x16, [fp, #-0x20]
    // 0x15dd61c: str             x16, [SP]
    // 0x15dd620: mov             x1, x0
    // 0x15dd624: ldur            x2, [fp, #-8]
    // 0x15dd628: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15dd628: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15dd62c: ldr             x4, [x4, #0xf00]
    // 0x15dd630: r0 = AppBar()
    //     0x15dd630: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dd634: ldur            x0, [fp, #-0x10]
    // 0x15dd638: LeaveFrame
    //     0x15dd638: mov             SP, fp
    //     0x15dd63c: ldp             fp, lr, [SP], #0x10
    // 0x15dd640: ret
    //     0x15dd640: ret             
    // 0x15dd644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dd644: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dd648: b               #0x15dd4b8
  }
}
