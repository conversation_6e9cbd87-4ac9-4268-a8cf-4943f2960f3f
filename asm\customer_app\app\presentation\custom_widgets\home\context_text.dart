// lib: , url: package:customer_app/app/presentation/custom_widgets/home/<USER>

// class id: 1049066, size: 0x8
class :: {
}

// class id: 3592, size: 0x14, field offset: 0x14
class _ContentTextState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x98e21c, size: 0x1f4
    // 0x98e21c: EnterFrame
    //     0x98e21c: stp             fp, lr, [SP, #-0x10]!
    //     0x98e220: mov             fp, SP
    // 0x98e224: AllocStack(0x30)
    //     0x98e224: sub             SP, SP, #0x30
    // 0x98e228: SetupParameters(_ContentTextState this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x98e228: mov             x0, x1
    //     0x98e22c: mov             x1, x2
    // 0x98e230: CheckStackOverflow
    //     0x98e230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98e234: cmp             SP, x16
    //     0x98e238: b.ls            #0x98e404
    // 0x98e23c: LoadField: r2 = r0->field_b
    //     0x98e23c: ldur            w2, [x0, #0xb]
    // 0x98e240: DecompressPointer r2
    //     0x98e240: add             x2, x2, HEAP, lsl #32
    // 0x98e244: cmp             w2, NULL
    // 0x98e248: b.eq            #0x98e40c
    // 0x98e24c: LoadField: r0 = r2->field_f
    //     0x98e24c: ldur            w0, [x2, #0xf]
    // 0x98e250: DecompressPointer r0
    //     0x98e250: add             x0, x0, HEAP, lsl #32
    // 0x98e254: cmp             w0, NULL
    // 0x98e258: b.ne            #0x98e264
    // 0x98e25c: r0 = Null
    //     0x98e25c: mov             x0, NULL
    // 0x98e260: b               #0x98e270
    // 0x98e264: LoadField: r3 = r0->field_7
    //     0x98e264: ldur            w3, [x0, #7]
    // 0x98e268: DecompressPointer r3
    //     0x98e268: add             x3, x3, HEAP, lsl #32
    // 0x98e26c: mov             x0, x3
    // 0x98e270: cmp             w0, NULL
    // 0x98e274: b.ne            #0x98e280
    // 0x98e278: r0 = Instance_TitleAlignment
    //     0x98e278: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x98e27c: ldr             x0, [x0, #0x518]
    // 0x98e280: r16 = Instance_TitleAlignment
    //     0x98e280: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x98e284: ldr             x16, [x16, #0x520]
    // 0x98e288: cmp             w0, w16
    // 0x98e28c: b.ne            #0x98e29c
    // 0x98e290: r0 = Instance_CrossAxisAlignment
    //     0x98e290: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x98e294: ldr             x0, [x0, #0xc68]
    // 0x98e298: b               #0x98e2c0
    // 0x98e29c: r16 = Instance_TitleAlignment
    //     0x98e29c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x98e2a0: ldr             x16, [x16, #0x518]
    // 0x98e2a4: cmp             w0, w16
    // 0x98e2a8: b.ne            #0x98e2b8
    // 0x98e2ac: r0 = Instance_CrossAxisAlignment
    //     0x98e2ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x98e2b0: ldr             x0, [x0, #0x890]
    // 0x98e2b4: b               #0x98e2c0
    // 0x98e2b8: r0 = Instance_CrossAxisAlignment
    //     0x98e2b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98e2bc: ldr             x0, [x0, #0xa18]
    // 0x98e2c0: stur            x0, [fp, #-0x10]
    // 0x98e2c4: LoadField: r3 = r2->field_b
    //     0x98e2c4: ldur            w3, [x2, #0xb]
    // 0x98e2c8: DecompressPointer r3
    //     0x98e2c8: add             x3, x3, HEAP, lsl #32
    // 0x98e2cc: stur            x3, [fp, #-8]
    // 0x98e2d0: r0 = of()
    //     0x98e2d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98e2d4: LoadField: r1 = r0->field_87
    //     0x98e2d4: ldur            w1, [x0, #0x87]
    // 0x98e2d8: DecompressPointer r1
    //     0x98e2d8: add             x1, x1, HEAP, lsl #32
    // 0x98e2dc: LoadField: r0 = r1->field_2b
    //     0x98e2dc: ldur            w0, [x1, #0x2b]
    // 0x98e2e0: DecompressPointer r0
    //     0x98e2e0: add             x0, x0, HEAP, lsl #32
    // 0x98e2e4: stur            x0, [fp, #-0x18]
    // 0x98e2e8: r1 = Instance_Color
    //     0x98e2e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x98e2ec: d0 = 0.700000
    //     0x98e2ec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98e2f0: ldr             d0, [x17, #0xf48]
    // 0x98e2f4: r0 = withOpacity()
    //     0x98e2f4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x98e2f8: r16 = 14.000000
    //     0x98e2f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x98e2fc: ldr             x16, [x16, #0x1d8]
    // 0x98e300: stp             x16, x0, [SP]
    // 0x98e304: ldur            x1, [fp, #-0x18]
    // 0x98e308: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x98e308: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x98e30c: ldr             x4, [x4, #0x9b8]
    // 0x98e310: r0 = copyWith()
    //     0x98e310: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x98e314: stur            x0, [fp, #-0x18]
    // 0x98e318: r0 = HtmlWidget()
    //     0x98e318: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0x98e31c: mov             x3, x0
    // 0x98e320: ldur            x0, [fp, #-8]
    // 0x98e324: stur            x3, [fp, #-0x20]
    // 0x98e328: StoreField: r3->field_1f = r0
    //     0x98e328: stur            w0, [x3, #0x1f]
    // 0x98e32c: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0x98e32c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0x98e330: ldr             x0, [x0, #0x1e0]
    // 0x98e334: StoreField: r3->field_23 = r0
    //     0x98e334: stur            w0, [x3, #0x23]
    // 0x98e338: r0 = Instance_ColumnMode
    //     0x98e338: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0x98e33c: ldr             x0, [x0, #0x1e8]
    // 0x98e340: StoreField: r3->field_3b = r0
    //     0x98e340: stur            w0, [x3, #0x3b]
    // 0x98e344: ldur            x0, [fp, #-0x18]
    // 0x98e348: StoreField: r3->field_3f = r0
    //     0x98e348: stur            w0, [x3, #0x3f]
    // 0x98e34c: r1 = Null
    //     0x98e34c: mov             x1, NULL
    // 0x98e350: r2 = 2
    //     0x98e350: movz            x2, #0x2
    // 0x98e354: r0 = AllocateArray()
    //     0x98e354: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98e358: mov             x2, x0
    // 0x98e35c: ldur            x0, [fp, #-0x20]
    // 0x98e360: stur            x2, [fp, #-8]
    // 0x98e364: StoreField: r2->field_f = r0
    //     0x98e364: stur            w0, [x2, #0xf]
    // 0x98e368: r1 = <Widget>
    //     0x98e368: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x98e36c: r0 = AllocateGrowableArray()
    //     0x98e36c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98e370: mov             x1, x0
    // 0x98e374: ldur            x0, [fp, #-8]
    // 0x98e378: stur            x1, [fp, #-0x18]
    // 0x98e37c: StoreField: r1->field_f = r0
    //     0x98e37c: stur            w0, [x1, #0xf]
    // 0x98e380: r0 = 2
    //     0x98e380: movz            x0, #0x2
    // 0x98e384: StoreField: r1->field_b = r0
    //     0x98e384: stur            w0, [x1, #0xb]
    // 0x98e388: r0 = Column()
    //     0x98e388: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x98e38c: mov             x1, x0
    // 0x98e390: r0 = Instance_Axis
    //     0x98e390: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x98e394: stur            x1, [fp, #-8]
    // 0x98e398: StoreField: r1->field_f = r0
    //     0x98e398: stur            w0, [x1, #0xf]
    // 0x98e39c: r0 = Instance_MainAxisAlignment
    //     0x98e39c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x98e3a0: ldr             x0, [x0, #0xa08]
    // 0x98e3a4: StoreField: r1->field_13 = r0
    //     0x98e3a4: stur            w0, [x1, #0x13]
    // 0x98e3a8: r0 = Instance_MainAxisSize
    //     0x98e3a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98e3ac: ldr             x0, [x0, #0xa10]
    // 0x98e3b0: ArrayStore: r1[0] = r0  ; List_4
    //     0x98e3b0: stur            w0, [x1, #0x17]
    // 0x98e3b4: ldur            x0, [fp, #-0x10]
    // 0x98e3b8: StoreField: r1->field_1b = r0
    //     0x98e3b8: stur            w0, [x1, #0x1b]
    // 0x98e3bc: r0 = Instance_VerticalDirection
    //     0x98e3bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98e3c0: ldr             x0, [x0, #0xa20]
    // 0x98e3c4: StoreField: r1->field_23 = r0
    //     0x98e3c4: stur            w0, [x1, #0x23]
    // 0x98e3c8: r0 = Instance_Clip
    //     0x98e3c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98e3cc: ldr             x0, [x0, #0x38]
    // 0x98e3d0: StoreField: r1->field_2b = r0
    //     0x98e3d0: stur            w0, [x1, #0x2b]
    // 0x98e3d4: StoreField: r1->field_2f = rZR
    //     0x98e3d4: stur            xzr, [x1, #0x2f]
    // 0x98e3d8: ldur            x0, [fp, #-0x18]
    // 0x98e3dc: StoreField: r1->field_b = r0
    //     0x98e3dc: stur            w0, [x1, #0xb]
    // 0x98e3e0: r0 = Padding()
    //     0x98e3e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x98e3e4: r1 = Instance_EdgeInsets
    //     0x98e3e4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40de8] Obj!EdgeInsets@d57351
    //     0x98e3e8: ldr             x1, [x1, #0xde8]
    // 0x98e3ec: StoreField: r0->field_f = r1
    //     0x98e3ec: stur            w1, [x0, #0xf]
    // 0x98e3f0: ldur            x1, [fp, #-8]
    // 0x98e3f4: StoreField: r0->field_b = r1
    //     0x98e3f4: stur            w1, [x0, #0xb]
    // 0x98e3f8: LeaveFrame
    //     0x98e3f8: mov             SP, fp
    //     0x98e3fc: ldp             fp, lr, [SP], #0x10
    // 0x98e400: ret
    //     0x98e400: ret             
    // 0x98e404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98e404: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98e408: b               #0x98e23c
    // 0x98e40c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98e40c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4319, size: 0x14, field offset: 0xc
//   const constructor, 
class ContentText extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7994c, size: 0x24
    // 0xc7994c: EnterFrame
    //     0xc7994c: stp             fp, lr, [SP, #-0x10]!
    //     0xc79950: mov             fp, SP
    // 0xc79954: mov             x0, x1
    // 0xc79958: r1 = <ContentText>
    //     0xc79958: add             x1, PP, #0x49, lsl #12  ; [pp+0x49388] TypeArguments: <ContentText>
    //     0xc7995c: ldr             x1, [x1, #0x388]
    // 0xc79960: r0 = _ContentTextState()
    //     0xc79960: bl              #0xc79970  ; Allocate_ContentTextStateStub -> _ContentTextState (size=0x14)
    // 0xc79964: LeaveFrame
    //     0xc79964: mov             SP, fp
    //     0xc79968: ldp             fp, lr, [SP], #0x10
    // 0xc7996c: ret
    //     0xc7996c: ret             
  }
}
