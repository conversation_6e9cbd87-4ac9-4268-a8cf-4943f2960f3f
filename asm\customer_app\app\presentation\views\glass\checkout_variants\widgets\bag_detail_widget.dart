// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart

// class id: 1049359, size: 0x8
class :: {
}

// class id: 3373, size: 0x14, field offset: 0x14
class _BagDetailWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93de28, size: 0x130
    // 0x93de28: EnterFrame
    //     0x93de28: stp             fp, lr, [SP, #-0x10]!
    //     0x93de2c: mov             fp, SP
    // 0x93de30: AllocStack(0x18)
    //     0x93de30: sub             SP, SP, #0x18
    // 0x93de34: SetupParameters(_BagDetailWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x93de34: stur            x1, [fp, #-8]
    // 0x93de38: CheckStackOverflow
    //     0x93de38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93de3c: cmp             SP, x16
    //     0x93de40: b.ls            #0x93df4c
    // 0x93de44: r1 = 1
    //     0x93de44: movz            x1, #0x1
    // 0x93de48: r0 = AllocateContext()
    //     0x93de48: bl              #0x16f6108  ; AllocateContextStub
    // 0x93de4c: mov             x1, x0
    // 0x93de50: ldur            x0, [fp, #-8]
    // 0x93de54: StoreField: r1->field_f = r0
    //     0x93de54: stur            w0, [x1, #0xf]
    // 0x93de58: r0 = LoadStaticField(0x878)
    //     0x93de58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93de5c: ldr             x0, [x0, #0x10f0]
    // 0x93de60: cmp             w0, NULL
    // 0x93de64: b.eq            #0x93df54
    // 0x93de68: LoadField: r3 = r0->field_53
    //     0x93de68: ldur            w3, [x0, #0x53]
    // 0x93de6c: DecompressPointer r3
    //     0x93de6c: add             x3, x3, HEAP, lsl #32
    // 0x93de70: stur            x3, [fp, #-0x10]
    // 0x93de74: LoadField: r0 = r3->field_7
    //     0x93de74: ldur            w0, [x3, #7]
    // 0x93de78: DecompressPointer r0
    //     0x93de78: add             x0, x0, HEAP, lsl #32
    // 0x93de7c: mov             x2, x1
    // 0x93de80: stur            x0, [fp, #-8]
    // 0x93de84: r1 = Function '<anonymous closure>':.
    //     0x93de84: add             x1, PP, #0x57, lsl #12  ; [pp+0x57150] AnonymousClosure: (0x902d60), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::initState (0x945948)
    //     0x93de88: ldr             x1, [x1, #0x150]
    // 0x93de8c: r0 = AllocateClosure()
    //     0x93de8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93de90: ldur            x2, [fp, #-8]
    // 0x93de94: mov             x3, x0
    // 0x93de98: r1 = Null
    //     0x93de98: mov             x1, NULL
    // 0x93de9c: stur            x3, [fp, #-8]
    // 0x93dea0: cmp             w2, NULL
    // 0x93dea4: b.eq            #0x93dec4
    // 0x93dea8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93dea8: ldur            w4, [x2, #0x17]
    // 0x93deac: DecompressPointer r4
    //     0x93deac: add             x4, x4, HEAP, lsl #32
    // 0x93deb0: r8 = X0
    //     0x93deb0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93deb4: LoadField: r9 = r4->field_7
    //     0x93deb4: ldur            x9, [x4, #7]
    // 0x93deb8: r3 = Null
    //     0x93deb8: add             x3, PP, #0x57, lsl #12  ; [pp+0x57158] Null
    //     0x93debc: ldr             x3, [x3, #0x158]
    // 0x93dec0: blr             x9
    // 0x93dec4: ldur            x0, [fp, #-0x10]
    // 0x93dec8: LoadField: r1 = r0->field_b
    //     0x93dec8: ldur            w1, [x0, #0xb]
    // 0x93decc: LoadField: r2 = r0->field_f
    //     0x93decc: ldur            w2, [x0, #0xf]
    // 0x93ded0: DecompressPointer r2
    //     0x93ded0: add             x2, x2, HEAP, lsl #32
    // 0x93ded4: LoadField: r3 = r2->field_b
    //     0x93ded4: ldur            w3, [x2, #0xb]
    // 0x93ded8: r2 = LoadInt32Instr(r1)
    //     0x93ded8: sbfx            x2, x1, #1, #0x1f
    // 0x93dedc: stur            x2, [fp, #-0x18]
    // 0x93dee0: r1 = LoadInt32Instr(r3)
    //     0x93dee0: sbfx            x1, x3, #1, #0x1f
    // 0x93dee4: cmp             x2, x1
    // 0x93dee8: b.ne            #0x93def4
    // 0x93deec: mov             x1, x0
    // 0x93def0: r0 = _growToNextCapacity()
    //     0x93def0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93def4: ldur            x2, [fp, #-0x10]
    // 0x93def8: ldur            x3, [fp, #-0x18]
    // 0x93defc: add             x4, x3, #1
    // 0x93df00: lsl             x5, x4, #1
    // 0x93df04: StoreField: r2->field_b = r5
    //     0x93df04: stur            w5, [x2, #0xb]
    // 0x93df08: LoadField: r1 = r2->field_f
    //     0x93df08: ldur            w1, [x2, #0xf]
    // 0x93df0c: DecompressPointer r1
    //     0x93df0c: add             x1, x1, HEAP, lsl #32
    // 0x93df10: ldur            x0, [fp, #-8]
    // 0x93df14: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93df14: add             x25, x1, x3, lsl #2
    //     0x93df18: add             x25, x25, #0xf
    //     0x93df1c: str             w0, [x25]
    //     0x93df20: tbz             w0, #0, #0x93df3c
    //     0x93df24: ldurb           w16, [x1, #-1]
    //     0x93df28: ldurb           w17, [x0, #-1]
    //     0x93df2c: and             x16, x17, x16, lsr #2
    //     0x93df30: tst             x16, HEAP, lsr #32
    //     0x93df34: b.eq            #0x93df3c
    //     0x93df38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93df3c: r0 = Null
    //     0x93df3c: mov             x0, NULL
    // 0x93df40: LeaveFrame
    //     0x93df40: mov             SP, fp
    //     0x93df44: ldp             fp, lr, [SP], #0x10
    // 0x93df48: ret
    //     0x93df48: ret             
    // 0x93df4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93df4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93df50: b               #0x93de44
    // 0x93df54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93df54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb39dd0, size: 0x1634
    // 0xb39dd0: EnterFrame
    //     0xb39dd0: stp             fp, lr, [SP, #-0x10]!
    //     0xb39dd4: mov             fp, SP
    // 0xb39dd8: AllocStack(0x98)
    //     0xb39dd8: sub             SP, SP, #0x98
    // 0xb39ddc: SetupParameters(_BagDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb39ddc: mov             x0, x1
    //     0xb39de0: stur            x1, [fp, #-8]
    //     0xb39de4: mov             x1, x2
    //     0xb39de8: stur            x2, [fp, #-0x10]
    // 0xb39dec: CheckStackOverflow
    //     0xb39dec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb39df0: cmp             SP, x16
    //     0xb39df4: b.ls            #0xb3b3dc
    // 0xb39df8: r1 = 1
    //     0xb39df8: movz            x1, #0x1
    // 0xb39dfc: r0 = AllocateContext()
    //     0xb39dfc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb39e00: mov             x2, x0
    // 0xb39e04: ldur            x0, [fp, #-8]
    // 0xb39e08: stur            x2, [fp, #-0x18]
    // 0xb39e0c: StoreField: r2->field_f = r0
    //     0xb39e0c: stur            w0, [x2, #0xf]
    // 0xb39e10: ldur            x1, [fp, #-0x10]
    // 0xb39e14: r0 = of()
    //     0xb39e14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb39e18: LoadField: r1 = r0->field_87
    //     0xb39e18: ldur            w1, [x0, #0x87]
    // 0xb39e1c: DecompressPointer r1
    //     0xb39e1c: add             x1, x1, HEAP, lsl #32
    // 0xb39e20: LoadField: r0 = r1->field_7
    //     0xb39e20: ldur            w0, [x1, #7]
    // 0xb39e24: DecompressPointer r0
    //     0xb39e24: add             x0, x0, HEAP, lsl #32
    // 0xb39e28: stur            x0, [fp, #-0x20]
    // 0xb39e2c: r1 = Instance_Color
    //     0xb39e2c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb39e30: d0 = 0.700000
    //     0xb39e30: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb39e34: ldr             d0, [x17, #0xf48]
    // 0xb39e38: r0 = withOpacity()
    //     0xb39e38: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb39e3c: r16 = 14.000000
    //     0xb39e3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb39e40: ldr             x16, [x16, #0x1d8]
    // 0xb39e44: stp             x0, x16, [SP]
    // 0xb39e48: ldur            x1, [fp, #-0x20]
    // 0xb39e4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb39e4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb39e50: ldr             x4, [x4, #0xaa0]
    // 0xb39e54: r0 = copyWith()
    //     0xb39e54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb39e58: stur            x0, [fp, #-0x20]
    // 0xb39e5c: r0 = Text()
    //     0xb39e5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb39e60: mov             x1, x0
    // 0xb39e64: r0 = "Bag"
    //     0xb39e64: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0xb39e68: ldr             x0, [x0, #0xd60]
    // 0xb39e6c: stur            x1, [fp, #-0x28]
    // 0xb39e70: StoreField: r1->field_b = r0
    //     0xb39e70: stur            w0, [x1, #0xb]
    // 0xb39e74: ldur            x0, [fp, #-0x20]
    // 0xb39e78: StoreField: r1->field_13 = r0
    //     0xb39e78: stur            w0, [x1, #0x13]
    // 0xb39e7c: r0 = Padding()
    //     0xb39e7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb39e80: mov             x1, x0
    // 0xb39e84: r0 = Instance_EdgeInsets
    //     0xb39e84: add             x0, PP, #0x40, lsl #12  ; [pp+0x408a8] Obj!EdgeInsets@d57d71
    //     0xb39e88: ldr             x0, [x0, #0x8a8]
    // 0xb39e8c: stur            x1, [fp, #-0x30]
    // 0xb39e90: StoreField: r1->field_f = r0
    //     0xb39e90: stur            w0, [x1, #0xf]
    // 0xb39e94: ldur            x0, [fp, #-0x28]
    // 0xb39e98: StoreField: r1->field_b = r0
    //     0xb39e98: stur            w0, [x1, #0xb]
    // 0xb39e9c: ldur            x0, [fp, #-8]
    // 0xb39ea0: LoadField: r2 = r0->field_b
    //     0xb39ea0: ldur            w2, [x0, #0xb]
    // 0xb39ea4: DecompressPointer r2
    //     0xb39ea4: add             x2, x2, HEAP, lsl #32
    // 0xb39ea8: cmp             w2, NULL
    // 0xb39eac: b.eq            #0xb3b3e4
    // 0xb39eb0: LoadField: r3 = r2->field_b
    //     0xb39eb0: ldur            w3, [x2, #0xb]
    // 0xb39eb4: DecompressPointer r3
    //     0xb39eb4: add             x3, x3, HEAP, lsl #32
    // 0xb39eb8: LoadField: r2 = r3->field_b
    //     0xb39eb8: ldur            w2, [x3, #0xb]
    // 0xb39ebc: DecompressPointer r2
    //     0xb39ebc: add             x2, x2, HEAP, lsl #32
    // 0xb39ec0: cmp             w2, NULL
    // 0xb39ec4: b.ne            #0xb39ed0
    // 0xb39ec8: r3 = Null
    //     0xb39ec8: mov             x3, NULL
    // 0xb39ecc: b               #0xb39ed8
    // 0xb39ed0: LoadField: r3 = r2->field_43
    //     0xb39ed0: ldur            w3, [x2, #0x43]
    // 0xb39ed4: DecompressPointer r3
    //     0xb39ed4: add             x3, x3, HEAP, lsl #32
    // 0xb39ed8: cmp             w3, NULL
    // 0xb39edc: r16 = true
    //     0xb39edc: add             x16, NULL, #0x20  ; true
    // 0xb39ee0: r17 = false
    //     0xb39ee0: add             x17, NULL, #0x30  ; false
    // 0xb39ee4: csel            x4, x16, x17, ne
    // 0xb39ee8: stur            x4, [fp, #-0x20]
    // 0xb39eec: cmp             w2, NULL
    // 0xb39ef0: b.ne            #0xb39efc
    // 0xb39ef4: r2 = Null
    //     0xb39ef4: mov             x2, NULL
    // 0xb39ef8: b               #0xb39f1c
    // 0xb39efc: LoadField: r3 = r2->field_43
    //     0xb39efc: ldur            w3, [x2, #0x43]
    // 0xb39f00: DecompressPointer r3
    //     0xb39f00: add             x3, x3, HEAP, lsl #32
    // 0xb39f04: cmp             w3, NULL
    // 0xb39f08: b.ne            #0xb39f14
    // 0xb39f0c: r2 = Null
    //     0xb39f0c: mov             x2, NULL
    // 0xb39f10: b               #0xb39f1c
    // 0xb39f14: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb39f14: ldur            w2, [x3, #0x17]
    // 0xb39f18: DecompressPointer r2
    //     0xb39f18: add             x2, x2, HEAP, lsl #32
    // 0xb39f1c: cmp             w2, NULL
    // 0xb39f20: b.ne            #0xb39fa0
    // 0xb39f24: mov             x23, x0
    // 0xb39f28: r10 = true
    //     0xb39f28: add             x10, NULL, #0x20  ; true
    // 0xb39f2c: r3 = 2
    //     0xb39f2c: movz            x3, #0x2
    // 0xb39f30: r5 = "Free"
    //     0xb39f30: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb39f34: ldr             x5, [x5, #0x668]
    // 0xb39f38: r4 = 150.000000
    //     0xb39f38: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb39f3c: ldr             x4, [x4, #0x690]
    // 0xb39f40: r2 = Instance_TextOverflow
    //     0xb39f40: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb39f44: ldr             x2, [x2, #0xe10]
    // 0xb39f48: r6 = Instance_EdgeInsets
    //     0xb39f48: add             x6, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb39f4c: ldr             x6, [x6, #0xa78]
    // 0xb39f50: r7 = Instance_CrossAxisAlignment
    //     0xb39f50: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb39f54: ldr             x7, [x7, #0xa18]
    // 0xb39f58: r12 = Instance_EdgeInsets
    //     0xb39f58: add             x12, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xb39f5c: ldr             x12, [x12, #0xa40]
    // 0xb39f60: r19 = Instance_MainAxisAlignment
    //     0xb39f60: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb39f64: ldr             x19, [x19, #0xa8]
    // 0xb39f68: r20 = Instance_EdgeInsets
    //     0xb39f68: add             x20, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb39f6c: ldr             x20, [x20, #0x778]
    // 0xb39f70: r13 = 4
    //     0xb39f70: movz            x13, #0x4
    // 0xb39f74: r14 = Instance_Axis
    //     0xb39f74: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb39f78: r9 = Instance_FlexFit
    //     0xb39f78: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb39f7c: ldr             x9, [x9, #0xe08]
    // 0xb39f80: r11 = Instance_BoxShape
    //     0xb39f80: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb39f84: ldr             x11, [x11, #0x80]
    // 0xb39f88: r0 = Instance_Alignment
    //     0xb39f88: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb39f8c: ldr             x0, [x0, #0xb10]
    // 0xb39f90: r1 = -1
    //     0xb39f90: movn            x1, #0
    // 0xb39f94: d0 = 12.000000
    //     0xb39f94: fmov            d0, #12.00000000
    // 0xb39f98: r8 = 1
    //     0xb39f98: movz            x8, #0x1
    // 0xb39f9c: b               #0xb3a8b0
    // 0xb39fa0: tbnz            w2, #4, #0xb3a838
    // 0xb39fa4: r0 = Radius()
    //     0xb39fa4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb39fa8: d0 = 12.000000
    //     0xb39fa8: fmov            d0, #12.00000000
    // 0xb39fac: stur            x0, [fp, #-0x28]
    // 0xb39fb0: StoreField: r0->field_7 = d0
    //     0xb39fb0: stur            d0, [x0, #7]
    // 0xb39fb4: StoreField: r0->field_f = d0
    //     0xb39fb4: stur            d0, [x0, #0xf]
    // 0xb39fb8: r0 = BorderRadius()
    //     0xb39fb8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb39fbc: mov             x2, x0
    // 0xb39fc0: ldur            x0, [fp, #-0x28]
    // 0xb39fc4: stur            x2, [fp, #-0x38]
    // 0xb39fc8: StoreField: r2->field_7 = r0
    //     0xb39fc8: stur            w0, [x2, #7]
    // 0xb39fcc: StoreField: r2->field_b = r0
    //     0xb39fcc: stur            w0, [x2, #0xb]
    // 0xb39fd0: StoreField: r2->field_f = r0
    //     0xb39fd0: stur            w0, [x2, #0xf]
    // 0xb39fd4: StoreField: r2->field_13 = r0
    //     0xb39fd4: stur            w0, [x2, #0x13]
    // 0xb39fd8: r1 = Instance_Color
    //     0xb39fd8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb39fdc: d0 = 0.070000
    //     0xb39fdc: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb39fe0: ldr             d0, [x17, #0x5f8]
    // 0xb39fe4: r0 = withOpacity()
    //     0xb39fe4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb39fe8: r16 = 1.000000
    //     0xb39fe8: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb39fec: str             x16, [SP]
    // 0xb39ff0: mov             x2, x0
    // 0xb39ff4: r1 = Null
    //     0xb39ff4: mov             x1, NULL
    // 0xb39ff8: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb39ff8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb39ffc: ldr             x4, [x4, #0x108]
    // 0xb3a000: r0 = Border.all()
    //     0xb3a000: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb3a004: stur            x0, [fp, #-0x28]
    // 0xb3a008: r0 = BoxDecoration()
    //     0xb3a008: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb3a00c: mov             x2, x0
    // 0xb3a010: ldur            x0, [fp, #-0x28]
    // 0xb3a014: stur            x2, [fp, #-0x40]
    // 0xb3a018: StoreField: r2->field_f = r0
    //     0xb3a018: stur            w0, [x2, #0xf]
    // 0xb3a01c: ldur            x0, [fp, #-0x38]
    // 0xb3a020: StoreField: r2->field_13 = r0
    //     0xb3a020: stur            w0, [x2, #0x13]
    // 0xb3a024: r0 = Instance_LinearGradient
    //     0xb3a024: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xb3a028: ldr             x0, [x0, #0x660]
    // 0xb3a02c: StoreField: r2->field_1b = r0
    //     0xb3a02c: stur            w0, [x2, #0x1b]
    // 0xb3a030: r0 = Instance_BoxShape
    //     0xb3a030: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3a034: ldr             x0, [x0, #0x80]
    // 0xb3a038: StoreField: r2->field_23 = r0
    //     0xb3a038: stur            w0, [x2, #0x23]
    // 0xb3a03c: ldur            x1, [fp, #-0x10]
    // 0xb3a040: r0 = of()
    //     0xb3a040: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a044: LoadField: r1 = r0->field_87
    //     0xb3a044: ldur            w1, [x0, #0x87]
    // 0xb3a048: DecompressPointer r1
    //     0xb3a048: add             x1, x1, HEAP, lsl #32
    // 0xb3a04c: LoadField: r0 = r1->field_7
    //     0xb3a04c: ldur            w0, [x1, #7]
    // 0xb3a050: DecompressPointer r0
    //     0xb3a050: add             x0, x0, HEAP, lsl #32
    // 0xb3a054: r16 = 12.000000
    //     0xb3a054: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3a058: ldr             x16, [x16, #0x9e8]
    // 0xb3a05c: r30 = Instance_Color
    //     0xb3a05c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb3a060: stp             lr, x16, [SP]
    // 0xb3a064: mov             x1, x0
    // 0xb3a068: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3a068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3a06c: ldr             x4, [x4, #0xaa0]
    // 0xb3a070: r0 = copyWith()
    //     0xb3a070: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3a074: stur            x0, [fp, #-0x28]
    // 0xb3a078: r0 = Text()
    //     0xb3a078: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3a07c: mov             x1, x0
    // 0xb3a080: r0 = "Free"
    //     0xb3a080: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb3a084: ldr             x0, [x0, #0x668]
    // 0xb3a088: stur            x1, [fp, #-0x38]
    // 0xb3a08c: StoreField: r1->field_b = r0
    //     0xb3a08c: stur            w0, [x1, #0xb]
    // 0xb3a090: ldur            x2, [fp, #-0x28]
    // 0xb3a094: StoreField: r1->field_13 = r2
    //     0xb3a094: stur            w2, [x1, #0x13]
    // 0xb3a098: r0 = Center()
    //     0xb3a098: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb3a09c: mov             x1, x0
    // 0xb3a0a0: r0 = Instance_Alignment
    //     0xb3a0a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb3a0a4: ldr             x0, [x0, #0xb10]
    // 0xb3a0a8: stur            x1, [fp, #-0x28]
    // 0xb3a0ac: StoreField: r1->field_f = r0
    //     0xb3a0ac: stur            w0, [x1, #0xf]
    // 0xb3a0b0: ldur            x0, [fp, #-0x38]
    // 0xb3a0b4: StoreField: r1->field_b = r0
    //     0xb3a0b4: stur            w0, [x1, #0xb]
    // 0xb3a0b8: r0 = RotatedBox()
    //     0xb3a0b8: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb3a0bc: r1 = -1
    //     0xb3a0bc: movn            x1, #0
    // 0xb3a0c0: stur            x0, [fp, #-0x38]
    // 0xb3a0c4: StoreField: r0->field_f = r1
    //     0xb3a0c4: stur            x1, [x0, #0xf]
    // 0xb3a0c8: ldur            x1, [fp, #-0x28]
    // 0xb3a0cc: StoreField: r0->field_b = r1
    //     0xb3a0cc: stur            w1, [x0, #0xb]
    // 0xb3a0d0: r0 = Container()
    //     0xb3a0d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb3a0d4: stur            x0, [fp, #-0x28]
    // 0xb3a0d8: r16 = 24.000000
    //     0xb3a0d8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb3a0dc: ldr             x16, [x16, #0xba8]
    // 0xb3a0e0: r30 = 56.000000
    //     0xb3a0e0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3a0e4: ldr             lr, [lr, #0xb78]
    // 0xb3a0e8: stp             lr, x16, [SP, #0x10]
    // 0xb3a0ec: r16 = Instance_BoxDecoration
    //     0xb3a0ec: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0xb3a0f0: ldr             x16, [x16, #0xdb0]
    // 0xb3a0f4: ldur            lr, [fp, #-0x38]
    // 0xb3a0f8: stp             lr, x16, [SP]
    // 0xb3a0fc: mov             x1, x0
    // 0xb3a100: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb3a100: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb3a104: ldr             x4, [x4, #0x870]
    // 0xb3a108: r0 = Container()
    //     0xb3a108: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb3a10c: ldur            x0, [fp, #-8]
    // 0xb3a110: LoadField: r1 = r0->field_b
    //     0xb3a110: ldur            w1, [x0, #0xb]
    // 0xb3a114: DecompressPointer r1
    //     0xb3a114: add             x1, x1, HEAP, lsl #32
    // 0xb3a118: cmp             w1, NULL
    // 0xb3a11c: b.eq            #0xb3b3e8
    // 0xb3a120: LoadField: r2 = r1->field_b
    //     0xb3a120: ldur            w2, [x1, #0xb]
    // 0xb3a124: DecompressPointer r2
    //     0xb3a124: add             x2, x2, HEAP, lsl #32
    // 0xb3a128: LoadField: r1 = r2->field_b
    //     0xb3a128: ldur            w1, [x2, #0xb]
    // 0xb3a12c: DecompressPointer r1
    //     0xb3a12c: add             x1, x1, HEAP, lsl #32
    // 0xb3a130: cmp             w1, NULL
    // 0xb3a134: b.ne            #0xb3a140
    // 0xb3a138: r1 = Null
    //     0xb3a138: mov             x1, NULL
    // 0xb3a13c: b               #0xb3a160
    // 0xb3a140: LoadField: r2 = r1->field_43
    //     0xb3a140: ldur            w2, [x1, #0x43]
    // 0xb3a144: DecompressPointer r2
    //     0xb3a144: add             x2, x2, HEAP, lsl #32
    // 0xb3a148: cmp             w2, NULL
    // 0xb3a14c: b.ne            #0xb3a158
    // 0xb3a150: r1 = Null
    //     0xb3a150: mov             x1, NULL
    // 0xb3a154: b               #0xb3a160
    // 0xb3a158: LoadField: r1 = r2->field_7
    //     0xb3a158: ldur            w1, [x2, #7]
    // 0xb3a15c: DecompressPointer r1
    //     0xb3a15c: add             x1, x1, HEAP, lsl #32
    // 0xb3a160: cmp             w1, NULL
    // 0xb3a164: b.ne            #0xb3a170
    // 0xb3a168: r3 = ""
    //     0xb3a168: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3a16c: b               #0xb3a174
    // 0xb3a170: mov             x3, x1
    // 0xb3a174: stur            x3, [fp, #-0x38]
    // 0xb3a178: r1 = Function '<anonymous closure>':.
    //     0xb3a178: add             x1, PP, #0x57, lsl #12  ; [pp+0x570a8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3a17c: ldr             x1, [x1, #0xa8]
    // 0xb3a180: r2 = Null
    //     0xb3a180: mov             x2, NULL
    // 0xb3a184: r0 = AllocateClosure()
    //     0xb3a184: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3a188: r1 = Function '<anonymous closure>':.
    //     0xb3a188: add             x1, PP, #0x57, lsl #12  ; [pp+0x570b0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3a18c: ldr             x1, [x1, #0xb0]
    // 0xb3a190: r2 = Null
    //     0xb3a190: mov             x2, NULL
    // 0xb3a194: stur            x0, [fp, #-0x48]
    // 0xb3a198: r0 = AllocateClosure()
    //     0xb3a198: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3a19c: stur            x0, [fp, #-0x50]
    // 0xb3a1a0: r0 = CachedNetworkImage()
    //     0xb3a1a0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb3a1a4: stur            x0, [fp, #-0x58]
    // 0xb3a1a8: r16 = 56.000000
    //     0xb3a1a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3a1ac: ldr             x16, [x16, #0xb78]
    // 0xb3a1b0: r30 = 56.000000
    //     0xb3a1b0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3a1b4: ldr             lr, [lr, #0xb78]
    // 0xb3a1b8: stp             lr, x16, [SP, #0x18]
    // 0xb3a1bc: r16 = Instance_BoxFit
    //     0xb3a1bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb3a1c0: ldr             x16, [x16, #0x118]
    // 0xb3a1c4: ldur            lr, [fp, #-0x48]
    // 0xb3a1c8: stp             lr, x16, [SP, #8]
    // 0xb3a1cc: ldur            x16, [fp, #-0x50]
    // 0xb3a1d0: str             x16, [SP]
    // 0xb3a1d4: mov             x1, x0
    // 0xb3a1d8: ldur            x2, [fp, #-0x38]
    // 0xb3a1dc: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xb3a1dc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xb3a1e0: ldr             x4, [x4, #0x710]
    // 0xb3a1e4: r0 = CachedNetworkImage()
    //     0xb3a1e4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb3a1e8: ldur            x0, [fp, #-8]
    // 0xb3a1ec: LoadField: r1 = r0->field_b
    //     0xb3a1ec: ldur            w1, [x0, #0xb]
    // 0xb3a1f0: DecompressPointer r1
    //     0xb3a1f0: add             x1, x1, HEAP, lsl #32
    // 0xb3a1f4: cmp             w1, NULL
    // 0xb3a1f8: b.eq            #0xb3b3ec
    // 0xb3a1fc: LoadField: r2 = r1->field_b
    //     0xb3a1fc: ldur            w2, [x1, #0xb]
    // 0xb3a200: DecompressPointer r2
    //     0xb3a200: add             x2, x2, HEAP, lsl #32
    // 0xb3a204: LoadField: r1 = r2->field_b
    //     0xb3a204: ldur            w1, [x2, #0xb]
    // 0xb3a208: DecompressPointer r1
    //     0xb3a208: add             x1, x1, HEAP, lsl #32
    // 0xb3a20c: cmp             w1, NULL
    // 0xb3a210: b.ne            #0xb3a21c
    // 0xb3a214: r1 = Null
    //     0xb3a214: mov             x1, NULL
    // 0xb3a218: b               #0xb3a23c
    // 0xb3a21c: LoadField: r2 = r1->field_43
    //     0xb3a21c: ldur            w2, [x1, #0x43]
    // 0xb3a220: DecompressPointer r2
    //     0xb3a220: add             x2, x2, HEAP, lsl #32
    // 0xb3a224: cmp             w2, NULL
    // 0xb3a228: b.ne            #0xb3a234
    // 0xb3a22c: r1 = Null
    //     0xb3a22c: mov             x1, NULL
    // 0xb3a230: b               #0xb3a23c
    // 0xb3a234: LoadField: r1 = r2->field_b
    //     0xb3a234: ldur            w1, [x2, #0xb]
    // 0xb3a238: DecompressPointer r1
    //     0xb3a238: add             x1, x1, HEAP, lsl #32
    // 0xb3a23c: cmp             w1, NULL
    // 0xb3a240: b.ne            #0xb3a24c
    // 0xb3a244: r2 = ""
    //     0xb3a244: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3a248: b               #0xb3a250
    // 0xb3a24c: mov             x2, x1
    // 0xb3a250: ldur            x1, [fp, #-0x10]
    // 0xb3a254: stur            x2, [fp, #-0x38]
    // 0xb3a258: r0 = of()
    //     0xb3a258: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a25c: LoadField: r1 = r0->field_87
    //     0xb3a25c: ldur            w1, [x0, #0x87]
    // 0xb3a260: DecompressPointer r1
    //     0xb3a260: add             x1, x1, HEAP, lsl #32
    // 0xb3a264: LoadField: r0 = r1->field_7
    //     0xb3a264: ldur            w0, [x1, #7]
    // 0xb3a268: DecompressPointer r0
    //     0xb3a268: add             x0, x0, HEAP, lsl #32
    // 0xb3a26c: r16 = 12.000000
    //     0xb3a26c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3a270: ldr             x16, [x16, #0x9e8]
    // 0xb3a274: r30 = Instance_Color
    //     0xb3a274: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3a278: stp             lr, x16, [SP]
    // 0xb3a27c: mov             x1, x0
    // 0xb3a280: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3a280: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3a284: ldr             x4, [x4, #0xaa0]
    // 0xb3a288: r0 = copyWith()
    //     0xb3a288: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3a28c: stur            x0, [fp, #-0x48]
    // 0xb3a290: r0 = Text()
    //     0xb3a290: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3a294: mov             x1, x0
    // 0xb3a298: ldur            x0, [fp, #-0x38]
    // 0xb3a29c: stur            x1, [fp, #-0x50]
    // 0xb3a2a0: StoreField: r1->field_b = r0
    //     0xb3a2a0: stur            w0, [x1, #0xb]
    // 0xb3a2a4: ldur            x0, [fp, #-0x48]
    // 0xb3a2a8: StoreField: r1->field_13 = r0
    //     0xb3a2a8: stur            w0, [x1, #0x13]
    // 0xb3a2ac: r2 = Instance_TextOverflow
    //     0xb3a2ac: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb3a2b0: ldr             x2, [x2, #0xe10]
    // 0xb3a2b4: StoreField: r1->field_2b = r2
    //     0xb3a2b4: stur            w2, [x1, #0x2b]
    // 0xb3a2b8: r3 = 2
    //     0xb3a2b8: movz            x3, #0x2
    // 0xb3a2bc: StoreField: r1->field_37 = r3
    //     0xb3a2bc: stur            w3, [x1, #0x37]
    // 0xb3a2c0: r0 = SizedBox()
    //     0xb3a2c0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb3a2c4: r4 = 150.000000
    //     0xb3a2c4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb3a2c8: ldr             x4, [x4, #0x690]
    // 0xb3a2cc: stur            x0, [fp, #-0x38]
    // 0xb3a2d0: StoreField: r0->field_f = r4
    //     0xb3a2d0: stur            w4, [x0, #0xf]
    // 0xb3a2d4: ldur            x1, [fp, #-0x50]
    // 0xb3a2d8: StoreField: r0->field_b = r1
    //     0xb3a2d8: stur            w1, [x0, #0xb]
    // 0xb3a2dc: ldur            x1, [fp, #-0x10]
    // 0xb3a2e0: r0 = of()
    //     0xb3a2e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a2e4: LoadField: r1 = r0->field_87
    //     0xb3a2e4: ldur            w1, [x0, #0x87]
    // 0xb3a2e8: DecompressPointer r1
    //     0xb3a2e8: add             x1, x1, HEAP, lsl #32
    // 0xb3a2ec: LoadField: r0 = r1->field_2b
    //     0xb3a2ec: ldur            w0, [x1, #0x2b]
    // 0xb3a2f0: DecompressPointer r0
    //     0xb3a2f0: add             x0, x0, HEAP, lsl #32
    // 0xb3a2f4: r16 = 12.000000
    //     0xb3a2f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3a2f8: ldr             x16, [x16, #0x9e8]
    // 0xb3a2fc: r30 = Instance_Color
    //     0xb3a2fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3a300: ldr             lr, [lr, #0x858]
    // 0xb3a304: stp             lr, x16, [SP]
    // 0xb3a308: mov             x1, x0
    // 0xb3a30c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3a30c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3a310: ldr             x4, [x4, #0xaa0]
    // 0xb3a314: r0 = copyWith()
    //     0xb3a314: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3a318: stur            x0, [fp, #-0x48]
    // 0xb3a31c: r0 = Text()
    //     0xb3a31c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3a320: r5 = "Free"
    //     0xb3a320: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb3a324: ldr             x5, [x5, #0x668]
    // 0xb3a328: stur            x0, [fp, #-0x50]
    // 0xb3a32c: StoreField: r0->field_b = r5
    //     0xb3a32c: stur            w5, [x0, #0xb]
    // 0xb3a330: ldur            x1, [fp, #-0x48]
    // 0xb3a334: StoreField: r0->field_13 = r1
    //     0xb3a334: stur            w1, [x0, #0x13]
    // 0xb3a338: ldur            x2, [fp, #-8]
    // 0xb3a33c: LoadField: r1 = r2->field_b
    //     0xb3a33c: ldur            w1, [x2, #0xb]
    // 0xb3a340: DecompressPointer r1
    //     0xb3a340: add             x1, x1, HEAP, lsl #32
    // 0xb3a344: cmp             w1, NULL
    // 0xb3a348: b.eq            #0xb3b3f0
    // 0xb3a34c: LoadField: r3 = r1->field_b
    //     0xb3a34c: ldur            w3, [x1, #0xb]
    // 0xb3a350: DecompressPointer r3
    //     0xb3a350: add             x3, x3, HEAP, lsl #32
    // 0xb3a354: LoadField: r1 = r3->field_b
    //     0xb3a354: ldur            w1, [x3, #0xb]
    // 0xb3a358: DecompressPointer r1
    //     0xb3a358: add             x1, x1, HEAP, lsl #32
    // 0xb3a35c: cmp             w1, NULL
    // 0xb3a360: b.ne            #0xb3a36c
    // 0xb3a364: r1 = Null
    //     0xb3a364: mov             x1, NULL
    // 0xb3a368: b               #0xb3a38c
    // 0xb3a36c: LoadField: r3 = r1->field_43
    //     0xb3a36c: ldur            w3, [x1, #0x43]
    // 0xb3a370: DecompressPointer r3
    //     0xb3a370: add             x3, x3, HEAP, lsl #32
    // 0xb3a374: cmp             w3, NULL
    // 0xb3a378: b.ne            #0xb3a384
    // 0xb3a37c: r1 = Null
    //     0xb3a37c: mov             x1, NULL
    // 0xb3a380: b               #0xb3a38c
    // 0xb3a384: LoadField: r1 = r3->field_13
    //     0xb3a384: ldur            w1, [x3, #0x13]
    // 0xb3a388: DecompressPointer r1
    //     0xb3a388: add             x1, x1, HEAP, lsl #32
    // 0xb3a38c: cmp             w1, NULL
    // 0xb3a390: b.ne            #0xb3a39c
    // 0xb3a394: r6 = ""
    //     0xb3a394: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3a398: b               #0xb3a3a0
    // 0xb3a39c: mov             x6, x1
    // 0xb3a3a0: ldur            x5, [fp, #-0x28]
    // 0xb3a3a4: ldur            x4, [fp, #-0x58]
    // 0xb3a3a8: ldur            x3, [fp, #-0x38]
    // 0xb3a3ac: ldur            x1, [fp, #-0x10]
    // 0xb3a3b0: stur            x6, [fp, #-0x48]
    // 0xb3a3b4: r0 = of()
    //     0xb3a3b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a3b8: LoadField: r1 = r0->field_87
    //     0xb3a3b8: ldur            w1, [x0, #0x87]
    // 0xb3a3bc: DecompressPointer r1
    //     0xb3a3bc: add             x1, x1, HEAP, lsl #32
    // 0xb3a3c0: LoadField: r0 = r1->field_2b
    //     0xb3a3c0: ldur            w0, [x1, #0x2b]
    // 0xb3a3c4: DecompressPointer r0
    //     0xb3a3c4: add             x0, x0, HEAP, lsl #32
    // 0xb3a3c8: r16 = 12.000000
    //     0xb3a3c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3a3cc: ldr             x16, [x16, #0x9e8]
    // 0xb3a3d0: r30 = Instance_TextDecoration
    //     0xb3a3d0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb3a3d4: ldr             lr, [lr, #0xe30]
    // 0xb3a3d8: stp             lr, x16, [SP]
    // 0xb3a3dc: mov             x1, x0
    // 0xb3a3e0: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb3a3e0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb3a3e4: ldr             x4, [x4, #0x698]
    // 0xb3a3e8: r0 = copyWith()
    //     0xb3a3e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3a3ec: stur            x0, [fp, #-0x60]
    // 0xb3a3f0: r0 = Text()
    //     0xb3a3f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3a3f4: mov             x3, x0
    // 0xb3a3f8: ldur            x0, [fp, #-0x48]
    // 0xb3a3fc: stur            x3, [fp, #-0x68]
    // 0xb3a400: StoreField: r3->field_b = r0
    //     0xb3a400: stur            w0, [x3, #0xb]
    // 0xb3a404: ldur            x0, [fp, #-0x60]
    // 0xb3a408: StoreField: r3->field_13 = r0
    //     0xb3a408: stur            w0, [x3, #0x13]
    // 0xb3a40c: r1 = Null
    //     0xb3a40c: mov             x1, NULL
    // 0xb3a410: r2 = 6
    //     0xb3a410: movz            x2, #0x6
    // 0xb3a414: r0 = AllocateArray()
    //     0xb3a414: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3a418: mov             x2, x0
    // 0xb3a41c: ldur            x0, [fp, #-0x50]
    // 0xb3a420: stur            x2, [fp, #-0x48]
    // 0xb3a424: StoreField: r2->field_f = r0
    //     0xb3a424: stur            w0, [x2, #0xf]
    // 0xb3a428: r16 = Instance_SizedBox
    //     0xb3a428: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb3a42c: ldr             x16, [x16, #0xa50]
    // 0xb3a430: StoreField: r2->field_13 = r16
    //     0xb3a430: stur            w16, [x2, #0x13]
    // 0xb3a434: ldur            x0, [fp, #-0x68]
    // 0xb3a438: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3a438: stur            w0, [x2, #0x17]
    // 0xb3a43c: r1 = <Widget>
    //     0xb3a43c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3a440: r0 = AllocateGrowableArray()
    //     0xb3a440: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3a444: mov             x1, x0
    // 0xb3a448: ldur            x0, [fp, #-0x48]
    // 0xb3a44c: stur            x1, [fp, #-0x50]
    // 0xb3a450: StoreField: r1->field_f = r0
    //     0xb3a450: stur            w0, [x1, #0xf]
    // 0xb3a454: r2 = 6
    //     0xb3a454: movz            x2, #0x6
    // 0xb3a458: StoreField: r1->field_b = r2
    //     0xb3a458: stur            w2, [x1, #0xb]
    // 0xb3a45c: r0 = Row()
    //     0xb3a45c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3a460: mov             x3, x0
    // 0xb3a464: r0 = Instance_Axis
    //     0xb3a464: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3a468: stur            x3, [fp, #-0x48]
    // 0xb3a46c: StoreField: r3->field_f = r0
    //     0xb3a46c: stur            w0, [x3, #0xf]
    // 0xb3a470: r4 = Instance_MainAxisAlignment
    //     0xb3a470: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3a474: ldr             x4, [x4, #0xa08]
    // 0xb3a478: StoreField: r3->field_13 = r4
    //     0xb3a478: stur            w4, [x3, #0x13]
    // 0xb3a47c: r5 = Instance_MainAxisSize
    //     0xb3a47c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3a480: ldr             x5, [x5, #0xa10]
    // 0xb3a484: ArrayStore: r3[0] = r5  ; List_4
    //     0xb3a484: stur            w5, [x3, #0x17]
    // 0xb3a488: r6 = Instance_CrossAxisAlignment
    //     0xb3a488: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3a48c: ldr             x6, [x6, #0xa18]
    // 0xb3a490: StoreField: r3->field_1b = r6
    //     0xb3a490: stur            w6, [x3, #0x1b]
    // 0xb3a494: r7 = Instance_VerticalDirection
    //     0xb3a494: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3a498: ldr             x7, [x7, #0xa20]
    // 0xb3a49c: StoreField: r3->field_23 = r7
    //     0xb3a49c: stur            w7, [x3, #0x23]
    // 0xb3a4a0: r8 = Instance_Clip
    //     0xb3a4a0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3a4a4: ldr             x8, [x8, #0x38]
    // 0xb3a4a8: StoreField: r3->field_2b = r8
    //     0xb3a4a8: stur            w8, [x3, #0x2b]
    // 0xb3a4ac: StoreField: r3->field_2f = rZR
    //     0xb3a4ac: stur            xzr, [x3, #0x2f]
    // 0xb3a4b0: ldur            x1, [fp, #-0x50]
    // 0xb3a4b4: StoreField: r3->field_b = r1
    //     0xb3a4b4: stur            w1, [x3, #0xb]
    // 0xb3a4b8: r1 = Null
    //     0xb3a4b8: mov             x1, NULL
    // 0xb3a4bc: r2 = 6
    //     0xb3a4bc: movz            x2, #0x6
    // 0xb3a4c0: r0 = AllocateArray()
    //     0xb3a4c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3a4c4: mov             x2, x0
    // 0xb3a4c8: ldur            x0, [fp, #-0x38]
    // 0xb3a4cc: stur            x2, [fp, #-0x50]
    // 0xb3a4d0: StoreField: r2->field_f = r0
    //     0xb3a4d0: stur            w0, [x2, #0xf]
    // 0xb3a4d4: r16 = Instance_SizedBox
    //     0xb3a4d4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb3a4d8: ldr             x16, [x16, #0xc70]
    // 0xb3a4dc: StoreField: r2->field_13 = r16
    //     0xb3a4dc: stur            w16, [x2, #0x13]
    // 0xb3a4e0: ldur            x0, [fp, #-0x48]
    // 0xb3a4e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3a4e4: stur            w0, [x2, #0x17]
    // 0xb3a4e8: r1 = <Widget>
    //     0xb3a4e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3a4ec: r0 = AllocateGrowableArray()
    //     0xb3a4ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3a4f0: mov             x1, x0
    // 0xb3a4f4: ldur            x0, [fp, #-0x50]
    // 0xb3a4f8: stur            x1, [fp, #-0x38]
    // 0xb3a4fc: StoreField: r1->field_f = r0
    //     0xb3a4fc: stur            w0, [x1, #0xf]
    // 0xb3a500: r2 = 6
    //     0xb3a500: movz            x2, #0x6
    // 0xb3a504: StoreField: r1->field_b = r2
    //     0xb3a504: stur            w2, [x1, #0xb]
    // 0xb3a508: r0 = Column()
    //     0xb3a508: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3a50c: mov             x1, x0
    // 0xb3a510: r0 = Instance_Axis
    //     0xb3a510: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3a514: stur            x1, [fp, #-0x48]
    // 0xb3a518: StoreField: r1->field_f = r0
    //     0xb3a518: stur            w0, [x1, #0xf]
    // 0xb3a51c: r2 = Instance_MainAxisAlignment
    //     0xb3a51c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3a520: ldr             x2, [x2, #0xa08]
    // 0xb3a524: StoreField: r1->field_13 = r2
    //     0xb3a524: stur            w2, [x1, #0x13]
    // 0xb3a528: r3 = Instance_MainAxisSize
    //     0xb3a528: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3a52c: ldr             x3, [x3, #0xa10]
    // 0xb3a530: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3a530: stur            w3, [x1, #0x17]
    // 0xb3a534: r4 = Instance_CrossAxisAlignment
    //     0xb3a534: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3a538: ldr             x4, [x4, #0x890]
    // 0xb3a53c: StoreField: r1->field_1b = r4
    //     0xb3a53c: stur            w4, [x1, #0x1b]
    // 0xb3a540: r5 = Instance_VerticalDirection
    //     0xb3a540: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3a544: ldr             x5, [x5, #0xa20]
    // 0xb3a548: StoreField: r1->field_23 = r5
    //     0xb3a548: stur            w5, [x1, #0x23]
    // 0xb3a54c: r6 = Instance_Clip
    //     0xb3a54c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3a550: ldr             x6, [x6, #0x38]
    // 0xb3a554: StoreField: r1->field_2b = r6
    //     0xb3a554: stur            w6, [x1, #0x2b]
    // 0xb3a558: StoreField: r1->field_2f = rZR
    //     0xb3a558: stur            xzr, [x1, #0x2f]
    // 0xb3a55c: ldur            x7, [fp, #-0x38]
    // 0xb3a560: StoreField: r1->field_b = r7
    //     0xb3a560: stur            w7, [x1, #0xb]
    // 0xb3a564: r0 = Padding()
    //     0xb3a564: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3a568: r6 = Instance_EdgeInsets
    //     0xb3a568: add             x6, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3a56c: ldr             x6, [x6, #0xa78]
    // 0xb3a570: stur            x0, [fp, #-0x38]
    // 0xb3a574: StoreField: r0->field_f = r6
    //     0xb3a574: stur            w6, [x0, #0xf]
    // 0xb3a578: ldur            x1, [fp, #-0x48]
    // 0xb3a57c: StoreField: r0->field_b = r1
    //     0xb3a57c: stur            w1, [x0, #0xb]
    // 0xb3a580: r1 = <FlexParentData>
    //     0xb3a580: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3a584: ldr             x1, [x1, #0xe00]
    // 0xb3a588: r0 = Expanded()
    //     0xb3a588: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3a58c: mov             x3, x0
    // 0xb3a590: r0 = 1
    //     0xb3a590: movz            x0, #0x1
    // 0xb3a594: stur            x3, [fp, #-0x48]
    // 0xb3a598: StoreField: r3->field_13 = r0
    //     0xb3a598: stur            x0, [x3, #0x13]
    // 0xb3a59c: r4 = Instance_FlexFit
    //     0xb3a59c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3a5a0: ldr             x4, [x4, #0xe08]
    // 0xb3a5a4: StoreField: r3->field_1b = r4
    //     0xb3a5a4: stur            w4, [x3, #0x1b]
    // 0xb3a5a8: ldur            x1, [fp, #-0x38]
    // 0xb3a5ac: StoreField: r3->field_b = r1
    //     0xb3a5ac: stur            w1, [x3, #0xb]
    // 0xb3a5b0: r1 = Null
    //     0xb3a5b0: mov             x1, NULL
    // 0xb3a5b4: r2 = 6
    //     0xb3a5b4: movz            x2, #0x6
    // 0xb3a5b8: r0 = AllocateArray()
    //     0xb3a5b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3a5bc: mov             x2, x0
    // 0xb3a5c0: ldur            x0, [fp, #-0x28]
    // 0xb3a5c4: stur            x2, [fp, #-0x38]
    // 0xb3a5c8: StoreField: r2->field_f = r0
    //     0xb3a5c8: stur            w0, [x2, #0xf]
    // 0xb3a5cc: ldur            x0, [fp, #-0x58]
    // 0xb3a5d0: StoreField: r2->field_13 = r0
    //     0xb3a5d0: stur            w0, [x2, #0x13]
    // 0xb3a5d4: ldur            x0, [fp, #-0x48]
    // 0xb3a5d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3a5d8: stur            w0, [x2, #0x17]
    // 0xb3a5dc: r1 = <Widget>
    //     0xb3a5dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3a5e0: r0 = AllocateGrowableArray()
    //     0xb3a5e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3a5e4: mov             x1, x0
    // 0xb3a5e8: ldur            x0, [fp, #-0x38]
    // 0xb3a5ec: stur            x1, [fp, #-0x28]
    // 0xb3a5f0: StoreField: r1->field_f = r0
    //     0xb3a5f0: stur            w0, [x1, #0xf]
    // 0xb3a5f4: r2 = 6
    //     0xb3a5f4: movz            x2, #0x6
    // 0xb3a5f8: StoreField: r1->field_b = r2
    //     0xb3a5f8: stur            w2, [x1, #0xb]
    // 0xb3a5fc: r0 = Row()
    //     0xb3a5fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3a600: mov             x2, x0
    // 0xb3a604: r0 = Instance_Axis
    //     0xb3a604: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3a608: stur            x2, [fp, #-0x38]
    // 0xb3a60c: StoreField: r2->field_f = r0
    //     0xb3a60c: stur            w0, [x2, #0xf]
    // 0xb3a610: r3 = Instance_MainAxisAlignment
    //     0xb3a610: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3a614: ldr             x3, [x3, #0xa08]
    // 0xb3a618: StoreField: r2->field_13 = r3
    //     0xb3a618: stur            w3, [x2, #0x13]
    // 0xb3a61c: r4 = Instance_MainAxisSize
    //     0xb3a61c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3a620: ldr             x4, [x4, #0xa10]
    // 0xb3a624: ArrayStore: r2[0] = r4  ; List_4
    //     0xb3a624: stur            w4, [x2, #0x17]
    // 0xb3a628: r7 = Instance_CrossAxisAlignment
    //     0xb3a628: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3a62c: ldr             x7, [x7, #0xa18]
    // 0xb3a630: StoreField: r2->field_1b = r7
    //     0xb3a630: stur            w7, [x2, #0x1b]
    // 0xb3a634: r5 = Instance_VerticalDirection
    //     0xb3a634: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3a638: ldr             x5, [x5, #0xa20]
    // 0xb3a63c: StoreField: r2->field_23 = r5
    //     0xb3a63c: stur            w5, [x2, #0x23]
    // 0xb3a640: r6 = Instance_Clip
    //     0xb3a640: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3a644: ldr             x6, [x6, #0x38]
    // 0xb3a648: StoreField: r2->field_2b = r6
    //     0xb3a648: stur            w6, [x2, #0x2b]
    // 0xb3a64c: StoreField: r2->field_2f = rZR
    //     0xb3a64c: stur            xzr, [x2, #0x2f]
    // 0xb3a650: ldur            x1, [fp, #-0x28]
    // 0xb3a654: StoreField: r2->field_b = r1
    //     0xb3a654: stur            w1, [x2, #0xb]
    // 0xb3a658: r1 = <FlexParentData>
    //     0xb3a658: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3a65c: ldr             x1, [x1, #0xe00]
    // 0xb3a660: r0 = Expanded()
    //     0xb3a660: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3a664: r8 = 1
    //     0xb3a664: movz            x8, #0x1
    // 0xb3a668: stur            x0, [fp, #-0x28]
    // 0xb3a66c: StoreField: r0->field_13 = r8
    //     0xb3a66c: stur            x8, [x0, #0x13]
    // 0xb3a670: r9 = Instance_FlexFit
    //     0xb3a670: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3a674: ldr             x9, [x9, #0xe08]
    // 0xb3a678: StoreField: r0->field_1b = r9
    //     0xb3a678: stur            w9, [x0, #0x1b]
    // 0xb3a67c: ldur            x1, [fp, #-0x38]
    // 0xb3a680: StoreField: r0->field_b = r1
    //     0xb3a680: stur            w1, [x0, #0xb]
    // 0xb3a684: ldur            x1, [fp, #-0x10]
    // 0xb3a688: r0 = of()
    //     0xb3a688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a68c: LoadField: r1 = r0->field_87
    //     0xb3a68c: ldur            w1, [x0, #0x87]
    // 0xb3a690: DecompressPointer r1
    //     0xb3a690: add             x1, x1, HEAP, lsl #32
    // 0xb3a694: LoadField: r0 = r1->field_7
    //     0xb3a694: ldur            w0, [x1, #7]
    // 0xb3a698: DecompressPointer r0
    //     0xb3a698: add             x0, x0, HEAP, lsl #32
    // 0xb3a69c: r16 = 12.000000
    //     0xb3a69c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3a6a0: ldr             x16, [x16, #0x9e8]
    // 0xb3a6a4: r30 = Instance_Color
    //     0xb3a6a4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3a6a8: ldr             lr, [lr, #0x858]
    // 0xb3a6ac: stp             lr, x16, [SP]
    // 0xb3a6b0: mov             x1, x0
    // 0xb3a6b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3a6b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3a6b8: ldr             x4, [x4, #0xaa0]
    // 0xb3a6bc: r0 = copyWith()
    //     0xb3a6bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3a6c0: stur            x0, [fp, #-0x38]
    // 0xb3a6c4: r0 = Text()
    //     0xb3a6c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3a6c8: mov             x1, x0
    // 0xb3a6cc: r0 = "Remove"
    //     0xb3a6cc: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0xb3a6d0: ldr             x0, [x0, #0x7d0]
    // 0xb3a6d4: stur            x1, [fp, #-0x48]
    // 0xb3a6d8: StoreField: r1->field_b = r0
    //     0xb3a6d8: stur            w0, [x1, #0xb]
    // 0xb3a6dc: ldur            x0, [fp, #-0x38]
    // 0xb3a6e0: StoreField: r1->field_13 = r0
    //     0xb3a6e0: stur            w0, [x1, #0x13]
    // 0xb3a6e4: r0 = InkWell()
    //     0xb3a6e4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb3a6e8: mov             x3, x0
    // 0xb3a6ec: ldur            x0, [fp, #-0x48]
    // 0xb3a6f0: stur            x3, [fp, #-0x38]
    // 0xb3a6f4: StoreField: r3->field_b = r0
    //     0xb3a6f4: stur            w0, [x3, #0xb]
    // 0xb3a6f8: ldur            x2, [fp, #-0x18]
    // 0xb3a6fc: r1 = Function '<anonymous closure>':.
    //     0xb3a6fc: add             x1, PP, #0x57, lsl #12  ; [pp+0x570b8] AnonymousClosure: (0xb3c338), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xb39dd0)
    //     0xb3a700: ldr             x1, [x1, #0xb8]
    // 0xb3a704: r0 = AllocateClosure()
    //     0xb3a704: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3a708: mov             x1, x0
    // 0xb3a70c: ldur            x0, [fp, #-0x38]
    // 0xb3a710: StoreField: r0->field_f = r1
    //     0xb3a710: stur            w1, [x0, #0xf]
    // 0xb3a714: r10 = true
    //     0xb3a714: add             x10, NULL, #0x20  ; true
    // 0xb3a718: StoreField: r0->field_43 = r10
    //     0xb3a718: stur            w10, [x0, #0x43]
    // 0xb3a71c: r11 = Instance_BoxShape
    //     0xb3a71c: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3a720: ldr             x11, [x11, #0x80]
    // 0xb3a724: StoreField: r0->field_47 = r11
    //     0xb3a724: stur            w11, [x0, #0x47]
    // 0xb3a728: StoreField: r0->field_6f = r10
    //     0xb3a728: stur            w10, [x0, #0x6f]
    // 0xb3a72c: r1 = false
    //     0xb3a72c: add             x1, NULL, #0x30  ; false
    // 0xb3a730: StoreField: r0->field_73 = r1
    //     0xb3a730: stur            w1, [x0, #0x73]
    // 0xb3a734: StoreField: r0->field_83 = r10
    //     0xb3a734: stur            w10, [x0, #0x83]
    // 0xb3a738: StoreField: r0->field_7b = r1
    //     0xb3a738: stur            w1, [x0, #0x7b]
    // 0xb3a73c: r0 = Padding()
    //     0xb3a73c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3a740: r12 = Instance_EdgeInsets
    //     0xb3a740: add             x12, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xb3a744: ldr             x12, [x12, #0xa40]
    // 0xb3a748: stur            x0, [fp, #-0x48]
    // 0xb3a74c: StoreField: r0->field_f = r12
    //     0xb3a74c: stur            w12, [x0, #0xf]
    // 0xb3a750: ldur            x1, [fp, #-0x38]
    // 0xb3a754: StoreField: r0->field_b = r1
    //     0xb3a754: stur            w1, [x0, #0xb]
    // 0xb3a758: r1 = Null
    //     0xb3a758: mov             x1, NULL
    // 0xb3a75c: r2 = 4
    //     0xb3a75c: movz            x2, #0x4
    // 0xb3a760: r0 = AllocateArray()
    //     0xb3a760: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3a764: mov             x2, x0
    // 0xb3a768: ldur            x0, [fp, #-0x28]
    // 0xb3a76c: stur            x2, [fp, #-0x38]
    // 0xb3a770: StoreField: r2->field_f = r0
    //     0xb3a770: stur            w0, [x2, #0xf]
    // 0xb3a774: ldur            x0, [fp, #-0x48]
    // 0xb3a778: StoreField: r2->field_13 = r0
    //     0xb3a778: stur            w0, [x2, #0x13]
    // 0xb3a77c: r1 = <Widget>
    //     0xb3a77c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3a780: r0 = AllocateGrowableArray()
    //     0xb3a780: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3a784: mov             x1, x0
    // 0xb3a788: ldur            x0, [fp, #-0x38]
    // 0xb3a78c: stur            x1, [fp, #-0x28]
    // 0xb3a790: StoreField: r1->field_f = r0
    //     0xb3a790: stur            w0, [x1, #0xf]
    // 0xb3a794: r13 = 4
    //     0xb3a794: movz            x13, #0x4
    // 0xb3a798: StoreField: r1->field_b = r13
    //     0xb3a798: stur            w13, [x1, #0xb]
    // 0xb3a79c: r0 = Row()
    //     0xb3a79c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3a7a0: r14 = Instance_Axis
    //     0xb3a7a0: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3a7a4: stur            x0, [fp, #-0x38]
    // 0xb3a7a8: StoreField: r0->field_f = r14
    //     0xb3a7a8: stur            w14, [x0, #0xf]
    // 0xb3a7ac: r19 = Instance_MainAxisAlignment
    //     0xb3a7ac: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb3a7b0: ldr             x19, [x19, #0xa8]
    // 0xb3a7b4: StoreField: r0->field_13 = r19
    //     0xb3a7b4: stur            w19, [x0, #0x13]
    // 0xb3a7b8: r1 = Instance_MainAxisSize
    //     0xb3a7b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3a7bc: ldr             x1, [x1, #0xa10]
    // 0xb3a7c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb3a7c0: stur            w1, [x0, #0x17]
    // 0xb3a7c4: r2 = Instance_CrossAxisAlignment
    //     0xb3a7c4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3a7c8: ldr             x2, [x2, #0x890]
    // 0xb3a7cc: StoreField: r0->field_1b = r2
    //     0xb3a7cc: stur            w2, [x0, #0x1b]
    // 0xb3a7d0: r3 = Instance_VerticalDirection
    //     0xb3a7d0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3a7d4: ldr             x3, [x3, #0xa20]
    // 0xb3a7d8: StoreField: r0->field_23 = r3
    //     0xb3a7d8: stur            w3, [x0, #0x23]
    // 0xb3a7dc: r4 = Instance_Clip
    //     0xb3a7dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3a7e0: ldr             x4, [x4, #0x38]
    // 0xb3a7e4: StoreField: r0->field_2b = r4
    //     0xb3a7e4: stur            w4, [x0, #0x2b]
    // 0xb3a7e8: StoreField: r0->field_2f = rZR
    //     0xb3a7e8: stur            xzr, [x0, #0x2f]
    // 0xb3a7ec: ldur            x5, [fp, #-0x28]
    // 0xb3a7f0: StoreField: r0->field_b = r5
    //     0xb3a7f0: stur            w5, [x0, #0xb]
    // 0xb3a7f4: r0 = Container()
    //     0xb3a7f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb3a7f8: stur            x0, [fp, #-0x28]
    // 0xb3a7fc: ldur            x16, [fp, #-0x40]
    // 0xb3a800: ldur            lr, [fp, #-0x38]
    // 0xb3a804: stp             lr, x16, [SP]
    // 0xb3a808: mov             x1, x0
    // 0xb3a80c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb3a80c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb3a810: ldr             x4, [x4, #0x88]
    // 0xb3a814: r0 = Container()
    //     0xb3a814: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb3a818: r0 = Padding()
    //     0xb3a818: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3a81c: r20 = Instance_EdgeInsets
    //     0xb3a81c: add             x20, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb3a820: ldr             x20, [x20, #0x778]
    // 0xb3a824: StoreField: r0->field_f = r20
    //     0xb3a824: stur            w20, [x0, #0xf]
    // 0xb3a828: ldur            x1, [fp, #-0x28]
    // 0xb3a82c: StoreField: r0->field_b = r1
    //     0xb3a82c: stur            w1, [x0, #0xb]
    // 0xb3a830: mov             x2, x0
    // 0xb3a834: b               #0xb3b1c0
    // 0xb3a838: r10 = true
    //     0xb3a838: add             x10, NULL, #0x20  ; true
    // 0xb3a83c: r3 = 2
    //     0xb3a83c: movz            x3, #0x2
    // 0xb3a840: r5 = "Free"
    //     0xb3a840: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb3a844: ldr             x5, [x5, #0x668]
    // 0xb3a848: r4 = 150.000000
    //     0xb3a848: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb3a84c: ldr             x4, [x4, #0x690]
    // 0xb3a850: r2 = Instance_TextOverflow
    //     0xb3a850: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb3a854: ldr             x2, [x2, #0xe10]
    // 0xb3a858: r6 = Instance_EdgeInsets
    //     0xb3a858: add             x6, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3a85c: ldr             x6, [x6, #0xa78]
    // 0xb3a860: r7 = Instance_CrossAxisAlignment
    //     0xb3a860: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3a864: ldr             x7, [x7, #0xa18]
    // 0xb3a868: r12 = Instance_EdgeInsets
    //     0xb3a868: add             x12, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xb3a86c: ldr             x12, [x12, #0xa40]
    // 0xb3a870: r19 = Instance_MainAxisAlignment
    //     0xb3a870: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb3a874: ldr             x19, [x19, #0xa8]
    // 0xb3a878: r20 = Instance_EdgeInsets
    //     0xb3a878: add             x20, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb3a87c: ldr             x20, [x20, #0x778]
    // 0xb3a880: r13 = 4
    //     0xb3a880: movz            x13, #0x4
    // 0xb3a884: r14 = Instance_Axis
    //     0xb3a884: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3a888: r9 = Instance_FlexFit
    //     0xb3a888: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3a88c: ldr             x9, [x9, #0xe08]
    // 0xb3a890: r11 = Instance_BoxShape
    //     0xb3a890: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3a894: ldr             x11, [x11, #0x80]
    // 0xb3a898: r0 = Instance_Alignment
    //     0xb3a898: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb3a89c: ldr             x0, [x0, #0xb10]
    // 0xb3a8a0: r1 = -1
    //     0xb3a8a0: movn            x1, #0
    // 0xb3a8a4: d0 = 12.000000
    //     0xb3a8a4: fmov            d0, #12.00000000
    // 0xb3a8a8: r8 = 1
    //     0xb3a8a8: movz            x8, #0x1
    // 0xb3a8ac: ldur            x23, [fp, #-8]
    // 0xb3a8b0: r0 = Radius()
    //     0xb3a8b0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3a8b4: d0 = 12.000000
    //     0xb3a8b4: fmov            d0, #12.00000000
    // 0xb3a8b8: stur            x0, [fp, #-0x28]
    // 0xb3a8bc: StoreField: r0->field_7 = d0
    //     0xb3a8bc: stur            d0, [x0, #7]
    // 0xb3a8c0: StoreField: r0->field_f = d0
    //     0xb3a8c0: stur            d0, [x0, #0xf]
    // 0xb3a8c4: r0 = BorderRadius()
    //     0xb3a8c4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3a8c8: mov             x2, x0
    // 0xb3a8cc: ldur            x0, [fp, #-0x28]
    // 0xb3a8d0: stur            x2, [fp, #-0x38]
    // 0xb3a8d4: StoreField: r2->field_7 = r0
    //     0xb3a8d4: stur            w0, [x2, #7]
    // 0xb3a8d8: StoreField: r2->field_b = r0
    //     0xb3a8d8: stur            w0, [x2, #0xb]
    // 0xb3a8dc: StoreField: r2->field_f = r0
    //     0xb3a8dc: stur            w0, [x2, #0xf]
    // 0xb3a8e0: StoreField: r2->field_13 = r0
    //     0xb3a8e0: stur            w0, [x2, #0x13]
    // 0xb3a8e4: ldur            x1, [fp, #-0x10]
    // 0xb3a8e8: r0 = of()
    //     0xb3a8e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a8ec: LoadField: r1 = r0->field_5b
    //     0xb3a8ec: ldur            w1, [x0, #0x5b]
    // 0xb3a8f0: DecompressPointer r1
    //     0xb3a8f0: add             x1, x1, HEAP, lsl #32
    // 0xb3a8f4: r0 = LoadClassIdInstr(r1)
    //     0xb3a8f4: ldur            x0, [x1, #-1]
    //     0xb3a8f8: ubfx            x0, x0, #0xc, #0x14
    // 0xb3a8fc: d0 = 0.070000
    //     0xb3a8fc: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb3a900: ldr             d0, [x17, #0x5f8]
    // 0xb3a904: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb3a904: sub             lr, x0, #0xffa
    //     0xb3a908: ldr             lr, [x21, lr, lsl #3]
    //     0xb3a90c: blr             lr
    // 0xb3a910: r16 = 1.000000
    //     0xb3a910: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb3a914: str             x16, [SP]
    // 0xb3a918: mov             x2, x0
    // 0xb3a91c: r1 = Null
    //     0xb3a91c: mov             x1, NULL
    // 0xb3a920: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb3a920: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb3a924: ldr             x4, [x4, #0x108]
    // 0xb3a928: r0 = Border.all()
    //     0xb3a928: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb3a92c: stur            x0, [fp, #-0x28]
    // 0xb3a930: r0 = BoxDecoration()
    //     0xb3a930: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb3a934: mov             x2, x0
    // 0xb3a938: ldur            x0, [fp, #-0x28]
    // 0xb3a93c: stur            x2, [fp, #-0x40]
    // 0xb3a940: StoreField: r2->field_f = r0
    //     0xb3a940: stur            w0, [x2, #0xf]
    // 0xb3a944: ldur            x0, [fp, #-0x38]
    // 0xb3a948: StoreField: r2->field_13 = r0
    //     0xb3a948: stur            w0, [x2, #0x13]
    // 0xb3a94c: r0 = Instance_BoxShape
    //     0xb3a94c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3a950: ldr             x0, [x0, #0x80]
    // 0xb3a954: StoreField: r2->field_23 = r0
    //     0xb3a954: stur            w0, [x2, #0x23]
    // 0xb3a958: ldur            x1, [fp, #-0x10]
    // 0xb3a95c: r0 = of()
    //     0xb3a95c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a960: LoadField: r1 = r0->field_5b
    //     0xb3a960: ldur            w1, [x0, #0x5b]
    // 0xb3a964: DecompressPointer r1
    //     0xb3a964: add             x1, x1, HEAP, lsl #32
    // 0xb3a968: r0 = LoadClassIdInstr(r1)
    //     0xb3a968: ldur            x0, [x1, #-1]
    //     0xb3a96c: ubfx            x0, x0, #0xc, #0x14
    // 0xb3a970: d0 = 0.400000
    //     0xb3a970: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb3a974: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb3a974: sub             lr, x0, #0xffa
    //     0xb3a978: ldr             lr, [x21, lr, lsl #3]
    //     0xb3a97c: blr             lr
    // 0xb3a980: stur            x0, [fp, #-0x28]
    // 0xb3a984: r0 = BoxDecoration()
    //     0xb3a984: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb3a988: mov             x2, x0
    // 0xb3a98c: ldur            x0, [fp, #-0x28]
    // 0xb3a990: stur            x2, [fp, #-0x38]
    // 0xb3a994: StoreField: r2->field_7 = r0
    //     0xb3a994: stur            w0, [x2, #7]
    // 0xb3a998: r0 = Instance_BorderRadius
    //     0xb3a998: add             x0, PP, #0x48, lsl #12  ; [pp+0x48990] Obj!BorderRadius@d5a281
    //     0xb3a99c: ldr             x0, [x0, #0x990]
    // 0xb3a9a0: StoreField: r2->field_13 = r0
    //     0xb3a9a0: stur            w0, [x2, #0x13]
    // 0xb3a9a4: r0 = Instance_BoxShape
    //     0xb3a9a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3a9a8: ldr             x0, [x0, #0x80]
    // 0xb3a9ac: StoreField: r2->field_23 = r0
    //     0xb3a9ac: stur            w0, [x2, #0x23]
    // 0xb3a9b0: ldur            x1, [fp, #-0x10]
    // 0xb3a9b4: r0 = of()
    //     0xb3a9b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3a9b8: LoadField: r1 = r0->field_87
    //     0xb3a9b8: ldur            w1, [x0, #0x87]
    // 0xb3a9bc: DecompressPointer r1
    //     0xb3a9bc: add             x1, x1, HEAP, lsl #32
    // 0xb3a9c0: LoadField: r0 = r1->field_7
    //     0xb3a9c0: ldur            w0, [x1, #7]
    // 0xb3a9c4: DecompressPointer r0
    //     0xb3a9c4: add             x0, x0, HEAP, lsl #32
    // 0xb3a9c8: r16 = 12.000000
    //     0xb3a9c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3a9cc: ldr             x16, [x16, #0x9e8]
    // 0xb3a9d0: r30 = Instance_Color
    //     0xb3a9d0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb3a9d4: stp             lr, x16, [SP]
    // 0xb3a9d8: mov             x1, x0
    // 0xb3a9dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3a9dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3a9e0: ldr             x4, [x4, #0xaa0]
    // 0xb3a9e4: r0 = copyWith()
    //     0xb3a9e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3a9e8: stur            x0, [fp, #-0x28]
    // 0xb3a9ec: r0 = Text()
    //     0xb3a9ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3a9f0: mov             x1, x0
    // 0xb3a9f4: r0 = "Free"
    //     0xb3a9f4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb3a9f8: ldr             x0, [x0, #0x668]
    // 0xb3a9fc: stur            x1, [fp, #-0x48]
    // 0xb3aa00: StoreField: r1->field_b = r0
    //     0xb3aa00: stur            w0, [x1, #0xb]
    // 0xb3aa04: ldur            x2, [fp, #-0x28]
    // 0xb3aa08: StoreField: r1->field_13 = r2
    //     0xb3aa08: stur            w2, [x1, #0x13]
    // 0xb3aa0c: r0 = Center()
    //     0xb3aa0c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb3aa10: mov             x1, x0
    // 0xb3aa14: r0 = Instance_Alignment
    //     0xb3aa14: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb3aa18: ldr             x0, [x0, #0xb10]
    // 0xb3aa1c: stur            x1, [fp, #-0x28]
    // 0xb3aa20: StoreField: r1->field_f = r0
    //     0xb3aa20: stur            w0, [x1, #0xf]
    // 0xb3aa24: ldur            x0, [fp, #-0x48]
    // 0xb3aa28: StoreField: r1->field_b = r0
    //     0xb3aa28: stur            w0, [x1, #0xb]
    // 0xb3aa2c: r0 = RotatedBox()
    //     0xb3aa2c: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb3aa30: mov             x1, x0
    // 0xb3aa34: r0 = -1
    //     0xb3aa34: movn            x0, #0
    // 0xb3aa38: stur            x1, [fp, #-0x48]
    // 0xb3aa3c: StoreField: r1->field_f = r0
    //     0xb3aa3c: stur            x0, [x1, #0xf]
    // 0xb3aa40: ldur            x0, [fp, #-0x28]
    // 0xb3aa44: StoreField: r1->field_b = r0
    //     0xb3aa44: stur            w0, [x1, #0xb]
    // 0xb3aa48: r0 = Container()
    //     0xb3aa48: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb3aa4c: stur            x0, [fp, #-0x28]
    // 0xb3aa50: r16 = 24.000000
    //     0xb3aa50: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb3aa54: ldr             x16, [x16, #0xba8]
    // 0xb3aa58: r30 = 56.000000
    //     0xb3aa58: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3aa5c: ldr             lr, [lr, #0xb78]
    // 0xb3aa60: stp             lr, x16, [SP, #0x10]
    // 0xb3aa64: ldur            x16, [fp, #-0x38]
    // 0xb3aa68: ldur            lr, [fp, #-0x48]
    // 0xb3aa6c: stp             lr, x16, [SP]
    // 0xb3aa70: mov             x1, x0
    // 0xb3aa74: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb3aa74: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb3aa78: ldr             x4, [x4, #0x870]
    // 0xb3aa7c: r0 = Container()
    //     0xb3aa7c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb3aa80: ldur            x0, [fp, #-8]
    // 0xb3aa84: LoadField: r1 = r0->field_b
    //     0xb3aa84: ldur            w1, [x0, #0xb]
    // 0xb3aa88: DecompressPointer r1
    //     0xb3aa88: add             x1, x1, HEAP, lsl #32
    // 0xb3aa8c: cmp             w1, NULL
    // 0xb3aa90: b.eq            #0xb3b3f4
    // 0xb3aa94: LoadField: r2 = r1->field_b
    //     0xb3aa94: ldur            w2, [x1, #0xb]
    // 0xb3aa98: DecompressPointer r2
    //     0xb3aa98: add             x2, x2, HEAP, lsl #32
    // 0xb3aa9c: LoadField: r1 = r2->field_b
    //     0xb3aa9c: ldur            w1, [x2, #0xb]
    // 0xb3aaa0: DecompressPointer r1
    //     0xb3aaa0: add             x1, x1, HEAP, lsl #32
    // 0xb3aaa4: cmp             w1, NULL
    // 0xb3aaa8: b.ne            #0xb3aab4
    // 0xb3aaac: r1 = Null
    //     0xb3aaac: mov             x1, NULL
    // 0xb3aab0: b               #0xb3aad4
    // 0xb3aab4: LoadField: r2 = r1->field_43
    //     0xb3aab4: ldur            w2, [x1, #0x43]
    // 0xb3aab8: DecompressPointer r2
    //     0xb3aab8: add             x2, x2, HEAP, lsl #32
    // 0xb3aabc: cmp             w2, NULL
    // 0xb3aac0: b.ne            #0xb3aacc
    // 0xb3aac4: r1 = Null
    //     0xb3aac4: mov             x1, NULL
    // 0xb3aac8: b               #0xb3aad4
    // 0xb3aacc: LoadField: r1 = r2->field_7
    //     0xb3aacc: ldur            w1, [x2, #7]
    // 0xb3aad0: DecompressPointer r1
    //     0xb3aad0: add             x1, x1, HEAP, lsl #32
    // 0xb3aad4: cmp             w1, NULL
    // 0xb3aad8: b.ne            #0xb3aae4
    // 0xb3aadc: r3 = ""
    //     0xb3aadc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3aae0: b               #0xb3aae8
    // 0xb3aae4: mov             x3, x1
    // 0xb3aae8: stur            x3, [fp, #-0x38]
    // 0xb3aaec: r1 = Function '<anonymous closure>':.
    //     0xb3aaec: add             x1, PP, #0x57, lsl #12  ; [pp+0x570c0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3aaf0: ldr             x1, [x1, #0xc0]
    // 0xb3aaf4: r2 = Null
    //     0xb3aaf4: mov             x2, NULL
    // 0xb3aaf8: r0 = AllocateClosure()
    //     0xb3aaf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3aafc: r1 = Function '<anonymous closure>':.
    //     0xb3aafc: add             x1, PP, #0x57, lsl #12  ; [pp+0x570c8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3ab00: ldr             x1, [x1, #0xc8]
    // 0xb3ab04: r2 = Null
    //     0xb3ab04: mov             x2, NULL
    // 0xb3ab08: stur            x0, [fp, #-0x48]
    // 0xb3ab0c: r0 = AllocateClosure()
    //     0xb3ab0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3ab10: stur            x0, [fp, #-0x50]
    // 0xb3ab14: r0 = CachedNetworkImage()
    //     0xb3ab14: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb3ab18: stur            x0, [fp, #-0x58]
    // 0xb3ab1c: r16 = 56.000000
    //     0xb3ab1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3ab20: ldr             x16, [x16, #0xb78]
    // 0xb3ab24: r30 = 56.000000
    //     0xb3ab24: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3ab28: ldr             lr, [lr, #0xb78]
    // 0xb3ab2c: stp             lr, x16, [SP, #0x18]
    // 0xb3ab30: r16 = Instance_BoxFit
    //     0xb3ab30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb3ab34: ldr             x16, [x16, #0x118]
    // 0xb3ab38: ldur            lr, [fp, #-0x48]
    // 0xb3ab3c: stp             lr, x16, [SP, #8]
    // 0xb3ab40: ldur            x16, [fp, #-0x50]
    // 0xb3ab44: str             x16, [SP]
    // 0xb3ab48: mov             x1, x0
    // 0xb3ab4c: ldur            x2, [fp, #-0x38]
    // 0xb3ab50: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xb3ab50: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xb3ab54: ldr             x4, [x4, #0x710]
    // 0xb3ab58: r0 = CachedNetworkImage()
    //     0xb3ab58: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb3ab5c: ldur            x0, [fp, #-8]
    // 0xb3ab60: LoadField: r1 = r0->field_b
    //     0xb3ab60: ldur            w1, [x0, #0xb]
    // 0xb3ab64: DecompressPointer r1
    //     0xb3ab64: add             x1, x1, HEAP, lsl #32
    // 0xb3ab68: cmp             w1, NULL
    // 0xb3ab6c: b.eq            #0xb3b3f8
    // 0xb3ab70: LoadField: r2 = r1->field_b
    //     0xb3ab70: ldur            w2, [x1, #0xb]
    // 0xb3ab74: DecompressPointer r2
    //     0xb3ab74: add             x2, x2, HEAP, lsl #32
    // 0xb3ab78: LoadField: r1 = r2->field_b
    //     0xb3ab78: ldur            w1, [x2, #0xb]
    // 0xb3ab7c: DecompressPointer r1
    //     0xb3ab7c: add             x1, x1, HEAP, lsl #32
    // 0xb3ab80: cmp             w1, NULL
    // 0xb3ab84: b.ne            #0xb3ab90
    // 0xb3ab88: r1 = Null
    //     0xb3ab88: mov             x1, NULL
    // 0xb3ab8c: b               #0xb3abb0
    // 0xb3ab90: LoadField: r2 = r1->field_43
    //     0xb3ab90: ldur            w2, [x1, #0x43]
    // 0xb3ab94: DecompressPointer r2
    //     0xb3ab94: add             x2, x2, HEAP, lsl #32
    // 0xb3ab98: cmp             w2, NULL
    // 0xb3ab9c: b.ne            #0xb3aba8
    // 0xb3aba0: r1 = Null
    //     0xb3aba0: mov             x1, NULL
    // 0xb3aba4: b               #0xb3abb0
    // 0xb3aba8: LoadField: r1 = r2->field_b
    //     0xb3aba8: ldur            w1, [x2, #0xb]
    // 0xb3abac: DecompressPointer r1
    //     0xb3abac: add             x1, x1, HEAP, lsl #32
    // 0xb3abb0: cmp             w1, NULL
    // 0xb3abb4: b.ne            #0xb3abc0
    // 0xb3abb8: r2 = ""
    //     0xb3abb8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3abbc: b               #0xb3abc4
    // 0xb3abc0: mov             x2, x1
    // 0xb3abc4: ldur            x1, [fp, #-0x10]
    // 0xb3abc8: stur            x2, [fp, #-0x38]
    // 0xb3abcc: r0 = of()
    //     0xb3abcc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3abd0: LoadField: r1 = r0->field_87
    //     0xb3abd0: ldur            w1, [x0, #0x87]
    // 0xb3abd4: DecompressPointer r1
    //     0xb3abd4: add             x1, x1, HEAP, lsl #32
    // 0xb3abd8: LoadField: r0 = r1->field_7
    //     0xb3abd8: ldur            w0, [x1, #7]
    // 0xb3abdc: DecompressPointer r0
    //     0xb3abdc: add             x0, x0, HEAP, lsl #32
    // 0xb3abe0: r16 = 12.000000
    //     0xb3abe0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3abe4: ldr             x16, [x16, #0x9e8]
    // 0xb3abe8: r30 = Instance_Color
    //     0xb3abe8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3abec: stp             lr, x16, [SP]
    // 0xb3abf0: mov             x1, x0
    // 0xb3abf4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3abf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3abf8: ldr             x4, [x4, #0xaa0]
    // 0xb3abfc: r0 = copyWith()
    //     0xb3abfc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3ac00: stur            x0, [fp, #-0x48]
    // 0xb3ac04: r0 = Text()
    //     0xb3ac04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3ac08: mov             x1, x0
    // 0xb3ac0c: ldur            x0, [fp, #-0x38]
    // 0xb3ac10: stur            x1, [fp, #-0x50]
    // 0xb3ac14: StoreField: r1->field_b = r0
    //     0xb3ac14: stur            w0, [x1, #0xb]
    // 0xb3ac18: ldur            x0, [fp, #-0x48]
    // 0xb3ac1c: StoreField: r1->field_13 = r0
    //     0xb3ac1c: stur            w0, [x1, #0x13]
    // 0xb3ac20: r0 = Instance_TextOverflow
    //     0xb3ac20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb3ac24: ldr             x0, [x0, #0xe10]
    // 0xb3ac28: StoreField: r1->field_2b = r0
    //     0xb3ac28: stur            w0, [x1, #0x2b]
    // 0xb3ac2c: r0 = 2
    //     0xb3ac2c: movz            x0, #0x2
    // 0xb3ac30: StoreField: r1->field_37 = r0
    //     0xb3ac30: stur            w0, [x1, #0x37]
    // 0xb3ac34: r0 = SizedBox()
    //     0xb3ac34: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb3ac38: mov             x2, x0
    // 0xb3ac3c: r0 = 150.000000
    //     0xb3ac3c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb3ac40: ldr             x0, [x0, #0x690]
    // 0xb3ac44: stur            x2, [fp, #-0x38]
    // 0xb3ac48: StoreField: r2->field_f = r0
    //     0xb3ac48: stur            w0, [x2, #0xf]
    // 0xb3ac4c: ldur            x0, [fp, #-0x50]
    // 0xb3ac50: StoreField: r2->field_b = r0
    //     0xb3ac50: stur            w0, [x2, #0xb]
    // 0xb3ac54: ldur            x1, [fp, #-0x10]
    // 0xb3ac58: r0 = of()
    //     0xb3ac58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ac5c: LoadField: r1 = r0->field_87
    //     0xb3ac5c: ldur            w1, [x0, #0x87]
    // 0xb3ac60: DecompressPointer r1
    //     0xb3ac60: add             x1, x1, HEAP, lsl #32
    // 0xb3ac64: LoadField: r0 = r1->field_2b
    //     0xb3ac64: ldur            w0, [x1, #0x2b]
    // 0xb3ac68: DecompressPointer r0
    //     0xb3ac68: add             x0, x0, HEAP, lsl #32
    // 0xb3ac6c: r16 = 12.000000
    //     0xb3ac6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3ac70: ldr             x16, [x16, #0x9e8]
    // 0xb3ac74: r30 = Instance_Color
    //     0xb3ac74: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3ac78: stp             lr, x16, [SP]
    // 0xb3ac7c: mov             x1, x0
    // 0xb3ac80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3ac80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3ac84: ldr             x4, [x4, #0xaa0]
    // 0xb3ac88: r0 = copyWith()
    //     0xb3ac88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3ac8c: stur            x0, [fp, #-0x48]
    // 0xb3ac90: r0 = Text()
    //     0xb3ac90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3ac94: mov             x2, x0
    // 0xb3ac98: r0 = "Free"
    //     0xb3ac98: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb3ac9c: ldr             x0, [x0, #0x668]
    // 0xb3aca0: stur            x2, [fp, #-0x50]
    // 0xb3aca4: StoreField: r2->field_b = r0
    //     0xb3aca4: stur            w0, [x2, #0xb]
    // 0xb3aca8: ldur            x0, [fp, #-0x48]
    // 0xb3acac: StoreField: r2->field_13 = r0
    //     0xb3acac: stur            w0, [x2, #0x13]
    // 0xb3acb0: ldur            x0, [fp, #-8]
    // 0xb3acb4: LoadField: r1 = r0->field_b
    //     0xb3acb4: ldur            w1, [x0, #0xb]
    // 0xb3acb8: DecompressPointer r1
    //     0xb3acb8: add             x1, x1, HEAP, lsl #32
    // 0xb3acbc: cmp             w1, NULL
    // 0xb3acc0: b.eq            #0xb3b3fc
    // 0xb3acc4: LoadField: r3 = r1->field_b
    //     0xb3acc4: ldur            w3, [x1, #0xb]
    // 0xb3acc8: DecompressPointer r3
    //     0xb3acc8: add             x3, x3, HEAP, lsl #32
    // 0xb3accc: LoadField: r1 = r3->field_b
    //     0xb3accc: ldur            w1, [x3, #0xb]
    // 0xb3acd0: DecompressPointer r1
    //     0xb3acd0: add             x1, x1, HEAP, lsl #32
    // 0xb3acd4: cmp             w1, NULL
    // 0xb3acd8: b.ne            #0xb3ace4
    // 0xb3acdc: r1 = Null
    //     0xb3acdc: mov             x1, NULL
    // 0xb3ace0: b               #0xb3ad04
    // 0xb3ace4: LoadField: r3 = r1->field_43
    //     0xb3ace4: ldur            w3, [x1, #0x43]
    // 0xb3ace8: DecompressPointer r3
    //     0xb3ace8: add             x3, x3, HEAP, lsl #32
    // 0xb3acec: cmp             w3, NULL
    // 0xb3acf0: b.ne            #0xb3acfc
    // 0xb3acf4: r1 = Null
    //     0xb3acf4: mov             x1, NULL
    // 0xb3acf8: b               #0xb3ad04
    // 0xb3acfc: LoadField: r1 = r3->field_13
    //     0xb3acfc: ldur            w1, [x3, #0x13]
    // 0xb3ad00: DecompressPointer r1
    //     0xb3ad00: add             x1, x1, HEAP, lsl #32
    // 0xb3ad04: cmp             w1, NULL
    // 0xb3ad08: b.ne            #0xb3ad14
    // 0xb3ad0c: r6 = ""
    //     0xb3ad0c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3ad10: b               #0xb3ad18
    // 0xb3ad14: mov             x6, x1
    // 0xb3ad18: ldur            x5, [fp, #-0x28]
    // 0xb3ad1c: ldur            x4, [fp, #-0x58]
    // 0xb3ad20: ldur            x3, [fp, #-0x38]
    // 0xb3ad24: ldur            x1, [fp, #-0x10]
    // 0xb3ad28: stur            x6, [fp, #-0x48]
    // 0xb3ad2c: r0 = of()
    //     0xb3ad2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3ad30: LoadField: r1 = r0->field_87
    //     0xb3ad30: ldur            w1, [x0, #0x87]
    // 0xb3ad34: DecompressPointer r1
    //     0xb3ad34: add             x1, x1, HEAP, lsl #32
    // 0xb3ad38: LoadField: r0 = r1->field_2b
    //     0xb3ad38: ldur            w0, [x1, #0x2b]
    // 0xb3ad3c: DecompressPointer r0
    //     0xb3ad3c: add             x0, x0, HEAP, lsl #32
    // 0xb3ad40: r16 = 12.000000
    //     0xb3ad40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3ad44: ldr             x16, [x16, #0x9e8]
    // 0xb3ad48: r30 = Instance_TextDecoration
    //     0xb3ad48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb3ad4c: ldr             lr, [lr, #0xe30]
    // 0xb3ad50: stp             lr, x16, [SP]
    // 0xb3ad54: mov             x1, x0
    // 0xb3ad58: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb3ad58: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb3ad5c: ldr             x4, [x4, #0x698]
    // 0xb3ad60: r0 = copyWith()
    //     0xb3ad60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3ad64: stur            x0, [fp, #-0x60]
    // 0xb3ad68: r0 = Text()
    //     0xb3ad68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3ad6c: mov             x3, x0
    // 0xb3ad70: ldur            x0, [fp, #-0x48]
    // 0xb3ad74: stur            x3, [fp, #-0x68]
    // 0xb3ad78: StoreField: r3->field_b = r0
    //     0xb3ad78: stur            w0, [x3, #0xb]
    // 0xb3ad7c: ldur            x0, [fp, #-0x60]
    // 0xb3ad80: StoreField: r3->field_13 = r0
    //     0xb3ad80: stur            w0, [x3, #0x13]
    // 0xb3ad84: r1 = Null
    //     0xb3ad84: mov             x1, NULL
    // 0xb3ad88: r2 = 6
    //     0xb3ad88: movz            x2, #0x6
    // 0xb3ad8c: r0 = AllocateArray()
    //     0xb3ad8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3ad90: mov             x2, x0
    // 0xb3ad94: ldur            x0, [fp, #-0x50]
    // 0xb3ad98: stur            x2, [fp, #-0x48]
    // 0xb3ad9c: StoreField: r2->field_f = r0
    //     0xb3ad9c: stur            w0, [x2, #0xf]
    // 0xb3ada0: r16 = Instance_SizedBox
    //     0xb3ada0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb3ada4: ldr             x16, [x16, #0xa50]
    // 0xb3ada8: StoreField: r2->field_13 = r16
    //     0xb3ada8: stur            w16, [x2, #0x13]
    // 0xb3adac: ldur            x0, [fp, #-0x68]
    // 0xb3adb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3adb0: stur            w0, [x2, #0x17]
    // 0xb3adb4: r1 = <Widget>
    //     0xb3adb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3adb8: r0 = AllocateGrowableArray()
    //     0xb3adb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3adbc: mov             x1, x0
    // 0xb3adc0: ldur            x0, [fp, #-0x48]
    // 0xb3adc4: stur            x1, [fp, #-0x50]
    // 0xb3adc8: StoreField: r1->field_f = r0
    //     0xb3adc8: stur            w0, [x1, #0xf]
    // 0xb3adcc: r2 = 6
    //     0xb3adcc: movz            x2, #0x6
    // 0xb3add0: StoreField: r1->field_b = r2
    //     0xb3add0: stur            w2, [x1, #0xb]
    // 0xb3add4: r0 = Row()
    //     0xb3add4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3add8: mov             x3, x0
    // 0xb3addc: r0 = Instance_Axis
    //     0xb3addc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3ade0: stur            x3, [fp, #-0x48]
    // 0xb3ade4: StoreField: r3->field_f = r0
    //     0xb3ade4: stur            w0, [x3, #0xf]
    // 0xb3ade8: r4 = Instance_MainAxisAlignment
    //     0xb3ade8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3adec: ldr             x4, [x4, #0xa08]
    // 0xb3adf0: StoreField: r3->field_13 = r4
    //     0xb3adf0: stur            w4, [x3, #0x13]
    // 0xb3adf4: r5 = Instance_MainAxisSize
    //     0xb3adf4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3adf8: ldr             x5, [x5, #0xa10]
    // 0xb3adfc: ArrayStore: r3[0] = r5  ; List_4
    //     0xb3adfc: stur            w5, [x3, #0x17]
    // 0xb3ae00: r6 = Instance_CrossAxisAlignment
    //     0xb3ae00: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3ae04: ldr             x6, [x6, #0xa18]
    // 0xb3ae08: StoreField: r3->field_1b = r6
    //     0xb3ae08: stur            w6, [x3, #0x1b]
    // 0xb3ae0c: r7 = Instance_VerticalDirection
    //     0xb3ae0c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3ae10: ldr             x7, [x7, #0xa20]
    // 0xb3ae14: StoreField: r3->field_23 = r7
    //     0xb3ae14: stur            w7, [x3, #0x23]
    // 0xb3ae18: r8 = Instance_Clip
    //     0xb3ae18: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3ae1c: ldr             x8, [x8, #0x38]
    // 0xb3ae20: StoreField: r3->field_2b = r8
    //     0xb3ae20: stur            w8, [x3, #0x2b]
    // 0xb3ae24: StoreField: r3->field_2f = rZR
    //     0xb3ae24: stur            xzr, [x3, #0x2f]
    // 0xb3ae28: ldur            x1, [fp, #-0x50]
    // 0xb3ae2c: StoreField: r3->field_b = r1
    //     0xb3ae2c: stur            w1, [x3, #0xb]
    // 0xb3ae30: r1 = Null
    //     0xb3ae30: mov             x1, NULL
    // 0xb3ae34: r2 = 6
    //     0xb3ae34: movz            x2, #0x6
    // 0xb3ae38: r0 = AllocateArray()
    //     0xb3ae38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3ae3c: mov             x2, x0
    // 0xb3ae40: ldur            x0, [fp, #-0x38]
    // 0xb3ae44: stur            x2, [fp, #-0x50]
    // 0xb3ae48: StoreField: r2->field_f = r0
    //     0xb3ae48: stur            w0, [x2, #0xf]
    // 0xb3ae4c: r16 = Instance_SizedBox
    //     0xb3ae4c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb3ae50: ldr             x16, [x16, #0xc70]
    // 0xb3ae54: StoreField: r2->field_13 = r16
    //     0xb3ae54: stur            w16, [x2, #0x13]
    // 0xb3ae58: ldur            x0, [fp, #-0x48]
    // 0xb3ae5c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3ae5c: stur            w0, [x2, #0x17]
    // 0xb3ae60: r1 = <Widget>
    //     0xb3ae60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3ae64: r0 = AllocateGrowableArray()
    //     0xb3ae64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3ae68: mov             x1, x0
    // 0xb3ae6c: ldur            x0, [fp, #-0x50]
    // 0xb3ae70: stur            x1, [fp, #-0x38]
    // 0xb3ae74: StoreField: r1->field_f = r0
    //     0xb3ae74: stur            w0, [x1, #0xf]
    // 0xb3ae78: r2 = 6
    //     0xb3ae78: movz            x2, #0x6
    // 0xb3ae7c: StoreField: r1->field_b = r2
    //     0xb3ae7c: stur            w2, [x1, #0xb]
    // 0xb3ae80: r0 = Column()
    //     0xb3ae80: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3ae84: mov             x1, x0
    // 0xb3ae88: r0 = Instance_Axis
    //     0xb3ae88: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3ae8c: stur            x1, [fp, #-0x48]
    // 0xb3ae90: StoreField: r1->field_f = r0
    //     0xb3ae90: stur            w0, [x1, #0xf]
    // 0xb3ae94: r2 = Instance_MainAxisAlignment
    //     0xb3ae94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3ae98: ldr             x2, [x2, #0xa08]
    // 0xb3ae9c: StoreField: r1->field_13 = r2
    //     0xb3ae9c: stur            w2, [x1, #0x13]
    // 0xb3aea0: r3 = Instance_MainAxisSize
    //     0xb3aea0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3aea4: ldr             x3, [x3, #0xa10]
    // 0xb3aea8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3aea8: stur            w3, [x1, #0x17]
    // 0xb3aeac: r4 = Instance_CrossAxisAlignment
    //     0xb3aeac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3aeb0: ldr             x4, [x4, #0x890]
    // 0xb3aeb4: StoreField: r1->field_1b = r4
    //     0xb3aeb4: stur            w4, [x1, #0x1b]
    // 0xb3aeb8: r5 = Instance_VerticalDirection
    //     0xb3aeb8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3aebc: ldr             x5, [x5, #0xa20]
    // 0xb3aec0: StoreField: r1->field_23 = r5
    //     0xb3aec0: stur            w5, [x1, #0x23]
    // 0xb3aec4: r6 = Instance_Clip
    //     0xb3aec4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3aec8: ldr             x6, [x6, #0x38]
    // 0xb3aecc: StoreField: r1->field_2b = r6
    //     0xb3aecc: stur            w6, [x1, #0x2b]
    // 0xb3aed0: StoreField: r1->field_2f = rZR
    //     0xb3aed0: stur            xzr, [x1, #0x2f]
    // 0xb3aed4: ldur            x7, [fp, #-0x38]
    // 0xb3aed8: StoreField: r1->field_b = r7
    //     0xb3aed8: stur            w7, [x1, #0xb]
    // 0xb3aedc: r0 = Padding()
    //     0xb3aedc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3aee0: mov             x2, x0
    // 0xb3aee4: r0 = Instance_EdgeInsets
    //     0xb3aee4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb3aee8: ldr             x0, [x0, #0xa78]
    // 0xb3aeec: stur            x2, [fp, #-0x38]
    // 0xb3aef0: StoreField: r2->field_f = r0
    //     0xb3aef0: stur            w0, [x2, #0xf]
    // 0xb3aef4: ldur            x0, [fp, #-0x48]
    // 0xb3aef8: StoreField: r2->field_b = r0
    //     0xb3aef8: stur            w0, [x2, #0xb]
    // 0xb3aefc: r1 = <FlexParentData>
    //     0xb3aefc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3af00: ldr             x1, [x1, #0xe00]
    // 0xb3af04: r0 = Expanded()
    //     0xb3af04: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3af08: mov             x3, x0
    // 0xb3af0c: r0 = 1
    //     0xb3af0c: movz            x0, #0x1
    // 0xb3af10: stur            x3, [fp, #-0x48]
    // 0xb3af14: StoreField: r3->field_13 = r0
    //     0xb3af14: stur            x0, [x3, #0x13]
    // 0xb3af18: r4 = Instance_FlexFit
    //     0xb3af18: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3af1c: ldr             x4, [x4, #0xe08]
    // 0xb3af20: StoreField: r3->field_1b = r4
    //     0xb3af20: stur            w4, [x3, #0x1b]
    // 0xb3af24: ldur            x1, [fp, #-0x38]
    // 0xb3af28: StoreField: r3->field_b = r1
    //     0xb3af28: stur            w1, [x3, #0xb]
    // 0xb3af2c: r1 = Null
    //     0xb3af2c: mov             x1, NULL
    // 0xb3af30: r2 = 6
    //     0xb3af30: movz            x2, #0x6
    // 0xb3af34: r0 = AllocateArray()
    //     0xb3af34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3af38: mov             x2, x0
    // 0xb3af3c: ldur            x0, [fp, #-0x28]
    // 0xb3af40: stur            x2, [fp, #-0x38]
    // 0xb3af44: StoreField: r2->field_f = r0
    //     0xb3af44: stur            w0, [x2, #0xf]
    // 0xb3af48: ldur            x0, [fp, #-0x58]
    // 0xb3af4c: StoreField: r2->field_13 = r0
    //     0xb3af4c: stur            w0, [x2, #0x13]
    // 0xb3af50: ldur            x0, [fp, #-0x48]
    // 0xb3af54: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3af54: stur            w0, [x2, #0x17]
    // 0xb3af58: r1 = <Widget>
    //     0xb3af58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3af5c: r0 = AllocateGrowableArray()
    //     0xb3af5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3af60: mov             x1, x0
    // 0xb3af64: ldur            x0, [fp, #-0x38]
    // 0xb3af68: stur            x1, [fp, #-0x28]
    // 0xb3af6c: StoreField: r1->field_f = r0
    //     0xb3af6c: stur            w0, [x1, #0xf]
    // 0xb3af70: r2 = 6
    //     0xb3af70: movz            x2, #0x6
    // 0xb3af74: StoreField: r1->field_b = r2
    //     0xb3af74: stur            w2, [x1, #0xb]
    // 0xb3af78: r0 = Row()
    //     0xb3af78: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3af7c: mov             x2, x0
    // 0xb3af80: r0 = Instance_Axis
    //     0xb3af80: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3af84: stur            x2, [fp, #-0x38]
    // 0xb3af88: StoreField: r2->field_f = r0
    //     0xb3af88: stur            w0, [x2, #0xf]
    // 0xb3af8c: r3 = Instance_MainAxisAlignment
    //     0xb3af8c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3af90: ldr             x3, [x3, #0xa08]
    // 0xb3af94: StoreField: r2->field_13 = r3
    //     0xb3af94: stur            w3, [x2, #0x13]
    // 0xb3af98: r4 = Instance_MainAxisSize
    //     0xb3af98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3af9c: ldr             x4, [x4, #0xa10]
    // 0xb3afa0: ArrayStore: r2[0] = r4  ; List_4
    //     0xb3afa0: stur            w4, [x2, #0x17]
    // 0xb3afa4: r1 = Instance_CrossAxisAlignment
    //     0xb3afa4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3afa8: ldr             x1, [x1, #0xa18]
    // 0xb3afac: StoreField: r2->field_1b = r1
    //     0xb3afac: stur            w1, [x2, #0x1b]
    // 0xb3afb0: r5 = Instance_VerticalDirection
    //     0xb3afb0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3afb4: ldr             x5, [x5, #0xa20]
    // 0xb3afb8: StoreField: r2->field_23 = r5
    //     0xb3afb8: stur            w5, [x2, #0x23]
    // 0xb3afbc: r6 = Instance_Clip
    //     0xb3afbc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3afc0: ldr             x6, [x6, #0x38]
    // 0xb3afc4: StoreField: r2->field_2b = r6
    //     0xb3afc4: stur            w6, [x2, #0x2b]
    // 0xb3afc8: StoreField: r2->field_2f = rZR
    //     0xb3afc8: stur            xzr, [x2, #0x2f]
    // 0xb3afcc: ldur            x1, [fp, #-0x28]
    // 0xb3afd0: StoreField: r2->field_b = r1
    //     0xb3afd0: stur            w1, [x2, #0xb]
    // 0xb3afd4: r1 = <FlexParentData>
    //     0xb3afd4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3afd8: ldr             x1, [x1, #0xe00]
    // 0xb3afdc: r0 = Expanded()
    //     0xb3afdc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3afe0: mov             x2, x0
    // 0xb3afe4: r0 = 1
    //     0xb3afe4: movz            x0, #0x1
    // 0xb3afe8: stur            x2, [fp, #-0x28]
    // 0xb3afec: StoreField: r2->field_13 = r0
    //     0xb3afec: stur            x0, [x2, #0x13]
    // 0xb3aff0: r0 = Instance_FlexFit
    //     0xb3aff0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3aff4: ldr             x0, [x0, #0xe08]
    // 0xb3aff8: StoreField: r2->field_1b = r0
    //     0xb3aff8: stur            w0, [x2, #0x1b]
    // 0xb3affc: ldur            x0, [fp, #-0x38]
    // 0xb3b000: StoreField: r2->field_b = r0
    //     0xb3b000: stur            w0, [x2, #0xb]
    // 0xb3b004: ldur            x1, [fp, #-0x10]
    // 0xb3b008: r0 = of()
    //     0xb3b008: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3b00c: LoadField: r1 = r0->field_87
    //     0xb3b00c: ldur            w1, [x0, #0x87]
    // 0xb3b010: DecompressPointer r1
    //     0xb3b010: add             x1, x1, HEAP, lsl #32
    // 0xb3b014: LoadField: r0 = r1->field_7
    //     0xb3b014: ldur            w0, [x1, #7]
    // 0xb3b018: DecompressPointer r0
    //     0xb3b018: add             x0, x0, HEAP, lsl #32
    // 0xb3b01c: r16 = 12.000000
    //     0xb3b01c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3b020: ldr             x16, [x16, #0x9e8]
    // 0xb3b024: r30 = Instance_Color
    //     0xb3b024: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3b028: ldr             lr, [lr, #0x858]
    // 0xb3b02c: stp             lr, x16, [SP]
    // 0xb3b030: mov             x1, x0
    // 0xb3b034: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3b034: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3b038: ldr             x4, [x4, #0xaa0]
    // 0xb3b03c: r0 = copyWith()
    //     0xb3b03c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3b040: stur            x0, [fp, #-0x10]
    // 0xb3b044: r0 = Text()
    //     0xb3b044: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3b048: mov             x1, x0
    // 0xb3b04c: r0 = "Add"
    //     0xb3b04c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a70] "Add"
    //     0xb3b050: ldr             x0, [x0, #0xa70]
    // 0xb3b054: stur            x1, [fp, #-0x38]
    // 0xb3b058: StoreField: r1->field_b = r0
    //     0xb3b058: stur            w0, [x1, #0xb]
    // 0xb3b05c: ldur            x0, [fp, #-0x10]
    // 0xb3b060: StoreField: r1->field_13 = r0
    //     0xb3b060: stur            w0, [x1, #0x13]
    // 0xb3b064: r0 = InkWell()
    //     0xb3b064: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb3b068: mov             x3, x0
    // 0xb3b06c: ldur            x0, [fp, #-0x38]
    // 0xb3b070: stur            x3, [fp, #-0x10]
    // 0xb3b074: StoreField: r3->field_b = r0
    //     0xb3b074: stur            w0, [x3, #0xb]
    // 0xb3b078: ldur            x2, [fp, #-0x18]
    // 0xb3b07c: r1 = Function '<anonymous closure>':.
    //     0xb3b07c: add             x1, PP, #0x57, lsl #12  ; [pp+0x570d0] AnonymousClosure: (0xb3c260), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xb39dd0)
    //     0xb3b080: ldr             x1, [x1, #0xd0]
    // 0xb3b084: r0 = AllocateClosure()
    //     0xb3b084: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3b088: mov             x1, x0
    // 0xb3b08c: ldur            x0, [fp, #-0x10]
    // 0xb3b090: StoreField: r0->field_f = r1
    //     0xb3b090: stur            w1, [x0, #0xf]
    // 0xb3b094: r1 = true
    //     0xb3b094: add             x1, NULL, #0x20  ; true
    // 0xb3b098: StoreField: r0->field_43 = r1
    //     0xb3b098: stur            w1, [x0, #0x43]
    // 0xb3b09c: r2 = Instance_BoxShape
    //     0xb3b09c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3b0a0: ldr             x2, [x2, #0x80]
    // 0xb3b0a4: StoreField: r0->field_47 = r2
    //     0xb3b0a4: stur            w2, [x0, #0x47]
    // 0xb3b0a8: StoreField: r0->field_6f = r1
    //     0xb3b0a8: stur            w1, [x0, #0x6f]
    // 0xb3b0ac: r2 = false
    //     0xb3b0ac: add             x2, NULL, #0x30  ; false
    // 0xb3b0b0: StoreField: r0->field_73 = r2
    //     0xb3b0b0: stur            w2, [x0, #0x73]
    // 0xb3b0b4: StoreField: r0->field_83 = r1
    //     0xb3b0b4: stur            w1, [x0, #0x83]
    // 0xb3b0b8: StoreField: r0->field_7b = r2
    //     0xb3b0b8: stur            w2, [x0, #0x7b]
    // 0xb3b0bc: r0 = Padding()
    //     0xb3b0bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3b0c0: mov             x3, x0
    // 0xb3b0c4: r0 = Instance_EdgeInsets
    //     0xb3b0c4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xb3b0c8: ldr             x0, [x0, #0xa40]
    // 0xb3b0cc: stur            x3, [fp, #-0x38]
    // 0xb3b0d0: StoreField: r3->field_f = r0
    //     0xb3b0d0: stur            w0, [x3, #0xf]
    // 0xb3b0d4: ldur            x0, [fp, #-0x10]
    // 0xb3b0d8: StoreField: r3->field_b = r0
    //     0xb3b0d8: stur            w0, [x3, #0xb]
    // 0xb3b0dc: r1 = Null
    //     0xb3b0dc: mov             x1, NULL
    // 0xb3b0e0: r2 = 4
    //     0xb3b0e0: movz            x2, #0x4
    // 0xb3b0e4: r0 = AllocateArray()
    //     0xb3b0e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b0e8: mov             x2, x0
    // 0xb3b0ec: ldur            x0, [fp, #-0x28]
    // 0xb3b0f0: stur            x2, [fp, #-0x10]
    // 0xb3b0f4: StoreField: r2->field_f = r0
    //     0xb3b0f4: stur            w0, [x2, #0xf]
    // 0xb3b0f8: ldur            x0, [fp, #-0x38]
    // 0xb3b0fc: StoreField: r2->field_13 = r0
    //     0xb3b0fc: stur            w0, [x2, #0x13]
    // 0xb3b100: r1 = <Widget>
    //     0xb3b100: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3b104: r0 = AllocateGrowableArray()
    //     0xb3b104: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3b108: mov             x1, x0
    // 0xb3b10c: ldur            x0, [fp, #-0x10]
    // 0xb3b110: stur            x1, [fp, #-0x28]
    // 0xb3b114: StoreField: r1->field_f = r0
    //     0xb3b114: stur            w0, [x1, #0xf]
    // 0xb3b118: r0 = 4
    //     0xb3b118: movz            x0, #0x4
    // 0xb3b11c: StoreField: r1->field_b = r0
    //     0xb3b11c: stur            w0, [x1, #0xb]
    // 0xb3b120: r0 = Row()
    //     0xb3b120: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3b124: mov             x1, x0
    // 0xb3b128: r0 = Instance_Axis
    //     0xb3b128: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3b12c: stur            x1, [fp, #-0x10]
    // 0xb3b130: StoreField: r1->field_f = r0
    //     0xb3b130: stur            w0, [x1, #0xf]
    // 0xb3b134: r0 = Instance_MainAxisAlignment
    //     0xb3b134: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb3b138: ldr             x0, [x0, #0xa8]
    // 0xb3b13c: StoreField: r1->field_13 = r0
    //     0xb3b13c: stur            w0, [x1, #0x13]
    // 0xb3b140: r0 = Instance_MainAxisSize
    //     0xb3b140: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3b144: ldr             x0, [x0, #0xa10]
    // 0xb3b148: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3b148: stur            w0, [x1, #0x17]
    // 0xb3b14c: r2 = Instance_CrossAxisAlignment
    //     0xb3b14c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3b150: ldr             x2, [x2, #0x890]
    // 0xb3b154: StoreField: r1->field_1b = r2
    //     0xb3b154: stur            w2, [x1, #0x1b]
    // 0xb3b158: r3 = Instance_VerticalDirection
    //     0xb3b158: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3b15c: ldr             x3, [x3, #0xa20]
    // 0xb3b160: StoreField: r1->field_23 = r3
    //     0xb3b160: stur            w3, [x1, #0x23]
    // 0xb3b164: r4 = Instance_Clip
    //     0xb3b164: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3b168: ldr             x4, [x4, #0x38]
    // 0xb3b16c: StoreField: r1->field_2b = r4
    //     0xb3b16c: stur            w4, [x1, #0x2b]
    // 0xb3b170: StoreField: r1->field_2f = rZR
    //     0xb3b170: stur            xzr, [x1, #0x2f]
    // 0xb3b174: ldur            x5, [fp, #-0x28]
    // 0xb3b178: StoreField: r1->field_b = r5
    //     0xb3b178: stur            w5, [x1, #0xb]
    // 0xb3b17c: r0 = Container()
    //     0xb3b17c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb3b180: stur            x0, [fp, #-0x28]
    // 0xb3b184: ldur            x16, [fp, #-0x40]
    // 0xb3b188: ldur            lr, [fp, #-0x10]
    // 0xb3b18c: stp             lr, x16, [SP]
    // 0xb3b190: mov             x1, x0
    // 0xb3b194: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb3b194: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb3b198: ldr             x4, [x4, #0x88]
    // 0xb3b19c: r0 = Container()
    //     0xb3b19c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb3b1a0: r0 = Padding()
    //     0xb3b1a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3b1a4: mov             x1, x0
    // 0xb3b1a8: r0 = Instance_EdgeInsets
    //     0xb3b1a8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb3b1ac: ldr             x0, [x0, #0x778]
    // 0xb3b1b0: StoreField: r1->field_f = r0
    //     0xb3b1b0: stur            w0, [x1, #0xf]
    // 0xb3b1b4: ldur            x0, [fp, #-0x28]
    // 0xb3b1b8: StoreField: r1->field_b = r0
    //     0xb3b1b8: stur            w0, [x1, #0xb]
    // 0xb3b1bc: mov             x2, x1
    // 0xb3b1c0: ldur            x0, [fp, #-8]
    // 0xb3b1c4: ldur            x1, [fp, #-0x20]
    // 0xb3b1c8: stur            x2, [fp, #-0x10]
    // 0xb3b1cc: r0 = Visibility()
    //     0xb3b1cc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb3b1d0: mov             x3, x0
    // 0xb3b1d4: ldur            x0, [fp, #-0x10]
    // 0xb3b1d8: stur            x3, [fp, #-0x28]
    // 0xb3b1dc: StoreField: r3->field_b = r0
    //     0xb3b1dc: stur            w0, [x3, #0xb]
    // 0xb3b1e0: r0 = Instance_SizedBox
    //     0xb3b1e0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb3b1e4: StoreField: r3->field_f = r0
    //     0xb3b1e4: stur            w0, [x3, #0xf]
    // 0xb3b1e8: ldur            x0, [fp, #-0x20]
    // 0xb3b1ec: StoreField: r3->field_13 = r0
    //     0xb3b1ec: stur            w0, [x3, #0x13]
    // 0xb3b1f0: r0 = false
    //     0xb3b1f0: add             x0, NULL, #0x30  ; false
    // 0xb3b1f4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb3b1f4: stur            w0, [x3, #0x17]
    // 0xb3b1f8: StoreField: r3->field_1b = r0
    //     0xb3b1f8: stur            w0, [x3, #0x1b]
    // 0xb3b1fc: StoreField: r3->field_1f = r0
    //     0xb3b1fc: stur            w0, [x3, #0x1f]
    // 0xb3b200: StoreField: r3->field_23 = r0
    //     0xb3b200: stur            w0, [x3, #0x23]
    // 0xb3b204: StoreField: r3->field_27 = r0
    //     0xb3b204: stur            w0, [x3, #0x27]
    // 0xb3b208: StoreField: r3->field_2b = r0
    //     0xb3b208: stur            w0, [x3, #0x2b]
    // 0xb3b20c: ldur            x0, [fp, #-8]
    // 0xb3b210: LoadField: r1 = r0->field_b
    //     0xb3b210: ldur            w1, [x0, #0xb]
    // 0xb3b214: DecompressPointer r1
    //     0xb3b214: add             x1, x1, HEAP, lsl #32
    // 0xb3b218: cmp             w1, NULL
    // 0xb3b21c: b.eq            #0xb3b400
    // 0xb3b220: LoadField: r0 = r1->field_b
    //     0xb3b220: ldur            w0, [x1, #0xb]
    // 0xb3b224: DecompressPointer r0
    //     0xb3b224: add             x0, x0, HEAP, lsl #32
    // 0xb3b228: LoadField: r1 = r0->field_b
    //     0xb3b228: ldur            w1, [x0, #0xb]
    // 0xb3b22c: DecompressPointer r1
    //     0xb3b22c: add             x1, x1, HEAP, lsl #32
    // 0xb3b230: cmp             w1, NULL
    // 0xb3b234: b.ne            #0xb3b240
    // 0xb3b238: r0 = Null
    //     0xb3b238: mov             x0, NULL
    // 0xb3b23c: b               #0xb3b264
    // 0xb3b240: LoadField: r0 = r1->field_f
    //     0xb3b240: ldur            w0, [x1, #0xf]
    // 0xb3b244: DecompressPointer r0
    //     0xb3b244: add             x0, x0, HEAP, lsl #32
    // 0xb3b248: cmp             w0, NULL
    // 0xb3b24c: b.ne            #0xb3b258
    // 0xb3b250: r0 = Null
    //     0xb3b250: mov             x0, NULL
    // 0xb3b254: b               #0xb3b264
    // 0xb3b258: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb3b258: ldur            w1, [x0, #0x17]
    // 0xb3b25c: DecompressPointer r1
    //     0xb3b25c: add             x1, x1, HEAP, lsl #32
    // 0xb3b260: mov             x0, x1
    // 0xb3b264: cmp             w0, NULL
    // 0xb3b268: b.ne            #0xb3b27c
    // 0xb3b26c: r1 = <BEntities>
    //     0xb3b26c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0xb3b270: ldr             x1, [x1, #0x130]
    // 0xb3b274: r2 = 0
    //     0xb3b274: movz            x2, #0
    // 0xb3b278: r0 = AllocateArray()
    //     0xb3b278: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b27c: ldur            x2, [fp, #-0x30]
    // 0xb3b280: ldur            x1, [fp, #-0x28]
    // 0xb3b284: r3 = LoadClassIdInstr(r0)
    //     0xb3b284: ldur            x3, [x0, #-1]
    //     0xb3b288: ubfx            x3, x3, #0xc, #0x14
    // 0xb3b28c: str             x0, [SP]
    // 0xb3b290: mov             x0, x3
    // 0xb3b294: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb3b294: movz            x17, #0xc898
    //     0xb3b298: add             lr, x0, x17
    //     0xb3b29c: ldr             lr, [x21, lr, lsl #3]
    //     0xb3b2a0: blr             lr
    // 0xb3b2a4: r3 = LoadInt32Instr(r0)
    //     0xb3b2a4: sbfx            x3, x0, #1, #0x1f
    // 0xb3b2a8: ldur            x2, [fp, #-0x18]
    // 0xb3b2ac: stur            x3, [fp, #-0x70]
    // 0xb3b2b0: r1 = Function '<anonymous closure>':.
    //     0xb3b2b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x570d8] AnonymousClosure: (0xb3b404), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xb39dd0)
    //     0xb3b2b4: ldr             x1, [x1, #0xd8]
    // 0xb3b2b8: r0 = AllocateClosure()
    //     0xb3b2b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3b2bc: r1 = Function '<anonymous closure>':.
    //     0xb3b2bc: add             x1, PP, #0x57, lsl #12  ; [pp+0x570e0] AnonymousClosure: (0x9fbd20), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xb3b2c0: ldr             x1, [x1, #0xe0]
    // 0xb3b2c4: r2 = Null
    //     0xb3b2c4: mov             x2, NULL
    // 0xb3b2c8: stur            x0, [fp, #-8]
    // 0xb3b2cc: r0 = AllocateClosure()
    //     0xb3b2cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3b2d0: stur            x0, [fp, #-0x10]
    // 0xb3b2d4: r0 = ListView()
    //     0xb3b2d4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb3b2d8: stur            x0, [fp, #-0x18]
    // 0xb3b2dc: r16 = true
    //     0xb3b2dc: add             x16, NULL, #0x20  ; true
    // 0xb3b2e0: r30 = false
    //     0xb3b2e0: add             lr, NULL, #0x30  ; false
    // 0xb3b2e4: stp             lr, x16, [SP, #8]
    // 0xb3b2e8: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb3b2e8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb3b2ec: ldr             x16, [x16, #0x1c8]
    // 0xb3b2f0: str             x16, [SP]
    // 0xb3b2f4: mov             x1, x0
    // 0xb3b2f8: ldur            x2, [fp, #-8]
    // 0xb3b2fc: ldur            x3, [fp, #-0x70]
    // 0xb3b300: ldur            x5, [fp, #-0x10]
    // 0xb3b304: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xb3b304: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb3b308: ldr             x4, [x4, #0x138]
    // 0xb3b30c: r0 = ListView.separated()
    //     0xb3b30c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb3b310: r1 = Null
    //     0xb3b310: mov             x1, NULL
    // 0xb3b314: r2 = 6
    //     0xb3b314: movz            x2, #0x6
    // 0xb3b318: r0 = AllocateArray()
    //     0xb3b318: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b31c: mov             x2, x0
    // 0xb3b320: ldur            x0, [fp, #-0x30]
    // 0xb3b324: stur            x2, [fp, #-8]
    // 0xb3b328: StoreField: r2->field_f = r0
    //     0xb3b328: stur            w0, [x2, #0xf]
    // 0xb3b32c: ldur            x0, [fp, #-0x28]
    // 0xb3b330: StoreField: r2->field_13 = r0
    //     0xb3b330: stur            w0, [x2, #0x13]
    // 0xb3b334: ldur            x0, [fp, #-0x18]
    // 0xb3b338: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3b338: stur            w0, [x2, #0x17]
    // 0xb3b33c: r1 = <Widget>
    //     0xb3b33c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3b340: r0 = AllocateGrowableArray()
    //     0xb3b340: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3b344: mov             x1, x0
    // 0xb3b348: ldur            x0, [fp, #-8]
    // 0xb3b34c: stur            x1, [fp, #-0x10]
    // 0xb3b350: StoreField: r1->field_f = r0
    //     0xb3b350: stur            w0, [x1, #0xf]
    // 0xb3b354: r0 = 6
    //     0xb3b354: movz            x0, #0x6
    // 0xb3b358: StoreField: r1->field_b = r0
    //     0xb3b358: stur            w0, [x1, #0xb]
    // 0xb3b35c: r0 = Column()
    //     0xb3b35c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3b360: mov             x1, x0
    // 0xb3b364: r0 = Instance_Axis
    //     0xb3b364: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3b368: stur            x1, [fp, #-8]
    // 0xb3b36c: StoreField: r1->field_f = r0
    //     0xb3b36c: stur            w0, [x1, #0xf]
    // 0xb3b370: r0 = Instance_MainAxisAlignment
    //     0xb3b370: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3b374: ldr             x0, [x0, #0xa08]
    // 0xb3b378: StoreField: r1->field_13 = r0
    //     0xb3b378: stur            w0, [x1, #0x13]
    // 0xb3b37c: r0 = Instance_MainAxisSize
    //     0xb3b37c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3b380: ldr             x0, [x0, #0xa10]
    // 0xb3b384: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3b384: stur            w0, [x1, #0x17]
    // 0xb3b388: r0 = Instance_CrossAxisAlignment
    //     0xb3b388: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3b38c: ldr             x0, [x0, #0x890]
    // 0xb3b390: StoreField: r1->field_1b = r0
    //     0xb3b390: stur            w0, [x1, #0x1b]
    // 0xb3b394: r0 = Instance_VerticalDirection
    //     0xb3b394: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3b398: ldr             x0, [x0, #0xa20]
    // 0xb3b39c: StoreField: r1->field_23 = r0
    //     0xb3b39c: stur            w0, [x1, #0x23]
    // 0xb3b3a0: r0 = Instance_Clip
    //     0xb3b3a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3b3a4: ldr             x0, [x0, #0x38]
    // 0xb3b3a8: StoreField: r1->field_2b = r0
    //     0xb3b3a8: stur            w0, [x1, #0x2b]
    // 0xb3b3ac: StoreField: r1->field_2f = rZR
    //     0xb3b3ac: stur            xzr, [x1, #0x2f]
    // 0xb3b3b0: ldur            x0, [fp, #-0x10]
    // 0xb3b3b4: StoreField: r1->field_b = r0
    //     0xb3b3b4: stur            w0, [x1, #0xb]
    // 0xb3b3b8: r0 = Padding()
    //     0xb3b3b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3b3bc: r1 = Instance_EdgeInsets
    //     0xb3b3bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb3b3c0: ldr             x1, [x1, #0x668]
    // 0xb3b3c4: StoreField: r0->field_f = r1
    //     0xb3b3c4: stur            w1, [x0, #0xf]
    // 0xb3b3c8: ldur            x1, [fp, #-8]
    // 0xb3b3cc: StoreField: r0->field_b = r1
    //     0xb3b3cc: stur            w1, [x0, #0xb]
    // 0xb3b3d0: LeaveFrame
    //     0xb3b3d0: mov             SP, fp
    //     0xb3b3d4: ldp             fp, lr, [SP], #0x10
    // 0xb3b3d8: ret
    //     0xb3b3d8: ret             
    // 0xb3b3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3b3dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3b3e0: b               #0xb39df8
    // 0xb3b3e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b3e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b3ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b3f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b3f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b3f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b3fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b3fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b400: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb3b404, size: 0x18c
    // 0xb3b404: EnterFrame
    //     0xb3b404: stp             fp, lr, [SP, #-0x10]!
    //     0xb3b408: mov             fp, SP
    // 0xb3b40c: AllocStack(0x18)
    //     0xb3b40c: sub             SP, SP, #0x18
    // 0xb3b410: SetupParameters()
    //     0xb3b410: ldr             x0, [fp, #0x20]
    //     0xb3b414: ldur            w1, [x0, #0x17]
    //     0xb3b418: add             x1, x1, HEAP, lsl #32
    //     0xb3b41c: stur            x1, [fp, #-8]
    // 0xb3b420: CheckStackOverflow
    //     0xb3b420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3b424: cmp             SP, x16
    //     0xb3b428: b.ls            #0xb3b580
    // 0xb3b42c: r1 = 1
    //     0xb3b42c: movz            x1, #0x1
    // 0xb3b430: r0 = AllocateContext()
    //     0xb3b430: bl              #0x16f6108  ; AllocateContextStub
    // 0xb3b434: mov             x2, x0
    // 0xb3b438: ldur            x0, [fp, #-8]
    // 0xb3b43c: stur            x2, [fp, #-0x18]
    // 0xb3b440: StoreField: r2->field_b = r0
    //     0xb3b440: stur            w0, [x2, #0xb]
    // 0xb3b444: ldr             x1, [fp, #0x10]
    // 0xb3b448: StoreField: r2->field_f = r1
    //     0xb3b448: stur            w1, [x2, #0xf]
    // 0xb3b44c: LoadField: r3 = r0->field_f
    //     0xb3b44c: ldur            w3, [x0, #0xf]
    // 0xb3b450: DecompressPointer r3
    //     0xb3b450: add             x3, x3, HEAP, lsl #32
    // 0xb3b454: stur            x3, [fp, #-0x10]
    // 0xb3b458: LoadField: r0 = r3->field_b
    //     0xb3b458: ldur            w0, [x3, #0xb]
    // 0xb3b45c: DecompressPointer r0
    //     0xb3b45c: add             x0, x0, HEAP, lsl #32
    // 0xb3b460: cmp             w0, NULL
    // 0xb3b464: b.eq            #0xb3b588
    // 0xb3b468: LoadField: r4 = r0->field_b
    //     0xb3b468: ldur            w4, [x0, #0xb]
    // 0xb3b46c: DecompressPointer r4
    //     0xb3b46c: add             x4, x4, HEAP, lsl #32
    // 0xb3b470: LoadField: r0 = r4->field_b
    //     0xb3b470: ldur            w0, [x4, #0xb]
    // 0xb3b474: DecompressPointer r0
    //     0xb3b474: add             x0, x0, HEAP, lsl #32
    // 0xb3b478: cmp             w0, NULL
    // 0xb3b47c: b.ne            #0xb3b488
    // 0xb3b480: r0 = Null
    //     0xb3b480: mov             x0, NULL
    // 0xb3b484: b               #0xb3b4f4
    // 0xb3b488: LoadField: r4 = r0->field_f
    //     0xb3b488: ldur            w4, [x0, #0xf]
    // 0xb3b48c: DecompressPointer r4
    //     0xb3b48c: add             x4, x4, HEAP, lsl #32
    // 0xb3b490: cmp             w4, NULL
    // 0xb3b494: b.ne            #0xb3b4a0
    // 0xb3b498: r0 = Null
    //     0xb3b498: mov             x0, NULL
    // 0xb3b49c: b               #0xb3b4f4
    // 0xb3b4a0: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xb3b4a0: ldur            w5, [x4, #0x17]
    // 0xb3b4a4: DecompressPointer r5
    //     0xb3b4a4: add             x5, x5, HEAP, lsl #32
    // 0xb3b4a8: cmp             w5, NULL
    // 0xb3b4ac: b.ne            #0xb3b4b8
    // 0xb3b4b0: r0 = Null
    //     0xb3b4b0: mov             x0, NULL
    // 0xb3b4b4: b               #0xb3b4f4
    // 0xb3b4b8: LoadField: r0 = r5->field_b
    //     0xb3b4b8: ldur            w0, [x5, #0xb]
    // 0xb3b4bc: r4 = LoadInt32Instr(r1)
    //     0xb3b4bc: sbfx            x4, x1, #1, #0x1f
    //     0xb3b4c0: tbz             w1, #0, #0xb3b4c8
    //     0xb3b4c4: ldur            x4, [x1, #7]
    // 0xb3b4c8: r1 = LoadInt32Instr(r0)
    //     0xb3b4c8: sbfx            x1, x0, #1, #0x1f
    // 0xb3b4cc: mov             x0, x1
    // 0xb3b4d0: mov             x1, x4
    // 0xb3b4d4: cmp             x1, x0
    // 0xb3b4d8: b.hs            #0xb3b58c
    // 0xb3b4dc: LoadField: r0 = r5->field_f
    //     0xb3b4dc: ldur            w0, [x5, #0xf]
    // 0xb3b4e0: DecompressPointer r0
    //     0xb3b4e0: add             x0, x0, HEAP, lsl #32
    // 0xb3b4e4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb3b4e4: add             x16, x0, x4, lsl #2
    //     0xb3b4e8: ldur            w1, [x16, #0xf]
    // 0xb3b4ec: DecompressPointer r1
    //     0xb3b4ec: add             x1, x1, HEAP, lsl #32
    // 0xb3b4f0: mov             x0, x1
    // 0xb3b4f4: cmp             w0, NULL
    // 0xb3b4f8: b.ne            #0xb3b508
    // 0xb3b4fc: r0 = BEntities()
    //     0xb3b4fc: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xb3b500: mov             x2, x0
    // 0xb3b504: b               #0xb3b50c
    // 0xb3b508: mov             x2, x0
    // 0xb3b50c: ldur            x1, [fp, #-0x10]
    // 0xb3b510: ldr             x3, [fp, #0x18]
    // 0xb3b514: r0 = glassThemeBagItem()
    //     0xb3b514: bl              #0xb3b590  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::glassThemeBagItem
    // 0xb3b518: stur            x0, [fp, #-8]
    // 0xb3b51c: r0 = InkWell()
    //     0xb3b51c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb3b520: mov             x3, x0
    // 0xb3b524: ldur            x0, [fp, #-8]
    // 0xb3b528: stur            x3, [fp, #-0x10]
    // 0xb3b52c: StoreField: r3->field_b = r0
    //     0xb3b52c: stur            w0, [x3, #0xb]
    // 0xb3b530: ldur            x2, [fp, #-0x18]
    // 0xb3b534: r1 = Function '<anonymous closure>':.
    //     0xb3b534: add             x1, PP, #0x57, lsl #12  ; [pp+0x570e8] AnonymousClosure: (0xb3bf54), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xb39dd0)
    //     0xb3b538: ldr             x1, [x1, #0xe8]
    // 0xb3b53c: r0 = AllocateClosure()
    //     0xb3b53c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3b540: mov             x1, x0
    // 0xb3b544: ldur            x0, [fp, #-0x10]
    // 0xb3b548: StoreField: r0->field_f = r1
    //     0xb3b548: stur            w1, [x0, #0xf]
    // 0xb3b54c: r1 = true
    //     0xb3b54c: add             x1, NULL, #0x20  ; true
    // 0xb3b550: StoreField: r0->field_43 = r1
    //     0xb3b550: stur            w1, [x0, #0x43]
    // 0xb3b554: r2 = Instance_BoxShape
    //     0xb3b554: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3b558: ldr             x2, [x2, #0x80]
    // 0xb3b55c: StoreField: r0->field_47 = r2
    //     0xb3b55c: stur            w2, [x0, #0x47]
    // 0xb3b560: StoreField: r0->field_6f = r1
    //     0xb3b560: stur            w1, [x0, #0x6f]
    // 0xb3b564: r2 = false
    //     0xb3b564: add             x2, NULL, #0x30  ; false
    // 0xb3b568: StoreField: r0->field_73 = r2
    //     0xb3b568: stur            w2, [x0, #0x73]
    // 0xb3b56c: StoreField: r0->field_83 = r1
    //     0xb3b56c: stur            w1, [x0, #0x83]
    // 0xb3b570: StoreField: r0->field_7b = r2
    //     0xb3b570: stur            w2, [x0, #0x7b]
    // 0xb3b574: LeaveFrame
    //     0xb3b574: mov             SP, fp
    //     0xb3b578: ldp             fp, lr, [SP], #0x10
    // 0xb3b57c: ret
    //     0xb3b57c: ret             
    // 0xb3b580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3b580: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3b584: b               #0xb3b42c
    // 0xb3b588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b588: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b58c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3b58c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ glassThemeBagItem(/* No info */) {
    // ** addr: 0xb3b590, size: 0x9c4
    // 0xb3b590: EnterFrame
    //     0xb3b590: stp             fp, lr, [SP, #-0x10]!
    //     0xb3b594: mov             fp, SP
    // 0xb3b598: AllocStack(0x78)
    //     0xb3b598: sub             SP, SP, #0x78
    // 0xb3b59c: SetupParameters(_BagDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb3b59c: mov             x0, x1
    //     0xb3b5a0: stur            x1, [fp, #-8]
    //     0xb3b5a4: mov             x1, x3
    //     0xb3b5a8: stur            x2, [fp, #-0x10]
    //     0xb3b5ac: stur            x3, [fp, #-0x18]
    // 0xb3b5b0: CheckStackOverflow
    //     0xb3b5b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3b5b4: cmp             SP, x16
    //     0xb3b5b8: b.ls            #0xb3bf44
    // 0xb3b5bc: r0 = Radius()
    //     0xb3b5bc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3b5c0: d0 = 12.000000
    //     0xb3b5c0: fmov            d0, #12.00000000
    // 0xb3b5c4: stur            x0, [fp, #-0x20]
    // 0xb3b5c8: StoreField: r0->field_7 = d0
    //     0xb3b5c8: stur            d0, [x0, #7]
    // 0xb3b5cc: StoreField: r0->field_f = d0
    //     0xb3b5cc: stur            d0, [x0, #0xf]
    // 0xb3b5d0: r0 = BorderRadius()
    //     0xb3b5d0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3b5d4: mov             x3, x0
    // 0xb3b5d8: ldur            x0, [fp, #-0x20]
    // 0xb3b5dc: stur            x3, [fp, #-0x28]
    // 0xb3b5e0: StoreField: r3->field_7 = r0
    //     0xb3b5e0: stur            w0, [x3, #7]
    // 0xb3b5e4: StoreField: r3->field_b = r0
    //     0xb3b5e4: stur            w0, [x3, #0xb]
    // 0xb3b5e8: StoreField: r3->field_f = r0
    //     0xb3b5e8: stur            w0, [x3, #0xf]
    // 0xb3b5ec: StoreField: r3->field_13 = r0
    //     0xb3b5ec: stur            w0, [x3, #0x13]
    // 0xb3b5f0: ldur            x0, [fp, #-0x10]
    // 0xb3b5f4: LoadField: r1 = r0->field_13
    //     0xb3b5f4: ldur            w1, [x0, #0x13]
    // 0xb3b5f8: DecompressPointer r1
    //     0xb3b5f8: add             x1, x1, HEAP, lsl #32
    // 0xb3b5fc: cmp             w1, NULL
    // 0xb3b600: b.ne            #0xb3b60c
    // 0xb3b604: r4 = ""
    //     0xb3b604: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3b608: b               #0xb3b610
    // 0xb3b60c: mov             x4, x1
    // 0xb3b610: stur            x4, [fp, #-0x20]
    // 0xb3b614: r1 = Function '<anonymous closure>':.
    //     0xb3b614: add             x1, PP, #0x57, lsl #12  ; [pp+0x57110] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3b618: ldr             x1, [x1, #0x110]
    // 0xb3b61c: r2 = Null
    //     0xb3b61c: mov             x2, NULL
    // 0xb3b620: r0 = AllocateClosure()
    //     0xb3b620: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3b624: r1 = Function '<anonymous closure>':.
    //     0xb3b624: add             x1, PP, #0x57, lsl #12  ; [pp+0x57118] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb3b628: ldr             x1, [x1, #0x118]
    // 0xb3b62c: r2 = Null
    //     0xb3b62c: mov             x2, NULL
    // 0xb3b630: stur            x0, [fp, #-0x30]
    // 0xb3b634: r0 = AllocateClosure()
    //     0xb3b634: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3b638: stur            x0, [fp, #-0x38]
    // 0xb3b63c: r0 = CachedNetworkImage()
    //     0xb3b63c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb3b640: stur            x0, [fp, #-0x40]
    // 0xb3b644: r16 = 56.000000
    //     0xb3b644: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3b648: ldr             x16, [x16, #0xb78]
    // 0xb3b64c: r30 = 56.000000
    //     0xb3b64c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb3b650: ldr             lr, [lr, #0xb78]
    // 0xb3b654: stp             lr, x16, [SP, #0x18]
    // 0xb3b658: r16 = Instance_BoxFit
    //     0xb3b658: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb3b65c: ldr             x16, [x16, #0x118]
    // 0xb3b660: ldur            lr, [fp, #-0x30]
    // 0xb3b664: stp             lr, x16, [SP, #8]
    // 0xb3b668: ldur            x16, [fp, #-0x38]
    // 0xb3b66c: str             x16, [SP]
    // 0xb3b670: mov             x1, x0
    // 0xb3b674: ldur            x2, [fp, #-0x20]
    // 0xb3b678: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xb3b678: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xb3b67c: ldr             x4, [x4, #0x710]
    // 0xb3b680: r0 = CachedNetworkImage()
    //     0xb3b680: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb3b684: r0 = ClipRRect()
    //     0xb3b684: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb3b688: mov             x2, x0
    // 0xb3b68c: ldur            x0, [fp, #-0x28]
    // 0xb3b690: stur            x2, [fp, #-0x30]
    // 0xb3b694: StoreField: r2->field_f = r0
    //     0xb3b694: stur            w0, [x2, #0xf]
    // 0xb3b698: r0 = Instance_Clip
    //     0xb3b698: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb3b69c: ldr             x0, [x0, #0x138]
    // 0xb3b6a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3b6a0: stur            w0, [x2, #0x17]
    // 0xb3b6a4: ldur            x0, [fp, #-0x40]
    // 0xb3b6a8: StoreField: r2->field_b = r0
    //     0xb3b6a8: stur            w0, [x2, #0xb]
    // 0xb3b6ac: ldur            x0, [fp, #-0x10]
    // 0xb3b6b0: LoadField: r1 = r0->field_f
    //     0xb3b6b0: ldur            w1, [x0, #0xf]
    // 0xb3b6b4: DecompressPointer r1
    //     0xb3b6b4: add             x1, x1, HEAP, lsl #32
    // 0xb3b6b8: cmp             w1, NULL
    // 0xb3b6bc: b.ne            #0xb3b6c8
    // 0xb3b6c0: r3 = ""
    //     0xb3b6c0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3b6c4: b               #0xb3b6cc
    // 0xb3b6c8: mov             x3, x1
    // 0xb3b6cc: ldur            x1, [fp, #-0x18]
    // 0xb3b6d0: stur            x3, [fp, #-0x20]
    // 0xb3b6d4: r0 = of()
    //     0xb3b6d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3b6d8: LoadField: r1 = r0->field_87
    //     0xb3b6d8: ldur            w1, [x0, #0x87]
    // 0xb3b6dc: DecompressPointer r1
    //     0xb3b6dc: add             x1, x1, HEAP, lsl #32
    // 0xb3b6e0: LoadField: r0 = r1->field_7
    //     0xb3b6e0: ldur            w0, [x1, #7]
    // 0xb3b6e4: DecompressPointer r0
    //     0xb3b6e4: add             x0, x0, HEAP, lsl #32
    // 0xb3b6e8: r16 = 12.000000
    //     0xb3b6e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3b6ec: ldr             x16, [x16, #0x9e8]
    // 0xb3b6f0: r30 = Instance_Color
    //     0xb3b6f0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3b6f4: stp             lr, x16, [SP]
    // 0xb3b6f8: mov             x1, x0
    // 0xb3b6fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3b6fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3b700: ldr             x4, [x4, #0xaa0]
    // 0xb3b704: r0 = copyWith()
    //     0xb3b704: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3b708: stur            x0, [fp, #-0x28]
    // 0xb3b70c: r0 = Text()
    //     0xb3b70c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3b710: mov             x1, x0
    // 0xb3b714: ldur            x0, [fp, #-0x20]
    // 0xb3b718: stur            x1, [fp, #-0x38]
    // 0xb3b71c: StoreField: r1->field_b = r0
    //     0xb3b71c: stur            w0, [x1, #0xb]
    // 0xb3b720: ldur            x0, [fp, #-0x28]
    // 0xb3b724: StoreField: r1->field_13 = r0
    //     0xb3b724: stur            w0, [x1, #0x13]
    // 0xb3b728: r0 = Instance_TextOverflow
    //     0xb3b728: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb3b72c: ldr             x0, [x0, #0xe10]
    // 0xb3b730: StoreField: r1->field_2b = r0
    //     0xb3b730: stur            w0, [x1, #0x2b]
    // 0xb3b734: r2 = 2
    //     0xb3b734: movz            x2, #0x2
    // 0xb3b738: StoreField: r1->field_37 = r2
    //     0xb3b738: stur            w2, [x1, #0x37]
    // 0xb3b73c: ldur            x3, [fp, #-0x10]
    // 0xb3b740: LoadField: r0 = r3->field_57
    //     0xb3b740: ldur            w0, [x3, #0x57]
    // 0xb3b744: DecompressPointer r0
    //     0xb3b744: add             x0, x0, HEAP, lsl #32
    // 0xb3b748: r4 = LoadClassIdInstr(r0)
    //     0xb3b748: ldur            x4, [x0, #-1]
    //     0xb3b74c: ubfx            x4, x4, #0xc, #0x14
    // 0xb3b750: r16 = "size"
    //     0xb3b750: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb3b754: ldr             x16, [x16, #0x9c0]
    // 0xb3b758: stp             x16, x0, [SP]
    // 0xb3b75c: mov             x0, x4
    // 0xb3b760: mov             lr, x0
    // 0xb3b764: ldr             lr, [x21, lr, lsl #3]
    // 0xb3b768: blr             lr
    // 0xb3b76c: tbnz            w0, #4, #0xb3b7c4
    // 0xb3b770: ldur            x0, [fp, #-0x10]
    // 0xb3b774: r1 = Null
    //     0xb3b774: mov             x1, NULL
    // 0xb3b778: r2 = 8
    //     0xb3b778: movz            x2, #0x8
    // 0xb3b77c: r0 = AllocateArray()
    //     0xb3b77c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b780: r16 = "Size: "
    //     0xb3b780: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb3b784: ldr             x16, [x16, #0xf00]
    // 0xb3b788: StoreField: r0->field_f = r16
    //     0xb3b788: stur            w16, [x0, #0xf]
    // 0xb3b78c: ldur            x1, [fp, #-0x10]
    // 0xb3b790: LoadField: r2 = r1->field_2f
    //     0xb3b790: ldur            w2, [x1, #0x2f]
    // 0xb3b794: DecompressPointer r2
    //     0xb3b794: add             x2, x2, HEAP, lsl #32
    // 0xb3b798: StoreField: r0->field_13 = r2
    //     0xb3b798: stur            w2, [x0, #0x13]
    // 0xb3b79c: r16 = " / Qty: "
    //     0xb3b79c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb3b7a0: ldr             x16, [x16, #0x760]
    // 0xb3b7a4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb3b7a4: stur            w16, [x0, #0x17]
    // 0xb3b7a8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb3b7a8: ldur            w2, [x1, #0x17]
    // 0xb3b7ac: DecompressPointer r2
    //     0xb3b7ac: add             x2, x2, HEAP, lsl #32
    // 0xb3b7b0: StoreField: r0->field_1b = r2
    //     0xb3b7b0: stur            w2, [x0, #0x1b]
    // 0xb3b7b4: str             x0, [SP]
    // 0xb3b7b8: r0 = _interpolate()
    //     0xb3b7b8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb3b7bc: mov             x3, x0
    // 0xb3b7c0: b               #0xb3b814
    // 0xb3b7c4: ldur            x0, [fp, #-0x10]
    // 0xb3b7c8: r1 = Null
    //     0xb3b7c8: mov             x1, NULL
    // 0xb3b7cc: r2 = 8
    //     0xb3b7cc: movz            x2, #0x8
    // 0xb3b7d0: r0 = AllocateArray()
    //     0xb3b7d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b7d4: r16 = "Variant: "
    //     0xb3b7d4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xb3b7d8: ldr             x16, [x16, #0xf08]
    // 0xb3b7dc: StoreField: r0->field_f = r16
    //     0xb3b7dc: stur            w16, [x0, #0xf]
    // 0xb3b7e0: ldur            x1, [fp, #-0x10]
    // 0xb3b7e4: LoadField: r2 = r1->field_2f
    //     0xb3b7e4: ldur            w2, [x1, #0x2f]
    // 0xb3b7e8: DecompressPointer r2
    //     0xb3b7e8: add             x2, x2, HEAP, lsl #32
    // 0xb3b7ec: StoreField: r0->field_13 = r2
    //     0xb3b7ec: stur            w2, [x0, #0x13]
    // 0xb3b7f0: r16 = " / Qty: "
    //     0xb3b7f0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb3b7f4: ldr             x16, [x16, #0x760]
    // 0xb3b7f8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb3b7f8: stur            w16, [x0, #0x17]
    // 0xb3b7fc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb3b7fc: ldur            w2, [x1, #0x17]
    // 0xb3b800: DecompressPointer r2
    //     0xb3b800: add             x2, x2, HEAP, lsl #32
    // 0xb3b804: StoreField: r0->field_1b = r2
    //     0xb3b804: stur            w2, [x0, #0x1b]
    // 0xb3b808: str             x0, [SP]
    // 0xb3b80c: r0 = _interpolate()
    //     0xb3b80c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb3b810: mov             x3, x0
    // 0xb3b814: ldur            x2, [fp, #-8]
    // 0xb3b818: ldur            x0, [fp, #-0x10]
    // 0xb3b81c: ldur            x1, [fp, #-0x18]
    // 0xb3b820: stur            x3, [fp, #-0x20]
    // 0xb3b824: r0 = of()
    //     0xb3b824: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3b828: LoadField: r1 = r0->field_87
    //     0xb3b828: ldur            w1, [x0, #0x87]
    // 0xb3b82c: DecompressPointer r1
    //     0xb3b82c: add             x1, x1, HEAP, lsl #32
    // 0xb3b830: LoadField: r0 = r1->field_2b
    //     0xb3b830: ldur            w0, [x1, #0x2b]
    // 0xb3b834: DecompressPointer r0
    //     0xb3b834: add             x0, x0, HEAP, lsl #32
    // 0xb3b838: stur            x0, [fp, #-0x28]
    // 0xb3b83c: r1 = Instance_Color
    //     0xb3b83c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3b840: d0 = 0.700000
    //     0xb3b840: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb3b844: ldr             d0, [x17, #0xf48]
    // 0xb3b848: r0 = withOpacity()
    //     0xb3b848: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3b84c: r16 = 12.000000
    //     0xb3b84c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3b850: ldr             x16, [x16, #0x9e8]
    // 0xb3b854: stp             x0, x16, [SP]
    // 0xb3b858: ldur            x1, [fp, #-0x28]
    // 0xb3b85c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3b85c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3b860: ldr             x4, [x4, #0xaa0]
    // 0xb3b864: r0 = copyWith()
    //     0xb3b864: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3b868: stur            x0, [fp, #-0x28]
    // 0xb3b86c: r0 = Text()
    //     0xb3b86c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3b870: mov             x3, x0
    // 0xb3b874: ldur            x0, [fp, #-0x20]
    // 0xb3b878: stur            x3, [fp, #-0x40]
    // 0xb3b87c: StoreField: r3->field_b = r0
    //     0xb3b87c: stur            w0, [x3, #0xb]
    // 0xb3b880: ldur            x0, [fp, #-0x28]
    // 0xb3b884: StoreField: r3->field_13 = r0
    //     0xb3b884: stur            w0, [x3, #0x13]
    // 0xb3b888: ldur            x0, [fp, #-0x10]
    // 0xb3b88c: LoadField: r4 = r0->field_2b
    //     0xb3b88c: ldur            w4, [x0, #0x2b]
    // 0xb3b890: DecompressPointer r4
    //     0xb3b890: add             x4, x4, HEAP, lsl #32
    // 0xb3b894: stur            x4, [fp, #-0x20]
    // 0xb3b898: r1 = Null
    //     0xb3b898: mov             x1, NULL
    // 0xb3b89c: r2 = 4
    //     0xb3b89c: movz            x2, #0x4
    // 0xb3b8a0: r0 = AllocateArray()
    //     0xb3b8a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b8a4: mov             x1, x0
    // 0xb3b8a8: ldur            x0, [fp, #-0x20]
    // 0xb3b8ac: StoreField: r1->field_f = r0
    //     0xb3b8ac: stur            w0, [x1, #0xf]
    // 0xb3b8b0: r16 = " "
    //     0xb3b8b0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb3b8b4: StoreField: r1->field_13 = r16
    //     0xb3b8b4: stur            w16, [x1, #0x13]
    // 0xb3b8b8: str             x1, [SP]
    // 0xb3b8bc: r0 = _interpolate()
    //     0xb3b8bc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb3b8c0: ldur            x1, [fp, #-0x18]
    // 0xb3b8c4: stur            x0, [fp, #-0x20]
    // 0xb3b8c8: r0 = of()
    //     0xb3b8c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3b8cc: LoadField: r1 = r0->field_87
    //     0xb3b8cc: ldur            w1, [x0, #0x87]
    // 0xb3b8d0: DecompressPointer r1
    //     0xb3b8d0: add             x1, x1, HEAP, lsl #32
    // 0xb3b8d4: LoadField: r0 = r1->field_7
    //     0xb3b8d4: ldur            w0, [x1, #7]
    // 0xb3b8d8: DecompressPointer r0
    //     0xb3b8d8: add             x0, x0, HEAP, lsl #32
    // 0xb3b8dc: r16 = 12.000000
    //     0xb3b8dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3b8e0: ldr             x16, [x16, #0x9e8]
    // 0xb3b8e4: r30 = Instance_Color
    //     0xb3b8e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3b8e8: stp             lr, x16, [SP]
    // 0xb3b8ec: mov             x1, x0
    // 0xb3b8f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3b8f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3b8f4: ldr             x4, [x4, #0xaa0]
    // 0xb3b8f8: r0 = copyWith()
    //     0xb3b8f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3b8fc: stur            x0, [fp, #-0x28]
    // 0xb3b900: r0 = TextSpan()
    //     0xb3b900: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb3b904: mov             x3, x0
    // 0xb3b908: ldur            x0, [fp, #-0x20]
    // 0xb3b90c: stur            x3, [fp, #-0x48]
    // 0xb3b910: StoreField: r3->field_b = r0
    //     0xb3b910: stur            w0, [x3, #0xb]
    // 0xb3b914: r0 = Instance__DeferringMouseCursor
    //     0xb3b914: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb3b918: ArrayStore: r3[0] = r0  ; List_4
    //     0xb3b918: stur            w0, [x3, #0x17]
    // 0xb3b91c: ldur            x1, [fp, #-0x28]
    // 0xb3b920: StoreField: r3->field_7 = r1
    //     0xb3b920: stur            w1, [x3, #7]
    // 0xb3b924: r1 = Null
    //     0xb3b924: mov             x1, NULL
    // 0xb3b928: r2 = 2
    //     0xb3b928: movz            x2, #0x2
    // 0xb3b92c: r0 = AllocateArray()
    //     0xb3b92c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3b930: mov             x2, x0
    // 0xb3b934: ldur            x0, [fp, #-0x48]
    // 0xb3b938: stur            x2, [fp, #-0x20]
    // 0xb3b93c: StoreField: r2->field_f = r0
    //     0xb3b93c: stur            w0, [x2, #0xf]
    // 0xb3b940: r1 = <TextSpan>
    //     0xb3b940: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xb3b944: ldr             x1, [x1, #0x940]
    // 0xb3b948: r0 = AllocateGrowableArray()
    //     0xb3b948: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3b94c: mov             x1, x0
    // 0xb3b950: ldur            x0, [fp, #-0x20]
    // 0xb3b954: stur            x1, [fp, #-0x28]
    // 0xb3b958: StoreField: r1->field_f = r0
    //     0xb3b958: stur            w0, [x1, #0xf]
    // 0xb3b95c: r2 = 2
    //     0xb3b95c: movz            x2, #0x2
    // 0xb3b960: StoreField: r1->field_b = r2
    //     0xb3b960: stur            w2, [x1, #0xb]
    // 0xb3b964: r0 = TextSpan()
    //     0xb3b964: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb3b968: mov             x1, x0
    // 0xb3b96c: ldur            x0, [fp, #-0x28]
    // 0xb3b970: stur            x1, [fp, #-0x20]
    // 0xb3b974: StoreField: r1->field_f = r0
    //     0xb3b974: stur            w0, [x1, #0xf]
    // 0xb3b978: r0 = Instance__DeferringMouseCursor
    //     0xb3b978: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb3b97c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3b97c: stur            w0, [x1, #0x17]
    // 0xb3b980: r0 = RichText()
    //     0xb3b980: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb3b984: mov             x1, x0
    // 0xb3b988: ldur            x2, [fp, #-0x20]
    // 0xb3b98c: stur            x0, [fp, #-0x20]
    // 0xb3b990: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb3b990: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb3b994: r0 = RichText()
    //     0xb3b994: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb3b998: ldur            x0, [fp, #-8]
    // 0xb3b99c: LoadField: r1 = r0->field_b
    //     0xb3b99c: ldur            w1, [x0, #0xb]
    // 0xb3b9a0: DecompressPointer r1
    //     0xb3b9a0: add             x1, x1, HEAP, lsl #32
    // 0xb3b9a4: cmp             w1, NULL
    // 0xb3b9a8: b.eq            #0xb3bf4c
    // 0xb3b9ac: ldur            x2, [fp, #-0x10]
    // 0xb3b9b0: LoadField: r1 = r2->field_3f
    //     0xb3b9b0: ldur            w1, [x2, #0x3f]
    // 0xb3b9b4: DecompressPointer r1
    //     0xb3b9b4: add             x1, x1, HEAP, lsl #32
    // 0xb3b9b8: cmp             w1, NULL
    // 0xb3b9bc: b.ne            #0xb3b9c8
    // 0xb3b9c0: r1 = Null
    //     0xb3b9c0: mov             x1, NULL
    // 0xb3b9c4: b               #0xb3b9dc
    // 0xb3b9c8: LoadField: r3 = r1->field_b
    //     0xb3b9c8: ldur            w3, [x1, #0xb]
    // 0xb3b9cc: cbnz            w3, #0xb3b9d8
    // 0xb3b9d0: r1 = false
    //     0xb3b9d0: add             x1, NULL, #0x30  ; false
    // 0xb3b9d4: b               #0xb3b9dc
    // 0xb3b9d8: r1 = true
    //     0xb3b9d8: add             x1, NULL, #0x20  ; true
    // 0xb3b9dc: cmp             w1, NULL
    // 0xb3b9e0: b.eq            #0xb3ba1c
    // 0xb3b9e4: tbnz            w1, #4, #0xb3ba1c
    // 0xb3b9e8: ldur            x1, [fp, #-0x18]
    // 0xb3b9ec: r0 = of()
    //     0xb3b9ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3b9f0: LoadField: r1 = r0->field_5b
    //     0xb3b9f0: ldur            w1, [x0, #0x5b]
    // 0xb3b9f4: DecompressPointer r1
    //     0xb3b9f4: add             x1, x1, HEAP, lsl #32
    // 0xb3b9f8: r0 = LoadClassIdInstr(r1)
    //     0xb3b9f8: ldur            x0, [x1, #-1]
    //     0xb3b9fc: ubfx            x0, x0, #0xc, #0x14
    // 0xb3ba00: d0 = 0.030000
    //     0xb3ba00: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb3ba04: ldr             d0, [x17, #0x238]
    // 0xb3ba08: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb3ba08: sub             lr, x0, #0xffa
    //     0xb3ba0c: ldr             lr, [x21, lr, lsl #3]
    //     0xb3ba10: blr             lr
    // 0xb3ba14: mov             x2, x0
    // 0xb3ba18: b               #0xb3ba24
    // 0xb3ba1c: r2 = Instance_Color
    //     0xb3ba1c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb3ba20: ldr             x2, [x2, #0xf88]
    // 0xb3ba24: ldur            x0, [fp, #-8]
    // 0xb3ba28: ldur            x1, [fp, #-0x10]
    // 0xb3ba2c: stur            x2, [fp, #-0x28]
    // 0xb3ba30: r0 = Radius()
    //     0xb3ba30: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3ba34: d0 = 20.000000
    //     0xb3ba34: fmov            d0, #20.00000000
    // 0xb3ba38: stur            x0, [fp, #-0x48]
    // 0xb3ba3c: StoreField: r0->field_7 = d0
    //     0xb3ba3c: stur            d0, [x0, #7]
    // 0xb3ba40: StoreField: r0->field_f = d0
    //     0xb3ba40: stur            d0, [x0, #0xf]
    // 0xb3ba44: r0 = BorderRadius()
    //     0xb3ba44: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3ba48: mov             x1, x0
    // 0xb3ba4c: ldur            x0, [fp, #-0x48]
    // 0xb3ba50: stur            x1, [fp, #-0x50]
    // 0xb3ba54: StoreField: r1->field_7 = r0
    //     0xb3ba54: stur            w0, [x1, #7]
    // 0xb3ba58: StoreField: r1->field_b = r0
    //     0xb3ba58: stur            w0, [x1, #0xb]
    // 0xb3ba5c: StoreField: r1->field_f = r0
    //     0xb3ba5c: stur            w0, [x1, #0xf]
    // 0xb3ba60: StoreField: r1->field_13 = r0
    //     0xb3ba60: stur            w0, [x1, #0x13]
    // 0xb3ba64: r0 = BoxDecoration()
    //     0xb3ba64: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb3ba68: mov             x2, x0
    // 0xb3ba6c: ldur            x0, [fp, #-0x28]
    // 0xb3ba70: stur            x2, [fp, #-0x48]
    // 0xb3ba74: StoreField: r2->field_7 = r0
    //     0xb3ba74: stur            w0, [x2, #7]
    // 0xb3ba78: ldur            x0, [fp, #-0x50]
    // 0xb3ba7c: StoreField: r2->field_13 = r0
    //     0xb3ba7c: stur            w0, [x2, #0x13]
    // 0xb3ba80: r0 = Instance_BoxShape
    //     0xb3ba80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3ba84: ldr             x0, [x0, #0x80]
    // 0xb3ba88: StoreField: r2->field_23 = r0
    //     0xb3ba88: stur            w0, [x2, #0x23]
    // 0xb3ba8c: ldur            x0, [fp, #-8]
    // 0xb3ba90: LoadField: r1 = r0->field_b
    //     0xb3ba90: ldur            w1, [x0, #0xb]
    // 0xb3ba94: DecompressPointer r1
    //     0xb3ba94: add             x1, x1, HEAP, lsl #32
    // 0xb3ba98: cmp             w1, NULL
    // 0xb3ba9c: b.eq            #0xb3bf50
    // 0xb3baa0: ldur            x0, [fp, #-0x10]
    // 0xb3baa4: LoadField: r1 = r0->field_3f
    //     0xb3baa4: ldur            w1, [x0, #0x3f]
    // 0xb3baa8: DecompressPointer r1
    //     0xb3baa8: add             x1, x1, HEAP, lsl #32
    // 0xb3baac: cmp             w1, NULL
    // 0xb3bab0: b.ne            #0xb3babc
    // 0xb3bab4: r0 = Null
    //     0xb3bab4: mov             x0, NULL
    // 0xb3bab8: b               #0xb3bad4
    // 0xb3babc: LoadField: r0 = r1->field_b
    //     0xb3babc: ldur            w0, [x1, #0xb]
    // 0xb3bac0: cbnz            w0, #0xb3bacc
    // 0xb3bac4: r1 = false
    //     0xb3bac4: add             x1, NULL, #0x30  ; false
    // 0xb3bac8: b               #0xb3bad0
    // 0xb3bacc: r1 = true
    //     0xb3bacc: add             x1, NULL, #0x20  ; true
    // 0xb3bad0: mov             x0, x1
    // 0xb3bad4: cmp             w0, NULL
    // 0xb3bad8: b.eq            #0xb3baec
    // 0xb3badc: tbnz            w0, #4, #0xb3baec
    // 0xb3bae0: r6 = "Customised"
    //     0xb3bae0: add             x6, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xb3bae4: ldr             x6, [x6, #0xd88]
    // 0xb3bae8: b               #0xb3baf0
    // 0xb3baec: r6 = ""
    //     0xb3baec: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3baf0: ldur            x5, [fp, #-0x30]
    // 0xb3baf4: ldur            x4, [fp, #-0x38]
    // 0xb3baf8: ldur            x3, [fp, #-0x40]
    // 0xb3bafc: ldur            x0, [fp, #-0x20]
    // 0xb3bb00: ldur            x1, [fp, #-0x18]
    // 0xb3bb04: stur            x6, [fp, #-8]
    // 0xb3bb08: r0 = of()
    //     0xb3bb08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3bb0c: LoadField: r1 = r0->field_87
    //     0xb3bb0c: ldur            w1, [x0, #0x87]
    // 0xb3bb10: DecompressPointer r1
    //     0xb3bb10: add             x1, x1, HEAP, lsl #32
    // 0xb3bb14: LoadField: r0 = r1->field_7
    //     0xb3bb14: ldur            w0, [x1, #7]
    // 0xb3bb18: DecompressPointer r0
    //     0xb3bb18: add             x0, x0, HEAP, lsl #32
    // 0xb3bb1c: r16 = 12.000000
    //     0xb3bb1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3bb20: ldr             x16, [x16, #0x9e8]
    // 0xb3bb24: r30 = Instance_Color
    //     0xb3bb24: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3bb28: stp             lr, x16, [SP]
    // 0xb3bb2c: mov             x1, x0
    // 0xb3bb30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3bb30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3bb34: ldr             x4, [x4, #0xaa0]
    // 0xb3bb38: r0 = copyWith()
    //     0xb3bb38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3bb3c: stur            x0, [fp, #-0x10]
    // 0xb3bb40: r0 = Text()
    //     0xb3bb40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3bb44: mov             x1, x0
    // 0xb3bb48: ldur            x0, [fp, #-8]
    // 0xb3bb4c: stur            x1, [fp, #-0x28]
    // 0xb3bb50: StoreField: r1->field_b = r0
    //     0xb3bb50: stur            w0, [x1, #0xb]
    // 0xb3bb54: ldur            x0, [fp, #-0x10]
    // 0xb3bb58: StoreField: r1->field_13 = r0
    //     0xb3bb58: stur            w0, [x1, #0x13]
    // 0xb3bb5c: r0 = Container()
    //     0xb3bb5c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb3bb60: stur            x0, [fp, #-8]
    // 0xb3bb64: r16 = Instance_EdgeInsets
    //     0xb3bb64: add             x16, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0xb3bb68: ldr             x16, [x16, #0xe0]
    // 0xb3bb6c: ldur            lr, [fp, #-0x48]
    // 0xb3bb70: stp             lr, x16, [SP, #8]
    // 0xb3bb74: ldur            x16, [fp, #-0x28]
    // 0xb3bb78: str             x16, [SP]
    // 0xb3bb7c: mov             x1, x0
    // 0xb3bb80: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb3bb80: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb3bb84: ldr             x4, [x4, #0x610]
    // 0xb3bb88: r0 = Container()
    //     0xb3bb88: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb3bb8c: r1 = Null
    //     0xb3bb8c: mov             x1, NULL
    // 0xb3bb90: r2 = 6
    //     0xb3bb90: movz            x2, #0x6
    // 0xb3bb94: r0 = AllocateArray()
    //     0xb3bb94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3bb98: mov             x2, x0
    // 0xb3bb9c: ldur            x0, [fp, #-0x20]
    // 0xb3bba0: stur            x2, [fp, #-0x10]
    // 0xb3bba4: StoreField: r2->field_f = r0
    //     0xb3bba4: stur            w0, [x2, #0xf]
    // 0xb3bba8: r16 = Instance_SizedBox
    //     0xb3bba8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xb3bbac: ldr             x16, [x16, #0x940]
    // 0xb3bbb0: StoreField: r2->field_13 = r16
    //     0xb3bbb0: stur            w16, [x2, #0x13]
    // 0xb3bbb4: ldur            x0, [fp, #-8]
    // 0xb3bbb8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3bbb8: stur            w0, [x2, #0x17]
    // 0xb3bbbc: r1 = <Widget>
    //     0xb3bbbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3bbc0: r0 = AllocateGrowableArray()
    //     0xb3bbc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3bbc4: mov             x1, x0
    // 0xb3bbc8: ldur            x0, [fp, #-0x10]
    // 0xb3bbcc: stur            x1, [fp, #-8]
    // 0xb3bbd0: StoreField: r1->field_f = r0
    //     0xb3bbd0: stur            w0, [x1, #0xf]
    // 0xb3bbd4: r2 = 6
    //     0xb3bbd4: movz            x2, #0x6
    // 0xb3bbd8: StoreField: r1->field_b = r2
    //     0xb3bbd8: stur            w2, [x1, #0xb]
    // 0xb3bbdc: r0 = Row()
    //     0xb3bbdc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3bbe0: mov             x3, x0
    // 0xb3bbe4: r0 = Instance_Axis
    //     0xb3bbe4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3bbe8: stur            x3, [fp, #-0x10]
    // 0xb3bbec: StoreField: r3->field_f = r0
    //     0xb3bbec: stur            w0, [x3, #0xf]
    // 0xb3bbf0: r4 = Instance_MainAxisAlignment
    //     0xb3bbf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3bbf4: ldr             x4, [x4, #0xa08]
    // 0xb3bbf8: StoreField: r3->field_13 = r4
    //     0xb3bbf8: stur            w4, [x3, #0x13]
    // 0xb3bbfc: r5 = Instance_MainAxisSize
    //     0xb3bbfc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3bc00: ldr             x5, [x5, #0xa10]
    // 0xb3bc04: ArrayStore: r3[0] = r5  ; List_4
    //     0xb3bc04: stur            w5, [x3, #0x17]
    // 0xb3bc08: r6 = Instance_CrossAxisAlignment
    //     0xb3bc08: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3bc0c: ldr             x6, [x6, #0xa18]
    // 0xb3bc10: StoreField: r3->field_1b = r6
    //     0xb3bc10: stur            w6, [x3, #0x1b]
    // 0xb3bc14: r7 = Instance_VerticalDirection
    //     0xb3bc14: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3bc18: ldr             x7, [x7, #0xa20]
    // 0xb3bc1c: StoreField: r3->field_23 = r7
    //     0xb3bc1c: stur            w7, [x3, #0x23]
    // 0xb3bc20: r8 = Instance_Clip
    //     0xb3bc20: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3bc24: ldr             x8, [x8, #0x38]
    // 0xb3bc28: StoreField: r3->field_2b = r8
    //     0xb3bc28: stur            w8, [x3, #0x2b]
    // 0xb3bc2c: StoreField: r3->field_2f = rZR
    //     0xb3bc2c: stur            xzr, [x3, #0x2f]
    // 0xb3bc30: ldur            x1, [fp, #-8]
    // 0xb3bc34: StoreField: r3->field_b = r1
    //     0xb3bc34: stur            w1, [x3, #0xb]
    // 0xb3bc38: r1 = Null
    //     0xb3bc38: mov             x1, NULL
    // 0xb3bc3c: r2 = 10
    //     0xb3bc3c: movz            x2, #0xa
    // 0xb3bc40: r0 = AllocateArray()
    //     0xb3bc40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3bc44: mov             x2, x0
    // 0xb3bc48: ldur            x0, [fp, #-0x38]
    // 0xb3bc4c: stur            x2, [fp, #-8]
    // 0xb3bc50: StoreField: r2->field_f = r0
    //     0xb3bc50: stur            w0, [x2, #0xf]
    // 0xb3bc54: r16 = Instance_SizedBox
    //     0xb3bc54: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb3bc58: ldr             x16, [x16, #0x568]
    // 0xb3bc5c: StoreField: r2->field_13 = r16
    //     0xb3bc5c: stur            w16, [x2, #0x13]
    // 0xb3bc60: ldur            x0, [fp, #-0x40]
    // 0xb3bc64: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3bc64: stur            w0, [x2, #0x17]
    // 0xb3bc68: r16 = Instance_SizedBox
    //     0xb3bc68: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xb3bc6c: ldr             x16, [x16, #0x568]
    // 0xb3bc70: StoreField: r2->field_1b = r16
    //     0xb3bc70: stur            w16, [x2, #0x1b]
    // 0xb3bc74: ldur            x0, [fp, #-0x10]
    // 0xb3bc78: StoreField: r2->field_1f = r0
    //     0xb3bc78: stur            w0, [x2, #0x1f]
    // 0xb3bc7c: r1 = <Widget>
    //     0xb3bc7c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3bc80: r0 = AllocateGrowableArray()
    //     0xb3bc80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3bc84: mov             x1, x0
    // 0xb3bc88: ldur            x0, [fp, #-8]
    // 0xb3bc8c: stur            x1, [fp, #-0x10]
    // 0xb3bc90: StoreField: r1->field_f = r0
    //     0xb3bc90: stur            w0, [x1, #0xf]
    // 0xb3bc94: r0 = 10
    //     0xb3bc94: movz            x0, #0xa
    // 0xb3bc98: StoreField: r1->field_b = r0
    //     0xb3bc98: stur            w0, [x1, #0xb]
    // 0xb3bc9c: r0 = Column()
    //     0xb3bc9c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3bca0: mov             x1, x0
    // 0xb3bca4: r0 = Instance_Axis
    //     0xb3bca4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3bca8: stur            x1, [fp, #-8]
    // 0xb3bcac: StoreField: r1->field_f = r0
    //     0xb3bcac: stur            w0, [x1, #0xf]
    // 0xb3bcb0: r2 = Instance_MainAxisAlignment
    //     0xb3bcb0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb3bcb4: ldr             x2, [x2, #0xa8]
    // 0xb3bcb8: StoreField: r1->field_13 = r2
    //     0xb3bcb8: stur            w2, [x1, #0x13]
    // 0xb3bcbc: r2 = Instance_MainAxisSize
    //     0xb3bcbc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3bcc0: ldr             x2, [x2, #0xa10]
    // 0xb3bcc4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb3bcc4: stur            w2, [x1, #0x17]
    // 0xb3bcc8: r3 = Instance_CrossAxisAlignment
    //     0xb3bcc8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3bccc: ldr             x3, [x3, #0x890]
    // 0xb3bcd0: StoreField: r1->field_1b = r3
    //     0xb3bcd0: stur            w3, [x1, #0x1b]
    // 0xb3bcd4: r3 = Instance_VerticalDirection
    //     0xb3bcd4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3bcd8: ldr             x3, [x3, #0xa20]
    // 0xb3bcdc: StoreField: r1->field_23 = r3
    //     0xb3bcdc: stur            w3, [x1, #0x23]
    // 0xb3bce0: r4 = Instance_Clip
    //     0xb3bce0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3bce4: ldr             x4, [x4, #0x38]
    // 0xb3bce8: StoreField: r1->field_2b = r4
    //     0xb3bce8: stur            w4, [x1, #0x2b]
    // 0xb3bcec: StoreField: r1->field_2f = rZR
    //     0xb3bcec: stur            xzr, [x1, #0x2f]
    // 0xb3bcf0: ldur            x5, [fp, #-0x10]
    // 0xb3bcf4: StoreField: r1->field_b = r5
    //     0xb3bcf4: stur            w5, [x1, #0xb]
    // 0xb3bcf8: r0 = Padding()
    //     0xb3bcf8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3bcfc: mov             x2, x0
    // 0xb3bd00: r0 = Instance_EdgeInsets
    //     0xb3bd00: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb3bd04: ldr             x0, [x0, #0x980]
    // 0xb3bd08: stur            x2, [fp, #-0x10]
    // 0xb3bd0c: StoreField: r2->field_f = r0
    //     0xb3bd0c: stur            w0, [x2, #0xf]
    // 0xb3bd10: ldur            x0, [fp, #-8]
    // 0xb3bd14: StoreField: r2->field_b = r0
    //     0xb3bd14: stur            w0, [x2, #0xb]
    // 0xb3bd18: r1 = <FlexParentData>
    //     0xb3bd18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb3bd1c: ldr             x1, [x1, #0xe00]
    // 0xb3bd20: r0 = Expanded()
    //     0xb3bd20: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3bd24: mov             x2, x0
    // 0xb3bd28: r0 = 1
    //     0xb3bd28: movz            x0, #0x1
    // 0xb3bd2c: stur            x2, [fp, #-8]
    // 0xb3bd30: StoreField: r2->field_13 = r0
    //     0xb3bd30: stur            x0, [x2, #0x13]
    // 0xb3bd34: r1 = Instance_FlexFit
    //     0xb3bd34: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3bd38: ldr             x1, [x1, #0xe08]
    // 0xb3bd3c: StoreField: r2->field_1b = r1
    //     0xb3bd3c: stur            w1, [x2, #0x1b]
    // 0xb3bd40: ldur            x1, [fp, #-0x10]
    // 0xb3bd44: StoreField: r2->field_b = r1
    //     0xb3bd44: stur            w1, [x2, #0xb]
    // 0xb3bd48: ldur            x1, [fp, #-0x18]
    // 0xb3bd4c: r0 = of()
    //     0xb3bd4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3bd50: LoadField: r1 = r0->field_5b
    //     0xb3bd50: ldur            w1, [x0, #0x5b]
    // 0xb3bd54: DecompressPointer r1
    //     0xb3bd54: add             x1, x1, HEAP, lsl #32
    // 0xb3bd58: stur            x1, [fp, #-0x10]
    // 0xb3bd5c: r0 = ColorFilter()
    //     0xb3bd5c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb3bd60: mov             x1, x0
    // 0xb3bd64: ldur            x0, [fp, #-0x10]
    // 0xb3bd68: stur            x1, [fp, #-0x18]
    // 0xb3bd6c: StoreField: r1->field_7 = r0
    //     0xb3bd6c: stur            w0, [x1, #7]
    // 0xb3bd70: r0 = Instance_BlendMode
    //     0xb3bd70: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb3bd74: ldr             x0, [x0, #0xb30]
    // 0xb3bd78: StoreField: r1->field_b = r0
    //     0xb3bd78: stur            w0, [x1, #0xb]
    // 0xb3bd7c: r0 = 1
    //     0xb3bd7c: movz            x0, #0x1
    // 0xb3bd80: StoreField: r1->field_13 = r0
    //     0xb3bd80: stur            x0, [x1, #0x13]
    // 0xb3bd84: r0 = SvgPicture()
    //     0xb3bd84: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb3bd88: stur            x0, [fp, #-0x10]
    // 0xb3bd8c: ldur            x16, [fp, #-0x18]
    // 0xb3bd90: str             x16, [SP]
    // 0xb3bd94: mov             x1, x0
    // 0xb3bd98: r2 = "assets/images/outline_arrow.svg"
    //     0xb3bd98: add             x2, PP, #0x34, lsl #12  ; [pp+0x34150] "assets/images/outline_arrow.svg"
    //     0xb3bd9c: ldr             x2, [x2, #0x150]
    // 0xb3bda0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb3bda0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb3bda4: ldr             x4, [x4, #0xa38]
    // 0xb3bda8: r0 = SvgPicture.asset()
    //     0xb3bda8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb3bdac: r0 = Align()
    //     0xb3bdac: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb3bdb0: mov             x1, x0
    // 0xb3bdb4: r0 = Instance_Alignment
    //     0xb3bdb4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb3bdb8: ldr             x0, [x0, #0xa78]
    // 0xb3bdbc: stur            x1, [fp, #-0x18]
    // 0xb3bdc0: StoreField: r1->field_f = r0
    //     0xb3bdc0: stur            w0, [x1, #0xf]
    // 0xb3bdc4: ldur            x0, [fp, #-0x10]
    // 0xb3bdc8: StoreField: r1->field_b = r0
    //     0xb3bdc8: stur            w0, [x1, #0xb]
    // 0xb3bdcc: r0 = Padding()
    //     0xb3bdcc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3bdd0: mov             x3, x0
    // 0xb3bdd4: r0 = Instance_EdgeInsets
    //     0xb3bdd4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xb3bdd8: ldr             x0, [x0, #0x268]
    // 0xb3bddc: stur            x3, [fp, #-0x10]
    // 0xb3bde0: StoreField: r3->field_f = r0
    //     0xb3bde0: stur            w0, [x3, #0xf]
    // 0xb3bde4: ldur            x0, [fp, #-0x18]
    // 0xb3bde8: StoreField: r3->field_b = r0
    //     0xb3bde8: stur            w0, [x3, #0xb]
    // 0xb3bdec: r1 = Null
    //     0xb3bdec: mov             x1, NULL
    // 0xb3bdf0: r2 = 6
    //     0xb3bdf0: movz            x2, #0x6
    // 0xb3bdf4: r0 = AllocateArray()
    //     0xb3bdf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3bdf8: mov             x2, x0
    // 0xb3bdfc: ldur            x0, [fp, #-0x30]
    // 0xb3be00: stur            x2, [fp, #-0x18]
    // 0xb3be04: StoreField: r2->field_f = r0
    //     0xb3be04: stur            w0, [x2, #0xf]
    // 0xb3be08: ldur            x0, [fp, #-8]
    // 0xb3be0c: StoreField: r2->field_13 = r0
    //     0xb3be0c: stur            w0, [x2, #0x13]
    // 0xb3be10: ldur            x0, [fp, #-0x10]
    // 0xb3be14: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3be14: stur            w0, [x2, #0x17]
    // 0xb3be18: r1 = <Widget>
    //     0xb3be18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3be1c: r0 = AllocateGrowableArray()
    //     0xb3be1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3be20: mov             x1, x0
    // 0xb3be24: ldur            x0, [fp, #-0x18]
    // 0xb3be28: stur            x1, [fp, #-8]
    // 0xb3be2c: StoreField: r1->field_f = r0
    //     0xb3be2c: stur            w0, [x1, #0xf]
    // 0xb3be30: r0 = 6
    //     0xb3be30: movz            x0, #0x6
    // 0xb3be34: StoreField: r1->field_b = r0
    //     0xb3be34: stur            w0, [x1, #0xb]
    // 0xb3be38: r0 = Row()
    //     0xb3be38: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3be3c: mov             x1, x0
    // 0xb3be40: r0 = Instance_Axis
    //     0xb3be40: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3be44: stur            x1, [fp, #-0x10]
    // 0xb3be48: StoreField: r1->field_f = r0
    //     0xb3be48: stur            w0, [x1, #0xf]
    // 0xb3be4c: r0 = Instance_MainAxisAlignment
    //     0xb3be4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3be50: ldr             x0, [x0, #0xa08]
    // 0xb3be54: StoreField: r1->field_13 = r0
    //     0xb3be54: stur            w0, [x1, #0x13]
    // 0xb3be58: r2 = Instance_MainAxisSize
    //     0xb3be58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3be5c: ldr             x2, [x2, #0xa10]
    // 0xb3be60: ArrayStore: r1[0] = r2  ; List_4
    //     0xb3be60: stur            w2, [x1, #0x17]
    // 0xb3be64: r3 = Instance_CrossAxisAlignment
    //     0xb3be64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3be68: ldr             x3, [x3, #0xa18]
    // 0xb3be6c: StoreField: r1->field_1b = r3
    //     0xb3be6c: stur            w3, [x1, #0x1b]
    // 0xb3be70: r4 = Instance_VerticalDirection
    //     0xb3be70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3be74: ldr             x4, [x4, #0xa20]
    // 0xb3be78: StoreField: r1->field_23 = r4
    //     0xb3be78: stur            w4, [x1, #0x23]
    // 0xb3be7c: r5 = Instance_Clip
    //     0xb3be7c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3be80: ldr             x5, [x5, #0x38]
    // 0xb3be84: StoreField: r1->field_2b = r5
    //     0xb3be84: stur            w5, [x1, #0x2b]
    // 0xb3be88: StoreField: r1->field_2f = rZR
    //     0xb3be88: stur            xzr, [x1, #0x2f]
    // 0xb3be8c: ldur            x6, [fp, #-8]
    // 0xb3be90: StoreField: r1->field_b = r6
    //     0xb3be90: stur            w6, [x1, #0xb]
    // 0xb3be94: r0 = IntrinsicHeight()
    //     0xb3be94: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb3be98: mov             x3, x0
    // 0xb3be9c: ldur            x0, [fp, #-0x10]
    // 0xb3bea0: stur            x3, [fp, #-8]
    // 0xb3bea4: StoreField: r3->field_b = r0
    //     0xb3bea4: stur            w0, [x3, #0xb]
    // 0xb3bea8: r1 = Null
    //     0xb3bea8: mov             x1, NULL
    // 0xb3beac: r2 = 2
    //     0xb3beac: movz            x2, #0x2
    // 0xb3beb0: r0 = AllocateArray()
    //     0xb3beb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3beb4: mov             x2, x0
    // 0xb3beb8: ldur            x0, [fp, #-8]
    // 0xb3bebc: stur            x2, [fp, #-0x10]
    // 0xb3bec0: StoreField: r2->field_f = r0
    //     0xb3bec0: stur            w0, [x2, #0xf]
    // 0xb3bec4: r1 = <Widget>
    //     0xb3bec4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3bec8: r0 = AllocateGrowableArray()
    //     0xb3bec8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3becc: mov             x1, x0
    // 0xb3bed0: ldur            x0, [fp, #-0x10]
    // 0xb3bed4: stur            x1, [fp, #-8]
    // 0xb3bed8: StoreField: r1->field_f = r0
    //     0xb3bed8: stur            w0, [x1, #0xf]
    // 0xb3bedc: r0 = 2
    //     0xb3bedc: movz            x0, #0x2
    // 0xb3bee0: StoreField: r1->field_b = r0
    //     0xb3bee0: stur            w0, [x1, #0xb]
    // 0xb3bee4: r0 = Column()
    //     0xb3bee4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3bee8: r1 = Instance_Axis
    //     0xb3bee8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3beec: StoreField: r0->field_f = r1
    //     0xb3beec: stur            w1, [x0, #0xf]
    // 0xb3bef0: r1 = Instance_MainAxisAlignment
    //     0xb3bef0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3bef4: ldr             x1, [x1, #0xa08]
    // 0xb3bef8: StoreField: r0->field_13 = r1
    //     0xb3bef8: stur            w1, [x0, #0x13]
    // 0xb3befc: r1 = Instance_MainAxisSize
    //     0xb3befc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3bf00: ldr             x1, [x1, #0xa10]
    // 0xb3bf04: ArrayStore: r0[0] = r1  ; List_4
    //     0xb3bf04: stur            w1, [x0, #0x17]
    // 0xb3bf08: r1 = Instance_CrossAxisAlignment
    //     0xb3bf08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3bf0c: ldr             x1, [x1, #0xa18]
    // 0xb3bf10: StoreField: r0->field_1b = r1
    //     0xb3bf10: stur            w1, [x0, #0x1b]
    // 0xb3bf14: r1 = Instance_VerticalDirection
    //     0xb3bf14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3bf18: ldr             x1, [x1, #0xa20]
    // 0xb3bf1c: StoreField: r0->field_23 = r1
    //     0xb3bf1c: stur            w1, [x0, #0x23]
    // 0xb3bf20: r1 = Instance_Clip
    //     0xb3bf20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3bf24: ldr             x1, [x1, #0x38]
    // 0xb3bf28: StoreField: r0->field_2b = r1
    //     0xb3bf28: stur            w1, [x0, #0x2b]
    // 0xb3bf2c: StoreField: r0->field_2f = rZR
    //     0xb3bf2c: stur            xzr, [x0, #0x2f]
    // 0xb3bf30: ldur            x1, [fp, #-8]
    // 0xb3bf34: StoreField: r0->field_b = r1
    //     0xb3bf34: stur            w1, [x0, #0xb]
    // 0xb3bf38: LeaveFrame
    //     0xb3bf38: mov             SP, fp
    //     0xb3bf3c: ldp             fp, lr, [SP], #0x10
    // 0xb3bf40: ret
    //     0xb3bf40: ret             
    // 0xb3bf44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3bf44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3bf48: b               #0xb3b5bc
    // 0xb3bf4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3bf4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3bf50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3bf50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3bf54, size: 0x30c
    // 0xb3bf54: EnterFrame
    //     0xb3bf54: stp             fp, lr, [SP, #-0x10]!
    //     0xb3bf58: mov             fp, SP
    // 0xb3bf5c: AllocStack(0x38)
    //     0xb3bf5c: sub             SP, SP, #0x38
    // 0xb3bf60: SetupParameters()
    //     0xb3bf60: ldr             x0, [fp, #0x10]
    //     0xb3bf64: ldur            w2, [x0, #0x17]
    //     0xb3bf68: add             x2, x2, HEAP, lsl #32
    //     0xb3bf6c: stur            x2, [fp, #-0x18]
    // 0xb3bf70: CheckStackOverflow
    //     0xb3bf70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3bf74: cmp             SP, x16
    //     0xb3bf78: b.ls            #0xb3c244
    // 0xb3bf7c: LoadField: r3 = r2->field_b
    //     0xb3bf7c: ldur            w3, [x2, #0xb]
    // 0xb3bf80: DecompressPointer r3
    //     0xb3bf80: add             x3, x3, HEAP, lsl #32
    // 0xb3bf84: stur            x3, [fp, #-0x10]
    // 0xb3bf88: LoadField: r0 = r3->field_f
    //     0xb3bf88: ldur            w0, [x3, #0xf]
    // 0xb3bf8c: DecompressPointer r0
    //     0xb3bf8c: add             x0, x0, HEAP, lsl #32
    // 0xb3bf90: LoadField: r4 = r0->field_b
    //     0xb3bf90: ldur            w4, [x0, #0xb]
    // 0xb3bf94: DecompressPointer r4
    //     0xb3bf94: add             x4, x4, HEAP, lsl #32
    // 0xb3bf98: stur            x4, [fp, #-8]
    // 0xb3bf9c: cmp             w4, NULL
    // 0xb3bfa0: b.eq            #0xb3c24c
    // 0xb3bfa4: LoadField: r0 = r4->field_b
    //     0xb3bfa4: ldur            w0, [x4, #0xb]
    // 0xb3bfa8: DecompressPointer r0
    //     0xb3bfa8: add             x0, x0, HEAP, lsl #32
    // 0xb3bfac: LoadField: r1 = r0->field_b
    //     0xb3bfac: ldur            w1, [x0, #0xb]
    // 0xb3bfb0: DecompressPointer r1
    //     0xb3bfb0: add             x1, x1, HEAP, lsl #32
    // 0xb3bfb4: cmp             w1, NULL
    // 0xb3bfb8: b.ne            #0xb3bfc4
    // 0xb3bfbc: r0 = Null
    //     0xb3bfbc: mov             x0, NULL
    // 0xb3bfc0: b               #0xb3c034
    // 0xb3bfc4: LoadField: r0 = r1->field_f
    //     0xb3bfc4: ldur            w0, [x1, #0xf]
    // 0xb3bfc8: DecompressPointer r0
    //     0xb3bfc8: add             x0, x0, HEAP, lsl #32
    // 0xb3bfcc: cmp             w0, NULL
    // 0xb3bfd0: b.ne            #0xb3bfdc
    // 0xb3bfd4: r0 = Null
    //     0xb3bfd4: mov             x0, NULL
    // 0xb3bfd8: b               #0xb3c034
    // 0xb3bfdc: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xb3bfdc: ldur            w5, [x0, #0x17]
    // 0xb3bfe0: DecompressPointer r5
    //     0xb3bfe0: add             x5, x5, HEAP, lsl #32
    // 0xb3bfe4: cmp             w5, NULL
    // 0xb3bfe8: b.ne            #0xb3bff4
    // 0xb3bfec: r0 = Null
    //     0xb3bfec: mov             x0, NULL
    // 0xb3bff0: b               #0xb3c034
    // 0xb3bff4: LoadField: r0 = r2->field_f
    //     0xb3bff4: ldur            w0, [x2, #0xf]
    // 0xb3bff8: DecompressPointer r0
    //     0xb3bff8: add             x0, x0, HEAP, lsl #32
    // 0xb3bffc: LoadField: r1 = r5->field_b
    //     0xb3bffc: ldur            w1, [x5, #0xb]
    // 0xb3c000: r6 = LoadInt32Instr(r0)
    //     0xb3c000: sbfx            x6, x0, #1, #0x1f
    //     0xb3c004: tbz             w0, #0, #0xb3c00c
    //     0xb3c008: ldur            x6, [x0, #7]
    // 0xb3c00c: r0 = LoadInt32Instr(r1)
    //     0xb3c00c: sbfx            x0, x1, #1, #0x1f
    // 0xb3c010: mov             x1, x6
    // 0xb3c014: cmp             x1, x0
    // 0xb3c018: b.hs            #0xb3c250
    // 0xb3c01c: LoadField: r0 = r5->field_f
    //     0xb3c01c: ldur            w0, [x5, #0xf]
    // 0xb3c020: DecompressPointer r0
    //     0xb3c020: add             x0, x0, HEAP, lsl #32
    // 0xb3c024: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb3c024: add             x16, x0, x6, lsl #2
    //     0xb3c028: ldur            w1, [x16, #0xf]
    // 0xb3c02c: DecompressPointer r1
    //     0xb3c02c: add             x1, x1, HEAP, lsl #32
    // 0xb3c030: mov             x0, x1
    // 0xb3c034: cmp             w0, NULL
    // 0xb3c038: b.ne            #0xb3c048
    // 0xb3c03c: r0 = BEntities()
    //     0xb3c03c: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xb3c040: mov             x3, x0
    // 0xb3c044: b               #0xb3c04c
    // 0xb3c048: mov             x3, x0
    // 0xb3c04c: ldur            x2, [fp, #-8]
    // 0xb3c050: LoadField: r0 = r2->field_b
    //     0xb3c050: ldur            w0, [x2, #0xb]
    // 0xb3c054: DecompressPointer r0
    //     0xb3c054: add             x0, x0, HEAP, lsl #32
    // 0xb3c058: LoadField: r1 = r0->field_b
    //     0xb3c058: ldur            w1, [x0, #0xb]
    // 0xb3c05c: DecompressPointer r1
    //     0xb3c05c: add             x1, x1, HEAP, lsl #32
    // 0xb3c060: cmp             w1, NULL
    // 0xb3c064: b.ne            #0xb3c074
    // 0xb3c068: ldur            x5, [fp, #-0x18]
    // 0xb3c06c: r0 = Null
    //     0xb3c06c: mov             x0, NULL
    // 0xb3c070: b               #0xb3c0f4
    // 0xb3c074: LoadField: r0 = r1->field_f
    //     0xb3c074: ldur            w0, [x1, #0xf]
    // 0xb3c078: DecompressPointer r0
    //     0xb3c078: add             x0, x0, HEAP, lsl #32
    // 0xb3c07c: cmp             w0, NULL
    // 0xb3c080: b.ne            #0xb3c090
    // 0xb3c084: ldur            x5, [fp, #-0x18]
    // 0xb3c088: r0 = Null
    //     0xb3c088: mov             x0, NULL
    // 0xb3c08c: b               #0xb3c0f4
    // 0xb3c090: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xb3c090: ldur            w4, [x0, #0x17]
    // 0xb3c094: DecompressPointer r4
    //     0xb3c094: add             x4, x4, HEAP, lsl #32
    // 0xb3c098: cmp             w4, NULL
    // 0xb3c09c: b.ne            #0xb3c0ac
    // 0xb3c0a0: ldur            x5, [fp, #-0x18]
    // 0xb3c0a4: r0 = Null
    //     0xb3c0a4: mov             x0, NULL
    // 0xb3c0a8: b               #0xb3c0f4
    // 0xb3c0ac: ldur            x5, [fp, #-0x18]
    // 0xb3c0b0: LoadField: r0 = r5->field_f
    //     0xb3c0b0: ldur            w0, [x5, #0xf]
    // 0xb3c0b4: DecompressPointer r0
    //     0xb3c0b4: add             x0, x0, HEAP, lsl #32
    // 0xb3c0b8: LoadField: r1 = r4->field_b
    //     0xb3c0b8: ldur            w1, [x4, #0xb]
    // 0xb3c0bc: r6 = LoadInt32Instr(r0)
    //     0xb3c0bc: sbfx            x6, x0, #1, #0x1f
    //     0xb3c0c0: tbz             w0, #0, #0xb3c0c8
    //     0xb3c0c4: ldur            x6, [x0, #7]
    // 0xb3c0c8: r0 = LoadInt32Instr(r1)
    //     0xb3c0c8: sbfx            x0, x1, #1, #0x1f
    // 0xb3c0cc: mov             x1, x6
    // 0xb3c0d0: cmp             x1, x0
    // 0xb3c0d4: b.hs            #0xb3c254
    // 0xb3c0d8: LoadField: r0 = r4->field_f
    //     0xb3c0d8: ldur            w0, [x4, #0xf]
    // 0xb3c0dc: DecompressPointer r0
    //     0xb3c0dc: add             x0, x0, HEAP, lsl #32
    // 0xb3c0e0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb3c0e0: add             x16, x0, x6, lsl #2
    //     0xb3c0e4: ldur            w1, [x16, #0xf]
    // 0xb3c0e8: DecompressPointer r1
    //     0xb3c0e8: add             x1, x1, HEAP, lsl #32
    // 0xb3c0ec: LoadField: r0 = r1->field_b
    //     0xb3c0ec: ldur            w0, [x1, #0xb]
    // 0xb3c0f0: DecompressPointer r0
    //     0xb3c0f0: add             x0, x0, HEAP, lsl #32
    // 0xb3c0f4: cmp             w0, NULL
    // 0xb3c0f8: b.ne            #0xb3c104
    // 0xb3c0fc: r4 = ""
    //     0xb3c0fc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3c100: b               #0xb3c108
    // 0xb3c104: mov             x4, x0
    // 0xb3c108: LoadField: r0 = r2->field_b
    //     0xb3c108: ldur            w0, [x2, #0xb]
    // 0xb3c10c: DecompressPointer r0
    //     0xb3c10c: add             x0, x0, HEAP, lsl #32
    // 0xb3c110: LoadField: r1 = r0->field_b
    //     0xb3c110: ldur            w1, [x0, #0xb]
    // 0xb3c114: DecompressPointer r1
    //     0xb3c114: add             x1, x1, HEAP, lsl #32
    // 0xb3c118: cmp             w1, NULL
    // 0xb3c11c: b.ne            #0xb3c128
    // 0xb3c120: r0 = Null
    //     0xb3c120: mov             x0, NULL
    // 0xb3c124: b               #0xb3c19c
    // 0xb3c128: LoadField: r0 = r1->field_f
    //     0xb3c128: ldur            w0, [x1, #0xf]
    // 0xb3c12c: DecompressPointer r0
    //     0xb3c12c: add             x0, x0, HEAP, lsl #32
    // 0xb3c130: cmp             w0, NULL
    // 0xb3c134: b.ne            #0xb3c140
    // 0xb3c138: r0 = Null
    //     0xb3c138: mov             x0, NULL
    // 0xb3c13c: b               #0xb3c19c
    // 0xb3c140: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xb3c140: ldur            w6, [x0, #0x17]
    // 0xb3c144: DecompressPointer r6
    //     0xb3c144: add             x6, x6, HEAP, lsl #32
    // 0xb3c148: cmp             w6, NULL
    // 0xb3c14c: b.ne            #0xb3c158
    // 0xb3c150: r0 = Null
    //     0xb3c150: mov             x0, NULL
    // 0xb3c154: b               #0xb3c19c
    // 0xb3c158: LoadField: r0 = r5->field_f
    //     0xb3c158: ldur            w0, [x5, #0xf]
    // 0xb3c15c: DecompressPointer r0
    //     0xb3c15c: add             x0, x0, HEAP, lsl #32
    // 0xb3c160: LoadField: r1 = r6->field_b
    //     0xb3c160: ldur            w1, [x6, #0xb]
    // 0xb3c164: r5 = LoadInt32Instr(r0)
    //     0xb3c164: sbfx            x5, x0, #1, #0x1f
    //     0xb3c168: tbz             w0, #0, #0xb3c170
    //     0xb3c16c: ldur            x5, [x0, #7]
    // 0xb3c170: r0 = LoadInt32Instr(r1)
    //     0xb3c170: sbfx            x0, x1, #1, #0x1f
    // 0xb3c174: mov             x1, x5
    // 0xb3c178: cmp             x1, x0
    // 0xb3c17c: b.hs            #0xb3c258
    // 0xb3c180: LoadField: r0 = r6->field_f
    //     0xb3c180: ldur            w0, [x6, #0xf]
    // 0xb3c184: DecompressPointer r0
    //     0xb3c184: add             x0, x0, HEAP, lsl #32
    // 0xb3c188: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb3c188: add             x16, x0, x5, lsl #2
    //     0xb3c18c: ldur            w1, [x16, #0xf]
    // 0xb3c190: DecompressPointer r1
    //     0xb3c190: add             x1, x1, HEAP, lsl #32
    // 0xb3c194: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb3c194: ldur            w0, [x1, #0x17]
    // 0xb3c198: DecompressPointer r0
    //     0xb3c198: add             x0, x0, HEAP, lsl #32
    // 0xb3c19c: cmp             w0, NULL
    // 0xb3c1a0: b.ne            #0xb3c1ac
    // 0xb3c1a4: r6 = 0
    //     0xb3c1a4: movz            x6, #0
    // 0xb3c1a8: b               #0xb3c1bc
    // 0xb3c1ac: r1 = LoadInt32Instr(r0)
    //     0xb3c1ac: sbfx            x1, x0, #1, #0x1f
    //     0xb3c1b0: tbz             w0, #0, #0xb3c1b8
    //     0xb3c1b4: ldur            x1, [x0, #7]
    // 0xb3c1b8: mov             x6, x1
    // 0xb3c1bc: ldur            x5, [fp, #-0x10]
    // 0xb3c1c0: LoadField: r7 = r2->field_13
    //     0xb3c1c0: ldur            w7, [x2, #0x13]
    // 0xb3c1c4: DecompressPointer r7
    //     0xb3c1c4: add             x7, x7, HEAP, lsl #32
    // 0xb3c1c8: r0 = BoxInt64Instr(r6)
    //     0xb3c1c8: sbfiz           x0, x6, #1, #0x1f
    //     0xb3c1cc: cmp             x6, x0, asr #1
    //     0xb3c1d0: b.eq            #0xb3c1dc
    //     0xb3c1d4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb3c1d8: stur            x6, [x0, #7]
    // 0xb3c1dc: stp             x3, x7, [SP, #0x10]
    // 0xb3c1e0: stp             x0, x4, [SP]
    // 0xb3c1e4: r4 = 0
    //     0xb3c1e4: movz            x4, #0
    // 0xb3c1e8: ldr             x0, [SP, #0x18]
    // 0xb3c1ec: r5 = UnlinkedCall_0x613b5c
    //     0xb3c1ec: add             x16, PP, #0x57, lsl #12  ; [pp+0x570f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb3c1f0: ldp             x5, lr, [x16, #0xf0]
    // 0xb3c1f4: blr             lr
    // 0xb3c1f8: ldur            x0, [fp, #-0x10]
    // 0xb3c1fc: LoadField: r1 = r0->field_f
    //     0xb3c1fc: ldur            w1, [x0, #0xf]
    // 0xb3c200: DecompressPointer r1
    //     0xb3c200: add             x1, x1, HEAP, lsl #32
    // 0xb3c204: LoadField: r0 = r1->field_b
    //     0xb3c204: ldur            w0, [x1, #0xb]
    // 0xb3c208: DecompressPointer r0
    //     0xb3c208: add             x0, x0, HEAP, lsl #32
    // 0xb3c20c: cmp             w0, NULL
    // 0xb3c210: b.eq            #0xb3c25c
    // 0xb3c214: LoadField: r1 = r0->field_1b
    //     0xb3c214: ldur            w1, [x0, #0x1b]
    // 0xb3c218: DecompressPointer r1
    //     0xb3c218: add             x1, x1, HEAP, lsl #32
    // 0xb3c21c: str             x1, [SP]
    // 0xb3c220: r4 = 0
    //     0xb3c220: movz            x4, #0
    // 0xb3c224: ldr             x0, [SP]
    // 0xb3c228: r5 = UnlinkedCall_0x613b5c
    //     0xb3c228: add             x16, PP, #0x57, lsl #12  ; [pp+0x57100] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb3c22c: ldp             x5, lr, [x16, #0x100]
    // 0xb3c230: blr             lr
    // 0xb3c234: r0 = Null
    //     0xb3c234: mov             x0, NULL
    // 0xb3c238: LeaveFrame
    //     0xb3c238: mov             SP, fp
    //     0xb3c23c: ldp             fp, lr, [SP], #0x10
    // 0xb3c240: ret
    //     0xb3c240: ret             
    // 0xb3c244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c244: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c248: b               #0xb3bf7c
    // 0xb3c24c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c24c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3c250: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3c250: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3c254: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3c254: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3c258: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3c258: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3c25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3c260, size: 0x60
    // 0xb3c260: EnterFrame
    //     0xb3c260: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c264: mov             fp, SP
    // 0xb3c268: AllocStack(0x8)
    //     0xb3c268: sub             SP, SP, #8
    // 0xb3c26c: SetupParameters()
    //     0xb3c26c: ldr             x0, [fp, #0x10]
    //     0xb3c270: ldur            w2, [x0, #0x17]
    //     0xb3c274: add             x2, x2, HEAP, lsl #32
    // 0xb3c278: CheckStackOverflow
    //     0xb3c278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c27c: cmp             SP, x16
    //     0xb3c280: b.ls            #0xb3c2b8
    // 0xb3c284: LoadField: r0 = r2->field_f
    //     0xb3c284: ldur            w0, [x2, #0xf]
    // 0xb3c288: DecompressPointer r0
    //     0xb3c288: add             x0, x0, HEAP, lsl #32
    // 0xb3c28c: stur            x0, [fp, #-8]
    // 0xb3c290: r1 = Function '<anonymous closure>':.
    //     0xb3c290: add             x1, PP, #0x57, lsl #12  ; [pp+0x57120] AnonymousClosure: (0xb3c2c0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xb39dd0)
    //     0xb3c294: ldr             x1, [x1, #0x120]
    // 0xb3c298: r0 = AllocateClosure()
    //     0xb3c298: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3c29c: ldur            x1, [fp, #-8]
    // 0xb3c2a0: mov             x2, x0
    // 0xb3c2a4: r0 = setState()
    //     0xb3c2a4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb3c2a8: r0 = Null
    //     0xb3c2a8: mov             x0, NULL
    // 0xb3c2ac: LeaveFrame
    //     0xb3c2ac: mov             SP, fp
    //     0xb3c2b0: ldp             fp, lr, [SP], #0x10
    // 0xb3c2b4: ret
    //     0xb3c2b4: ret             
    // 0xb3c2b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c2b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c2bc: b               #0xb3c284
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3c2c0, size: 0x78
    // 0xb3c2c0: EnterFrame
    //     0xb3c2c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c2c4: mov             fp, SP
    // 0xb3c2c8: AllocStack(0x8)
    //     0xb3c2c8: sub             SP, SP, #8
    // 0xb3c2cc: SetupParameters()
    //     0xb3c2cc: ldr             x0, [fp, #0x10]
    //     0xb3c2d0: ldur            w1, [x0, #0x17]
    //     0xb3c2d4: add             x1, x1, HEAP, lsl #32
    // 0xb3c2d8: CheckStackOverflow
    //     0xb3c2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c2dc: cmp             SP, x16
    //     0xb3c2e0: b.ls            #0xb3c32c
    // 0xb3c2e4: LoadField: r0 = r1->field_f
    //     0xb3c2e4: ldur            w0, [x1, #0xf]
    // 0xb3c2e8: DecompressPointer r0
    //     0xb3c2e8: add             x0, x0, HEAP, lsl #32
    // 0xb3c2ec: LoadField: r1 = r0->field_b
    //     0xb3c2ec: ldur            w1, [x0, #0xb]
    // 0xb3c2f0: DecompressPointer r1
    //     0xb3c2f0: add             x1, x1, HEAP, lsl #32
    // 0xb3c2f4: cmp             w1, NULL
    // 0xb3c2f8: b.eq            #0xb3c334
    // 0xb3c2fc: LoadField: r0 = r1->field_1f
    //     0xb3c2fc: ldur            w0, [x1, #0x1f]
    // 0xb3c300: DecompressPointer r0
    //     0xb3c300: add             x0, x0, HEAP, lsl #32
    // 0xb3c304: str             x0, [SP]
    // 0xb3c308: r4 = 0
    //     0xb3c308: movz            x4, #0
    // 0xb3c30c: ldr             x0, [SP]
    // 0xb3c310: r5 = UnlinkedCall_0x613b5c
    //     0xb3c310: add             x16, PP, #0x57, lsl #12  ; [pp+0x57128] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb3c314: ldp             x5, lr, [x16, #0x128]
    // 0xb3c318: blr             lr
    // 0xb3c31c: r0 = Null
    //     0xb3c31c: mov             x0, NULL
    // 0xb3c320: LeaveFrame
    //     0xb3c320: mov             SP, fp
    //     0xb3c324: ldp             fp, lr, [SP], #0x10
    // 0xb3c328: ret
    //     0xb3c328: ret             
    // 0xb3c32c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c32c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c330: b               #0xb3c2e4
    // 0xb3c334: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c334: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3c338, size: 0x60
    // 0xb3c338: EnterFrame
    //     0xb3c338: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c33c: mov             fp, SP
    // 0xb3c340: AllocStack(0x8)
    //     0xb3c340: sub             SP, SP, #8
    // 0xb3c344: SetupParameters()
    //     0xb3c344: ldr             x0, [fp, #0x10]
    //     0xb3c348: ldur            w2, [x0, #0x17]
    //     0xb3c34c: add             x2, x2, HEAP, lsl #32
    // 0xb3c350: CheckStackOverflow
    //     0xb3c350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c354: cmp             SP, x16
    //     0xb3c358: b.ls            #0xb3c390
    // 0xb3c35c: LoadField: r0 = r2->field_f
    //     0xb3c35c: ldur            w0, [x2, #0xf]
    // 0xb3c360: DecompressPointer r0
    //     0xb3c360: add             x0, x0, HEAP, lsl #32
    // 0xb3c364: stur            x0, [fp, #-8]
    // 0xb3c368: r1 = Function '<anonymous closure>':.
    //     0xb3c368: add             x1, PP, #0x57, lsl #12  ; [pp+0x57138] AnonymousClosure: (0xb3c398), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xb39dd0)
    //     0xb3c36c: ldr             x1, [x1, #0x138]
    // 0xb3c370: r0 = AllocateClosure()
    //     0xb3c370: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3c374: ldur            x1, [fp, #-8]
    // 0xb3c378: mov             x2, x0
    // 0xb3c37c: r0 = setState()
    //     0xb3c37c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb3c380: r0 = Null
    //     0xb3c380: mov             x0, NULL
    // 0xb3c384: LeaveFrame
    //     0xb3c384: mov             SP, fp
    //     0xb3c388: ldp             fp, lr, [SP], #0x10
    // 0xb3c38c: ret
    //     0xb3c38c: ret             
    // 0xb3c390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c390: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c394: b               #0xb3c35c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3c398, size: 0x78
    // 0xb3c398: EnterFrame
    //     0xb3c398: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c39c: mov             fp, SP
    // 0xb3c3a0: AllocStack(0x8)
    //     0xb3c3a0: sub             SP, SP, #8
    // 0xb3c3a4: SetupParameters()
    //     0xb3c3a4: ldr             x0, [fp, #0x10]
    //     0xb3c3a8: ldur            w1, [x0, #0x17]
    //     0xb3c3ac: add             x1, x1, HEAP, lsl #32
    // 0xb3c3b0: CheckStackOverflow
    //     0xb3c3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c3b4: cmp             SP, x16
    //     0xb3c3b8: b.ls            #0xb3c404
    // 0xb3c3bc: LoadField: r0 = r1->field_f
    //     0xb3c3bc: ldur            w0, [x1, #0xf]
    // 0xb3c3c0: DecompressPointer r0
    //     0xb3c3c0: add             x0, x0, HEAP, lsl #32
    // 0xb3c3c4: LoadField: r1 = r0->field_b
    //     0xb3c3c4: ldur            w1, [x0, #0xb]
    // 0xb3c3c8: DecompressPointer r1
    //     0xb3c3c8: add             x1, x1, HEAP, lsl #32
    // 0xb3c3cc: cmp             w1, NULL
    // 0xb3c3d0: b.eq            #0xb3c40c
    // 0xb3c3d4: LoadField: r0 = r1->field_23
    //     0xb3c3d4: ldur            w0, [x1, #0x23]
    // 0xb3c3d8: DecompressPointer r0
    //     0xb3c3d8: add             x0, x0, HEAP, lsl #32
    // 0xb3c3dc: str             x0, [SP]
    // 0xb3c3e0: r4 = 0
    //     0xb3c3e0: movz            x4, #0
    // 0xb3c3e4: ldr             x0, [SP]
    // 0xb3c3e8: r5 = UnlinkedCall_0x613b5c
    //     0xb3c3e8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57140] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb3c3ec: ldp             x5, lr, [x16, #0x140]
    // 0xb3c3f0: blr             lr
    // 0xb3c3f4: r0 = Null
    //     0xb3c3f4: mov             x0, NULL
    // 0xb3c3f8: LeaveFrame
    //     0xb3c3f8: mov             SP, fp
    //     0xb3c3fc: ldp             fp, lr, [SP], #0x10
    // 0xb3c400: ret
    //     0xb3c400: ret             
    // 0xb3c404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c404: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c408: b               #0xb3c3bc
    // 0xb3c40c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c40c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4111, size: 0x28, field offset: 0xc
class BagDetailWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e840, size: 0x24
    // 0xc7e840: EnterFrame
    //     0xc7e840: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e844: mov             fp, SP
    // 0xc7e848: mov             x0, x1
    // 0xc7e84c: r1 = <BagDetailWidget>
    //     0xc7e84c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a78] TypeArguments: <BagDetailWidget>
    //     0xc7e850: ldr             x1, [x1, #0xa78]
    // 0xc7e854: r0 = _BagDetailWidgetState()
    //     0xc7e854: bl              #0xc7e864  ; Allocate_BagDetailWidgetStateStub -> _BagDetailWidgetState (size=0x14)
    // 0xc7e858: LeaveFrame
    //     0xc7e858: mov             SP, fp
    //     0xc7e85c: ldp             fp, lr, [SP], #0x10
    // 0xc7e860: ret
    //     0xc7e860: ret             
  }
}
