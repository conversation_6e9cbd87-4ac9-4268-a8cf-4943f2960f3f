// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart

// class id: 1049480, size: 0x8
class :: {
}

// class id: 4541, size: 0x14, field offset: 0x14
//   const constructor, 
class PaymentMethodsCheckoutWidget extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1366b84, size: 0x64
    // 0x1366b84: EnterFrame
    //     0x1366b84: stp             fp, lr, [SP, #-0x10]!
    //     0x1366b88: mov             fp, SP
    // 0x1366b8c: AllocStack(0x18)
    //     0x1366b8c: sub             SP, SP, #0x18
    // 0x1366b90: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1366b90: stur            x1, [fp, #-8]
    //     0x1366b94: stur            x2, [fp, #-0x10]
    // 0x1366b98: r1 = 2
    //     0x1366b98: movz            x1, #0x2
    // 0x1366b9c: r0 = AllocateContext()
    //     0x1366b9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1366ba0: mov             x1, x0
    // 0x1366ba4: ldur            x0, [fp, #-8]
    // 0x1366ba8: stur            x1, [fp, #-0x18]
    // 0x1366bac: StoreField: r1->field_f = r0
    //     0x1366bac: stur            w0, [x1, #0xf]
    // 0x1366bb0: ldur            x0, [fp, #-0x10]
    // 0x1366bb4: StoreField: r1->field_13 = r0
    //     0x1366bb4: stur            w0, [x1, #0x13]
    // 0x1366bb8: r0 = Obx()
    //     0x1366bb8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1366bbc: ldur            x2, [fp, #-0x18]
    // 0x1366bc0: r1 = Function '<anonymous closure>':.
    //     0x1366bc0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c478] AnonymousClosure: (0x1366be8), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::bottomNavigationBar (0x1366b84)
    //     0x1366bc4: ldr             x1, [x1, #0x478]
    // 0x1366bc8: stur            x0, [fp, #-8]
    // 0x1366bcc: r0 = AllocateClosure()
    //     0x1366bcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1366bd0: mov             x1, x0
    // 0x1366bd4: ldur            x0, [fp, #-8]
    // 0x1366bd8: StoreField: r0->field_b = r1
    //     0x1366bd8: stur            w1, [x0, #0xb]
    // 0x1366bdc: LeaveFrame
    //     0x1366bdc: mov             SP, fp
    //     0x1366be0: ldp             fp, lr, [SP], #0x10
    // 0x1366be4: ret
    //     0x1366be4: ret             
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x1366be8, size: 0x674
    // 0x1366be8: EnterFrame
    //     0x1366be8: stp             fp, lr, [SP, #-0x10]!
    //     0x1366bec: mov             fp, SP
    // 0x1366bf0: AllocStack(0x50)
    //     0x1366bf0: sub             SP, SP, #0x50
    // 0x1366bf4: SetupParameters()
    //     0x1366bf4: ldr             x0, [fp, #0x10]
    //     0x1366bf8: ldur            w2, [x0, #0x17]
    //     0x1366bfc: add             x2, x2, HEAP, lsl #32
    //     0x1366c00: stur            x2, [fp, #-8]
    // 0x1366c04: CheckStackOverflow
    //     0x1366c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1366c08: cmp             SP, x16
    //     0x1366c0c: b.ls            #0x1367244
    // 0x1366c10: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1366c10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1366c14: ldr             x0, [x0, #0x1c80]
    //     0x1366c18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1366c1c: cmp             w0, w16
    //     0x1366c20: b.ne            #0x1366c2c
    //     0x1366c24: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1366c28: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1366c2c: r0 = GetNavigation.size()
    //     0x1366c2c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1366c30: LoadField: d0 = r0->field_f
    //     0x1366c30: ldur            d0, [x0, #0xf]
    // 0x1366c34: d1 = 0.120000
    //     0x1366c34: ldr             d1, [PP, #0x54a8]  ; [pp+0x54a8] IMM: double(0.12) from 0x3fbeb851eb851eb8
    // 0x1366c38: fmul            d2, d0, d1
    // 0x1366c3c: stur            d2, [fp, #-0x40]
    // 0x1366c40: r1 = _ConstMap len:11
    //     0x1366c40: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x1366c44: ldr             x1, [x1, #0xc28]
    // 0x1366c48: r2 = 8
    //     0x1366c48: movz            x2, #0x8
    // 0x1366c4c: r0 = []()
    //     0x1366c4c: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1366c50: stur            x0, [fp, #-0x10]
    // 0x1366c54: r0 = BoxDecoration()
    //     0x1366c54: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1366c58: mov             x2, x0
    // 0x1366c5c: r0 = Instance_Color
    //     0x1366c5c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1366c60: stur            x2, [fp, #-0x18]
    // 0x1366c64: StoreField: r2->field_7 = r0
    //     0x1366c64: stur            w0, [x2, #7]
    // 0x1366c68: ldur            x0, [fp, #-0x10]
    // 0x1366c6c: ArrayStore: r2[0] = r0  ; List_4
    //     0x1366c6c: stur            w0, [x2, #0x17]
    // 0x1366c70: r0 = Instance_BoxShape
    //     0x1366c70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1366c74: ldr             x0, [x0, #0x80]
    // 0x1366c78: StoreField: r2->field_23 = r0
    //     0x1366c78: stur            w0, [x2, #0x23]
    // 0x1366c7c: ldur            x0, [fp, #-8]
    // 0x1366c80: LoadField: r1 = r0->field_f
    //     0x1366c80: ldur            w1, [x0, #0xf]
    // 0x1366c84: DecompressPointer r1
    //     0x1366c84: add             x1, x1, HEAP, lsl #32
    // 0x1366c88: r0 = controller()
    //     0x1366c88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366c8c: LoadField: r1 = r0->field_87
    //     0x1366c8c: ldur            w1, [x0, #0x87]
    // 0x1366c90: DecompressPointer r1
    //     0x1366c90: add             x1, x1, HEAP, lsl #32
    // 0x1366c94: r0 = value()
    //     0x1366c94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1366c98: LoadField: r1 = r0->field_f
    //     0x1366c98: ldur            w1, [x0, #0xf]
    // 0x1366c9c: DecompressPointer r1
    //     0x1366c9c: add             x1, x1, HEAP, lsl #32
    // 0x1366ca0: cmp             w1, NULL
    // 0x1366ca4: b.ne            #0x1366cb0
    // 0x1366ca8: r0 = ""
    //     0x1366ca8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1366cac: b               #0x1366cb4
    // 0x1366cb0: mov             x0, x1
    // 0x1366cb4: ldur            x2, [fp, #-8]
    // 0x1366cb8: stur            x0, [fp, #-0x10]
    // 0x1366cbc: LoadField: r1 = r2->field_13
    //     0x1366cbc: ldur            w1, [x2, #0x13]
    // 0x1366cc0: DecompressPointer r1
    //     0x1366cc0: add             x1, x1, HEAP, lsl #32
    // 0x1366cc4: r0 = of()
    //     0x1366cc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1366cc8: LoadField: r1 = r0->field_87
    //     0x1366cc8: ldur            w1, [x0, #0x87]
    // 0x1366ccc: DecompressPointer r1
    //     0x1366ccc: add             x1, x1, HEAP, lsl #32
    // 0x1366cd0: LoadField: r0 = r1->field_2b
    //     0x1366cd0: ldur            w0, [x1, #0x2b]
    // 0x1366cd4: DecompressPointer r0
    //     0x1366cd4: add             x0, x0, HEAP, lsl #32
    // 0x1366cd8: stur            x0, [fp, #-0x20]
    // 0x1366cdc: r1 = Instance_Color
    //     0x1366cdc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1366ce0: d0 = 0.700000
    //     0x1366ce0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1366ce4: ldr             d0, [x17, #0xf48]
    // 0x1366ce8: r0 = withOpacity()
    //     0x1366ce8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1366cec: r16 = 12.000000
    //     0x1366cec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1366cf0: ldr             x16, [x16, #0x9e8]
    // 0x1366cf4: stp             x0, x16, [SP]
    // 0x1366cf8: ldur            x1, [fp, #-0x20]
    // 0x1366cfc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1366cfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1366d00: ldr             x4, [x4, #0xaa0]
    // 0x1366d04: r0 = copyWith()
    //     0x1366d04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1366d08: stur            x0, [fp, #-0x20]
    // 0x1366d0c: r0 = Text()
    //     0x1366d0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1366d10: mov             x2, x0
    // 0x1366d14: ldur            x0, [fp, #-0x10]
    // 0x1366d18: stur            x2, [fp, #-0x28]
    // 0x1366d1c: StoreField: r2->field_b = r0
    //     0x1366d1c: stur            w0, [x2, #0xb]
    // 0x1366d20: ldur            x0, [fp, #-0x20]
    // 0x1366d24: StoreField: r2->field_13 = r0
    //     0x1366d24: stur            w0, [x2, #0x13]
    // 0x1366d28: ldur            x0, [fp, #-8]
    // 0x1366d2c: LoadField: r1 = r0->field_f
    //     0x1366d2c: ldur            w1, [x0, #0xf]
    // 0x1366d30: DecompressPointer r1
    //     0x1366d30: add             x1, x1, HEAP, lsl #32
    // 0x1366d34: r0 = controller()
    //     0x1366d34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366d38: mov             x1, x0
    // 0x1366d3c: r0 = offersResponse()
    //     0x1366d3c: bl              #0x9eefa8  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::offersResponse
    // 0x1366d40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1366d40: ldur            w1, [x0, #0x17]
    // 0x1366d44: DecompressPointer r1
    //     0x1366d44: add             x1, x1, HEAP, lsl #32
    // 0x1366d48: cmp             w1, NULL
    // 0x1366d4c: b.ne            #0x1366d58
    // 0x1366d50: r3 = ""
    //     0x1366d50: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1366d54: b               #0x1366d5c
    // 0x1366d58: mov             x3, x1
    // 0x1366d5c: ldur            x2, [fp, #-8]
    // 0x1366d60: ldur            d0, [fp, #-0x40]
    // 0x1366d64: ldur            x0, [fp, #-0x28]
    // 0x1366d68: stur            x3, [fp, #-0x10]
    // 0x1366d6c: LoadField: r1 = r2->field_13
    //     0x1366d6c: ldur            w1, [x2, #0x13]
    // 0x1366d70: DecompressPointer r1
    //     0x1366d70: add             x1, x1, HEAP, lsl #32
    // 0x1366d74: r0 = of()
    //     0x1366d74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1366d78: LoadField: r1 = r0->field_87
    //     0x1366d78: ldur            w1, [x0, #0x87]
    // 0x1366d7c: DecompressPointer r1
    //     0x1366d7c: add             x1, x1, HEAP, lsl #32
    // 0x1366d80: LoadField: r0 = r1->field_7
    //     0x1366d80: ldur            w0, [x1, #7]
    // 0x1366d84: DecompressPointer r0
    //     0x1366d84: add             x0, x0, HEAP, lsl #32
    // 0x1366d88: stur            x0, [fp, #-0x20]
    // 0x1366d8c: r1 = Instance_Color
    //     0x1366d8c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1366d90: d0 = 0.700000
    //     0x1366d90: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1366d94: ldr             d0, [x17, #0xf48]
    // 0x1366d98: r0 = withOpacity()
    //     0x1366d98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1366d9c: r16 = 16.000000
    //     0x1366d9c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1366da0: ldr             x16, [x16, #0x188]
    // 0x1366da4: stp             x0, x16, [SP]
    // 0x1366da8: ldur            x1, [fp, #-0x20]
    // 0x1366dac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1366dac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1366db0: ldr             x4, [x4, #0xaa0]
    // 0x1366db4: r0 = copyWith()
    //     0x1366db4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1366db8: stur            x0, [fp, #-0x20]
    // 0x1366dbc: r0 = Text()
    //     0x1366dbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1366dc0: mov             x1, x0
    // 0x1366dc4: ldur            x0, [fp, #-0x10]
    // 0x1366dc8: stur            x1, [fp, #-0x30]
    // 0x1366dcc: StoreField: r1->field_b = r0
    //     0x1366dcc: stur            w0, [x1, #0xb]
    // 0x1366dd0: ldur            x0, [fp, #-0x20]
    // 0x1366dd4: StoreField: r1->field_13 = r0
    //     0x1366dd4: stur            w0, [x1, #0x13]
    // 0x1366dd8: r0 = Padding()
    //     0x1366dd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1366ddc: mov             x3, x0
    // 0x1366de0: r0 = Instance_EdgeInsets
    //     0x1366de0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x1366de4: ldr             x0, [x0, #0x668]
    // 0x1366de8: stur            x3, [fp, #-0x10]
    // 0x1366dec: StoreField: r3->field_f = r0
    //     0x1366dec: stur            w0, [x3, #0xf]
    // 0x1366df0: ldur            x0, [fp, #-0x30]
    // 0x1366df4: StoreField: r3->field_b = r0
    //     0x1366df4: stur            w0, [x3, #0xb]
    // 0x1366df8: r1 = Null
    //     0x1366df8: mov             x1, NULL
    // 0x1366dfc: r2 = 2
    //     0x1366dfc: movz            x2, #0x2
    // 0x1366e00: r0 = AllocateArray()
    //     0x1366e00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1366e04: mov             x2, x0
    // 0x1366e08: ldur            x0, [fp, #-0x10]
    // 0x1366e0c: stur            x2, [fp, #-0x20]
    // 0x1366e10: StoreField: r2->field_f = r0
    //     0x1366e10: stur            w0, [x2, #0xf]
    // 0x1366e14: r1 = <Widget>
    //     0x1366e14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1366e18: r0 = AllocateGrowableArray()
    //     0x1366e18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1366e1c: mov             x1, x0
    // 0x1366e20: ldur            x0, [fp, #-0x20]
    // 0x1366e24: stur            x1, [fp, #-0x10]
    // 0x1366e28: StoreField: r1->field_f = r0
    //     0x1366e28: stur            w0, [x1, #0xf]
    // 0x1366e2c: r0 = 2
    //     0x1366e2c: movz            x0, #0x2
    // 0x1366e30: StoreField: r1->field_b = r0
    //     0x1366e30: stur            w0, [x1, #0xb]
    // 0x1366e34: r0 = Row()
    //     0x1366e34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1366e38: mov             x3, x0
    // 0x1366e3c: r0 = Instance_Axis
    //     0x1366e3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1366e40: stur            x3, [fp, #-0x20]
    // 0x1366e44: StoreField: r3->field_f = r0
    //     0x1366e44: stur            w0, [x3, #0xf]
    // 0x1366e48: r4 = Instance_MainAxisAlignment
    //     0x1366e48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1366e4c: ldr             x4, [x4, #0xa08]
    // 0x1366e50: StoreField: r3->field_13 = r4
    //     0x1366e50: stur            w4, [x3, #0x13]
    // 0x1366e54: r5 = Instance_MainAxisSize
    //     0x1366e54: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1366e58: ldr             x5, [x5, #0xa10]
    // 0x1366e5c: ArrayStore: r3[0] = r5  ; List_4
    //     0x1366e5c: stur            w5, [x3, #0x17]
    // 0x1366e60: r1 = Instance_CrossAxisAlignment
    //     0x1366e60: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1366e64: ldr             x1, [x1, #0xa18]
    // 0x1366e68: StoreField: r3->field_1b = r1
    //     0x1366e68: stur            w1, [x3, #0x1b]
    // 0x1366e6c: r6 = Instance_VerticalDirection
    //     0x1366e6c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1366e70: ldr             x6, [x6, #0xa20]
    // 0x1366e74: StoreField: r3->field_23 = r6
    //     0x1366e74: stur            w6, [x3, #0x23]
    // 0x1366e78: r7 = Instance_Clip
    //     0x1366e78: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1366e7c: ldr             x7, [x7, #0x38]
    // 0x1366e80: StoreField: r3->field_2b = r7
    //     0x1366e80: stur            w7, [x3, #0x2b]
    // 0x1366e84: StoreField: r3->field_2f = rZR
    //     0x1366e84: stur            xzr, [x3, #0x2f]
    // 0x1366e88: ldur            x1, [fp, #-0x10]
    // 0x1366e8c: StoreField: r3->field_b = r1
    //     0x1366e8c: stur            w1, [x3, #0xb]
    // 0x1366e90: r1 = Null
    //     0x1366e90: mov             x1, NULL
    // 0x1366e94: r2 = 4
    //     0x1366e94: movz            x2, #0x4
    // 0x1366e98: r0 = AllocateArray()
    //     0x1366e98: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1366e9c: mov             x2, x0
    // 0x1366ea0: ldur            x0, [fp, #-0x28]
    // 0x1366ea4: stur            x2, [fp, #-0x10]
    // 0x1366ea8: StoreField: r2->field_f = r0
    //     0x1366ea8: stur            w0, [x2, #0xf]
    // 0x1366eac: ldur            x0, [fp, #-0x20]
    // 0x1366eb0: StoreField: r2->field_13 = r0
    //     0x1366eb0: stur            w0, [x2, #0x13]
    // 0x1366eb4: r1 = <Widget>
    //     0x1366eb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1366eb8: r0 = AllocateGrowableArray()
    //     0x1366eb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1366ebc: mov             x1, x0
    // 0x1366ec0: ldur            x0, [fp, #-0x10]
    // 0x1366ec4: stur            x1, [fp, #-0x20]
    // 0x1366ec8: StoreField: r1->field_f = r0
    //     0x1366ec8: stur            w0, [x1, #0xf]
    // 0x1366ecc: r0 = 4
    //     0x1366ecc: movz            x0, #0x4
    // 0x1366ed0: StoreField: r1->field_b = r0
    //     0x1366ed0: stur            w0, [x1, #0xb]
    // 0x1366ed4: r0 = Column()
    //     0x1366ed4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1366ed8: mov             x1, x0
    // 0x1366edc: r0 = Instance_Axis
    //     0x1366edc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1366ee0: stur            x1, [fp, #-0x10]
    // 0x1366ee4: StoreField: r1->field_f = r0
    //     0x1366ee4: stur            w0, [x1, #0xf]
    // 0x1366ee8: r0 = Instance_MainAxisAlignment
    //     0x1366ee8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1366eec: ldr             x0, [x0, #0xa08]
    // 0x1366ef0: StoreField: r1->field_13 = r0
    //     0x1366ef0: stur            w0, [x1, #0x13]
    // 0x1366ef4: r2 = Instance_MainAxisSize
    //     0x1366ef4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1366ef8: ldr             x2, [x2, #0xa10]
    // 0x1366efc: ArrayStore: r1[0] = r2  ; List_4
    //     0x1366efc: stur            w2, [x1, #0x17]
    // 0x1366f00: r3 = Instance_CrossAxisAlignment
    //     0x1366f00: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1366f04: ldr             x3, [x3, #0x890]
    // 0x1366f08: StoreField: r1->field_1b = r3
    //     0x1366f08: stur            w3, [x1, #0x1b]
    // 0x1366f0c: r4 = Instance_VerticalDirection
    //     0x1366f0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1366f10: ldr             x4, [x4, #0xa20]
    // 0x1366f14: StoreField: r1->field_23 = r4
    //     0x1366f14: stur            w4, [x1, #0x23]
    // 0x1366f18: r5 = Instance_Clip
    //     0x1366f18: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1366f1c: ldr             x5, [x5, #0x38]
    // 0x1366f20: StoreField: r1->field_2b = r5
    //     0x1366f20: stur            w5, [x1, #0x2b]
    // 0x1366f24: StoreField: r1->field_2f = rZR
    //     0x1366f24: stur            xzr, [x1, #0x2f]
    // 0x1366f28: ldur            x6, [fp, #-0x20]
    // 0x1366f2c: StoreField: r1->field_b = r6
    //     0x1366f2c: stur            w6, [x1, #0xb]
    // 0x1366f30: r16 = <EdgeInsets>
    //     0x1366f30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1366f34: ldr             x16, [x16, #0xda0]
    // 0x1366f38: r30 = Instance_EdgeInsets
    //     0x1366f38: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1366f3c: ldr             lr, [lr, #0x1f0]
    // 0x1366f40: stp             lr, x16, [SP]
    // 0x1366f44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1366f44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1366f48: r0 = all()
    //     0x1366f48: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1366f4c: ldur            x2, [fp, #-8]
    // 0x1366f50: stur            x0, [fp, #-0x20]
    // 0x1366f54: LoadField: r1 = r2->field_13
    //     0x1366f54: ldur            w1, [x2, #0x13]
    // 0x1366f58: DecompressPointer r1
    //     0x1366f58: add             x1, x1, HEAP, lsl #32
    // 0x1366f5c: r0 = of()
    //     0x1366f5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1366f60: LoadField: r1 = r0->field_5b
    //     0x1366f60: ldur            w1, [x0, #0x5b]
    // 0x1366f64: DecompressPointer r1
    //     0x1366f64: add             x1, x1, HEAP, lsl #32
    // 0x1366f68: r16 = <Color>
    //     0x1366f68: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1366f6c: ldr             x16, [x16, #0xf80]
    // 0x1366f70: stp             x1, x16, [SP]
    // 0x1366f74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1366f74: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1366f78: r0 = all()
    //     0x1366f78: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1366f7c: ldur            x2, [fp, #-8]
    // 0x1366f80: stur            x0, [fp, #-0x28]
    // 0x1366f84: LoadField: r1 = r2->field_13
    //     0x1366f84: ldur            w1, [x2, #0x13]
    // 0x1366f88: DecompressPointer r1
    //     0x1366f88: add             x1, x1, HEAP, lsl #32
    // 0x1366f8c: r0 = of()
    //     0x1366f8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1366f90: LoadField: r1 = r0->field_5b
    //     0x1366f90: ldur            w1, [x0, #0x5b]
    // 0x1366f94: DecompressPointer r1
    //     0x1366f94: add             x1, x1, HEAP, lsl #32
    // 0x1366f98: stur            x1, [fp, #-0x30]
    // 0x1366f9c: r0 = BorderSide()
    //     0x1366f9c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1366fa0: mov             x1, x0
    // 0x1366fa4: ldur            x0, [fp, #-0x30]
    // 0x1366fa8: stur            x1, [fp, #-0x38]
    // 0x1366fac: StoreField: r1->field_7 = r0
    //     0x1366fac: stur            w0, [x1, #7]
    // 0x1366fb0: d0 = 1.000000
    //     0x1366fb0: fmov            d0, #1.00000000
    // 0x1366fb4: StoreField: r1->field_b = d0
    //     0x1366fb4: stur            d0, [x1, #0xb]
    // 0x1366fb8: r0 = Instance_BorderStyle
    //     0x1366fb8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1366fbc: ldr             x0, [x0, #0xf68]
    // 0x1366fc0: StoreField: r1->field_13 = r0
    //     0x1366fc0: stur            w0, [x1, #0x13]
    // 0x1366fc4: d0 = -1.000000
    //     0x1366fc4: fmov            d0, #-1.00000000
    // 0x1366fc8: ArrayStore: r1[0] = d0  ; List_8
    //     0x1366fc8: stur            d0, [x1, #0x17]
    // 0x1366fcc: r0 = RoundedRectangleBorder()
    //     0x1366fcc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1366fd0: mov             x1, x0
    // 0x1366fd4: r0 = Instance_BorderRadius
    //     0x1366fd4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1366fd8: ldr             x0, [x0, #0xf70]
    // 0x1366fdc: StoreField: r1->field_b = r0
    //     0x1366fdc: stur            w0, [x1, #0xb]
    // 0x1366fe0: ldur            x0, [fp, #-0x38]
    // 0x1366fe4: StoreField: r1->field_7 = r0
    //     0x1366fe4: stur            w0, [x1, #7]
    // 0x1366fe8: r16 = <RoundedRectangleBorder>
    //     0x1366fe8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1366fec: ldr             x16, [x16, #0xf78]
    // 0x1366ff0: stp             x1, x16, [SP]
    // 0x1366ff4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1366ff4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1366ff8: r0 = all()
    //     0x1366ff8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1366ffc: stur            x0, [fp, #-0x30]
    // 0x1367000: r0 = ButtonStyle()
    //     0x1367000: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1367004: mov             x1, x0
    // 0x1367008: ldur            x0, [fp, #-0x28]
    // 0x136700c: stur            x1, [fp, #-0x38]
    // 0x1367010: StoreField: r1->field_b = r0
    //     0x1367010: stur            w0, [x1, #0xb]
    // 0x1367014: ldur            x0, [fp, #-0x20]
    // 0x1367018: StoreField: r1->field_23 = r0
    //     0x1367018: stur            w0, [x1, #0x23]
    // 0x136701c: ldur            x0, [fp, #-0x30]
    // 0x1367020: StoreField: r1->field_43 = r0
    //     0x1367020: stur            w0, [x1, #0x43]
    // 0x1367024: r0 = TextButtonThemeData()
    //     0x1367024: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1367028: mov             x2, x0
    // 0x136702c: ldur            x0, [fp, #-0x38]
    // 0x1367030: stur            x2, [fp, #-0x20]
    // 0x1367034: StoreField: r2->field_7 = r0
    //     0x1367034: stur            w0, [x2, #7]
    // 0x1367038: ldur            x0, [fp, #-8]
    // 0x136703c: LoadField: r1 = r0->field_13
    //     0x136703c: ldur            w1, [x0, #0x13]
    // 0x1367040: DecompressPointer r1
    //     0x1367040: add             x1, x1, HEAP, lsl #32
    // 0x1367044: r0 = of()
    //     0x1367044: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1367048: LoadField: r1 = r0->field_87
    //     0x1367048: ldur            w1, [x0, #0x87]
    // 0x136704c: DecompressPointer r1
    //     0x136704c: add             x1, x1, HEAP, lsl #32
    // 0x1367050: LoadField: r0 = r1->field_7
    //     0x1367050: ldur            w0, [x1, #7]
    // 0x1367054: DecompressPointer r0
    //     0x1367054: add             x0, x0, HEAP, lsl #32
    // 0x1367058: r16 = Instance_Color
    //     0x1367058: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x136705c: r30 = 14.000000
    //     0x136705c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1367060: ldr             lr, [lr, #0x1d8]
    // 0x1367064: stp             lr, x16, [SP]
    // 0x1367068: mov             x1, x0
    // 0x136706c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x136706c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1367070: ldr             x4, [x4, #0x9b8]
    // 0x1367074: r0 = copyWith()
    //     0x1367074: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1367078: stur            x0, [fp, #-0x28]
    // 0x136707c: r0 = Text()
    //     0x136707c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1367080: mov             x3, x0
    // 0x1367084: r0 = "PLACE ORDER"
    //     0x1367084: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c480] "PLACE ORDER"
    //     0x1367088: ldr             x0, [x0, #0x480]
    // 0x136708c: stur            x3, [fp, #-0x30]
    // 0x1367090: StoreField: r3->field_b = r0
    //     0x1367090: stur            w0, [x3, #0xb]
    // 0x1367094: ldur            x0, [fp, #-0x28]
    // 0x1367098: StoreField: r3->field_13 = r0
    //     0x1367098: stur            w0, [x3, #0x13]
    // 0x136709c: ldur            x2, [fp, #-8]
    // 0x13670a0: r1 = Function '<anonymous closure>':.
    //     0x13670a0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c488] AnonymousClosure: (0x136725c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::bottomNavigationBar (0x1366b84)
    //     0x13670a4: ldr             x1, [x1, #0x488]
    // 0x13670a8: r0 = AllocateClosure()
    //     0x13670a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13670ac: stur            x0, [fp, #-8]
    // 0x13670b0: r0 = TextButton()
    //     0x13670b0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13670b4: mov             x1, x0
    // 0x13670b8: ldur            x0, [fp, #-8]
    // 0x13670bc: stur            x1, [fp, #-0x28]
    // 0x13670c0: StoreField: r1->field_b = r0
    //     0x13670c0: stur            w0, [x1, #0xb]
    // 0x13670c4: r0 = false
    //     0x13670c4: add             x0, NULL, #0x30  ; false
    // 0x13670c8: StoreField: r1->field_27 = r0
    //     0x13670c8: stur            w0, [x1, #0x27]
    // 0x13670cc: r0 = true
    //     0x13670cc: add             x0, NULL, #0x20  ; true
    // 0x13670d0: StoreField: r1->field_2f = r0
    //     0x13670d0: stur            w0, [x1, #0x2f]
    // 0x13670d4: ldur            x0, [fp, #-0x30]
    // 0x13670d8: StoreField: r1->field_37 = r0
    //     0x13670d8: stur            w0, [x1, #0x37]
    // 0x13670dc: r0 = Instance_ValueKey
    //     0x13670dc: add             x0, PP, #0x39, lsl #12  ; [pp+0x39868] Obj!ValueKey<String>@d5b371
    //     0x13670e0: ldr             x0, [x0, #0x868]
    // 0x13670e4: StoreField: r1->field_7 = r0
    //     0x13670e4: stur            w0, [x1, #7]
    // 0x13670e8: r0 = TextButtonTheme()
    //     0x13670e8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13670ec: mov             x3, x0
    // 0x13670f0: ldur            x0, [fp, #-0x20]
    // 0x13670f4: stur            x3, [fp, #-8]
    // 0x13670f8: StoreField: r3->field_f = r0
    //     0x13670f8: stur            w0, [x3, #0xf]
    // 0x13670fc: ldur            x0, [fp, #-0x28]
    // 0x1367100: StoreField: r3->field_b = r0
    //     0x1367100: stur            w0, [x3, #0xb]
    // 0x1367104: r1 = Null
    //     0x1367104: mov             x1, NULL
    // 0x1367108: r2 = 6
    //     0x1367108: movz            x2, #0x6
    // 0x136710c: r0 = AllocateArray()
    //     0x136710c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1367110: mov             x2, x0
    // 0x1367114: ldur            x0, [fp, #-0x10]
    // 0x1367118: stur            x2, [fp, #-0x20]
    // 0x136711c: StoreField: r2->field_f = r0
    //     0x136711c: stur            w0, [x2, #0xf]
    // 0x1367120: r16 = Instance_Spacer
    //     0x1367120: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x1367124: ldr             x16, [x16, #0xf0]
    // 0x1367128: StoreField: r2->field_13 = r16
    //     0x1367128: stur            w16, [x2, #0x13]
    // 0x136712c: ldur            x0, [fp, #-8]
    // 0x1367130: ArrayStore: r2[0] = r0  ; List_4
    //     0x1367130: stur            w0, [x2, #0x17]
    // 0x1367134: r1 = <Widget>
    //     0x1367134: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1367138: r0 = AllocateGrowableArray()
    //     0x1367138: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x136713c: mov             x1, x0
    // 0x1367140: ldur            x0, [fp, #-0x20]
    // 0x1367144: stur            x1, [fp, #-8]
    // 0x1367148: StoreField: r1->field_f = r0
    //     0x1367148: stur            w0, [x1, #0xf]
    // 0x136714c: r0 = 6
    //     0x136714c: movz            x0, #0x6
    // 0x1367150: StoreField: r1->field_b = r0
    //     0x1367150: stur            w0, [x1, #0xb]
    // 0x1367154: r0 = Row()
    //     0x1367154: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1367158: mov             x1, x0
    // 0x136715c: r0 = Instance_Axis
    //     0x136715c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1367160: stur            x1, [fp, #-0x10]
    // 0x1367164: StoreField: r1->field_f = r0
    //     0x1367164: stur            w0, [x1, #0xf]
    // 0x1367168: r0 = Instance_MainAxisAlignment
    //     0x1367168: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x136716c: ldr             x0, [x0, #0xa08]
    // 0x1367170: StoreField: r1->field_13 = r0
    //     0x1367170: stur            w0, [x1, #0x13]
    // 0x1367174: r0 = Instance_MainAxisSize
    //     0x1367174: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1367178: ldr             x0, [x0, #0xa10]
    // 0x136717c: ArrayStore: r1[0] = r0  ; List_4
    //     0x136717c: stur            w0, [x1, #0x17]
    // 0x1367180: r0 = Instance_CrossAxisAlignment
    //     0x1367180: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1367184: ldr             x0, [x0, #0x890]
    // 0x1367188: StoreField: r1->field_1b = r0
    //     0x1367188: stur            w0, [x1, #0x1b]
    // 0x136718c: r0 = Instance_VerticalDirection
    //     0x136718c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1367190: ldr             x0, [x0, #0xa20]
    // 0x1367194: StoreField: r1->field_23 = r0
    //     0x1367194: stur            w0, [x1, #0x23]
    // 0x1367198: r0 = Instance_Clip
    //     0x1367198: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x136719c: ldr             x0, [x0, #0x38]
    // 0x13671a0: StoreField: r1->field_2b = r0
    //     0x13671a0: stur            w0, [x1, #0x2b]
    // 0x13671a4: StoreField: r1->field_2f = rZR
    //     0x13671a4: stur            xzr, [x1, #0x2f]
    // 0x13671a8: ldur            x0, [fp, #-8]
    // 0x13671ac: StoreField: r1->field_b = r0
    //     0x13671ac: stur            w0, [x1, #0xb]
    // 0x13671b0: r0 = Padding()
    //     0x13671b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13671b4: mov             x1, x0
    // 0x13671b8: r0 = Instance_EdgeInsets
    //     0x13671b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13671bc: ldr             x0, [x0, #0x1f0]
    // 0x13671c0: stur            x1, [fp, #-8]
    // 0x13671c4: StoreField: r1->field_f = r0
    //     0x13671c4: stur            w0, [x1, #0xf]
    // 0x13671c8: ldur            x0, [fp, #-0x10]
    // 0x13671cc: StoreField: r1->field_b = r0
    //     0x13671cc: stur            w0, [x1, #0xb]
    // 0x13671d0: r0 = Container()
    //     0x13671d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13671d4: stur            x0, [fp, #-0x10]
    // 0x13671d8: ldur            x16, [fp, #-0x18]
    // 0x13671dc: ldur            lr, [fp, #-8]
    // 0x13671e0: stp             lr, x16, [SP]
    // 0x13671e4: mov             x1, x0
    // 0x13671e8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13671e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13671ec: ldr             x4, [x4, #0x88]
    // 0x13671f0: r0 = Container()
    //     0x13671f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13671f4: ldur            d0, [fp, #-0x40]
    // 0x13671f8: r0 = inline_Allocate_Double()
    //     0x13671f8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13671fc: add             x0, x0, #0x10
    //     0x1367200: cmp             x1, x0
    //     0x1367204: b.ls            #0x136724c
    //     0x1367208: str             x0, [THR, #0x50]  ; THR::top
    //     0x136720c: sub             x0, x0, #0xf
    //     0x1367210: movz            x1, #0xe15c
    //     0x1367214: movk            x1, #0x3, lsl #16
    //     0x1367218: stur            x1, [x0, #-1]
    // 0x136721c: StoreField: r0->field_7 = d0
    //     0x136721c: stur            d0, [x0, #7]
    // 0x1367220: stur            x0, [fp, #-8]
    // 0x1367224: r0 = SizedBox()
    //     0x1367224: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1367228: ldur            x1, [fp, #-8]
    // 0x136722c: StoreField: r0->field_13 = r1
    //     0x136722c: stur            w1, [x0, #0x13]
    // 0x1367230: ldur            x1, [fp, #-0x10]
    // 0x1367234: StoreField: r0->field_b = r1
    //     0x1367234: stur            w1, [x0, #0xb]
    // 0x1367238: LeaveFrame
    //     0x1367238: mov             SP, fp
    //     0x136723c: ldp             fp, lr, [SP], #0x10
    // 0x1367240: ret
    //     0x1367240: ret             
    // 0x1367244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1367244: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1367248: b               #0x1366c10
    // 0x136724c: SaveReg d0
    //     0x136724c: str             q0, [SP, #-0x10]!
    // 0x1367250: r0 = AllocateDouble()
    //     0x1367250: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1367254: RestoreReg d0
    //     0x1367254: ldr             q0, [SP], #0x10
    // 0x1367258: b               #0x136721c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x136725c, size: 0xf8
    // 0x136725c: EnterFrame
    //     0x136725c: stp             fp, lr, [SP, #-0x10]!
    //     0x1367260: mov             fp, SP
    // 0x1367264: AllocStack(0x20)
    //     0x1367264: sub             SP, SP, #0x20
    // 0x1367268: SetupParameters()
    //     0x1367268: ldr             x0, [fp, #0x10]
    //     0x136726c: ldur            w2, [x0, #0x17]
    //     0x1367270: add             x2, x2, HEAP, lsl #32
    //     0x1367274: stur            x2, [fp, #-8]
    // 0x1367278: CheckStackOverflow
    //     0x1367278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x136727c: cmp             SP, x16
    //     0x1367280: b.ls            #0x136734c
    // 0x1367284: LoadField: r1 = r2->field_f
    //     0x1367284: ldur            w1, [x2, #0xf]
    // 0x1367288: DecompressPointer r1
    //     0x1367288: add             x1, x1, HEAP, lsl #32
    // 0x136728c: r0 = controller()
    //     0x136728c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1367290: mov             x1, x0
    // 0x1367294: r0 = couponType()
    //     0x1367294: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x1367298: r1 = 60
    //     0x1367298: movz            x1, #0x3c
    // 0x136729c: branchIfSmi(r0, 0x13672a8)
    //     0x136729c: tbz             w0, #0, #0x13672a8
    // 0x13672a0: r1 = LoadClassIdInstr(r0)
    //     0x13672a0: ldur            x1, [x0, #-1]
    //     0x13672a4: ubfx            x1, x1, #0xc, #0x14
    // 0x13672a8: r16 = ""
    //     0x13672a8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13672ac: stp             x16, x0, [SP]
    // 0x13672b0: mov             x0, x1
    // 0x13672b4: mov             lr, x0
    // 0x13672b8: ldr             lr, [x21, lr, lsl #3]
    // 0x13672bc: blr             lr
    // 0x13672c0: tbz             w0, #4, #0x1367324
    // 0x13672c4: ldur            x0, [fp, #-8]
    // 0x13672c8: LoadField: r1 = r0->field_f
    //     0x13672c8: ldur            w1, [x0, #0xf]
    // 0x13672cc: DecompressPointer r1
    //     0x13672cc: add             x1, x1, HEAP, lsl #32
    // 0x13672d0: r0 = controller()
    //     0x13672d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13672d4: mov             x2, x0
    // 0x13672d8: ldur            x0, [fp, #-8]
    // 0x13672dc: stur            x2, [fp, #-0x10]
    // 0x13672e0: LoadField: r1 = r0->field_f
    //     0x13672e0: ldur            w1, [x0, #0xf]
    // 0x13672e4: DecompressPointer r1
    //     0x13672e4: add             x1, x1, HEAP, lsl #32
    // 0x13672e8: r0 = controller()
    //     0x13672e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13672ec: mov             x1, x0
    // 0x13672f0: r0 = couponType()
    //     0x13672f0: bl              #0x8a36ec  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::couponType
    // 0x13672f4: ldur            x1, [fp, #-0x10]
    // 0x13672f8: r0 = initPaymentDetails()
    //     0x13672f8: bl              #0x12db22c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::initPaymentDetails
    // 0x13672fc: ldur            x0, [fp, #-8]
    // 0x1367300: LoadField: r1 = r0->field_f
    //     0x1367300: ldur            w1, [x0, #0xf]
    // 0x1367304: DecompressPointer r1
    //     0x1367304: add             x1, x1, HEAP, lsl #32
    // 0x1367308: r0 = controller()
    //     0x1367308: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136730c: mov             x1, x0
    // 0x1367310: r2 = "checkout_online_payment_method_selected"
    //     0x1367310: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c490] "checkout_online_payment_method_selected"
    //     0x1367314: ldr             x2, [x2, #0x490]
    // 0x1367318: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1367318: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x136731c: r0 = checkoutPostEvent()
    //     0x136731c: bl              #0x1367354  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::checkoutPostEvent
    // 0x1367320: b               #0x136733c
    // 0x1367324: ldur            x0, [fp, #-8]
    // 0x1367328: LoadField: r1 = r0->field_f
    //     0x1367328: ldur            w1, [x0, #0xf]
    // 0x136732c: DecompressPointer r1
    //     0x136732c: add             x1, x1, HEAP, lsl #32
    // 0x1367330: r2 = "Please select any payment mode!"
    //     0x1367330: add             x2, PP, #0x38, lsl #12  ; [pp+0x38198] "Please select any payment mode!"
    //     0x1367334: ldr             x2, [x2, #0x198]
    // 0x1367338: r0 = showErrorSnackBar()
    //     0x1367338: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x136733c: r0 = Null
    //     0x136733c: mov             x0, NULL
    // 0x1367340: LeaveFrame
    //     0x1367340: mov             SP, fp
    //     0x1367344: ldp             fp, lr, [SP], #0x10
    // 0x1367348: ret
    //     0x1367348: ret             
    // 0x136734c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x136734c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1367350: b               #0x1367284
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13ace4c, size: 0x94
    // 0x13ace4c: EnterFrame
    //     0x13ace4c: stp             fp, lr, [SP, #-0x10]!
    //     0x13ace50: mov             fp, SP
    // 0x13ace54: AllocStack(0x8)
    //     0x13ace54: sub             SP, SP, #8
    // 0x13ace58: SetupParameters()
    //     0x13ace58: ldr             x0, [fp, #0x10]
    //     0x13ace5c: ldur            w2, [x0, #0x17]
    //     0x13ace60: add             x2, x2, HEAP, lsl #32
    //     0x13ace64: stur            x2, [fp, #-8]
    // 0x13ace68: CheckStackOverflow
    //     0x13ace68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ace6c: cmp             SP, x16
    //     0x13ace70: b.ls            #0x13aced8
    // 0x13ace74: LoadField: r1 = r2->field_f
    //     0x13ace74: ldur            w1, [x2, #0xf]
    // 0x13ace78: DecompressPointer r1
    //     0x13ace78: add             x1, x1, HEAP, lsl #32
    // 0x13ace7c: r0 = controller()
    //     0x13ace7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ace80: mov             x1, x0
    // 0x13ace84: ldur            x0, [fp, #-8]
    // 0x13ace88: LoadField: r2 = r0->field_13
    //     0x13ace88: ldur            w2, [x0, #0x13]
    // 0x13ace8c: DecompressPointer r2
    //     0x13ace8c: add             x2, x2, HEAP, lsl #32
    // 0x13ace90: cmp             w2, NULL
    // 0x13ace94: b.ne            #0x13acea0
    // 0x13ace98: r2 = Null
    //     0x13ace98: mov             x2, NULL
    // 0x13ace9c: b               #0x13aceac
    // 0x13acea0: LoadField: r3 = r2->field_f
    //     0x13acea0: ldur            w3, [x2, #0xf]
    // 0x13acea4: DecompressPointer r3
    //     0x13acea4: add             x3, x3, HEAP, lsl #32
    // 0x13acea8: mov             x2, x3
    // 0x13aceac: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x13aceac: ldur            w3, [x0, #0x17]
    // 0x13aceb0: DecompressPointer r3
    //     0x13aceb0: add             x3, x3, HEAP, lsl #32
    // 0x13aceb4: r0 = LoadInt32Instr(r3)
    //     0x13aceb4: sbfx            x0, x3, #1, #0x1f
    //     0x13aceb8: tbz             w3, #0, #0x13acec0
    //     0x13acebc: ldur            x0, [x3, #7]
    // 0x13acec0: mov             x3, x0
    // 0x13acec4: r0 = setOnlinePaymentMethod()
    //     0x13acec4: bl              #0x13ad324  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::setOnlinePaymentMethod
    // 0x13acec8: r0 = Null
    //     0x13acec8: mov             x0, NULL
    // 0x13acecc: LeaveFrame
    //     0x13acecc: mov             SP, fp
    //     0x13aced0: ldp             fp, lr, [SP], #0x10
    // 0x13aced4: ret
    //     0x13aced4: ret             
    // 0x13aced8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13aced8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13acedc: b               #0x13ace74
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0x13acee0, size: 0x3b4
    // 0x13acee0: EnterFrame
    //     0x13acee0: stp             fp, lr, [SP, #-0x10]!
    //     0x13acee4: mov             fp, SP
    // 0x13acee8: AllocStack(0x40)
    //     0x13acee8: sub             SP, SP, #0x40
    // 0x13aceec: SetupParameters()
    //     0x13aceec: ldr             x0, [fp, #0x10]
    //     0x13acef0: ldur            w2, [x0, #0x17]
    //     0x13acef4: add             x2, x2, HEAP, lsl #32
    //     0x13acef8: stur            x2, [fp, #-8]
    // 0x13acefc: CheckStackOverflow
    //     0x13acefc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13acf00: cmp             SP, x16
    //     0x13acf04: b.ls            #0x13ad28c
    // 0x13acf08: LoadField: r1 = r2->field_f
    //     0x13acf08: ldur            w1, [x2, #0xf]
    // 0x13acf0c: DecompressPointer r1
    //     0x13acf0c: add             x1, x1, HEAP, lsl #32
    // 0x13acf10: r0 = controller()
    //     0x13acf10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13acf14: LoadField: r1 = r0->field_7f
    //     0x13acf14: ldur            w1, [x0, #0x7f]
    // 0x13acf18: DecompressPointer r1
    //     0x13acf18: add             x1, x1, HEAP, lsl #32
    // 0x13acf1c: r0 = value()
    //     0x13acf1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13acf20: ldur            x2, [fp, #-8]
    // 0x13acf24: LoadField: r1 = r2->field_13
    //     0x13acf24: ldur            w1, [x2, #0x13]
    // 0x13acf28: DecompressPointer r1
    //     0x13acf28: add             x1, x1, HEAP, lsl #32
    // 0x13acf2c: cmp             w1, NULL
    // 0x13acf30: b.ne            #0x13acf3c
    // 0x13acf34: r1 = Null
    //     0x13acf34: mov             x1, NULL
    // 0x13acf38: b               #0x13acf48
    // 0x13acf3c: LoadField: r3 = r1->field_f
    //     0x13acf3c: ldur            w3, [x1, #0xf]
    // 0x13acf40: DecompressPointer r3
    //     0x13acf40: add             x3, x3, HEAP, lsl #32
    // 0x13acf44: mov             x1, x3
    // 0x13acf48: r3 = 60
    //     0x13acf48: movz            x3, #0x3c
    // 0x13acf4c: branchIfSmi(r0, 0x13acf58)
    //     0x13acf4c: tbz             w0, #0, #0x13acf58
    // 0x13acf50: r3 = LoadClassIdInstr(r0)
    //     0x13acf50: ldur            x3, [x0, #-1]
    //     0x13acf54: ubfx            x3, x3, #0xc, #0x14
    // 0x13acf58: stp             x1, x0, [SP]
    // 0x13acf5c: mov             x0, x3
    // 0x13acf60: mov             lr, x0
    // 0x13acf64: ldr             lr, [x21, lr, lsl #3]
    // 0x13acf68: blr             lr
    // 0x13acf6c: tbnz            w0, #4, #0x13acfb4
    // 0x13acf70: ldur            x2, [fp, #-8]
    // 0x13acf74: LoadField: r1 = r2->field_f
    //     0x13acf74: ldur            w1, [x2, #0xf]
    // 0x13acf78: DecompressPointer r1
    //     0x13acf78: add             x1, x1, HEAP, lsl #32
    // 0x13acf7c: r0 = controller()
    //     0x13acf7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13acf80: mov             x1, x0
    // 0x13acf84: r0 = paymentSelectedIndex()
    //     0x13acf84: bl              #0x13ace04  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::paymentSelectedIndex
    // 0x13acf88: ldur            x2, [fp, #-8]
    // 0x13acf8c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x13acf8c: ldur            w1, [x2, #0x17]
    // 0x13acf90: DecompressPointer r1
    //     0x13acf90: add             x1, x1, HEAP, lsl #32
    // 0x13acf94: r3 = LoadInt32Instr(r1)
    //     0x13acf94: sbfx            x3, x1, #1, #0x1f
    //     0x13acf98: tbz             w1, #0, #0x13acfa0
    //     0x13acf9c: ldur            x3, [x1, #7]
    // 0x13acfa0: cmp             x0, x3
    // 0x13acfa4: b.ne            #0x13acfb8
    // 0x13acfa8: r0 = Instance_IconData
    //     0x13acfa8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x13acfac: ldr             x0, [x0, #0x30]
    // 0x13acfb0: b               #0x13acfc0
    // 0x13acfb4: ldur            x2, [fp, #-8]
    // 0x13acfb8: r0 = Instance_IconData
    //     0x13acfb8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x13acfbc: ldr             x0, [x0, #0x38]
    // 0x13acfc0: stur            x0, [fp, #-0x10]
    // 0x13acfc4: LoadField: r1 = r2->field_1b
    //     0x13acfc4: ldur            w1, [x2, #0x1b]
    // 0x13acfc8: DecompressPointer r1
    //     0x13acfc8: add             x1, x1, HEAP, lsl #32
    // 0x13acfcc: r0 = of()
    //     0x13acfcc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13acfd0: LoadField: r1 = r0->field_5b
    //     0x13acfd0: ldur            w1, [x0, #0x5b]
    // 0x13acfd4: DecompressPointer r1
    //     0x13acfd4: add             x1, x1, HEAP, lsl #32
    // 0x13acfd8: stur            x1, [fp, #-0x18]
    // 0x13acfdc: r0 = Icon()
    //     0x13acfdc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x13acfe0: mov             x1, x0
    // 0x13acfe4: ldur            x0, [fp, #-0x10]
    // 0x13acfe8: stur            x1, [fp, #-0x20]
    // 0x13acfec: StoreField: r1->field_b = r0
    //     0x13acfec: stur            w0, [x1, #0xb]
    // 0x13acff0: ldur            x0, [fp, #-0x18]
    // 0x13acff4: StoreField: r1->field_23 = r0
    //     0x13acff4: stur            w0, [x1, #0x23]
    // 0x13acff8: ldur            x2, [fp, #-8]
    // 0x13acffc: LoadField: r0 = r2->field_13
    //     0x13acffc: ldur            w0, [x2, #0x13]
    // 0x13ad000: DecompressPointer r0
    //     0x13ad000: add             x0, x0, HEAP, lsl #32
    // 0x13ad004: cmp             w0, NULL
    // 0x13ad008: b.ne            #0x13ad014
    // 0x13ad00c: r0 = Null
    //     0x13ad00c: mov             x0, NULL
    // 0x13ad010: b               #0x13ad020
    // 0x13ad014: LoadField: r3 = r0->field_7
    //     0x13ad014: ldur            w3, [x0, #7]
    // 0x13ad018: DecompressPointer r3
    //     0x13ad018: add             x3, x3, HEAP, lsl #32
    // 0x13ad01c: mov             x0, x3
    // 0x13ad020: cmp             w0, NULL
    // 0x13ad024: b.ne            #0x13ad02c
    // 0x13ad028: r0 = ""
    //     0x13ad028: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13ad02c: stur            x0, [fp, #-0x10]
    // 0x13ad030: r0 = CachedNetworkImage()
    //     0x13ad030: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13ad034: stur            x0, [fp, #-0x18]
    // 0x13ad038: r16 = 48.000000
    //     0x13ad038: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13ad03c: ldr             x16, [x16, #0xad8]
    // 0x13ad040: r30 = 48.000000
    //     0x13ad040: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13ad044: ldr             lr, [lr, #0xad8]
    // 0x13ad048: stp             lr, x16, [SP]
    // 0x13ad04c: mov             x1, x0
    // 0x13ad050: ldur            x2, [fp, #-0x10]
    // 0x13ad054: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x13ad054: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x13ad058: ldr             x4, [x4, #0x900]
    // 0x13ad05c: r0 = CachedNetworkImage()
    //     0x13ad05c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13ad060: r0 = Padding()
    //     0x13ad060: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ad064: mov             x2, x0
    // 0x13ad068: r0 = Instance_EdgeInsets
    //     0x13ad068: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x13ad06c: ldr             x0, [x0, #0xa78]
    // 0x13ad070: stur            x2, [fp, #-0x28]
    // 0x13ad074: StoreField: r2->field_f = r0
    //     0x13ad074: stur            w0, [x2, #0xf]
    // 0x13ad078: ldur            x1, [fp, #-0x18]
    // 0x13ad07c: StoreField: r2->field_b = r1
    //     0x13ad07c: stur            w1, [x2, #0xb]
    // 0x13ad080: ldur            x3, [fp, #-8]
    // 0x13ad084: LoadField: r1 = r3->field_13
    //     0x13ad084: ldur            w1, [x3, #0x13]
    // 0x13ad088: DecompressPointer r1
    //     0x13ad088: add             x1, x1, HEAP, lsl #32
    // 0x13ad08c: cmp             w1, NULL
    // 0x13ad090: b.ne            #0x13ad09c
    // 0x13ad094: r1 = Null
    //     0x13ad094: mov             x1, NULL
    // 0x13ad098: b               #0x13ad0a8
    // 0x13ad09c: LoadField: r4 = r1->field_b
    //     0x13ad09c: ldur            w4, [x1, #0xb]
    // 0x13ad0a0: DecompressPointer r4
    //     0x13ad0a0: add             x4, x4, HEAP, lsl #32
    // 0x13ad0a4: mov             x1, x4
    // 0x13ad0a8: cmp             w1, NULL
    // 0x13ad0ac: b.ne            #0x13ad0b8
    // 0x13ad0b0: r5 = ""
    //     0x13ad0b0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13ad0b4: b               #0x13ad0bc
    // 0x13ad0b8: mov             x5, x1
    // 0x13ad0bc: ldur            x4, [fp, #-0x20]
    // 0x13ad0c0: stur            x5, [fp, #-0x10]
    // 0x13ad0c4: LoadField: r1 = r3->field_1b
    //     0x13ad0c4: ldur            w1, [x3, #0x1b]
    // 0x13ad0c8: DecompressPointer r1
    //     0x13ad0c8: add             x1, x1, HEAP, lsl #32
    // 0x13ad0cc: r0 = of()
    //     0x13ad0cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ad0d0: LoadField: r1 = r0->field_87
    //     0x13ad0d0: ldur            w1, [x0, #0x87]
    // 0x13ad0d4: DecompressPointer r1
    //     0x13ad0d4: add             x1, x1, HEAP, lsl #32
    // 0x13ad0d8: LoadField: r0 = r1->field_2b
    //     0x13ad0d8: ldur            w0, [x1, #0x2b]
    // 0x13ad0dc: DecompressPointer r0
    //     0x13ad0dc: add             x0, x0, HEAP, lsl #32
    // 0x13ad0e0: r16 = 14.000000
    //     0x13ad0e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ad0e4: ldr             x16, [x16, #0x1d8]
    // 0x13ad0e8: r30 = Instance_Color
    //     0x13ad0e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ad0ec: stp             lr, x16, [SP]
    // 0x13ad0f0: mov             x1, x0
    // 0x13ad0f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13ad0f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13ad0f8: ldr             x4, [x4, #0xaa0]
    // 0x13ad0fc: r0 = copyWith()
    //     0x13ad0fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ad100: stur            x0, [fp, #-0x18]
    // 0x13ad104: r0 = Text()
    //     0x13ad104: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ad108: mov             x1, x0
    // 0x13ad10c: ldur            x0, [fp, #-0x10]
    // 0x13ad110: stur            x1, [fp, #-0x30]
    // 0x13ad114: StoreField: r1->field_b = r0
    //     0x13ad114: stur            w0, [x1, #0xb]
    // 0x13ad118: ldur            x0, [fp, #-0x18]
    // 0x13ad11c: StoreField: r1->field_13 = r0
    //     0x13ad11c: stur            w0, [x1, #0x13]
    // 0x13ad120: r0 = Padding()
    //     0x13ad120: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ad124: mov             x3, x0
    // 0x13ad128: r0 = Instance_EdgeInsets
    //     0x13ad128: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x13ad12c: ldr             x0, [x0, #0xa78]
    // 0x13ad130: stur            x3, [fp, #-0x10]
    // 0x13ad134: StoreField: r3->field_f = r0
    //     0x13ad134: stur            w0, [x3, #0xf]
    // 0x13ad138: ldur            x0, [fp, #-0x30]
    // 0x13ad13c: StoreField: r3->field_b = r0
    //     0x13ad13c: stur            w0, [x3, #0xb]
    // 0x13ad140: r1 = Null
    //     0x13ad140: mov             x1, NULL
    // 0x13ad144: r2 = 6
    //     0x13ad144: movz            x2, #0x6
    // 0x13ad148: r0 = AllocateArray()
    //     0x13ad148: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ad14c: mov             x2, x0
    // 0x13ad150: ldur            x0, [fp, #-0x20]
    // 0x13ad154: stur            x2, [fp, #-0x18]
    // 0x13ad158: StoreField: r2->field_f = r0
    //     0x13ad158: stur            w0, [x2, #0xf]
    // 0x13ad15c: ldur            x0, [fp, #-0x28]
    // 0x13ad160: StoreField: r2->field_13 = r0
    //     0x13ad160: stur            w0, [x2, #0x13]
    // 0x13ad164: ldur            x0, [fp, #-0x10]
    // 0x13ad168: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ad168: stur            w0, [x2, #0x17]
    // 0x13ad16c: r1 = <Widget>
    //     0x13ad16c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13ad170: r0 = AllocateGrowableArray()
    //     0x13ad170: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ad174: mov             x1, x0
    // 0x13ad178: ldur            x0, [fp, #-0x18]
    // 0x13ad17c: stur            x1, [fp, #-0x10]
    // 0x13ad180: StoreField: r1->field_f = r0
    //     0x13ad180: stur            w0, [x1, #0xf]
    // 0x13ad184: r0 = 6
    //     0x13ad184: movz            x0, #0x6
    // 0x13ad188: StoreField: r1->field_b = r0
    //     0x13ad188: stur            w0, [x1, #0xb]
    // 0x13ad18c: r0 = Row()
    //     0x13ad18c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13ad190: mov             x1, x0
    // 0x13ad194: r0 = Instance_Axis
    //     0x13ad194: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13ad198: stur            x1, [fp, #-0x18]
    // 0x13ad19c: StoreField: r1->field_f = r0
    //     0x13ad19c: stur            w0, [x1, #0xf]
    // 0x13ad1a0: r0 = Instance_MainAxisAlignment
    //     0x13ad1a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13ad1a4: ldr             x0, [x0, #0xa08]
    // 0x13ad1a8: StoreField: r1->field_13 = r0
    //     0x13ad1a8: stur            w0, [x1, #0x13]
    // 0x13ad1ac: r0 = Instance_MainAxisSize
    //     0x13ad1ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13ad1b0: ldr             x0, [x0, #0xa10]
    // 0x13ad1b4: ArrayStore: r1[0] = r0  ; List_4
    //     0x13ad1b4: stur            w0, [x1, #0x17]
    // 0x13ad1b8: r0 = Instance_CrossAxisAlignment
    //     0x13ad1b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13ad1bc: ldr             x0, [x0, #0xa18]
    // 0x13ad1c0: StoreField: r1->field_1b = r0
    //     0x13ad1c0: stur            w0, [x1, #0x1b]
    // 0x13ad1c4: r0 = Instance_VerticalDirection
    //     0x13ad1c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13ad1c8: ldr             x0, [x0, #0xa20]
    // 0x13ad1cc: StoreField: r1->field_23 = r0
    //     0x13ad1cc: stur            w0, [x1, #0x23]
    // 0x13ad1d0: r0 = Instance_Clip
    //     0x13ad1d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13ad1d4: ldr             x0, [x0, #0x38]
    // 0x13ad1d8: StoreField: r1->field_2b = r0
    //     0x13ad1d8: stur            w0, [x1, #0x2b]
    // 0x13ad1dc: StoreField: r1->field_2f = rZR
    //     0x13ad1dc: stur            xzr, [x1, #0x2f]
    // 0x13ad1e0: ldur            x0, [fp, #-0x10]
    // 0x13ad1e4: StoreField: r1->field_b = r0
    //     0x13ad1e4: stur            w0, [x1, #0xb]
    // 0x13ad1e8: r0 = Center()
    //     0x13ad1e8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13ad1ec: mov             x1, x0
    // 0x13ad1f0: r0 = Instance_Alignment
    //     0x13ad1f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13ad1f4: ldr             x0, [x0, #0xb10]
    // 0x13ad1f8: stur            x1, [fp, #-0x10]
    // 0x13ad1fc: StoreField: r1->field_f = r0
    //     0x13ad1fc: stur            w0, [x1, #0xf]
    // 0x13ad200: ldur            x0, [fp, #-0x18]
    // 0x13ad204: StoreField: r1->field_b = r0
    //     0x13ad204: stur            w0, [x1, #0xb]
    // 0x13ad208: r0 = Padding()
    //     0x13ad208: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ad20c: mov             x1, x0
    // 0x13ad210: r0 = Instance_EdgeInsets
    //     0x13ad210: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x13ad214: ldr             x0, [x0, #0xe68]
    // 0x13ad218: stur            x1, [fp, #-0x18]
    // 0x13ad21c: StoreField: r1->field_f = r0
    //     0x13ad21c: stur            w0, [x1, #0xf]
    // 0x13ad220: ldur            x0, [fp, #-0x10]
    // 0x13ad224: StoreField: r1->field_b = r0
    //     0x13ad224: stur            w0, [x1, #0xb]
    // 0x13ad228: r0 = InkWell()
    //     0x13ad228: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13ad22c: mov             x3, x0
    // 0x13ad230: ldur            x0, [fp, #-0x18]
    // 0x13ad234: stur            x3, [fp, #-0x10]
    // 0x13ad238: StoreField: r3->field_b = r0
    //     0x13ad238: stur            w0, [x3, #0xb]
    // 0x13ad23c: ldur            x2, [fp, #-8]
    // 0x13ad240: r1 = Function '<anonymous closure>':.
    //     0x13ad240: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6d0] AnonymousClosure: (0x13ace4c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::lineThemePaymentMethodCard (0x13ad294)
    //     0x13ad244: ldr             x1, [x1, #0x6d0]
    // 0x13ad248: r0 = AllocateClosure()
    //     0x13ad248: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ad24c: mov             x1, x0
    // 0x13ad250: ldur            x0, [fp, #-0x10]
    // 0x13ad254: StoreField: r0->field_f = r1
    //     0x13ad254: stur            w1, [x0, #0xf]
    // 0x13ad258: r1 = true
    //     0x13ad258: add             x1, NULL, #0x20  ; true
    // 0x13ad25c: StoreField: r0->field_43 = r1
    //     0x13ad25c: stur            w1, [x0, #0x43]
    // 0x13ad260: r2 = Instance_BoxShape
    //     0x13ad260: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13ad264: ldr             x2, [x2, #0x80]
    // 0x13ad268: StoreField: r0->field_47 = r2
    //     0x13ad268: stur            w2, [x0, #0x47]
    // 0x13ad26c: StoreField: r0->field_6f = r1
    //     0x13ad26c: stur            w1, [x0, #0x6f]
    // 0x13ad270: r2 = false
    //     0x13ad270: add             x2, NULL, #0x30  ; false
    // 0x13ad274: StoreField: r0->field_73 = r2
    //     0x13ad274: stur            w2, [x0, #0x73]
    // 0x13ad278: StoreField: r0->field_83 = r1
    //     0x13ad278: stur            w1, [x0, #0x83]
    // 0x13ad27c: StoreField: r0->field_7b = r2
    //     0x13ad27c: stur            w2, [x0, #0x7b]
    // 0x13ad280: LeaveFrame
    //     0x13ad280: mov             SP, fp
    //     0x13ad284: ldp             fp, lr, [SP], #0x10
    // 0x13ad288: ret
    //     0x13ad288: ret             
    // 0x13ad28c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ad28c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ad290: b               #0x13acf08
  }
  _ lineThemePaymentMethodCard(/* No info */) {
    // ** addr: 0x13ad294, size: 0x90
    // 0x13ad294: EnterFrame
    //     0x13ad294: stp             fp, lr, [SP, #-0x10]!
    //     0x13ad298: mov             fp, SP
    // 0x13ad29c: AllocStack(0x28)
    //     0x13ad29c: sub             SP, SP, #0x28
    // 0x13ad2a0: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x13ad2a0: stur            x1, [fp, #-8]
    //     0x13ad2a4: stur            x2, [fp, #-0x10]
    //     0x13ad2a8: stur            x3, [fp, #-0x18]
    //     0x13ad2ac: stur            x5, [fp, #-0x20]
    // 0x13ad2b0: r1 = 4
    //     0x13ad2b0: movz            x1, #0x4
    // 0x13ad2b4: r0 = AllocateContext()
    //     0x13ad2b4: bl              #0x16f6108  ; AllocateContextStub
    // 0x13ad2b8: mov             x2, x0
    // 0x13ad2bc: ldur            x0, [fp, #-8]
    // 0x13ad2c0: stur            x2, [fp, #-0x28]
    // 0x13ad2c4: StoreField: r2->field_f = r0
    //     0x13ad2c4: stur            w0, [x2, #0xf]
    // 0x13ad2c8: ldur            x0, [fp, #-0x10]
    // 0x13ad2cc: StoreField: r2->field_13 = r0
    //     0x13ad2cc: stur            w0, [x2, #0x13]
    // 0x13ad2d0: ldur            x3, [fp, #-0x18]
    // 0x13ad2d4: r0 = BoxInt64Instr(r3)
    //     0x13ad2d4: sbfiz           x0, x3, #1, #0x1f
    //     0x13ad2d8: cmp             x3, x0, asr #1
    //     0x13ad2dc: b.eq            #0x13ad2e8
    //     0x13ad2e0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x13ad2e4: stur            x3, [x0, #7]
    // 0x13ad2e8: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ad2e8: stur            w0, [x2, #0x17]
    // 0x13ad2ec: ldur            x0, [fp, #-0x20]
    // 0x13ad2f0: StoreField: r2->field_1b = r0
    //     0x13ad2f0: stur            w0, [x2, #0x1b]
    // 0x13ad2f4: r0 = Obx()
    //     0x13ad2f4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13ad2f8: ldur            x2, [fp, #-0x28]
    // 0x13ad2fc: r1 = Function '<anonymous closure>':.
    //     0x13ad2fc: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6c8] AnonymousClosure: (0x13acee0), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::lineThemePaymentMethodCard (0x13ad294)
    //     0x13ad300: ldr             x1, [x1, #0x6c8]
    // 0x13ad304: stur            x0, [fp, #-8]
    // 0x13ad308: r0 = AllocateClosure()
    //     0x13ad308: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ad30c: mov             x1, x0
    // 0x13ad310: ldur            x0, [fp, #-8]
    // 0x13ad314: StoreField: r0->field_b = r1
    //     0x13ad314: stur            w1, [x0, #0xb]
    // 0x13ad318: LeaveFrame
    //     0x13ad318: mov             SP, fp
    //     0x13ad31c: ldp             fp, lr, [SP], #0x10
    // 0x13ad320: ret
    //     0x13ad320: ret             
  }
  _ body(/* No info */) {
    // ** addr: 0x150131c, size: 0x64
    // 0x150131c: EnterFrame
    //     0x150131c: stp             fp, lr, [SP, #-0x10]!
    //     0x1501320: mov             fp, SP
    // 0x1501324: AllocStack(0x18)
    //     0x1501324: sub             SP, SP, #0x18
    // 0x1501328: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1501328: stur            x1, [fp, #-8]
    //     0x150132c: stur            x2, [fp, #-0x10]
    // 0x1501330: r1 = 2
    //     0x1501330: movz            x1, #0x2
    // 0x1501334: r0 = AllocateContext()
    //     0x1501334: bl              #0x16f6108  ; AllocateContextStub
    // 0x1501338: mov             x1, x0
    // 0x150133c: ldur            x0, [fp, #-8]
    // 0x1501340: stur            x1, [fp, #-0x18]
    // 0x1501344: StoreField: r1->field_f = r0
    //     0x1501344: stur            w0, [x1, #0xf]
    // 0x1501348: ldur            x0, [fp, #-0x10]
    // 0x150134c: StoreField: r1->field_13 = r0
    //     0x150134c: stur            w0, [x1, #0x13]
    // 0x1501350: r0 = Obx()
    //     0x1501350: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1501354: ldur            x2, [fp, #-0x18]
    // 0x1501358: r1 = Function '<anonymous closure>':.
    //     0x1501358: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c650] AnonymousClosure: (0x1501380), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x150131c)
    //     0x150135c: ldr             x1, [x1, #0x650]
    // 0x1501360: stur            x0, [fp, #-8]
    // 0x1501364: r0 = AllocateClosure()
    //     0x1501364: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1501368: mov             x1, x0
    // 0x150136c: ldur            x0, [fp, #-8]
    // 0x1501370: StoreField: r0->field_b = r1
    //     0x1501370: stur            w1, [x0, #0xb]
    // 0x1501374: LeaveFrame
    //     0x1501374: mov             SP, fp
    //     0x1501378: ldp             fp, lr, [SP], #0x10
    // 0x150137c: ret
    //     0x150137c: ret             
  }
  [closure] WillPopScope <anonymous closure>(dynamic) {
    // ** addr: 0x1501380, size: 0x1010
    // 0x1501380: EnterFrame
    //     0x1501380: stp             fp, lr, [SP, #-0x10]!
    //     0x1501384: mov             fp, SP
    // 0x1501388: AllocStack(0xa8)
    //     0x1501388: sub             SP, SP, #0xa8
    // 0x150138c: SetupParameters()
    //     0x150138c: ldr             x0, [fp, #0x10]
    //     0x1501390: ldur            w2, [x0, #0x17]
    //     0x1501394: add             x2, x2, HEAP, lsl #32
    //     0x1501398: stur            x2, [fp, #-0x10]
    // 0x150139c: CheckStackOverflow
    //     0x150139c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15013a0: cmp             SP, x16
    //     0x15013a4: b.ls            #0x1502388
    // 0x15013a8: LoadField: r1 = r2->field_f
    //     0x15013a8: ldur            w1, [x2, #0xf]
    // 0x15013ac: DecompressPointer r1
    //     0x15013ac: add             x1, x1, HEAP, lsl #32
    // 0x15013b0: stur            x1, [fp, #-8]
    // 0x15013b4: r0 = Obx()
    //     0x15013b4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15013b8: ldur            x2, [fp, #-0x10]
    // 0x15013bc: r1 = Function '<anonymous closure>':.
    //     0x15013bc: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c658] AnonymousClosure: (0x1504ac8), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x150131c)
    //     0x15013c0: ldr             x1, [x1, #0x658]
    // 0x15013c4: stur            x0, [fp, #-0x18]
    // 0x15013c8: r0 = AllocateClosure()
    //     0x15013c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15013cc: mov             x1, x0
    // 0x15013d0: ldur            x0, [fp, #-0x18]
    // 0x15013d4: StoreField: r0->field_b = r1
    //     0x15013d4: stur            w1, [x0, #0xb]
    // 0x15013d8: r1 = Null
    //     0x15013d8: mov             x1, NULL
    // 0x15013dc: r2 = 4
    //     0x15013dc: movz            x2, #0x4
    // 0x15013e0: r0 = AllocateArray()
    //     0x15013e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15013e4: stur            x0, [fp, #-0x20]
    // 0x15013e8: r16 = "Bag "
    //     0x15013e8: add             x16, PP, #0x34, lsl #12  ; [pp+0x346a8] "Bag "
    //     0x15013ec: ldr             x16, [x16, #0x6a8]
    // 0x15013f0: StoreField: r0->field_f = r16
    //     0x15013f0: stur            w16, [x0, #0xf]
    // 0x15013f4: ldur            x1, [fp, #-8]
    // 0x15013f8: r0 = controller()
    //     0x15013f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15013fc: LoadField: r1 = r0->field_73
    //     0x15013fc: ldur            w1, [x0, #0x73]
    // 0x1501400: DecompressPointer r1
    //     0x1501400: add             x1, x1, HEAP, lsl #32
    // 0x1501404: r0 = value()
    //     0x1501404: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1501408: LoadField: r1 = r0->field_b
    //     0x1501408: ldur            w1, [x0, #0xb]
    // 0x150140c: DecompressPointer r1
    //     0x150140c: add             x1, x1, HEAP, lsl #32
    // 0x1501410: cmp             w1, NULL
    // 0x1501414: b.ne            #0x1501420
    // 0x1501418: r0 = Null
    //     0x1501418: mov             x0, NULL
    // 0x150141c: b               #0x1501444
    // 0x1501420: LoadField: r0 = r1->field_f
    //     0x1501420: ldur            w0, [x1, #0xf]
    // 0x1501424: DecompressPointer r0
    //     0x1501424: add             x0, x0, HEAP, lsl #32
    // 0x1501428: cmp             w0, NULL
    // 0x150142c: b.ne            #0x1501438
    // 0x1501430: r0 = Null
    //     0x1501430: mov             x0, NULL
    // 0x1501434: b               #0x1501444
    // 0x1501438: LoadField: r1 = r0->field_7
    //     0x1501438: ldur            w1, [x0, #7]
    // 0x150143c: DecompressPointer r1
    //     0x150143c: add             x1, x1, HEAP, lsl #32
    // 0x1501440: mov             x0, x1
    // 0x1501444: cmp             w0, NULL
    // 0x1501448: b.ne            #0x1501450
    // 0x150144c: r0 = ""
    //     0x150144c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1501450: ldur            x2, [fp, #-0x10]
    // 0x1501454: ldur            x1, [fp, #-0x20]
    // 0x1501458: ArrayStore: r1[1] = r0  ; List_4
    //     0x1501458: add             x25, x1, #0x13
    //     0x150145c: str             w0, [x25]
    //     0x1501460: tbz             w0, #0, #0x150147c
    //     0x1501464: ldurb           w16, [x1, #-1]
    //     0x1501468: ldurb           w17, [x0, #-1]
    //     0x150146c: and             x16, x17, x16, lsr #2
    //     0x1501470: tst             x16, HEAP, lsr #32
    //     0x1501474: b.eq            #0x150147c
    //     0x1501478: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x150147c: ldur            x16, [fp, #-0x20]
    // 0x1501480: str             x16, [SP]
    // 0x1501484: r0 = _interpolate()
    //     0x1501484: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1501488: ldur            x2, [fp, #-0x10]
    // 0x150148c: stur            x0, [fp, #-0x20]
    // 0x1501490: LoadField: r1 = r2->field_13
    //     0x1501490: ldur            w1, [x2, #0x13]
    // 0x1501494: DecompressPointer r1
    //     0x1501494: add             x1, x1, HEAP, lsl #32
    // 0x1501498: r0 = of()
    //     0x1501498: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150149c: LoadField: r1 = r0->field_87
    //     0x150149c: ldur            w1, [x0, #0x87]
    // 0x15014a0: DecompressPointer r1
    //     0x15014a0: add             x1, x1, HEAP, lsl #32
    // 0x15014a4: LoadField: r0 = r1->field_7
    //     0x15014a4: ldur            w0, [x1, #7]
    // 0x15014a8: DecompressPointer r0
    //     0x15014a8: add             x0, x0, HEAP, lsl #32
    // 0x15014ac: r16 = Instance_Color
    //     0x15014ac: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15014b0: r30 = 14.000000
    //     0x15014b0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x15014b4: ldr             lr, [lr, #0x1d8]
    // 0x15014b8: stp             lr, x16, [SP]
    // 0x15014bc: mov             x1, x0
    // 0x15014c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15014c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15014c4: ldr             x4, [x4, #0x9b8]
    // 0x15014c8: r0 = copyWith()
    //     0x15014c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15014cc: stur            x0, [fp, #-0x28]
    // 0x15014d0: r0 = Text()
    //     0x15014d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15014d4: mov             x3, x0
    // 0x15014d8: ldur            x0, [fp, #-0x20]
    // 0x15014dc: stur            x3, [fp, #-0x30]
    // 0x15014e0: StoreField: r3->field_b = r0
    //     0x15014e0: stur            w0, [x3, #0xb]
    // 0x15014e4: ldur            x0, [fp, #-0x28]
    // 0x15014e8: StoreField: r3->field_13 = r0
    //     0x15014e8: stur            w0, [x3, #0x13]
    // 0x15014ec: r1 = <Widget>
    //     0x15014ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15014f0: r2 = 0
    //     0x15014f0: movz            x2, #0
    // 0x15014f4: r0 = _GrowableList()
    //     0x15014f4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x15014f8: ldur            x2, [fp, #-0x10]
    // 0x15014fc: stur            x0, [fp, #-0x20]
    // 0x1501500: LoadField: r1 = r2->field_f
    //     0x1501500: ldur            w1, [x2, #0xf]
    // 0x1501504: DecompressPointer r1
    //     0x1501504: add             x1, x1, HEAP, lsl #32
    // 0x1501508: r0 = controller()
    //     0x1501508: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150150c: LoadField: r1 = r0->field_73
    //     0x150150c: ldur            w1, [x0, #0x73]
    // 0x1501510: DecompressPointer r1
    //     0x1501510: add             x1, x1, HEAP, lsl #32
    // 0x1501514: r0 = value()
    //     0x1501514: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1501518: LoadField: r1 = r0->field_b
    //     0x1501518: ldur            w1, [x0, #0xb]
    // 0x150151c: DecompressPointer r1
    //     0x150151c: add             x1, x1, HEAP, lsl #32
    // 0x1501520: cmp             w1, NULL
    // 0x1501524: b.ne            #0x1501530
    // 0x1501528: ldur            x2, [fp, #-0x20]
    // 0x150152c: b               #0x1501d70
    // 0x1501530: LoadField: r0 = r1->field_43
    //     0x1501530: ldur            w0, [x1, #0x43]
    // 0x1501534: DecompressPointer r0
    //     0x1501534: add             x0, x0, HEAP, lsl #32
    // 0x1501538: cmp             w0, NULL
    // 0x150153c: b.eq            #0x1501d6c
    // 0x1501540: ldur            x2, [fp, #-0x10]
    // 0x1501544: LoadField: r1 = r2->field_f
    //     0x1501544: ldur            w1, [x2, #0xf]
    // 0x1501548: DecompressPointer r1
    //     0x1501548: add             x1, x1, HEAP, lsl #32
    // 0x150154c: r0 = controller()
    //     0x150154c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1501550: LoadField: r1 = r0->field_73
    //     0x1501550: ldur            w1, [x0, #0x73]
    // 0x1501554: DecompressPointer r1
    //     0x1501554: add             x1, x1, HEAP, lsl #32
    // 0x1501558: r0 = value()
    //     0x1501558: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150155c: LoadField: r1 = r0->field_b
    //     0x150155c: ldur            w1, [x0, #0xb]
    // 0x1501560: DecompressPointer r1
    //     0x1501560: add             x1, x1, HEAP, lsl #32
    // 0x1501564: cmp             w1, NULL
    // 0x1501568: b.ne            #0x1501574
    // 0x150156c: r0 = Null
    //     0x150156c: mov             x0, NULL
    // 0x1501570: b               #0x1501598
    // 0x1501574: LoadField: r0 = r1->field_43
    //     0x1501574: ldur            w0, [x1, #0x43]
    // 0x1501578: DecompressPointer r0
    //     0x1501578: add             x0, x0, HEAP, lsl #32
    // 0x150157c: cmp             w0, NULL
    // 0x1501580: b.ne            #0x150158c
    // 0x1501584: r0 = Null
    //     0x1501584: mov             x0, NULL
    // 0x1501588: b               #0x1501598
    // 0x150158c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x150158c: ldur            w1, [x0, #0x17]
    // 0x1501590: DecompressPointer r1
    //     0x1501590: add             x1, x1, HEAP, lsl #32
    // 0x1501594: mov             x0, x1
    // 0x1501598: cmp             w0, NULL
    // 0x150159c: b.ne            #0x15015a4
    // 0x15015a0: r0 = false
    //     0x15015a0: add             x0, NULL, #0x30  ; false
    // 0x15015a4: ldur            x2, [fp, #-0x10]
    // 0x15015a8: stur            x0, [fp, #-0x28]
    // 0x15015ac: r1 = Instance_Color
    //     0x15015ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15015b0: d0 = 0.070000
    //     0x15015b0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x15015b4: ldr             d0, [x17, #0x5f8]
    // 0x15015b8: r0 = withOpacity()
    //     0x15015b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x15015bc: r16 = 1.000000
    //     0x15015bc: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15015c0: str             x16, [SP]
    // 0x15015c4: mov             x2, x0
    // 0x15015c8: r1 = Null
    //     0x15015c8: mov             x1, NULL
    // 0x15015cc: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x15015cc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x15015d0: ldr             x4, [x4, #0x108]
    // 0x15015d4: r0 = Border.all()
    //     0x15015d4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x15015d8: stur            x0, [fp, #-0x38]
    // 0x15015dc: r0 = BoxDecoration()
    //     0x15015dc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x15015e0: mov             x2, x0
    // 0x15015e4: ldur            x0, [fp, #-0x38]
    // 0x15015e8: stur            x2, [fp, #-0x40]
    // 0x15015ec: StoreField: r2->field_f = r0
    //     0x15015ec: stur            w0, [x2, #0xf]
    // 0x15015f0: r0 = Instance_LinearGradient
    //     0x15015f0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0x15015f4: ldr             x0, [x0, #0x660]
    // 0x15015f8: StoreField: r2->field_1b = r0
    //     0x15015f8: stur            w0, [x2, #0x1b]
    // 0x15015fc: r0 = Instance_BoxShape
    //     0x15015fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1501600: ldr             x0, [x0, #0x80]
    // 0x1501604: StoreField: r2->field_23 = r0
    //     0x1501604: stur            w0, [x2, #0x23]
    // 0x1501608: ldur            x0, [fp, #-0x10]
    // 0x150160c: LoadField: r1 = r0->field_13
    //     0x150160c: ldur            w1, [x0, #0x13]
    // 0x1501610: DecompressPointer r1
    //     0x1501610: add             x1, x1, HEAP, lsl #32
    // 0x1501614: r0 = of()
    //     0x1501614: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1501618: LoadField: r1 = r0->field_87
    //     0x1501618: ldur            w1, [x0, #0x87]
    // 0x150161c: DecompressPointer r1
    //     0x150161c: add             x1, x1, HEAP, lsl #32
    // 0x1501620: LoadField: r0 = r1->field_7
    //     0x1501620: ldur            w0, [x1, #7]
    // 0x1501624: DecompressPointer r0
    //     0x1501624: add             x0, x0, HEAP, lsl #32
    // 0x1501628: r16 = 12.000000
    //     0x1501628: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x150162c: ldr             x16, [x16, #0x9e8]
    // 0x1501630: r30 = Instance_Color
    //     0x1501630: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1501634: stp             lr, x16, [SP]
    // 0x1501638: mov             x1, x0
    // 0x150163c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x150163c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1501640: ldr             x4, [x4, #0xaa0]
    // 0x1501644: r0 = copyWith()
    //     0x1501644: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1501648: stur            x0, [fp, #-0x38]
    // 0x150164c: r0 = Text()
    //     0x150164c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1501650: mov             x1, x0
    // 0x1501654: r0 = "Free"
    //     0x1501654: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x1501658: ldr             x0, [x0, #0x668]
    // 0x150165c: stur            x1, [fp, #-0x48]
    // 0x1501660: StoreField: r1->field_b = r0
    //     0x1501660: stur            w0, [x1, #0xb]
    // 0x1501664: ldur            x2, [fp, #-0x38]
    // 0x1501668: StoreField: r1->field_13 = r2
    //     0x1501668: stur            w2, [x1, #0x13]
    // 0x150166c: r0 = Center()
    //     0x150166c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1501670: mov             x1, x0
    // 0x1501674: r0 = Instance_Alignment
    //     0x1501674: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1501678: ldr             x0, [x0, #0xb10]
    // 0x150167c: stur            x1, [fp, #-0x38]
    // 0x1501680: StoreField: r1->field_f = r0
    //     0x1501680: stur            w0, [x1, #0xf]
    // 0x1501684: ldur            x0, [fp, #-0x48]
    // 0x1501688: StoreField: r1->field_b = r0
    //     0x1501688: stur            w0, [x1, #0xb]
    // 0x150168c: r0 = RotatedBox()
    //     0x150168c: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0x1501690: mov             x1, x0
    // 0x1501694: r0 = -1
    //     0x1501694: movn            x0, #0
    // 0x1501698: stur            x1, [fp, #-0x48]
    // 0x150169c: StoreField: r1->field_f = r0
    //     0x150169c: stur            x0, [x1, #0xf]
    // 0x15016a0: ldur            x0, [fp, #-0x38]
    // 0x15016a4: StoreField: r1->field_b = r0
    //     0x15016a4: stur            w0, [x1, #0xb]
    // 0x15016a8: r0 = Container()
    //     0x15016a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15016ac: stur            x0, [fp, #-0x38]
    // 0x15016b0: r16 = 24.000000
    //     0x15016b0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15016b4: ldr             x16, [x16, #0xba8]
    // 0x15016b8: r30 = 56.000000
    //     0x15016b8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x15016bc: ldr             lr, [lr, #0xb78]
    // 0x15016c0: stp             lr, x16, [SP, #0x10]
    // 0x15016c4: r16 = Instance_Color
    //     0x15016c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x15016c8: ldr             x16, [x16, #0x858]
    // 0x15016cc: ldur            lr, [fp, #-0x48]
    // 0x15016d0: stp             lr, x16, [SP]
    // 0x15016d4: mov             x1, x0
    // 0x15016d8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0x15016d8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x15016dc: ldr             x4, [x4, #0x670]
    // 0x15016e0: r0 = Container()
    //     0x15016e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15016e4: ldur            x2, [fp, #-0x10]
    // 0x15016e8: LoadField: r1 = r2->field_f
    //     0x15016e8: ldur            w1, [x2, #0xf]
    // 0x15016ec: DecompressPointer r1
    //     0x15016ec: add             x1, x1, HEAP, lsl #32
    // 0x15016f0: r0 = controller()
    //     0x15016f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15016f4: LoadField: r1 = r0->field_73
    //     0x15016f4: ldur            w1, [x0, #0x73]
    // 0x15016f8: DecompressPointer r1
    //     0x15016f8: add             x1, x1, HEAP, lsl #32
    // 0x15016fc: r0 = value()
    //     0x15016fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1501700: LoadField: r1 = r0->field_b
    //     0x1501700: ldur            w1, [x0, #0xb]
    // 0x1501704: DecompressPointer r1
    //     0x1501704: add             x1, x1, HEAP, lsl #32
    // 0x1501708: cmp             w1, NULL
    // 0x150170c: b.ne            #0x1501718
    // 0x1501710: r0 = Null
    //     0x1501710: mov             x0, NULL
    // 0x1501714: b               #0x150173c
    // 0x1501718: LoadField: r0 = r1->field_43
    //     0x1501718: ldur            w0, [x1, #0x43]
    // 0x150171c: DecompressPointer r0
    //     0x150171c: add             x0, x0, HEAP, lsl #32
    // 0x1501720: cmp             w0, NULL
    // 0x1501724: b.ne            #0x1501730
    // 0x1501728: r0 = Null
    //     0x1501728: mov             x0, NULL
    // 0x150172c: b               #0x150173c
    // 0x1501730: LoadField: r1 = r0->field_7
    //     0x1501730: ldur            w1, [x0, #7]
    // 0x1501734: DecompressPointer r1
    //     0x1501734: add             x1, x1, HEAP, lsl #32
    // 0x1501738: mov             x0, x1
    // 0x150173c: cmp             w0, NULL
    // 0x1501740: b.ne            #0x1501748
    // 0x1501744: r0 = ""
    //     0x1501744: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1501748: ldur            x2, [fp, #-0x10]
    // 0x150174c: stur            x0, [fp, #-0x48]
    // 0x1501750: r0 = ImageHeaders.forImages()
    //     0x1501750: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x1501754: r1 = Function '<anonymous closure>':.
    //     0x1501754: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c678] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1501758: ldr             x1, [x1, #0x678]
    // 0x150175c: r2 = Null
    //     0x150175c: mov             x2, NULL
    // 0x1501760: stur            x0, [fp, #-0x50]
    // 0x1501764: r0 = AllocateClosure()
    //     0x1501764: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1501768: r1 = Function '<anonymous closure>':.
    //     0x1501768: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c680] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x150176c: ldr             x1, [x1, #0x680]
    // 0x1501770: r2 = Null
    //     0x1501770: mov             x2, NULL
    // 0x1501774: stur            x0, [fp, #-0x58]
    // 0x1501778: r0 = AllocateClosure()
    //     0x1501778: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150177c: stur            x0, [fp, #-0x60]
    // 0x1501780: r0 = CachedNetworkImage()
    //     0x1501780: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1501784: stur            x0, [fp, #-0x68]
    // 0x1501788: ldur            x16, [fp, #-0x50]
    // 0x150178c: r30 = 56.000000
    //     0x150178c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1501790: ldr             lr, [lr, #0xb78]
    // 0x1501794: stp             lr, x16, [SP, #0x20]
    // 0x1501798: r16 = 56.000000
    //     0x1501798: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x150179c: ldr             x16, [x16, #0xb78]
    // 0x15017a0: r30 = Instance_BoxFit
    //     0x15017a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x15017a4: ldr             lr, [lr, #0x118]
    // 0x15017a8: stp             lr, x16, [SP, #0x10]
    // 0x15017ac: ldur            x16, [fp, #-0x58]
    // 0x15017b0: ldur            lr, [fp, #-0x60]
    // 0x15017b4: stp             lr, x16, [SP]
    // 0x15017b8: mov             x1, x0
    // 0x15017bc: ldur            x2, [fp, #-0x48]
    // 0x15017c0: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x4, httpHeaders, 0x2, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0x15017c0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c688] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0x15017c4: ldr             x4, [x4, #0x688]
    // 0x15017c8: r0 = CachedNetworkImage()
    //     0x15017c8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15017cc: ldur            x2, [fp, #-0x10]
    // 0x15017d0: LoadField: r1 = r2->field_f
    //     0x15017d0: ldur            w1, [x2, #0xf]
    // 0x15017d4: DecompressPointer r1
    //     0x15017d4: add             x1, x1, HEAP, lsl #32
    // 0x15017d8: r0 = controller()
    //     0x15017d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15017dc: LoadField: r1 = r0->field_73
    //     0x15017dc: ldur            w1, [x0, #0x73]
    // 0x15017e0: DecompressPointer r1
    //     0x15017e0: add             x1, x1, HEAP, lsl #32
    // 0x15017e4: r0 = value()
    //     0x15017e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15017e8: LoadField: r1 = r0->field_b
    //     0x15017e8: ldur            w1, [x0, #0xb]
    // 0x15017ec: DecompressPointer r1
    //     0x15017ec: add             x1, x1, HEAP, lsl #32
    // 0x15017f0: cmp             w1, NULL
    // 0x15017f4: b.ne            #0x1501800
    // 0x15017f8: r0 = Null
    //     0x15017f8: mov             x0, NULL
    // 0x15017fc: b               #0x1501824
    // 0x1501800: LoadField: r0 = r1->field_43
    //     0x1501800: ldur            w0, [x1, #0x43]
    // 0x1501804: DecompressPointer r0
    //     0x1501804: add             x0, x0, HEAP, lsl #32
    // 0x1501808: cmp             w0, NULL
    // 0x150180c: b.ne            #0x1501818
    // 0x1501810: r0 = Null
    //     0x1501810: mov             x0, NULL
    // 0x1501814: b               #0x1501824
    // 0x1501818: LoadField: r1 = r0->field_b
    //     0x1501818: ldur            w1, [x0, #0xb]
    // 0x150181c: DecompressPointer r1
    //     0x150181c: add             x1, x1, HEAP, lsl #32
    // 0x1501820: mov             x0, x1
    // 0x1501824: cmp             w0, NULL
    // 0x1501828: b.ne            #0x1501830
    // 0x150182c: r0 = ""
    //     0x150182c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1501830: ldur            x2, [fp, #-0x10]
    // 0x1501834: stur            x0, [fp, #-0x48]
    // 0x1501838: LoadField: r1 = r2->field_13
    //     0x1501838: ldur            w1, [x2, #0x13]
    // 0x150183c: DecompressPointer r1
    //     0x150183c: add             x1, x1, HEAP, lsl #32
    // 0x1501840: r0 = of()
    //     0x1501840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1501844: LoadField: r1 = r0->field_87
    //     0x1501844: ldur            w1, [x0, #0x87]
    // 0x1501848: DecompressPointer r1
    //     0x1501848: add             x1, x1, HEAP, lsl #32
    // 0x150184c: LoadField: r0 = r1->field_7
    //     0x150184c: ldur            w0, [x1, #7]
    // 0x1501850: DecompressPointer r0
    //     0x1501850: add             x0, x0, HEAP, lsl #32
    // 0x1501854: r16 = 12.000000
    //     0x1501854: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1501858: ldr             x16, [x16, #0x9e8]
    // 0x150185c: r30 = Instance_Color
    //     0x150185c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1501860: stp             lr, x16, [SP]
    // 0x1501864: mov             x1, x0
    // 0x1501868: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1501868: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x150186c: ldr             x4, [x4, #0xaa0]
    // 0x1501870: r0 = copyWith()
    //     0x1501870: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1501874: stur            x0, [fp, #-0x50]
    // 0x1501878: r0 = Text()
    //     0x1501878: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x150187c: mov             x1, x0
    // 0x1501880: ldur            x0, [fp, #-0x48]
    // 0x1501884: stur            x1, [fp, #-0x58]
    // 0x1501888: StoreField: r1->field_b = r0
    //     0x1501888: stur            w0, [x1, #0xb]
    // 0x150188c: ldur            x0, [fp, #-0x50]
    // 0x1501890: StoreField: r1->field_13 = r0
    //     0x1501890: stur            w0, [x1, #0x13]
    // 0x1501894: r0 = Instance_TextOverflow
    //     0x1501894: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x1501898: ldr             x0, [x0, #0xe10]
    // 0x150189c: StoreField: r1->field_2b = r0
    //     0x150189c: stur            w0, [x1, #0x2b]
    // 0x15018a0: r0 = 2
    //     0x15018a0: movz            x0, #0x2
    // 0x15018a4: StoreField: r1->field_37 = r0
    //     0x15018a4: stur            w0, [x1, #0x37]
    // 0x15018a8: r0 = SizedBox()
    //     0x15018a8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x15018ac: mov             x2, x0
    // 0x15018b0: r0 = 150.000000
    //     0x15018b0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0x15018b4: ldr             x0, [x0, #0x690]
    // 0x15018b8: stur            x2, [fp, #-0x48]
    // 0x15018bc: StoreField: r2->field_f = r0
    //     0x15018bc: stur            w0, [x2, #0xf]
    // 0x15018c0: ldur            x0, [fp, #-0x58]
    // 0x15018c4: StoreField: r2->field_b = r0
    //     0x15018c4: stur            w0, [x2, #0xb]
    // 0x15018c8: ldur            x0, [fp, #-0x10]
    // 0x15018cc: LoadField: r1 = r0->field_13
    //     0x15018cc: ldur            w1, [x0, #0x13]
    // 0x15018d0: DecompressPointer r1
    //     0x15018d0: add             x1, x1, HEAP, lsl #32
    // 0x15018d4: r0 = of()
    //     0x15018d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15018d8: LoadField: r1 = r0->field_87
    //     0x15018d8: ldur            w1, [x0, #0x87]
    // 0x15018dc: DecompressPointer r1
    //     0x15018dc: add             x1, x1, HEAP, lsl #32
    // 0x15018e0: LoadField: r0 = r1->field_2b
    //     0x15018e0: ldur            w0, [x1, #0x2b]
    // 0x15018e4: DecompressPointer r0
    //     0x15018e4: add             x0, x0, HEAP, lsl #32
    // 0x15018e8: r16 = 12.000000
    //     0x15018e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x15018ec: ldr             x16, [x16, #0x9e8]
    // 0x15018f0: r30 = Instance_Color
    //     0x15018f0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x15018f4: ldr             lr, [lr, #0x858]
    // 0x15018f8: stp             lr, x16, [SP]
    // 0x15018fc: mov             x1, x0
    // 0x1501900: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1501900: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1501904: ldr             x4, [x4, #0xaa0]
    // 0x1501908: r0 = copyWith()
    //     0x1501908: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150190c: stur            x0, [fp, #-0x50]
    // 0x1501910: r0 = Text()
    //     0x1501910: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1501914: mov             x2, x0
    // 0x1501918: r0 = "Free"
    //     0x1501918: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x150191c: ldr             x0, [x0, #0x668]
    // 0x1501920: stur            x2, [fp, #-0x58]
    // 0x1501924: StoreField: r2->field_b = r0
    //     0x1501924: stur            w0, [x2, #0xb]
    // 0x1501928: ldur            x0, [fp, #-0x50]
    // 0x150192c: StoreField: r2->field_13 = r0
    //     0x150192c: stur            w0, [x2, #0x13]
    // 0x1501930: ldur            x0, [fp, #-0x10]
    // 0x1501934: LoadField: r1 = r0->field_f
    //     0x1501934: ldur            w1, [x0, #0xf]
    // 0x1501938: DecompressPointer r1
    //     0x1501938: add             x1, x1, HEAP, lsl #32
    // 0x150193c: r0 = controller()
    //     0x150193c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1501940: LoadField: r1 = r0->field_73
    //     0x1501940: ldur            w1, [x0, #0x73]
    // 0x1501944: DecompressPointer r1
    //     0x1501944: add             x1, x1, HEAP, lsl #32
    // 0x1501948: r0 = value()
    //     0x1501948: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150194c: LoadField: r1 = r0->field_b
    //     0x150194c: ldur            w1, [x0, #0xb]
    // 0x1501950: DecompressPointer r1
    //     0x1501950: add             x1, x1, HEAP, lsl #32
    // 0x1501954: cmp             w1, NULL
    // 0x1501958: b.ne            #0x1501964
    // 0x150195c: r0 = Null
    //     0x150195c: mov             x0, NULL
    // 0x1501960: b               #0x1501988
    // 0x1501964: LoadField: r0 = r1->field_43
    //     0x1501964: ldur            w0, [x1, #0x43]
    // 0x1501968: DecompressPointer r0
    //     0x1501968: add             x0, x0, HEAP, lsl #32
    // 0x150196c: cmp             w0, NULL
    // 0x1501970: b.ne            #0x150197c
    // 0x1501974: r0 = Null
    //     0x1501974: mov             x0, NULL
    // 0x1501978: b               #0x1501988
    // 0x150197c: LoadField: r1 = r0->field_13
    //     0x150197c: ldur            w1, [x0, #0x13]
    // 0x1501980: DecompressPointer r1
    //     0x1501980: add             x1, x1, HEAP, lsl #32
    // 0x1501984: mov             x0, x1
    // 0x1501988: cmp             w0, NULL
    // 0x150198c: b.ne            #0x1501998
    // 0x1501990: r8 = ""
    //     0x1501990: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1501994: b               #0x150199c
    // 0x1501998: mov             x8, x0
    // 0x150199c: ldur            x2, [fp, #-0x10]
    // 0x15019a0: ldur            x7, [fp, #-0x20]
    // 0x15019a4: ldur            x6, [fp, #-0x28]
    // 0x15019a8: ldur            x5, [fp, #-0x38]
    // 0x15019ac: ldur            x4, [fp, #-0x68]
    // 0x15019b0: ldur            x3, [fp, #-0x48]
    // 0x15019b4: ldur            x0, [fp, #-0x58]
    // 0x15019b8: stur            x8, [fp, #-0x50]
    // 0x15019bc: LoadField: r1 = r2->field_13
    //     0x15019bc: ldur            w1, [x2, #0x13]
    // 0x15019c0: DecompressPointer r1
    //     0x15019c0: add             x1, x1, HEAP, lsl #32
    // 0x15019c4: r0 = of()
    //     0x15019c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15019c8: LoadField: r1 = r0->field_87
    //     0x15019c8: ldur            w1, [x0, #0x87]
    // 0x15019cc: DecompressPointer r1
    //     0x15019cc: add             x1, x1, HEAP, lsl #32
    // 0x15019d0: LoadField: r0 = r1->field_2b
    //     0x15019d0: ldur            w0, [x1, #0x2b]
    // 0x15019d4: DecompressPointer r0
    //     0x15019d4: add             x0, x0, HEAP, lsl #32
    // 0x15019d8: r16 = 12.000000
    //     0x15019d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x15019dc: ldr             x16, [x16, #0x9e8]
    // 0x15019e0: r30 = Instance_TextDecoration
    //     0x15019e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x15019e4: ldr             lr, [lr, #0xe30]
    // 0x15019e8: stp             lr, x16, [SP]
    // 0x15019ec: mov             x1, x0
    // 0x15019f0: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x15019f0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x15019f4: ldr             x4, [x4, #0x698]
    // 0x15019f8: r0 = copyWith()
    //     0x15019f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15019fc: stur            x0, [fp, #-0x60]
    // 0x1501a00: r0 = Text()
    //     0x1501a00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1501a04: mov             x3, x0
    // 0x1501a08: ldur            x0, [fp, #-0x50]
    // 0x1501a0c: stur            x3, [fp, #-0x70]
    // 0x1501a10: StoreField: r3->field_b = r0
    //     0x1501a10: stur            w0, [x3, #0xb]
    // 0x1501a14: ldur            x0, [fp, #-0x60]
    // 0x1501a18: StoreField: r3->field_13 = r0
    //     0x1501a18: stur            w0, [x3, #0x13]
    // 0x1501a1c: r1 = Null
    //     0x1501a1c: mov             x1, NULL
    // 0x1501a20: r2 = 6
    //     0x1501a20: movz            x2, #0x6
    // 0x1501a24: r0 = AllocateArray()
    //     0x1501a24: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1501a28: mov             x2, x0
    // 0x1501a2c: ldur            x0, [fp, #-0x58]
    // 0x1501a30: stur            x2, [fp, #-0x50]
    // 0x1501a34: StoreField: r2->field_f = r0
    //     0x1501a34: stur            w0, [x2, #0xf]
    // 0x1501a38: r16 = Instance_SizedBox
    //     0x1501a38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x1501a3c: ldr             x16, [x16, #0xa50]
    // 0x1501a40: StoreField: r2->field_13 = r16
    //     0x1501a40: stur            w16, [x2, #0x13]
    // 0x1501a44: ldur            x0, [fp, #-0x70]
    // 0x1501a48: ArrayStore: r2[0] = r0  ; List_4
    //     0x1501a48: stur            w0, [x2, #0x17]
    // 0x1501a4c: r1 = <Widget>
    //     0x1501a4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1501a50: r0 = AllocateGrowableArray()
    //     0x1501a50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1501a54: mov             x1, x0
    // 0x1501a58: ldur            x0, [fp, #-0x50]
    // 0x1501a5c: stur            x1, [fp, #-0x58]
    // 0x1501a60: StoreField: r1->field_f = r0
    //     0x1501a60: stur            w0, [x1, #0xf]
    // 0x1501a64: r2 = 6
    //     0x1501a64: movz            x2, #0x6
    // 0x1501a68: StoreField: r1->field_b = r2
    //     0x1501a68: stur            w2, [x1, #0xb]
    // 0x1501a6c: r0 = Row()
    //     0x1501a6c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1501a70: mov             x3, x0
    // 0x1501a74: r0 = Instance_Axis
    //     0x1501a74: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1501a78: stur            x3, [fp, #-0x50]
    // 0x1501a7c: StoreField: r3->field_f = r0
    //     0x1501a7c: stur            w0, [x3, #0xf]
    // 0x1501a80: r4 = Instance_MainAxisAlignment
    //     0x1501a80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1501a84: ldr             x4, [x4, #0xa08]
    // 0x1501a88: StoreField: r3->field_13 = r4
    //     0x1501a88: stur            w4, [x3, #0x13]
    // 0x1501a8c: r5 = Instance_MainAxisSize
    //     0x1501a8c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1501a90: ldr             x5, [x5, #0xa10]
    // 0x1501a94: ArrayStore: r3[0] = r5  ; List_4
    //     0x1501a94: stur            w5, [x3, #0x17]
    // 0x1501a98: r6 = Instance_CrossAxisAlignment
    //     0x1501a98: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1501a9c: ldr             x6, [x6, #0xa18]
    // 0x1501aa0: StoreField: r3->field_1b = r6
    //     0x1501aa0: stur            w6, [x3, #0x1b]
    // 0x1501aa4: r7 = Instance_VerticalDirection
    //     0x1501aa4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1501aa8: ldr             x7, [x7, #0xa20]
    // 0x1501aac: StoreField: r3->field_23 = r7
    //     0x1501aac: stur            w7, [x3, #0x23]
    // 0x1501ab0: r8 = Instance_Clip
    //     0x1501ab0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1501ab4: ldr             x8, [x8, #0x38]
    // 0x1501ab8: StoreField: r3->field_2b = r8
    //     0x1501ab8: stur            w8, [x3, #0x2b]
    // 0x1501abc: StoreField: r3->field_2f = rZR
    //     0x1501abc: stur            xzr, [x3, #0x2f]
    // 0x1501ac0: ldur            x1, [fp, #-0x58]
    // 0x1501ac4: StoreField: r3->field_b = r1
    //     0x1501ac4: stur            w1, [x3, #0xb]
    // 0x1501ac8: r1 = Null
    //     0x1501ac8: mov             x1, NULL
    // 0x1501acc: r2 = 6
    //     0x1501acc: movz            x2, #0x6
    // 0x1501ad0: r0 = AllocateArray()
    //     0x1501ad0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1501ad4: mov             x2, x0
    // 0x1501ad8: ldur            x0, [fp, #-0x48]
    // 0x1501adc: stur            x2, [fp, #-0x58]
    // 0x1501ae0: StoreField: r2->field_f = r0
    //     0x1501ae0: stur            w0, [x2, #0xf]
    // 0x1501ae4: r16 = Instance_SizedBox
    //     0x1501ae4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1501ae8: ldr             x16, [x16, #0xc70]
    // 0x1501aec: StoreField: r2->field_13 = r16
    //     0x1501aec: stur            w16, [x2, #0x13]
    // 0x1501af0: ldur            x0, [fp, #-0x50]
    // 0x1501af4: ArrayStore: r2[0] = r0  ; List_4
    //     0x1501af4: stur            w0, [x2, #0x17]
    // 0x1501af8: r1 = <Widget>
    //     0x1501af8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1501afc: r0 = AllocateGrowableArray()
    //     0x1501afc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1501b00: mov             x1, x0
    // 0x1501b04: ldur            x0, [fp, #-0x58]
    // 0x1501b08: stur            x1, [fp, #-0x48]
    // 0x1501b0c: StoreField: r1->field_f = r0
    //     0x1501b0c: stur            w0, [x1, #0xf]
    // 0x1501b10: r2 = 6
    //     0x1501b10: movz            x2, #0x6
    // 0x1501b14: StoreField: r1->field_b = r2
    //     0x1501b14: stur            w2, [x1, #0xb]
    // 0x1501b18: r0 = Column()
    //     0x1501b18: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1501b1c: mov             x1, x0
    // 0x1501b20: r0 = Instance_Axis
    //     0x1501b20: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1501b24: stur            x1, [fp, #-0x50]
    // 0x1501b28: StoreField: r1->field_f = r0
    //     0x1501b28: stur            w0, [x1, #0xf]
    // 0x1501b2c: r2 = Instance_MainAxisAlignment
    //     0x1501b2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1501b30: ldr             x2, [x2, #0xa08]
    // 0x1501b34: StoreField: r1->field_13 = r2
    //     0x1501b34: stur            w2, [x1, #0x13]
    // 0x1501b38: r3 = Instance_MainAxisSize
    //     0x1501b38: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1501b3c: ldr             x3, [x3, #0xa10]
    // 0x1501b40: ArrayStore: r1[0] = r3  ; List_4
    //     0x1501b40: stur            w3, [x1, #0x17]
    // 0x1501b44: r4 = Instance_CrossAxisAlignment
    //     0x1501b44: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1501b48: ldr             x4, [x4, #0x890]
    // 0x1501b4c: StoreField: r1->field_1b = r4
    //     0x1501b4c: stur            w4, [x1, #0x1b]
    // 0x1501b50: r4 = Instance_VerticalDirection
    //     0x1501b50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1501b54: ldr             x4, [x4, #0xa20]
    // 0x1501b58: StoreField: r1->field_23 = r4
    //     0x1501b58: stur            w4, [x1, #0x23]
    // 0x1501b5c: r5 = Instance_Clip
    //     0x1501b5c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1501b60: ldr             x5, [x5, #0x38]
    // 0x1501b64: StoreField: r1->field_2b = r5
    //     0x1501b64: stur            w5, [x1, #0x2b]
    // 0x1501b68: StoreField: r1->field_2f = rZR
    //     0x1501b68: stur            xzr, [x1, #0x2f]
    // 0x1501b6c: ldur            x6, [fp, #-0x48]
    // 0x1501b70: StoreField: r1->field_b = r6
    //     0x1501b70: stur            w6, [x1, #0xb]
    // 0x1501b74: r0 = Padding()
    //     0x1501b74: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1501b78: mov             x2, x0
    // 0x1501b7c: r0 = Instance_EdgeInsets
    //     0x1501b7c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x1501b80: ldr             x0, [x0, #0xa78]
    // 0x1501b84: stur            x2, [fp, #-0x48]
    // 0x1501b88: StoreField: r2->field_f = r0
    //     0x1501b88: stur            w0, [x2, #0xf]
    // 0x1501b8c: ldur            x0, [fp, #-0x50]
    // 0x1501b90: StoreField: r2->field_b = r0
    //     0x1501b90: stur            w0, [x2, #0xb]
    // 0x1501b94: r1 = <FlexParentData>
    //     0x1501b94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1501b98: ldr             x1, [x1, #0xe00]
    // 0x1501b9c: r0 = Expanded()
    //     0x1501b9c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1501ba0: mov             x3, x0
    // 0x1501ba4: r0 = 1
    //     0x1501ba4: movz            x0, #0x1
    // 0x1501ba8: stur            x3, [fp, #-0x50]
    // 0x1501bac: StoreField: r3->field_13 = r0
    //     0x1501bac: stur            x0, [x3, #0x13]
    // 0x1501bb0: r0 = Instance_FlexFit
    //     0x1501bb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1501bb4: ldr             x0, [x0, #0xe08]
    // 0x1501bb8: StoreField: r3->field_1b = r0
    //     0x1501bb8: stur            w0, [x3, #0x1b]
    // 0x1501bbc: ldur            x0, [fp, #-0x48]
    // 0x1501bc0: StoreField: r3->field_b = r0
    //     0x1501bc0: stur            w0, [x3, #0xb]
    // 0x1501bc4: r1 = Null
    //     0x1501bc4: mov             x1, NULL
    // 0x1501bc8: r2 = 6
    //     0x1501bc8: movz            x2, #0x6
    // 0x1501bcc: r0 = AllocateArray()
    //     0x1501bcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1501bd0: mov             x2, x0
    // 0x1501bd4: ldur            x0, [fp, #-0x38]
    // 0x1501bd8: stur            x2, [fp, #-0x48]
    // 0x1501bdc: StoreField: r2->field_f = r0
    //     0x1501bdc: stur            w0, [x2, #0xf]
    // 0x1501be0: ldur            x0, [fp, #-0x68]
    // 0x1501be4: StoreField: r2->field_13 = r0
    //     0x1501be4: stur            w0, [x2, #0x13]
    // 0x1501be8: ldur            x0, [fp, #-0x50]
    // 0x1501bec: ArrayStore: r2[0] = r0  ; List_4
    //     0x1501bec: stur            w0, [x2, #0x17]
    // 0x1501bf0: r1 = <Widget>
    //     0x1501bf0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1501bf4: r0 = AllocateGrowableArray()
    //     0x1501bf4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1501bf8: mov             x1, x0
    // 0x1501bfc: ldur            x0, [fp, #-0x48]
    // 0x1501c00: stur            x1, [fp, #-0x38]
    // 0x1501c04: StoreField: r1->field_f = r0
    //     0x1501c04: stur            w0, [x1, #0xf]
    // 0x1501c08: r0 = 6
    //     0x1501c08: movz            x0, #0x6
    // 0x1501c0c: StoreField: r1->field_b = r0
    //     0x1501c0c: stur            w0, [x1, #0xb]
    // 0x1501c10: r0 = Row()
    //     0x1501c10: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1501c14: mov             x1, x0
    // 0x1501c18: r0 = Instance_Axis
    //     0x1501c18: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1501c1c: stur            x1, [fp, #-0x48]
    // 0x1501c20: StoreField: r1->field_f = r0
    //     0x1501c20: stur            w0, [x1, #0xf]
    // 0x1501c24: r2 = Instance_MainAxisAlignment
    //     0x1501c24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1501c28: ldr             x2, [x2, #0xa08]
    // 0x1501c2c: StoreField: r1->field_13 = r2
    //     0x1501c2c: stur            w2, [x1, #0x13]
    // 0x1501c30: r3 = Instance_MainAxisSize
    //     0x1501c30: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1501c34: ldr             x3, [x3, #0xa10]
    // 0x1501c38: ArrayStore: r1[0] = r3  ; List_4
    //     0x1501c38: stur            w3, [x1, #0x17]
    // 0x1501c3c: r4 = Instance_CrossAxisAlignment
    //     0x1501c3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1501c40: ldr             x4, [x4, #0xa18]
    // 0x1501c44: StoreField: r1->field_1b = r4
    //     0x1501c44: stur            w4, [x1, #0x1b]
    // 0x1501c48: r5 = Instance_VerticalDirection
    //     0x1501c48: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1501c4c: ldr             x5, [x5, #0xa20]
    // 0x1501c50: StoreField: r1->field_23 = r5
    //     0x1501c50: stur            w5, [x1, #0x23]
    // 0x1501c54: r6 = Instance_Clip
    //     0x1501c54: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1501c58: ldr             x6, [x6, #0x38]
    // 0x1501c5c: StoreField: r1->field_2b = r6
    //     0x1501c5c: stur            w6, [x1, #0x2b]
    // 0x1501c60: StoreField: r1->field_2f = rZR
    //     0x1501c60: stur            xzr, [x1, #0x2f]
    // 0x1501c64: ldur            x7, [fp, #-0x38]
    // 0x1501c68: StoreField: r1->field_b = r7
    //     0x1501c68: stur            w7, [x1, #0xb]
    // 0x1501c6c: r0 = Container()
    //     0x1501c6c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1501c70: stur            x0, [fp, #-0x38]
    // 0x1501c74: ldur            x16, [fp, #-0x40]
    // 0x1501c78: ldur            lr, [fp, #-0x48]
    // 0x1501c7c: stp             lr, x16, [SP]
    // 0x1501c80: mov             x1, x0
    // 0x1501c84: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1501c84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x1501c88: ldr             x4, [x4, #0x88]
    // 0x1501c8c: r0 = Container()
    //     0x1501c8c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1501c90: r0 = Padding()
    //     0x1501c90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1501c94: mov             x1, x0
    // 0x1501c98: r0 = Instance_EdgeInsets
    //     0x1501c98: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x1501c9c: ldr             x0, [x0, #0x778]
    // 0x1501ca0: stur            x1, [fp, #-0x40]
    // 0x1501ca4: StoreField: r1->field_f = r0
    //     0x1501ca4: stur            w0, [x1, #0xf]
    // 0x1501ca8: ldur            x0, [fp, #-0x38]
    // 0x1501cac: StoreField: r1->field_b = r0
    //     0x1501cac: stur            w0, [x1, #0xb]
    // 0x1501cb0: r0 = Visibility()
    //     0x1501cb0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1501cb4: mov             x2, x0
    // 0x1501cb8: ldur            x0, [fp, #-0x40]
    // 0x1501cbc: stur            x2, [fp, #-0x38]
    // 0x1501cc0: StoreField: r2->field_b = r0
    //     0x1501cc0: stur            w0, [x2, #0xb]
    // 0x1501cc4: r0 = Instance_SizedBox
    //     0x1501cc4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1501cc8: StoreField: r2->field_f = r0
    //     0x1501cc8: stur            w0, [x2, #0xf]
    // 0x1501ccc: ldur            x0, [fp, #-0x28]
    // 0x1501cd0: StoreField: r2->field_13 = r0
    //     0x1501cd0: stur            w0, [x2, #0x13]
    // 0x1501cd4: r0 = false
    //     0x1501cd4: add             x0, NULL, #0x30  ; false
    // 0x1501cd8: ArrayStore: r2[0] = r0  ; List_4
    //     0x1501cd8: stur            w0, [x2, #0x17]
    // 0x1501cdc: StoreField: r2->field_1b = r0
    //     0x1501cdc: stur            w0, [x2, #0x1b]
    // 0x1501ce0: StoreField: r2->field_1f = r0
    //     0x1501ce0: stur            w0, [x2, #0x1f]
    // 0x1501ce4: StoreField: r2->field_23 = r0
    //     0x1501ce4: stur            w0, [x2, #0x23]
    // 0x1501ce8: StoreField: r2->field_27 = r0
    //     0x1501ce8: stur            w0, [x2, #0x27]
    // 0x1501cec: StoreField: r2->field_2b = r0
    //     0x1501cec: stur            w0, [x2, #0x2b]
    // 0x1501cf0: ldur            x3, [fp, #-0x20]
    // 0x1501cf4: LoadField: r1 = r3->field_b
    //     0x1501cf4: ldur            w1, [x3, #0xb]
    // 0x1501cf8: LoadField: r4 = r3->field_f
    //     0x1501cf8: ldur            w4, [x3, #0xf]
    // 0x1501cfc: DecompressPointer r4
    //     0x1501cfc: add             x4, x4, HEAP, lsl #32
    // 0x1501d00: LoadField: r5 = r4->field_b
    //     0x1501d00: ldur            w5, [x4, #0xb]
    // 0x1501d04: r4 = LoadInt32Instr(r1)
    //     0x1501d04: sbfx            x4, x1, #1, #0x1f
    // 0x1501d08: stur            x4, [fp, #-0x78]
    // 0x1501d0c: r1 = LoadInt32Instr(r5)
    //     0x1501d0c: sbfx            x1, x5, #1, #0x1f
    // 0x1501d10: cmp             x4, x1
    // 0x1501d14: b.ne            #0x1501d20
    // 0x1501d18: mov             x1, x3
    // 0x1501d1c: r0 = _growToNextCapacity()
    //     0x1501d1c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1501d20: ldur            x2, [fp, #-0x20]
    // 0x1501d24: ldur            x3, [fp, #-0x78]
    // 0x1501d28: add             x0, x3, #1
    // 0x1501d2c: lsl             x1, x0, #1
    // 0x1501d30: StoreField: r2->field_b = r1
    //     0x1501d30: stur            w1, [x2, #0xb]
    // 0x1501d34: LoadField: r1 = r2->field_f
    //     0x1501d34: ldur            w1, [x2, #0xf]
    // 0x1501d38: DecompressPointer r1
    //     0x1501d38: add             x1, x1, HEAP, lsl #32
    // 0x1501d3c: ldur            x0, [fp, #-0x38]
    // 0x1501d40: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1501d40: add             x25, x1, x3, lsl #2
    //     0x1501d44: add             x25, x25, #0xf
    //     0x1501d48: str             w0, [x25]
    //     0x1501d4c: tbz             w0, #0, #0x1501d68
    //     0x1501d50: ldurb           w16, [x1, #-1]
    //     0x1501d54: ldurb           w17, [x0, #-1]
    //     0x1501d58: and             x16, x17, x16, lsr #2
    //     0x1501d5c: tst             x16, HEAP, lsr #32
    //     0x1501d60: b.eq            #0x1501d68
    //     0x1501d64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1501d68: b               #0x1501d70
    // 0x1501d6c: ldur            x2, [fp, #-0x20]
    // 0x1501d70: ldur            x0, [fp, #-0x10]
    // 0x1501d74: LoadField: r1 = r0->field_f
    //     0x1501d74: ldur            w1, [x0, #0xf]
    // 0x1501d78: DecompressPointer r1
    //     0x1501d78: add             x1, x1, HEAP, lsl #32
    // 0x1501d7c: r0 = controller()
    //     0x1501d7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1501d80: LoadField: r1 = r0->field_73
    //     0x1501d80: ldur            w1, [x0, #0x73]
    // 0x1501d84: DecompressPointer r1
    //     0x1501d84: add             x1, x1, HEAP, lsl #32
    // 0x1501d88: r0 = value()
    //     0x1501d88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1501d8c: LoadField: r1 = r0->field_b
    //     0x1501d8c: ldur            w1, [x0, #0xb]
    // 0x1501d90: DecompressPointer r1
    //     0x1501d90: add             x1, x1, HEAP, lsl #32
    // 0x1501d94: cmp             w1, NULL
    // 0x1501d98: b.ne            #0x1501da4
    // 0x1501d9c: r0 = Null
    //     0x1501d9c: mov             x0, NULL
    // 0x1501da0: b               #0x1501dc8
    // 0x1501da4: LoadField: r0 = r1->field_f
    //     0x1501da4: ldur            w0, [x1, #0xf]
    // 0x1501da8: DecompressPointer r0
    //     0x1501da8: add             x0, x0, HEAP, lsl #32
    // 0x1501dac: cmp             w0, NULL
    // 0x1501db0: b.ne            #0x1501dbc
    // 0x1501db4: r0 = Null
    //     0x1501db4: mov             x0, NULL
    // 0x1501db8: b               #0x1501dc8
    // 0x1501dbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1501dbc: ldur            w1, [x0, #0x17]
    // 0x1501dc0: DecompressPointer r1
    //     0x1501dc0: add             x1, x1, HEAP, lsl #32
    // 0x1501dc4: mov             x0, x1
    // 0x1501dc8: cmp             w0, NULL
    // 0x1501dcc: b.ne            #0x1501de0
    // 0x1501dd0: r1 = <BEntities>
    //     0x1501dd0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0x1501dd4: ldr             x1, [x1, #0x130]
    // 0x1501dd8: r2 = 0
    //     0x1501dd8: movz            x2, #0
    // 0x1501ddc: r0 = AllocateArray()
    //     0x1501ddc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1501de0: ldur            x1, [fp, #-0x20]
    // 0x1501de4: r2 = LoadClassIdInstr(r0)
    //     0x1501de4: ldur            x2, [x0, #-1]
    //     0x1501de8: ubfx            x2, x2, #0xc, #0x14
    // 0x1501dec: str             x0, [SP]
    // 0x1501df0: mov             x0, x2
    // 0x1501df4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1501df4: movz            x17, #0xc898
    //     0x1501df8: add             lr, x0, x17
    //     0x1501dfc: ldr             lr, [x21, lr, lsl #3]
    //     0x1501e00: blr             lr
    // 0x1501e04: ldur            x2, [fp, #-0x10]
    // 0x1501e08: r1 = Function '<anonymous closure>':.
    //     0x1501e08: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6a0] AnonymousClosure: (0x150414c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x150131c)
    //     0x1501e0c: ldr             x1, [x1, #0x6a0]
    // 0x1501e10: stur            x0, [fp, #-0x28]
    // 0x1501e14: r0 = AllocateClosure()
    //     0x1501e14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1501e18: stur            x0, [fp, #-0x38]
    // 0x1501e1c: r0 = ListView()
    //     0x1501e1c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1501e20: stur            x0, [fp, #-0x40]
    // 0x1501e24: r16 = true
    //     0x1501e24: add             x16, NULL, #0x20  ; true
    // 0x1501e28: r30 = false
    //     0x1501e28: add             lr, NULL, #0x30  ; false
    // 0x1501e2c: stp             lr, x16, [SP, #8]
    // 0x1501e30: r16 = Instance_NeverScrollableScrollPhysics
    //     0x1501e30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x1501e34: ldr             x16, [x16, #0x1c8]
    // 0x1501e38: str             x16, [SP]
    // 0x1501e3c: mov             x1, x0
    // 0x1501e40: ldur            x2, [fp, #-0x38]
    // 0x1501e44: ldur            x3, [fp, #-0x28]
    // 0x1501e48: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x1501e48: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fd8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x1501e4c: ldr             x4, [x4, #0xfd8]
    // 0x1501e50: r0 = ListView.builder()
    //     0x1501e50: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1501e54: ldur            x0, [fp, #-0x20]
    // 0x1501e58: LoadField: r1 = r0->field_b
    //     0x1501e58: ldur            w1, [x0, #0xb]
    // 0x1501e5c: LoadField: r2 = r0->field_f
    //     0x1501e5c: ldur            w2, [x0, #0xf]
    // 0x1501e60: DecompressPointer r2
    //     0x1501e60: add             x2, x2, HEAP, lsl #32
    // 0x1501e64: LoadField: r3 = r2->field_b
    //     0x1501e64: ldur            w3, [x2, #0xb]
    // 0x1501e68: r2 = LoadInt32Instr(r1)
    //     0x1501e68: sbfx            x2, x1, #1, #0x1f
    // 0x1501e6c: stur            x2, [fp, #-0x78]
    // 0x1501e70: r1 = LoadInt32Instr(r3)
    //     0x1501e70: sbfx            x1, x3, #1, #0x1f
    // 0x1501e74: cmp             x2, x1
    // 0x1501e78: b.ne            #0x1501e84
    // 0x1501e7c: mov             x1, x0
    // 0x1501e80: r0 = _growToNextCapacity()
    //     0x1501e80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1501e84: ldur            x4, [fp, #-0x10]
    // 0x1501e88: ldur            x5, [fp, #-0x30]
    // 0x1501e8c: ldur            x2, [fp, #-0x20]
    // 0x1501e90: ldur            x3, [fp, #-0x78]
    // 0x1501e94: add             x0, x3, #1
    // 0x1501e98: lsl             x1, x0, #1
    // 0x1501e9c: StoreField: r2->field_b = r1
    //     0x1501e9c: stur            w1, [x2, #0xb]
    // 0x1501ea0: LoadField: r1 = r2->field_f
    //     0x1501ea0: ldur            w1, [x2, #0xf]
    // 0x1501ea4: DecompressPointer r1
    //     0x1501ea4: add             x1, x1, HEAP, lsl #32
    // 0x1501ea8: ldur            x0, [fp, #-0x40]
    // 0x1501eac: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1501eac: add             x25, x1, x3, lsl #2
    //     0x1501eb0: add             x25, x25, #0xf
    //     0x1501eb4: str             w0, [x25]
    //     0x1501eb8: tbz             w0, #0, #0x1501ed4
    //     0x1501ebc: ldurb           w16, [x1, #-1]
    //     0x1501ec0: ldurb           w17, [x0, #-1]
    //     0x1501ec4: and             x16, x17, x16, lsr #2
    //     0x1501ec8: tst             x16, HEAP, lsr #32
    //     0x1501ecc: b.eq            #0x1501ed4
    //     0x1501ed0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1501ed4: r0 = Column()
    //     0x1501ed4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1501ed8: mov             x1, x0
    // 0x1501edc: r0 = Instance_Axis
    //     0x1501edc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1501ee0: stur            x1, [fp, #-0x28]
    // 0x1501ee4: StoreField: r1->field_f = r0
    //     0x1501ee4: stur            w0, [x1, #0xf]
    // 0x1501ee8: r0 = Instance_MainAxisAlignment
    //     0x1501ee8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1501eec: ldr             x0, [x0, #0xa08]
    // 0x1501ef0: StoreField: r1->field_13 = r0
    //     0x1501ef0: stur            w0, [x1, #0x13]
    // 0x1501ef4: r0 = Instance_MainAxisSize
    //     0x1501ef4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1501ef8: ldr             x0, [x0, #0xa10]
    // 0x1501efc: ArrayStore: r1[0] = r0  ; List_4
    //     0x1501efc: stur            w0, [x1, #0x17]
    // 0x1501f00: r2 = Instance_CrossAxisAlignment
    //     0x1501f00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1501f04: ldr             x2, [x2, #0xa18]
    // 0x1501f08: StoreField: r1->field_1b = r2
    //     0x1501f08: stur            w2, [x1, #0x1b]
    // 0x1501f0c: r3 = Instance_VerticalDirection
    //     0x1501f0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1501f10: ldr             x3, [x3, #0xa20]
    // 0x1501f14: StoreField: r1->field_23 = r3
    //     0x1501f14: stur            w3, [x1, #0x23]
    // 0x1501f18: r4 = Instance_Clip
    //     0x1501f18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1501f1c: ldr             x4, [x4, #0x38]
    // 0x1501f20: StoreField: r1->field_2b = r4
    //     0x1501f20: stur            w4, [x1, #0x2b]
    // 0x1501f24: StoreField: r1->field_2f = rZR
    //     0x1501f24: stur            xzr, [x1, #0x2f]
    // 0x1501f28: ldur            x5, [fp, #-0x20]
    // 0x1501f2c: StoreField: r1->field_b = r5
    //     0x1501f2c: stur            w5, [x1, #0xb]
    // 0x1501f30: r0 = Container()
    //     0x1501f30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1501f34: mov             x1, x0
    // 0x1501f38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1501f38: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1501f3c: r0 = Container()
    //     0x1501f3c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1501f40: r0 = Accordion()
    //     0x1501f40: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0x1501f44: mov             x1, x0
    // 0x1501f48: ldur            x0, [fp, #-0x30]
    // 0x1501f4c: stur            x1, [fp, #-0x20]
    // 0x1501f50: StoreField: r1->field_b = r0
    //     0x1501f50: stur            w0, [x1, #0xb]
    // 0x1501f54: ldur            x0, [fp, #-0x28]
    // 0x1501f58: StoreField: r1->field_13 = r0
    //     0x1501f58: stur            w0, [x1, #0x13]
    // 0x1501f5c: r0 = false
    //     0x1501f5c: add             x0, NULL, #0x30  ; false
    // 0x1501f60: ArrayStore: r1[0] = r0  ; List_4
    //     0x1501f60: stur            w0, [x1, #0x17]
    // 0x1501f64: r0 = 25.000000
    //     0x1501f64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x1501f68: ldr             x0, [x0, #0x98]
    // 0x1501f6c: StoreField: r1->field_1b = r0
    //     0x1501f6c: stur            w0, [x1, #0x1b]
    // 0x1501f70: r0 = true
    //     0x1501f70: add             x0, NULL, #0x20  ; true
    // 0x1501f74: StoreField: r1->field_1f = r0
    //     0x1501f74: stur            w0, [x1, #0x1f]
    // 0x1501f78: r0 = Padding()
    //     0x1501f78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1501f7c: mov             x2, x0
    // 0x1501f80: r0 = Instance_EdgeInsets
    //     0x1501f80: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c6a8] Obj!EdgeInsets@d59ba1
    //     0x1501f84: ldr             x0, [x0, #0x6a8]
    // 0x1501f88: stur            x2, [fp, #-0x28]
    // 0x1501f8c: StoreField: r2->field_f = r0
    //     0x1501f8c: stur            w0, [x2, #0xf]
    // 0x1501f90: ldur            x0, [fp, #-0x20]
    // 0x1501f94: StoreField: r2->field_b = r0
    //     0x1501f94: stur            w0, [x2, #0xb]
    // 0x1501f98: r1 = Instance_Color
    //     0x1501f98: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1501f9c: d0 = 0.100000
    //     0x1501f9c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1501fa0: r0 = withOpacity()
    //     0x1501fa0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1501fa4: stur            x0, [fp, #-0x20]
    // 0x1501fa8: r0 = Divider()
    //     0x1501fa8: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x1501fac: mov             x2, x0
    // 0x1501fb0: ldur            x0, [fp, #-0x20]
    // 0x1501fb4: stur            x2, [fp, #-0x30]
    // 0x1501fb8: StoreField: r2->field_1f = r0
    //     0x1501fb8: stur            w0, [x2, #0x1f]
    // 0x1501fbc: ldur            x0, [fp, #-0x10]
    // 0x1501fc0: LoadField: r1 = r0->field_f
    //     0x1501fc0: ldur            w1, [x0, #0xf]
    // 0x1501fc4: DecompressPointer r1
    //     0x1501fc4: add             x1, x1, HEAP, lsl #32
    // 0x1501fc8: r0 = controller()
    //     0x1501fc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1501fcc: LoadField: r1 = r0->field_87
    //     0x1501fcc: ldur            w1, [x0, #0x87]
    // 0x1501fd0: DecompressPointer r1
    //     0x1501fd0: add             x1, x1, HEAP, lsl #32
    // 0x1501fd4: r0 = value()
    //     0x1501fd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1501fd8: LoadField: r1 = r0->field_2f
    //     0x1501fd8: ldur            w1, [x0, #0x2f]
    // 0x1501fdc: DecompressPointer r1
    //     0x1501fdc: add             x1, x1, HEAP, lsl #32
    // 0x1501fe0: cmp             w1, NULL
    // 0x1501fe4: b.ne            #0x1501ff0
    // 0x1501fe8: r0 = ""
    //     0x1501fe8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1501fec: b               #0x1501ff4
    // 0x1501ff0: mov             x0, x1
    // 0x1501ff4: ldur            x2, [fp, #-0x10]
    // 0x1501ff8: stur            x0, [fp, #-0x20]
    // 0x1501ffc: LoadField: r1 = r2->field_13
    //     0x1501ffc: ldur            w1, [x2, #0x13]
    // 0x1502000: DecompressPointer r1
    //     0x1502000: add             x1, x1, HEAP, lsl #32
    // 0x1502004: r0 = of()
    //     0x1502004: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1502008: LoadField: r1 = r0->field_87
    //     0x1502008: ldur            w1, [x0, #0x87]
    // 0x150200c: DecompressPointer r1
    //     0x150200c: add             x1, x1, HEAP, lsl #32
    // 0x1502010: LoadField: r0 = r1->field_7
    //     0x1502010: ldur            w0, [x1, #7]
    // 0x1502014: DecompressPointer r0
    //     0x1502014: add             x0, x0, HEAP, lsl #32
    // 0x1502018: stur            x0, [fp, #-0x38]
    // 0x150201c: r1 = Instance_Color
    //     0x150201c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1502020: d0 = 0.700000
    //     0x1502020: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1502024: ldr             d0, [x17, #0xf48]
    // 0x1502028: r0 = withOpacity()
    //     0x1502028: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x150202c: r16 = 14.000000
    //     0x150202c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1502030: ldr             x16, [x16, #0x1d8]
    // 0x1502034: stp             x16, x0, [SP]
    // 0x1502038: ldur            x1, [fp, #-0x38]
    // 0x150203c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x150203c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1502040: ldr             x4, [x4, #0x9b8]
    // 0x1502044: r0 = copyWith()
    //     0x1502044: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1502048: stur            x0, [fp, #-0x38]
    // 0x150204c: r0 = Text()
    //     0x150204c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1502050: mov             x1, x0
    // 0x1502054: ldur            x0, [fp, #-0x20]
    // 0x1502058: stur            x1, [fp, #-0x40]
    // 0x150205c: StoreField: r1->field_b = r0
    //     0x150205c: stur            w0, [x1, #0xb]
    // 0x1502060: ldur            x0, [fp, #-0x38]
    // 0x1502064: StoreField: r1->field_13 = r0
    //     0x1502064: stur            w0, [x1, #0x13]
    // 0x1502068: r0 = SvgPicture()
    //     0x1502068: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x150206c: stur            x0, [fp, #-0x20]
    // 0x1502070: r16 = "return order"
    //     0x1502070: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x1502074: ldr             x16, [x16, #0xc78]
    // 0x1502078: r30 = Instance_BoxFit
    //     0x1502078: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x150207c: ldr             lr, [lr, #0xb18]
    // 0x1502080: stp             lr, x16, [SP]
    // 0x1502084: mov             x1, x0
    // 0x1502088: r2 = "assets/images/secure_icon.svg"
    //     0x1502088: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0x150208c: ldr             x2, [x2, #0xc80]
    // 0x1502090: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x1502090: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x1502094: ldr             x4, [x4, #0xb28]
    // 0x1502098: r0 = SvgPicture.asset()
    //     0x1502098: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x150209c: r1 = Null
    //     0x150209c: mov             x1, NULL
    // 0x15020a0: r2 = 4
    //     0x15020a0: movz            x2, #0x4
    // 0x15020a4: r0 = AllocateArray()
    //     0x15020a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15020a8: mov             x2, x0
    // 0x15020ac: ldur            x0, [fp, #-0x40]
    // 0x15020b0: stur            x2, [fp, #-0x38]
    // 0x15020b4: StoreField: r2->field_f = r0
    //     0x15020b4: stur            w0, [x2, #0xf]
    // 0x15020b8: ldur            x0, [fp, #-0x20]
    // 0x15020bc: StoreField: r2->field_13 = r0
    //     0x15020bc: stur            w0, [x2, #0x13]
    // 0x15020c0: r1 = <Widget>
    //     0x15020c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15020c4: r0 = AllocateGrowableArray()
    //     0x15020c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15020c8: mov             x1, x0
    // 0x15020cc: ldur            x0, [fp, #-0x38]
    // 0x15020d0: stur            x1, [fp, #-0x20]
    // 0x15020d4: StoreField: r1->field_f = r0
    //     0x15020d4: stur            w0, [x1, #0xf]
    // 0x15020d8: r0 = 4
    //     0x15020d8: movz            x0, #0x4
    // 0x15020dc: StoreField: r1->field_b = r0
    //     0x15020dc: stur            w0, [x1, #0xb]
    // 0x15020e0: r0 = Row()
    //     0x15020e0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15020e4: mov             x1, x0
    // 0x15020e8: r0 = Instance_Axis
    //     0x15020e8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15020ec: stur            x1, [fp, #-0x38]
    // 0x15020f0: StoreField: r1->field_f = r0
    //     0x15020f0: stur            w0, [x1, #0xf]
    // 0x15020f4: r0 = Instance_MainAxisAlignment
    //     0x15020f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x15020f8: ldr             x0, [x0, #0xa8]
    // 0x15020fc: StoreField: r1->field_13 = r0
    //     0x15020fc: stur            w0, [x1, #0x13]
    // 0x1502100: r0 = Instance_MainAxisSize
    //     0x1502100: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1502104: ldr             x0, [x0, #0xa10]
    // 0x1502108: ArrayStore: r1[0] = r0  ; List_4
    //     0x1502108: stur            w0, [x1, #0x17]
    // 0x150210c: r0 = Instance_CrossAxisAlignment
    //     0x150210c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1502110: ldr             x0, [x0, #0xa18]
    // 0x1502114: StoreField: r1->field_1b = r0
    //     0x1502114: stur            w0, [x1, #0x1b]
    // 0x1502118: r0 = Instance_VerticalDirection
    //     0x1502118: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x150211c: ldr             x0, [x0, #0xa20]
    // 0x1502120: StoreField: r1->field_23 = r0
    //     0x1502120: stur            w0, [x1, #0x23]
    // 0x1502124: r0 = Instance_Clip
    //     0x1502124: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1502128: ldr             x0, [x0, #0x38]
    // 0x150212c: StoreField: r1->field_2b = r0
    //     0x150212c: stur            w0, [x1, #0x2b]
    // 0x1502130: StoreField: r1->field_2f = rZR
    //     0x1502130: stur            xzr, [x1, #0x2f]
    // 0x1502134: ldur            x0, [fp, #-0x20]
    // 0x1502138: StoreField: r1->field_b = r0
    //     0x1502138: stur            w0, [x1, #0xb]
    // 0x150213c: r0 = Padding()
    //     0x150213c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1502140: mov             x2, x0
    // 0x1502144: r0 = Instance_EdgeInsets
    //     0x1502144: add             x0, PP, #0x3a, lsl #12  ; [pp+0x3af88] Obj!EdgeInsets@d59b71
    //     0x1502148: ldr             x0, [x0, #0xf88]
    // 0x150214c: stur            x2, [fp, #-0x20]
    // 0x1502150: StoreField: r2->field_f = r0
    //     0x1502150: stur            w0, [x2, #0xf]
    // 0x1502154: ldur            x0, [fp, #-0x38]
    // 0x1502158: StoreField: r2->field_b = r0
    //     0x1502158: stur            w0, [x2, #0xb]
    // 0x150215c: ldur            x0, [fp, #-0x10]
    // 0x1502160: LoadField: r1 = r0->field_f
    //     0x1502160: ldur            w1, [x0, #0xf]
    // 0x1502164: DecompressPointer r1
    //     0x1502164: add             x1, x1, HEAP, lsl #32
    // 0x1502168: r0 = controller()
    //     0x1502168: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150216c: LoadField: r1 = r0->field_87
    //     0x150216c: ldur            w1, [x0, #0x87]
    // 0x1502170: DecompressPointer r1
    //     0x1502170: add             x1, x1, HEAP, lsl #32
    // 0x1502174: r0 = value()
    //     0x1502174: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502178: LoadField: r1 = r0->field_1f
    //     0x1502178: ldur            w1, [x0, #0x1f]
    // 0x150217c: DecompressPointer r1
    //     0x150217c: add             x1, x1, HEAP, lsl #32
    // 0x1502180: cmp             w1, NULL
    // 0x1502184: b.ne            #0x150219c
    // 0x1502188: r1 = <PaymentOptions>
    //     0x1502188: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x150218c: ldr             x1, [x1, #0x5a8]
    // 0x1502190: r2 = 0
    //     0x1502190: movz            x2, #0
    // 0x1502194: r0 = AllocateArray()
    //     0x1502194: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1502198: b               #0x15021a0
    // 0x150219c: mov             x0, x1
    // 0x15021a0: ldur            x2, [fp, #-0x10]
    // 0x15021a4: ldur            x5, [fp, #-0x18]
    // 0x15021a8: ldur            x4, [fp, #-0x28]
    // 0x15021ac: ldur            x3, [fp, #-0x30]
    // 0x15021b0: ldur            x1, [fp, #-0x20]
    // 0x15021b4: r6 = LoadClassIdInstr(r0)
    //     0x15021b4: ldur            x6, [x0, #-1]
    //     0x15021b8: ubfx            x6, x6, #0xc, #0x14
    // 0x15021bc: str             x0, [SP]
    // 0x15021c0: mov             x0, x6
    // 0x15021c4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x15021c4: movz            x17, #0xc898
    //     0x15021c8: add             lr, x0, x17
    //     0x15021cc: ldr             lr, [x21, lr, lsl #3]
    //     0x15021d0: blr             lr
    // 0x15021d4: r3 = LoadInt32Instr(r0)
    //     0x15021d4: sbfx            x3, x0, #1, #0x1f
    // 0x15021d8: ldur            x2, [fp, #-0x10]
    // 0x15021dc: stur            x3, [fp, #-0x78]
    // 0x15021e0: r1 = Function '<anonymous closure>':.
    //     0x15021e0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6b0] AnonymousClosure: (0x1504078), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x150131c)
    //     0x15021e4: ldr             x1, [x1, #0x6b0]
    // 0x15021e8: r0 = AllocateClosure()
    //     0x15021e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15021ec: r1 = Function '<anonymous closure>':.
    //     0x15021ec: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6b8] AnonymousClosure: (0x15023c8), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x1505978)
    //     0x15021f0: ldr             x1, [x1, #0x6b8]
    // 0x15021f4: r2 = Null
    //     0x15021f4: mov             x2, NULL
    // 0x15021f8: stur            x0, [fp, #-0x38]
    // 0x15021fc: r0 = AllocateClosure()
    //     0x15021fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1502200: stur            x0, [fp, #-0x40]
    // 0x1502204: r0 = ListView()
    //     0x1502204: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1502208: stur            x0, [fp, #-0x48]
    // 0x150220c: r16 = true
    //     0x150220c: add             x16, NULL, #0x20  ; true
    // 0x1502210: r30 = false
    //     0x1502210: add             lr, NULL, #0x30  ; false
    // 0x1502214: stp             lr, x16, [SP]
    // 0x1502218: mov             x1, x0
    // 0x150221c: ldur            x2, [fp, #-0x38]
    // 0x1502220: ldur            x3, [fp, #-0x78]
    // 0x1502224: ldur            x5, [fp, #-0x40]
    // 0x1502228: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x1502228: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x150222c: ldr             x4, [x4, #0xc98]
    // 0x1502230: r0 = ListView.separated()
    //     0x1502230: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x1502234: r0 = Padding()
    //     0x1502234: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1502238: mov             x2, x0
    // 0x150223c: r0 = Instance_EdgeInsets
    //     0x150223c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1502240: ldr             x0, [x0, #0x1f0]
    // 0x1502244: stur            x2, [fp, #-0x38]
    // 0x1502248: StoreField: r2->field_f = r0
    //     0x1502248: stur            w0, [x2, #0xf]
    // 0x150224c: ldur            x1, [fp, #-0x48]
    // 0x1502250: StoreField: r2->field_b = r1
    //     0x1502250: stur            w1, [x2, #0xb]
    // 0x1502254: ldur            x1, [fp, #-0x10]
    // 0x1502258: LoadField: r3 = r1->field_f
    //     0x1502258: ldur            w3, [x1, #0xf]
    // 0x150225c: DecompressPointer r3
    //     0x150225c: add             x3, x3, HEAP, lsl #32
    // 0x1502260: mov             x1, x3
    // 0x1502264: r0 = controller()
    //     0x1502264: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502268: LoadField: r1 = r0->field_57
    //     0x1502268: ldur            w1, [x0, #0x57]
    // 0x150226c: DecompressPointer r1
    //     0x150226c: add             x1, x1, HEAP, lsl #32
    // 0x1502270: r0 = value()
    //     0x1502270: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502274: stur            x0, [fp, #-0x10]
    // 0x1502278: r0 = PaymentTrustMarkers()
    //     0x1502278: bl              #0xbc7fe4  ; AllocatePaymentTrustMarkersStub -> PaymentTrustMarkers (size=0x14)
    // 0x150227c: mov             x1, x0
    // 0x1502280: r0 = true
    //     0x1502280: add             x0, NULL, #0x20  ; true
    // 0x1502284: stur            x1, [fp, #-0x40]
    // 0x1502288: StoreField: r1->field_b = r0
    //     0x1502288: stur            w0, [x1, #0xb]
    // 0x150228c: ldur            x0, [fp, #-0x10]
    // 0x1502290: StoreField: r1->field_f = r0
    //     0x1502290: stur            w0, [x1, #0xf]
    // 0x1502294: r0 = Padding()
    //     0x1502294: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1502298: mov             x3, x0
    // 0x150229c: r0 = Instance_EdgeInsets
    //     0x150229c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x15022a0: ldr             x0, [x0, #0x1f0]
    // 0x15022a4: stur            x3, [fp, #-0x10]
    // 0x15022a8: StoreField: r3->field_f = r0
    //     0x15022a8: stur            w0, [x3, #0xf]
    // 0x15022ac: ldur            x0, [fp, #-0x40]
    // 0x15022b0: StoreField: r3->field_b = r0
    //     0x15022b0: stur            w0, [x3, #0xb]
    // 0x15022b4: r1 = Null
    //     0x15022b4: mov             x1, NULL
    // 0x15022b8: r2 = 12
    //     0x15022b8: movz            x2, #0xc
    // 0x15022bc: r0 = AllocateArray()
    //     0x15022bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15022c0: mov             x2, x0
    // 0x15022c4: ldur            x0, [fp, #-0x18]
    // 0x15022c8: stur            x2, [fp, #-0x40]
    // 0x15022cc: StoreField: r2->field_f = r0
    //     0x15022cc: stur            w0, [x2, #0xf]
    // 0x15022d0: ldur            x0, [fp, #-0x28]
    // 0x15022d4: StoreField: r2->field_13 = r0
    //     0x15022d4: stur            w0, [x2, #0x13]
    // 0x15022d8: ldur            x0, [fp, #-0x30]
    // 0x15022dc: ArrayStore: r2[0] = r0  ; List_4
    //     0x15022dc: stur            w0, [x2, #0x17]
    // 0x15022e0: ldur            x0, [fp, #-0x20]
    // 0x15022e4: StoreField: r2->field_1b = r0
    //     0x15022e4: stur            w0, [x2, #0x1b]
    // 0x15022e8: ldur            x0, [fp, #-0x38]
    // 0x15022ec: StoreField: r2->field_1f = r0
    //     0x15022ec: stur            w0, [x2, #0x1f]
    // 0x15022f0: ldur            x0, [fp, #-0x10]
    // 0x15022f4: StoreField: r2->field_23 = r0
    //     0x15022f4: stur            w0, [x2, #0x23]
    // 0x15022f8: r1 = <Widget>
    //     0x15022f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15022fc: r0 = AllocateGrowableArray()
    //     0x15022fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1502300: mov             x1, x0
    // 0x1502304: ldur            x0, [fp, #-0x40]
    // 0x1502308: stur            x1, [fp, #-0x10]
    // 0x150230c: StoreField: r1->field_f = r0
    //     0x150230c: stur            w0, [x1, #0xf]
    // 0x1502310: r0 = 12
    //     0x1502310: movz            x0, #0xc
    // 0x1502314: StoreField: r1->field_b = r0
    //     0x1502314: stur            w0, [x1, #0xb]
    // 0x1502318: r0 = ListView()
    //     0x1502318: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x150231c: stur            x0, [fp, #-0x18]
    // 0x1502320: r16 = true
    //     0x1502320: add             x16, NULL, #0x20  ; true
    // 0x1502324: r30 = false
    //     0x1502324: add             lr, NULL, #0x30  ; false
    // 0x1502328: stp             lr, x16, [SP, #8]
    // 0x150232c: r16 = Instance_RangeMaintainingScrollPhysics
    //     0x150232c: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3afa0] Obj!RangeMaintainingScrollPhysics@d55901
    //     0x1502330: ldr             x16, [x16, #0xfa0]
    // 0x1502334: str             x16, [SP]
    // 0x1502338: mov             x1, x0
    // 0x150233c: ldur            x2, [fp, #-0x10]
    // 0x1502340: r4 = const [0, 0x5, 0x3, 0x2, physics, 0x4, primary, 0x3, shrinkWrap, 0x2, null]
    //     0x1502340: add             x4, PP, #0x3a, lsl #12  ; [pp+0x3afa8] List(11) [0, 0x5, 0x3, 0x2, "physics", 0x4, "primary", 0x3, "shrinkWrap", 0x2, Null]
    //     0x1502344: ldr             x4, [x4, #0xfa8]
    // 0x1502348: r0 = ListView()
    //     0x1502348: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x150234c: r0 = WillPopScope()
    //     0x150234c: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1502350: mov             x3, x0
    // 0x1502354: ldur            x0, [fp, #-0x18]
    // 0x1502358: stur            x3, [fp, #-0x10]
    // 0x150235c: StoreField: r3->field_b = r0
    //     0x150235c: stur            w0, [x3, #0xb]
    // 0x1502360: ldur            x2, [fp, #-8]
    // 0x1502364: r1 = Function 'onBackPress':.
    //     0x1502364: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6c0] AnonymousClosure: (0x1502390), in [package:customer_app/app/presentation/views/basic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::onBackPress (0x13ac748)
    //     0x1502368: ldr             x1, [x1, #0x6c0]
    // 0x150236c: r0 = AllocateClosure()
    //     0x150236c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1502370: mov             x1, x0
    // 0x1502374: ldur            x0, [fp, #-0x10]
    // 0x1502378: StoreField: r0->field_f = r1
    //     0x1502378: stur            w1, [x0, #0xf]
    // 0x150237c: LeaveFrame
    //     0x150237c: mov             SP, fp
    //     0x1502380: ldp             fp, lr, [SP], #0x10
    // 0x1502384: ret
    //     0x1502384: ret             
    // 0x1502388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1502388: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x150238c: b               #0x15013a8
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x1502390, size: 0x38
    // 0x1502390: EnterFrame
    //     0x1502390: stp             fp, lr, [SP, #-0x10]!
    //     0x1502394: mov             fp, SP
    // 0x1502398: ldr             x0, [fp, #0x10]
    // 0x150239c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x150239c: ldur            w1, [x0, #0x17]
    // 0x15023a0: DecompressPointer r1
    //     0x15023a0: add             x1, x1, HEAP, lsl #32
    // 0x15023a4: CheckStackOverflow
    //     0x15023a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15023a8: cmp             SP, x16
    //     0x15023ac: b.ls            #0x15023c0
    // 0x15023b0: r0 = onBackPress()
    //     0x15023b0: bl              #0x13ac748  ; [package:customer_app/app/presentation/views/basic/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::onBackPress
    // 0x15023b4: LeaveFrame
    //     0x15023b4: mov             SP, fp
    //     0x15023b8: ldp             fp, lr, [SP], #0x10
    // 0x15023bc: ret
    //     0x15023bc: ret             
    // 0x15023c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15023c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15023c4: b               #0x15023b0
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x1504078, size: 0xd4
    // 0x1504078: EnterFrame
    //     0x1504078: stp             fp, lr, [SP, #-0x10]!
    //     0x150407c: mov             fp, SP
    // 0x1504080: AllocStack(0x8)
    //     0x1504080: sub             SP, SP, #8
    // 0x1504084: SetupParameters()
    //     0x1504084: ldr             x0, [fp, #0x20]
    //     0x1504088: ldur            w1, [x0, #0x17]
    //     0x150408c: add             x1, x1, HEAP, lsl #32
    // 0x1504090: CheckStackOverflow
    //     0x1504090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1504094: cmp             SP, x16
    //     0x1504098: b.ls            #0x1504140
    // 0x150409c: LoadField: r0 = r1->field_f
    //     0x150409c: ldur            w0, [x1, #0xf]
    // 0x15040a0: DecompressPointer r0
    //     0x15040a0: add             x0, x0, HEAP, lsl #32
    // 0x15040a4: mov             x1, x0
    // 0x15040a8: stur            x0, [fp, #-8]
    // 0x15040ac: r0 = controller()
    //     0x15040ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15040b0: LoadField: r1 = r0->field_87
    //     0x15040b0: ldur            w1, [x0, #0x87]
    // 0x15040b4: DecompressPointer r1
    //     0x15040b4: add             x1, x1, HEAP, lsl #32
    // 0x15040b8: r0 = value()
    //     0x15040b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15040bc: LoadField: r2 = r0->field_1f
    //     0x15040bc: ldur            w2, [x0, #0x1f]
    // 0x15040c0: DecompressPointer r2
    //     0x15040c0: add             x2, x2, HEAP, lsl #32
    // 0x15040c4: cmp             w2, NULL
    // 0x15040c8: b.ne            #0x15040d8
    // 0x15040cc: ldr             x3, [fp, #0x10]
    // 0x15040d0: r2 = Null
    //     0x15040d0: mov             x2, NULL
    // 0x15040d4: b               #0x1504118
    // 0x15040d8: ldr             x3, [fp, #0x10]
    // 0x15040dc: LoadField: r0 = r2->field_b
    //     0x15040dc: ldur            w0, [x2, #0xb]
    // 0x15040e0: r4 = LoadInt32Instr(r3)
    //     0x15040e0: sbfx            x4, x3, #1, #0x1f
    //     0x15040e4: tbz             w3, #0, #0x15040ec
    //     0x15040e8: ldur            x4, [x3, #7]
    // 0x15040ec: r1 = LoadInt32Instr(r0)
    //     0x15040ec: sbfx            x1, x0, #1, #0x1f
    // 0x15040f0: mov             x0, x1
    // 0x15040f4: mov             x1, x4
    // 0x15040f8: cmp             x1, x0
    // 0x15040fc: b.hs            #0x1504148
    // 0x1504100: LoadField: r0 = r2->field_f
    //     0x1504100: ldur            w0, [x2, #0xf]
    // 0x1504104: DecompressPointer r0
    //     0x1504104: add             x0, x0, HEAP, lsl #32
    // 0x1504108: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x1504108: add             x16, x0, x4, lsl #2
    //     0x150410c: ldur            w1, [x16, #0xf]
    // 0x1504110: DecompressPointer r1
    //     0x1504110: add             x1, x1, HEAP, lsl #32
    // 0x1504114: mov             x2, x1
    // 0x1504118: r0 = LoadInt32Instr(r3)
    //     0x1504118: sbfx            x0, x3, #1, #0x1f
    //     0x150411c: tbz             w3, #0, #0x1504124
    //     0x1504120: ldur            x0, [x3, #7]
    // 0x1504124: ldur            x1, [fp, #-8]
    // 0x1504128: mov             x3, x0
    // 0x150412c: ldr             x5, [fp, #0x18]
    // 0x1504130: r0 = lineThemePaymentMethodCard()
    //     0x1504130: bl              #0x13ad294  ; [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::lineThemePaymentMethodCard
    // 0x1504134: LeaveFrame
    //     0x1504134: mov             SP, fp
    //     0x1504138: ldp             fp, lr, [SP], #0x10
    // 0x150413c: ret
    //     0x150413c: ret             
    // 0x1504140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1504140: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1504144: b               #0x150409c
    // 0x1504148: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1504148: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x150414c, size: 0x74
    // 0x150414c: EnterFrame
    //     0x150414c: stp             fp, lr, [SP, #-0x10]!
    //     0x1504150: mov             fp, SP
    // 0x1504154: AllocStack(0x10)
    //     0x1504154: sub             SP, SP, #0x10
    // 0x1504158: SetupParameters()
    //     0x1504158: ldr             x0, [fp, #0x20]
    //     0x150415c: ldur            w1, [x0, #0x17]
    //     0x1504160: add             x1, x1, HEAP, lsl #32
    //     0x1504164: stur            x1, [fp, #-8]
    // 0x1504168: r1 = 2
    //     0x1504168: movz            x1, #0x2
    // 0x150416c: r0 = AllocateContext()
    //     0x150416c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1504170: mov             x1, x0
    // 0x1504174: ldur            x0, [fp, #-8]
    // 0x1504178: stur            x1, [fp, #-0x10]
    // 0x150417c: StoreField: r1->field_b = r0
    //     0x150417c: stur            w0, [x1, #0xb]
    // 0x1504180: ldr             x0, [fp, #0x18]
    // 0x1504184: StoreField: r1->field_f = r0
    //     0x1504184: stur            w0, [x1, #0xf]
    // 0x1504188: ldr             x0, [fp, #0x10]
    // 0x150418c: StoreField: r1->field_13 = r0
    //     0x150418c: stur            w0, [x1, #0x13]
    // 0x1504190: r0 = Obx()
    //     0x1504190: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1504194: ldur            x2, [fp, #-0x10]
    // 0x1504198: r1 = Function '<anonymous closure>':.
    //     0x1504198: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6f8] AnonymousClosure: (0x15041c0), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::body (0x150131c)
    //     0x150419c: ldr             x1, [x1, #0x6f8]
    // 0x15041a0: stur            x0, [fp, #-8]
    // 0x15041a4: r0 = AllocateClosure()
    //     0x15041a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15041a8: mov             x1, x0
    // 0x15041ac: ldur            x0, [fp, #-8]
    // 0x15041b0: StoreField: r0->field_b = r1
    //     0x15041b0: stur            w1, [x0, #0xb]
    // 0x15041b4: LeaveFrame
    //     0x15041b4: mov             SP, fp
    //     0x15041b8: ldp             fp, lr, [SP], #0x10
    // 0x15041bc: ret
    //     0x15041bc: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x15041c0, size: 0x134
    // 0x15041c0: EnterFrame
    //     0x15041c0: stp             fp, lr, [SP, #-0x10]!
    //     0x15041c4: mov             fp, SP
    // 0x15041c8: AllocStack(0x10)
    //     0x15041c8: sub             SP, SP, #0x10
    // 0x15041cc: SetupParameters()
    //     0x15041cc: ldr             x0, [fp, #0x10]
    //     0x15041d0: ldur            w2, [x0, #0x17]
    //     0x15041d4: add             x2, x2, HEAP, lsl #32
    //     0x15041d8: stur            x2, [fp, #-0x10]
    // 0x15041dc: CheckStackOverflow
    //     0x15041dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15041e0: cmp             SP, x16
    //     0x15041e4: b.ls            #0x15042e8
    // 0x15041e8: LoadField: r0 = r2->field_b
    //     0x15041e8: ldur            w0, [x2, #0xb]
    // 0x15041ec: DecompressPointer r0
    //     0x15041ec: add             x0, x0, HEAP, lsl #32
    // 0x15041f0: stur            x0, [fp, #-8]
    // 0x15041f4: LoadField: r1 = r0->field_f
    //     0x15041f4: ldur            w1, [x0, #0xf]
    // 0x15041f8: DecompressPointer r1
    //     0x15041f8: add             x1, x1, HEAP, lsl #32
    // 0x15041fc: r0 = controller()
    //     0x15041fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504200: LoadField: r1 = r0->field_73
    //     0x1504200: ldur            w1, [x0, #0x73]
    // 0x1504204: DecompressPointer r1
    //     0x1504204: add             x1, x1, HEAP, lsl #32
    // 0x1504208: r0 = value()
    //     0x1504208: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150420c: LoadField: r1 = r0->field_b
    //     0x150420c: ldur            w1, [x0, #0xb]
    // 0x1504210: DecompressPointer r1
    //     0x1504210: add             x1, x1, HEAP, lsl #32
    // 0x1504214: cmp             w1, NULL
    // 0x1504218: b.ne            #0x1504228
    // 0x150421c: ldur            x3, [fp, #-0x10]
    // 0x1504220: r0 = Null
    //     0x1504220: mov             x0, NULL
    // 0x1504224: b               #0x15042a4
    // 0x1504228: LoadField: r0 = r1->field_f
    //     0x1504228: ldur            w0, [x1, #0xf]
    // 0x150422c: DecompressPointer r0
    //     0x150422c: add             x0, x0, HEAP, lsl #32
    // 0x1504230: cmp             w0, NULL
    // 0x1504234: b.ne            #0x1504244
    // 0x1504238: ldur            x3, [fp, #-0x10]
    // 0x150423c: r0 = Null
    //     0x150423c: mov             x0, NULL
    // 0x1504240: b               #0x15042a4
    // 0x1504244: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x1504244: ldur            w2, [x0, #0x17]
    // 0x1504248: DecompressPointer r2
    //     0x1504248: add             x2, x2, HEAP, lsl #32
    // 0x150424c: cmp             w2, NULL
    // 0x1504250: b.ne            #0x1504260
    // 0x1504254: ldur            x3, [fp, #-0x10]
    // 0x1504258: r0 = Null
    //     0x1504258: mov             x0, NULL
    // 0x150425c: b               #0x15042a4
    // 0x1504260: ldur            x3, [fp, #-0x10]
    // 0x1504264: LoadField: r0 = r3->field_13
    //     0x1504264: ldur            w0, [x3, #0x13]
    // 0x1504268: DecompressPointer r0
    //     0x1504268: add             x0, x0, HEAP, lsl #32
    // 0x150426c: LoadField: r1 = r2->field_b
    //     0x150426c: ldur            w1, [x2, #0xb]
    // 0x1504270: r4 = LoadInt32Instr(r0)
    //     0x1504270: sbfx            x4, x0, #1, #0x1f
    //     0x1504274: tbz             w0, #0, #0x150427c
    //     0x1504278: ldur            x4, [x0, #7]
    // 0x150427c: r0 = LoadInt32Instr(r1)
    //     0x150427c: sbfx            x0, x1, #1, #0x1f
    // 0x1504280: mov             x1, x4
    // 0x1504284: cmp             x1, x0
    // 0x1504288: b.hs            #0x15042f0
    // 0x150428c: LoadField: r0 = r2->field_f
    //     0x150428c: ldur            w0, [x2, #0xf]
    // 0x1504290: DecompressPointer r0
    //     0x1504290: add             x0, x0, HEAP, lsl #32
    // 0x1504294: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x1504294: add             x16, x0, x4, lsl #2
    //     0x1504298: ldur            w1, [x16, #0xf]
    // 0x150429c: DecompressPointer r1
    //     0x150429c: add             x1, x1, HEAP, lsl #32
    // 0x15042a0: mov             x0, x1
    // 0x15042a4: cmp             w0, NULL
    // 0x15042a8: b.ne            #0x15042b8
    // 0x15042ac: r0 = BEntities()
    //     0x15042ac: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0x15042b0: mov             x2, x0
    // 0x15042b4: b               #0x15042bc
    // 0x15042b8: mov             x2, x0
    // 0x15042bc: ldur            x0, [fp, #-0x10]
    // 0x15042c0: ldur            x1, [fp, #-8]
    // 0x15042c4: LoadField: r3 = r0->field_f
    //     0x15042c4: ldur            w3, [x0, #0xf]
    // 0x15042c8: DecompressPointer r3
    //     0x15042c8: add             x3, x3, HEAP, lsl #32
    // 0x15042cc: LoadField: r0 = r1->field_f
    //     0x15042cc: ldur            w0, [x1, #0xf]
    // 0x15042d0: DecompressPointer r0
    //     0x15042d0: add             x0, x0, HEAP, lsl #32
    // 0x15042d4: mov             x1, x0
    // 0x15042d8: r0 = lineThemeBagItem()
    //     0x15042d8: bl              #0x15042f4  ; [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::lineThemeBagItem
    // 0x15042dc: LeaveFrame
    //     0x15042dc: mov             SP, fp
    //     0x15042e0: ldp             fp, lr, [SP], #0x10
    // 0x15042e4: ret
    //     0x15042e4: ret             
    // 0x15042e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15042e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15042ec: b               #0x15041e8
    // 0x15042f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x15042f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ lineThemeBagItem(/* No info */) {
    // ** addr: 0x15042f4, size: 0x7d4
    // 0x15042f4: EnterFrame
    //     0x15042f4: stp             fp, lr, [SP, #-0x10]!
    //     0x15042f8: mov             fp, SP
    // 0x15042fc: AllocStack(0x68)
    //     0x15042fc: sub             SP, SP, #0x68
    // 0x1504300: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x1504300: mov             x4, x1
    //     0x1504304: mov             x0, x3
    //     0x1504308: stur            x3, [fp, #-0x20]
    //     0x150430c: mov             x3, x2
    //     0x1504310: stur            x1, [fp, #-0x10]
    //     0x1504314: stur            x2, [fp, #-0x18]
    // 0x1504318: CheckStackOverflow
    //     0x1504318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x150431c: cmp             SP, x16
    //     0x1504320: b.ls            #0x1504ac0
    // 0x1504324: LoadField: r1 = r3->field_13
    //     0x1504324: ldur            w1, [x3, #0x13]
    // 0x1504328: DecompressPointer r1
    //     0x1504328: add             x1, x1, HEAP, lsl #32
    // 0x150432c: cmp             w1, NULL
    // 0x1504330: b.ne            #0x150433c
    // 0x1504334: r5 = ""
    //     0x1504334: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1504338: b               #0x1504340
    // 0x150433c: mov             x5, x1
    // 0x1504340: stur            x5, [fp, #-8]
    // 0x1504344: r1 = Function '<anonymous closure>':.
    //     0x1504344: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c700] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1504348: ldr             x1, [x1, #0x700]
    // 0x150434c: r2 = Null
    //     0x150434c: mov             x2, NULL
    // 0x1504350: r0 = AllocateClosure()
    //     0x1504350: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1504354: r1 = Function '<anonymous closure>':.
    //     0x1504354: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c708] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1504358: ldr             x1, [x1, #0x708]
    // 0x150435c: r2 = Null
    //     0x150435c: mov             x2, NULL
    // 0x1504360: stur            x0, [fp, #-0x28]
    // 0x1504364: r0 = AllocateClosure()
    //     0x1504364: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1504368: stur            x0, [fp, #-0x30]
    // 0x150436c: r0 = CachedNetworkImage()
    //     0x150436c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1504370: stur            x0, [fp, #-0x38]
    // 0x1504374: r16 = 56.000000
    //     0x1504374: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1504378: ldr             x16, [x16, #0xb78]
    // 0x150437c: r30 = 56.000000
    //     0x150437c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1504380: ldr             lr, [lr, #0xb78]
    // 0x1504384: stp             lr, x16, [SP, #0x18]
    // 0x1504388: r16 = Instance_BoxFit
    //     0x1504388: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x150438c: ldr             x16, [x16, #0x118]
    // 0x1504390: ldur            lr, [fp, #-0x28]
    // 0x1504394: stp             lr, x16, [SP, #8]
    // 0x1504398: ldur            x16, [fp, #-0x30]
    // 0x150439c: str             x16, [SP]
    // 0x15043a0: mov             x1, x0
    // 0x15043a4: ldur            x2, [fp, #-8]
    // 0x15043a8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0x15043a8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0x15043ac: ldr             x4, [x4, #0x710]
    // 0x15043b0: r0 = CachedNetworkImage()
    //     0x15043b0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15043b4: r0 = Padding()
    //     0x15043b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15043b8: mov             x2, x0
    // 0x15043bc: r0 = Instance_EdgeInsets
    //     0x15043bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x15043c0: ldr             x0, [x0, #0x980]
    // 0x15043c4: stur            x2, [fp, #-0x28]
    // 0x15043c8: StoreField: r2->field_f = r0
    //     0x15043c8: stur            w0, [x2, #0xf]
    // 0x15043cc: ldur            x1, [fp, #-0x38]
    // 0x15043d0: StoreField: r2->field_b = r1
    //     0x15043d0: stur            w1, [x2, #0xb]
    // 0x15043d4: ldur            x3, [fp, #-0x18]
    // 0x15043d8: LoadField: r1 = r3->field_f
    //     0x15043d8: ldur            w1, [x3, #0xf]
    // 0x15043dc: DecompressPointer r1
    //     0x15043dc: add             x1, x1, HEAP, lsl #32
    // 0x15043e0: cmp             w1, NULL
    // 0x15043e4: b.ne            #0x15043f0
    // 0x15043e8: r4 = ""
    //     0x15043e8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15043ec: b               #0x15043f4
    // 0x15043f0: mov             x4, x1
    // 0x15043f4: ldur            x1, [fp, #-0x20]
    // 0x15043f8: stur            x4, [fp, #-8]
    // 0x15043fc: r0 = of()
    //     0x15043fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1504400: LoadField: r1 = r0->field_87
    //     0x1504400: ldur            w1, [x0, #0x87]
    // 0x1504404: DecompressPointer r1
    //     0x1504404: add             x1, x1, HEAP, lsl #32
    // 0x1504408: LoadField: r0 = r1->field_2b
    //     0x1504408: ldur            w0, [x1, #0x2b]
    // 0x150440c: DecompressPointer r0
    //     0x150440c: add             x0, x0, HEAP, lsl #32
    // 0x1504410: stur            x0, [fp, #-0x30]
    // 0x1504414: r1 = Instance_Color
    //     0x1504414: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1504418: d0 = 0.700000
    //     0x1504418: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x150441c: ldr             d0, [x17, #0xf48]
    // 0x1504420: r0 = withOpacity()
    //     0x1504420: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1504424: r16 = 12.000000
    //     0x1504424: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1504428: ldr             x16, [x16, #0x9e8]
    // 0x150442c: stp             x0, x16, [SP]
    // 0x1504430: ldur            x1, [fp, #-0x30]
    // 0x1504434: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1504434: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1504438: ldr             x4, [x4, #0xaa0]
    // 0x150443c: r0 = copyWith()
    //     0x150443c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1504440: stur            x0, [fp, #-0x30]
    // 0x1504444: r0 = Text()
    //     0x1504444: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1504448: mov             x3, x0
    // 0x150444c: ldur            x0, [fp, #-8]
    // 0x1504450: stur            x3, [fp, #-0x38]
    // 0x1504454: StoreField: r3->field_b = r0
    //     0x1504454: stur            w0, [x3, #0xb]
    // 0x1504458: ldur            x0, [fp, #-0x30]
    // 0x150445c: StoreField: r3->field_13 = r0
    //     0x150445c: stur            w0, [x3, #0x13]
    // 0x1504460: r0 = Instance_TextOverflow
    //     0x1504460: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x1504464: ldr             x0, [x0, #0xe10]
    // 0x1504468: StoreField: r3->field_2b = r0
    //     0x1504468: stur            w0, [x3, #0x2b]
    // 0x150446c: r0 = 2
    //     0x150446c: movz            x0, #0x2
    // 0x1504470: StoreField: r3->field_37 = r0
    //     0x1504470: stur            w0, [x3, #0x37]
    // 0x1504474: r1 = Null
    //     0x1504474: mov             x1, NULL
    // 0x1504478: r2 = 8
    //     0x1504478: movz            x2, #0x8
    // 0x150447c: r0 = AllocateArray()
    //     0x150447c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1504480: mov             x1, x0
    // 0x1504484: stur            x1, [fp, #-8]
    // 0x1504488: r16 = "Size: "
    //     0x1504488: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x150448c: ldr             x16, [x16, #0xf00]
    // 0x1504490: StoreField: r1->field_f = r16
    //     0x1504490: stur            w16, [x1, #0xf]
    // 0x1504494: ldur            x2, [fp, #-0x18]
    // 0x1504498: LoadField: r0 = r2->field_2f
    //     0x1504498: ldur            w0, [x2, #0x2f]
    // 0x150449c: DecompressPointer r0
    //     0x150449c: add             x0, x0, HEAP, lsl #32
    // 0x15044a0: cmp             w0, NULL
    // 0x15044a4: b.ne            #0x15044ac
    // 0x15044a8: r0 = ""
    //     0x15044a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15044ac: StoreField: r1->field_13 = r0
    //     0x15044ac: stur            w0, [x1, #0x13]
    // 0x15044b0: r16 = " / Qty: "
    //     0x15044b0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x15044b4: ldr             x16, [x16, #0x760]
    // 0x15044b8: ArrayStore: r1[0] = r16  ; List_4
    //     0x15044b8: stur            w16, [x1, #0x17]
    // 0x15044bc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x15044bc: ldur            w0, [x2, #0x17]
    // 0x15044c0: DecompressPointer r0
    //     0x15044c0: add             x0, x0, HEAP, lsl #32
    // 0x15044c4: r3 = 60
    //     0x15044c4: movz            x3, #0x3c
    // 0x15044c8: branchIfSmi(r0, 0x15044d4)
    //     0x15044c8: tbz             w0, #0, #0x15044d4
    // 0x15044cc: r3 = LoadClassIdInstr(r0)
    //     0x15044cc: ldur            x3, [x0, #-1]
    //     0x15044d0: ubfx            x3, x3, #0xc, #0x14
    // 0x15044d4: str             x0, [SP]
    // 0x15044d8: mov             x0, x3
    // 0x15044dc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x15044dc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x15044e0: r0 = GDT[cid_x0 + 0x2700]()
    //     0x15044e0: movz            x17, #0x2700
    //     0x15044e4: add             lr, x0, x17
    //     0x15044e8: ldr             lr, [x21, lr, lsl #3]
    //     0x15044ec: blr             lr
    // 0x15044f0: mov             x1, x0
    // 0x15044f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15044f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15044f8: r0 = tryParse()
    //     0x15044f8: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x15044fc: cmp             w0, NULL
    // 0x1504500: b.ne            #0x150450c
    // 0x1504504: r3 = 0
    //     0x1504504: movz            x3, #0
    // 0x1504508: b               #0x150451c
    // 0x150450c: r1 = LoadInt32Instr(r0)
    //     0x150450c: sbfx            x1, x0, #1, #0x1f
    //     0x1504510: tbz             w0, #0, #0x1504518
    //     0x1504514: ldur            x1, [x0, #7]
    // 0x1504518: mov             x3, x1
    // 0x150451c: ldur            x2, [fp, #-0x18]
    // 0x1504520: r0 = BoxInt64Instr(r3)
    //     0x1504520: sbfiz           x0, x3, #1, #0x1f
    //     0x1504524: cmp             x3, x0, asr #1
    //     0x1504528: b.eq            #0x1504534
    //     0x150452c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x1504530: stur            x3, [x0, #7]
    // 0x1504534: ldur            x1, [fp, #-8]
    // 0x1504538: ArrayStore: r1[3] = r0  ; List_4
    //     0x1504538: add             x25, x1, #0x1b
    //     0x150453c: str             w0, [x25]
    //     0x1504540: tbz             w0, #0, #0x150455c
    //     0x1504544: ldurb           w16, [x1, #-1]
    //     0x1504548: ldurb           w17, [x0, #-1]
    //     0x150454c: and             x16, x17, x16, lsr #2
    //     0x1504550: tst             x16, HEAP, lsr #32
    //     0x1504554: b.eq            #0x150455c
    //     0x1504558: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x150455c: ldur            x16, [fp, #-8]
    // 0x1504560: str             x16, [SP]
    // 0x1504564: r0 = _interpolate()
    //     0x1504564: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1504568: ldur            x1, [fp, #-0x20]
    // 0x150456c: stur            x0, [fp, #-8]
    // 0x1504570: r0 = of()
    //     0x1504570: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1504574: LoadField: r1 = r0->field_87
    //     0x1504574: ldur            w1, [x0, #0x87]
    // 0x1504578: DecompressPointer r1
    //     0x1504578: add             x1, x1, HEAP, lsl #32
    // 0x150457c: LoadField: r0 = r1->field_7
    //     0x150457c: ldur            w0, [x1, #7]
    // 0x1504580: DecompressPointer r0
    //     0x1504580: add             x0, x0, HEAP, lsl #32
    // 0x1504584: stur            x0, [fp, #-0x30]
    // 0x1504588: r1 = Instance_Color
    //     0x1504588: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x150458c: d0 = 0.700000
    //     0x150458c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1504590: ldr             d0, [x17, #0xf48]
    // 0x1504594: r0 = withOpacity()
    //     0x1504594: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1504598: r16 = 12.000000
    //     0x1504598: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x150459c: ldr             x16, [x16, #0x9e8]
    // 0x15045a0: stp             x0, x16, [SP]
    // 0x15045a4: ldur            x1, [fp, #-0x30]
    // 0x15045a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15045a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15045ac: ldr             x4, [x4, #0xaa0]
    // 0x15045b0: r0 = copyWith()
    //     0x15045b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15045b4: stur            x0, [fp, #-0x30]
    // 0x15045b8: r0 = Text()
    //     0x15045b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15045bc: mov             x2, x0
    // 0x15045c0: ldur            x0, [fp, #-8]
    // 0x15045c4: stur            x2, [fp, #-0x40]
    // 0x15045c8: StoreField: r2->field_b = r0
    //     0x15045c8: stur            w0, [x2, #0xb]
    // 0x15045cc: ldur            x0, [fp, #-0x30]
    // 0x15045d0: StoreField: r2->field_13 = r0
    //     0x15045d0: stur            w0, [x2, #0x13]
    // 0x15045d4: ldur            x0, [fp, #-0x18]
    // 0x15045d8: LoadField: r1 = r0->field_2b
    //     0x15045d8: ldur            w1, [x0, #0x2b]
    // 0x15045dc: DecompressPointer r1
    //     0x15045dc: add             x1, x1, HEAP, lsl #32
    // 0x15045e0: cmp             w1, NULL
    // 0x15045e4: b.ne            #0x15045f4
    // 0x15045e8: r5 = "₹0"
    //     0x15045e8: add             x5, PP, #0x34, lsl #12  ; [pp+0x34a10] "₹0"
    //     0x15045ec: ldr             x5, [x5, #0xa10]
    // 0x15045f0: b               #0x15045f8
    // 0x15045f4: mov             x5, x1
    // 0x15045f8: ldur            x4, [fp, #-0x28]
    // 0x15045fc: ldur            x3, [fp, #-0x38]
    // 0x1504600: ldur            x1, [fp, #-0x20]
    // 0x1504604: stur            x5, [fp, #-8]
    // 0x1504608: r0 = of()
    //     0x1504608: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150460c: LoadField: r1 = r0->field_87
    //     0x150460c: ldur            w1, [x0, #0x87]
    // 0x1504610: DecompressPointer r1
    //     0x1504610: add             x1, x1, HEAP, lsl #32
    // 0x1504614: LoadField: r0 = r1->field_2b
    //     0x1504614: ldur            w0, [x1, #0x2b]
    // 0x1504618: DecompressPointer r0
    //     0x1504618: add             x0, x0, HEAP, lsl #32
    // 0x150461c: stur            x0, [fp, #-0x20]
    // 0x1504620: r1 = Instance_Color
    //     0x1504620: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1504624: d0 = 0.700000
    //     0x1504624: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1504628: ldr             d0, [x17, #0xf48]
    // 0x150462c: r0 = withOpacity()
    //     0x150462c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1504630: r16 = 12.000000
    //     0x1504630: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1504634: ldr             x16, [x16, #0x9e8]
    // 0x1504638: stp             x0, x16, [SP]
    // 0x150463c: ldur            x1, [fp, #-0x20]
    // 0x1504640: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1504640: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1504644: ldr             x4, [x4, #0xaa0]
    // 0x1504648: r0 = copyWith()
    //     0x1504648: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150464c: stur            x0, [fp, #-0x20]
    // 0x1504650: r0 = Text()
    //     0x1504650: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1504654: mov             x3, x0
    // 0x1504658: ldur            x0, [fp, #-8]
    // 0x150465c: stur            x3, [fp, #-0x30]
    // 0x1504660: StoreField: r3->field_b = r0
    //     0x1504660: stur            w0, [x3, #0xb]
    // 0x1504664: ldur            x0, [fp, #-0x20]
    // 0x1504668: StoreField: r3->field_13 = r0
    //     0x1504668: stur            w0, [x3, #0x13]
    // 0x150466c: r1 = Null
    //     0x150466c: mov             x1, NULL
    // 0x1504670: r2 = 10
    //     0x1504670: movz            x2, #0xa
    // 0x1504674: r0 = AllocateArray()
    //     0x1504674: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1504678: mov             x2, x0
    // 0x150467c: ldur            x0, [fp, #-0x38]
    // 0x1504680: stur            x2, [fp, #-8]
    // 0x1504684: StoreField: r2->field_f = r0
    //     0x1504684: stur            w0, [x2, #0xf]
    // 0x1504688: r16 = Instance_SizedBox
    //     0x1504688: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x150468c: ldr             x16, [x16, #0xc70]
    // 0x1504690: StoreField: r2->field_13 = r16
    //     0x1504690: stur            w16, [x2, #0x13]
    // 0x1504694: ldur            x0, [fp, #-0x40]
    // 0x1504698: ArrayStore: r2[0] = r0  ; List_4
    //     0x1504698: stur            w0, [x2, #0x17]
    // 0x150469c: r16 = Instance_SizedBox
    //     0x150469c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x15046a0: ldr             x16, [x16, #0xc70]
    // 0x15046a4: StoreField: r2->field_1b = r16
    //     0x15046a4: stur            w16, [x2, #0x1b]
    // 0x15046a8: ldur            x0, [fp, #-0x30]
    // 0x15046ac: StoreField: r2->field_1f = r0
    //     0x15046ac: stur            w0, [x2, #0x1f]
    // 0x15046b0: r1 = <Widget>
    //     0x15046b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15046b4: r0 = AllocateGrowableArray()
    //     0x15046b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15046b8: mov             x1, x0
    // 0x15046bc: ldur            x0, [fp, #-8]
    // 0x15046c0: stur            x1, [fp, #-0x20]
    // 0x15046c4: StoreField: r1->field_f = r0
    //     0x15046c4: stur            w0, [x1, #0xf]
    // 0x15046c8: r0 = 10
    //     0x15046c8: movz            x0, #0xa
    // 0x15046cc: StoreField: r1->field_b = r0
    //     0x15046cc: stur            w0, [x1, #0xb]
    // 0x15046d0: r0 = Column()
    //     0x15046d0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x15046d4: mov             x1, x0
    // 0x15046d8: r0 = Instance_Axis
    //     0x15046d8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x15046dc: stur            x1, [fp, #-8]
    // 0x15046e0: StoreField: r1->field_f = r0
    //     0x15046e0: stur            w0, [x1, #0xf]
    // 0x15046e4: r2 = Instance_MainAxisAlignment
    //     0x15046e4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x15046e8: ldr             x2, [x2, #0xa8]
    // 0x15046ec: StoreField: r1->field_13 = r2
    //     0x15046ec: stur            w2, [x1, #0x13]
    // 0x15046f0: r2 = Instance_MainAxisSize
    //     0x15046f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15046f4: ldr             x2, [x2, #0xa10]
    // 0x15046f8: ArrayStore: r1[0] = r2  ; List_4
    //     0x15046f8: stur            w2, [x1, #0x17]
    // 0x15046fc: r3 = Instance_CrossAxisAlignment
    //     0x15046fc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1504700: ldr             x3, [x3, #0x890]
    // 0x1504704: StoreField: r1->field_1b = r3
    //     0x1504704: stur            w3, [x1, #0x1b]
    // 0x1504708: r4 = Instance_VerticalDirection
    //     0x1504708: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x150470c: ldr             x4, [x4, #0xa20]
    // 0x1504710: StoreField: r1->field_23 = r4
    //     0x1504710: stur            w4, [x1, #0x23]
    // 0x1504714: r5 = Instance_Clip
    //     0x1504714: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1504718: ldr             x5, [x5, #0x38]
    // 0x150471c: StoreField: r1->field_2b = r5
    //     0x150471c: stur            w5, [x1, #0x2b]
    // 0x1504720: StoreField: r1->field_2f = rZR
    //     0x1504720: stur            xzr, [x1, #0x2f]
    // 0x1504724: ldur            x6, [fp, #-0x20]
    // 0x1504728: StoreField: r1->field_b = r6
    //     0x1504728: stur            w6, [x1, #0xb]
    // 0x150472c: r0 = Padding()
    //     0x150472c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1504730: mov             x2, x0
    // 0x1504734: r0 = Instance_EdgeInsets
    //     0x1504734: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1504738: ldr             x0, [x0, #0x980]
    // 0x150473c: stur            x2, [fp, #-0x20]
    // 0x1504740: StoreField: r2->field_f = r0
    //     0x1504740: stur            w0, [x2, #0xf]
    // 0x1504744: ldur            x0, [fp, #-8]
    // 0x1504748: StoreField: r2->field_b = r0
    //     0x1504748: stur            w0, [x2, #0xb]
    // 0x150474c: r1 = <FlexParentData>
    //     0x150474c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1504750: ldr             x1, [x1, #0xe00]
    // 0x1504754: r0 = Expanded()
    //     0x1504754: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1504758: mov             x3, x0
    // 0x150475c: r0 = 1
    //     0x150475c: movz            x0, #0x1
    // 0x1504760: stur            x3, [fp, #-8]
    // 0x1504764: StoreField: r3->field_13 = r0
    //     0x1504764: stur            x0, [x3, #0x13]
    // 0x1504768: r0 = Instance_FlexFit
    //     0x1504768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x150476c: ldr             x0, [x0, #0xe08]
    // 0x1504770: StoreField: r3->field_1b = r0
    //     0x1504770: stur            w0, [x3, #0x1b]
    // 0x1504774: ldur            x0, [fp, #-0x20]
    // 0x1504778: StoreField: r3->field_b = r0
    //     0x1504778: stur            w0, [x3, #0xb]
    // 0x150477c: r1 = Null
    //     0x150477c: mov             x1, NULL
    // 0x1504780: r2 = 4
    //     0x1504780: movz            x2, #0x4
    // 0x1504784: r0 = AllocateArray()
    //     0x1504784: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1504788: mov             x2, x0
    // 0x150478c: ldur            x0, [fp, #-0x28]
    // 0x1504790: stur            x2, [fp, #-0x20]
    // 0x1504794: StoreField: r2->field_f = r0
    //     0x1504794: stur            w0, [x2, #0xf]
    // 0x1504798: ldur            x0, [fp, #-8]
    // 0x150479c: StoreField: r2->field_13 = r0
    //     0x150479c: stur            w0, [x2, #0x13]
    // 0x15047a0: r1 = <Widget>
    //     0x15047a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15047a4: r0 = AllocateGrowableArray()
    //     0x15047a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15047a8: mov             x1, x0
    // 0x15047ac: ldur            x0, [fp, #-0x20]
    // 0x15047b0: stur            x1, [fp, #-8]
    // 0x15047b4: StoreField: r1->field_f = r0
    //     0x15047b4: stur            w0, [x1, #0xf]
    // 0x15047b8: r2 = 4
    //     0x15047b8: movz            x2, #0x4
    // 0x15047bc: StoreField: r1->field_b = r2
    //     0x15047bc: stur            w2, [x1, #0xb]
    // 0x15047c0: r0 = Row()
    //     0x15047c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15047c4: mov             x2, x0
    // 0x15047c8: r0 = Instance_Axis
    //     0x15047c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15047cc: stur            x2, [fp, #-0x20]
    // 0x15047d0: StoreField: r2->field_f = r0
    //     0x15047d0: stur            w0, [x2, #0xf]
    // 0x15047d4: r0 = Instance_MainAxisAlignment
    //     0x15047d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15047d8: ldr             x0, [x0, #0xa08]
    // 0x15047dc: StoreField: r2->field_13 = r0
    //     0x15047dc: stur            w0, [x2, #0x13]
    // 0x15047e0: r3 = Instance_MainAxisSize
    //     0x15047e0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15047e4: ldr             x3, [x3, #0xa10]
    // 0x15047e8: ArrayStore: r2[0] = r3  ; List_4
    //     0x15047e8: stur            w3, [x2, #0x17]
    // 0x15047ec: r1 = Instance_CrossAxisAlignment
    //     0x15047ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x15047f0: ldr             x1, [x1, #0x890]
    // 0x15047f4: StoreField: r2->field_1b = r1
    //     0x15047f4: stur            w1, [x2, #0x1b]
    // 0x15047f8: r4 = Instance_VerticalDirection
    //     0x15047f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15047fc: ldr             x4, [x4, #0xa20]
    // 0x1504800: StoreField: r2->field_23 = r4
    //     0x1504800: stur            w4, [x2, #0x23]
    // 0x1504804: r5 = Instance_Clip
    //     0x1504804: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1504808: ldr             x5, [x5, #0x38]
    // 0x150480c: StoreField: r2->field_2b = r5
    //     0x150480c: stur            w5, [x2, #0x2b]
    // 0x1504810: StoreField: r2->field_2f = rZR
    //     0x1504810: stur            xzr, [x2, #0x2f]
    // 0x1504814: ldur            x1, [fp, #-8]
    // 0x1504818: StoreField: r2->field_b = r1
    //     0x1504818: stur            w1, [x2, #0xb]
    // 0x150481c: ldur            x6, [fp, #-0x18]
    // 0x1504820: LoadField: r1 = r6->field_3f
    //     0x1504820: ldur            w1, [x6, #0x3f]
    // 0x1504824: DecompressPointer r1
    //     0x1504824: add             x1, x1, HEAP, lsl #32
    // 0x1504828: cmp             w1, NULL
    // 0x150482c: b.ne            #0x1504838
    // 0x1504830: r1 = Null
    //     0x1504830: mov             x1, NULL
    // 0x1504834: b               #0x150484c
    // 0x1504838: LoadField: r7 = r1->field_b
    //     0x1504838: ldur            w7, [x1, #0xb]
    // 0x150483c: cbnz            w7, #0x1504848
    // 0x1504840: r1 = false
    //     0x1504840: add             x1, NULL, #0x30  ; false
    // 0x1504844: b               #0x150484c
    // 0x1504848: r1 = true
    //     0x1504848: add             x1, NULL, #0x20  ; true
    // 0x150484c: cmp             w1, NULL
    // 0x1504850: b.eq            #0x1504864
    // 0x1504854: tbnz            w1, #4, #0x1504864
    // 0x1504858: mov             x0, x6
    // 0x150485c: r2 = true
    //     0x150485c: add             x2, NULL, #0x20  ; true
    // 0x1504860: b               #0x15048bc
    // 0x1504864: ldur            x1, [fp, #-0x10]
    // 0x1504868: r0 = controller()
    //     0x1504868: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150486c: LoadField: r1 = r0->field_6f
    //     0x150486c: ldur            w1, [x0, #0x6f]
    // 0x1504870: DecompressPointer r1
    //     0x1504870: add             x1, x1, HEAP, lsl #32
    // 0x1504874: r0 = value()
    //     0x1504874: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1504878: LoadField: r1 = r0->field_13
    //     0x1504878: ldur            w1, [x0, #0x13]
    // 0x150487c: DecompressPointer r1
    //     0x150487c: add             x1, x1, HEAP, lsl #32
    // 0x1504880: cmp             w1, NULL
    // 0x1504884: b.ne            #0x1504890
    // 0x1504888: r0 = Null
    //     0x1504888: mov             x0, NULL
    // 0x150488c: b               #0x15048a8
    // 0x1504890: LoadField: r0 = r1->field_b
    //     0x1504890: ldur            w0, [x1, #0xb]
    // 0x1504894: cbnz            w0, #0x15048a0
    // 0x1504898: r1 = false
    //     0x1504898: add             x1, NULL, #0x30  ; false
    // 0x150489c: b               #0x15048a4
    // 0x15048a0: r1 = true
    //     0x15048a0: add             x1, NULL, #0x20  ; true
    // 0x15048a4: mov             x0, x1
    // 0x15048a8: cmp             w0, NULL
    // 0x15048ac: b.ne            #0x15048b4
    // 0x15048b0: r0 = false
    //     0x15048b0: add             x0, NULL, #0x30  ; false
    // 0x15048b4: mov             x2, x0
    // 0x15048b8: ldur            x0, [fp, #-0x18]
    // 0x15048bc: stur            x2, [fp, #-0x28]
    // 0x15048c0: LoadField: r3 = r0->field_3f
    //     0x15048c0: ldur            w3, [x0, #0x3f]
    // 0x15048c4: DecompressPointer r3
    //     0x15048c4: add             x3, x3, HEAP, lsl #32
    // 0x15048c8: ldur            x1, [fp, #-0x10]
    // 0x15048cc: stur            x3, [fp, #-8]
    // 0x15048d0: r0 = controller()
    //     0x15048d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15048d4: LoadField: r1 = r0->field_6f
    //     0x15048d4: ldur            w1, [x0, #0x6f]
    // 0x15048d8: DecompressPointer r1
    //     0x15048d8: add             x1, x1, HEAP, lsl #32
    // 0x15048dc: r0 = value()
    //     0x15048dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15048e0: mov             x2, x0
    // 0x15048e4: ldur            x0, [fp, #-0x18]
    // 0x15048e8: stur            x2, [fp, #-0x30]
    // 0x15048ec: LoadField: r1 = r0->field_43
    //     0x15048ec: ldur            w1, [x0, #0x43]
    // 0x15048f0: DecompressPointer r1
    //     0x15048f0: add             x1, x1, HEAP, lsl #32
    // 0x15048f4: cbz             w1, #0x150490c
    // 0x15048f8: LoadField: r1 = r0->field_47
    //     0x15048f8: ldur            w1, [x0, #0x47]
    // 0x15048fc: DecompressPointer r1
    //     0x15048fc: add             x1, x1, HEAP, lsl #32
    // 0x1504900: mov             x4, x1
    // 0x1504904: mov             x0, x2
    // 0x1504908: b               #0x15049a8
    // 0x150490c: ldur            x1, [fp, #-0x10]
    // 0x1504910: r0 = controller()
    //     0x1504910: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504914: LoadField: r1 = r0->field_bf
    //     0x1504914: ldur            w1, [x0, #0xbf]
    // 0x1504918: DecompressPointer r1
    //     0x1504918: add             x1, x1, HEAP, lsl #32
    // 0x150491c: cmp             w1, NULL
    // 0x1504920: b.eq            #0x1504990
    // 0x1504924: ldur            x1, [fp, #-0x10]
    // 0x1504928: r0 = controller()
    //     0x1504928: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150492c: LoadField: r1 = r0->field_bf
    //     0x150492c: ldur            w1, [x0, #0xbf]
    // 0x1504930: DecompressPointer r1
    //     0x1504930: add             x1, x1, HEAP, lsl #32
    // 0x1504934: cmp             w1, NULL
    // 0x1504938: b.ne            #0x1504944
    // 0x150493c: r0 = Null
    //     0x150493c: mov             x0, NULL
    // 0x1504940: b               #0x150495c
    // 0x1504944: LoadField: r0 = r1->field_7
    //     0x1504944: ldur            w0, [x1, #7]
    // 0x1504948: cbnz            w0, #0x1504954
    // 0x150494c: r1 = false
    //     0x150494c: add             x1, NULL, #0x30  ; false
    // 0x1504950: b               #0x1504958
    // 0x1504954: r1 = true
    //     0x1504954: add             x1, NULL, #0x20  ; true
    // 0x1504958: mov             x0, x1
    // 0x150495c: cmp             w0, NULL
    // 0x1504960: b.eq            #0x1504990
    // 0x1504964: tbnz            w0, #4, #0x1504990
    // 0x1504968: ldur            x1, [fp, #-0x10]
    // 0x150496c: r0 = controller()
    //     0x150496c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504970: LoadField: r1 = r0->field_bf
    //     0x1504970: ldur            w1, [x0, #0xbf]
    // 0x1504974: DecompressPointer r1
    //     0x1504974: add             x1, x1, HEAP, lsl #32
    // 0x1504978: cmp             w1, NULL
    // 0x150497c: b.ne            #0x1504988
    // 0x1504980: r0 = ""
    //     0x1504980: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1504984: b               #0x15049a0
    // 0x1504988: mov             x0, x1
    // 0x150498c: b               #0x15049a0
    // 0x1504990: ldur            x0, [fp, #-0x18]
    // 0x1504994: LoadField: r1 = r0->field_47
    //     0x1504994: ldur            w1, [x0, #0x47]
    // 0x1504998: DecompressPointer r1
    //     0x1504998: add             x1, x1, HEAP, lsl #32
    // 0x150499c: mov             x0, x1
    // 0x15049a0: mov             x4, x0
    // 0x15049a4: ldur            x0, [fp, #-0x30]
    // 0x15049a8: ldur            x3, [fp, #-0x20]
    // 0x15049ac: ldur            x1, [fp, #-0x28]
    // 0x15049b0: ldur            x2, [fp, #-8]
    // 0x15049b4: stur            x4, [fp, #-0x10]
    // 0x15049b8: r0 = CustomisedStrip()
    //     0x15049b8: bl              #0x9d72b8  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0x15049bc: mov             x1, x0
    // 0x15049c0: ldur            x0, [fp, #-8]
    // 0x15049c4: stur            x1, [fp, #-0x18]
    // 0x15049c8: StoreField: r1->field_b = r0
    //     0x15049c8: stur            w0, [x1, #0xb]
    // 0x15049cc: ldur            x0, [fp, #-0x30]
    // 0x15049d0: StoreField: r1->field_f = r0
    //     0x15049d0: stur            w0, [x1, #0xf]
    // 0x15049d4: ldur            x0, [fp, #-0x10]
    // 0x15049d8: StoreField: r1->field_13 = r0
    //     0x15049d8: stur            w0, [x1, #0x13]
    // 0x15049dc: r0 = Visibility()
    //     0x15049dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15049e0: mov             x3, x0
    // 0x15049e4: ldur            x0, [fp, #-0x18]
    // 0x15049e8: stur            x3, [fp, #-8]
    // 0x15049ec: StoreField: r3->field_b = r0
    //     0x15049ec: stur            w0, [x3, #0xb]
    // 0x15049f0: r0 = Instance_SizedBox
    //     0x15049f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15049f4: StoreField: r3->field_f = r0
    //     0x15049f4: stur            w0, [x3, #0xf]
    // 0x15049f8: ldur            x0, [fp, #-0x28]
    // 0x15049fc: StoreField: r3->field_13 = r0
    //     0x15049fc: stur            w0, [x3, #0x13]
    // 0x1504a00: r0 = false
    //     0x1504a00: add             x0, NULL, #0x30  ; false
    // 0x1504a04: ArrayStore: r3[0] = r0  ; List_4
    //     0x1504a04: stur            w0, [x3, #0x17]
    // 0x1504a08: StoreField: r3->field_1b = r0
    //     0x1504a08: stur            w0, [x3, #0x1b]
    // 0x1504a0c: StoreField: r3->field_1f = r0
    //     0x1504a0c: stur            w0, [x3, #0x1f]
    // 0x1504a10: StoreField: r3->field_23 = r0
    //     0x1504a10: stur            w0, [x3, #0x23]
    // 0x1504a14: StoreField: r3->field_27 = r0
    //     0x1504a14: stur            w0, [x3, #0x27]
    // 0x1504a18: StoreField: r3->field_2b = r0
    //     0x1504a18: stur            w0, [x3, #0x2b]
    // 0x1504a1c: r1 = Null
    //     0x1504a1c: mov             x1, NULL
    // 0x1504a20: r2 = 4
    //     0x1504a20: movz            x2, #0x4
    // 0x1504a24: r0 = AllocateArray()
    //     0x1504a24: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1504a28: mov             x2, x0
    // 0x1504a2c: ldur            x0, [fp, #-0x20]
    // 0x1504a30: stur            x2, [fp, #-0x10]
    // 0x1504a34: StoreField: r2->field_f = r0
    //     0x1504a34: stur            w0, [x2, #0xf]
    // 0x1504a38: ldur            x0, [fp, #-8]
    // 0x1504a3c: StoreField: r2->field_13 = r0
    //     0x1504a3c: stur            w0, [x2, #0x13]
    // 0x1504a40: r1 = <Widget>
    //     0x1504a40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1504a44: r0 = AllocateGrowableArray()
    //     0x1504a44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1504a48: mov             x1, x0
    // 0x1504a4c: ldur            x0, [fp, #-0x10]
    // 0x1504a50: stur            x1, [fp, #-8]
    // 0x1504a54: StoreField: r1->field_f = r0
    //     0x1504a54: stur            w0, [x1, #0xf]
    // 0x1504a58: r0 = 4
    //     0x1504a58: movz            x0, #0x4
    // 0x1504a5c: StoreField: r1->field_b = r0
    //     0x1504a5c: stur            w0, [x1, #0xb]
    // 0x1504a60: r0 = Column()
    //     0x1504a60: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1504a64: r1 = Instance_Axis
    //     0x1504a64: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1504a68: StoreField: r0->field_f = r1
    //     0x1504a68: stur            w1, [x0, #0xf]
    // 0x1504a6c: r1 = Instance_MainAxisAlignment
    //     0x1504a6c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1504a70: ldr             x1, [x1, #0xa08]
    // 0x1504a74: StoreField: r0->field_13 = r1
    //     0x1504a74: stur            w1, [x0, #0x13]
    // 0x1504a78: r1 = Instance_MainAxisSize
    //     0x1504a78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1504a7c: ldr             x1, [x1, #0xa10]
    // 0x1504a80: ArrayStore: r0[0] = r1  ; List_4
    //     0x1504a80: stur            w1, [x0, #0x17]
    // 0x1504a84: r1 = Instance_CrossAxisAlignment
    //     0x1504a84: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1504a88: ldr             x1, [x1, #0xa18]
    // 0x1504a8c: StoreField: r0->field_1b = r1
    //     0x1504a8c: stur            w1, [x0, #0x1b]
    // 0x1504a90: r1 = Instance_VerticalDirection
    //     0x1504a90: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1504a94: ldr             x1, [x1, #0xa20]
    // 0x1504a98: StoreField: r0->field_23 = r1
    //     0x1504a98: stur            w1, [x0, #0x23]
    // 0x1504a9c: r1 = Instance_Clip
    //     0x1504a9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1504aa0: ldr             x1, [x1, #0x38]
    // 0x1504aa4: StoreField: r0->field_2b = r1
    //     0x1504aa4: stur            w1, [x0, #0x2b]
    // 0x1504aa8: StoreField: r0->field_2f = rZR
    //     0x1504aa8: stur            xzr, [x0, #0x2f]
    // 0x1504aac: ldur            x1, [fp, #-8]
    // 0x1504ab0: StoreField: r0->field_b = r1
    //     0x1504ab0: stur            w1, [x0, #0xb]
    // 0x1504ab4: LeaveFrame
    //     0x1504ab4: mov             SP, fp
    //     0x1504ab8: ldp             fp, lr, [SP], #0x10
    // 0x1504abc: ret
    //     0x1504abc: ret             
    // 0x1504ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1504ac0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1504ac4: b               #0x1504324
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x1504ac8, size: 0x64
    // 0x1504ac8: EnterFrame
    //     0x1504ac8: stp             fp, lr, [SP, #-0x10]!
    //     0x1504acc: mov             fp, SP
    // 0x1504ad0: AllocStack(0x8)
    //     0x1504ad0: sub             SP, SP, #8
    // 0x1504ad4: SetupParameters()
    //     0x1504ad4: ldr             x0, [fp, #0x10]
    //     0x1504ad8: ldur            w1, [x0, #0x17]
    //     0x1504adc: add             x1, x1, HEAP, lsl #32
    // 0x1504ae0: CheckStackOverflow
    //     0x1504ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1504ae4: cmp             SP, x16
    //     0x1504ae8: b.ls            #0x1504b24
    // 0x1504aec: LoadField: r0 = r1->field_f
    //     0x1504aec: ldur            w0, [x1, #0xf]
    // 0x1504af0: DecompressPointer r0
    //     0x1504af0: add             x0, x0, HEAP, lsl #32
    // 0x1504af4: mov             x1, x0
    // 0x1504af8: r0 = controller()
    //     0x1504af8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1504afc: mov             x1, x0
    // 0x1504b00: r0 = freeGiftDetailResponse()
    //     0x1504b00: bl              #0x12d6818  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::freeGiftDetailResponse
    // 0x1504b04: stur            x0, [fp, #-8]
    // 0x1504b08: r0 = CheckoutBreadCrumb()
    //     0x1504b08: bl              #0x1391e94  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x1504b0c: ldur            x1, [fp, #-8]
    // 0x1504b10: StoreField: r0->field_b = r1
    //     0x1504b10: stur            w1, [x0, #0xb]
    // 0x1504b14: StoreField: r0->field_f = rZR
    //     0x1504b14: stur            xzr, [x0, #0xf]
    // 0x1504b18: LeaveFrame
    //     0x1504b18: mov             SP, fp
    //     0x1504b1c: ldp             fp, lr, [SP], #0x10
    // 0x1504b20: ret
    //     0x1504b20: ret             
    // 0x1504b24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1504b24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1504b28: b               #0x1504aec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15ce36c, size: 0xec
    // 0x15ce36c: EnterFrame
    //     0x15ce36c: stp             fp, lr, [SP, #-0x10]!
    //     0x15ce370: mov             fp, SP
    // 0x15ce374: AllocStack(0x18)
    //     0x15ce374: sub             SP, SP, #0x18
    // 0x15ce378: SetupParameters()
    //     0x15ce378: ldr             x0, [fp, #0x10]
    //     0x15ce37c: ldur            w3, [x0, #0x17]
    //     0x15ce380: add             x3, x3, HEAP, lsl #32
    //     0x15ce384: stur            x3, [fp, #-8]
    // 0x15ce388: CheckStackOverflow
    //     0x15ce388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ce38c: cmp             SP, x16
    //     0x15ce390: b.ls            #0x15ce450
    // 0x15ce394: LoadField: r1 = r3->field_f
    //     0x15ce394: ldur            w1, [x3, #0xf]
    // 0x15ce398: DecompressPointer r1
    //     0x15ce398: add             x1, x1, HEAP, lsl #32
    // 0x15ce39c: r2 = false
    //     0x15ce39c: add             x2, NULL, #0x30  ; false
    // 0x15ce3a0: r0 = showLoading()
    //     0x15ce3a0: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15ce3a4: ldur            x0, [fp, #-8]
    // 0x15ce3a8: LoadField: r1 = r0->field_f
    //     0x15ce3a8: ldur            w1, [x0, #0xf]
    // 0x15ce3ac: DecompressPointer r1
    //     0x15ce3ac: add             x1, x1, HEAP, lsl #32
    // 0x15ce3b0: r0 = controller()
    //     0x15ce3b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ce3b4: LoadField: r1 = r0->field_5b
    //     0x15ce3b4: ldur            w1, [x0, #0x5b]
    // 0x15ce3b8: DecompressPointer r1
    //     0x15ce3b8: add             x1, x1, HEAP, lsl #32
    // 0x15ce3bc: r0 = value()
    //     0x15ce3bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ce3c0: tbnz            w0, #4, #0x15ce3f8
    // 0x15ce3c4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15ce3c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15ce3c8: ldr             x0, [x0, #0x1c80]
    //     0x15ce3cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15ce3d0: cmp             w0, w16
    //     0x15ce3d4: b.ne            #0x15ce3e0
    //     0x15ce3d8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15ce3dc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15ce3e0: r16 = "/search"
    //     0x15ce3e0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15ce3e4: ldr             x16, [x16, #0x838]
    // 0x15ce3e8: stp             x16, NULL, [SP]
    // 0x15ce3ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15ce3ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15ce3f0: r0 = GetNavigation.toNamed()
    //     0x15ce3f0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15ce3f4: b               #0x15ce440
    // 0x15ce3f8: ldur            x0, [fp, #-8]
    // 0x15ce3fc: LoadField: r1 = r0->field_f
    //     0x15ce3fc: ldur            w1, [x0, #0xf]
    // 0x15ce400: DecompressPointer r1
    //     0x15ce400: add             x1, x1, HEAP, lsl #32
    // 0x15ce404: r0 = controller()
    //     0x15ce404: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ce408: LoadField: r1 = r0->field_7f
    //     0x15ce408: ldur            w1, [x0, #0x7f]
    // 0x15ce40c: DecompressPointer r1
    //     0x15ce40c: add             x1, x1, HEAP, lsl #32
    // 0x15ce410: r2 = ""
    //     0x15ce410: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15ce414: r0 = value=()
    //     0x15ce414: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15ce418: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15ce418: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15ce41c: ldr             x0, [x0, #0x1c80]
    //     0x15ce420: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15ce424: cmp             w0, w16
    //     0x15ce428: b.ne            #0x15ce434
    //     0x15ce42c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15ce430: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15ce434: str             NULL, [SP]
    // 0x15ce438: r4 = const [0x1, 0, 0, 0, null]
    //     0x15ce438: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15ce43c: r0 = GetNavigation.back()
    //     0x15ce43c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15ce440: r0 = Null
    //     0x15ce440: mov             x0, NULL
    // 0x15ce444: LeaveFrame
    //     0x15ce444: mov             SP, fp
    //     0x15ce448: ldp             fp, lr, [SP], #0x10
    // 0x15ce44c: ret
    //     0x15ce44c: ret             
    // 0x15ce450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ce450: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ce454: b               #0x15ce394
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e90ec, size: 0x24c
    // 0x15e90ec: EnterFrame
    //     0x15e90ec: stp             fp, lr, [SP, #-0x10]!
    //     0x15e90f0: mov             fp, SP
    // 0x15e90f4: AllocStack(0x28)
    //     0x15e90f4: sub             SP, SP, #0x28
    // 0x15e90f8: SetupParameters(PaymentMethodsCheckoutWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e90f8: stur            x1, [fp, #-8]
    //     0x15e90fc: stur            x2, [fp, #-0x10]
    // 0x15e9100: CheckStackOverflow
    //     0x15e9100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e9104: cmp             SP, x16
    //     0x15e9108: b.ls            #0x15e9330
    // 0x15e910c: r1 = 2
    //     0x15e910c: movz            x1, #0x2
    // 0x15e9110: r0 = AllocateContext()
    //     0x15e9110: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e9114: ldur            x1, [fp, #-8]
    // 0x15e9118: stur            x0, [fp, #-0x18]
    // 0x15e911c: StoreField: r0->field_f = r1
    //     0x15e911c: stur            w1, [x0, #0xf]
    // 0x15e9120: ldur            x2, [fp, #-0x10]
    // 0x15e9124: StoreField: r0->field_13 = r2
    //     0x15e9124: stur            w2, [x0, #0x13]
    // 0x15e9128: r0 = Obx()
    //     0x15e9128: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e912c: ldur            x2, [fp, #-0x18]
    // 0x15e9130: r1 = Function '<anonymous closure>':.
    //     0x15e9130: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c718] AnonymousClosure: (0x15e9338), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::appBar (0x15e90ec)
    //     0x15e9134: ldr             x1, [x1, #0x718]
    // 0x15e9138: stur            x0, [fp, #-0x10]
    // 0x15e913c: r0 = AllocateClosure()
    //     0x15e913c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e9140: mov             x1, x0
    // 0x15e9144: ldur            x0, [fp, #-0x10]
    // 0x15e9148: StoreField: r0->field_b = r1
    //     0x15e9148: stur            w1, [x0, #0xb]
    // 0x15e914c: ldur            x1, [fp, #-8]
    // 0x15e9150: r0 = controller()
    //     0x15e9150: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e9154: mov             x1, x0
    // 0x15e9158: r0 = appConfigResponse()
    //     0x15e9158: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0x15e915c: tbnz            w0, #4, #0x15e91f4
    // 0x15e9160: ldur            x2, [fp, #-0x18]
    // 0x15e9164: LoadField: r1 = r2->field_13
    //     0x15e9164: ldur            w1, [x2, #0x13]
    // 0x15e9168: DecompressPointer r1
    //     0x15e9168: add             x1, x1, HEAP, lsl #32
    // 0x15e916c: r0 = of()
    //     0x15e916c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e9170: LoadField: r1 = r0->field_5b
    //     0x15e9170: ldur            w1, [x0, #0x5b]
    // 0x15e9174: DecompressPointer r1
    //     0x15e9174: add             x1, x1, HEAP, lsl #32
    // 0x15e9178: stur            x1, [fp, #-8]
    // 0x15e917c: r0 = ColorFilter()
    //     0x15e917c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e9180: mov             x1, x0
    // 0x15e9184: ldur            x0, [fp, #-8]
    // 0x15e9188: stur            x1, [fp, #-0x20]
    // 0x15e918c: StoreField: r1->field_7 = r0
    //     0x15e918c: stur            w0, [x1, #7]
    // 0x15e9190: r0 = Instance_BlendMode
    //     0x15e9190: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9194: ldr             x0, [x0, #0xb30]
    // 0x15e9198: StoreField: r1->field_b = r0
    //     0x15e9198: stur            w0, [x1, #0xb]
    // 0x15e919c: r2 = 1
    //     0x15e919c: movz            x2, #0x1
    // 0x15e91a0: StoreField: r1->field_13 = r2
    //     0x15e91a0: stur            x2, [x1, #0x13]
    // 0x15e91a4: r0 = SvgPicture()
    //     0x15e91a4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e91a8: stur            x0, [fp, #-8]
    // 0x15e91ac: ldur            x16, [fp, #-0x20]
    // 0x15e91b0: str             x16, [SP]
    // 0x15e91b4: mov             x1, x0
    // 0x15e91b8: r2 = "assets/images/search.svg"
    //     0x15e91b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e91bc: ldr             x2, [x2, #0xa30]
    // 0x15e91c0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e91c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e91c4: ldr             x4, [x4, #0xa38]
    // 0x15e91c8: r0 = SvgPicture.asset()
    //     0x15e91c8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e91cc: r0 = Align()
    //     0x15e91cc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e91d0: r3 = Instance_Alignment
    //     0x15e91d0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e91d4: ldr             x3, [x3, #0xb10]
    // 0x15e91d8: StoreField: r0->field_f = r3
    //     0x15e91d8: stur            w3, [x0, #0xf]
    // 0x15e91dc: r4 = 1.000000
    //     0x15e91dc: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e91e0: StoreField: r0->field_13 = r4
    //     0x15e91e0: stur            w4, [x0, #0x13]
    // 0x15e91e4: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e91e4: stur            w4, [x0, #0x17]
    // 0x15e91e8: ldur            x1, [fp, #-8]
    // 0x15e91ec: StoreField: r0->field_b = r1
    //     0x15e91ec: stur            w1, [x0, #0xb]
    // 0x15e91f0: b               #0x15e92a4
    // 0x15e91f4: ldur            x5, [fp, #-0x18]
    // 0x15e91f8: r4 = 1.000000
    //     0x15e91f8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e91fc: r0 = Instance_BlendMode
    //     0x15e91fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9200: ldr             x0, [x0, #0xb30]
    // 0x15e9204: r3 = Instance_Alignment
    //     0x15e9204: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e9208: ldr             x3, [x3, #0xb10]
    // 0x15e920c: r2 = 1
    //     0x15e920c: movz            x2, #0x1
    // 0x15e9210: LoadField: r1 = r5->field_13
    //     0x15e9210: ldur            w1, [x5, #0x13]
    // 0x15e9214: DecompressPointer r1
    //     0x15e9214: add             x1, x1, HEAP, lsl #32
    // 0x15e9218: r0 = of()
    //     0x15e9218: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e921c: LoadField: r1 = r0->field_5b
    //     0x15e921c: ldur            w1, [x0, #0x5b]
    // 0x15e9220: DecompressPointer r1
    //     0x15e9220: add             x1, x1, HEAP, lsl #32
    // 0x15e9224: stur            x1, [fp, #-8]
    // 0x15e9228: r0 = ColorFilter()
    //     0x15e9228: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e922c: mov             x1, x0
    // 0x15e9230: ldur            x0, [fp, #-8]
    // 0x15e9234: stur            x1, [fp, #-0x20]
    // 0x15e9238: StoreField: r1->field_7 = r0
    //     0x15e9238: stur            w0, [x1, #7]
    // 0x15e923c: r0 = Instance_BlendMode
    //     0x15e923c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9240: ldr             x0, [x0, #0xb30]
    // 0x15e9244: StoreField: r1->field_b = r0
    //     0x15e9244: stur            w0, [x1, #0xb]
    // 0x15e9248: r0 = 1
    //     0x15e9248: movz            x0, #0x1
    // 0x15e924c: StoreField: r1->field_13 = r0
    //     0x15e924c: stur            x0, [x1, #0x13]
    // 0x15e9250: r0 = SvgPicture()
    //     0x15e9250: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e9254: stur            x0, [fp, #-8]
    // 0x15e9258: ldur            x16, [fp, #-0x20]
    // 0x15e925c: str             x16, [SP]
    // 0x15e9260: mov             x1, x0
    // 0x15e9264: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e9264: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e9268: ldr             x2, [x2, #0xa40]
    // 0x15e926c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e926c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e9270: ldr             x4, [x4, #0xa38]
    // 0x15e9274: r0 = SvgPicture.asset()
    //     0x15e9274: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e9278: r0 = Align()
    //     0x15e9278: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e927c: mov             x1, x0
    // 0x15e9280: r0 = Instance_Alignment
    //     0x15e9280: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e9284: ldr             x0, [x0, #0xb10]
    // 0x15e9288: StoreField: r1->field_f = r0
    //     0x15e9288: stur            w0, [x1, #0xf]
    // 0x15e928c: r0 = 1.000000
    //     0x15e928c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9290: StoreField: r1->field_13 = r0
    //     0x15e9290: stur            w0, [x1, #0x13]
    // 0x15e9294: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e9294: stur            w0, [x1, #0x17]
    // 0x15e9298: ldur            x0, [fp, #-8]
    // 0x15e929c: StoreField: r1->field_b = r0
    //     0x15e929c: stur            w0, [x1, #0xb]
    // 0x15e92a0: mov             x0, x1
    // 0x15e92a4: stur            x0, [fp, #-8]
    // 0x15e92a8: r0 = InkWell()
    //     0x15e92a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e92ac: mov             x3, x0
    // 0x15e92b0: ldur            x0, [fp, #-8]
    // 0x15e92b4: stur            x3, [fp, #-0x20]
    // 0x15e92b8: StoreField: r3->field_b = r0
    //     0x15e92b8: stur            w0, [x3, #0xb]
    // 0x15e92bc: ldur            x2, [fp, #-0x18]
    // 0x15e92c0: r1 = Function '<anonymous closure>':.
    //     0x15e92c0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c720] AnonymousClosure: (0x15ce36c), in [package:customer_app/app/presentation/views/line/checkout_variants/payment_methods_checkout_widget.dart] PaymentMethodsCheckoutWidget::appBar (0x15e90ec)
    //     0x15e92c4: ldr             x1, [x1, #0x720]
    // 0x15e92c8: r0 = AllocateClosure()
    //     0x15e92c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e92cc: ldur            x2, [fp, #-0x20]
    // 0x15e92d0: StoreField: r2->field_f = r0
    //     0x15e92d0: stur            w0, [x2, #0xf]
    // 0x15e92d4: r0 = true
    //     0x15e92d4: add             x0, NULL, #0x20  ; true
    // 0x15e92d8: StoreField: r2->field_43 = r0
    //     0x15e92d8: stur            w0, [x2, #0x43]
    // 0x15e92dc: r1 = Instance_BoxShape
    //     0x15e92dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e92e0: ldr             x1, [x1, #0x80]
    // 0x15e92e4: StoreField: r2->field_47 = r1
    //     0x15e92e4: stur            w1, [x2, #0x47]
    // 0x15e92e8: StoreField: r2->field_6f = r0
    //     0x15e92e8: stur            w0, [x2, #0x6f]
    // 0x15e92ec: r1 = false
    //     0x15e92ec: add             x1, NULL, #0x30  ; false
    // 0x15e92f0: StoreField: r2->field_73 = r1
    //     0x15e92f0: stur            w1, [x2, #0x73]
    // 0x15e92f4: StoreField: r2->field_83 = r0
    //     0x15e92f4: stur            w0, [x2, #0x83]
    // 0x15e92f8: StoreField: r2->field_7b = r1
    //     0x15e92f8: stur            w1, [x2, #0x7b]
    // 0x15e92fc: r0 = AppBar()
    //     0x15e92fc: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e9300: stur            x0, [fp, #-8]
    // 0x15e9304: ldur            x16, [fp, #-0x10]
    // 0x15e9308: str             x16, [SP]
    // 0x15e930c: mov             x1, x0
    // 0x15e9310: ldur            x2, [fp, #-0x20]
    // 0x15e9314: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e9314: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e9318: ldr             x4, [x4, #0xf00]
    // 0x15e931c: r0 = AppBar()
    //     0x15e931c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e9320: ldur            x0, [fp, #-8]
    // 0x15e9324: LeaveFrame
    //     0x15e9324: mov             SP, fp
    //     0x15e9328: ldp             fp, lr, [SP], #0x10
    // 0x15e932c: ret
    //     0x15e932c: ret             
    // 0x15e9330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e9330: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e9334: b               #0x15e910c
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15e9338, size: 0x42c
    // 0x15e9338: EnterFrame
    //     0x15e9338: stp             fp, lr, [SP, #-0x10]!
    //     0x15e933c: mov             fp, SP
    // 0x15e9340: AllocStack(0x48)
    //     0x15e9340: sub             SP, SP, #0x48
    // 0x15e9344: SetupParameters()
    //     0x15e9344: ldr             x0, [fp, #0x10]
    //     0x15e9348: ldur            w2, [x0, #0x17]
    //     0x15e934c: add             x2, x2, HEAP, lsl #32
    //     0x15e9350: stur            x2, [fp, #-8]
    // 0x15e9354: CheckStackOverflow
    //     0x15e9354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e9358: cmp             SP, x16
    //     0x15e935c: b.ls            #0x15e975c
    // 0x15e9360: LoadField: r1 = r2->field_f
    //     0x15e9360: ldur            w1, [x2, #0xf]
    // 0x15e9364: DecompressPointer r1
    //     0x15e9364: add             x1, x1, HEAP, lsl #32
    // 0x15e9368: r0 = controller()
    //     0x15e9368: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e936c: LoadField: r1 = r0->field_57
    //     0x15e936c: ldur            w1, [x0, #0x57]
    // 0x15e9370: DecompressPointer r1
    //     0x15e9370: add             x1, x1, HEAP, lsl #32
    // 0x15e9374: r0 = value()
    //     0x15e9374: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e9378: LoadField: r1 = r0->field_3f
    //     0x15e9378: ldur            w1, [x0, #0x3f]
    // 0x15e937c: DecompressPointer r1
    //     0x15e937c: add             x1, x1, HEAP, lsl #32
    // 0x15e9380: cmp             w1, NULL
    // 0x15e9384: b.ne            #0x15e9390
    // 0x15e9388: r0 = Null
    //     0x15e9388: mov             x0, NULL
    // 0x15e938c: b               #0x15e9398
    // 0x15e9390: LoadField: r0 = r1->field_f
    //     0x15e9390: ldur            w0, [x1, #0xf]
    // 0x15e9394: DecompressPointer r0
    //     0x15e9394: add             x0, x0, HEAP, lsl #32
    // 0x15e9398: r1 = LoadClassIdInstr(r0)
    //     0x15e9398: ldur            x1, [x0, #-1]
    //     0x15e939c: ubfx            x1, x1, #0xc, #0x14
    // 0x15e93a0: r16 = "image_text"
    //     0x15e93a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15e93a4: ldr             x16, [x16, #0xa88]
    // 0x15e93a8: stp             x16, x0, [SP]
    // 0x15e93ac: mov             x0, x1
    // 0x15e93b0: mov             lr, x0
    // 0x15e93b4: ldr             lr, [x21, lr, lsl #3]
    // 0x15e93b8: blr             lr
    // 0x15e93bc: tbnz            w0, #4, #0x15e93c8
    // 0x15e93c0: r2 = true
    //     0x15e93c0: add             x2, NULL, #0x20  ; true
    // 0x15e93c4: b               #0x15e9428
    // 0x15e93c8: ldur            x0, [fp, #-8]
    // 0x15e93cc: LoadField: r1 = r0->field_f
    //     0x15e93cc: ldur            w1, [x0, #0xf]
    // 0x15e93d0: DecompressPointer r1
    //     0x15e93d0: add             x1, x1, HEAP, lsl #32
    // 0x15e93d4: r0 = controller()
    //     0x15e93d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e93d8: LoadField: r1 = r0->field_57
    //     0x15e93d8: ldur            w1, [x0, #0x57]
    // 0x15e93dc: DecompressPointer r1
    //     0x15e93dc: add             x1, x1, HEAP, lsl #32
    // 0x15e93e0: r0 = value()
    //     0x15e93e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e93e4: LoadField: r1 = r0->field_3f
    //     0x15e93e4: ldur            w1, [x0, #0x3f]
    // 0x15e93e8: DecompressPointer r1
    //     0x15e93e8: add             x1, x1, HEAP, lsl #32
    // 0x15e93ec: cmp             w1, NULL
    // 0x15e93f0: b.ne            #0x15e93fc
    // 0x15e93f4: r0 = Null
    //     0x15e93f4: mov             x0, NULL
    // 0x15e93f8: b               #0x15e9404
    // 0x15e93fc: LoadField: r0 = r1->field_f
    //     0x15e93fc: ldur            w0, [x1, #0xf]
    // 0x15e9400: DecompressPointer r0
    //     0x15e9400: add             x0, x0, HEAP, lsl #32
    // 0x15e9404: r1 = LoadClassIdInstr(r0)
    //     0x15e9404: ldur            x1, [x0, #-1]
    //     0x15e9408: ubfx            x1, x1, #0xc, #0x14
    // 0x15e940c: r16 = "image"
    //     0x15e940c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15e9410: stp             x16, x0, [SP]
    // 0x15e9414: mov             x0, x1
    // 0x15e9418: mov             lr, x0
    // 0x15e941c: ldr             lr, [x21, lr, lsl #3]
    // 0x15e9420: blr             lr
    // 0x15e9424: mov             x2, x0
    // 0x15e9428: ldur            x0, [fp, #-8]
    // 0x15e942c: stur            x2, [fp, #-0x10]
    // 0x15e9430: LoadField: r1 = r0->field_f
    //     0x15e9430: ldur            w1, [x0, #0xf]
    // 0x15e9434: DecompressPointer r1
    //     0x15e9434: add             x1, x1, HEAP, lsl #32
    // 0x15e9438: r0 = controller()
    //     0x15e9438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e943c: LoadField: r1 = r0->field_57
    //     0x15e943c: ldur            w1, [x0, #0x57]
    // 0x15e9440: DecompressPointer r1
    //     0x15e9440: add             x1, x1, HEAP, lsl #32
    // 0x15e9444: r0 = value()
    //     0x15e9444: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e9448: LoadField: r1 = r0->field_27
    //     0x15e9448: ldur            w1, [x0, #0x27]
    // 0x15e944c: DecompressPointer r1
    //     0x15e944c: add             x1, x1, HEAP, lsl #32
    // 0x15e9450: cmp             w1, NULL
    // 0x15e9454: b.ne            #0x15e9460
    // 0x15e9458: r2 = ""
    //     0x15e9458: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e945c: b               #0x15e9464
    // 0x15e9460: mov             x2, x1
    // 0x15e9464: ldur            x0, [fp, #-8]
    // 0x15e9468: ldur            x1, [fp, #-0x10]
    // 0x15e946c: stur            x2, [fp, #-0x18]
    // 0x15e9470: r0 = ImageHeaders.forImages()
    //     0x15e9470: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15e9474: stur            x0, [fp, #-0x20]
    // 0x15e9478: r0 = CachedNetworkImage()
    //     0x15e9478: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15e947c: stur            x0, [fp, #-0x28]
    // 0x15e9480: ldur            x16, [fp, #-0x20]
    // 0x15e9484: r30 = Instance_BoxFit
    //     0x15e9484: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e9488: ldr             lr, [lr, #0xb18]
    // 0x15e948c: stp             lr, x16, [SP, #0x10]
    // 0x15e9490: r16 = 50.000000
    //     0x15e9490: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15e9494: ldr             x16, [x16, #0xa90]
    // 0x15e9498: r30 = 50.000000
    //     0x15e9498: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15e949c: ldr             lr, [lr, #0xa90]
    // 0x15e94a0: stp             lr, x16, [SP]
    // 0x15e94a4: mov             x1, x0
    // 0x15e94a8: ldur            x2, [fp, #-0x18]
    // 0x15e94ac: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15e94ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15e94b0: ldr             x4, [x4, #0xa98]
    // 0x15e94b4: r0 = CachedNetworkImage()
    //     0x15e94b4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15e94b8: r0 = Visibility()
    //     0x15e94b8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e94bc: mov             x2, x0
    // 0x15e94c0: ldur            x0, [fp, #-0x28]
    // 0x15e94c4: stur            x2, [fp, #-0x18]
    // 0x15e94c8: StoreField: r2->field_b = r0
    //     0x15e94c8: stur            w0, [x2, #0xb]
    // 0x15e94cc: r0 = Instance_SizedBox
    //     0x15e94cc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e94d0: StoreField: r2->field_f = r0
    //     0x15e94d0: stur            w0, [x2, #0xf]
    // 0x15e94d4: ldur            x1, [fp, #-0x10]
    // 0x15e94d8: StoreField: r2->field_13 = r1
    //     0x15e94d8: stur            w1, [x2, #0x13]
    // 0x15e94dc: r3 = false
    //     0x15e94dc: add             x3, NULL, #0x30  ; false
    // 0x15e94e0: ArrayStore: r2[0] = r3  ; List_4
    //     0x15e94e0: stur            w3, [x2, #0x17]
    // 0x15e94e4: StoreField: r2->field_1b = r3
    //     0x15e94e4: stur            w3, [x2, #0x1b]
    // 0x15e94e8: StoreField: r2->field_1f = r3
    //     0x15e94e8: stur            w3, [x2, #0x1f]
    // 0x15e94ec: StoreField: r2->field_23 = r3
    //     0x15e94ec: stur            w3, [x2, #0x23]
    // 0x15e94f0: StoreField: r2->field_27 = r3
    //     0x15e94f0: stur            w3, [x2, #0x27]
    // 0x15e94f4: StoreField: r2->field_2b = r3
    //     0x15e94f4: stur            w3, [x2, #0x2b]
    // 0x15e94f8: ldur            x4, [fp, #-8]
    // 0x15e94fc: LoadField: r1 = r4->field_f
    //     0x15e94fc: ldur            w1, [x4, #0xf]
    // 0x15e9500: DecompressPointer r1
    //     0x15e9500: add             x1, x1, HEAP, lsl #32
    // 0x15e9504: r0 = controller()
    //     0x15e9504: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e9508: LoadField: r1 = r0->field_57
    //     0x15e9508: ldur            w1, [x0, #0x57]
    // 0x15e950c: DecompressPointer r1
    //     0x15e950c: add             x1, x1, HEAP, lsl #32
    // 0x15e9510: r0 = value()
    //     0x15e9510: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e9514: LoadField: r1 = r0->field_3f
    //     0x15e9514: ldur            w1, [x0, #0x3f]
    // 0x15e9518: DecompressPointer r1
    //     0x15e9518: add             x1, x1, HEAP, lsl #32
    // 0x15e951c: cmp             w1, NULL
    // 0x15e9520: b.ne            #0x15e952c
    // 0x15e9524: r0 = Null
    //     0x15e9524: mov             x0, NULL
    // 0x15e9528: b               #0x15e9534
    // 0x15e952c: LoadField: r0 = r1->field_f
    //     0x15e952c: ldur            w0, [x1, #0xf]
    // 0x15e9530: DecompressPointer r0
    //     0x15e9530: add             x0, x0, HEAP, lsl #32
    // 0x15e9534: r1 = LoadClassIdInstr(r0)
    //     0x15e9534: ldur            x1, [x0, #-1]
    //     0x15e9538: ubfx            x1, x1, #0xc, #0x14
    // 0x15e953c: r16 = "image_text"
    //     0x15e953c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15e9540: ldr             x16, [x16, #0xa88]
    // 0x15e9544: stp             x16, x0, [SP]
    // 0x15e9548: mov             x0, x1
    // 0x15e954c: mov             lr, x0
    // 0x15e9550: ldr             lr, [x21, lr, lsl #3]
    // 0x15e9554: blr             lr
    // 0x15e9558: tbnz            w0, #4, #0x15e9564
    // 0x15e955c: r2 = true
    //     0x15e955c: add             x2, NULL, #0x20  ; true
    // 0x15e9560: b               #0x15e95c4
    // 0x15e9564: ldur            x0, [fp, #-8]
    // 0x15e9568: LoadField: r1 = r0->field_f
    //     0x15e9568: ldur            w1, [x0, #0xf]
    // 0x15e956c: DecompressPointer r1
    //     0x15e956c: add             x1, x1, HEAP, lsl #32
    // 0x15e9570: r0 = controller()
    //     0x15e9570: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e9574: LoadField: r1 = r0->field_57
    //     0x15e9574: ldur            w1, [x0, #0x57]
    // 0x15e9578: DecompressPointer r1
    //     0x15e9578: add             x1, x1, HEAP, lsl #32
    // 0x15e957c: r0 = value()
    //     0x15e957c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e9580: LoadField: r1 = r0->field_3f
    //     0x15e9580: ldur            w1, [x0, #0x3f]
    // 0x15e9584: DecompressPointer r1
    //     0x15e9584: add             x1, x1, HEAP, lsl #32
    // 0x15e9588: cmp             w1, NULL
    // 0x15e958c: b.ne            #0x15e9598
    // 0x15e9590: r0 = Null
    //     0x15e9590: mov             x0, NULL
    // 0x15e9594: b               #0x15e95a0
    // 0x15e9598: LoadField: r0 = r1->field_f
    //     0x15e9598: ldur            w0, [x1, #0xf]
    // 0x15e959c: DecompressPointer r0
    //     0x15e959c: add             x0, x0, HEAP, lsl #32
    // 0x15e95a0: r1 = LoadClassIdInstr(r0)
    //     0x15e95a0: ldur            x1, [x0, #-1]
    //     0x15e95a4: ubfx            x1, x1, #0xc, #0x14
    // 0x15e95a8: r16 = "text"
    //     0x15e95a8: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15e95ac: stp             x16, x0, [SP]
    // 0x15e95b0: mov             x0, x1
    // 0x15e95b4: mov             lr, x0
    // 0x15e95b8: ldr             lr, [x21, lr, lsl #3]
    // 0x15e95bc: blr             lr
    // 0x15e95c0: mov             x2, x0
    // 0x15e95c4: ldur            x0, [fp, #-8]
    // 0x15e95c8: stur            x2, [fp, #-0x10]
    // 0x15e95cc: LoadField: r1 = r0->field_f
    //     0x15e95cc: ldur            w1, [x0, #0xf]
    // 0x15e95d0: DecompressPointer r1
    //     0x15e95d0: add             x1, x1, HEAP, lsl #32
    // 0x15e95d4: r0 = controller()
    //     0x15e95d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e95d8: LoadField: r1 = r0->field_57
    //     0x15e95d8: ldur            w1, [x0, #0x57]
    // 0x15e95dc: DecompressPointer r1
    //     0x15e95dc: add             x1, x1, HEAP, lsl #32
    // 0x15e95e0: r0 = value()
    //     0x15e95e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e95e4: LoadField: r1 = r0->field_2b
    //     0x15e95e4: ldur            w1, [x0, #0x2b]
    // 0x15e95e8: DecompressPointer r1
    //     0x15e95e8: add             x1, x1, HEAP, lsl #32
    // 0x15e95ec: cmp             w1, NULL
    // 0x15e95f0: b.ne            #0x15e95fc
    // 0x15e95f4: r4 = ""
    //     0x15e95f4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e95f8: b               #0x15e9600
    // 0x15e95fc: mov             x4, x1
    // 0x15e9600: ldur            x0, [fp, #-8]
    // 0x15e9604: ldur            x3, [fp, #-0x18]
    // 0x15e9608: ldur            x2, [fp, #-0x10]
    // 0x15e960c: stur            x4, [fp, #-0x20]
    // 0x15e9610: LoadField: r1 = r0->field_13
    //     0x15e9610: ldur            w1, [x0, #0x13]
    // 0x15e9614: DecompressPointer r1
    //     0x15e9614: add             x1, x1, HEAP, lsl #32
    // 0x15e9618: r0 = of()
    //     0x15e9618: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e961c: LoadField: r1 = r0->field_87
    //     0x15e961c: ldur            w1, [x0, #0x87]
    // 0x15e9620: DecompressPointer r1
    //     0x15e9620: add             x1, x1, HEAP, lsl #32
    // 0x15e9624: LoadField: r0 = r1->field_2b
    //     0x15e9624: ldur            w0, [x1, #0x2b]
    // 0x15e9628: DecompressPointer r0
    //     0x15e9628: add             x0, x0, HEAP, lsl #32
    // 0x15e962c: r16 = 16.000000
    //     0x15e962c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15e9630: ldr             x16, [x16, #0x188]
    // 0x15e9634: r30 = Instance_Color
    //     0x15e9634: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e9638: stp             lr, x16, [SP]
    // 0x15e963c: mov             x1, x0
    // 0x15e9640: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15e9640: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15e9644: ldr             x4, [x4, #0xaa0]
    // 0x15e9648: r0 = copyWith()
    //     0x15e9648: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e964c: stur            x0, [fp, #-8]
    // 0x15e9650: r0 = Text()
    //     0x15e9650: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e9654: mov             x1, x0
    // 0x15e9658: ldur            x0, [fp, #-0x20]
    // 0x15e965c: stur            x1, [fp, #-0x28]
    // 0x15e9660: StoreField: r1->field_b = r0
    //     0x15e9660: stur            w0, [x1, #0xb]
    // 0x15e9664: ldur            x0, [fp, #-8]
    // 0x15e9668: StoreField: r1->field_13 = r0
    //     0x15e9668: stur            w0, [x1, #0x13]
    // 0x15e966c: r0 = Visibility()
    //     0x15e966c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e9670: mov             x3, x0
    // 0x15e9674: ldur            x0, [fp, #-0x28]
    // 0x15e9678: stur            x3, [fp, #-8]
    // 0x15e967c: StoreField: r3->field_b = r0
    //     0x15e967c: stur            w0, [x3, #0xb]
    // 0x15e9680: r0 = Instance_SizedBox
    //     0x15e9680: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e9684: StoreField: r3->field_f = r0
    //     0x15e9684: stur            w0, [x3, #0xf]
    // 0x15e9688: ldur            x0, [fp, #-0x10]
    // 0x15e968c: StoreField: r3->field_13 = r0
    //     0x15e968c: stur            w0, [x3, #0x13]
    // 0x15e9690: r0 = false
    //     0x15e9690: add             x0, NULL, #0x30  ; false
    // 0x15e9694: ArrayStore: r3[0] = r0  ; List_4
    //     0x15e9694: stur            w0, [x3, #0x17]
    // 0x15e9698: StoreField: r3->field_1b = r0
    //     0x15e9698: stur            w0, [x3, #0x1b]
    // 0x15e969c: StoreField: r3->field_1f = r0
    //     0x15e969c: stur            w0, [x3, #0x1f]
    // 0x15e96a0: StoreField: r3->field_23 = r0
    //     0x15e96a0: stur            w0, [x3, #0x23]
    // 0x15e96a4: StoreField: r3->field_27 = r0
    //     0x15e96a4: stur            w0, [x3, #0x27]
    // 0x15e96a8: StoreField: r3->field_2b = r0
    //     0x15e96a8: stur            w0, [x3, #0x2b]
    // 0x15e96ac: r1 = Null
    //     0x15e96ac: mov             x1, NULL
    // 0x15e96b0: r2 = 6
    //     0x15e96b0: movz            x2, #0x6
    // 0x15e96b4: r0 = AllocateArray()
    //     0x15e96b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e96b8: mov             x2, x0
    // 0x15e96bc: ldur            x0, [fp, #-0x18]
    // 0x15e96c0: stur            x2, [fp, #-0x10]
    // 0x15e96c4: StoreField: r2->field_f = r0
    //     0x15e96c4: stur            w0, [x2, #0xf]
    // 0x15e96c8: r16 = Instance_SizedBox
    //     0x15e96c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15e96cc: ldr             x16, [x16, #0xaa8]
    // 0x15e96d0: StoreField: r2->field_13 = r16
    //     0x15e96d0: stur            w16, [x2, #0x13]
    // 0x15e96d4: ldur            x0, [fp, #-8]
    // 0x15e96d8: ArrayStore: r2[0] = r0  ; List_4
    //     0x15e96d8: stur            w0, [x2, #0x17]
    // 0x15e96dc: r1 = <Widget>
    //     0x15e96dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e96e0: r0 = AllocateGrowableArray()
    //     0x15e96e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e96e4: mov             x1, x0
    // 0x15e96e8: ldur            x0, [fp, #-0x10]
    // 0x15e96ec: stur            x1, [fp, #-8]
    // 0x15e96f0: StoreField: r1->field_f = r0
    //     0x15e96f0: stur            w0, [x1, #0xf]
    // 0x15e96f4: r0 = 6
    //     0x15e96f4: movz            x0, #0x6
    // 0x15e96f8: StoreField: r1->field_b = r0
    //     0x15e96f8: stur            w0, [x1, #0xb]
    // 0x15e96fc: r0 = Row()
    //     0x15e96fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15e9700: r1 = Instance_Axis
    //     0x15e9700: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15e9704: StoreField: r0->field_f = r1
    //     0x15e9704: stur            w1, [x0, #0xf]
    // 0x15e9708: r1 = Instance_MainAxisAlignment
    //     0x15e9708: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15e970c: ldr             x1, [x1, #0xab0]
    // 0x15e9710: StoreField: r0->field_13 = r1
    //     0x15e9710: stur            w1, [x0, #0x13]
    // 0x15e9714: r1 = Instance_MainAxisSize
    //     0x15e9714: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15e9718: ldr             x1, [x1, #0xa10]
    // 0x15e971c: ArrayStore: r0[0] = r1  ; List_4
    //     0x15e971c: stur            w1, [x0, #0x17]
    // 0x15e9720: r1 = Instance_CrossAxisAlignment
    //     0x15e9720: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15e9724: ldr             x1, [x1, #0xa18]
    // 0x15e9728: StoreField: r0->field_1b = r1
    //     0x15e9728: stur            w1, [x0, #0x1b]
    // 0x15e972c: r1 = Instance_VerticalDirection
    //     0x15e972c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15e9730: ldr             x1, [x1, #0xa20]
    // 0x15e9734: StoreField: r0->field_23 = r1
    //     0x15e9734: stur            w1, [x0, #0x23]
    // 0x15e9738: r1 = Instance_Clip
    //     0x15e9738: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15e973c: ldr             x1, [x1, #0x38]
    // 0x15e9740: StoreField: r0->field_2b = r1
    //     0x15e9740: stur            w1, [x0, #0x2b]
    // 0x15e9744: StoreField: r0->field_2f = rZR
    //     0x15e9744: stur            xzr, [x0, #0x2f]
    // 0x15e9748: ldur            x1, [fp, #-8]
    // 0x15e974c: StoreField: r0->field_b = r1
    //     0x15e974c: stur            w1, [x0, #0xb]
    // 0x15e9750: LeaveFrame
    //     0x15e9750: mov             SP, fp
    //     0x15e9754: ldp             fp, lr, [SP], #0x10
    // 0x15e9758: ret
    //     0x15e9758: ret             
    // 0x15e975c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e975c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e9760: b               #0x15e9360
  }
}
