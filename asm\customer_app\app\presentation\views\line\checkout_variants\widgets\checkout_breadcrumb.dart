// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_breadcrumb.dart

// class id: 1049487, size: 0x8
class :: {
}

// class id: 3278, size: 0x1c, field offset: 0x14
class _CheckoutBreadCrumbState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x947bf4, size: 0x140
    // 0x947bf4: EnterFrame
    //     0x947bf4: stp             fp, lr, [SP, #-0x10]!
    //     0x947bf8: mov             fp, SP
    // 0x947bfc: AllocStack(0x28)
    //     0x947bfc: sub             SP, SP, #0x28
    // 0x947c00: SetupParameters(_CheckoutBreadCrumbState this /* r1 => r0, fp-0x10 */)
    //     0x947c00: mov             x0, x1
    //     0x947c04: stur            x1, [fp, #-0x10]
    // 0x947c08: CheckStackOverflow
    //     0x947c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947c0c: cmp             SP, x16
    //     0x947c10: b.ls            #0x947d28
    // 0x947c14: LoadField: r1 = r0->field_b
    //     0x947c14: ldur            w1, [x0, #0xb]
    // 0x947c18: DecompressPointer r1
    //     0x947c18: add             x1, x1, HEAP, lsl #32
    // 0x947c1c: cmp             w1, NULL
    // 0x947c20: b.eq            #0x947d30
    // 0x947c24: LoadField: r2 = r1->field_b
    //     0x947c24: ldur            w2, [x1, #0xb]
    // 0x947c28: DecompressPointer r2
    //     0x947c28: add             x2, x2, HEAP, lsl #32
    // 0x947c2c: LoadField: r3 = r2->field_7
    //     0x947c2c: ldur            w3, [x2, #7]
    // 0x947c30: DecompressPointer r3
    //     0x947c30: add             x3, x3, HEAP, lsl #32
    // 0x947c34: stur            x3, [fp, #-8]
    // 0x947c38: cmp             w3, NULL
    // 0x947c3c: b.ne            #0x947c48
    // 0x947c40: r0 = Null
    //     0x947c40: mov             x0, NULL
    // 0x947c44: b               #0x947c7c
    // 0x947c48: r1 = Function '<anonymous closure>':.
    //     0x947c48: add             x1, PP, #0x54, lsl #12  ; [pp+0x54868] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x947c4c: ldr             x1, [x1, #0x868]
    // 0x947c50: r2 = Null
    //     0x947c50: mov             x2, NULL
    // 0x947c54: r0 = AllocateClosure()
    //     0x947c54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x947c58: r16 = <String?>
    //     0x947c58: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x947c5c: ldur            lr, [fp, #-8]
    // 0x947c60: stp             lr, x16, [SP, #8]
    // 0x947c64: str             x0, [SP]
    // 0x947c68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x947c68: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x947c6c: r0 = map()
    //     0x947c6c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x947c70: mov             x1, x0
    // 0x947c74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x947c74: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x947c78: r0 = toList()
    //     0x947c78: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x947c7c: cmp             w0, NULL
    // 0x947c80: b.ne            #0x947cc0
    // 0x947c84: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x947c84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x947c88: ldr             x0, [x0]
    //     0x947c8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x947c90: cmp             w0, w16
    //     0x947c94: b.ne            #0x947ca0
    //     0x947c98: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x947c9c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x947ca0: r1 = <String?>
    //     0x947ca0: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x947ca4: stur            x0, [fp, #-8]
    // 0x947ca8: r0 = AllocateGrowableArray()
    //     0x947ca8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x947cac: mov             x1, x0
    // 0x947cb0: ldur            x0, [fp, #-8]
    // 0x947cb4: StoreField: r1->field_f = r0
    //     0x947cb4: stur            w0, [x1, #0xf]
    // 0x947cb8: StoreField: r1->field_b = rZR
    //     0x947cb8: stur            wzr, [x1, #0xb]
    // 0x947cbc: b               #0x947cc4
    // 0x947cc0: mov             x1, x0
    // 0x947cc4: ldur            x2, [fp, #-0x10]
    // 0x947cc8: mov             x0, x1
    // 0x947ccc: StoreField: r2->field_13 = r0
    //     0x947ccc: stur            w0, [x2, #0x13]
    //     0x947cd0: ldurb           w16, [x2, #-1]
    //     0x947cd4: ldurb           w17, [x0, #-1]
    //     0x947cd8: and             x16, x17, x16, lsr #2
    //     0x947cdc: tst             x16, HEAP, lsr #32
    //     0x947ce0: b.eq            #0x947ce8
    //     0x947ce4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x947ce8: r0 = toSet()
    //     0x947ce8: bl              #0x7d698c  ; [dart:core] _GrowableList::toSet
    // 0x947cec: mov             x1, x0
    // 0x947cf0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x947cf0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x947cf4: r0 = toList()
    //     0x947cf4: bl              #0x7dd5cc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0x947cf8: ldur            x1, [fp, #-0x10]
    // 0x947cfc: ArrayStore: r1[0] = r0  ; List_4
    //     0x947cfc: stur            w0, [x1, #0x17]
    //     0x947d00: ldurb           w16, [x1, #-1]
    //     0x947d04: ldurb           w17, [x0, #-1]
    //     0x947d08: and             x16, x17, x16, lsr #2
    //     0x947d0c: tst             x16, HEAP, lsr #32
    //     0x947d10: b.eq            #0x947d18
    //     0x947d14: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x947d18: r0 = Null
    //     0x947d18: mov             x0, NULL
    // 0x947d1c: LeaveFrame
    //     0x947d1c: mov             SP, fp
    //     0x947d20: ldp             fp, lr, [SP], #0x10
    // 0x947d24: ret
    //     0x947d24: ret             
    // 0x947d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947d28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947d2c: b               #0x947c14
    // 0x947d30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947d30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbb5090, size: 0x35c
    // 0xbb5090: EnterFrame
    //     0xbb5090: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5094: mov             fp, SP
    // 0xbb5098: AllocStack(0x58)
    //     0xbb5098: sub             SP, SP, #0x58
    // 0xbb509c: SetupParameters(_CheckoutBreadCrumbState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb509c: stur            x1, [fp, #-8]
    //     0xbb50a0: stur            x2, [fp, #-0x10]
    // 0xbb50a4: CheckStackOverflow
    //     0xbb50a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb50a8: cmp             SP, x16
    //     0xbb50ac: b.ls            #0xbb53d0
    // 0xbb50b0: r1 = 2
    //     0xbb50b0: movz            x1, #0x2
    // 0xbb50b4: r0 = AllocateContext()
    //     0xbb50b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb50b8: mov             x2, x0
    // 0xbb50bc: ldur            x0, [fp, #-8]
    // 0xbb50c0: stur            x2, [fp, #-0x18]
    // 0xbb50c4: StoreField: r2->field_f = r0
    //     0xbb50c4: stur            w0, [x2, #0xf]
    // 0xbb50c8: ldur            x1, [fp, #-0x10]
    // 0xbb50cc: StoreField: r2->field_13 = r1
    //     0xbb50cc: stur            w1, [x2, #0x13]
    // 0xbb50d0: LoadField: r1 = r0->field_b
    //     0xbb50d0: ldur            w1, [x0, #0xb]
    // 0xbb50d4: DecompressPointer r1
    //     0xbb50d4: add             x1, x1, HEAP, lsl #32
    // 0xbb50d8: cmp             w1, NULL
    // 0xbb50dc: b.eq            #0xbb53d8
    // 0xbb50e0: mov             x1, x0
    // 0xbb50e4: r0 = _calculateLineLength()
    //     0xbb50e4: bl              #0xa07c94  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::_calculateLineLength
    // 0xbb50e8: mov             v1.16b, v0.16b
    // 0xbb50ec: ldur            x0, [fp, #-8]
    // 0xbb50f0: stur            d1, [fp, #-0x48]
    // 0xbb50f4: LoadField: r1 = r0->field_b
    //     0xbb50f4: ldur            w1, [x0, #0xb]
    // 0xbb50f8: DecompressPointer r1
    //     0xbb50f8: add             x1, x1, HEAP, lsl #32
    // 0xbb50fc: cmp             w1, NULL
    // 0xbb5100: b.eq            #0xbb53dc
    // 0xbb5104: r1 = Instance_Color
    //     0xbb5104: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5108: d0 = 0.300000
    //     0xbb5108: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbb510c: ldr             d0, [x17, #0x658]
    // 0xbb5110: r0 = withOpacity()
    //     0xbb5110: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb5114: mov             x2, x0
    // 0xbb5118: ldur            x0, [fp, #-8]
    // 0xbb511c: stur            x2, [fp, #-0x10]
    // 0xbb5120: LoadField: r1 = r0->field_b
    //     0xbb5120: ldur            w1, [x0, #0xb]
    // 0xbb5124: DecompressPointer r1
    //     0xbb5124: add             x1, x1, HEAP, lsl #32
    // 0xbb5128: cmp             w1, NULL
    // 0xbb512c: b.eq            #0xbb53e0
    // 0xbb5130: r1 = Instance_Color
    //     0xbb5130: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5134: d0 = 0.300000
    //     0xbb5134: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbb5138: ldr             d0, [x17, #0x658]
    // 0xbb513c: r0 = withOpacity()
    //     0xbb513c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb5140: stur            x0, [fp, #-0x20]
    // 0xbb5144: r0 = LineStyle()
    //     0xbb5144: bl              #0xa07c88  ; AllocateLineStyleStub -> LineStyle (size=0x48)
    // 0xbb5148: mov             x3, x0
    // 0xbb514c: r0 = Instance_LineType
    //     0xbb514c: add             x0, PP, #0x54, lsl #12  ; [pp+0x547d8] Obj!LineType@d74fa1
    //     0xbb5150: ldr             x0, [x0, #0x7d8]
    // 0xbb5154: stur            x3, [fp, #-0x28]
    // 0xbb5158: StoreField: r3->field_37 = r0
    //     0xbb5158: stur            w0, [x3, #0x37]
    // 0xbb515c: ldur            x0, [fp, #-0x20]
    // 0xbb5160: StoreField: r3->field_b = r0
    //     0xbb5160: stur            w0, [x3, #0xb]
    // 0xbb5164: ldur            x0, [fp, #-0x10]
    // 0xbb5168: StoreField: r3->field_f = r0
    //     0xbb5168: stur            w0, [x3, #0xf]
    // 0xbb516c: ldur            d0, [fp, #-0x48]
    // 0xbb5170: ArrayStore: r3[0] = d0  ; List_8
    //     0xbb5170: stur            d0, [x3, #0x17]
    // 0xbb5174: d0 = 4.000000
    //     0xbb5174: fmov            d0, #4.00000000
    // 0xbb5178: StoreField: r3->field_27 = d0
    //     0xbb5178: stur            d0, [x3, #0x27]
    // 0xbb517c: d0 = 1.000000
    //     0xbb517c: fmov            d0, #1.00000000
    // 0xbb5180: StoreField: r3->field_1f = d0
    //     0xbb5180: stur            d0, [x3, #0x1f]
    // 0xbb5184: StoreField: r3->field_2f = rZR
    //     0xbb5184: stur            xzr, [x3, #0x2f]
    // 0xbb5188: ldur            x0, [fp, #-8]
    // 0xbb518c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb518c: ldur            w1, [x0, #0x17]
    // 0xbb5190: DecompressPointer r1
    //     0xbb5190: add             x1, x1, HEAP, lsl #32
    // 0xbb5194: LoadField: r0 = r1->field_b
    //     0xbb5194: ldur            w0, [x1, #0xb]
    // 0xbb5198: ldur            x2, [fp, #-0x18]
    // 0xbb519c: stur            x0, [fp, #-8]
    // 0xbb51a0: r1 = Function '<anonymous closure>':.
    //     0xbb51a0: add             x1, PP, #0x54, lsl #12  ; [pp+0x547e0] AnonymousClosure: (0xbb5470), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::build (0xbb5090)
    //     0xbb51a4: ldr             x1, [x1, #0x7e0]
    // 0xbb51a8: r0 = AllocateClosure()
    //     0xbb51a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb51ac: mov             x3, x0
    // 0xbb51b0: ldur            x0, [fp, #-8]
    // 0xbb51b4: stur            x3, [fp, #-0x10]
    // 0xbb51b8: r2 = LoadInt32Instr(r0)
    //     0xbb51b8: sbfx            x2, x0, #1, #0x1f
    // 0xbb51bc: r1 = <EasyStep>
    //     0xbb51bc: add             x1, PP, #0x54, lsl #12  ; [pp+0x547e8] TypeArguments: <EasyStep>
    //     0xbb51c0: ldr             x1, [x1, #0x7e8]
    // 0xbb51c4: r0 = _GrowableList()
    //     0xbb51c4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbb51c8: mov             x1, x0
    // 0xbb51cc: stur            x1, [fp, #-0x20]
    // 0xbb51d0: LoadField: r0 = r1->field_b
    //     0xbb51d0: ldur            w0, [x1, #0xb]
    // 0xbb51d4: r2 = LoadInt32Instr(r0)
    //     0xbb51d4: sbfx            x2, x0, #1, #0x1f
    // 0xbb51d8: stur            x2, [fp, #-0x38]
    // 0xbb51dc: LoadField: r3 = r1->field_f
    //     0xbb51dc: ldur            w3, [x1, #0xf]
    // 0xbb51e0: DecompressPointer r3
    //     0xbb51e0: add             x3, x3, HEAP, lsl #32
    // 0xbb51e4: stur            x3, [fp, #-8]
    // 0xbb51e8: r4 = 0
    //     0xbb51e8: movz            x4, #0
    // 0xbb51ec: stur            x4, [fp, #-0x30]
    // 0xbb51f0: CheckStackOverflow
    //     0xbb51f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb51f4: cmp             SP, x16
    //     0xbb51f8: b.ls            #0xbb53e4
    // 0xbb51fc: cmp             x4, x2
    // 0xbb5200: b.ge            #0xbb52a8
    // 0xbb5204: lsl             x0, x4, #1
    // 0xbb5208: ldur            x16, [fp, #-0x10]
    // 0xbb520c: stp             x0, x16, [SP]
    // 0xbb5210: ldur            x0, [fp, #-0x10]
    // 0xbb5214: ClosureCall
    //     0xbb5214: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xbb5218: ldur            x2, [x0, #0x1f]
    //     0xbb521c: blr             x2
    // 0xbb5220: mov             x3, x0
    // 0xbb5224: r2 = Null
    //     0xbb5224: mov             x2, NULL
    // 0xbb5228: r1 = Null
    //     0xbb5228: mov             x1, NULL
    // 0xbb522c: stur            x3, [fp, #-0x40]
    // 0xbb5230: r4 = 60
    //     0xbb5230: movz            x4, #0x3c
    // 0xbb5234: branchIfSmi(r0, 0xbb5240)
    //     0xbb5234: tbz             w0, #0, #0xbb5240
    // 0xbb5238: r4 = LoadClassIdInstr(r0)
    //     0xbb5238: ldur            x4, [x0, #-1]
    //     0xbb523c: ubfx            x4, x4, #0xc, #0x14
    // 0xbb5240: r17 = 4961
    //     0xbb5240: movz            x17, #0x1361
    // 0xbb5244: cmp             x4, x17
    // 0xbb5248: b.eq            #0xbb5260
    // 0xbb524c: r8 = EasyStep
    //     0xbb524c: add             x8, PP, #0x54, lsl #12  ; [pp+0x547f0] Type: EasyStep
    //     0xbb5250: ldr             x8, [x8, #0x7f0]
    // 0xbb5254: r3 = Null
    //     0xbb5254: add             x3, PP, #0x54, lsl #12  ; [pp+0x547f8] Null
    //     0xbb5258: ldr             x3, [x3, #0x7f8]
    // 0xbb525c: r0 = EasyStep()
    //     0xbb525c: bl              #0x94ba84  ; IsType_EasyStep_Stub
    // 0xbb5260: ldur            x1, [fp, #-8]
    // 0xbb5264: ldur            x0, [fp, #-0x40]
    // 0xbb5268: ldur            x2, [fp, #-0x30]
    // 0xbb526c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbb526c: add             x25, x1, x2, lsl #2
    //     0xbb5270: add             x25, x25, #0xf
    //     0xbb5274: str             w0, [x25]
    //     0xbb5278: tbz             w0, #0, #0xbb5294
    //     0xbb527c: ldurb           w16, [x1, #-1]
    //     0xbb5280: ldurb           w17, [x0, #-1]
    //     0xbb5284: and             x16, x17, x16, lsr #2
    //     0xbb5288: tst             x16, HEAP, lsr #32
    //     0xbb528c: b.eq            #0xbb5294
    //     0xbb5290: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb5294: add             x4, x2, #1
    // 0xbb5298: ldur            x1, [fp, #-0x20]
    // 0xbb529c: ldur            x3, [fp, #-8]
    // 0xbb52a0: ldur            x2, [fp, #-0x38]
    // 0xbb52a4: b               #0xbb51ec
    // 0xbb52a8: mov             x0, x1
    // 0xbb52ac: ldur            x1, [fp, #-0x28]
    // 0xbb52b0: r0 = EasyStepper()
    //     0xbb52b0: bl              #0xa07c7c  ; AllocateEasyStepperStub -> EasyStepper (size=0xc8)
    // 0xbb52b4: stur            x0, [fp, #-8]
    // 0xbb52b8: StoreField: r0->field_67 = rZR
    //     0xbb52b8: stur            xzr, [x0, #0x67]
    // 0xbb52bc: ldur            x1, [fp, #-0x20]
    // 0xbb52c0: StoreField: r0->field_b = r1
    //     0xbb52c0: stur            w1, [x0, #0xb]
    // 0xbb52c4: r3 = true
    //     0xbb52c4: add             x3, NULL, #0x20  ; true
    // 0xbb52c8: StoreField: r0->field_f = r3
    //     0xbb52c8: stur            w3, [x0, #0xf]
    // 0xbb52cc: r1 = Instance_Axis
    //     0xbb52cc: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb52d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb52d0: stur            w1, [x0, #0x17]
    // 0xbb52d4: ldur            x2, [fp, #-0x18]
    // 0xbb52d8: r1 = Function '<anonymous closure>':.
    //     0xbb52d8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54808] AnonymousClosure: (0xbb53ec), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::build (0xbb5090)
    //     0xbb52dc: ldr             x1, [x1, #0x808]
    // 0xbb52e0: r0 = AllocateClosure()
    //     0xbb52e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb52e4: mov             x1, x0
    // 0xbb52e8: ldur            x0, [fp, #-8]
    // 0xbb52ec: StoreField: r0->field_13 = r1
    //     0xbb52ec: stur            w1, [x0, #0x13]
    // 0xbb52f0: d0 = 10.000000
    //     0xbb52f0: fmov            d0, #10.00000000
    // 0xbb52f4: StoreField: r0->field_57 = d0
    //     0xbb52f4: stur            d0, [x0, #0x57]
    // 0xbb52f8: r1 = true
    //     0xbb52f8: add             x1, NULL, #0x20  ; true
    // 0xbb52fc: StoreField: r0->field_5f = r1
    //     0xbb52fc: stur            w1, [x0, #0x5f]
    // 0xbb5300: r2 = false
    //     0xbb5300: add             x2, NULL, #0x30  ; false
    // 0xbb5304: StoreField: r0->field_63 = r2
    //     0xbb5304: stur            w2, [x0, #0x63]
    // 0xbb5308: StoreField: r0->field_77 = r1
    //     0xbb5308: stur            w1, [x0, #0x77]
    // 0xbb530c: r3 = Instance_Alignment
    //     0xbb530c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xbb5310: ldr             x3, [x3, #0xf98]
    // 0xbb5314: StoreField: r0->field_7b = r3
    //     0xbb5314: stur            w3, [x0, #0x7b]
    // 0xbb5318: StoreField: r0->field_bf = r1
    //     0xbb5318: stur            w1, [x0, #0xbf]
    // 0xbb531c: r1 = Instance_EdgeInsetsDirectional
    //     0xbb531c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54810] Obj!EdgeInsetsDirectional@d569f1
    //     0xbb5320: ldr             x1, [x1, #0x810]
    // 0xbb5324: StoreField: r0->field_87 = r1
    //     0xbb5324: stur            w1, [x0, #0x87]
    // 0xbb5328: StoreField: r0->field_8b = r2
    //     0xbb5328: stur            w2, [x0, #0x8b]
    // 0xbb532c: d0 = 8.000000
    //     0xbb532c: fmov            d0, #8.00000000
    // 0xbb5330: StoreField: r0->field_7f = d0
    //     0xbb5330: stur            d0, [x0, #0x7f]
    // 0xbb5334: r1 = Instance__Linear
    //     0xbb5334: ldr             x1, [PP, #0x4cf8]  ; [pp+0x4cf8] Obj!_Linear@d5bb01
    // 0xbb5338: StoreField: r0->field_8f = r1
    //     0xbb5338: stur            w1, [x0, #0x8f]
    // 0xbb533c: r1 = Instance_Duration
    //     0xbb533c: ldr             x1, [PP, #0xa68]  ; [pp+0xa68] Obj!Duration@d776d1
    // 0xbb5340: StoreField: r0->field_93 = r1
    //     0xbb5340: stur            w1, [x0, #0x93]
    // 0xbb5344: d0 = 0.800000
    //     0xbb5344: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0xbb5348: ldr             d0, [x17, #0xb28]
    // 0xbb534c: StoreField: r0->field_97 = d0
    //     0xbb534c: stur            d0, [x0, #0x97]
    // 0xbb5350: r1 = Instance_StepShape
    //     0xbb5350: add             x1, PP, #0x54, lsl #12  ; [pp+0x54818] Obj!StepShape@d75061
    //     0xbb5354: ldr             x1, [x1, #0x818]
    // 0xbb5358: StoreField: r0->field_9f = r1
    //     0xbb5358: stur            w1, [x0, #0x9f]
    // 0xbb535c: r1 = Instance_BorderType
    //     0xbb535c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54820] Obj!BorderType@d75041
    //     0xbb5360: ldr             x1, [x1, #0x820]
    // 0xbb5364: StoreField: r0->field_a7 = r1
    //     0xbb5364: stur            w1, [x0, #0xa7]
    // 0xbb5368: r1 = const [3.0, 1.0]
    //     0xbb5368: add             x1, PP, #0x54, lsl #12  ; [pp+0x54828] List<double>(2)
    //     0xbb536c: ldr             x1, [x1, #0x828]
    // 0xbb5370: StoreField: r0->field_ab = r1
    //     0xbb5370: stur            w1, [x0, #0xab]
    // 0xbb5374: StoreField: r0->field_af = r2
    //     0xbb5374: stur            w2, [x0, #0xaf]
    // 0xbb5378: StoreField: r0->field_b7 = r2
    //     0xbb5378: stur            w2, [x0, #0xb7]
    // 0xbb537c: r1 = Instance_TextDirection
    //     0xbb537c: ldr             x1, [PP, #0x2348]  ; [pp+0x2348] Obj!TextDirection@d76521
    // 0xbb5380: StoreField: r0->field_bb = r1
    //     0xbb5380: stur            w1, [x0, #0xbb]
    // 0xbb5384: ldur            x1, [fp, #-0x28]
    // 0xbb5388: StoreField: r0->field_c3 = r1
    //     0xbb5388: stur            w1, [x0, #0xc3]
    // 0xbb538c: r0 = SizedBox()
    //     0xbb538c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb5390: mov             x1, x0
    // 0xbb5394: r0 = 56.000000
    //     0xbb5394: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb5398: ldr             x0, [x0, #0xb78]
    // 0xbb539c: stur            x1, [fp, #-0x10]
    // 0xbb53a0: StoreField: r1->field_13 = r0
    //     0xbb53a0: stur            w0, [x1, #0x13]
    // 0xbb53a4: ldur            x0, [fp, #-8]
    // 0xbb53a8: StoreField: r1->field_b = r0
    //     0xbb53a8: stur            w0, [x1, #0xb]
    // 0xbb53ac: r0 = Padding()
    //     0xbb53ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb53b0: r1 = Instance_EdgeInsets
    //     0xbb53b0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54830] Obj!EdgeInsets@d57e31
    //     0xbb53b4: ldr             x1, [x1, #0x830]
    // 0xbb53b8: StoreField: r0->field_f = r1
    //     0xbb53b8: stur            w1, [x0, #0xf]
    // 0xbb53bc: ldur            x1, [fp, #-0x10]
    // 0xbb53c0: StoreField: r0->field_b = r1
    //     0xbb53c0: stur            w1, [x0, #0xb]
    // 0xbb53c4: LeaveFrame
    //     0xbb53c4: mov             SP, fp
    //     0xbb53c8: ldp             fp, lr, [SP], #0x10
    // 0xbb53cc: ret
    //     0xbb53cc: ret             
    // 0xbb53d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb53d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb53d4: b               #0xbb50b0
    // 0xbb53d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb53d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb53dc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb53dc: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb53e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb53e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb53e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb53e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb53e8: b               #0xbb51fc
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbb53ec, size: 0x84
    // 0xbb53ec: EnterFrame
    //     0xbb53ec: stp             fp, lr, [SP, #-0x10]!
    //     0xbb53f0: mov             fp, SP
    // 0xbb53f4: AllocStack(0x10)
    //     0xbb53f4: sub             SP, SP, #0x10
    // 0xbb53f8: SetupParameters()
    //     0xbb53f8: ldr             x0, [fp, #0x18]
    //     0xbb53fc: ldur            w1, [x0, #0x17]
    //     0xbb5400: add             x1, x1, HEAP, lsl #32
    //     0xbb5404: stur            x1, [fp, #-8]
    // 0xbb5408: CheckStackOverflow
    //     0xbb5408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb540c: cmp             SP, x16
    //     0xbb5410: b.ls            #0xbb5468
    // 0xbb5414: r1 = 1
    //     0xbb5414: movz            x1, #0x1
    // 0xbb5418: r0 = AllocateContext()
    //     0xbb5418: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb541c: mov             x1, x0
    // 0xbb5420: ldur            x0, [fp, #-8]
    // 0xbb5424: StoreField: r1->field_b = r0
    //     0xbb5424: stur            w0, [x1, #0xb]
    // 0xbb5428: ldr             x2, [fp, #0x10]
    // 0xbb542c: StoreField: r1->field_f = r2
    //     0xbb542c: stur            w2, [x1, #0xf]
    // 0xbb5430: LoadField: r3 = r0->field_f
    //     0xbb5430: ldur            w3, [x0, #0xf]
    // 0xbb5434: DecompressPointer r3
    //     0xbb5434: add             x3, x3, HEAP, lsl #32
    // 0xbb5438: mov             x2, x1
    // 0xbb543c: stur            x3, [fp, #-0x10]
    // 0xbb5440: r1 = Function '<anonymous closure>':.
    //     0xbb5440: add             x1, PP, #0x54, lsl #12  ; [pp+0x54838] AnonymousClosure: (0x8d3db4), in [package:get/get_instance/src/get_instance.dart] GetInstance::put (0x8d3dcc)
    //     0xbb5444: ldr             x1, [x1, #0x838]
    // 0xbb5448: r0 = AllocateClosure()
    //     0xbb5448: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb544c: ldur            x1, [fp, #-0x10]
    // 0xbb5450: mov             x2, x0
    // 0xbb5454: r0 = setState()
    //     0xbb5454: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb5458: r0 = Null
    //     0xbb5458: mov             x0, NULL
    // 0xbb545c: LeaveFrame
    //     0xbb545c: mov             SP, fp
    //     0xbb5460: ldp             fp, lr, [SP], #0x10
    // 0xbb5464: ret
    //     0xbb5464: ret             
    // 0xbb5468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb5468: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb546c: b               #0xbb5414
  }
  [closure] EasyStep <anonymous closure>(dynamic, int) {
    // ** addr: 0xbb5470, size: 0x450
    // 0xbb5470: EnterFrame
    //     0xbb5470: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5474: mov             fp, SP
    // 0xbb5478: AllocStack(0x50)
    //     0xbb5478: sub             SP, SP, #0x50
    // 0xbb547c: SetupParameters()
    //     0xbb547c: ldr             x0, [fp, #0x18]
    //     0xbb5480: ldur            w2, [x0, #0x17]
    //     0xbb5484: add             x2, x2, HEAP, lsl #32
    //     0xbb5488: stur            x2, [fp, #-8]
    // 0xbb548c: CheckStackOverflow
    //     0xbb548c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb5490: cmp             SP, x16
    //     0xbb5494: b.ls            #0xbb587c
    // 0xbb5498: LoadField: r0 = r2->field_f
    //     0xbb5498: ldur            w0, [x2, #0xf]
    // 0xbb549c: DecompressPointer r0
    //     0xbb549c: add             x0, x0, HEAP, lsl #32
    // 0xbb54a0: LoadField: r1 = r0->field_b
    //     0xbb54a0: ldur            w1, [x0, #0xb]
    // 0xbb54a4: DecompressPointer r1
    //     0xbb54a4: add             x1, x1, HEAP, lsl #32
    // 0xbb54a8: cmp             w1, NULL
    // 0xbb54ac: b.eq            #0xbb5884
    // 0xbb54b0: LoadField: r0 = r1->field_b
    //     0xbb54b0: ldur            w0, [x1, #0xb]
    // 0xbb54b4: DecompressPointer r0
    //     0xbb54b4: add             x0, x0, HEAP, lsl #32
    // 0xbb54b8: LoadField: r3 = r0->field_7
    //     0xbb54b8: ldur            w3, [x0, #7]
    // 0xbb54bc: DecompressPointer r3
    //     0xbb54bc: add             x3, x3, HEAP, lsl #32
    // 0xbb54c0: cmp             w3, NULL
    // 0xbb54c4: b.ne            #0xbb54d4
    // 0xbb54c8: ldr             x4, [fp, #0x10]
    // 0xbb54cc: r0 = Null
    //     0xbb54cc: mov             x0, NULL
    // 0xbb54d0: b               #0xbb5514
    // 0xbb54d4: ldr             x4, [fp, #0x10]
    // 0xbb54d8: LoadField: r0 = r3->field_b
    //     0xbb54d8: ldur            w0, [x3, #0xb]
    // 0xbb54dc: r5 = LoadInt32Instr(r4)
    //     0xbb54dc: sbfx            x5, x4, #1, #0x1f
    //     0xbb54e0: tbz             w4, #0, #0xbb54e8
    //     0xbb54e4: ldur            x5, [x4, #7]
    // 0xbb54e8: r1 = LoadInt32Instr(r0)
    //     0xbb54e8: sbfx            x1, x0, #1, #0x1f
    // 0xbb54ec: mov             x0, x1
    // 0xbb54f0: mov             x1, x5
    // 0xbb54f4: cmp             x1, x0
    // 0xbb54f8: b.hs            #0xbb5888
    // 0xbb54fc: LoadField: r0 = r3->field_f
    //     0xbb54fc: ldur            w0, [x3, #0xf]
    // 0xbb5500: DecompressPointer r0
    //     0xbb5500: add             x0, x0, HEAP, lsl #32
    // 0xbb5504: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbb5504: add             x16, x0, x5, lsl #2
    //     0xbb5508: ldur            w1, [x16, #0xf]
    // 0xbb550c: DecompressPointer r1
    //     0xbb550c: add             x1, x1, HEAP, lsl #32
    // 0xbb5510: mov             x0, x1
    // 0xbb5514: cmp             w0, NULL
    // 0xbb5518: b.ne            #0xbb5520
    // 0xbb551c: r0 = BreadCrumbData()
    //     0xbb551c: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0xbb5520: stur            x0, [fp, #-0x18]
    // 0xbb5524: LoadField: r1 = r0->field_7
    //     0xbb5524: ldur            w1, [x0, #7]
    // 0xbb5528: DecompressPointer r1
    //     0xbb5528: add             x1, x1, HEAP, lsl #32
    // 0xbb552c: cmp             w1, NULL
    // 0xbb5530: b.ne            #0xbb553c
    // 0xbb5534: r3 = ""
    //     0xbb5534: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb5538: b               #0xbb5540
    // 0xbb553c: mov             x3, x1
    // 0xbb5540: ldr             x2, [fp, #0x10]
    // 0xbb5544: ldur            x1, [fp, #-8]
    // 0xbb5548: stur            x3, [fp, #-0x10]
    // 0xbb554c: r0 = SvgPicture()
    //     0xbb554c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbb5550: mov             x1, x0
    // 0xbb5554: ldur            x2, [fp, #-0x10]
    // 0xbb5558: stur            x0, [fp, #-0x10]
    // 0xbb555c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb555c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb5560: r0 = SvgPicture.asset()
    //     0xbb5560: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbb5564: ldur            x3, [fp, #-8]
    // 0xbb5568: LoadField: r2 = r3->field_f
    //     0xbb5568: ldur            w2, [x3, #0xf]
    // 0xbb556c: DecompressPointer r2
    //     0xbb556c: add             x2, x2, HEAP, lsl #32
    // 0xbb5570: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbb5570: ldur            w4, [x2, #0x17]
    // 0xbb5574: DecompressPointer r4
    //     0xbb5574: add             x4, x4, HEAP, lsl #32
    // 0xbb5578: LoadField: r0 = r4->field_b
    //     0xbb5578: ldur            w0, [x4, #0xb]
    // 0xbb557c: ldr             x1, [fp, #0x10]
    // 0xbb5580: r5 = LoadInt32Instr(r1)
    //     0xbb5580: sbfx            x5, x1, #1, #0x1f
    //     0xbb5584: tbz             w1, #0, #0xbb558c
    //     0xbb5588: ldur            x5, [x1, #7]
    // 0xbb558c: stur            x5, [fp, #-0x20]
    // 0xbb5590: r1 = LoadInt32Instr(r0)
    //     0xbb5590: sbfx            x1, x0, #1, #0x1f
    // 0xbb5594: mov             x0, x1
    // 0xbb5598: mov             x1, x5
    // 0xbb559c: cmp             x1, x0
    // 0xbb55a0: b.hs            #0xbb588c
    // 0xbb55a4: LoadField: r0 = r4->field_f
    //     0xbb55a4: ldur            w0, [x4, #0xf]
    // 0xbb55a8: DecompressPointer r0
    //     0xbb55a8: add             x0, x0, HEAP, lsl #32
    // 0xbb55ac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbb55ac: add             x16, x0, x5, lsl #2
    //     0xbb55b0: ldur            w1, [x16, #0xf]
    // 0xbb55b4: DecompressPointer r1
    //     0xbb55b4: add             x1, x1, HEAP, lsl #32
    // 0xbb55b8: mov             x16, x1
    // 0xbb55bc: mov             x1, x2
    // 0xbb55c0: mov             x2, x16
    // 0xbb55c4: r0 = _getTextWidth()
    //     0xbb55c4: bl              #0xa084ac  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::_getTextWidth
    // 0xbb55c8: ldur            x3, [fp, #-8]
    // 0xbb55cc: stur            d0, [fp, #-0x38]
    // 0xbb55d0: LoadField: r2 = r3->field_f
    //     0xbb55d0: ldur            w2, [x3, #0xf]
    // 0xbb55d4: DecompressPointer r2
    //     0xbb55d4: add             x2, x2, HEAP, lsl #32
    // 0xbb55d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbb55d8: ldur            w4, [x2, #0x17]
    // 0xbb55dc: DecompressPointer r4
    //     0xbb55dc: add             x4, x4, HEAP, lsl #32
    // 0xbb55e0: LoadField: r0 = r4->field_b
    //     0xbb55e0: ldur            w0, [x4, #0xb]
    // 0xbb55e4: r1 = LoadInt32Instr(r0)
    //     0xbb55e4: sbfx            x1, x0, #1, #0x1f
    // 0xbb55e8: mov             x0, x1
    // 0xbb55ec: ldur            x1, [fp, #-0x20]
    // 0xbb55f0: cmp             x1, x0
    // 0xbb55f4: b.hs            #0xbb5890
    // 0xbb55f8: LoadField: r0 = r4->field_f
    //     0xbb55f8: ldur            w0, [x4, #0xf]
    // 0xbb55fc: DecompressPointer r0
    //     0xbb55fc: add             x0, x0, HEAP, lsl #32
    // 0xbb5600: ldur            x4, [fp, #-0x20]
    // 0xbb5604: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbb5604: add             x16, x0, x4, lsl #2
    //     0xbb5608: ldur            w1, [x16, #0xf]
    // 0xbb560c: DecompressPointer r1
    //     0xbb560c: add             x1, x1, HEAP, lsl #32
    // 0xbb5610: mov             x16, x1
    // 0xbb5614: mov             x1, x2
    // 0xbb5618: mov             x2, x16
    // 0xbb561c: r0 = _formatText()
    //     0xbb561c: bl              #0xa08320  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::_formatText
    // 0xbb5620: mov             x2, x0
    // 0xbb5624: ldur            x0, [fp, #-8]
    // 0xbb5628: stur            x2, [fp, #-0x28]
    // 0xbb562c: LoadField: r1 = r0->field_13
    //     0xbb562c: ldur            w1, [x0, #0x13]
    // 0xbb5630: DecompressPointer r1
    //     0xbb5630: add             x1, x1, HEAP, lsl #32
    // 0xbb5634: r0 = of()
    //     0xbb5634: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb5638: LoadField: r1 = r0->field_87
    //     0xbb5638: ldur            w1, [x0, #0x87]
    // 0xbb563c: DecompressPointer r1
    //     0xbb563c: add             x1, x1, HEAP, lsl #32
    // 0xbb5640: LoadField: r0 = r1->field_7
    //     0xbb5640: ldur            w0, [x1, #7]
    // 0xbb5644: DecompressPointer r0
    //     0xbb5644: add             x0, x0, HEAP, lsl #32
    // 0xbb5648: ldur            x1, [fp, #-0x18]
    // 0xbb564c: stur            x0, [fp, #-0x30]
    // 0xbb5650: LoadField: r2 = r1->field_f
    //     0xbb5650: ldur            w2, [x1, #0xf]
    // 0xbb5654: DecompressPointer r2
    //     0xbb5654: add             x2, x2, HEAP, lsl #32
    // 0xbb5658: cmp             w2, NULL
    // 0xbb565c: b.eq            #0xbb566c
    // 0xbb5660: tbnz            w2, #4, #0xbb566c
    // 0xbb5664: r5 = Instance_Color
    //     0xbb5664: ldr             x5, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5668: b               #0xbb5680
    // 0xbb566c: r1 = Instance_Color
    //     0xbb566c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5670: d0 = 0.300000
    //     0xbb5670: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbb5674: ldr             d0, [x17, #0x658]
    // 0xbb5678: r0 = withOpacity()
    //     0xbb5678: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb567c: mov             x5, x0
    // 0xbb5680: ldur            x0, [fp, #-8]
    // 0xbb5684: ldur            x4, [fp, #-0x10]
    // 0xbb5688: ldur            d0, [fp, #-0x38]
    // 0xbb568c: ldur            x3, [fp, #-0x28]
    // 0xbb5690: ldur            x2, [fp, #-0x20]
    // 0xbb5694: stur            x5, [fp, #-0x18]
    // 0xbb5698: LoadField: r6 = r0->field_f
    //     0xbb5698: ldur            w6, [x0, #0xf]
    // 0xbb569c: DecompressPointer r6
    //     0xbb569c: add             x6, x6, HEAP, lsl #32
    // 0xbb56a0: ArrayLoad: r7 = r6[0]  ; List_4
    //     0xbb56a0: ldur            w7, [x6, #0x17]
    // 0xbb56a4: DecompressPointer r7
    //     0xbb56a4: add             x7, x7, HEAP, lsl #32
    // 0xbb56a8: LoadField: r0 = r7->field_b
    //     0xbb56a8: ldur            w0, [x7, #0xb]
    // 0xbb56ac: r1 = LoadInt32Instr(r0)
    //     0xbb56ac: sbfx            x1, x0, #1, #0x1f
    // 0xbb56b0: mov             x0, x1
    // 0xbb56b4: mov             x1, x2
    // 0xbb56b8: cmp             x1, x0
    // 0xbb56bc: b.hs            #0xbb5894
    // 0xbb56c0: LoadField: r0 = r7->field_f
    //     0xbb56c0: ldur            w0, [x7, #0xf]
    // 0xbb56c4: DecompressPointer r0
    //     0xbb56c4: add             x0, x0, HEAP, lsl #32
    // 0xbb56c8: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xbb56c8: add             x16, x0, x2, lsl #2
    //     0xbb56cc: ldur            w1, [x16, #0xf]
    // 0xbb56d0: DecompressPointer r1
    //     0xbb56d0: add             x1, x1, HEAP, lsl #32
    // 0xbb56d4: mov             x2, x1
    // 0xbb56d8: mov             x1, x6
    // 0xbb56dc: r0 = _getFontSize()
    //     0xbb56dc: bl              #0xa08264  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::_getFontSize
    // 0xbb56e0: r0 = inline_Allocate_Double()
    //     0xbb56e0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbb56e4: add             x0, x0, #0x10
    //     0xbb56e8: cmp             x1, x0
    //     0xbb56ec: b.ls            #0xbb5898
    //     0xbb56f0: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb56f4: sub             x0, x0, #0xf
    //     0xbb56f8: movz            x1, #0xe15c
    //     0xbb56fc: movk            x1, #0x3, lsl #16
    //     0xbb5700: stur            x1, [x0, #-1]
    // 0xbb5704: StoreField: r0->field_7 = d0
    //     0xbb5704: stur            d0, [x0, #7]
    // 0xbb5708: ldur            x16, [fp, #-0x18]
    // 0xbb570c: stp             x0, x16, [SP, #8]
    // 0xbb5710: r16 = 1.100000
    //     0xbb5710: add             x16, PP, #0x54, lsl #12  ; [pp+0x54840] 1.1
    //     0xbb5714: ldr             x16, [x16, #0x840]
    // 0xbb5718: str             x16, [SP]
    // 0xbb571c: ldur            x1, [fp, #-0x30]
    // 0xbb5720: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, fontSize, 0x2, height, 0x3, null]
    //     0xbb5720: add             x4, PP, #0x54, lsl #12  ; [pp+0x54848] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "fontSize", 0x2, "height", 0x3, Null]
    //     0xbb5724: ldr             x4, [x4, #0x848]
    // 0xbb5728: r0 = copyWith()
    //     0xbb5728: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb572c: stur            x0, [fp, #-8]
    // 0xbb5730: r0 = Text()
    //     0xbb5730: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb5734: mov             x1, x0
    // 0xbb5738: ldur            x0, [fp, #-0x28]
    // 0xbb573c: stur            x1, [fp, #-0x18]
    // 0xbb5740: StoreField: r1->field_b = r0
    //     0xbb5740: stur            w0, [x1, #0xb]
    // 0xbb5744: ldur            x0, [fp, #-8]
    // 0xbb5748: StoreField: r1->field_13 = r0
    //     0xbb5748: stur            w0, [x1, #0x13]
    // 0xbb574c: r0 = Instance_TextAlign
    //     0xbb574c: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbb5750: StoreField: r1->field_1b = r0
    //     0xbb5750: stur            w0, [x1, #0x1b]
    // 0xbb5754: r0 = Instance_TextOverflow
    //     0xbb5754: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbb5758: ldr             x0, [x0, #0xe10]
    // 0xbb575c: StoreField: r1->field_2b = r0
    //     0xbb575c: stur            w0, [x1, #0x2b]
    // 0xbb5760: r0 = 4
    //     0xbb5760: movz            x0, #0x4
    // 0xbb5764: StoreField: r1->field_37 = r0
    //     0xbb5764: stur            w0, [x1, #0x37]
    // 0xbb5768: ldur            d0, [fp, #-0x38]
    // 0xbb576c: r0 = inline_Allocate_Double()
    //     0xbb576c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbb5770: add             x0, x0, #0x10
    //     0xbb5774: cmp             x2, x0
    //     0xbb5778: b.ls            #0xbb58a8
    //     0xbb577c: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb5780: sub             x0, x0, #0xf
    //     0xbb5784: movz            x2, #0xe15c
    //     0xbb5788: movk            x2, #0x3, lsl #16
    //     0xbb578c: stur            x2, [x0, #-1]
    // 0xbb5790: StoreField: r0->field_7 = d0
    //     0xbb5790: stur            d0, [x0, #7]
    // 0xbb5794: stur            x0, [fp, #-8]
    // 0xbb5798: r0 = SizedBox()
    //     0xbb5798: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb579c: mov             x3, x0
    // 0xbb57a0: ldur            x0, [fp, #-8]
    // 0xbb57a4: stur            x3, [fp, #-0x28]
    // 0xbb57a8: StoreField: r3->field_f = r0
    //     0xbb57a8: stur            w0, [x3, #0xf]
    // 0xbb57ac: ldur            x0, [fp, #-0x18]
    // 0xbb57b0: StoreField: r3->field_b = r0
    //     0xbb57b0: stur            w0, [x3, #0xb]
    // 0xbb57b4: r1 = Null
    //     0xbb57b4: mov             x1, NULL
    // 0xbb57b8: r2 = 2
    //     0xbb57b8: movz            x2, #0x2
    // 0xbb57bc: r0 = AllocateArray()
    //     0xbb57bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb57c0: mov             x2, x0
    // 0xbb57c4: ldur            x0, [fp, #-0x28]
    // 0xbb57c8: stur            x2, [fp, #-8]
    // 0xbb57cc: StoreField: r2->field_f = r0
    //     0xbb57cc: stur            w0, [x2, #0xf]
    // 0xbb57d0: r1 = <Widget>
    //     0xbb57d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb57d4: r0 = AllocateGrowableArray()
    //     0xbb57d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb57d8: mov             x1, x0
    // 0xbb57dc: ldur            x0, [fp, #-8]
    // 0xbb57e0: stur            x1, [fp, #-0x18]
    // 0xbb57e4: StoreField: r1->field_f = r0
    //     0xbb57e4: stur            w0, [x1, #0xf]
    // 0xbb57e8: r0 = 2
    //     0xbb57e8: movz            x0, #0x2
    // 0xbb57ec: StoreField: r1->field_b = r0
    //     0xbb57ec: stur            w0, [x1, #0xb]
    // 0xbb57f0: r0 = Column()
    //     0xbb57f0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb57f4: mov             x1, x0
    // 0xbb57f8: r0 = Instance_Axis
    //     0xbb57f8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb57fc: stur            x1, [fp, #-8]
    // 0xbb5800: StoreField: r1->field_f = r0
    //     0xbb5800: stur            w0, [x1, #0xf]
    // 0xbb5804: r0 = Instance_MainAxisAlignment
    //     0xbb5804: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb5808: ldr             x0, [x0, #0xa08]
    // 0xbb580c: StoreField: r1->field_13 = r0
    //     0xbb580c: stur            w0, [x1, #0x13]
    // 0xbb5810: r0 = Instance_MainAxisSize
    //     0xbb5810: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb5814: ldr             x0, [x0, #0xa10]
    // 0xbb5818: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb5818: stur            w0, [x1, #0x17]
    // 0xbb581c: r0 = Instance_CrossAxisAlignment
    //     0xbb581c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb5820: ldr             x0, [x0, #0xa18]
    // 0xbb5824: StoreField: r1->field_1b = r0
    //     0xbb5824: stur            w0, [x1, #0x1b]
    // 0xbb5828: r0 = Instance_VerticalDirection
    //     0xbb5828: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb582c: ldr             x0, [x0, #0xa20]
    // 0xbb5830: StoreField: r1->field_23 = r0
    //     0xbb5830: stur            w0, [x1, #0x23]
    // 0xbb5834: r0 = Instance_Clip
    //     0xbb5834: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb5838: ldr             x0, [x0, #0x38]
    // 0xbb583c: StoreField: r1->field_2b = r0
    //     0xbb583c: stur            w0, [x1, #0x2b]
    // 0xbb5840: StoreField: r1->field_2f = rZR
    //     0xbb5840: stur            xzr, [x1, #0x2f]
    // 0xbb5844: ldur            x0, [fp, #-0x18]
    // 0xbb5848: StoreField: r1->field_b = r0
    //     0xbb5848: stur            w0, [x1, #0xb]
    // 0xbb584c: r0 = EasyStep()
    //     0xbb584c: bl              #0xa08258  ; AllocateEasyStepStub -> EasyStep (size=0x20)
    // 0xbb5850: ldur            x1, [fp, #-0x10]
    // 0xbb5854: StoreField: r0->field_7 = r1
    //     0xbb5854: stur            w1, [x0, #7]
    // 0xbb5858: ldur            x1, [fp, #-8]
    // 0xbb585c: StoreField: r0->field_b = r1
    //     0xbb585c: stur            w1, [x0, #0xb]
    // 0xbb5860: r1 = false
    //     0xbb5860: add             x1, NULL, #0x30  ; false
    // 0xbb5864: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb5864: stur            w1, [x0, #0x17]
    // 0xbb5868: r1 = true
    //     0xbb5868: add             x1, NULL, #0x20  ; true
    // 0xbb586c: StoreField: r0->field_1b = r1
    //     0xbb586c: stur            w1, [x0, #0x1b]
    // 0xbb5870: LeaveFrame
    //     0xbb5870: mov             SP, fp
    //     0xbb5874: ldp             fp, lr, [SP], #0x10
    // 0xbb5878: ret
    //     0xbb5878: ret             
    // 0xbb587c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb587c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb5880: b               #0xbb5498
    // 0xbb5884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb5884: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb5888: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb5888: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb588c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb588c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb5890: r0 = RangeErrorSharedWithFPURegs()
    //     0xbb5890: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xbb5894: r0 = RangeErrorSharedWithFPURegs()
    //     0xbb5894: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xbb5898: SaveReg d0
    //     0xbb5898: str             q0, [SP, #-0x10]!
    // 0xbb589c: r0 = AllocateDouble()
    //     0xbb589c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbb58a0: RestoreReg d0
    //     0xbb58a0: ldr             q0, [SP], #0x10
    // 0xbb58a4: b               #0xbb5704
    // 0xbb58a8: SaveReg d0
    //     0xbb58a8: str             q0, [SP, #-0x10]!
    // 0xbb58ac: SaveReg r1
    //     0xbb58ac: str             x1, [SP, #-8]!
    // 0xbb58b0: r0 = AllocateDouble()
    //     0xbb58b0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbb58b4: RestoreReg r1
    //     0xbb58b4: ldr             x1, [SP], #8
    // 0xbb58b8: RestoreReg d0
    //     0xbb58b8: ldr             q0, [SP], #0x10
    // 0xbb58bc: b               #0xbb5790
  }
}

// class id: 4021, size: 0x18, field offset: 0xc
//   const constructor, 
class CheckoutBreadCrumb extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80218, size: 0x48
    // 0xc80218: EnterFrame
    //     0xc80218: stp             fp, lr, [SP, #-0x10]!
    //     0xc8021c: mov             fp, SP
    // 0xc80220: AllocStack(0x8)
    //     0xc80220: sub             SP, SP, #8
    // 0xc80224: CheckStackOverflow
    //     0xc80224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80228: cmp             SP, x16
    //     0xc8022c: b.ls            #0xc80258
    // 0xc80230: r1 = <CheckoutBreadCrumb>
    //     0xc80230: add             x1, PP, #0x48, lsl #12  ; [pp+0x486a8] TypeArguments: <CheckoutBreadCrumb>
    //     0xc80234: ldr             x1, [x1, #0x6a8]
    // 0xc80238: r0 = _CheckoutBreadCrumbState()
    //     0xc80238: bl              #0xc80260  ; Allocate_CheckoutBreadCrumbStateStub -> _CheckoutBreadCrumbState (size=0x1c)
    // 0xc8023c: mov             x1, x0
    // 0xc80240: stur            x0, [fp, #-8]
    // 0xc80244: r0 = _CheckoutBreadCrumbState()
    //     0xc80244: bl              #0xc7ac28  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_breadcrumb.dart] _CheckoutBreadCrumbState::_CheckoutBreadCrumbState
    // 0xc80248: ldur            x0, [fp, #-8]
    // 0xc8024c: LeaveFrame
    //     0xc8024c: mov             SP, fp
    //     0xc80250: ldp             fp, lr, [SP], #0x10
    // 0xc80254: ret
    //     0xc80254: ret             
    // 0xc80258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80258: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8025c: b               #0xc80230
  }
}
