// lib: , url: package:customer_app/app/presentation/custom_widgets/orders/post_order/order_detail_rating_review_widget.dart

// class id: 1049072, size: 0x8
class :: {
}

// class id: 3586, size: 0x14, field offset: 0x14
class _OrderDetailRatingReviewWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x997cc8, size: 0x3c0
    // 0x997cc8: EnterFrame
    //     0x997cc8: stp             fp, lr, [SP, #-0x10]!
    //     0x997ccc: mov             fp, SP
    // 0x997cd0: AllocStack(0x48)
    //     0x997cd0: sub             SP, SP, #0x48
    // 0x997cd4: SetupParameters(_OrderDetailRatingReviewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x997cd4: mov             x0, x1
    //     0x997cd8: stur            x1, [fp, #-8]
    //     0x997cdc: mov             x1, x2
    //     0x997ce0: stur            x2, [fp, #-0x10]
    // 0x997ce4: CheckStackOverflow
    //     0x997ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997ce8: cmp             SP, x16
    //     0x997cec: b.ls            #0x998078
    // 0x997cf0: r1 = 1
    //     0x997cf0: movz            x1, #0x1
    // 0x997cf4: r0 = AllocateContext()
    //     0x997cf4: bl              #0x16f6108  ; AllocateContextStub
    // 0x997cf8: mov             x3, x0
    // 0x997cfc: ldur            x0, [fp, #-8]
    // 0x997d00: stur            x3, [fp, #-0x18]
    // 0x997d04: StoreField: r3->field_f = r0
    //     0x997d04: stur            w0, [x3, #0xf]
    // 0x997d08: LoadField: r1 = r0->field_b
    //     0x997d08: ldur            w1, [x0, #0xb]
    // 0x997d0c: DecompressPointer r1
    //     0x997d0c: add             x1, x1, HEAP, lsl #32
    // 0x997d10: cmp             w1, NULL
    // 0x997d14: b.eq            #0x998080
    // 0x997d18: LoadField: d0 = r1->field_b
    //     0x997d18: ldur            d0, [x1, #0xb]
    // 0x997d1c: mov             x2, x3
    // 0x997d20: stur            d0, [fp, #-0x38]
    // 0x997d24: r1 = Function '<anonymous closure>':.
    //     0x997d24: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bf08] AnonymousClosure: (0x998324), in [package:customer_app/app/presentation/custom_widgets/orders/post_order/order_detail_rating_review_widget.dart] _OrderDetailRatingReviewWidgetState::build (0x997cc8)
    //     0x997d28: ldr             x1, [x1, #0xf08]
    // 0x997d2c: r0 = AllocateClosure()
    //     0x997d2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x997d30: stur            x0, [fp, #-0x20]
    // 0x997d34: r0 = RatingBar()
    //     0x997d34: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0x997d38: mov             x3, x0
    // 0x997d3c: ldur            x0, [fp, #-0x20]
    // 0x997d40: stur            x3, [fp, #-0x28]
    // 0x997d44: StoreField: r3->field_b = r0
    //     0x997d44: stur            w0, [x3, #0xb]
    // 0x997d48: r0 = false
    //     0x997d48: add             x0, NULL, #0x30  ; false
    // 0x997d4c: StoreField: r3->field_1f = r0
    //     0x997d4c: stur            w0, [x3, #0x1f]
    // 0x997d50: r4 = Instance_Axis
    //     0x997d50: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x997d54: StoreField: r3->field_23 = r4
    //     0x997d54: stur            w4, [x3, #0x23]
    // 0x997d58: r5 = true
    //     0x997d58: add             x5, NULL, #0x20  ; true
    // 0x997d5c: StoreField: r3->field_27 = r5
    //     0x997d5c: stur            w5, [x3, #0x27]
    // 0x997d60: d0 = 2.000000
    //     0x997d60: fmov            d0, #2.00000000
    // 0x997d64: StoreField: r3->field_2b = d0
    //     0x997d64: stur            d0, [x3, #0x2b]
    // 0x997d68: StoreField: r3->field_33 = r0
    //     0x997d68: stur            w0, [x3, #0x33]
    // 0x997d6c: ldur            d0, [fp, #-0x38]
    // 0x997d70: StoreField: r3->field_37 = d0
    //     0x997d70: stur            d0, [x3, #0x37]
    // 0x997d74: r1 = 5
    //     0x997d74: movz            x1, #0x5
    // 0x997d78: StoreField: r3->field_3f = r1
    //     0x997d78: stur            x1, [x3, #0x3f]
    // 0x997d7c: r1 = Instance_EdgeInsets
    //     0x997d7c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0x997d80: ldr             x1, [x1, #0xa68]
    // 0x997d84: StoreField: r3->field_47 = r1
    //     0x997d84: stur            w1, [x3, #0x47]
    // 0x997d88: d0 = 20.000000
    //     0x997d88: fmov            d0, #20.00000000
    // 0x997d8c: StoreField: r3->field_4b = d0
    //     0x997d8c: stur            d0, [x3, #0x4b]
    // 0x997d90: StoreField: r3->field_53 = rZR
    //     0x997d90: stur            xzr, [x3, #0x53]
    // 0x997d94: StoreField: r3->field_5b = r0
    //     0x997d94: stur            w0, [x3, #0x5b]
    // 0x997d98: StoreField: r3->field_5f = r0
    //     0x997d98: stur            w0, [x3, #0x5f]
    // 0x997d9c: ldur            x2, [fp, #-0x18]
    // 0x997da0: r1 = Function '<anonymous closure>':.
    //     0x997da0: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bf10] AnonymousClosure: (0x998134), in [package:customer_app/app/presentation/custom_widgets/orders/post_order/order_detail_rating_review_widget.dart] _OrderDetailRatingReviewWidgetState::build (0x997cc8)
    //     0x997da4: ldr             x1, [x1, #0xf10]
    // 0x997da8: r0 = AllocateClosure()
    //     0x997da8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x997dac: mov             x1, x0
    // 0x997db0: ldur            x0, [fp, #-0x28]
    // 0x997db4: StoreField: r0->field_63 = r1
    //     0x997db4: stur            w1, [x0, #0x63]
    // 0x997db8: ldur            x1, [fp, #-0x10]
    // 0x997dbc: r0 = of()
    //     0x997dbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x997dc0: LoadField: r1 = r0->field_87
    //     0x997dc0: ldur            w1, [x0, #0x87]
    // 0x997dc4: DecompressPointer r1
    //     0x997dc4: add             x1, x1, HEAP, lsl #32
    // 0x997dc8: LoadField: r0 = r1->field_2b
    //     0x997dc8: ldur            w0, [x1, #0x2b]
    // 0x997dcc: DecompressPointer r0
    //     0x997dcc: add             x0, x0, HEAP, lsl #32
    // 0x997dd0: stur            x0, [fp, #-0x20]
    // 0x997dd4: r1 = Instance_Color
    //     0x997dd4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x997dd8: d0 = 0.300000
    //     0x997dd8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x997ddc: ldr             d0, [x17, #0x658]
    // 0x997de0: r0 = withOpacity()
    //     0x997de0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x997de4: r16 = 12.000000
    //     0x997de4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x997de8: ldr             x16, [x16, #0x9e8]
    // 0x997dec: stp             x0, x16, [SP]
    // 0x997df0: ldur            x1, [fp, #-0x20]
    // 0x997df4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x997df4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x997df8: ldr             x4, [x4, #0xaa0]
    // 0x997dfc: r0 = copyWith()
    //     0x997dfc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x997e00: stur            x0, [fp, #-0x20]
    // 0x997e04: r0 = Text()
    //     0x997e04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x997e08: mov             x1, x0
    // 0x997e0c: r0 = "Rate this product"
    //     0x997e0c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fcb0] "Rate this product"
    //     0x997e10: ldr             x0, [x0, #0xcb0]
    // 0x997e14: stur            x1, [fp, #-0x30]
    // 0x997e18: StoreField: r1->field_b = r0
    //     0x997e18: stur            w0, [x1, #0xb]
    // 0x997e1c: ldur            x0, [fp, #-0x20]
    // 0x997e20: StoreField: r1->field_13 = r0
    //     0x997e20: stur            w0, [x1, #0x13]
    // 0x997e24: r0 = Padding()
    //     0x997e24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x997e28: mov             x3, x0
    // 0x997e2c: r0 = Instance_EdgeInsets
    //     0x997e2c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x997e30: ldr             x0, [x0, #0x668]
    // 0x997e34: stur            x3, [fp, #-0x20]
    // 0x997e38: StoreField: r3->field_f = r0
    //     0x997e38: stur            w0, [x3, #0xf]
    // 0x997e3c: ldur            x0, [fp, #-0x30]
    // 0x997e40: StoreField: r3->field_b = r0
    //     0x997e40: stur            w0, [x3, #0xb]
    // 0x997e44: r1 = Null
    //     0x997e44: mov             x1, NULL
    // 0x997e48: r2 = 4
    //     0x997e48: movz            x2, #0x4
    // 0x997e4c: r0 = AllocateArray()
    //     0x997e4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x997e50: mov             x2, x0
    // 0x997e54: ldur            x0, [fp, #-0x28]
    // 0x997e58: stur            x2, [fp, #-0x30]
    // 0x997e5c: StoreField: r2->field_f = r0
    //     0x997e5c: stur            w0, [x2, #0xf]
    // 0x997e60: ldur            x0, [fp, #-0x20]
    // 0x997e64: StoreField: r2->field_13 = r0
    //     0x997e64: stur            w0, [x2, #0x13]
    // 0x997e68: r1 = <Widget>
    //     0x997e68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x997e6c: r0 = AllocateGrowableArray()
    //     0x997e6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x997e70: mov             x1, x0
    // 0x997e74: ldur            x0, [fp, #-0x30]
    // 0x997e78: stur            x1, [fp, #-0x20]
    // 0x997e7c: StoreField: r1->field_f = r0
    //     0x997e7c: stur            w0, [x1, #0xf]
    // 0x997e80: r2 = 4
    //     0x997e80: movz            x2, #0x4
    // 0x997e84: StoreField: r1->field_b = r2
    //     0x997e84: stur            w2, [x1, #0xb]
    // 0x997e88: r0 = Column()
    //     0x997e88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x997e8c: mov             x2, x0
    // 0x997e90: r0 = Instance_Axis
    //     0x997e90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x997e94: stur            x2, [fp, #-0x28]
    // 0x997e98: StoreField: r2->field_f = r0
    //     0x997e98: stur            w0, [x2, #0xf]
    // 0x997e9c: r0 = Instance_MainAxisAlignment
    //     0x997e9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x997ea0: ldr             x0, [x0, #0xa08]
    // 0x997ea4: StoreField: r2->field_13 = r0
    //     0x997ea4: stur            w0, [x2, #0x13]
    // 0x997ea8: r0 = Instance_MainAxisSize
    //     0x997ea8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x997eac: ldr             x0, [x0, #0xa10]
    // 0x997eb0: ArrayStore: r2[0] = r0  ; List_4
    //     0x997eb0: stur            w0, [x2, #0x17]
    // 0x997eb4: r1 = Instance_CrossAxisAlignment
    //     0x997eb4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x997eb8: ldr             x1, [x1, #0x890]
    // 0x997ebc: StoreField: r2->field_1b = r1
    //     0x997ebc: stur            w1, [x2, #0x1b]
    // 0x997ec0: r3 = Instance_VerticalDirection
    //     0x997ec0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x997ec4: ldr             x3, [x3, #0xa20]
    // 0x997ec8: StoreField: r2->field_23 = r3
    //     0x997ec8: stur            w3, [x2, #0x23]
    // 0x997ecc: r4 = Instance_Clip
    //     0x997ecc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x997ed0: ldr             x4, [x4, #0x38]
    // 0x997ed4: StoreField: r2->field_2b = r4
    //     0x997ed4: stur            w4, [x2, #0x2b]
    // 0x997ed8: StoreField: r2->field_2f = rZR
    //     0x997ed8: stur            xzr, [x2, #0x2f]
    // 0x997edc: ldur            x1, [fp, #-0x20]
    // 0x997ee0: StoreField: r2->field_b = r1
    //     0x997ee0: stur            w1, [x2, #0xb]
    // 0x997ee4: ldur            x1, [fp, #-8]
    // 0x997ee8: LoadField: r5 = r1->field_b
    //     0x997ee8: ldur            w5, [x1, #0xb]
    // 0x997eec: DecompressPointer r5
    //     0x997eec: add             x5, x5, HEAP, lsl #32
    // 0x997ef0: cmp             w5, NULL
    // 0x997ef4: b.eq            #0x998084
    // 0x997ef8: LoadField: r6 = r5->field_1b
    //     0x997ef8: ldur            w6, [x5, #0x1b]
    // 0x997efc: DecompressPointer r6
    //     0x997efc: add             x6, x6, HEAP, lsl #32
    // 0x997f00: ldur            x1, [fp, #-0x10]
    // 0x997f04: stur            x6, [fp, #-8]
    // 0x997f08: r0 = of()
    //     0x997f08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x997f0c: LoadField: r1 = r0->field_87
    //     0x997f0c: ldur            w1, [x0, #0x87]
    // 0x997f10: DecompressPointer r1
    //     0x997f10: add             x1, x1, HEAP, lsl #32
    // 0x997f14: LoadField: r0 = r1->field_f
    //     0x997f14: ldur            w0, [x1, #0xf]
    // 0x997f18: DecompressPointer r0
    //     0x997f18: add             x0, x0, HEAP, lsl #32
    // 0x997f1c: cmp             w0, NULL
    // 0x997f20: b.ne            #0x997f2c
    // 0x997f24: r2 = Null
    //     0x997f24: mov             x2, NULL
    // 0x997f28: b               #0x997f54
    // 0x997f2c: r16 = 12.000000
    //     0x997f2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x997f30: ldr             x16, [x16, #0x9e8]
    // 0x997f34: r30 = Instance_Color
    //     0x997f34: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x997f38: ldr             lr, [lr, #0x858]
    // 0x997f3c: stp             lr, x16, [SP]
    // 0x997f40: mov             x1, x0
    // 0x997f44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x997f44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x997f48: ldr             x4, [x4, #0xaa0]
    // 0x997f4c: r0 = copyWith()
    //     0x997f4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x997f50: mov             x2, x0
    // 0x997f54: ldur            x0, [fp, #-0x28]
    // 0x997f58: ldur            x1, [fp, #-8]
    // 0x997f5c: stur            x2, [fp, #-0x10]
    // 0x997f60: r0 = Text()
    //     0x997f60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x997f64: mov             x1, x0
    // 0x997f68: ldur            x0, [fp, #-8]
    // 0x997f6c: stur            x1, [fp, #-0x20]
    // 0x997f70: StoreField: r1->field_b = r0
    //     0x997f70: stur            w0, [x1, #0xb]
    // 0x997f74: ldur            x0, [fp, #-0x10]
    // 0x997f78: StoreField: r1->field_13 = r0
    //     0x997f78: stur            w0, [x1, #0x13]
    // 0x997f7c: r0 = InkWell()
    //     0x997f7c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x997f80: mov             x3, x0
    // 0x997f84: ldur            x0, [fp, #-0x20]
    // 0x997f88: stur            x3, [fp, #-8]
    // 0x997f8c: StoreField: r3->field_b = r0
    //     0x997f8c: stur            w0, [x3, #0xb]
    // 0x997f90: ldur            x2, [fp, #-0x18]
    // 0x997f94: r1 = Function '<anonymous closure>':.
    //     0x997f94: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bf18] AnonymousClosure: (0x9980b8), in [package:customer_app/app/presentation/custom_widgets/orders/post_order/order_detail_rating_review_widget.dart] _OrderDetailRatingReviewWidgetState::build (0x997cc8)
    //     0x997f98: ldr             x1, [x1, #0xf18]
    // 0x997f9c: r0 = AllocateClosure()
    //     0x997f9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x997fa0: mov             x1, x0
    // 0x997fa4: ldur            x0, [fp, #-8]
    // 0x997fa8: StoreField: r0->field_f = r1
    //     0x997fa8: stur            w1, [x0, #0xf]
    // 0x997fac: r1 = true
    //     0x997fac: add             x1, NULL, #0x20  ; true
    // 0x997fb0: StoreField: r0->field_43 = r1
    //     0x997fb0: stur            w1, [x0, #0x43]
    // 0x997fb4: r2 = Instance_BoxShape
    //     0x997fb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x997fb8: ldr             x2, [x2, #0x80]
    // 0x997fbc: StoreField: r0->field_47 = r2
    //     0x997fbc: stur            w2, [x0, #0x47]
    // 0x997fc0: StoreField: r0->field_6f = r1
    //     0x997fc0: stur            w1, [x0, #0x6f]
    // 0x997fc4: r2 = false
    //     0x997fc4: add             x2, NULL, #0x30  ; false
    // 0x997fc8: StoreField: r0->field_73 = r2
    //     0x997fc8: stur            w2, [x0, #0x73]
    // 0x997fcc: StoreField: r0->field_83 = r1
    //     0x997fcc: stur            w1, [x0, #0x83]
    // 0x997fd0: StoreField: r0->field_7b = r2
    //     0x997fd0: stur            w2, [x0, #0x7b]
    // 0x997fd4: r1 = Null
    //     0x997fd4: mov             x1, NULL
    // 0x997fd8: r2 = 4
    //     0x997fd8: movz            x2, #0x4
    // 0x997fdc: r0 = AllocateArray()
    //     0x997fdc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x997fe0: mov             x2, x0
    // 0x997fe4: ldur            x0, [fp, #-0x28]
    // 0x997fe8: stur            x2, [fp, #-0x10]
    // 0x997fec: StoreField: r2->field_f = r0
    //     0x997fec: stur            w0, [x2, #0xf]
    // 0x997ff0: ldur            x0, [fp, #-8]
    // 0x997ff4: StoreField: r2->field_13 = r0
    //     0x997ff4: stur            w0, [x2, #0x13]
    // 0x997ff8: r1 = <Widget>
    //     0x997ff8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x997ffc: r0 = AllocateGrowableArray()
    //     0x997ffc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x998000: mov             x1, x0
    // 0x998004: ldur            x0, [fp, #-0x10]
    // 0x998008: stur            x1, [fp, #-8]
    // 0x99800c: StoreField: r1->field_f = r0
    //     0x99800c: stur            w0, [x1, #0xf]
    // 0x998010: r0 = 4
    //     0x998010: movz            x0, #0x4
    // 0x998014: StoreField: r1->field_b = r0
    //     0x998014: stur            w0, [x1, #0xb]
    // 0x998018: r0 = Row()
    //     0x998018: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x99801c: r1 = Instance_Axis
    //     0x99801c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x998020: StoreField: r0->field_f = r1
    //     0x998020: stur            w1, [x0, #0xf]
    // 0x998024: r1 = Instance_MainAxisAlignment
    //     0x998024: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x998028: ldr             x1, [x1, #0xa8]
    // 0x99802c: StoreField: r0->field_13 = r1
    //     0x99802c: stur            w1, [x0, #0x13]
    // 0x998030: r1 = Instance_MainAxisSize
    //     0x998030: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x998034: ldr             x1, [x1, #0xa10]
    // 0x998038: ArrayStore: r0[0] = r1  ; List_4
    //     0x998038: stur            w1, [x0, #0x17]
    // 0x99803c: r1 = Instance_CrossAxisAlignment
    //     0x99803c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x998040: ldr             x1, [x1, #0xa18]
    // 0x998044: StoreField: r0->field_1b = r1
    //     0x998044: stur            w1, [x0, #0x1b]
    // 0x998048: r1 = Instance_VerticalDirection
    //     0x998048: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x99804c: ldr             x1, [x1, #0xa20]
    // 0x998050: StoreField: r0->field_23 = r1
    //     0x998050: stur            w1, [x0, #0x23]
    // 0x998054: r1 = Instance_Clip
    //     0x998054: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x998058: ldr             x1, [x1, #0x38]
    // 0x99805c: StoreField: r0->field_2b = r1
    //     0x99805c: stur            w1, [x0, #0x2b]
    // 0x998060: StoreField: r0->field_2f = rZR
    //     0x998060: stur            xzr, [x0, #0x2f]
    // 0x998064: ldur            x1, [fp, #-8]
    // 0x998068: StoreField: r0->field_b = r1
    //     0x998068: stur            w1, [x0, #0xb]
    // 0x99806c: LeaveFrame
    //     0x99806c: mov             SP, fp
    //     0x998070: ldp             fp, lr, [SP], #0x10
    // 0x998074: ret
    //     0x998074: ret             
    // 0x998078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x998078: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99807c: b               #0x997cf0
    // 0x998080: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x998080: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x998084: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x998084: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9980b8, size: 0x7c
    // 0x9980b8: EnterFrame
    //     0x9980b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9980bc: mov             fp, SP
    // 0x9980c0: AllocStack(0x8)
    //     0x9980c0: sub             SP, SP, #8
    // 0x9980c4: SetupParameters()
    //     0x9980c4: ldr             x0, [fp, #0x10]
    //     0x9980c8: ldur            w1, [x0, #0x17]
    //     0x9980cc: add             x1, x1, HEAP, lsl #32
    // 0x9980d0: CheckStackOverflow
    //     0x9980d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9980d4: cmp             SP, x16
    //     0x9980d8: b.ls            #0x998128
    // 0x9980dc: LoadField: r0 = r1->field_f
    //     0x9980dc: ldur            w0, [x1, #0xf]
    // 0x9980e0: DecompressPointer r0
    //     0x9980e0: add             x0, x0, HEAP, lsl #32
    // 0x9980e4: LoadField: r1 = r0->field_b
    //     0x9980e4: ldur            w1, [x0, #0xb]
    // 0x9980e8: DecompressPointer r1
    //     0x9980e8: add             x1, x1, HEAP, lsl #32
    // 0x9980ec: cmp             w1, NULL
    // 0x9980f0: b.eq            #0x998130
    // 0x9980f4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9980f4: ldur            w0, [x1, #0x17]
    // 0x9980f8: DecompressPointer r0
    //     0x9980f8: add             x0, x0, HEAP, lsl #32
    // 0x9980fc: str             x0, [SP]
    // 0x998100: r4 = 0
    //     0x998100: movz            x4, #0
    // 0x998104: ldr             x0, [SP]
    // 0x998108: r16 = UnlinkedCall_0x613b5c
    //     0x998108: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bf20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x99810c: add             x16, x16, #0xf20
    // 0x998110: ldp             x5, lr, [x16]
    // 0x998114: blr             lr
    // 0x998118: r0 = Null
    //     0x998118: mov             x0, NULL
    // 0x99811c: LeaveFrame
    //     0x99811c: mov             SP, fp
    //     0x998120: ldp             fp, lr, [SP], #0x10
    // 0x998124: ret
    //     0x998124: ret             
    // 0x998128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x998128: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99812c: b               #0x9980dc
    // 0x998130: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x998130: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SvgPicture <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x998134, size: 0x1f0
    // 0x998134: EnterFrame
    //     0x998134: stp             fp, lr, [SP, #-0x10]!
    //     0x998138: mov             fp, SP
    // 0x99813c: AllocStack(0x8)
    //     0x99813c: sub             SP, SP, #8
    // 0x998140: SetupParameters()
    //     0x998140: fmov            d0, #1.00000000
    //     0x998144: ldr             x0, [fp, #0x20]
    //     0x998148: ldur            w1, [x0, #0x17]
    //     0x99814c: add             x1, x1, HEAP, lsl #32
    // 0x998140: d0 = 1.000000
    // 0x998150: CheckStackOverflow
    //     0x998150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x998154: cmp             SP, x16
    //     0x998158: b.ls            #0x9982f8
    // 0x99815c: LoadField: r0 = r1->field_f
    //     0x99815c: ldur            w0, [x1, #0xf]
    // 0x998160: DecompressPointer r0
    //     0x998160: add             x0, x0, HEAP, lsl #32
    // 0x998164: LoadField: r1 = r0->field_b
    //     0x998164: ldur            w1, [x0, #0xb]
    // 0x998168: DecompressPointer r1
    //     0x998168: add             x1, x1, HEAP, lsl #32
    // 0x99816c: cmp             w1, NULL
    // 0x998170: b.eq            #0x998300
    // 0x998174: LoadField: d1 = r1->field_b
    //     0x998174: ldur            d1, [x1, #0xb]
    // 0x998178: fsub            d2, d1, d0
    // 0x99817c: ldr             x0, [fp, #0x10]
    // 0x998180: r1 = LoadInt32Instr(r0)
    //     0x998180: sbfx            x1, x0, #1, #0x1f
    //     0x998184: tbz             w0, #0, #0x99818c
    //     0x998188: ldur            x1, [x0, #7]
    // 0x99818c: scvtf           d0, x1
    // 0x998190: fcmp            d0, d2
    // 0x998194: b.ne            #0x9982cc
    // 0x998198: fcmp            d1, d1
    // 0x99819c: b.vs            #0x998304
    // 0x9981a0: fcvtzs          x0, d1
    // 0x9981a4: asr             x16, x0, #0x1e
    // 0x9981a8: cmp             x16, x0, asr #63
    // 0x9981ac: b.ne            #0x998304
    // 0x9981b0: lsl             x0, x0, #1
    // 0x9981b4: r1 = LoadInt32Instr(r0)
    //     0x9981b4: sbfx            x1, x0, #1, #0x1f
    //     0x9981b8: tbz             w0, #0, #0x9981c0
    //     0x9981bc: ldur            x1, [x0, #7]
    // 0x9981c0: cmp             x1, #3
    // 0x9981c4: b.gt            #0x998264
    // 0x9981c8: cmp             x1, #2
    // 0x9981cc: b.gt            #0x998238
    // 0x9981d0: cmp             x1, #1
    // 0x9981d4: b.gt            #0x99820c
    // 0x9981d8: cmp             w0, #2
    // 0x9981dc: b.ne            #0x9982cc
    // 0x9981e0: r0 = SvgPicture()
    //     0x9981e0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9981e4: mov             x1, x0
    // 0x9981e8: r2 = "assets/images/star1.svg"
    //     0x9981e8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fe50] "assets/images/star1.svg"
    //     0x9981ec: ldr             x2, [x2, #0xe50]
    // 0x9981f0: stur            x0, [fp, #-8]
    // 0x9981f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9981f4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9981f8: r0 = SvgPicture.asset()
    //     0x9981f8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9981fc: ldur            x0, [fp, #-8]
    // 0x998200: LeaveFrame
    //     0x998200: mov             SP, fp
    //     0x998204: ldp             fp, lr, [SP], #0x10
    // 0x998208: ret
    //     0x998208: ret             
    // 0x99820c: r0 = SvgPicture()
    //     0x99820c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x998210: mov             x1, x0
    // 0x998214: r2 = "assets/images/star2.svg"
    //     0x998214: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fe58] "assets/images/star2.svg"
    //     0x998218: ldr             x2, [x2, #0xe58]
    // 0x99821c: stur            x0, [fp, #-8]
    // 0x998220: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x998220: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x998224: r0 = SvgPicture.asset()
    //     0x998224: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x998228: ldur            x0, [fp, #-8]
    // 0x99822c: LeaveFrame
    //     0x99822c: mov             SP, fp
    //     0x998230: ldp             fp, lr, [SP], #0x10
    // 0x998234: ret
    //     0x998234: ret             
    // 0x998238: r0 = SvgPicture()
    //     0x998238: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x99823c: mov             x1, x0
    // 0x998240: r2 = "assets/images/star3.svg"
    //     0x998240: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fe60] "assets/images/star3.svg"
    //     0x998244: ldr             x2, [x2, #0xe60]
    // 0x998248: stur            x0, [fp, #-8]
    // 0x99824c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x99824c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x998250: r0 = SvgPicture.asset()
    //     0x998250: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x998254: ldur            x0, [fp, #-8]
    // 0x998258: LeaveFrame
    //     0x998258: mov             SP, fp
    //     0x99825c: ldp             fp, lr, [SP], #0x10
    // 0x998260: ret
    //     0x998260: ret             
    // 0x998264: cmp             x1, #4
    // 0x998268: b.gt            #0x998298
    // 0x99826c: r0 = SvgPicture()
    //     0x99826c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x998270: mov             x1, x0
    // 0x998274: r2 = "assets/images/star4.svg"
    //     0x998274: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fe68] "assets/images/star4.svg"
    //     0x998278: ldr             x2, [x2, #0xe68]
    // 0x99827c: stur            x0, [fp, #-8]
    // 0x998280: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x998280: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x998284: r0 = SvgPicture.asset()
    //     0x998284: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x998288: ldur            x0, [fp, #-8]
    // 0x99828c: LeaveFrame
    //     0x99828c: mov             SP, fp
    //     0x998290: ldp             fp, lr, [SP], #0x10
    // 0x998294: ret
    //     0x998294: ret             
    // 0x998298: cmp             w0, #0xa
    // 0x99829c: b.ne            #0x9982cc
    // 0x9982a0: r0 = SvgPicture()
    //     0x9982a0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9982a4: mov             x1, x0
    // 0x9982a8: r2 = "assets/images/star5.svg"
    //     0x9982a8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fe70] "assets/images/star5.svg"
    //     0x9982ac: ldr             x2, [x2, #0xe70]
    // 0x9982b0: stur            x0, [fp, #-8]
    // 0x9982b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9982b4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9982b8: r0 = SvgPicture.asset()
    //     0x9982b8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9982bc: ldur            x0, [fp, #-8]
    // 0x9982c0: LeaveFrame
    //     0x9982c0: mov             SP, fp
    //     0x9982c4: ldp             fp, lr, [SP], #0x10
    // 0x9982c8: ret
    //     0x9982c8: ret             
    // 0x9982cc: r0 = SvgPicture()
    //     0x9982cc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9982d0: mov             x1, x0
    // 0x9982d4: r2 = "assets/images/ratedStar.svg"
    //     0x9982d4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36bc0] "assets/images/ratedStar.svg"
    //     0x9982d8: ldr             x2, [x2, #0xbc0]
    // 0x9982dc: stur            x0, [fp, #-8]
    // 0x9982e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9982e0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9982e4: r0 = SvgPicture.asset()
    //     0x9982e4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9982e8: ldur            x0, [fp, #-8]
    // 0x9982ec: LeaveFrame
    //     0x9982ec: mov             SP, fp
    //     0x9982f0: ldp             fp, lr, [SP], #0x10
    // 0x9982f4: ret
    //     0x9982f4: ret             
    // 0x9982f8: r0 = StackOverflowSharedWithFPURegs()
    //     0x9982f8: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0x9982fc: b               #0x99815c
    // 0x998300: r0 = NullCastErrorSharedWithFPURegs()
    //     0x998300: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0x998304: SaveReg d1
    //     0x998304: str             q1, [SP, #-0x10]!
    // 0x998308: d0 = 0.000000
    //     0x998308: fmov            d0, d1
    // 0x99830c: r0 = 74
    //     0x99830c: movz            x0, #0x4a
    // 0x998310: r30 = DoubleToIntegerStub
    //     0x998310: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0x998314: LoadField: r30 = r30->field_7
    //     0x998314: ldur            lr, [lr, #7]
    // 0x998318: blr             lr
    // 0x99831c: RestoreReg d1
    //     0x99831c: ldr             q1, [SP], #0x10
    // 0x998320: b               #0x9981b4
  }
  [closure] void <anonymous closure>(dynamic, double) {
    // ** addr: 0x998324, size: 0x80
    // 0x998324: EnterFrame
    //     0x998324: stp             fp, lr, [SP, #-0x10]!
    //     0x998328: mov             fp, SP
    // 0x99832c: AllocStack(0x10)
    //     0x99832c: sub             SP, SP, #0x10
    // 0x998330: SetupParameters()
    //     0x998330: ldr             x0, [fp, #0x18]
    //     0x998334: ldur            w1, [x0, #0x17]
    //     0x998338: add             x1, x1, HEAP, lsl #32
    // 0x99833c: CheckStackOverflow
    //     0x99833c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x998340: cmp             SP, x16
    //     0x998344: b.ls            #0x998398
    // 0x998348: LoadField: r0 = r1->field_f
    //     0x998348: ldur            w0, [x1, #0xf]
    // 0x99834c: DecompressPointer r0
    //     0x99834c: add             x0, x0, HEAP, lsl #32
    // 0x998350: LoadField: r1 = r0->field_b
    //     0x998350: ldur            w1, [x0, #0xb]
    // 0x998354: DecompressPointer r1
    //     0x998354: add             x1, x1, HEAP, lsl #32
    // 0x998358: cmp             w1, NULL
    // 0x99835c: b.eq            #0x9983a0
    // 0x998360: LoadField: r0 = r1->field_13
    //     0x998360: ldur            w0, [x1, #0x13]
    // 0x998364: DecompressPointer r0
    //     0x998364: add             x0, x0, HEAP, lsl #32
    // 0x998368: ldr             x16, [fp, #0x10]
    // 0x99836c: stp             x16, x0, [SP]
    // 0x998370: r4 = 0
    //     0x998370: movz            x4, #0
    // 0x998374: ldr             x0, [SP, #8]
    // 0x998378: r16 = UnlinkedCall_0x613b5c
    //     0x998378: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bf30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x99837c: add             x16, x16, #0xf30
    // 0x998380: ldp             x5, lr, [x16]
    // 0x998384: blr             lr
    // 0x998388: r0 = Null
    //     0x998388: mov             x0, NULL
    // 0x99838c: LeaveFrame
    //     0x99838c: mov             SP, fp
    //     0x998390: ldp             fp, lr, [SP], #0x10
    // 0x998394: ret
    //     0x998394: ret             
    // 0x998398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x998398: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99839c: b               #0x998348
    // 0x9983a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9983a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4313, size: 0x20, field offset: 0xc
//   const constructor, 
class OrderDetailRatingReviewWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79b90, size: 0x24
    // 0xc79b90: EnterFrame
    //     0xc79b90: stp             fp, lr, [SP, #-0x10]!
    //     0xc79b94: mov             fp, SP
    // 0xc79b98: mov             x0, x1
    // 0xc79b9c: r1 = <OrderDetailRatingReviewWidget>
    //     0xc79b9c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49360] TypeArguments: <OrderDetailRatingReviewWidget>
    //     0xc79ba0: ldr             x1, [x1, #0x360]
    // 0xc79ba4: r0 = _OrderDetailRatingReviewWidgetState()
    //     0xc79ba4: bl              #0xc79bb4  ; Allocate_OrderDetailRatingReviewWidgetStateStub -> _OrderDetailRatingReviewWidgetState (size=0x14)
    // 0xc79ba8: LeaveFrame
    //     0xc79ba8: mov             SP, fp
    //     0xc79bac: ldp             fp, lr, [SP], #0x10
    // 0xc79bb0: ret
    //     0xc79bb0: ret             
  }
  const _ OrderDetailRatingReviewWidget(/* No info */) {
    // ** addr: 0x142056c, size: 0x84
    // 0x142056c: EnterFrame
    //     0x142056c: stp             fp, lr, [SP, #-0x10]!
    //     0x1420570: mov             fp, SP
    // 0x1420574: mov             x0, x3
    // 0x1420578: mov             x3, x1
    // 0x142057c: mov             x1, x5
    // 0x1420580: StoreField: r3->field_b = d0
    //     0x1420580: stur            d0, [x3, #0xb]
    // 0x1420584: StoreField: r3->field_13 = r0
    //     0x1420584: stur            w0, [x3, #0x13]
    //     0x1420588: ldurb           w16, [x3, #-1]
    //     0x142058c: ldurb           w17, [x0, #-1]
    //     0x1420590: and             x16, x17, x16, lsr #2
    //     0x1420594: tst             x16, HEAP, lsr #32
    //     0x1420598: b.eq            #0x14205a0
    //     0x142059c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14205a0: mov             x0, x2
    // 0x14205a4: ArrayStore: r3[0] = r0  ; List_4
    //     0x14205a4: stur            w0, [x3, #0x17]
    //     0x14205a8: ldurb           w16, [x3, #-1]
    //     0x14205ac: ldurb           w17, [x0, #-1]
    //     0x14205b0: and             x16, x17, x16, lsr #2
    //     0x14205b4: tst             x16, HEAP, lsr #32
    //     0x14205b8: b.eq            #0x14205c0
    //     0x14205bc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14205c0: mov             x0, x1
    // 0x14205c4: StoreField: r3->field_1b = r0
    //     0x14205c4: stur            w0, [x3, #0x1b]
    //     0x14205c8: ldurb           w16, [x3, #-1]
    //     0x14205cc: ldurb           w17, [x0, #-1]
    //     0x14205d0: and             x16, x17, x16, lsr #2
    //     0x14205d4: tst             x16, HEAP, lsr #32
    //     0x14205d8: b.eq            #0x14205e0
    //     0x14205dc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14205e0: r0 = Null
    //     0x14205e0: mov             x0, NULL
    // 0x14205e4: LeaveFrame
    //     0x14205e4: mov             SP, fp
    //     0x14205e8: ldp             fp, lr, [SP], #0x10
    // 0x14205ec: ret
    //     0x14205ec: ret             
  }
}
