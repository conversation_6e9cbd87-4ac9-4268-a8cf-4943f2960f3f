// lib: , url: package:dio/src/progress_stream/io_progress_stream.dart

// class id: 1049611, size: 0x8
class :: {

  static _ addProgress(/* No info */) {
    // ** addr: 0x881f28, size: 0x10c
    // 0x881f28: EnterFrame
    //     0x881f28: stp             fp, lr, [SP, #-0x10]!
    //     0x881f2c: mov             fp, SP
    // 0x881f30: AllocStack(0x30)
    //     0x881f30: sub             SP, SP, #0x30
    // 0x881f34: SetupParameters(dynamic _ /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x881f34: mov             x5, x1
    //     0x881f38: mov             x4, x2
    //     0x881f3c: stur            x1, [fp, #-8]
    //     0x881f40: stur            x2, [fp, #-0x10]
    //     0x881f44: stur            x3, [fp, #-0x18]
    // 0x881f48: CheckStackOverflow
    //     0x881f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x881f4c: cmp             SP, x16
    //     0x881f50: b.ls            #0x88202c
    // 0x881f54: mov             x0, x5
    // 0x881f58: r2 = Null
    //     0x881f58: mov             x2, NULL
    // 0x881f5c: r1 = Null
    //     0x881f5c: mov             x1, NULL
    // 0x881f60: cmp             w0, NULL
    // 0x881f64: b.eq            #0x881fb0
    // 0x881f68: branchIfSmi(r0, 0x881fb0)
    //     0x881f68: tbz             w0, #0, #0x881fb0
    // 0x881f6c: r3 = SubtypeTestCache
    //     0x881f6c: add             x3, PP, #0xa, lsl #12  ; [pp+0xa568] SubtypeTestCache
    //     0x881f70: ldr             x3, [x3, #0x568]
    // 0x881f74: r30 = Subtype2TestCacheStub
    //     0x881f74: ldr             lr, [PP, #0x30]  ; [pp+0x30] Stub: Subtype2TestCache (0x612da8)
    // 0x881f78: LoadField: r30 = r30->field_7
    //     0x881f78: ldur            lr, [lr, #7]
    // 0x881f7c: blr             lr
    // 0x881f80: cmp             w7, NULL
    // 0x881f84: b.eq            #0x881f90
    // 0x881f88: tbnz            w7, #4, #0x881fb0
    // 0x881f8c: b               #0x881fb8
    // 0x881f90: r8 = Stream<Uint8List>
    //     0x881f90: add             x8, PP, #0xa, lsl #12  ; [pp+0xa570] Type: Stream<Uint8List>
    //     0x881f94: ldr             x8, [x8, #0x570]
    // 0x881f98: r3 = SubtypeTestCache
    //     0x881f98: add             x3, PP, #0xa, lsl #12  ; [pp+0xa578] SubtypeTestCache
    //     0x881f9c: ldr             x3, [x3, #0x578]
    // 0x881fa0: r30 = InstanceOfStub
    //     0x881fa0: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x881fa4: LoadField: r30 = r30->field_7
    //     0x881fa4: ldur            lr, [lr, #7]
    // 0x881fa8: blr             lr
    // 0x881fac: b               #0x881fbc
    // 0x881fb0: r0 = false
    //     0x881fb0: add             x0, NULL, #0x30  ; false
    // 0x881fb4: b               #0x881fbc
    // 0x881fb8: r0 = true
    //     0x881fb8: add             x0, NULL, #0x20  ; true
    // 0x881fbc: tbnz            w0, #4, #0x881fe4
    // 0x881fc0: r16 = <Uint8List>
    //     0x881fc0: add             x16, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x881fc4: ldr             x16, [x16, #0x598]
    // 0x881fc8: ldur            lr, [fp, #-0x10]
    // 0x881fcc: stp             lr, x16, [SP, #8]
    // 0x881fd0: ldur            x16, [fp, #-0x18]
    // 0x881fd4: str             x16, [SP]
    // 0x881fd8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x881fd8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x881fdc: r0 = _transform()
    //     0x881fdc: bl              #0x882034  ; [package:dio/src/progress_stream/io_progress_stream.dart] ::_transform
    // 0x881fe0: b               #0x882004
    // 0x881fe4: r16 = <List<int>>
    //     0x881fe4: add             x16, PP, #9, lsl #12  ; [pp+0x90f8] TypeArguments: <List<int>>
    //     0x881fe8: ldr             x16, [x16, #0xf8]
    // 0x881fec: ldur            lr, [fp, #-0x10]
    // 0x881ff0: stp             lr, x16, [SP, #8]
    // 0x881ff4: ldur            x16, [fp, #-0x18]
    // 0x881ff8: str             x16, [SP]
    // 0x881ffc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x881ffc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x882000: r0 = _transform()
    //     0x882000: bl              #0x882034  ; [package:dio/src/progress_stream/io_progress_stream.dart] ::_transform
    // 0x882004: r16 = <Uint8List>
    //     0x882004: add             x16, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x882008: ldr             x16, [x16, #0x598]
    // 0x88200c: ldur            lr, [fp, #-8]
    // 0x882010: stp             lr, x16, [SP, #8]
    // 0x882014: str             x0, [SP]
    // 0x882018: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x882018: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88201c: r0 = transform()
    //     0x88201c: bl              #0x864bd0  ; [dart:async] Stream::transform
    // 0x882020: LeaveFrame
    //     0x882020: mov             SP, fp
    //     0x882024: ldp             fp, lr, [SP], #0x10
    // 0x882028: ret
    //     0x882028: ret             
    // 0x88202c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88202c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x882030: b               #0x881f54
  }
  static StreamTransformer<Y0, Uint8List> _transform<Y0 extends List<int>>(int?, RequestOptions) {
    // ** addr: 0x882034, size: 0x104
    // 0x882034: EnterFrame
    //     0x882034: stp             fp, lr, [SP, #-0x10]!
    //     0x882038: mov             fp, SP
    // 0x88203c: AllocStack(0x18)
    //     0x88203c: sub             SP, SP, #0x18
    // 0x882040: SetupParameters()
    //     0x882040: ldur            w0, [x4, #0xf]
    //     0x882044: stur            x0, [fp, #-0x10]
    //     0x882048: cbnz            w0, #0x882054
    //     0x88204c: mov             x3, NULL
    //     0x882050: b               #0x882064
    //     0x882054: ldur            w1, [x4, #0x17]
    //     0x882058: add             x2, fp, w1, sxtw #2
    //     0x88205c: ldr             x2, [x2, #0x10]
    //     0x882060: mov             x3, x2
    // 0x882064: ldr             x2, [fp, #0x18]
    // 0x882068: ldr             x1, [fp, #0x10]
    // 0x88206c: stur            x3, [fp, #-8]
    // 0x882070: r1 = 3
    //     0x882070: movz            x1, #0x3
    // 0x882074: r0 = AllocateContext()
    //     0x882074: bl              #0x16f6108  ; AllocateContextStub
    // 0x882078: mov             x1, x0
    // 0x88207c: ldr             x0, [fp, #0x18]
    // 0x882080: StoreField: r1->field_f = r0
    //     0x882080: stur            w0, [x1, #0xf]
    // 0x882084: ldr             x0, [fp, #0x10]
    // 0x882088: StoreField: r1->field_13 = r0
    //     0x882088: stur            w0, [x1, #0x13]
    // 0x88208c: ldur            x0, [fp, #-0x10]
    // 0x882090: cbnz            w0, #0x8820a0
    // 0x882094: r0 = <List<int>>
    //     0x882094: add             x0, PP, #9, lsl #12  ; [pp+0x90f8] TypeArguments: <List<int>>
    //     0x882098: ldr             x0, [x0, #0xf8]
    // 0x88209c: b               #0x8820a4
    // 0x8820a0: ldur            x0, [fp, #-8]
    // 0x8820a4: stur            x0, [fp, #-8]
    // 0x8820a8: ArrayStore: r1[0] = rZR  ; List_4
    //     0x8820a8: stur            wzr, [x1, #0x17]
    // 0x8820ac: mov             x2, x1
    // 0x8820b0: r1 = Function '<anonymous closure>': static.
    //     0x8820b0: add             x1, PP, #0xa, lsl #12  ; [pp+0xa580] AnonymousClosure: static (0x8821a8), in [package:dio/src/progress_stream/io_progress_stream.dart] ::_transform (0x882034)
    //     0x8820b4: ldr             x1, [x1, #0x580]
    // 0x8820b8: r0 = AllocateClosure()
    //     0x8820b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8820bc: ldur            x1, [fp, #-8]
    // 0x8820c0: stur            x0, [fp, #-0x10]
    // 0x8820c4: StoreField: r0->field_b = r1
    //     0x8820c4: stur            w1, [x0, #0xb]
    // 0x8820c8: r2 = Null
    //     0x8820c8: mov             x2, NULL
    // 0x8820cc: r3 = <Y0 bound List, Uint8List>
    //     0x8820cc: add             x3, PP, #0xa, lsl #12  ; [pp+0xa588] TypeArguments: <Y0 bound List, Uint8List>
    //     0x8820d0: ldr             x3, [x3, #0x588]
    // 0x8820d4: r30 = InstantiateTypeArgumentsStub
    //     0x8820d4: ldr             lr, [PP, #0x208]  ; [pp+0x208] Stub: InstantiateTypeArguments (0x600f4c)
    // 0x8820d8: LoadField: r30 = r30->field_7
    //     0x8820d8: ldur            lr, [lr, #7]
    // 0x8820dc: blr             lr
    // 0x8820e0: mov             x1, x0
    // 0x8820e4: stur            x0, [fp, #-8]
    // 0x8820e8: r0 = _StreamHandlerTransformer()
    //     0x8820e8: bl              #0x882138  ; Allocate_StreamHandlerTransformerStub -> _StreamHandlerTransformer<X0, X1> (size=0x10)
    // 0x8820ec: stur            x0, [fp, #-0x18]
    // 0x8820f0: r1 = 2
    //     0x8820f0: movz            x1, #0x2
    // 0x8820f4: r0 = AllocateContext()
    //     0x8820f4: bl              #0x16f6108  ; AllocateContextStub
    // 0x8820f8: mov             x1, x0
    // 0x8820fc: ldur            x0, [fp, #-0x18]
    // 0x882100: StoreField: r1->field_f = r0
    //     0x882100: stur            w0, [x1, #0xf]
    // 0x882104: ldur            x2, [fp, #-0x10]
    // 0x882108: StoreField: r1->field_13 = r2
    //     0x882108: stur            w2, [x1, #0x13]
    // 0x88210c: mov             x2, x1
    // 0x882110: ldur            x3, [fp, #-8]
    // 0x882114: r1 = Function '<anonymous closure>':.
    //     0x882114: add             x1, PP, #0xa, lsl #12  ; [pp+0xa590] AnonymousClosure: (0x882144), of [dart:async] _StreamHandlerTransformer<X0, X1>
    //     0x882118: ldr             x1, [x1, #0x590]
    // 0x88211c: r0 = AllocateClosureTA()
    //     0x88211c: bl              #0x16f6310  ; AllocateClosureTAStub
    // 0x882120: mov             x1, x0
    // 0x882124: ldur            x0, [fp, #-0x18]
    // 0x882128: StoreField: r0->field_b = r1
    //     0x882128: stur            w1, [x0, #0xb]
    // 0x88212c: LeaveFrame
    //     0x88212c: mov             SP, fp
    //     0x882130: ldp             fp, lr, [SP], #0x10
    // 0x882134: ret
    //     0x882134: ret             
  }
  [closure] static void <anonymous closure>(dynamic, Y0, EventSink<Uint8List>) {
    // ** addr: 0x8821a8, size: 0x204
    // 0x8821a8: EnterFrame
    //     0x8821a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8821ac: mov             fp, SP
    // 0x8821b0: AllocStack(0x18)
    //     0x8821b0: sub             SP, SP, #0x18
    // 0x8821b4: SetupParameters()
    //     0x8821b4: ldr             x0, [fp, #0x20]
    //     0x8821b8: ldur            w3, [x0, #0x17]
    //     0x8821bc: add             x3, x3, HEAP, lsl #32
    //     0x8821c0: stur            x3, [fp, #-8]
    // 0x8821c4: CheckStackOverflow
    //     0x8821c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8821c8: cmp             SP, x16
    //     0x8821cc: b.ls            #0x8823a4
    // 0x8821d0: LoadField: r0 = r3->field_13
    //     0x8821d0: ldur            w0, [x3, #0x13]
    // 0x8821d4: DecompressPointer r0
    //     0x8821d4: add             x0, x0, HEAP, lsl #32
    // 0x8821d8: LoadField: r1 = r0->field_5f
    //     0x8821d8: ldur            w1, [x0, #0x5f]
    // 0x8821dc: DecompressPointer r1
    //     0x8821dc: add             x1, x1, HEAP, lsl #32
    // 0x8821e0: cmp             w1, NULL
    // 0x8821e4: b.eq            #0x882274
    // 0x8821e8: LoadField: r2 = r1->field_b
    //     0x8821e8: ldur            w2, [x1, #0xb]
    // 0x8821ec: DecompressPointer r2
    //     0x8821ec: add             x2, x2, HEAP, lsl #32
    // 0x8821f0: cmp             w2, NULL
    // 0x8821f4: b.eq            #0x88226c
    // 0x8821f8: ldr             x3, [fp, #0x10]
    // 0x8821fc: StoreField: r1->field_f = r0
    //     0x8821fc: stur            w0, [x1, #0xf]
    //     0x882200: ldurb           w16, [x1, #-1]
    //     0x882204: ldurb           w17, [x0, #-1]
    //     0x882208: and             x16, x17, x16, lsr #2
    //     0x88220c: tst             x16, HEAP, lsr #32
    //     0x882210: b.eq            #0x882218
    //     0x882214: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x882218: r0 = LoadClassIdInstr(r3)
    //     0x882218: ldur            x0, [x3, #-1]
    //     0x88221c: ubfx            x0, x0, #0xc, #0x14
    // 0x882220: mov             x1, x3
    // 0x882224: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x882224: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x882228: r0 = GDT[cid_x0 + 0x11295]()
    //     0x882228: movz            x17, #0x1295
    //     0x88222c: movk            x17, #0x1, lsl #16
    //     0x882230: add             lr, x0, x17
    //     0x882234: ldr             lr, [x21, lr, lsl #3]
    //     0x882238: blr             lr
    // 0x88223c: ldr             x0, [fp, #0x10]
    // 0x882240: r1 = LoadClassIdInstr(r0)
    //     0x882240: ldur            x1, [x0, #-1]
    //     0x882244: ubfx            x1, x1, #0xc, #0x14
    // 0x882248: mov             x16, x0
    // 0x88224c: mov             x0, x1
    // 0x882250: mov             x1, x16
    // 0x882254: r0 = GDT[cid_x0 + 0x10f21]()
    //     0x882254: movz            x17, #0xf21
    //     0x882258: movk            x17, #0x1, lsl #16
    //     0x88225c: add             lr, x0, x17
    //     0x882260: ldr             lr, [x21, lr, lsl #3]
    //     0x882264: blr             lr
    // 0x882268: b               #0x882394
    // 0x88226c: ldr             x0, [fp, #0x10]
    // 0x882270: b               #0x882278
    // 0x882274: ldr             x0, [fp, #0x10]
    // 0x882278: ldr             x4, [fp, #0x18]
    // 0x88227c: r1 = LoadClassIdInstr(r4)
    //     0x88227c: ldur            x1, [x4, #-1]
    //     0x882280: ubfx            x1, x1, #0xc, #0x14
    // 0x882284: sub             x16, x1, #0x74
    // 0x882288: cmp             x16, #3
    // 0x88228c: b.hi            #0x8822c0
    // 0x882290: r1 = LoadClassIdInstr(r0)
    //     0x882290: ldur            x1, [x0, #-1]
    //     0x882294: ubfx            x1, x1, #0xc, #0x14
    // 0x882298: mov             x16, x0
    // 0x88229c: mov             x0, x1
    // 0x8822a0: mov             x1, x16
    // 0x8822a4: mov             x2, x4
    // 0x8822a8: r0 = GDT[cid_x0 + 0x11a81]()
    //     0x8822a8: movz            x17, #0x1a81
    //     0x8822ac: movk            x17, #0x1, lsl #16
    //     0x8822b0: add             lr, x0, x17
    //     0x8822b4: ldr             lr, [x21, lr, lsl #3]
    //     0x8822b8: blr             lr
    // 0x8822bc: b               #0x8822f8
    // 0x8822c0: ldr             x2, [fp, #0x18]
    // 0x8822c4: r1 = Null
    //     0x8822c4: mov             x1, NULL
    // 0x8822c8: r0 = Uint8List.fromList()
    //     0x8822c8: bl              #0x6568d8  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0x8822cc: ldr             x1, [fp, #0x10]
    // 0x8822d0: r2 = LoadClassIdInstr(r1)
    //     0x8822d0: ldur            x2, [x1, #-1]
    //     0x8822d4: ubfx            x2, x2, #0xc, #0x14
    // 0x8822d8: mov             x16, x0
    // 0x8822dc: mov             x0, x2
    // 0x8822e0: mov             x2, x16
    // 0x8822e4: r0 = GDT[cid_x0 + 0x11a81]()
    //     0x8822e4: movz            x17, #0x1a81
    //     0x8822e8: movk            x17, #0x1, lsl #16
    //     0x8822ec: add             lr, x0, x17
    //     0x8822f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8822f4: blr             lr
    // 0x8822f8: ldur            x1, [fp, #-8]
    // 0x8822fc: LoadField: r0 = r1->field_f
    //     0x8822fc: ldur            w0, [x1, #0xf]
    // 0x882300: DecompressPointer r0
    //     0x882300: add             x0, x0, HEAP, lsl #32
    // 0x882304: cmp             w0, NULL
    // 0x882308: b.eq            #0x882394
    // 0x88230c: ldr             x0, [fp, #0x18]
    // 0x882310: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x882310: ldur            w2, [x1, #0x17]
    // 0x882314: DecompressPointer r2
    //     0x882314: add             x2, x2, HEAP, lsl #32
    // 0x882318: stur            x2, [fp, #-0x10]
    // 0x88231c: r3 = LoadClassIdInstr(r0)
    //     0x88231c: ldur            x3, [x0, #-1]
    //     0x882320: ubfx            x3, x3, #0xc, #0x14
    // 0x882324: str             x0, [SP]
    // 0x882328: mov             x0, x3
    // 0x88232c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x88232c: movz            x17, #0xc898
    //     0x882330: add             lr, x0, x17
    //     0x882334: ldr             lr, [x21, lr, lsl #3]
    //     0x882338: blr             lr
    // 0x88233c: ldur            x2, [fp, #-0x10]
    // 0x882340: r3 = LoadInt32Instr(r2)
    //     0x882340: sbfx            x3, x2, #1, #0x1f
    //     0x882344: tbz             w2, #0, #0x88234c
    //     0x882348: ldur            x3, [x2, #7]
    // 0x88234c: r2 = LoadInt32Instr(r0)
    //     0x88234c: sbfx            x2, x0, #1, #0x1f
    //     0x882350: tbz             w0, #0, #0x882358
    //     0x882354: ldur            x2, [x0, #7]
    // 0x882358: add             x4, x3, x2
    // 0x88235c: r0 = BoxInt64Instr(r4)
    //     0x88235c: sbfiz           x0, x4, #1, #0x1f
    //     0x882360: cmp             x4, x0, asr #1
    //     0x882364: b.eq            #0x882370
    //     0x882368: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88236c: stur            x4, [x0, #7]
    // 0x882370: ldur            x1, [fp, #-8]
    // 0x882374: ArrayStore: r1[0] = r0  ; List_4
    //     0x882374: stur            w0, [x1, #0x17]
    //     0x882378: tbz             w0, #0, #0x882394
    //     0x88237c: ldurb           w16, [x1, #-1]
    //     0x882380: ldurb           w17, [x0, #-1]
    //     0x882384: and             x16, x17, x16, lsr #2
    //     0x882388: tst             x16, HEAP, lsr #32
    //     0x88238c: b.eq            #0x882394
    //     0x882390: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x882394: r0 = Null
    //     0x882394: mov             x0, NULL
    // 0x882398: LeaveFrame
    //     0x882398: mov             SP, fp
    //     0x88239c: ldp             fp, lr, [SP], #0x10
    // 0x8823a0: ret
    //     0x8823a0: ret             
    // 0x8823a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8823a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8823a8: b               #0x8821d0
  }
}
