// lib: , url: package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart

// class id: 1049478, size: 0x8
class :: {
}

// class id: 4543, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestOtpPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1366300, size: 0x64
    // 0x1366300: EnterFrame
    //     0x1366300: stp             fp, lr, [SP, #-0x10]!
    //     0x1366304: mov             fp, SP
    // 0x1366308: AllocStack(0x18)
    //     0x1366308: sub             SP, SP, #0x18
    // 0x136630c: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x136630c: stur            x1, [fp, #-8]
    //     0x1366310: stur            x2, [fp, #-0x10]
    // 0x1366314: r1 = 2
    //     0x1366314: movz            x1, #0x2
    // 0x1366318: r0 = AllocateContext()
    //     0x1366318: bl              #0x16f6108  ; AllocateContextStub
    // 0x136631c: mov             x1, x0
    // 0x1366320: ldur            x0, [fp, #-8]
    // 0x1366324: stur            x1, [fp, #-0x18]
    // 0x1366328: StoreField: r1->field_f = r0
    //     0x1366328: stur            w0, [x1, #0xf]
    // 0x136632c: ldur            x0, [fp, #-0x10]
    // 0x1366330: StoreField: r1->field_13 = r0
    //     0x1366330: stur            w0, [x1, #0x13]
    // 0x1366334: r0 = Obx()
    //     0x1366334: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1366338: ldur            x2, [fp, #-0x18]
    // 0x136633c: r1 = Function '<anonymous closure>':.
    //     0x136633c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d620] AnonymousClosure: (0x1366364), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::bottomNavigationBar (0x1366300)
    //     0x1366340: ldr             x1, [x1, #0x620]
    // 0x1366344: stur            x0, [fp, #-8]
    // 0x1366348: r0 = AllocateClosure()
    //     0x1366348: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x136634c: mov             x1, x0
    // 0x1366350: ldur            x0, [fp, #-8]
    // 0x1366354: StoreField: r0->field_b = r1
    //     0x1366354: stur            w1, [x0, #0xb]
    // 0x1366358: LeaveFrame
    //     0x1366358: mov             SP, fp
    //     0x136635c: ldp             fp, lr, [SP], #0x10
    // 0x1366360: ret
    //     0x1366360: ret             
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x1366364, size: 0x638
    // 0x1366364: EnterFrame
    //     0x1366364: stp             fp, lr, [SP, #-0x10]!
    //     0x1366368: mov             fp, SP
    // 0x136636c: AllocStack(0x60)
    //     0x136636c: sub             SP, SP, #0x60
    // 0x1366370: SetupParameters()
    //     0x1366370: ldr             x0, [fp, #0x10]
    //     0x1366374: ldur            w2, [x0, #0x17]
    //     0x1366378: add             x2, x2, HEAP, lsl #32
    //     0x136637c: stur            x2, [fp, #-8]
    // 0x1366380: CheckStackOverflow
    //     0x1366380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1366384: cmp             SP, x16
    //     0x1366388: b.ls            #0x1366950
    // 0x136638c: LoadField: r1 = r2->field_13
    //     0x136638c: ldur            w1, [x2, #0x13]
    // 0x1366390: DecompressPointer r1
    //     0x1366390: add             x1, x1, HEAP, lsl #32
    // 0x1366394: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1366394: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1366398: r0 = _of()
    //     0x1366398: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x136639c: LoadField: r1 = r0->field_23
    //     0x136639c: ldur            w1, [x0, #0x23]
    // 0x13663a0: DecompressPointer r1
    //     0x13663a0: add             x1, x1, HEAP, lsl #32
    // 0x13663a4: LoadField: d0 = r1->field_1f
    //     0x13663a4: ldur            d0, [x1, #0x1f]
    // 0x13663a8: stur            d0, [fp, #-0x40]
    // 0x13663ac: r0 = EdgeInsets()
    //     0x13663ac: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x13663b0: stur            x0, [fp, #-0x10]
    // 0x13663b4: StoreField: r0->field_7 = rZR
    //     0x13663b4: stur            xzr, [x0, #7]
    // 0x13663b8: StoreField: r0->field_f = rZR
    //     0x13663b8: stur            xzr, [x0, #0xf]
    // 0x13663bc: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13663bc: stur            xzr, [x0, #0x17]
    // 0x13663c0: ldur            d0, [fp, #-0x40]
    // 0x13663c4: StoreField: r0->field_1f = d0
    //     0x13663c4: stur            d0, [x0, #0x1f]
    // 0x13663c8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13663c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13663cc: ldr             x0, [x0, #0x1c80]
    //     0x13663d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13663d4: cmp             w0, w16
    //     0x13663d8: b.ne            #0x13663e4
    //     0x13663dc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13663e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13663e4: r0 = GetNavigation.width()
    //     0x13663e4: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x13663e8: ldur            x2, [fp, #-8]
    // 0x13663ec: stur            d0, [fp, #-0x40]
    // 0x13663f0: LoadField: r1 = r2->field_13
    //     0x13663f0: ldur            w1, [x2, #0x13]
    // 0x13663f4: DecompressPointer r1
    //     0x13663f4: add             x1, x1, HEAP, lsl #32
    // 0x13663f8: r0 = of()
    //     0x13663f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13663fc: LoadField: r1 = r0->field_5b
    //     0x13663fc: ldur            w1, [x0, #0x5b]
    // 0x1366400: DecompressPointer r1
    //     0x1366400: add             x1, x1, HEAP, lsl #32
    // 0x1366404: r0 = LoadClassIdInstr(r1)
    //     0x1366404: ldur            x0, [x1, #-1]
    //     0x1366408: ubfx            x0, x0, #0xc, #0x14
    // 0x136640c: r2 = 40
    //     0x136640c: movz            x2, #0x28
    // 0x1366410: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x1366410: sub             lr, x0, #0xfe7
    //     0x1366414: ldr             lr, [x21, lr, lsl #3]
    //     0x1366418: blr             lr
    // 0x136641c: stur            x0, [fp, #-0x18]
    // 0x1366420: r0 = BorderSide()
    //     0x1366420: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1366424: mov             x1, x0
    // 0x1366428: ldur            x0, [fp, #-0x18]
    // 0x136642c: stur            x1, [fp, #-0x20]
    // 0x1366430: StoreField: r1->field_7 = r0
    //     0x1366430: stur            w0, [x1, #7]
    // 0x1366434: d0 = 2.000000
    //     0x1366434: fmov            d0, #2.00000000
    // 0x1366438: StoreField: r1->field_b = d0
    //     0x1366438: stur            d0, [x1, #0xb]
    // 0x136643c: r0 = Instance_BorderStyle
    //     0x136643c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1366440: ldr             x0, [x0, #0xf68]
    // 0x1366444: StoreField: r1->field_13 = r0
    //     0x1366444: stur            w0, [x1, #0x13]
    // 0x1366448: d0 = -1.000000
    //     0x1366448: fmov            d0, #-1.00000000
    // 0x136644c: ArrayStore: r1[0] = d0  ; List_8
    //     0x136644c: stur            d0, [x1, #0x17]
    // 0x1366450: r0 = Border()
    //     0x1366450: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x1366454: mov             x1, x0
    // 0x1366458: ldur            x0, [fp, #-0x20]
    // 0x136645c: stur            x1, [fp, #-0x18]
    // 0x1366460: StoreField: r1->field_7 = r0
    //     0x1366460: stur            w0, [x1, #7]
    // 0x1366464: r0 = Instance_BorderSide
    //     0x1366464: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1366468: ldr             x0, [x0, #0xe20]
    // 0x136646c: StoreField: r1->field_b = r0
    //     0x136646c: stur            w0, [x1, #0xb]
    // 0x1366470: StoreField: r1->field_f = r0
    //     0x1366470: stur            w0, [x1, #0xf]
    // 0x1366474: StoreField: r1->field_13 = r0
    //     0x1366474: stur            w0, [x1, #0x13]
    // 0x1366478: r0 = BoxDecoration()
    //     0x1366478: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x136647c: mov             x2, x0
    // 0x1366480: ldur            x0, [fp, #-0x18]
    // 0x1366484: stur            x2, [fp, #-0x20]
    // 0x1366488: StoreField: r2->field_f = r0
    //     0x1366488: stur            w0, [x2, #0xf]
    // 0x136648c: r0 = Instance_BoxShape
    //     0x136648c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1366490: ldr             x0, [x0, #0x80]
    // 0x1366494: StoreField: r2->field_23 = r0
    //     0x1366494: stur            w0, [x2, #0x23]
    // 0x1366498: ldur            x0, [fp, #-8]
    // 0x136649c: LoadField: r1 = r0->field_f
    //     0x136649c: ldur            w1, [x0, #0xf]
    // 0x13664a0: DecompressPointer r1
    //     0x13664a0: add             x1, x1, HEAP, lsl #32
    // 0x13664a4: r0 = controller()
    //     0x13664a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13664a8: LoadField: r1 = r0->field_d3
    //     0x13664a8: ldur            w1, [x0, #0xd3]
    // 0x13664ac: DecompressPointer r1
    //     0x13664ac: add             x1, x1, HEAP, lsl #32
    // 0x13664b0: r0 = value()
    //     0x13664b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13664b4: tbnz            w0, #4, #0x13664d8
    // 0x13664b8: ldur            x2, [fp, #-8]
    // 0x13664bc: LoadField: r1 = r2->field_13
    //     0x13664bc: ldur            w1, [x2, #0x13]
    // 0x13664c0: DecompressPointer r1
    //     0x13664c0: add             x1, x1, HEAP, lsl #32
    // 0x13664c4: r0 = of()
    //     0x13664c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13664c8: LoadField: r1 = r0->field_5b
    //     0x13664c8: ldur            w1, [x0, #0x5b]
    // 0x13664cc: DecompressPointer r1
    //     0x13664cc: add             x1, x1, HEAP, lsl #32
    // 0x13664d0: mov             x0, x1
    // 0x13664d4: b               #0x13664e0
    // 0x13664d8: r0 = Instance_MaterialColor
    //     0x13664d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x13664dc: ldr             x0, [x0, #0xdc0]
    // 0x13664e0: ldur            x2, [fp, #-8]
    // 0x13664e4: r16 = <Color>
    //     0x13664e4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13664e8: ldr             x16, [x16, #0xf80]
    // 0x13664ec: stp             x0, x16, [SP]
    // 0x13664f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13664f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13664f4: r0 = all()
    //     0x13664f4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13664f8: stur            x0, [fp, #-0x18]
    // 0x13664fc: r16 = <RoundedRectangleBorder>
    //     0x13664fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1366500: ldr             x16, [x16, #0xf78]
    // 0x1366504: r30 = Instance_RoundedRectangleBorder
    //     0x1366504: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1366508: ldr             lr, [lr, #0xd68]
    // 0x136650c: stp             lr, x16, [SP]
    // 0x1366510: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1366510: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1366514: r0 = all()
    //     0x1366514: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1366518: stur            x0, [fp, #-0x28]
    // 0x136651c: r0 = ButtonStyle()
    //     0x136651c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1366520: mov             x1, x0
    // 0x1366524: ldur            x0, [fp, #-0x18]
    // 0x1366528: stur            x1, [fp, #-0x30]
    // 0x136652c: StoreField: r1->field_b = r0
    //     0x136652c: stur            w0, [x1, #0xb]
    // 0x1366530: ldur            x0, [fp, #-0x28]
    // 0x1366534: StoreField: r1->field_43 = r0
    //     0x1366534: stur            w0, [x1, #0x43]
    // 0x1366538: r0 = TextButtonThemeData()
    //     0x1366538: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x136653c: mov             x2, x0
    // 0x1366540: ldur            x0, [fp, #-0x30]
    // 0x1366544: stur            x2, [fp, #-0x18]
    // 0x1366548: StoreField: r2->field_7 = r0
    //     0x1366548: stur            w0, [x2, #7]
    // 0x136654c: ldur            x0, [fp, #-8]
    // 0x1366550: LoadField: r1 = r0->field_f
    //     0x1366550: ldur            w1, [x0, #0xf]
    // 0x1366554: DecompressPointer r1
    //     0x1366554: add             x1, x1, HEAP, lsl #32
    // 0x1366558: r0 = controller()
    //     0x1366558: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136655c: LoadField: r1 = r0->field_d3
    //     0x136655c: ldur            w1, [x0, #0xd3]
    // 0x1366560: DecompressPointer r1
    //     0x1366560: add             x1, x1, HEAP, lsl #32
    // 0x1366564: r0 = value()
    //     0x1366564: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1366568: tbnz            w0, #4, #0x1366584
    // 0x136656c: ldur            x2, [fp, #-8]
    // 0x1366570: r1 = Function '<anonymous closure>':.
    //     0x1366570: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d628] AnonymousClosure: (0x136699c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::bottomNavigationBar (0x1366300)
    //     0x1366574: ldr             x1, [x1, #0x628]
    // 0x1366578: r0 = AllocateClosure()
    //     0x1366578: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x136657c: mov             x4, x0
    // 0x1366580: b               #0x1366588
    // 0x1366584: r4 = Null
    //     0x1366584: mov             x4, NULL
    // 0x1366588: ldur            x2, [fp, #-8]
    // 0x136658c: ldur            x3, [fp, #-0x10]
    // 0x1366590: ldur            d0, [fp, #-0x40]
    // 0x1366594: ldur            x0, [fp, #-0x18]
    // 0x1366598: stur            x4, [fp, #-0x28]
    // 0x136659c: LoadField: r1 = r2->field_13
    //     0x136659c: ldur            w1, [x2, #0x13]
    // 0x13665a0: DecompressPointer r1
    //     0x13665a0: add             x1, x1, HEAP, lsl #32
    // 0x13665a4: r0 = of()
    //     0x13665a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13665a8: LoadField: r1 = r0->field_87
    //     0x13665a8: ldur            w1, [x0, #0x87]
    // 0x13665ac: DecompressPointer r1
    //     0x13665ac: add             x1, x1, HEAP, lsl #32
    // 0x13665b0: LoadField: r0 = r1->field_7
    //     0x13665b0: ldur            w0, [x1, #7]
    // 0x13665b4: DecompressPointer r0
    //     0x13665b4: add             x0, x0, HEAP, lsl #32
    // 0x13665b8: r16 = 14.000000
    //     0x13665b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13665bc: ldr             x16, [x16, #0x1d8]
    // 0x13665c0: r30 = Instance_Color
    //     0x13665c0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13665c4: stp             lr, x16, [SP]
    // 0x13665c8: mov             x1, x0
    // 0x13665cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13665cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13665d0: ldr             x4, [x4, #0xaa0]
    // 0x13665d4: r0 = copyWith()
    //     0x13665d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13665d8: stur            x0, [fp, #-0x30]
    // 0x13665dc: r0 = Text()
    //     0x13665dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13665e0: mov             x1, x0
    // 0x13665e4: r0 = "CONTINUE"
    //     0x13665e4: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x13665e8: ldr             x0, [x0, #0xac8]
    // 0x13665ec: stur            x1, [fp, #-0x38]
    // 0x13665f0: StoreField: r1->field_b = r0
    //     0x13665f0: stur            w0, [x1, #0xb]
    // 0x13665f4: ldur            x0, [fp, #-0x30]
    // 0x13665f8: StoreField: r1->field_13 = r0
    //     0x13665f8: stur            w0, [x1, #0x13]
    // 0x13665fc: r0 = TextButton()
    //     0x13665fc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1366600: mov             x1, x0
    // 0x1366604: ldur            x0, [fp, #-0x28]
    // 0x1366608: stur            x1, [fp, #-0x30]
    // 0x136660c: StoreField: r1->field_b = r0
    //     0x136660c: stur            w0, [x1, #0xb]
    // 0x1366610: r0 = false
    //     0x1366610: add             x0, NULL, #0x30  ; false
    // 0x1366614: StoreField: r1->field_27 = r0
    //     0x1366614: stur            w0, [x1, #0x27]
    // 0x1366618: r2 = true
    //     0x1366618: add             x2, NULL, #0x20  ; true
    // 0x136661c: StoreField: r1->field_2f = r2
    //     0x136661c: stur            w2, [x1, #0x2f]
    // 0x1366620: ldur            x3, [fp, #-0x38]
    // 0x1366624: StoreField: r1->field_37 = r3
    //     0x1366624: stur            w3, [x1, #0x37]
    // 0x1366628: r0 = TextButtonTheme()
    //     0x1366628: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x136662c: mov             x1, x0
    // 0x1366630: ldur            x0, [fp, #-0x18]
    // 0x1366634: stur            x1, [fp, #-0x28]
    // 0x1366638: StoreField: r1->field_f = r0
    //     0x1366638: stur            w0, [x1, #0xf]
    // 0x136663c: ldur            x0, [fp, #-0x30]
    // 0x1366640: StoreField: r1->field_b = r0
    //     0x1366640: stur            w0, [x1, #0xb]
    // 0x1366644: ldur            d0, [fp, #-0x40]
    // 0x1366648: r0 = inline_Allocate_Double()
    //     0x1366648: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x136664c: add             x0, x0, #0x10
    //     0x1366650: cmp             x2, x0
    //     0x1366654: b.ls            #0x1366958
    //     0x1366658: str             x0, [THR, #0x50]  ; THR::top
    //     0x136665c: sub             x0, x0, #0xf
    //     0x1366660: movz            x2, #0xe15c
    //     0x1366664: movk            x2, #0x3, lsl #16
    //     0x1366668: stur            x2, [x0, #-1]
    // 0x136666c: StoreField: r0->field_7 = d0
    //     0x136666c: stur            d0, [x0, #7]
    // 0x1366670: stur            x0, [fp, #-0x18]
    // 0x1366674: r0 = Container()
    //     0x1366674: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1366678: stur            x0, [fp, #-0x30]
    // 0x136667c: r16 = Instance_EdgeInsets
    //     0x136667c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x1366680: ldr             x16, [x16, #0xe48]
    // 0x1366684: ldur            lr, [fp, #-0x18]
    // 0x1366688: stp             lr, x16, [SP, #0x10]
    // 0x136668c: ldur            x16, [fp, #-0x20]
    // 0x1366690: ldur            lr, [fp, #-0x28]
    // 0x1366694: stp             lr, x16, [SP]
    // 0x1366698: mov             x1, x0
    // 0x136669c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x136669c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x13666a0: ldr             x4, [x4, #0x18]
    // 0x13666a4: r0 = Container()
    //     0x13666a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13666a8: r0 = GetNavigation.size()
    //     0x13666a8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13666ac: LoadField: d0 = r0->field_7
    //     0x13666ac: ldur            d0, [x0, #7]
    // 0x13666b0: ldur            x0, [fp, #-8]
    // 0x13666b4: stur            d0, [fp, #-0x40]
    // 0x13666b8: LoadField: r1 = r0->field_13
    //     0x13666b8: ldur            w1, [x0, #0x13]
    // 0x13666bc: DecompressPointer r1
    //     0x13666bc: add             x1, x1, HEAP, lsl #32
    // 0x13666c0: r0 = of()
    //     0x13666c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13666c4: LoadField: r1 = r0->field_87
    //     0x13666c4: ldur            w1, [x0, #0x87]
    // 0x13666c8: DecompressPointer r1
    //     0x13666c8: add             x1, x1, HEAP, lsl #32
    // 0x13666cc: LoadField: r0 = r1->field_2b
    //     0x13666cc: ldur            w0, [x1, #0x2b]
    // 0x13666d0: DecompressPointer r0
    //     0x13666d0: add             x0, x0, HEAP, lsl #32
    // 0x13666d4: stur            x0, [fp, #-8]
    // 0x13666d8: r1 = Instance_Color
    //     0x13666d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13666dc: d0 = 0.700000
    //     0x13666dc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13666e0: ldr             d0, [x17, #0xf48]
    // 0x13666e4: r0 = withOpacity()
    //     0x13666e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13666e8: r16 = 10.000000
    //     0x13666e8: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x13666ec: stp             x0, x16, [SP]
    // 0x13666f0: ldur            x1, [fp, #-8]
    // 0x13666f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13666f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13666f8: ldr             x4, [x4, #0xaa0]
    // 0x13666fc: r0 = copyWith()
    //     0x13666fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1366700: stur            x0, [fp, #-8]
    // 0x1366704: r0 = Text()
    //     0x1366704: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1366708: mov             x1, x0
    // 0x136670c: r0 = "Powered By"
    //     0x136670c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x1366710: ldr             x0, [x0, #0x750]
    // 0x1366714: stur            x1, [fp, #-0x18]
    // 0x1366718: StoreField: r1->field_b = r0
    //     0x1366718: stur            w0, [x1, #0xb]
    // 0x136671c: ldur            x0, [fp, #-8]
    // 0x1366720: StoreField: r1->field_13 = r0
    //     0x1366720: stur            w0, [x1, #0x13]
    // 0x1366724: r0 = SvgPicture()
    //     0x1366724: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1366728: stur            x0, [fp, #-8]
    // 0x136672c: r16 = 20.000000
    //     0x136672c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x1366730: ldr             x16, [x16, #0xac8]
    // 0x1366734: str             x16, [SP]
    // 0x1366738: mov             x1, x0
    // 0x136673c: r2 = "assets/images/shopdeck.svg"
    //     0x136673c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x1366740: ldr             x2, [x2, #0x758]
    // 0x1366744: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x1366744: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x1366748: ldr             x4, [x4, #0x760]
    // 0x136674c: r0 = SvgPicture.asset()
    //     0x136674c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1366750: r1 = Null
    //     0x1366750: mov             x1, NULL
    // 0x1366754: r2 = 4
    //     0x1366754: movz            x2, #0x4
    // 0x1366758: r0 = AllocateArray()
    //     0x1366758: bl              #0x16f7198  ; AllocateArrayStub
    // 0x136675c: mov             x2, x0
    // 0x1366760: ldur            x0, [fp, #-0x18]
    // 0x1366764: stur            x2, [fp, #-0x20]
    // 0x1366768: StoreField: r2->field_f = r0
    //     0x1366768: stur            w0, [x2, #0xf]
    // 0x136676c: ldur            x0, [fp, #-8]
    // 0x1366770: StoreField: r2->field_13 = r0
    //     0x1366770: stur            w0, [x2, #0x13]
    // 0x1366774: r1 = <Widget>
    //     0x1366774: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1366778: r0 = AllocateGrowableArray()
    //     0x1366778: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x136677c: mov             x1, x0
    // 0x1366780: ldur            x0, [fp, #-0x20]
    // 0x1366784: stur            x1, [fp, #-8]
    // 0x1366788: StoreField: r1->field_f = r0
    //     0x1366788: stur            w0, [x1, #0xf]
    // 0x136678c: r2 = 4
    //     0x136678c: movz            x2, #0x4
    // 0x1366790: StoreField: r1->field_b = r2
    //     0x1366790: stur            w2, [x1, #0xb]
    // 0x1366794: r0 = Row()
    //     0x1366794: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1366798: mov             x1, x0
    // 0x136679c: r0 = Instance_Axis
    //     0x136679c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13667a0: stur            x1, [fp, #-0x18]
    // 0x13667a4: StoreField: r1->field_f = r0
    //     0x13667a4: stur            w0, [x1, #0xf]
    // 0x13667a8: r0 = Instance_MainAxisAlignment
    //     0x13667a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13667ac: ldr             x0, [x0, #0xa08]
    // 0x13667b0: StoreField: r1->field_13 = r0
    //     0x13667b0: stur            w0, [x1, #0x13]
    // 0x13667b4: r2 = Instance_MainAxisSize
    //     0x13667b4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13667b8: ldr             x2, [x2, #0xdd0]
    // 0x13667bc: ArrayStore: r1[0] = r2  ; List_4
    //     0x13667bc: stur            w2, [x1, #0x17]
    // 0x13667c0: r3 = Instance_CrossAxisAlignment
    //     0x13667c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13667c4: ldr             x3, [x3, #0xa18]
    // 0x13667c8: StoreField: r1->field_1b = r3
    //     0x13667c8: stur            w3, [x1, #0x1b]
    // 0x13667cc: r4 = Instance_VerticalDirection
    //     0x13667cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13667d0: ldr             x4, [x4, #0xa20]
    // 0x13667d4: StoreField: r1->field_23 = r4
    //     0x13667d4: stur            w4, [x1, #0x23]
    // 0x13667d8: r5 = Instance_Clip
    //     0x13667d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13667dc: ldr             x5, [x5, #0x38]
    // 0x13667e0: StoreField: r1->field_2b = r5
    //     0x13667e0: stur            w5, [x1, #0x2b]
    // 0x13667e4: StoreField: r1->field_2f = rZR
    //     0x13667e4: stur            xzr, [x1, #0x2f]
    // 0x13667e8: ldur            x6, [fp, #-8]
    // 0x13667ec: StoreField: r1->field_b = r6
    //     0x13667ec: stur            w6, [x1, #0xb]
    // 0x13667f0: ldur            d0, [fp, #-0x40]
    // 0x13667f4: r6 = inline_Allocate_Double()
    //     0x13667f4: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x13667f8: add             x6, x6, #0x10
    //     0x13667fc: cmp             x7, x6
    //     0x1366800: b.ls            #0x1366970
    //     0x1366804: str             x6, [THR, #0x50]  ; THR::top
    //     0x1366808: sub             x6, x6, #0xf
    //     0x136680c: movz            x7, #0xe15c
    //     0x1366810: movk            x7, #0x3, lsl #16
    //     0x1366814: stur            x7, [x6, #-1]
    // 0x1366818: StoreField: r6->field_7 = d0
    //     0x1366818: stur            d0, [x6, #7]
    // 0x136681c: stur            x6, [fp, #-8]
    // 0x1366820: r0 = Container()
    //     0x1366820: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1366824: stur            x0, [fp, #-0x20]
    // 0x1366828: r16 = Instance_Alignment
    //     0x1366828: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x136682c: ldr             x16, [x16, #0xb10]
    // 0x1366830: ldur            lr, [fp, #-8]
    // 0x1366834: stp             lr, x16, [SP, #0x10]
    // 0x1366838: r16 = Instance_BoxDecoration
    //     0x1366838: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x136683c: ldr             x16, [x16, #0x768]
    // 0x1366840: ldur            lr, [fp, #-0x18]
    // 0x1366844: stp             lr, x16, [SP]
    // 0x1366848: mov             x1, x0
    // 0x136684c: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x136684c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x1366850: ldr             x4, [x4, #0x770]
    // 0x1366854: r0 = Container()
    //     0x1366854: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1366858: r1 = Null
    //     0x1366858: mov             x1, NULL
    // 0x136685c: r2 = 4
    //     0x136685c: movz            x2, #0x4
    // 0x1366860: r0 = AllocateArray()
    //     0x1366860: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1366864: mov             x2, x0
    // 0x1366868: ldur            x0, [fp, #-0x30]
    // 0x136686c: stur            x2, [fp, #-8]
    // 0x1366870: StoreField: r2->field_f = r0
    //     0x1366870: stur            w0, [x2, #0xf]
    // 0x1366874: ldur            x0, [fp, #-0x20]
    // 0x1366878: StoreField: r2->field_13 = r0
    //     0x1366878: stur            w0, [x2, #0x13]
    // 0x136687c: r1 = <Widget>
    //     0x136687c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1366880: r0 = AllocateGrowableArray()
    //     0x1366880: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1366884: mov             x1, x0
    // 0x1366888: ldur            x0, [fp, #-8]
    // 0x136688c: stur            x1, [fp, #-0x18]
    // 0x1366890: StoreField: r1->field_f = r0
    //     0x1366890: stur            w0, [x1, #0xf]
    // 0x1366894: r0 = 4
    //     0x1366894: movz            x0, #0x4
    // 0x1366898: StoreField: r1->field_b = r0
    //     0x1366898: stur            w0, [x1, #0xb]
    // 0x136689c: r0 = Column()
    //     0x136689c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13668a0: mov             x1, x0
    // 0x13668a4: r0 = Instance_Axis
    //     0x13668a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13668a8: stur            x1, [fp, #-8]
    // 0x13668ac: StoreField: r1->field_f = r0
    //     0x13668ac: stur            w0, [x1, #0xf]
    // 0x13668b0: r0 = Instance_MainAxisAlignment
    //     0x13668b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13668b4: ldr             x0, [x0, #0xa08]
    // 0x13668b8: StoreField: r1->field_13 = r0
    //     0x13668b8: stur            w0, [x1, #0x13]
    // 0x13668bc: r0 = Instance_MainAxisSize
    //     0x13668bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13668c0: ldr             x0, [x0, #0xdd0]
    // 0x13668c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x13668c4: stur            w0, [x1, #0x17]
    // 0x13668c8: r0 = Instance_CrossAxisAlignment
    //     0x13668c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13668cc: ldr             x0, [x0, #0xa18]
    // 0x13668d0: StoreField: r1->field_1b = r0
    //     0x13668d0: stur            w0, [x1, #0x1b]
    // 0x13668d4: r0 = Instance_VerticalDirection
    //     0x13668d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13668d8: ldr             x0, [x0, #0xa20]
    // 0x13668dc: StoreField: r1->field_23 = r0
    //     0x13668dc: stur            w0, [x1, #0x23]
    // 0x13668e0: r0 = Instance_Clip
    //     0x13668e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13668e4: ldr             x0, [x0, #0x38]
    // 0x13668e8: StoreField: r1->field_2b = r0
    //     0x13668e8: stur            w0, [x1, #0x2b]
    // 0x13668ec: StoreField: r1->field_2f = rZR
    //     0x13668ec: stur            xzr, [x1, #0x2f]
    // 0x13668f0: ldur            x0, [fp, #-0x18]
    // 0x13668f4: StoreField: r1->field_b = r0
    //     0x13668f4: stur            w0, [x1, #0xb]
    // 0x13668f8: r0 = Padding()
    //     0x13668f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13668fc: mov             x1, x0
    // 0x1366900: ldur            x0, [fp, #-0x10]
    // 0x1366904: stur            x1, [fp, #-0x18]
    // 0x1366908: StoreField: r1->field_f = r0
    //     0x1366908: stur            w0, [x1, #0xf]
    // 0x136690c: ldur            x0, [fp, #-8]
    // 0x1366910: StoreField: r1->field_b = r0
    //     0x1366910: stur            w0, [x1, #0xb]
    // 0x1366914: r0 = SafeArea()
    //     0x1366914: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1366918: r1 = true
    //     0x1366918: add             x1, NULL, #0x20  ; true
    // 0x136691c: StoreField: r0->field_b = r1
    //     0x136691c: stur            w1, [x0, #0xb]
    // 0x1366920: StoreField: r0->field_f = r1
    //     0x1366920: stur            w1, [x0, #0xf]
    // 0x1366924: StoreField: r0->field_13 = r1
    //     0x1366924: stur            w1, [x0, #0x13]
    // 0x1366928: ArrayStore: r0[0] = r1  ; List_4
    //     0x1366928: stur            w1, [x0, #0x17]
    // 0x136692c: r1 = Instance_EdgeInsets
    //     0x136692c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1366930: StoreField: r0->field_1b = r1
    //     0x1366930: stur            w1, [x0, #0x1b]
    // 0x1366934: r1 = false
    //     0x1366934: add             x1, NULL, #0x30  ; false
    // 0x1366938: StoreField: r0->field_1f = r1
    //     0x1366938: stur            w1, [x0, #0x1f]
    // 0x136693c: ldur            x1, [fp, #-0x18]
    // 0x1366940: StoreField: r0->field_23 = r1
    //     0x1366940: stur            w1, [x0, #0x23]
    // 0x1366944: LeaveFrame
    //     0x1366944: mov             SP, fp
    //     0x1366948: ldp             fp, lr, [SP], #0x10
    // 0x136694c: ret
    //     0x136694c: ret             
    // 0x1366950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1366950: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1366954: b               #0x136638c
    // 0x1366958: SaveReg d0
    //     0x1366958: str             q0, [SP, #-0x10]!
    // 0x136695c: SaveReg r1
    //     0x136695c: str             x1, [SP, #-8]!
    // 0x1366960: r0 = AllocateDouble()
    //     0x1366960: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1366964: RestoreReg r1
    //     0x1366964: ldr             x1, [SP], #8
    // 0x1366968: RestoreReg d0
    //     0x1366968: ldr             q0, [SP], #0x10
    // 0x136696c: b               #0x136666c
    // 0x1366970: SaveReg d0
    //     0x1366970: str             q0, [SP, #-0x10]!
    // 0x1366974: stp             x4, x5, [SP, #-0x10]!
    // 0x1366978: stp             x2, x3, [SP, #-0x10]!
    // 0x136697c: stp             x0, x1, [SP, #-0x10]!
    // 0x1366980: r0 = AllocateDouble()
    //     0x1366980: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1366984: mov             x6, x0
    // 0x1366988: ldp             x0, x1, [SP], #0x10
    // 0x136698c: ldp             x2, x3, [SP], #0x10
    // 0x1366990: ldp             x4, x5, [SP], #0x10
    // 0x1366994: RestoreReg d0
    //     0x1366994: ldr             q0, [SP], #0x10
    // 0x1366998: b               #0x1366818
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x136699c, size: 0x184
    // 0x136699c: EnterFrame
    //     0x136699c: stp             fp, lr, [SP, #-0x10]!
    //     0x13669a0: mov             fp, SP
    // 0x13669a4: AllocStack(0x40)
    //     0x13669a4: sub             SP, SP, #0x40
    // 0x13669a8: SetupParameters()
    //     0x13669a8: ldr             x0, [fp, #0x10]
    //     0x13669ac: ldur            w2, [x0, #0x17]
    //     0x13669b0: add             x2, x2, HEAP, lsl #32
    //     0x13669b4: stur            x2, [fp, #-8]
    // 0x13669b8: CheckStackOverflow
    //     0x13669b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13669bc: cmp             SP, x16
    //     0x13669c0: b.ls            #0x1366b18
    // 0x13669c4: LoadField: r1 = r2->field_f
    //     0x13669c4: ldur            w1, [x2, #0xf]
    // 0x13669c8: DecompressPointer r1
    //     0x13669c8: add             x1, x1, HEAP, lsl #32
    // 0x13669cc: r0 = controller()
    //     0x13669cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13669d0: mov             x2, x0
    // 0x13669d4: ldur            x0, [fp, #-8]
    // 0x13669d8: stur            x2, [fp, #-0x10]
    // 0x13669dc: LoadField: r1 = r0->field_f
    //     0x13669dc: ldur            w1, [x0, #0xf]
    // 0x13669e0: DecompressPointer r1
    //     0x13669e0: add             x1, x1, HEAP, lsl #32
    // 0x13669e4: r0 = controller()
    //     0x13669e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13669e8: mov             x1, x0
    // 0x13669ec: r0 = freeGiftDetailResponse()
    //     0x13669ec: bl              #0x12d6818  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::freeGiftDetailResponse
    // 0x13669f0: mov             x2, x0
    // 0x13669f4: ldur            x0, [fp, #-8]
    // 0x13669f8: stur            x2, [fp, #-0x18]
    // 0x13669fc: LoadField: r1 = r0->field_f
    //     0x13669fc: ldur            w1, [x0, #0xf]
    // 0x1366a00: DecompressPointer r1
    //     0x1366a00: add             x1, x1, HEAP, lsl #32
    // 0x1366a04: r0 = controller()
    //     0x1366a04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366a08: LoadField: r3 = r0->field_f3
    //     0x1366a08: ldur            w3, [x0, #0xf3]
    // 0x1366a0c: DecompressPointer r3
    //     0x1366a0c: add             x3, x3, HEAP, lsl #32
    // 0x1366a10: ldur            x1, [fp, #-0x10]
    // 0x1366a14: ldur            x2, [fp, #-0x18]
    // 0x1366a18: r0 = verifyOtp()
    //     0x1366a18: bl              #0x12ebf14  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::verifyOtp
    // 0x1366a1c: ldur            x0, [fp, #-8]
    // 0x1366a20: LoadField: r1 = r0->field_f
    //     0x1366a20: ldur            w1, [x0, #0xf]
    // 0x1366a24: DecompressPointer r1
    //     0x1366a24: add             x1, x1, HEAP, lsl #32
    // 0x1366a28: r0 = controller()
    //     0x1366a28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366a2c: mov             x2, x0
    // 0x1366a30: ldur            x0, [fp, #-8]
    // 0x1366a34: stur            x2, [fp, #-0x10]
    // 0x1366a38: LoadField: r1 = r0->field_f
    //     0x1366a38: ldur            w1, [x0, #0xf]
    // 0x1366a3c: DecompressPointer r1
    //     0x1366a3c: add             x1, x1, HEAP, lsl #32
    // 0x1366a40: r0 = controller()
    //     0x1366a40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366a44: mov             x1, x0
    // 0x1366a48: r0 = bumperCouponData()
    //     0x1366a48: bl              #0x8a2a70  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bumperCouponData
    // 0x1366a4c: r16 = "request_otp"
    //     0x1366a4c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d630] "request_otp"
    //     0x1366a50: ldr             x16, [x16, #0x630]
    // 0x1366a54: r30 = "landing_page"
    //     0x1366a54: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x1366a58: ldr             lr, [lr, #0x638]
    // 0x1366a5c: stp             lr, x16, [SP, #0x18]
    // 0x1366a60: r16 = "page_cta"
    //     0x1366a60: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c780] "page_cta"
    //     0x1366a64: ldr             x16, [x16, #0x780]
    // 0x1366a68: r30 = "continue"
    //     0x1366a68: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d640] "continue"
    //     0x1366a6c: ldr             lr, [lr, #0x640]
    // 0x1366a70: stp             lr, x16, [SP, #8]
    // 0x1366a74: str             x0, [SP]
    // 0x1366a78: ldur            x1, [fp, #-0x10]
    // 0x1366a7c: r2 = "checkout_widget_fill"
    //     0x1366a7c: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d5c8] "checkout_widget_fill"
    //     0x1366a80: ldr             x2, [x2, #0x5c8]
    // 0x1366a84: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, orderId, 0x6, pageId, 0x2, pageType, 0x3, null]
    //     0x1366a84: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d648] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "orderId", 0x6, "pageId", 0x2, "pageType", 0x3, Null]
    //     0x1366a88: ldr             x4, [x4, #0x648]
    // 0x1366a8c: r0 = checkoutPostEvent()
    //     0x1366a8c: bl              #0x12eb500  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::checkoutPostEvent
    // 0x1366a90: ldur            x0, [fp, #-8]
    // 0x1366a94: LoadField: r1 = r0->field_f
    //     0x1366a94: ldur            w1, [x0, #0xf]
    // 0x1366a98: DecompressPointer r1
    //     0x1366a98: add             x1, x1, HEAP, lsl #32
    // 0x1366a9c: r0 = controller()
    //     0x1366a9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366aa0: mov             x2, x0
    // 0x1366aa4: ldur            x0, [fp, #-8]
    // 0x1366aa8: stur            x2, [fp, #-0x10]
    // 0x1366aac: LoadField: r1 = r0->field_f
    //     0x1366aac: ldur            w1, [x0, #0xf]
    // 0x1366ab0: DecompressPointer r1
    //     0x1366ab0: add             x1, x1, HEAP, lsl #32
    // 0x1366ab4: r0 = controller()
    //     0x1366ab4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366ab8: LoadField: r1 = r0->field_83
    //     0x1366ab8: ldur            w1, [x0, #0x83]
    // 0x1366abc: DecompressPointer r1
    //     0x1366abc: add             x1, x1, HEAP, lsl #32
    // 0x1366ac0: r0 = value()
    //     0x1366ac0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1366ac4: r16 = "request_otp"
    //     0x1366ac4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d630] "request_otp"
    //     0x1366ac8: ldr             x16, [x16, #0x630]
    // 0x1366acc: r30 = "landing_page"
    //     0x1366acc: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x1366ad0: ldr             lr, [lr, #0x638]
    // 0x1366ad4: stp             lr, x16, [SP, #0x18]
    // 0x1366ad8: r16 = "page_cta"
    //     0x1366ad8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c780] "page_cta"
    //     0x1366adc: ldr             x16, [x16, #0x780]
    // 0x1366ae0: r30 = "continue"
    //     0x1366ae0: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d640] "continue"
    //     0x1366ae4: ldr             lr, [lr, #0x640]
    // 0x1366ae8: stp             lr, x16, [SP, #8]
    // 0x1366aec: str             x0, [SP]
    // 0x1366af0: ldur            x1, [fp, #-0x10]
    // 0x1366af4: r2 = "checkout_cta_clicked"
    //     0x1366af4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x1366af8: ldr             x2, [x2, #0x7b0]
    // 0x1366afc: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, orderId, 0x6, pageId, 0x2, pageType, 0x3, null]
    //     0x1366afc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d648] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "orderId", 0x6, "pageId", 0x2, "pageType", 0x3, Null]
    //     0x1366b00: ldr             x4, [x4, #0x648]
    // 0x1366b04: r0 = checkoutPostEvent()
    //     0x1366b04: bl              #0x12eb500  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::checkoutPostEvent
    // 0x1366b08: r0 = Null
    //     0x1366b08: mov             x0, NULL
    // 0x1366b0c: LeaveFrame
    //     0x1366b0c: mov             SP, fp
    //     0x1366b10: ldp             fp, lr, [SP], #0x10
    // 0x1366b14: ret
    //     0x1366b14: ret             
    // 0x1366b18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1366b18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1366b1c: b               #0x13669c4
  }
  [closure] bool <anonymous closure>(dynamic, Route<dynamic>) {
    // ** addr: 0x1394804, size: 0x1e4
    // 0x1394804: EnterFrame
    //     0x1394804: stp             fp, lr, [SP, #-0x10]!
    //     0x1394808: mov             fp, SP
    // 0x139480c: AllocStack(0x10)
    //     0x139480c: sub             SP, SP, #0x10
    // 0x1394810: CheckStackOverflow
    //     0x1394810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1394814: cmp             SP, x16
    //     0x1394818: b.ls            #0x13949e0
    // 0x139481c: ldr             x1, [fp, #0x10]
    // 0x1394820: LoadField: r0 = r1->field_13
    //     0x1394820: ldur            w0, [x1, #0x13]
    // 0x1394824: DecompressPointer r0
    //     0x1394824: add             x0, x0, HEAP, lsl #32
    // 0x1394828: r2 = LoadClassIdInstr(r0)
    //     0x1394828: ldur            x2, [x0, #-1]
    //     0x139482c: ubfx            x2, x2, #0xc, #0x14
    // 0x1394830: cmp             x2, #0x62e
    // 0x1394834: b.ne            #0x1394848
    // 0x1394838: LoadField: r2 = r0->field_7
    //     0x1394838: ldur            w2, [x0, #7]
    // 0x139483c: DecompressPointer r2
    //     0x139483c: add             x2, x2, HEAP, lsl #32
    // 0x1394840: mov             x0, x2
    // 0x1394844: b               #0x1394854
    // 0x1394848: LoadField: r2 = r0->field_6b
    //     0x1394848: ldur            w2, [x0, #0x6b]
    // 0x139484c: DecompressPointer r2
    //     0x139484c: add             x2, x2, HEAP, lsl #32
    // 0x1394850: mov             x0, x2
    // 0x1394854: r2 = LoadClassIdInstr(r0)
    //     0x1394854: ldur            x2, [x0, #-1]
    //     0x1394858: ubfx            x2, x2, #0xc, #0x14
    // 0x139485c: r16 = "/checkout_order_summary_page"
    //     0x139485c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x1394860: ldr             x16, [x16, #0x9d8]
    // 0x1394864: stp             x16, x0, [SP]
    // 0x1394868: mov             x0, x2
    // 0x139486c: mov             lr, x0
    // 0x1394870: ldr             lr, [x21, lr, lsl #3]
    // 0x1394874: blr             lr
    // 0x1394878: tbnz            w0, #4, #0x139488c
    // 0x139487c: r0 = true
    //     0x139487c: add             x0, NULL, #0x20  ; true
    // 0x1394880: LeaveFrame
    //     0x1394880: mov             SP, fp
    //     0x1394884: ldp             fp, lr, [SP], #0x10
    // 0x1394888: ret
    //     0x1394888: ret             
    // 0x139488c: ldr             x1, [fp, #0x10]
    // 0x1394890: LoadField: r0 = r1->field_13
    //     0x1394890: ldur            w0, [x1, #0x13]
    // 0x1394894: DecompressPointer r0
    //     0x1394894: add             x0, x0, HEAP, lsl #32
    // 0x1394898: r2 = LoadClassIdInstr(r0)
    //     0x1394898: ldur            x2, [x0, #-1]
    //     0x139489c: ubfx            x2, x2, #0xc, #0x14
    // 0x13948a0: cmp             x2, #0x62e
    // 0x13948a4: b.ne            #0x13948b8
    // 0x13948a8: LoadField: r2 = r0->field_7
    //     0x13948a8: ldur            w2, [x0, #7]
    // 0x13948ac: DecompressPointer r2
    //     0x13948ac: add             x2, x2, HEAP, lsl #32
    // 0x13948b0: mov             x0, x2
    // 0x13948b4: b               #0x13948c4
    // 0x13948b8: LoadField: r2 = r0->field_6b
    //     0x13948b8: ldur            w2, [x0, #0x6b]
    // 0x13948bc: DecompressPointer r2
    //     0x13948bc: add             x2, x2, HEAP, lsl #32
    // 0x13948c0: mov             x0, x2
    // 0x13948c4: r2 = LoadClassIdInstr(r0)
    //     0x13948c4: ldur            x2, [x0, #-1]
    //     0x13948c8: ubfx            x2, x2, #0xc, #0x14
    // 0x13948cc: r16 = "/bag"
    //     0x13948cc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x13948d0: ldr             x16, [x16, #0x468]
    // 0x13948d4: stp             x16, x0, [SP]
    // 0x13948d8: mov             x0, x2
    // 0x13948dc: mov             lr, x0
    // 0x13948e0: ldr             lr, [x21, lr, lsl #3]
    // 0x13948e4: blr             lr
    // 0x13948e8: tbnz            w0, #4, #0x13948fc
    // 0x13948ec: r0 = true
    //     0x13948ec: add             x0, NULL, #0x20  ; true
    // 0x13948f0: LeaveFrame
    //     0x13948f0: mov             SP, fp
    //     0x13948f4: ldp             fp, lr, [SP], #0x10
    // 0x13948f8: ret
    //     0x13948f8: ret             
    // 0x13948fc: ldr             x1, [fp, #0x10]
    // 0x1394900: LoadField: r0 = r1->field_13
    //     0x1394900: ldur            w0, [x1, #0x13]
    // 0x1394904: DecompressPointer r0
    //     0x1394904: add             x0, x0, HEAP, lsl #32
    // 0x1394908: r2 = LoadClassIdInstr(r0)
    //     0x1394908: ldur            x2, [x0, #-1]
    //     0x139490c: ubfx            x2, x2, #0xc, #0x14
    // 0x1394910: cmp             x2, #0x62e
    // 0x1394914: b.ne            #0x1394928
    // 0x1394918: LoadField: r2 = r0->field_7
    //     0x1394918: ldur            w2, [x0, #7]
    // 0x139491c: DecompressPointer r2
    //     0x139491c: add             x2, x2, HEAP, lsl #32
    // 0x1394920: mov             x0, x2
    // 0x1394924: b               #0x1394934
    // 0x1394928: LoadField: r2 = r0->field_6b
    //     0x1394928: ldur            w2, [x0, #0x6b]
    // 0x139492c: DecompressPointer r2
    //     0x139492c: add             x2, x2, HEAP, lsl #32
    // 0x1394930: mov             x0, x2
    // 0x1394934: r2 = LoadClassIdInstr(r0)
    //     0x1394934: ldur            x2, [x0, #-1]
    //     0x1394938: ubfx            x2, x2, #0xc, #0x14
    // 0x139493c: r16 = "/product-detail"
    //     0x139493c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x1394940: ldr             x16, [x16, #0x4a8]
    // 0x1394944: stp             x16, x0, [SP]
    // 0x1394948: mov             x0, x2
    // 0x139494c: mov             lr, x0
    // 0x1394950: ldr             lr, [x21, lr, lsl #3]
    // 0x1394954: blr             lr
    // 0x1394958: tbnz            w0, #4, #0x139496c
    // 0x139495c: r0 = true
    //     0x139495c: add             x0, NULL, #0x20  ; true
    // 0x1394960: LeaveFrame
    //     0x1394960: mov             SP, fp
    //     0x1394964: ldp             fp, lr, [SP], #0x10
    // 0x1394968: ret
    //     0x1394968: ret             
    // 0x139496c: ldr             x0, [fp, #0x10]
    // 0x1394970: LoadField: r1 = r0->field_13
    //     0x1394970: ldur            w1, [x0, #0x13]
    // 0x1394974: DecompressPointer r1
    //     0x1394974: add             x1, x1, HEAP, lsl #32
    // 0x1394978: r0 = LoadClassIdInstr(r1)
    //     0x1394978: ldur            x0, [x1, #-1]
    //     0x139497c: ubfx            x0, x0, #0xc, #0x14
    // 0x1394980: cmp             x0, #0x62e
    // 0x1394984: b.ne            #0x1394994
    // 0x1394988: LoadField: r0 = r1->field_7
    //     0x1394988: ldur            w0, [x1, #7]
    // 0x139498c: DecompressPointer r0
    //     0x139498c: add             x0, x0, HEAP, lsl #32
    // 0x1394990: b               #0x139499c
    // 0x1394994: LoadField: r0 = r1->field_6b
    //     0x1394994: ldur            w0, [x1, #0x6b]
    // 0x1394998: DecompressPointer r0
    //     0x1394998: add             x0, x0, HEAP, lsl #32
    // 0x139499c: r1 = LoadClassIdInstr(r0)
    //     0x139499c: ldur            x1, [x0, #-1]
    //     0x13949a0: ubfx            x1, x1, #0xc, #0x14
    // 0x13949a4: r16 = "/"
    //     0x13949a4: ldr             x16, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x13949a8: stp             x16, x0, [SP]
    // 0x13949ac: mov             x0, x1
    // 0x13949b0: mov             lr, x0
    // 0x13949b4: ldr             lr, [x21, lr, lsl #3]
    // 0x13949b8: blr             lr
    // 0x13949bc: tbnz            w0, #4, #0x13949d0
    // 0x13949c0: r0 = true
    //     0x13949c0: add             x0, NULL, #0x20  ; true
    // 0x13949c4: LeaveFrame
    //     0x13949c4: mov             SP, fp
    //     0x13949c8: ldp             fp, lr, [SP], #0x10
    // 0x13949cc: ret
    //     0x13949cc: ret             
    // 0x13949d0: r0 = false
    //     0x13949d0: add             x0, NULL, #0x30  ; false
    // 0x13949d4: LeaveFrame
    //     0x13949d4: mov             SP, fp
    //     0x13949d8: ldp             fp, lr, [SP], #0x10
    // 0x13949dc: ret
    //     0x13949dc: ret             
    // 0x13949e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13949e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13949e4: b               #0x139481c
  }
  _ getBack(/* No info */) {
    // ** addr: 0x13949e8, size: 0xb0
    // 0x13949e8: EnterFrame
    //     0x13949e8: stp             fp, lr, [SP, #-0x10]!
    //     0x13949ec: mov             fp, SP
    // 0x13949f0: AllocStack(0x8)
    //     0x13949f0: sub             SP, SP, #8
    // 0x13949f4: CheckStackOverflow
    //     0x13949f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13949f8: cmp             SP, x16
    //     0x13949fc: b.ls            #0x1394a90
    // 0x1394a00: r2 = false
    //     0x1394a00: add             x2, NULL, #0x30  ; false
    // 0x1394a04: r0 = showLoading()
    //     0x1394a04: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x1394a08: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1394a08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1394a0c: ldr             x0, [x0, #0x1c80]
    //     0x1394a10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1394a14: cmp             w0, w16
    //     0x1394a18: b.ne            #0x1394a24
    //     0x1394a1c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1394a20: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1394a24: r1 = Function '<anonymous closure>':.
    //     0x1394a24: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d868] AnonymousClosure: (0x1394804), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack (0x13949e8)
    //     0x1394a28: ldr             x1, [x1, #0x868]
    // 0x1394a2c: r2 = Null
    //     0x1394a2c: mov             x2, NULL
    // 0x1394a30: r0 = AllocateClosure()
    //     0x1394a30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1394a34: mov             x1, x0
    // 0x1394a38: r0 = GetNavigation.until()
    //     0x1394a38: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x1394a3c: r1 = <bool>
    //     0x1394a3c: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x1394a40: r0 = _Future()
    //     0x1394a40: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x1394a44: stur            x0, [fp, #-8]
    // 0x1394a48: StoreField: r0->field_b = rZR
    //     0x1394a48: stur            xzr, [x0, #0xb]
    // 0x1394a4c: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x1394a4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1394a50: ldr             x0, [x0, #0x778]
    //     0x1394a54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1394a58: cmp             w0, w16
    //     0x1394a5c: b.ne            #0x1394a68
    //     0x1394a60: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x1394a64: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x1394a68: mov             x1, x0
    // 0x1394a6c: ldur            x0, [fp, #-8]
    // 0x1394a70: StoreField: r0->field_13 = r1
    //     0x1394a70: stur            w1, [x0, #0x13]
    // 0x1394a74: mov             x1, x0
    // 0x1394a78: r2 = false
    //     0x1394a78: add             x2, NULL, #0x30  ; false
    // 0x1394a7c: r0 = _asyncComplete()
    //     0x1394a7c: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x1394a80: ldur            x0, [fp, #-8]
    // 0x1394a84: LeaveFrame
    //     0x1394a84: mov             SP, fp
    //     0x1394a88: ldp             fp, lr, [SP], #0x10
    // 0x1394a8c: ret
    //     0x1394a8c: ret             
    // 0x1394a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1394a90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1394a94: b               #0x1394a00
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x1394a98, size: 0x38
    // 0x1394a98: EnterFrame
    //     0x1394a98: stp             fp, lr, [SP, #-0x10]!
    //     0x1394a9c: mov             fp, SP
    // 0x1394aa0: ldr             x0, [fp, #0x10]
    // 0x1394aa4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1394aa4: ldur            w1, [x0, #0x17]
    // 0x1394aa8: DecompressPointer r1
    //     0x1394aa8: add             x1, x1, HEAP, lsl #32
    // 0x1394aac: CheckStackOverflow
    //     0x1394aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1394ab0: cmp             SP, x16
    //     0x1394ab4: b.ls            #0x1394ac8
    // 0x1394ab8: r0 = getBack()
    //     0x1394ab8: bl              #0x13949e8  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack
    // 0x1394abc: LeaveFrame
    //     0x1394abc: mov             SP, fp
    //     0x1394ac0: ldp             fp, lr, [SP], #0x10
    // 0x1394ac4: ret
    //     0x1394ac4: ret             
    // 0x1394ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1394ac8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1394acc: b               #0x1394ab8
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13951a4, size: 0x7c
    // 0x13951a4: EnterFrame
    //     0x13951a4: stp             fp, lr, [SP, #-0x10]!
    //     0x13951a8: mov             fp, SP
    // 0x13951ac: AllocStack(0x10)
    //     0x13951ac: sub             SP, SP, #0x10
    // 0x13951b0: SetupParameters()
    //     0x13951b0: ldr             x0, [fp, #0x10]
    //     0x13951b4: ldur            w2, [x0, #0x17]
    //     0x13951b8: add             x2, x2, HEAP, lsl #32
    //     0x13951bc: stur            x2, [fp, #-8]
    // 0x13951c0: CheckStackOverflow
    //     0x13951c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13951c4: cmp             SP, x16
    //     0x13951c8: b.ls            #0x1395218
    // 0x13951cc: LoadField: r1 = r2->field_f
    //     0x13951cc: ldur            w1, [x2, #0xf]
    // 0x13951d0: DecompressPointer r1
    //     0x13951d0: add             x1, x1, HEAP, lsl #32
    // 0x13951d4: r0 = controller()
    //     0x13951d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13951d8: mov             x2, x0
    // 0x13951dc: ldur            x0, [fp, #-8]
    // 0x13951e0: stur            x2, [fp, #-0x10]
    // 0x13951e4: LoadField: r1 = r0->field_f
    //     0x13951e4: ldur            w1, [x0, #0xf]
    // 0x13951e8: DecompressPointer r1
    //     0x13951e8: add             x1, x1, HEAP, lsl #32
    // 0x13951ec: r0 = controller()
    //     0x13951ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13951f0: LoadField: r1 = r0->field_ab
    //     0x13951f0: ldur            w1, [x0, #0xab]
    // 0x13951f4: DecompressPointer r1
    //     0x13951f4: add             x1, x1, HEAP, lsl #32
    // 0x13951f8: r0 = value()
    //     0x13951f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13951fc: ldur            x1, [fp, #-0x10]
    // 0x1395200: mov             x2, x0
    // 0x1395204: r0 = resendOtp()
    //     0x1395204: bl              #0x1395220  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::resendOtp
    // 0x1395208: r0 = Null
    //     0x1395208: mov             x0, NULL
    // 0x139520c: LeaveFrame
    //     0x139520c: mov             SP, fp
    //     0x1395210: ldp             fp, lr, [SP], #0x10
    // 0x1395214: ret
    //     0x1395214: ret             
    // 0x1395218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1395218: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x139521c: b               #0x13951cc
  }
  [closure] Null <anonymous closure>(dynamic, String, bool) {
    // ** addr: 0x13954a8, size: 0xd4
    // 0x13954a8: EnterFrame
    //     0x13954a8: stp             fp, lr, [SP, #-0x10]!
    //     0x13954ac: mov             fp, SP
    // 0x13954b0: AllocStack(0x10)
    //     0x13954b0: sub             SP, SP, #0x10
    // 0x13954b4: SetupParameters()
    //     0x13954b4: ldr             x0, [fp, #0x20]
    //     0x13954b8: ldur            w2, [x0, #0x17]
    //     0x13954bc: add             x2, x2, HEAP, lsl #32
    //     0x13954c0: stur            x2, [fp, #-8]
    // 0x13954c4: CheckStackOverflow
    //     0x13954c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13954c8: cmp             SP, x16
    //     0x13954cc: b.ls            #0x1395574
    // 0x13954d0: LoadField: r1 = r2->field_f
    //     0x13954d0: ldur            w1, [x2, #0xf]
    // 0x13954d4: DecompressPointer r1
    //     0x13954d4: add             x1, x1, HEAP, lsl #32
    // 0x13954d8: r0 = controller()
    //     0x13954d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13954dc: LoadField: r1 = r0->field_d3
    //     0x13954dc: ldur            w1, [x0, #0xd3]
    // 0x13954e0: DecompressPointer r1
    //     0x13954e0: add             x1, x1, HEAP, lsl #32
    // 0x13954e4: ldr             x2, [fp, #0x10]
    // 0x13954e8: r0 = value=()
    //     0x13954e8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13954ec: ldur            x0, [fp, #-8]
    // 0x13954f0: LoadField: r1 = r0->field_f
    //     0x13954f0: ldur            w1, [x0, #0xf]
    // 0x13954f4: DecompressPointer r1
    //     0x13954f4: add             x1, x1, HEAP, lsl #32
    // 0x13954f8: r0 = controller()
    //     0x13954f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13954fc: mov             x1, x0
    // 0x1395500: ldr             x0, [fp, #0x18]
    // 0x1395504: StoreField: r1->field_f3 = r0
    //     0x1395504: stur            w0, [x1, #0xf3]
    //     0x1395508: ldurb           w16, [x1, #-1]
    //     0x139550c: ldurb           w17, [x0, #-1]
    //     0x1395510: and             x16, x17, x16, lsr #2
    //     0x1395514: tst             x16, HEAP, lsr #32
    //     0x1395518: b.eq            #0x1395520
    //     0x139551c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1395520: ldur            x0, [fp, #-8]
    // 0x1395524: LoadField: r1 = r0->field_f
    //     0x1395524: ldur            w1, [x0, #0xf]
    // 0x1395528: DecompressPointer r1
    //     0x1395528: add             x1, x1, HEAP, lsl #32
    // 0x139552c: r0 = controller()
    //     0x139552c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1395530: mov             x2, x0
    // 0x1395534: ldur            x0, [fp, #-8]
    // 0x1395538: stur            x2, [fp, #-0x10]
    // 0x139553c: LoadField: r1 = r0->field_f
    //     0x139553c: ldur            w1, [x0, #0xf]
    // 0x1395540: DecompressPointer r1
    //     0x1395540: add             x1, x1, HEAP, lsl #32
    // 0x1395544: r0 = controller()
    //     0x1395544: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1395548: LoadField: r1 = r0->field_ab
    //     0x1395548: ldur            w1, [x0, #0xab]
    // 0x139554c: DecompressPointer r1
    //     0x139554c: add             x1, x1, HEAP, lsl #32
    // 0x1395550: r0 = value()
    //     0x1395550: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1395554: ldur            x1, [fp, #-0x10]
    // 0x1395558: mov             x2, x0
    // 0x139555c: ldr             x3, [fp, #0x18]
    // 0x1395560: r0 = verifyOtp()
    //     0x1395560: bl              #0x12ebf14  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::verifyOtp
    // 0x1395564: r0 = Null
    //     0x1395564: mov             x0, NULL
    // 0x1395568: LeaveFrame
    //     0x1395568: mov             SP, fp
    //     0x139556c: ldp             fp, lr, [SP], #0x10
    // 0x1395570: ret
    //     0x1395570: ret             
    // 0x1395574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1395574: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1395578: b               #0x13954d0
  }
  _ body(/* No info */) {
    // ** addr: 0x1500d5c, size: 0x218
    // 0x1500d5c: EnterFrame
    //     0x1500d5c: stp             fp, lr, [SP, #-0x10]!
    //     0x1500d60: mov             fp, SP
    // 0x1500d64: AllocStack(0x30)
    //     0x1500d64: sub             SP, SP, #0x30
    // 0x1500d68: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1500d68: stur            x1, [fp, #-8]
    //     0x1500d6c: stur            x2, [fp, #-0x10]
    // 0x1500d70: CheckStackOverflow
    //     0x1500d70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1500d74: cmp             SP, x16
    //     0x1500d78: b.ls            #0x1500f6c
    // 0x1500d7c: r1 = 2
    //     0x1500d7c: movz            x1, #0x2
    // 0x1500d80: r0 = AllocateContext()
    //     0x1500d80: bl              #0x16f6108  ; AllocateContextStub
    // 0x1500d84: ldur            x1, [fp, #-8]
    // 0x1500d88: stur            x0, [fp, #-0x18]
    // 0x1500d8c: StoreField: r0->field_f = r1
    //     0x1500d8c: stur            w1, [x0, #0xf]
    // 0x1500d90: ldur            x2, [fp, #-0x10]
    // 0x1500d94: StoreField: r0->field_13 = r2
    //     0x1500d94: stur            w2, [x0, #0x13]
    // 0x1500d98: r0 = Obx()
    //     0x1500d98: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1500d9c: ldur            x2, [fp, #-0x18]
    // 0x1500da0: r1 = Function '<anonymous closure>':.
    //     0x1500da0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d838] AnonymousClosure: (0x1501230), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x1500da4: ldr             x1, [x1, #0x838]
    // 0x1500da8: stur            x0, [fp, #-0x10]
    // 0x1500dac: r0 = AllocateClosure()
    //     0x1500dac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500db0: mov             x1, x0
    // 0x1500db4: ldur            x0, [fp, #-0x10]
    // 0x1500db8: StoreField: r0->field_b = r1
    //     0x1500db8: stur            w1, [x0, #0xb]
    // 0x1500dbc: ldur            x1, [fp, #-8]
    // 0x1500dc0: r0 = controller()
    //     0x1500dc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1500dc4: LoadField: r1 = r0->field_ab
    //     0x1500dc4: ldur            w1, [x0, #0xab]
    // 0x1500dc8: DecompressPointer r1
    //     0x1500dc8: add             x1, x1, HEAP, lsl #32
    // 0x1500dcc: r0 = value()
    //     0x1500dcc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1500dd0: ldur            x2, [fp, #-0x18]
    // 0x1500dd4: r1 = Function '<anonymous closure>':.
    //     0x1500dd4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d840] AnonymousClosure: (0x13954a8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x1500dd8: ldr             x1, [x1, #0x840]
    // 0x1500ddc: stur            x0, [fp, #-0x20]
    // 0x1500de0: r0 = AllocateClosure()
    //     0x1500de0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500de4: stur            x0, [fp, #-0x28]
    // 0x1500de8: r0 = CheckoutOtpWidget()
    //     0x1500de8: bl              #0x139a398  ; AllocateCheckoutOtpWidgetStub -> CheckoutOtpWidget (size=0x20)
    // 0x1500dec: mov             x3, x0
    // 0x1500df0: ldur            x0, [fp, #-0x28]
    // 0x1500df4: stur            x3, [fp, #-0x30]
    // 0x1500df8: StoreField: r3->field_b = r0
    //     0x1500df8: stur            w0, [x3, #0xb]
    // 0x1500dfc: ldur            x2, [fp, #-0x18]
    // 0x1500e00: r1 = Function '<anonymous closure>':.
    //     0x1500e00: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d848] AnonymousClosure: (0x13951a4), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x1500e04: ldr             x1, [x1, #0x848]
    // 0x1500e08: r0 = AllocateClosure()
    //     0x1500e08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500e0c: mov             x1, x0
    // 0x1500e10: ldur            x0, [fp, #-0x30]
    // 0x1500e14: StoreField: r0->field_f = r1
    //     0x1500e14: stur            w1, [x0, #0xf]
    // 0x1500e18: r1 = Function '<anonymous closure>':.
    //     0x1500e18: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d850] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x1500e1c: ldr             x1, [x1, #0x850]
    // 0x1500e20: r2 = Null
    //     0x1500e20: mov             x2, NULL
    // 0x1500e24: r0 = AllocateClosure()
    //     0x1500e24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500e28: mov             x1, x0
    // 0x1500e2c: ldur            x0, [fp, #-0x30]
    // 0x1500e30: StoreField: r0->field_13 = r1
    //     0x1500e30: stur            w1, [x0, #0x13]
    // 0x1500e34: ldur            x1, [fp, #-0x20]
    // 0x1500e38: StoreField: r0->field_1b = r1
    //     0x1500e38: stur            w1, [x0, #0x1b]
    // 0x1500e3c: ldur            x2, [fp, #-0x18]
    // 0x1500e40: r1 = Function '<anonymous closure>':.
    //     0x1500e40: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d858] AnonymousClosure: (0x1500f74), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x1500e44: ldr             x1, [x1, #0x858]
    // 0x1500e48: r0 = AllocateClosure()
    //     0x1500e48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500e4c: mov             x1, x0
    // 0x1500e50: ldur            x0, [fp, #-0x30]
    // 0x1500e54: ArrayStore: r0[0] = r1  ; List_4
    //     0x1500e54: stur            w1, [x0, #0x17]
    // 0x1500e58: r1 = Null
    //     0x1500e58: mov             x1, NULL
    // 0x1500e5c: r2 = 4
    //     0x1500e5c: movz            x2, #0x4
    // 0x1500e60: r0 = AllocateArray()
    //     0x1500e60: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1500e64: mov             x2, x0
    // 0x1500e68: ldur            x0, [fp, #-0x10]
    // 0x1500e6c: stur            x2, [fp, #-0x18]
    // 0x1500e70: StoreField: r2->field_f = r0
    //     0x1500e70: stur            w0, [x2, #0xf]
    // 0x1500e74: ldur            x0, [fp, #-0x30]
    // 0x1500e78: StoreField: r2->field_13 = r0
    //     0x1500e78: stur            w0, [x2, #0x13]
    // 0x1500e7c: r1 = <Widget>
    //     0x1500e7c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1500e80: r0 = AllocateGrowableArray()
    //     0x1500e80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1500e84: mov             x1, x0
    // 0x1500e88: ldur            x0, [fp, #-0x18]
    // 0x1500e8c: stur            x1, [fp, #-0x10]
    // 0x1500e90: StoreField: r1->field_f = r0
    //     0x1500e90: stur            w0, [x1, #0xf]
    // 0x1500e94: r0 = 4
    //     0x1500e94: movz            x0, #0x4
    // 0x1500e98: StoreField: r1->field_b = r0
    //     0x1500e98: stur            w0, [x1, #0xb]
    // 0x1500e9c: r0 = Column()
    //     0x1500e9c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1500ea0: mov             x1, x0
    // 0x1500ea4: r0 = Instance_Axis
    //     0x1500ea4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1500ea8: stur            x1, [fp, #-0x18]
    // 0x1500eac: StoreField: r1->field_f = r0
    //     0x1500eac: stur            w0, [x1, #0xf]
    // 0x1500eb0: r0 = Instance_MainAxisAlignment
    //     0x1500eb0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1500eb4: ldr             x0, [x0, #0xa08]
    // 0x1500eb8: StoreField: r1->field_13 = r0
    //     0x1500eb8: stur            w0, [x1, #0x13]
    // 0x1500ebc: r0 = Instance_MainAxisSize
    //     0x1500ebc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1500ec0: ldr             x0, [x0, #0xa10]
    // 0x1500ec4: ArrayStore: r1[0] = r0  ; List_4
    //     0x1500ec4: stur            w0, [x1, #0x17]
    // 0x1500ec8: r0 = Instance_CrossAxisAlignment
    //     0x1500ec8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1500ecc: ldr             x0, [x0, #0xa18]
    // 0x1500ed0: StoreField: r1->field_1b = r0
    //     0x1500ed0: stur            w0, [x1, #0x1b]
    // 0x1500ed4: r0 = Instance_VerticalDirection
    //     0x1500ed4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1500ed8: ldr             x0, [x0, #0xa20]
    // 0x1500edc: StoreField: r1->field_23 = r0
    //     0x1500edc: stur            w0, [x1, #0x23]
    // 0x1500ee0: r0 = Instance_Clip
    //     0x1500ee0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1500ee4: ldr             x0, [x0, #0x38]
    // 0x1500ee8: StoreField: r1->field_2b = r0
    //     0x1500ee8: stur            w0, [x1, #0x2b]
    // 0x1500eec: StoreField: r1->field_2f = rZR
    //     0x1500eec: stur            xzr, [x1, #0x2f]
    // 0x1500ef0: ldur            x0, [fp, #-0x10]
    // 0x1500ef4: StoreField: r1->field_b = r0
    //     0x1500ef4: stur            w0, [x1, #0xb]
    // 0x1500ef8: r0 = SafeArea()
    //     0x1500ef8: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1500efc: mov             x1, x0
    // 0x1500f00: r0 = true
    //     0x1500f00: add             x0, NULL, #0x20  ; true
    // 0x1500f04: stur            x1, [fp, #-0x10]
    // 0x1500f08: StoreField: r1->field_b = r0
    //     0x1500f08: stur            w0, [x1, #0xb]
    // 0x1500f0c: StoreField: r1->field_f = r0
    //     0x1500f0c: stur            w0, [x1, #0xf]
    // 0x1500f10: StoreField: r1->field_13 = r0
    //     0x1500f10: stur            w0, [x1, #0x13]
    // 0x1500f14: ArrayStore: r1[0] = r0  ; List_4
    //     0x1500f14: stur            w0, [x1, #0x17]
    // 0x1500f18: r0 = Instance_EdgeInsets
    //     0x1500f18: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1500f1c: StoreField: r1->field_1b = r0
    //     0x1500f1c: stur            w0, [x1, #0x1b]
    // 0x1500f20: r0 = false
    //     0x1500f20: add             x0, NULL, #0x30  ; false
    // 0x1500f24: StoreField: r1->field_1f = r0
    //     0x1500f24: stur            w0, [x1, #0x1f]
    // 0x1500f28: ldur            x0, [fp, #-0x18]
    // 0x1500f2c: StoreField: r1->field_23 = r0
    //     0x1500f2c: stur            w0, [x1, #0x23]
    // 0x1500f30: r0 = WillPopScope()
    //     0x1500f30: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1500f34: mov             x3, x0
    // 0x1500f38: ldur            x0, [fp, #-0x10]
    // 0x1500f3c: stur            x3, [fp, #-0x18]
    // 0x1500f40: StoreField: r3->field_b = r0
    //     0x1500f40: stur            w0, [x3, #0xb]
    // 0x1500f44: ldur            x2, [fp, #-8]
    // 0x1500f48: r1 = Function 'getBack':.
    //     0x1500f48: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d860] AnonymousClosure: (0x1394a98), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack (0x13949e8)
    //     0x1500f4c: ldr             x1, [x1, #0x860]
    // 0x1500f50: r0 = AllocateClosure()
    //     0x1500f50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500f54: mov             x1, x0
    // 0x1500f58: ldur            x0, [fp, #-0x18]
    // 0x1500f5c: StoreField: r0->field_f = r1
    //     0x1500f5c: stur            w1, [x0, #0xf]
    // 0x1500f60: LeaveFrame
    //     0x1500f60: mov             SP, fp
    //     0x1500f64: ldp             fp, lr, [SP], #0x10
    // 0x1500f68: ret
    //     0x1500f68: ret             
    // 0x1500f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1500f6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1500f70: b               #0x1500d7c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1500f74, size: 0x84
    // 0x1500f74: EnterFrame
    //     0x1500f74: stp             fp, lr, [SP, #-0x10]!
    //     0x1500f78: mov             fp, SP
    // 0x1500f7c: AllocStack(0x10)
    //     0x1500f7c: sub             SP, SP, #0x10
    // 0x1500f80: SetupParameters()
    //     0x1500f80: ldr             x0, [fp, #0x10]
    //     0x1500f84: ldur            w2, [x0, #0x17]
    //     0x1500f88: add             x2, x2, HEAP, lsl #32
    //     0x1500f8c: stur            x2, [fp, #-0x10]
    // 0x1500f90: CheckStackOverflow
    //     0x1500f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1500f94: cmp             SP, x16
    //     0x1500f98: b.ls            #0x1500ff0
    // 0x1500f9c: LoadField: r0 = r2->field_13
    //     0x1500f9c: ldur            w0, [x2, #0x13]
    // 0x1500fa0: DecompressPointer r0
    //     0x1500fa0: add             x0, x0, HEAP, lsl #32
    // 0x1500fa4: stur            x0, [fp, #-8]
    // 0x1500fa8: LoadField: r1 = r2->field_f
    //     0x1500fa8: ldur            w1, [x2, #0xf]
    // 0x1500fac: DecompressPointer r1
    //     0x1500fac: add             x1, x1, HEAP, lsl #32
    // 0x1500fb0: r0 = controller()
    //     0x1500fb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1500fb4: LoadField: r1 = r0->field_ab
    //     0x1500fb4: ldur            w1, [x0, #0xab]
    // 0x1500fb8: DecompressPointer r1
    //     0x1500fb8: add             x1, x1, HEAP, lsl #32
    // 0x1500fbc: r0 = value()
    //     0x1500fbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1500fc0: mov             x1, x0
    // 0x1500fc4: ldur            x0, [fp, #-0x10]
    // 0x1500fc8: LoadField: r2 = r0->field_f
    //     0x1500fc8: ldur            w2, [x0, #0xf]
    // 0x1500fcc: DecompressPointer r2
    //     0x1500fcc: add             x2, x2, HEAP, lsl #32
    // 0x1500fd0: mov             x3, x1
    // 0x1500fd4: mov             x1, x2
    // 0x1500fd8: ldur            x2, [fp, #-8]
    // 0x1500fdc: r0 = _showEditPhoneBottomSheet()
    //     0x1500fdc: bl              #0x1500ff8  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet
    // 0x1500fe0: r0 = Null
    //     0x1500fe0: mov             x0, NULL
    // 0x1500fe4: LeaveFrame
    //     0x1500fe4: mov             SP, fp
    //     0x1500fe8: ldp             fp, lr, [SP], #0x10
    // 0x1500fec: ret
    //     0x1500fec: ret             
    // 0x1500ff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1500ff0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1500ff4: b               #0x1500f9c
  }
  _ _showEditPhoneBottomSheet(/* No info */) {
    // ** addr: 0x1500ff8, size: 0x84
    // 0x1500ff8: EnterFrame
    //     0x1500ff8: stp             fp, lr, [SP, #-0x10]!
    //     0x1500ffc: mov             fp, SP
    // 0x1501000: AllocStack(0x38)
    //     0x1501000: sub             SP, SP, #0x38
    // 0x1501004: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x1501004: stur            x1, [fp, #-8]
    //     0x1501008: stur            x2, [fp, #-0x10]
    //     0x150100c: stur            x3, [fp, #-0x18]
    // 0x1501010: CheckStackOverflow
    //     0x1501010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1501014: cmp             SP, x16
    //     0x1501018: b.ls            #0x1501074
    // 0x150101c: r1 = 2
    //     0x150101c: movz            x1, #0x2
    // 0x1501020: r0 = AllocateContext()
    //     0x1501020: bl              #0x16f6108  ; AllocateContextStub
    // 0x1501024: mov             x1, x0
    // 0x1501028: ldur            x0, [fp, #-8]
    // 0x150102c: StoreField: r1->field_f = r0
    //     0x150102c: stur            w0, [x1, #0xf]
    // 0x1501030: ldur            x0, [fp, #-0x18]
    // 0x1501034: StoreField: r1->field_13 = r0
    //     0x1501034: stur            w0, [x1, #0x13]
    // 0x1501038: mov             x2, x1
    // 0x150103c: r1 = Function '<anonymous closure>':.
    //     0x150103c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d870] AnonymousClosure: (0x150107c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet (0x1500ff8)
    //     0x1501040: ldr             x1, [x1, #0x870]
    // 0x1501044: r0 = AllocateClosure()
    //     0x1501044: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1501048: stp             x0, NULL, [SP, #0x10]
    // 0x150104c: ldur            x16, [fp, #-0x10]
    // 0x1501050: r30 = true
    //     0x1501050: add             lr, NULL, #0x20  ; true
    // 0x1501054: stp             lr, x16, [SP]
    // 0x1501058: r4 = const [0x1, 0x3, 0x3, 0x2, isScrollControlled, 0x2, null]
    //     0x1501058: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d878] List(7) [0x1, 0x3, 0x3, 0x2, "isScrollControlled", 0x2, Null]
    //     0x150105c: ldr             x4, [x4, #0x878]
    // 0x1501060: r0 = showModalBottomSheet()
    //     0x1501060: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1501064: r0 = Null
    //     0x1501064: mov             x0, NULL
    // 0x1501068: LeaveFrame
    //     0x1501068: mov             SP, fp
    //     0x150106c: ldp             fp, lr, [SP], #0x10
    // 0x1501070: ret
    //     0x1501070: ret             
    // 0x1501074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1501074: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1501078: b               #0x150101c
  }
  [closure] AnimatedPadding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x150107c, size: 0xe0
    // 0x150107c: EnterFrame
    //     0x150107c: stp             fp, lr, [SP, #-0x10]!
    //     0x1501080: mov             fp, SP
    // 0x1501084: AllocStack(0x28)
    //     0x1501084: sub             SP, SP, #0x28
    // 0x1501088: SetupParameters()
    //     0x1501088: ldr             x0, [fp, #0x18]
    //     0x150108c: ldur            w2, [x0, #0x17]
    //     0x1501090: add             x2, x2, HEAP, lsl #32
    //     0x1501094: stur            x2, [fp, #-8]
    // 0x1501098: CheckStackOverflow
    //     0x1501098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x150109c: cmp             SP, x16
    //     0x15010a0: b.ls            #0x1501154
    // 0x15010a4: ldr             x1, [fp, #0x10]
    // 0x15010a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15010a8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15010ac: r0 = _of()
    //     0x15010ac: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x15010b0: LoadField: r1 = r0->field_23
    //     0x15010b0: ldur            w1, [x0, #0x23]
    // 0x15010b4: DecompressPointer r1
    //     0x15010b4: add             x1, x1, HEAP, lsl #32
    // 0x15010b8: LoadField: d0 = r1->field_1f
    //     0x15010b8: ldur            d0, [x1, #0x1f]
    // 0x15010bc: stur            d0, [fp, #-0x28]
    // 0x15010c0: r0 = EdgeInsets()
    //     0x15010c0: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x15010c4: stur            x0, [fp, #-0x18]
    // 0x15010c8: StoreField: r0->field_7 = rZR
    //     0x15010c8: stur            xzr, [x0, #7]
    // 0x15010cc: StoreField: r0->field_f = rZR
    //     0x15010cc: stur            xzr, [x0, #0xf]
    // 0x15010d0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x15010d0: stur            xzr, [x0, #0x17]
    // 0x15010d4: ldur            d0, [fp, #-0x28]
    // 0x15010d8: StoreField: r0->field_1f = d0
    //     0x15010d8: stur            d0, [x0, #0x1f]
    // 0x15010dc: ldur            x2, [fp, #-8]
    // 0x15010e0: LoadField: r1 = r2->field_13
    //     0x15010e0: ldur            w1, [x2, #0x13]
    // 0x15010e4: DecompressPointer r1
    //     0x15010e4: add             x1, x1, HEAP, lsl #32
    // 0x15010e8: stur            x1, [fp, #-0x10]
    // 0x15010ec: r0 = EditPhoneBottomSheet()
    //     0x15010ec: bl              #0x150115c  ; AllocateEditPhoneBottomSheetStub -> EditPhoneBottomSheet (size=0x14)
    // 0x15010f0: mov             x3, x0
    // 0x15010f4: ldur            x0, [fp, #-0x10]
    // 0x15010f8: stur            x3, [fp, #-0x20]
    // 0x15010fc: StoreField: r3->field_b = r0
    //     0x15010fc: stur            w0, [x3, #0xb]
    // 0x1501100: ldur            x2, [fp, #-8]
    // 0x1501104: r1 = Function '<anonymous closure>':.
    //     0x1501104: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d880] AnonymousClosure: (0x1501168), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet (0x1500ff8)
    //     0x1501108: ldr             x1, [x1, #0x880]
    // 0x150110c: r0 = AllocateClosure()
    //     0x150110c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1501110: mov             x1, x0
    // 0x1501114: ldur            x0, [fp, #-0x20]
    // 0x1501118: StoreField: r0->field_f = r1
    //     0x1501118: stur            w1, [x0, #0xf]
    // 0x150111c: r0 = AnimatedPadding()
    //     0x150111c: bl              #0x12a22b8  ; AllocateAnimatedPaddingStub -> AnimatedPadding (size=0x20)
    // 0x1501120: ldur            x1, [fp, #-0x18]
    // 0x1501124: ArrayStore: r0[0] = r1  ; List_4
    //     0x1501124: stur            w1, [x0, #0x17]
    // 0x1501128: ldur            x1, [fp, #-0x20]
    // 0x150112c: StoreField: r0->field_1b = r1
    //     0x150112c: stur            w1, [x0, #0x1b]
    // 0x1501130: r1 = Instance_Cubic
    //     0x1501130: add             x1, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0x1501134: ldr             x1, [x1, #0x2b0]
    // 0x1501138: StoreField: r0->field_b = r1
    //     0x1501138: stur            w1, [x0, #0xb]
    // 0x150113c: r1 = Instance_Duration
    //     0x150113c: add             x1, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x1501140: ldr             x1, [x1, #0xf00]
    // 0x1501144: StoreField: r0->field_f = r1
    //     0x1501144: stur            w1, [x0, #0xf]
    // 0x1501148: LeaveFrame
    //     0x1501148: mov             SP, fp
    //     0x150114c: ldp             fp, lr, [SP], #0x10
    // 0x1501150: ret
    //     0x1501150: ret             
    // 0x1501154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1501154: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1501158: b               #0x15010a4
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x1501168, size: 0xc8
    // 0x1501168: EnterFrame
    //     0x1501168: stp             fp, lr, [SP, #-0x10]!
    //     0x150116c: mov             fp, SP
    // 0x1501170: AllocStack(0x18)
    //     0x1501170: sub             SP, SP, #0x18
    // 0x1501174: SetupParameters(CheckoutRequestOtpPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1501174: stur            NULL, [fp, #-8]
    //     0x1501178: movz            x0, #0
    //     0x150117c: add             x1, fp, w0, sxtw #2
    //     0x1501180: ldr             x1, [x1, #0x18]
    //     0x1501184: add             x2, fp, w0, sxtw #2
    //     0x1501188: ldr             x2, [x2, #0x10]
    //     0x150118c: stur            x2, [fp, #-0x18]
    //     0x1501190: ldur            w3, [x1, #0x17]
    //     0x1501194: add             x3, x3, HEAP, lsl #32
    //     0x1501198: stur            x3, [fp, #-0x10]
    // 0x150119c: CheckStackOverflow
    //     0x150119c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15011a0: cmp             SP, x16
    //     0x15011a4: b.ls            #0x1501228
    // 0x15011a8: InitAsync() -> Future<Null?>
    //     0x15011a8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x15011ac: bl              #0x6326e0  ; InitAsyncStub
    // 0x15011b0: ldur            x0, [fp, #-0x10]
    // 0x15011b4: LoadField: r1 = r0->field_f
    //     0x15011b4: ldur            w1, [x0, #0xf]
    // 0x15011b8: DecompressPointer r1
    //     0x15011b8: add             x1, x1, HEAP, lsl #32
    // 0x15011bc: r0 = controller()
    //     0x15011bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15011c0: mov             x1, x0
    // 0x15011c4: ldur            x2, [fp, #-0x18]
    // 0x15011c8: r0 = phoneNumber=()
    //     0x15011c8: bl              #0x1394d9c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::phoneNumber=
    // 0x15011cc: ldur            x0, [fp, #-0x10]
    // 0x15011d0: LoadField: r1 = r0->field_f
    //     0x15011d0: ldur            w1, [x0, #0xf]
    // 0x15011d4: DecompressPointer r1
    //     0x15011d4: add             x1, x1, HEAP, lsl #32
    // 0x15011d8: r0 = controller()
    //     0x15011d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15011dc: LoadField: r1 = r0->field_fb
    //     0x15011dc: ldur            w1, [x0, #0xfb]
    // 0x15011e0: DecompressPointer r1
    //     0x15011e0: add             x1, x1, HEAP, lsl #32
    // 0x15011e4: r2 = true
    //     0x15011e4: add             x2, NULL, #0x20  ; true
    // 0x15011e8: r0 = value=()
    //     0x15011e8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15011ec: ldur            x0, [fp, #-0x10]
    // 0x15011f0: LoadField: r1 = r0->field_f
    //     0x15011f0: ldur            w1, [x0, #0xf]
    // 0x15011f4: DecompressPointer r1
    //     0x15011f4: add             x1, x1, HEAP, lsl #32
    // 0x15011f8: r0 = controller()
    //     0x15011f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15011fc: mov             x1, x0
    // 0x1501200: ldur            x2, [fp, #-0x18]
    // 0x1501204: r0 = updatePrefPhoneNumber()
    //     0x1501204: bl              #0x139513c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::updatePrefPhoneNumber
    // 0x1501208: ldur            x0, [fp, #-0x10]
    // 0x150120c: LoadField: r1 = r0->field_f
    //     0x150120c: ldur            w1, [x0, #0xf]
    // 0x1501210: DecompressPointer r1
    //     0x1501210: add             x1, x1, HEAP, lsl #32
    // 0x1501214: r0 = controller()
    //     0x1501214: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1501218: mov             x1, x0
    // 0x150121c: r0 = getOtp()
    //     0x150121c: bl              #0x1394f64  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::getOtp
    // 0x1501220: r0 = Null
    //     0x1501220: mov             x0, NULL
    // 0x1501224: r0 = ReturnAsyncNotFuture()
    //     0x1501224: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1501228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1501228: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x150122c: b               #0x15011a8
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x1501230, size: 0x64
    // 0x1501230: EnterFrame
    //     0x1501230: stp             fp, lr, [SP, #-0x10]!
    //     0x1501234: mov             fp, SP
    // 0x1501238: AllocStack(0x8)
    //     0x1501238: sub             SP, SP, #8
    // 0x150123c: SetupParameters()
    //     0x150123c: ldr             x0, [fp, #0x10]
    //     0x1501240: ldur            w1, [x0, #0x17]
    //     0x1501244: add             x1, x1, HEAP, lsl #32
    // 0x1501248: CheckStackOverflow
    //     0x1501248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x150124c: cmp             SP, x16
    //     0x1501250: b.ls            #0x150128c
    // 0x1501254: LoadField: r0 = r1->field_f
    //     0x1501254: ldur            w0, [x1, #0xf]
    // 0x1501258: DecompressPointer r0
    //     0x1501258: add             x0, x0, HEAP, lsl #32
    // 0x150125c: mov             x1, x0
    // 0x1501260: r0 = controller()
    //     0x1501260: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1501264: mov             x1, x0
    // 0x1501268: r0 = offerParams()
    //     0x1501268: bl              #0x91dfa4  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::offerParams
    // 0x150126c: stur            x0, [fp, #-8]
    // 0x1501270: r0 = CheckoutBreadCrumb()
    //     0x1501270: bl              #0x1391e94  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x1501274: ldur            x1, [fp, #-8]
    // 0x1501278: StoreField: r0->field_b = r1
    //     0x1501278: stur            w1, [x0, #0xb]
    // 0x150127c: StoreField: r0->field_f = rZR
    //     0x150127c: stur            xzr, [x0, #0xf]
    // 0x1501280: LeaveFrame
    //     0x1501280: mov             SP, fp
    //     0x1501284: ldp             fp, lr, [SP], #0x10
    // 0x1501288: ret
    //     0x1501288: ret             
    // 0x150128c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150128c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1501290: b               #0x1501254
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15cd5f8, size: 0x420
    // 0x15cd5f8: EnterFrame
    //     0x15cd5f8: stp             fp, lr, [SP, #-0x10]!
    //     0x15cd5fc: mov             fp, SP
    // 0x15cd600: AllocStack(0x40)
    //     0x15cd600: sub             SP, SP, #0x40
    // 0x15cd604: SetupParameters()
    //     0x15cd604: ldr             x0, [fp, #0x10]
    //     0x15cd608: ldur            w2, [x0, #0x17]
    //     0x15cd60c: add             x2, x2, HEAP, lsl #32
    //     0x15cd610: stur            x2, [fp, #-8]
    // 0x15cd614: CheckStackOverflow
    //     0x15cd614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cd618: cmp             SP, x16
    //     0x15cd61c: b.ls            #0x15cda10
    // 0x15cd620: LoadField: r1 = r2->field_f
    //     0x15cd620: ldur            w1, [x2, #0xf]
    // 0x15cd624: DecompressPointer r1
    //     0x15cd624: add             x1, x1, HEAP, lsl #32
    // 0x15cd628: r0 = controller()
    //     0x15cd628: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cd62c: LoadField: r1 = r0->field_5b
    //     0x15cd62c: ldur            w1, [x0, #0x5b]
    // 0x15cd630: DecompressPointer r1
    //     0x15cd630: add             x1, x1, HEAP, lsl #32
    // 0x15cd634: r0 = value()
    //     0x15cd634: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cd638: LoadField: r1 = r0->field_3f
    //     0x15cd638: ldur            w1, [x0, #0x3f]
    // 0x15cd63c: DecompressPointer r1
    //     0x15cd63c: add             x1, x1, HEAP, lsl #32
    // 0x15cd640: cmp             w1, NULL
    // 0x15cd644: b.ne            #0x15cd650
    // 0x15cd648: r0 = Null
    //     0x15cd648: mov             x0, NULL
    // 0x15cd64c: b               #0x15cd658
    // 0x15cd650: LoadField: r0 = r1->field_f
    //     0x15cd650: ldur            w0, [x1, #0xf]
    // 0x15cd654: DecompressPointer r0
    //     0x15cd654: add             x0, x0, HEAP, lsl #32
    // 0x15cd658: r1 = LoadClassIdInstr(r0)
    //     0x15cd658: ldur            x1, [x0, #-1]
    //     0x15cd65c: ubfx            x1, x1, #0xc, #0x14
    // 0x15cd660: r16 = "image_text"
    //     0x15cd660: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cd664: ldr             x16, [x16, #0xa88]
    // 0x15cd668: stp             x16, x0, [SP]
    // 0x15cd66c: mov             x0, x1
    // 0x15cd670: mov             lr, x0
    // 0x15cd674: ldr             lr, [x21, lr, lsl #3]
    // 0x15cd678: blr             lr
    // 0x15cd67c: tbnz            w0, #4, #0x15cd688
    // 0x15cd680: r2 = true
    //     0x15cd680: add             x2, NULL, #0x20  ; true
    // 0x15cd684: b               #0x15cd6e8
    // 0x15cd688: ldur            x0, [fp, #-8]
    // 0x15cd68c: LoadField: r1 = r0->field_f
    //     0x15cd68c: ldur            w1, [x0, #0xf]
    // 0x15cd690: DecompressPointer r1
    //     0x15cd690: add             x1, x1, HEAP, lsl #32
    // 0x15cd694: r0 = controller()
    //     0x15cd694: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cd698: LoadField: r1 = r0->field_5b
    //     0x15cd698: ldur            w1, [x0, #0x5b]
    // 0x15cd69c: DecompressPointer r1
    //     0x15cd69c: add             x1, x1, HEAP, lsl #32
    // 0x15cd6a0: r0 = value()
    //     0x15cd6a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cd6a4: LoadField: r1 = r0->field_3f
    //     0x15cd6a4: ldur            w1, [x0, #0x3f]
    // 0x15cd6a8: DecompressPointer r1
    //     0x15cd6a8: add             x1, x1, HEAP, lsl #32
    // 0x15cd6ac: cmp             w1, NULL
    // 0x15cd6b0: b.ne            #0x15cd6bc
    // 0x15cd6b4: r0 = Null
    //     0x15cd6b4: mov             x0, NULL
    // 0x15cd6b8: b               #0x15cd6c4
    // 0x15cd6bc: LoadField: r0 = r1->field_f
    //     0x15cd6bc: ldur            w0, [x1, #0xf]
    // 0x15cd6c0: DecompressPointer r0
    //     0x15cd6c0: add             x0, x0, HEAP, lsl #32
    // 0x15cd6c4: r1 = LoadClassIdInstr(r0)
    //     0x15cd6c4: ldur            x1, [x0, #-1]
    //     0x15cd6c8: ubfx            x1, x1, #0xc, #0x14
    // 0x15cd6cc: r16 = "image"
    //     0x15cd6cc: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15cd6d0: stp             x16, x0, [SP]
    // 0x15cd6d4: mov             x0, x1
    // 0x15cd6d8: mov             lr, x0
    // 0x15cd6dc: ldr             lr, [x21, lr, lsl #3]
    // 0x15cd6e0: blr             lr
    // 0x15cd6e4: mov             x2, x0
    // 0x15cd6e8: ldur            x0, [fp, #-8]
    // 0x15cd6ec: stur            x2, [fp, #-0x10]
    // 0x15cd6f0: LoadField: r1 = r0->field_f
    //     0x15cd6f0: ldur            w1, [x0, #0xf]
    // 0x15cd6f4: DecompressPointer r1
    //     0x15cd6f4: add             x1, x1, HEAP, lsl #32
    // 0x15cd6f8: r0 = controller()
    //     0x15cd6f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cd6fc: LoadField: r1 = r0->field_5b
    //     0x15cd6fc: ldur            w1, [x0, #0x5b]
    // 0x15cd700: DecompressPointer r1
    //     0x15cd700: add             x1, x1, HEAP, lsl #32
    // 0x15cd704: r0 = value()
    //     0x15cd704: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cd708: LoadField: r1 = r0->field_27
    //     0x15cd708: ldur            w1, [x0, #0x27]
    // 0x15cd70c: DecompressPointer r1
    //     0x15cd70c: add             x1, x1, HEAP, lsl #32
    // 0x15cd710: cmp             w1, NULL
    // 0x15cd714: b.ne            #0x15cd720
    // 0x15cd718: r2 = ""
    //     0x15cd718: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cd71c: b               #0x15cd724
    // 0x15cd720: mov             x2, x1
    // 0x15cd724: ldur            x0, [fp, #-8]
    // 0x15cd728: ldur            x1, [fp, #-0x10]
    // 0x15cd72c: stur            x2, [fp, #-0x18]
    // 0x15cd730: r0 = CachedNetworkImage()
    //     0x15cd730: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15cd734: stur            x0, [fp, #-0x20]
    // 0x15cd738: r16 = Instance_BoxFit
    //     0x15cd738: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cd73c: ldr             x16, [x16, #0xb18]
    // 0x15cd740: r30 = 50.000000
    //     0x15cd740: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cd744: ldr             lr, [lr, #0xa90]
    // 0x15cd748: stp             lr, x16, [SP, #8]
    // 0x15cd74c: r16 = 50.000000
    //     0x15cd74c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cd750: ldr             x16, [x16, #0xa90]
    // 0x15cd754: str             x16, [SP]
    // 0x15cd758: mov             x1, x0
    // 0x15cd75c: ldur            x2, [fp, #-0x18]
    // 0x15cd760: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15cd760: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15cd764: ldr             x4, [x4, #0x8e0]
    // 0x15cd768: r0 = CachedNetworkImage()
    //     0x15cd768: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15cd76c: r0 = Visibility()
    //     0x15cd76c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cd770: mov             x2, x0
    // 0x15cd774: ldur            x0, [fp, #-0x20]
    // 0x15cd778: stur            x2, [fp, #-0x18]
    // 0x15cd77c: StoreField: r2->field_b = r0
    //     0x15cd77c: stur            w0, [x2, #0xb]
    // 0x15cd780: r0 = Instance_SizedBox
    //     0x15cd780: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cd784: StoreField: r2->field_f = r0
    //     0x15cd784: stur            w0, [x2, #0xf]
    // 0x15cd788: ldur            x1, [fp, #-0x10]
    // 0x15cd78c: StoreField: r2->field_13 = r1
    //     0x15cd78c: stur            w1, [x2, #0x13]
    // 0x15cd790: r3 = false
    //     0x15cd790: add             x3, NULL, #0x30  ; false
    // 0x15cd794: ArrayStore: r2[0] = r3  ; List_4
    //     0x15cd794: stur            w3, [x2, #0x17]
    // 0x15cd798: StoreField: r2->field_1b = r3
    //     0x15cd798: stur            w3, [x2, #0x1b]
    // 0x15cd79c: StoreField: r2->field_1f = r3
    //     0x15cd79c: stur            w3, [x2, #0x1f]
    // 0x15cd7a0: StoreField: r2->field_23 = r3
    //     0x15cd7a0: stur            w3, [x2, #0x23]
    // 0x15cd7a4: StoreField: r2->field_27 = r3
    //     0x15cd7a4: stur            w3, [x2, #0x27]
    // 0x15cd7a8: StoreField: r2->field_2b = r3
    //     0x15cd7a8: stur            w3, [x2, #0x2b]
    // 0x15cd7ac: ldur            x4, [fp, #-8]
    // 0x15cd7b0: LoadField: r1 = r4->field_f
    //     0x15cd7b0: ldur            w1, [x4, #0xf]
    // 0x15cd7b4: DecompressPointer r1
    //     0x15cd7b4: add             x1, x1, HEAP, lsl #32
    // 0x15cd7b8: r0 = controller()
    //     0x15cd7b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cd7bc: LoadField: r1 = r0->field_5b
    //     0x15cd7bc: ldur            w1, [x0, #0x5b]
    // 0x15cd7c0: DecompressPointer r1
    //     0x15cd7c0: add             x1, x1, HEAP, lsl #32
    // 0x15cd7c4: r0 = value()
    //     0x15cd7c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cd7c8: LoadField: r1 = r0->field_3f
    //     0x15cd7c8: ldur            w1, [x0, #0x3f]
    // 0x15cd7cc: DecompressPointer r1
    //     0x15cd7cc: add             x1, x1, HEAP, lsl #32
    // 0x15cd7d0: cmp             w1, NULL
    // 0x15cd7d4: b.ne            #0x15cd7e0
    // 0x15cd7d8: r0 = Null
    //     0x15cd7d8: mov             x0, NULL
    // 0x15cd7dc: b               #0x15cd7e8
    // 0x15cd7e0: LoadField: r0 = r1->field_f
    //     0x15cd7e0: ldur            w0, [x1, #0xf]
    // 0x15cd7e4: DecompressPointer r0
    //     0x15cd7e4: add             x0, x0, HEAP, lsl #32
    // 0x15cd7e8: r1 = LoadClassIdInstr(r0)
    //     0x15cd7e8: ldur            x1, [x0, #-1]
    //     0x15cd7ec: ubfx            x1, x1, #0xc, #0x14
    // 0x15cd7f0: r16 = "image_text"
    //     0x15cd7f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cd7f4: ldr             x16, [x16, #0xa88]
    // 0x15cd7f8: stp             x16, x0, [SP]
    // 0x15cd7fc: mov             x0, x1
    // 0x15cd800: mov             lr, x0
    // 0x15cd804: ldr             lr, [x21, lr, lsl #3]
    // 0x15cd808: blr             lr
    // 0x15cd80c: tbnz            w0, #4, #0x15cd818
    // 0x15cd810: r2 = true
    //     0x15cd810: add             x2, NULL, #0x20  ; true
    // 0x15cd814: b               #0x15cd878
    // 0x15cd818: ldur            x0, [fp, #-8]
    // 0x15cd81c: LoadField: r1 = r0->field_f
    //     0x15cd81c: ldur            w1, [x0, #0xf]
    // 0x15cd820: DecompressPointer r1
    //     0x15cd820: add             x1, x1, HEAP, lsl #32
    // 0x15cd824: r0 = controller()
    //     0x15cd824: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cd828: LoadField: r1 = r0->field_5b
    //     0x15cd828: ldur            w1, [x0, #0x5b]
    // 0x15cd82c: DecompressPointer r1
    //     0x15cd82c: add             x1, x1, HEAP, lsl #32
    // 0x15cd830: r0 = value()
    //     0x15cd830: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cd834: LoadField: r1 = r0->field_3f
    //     0x15cd834: ldur            w1, [x0, #0x3f]
    // 0x15cd838: DecompressPointer r1
    //     0x15cd838: add             x1, x1, HEAP, lsl #32
    // 0x15cd83c: cmp             w1, NULL
    // 0x15cd840: b.ne            #0x15cd84c
    // 0x15cd844: r0 = Null
    //     0x15cd844: mov             x0, NULL
    // 0x15cd848: b               #0x15cd854
    // 0x15cd84c: LoadField: r0 = r1->field_f
    //     0x15cd84c: ldur            w0, [x1, #0xf]
    // 0x15cd850: DecompressPointer r0
    //     0x15cd850: add             x0, x0, HEAP, lsl #32
    // 0x15cd854: r1 = LoadClassIdInstr(r0)
    //     0x15cd854: ldur            x1, [x0, #-1]
    //     0x15cd858: ubfx            x1, x1, #0xc, #0x14
    // 0x15cd85c: r16 = "text"
    //     0x15cd85c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15cd860: stp             x16, x0, [SP]
    // 0x15cd864: mov             x0, x1
    // 0x15cd868: mov             lr, x0
    // 0x15cd86c: ldr             lr, [x21, lr, lsl #3]
    // 0x15cd870: blr             lr
    // 0x15cd874: mov             x2, x0
    // 0x15cd878: ldur            x0, [fp, #-8]
    // 0x15cd87c: stur            x2, [fp, #-0x10]
    // 0x15cd880: LoadField: r1 = r0->field_f
    //     0x15cd880: ldur            w1, [x0, #0xf]
    // 0x15cd884: DecompressPointer r1
    //     0x15cd884: add             x1, x1, HEAP, lsl #32
    // 0x15cd888: r0 = controller()
    //     0x15cd888: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cd88c: LoadField: r1 = r0->field_5b
    //     0x15cd88c: ldur            w1, [x0, #0x5b]
    // 0x15cd890: DecompressPointer r1
    //     0x15cd890: add             x1, x1, HEAP, lsl #32
    // 0x15cd894: r0 = value()
    //     0x15cd894: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cd898: LoadField: r1 = r0->field_2b
    //     0x15cd898: ldur            w1, [x0, #0x2b]
    // 0x15cd89c: DecompressPointer r1
    //     0x15cd89c: add             x1, x1, HEAP, lsl #32
    // 0x15cd8a0: cmp             w1, NULL
    // 0x15cd8a4: b.ne            #0x15cd8b0
    // 0x15cd8a8: r4 = ""
    //     0x15cd8a8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cd8ac: b               #0x15cd8b4
    // 0x15cd8b0: mov             x4, x1
    // 0x15cd8b4: ldur            x0, [fp, #-8]
    // 0x15cd8b8: ldur            x3, [fp, #-0x18]
    // 0x15cd8bc: ldur            x2, [fp, #-0x10]
    // 0x15cd8c0: stur            x4, [fp, #-0x20]
    // 0x15cd8c4: LoadField: r1 = r0->field_13
    //     0x15cd8c4: ldur            w1, [x0, #0x13]
    // 0x15cd8c8: DecompressPointer r1
    //     0x15cd8c8: add             x1, x1, HEAP, lsl #32
    // 0x15cd8cc: r0 = of()
    //     0x15cd8cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cd8d0: LoadField: r1 = r0->field_87
    //     0x15cd8d0: ldur            w1, [x0, #0x87]
    // 0x15cd8d4: DecompressPointer r1
    //     0x15cd8d4: add             x1, x1, HEAP, lsl #32
    // 0x15cd8d8: LoadField: r0 = r1->field_7
    //     0x15cd8d8: ldur            w0, [x1, #7]
    // 0x15cd8dc: DecompressPointer r0
    //     0x15cd8dc: add             x0, x0, HEAP, lsl #32
    // 0x15cd8e0: r16 = 14.000000
    //     0x15cd8e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x15cd8e4: ldr             x16, [x16, #0x1d8]
    // 0x15cd8e8: r30 = Instance_Color
    //     0x15cd8e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15cd8ec: stp             lr, x16, [SP]
    // 0x15cd8f0: mov             x1, x0
    // 0x15cd8f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15cd8f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15cd8f8: ldr             x4, [x4, #0xaa0]
    // 0x15cd8fc: r0 = copyWith()
    //     0x15cd8fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cd900: stur            x0, [fp, #-8]
    // 0x15cd904: r0 = Text()
    //     0x15cd904: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cd908: mov             x1, x0
    // 0x15cd90c: ldur            x0, [fp, #-0x20]
    // 0x15cd910: stur            x1, [fp, #-0x28]
    // 0x15cd914: StoreField: r1->field_b = r0
    //     0x15cd914: stur            w0, [x1, #0xb]
    // 0x15cd918: ldur            x0, [fp, #-8]
    // 0x15cd91c: StoreField: r1->field_13 = r0
    //     0x15cd91c: stur            w0, [x1, #0x13]
    // 0x15cd920: r0 = Visibility()
    //     0x15cd920: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cd924: mov             x3, x0
    // 0x15cd928: ldur            x0, [fp, #-0x28]
    // 0x15cd92c: stur            x3, [fp, #-8]
    // 0x15cd930: StoreField: r3->field_b = r0
    //     0x15cd930: stur            w0, [x3, #0xb]
    // 0x15cd934: r0 = Instance_SizedBox
    //     0x15cd934: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cd938: StoreField: r3->field_f = r0
    //     0x15cd938: stur            w0, [x3, #0xf]
    // 0x15cd93c: ldur            x0, [fp, #-0x10]
    // 0x15cd940: StoreField: r3->field_13 = r0
    //     0x15cd940: stur            w0, [x3, #0x13]
    // 0x15cd944: r0 = false
    //     0x15cd944: add             x0, NULL, #0x30  ; false
    // 0x15cd948: ArrayStore: r3[0] = r0  ; List_4
    //     0x15cd948: stur            w0, [x3, #0x17]
    // 0x15cd94c: StoreField: r3->field_1b = r0
    //     0x15cd94c: stur            w0, [x3, #0x1b]
    // 0x15cd950: StoreField: r3->field_1f = r0
    //     0x15cd950: stur            w0, [x3, #0x1f]
    // 0x15cd954: StoreField: r3->field_23 = r0
    //     0x15cd954: stur            w0, [x3, #0x23]
    // 0x15cd958: StoreField: r3->field_27 = r0
    //     0x15cd958: stur            w0, [x3, #0x27]
    // 0x15cd95c: StoreField: r3->field_2b = r0
    //     0x15cd95c: stur            w0, [x3, #0x2b]
    // 0x15cd960: r1 = Null
    //     0x15cd960: mov             x1, NULL
    // 0x15cd964: r2 = 6
    //     0x15cd964: movz            x2, #0x6
    // 0x15cd968: r0 = AllocateArray()
    //     0x15cd968: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15cd96c: mov             x2, x0
    // 0x15cd970: ldur            x0, [fp, #-0x18]
    // 0x15cd974: stur            x2, [fp, #-0x10]
    // 0x15cd978: StoreField: r2->field_f = r0
    //     0x15cd978: stur            w0, [x2, #0xf]
    // 0x15cd97c: r16 = Instance_SizedBox
    //     0x15cd97c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15cd980: ldr             x16, [x16, #0xaa8]
    // 0x15cd984: StoreField: r2->field_13 = r16
    //     0x15cd984: stur            w16, [x2, #0x13]
    // 0x15cd988: ldur            x0, [fp, #-8]
    // 0x15cd98c: ArrayStore: r2[0] = r0  ; List_4
    //     0x15cd98c: stur            w0, [x2, #0x17]
    // 0x15cd990: r1 = <Widget>
    //     0x15cd990: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15cd994: r0 = AllocateGrowableArray()
    //     0x15cd994: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15cd998: mov             x1, x0
    // 0x15cd99c: ldur            x0, [fp, #-0x10]
    // 0x15cd9a0: stur            x1, [fp, #-8]
    // 0x15cd9a4: StoreField: r1->field_f = r0
    //     0x15cd9a4: stur            w0, [x1, #0xf]
    // 0x15cd9a8: r0 = 6
    //     0x15cd9a8: movz            x0, #0x6
    // 0x15cd9ac: StoreField: r1->field_b = r0
    //     0x15cd9ac: stur            w0, [x1, #0xb]
    // 0x15cd9b0: r0 = Row()
    //     0x15cd9b0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15cd9b4: r1 = Instance_Axis
    //     0x15cd9b4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15cd9b8: StoreField: r0->field_f = r1
    //     0x15cd9b8: stur            w1, [x0, #0xf]
    // 0x15cd9bc: r1 = Instance_MainAxisAlignment
    //     0x15cd9bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15cd9c0: ldr             x1, [x1, #0xab0]
    // 0x15cd9c4: StoreField: r0->field_13 = r1
    //     0x15cd9c4: stur            w1, [x0, #0x13]
    // 0x15cd9c8: r1 = Instance_MainAxisSize
    //     0x15cd9c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15cd9cc: ldr             x1, [x1, #0xa10]
    // 0x15cd9d0: ArrayStore: r0[0] = r1  ; List_4
    //     0x15cd9d0: stur            w1, [x0, #0x17]
    // 0x15cd9d4: r1 = Instance_CrossAxisAlignment
    //     0x15cd9d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15cd9d8: ldr             x1, [x1, #0xa18]
    // 0x15cd9dc: StoreField: r0->field_1b = r1
    //     0x15cd9dc: stur            w1, [x0, #0x1b]
    // 0x15cd9e0: r1 = Instance_VerticalDirection
    //     0x15cd9e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15cd9e4: ldr             x1, [x1, #0xa20]
    // 0x15cd9e8: StoreField: r0->field_23 = r1
    //     0x15cd9e8: stur            w1, [x0, #0x23]
    // 0x15cd9ec: r1 = Instance_Clip
    //     0x15cd9ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15cd9f0: ldr             x1, [x1, #0x38]
    // 0x15cd9f4: StoreField: r0->field_2b = r1
    //     0x15cd9f4: stur            w1, [x0, #0x2b]
    // 0x15cd9f8: StoreField: r0->field_2f = rZR
    //     0x15cd9f8: stur            xzr, [x0, #0x2f]
    // 0x15cd9fc: ldur            x1, [fp, #-8]
    // 0x15cda00: StoreField: r0->field_b = r1
    //     0x15cda00: stur            w1, [x0, #0xb]
    // 0x15cda04: LeaveFrame
    //     0x15cda04: mov             SP, fp
    //     0x15cda08: ldp             fp, lr, [SP], #0x10
    // 0x15cda0c: ret
    //     0x15cda0c: ret             
    // 0x15cda10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cda10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cda14: b               #0x15cd620
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e8698, size: 0x24c
    // 0x15e8698: EnterFrame
    //     0x15e8698: stp             fp, lr, [SP, #-0x10]!
    //     0x15e869c: mov             fp, SP
    // 0x15e86a0: AllocStack(0x28)
    //     0x15e86a0: sub             SP, SP, #0x28
    // 0x15e86a4: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e86a4: stur            x1, [fp, #-8]
    //     0x15e86a8: stur            x2, [fp, #-0x10]
    // 0x15e86ac: CheckStackOverflow
    //     0x15e86ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e86b0: cmp             SP, x16
    //     0x15e86b4: b.ls            #0x15e88dc
    // 0x15e86b8: r1 = 2
    //     0x15e86b8: movz            x1, #0x2
    // 0x15e86bc: r0 = AllocateContext()
    //     0x15e86bc: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e86c0: ldur            x1, [fp, #-8]
    // 0x15e86c4: stur            x0, [fp, #-0x18]
    // 0x15e86c8: StoreField: r0->field_f = r1
    //     0x15e86c8: stur            w1, [x0, #0xf]
    // 0x15e86cc: ldur            x2, [fp, #-0x10]
    // 0x15e86d0: StoreField: r0->field_13 = r2
    //     0x15e86d0: stur            w2, [x0, #0x13]
    // 0x15e86d4: r0 = Obx()
    //     0x15e86d4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e86d8: ldur            x2, [fp, #-0x18]
    // 0x15e86dc: r1 = Function '<anonymous closure>':.
    //     0x15e86dc: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8d0] AnonymousClosure: (0x15cd5f8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::appBar (0x15e8698)
    //     0x15e86e0: ldr             x1, [x1, #0x8d0]
    // 0x15e86e4: stur            x0, [fp, #-0x10]
    // 0x15e86e8: r0 = AllocateClosure()
    //     0x15e86e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e86ec: mov             x1, x0
    // 0x15e86f0: ldur            x0, [fp, #-0x10]
    // 0x15e86f4: StoreField: r0->field_b = r1
    //     0x15e86f4: stur            w1, [x0, #0xb]
    // 0x15e86f8: ldur            x1, [fp, #-8]
    // 0x15e86fc: r0 = controller()
    //     0x15e86fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e8700: mov             x1, x0
    // 0x15e8704: r0 = paymentResponse()
    //     0x15e8704: bl              #0x12ddbf0  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::paymentResponse
    // 0x15e8708: tbnz            w0, #4, #0x15e87a0
    // 0x15e870c: ldur            x2, [fp, #-0x18]
    // 0x15e8710: LoadField: r1 = r2->field_13
    //     0x15e8710: ldur            w1, [x2, #0x13]
    // 0x15e8714: DecompressPointer r1
    //     0x15e8714: add             x1, x1, HEAP, lsl #32
    // 0x15e8718: r0 = of()
    //     0x15e8718: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e871c: LoadField: r1 = r0->field_5b
    //     0x15e871c: ldur            w1, [x0, #0x5b]
    // 0x15e8720: DecompressPointer r1
    //     0x15e8720: add             x1, x1, HEAP, lsl #32
    // 0x15e8724: stur            x1, [fp, #-8]
    // 0x15e8728: r0 = ColorFilter()
    //     0x15e8728: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e872c: mov             x1, x0
    // 0x15e8730: ldur            x0, [fp, #-8]
    // 0x15e8734: stur            x1, [fp, #-0x20]
    // 0x15e8738: StoreField: r1->field_7 = r0
    //     0x15e8738: stur            w0, [x1, #7]
    // 0x15e873c: r0 = Instance_BlendMode
    //     0x15e873c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e8740: ldr             x0, [x0, #0xb30]
    // 0x15e8744: StoreField: r1->field_b = r0
    //     0x15e8744: stur            w0, [x1, #0xb]
    // 0x15e8748: r2 = 1
    //     0x15e8748: movz            x2, #0x1
    // 0x15e874c: StoreField: r1->field_13 = r2
    //     0x15e874c: stur            x2, [x1, #0x13]
    // 0x15e8750: r0 = SvgPicture()
    //     0x15e8750: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e8754: stur            x0, [fp, #-8]
    // 0x15e8758: ldur            x16, [fp, #-0x20]
    // 0x15e875c: str             x16, [SP]
    // 0x15e8760: mov             x1, x0
    // 0x15e8764: r2 = "assets/images/search.svg"
    //     0x15e8764: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e8768: ldr             x2, [x2, #0xa30]
    // 0x15e876c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e876c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e8770: ldr             x4, [x4, #0xa38]
    // 0x15e8774: r0 = SvgPicture.asset()
    //     0x15e8774: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e8778: r0 = Align()
    //     0x15e8778: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e877c: r3 = Instance_Alignment
    //     0x15e877c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e8780: ldr             x3, [x3, #0xb10]
    // 0x15e8784: StoreField: r0->field_f = r3
    //     0x15e8784: stur            w3, [x0, #0xf]
    // 0x15e8788: r4 = 1.000000
    //     0x15e8788: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e878c: StoreField: r0->field_13 = r4
    //     0x15e878c: stur            w4, [x0, #0x13]
    // 0x15e8790: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e8790: stur            w4, [x0, #0x17]
    // 0x15e8794: ldur            x1, [fp, #-8]
    // 0x15e8798: StoreField: r0->field_b = r1
    //     0x15e8798: stur            w1, [x0, #0xb]
    // 0x15e879c: b               #0x15e8850
    // 0x15e87a0: ldur            x5, [fp, #-0x18]
    // 0x15e87a4: r4 = 1.000000
    //     0x15e87a4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e87a8: r0 = Instance_BlendMode
    //     0x15e87a8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e87ac: ldr             x0, [x0, #0xb30]
    // 0x15e87b0: r3 = Instance_Alignment
    //     0x15e87b0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e87b4: ldr             x3, [x3, #0xb10]
    // 0x15e87b8: r2 = 1
    //     0x15e87b8: movz            x2, #0x1
    // 0x15e87bc: LoadField: r1 = r5->field_13
    //     0x15e87bc: ldur            w1, [x5, #0x13]
    // 0x15e87c0: DecompressPointer r1
    //     0x15e87c0: add             x1, x1, HEAP, lsl #32
    // 0x15e87c4: r0 = of()
    //     0x15e87c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e87c8: LoadField: r1 = r0->field_5b
    //     0x15e87c8: ldur            w1, [x0, #0x5b]
    // 0x15e87cc: DecompressPointer r1
    //     0x15e87cc: add             x1, x1, HEAP, lsl #32
    // 0x15e87d0: stur            x1, [fp, #-8]
    // 0x15e87d4: r0 = ColorFilter()
    //     0x15e87d4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e87d8: mov             x1, x0
    // 0x15e87dc: ldur            x0, [fp, #-8]
    // 0x15e87e0: stur            x1, [fp, #-0x20]
    // 0x15e87e4: StoreField: r1->field_7 = r0
    //     0x15e87e4: stur            w0, [x1, #7]
    // 0x15e87e8: r0 = Instance_BlendMode
    //     0x15e87e8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e87ec: ldr             x0, [x0, #0xb30]
    // 0x15e87f0: StoreField: r1->field_b = r0
    //     0x15e87f0: stur            w0, [x1, #0xb]
    // 0x15e87f4: r0 = 1
    //     0x15e87f4: movz            x0, #0x1
    // 0x15e87f8: StoreField: r1->field_13 = r0
    //     0x15e87f8: stur            x0, [x1, #0x13]
    // 0x15e87fc: r0 = SvgPicture()
    //     0x15e87fc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e8800: stur            x0, [fp, #-8]
    // 0x15e8804: ldur            x16, [fp, #-0x20]
    // 0x15e8808: str             x16, [SP]
    // 0x15e880c: mov             x1, x0
    // 0x15e8810: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e8810: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e8814: ldr             x2, [x2, #0xa40]
    // 0x15e8818: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e8818: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e881c: ldr             x4, [x4, #0xa38]
    // 0x15e8820: r0 = SvgPicture.asset()
    //     0x15e8820: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e8824: r0 = Align()
    //     0x15e8824: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e8828: mov             x1, x0
    // 0x15e882c: r0 = Instance_Alignment
    //     0x15e882c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e8830: ldr             x0, [x0, #0xb10]
    // 0x15e8834: StoreField: r1->field_f = r0
    //     0x15e8834: stur            w0, [x1, #0xf]
    // 0x15e8838: r0 = 1.000000
    //     0x15e8838: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e883c: StoreField: r1->field_13 = r0
    //     0x15e883c: stur            w0, [x1, #0x13]
    // 0x15e8840: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e8840: stur            w0, [x1, #0x17]
    // 0x15e8844: ldur            x0, [fp, #-8]
    // 0x15e8848: StoreField: r1->field_b = r0
    //     0x15e8848: stur            w0, [x1, #0xb]
    // 0x15e884c: mov             x0, x1
    // 0x15e8850: stur            x0, [fp, #-8]
    // 0x15e8854: r0 = InkWell()
    //     0x15e8854: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e8858: mov             x3, x0
    // 0x15e885c: ldur            x0, [fp, #-8]
    // 0x15e8860: stur            x3, [fp, #-0x20]
    // 0x15e8864: StoreField: r3->field_b = r0
    //     0x15e8864: stur            w0, [x3, #0xb]
    // 0x15e8868: ldur            x2, [fp, #-0x18]
    // 0x15e886c: r1 = Function '<anonymous closure>':.
    //     0x15e886c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8d8] AnonymousClosure: (0x15e88e4), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::appBar (0x15e8698)
    //     0x15e8870: ldr             x1, [x1, #0x8d8]
    // 0x15e8874: r0 = AllocateClosure()
    //     0x15e8874: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e8878: ldur            x2, [fp, #-0x20]
    // 0x15e887c: StoreField: r2->field_f = r0
    //     0x15e887c: stur            w0, [x2, #0xf]
    // 0x15e8880: r0 = true
    //     0x15e8880: add             x0, NULL, #0x20  ; true
    // 0x15e8884: StoreField: r2->field_43 = r0
    //     0x15e8884: stur            w0, [x2, #0x43]
    // 0x15e8888: r1 = Instance_BoxShape
    //     0x15e8888: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e888c: ldr             x1, [x1, #0x80]
    // 0x15e8890: StoreField: r2->field_47 = r1
    //     0x15e8890: stur            w1, [x2, #0x47]
    // 0x15e8894: StoreField: r2->field_6f = r0
    //     0x15e8894: stur            w0, [x2, #0x6f]
    // 0x15e8898: r1 = false
    //     0x15e8898: add             x1, NULL, #0x30  ; false
    // 0x15e889c: StoreField: r2->field_73 = r1
    //     0x15e889c: stur            w1, [x2, #0x73]
    // 0x15e88a0: StoreField: r2->field_83 = r0
    //     0x15e88a0: stur            w0, [x2, #0x83]
    // 0x15e88a4: StoreField: r2->field_7b = r1
    //     0x15e88a4: stur            w1, [x2, #0x7b]
    // 0x15e88a8: r0 = AppBar()
    //     0x15e88a8: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e88ac: stur            x0, [fp, #-8]
    // 0x15e88b0: ldur            x16, [fp, #-0x10]
    // 0x15e88b4: str             x16, [SP]
    // 0x15e88b8: mov             x1, x0
    // 0x15e88bc: ldur            x2, [fp, #-0x20]
    // 0x15e88c0: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e88c0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e88c4: ldr             x4, [x4, #0xf00]
    // 0x15e88c8: r0 = AppBar()
    //     0x15e88c8: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e88cc: ldur            x0, [fp, #-8]
    // 0x15e88d0: LeaveFrame
    //     0x15e88d0: mov             SP, fp
    //     0x15e88d4: ldp             fp, lr, [SP], #0x10
    // 0x15e88d8: ret
    //     0x15e88d8: ret             
    // 0x15e88dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e88dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e88e0: b               #0x15e86b8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e88e4, size: 0xc8
    // 0x15e88e4: EnterFrame
    //     0x15e88e4: stp             fp, lr, [SP, #-0x10]!
    //     0x15e88e8: mov             fp, SP
    // 0x15e88ec: AllocStack(0x18)
    //     0x15e88ec: sub             SP, SP, #0x18
    // 0x15e88f0: SetupParameters()
    //     0x15e88f0: ldr             x0, [fp, #0x10]
    //     0x15e88f4: ldur            w3, [x0, #0x17]
    //     0x15e88f8: add             x3, x3, HEAP, lsl #32
    //     0x15e88fc: stur            x3, [fp, #-8]
    // 0x15e8900: CheckStackOverflow
    //     0x15e8900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e8904: cmp             SP, x16
    //     0x15e8908: b.ls            #0x15e89a4
    // 0x15e890c: LoadField: r1 = r3->field_f
    //     0x15e890c: ldur            w1, [x3, #0xf]
    // 0x15e8910: DecompressPointer r1
    //     0x15e8910: add             x1, x1, HEAP, lsl #32
    // 0x15e8914: r2 = false
    //     0x15e8914: add             x2, NULL, #0x30  ; false
    // 0x15e8918: r0 = showLoading()
    //     0x15e8918: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e891c: ldur            x0, [fp, #-8]
    // 0x15e8920: LoadField: r1 = r0->field_f
    //     0x15e8920: ldur            w1, [x0, #0xf]
    // 0x15e8924: DecompressPointer r1
    //     0x15e8924: add             x1, x1, HEAP, lsl #32
    // 0x15e8928: r0 = controller()
    //     0x15e8928: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e892c: LoadField: r1 = r0->field_a3
    //     0x15e892c: ldur            w1, [x0, #0xa3]
    // 0x15e8930: DecompressPointer r1
    //     0x15e8930: add             x1, x1, HEAP, lsl #32
    // 0x15e8934: r0 = value()
    //     0x15e8934: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e8938: tbnz            w0, #4, #0x15e8970
    // 0x15e893c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e893c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e8940: ldr             x0, [x0, #0x1c80]
    //     0x15e8944: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e8948: cmp             w0, w16
    //     0x15e894c: b.ne            #0x15e8958
    //     0x15e8950: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e8954: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e8958: r16 = "/search"
    //     0x15e8958: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e895c: ldr             x16, [x16, #0x838]
    // 0x15e8960: stp             x16, NULL, [SP]
    // 0x15e8964: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e8964: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e8968: r0 = GetNavigation.toNamed()
    //     0x15e8968: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e896c: b               #0x15e8994
    // 0x15e8970: ldur            x0, [fp, #-8]
    // 0x15e8974: LoadField: r1 = r0->field_f
    //     0x15e8974: ldur            w1, [x0, #0xf]
    // 0x15e8978: DecompressPointer r1
    //     0x15e8978: add             x1, x1, HEAP, lsl #32
    // 0x15e897c: r2 = false
    //     0x15e897c: add             x2, NULL, #0x30  ; false
    // 0x15e8980: r0 = showLoading()
    //     0x15e8980: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e8984: ldur            x0, [fp, #-8]
    // 0x15e8988: LoadField: r1 = r0->field_f
    //     0x15e8988: ldur            w1, [x0, #0xf]
    // 0x15e898c: DecompressPointer r1
    //     0x15e898c: add             x1, x1, HEAP, lsl #32
    // 0x15e8990: r0 = getBack()
    //     0x15e8990: bl              #0x13949e8  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack
    // 0x15e8994: r0 = Null
    //     0x15e8994: mov             x0, NULL
    // 0x15e8998: LeaveFrame
    //     0x15e8998: mov             SP, fp
    //     0x15e899c: ldp             fp, lr, [SP], #0x10
    // 0x15e89a0: ret
    //     0x15e89a0: ret             
    // 0x15e89a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e89a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e89a8: b               #0x15e890c
  }
}
